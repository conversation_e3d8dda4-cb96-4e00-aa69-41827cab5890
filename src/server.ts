import { InversifyExpressServer, TYPE } from "inversify-express-utils"
import { Container } from "inversify"
import * as express from "express"
import { ISEOService } from "./app/seo/ISEOService"
import CUREFIT_API_TYPES from "./config/ioc/types"
import { SEO } from "@curefit/vm-common"
import { <PERSON>ogger, Logger, BASE_TYPES, CLSUtil } from "@curefit/base"
import * as util from "util"
import { IBreadCrumbService } from "./app/breadcrumb/interface"
import kernel from "./config/ioc/ioc"
import { modifiedRoute } from "./app/util/RouteUtil"

export class APIServer extends InversifyExpressServer {
    private seoService: ISEOService
    private breadcrumbService: IBreadCrumbService
    private logger: ILogger
    private clsUtil: CLSUtil

    constructor(protected kernel1: Container) {
        super(kernel1)
        this.logger = kernel1.get<Logger>(BASE_TYPES.ILogger)
        this.seoService = kernel1.get<ISEOService>(CUREFIT_API_TYPES.SEOService)
        this.breadcrumbService = kernel1.get<IBreadCrumbService>(CUREFIT_API_TYPES.BreadCrumbService)
        this.clsUtil = kernel1.get<CLSUtil>(BASE_TYPES.ClsUtil)

        const obj: any = this
        obj.handlerFactory = this.handlerFactory1
    }

    private handlerFactory1(controllerName: any, key: string): express.RequestHandler {
        const that = this
        return function CfApiRequestHandler(req: express.Request, res: express.Response, next: express.NextFunction) {
            let _value: any
            if (res.headersSent) {
                // NOTE: response already sent, need not execute further controllers
                return next()
            }
            Promise.resolve(that.execute(controllerName, key, req, res, next))
                .then((value: any) => {
                    _value = value
                    if (!res.headersSent) {
                        res.send(value)
                    }
                    next()
                }).catch((error: any) => {
                    that.logger.error("Error sending response: " + util.inspect(_value, false, 10, false))
                    next(error)
                })
        }
    }

    protected async execute(controllerName: any, key: string, req: express.Request, res: express.Response, next: express.NextFunction) {
        const named: any = this.kernel1.getNamed(TYPE.Controller, controllerName)
        const { seoPageId, breadcrumb } = req.query,
            fetchSeoData = seoPageId && seoPageId !== "undefined"

        const refererValue = req.header("referrer") || req.header("x-server-referer"),
            fetchBreadCrumbs = breadcrumb && breadcrumb !== "undefined" && refererValue

        const ns = this.clsUtil.getNamespace()
        ns.set(CLSUtil.SERVICE_ROUTE, modifiedRoute(req, res) ?? "anon")

        const [ result, seoData, breadCrumbList ] = await Promise.all([
            named[key](req, res, next),
            fetchSeoData ? this.seoService.getMetaDataById(seoPageId) : new Promise((resolve, reject) => { return resolve(null) }),
            fetchBreadCrumbs ? this.breadcrumbService.getBreadcrumbForRoute(refererValue) : new Promise((resolve, reject) => { return resolve(null) })
        ])


        if (seoData) {
            result["seoData"] = seoData
        }

        if (breadCrumbList) {
            result["breadCrumbs"] = breadCrumbList
        }
        if (res?.locals?.seoSchema) {
            result["seoData"] = {
                ...(result["seoData"] || {}),
                seoSchema: res?.locals?.seoSchema
            }
        }
        if (result?.seoData?.title) {
            if (result?.seoData?.title.includes("{{price}}") && res.locals.productPrice) {
                result.seoData.title = result?.seoData?.title.replace("{{price}}", res.locals.productPrice)
            }
            if (result?.seoData?.title.includes("{{productName}}") && res.locals.productName) {
                result.seoData.title = result?.seoData?.title.replace("{{productName}}", res.locals.productName)
            }
        }
        if (result?.seoData?.meta) {
            if (result?.seoData?.meta.includes("{{price}}") && res.locals.productPrice) {
                result.seoData.meta = result.seoData.meta.replace("{{price}}", res.locals.productPrice)
            }
            if (result.seoData.meta.includes("{{productName}}") && res.locals.productName) {
                result.seoData.meta = result.seoData.meta.replace("{{productName}}", res.locals.productName)
            }
        }
        if (res?.locals?.canonical) {
            result["seoData"] = result["seoData"] || {}
            result.seoData.canonical = res?.locals?.canonical
        }
        return result
    }
}
