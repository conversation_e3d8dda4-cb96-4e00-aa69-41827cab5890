{"appName": "curefit-api", "loggingDir": "/logs/curefit-api", "mongoConf": {"connections": [{"entitySpace": "DEFAULT", "instance": {"replicaSetName": "stage-shard-0", "host": "stage-pl-0.tfwfr.mongodb.net", "children": [{"host": "stage-shard-00-00-tfwfr.mongodb.net"}, {"host": "stage-shard-00-01-tfwfr.mongodb.net"}, {"host": "stage-shard-00-02-tfwfr.mongodb.net"}]}, "credentials": {"dbName": "curefit-stage", "replicaSetName": "stage-shard-0", "username": "service-access", "password": "xkcD32PCnGOfdril", "ssl": true, "authSource": "admin", "connectionOptions": {"socketTimeoutMS": 600000, "minPoolSize": 2, "maxPoolSize": 4}}}, {"entitySpace": "DIGITAL", "instance": {"replicaSetName": "stage-shard-0", "host": "stage-pl-0.tfwfr.mongodb.net", "children": [{"host": "stage-shard-00-00-tfwfr.mongodb.net"}, {"host": "stage-shard-00-01-tfwfr.mongodb.net"}, {"host": "stage-shard-00-02-tfwfr.mongodb.net"}]}, "credentials": {"dbName": "curefit-stage", "replicaSetName": "stage-shard-0", "username": "service-access", "password": "xkcD32PCnGOfdril", "ssl": true, "authSource": "admin", "connectionOptions": {"socketTimeoutMS": 600000, "minPoolSize": 2, "maxPoolSize": 4}}}], "isLeanQueryEnabled": true}, "mysqlConf": {"connections": [{"entitySpace": "DEFAULT", "connection": {"debug": false, "connection": {"host": "cfdb.cw5efoqjpxhj.ap-south-1.rds.amazonaws.com", "user": "curefit", "password": "cure!123", "database": "cfdb", "connectionLimit": 10, "compress": true, "multipleStatements": true}, "pool": {"min": 5, "max": 20, "idleTimeoutMillis": 500000, "acquireTimeoutMillis": 100000}}}, {"entitySpace": "USER_PROFILE", "connection": {"debug": false, "connection": {"host": "curefit.cw5efoqjpxhj.ap-south-1.rds.amazonaws.com", "user": "curefit", "password": "cure!123", "database": "userprofile", "connectionLimit": 10, "compress": true, "multipleStatements": true}, "pool": {"min": 5, "max": 20, "idleTimeoutMillis": 500000, "acquireTimeoutMillis": 100000}}}]}, "apikeys": [{"service": "hercules", "key": "khjk423reterjk543534k"}, {"service": "wallet", "key": "khjk423reterjk543534k", "merchantId": "", "clientId": ""}], "backendServices": {"ptService": {"url": "http://personal-trainer-v2.stage.cure.fit.internal", "apiKey": "", "agentHost": "curefit-api"}, "foodwayService": {"url": "http://foodway.stage.cure.fit.internal", "apiKey": "2b162366-6444-4e42-b87c-131fec7a6dbc"}, "metric": {"url": "http://metrics.stage.cure.fit.internal"}, "rashi": {"url": "http://rashi.stage.cure.fit.internal"}, "ramsay": {"apiKey": "HyPB6PxBI3YZSNzdkg2oW1mquYCx72jF", "url": "http://ramsay.stage.cure.fit.internal"}, "reportIssues": {"url": "http://report-issues.stage.cure.fit.internal/", "apiKey": "app-stage-access"}, "identity": {"url": "http://identity.stage.cure.fit.internal", "apiKey": ""}, "fitClub": {"url": "http://fitclub.stage.curefit.co/", "apiKey": "608824bf-8d3c-4353-a548-abe30a5ceeca", "clientId": "curefit-api"}, "fitcash": {"url": "http://fitcash.stage.cure.fit.internal/wallet/v1", "apiKey": "cfapi", "clientId": "cfapi", "merchantId": "curefit"}, "referral": {"url": "http://referral.stage.cure.fit.internal"}, "enterpriseClient": {"url": "http://enterprise-api.stage.cure.fit.internal"}, "cult": {"url": "http://cultapi.stage.cure.fit.internal/v1", "urlV2": "http://cultapi.stage.cure.fit.internal/v2", "apiKey": "66f7jWoQ3INWMlyye81Cm5q7z6q9LJfvF3mDR6zL"}, "gmf": {"url": "http://gms.stage.curefit.co"}, "segmentationService": {"url": "http://segmentation.stage.cure.fit.internal"}, "eatApi": {"url": "http://eatapi.stage.cure.fit.internal", "apiKey": "608824bf-8d3c-4353-a548-abe30a5ceeca"}, "healthface": {"url": "http://albus.stage.cure.fit.internal"}, "albus": {"url": "http://albus.stage.cure.fit.internal", "ptUrl": "http://personal-trainer.stage.cure.fit.internal", "apiKey": "b67ab8c842c951f275248a0b854edc17189a2ced"}, "alfred": {"url": "http://alfred.stage.cure.fit.internal"}, "indus": {"url": "http://indus.stage.cure.fit.internal"}, "nest": {"url": "http://nest.stage.cure.fit.internal"}, "Constello": {"url": "http://constello.stage.cure.fit.internal"}, "recommendationService": {"url": "http://recommendation-service-api.stage.cure.fit.internal", "apiKey": "J9oMSF42QNRCV0GAP0XiJ6Di8hAI7gfR"}, "cultRecommendationService": {"url": "http://cultapi.stage.internal.cult.fit/v1", "apiKey": "66f7jWoQ3INWMlyye81Cm5q7z6q9LJfvF3mDR6zL"}, "gymfitRecommendationService": {"url": "http://gymfit.stage.cure.fit.internal", "apiKey": "eca60a46-d888-4b93-8415-ff9c6315b9ed"}, "eatRecommendationService": {"url": "http://eatapi.stage.curefit.co", "apiKey": "J9oMSF42QNRCV0GAP0XiJ6Di8hAI7gfR"}, "diyFitnessRecommendationService": {"url": "http://diy-fs.stage.cure.fit.internal", "apiKey": "J9oMSF42QNRCV0GAP0XiJ6Di8hAI7gfR"}, "diyMeditationRecommendationService": {"url": "http://diy-fs.stage.cure.fit.internal", "apiKey": "J9oMSF42QNRCV0GAP0XiJ6Di8hAI7gfR"}, "gymfit": {"url": "http://gymfit.stage.cure.fit.internal", "apiKey": "eca60a46-d888-4b93-8415-ff9c6315b9ed"}, "membershipservice": {"url": "http://membership-service.stage.cure.fit.internal", "agentHost": "curefit-api"}, "user": {"url": "http://user-service.stage.cure.fit.internal/v1", "apiKey": "9dXvsOy3OZTg0uLGNqQWZrKWu4PwiGnNywJI8rgv"}, "wholefitApi": {"url": "http://wholefitapi.stage.cure.fit.internal"}, "iris": {"url": "http://iris.stage.cure.fit.internal"}, "social": {"url": "http://social-service-internal.social-service:8080", "apiKey": "2e96995b-d6b3-418d-aeb1-ed77523f1d3e", "loggingEnabled": true}, "rewardService": {"url": "http://reward-service.stage.cure.fit.internal", "apiKey": "be73f9c930d8cebb5c4238f8522b6386"}, "offerService": {"cache": {"enabled": true, "cacheSize": 1000, "ttlMs": 600000}}, "GYMPASS": {"apiKey": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcGlfa2V5IjoiYjE5MGQ3MjgtN2U5YS00ZTVhLWJkNjMtMzQ4NzA1Yzk0NzYyIiwidGVzdCI6dHJ1ZX0.jVYo79DjrlOdlByXjSfj1jRr1f4CmGccKLcVCM2kTGg", "url": "https://api.wellness.gympass.com/events"}, "mediaGateway": {"url": "http://media-gateway.stage.cure.fit.internal", "apiKey": "a255f9c4-6766-42d7-9101-8e05af160930", "loggingEnabled": true}, "curio": {"url": "http://curio.stage.cure.fit.internal", "apiKey": "curefit-api-stage-access"}, "hamlet": {"apiKey": "d1422beb-74ab-489c-ba83-5424189b6d89"}, "cultbikeService": {"url": "https://stagingcert.tread.fit", "apiKey": "1Acc-b99a-793a0bnQzd58-9a9eLme9"}, "configStore": {"url": "http://config-store.stage.cure.fit.internal", "apiKey": "cf-api-stage-access"}, "oms": {"createFulfillmentApiKey": "CUREFIT_API"}, "cfApiAnalyticsConfig": {"url": "https://dataplatform-webhook.curefit.co/prod/fitness/cf_api_analytics", "apiKey": "343ed474f70fcc1450ea50ebb66eee19", "isEnable": false}, "packManagementService": {"url": "http://pack-management-service.stage.cure.fit.internal", "agentHost": "curefit-api"}, "tesseractService": {"url": "https://tesseract.cultstore.com", "apiKey": "9568394da00672fd2e4a87e9cd6843642b188973cc552dd9c65dcc5ce99"}, "mystiqueService": {"url": "http://mystique.stage.cure.fit.internal"}}, "redisConf": {"connections": [{"entitySpace": "CFAPI-CACHE", "hosts": [{"host": "${INFRA_CONFIG_REDIS_CF_API_CACHE_HOST}", "port": "${INFRA_CONFIG_REDIS_CF_API_CACHE_PORT}"}], "redisOptions": {"username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 1, "max": 2}, "isCluster": true}, {"entitySpace": "RATE-LIMIT", "hosts": [{"host": "${INFRA_CONFIG_REDIS_CF_API_CACHE_HOST}", "port": "${INFRA_CONFIG_REDIS_CF_API_CACHE_PORT}"}], "redisOptions": {"username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 1, "max": 2}, "isCluster": true}, {"entitySpace": "DEFAULT", "hosts": [{"host": "${INFRA_CONFIG_REDIS_CUREFIT_HOST}", "port": "${INFRA_CONFIG_REDIS_CUREFIT_PORT}"}], "redisOptions": {"username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 1, "max": 2}, "isCluster": false}, {"entitySpace": "SESSION-CACHE", "hosts": [{"host": "${INFRA_CONFIG_REDIS_CULT_STAGE_CACHE_HOST}", "port": "${INFRA_CONFIG_REDIS_CULT_STAGE_CACHE_PORT}"}], "redisOptions": {"username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 1, "max": 2}, "isCluster": false}, {"entitySpace": "VM-CACHE", "hosts": [{"host": "${INFRA_CONFIG_REDIS_CUREFIT_HOST}", "port": "${INFRA_CONFIG_REDIS_CUREFIT_PORT}"}], "redisOptions": {"username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 1, "max": 2}, "isCluster": false}, {"entitySpace": "CULT-CACHE", "hosts": [{"host": "${INFRA_CONFIG_REDIS_CF_API_CACHE_HOST}", "port": "${INFRA_CONFIG_REDIS_CF_API_CACHE_PORT}"}], "redisOptions": {"username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 1, "max": 2}, "isCluster": false}, {"entitySpace": "REDIS_CACHE", "hosts": [{"host": "${INFRA_CONFIG_REDIS_CUREFIT_HOST}", "port": "${INFRA_CONFIG_REDIS_CUREFIT_PORT}"}], "redisOptions": {"username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 1, "max": 2}, "isCluster": false}, {"entitySpace": "ON_DEMAND_POOL", "hosts": [{"host": "${INFRA_CONFIG_REDIS_CUREFIT_HOST}", "port": "${INFRA_CONFIG_REDIS_CUREFIT_PORT}"}], "redisOptions": {"username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 1, "max": 2}, "isCluster": false}, {"entitySpace": "RIDDLER_CACHE", "hosts": [{"host": "${INFRA_CONFIG_REDIS_CUREFIT_HOST}", "port": "${INFRA_CONFIG_REDIS_CUREFIT_PORT}"}], "redisOptions": {"username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 1, "max": 2}, "isCluster": false}, {"entitySpace": "CATALOG", "hosts": [{"host": "${INFRA_CONFIG_REDIS_CUREFIT_HOST}", "port": "${INFRA_CONFIG_REDIS_CUREFIT_PORT}"}], "redisOptions": {"username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 1, "max": 2}, "isCluster": false}, {"entitySpace": "SEGMENTATION_REDIS_CACHE", "hosts": [{"host": "${INFRA_CONFIG_REDIS_CUREFIT_STAGE_PLATFORMS_SEGMENTATION_HOST}", "port": "${INFRA_CONFIG_REDIS_CUREFIT_STAGE_PLATFORMS_SEGMENTATION_PORT}"}], "redisOptions": {"username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 1, "max": 2}, "isCluster": true}, {"entitySpace": "EAT_REDIS_CACHE", "hosts": [{"host": "${INFRA_CONFIG_REDIS_CUREFIT_HOST}", "port": "${INFRA_CONFIG_REDIS_CUREFIT_PORT}"}], "redisOptions": {"username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 1, "max": 2}, "isCluster": false}, {"entitySpace": "RASHI_REDIS_CACHE", "hosts": [{"host": "${INFRA_CONFIG_REDIS_CUREFIT_STAGE_PLATFORMS_SEGMENTATION_HOST}", "port": "${INFRA_CONFIG_REDIS_CUREFIT_STAGE_PLATFORMS_SEGMENTATION_PORT}"}], "redisOptions": {"username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 1, "max": 2}, "isCluster": true}]}, "sentryConf": {"enabled": true, "dsn": "https://<EMAIL>/1359021"}, "catalogService": {"v3": {"url": "http://catalog.stage.curefit.co", "size": 10000, "ttlMs": 600000, "bulkGetChunkSize": 20}, "useCatalogServiceV2": true, "size": 2000, "ttlMs": 120000}, "eventsConf": {"arnPrefix": "arn:aws:sns:ap-south-1:035243212545:", "sqsPrefix": "https://sqs.ap-south-1.amazonaws.com/035243212545/"}, "serverSideCachingConfig": {"isEnabled": true, "cacheDestination": "DEFAULT", "enabledMethods": ["CultServiceOld", "OllivanderCityService", "DiagnosticService", "HealthfaceService", "BreadCrumbService", "SitemapController"]}, "cacheService": {"size": 100, "ttlMs": 300000}, "sqsConf": {"connectTimeout": 5000, "maxRetries": 8, "url": "https://ap-south-1.queue.amazonaws.com/035243212545", "enableDebugLogs": true, "retryDelayBaseMs": 25, "timeout": 30000, "isMessageBufferingEnabled": true, "bufferMessagePublishIntervalMillis": 1000}, "snsTopicArns": {"rashiUserEvents": "arn:aws:sns:ap-south-1:035243212545:stage-rashi-user-events"}, "cultstoreShopify": {"googleOAuth2": {"clientId": "710378156821-clapqgna4ps870m5nbt9vcrt8cgsoqf3.apps.googleusercontent.com", "clientSecret": "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"}, "truecaller": {"appKey": "VqmxCc8e71864a1204d36b9e156a7a9d14f6b", "partnerName": "cultsport", "lang": "en", "defaultUiConfig": {"loginPrefix": "placeorder", "loginSuffix": "login", "ctaPrefix": "use", "ctaColor": "%23ff3278", "ctaTextColor": "%23ffffff", "btnShape": "rect", "skipOption": "later"}}}}