{"appName": "curefit-api", "loggingDir": "/logs/curefit-api", "mongoConf": {"connections": [{"entitySpace": "DEFAULT", "instance": {"replicaSetName": "prod-shard-0", "host": "prod-pl-0.vtjqj.mongodb.net", "children": [{"host": "prod-shard-00-00-vtjqj.mongodb.net"}, {"host": "prod-shard-00-01-vtjqj.mongodb.net"}, {"host": "prod-shard-00-02-vtjqj.mongodb.net"}]}, "credentials": {"dbName": "curefit-prod", "replicaSetName": "prod-shard-0", "username": "curefit-api-rw", "password": "jAm6mRvfAD+lXDAT", "ssl": true, "authSource": "admin", "connectionOptions": {"socketTimeoutMS": 60000, "minPoolSize": 0, "maxPoolSize": 2}}}, {"entitySpace": "DIGITAL", "instance": {"host": "prod-digital-pl-0.vtjqj.mongodb.net", "children": [{"host": "prod-digital-shard-00-00.vtjqj.mongodb.net"}, {"host": "prod-digital-shard-00-01.vtjqj.mongodb.net"}, {"host": "prod-digital-shard-00-02.vtjqj.mongodb.net"}]}, "credentials": {"dbName": "curefit-prod", "username": "curefit-api-rw", "password": "jAm6mRvfAD+lXDAT", "ssl": true, "authSource": "admin", "connectionOptions": {"socketTimeoutMS": 600000, "minPoolSize": 0, "maxPoolSize": 4}}}], "isLeanQueryEnabled": true}, "apikeys": [{"service": "hercules", "key": "PjMlHnuk6w6O1WIqvP"}, {"service": "wallet", "key": "khjk423reterjk543534k", "merchantId": "", "clientId": ""}], "backendServices": {"ptService": {"url": "http://personal-trainer-v2.alpha.cure.fit.internal", "apiKey": "", "agentHost": "curefit-api"}, "foodwayService": {"url": "http://foodway.alpha.cure.fit.internal", "apiKey": "43bcc7be-e566-482f-8851-a5c861172c8b"}, "metric": {"url": "http://metrics.production.cure.fit.internal"}, "rashi": {"url": "http://rashi.alpha.cure.fit.internal"}, "ramsay": {"apiKey": "HyPB6PxBI3YZSNzdkg2oW1mquYCx72jF", "url": "http://ramsay.production.cure.fit.internal"}, "reportIssues": {"url": "http://report-issues.alpha.cure.fit.internal", "apiKey": "gauzietheiTua1fu5os0Eehoh0eigheejahcoh1i"}, "fitcash": {"url": "http://fitcash.production.cure.fit.internal/wallet/v1", "apiKey": "f0505710ca1f47da804a77a60b91e640", "clientId": "cfapi", "merchantId": "curefit"}, "fitClub": {"url": "http://fitclub.curefit.co", "apiKey": "c3f05707-8f28-4d27-b980-bffee1e2c058", "clientId": "curefit-api"}, "referral": {"url": "http://referral.production.cure.fit.internal"}, "segmentationService": {"url": "http://segmentation.alpha.cure.fit.internal"}, "enterpriseClient": {"url": "http://enterprise-api.production.cure.fit.internal"}, "cult": {"url": "http://cultapi.alpha.cure.fit.internal/v1", "urlV2": "http://cultapi.alpha.cure.fit.internal/v2", "apiKey": "BxyPmucncAopDeOieJe7j0ELCbX809DL6NuwSCYK"}, "gmf": {"url": "http://gmf-api.curefit.co"}, "eatApi": {"url": "http://eatapi.alpha.cure.fit.internal", "apiKey": "608824bf-8d3c-4353-a548-abe30a5ceeca"}, "healthface": {"url": "http://albus.alpha.cure.fit.internal", "circuitBreaker": {"name": "HealthfaceService", "timeout": 8000, "errorThresholdPercentage": 25, "resetTimeout": 10000}}, "albus": {"url": "http://albus.alpha.cure.fit.internal", "ptUrl": "http://personal-trainer.alpha.cure.fit.internal", "apiKey": "da5ec0c17acdf1e1ecce61f6b5258ba1ad2b94a3"}, "indus": {"url": "http://indus.production.cure.fit.internal"}, "nest": {"url": "http://nest.production.cure.fit.internal"}, "alfred": {"url": "http://alfred.alpha.cure.fit.internal", "circuitBreaker": {"name": "AlfredService", "timeout": 8000, "errorThresholdPercentage": 25, "resetTimeout": 10000}}, "Constello": {"url": "http://constello.production.cure.fit.internal"}, "recommendationService": {"url": "http://reco-api.curefit.co", "apiKey": "J9oMSF42QNRCV0GAP0XiJ6Di8hAI7gfR"}, "cultRecommendationService": {"url": "http://cultapi.alpha.cure.fit.internal/v1", "apiKey": "BxyPmucncAopDeOieJe7j0ELCbX809DL6NuwSCYK"}, "identity": {"url": "http://identity.alpha.cure.fit.internal", "apiKey": ""}, "eatRecommendationService": {"url": "http://eatapi.alpha.cure.fit.internal", "apiKey": "J9oMSF42QNRCV0GAP0XiJ6Di8hAI7gfR"}, "diyFitnessRecommendationService": {"url": "http://diy-fs.alpha.cure.fit.internal", "apiKey": "J9oMSF42QNRCV0GAP0XiJ6Di8hAI7gfR"}, "diyMeditationRecommendationService": {"url": "http://diy-fs.alpha.cure.fit.internal", "apiKey": "J9oMSF42QNRCV0GAP0XiJ6Di8hAI7gfR"}, "user": {"url": "http://user-service.alpha.cure.fit.internal/v1", "apiKey": "YjXOmE7ZiRB5VkaMp6dp9jdE5Bzp5TDI7yJW0mXJ"}, "gymfit": {"url": "http://gymfit.alpha.cure.fit.internal", "apiKey": "eca60a46-d888-4b93-8415-ff9c6315b9ed"}, "membershipservice": {"url": "http://membership-service.alpha.cure.fit.internal", "agentHost": "curefit-api"}, "wholefitApi": {"url": "http://wholefitapi.alpha.cure.fit.internal"}, "iris": {"url": "http://iris.alpha.cure.fit.internal"}, "rewardService": {"url": "http://reward-service.production.cure.fit.internal", "apiKey": "56deebd21678d11a01cb10711e8a23a2"}, "diy": {"url": "http://diy-fs.alpha.cure.fit.internal", "apiKey": "J9oMSF42QNRCV0GAP0XiJ6Di8hAI7gfR"}, "riddler": {"url": "http://riddler.alpha.cure.fit.internal"}, "offerService": {"url": "http://offer-service.alpha.cure.fit.internal", "urlV3": "http://offer-service-v3.alpha.cure.fit.internal", "cache": {"enabled": true, "cacheSize": 1000, "ttlMs": 15000}}, "GYMPASS": {"apiKey": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcGlfa2V5IjoiYjE5MGQ3MjgtN2U5YS00ZTVhLWJkNjMtMzQ4NzA1Yzk0NzYyIiwidGVzdCI6dHJ1ZX0.jVYo79DjrlOdlByXjSfj1jRr1f4CmGccKLcVCM2kTGg", "url": "https://api.wellness.gympass.com/events"}, "mediaGateway": {"url": "http://media-gateway.production.cure.fit.internal", "apiKey": "68490e70-cf98-4841-8685-1324c0dd4ea1", "loggingEnabled": true}, "curio": {"url": "http://curio.alpha.cure.fit.internal", "apiKey": "bb7d8e02-48e0-4056-84d6-6d78862f8d7e"}, "hamlet": {"apiKey": "1704a326-cd8f-4319-8749-81427525bd01"}, "cultbikeService": {"url": "https://api.tread.fit", "apiKey": "2sEv-bFv0-819aPlnQzMa1-9a9eG5Xa"}, "configStore": {"url": "http://config-store.alpha.cure.fit.internal", "apiKey": "5053d4e1-7857-4bd2-a66b-4f8a21c9be6a"}, "oms": {"url": "http://oms.alpha.cure.fit.internal", "apiKey": "gfgdfgdfg543432423", "createFulfillmentApiKey": "a6f01546-96ca-4d0e-86d6-51694fba6c73"}, "cfApiAnalyticsConfig": {"url": "https://dataplatform-webhook.curefit.co/prod/fitness/cf_api_analytics", "apiKey": "343ed474f70fcc1450ea50ebb66eee19", "isEnable": true}, "packManagementService": {"url": "http://pack-management-service.alpha.cure.fit.internal", "agentHost": "curefit-api"}, "tesseractService": {"url": "https://tesseract.cultstore.com", "apiKey": "9568394da00672fd2e4a87e9cd6843642b188973cc552dd9c65dcc5ce99"}, "mystiqueService": {"url": "http://mystique.alpha.cure.fit.internal"}, "hercules": {"url": "http://hercules.alpha.cure.fit.internal", "apiKey": "PjMlHnuk6w6O1WIqvP", "agentHost": "curefit-api"}}, "redisConf": {"connections": [{"entitySpace": "CFAPI-CACHE", "hosts": [{"host": "${INFRA_CONFIG_REDIS_CUREFIT_CF_API_CACHE_HOST}", "port": "${INFRA_CONFIG_REDIS_CUREFIT_CF_API_CACHE_PORT}"}], "redisOptions": {"username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 5, "max": 15}, "isCluster": true}, {"entitySpace": "RATE-LIMIT", "hosts": [{"host": "${INFRA_CONFIG_REDIS_CUREFIT_CF_API_CACHE_HOST}", "port": "${INFRA_CONFIG_REDIS_CUREFIT_CF_API_CACHE_PORT}"}], "redisOptions": {"username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 5, "max": 15}, "isCluster": true}, {"entitySpace": "VM-CACHE", "hosts": [{"host": "${INFRA_CONFIG_REDIS_CUREFIT_CF_API_CACHE_HOST}", "port": "${INFRA_CONFIG_REDIS_CUREFIT_CF_API_CACHE_PORT}"}], "redisOptions": {"username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 5, "max": 15}, "isCluster": true}, {"entitySpace": "CULT-CACHE", "hosts": [{"host": "${INFRA_CONFIG_REDIS_CULT_PROD_CACHE_HOST}", "port": "${INFRA_CONFIG_REDIS_CULT_PROD_CACHE_PORT}"}], "redisOptions": {"enableAutoPipelining": true, "commandTimeout": 2000, "connectionName": "cult-api-cfapi", "username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 1, "max": 2}, "isCluster": true}, {"entitySpace": "DEFAULT", "hosts": [{"host": "${INFRA_CONFIG_REDIS_CUREFIT_DEFAULT_CACHE_HOST}", "port": "${INFRA_CONFIG_REDIS_CUREFIT_DEFAULT_CACHE_PORT}"}], "redisOptions": {"username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 5, "max": 15}, "isCluster": true}, {"entitySpace": "SESSION-CACHE", "hosts": [{"host": "${INFRA_CONFIG_REDIS_PRODUCTION_SESSION_TRANSIENT_CACHE_HOST}", "port": "${INFRA_CONFIG_REDIS_PRODUCTION_SESSION_TRANSIENT_CACHE_PORT}"}], "redisOptions": {"username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 5, "max": 15}, "isCluster": true}, {"entitySpace": "REDIS_CACHE", "hosts": [{"host": "${INFRA_CONFIG_REDIS_PROD_EATFIT_REDIS_HOST}", "port": "${INFRA_CONFIG_REDIS_PROD_EATFIT_REDIS_PORT}"}], "redisOptions": {"username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 5, "max": 15}, "isCluster": false}, {"entitySpace": "RIDDLER_CACHE", "hosts": [{"host": "${INFRA_CONFIG_REDIS_RIDDLER_SERVICE_CACHE_HOST}", "port": "${INFRA_CONFIG_REDIS_RIDDLER_SERVICE_CACHE_PORT}"}], "redisOptions": {"username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 5, "max": 15}, "isCluster": true}, {"entitySpace": "ON_DEMAND_POOL", "hosts": [{"host": "${INFRA_CONFIG_REDIS_PROD_EATFIT_REDIS_HOST}", "port": "${INFRA_CONFIG_REDIS_PROD_EATFIT_REDIS_PORT}"}], "redisOptions": {"username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 5, "max": 15}, "isCluster": false}, {"entitySpace": "CATALOG", "hosts": [{"host": "${INFRA_CONFIG_REDIS_CATALOG_SERVICE_CACHE_HOST}", "port": "${INFRA_CONFIG_REDIS_CATALOG_SERVICE_CACHE_PORT}"}], "redisOptions": {"username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 5, "max": 15}, "isCluster": false}, {"entitySpace": "SEGMENTATION_REDIS_CACHE", "hosts": [{"host": "${INFRA_CONFIG_REDIS_PLATFORMS_SEGMENTATION_CACHE_HOST}", "port": "${INFRA_CONFIG_REDIS_PLATFORMS_SEGMENTATION_CACHE_PORT}"}], "redisOptions": {"username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 5, "max": 15}, "isCluster": true}, {"entitySpace": "EAT_REDIS_CACHE", "hosts": [{"host": "${INFRA_CONFIG_REDIS_PROD_EATFIT_REDIS_HOST}", "port": "${INFRA_CONFIG_REDIS_PROD_EATFIT_REDIS_PORT}"}], "redisOptions": {"username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 5, "max": 15}, "isCluster": false}, {"entitySpace": "RASHI_REDIS_CACHE", "hosts": [{"host": "${INFRA_CONFIG_REDIS_PLATFORMS_SEGMENTATION_CACHE_HOST}", "port": "${INFRA_CONFIG_REDIS_PLATFORMS_SEGMENTATION_CACHE_PORT}"}], "redisOptions": {"username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 5, "max": 15}, "isCluster": true}]}, "mysqlConf": {"connections": [{"entitySpace": "DEFAULT", "connection": {"debug": false, "connection": {"host": "curefitprod-master.production.cure.fit.internal", "user": "cfapi_rw", "password": "eMVLjRXjq4cA60Rc", "database": "cfdb", "connectionLimit": 10, "compress": true, "multipleStatements": true}, "pool": {"min": 1, "max": 5, "idleTimeoutMillis": 300000, "acquireTimeoutMillis": 100000}}}, {"entitySpace": "USER_PROFILE", "connection": {"debug": false, "connection": {"host": "curefitprod-master.production.cure.fit.internal", "user": "cfapi_rw", "password": "eMVLjRXjq4cA60Rc", "database": "userprofile", "connectionLimit": 10, "compress": true, "multipleStatements": true}, "pool": {"min": 1, "max": 4, "idleTimeoutMillis": 500000, "acquireTimeoutMillis": 100000}}}]}, "rollbar": {"enabled": false, "key": "61c4c00a0705498487a52bb42f616d9b"}, "sentryConf": {"enabled": true, "dsn": "https://<EMAIL>/1359021"}, "catalogService": {"v3": {"url": "http://catalog.production.cure.fit.internal", "size": 10000, "ttlMs": 600000, "bulkGetChunkSize": 20}, "useCatalogServiceV2": true, "size": 1000, "ttlMs": 120000}, "eventsConf": {"arnPrefix": "arn:aws:sns:ap-south-1:035243212545:", "sqsPrefix": "https://sqs.ap-south-1.amazonaws.com/035243212545/"}, "eventLoopLagMonitoring": {"enabled": true}, "cacheService": {"size": 10000, "ttlMs": 300000}, "sqsConf": {"connectTimeout": 5000, "maxRetries": 8, "url": "https://ap-south-1.queue.amazonaws.com/035243212545", "retryDelayBaseMs": 25, "timeout": 30000, "isMessageBufferingEnabled": true, "bufferMessagePublishIntervalMillis": 200}, "snsTopicArns": {"rashiUserEvents": "arn:aws:sns:ap-south-1:035243212545:production-rashi-user-events"}, "cultstoreShopify": {"googleOAuth2": {"clientId": "710378156821-clapqgna4ps870m5nbt9vcrt8cgsoqf3.apps.googleusercontent.com", "clientSecret": "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"}, "truecaller": {"appKey": "MBJcl973b46ec89554d4f861412afce972289", "partnerName": "cultsport", "lang": "en", "defaultUiConfig": {"loginPrefix": "placeorder", "loginSuffix": "login", "ctaPrefix": "use", "ctaColor": "%23ff3278", "ctaTextColor": "%23ffffff", "btnShape": "rect", "skipOption": "later"}}}}