{"appName": "curefit-api", "loggingDir": "/logs/curefit-api", "mongoConf": {"connections": [{"entitySpace": "DEFAULT", "instance": {"replicaSetName": "prod-shard-0", "host": "prod-pl-0.vtjqj.mongodb.net", "children": [{"host": "prod-shard-00-00-vtjqj.mongodb.net"}, {"host": "prod-shard-00-01-vtjqj.mongodb.net"}, {"host": "prod-shard-00-02-vtjqj.mongodb.net"}]}, "credentials": {"dbName": "curefit-prod", "replicaSetName": "prod-shard-0", "username": "curefit-api-rw", "password": "jAm6mRvfAD+lXDAT", "ssl": true, "authSource": "admin", "connectionOptions": {"socketTimeoutMS": 60000, "minPoolSize": 1, "maxPoolSize": 3}}}, {"entitySpace": "DIGITAL", "instance": {"host": "prod-digital-pl-0.vtjqj.mongodb.net", "children": [{"host": "prod-digital-shard-00-00.vtjqj.mongodb.net"}, {"host": "prod-digital-shard-00-01.vtjqj.mongodb.net"}, {"host": "prod-digital-shard-00-02.vtjqj.mongodb.net"}]}, "credentials": {"dbName": "curefit-prod", "username": "curefit-api-rw", "password": "jAm6mRvfAD+lXDAT", "ssl": true, "authSource": "admin", "connectionOptions": {"socketTimeoutMS": 600000, "minPoolSize": 1, "maxPoolSize": 5}}}], "isLeanQueryEnabled": true}, "apikeys": [{"service": "hercules", "key": "PjMlHnuk6w6O1WIqvP"}, {"service": "wallet", "key": "khjk423reterjk543534k", "merchantId": "", "clientId": ""}], "backendServices": {"ptService": {"url": "http://personal-trainer-v2.production.cure.fit.internal", "apiKey": "", "agentHost": "curefit-api"}, "foodwayService": {"url": "http://foodway.production.cure.fit.internal", "apiKey": "43bcc7be-e566-482f-8851-a5c861172c8b"}, "metric": {"url": "http://metrics.production.cure.fit.internal"}, "rashi": {"url": "http://rashi.production.cure.fit.internal"}, "ramsay": {"apiKey": "HyPB6PxBI3YZSNzdkg2oW1mquYCx72jF", "url": "http://ramsay.production.cure.fit.internal"}, "reportIssues": {"url": "http://report-issues.production.cure.fit.internal/", "apiKey": "gauzietheiTua1fu5os0Eehoh0eigheejahcoh1i"}, "fitcash": {"url": "http://fitcash.production.cure.fit.internal/wallet/v1", "apiKey": "f0505710ca1f47da804a77a60b91e640", "clientId": "cfapi", "merchantId": "curefit"}, "fitClub": {"url": "http://fitclub.curefit.co", "apiKey": "c3f05707-8f28-4d27-b980-bffee1e2c058", "clientId": "curefit-api"}, "referral": {"url": "http://referral.production.cure.fit.internal"}, "enterpriseClient": {"url": "http://enterprise-api.production.cure.fit.internal"}, "cult": {"url": "http://cultapi.production.cure.fit.internal/v1", "urlV2": "http://cultapi.production.cure.fit.internal/v2", "apiKey": "BxyPmucncAopDeOieJe7j0ELCbX809DL6NuwSCYK"}, "segmentationService": {"url": "http://segmentation.production.cure.fit.internal"}, "gmf": {"url": "http://internal-gmf-api-prod-lb-846164103.ap-south-1.elb.amazonaws.com"}, "eatApi": {"url": "http://eatapi.production.cure.fit.internal", "apiKey": "608824bf-8d3c-4353-a548-abe30a5ceeca"}, "healthface": {"url": "http://albus-service.production.cure.fit.internal"}, "albus": {"url": "http://albus-service.production.cure.fit.internal", "ptUrl": "http://personal-trainer.production.cure.fit.internal", "apiKey": "da5ec0c17acdf1e1ecce61f6b5258ba1ad2b94a3"}, "indus": {"url": "http://indus.production.cure.fit.internal"}, "nest": {"url": "http://nest.production.cure.fit.internal"}, "alfred": {"url": "http://alfred.production.cure.fit.internal"}, "Constello": {"url": "http://constello.production.cure.fit.internal"}, "recommendationService": {"url": "http://recommendation-service-api.production.cure.fit.internal", "apiKey": "J9oMSF42QNRCV0GAP0XiJ6Di8hAI7gfR"}, "cultRecommendationService": {"url": "http://cultapi.production.cure.fit.internal/v1", "apiKey": "BxyPmucncAopDeOieJe7j0ELCbX809DL6NuwSCYK"}, "eatRecommendationService": {"url": "http://eatapi.production.cure.fit.internal", "apiKey": "J9oMSF42QNRCV0GAP0XiJ6Di8hAI7gfR"}, "diyFitnessRecommendationService": {"url": "http://diy-fs.production.cure.fit.internal", "apiKey": "J9oMSF42QNRCV0GAP0XiJ6Di8hAI7gfR"}, "diyMeditationRecommendationService": {"url": "http://diy-fs.production.cure.fit.internal", "apiKey": "J9oMSF42QNRCV0GAP0XiJ6Di8hAI7gfR"}, "gymfitRecommendationService": {"url": "http://gymfit.production.cure.fit.internal", "apiKey": "eca60a46-d888-4b93-8415-ff9c6315b9ed"}, "user": {"url": "http://user-service.production.cure.fit.internal/v1", "apiKey": "YjXOmE7ZiRB5VkaMp6dp9jdE5Bzp5TDI7yJW0mXJ"}, "gymfit": {"url": "http://gymfit.production.cure.fit.internal", "apiKey": "eca60a46-d888-4b93-8415-ff9c6315b9ed"}, "identity": {"url": "http://identity.production.cure.fit.internal", "apiKey": ""}, "membershipservice": {"url": "http://membership-service.production.cure.fit.internal", "agentHost": "curefit-api"}, "wholefitApi": {"url": "http://wholefitapi.production.cure.fit.internal"}, "iris": {"url": "http://iris.production.cure.fit.internal"}, "rewardService": {"url": "http://reward-service.production.cure.fit.internal", "apiKey": "56deebd21678d11a01cb10711e8a23a2"}, "offerService": {"cache": {"enabled": true, "cacheSize": 1000, "ttlMs": 15000}}, "GYMPASS": {"apiKey": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcGlfa2V5IjoiNGY0OWI1YzMtZTI4NS00OTJkLWFiYmMtN2FjMTlkY2EyNGQ2IiwidGVzdCI6ZmFsc2V9.nIVseZxjaHL4djPVqnP1ZN0bEFXYAblbTQBN6bfLSvA", "url": "https://api.wellness.gympass.com/events"}, "mediaGateway": {"url": "http://media-gateway.production.cure.fit.internal", "apiKey": "68490e70-cf98-4841-8685-1324c0dd4ea1", "loggingEnabled": true}, "curio": {"url": "http://curio.production.cure.fit.internal", "apiKey": "bb7d8e02-48e0-4056-84d6-6d78862f8d7e"}, "hamlet": {"apiKey": "1704a326-cd8f-4319-8749-81427525bd01"}, "cultbikeService": {"url": "https://api.tread.fit", "apiKey": "2sEv-bFv0-819aPlnQzMa1-9a9eG5Xa"}, "configStore": {"url": "http://config-store.production.cure.fit.internal", "apiKey": "5053d4e1-7857-4bd2-a66b-4f8a21c9be6a"}, "oms": {"createFulfillmentApiKey": "a6f01546-96ca-4d0e-86d6-51694fba6c73"}, "cfApiAnalyticsConfig": {"url": "https://dataplatform-webhook.curefit.co/prod/fitness/cf_api_analytics", "apiKey": "343ed474f70fcc1450ea50ebb66eee19", "isEnable": true}, "packManagementService": {"url": "http://pack-management-service.production.cure.fit.internal", "agentHost": "curefit-api"}, "tesseractService": {"url": "https://tesseract.cultstore.com", "apiKey": "9568394da00672fd2e4a87e9cd6843642b188973cc552dd9c65dcc5ce99"}, "mystiqueService": {"url": "http://mystique.production.cure.fit.internal"}}, "redisConf": {"connections": [{"entitySpace": "CFAPI-CACHE", "hosts": [{"host": "${INFRA_CONFIG_REDIS_CUREFIT_CF_API_CACHE_HOST}", "port": "${INFRA_CONFIG_REDIS_CUREFIT_CF_API_CACHE_PORT}"}], "redisOptions": {"enableAutoPipelining": true, "commandTimeout": 2500, "connectionName": "cfapi-node-cfapi", "username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 20, "max": 30}, "isCluster": true}, {"entitySpace": "RATE-LIMIT", "hosts": [{"host": "${INFRA_CONFIG_REDIS_CUREFIT_CF_API_CACHE_HOST}", "port": "${INFRA_CONFIG_REDIS_CUREFIT_CF_API_CACHE_PORT}"}], "redisOptions": {"enableAutoPipelining": true, "commandTimeout": 2500, "connectionName": "cfapi-node-ratelimit", "username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 2, "max": 10}, "isCluster": true}, {"entitySpace": "VM-CACHE", "hosts": [{"host": "${INFRA_CONFIG_REDIS_CUREFIT_CF_API_CACHE_HOST}", "port": "${INFRA_CONFIG_REDIS_CUREFIT_CF_API_CACHE_PORT}"}], "redisOptions": {"enableAutoPipelining": true, "commandTimeout": 2500, "connectionName": "cfapi-node-vm", "username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 20, "max": 30}, "isCluster": true}, {"entitySpace": "CULT-CACHE", "hosts": [{"host": "${INFRA_CONFIG_REDIS_CULT_PROD_CACHE_HOST}", "port": "${INFRA_CONFIG_REDIS_CULT_PROD_CACHE_PORT}"}], "redisOptions": {"enableAutoPipelining": true, "commandTimeout": 2000, "connectionName": "cult-api-cfapi", "username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 1, "max": 2}, "isCluster": true}, {"entitySpace": "DEFAULT", "hosts": [{"host": "${INFRA_CONFIG_REDIS_CUREFIT_DEFAULT_CACHE_HOST}", "port": "${INFRA_CONFIG_REDIS_CUREFIT_DEFAULT_CACHE_PORT}"}], "redisOptions": {"enableAutoPipelining": true, "commandTimeout": 1500, "connectionName": "cfapi-node-default", "username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 15, "max": 30}, "isCluster": true}, {"entitySpace": "SESSION-CACHE", "hosts": [{"host": "${INFRA_CONFIG_REDIS_PRODUCTION_SESSION_TRANSIENT_CACHE_HOST}", "port": "${INFRA_CONFIG_REDIS_PRODUCTION_SESSION_TRANSIENT_CACHE_PORT}"}], "redisOptions": {"enableAutoPipelining": true, "commandTimeout": 1500, "connectionName": "cfapi-node-session", "username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 25, "max": 30}, "isCluster": true}, {"entitySpace": "REDIS_CACHE", "hosts": [{"host": "${INFRA_CONFIG_REDIS_PROD_EATFIT_REDIS_HOST}", "port": "${INFRA_CONFIG_REDIS_PROD_EATFIT_REDIS_PORT}"}], "redisOptions": {"enableAutoPipelining": true, "commandTimeout": 1500, "connectionName": "cfapi-node-redis", "username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 25, "max": 30}, "isCluster": false}, {"entitySpace": "RIDDLER_CACHE", "hosts": [{"host": "${INFRA_CONFIG_REDIS_RIDDLER_SERVICE_CACHE_HOST}", "port": "${INFRA_CONFIG_REDIS_RIDDLER_SERVICE_CACHE_PORT}"}], "redisOptions": {"enableAutoPipelining": true, "commandTimeout": 1500, "connectionName": "cfapi-node-riddler", "username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 5, "max": 20}, "isCluster": true}, {"entitySpace": "ON_DEMAND_POOL", "hosts": [{"host": "${INFRA_CONFIG_REDIS_PROD_EATFIT_REDIS_HOST}", "port": "${INFRA_CONFIG_REDIS_PROD_EATFIT_REDIS_PORT}"}], "redisOptions": {"enableAutoPipelining": true, "commandTimeout": 1500, "connectionName": "cfapi-node-on-demand", "username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 15, "max": 20}, "isCluster": false}, {"entitySpace": "CATALOG", "hosts": [{"host": "${INFRA_CONFIG_REDIS_CATALOG_SERVICE_CACHE_HOST}", "port": "${INFRA_CONFIG_REDIS_CATALOG_SERVICE_CACHE_PORT}"}], "redisOptions": {"enableAutoPipelining": true, "commandTimeout": 1500, "connectionName": "cfapi-node-catalog", "username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 5, "max": 20}, "isCluster": false}, {"entitySpace": "SEGMENTATION_REDIS_CACHE", "hosts": [{"host": "${INFRA_CONFIG_REDIS_PLATFORMS_SEGMENTATION_CACHE_HOST}", "port": "${INFRA_CONFIG_REDIS_PLATFORMS_SEGMENTATION_CACHE_PORT}"}], "redisOptions": {"enableAutoPipelining": true, "commandTimeout": 1500, "connectionName": "cfapi-node-segment", "username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 5, "max": 30}, "isCluster": true}, {"entitySpace": "EAT_REDIS_CACHE", "hosts": [{"host": "${INFRA_CONFIG_REDIS_PROD_EATFIT_REDIS_HOST}", "port": "${INFRA_CONFIG_REDIS_PROD_EATFIT_REDIS_PORT}"}], "redisOptions": {"enableAutoPipelining": true, "commandTimeout": 1500, "connectionName": "cfapi-node-eat", "username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 15, "max": 30}, "isCluster": false}, {"entitySpace": "RASHI_REDIS_CACHE", "hosts": [{"host": "${INFRA_CONFIG_REDIS_PLATFORMS_SEGMENTATION_CACHE_HOST}", "port": "${INFRA_CONFIG_REDIS_PLATFORMS_SEGMENTATION_CACHE_PORT}"}], "redisOptions": {"enableAutoPipelining": true, "commandTimeout": 1500, "connectionName": "cfapi-node-rashi", "username": "${INFRA_CONFIG_REDIS_USERNAME}", "password": "${INFRA_CONFIG_REDIS_PASSWORD}"}, "poolOptions": {"min": 5, "max": 30}, "isCluster": true}]}, "mysqlConf": {"connections": [{"entitySpace": "DEFAULT", "connection": {"debug": false, "connection": {"host": "curefitprod-master.production.cure.fit.internal", "user": "cfapi_rw", "password": "eMVLjRXjq4cA60Rc", "database": "cfdb", "connectionLimit": 5, "compress": true, "multipleStatements": true}, "pool": {"min": 1, "max": 3, "idleTimeoutMillis": 300000, "acquireTimeoutMillis": 100000}}}, {"entitySpace": "USER_PROFILE", "connection": {"debug": false, "connection": {"host": "curefitprod-master.production.cure.fit.internal", "user": "cfapi_rw", "password": "eMVLjRXjq4cA60Rc", "database": "userprofile", "connectionLimit": 5, "compress": true, "multipleStatements": true}, "pool": {"min": 1, "max": 3, "idleTimeoutMillis": 300000, "acquireTimeoutMillis": 100000}}}]}, "rollbar": {"enabled": false, "key": "3933203eaabe4377977e95aa5d972127"}, "sentryConf": {"enabled": true, "dsn": "https://<EMAIL>/1359021"}, "catalogService": {"v3": {"url": "http://catalog.production.cure.fit.internal", "size": 10000, "ttlMs": 600000, "bulkGetChunkSize": 20}, "useCatalogServiceV2": true, "size": 10000, "ttlMs": 600000}, "eventsConf": {"arnPrefix": "arn:aws:sns:ap-south-1:035243212545:", "sqsPrefix": "https://sqs.ap-south-1.amazonaws.com/035243212545/"}, "serverSideCachingConfig": {"isEnabled": true, "cacheDestination": "DEFAULT", "enabledMethods": ["CultServiceOld", "OllivanderCityService", "DiagnosticService", "HealthfaceService", "BreadCrumbService", "SitemapController"]}, "cacheService": {"size": 10000, "ttlMs": 300000}, "sqsConf": {"connectTimeout": 5000, "maxRetries": 8, "url": "https://ap-south-1.queue.amazonaws.com/035243212545", "retryDelayBaseMs": 25, "timeout": 30000, "isMessageBufferingEnabled": false, "bufferMessagePublishIntervalMillis": 200}, "snsTopicArns": {"rashiUserEvents": "arn:aws:sns:ap-south-1:035243212545:production-rashi-user-events"}, "cultstoreShopify": {"googleOAuth2": {"clientId": "710378156821-clapqgna4ps870m5nbt9vcrt8cgsoqf3.apps.googleusercontent.com", "clientSecret": "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"}, "truecaller": {"appKey": "QQJON18fd1c2d5e24421b9a008d6d498a39f4", "partnerName": "cultsport", "lang": "en", "defaultUiConfig": {"loginPrefix": "placeorder", "loginSuffix": "login", "ctaPrefix": "use", "ctaColor": "%23ff3278", "ctaTextColor": "%23ffffff", "btnShape": "rect", "skipOption": "later"}}}}