{"appName": "curefit-api", "loggingDir": "logs", "mongoConf": {"connections": [{"entitySpace": "DEFAULT", "instance": {"replicaSetName": "stage-shard-0", "host": "stage-pl-0.tfwfr.mongodb.net", "children": [{"host": "stage-shard-00-00-tfwfr.mongodb.net"}, {"host": "stage-shard-00-01-tfwfr.mongodb.net"}, {"host": "stage-shard-00-02-tfwfr.mongodb.net"}]}, "credentials": {"dbName": "curefit-stage", "replicaSetName": "stage-shard-0", "username": "service-access", "password": "xkcD32PCnGOfdril", "ssl": true, "authSource": "admin", "connectionOptions": {"socketTimeoutMS": 600000, "minPoolSize": 1, "maxPoolSize": 5}}}, {"entitySpace": "DIGITAL", "instance": {"replicaSetName": "stage-shard-0", "host": "stage-pl-0.tfwfr.mongodb.net", "children": [{"host": "stage-shard-00-00-tfwfr.mongodb.net"}, {"host": "stage-shard-00-01-tfwfr.mongodb.net"}, {"host": "stage-shard-00-02-tfwfr.mongodb.net"}]}, "credentials": {"dbName": "curefit-stage", "replicaSetName": "stage-shard-0", "username": "service-access", "password": "xkcD32PCnGOfdril", "ssl": true, "authSource": "admin", "connectionOptions": {"socketTimeoutMS": 600000, "minPoolSize": 1, "maxPoolSize": 5}}}], "isLeanQueryEnabled": true}, "mysqlConf": {"connections": [{"entitySpace": "DEFAULT", "connection": {"debug": false, "connection": {"host": "localhost", "port": 3306, "user": "curefit", "password": "cure!123", "database": "cfdb", "connectionLimit": 10, "compress": true, "multipleStatements": true}, "pool": {"min": 1, "max": 10, "idleTimeoutMillis": 500000, "acquireTimeoutMillis": 100000}}}, {"entitySpace": "USER_PROFILE", "connection": {"debug": false, "connection": {"host": "localhost", "port": 4406, "user": "curefit", "password": "cure!123", "database": "userprofile", "connectionLimit": 10, "compress": true, "multipleStatements": true}, "pool": {"min": 1, "max": 10, "idleTimeoutMillis": 500000, "acquireTimeoutMillis": 100000}}}]}, "apikeys": [{"service": "hercules", "key": "khjk423reterjk543534k"}, {"service": "wallet", "key": "khjk423reterjk543534k", "merchantId": "", "clientId": ""}], "redisConf": {"connections": [{"entitySpace": "CFAPI-CACHE", "hosts": [{"host": "localhost", "port": 6374}], "redisOptions": {}, "poolOptions": {"min": 1, "max": 2}, "isCluster": false}, {"entitySpace": "RATE-LIMIT", "hosts": [{"host": "localhost", "port": 6374}], "redisOptions": {}, "poolOptions": {"min": 1, "max": 2}, "isCluster": false}, {"entitySpace": "DEFAULT", "hosts": [{"host": "localhost", "port": 6379}], "redisOptions": {}, "poolOptions": {"min": 1, "max": 2}, "isCluster": false}, {"entitySpace": "SESSION-CACHE", "hosts": [{"host": "localhost", "port": 6379}], "redisOptions": {}, "poolOptions": {"min": 1, "max": 2}, "isCluster": false}, {"entitySpace": "VM-CACHE", "hosts": [{"host": "localhost", "port": 6379}], "redisOptions": {}, "poolOptions": {"min": 1, "max": 2}, "isCluster": false}, {"entitySpace": "CULT-CACHE", "hosts": [{"host": "curefit-cf-api-cache.stage.cure.fit.internal", "port": 6379}], "redisOptions": {}, "poolOptions": {"min": 1, "max": 2}, "isCluster": false}, {"entitySpace": "REDIS_CACHE", "hosts": [{"host": "localhost", "port": 6379}], "redisOptions": {}, "poolOptions": {"min": 1, "max": 2}, "isCluster": false}, {"entitySpace": "RIDDLER_CACHE", "hosts": [{"host": "localhost", "port": 6379}], "redisOptions": {}, "poolOptions": {"min": 1, "max": 2}, "isCluster": false}, {"entitySpace": "ON_DEMAND_POOL", "hosts": [{"host": "localhost", "port": 6379}], "redisOptions": {}, "poolOptions": {"min": 1, "max": 2}, "isCluster": false}, {"entitySpace": "CATALOG", "hosts": [{"host": "localhost", "port": 6379}], "redisOptions": {}, "poolOptions": {"min": 1, "max": 2}, "isCluster": false}, {"entitySpace": "SEGMENTATION_REDIS_CACHE", "hosts": [{"host": "localhost", "port": 6381}], "redisOptions": {}, "poolOptions": {"min": 1, "max": 2}, "isCluster": false}, {"entitySpace": "EAT_REDIS_CACHE", "hosts": [{"host": "localhost", "port": 6379}], "redisOptions": {}, "poolOptions": {"min": 1, "max": 2}, "isCluster": false}, {"entitySpace": "RASHI_REDIS_CACHE", "hosts": [{"host": "localhost", "port": 6366}], "redisOptions": {}, "poolOptions": {"min": 1, "max": 2}, "isCluster": false}]}, "backendServices": {"ptService": {"url": "http://localhost:8080", "apiKey": "", "agentHost": "curefit-api"}, "foodwayService": {"url": "http://localhost:9000", "apiKey": "2b162366-6444-4e42-b87c-131fec7a6dbc"}, "metric": {"url": "http://localhost:20000"}, "rashi": {"url": "http://localhost:8081"}, "payment": {"url": "http://localhost:4011"}, "ramsay": {"apiKey": "gjhagjfsabghjg6jg5", "url": "http://localhost:3008"}, "reportIssues": {"url": "http://localhost:3016", "apiKey": "app-stage-access"}, "fitcash": {"url": "http://localhost:5115/wallet/v1", "apiKey": "cfapi", "clientId": "cfapi", "merchantId": "curefit"}, "hercules": {"apiKey": "MFM4kAiMG0yfpW9x3q9xo6sPT4zX792W", "url": "http://localhost:9009"}, "fitClub": {"url": "http://fitclub.stage.curefit.co/", "apiKey": "608824bf-8d3c-4353-a548-abe30a5ceeca", "clientId": "curefit-api"}, "referral": {"url": "http://localhost:4554"}, "enterpriseClient": {"url": "http://localhost:3016"}, "cult": {"url": "http://localhost:5005/v1", "urlV2": "http://localhost:5005/v2", "apiKey": "66f7jWoQ3INWMlyye81Cm5q7z6q9LJfvF3mDR6zL"}, "gmf": {"url": "http://localhost:5015"}, "healthface": {"url": "http://localhost:20000"}, "alfred": {"url": "http://localhost:3011"}, "indus": {"url": "http://localhost:12000"}, "nest": {"url": "http://localhost:7654"}, "albus": {"url": "http://localhost:5069", "ptUrl": "http://localhost:5070", "apiKey": "b67ab8c842c951f275248a0b854edc17189a2ced"}, "Constello": {"url": "http://localhost:5225"}, "recommendationService": {"url": "http://localhost:8013", "apiKey": "J9oMSF42QNRCV0GAP0XiJ6Di8hAI7gfR"}, "cultRecommendationService": {"url": "http://localhost:5005/v1", "apiKey": "66f7jWoQ3INWMlyye81Cm5q7z6q9LJfvF3mDR6zL"}, "eatApi": {"url": "http://localhost:6009", "apiKey": "608824bf-8d3c-4353-a548-abe30a5ceeca"}, "eatRecommendationService": {"url": "http://localhost:6009", "apiKey": "J9oMSF42QNRCV0GAP0XiJ6Di8hAI7gfR"}, "segmentationService": {"url": "http://localhost:3456"}, "diyFitnessRecommendationService": {"url": "http://localhost:5010", "apiKey": "J9oMSF42QNRCV0GAP0XiJ6Di8hAI7gfR"}, "diyMeditationRecommendationService": {"url": "http://localhost:5010", "apiKey": "J9oMSF42QNRCV0GAP0XiJ6Di8hAI7gfR"}, "gymfitRecommendationService": {"url": "http://localhost:5020", "apiKey": "eca60a46-d888-4b93-8415-ff9c6315b9ed"}, "user": {"url": "http://localhost:5001/v1", "apiKey": "9dXvsOy3OZTg0uLGNqQWZrKWu4PwiGnNywJI8rgv"}, "gymfit": {"url": "http://localhost:5020", "apiKey": "eca60a46-d888-4b93-8415-ff9c6315b9ed"}, "membershipservice": {"url": "http://localhost:8080", "agentHost": "curefit-api"}, "wholefitApi": {"url": "http://localhost:6010"}, "iris": {"url": "http://localhost:3010"}, "social": {"url": "http://localhost:8089", "apiKey": "2e96995b-d6b3-418d-aeb1-ed77523f1d3e", "loggingEnabled": true}, "rewardService": {"url": "http://localhost:3772", "apiKey": "be73f9c930d8cebb5c4238f8522b6386"}, "offerService": {"cache": {"enabled": true, "cacheSize": 1000, "ttlMs": 60000}}, "GYMPASS": {"apiKey": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcGlfa2V5IjoiYjE5MGQ3MjgtN2U5YS00ZTVhLWJkNjMtMzQ4NzA1Yzk0NzYyIiwidGVzdCI6dHJ1ZX0.jVYo79DjrlOdlByXjSfj1jRr1f4CmGccKLcVCM2kTGg", "url": "https://api.wellness.gympass.com/events"}, "mediaGateway": {"url": "http://localhost:8091", "apiKey": "a255f9c4-6766-42d7-9101-8e05af160930", "loggingEnabled": true}, "curio": {"url": "http://localhost:8200", "apiKey": "curefit-api-stage-access"}, "hamlet": {"apiKey": "d1422beb-74ab-489c-ba83-5424189b6d89"}, "cultbikeService": {"url": "https://stagingcert.tread.fit", "apiKey": "1Acc-b99a-793a0bnQzd58-9a9eLme9"}, "configStore": {"url": "http://config-store.stage.curefit.co", "apiKey": "cf-api-stage-access"}, "oms": {"createFulfillmentApiKey": "CUREFIT_API"}, "cfApiAnalyticsConfig": {"url": "https://dataplatform-webhook.curefit.co/prod/fitness/cf_api_analytics", "apiKey": "343ed474f70fcc1450ea50ebb66eee19", "isEnable": false}, "packManagementService": {"url": "http://localhost:5050", "agentHost": "curefit-api"}, "tesseractService": {"url": "https://tesseract.cultstore.com", "apiKey": "9568394da00672fd2e4a87e9cd6843642b188973cc552dd9c65dcc5ce99"}, "mystiqueService": {"url": "http://mystique.stage.cure.fit.internal"}}, "sentryConf": {"enabled": false, "dsn": "https://<EMAIL>/1359021"}, "catalogService": {"v3": {"url": "http://catalog.stage.curefit.co", "size": 10000, "ttlMs": 600000, "bulkGetChunkSize": 20}, "useCatalogServiceV2": true, "size": 1000, "ttlMs": 60000}, "eventsConf": {"arnPrefix": "arn:aws:sns:ap-south-1:035243212545:", "sqsPrefix": "https://sqs.ap-south-1.amazonaws.com/035243212545/"}, "menuServiceConf": {"areasToPopulateCache": ["9", "36", "64", "72", "56", "87", "1", "38", "1", "11", "89", "10"]}, "serverSideCachingConfig": {"isEnabled": true, "cacheDestination": "DEFAULT", "enabledMethods": ["CultServiceOld", "OllivanderCityService", "DiagnosticService", "HealthfaceService", "BreadCrumbService", "SitemapController"]}, "cacheService": {"size": 10000, "ttlMs": 300000}, "sqsConf": {"connectTimeout": 5000, "maxRetries": 8, "url": "https://ap-south-1.queue.amazonaws.com/035243212545", "enableDebugLogs": false, "retryDelayBaseMs": 25}, "snsTopicArns": {"rashiUserEvents": "arn:aws:sns:ap-south-1:035243212545:stage-rashi-user-events"}, "cultstoreShopify": {"googleOAuth2": {"clientId": "710378156821-clapqgna4ps870m5nbt9vcrt8cgsoqf3.apps.googleusercontent.com", "clientSecret": "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"}, "truecaller": {"appKey": "VqmxCc8e71864a1204d36b9e156a7a9d14f6b", "partnerName": "cultsport", "lang": "en", "defaultUiConfig": {"loginPrefix": "placeorder", "loginSuffix": "login", "ctaPrefix": "use", "ctaColor": "%23ff3278", "ctaTextColor": "%23ffffff", "btnShape": "rect", "skipOption": "later"}}}}