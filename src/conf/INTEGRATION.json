{"appName": "curefit-api", "loggingDir": "/logs/curefit-api", "mongoConf": {"connections": [{"entitySpace": "DEFAULT", "instance": {"replicaSetName": "stage-shard-0", "host": "stage-pl-0.tfwfr.mongodb.net", "children": [{"host": "stage-shard-00-00-tfwfr.mongodb.net"}, {"host": "stage-shard-00-01-tfwfr.mongodb.net"}, {"host": "stage-shard-00-02-tfwfr.mongodb.net"}]}, "credentials": {"dbName": "curefit-stage", "replicaSetName": "stage-shard-0", "username": "service-access", "password": "xkcD32PCnGOfdril", "ssl": true, "authSource": "admin", "connectionOptions": {"socketTimeoutMS": 600000, "minPoolSize": 2, "maxPoolSize": 4}}}, {"entitySpace": "DIGITAL", "instance": {"replicaSetName": "stage-shard-0", "host": "stage-pl-0.tfwfr.mongodb.net", "children": [{"host": "stage-shard-00-00-tfwfr.mongodb.net"}, {"host": "stage-shard-00-01-tfwfr.mongodb.net"}, {"host": "stage-shard-00-02-tfwfr.mongodb.net"}]}, "credentials": {"dbName": "curefit-stage", "replicaSetName": "stage-shard-0", "username": "service-access", "password": "xkcD32PCnGOfdril", "ssl": true, "authSource": "admin", "connectionOptions": {"socketTimeoutMS": 600000, "minPoolSize": 2, "maxPoolSize": 4}}}], "isLeanQueryEnabled": true}, "mysqlConf": {"connections": [{"entitySpace": "DEFAULT", "connection": {"debug": false, "connection": {"host": "cfdb.cw5efoqjpxhj.ap-south-1.rds.amazonaws.com", "user": "curefit", "password": "cure!123", "database": "cfdb", "connectionLimit": 10, "compress": true, "multipleStatements": true}, "pool": {"min": 2, "max": 10, "idleTimeoutMillis": 500000, "acquireTimeoutMillis": 100000}}}, {"entitySpace": "USER_PROFILE", "connection": {"debug": false, "connection": {"host": "cfdb.cw5efoqjpxhj.ap-south-1.rds.amazonaws.com", "user": "curefit", "password": "cure!123", "database": "userprofile", "connectionLimit": 10, "compress": true, "multipleStatements": true}, "pool": {"min": 2, "max": 10, "idleTimeoutMillis": 500000, "acquireTimeoutMillis": 100000}}}]}, "apikeys": [{"service": "hercules", "key": "khjk423reterjk543534k"}, {"service": "wallet", "key": "khjk423reterjk543534k", "merchantId": "", "clientId": ""}], "backendServices": {"ptService": {"url": "http://personal-trainer-v2.stage.cure.fit.internal", "apiKey": "", "agentHost": "curefit-api"}, "metric": {"url": "http://metrics.stage.cure.fit.internal"}, "rashi": {"url": "https://rashi.stage.curefit.co"}, "ramsay": {"apiKey": "HyPB6PxBI3YZSNzdkg2oW1mquYCx72jF", "url": "http://ramsay.stage.cure.fit.internal"}, "reportIssues": {"url": "http://report-issues.stage.curefit.co/", "apiKey": "app-stage-access"}, "fitClub": {"url": "http://fitclub.stage.curefit.co/", "apiKey": "608824bf-8d3c-4353-a548-abe30a5ceeca", "clientId": "curefit-api"}, "fitcash": {"url": "http://fitcash.stage.cure.fit.internal/wallet/v1", "apiKey": "cfapi", "clientId": "cfapi", "merchantId": "curefit"}, "referral": {"url": "http://referral.stage.cure.fit.internal"}, "segmentationService": {"url": "http://segmentation.stage.cure.fit.internal"}, "cult": {"url": "http://cultapi.stage.internal.cult.fit/v1", "urlV2": "http://cultapi.stage.internal.cult.fit/v2", "apiKey": "66f7jWoQ3INWMlyye81Cm5q7z6q9LJfvF3mDR6zL"}, "gmf": {"url": "http://gms.stage.curefit.co"}, "eatApi": {"url": "http://eatapi.stage.cure.fit.internal", "apiKey": "608824bf-8d3c-4353-a548-abe30a5ceeca"}, "identity": {"url": "http://identity.stage.cure.fit.internal", "apiKey": ""}, "healthface": {"url": "http://albus.stage.cure.fit.internal", "circuitBreaker": {"name": "HealthfaceService", "timeout": 5000, "errorThresholdPercentage": 50, "resetTimeout": 10000}}, "albus": {"url": "http://albus.stage.curefit.co", "ptUrl": "http://personal-trainer.stage.cure.fit.internal", "apiKey": "b67ab8c842c951f275248a0b854edc17189a2ced"}, "indus": {"url": "http://indus.stage.curefit.co"}, "alfred": {"url": "http://alfred.stage.cure.fit.internal", "circuitBreaker": {"name": "AlfredService", "timeout": 5000, "errorThresholdPercentage": 50, "resetTimeout": 10000}}, "Constello": {"url": "http://constello.stage.cure.fit.internal"}, "recommendationService": {"url": "http://reco-api.stage.curefit.co", "apiKey": "J9oMSF42QNRCV0GAP0XiJ6Di8hAI7gfR"}, "cultRecommendationService": {"url": "http://cultapi.stage.internal.cult.fit/v1", "apiKey": "66f7jWoQ3INWMlyye81Cm5q7z6q9LJfvF3mDR6zL"}, "eatRecommendationService": {"url": "http://eatapi.stage.curefit.co", "apiKey": "J9oMSF42QNRCV0GAP0XiJ6Di8hAI7gfR"}, "diyFitnessRecommendationService": {"url": "http://diy-fs.stage.cure.fit.internal", "apiKey": "J9oMSF42QNRCV0GAP0XiJ6Di8hAI7gfR"}, "diyMeditationRecommendationService": {"url": "http://diy-fs.stage.cure.fit.internal", "apiKey": "J9oMSF42QNRCV0GAP0XiJ6Di8hAI7gfR"}, "user": {"url": "http://user-service.stage.curefit.co/v1", "apiKey": "9dXvsOy3OZTg0uLGNqQWZrKWu4PwiGnNywJI8rgv"}, "iris": {"url": "http://iris.stage.cure.fit.internal"}, "GYMPASS": {"apiKey": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcGlfa2V5IjoiYjE5MGQ3MjgtN2U5YS00ZTVhLWJkNjMtMzQ4NzA1Yzk0NzYyIiwidGVzdCI6dHJ1ZX0.jVYo79DjrlOdlByXjSfj1jRr1f4CmGccKLcVCM2kTGg", "url": "https://api.wellness.gympass.com/events"}, "mediaGateway": {"url": "http://media-gateway.stage.cure.fit.internal", "apiKey": "a255f9c4-6766-42d7-9101-8e05af160930", "loggingEnabled": true}, "hamlet": {"apiKey": "d1422beb-74ab-489c-ba83-5424189b6d89"}, "cultbikeService": {"url": "https://stagingcert.tread.fit", "apiKey": "1Acc-b99a-793a0bnQzd58-9a9eLme9"}, "oms": {"createFulfillmentApiKey": "CUREFIT_API"}, "cfApiAnalyticsConfig": {"url": "https://dataplatform-webhook.curefit.co/prod/fitness/cf_api_analytics", "apiKey": "343ed474f70fcc1450ea50ebb66eee19", "isEnable": false}, "packManagementService": {"url": "http://pack-management-service.stage.cure.fit.internal", "agentHost": "curefit-api"}, "tesseractService": {"url": "https://tesseract.cultstore.com", "apiKey": "9568394da00672fd2e4a87e9cd6843642b188973cc552dd9c65dcc5ce99"}, "mystiqueService": {"url": "http://mystique.stage.cure.fit.internal"}}, "redisConf": {"connections": [{"entitySpace": "DEFAULT", "hosts": [{"host": "curefit.y66lea.0001.aps1.cache.amazonaws.com", "port": 6379}], "redisOptions": {}, "isCluster": false}, {"entitySpace": "SESSION-CACHE", "hosts": [{"host": "cult-stage-cache-primary.stage.cure.fit.internal", "port": 6379}], "redisOptions": {}, "isCluster": false}, {"entitySpace": "VM-CACHE", "hosts": [{"host": "curefit.y66lea.0001.aps1.cache.amazonaws.com", "port": 6379}], "redisOptions": {}, "isCluster": false}, {"entitySpace": "CULT-CACHE", "hosts": [{"host": "curefit-cf-api-cache.stage.cure.fit.internal", "port": 6379}], "redisOptions": {"enableAutoPipelining": true, "commandTimeout": 2000, "connectionName": "cult-api-cfapi"}, "poolOptions": {"min": 1, "max": 2}, "isCluster": true}, {"entitySpace": "REDIS_CACHE", "hosts": [{"host": "curefit.y66lea.0001.aps1.cache.amazonaws.com", "port": 6379}], "redisOptions": {}, "isCluster": false}, {"entitySpace": "ON_DEMAND_POOL", "hosts": [{"host": "curefit.y66lea.0001.aps1.cache.amazonaws.com", "port": 6379}], "redisOptions": {}, "isCluster": false}, {"entitySpace": "CATALOG", "hosts": [{"host": "curefit.y66lea.0001.aps1.cache.amazonaws.com", "port": 6379}], "redisOptions": {}, "isCluster": false}, {"entitySpace": "SEGMENTATION_REDIS_CACHE", "hosts": [{"host": "platforms-segmentation-cache.stage.cure.fit.internal", "port": 6379}], "redisOptions": {}, "isCluster": true}, {"entitySpace": "EAT_REDIS_CACHE", "hosts": [{"host": "curefit.y66lea.0001.aps1.cache.amazonaws.com", "port": 6379}], "redisOptions": {}, "isCluster": false}]}, "sentryConf": {"enabled": true, "dsn": "https://<EMAIL>/1359021"}, "catalogService": {"useCatalogServiceV2": true, "size": 2000, "ttlMs": 120000}, "eventsConf": {"arnPrefix": "arn:aws:sns:ap-south-1:035243212545:", "sqsPrefix": "https://sqs.ap-south-1.amazonaws.com/035243212545/"}, "cacheService": {"size": 100, "ttlMs": 300000}, "sqsConf": {"connectTimeout": 5000, "maxRetries": 8, "url": "https://ap-south-1.queue.amazonaws.com/035243212545", "enableDebugLogs": true, "retryDelayBaseMs": 25, "timeout": 30000}, "snsTopicArns": {"rashiUserEvents": "arn:aws:sns:ap-south-1:035243212545:stage-rashi-user-events"}, "cultstoreShopify": {"googleOAuth2": {"clientId": "710378156821-clapqgna4ps870m5nbt9vcrt8cgsoqf3.apps.googleusercontent.com", "clientSecret": "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"}, "truecaller": {"appKey": "VqmxCc8e71864a1204d36b9e156a7a9d14f6b", "partnerName": "cultsport", "lang": "en", "defaultUiConfig": {"loginPrefix": "placeorder", "loginSuffix": "login", "ctaPrefix": "use", "ctaColor": "%23ff3278", "ctaTextColor": "%23ffffff", "btnShape": "rect", "skipOption": "later"}}}}