// datadog
process.env.DD_TRACE_DEBUG = "false"
process.env.DD_TRACE_ENABLED = "true"
process.env.DD_TRACE_STARTUP_LOGS = "true"

require("./tracer")

// @ts-ignore
global.fetch = require("node-fetch")

import kernel from "./config/ioc/ioc"
import { Logger, BASE_TYPES } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

require("./worker/master")

const logger: Logger = kernel.get<Logger>(BASE_TYPES.ILogger)
const rollbarService: RollbarService = kernel.get<RollbarService>(ERROR_COMMON_TYPES.RollbarService)

process.on("unhandledRejection", (reason: any, promise: Promise<any>) => {
    if (reason.statusCode && reason.statusCode >= 500) {
        rollbarService.sendError(reason)
    }
    logger.error("Unhandled rejection", {
        reason,
    })
})

process.on("uncaughtException", (error: any) => {
    if (error.statusCode && error.statusCode >= 500) {
        rollbarService.sendError(error)
    }
    logger.error("uncaughtException", { error })
})
