import tracer from "dd-trace"
import * as express from "express"
import * as opentracing from "opentracing"
import { ClientRequest, IncomingMessage, ServerResponse } from "http"

tracer.init({
    runtimeMetrics: process.env.APP_ENV !== "PRODUCTION",
    sampleRate: process.env.APP_ENV !== "PRODUCTION" ? 0.05 : 0.001,
    flushInterval: 10_000,
    startupLogs: true,
    profiling: false
}) // initialized in a different file to avoid hoisting.

tracer.use("express", {
  hooks: {
    request: (span: opentracing.Span, req: express.Request, res: ServerResponse) => {
            // the request hook is invoked for both .on 'data' and .on 'end' events
            // the response and it's metadata will exist for 'data' events but not '.end' events
            // configure your hook accordingly, this is an example
            if (req.path.startsWith("/page/")) {
                span.setTag("http.route", req.path)
            }
            if (res && res.statusCode >= 400) {
                span.setTag("error.msg", res.statusMessage)
            }
    }
  }
})

const StatsD = require("node-dogstatsd").StatsD
const dogstatsd = new StatsD()
export default tracer
