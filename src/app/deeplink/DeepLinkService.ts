import { I<PERSON>rud<PERSON>eyValue, IMultiCrudKeyValue, REDIS_TYPES } from "@curefit/redis-utils"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { inject, injectable } from "inversify"
import { IDeeplinkService, IRIS_CLIENT_TYPES } from "@curefit/iris-client"
import { DeeplinkServices, LinkPayload } from "@curefit/iris-common"
import { ActionUtil, ActionUtilV1, SeoUrlParams } from "@curefit/base-utils"
import { DIYRecipeProduct, OnDemandVideoCategory } from "@curefit/diy-common"
import { UrlPathBuilder } from "@curefit/product-common"
import { Tenant } from "@curefit/user-common"
import { PageTypes } from "@curefit/apps-common"
import { UserAgent } from "@curefit/base-common"


export interface IDeepLinkService {
    getRecipeDeeplink(tenant: Tenant, recipe: DIYRecipeProduct): Promise<string>
    getOnDemandCollectionShareLink(tenant: Tenant, onDemandVideoCategory: OnDemandVideoCategory, onDemandVideoCollectionId: string, title: string, description: string, image: string, videoId: string): Promise<string>
    getOnDemandPremiereShareLink(tenant: Tenant, onDemandVideoCategory: OnDemandVideoCategory, title: string, description: string, image: string, videoId: string): Promise<string>
}

@injectable()
export class DeepLinkService implements IDeepLinkService {

    private crudDao: ICrudKeyValue
    constructor(
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(REDIS_TYPES.MultiCrudKeyValueDao) private multiCrudKeyValueDao: IMultiCrudKeyValue,
        @inject(IRIS_CLIENT_TYPES.DeepLinkService) private deeplinkService: IDeeplinkService
    ) {
        this.crudDao = multiCrudKeyValueDao.getICrudKeyValue("DEFAULT")
    }

    async getRecipeDeeplink(tenant: Tenant, recipe: DIYRecipeProduct): Promise<string> {
        const preferredService = tenant === Tenant.LIVEFIT_APP ? DeeplinkServices.BRANCH : undefined
        const key = DeeplinkUtil.getRecipeKey(tenant, recipe.productId)
        const deeplinkFromCache = await this.crudDao.read(key)

        if (deeplinkFromCache) {
            this.logger.info(`Fetched deeplink for ${key}`)
            return deeplinkFromCache
        }
        const seoParams: SeoUrlParams = {
            productName: recipe.title
        }

        const linkPayload: LinkPayload = {
            applink: `${ActionUtil.recipeHomePage(recipe.productId, null, null)}&pageFrom=deeplink`,
            weblink: `https://cure.fit${ActionUtilV1.recipeHomePage(seoParams, recipe.productId)}?pageFrom=deeplink`,
            socialMetaTagInfo: {
                //  need  to update this url to the ones with < 300 kb
                socialImageLink: "https://cdn-images.cure.fit/www-curefit-com/image/upload/w_300,ar_150:200,q_auto:eco,c_fit,f_auto" + UrlPathBuilder.prefixSlash(recipe.imageDetails.thumbnailImage),
                socialTitle: recipe.title,
                socialDescription: recipe.subTitle
            }
        }
        const response = await this.deeplinkService.createUniversalDeepLinkUrl(tenant, linkPayload, undefined, undefined, preferredService)
        const insertInRedis = await this.crudDao.upsertWithExpiry(key, response.url, DeeplinkUtil.expireTtl)
        if (insertInRedis) {
            this.logger.info(`Inserted deeplink for ${key}`)
        }
        return response.url
    }

    async getOnDemandCollectionShareLink(tenant: Tenant, onDemandVideoCategory: OnDemandVideoCategory, onDemandVideoCollectionId: string, title: string, videoTitle: string, image: string, videoId: string): Promise<string> {
        const preferredService = tenant === Tenant.LIVEFIT_APP ? DeeplinkServices.BRANCH : undefined
        const key = DeeplinkUtil.getKeyForOnDemandCollection(tenant, onDemandVideoCategory, onDemandVideoCollectionId, videoId)
        const deeplinkFromCache = await this.crudDao.read(key)

        if (deeplinkFromCache) {
            this.logger.info(`Fetched deeplink for ${key}`)
            return deeplinkFromCache
        }

        const appLink = `curefit://collectionslistpage?collectionCategory=${onDemandVideoCategory}&collectionId=${onDemandVideoCollectionId}&pageFrom=deeplink`
        const linkTitle = onDemandVideoCategory === "EAT" ? `eat.live: ${title}` : `pop.live: ${title}`
        // todo: Nisheet make changes for deeplink redirect same as the recipe deeplink once collections is live on web/mweb
        const linkPayload: LinkPayload = {
            applink: appLink,
            socialMetaTagInfo: {
                //  need  to update this url to the ones with < 300 kb
                socialImageLink: "https://cdn-images.cure.fit/www-curefit-com/image/upload/w_300,ar_150:200,q_auto:eco,c_fit,f_auto" + UrlPathBuilder.prefixSlash(image),
                socialTitle: linkTitle,
                socialDescription: videoTitle
            }
        }
        const response = await this.deeplinkService.createUniversalDeepLinkUrl(tenant, linkPayload, undefined, undefined, preferredService)
        const insertInRedis = await this.crudDao.upsertWithExpiry(key, response.url, DeeplinkUtil.expireTtl)
        if (insertInRedis) {
            this.logger.info(`Inserted deeplink for ${key}`)
        }
        return response.url
    }

    async getOnDemandPremiereShareLink(tenant: Tenant, onDemandVideoCategory: OnDemandVideoCategory, title: string, description: string, image: string, videoId: string): Promise<string> {
        const preferredService = tenant === Tenant.LIVEFIT_APP ? DeeplinkServices.BRANCH : undefined
        const key = DeeplinkUtil.getKeyForOnDemandPremiereCollection(tenant, onDemandVideoCategory, videoId)
        const deeplinkFromCache = await this.crudDao.read(key)

        if (deeplinkFromCache) {
            this.logger.info(`Fetched deeplink for ${key}`)
            return deeplinkFromCache
        }

        const appLink = `curefit://${PageTypes.LivePremiereDetailsPage}?id=${videoId}&category=${onDemandVideoCategory}&pageFrom=deeplink`
        const linkTitle = onDemandVideoCategory === "EAT" ? `eat.live: ${title}` : `pop.live: ${title}`
        // todo: Nisheet make changes for deeplink redirect same as the recipe deeplink once collections is live on web/mweb
        const linkPayload: LinkPayload = {
            applink: appLink,
            socialMetaTagInfo: {
                //  need  to update this url to the ones with < 300 kb
                socialImageLink: "https://cdn-images.cure.fit/www-curefit-com/image/upload/w_300,ar_150:200,q_auto:eco,c_fit,f_auto" + UrlPathBuilder.prefixSlash(image),
                socialTitle: linkTitle,
                socialDescription: description
            }
        }
        const response = await this.deeplinkService.createUniversalDeepLinkUrl(tenant, linkPayload, undefined, undefined, preferredService)
        const insertInRedis = await this.crudDao.upsertWithExpiry(key, response.url, DeeplinkUtil.expireTtl)
        if (insertInRedis) {
            this.logger.info(`Inserted deeplink for ${key}`)
        }
        return response.url
    }
}

export class DeeplinkUtil {
    public static expireTtl = 2592000
    private static separator = ":"

    public static getRecipeDeeplinkKeyHash(tenant: Tenant, category: Keys): string {
        return "CFAPP" + DeeplinkUtil.separator + tenant + DeeplinkUtil.separator + category + DeeplinkUtil.separator
    }

    public static getRecipeKey(tenant: Tenant, recipeId: string): string {
        return DeeplinkUtil.getRecipeDeeplinkKeyHash(tenant, Keys.RECIPE_DEEPLINK) + recipeId
    }

    public static getKeyForOnDemandCollection(tenant: Tenant, collectionCategory: OnDemandVideoCategory, collectionId: string, videoId: string): string {
        return DeeplinkUtil.getRecipeDeeplinkKeyHash(tenant, Keys.ONDEMAND_VIDEO_DEEPLINK) + collectionCategory + DeeplinkUtil.separator + collectionId + DeeplinkUtil.separator + videoId
    }

    public static getKeyForOnDemandPremiereCollection(tenant: Tenant, collectionCategory: OnDemandVideoCategory, videoId: string): string {
        return DeeplinkUtil.getRecipeDeeplinkKeyHash(tenant, Keys.ONDEMAND_PREMIERE_DEEPLINK) + collectionCategory + DeeplinkUtil.separator + videoId
    }

}

enum Keys {
    RECIPE_DEEPLINK = "DEEPLINK",
    ONDEMAND_VIDEO_DEEPLINK = "ON_DEMAND_VIDEO",
    ONDEMAND_PREMIERE_DEEPLINK = "ONDEMAND_PREMIERE_DEEPLINK"
}
