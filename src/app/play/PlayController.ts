import { Container, inject } from "inversify"
import { controller, httpGet } from "inversify-express-utils"
import AuthMiddleware from "../auth/AuthMiddleware"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import * as express from "express"
import { ProductDetailPage } from "../common/views/WidgetView"
import { UserContext } from "@curefit/userinfo-common"
import * as _ from "lodash"
import GymfitPackDetailViewBuilder from "../pack/GymfitPackDetailViewBuilder"
import PlayPackDetailViewBuilder from "../pack/PlayPackDetailViewBuilder"
import { CenterResponse } from "@curefit/center-service-common"
import { CENTER_SERVICE_CLIENT_TYPES, ICenterService } from "@curefit/center-service-client"
import { CacheHelper } from "../util/CacheHelper"
import { ProductType } from "@curefit/product-common"
import { ErrorCodes } from "../error/ErrorCodes"
import PlayUpgradeDetailViewBuilder from "../pack/PlayUpgradeDetailViewBuilder"

export function playControllerFactory(kernel: Container) {
@controller("/play",
    kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)

    class PlayController {
        constructor(
            @inject(CUREFIT_API_TYPES.PlayPackDetailViewBuilder) private playPackDetailViewBuilder: PlayPackDetailViewBuilder,
            @inject(CENTER_SERVICE_CLIENT_TYPES.CenterService) private centerService: ICenterService,
            @inject(CUREFIT_API_TYPES.PlayUpgradeDetailViewBuilder) private playUpgradeDetailViewBuilder: PlayUpgradeDetailViewBuilder,
        ) {
        }

        @httpGet("/packInfo")
        async getPackInfo(req: express.Request): Promise<ProductDetailPage> {
            const productId: string = req.query.productId
            const selectedStartDate: string = req.query.startDate
            const preferredCenterId: string = req.query.preferredCenterId
            const membershipId: string = req.query.membershipId
            const workoutId: string = req.query.workoutId
            const workoutName: string = req.query.workoutName
            const userContext: UserContext = req.userContext as UserContext
            if (_.isEmpty(membershipId)) {
                let centerResponsePromise
                if (preferredCenterId) {
                    centerResponsePromise = this.centerService.getCenterById(Number(preferredCenterId))
                }
                return this.playPackDetailViewBuilder.getPrePurchaseView(
                    userContext,
                    productId,
                    selectedStartDate,
                    preferredCenterId,
                    centerResponsePromise,
                    workoutId,
                    workoutName,
                )
            } else {
                return this.playPackDetailViewBuilder.getPostPurchaseView(userContext, parseInt(membershipId))
            }
        }

        @httpGet("/upgrademembership")
        async upgradeMembershipDetails(req: express.Request): Promise<{ widgets: Array<any> }> {
            const membershipId: string = req.query.membershipId as string
            const packId: string = req.query.packId as string
            const productType: ProductType = req.query.productType as ProductType
            const centerId: number = req.query.centerId as number
            const userContext: UserContext = req.userContext as UserContext
            const workoutId: string = req.query.workoutId
            const workoutName: string = req.query.workoutName

            return this.playUpgradeDetailViewBuilder.buildView(userContext, centerId, packId, parseInt(membershipId), workoutName, workoutId)
        }
    }

    return PlayController
}

export default playControllerFactory
