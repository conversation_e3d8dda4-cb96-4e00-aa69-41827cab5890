import * as express from "express"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import { Container, inject } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { MASKED_HCM_NUMBERS } from "@curefit/base-utils"
import { IFoodShipmentReadonlyDao } from "@curefit/shipment-models"
import { ErrorFactory } from "@curefit/error-client"
import { WorkflowUtil, Timezone } from "@curefit/util-common"
import { IApiKeyService, BASE_UTILS_TYPES } from "@curefit/base-utils"
import * as _ from "lodash"
import AuthMiddleware from "../auth/AuthMiddleware"
import { IPilotReadonlyDao, FLASH_MODELS_TYPES } from "@curefit/flash-models"
import { IPilotStateReadonlyDao } from "@curefit/flash-models"
import { ILotReadonlyDao } from "@curefit/flash-models"
import { CartShipment } from "@curefit/eat-common"
import { ISMSService, SMS_CLIENT_TYPES } from "@curefit/sms-client"
import { ICartShipmentReadWriteDao, SHIPMENT_MODELS_TYPES } from "@curefit/shipment-models"
import { Logger, BASE_TYPES } from "@curefit/base"
import { IUserService } from "@curefit/user-client"
import { SlotUtil } from "@curefit/eat-util"
import { ShipmentUtil } from "@curefit/alfred-client"
import { TimeUtil } from "@curefit/util-common"
import { USER_CLIENT_TYPES } from "@curefit/user-client"
import { DELIVERY_CLIENT_TYPES, IDeliveryAreaService } from "@curefit/delivery-client"
import { ILockAccess, LOCK_TYPES, RedlockAccess } from "@curefit/lock-utils"
import { ErrorCodes } from "../error/ErrorCodes"
import { CallType } from "@curefit/logistics-common"
import { MultiRedisAccess, REDIS_TYPES } from "@curefit/redis-utils"

const CART_UPDATE_LOCK_TIME_IN_MILLIS = 5000

export function controllerFactory(kernel: Container) {
    @controller("/logistics", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class LogisticsController {

        private lockAccess: ILockAccess

        constructor(@inject(SHIPMENT_MODELS_TYPES.CartShipmentReadwriteDao) private cartShipmentDao: ICartShipmentReadWriteDao,
                    @inject(SHIPMENT_MODELS_TYPES.FoodShipmentReadonlyDao) private foodShipmentDao: IFoodShipmentReadonlyDao,
                    @inject(FLASH_MODELS_TYPES.PilotReadonlyDao) private pilotDao: IPilotReadonlyDao,
                    @inject(FLASH_MODELS_TYPES.PilotStateReadonlyDao) private pilotStateDao: IPilotStateReadonlyDao,
                    @inject(FLASH_MODELS_TYPES.LotReadonlyDao) private lotDao: ILotReadonlyDao,
                    @inject(BASE_UTILS_TYPES.ApiKeyService) private apiKeyService: IApiKeyService,
                    @inject(SMS_CLIENT_TYPES.SMSService) private smsService: ISMSService,
                    @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
                    @inject(DELIVERY_CLIENT_TYPES.DeliveryAreaService) private delAreaSvc: IDeliveryAreaService,
                    @inject(REDIS_TYPES.MultiRedisAccess) private multiRedisAccess: MultiRedisAccess,
                    @inject(BASE_TYPES.ILogger) private logger: Logger,
                    @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        ) {
            this.lockAccess = new RedlockAccess(this.multiRedisAccess, logger, "CFAPI-CACHE")
        }

        @httpGet("/customernumber")
        public async getCustomerNumber(req: express.Request): Promise<string> {
            const apiKey = await this.apiKeyService.getApiKey(req.headers["apikey"] as string)
            if (!apiKey) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid api key").build()
            }
            if (!_.includes(apiKey.actions, "FETCH_SHIPMENT_PHONE")) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid api key").build()
            }
            if (process.env.ENVIRONMENT === "STAGE") {
                return "200|dial=9538209544"
            }
            const shipmentPin = req.query.pin
            const pilotPhone = req.query.from

            if (pilotPhone === "9986312121" && shipmentPin === "2121") {
                // TODO SFX integration
                return "200|dial=7838924034"
            }
            if (pilotPhone === "9966670000" && shipmentPin === "9876") {
                // TODO SFX integration
                return "200|dial=8197867418"
            }

            if (pilotPhone === "9607962604" && shipmentPin === "5432") {
                // TODO XB integration
                return "200|dial=9923400974"
            }

            const cartShipment = await this.getShipmentFromPin(shipmentPin, pilotPhone)
            const shipments = await this.foodShipmentDao.find({ condition: { cartShipmentId: cartShipment.cartShipmentId } })
            const state = WorkflowUtil.getCurrentState(shipments[0].workflow)
            if (state.state === "DELIVERED" || state.state === "REJECTED") {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid order").build()
            }
            return "200|dial=" + cartShipment.userAddress.phoneNumber
        }

        @httpGet("/hcmnumber")
        public async getHCMNumber(req: express.Request, resp: express.Response): Promise<any> {
            const apiKey = await this.apiKeyService.getApiKey(req.headers["apikey"] as string)
            if (!apiKey) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid api key").build()
            }
            if (!_.includes(apiKey.actions, "FETCH_SHIPMENT_PHONE")) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid api key").build()
            }
            if (process.env.ENVIRONMENT === "STAGE") {
                return "200|dial=9538209544"
            }
            const customerNumber = req.query.from
            const calledNumber = req.query.to
            const timezone = TimeUtil.IST_TIMEZONE
            const data = await this.getPilotPhoneNumber(customerNumber, calledNumber, timezone)
            resp.header("Cache-Control", "private, no-cache, no-store, must-revalidate")
            resp.header("Expires", "-1")
            resp.header("Pragma", "no-cache")
            return resp.status(data.status).send(data.data)
        }

        @httpPost("/calldetails")
        public async updateCallDetails(req: express.Request): Promise<any> {

            const callDuration = req.query.DialCallDuration ? req.query.DialCallDuration : 0
            const startTime = req.query.StartTime
            const endTime = req.query.EndTime ? req.query.EndTime : undefined
            const callType: CallType = req.query.CallType
            const fwdTo = req.query.fwdTo
            const from = req.query.From
            const to = req.query.To
            const recordingUrl = req.query.RecordingUrl ? req.query.RecordingUrl : undefined
            const digitPressed = req.query.DigitPressed
            const isCustomerCall = _.some(MASKED_HCM_NUMBERS, number => number.endsWith(to))
            let cartShipment: CartShipment
            if (isCustomerCall) {
                const user = await this.userService.getUserUsingPhone(from)
                if (_.isNil(user)) {
                    return { success: false }
                }
                const timezone = TimeUtil.IST_TIMEZONE
                cartShipment = await this.getMatchingShipment(to, user.id, timezone)
            } else {
                cartShipment = await this.getShipmentFromPin(digitPressed, from)
            }

            this.logger.info(`Taking lock on ${cartShipment.cartShipmentId} for updating call details`)
            const cartLock = await this.lockAccess.lockResource(cartShipment.cartShipmentId, CART_UPDATE_LOCK_TIME_IN_MILLIS)
            this.logger.info(`Got lock`)
            try {

                // reload after getting lock
                cartShipment = await this.cartShipmentDao.findOne({ cartShipmentId: cartShipment.cartShipmentId })

                const callAttempts = cartShipment.callAttempts ? cartShipment.callAttempts : []
                callAttempts.push({
                    callDuration: callDuration,
                    startTime,
                    endTime,
                    callType,
                    customerNumber: isCustomerCall ? from : fwdTo,
                    crewNumber: isCustomerCall ? fwdTo : from,
                    providerNumber: to,
                    recordingUrl,
                    pin: digitPressed,
                    isCustomerCall,
                    timestamp: new Date()
                })
                if (!isCustomerCall && (callAttempts.length === 1) && (callType !== "ANSWER")) {
                    // First call. SMS needs to be sent
                    await this.sendSMS(cartShipment.userAddress.phoneNumber)
                }
                cartShipment.callAttempts = callAttempts
                await this.cartShipmentDao.findOneAndUpdate({ cartShipmentId: cartShipment.cartShipmentId }, cartShipment)
            } finally {
                this.logger.info(`Releasing lock`)
                this.lockAccess.unlockResource(cartLock)
            }
            return { success: true }
        }

        @httpGet("/acceptcall/:phoneNumber")
        public async acceptCall(req: express.Request, resp: express.Response): Promise<any> {
            const phoneNumber = req.params.phoneNumber
            if (_.includes(["9986312121", "9966670000"], phoneNumber)) {
                // TODO SFX integration
                return resp.status(200).send("200")
            }

            if (_.includes(["9607962604"], phoneNumber)) {
                // TODO XB integration
                return resp.status(200).send("200")
            }

            const pilot = await this.pilotDao.findOne({ phone: phoneNumber, status: "LIVE" })
            if (_.isNil(pilot)) {
                return resp.status(300).send("300")
            } else {
                return resp.status(200).send("200")
            }
        }

        @httpGet("/acceptcustomercall/:customerNumber/:calledNumber")
        public async acceptCustomerCall(req: express.Request, resp: express.Response): Promise<any> {
            const customerNumber = req.params.customerNumber
            const calledNumber = req.params.calledNumber
            const timezone = TimeUtil.IST_TIMEZONE
            const data = await this.getPilotPhoneNumber(customerNumber, calledNumber, timezone)
            resp.header("Cache-Control", "private, no-cache, no-store, must-revalidate")
            resp.header("Expires", "-1")
            resp.header("Pragma", "no-cache")
            return resp.status(data.status).send(data.data)
        }

        private async getPilotPhoneNumber(customerNumber: string, calledNumber: string, timezone = TimeUtil.IST_TIMEZONE):
            Promise<{ status: number, data: string }> {
            // Get customer from phone number
            const user = await this.userService.getUserByPhoneWithDefaultCountryCode(customerNumber)
            this.logger.info("Got numbers: " + customerNumber + " " + calledNumber)
            if (_.isNil(user)) {
                this.logger.info("Not a valid user number")
                return { status: 200, data: "300" }
            }
            // Get active cart shipments based on this number
            const shipment = await this.getMatchingShipment(calledNumber, user.id, timezone)
            if (_.isNil(shipment)) {
                this.logger.info("No valid shipments found for " + user.id)
                return { status: 200, data: "400" }
            }
            const foodShipment = await this.foodShipmentDao.findOne({ cartShipmentId: shipment.cartShipmentId })
            // Check if shipment matches criteria
            if (!ShipmentUtil.canCustomerCall(shipment, foodShipment)) {
                this.logger.info("Shipment criteria not met " + shipment.cartShipmentId)
                return { status: 200, data: "400" }
            }

            // Get lot for shipment
            const matchingLots = await this.lotDao.find({
                condition: {
                    date: TimeUtil.todaysDate(timezone),
                    "consignments.shipments.cartShipmentId": shipment.cartShipmentId
                }
            })
            this.logger.info("Found matchings lots: " + matchingLots.length)
            const matchingLot = (matchingLots.length === 1) ? matchingLots[0] : matchingLots.find(l => {
                const matching = _.flatten(l.consignments.map(c => c.shipments)).find(s => s.cartShipmentId === shipment.cartShipmentId)
                return (matching.status === "ACTIVE")
            })
            if (_.isNil(matchingLot) || _.isNil(matchingLot.pilotId)) {
                this.logger.info("No matching lot")
                return { status: 200, data: "400" }
            }
            this.logger.info("Found lot: " + matchingLot.lotId + " and pilot: " + matchingLot.pilotId)
            const pilot = await this.pilotDao.findOne({ pilotId: matchingLot.pilotId })
            return { status: 200, data: `200|dial=${pilot.phone}` }
        }

        private getMatchingShipment(calledNumber: string, userId: string, timezone: Timezone) {
            const santizedCalledNumber = "+91" + calledNumber.substring(calledNumber.length - 10)
            const slots = SlotUtil.getDeliverySlots(SlotUtil.getCurrentMealSlotExact(timezone)).map(s => s.slotId)
            return this.cartShipmentDao.findOne({
                deliveryDate: TimeUtil.todaysDate(timezone),
                deliverySlot: { $in: slots },
                crewPhoneNumber: santizedCalledNumber,
                userId: (userId + ""),
                deliveryChannel: "ONLINE"
            })
        }

        @httpGet("/clicktocalldetails")
        public async clickToCallDetails(req: express.Request): Promise<any> {
            const leg1 = req.query.leg1
            const leg2 = req.query.leg2
            const status1 = req.query.status1
            const status2 = req.query.status2
            const duration = req.query.duration
            const billsec = req.query.billsec
            const startTime = req.query.start
            const endTime = req.query.end
            const url = req.query.path
            const cartShipmentId = req.query.cartShipmentId

            this.logger.info(`Taking lock on ${cartShipmentId} for updating clicktocalldetails`)
            const cartLock = await this.lockAccess.lockResource(cartShipmentId, CART_UPDATE_LOCK_TIME_IN_MILLIS)
            this.logger.info(`Got lock`)
            try {
                const cartShipment = await this.cartShipmentDao.findOne({ cartShipmentId })
                if (_.isNil(cartShipment)) {
                    throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage(`Not a valid shipment`).build()
                }
                if (_.isNil(cartShipment.callAttempts)) {
                    cartShipment.callAttempts = []
                }
                let callType: CallType = status2 ? status2 : status1
                callType = callType ? callType : "FAILED"
                cartShipment.callAttempts.push({
                    callDuration: billsec,
                    startTime,
                    endTime,
                    callType: callType,
                    customerNumber: leg2,
                    crewNumber: leg1,
                    recordingUrl: url,
                    isCustomerCall: false,
                    timestamp: new Date()
                })
                if ((cartShipment.callAttempts.length === 1) && (status2 !== "ANSWER")) {
                    // First call. SMS needs to be sent
                    await this.sendSMS(cartShipment.userAddress.phoneNumber)
                }
                await this.cartShipmentDao.findOneAndUpdate({ cartShipmentId }, cartShipment)
            } finally {
                this.logger.info(`Releasing lock`)
                this.lockAccess.unlockResource(cartLock)
            }
            return true
        }

        private async getShipmentFromPin(shipmentPin: string, pilotPhone: string): Promise<CartShipment> {
            if (process.env.ENVIRONMENT === "STAGE") {
                return this.cartShipmentDao.findOne({ cartShipmentId: "8000002348" })
            }
            const pilot = await this.pilotDao.findOne({ phone: pilotPhone, status: "LIVE" })
            if (!pilot) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage(`Cannot find pilot for ${pilotPhone}`).build()
            }
            const pilotState = await this.pilotStateDao.findOne({ pilotId: pilot.pilotId })
            if (!pilotState || !pilotState.workflow) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage(`Invalid order`).build()
            }
            const lotId = pilotState.workflow.meta.lotId
            if (!lotId) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage(`Invalid order`).build()
            }
            const lot = await this.lotDao.findOne({ lotId })
            const cartShipmentIds = _.flatten(lot.consignments.map(consignment => consignment.shipments.map(s => s.cartShipmentId)))
            const cartShipmentId = cartShipmentIds.find(shipmentId => shipmentId.endsWith(shipmentPin))
            if (!cartShipmentId) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage(`Invalid order`).build()
            }
            const cartShipment = await this.cartShipmentDao.findOne({ cartShipmentId })
            return cartShipment
        }

        private async sendSMS(phone: string): Promise<boolean> {
            if (_.isNil(phone) || phone.length === 0) {
                return false
            }
            const sanitizedPhone = this.smsService.sanitizeNumber(phone)
            this.logger.info("Send call failed sms to " + phone)
            await this.smsService.sendSMS(sanitizedPhone,
                "Our health crew tried reaching you to deliver your meal but was not able to get through to you. He is currently at your location. Please collect your order at the earliest.")
            return true
        }
    }

    return LogisticsController
}

export default controllerFactory
