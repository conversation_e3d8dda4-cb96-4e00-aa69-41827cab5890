import { injectable, inject } from "inversify"
import { Logger } from "@curefit/base"
import { onVideoCompletion, onLogin, onLogout, onSignup } from "./GympassEventListener"
import { ExternalSourceUtil, Sources } from "./ExternalSourceUtil"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import * as _ from "lodash"
import { USER_CLIENT_TYPES, IUserService } from "@curefit/user-client"
import { BASE_TYPES, FetchUtilV2 } from "@curefit/base"
import { Tenant, User } from "@curefit/user-common"
import { CacheHelper } from "../util/CacheHelper"

const events = require("events")
export enum eventName {
    VIDEO_COMPLETION = "VIDEO_COMPLETION",
    LOGIN = "LOGIN",
    LOGOUT = "LOGOUT",
    SIGNUP = "SIGNUP"
}

export enum eventTriggerForExternalSource {
    VIDEO_COMPLETION_GYMPASS = "VIDEO_COMPLETION_GYMPASS",
    LOGIN_GYMPASS = "LOGIN_GYMPASS",
    LOGOUT_GYMPASS = "LOGOUT_GYMPASS",
    SIGNUP_GYMPASS = "SIGNUP_GYMPASS"
}

@injectable()
export class CustomEventEmitter {
    eventEmitter: any

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(CUREFIT_API_TYPES.ExternalSourceUtil) private externalSourceUtil: ExternalSourceUtil,
        @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
        @inject(CUREFIT_API_TYPES.GympassConf) private gympassConf: any,
        @inject(BASE_TYPES.FetchUtilV2) protected fetchHelper: FetchUtilV2,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper
    ) {
        this.eventEmitter = new events.EventEmitter()
        this.eventEmitter.on(eventTriggerForExternalSource.VIDEO_COMPLETION_GYMPASS, onVideoCompletion)
        this.eventEmitter.on(eventTriggerForExternalSource.LOGIN_GYMPASS, onLogin)
        this.eventEmitter.on(eventTriggerForExternalSource.LOGOUT_GYMPASS, onLogout)
        this.eventEmitter.on(eventTriggerForExternalSource.SIGNUP_GYMPASS, onSignup)
    }

    public async emitEvent(eventName: eventName, userId: string, source: string, tenant?: Tenant) {
        this.logger.info(`emitter called with eventName: ${eventName}, userId: ${userId}, source: ${source}, tenant: ${tenant}`)
        if (!_.isEmpty(source)) {
            const user = await this.userService.getUser(userId)
            const event = eventName + "_" + source
            this.eventEmitter.emit(event, userId, user.email, this.gympassConf, this.logger, this.fetchHelper)
        }
    }
}
