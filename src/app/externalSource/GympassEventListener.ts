import * as moment from "moment-timezone"
import { Sources } from "./ExternalSourceUtil"
import { Logger, FetchUtilV2 } from "@curefit/base"
import * as _ from "lodash"
const fetch = require("node-fetch")

enum GympassEvents {
    SIGNUP = "signup",
    LOGIN = "signin",
    LOGOUT = "signout",
    VIDEO = "video",
    AUDIO = "audio",
    OTHER = "other"
}

export async function onVideoCompletion(userId: string, email: string, gympassConf: any, logger: Logger, fetchHelper: FetchUtilV2) {
    logger.info("video completed for Gympass user " + userId)
    const payload = frameGympassPayload(GympassEvents.VIDEO, email)
    await sendEventToGympass(payload, gympassConf[Sources.GYMPASS.toString()], logger, fetchHelper)
}

export async function onSignup(userId: string, email: string, gympassConf: any, logger: Logger, fetchHelper: FetchUtilV2) {
    logger.info("Gympass user " + userId + " has signed up")
    const payload = frameGympassPayload(GympassEvents.SIGNUP, email)
    await sendEventToGympass(payload, gympassConf[Sources.GYMPASS.toString()], logger, fetchHelper)
}

export async function onLogin(userId: string, email: string, gympassConf: any, logger: Logger, fetchHelper: FetchUtilV2) {
    logger.info("Gympass user " + userId + " has logged in")
    const payload = frameGympassPayload(GympassEvents.LOGIN, email)
    await sendEventToGympass(payload, gympassConf[Sources.GYMPASS.toString()], logger, fetchHelper)
}

export async function onLogout(userId: string, email: string, gympassConf: any, logger: Logger, fetchHelper: FetchUtilV2) {
    logger.info("Gympass user " + userId + " has logged out")
    const payload = frameGympassPayload(GympassEvents.LOGOUT, email)
    await sendEventToGympass(payload, gympassConf[Sources.GYMPASS.toString()], logger, fetchHelper)
}

const frameGympassPayload = (actionId: string, email: string) => {
    const payload = [{
        email,
        event_type: actionId,
        timestamp: moment.tz("UTC")
    }]
    return payload
}

async function sendEventToGympass(payload: any, apiConf: any, logger: Logger, fetchHelperV2: FetchUtilV2) {
    try {
        logger.debug("Gympass conf url **** " + apiConf["url"])
        logger.debug("Gympass Payload *** " + JSON.stringify(payload))
        const headers = {
            "Authorization": apiConf["apiKey"],
            "Content-Type": "application/json"
        }
        const response: any = await fetch(apiConf["url"], fetchHelperV2.post({ body: payload, headers }))
        logger.info(`Response status code for user email ${payload[0]["email"]}  is ${response.status}`)
        if (!_.isEqual(200, response.status)) {
            await fetchHelperV2.parseResponse(response)
        }
        logger.debug("Call to Gympass has completed **** " + JSON.stringify(response.status))
    } catch (err) {
        logger.error(`Failure while sending event to Gympass.Event details : ${JSON.stringify(payload)}, error: ${JSON.stringify(err)}`)
    }
}
