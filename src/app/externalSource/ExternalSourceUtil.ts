import { injectable, inject } from "inversify"
import { BASE_TYPES, Logger } from "@curefit/base"
import { IUserService, USER_CLIENT_TYPES, UserAttribute, UserAttributeName } from "@curefit/user-client"
import * as _ from "lodash"
import { LoginPageTypesCompat } from "../util/AppUtil"
import { PageTypes } from "@curefit/apps-common"


export enum Sources {
    GYMPASS = "GYMPASS"
}

@injectable()
export class ExternalSourceUtil {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
    ) { }

    public async createOrUpdateUserAttributes(source: Sources, userId: string) {
            const attribute: UserAttribute = {
                userID: userId as unknown as number,
                attrName: UserAttributeName.ATTRIBUTION_SOURCE,
                attrValue: source.toString()
            }
            this.userService.createOrUpdateUserAttributes(attribute, "")
    }

    private async getExternalSourceAttribution(userId: string, source?: Sources): Promise<UserAttribute[]> {
        const userAttributes: UserAttribute[] = await this.userService.getAllLinkedAttributesForUser(userId, [UserAttributeName.ATTRIBUTION_SOURCE])
        return _.isEmpty(source) ? userAttributes : userAttributes && userAttributes.filter(attribute => {
            return _.isEqual(attribute.attrValue, source)
        })
    }

    public async getExternalSourceValue(userId: string): Promise<string> {
        const userAttributes: UserAttribute[] = await this.getExternalSourceAttribution(userId)
        return !_.isEmpty(userAttributes) ? userAttributes[0]?.attrValue : ""
    }


}
