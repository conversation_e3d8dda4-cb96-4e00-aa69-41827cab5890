import { inject, injectable } from "inversify"
import { BASE_TYPES, Logger } from "@curefit/base"
import * as express from "express"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { ISessionBusiness as ISessionService } from "@curefit/base-utils/dist/src/services/ISessionService"
import { UserContext } from "@curefit/userinfo-common"
import { User } from "@curefit/user-common"
import { ErrorCodes } from "../error/ErrorCodes"
import { ErrorFactory } from "@curefit/error-client"

@injectable()
export class MultiDeviceHandlingMiddleware {
    MIN_ANDROID_APP_VERSION_SUPPORTED_FOR_PRIMARY_DEVICE_BINDING: number = 10.19
    MIN_IOS_APP_VERSION_SUPPORTED_FOR_PRIMARY_DEVICE_BINDING: number = 10.19
    USER_WHITELIST_FOR_LOGGING: string[] = ["91579728"]

    constructor(@inject(BASE_TYPES.ILogger) private logger: Logger,
                @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
                @inject(CUREFIT_API_TYPES.SessionService) private sessionBusiness: ISessionService) {
        this.validateSession = this.validateSession.bind(this)
    }
    public async validateSession(req: express.Request, res: express.Response, next: express.NextFunction): Promise<void> {
        try {
            this.logger.debug("Inside MultiDeviceHandlingMiddleware")
            const userContext: UserContext = req.userContext
            const user: User = await userContext.userPromise
            this.log("User " + JSON.stringify(user), user.id)
            if (user.isFlaggedForMultiDeviceUsage) {
                this.log(`userId ${user.id} is flagged. Checking deviceId`, user.id)
                const deviceId: string = userContext.sessionInfo.deviceId
                const primaryDeviceId: string = user.primaryDeviceId
                if (!primaryDeviceId) {
                    this.log(`userId ${user.id} has not yet registered the primary device. Raising alert to register the primary device`, user.id)
                    this.checkMinAppVersionSupportForPrimaryDeviceBindingFeature(userContext)
                    throw this.errorFactory.withCode(ErrorCodes.REGISTER_PRIMARY_DEVICE_ERR, 456).withDebugMessage("Please register your device and login from it to check in").build()
                } else {
                    if (primaryDeviceId !== deviceId) {
                        this.log(`For userId ${user.id}, deviceId ${deviceId} is not primary device. Raising alert to check in from primary device`, user.id)
                        throw this.errorFactory.withCode(ErrorCodes.CHECKIN_FROM_PRIMARY_DEVICE, 456).withDebugMessage("This is not the registered device. Please login from registered device to check in").build()
                    } else {
                        this.log(`userId ${user.id} checking in from primary device`, user.id)
                    }
                }
            } else {
                this.log(`userId ${user.id} is not flagged`, user.id)
            }
            next()
        } catch (err) {
            next(err)
        }
    }

    private log(logLine: string, userId: string) {
        if (this.USER_WHITELIST_FOR_LOGGING.includes(userId)) {
            this.logger.info(logLine)
        } else {
            this.logger.debug(logLine)
        }
    }

    private checkMinAppVersionSupportForPrimaryDeviceBindingFeature(userContext: UserContext) {
        const appVersion = userContext.sessionInfo.appVersion || userContext.sessionInfo.clientVersion
        const osName = userContext.sessionInfo.osName
        if (osName === "android" && appVersion < this.MIN_ANDROID_APP_VERSION_SUPPORTED_FOR_PRIMARY_DEVICE_BINDING) {
            throw this.errorFactory.withCode(ErrorCodes.APP_VERSION_NOT_SUPPORTED_ANDROID_ERR, 456).withDebugMessage("Please update your app to register your primary device").build()
        }
        else if (osName === "ios" && appVersion < this.MIN_IOS_APP_VERSION_SUPPORTED_FOR_PRIMARY_DEVICE_BINDING) {
            throw this.errorFactory.withCode(ErrorCodes.APP_VERSION_NOT_SUPPORTED_IOS_ERR, 456).withDebugMessage("Please update your app to register your primary device").build()
        }

    }
}