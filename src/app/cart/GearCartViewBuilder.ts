import * as _ from "lodash"
import { inject, injectable } from "inversify"
import { Lo<PERSON>, BASE_TYPES } from "@curefit/base"
import { Order, OrderProductSnapshots, ProductAvailability } from "@curefit/order-common"
import { ProductPrice } from "@curefit/product-common"
import { User } from "@curefit/user-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { UserDeliveryAddress } from "@curefit/eat-common"
import { ShipmentArrivalInfo } from "@curefit/oms-api-client"
import { RUPEE_SYMBOL } from "@curefit/finance-common"
import {
  Action,
  GearAddressWidget,
  GearShipmentArrivalInfoWidget,
  PriceDetailsWidget,
  ProductDetailPage,
  WidgetType,
  WidgetView,
  GearCartAddonWidget,
  GearAddon,
  GearCartAddonsBuildParams,
  FormattedTextItem,
  FormattedTextWidget,
  NeuPassEarnedCoinsWidget,
  getOffersWidget,
  OfferCalloutWidget,
  CSCheckoutVoucherWidgetV2
} from "../common/views/WidgetView"
import { GearUtil, IGearService, GEARVAULT_CLIENT_TYPES } from "@curefit/gearvault-client"
import { OrderMeta } from "@curefit/apps-common"
import { ActionUtil } from "@curefit/base-utils"
import { Timezone, TimeUtil } from "@curefit/util-common"
import { CatalogueVariant, CatalogueProduct, Annotation } from "@curefit/gear-common"
import AppUtil from "../util/AppUtil"
import { UserContext } from "@curefit/vm-models"
import { OFFER_SERVICE_CLIENT_TYPES, OfferServiceV3 } from "@curefit/offer-service-client"
import { OfferV2 } from "@curefit/offer-common"
import GearProductViewBuilder from "../product/GearProductViewBuilder"
import {
  IThirdPartyService,
  PotentialTataPointsEarnResponse,
  THIRD_PARTY_CLIENT_TYPES,
  TataUtil
} from "@curefit/third-party-integrations-client"
import { TataNeuUtil } from "../util/TataNeuUtil"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { IUserSegmentClient, SEGMENTATION_CLIENT_TYPES, UserExistenceInSegmentResponse } from "@curefit/segmentation-service-client"
import OrderViewBuilder from "../order/OrderViewBuilder"
import MixpanelEventService from "../cultsport/MixpanelEventService"

export interface BaseGearCartView extends ProductDetailPage {

}

export interface GearCartReviewView extends BaseGearCartView {
  showDeliveryInfo?: boolean
}

const PRIMARY_IMAGE_TAG = "BOGO_IMAGE"

@injectable()
export class GearCartViewBuilder {

  constructor(
    @inject(BASE_TYPES.ILogger) private logger: Logger,
    @inject(GEARVAULT_CLIENT_TYPES.GearService) private gearService: IGearService,
    @inject(GEARVAULT_CLIENT_TYPES.GearUtil) private gearUtil: GearUtil,
    @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) private offerServiceV3: OfferServiceV3,
    @inject(THIRD_PARTY_CLIENT_TYPES.ThirdPartyService) private thirdPartyService: IThirdPartyService,
    @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
    @inject(SEGMENTATION_CLIENT_TYPES.UserSegmentClient) public segmentationClient: IUserSegmentClient,
    @inject(CUREFIT_API_TYPES.OrderViewBuilder) private orderViewBuilder: OrderViewBuilder,
    @inject(CUREFIT_API_TYPES.MixpanelEventService) protected mixpanelEventService: MixpanelEventService
) { }

  async getOffersAppliedText(order: Order, userContext: UserContext): Promise<FormattedTextWidget | OfferCalloutWidget> {
    if (!order.offersApplied || order.offersApplied.length < 1) {
      return null
    }
    let allOffers: { [key: string]: OfferV2 }
    try {
      allOffers = (await this.offerServiceV3.getOffersByIds(order.offersApplied)).data
    }
    catch (err) {
      this.logger.error("Error while fetching offerdetails ", err)
      return null
    }

    if (AppUtil.isCultSportWebApp(userContext)) {
      const finalOffers = order.offersApplied.map((appliedOfferId, index) => {
        if (!allOffers[appliedOfferId]) {
          return null
        }
        return allOffers[appliedOfferId]
      }).filter(Boolean)
      if (_.isEmpty(finalOffers)) {
        return null
      }
      return getOffersWidget("Offers Applied", finalOffers, userContext.sessionInfo.userAgent, true)
    }

    const offerTextTemplate: FormattedTextItem = {
      text: "",
      fontColor: "#4A4A4A",
      fontSize: 16,
      fontWeight: "BOLD",
      lineHeight: 22
    }

    const plusSignTemplate: FormattedTextItem = {
      text: "",
      fontColor: "#4A4A4A",
      fontSize: 16,
      fontWeight: "BOLD",
      lineHeight: 22
    }
    const offerFormattedTitles: FormattedTextItem[] = []

    order.offersApplied.forEach((appliedOfferId, index) => {
      if (!allOffers[appliedOfferId]) {
        return
      }
      offerFormattedTitles.push({
        ...offerTextTemplate,
        text: (`\n${" ".repeat(7)}+ `) || "",
        fontColor: "#FF3278",
        fontWeight: "BOLD",
      })

      offerFormattedTitles.push({
        ...offerTextTemplate,
        text: (`${allOffers[appliedOfferId].title}${index === order.offersApplied.length - 1 ? "\n" : ""}`) || "",
        fontColor: "#7f7f7f",
        fontWeight: "REGULAR",
      })
    })
    const formatText = new FormattedTextWidget([{
      ...offerTextTemplate,
      text: `${" ".repeat(7)}Offers Applied`,
    }, ...offerFormattedTitles])

    return formatText
  }
  async buildGearCartReviewView(userId: string, user: User, order: Order, availabilities: ProductAvailability[], userContext: UserContext,
    arrivalInfo?: ShipmentArrivalInfo, userAgent?: UserAgent, timezone: Timezone = TimeUtil.IST_TIMEZONE, showDeliveryInfo?: boolean, showNewVoucher?: boolean): Promise<GearCartReviewView> {
    const widgetPromises: Promise<WidgetView>[] = []
    const cartItemWidgetPromise = this.getCartLineItemsWidget(order, availabilities, userContext)
    widgetPromises.push(cartItemWidgetPromise)
    widgetPromises.push(this.getOffersAppliedText(order, userContext))
    widgetPromises.push(this.getPriceDetailsWidget(order))
    if (AppUtil.isTataNeuWebFlow(order?.source) || (await this.orderViewBuilder.isTataOrganicTestUser(userContext) && ["CUREFIT_APP", "CUREFIT_NEW_WEBSITE"].includes(order.source))) {
      const segmentsForTataNeu = TataNeuUtil.getSegmentsForTataNeu()
      // TODO: Enable this check later when Tata asks to show the checkbox again.
      // const doesNeuPassConsentPending: boolean = await TataNeuUtil.doesUserBelongToTataSegment(this.segmentationClient, segmentsForTataNeu.NEUPASS_CONSENT_PENDING, userId)
      const doesNeuPassConsentPending: boolean = false
      if (TataUtil.isTataNeuProduct(order) && doesNeuPassConsentPending) {
        const activationCheckboxWidget: WidgetView = await this.orderViewBuilder.getNeupassActivationWidget(order, userContext?.userProfile?.userId, false)
        if (!_.isEmpty(activationCheckboxWidget)) {
          widgetPromises.push(new Promise<WidgetView>((resolve, reject) => {
            resolve(activationCheckboxWidget)
          }))
        }
      } else {
        const isNeuPassUser: boolean = await TataNeuUtil.doesUserBelongToTataSegment(this.segmentationClient, segmentsForTataNeu.NEUPASS_USER, userId)
        if (TataUtil.isOrderSourceSupportedForTataNeu(order) && TataUtil.isTataNeuProduct(order) && isNeuPassUser) {
          const potentialEarnWidget: NeuPassEarnedCoinsWidget = await this.orderViewBuilder.getPotentialNeuPassEarnWidget(order, userContext?.userProfile?.userId)
          if (!_.isEmpty(potentialEarnWidget)) {
            widgetPromises.push(new Promise<NeuPassEarnedCoinsWidget>((resolve, reject) => {
              resolve(potentialEarnWidget)
            }))
          }
        }
      }
    }
    widgetPromises.push(this.getGearAddressWidget(order))
    if (showNewVoucher) {
      widgetPromises.push(this.getCSCheckoutVoucherWidget(order, cartItemWidgetPromise))
    }
    if (showDeliveryInfo) {
      widgetPromises.push(this.getGearShipmentArrivalInfoWidget(arrivalInfo || {} as ShipmentArrivalInfo, timezone))
    }

    const availabilitiesMap = _.keyBy(availabilities, "productId")

    this.mixpanelEventService.sendCartReviewEvents(userContext, order, cartItemWidgetPromise, arrivalInfo, availabilitiesMap)

    const widgets = await Promise.all(widgetPromises)
    const nonEmptyWidgets = widgets.filter(widget => !_.isEmpty(widget))

    const numProducts = _.sum(order.products.map(p => { return p.quantity }))
    const useWebActions = userAgent === "DESKTOP" || userAgent === "MBROWSER"

    const actions: Action[] = []
    if (order?.userAddress && !_.isEmpty(availabilities) && availabilities.findIndex(item => !item.isServiceable) >= 0) {
      actions.push({
        actionType: "CHANGE_ADDRESS",
        url: ActionUtil.selectAddress("gear_cart_review"),
        title: "Change Address"
      })
    } else if (numProducts === 0) {
      actions.push({
        actionType: "POP_ACTION",
        title: "Out of Stock"
      })
    } else if (arrivalInfo && arrivalInfo.eta) {
      if (order.totalAmountPayable === 0) {
        actions.push({
          title: "Get for free",
          actionType: useWebActions ? "PAY_FREE" : "GEAR_PAY_FREE"
        })
      } else {
        actions.push({
          actionType: useWebActions ? "CART_PAY_NOW" : "GEAR_CART_PAY_NOW",
          url: ActionUtil.cartPayment("gear_cart_review"),
          title: "Checkout"
        })
      }
    } else {
      actions.push({
        actionType: "CHANGE_ADDRESS",
        url: ActionUtil.selectAddress("gear_cart_review"),
        title: "Select Address"
      })
    }

    return { widgets: nonEmptyWidgets, actions, showDeliveryInfo: showDeliveryInfo }
  }

  async buildGearCartCheckoutView(user: User, order: Order): Promise<GearOrderCheckoutDetail> {
    return {
      orderMeta: {
        orderId: order.orderId,
        customerName: `${user.firstName} ${user.lastName}`,
        customerEmail: user.email,
        customerPhone: user.phone,
        price: {
          listingPrice: order.priceDetails.total_payable,
          mrp: order.priceDetails.total_without_discount
        },
        orderType: "GEAR",
        title: order.productSnapshots[0].title,
        displayText: order.productSnapshots[0].title,
        vertical: "CULT_GEAR",
        gearOrderId: order.gearOrderId,
        offersApplied: order.offersApplied,
        couponCode: order.couponCode,
        productIds: order?.productSnapshots?.map((product) => { return product.masterProductId }).filter(Boolean), // overriding productIds with master product ids as we do not need the sku ids
      } as GearOrderMeta
    }
  }

  private async getGearAddressWidget(order: Order): Promise<GearAddressWidget> {
    const address: UserDeliveryAddress = order.userAddress
    return new GearAddressWidget(address)
  }

  private async getCSCheckoutVoucherWidget(order: Order, gearCartListWidgetpromise: Promise<GearCartListWidget>): Promise<CSCheckoutVoucherWidgetV2> {
    const gearCartListWidget = await gearCartListWidgetpromise
    this.logger.info("getCSCheckoutVoucherWidget orderItems " + JSON.stringify(gearCartListWidget.orderItems))
    return order.priceDetails ? new CSCheckoutVoucherWidgetV2(order, gearCartListWidget) : undefined
  }

  private async getCartLineItemsWidget(order: Order, availabilities: ProductAvailability[], userContext: UserContext): Promise<GearCartListWidget> {
    const skus: string[] = _.map(availabilities, (availability: ProductAvailability) => {
      return GearUtil.reduceProductIdToSku(availability.productId)
    })

    const isGearBrandNameSupported = AppUtil.isGearBrandNameSupported(userContext)
    const variants: CatalogueVariant[] = await this.gearUtil.getGearVariantsFromSkus(skus)

    const availabilitiesMap = _.keyBy(availabilities, "productId")
    const snapshotMap = _.keyBy(order.productSnapshots, "productId")

    const lineItems: GearCartLineItem[] = _.map(variants, (variant: any) => {
      const availability: ProductAvailability = availabilitiesMap[variant.sku]
      const fulfilmentError = availability.isServiceable === false ?
        "Address not serviceable" :
        (availability.inStock ? "" : "Selected quantity not available")

      const thumbnailImage = _.isEmpty(variant.images)
        ? "DUMMY"
        : (_.find(variant.images, i => _.includes(i.tags, PRIMARY_IMAGE_TAG)) || variant.images[0])
      const gearCartLineItem: GearCartLineItem = {
        productId: variant.sku,
        productName: isGearBrandNameSupported || variant.brand == null ? variant.name : `${variant.brand.toUpperCase()} ${variant.name}`,
        productBrand: variant.brand,
        image: thumbnailImage,
        status: availability.status as string,
        action: {
          actionType: "NAVIGATION",
          url: ActionUtil.gearProduct(variant.master_product_id, variant.sku,  AppUtil.isCultSportWebApp(userContext) ? variant?.slug : undefined)
        },
        price: {
          mrp: Number(variant.price),
          listingPrice: Number(variant.price),
          currency: "INR"
        },
        // stock: availability.available, // removing this field since we should not send stock info to UI
        in_stock: availability.inStock,
        is_serviceable: availability.isServiceable,
        is_increment_enabled: !fulfilmentError,
        fulfilmentError: fulfilmentError,
        quantity: availability.quantity,
        category: variant?.articleType || variant?.category,
        superCategory: variant?.category,
        masterProductId: variant.master_product_id,
        annotations: _.map(variant.annotations, (annotation: string) => GearProductViewBuilder.getStyledAnnotation(annotation)),
        productEdd: availability.productEdd
      }

      const snapshot: OrderProductSnapshots = snapshotMap[variant.sku]
      if (snapshot) {
        snapshot.price.listingPrice = Number(snapshot.price.listingPrice.toFixed(2))
        snapshot.price.mrp = Number(snapshot.price.mrp.toFixed(2))
        gearCartLineItem.price = snapshot.price
        gearCartLineItem.quantity = snapshot.quantity
        if (snapshot.attributes && snapshot.attributes.size) {
          gearCartLineItem.size = snapshot.attributes.size
        }
      }
      return gearCartLineItem
    })
    return new GearCartListWidget(lineItems)
  }

  private async getGearShipmentArrivalInfoWidget(arrivalInfo: ShipmentArrivalInfo, timezone: Timezone): Promise<GearShipmentArrivalInfoWidget> {
    return new GearShipmentArrivalInfoWidget(arrivalInfo.eta, timezone)
  }

  private async getPriceDetailsWidget(order: Order): Promise<PriceDetailsWidget> {
    return new PriceDetailsWidget(GearUtil.getPriceDetailsComponents(order.priceDetails, order, true))
  }

  private async getGearAddonsWidget(userContext: UserContext, addons: CatalogueProduct[]): Promise<GearCartAddonWidget> {
    const cartAddons: GearAddon[] = _.map(addons, (addon: any) => {
      const variant: any = addon.variants[0]
      const actions: Action[] = [{
        actionType: "NAVIGATION",
        url: ActionUtil.gearProduct(addon.id, undefined, AppUtil.isCultSportWebApp(userContext) ? addon?.slug : undefined),
        payload: {
          productId: addon.id
        }
      },
      {
        actionType: "GEAR_ADD_TO_CART",
        payload: {
          productId: addon.id,
          sku: variant.sku,
          name: variant.name
        }
      }
      ]
      return {
        name: addon.name,
        price: addon.price,
        primaryPrice: addon.display_price,
        secondaryPrice: addon.discount_percent ? `${RUPEE_SYMBOL} ${addon.mrp}` : undefined,
        image_url: _.get(addon, "master.images[0].mini_url", ""),
        actions
      }
    })
    const buildParams: GearCartAddonsBuildParams = {
      title: "You may also like",
      addons: cartAddons,
    }

    return new GearCartAddonWidget(buildParams)
  }

}

export interface GearCartLineItem {
  productId: string
  productName: string
  productBrand: string
  image: string
  price?: ProductPrice
  action?: Action
  status: string,
  quantity?: number,
  stock?: number,
  size?: string,
  in_stock?: boolean,
  is_serviceable?: boolean,
  is_increment_enabled: boolean,
  fulfilmentError?: string
  annotations?: Annotation[]
  category?: string
  masterProductId?: string
  productEdd?: string
  superCategory?: string
}

export class GearCartListWidget implements WidgetView {
  public widgetType: WidgetType = "GEAR_CART_LIST_WIDGET"
  public orderItems: GearCartLineItem[]

  constructor(orderItems: GearCartLineItem[]) {
    this.orderItems = orderItems
  }
}

export interface GearOrderMeta extends OrderMeta {
  gearOrderId: string
  couponCode?: string
  offersApplied?: string[]
}

export interface GearOrderCheckoutDetail {
  orderMeta: GearOrderMeta
  reportData?: { action: string, meta: any }
}

export default GearCartViewBuilder
