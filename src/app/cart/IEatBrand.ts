import { CartReviewView, CheckoutAddressWidget, OrderItem } from "./CartViewBuilder"
import { inject, injectable } from "inversify"
import  { ALL_MEAL_SLOTS } from "@curefit/eat"
import {
    DeliverySlot, FoodPack, FoodProduct as Product,
    ListingBrandIdType,
    MealSlot,
    MenuAvailabilityResult,
    MenuType,
    UserDeliveryAddress,
    DropInstruction, CutleryInstruction
} from "@curefit/eat-common"
import { UserContext } from "@curefit/userinfo-common"
import { capitalizeFirstLetter, CdnUtil, TimeUtil, Timezone } from "@curefit/util-common"
import {
    Action, ActionSheet,
    ActionSheetOption, AlertInfo,
    DateWiseDeliverySlotAction,
    CafeDeliverySlotAction,
    DeliverySlotAction, FitClubCheckoutSavingsWidget, MealAction, SwitchWidget, WidgetType, WidgetView, SwitchWidgetV3
} from "../common/views/WidgetView"
import { ActionUtil, MAX_CART_SIZE, MealUtil } from "@curefit/base-utils"
import { PriceComponent } from "../order/OrderViewBuilder"
import { DeliveryCharge, ExtraCharge, ProductPrice, UrlPathBuilder } from "@curefit/product-common"
import {
    CartReviewPayload, ClientDeliveryInfo,
    Order,
    OrderProductSnapshots, OrderSource,
    ProductAvailability
} from "@curefit/order-common"
import { BillingInfo } from "@curefit/finance-common"
import EatUtil, { Instruction } from "../util/EatUtil"
import * as _ from "lodash"
import { OfferV2 } from "@curefit/offer-common"
import {
    ArrivalInfo,
    CartReviewResponse,
} from "@curefit/oms-api-client"
import { DEFAULT_CUTLERY_CHARGE, DEFAULT_DELIVERY_CHARGE, PACKAGING_CHARGE } from "../util/OrderUtil"
import AppUtil from "../util/AppUtil"
import { MeasurementUnit, NutritionTag } from "@curefit/food-common"
import { SlotUtil } from "@curefit/eat-util"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { User } from "@curefit/user-common"
import { AlertInfoParams, AlertInfos } from "./AlertInfos"
import { HourMin } from "@curefit/base-common"
import { BaseWidget, IServiceInterfaces, WholefitCartOfferWidget } from "@curefit/vm-models"
import {
    Addon,
    AlertInfoStatus,
    BillingDetails,
    CartAddon,
    FitClubAddon,
    OrderItemInfo,
    ReviewRequest
} from "@curefit/eat-api-client"
import { EatSubscriptionUtil } from "../util/EatSubscriptionUtil"
import { AlertStatus, MarketplaceOrderItemInfo } from "@curefit/eat-api-common"
import { IFitcashService } from "@curefit/fitcash-client"
import { FitClubUtil } from "@curefit/fitclub-client"
import {
    OfferService,
    IOfferServiceV2,
    OFFER_SERVICE_CLIENT_TYPES,
    OfferServiceV3
} from "@curefit/offer-service-client"
import { UserAssignment } from "@curefit/hamlet-common"
import { HCMTippingWidget } from "@curefit/vm-models"
import { ERROR_COMMON_TYPES } from "@curefit/error-common"
import { ErrorFactory } from "@curefit/error-client"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import IUserBusiness from "../user/IUserBusiness"
import FoodMarketplaceUtil from "../util/FoodMarketplaceUtil"
import { ConfigService, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"

interface CartListWidget extends WidgetView {
    title: string
    orderItems: OrderItem[] | MarketplaceOrderItemInfo[]
    cartValue?: number
    priceDetails?: PriceComponent[]
    fitClubSavings?: string
    c1CategoryId?: string
}

export interface BillingWidget extends WidgetView {
    title: string
    cartValue: number
    priceDetails: PriceComponent[]
    fitClubSavings?: string,
    fitcashBalance?: number,
    isFitcashEnabled?: boolean,
    currency?: string,
    deliveryTipTitle?: string
}
export interface AddonItem {
    title: string
    titleWithoutUnits?: string
    recommended?: boolean
    productId: string
    stock: number
    isInventorySet: boolean
    calories: string | number
    categoryId?: string
    foodCategoryId?: string
    offerIds?: string[]
    price: ProductPrice
    actions: Action[]
    image: string
    imageThumbnail?: string
    isVeg: boolean
    difference?: number
    overlayImage?: string,
    nutritiontags?: NutritionTag[],
    mealSlot?: {
        id: MealSlot
        name: string
    },
    salesTag?: string,
    shipmentWeight?: number,
    isAllDayItem?: boolean,
    qty?: number,
    variants?: string[],
    parentProductId?: string,
    unit?: MeasurementUnit,
    displayUnitQty?: string,
    variantTitle?: string,
    listingBrand?: ListingBrandIdType
}

export interface PageActionRequest {
    userContext: UserContext,
    order: Order,
    numProducts: number,
    arrivalInfo?: ArrivalInfo,
    onDemandText?: string,
    mealSlotDeliverySlots?: { [mealSlot: string]: string[] },
    total: number,
    deliveryInfo: ClientDeliveryInfo[],
    isCafe: boolean,
    isDeliverySlotEmpty?: boolean,
    fitClubAddon?: FitClubAddon,
    allSlots?: DeliverySlot[],
    deliveryWindows?: {
        startingHours: HourMin,
        closingHours: HourMin,
        title: string,
        isEnabled: boolean;
        isCheckoutV2Supported?: boolean
    }[]
    billingDetails?: BillingDetails,
    mealSlot?: MenuType,
    currency: string,
    isFitcashEnabled?: boolean
    isCheckoutV2Supported?: boolean
    address?: any
}

interface AddonListWidget extends WidgetView {
    title: string
    subTitle?: string
    addonItems: AddonItem[]
}

interface OffersInfo {
    offerId?: string
    offerVersion?: number
    offerTitle?: string
}

interface OrderDiscountSplit {
    offerId?: string
    offerDiscount?: number
}


class StartDateWidget implements WidgetView {
    widgetType: "ACTION_SHEET_WIDGET"
    icon: "DATE"
    isDisabled: false
    action: ActionSheet

    constructor(date: string, validPackStartDates: string[], userContext: UserContext, timezone: Timezone) {
        validPackStartDates.sort()
        const dateoptions: ActionSheetOption[] = []
        for (let i: number = 0; i < validPackStartDates.length; i++) {
            const dateStr: string = TimeUtil.formatDateStringInTimeZone(validPackStartDates[i], timezone, "Do MMMM")
            dateoptions.push({
                optionId: validPackStartDates[i],
                payload: {
                    date: validPackStartDates[i]
                },
                optionText: "Starting: " + dateStr
            })
        }
        const selectedoptionindex = validPackStartDates.findIndex((validDate) => { return validDate === date })
        this.widgetType = "ACTION_SHEET_WIDGET"
        this.icon = "DATE"
        this.isDisabled = false
        this.action = {
            selectedOption: selectedoptionindex,
            options: dateoptions,
            actionType: "SET_PACK_STARTDATE"
        }
        if (MealUtil.isPackStartDateCalendarSupported(userContext)) {
            this.action = {
                actionType: "SHOW_DATETIME_PICKER_MODAL",
                title: "Start Date",
                selectedOption: 0,
                options: [{ optionText: TimeUtil.formatDateStringInTimeZone(date, timezone, "Do MMMM") }],
                defaultDate: date,
                meta: {
                    defaultDate: date,
                    minimumDate: validPackStartDates[0],
                    maximumDate: validPackStartDates[validPackStartDates.length - 1],
                    pickDateAction: {
                        actionType: "SET_PACK_STARTDATE"
                    }
                }
            }
        }
    }
}

export class DeliverySlotWidgetV3 implements WidgetView {
    title: string
    subTitle?: string
    deliverySlot?: string
    deliveryDate?: string
    isDisabled: boolean
    action: DeliverySlotAction | DateWiseDeliverySlotAction
    widgetType: WidgetType = "DELIVERY_SLOT_WIDGET"

    constructor(title: string, isDisabled: boolean, action: DeliverySlotAction | DateWiseDeliverySlotAction, subtitle?: string, deliverySlot?: string, deliveryDate?: string) {
        this.title = title
        this.subTitle = subtitle
        this.isDisabled = isDisabled
        this.deliverySlot = deliverySlot
        this.deliveryDate = deliveryDate
        this.action = action
    }
}

export class FCStoreWidget implements WidgetView {
    widgetType: WidgetType = "CART_STORE_WIDGET"
    title: string
    imageUrl: string
    address: string

    constructor(title: string, imageUrl: string, address: string) {
        this.title = title
        this.imageUrl = imageUrl
        this.address = address
    }
}

export class DeliveryInstructionWidget {
    widgetType: WidgetType = "COOKING_INSTRUCTIONS_WIDGET"
    title: string
    hint: string
    instruction: string

    constructor(title: string, hint: string, instruction: string) {
        this.title = title
        this.hint = hint
        this.instruction = instruction
    }
}

export interface IDeliveryInstruction {
    type: DropInstruction
    title: string
    description: string
}

export class DeliveryInstructionWidgetV2 {
    widgetType: WidgetType = "DELIVERY_INSTRUCTIONS_WIDGET"
    title: string
    instructions: Instruction[]
    selectedInstruction: Instruction
    contactInstructions: Instruction[]
    modalInfo: { title: string, description: string }
    constructor(title: string, instructions: Instruction[], selectedInstruction: Instruction, modalInfo: { title: string, description: string }, contactInstructions: Instruction[]) {
        this.title = title
        this.instructions = instructions
        this.selectedInstruction = selectedInstruction
        this.contactInstructions = contactInstructions
        this.modalInfo = modalInfo
    }
}

export class CafeDeliverySlotWidget extends DeliverySlotWidgetV3 {
    deliveryWindowTitle?: string
}

@injectable()
export abstract class IEatBrand {
    constructor(@inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
                @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) private offerServiceV2: IOfferServiceV2,
                @inject(CUREFIT_API_TYPES.UserBusiness) protected userBusiness: IUserBusiness,
                @inject(ERROR_COMMON_TYPES.ErrorConfiguration) protected errorFactory: ErrorFactory,
                @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) protected offerServiceV3: OfferServiceV3,
                @inject(PAGE_CONFIG_TYPES.ConfigService) public configService: ConfigService,
                ) { }

    public getBestCartOffers(cartOffers: OfferV2[], order: Order): OfferV2[] {
        const cartSubTotal = order.productSnapshots.reduce((subTotal, product) => {
            return subTotal + (product.price.listingPrice * product.quantity)
        }, 0)
       const filteredOffers = cartOffers.filter((offer) => {
            return offer.constraints.minimumAmount && offer.constraints.minimumAmount > cartSubTotal
        })
        return filteredOffers.sort((bestOffer: OfferV2, currentOffer: OfferV2) => {
            return currentOffer.constraints.minimumAmount <= cartSubTotal ? -1 :
                bestOffer === undefined ? 1 :
                    bestOffer.constraints.minimumAmount > currentOffer.constraints.minimumAmount ? 1 :
                        bestOffer.constraints.minimumAmount < currentOffer.constraints.minimumAmount ? -1 :
                            bestOffer.priority > currentOffer.priority ? -1 : 1
        }).slice(0, 2)
    }

    private getNextOffer(cartOffers: OfferV2[], order: Order, mealSlot: MenuType): OfferV2 {
        const cartSubTotal = order.productSnapshots.reduce((subTotal, product) => {
            return subTotal + (product.price.listingPrice * product.quantity)
        }, 0)
        const nextOffer = cartOffers.reduce((bestOffer: OfferV2, currentOffer: OfferV2) => {
            if (_.isNil(currentOffer.constraints.mealSlots)
                || currentOffer.constraints.mealSlots.indexOf(mealSlot) < 0
                || currentOffer.constraints.minimumAmount <= cartSubTotal
                || (currentOffer.constraints.maximumAmount && currentOffer.constraints.maximumAmount < cartSubTotal)) {
                return bestOffer
            } else {
                if (bestOffer === undefined)
                    return currentOffer
                else if (bestOffer.constraints.minimumAmount === currentOffer.constraints.minimumAmount) {
                    return bestOffer.priority > currentOffer.priority ? bestOffer : currentOffer
                } else if (bestOffer.constraints.minimumAmount < currentOffer.constraints.minimumAmount) {
                    return bestOffer
                } else {
                    return currentOffer
                }
            }
        }, undefined)
        return nextOffer
    }

    public webCartAddressAction(mealSlot: string): Action {
        return {
            actionType: "CHANGE_ADDRESS",
            url: "curefit://selectaddress?pageFrom=cart_checkout",
            title: "Pick Address",
            meta: {
                mealSlot: mealSlot
            }
        }
    }

    public showCartAddressModalAction(mealSlot: string, navigateToSlotPicker: boolean = false): Action {
        return {
            actionType: "SHOW_CART_ADDRESS_MODAL",
            title: "Proceed",
            meta: {
                mealSlot: mealSlot,
                navigateToSlotPicker: navigateToSlotPicker
            }
        }
    }
    private async getPriceDetails(userContext: UserContext, billingDetails: BillingDetails, listingBrand: ListingBrandIdType, deliveryCharge?: DeliveryCharge, packagingCharge?: ExtraCharge, showDelivery?: boolean, showPackaging?: boolean, showBasePrice?: boolean, adjustedAmount?: number, penaltyAmount?: number, showTaxes?: boolean, isFitcashEnabled?: boolean, orderDiscountSplit?: OrderDiscountSplit[], offersInfo?: OffersInfo[], numTickets?: number): Promise<{ components: PriceComponent[], cartValue: number, discountValue: number }> {
        const extraChargeConfig = billingDetails.extraChargeConfig
        const billingInfo: BillingInfo = billingDetails.billingInfo
        const offersApplied = _.map(billingDetails.offersApplied)
        let deliveryChargeForBrand = _.get(extraChargeConfig, "configs.deliveryCharge." + listingBrand, DEFAULT_DELIVERY_CHARGE)
        deliveryChargeForBrand = !_.isNil(numTickets) ? deliveryChargeForBrand * numTickets : deliveryChargeForBrand
        let packagingChargeForBrand = _.get(extraChargeConfig, "configs.packagingCharge." + listingBrand, PACKAGING_CHARGE)
        packagingChargeForBrand += listingBrand === "EAT_FIT" ? _.get(extraChargeConfig, "configs.cutleryCharge" , DEFAULT_CUTLERY_CHARGE) : 0
        packagingChargeForBrand = !_.isNil(numTickets) ? packagingChargeForBrand * numTickets : packagingChargeForBrand
        const priceDetails: PriceComponent[] = []
        const deliveryOffer = _.find(offersApplied, (offerV2) => { return offerV2.noDeliveryCharge === true })
        const packagingOffer = _.find(offersApplied, (offerV2) => { return offerV2.noPackagingCharge === true })
        const cartDiscountDetails: PriceComponent[] = []
        let totalDiscount = 0
        priceDetails.push({
            title: "Item Total",
            value:  billingInfo.mrp.toFixed(2),
            valueWithCurrency: `${AppUtil.getCurrencySymbol(billingDetails.billingInfo.currency)} ${billingInfo.mrp.toFixed(2)}`
        })
        if (!_.isEmpty(orderDiscountSplit)) {
            const filterOrderDiscount = orderDiscountSplit.filter(discount => discount.offerDiscount > 0)
            if (filterOrderDiscount.length) {
                let discountWithoutLabel = 0
                const getDiscountTitle = (await this.offerServiceV3.getOffersByIds(filterOrderDiscount.map(discount => discount.offerId)))?.data
                filterOrderDiscount.forEach(discount => {
                    totalDiscount += discount.offerDiscount
                    const cartLabel = getDiscountTitle[discount.offerId].uiLabels?.cartLabel
                    if (cartLabel) {
                        cartDiscountDetails.push({
                            title: cartLabel,
                            value: discount.offerDiscount.toFixed(2),
                            valueWithCurrency: `-${AppUtil.getCurrencySymbol(billingDetails.billingInfo.currency)}${discount.offerDiscount.toFixed(2)}`,
                            isDiscount: true,
                        })
                    } else {
                        discountWithoutLabel += discount.offerDiscount
                    }
                })
                if (discountWithoutLabel > 0) {
                    cartDiscountDetails.push({
                        title: `Cart Discount`,
                        value: discountWithoutLabel.toFixed(2),
                        valueWithCurrency: `-${AppUtil.getCurrencySymbol(billingDetails.billingInfo.currency)}${discountWithoutLabel.toFixed(2)}`,
                        isDiscount: true,
                    })
                }
            }
        }

        if (billingInfo.discount) {
            const productDiscount = billingInfo.discount - billingInfo.orderDiscount
            if (productDiscount > 0) {
                totalDiscount += productDiscount
                priceDetails.push({
                    title: "Price Discount",
                    value: productDiscount.toFixed(2),
                    valueWithCurrency: `${"–"}${AppUtil.getCurrencySymbol(billingDetails.billingInfo.currency)}${productDiscount.toFixed(2)}`,
                    isDiscount: true,
                })
            }
            if (!_.isEmpty(cartDiscountDetails)) {
                priceDetails.push(...cartDiscountDetails)
            }
            // if (billingInfo.orderDiscount > 0) {
            //     totalDiscount += billingInfo.orderDiscount
            //     priceDetails.push({
            //         title: "Cart Discount",
            //         value: billingInfo.orderDiscount.toFixed(2),
            //         valueWithCurrency: `${ "–" }${AppUtil.getCurrencySymbol(billingDetails.billingInfo.currency)} ${billingInfo.orderDiscount.toFixed(2)}`,
            //         isDiscount: true,
            //     })
            // }
        }
        if (showBasePrice !== false) {
            priceDetails.push({
                title: "Base Price",
                value: billingInfo.unitPrice.toFixed(2),
                valueWithCurrency: `${AppUtil.getCurrencySymbol(billingDetails.billingInfo.currency)}${billingInfo.unitPrice.toFixed(2)}`
            })
        }

        if (showTaxes !== false) {
            priceDetails.push({
                title: billingInfo.tax.type + "@" + billingInfo.tax.percentage + "%",
                value: billingInfo.tax.amount.toFixed(2),
                valueWithCurrency: `${AppUtil.getCurrencySymbol(billingDetails.billingInfo.currency)}${billingInfo.tax.amount.toFixed(2)}`
            })
        }

        if (deliveryCharge && showDelivery !== false) {
            priceDetails.push({
                // title: mealSlot === "SNACKS" ? `Delivery charge\n(for orders under ${RUPEE_SYMBOL}50)`
                // : `Delivery charge\n(for orders under ${RUPEE_SYMBOL}200)`,
                title: `Delivery charge`,
                value: deliveryCharge.total.toFixed(2),
                valueWithCurrency: `${AppUtil.getCurrencySymbol(billingDetails.billingInfo.currency)}${deliveryCharge.total.toFixed(2)}`
            })
        } else if (showDelivery !== false && deliveryOffer) {
            if (AppUtil.isDeliveryStrikeOffSupported(userContext)) {
                totalDiscount += deliveryChargeForBrand
                priceDetails.push({
                    title: `Delivery charge`,
                    value: deliveryChargeForBrand + "",
                    valueWithCurrency: `${AppUtil.getCurrencySymbol(billingDetails.billingInfo.currency)}${deliveryChargeForBrand + ""}`,
                    offerText: "FREE",
                    isValueStrikeOff: true,
                    isDiscount: true
                })
            } else {
                priceDetails.push({
                    title: `Delivery charge`,
                    value: "0",
                    valueWithCurrency: `${AppUtil.getCurrencySymbol(billingDetails.billingInfo.currency)} ${"0"}`,
                })
            }
        }

        if (packagingCharge && showPackaging !== false && packagingCharge.total !== 0) {
            // this.logger.info("packaging shows up here")
            priceDetails.push({
                title: `Packaging charge`,
                value: packagingCharge.total.toFixed(2),
                valueWithCurrency: `${AppUtil.getCurrencySymbol(billingDetails.billingInfo.currency)}${packagingCharge.total.toFixed(2)}`
            })
        } else if (showPackaging !== false && packagingOffer) {
            if (AppUtil.isDeliveryStrikeOffSupported(userContext)) {
                totalDiscount += packagingChargeForBrand
                priceDetails.push({
                    title: `Packaging charge`,
                    value: packagingChargeForBrand + "",
                    valueWithCurrency: `${AppUtil.getCurrencySymbol(billingDetails.billingInfo.currency)}${packagingChargeForBrand + ""}`,
                    offerText: "FREE",
                    isValueStrikeOff: true,
                    isDiscount: true,
                })
            } else {
                priceDetails.push({
                    title: `Packaging charge`,
                    value: "0",
                    valueWithCurrency: `${AppUtil.getCurrencySymbol(billingDetails.billingInfo.currency)} ${"0"}`
                })
            }
        }
        if (penaltyAmount > 0) {
            priceDetails.push({
                title: "Penalty Amount",
                value: penaltyAmount.toFixed(2),
                valueWithCurrency: `${AppUtil.getCurrencySymbol(billingDetails.billingInfo.currency)} ${penaltyAmount.toFixed(2)}`
            })
        }
        if (adjustedAmount && adjustedAmount > 0) {
            priceDetails.push({
                title: "Adjusted Amount",
                value: adjustedAmount.toFixed(2),
                valueWithCurrency: `${AppUtil.getCurrencySymbol(billingDetails.billingInfo.currency)}${adjustedAmount.toFixed(2)}`
            })
        }
        let total = deliveryCharge ? billingInfo.total + deliveryCharge.total : billingInfo.total
        total = packagingCharge ? total + packagingCharge.total : total

        if (billingDetails.fitcashBalance > 0) {
            if (isFitcashEnabled) {
                priceDetails.push({
                    title: "Total",
                    value: total.toFixed(2),
                    valueWithCurrency: `${AppUtil.getCurrencySymbol(billingDetails.billingInfo.currency)} ${total.toFixed(2)}`,
                    isTotalWithoutFitcash: true
                })
            }

            priceDetails.push({
                title: `Use Fitcash`,
                fitcashBalance: billingDetails.fitcashBalance,
                value: `${billingDetails.fitcashBalance > total ? total : billingDetails.fitcashBalance.toFixed(0)}`,
                valueWithCurrency: `${AppUtil.getCurrencySymbol(billingDetails.billingInfo.currency)} ${(billingDetails.fitcashBalance > total ? total : billingDetails.fitcashBalance).toFixed(0)}`,
                isEnabled: isFitcashEnabled,
                isFitcashPrice: true
            })

            if (isFitcashEnabled) {
                total = (billingDetails.fitcashBalance > total ? 0 : total - billingDetails.fitcashBalance)
                totalDiscount += (billingDetails.fitcashBalance > total ? total : billingDetails.fitcashBalance)
            }
        }

        priceDetails.push({
            title: "Total Payable",
            value: total.toFixed(2),
            valueWithCurrency: `${AppUtil.getCurrencySymbol(billingDetails.billingInfo.currency)}${total.toFixed(2)}`

        })
        return { components: priceDetails, cartValue: total, discountValue: totalDiscount }
    }

    public async buildAddressWidget(userContext: UserContext, weekDayAddress: UserDeliveryAddress, weekendAddress: UserDeliveryAddress, weekendEnabled: boolean, isPack: boolean, mealSlot: MenuType, action?: Action, wigetType?: WidgetType): Promise<CheckoutAddressWidget> {
        const checkoutAddressWidget = new CheckoutAddressWidget(userContext, weekDayAddress, weekendAddress, weekendEnabled, isPack, mealSlot, undefined, wigetType)
        if (action) {
            checkoutAddressWidget.action = action
            checkoutAddressWidget.actions = [action]
        }
        return checkoutAddressWidget
    }
    public async buildStartDateWidget(date: string, validPackStartDates: string[], userContext: UserContext, timezone: Timezone): Promise<StartDateWidget> {
        return new StartDateWidget(date, validPackStartDates, userContext, timezone)
    }

    public async buildCafeDeliverySlotWidget(title: string, isDisabled: boolean, action: DeliverySlotAction | DateWiseDeliverySlotAction, subtitle?: string, deliverySlot?: string, deliveryDate?: string, deliveryWindowTitle?: string) {
        const widget = new CafeDeliverySlotWidget(title, isDisabled, action, subtitle, deliverySlot, deliveryDate)
        widget.deliveryWindowTitle = deliveryWindowTitle
        return widget
    }

    public buildDeliverySlotWidgetV3(title: string, isDisabled: boolean, action: DeliverySlotAction | DateWiseDeliverySlotAction, subtitle?: string, deliverySlot?: string, deliveryDate?: string) {
        return new DeliverySlotWidgetV3(title, isDisabled, action, subtitle, deliverySlot, deliveryDate)
    }

    public buildCartListWidgetPayload(orderItemsInfo: OrderItemInfo[], images: string[], actions: Action[]): OrderItem[] {
        const orderItems: OrderItem[] = []
        for (let index = 0; index < orderItemsInfo.length; index = index + 1) {
            const orderItemInfo = orderItemsInfo[index]
            const stock = (orderItemInfo.price.listingPrice === 0 && orderItemInfo.stock > 0 ? 1 : (orderItemInfo.maxCartQty ? Math.min(orderItemInfo.stock, orderItemInfo.maxCartQty) : orderItemInfo.stock))
            const orderItem: OrderItem = {
                isVeg: orderItemInfo.isVeg,
                offerId: orderItemInfo.offerId,
                productId: orderItemInfo.productId,
                action: actions[index],
                image: images && images[index] ? images[index] : undefined,
                quantity: orderItemInfo.quantity,
                price: orderItemInfo.price,
                productName: orderItemInfo.productName,
                productNameWithoutUnits: orderItemInfo.productNameWithoutUnits,
                productType: orderItemInfo.productType,
                subTitle: orderItemInfo.option.subscriptionType !== undefined ? (capitalizeFirstLetter(orderItemInfo.option.subscriptionType) + " plan") : FoodMarketplaceUtil.getVariantTitle(orderItemInfo?.option),
                additionalSubtitle: FoodMarketplaceUtil.getAddonTitle(orderItemInfo?.option),
                option: orderItemInfo.option,
                stock: stock,
                parentProductId: orderItemInfo.parentProductId,
                comboOfferId: orderItemInfo.comboOfferId,
                servingUnit: orderItemInfo.servingUnit,
                servingQty: orderItemInfo.servingQty,
                displayUnitQty: orderItemInfo.displayUnitQty,
                variantTitle: orderItemInfo.variantTitle
            }
            orderItems.push(orderItem)
        }
        return orderItems
    }

    public async buildCartListWidget(userContext: UserContext, order: Order, billingDetails: BillingDetails, orderItems: OrderItem[] | MarketplaceOrderItemInfo[]): Promise<CartListWidget> {
        const listingBrand: ListingBrandIdType = EatUtil.getListingBrandFromOrder(order)
        const priceDetails = billingDetails ? await this.getPriceDetails(userContext, billingDetails, listingBrand, order.deliveryCharges, order.packagingCharges, true, true, false, undefined, undefined, false) : undefined
        let numItems = 0
        for (const orderItem of orderItems)
            numItems = numItems + orderItem.quantity
        const widget: CartListWidget = {
            widgetType: "CART_LIST_WIDGET",
            orderItems: orderItems,
            priceDetails: userContext.sessionInfo.userAgent !== "APP" && priceDetails.components, // figure out for foodmp
            cartValue: userContext.sessionInfo.userAgent !== "APP" && priceDetails.cartValue,  // figure out for foodmp
            title: `${numItems} ITEMS`,
        }
        return widget
    }


    public async buildCartListWidgetV2(userContext: UserContext, order: Order, billingDetails: BillingDetails, orderItems: OrderItem[], title?: string, c1CategoryId?: string): Promise<CartListWidget> {
        const listingBrand: ListingBrandIdType = EatUtil.getListingBrandFromOrder(order)
        const priceDetails = await this.getPriceDetails(userContext, billingDetails, listingBrand, order.deliveryCharges, order.packagingCharges, true, true, false, undefined, undefined, false)
        let numItems = 0
        for (const orderItem of orderItems)
            numItems = numItems + orderItem.quantity
        const widget: CartListWidget = {
            widgetType: "CART_LIST_WIDGET",
            orderItems: orderItems,
            priceDetails: userContext.sessionInfo.userAgent !== "APP" && priceDetails.components,
            cartValue: userContext.sessionInfo.userAgent !== "APP" && priceDetails.cartValue,
            title: title ? title : `${numItems} ITEMS`,
            c1CategoryId: c1CategoryId ? c1CategoryId : "OTHERS"
        }
        return widget
    }

    public async buildBillingWidget(userContext: UserContext, order: Order, billingDetails: BillingDetails, isFitcashEnabled?: boolean, shouldShowDeliveryTipWidget?: boolean): Promise<BillingWidget> {
        const listingBrand: ListingBrandIdType = EatUtil.getListingBrandFromOrder(order)
        const isPack: boolean = !_.isEmpty(order.productSnapshots) && order.productSnapshots[0].isPack
        const numTickets: number = isPack && !_.isEmpty(order.productSnapshots) ? order.productSnapshots[0].option.numTickets : undefined
        const priceDetails = await this.getPriceDetails(userContext, billingDetails, listingBrand, order.deliveryCharges, order.packagingCharges, true, true, false, undefined, undefined, false, isFitcashEnabled, order.orderDiscountSplit, order.offersInfo, numTickets)
        const widget: BillingWidget = {
            widgetType: "BILLING_WIDGET",
            title: `Billing Details`,
            priceDetails: priceDetails.components,
            cartValue: priceDetails.cartValue,
            fitClubSavings: undefined,
            fitcashBalance: billingDetails.fitcashBalance,
            isFitcashEnabled: isFitcashEnabled,
            currency: _.get(billingDetails, "billingInfo.currency"),
            deliveryTipTitle: shouldShowDeliveryTipWidget ? "Tip" : undefined,
        }
        if (priceDetails.discountValue) {
            widget.totalSaving = {
                iconUrl: CdnUtil.getCdnUrl("curefit-content/image/payment/discount_green.png"),
                    data: [
                    { text: "Total Saving of " },
                    {
                        text: `${AppUtil.getCurrencySymbol(billingDetails.billingInfo.currency)}${+priceDetails.discountValue.toFixed(2)}`,
                        style: {
                            fontFamily: "BrandonText-Bold",
                        },
                    },
                ]
            }
        }
        return widget
    }

    public async buildCartAddonWidget(addon: CartAddon, order: Order, menuAvailabilityResult: MenuAvailabilityResult, mealSlot: MenuType, listingBrand: ListingBrandIdType, userContext: UserContext): Promise<AddonListWidget> {
        const addonItems: AddonItem[] = []
        const productOffers = addon.productOffers
        for (let i = 0; i < productOffers.length; i++) {
            const product = productOffers[i].product
            const offerAndPrice = productOffers[i].offerAndPrice
            const availability = productOffers[i].availability
            if (availability.left > 0) {
                let offerIds
                if (!_.isEmpty(offerAndPrice.offers)) {
                    offerIds = _.map(offerAndPrice.offers, offer => {
                        return offer.offerId
                    })
                }
                const image = UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, product.productType, "THUMBNAIL", product.imageVersion)
                const seoParams = {
                    productName: product.title
                }
                const actions: (Action | MealAction)[] = [{
                    actionType: "WIDGET_NAVIGATION",
                    url: ActionUtil.foodSingle(product.productId, menuAvailabilityResult.day, mealSlot, true, undefined, userContext.sessionInfo.userAgent, seoParams)
                }, {
                    actionType: "ADD_TO_CART",
                    title: "ADD",
                    image: image,
                    date: menuAvailabilityResult.day,
                    productId: product.productId,
                    price: offerAndPrice.price,
                        offerId: !_.isEmpty(offerIds) ? offerIds[0] : undefined,
                        offerIds: offerIds,
                        maxCartSize: MAX_CART_SIZE,
                        productName: product.title,
                        stock: offerAndPrice.price.listingPrice === 0 && availability.left > 0 ? 1 : Math.min(MAX_CART_SIZE, availability.left),
                        mealSlot: {
                            id: mealSlot,
                            name: capitalizeFirstLetter(mealSlot.toLowerCase())
                        },
                        listingBrand: listingBrand
                    }]
                product.price = offerAndPrice.price
                const addonItem: AddonItem = {
                    image: image,
                    title: product.title,
                    productId: product.productId,
                    price: offerAndPrice.price,
                    stock: offerAndPrice.price.listingPrice === 0 && availability.left > 0 ? 1 : Math.min(MAX_CART_SIZE, availability.left),
                    calories: `${product.attributes.nutritionInfo.Calories["Total Calories"]} cal`,
                    actions: actions,
                    isVeg: product.attributes["isVeg"] === "TRUE" ? true : false,
                    isInventorySet: availability.total > 0 ? true : false,
                    recommended: productOffers[i].recommended
                }
                addonItems.push(addonItem)
            }
        }
        if (_.isEmpty(addonItems))
            return undefined
        else {
            const addonListWidget: AddonListWidget = {
                title: "People also add",
                subTitle: undefined,
                addonItems: addonItems,
                widgetType: "ADDON_LIST_WIDGET"
            }
            return addonListWidget
        }
    }

    public async buildFitClubSavingsWidget(userContext: UserContext, fitcashBalance: number, fitCashEarned: number, hasDeliveryOffer: boolean): Promise<FitClubCheckoutSavingsWidget> {
        let fitClubSavingsText: string = undefined
        if (fitCashEarned) {
            fitClubSavingsText = "Earn upto " + fitCashEarned + " fitcash"
        }
        if (hasDeliveryOffer) {
            if (fitClubSavingsText) {
                fitClubSavingsText += " and free delivery"
            } else {
                fitClubSavingsText = "Free delivery"
            }
        }
        const amount = fitcashBalance ? fitcashBalance : 0
        return {
            widgetType: "FITCLUB_CHECKOUT_SAVINGS_WIDGET",
            title: fitClubSavingsText,
            fitcashBalanceText: amount ? "Fitcash Balance: " + amount : undefined
        }
    }

    public async buildCutleryWidget(mealSlot: MenuType, isCutleryEnabled: boolean, subtitle: string, userContext?: UserContext): Promise<SwitchWidget> {
        const cutleryWidget: SwitchWidget = {
            widgetType: "SWITCH_WIDGET",
            title: isCutleryEnabled ? "Bring cutlery" : "Don't bring cutlery",
            icon: "CUTLERY",
            action: { actionType: "SET_CUTLERY_INSTRUCTION", meta: { cutleryInstruction: isCutleryEnabled ? "NO_CUTLERY" : "GIVE_CUTLERY", mealSlot: mealSlot } },
            isEnabled: isCutleryEnabled,
            subTitle: subtitle
        }
        return cutleryWidget
    }
    public async buildCutleryWidgetV2(mealSlot: MenuType, isCutleryEnabled: boolean, subtitle: string, userContext: UserContext, cutleryInstructionFromClient: CutleryInstruction = undefined): Promise<SwitchWidgetV3> {
        const cutleryInstructionMeta = cutleryInstructionFromClient ? {
            cutleryInstruction: cutleryInstructionFromClient,
            // opposite of whatever client passed
            noCutleryInstruction: cutleryInstructionFromClient === "GIVE_CUTLERY" ? "NO_CUTLERY" : "GIVE_CUTLERY"
        } : {
            cutleryInstruction: "GIVE_CUTLERY",
            noCutleryInstruction: "NO_CUTLERY"
        }
        const actionMeta = AppUtil.isCutlerySelectClientSideHandleSupported(userContext) ?
            {
                mealSlot: mealSlot,
                ...cutleryInstructionMeta
            } :
            {
                cutleryInstruction: isCutleryEnabled ? "NO_CUTLERY" : "GIVE_CUTLERY",
                mealSlot: mealSlot
            }
        const cutleryWidget: SwitchWidgetV3 = {
            widgetType: "SWITCH_WIDGET_V3",
            title: "I don't want cutlery",
            action: { actionType: "SET_CUTLERY_INSTRUCTION", meta: actionMeta },
            isEnabled: AppUtil.isCutlerySelectClientSideHandleSupported(userContext) ? undefined : isCutleryEnabled,
            subTitle: subtitle
        }
        return cutleryWidget
    }

    async buildPageAction(pageActionRequest: PageActionRequest) {
        const userContext = pageActionRequest.userContext
        const order = pageActionRequest.order
        const isFitcashEnabled = pageActionRequest.isFitcashEnabled
        const numProducts = pageActionRequest.numProducts
        const mealSlotDeliverySlots = pageActionRequest.mealSlotDeliverySlots
        const deliveryInfo = pageActionRequest.deliveryInfo
        const isDeliverySlotEmpty = pageActionRequest.isDeliverySlotEmpty
        const allSlots = pageActionRequest.allSlots
        const arrivalInfo = pageActionRequest.arrivalInfo
        const isCafe = pageActionRequest.isCafe
        const onDemandText = pageActionRequest.onDemandText
        const fitClubAddon = pageActionRequest.fitClubAddon
        const billingDetails = pageActionRequest.billingDetails
        const currency = pageActionRequest.currency
        const mealSlot = pageActionRequest.mealSlot
        let action: Action | DeliverySlotAction | CafeDeliverySlotAction
        if (!userContext.sessionInfo.isUserLoggedIn) {
            action = {
                actionType: "SHOW_ALERT_MODAL",
                title: "Get pack", meta: {
                    title: "Login Required!",
                    subTitle: "Please login to continue",
                    actions: [{ actionType: "LOGOUT", title: "Login" }]
                }
            }
            return action

        }
        const tz = userContext.userProfile.timezone
        const isPack = order.productSnapshots[0].isPack
        const isWeekendEnabled = order.productSnapshots[0].option.weekendEnabled
        if (numProducts === 0) {
            // Handling case where there 0 items due to checkout
            action = {
                actionType: "POP_ACTION",
                url: "curefit://eatfitclp",
                title: "Ok"
            }
        }
        const mealSlots = _.keys(mealSlotDeliverySlots)
        mealSlots.sort((slotA, slotB) => {
            return ALL_MEAL_SLOTS.indexOf(<MealSlot>slotA) - ALL_MEAL_SLOTS.indexOf(<MealSlot>slotB)
        })
        const supportsDeliveryWindow = await AppUtil.isEatDeliveryWindowSupported(userContext)
        const everyReturn = mealSlots.every((mealSlot) => {
            if (!_.isNil(action)) {
                return false
            }
            const availableSlots = mealSlotDeliverySlots[mealSlot]
            const deliveryInfoIndex = deliveryInfo.findIndex((info) => {
                return info.mealSlot === mealSlot
            })
            const info = deliveryInfo[deliveryInfoIndex]
            const checkForDeliveryWindowAction = supportsDeliveryWindow && (!_.isNil(pageActionRequest.deliveryWindows) && !_.isEmpty(pageActionRequest.deliveryWindows) && !info.deliveryWindow)

            if (_.isNil(info) || info.isWeekdayAddressServiceable === false || _.isNil(info.addressId)) {
                if (pageActionRequest.isCheckoutV2Supported && !isPack) {
                    action = {
                        actionType: "SHOW_CART_ADDRESS_MODAL",
                        title: "Select Address",
                        meta: {
                            mealSlot: mealSlot,
                            navigateToSlotPicker: false
                        }
                    }
                }
                else  {
                    action = {
                        actionType: "CHANGE_ADDRESS",
                        url: "curefit://selectaddress?pageFrom=cart_checkout",
                        meta: {
                            isWeekendAddress: false,
                            mealSlot: mealSlot
                        },
                        title: "Pick " + (isPack ? "weekday address" : "address")
                    }
                }

            } else if (isWeekendEnabled && (info.isWeekendAddressServiceable === false || _.isNil(info.weekendAddressId))) {
                action = {
                    actionType: "CHANGE_WEEKEND_ADDRESS",
                    meta: {
                        isWeekendAddress: true,
                        mealSlot: mealSlot
                    },
                    url: "curefit://selectaddress?pageFrom=cart_checkout",
                    title: "Pick " + "weekend address"
                }
            } else if (checkForDeliveryWindowAction && !info.deliveryWindow) {
                action = {
                    title: "Select delivery slot",
                    actionType: "CHANGE_SLOT",
                    mealSlot: mealSlot as MenuType,
                    deliveryWindows: pageActionRequest.deliveryWindows
                }
            } else if ((info.deliverySlot === undefined && !info.deliveryWindow) || (isDeliverySlotEmpty && onDemandText === "")) {
                const slots = _.map(allSlots, (slot) => {
                    const slotDetail: {
                        deliverySlot: string
                        title: string,
                        isEnabled: boolean
                    } = {
                        deliverySlot: slot.slotId,
                        title: MealUtil.getDisplayTextForSlot(slot, undefined, arrivalInfo, onDemandText, isCafe),
                        isEnabled: _.findIndex(availableSlots, (availableSlot) => {
                            return availableSlot === slot.slotId
                        }) !== -1
                    }
                    return slotDetail
                })
                if (!_.isNil(onDemandText) && onDemandText.length > 0) {
                    slots.unshift({
                        deliverySlot: SlotUtil.ON_DEMAND_SLOT,
                        title: MealUtil.getSlotDisplayText(SlotUtil.ON_DEMAND_SLOT, undefined, arrivalInfo, onDemandText, isCafe),
                        isEnabled: true
                    })
                }
                action = {
                    title: "Select delivery slot",
                    actionType: "CHANGE_SLOT",
                    mealSlot: mealSlot as MenuType,
                    slots: slots,
                    deliveryWindows: pageActionRequest.deliveryWindows
                }
            }
            return true
        })
        if (_.isNil(action)) {
            let totalAmountPayable = order.totalAmountPayable
            let freeTitle = "Get for free"
            if (isFitcashEnabled && billingDetails.fitcashBalance > 0) {
                if (billingDetails.fitcashBalance > totalAmountPayable) {
                    freeTitle = "Pay with Fitcash"
                    totalAmountPayable = 0
                } else {
                    totalAmountPayable = totalAmountPayable - billingDetails.fitcashBalance
                }
            }
            if (totalAmountPayable === 0) {
                action = {
                    title: freeTitle,
                    actionType: "PAY_FREE"
                }
            } else {

                action = {
                    title: `Pay ${AppUtil.getCurrencySymbol(currency)} ${totalAmountPayable}`,
                    actionType: "CART_PAY_NOW",
                    url: "curefit://payment"
                }

                if (!_.isNil(fitClubAddon)) {
                    let fitclubAlertAction: Action

                    const fitClubFitCash: number = billingDetails.fitClubFitCash
                    const deliveryCharge: number = !_.isNil(order.deliveryCharges) ? order.deliveryCharges.total : 0
                    const fitClubDeliverySavings = deliveryCharge > 0 ? deliveryCharge : DEFAULT_DELIVERY_CHARGE
                    const defaultFitClubProduct = fitClubAddon.defaultFitClubProduct
                    if (fitClubAddon.showFitclubNotAddedAlert && !_.isNil(fitClubFitCash) && !_.isNil(fitClubDeliverySavings)) {
                        const cartAction = {...action}
                        cartAction.title = "Remove"
                        fitclubAlertAction = {
                            title: action.title,
                            actionType: "SHOW_ALERT_MODAL",
                            meta: {
                                title: "Add FitClub to your order!",
                                subTitle: `You will get ${AppUtil.getCurrencySymbol(currency)} ${fitClubFitCash + fitClubDeliverySavings} benefit.\nGet ${fitClubFitCash} Fitcash and FREE delivery worth ${AppUtil.getCurrencySymbol(currency)} ${fitClubDeliverySavings} on this order`,
                                actions: [{
                                    actionType: "ADD_FITCLUB_CART",
                                    title: "Add",
                                    meta: {...defaultFitClubProduct, mealSlot: {id: mealSlot}, skipCartSlotDate: true }},
                                    cartAction],
                            }
                        }
                        action = fitclubAlertAction
                    }
                }
            }
        }
        return action
    }

    checkComboCreation(cartReviewPayload: CartReviewPayload, cartReviewResult: CartReviewResponse): { title: string, subTitle: string }[] {
        const updateLog: { title: string, subTitle: string }[] = []
        const requestComboQtyMap: { [id: string]: number } = {}
        const responseComboQtyMap: { [id: string]: number } = {}
        cartReviewPayload.orderProducts.forEach((orderProduct) => {
            if (orderProduct.option.comboOfferId) {
                requestComboQtyMap[orderProduct.productId] = orderProduct.quantity
            }
        })
        cartReviewResult.order.productSnapshots.forEach((productSnapshot: OrderProductSnapshots) => {
            if (productSnapshot.comboOfferId) {
                responseComboQtyMap[productSnapshot.productId] = productSnapshot.quantity
            }
        })
        const requestHasMoreKeys = _.keys(requestComboQtyMap).length >= _.keys(responseComboQtyMap).length
        _.mapValues(requestHasMoreKeys ? requestComboQtyMap : responseComboQtyMap, (qty, pid) => {
            const lookUpMap = requestHasMoreKeys ? responseComboQtyMap : requestComboQtyMap
            const responseProduct = _.find(cartReviewResult.order.productSnapshots, (productSnapshot: OrderProductSnapshots) => { return productSnapshot.productId === pid && !_.isNil(productSnapshot.comboOfferId) })
            if (_.isNil(lookUpMap[pid]) || lookUpMap[pid] < qty) {
                if (requestHasMoreKeys) {
                    updateLog.push({
                        title: "Combo removed",
                        subTitle: "The combo offer was removed"
                    })
                } else {
                    updateLog.push({
                        title: "Combo created",
                        subTitle: "A combo offer has been applied on " + responseProduct.title
                    })
                }
            } else if (lookUpMap[pid] > qty) {
                if (requestHasMoreKeys) {
                    updateLog.push({
                        title: "Combo created",
                        subTitle: "A combo offer has been applied on " + responseProduct.title
                    })
                } else {
                    updateLog.push({
                        title: "Combo removed",
                        subTitle: "The combo offer was removed"
                    })
                }
            }
        })
        return updateLog
    }

     getAlertInfo(alertStatus: AlertInfoStatus | AlertStatus, deliveryInfo: ClientDeliveryInfo[], availabilities: ProductAvailability[], products: Product[], numProducts: number, user: User, userContext: UserContext, isOfferOverriden: boolean, updateLog: { title: string, subTitle: string }[], orderSource?: OrderSource, listingBrand?: ListingBrandIdType): AlertInfo {
        const alertInfoParams: AlertInfoParams = {
            updateLog: undefined,
            unAvailableProducts: undefined,
            nonServiceableInfo: undefined,
            products: undefined,
            orderSource: orderSource,
            listingBrand: listingBrand
        }
        let mealSlot: MenuType
        if (!_.isEmpty(deliveryInfo)) {
            mealSlot = deliveryInfo[0].mealSlot
        }
        const unAvailableProductAvailabilities: ProductAvailability[] = availabilities.filter(availability => {
            if (availability.status !== "AVAILABLE") {
                numProducts = numProducts - 1
                return true
            }
        })
        let nonServiceableAddressIndex = -1
         if (!_.isEmpty(deliveryInfo)) {
             nonServiceableAddressIndex = deliveryInfo.findIndex((info) => {
                 return info.isWeekdayAddressServiceable === false || info.isWeekendAddressServiceable === false
             })
         }
        if (nonServiceableAddressIndex >= 0) {
            alertInfoParams.nonServiceableInfo = deliveryInfo[nonServiceableAddressIndex]
            // return AlertInfos.AlertInfo("NON_SERVICEABLE_ALERT", alertInfoParams, userContext)
        }

        const unAvailableProducts = _.map(unAvailableProductAvailabilities, unAvailableProduct => {
            const productInfo = products.find(product => {
                return product.productId === unAvailableProduct.productId
            })
            return {
                title: productInfo.title,
                productId: productInfo.productId,
                parentProductId: productInfo.parentProductId,
                mealSlot: { id: mealSlot }
            }
        })
        alertInfoParams.unAvailableProducts = unAvailableProducts
        alertInfoParams.products = products
        alertInfoParams.updateLog = updateLog
        if (alertStatus)
            return AlertInfos.AlertInfo(alertStatus, alertInfoParams, userContext)

        if (alertInfoParams.nonServiceableInfo)
            return AlertInfos.AlertInfo("NON_SERVICEABLE_ALERT", alertInfoParams, userContext)

        if (updateLog && updateLog.length > 0) {
            return AlertInfos.AlertInfo("CART_UPDATED", alertInfoParams, userContext)
        }

        if (alertInfoParams.unAvailableProducts.length > 0) {
            const alertInfoParams: AlertInfoParams = {
                unAvailableProducts: unAvailableProducts,
                products: products,
                orderSource: orderSource,
                listingBrand: listingBrand
            }
            if (userContext.sessionInfo.isUserLoggedIn) {
                const result = AppUtil.getUserAlertInfo(user, userContext)
                if (result && result.alertInfo)
                    return result.alertInfo
            }
            if (isOfferOverriden) {
                return AlertInfos.AlertInfo("OFFER_OVERIDDEN_ALERT")
            }
            if (numProducts === 0) {
                return AlertInfos.AlertInfo("ITEMS_UNAVAILABLE", alertInfoParams, userContext)
            }

            return AlertInfos.AlertInfo("SOME_ITEMS_UNAVAILABLE", alertInfoParams, userContext)
        }

        return undefined

    }

    public getCartAddon(addons: Addon[]): CartAddon {
        const cartAddon =  _.find(addons, addon => {
            if (addon.addonType === "CART_ADDON")
                return true
        })
        if (!_.isNil(cartAddon))
            return cartAddon.addon
        return undefined
    }

    public getFitClubAddon(addons: Addon[]): FitClubAddon {
        const fitClubAddon =  _.find(addons, addon => {
            if (addon.addonType === "FIT_CLUB")
                return true
        })
        if (!_.isNil(fitClubAddon))
            return fitClubAddon.addon
        return undefined
    }

    public checkForDeliverySlotTitle(selectedSlot: string, onDemandText: string, isDeliverySlotEmpty: boolean): boolean {
        return ((isDeliverySlotEmpty && onDemandText === "") || _.isNil(selectedSlot))
    }

    protected async addFitClubSavingsWidgets(fitcashService: IFitcashService, offerService: IOfferServiceV2, userContext: UserContext, order: Order, billingDetails: BillingDetails, isFitcashEnabled: boolean, widgetViewPromises: Promise<WidgetView>[]) {
        const fitcashBalance = (await fitcashService.balance(userContext.userProfile.userId, userContext.userProfile.city.country.currencyCode)).balance / 100
        const cartOffersResult: {[key: string]: OfferV2} = !_.isEmpty(order.offersApplied) ? await offerService.getOffers(order.offersApplied) : {}
        const fitCashOffer = _.maxBy(_.filter(cartOffersResult, (offer: OfferV2) => { return offer.addons[0] && offer.addons[0].addonType === "FITCASH" }), "priority")
        const orderTotal = billingDetails.orderTotal
        const minAmount = (fitCashOffer && fitCashOffer.constraints && fitCashOffer.constraints.minimumAmount) || 0
        let eligibleAmount = orderTotal >= minAmount ? orderTotal : 0
        if (eligibleAmount && fitcashBalance && isFitcashEnabled) {
            eligibleAmount = Math.max(eligibleAmount - fitcashBalance, 0)
        }
        const fitClubFitCash = FitClubUtil.getFitCash(fitCashOffer, eligibleAmount, true)

        const hasDeliveryOffer = _.isNil(order.deliveryCharges) || order.deliveryCharges.total == 0
        if (fitClubFitCash > 0 || hasDeliveryOffer || fitcashBalance > 0) {
            const fitClubSavingsWidgetPromise = this.buildFitClubSavingsWidget(userContext, fitcashBalance, fitClubFitCash, hasDeliveryOffer)
            widgetViewPromises.push(fitClubSavingsWidgetPromise)
        }
    }

    public async getHCMTippingWidget(): Promise<BaseWidget> {
        const widget = new HCMTippingWidget()
        widget.tip = 20 // default tip value
        widget.title = "Add a tip & show some love"
        widget.subtitle = "We will pass it on to our kitchen and delivery staff"
        widget.tipValues = [
            {
                value: 5
            },
            {
                value: 10
            },
            {
                value: 15
            },
            {
                value: 20
            }
        ]
        return widget.buildView(undefined, undefined, {})
    }

    abstract build(interfaces: IServiceInterfaces, request: ReviewRequest, userContext: UserContext): Promise<CartReviewView>
}
