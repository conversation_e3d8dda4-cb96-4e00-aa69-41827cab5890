import * as express from "express"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import { Container, inject } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import {
    ALFRED_CLIENT_TYPES,
    IFulfilmentService,
    IPackService,
    IShipmentService,
    OrderCreateInternal,
    SubOrderCreateV2
} from "@curefit/alfred-client"
import {
    CartReviewResponse,
    IOrderService as IOmsApiClient,
    OMS_API_CLIENT_TYPES,
    OrderCartReviewResponse,
    OrderCheckoutRequest,
    OrderCheckoutResponse,
    OrderCreate
} from "@curefit/oms-api-client"
import IUserBusiness, { UserCity } from "../user/IUserBusiness"
import IProductBusiness from "../product/IProductBusiness"
import {
    CafeSlot,
    DeliveryInfo,
    DeliveryInstruction,
    DeliverySlot,
    FoodPack,
    FoodProduct as Product,
    ListingBrandIdType,
    MenuType,
    UserDeliveryAddress
} from "@curefit/eat-common"
import { PreferredLocation, Session, UserContext } from "@curefit/userinfo-common"
import { Tenant, User } from "@curefit/user-common"
import AuthMiddleware from "../auth/AuthMiddleware"
import {
    BaseOrder,
    CartReviewPayload,
    CultMembershipMetadata,
    CultMindMembershipUpgradeMetadata,
    isMembershipUpgradeClientMetadata,
    Order,
    OrderProduct,
    OrderProductOption,
    OrderProductSnapshots,
    OrderSource
} from "@curefit/order-common"
import { SlotUtil } from "@curefit/eat-util"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { BASE_TYPES, ErrorBase, Logger } from "@curefit/base"
import { MAX_CART_SIZE, MealUtil, OfferUtil, WHOLE_FIT_V2_MAX_CART_SIZE } from "@curefit/base-utils"
import { AlertInfo, ProductDetailPage } from "../common/views/WidgetView"
import { CATALOG_CLIENT_TYPES, CultPackFilter, ICatalogueService, ICatalogueServicePMS, ProPackFilters } from "@curefit/catalog-client"
import * as _ from "lodash"
import { ADDRESS_NOT_FOUND_ERROR_CODE, ErrorFactory, HTTP_CODE } from "@curefit/error-client"
import { TaxType } from "@curefit/finance-common"
import CartViewBuilder, { CartReviewView } from "./CartViewBuilder"
import { ICapacityService, KiosksDemandService, MASTERCHEF_CLIENT_TYPES } from "@curefit/masterchef-client"
import OrderViewBuilder from "../order/OrderViewBuilder"
import { FoodBooking, FoodPackBooking } from "@curefit/shipment-common"
import AppUtil from "../util/AppUtil"
import CapacityServiceWrapper from "../product/CapacityServiceWrapper"
import BaseOrderConfirmationViewBuilder, {
    ConfirmationRequestParams,
    ConfirmationView
} from "../../app/order/BaseOrderConfirmationViewBuilder"
import OrderUtil from "../util/OrderUtil"
import { EatOfferRequestParamsV3, FoodSinglePriceOfferResponse } from "@curefit/offer-common"
import { FEEDBACK_MONGO_TYPES, IFeedbackReadOnlyDao } from "@curefit/feedback-mongo"
import FeedbackPageConfigV2Cache from "../ugc/FeedbackPageConfigV2Cache"
import { CAESAR_CLIENT_TYPES, IMenuService } from "@curefit/caesar-client"
import {
    DELIVERY_CLIENT_TYPES,
    DeliverySlotService,
    IDeliveryAreaService,
    IDeliverySlotService,
    IKioskReadonlyDao
} from "@curefit/delivery-client"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import EatUtil from "../util/EatUtil"
import { IEatBrand } from "./IEatBrand"
import { EAT_API_CLIENT_TYPES, EatBrand, IEatApiService, ReviewRequest, UserDetailInfo } from "@curefit/eat-api-client"
import { OrderCheckoutDetail } from "@curefit/apps-common"
import { IUserService, USER_CLIENT_TYPES } from "@curefit/user-client"
import { TippingCharge } from "@curefit/product-common"
import { ConfigService, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import { CacheHelper } from "../util/CacheHelper"
import { ALBUS_CLIENT_TYPES, IHealthfaceService } from "@curefit/albus-client"
import { ErrorCodes } from "../error/ErrorCodes"
import { DiagnosticProduct, Doctor } from "@curefit/care-common"
import CareUtil from "../util/CareUtil"
import { CareCultDayTransferPageViewBuilder } from "../care/CareCultDayTransferProductPageBuilder"
import FoodMarketplaceUtil from "../util/FoodMarketplaceUtil"
import { FOODWAY_CLIENT_TYPES, IFoodwayApiService } from "@curefit/foodway-client"
import { ShipmentStatus } from "@curefit/gandalf-common"
import CFAPIJavaService from "../CFAPIJavaService"
import { PromiseCache } from "../util/VMUtil"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { IOllivanderCityService, OLLIVANDER_CLIENT_TYPES } from "@curefit/ollivander-node-client"
import { CENTER_SERVICE_CLIENT_TYPES, ICenterService } from "@curefit/center-service-client"
import { CenterResponse } from "@curefit/center-service-common"
import { GymfitAccessLevel, GymfitListingChannels, GymfitProductSubType, GymPtProduct } from "@curefit/gymfit-common"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import * as moment from "moment"
import SugarfitUtil from "../util/SugarfitUtil"
import { ICultBusiness } from "../cult/CultBusiness"
import { GYMFIT_CLIENT_TYPES, IGymfitService } from "@curefit/gymfit-client"
import { IIndusService, INDUS_CLIENT_TYPES, UserOfferResponseV2 } from "@curefit/indus-client"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import AuthUtil from "../util/AuthUtil"
import LocationUtil from "../util/LocationUtil"
import { PackStartDateAnalyticsEvent } from "../cfAnalytics/PackStartDateAnalyticsEvent"
import { AnalyticsEventName } from "../cfAnalytics/AnalyticsEventName"
import { ICFAnalytics } from "../cfAnalytics/CFAnalytics"
import {
    LUX_MEMBERSHIPS_PRIMARY_BENEFITS,
    GYM_PT_MEMBERSHIPS_PRIMARY_BENEFITS, LUX_MEMBERSHIPS_ALLOWOVERLAP_BENEFITS
} from "../util/GymfitUtil"
import FitnessUtil from "../util/FitnessUtil"
import {
    AccessLevel,
    AugmentedOfflineFitnessPack,
    BenefitEntry,
    Namespace,
    OfflineFitnessPack,
    ProductSubType,
    PurchaseFlow,
    Restriction,
    Visibility
} from "@curefit/pack-management-service-common"
import { IOfflineFitnessPackService, PACK_CLIENT_TYPES } from "@curefit/pack-management-service-client"
import { City } from "@curefit/location-common"
import { IUserAttributeClient, RASHI_CLIENT_TYPES } from "@curefit/rashi-client"
import CatalogueServiceUtilities from "../util/CatalogueServiceUtilities"
import { CultPackType } from "@curefit/cult-common"
import { ISegmentationCacheClient, SEGMENTATION_CLIENT_TYPES } from "@curefit/segmentation-service-client"
import { FeatureStateCache } from "../page/vm/services/FeatureStateCache"

export function controllerFactory(kernel: Container) {
    @controller("/cart", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class CartController {
        private BrandReviewPageMap = new Map<EatBrand | "FOOD_MARKETPLACE" | "CULT_BIKE", IEatBrand>()

        constructor(
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private interfaces: CFServiceInterfaces,
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
            @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
            @inject(CUREFIT_API_TYPES.UserBusiness) private userBusiness: IUserBusiness,
            @inject(CUREFIT_API_TYPES.ProductBusiness) private productBusiness: IProductBusiness,
            @inject(CUREFIT_API_TYPES.CartViewBuilder) private cartViewBuilder: CartViewBuilder,
            @inject(MASTERCHEF_CLIENT_TYPES.CapacityService) private capacityService: ICapacityService,
            @inject(CAESAR_CLIENT_TYPES.MenuService) private menuService: IMenuService,
            @inject(CUREFIT_API_TYPES.OrderViewBuilder) private orderViewBuilder: OrderViewBuilder,
            @inject(ALFRED_CLIENT_TYPES.ShipmentService) private shipmentService: IShipmentService,
            @inject(ALFRED_CLIENT_TYPES.FulfilmentService) private fulfilmentService: IFulfilmentService,
            @inject(CUREFIT_API_TYPES.CapacityServiceWrapper) private capacityServiceWrapper: CapacityServiceWrapper,
            @inject(ALFRED_CLIENT_TYPES.PackService) private packService: IPackService,
            @inject(FEEDBACK_MONGO_TYPES.FeedbackReadOnlyDao) private feedbackDao: IFeedbackReadOnlyDao,
            @inject(CUREFIT_API_TYPES.FeedbackPageConfigV2Cache) private feedbackPageConfigV2Cache: FeedbackPageConfigV2Cache,
            @inject(CUREFIT_API_TYPES.OrderConfirmationViewBuilder) private orderConfirmationViewBuilder: BaseOrderConfirmationViewBuilder,
            @inject(CUREFIT_API_TYPES.OrderConfirmationViewBuilderV1) private orderConfirmationViewBuilderV1: BaseOrderConfirmationViewBuilder,
            @inject(PAGE_CONFIG_TYPES.ConfigService) private configService: ConfigService,
            @inject(MASTERCHEF_CLIENT_TYPES.KiosksDemandService) private kiosksDemandService: KiosksDemandService,
            @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness,
            @inject(DELIVERY_CLIENT_TYPES.KioskReadonlyDao) private kioskDao: IKioskReadonlyDao,
            @inject(DELIVERY_CLIENT_TYPES.DeliverySlotService) private deliverySlotService: IDeliverySlotService,
            @inject(DELIVERY_CLIENT_TYPES.DeliveryAreaService) private deliveryAreaService: IDeliveryAreaService,
            @inject(CUREFIT_API_TYPES.Cafe) private cafe: IEatBrand,
            @inject(CUREFIT_API_TYPES.OnlineKiosk) private onlineKiosk: IEatBrand,
            @inject(CUREFIT_API_TYPES.WholeFit) private wholeFit: IEatBrand,
            @inject(CUREFIT_API_TYPES.Marketplace) private marketplace: IEatBrand,
            @inject(EAT_API_CLIENT_TYPES.IEatApiService) private eatApiClientService: IEatApiService,
            @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
            @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
            @inject(CUREFIT_API_TYPES.FoodMarketplace) private foodMarketplace: IEatBrand,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
            @inject(CUREFIT_API_TYPES.CareCultDayTransferPageViewBuilder) private careCultDayTransferPageViewBuilder: CareCultDayTransferPageViewBuilder,
            @inject(FOODWAY_CLIENT_TYPES.IFoodwayApiService) private foodwayService: IFoodwayApiService,
            @inject(CUREFIT_API_TYPES.CFAPIJavaService) public cfAPIJavaService: CFAPIJavaService,
            @inject(CUREFIT_API_TYPES.CultBike) private cultbike: IEatBrand,
            @inject(OLLIVANDER_CLIENT_TYPES.IOllivanderCityService) private ollivanderService: IOllivanderCityService,
            @inject(CENTER_SERVICE_CLIENT_TYPES.CenterService) private centerService: ICenterService,
            @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) public membershipService: IMembershipService,
            @inject(OMS_API_CLIENT_TYPES.OrderService) public omsApiClient: IOmsApiClient,
            @inject(CUREFIT_API_TYPES.CultBusiness) private cultBusiness: ICultBusiness,
            @inject(GYMFIT_CLIENT_TYPES.GymfitService) private gymfitService: IGymfitService,
            @inject(INDUS_CLIENT_TYPES.IndusService) private indusService: IIndusService,
            @inject(LOCATION_TYPES.CityService) private cityService: ICityService,
            @inject(CUREFIT_API_TYPES.CFAnalytics) private cfAnalytics: ICFAnalytics,
            @inject(PACK_CLIENT_TYPES.OfflineFitnessPackService) private offlineFitnessPackService: IOfflineFitnessPackService,
            @inject(RASHI_CLIENT_TYPES.UserAttributeClient) public userAttributeClient: IUserAttributeClient,
            @inject(CATALOG_CLIENT_TYPES.CatalogServiceReaderPMS) private catalogueServicePMS: ICatalogueServicePMS,
            @inject(SEGMENTATION_CLIENT_TYPES.SegmentationCacheClient) private segmentationCacheClient: ISegmentationCacheClient,
            @inject(CUREFIT_API_TYPES.FeatureStateCache) public featureStateCache: FeatureStateCache,
        ) {
            this.BrandReviewPageMap.set("KIOSK", this.onlineKiosk)
            this.BrandReviewPageMap.set("ONLINE", this.onlineKiosk)
            this.BrandReviewPageMap.set("CAFE", this.cafe)
            this.BrandReviewPageMap.set("WHOLE_FIT", this.wholeFit)
            this.BrandReviewPageMap.set("EAT_3P", this.marketplace)
            this.BrandReviewPageMap.set("FOOD_MARKETPLACE", this.foodMarketplace)
            this.BrandReviewPageMap.set("CULT_BIKE", this.cultbike)

        }

        @httpPost("/review")
        async cartReview(req: express.Request): Promise<CartReviewView> {
            const session: Session = req.session
            const cartReviewPayload: CartReviewPayload = req.body
            const latitude: number = req.headers["lat"] ? Number(req.headers["lat"]) : undefined
            const longitude: number = req.headers["lon"] ? Number(req.headers["lon"]) : undefined
            const userContext = req.userContext as UserContext
            const listingBrand: ListingBrandIdType  | "CULT_BIKE" = cartReviewPayload.option.listingBrand
            const isCultBikeFlow = (listingBrand as string) == "CULT_BIKE"
            const tenant: Tenant = AppUtil.getTenantFromReq(req)
            if (_.isEmpty(cartReviewPayload.orderProducts)) {
                throw this.errorFactory.withCode(ErrorCodes.CART_EMPTY_ERR, 400).withDebugMessage("No items in cart").build()
            }
            this.processPayloadForWeb(cartReviewPayload)
            let totalNumberOfProducts = 0
            cartReviewPayload.orderProducts.forEach(orderProduct => {
                totalNumberOfProducts = totalNumberOfProducts + orderProduct.quantity
            })
            const cityId = userContext.userProfile.cityId
            this.cartSizeCheck(listingBrand, totalNumberOfProducts, cityId)
            const apiKey: string = req.headers["apikey"] as string
            const orderSource: OrderSource = AppUtil.callSource(apiKey)
            if (!MealUtil.isFoodPackBundleSupported(userContext)) {
                cartReviewPayload.option.deliveryInfo = [{
                    deliverySlot: cartReviewPayload.option.deliverySlot,
                    mealSlot: cartReviewPayload.option.mealSlot,
                    addressId: cartReviewPayload.option.addressId,
                    weekendAddressId: cartReviewPayload.option.weekendAddressId
                }]
            }
            if (_.isEmpty(cartReviewPayload.option.deliveryInfo)) {
                cartReviewPayload.option.deliveryInfo = [{ mealSlot: cartReviewPayload.option.mealSlot }]
            }
            if (cartReviewPayload.option.deliveryInfo[0].addressId) {
                try {
                    const updateBrowseLocation = await this.userBusiness.updateBrowseLocation(session, {
                        addressId: cartReviewPayload.option.deliveryInfo[0].addressId,
                        shouldUpdateSession: isCultBikeFlow ? true : undefined,
                        listingBrand: isCultBikeFlow ? listingBrand : undefined
                    }, tenant)
                    // Added this since session is updated only in next api call, so manually updating the city for cult bike flow on update browse location
                    if (updateBrowseLocation?.city?.cityId !== userContext.userProfile.cityId && isCultBikeFlow) {
                        userContext.userProfile.city = updateBrowseLocation.city
                        userContext.userProfile.cityId = updateBrowseLocation.city.cityId
                    }
                } catch (err) {
                    // To handle the case where client is passing invalid or deleted address id
                    if (err.statusCode === ADDRESS_NOT_FOUND_ERROR_CODE) {
                        cartReviewPayload.option.deliveryInfo[0].addressId = undefined
                    } else {
                        throw err
                    }
                }
            }
            const mealSlot = cartReviewPayload.option && cartReviewPayload.option.deliveryInfo && cartReviewPayload.option.deliveryInfo[0] ? cartReviewPayload.option.deliveryInfo[0].mealSlot : undefined
            const isPack = cartReviewPayload.orderProducts[0].option.isPack
            const isFitcashOnReviewPageSupported = false
            // todo: temporary fix for enabling orders in qsr shared areas between 12 - 1
            const ignoreServiceableTimings = isPack || (TimeUtil.getMomentNow(TimeUtil.IST_TIMEZONE).hours() === 0)
            const preferredLocation = isCultBikeFlow ? undefined : await this.userBusiness.getPreferredLocation(userContext, userContext.userProfile.userId, userContext.sessionInfo.sessionData, longitude, latitude, mealSlot, ignoreServiceableTimings, cartReviewPayload.option.listingBrand)
            // this.logger.info(`Cartreview: preferredLocation: ${JSON.stringify(preferredLocation)}`)
            this.logger.info(`ignoreServiceableTimings: ${ignoreServiceableTimings} hours: ${TimeUtil.getMomentNow(TimeUtil.IST_TIMEZONE).hours()}`)
            cartReviewPayload && cartReviewPayload.option && cartReviewPayload.option.deliveryInfo[0] && this.logger.info(`Cartreview: deliveryInfo: ${JSON.stringify(cartReviewPayload.option.deliveryInfo[0])}`)
            const brand: EatBrand | "FOOD_MARKETPLACE" | "CULT_BIKE" = EatUtil.getEatBrand(preferredLocation, listingBrand)
            const isFitcashEnabled = (brand === "CULT_BIKE" && cartReviewPayload.option.isFitcashEnabled === true)  || false
            const request = this.generateReviewRequest(userContext, orderSource, session, cartReviewPayload, latitude, longitude, preferredLocation, isFitcashEnabled, isFitcashOnReviewPageSupported)

            return this.BrandReviewPageMap.get(brand).build(this.interfaces, request, userContext)
        }

        private generateReviewRequest(userContext: UserContext, orderSource: OrderSource, session: Session, cartReviewPayload: CartReviewPayload, lat: number, long: number, preferredLocation: PreferredLocation, isFitcashEnabled: boolean, isFitcashOnReviewPageSupported: boolean) {

            const userDetailInfo: UserDetailInfo = {
                city: userContext.userProfile.city,
                cityId: userContext.userProfile.cityId,
                userTimezone: userContext.userProfile.timezone,
                orderSource: orderSource,
                userId: userContext.userProfile.userId,
                deviceId: userContext.sessionInfo.deviceId,
                userAgent: userContext.sessionInfo.userAgent,
                osName: userContext.sessionInfo.osName,
                appVersion: userContext.sessionInfo.appVersion,
                cpVersion: userContext.sessionInfo.cpVersion
            }

            const request: ReviewRequest = {
                sessionData: session.sessionData,
                cartReviewPayload: cartReviewPayload,
                latitude: lat,
                longitude: long,
                userDetailInfo: userDetailInfo,
                preferredLocation: preferredLocation,
                isFitcashEnabled,
                isFitcashOnReviewPageSupported
            }

            return request
        }

        private processPayloadForWeb(cartReviewPayload: CartReviewPayload) {
            if (cartReviewPayload.option.listingBrand === "FOOD_MARKETPLACE") {
                return
            }
            if (cartReviewPayload.option.deliverySlot === "") {
                cartReviewPayload.option.deliverySlot = undefined
            }
            if (cartReviewPayload.option.addressId === "") {
                cartReviewPayload.option.addressId = undefined
            }
            if (cartReviewPayload.option.weekendAddressId === "") {
                cartReviewPayload.option.weekendAddressId = undefined
            }
            if (cartReviewPayload.option.deliveryInfo) {
                cartReviewPayload.option.deliveryInfo = cartReviewPayload.option.deliveryInfo.filter((deliveryInfo) => {
                    return !_.isNil(deliveryInfo.mealSlot) && !_.isObject(deliveryInfo.mealSlot)
                })
            }
        }

        private getDaywiseAvailableSlots(areaTz: Timezone, dateWiseAvailableSlots: { [date: string]: string[] }, inventoryMap: { [p: string]: { [p: string]: string[] } }, cartReviewPayload: CartReviewPayload, listingBrand: ListingBrandIdType) {
            if (listingBrand === "WHOLE_FIT") {
                const allSlots: DeliverySlot[] = this.deliverySlotService.getDeliverySlotsForListingBrand("ALL", "ONLINE", "WHOLE_FIT")
                const len: number = allSlots.length - 1
                const presentDay = TimeUtil.todaysDate(areaTz)
                const nextDay = TimeUtil.addDays(areaTz, presentDay, 1)
                const DayAfter = TimeUtil.addDays(areaTz, presentDay, 2)
                const deliveryWindowForSlot = SlotUtil.getDeliveryWindowFromHourMin(presentDay, allSlots[len].startTime, allSlots[len].endTime, areaTz)
                const slotCutOff = SlotUtil.getHardCutOffByChannelOrSlot(allSlots[len].slotId, deliveryWindowForSlot, allSlots[len].deliveryChannel, areaTz)
                const currTime = TimeUtil.now(areaTz)
                if (TimeUtil.compare(slotCutOff, currTime) > 0) {
                    dateWiseAvailableSlots[presentDay] = inventoryMap[cartReviewPayload.orderProducts[0].productId][presentDay]
                    dateWiseAvailableSlots[nextDay] = inventoryMap[cartReviewPayload.orderProducts[0].productId][nextDay]
                } else {
                    dateWiseAvailableSlots[nextDay] = inventoryMap[cartReviewPayload.orderProducts[0].productId][nextDay]
                    dateWiseAvailableSlots[DayAfter] = inventoryMap[cartReviewPayload.orderProducts[0].productId][DayAfter]
                }
            }
        }

        checkComboCreation(cartReviewPayload: CartReviewPayload, cartReviewResult: CartReviewResponse): AlertInfo {
            const updateLog: { title: string, subTitle: string }[] = []
            const requestComboQtyMap: { [id: string]: number } = {}
            const responseComboQtyMap: { [id: string]: number } = {}
            cartReviewPayload.orderProducts.forEach((orderProduct) => {
                if (orderProduct.option.comboOfferId) {
                    requestComboQtyMap[orderProduct.productId] = orderProduct.quantity
                }
            })
            cartReviewResult.order.productSnapshots.forEach((productSnapshot: OrderProductSnapshots) => {
                if (productSnapshot.comboOfferId) {
                    responseComboQtyMap[productSnapshot.productId] = productSnapshot.quantity
                }
            })
            const requestHasMoreKeys = _.keys(requestComboQtyMap).length >= _.keys(responseComboQtyMap).length
            _.mapValues(requestHasMoreKeys ? requestComboQtyMap : responseComboQtyMap, (qty, pid) => {
                const lookUpMap = requestHasMoreKeys ? responseComboQtyMap : requestComboQtyMap
                const responseProduct = _.find(cartReviewResult.order.productSnapshots, (productSnapshot: OrderProductSnapshots) => { return productSnapshot.productId === pid && !_.isNil(productSnapshot.comboOfferId) })
                if (_.isNil(lookUpMap[pid]) || lookUpMap[pid] < qty) {
                    if (requestHasMoreKeys) {
                        updateLog.push({
                            title: "Combo removed",
                            subTitle: "The combo offer was removed"
                        })
                    } else {
                        updateLog.push({
                            title: "Combo created",
                            subTitle: "A combo offer has been applied on " + responseProduct.title
                        })
                    }
                } else if (lookUpMap[pid] > qty) {
                    if (requestHasMoreKeys) {
                        updateLog.push({
                            title: "Combo created",
                            subTitle: "A combo offer has been applied on " + responseProduct.title
                        })
                    } else {
                        updateLog.push({
                            title: "Combo removed",
                            subTitle: "The combo offer was removed"
                        })
                    }
                }
            })
            if (updateLog.length === 0) {
                return undefined
            } else {
                return {
                    title: "Cart Updated",
                    subTitle: undefined,
                    listItems: updateLog,
                    actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
                }
            }
        }

        checkSlotAvailabiltiy(availableSlots: string[], dateWiseAvailableSlots: { [date: string]: string[] }, listingBrand: ListingBrandIdType, selectedSlot?: string, date?: string): { alertInfo: AlertInfo, updatedSlot: string, updatedDate: string } {
            if (listingBrand === "WHOLE_FIT") {
                return this.checkSlotAvailabilityForWholeFit(dateWiseAvailableSlots, selectedSlot, date)
            }
            if (_.isNil(availableSlots) || _.isEmpty(availableSlots)) {
                this.logger.error("Slots not available")
                return {
                    updatedSlot: undefined,
                    updatedDate: undefined,
                    alertInfo: {
                        title: "Sold out",
                        subTitle: "All slots are unavailable at this location",
                        actions: [{
                            actionType: "HIDE_ALERT_MODAL",
                            title: "Ok",
                            url: "curefit://eatfitclp"
                        }]
                    }
                }
            }
            if (selectedSlot) {
                if (_.findIndex(availableSlots, (slot) => {
                    return selectedSlot === slot
                }) === -1) {
                    return {
                        updatedSlot: availableSlots[0],
                        updatedDate: date,
                        alertInfo: {
                            title: "Slot Unavailable",
                            subTitle: "Orders for your selected slot are unavailable on this date. The next available slot has been selected",
                            actions: [{
                                actionType: "HIDE_ALERT_MODAL",
                                title: "Ok"
                            }]
                        }
                    }
                }
            }
            return { updatedSlot: selectedSlot, updatedDate: date, alertInfo: undefined }
        }

        private checkSlotAvailabilityForWholeFit(dateWiseAvailableSlots: { [p: string]: string[] }, selectedSlot: string, date: string): { alertInfo: AlertInfo, updatedSlot: string, updatedDate: string } {
            let slotsUnavailable = true
            const days = _.keys(dateWiseAvailableSlots)
            if (!_.isEmpty(days)) {
                for (const day of days) {
                    if (!_.isEmpty(dateWiseAvailableSlots[day])) {
                        slotsUnavailable = false
                        break
                    }
                }
            }
            if (slotsUnavailable) {
                return {
                    updatedSlot: undefined,
                    updatedDate: undefined,
                    alertInfo: {
                        title: "Sold out",
                        subTitle: "All slots are unavailable at this location",
                        actions: [{
                            actionType: "HIDE_ALERT_MODAL",
                            title: "Ok",
                            url: "curefit://eatfitclp"
                        }]
                    }
                }
            }
            if (selectedSlot && date) {
                const slotsForSelectedDate = dateWiseAvailableSlots[date]
                if (_.findIndex(slotsForSelectedDate, slot => {
                    return slot === selectedSlot
                }) === -1) {
                    const days = _.keys(dateWiseAvailableSlots)
                    let updatedSlot
                    let updatedDate
                    for (const day of days) {
                        if (!_.isEmpty(dateWiseAvailableSlots[day])) {
                            updatedSlot = dateWiseAvailableSlots[day][0]
                            updatedDate = day
                            break
                        }
                    }
                    return {
                        updatedSlot: updatedSlot,
                        updatedDate: updatedDate,
                        alertInfo: {
                            title: "Slot Unavailable",
                            subTitle: "Orders for your selected slot are unavailable on this date. The next available slot has been selected",
                            actions: [{
                                actionType: "HIDE_ALERT_MODAL",
                                title: "Ok"
                            }]
                        }
                    }
                }
            }
            return { updatedSlot: selectedSlot, updatedDate: date, alertInfo: undefined }
        }

        private async handlePageCityMismatch(userCityId: string, pageCityId: string, canUpdateUserCity: any, userContext: UserContext, session: Session, pageId: string ): Promise<CartReviewView | null> {
            if (userCityId === pageCityId || !AppUtil.isAppCityMismatchModalSupported(userContext) || !AppUtil.isMobileApp(userContext)) {
                return null
            }
            if (canUpdateUserCity === true || canUpdateUserCity === "true") {
                await this.userBusiness.updateCity(userContext, session, pageCityId)
                return null
            }
            const userCityDetail: City = this.cityService.getCityById(userCityId)
            const pageCityDetail: City = this.cityService.getCityById(pageCityId)
            const cityModal = {
                title: "Do you want to change city from ",
                subTitle: "You are selecting a locality in " + pageCityDetail.name,
                selectedCityName: userCityDetail.name,
                detectedCityName: pageCityDetail.name,
                primaryButton: {
                    title: "Confirm"
                },
                secondaryButton: {
                    title: "Cancel",
                    url: "curefit://tabpage?pageId=" + pageId,
                    actionType: "NAVIGATION"
                }
            }
            return {widgets: [], cityModalData: cityModal}
        }

        @httpPost("/review/v1")
        async cartReviewV1(req: express.Request): Promise<CartReviewView> {
            const session: Session = req.session
            const vertical: string = req.query.vertical
            const userId = session.userId
            const userContext = req.userContext as UserContext
            const cartReviewPayload: CartReviewPayload = req.body
            const { canUpdateCity } = req.body
            const tz = userContext.userProfile.timezone
            if (_.isEmpty(cartReviewPayload.orderProducts)) {
                throw this.errorFactory.withCode(ErrorCodes.CART_EMPTY_ERR, 400).withDebugMessage("No items in cart").build()
            }
            if (CareUtil.isCultTransferCareProducts(cartReviewPayload.orderProducts[0].productId)) {
                return this.careCultDayTransferPageViewBuilder.getCultDaysSwapOrderConfirmationPage(userContext, cartReviewPayload.orderProducts[0].productId, _.get(cartReviewPayload, "orderProducts.0.option.memberShipType", "CULT"))
            }
            const countryId = _.get(userContext, "userProfile.city.countryId", null)
            if (countryId !== "IN") {
                cartReviewPayload.useFitCash = false
            }
            const apiKey: string = req.headers["apikey"] as string
            const orderSource: OrderSource = AppUtil.callSource(apiKey)
            let cityId = session.sessionData.cityId
            const user = await this.userService.getUser(userId)
            const result = AppUtil.getUserAlertInfo(user, userContext)
            const appVersion = userContext.sessionInfo.appVersion
            const code: string = result.code
            if (!_.isEmpty(code) && (
                ((AppUtil.isWeb(userContext) && !AuthUtil.isFixedGuestUser(userContext)) || !AuthUtil.isGuestUser(userContext))
                || appVersion < 10.13
            )) {
                throw this.errorFactory.withCode(result.code, result.alertInfo.statusCode).build()
            }
            const userSegments = await this.segmentationCacheClient.getUserSegments(
                userId
            )
            const product = await this.catalogueServicePMS.getCatalogProduct(cartReviewPayload.orderProducts[0].productId)

            let correspondingSkuPlusPackId: string = undefined
            let correspondingSkuPlusPack: AugmentedOfflineFitnessPack = undefined

            if (product.productType === "ADDON") {
                if (AppUtil.isAppSupportedAddonPurchase(userContext)) {
                    throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, HTTP_CODE.BAD_REQUEST).build()
                } else {
                    throw this.errorFactory.withCode(ErrorCodes.APP_VERSION_NOT_SUPPORTED_ANDROID_ERR, HTTP_CODE.PRECONDITION_FAILED).build()
                }
            }

            const addonProductIdsFromReqBody: string[] = cartReviewPayload?.orderProducts[0]?.addonProductIds
            let boosterPack: OfflineFitnessPack = undefined
            const isBoosterPackFlowActive = AppUtil.isBoosterPackFlowActive()
            if (isBoosterPackFlowActive &&  product && (product.productType === "FITNESS" || product.productType === "GYMFIT_FITNESS_PRODUCT")) {
                const addOnPacks: OfflineFitnessPack[] = await this.offlineFitnessPackService.fetchAddOnPacks({
                    basePackId: product.productId, purchaseFlow: PurchaseFlow.PRE,
                    namespace: Namespace.OFFLINE_FITNESS
                })
                if (addOnPacks && addOnPacks.length > 0) {
                    boosterPack = addOnPacks[0]
                }
            }

            const bundleProduct = product?.productType === "BUNDLE" ? <DiagnosticProduct>product : undefined
            const isCultLivePT = bundleProduct && (bundleProduct.subCategoryCode === "LIVE_PERSONAL_TRAINING" || bundleProduct.subCategoryCode === "LIVE_SGT"
                || CareUtil.isTransformTenant(bundleProduct.subCategoryCode) || bundleProduct.subCategoryCode === "MIND_THERAPY")
            const isGymPT = product && product.productType === "GYM_PT_PRODUCT"
            const isLux = product && product.productType === "LUX_FITNESS_PRODUCT"
            // FITNESS, GYMFIT_FITNESS_PRODUCT
            if ((AppUtil.isSugarFitOrUltraFitApp(userContext) || isCultLivePT) && !cartReviewPayload.orderProducts[0].option.patientId) {
                cartReviewPayload.orderProducts[0].option.patientId = (await CareUtil.getOrCreatePatient(userContext, this.userCache, this.healthfaceService)).id
            }
            // Filter for duplicate Products if any
            let uniqueProducts = [...new Map(cartReviewPayload.orderProducts.map(item => [item.productId, item])).values()]

            if (boosterPack?.productId && addonProductIdsFromReqBody?.length > 0) {
                uniqueProducts[0].addonProductIds = [boosterPack?.productId]
            }

            let centerId = uniqueProducts[0].option.centerId
            const centerServiceId = uniqueProducts[0].option.centerId

            let clientMetadata: CultMembershipMetadata = req.body.clientMetadata ? req.body.clientMetadata : {}
            clientMetadata.subUserId = userContext.userProfile.subUserId ? userContext.userProfile.subUserId : clientMetadata.subUserId
            let minimumEligibleStartDate
            if (isGymPT) {
                const agentDetails: Doctor = await this.ollivanderService.getDoctorDetails(cartReviewPayload.orderProducts[0].option.doctorId)
                const trainerIdentityId: number = agentDetails.identityId
                const centerServiceResponse: CenterResponse = await this.centerService.getCenterById(Number(centerId))
                const gymCenterId = centerServiceResponse.meta.gymfitCenterId
                const gymPtProduct: GymPtProduct = await this.catalogueService.getGymPtProductById(cartReviewPayload.orderProducts[0].productId)
                const date = await this.getEarliestStartDateForPtMembership(userContext, gymPtProduct.durationInDays)
                minimumEligibleStartDate = date
                this.logger.info(`Earliest StartDate for PT membership: ${JSON.stringify(date)}`)
                centerId = gymCenterId.toString()
                uniqueProducts = uniqueProducts.map(product => {
                    let startDate: string = product?.option?.startDate
                    if (startDate && startDate < date) {
                        startDate = date
                    }
                    return {
                        ...product,
                        option: {
                            ...product.option,
                            centerId: gymCenterId.toString(),
                            startDate: startDate ? startDate : date,
                            gymPtOrderOptions: {
                                preferredTrainerId: trainerIdentityId.toString()
                            }
                        }
                    }
                })

                clientMetadata = Object.assign({}, clientMetadata, {
                    centerId: gymCenterId,
                    centerServiceCenterId: centerServiceResponse.id
                })

                // disable fitcash usage for gym pt as payment is breaking for PT product when fitcash is used. Disable till that is fixed.
                cartReviewPayload.useFitCash = false
            }

            if (product.productType === "PLAY" && product.isPack) {
                const playPack: OfflineFitnessPack = await this.catalogueServicePMS.getProduct(cartReviewPayload.orderProducts[0].productId)

                if (!(playPack.status === "ACTIVE")) {
                    const packInActiveModal = {
                        title: appVersion < 10.61 ? "Pack is not available for sale. for explore others pack " : "Pack is not available for sale. Explore our others offering.",
                        icon: "no_show",
                        subTitle: "This pack is no longer active.",
                        selectedCityName: appVersion < 10.61 ? "Go" : null,
                        detectedCityName: appVersion < 10.61 ? "home" : null,
                        primaryButton : appVersion >= 10.61 ? {
                            title: " I UNDERSTOOD",
                            url: "curefit://tabpage?pageId=playclp",
                            actionType: "NAVIGATION",
                            meta : {
                                refreshPage: false
                            }
                        } : null,
                        secondaryButton: appVersion < 10.61 ? {
                            title: " I UNDERSTOOD",
                            url: "curefit://tabpage?pageId=playclp",
                            actionType: "NAVIGATION"
                        } : null
                    }
                    return {widgets: [], cityModalData: packInActiveModal}
                }

                const cityModal = await this.handlePageCityMismatch(
                    userContext.userProfile.cityId,
                    playPack?.clientMetadata.cityId,
                    canUpdateCity,
                    userContext,
                    session,
                    "hometab"
                )
                if (!_.isNil(cityModal)) {
                    return cityModal
                }
                // Temp fix to handle the case where device is passing past date as start date
                if (uniqueProducts[0].option.startDate && uniqueProducts[0].option.startDate < TimeUtil.todaysDateWithTimezone(tz)) {
                    uniqueProducts[0].option.startDate = TimeUtil.todaysDateWithTimezone(tz)
                }

                if (uniqueProducts[0].option.centerId) {
                    clientMetadata = Object.assign({}, clientMetadata, {
                        centerId: uniqueProducts[0].option.centerId,
                        centerServiceCenterId: uniqueProducts[0].option.centerId
                    })
                }

                if (uniqueProducts[0].option.centerServiceCenterId) {
                    clientMetadata = Object.assign({}, clientMetadata, {
                        centerServiceCenterId: uniqueProducts[0].option.centerServiceCenterId
                    })
                }

                if (uniqueProducts[0].option.workoutId) {
                    clientMetadata = Object.assign({}, clientMetadata, {
                        workoutId: uniqueProducts[0].option.workoutId
                    })
                }

                const metadata = await this.cultBusiness.getPlayClientMetadataForOrder(userContext)
                if (metadata) {
                    clientMetadata = Object.assign({}, clientMetadata, {...metadata})
                }
            }

            if (product && product.productType === "GYMFIT_FITNESS_PRODUCT" && product.isPack) {
                const gymfitFitnessProduct = await this.catalogueServicePMS.getProduct(cartReviewPayload.orderProducts[0].productId)
                if (gymfitFitnessProduct?.product?.productSubType === ProductSubType.ADDONBUNDLE) {
                    const proPlusAddOnIds = gymfitFitnessProduct?.baseAddOns?.map((addOn) => addOn?.packId)
                    if (proPlusAddOnIds?.length > 0) {
                        uniqueProducts[0].addonProductIds = proPlusAddOnIds
                    }
                }

                // SKU PLUS BOTTOMSHEET CHANGES
                // FETCHING ALL PRO PLUS PACKS AT ONCE

                let proPlusPacks: AugmentedOfflineFitnessPack[] = await this.offlineFitnessPackService.searchCachedPacksWithAugments({
                    namespace: Namespace.OFFLINE_FITNESS,
                    productTypes: ["GYMFIT_FITNESS_PRODUCT"],
                    productSubType: ProductSubType.PLUS,
                    status: "ACTIVE",
                    saleEnabled: true,
                    userId: userId,
                    visibility: Visibility.APP,
                    restrictions: {cities: [cityId]},
                    durationLowerLimit: gymfitFitnessProduct.product.durationInDays - 5,
                    durationUpperLimit: gymfitFitnessProduct.product.durationInDays + 5,
                    augments: { includeExhaustiveBenefits: true }
                })
                if (gymfitFitnessProduct?.constraints?.segmentIds?.length > 0) {
                    proPlusPacks = proPlusPacks?.filter((pack) => pack?.constraints?.segmentIds?.length > 0)
                }
                correspondingSkuPlusPack = proPlusPacks?.length > 0 ? proPlusPacks[0] : undefined
                correspondingSkuPlusPackId = correspondingSkuPlusPack?.id

                const cityModal = await this.handlePageCityMismatch(
                    userContext.userProfile.cityId,
                    gymfitFitnessProduct?.clientMetadata?.cityId,
                    canUpdateCity,
                    userContext,
                    session,
                    "fitnesshub"
                )
                if (!_.isNil(cityModal)) {
                    return cityModal
                }
                const centerServiceId = CatalogueServiceUtilities.getAccessLevel(gymfitFitnessProduct) == GymfitAccessLevel.CENTER ? CatalogueServiceUtilities.getExternalAccessLevelId(gymfitFitnessProduct) : uniqueProducts[0].option.centerServiceCenterId
                this.logger.info("Pro Center Id: " + uniqueProducts[0].option.centerId + ", Pro Center Service Center Id: " + uniqueProducts[0].option.centerServiceCenterId + "Pro Center Service Id : " + centerServiceId)
                let date = await this.getEarliestStartDateForFitnessMembership(userContext, gymfitFitnessProduct.product.durationInDays, centerServiceId, gymfitFitnessProduct.title)
                this.logger.info("Pro Start Date: " + date)
                if (date < TimeUtil.todaysDateWithTimezone(tz)) {
                    date = TimeUtil.todaysDateWithTimezone(tz)
                }
                let centerId: string
                if (centerServiceId) {
                    const center = await this.centerService.getCenterById(Number(centerServiceId))
                    centerId = center?.meta?.gymfitCenterId?.toString()
                    clientMetadata = Object.assign({}, clientMetadata, {
                        centerId,
                        centerServiceCenterId: centerServiceId,
                    })
                }
                uniqueProducts = uniqueProducts.map(product => {
                    const options: OrderProductOption = {
                        startDate: date
                    }
                    options["centerServiceCenterId"] = centerServiceId
                    options["centerId"] = centerId
                    return {
                        ...product,
                        option: options
                    }
                })
                cityId = gymfitFitnessProduct?.clientMetadata?.cityId ?? cityId
            }

            if (isLux) {
                const luxPack: OfflineFitnessPack = await this.offlineFitnessPackService.getCachedPackById(cartReviewPayload.orderProducts[0].productId)
                const cityModal = await this.handlePageCityMismatch(
                    userContext.userProfile.cityId,
                    luxPack.clientMetadata?.cityId,
                    canUpdateCity,
                    userContext,
                    session,
                    "fitnesshub"
                )
                if (!_.isNil(cityModal)) {
                    return cityModal
                }
                const date = await this.getEarliestStartDateForLuxMembership(userContext, luxPack.product.durationInDays, luxPack)
                const centerServiceId = luxPack.restrictions?.centers?.[0]
                const center = centerServiceId && await this.centerService.getCenterById(centerServiceId)
                const centerId = center?.meta?.gymfitCenterId
                uniqueProducts = uniqueProducts.map(product => {
                    return {
                        ...product,
                        option: {
                            centerId: centerId?.toString(),
                            centerServiceCenterId : centerServiceId?.toString(),
                            startDate: date
                        }
                    }
                })
                clientMetadata = Object.assign({}, clientMetadata, {
                    centerId: centerId,
                    centerServiceCenterId : centerServiceId,
                })
                cityId = luxPack.clientMetadata?.cityId ?? cityId
            }

            const createOrderPayload: OrderCreate = {
                userId: session.userId,
                deviceId: session.deviceId,
                products: uniqueProducts,
                source: orderSource,
                cityId: cityId,
                useOffersV2: true,
                dontCreateRazorpayOrder: true,
                useFitCash: cartReviewPayload.useFitCash ? cartReviewPayload.useFitCash : false,
                tenant: AppUtil.getTenantFromUserContext(userContext),
                clientMetadata,
                osName: userContext.sessionInfo.osName,
                appVersion: (userContext.sessionInfo.appVersion ?? 0).toString()
            }

            const omsCartReviewPayload: OrderCheckoutRequest = _.cloneDeep({
                userId: session.userId,
                deviceId: session.deviceId,
                orderProducts: uniqueProducts,
                source: orderSource,
                cityId: cityId,
                useFitCash: cartReviewPayload.useFitCash ? cartReviewPayload.useFitCash : false,
                tenant: AppUtil.getTenantFromUserContext(userContext),
                clientMetadata,
                osName: userContext.sessionInfo.osName, //
                appVersion: (userContext.sessionInfo.appVersion ?? 0).toString(),
                advertiserId: undefined,
                userAddress: undefined
            })
            if (["BUNDLE", "SF_CONSUMABLE", "CONSULTATION", "DIAGNOSTICS", "FITNESS", "MIND", "FOOD_MARKETPLACE", "GYM_PT_PRODUCT", "PLAY", "LUX_FITNESS_PRODUCT"].includes(product.productType)) {
                createOrderPayload.offersVersion = 3
            }

            if (product && product.productType === "FITNESS" && product.isPack) {
                const cultProduct: OfflineFitnessPack = await this.catalogueServicePMS.getProduct(cartReviewPayload.orderProducts[0].productId)
                if (cultProduct?.product?.productSubType === ProductSubType.ADDONBUNDLE) {
                    const elitePlusAddOnIds = cultProduct?.baseAddOns?.map((addOn) => addOn?.packId)
                    if (elitePlusAddOnIds?.length > 0) {
                        createOrderPayload.products[0].addonProductIds = elitePlusAddOnIds
                        omsCartReviewPayload.orderProducts[0].addonProductIds = elitePlusAddOnIds
                    }
                }
                const centerServiceId = createOrderPayload.products[0].option.centerServiceCenterId
                const isSelectFlow = CatalogueServiceUtilities.getAccessLevel(cultProduct) === AccessLevel.CENTER
                const startDateFromReqBody = cartReviewPayload?.orderProducts[0]?.option?.startDate

                let restrictions: Restriction = {cities: [cityId]}
                let productSubTypeUpgradePack: ProductSubType
                if (cultProduct?.product?.productSubType === ProductSubType.LITE) {
                    // SKU BOTTOMSHEET FUP
                    if (isSelectFlow) {
                        restrictions = {centers: [cultProduct?.restrictions?.centers?.[0]]}
                    }
                    productSubTypeUpgradePack = ProductSubType.GENERAL
                } else {
                    // SKU PLUS BOTTOMSHEET CHANGES
                    productSubTypeUpgradePack = ProductSubType.PLUS
                }
                let upgradedPacks: AugmentedOfflineFitnessPack[] = await this.offlineFitnessPackService.searchCachedPacksWithAugments({
                    namespace: Namespace.OFFLINE_FITNESS,
                    productTypes: ["FITNESS"],
                    productSubType: productSubTypeUpgradePack,
                    status: "ACTIVE",
                    saleEnabled: true,
                    userId: userId,
                    visibility: Visibility.APP,
                    restrictions: restrictions,
                    durationLowerLimit: cultProduct.product.durationInDays - 5,
                    durationUpperLimit: cultProduct.product.durationInDays + 5,
                    augments: { includeExhaustiveBenefits: true }
                })
                if (cultProduct?.product?.productSubType !== ProductSubType.LITE && cultProduct?.constraints?.segmentIds?.length > 0) {
                    upgradedPacks = upgradedPacks?.filter((pack) => pack?.constraints?.segmentIds?.length > 0)
                }
                if (isSelectFlow && _.isNil(startDateFromReqBody)) {
                    correspondingSkuPlusPack = upgradedPacks?.length > 0 ? upgradedPacks[0] : undefined
                    correspondingSkuPlusPackId = correspondingSkuPlusPack?.id
                } else if (_.isNil(centerServiceId) && _.isNil(startDateFromReqBody)) {
                    correspondingSkuPlusPack = upgradedPacks?.length > 0 ? upgradedPacks[0] : undefined
                    correspondingSkuPlusPackId = correspondingSkuPlusPack?.id
                }

                const cityModal = await this.handlePageCityMismatch(
                    userContext.userProfile.cityId,
                    cultProduct.clientMetadata?.cityId,
                    canUpdateCity,
                    userContext,
                    session,
                    "fitnesshub"
                )
                if (!_.isNil(cityModal)) {
                    return cityModal
                }
                const date = await this.getEarliestStartDateForFitnessMembership(userContext, cultProduct.product.durationInDays, centerServiceId, cultProduct.title)
                createOrderPayload.products[0].option.startDate = date
                omsCartReviewPayload.orderProducts[0].option.startDate = date

                if (createOrderPayload.products[0].option.startDate < TimeUtil.todaysDateWithTimezone(tz)) {
                    createOrderPayload.products[0].option.startDate = TimeUtil.todaysDateWithTimezone(tz)
                    omsCartReviewPayload.orderProducts[0].option.startDate = TimeUtil.todaysDateWithTimezone(tz)
                }

                if (createOrderPayload.products[0].option.centerId) {
                    createOrderPayload.clientMetadata = Object.assign({}, createOrderPayload.clientMetadata, {
                        centerId: createOrderPayload.products[0].option.centerId
                    })
                    omsCartReviewPayload.clientMetadata = Object.assign({}, omsCartReviewPayload.clientMetadata, {
                        centerId: omsCartReviewPayload.orderProducts[0].option.centerId
                    })
                }

                if (createOrderPayload.products[0].option.centerServiceCenterId) {
                    createOrderPayload.clientMetadata = Object.assign({}, createOrderPayload.clientMetadata, {
                        centerServiceCenterId: createOrderPayload.products[0].option.centerServiceCenterId
                    })
                    omsCartReviewPayload.clientMetadata = Object.assign({}, omsCartReviewPayload.clientMetadata, {
                        centerServiceCenterId: omsCartReviewPayload.orderProducts[0].option.centerServiceCenterId
                    })
                }

                const metadata = await this.cultBusiness.getClientMetadataForOrder(userContext, createOrderPayload, product)
                if (metadata) {
                    createOrderPayload.clientMetadata = Object.assign({}, createOrderPayload.clientMetadata, { ...metadata })
                    omsCartReviewPayload.clientMetadata = Object.assign({}, omsCartReviewPayload.clientMetadata, { ...metadata })
                }
                if (!createOrderPayload.products[0].option.centerServiceCenterId && createOrderPayload.products[0].option.centerId) {
                    let centerId = createOrderPayload.products[0].option.centerId
                    // for upgrade orders
                    if (createOrderPayload.clientMetadata && isMembershipUpgradeClientMetadata(createOrderPayload.clientMetadata)) {
                        centerId = (createOrderPayload.clientMetadata as CultMindMembershipUpgradeMetadata).cultCenter.id.toString()
                    }
                    const cultCenter = await this.catalogueService.getCultCenter(centerId)
                    createOrderPayload.products[0].option.centerServiceCenterId = cultCenter.centerServiceId
                    omsCartReviewPayload.orderProducts[0].option.centerServiceCenterId = cultCenter.centerServiceId
                }
                cityId = cultProduct?.clientMetadata?.cityId ?? cityId
                omsCartReviewPayload.cityId = cityId
                createOrderPayload.cityId = cityId
            }

            let couponData: UserOfferResponseV2
            if (AppUtil.isSugarFitOrUltraFitApp(userContext)) {
                try {
                    if (["SF_CONSUMABLE"].includes(product.productType)) {
                        if (cartReviewPayload?.couponId === "") {
                            // User manually removes the coupon
                            const couponResponse = await this.indusService.applyOfferV2(Number(userId), " ")
                            if (couponResponse) {
                                couponData = { couponCode: cartReviewPayload?.couponId, message: "", isValid: couponResponse?.isValid }
                            }
                        } else if (!_.isNil(cartReviewPayload?.couponId)) {
                            // User applies the coupon
                            const couponResponse = await this.indusService.applyOfferV2(Number(userId), cartReviewPayload?.couponId)
                            if (couponResponse) {
                                couponData = couponResponse
                            }
                        } else if (_.isNil(cartReviewPayload?.couponId)) {
                            // Check if perviously applied coupons are present. Could be user applied coupona nd logged out and relogged-in.
                            const couponResponse = await this.indusService.getAppliedOffers(Number(userId))
                            if (couponResponse) {
                                couponData = couponResponse
                            }
                        }
                    }
                } catch (e) {
                    try {
                        this.logger.info("Error in coupon", JSON.stringify(e))
                    } catch (e) {
                        this.logger.info("Error in coupon ==> Json parser")
                    }
                    couponData = { couponCode: cartReviewPayload?.couponId, message: "", isValid: false }
                }
                const amountPayableOverride: number = await this.getSugarfitPriceOverride(userId, createOrderPayload, req)
                if (amountPayableOverride) {
                    _.set(createOrderPayload, "protectedParams.amountPayableOverride", amountPayableOverride)
                }
            }

            const omsCartReviewPayloadCorrespondingSkuPlus: OrderCheckoutRequest = _.cloneDeep(omsCartReviewPayload)
            if (correspondingSkuPlusPackId) {
                omsCartReviewPayloadCorrespondingSkuPlus.orderProducts[0].productId = correspondingSkuPlusPackId
            }
            let correspondingSkuPlusOrder: Order

            createOrderPayload.omsCheckoutRequest = omsCartReviewPayload
            let order: Order
            let payByMembershipDetails
            if (await this.orderViewBuilder.isOMSCheckoutFlow(omsCartReviewPayload.userId, omsCartReviewPayload.deviceId, product, "CART-REVIEW", AppUtil.getTenantFromUserContext(userContext))) {
                order = (await this.omsApiClient.reviewOrderCart(omsCartReviewPayload)).order
                if (correspondingSkuPlusPackId) {
                    correspondingSkuPlusOrder = (await this.omsApiClient.reviewOrderCart(omsCartReviewPayloadCorrespondingSkuPlus)).order
                }
            }
            else {
                const cartReviewResult = await this.omsApiClient.cartReview(createOrderPayload)
                order = cartReviewResult.order
                payByMembershipDetails = cartReviewResult.payByMembershipDetails
            }
            return this.cartViewBuilder.buildCartReviewViewV1(userContext, vertical, order, payByMembershipDetails, cartReviewPayload,
                session.userId, session.deviceId, this.userAttributeClient, user, +centerServiceId, couponData, minimumEligibleStartDate, boosterPack, addonProductIdsFromReqBody, correspondingSkuPlusPackId, correspondingSkuPlusOrder, correspondingSkuPlusPack)

        }

        @httpPost("/setfeaturestate")
        async setFeatureState(req: express.Request) {
            const { showSkuPlusModalKey, showSkuPlusModalState} = req.body
            const userContext = req.userContext as UserContext
            const timeToExpire = moment().add(1, "days").unix() - moment().unix()
            await this.featureStateCache.setWithExpiry(userContext.userProfile.userId, showSkuPlusModalKey, showSkuPlusModalState, timeToExpire)
            return {
                message: "success"
            }
        }

        private async overrideOffer(createOrderPayload: OrderCreate, singleOffersResponse: FoodSinglePriceOfferResponse, mealSlot: MenuType) {
            const productIds = _.map(createOrderPayload.products, product => { return product.productId })
            const productMap: { [productId: string]: Product } = await this.catalogueService.getProductMap(productIds)
            createOrderPayload.products.forEach(orderProduct => {
                const product = productMap[orderProduct.productId]
                const offerAndPrice = OfferUtil.getSingleOfferAndPrice(product, orderProduct.option.startDate, singleOffersResponse, mealSlot)
                if (!_.isEmpty(offerAndPrice.offers)) {
                    orderProduct.option.offerV2Ids = _.map(offerAndPrice.offers, offer => {
                        return offer.offerId
                    })
                } else {
                    orderProduct.option.offerV2Ids = undefined
                    orderProduct.option.offerId = undefined
                }
            })
        }

        @httpPost("/checkout")
        async cartCheckout(req: express.Request): Promise<OrderCheckoutDetail | CartReviewView | ConfirmationView> {
            const cartReviewPayload: CartReviewPayload = req.body
            if (_.isEmpty(cartReviewPayload.orderProducts)) {
                throw this.errorFactory.withCode(ErrorCodes.CART_EMPTY_ERR, 400).withDebugMessage("No items in cart").build()
            }
            const isPack = cartReviewPayload.orderProducts[0].option.isPack ? true : false
            const userContext: UserContext = req.userContext as UserContext
            userContext.userProfile.promiseMapCache ??= new PromiseCache(this.interfaces)
            const session: Session = req.session
            const latitude: number = req.headers["lat"] ? Number(req.headers["lat"]) : undefined
            const longitude: number = req.headers["lon"] ? Number(req.headers["lon"]) : undefined
            const apiKey: string = req.headers["apikey"] as string
            const orderSource: OrderSource = AppUtil.callSource(apiKey)
            const countryId = _.get(userContext, "userProfile.city.countryId", null)
            const listingBrand: ListingBrandIdType  | "CULT_BIKE" = cartReviewPayload.option.listingBrand ? cartReviewPayload.option.listingBrand : "EAT_FIT"
            const isCultBikeFlow = (listingBrand as string) == "CULT_BIKE"
            const isCafeFlow = (listingBrand as string) == "EAT_FIT"
            const deliveryTip: number = _.get(cartReviewPayload, "deliveryTip.amount")
            const tenant: Tenant = AppUtil.getTenantFromReq(req)
            if (isCultBikeFlow) {
                cartReviewPayload.useFitCash = false
            }
            if (countryId !== "IN") {
                cartReviewPayload.useFitCash = false
            }
            if (_.isEmpty(cartReviewPayload.orderProducts)) {
                throw this.errorFactory.withCode(ErrorCodes.CART_EMPTY_ERR, 400).withDebugMessage("No items in cart").build()
            }
            if (!MealUtil.isFoodPackBundleSupported(userContext) || cartReviewPayload.option.orderId) {
                cartReviewPayload.option.deliveryInfo = [{ deliverySlot: cartReviewPayload.option.deliverySlot, mealSlot: cartReviewPayload.option.mealSlot, addressId: cartReviewPayload.option.addressId, weekendAddressId: cartReviewPayload.option.weekendAddressId, deliveryWindow: !_.isEmpty(cartReviewPayload.option.deliveryInfo) ? cartReviewPayload.option.deliveryInfo[0].deliveryWindow : undefined }]
            }
            let cartAddressId
            const orderProductMap = await this.catalogueService.getProductMap(_.map(cartReviewPayload.orderProducts, (orderProduct) => { return orderProduct.productId }))
            const fitClubProduct = _.find(cartReviewPayload.orderProducts, (orderProduct) => { return orderProductMap[orderProduct.productId].productType === "FIT_CLUB_MEMBERSHIP" })
            const isOnlyFitClubInCart = !_.isNil(fitClubProduct) && cartReviewPayload.orderProducts.length === 1
            if (!_.isEmpty(cartReviewPayload.option.deliveryInfo) && cartReviewPayload.option.deliveryInfo[0].addressId && !isCafeFlow) {
                cartAddressId = cartReviewPayload.option.deliveryInfo[0].addressId
                const updateBrowseLocation = await this.userBusiness.updateBrowseLocation(req.session, {
                    addressId: cartReviewPayload.option.deliveryInfo[0].addressId,
                    shouldUpdateSession: isCultBikeFlow ? true : undefined,
                    listingBrand: isCultBikeFlow ? listingBrand : undefined
                }, tenant)
                // Added this since session is updated only in next api call, so manually updating the city for cult bike flow on update browse location
                if (updateBrowseLocation?.city?.cityId !== userContext.userProfile.cityId && isCultBikeFlow) {
                    userContext.userProfile.city = updateBrowseLocation.city
                    userContext.userProfile.cityId = updateBrowseLocation.city.cityId
                }
            }
            const userPromise = this.userService.getUser(userContext.userProfile.userId)
            const preferredLocationPromise = isCultBikeFlow ? undefined : this.userBusiness.getPreferredLocation(userContext, userContext.userProfile.userId, userContext.sessionInfo.sessionData, longitude, latitude, cartReviewPayload.option.mealSlot, isPack, cartReviewPayload.option.listingBrand)
            const preferredLocation = preferredLocationPromise ? await preferredLocationPromise : undefined
            if (!isOnlyFitClubInCart && !cartReviewPayload.option.orderId && !cartAddressId) {
                if (preferredLocation?.address) {
                    this.logger.info("Using address store in session as it's not passed")
                    cartReviewPayload.option.deliveryInfo[0].addressId = preferredLocation.address.addressId
                } else {
                    throw this.errorFactory.withCode(ErrorCodes.NO_ADDRESS_ON_CHECKOUT_ERR, 500).withDebugMessage("No address on record").build()
                }
            }
            const user = await userPromise
            const result = AppUtil.getUserAlertInfo(user, userContext)
            const code: string = result.code
            if (!_.isEmpty(code)) {
                throw this.errorFactory.withCode(result.code, result.alertInfo.statusCode).build()
            }
            try {
                let order: BaseOrder = undefined
                let isSubscriptionAddonPack = false
                if (!_.isEmpty(cartReviewPayload.orderProducts)) {
                    const orderProduct = cartReviewPayload.orderProducts[0]
                    const fulfilmentId = _.get(orderProduct, "option.fulfilmentId")
                    const orderId = _.get(orderProduct, "option.orderId")
                    isSubscriptionAddonPack = isPack && !_.isNil(fulfilmentId)
                    cartReviewPayload.option.fulfilmentId = fulfilmentId
                    if (!_.isNil(orderId)) {
                        cartReviewPayload.option.orderId = orderId
                    }
                }
                if (cartReviewPayload.option.orderId || isSubscriptionAddonPack) {
                    /*
                     * Add tip component if required for suborder
                     */
                    throw this.errorFactory.withCode(ErrorCodes.CALL_NOT_ALLOWED, 400).build()
                    // const createSubOrderPayload = await this.getAlfredSuborderPayload(user, userContext.sessionInfo.deviceId, orderSource, cartReviewPayload)
                    // order = await this.orderService.createSubOrderV2(createSubOrderPayload)
                } else {
                    const isCafe = preferredLocation?.address && preferredLocation?.address?.kioskType === "CAFE"
                    let kioskId: string
                    if (isCafe) {
                        kioskId = preferredLocation?.address?.kioskId
                    }
                    let createOrderPayload: OrderCreate
                    let checkoutRequest: OrderCheckoutRequest
                    if (listingBrand === "FOOD_MARKETPLACE") {
                        // creating a separate payload for foodmp barred of some checks
                        createOrderPayload = await FoodMarketplaceUtil.generateAlfredRequest(this.generateReviewRequest(userContext, orderSource, session, cartReviewPayload, latitude, longitude, preferredLocation, false, false), userContext)
                    } else if ((listingBrand as string) === "CULT_BIKE") {
                        createOrderPayload =  await this.getCultBikeAlfredCreateOrderPayload(userContext, user, preferredLocation, userContext.sessionInfo.deviceId, orderSource, cartReviewPayload, userContext.userProfile.cityId, isOnlyFitClubInCart, isCafe, null, listingBrand, deliveryTip, cartReviewPayload.utm)
                    } else {
                        if (!isCafe) {
                            createOrderPayload = await this.getAlfredCreateOrderPayload(userContext, user, preferredLocation, userContext.sessionInfo.deviceId, orderSource, cartReviewPayload, userContext.userProfile.cityId, isOnlyFitClubInCart, isCafe, null, listingBrand, deliveryTip, cartReviewPayload.utm)
                        }
                    }
                    checkoutRequest = this.getOMSOrderCheckoutPayload(
                        userContext,
                        user,
                        userContext.sessionInfo.deviceId,
                        orderSource,
                        cartReviewPayload,
                        userContext.userProfile.cityId,
                        isCafe,
                        cartReviewPayload.utm,
                        kioskId
                    )
                    if (!isCafe) {
                        createOrderPayload.osName = userContext.sessionInfo.osName
                        createOrderPayload.appVersion = (userContext.sessionInfo.appVersion ?? 0).toString()
                    }
                    checkoutRequest.osName = userContext.sessionInfo.osName
                    checkoutRequest.appVersion = (userContext.sessionInfo.appVersion ?? 0).toString()
                    this.logger.info(`Alfred createOrderPayload for ${listingBrand}: ${JSON.stringify(createOrderPayload)}`)
                    if (isCafe) {
                        const checkoutResponse: OrderCheckoutResponse = await this.omsApiClient.checkoutOrder(checkoutRequest)
                        order = checkoutResponse.order
                    } else {
                        createOrderPayload.omsCheckoutRequest = checkoutRequest
                        order = await this.omsApiClient.createOrder(createOrderPayload)
                    }
                }
                const orderCheckoutObj = await this.orderViewBuilder.buildView({
                    userContext: userContext,
                    order: order,
                    product: order.productSnapshots[0],
                    listingBrand: listingBrand
                })
                if (!_.isNil(cartReviewPayload.option.payFromPaytm) && cartReviewPayload.option.payFromPaytm === true) {
                    const orderAfterPayment = await this.omsApiClient.paytmWithdraw(userContext.userProfile.userId, order.orderId)
                    const apiKey: string = req.headers["api-key"] as string || req.headers["apikey"] as string
                    const params: ConfirmationRequestParams = {
                        orderSource: AppUtil.callSource(apiKey),
                        userContext: userContext
                    }
                    if (OrderUtil.isNewOrderConfirmationScreenSupported(userContext.sessionInfo.osName, userContext.sessionInfo.appVersion, userContext.sessionInfo.cpVersion)) {
                        return this.orderConfirmationViewBuilderV1.buildOrderConfirmationView(userContext, orderAfterPayment, params)
                    }
                    return this.orderConfirmationViewBuilder.buildOrderConfirmationView(userContext, orderAfterPayment, params)
                } else
                    return orderCheckoutObj
            } catch (err) {
                if (err.statusCode && err.statusCode === 410) {
                    const userError = Object.assign({}, err)
                    userError.title = "Sorry"
                    userError.subTitle = "This item is not available right now"
                    throw userError

                } else {
                    throw err
                }
            }
        }

        @httpPost("/live/checkout")
        async cartLiveCheckout(req: express.Request): Promise<OrderCheckoutDetail | CartReviewView | ConfirmationView> {
            const session: Session = req.session
            const cartReviewPayload: CartReviewPayload = req.body
            // Parsing the quantity field to a number
            cartReviewPayload.orderProducts.forEach(orderProduct => {
                if (typeof orderProduct.quantity === "string") {
                    orderProduct.quantity = parseInt(orderProduct.quantity, 10)
                }
            })
            if (_.isEmpty(cartReviewPayload.orderProducts)) {
                throw this.errorFactory.withCode(ErrorCodes.CART_EMPTY_ERR, 400).withDebugMessage("No items in cart").build()
            }
            let totalNumberOfProducts = 0
            cartReviewPayload.orderProducts.forEach(orderProduct => {
                totalNumberOfProducts = totalNumberOfProducts + orderProduct.quantity
            })
            if (totalNumberOfProducts > 10) {
                throw this.errorFactory.withCode(ErrorCodes.MAX_ITEMS_IN_CART_ERR, 400).withMeta({ maxItems: 10 }).withDebugMessage(`Only 10 items can be added in cart`).build()
            }
            const userContext: UserContext = req.userContext as UserContext
            userContext.userProfile.promiseMapCache ??= new PromiseCache(this.interfaces)
            const apiKey: string = req.headers["apikey"] as string
            const orderSource: OrderSource = AppUtil.callSource(apiKey)
            const userPromise = this.userService.getUser(userContext.userProfile.userId)
            const listingBrand: ListingBrandIdType = cartReviewPayload.option.listingBrand ? cartReviewPayload.option.listingBrand : "EAT_FIT"
            const user = await userPromise
            const result = AppUtil.getUserAlertInfo(user, userContext)
            const code: string = result.code
            if (!_.isEmpty(code)) {
                throw this.errorFactory.withCode(result.code, result.alertInfo.statusCode).build()
            }
            const productId = cartReviewPayload.orderProducts[0].productId
            const trialPack = await this.interfaces.diyService.getTrialCFLiveProduct(userContext.userProfile.userId, AppUtil.getRequestSource(userContext), AppUtil.getTenantFromUserContext(userContext))
            if (trialPack.productId === productId) {
                const isEligibleForTrial = await this.interfaces.diyService.isEligibleForTrial(userContext.userProfile.userId, AppUtil.getTenantFromUserContext(userContext))
                if (!isEligibleForTrial) {
                    return undefined
                }
                if (AppUtil.isWeb(userContext) || userContext.sessionInfo.clientVersion < 9.14) {
                    // disable the whatsapp flag
                    this.logger.info("calling API to disable nudges flag for userId: ", userContext.userProfile.userId)
                    try {
                        const res = await this.cfAPIJavaService.disableNudgesFlagForFitness(userContext)
                    } catch (error) {
                        this.logger.error("disabling nudges for fitness failed for userId  " + userContext.userProfile.userId + " with error: ", error)
                    }
                }
            }
            const useFitCash: boolean = req.body.useFitCash ? req.body.useFitCash : false
            const advertiserId: string = req.body.advertiserId
            try {
                let order: BaseOrder = undefined
                const orderCreate: OrderCreate = {
                    userId: session.userId,
                    deviceId: session.deviceId,
                    products: cartReviewPayload.orderProducts,
                    source: orderSource,
                    useOffersV2: true,
                    dontCreateRazorpayOrder: true,
                    cityId: session.sessionData.cityId,
                    useFitCash: useFitCash,
                    offersVersion: 3,
                    tenant: AppUtil.getTenantFromUserContext(userContext),
                    osName: userContext.sessionInfo.osName,
                    appVersion: (userContext.sessionInfo.appVersion ?? 0).toString()
                }
                const checkoutRequest: OrderCheckoutRequest = {
                    userId: session.userId,
                    deviceId: session.deviceId,
                    orderProducts: cartReviewPayload.orderProducts,
                    source: orderSource,
                    cityId: session.sessionData.cityId,
                    useFitCash: useFitCash,
                    tenant: AppUtil.getTenantFromUserContext(userContext),
                    osName: userContext.sessionInfo.osName,
                    advertiserId: advertiserId,
                    userAddress: undefined,
                    appVersion: (userContext.sessionInfo.appVersion ?? 0).toString()
                }
                const addressId: string = orderCreate.products[0].option.addressId
                if (addressId) {
                    orderCreate.address = await this.userBusiness.getAddress(session.userId, addressId)
                    checkoutRequest.userAddress = orderCreate.address
                }
                if (!_.isNil(advertiserId)) {
                    orderCreate.advertiserId = advertiserId
                }
                if (await this.orderViewBuilder.isOMSCheckoutFlow(userContext?.userProfile?.userId, userContext?.sessionInfo?.deviceId, cartReviewPayload.orderProducts[0], "CHECKOUT", AppUtil.getTenantFromUserContext(userContext))) {
                    const checkoutResponse: OrderCheckoutResponse = await this.omsApiClient.checkoutOrder(checkoutRequest)
                    order = checkoutResponse.order
                } else {
                    order = await this.omsApiClient.createOrder(orderCreate)
                }
                const orderCheckoutObj = await this.orderViewBuilder.buildView({
                    userContext: userContext,
                    order: order,
                    product: order.productSnapshots[0],
                    listingBrand: listingBrand
                })
                if (!_.isNil(cartReviewPayload.option.payFromPaytm) && cartReviewPayload.option.payFromPaytm === true) {
                    const orderAfterPayment = await this.omsApiClient.paytmWithdraw(userContext.userProfile.userId, order.orderId)
                    const apiKey: string = req.headers["api-key"] as string || req.headers["apikey"] as string
                    const params: ConfirmationRequestParams = {
                        orderSource: AppUtil.callSource(apiKey),
                        userContext: userContext
                    }
                    if (OrderUtil.isNewOrderConfirmationScreenSupported(userContext.sessionInfo.osName, userContext.sessionInfo.appVersion, userContext.sessionInfo.cpVersion)) {
                        return this.orderConfirmationViewBuilderV1.buildOrderConfirmationView(userContext, orderAfterPayment, params)
                    }
                    return this.orderConfirmationViewBuilder.buildOrderConfirmationView(userContext, orderAfterPayment, params)
                } else
                    return orderCheckoutObj
            } catch (err) {
                const error: ErrorBase = <ErrorBase>err
                if (error.responseCode && error.responseCode === 410) {
                    const userError = Object.assign({}, err)
                    userError.title = "Sorry"
                    userError.subTitle = "This item is not available right now"
                    throw userError
                } else {
                    throw err
                }
            }
        }

        @httpPost("/marketplace/checkout")
        async cartMarketplaceCheckout(req: express.Request): Promise<OrderCheckoutDetail | CartReviewView | ConfirmationView> {
            const session: Session = req.session
            const cartReviewPayload: CartReviewPayload = req.body
            if (_.isEmpty(cartReviewPayload.orderProducts)) {
                throw this.errorFactory.withCode(ErrorCodes.CART_EMPTY_ERR, 400).withDebugMessage("No items in cart").build()
            }
            const userContext: UserContext = req.userContext as UserContext
            const latitude: number = req.headers["lat"] ? Number(req.headers["lat"]) : undefined
            const longitude: number = req.headers["lon"] ? Number(req.headers["lon"]) : undefined
            const apiKey: string = req.headers["apikey"] as string
            const orderSource: OrderSource = AppUtil.callSource(apiKey)
            const listingBrand: ListingBrandIdType = cartReviewPayload.option.listingBrand ? cartReviewPayload.option.listingBrand : "EAT_FIT"
            const tenant: Tenant = AppUtil.getTenantFromReq(req)
            if (_.isEmpty(cartReviewPayload.orderProducts)) {
                throw this.errorFactory.withCode(ErrorCodes.CART_EMPTY_ERR, 400).withDebugMessage("No items in cart").build()
            }
            let cartAddressId
            if (!_.isEmpty(cartReviewPayload.option.deliveryInfo) && cartReviewPayload.option.deliveryInfo[0].addressId) {
                cartAddressId = cartReviewPayload.option.deliveryInfo[0].addressId
                await this.userBusiness.updateBrowseLocation(req.session, { addressId: cartReviewPayload.option.deliveryInfo[0].addressId }, tenant)
            }
            const userPromise = this.userService.getUser(userContext.userProfile.userId)
            const preferredLocationPromise = this.userBusiness.getPreferredLocation(userContext, userContext.userProfile.userId, userContext.sessionInfo.sessionData, longitude, latitude, cartReviewPayload.option.mealSlot, false, cartReviewPayload.option.listingBrand)
            const preferredLocation = await preferredLocationPromise
            if (!cartReviewPayload.option.orderId && !cartAddressId) {
                if (preferredLocation.address) {
                    this.logger.info("Using address store in session as it's not passed")
                    cartReviewPayload.option.deliveryInfo[0].addressId = preferredLocation.address.addressId
                } else {
                    throw this.errorFactory.withCode(ErrorCodes.NO_ADDRESS_ON_CHECKOUT_ERR, 500).withDebugMessage("No address on record").build()
                }
            }
            const user = await userPromise
            const result = AppUtil.getUserAlertInfo(user, userContext)
            const code: string = result.code
            if (!_.isEmpty(code))
                throw this.errorFactory.withCode(result.code, result.alertInfo.statusCode).build()
            try {
                const userDetailInfo: UserDetailInfo = {
                    city: userContext.userProfile.city,
                    cityId: userContext.userProfile.cityId,
                    userTimezone: userContext.userProfile.timezone,
                    orderSource: orderSource,
                    userId: userContext.userProfile.userId,
                    deviceId: userContext.sessionInfo.deviceId,
                    userAgent: userContext.sessionInfo.userAgent,
                    osName: userContext.sessionInfo.osName,
                    appVersion: userContext.sessionInfo.appVersion,
                    cpVersion: userContext.sessionInfo.cpVersion
                }
                const request: ReviewRequest = {
                    sessionData: session.sessionData,
                    cartReviewPayload: cartReviewPayload,
                    latitude: latitude,
                    longitude: longitude,
                    userDetailInfo: userDetailInfo,
                    preferredLocation: preferredLocation
                }
                /*
                 * Add tip component if required for marketplace checkout coming from client
                 * const deliveryTip: number = _.get(cartReviewPayload, "deliveryTip.amount")
                 */
                const order = await this.interfaces.eatApiClientService.getMarketplaceCheckoutResponse(request)
                const orderCheckoutObj = await this.orderViewBuilder.buildView({
                    userContext: userContext,
                    order: order,
                    product: order.productSnapshots[0],
                    listingBrand: listingBrand
                })
                if (!_.isNil(cartReviewPayload.option.payFromPaytm) && cartReviewPayload.option.payFromPaytm === true) {
                    const orderAfterPayment = await this.omsApiClient.paytmWithdraw(userContext.userProfile.userId, order.orderId)
                    const apiKey: string = req.headers["api-key"] as string || req.headers["apikey"] as string
                    const params: ConfirmationRequestParams = {
                        orderSource: AppUtil.callSource(apiKey),
                        userContext: userContext
                    }
                    if (OrderUtil.isNewOrderConfirmationScreenSupported(userContext.sessionInfo.osName, userContext.sessionInfo.appVersion, userContext.sessionInfo.cpVersion)) {
                        return this.orderConfirmationViewBuilderV1.buildOrderConfirmationView(userContext, orderAfterPayment, params)
                    }
                    return this.orderConfirmationViewBuilder.buildOrderConfirmationView(userContext, orderAfterPayment, params)
                } else
                    return orderCheckoutObj
            } catch (err) {
                this.logger.error(err)
                this.logger.error("checkout failed - " + JSON.stringify(err))
                if (err.statusCode && err.statusCode === 410) {
                    const userError = Object.assign({}, err)
                    userError.title = "Sorry"
                    userError.subTitle = "This item is not available right now"
                    throw userError

                } else {
                    throw err
                }
            }
        }

        @httpPost("/checkout/v1")
        async cartCheckoutV1(req: express.Request): Promise<OrderCheckoutDetail | CartReviewView | ConfirmationView> {
            const session: Session = req.session
            const cartReviewPayload: CartReviewPayload = req.body
            const userId: string = session.userId
            const userContext: UserContext = req.userContext as UserContext
            userContext.userProfile.promiseMapCache ??= new PromiseCache(this.interfaces)
            const apiKey: string = req.headers["apikey"] as string
            const orderSource: OrderSource = AppUtil.callSource(apiKey)
            const countryId = _.get(userContext, "userProfile.city.countryId", null)
            const tz = userContext.userProfile.timezone
            if (countryId !== "IN" || AppUtil.isSugarFitOrUltraFitApp(userContext)) {
                cartReviewPayload.useFitCash = false
            }
            let useFitCash: boolean = AppUtil.isSugarFitOrUltraFitApp(userContext) ? false : req.body.useFitCash ? req.body.useFitCash : false

            if (_.isEmpty(cartReviewPayload.orderProducts)) {
                throw this.errorFactory.withCode(ErrorCodes.CART_EMPTY_ERR, 400).withDebugMessage("No items in cart").build()
            }
            const user = await this.userService.getUser(userId)
            const result = AppUtil.getUserAlertInfo(user, userContext)
            const code: string = result.code
            if (!_.isEmpty(code))
                throw this.errorFactory.withCode(result.code, result.alertInfo.statusCode).build()
            const product = await this.catalogueServicePMS.getCatalogProduct(cartReviewPayload.orderProducts[0].productId)
            const userCity: UserCity = await LocationUtil.getUserCityFromReq(req, this.cityService, this.logger, null, null)
            let cityId: string = userCity.city.cityId
            const bundleProduct = product.productType === "BUNDLE" ? <DiagnosticProduct>product : undefined
            const isCultLivePT = bundleProduct && (bundleProduct.subCategoryCode === "LIVE_PERSONAL_TRAINING" || bundleProduct.subCategoryCode === "LIVE_SGT" || CareUtil.isTransformTenant(bundleProduct.subCategoryCode)  || bundleProduct.subCategoryCode === "MIND_THERAPY")
            if (isCultLivePT && !cartReviewPayload.orderProducts[0].option.patientId) {
                cartReviewPayload.orderProducts[0].option.patientId = (await CareUtil.getOrCreatePatient(userContext, this.userCache, this.healthfaceService)).id
            }

            const addonProductIdsFromReqBody: string[] = cartReviewPayload?.orderProducts[0]?.addonProductIds
            /*
             * Add tip component if required for marketplace checkout coming from client
             * const deliveryTip: number = _.get(cartReviewPayload, "deliveryTip.amount")
             */
            // Filter for duplicate Products if any
            let uniqueProducts = [...new Map(cartReviewPayload.orderProducts.map(item => [item.productId, item])).values()]
            if (addonProductIdsFromReqBody?.length > 0) {
                uniqueProducts[0].addonProductIds = addonProductIdsFromReqBody
            }
            let centerId = uniqueProducts[0].option.centerId
            let centerServiceCenterId: string
            const isGymPT = product && product.productType === "GYM_PT_PRODUCT"
            const isLux = product && product.productType === "LUX_FITNESS_PRODUCT"
            if (isGymPT) {
                const agentDetails: Doctor = await this.ollivanderService.getDoctorDetails(cartReviewPayload.orderProducts[0].option.doctorId)
                const trainerIdentityId: number = agentDetails.identityId
                const centerServiceResponse: CenterResponse = await this.centerService.getCenterById(Number(centerId))
                const gymCenterId = centerServiceResponse.meta.gymfitCenterId
                const gymPtProduct: GymPtProduct = await this.catalogueService.getGymPtProductById(cartReviewPayload.orderProducts[0].productId)
                let date: string
                if (cartReviewPayload.orderProducts[0].option?.startDate) {
                    date = TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, new Date(cartReviewPayload.orderProducts[0].option?.startDate))
                } else {
                    date = await this.getEarliestStartDateForPtMembership(userContext, gymPtProduct.durationInDays)
                }
                this.logger.info(`Earliest StartDate for PT membership: ${JSON.stringify(date)}`)
                centerId = gymCenterId.toString()
                centerServiceCenterId = centerServiceResponse.id.toString()
                uniqueProducts = uniqueProducts.map(product => {
                    return {
                        ...product,
                        option: {
                            ...product.option,
                            centerId: gymCenterId.toString(),
                            startDate: date,
                            gymPtOrderOptions: {
                                preferredTrainerId: trainerIdentityId.toString()
                            }
                        }
                    }
                })
                // disable fitcash usage for gym pt as payment is breaking for PT product when fitcash is used. Disable till that is fixed.
                useFitCash = false
            }
            if (product && product.productType === "GYMFIT_FITNESS_PRODUCT" && product.isPack) {
                const gymfitFitnessProduct = await this.catalogueServicePMS.getProduct(cartReviewPayload.orderProducts[0].productId)
                if (gymfitFitnessProduct?.product?.productSubType === ProductSubType.ADDONBUNDLE) {
                    const proPlusAddOnIds = gymfitFitnessProduct?.baseAddOns?.map((addOn) => addOn?.packId)
                    if (proPlusAddOnIds?.length > 0) {
                        uniqueProducts[0].addonProductIds = proPlusAddOnIds
                    }
                }
                const centerServiceId = CatalogueServiceUtilities.getAccessLevel(gymfitFitnessProduct) == GymfitAccessLevel.CENTER ? CatalogueServiceUtilities.getExternalAccessLevelId(gymfitFitnessProduct) : uniqueProducts[0].option.centerServiceCenterId
                let date: string
                if (cartReviewPayload.orderProducts[0].option?.startDate) {
                    date = TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, new Date(cartReviewPayload.orderProducts[0].option?.startDate))
                } else {
                    date = await this.getEarliestStartDateForFitnessMembership(userContext, gymfitFitnessProduct.product.durationInDays, centerServiceId, gymfitFitnessProduct.title)
                }
                const options: OrderProductOption = {
                    startDate: date
                }
                const center = await this.centerService.getCenterById(Number(centerServiceId))
                centerId = center?.meta?.gymfitCenterId?.toString()
                options["centerId"] = center?.meta?.gymfitCenterId?.toString()
                options["centerServiceCenterId"] = centerServiceId
                centerServiceCenterId = centerServiceId

                uniqueProducts = uniqueProducts.map(product => {
                    return {
                        ...product,
                        option: options
                    }
                })
                cityId = gymfitFitnessProduct?.clientMetadata?.cityId ?? cityId
            }
            if (isLux) {
                const luxPack: OfflineFitnessPack = await this.offlineFitnessPackService.getCachedPackById(cartReviewPayload.orderProducts[0].productId)

                let date: string
                if (cartReviewPayload.orderProducts[0].option?.startDate) {
                    date = TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, new Date(cartReviewPayload.orderProducts[0].option?.startDate))
                } else {
                    date = await this.getEarliestStartDateForLuxMembership(userContext, luxPack.product.durationInDays, luxPack)
                }
                centerServiceCenterId = luxPack.restrictions?.centers?.[0]?.toString()
                const center = centerServiceCenterId && await this.centerService.getCenterById(Number(centerServiceCenterId))
                centerId = center?.meta?.gymfitCenterId?.toString()
                uniqueProducts = uniqueProducts.map(product => {
                    return {
                        ...product,
                        option: {
                            centerId: centerId,
                            centerServiceCenterId : centerServiceCenterId,
                            startDate: date
                        }
                    }
                })
                cityId = luxPack.clientMetadata?.cityId ?? cityId
            }
            const clientMetadata: CultMembershipMetadata = req.body.clientMetadata ? req.body.clientMetadata : {}
            clientMetadata.subUserId = userContext.userProfile.subUserId ? userContext.userProfile.subUserId : clientMetadata.subUserId

            const createOrderPayload: OrderCreate = {
                userId: session.userId,
                deviceId: session.deviceId,
                products: uniqueProducts,
                useOffersV2: true,
                source: orderSource,
                cityId: cityId,
                dontCreateRazorpayOrder: true,
                useFitCash: useFitCash,
                tenant: AppUtil.getTenantFromUserContext(userContext),
                osName: userContext.sessionInfo.osName,
                appVersion: (userContext.sessionInfo.appVersion ?? 0).toString(),
                clientMetadata: clientMetadata,
            }

            const checkoutRequest: OrderCheckoutRequest = _.cloneDeep({
                userId: session.userId,
                deviceId: session.deviceId,
                orderProducts: uniqueProducts,
                source: orderSource,
                clientMetadata: clientMetadata,
                cityId: cityId,
                useFitCash: useFitCash,
                dontApplyOffers: (clientMetadata as any)?.isUpgradeMembership ?? false,
                tenant: AppUtil.getTenantFromUserContext(userContext),
                osName: userContext.sessionInfo.osName,
                advertiserId: undefined,
                userAddress: undefined,
                appVersion: (userContext.sessionInfo.appVersion ?? 0).toString()
            })

            if (product && product.productType === "FITNESS" && product.isPack) {
                const fitnessProduct: OfflineFitnessPack = await this.catalogueServicePMS.getProduct(product.productId)
                if (fitnessProduct?.product?.productSubType === ProductSubType.ADDONBUNDLE) {
                    const elitePlusAddOnIds = fitnessProduct?.baseAddOns?.map((addOn) => addOn?.packId)
                    if (elitePlusAddOnIds?.length > 0) {
                        createOrderPayload.products[0].addonProductIds = elitePlusAddOnIds
                        checkoutRequest.orderProducts[0].addonProductIds = elitePlusAddOnIds
                    }
                }
                if (createOrderPayload.products[0].option.startDate < TimeUtil.todaysDateWithTimezone(tz)) {
                    createOrderPayload.products[0].option.startDate = TimeUtil.todaysDateWithTimezone(tz)
                    checkoutRequest.orderProducts[0].option.startDate = TimeUtil.todaysDateWithTimezone(tz)
                }

                if (createOrderPayload.products[0].option.centerId) {
                    createOrderPayload.clientMetadata = Object.assign({}, createOrderPayload.clientMetadata, {
                        centerId: createOrderPayload.products[0].option.centerId
                    })
                    checkoutRequest.clientMetadata = Object.assign({}, checkoutRequest.clientMetadata, {
                        centerId: checkoutRequest.orderProducts[0].option.centerId
                    })
                }

                if (createOrderPayload.products[0].option.centerServiceCenterId) {
                    createOrderPayload.clientMetadata = Object.assign({}, createOrderPayload.clientMetadata, {
                        centerServiceCenterId: createOrderPayload.products[0].option.centerServiceCenterId
                    })
                    checkoutRequest.clientMetadata = Object.assign({}, checkoutRequest.clientMetadata, {
                        centerServiceCenterId: checkoutRequest.orderProducts[0].option.centerServiceCenterId
                    })
                }

                const metadata = await this.cultBusiness.getClientMetadataForOrder(userContext, createOrderPayload, product)
                if (metadata) {
                    createOrderPayload.clientMetadata = Object.assign({}, createOrderPayload.clientMetadata, { ...metadata })
                    checkoutRequest.clientMetadata = Object.assign({}, checkoutRequest.clientMetadata, { ...metadata })
                }
                if (!createOrderPayload.products[0].option.centerServiceCenterId && createOrderPayload.products[0].option.centerId) {
                    let centerId = createOrderPayload.products[0].option.centerId
                    // for upgrade orders
                    if (createOrderPayload.clientMetadata && isMembershipUpgradeClientMetadata(createOrderPayload.clientMetadata)) {
                        centerId = (createOrderPayload.clientMetadata as CultMindMembershipUpgradeMetadata).cultCenter.id.toString()
                    }
                    const cultCenter = await this.catalogueService.getCultCenter(centerId)
                    createOrderPayload.products[0].option.centerServiceCenterId = cultCenter.centerServiceId
                    checkoutRequest.orderProducts[0].option.centerServiceCenterId = cultCenter.centerServiceId
                }
                cityId = fitnessProduct?.clientMetadata?.cityId ?? cityId
                checkoutRequest.cityId = cityId
                createOrderPayload.cityId = cityId
            }

            if (!_.isNil(cartReviewPayload.advertiserId)) {
                createOrderPayload.advertiserId = cartReviewPayload.advertiserId
                checkoutRequest.advertiserId = cartReviewPayload.advertiserId
            }
            if (!_.isEmpty(cartReviewPayload.careOptions)) {
                createOrderPayload.orderCreationMeta = { careMeta: { diagnosticCartId: cartReviewPayload.careOptions.diagnosticCartId }}
                checkoutRequest.orderCreationMeta = { careMeta: { diagnosticCartId: cartReviewPayload.careOptions.diagnosticCartId }}
            }
            const addressid: string = createOrderPayload.products[0].option.addressId
            if (addressid) {
                const userAddress = await this.userBusiness.getAddress(userId, addressid)
                if (["SF_CONSUMABLE"].includes(product.productType) && _.isEmpty(userAddress?.structuredAddress?.city)) {
                    return {
                        widgets: [],
                        orderMeta: null,
                        alertInfo: {
                            title: "Issue with address!",
                            subTitle: "We were unable to fetch CITY from your address. Kindly change/re-add the address again.",
                            actions: [{title: "ADD ADDRESS", actionType: "NAVIGATION", url: "curefit://selectaddress"}]
                        }
                    }
                } else {
                    createOrderPayload.address = userAddress
                    checkoutRequest.userAddress = userAddress
                }
            }
            if (centerId) {
                createOrderPayload.clientMetadata = Object.assign({}, createOrderPayload.clientMetadata, {
                    centerId: centerId
                })
                checkoutRequest.clientMetadata = Object.assign({}, checkoutRequest.clientMetadata, {
                    centerId: centerId
                })
            }
            if (centerServiceCenterId) {
                createOrderPayload.clientMetadata = Object.assign({}, createOrderPayload.clientMetadata, {
                    centerServiceCenterId: centerServiceCenterId
                })
                checkoutRequest.clientMetadata = Object.assign({}, checkoutRequest.clientMetadata, {
                    centerServiceCenterId: centerServiceCenterId
                })
            }
            if (["BUNDLE", "SF_CONSUMABLE", "CONSULTATION", "DIAGNOSTICS", "FITNESS", "MIND", "FOOD_MARKETPLACE", "GYM_PT_PRODUCT", "LUX_FITNESS_PRODUCT"].includes(product.productType)) {
                createOrderPayload.offersVersion = 3
            }
            if (AppUtil.isSugarFitOrUltraFitApp(userContext)) {
                if (product.productType === "DEVICE") {
                    createOrderPayload.offersVersion = 3
                    createOrderPayload.products.forEach(item => item.option.externalOrderInfo = {
                        "extPlatformChannel": "SELF"
                    })
                }
                const amountPayableOverride = await this.getSugarfitPriceOverride(userId, createOrderPayload, req)
                if (amountPayableOverride) {
                    const createOrderInternal: OrderCreateInternal = {
                        ...createOrderPayload,
                        protectedParams : {
                            amountPayableOverride : amountPayableOverride
                        }
                    }
                    this.logger.info(`Alfred createOrderPayload : ${JSON.stringify(createOrderInternal)}`)
                    const order = await this.omsApiClient.createInternalOrder(createOrderInternal)
                    return await this.orderViewBuilder.buildView({
                        userContext: userContext,
                        order: order,
                        product: order.productSnapshots[0]
                    })
                }
            }
            createOrderPayload.omsCheckoutRequest = checkoutRequest
            await this.logger.info("Checkout Request : ", {checkoutRequest})
            await this.logger.info("Create Order Payload : ", {createOrderPayload})
            let order
            if (await this.orderViewBuilder.isOMSCheckoutFlow(session.userId, session.deviceId, product, "CHECKOUT", AppUtil.getTenantFromUserContext(userContext))) {
                const checkoutResponse: OrderCheckoutResponse = await this.omsApiClient.checkoutOrder(checkoutRequest)
                order = checkoutResponse.order
            } else {
               order = await this.omsApiClient.createOrder(createOrderPayload)
            }
            return await this.orderViewBuilder.buildView({
                userContext: userContext,
                order: order,
                product: order.productSnapshots[0]
            })
        }

        @httpGet("/")
        async cartDetail(req: express.Request): Promise<ProductDetailPage> {
            const userContext: UserContext = req.userContext as UserContext
            const fulfilmentId: string = req.query.fulfilmentId
            const date: string = req.query.date
            const listingBrand: string = req.query.listingBrand
            const marketplaceOrderId: string = req.query.marketplaceOrderId
            const orderId: string = req.query.orderId
            const order: Order = await this.omsApiClient.getOrder(orderId)
            if (order.userId !== req.session.userId) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_AUTHORIZED_TO_VIEW_ORDER, 401).withDebugMessage("Not authorized to view order").build()
            }
            let feedback

            if (listingBrand === "FOOD_MARKETPLACE") {

                const [order, foodMarketplaceBooking] = await Promise.all([
                    this.omsApiClient.getOrder(orderId),
                    this.foodwayService.getUpcomingScreenDetails(marketplaceOrderId)
                ])
                if (foodMarketplaceBooking.status === ShipmentStatus.DELIVERED) {
                    feedback = await this.feedbackDao.findOne({ orderId: orderId })
                }
                return this.cartViewBuilder.buildFoodMarketplaceCartDetailView(order, userContext, foodMarketplaceBooking, date, feedback)
            }
            let foodBooking: FoodBooking
            if (date) {
                foodBooking = await this.shipmentService.getFoodShipment(userContext.userProfile.userId, fulfilmentId, date)
                this.logger.info("[ALFRED_SUNSET] /shipment/foodShipment CartController", {foodBooking})
            } else {
                const shipmentsPromise = this.shipmentService.getFoodShipmentsByFulfilmentId(fulfilmentId)
                const bookingResult: { booking?: FoodPackBooking, menu: { [date: string]: string } } = await shipmentsPromise
                foodBooking = this.getFoodBooking(bookingResult)
            }
            if (foodBooking && foodBooking.state === "DELIVERED") {
                feedback = await this.feedbackDao.findOne({ shipmentId: foodBooking.shipmentId })
            }
            return this.cartViewBuilder.buildCartDetailView(foodBooking, order, req.userContext as UserContext, undefined, feedback, this.feedbackPageConfigV2Cache)
        }

        private getFoodBooking(bookingResult: { booking?: FoodPackBooking, menu: { [date: string]: string } }): FoodBooking {
            let foodBooking: FoodBooking = undefined
            if (bookingResult && bookingResult.booking) {
                const foodPackBooking: FoodPackBooking = bookingResult.booking
                if (foodPackBooking.activeShipment) {
                    foodBooking = foodPackBooking.activeShipment
                }
                if (!foodBooking && foodPackBooking.futureShipment) {
                    foodPackBooking.futureShipment.forEach(booking => {
                        foodBooking = booking
                    })
                }
                if (!foodBooking && foodPackBooking.pastShipment) {
                    foodPackBooking.pastShipment.forEach(booking => {
                        foodBooking = booking
                    })
                }
            }
            return foodBooking
        }
        private async getEarliestStartDateForPtMembership(userContext: UserContext, durationInDays: number) {
            const earlistStartTime = await this.membershipService.getEarliestStartDate(userContext?.userProfile?.userId, Tenant.CUREFIT_APP, GYM_PT_MEMBERSHIPS_PRIMARY_BENEFITS, durationInDays * 86400)
            const earliestStartDate = moment(earlistStartTime.start).tz(userContext.userProfile.timezone).format("YYYY-MM-DD")
            this.logger.info("membership start Date: " + earliestStartDate)
            return earliestStartDate
        }

        private async getEarliestStartDateForLuxMembership(userContext: UserContext, durationInDays: number, luxPack: OfflineFitnessPack) {
            const luxGxBenefit: BenefitEntry = luxPack.product.benefits.find((item) => item.name.toUpperCase() === "LUX_GX")
            const earlistStartTime = !_.isNil(luxGxBenefit)
                ? await this.membershipService.getEarliestStartDate(userContext?.userProfile?.userId, Tenant.CUREFIT_APP, LUX_MEMBERSHIPS_ALLOWOVERLAP_BENEFITS, durationInDays * 86400)
                : await this.membershipService.getEarliestStartDate(userContext?.userProfile?.userId, Tenant.CUREFIT_APP, LUX_MEMBERSHIPS_PRIMARY_BENEFITS, durationInDays * 86400)
            let earliestStartDate: string = moment(earlistStartTime.start).tz(userContext.userProfile.timezone).format("YYYY-MM-DD")
            const centerServiceId = luxPack.restrictions?.centers?.[0]
            let centerResponse: CenterResponse
            if (centerServiceId) {
                centerResponse = await this.centerService.getCenterById(Number(centerServiceId))
                if (centerResponse) {
                    const centerLaunchDate = centerResponse.launchDate
                    if (!_.isNil(centerLaunchDate)) {
                        const centerLaunchDateString = moment(centerLaunchDate).tz(userContext.userProfile.timezone).format("YYYY-MM-DD")
                        earliestStartDate = moment(centerLaunchDateString) > moment(earliestStartDate) ? centerLaunchDateString : earliestStartDate
                    }
                }
            }
            this.cfAnalytics.sendEventFromUserContext(<PackStartDateAnalyticsEvent> {
                analyticsEventName: AnalyticsEventName.PACK_START_DATE,
                from: "CartController.getEarliestStartDateForLuxMembership",
                packDuration: durationInDays * 86400,
                packName: luxPack?.product?.title,
                productId: luxPack?.id,
                finalEarliestStartDate: moment(earliestStartDate).tz(userContext?.userProfile?.timezone).format("YYYY-MM-DD"),
                previousMembEndDate: moment(earliestStartDate).tz(userContext?.userProfile?.timezone).format("YYYY-MM-DD"),
            }, userContext, false, true, true, false)
            this.logger.info("lux membership start Date: " + earliestStartDate)
            return earliestStartDate
        }

        private async getEarliestStartDateForFitnessMembership(userContext: UserContext, durationInDays: number, centerServiceId: number | string, packName: string) {
            return await FitnessUtil.getEarliestStartDateForFitnessMembership(userContext, durationInDays, centerServiceId, packName, this.membershipService, this.centerService, this.logger, this.cfAnalytics)
        }

        private getFirstAvailableStartDate(areaTz: Timezone, inventory: { [date: string]: string[] }, isWeekendEnabled: boolean, isPack: boolean) {
            let firstAvailableDate: string = null
            Object.keys(inventory).forEach(date => {
                if ((!isWeekendEnabled && isPack && !TimeUtil.isWeekDay(date, undefined, areaTz)) || _.isNil(inventory[date]) || inventory[date].length === 0) {
                    delete inventory[date]
                } else {
                    if (!firstAvailableDate || firstAvailableDate > date) {
                        firstAvailableDate = date
                    }
                }
            })
            this.logger.debug("default start date : " + firstAvailableDate)
            return firstAvailableDate
        }

        private async addDefaultAddressIdIfNeeded(cartReviewPayload: CartReviewPayload, preferredLocation: PreferredLocation) {
            if (!cartReviewPayload.option.addressId) {
                cartReviewPayload.option.deliveryInfo[0].addressId = preferredLocation.address ? preferredLocation.address.addressId : undefined
            }
        }

        private addDefaultSlotIfNeeded(mealSlotDeliverySlots: { [p: string]: string[] }, cartReviewPayload: CartReviewPayload, isPackBundle: boolean, isOnlyFitClubInCart: boolean, dateWiseAvailableSlots: { [date: string]: string[] }, listingBrand: ListingBrandIdType) {
            let isDefaultSlotAdded = false
            if (isOnlyFitClubInCart)
                return isDefaultSlotAdded
            if (isPackBundle) {
                const mealSlots = _.keys(mealSlotDeliverySlots)
                _.forEach(mealSlots, (mealSlot) => {
                    if (cartReviewPayload.option.deliveryInfo) {
                        const index = cartReviewPayload.option.deliveryInfo.findIndex((deliveryInfo) => {
                            return deliveryInfo.mealSlot === mealSlot
                        })
                        if (index === -1) {
                            cartReviewPayload.option.deliveryInfo.push({
                                mealSlot: <MenuType>mealSlot,
                                deliverySlot: mealSlotDeliverySlots[mealSlot][0]
                            })
                            isDefaultSlotAdded = true
                        }
                    } else {
                        cartReviewPayload.option.deliveryInfo = [{
                            mealSlot: <MenuType>mealSlot,
                            deliverySlot: mealSlotDeliverySlots[mealSlot][0]
                        }]
                        isDefaultSlotAdded = true
                    }
                })
            } else {
                // Select next day delivery Slot for whole_fit
                if (listingBrand === "WHOLE_FIT" && _.isNil(cartReviewPayload.option.deliveryInfo[0].deliverySlot)) {
                    const days = _.keys(dateWiseAvailableSlots)
                    for (const day of days) {
                        if (!_.isEmpty(dateWiseAvailableSlots[day])) {
                            cartReviewPayload.option.deliveryInfo[0].deliverySlot = dateWiseAvailableSlots[day][0]
                            cartReviewPayload.option.date = day
                            isDefaultSlotAdded = true
                            break
                        }
                    }
                } else if (_.isNil(cartReviewPayload.option.deliveryInfo[0].deliverySlot)) {
                    cartReviewPayload.option.deliveryInfo[0].deliverySlot = mealSlotDeliverySlots[cartReviewPayload.option.mealSlot][0]
                    isDefaultSlotAdded = true
                }
            }
            return isDefaultSlotAdded
        }

        private async getAlfredSuborderPayload(user: User, deviceId: string, source: OrderSource, cartReviewPayload: CartReviewPayload): Promise<SubOrderCreateV2> {
            const orderProducts: OrderProduct[] = _.map(cartReviewPayload.orderProducts, cartProduct => {
                const orderProduct: OrderProduct = {
                    productId: cartProduct.productId,
                    quantity: cartProduct.quantity,
                    option: {}
                }
                if (cartReviewPayload.option) {
                    if (!cartProduct.option) {
                        cartProduct.option = {}
                    }
                    orderProduct.option.isChangedMeal = cartProduct.option.isChangedMeal
                    orderProduct.option.subscriptionType = cartProduct.option.subscriptionType
                    orderProduct.option.weekendEnabled = cartProduct.option.weekendEnabled
                    // Temp hack as website is passing offer id as empty
                    orderProduct.option.offerId = !_.isEmpty(cartProduct.option.offerId) ? cartProduct.option.offerId : undefined
                    orderProduct.option.startDate = cartReviewPayload.option.date
                    orderProduct.option.deliverySlot = cartReviewPayload.option.deliverySlot
                    orderProduct.option.numDays = cartProduct.option.numDays
                    orderProduct.option.numTickets = cartProduct.option.numTickets
                    orderProduct.option.isPack = cartProduct.option.isPack
                    orderProduct.option.packId = cartProduct.option.packId
                }
                if (orderProduct.option.offerId) {
                    orderProduct.option.offerV2Ids = [orderProduct.option.offerId]
                }
                return orderProduct
            })
            const orderCreate: SubOrderCreateV2 = {
                userId: user.id,
                deviceId: deviceId,
                orderId: cartReviewPayload.option.orderId,
                fulfilmentId: cartReviewPayload.option.fulfilmentId,
                products: orderProducts,
                source: source,
                useOffersV2: true,
                dontCreateRazorpayOrder: true,
                useFitCash: cartReviewPayload.useFitCash ? cartReviewPayload.useFitCash : false
            }
            if (!_.isNil(cartReviewPayload.advertiserId)) orderCreate.advertiserId = cartReviewPayload.advertiserId
            return orderCreate
        }


        private async getAlfredCreateOrderPayload(userContext: UserContext, user: User, preferredLocation: PreferredLocation, deviceId: string, source: OrderSource,
            cartReviewPayload: CartReviewPayload, cityId: string, isOnlyFitClubInCart?: boolean, isCafe?: boolean, subPacks?: FoodPack[],
            listingBrand?: ListingBrandIdType, deliveryTip?: number, utm?: any): Promise<OrderCreate> {
            const isPack = cartReviewPayload.orderProducts[0].option.isPack ? true : false
            const deliveryInfoPromises = _.map(cartReviewPayload.option.deliveryInfo, (deliveryInfo) => {
                if ((listingBrand as string) === "CULT_BIKE") {
                    // added to avoid cultlery instructions
                } else if (_.isNil(deliveryInfo.cutleryInstruction) && listingBrand !== "WHOLE_FIT") {
                    deliveryInfo.cutleryInstruction = "GIVE_CUTLERY"
                } else if (listingBrand === "WHOLE_FIT") {
                    deliveryInfo.cutleryInstruction = undefined
                }
                if (deliveryInfo.isCopySelected === true) {
                    const copyInfo = _.find(cartReviewPayload.option.deliveryInfo, (info) => {
                        return deliveryInfo.mealSlot === "SNACKS" ? info.mealSlot === "LUNCH" : info.mealSlot === "BREAKFAST"
                    })
                    if (copyInfo) {
                        deliveryInfo.addressId = copyInfo.addressId
                        deliveryInfo.weekendAddressId = copyInfo.weekendAddressId
                    }
                }
                const weekdayAddressPromise = deliveryInfo.addressId ? this.userBusiness.getAreaAndAddressByAddressId(deliveryInfo.addressId, user.id, deliveryInfo.mealSlot, isPack, listingBrand) : undefined
                const weekendAddressPromise = deliveryInfo.weekendAddressId ? this.userBusiness.getAreaAndAddressByAddressId(deliveryInfo.weekendAddressId, user.id, deliveryInfo.mealSlot, isPack, listingBrand) : undefined
                return {
                    mealSlot: deliveryInfo.mealSlot,
                    deliverySlot: deliveryInfo.deliverySlot,
                    addressPromise: weekdayAddressPromise,
                    weekendAddressPromise: weekendAddressPromise,
                    cutleryInstruction: deliveryInfo.cutleryInstruction,
                }
            })
            const infoPromises = _.map(deliveryInfoPromises, async (info, index) => {
                const result = await info.addressPromise
                const weekendResult = await info.weekendAddressPromise
                let weekendAddress
                const userDeliveryInstruction = _.get(cartReviewPayload, "option.eatDeliveryInstruction")
                let defaultDeliveryInstruction: DeliveryInstruction
                if (userDeliveryInstruction) {
                    defaultDeliveryInstruction = {
                        dropInstruction: userDeliveryInstruction.dropInstruction,
                        contactInstruction: userDeliveryInstruction.contactInstruction,
                        cutleryInstruction: info.cutleryInstruction
                    }
                } else {
                    defaultDeliveryInstruction = {
                        dropInstruction: "ME",
                        contactInstruction: "CALL_ME",
                        cutleryInstruction: info.cutleryInstruction
                    }
                }
                let address: UserDeliveryAddress = {
                    addressId: "DUMMY",
                    areaId: subPacks ? "1" : preferredLocation.area ? preferredLocation.area.areaId : preferredLocation.defaultArea.areaId,
                    kioskId: preferredLocation.address ? preferredLocation.address.kioskId : undefined,
                    addressLine1: isOnlyFitClubInCart ? "-" : "",
                    addressLine2: isOnlyFitClubInCart ? "-" : "",
                    name: isOnlyFitClubInCart ? user.firstName + " " + user.lastName : "",
                    userId: user.id,
                    phoneNumber: isOnlyFitClubInCart ? user.phone : undefined,
                    latLong: preferredLocation.latLong ? [preferredLocation.latLong.long, preferredLocation.latLong.lat] : [0, 0],
                    eatDeliveryInstruction: defaultDeliveryInstruction
                }
                if (result && result.address) {
                    const areaId = result.area ? result.area.areaId : result.defaultArea.areaId
                    address = Object.assign(result.address, { areaId: areaId, name: user.firstName + " " + user.lastName, phoneNumber: user.phone })
                    if (result.area !== undefined) {
                        if (cartReviewPayload.orderProducts[0].option.isPack) {
                            const productId = subPacks ? subPacks[0].productId : cartReviewPayload.orderProducts[0].productId
                            cartReviewPayload.option.deliveryInfo[index].isWeekdayAddressServiceable = await this.menuService.isServiceableAreaForPack(productId, result.area.areaId, cartReviewPayload.option.date, false)
                        } else {
                            cartReviewPayload.option.deliveryInfo[index].isWeekdayAddressServiceable = true
                        }
                    } else {
                        cartReviewPayload.option.deliveryInfo[index].isWeekdayAddressServiceable = false
                    }
                    address.eatDeliveryInstruction = defaultDeliveryInstruction
                }
                if (weekendResult && weekendResult.address) {
                    const weekendAreaId = weekendResult.area ? weekendResult.area.areaId : weekendResult.defaultArea.areaId
                    weekendAddress = Object.assign(weekendResult.address, { areaId: weekendAreaId, name: user.firstName + " " + user.lastName, phoneNumber: user.phone })
                    if (weekendResult.area !== undefined) {
                        if (cartReviewPayload.orderProducts[0].option.isPack) {
                            const productId = subPacks ? subPacks[0].productId : cartReviewPayload.orderProducts[0].productId
                            cartReviewPayload.option.deliveryInfo[index].isWeekendAddressServiceable = await this.menuService.isServiceableAreaForPack(productId, weekendResult.area.areaId, cartReviewPayload.option.date, true)
                        } else {
                            cartReviewPayload.option.deliveryInfo[index].isWeekendAddressServiceable = true
                        }
                    } else {
                        cartReviewPayload.option.deliveryInfo[index].isWeekendAddressServiceable = false
                    }
                    weekendAddress.eatDeliveryInstruction = defaultDeliveryInstruction
                }
                return {
                    deliverySlot: info.deliverySlot, // user.isInternalUser ? undefined : info.deliverySlot,
                    mealSlot: info.mealSlot,
                    address: address,
                    weekendAddress: weekendAddress,
                }
            })
            let isOnDemandOrder: boolean = false
            const deliveryInfo: DeliveryInfo[] = await Promise.all(infoPromises)
            const pidQuantityMap: { [id: string]: number } = {}
            const reqOrderProducts = _.uniqBy(cartReviewPayload.orderProducts, (product) => {
                pidQuantityMap[product.productId] = pidQuantityMap[product.productId] ? (pidQuantityMap[product.productId] + product.quantity) : product.quantity
                return product.productId
            })
            const isDeliveryWindowSupported = await AppUtil.isEatDeliveryWindowSupported(userContext, user)
            const orderProducts: OrderProduct[] = _.map(reqOrderProducts, (cartProduct) => {
                const orderProduct: OrderProduct = {
                    productId: cartProduct.productId,
                    quantity: pidQuantityMap[cartProduct.productId],
                    option: {}
                }
                if (cartReviewPayload.option) {
                    if (!cartProduct.option) {
                        cartProduct.option = {}
                    }
                    orderProduct.option.subscriptionType = cartProduct.option.subscriptionType
                    orderProduct.option.weekendEnabled = cartProduct.option.weekendEnabled
                    orderProduct.option.membershipType = cartProduct.option.membershipType
                    orderProduct.option.offerOrderId = cartProduct.option.offerOrderId
                    // Temp hack as website is passing offer id as empty
                    orderProduct.option.offerId = !_.isEmpty(cartProduct.option.offerId) ? cartProduct.option.offerId : undefined
                    orderProduct.option.startDate = cartReviewPayload.option.date
                    const slot: string = cartReviewPayload.option.deliveryInfo[0].deliverySlot
                    const deliveryWindow = cartReviewPayload.option.deliveryInfo[0].deliveryWindow
                    if (!isCafe) { // && !user.isInternalUser) {
                        orderProduct.option.deliverySlot = slot
                    }
                    if (slot === SlotUtil.ON_DEMAND_SLOT) {
                        isOnDemandOrder = true
                    }
                    orderProduct.option.numDays = cartProduct.option.numDays
                    orderProduct.option.fulfilmentId = cartProduct.option.fulfilmentId
                    let deliverySlot: DeliverySlot
                    if (isCafe && !isOnlyFitClubInCart) {
                        deliverySlot = DeliverySlotService.getDeliverySlotForCafe(<CafeSlot>slot)
                    } else {
                        deliverySlot = this.deliverySlotService.getSlotById(slot)
                    }

                    if (!_.isNil(deliveryWindow) && isDeliveryWindowSupported) {
                        orderProduct.option.deliveryWindow = deliveryWindow
                    } else if (!_.isNil(deliverySlot)) {
                        orderProduct.option.deliveryWindow = {
                            startingHours: deliverySlot.startTime,
                            closingHours: deliverySlot.endTime
                        }
                    }

                    if (!_.isNil(cartReviewPayload.option.numTickets)) {
                        orderProduct.option.numTickets = cartReviewPayload.option.numTickets
                    } else {
                        cartReviewPayload.option.numTickets = cartProduct.option.numTickets
                        orderProduct.option.numTickets = cartProduct.option.numTickets
                    }
                    if (!_.isNil(cartReviewPayload.option.autorenewalEnabled)) {
                        orderProduct.option.autorenewalEnabled = cartReviewPayload.option.autorenewalEnabled
                    }
                    if (!_.isNil(cartProduct.option.autorenewalEnabled)) {
                        orderProduct.option.autorenewalEnabled = cartProduct.option.autorenewalEnabled
                    }
                }
                if (orderProduct.option.offerId) {
                    orderProduct.option.offerV2Ids = [orderProduct.option.offerId]
                }
                return orderProduct
            })
            const orderCreate: OrderCreate = {
                userId: user.id,
                deviceId: deviceId,
                products: orderProducts,
                source: source,
                cityId: cityId,
                useOffersV2: true,
                orderNotes: cartReviewPayload.option.cultBookingNumber ? { referrerSource: "CULT_CLASS", referrerId: cartReviewPayload.option.cultBookingNumber } : undefined,
                dontCreateRazorpayOrder: true,
                useFitCash: cartReviewPayload.useFitCash ? cartReviewPayload.useFitCash : false,
                deliveryInfo: deliveryInfo,
                listingBrand: listingBrand,
                isOnDemandOrder: isOnDemandOrder,
                tipAmount: !_.isNil(deliveryTip) ? this.computeDeliveryTip(deliveryTip) : undefined,
                utm: utm,
                tenant: AppUtil.getTenantFromUserContext(userContext)
            }
            if (!_.isNil(cartReviewPayload.advertiserId)) orderCreate.advertiserId = cartReviewPayload.advertiserId
            return orderCreate
        }

        private getOMSOrderCheckoutPayload(
            userContext: UserContext,
            user: User,
            deviceId: string,
            source: OrderSource,
            cartReviewPayload: CartReviewPayload,
            cityId: string,
            isCafe?: boolean,
            utm?: any,
            kioskId?: string
        ): OrderCheckoutRequest {
            const pidQuantityMap: { [id: string]: number } = {}
            const reqOrderProducts = _.uniqBy(cartReviewPayload.orderProducts, (product) => {
                pidQuantityMap[product.productId] = pidQuantityMap[product.productId] ? (pidQuantityMap[product.productId] + product.quantity) : product.quantity
                return product.productId
            })
            const orderProducts: OrderProduct[] = _.map(reqOrderProducts, (cartProduct) => {
                const orderProduct: OrderProduct = {
                    productId: cartProduct.productId,
                    quantity: pidQuantityMap[cartProduct.productId],
                    option: {}
                }
                if (cartReviewPayload.option) {
                    if (!cartProduct.option) {
                        cartProduct.option = {}
                    }
                    orderProduct.option.startDate = cartReviewPayload.option.date
                    orderProduct.option.fulfilmentId = cartProduct.option.fulfilmentId
                }
                return orderProduct
            })
            const orderCreate: OrderCheckoutRequest = {
                userId: user.id,
                deviceId: deviceId,
                orderProducts: orderProducts,
                source: source,
                cityId: cityId,
                orderNotes: cartReviewPayload.option.cultBookingNumber ? { referrerSource: "CULT_CLASS", referrerId: cartReviewPayload.option.cultBookingNumber } : undefined,
                useFitCash: cartReviewPayload.useFitCash ? cartReviewPayload.useFitCash : false,
                utm: utm,
                tenant: AppUtil.getTenantFromUserContext(userContext),
                advertiserId: cartReviewPayload.advertiserId,
                userAddress: (kioskId) ? {
                    userId: user.id,
                    addressId: "DUMMY",
                    kioskId: kioskId,
                    addressLine1: "",
                    addressLine2: "DUMMY",
                    latLong: [0, 0]
                } : undefined
            }
            return orderCreate
        }

        private async getCultBikeAlfredCreateOrderPayload(userContext: UserContext, user: User, preferredLocation: PreferredLocation, deviceId: string, source: OrderSource,
            cartReviewPayload: CartReviewPayload, cityId: string, isOnlyFitClubInCart?: boolean, isCafe?: boolean, subPacks?: FoodPack[],
            listingBrand?: ListingBrandIdType, deliveryTip?: number, utm?: any): Promise<OrderCreate> {
            const isPack = false
            const deliveryInfoPromises = _.map(cartReviewPayload.option.deliveryInfo, (deliveryInfo) => {
                const weekdayAddressPromise = deliveryInfo.addressId ? this.userBusiness.getAreaAndAddressByAddressId(deliveryInfo.addressId, user.id, deliveryInfo.mealSlot, isPack, listingBrand) : undefined
                return {
                    mealSlot: deliveryInfo.mealSlot,
                    deliverySlot: deliveryInfo.deliverySlot,
                    addressPromise: weekdayAddressPromise,
                    weekendAddressPromise: undefined,
                    cutleryInstruction: undefined,
                }
            })
            const infoPromises = _.map(deliveryInfoPromises, async (info, index) => {
                const result = await info.addressPromise
                let address: UserDeliveryAddress = {
                    addressId: "DUMMY",
                    areaId: undefined,
                    kioskId: undefined,
                    addressLine1: "",
                    addressLine2: "",
                    name: isOnlyFitClubInCart ? user.firstName + " " + user.lastName : "",
                    userId: user.id,
                    phoneNumber: isOnlyFitClubInCart ? user.phone : undefined,
                    latLong: [0, 0],
                    eatDeliveryInstruction: undefined
                }
                if (result && result.address) {
                    const areaId = result?.area ? result.area?.areaId : result?.defaultArea?.areaId
                    address = Object.assign(result.address, { areaId: areaId, name: user.firstName + " " + user.lastName, phoneNumber: user.phone })
                    cartReviewPayload.option.deliveryInfo[index].isWeekdayAddressServiceable = !_.isEmpty(await this.interfaces.segmentService.doesUserBelongToSegment(AppUtil.cultBikeServiceabilitySegment(), userContext))
                }
                return {
                    deliverySlot: info.deliverySlot, // user.isInternalUser ? undefined : info.deliverySlot,
                    mealSlot: info.mealSlot,
                    address: address,
                }
            })
            const isOnDemandOrder: boolean = false
            const deliveryInfo: DeliveryInfo[] = await Promise.all(infoPromises)
            const pidQuantityMap: { [id: string]: number } = {}
            const reqOrderProducts = _.uniqBy(cartReviewPayload.orderProducts, (product) => {
                pidQuantityMap[product.productId] = pidQuantityMap[product.productId] ? (pidQuantityMap[product.productId] + product.quantity) : product.quantity
                return product.productId
            })
            const orderProducts: OrderProduct[] = _.map(reqOrderProducts, (cartProduct) => {
                const orderProduct: OrderProduct = {
                    productId: cartProduct.productId,
                    quantity: pidQuantityMap[cartProduct.productId],
                    option: {}
                }
                if (cartReviewPayload.option) {
                    if (!cartProduct.option) {
                        cartProduct.option = {}
                    }
                    orderProduct.option.offerOrderId = cartProduct.option.offerOrderId
                    // Temp hack as website is passing offer id as empty
                    orderProduct.option.offerId = !_.isEmpty(cartProduct.option.offerId) ? cartProduct.option.offerId : undefined
                    orderProduct.option.startDate = cartReviewPayload.option.date
                    orderProduct.option.fulfilmentId = cartProduct.option.fulfilmentId
                }
                if (orderProduct.option.offerId) {
                    orderProduct.option.offerV2Ids = [orderProduct.option.offerId]
                }
                return orderProduct
            })
            const orderCreate: OrderCreate = {
                userId: user.id,
                deviceId: deviceId,
                products: orderProducts,
                source: source,
                cityId: cityId,
                useOffersV2: true,
                orderNotes: undefined,
                dontCreateRazorpayOrder: true,
                useFitCash: cartReviewPayload.useFitCash ? cartReviewPayload.useFitCash : false,
                deliveryInfo: deliveryInfo,
                listingBrand: listingBrand,
                isOnDemandOrder: isOnDemandOrder,
                tipAmount: !_.isNil(deliveryTip) ? this.computeDeliveryTip(deliveryTip) : undefined,
                utm: utm,
                tenant: AppUtil.getTenantFromUserContext(userContext),
                offersVersion: 3,
            }
            if (!_.isNil(cartReviewPayload.advertiserId)) orderCreate.advertiserId = cartReviewPayload.advertiserId
            return orderCreate
        }

        cartSizeCheck(listingBrand: ListingBrandIdType, totalNumberOfProducts: number, cityId: string) {
            if (listingBrand === "FOOD_MARKETPLACE" && totalNumberOfProducts > 10) {
                throw this.errorFactory.withCode(ErrorCodes.MAX_ITEMS_IN_CART_ERR, 400).withMeta({ totalItems: totalNumberOfProducts, maxItems: 10 }).withDebugMessage(`Only ${10} items can be added in cart`).build()
            }
            const cityWiseMaxCartSize = _.get(this.configService.getConfig("EAT_CLP_OVERRIDES").configs, "citywiseMaxCartSize")
            const eatMaxAllowedCartSize = cityId ? _.get(cityWiseMaxCartSize, cityId, MAX_CART_SIZE) : MAX_CART_SIZE
            if (listingBrand !== "WHOLE_FIT" && totalNumberOfProducts > eatMaxAllowedCartSize) {
                throw this.errorFactory.withCode(ErrorCodes.MAX_ITEMS_IN_CART_ERR, 400).withMeta({ maxItems: eatMaxAllowedCartSize }).withDebugMessage(`Only ${eatMaxAllowedCartSize} items can be added in cart`).build()
            } else if (listingBrand === "WHOLE_FIT" && totalNumberOfProducts > WHOLE_FIT_V2_MAX_CART_SIZE) {
                throw this.errorFactory.withCode(ErrorCodes.MAX_ITEMS_IN_CART_ERR, 400).withMeta({ maxItems: WHOLE_FIT_V2_MAX_CART_SIZE }).withDebugMessage(`Only ${eatMaxAllowedCartSize} items can be added in cart`).build()
            }
        }

        computeDeliveryTip(deliveryTip: number): TippingCharge {
            const charge: TippingCharge = {
                unitPrice: deliveryTip,
                tax: {
                    amount: 0,
                    percentage: 0,
                    type: TaxType.CGST_SGST
                },
                total: deliveryTip
            }
            return charge
        }

        async getSugarfitPriceOverride(userId: string, createOrderPayload: OrderCreate, req: express.Request): Promise<number> {
            try {
                const freemiumUserOffers = await this.cfAPIJavaService.getFreemiumOffer(userId, req)
                return SugarfitUtil.getPriceOverrideForFreemiumPack(createOrderPayload, freemiumUserOffers)
            } catch (error) {
                this.logger.error("getFreemiumOfferForPack failed for userId  " + userId + " with error: ", error)
                throw error
            }
        }
    }

    return CartController
}
export default controllerFactory
