import { inject, injectable } from "inversify"
import { CATALOG_CLIENT_TYPES, ICatalogueService, ICatalogueServicePMS } from "@curefit/catalog-client"
import {
    ALFRED_CLIENT_TYPES,
    FoodBooking,
    FoodPackBooking,
    IAlfredServiceEther as IAlfredService,
    IShipmentService
} from "@curefit/alfred-client"
import {
    IIndusService,
    INDUS_CLIENT_TYPES,
    IndusOrderProduct,
    IndusOrderShipments,
    IndusProduct,
    ProductResponse,
    UserOfferResponseV2
} from "@curefit/indus-client"
import { ArrivalInfo, OfferHelper, OMS_API_CLIENT_TYPES, PayByMembershipDetails } from "@curefit/oms-api-client"
import {
    Action,
    ActionCard,
    ActionSheet,
    ActionSheetOption,
    ActionSheetWidget,
    AlertInfo,
    CartBaseProductSummaryWidget,
    CartPatientSummaryWidget,
    CenterSelectionWidget,
    ChronicCareCGMCheckoutWidget,
    ChronicCareCreativeWidget,
    ChronicCarePackCheckoutWidget,
    DatePickerWidget,
    DateWiseDeliverySlotAction,
    DeliverySlotAction,
    ExpandableListWidget,
    FitClubCheckoutSavingsWidget,
    InstructionsWidget,
    LHRBodyPart,
    LHRBodyPartsWidget,
    ManageOptionPayload,
    ManageOptions,
    ManageOptionsWidget,
    MealAction,
    PackInfoWidget,
    ProductDetailPage,
    ProductFeedbackWidget,
    ProductListWidget,
    SelectCenterAction,
    SfDiagnosticCartAddressWidget,
    SfDiagnosticCartListWidget,
    SfEcomCarCouponOrderProduct,
    SfEComCartAddressWidget,
    SfEComCartBillingWidget,
    SfEComCartCouponWidget,
    SfEComCartListWidget,
    SfEComPeopleBoughtWidget,
    SfEComProductItemSmallWidget,
    SfWidgetSeparator,
    SwitchWidget,
    TransformPaymentDetailsWidget,
    WidgetType,
    WidgetView
} from "../common/views/WidgetView"
import {
    CARE_BUNDLE_SUBCATEGORY_CODES,
    ConsultationProduct,
    DiagnosticProduct,
    Doctor,
    HealthfaceTenant,
    SUB_CATEGORY_CODE,
    TRANSFORM_SUB_CATEGORY_CODE_TYPE
} from "@curefit/care-common"
import { CustomerIssueType } from "@curefit/issue-common"
import {
    DeliveryInstruction,
    DeliverySlot,
    FoodPack,
    FoodProduct as Product,
    ListingBrandIdType,
    MealSlot,
    MenuType,
    UserDeliveryAddress
} from "@curefit/eat-common"
import { AccessLevel, CultCenter } from "@curefit/cult-common"
import { DeliveryCharge, ExtraCharge, ProductPrice, ProductType, UrlPathBuilder } from "@curefit/product-common"
import { HourMin, UserAgentType as UserAgent } from "@curefit/base-common"
import { User } from "@curefit/user-common"
import {
    ManagedPlanPaymentWidget,
    PackName,
    PaymentDetailsWidget,
    PriceComponent,
    PriceValue
} from "../order/OrderViewBuilder"
import {
    BaseOrder,
    CartReviewPayload,
    ClientDeliveryInfo,
    MembershipPaymentType,
    Order,
    OrderProduct,
    OrderProductOption,
    OrderProductSnapshots,
    ProductAvailability
} from "@curefit/order-common"
import { capitalizeFirstLetter, TimeUtil, Timezone } from "@curefit/util-common"
import EatUtil from "../util/EatUtil"
import { ALL_MEAL_SLOTS } from "@curefit/eat"
import {
    ActionUtil,
    CATEGORY_PRIORITY_MAP,
    MAX_CART_SIZE,
    MealUtil,
    OfferUtil,
    OrderUtil,
    SeoUrlParams
} from "@curefit/base-utils"
import { SlotUtil } from "@curefit/eat-util"
import * as _ from "lodash"
import { ceil, floor } from "lodash"
import { BillingInfo, RUPEE_SYMBOL } from "@curefit/finance-common"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import IProductBusiness, { MenuAvailabilityResult } from "../product/IProductBusiness"
import ICRMIssueService from "../crm/ICRMIssueService"
import OrderTrackingStatusWidget from "../common/views/OrderTrackingStatusWidget"
import { BannerCarouselWidget, ListingActionableCardWidget, MealItem } from "../page/PageWidgets"
import { BASE_TYPES, Logger } from "@curefit/base"
import {
    ALBUS_CLIENT_TYPES,
    BundleSellableProduct,
    BundleSessionSellableProduct,
    DiagnosticTestProduct,
    HealthfaceProductInfo,
    IHealthfaceService,
    ManagedPlanSellableProduct,
    MPChildProduct,
    Patient
} from "@curefit/albus-client"
import { OfferV2 } from "@curefit/offer-common"
import {
    FoodSinglePriceOfferResponse,
    IOfferServiceV2,
    OFFER_SERVICE_CLIENT_TYPES,
    OfferServiceV3,
    PackOffersResponse
} from "@curefit/offer-service-client"
import AppUtil, {
    addCommaInPriceINR,
    AppFont,
    CANCELLATION_WINDOW_FOR_NO_SHOW,
    CARE_COLLAPSIBLE_OFFER_SUPPORTED,
    noShowSkuPlusModalState,
    showSkuPlusModalElitePlusKey,
    showSkuPlusModalProPlusKey
} from "../util/AppUtil"
import { UserContext } from "@curefit/userinfo-common"
import { AddChangeCancelDailyMealWidget, BaseWidget, CareWidgetUtil, } from "@curefit/vm-models"
import { DELIVERY_CLIENT_TYPES, IDeliveryAreaService, IDeliverySlotService } from "@curefit/delivery-client"
import { CULT_CLIENT_TYPES, ICultServiceOld as ICultService } from "@curefit/cult-client"
import TeleconsultationDetailsPageConfig from "../care/TeleconsultationDetailsPageConfig"
import { Feedback } from "@curefit/feedback-common"
import FeedbackPageConfigV2Cache from "../ugc/FeedbackPageConfigV2Cache"
import CultUtil, { CULT_NAS_PRICE_HIKE_BANNER_ID, PRO_NAS_PRICE_HIKE_BANNER_ID } from "../util/CultUtil"
import { Orientation } from "@curefit/vm-common"
import CultPackPageConfig from "../pack/CultPackPageConfig"
import { CareUtil } from "../util/CareUtil"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { FITCASH_CLIENT_TYPES, IFitcashService } from "@curefit/fitcash-client"
import OrderUtilV1, { DEFAULT_DELIVERY_CHARGE, PACKAGING_CHARGE } from "../util/OrderUtil"
import { FitclubBusiness } from "../fitclub/FitclubBusiness"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { ConfigService, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import { EatSubscriptionUtil } from "../util/EatSubscriptionUtil"
import { ICapacityService, MASTERCHEF_CLIENT_TYPES } from "@curefit/masterchef-client"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { FitclubSavingsCalloutWidgetV2 } from "../product/FitclubSavingsCalloutWidgetV2"
import { IOfferAddonData, IOfferAddonWidgetParams, OfferAddonWidget } from "../common/views/OfferAddonWidget"
import { HighlightedToggleSelectionWidget } from "@curefit/apps-common/dist/src/widgets/cult/interfaces"
import { ActionType, NoteListWidget, ProductOffer, ProductOfferWidgetV2 } from "@curefit/apps-common"
import { ErrorFactory } from "@curefit/error-client"
import { ErrorCodes } from "../error/ErrorCodes"
import { PromiseCache } from "../util/VMUtil"
import FoodMarketplaceUtil, { FoodMarketplaceFESupportedStatus } from "../util/FoodMarketplaceUtil"
import { ShipmentStatus, UpcomingOrderDetailsResponse } from "@curefit/gandalf-common"
import { IOllivanderCityService, OLLIVANDER_CLIENT_TYPES } from "@curefit/ollivander-node-client"
import { DoctorAssetsResponse } from "@curefit/ollivander-node-client/dist/src/OllivanderCityService"
import { GymPtProduct } from "@curefit/gymfit-common"
import { CENTER_SERVICE_CLIENT_TYPES, ICenterService } from "@curefit/center-service-client"
import { CenterResponse } from "@curefit/center-service-common"
import GymfitUtil from "../util/GymfitUtil"
import { CGMProduct } from "@curefit/care-common/src/product"
import { IDENTITY_CLIENT_TYPES, IIdentityService } from "@curefit/identity-client"
import { TransformUtil } from "../util/TransformUtil"
import PlaySelectPackViewBuilder from "../pack/PlaySelectPackViewBuilder"
import { CultMembershipMetadata } from "@curefit/order-common/src/Order"
import PlayUtil, { PLAY_NAS_PRICE_HIKE_BANNER_ID } from "../util/PlayUtil"
import IUserBusiness from "../user/IUserBusiness"
import { IPersonalTrainingService, PERSONAL_TRAINING_CLIENT_TYPES } from "@curefit/personal-training-v2-client"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { GymfitAccessLevel } from "@curefit/gymfit-common/dist/src/Listing"
import { IssueDetailView } from "../crm/IssueBusiness"
import WidgetBuilder from "../page/vm/WidgetBuilder"
import { HeadersUtil } from "../../util/HeadersUtil"
import {
    AugmentedOfflineFitnessPack,
    BenefitEntry,
    BenefitType,
    ExhaustivePackBenefit,
    OfflineFitnessPack,
    ProductSubType
} from "@curefit/pack-management-service-common"
import { IAppConfigStoreService } from "../appConfig/AppConfigStoreService"
import { IOfflineFitnessPackService, PACK_CLIENT_TYPES, PackUtils } from "@curefit/pack-management-service-client"
import PlayPackViewBuilder from "../pack/PlayPackViewBuilder"
import { IUserAttributeClient } from "@curefit/rashi-client"
import { ISegmentationCacheClient, SEGMENTATION_CLIENT_TYPES } from "@curefit/segmentation-service-client"
import CatalogueServiceUtilities from "../util/CatalogueServiceUtilities"
import { FeatureStateCache } from "../page/vm/services/FeatureStateCache"
import { it } from "node:test"

const clone = require("clone")

export interface ClientCart {
    startDate: string
    deliveryInfos: ClientDeliveryInfo[]
}

export interface CartReviewView extends ProductDetailPage {
    alertInfo?: AlertInfo,
    cart?: ClientCart,
    showDivider?: boolean
    total?: string,
    totalWithoutCurrency?: number,
    cityModalData?: any,
    initAction?: Action
}

export class DeliverySlotWidget implements WidgetView {
    title: string
    subTitle?: string
    deliverySlot?: string
    isDisabled: boolean
    action: DeliverySlotAction
    widgetType: WidgetType = "DELIVERY_SLOT_WIDGET"

    constructor(userContext: UserContext, date: string, availableSlots: string[], selectedSlot: string,
                isDisabled: boolean, allSlots: DeliverySlot[], arrivalInfo: ArrivalInfo, onDemandText: string,
                mealSlot: MenuType, areaTz: Timezone, isCafe?: boolean, isDeliverySlotEmpty?: boolean) {
        let title: string = "Change Delivery Slot"
        if (selectedSlot === SlotUtil.ON_DEMAND_SLOT) {
            title = "Schedule"
        }
        const selectedSlotObject: DeliverySlot = _.find(allSlots, slot => {
            return slot.slotId === selectedSlot
        })
        this.title = this.checkForDeliverySlotTitle(userContext, selectedSlot, onDemandText, isDeliverySlotEmpty) ? "Select delivery slot" : MealUtil.getDisplayTextForSlot(selectedSlotObject, undefined, arrivalInfo, onDemandText, isCafe)
        this.subTitle = TimeUtil.getDayText(date, areaTz)
        this.deliverySlot = selectedSlot
        this.isDisabled = isDisabled
        const slots = _.map(allSlots, slot => {
            const slotDetail: {
                deliverySlot: string
                title: string,
                isEnabled: boolean
            } = {
                deliverySlot: slot.slotId,
                title: MealUtil.getDisplayTextForSlot(slot, undefined, arrivalInfo, onDemandText, isCafe),
                isEnabled: _.findIndex(availableSlots, (availableSlot) => {
                    return availableSlot === slot.slotId
                }) !== -1
            }
            return slotDetail
        })
        if (!_.isNil(onDemandText) && onDemandText.length > 0) {
            slots.splice(0, 0, {
                title: MealUtil.getSlotDisplayText(SlotUtil.ON_DEMAND_SLOT, undefined, arrivalInfo, onDemandText, isCafe),
                deliverySlot: undefined,
                isEnabled: true
            })
        }
        this.action = {
            title: title,
            actionType: "CHANGE_SLOT",
            mealSlot: mealSlot,
            slots: slots
        }
    }

    private checkForDeliverySlotTitle(userContext: UserContext, selectedSlot: string, onDemandText: string, isDeliverySlotEmpty: boolean): boolean {
        return ((isDeliverySlotEmpty && onDemandText === "") || _.isNil(selectedSlot))
    }
}

export class DeliverySlotWidgetV2 implements WidgetView {
    title: string
    subTitle?: string
    deliverySlot?: string
    deliveryDate?: string
    isDisabled: boolean
    action: DateWiseDeliverySlotAction
    widgetType: WidgetType = "DELIVERY_SLOT_WIDGET"

    constructor(userContext: UserContext, date: string, availableSlots: { [date: string]: string[] }, selectedSlot: string,
                isDisabled: boolean, allSlots: DeliverySlot[], arrivalInfo: ArrivalInfo, onDemandText: string, mealSlot: MenuType,
                areaTz: Timezone, isCafe?: boolean, isDeliverySlotEmpty?: boolean) {
        let title: string = "Change Delivery Slot"
        if (selectedSlot === SlotUtil.ON_DEMAND_SLOT) {
            title = "Schedule"
        }
        const selectedSlotObject: DeliverySlot = _.find(allSlots, slot => {
            return slot.slotId === selectedSlot
        })
        this.title = this.checkForDeliverySlotTitle(userContext, selectedSlot, onDemandText, isDeliverySlotEmpty) ? "Select delivery slot" : MealUtil.getDisplayTextForSlot(selectedSlotObject, undefined, arrivalInfo, onDemandText, isCafe)
        this.subTitle = TimeUtil.getDayText(date, areaTz)
        this.deliverySlot = selectedSlot
        this.deliveryDate = date
        this.isDisabled = isDisabled
        const dayWiseSlots: any[] = []
        const days = _.keys(availableSlots)
        _.forEach(days, day => {
            const slots: any[] = []
            _.forEach(allSlots, slot => {
                const isSlotAvailable: string = _.find(availableSlots[day], daySlot => {
                    return daySlot === slot.slotId
                })

                slots.push({
                    slotId: slot.slotId,
                    title: MealUtil.getDisplayTextForSlot(slot, undefined, arrivalInfo, onDemandText, isCafe),
                    isEnabled: !_.isNil(isSlotAvailable)
                })

            })
            const dayWiseSlot = {
                deliveryDate: day,
                title: TimeUtil.getDayText(day, areaTz),
                deliverySlots: slots
            }
            dayWiseSlots.push(dayWiseSlot)
        })
        this.action = {
            title: title,
            actionType: "CHANGE_SLOTV2",
            mealSlot: mealSlot,
            slots: dayWiseSlots,
            deliveryDate: date,
            deliverySlot: selectedSlot
        }
    }

    private checkForDeliverySlotTitle(userContext: UserContext, selectedSlot: string, onDemandText: string, isDeliverySlotEmpty: boolean): boolean {
        return ((isDeliverySlotEmpty && onDemandText === "") || _.isNil(selectedSlot))
    }
}

export class CheckoutAddressWidget implements WidgetView {
    weekdayAddressId?: string
    weekdayAddress?: string
    weekendAddressId?: string
    weekendAddress?: string
    isDisabled: boolean
    addressId?: string // backward compatibility
    actions: Action[]
    widgetType: WidgetType = "ADDRESS_WIDGET"
    title: string = "Address"
    subTitle?: string // backward compatibility
    action?: Action // backward compatibility
    weekdaySubTitle?: string
    weekendSubTitle?: string
    weekdayDeliveryInstruction?: DeliveryInstruction
    weekendDeliveryInstruction?: DeliveryInstruction

    constructor(userContext: UserContext, weekDayAddress: UserDeliveryAddress, weekendAddress: UserDeliveryAddress, weekendEnabled: boolean, isPack: boolean, mealSlot: MenuType, isCopySelected?: boolean, widgetType?: WidgetType) {
        this.title = "Address"
        if (widgetType) {
            this.widgetType = widgetType
        }
        let weekDayAddressText = ""
        if (!userContext.sessionInfo.isUserLoggedIn) {
            this.isDisabled = true
        } else {
            this.isDisabled = weekDayAddress && weekDayAddress.kioskId ? true : false
        }
        if (weekDayAddress && weekDayAddress.addressId !== "DUMMY") {
            // if (weekDayAddress.addressLine1)
            //     weekDayAddressText = weekDayAddress.addressLine1 + ","
            this.title = weekDayAddress.addressType
            weekDayAddressText = weekDayAddressText + weekDayAddress.addressLine2
            this.weekdayDeliveryInstruction = weekDayAddress.eatDeliveryInstruction
            this.weekdayAddressId = weekDayAddress.addressId
            this.addressId = weekDayAddress.addressId
            this.weekdayAddress = weekDayAddressText // capitalizeFirstLetter(weekDayAddress.addressType ? weekDayAddress.addressType : "OTHER") + ": " + weekDayAddressText
            this.subTitle = weekDayAddressText
        }
        if (isPack) {
            this.weekdaySubTitle = "Weekdays:"
            this.weekendSubTitle = "Weekends:"
        }
        let weekendAddressText = ""
        if (weekendAddress && weekendAddress.addressId !== "DUMMY") {
            // if (weekendAddress.addressLine1)
            //     weekendAddressText = weekendAddress.addressLine1 + ","
            weekendAddressText = weekendAddressText + weekendAddress.addressLine2
            this.weekendDeliveryInstruction = weekendAddress.eatDeliveryInstruction
            this.weekendAddressId = weekendAddress.addressId
            this.weekendAddress = weekendAddressText // capitalizeFirstLetter(weekendAddress.addressType ? weekendAddress.addressType : "OTHER") + ": " + weekendAddressText
        }
        this.action = {
            actionType: "CHANGE_ADDRESS",
            url: "curefit://selectaddress?pageFrom=cart_checkout",
            title: "Change"
        }
        this.actions = [{
            actionType: "CHANGE_ADDRESS",
            url: "curefit://selectaddress?pageFrom=cart_checkout",
            title: weekendEnabled ? "Select Weekday Address" : "Select Address",
            meta: {
                isWeekendAddress: false,
                mealSlot: mealSlot
            }
        }]
        if (weekendEnabled) {
            this.actions.push({
                actionType: "CHANGE_WEEKEND_ADDRESS",
                url: "curefit://selectaddress?pageFrom=cart_checkout",
                title: "Select Weekend Address",
                meta: {
                    isWeekendAddress: true,
                    mealSlot: mealSlot
                }
            })
        }
        if (!_.isNil(isCopySelected) && (mealSlot === "SNACKS" || mealSlot === "DINNER")) {
            this.actions.push({
                title: "Same as " + (mealSlot === "SNACKS" ? "Lunch" : "Breakfast"),
                actionType: "TOGGLE_COPY_ADDRESS",
                meta: {
                    mealSlot: mealSlot,
                    isSelected: !isCopySelected
                }
            })
        }
    }
}

export class DeliveryInfoWidget implements WidgetView {
    addressInfo: CheckoutAddressWidget
    slotInfo: DeliverySlotWidget
    title: string
    subTitleLine1: string
    subTitleLine2: string
    subTitleLine3: string
    widgetType: WidgetType = "DELIVERY_INFO_WIDGET"

    constructor(userContext: UserContext, weekDayAddress: UserDeliveryAddress, weekendAddress: UserDeliveryAddress,
                weekendEnabled: boolean, startDate: string, availableSlots: string[], allSlots: DeliverySlot[],
                selectedSlot: string, arrivalInfo: ArrivalInfo, mealSlot: MealSlot, isCopySelected: boolean,
                isDeliverySlotEmpty: boolean, areaTz: Timezone) {
        this.addressInfo = new CheckoutAddressWidget(userContext, weekDayAddress, weekendAddress, weekendEnabled, true, mealSlot, isCopySelected === undefined ? false : isCopySelected)
        this.slotInfo = new DeliverySlotWidget(userContext, startDate, availableSlots, selectedSlot, false, allSlots, arrivalInfo, undefined, mealSlot, areaTz, isDeliverySlotEmpty)
        this.title = capitalizeFirstLetter(mealSlot)
        if (selectedSlot) {
            this.subTitleLine3 = " - " + this.slotInfo.title
        }
        if (weekDayAddress)
            this.subTitleLine1 = this.addressInfo.weekdayAddress
        if (weekendAddress)
            this.subTitleLine2 = this.addressInfo.weekendAddress
    }
}

export interface OrderItem {
    isVeg: boolean
    offerId?: string
    productId: string
    image?: string
    quantity: number
    price: ProductPrice
    productName: string
    productNameWithoutUnits?: string
    productType?: ProductType
    subTitle?: string
    additionalSubtitle?: string
    option?: OrderProductOption
    action?: Action
    stock?: number
    parentProductId?: string
    comboOfferId?: string,
    servingUnit?: string,
    servingQty?: number
    displayUnitQty?: string,
    variantTitle?: string
}

interface AddonItem extends MealItem {
}

interface OrderItemDetail extends OrderItem {
    calories: number
    image: string
}

interface CartSummaryWidget extends WidgetView {
    title: string
    subTitle?: string
    manageOptions: ManageOptions
    meta: any
}

export interface CartListWidget extends WidgetView {
    title: string
    orderItems: OrderItem[]
    cartValue?: number
    priceDetails?: PriceComponent[]
    fitClubSavings?: string
}

interface BillingWidget extends WidgetView {
    title: string
    cartValue: number
    priceDetails: PriceComponent[]
    fitClubSavings?: string
    fitcashBalance?: number,
    isFitcashEnabled?: boolean,
    currency?: string
}

interface AddonListWidget extends WidgetView {
    title: string
    subTitle?: string
    addonItems: AddonItem[]
}

interface CareAddonListWidget extends WidgetView {
    title: string
    subTitle?: string
    addonItems: CareAddonItem[]
    orientation?: Orientation
    noteText?: string
    showSelectAll?: boolean
}

export interface CareAddonItem {
    image: string,
    title: string,
    subTitle?: string,
    price: ProductPrice,
    action: Action,
    includedInPack?: boolean,
    added: boolean,
    productId: string,
    offerIds?: string[]
}

interface CartDetailListWidget extends WidgetView {
    title: string
    orderItems: OrderItemDetail[]
}

interface CareHomeCollectionCharges {
    showHomeSampleCharges?: boolean,
    homeSampleCharges?: number,
    homeSampleCartLimit?: number,
    orderHomeSampleCharges?: number,
}

@injectable()
class CartViewBuilder {

    constructor(
        @inject(MASTERCHEF_CLIENT_TYPES.CapacityService) private capacityService: ICapacityService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReaderPMS) private catalogueServicePMS: ICatalogueServicePMS,
        @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
        @inject(ALFRED_CLIENT_TYPES.ShipmentService) private shipmentService: IShipmentService,
        @inject(ALFRED_CLIENT_TYPES.AlfredService) private alfredService: IAlfredService,
        @inject(INDUS_CLIENT_TYPES.IndusService) private indusService: IIndusService,
        @inject(CUREFIT_API_TYPES.AppConfigStoreService) private appConfigStore: IAppConfigStoreService,
        @inject(CUREFIT_API_TYPES.ProductBusiness) private productBusiness: IProductBusiness,
        @inject(CUREFIT_API_TYPES.CRMIssueService) private CRMIssueService: ICRMIssueService,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) private offerServiceV2: IOfferServiceV2,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) private offerServiceV3: OfferServiceV3,
        @inject(DELIVERY_CLIENT_TYPES.DeliveryAreaService) private deliveryAreaService: IDeliveryAreaService,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
        @inject(FITCASH_CLIENT_TYPES.FitcashService) private fitcashService: IFitcashService,
        @inject(CULT_CLIENT_TYPES.MindFitService) private mindFitService: ICultService,
        @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness,
        @inject(CUREFIT_API_TYPES.TeleconsultationDetailsPageConfig) protected tcDetailsPageConfig: TeleconsultationDetailsPageConfig,
        @inject(CUREFIT_API_TYPES.CultPackPageConfig) protected cultPackPageConfig: CultPackPageConfig,
        @inject(DELIVERY_CLIENT_TYPES.DeliverySlotService) protected deliverySlotService: IDeliverySlotService,
        @inject(CUREFIT_API_TYPES.FitclubBusiness) protected fitclubBusiness: FitclubBusiness,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(PAGE_CONFIG_TYPES.ConfigService) private configService: ConfigService,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        @inject(OLLIVANDER_CLIENT_TYPES.IOllivanderCityService) private ollivanderService: IOllivanderCityService,
        @inject(CENTER_SERVICE_CLIENT_TYPES.CenterService) private centerService: ICenterService,
        @inject(LOCATION_TYPES.CityService) private cityService: ICityService,
        @inject(OMS_API_CLIENT_TYPES.OfferHelper) private offerHelper: OfferHelper,
        @inject(IDENTITY_CLIENT_TYPES.IdentityService) protected identityService: IIdentityService,
        @inject(CUREFIT_API_TYPES.UserBusiness) private userBusiness: IUserBusiness,
        @inject(PERSONAL_TRAINING_CLIENT_TYPES.PersonalTrainingService) private ptService: IPersonalTrainingService,
        @inject(CUREFIT_API_TYPES.WidgetBuilder) public widgetBuilder: WidgetBuilder,
        @inject(CUREFIT_API_TYPES.PlaySelectPackViewBuilder) private playSelectPackViewBuilder: PlaySelectPackViewBuilder,
        @inject(CUREFIT_API_TYPES.PlayPackViewBuilder) private playPackViewBuilder: PlayPackViewBuilder,
        @inject(PACK_CLIENT_TYPES.OfflineFitnessPackService) private offlineFitnessPackService: IOfflineFitnessPackService,
        @inject(SEGMENTATION_CLIENT_TYPES.SegmentationCacheClient) private segmentationCacheClient: ISegmentationCacheClient,
        @inject(CUREFIT_API_TYPES.FeatureStateCache) public featureStateCache: FeatureStateCache,
    ) {
    }

    colorMap = {
        blue: "#7BF7FF",
        green: "#9EFDDB",
        lightBlue: "#51B9CF",
        pink: "#fc55da",
        orange: "#ff6522"
    }
    durationColorMap = {
        3: this.colorMap.blue,
        6: this.colorMap.orange,
        12: this.colorMap.pink,
    }

    getRandomColor(): string {
        const colorValues = Object.values(this.durationColorMap)
        return colorValues[Math.floor(Math.random() * colorValues.length)]
    }
    private freeTitleTag = "Get for free"

    async buildCartReviewView(userContext: UserContext, mealSlotDeliverySlots: { [mealSlot: string]: string[] }, arrivalInfo: ArrivalInfo, deliveryInfo: ClientDeliveryInfo[], order: Order,
                              availabilities: ProductAvailability[], menuAvailabilityResult: MenuAvailabilityResult, mealSlot: MenuType,
                              user: User, deviceId: string, areaId: string, singleOffersResponse?: FoodSinglePriceOfferResponse,
                              isOfferOverriden: boolean = false, validPackStartDates?: string[], alertInfo?: AlertInfo, isCafe?: boolean, listingBrand?: ListingBrandIdType, dateWiseSlots?: { [date: string]: string[] }, isDeliverySlotEmpty?: boolean):
        Promise<CartReviewView> {
        const isPackBundle = order.productSnapshots[0].isPack && mealSlot === "ALL"
        const widgetViewPromises: Promise<WidgetView>[] = []
        const numProducts = _.sum(order.productSnapshots.map(p => {
            return p.quantity
        }))
        const isKiosk = order.userAddress && order.userAddress.kioskId !== undefined
        const isDisabled = (mealSlot === "ALL" || isKiosk) && !isCafe
        const onDemandText = order.eatOptions && order.eatOptions.onDemandPromise ? order.eatOptions.onDemandPromise.message : ""
        const areaTz = await this.deliveryAreaService.getTimeZoneForAreaId(areaId)
        let total = 0
        const billingInfo = this.offerHelper.getOrderBilling(order)
        if (numProducts > 0) {
            const orderProductOption = order.productSnapshots[0].option
            if (!isPackBundle) {
                widgetViewPromises.push(this.getAddressWidget(userContext, order.userAddress, order.userWeekendAddress, orderProductOption.weekendEnabled, order.productSnapshots[0].isPack, mealSlot))
                if (order.productSnapshots[0].isPack === true) {
                    widgetViewPromises.push(this.getStartDateWidget(orderProductOption.startDate, validPackStartDates, userContext, areaTz))
                }
                let allSlots: DeliverySlot[] = this.deliverySlotService.getDeliverySlotsForListingBrand(mealSlot, isKiosk ? "KIOSK" : "ONLINE", listingBrand)
                if (isCafe) {
                    allSlots = SlotUtil.getAllDeliverySlotForCafe()
                }
                let deliverySlotDisabled = isDisabled
                if (order.orderNotes && order.orderNotes.referrerSource === "CULT_CLASS") {
                    deliverySlotDisabled = true
                }
                const deliverySlotWidget = listingBrand === "WHOLE_FIT" ?
                    Promise.resolve(new DeliverySlotWidgetV2(userContext, orderProductOption.startDate, dateWiseSlots,
                        deliveryInfo[0].deliverySlot, deliverySlotDisabled, allSlots, arrivalInfo, onDemandText, mealSlot, areaTz, isCafe, isDeliverySlotEmpty)) :
                    Promise.resolve(new DeliverySlotWidget(userContext, orderProductOption.startDate, mealSlotDeliverySlots[mealSlot],
                        deliveryInfo[0].deliverySlot, deliverySlotDisabled, allSlots, arrivalInfo, onDemandText, mealSlot, areaTz, isCafe, isDeliverySlotEmpty))
                widgetViewPromises.push(deliverySlotWidget)
                if (MealUtil.isCutleryChangeSupported(userContext) && !isKiosk && listingBrand !== "WHOLE_FIT") {
                    const isCutleryEnabled = order.userAddress.eatDeliveryInstruction.cutleryInstruction === "GIVE_CUTLERY"
                    const subtitle = (order.packagingCharges && !order.productSnapshots[0].isPack) ? "Refuse cutlery, save ₹3 and go eco-friendly!" : "Refuse cutlery and go eco-friendly"
                    const cutleryWidget: SwitchWidget = {
                        widgetType: "SWITCH_WIDGET",
                        title: isCutleryEnabled ? "Bring cutlery" : "Don't bring cutlery",
                        icon: "CUTLERY",
                        action: {
                            actionType: "SET_CUTLERY_INSTRUCTION",
                            meta: {
                                cutleryInstruction: isCutleryEnabled ? "NO_CUTLERY" : "GIVE_CUTLERY",
                                mealSlot: mealSlot
                            }
                        },
                        isEnabled: isCutleryEnabled,
                        subTitle: subtitle
                    }
                    widgetViewPromises.push(Promise.resolve(cutleryWidget))
                }
            } else {
                // arrival info should be at a meal slot level if bundles are used at kiosks
                widgetViewPromises.push(Promise.resolve(this.getDeliveryInfoWidget(userContext, order, mealSlotDeliverySlots, isKiosk, arrivalInfo, deliveryInfo, isDeliverySlotEmpty, areaTz)))
                widgetViewPromises.push(this.getStartDateWidget(orderProductOption.startDate, validPackStartDates, userContext, areaTz))
            }

            const cartListWidgetAndBillingPromise = this.getCartListWidgetAndBilling(userContext, order, billingInfo, availabilities, areaTz)
            // this.logger.info("order.productSnapshots[0].isPack : " + order.productSnapshots[0].isPack)
            if ((_.isNil(order.productSnapshots[0].isPack) || order.productSnapshots[0].isPack === false) && listingBrand !== "WHOLE_FIT") {
                const cartAddonWidgetPromise = this.getCartAddonWidget(mealSlot, singleOffersResponse, menuAvailabilityResult, order, user.id, deviceId, userContext, areaId, listingBrand)
                const cartAddonWidget = await cartAddonWidgetPromise
                if (cartAddonWidget)
                    widgetViewPromises.push(Promise.resolve(cartAddonWidget))
            }
            const cartListWidgetAndBilling = await cartListWidgetAndBillingPromise
            total = cartListWidgetAndBilling.billing.total
            widgetViewPromises.push(Promise.resolve(cartListWidgetAndBilling.widget))
        }
        const widgets = await Promise.all(widgetViewPromises)
        const action: Action | DeliverySlotAction = await this.getPageAction(userContext, order, numProducts, arrivalInfo, onDemandText, mealSlotDeliverySlots, total, deliveryInfo, isCafe, isDeliverySlotEmpty, undefined, undefined, undefined, billingInfo.currency)

        return {
            widgets: widgets,
            pageActions: [action],
            actions: [action],
            alertInfo: alertInfo ? alertInfo : await this.getAlertInfo(deliveryInfo, availabilities, order.productSnapshots, numProducts, user, userContext, isOfferOverriden),
            cart: {
                deliveryInfos: deliveryInfo,
                startDate: order.productSnapshots[0].option.startDate
            }
        }
    }

    getDeliveryInfoWidget(userContext: UserContext, order: Order, mealSlotDeliverySlots: { [mealSlot: string]: string[] },
                          isKiosk: boolean, arrivalInfo: ArrivalInfo, deliveryInfos: ClientDeliveryInfo[],
                          isDeliverySlotEmpty: boolean, areaTz: Timezone): ExpandableListWidget {
        const orderProductOption = order.productSnapshots[0].option
        return {
            widgetType: "EXPANDABLE_LIST_WIDGET",
            title: undefined,
            defaultOpenIndex: 0,
            widgets: [
                ..._.map(order.eatOptions.deliveryInfo, (deliveryInfo) => {
                    const allSlots = SlotUtil.getDeliverySlotsForMealSlotAndChannel(<MealSlot>deliveryInfo.mealSlot, isKiosk ? "KIOSK" : "ONLINE")
                    const clientInfo = _.find(deliveryInfos, (info) => {
                        return info.mealSlot === deliveryInfo.mealSlot
                    })
                    return new DeliveryInfoWidget(userContext, deliveryInfo.address, deliveryInfo.weekendAddress, orderProductOption.weekendEnabled,
                        orderProductOption.startDate, mealSlotDeliverySlots[deliveryInfo.mealSlot], allSlots, deliveryInfo.deliverySlot,
                        arrivalInfo, <MealSlot>deliveryInfo.mealSlot, clientInfo.isCopySelected, isDeliverySlotEmpty, areaTz)
                })
            ],
            allowMultipleOpen: false
        }
    }

    async getFitClubCheckoutSavingsWidget(userContext: UserContext, fitCashEarned: number, fitClubDeliverySavings: number, isOnlyFitClubAndOfferOrderId?: boolean): Promise<FitClubCheckoutSavingsWidget> {
        let fitClubSavingsText: string = undefined
        if (fitCashEarned) {
            if (isOnlyFitClubAndOfferOrderId) {
                fitClubSavingsText = "Earn " + fitCashEarned + " fitcash when your meal is delivered"
            } else {
                fitClubSavingsText = "Earn upto " + fitCashEarned + " fitcash"
            }
        }
        if (fitClubDeliverySavings && !isOnlyFitClubAndOfferOrderId) {
            if (fitClubSavingsText) {
                fitClubSavingsText += " and free delivery"
            } else {
                fitClubSavingsText = "Free delivery"
            }
        }
        const fitcashBalance = await this.fitcashService.balance(userContext.userProfile.userId, userContext.userProfile.city.country.currencyCode)
        const amount = fitcashBalance.balance ? (fitcashBalance.balance / 100) : 0
        return {
            widgetType: "FITCLUB_CHECKOUT_SAVINGS_WIDGET",
            title: fitClubSavingsText,
            fitcashBalanceText: amount ? "Fitcash Balance: " + amount : undefined
        }
    }

    async getPageAction(userContext: UserContext, order: Order, numProducts: number, arrivalInfo: ArrivalInfo, onDemandText: string, mealSlotDeliverySlots: { [mealSlot: string]: string[] }, total: number, deliveryInfo: ClientDeliveryInfo[], isCafe: boolean, isDeliverySlotEmpty: boolean, mealSlot?: MenuType, fitcashBalance?: number, isFitcashEnabled?: boolean, currency?: string) {
        let action: Action | DeliverySlotAction
        if (!userContext.sessionInfo.isUserLoggedIn) {
            action = {
                actionType: "SHOW_ALERT_MODAL",
                title: "Login", meta: {
                    title: "Login Required!",
                    subTitle: "Please login to continue",
                    actions: [{actionType: "LOGOUT", title: "Login"}]
                }
            }
            return action
        }
        const tz = userContext.userProfile.timezone
        const isPack = order.productSnapshots[0].isPack
        const isPackBundle = order.productSnapshots[0].packType === "BUNDLE"
        const isWeekendEnabled = order.productSnapshots[0].option.weekendEnabled
        if (numProducts === 0) {
            // Handling case where there 0 items due to checkout
            action = {
                actionType: "POP_ACTION",
                url: "curefit://eatfitclp",
                title: "Ok"
            }
        }
        const mealSlots = _.keys(mealSlotDeliverySlots)
        mealSlots.sort((slotA, slotB) => {
            return ALL_MEAL_SLOTS.indexOf(<MealSlot>slotA) - ALL_MEAL_SLOTS.indexOf(<MealSlot>slotB)
        })
        const everyReturn = mealSlots.every((mealSlot) => {
            if (!_.isNil(action)) {
                return false
            }
            const availableSlots = mealSlotDeliverySlots[mealSlot]
            const deliveryInfoIndex = deliveryInfo.findIndex((info) => {
                return info.mealSlot === mealSlot
            })
            const info = deliveryInfo[deliveryInfoIndex]
            if (_.isNil(info) || info.isWeekdayAddressServiceable === false || _.isNil(info.addressId)) {
                action = {
                    actionType: "CHANGE_ADDRESS",
                    url: "curefit://selectaddress?pageFrom=cart_checkout",
                    meta: {
                        isWeekendAddress: false,
                        mealSlot: mealSlot
                    },
                    title: "Pick " + (isPackBundle ? mealSlot.toLowerCase() + " " : "") + (isPack ? "weekday address" : "address")
                }
            } else if (isWeekendEnabled && (info.isWeekendAddressServiceable === false || _.isNil(info.weekendAddressId))) {
                action = {
                    actionType: "CHANGE_WEEKEND_ADDRESS",
                    meta: {
                        isWeekendAddress: true,
                        mealSlot: mealSlot
                    },
                    url: "curefit://selectaddress?pageFrom=cart_checkout",
                    title: "Pick " + (isPackBundle ? (mealSlot.toLowerCase() + " ") : "") + "weekend address"
                }
            } else if (info.deliverySlot === undefined || (isDeliverySlotEmpty && onDemandText === "")) {
                const allSlots: DeliverySlot[] = isCafe ? SlotUtil.getAllDeliverySlotForCafe() : mealSlot === "ALL" ? [] : SlotUtil.getDeliverySlotsForMealSlotAndChannel(<MealSlot>mealSlot, "ONLINE")
                const slots = _.map(allSlots, (slot) => {
                    const slotDetail: {
                        deliverySlot: string
                        title: string,
                        isEnabled: boolean
                    } = {
                        deliverySlot: slot.slotId,
                        title: MealUtil.getDisplayTextForSlot(slot, undefined, arrivalInfo, onDemandText, isCafe),
                        isEnabled: _.findIndex(availableSlots, (availableSlot) => {
                            return availableSlot === slot.slotId
                        }) !== -1
                    }
                    return slotDetail
                })
                if (!_.isNil(onDemandText) && onDemandText.length > 0) {
                    slots.unshift({
                        deliverySlot: SlotUtil.ON_DEMAND_SLOT,
                        title: MealUtil.getSlotDisplayText(SlotUtil.ON_DEMAND_SLOT, undefined, arrivalInfo, onDemandText, isCafe),
                        isEnabled: true
                    })
                }
                action = {
                    title: "Select delivery slot",
                    actionType: "CHANGE_SLOT",
                    mealSlot: mealSlot as MenuType,
                    slots: slots
                }
            }
            return true
        })
        if (_.isNil(action)) {
            let totalAmountPayable = order.totalAmountPayable
            let freeTitle = "Get for free"
            if (isFitcashEnabled && fitcashBalance > 0) {
                if (fitcashBalance > totalAmountPayable) {
                    freeTitle = "Pay with Fitcash"
                    totalAmountPayable = 0
                } else {
                    totalAmountPayable = totalAmountPayable - fitcashBalance
                }
            }
            totalAmountPayable = Math.round(totalAmountPayable)
            this.logger.info(`[FITCASH_BILLING_WIDGET]: ${fitcashBalance} ${totalAmountPayable}`)
            if (totalAmountPayable === 0) {
                action = {
                    title: freeTitle,
                    actionType: "PAY_FREE"
                }
            } else {
                action = {
                    title: `Pay ${AppUtil.getCurrencySymbol(currency)} ${totalAmountPayable}`,
                    actionType: "CART_PAY_NOW",
                    url: "curefit://payment"
                }
            }
        }
        return action
    }

    async buildCartReviewViewV1(userContext: UserContext, vertical: string, order: Order, payByMembershipDetails: PayByMembershipDetails, cartReviewPayload: CartReviewPayload, userId: string, deviceId: string, userAttributeClient: IUserAttributeClient,
                                user: User, serviceCenterId?: number, couponData?: UserOfferResponseV2, minimumEligibleStartDate?: string, boosterPack?: OfflineFitnessPack, addonProductIdsFromReqBody?: string[], correspondingSkuPlusPackId?: string, correspondingSkuPlusOrder?: Order, correspondingSkuPlusPack?: AugmentedOfflineFitnessPack): Promise<CartReviewView> {
        const productId: string = order.products[0].option && !_.isEmpty(order.products[0].option.parentProductCode) ? order.products[0].option.parentProductCode : order.products[0].productId
        const baseProduct: Product = await this.catalogueServicePMS.getCatalogProduct(productId)
        const widgetViewPromises: Promise<WidgetView>[] = []
        const footerWidgetViewPromises: Promise<WidgetView>[] = []
        let pageAction: Action | SelectCenterAction
        let helpAction: Action
        let initAction: Action
        let alertInfo: AlertInfo
        const userAgent = userContext.sessionInfo.userAgent
        const timezone = userContext.userProfile.timezone
        let themeType: string = "CLASSIC"
        const tz = timezone
        let analyticsData
        let footerPadding
        let topPadding
        let doRemoveContinuePurchaseCTA: boolean = true
        userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
        const showBoosterPackSelectionModal: boolean = boosterPack && !addonProductIdsFromReqBody
        let doesUserBelongsToBoosterPackSelectionPreModalSegment = false
        let doesUserBelongsToSkuPlusBottomsheetExperiment = false
        let doesUserBelongsToNewCheckoutPageExperiment = false
        let doesUserBelongsToSkuPlusUpgradeBanner = false
        if (order.source !== "SUGARFIT_WEBSITE") {
            doesUserBelongsToBoosterPackSelectionPreModalSegment =
              await AppUtil.doesUserBelongsToBoosterPackSelectionPreModal(
                userContext,
                this.serviceInterfaces.segmentService
              )
            doesUserBelongsToSkuPlusBottomsheetExperiment = await AppUtil.doesUserBelongsToSkuPlusBottomsheetExperiment(userContext, this.serviceInterfaces.segmentService)
            doesUserBelongsToNewCheckoutPageExperiment = await AppUtil.doesUserBelongsToNewCheckoutPageExperiment(userContext, this.serviceInterfaces.segmentService)
            doesUserBelongsToSkuPlusUpgradeBanner = await AppUtil.doesUserBelongsToSkuPlusUpgradeBanner(userContext, this.serviceInterfaces.segmentService)
        }
        const showAddOnFomoDetails: boolean = boosterPack && addonProductIdsFromReqBody?.length === 0
        const isBoosterDetailsPresent: boolean = boosterPack && addonProductIdsFromReqBody?.length > 0

        await this.logger.info(`PT_PILOT logs userId ${userContext?.userProfile?.userId} baseProductType ${baseProduct.productType}`)
        if (baseProduct.productType === "BUNDLE") {
            const diagnosticsProduct = <DiagnosticProduct>baseProduct
            const isPhysioOrLC = diagnosticsProduct.subCategoryCode === "PHYSIOTHERAPY" || diagnosticsProduct.subCategoryCode === "NUTRITIONIST"
            const patientId: number = order.products[0].option.patientId
            const patientPromise = this.healthfaceService.getPatientDetails(patientId)
            const isTransformProduct: boolean = diagnosticsProduct.subCategoryCode === TRANSFORM_SUB_CATEGORY_CODE_TYPE.TRANSFORM
            const isTransformPlusProduct: boolean = diagnosticsProduct.subCategoryCode === TRANSFORM_SUB_CATEGORY_CODE_TYPE.TRANSFORM_PLUS
            const isTransformEliteBootcampProduct: boolean = !_.isNil(diagnosticsProduct.infoSection?.productTypeMeta?.isBootcampPlusElite) && diagnosticsProduct.infoSection?.productTypeMeta?.isBootcampPlusElite
            const isBootcampOutdoorProduct: boolean = !_.isNil(diagnosticsProduct.infoSection?.productTypeMeta?.isBootcampOutdoor) && diagnosticsProduct.infoSection?.productTypeMeta?.isBootcampOutdoor
            const isBootcampPulseProduct: boolean = !_.isNil(diagnosticsProduct.infoSection?.productTypeMeta?.isBootcampOutdoor) && diagnosticsProduct.infoSection?.productTypeMeta?.isBootcampPulse
            const isTransformBootcampOnlyProduct: boolean = diagnosticsProduct.subCategoryCode === TRANSFORM_SUB_CATEGORY_CODE_TYPE.BOOTCAMP
            const isTransformBootcampProduct: boolean = isTransformEliteBootcampProduct || isTransformBootcampOnlyProduct
            const isTransformLiftProduct: boolean = diagnosticsProduct.subCategoryCode === TRANSFORM_SUB_CATEGORY_CODE_TYPE.LIFT
            const isTransformNCProduct: boolean = diagnosticsProduct.subCategoryCode === TRANSFORM_SUB_CATEGORY_CODE_TYPE.TRANSFORM_NC
            const isTransformPTProduct: boolean = diagnosticsProduct.subCategoryCode === TRANSFORM_SUB_CATEGORY_CODE_TYPE.TRANSFORM_PT
            const isTransformFCProduct: boolean = diagnosticsProduct.subCategoryCode === TRANSFORM_SUB_CATEGORY_CODE_TYPE.TRANSFORM_FC

            // Separating out Flutter app flows
            if (AppUtil.isFromFlutterAppFlow(userContext) && ["MIND_THERAPY", "GYMFIT_PERSONAL_TRAINING"].includes(diagnosticsProduct.subCategoryCode)) {
                if ("MIND_THERAPY" === diagnosticsProduct.subCategoryCode) {
                    doRemoveContinuePurchaseCTA = false
                    const productType: ProductType = <ProductType>diagnosticsProduct.subCategoryCode
                    const orientation: Orientation = "TOP"
                    const healthfaceTenant: HealthfaceTenant = CareUtil.getHealthfaceTenant(diagnosticsProduct.subCategoryCode)
                    const healthfaceProduct = <BundleSessionSellableProduct>(await this.healthfaceService.getProductInfoDetailsCached("BUNDLE", diagnosticsProduct.subCategoryCode, productId, healthfaceTenant))[0].baseSellableProduct
                    const {howItWorksItemV2, packHowItHelpV2} = CareUtil.getPackContent(healthfaceProduct)
                    widgetViewPromises.push(this.getPaymentSummaryWidget(
                        userContext,
                        order,
                        productType,
                        baseProduct.title,
                        healthfaceProduct.infoSection.numberOfSessions.toString(),
                        healthfaceProduct.infoSection.cartCheckoutTitle,
                        healthfaceProduct.infoSection.numberOfSessions > 1 ? "SESSIONS" : "SESSION"
                    ))
                    const offerWidgetPromise = this.addOfferAddonWidget(order, diagnosticsProduct.subCategoryCode)
                    if (offerWidgetPromise) {
                        widgetViewPromises.push(offerWidgetPromise)
                    }
                    widgetViewPromises.push(Promise.resolve(CareUtil.getWhatYouGetInstructionWidget(packHowItHelpV2, userContext, orientation)))
                    widgetViewPromises.push(Promise.resolve(CareUtil.getHowItWorksWidget(howItWorksItemV2, userContext.sessionInfo.userAgent)))
                } else if ("GYMFIT_PERSONAL_TRAINING" === diagnosticsProduct.subCategoryCode) {
                    await this.logger.info(`PT_PILOT logs userId ${userContext?.userProfile?.userId} diagnosticsProduct.subCategoryCode ${diagnosticsProduct.subCategoryCode}`)
                    const productType: ProductType = <ProductType>diagnosticsProduct.subCategoryCode
                    widgetViewPromises.push(this.getPaymentSummaryWidget(
                        userContext,
                        order,
                        productType,
                        baseProduct.title,
                        baseProduct.productSpec.numberOfSessions.toString(),
                        "Personal Training",
                        baseProduct.productSpec.numberOfSessions > 1 ? "SESSIONS" : "SESSION"
                    ))
                    widgetViewPromises.push(this.addGymPTTrainersWidget(cartReviewPayload))
                    const offerWidgetPromise = this.addGymPTOfferAddonWidget(order)
                    if (offerWidgetPromise) {
                        widgetViewPromises.push(offerWidgetPromise)
                    }
                    // widgetViewPromises.push(this.addNoteWidget())
                }
            } else if (diagnosticsProduct.subCategoryCode === "MIND_THERAPY" || diagnosticsProduct.subCategoryCode === "PERSONAL_TRAINING" || isPhysioOrLC || diagnosticsProduct.subCategoryCode === "LIVE_PERSONAL_TRAINING" || diagnosticsProduct.subCategoryCode === "LIVE_SGT" || CareUtil.isTransformTenant(diagnosticsProduct.subCategoryCode)) {
                widgetViewPromises.push(this.getCartBaseProductSummaryWidget(order, userAgent, <DiagnosticProduct>baseProduct, patientPromise))
                const isLivePTPackPageWithOffersSupported = AppUtil.isLivePTPackPageWithOffersSupported(userContext)
                const isWebContext = AppUtil.isWeb(userContext)
                const isLivePT = diagnosticsProduct.subCategoryCode === "LIVE_PERSONAL_TRAINING"
                const shouldShowStartDateInfoPT = isLivePT && (isLivePTPackPageWithOffersSupported || isWebContext)
                const shouldShowStartDateInfoSGT = diagnosticsProduct.subCategoryCode === "LIVE_SGT" && isLivePTPackPageWithOffersSupported
                const shouldShowStartDateInfo = shouldShowStartDateInfoSGT || shouldShowStartDateInfoPT
                if (shouldShowStartDateInfo && cartReviewPayload.orderProducts[0]?.option?.startTime && !isTransformBootcampProduct && !isTransformLiftProduct) {
                    widgetViewPromises.push(this.getStartDateInfoWidget(cartReviewPayload.orderProducts[0].option.startTime, tz, isLivePT && isWebContext))
                }
                if ((diagnosticsProduct.subCategoryCode === "LIVE_PERSONAL_TRAINING" || diagnosticsProduct.subCategoryCode === "LIVE_SGT") && payByMembershipDetails && payByMembershipDetails.cult && payByMembershipDetails.cult.isMember) {
                    widgetViewPromises.push(this.getCultPayByMembershipWidget(userContext, order, payByMembershipDetails, MembershipPaymentType.CULT))
                }
                if ((diagnosticsProduct.subCategoryCode === "LIVE_PERSONAL_TRAINING" || diagnosticsProduct.subCategoryCode === "LIVE_SGT") && payByMembershipDetails && payByMembershipDetails.mind && payByMembershipDetails.mind.isMember) {
                    widgetViewPromises.push(this.getCultPayByMembershipWidget(userContext, order, payByMembershipDetails, MembershipPaymentType.MIND))
                }
                let productType: ProductType
                if (diagnosticsProduct.subCategoryCode === "LIVE_PERSONAL_TRAINING" || diagnosticsProduct.subCategoryCode === "LIVE_SGT" || CareUtil.isTransformTenant(diagnosticsProduct.subCategoryCode)) {
                    productType = <ProductType>diagnosticsProduct.subCategoryCode
                }
                if (diagnosticsProduct.subCategoryCode === "LIVE_PERSONAL_TRAINING" && userContext.sessionInfo.appVersion >= 8.58) {
                    if (!userContext.userProfile.promiseMapCache) {
                        userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
                    }
                    const isUserRenewalUser = await this.serviceInterfaces.segmentService.doesUserBelongToSegment("b99f4bf8-aa6c-474d-8d75-238a8a74308b", userContext)
                    if (isUserRenewalUser) {
                        widgetViewPromises.push(this.getLivePTRenewalNoteWidget(userContext))
                    }
                }
                if (CareUtil.isTransformTenant(diagnosticsProduct.subCategoryCode)) {

                    const isWaitlistSupported = await AppUtil.isTransformWaitlistSupported(userContext)
                    if (isWaitlistSupported) {
                        pageAction = {
                            meta: {
                                consentInfo: {
                                    message: isTransformPlusProduct ? "I consent to being contacted via whatsapp and call by my coaches during this membership" : "I consent to being contacted via whatsapp by my coach during this membership"
                                },
                                freePayment: order.totalAmountPayable === 0 && AppUtil.isTransformFreePaymentSupported(userContext),
                            },
                            title: `Pay ${RUPEE_SYMBOL}${order.totalAmountPayable}`,
                            actionType: order.totalAmountPayable === 0 && AppUtil.isTransformFreePaymentSupported(userContext) ? "CARE_PAY_FREE" : "CARE_CART_PAY_NOW",
                            url: order.totalAmountPayable === 0 && AppUtil.isTransformFreePaymentSupported(userContext) ? "curefit://transform_free" : "curefit://payment?vertical=CARE",
                        }
                    }
                    const suffix: string = diagnosticsProduct.infoSection.packTitle
                    const hexColorBootcamp: string = "#FC55DA"
                    if (isTransformNCProduct || isTransformFCProduct) {
                        const duration = ceil(diagnosticsProduct.duration / 30)
                        const footerDuration = duration > 1 ? "MONTHS" : "MONTH"
                        widgetViewPromises.push(this.getTfBootcampPaymentSummaryWidget(userContext, order, productType, baseProduct.title, duration.toString(), "", footerDuration, suffix, hexColorBootcamp))
                    } else if (isTransformPTProduct) {
                        const footerDuration = baseProduct.productSpec.numberOfSessions > 1 ? "SESSIONS" : "SESSION"
                        const duration = baseProduct.productSpec.numberOfSessions.toString()
                        widgetViewPromises.push(this.getTfBootcampPaymentSummaryWidget(userContext, order, productType, baseProduct.title, duration, "", footerDuration, suffix, hexColorBootcamp))
                    } else if (isTransformBootcampProduct) {
                        const footerDuration = isTransformEliteBootcampProduct ? "MONTHS" : "WEEKS"
                        const duration = isTransformEliteBootcampProduct ? ceil(diagnosticsProduct.duration / 30).toString() : floor(diagnosticsProduct.duration / 7).toString()
                        widgetViewPromises.push(this.getTfBootcampPaymentSummaryWidget(userContext, order, productType, baseProduct.title, duration, "", footerDuration, suffix, hexColorBootcamp))
                    } else if (isTransformLiftProduct) {
                        const duration = floor(diagnosticsProduct.duration / 7).toString()
                        const footerDuration = "WEEKS"
                        widgetViewPromises.push(this.getTfBootcampPaymentSummaryWidget(userContext, order, productType, baseProduct.title, duration, "", footerDuration, suffix, hexColorBootcamp))
                    } else if (AppUtil.isTransformStartDateSelectionSupported(userContext)) {
                        widgetViewPromises.push(this.getTfBootcampPaymentSummaryWidget(userContext, order, productType, baseProduct.title, ceil(diagnosticsProduct.duration / 30).toString(), "", "MONTHS", suffix, hexColorBootcamp))
                    } else {
                        widgetViewPromises.push(this.getTransformPaymentSummaryWidget(userContext, order, productType, baseProduct.title, ceil(diagnosticsProduct.duration / 30).toString(), "", "MONTHS"))
                    }
                } else {
                    widgetViewPromises.push(this.getPaymentSummaryWidget(userContext, order, productType, baseProduct.title))
                }
                // if (AppUtil.isBootcampReferralSupported(userContext) && (isTransformProduct || isTransformPlusProduct || isTransformBootcampProduct)) {
                //     widgetViewPromises.push(TransformUtil.getTimerBannerWidget(userContext, this.widgetBuilder, this.serviceInterfaces, diagnosticsProduct.subCategoryCode))
                // }
                if (isTransformBootcampOnlyProduct && isBootcampOutdoorProduct) {
                    widgetViewPromises.push(TransformUtil.getBootcampOutdoorBannerWidget(userContext, this.widgetBuilder, this.serviceInterfaces, diagnosticsProduct.subCategoryCode))
                }
                if (diagnosticsProduct.subCategoryCode === "LIVE_PERSONAL_TRAINING" || diagnosticsProduct.subCategoryCode === "LIVE_SGT" || CareUtil.isTransformTenant(diagnosticsProduct.subCategoryCode)) {
                    const offerWidgetPromise = this.addOfferAddonWidget(order, diagnosticsProduct.subCategoryCode, isTransformBootcampProduct || isTransformLiftProduct)
                    if (offerWidgetPromise) {
                        widgetViewPromises.push(offerWidgetPromise)
                    }
                    let orientation: Orientation
                    if (CareUtil.isTransformTenant(diagnosticsProduct.subCategoryCode)) {

                        isWebContext ? orientation = "RIGHT" : orientation = "TOP"
                        const healthfaceTenant: HealthfaceTenant = CareUtil.getHealthfaceTenant(diagnosticsProduct.subCategoryCode)
                        const startDateInfo = await this.healthfaceService.getMembershipStartDateDetails(userContext.userProfile.userId, productId, healthfaceTenant)
                        const healthfaceProduct = <BundleSessionSellableProduct>(await this.healthfaceService.getProductInfoDetailsCached("BUNDLE", diagnosticsProduct.subCategoryCode, productId, healthfaceTenant))[0].baseSellableProduct
                        let howItWorksItem, packHowItHelp, howItWorksItemWeb
                        healthfaceProduct.infoSection.children.map(infoSection => {
                            switch (infoSection.type) {
                                case "PACK_STEPS":
                                    howItWorksItem = infoSection
                                    break
                                case "PACK_STEPS_V2":
                                    howItWorksItemWeb = infoSection
                                    break
                                case "WHAT_YOU_GET":
                                    packHowItHelp = infoSection
                                    break
                            }
                        })
                        if (isWebContext) {
                            widgetViewPromises.push(Promise.resolve(CareUtil.getHowItWorksWidget(howItWorksItemWeb, userContext.sessionInfo.userAgent)))
                            widgetViewPromises.push(Promise.resolve(CareUtil.getBillingAddress(orientation, false, startDateInfo)))
                        } else {
                            if (isTransformBootcampProduct || isTransformLiftProduct) {
                                // widgetViewPromises.push(Promise.resolve(TransformUtil.getCoachInfoBootcamp()))
                                widgetViewPromises.push(Promise.resolve(this.getNoCostEMIWidget(order, diagnosticsProduct, diagnosticsProduct.subCategoryCode)))
                                widgetViewPromises.push(Promise.resolve(TransformUtil.getWhatYouGetInstructionWidget(packHowItHelp, userContext)))
                                isTransformEliteBootcampProduct ? widgetViewPromises.push(Promise.resolve(TransformUtil.getCultpassElitePolicyWidget())) : null
                                widgetViewPromises.push(Promise.resolve(TransformUtil.getHowItWorksWidget(howItWorksItem, userContext.sessionInfo.userAgent, null)))
                                if (AppUtil.isBatchSelectionSupportedForBootcamp(userContext)) {
                                    footerWidgetViewPromises.push(Promise.resolve(TransformUtil.getCheckoutActionsV2Bootcamp(diagnosticsProduct.productId, isTransformLiftProduct, isBootcampPulseProduct)))
                                } else {
                                    footerWidgetViewPromises.push(Promise.resolve(TransformUtil.getCheckoutActionsBootcamp(diagnosticsProduct.productId)))
                                }
                            }
                            if (!isTransformPlusProduct && !isTransformBootcampProduct && !isTransformLiftProduct && !isTransformPTProduct && !isTransformNCProduct && !isTransformFCProduct) {
                                widgetViewPromises.push(Promise.resolve(CareUtil.getWhatYouGetInstructionWidget(packHowItHelp, userContext, orientation)))
                            }
                            if (!isTransformBootcampProduct && !isTransformLiftProduct) {
                                widgetViewPromises.push(Promise.resolve(CareUtil.getHowItWorksWidget(howItWorksItem, userContext.sessionInfo.userAgent, null, TransformUtil.getHowItWorksLayoutTransform())))
                            }
                            if (isTransformProduct || isTransformPlusProduct || isTransformPTProduct || isTransformNCProduct || isTransformFCProduct) {
                                themeType = "DAY_NIGHT"
                                const startDateData = await this.healthfaceService.getMembershipStartDateDetails(userId, diagnosticsProduct.productId, healthfaceTenant)
                                footerWidgetViewPromises.push(Promise.resolve(TransformUtil.getCheckoutActionsTransform(diagnosticsProduct.productId, diagnosticsProduct.subCategoryCode, order.totalAmountPayable, userContext, startDateData)))
                            }
                        }
                        // widgetViewPromises.push(Promise.resolve(CareUtil.getStartDateInfo(startDateInfo)))
                        // const waitlistStatus = await this.healthfaceService.getCoachWaitlistStatusForUser(userId, "TRANSFORM")
                        // if (waitlistStatus.isFitStartSaleEnabled) {
                        //     widgetViewPromises.push(Promise.resolve(
                        //         {
                        //             widgetType: "FORMATTED_TEXT_WIDGET",
                        //             header: {
                        //                 title: "Attention",
                        //             },
                        //             data: [
                        //                 {
                        //                     text: "Your membership will start from Jan 1st, after New Years! Make sure you schedule your first call with your coach then.",
                        //                     fontWeight: "MEDIUM",
                        //                     fontSize: 15,
                        //                     fontColor: "rgba(0, 0, 0, 0.5)",
                        //                     lineHeight: 25
                        //                 }
                        //             ]
                        //         }
                        //     ))
                        // }
                        // if (!isTransformPlusProduct && !isTransformBootcampProduct && !isTransformLiftProduct) {
                        //     widgetViewPromises.push(Promise.resolve(
                        //         {
                        //             widgetType: "FORMATTED_TEXT_WIDGET",
                        //             header: {
                        //                 title: "Still not sure?",
                        //             },
                        //             data: [
                        //                 {
                        //                     text: "Cancel within the first 7 days and we'll refund you the full amount.",
                        //                     fontWeight: "MEDIUM",
                        //                     fontSize: 15,
                        //                     fontColor: "rgba(0, 0, 0, 0.5)",
                        //                     lineHeight: 25
                        //                 }
                        //             ]
                        //         }
                        //     ))
                        // }
                    }
                }
            } else if (diagnosticsProduct.subCategoryCode === "CHRONIC_CARE_SUBSCRIPTION") {
                doRemoveContinuePurchaseCTA = false
                if (_.get(diagnosticsProduct, "productSpec.isRenewal", false)) {
                    widgetViewPromises.push(this.getChronicCarePackCheckoutWidget(diagnosticsProduct))
                    pageAction = {
                        title: `Pay ${RUPEE_SYMBOL}${order.totalAmountPayable}`,
                        actionType: "CARE_CART_PAY_NOW",
                        url: "curefit://payment?vertical=CARE"
                    }
                    const minStartDateOffsetInDays = _.get(diagnosticsProduct, "productSpec.minStartDateDelayDays", 0)
                    const minStartDateInMillis = _.get(diagnosticsProduct, "productSpec.minStartDateMillis", TimeUtil.getDateNow(timezone).getTime())
                    let minStartDate
                    if (minStartDateInMillis < TimeUtil.getDateNow(timezone).getTime()) {
                        minStartDate = TimeUtil.addToDate(TimeUtil.getDateNow(timezone), timezone, minStartDateOffsetInDays, "days")
                    } else {
                        minStartDate = TimeUtil.addToDate(TimeUtil.parseDateFromEpoch(minStartDateInMillis), timezone, minStartDateOffsetInDays, "days")
                    }
                    pageAction.minStartDate = minStartDate.toDate().getTime()
                    const maxDelayDays = _.get(diagnosticsProduct, "productSpec.maxDelayStartDays", 0)
                    if (maxDelayDays > 0) {
                        pageAction.maxStartDate = TimeUtil.addToDate(minStartDate.toDate(), timezone, maxDelayDays, "days")
                    }
                    if (userAgent === "DESKTOP") {
                        pageAction.orientation = "RIGHT"
                    }
                } else {
                    widgetViewPromises.push(this.getChronicCareCreativeWidget(userContext, diagnosticsProduct.productId))
                }
                widgetViewPromises.push(this.getChronicCarePaymentSummaryWidget(userContext, order, diagnosticsProduct))
            } else {
                doRemoveContinuePurchaseCTA = false
                widgetViewPromises.push(this.getCartBaseProductSummaryWidget(order, userAgent, diagnosticsProduct, patientPromise))
                // widgetViewPromises.push(this.getPatientSummaryWidget(userAgent, order.products, patientPromise))
                if (diagnosticsProduct.subCategoryCode === "MP" || diagnosticsProduct.subCategoryCode === "MP_SUBS" || diagnosticsProduct.subCategoryCode === "MP_V2") {
                    widgetViewPromises.push(this.getManagedPlanPaymentSummaryWidget(userContext, order))
                } else {
                    if (
                        !CareUtil.isPartOfConsultationPackProducts(diagnosticsProduct.subCategoryCode) &&
                        !CareUtil.isPartOfSkinProducts(diagnosticsProduct.subCategoryCode) &&
                        diagnosticsProduct.subCategoryCode !== "AI_MG"
                    ) {
                        const addonWidgetPromise = this.getCareCartAddonsWidget(userContext, userAgent, order, diagnosticsProduct, userId, deviceId)
                        addonWidgetPromise && widgetViewPromises.push(addonWidgetPromise)
                        const isCollapsibleOfferWidgetSupported = userContext.sessionInfo.appVersion >= CARE_COLLAPSIBLE_OFFER_SUPPORTED
                        const offerWidgetPromise = isCollapsibleOfferWidgetSupported ? this.getCollapsibleOfferWidget(userContext, false) : this.getOfferCalloutWidget(userContext)
                        offerWidgetPromise && widgetViewPromises.push(offerWidgetPromise)
                        const showHomeSampleCharges = ["DIAG_PACK", "HCU_PACK"].indexOf(diagnosticsProduct.subCategoryCode) !== -1
                        const paymentDetailPromise = this.getHCUPaymentSummaryWidget(userContext, order, showHomeSampleCharges)
                        paymentDetailPromise && widgetViewPromises.push(paymentDetailPromise)
                    } else {
                        if (CareUtil.isLHRProduct(diagnosticsProduct.clubCode)) {
                            const selectedBodyPartIds: string[] = _.get(order.products[0], "option.selectedBodyParts")
                            widgetViewPromises.push(this.getLHRBodyPartsWidget(selectedBodyPartIds, diagnosticsProduct))
                        }
                        widgetViewPromises.push(this.getPaymentSummaryWidget(userContext, order))
                    }
                }
            }
            if (order.totalAmountPayable === 0 && !CareUtil.isTransformTenant(diagnosticsProduct.subCategoryCode)) {
                const title = "Get for free"
                pageAction = {
                    title: title,
                    actionType: "CARE_PAY_FREE"
                }
                if (userAgent === "DESKTOP") {
                    pageAction.orientation = "RIGHT"
                }
            } else if (!pageAction) {
                pageAction = {
                    title: `Pay ${RUPEE_SYMBOL}${order.totalAmountPayable}`,
                    actionType: "CARE_CART_PAY_NOW",
                    url: "curefit://payment?vertical=CARE"
                }
                if (userAgent === "DESKTOP") {
                    pageAction.orientation = "RIGHT"
                }
            }
        } else if (baseProduct.productType === "CONSULTATION") {
            const consultationProduct = <ConsultationProduct>baseProduct
            let isMPV2 = false
            // order.products.map(orderproduct => {
            //     widgetViewPromises.push(this.getMultiConsultationCard(orderproduct))
            // })
            const parentPromises = order.products.map(orderproduct => {
                if (orderproduct.option.doctorId) {
                    widgetViewPromises.push(this.getMultiConsultationCard(orderproduct, userContext))
                }
                if (orderproduct.option.parentBookingId && orderproduct.option.parentBookingId !== -1) {
                    return this.healthfaceService.getBookingDetail(orderproduct.option.parentBookingId)
                } else {
                    return null
                }
            })
            const parentBookings = await Promise.all(parentPromises)
            if (parentBookings) {
                parentBookings.map((parentBooking: any) => {
                    if (parentBooking) {
                        isMPV2 = parentBooking.booking.subCategoryCode === "MP_V2_OT" || parentBooking.booking.subCategoryCode === "MP_V2"
                    }
                })
            }
            if (order.totalAmountPayable === 0) {
                const offline: boolean = consultationProduct.consultationMode === "INCENTRE"
                const instructions = isMPV2 ? CareUtil.getInstructionsForMPV2ConsultationAppointments() : offline ? this.tcDetailsPageConfig.offlineInstructions : this.tcDetailsPageConfig.onlineInstructions
                widgetViewPromises.push(Promise.resolve(new InstructionsWidget(instructions, undefined, true)))
            } else {
                widgetViewPromises.push(this.getPaymentSummaryWidget(userContext, order))
            }

            if (order.totalAmountPayable === 0) {
                pageAction = {
                    title: "Proceed to confirm",
                    actionType: "CARE_PAY_FREE"
                }
            } else {
                pageAction = {
                    title: `Pay ${RUPEE_SYMBOL}${order.totalAmountPayable}`,
                    actionType: "CARE_CART_PAY_NOW",
                    orientation: "RIGHT",
                    url: "curefit://payment"
                }
            }
        }

        else if (baseProduct.productType === "FITNESS" && baseProduct.isPack) {
            const isDropdownClose = await AppUtil.isAppUserInCheckoutDropdownExperiment(userContext, this.serviceInterfaces.segmentService)
            const cultProduct: AugmentedOfflineFitnessPack = await this.catalogueServicePMS.getAugmentedPackById(productId, {includeExhaustiveBenefits: true})
            const productType: ProductType = cultProduct.productType
            const productTitle = CatalogueServiceUtilities.getFitnessDisplayName(cultProduct)
            const isAllIndiaPack = CatalogueServiceUtilities.isAllIndiaPack(cultProduct)
            const isSelectPack = CatalogueServiceUtilities.getAccessLevel(cultProduct) === AccessLevel.CENTER
            const months = Math.floor(cultProduct.product.durationInDays / 30)
            let color = this.getRandomColor()
            if (months == 3) color = this.durationColorMap["3"]
            else if (months == 6) color = this.durationColorMap["6"]
            else if (months == 12) color = this.durationColorMap["12"]

            if (doesUserBelongsToNewCheckoutPageExperiment) {
                const bannerTimerWidget = this.buildBannerTimerWidget(userContext, CULT_NAS_PRICE_HIKE_BANNER_ID)
                if (bannerTimerWidget) {
                    widgetViewPromises.push(bannerTimerWidget)
                    topPadding = 70
                }
                widgetViewPromises.push(
                    this.createPaymentAndOfferDetailWidget(
                        userContext, months.toString(), months > 1 ? "MONTHS" : "MONTH", color, isSelectPack ? "SELECT" : "ELITE", baseProduct.productType,
                        cultProduct?.product?.productSubType, isSelectPack ? cultProduct?.clientMetadata?.centerName : null, order, isAllIndiaPack
                    )
                )
            } else {
                widgetViewPromises.push(this.getPaymentSummaryWidget(
                    userContext,
                    order,
                    productType,
                    productTitle,
                    months.toString(),
                    productTitle,
                    months > 1 ? "MONTHS" : "MONTH",
                    null,
                    true,
                    true,
                    color,
                    true,
                    !isDropdownClose,
                    boosterPack,
                    addonProductIdsFromReqBody,
                    cultProduct?.product?.productSubType
                ))
                const bannerTimerWidget = this.buildBannerTimerWidget(userContext, CULT_NAS_PRICE_HIKE_BANNER_ID)
                if (bannerTimerWidget) {
                    widgetViewPromises.push(bannerTimerWidget)
                }
            }

            let priceDifference = undefined
            if (correspondingSkuPlusOrder?.totalAmountPayable && order?.totalAmountPayable) {
                priceDifference = Math.ceil(correspondingSkuPlusOrder.totalAmountPayable - order.totalAmountPayable)
            }

            let exhaustivePackbenefitsDifference: any = undefined
            if (cultProduct?.augments?.exhaustiveBenefitList?.length > 0 && correspondingSkuPlusPack?.augments?.exhaustiveBenefitList?.length > 0) {
                exhaustivePackbenefitsDifference = PackUtils.compareExhaustiveBenefitLists(correspondingSkuPlusPack?.augments?.exhaustiveBenefitList, cultProduct?.augments?.exhaustiveBenefitList, true)
            }
            const emiWidget = this.addFitnessEMIWidget(order, cultProduct)
            if (doesUserBelongsToNewCheckoutPageExperiment) {
                if (emiWidget) {
                    widgetViewPromises.push(emiWidget)
                }
                const whatYouGetBenefitsWidget = this.getWhatYouGetBenefitsWidget(cultProduct, this.isPlusPack(cultProduct), isSelectPack, this.isLitePack(cultProduct), true)
                if (whatYouGetBenefitsWidget) {
                    widgetViewPromises.push(whatYouGetBenefitsWidget)
                }
            }

            if (this.isLitePack(cultProduct)) {
                // SKU BANNER FUP
                const horizontalUpgradeSkuBannerWidget = this.upgradeSkuBannerWidget(correspondingSkuPlusPackId, priceDifference, true, isSelectPack)
                if (horizontalUpgradeSkuBannerWidget) {
                    widgetViewPromises.push(horizontalUpgradeSkuBannerWidget)
                }
            } else {
                // SKU PLUS BANNER ELITE
                if (doesUserBelongsToSkuPlusUpgradeBanner && months && !this.isPlusPack(cultProduct) && !isAllIndiaPack
                && !isSelectPack) {
                    const horizontalUpgradeSkuPlusWidget = this.addHorizontalUpgradeSkuPlusWidget(exhaustivePackbenefitsDifference, correspondingSkuPlusPackId, priceDifference, true)
                    if (horizontalUpgradeSkuPlusWidget) {
                        widgetViewPromises.push(horizontalUpgradeSkuPlusWidget)
                    }
                }
            }

            if (showAddOnFomoDetails) { // BOOSTER PACK NO CASE
                const addOnFomoHorizontalWidget = this.addHorizontalFomoWidget(boosterPack, true)
                if (addOnFomoHorizontalWidget) {
                    widgetViewPromises.push(addOnFomoHorizontalWidget)
                }
            }

            if (!doesUserBelongsToNewCheckoutPageExperiment) {
                if (emiWidget) {
                    widgetViewPromises.push(emiWidget)
                }
                const offerWidgetPromise = this.addGenericOfferAddonWidget(order)
                if (offerWidgetPromise) {
                    widgetViewPromises.push(offerWidgetPromise)
                }
                if (this.isPlusPack(cultProduct)) {
                    const skuPlusBenfitsWidget = this.getSkuPlusBenefitsWidget(cultProduct, true)
                    if (skuPlusBenfitsWidget) {
                         widgetViewPromises.push(skuPlusBenfitsWidget)
                    }
                } else {
                    widgetViewPromises.push(this.addEliteWhatYouGet(cultProduct, false, isBoosterDetailsPresent, userContext))
                }
            }

            if (isBoosterDetailsPresent) { // BOOSTER PACK YES CASE
                const boosterBenefitsWidget = this.addBoosterBenefits(boosterPack, false, true)
                if (boosterBenefitsWidget) {
                    widgetViewPromises.push(boosterBenefitsWidget)
                }
            }

            const howItWorks = this.addEliteHowItWorks(cultProduct, false, this.isLitePack(cultProduct))
            if (howItWorks) {
                widgetViewPromises.push(howItWorks)
            }
            footerWidgetViewPromises.push(this.getEliteBottomCTA(order, userContext, cultProduct, boosterPack, showBoosterPackSelectionModal, doesUserBelongsToBoosterPackSelectionPreModalSegment))
            footerPadding = 220

            const issues: IssueDetailView[] = await this.serviceInterfaces.issueBusiness.getCultOrMindSubscriptionPrePurchaseIssues(cultProduct, userContext)
            helpAction = {
                title: "HELP",
                actionType: "REPORT_ISSUE",
                meta: {
                    title: "Help",
                    issues: issues
                }
            }

            if (showBoosterPackSelectionModal && doesUserBelongsToBoosterPackSelectionPreModalSegment) { // SHOW PRE-MODAL FOR BOOSTER PACK SELECTION
                initAction = this.getBoosterPackSelectionModalAction(boosterPack, true)
            }

            if (this.isLitePack(cultProduct)) {
                // SKU MODAL INIT ACTION FUP
                initAction = await this.upgradeSkuModalAction(correspondingSkuPlusPackId, priceDifference, true, isSelectPack)
            } else {
                // SKU PLUS MODAL INIT ACTION ELITE
                const noShowSkuPlusModal = await this.featureStateCache.match(userContext.userProfile.userId, showSkuPlusModalElitePlusKey, noShowSkuPlusModalState)
                if (doesUserBelongsToSkuPlusBottomsheetExperiment && !noShowSkuPlusModal && !this.isPlusPack(cultProduct) && !isAllIndiaPack && !isSelectPack) {
                    initAction = await this.getSkuPlusPackModalAction(exhaustivePackbenefitsDifference, correspondingSkuPlusPackId, priceDifference, true)
                }
            }

            analyticsData = {
                pageId: "elite_checkout",
            }

            if (!(await AppUtil.doesUserPurchasedAnyMembershipBefore(this.serviceInterfaces.segmentService, userContext))) {
                doRemoveContinuePurchaseCTA = false
                this.setUserDataForContinuePurchaseOption( userAttributeClient, userId, cultProduct)
            }
            this.setUserDataForContinuePurchaseOptionProductId( userAttributeClient, userId, cultProduct)

        } else if (baseProduct.productType === "GYMFIT_FITNESS_PRODUCT" && baseProduct.isPack) {
            const isDropdownClose = await AppUtil.isAppUserInCheckoutDropdownExperiment(userContext, this.serviceInterfaces.segmentService)
            const gymfitFitnessProduct: AugmentedOfflineFitnessPack = await this.catalogueServicePMS.getAugmentedPackById(productId, {includeExhaustiveBenefits: true})
            const productType: ProductType = gymfitFitnessProduct.productType
            const productTitle = _.isNil(gymfitFitnessProduct.displayName) || _.isEmpty(gymfitFitnessProduct.displayName) ? gymfitFitnessProduct.title : gymfitFitnessProduct.displayName
            const isSelectPack = CatalogueServiceUtilities.getAccessLevel(gymfitFitnessProduct) === GymfitAccessLevel.CENTER
            const months = Math.floor(gymfitFitnessProduct.product.durationInDays / 30)
            let color = this.getRandomColor()
            if (months == 3) color = this.durationColorMap["3"]
            else if (months == 6) color = this.durationColorMap["6"]
            else if (months == 12) color = this.durationColorMap["12"]

            if (doesUserBelongsToNewCheckoutPageExperiment) {
                const bannerTimerWidget = this.buildBannerTimerWidget(userContext, PRO_NAS_PRICE_HIKE_BANNER_ID)
                if (bannerTimerWidget) {
                    widgetViewPromises.push(bannerTimerWidget)
                    topPadding = 70
                }
                widgetViewPromises.push(
                    this.createPaymentAndOfferDetailWidget(
                        userContext, months.toString(), months > 1 ? "MONTHS" : "MONTH", color, isSelectPack ? "SELECT" : "PRO", baseProduct.productType,
                        gymfitFitnessProduct?.product?.productSubType, isSelectPack ? gymfitFitnessProduct?.clientMetadata?.centerName : null, order
                    )
                )
            } else {
                widgetViewPromises.push(this.getPaymentSummaryWidget(
                    userContext,
                    order,
                    productType,
                    productTitle,
                    months.toString(),
                    productTitle,
                    months > 1 ? "MONTHS" : "MONTH",
                    null,
                    true,
                    true,
                    color,
                    true,
                    !isDropdownClose,
                    boosterPack,
                    addonProductIdsFromReqBody,
                    gymfitFitnessProduct?.product?.productSubType
                ))
                const bannerTimerWidget = this.buildBannerTimerWidget(userContext, PRO_NAS_PRICE_HIKE_BANNER_ID)
                if (bannerTimerWidget) {
                    widgetViewPromises.push(bannerTimerWidget)
                }
            }

            let priceDifference = undefined
            if (correspondingSkuPlusOrder?.totalAmountPayable && order?.totalAmountPayable) {
                priceDifference = Math.ceil(correspondingSkuPlusOrder.totalAmountPayable - order.totalAmountPayable)
            }

            let exhaustivePackbenefitsDifference: any = undefined
            if (gymfitFitnessProduct?.augments?.exhaustiveBenefitList?.length > 0 && correspondingSkuPlusPack?.augments?.exhaustiveBenefitList?.length > 0) {
                exhaustivePackbenefitsDifference = PackUtils.compareExhaustiveBenefitLists(correspondingSkuPlusPack?.augments?.exhaustiveBenefitList, gymfitFitnessProduct?.augments?.exhaustiveBenefitList, false)
            }

            const emiWidget = this.addGymFitnessEMIWidget(order, gymfitFitnessProduct)
            if (doesUserBelongsToNewCheckoutPageExperiment) {
                if (emiWidget) {
                    widgetViewPromises.push(emiWidget)
                }
                const whatYouGetBenefitsWidget = this.getWhatYouGetBenefitsWidget(gymfitFitnessProduct, this.isPlusPack(gymfitFitnessProduct), isSelectPack, this.isLitePack(gymfitFitnessProduct), false)
                if (whatYouGetBenefitsWidget) {
                    widgetViewPromises.push(whatYouGetBenefitsWidget)
                }
            }

            // SKU PLUS BANNER PRO
            if (doesUserBelongsToSkuPlusUpgradeBanner && months && !this.isPlusPack(gymfitFitnessProduct) && !isSelectPack) {
                const horizontalUpgradeSkuPlusWidget = this.addHorizontalUpgradeSkuPlusWidget(exhaustivePackbenefitsDifference, correspondingSkuPlusPackId, priceDifference, false)
                if (horizontalUpgradeSkuPlusWidget) {
                    widgetViewPromises.push(horizontalUpgradeSkuPlusWidget)
                }
            }

            if (showAddOnFomoDetails) { // BOOSTER PACK NO CASE
                const addOnFomoHorizontalWidget = this.addHorizontalFomoWidget(boosterPack)
                if (addOnFomoHorizontalWidget) {
                    widgetViewPromises.push(addOnFomoHorizontalWidget)
                }
            }

           if (!doesUserBelongsToNewCheckoutPageExperiment) {
                if (emiWidget) {
                    widgetViewPromises.push(emiWidget)
                }
                const offerWidgetPromise = this.addGenericOfferAddonWidget(order)
                if (offerWidgetPromise) {
                    widgetViewPromises.push(offerWidgetPromise)
                }

                if (this.isPlusPack(gymfitFitnessProduct)) {
                    const skuPlusBenfitsWidget = this.getSkuPlusBenefitsWidget(gymfitFitnessProduct)
                    if (skuPlusBenfitsWidget) {
                        widgetViewPromises.push(skuPlusBenfitsWidget)
                    }
                } else {
                    widgetViewPromises.push(this.addGymFitWhatYouGet(gymfitFitnessProduct, false, isBoosterDetailsPresent))
                }
           }

            if (isBoosterDetailsPresent) { // BOOSTER PACK YES CASE
                const boosterBenefitsWidget = this.addBoosterBenefits(boosterPack, false, false)
                if (boosterBenefitsWidget) {
                    widgetViewPromises.push(boosterBenefitsWidget)
                }
            }

            const howItWorks = this.addProHowItWorks()
            if (howItWorks) {
                widgetViewPromises.push(howItWorks)
            }
            footerWidgetViewPromises.push(this.getProBottomCTA(order, userContext, gymfitFitnessProduct, boosterPack, showBoosterPackSelectionModal, doesUserBelongsToBoosterPackSelectionPreModalSegment))
            footerPadding = 220

            if (showBoosterPackSelectionModal && doesUserBelongsToBoosterPackSelectionPreModalSegment) { // SHOW PRE-MODAL FOR BOOSTER PACK SELECTION
                initAction = this.getBoosterPackSelectionModalAction(boosterPack, false)
            }

            // SKU PLUS MODAL INIT ACTION PRO
            const noShowSkuPlusModal = await this.featureStateCache.match(userContext.userProfile.userId, showSkuPlusModalProPlusKey, noShowSkuPlusModalState)
            if (doesUserBelongsToSkuPlusBottomsheetExperiment && !noShowSkuPlusModal && !this.isPlusPack(gymfitFitnessProduct) && !isSelectPack) {
                initAction = await this.getSkuPlusPackModalAction(exhaustivePackbenefitsDifference, correspondingSkuPlusPackId, priceDifference, false)
            }

            analyticsData = {
                pageId: "pro_checkout",
            }

            if (!(await AppUtil.doesUserPurchasedAnyMembershipBefore(this.serviceInterfaces.segmentService, userContext))) {
                doRemoveContinuePurchaseCTA = false
                this.setUserDataForContinuePurchaseOption(userAttributeClient, userId, gymfitFitnessProduct)
            }
            this.setUserDataForContinuePurchaseOptionProductId( userAttributeClient, userId, gymfitFitnessProduct)

        } else if (baseProduct.productType === "GYM_PT_PRODUCT") {
            const userId = userContext?.userProfile?.userId ? Number(userContext?.userProfile?.userId) : null
            await this.logger.info(`PT_PILOT logs userId ${userContext?.userProfile?.userId} productType ${baseProduct.productType}`)
            const ptProduct: GymPtProduct = await this.catalogueService.getGymPtProductById(productId)
            await this.logger.info(`PT_PILOT logs userId ${userContext?.userProfile?.userId} product ${JSON.stringify(ptProduct)}`)
            const productType: ProductType = ptProduct.productType
            const tz = userContext.userProfile.timezone
            const totalDuration = ptProduct?.durationInDays ? ptProduct?.durationInDays : null

            const minEligibleDate = order?.products?.[0]?.option?.startDate ? TimeUtil.formatDateInTimeZone(tz, new Date(order.products[0].option.startDate)) : TimeUtil.todaysDate(tz)

            const expiryDate = totalDuration ? TimeUtil.formatDateStringInTimeZoneDateFns(TimeUtil.addDays(tz, minEligibleDate, totalDuration), tz, "dd MMM yyyy") : null

            widgetViewPromises.push(this.getPaymentSummaryWidget(
                userContext,
                order,
                productType,
                ptProduct.title,
                ptProduct.listingCategory.restrictions[0].restrictionCount.toString(),
                "Personal Training",
                ptProduct.listingCategory.restrictions[0].restrictionCount > 1 ? "SESSIONS" : "SESSION",
                expiryDate
            ))

            await this.logger.info(`PT_PILOT logs  userId ${userContext?.userProfile?.userId} centerId ${serviceCenterId} userId ${userId}`)

            const doesNoShowCancellationPolicyV1Apply = await this.ptService.isNoShowCancellationPolicyV1ApplicableInCenter(serviceCenterId, userId, HeadersUtil.getCommonHeaders(userContext))
            await this.logger.info(`PT_PILOT logs userId ${userContext?.userProfile?.userId} noshowFlag ${doesNoShowCancellationPolicyV1Apply?.doesApply} centerId ${serviceCenterId} userId ${userId}`)
            widgetViewPromises.push(this.addGymPTTrainersWidget(cartReviewPayload))
            if (doesNoShowCancellationPolicyV1Apply?.doesApply) {
                widgetViewPromises.push(this.addSessionsExpiryWidget(ptProduct))
                widgetViewPromises.push(this.addCancellationsWidget(ptProduct))
            }
            // widgetViewPromises.push(this.addSessionsExpiryWidget())
            const offerWidgetPromise = this.addGymPTOfferAddonWidget(order)
            if (offerWidgetPromise) {
                widgetViewPromises.push(offerWidgetPromise)
            }
            if (order.totalAmountPayable === 0) {
                const title = "Get for free"
                pageAction = {
                    title: title,
                    actionType: "CARE_PAY_FREE"
                }
                if (userAgent === "DESKTOP") {
                    pageAction.orientation = "RIGHT"
                }
            } else if (!pageAction) {
                pageAction = {
                    title: `Pay ${RUPEE_SYMBOL}${order.totalAmountPayable}`,
                    actionType: "CARE_CART_PAY_NOW",
                    url: "curefit://payment"
                }
                if (userAgent === "DESKTOP") {
                    pageAction.orientation = "RIGHT"
                }
            }
        } else if (baseProduct.productType === "LUX_FITNESS_PRODUCT") {
            const luxPack: OfflineFitnessPack = await this.offlineFitnessPackService.getCachedPackById(productId)
            const productType: ProductType = luxPack.product.productType
            const centerName = luxPack.clientMetadata?.centerName
            const productTitle = _.isNil(centerName) || _.isEmpty(centerName) ? luxPack.product.title : centerName
            let months: any
            let footerString: string
            const luxGxBenefit: BenefitEntry = luxPack.product.benefits.find((item) => (item.name === "LUX_GX" || item.name === "LUX_GROUP_CLASSES" ))
            let packName: any = undefined
            const durationInDays = luxPack.product.durationInDays
            if (!_.isNil(luxGxBenefit)) {
                footerString = "SESSIONS"
                const isUnlimitedBenefit: boolean = luxGxBenefit.tickets > 100 || (luxGxBenefit.tickets > 30 && luxGxBenefit.type === BenefitType.MONTHLY)
                const packDuration = Math.floor(durationInDays / 30)
                let validityString = "Valid for " + packDuration + (packDuration > 1 ? " months" : " month")
                if (durationInDays < 180) {
                    validityString = "Valid for " + durationInDays + (durationInDays > 1 ? " days" : " day")
                }
                packName = {
                    "title": {
                        "text": productTitle,
                        "color": "#FFFFFF",
                        "typeScale": "H1"
                    },
                    "subtitle1": {
                        "text": validityString,
                        "typeScale": "P5"
                    },
                }
                if (isUnlimitedBenefit) {
                    months = "∞"
                } else {
                    months = luxGxBenefit.tickets
                }
            } else {
                months = Math.floor(durationInDays / 30)
                footerString = months > 1 ? "MONTHS" : "MONTH"
            }
            widgetViewPromises.push(this.getPaymentSummaryWidget(
                userContext,
                order,
                productType,
                productTitle,
                months.toString(),
                productTitle,
                footerString,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                packName,
            ))

            const emiWidget = this.addGymFitnessEMIWidgetPMS(order, luxPack)
            if (emiWidget) {
                widgetViewPromises.push(emiWidget)
            }

            const offerWidgetPromise = this.addGenericOfferAddonWidget(order)
            if (offerWidgetPromise) {
                widgetViewPromises.push(offerWidgetPromise)
            }
            const howItWorks = this.addLuxHowItWorks(centerName, luxPack.product.benefits)
            if (howItWorks) {
                widgetViewPromises.push(howItWorks)
            }
            pageAction = {
                title: (order.totalAmountPayable === 0) ? this.freeTitleTag : `Pay ${RUPEE_SYMBOL}${order.totalAmountPayable}`,
                actionType: "CARE_CART_PAY_NOW",
                url: "curefit://payment",
                meta: (order.totalAmountPayable === 0) ? {payFree: true} : null
            }
            const centerServiceId = luxPack.restrictions?.centers?.[0]
            const center = centerServiceId && await this.centerService.getCenterById(centerServiceId)
            const centerId = center?.meta?.gymfitCenterId

            analyticsData = {
                pageId: "lux_checkout",
                centerId: centerId?.toString(),
                centerServiceCenterId: centerServiceId?.toString(),
            }
        } else if (baseProduct.productType === "DEVICE") {
            // sugarf.fit
            const product = <CGMProduct>baseProduct
            if (product.subCategoryCode === "CGM") {
                widgetViewPromises.push(this.getChronicCareCGMCheckoutWidget(product))
                pageAction = {
                    title: `Pay ${RUPEE_SYMBOL}${order.totalAmountPayable}`,
                    actionType: "CARE_CART_PAY_NOW",
                    url: "curefit://payment?vertical=CARE"
                }
                widgetViewPromises.push(this.getChronicCareCGMPaymentSummaryWidget(userContext, order, baseProduct.productType))
            }
        } else if (baseProduct.productType === "SF_CONSUMABLE") {
            let deliveryEta
            const addressid: string = cartReviewPayload.orderProducts[0].option.addressId
            let pincode: number
            if (addressid) {
                const userDeliveryAddress = await this.userBusiness.getAddress(userId, addressid)
                this.logger.info(`User delivery address for userId :: ${userId}, address :: ${JSON.stringify(userDeliveryAddress)}`)
                pincode = Number(userDeliveryAddress?.structuredAddress?.pincode)
                const serviceabilityCheckResponse = await this.indusService.serviceabilityCheck(pincode)
                deliveryEta = serviceabilityCheckResponse.edd
                if (serviceabilityCheckResponse && !serviceabilityCheckResponse?.isServicable) {
                    alertInfo = await this.validateSfServiceability()
                }
            } else {
                alertInfo = await this.getSfAddAddressAlert()
            }
            if (!alertInfo) {
                alertInfo = await this.validateSfInventory(cartReviewPayload.orderProducts)
            }
            const billingInfo = this.offerHelper.getOrderBilling(order)
            widgetViewPromises.push(this.getSfEComCartAddressWidget(deliveryEta, order, billingInfo))
            // const delayedDeliveryBannerWidget = this.getSfEcomDelayedDeliveryBannerCarouselWidget(order)
            // if (delayedDeliveryBannerWidget) {
            //     widgetViewPromises.push(delayedDeliveryBannerWidget)
            // }
            const indusOrderProducts: IndusOrderProduct[] = _.map(order.products, p => {
                return {
                    productCode: p.productId,
                    quantity: p.quantity,
                    price: p.price?.listingPrice || p.price?.sellingPrice || p.price?.mrp,
                    pincode: pincode,
                }
            })
            const indusShipments = await this.indusService.buildOrderShipments(indusOrderProducts)
            const separatorWidget = this.getSfSeperatorWidget()
            widgetViewPromises.push(this.getSfEComCartListWidget(userContext, order, billingInfo, indusShipments, couponData))
            widgetViewPromises.push(separatorWidget)
            widgetViewPromises.push(this.getSfEComCouponWidget(userContext, order, billingInfo, indusShipments, couponData))
            widgetViewPromises.push(separatorWidget)
            widgetViewPromises.push(this.getSfEComPeopleAlsoBoughtWidget(userContext, order))
            widgetViewPromises.push(separatorWidget)
            widgetViewPromises.push(this.getSfEComBillingWidget(userContext, order, billingInfo, indusShipments))

            const paymentAction: Action = {
                title: `Pay ${RUPEE_SYMBOL}${order.totalAmountPayable}`,
                actionType: "SF_CART_PAY_NOW",
                url: "curefit://payment?vertical=CARE&tenant=SUGARFIT&platform=ECOMMERCE",
                disabled: !!alertInfo
            }
            pageAction = paymentAction

            if (!this.doesPhoneNumberExist(user)) {
                pageAction = {
                    actionType: "UPDATE_PHONE_NUMBER" as any,
                    title: `Pay ${RUPEE_SYMBOL}${order.totalAmountPayable}`,
                    meta: {
                        message: "To proceed further, please add phone number for delivery and any future communications",
                    }
                }
            }
        } else if (AppUtil.isSugarFitOrUltraFitApp(userContext) && baseProduct.productType === "DIAGNOSTICS") {
            //  sugarf.fit
            const billingInfo = this.offerHelper.getOrderBilling(order)
            const separatorWidget = this.getSfSeperatorWidget()
            widgetViewPromises.push(this.getSfDiagnosticCartAddressWidget())
            widgetViewPromises.push(this.getSfDiagnosticCartListWidget(order))
            widgetViewPromises.push(separatorWidget)
            widgetViewPromises.push(this.getSfDiagnosticsBillingWidget(userContext, order, billingInfo))
            const paymentAction: Action = {
                title: `Pay ${RUPEE_SYMBOL}${order.totalAmountPayable}`,
                actionType: "SF_DIAGNOSTIC_CART_PAY_NOW",
                url: "",
                disabled: !!alertInfo
            }
            pageAction = paymentAction
            if (!this.doesPhoneNumberExist(user)) {
                pageAction = {
                    actionType: "UPDATE_PHONE_NUMBER" as any,
                    title: `Pay ${RUPEE_SYMBOL}${order.totalAmountPayable}`,
                    meta: {
                        message: "To proceed further, please add phone number for delivery and any future communications",
                    }
                }
            }
        } else if (baseProduct.productType === "PLAY" && baseProduct.isPack && baseProduct.subLevelAccessListings.length == 0) {
            const clientMetaData: CultMembershipMetadata = order.clientMetadata as CultMembershipMetadata
            const playPack: OfflineFitnessPack = await this.catalogueServicePMS.getProduct(productId)
            const durationInMonths: number = Math.floor(playPack.product.durationInDays / 30)

            if (AccessLevel.CENTER === CatalogueServiceUtilities.getAccessLevel(playPack)) {
                const centerResponsePromise: Promise<CenterResponse> = this.centerService.getCenterById(Number(clientMetaData.centerId))
                const centerInfo = await centerResponsePromise
                const playSuffixAndHeader = PlayUtil.getSuffixAndPackHeaderForPlaySelectPack(centerInfo.name)

                widgetViewPromises.push(this.getPlaySelectPaymentSummaryWidget(
                    userContext,
                    order,
                    playPack.productType,
                    playPack.title,
                    String(durationInMonths),
                    playSuffixAndHeader.suffix,
                    durationInMonths > 1 ? "MONTHS" : "MONTH",
                    playSuffixAndHeader.packHeader,
                    "#FC55DA"
                ))

                const bannerTimerWidget = this.buildBannerTimerWidget(userContext, PLAY_NAS_PRICE_HIKE_BANNER_ID)
                if (bannerTimerWidget) {
                    widgetViewPromises.push(bannerTimerWidget)
                }

                let workoutResponsePromise
                if (clientMetaData.workoutId) {
                    workoutResponsePromise = this.cultFitService.getWorkout(clientMetaData.workoutId, "CUREFIT_API", userContext.userProfile.city.cultCityId)
                }
                widgetViewPromises.push(...this.playSelectPackViewBuilder.getWidgetPromises(userContext, baseProduct.productType, order, playPack, centerResponsePromise, workoutResponsePromise))
                footerWidgetViewPromises.push(...this.playSelectPackViewBuilder.getFooterWidgetPromises(playPack, order, userContext, centerResponsePromise, workoutResponsePromise))
            } else if (AccessLevel.CITY === CatalogueServiceUtilities.getAccessLevel(playPack)) {
                // const city = this.cityService.getCityByCultCityId(playPack.cityId);
                let centerResponsePromise: Promise<CenterResponse>
                if (clientMetaData?.centerId) {
                    centerResponsePromise = this.centerService.getCenterById(Number(clientMetaData.centerId))
                }
                let workoutResponsePromise
                const playSuffixAndHeader = PlayUtil.getSuffixAndPackHeaderForPlayPack()
                widgetViewPromises.push(this.getPlaySelectPaymentSummaryWidget(
                    userContext,
                    order,
                    playPack.productType,
                    playPack.title,
                    String(durationInMonths),
                    playSuffixAndHeader.suffix,
                    durationInMonths > 1 ? "MONTHS" : "MONTH",
                    playSuffixAndHeader.packHeader,
                    "#FC55DA"
                ))

                const bannerTimerWidget = this.buildBannerTimerWidget(userContext, PLAY_NAS_PRICE_HIKE_BANNER_ID)
                if (bannerTimerWidget) {
                    widgetViewPromises.push(bannerTimerWidget)
                }

                if (clientMetaData.workoutId) {
                    workoutResponsePromise = this.cultFitService.getWorkout(clientMetaData.workoutId, "CUREFIT_API", userContext.userProfile.city.cultCityId)
                }

                widgetViewPromises.push(...this.playPackViewBuilder.getWidgetPromises(baseProduct.productType, order, playPack, centerResponsePromise, workoutResponsePromise))
                footerWidgetViewPromises.push(
                    ...this.playPackViewBuilder.getFooterWidgetPromises(playPack, order,
                        userContext, centerResponsePromise, workoutResponsePromise))
            }
        } else if (
            baseProduct.productType === "PLAY"
            && baseProduct.isPack
            && baseProduct.subLevelAccessListings.length > 0
        ) {
            const clientMetaData: CultMembershipMetadata = order.clientMetadata as CultMembershipMetadata
            const playPack: OfflineFitnessPack = await this.catalogueServicePMS.getProduct(productId)
            const durationInMonths: number = Math.floor(playPack.product.durationInDays / 30)
            const isCityLevelSLPPack = AccessLevel.CITY === CatalogueServiceUtilities.getAccessLevel(playPack)
            let workoutResponsePromise
            let playSuffixAndHeader
            let centerResponsePromise: Promise<CenterResponse>
            if (clientMetaData.centerId && clientMetaData.centerId.toString().length != 0) {
                centerResponsePromise = this.centerService.getCenterById(Number(clientMetaData.centerId))
            }
            const centerInfo = await centerResponsePromise
            if (clientMetaData.workoutId) {
                workoutResponsePromise = this.cultFitService.getWorkout(clientMetaData.workoutId, "CUREFIT_API", userContext.userProfile.city.cultCityId)
            }
            const workoutInfo = await workoutResponsePromise
            if (isCityLevelSLPPack) {
                playSuffixAndHeader = PlayUtil.getSuffixAndPackHeaderForPlayCityLevelSportsPack(workoutInfo.name)
            } else {
                playSuffixAndHeader = PlayUtil.getSuffixAndPackHeaderForPlaySportPack(centerInfo.name, workoutInfo.name)
            }

            widgetViewPromises.push(this.getPlaySelectPaymentSummaryWidget(
                userContext,
                order,
                playPack.productType,
                playPack.title,
                String(durationInMonths),
                playSuffixAndHeader.suffix,
                durationInMonths > 1 ? "MONTHS" : "MONTH",
                playSuffixAndHeader.packHeader,
                "#FC55DA"
            ))

            const bannerTimerWidget = this.buildBannerTimerWidget(userContext, PLAY_NAS_PRICE_HIKE_BANNER_ID)
            if (bannerTimerWidget) {
                widgetViewPromises.push(bannerTimerWidget)
            }

            if (isCityLevelSLPPack) {
                widgetViewPromises.push(...this.playSelectPackViewBuilder.getWidgetPromises(userContext, "PLAY_CITY_SPORT" as ProductType, order, playPack, centerResponsePromise, workoutResponsePromise))
                footerWidgetViewPromises.push(...this.playSelectPackViewBuilder.getFooterWidgetPromisesForSLPCity(playPack, order, userContext, centerResponsePromise, workoutResponsePromise))
            } else {
                widgetViewPromises.push(...this.playSelectPackViewBuilder.getWidgetPromises(userContext, "PLAY_SPORT" as ProductType, order, playPack, centerResponsePromise, workoutResponsePromise))
                footerWidgetViewPromises.push(...this.playSelectPackViewBuilder.getFooterWidgetPromises(playPack, order, userContext, centerResponsePromise, workoutResponsePromise))
            }
           }

        if (doRemoveContinuePurchaseCTA) {
            this.removeUserDataForContinuePurchaseOption(userAttributeClient, userId)
        }

        let widgets = await Promise.all(widgetViewPromises)
        await this.logger.info(`PT_PILOT logs userId ${userContext?.userProfile?.userId} widgets ${JSON.stringify(widgets)}`)
        widgets = _.filter(widgets, widget => !_.isEmpty(widget))
        let footerWidgets = await Promise.all(footerWidgetViewPromises)
        footerWidgets = _.filter(footerWidgets, widget => !_.isEmpty(widget))
        const minEligibleDate = TimeUtil.formatDateInTimeZone(tz, new Date(order.products[0].option.startDate))
        const maximumDate = TimeUtil.addDays(tz, minEligibleDate, 30)
        const meta: any = {
            startDate: order.products[0].option.startDate,
            title: "Pick a start date",
            selectedDateText: "Starts on: ",
            endDate: maximumDate,
            analyticsData,
            footerPadding,
            topPadding
        }
        if (baseProduct?.productType && (baseProduct.productType === "GYM_PT_PRODUCT") && minimumEligibleStartDate) {
            meta["minimumEligibleStartDate"] = minimumEligibleStartDate
        }
        return {
            widgets: widgets,
            footerWidgets: footerWidgets,
            themeType: themeType,
            actions: [pageAction],
            alertInfo: alertInfo,
            navigationAction: helpAction,
            initAction: initAction,
            meta
        }
    }

    private async removeUserDataForContinuePurchaseOption(userAttributeClient: IUserAttributeClient, userId: string) {
        try {
            await userAttributeClient.setUserAttributes({
                userId: Number(userId),
                attribute: "continue_purchase_option",
                attrValue: { "$delete": 1 },
                namespace: "GLOBAL",
                description: "Data for Continue Purchase Option",
                dataType: "OBJECT",
                occuredAt: new Date().getTime()
            })
        } catch (e) {
            // fire and move on
            this.logger.error(`Error in removing user data for continue purchase option for user ${userId}`, {e})
            this.serviceInterfaces.rollbarService.sendError(e)
        }
    }

    private async setUserDataForContinuePurchaseOption( userAttributeClient: IUserAttributeClient, userId: string, product: OfflineFitnessPack ) {
        try {
            let center
            if (CatalogueServiceUtilities.getAccessLevel(product) === "CENTER" && CatalogueServiceUtilities.getExternalAccessLevelId(product) != null) {
                center = await this.centerService.getCenterById(parseInt(CatalogueServiceUtilities.getExternalAccessLevelId(product)))
            }
            await userAttributeClient.setUserAttributes({
                userId: Number(userId),
                attribute: "continue_purchase_option",
                attrValue: {
                    productId: product.productId,
                    productName: product.displayName,
                    centerServiceId: center?.id,
                    centerName: center?.name,
                    sku: product.productType,
                    isSelect: CatalogueServiceUtilities.getAccessLevel(product) === "CENTER",
                },
                namespace: "GLOBAL",
                description: "Data for Continue Purchase Option",
                dataType: "OBJECT",
                occuredAt: new Date().getTime()
            })
        } catch (e) {
            this.logger.error(`Error in setting user data for continue purchase option for user ${userId}`, {e})
            // fire and move on
            this.serviceInterfaces.rollbarService.sendError(e, { extra: { productId: product.productId }})
        }
    }

    private async setUserDataForContinuePurchaseOptionProductId( userAttributeClient: IUserAttributeClient, userId: string, offlineFitnessPack: OfflineFitnessPack) {
        try {
            await userAttributeClient.setUserAttributes({
                userId: Number(userId),
                attribute: "continue_purchase_option_productId",
                attrValue: offlineFitnessPack.productId,
                namespace: "GLOBAL",
                description: "ProductId for Continue Purchase Option",
                dataType: "STRING",
                occuredAt: new Date().getTime()
            })
        } catch (e) {
            this.logger.error(`Error in setting user data for continue purchase option productId for user ${userId}`, {e})
            // fire and move on
            this.serviceInterfaces.rollbarService.sendError(e, { extra: { productId: offlineFitnessPack.productId }})
        }
    }

    private async getOfferCalloutWidget(userContext: UserContext): Promise<ProductOfferWidgetV2> | null {
        const source = AppUtil.callSourceFromContext(userContext)
        const isWeb = userContext.sessionInfo.userAgent !== "APP"
        const isDesktop = userContext.sessionInfo.userAgent === "DESKTOP"
        // Fetch Care Cart Offers for Bundle
        const {offerIds} = await this.offerServiceV3.getApplicableCartOffersForCare({
            userInfo: {userId: userContext.userProfile.userId, deviceId: userContext.sessionInfo.deviceId},
            requiredOfferTypes: ["BUNDLE"],
            source,
            cityId: userContext.userProfile.cityId
        })
        const offers = _.values((await this.offerServiceV3.getOffersByIds(offerIds)).data)
        const uniqueOfferIds: string[] = []
        const webContainerStyle = {boxShadow: "none", border: "none", margin: 0}
        const contentContainerStyle = {
            marginBottom: 10,
            marginTop: 10,
            backgroundColor: "#dfefe6", ...(isWeb ? webContainerStyle : {})
        }
        if (!_.isEmpty(offers)) {
            const actionType: ActionType = "SHOW_OFFERS_TNC_MODAL"
            const offerItems: ProductOffer[] = []
            offers.map(offer => {
                if (!_.isEmpty(offer)) {
                    if (uniqueOfferIds.indexOf(offer.offerId) === -1 && !offer.displayContexts?.includes("NONE")) {
                        uniqueOfferIds.push(offer.offerId)
                        offerItems.push({
                            title: offer.description.toString(),
                            iconType: "/image/icons/cult/tick.png",
                            tnc: {
                                title: "T&C",
                                action: {
                                    actionType: actionType,
                                    meta: {
                                        title: "Offer Details",
                                        dataItems: offer.tNc,
                                        url: offer.tNcUrl
                                    }
                                }
                            }
                        })
                    }
                }
            })

            if (offerItems.length > 0) {
                return {
                    widgetType: "PRODUCT_OFFER_WIDGET_V2",
                    offerItems: offerItems,
                    dividerType: "NONE",
                    contentContainerStyle,
                    hideDashedBorder: true,
                    offersBackground: "#dfefe6",
                    orientation: isDesktop ? "RIGHT" : undefined,
                }
            } else {
                return undefined
            }
        } else {
            return undefined
        }

    }

    private async getCollapsibleOfferWidget(userContext: UserContext, isWeb?: boolean): Promise<any> | null {
        const source = AppUtil.callSourceFromContext(userContext)
        // Fetch Care Cart Offers for Bundle
        const {offerIds} = await this.offerServiceV3.getApplicableCartOffersForCare({
            userInfo: {userId: userContext.userProfile.userId, deviceId: userContext.sessionInfo.deviceId},
            requiredOfferTypes: ["BUNDLE"],
            source,
            cityId: userContext.userProfile.cityId
        })
        const offers = _.values((await this.offerServiceV3.getOffersByIds(offerIds)).data)
        const uniqueOfferIds: string[] = []
        const webContainerStyle = {boxShadow: "none", border: "none", margin: 0}
        const contentContainerStyle = {
            marginBottom: 10,
            marginTop: 10,
            backgroundColor: "#f0f9ff", ...(isWeb ? webContainerStyle : {})
        }
        if (!_.isEmpty(offers)) {
            const actionType: ActionType = "SHOW_OFFERS_TNC_MODAL"
            const offerItems: ProductOffer[] = []
            offers.map(offer => {
                if (!_.isEmpty(offer)) {
                    if (uniqueOfferIds.indexOf(offer.offerId) === -1 && !offer.displayContexts?.includes("NONE")) {
                        uniqueOfferIds.push(offer.offerId)
                        offerItems.push({
                            title: offer.description.toString(),
                            iconType: "/image/icons/cult/tick.png",
                            tnc: {
                                title: "T&C",
                                action: {
                                    actionType: actionType,
                                    meta: {
                                        title: "Offer Details",
                                        dataItems: offer.tNc,
                                        url: offer.tNcUrl
                                    }
                                }
                            }
                        })
                    }
                }
            })

            if (offerItems.length > 0) {
                return {
                    widgetType: "COLLAPSIBLE_OFFER_WIDGET",
                    offerItems: offerItems,
                    dividerType: "NONE",
                    contentContainerStyle,
                    hideDashedBorder: true,
                    offersBackground: "#f0f9ff",
                    collapseLimit: 2,
                }
            } else {
                return undefined
            }
        } else {
            return undefined
        }

    }


    private async addSessionsExpiryWidget(ptProduct: GymPtProduct): Promise<WidgetView> {
        const totalDuration = ptProduct?.durationInDays ? ptProduct?.durationInDays : 30
        return {
            widgetType: "TOPIC_DETAILS_LIST_WIDGET",
            title: "Sessions Expiry",
            items: [
                {
                    subTitle: `Complete your sessions within ${totalDuration} days from purchase, as any remaining sessions will expire.`,
                    icon: "/image/fitnessPlanner/tick.png",
                }
            ],
            noBorderIcon: true,
        }
    }

    private async addCancellationsWidget(ptProduct: GymPtProduct): Promise<WidgetView> {
        const totalDuration = ptProduct?.durationInDays ? ptProduct?.durationInDays : 30
        return {
            widgetType: "TOPIC_DETAILS_LIST_WIDGET",
            title: "Cancellations & No-Shows",
            items: [
                {
                    subTitle: `Cancel or reschedule your PT session up to ${CANCELLATION_WINDOW_FOR_NO_SHOW} hours before the booking time to compensate for the trainer’s time.`,
                    icon: "/image/fitnessPlanner/tick.png",
                },
                {
                    subTitle: `Complete your sessions within ${totalDuration} days from purchase, as any remaining sessions will expire.`,
                    icon: "/image/fitnessPlanner/tick.png",
                },
                {
                    subTitle: "No-shows will count towards your total sessions.",
                    icon: "/image/fitnessPlanner/tick.png",
                },
                {
                    subTitle: "Remember to scan your session QR code with your personal trainer to avoid it being counted as a no-show.",
                    icon: "/image/fitnessPlanner/tick.png",
                },
                {
                    subTitle: "For any no-shows marked due to trainer unavailability or tech problems, contact the center manager for a credit.",
                    icon: "/image/fitnessPlanner/tick.png",
                },
                {
                    subTitle: "PT packs are non-refundable and non-transferable.",
                    icon: "/image/fitnessPlanner/tick.png",
                },
                {
                    subTitle: "PT packs are structured with a recommended frequency of 3 sessions per week.",
                    icon: "/image/fitnessPlanner/tick.png",
                },
            ],
            noBorderIcon: true,
        }
    }

    private async addGymPTTrainersWidget(cartReviewPayload: CartReviewPayload): Promise<WidgetView> {
        if (cartReviewPayload.orderProducts[0] && cartReviewPayload.orderProducts[0].option && cartReviewPayload.orderProducts[0].option.doctorId) {
            const agentDetails: Doctor = await this.ollivanderService.getDoctorDetails(cartReviewPayload.orderProducts[0].option.doctorId)
            const doctorAssets: DoctorAssetsResponse = await this.ollivanderService.getDoctorAssets(cartReviewPayload.orderProducts[0].option.doctorId)
            const center: CenterResponse = await this.centerService.getCenterById(Number(cartReviewPayload.orderProducts[0].option.centerId))
            const ptServiceType = agentDetails?.resourceServiceMapping.find(mapping => mapping.subServiceType?.groupType && mapping.subServiceType.groupType === "GYMFIT_PERSONAL_TRAINING")
            const centerName = center?.name
            const identityResponse = await this.identityService.getIdentityById("CUREFIT", agentDetails.identityId)
            const trainerName = identityResponse && identityResponse.name ? identityResponse.name : agentDetails.name
            return {
                widgetType: "YOUR_TRAINER_WIDGET",
                heading: CartViewBuilder.getTrainersLevelText(ptServiceType?.subServiceTypeCode),
                title: GymfitUtil.capitalizeFirstLetterOfAllWords(trainerName),
                description: centerName,
                image: doctorAssets?.mediaList && doctorAssets?.mediaList.length > 0 ? doctorAssets?.mediaList[0]?.mediaUrl : undefined,
            }
        }
        return null
    }

    private async addNoteWidget(): Promise<WidgetView> {
        return {
            widgetType: "TOPIC_DETAILS_LIST_WIDGET",
            title: "Note",
            items: [
                {
                    subtitle: "You will need an active cultpass to access the gym for your personal training sessions. You can schedule your sessions by directly reaching out to your trainer.",
                    icon: "/image/icons/cult/time_up.png",
                }
            ],
        }
    }

    public static getTrainersLevelText(level: string): string {
        if (level.endsWith("L1")) return "LEVEL 1"
        else if (level.endsWith("L2")) return "LEVEL 2"
        else if (level.endsWith("L3")) return "LEVEL 3"
        else if (level.endsWith("L4")) return "LEVEL 4"
        else if (level.endsWith("L5")) return "LEVEL 5"
        else if (level.endsWith("L6")) return "LEVEL 6"
        return "LEVEL 7"
    }

    private async addGymFitnessEMIWidget(order: Order, product: OfflineFitnessPack): Promise<WidgetView> {
        if (_.isEmpty(order.offersApplied)) return
        const offerV2Map: { [key: string]: OfferV2 } = await this.offerServiceV3.getOffersByIds(order.offersApplied).then(o => o.data)
        let emiOffer: OfferV2
        _.each(offerV2Map, (offer: OfferV2) => {
            if (offer.constraints.paymentChannel === "EMI") {
                emiOffer = offer
            }
        })
        if (emiOffer) {
            return CartViewBuilder.getGymNoCostEMIWidgetView(product, emiOffer, order.totalAmountPayable)
        } else {
            return null
        }
    }

    private async addGymFitnessEMIWidgetPMS(order: Order, pack: OfflineFitnessPack): Promise<WidgetView> {
        if (_.isEmpty(order.offersApplied)) return
        const offerV2Map: { [key: string]: OfferV2 } = await this.offerServiceV3.getOffersByIds(order.offersApplied).then(o => o.data)
        let emiOffer: OfferV2
        _.each(offerV2Map, (offer: OfferV2) => {
            if (offer.constraints.paymentChannel === "EMI") {
                emiOffer = offer
            }
        })
        if (emiOffer) {
            return CartViewBuilder.getGymNoCostEMIWidgetViewPMS(pack, emiOffer, order.totalAmountPayable)
        } else {
            return null
        }
    }

    private async buildBannerTimerWidget(userContext: UserContext, widgetId: string, isSkuPlusBanner = false): Promise<any> {
        const widgetResponse = await this.widgetBuilder.buildWidgets([widgetId], this.serviceInterfaces, userContext, undefined, undefined)
        if (!_.isEmpty(widgetResponse) && !_.isEmpty(widgetResponse.widgets)) {
            const widget: any = widgetResponse.widgets[0] && widgetResponse.widgets[0].widgetType === "BANNER_CAROUSEL_WIDGET" ? widgetResponse.widgets[0] : undefined
            // updating layoutprops in same object was causing issue in same widget displaying in other place
            const bannerWidget = clone(widget)
            bannerWidget.layoutProps.spacing = {top : "30", bottom: "20"}
            if (isSkuPlusBanner) {
                bannerWidget.data = bannerWidget.data.map((banner: any) => {
                    return {
                        ...banner,
                        action: {
                            actionType : "MULTI_POP_THEN_NAVIGATION",
                            meta : {
                                numberOfPop : 1,
                                action : banner.action ? banner.action : null
                            }
                        },
                    }
                })
            } else {
                bannerWidget.layoutProps.shinnyBorder = true
                bannerWidget.layoutProps.roundedCorners = false
            }
            return bannerWidget
        }
        return null
    }

    private async buildSkuPlusBenefitsBannerWidget(userContext: UserContext, widgetId: string): Promise<any> {
        const widgetResponse = await this.widgetBuilder.buildWidgets([widgetId], this.serviceInterfaces, userContext, undefined, undefined)
        if (!_.isEmpty(widgetResponse) && !_.isEmpty(widgetResponse.widgets)) {
            const widget: any = widgetResponse.widgets[0] && widgetResponse.widgets[0].widgetType === "BANNER_CAROUSEL_WIDGET" ? widgetResponse.widgets[0] : undefined
            // updating layoutprops in same object was causing issue in same widget displaying in other place
            const bannerWidget = clone(widget)
            return bannerWidget
        }
        return null
    }

    private async addFitnessEMIWidget(order: Order, product: OfflineFitnessPack): Promise<WidgetView> {
        if (_.isEmpty(order.offersApplied)) return
        const offerV2Map: { [key: string]: OfferV2 } = await this.offerServiceV3.getOffersByIds(order.offersApplied).then(o => o.data)
        let emiOffer: OfferV2
        _.each(offerV2Map, (offer: OfferV2) => {
            if (offer.constraints.paymentChannel === "EMI") {
                emiOffer = offer
            }
        })
        if (emiOffer) {
            return CartViewBuilder.getFitnessNoCostEMIWidgetView(product, emiOffer, order.totalAmountPayable)
        } else {
            return null
        }
    }

    public static getGymNoCostEMIWidgetView(product: OfflineFitnessPack, emiOffer: OfferV2, totalPayable?: number): WidgetView {

        let maxEmiTenure = 12
        emiOffer.addons.forEach((addOn) => {
            if (addOn.config && addOn.config.maxEmiTenure) {
                maxEmiTenure = addOn.config.maxEmiTenure
            }
        })

        const numberOfMonths = CultUtil.getEmiTenureForPack(product.product.durationInDays, maxEmiTenure)
        const item = {
            imageUrl: "image/transform/EMI.png",
            title: "No Cost EMI starts at just",
            symbol: "₹",
            price: `${Math.round((totalPayable ?? product.price.sellingPrice) / numberOfMonths)}`,
            suffix: "month*",
            action: {
                actionType: "NAVIGATION",
                // PackId to ProductId migration: ignoring: productId used as packId
                url: `curefit://nocostemipage?packId=${product.productId}&productType=${product.productType}`,
                title: "DETAILS"
            },
            fontSize: "P5",
            hexColor: "#AAAAAA"
        }

        const title = "No Cost EMI"
        return {
            widgetType: "NO_COST_EMI_WIDGET",
            title: title,
            hasDividerBelow: true,
            items: [item]
        }
    }

    public static getGymNoCostEMIWidgetViewPMS(pack: OfflineFitnessPack, emiOffer: OfferV2, totalPayable?: number): WidgetView {

        let maxEmiTenure = 12
        emiOffer.addons.forEach((addOn) => {
            if (addOn.config && addOn.config.maxEmiTenure) {
                maxEmiTenure = addOn.config.maxEmiTenure
            }
        })

        const numberOfMonths = CultUtil.getEmiTenureForPack(pack.product.durationInDays, maxEmiTenure)
        const item = {
            imageUrl: "image/transform/EMI.png",
            title: "No Cost EMI starts at just",
            symbol: "₹",
            price: `${Math.round((totalPayable ?? pack.price.sellingPrice) / numberOfMonths)}`,
            suffix: "month*",
            action: {
                actionType: "NAVIGATION",
                // PackId to ProductId migration: ignoring: productId used as packId - pack.id is productId in case of offlineFitnessPack (lux)
                url: `curefit://nocostemipage?packId=${pack.id}&productType=${pack.product.productType}`,
                title: "DETAILS"
            },
            fontSize: "P5",
            hexColor: "#AAAAAA"
        }

        const title = "No Cost EMI"
        return {
            widgetType: "NO_COST_EMI_WIDGET",
            title: title,
            hasDividerBelow: true,
            items: [item]
        }
    }

    public static getFitnessNoCostEMIWidgetView(product: OfflineFitnessPack, emiOffer: OfferV2, totalPayable?: number): WidgetView {

        let maxEmiTenure = 12
        emiOffer.addons.forEach((addOn) => {
            if (addOn.config && addOn.config.maxEmiTenure) {
                maxEmiTenure = addOn.config.maxEmiTenure
            }
        })

        const numberOfMonths = CultUtil.getEmiTenureForPack(product.product.durationInDays, maxEmiTenure)
        const url = `curefit://nocostemipage?packId=${product.productId}&productType=${product.productType}`
        const item = {
            imageUrl: "image/transform/EMI.png",
            title: "No Cost EMI starts at just",
            symbol: "₹",
            price: `${Math.round((totalPayable ?? product.price.sellingPrice) / numberOfMonths)}`,
            suffix: "month*",
            action: {
                actionType: "NAVIGATION",
                url: url,
                title: "DETAILS"
            },
            fontSize: "P5",
            hexColor: "#AAAAAA"
        }

        const title = "No Cost EMI"
        return {
            widgetType: "NO_COST_EMI_WIDGET",
            title: title,
            hasDividerBelow: true,
            items: [item]
        }
    }

    private async addLuxHowItWorks(centerName: string, benefits: BenefitEntry[]): Promise<WidgetView> {

        const cultList: Array<{subTitle: string, icon: string}> = benefits.find(benefitEntry => benefitEntry.name.toLowerCase() === "cult") ? [
            {
                subTitle: "Cult centers: Book a class that you like. Reach the center on time & enjoy the workout.",
                icon: "/image/icons/cult/Group_icon.svg",
            },
            {
                subTitle: "Elite and Pro Gyms: Visit a Cult Gym at any time & check-in via your phone & start your workout.",
                icon: "/image/icons/cult/Gym_icon.svg",
            }
        ] : []
        const homeList: Array<{subTitle: string, icon: string}> = benefits.find(benefitEntry => benefitEntry.name.toLowerCase() === "cf_live" || benefitEntry.name.toLowerCase() === "live")
            ? [
                {
                    subTitle: "Choose from the wide variety of online workouts and join in from anywhere.",
                    icon: "/image/icons/cult/live_icon.svg",
                }
            ] : []
        const gymList: Array<{subTitle: string, icon: string}> = benefits.find(benefitEntry => benefitEntry.name.toLowerCase() === "lux_gx") ? [
            {
                subTitle: `Book session at ${centerName} and check-in via your phone & start your workout.`,
                icon: "/image/icons/cult/Gym_icon.svg"
            }
        ] : [
            {
                subTitle: `Visit ${centerName} at any time & check-in via your phone & start your workout.`,
                icon: "/image/icons/cult/Gym_icon.svg"
            }
        ]

        return {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "SMALL",
            header: {
                title: "How It Works",
                style: {
                    fontSize: 18
                },
                color: "#000000"
            },
            items: [
                ...gymList,
                ...cultList,
                ...homeList
            ],
            hideSepratorLines: false,
            hasDividerBelow: false,
            widgetMetric: {
                isWidgetImpressionRequired: true,
                widgetType: "PRODUCT_LIST_WIDGET",
                widgetId: "",
                widgetName: "HOW_IT_WORKS"
            },
        }
    }

    public async addEliteHowItWorks(pack: OfflineFitnessPack, isWeb = false, isLitePack = false): Promise<WidgetView> {
        const liveBenefit: BenefitEntry = pack.product.benefits.find(packBenefit => packBenefit.name === "CF_LIVE")
        const items = []
        if (!isLitePack) {
            items.push({
                subTitle: "Cult centers: Book a class that you like. Reach the center on time & enjoy the workout.",
                icon: isWeb ? "/image/icons/howItWorks/groupWorkout_1.png" : "/image/icons/cult/Group_icon.svg",
                padding: 5
            })
        }

        items.push({
            subTitle: "Gyms: Visit a Cult Gym at any time & check-in via your phone & start your workout.",
            icon: isWeb ? "/image/icons/howItWorks/gymWorkout_1.png" : "/image/icons/cult/Gym_icon.svg",
            padding: 5
        })

        if (liveBenefit?.tickets > 0) {
            items.push({
                subTitle: "Choose from the wide variety of online workouts and join in from anywhere.",
                icon: isWeb ? "/image/icons/howItWorks/liveWorkout_1.png" : "/image/icons/cult/live_icon.svg",
                padding: 5
            })
        }

        return {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "SMALL",
            header: {
                title: "How It Works",
                style: {
                    fontSize: 18
                },
                color: "#000000"
            },
            items,
            hideSepratorLines: true,
            hasDividerBelow: false,
            showSquareIcons: true,
            widgetMetric: {
                isWidgetImpressionRequired: true,
                widgetType: "PRODUCT_LIST_WIDGET",
                widgetId: "",
                widgetName: "HOW_IT_WORKS"
            },
        }
    }

    public async addProHowItWorks(isWeb = false): Promise<WidgetView> {
        return {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "SMALL",
            header: {
                title: "How It Works",
                style: {
                    fontSize: 18
                },
                color: "#000000"
            },
            items: [
                {
                    subTitle: "Cult centers: Book a class that you like. Reach the center on time & enjoy the workout",
                    icon: isWeb ? "/image/icons/howItWorks/groupWorkout_1.png" : "/image/icons/cult/Group_icon.svg",
                    padding: 5
                },
                {
                    subTitle: "Gyms: Visit a Cult Gym at any time & check-in via your phone & start your workout.",
                    icon: isWeb ? "/image/icons/howItWorks/gymWorkout_1.png" : "/image/icons/cult/Gym_icon.svg",
                    padding: 5
                },
                {
                    subTitle: "Choose from the wide variety of online workouts and join in from anywhere.",
                    icon: isWeb ? "/image/icons/howItWorks/liveWorkout_1.png" : "/image/icons/cult/live_icon.svg",
                    padding: 5
                }
            ],
            hideSepratorLines: true,
            hasDividerBelow: false,
            showSquareIcons: true,
            widgetMetric: {
                isWidgetImpressionRequired: true,
                widgetType: "PRODUCT_LIST_WIDGET",
                widgetId: "",
                widgetName: "HOW_IT_WORKS"
            },
        }
    }


    public async addEliteWhatYouGet(cultProduct: OfflineFitnessPack, isWeb = false, isBoosterDetailsPresent = false, userContext: UserContext): Promise<WidgetView> {
        const awayBenefit: BenefitEntry = cultProduct.product.benefits.find(packBenefit => packBenefit.name === "CULT_AWAY")
        const awayCenterBenefit: BenefitEntry = cultProduct.product.benefits.find(packBenefit => packBenefit.name === "CENTER_AWAY")
        const awayCenterCreditsBenefit: BenefitEntry = cultProduct.product.benefits.find(packBenefit => packBenefit.name === "ACCESS_CREDITS")
        const liveBenefit: BenefitEntry = cultProduct.product.benefits.find(packBenefit => packBenefit.name === "CF_LIVE")
        const items = []
        const city = this.cityService.getCityById(cultProduct.clientMetadata?.cityId ?? CatalogueServiceUtilities.getExternalAccessLevelId(cultProduct)).name
        if (isBoosterDetailsPresent) {
            items.push({
                subTitle: `Unlimited access to all centers in ${city ?? "your city"}`,
                icon: isWeb ? "" : "/image/icons/cult/tick_icon.svg",
                padding: 5
            })
        } else {
            // Experiment all India Packs With some hack. Minimum amount of away tickets should be >= 100
            const isAllIndiaPack = CatalogueServiceUtilities.isAllIndiaPack(cultProduct)
            if (CatalogueServiceUtilities.getAccessLevel(cultProduct) === AccessLevel.CITY) {
                if (isAllIndiaPack) {
                    items.push({
                        subTitle: `Unlimited access to all centers across cities`,
                        icon: isWeb ? "" : "/image/icons/cult/tick_icon.svg",
                        padding: 5
                    })
                } else {
                    items.push({
                        subTitle: `Unlimited access to all centers in ${city ?? "your city"}`,
                        icon: isWeb ? "" : "/image/icons/cult/tick_icon.svg",
                        padding: 5
                    })
                    if (awayBenefit && awayBenefit.tickets && awayBenefit.tickets > 0) {
                        items.push({
                            subTitle: `Access ${awayBenefit.tickets} sessions${awayBenefit.type === BenefitType.MONTHLY ? " / month" : ""} across India`,
                            icon: isWeb ? "" : "/image/icons/cult/tick_icon.svg",
                            padding: 5
                        })
                    }
                }
            } else if (CatalogueServiceUtilities.getAccessLevel(cultProduct) === AccessLevel.CENTER) {
                if (CatalogueServiceUtilities.getExternalAccessLevelId(cultProduct)) {
                    const center = await this.centerService.getCenterById(cultProduct.restrictions?.centers?.[0])
                    items.push({
                        subTitle: `Unlimited access to the ${center.name}`,
                        icon: isWeb ? "" : "/image/icons/cult/tick_icon.svg",
                        padding: 5
                    })
                }
                if (awayCenterBenefit && awayCenterBenefit.tickets && awayCenterBenefit.tickets > 0) {
                    items.push({
                        subTitle: `Access ${awayCenterBenefit.tickets} sessions${awayCenterBenefit.type === BenefitType.MONTHLY ? "/ month" : ""} in other centres in ${this.cityService.getCityById(cultProduct.clientMetadata.cityId).name}`,
                        icon: isWeb ? "" : "/image/icons/cult/tick_icon.svg",
                        padding: 5
                    })
                }
                if (awayCenterCreditsBenefit && awayCenterCreditsBenefit.tickets && awayCenterCreditsBenefit.tickets > 0) {
                    items.push({
                        subTitle: `Free ${awayCenterCreditsBenefit.tickets} credits to access other centres \n(up to ${awayCenterCreditsBenefit.tickets} sessions)`,
                        icon: isWeb ? "" : "/image/icons/cult/tick_icon.svg",
                        padding: 5
                    })
                }
            }
            if (cultProduct.product.pauseDays && cultProduct.product.pauseDays > 0) {
                items.push({
                    subTitle: `${cultProduct.product.pauseDays} days of membership pause`,
                    icon: isWeb ? "" : "/image/icons/cult/tick_icon.svg",
                    padding: 5
                })
            }
            if (liveBenefit?.tickets > 0) {
                items.push({
                    subTitle: "1000+ online workout hours",
                    icon: isWeb ? "" : "/image/icons/cult/tick_icon.svg",
                    padding: 5
                })
            }
        }
        return {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: isWeb ? "BULLET" : "SMALL",
            header: {
                title: "What you’ll get",
                style: {
                    fontSize: 18
                },
                color: "#000000"
            },
            items,
            hideSepratorLines: false,
            hasDividerBelow: false,
            showSquareIcons: true,
        }
    }

    public getBoosterPackSelectionModalAction(boosterPack?: OfflineFitnessPack, isElitePack = false, ctaTitle?: string): Action {
        return {
            actionType: "SHOW_CUSTOM_BOTTOM_SHEET",
            title: ctaTitle,
            meta: {
                backdropClickable: false,
                backButtonDisabled: true,
                bgImageUrl: "/image/icons/addons_bg.png",
                widgets: [this.addFomoWidget(boosterPack, false, isElitePack)]
            }
        }
    }

    public async getSkuPlusPackModalAction(exhaustiveBenefitList?: any, skuPlusPackId?: string, priceDifference?: number, isElitePack = false): Promise<Action> {
        if (!skuPlusPackId || _.isNil(exhaustiveBenefitList) || _.isEmpty(exhaustiveBenefitList) || !priceDifference) return null
        return {
            actionType: "SHOW_CUSTOM_BOTTOM_SHEET",
            analyticsData: {
                title: "Upgrade to skuplus bottomsheet",
                type: "SHOW_CUSTOM_BOTTOM_SHEET",
                productType: isElitePack ? "ELITE" : "PRO"
            },
            meta: {
                backdropClickable: false,
                backButtonDisabled: true,
                blurEnabled : true,
                bgImageUrl: isElitePack ? "/image/vm/a8de9e75-8286-4b5f-b7b6-57def1171d36.png" : "/image/vm/6bf281cc-27e0-4270-9ab0-a54d38ea7566.png",
                widgets: [
                    await this.addVerticalUpgradeSkuPlusWidget(exhaustiveBenefitList, skuPlusPackId, priceDifference, isElitePack)
                ]
            }
        }
    }

    public async addHorizontalFomoWidget(boosterPack?: OfflineFitnessPack, isElitePack = false): Promise<WidgetView> {
        const addonItems = this.getAddOnItemsList(boosterPack, true)
        return {
            widgetType: "ADDON_FOMO_HORIZONTAL_WIDGET",
            bgImageUrl: "/image/icons/addons_bg.png",
            itemHeight: 170,
            title: {
                text: "Travel stress free & workout anywhere",
                color: "#FFFFFF",
                maxLine: 2,
                alignment: "center",
                typeScale: "H1",
                richText: false
            },
            actions : [
                {
                    title: `ADD ${isElitePack ? "ELITE" : "PRO"} BOOSTER FOR ₹${Math.round(boosterPack?.price?.listingPrice)}`,
                    actionType: "UPDATE_CHECKOUT_V2_BLOC",
                    variant: "primary",
                    meta: {
                        informUpdate: true,
                        refreshPage: true,
                        addonProductIds: [boosterPack?.productId]
                    }
                }
            ],
            addonItems,
            layoutProps: {
                spacing: {
                    top: 20,
                    bottom: 20
                }
            }
        }
    }

    public async addHorizontalUpgradeSkuPlusWidget(exhaustiveBenefitList?: any, skuPlusPackId?: string, priceDifference?: number, isElitePack = false): Promise<WidgetView> {
        if (!skuPlusPackId || _.isNil(exhaustiveBenefitList) || _.isEmpty(exhaustiveBenefitList) || !priceDifference) return null
        const addonItems = this.getSkuPlusAddOnItemsList(exhaustiveBenefitList, true)
        return {
            widgetType: "ADDON_FOMO_HORIZONTAL_WIDGET",
            hasDivideBelow: false,
            hasDividerTop: false,
            layoutProps: {
              spacing: {
                top: "20",
                bottom: "20"
              }
            },
            itemHeight: 185,
            bgImageUrl: isElitePack ? "/image/vm/a8de9e75-8286-4b5f-b7b6-57def1171d36.png" : "/image/vm/6bf281cc-27e0-4270-9ab0-a54d38ea7566.png",
            actions: [
                {
                    title: `UPGRADE TO ${isElitePack ? "ELITE" : "PRO"} PLUS`,
                    actionType: "MULTI_POP_THEN_NAVIGATION",
                    variant: "secondary",
                    analyticsData: {
                        title: "Upgrade to skuplus horizontal widget",
                        skuType: isElitePack ? "ELITE PLUS" : "PRO PLUS",
                    },
                    meta: {
                        numberOfPop: 1,
                        action: {
                          actionType: "NAVIGATION",
                          url: `curefit://checkout_v2?productId=${skuPlusPackId}`
                        }
                    }
                }
            ],
            title: {
              text: `Add 2x more flexibility with ${(isElitePack ? "[H1,#CB8EFF,ELITE]" : "[H1,#FF9F9E,PRO]")} ${(isElitePack ? " [H3,#CB8EFF,PLUS,italic]" : " [H3,#FF9F9E,PLUS,italic]")} at ₹${priceDifference} extra`,
              color: "#FFFFFF",
              maxLine: "2",
              alignment: "center",
              typeScale: "H1",
              richText: false
            },
            addonItems
        }
    }

    public async addVerticalUpgradeSkuPlusWidget(exhaustiveBenefitList?: any, skuPlusPackId?: string, priceDifference?: number, isElitePack = false): Promise<WidgetView> {
        if (!skuPlusPackId || _.isNil(exhaustiveBenefitList) || _.isEmpty(exhaustiveBenefitList)) return null
        const addonItems = this.getSkuPlusAddOnItemsList(exhaustiveBenefitList, false)
        return {
            widgetType: "ADDON_FOMO_WIDGET",
            itemHorizontalPadding: 35,
            title: {
                text: `Add 2x more flexibility with ${(isElitePack ? "[H1,#CB8EFF,ELITE]" : "[H1,#FF9F9E,PRO]")} ${(isElitePack ? " [H3,#CB8EFF,PLUS,italic]" : " [H3,#FF9F9E,PLUS,italic]")} at ₹${priceDifference} extra`,
                color: "#FFFFFF",
                maxLine: "2",
                alignment: "center",
                typeScale: "H1",
                richText: false
            },
            actions : [
                {
                    title: `UPGRADE TO ${isElitePack ? "ELITE" : "PRO"} PLUS`,
                    actionType: "MULTI_POP_THEN_NAVIGATION",
                    variant: "primary",
                    analyticsData: {
                        title: "Upgrade to skuplus",
                        skuType: isElitePack ? "ELITE PLUS" : "PRO PLUS",
                        userAction: "YES"
                    },
                    meta: {
                        numberOfPop: 2,
                        action: {
                          actionType: "NAVIGATION",
                          url: `curefit://checkout_v2?productId=${skuPlusPackId}`
                        }
                    }
                },
                {
                    title: `NO, I WANT JUST ${isElitePack ? "ELITE" : "PRO"} MEMBERSHIP`,
                    actionType: "MULTI_POP_THEN_NAVIGATION",
                    variant: "tertiary",
                    analyticsData: {
                        title: "Upgrade to skuplus",
                        skuType: isElitePack ? "ELITE PLUS" : "PRO PLUS",
                        userAction: "NO"
                    },
                    meta: {
                        textColor: "66FFFFFF",
                        numberOfPop: 1,
                        action: {
                          actionType: "DYNAMIC_API_CALL",
                          meta: {
                            showSkuPlusModalKey: isElitePack ? showSkuPlusModalElitePlusKey : showSkuPlusModalProPlusKey,
                            showSkuPlusModalState: noShowSkuPlusModalState,
                            apiUrl: "/cart/setfeaturestate"
                          }
                        }
                    }
                }
            ],
            layoutProps: {
                spacing: {
                    top: "20",
                    bottom: "20"
                }
            },
            addonItems
        }
    }


    public async upgradeSkuBannerWidget(upgradedSkuPackId?: string, priceDifference?: number, isElitePack?: boolean, isSelectPack?: boolean, isModalFlow?: boolean): Promise<WidgetView> {
        if (!upgradedSkuPackId || !priceDifference) return null
        const actions: Action[] = [
            {
                title: `UPGRADE TO ${isSelectPack ? "SELECT" : isElitePack ? "ELITE" : "PRO"}`,
                actionType: "MULTI_POP_THEN_NAVIGATION",
                variant: isModalFlow ? "primary" : "secondary",
                analyticsData: {
                    title: "Upgrade sku widget",
                    skuType: isSelectPack ? "SELECT LITE" : isElitePack ? "ELITE LITE" : "PRO LITE",
                },
                meta: {
                    numberOfPop: isModalFlow ? 2 : 1,
                    action: {
                      actionType: "NAVIGATION",
                      url: `curefit://checkout_v2?productId=${upgradedSkuPackId}`
                    }
                }
            }
        ]
        if (isModalFlow) {
            actions.push({
                title: `NO, I WANT JUST ${isSelectPack ? "SELECT LITE" : isElitePack ? "ELITE LITE" : "PRO LITE"} MEMBERSHIP`,
                actionType: "MULTI_POP_THEN_NAVIGATION",
                variant: "tertiary",
                analyticsData: {
                    title: "Upgrade sku widget",
                    skuType: isSelectPack ? "SELECT LITE" : isElitePack ? "ELITE LITE" : "PRO LITE",
                },
                meta: {
                    textColor: "66FFFFFF",
                    numberOfPop: 1
                }
            })
        }
        return {
            widgetType: "ADDON_FOMO_WIDGET",
            bgImageUrl: !isModalFlow ? (isSelectPack ? "/image/vm/e68c4743-962d-4256-9e4f-a3db1cbe0423.png" : isElitePack ? "/image/vm/8488bda0-a39a-44db-b8dd-d40437d2c019.png" : null) : null,
            lottieUrl: isSelectPack ? "/image/mem-exp/lottie/select.json" : isElitePack ? "/image/mem-exp/lottie/elite.json" : null,
            lottieTextList: [
                {originalText: "₹2000", finalText: `₹${priceDifference}`}
            ],
            actions,
            lottieVerticalPadding: isModalFlow ? 15 : 0,
            layoutProps: {
                spacing: {
                    top: isModalFlow ? "0" : "10",
                    bottom: isModalFlow ? "20" : "20"
                }
            },
            hasDivideBelow: false,
            hasDividerTop: false,
        }
    }

    public async upgradeSkuModalAction(skuPlusPackId?: string, priceDifference?: number, isElitePack?: boolean, isSelectPack?: boolean): Promise<Action> {
        if (!skuPlusPackId || !priceDifference) return null
        return {
            actionType: "SHOW_CUSTOM_BOTTOM_SHEET",
            analyticsData: {
                title: "Upgrade to sku bottomsheet",
                type: "SHOW_CUSTOM_BOTTOM_SHEET",
                productType: isSelectPack ? "SELECT" : isElitePack ? "ELITE" : "PRO"
            },
            meta: {
                backdropClickable: true,
                showTopNotch: true,
                blurEnabled : true,
                bgImageUrl: isSelectPack ? "/image/vm/bcf79b41-3421-4428-a643-d67b87f6923e.png" : isElitePack ? "/image/vm/df7c40c4-14f2-4661-bf80-8b5345d84f1f.png" : null,
                widgets: [
                    await this.upgradeSkuBannerWidget(skuPlusPackId, priceDifference, isElitePack, isSelectPack, true)
                ]
            }
        }
    }

    public getHorizontalAddonItems(text: string, image: string) {
        return {
            itemAspectRatio: 1,
            title: {
                text: text,
                color: "#FFFFFF",
                maxLine: "3",
                alignment: "center",
                typeScale: "P1",
                richText: false
            },
            imageData: {
                url: image,
                imageType: "REGULAR",
                height: 47,
                width: 47
            }
        }
    }

    public addFomoWidget(boosterPack?: OfflineFitnessPack, isRemovalFlow = false, isElitePack = false): WidgetView {
        const addonItems = this.getAddOnItemsList(boosterPack, false, isRemovalFlow)
        return {
            widgetType: "ADDON_FOMO_WIDGET",
            itemHorizontalPadding: 35,
            title: {
                text: isRemovalFlow ? "You are opting out of our best benefits" : `Level up your pack with the ${isElitePack ? "ELITE" : "PRO"} Booster`,
                color: "#FFFFFF",
                maxLine: 2,
                alignment: "center",
                typeScale: "H1",
                richText: false
            },
            subtitle: {
                text: isRemovalFlow ? "Benefits you'll miss out on" : "10% of cult athletes are already seeing a difference",
                color: isRemovalFlow ? "66FFFFFF" : "#FFFFFF",
                maxLine: 2,
                alignment: "center",
                typeScale: isRemovalFlow ? "P6" : "P8",
                richText: false
            },
            actions : [
                {
                    title: isRemovalFlow ? "I want to keep the boosters" : `ADD ${isElitePack ? "ELITE" : "PRO"} BOOSTER FOR ₹${Math.round(boosterPack?.price?.listingPrice)}`,
                    actionType: isRemovalFlow ? "EMPTY_ACTION" : "UPDATE_CHECKOUT_V2_BLOC",
                    variant: "primary",
                    meta: isRemovalFlow ? null : {
                        informUpdate: true,
                        refreshPage: true,
                        addonProductIds: [boosterPack?.productId]
                    }
                },
                {
                    title: isRemovalFlow ? "I want to miss out on the benefits" : "CONTINUE WITHOUT ADDED BENEFITS",
                    actionType: "UPDATE_CHECKOUT_V2_BLOC",
                    variant: "tertiary",
                    meta: {
                        informUpdate: true,
                        refreshPage: true,
                        addonProductIds: [],
                        textColor: "66FFFFFF"
                    }
                }
            ],
            baseAction: {
                actionType: "POP_NAVIGATION"
            },
            addonItems,
            layoutProps: {
                spacing: {
                    top: 0,
                    bottom: 20
                }
            }
        }
    }

    public getSkuPlusModalActionsWidget(skuPlusPackId: string, userContext?: UserContext, isElitePack = false): WidgetView {
        if (userContext?.sessionInfo?.appVersion >= 10.81) {
            return {
                widgetType: "ACTION_LIST_WIDGET",
                hasDivideBelow: false,
                layoutProps: {
                  spacing: {top: "20", bottom: "30"}
                },
                actionsDirection: "vertical",
                actionList: [
                    {
                        title: `UPGRADE TO ${isElitePack ? "ELITE" : "PRO"} PLUS`,
                        actionType: "MULTI_POP_THEN_NAVIGATION",
                        variant: "primary",
                        meta: {
                            numberOfPop: 2,
                            action: {
                              actionType: "NAVIGATION",
                              url: `curefit://checkout_v2?productId=${skuPlusPackId}`
                            }
                        }
                    },
                    {
                        title: `NO, I WANT JUST ${isElitePack ? "ELITE" : "PRO"} MEMBERSHIP`,
                        actionType: "MULTI_POP_THEN_NAVIGATION",
                        variant: "tertiary",
                        meta: {
                            textColor: "66FFFFFF",
                            numberOfPop: 1,
                            action: {
                              actionType: "DYNAMIC_API_CALL",
                              meta: {
                                showSkuPlusModalKey: isElitePack ? showSkuPlusModalElitePlusKey : showSkuPlusModalProPlusKey,
                                showSkuPlusModalState: noShowSkuPlusModalState,
                                apiUrl: "/cart/setfeaturestate"
                              }
                            }
                        }
                    }
                ]
            }
        }
        return {
            widgetType: "ADDON_FOMO_WIDGET",
            itemHorizontalPadding: 35,
            actions : [
                {
                    title: `UPGRADE TO ${isElitePack ? "ELITE" : "PRO"} PLUS`,
                    actionType: "MULTI_POP_THEN_NAVIGATION",
                    variant: "primary",
                    meta: {
                        numberOfPop: 2,
                        action: {
                          actionType: "NAVIGATION",
                          url: `curefit://checkout_v2?productId=${skuPlusPackId}`
                        }
                    }
                },
                {
                    title: `NO, I WANT JUST ${isElitePack ? "ELITE" : "PRO"} MEMBERSHIP`,
                    actionType: "MULTI_POP_THEN_NAVIGATION",
                    variant: "tertiary",
                    meta: {
                        textColor: "66FFFFFF",
                        numberOfPop: 1,
                        action: {
                          actionType: "DYNAMIC_API_CALL",
                          meta: {
                            showSkuPlusModalKey: isElitePack ? showSkuPlusModalElitePlusKey : showSkuPlusModalProPlusKey,
                            showSkuPlusModalState: noShowSkuPlusModalState,
                            apiUrl: "/cart/setfeaturestate"
                          }
                        }
                    }
                }
            ],
            layoutProps: {
                spacing: {
                    top: "0",
                    bottom: "10"
                }
            }
        }
    }

    public getAddonItems(text: string, image: string, isRemovalFlow?: boolean) {
        return {
            title: {
                text: text,
                color: isRemovalFlow ? "66FFFFFF" : "#FFFFFF",
                maxLine: "2",
                alignment: "left",
                typeScale: "P1",
                richText: false,
                strikethrough: isRemovalFlow
            },
            imageData: {
                url: image,
                imageType: "REGULAR",
                height: 47,
                width: 47,
                grayscale: isRemovalFlow
            }
        }
    }

    public getAddOnItemsList(boosterPack?: OfflineFitnessPack, isHorizontal = false, isRemovalFlow = false) {
        const addonItems: any = []
        if (boosterPack) {
            boosterPack?.product?.benefits?.slice(0, 3)?.map((benefit: any) => {
                const title: string = benefit?.displayTitle
                const iconUrl: string = benefit?.meta?.icon
                if (title && iconUrl) {
                    addonItems.push(isHorizontal ? this.getHorizontalAddonItems(title, iconUrl) : this.getAddonItems(title, iconUrl, isRemovalFlow))
                }
            })
        }
        return addonItems
    }

    public getSkuPlusAddOnItemsList(exhaustiveBenefitList?: any, isHorizontal = false) {
        const addonItems: any = []
        if (exhaustiveBenefitList?.length > 0) {
            if (isHorizontal) {
                exhaustiveBenefitList?.map((benefit: any) => {
                    const title: string = benefit?.displayTitle
                    const iconUrl: string = benefit?.displayIcon
                    if (title && iconUrl) {
                        addonItems.push(this.getHorizontalAddonItems(title, iconUrl))
                    }
                })
            } else {
                exhaustiveBenefitList?.slice(0, 6)?.map((benefit: any) => {
                    const title: string = benefit?.displayTitle
                    const iconUrl: string = benefit?.displayIcon
                    if (title && iconUrl) {
                        addonItems.push(this.getAddonItems(title, iconUrl))
                    }
                })
            }
        }
        return addonItems
    }

    public async addBoosterBenefits(boosterPack?: OfflineFitnessPack, isWeb = false, isElitePack = true): Promise<WidgetView> {
        const items: any = []
        if (boosterPack) {
            boosterPack?.product?.benefits?.slice(0, 3)?.map((benefit: any) => {
                const title: string = benefit?.displayTitle
                if (title) {
                    items.push(this.getGradientTextItem(title, isWeb, isElitePack))
                }
            })
        }

        return {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: isWeb ? "BULLET" : "SMALL",
            header: {
                title: "Booster Benefits",
                style: {
                    fontSize: 18
                }
            },
            headerGradientColor : {
                direction : "DIAGONAL",
                gradientType : "LINEAR",
                colors : ["#9648FF", "#EED3FF", "#6A4693"]
            },
            headerRightAction : {
                title: "REMOVE",
                actionType: "UPDATE_CHECKOUT_V2_BLOC",
                variant: "tertiarySmall",
                meta: {
                    informUpdate: true,
                    refreshPage: true,
                    addonProductIds: [],
                    textColor: "#99FFFFFF"
                }
            },
            items,
            layoutProps: {
                spacing: {
                    top: 0,
                    bottom: 20
                }
            },
            collapsable: false,
            hideSepratorLines: true,
            hasDividerBelow: true,
            showSquareIcons: true,
        }
    }

    public async getSkuPlusBenefitsWidget(pack?: AugmentedOfflineFitnessPack, isElitePlusPack = false): Promise<WidgetView> {
        const items: any = []
        if (pack) {
            pack?.augments?.exhaustiveBenefitList?.map((benefit: ExhaustivePackBenefit) => {
                const title: string = benefit?.displayTitle
                if (title) {
                    items.push(this.getGradientTextItem(title, false, isElitePlusPack))
                }
            })
        }
        if (items?.length === 0)  return null

        return {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "SMALL",
            header: {
                textData: {
                    text: "Benefits of " + (isElitePlusPack ? "[H2,#CB8EFF,ELITE]" : "[H2,#FF9F9E,PRO]") + (isElitePlusPack ? " [P5,#CB8EFF,PLUS,italic]" : " [P5,#FF9F9E,PLUS,italic]"),
                    isRichText: true,
                    typeScale: "H3"
                }
            },
            items,
            layoutProps: {
                spacing: {
                    top: 0,
                    bottom: 20
                }
            },
            collapsable: false,
            hideSepratorLines: false,
            hasDividerBelow: false,
            showSquareIcons: true,
        }
    }

    public async getWhatYouGetBenefitsWidget(pack?: AugmentedOfflineFitnessPack, isPlusPack?: boolean, isSelectPack?: boolean, isLitePack?: boolean, isElitePack?: boolean): Promise<any> {
        const items: any = []
        if (pack) {
            pack?.augments?.exhaustiveBenefitList?.map((benefit: ExhaustivePackBenefit) => {
                let title: string = undefined
                let subtitle: string = undefined
                let imageUrl: string = undefined
                const tileColor: string = isPlusPack ? "#2C2C4C99" : "#2C2C4C66"
                const name: string = benefit?.name
                const tickets: number = benefit?.tickets
                if (name) {
                    title = CultUtil.getBenefitTitleFromBenefitName(name, tickets)
                    subtitle = CultUtil.getBenefitSubtitleFromBenefitName(name, benefit?.meta?.displaySubTitle, isElitePack)
                    imageUrl = CultUtil.getBenefitImageUrlFromBenefitName(name, isPlusPack, isSelectPack, isLitePack, isElitePack)
                    if (title && subtitle && imageUrl) {
                        items.push(this.getBenefitTileItem(title, subtitle, imageUrl, tileColor))
                    }
                    if (name === "CULT") {
                        imageUrl = isPlusPack ? (isSelectPack ? "/image/sku_plus_icons/gym_sp.svg" : "/image/sku_plus_icons/gym_ep.svg")
                                   : isLitePack ? (isSelectPack ? "/image/sku_plus_icons/gym.svg" : "/image/sku_icons/gym_el.svg")
                                   : isSelectPack ? "/image/sku_icons/gym_select.svg" :  isElitePack ? "/image/sku_icons/gym_eg.svg" : "/image/sku_plus_icons/gym.svg"
                        items.push(this.getBenefitTileItem("Unlimited", "Gym Access", imageUrl, tileColor))
                    }
                }
            })
        }
        if (items?.length === 0)  return null

        return {
            widgetType: "WHAT_YOU_GET_BENEFITS_WIDGET",
            hasDivideBelow: false,
            hasDividerTop: false,
            layoutProps: {
              spacing: {
                top: "30",
                bottom: "20"
              }
            },
            title: {
              text: "What you'll get",
              typeScale: "H2"
            },
            benefitItemList: items
          }
    }

    public getGradientTextItem(text: string, isWeb = false, isElitePack = true) {
        return {
            title: text,
            titleFont: "P4",
            titleGradientColor : {
                direction : "DIAGONAL",
                gradientType : "LINEAR",
                colors : isElitePack ? ["#9648FF", "#EED3FF", "#6A4693"] : ["#FF9F9E", "#FFDADD", "#FFBDBC", "#FB857F"],
            },
            icon: isWeb ? "" : isElitePack ? "/image/icons/purple_tick.png" : "/image/sku_plus/pro_plus_tick.png",
            iconSize : 35,
            padding: 5
        }
    }

    public getBenefitTileItem(title?: string, subtitle?: string, imageUrl?: string, tileColor?: string) {
        return {
            title: {
              text: title,
              typeScale: "P3",
              opacity: 0.8
            },
            subtitle: {
              text: subtitle,
              typeScale: "P8",
              opacity: 0.8
            },
            imageUrl: imageUrl,
            tileColor: tileColor
        }
    }

    public async addGymFitWhatYouGet(gymfitFitnessProduct: OfflineFitnessPack, isWeb = false, isBoosterDetailsPresent = false): Promise<WidgetView> {
        const awayBenefit: BenefitEntry = gymfitFitnessProduct.product.benefits.find(benefit => benefit.name === "CULT")
        const awayCenterBenefit: BenefitEntry = gymfitFitnessProduct.product.benefits.find(benefit => benefit.name === "CENTER_AWAY")
        const awayCenterCreditsBenefit: BenefitEntry = gymfitFitnessProduct.product.benefits.find(benefit => benefit.name === "ACCESS_CREDITS")
        const liveBenefit: BenefitEntry = gymfitFitnessProduct.product.benefits.find(benefit => benefit.name === "CF_LIVE")
        const items = []
        if (isBoosterDetailsPresent) {
            items.push({
                subTitle: `Unlimited access to all PRO centers in ${this.cityService.getCityById(gymfitFitnessProduct.restrictions?.cities?.[0])?.name ?? "city"}`,
                icon: isWeb ? "" : "/image/icons/cult/tick_icon.svg",
                padding: 5
            })
        } else {
            if (CatalogueServiceUtilities.getAccessLevel(gymfitFitnessProduct) === GymfitAccessLevel.CITY) {
                items.push({
                    subTitle: `Unlimited access to all PRO centers in ${this.cityService.getCityById(gymfitFitnessProduct.restrictions?.cities?.[0])?.name ?? "city"}`,
                    icon: isWeb ? "" : "/image/icons/cult/tick_icon.svg",
                    padding: 5
                })
                if (awayBenefit && awayBenefit.tickets && awayBenefit.tickets > 0) {
                    items.push({
                        subTitle: `Access ${awayBenefit.tickets} sessions${awayBenefit.type === BenefitType.MONTHLY ? "/ month" : ""} in Elite centers`,
                        icon: isWeb ? "" : "/image/icons/cult/tick_icon.svg",
                        padding: 5
                    })
                }
                if (awayCenterCreditsBenefit && awayCenterCreditsBenefit.tickets && awayCenterCreditsBenefit.tickets > 0) {
                    items.push({
                        subTitle: `Free ${awayCenterCreditsBenefit.tickets} credits to access other centres \n(up to ${awayCenterCreditsBenefit.tickets} sessions)`,
                        icon: isWeb ? "" : "/image/icons/cult/tick_icon.svg",
                        padding: 5
                    })
                }
            } else if (CatalogueServiceUtilities.getAccessLevel(gymfitFitnessProduct) === GymfitAccessLevel.CENTER) {

                const centerId = gymfitFitnessProduct?.restrictions?.centers?.[0]
                if (centerId) {
                    const center = await this.centerService.getCenterById(centerId)
                    items.push({
                        subTitle: `Unlimited access to the ${center.name}`,
                        icon: isWeb ? "" : "/image/icons/cult/tick_icon.svg",
                        padding: 5
                    })
                }
                if (awayCenterBenefit && awayCenterBenefit.tickets && awayCenterBenefit.tickets > 0) {
                    items.push({
                        subTitle: `Access ${awayCenterBenefit.tickets} sessions${awayCenterBenefit.type === BenefitType.MONTHLY ? " / month" : ""} in other centres in ${this.cityService.getCityById(gymfitFitnessProduct.clientMetadata?.cityId).name}`,
                        icon: isWeb ? "" : "/image/icons/cult/tick_icon.svg",
                        padding: 5
                    })
                }
                if (awayCenterCreditsBenefit && awayCenterCreditsBenefit.tickets && awayCenterCreditsBenefit.tickets > 0) {
                    items.push({
                        subTitle: `Free ${awayCenterCreditsBenefit.tickets} credits to access other centres \n(up to ${awayCenterCreditsBenefit.tickets} sessions)`,
                        icon: isWeb ? "" : "/image/icons/cult/tick_icon.svg",
                        padding: 5
                    })
                }
            }
            if (gymfitFitnessProduct.product.pauseDays && gymfitFitnessProduct.product.pauseDays > 0) {
                items.push({
                    subTitle: `${gymfitFitnessProduct.product.pauseDays} Days of membership pause days`,
                    icon: isWeb ? "" : "/image/icons/cult/tick_icon.svg",
                    padding: 5
                })
            }
            if (liveBenefit?.tickets > 0) {
                items.push({
                    subTitle: "1000+ online workout hours",
                    icon: isWeb ? "" : "/image/icons/cult/tick_icon.svg",
                    padding: 5
                })
            }
        }

        return {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: isWeb ? "BULLET" : "SMALL",
            header: {
                title: isWeb ? "About this pack" :  "What you’ll get",
                style: {
                    fontSize: isWeb ? 24 : 18
                },
                color: "#000000"
            },
            items,
            hideSepratorLines: false,
            hasDividerBelow: false,
            showSquareIcons: true,
        }


    }

    private async getEliteBottomCTA(order: Order, userContext: UserContext, cultProduct: OfflineFitnessPack, boosterPack?: OfflineFitnessPack, showBoosterPackSelectionModal?: boolean, doesUserBelongsToBoosterPackSelectionPreModalSegment?: boolean): Promise<WidgetView> {
        const minEligibleDate = TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, new Date(order.products[0].option.startDate))
        const maximumDate = TimeUtil.addDays(userContext.userProfile.timezone, minEligibleDate, 30)
        const showBoosterPackSelectionPostModal = showBoosterPackSelectionModal && !doesUserBelongsToBoosterPackSelectionPreModalSegment
        let url = "curefit://fl_selectpackpreferredcenters?productType=FITNESS&productId=" + cultProduct.productId
        if (this.isLitePack(cultProduct)) {
            url += "&flow=LITE"
        }
        return {
            widgetType: "PACK_CENTER_DATE_CHECKOUT_CTA",
            centerNotSelectedString: "Select preferred center",
            centerSelectedString: "Preferred center: ",
            dateNotSelectedString: "Select start date",
            dateSelectedString: "Starts on: ",
            actionsList: [
                {
                    actionType: "NAVIGATION",
                    url,
                    title: "Select preferred center",
                    analyticsData: this.getEliteAnalyticsData(cultProduct)
                },
                {
                    title: "Select start date",
                    actionType: "SELECT_CHECKOUT_DATE",
                    meta: {
                        startDate: order.products[0].option.startDate,
                        endDate: maximumDate,
                    },
                    analyticsData: this.getEliteAnalyticsData(cultProduct)
                },
                showBoosterPackSelectionPostModal ? this.getBoosterPackSelectionModalAction(boosterPack, true, "PROCEED") : {
                    title: (order.totalAmountPayable === 0) ? this.freeTitleTag : `Pay ${RUPEE_SYMBOL}${order.totalAmountPayable}`,
                    actionType: "CARE_CART_PAY_NOW",
                    url: "curefit://payment",
                    analyticsData: this.getEliteAnalyticsData(cultProduct),
                    meta: (order.totalAmountPayable === 0) ? {payFree: true} : null
                }
            ],
        }
    }

    private getEliteAnalyticsData(cultProduct: OfflineFitnessPack) {
        return {
            page: CatalogueServiceUtilities.getAccessLevel(cultProduct) === AccessLevel.CITY ? "elite_checkout" : "select_checkout",
            city: this.cityService.getCityById(cultProduct.clientMetadata.cityId).name,
            centerId: CatalogueServiceUtilities.getAccessLevel(cultProduct) === AccessLevel.CENTER ? cultProduct.restrictions?.centers?.[0] : null,
        }
    }

    private async getProBottomCTA(order: Order, userContext: UserContext, gymfitFitnessProduct: OfflineFitnessPack, boosterPack?: OfflineFitnessPack, showBoosterPackSelectionModal?: boolean, doesUserBelongsToBoosterPackSelectionPreModalSegment?: boolean): Promise<WidgetView> {
        const minEligibleDate = TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, new Date(order.products[0].option.startDate))
        const maximumDate = TimeUtil.addDays(userContext.userProfile.timezone, minEligibleDate, 30)
        const showBoosterPackSelectionPostModal = showBoosterPackSelectionModal && !doesUserBelongsToBoosterPackSelectionPreModalSegment
        return {
            widgetType: "PACK_CENTER_DATE_CHECKOUT_CTA",
            dateNotSelectedString: "Select start date",
            dateSelectedString: "Starts on: ",
            centerNotSelectedString: "Select preferred center",
            centerSelectedString: "Preferred center: ",
            actionsList: [
                this.getPreferredCenterCtaForPro(userContext, gymfitFitnessProduct),
                {
                    title: "Select start date",
                    actionType: "SELECT_CHECKOUT_DATE",
                    meta: {
                        startDate: order.products[0].option.startDate,
                        endDate: maximumDate,
                    },
                    analyticsData: this.getProAnalyticsData(gymfitFitnessProduct)
                },
                showBoosterPackSelectionPostModal ? this.getBoosterPackSelectionModalAction(boosterPack, false, "PROCEED") : {
                    title: (order.totalAmountPayable === 0) ? this.freeTitleTag : `Pay ${RUPEE_SYMBOL}${order.totalAmountPayable}`,
                    actionType: "CARE_CART_PAY_NOW",
                    url: "curefit://payment",
                    analyticsData: this.getProAnalyticsData(gymfitFitnessProduct),
                    meta: (order.totalAmountPayable === 0) ? {payFree: true} : null
                }
            ],
        }
    }

    private getPreferredCenterCtaForPro(userContext: UserContext, gymfitFitnessProduct: OfflineFitnessPack) {
        const appVersion = userContext.sessionInfo.appVersion
        const userAgent = userContext.sessionInfo.userAgent

        if (AppUtil.isProPreferredCenterSupported(appVersion, userAgent)) {
            return {
                actionType: "NAVIGATION",
                url: "curefit://fl_selectpackpreferredcenters?productType=GYMFIT_FITNESS_PRODUCT&productId=" + gymfitFitnessProduct.productId,
                title: "Select preferred center",
                analyticsData: this.getProAnalyticsData(gymfitFitnessProduct)
            }
        } else {
            return {}
        }

    }

    private getProAnalyticsData(gymfitFitnessProduct: OfflineFitnessPack) {
        return {
            page: CatalogueServiceUtilities.getAccessLevel(gymfitFitnessProduct) === GymfitAccessLevel.CITY ? "pro_checkout" : "select_checkout",
            city: this.cityService.getCityById(gymfitFitnessProduct.clientMetadata.cityId)?.name ?? "city",
            centerId: CatalogueServiceUtilities.getAccessLevel(gymfitFitnessProduct) === GymfitAccessLevel.CENTER ? gymfitFitnessProduct.restrictions?.centers?.[0] : null,
        }
    }

    private async addGenericOfferAddonWidget(order: Order): Promise<WidgetView> {
        if (_.isEmpty(order.offersApplied)) return
        const offers = (await this.offerServiceV3.getOffersByIds(order.offersApplied))?.data
        const staticOfferItems: IOfferAddonData[] = Object.keys(offers).map((offerId) => {
            const {description, tNc, tNcUrl} = offers[offerId]
            return {
                description,
                action: {
                    actionType: "SHOW_OFFERS_TNC_MODAL",
                    meta: {
                        title: "Offer Details",
                        dataItems: tNc,
                        url: tNcUrl
                    }
                },
                prefixImageurl: "/image/icons/cult/offer_icon.svg",
                fontSize: "P5",
                hexColor: "#AAAAAA"
            }
        })

        const filteredOffers = staticOfferItems.filter(offer => !_.isEmpty(offer.description))

        if (!filteredOffers || filteredOffers.length < 1) {
            return null
        }

        return {
            widgetType: "OFFER_ADDONS_WIDGET",
            data: {
                title: "Offers Applied",
                offers: filteredOffers,
                type: "CHECKOUT"
            },
            orientation: "RIGHT",
            actionSupported: true,
            containerStyle: {
                paddingLeft: 25,
            },
            imageContainerStyle: {
                flex: 0.12,
            },
            bulletViewStyle: {
                flex: 0.12,
            }
        }
    }

    private async addGymPTOfferAddonWidget(order: Order): Promise<WidgetView> {
        if (_.isEmpty(order.offersApplied)) return
        const offers = (await this.offerServiceV3.getOffersByIds(order.offersApplied))?.data
        const staticOfferItems: IOfferAddonData[] = Object.keys(offers).map((offerId) => {
            const {description = ""} = offers[offerId]
            return {
                description
            }
        })
        return {
            widgetType: "OFFER_ADDONS_WIDGET",
            data: {
                title: "What else you get",
                offers: staticOfferItems,
                type: "CHECKOUT"
            },
            orientation: "RIGHT",
            containerStyle: {
                paddingLeft: 25,
            },
            imageContainerStyle: {
                flex: 0.12,
            },
            bulletViewStyle: {
                flex: 0.12,
            }
        }
    }

    private async addOfferAddonWidget(order: Order, subCategoryCode?: SUB_CATEGORY_CODE, enableOfferAction?: boolean): Promise<WidgetView> {
        if (_.isEmpty(order.offersApplied)) return
        const offerV2MapPromise: Promise<{
            [key: string]: OfferV2;
        }> = this.offerServiceV3.getOffersByIds(order.offersApplied).then(o => o.data)
        const offerAddonWidgetParams: IOfferAddonWidgetParams = {
            logger: this.logger,
            offerV2MapPromise: offerV2MapPromise,
            useLabel: "CART_LABEL"
        }
        if (enableOfferAction) {
            offerAddonWidgetParams.title = "Offers Applied"
        }
        let offerAddonWidget: OfferAddonWidget = new OfferAddonWidget()
        offerAddonWidget = await offerAddonWidget.buildView(offerAddonWidgetParams)
        if (_.isEmpty(offerAddonWidget)) {
            return undefined
        }
        if (subCategoryCode === "LIVE_PERSONAL_TRAINING") {
            offerAddonWidget.productType = "LIVE_PERSONAL_TRAINING"
        } else if (subCategoryCode === "LIVE_SGT") {
            offerAddonWidget.productType = "LIVE_SGT"
        }
        offerAddonWidget.orientation = "RIGHT"
        offerAddonWidget.containerStyle = {
            paddingLeft: 25
        }
        offerAddonWidget.imageContainerStyle = {
            flex: 0.12
        }
        offerAddonWidget.bulletViewStyle = {
            flex: 0.12
        }
        if (enableOfferAction) {
            offerAddonWidget.actionSupported = true
        }
        return offerAddonWidget
    }

    async getNoCostEMIWidget(order: Order, product: DiagnosticProduct, subCategoryCode?: SUB_CATEGORY_CODE): Promise<WidgetView> {
        if (_.isEmpty(order.offersApplied)) return
        const offerV2Map: { [key: string]: OfferV2 } = await this.offerServiceV3.getOffersByIds(order.offersApplied).then(o => o.data)
        _.each(offerV2Map, (offer: OfferV2) => {
            if (CareUtil.isTransformTenant(subCategoryCode)) {
                if (offer.constraints.paymentChannel === "EMI") {
                    return TransformUtil.getTransformNoCostEMIWidget(product, offer, order.totalAmountPayable)
                }
            }
        })
        return null
    }

    async getMultiConsultationCard(orderProduct: OrderProduct, userContext: UserContext): Promise<ListingActionableCardWidget> {
        const productPromise = this.catalogueService.getProduct(orderProduct.productId)
        const doctorPromise = this.healthfaceService.getDoctorDetails(orderProduct.option.doctorId)
        const patientPromise = this.healthfaceService.getPatientDetails(orderProduct.option.patientId)
        const consultationProduct = <ConsultationProduct>(await productPromise)
        const vertical = CareUtil.getVerticalForConsultation(consultationProduct?.doctorType)
        const doctor = await doctorPromise
        const isVideo = consultationProduct.consultationMode === "ONLINE"
        const isLC = consultationProduct.doctorType === "LC"
        const tz = userContext.userProfile.timezone
        const widget: ListingActionableCardWidget = {
            widgetType: "LISTING_ACTIONABLE_CARD_WIDGET",
            tag: undefined,
            showDivider: false,
            title: `${doctor.doctorTypes[0].type.displayValue} Consultation`,
            subTitle: `With ${doctor.name} for ${(await patientPromise).name}`,
            imageUrl: doctor.displayImage,
            footer: [{
                text: `${isVideo ? `Video |
                    ${TimeUtil.formatEpochInTimeZone(tz, orderProduct.option.startTime, "ddd, D MMM, h:mm A")}` :
                    `At Centre | ${TimeUtil.formatEpochInTimeZone(tz, orderProduct.option.startTime, "ddd, D MMM, h:mm A")}`}`,
                icon: isVideo ? "video" : "incentre"
            }],
            actions: [],
            cardAction: {
                actionType: "NAVIGATION",
                url: ActionUtil.teleconsultationSingle(userContext, orderProduct.productId, consultationProduct.urlPath, orderProduct.option.bookingId.toString(), undefined, vertical)
            }
        }
        return Promise.resolve(widget)
    }

    private offerOverridenAlertInfo(): AlertInfo {
        const alertInfo: AlertInfo = {
            title: "Cart Updated",
            isInlineMessage: true,
            subTitle: "Your cart has been updated as per the offer valid for you.",
            actions: [{title: "Ok", actionType: "HIDE_ALERT_MODAL"}]
        }
        return alertInfo
    }

    getSetCodeBasedTitle(diagnosticsProduct: DiagnosticProduct, baseProduct: Product) {
        switch (diagnosticsProduct?.setCode) {
            case "COUPLE_THERAPY_SET":
                return `Couple Therapy Pack ${baseProduct.title}`
            case "MIND_THERAPY_SET":
                `1 on 1 Therapy Pack ${baseProduct.title}`
            default:
                return baseProduct.title
        }
    }

    async getCartBaseProductSummaryWidget(baseOrder: Order, userAgent: UserAgent, baseProduct: Product, patientPromise?: Promise<Patient>): Promise<CartBaseProductSummaryWidget> {
        let subTitle
        let title = baseProduct.title
        let icon
        let imageUrl
        let showDivider = undefined
        let productType: ProductType
        if (baseProduct.productType === "BUNDLE") {
            const diagnosticsProduct = <DiagnosticProduct>baseProduct
            if (diagnosticsProduct.subCategoryCode === "MIND_THERAPY") {
                title = this.getSetCodeBasedTitle(diagnosticsProduct, baseProduct)
                imageUrl = diagnosticsProduct.imageUrl
            } else if (diagnosticsProduct.subCategoryCode === "PERSONAL_TRAINING") {
                title = "1 on 1 Training Pack"
                subTitle = baseProduct.title
                icon = "BOXING"
            } else if (diagnosticsProduct.subCategoryCode === "LIVE_PERSONAL_TRAINING" || diagnosticsProduct.subCategoryCode === "LIVE_SGT") {
                showDivider = false
                productType = diagnosticsProduct.subCategoryCode as ProductType
            } else if (CareUtil.isTransformTenant(diagnosticsProduct.subCategoryCode)) {
                showDivider = false
                productType = "TRANSFORM"
            }
        }
        const widget = new CartBaseProductSummaryWidget(title, subTitle, undefined, icon, undefined, undefined, imageUrl)
        if (patientPromise && baseProduct.productType === "BUNDLE" && patientPromise && baseProduct.productType === "BUNDLE" && (
            CARE_BUNDLE_SUBCATEGORY_CODES.includes((<DiagnosticProduct>baseProduct).subCategoryCode)
        )) {
            widget.price = baseOrder.productSnapshots[0].price
            widget.imageUrl = (baseProduct && baseProduct.imageUrl) || undefined
            const patient = await patientPromise
            const age = !_.isEmpty(patient) && !_.isEmpty(patient.formattedAge) ? patient.formattedAge.numOfYears : undefined
            widget.subTitle = age && age !== -1 ? `For ${patient.name} | ${patient.gender}, ${age}` : `For ${patient.name} | ${patient.gender}`
        } else if (baseProduct.productType === "BUNDLE" && ((<DiagnosticProduct>baseProduct).subCategoryCode === "LIVE_PERSONAL_TRAINING" || (<DiagnosticProduct>baseProduct).subCategoryCode === "LIVE_SGT" || CareUtil.isTransformTenant((<DiagnosticProduct>baseProduct).subCategoryCode))) {
            widget.price = baseOrder.productSnapshots[0].price
            widget.imageUrl = (baseProduct && baseProduct.imageUrl) || undefined
        }
        if (userAgent === "DESKTOP") {
            widget.orientation = "LEFT"
            if (CareUtil.isTransformTenant((<DiagnosticProduct>baseProduct).subCategoryCode)) {
                widget.imageUrl = "/image/transform-coach/info/transform-web.png"
            }
        }
        if (showDivider !== undefined) {
            widget.showDivider = showDivider
        }
        widget.productType = productType
        return widget
    }

    async getPatientSummaryWidget(userAgent: UserAgent, products: OrderProduct[], patientPromise: Promise<Patient>): Promise<CartPatientSummaryWidget> {
        const patient = await patientPromise
        const age = !_.isEmpty(patient) && !_.isEmpty(patient.formattedAge) ? patient.formattedAge.numOfYears : undefined
        const subtitle = age && age !== -1 && patient.gender ? `${patient.gender}, ${age}` : (patient.gender ? `${patient.gender}` : "")
        const widget = new CartPatientSummaryWidget(patient.name, subtitle, "PERSON")
        if (userAgent === "DESKTOP") {
            widget.orientation = "RIGHT"
        }
        return widget
    }

    async getTransformPaymentSummaryWidget(userContext: UserContext, order: Order, productType?: ProductType, title?: string, duration?: string, suffix?: string, footer?: string): Promise<PaymentDetailsWidget> {
        const billingInfo = this.offerHelper.getOrderBilling(order)
        const listingBrand: ListingBrandIdType = EatUtil.getListingBrandFromOrder(order)
        const offersApplied = !_.isEmpty(order.offersApplied) ? await this.offerServiceV3.getOffersByIds(order.offersApplied) : {}
        const priceDetails = this.getPriceDetailsForTransform(userContext, {
            ...billingInfo,
            orderDiscountsWithTitle: offersApplied
        }, _.map(offersApplied), listingBrand, order.deliveryCharges, order.packagingCharges, false, false, false)
        const paymentDetailWidget: PaymentDetailsWidget = {
            title: title,
            footer: footer,
            suffix: suffix,
            duration: duration,
            widgetType: "PAYMENT_DETAILS_WIDGET",
            priceDetails: priceDetails.components,
            finalPrice: {
                listingPrice: _.round(billingInfo.total, 2),
                mrp: _.round(billingInfo.total, 2),
                currency: billingInfo.currency
            }
        }
        paymentDetailWidget.productType = productType
        if (userContext.sessionInfo.userAgent === "DESKTOP") {
            paymentDetailWidget.orientation = "RIGHT"
        }
        return paymentDetailWidget
    }

    async getTfBootcampPaymentSummaryWidget(userContext: UserContext, order: Order, productType?: ProductType, title?: string, duration?: string, suffix?: string, footer?: string, packHeader?: string, hexColor?: string): Promise<PaymentDetailsWidget> {
        const billingInfo = this.offerHelper.getOrderBilling(order)
        const listingBrand: ListingBrandIdType = EatUtil.getListingBrandFromOrder(order)
        const offersApplied = !_.isEmpty(order.offersApplied) ? await this.offerServiceV3.getOffersByIds(order.offersApplied) : {}
        const priceDetails = this.getPriceDetailsForTransform(userContext, {
            ...billingInfo,
            orderDiscountsWithTitle: offersApplied
        }, _.map(offersApplied), listingBrand, order.deliveryCharges, order.packagingCharges, false, false, false)
        const paymentDetailWidget: TransformPaymentDetailsWidget = {
            widgetType: "TRANSFORM_PAYMENT_DETAILS_WIDGET",
            title: title,
            footer: footer,
            suffix: suffix,
            duration: duration,
            packHeader: packHeader,
            hexColor: hexColor,
            productType: productType,
            priceDetails: priceDetails.components,
            finalPrice: {
                listingPrice: _.round(billingInfo.total, 2),
                mrp: _.round(billingInfo.total, 2),
                currency: billingInfo.currency
            }
        }
        paymentDetailWidget.productType = productType
        return paymentDetailWidget
    }

    async getPlaySelectPaymentSummaryWidget(
        userContext: UserContext,
        order: Order,
        productType?: ProductType,
        title?: string,
        duration?: string,
        suffix?: string,
        footer?: string,
        packHeader?: string,
        hexColor?: string
    ): Promise<PaymentDetailsWidget> {
        const billingInfo = this.offerHelper.getOrderBilling(order)
        const listingBrand: ListingBrandIdType = EatUtil.getListingBrandFromOrder(order)
        const offersApplied = !_.isEmpty(order.offersApplied) ? await this.offerServiceV3.getOffersByIds(order.offersApplied) : {}
        const isPartOfGstSegment = await AppUtil.doesUserBelongToGSTsplitExperiment(userContext, this.serviceInterfaces)
        const priceDetails = this.getPriceDetails(userContext, {
            ...billingInfo,
            orderDiscountsWithTitle: offersApplied
        }, _.map(offersApplied), listingBrand, order.deliveryCharges, order.packagingCharges, false, false, false, undefined, undefined, isPartOfGstSegment, undefined, false, undefined, order)
        const paymentDetailWidget: PaymentDetailsWidget = {
            widgetType: "PAYMENT_DETAILS_WIDGET",
            title: title,
            footer: footer,
            suffix: suffix + "\n" + packHeader,
            duration: duration,
            packHeader: packHeader,
            hexColor: hexColor,
            productType: productType,
            priceDetails: priceDetails.components,
            finalPrice: {
                listingPrice: _.round(billingInfo.total, 2),
                mrp: _.round(billingInfo.total, 2),
                currency: billingInfo.currency
            },
            showCollapse : true,
            isCollapseOpen : false,
            topPadding: 20
        }
        paymentDetailWidget.productType = productType
        return paymentDetailWidget
    }

    async getPaymentSummaryWidget(
        userContext: UserContext,
        order: Order,
        productType?: ProductType,
        title?: string,
        duration?: string,
        suffix?: string,
        footer?: string,
        expiryDate?: string,
        topPaddingNeeded?: boolean,
        wrapInGradient?: boolean,
        color?: string,
         showCollapse?: boolean,
        isCollapseOpen?: boolean,
        boosterPack?: OfflineFitnessPack,
        addonProductIdsFromReqBody?: string[],
        productSubType?: ProductSubType,
        newPackNameData?: any
    ): Promise<PaymentDetailsWidget> {
        const billingInfo = this.offerHelper.getOrderBilling(order)
        const listingBrand: ListingBrandIdType = EatUtil.getListingBrandFromOrder(order)
        const offersApplied = !_.isEmpty(order.offersApplied) ? await this.offerServiceV3.getOffersByIds(order.offersApplied) : {}
        const isPartOfGstSegment = await AppUtil.doesUserBelongToGSTsplitExperiment(userContext, this.serviceInterfaces)
        const priceDetails = this.getPriceDetails(userContext, {
            ...billingInfo,
            orderDiscountsWithTitle: offersApplied,
        }, _.map(offersApplied), listingBrand, order.deliveryCharges, order.packagingCharges, false, false, false, undefined, undefined, isPartOfGstSegment, undefined, false, undefined, order, boosterPack)

        const showBoosterPackName = boosterPack && addonProductIdsFromReqBody?.length > 0
        let packName: PackName = newPackNameData
        if (productSubType && (productSubType === ProductSubType.ADDONBUNDLE || productSubType === ProductSubType.PLUS)) {
            packName = {
                title: {
                  text: title?.split(" ")[0],
                  color: "#FFFFFF",
                  typeScale: "H1",
                  richText: false
                },
                subtitle1: {
                  text: title?.split(" ")[1]?.toUpperCase(),
                  typeScale: "H1",
                  richText: false,
                  gradient: {
                    colors: productType === "FITNESS" ? ["#9648FF", "#EED3FF", "#6A4693"] : ["#FF9F9E", "#FFDADD", "#FFBDBC", "#FB857F"],
                    direction: "DIAGONAL",
                    gradientType: "LINEAR"
                  }
                },
                subtitle2: {
                  text: " " + (title?.split(" ")[2]?.toUpperCase() || "PLUS"),
                  typeScale: "P2",
                  richText: false,
                  fontStyle: "italic",
                  gradient: {
                      colors: productType === "FITNESS" ? ["#9648FF", "#EED3FF", "#6A4693"] : ["#FF9F9E", "#FFDADD", "#FFBDBC", "#FB857F"],
                      direction: "DIAGONAL",
                      gradientType: "LINEAR"
                  }
                }
              }
        } else if (showBoosterPackName) {
            packName = {
                title: {
                  text: title?.split(" ")[0],
                  color: "#FFFFFF",
                  typeScale: "H2",
                  richText: false
                },
                subtitle1: {
                  text: title?.split(" ")[1],
                  color: "#FFFFFF",
                  typeScale: "H2",
                  richText: false
                },
                subtitle2: {
                  text: " + Booster",
                  typeScale: "H2",
                  richText: false,
                  gradient: {
                    colors: productType === "FITNESS" ? ["#9648FF", "#EED3FF", "#6A4693"] : ["#FF9F9E", "#FFDADD", "#FFBDBC", "#FB857F"],
                    direction: "DIAGONAL",
                    gradientType: "LINEAR"
                  }
                }
              }
        }

        let paymentDetailWidget: PaymentDetailsWidget = {
            title: title,
            footer: footer,
            suffix: suffix,
            duration: duration,
            widgetType: "PAYMENT_DETAILS_WIDGET",
            priceDetails: priceDetails.components,
            finalPrice: {
                listingPrice: _.round(billingInfo.total, 2),
                mrp: _.round(billingInfo.total, 2),
                currency: billingInfo.currency
            },
            expiryDate: expiryDate,
            topPadding: topPaddingNeeded == true ? 60 : 0,
            wrapInGradient: wrapInGradient,
            packName: packName,
            showCollapse,
            isCollapseOpen
        }
        if (!_.isNil(color)) {
            paymentDetailWidget = {
                ...paymentDetailWidget,
                hexColor: color
            }
        }
        paymentDetailWidget.productType = productType
        if (userContext.sessionInfo.userAgent === "DESKTOP") {
            paymentDetailWidget.orientation = "RIGHT"
        }
        return paymentDetailWidget
    }

    async createPaymentAndOfferDetailWidget(
        userContext: UserContext,
        duration: string,
        timeUnit: string = "MONTHS",
        color: string,
        packName: string,
        productType: ProductType,
        productSubType?: ProductSubType,
        centerName?: string, // only in case of select, not preferred center for Elite
        order?: Order,
        isAllIndiaPack?: boolean
    ): Promise<any> {

        const billingInfo = this.offerHelper.getOrderBilling(order)
        const offersApplied: any = !_.isEmpty(order.offersApplied) ? await this.offerServiceV3.getOffersByIds(order.offersApplied) : {}
        const isPartOfGstSegment = await AppUtil.doesUserBelongToGSTsplitExperiment(userContext, this.serviceInterfaces)
        const priceDetails: { components: PriceComponent[], cartValue: number } = this.getPriceDetails(userContext, {...billingInfo, orderDiscountsWithTitle: offersApplied }, _.map(offersApplied),
            null, null, null, false, false, false, undefined,
            undefined, isPartOfGstSegment, undefined, false, undefined, order, null
        )

        const isPlus: boolean = (productSubType && (productSubType === ProductSubType.ADDONBUNDLE || productSubType === ProductSubType.PLUS))
        const isLitePack: boolean = (productSubType === ProductSubType.LITE)
        const packSuffix: string = isPlus ? "PLUS" : isLitePack ? "LITE" : null
        const packNameGradientColor = isPlus
                                    ? (productType === "FITNESS" ? ["#9648FF", "#EED3FF", "#6A4693"] : ["#FF9F9E", "#FFDADD", "#FFBDBC", "#FB857F"])
                                    : isLitePack ? (centerName ? ["#C7D2FF", "#3F64F2"] : (productType === "FITNESS" ? ["#8F9BC8", "#6D87E6"] : null))
                                    : (centerName ? null : (productType === "FITNESS" ? ["#FFC748", "#FFD579", "#FFD783", "#AA7620"] : ["#868686", "#EEEEEE", "#868686"]))

        let offers: Array<{ title: string, icon: string, tncAction?: Action }>
        if (!_.isNil(offersApplied) && !_.isNil(offersApplied?.data)) {
            offers = Object.keys(offersApplied?.data).map((offerId: string) => {
                if (!_.isNil(offersApplied?.data?.[offerId])) {
                    const { description, tNc, tNcUrl, addons } = offersApplied?.data?.[offerId]
                    if (!_.isNil(description) && description?.length > 0) {
                        return {
                            title: description,
                            icon: CultUtil.getOfferIconUrlFromOfferType(addons?.[0]?.addonType),
                            tncAction: _.isNil(tNc) || _.isNil(tNcUrl) ? null : {
                                actionType: "SHOW_OFFERS_TNC_MODAL",
                                meta: {
                                    title: "Offer Details",
                                    dataItems: tNc,
                                    url: tNcUrl
                                }
                            }
                        }
                    }
                }
            })
        }

        const offerList: any = []
        if (!_.isNil(offers)) {
            for (const offer of offers) {
                if (!_.isNil(offer)) {
                    offerList.push({
                        "offer": {
                            "text": offer.title,
                            "maxLine": 2,
                            "typeScale": "P4"
                        },
                        "prefixImage": {
                            "url": offer.icon,
                            "height": 16,
                        },
                        "tnc": _.isNil(offer.tncAction) ? null : {
                            "text": "T&C",
                            "typeScale": "P8",
                            "textDecoration": "underline",
                            "color": "#00BEFF",
                            "action": offer.tncAction
                        }
                    })
                }
            }
        }

        const priceDistributionList: any = []
        if (!_.isNil(priceDetails)) {
            for (const price of priceDetails.components) {
                priceDistributionList.push({
                    "text": {
                        "text": price.title,
                        "typeScale": "P5",
                        "opacity": 0.6,
                        "textDecoration": _.isNil(price.action) ? null : "underline",
                        "textDecorationStyle": _.isNil(price.action) ? null : "dashed",
                        "decorationThickness": _.isNil(price.action) ? null : 1,
                        "action": price.action
                    },
                    "price": {
                        "text": price?.isDiscount ? `-₹${addCommaInPriceINR(price.value)}` : `₹${addCommaInPriceINR(price.value)}`,
                        "typeScale": "P5",
                        "opacity": 0.6
                    }
                })
            }
            if (priceDistributionList?.length > 0) {
                priceDistributionList[priceDistributionList.length - 1]["isTotalPayable"] = true
                priceDistributionList[priceDistributionList.length - 1]["text"] = {...priceDistributionList[priceDistributionList.length - 1]["text"], "typeScale": "H2", "opacity" : null}
                priceDistributionList[priceDistributionList.length - 1]["price"] = {...priceDistributionList[priceDistributionList.length - 1]["price"], "typeScale": "H1", "opacity" : null, "text": priceDistributionList[priceDistributionList.length - 1]["price"]["text"]?.split(".")[0]}
            }
        }

        return {
            "widgetType": "PAYMENT_AND_OFFER_DETAIL_WIDGET",
            "centerName": _.isNil(centerName) ? null : {
                "text": centerName,
                "typeScale": "P4",
                "prefixIcon": {
                    "code": "location-small",
                    "size": 20,
                }
            },
            "header": {
                "duration": {
                    "text": `[H5,${color},${duration}]\n[P8,${color},${timeUnit}]`,
                    "isRichText": true,
                    "alignment": "center"
                },
                "pass": {
                    "text": isAllIndiaPack ? "All India" : "cultpass",
                    "typeScale": "P11"
                },
                "title": {
                    "text": packName,
                    "typeScale": "H9",
                    "gradient": _.isNil(packNameGradientColor) ? null : {
                        "colors": packNameGradientColor,
                        "direction": "DIAGONAL",
                        "gradientType": "LINEAR"
                    }
                },
                "suffix": _.isNil(packSuffix) ? null : {
                    "text": " " + packSuffix,
                    "typeScale": "P11",
                    "fontStyle": "italic",
                    "gradient": _.isNil(packNameGradientColor) ? null : {
                        "colors": packNameGradientColor,
                        "direction": "DIAGONAL",
                        "gradientType": "LINEAR"
                    }
                }
            },
            "offerString": {
                "text": "APPLIED OFFERS FOR TODAY",
                "color": "#0FE498",
                "typeScale": "P6",
                "prefixImage": {
                    "url": "/image/sku_plus_icons/tick_success.svg",
                    "height": 16
                }
            },
            "isCollapseOpen": false,
            "offerList": offerList,
            "priceDetailList": priceDistributionList
        }
    }

    async getChronicCarePaymentSummaryWidget(userContext: UserContext, order: Order, bundleProduct: DiagnosticProduct, productType?: ProductType): Promise<PaymentDetailsWidget> {
        const billingInfo = this.offerHelper.getOrderBilling(order)
        const showTaxes = billingInfo?.tax?.amount > 0
        const listingBrand: ListingBrandIdType = EatUtil.getListingBrandFromOrder(order)
        const offersApplied = !_.isEmpty(order.offersApplied) ? await this.offerServiceV3.getOffersByIds(order.offersApplied) : {}
        const priceDetails = this.getPriceDetails(userContext, {
            ...billingInfo,
            orderDiscountsWithTitle: offersApplied
        }, _.map(offersApplied), listingBrand, order.deliveryCharges, order.packagingCharges, false, false, false, undefined, undefined, showTaxes)
        const paymentDetailWidget: PaymentDetailsWidget = {
            widgetType: "PAYMENT_DETAILS_WIDGET",
            priceDetails: priceDetails.components,
            finalPrice: {
                listingPrice: _.round(billingInfo.total, 2),
                mrp: _.round(billingInfo.total, 2),
                currency: billingInfo.currency
            }
        }
        paymentDetailWidget.productType = productType
        paymentDetailWidget.fulfilledByText = "* Payments powered by cult.fit"
        if (userContext.sessionInfo.userAgent === "DESKTOP") {
            paymentDetailWidget.orientation = "RIGHT"
        }
        const timezone = userContext.userProfile.timezone
        const minStartDateOffsetInDays = _.get(bundleProduct, "productSpec.minStartDateDelayDays", 0)
        const minStartDateInMillis = _.get(bundleProduct, "productSpec.minStartDateMillis", TimeUtil.getDateNow(timezone).getTime())
        let minStartDate
        if (minStartDateInMillis < TimeUtil.getDateNow(timezone).getTime()) {
            minStartDate = TimeUtil.addToDate(TimeUtil.getDateNow(timezone), timezone, minStartDateOffsetInDays, "days")
        } else {
            minStartDate = TimeUtil.addToDate(TimeUtil.parseDateFromEpoch(minStartDateInMillis), timezone, minStartDateOffsetInDays, "days")
        }
        paymentDetailWidget.minStartDate = minStartDate.toDate().getTime()
        const maxDelayDays = _.get(bundleProduct, "productSpec.maxDelayStartDays", 0)
        if (maxDelayDays > 0) {
            paymentDetailWidget.maxStartDate = TimeUtil.addToDate(minStartDate.toDate(), timezone, maxDelayDays, "days").toDate().getTime()
        }
        return paymentDetailWidget
    }

    async getChronicCareCGMPaymentSummaryWidget(userContext: UserContext, order: Order, productType?: ProductType): Promise<PaymentDetailsWidget> {
        const billingInfo = this.offerHelper.getOrderBilling(order)
        const showTaxes = billingInfo?.tax?.amount > 0
        const listingBrand: ListingBrandIdType = EatUtil.getListingBrandFromOrder(order)
        const offersApplied = !_.isEmpty(order.offersApplied) ? await this.offerServiceV3.getOffersByIds(order.offersApplied) : {}
        const priceDetails = this.getPriceDetails(userContext, {
            ...billingInfo,
            orderDiscountsWithTitle: offersApplied
        }, _.map(offersApplied), listingBrand, order.deliveryCharges, order.packagingCharges, false, false, false, undefined, undefined, showTaxes)
        const paymentDetailWidget: PaymentDetailsWidget = {
            widgetType: "PAYMENT_DETAILS_WIDGET",
            priceDetails: priceDetails.components,
            finalPrice: {
                listingPrice: _.round(billingInfo.total, 2),
                mrp: _.round(billingInfo.total, 2),
                currency: billingInfo.currency
            }
        }
        paymentDetailWidget.productType = productType
        paymentDetailWidget.fulfilledByText = "* Payments powered by cult.fit"
        return paymentDetailWidget
    }

    async getChronicCareCreativeWidget(userContext: UserContext, productId?: string): Promise<ChronicCareCreativeWidget> {
        let creativeType
        if (productId === "SUGARFIT_TRIAL") {
            creativeType = "CHECKOUT_TRIAL"
        } else if (productId === "SUGARFIT_PD_PACK") {
            creativeType = "CHECKOUT_PREDIABETES"
        } else if (productId === "SUGARFIT_WELLNESS") {
            creativeType = productId
        } else {
            creativeType = "ANNUAL_PACK_PAGE"
        }
        const widget: ChronicCareCreativeWidget = {
            widgetType: "CHRONIC_CARE_CREATIVE_WIDGET" as any,
            aspectRatio: AppUtil.isSugarfitPackNewImagesSupported(userContext) ? 0.68 : 1.49,
            marginLeft: 30,
            marginRight: 70,
            creativeType,
            marginTop: 10,
            marginBottom: 20
        }
        return widget
    }

    async getHCUPaymentSummaryWidget(userContext: UserContext, order: Order, showHomeSampleCharges?: boolean): Promise<PaymentDetailsWidget> {
        const billingInfo = this.offerHelper.getOrderBilling(order)
        const listingBrand: ListingBrandIdType = EatUtil.getListingBrandFromOrder(order)
        const offersApplied = !_.isEmpty(order.offersApplied) ? await this.offerServiceV3.getOffersByIds(order.offersApplied) : {}
        const careHomeCollectionCharges: CareHomeCollectionCharges = {}
        if (order.collectionCharges?.total >= 0 && showHomeSampleCharges) {
            const careExtraChargeConfig = this.configService.getConfig("CARE_EXTRA_CHARGE_CONFIG")
            const collectionChargeLimit = _.get(careExtraChargeConfig, "configs.collectionChargeLimit")
            const collectionChargeAmount = _.get(careExtraChargeConfig, "configs.collectionCharge")
            careHomeCollectionCharges.showHomeSampleCharges = true
            careHomeCollectionCharges.homeSampleCharges = collectionChargeAmount
            careHomeCollectionCharges.homeSampleCartLimit = collectionChargeLimit
            careHomeCollectionCharges.orderHomeSampleCharges = order.collectionCharges?.total
        }
        const priceDetails = this.getPriceDetails(userContext, billingInfo, _.map(offersApplied), listingBrand, order.deliveryCharges, order.packagingCharges, false, false, false, undefined, undefined, undefined, undefined, undefined, careHomeCollectionCharges)
        const paymentDetailWidget: PaymentDetailsWidget = {
            widgetType: "PAYMENT_DETAILS_WIDGET",
            priceDetails: priceDetails.components,
            finalPrice: {
                listingPrice: _.round(billingInfo.total, 2),
                mrp: _.round(billingInfo.total, 2),
                currency: billingInfo.currency
            }
        }
        if (userContext.sessionInfo.userAgent === "DESKTOP") {
            paymentDetailWidget.orientation = "RIGHT"
        }
        return paymentDetailWidget
    }

    async getSubscriptionPaymentSummaryWidget(userContext: UserContext, order: Order): Promise<PaymentDetailsWidget> {
        const billingInfo = this.offerHelper.getOrderBilling(order)
        const offersApplied = !_.isEmpty(order.offersApplied) ? await this.offerServiceV3.getOffersByIds(order.offersApplied) : {}
        const listingBrand: ListingBrandIdType = EatUtil.getListingBrandFromOrder(order)
        const priceDetails = this.getPriceDetails(userContext, {
            ...billingInfo,
            orderDiscountsWithTitle: offersApplied
        }, _.map(offersApplied), listingBrand, order.deliveryCharges, order.packagingCharges, false, false, true, order.adjustedAmount, order.penaltyAmount)
        const paymentDetailWidget: PaymentDetailsWidget = {
            widgetType: "PAYMENT_DETAILS_WIDGET",
            priceDetails: priceDetails.components,
            finalPrice: {
                listingPrice: _.round(billingInfo.total, 2),
                mrp: _.round(billingInfo.total, 2),
                currency: billingInfo.currency
            }
        }
        return paymentDetailWidget
    }

    async getCultPayByMembershipWidget(userContext: UserContext, order: Order, payByMembershipDetails: PayByMembershipDetails, membershipPaymentType: MembershipPaymentType): Promise<HighlightedToggleSelectionWidget> {
        const cultMembershipPaymentOptions = order.products[0].option.cultMemberShipPaymentOptions
        const isSelected = cultMembershipPaymentOptions && membershipPaymentType === cultMembershipPaymentOptions.membershipPaymentType && cultMembershipPaymentOptions.useCultMemberShipForPayment
        const isEnabled = membershipPaymentType === MembershipPaymentType.CULT ? payByMembershipDetails.cult.isEligible : payByMembershipDetails.mind.isEligible
        const numDaysToDeduct = membershipPaymentType === MembershipPaymentType.CULT ? payByMembershipDetails.cult.numDaysToDeduct : payByMembershipDetails.mind.numDaysToDeduct
        const subTitle = isEnabled ? `*Note: As a member of ${membershipPaymentType === MembershipPaymentType.CULT ? "Cult" : "Mind"}, you can choose to swap ${numDaysToDeduct} days from your currently active pack, to avail this pack, at no additional cost.` : `*Note: You have less than ${numDaysToDeduct} days remaining in your currently active Pack, hence swap option is disabled.`
        const widget: HighlightedToggleSelectionWidget = {
            widgetType: "HIGHLIGHTED_TOGGLE_SELECTION_WIDGET",
            title: `Swap ${numDaysToDeduct} days from your ${membershipPaymentType === MembershipPaymentType.CULT ? "Cult" : "Mind"} Membership`,
            subTitle: subTitle,
            action: {
                actionType: "CARE_CHANGE_ORDER_PRODUCT_OPTION",
                meta: {
                    productId: order.products[0].productId,
                    optionMeta: {
                        cultMemberShipPaymentOptions: {
                            ...cultMembershipPaymentOptions,
                            useCultMemberShipForPayment: !isSelected,
                            membershipPaymentType: membershipPaymentType
                        }
                    }
                }
            },
            bgColor: isEnabled ? "#f4e7ff" : "#e7e7e7",
            selected: isSelected,
            enabled: isEnabled,
            analyticsData: {
                productId: order.products[0].productId,
                optionEnabled: isEnabled,
                optionSelected: isSelected
            },
            showDivider: false
        }
        if (AppUtil.isWeb(userContext)) {
            widget.orientation = "RIGHT"
            widget.productType = "LIVE_PERSONAL_TRAINING"
        }
        return widget
    }

    async getManagedPlanPaymentSummaryWidget(userContext: UserContext, order: Order): Promise<ManagedPlanPaymentWidget> {
        const billingInfo = this.offerHelper.getOrderBilling(order)
        const managedPlanPaymentWidget: ManagedPlanPaymentWidget = {
            widgetType: "MANAGED_PLAN_PAYMENT_WIDGET",
            header: {
                title: "Total"
            },
            footer: {
                title: "Total Payable",
                price: {
                    listingPrice: _.round(billingInfo.total, 2),
                    currency: billingInfo.currency
                }
            },
            items: await this.getManagedPlanPriceBreakup(userContext, order, billingInfo)
        }
        return managedPlanPaymentWidget
    }

    async getManagedPlanPriceBreakup(userContext: UserContext, order: Order, billingInfo: BillingInfo): Promise<PriceValue[]> {
        const prices: PriceValue[] = []
        const offers = await CareWidgetUtil.getCareProductOffersFromUserContext(
            userContext,
            "BUNDLE",
            order.products.filter(product => product.productType !== "DEVICE").map(product => product.productId),
            AppUtil.callSourceFromContext(userContext),
            this.serviceInterfaces,
            true
        )
        const deviceProducts = order.productSnapshots.filter(product => product.productType === "DEVICE")
        let deviceOffers: PackOffersResponse
        let mendatoryDevices: OrderProductSnapshots[] = []
        let addOnDevices: OrderProductSnapshots[] = []
        if (!_.isEmpty(deviceProducts)) {
            deviceOffers = await CareWidgetUtil.getCareProductOffersFromUserContext(
                userContext,
                "DEVICE",
                deviceProducts.map(product => product.productId),
                AppUtil.callSourceFromContext(userContext),
                this.serviceInterfaces,
                true
            )
            mendatoryDevices = deviceProducts.filter(product => product.option.childProductType === "CHILD_MANDATORY")
            addOnDevices = deviceProducts.filter(product => product.option.childProductType === "CHILD_ADD_ON")
        }
        const recurringProduct = order.productSnapshots.find(product => product.option.subCategoryCode === "MP_SUBS")
        if (!_.isEmpty(recurringProduct)) {
            prices.push({
                title: recurringProduct.title || "Recurring Charges",
                price: {
                    mrp: _.round(recurringProduct.price.mrp, 2),
                    listingPrice: _.round(OfferUtil.getPackOfferAndPrice(recurringProduct, offers).price.listingPrice, 2),
                    currency: billingInfo.currency
                }
            })
        }
        const otTest = order.productSnapshots.find(product => product.option.subCategoryCode === "MP_OT")
        if (!_.isEmpty(otTest)) {
            prices.push({
                title: "One time Onboarding Charges",
                price: {
                    mrp: _.round(otTest.price.mrp + _.sum(mendatoryDevices.map(device => device.price.mrp)), 2),
                    listingPrice: _.round(OfferUtil.getPackOfferAndPrice(otTest, offers).price.listingPrice +
                        _.sum(mendatoryDevices.map(device => OfferUtil.getPackOfferAndPrice(device, deviceOffers).price.listingPrice)), 2),
                    currency: billingInfo.currency
                },
                items: [
                    {
                        title: "Onboarding Test",
                        price: {
                            mrp: _.round(otTest.price.mrp, 2),
                            listingPrice: _.round(OfferUtil.getPackOfferAndPrice(otTest, offers).price.listingPrice, 2),
                            currency: billingInfo.currency
                        }
                    }, ...(mendatoryDevices.map(device => {
                        return {
                            title: device.title,
                            price: {
                                mrp: _.round(device.price.listingPrice, 2),
                                listingPrice: _.round(OfferUtil.getPackOfferAndPrice(device, deviceOffers).price.listingPrice, 2),
                                currency: billingInfo.currency
                            }
                        }
                    }))
                ]
            })
        }
        if (!_.isEmpty(addOnDevices)) {
            prices.push({
                title: "Addon Devices",
                price: {
                    mrp: _.round(_.sum(addOnDevices.map(device => device.price.mrp)), 2),
                    listingPrice: _.round(_.sum(addOnDevices.map(device => OfferUtil.getPackOfferAndPrice(device, deviceOffers).price.listingPrice)), 2),
                    currency: billingInfo.currency
                },
                items: [
                    ...(addOnDevices.map(device => {
                        return {
                            title: device.title,
                            price: {
                                mrp: _.round(device.price.mrp, 2),
                                listingPrice: _.round(OfferUtil.getPackOfferAndPrice(device, deviceOffers).price.listingPrice, 2),
                                currency: billingInfo.currency
                            }
                        }
                    }))
                ]
            })
        }
        if (billingInfo.tax.amount > 0) {
            prices.push({
                title: "Tax",
                price: {
                    listingPrice: _.round(billingInfo.tax.amount, 2),
                    currency: billingInfo.currency
                }
            })
        }

        return prices
    }

    async getCareCartAddonsWidget(userContext: UserContext, userAgent: UserAgent, order: Order, baseProduct: DiagnosticProduct, userId: string, deviceId: string): Promise<CareAddonListWidget> {
        const productInfo: HealthfaceProductInfo = (await this.healthfaceService.getProductInfoDetailsCached("BUNDLE", baseProduct.subCategoryCode, baseProduct.productId, CareUtil.getHealthfaceTenant(baseProduct.subCategoryCode)))[0]
        const productIds = _.uniq(_.map(order.products, product => {
            return product.productId
        }))
        const cityCode: string = userContext.userProfile.cityId
        let offers, noteText
        if (baseProduct.subCategoryCode === "HCU" || baseProduct.subCategoryCode === "HCU_PACK" || baseProduct.subCategoryCode === "GENETICS_PACK" || baseProduct.subCategoryCode === "DIAG_PACK") {
            let testAddOnProducts: DiagnosticTestProduct[]
            if (baseProduct.subCategoryCode === "HCU") {
                testAddOnProducts = (<BundleSellableProduct>productInfo.baseSellableProduct).packageProduct.addOnProducts
            } else if (baseProduct.subCategoryCode === "HCU_PACK" || baseProduct.subCategoryCode === "GENETICS_PACK" || baseProduct.subCategoryCode === "DIAG_PACK") {
                // OT
                const otProducts = (<ManagedPlanSellableProduct>productInfo.baseSellableProduct).childProducts.filter(product => product.baseSellableProduct.subCategoryCode === "DIAG_PACK_OT" || product.baseSellableProduct.subCategoryCode === "HCU_PACK_OT" || product.baseSellableProduct.subCategoryCode === "GENETICS_PACK_OT")
                testAddOnProducts = otProducts[0].baseSellableProduct.packageProduct.addOnProducts
            }
            if (_.isEmpty(testAddOnProducts)) {
                return undefined
            }
            offers = await CareWidgetUtil.getCareProductOffersFromUserContext(
                userContext,
                "DIAGNOSTICS",
                testAddOnProducts.map(product => product.code),
                AppUtil.callSourceFromContext(userContext),
                this.serviceInterfaces,
                true
            )
            const productArray: DiagnosticProduct[] = []

            for (let i = 0; i < testAddOnProducts.length; i++) {
                const addon = testAddOnProducts[i]
                const product = clone(await this.catalogueService.getProduct(addon.code))
                productArray.push(<DiagnosticProduct>product)
            }
            const addonItems: CareAddonItem[] = []
            const sampleCollectionLocationResponse = await this.healthfaceService.getDiagnosticTestAllowedActionFromCollectionCodes(productIds, undefined, cityCode)
            const diagnosticsTestAllowedLocation = sampleCollectionLocationResponse.sampleCollectionLocations
            noteText = (diagnosticsTestAllowedLocation && diagnosticsTestAllowedLocation.length === 1 && diagnosticsTestAllowedLocation[0] === "ALL_AT_CENTRE") ? "Home Sample Collection is not available for the selected tests" : undefined
            for (let i = 0; i < productArray.length; i++) {
                const product = productArray[i]
                const offerDetails = OfferUtil.getPackOfferAndPrice(product, offers)
                const offerIds = _.map(offerDetails.offers, offer => {
                    return offer.offerId
                })

                const itemAdded = productIds.indexOf(product.productId) != -1
                const action: Action = itemAdded ? {
                    actionType: "CARE_REMOVE_FROM_CART",
                    meta: {
                        productId: product.productId
                    }
                } : {
                    actionType: "CARE_ADD_TO_CART",
                    meta: {
                        productId: product.productId
                    }
                }
                const addonItem: CareAddonItem = {
                    image: product.imageUrl,
                    title: product.title,
                    subTitle: product.testCount > 0 ? `${product.testCount} tests` : undefined,
                    productId: product.productId,
                    price: offerDetails.price,
                    offerIds: offerIds,
                    added: itemAdded,
                    action: action,
                }
                addonItems.push(addonItem)
            }
            const widget: CareAddonListWidget = {
                widgetType: "DIAGNOSTICS_ADDON_LIST_WIDGET",
                title: "Select Add-on Tests",
                addonItems: addonItems,
                noteText: noteText,
                showSelectAll: false,
            }
            if (userAgent === "DESKTOP") {
                widget.orientation = "LEFT"
            }
            return widget
        } else if (baseProduct.subCategoryCode === "MP") {
            const deviceAddOnProducts: MPChildProduct[] = (<ManagedPlanSellableProduct>productInfo.baseSellableProduct).childProducts.filter(product => product.baseSellableProduct.categoryCode === "DEVICE" && product.baseSellableProduct.subCategoryCode !== "INT_WELCOME_KIT" && (product.childProductType === "CHILD_ADD_ON" || product.childProductType === "CHILD_MANDATORY"))
            offers = await CareWidgetUtil.getCareProductOffersFromUserContext(
                userContext,
                "DEVICE",
                deviceAddOnProducts.map(product => product.baseSellableProduct.productCode),
                AppUtil.callSourceFromContext(userContext),
                this.serviceInterfaces,
                true
            )
            const addonItems: CareAddonItem[] = []

            for (let i = 0; i < deviceAddOnProducts.length; i++) {
                const addon = deviceAddOnProducts[i]
                const product = <DiagnosticProduct>clone(await this.catalogueService.getProduct(addon.baseSellableProduct.productCode))
                const offerDetails = OfferUtil.getPackOfferAndPrice(product, offers)
                const offerIds = _.map(offerDetails.offers, offer => {
                    return offer.offerId
                })

                const itemAdded = productIds.indexOf(product.productId) != -1
                const action: Action = itemAdded ? {
                    actionType: "CARE_REMOVE_FROM_CART",
                    meta: {
                        productId: product.productId
                    }
                } : {
                    actionType: "CARE_ADD_TO_CART",
                    meta: {
                        parentProductCode: productInfo.baseSellableProduct.productCode,
                        categoryCode: addon.baseSellableProduct.categoryCode,
                        subCategoryCode: addon.baseSellableProduct.subCategoryCode,
                        groupCode: addon.groupCode,
                        childProductType: addon.childProductType,
                        productId: product.productId
                    }
                }
                const addonItem: CareAddonItem = {
                    image: product.imageUrl,
                    title: product.title,
                    includedInPack: addon.childProductType === "CHILD_MANDATORY",
                    subTitle: product.testCount > 0 ? `${product.testCount} tests` : undefined,
                    productId: product.productId,
                    price: offerDetails.price,
                    offerIds: offerIds,
                    added: itemAdded,
                    action: action,
                }
                addonItems.push(addonItem)
            }
            return {
                widgetType: "DEVICE_ADDON_WIDGET",
                title: "Recommended Devices",
                addonItems: addonItems
            }
        } else {
            this.logger.error(`${baseProduct.subCategoryCode} subCategoryCode is not supported`)
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage(`${baseProduct.subCategoryCode} subCategoryCode is not supported`).build()
        }

    }

    private async getFoodMarketplaceSummaryWidget(order: Order, userContext: UserContext, foodMarketplaceBooking: UpcomingOrderDetailsResponse, date: string): Promise<CartSummaryWidget> {

        const issuesMap = await this.CRMIssueService.getIssuesMap()
        const result = await this.productBusiness.getFoodMarketplaceManageOptions(userContext, foodMarketplaceBooking, issuesMap)
        const subTitle = this.getFoodMarketplaceSummaryWidgetTitle(foodMarketplaceBooking.status)

        return {
            widgetType: "CART_SUMMARY_WIDGET",
            manageOptions: result.manageOptions,
            meta: result.meta,
            title: "Your Food order",
            subTitle
        }

    }

    private getFoodMarketplaceSummaryWidgetTitle(status: ShipmentStatus): string {
        if (FoodMarketplaceUtil.isFoodMarketplaceOrderCancelled(status)) {
            return "Order Cancelled"
        } else if (FoodMarketplaceUtil.isFoodMarketplaceOrderDelivered(status)) {
            return "Order Delivered!"
        }
        return `Arriving Soon`
    }

    private async getFoodMarketplaceAddressOption(order: Order): Promise<WidgetView> {

        const {userAddress} = order
        const {addressType, addressLine1 = "", addressLine2 = ""} = userAddress
        const addressText = addressLine1 + addressLine2

        const addressOptions: ManageOptions = {
            displayText: addressType + ": " + addressText,
            subHelper: addressText,
            options: [],
            icon: "LOCATION"
        }

        return Promise.resolve(new ManageOptionsWidget(addressOptions, undefined, true))
    }

    private async getFoodMarketplaceFeedbackWidget(feedback: Feedback, feedbackPageConfigV2Cache?: FeedbackPageConfigV2Cache): Promise<WidgetView> {
        if (feedback && feedbackPageConfigV2Cache) {
            return Promise.resolve(new ProductFeedbackWidget(await feedbackPageConfigV2Cache.getQuestionV2(feedback), feedback.feedbackId))
        }
        return undefined
    }

    async getFoodMarketplaceOrderTrackingWidget(foodMarketplaceBooking: UpcomingOrderDetailsResponse, userContext: UserContext): Promise<WidgetView> {

        const {
            trackUrl,
            cfOrderId,
            foodOrderId,
            status,
            statusHistory,
            dropEta,
            riderContactNumber,
            riderName
        } = foodMarketplaceBooking
        const orderTrackingWidget = new OrderTrackingStatusWidget()
        orderTrackingWidget["setTimer"] = true // for polling the requests
        orderTrackingWidget.foodOrderId = foodOrderId
        orderTrackingWidget.actions = []

        if (status !== "DELIVERED" && !_.isEmpty(trackUrl)) {
            const trackingAction = AppUtil.isFoodMarketplaceInAppTrackingSupported(userContext) ?
                FoodMarketplaceUtil.generateInAppOrderTrackingAction(trackUrl)
                : FoodMarketplaceUtil.generateBrowserRedirectActionForOrderTracking(trackUrl)

            if (AppUtil.isNewOrderTrackingWidgetSupported(userContext)) {
                orderTrackingWidget.actions.push(trackingAction)
            } else {
                orderTrackingWidget.action = trackingAction
            }
        }
        if (riderContactNumber) {
            orderTrackingWidget.actions.push(FoodMarketplaceUtil.getCallPartnerAction(riderContactNumber))
        }

        const mappedOrderStatus = [FoodMarketplaceUtil.mapGandalfStatusToFrontendStatus(status)]
        const expectedDeliveryTime = FoodMarketplaceUtil.createDropEtaString(dropEta)


        orderTrackingWidget.values = [
            {
                status: FoodMarketplaceFESupportedStatus.PLACED as any,
                active: mappedOrderStatus.includes(FoodMarketplaceFESupportedStatus.PLACED),
                title: FoodMarketplaceUtil.getTextCorrespondingToStatus(FoodMarketplaceFESupportedStatus.PLACED),
                expectedDeliveryTime
            },
            {
                status: FoodMarketplaceFESupportedStatus.PREPARING,
                active: mappedOrderStatus.includes(FoodMarketplaceFESupportedStatus.PREPARING),
                title: FoodMarketplaceUtil.getTextCorrespondingToStatus(FoodMarketplaceFESupportedStatus.PREPARING),
                expectedDeliveryTime
            },
            {
                status: FoodMarketplaceFESupportedStatus.RIDER_ASSIGNED as any,
                active: mappedOrderStatus.includes(FoodMarketplaceFESupportedStatus.RIDER_ASSIGNED),
                title: FoodMarketplaceUtil.getTextCorrespondingToStatus(FoodMarketplaceFESupportedStatus.RIDER_ASSIGNED),
                expectedDeliveryTime
            },
            {
                status: FoodMarketplaceFESupportedStatus.RIDER_ARRIVED_AT_RESTAURANT as any,
                active: mappedOrderStatus.includes(FoodMarketplaceFESupportedStatus.RIDER_ARRIVED_AT_RESTAURANT),
                title: FoodMarketplaceUtil.getTextCorrespondingToStatus(FoodMarketplaceFESupportedStatus.RIDER_ARRIVED_AT_RESTAURANT),
                expectedDeliveryTime
            },
            {
                status: FoodMarketplaceFESupportedStatus.ON_ITS_WAY,
                active: mappedOrderStatus.includes(FoodMarketplaceFESupportedStatus.ON_ITS_WAY),
                title: FoodMarketplaceUtil.getTextCorrespondingToStatus(FoodMarketplaceFESupportedStatus.ON_ITS_WAY),
                expectedDeliveryTime
            },
            {
                status: mappedOrderStatus.includes(FoodMarketplaceFESupportedStatus.REJECTED) ? "REJECTED" : "DELIVERED",
                active: mappedOrderStatus.includes(FoodMarketplaceFESupportedStatus.DELIVERED) || mappedOrderStatus.includes(FoodMarketplaceFESupportedStatus.REJECTED),
                title: FoodMarketplaceUtil.getTextCorrespondingToStatus(mappedOrderStatus.includes(FoodMarketplaceFESupportedStatus.REJECTED) ? FoodMarketplaceFESupportedStatus.REJECTED : FoodMarketplaceFESupportedStatus.DELIVERED)
            }
        ]
        return orderTrackingWidget
    }

    private async getFoodMarketplaceCartDetailWidget(order: Order, foodMarketplaceBooking: UpcomingOrderDetailsResponse, userContext: UserContext, date: string): Promise<CartDetailListWidget> {
        const {productSnapshots: orderProductSnapShots, products, payments, productTypeOptions} = order
        const {productWiseInfo, listingBrand} = productTypeOptions
        const productWiseInfoMap = _.keyBy(productWiseInfo, p => FoodMarketplaceUtil.generateProductAddonVariantComboKey(p.productId, {
            addons: p?.addons,
            variants: p?.variants
        }))

        const currency = payments[0].currency

        let numItems = 0
        const orderItemsPromises: Promise<OrderItemDetail>[] = _.map(orderProductSnapShots, async orderProductSnapShot => {
            const {quantity, productId} = orderProductSnapShot
            const product = await this.catalogueService.getProduct(productId)
            const {foodType, title, imageUrl} = product
            numItems = numItems + quantity
            const {option} = orderProductSnapShot
            const key = FoodMarketplaceUtil.generateProductAddonVariantComboKey(productId, option)
            const priceInfo = productWiseInfoMap[key]
            const mrp = orderProductSnapShot.price.mrp

            const orderItem: OrderItemDetail = {
                productId: productId,
                price: {
                    mrp: Math.round(mrp),
                    listingPrice: Math.round(priceInfo.priceWithoutTax),
                    currency: currency
                },
                isVeg: foodType === "Veg",
                calories: undefined,
                productName: title,
                productNameWithoutUnits: title,
                quantity: quantity,
                servingUnit: undefined,
                servingQty: undefined,
                displayUnitQty: undefined,
                variantTitle: FoodMarketplaceUtil.getVariantTitle(orderProductSnapShot.option),
                image: imageUrl || FoodMarketplaceUtil.getDummyMealCardImageUrl(),
                subTitle: FoodMarketplaceUtil.getVariantTitle(orderProductSnapShot.option),
                additionalSubtitle: FoodMarketplaceUtil.getAddonTitle(orderProductSnapShot.option)
            }

            orderItem["action"] = undefined // since no pdp
            return orderItem
        })
        const orderItems: OrderItemDetail[] = await Promise.all(orderItemsPromises)
        const widget: CartDetailListWidget = {
            widgetType: "CART_DETAIL_LIST_WIDGET",
            orderItems: orderItems,
            title: numItems === 1 ? `${numItems} item ordered` : `${numItems} items ordered`
        }
        return widget
    }

    async buildFoodMarketplaceCartDetailView(order: Order, userContext: UserContext, foodMarketplaceBooking: UpcomingOrderDetailsResponse, date: string, feedback: Feedback, feedbackPageConfigV2Cache?: FeedbackPageConfigV2Cache): Promise<ProductDetailPage> {

        const widgetPromises: Promise<WidgetView | BaseWidget>[] = []

        widgetPromises.push(this.getFoodMarketplaceSummaryWidget(order, userContext, foodMarketplaceBooking, date))
        widgetPromises.push(this.getFoodMarketplaceAddressOption(order))
        widgetPromises.push(this.getFoodMarketplaceFeedbackWidget(feedback, feedbackPageConfigV2Cache))
        widgetPromises.push(this.getFoodMarketplaceOrderTrackingWidget(foodMarketplaceBooking, userContext))
        widgetPromises.push(this.getFoodMarketplaceCartDetailWidget(order, foodMarketplaceBooking, userContext, date))


        const widgets = _.compact(await Promise.all(widgetPromises))
        const pageActions: Action[] = [{
            title: "Done",
            actionType: "POP_ACTION"
        }]

        return {widgets: widgets, pageActions: pageActions, actions: pageActions, alertInfo: undefined}

    }

    async buildCartDetailView(foodBooking: FoodBooking, order: BaseOrder, userContext: UserContext, alertInfo?: AlertInfo, feedback?: Feedback, feedbackPageConfigV2Cache?: FeedbackPageConfigV2Cache): Promise<ProductDetailPage> {
        const widgetPromises: Promise<WidgetView | BaseWidget>[] = []
        const tz = foodBooking.timezone
        const isCafe: boolean = !_.isNil(foodBooking.address) && foodBooking.address.kioskType === "CAFE"
        let isFitClubSupported = await FitclubBusiness.isFitClubSupported(userContext)
        isFitClubSupported = isFitClubSupported && !isCafe
        const onDemand: boolean = foodBooking.deliverySlot && foodBooking.deliverySlot.slotId === SlotUtil.ON_DEMAND_SLOT
        const issuesMap = await this.CRMIssueService.getIssuesMap()
        const date = TimeUtil.getMomentForDate(foodBooking.deliveryWindow.start, tz)
        const hourMin: HourMin = {
            hour: date.hours(),
            min: date.minutes()
        }
        const isAddChangeMealSupportedForBrand = EatUtil.isAddChangeMealSupportedForBrand(foodBooking?.listingBrand)
        if (foodBooking?.listingBrand === "DEALS" || foodBooking.listingBrand === "CUREFOODS") {
            foodBooking.listingBrand = "EAT_FIT"
        }
        // let pageTitle: string
        let foodPackBooking: FoodPackBooking
        if (foodBooking.packId) {
            foodPackBooking = (await this.shipmentService.getFoodShipmentsByFulfilmentId(foodBooking.fulfilmentId)).booking
        }
        const isCancelCutOffPassed: boolean = SlotUtil.isCancelCutOffPassed(foodBooking.deliveryDate, tz, foodBooking.deliverySlot ? foodBooking.deliverySlot.slotId : undefined, foodBooking.deliveryWindow)
        let foodPack: FoodPack
        if (foodBooking.packId)
            foodPack = await this.catalogueService.getFoodPack(foodBooking.packId)
        let mealSlot: MenuType = foodBooking.deliverySlot ? SlotUtil.getMealSlotForSlotId(foodBooking.deliverySlot.slotId, tz) : SlotUtil.getClosestMealSlot(hourMin)
        if (foodBooking.listingBrand === "WHOLE_FIT") {
            mealSlot = "ALL"
        }
        const isAddChangeMealNotSupportedTagPresent: boolean = !_.isEmpty(foodPack) && foodPack.tags?.indexOf(EatSubscriptionUtil.ADD_CHANGE_MEAL_NOT_SUPPORTED_TAG) > -1
        if (EatSubscriptionUtil.isSubscriptionAttachPurchased(userContext, foodPackBooking)) {
            // For new versions of app with subscription addon pack purchased along
            widgetPromises.push(this.getCartDetailListWidget(order, foodBooking, userContext))
            if (!isCancelCutOffPassed && foodBooking.canBeCancelled) {
                const manageOptions = await this.getManageOptions(foodBooking, true, true, false, tz)
                const queryParams: any = {}
                queryParams["cancelOptions"] = manageOptions
                if (foodBooking.changeMealAllowed) {
                    queryParams["changeMealOptions"] = {
                        "fulfilmentId": foodBooking.fulfilmentId,
                        "mealSlot": mealSlot,
                        "date": foodBooking.deliveryDate,
                        "url": `curefit://addchangemealv2?fulfilmentId=${foodBooking.fulfilmentId}&date=${foodBooking.deliveryDate}&slot=${mealSlot}`
                    }
                }
                widgetPromises.push(new AddChangeCancelDailyMealWidget().buildView(null, userContext, queryParams))
            } else if (foodBooking.state === "CANCELLED") { // adding the undo cancel meal widget if meal is already cancelled
                widgetPromises.push(this.getCancelMealWidget(foodBooking, true, true, false, tz))
            }
        }

        if (!AppUtil.isSubscriptionAttachSupported(userContext) || EatSubscriptionUtil.isNotSubscriptionPack(userContext, foodPackBooking) || !EatSubscriptionUtil.isSubscriptionAttachPurchased(userContext, foodPackBooking)) {
            // 1. for old version
            // 2. new version non subscripton meals
            // 3. new version subscription without addon meals
            if (feedback && (["NOT_RATED", "DISMISSED"].includes(feedback.rating))) {
                widgetPromises.push(this.getCartSummaryWidget(userContext, foodBooking, issuesMap, true))
                widgetPromises.push(Promise.resolve(new ProductFeedbackWidget(await feedbackPageConfigV2Cache.getQuestionV2(feedback), feedback.feedbackId)))
            } else if (foodBooking && foodBooking.state) {
                const orderTackingWidget = new OrderTrackingStatusWidget(foodBooking, mealSlot, userContext)
                if (orderTackingWidget && orderTackingWidget.values && orderTackingWidget.values.length > 0) {
                    widgetPromises.push(this.getCartSummaryWidget(userContext, foodBooking, issuesMap, false))
                    widgetPromises.push(Promise.resolve(orderTackingWidget))
                } else {
                    widgetPromises.push(this.getCartSummaryWidget(userContext, foodBooking, issuesMap, true))
                }
            } else {
                widgetPromises.push(this.getCartSummaryWidget(userContext, foodBooking, issuesMap, true))
            }
        }
        if (foodBooking.state !== "DELIVERED" && foodBooking.state !== "CANCELLED" && foodBooking.address.addressType === "KIOSK") {
            this.logger.info("UserId: " + `${userContext.userProfile.userId},` + "FoodBooking state: " + `${foodBooking.state}` + " for passcode widget")
            widgetPromises.push(EatUtil.getPasscodeWidget(foodBooking))
        }
        const mealManageOptions = await this.productBusiness.getMealManageOptions(userContext, foodBooking, issuesMap, false, true, foodBooking.address.kioskType !== "CAFE", false, onDemand)
        const enabledOptions = mealManageOptions.manageOptions.options.filter((option: ManageOptionPayload) => {
            return option.isEnabled
        })
        let addressText = foodBooking.address.addressLine1 ? foodBooking.address.addressLine1 + ", " : ""
        addressText = addressText + foodBooking.address.addressLine2
        const addressOptions: ManageOptions = {
            displayText: foodBooking.address.addressType + ": " + addressText,
            subHelper: addressText,
            options: [],
            icon: "LOCATION"
        }
        const isFitclubMember = await this.fitclubBusiness.isFitclubMember(userContext.userProfile.userId)
        const cashback = await this.offerServiceV2.speculateCashbackFromCart(order)
        const fitClubFitCash = cashback.isEligible ? cashback.amount : 0
        const deliveryCharge: number = !_.isNil(order.deliveryCharges) ? order.deliveryCharges.total : 0
        if ((isFitclubMember || isFitClubSupported) && !foodBooking.packId && fitClubFitCash && !OrderUtil.isSubOrderId(foodBooking.orderId) && foodBooking.listingBrand !== "WHOLE_FIT") {
            if (AppUtil.isFitClubAllowed(userContext, isFitclubMember) && fitClubFitCash > 0 && !isCafe) {
                widgetPromises.push(Promise.resolve(new FitclubSavingsCalloutWidgetV2(userContext, fitClubFitCash, deliveryCharge, "cart checkout page", true, true)))
            }
        }

        const rescheduleWidget = Promise.resolve(new ManageOptionsWidget(mealManageOptions.manageOptions, mealManageOptions.meta, enabledOptions.length === 0))
        if (foodBooking.packId) { // for subscriptions
            if (!AppUtil.isSubscriptionAttachSupported(userContext) || EatSubscriptionUtil.shouldWidgetBeIncludedForNoAttachSubscription(userContext, foodPackBooking)) {
                // 1. For old versions
                // 2. For new versions with subscription and without addon pack purchased
                if (!isAddChangeMealNotSupportedTagPresent && foodBooking.listingBrand !== "WHOLE_FIT") {
                    widgetPromises.push(Promise.resolve(this.getChangeMealWidget(userContext, mealSlot, foodBooking, isCancelCutOffPassed, true)))
                }
                widgetPromises.push(this.getCancelMealWidget(foodBooking, true, true, false, tz))
            }
            widgetPromises.push(rescheduleWidget)
            widgetPromises.push(Promise.resolve(this.getChangeAddressWidget(userContext, foodBooking, isCancelCutOffPassed, true)))
            if (foodBooking.address.addressType !== "KIOSK")
                widgetPromises.push(Promise.resolve(this.getChangeInstructionWidget(foodBooking, isCancelCutOffPassed, true, userContext)))
        } else {
            if (foodBooking.deliveryType !== "ON_DEMAND") {
                if (isAddChangeMealSupportedForBrand && !isAddChangeMealNotSupportedTagPresent && foodBooking.address && foodBooking.address.kioskType !== "CAFE" && foodBooking.listingBrand !== "WHOLE_FIT") {
                    widgetPromises.push(Promise.resolve(this.getChangeMealWidget(userContext, mealSlot, foodBooking, isCancelCutOffPassed, true)))
                }
                widgetPromises.push(this.getCancelMealWidget(foodBooking, false, true, false, tz))
                widgetPromises.push(rescheduleWidget)
            }
            widgetPromises.push(Promise.resolve(new ManageOptionsWidget(addressOptions, undefined, true)))
        }
        if (!AppUtil.isSubscriptionAttachSupported(userContext) || EatSubscriptionUtil.isNotSubscriptionPack(userContext, foodPackBooking) || EatSubscriptionUtil.shouldWidgetBeIncludedForNoAttachSubscription(userContext, foodPackBooking)) {
            // 1. for old version
            // 2. new version non subscripton meals
            // 3. new version subscription without addon along meals
            widgetPromises.push(this.getCartDetailListWidget(order, foodBooking, userContext))

        }
        if (foodBooking.packId)
            widgetPromises.push(Promise.resolve(this.getPackInfoWidget(foodPack, foodBooking, userContext)))
        let widgets = await Promise.all(widgetPromises)
        widgets = widgets.filter(w => {
            return !_.isNil(w)
        })
        const pageActions: Action[] = [{
            title: "Done",
            actionType: "POP_ACTION"
        }]
        return {widgets: widgets, pageActions: pageActions, actions: pageActions, alertInfo: alertInfo}
    }

    private getChangeAddressWidget(userContext: UserContext, foodBooking: FoodBooking, isCancelCutOffPassed: boolean, isPackActive: boolean): ManageOptionsWidget {
        const isEnabled = userContext.sessionInfo.isUserLoggedIn && !isCancelCutOffPassed && isPackActive && foodBooking.state !== "CANCELLED" && foodBooking.state !== "DELIVERED" && foodBooking.address.addressType !== "KIOSK"

        let addressText = foodBooking.address.addressLine1 ? foodBooking.address.addressLine1 + ", " : ""
        addressText = addressText + foodBooking.address.addressLine2
        const manageOptions: ManageOptions = {
            displayText: foodBooking.address.addressType + ": " + addressText,
            options: [
                {
                    displayText: "Change address",
                    action: "curefit://selectAddress",
                    type: "CHANGE_CART_ADDRESS",
                    isEnabled: isEnabled
                }
            ],
            icon: "LOCATION",
            secondaryIcon: "DROP_DOWN",
        }

        return {
            widgetType: "MANAGE_OPTIONS_WIDGET",
            manageOptions: manageOptions,
            invertStyle: true,
            isDisabled: !isEnabled,
            meta: this.getMealProductMeta(foodBooking)
        }
    }

    private getChangeInstructionWidget(foodBooking: FoodBooking, isCancelCutOffPassed: boolean, isPackActive: boolean, userContext: UserContext): ActionSheetWidget {
        const isEnabled = !isCancelCutOffPassed && isPackActive && foodBooking.state !== "CANCELLED" && foodBooking.state !== "DELIVERED"
        let actionSheet: ActionSheet = {
            selectedOption: foodBooking.address ? EatUtil.findDeliveryInstructionId(foodBooking.address.eatDeliveryInstruction) : 0,
            options: EatUtil.convertDeliveryInstructionsToOptions(),
            meta: {
                productId: foodBooking.productId,
                fulfilmentId: foodBooking.fulfilmentId,
                date: foodBooking.deliveryDate,
                packId: foodBooking.packId
            },
            actionType: "CHANGE_ORDER_INSTRUCTION"
        }
        const address = foodBooking.address
        if (MealUtil.isCutleryChangeSupported(userContext)) {
            actionSheet = EatUtil.getCutleryInstructionAction(address.eatDeliveryInstruction, actionSheet.meta, "CHANGE_ORDER_INSTRUCTION")
        }
        return {
            widgetType: "ACTION_SHEET_WIDGET",
            icon: "INSTRUCTION",
            isDisabled: !isEnabled,
            action: actionSheet
        }
    }

    private async getManageOptions(foodBooking: FoodBooking, isPackOrder: boolean, isPackActive: boolean, isOnDemand: boolean,
                                   timezone: Timezone): Promise<ManageOptions> {
        const options = await this.productBusiness.getCancelManageOptions(foodBooking, isPackOrder, isPackActive, false, isOnDemand, true, timezone)
        options.manageOptions.options = options.manageOptions.options.filter((option: ManageOptionPayload) => {
            return option.isEnabled
        })
        return options.manageOptions
    }

    private async getCancelMealWidget(foodBooking: FoodBooking, isPackOrder: boolean, isPackActive: boolean, isOnDemand: boolean,
                                      timezone: Timezone): Promise<ManageOptionsWidget> {
        const manageOptions = await this.productBusiness.getCancelManageOptions(foodBooking, isPackOrder, isPackActive, false, isOnDemand, true, timezone)
        manageOptions.manageOptions.options = manageOptions.manageOptions.options.filter((option: ManageOptionPayload) => {
            return option.isEnabled
        })
        const isEnabled = manageOptions.manageOptions.options.length > 0
        const manageOptionWidget = new ManageOptionsWidget(manageOptions.manageOptions, manageOptions.meta)
        manageOptionWidget.isDisabled = !isEnabled
        return manageOptionWidget
    }

    private getChangeMealWidget(userContext: UserContext, mealSlot: MenuType, foodBooking: FoodBooking, isCancelCutOffPassed: boolean, isPackActive: boolean): ManageOptionsWidget {
        const isEnabled = !isCancelCutOffPassed && foodBooking.state !== "CANCELLED" && foodBooking.state !== "DELIVERED" && isPackActive && foodBooking.changeMealAllowed && foodBooking.listingBrand !== "WHOLE_FIT"
        const displayText = MealUtil.isAddMealSupported(userContext) ? foodBooking.packId ? "Change / Add items" : "Add items" : "Change meal"
        const manageOptions: ManageOptions = {
            displayText: displayText,
            helperText: "",
            options: [
                {
                    displayText: displayText,
                    action: ActionUtil.changeMealPage(foodBooking.productId, foodBooking.deliveryDate, mealSlot, foodBooking.fulfilmentId, foodBooking.packId === undefined),
                    type: "CHANGE_MEAL",
                    isEnabled: isEnabled
                }
            ],
            icon: "CHANGE_MEAL",
        }

        return {
            widgetType: "MANAGE_OPTIONS_WIDGET",
            manageOptions: manageOptions,
            isDisabled: !isEnabled,
            meta: this.getMealProductMeta(foodBooking)
        }
    }

    private getMealProductMeta(foodBooking: FoodBooking): any {
        return {
            fulfilmentId: foodBooking.fulfilmentId,
            date: foodBooking.deliveryDate,
            packId: foodBooking.packId,
            productId: foodBooking.productId
        }
    }

    async getStartDateWidget(date: string, validPackStartDates: string[], userContext: UserContext, timezone: Timezone): Promise<ActionSheetWidget> {
        validPackStartDates.sort()
        const dateoptions: ActionSheetOption[] = []
        for (let i: number = 0; i < validPackStartDates.length; i++) {
            const dateStr: string = TimeUtil.formatDateStringInTimeZone(validPackStartDates[i], timezone, "Do MMMM")
            dateoptions.push({
                optionId: validPackStartDates[i],
                payload: {
                    date: validPackStartDates[i]
                },
                optionText: "Starting: " + dateStr
            })
        }
        const selectedoptionindex = validPackStartDates.findIndex((validDate) => {
            return validDate === date
        })
        const widget: ActionSheetWidget = {
            widgetType: "ACTION_SHEET_WIDGET",
            icon: "DATE",
            isDisabled: false,
            action: {
                selectedOption: selectedoptionindex,
                options: dateoptions,
                actionType: "SET_PACK_STARTDATE"
            }
        }
        if (MealUtil.isPackStartDateCalendarSupported(userContext)) {
            widget.action = {
                actionType: "SHOW_DATETIME_PICKER_MODAL",
                title: "Start Date",
                selectedOption: 0,
                options: [{optionText: TimeUtil.formatDateStringInTimeZone(date, timezone, "Do MMMM")}],
                defaultDate: date,
                meta: {
                    defaultDate: date,
                    minimumDate: validPackStartDates[0],
                    maximumDate: validPackStartDates[validPackStartDates.length - 1],
                    pickDateAction: {
                        actionType: "SET_PACK_STARTDATE"
                    }
                }
            }
        }
        return widget
    }

    async getCartSummaryWidget(userContext: UserContext, foodBooking: FoodBooking, issuesMap: Map<string, CustomerIssueType[]>, showSubTitle: boolean): Promise<CartSummaryWidget> {
        const tz = foodBooking.timezone
        const startDate = TimeUtil.getMomentForDate(foodBooking.deliveryWindow.start, tz)
        const startTime: HourMin = {
            hour: startDate.hours(),
            min: startDate.minutes()
        }
        const mealSlot = foodBooking.deliverySlot ? SlotUtil.getMealSlotForSlotId(foodBooking.deliverySlot.slotId, foodBooking.timezone) : SlotUtil.getClosestMealSlot(startTime)
        const isCafe: boolean = !_.isNil(foodBooking.address) && foodBooking.address.kioskType === "CAFE"
        let title: string
        const subtitleText: string = foodBooking.listingBrand === "WHOLE_FIT" ? "Order" : "Meal"
        if (foodBooking.listingBrand === "WHOLE_FIT") {
            title = "Your whole.fit order"
        } else {
            title = (!isCafe && mealSlot) ? `Your ${capitalizeFirstLetter(mealSlot.toLowerCase())} order` : "Your Workout Snack "
        }
        let subTitle
        if (foodBooking.state === "CANCELLED") {
            subTitle = `${subtitleText} cancelled`
        } else if (foodBooking.state === "DELIVERED") {
            subTitle = `${subtitleText} delivered`
        } else {
            subTitle = `Arriving ${TimeUtil.getDayText(foodBooking.deliveryDate, tz)}, ${MealUtil.getSlotDisplayTextFoodBooking(foodBooking)}`
        }
        const result = await this.productBusiness.getMealManageOptions(userContext, foodBooking, issuesMap, false, true, false, true)
        return {
            widgetType: "CART_SUMMARY_WIDGET",
            manageOptions: result.manageOptions,
            meta: result.meta,
            title: title,
            subTitle: showSubTitle && !isCafe ? subTitle : undefined
        }

    }

    async getAlertInfo(deliveryInfo: ClientDeliveryInfo[], availabilities: ProductAvailability[], products: Product[], numProducts: number, user: User, userContext: UserContext, isOfferOverriden: boolean): Promise<AlertInfo> {
        let mealSlot: MenuType
        if (!_.isEmpty(deliveryInfo)) {
            mealSlot = deliveryInfo[0].mealSlot
        }
        if (userContext.sessionInfo.isUserLoggedIn) {
            const result = AppUtil.getUserAlertInfo(user, userContext)
            if (result && result.alertInfo)
                return result.alertInfo
        }
        if (isOfferOverriden) {
            return this.offerOverridenAlertInfo()
        }

        const unAvailableProductAvailabilities: ProductAvailability[] = availabilities.filter(availability => {
            return availability.status !== "AVAILABLE"
        })
        let isPackProduct = false
        const unavailableProducts = _.map(unAvailableProductAvailabilities, unAvailableProduct => {
            const productInfo = products.find(product => {
                isPackProduct = product.isPack
                return product.productId === unAvailableProduct.productId
            })
            return {
                title: productInfo.title,
                productId: productInfo.productId,
                parentProductId: productInfo.parentProductId,
                mealSlot: {id: mealSlot}
            }
        })
        if (numProducts === 0) {
            return {
                title: "Items in cart not available",
                subTitle: "Items have gone out of stock and removed from your cart.",
                actions: AppUtil.isEatSoldOutCartItemRemovalSupported(userContext) ?
                    [{
                        actionType: isPackProduct && AppUtil.isSubscriptionAttachSupported(userContext) ? "REMOVE_SOLD_OUT_PACK_CART_ITEMS" : "REMOVE_SOLD_OUT_CART_ITEMS",
                        title: "Ok",
                        meta: {
                            items: unavailableProducts,
                            pageFrom: "cartcheckout"
                        },
                    }]
                    : [{
                        actionType: "HIDE_ALERT_MODAL",
                        title: "Ok",
                        url: "curefit://eatfitclp"
                    }],
            }
        }
        const nonServiceableAddressIndex = deliveryInfo.findIndex((info) => {
            return info.isWeekdayAddressServiceable === false || info.isWeekendAddressServiceable === false
        })
        if (nonServiceableAddressIndex >= 0) {
            const nonServiceableInfo = deliveryInfo[nonServiceableAddressIndex]
            return {
                title: "No Service At This Address!",
                subTitle: "Some items are not yet available at the address you’ve picked. Please try with a different address.",
                actions: [
                    {
                        actionType: nonServiceableInfo.isWeekdayAddressServiceable === false ? "CHANGE_ADDRESS" : "CHANGE_WEEKEND_ADDRESS",
                        title: nonServiceableInfo.isWeekdayAddressServiceable === false ? "Pick another address" : "Pick another weekend address",
                        url: "curefit://selectaddress?pageFrom=cart_checkout",
                        meta: {
                            isWeekendAddress: nonServiceableInfo.isWeekdayAddressServiceable === true,
                            mealSlot: nonServiceableInfo.mealSlot
                        }
                    }],
            }
        }


        if (unAvailableProductAvailabilities.length > 0) {
            let isPackProduct = false
            return {
                title: "Some items unavailable!",
                subTitle: `These ${unAvailableProductAvailabilities.length} items have gone out of stock, and will be removed from your cart: `,
                unavailableItems: _.map(unAvailableProductAvailabilities, unAvailableProduct => {
                    const productInfo = products.find(product => {
                        isPackProduct = product.isPack
                        return product.productId === unAvailableProduct.productId
                    })
                    return productInfo.title
                }),
                actions: AppUtil.isEatSoldOutCartItemRemovalSupported(userContext) ?
                    [{
                        actionType: isPackProduct && AppUtil.isSubscriptionAttachSupported(userContext) ? "REMOVE_SOLD_OUT_PACK_CART_ITEMS" : "REMOVE_SOLD_OUT_CART_ITEMS",
                        title: "Continue to cart",
                        meta: {
                            items: unavailableProducts,
                            pageFrom: "cartcheckout"
                        },
                    }]
                    : [{
                        actionType: "HIDE_ALERT_MODAL",
                        title: "Continue to cart",
                        url: "curefit://cartcheckout"
                    }]
            }
        }
    }

    private async getAddressWidget(userContext: UserContext, weekDayAddress: UserDeliveryAddress, weekendAddress: UserDeliveryAddress, weekendEnabled: boolean, isPack: boolean, mealSlot: MenuType): Promise<CheckoutAddressWidget> {
        return new CheckoutAddressWidget(userContext, weekDayAddress, weekendAddress, weekendEnabled, isPack, mealSlot, undefined)
    }

    private async getCartDetailListWidget(order: Order, foodBooking: FoodBooking, userContext: UserContext): Promise<CartDetailListWidget> {
        let numItems = 0
        const timezone = foodBooking.timezone
        const orderProductOption = order.productSnapshots[0].option
        const mealSlot = SlotUtil.getMealSlotForSlotId(orderProductOption.deliverySlot, timezone)
        // const orderProductSnapShots = await this.orderService.getSubOrderFoodProductSnapshots(order, foodFulfilment, foodBooking.deliveryDate)
        const orderProductSnapShots = [] as OrderProductSnapshots[]
        const orderItemsPromises: Promise<OrderItemDetail>[] = _.map(orderProductSnapShots, async orderProductSnapShot => {
            const quantity = orderProductSnapShot.quantity
            numItems = numItems + quantity
            const productBilling = this.offerHelper.getProductBilling(orderProductSnapShot, quantity)
            const listingBrand: ListingBrandIdType = EatUtil.getListingBrandFromOrder(order)
            let parentProduct
            if (!_.isNil(orderProductSnapShot.parentProductId)) {
                parentProduct = await this.catalogueService.getProduct(orderProductSnapShot.parentProductId)
            }
            const imageProductId = EatUtil.getProductId(orderProductSnapShot)
            const nutritionalInfo = EatUtil.computeNutritionalInfo(orderProductSnapShot, parentProduct, listingBrand)
            const isVeg = orderProductSnapShot.attributes ? orderProductSnapShot.attributes["isVeg"] === "TRUE" ? true : false : undefined
            const calories = nutritionalInfo && !isNaN(nutritionalInfo.Calories["Total Calories"]) ? nutritionalInfo.Calories["Total Calories"] : undefined
            const orderItem: OrderItemDetail = {
                productId: orderProductSnapShot.productId,
                price: {
                    mrp: Math.round(productBilling.total),
                    listingPrice: Math.round(productBilling.total),
                    currency: productBilling.currency
                },
                isVeg: isVeg,
                calories: calories,
                productName: orderProductSnapShot.title,
                productNameWithoutUnits: orderProductSnapShot.titleWithoutUnits,
                quantity: quantity,
                servingUnit: orderProductSnapShot.servingUnit,
                servingQty: orderProductSnapShot.servingQty,
                displayUnitQty: EatUtil.getQuantityTitle(orderProductSnapShot),
                variantTitle: EatUtil.getVariantTitle(orderProductSnapShot, listingBrand),
                image: orderProductSnapShot.productType === "FIT_CLUB_MEMBERSHIP" ?
                    UrlPathBuilder.getPackImagePath(orderProductSnapShot.productId, "FIT_CLUB_MEMBERSHIP", "THUMBNAIL", orderProductSnapShot.imageVersion, userContext.sessionInfo.userAgent) :
                    UrlPathBuilder.getSingleImagePath(imageProductId, "FOOD", "THUMBNAIL", orderProductSnapShot.imageVersion)
            }
            const seoParams = {
                productName: orderProductSnapShot.title
            }
            orderItem["action"] = {
                actionType: "NAVIGATION",
                url: ActionUtil.foodSingle(orderProductSnapShot.productId, orderProductOption.startDate, mealSlot, false, undefined, userContext.sessionInfo.userAgent, seoParams)
            }
            return orderItem
        })
        const orderItems: OrderItemDetail[] = await Promise.all(orderItemsPromises)
        const widget: CartDetailListWidget = {
            widgetType: "CART_DETAIL_LIST_WIDGET",
            orderItems: orderItems,
            title: numItems === 1 ? `${numItems} item ordered` : `${numItems} items ordered`
        }
        return widget
    }

    private isValidAddon(order: Order, product: Product): boolean {
        return EatUtil.isAddOnCategory(product.categoryId) && this.productBusiness.isValidAddOn(order.productSnapshots, product)
    }

    private async getCartAddonWidget(mealSlot: MenuType, singleOffersResponse: FoodSinglePriceOfferResponse, menuAvailabilityResult: MenuAvailabilityResult, order: Order, userId: string, deviceId: string, userContext: UserContext, areaId: string, listingBrand?: ListingBrandIdType): Promise<AddonListWidget> {
        const cityId = userContext.userProfile.cityId
        const menusExcludingOrderedProduct = menuAvailabilityResult.menus.filter(menu => {
            return _.find(order.productSnapshots, (productSnapshot: OrderProductSnapshots) => {
                return productSnapshot.productId === menu.productId && _.isNil(productSnapshot.comboOfferId)
            }) === undefined
        })
        const addonItems: AddonItem[] = []
        const productArray: Product[] = []
        for (let i = 0; i < menusExcludingOrderedProduct.length; i++) {
            const menu = menusExcludingOrderedProduct[i]
            const product = clone(await this.catalogueService.getProduct(menu.productId))
            productArray.push(product)
        }
        productArray.sort((product1: Product, product2: Product) => {
            const diff = CATEGORY_PRIORITY_MAP.get(product1.categoryId) - CATEGORY_PRIORITY_MAP.get(product2.categoryId)
            return diff > 0 ? 1 : diff < 0 ? -1 : diff
        })
        for (let i = 0; i < productArray.length; i++) {
            const product = productArray[i]
            if (!this.isValidAddon(order, product)) {
                continue
            }
            const offerAndPrice = OfferUtil.getSingleOfferAndPrice(product, order.productSnapshots[0].option.startDate, singleOffersResponse, mealSlot)
            const availability = OfferUtil.getAvailabilityV1(product, menuAvailabilityResult.inventoryResult)
            if (availability.left > 0) {
                let offerIds
                if (!_.isEmpty(offerAndPrice.offers)) {
                    offerIds = _.map(offerAndPrice.offers, offer => {
                        return offer.offerId
                    })
                }
                const image = UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, product.productType, "THUMBNAIL", product.imageVersion)
                const seoParams = {
                    productName: product.title
                }
                const actions: (Action | MealAction)[] = [{
                    actionType: "WIDGET_NAVIGATION",
                    url: ActionUtil.foodSingle(product.productId, menuAvailabilityResult.day, mealSlot, true, undefined, userContext.sessionInfo.userAgent, seoParams)
                }, {
                    actionType: "ADD_TO_CART",
                    title: "ADD",
                    image: image,
                    date: menuAvailabilityResult.day,
                    productId: product.productId,
                    price: offerAndPrice.price,
                    offerId: !_.isEmpty(offerIds) ? offerIds[0] : undefined,
                    offerIds: offerIds,
                    maxCartSize: MAX_CART_SIZE,
                    productName: product.title,
                    stock: Math.min(MAX_CART_SIZE, availability.left),
                    mealSlot: {
                        id: mealSlot,
                        name: capitalizeFirstLetter(mealSlot.toLowerCase())
                    },
                    listingBrand: listingBrand
                }]
                product.price = offerAndPrice.price
                const addonItem: AddonItem = {
                    image: image,
                    title: product.title,
                    productId: product.productId,
                    price: offerAndPrice.price,
                    date: menuAvailabilityResult.day,
                    stock: Math.min(MAX_CART_SIZE, availability.left),
                    calories: `${product.attributes.nutritionInfo.Calories["Total Calories"]} cal`,
                    actions: actions,
                    isVeg: product.attributes["isVeg"] === "TRUE" ? true : false,
                    isInventorySet: availability.total > 0 ? true : false
                }
                addonItems.push(addonItem)
            }
        }
        if (_.isEmpty(addonItems))
            return undefined
        else {
            const cartSubTotal = order.productSnapshots.reduce((subTotal, product) => {
                return subTotal + (product.price.listingPrice * product.quantity)
            }, 0)
            const cartOffers: OfferV2[] = []
            const nextOffer: OfferV2 = cartOffers.reduce((bestOffer: OfferV2, currentOffer: OfferV2) => {
                if (_.isNil(currentOffer.constraints.mealSlots)
                    || currentOffer.constraints.mealSlots.indexOf(mealSlot) < 0
                    || currentOffer.constraints.minimumAmount <= cartSubTotal
                    || (currentOffer.constraints.maximumAmount && currentOffer.constraints.maximumAmount < cartSubTotal)) {
                    return bestOffer
                } else {
                    if (bestOffer === undefined)
                        return currentOffer
                    else if (bestOffer.constraints.minimumAmount === currentOffer.constraints.minimumAmount) {
                        return bestOffer.priority > currentOffer.priority ? bestOffer : currentOffer
                    } else if (bestOffer.constraints.minimumAmount < currentOffer.constraints.minimumAmount) {
                        return bestOffer
                    } else {
                        return currentOffer
                    }
                }
            }, undefined)
            const subTitle = nextOffer ? nextOffer.description : undefined
            addonItems.sort((a, b) => {
                return a.price.listingPrice - b.price.listingPrice
            })
            const addonListWidget: AddonListWidget = {
                title: "ADD AN ITEM",
                subTitle: subTitle,
                addonItems: addonItems,
                widgetType: "ADDON_LIST_WIDGET"
            }
            return addonListWidget
        }
    }

    private async getCartListWidgetAndBilling(userContext: UserContext, order: Order, billingInfo: BillingInfo, availabilities: ProductAvailability[], areaTz: Timezone): Promise<{ widget: CartListWidget, billing: BillingInfo }> {
        let numItems = 0
        const orderProductOption = order.productSnapshots[0].option
        const listingBrand: ListingBrandIdType = EatUtil.getListingBrandFromOrder(order)
        const mealSlot = SlotUtil.getMealSlotForSlotId(orderProductOption.deliverySlot, areaTz)
        const orderItemsPromises: Promise<OrderItem>[] = _.map(order.productSnapshots, async orderProductSnapShot => {
            const quantity = orderProductSnapShot.quantity
            numItems = numItems + quantity
            const productAvailability = _.find(availabilities, productAvailability => {
                return productAvailability.productId === orderProductSnapShot.productId
            })
            const productBilling = this.offerHelper.getProductBilling(orderProductSnapShot, quantity)
            const seoParams = {
                productName: orderProductSnapShot.title
            }
            const orderItem: OrderItem = {
                productId: orderProductSnapShot.productId,
                image: UrlPathBuilder.getSingleImagePath(orderProductSnapShot.parentProductId ? orderProductSnapShot.parentProductId : orderProductSnapShot.productId, orderProductSnapShot.productType, "THUMBNAIL", orderProductSnapShot.imageVersion),
                offerId: orderProductSnapShot.option.offerId,
                price: {
                    mrp: productBilling.total,
                    listingPrice: productBilling.total,
                    currency: productBilling.currency
                },
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtil.foodSingle(orderProductSnapShot.productId, orderProductOption.startDate,
                        mealSlot, false, undefined, userContext.sessionInfo.userAgent, seoParams),
                },
                option: orderProductSnapShot.option,
                parentProductId: orderProductSnapShot.complementaryProductIds ? orderProductSnapShot.complementaryProductIds[0] : undefined,
                comboOfferId: orderProductSnapShot.comboOfferId,
                productName: orderProductSnapShot.title,
                productNameWithoutUnits: orderProductSnapShot.titleWithoutUnits,
                subTitle: orderProductSnapShot.option.subscriptionType !== undefined ? (capitalizeFirstLetter(orderProductSnapShot.option.subscriptionType) + " plan") : undefined,
                isVeg: this.getIsVeg(orderProductSnapShot),
                stock: Math.min(MAX_CART_SIZE, productAvailability.available),
                quantity: quantity,
                servingQty: orderProductSnapShot.servingQty,
                servingUnit: orderProductSnapShot.servingUnit,
                displayUnitQty: EatUtil.getQuantityTitle(orderProductSnapShot),
                variantTitle: EatUtil.getVariantTitle(orderProductSnapShot, listingBrand),
            }
            return orderItem
        })
        const orderItems: OrderItem[] = await Promise.all(orderItemsPromises)
        const offersApplied = !_.isEmpty(order.offersApplied) ? await this.offerServiceV3.getOffersByIds(order.offersApplied) : {}
        const priceDetails = this.getPriceDetails(userContext, {
            ...billingInfo,
            orderDiscountsWithTitle: offersApplied
        }, _.map(offersApplied), listingBrand, order.deliveryCharges, order.packagingCharges, true, true, false, undefined, undefined, false)
        const widget: CartListWidget = {
            widgetType: "CART_LIST_WIDGET",
            orderItems: orderItems,
            title: `${numItems} ITEMS`,
            priceDetails: priceDetails.components,
            cartValue: priceDetails.cartValue
        }
        return {widget: widget, billing: billingInfo}
    }

    private async getCartListWidget(userContext: UserContext, areaTz: Timezone, order: Order, availabilities: ProductAvailability[],
                                    fitclubPickerAction: Action, fitcashEarning: number, freeDeliverySaving: number): Promise<CartListWidget> {
        let numItems = 0
        const isOnline = order.userAddress.addressType !== "KIOSK"
        const orderProductOption = order.productSnapshots[0].option
        const listingBrand: ListingBrandIdType = EatUtil.getListingBrandFromOrder(order)
        const fitClubProduct = _.find(order.productSnapshots, (orderProduct) => {
            return orderProduct.productType === "FIT_CLUB_MEMBERSHIP"
        })
        const mealSlot = SlotUtil.getMealSlotForSlotId(orderProductOption.deliverySlot, areaTz)
        const orderItemsPromises: Promise<OrderItem>[] = _.map(order.productSnapshots, async orderProductSnapShot => {
            const quantity = orderProductSnapShot.quantity
            numItems = numItems + quantity
            const productAvailability = _.find(availabilities, productAvailability => {
                return productAvailability.productId === orderProductSnapShot.productId
            })
            const productBilling = this.offerHelper.getProductBilling(orderProductSnapShot, quantity)
            const subTitle: string = orderProductSnapShot.option.subscriptionType !== undefined ? (capitalizeFirstLetter(orderProductSnapShot.option.subscriptionType) + " plan") : undefined
            const seoParams = {
                productName: orderProductSnapShot.title
            }
            const orderItem: OrderItem = {
                productId: orderProductSnapShot.productId,
                image: orderProductSnapShot.isPack || orderProductSnapShot.productType === "FIT_CLUB_MEMBERSHIP" ?
                    UrlPathBuilder.getPackImagePath(orderProductSnapShot.productId, orderProductSnapShot.productType, orderProductSnapShot.productType === "FIT_CLUB_MEMBERSHIP" ? "THUMBNAIL" : "MAGAZINE", orderProductSnapShot.imageVersion, userContext.sessionInfo.userAgent) :
                    UrlPathBuilder.getSingleImagePath(orderProductSnapShot.parentProductId ? orderProductSnapShot.parentProductId : orderProductSnapShot.productId, orderProductSnapShot.productType, "THUMBNAIL", orderProductSnapShot.imageVersion),
                offerId: orderProductSnapShot.option.offerId,
                price: {
                    mrp: productBilling.total,
                    listingPrice: productBilling.total,
                    currency: productBilling.currency
                },
                action: orderProductSnapShot.productType === "FIT_CLUB_MEMBERSHIP" ? fitclubPickerAction : {
                    actionType: "NAVIGATION",
                    url: ActionUtil.foodSingle(orderProductSnapShot.productId, orderProductOption.startDate,
                        mealSlot, false, undefined, userContext.sessionInfo.userAgent, seoParams),
                },
                productType: orderProductSnapShot.productType,
                option: orderProductSnapShot.option,
                parentProductId: orderProductSnapShot.complementaryProductIds ? orderProductSnapShot.complementaryProductIds[0] : undefined,
                comboOfferId: orderProductSnapShot.comboOfferId,
                productName: orderProductSnapShot.title,
                productNameWithoutUnits: orderProductSnapShot.titleWithoutUnits,
                subTitle: subTitle,
                isVeg: this.getIsVeg(orderProductSnapShot),
                stock: orderProductSnapShot.productType === "FIT_CLUB_MEMBERSHIP" ? MAX_CART_SIZE : Math.min(MAX_CART_SIZE, productAvailability.available),
                quantity: quantity,
                servingUnit: orderProductSnapShot.servingUnit,
                servingQty: orderProductSnapShot.servingQty,
                displayUnitQty: EatUtil.getQuantityTitle(orderProductSnapShot),
                variantTitle: EatUtil.getVariantTitle(orderProductSnapShot, listingBrand),
            }
            return orderItem
        })
        const orderItems: OrderItem[] = await Promise.all(orderItemsPromises)
        orderItems.sort((orderItemA, orderItemB) => {
            return orderItemA.productType === "FIT_CLUB_MEMBERSHIP" ? -1 : orderItemB.productType === "FIT_CLUB_MEMBERSHIP" ? 1 : 0
        })
        const widget: CartListWidget = {
            widgetType: "CART_LIST_WIDGET",
            orderItems: orderItems,
            title: `${numItems} ITEMS`,
        }
        return widget
    }

    private async getBillingWidget(userContext: UserContext, order: Order, billingInfo: BillingInfo, fitcashBalance?: number, isFitcashEnabled?: boolean): Promise<BillingWidget> {
        const offersApplied = !_.isEmpty(order.offersApplied) ? await this.offerServiceV3.getOffersByIds(order.offersApplied) : {}
        const listingBrand: ListingBrandIdType = EatUtil.getListingBrandFromOrder(order)
        const priceDetails = this.getPriceDetails(userContext, {
            ...billingInfo,
            orderDiscountsWithTitle: offersApplied
        }, _.map(offersApplied), listingBrand, order.deliveryCharges, order.packagingCharges, true, true, false, undefined, undefined, false, fitcashBalance, isFitcashEnabled)

        const widget: BillingWidget = {
            widgetType: "BILLING_WIDGET",
            title: `BILLING DETAILS`,
            priceDetails: priceDetails.components,
            cartValue: priceDetails.cartValue,
            fitClubSavings: undefined,
            fitcashBalance,
            isFitcashEnabled,
            currency: billingInfo.currency
        }
        return widget
    }


    private getIsVeg(productSnapshot: Product): boolean {
        if (!_.isNil(productSnapshot.attributes)) {
            return productSnapshot.attributes["isVeg"] === "TRUE" ? true : false
        } else if (!_.isNil(productSnapshot.isVeg)) {
            return productSnapshot.isVeg
        } else {
            return false
        }
    }

    private getPriceDetails(userContext: UserContext, billingInfo: BillingInfo, offersApplied: OfferV2[], listingBrand: ListingBrandIdType, deliveryCharge?: DeliveryCharge, packagingCharge?: ExtraCharge, showDelivery?: boolean, showPackaging?: boolean, showBasePrice?: boolean, adjustedAmount?: number, penaltyAmount?: number, showTaxes?: boolean, fitcashBalance?: number, isFitcashEnabled?: boolean, careHomeCollectionCharges?: CareHomeCollectionCharges, order?: Order, boosterPack?: OfflineFitnessPack): { components: PriceComponent[], cartValue: number } {
        const extraChargeConfig = this.configService.getConfig("EAT_EXTRA_CHARGE_CONFIG")
        const deliveryChargeForBrand = _.get(extraChargeConfig, "configs.deliveryCharge." + listingBrand, DEFAULT_DELIVERY_CHARGE)
        const packagingChargeForBrand = _.get(extraChargeConfig, "configs.packagingCharge." + listingBrand, PACKAGING_CHARGE)
        let priceDetails: PriceComponent[] = []

        // PLATFORM FEE
        let platformFee: string = undefined
        let platformFeeTax: string = undefined
        let totalPlatformFeeIncTax: number = undefined
        if (order?.extraCharges && order?.extraCharges?.platformFee) {
            platformFee = order?.extraCharges?.platformFee?.unitPrice?.toFixed(2)
            platformFeeTax = order?.extraCharges?.platformFee?.tax?.amount?.toFixed(2)
            totalPlatformFeeIncTax = order?.extraCharges?.platformFee?.total
        }

        // BOOSTER PACKS PRICING
        const boosterPackProductPriceObject = OrderUtilV1.isPlusMembershipOrder(order) ? undefined : order?.productSnapshots?.[0]?.addOnSnapshots?.[0]?.price
        let totalBoosterPackPriceIncTax: number = undefined
        let boosterPackTax: number = undefined
        if (boosterPackProductPriceObject) {
            totalBoosterPackPriceIncTax = boosterPackProductPriceObject?.listingPrice
            boosterPackTax = boosterPackProductPriceObject?.tax?.taxAmount
        }

        const deliveryOffer = _.find(offersApplied, (offerV2) => {
            return offerV2.noDeliveryCharge === true
        })
        const packagingOffer = _.find(offersApplied, (offerV2) => {
            return offerV2.noPackagingCharge === true
        })
        if (platformFee || boosterPack) {
            priceDetails.push({
                title: "Membership Price",
                value: (billingInfo.mrp - (showTaxes !== false ? billingInfo.tax.amount : 0) + (!showTaxes && totalPlatformFeeIncTax ? totalPlatformFeeIncTax : 0)).toFixed(2),
            })
        } else {
            priceDetails.push({
                title: "Membership Price",
                value: (billingInfo.mrp - (showTaxes !== false ? billingInfo.tax.amount : 0)).toFixed(2),
            })
        }

        // BOOSTER PRICE
        if (totalBoosterPackPriceIncTax && boosterPackTax) {
            priceDetails.push({
                title: "Booster Price",
                prefixIcon: "cancel",
                value: (totalBoosterPackPriceIncTax - boosterPackTax).toFixed(2),
                showBottomDivider: true,
                action: boosterPack ? {
                    actionType: "SHOW_CUSTOM_BOTTOM_SHEET",
                    meta: {
                        backdropClickable: true,
                        bgImageUrl: "/image/icons/addons_bg.png",
                        widgets: [this.addFomoWidget(boosterPack, true)]
                    }
                } : null
            })
        }

        if (showTaxes !== false) {
            if (!platformFee || !platformFeeTax) {
                priceDetails.push({
                    title: billingInfo.tax.type + "@" + billingInfo.tax.percentage + "%",
                    value: (billingInfo.tax.amount + (boosterPackTax ? boosterPackTax : 0)).toFixed(2),
                })
            }
            else if (platformFee && platformFeeTax) {
                let totalFee = billingInfo.tax.amount
                totalFee = totalPlatformFeeIncTax ? totalFee + totalPlatformFeeIncTax : totalFee
                totalFee = boosterPackTax ? totalFee + boosterPackTax : totalFee
                priceDetails.push({
                    title: "Taxes & Fees",
                    value: totalFee.toFixed(2),
                    action: totalPlatformFeeIncTax ? CartViewBuilder.getTaxesAndFeeBottomSheetAction(billingInfo, platformFee, platformFeeTax, totalPlatformFeeIncTax, boosterPackTax) : null
                })
            }
        }

        if (billingInfo.discount) {
            priceDetails.push(...OrderUtilV1.getDiscountPriceDetails(billingInfo))
        }

        if (showBasePrice !== false) {
            priceDetails.push({
                title: "Base Price",
                value: billingInfo.unitPrice.toFixed(2),
            })
        }

        if (deliveryCharge && showDelivery !== false) {
            priceDetails.push({
                // title: mealSlot === "SNACKS" ? `Delivery charge\n(for orders under ${RUPEE_SYMBOL}50)`
                // : `Delivery charge\n(for orders under ${RUPEE_SYMBOL}200)`,
                title: `Delivery charge`,
                value: deliveryCharge.total.toFixed(2),
            })
        } else if (showDelivery !== false && deliveryOffer) {
            if (AppUtil.isDeliveryStrikeOffSupported(userContext)) {
                priceDetails.push({
                    title: `Delivery charge`,
                    value: deliveryChargeForBrand + "",
                    offerText: "FREE",
                    isValueStrikeOff: true,
                    isDiscount: true,
                })
            } else {
                priceDetails.push({
                    title: `Delivery charge`,
                    value: "0",
                })
            }
        }

        if (packagingCharge && showPackaging !== false && packagingCharge.total !== 0) {
            // this.logger.info("packaging shows up here")
            priceDetails.push({
                title: `Packaging charge`,
                value: packagingCharge.total.toFixed(2),
            })
        } else if (showPackaging !== false && packagingOffer) {
            if (AppUtil.isDeliveryStrikeOffSupported(userContext)) {
                priceDetails.push({
                    title: `Packaging charge`,
                    value: packagingChargeForBrand + "",
                    offerText: "FREE",
                    isValueStrikeOff: true,
                    isDiscount: true,
                })
            } else {
                priceDetails.push({
                    title: `Packaging charge`,
                    value: "0",
                })
            }
        }
        if (penaltyAmount > 0) {
            priceDetails.push({
                title: "Penalty Amount",
                value: penaltyAmount.toFixed(2)
            })
        }
        if (adjustedAmount && adjustedAmount > 0) {
            priceDetails.push({
                title: "Adjusted Amount",
                value: adjustedAmount.toFixed(2),
                isDiscount: true,
            })
        }
        let total = deliveryCharge ? billingInfo.total + deliveryCharge.total : billingInfo.total
        total = packagingCharge ? total + packagingCharge.total : total
        total = totalPlatformFeeIncTax ? total + totalPlatformFeeIncTax : total
        total = totalBoosterPackPriceIncTax ? total + totalBoosterPackPriceIncTax : total

        if (fitcashBalance > 0) {
            if (isFitcashEnabled) {
                priceDetails.push({
                    title: "Total",
                    value: total.toFixed(2),
                    isTotalWithoutFitcash: true,
                })
            }

            priceDetails.push({
                title: `Use Fitcash`,
                fitcashBalance,
                value: (fitcashBalance > total ? total : fitcashBalance).toFixed(0),
                isEnabled: isFitcashEnabled,
                isFitcashPrice: true,
            })

            if (isFitcashEnabled) {
                total = (fitcashBalance > total ? 0 : total - fitcashBalance)
            }
        }
        if (careHomeCollectionCharges?.showHomeSampleCharges) {
            priceDetails.push({
                title: `Home Collection Charge`,
                value: careHomeCollectionCharges?.orderHomeSampleCharges > 0 ? careHomeCollectionCharges?.homeSampleCharges.toString() : "FREE",
                valueWithCurrency: careHomeCollectionCharges?.homeSampleCharges > 0 ? RUPEE_SYMBOL + careHomeCollectionCharges.homeSampleCharges.toString() : undefined,
                offerText: "FREE",
                isValueStrikeOff: careHomeCollectionCharges?.orderHomeSampleCharges ? false : true,
            })
            total = total + (careHomeCollectionCharges?.orderHomeSampleCharges ? careHomeCollectionCharges?.orderHomeSampleCharges : 0)
        }
        priceDetails.push({
            title: "Total Payable",
            value: total.toFixed(2),
        })
        priceDetails = priceDetails.map(o => {
            o.symbol = `\u20B9`
            return o
        })
        return {components: priceDetails, cartValue: total}
    }

    private getPriceDetailsForTransform(userContext: UserContext, billingInfo: BillingInfo, offersApplied: OfferV2[], listingBrand: ListingBrandIdType, deliveryCharge?: DeliveryCharge, packagingCharge?: ExtraCharge, showDelivery?: boolean, showPackaging?: boolean, showBasePrice?: boolean, adjustedAmount?: number, penaltyAmount?: number, showTaxes?: boolean, fitcashBalance?: number, isFitcashEnabled?: boolean, careHomeCollectionCharges?: CareHomeCollectionCharges): { components: PriceComponent[], cartValue: number } {
        let priceDetails: PriceComponent[] = []


        priceDetails.push({
            title: "MRP",
            value: billingInfo.mrp.toFixed(2),
        })

        if (billingInfo.discount) {
            priceDetails.push(...OrderUtilV1.getDiscountPriceDetails(billingInfo))
        }

        let totalPrice: number = billingInfo.mrp - billingInfo.tax.amount
        priceDetails.forEach((priceData) => {
            if (priceData.title?.toLowerCase().includes("discount")) {
                totalPrice = totalPrice - Number(priceData.value)
            }
        })

        priceDetails.unshift({
            title: "Total",
            value: totalPrice.toFixed(2),
        })

        if (showTaxes !== false) {
            priceDetails.push({
                title: billingInfo.tax.type + "@" + billingInfo.tax.percentage + "%",
                value: billingInfo.tax.amount.toFixed(2),
            })
        }

        let total = billingInfo.total

        if (fitcashBalance > 0) {
            if (isFitcashEnabled) {
                priceDetails.push({
                    title: "Total",
                    value: total.toFixed(2),
                    isTotalWithoutFitcash: true,
                })
            }

            priceDetails.push({
                title: `Use Fitcash`,
                fitcashBalance,
                value: (fitcashBalance > total ? total : fitcashBalance).toFixed(0),
                isEnabled: isFitcashEnabled,
                isFitcashPrice: true,
            })

            if (isFitcashEnabled) {
                total = (fitcashBalance > total ? 0 : total - fitcashBalance)
            }
        }
        priceDetails.push({
            title: "Total Payable",
            value: total.toFixed(2),
        })
        priceDetails = priceDetails.map(o => {
            o.symbol = `\u20B9`
            return o
        })
        return {components: priceDetails, cartValue: total}
    }

    private getPriceDetailsForSugarfit(userContext: UserContext, billingInfo: BillingInfo, offersApplied: OfferV2[], listingBrand: ListingBrandIdType,  maxDeliveryChargeForBrand: number, deliveryCharge?: DeliveryCharge, packagingCharge?: ExtraCharge, showDelivery?: boolean, showPackaging?: boolean, showBasePrice?: boolean, adjustedAmount?: number, penaltyAmount?: number, showTaxes?: boolean, fitcashBalance?: number, isFitcashEnabled?: boolean, careHomeCollectionCharges?: CareHomeCollectionCharges): { components: PriceComponent[], cartValue: number, freeDeliveryOverrideMessage?: string, moreAmountToBuyForFreeDelivery?: number, usersCartValueAfterPriceDiscount?: number } {
        const extraChargeConfig = this.configService.getConfig("EAT_EXTRA_CHARGE_CONFIG")
        const deliveryChargeForBrand = maxDeliveryChargeForBrand || 70
        const ecomConfig: {
            minimumCartValueForFreeDelivery: number;
          } = JSON.parse(this.appConfigStore.getConfig("SF_ECOM_CONFIG", "{}"))
        const minAmountForFreeDelivery = _.isNumber(ecomConfig?.minimumCartValueForFreeDelivery) ? ecomConfig?.minimumCartValueForFreeDelivery : 900
        const packagingChargeForBrand = _.get(extraChargeConfig, "configs.packagingCharge." + listingBrand, PACKAGING_CHARGE)
        let priceDetails: PriceComponent[] = []
        const deliveryOffer = _.find(offersApplied, (offerV2) => {
            return offerV2.noDeliveryCharge === true
        })
        const packagingOffer = _.find(offersApplied, (offerV2) => {
            return offerV2.noPackagingCharge === true
        })
        priceDetails.push({
            title: "Total (all taxes included)",
            value: billingInfo.mrp.toFixed(2),
        })
        let usersCartValueAfterPriceDiscount = Number(billingInfo.mrp.toFixed(2))

        if (billingInfo.discount) {
            const discountPriceDetailsComponent = OrderUtilV1.getDiscountPriceDetails(billingInfo)
            usersCartValueAfterPriceDiscount = usersCartValueAfterPriceDiscount - OrderUtilV1.getSfProductPriceDiscount(billingInfo)
            priceDetails.push(...discountPriceDetailsComponent)
        }

        if (showBasePrice !== false) {
            priceDetails.push({
                title: "Base Price",
                value: billingInfo.unitPrice.toFixed(2),
            })
        }

        if (showTaxes !== false) {
            priceDetails.push({
                title: billingInfo.tax.type + "@" + billingInfo.tax.percentage + "%",
                value: billingInfo.tax.amount.toFixed(2),
            })
        }

        let isFreeFromDeliveryCharge = false
        if (deliveryCharge && showDelivery !== false) {
            isFreeFromDeliveryCharge = false
            priceDetails.push({
                // title: mealSlot === "SNACKS" ? `Delivery charge\n(for orders under ${RUPEE_SYMBOL}50)`
                // : `Delivery charge\n(for orders under ${RUPEE_SYMBOL}200)`,
                title: `Delivery charge`,
                value: deliveryCharge.total.toFixed(2),
            })
        } else if (showDelivery !== false) {
            isFreeFromDeliveryCharge = true
            priceDetails.push({
                title: `Delivery`,
                value: deliveryChargeForBrand.toString() || "",
                offerText: "FREE",
                isValueStrikeOff: true,
                isDiscount: false,
            })
        }

        if (packagingCharge && showPackaging !== false && packagingCharge.total !== 0) {
            // this.logger.info("packaging shows up here")
            priceDetails.push({
                title: `Packaging charge`,
                value: packagingCharge.total.toFixed(2),
            })
        } else if (showPackaging !== false && packagingOffer) {
            if (AppUtil.isDeliveryStrikeOffSupported(userContext)) {
                priceDetails.push({
                    title: `Packaging charge`,
                    value: packagingChargeForBrand + "",
                    offerText: "FREE",
                    isValueStrikeOff: true,
                    isDiscount: true,
                })
            } else {
                priceDetails.push({
                    title: `Packaging charge`,
                    value: "0",
                })
            }
        }
        if (penaltyAmount > 0) {
            priceDetails.push({
                title: "Penalty Amount",
                value: penaltyAmount.toFixed(2)
            })
        }
        if (adjustedAmount && adjustedAmount > 0) {
            priceDetails.push({
                title: "Adjusted Amount",
                value: adjustedAmount.toFixed(2),
                isDiscount: true,
            })
        }
        let total = deliveryCharge ? billingInfo.total + deliveryCharge.total : billingInfo.total
        total = packagingCharge ? total + packagingCharge.total : total

        if (fitcashBalance > 0) {
            if (isFitcashEnabled) {
                priceDetails.push({
                    title: "Total",
                    value: total.toFixed(2),
                    isTotalWithoutFitcash: true,
                })
            }

            priceDetails.push({
                title: `Use Fitcash`,
                fitcashBalance,
                value: (fitcashBalance > total ? total : fitcashBalance).toFixed(0),
                isEnabled: isFitcashEnabled,
                isFitcashPrice: true,
            })

            if (isFitcashEnabled) {
                total = (fitcashBalance > total ? 0 : total - fitcashBalance)
            }
        }
        if (careHomeCollectionCharges?.showHomeSampleCharges) {
            priceDetails.push({
                title: `Home Collection Charge`,
                value: careHomeCollectionCharges?.orderHomeSampleCharges > 0 ? careHomeCollectionCharges?.orderHomeSampleCharges?.toString() : "150",
                valueWithCurrency: careHomeCollectionCharges?.orderHomeSampleCharges > 0 ? RUPEE_SYMBOL + careHomeCollectionCharges?.orderHomeSampleCharges?.toString() : undefined,
                offerText: "FREE",
                isValueStrikeOff: careHomeCollectionCharges?.orderHomeSampleCharges > 0 ? false : true,
            })
            total = total + (careHomeCollectionCharges?.orderHomeSampleCharges ? careHomeCollectionCharges?.orderHomeSampleCharges : 0)
        }
        priceDetails.push({
            title: "Total Payable",
            value: total.toFixed(2),
        })
        priceDetails = priceDetails.map(o => {
            o.symbol = `\u20B9`
            return o
        })
        let freeDeliveryOverrideMessage = undefined
        const moreAmountToBuy = isFreeFromDeliveryCharge ? 0 : minAmountForFreeDelivery - usersCartValueAfterPriceDiscount
        if (moreAmountToBuy > 0) {
            freeDeliveryOverrideMessage = `Add items worth ₹${moreAmountToBuy % 1 === 0 ? moreAmountToBuy : moreAmountToBuy.toFixed(2)} more to get FREE delivery`
        } else {
            freeDeliveryOverrideMessage = `🎊 It's a FREE delivery for you 🎊`
        }
        return {components: priceDetails, cartValue: total, freeDeliveryOverrideMessage, moreAmountToBuyForFreeDelivery: moreAmountToBuy, usersCartValueAfterPriceDiscount}
    }

    private getPackInfoWidget(foodPack: FoodPack, foodBooking: FoodBooking, userContext: UserContext): WidgetView {
        const seoParamas: SeoUrlParams = {
            productName: foodPack.title
        }
        const action: ActionCard = {
            title: "SEE PACK",
            action: ActionUtil.foodPack(foodBooking.packId, foodBooking.fulfilmentId, undefined, undefined, userContext.sessionInfo.userAgent, seoParamas)
        }
        return new PackInfoWidget(foodBooking.packId, foodPack.title, action)
    }

    private async getLHRBodyPartsWidget(selectedBodyPartIds: string[], product: DiagnosticProduct): Promise<LHRBodyPartsWidget> {
        if (!_.isEmpty(selectedBodyPartIds) && !_.isEmpty(_.get(product, "productSpec.allowedBodyParts"))) {
            const selectedParts: LHRBodyPart[] = []
            _.map(product.productSpec.allowedBodyParts, part => {
                if (selectedBodyPartIds.indexOf(part.code) != -1) {
                    selectedParts.push({
                        imageUrl: part.assetUrl,
                        code: part.code,
                        title: part.displayName,
                    })
                }
            })
            return {
                widgetType: "CARE_LHR_BODY_PARTS_WIDGET",
                style: {
                    backgroundColor: "#f2f4f8",
                },
                bodyParts: selectedParts
            }
        }
        return undefined
    }

    private async getStartDateInfoWidget(startTime: number, tz: Timezone, isRightOriented?: boolean): Promise<ProductListWidget> {
        const actionCards = [{
            title: "Starting Date",
            subTitle: TimeUtil.formatEpochInTimeZone(tz, Number(startTime), "D MMMM YYYY"),
            icon: "/image/icons/cult/calendar_new.png",
            iconSize: 18,
            titleStyle: {color: "#a8a8a8", fontSize: 12, fontFamily: AppFont.Medium},
            subtitleStyle: {fontSize: 14, fontFamily: AppFont.Regular},
            rowContainer: {marginLeft: 13}
        }]
        const startDateInfoWidget = new ProductListWidget("DYNAMIC_ICON", undefined, actionCards)
        startDateInfoWidget.noTopPadding = true
        startDateInfoWidget.noBottomPadding = true
        return {...startDateInfoWidget, showDivider: false, orientation: isRightOriented ? "RIGHT" : undefined}
    }

    private async getSGTStartDateSelectionWidget(userContext: UserContext, order: Order): Promise<DatePickerWidget> {
        const datePickerWidget: DatePickerWidget = {
            startDate: "2020-05-05",
            endDate: "2021-05-05",
            selectedDate: order.products[0].option.startDate ? order.products[0].option.startDate : "2020-05-25",
            canChangeStartDate: true,
            title: "Starting Date",
            widgetType: "DATE_PICKER_WIDGET",
            showDivider: false,
            action: {
                actionType: "CARE_CHANGE_ORDER_PRODUCT_OPTION",
                meta: {
                    productId: order.products[0].productId,
                    optionMeta: {
                        startDate: order.products[0].option.startDate ? order.products[0].option.startDate : "2020-05-25"
                    }
                }
            }
        }
        return datePickerWidget
    }

    private async getStartDateSelectionWidget(userContext: UserContext, startDate: string, endDate: string, selectedDate: string, canChangeStartDate: boolean, isRenewFlow: boolean): Promise<DatePickerWidget> {
        const datePickerWidget: DatePickerWidget = {
            startDate: startDate,
            endDate: endDate,
            selectedDate: selectedDate,
            canChangeStartDate: canChangeStartDate,
            title: "Pick a start date",
            selectedDateText: isRenewFlow ? `Renewal from ${TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, TimeUtil.parseDate(selectedDate, userContext.userProfile.timezone), "DD MMM")}` : undefined,
            calloutText: selectedDate && !isRenewFlow ? "Subscription renews automatically every month from your start date." : undefined,
            widgetType: "DATE_PICKER_WIDGET"
        }
        return datePickerWidget
    }

    private async getCenterSelectionWidget(productType: ProductType, canChangeCenter: boolean, preferredCenter: CultCenter): Promise<CenterSelectionWidget> {
        const action: SelectCenterAction = {
            title: "Pick a center",
            showHelp: true,
            showFavourite: false,
            actionType: "SELECT_CENTER",
            productType: productType,
            meta: {
                showAvailableWorkouts: true
            }
        }
        const centerSelectionWidget: CenterSelectionWidget = {
            title: "Pick a preferred centre",
            canChangeCenter: canChangeCenter,
            preferredCenterId: preferredCenter ? preferredCenter.id : undefined,
            preferredCenterName: preferredCenter ? preferredCenter.name : undefined,
            widgetType: "CENTER_PICKER_WIDGET",
            action: action
        }
        return centerSelectionWidget
    }

    private async getLivePTRenewalNoteWidget(userContext: UserContext): Promise<NoteListWidget> {
        return {
            widgetType: "NOTE_LIST_WIDGET",
            note: {
                title: "NOTE",
                color: "#000000"
            },
            data: [{
                icon: undefined,
                info: "As you are renewing, you will be able to continue with your previous pack trainers."
            }]
        }
    }

    async getChronicCarePackCheckoutWidget(bundleProduct: DiagnosticProduct): Promise<ChronicCarePackCheckoutWidget> {
        const gradientColor: string[] = ["#FFFFFF", "#F9CF87", "#FFFFFF", "#FCBA4A", "#FFFFFF", "#FFFFFF"]
        const appHeading: string = bundleProduct?.productId?.startsWith("ULTRAFIT") ? "ultra.fit" : "sugar.fit"
        const valueType: string = _.get(bundleProduct, "infoSection.packDurationMonths", 1) == 1 ? "MONTH" : "MONTHS"
        const widgetItem: ChronicCarePackCheckoutWidget = {
            widgetType: "PACK_PURCHASE_CART_CARD" as any,
            value: _.get(bundleProduct, "infoSection.packDurationMonths", 4),
            valueType: valueType,
            subHeading: "Renewal Pack",
            heading: appHeading,
            price: `${RUPEE_SYMBOL} ${bundleProduct.price.listingPrice}`,
            packType: _.get(bundleProduct, "infoSection.packType", "PRO"),
            gradientColor: _.get(bundleProduct, "infoSection.renderingInfo.gradientColor", gradientColor),
            renderingInfo: _.get(bundleProduct, "infoSection.renderingInfo", null)
        }
        const widget: ChronicCarePackCheckoutWidget = widgetItem
        return widget
    }

    async getChronicCareCGMCheckoutWidget(bundleProduct: CGMProduct): Promise<ChronicCareCGMCheckoutWidget> {
        const color = _.get(bundleProduct, "infoSection.appRenderingInfo.color", ["#11908C", "#4BCECC", "#0C9792"])
        const cgmCount = _.get(bundleProduct, "infoSection.appRenderingInfo.cgmCount", "")
        const cgmText = _.get(bundleProduct, "infoSection.appRenderingInfo.cgmText", "CGM")
        const subTitle = _.get(bundleProduct, "infoSection.appRenderingInfo.subTitle", "")
        const widgetItem: ChronicCareCGMCheckoutWidget = {
            widgetType: "CGM_PURCHASE_CART_CARD" as any,
            cgmCount: cgmCount,
            cgmText: cgmText,
            subTitle: subTitle,
            heading: "Your Order",
            mrp: `${RUPEE_SYMBOL} ${bundleProduct.price.mrp}`,
            listingPrice: `${RUPEE_SYMBOL} ${bundleProduct.price.listingPrice}`,
            gradientColor: color,
        }
        const widget: ChronicCareCGMCheckoutWidget = widgetItem
        return widget
    }

    async getSfEComCartAddressWidget(deliveryEta: Date, order: Order, billingInfo: BillingInfo): Promise<SfEComCartAddressWidget> {
        const productCodesInCart = _.map(order.products, p => p.productId)
        const deliveryEtaTo = new Date(deliveryEta)
        const delayedDeliveryDate = new Date(1714745744000)
        deliveryEtaTo.setDate(deliveryEtaTo.getDate() + 4)
        const discountPrices = OrderUtilV1.getDiscountPriceDetails(billingInfo)
        const widgetItem: SfEComCartAddressWidget = {
            widgetType: "SF_ECOM_CART_ADDRESS_WIDGET" as any,
            deliveryByTitle: "Delivery by",
            deliveryEtaFrom: deliveryEta,
            deliveryEtaTo: null,
            discountAmount: _.find(discountPrices, (price: PriceComponent) => price.title == "Price Discount")?.value
        }
        const widget: SfEComCartAddressWidget = widgetItem
        return widget
    }

    async getSfDiagnosticCartAddressWidget(): Promise<SfDiagnosticCartAddressWidget> {
        const widgetItem: SfDiagnosticCartAddressWidget = {
            widgetType: "SF_DIAGNOSTIC_CART_ADDRESS_WIDGET" as any
        }
        const widget: SfDiagnosticCartAddressWidget = widgetItem
        return widget
    }

    async getSfEcomDelayedDeliveryBannerCarouselWidget(order: Order) {
        const productCodesInCart = _.map(order.products, p => p.productId)
        if (productCodesInCart.includes("SMART_SCALE")) {
            const width = 315
            const height = 95
            const imageUrl = "image/chroniccare/marketing/banners/ecommerce/smart-scale-delay.png"
            const action: any = undefined
            return new BannerCarouselWidget(`${width}:${height}`, [{
                id: imageUrl,
                image: imageUrl,
                action,
            }], {
                bannerHeight: height,
                bannerWidth: width,
                verticalPadding: 10,
                roundedCorners: true,
            }, 1, false, false, false)
        }
        return undefined
    }

    async getSfEComCartListWidget(userContext: UserContext, order: Order,  billingInfo: BillingInfo, indusShipments: IndusOrderShipments[], couponData: UserOfferResponseV2): Promise<SfEComCartListWidget> {
        const ecomConfig: {
            couponConfig: {
                couponCode: string
                minimumOrderValue: number
            };
            minimumCartValueForFreeDelivery: number;
          } = JSON.parse(this.appConfigStore.getConfig("SF_ECOM_CONFIG", "{}"))
        const minimumCartValueForFreeDelivery = _.isNumber(ecomConfig?.minimumCartValueForFreeDelivery) ? ecomConfig?.minimumCartValueForFreeDelivery : 900
        const isSugarfitStoreTabSupportedUser = await AppUtil.isSugarfitStoreTabSupportedUser(userContext, this.segmentationCacheClient)
        const productCodesInCart = _.map(order.products, p => p.productId)
        let containsSmartScale = false
        if (productCodesInCart.includes("SMART_SCALE")) {
            containsSmartScale = true
        }
        const totalMaxDeliveryChargeApplicable = indusShipments.reduce((sum, shipment) => {
            return sum + (shipment.maxDeliveryCharge || 0)
        }, 0)
        const showTaxes = false
        const listingBrand: ListingBrandIdType = EatUtil.getListingBrandFromOrder(order)
        const offersApplied = !_.isEmpty(order.offersApplied) ? await this.offerServiceV3.getOffersByIds(order.offersApplied) : {}
        const priceDetails = this.getPriceDetailsForSugarfit(userContext, {
            ...billingInfo,
            orderDiscountsWithTitle: offersApplied
        }, _.map(offersApplied), listingBrand, totalMaxDeliveryChargeApplicable, order.deliveryCharges, order.packagingCharges, true, false, false, undefined, undefined, showTaxes)
        const indusProducts: ProductResponse[] = await Promise.all(_.map(order.products, async p => {
            return await this.indusService.searchProductWithCode(p.productId)
        }))
        const indusProductsByProductCode = _.keyBy(indusProducts, "productEntry.productCode")
        const shipmentsWithFreeDelivery = _.filter(indusShipments, shipment => {
            return shipment.applicableDeliveryCharge === 0
        })
        let totalPayableOfshipmentsWithFreeDelivery = 0
        _.forEach(shipmentsWithFreeDelivery, shipment => {
            const totalPayableOfShipment = shipment.orderProducts.reduce((sum, o) => {
                const givenProduct = indusProductsByProductCode[o.productCode]
                return sum + ((givenProduct?.productEntry?.listingPrice * o.quantity) || 0)
            }, 0)
            totalPayableOfshipmentsWithFreeDelivery += totalPayableOfShipment
        })
        const totalPayableOfshipmentsWithoutFreeDelivery = priceDetails.usersCartValueAfterPriceDiscount - totalPayableOfshipmentsWithFreeDelivery

        let couponMessage = couponData?.message
        let isCouponValid = couponData?.isValid
        if (ecomConfig && ecomConfig.couponConfig && ecomConfig.couponConfig.couponCode
            && ecomConfig.couponConfig.couponCode.toLowerCase() === couponData?.couponCode?.toLowerCase()
            && priceDetails.usersCartValueAfterPriceDiscount < ecomConfig.couponConfig.minimumOrderValue) {
            couponMessage = `Applicable on minimum order value of ₹${ecomConfig.couponConfig.minimumOrderValue}`
            isCouponValid = false
        }
        const widgetItem: SfEComCartListWidget = {
            widgetType: "SF_ECOM_CART_LIST_WIDGET" as any,
            couponData: {
                couponOptionEnabled: true,
                appliedCouponId: couponData?.couponCode,
                couponMessage,
                isValid: isCouponValid,
                couponCode: couponData?.couponCode,
                isEligible: couponData?.isEligible,
                message: couponData?.message,
                description: couponData?.description,
            },
            cartShipments: _.map(indusShipments, shipment => {
                return {
                    deliveryByTitle: "Delivery by",
                    deliveryEtaFrom: shipment.edd,
                    deliveryEtaTo: undefined,
                    deliveryChargePrice: {
                        price: shipment.applicableDeliveryCharge,
                        currency: "INR"
                    },
                    freeDeliveryMessage: "FREE DELIVERY 🥳",
                    moreAmountToBuyForFreeDelivery: shipment.applicableDeliveryCharge > 0 && minimumCartValueForFreeDelivery > totalPayableOfshipmentsWithoutFreeDelivery
                    ? minimumCartValueForFreeDelivery - totalPayableOfshipmentsWithoutFreeDelivery : 0,
                    shipmentProducts: _.map(shipment.orderProducts, sp => {
                        const ip = indusProductsByProductCode[sp.productCode]
                        return {
                            product: {
                                title: _.get(ip, "productEntry.title"),
                                images: _.get(ip, "productEntry.imageUrls"),
                                productCode: _.get(ip, "productEntry.productCode"),
                                unitQuantity: _.get(ip, "productEntry.unitQuantity"),
                                unit: _.get(ip, "productEntry.unit"),
                                displayUnitQuantity: _.get(ip, "productEntry.unitQuantity"),
                                displayUnit: _.get(ip, "productEntry.unit"),
                                priceDetails: {
                                    mrp: _.get(ip, "productEntry.mrp"),
                                    listingPrice: _.get(ip, "productEntry.listingPrice"),
                                    currency: "INR",
                                },
                            },
                            pdpDeeplink: `curefit://sfecommercepdp?productId=${_.get(ip, "productEntry.productCode")}`,
                            orderQuantity: sp.quantity,
                            isCouponApplicable: (couponData && couponData?.isValid && couponData?.isEligible && couponData?.eligibleProductCodes.includes(ip?.productEntry?.productCode)) || undefined
                        }
                    })
                }}),
            cartItems: _.map(indusProducts, ip => {
                const cartItemWidget: any = {
                    widgetType: "SF_ECOM_CART_LIST_ITEM_WIDGET",
                    product: {
                        title: _.get(ip, "productEntry.title"),
                        images: _.get(ip, "productEntry.imageUrls"),
                        productCode: _.get(ip, "productEntry.productCode"),
                        unitQuantity: _.get(ip, "productEntry.unitQuantity"),
                        unit: _.get(ip, "productEntry.unit"),
                        displayUnitQuantity: _.get(ip, "productEntry.unitQuantity"),
                        displayUnit: _.get(ip, "productEntry.unit"),
                        priceDetails: {
                            mrp: _.get(ip, "productEntry.mrp"),
                            listingPrice: _.get(ip, "productEntry.listingPrice"),
                            currency: "INR",
                        },
                    },
                    noReturnText: "",
                    orderQuantity: _.find(order.products, p => {
                        return (ip?.productEntry?.productCode === p.productId)
                    })?.quantity || 1
                }
                if (couponData && couponData?.isValid && couponData?.isEligible
                    && !couponData?.eligibleProductCodes.includes(ip?.productEntry?.productCode)) {
                    cartItemWidget.nonEligibleCouponMessage = `${couponData?.couponCode} coupon is not valid for this product`
                }
                if (couponData && couponData?.isValid && couponData?.isEligible
                    && couponData?.eligibleProductCodes.includes(ip?.productEntry?.productCode)) {
                    cartItemWidget.isCouponApplicable = true
                }
                return cartItemWidget
            }),
            note: "Please check expiry of your products before use.",
            priceDetails: priceDetails.components,
            finalPrice: {
                listingPrice: _.round(billingInfo.total, 2),
                mrp: _.round(billingInfo.total, 2),
                currency: billingInfo.currency
            },
            freeDeliveryOverrideMessage: priceDetails.freeDeliveryOverrideMessage,
            fulfilledByText: "",
            addMoreEnabled: isSugarfitStoreTabSupportedUser ? true : !containsSmartScale,
        }
        const widget: SfEComCartListWidget = widgetItem
        return widget
    }

    async getSfDiagnosticCartListWidget(order: Order): Promise<SfDiagnosticCartListWidget> {
        const diagnosticProducts: Product[] = await Promise.all(_.map(order.products, async p => {
            return await this.catalogueServicePMS.getCatalogProduct(p.productId)
        }))
        const diagnosticProductsByProductCode = _.keyBy(diagnosticProducts, "productId")
        const widgetItem: SfDiagnosticCartListWidget = {
            widgetType: "SF_DIAGNOSTIC_CART_LIST_WIDGET" as any,
            cartShipments: {
                    startDate: order.products[0]?.option?.atHomeTestRequest?.startTime,
                    endDate: order.products[0]?.option?.atHomeTestRequest?.endTime,
                    sampleCollectionTitle: "Sample Collection",
                    shipmentProducts: _.map(order.products, p => {
                        const ip = diagnosticProductsByProductCode[p.productId]
                        return {
                            product: {
                                title: _.get(ip, "title"),
                                imgUrl: _.get(ip, "imageUrl"),
                                productCode: _.get(ip, "productId"),
                                priceDetails: {
                                    mrp: _.get(ip, "price.mrp"),
                                    listingPrice: _.get(ip, "price.listingPrice"),
                                    currency: "INR",
                                },
                            },
                            pdpDeeplink: `curefit://sfdiagnosticstdp?productId=${_.get(ip, "productId")}`,
                            orderQuantity: p.quantity,
                        }
                    })
                }
        }
        const widget: SfDiagnosticCartListWidget = widgetItem
        return widget
    }

    async getSfEComCouponWidget(userContext: UserContext, order: Order, billingInfo: BillingInfo, indusShipments: IndusOrderShipments[], couponData: UserOfferResponseV2): Promise<SfEComCartCouponWidget> {
        const shippingCharge = order?.deliveryCharges?.total || 70
        const totalMaxDeliveryCharge = indusShipments.reduce((sum, shipment) => {
            return sum + (shipment.maxDeliveryCharge || 0)
        }, 0)
        const showTaxes = false
        const listingBrand: ListingBrandIdType = EatUtil.getListingBrandFromOrder(order)
        const offersApplied = !_.isEmpty(order.offersApplied) ? await this.offerServiceV3.getOffersByIds(order.offersApplied) : {}
        const priceDetails = this.getPriceDetailsForSugarfit(userContext, {
            ...billingInfo,
            orderDiscountsWithTitle: offersApplied
        }, _.map(offersApplied), listingBrand, totalMaxDeliveryCharge, order.deliveryCharges, order.packagingCharges, true, false, false, undefined, undefined, showTaxes)
        const ecomConfig: {
            couponConfig: {
              couponCode: string
              minimumOrderValue: number
            };
          } = JSON.parse(this.appConfigStore.getConfig("SF_ECOM_CONFIG", "{}"))

        let couponMessage = couponData?.message
        let isCouponValid = couponData?.isValid
        if (ecomConfig && ecomConfig.couponConfig && ecomConfig.couponConfig.couponCode
            && ecomConfig.couponConfig.couponCode.toLowerCase() === couponData?.couponCode?.toLowerCase()
            && priceDetails.usersCartValueAfterPriceDiscount < ecomConfig.couponConfig.minimumOrderValue) {
                couponMessage = `Applicable on minimum order value of ₹${ecomConfig.couponConfig.minimumOrderValue}`
                isCouponValid = false
        }
        const cartDiscountIndex = _.findIndex(priceDetails?.components, c => c.title.toLowerCase().includes("cart"))
        let savedValue = undefined
        if (cartDiscountIndex > -1) {
            savedValue = `${priceDetails?.components[cartDiscountIndex].symbol}${priceDetails?.components[cartDiscountIndex].value}`
        }
        const orderedProductsList: SfEcomCarCouponOrderProduct[] = []
        _.map(order.products, p => {
            orderedProductsList.push({
                productCode: p.productId,
                quantity: p.quantity,
                price: p.price?.listingPrice || p.price?.sellingPrice || p.price?.mrp,
            })
        })
        const widgetItem: SfEComCartCouponWidget = {
            widgetType: "SF_ECOM_CART_COUPON_WIDGET" as any,
            title: "Offers & Coupons",
            couponData: {
                couponOptionEnabled: true,
                appliedCouponId: couponData?.couponCode,
                couponMessage,
                isValid: isCouponValid,
                savingsAmount: savedValue,
                couponCode: couponData?.couponCode,
                isEligible: couponData?.isEligible,
                message: couponData?.message,
                description: couponData?.description,
            },
            couponRequestPayload: {
                userId: Number(userContext.userProfile.userId),
                totalCartValue: billingInfo.total,
                shippingCharge: shippingCharge,
                orderedProductsList,
                source: "APP"
            }
        }
        const widget: SfEComCartCouponWidget = widgetItem
        return widget
    }

    async getSfEComBillingWidget(userContext: UserContext, order: Order, billingInfo: BillingInfo, indusShipments: IndusOrderShipments[]): Promise<SfEComCartBillingWidget> {
        const productCodesInCart = _.map(order.products, p => p.productId)
        let containsSmartScale = false
        if (productCodesInCart.includes("SMART_SCALE")) {
            containsSmartScale = true
        }
        const totalMaxDeliveryChargeApplicable = indusShipments.reduce((sum, shipment) => {
            return sum + (shipment.maxDeliveryCharge || 0)
        }, 0)
        const showTaxes = false
        const listingBrand: ListingBrandIdType = EatUtil.getListingBrandFromOrder(order)
        const offersApplied = !_.isEmpty(order.offersApplied) ? await this.offerServiceV3.getOffersByIds(order.offersApplied) : {}
        const priceDetails = this.getPriceDetailsForSugarfit(userContext, {
            ...billingInfo,
            orderDiscountsWithTitle: offersApplied
        }, _.map(offersApplied), listingBrand, totalMaxDeliveryChargeApplicable, order.deliveryCharges, order.packagingCharges, true, false, false, undefined, undefined, showTaxes)

        const deliveryChargeForAnalytics = order.deliveryCharges?.total || 0
        const analyticsData = {
            mrp: billingInfo?.mrp,
            discount: billingInfo?.discount,
            deliveryCharge: deliveryChargeForAnalytics,
            amountPayable: billingInfo?.amountPayable + deliveryChargeForAnalytics,
        }
        const widgetItem: SfEComCartBillingWidget = {
            widgetType: "SF_ECOM_CART_BILLING_WIDGET" as any,
            priceDetails: priceDetails.components,
            finalPrice: {
                listingPrice: _.round(billingInfo.total, 2),
                mrp: _.round(billingInfo.total, 2),
                currency: billingInfo.currency
            },
            freeDeliveryOverrideMessage: priceDetails.freeDeliveryOverrideMessage,
            moreAmountToBuyForFreeDelivery: priceDetails.moreAmountToBuyForFreeDelivery,
            fulfilledByText: "",
            shippingPolicyLink: "https://www.sugarfit.com/activefoods/shipping-policy",
            refundPolicyLink: "https://www.sugarfit.com/activefoods/refund-policy",
            policyMessage: "Please make sure that address & order details are correct. Read the policies carefully.",
            analyticsData,
        }
        const widget: SfEComCartBillingWidget = widgetItem
        return widget
    }

    async getSfDiagnosticsBillingWidget(userContext: UserContext, order: Order, billingInfo: BillingInfo): Promise<SfEComCartBillingWidget> {
        const showTaxes = false
        const listingBrand: ListingBrandIdType = EatUtil.getListingBrandFromOrder(order)
        const offersApplied = !_.isEmpty(order.offersApplied) ? await this.offerServiceV3.getOffersByIds(order.offersApplied) : {}
        const careHomeCollectionCharges: CareHomeCollectionCharges = {}
        if (order.collectionCharges?.total >= 0) {
            careHomeCollectionCharges.showHomeSampleCharges = true
            careHomeCollectionCharges.orderHomeSampleCharges = order.collectionCharges?.total
        }
        const priceDetails = this.getPriceDetailsForSugarfit(userContext, {
            ...billingInfo,
            orderDiscountsWithTitle: offersApplied
        }, _.map(offersApplied), listingBrand, 0, order.deliveryCharges, order.packagingCharges, false, false, false, undefined, undefined, showTaxes, undefined, false, careHomeCollectionCharges)

        const deliveryChargeForAnalytics = order.deliveryCharges?.total || 0
        const analyticsData = {
            mrp: billingInfo?.mrp,
            discount: billingInfo?.discount,
            deliveryCharge: deliveryChargeForAnalytics,
            amountPayable: billingInfo?.amountPayable + deliveryChargeForAnalytics,
        }
        const widgetItem: SfEComCartBillingWidget = {
            widgetType: "SF_ECOM_CART_BILLING_WIDGET" as any,
            priceDetails: priceDetails.components,
            finalPrice: {
                listingPrice: _.round(billingInfo.total, 2),
                mrp: _.round(billingInfo.total, 2),
                currency: billingInfo.currency
            },
            freeDeliveryOverrideMessage: priceDetails.freeDeliveryOverrideMessage,
            moreAmountToBuyForFreeDelivery: priceDetails.moreAmountToBuyForFreeDelivery,
            fulfilledByText: "",
            shippingPolicyLink: "https://www.sugarfit.com/activefoods/shipping-policy",
            refundPolicyLink: "https://www.sugarfit.com/activefoods/refund-policy",
            policyMessage: "Please make sure that address & order details are correct. Read the policies carefully.",
            analyticsData,
        }
        const widget: SfEComCartBillingWidget = widgetItem
        return widget
    }

    async getSfSeperatorWidget(): Promise<SfWidgetSeparator> {
        const widgetItem: SfWidgetSeparator = {
            widgetType: "SF_WIDGET_SEPARATOR" as any,
            gradientColors: ["#EEEFEF", "#EEEFEF"]
        }
        const widget: SfWidgetSeparator = widgetItem
        return widget
    }

    async getSfEComPeopleAlsoBoughtWidget(userContext: UserContext, order: Order): Promise<SfEComPeopleBoughtWidget> {
        const isSugarfitStoreTabSupportedUser = await AppUtil.isSugarfitStoreTabSupportedUser(userContext, this.segmentationCacheClient)
        const productCodesInCart = _.map(order.products, p => p.productId)
        if (productCodesInCart.includes("SMART_SCALE") && !isSugarfitStoreTabSupportedUser) {
            return null
        }
        const allProducts = await this.indusService.searchWithAvailability({ productCodes: [] })
        const unavailableProductCodes: string[] = []
        if (allProducts) {
            const productPromises = _.map(allProducts, async inv => {
                if (inv && inv.maxAvailableQuantity <= 0) {
                    unavailableProductCodes.push(inv.productEntry?.productCode)
                }
            })
            await Promise.all(productPromises)
        }

        const yetToBuyProducts: IndusProduct[] = []
        const allowedProducts = AppUtil.isSfJuiceSupported(userContext) ? ["AATA_1_KG", "AATA_5_KG", "RICE_1_KG", "RICE_5_KG", "ACV_500_ML", "JUICE_1_L"] : ["AATA_1_KG", "AATA_5_KG", "RICE_1_KG", "RICE_5_KG"]
        if (productCodesInCart.includes("AATA_5_KG")) {
            const i = allowedProducts.indexOf("AATA_1_KG")
            if (i > -1) {
                allowedProducts.splice(i, 1)
            }
        }
        if (productCodesInCart.includes("RICE_5_KG")) {
            const i = allowedProducts.indexOf("RICE_1_KG")
            if (i > -1) {
                allowedProducts.splice(i, 1)
            }
        }
        _.map(allProducts, p => {
            if (allowedProducts.includes(p?.productEntry?.productCode) && !productCodesInCart.includes(p?.productEntry?.productCode) && !unavailableProductCodes.includes(p?.productEntry?.productCode)) {
                yetToBuyProducts.push(p?.productEntry)
            }
        })
        if (!_.isEmpty(yetToBuyProducts)) {
            const yetToBuyProductWidgets: SfEComProductItemSmallWidget[] = _.map(yetToBuyProducts, p => {
                const ecomProduct = {
                    productCode: p.productCode,
                    category: p.category,
                    title: p.title,
                    images: p.imageUrls,
                    unitQuantity: p.unitQuantity,
                    unit: p.unit,
                    displayUnitQuantity: p.unitQuantity,
                    displayUnit: p.unit,
                    priceDetails: {
                        listingPrice: p.listingPrice,
                        mrp: p.mrp,
                        currency: "INR",
                    },
                    isAvailable: true,
                    maxOrderQuantity: 10,
                    availableQuantity: 10,
                }
                return {
                    widgetType: "SF_ECOM_PRODUCT_ITEM_SMALL_WIDGET" as any,
                    product: ecomProduct,
                    cartAction: {
                        actionType: "SF_ADD_TO_CART_VIA_MODAL",
                        meta: {
                            product: ecomProduct,
                            quantity: 1
                        }
                    }
                }
            })
            return {
                widgetType: "SF_ECOM_PEOPLE_BOUGHT_LIST_WIDGET" as any,
                title: "People also bought",
                widgets: yetToBuyProductWidgets,
            }
        }
        return null
    }

    doesPhoneNumberExist(user: User): boolean {
        if (user && !_.isNil(user.phone)) {
            return true
        }
        return false
    }

    isPlusPack(pack: OfflineFitnessPack) {
        return pack.product.productSubType === ProductSubType.PLUS || pack.product.productSubType === ProductSubType.ADDONBUNDLE

    }

    isLitePack(pack: OfflineFitnessPack) {
        return pack.product.productSubType === ProductSubType.LITE

    }

    async validateSfInventory(orderProducts: OrderProduct[]): Promise<AlertInfo> {
        const inventoryRequest = {
            productCodes: _.map(orderProducts, op => {
                return op.productId
            })
        }
        const inventoryAvailabilityList = await this.indusService.searchWithAvailability(inventoryRequest)
        if (inventoryAvailabilityList) {
            const unavailableItemNames: string[] = []
            const productPromises = _.map(inventoryAvailabilityList, async inv => {
                if (inv && inv.maxAvailableQuantity <= 0) {
                    unavailableItemNames.push(inv?.productEntry?.title)
                }
            })
            await Promise.all(productPromises)
            if (unavailableItemNames.length > 0) {
                const commaSeperatedNames = unavailableItemNames.join(", ")
                return {
                    title: "Some item/s unavailable!",
                    subTitle: `${commaSeperatedNames} ${unavailableItemNames.length > 1 ? "have" : "has"} gone out of stock. Please remove it from your cart.`,
                    actions: [{title: "Ok", actionType: "HIDE_ALERT_MODAL"}]
                }
            }
        }
    }

    async validateSfServiceability(): Promise<AlertInfo> {
        return {
            title: "No service at this address!",
            subTitle: "Some items are not yet available at the address you’ve picked. Please try with a different address.",
            actions: [{title: "Ok", actionType: "HIDE_ALERT_MODAL"}]
        }
    }

    async getSfAddAddressAlert(): Promise<AlertInfo> {
        return {
            title: "Address is missing!",
            subTitle: "Kindly add your delivery address to proceed further.",
            actions: [{title: "Ok", actionType: "HIDE_ALERT_MODAL"}]
        }
    }

    public static getTaxesAndFeeBottomSheetAction(billingInfo?: BillingInfo, platformFee?: string, platformFeeTax?: string, totalPlatformFeeIncTax?: number, boosterPackTax?: number): Action {
        const meta: any  = {}
        const widgets: any = [
            {
                widgetType: "TAX_AND_FEE_DETAILS_WIDGET",
                hasDivideBelow: false,
                layoutProps: {
                  spacing: {top: "40", bottom: "40"}
                },
                title: platformFee ? "Taxes & Fees" : "Platform fee",
                taxAndFeeItemList: platformFee ? [
                  {
                    title: billingInfo.tax.type + "@" + billingInfo.tax.percentage + "%",
                    value: `₹${billingInfo.tax.amount.toFixed(2)}`,
                  },
                  boosterPackTax ? {
                    title: "GST on Booster pack",
                    value: `₹${boosterPackTax}`
                  } : null,
                  {
                    title: "Platform Fee",
                    value: `₹${platformFee}`
                  },
                  {
                    title: "GST on Platform fee ",
                    value: `₹${platformFeeTax}`
                  },
                  {
                    title: "Total",
                    value: `₹${(totalPlatformFeeIncTax + billingInfo.tax.amount + (boosterPackTax ? boosterPackTax : 0)).toFixed(2)}`,
                    isTotal: true
                }
                ] : null,
                description:
                    "The small platform fee contributes to a seamless and integrated fitness journey, ensuring the best possible experience backed by the latest technology"
              },
             {
               widgetType: "ACTION_LIST_WIDGET",
               hasDivideBelow: false,
               layoutProps: {
                 spacing: {top: 0, bottom: 40}
               },
               actionList: [
                 {
                   title: platformFee ? "OKAY, GOT IT" : "CLOSE",
                   actionType: "POP_NAVIGATION",
                   variant: "secondary"
                 }
               ]
             }]
        meta.showTopNotch = true
        meta.widgets = widgets
        return {
            actionType: "SHOW_CUSTOM_BOTTOM_SHEET",
            analyticsData: {
                "modal": "TAXES_AND_FEE_BOTTOM_SHEET",
                "gst": (billingInfo?.tax?.amount ?? 0).toFixed(2),
                "platformfee": platformFee,
                "platformfeetax": platformFeeTax,
                "totalfee": (totalPlatformFeeIncTax + (billingInfo?.tax?.amount ?? 0)).toFixed(2)
            },
            meta,
        }
    }

}

export default CartViewBuilder
