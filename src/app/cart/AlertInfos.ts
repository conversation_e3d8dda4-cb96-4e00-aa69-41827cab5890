import { AlertInfo } from "../common/views/WidgetView"
import * as _ from "lodash"
import { ClientDeliveryInfo, FoodMarketplaceVariantAddonInfo, OrderSource } from "@curefit/order-common"
import { FoodProduct as Product, MenuType, ListingBrandIdType } from "@curefit/eat-common"
import { AlertInfoStatus } from "@curefit/eat-api-client"
import { UserContext } from "@curefit/vm-models"
import AppUtil from "../util/AppUtil"
import { AlertStatus } from "@curefit/eat-api-common"

export interface ItemDetails {
    title: string,
    productId: string,
    parentProductId?: string,
    mealSlot?: { id: MenuType}
    addons?: FoodMarketplaceVariantAddonInfo[]
    variants?: FoodMarketplaceVariantAddonInfo[]
}

export interface AlertInfoParams {
    updateLog?: { title: string, subTitle: string }[]
    nonServiceableInfo?: ClientDeliveryInfo
    unAvailableProducts?: ItemDetails[]
    products?: Product[]
    listingBrand?: ListingBrandIdType,
    orderSource?: OrderSource
}

export class AlertInfos {
    public static AlertInfo(alertStatus: AlertInfoStatus | AlertStatus, alertInfoParams?: AlertInfoParams, userContext?: UserContext ): AlertInfo {
        // needed only for unavailability cases
        const redirectUrl =  AlertInfos.getRedirectUrl(alertInfoParams.orderSource, alertInfoParams.listingBrand)

        switch (alertStatus) {
            case "SOLD_OUT": {
                const alertInfo: AlertInfo = {
                    title: "Sold out",
                    subTitle: "All slots are unavailable at this location",
                    actions: [{
                        actionType: "HIDE_ALERT_MODAL",
                        title: "Ok",
                        url: redirectUrl
                    }]
                }
                return alertInfo
            }
            case "START_DATE_UPDATED": {
                const alertInfo: AlertInfo = {
                    title: "Start Date Updated",
                    subTitle: "The date you had selected is unavailable. We have updated your start date to the first available date",
                    actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
                }
                return alertInfo
            }
            case "SLOT_UNAVAILABLE": {
                const alertInfo: AlertInfo = {
                    title: "Slot Unavailable",
                        subTitle: "Orders for your selected slot are unavailable on this date. The next available slot has been selected",
                        actions: [{
                        actionType: "HIDE_ALERT_MODAL",
                        title: "Ok"
                    }]
                }
                return alertInfo
            }
            case "CART_UPDATED": {
                const updateLog = alertInfoParams.updateLog
                const alertInfo: AlertInfo = {
                    title: "Cart Updated",
                    subTitle: undefined,
                    listItems: updateLog,
                    actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
                }
                return alertInfo
            }
            case "ITEMS_UNAVAILABLE": {
                const products: Product[] = alertInfoParams.products
                const alertInfo: AlertInfo = {
                    title: "Items in cart not available",
                    subTitle: "Items have gone out of stock and removed from your cart.",
                    actions: AppUtil.isEatSoldOutCartItemRemovalSupported(userContext) ?
                        [{
                            actionType: products[0].isPack && AppUtil.isSubscriptionAttachSupported(userContext) ? "REMOVE_SOLD_OUT_PACK_CART_ITEMS" : "REMOVE_SOLD_OUT_CART_ITEMS",
                            title: "Ok",
                            meta: {
                                items: alertInfoParams.unAvailableProducts,
                                pageFrom: "cartcheckout"
                            },
                        }]
                        : [{
                            actionType: "HIDE_ALERT_MODAL",
                            title: "Ok",
                            url: redirectUrl
                        }],
                    unavailableItems: AppUtil.isWeb(userContext) ?  _.map(alertInfoParams.unAvailableProducts, unAvailableProduct => {
                        const productInfo = products.find(product => {
                            return product.productId === unAvailableProduct.productId
                        })
                        return productInfo.title
                    }) : undefined
                }
                return alertInfo
            }
            case "SOME_ITEMS_UNAVAILABLE": {
                const unAvailableProducts = alertInfoParams.unAvailableProducts
                const products: Product[] = alertInfoParams.products

                const alertInfo: AlertInfo = {
                    title: "Some items unavailable!",
                    subTitle: `These ${unAvailableProducts.length} items have gone out of stock, and will be removed from your cart: `,
                    unavailableItems: _.map(unAvailableProducts, unAvailableProduct => {
                        const productInfo = products.find(product => {
                            return product.productId === unAvailableProduct.productId
                        })
                        return productInfo.title
                    }),
                    actions: AppUtil.isEatSoldOutCartItemRemovalSupported(userContext) ?
                        [{
                            actionType: products[0].isPack && AppUtil.isSubscriptionAttachSupported(userContext) ? "REMOVE_SOLD_OUT_PACK_CART_ITEMS" : "REMOVE_SOLD_OUT_CART_ITEMS",
                            title: "Continue to cart",
                            meta: {
                                items: unAvailableProducts,
                                pageFrom: "cartcheckout"
                            },
                        }]
                        : [{
                            actionType: "HIDE_ALERT_MODAL",
                            title: "Continue to cart",
                            url: "curefit://cartcheckout"
                        }]
                }
                return alertInfo
            }
            case "OFFER_OVERIDDEN_ALERT": {
                const alertInfo: AlertInfo = {
                    title: "Cart Updated",
                    isInlineMessage: true,
                    subTitle: "Your cart has been updated as per the offer valid for you.",
                    actions: [{ title: "Ok", actionType: "HIDE_ALERT_MODAL" }]
                }
                return alertInfo
            }
            case "ADDRESS_OUT_OF_DELIVERY_AREA": {
                const alertInfo: AlertInfo = {
                    title: "No Service At This Address!",
                    subTitle: "Some item/s are not yet available at the address you’ve picked. Please try with a different address.",
                    actions: [
                        {
                            actionType: "CHANGE_ADDRESS",
                            title: "Pick another address",
                            url: "curefit://selectaddress?pageFrom=cart_checkout",
                            meta: {
                                listingBrand: alertInfoParams?.listingBrand,
                                isWeekendAddress: false,
                                mealSlot: "ALL"
                            }
                        }],
                }
                return alertInfo
            }
            case "NON_SERVICEABLE_ALERT": {
                const nonServiceableInfo = alertInfoParams?.nonServiceableInfo
                const alertInfo: AlertInfo = {
                    title: "No Service At This Address!",
                    subTitle: "Some items are not yet available at the address you’ve picked. Please try with a different address.",
                    actions: [
                        {
                            actionType: alertInfoParams?.listingBrand === "FOOD_MARKETPLACE" || nonServiceableInfo?.isWeekdayAddressServiceable === false ? "CHANGE_ADDRESS" : "CHANGE_WEEKEND_ADDRESS",
                            title: alertInfoParams?.listingBrand === "FOOD_MARKETPLACE" || nonServiceableInfo?.isWeekdayAddressServiceable === false ? "Pick another address" : "Pick another weekend address",
                            url: "curefit://selectaddress?pageFrom=cart_checkout",
                            meta: {
                                isWeekendAddress: alertInfoParams?.listingBrand === "FOOD_MARKETPLACE" ? false : nonServiceableInfo?.isWeekdayAddressServiceable === true,
                                mealSlot: nonServiceableInfo?.mealSlot
                            }
                        }],
                }
                return alertInfo
            }
            case "STORE_NOT_SERVICEABLE": {
                const alertInfo: AlertInfo = {
                    title: "Store closed",
                    subTitle: "Sorry for the inconvenience",
                    actions: [
                        {
                            actionType: "HIDE_ALERT_MODAL",
                            title: "Ok",
                            url: redirectUrl
                        }],
                }
                return alertInfo
            }
        }
    }

    public static getRedirectUrl(orderSource: OrderSource, listingBrand: ListingBrandIdType): string {
        if  ((orderSource === "CUREFIT_NEW_WEBSITE" || orderSource === "PHONEPE_CUREFIT_WHOLE") && listingBrand === "WHOLE_FIT") {
            return "curefit://wholefitclp"
        } else if (listingBrand === "FOOD_MARKETPLACE") {
            return "curefit://tabpage?pageId=foodmpoutletlist"
        } else {
            return "curefit://eatfitclp"
        }
    }
}
