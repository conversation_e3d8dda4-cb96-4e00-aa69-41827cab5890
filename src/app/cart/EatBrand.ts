import {
    CafeDeliverySlotWidget,
    DeliveryInstructionWidget,
    FCStoreWidget,
    IEatBrand,
    PageActionRequest,
    DeliveryInstructionWidgetV2,
    BillingWidget, AddonItem
} from "./IEatBrand"
import {
    CartListWidget,
    CartReviewView
} from "./CartViewBuilder"
import {
    Action,
    AlertInfo,
    DateWiseDeliverySlotAction,
    DeliverySlotAction,
    WidgetView,
    CafeDeliverySlotAction, MealAction
} from "../common/views/WidgetView"
import { PreferredLocation, UserContext } from "@curefit/userinfo-common"
import { CdnUtil, TimeUtil, Timezone } from "@curefit/util-common"
import {
    ClientDeliveryInfo,
    Order,
    CartReviewPayload,
    OrderSource,
    OrderProduct,
    FoodMarketplaceVariantAddonInfo,
    GearProductSnapshots,
    OrderProductOption,
    FoodMarketplaceProductWiseInfo,
    CartItemStatus
} from "@curefit/order-common"
import {
    CutleryInstruction, DeliveryInfo, DeliveryInstruction,
    DeliverySlot, FoodProduct, ListingBrandIdType, MealSlot,
    MenuType, SalesChannel, UserDeliveryAddress
} from "@curefit/eat-common"
import {
    ArrivalInfo, OrderCreate,
} from "@curefit/oms-api-client"
import * as _ from "lodash"
import { ActionUtil, MAX_CART_SIZE, MealUtil } from "@curefit/base-utils"
import { UserAgentType, StartEndTime, HourMin } from "@curefit/base-common"
import {
    FoodMarketPlaceVariantAddonItem,
    ImagePathBuilder, Product, ProductPrice, ProductType,
    RetailerImageCategory,
    UrlPathBuilder
} from "@curefit/product-common"
import EatUtil, { EatDeliveryInstructionList, EatContactInstructionList, Instruction } from "../util/EatUtil"
import { AlertInfoParams, AlertInfos, ItemDetails } from "./AlertInfos"
import { User } from "@curefit/user-common"
import { BaseWidget, IBaseWidget, IServiceInterfaces, WholefitCartOfferWidget } from "@curefit/vm-models"
import {
    AlertInfoStatus,
    BillingDetails, CartAddon,
    EatApiResponseInterface,
    OrderItemInfo,
    ReviewRequest
} from "@curefit/eat-api-client"
import AppUtil from "../util/AppUtil"
import { FulfilmentCenter } from "@curefit/marketplace-common"
import { ALL_MEAL_SLOTS } from "@curefit/eat"
import { SlotUtil } from "@curefit/eat-util"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { BillingInfo } from "@curefit/finance-common"
 import { EatAddressWidget } from "@curefit/vm-models/dist/src/models/widgets/EatAddressWidget"
import {
    ProductAvailability,
    ProductAvailabilityRequest
} from "@curefit/foodway-common"
import { PriceComponent } from "../order/OrderViewBuilder"
import { ErrorCodes } from "../error/ErrorCodes"
import { LatLong } from "@curefit/location-common"
import FoodMarketplaceUtil from "../util/FoodMarketplaceUtil"
import OffersUtil from "../util/OffersUtil"
import CareUtil from "../util/CareUtil"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import IUserBusiness from "../user/IUserBusiness"
import kernel from "../../config/ioc/ioc"
import { GenericError } from "@curefit/error-client"
import { WalletBalance } from "@curefit/fitcash-common"
import { is } from "bluebird"
import { OfferV2 } from "@curefit/offer-common"


export class Cafe extends IEatBrand {

    private getCartListWidget(userContext: UserContext, billingDetails: BillingDetails, orderItemsInfo: OrderItemInfo[], userAgent: UserAgentType, order: Order, mealSlot: MenuType) {
        let numItems = 0
        const images: string[] = []
        const actions: Action[] = []

        const orderProductOption = order.productSnapshots[0].option
        orderItemsInfo.forEach(orderItemInfo => {
            numItems = numItems + orderItemInfo.quantity
            const image = orderItemInfo.isPack ?
                UrlPathBuilder.getPackImagePath(orderItemInfo.productId, orderItemInfo.productType, "MAGAZINE", orderItemInfo.imageVersion, userAgent) :
                UrlPathBuilder.getSingleImagePath(orderItemInfo.parentProductId ? orderItemInfo.parentProductId : orderItemInfo.productId, orderItemInfo.productType, "THUMBNAIL", orderItemInfo.imageVersion)
            const seoParams = {
                productName: orderItemInfo.productName
            }
            const action: Action = {
                actionType: "NAVIGATION",
                url: ActionUtil.foodSingle(orderItemInfo.productId, orderProductOption.startDate,
                    mealSlot, false, undefined, userContext.sessionInfo.userAgent, seoParams),
            }
            images.push(image)
            actions.push(action)
        })
        const orderItems =  this.buildCartListWidgetPayload(orderItemsInfo, images, actions)
        return this.buildCartListWidget(userContext, order, billingDetails, orderItems)
    }

    public async build(interfaces: CFServiceInterfaces, request: ReviewRequest, userContext: UserContext): Promise<CartReviewView> {
        const widgetViewPromises: Promise<WidgetView>[] = []
        const user: User = (await userContext.userPromise)
        const deliveryInfo = request.cartReviewPayload.option.deliveryInfo
        const cartReviewPayload: CartReviewPayload = request.cartReviewPayload
        const preferredLocation = request.preferredLocation
        const kioskId = (<any>request.cartReviewPayload.orderProducts[0].option).kioskId
        const productList: {productId: string, availability: {left: number}}[]
            = await interfaces.cafeProductService.getProducts(kioskId)
        const availablityMap = _.keyBy(productList, p => p.productId)
        const area = await interfaces.deliveryAreaService.findAreaForKiosk(kioskId)
        const kioskTimezone = interfaces.deliveryAreaService.getTimeZoneForArea(area)
        const allProducts = cartReviewPayload.orderProducts.map(p => {
            return {productId: p.productId, quantity: p.quantity}
        })
        const productMap: {[productId: string]: FoodProduct} = await interfaces.catalogueService.getProductMap(allProducts.map(p => p.productId))
        let total = 0
        let mrp = 0
        allProducts.map(p => {
            total += (productMap[p.productId].price.listingPrice) * p.quantity
            mrp += (productMap[p.productId].price.mrp) * p.quantity
        })
        const billingDetails: BillingDetails = {
            orderTotal: total,
            billingInfo: {
                mrp: mrp,
                total: total,
                discount: mrp - total,
                amountPayable: total,
                orderDiscount: 0,
                unitPrice: total,
                currency: productMap[allProducts[0].productId].price.currency,
            },
            extraChargeConfig: undefined,
            offersApplied: undefined
        }
        const availabilities = allProducts.map(p => {
            return {
                productId: p.productId,
                status: (availablityMap[p.productId].availability.left > 0) ? <CartItemStatus>"AVAILABLE" : <CartItemStatus>"OUT_OF_STOCK",
                available: availablityMap[p.productId].availability.left
            }
        })
        const orderItemsInfo: OrderItemInfo[] = allProducts.map(p => {
            const product = productMap[p.productId]
            return {
                isVeg: product.isVeg,
                productId: product.productId,
                quantity: p.quantity,
                calories: undefined,
                price: product.price,
                productName: product.title,
                imageVersion: product.imageVersion,
                productType: product.productType,
                isPack: false,
                productNameWithoutUnits: product.titleWithoutUnits,
                option: {
                    startDate: TimeUtil.todaysDate(kioskTimezone),
                    bookingId: cartReviewPayload.option.cultBookingNumber,
                    kioskId: kioskId
                },
            }
        })
        const order: Order = {
            userId: user.id,
            orderId: null,
            totalPayable: total,
            totalAmountPayable: total,
            totalFitCashPayable: 0,
            createdDate: new Date(),
            products: allProducts.map(p => {return {
                productId: p.productId,
                quantity: p.quantity,
                option: {
                    startDate: TimeUtil.todaysDate(kioskTimezone),
                    bookingId: cartReviewPayload.option.cultBookingNumber
                }
            }}),
            productSnapshots: allProducts.map(p => {
                const product = productMap[p.productId]
                return {
                    ...product,
                    quantity: p.quantity,
                    option: {
                        startDate: TimeUtil.todaysDate(kioskTimezone),
                        bookingId: cartReviewPayload.option.cultBookingNumber,
                        kioskId: kioskId
                    }
                }
            }),
            statusHistory: []
        }

        const cartListWidgetPromise = this.getCartListWidget(userContext, billingDetails, orderItemsInfo, userContext.sessionInfo.userAgent, order, "ALL")

        interfaces.logger.info("order.productSnapshots[0].isPack : " + order.productSnapshots[0].isPack)
        const addressWidgetPromise = this.buildAddressWidget(userContext, preferredLocation.address, undefined, false, order.productSnapshots[0].isPack, "ALL")
        widgetViewPromises.push(addressWidgetPromise)

        widgetViewPromises.push(cartListWidgetPromise)
        const getBillingWidgetPromise = this.buildBillingWidget(userContext, order, billingDetails)
        widgetViewPromises.push(getBillingWidgetPromise)
        const widgets = await Promise.all(widgetViewPromises)
        const numProducts = _.sum(order.productSnapshots.map(p => { return p.quantity }))

        const pageActionRequest: PageActionRequest = {
            userContext: userContext,
            order: order,
            numProducts: numProducts,
            total: billingDetails.billingInfo.total,
            deliveryInfo: deliveryInfo,
            isCafe: true,
            billingDetails: billingDetails,
            currency: billingDetails.billingInfo.currency,
        }
        const action: Action | DeliverySlotAction = await this.buildPageAction(pageActionRequest)

        return {
            widgets: widgets,
            pageActions: [action],
            actions: [action],
            alertInfo: this.getAlertInfo(null, undefined, availabilities, order.productSnapshots, numProducts, user, userContext, false, undefined),
            cart: {
                deliveryInfos: deliveryInfo,
                startDate: order.productSnapshots[0].option.startDate
            }
        }
    }
}

export class OnlineKiosk extends IEatBrand {
    private getCartListWidget(userContext: UserContext, billingDetails: BillingDetails, orderItemsInfo: OrderItemInfo[], userAgent: UserAgentType, order: Order, mealSlot: MenuType) {
        let numItems = 0
        const images: string[] = []
        const actions: Action[] = []

        const orderProductOption = order.productSnapshots[0].option
        orderItemsInfo.forEach(orderItemInfo => {
            numItems = numItems + orderItemInfo.quantity
            const seoParams = {
                productName: orderItemInfo.productName
            }
            const imageProductId = EatUtil.getProductId(orderItemInfo)
            const image = orderItemInfo.isPack || orderItemInfo.productType === "FIT_CLUB_MEMBERSHIP" ?
                UrlPathBuilder.getPackImagePath(orderItemInfo.productId, orderItemInfo.productType, orderItemInfo.productType === "FIT_CLUB_MEMBERSHIP" ? "THUMBNAIL" : "MAGAZINE", orderItemInfo.imageVersion, userAgent) :
                UrlPathBuilder.getSingleImagePath(imageProductId, orderItemInfo.productType, "THUMBNAIL", orderItemInfo.imageVersion)
            const action: Action = {
                actionType: "NAVIGATION",
                url: ActionUtil.foodSingle(orderItemInfo.productId, orderProductOption.startDate,
                    mealSlot, false, undefined, userContext.sessionInfo.userAgent, seoParams),
            }
            images.push(image)
            actions.push(action)
        })
        const orderItems =  this.buildCartListWidgetPayload(orderItemsInfo, images, actions)
        return this.buildCartListWidget(userContext, order, billingDetails, orderItems)
    }

    private getDeliverySlotWidget(date: string, availableSlots: string[], selectedSlot: string, allSlots: DeliverySlot[], arrivalInfo: ArrivalInfo, onDemandText: string, mealSlot: MenuType, areaTz: Timezone, isDeliverySlotEmpty: boolean, isKiosk: boolean) {
        const isDisabled: boolean = (mealSlot === "ALL") || isKiosk
        const selectedSlotObject: DeliverySlot = _.find(allSlots, slot => {
            return slot.slotId === selectedSlot
        })
        const title = this.checkForDeliverySlotTitle(selectedSlot, onDemandText, isDeliverySlotEmpty) ? "Select delivery slot" : MealUtil.getDisplayTextForSlot(selectedSlotObject, undefined, arrivalInfo, onDemandText, false)
        const subTitle: string = TimeUtil.getDayText(date, areaTz)
        const deliverySlot: string = selectedSlot
        const deliveryDate: string = date
        const slots = _.map(allSlots, slot => {
            const slotDetail: {
                deliverySlot: string
                title: string,
                isEnabled: boolean
            } = {
                deliverySlot: slot.slotId,
                title: MealUtil.getDisplayTextForSlot(slot, undefined, arrivalInfo, onDemandText, true),
                isEnabled: _.findIndex(availableSlots, (availableSlot) => {
                    return availableSlot === slot.slotId
                }) !== -1
            }
            return slotDetail
        })
        const action: DeliverySlotAction = {
            title: title,
            actionType: "CHANGE_SLOT",
            mealSlot: mealSlot,
            slots: slots
        }
        return this.buildDeliverySlotWidgetV3(title, isDisabled, action, subTitle, deliverySlot, deliveryDate)
    }

    private getCutleryWidget(order: Order, mealSlot: MenuType) {
        const isCutleryEnabled: boolean = order.userAddress.eatDeliveryInstruction.cutleryInstruction === "GIVE_CUTLERY"
        const subtitle: string = (order.packagingCharges && !order.productSnapshots[0].isPack) ? "Refuse cutlery, save ₹3 and go eco-friendly!" : "Refuse cutlery and go eco-friendly"
        return this.buildCutleryWidget(mealSlot, isCutleryEnabled, subtitle)
    }
    private getCutleryWidgetV2(order: Order, mealSlot: MenuType, userContext: UserContext, cutleryInstructionFromClient: CutleryInstruction) {
        const isCutleryEnabled: boolean = order.userAddress.eatDeliveryInstruction.cutleryInstruction === "GIVE_CUTLERY"
        const subtitle: string = (order.packagingCharges && !order.productSnapshots[0].isPack) ? "Refuse cutlery, save ₹3 and go eco-friendly!" : "Refuse cutlery and go eco-friendly"
        return this.buildCutleryWidgetV2(mealSlot, isCutleryEnabled, subtitle, userContext, cutleryInstructionFromClient)
    }
    private async getEatDeliveryInstructionWidget(order: Order, cartReviewPayload: CartReviewPayload, userContext: UserContext): Promise<DeliveryInstructionWidgetV2> {
        const modalInfo = {
            title: "CONTACTLESS DELIVERY",
            description: "Opt for contactless delivery and the meal will be placed on a clean surface outside your door or at the reception"
        }
        const isContactInstructionSupported = AppUtil.isCutlerySelectClientSideHandleSupported(userContext)
        const instructionList = EatDeliveryInstructionList
        const contactInstructionList = EatContactInstructionList
        let selectedIndex = 0
        if (isContactInstructionSupported) {
            selectedIndex = _.get(cartReviewPayload, "option.eatDeliveryInstruction") ? EatUtil.findEatDeliveryDropInstructionId(cartReviewPayload.option.eatDeliveryInstruction) : 2
        } else {
            selectedIndex = _.get(cartReviewPayload, "option.eatDeliveryInstruction") ? EatUtil.findEatDeliveryInstructionId(cartReviewPayload.option.eatDeliveryInstruction) : 2
        }
        const selectedContactIndex = _.get(cartReviewPayload, "option.eatDeliveryInstruction") ? EatUtil.findEatContactInstructionId(cartReviewPayload.option.eatDeliveryInstruction) : 0
        const deliveryInstruction: Instruction = instructionList[selectedIndex]
        const selectedContactInstruction = contactInstructionList[selectedContactIndex]
        const instructionPayload: Instruction = {
            displayName: deliveryInstruction.displayName,
            payload: {
                contactInstruction: selectedContactInstruction.payload.contactInstruction,
                dropInstruction: deliveryInstruction.payload.dropInstruction,
                cutleryInstruction: deliveryInstruction.payload.cutleryInstruction
            }
        }
        const deliveryInstructionWidget: DeliveryInstructionWidgetV2 = new DeliveryInstructionWidgetV2("", instructionList, isContactInstructionSupported ? instructionPayload : deliveryInstruction, modalInfo, contactInstructionList)
        return deliveryInstructionWidget
    }

    public async build(interfaces: CFServiceInterfaces, request: ReviewRequest, userContext: UserContext): Promise<CartReviewView> {
        const eatApiResponse: EatApiResponseInterface = await interfaces.eatApiClientService.getEatReviewResponse(request)
        const widgetViewPromises: Promise<WidgetView | BaseWidget>[] = []
        const areaTz: Timezone = eatApiResponse.areaTz
        const user: User = eatApiResponse.user
        const cartReviewPayload = eatApiResponse.cartReviewPayload
        const cartReviewResult = eatApiResponse.cartReviewWrapperResponse.cartReviewResult
        const mealSlot: MenuType = cartReviewPayload.option.mealSlot
        const allSlots: DeliverySlot[] = eatApiResponse.allSlots
        const mealSlotDeliverySlots = eatApiResponse.mealSlotDeliverySlotInfo.mealSlotDeliverySlot
        const deliveryInfo = cartReviewPayload.option.deliveryInfo
        const billingDetails: BillingDetails = eatApiResponse.billingDetails
        const packDates: string[] = eatApiResponse.packDates
        const isDeliverySlotEmpty: boolean = _.isNil(cartReviewPayload.option.deliverySlot) && (userContext.sessionInfo.userAgent === "DESKTOP" || userContext.sessionInfo.userAgent === "MBROWSER")
        const orderItemsInfo = eatApiResponse.orderItems
        const menuAvailabilityResult = eatApiResponse.menuAvailability
        const alertStatus: AlertInfoStatus = eatApiResponse.alertStatus
        const userPromise = userContext.userPromise
        const cutleryInstructionFromClient: CutleryInstruction = _.get(request, "cartReviewPayload.option.deliveryInfo[0].cutleryInstruction", undefined)

        if (alertStatus === "SOLD_OUT" || alertStatus === "NON_SERVICEABLE_ALERT") {
            const alertInfoParams: AlertInfoParams = {
                nonServiceableInfo: undefined
            }
            const nonServiceableAddressIndex = deliveryInfo.findIndex((info) => {
                return info.isWeekdayAddressServiceable === false || info.isWeekendAddressServiceable === false
            })
            if (nonServiceableAddressIndex >= 0) {
                alertInfoParams.nonServiceableInfo = deliveryInfo[nonServiceableAddressIndex]
            }
            return {
                widgets: [],
                alertInfo: AlertInfos.AlertInfo(alertStatus, alertInfoParams, userContext ),
                cart: {
                    startDate: undefined,
                    deliveryInfos: cartReviewPayload.option.deliveryInfo
                }
            }
        }
        const updateLog = this.checkComboCreation(cartReviewPayload, cartReviewResult)
        const order: Order = cartReviewResult.order
        const orderProductOption = order.productSnapshots[0].option
        const onDemandText = order.eatOptions && order.eatOptions.onDemandPromise ? order.eatOptions.onDemandPromise.message : ""
        const addons = eatApiResponse.addons
        const cartAddon = this.getCartAddon(addons)
        const arrivalInfo: ArrivalInfo = cartReviewResult.arrivalInfo

        await this.addFitClubSavingsWidgets(interfaces.fitcashService, interfaces.offerService, userContext, order, billingDetails, request.isFitcashEnabled, widgetViewPromises)

        const cartListWidgetPromise = this.getCartListWidget(userContext, billingDetails, orderItemsInfo, userContext.sessionInfo.userAgent, order, mealSlot)
        // this.logger.info("order.productSnapshots[0].isPack : " + order.productSnapshots[0].isPack)
        widgetViewPromises.push(cartListWidgetPromise)
        const eatAddressWidget: Promise<WidgetView | BaseWidget>[] = []
        const isKiosk = order.userAddress && order.userAddress.kioskId !== undefined

        // widget checks
        const shouldShowAddressWidget = EatUtil.isAddressWidgetSupported(false)
        const shouldShowStartDateWidget = EatUtil.isStartDateWidgetSupported(order.productSnapshots[0].isPack)
        const shouldShowDeliverySlotWidget = EatUtil.isDeliverySlotWidgetSupported(false)
        const shouldShowCutleryWidget = EatUtil.isCutleryWidgetSupported(false, false, "EAT_FIT")
        const shouldShowHCMTippingWidget = await AppUtil.isHCMTippingSupported(userContext, order, userPromise, interfaces)
        const isCheckoutV2Supported = AppUtil.isNewCheckoutSupported(userContext)
        if (isCheckoutV2Supported && !order.productSnapshots[0].isPack) {
            if (shouldShowAddressWidget && order.userAddress.addressId !== "DUMMY") {
                const addressWidgetPromise = this.buildAddressWidget(userContext, order.userAddress, order.userWeekendAddress, orderProductOption.weekendEnabled, order.productSnapshots[0].isPack, mealSlot, this.showCartAddressModalAction(mealSlot, false), "ADDRESS_WIDGET_V2")
                eatAddressWidget.push(addressWidgetPromise)
            }
            if (shouldShowStartDateWidget) {
                const startDateWidgetPromise = this.buildStartDateWidget(orderProductOption.startDate, packDates, userContext, areaTz)
                eatAddressWidget.push(startDateWidgetPromise)
            }
            if (shouldShowDeliverySlotWidget && order.userAddress.addressId !== "DUMMY") {
                const deliverySlotWidget = Promise.resolve(this.getDeliverySlotWidget(orderProductOption.startDate, mealSlotDeliverySlots[mealSlot],
                    deliveryInfo[0].deliverySlot, allSlots, arrivalInfo, onDemandText, mealSlot, areaTz, isDeliverySlotEmpty, isKiosk))
                eatAddressWidget.push(deliverySlotWidget)
            }
        }
        else {
            if (shouldShowAddressWidget) {
                const addressWidgetPromise = this.buildAddressWidget(userContext, order.userAddress, order.userWeekendAddress, orderProductOption.weekendEnabled, order.productSnapshots[0].isPack, mealSlot )
                widgetViewPromises.push(addressWidgetPromise)
            }
            if (shouldShowStartDateWidget) {
                const startDateWidgetPromise = this.buildStartDateWidget(orderProductOption.startDate, packDates, userContext, areaTz)
                widgetViewPromises.push(startDateWidgetPromise)
            }
            if (shouldShowDeliverySlotWidget) {
                const isKiosk = order.userAddress && order.userAddress.kioskId !== undefined
                const deliverySlotWidget = Promise.resolve(this.getDeliverySlotWidget(orderProductOption.startDate, mealSlotDeliverySlots[mealSlot],
                    deliveryInfo[0].deliverySlot, allSlots, arrivalInfo, onDemandText, mealSlot, areaTz, isDeliverySlotEmpty, isKiosk))
                widgetViewPromises.push(deliverySlotWidget)
            }
        }
        if (shouldShowCutleryWidget) {
            // using the same widget for order now and subscription
                const cutleryWidgetPromise = this.getCutleryWidgetV2(order, mealSlot, userContext, cutleryInstructionFromClient)
                widgetViewPromises.push(cutleryWidgetPromise)
        }

        if (shouldShowHCMTippingWidget) {
            const hcmTippingWidgetPromise = this.getHCMTippingWidget()
            if (hcmTippingWidgetPromise) {
                widgetViewPromises.push(hcmTippingWidgetPromise)
            }
        }
        if (AppUtil.isEatDeliveryInstructionSupported(userContext) && !isKiosk) {
            if (eatApiResponse.areaId) {
                const deliveryArea = await interfaces.deliveryAreaService.getDeliveryArea(eatApiResponse.areaId)
                if (!_.get(deliveryArea, "logisticsConfigs.mode.only3PLogistics")) {
                    const deliveryInstructionWidgetPromise = this.getEatDeliveryInstructionWidget(order, cartReviewPayload, userContext)
                    widgetViewPromises.push(deliveryInstructionWidgetPromise)
                }
            }
        }
        const shouldShowCartAddonWidget = EatUtil.isCartAddonWidgetSupported(cartAddon, false, order.productSnapshots[0].isPack , "EAT_FIT")
        if ( shouldShowCartAddonWidget) {
            const cartAddonWidgetPromise = this.buildCartAddonWidget(cartAddon, order, menuAvailabilityResult, mealSlot, "EAT_FIT", userContext)
            const cartAddonWidget = await cartAddonWidgetPromise
            if (cartAddonWidget)
                widgetViewPromises.push(Promise.resolve(cartAddonWidget))
        }

        const getBillingWidgetPromise = this.buildBillingWidget(userContext, order, billingDetails, request.isFitcashEnabled, shouldShowHCMTippingWidget)
        if (isCheckoutV2Supported && eatAddressWidget.length > 0 && !order.productSnapshots[0].isPack) {
            const eatAddressWidgetComponents = await Promise.all(eatAddressWidget)
            const eatAddressandDeliverySlotWidget = new EatAddressWidget(eatAddressWidgetComponents).buildView(interfaces, userContext, [])
            widgetViewPromises.push(eatAddressandDeliverySlotWidget)
        }
        widgetViewPromises.push(getBillingWidgetPromise)
        const widgets = await Promise.all(widgetViewPromises)
        const numProducts = _.sum(order.productSnapshots.map(p => { return p.quantity }))

        const total: number = billingDetails.billingInfo.total
        const pageActionRequest: PageActionRequest = {
            userContext: userContext,
            order: order,
            numProducts: numProducts,
            arrivalInfo: arrivalInfo,
            onDemandText: onDemandText,
            mealSlotDeliverySlots: mealSlotDeliverySlots,
            total: total,
            deliveryInfo: deliveryInfo,
            isCafe: false,
            isDeliverySlotEmpty: isDeliverySlotEmpty,
            allSlots: allSlots,
            billingDetails: billingDetails,
            mealSlot: mealSlot,
            currency: billingDetails.billingInfo.currency,
            isFitcashEnabled: request.isFitcashEnabled,
            isCheckoutV2Supported: isCheckoutV2Supported
        }
        const action: Action | DeliverySlotAction = await this.buildPageAction(pageActionRequest)

        const isOfferOverriden: boolean = eatApiResponse.cartReviewWrapperResponse.isOfferOverriden
        return {
            showDivider: isCheckoutV2Supported && !order.productSnapshots[0].isPack ? false : true,
            widgets: widgets,
            pageActions: [action],
            actions: [action],
            alertInfo: this.getAlertInfo(alertStatus, deliveryInfo, cartReviewResult.availabilities, order.productSnapshots, numProducts, user, userContext, isOfferOverriden, updateLog),
            cart: {
                deliveryInfos: deliveryInfo,
                startDate: order.productSnapshots[0].option.startDate
            },
            total: `${AppUtil.getCurrencySymbol(billingDetails.billingInfo.currency)} ${order.totalAmountPayable}`,
            totalWithoutCurrency: order.totalAmountPayable,
        }

    }
}

export class WholeFit extends IEatBrand {

    private getDeliverySlotWidget(date: string, availableSlots: { [date: string]: string[] }, selectedSlot: string, allSlots: DeliverySlot[], arrivalInfo: ArrivalInfo, onDemandText: string, mealSlot: MenuType, areaTz: Timezone, isDeliverySlotEmpty: boolean, justSlotDataRequired?: boolean, isWeb?: boolean): any {
        const isDisabled: boolean = false
        const selectedSlotObject: DeliverySlot = _.find(allSlots, slot => {
            return slot.slotId === selectedSlot
        })
        const title = this.checkForDeliverySlotTitle(selectedSlot, onDemandText, isDeliverySlotEmpty) ? "Select delivery slot" : MealUtil.getDisplayTextForSlot(selectedSlotObject, undefined, arrivalInfo, onDemandText, false)
        const subTitle: string = TimeUtil.getDayText(date, areaTz)
        const deliverySlot: string = selectedSlot
        const deliveryDate: string = date
        const dayWiseSlots: any[] = []
        const days = _.keys(availableSlots)
        _.forEach(days, day => {
            const slots: any[] = []
            _.forEach(allSlots, slot => {
                const isSlotAvailable: string = _.find(availableSlots[day], daySlot => {
                    return daySlot === slot.slotId
                })

                slots.push({
                    slotId: slot.slotId,
                    title: MealUtil.getDisplayTextForSlot(slot, undefined, arrivalInfo, onDemandText, false),
                    isEnabled: !_.isNil(isSlotAvailable)
                })

            })
            const dayWiseSlot = {
                deliveryDate: day,
                title: TimeUtil.getDayText(day, areaTz),
                deliverySlots: slots
            }
            dayWiseSlots.push(dayWiseSlot)
        })
        const action: DateWiseDeliverySlotAction = {
            title: title,
            actionType: isWeb ? "CHANGE_SLOT" : "CHANGE_SLOTV2",
            mealSlot: mealSlot,
            slots: dayWiseSlots,
            deliveryDate: date,
            deliverySlot: selectedSlot
        }
        const deliverySlotWidget = this.buildDeliverySlotWidgetV3(title, isDisabled, action, subTitle, deliverySlot, deliveryDate)
        if (justSlotDataRequired) {
            return deliverySlotWidget.action
        }
        return deliverySlotWidget
    }

    private getCartListWidgetV2(userContext: UserContext, billingDetails: BillingDetails, userAgent: UserAgentType, order: Order, mealSlot: MenuType, c1ProductMap?: Map<string, OrderItemInfo[]>): Promise<CartListWidget>[] {
    const orderProductOption = order.productSnapshots[0].option
    const cartlistWidgetPromise: Promise<CartListWidget>[] = []
    c1ProductMap.forEach( (c1, c1name) => {
        const images: string[] = []
        const actions: Action[] = []
        const orderItemsInfo = c1 as OrderItemInfo[]
        const c1CategoryId = orderItemsInfo[0].c1CategoryId
        orderItemsInfo.forEach(orderItemInfo => {
            const imageProductId = EatUtil.getProductId(orderItemInfo)
            const image = orderItemInfo.isPack ? UrlPathBuilder.getPackImagePath(orderItemInfo.productId, orderItemInfo.productType, "MAGAZINE", orderItemInfo.imageVersion, userAgent) :
                UrlPathBuilder.getSingleImagePath(imageProductId, orderItemInfo.productType, "THUMBNAIL", orderItemInfo.imageVersion)
            const seoParams = {
                productName: orderItemInfo.productName
            }
            const action: Action = {
                actionType: "NAVIGATION",
                url: ActionUtil.foodSingle(orderItemInfo.productId, orderProductOption.startDate,
                    mealSlot, false, undefined, userContext.sessionInfo.userAgent, seoParams),
            }
            images.push(image)
            actions.push(action)
        })
        const orderItems = this.buildCartListWidgetPayload(orderItemsInfo, images, actions)
        cartlistWidgetPromise.push(this.buildCartListWidgetV2(userContext, order, billingDetails, orderItems, c1name, c1CategoryId ))
    })
    return cartlistWidgetPromise
}


    private getCartListWidget(interfaces: CFServiceInterfaces, userContext: UserContext, billingDetails: BillingDetails, orderItemsInfo: OrderItemInfo[], userAgent: UserAgentType, order: Order, mealSlot: MenuType, c1ProductMap?: Map<string, OrderItemInfo[]>): Promise<CartListWidget>[] {
        if (!_.isNil(c1ProductMap) && !_.isEmpty(c1ProductMap)) {
            return this.getCartListWidgetV2(userContext, billingDetails, userAgent, order, mealSlot, c1ProductMap)
        }
        let numItems: number = 0
        const images: string[] = []
        const actions: Action[] = []
        const orderProductOption = order.productSnapshots[0].option
        for (const item of orderItemsInfo) {
            numItems = numItems + item.quantity
            const imageProductId = EatUtil.getProductId(item)
            const image = item.isPack ? UrlPathBuilder.getPackImagePath(item.productId, item.productType, item.productType === "FIT_CLUB_MEMBERSHIP" ? "THUMBNAIL" : "MAGAZINE", item.imageVersion, userAgent) :
                    UrlPathBuilder.getSingleImagePath(imageProductId, item.productType, "THUMBNAIL", item.imageVersion)
                const seoParams = {
                    productName: item.productName
                }
                const action: Action = {
                    actionType: "NAVIGATION",
                    url: ActionUtil.foodSingle(item.productId, orderProductOption.startDate,
                        mealSlot, false, undefined, userContext.sessionInfo.userAgent, seoParams)
                }
                images.push(image)
                actions.push(action)
        }
        const orderItems = this.buildCartListWidgetPayload(orderItemsInfo, images, actions)
        return [this.buildCartListWidgetV2(userContext, order, billingDetails, orderItems, numItems === 1 ? `${numItems} Item` : `${numItems} Items`)]
    }

    async buildWholeFitPageAction(pageActionRequest: PageActionRequest, interfaces: CFServiceInterfaces) {
        const userContext = pageActionRequest.userContext
        const order = pageActionRequest.order
        const numProducts = pageActionRequest.numProducts
        const mealSlotDeliverySlots = pageActionRequest.mealSlotDeliverySlots
        const deliveryInfo = pageActionRequest.deliveryInfo
        const isDeliverySlotEmpty = pageActionRequest.isDeliverySlotEmpty
        const allSlots = pageActionRequest.allSlots
        const arrivalInfo = pageActionRequest.arrivalInfo
        const isCafe = pageActionRequest.isCafe
        const onDemandText = pageActionRequest.onDemandText
        const billingDetails = pageActionRequest.billingDetails
        const currency = pageActionRequest.currency
        const mealSlot = pageActionRequest.mealSlot
        let action: Action | DeliverySlotAction | CafeDeliverySlotAction
        const isWeb = userContext.sessionInfo.userAgent !== "APP"
        if (!userContext.sessionInfo.isUserLoggedIn) {
            action = {
                actionType: "SHOW_ALERT_MODAL",
                title: "Get pack", meta: {
                    title: "Login Required!",
                    subTitle: "Please login to continue",
                    actions: [{ actionType: "LOGOUT", title: "Login" }]
                }
            }
            return action

        }
        const tz = userContext.userProfile.timezone
        if (numProducts === 0) {
            // Handling case where there 0 items due to checkout
            action = {
                actionType: "POP_ACTION",
                url: "curefit://wholefitclp",
                title: "Ok"
            }
        }
        const mealSlots = _.keys(mealSlotDeliverySlots)
        mealSlots.sort((slotA, slotB) => {
            return ALL_MEAL_SLOTS.indexOf(<MealSlot>slotA) - ALL_MEAL_SLOTS.indexOf(<MealSlot>slotB)
        })
        const supportsDeliveryWindow = await AppUtil.isEatDeliveryWindowSupported(userContext)
        const everyReturn = mealSlots.every((mealSlot) => {
            if (!_.isNil(action)) {
                return false
            }
            const availableSlots = mealSlotDeliverySlots[mealSlot]
            const deliveryInfoIndex = deliveryInfo.findIndex((info) => {
                return info.mealSlot === mealSlot
            })
            const info = deliveryInfo[deliveryInfoIndex]
            // interfaces.logger.info(`info obejct: ${JSON.stringify(info)}`)
            if (_.isNil(info) || info.isWeekdayAddressServiceable === false || _.isNil(info.addressId)) {
                /**
                 * When the address user selected is unservicable
                 * when addressId is undefined (there is no address selected)
                 */
                interfaces.logger.info("Proceed to select the addressId")
                if (isWeb) {
                    action = this.webCartAddressAction(mealSlot)
                } else {
                    action = this.showCartAddressModalAction(mealSlot, true)
                }
            } else if (info.deliverySlot === undefined || (isDeliverySlotEmpty && onDemandText === "")) {
                /*
                used when deliverySlot is not selected by the user
                 */
                interfaces.logger.info("Slots processing")
                const slots = _.map(allSlots, (slot) => {
                    const slotDetail: {
                        deliverySlot: string
                        title: string,
                        isEnabled: boolean
                    } = {
                        deliverySlot: slot.slotId,
                        title: MealUtil.getDisplayTextForSlot(slot, undefined, arrivalInfo, onDemandText, isCafe),
                        isEnabled: _.findIndex(availableSlots, (availableSlot) => {
                            return availableSlot === slot.slotId
                        }) !== -1
                    }
                    return slotDetail
                })
                if (!_.isNil(onDemandText) && onDemandText.length > 0) {
                    slots.unshift({
                        deliverySlot: SlotUtil.ON_DEMAND_SLOT,
                        title: MealUtil.getSlotDisplayText(SlotUtil.ON_DEMAND_SLOT, undefined, arrivalInfo, onDemandText, isCafe),
                        isEnabled: true
                    })
                }
                action = {
                    title: "Select delivery slot",
                    actionType: isWeb ? "CHANGE_SLOT" : "SHOW_DELIVERY_SLOT_MODAL",
                    mealSlot: mealSlot as MenuType,
                    slots: slots,
                    deliveryWindows: pageActionRequest.deliveryWindows
                }
            }
            return true
        })
        if (_.isNil(action)) {
            interfaces.logger.info("Proceed to pay")
            const totalAmountPayable = order.totalAmountPayable
            if (totalAmountPayable === 0) {
                action = {
                    title: "Pay for free",
                    actionType: "PAY_FREE"
                }
            } else {
                action = {
                    title: `Pay ${AppUtil.getCurrencySymbol(currency)} ${totalAmountPayable}`,
                    actionType: "CART_PAY_NOW",
                    url: "curefit://payment"
                }
            }
        }
        return action
    }

    public async wholefitV2Response(interfaces: CFServiceInterfaces,  userContext: UserContext, eatApiResponse: EatApiResponseInterface, isFitcashEnabled: boolean, startDate: string, orderSource: OrderSource): Promise<{
        widgets: (WidgetView | IBaseWidget)[],
        pageActions?: Action[],
        actions?: Action[],
        alertInfo?: AlertInfo,
        cart?: {
            deliveryInfos: ClientDeliveryInfo[],
            startDate: string
        },
        deliverySlots?: DeliverySlotAction | DateWiseDeliverySlotAction
        total?: string
    }> {
        const widgetViewPromises: (Promise<WidgetView | IBaseWidget>)[] = []
        const areaTz: Timezone = eatApiResponse.areaTz
        const user: User = eatApiResponse.user
        const cartReviewPayload = eatApiResponse.cartReviewPayload
        const cartReviewResult = eatApiResponse.cartReviewWrapperResponse.cartReviewResult
        const mealSlot: MenuType = cartReviewPayload.option.mealSlot
        const allSlots: DeliverySlot[] = eatApiResponse.allSlots
        const mealSlotDeliverySlots = eatApiResponse.mealSlotDeliverySlotInfo.mealSlotDeliverySlot
        const deliveryInfo = cartReviewPayload.option.deliveryInfo
        const billingDetails: BillingDetails = eatApiResponse.billingDetails
        const isDeliverySlotEmpty: boolean = _.isNil(cartReviewPayload.option.deliverySlot) && (userContext.sessionInfo.userAgent === "DESKTOP" || userContext.sessionInfo.userAgent === "MBROWSER")
        const orderItemsInfo = eatApiResponse.orderItems
        const dateWiseAvailableSlots = eatApiResponse.mealSlotDeliverySlotInfo.dateWiseAvailableSlots
        const alertStatus: AlertInfoStatus = eatApiResponse.alertStatus
        const isWeb = userContext.sessionInfo.userAgent !== "APP"
        if (alertStatus === "SOLD_OUT" || alertStatus === "NON_SERVICEABLE_ALERT") {
            const alertInfoParams: AlertInfoParams = {
                nonServiceableInfo: undefined
            }
            const nonServiceableAddressIndex = deliveryInfo.findIndex((info) => {
                return info.isWeekdayAddressServiceable === false || info.isWeekendAddressServiceable === false
            })
            if (nonServiceableAddressIndex >= 0) {
                alertInfoParams.nonServiceableInfo = deliveryInfo[nonServiceableAddressIndex]
            }
            return {
                widgets: [],
                alertInfo: AlertInfos.AlertInfo(
                    alertStatus === "NON_SERVICEABLE_ALERT" ? "ADDRESS_OUT_OF_DELIVERY_AREA" : alertStatus,
                    {
                        listingBrand: "WHOLE_FIT",
                        orderSource: orderSource
                    }),
                cart: {
                    startDate: undefined,
                    deliveryInfos: cartReviewPayload.option.deliveryInfo
                }
            }
        }
        const updateLog = this.checkComboCreation(cartReviewPayload, cartReviewResult)
        const order = cartReviewResult.order
        const orderProductOption = order.productSnapshots[0].option
        const arrivalInfo: ArrivalInfo = cartReviewResult.arrivalInfo
        const onDemandText = order.eatOptions && order.eatOptions.onDemandPromise ? order.eatOptions.onDemandPromise.message : ""

        const c1ProductMap = new Map<string, OrderItemInfo[]>()
        const isWholefitc1CategorisationSupported = AppUtil.isWholefitc1CategorisationSupported(userContext)
        if (isWholefitc1CategorisationSupported) {
            this.addCategoryIdtoItems(interfaces, orderItemsInfo, c1ProductMap)
        }
        const cartListWidgetPromise = this.getCartListWidget(interfaces, userContext, billingDetails, orderItemsInfo, userContext.sessionInfo.userAgent, order, mealSlot, c1ProductMap)
        widgetViewPromises.push(...cartListWidgetPromise)
        const getBillingWidgetPromise = this.buildBillingWidget(userContext, order, billingDetails, isFitcashEnabled)
        interfaces.logger.info(`the addressId is ${order.userAddress.addressId}`)
        interfaces.logger.info(`userAgent: ${userContext.sessionInfo.userAgent}`)
        if (order.userAddress.addressId !== "DUMMY" || isWeb) {
            // interfaces.logger.info(`order object: ${JSON.stringify(order)}`)
            widgetViewPromises.push(this.buildAddressWidget(userContext, order.userAddress, order.userWeekendAddress, orderProductOption.weekendEnabled, order.productSnapshots[0].isPack, mealSlot, isWeb ? this.webCartAddressAction(mealSlot) : this.showCartAddressModalAction(mealSlot)))
            widgetViewPromises.push(this.getDeliverySlotWidget(orderProductOption.startDate, dateWiseAvailableSlots, deliveryInfo[0].deliverySlot, allSlots, arrivalInfo, onDemandText, mealSlot, areaTz, isDeliverySlotEmpty, false, isWeb))
        }
        widgetViewPromises.push(getBillingWidgetPromise)
        let widgets = await Promise.all(widgetViewPromises)
        widgets = widgets.filter(v => !_.isNil(v))
        const numProducts = _.sum(order.productSnapshots.map((p: any) => {
            return p.quantity
        }))

        const total: number = billingDetails.billingInfo.total
        const pageActionRequest: PageActionRequest = {
            userContext: userContext,
            order: order,
            numProducts: numProducts,
            arrivalInfo: arrivalInfo,
            onDemandText: onDemandText,
            mealSlotDeliverySlots: mealSlotDeliverySlots,
            total: total,
            deliveryInfo: deliveryInfo,
            isCafe: false,
            isDeliverySlotEmpty: isDeliverySlotEmpty,
            allSlots: allSlots,
            billingDetails: billingDetails,
            mealSlot: mealSlot,
            currency: billingDetails.billingInfo.currency,
            isFitcashEnabled: isFitcashEnabled
        }
        const action: Action | DeliverySlotAction = await this.buildWholeFitPageAction(pageActionRequest, interfaces)

        const isOfferOverriden: boolean = eatApiResponse.cartReviewWrapperResponse.isOfferOverriden
        return {
            total: `${AppUtil.getCurrencySymbol(billingDetails.billingInfo.currency)} ${order.totalAmountPayable}`,
            deliverySlots: this.getDeliverySlotWidget(orderProductOption.startDate, dateWiseAvailableSlots, deliveryInfo[0].deliverySlot, allSlots, arrivalInfo, onDemandText, mealSlot, areaTz, isDeliverySlotEmpty, true, isWeb),
            widgets: widgets,
            cart: {
                deliveryInfos: deliveryInfo,
                startDate: order.productSnapshots[0].option.startDate
            },
            alertInfo: this.getAlertInfo(alertStatus, deliveryInfo, cartReviewResult.availabilities, order.productSnapshots, numProducts, user, userContext, isOfferOverriden, updateLog, orderSource, "WHOLE_FIT"),
            actions: [action],
            pageActions: [action]
        }
    }

    addCategoryIdtoItems(interfaces: CFServiceInterfaces, orderItemInfo: OrderItemInfo[], c1ProductMap: Map<string, OrderItemInfo[]>) {
        // populates the c1ProductMap as well
        // todo: make this generic for eatfit as well when required, change salesChannel
        orderItemInfo.forEach(item => {
            const categoryId = interfaces.salesCategoryService.getSalesChannelCategoryId(_.get(item, "salesChannelConfig"), "CUREFIT_WHOLEFIT")
            let c1
            if (categoryId) {
                const c1Details = interfaces.salesCategoryService.getTopLevelCategory(categoryId, "CUREFIT_WHOLEFIT")
                c1 = c1Details.name
                item.c1CategoryId = c1Details.categoryId
            } else {
                c1 = "OTHERS"
                item.c1CategoryId = "OTHERS"
            }
            item.categoryId = categoryId ? categoryId : "OTHERS"
            const c1Mapping = c1ProductMap.get(c1)
            if (c1Mapping) {
                c1Mapping.push(item)
            } else {
                c1ProductMap.set(c1, [item])
            }
        })
    }


    public async buildV2(interfaces: CFServiceInterfaces, userContext: UserContext, eatApiResponse: EatApiResponseInterface, isFitCashEnabled: boolean, startDate: string, orderSource: OrderSource): Promise<CartReviewView> {
        return await this.wholefitV2Response(interfaces, userContext, eatApiResponse, isFitCashEnabled, startDate, orderSource)
    }
    public async build(interfaces: CFServiceInterfaces, request: ReviewRequest, userContext: UserContext): Promise<CartReviewView> {
        // interfaces.logger.info(`review request for eat-api: ${JSON.stringify(request)}`)
        const eatApiResponse: EatApiResponseInterface = await interfaces.eatApiClientService.getEatReviewResponse(request)
        if (AppUtil.isWholeFitV2ReviewSupported(userContext)) {
            // not relying on eat-api for these
            const startDate = request.cartReviewPayload.option.date
            const orderSource = _.get(request, "userDetailInfo.orderSource")
            interfaces.logger.info(`orderSource: ${orderSource}`)
            return this.buildV2(interfaces, userContext, eatApiResponse, request.isFitcashEnabled, startDate, orderSource)
        }
        const widgetViewPromises: Promise<WidgetView>[] = []
        const areaTz: Timezone = eatApiResponse.areaTz
        const user: User = eatApiResponse.user
        const cartReviewPayload = eatApiResponse.cartReviewPayload
        const cartReviewResult = eatApiResponse.cartReviewWrapperResponse.cartReviewResult
        const mealSlot: MenuType = cartReviewPayload.option.mealSlot
        const allSlots: DeliverySlot[] = eatApiResponse.allSlots
        const mealSlotDeliverySlots = eatApiResponse.mealSlotDeliverySlotInfo.mealSlotDeliverySlot
        const deliveryInfo = cartReviewPayload.option.deliveryInfo
        const billingDetails: BillingDetails = eatApiResponse.billingDetails
        const packDates: string[] = eatApiResponse.packDates
        const isDeliverySlotEmpty: boolean = _.isNil(cartReviewPayload.option.deliverySlot) && (userContext.sessionInfo.userAgent === "DESKTOP" || userContext.sessionInfo.userAgent === "MBROWSER")
        const orderItemsInfo = eatApiResponse.orderItems
        const dateWiseAvailableSlots = eatApiResponse.mealSlotDeliverySlotInfo.dateWiseAvailableSlots
        const alertStatus: AlertInfoStatus = eatApiResponse.alertStatus
        if (alertStatus === "SOLD_OUT" || alertStatus === "NON_SERVICEABLE_ALERT") {
            // const alertInfoParams: AlertInfoParams = {
            //     nonServiceableInfo: undefined
            // }
            // const nonServiceableAddressIndex = deliveryInfo.findIndex((info) => {
            //     return info.isWeekdayAddressServiceable === false || info.isWeekendAddressServiceable === false
            // })
            // if (nonServiceableAddressIndex >= 0) {
            //     alertInfoParams.nonServiceableInfo = deliveryInfo[nonServiceableAddressIndex]
            // }

            // TODO: To add a permanent fix for non Serviceable Alert @Nisheet
            return {
                widgets: [],
                alertInfo: AlertInfos.AlertInfo(alertStatus === "NON_SERVICEABLE_ALERT" ? "ADDRESS_OUT_OF_DELIVERY_AREA" : alertStatus),
                cart: {
                    startDate: undefined,
                    deliveryInfos: cartReviewPayload.option.deliveryInfo
                }
            }
        }
        const updateLog = this.checkComboCreation(cartReviewPayload, cartReviewResult)
        const order: Order = cartReviewResult.order
        const orderProductOption = order.productSnapshots[0].option
        const arrivalInfo: ArrivalInfo = cartReviewResult.arrivalInfo
        const onDemandText = order.eatOptions && order.eatOptions.onDemandPromise ? order.eatOptions.onDemandPromise.message : ""


        const cartListWidgetPromise = this.getCartListWidget(interfaces, userContext, billingDetails,  orderItemsInfo, userContext.sessionInfo.userAgent, order, mealSlot)
        // this.logger.info("order.productSnapshots[0].isPack : " + order.productSnapshots[0].isPack)
        const addressWidgetPromise = this.buildAddressWidget(userContext, order.userAddress, order.userWeekendAddress, orderProductOption.weekendEnabled, order.productSnapshots[0].isPack, mealSlot)
        widgetViewPromises.push(addressWidgetPromise)


        const shouldStartDateWidget = EatUtil.isStartDateWidgetSupported(order.productSnapshots[0].isPack)
        if (shouldStartDateWidget) {
            const startDateWidgetPromise = this.buildStartDateWidget(orderProductOption.startDate, packDates, userContext, areaTz)
            widgetViewPromises.push(startDateWidgetPromise)
        }

        const deliverySlotWidget = Promise.resolve(this.getDeliverySlotWidget(orderProductOption.startDate, dateWiseAvailableSlots, deliveryInfo[0].deliverySlot, allSlots, arrivalInfo, onDemandText, mealSlot, areaTz, isDeliverySlotEmpty, false))
        widgetViewPromises.push(deliverySlotWidget)

        widgetViewPromises.push(...cartListWidgetPromise)

        const getBillingWidgetPromise = this.buildBillingWidget(userContext, order, billingDetails, request.isFitcashEnabled)

        widgetViewPromises.push(getBillingWidgetPromise)
        const widgets = await Promise.all(widgetViewPromises)
        const numProducts = _.sum(order.productSnapshots.map(p => { return p.quantity }))

        const total: number = billingDetails.billingInfo.total
        const pageActionRequest: PageActionRequest = {
            userContext: userContext,
            order: order,
            numProducts: numProducts,
            arrivalInfo: arrivalInfo,
            onDemandText: onDemandText,
            mealSlotDeliverySlots: mealSlotDeliverySlots,
            total: total,
            deliveryInfo: deliveryInfo,
            isCafe: false,
            isDeliverySlotEmpty: isDeliverySlotEmpty,
            allSlots: allSlots,
            billingDetails: billingDetails,
            mealSlot: mealSlot,
            currency: billingDetails.billingInfo.currency,
            isFitcashEnabled: request.isFitcashEnabled
        }
        const action: Action | DeliverySlotAction = await this.buildPageAction(pageActionRequest)

        const isOfferOverriden: boolean = eatApiResponse.cartReviewWrapperResponse.isOfferOverriden
        return {
            widgets: widgets,
            pageActions: [action],
            actions: [action],
            alertInfo: this.getAlertInfo(alertStatus, deliveryInfo, cartReviewResult.availabilities, order.productSnapshots, numProducts, user, userContext, isOfferOverriden, updateLog),
            cart: {
                deliveryInfos: deliveryInfo,
                startDate: order.productSnapshots[0].option.startDate
            }
        }
    }
}

export class MarketPlace extends IEatBrand {
    private getCutleryWidget(order: Order, mealSlot: MenuType) {
        const isCutleryEnabled: boolean = order.userAddress.eatDeliveryInstruction.cutleryInstruction === "GIVE_CUTLERY"
        const subtitle: string = order.packagingCharges  ? "Refuse cutlery, save ₹3 and go eco-friendly!" : "Refuse cutlery and go eco-friendly"
        return this.buildCutleryWidget(mealSlot, isCutleryEnabled, subtitle)
    }

    private async getFcStoreWidget(fc: FulfilmentCenter): Promise<FCStoreWidget> {
        const retailerName = fc.name
        const retailerId = fc.retailerId
        const image = ImagePathBuilder.getRetailerImagePath(retailerId, RetailerImageCategory.LOGO, 2)
        const address = fc.address.addressString
        return new FCStoreWidget(retailerName, image, address)
    }

    private async getDeliveryInstructionWidget(instruction: string): Promise<DeliveryInstructionWidget> {
        const title: string = "Any Cooking Instructions?"
        const notes: string = !_.isEmpty(instruction) ? instruction : undefined
        const hint: string = "E.g. Please call me when you arrive"
        return new DeliveryInstructionWidget(title, hint, notes)
    }

    private getEatMarketplacePageAction(userContext: UserContext, fc: FulfilmentCenter, cartReviewPayload: CartReviewPayload, order: Order, billingInfo: BillingInfo, isAddressServiceable: boolean): Action {
        let action: Action
        if ( !userContext.sessionInfo.isUserLoggedIn) {
            action = {
                title: "Login to continue",
                actionType: "SHOW_LOGIN_MODAL",
                url: "curefit://loginmodal",
            }
            return action
        }
        const addressId = !_.isNil(cartReviewPayload.option.deliveryInfo) ? cartReviewPayload.option.deliveryInfo[0].addressId : undefined
        const totalAmountPayable = order.totalAmountPayable
        const currency = billingInfo.currency
        if (_.isNil(addressId) || !isAddressServiceable) {
            action = {
                actionType: "CHANGE_ADDRESS",
                url: "curefit://selectaddress?pageFrom=cart_checkout",
                meta: {
                    fcId: fc.fcId,
                    retailerId: fc.retailerId,
                    mealSlot: "ALL"
                },
                title: "Pick address"
            }
            return action
        }

        if (_.isNil(action)) {
            if (order.totalAmountPayable === 0) {
                action = {
                    title: "Get for free",
                    actionType: "PAY_FREE"
                }
            } else {
                action = {
                    title: `Pay ${AppUtil.getCurrencySymbol(currency)} ${totalAmountPayable}`,
                    actionType: "CART_PAY_NOW",
                    url: "curefit://payment",
                }
            }
        }
        return action
    }

    async build(interfaces: IServiceInterfaces, request: ReviewRequest, userContext: UserContext): Promise<CartReviewView> {
        const eatApiReviewResponse = await interfaces.eatApiClientService.getMarketplaceReviewResponse(request)
        const widgetViewPromises: Promise<WidgetView>[] = []

        const cartReviewPayload = request.cartReviewPayload
        const fc = eatApiReviewResponse.fc
        const order = eatApiReviewResponse.order
        const orderItems = eatApiReviewResponse.orderItems
        const billingInfo = eatApiReviewResponse.billingInfo
        const offersApplied = eatApiReviewResponse.offersApplied
        const mealSlot = "ALL"

        const orderProductOption = order.productSnapshots[0].option
        const billingDetails: BillingDetails = {
            billingInfo: billingInfo,
            orderTotal: billingInfo.total,
            offersApplied: offersApplied,
            extraChargeConfig: undefined
        }
        const instruction = cartReviewPayload.option.cookingInstructions
        const alertStatus = eatApiReviewResponse.alertStatus
        const alertInfo = AlertInfos.AlertInfo(alertStatus)
        const isAddressServiceable: boolean = (alertStatus !== "ADDRESS_OUT_OF_DELIVERY_AREA") ? true : false
        const cartListWidgetPromise = this.buildCartListWidget(userContext, order, billingDetails, orderItems)
        const billingWidgetPromise = this.buildBillingWidget(userContext, order, billingDetails)
        const deliveryInstructionPromise = this.getDeliveryInstructionWidget(instruction)
        const fcStoreWidgetPromise = this.getFcStoreWidget(fc)
        const addressWidgetPromise = this.buildAddressWidget(userContext, order.userAddress, order.userWeekendAddress, orderProductOption.weekendEnabled, order.productSnapshots[0].isPack, mealSlot)
        const cutleryWidgetPromise = this.getCutleryWidget(order, mealSlot)

        widgetViewPromises.push(fcStoreWidgetPromise)
        widgetViewPromises.push(cartListWidgetPromise)
        widgetViewPromises.push(deliveryInstructionPromise)
        widgetViewPromises.push(billingWidgetPromise)
        widgetViewPromises.push(addressWidgetPromise)
        widgetViewPromises.push(cutleryWidgetPromise)

        const widgets = await Promise.all(widgetViewPromises)
        const pageAction = this.getEatMarketplacePageAction(userContext, fc, cartReviewPayload, order, billingInfo, isAddressServiceable)
        const deliveryInfo = cartReviewPayload.option.deliveryInfo
        return {
            widgets: widgets,
            pageActions: [pageAction],
            actions: [pageAction],
            alertInfo: alertInfo,
            cart: {
                deliveryInfos: deliveryInfo,
                startDate: order.productSnapshots[0].option.startDate
            }
        }
    }
}

export class FoodMarketplace extends IEatBrand {
    private getCutleryWidgetV2(order: Order, mealSlot: MenuType, userContext: UserContext, cutleryInstructionFromClient: CutleryInstruction) {
        const isCutleryEnabled: boolean = order?.userAddress?.eatDeliveryInstruction?.cutleryInstruction === "GIVE_CUTLERY" ?? false
        const subtitle: string = "Refuse cutlery and go eco-friendly"
        return this.buildCutleryWidgetV2(mealSlot, isCutleryEnabled, subtitle, userContext, cutleryInstructionFromClient)
    }

    private getCartListWidget(userContext: UserContext, order: Order, productMap: { [id: string]: Product}) {

        const images: string[] = []
        const actions: Action[] = []
        const { productSnapshots, productTypeOptions } = order
        const { productWiseInfo } = productTypeOptions
        const productSnapshotKeyMap = _.keyBy(productSnapshots, p => FoodMarketplaceUtil.generateProductAddonVariantComboKey(p.productId, p.option))

        productWiseInfo.forEach((ps: FoodMarketplaceProductWiseInfo, index: number) => {
            const key = FoodMarketplaceUtil.generateProductAddonVariantComboKey(ps.productId, {
                addons: ps.addons,
                variants: ps.variants
            })
            // updatin price just for display
            productSnapshotKeyMap[key].price.listingPrice = Math.round(ps.priceWithoutTax)
            productSnapshotKeyMap[key].price.mrp = Math.round(ps.subtotal)

            const product = productMap[ps.productId]
            const image = product.imageUrl || FoodMarketplaceUtil.getDummyMealCardImageUrl(index)
            const action: Action = undefined // no action for foodmp as no pdp
            images.push(image)
            actions.push(action)
        })
        const orderItems =  this.buildCartListWidgetPayload(this.mapToOrderItem(_.values(productSnapshotKeyMap)), images, actions)
        return this.buildCartListWidget(userContext, order, undefined, orderItems)
    }

    private async getEatDeliveryInstructionWidget(order: Order, cartReviewPayload: CartReviewPayload, userContext: UserContext): Promise<DeliveryInstructionWidgetV2> {
        const modalInfo = {
            title: "CONTACTLESS DELIVERY",
            description: "Opt for contactless delivery and the meal will be placed on a clean surface outside your door or at the reception"
        }
        const isContactInstructionSupported = AppUtil.isCutlerySelectClientSideHandleSupported(userContext)
        const instructionList = EatDeliveryInstructionList
        const contactInstructionList = EatContactInstructionList
        let selectedIndex = 0
        if (isContactInstructionSupported) {
            selectedIndex = _.get(cartReviewPayload, "option.eatDeliveryInstruction") ? EatUtil.findEatDeliveryDropInstructionId(cartReviewPayload.option.eatDeliveryInstruction) : 2
        } else {
            selectedIndex = _.get(cartReviewPayload, "option.eatDeliveryInstruction") ? EatUtil.findEatDeliveryInstructionId(cartReviewPayload.option.eatDeliveryInstruction) : 2
        }
        const selectedContactIndex = _.get(cartReviewPayload, "option.eatDeliveryInstruction") ? EatUtil.findEatContactInstructionId(cartReviewPayload.option.eatDeliveryInstruction) : 0
        const deliveryInstruction: Instruction = instructionList[selectedIndex]
        const selectedContactInstruction = contactInstructionList[selectedContactIndex]
        const instructionPayload: Instruction = {
            displayName: deliveryInstruction.displayName,
            payload: {
                contactInstruction: selectedContactInstruction.payload.contactInstruction,
                dropInstruction: deliveryInstruction.payload.dropInstruction,
                cutleryInstruction: deliveryInstruction.payload.cutleryInstruction
            }
        }
        const deliveryInstructionWidget: DeliveryInstructionWidgetV2 = new DeliveryInstructionWidgetV2("", instructionList, isContactInstructionSupported ? instructionPayload : deliveryInstruction, modalInfo, contactInstructionList)
        return deliveryInstructionWidget
    }

    private skipAvailability(cartReviewPayload: CartReviewPayload): boolean {
        let skip = false
        if (_.isEmpty(cartReviewPayload?.option?.deliveryInfo[0]?.addressId)) {
            skip = true
        }
        return skip
    }

    private getPostProcessingBasedAlerts(userContext: UserContext, serviceabilityInfo: FoodMarketplaceServiceabilityInfo): AlertInfo {

        let alert: AlertInfo = undefined
        if (serviceabilityInfo && serviceabilityInfo?.isServiceable === false) {
            alert = AlertInfos.AlertInfo("ADDRESS_OUT_OF_DELIVERY_AREA", {
                listingBrand: "FOOD_MARKETPLACE",
            }, userContext )
            alert.meta = {
                // loading analytics
                ...(serviceabilityInfo ?? {}),
                listingBrand: "FOOD_MARKETPLACE",
                userId: userContext?.userProfile?.userId,
                city: userContext?.userProfile?.cityId
            }
        }
        return alert

    }

    private getAvailabilityBasedAlert(userContext: UserContext, serviceabilityInfo: FoodMarketplaceServiceabilityInfo, availabilityInfo: FoodMarketplaceAvailabilityInfo, catalogProducts: Product[] ) {

        if (!serviceabilityInfo && !availabilityInfo) {
            return undefined
        }

        let alert: AlertInfo
        const { unavailableItems, availableItems} = availabilityInfo

        if (_.isEmpty(availableItems)) {
            // complete unavailable case
            alert = AlertInfos.AlertInfo("ITEMS_UNAVAILABLE", {
                products: catalogProducts,
                listingBrand: "FOOD_MARKETPLACE",
                unAvailableProducts: unavailableItems
            }, userContext )
        } else if ( !_.isEmpty( unavailableItems)) {
            // partial unavailability case
            alert = AlertInfos.AlertInfo("SOME_ITEMS_UNAVAILABLE", {
                products: catalogProducts,
                listingBrand: "FOOD_MARKETPLACE",
                unAvailableProducts: unavailableItems
            }, userContext )
        }
        return alert
    }

    async build(interfaces: CFServiceInterfaces, request: ReviewRequest, userContext: UserContext): Promise<CartReviewView> {

        const { cartReviewPayload, latitude, longitude, preferredLocation } = request
        const { orderProducts, option } = cartReviewPayload
        const productMap: { [id: string]: Product} = await interfaces.catalogueService.getProductMap(_.map(orderProducts, o => o.productId))
        const products: Product[] = _.values(productMap)

        const skipAvailability = this.skipAvailability(cartReviewPayload)
        const availabilityResults = skipAvailability ? undefined : await this.getProductsAvailability(interfaces, cartReviewPayload, latitude, longitude, productMap, preferredLocation)

        const serviceabilityAlert = this.getAvailabilityBasedAlert(userContext, availabilityResults?.serviceabilityInfo, availabilityResults?.availabilityInfo, products)
        if (serviceabilityAlert) {
            // showing item based unserviceability from here
            return {
                widgets: [],
                alertInfo: serviceabilityAlert,
                cart: {
                    startDate: undefined,
                    deliveryInfos: option?.deliveryInfo
                }
            }
        }

        const alfredRequest = await FoodMarketplaceUtil.generateAlfredRequest(request, userContext)
        // interfaces.logger.info(`Alfred Request:=== ${JSON.stringify(alfredRequest)}`)
        const { order } = await interfaces.orderService.cartReview(alfredRequest).catch((e: Error) => {
            throw this.errorFactory.withCode("FOODMP CART BREAK AT ALFRED", 500).withMessage(e.message).build()
        })
        const currency = order.productSnapshots[0].price.currency || "INR"
        const numProducts = _.sum(order.productSnapshots.map(p => { return p.quantity }))

        const pageActionRequest: PageActionRequest = {
            userContext: userContext,
            order: order,
            numProducts: numProducts,
            total: order.totalAmountPayable,
            deliveryInfo: option.deliveryInfo,
            isCafe: false,
            currency: currency,
            isFitcashEnabled: request.isFitcashEnabled,
            isCheckoutV2Supported: true,
            address: !_.isNil(order.userAddress) && availabilityResults?.serviceabilityInfo?.isServiceable === true ? order.userAddress : undefined
            // handling the case where the user address is unserviceable
        }

        const action = this.getPageAction(pageActionRequest)
        return {
            ...(await this.buildCartView(interfaces, userContext, order, request, productMap)),
            actions: [action],
            pageActions: [action],
            alertInfo: this.getPostProcessingBasedAlerts(userContext, availabilityResults?.serviceabilityInfo),
            cart: {
                startDate: undefined, // todo can break
                deliveryInfos: option?.deliveryInfo
            },
            total: `${AppUtil.getCurrencySymbol(currency)} ${order.totalPayable}`,
            totalWithoutCurrency: order.totalPayable

        }
    }

    private getPageAction(pageActionRequest: PageActionRequest): Action {

        const userContext = pageActionRequest.userContext
        const order = pageActionRequest.order
        const numProducts = pageActionRequest.numProducts
        const currency = pageActionRequest.currency
        const address = pageActionRequest.address

        let action: Action
        if (!userContext.sessionInfo.isUserLoggedIn) {
            action = {
                actionType: "SHOW_ALERT_MODAL",
                title: "Get pack", meta: {
                    title: "Login Required!",
                    subTitle: "Please login to continue",
                    actions: [{ actionType: "LOGOUT", title: "Login" }]
                }
            }
            return action
        }
        if (numProducts === 0) {
            // Handling case where there 0 items due to checkout
            const outletId = order?.productTypeOptions?.vendor?.outletId
            action = {
                actionType: "POP_ACTION",
                url: outletId ? "curefit://eatfitclp?outletId=" + outletId : "curefit://tabpage?pageId=foodmpoutletlist",
                title: "Ok"
            }
        }

        if (pageActionRequest.isCheckoutV2Supported && !address ) {
            action = {
                actionType: "SHOW_CART_ADDRESS_MODAL",
                title: "Select Address",
                meta: {
                    mealSlot: undefined, // todo: can break
                    navigateToSlotPicker: false
                }
            }
        }
        if (_.isNil(action)) {
            const { totalPayable } = order
            action = {
                title: `Pay ${AppUtil.getCurrencySymbol(currency)} ${totalPayable}`,
                actionType: "CART_PAY_NOW",
                url: "curefit://payment"
            }
        }
        return action

    }

    private async buildCartView(interfaces: CFServiceInterfaces, userContext: UserContext, order: Order, request: ReviewRequest, productMap: { [id: string]: Product}): Promise<{
        widgets: ( IBaseWidget | WidgetView )[]
        showDivider: boolean
    }> {

        const { cartReviewPayload } = request
        const widgetViewPromises: Promise<WidgetView | BaseWidget>[] = []


        const cartListWidgetPromise = this.getCartListWidget(userContext, order, productMap)
        widgetViewPromises.push(cartListWidgetPromise)

        const shouldShowAddressWidget = EatUtil.isAddressWidgetSupported(false)
        const shouldShowCutleryWidget = EatUtil.isCutleryWidgetSupported(false, false, "FOOD_MARKETPLACE")
        // const shouldShowHCMTippingWidget = await AppUtil.isHCMTippingSupported(userContext, order, userPromise, interfaces)

        if (shouldShowCutleryWidget) {
            const cutleryInstructionFromClient: CutleryInstruction = _.get(request, "cartReviewPayload.option.deliveryInfo[0].cutleryInstruction", undefined)
            const cutleryWidgetPromise = this.getCutleryWidgetV2(order, "ALL", userContext, cutleryInstructionFromClient)
            widgetViewPromises.push(cutleryWidgetPromise)
        }

        if (AppUtil.isEatDeliveryInstructionSupported(userContext)) {
            const deliveryInstructionWidgetPromise = this.getEatDeliveryInstructionWidget(order, cartReviewPayload, userContext)
            widgetViewPromises.push(deliveryInstructionWidgetPromise)
        }

        const eatAddressWidget = []
        if (shouldShowAddressWidget && !_.isEmpty(order.userAddress)) {
            const addressWidgetPromise = this.buildAddressWidget(userContext, order.userAddress, undefined, false, order.productSnapshots[0].isPack, undefined, this.showCartAddressModalAction(undefined, false), "ADDRESS_WIDGET_V2")
            eatAddressWidget.push(addressWidgetPromise)
        }
        if (eatAddressWidget.length > 0) {
            const eatAddressWidgetComponents = await Promise.all(eatAddressWidget)
            const eatAddressandDeliverySlotWidget = new EatAddressWidget(eatAddressWidgetComponents).buildView(interfaces, userContext, [])
            widgetViewPromises.push(eatAddressandDeliverySlotWidget)
        }

        if (AppUtil.isFoodMarketplaceCartAddonSupported(userContext)) {
            widgetViewPromises.push(this.buildAddonWidget(interfaces, request))
        }

        const getBillingWidgetPromise = this.buildBillingWidget(userContext, order)
        widgetViewPromises.push(getBillingWidgetPromise)

        return  {
            widgets: _.compact(await Promise.all(widgetViewPromises)),
            showDivider: false
        }
    }

    private getOutletIdFromCartPayload(cartReviewPayload: CartReviewPayload) {
        return cartReviewPayload?.option?.vendor?.outletId
    }

    public async buildAddonWidget(interfaces: CFServiceInterfaces, request: ReviewRequest ): Promise<WidgetView> {
        const { brandId, outletId } = request?.cartReviewPayload?.option?.vendor
        const { productIds = [], addonVariantAvailabilityMap = { } } = await interfaces.foodwayService.getAddonForOutlet( { outletId })
        if (_.isEmpty(productIds)) {
            return undefined
        }

        const addonItems: AddonItem[] = []
        const map: { [id: string]: FoodProduct } = await interfaces.catalogueService.getProductMap(productIds)

        for (let i = 0; i < productIds.length; i++) {
            const product = map[productIds[i]]
            const offerAndPrice = product.price // todo offer considerations
            // let offerIds
            // if (!_.isEmpty(offerAndPrice.offers)) {
            //     offerIds = _.map(offerAndPrice.offers, offer => {
            //         return offer.offerId
            //     })
            // }
            product.price = offerAndPrice
            FoodMarketplaceUtil.setAddonVariantAvailabilityInProduct(product, addonVariantAvailabilityMap)
            const actions: (Action | MealAction)[] = [ FoodMarketplaceUtil.getFoodCardAction(product, outletId, brandId, "", MAX_CART_SIZE) ]
            const addonItem: AddonItem = {
                image: product.imageUrl,
                title: product.title,
                productId: product.productId,
                price: product.price, // todo integrate offer service here
                stock: MAX_CART_SIZE, // todo put a stock check here
                calories: undefined, // todo wire this `${product.attributes.nutritionInfo.Calories["Total Calories"]} cal`,
                actions: actions,
                isVeg: product.foodType === "Veg",
                isInventorySet: true, // this should not be needed as we check the inventory in backend
                recommended: false // todo maybe need a logic here
            }
            addonItems.push(addonItem)

        }
        if (_.isEmpty(addonItems))
            return undefined
        else {
            const addonListWidget: any = {
                title: "People also add",
                subTitle: undefined,
                addonItems: addonItems,
                widgetType: "ADDON_LIST_WIDGET"
            }
            return addonListWidget
        }
    }

    async buildBillingWidget(userContext: UserContext, order: Order): Promise<BillingWidget> {

        let { productTypeOptions: { totalDiscount = 0 } } = order
        const { productTypeOptions: { listingBrand = "FOOD_MARKETPLACE" } } = order
        const noDeliveryChargeOfferPresent = await OffersUtil.isFreeDeliveryOfferPresent(this.offerServiceV3, order.offersApplied || [])
        const extraChargeConfig = await this.configService.getConfig("FOOD_MARKETPLACE_CONFIG")
        const deliveryChargeForBrand = _.get(extraChargeConfig, "configs.deliveryCharge." + listingBrand, FoodMarketplaceUtil.FOOD_MARKETPLACE_DEFAULT_DELIVERY_CHARGE)
        const priceDetails = await this.getFoodMarketplaceOrderPriceDetails(userContext, order, noDeliveryChargeOfferPresent, deliveryChargeForBrand)
        if (noDeliveryChargeOfferPresent) {
            totalDiscount += deliveryChargeForBrand
        }

        const widget: BillingWidget = {
            widgetType: "BILLING_WIDGET",
            title: `Billing Details`,
            priceDetails: priceDetails.components,
            cartValue: priceDetails.cartValue,
            fitClubSavings: undefined,
            fitcashBalance: undefined, // check
            isFitcashEnabled: undefined, // check
            currency: priceDetails.currency,
            deliveryTipTitle: undefined // for later
        }
        if (totalDiscount > 0) {
            widget.totalSaving = {
                iconUrl: CdnUtil.getCdnUrl("curefit-content/image/payment/discount_green.png"),
                data: [
                    { text: "Total Saving of " },
                    {
                        text: `${AppUtil.getCurrencySymbol(priceDetails.currency)}${totalDiscount.toFixed(2)}`,
                        style: {
                            fontFamily: "BrandonText-Bold",
                        },
                    },
                ]
            }
        }
        return widget

    }

    private async getFoodMarketplaceOrderPriceDetails(userContext: UserContext, order: Order, noDeliveryChargeOfferPresent: boolean = false, deliveryChargeForBrand: number = 0): Promise<{
        components: PriceComponent[],
        cartValue: number
        currency: string
    }> {
        const { productTypeOptions, deliveryCharges, totalPayable } = order
        const { totalPartnerTaxes = 0, totalPartnerCharges = 0, subtotal: totalPayableAmountWithoutTax = 0, totalDiscount = 0 } = productTypeOptions
        const currency = order.productSnapshots[0].price.currency

        const priceDetails: PriceComponent[] = []

        priceDetails.push({
            title: "Item Total",
            value:  totalPayableAmountWithoutTax.toFixed(2),
            valueWithCurrency: `${AppUtil.getCurrencySymbol(currency)} ${totalPayableAmountWithoutTax.toFixed(2)}`
        })

        if (totalDiscount > 0) {
            priceDetails.push({
                title: "Discount",
                value: totalDiscount.toFixed(2),
                valueWithCurrency: `-${AppUtil.getCurrencySymbol(currency)}${totalDiscount.toFixed(2)}`,
                isDiscount: true
            })
        }

        if (deliveryCharges) {
            priceDetails.push({
                title: `Delivery charge`,
                value: deliveryCharges.total.toFixed(2),
                valueWithCurrency: `${AppUtil.getCurrencySymbol(currency)}${deliveryCharges.total.toFixed(2)}`
                })
        } else {
            // assuming if the object is not present, it must have been waived off because of discount
            if (noDeliveryChargeOfferPresent && deliveryChargeForBrand > 0) {
                priceDetails.push({
                    title: `Delivery charge`,
                    value: deliveryChargeForBrand + "",
                    valueWithCurrency: `${AppUtil.getCurrencySymbol(currency)}${deliveryChargeForBrand + ""}`,
                    offerText: "FREE",
                    isValueStrikeOff: true,
                    isDiscount: true
                })
            }
        }

        if (totalPartnerTaxes > 0) {
            priceDetails.push({
                title: `GST`,
                value: Math.floor(totalPartnerTaxes)?.toFixed(2),
                valueWithCurrency: `${AppUtil.getCurrencySymbol(currency)}${Math.round(totalPartnerTaxes)?.toFixed(2)}`
            })
        }

        if (totalPartnerCharges > 0) {
            priceDetails.push({
                title: `Packaging Charges`,
                value: Math.floor(totalPartnerCharges)?.toFixed(2),
                valueWithCurrency: `${AppUtil.getCurrencySymbol(currency)}${Math.round(totalPartnerCharges)?.toFixed(2)}`
            })
        }

        priceDetails.push({
            title: "Total Payable",
            value: totalPayable.toFixed(2),
            valueWithCurrency: `${AppUtil.getCurrencySymbol(currency)}${totalPayable.toFixed(2)}`
        })


        return {
            components: priceDetails,
            cartValue: totalPayable,
            currency
        }
    }

    private mapToOrderItem(productSnapShots: GearProductSnapshots[]): OrderItemInfo[] {
        return _.compact(_.map(productSnapShots, (p: GearProductSnapshots) => {
            return {
                isVeg: p.foodType === "Veg",
                offerId: undefined, // todo for now
                productId: p.productId,
                quantity: p.quantity,
                calories: undefined,
                price: p.price,
                isPack: undefined,
                productName: p.title,
                productNameWithoutUnits: p.title,
                productType: p.productType,
                option: p.option,
                stock: FoodMarketplaceUtil.FOODMP_MAX_CART_SIZE,
                parentProductId: p.productId,
                comboOfferId: undefined,
                servingUnit: undefined,
                servingQty: undefined,
                maxCartQty: p.maxCartQty || FoodMarketplaceUtil.FOODMP_MAX_CART_SIZE,
                displayUnitQty: undefined,
                variantTitle: FoodMarketplaceUtil.getVariantTitle(p.option),
                imageVersion: undefined,
                categoryId: p.categoryId,
                c1CategoryId: p.categoryId
            } as OrderItemInfo
        }))
    }

    private async getProductsAvailability(interfaces: CFServiceInterfaces, cartReviewPayload: CartReviewPayload, lat: number, long: number, productMap: { [id: string]: Product}, preferredLocation: PreferredLocation): Promise<{
        serviceabilityInfo?: FoodMarketplaceServiceabilityInfo
        availabilityInfo?: FoodMarketplaceAvailabilityInfo
    }> {
        let unserviceable = false
        const availabilityRequest = this.generateFoodwayServiceabilityPayload(cartReviewPayload, preferredLocation)
        let availability: ProductAvailability[]
        const availableItems: ItemDetails[] = []
        const unavailableItems: ItemDetails[] = []

        try {
           const productAvailabilityResponse = await interfaces.foodwayService.getAvailabilityForProducts(availabilityRequest)
            availability = productAvailabilityResponse.productAvailabilities
            unserviceable = !productAvailabilityResponse.outletIsDeliverable
        } catch (e) {
            interfaces.logger.error("FOODMP Prod availability failed for " + JSON.stringify(availabilityRequest), e)
            throw e
        }

        for ( let i = 0; i < availability.length; i = i + 1) {
            const productEntry = productMap[availability[i].productId]
            if (!availability[i].available) {
                unavailableItems.push({
                    title: productEntry.title,
                    productId: availability[i].productId
                })
            } else {
                availableItems.push({
                    title: productEntry.title,
                    productId: availability[i].productId
                })
            }
        }

       return {
            serviceabilityInfo: {
                isServiceable: !unserviceable,
                latLong: availabilityRequest?.latLong,
                addressId: preferredLocation?.address?.addressId,
                outletId: availabilityRequest.outletId
            },
           availabilityInfo: {
                availableItems: availableItems,
                unavailableItems: unavailableItems
           }
       }
    }

    private generateFoodwayServiceabilityPayload(cartReviewPayload: CartReviewPayload, preferredLocation: PreferredLocation): ProductAvailabilityRequest {
        const { orderProducts } = cartReviewPayload
        const latLong: LatLong = {
            lat: preferredLocation.latLong.lat,
            long: preferredLocation.latLong.long
        }

        const request: ProductAvailabilityRequest = {
            orderProducts: orderProducts,
            outletId: this.getOutletIdFromCartPayload(cartReviewPayload),
            latLong: latLong
        }

        return request
    }
}

export class CultBike extends IEatBrand {

    private getPostProcessingBasedAlerts(userContext: UserContext, serviceabilityInfo: {isServiceable: boolean}): AlertInfo {

        let alert: AlertInfo = undefined
        if (serviceabilityInfo?.isServiceable === false) {
            alert = AlertInfos.AlertInfo("ADDRESS_OUT_OF_DELIVERY_AREA", {
                listingBrand: "CULT_BIKE" as any,
            }, userContext )
            alert.meta = {
                // loading analytics
                ...(serviceabilityInfo ?? {}),
                listingBrand: "CULT_BIKE",
                userId: userContext?.userProfile?.userId,
                city: userContext?.userProfile?.cityId
            }
        }
        return alert

    }

    async build(interfaces: CFServiceInterfaces, request: ReviewRequest, userContext: UserContext): Promise<CartReviewView> {

        const { cartReviewPayload, latitude, longitude, preferredLocation } = request
        const { orderProducts, option } = cartReviewPayload
        const products: Product[] = await interfaces.catalogueService.getProducts(request.cartReviewPayload.orderProducts.map(product => product.productId))

        const alfredRequest = await this.generateAlfredRequest(request, userContext)
        const { order } = await interfaces.orderService.cartReview(alfredRequest).catch((e: Error) => {
            throw this.errorFactory.withCode("CULT BIKE CART BREAK AT ALFRED", 500).withMessage(e.message).build()
        })

        const availabilityResults = order?.userAddress && await this.getProductsAvailability(userContext, interfaces, cartReviewPayload, order, preferredLocation)
        const currency = order.productSnapshots[0].price.currency || "INR"
        const numProducts = _.sum(order.productSnapshots.map(p => { return p.quantity }))

        const pageActionRequest: PageActionRequest = {
            userContext: userContext,
            order: order,
            numProducts: numProducts,
            total: order.totalAmountPayable,
            deliveryInfo: option.deliveryInfo,
            isCafe: false,
            currency: currency,
            isFitcashEnabled: request.isFitcashEnabled,
            isCheckoutV2Supported: true,
            address: !_.isNil(order.userAddress) ? order.userAddress : undefined
        }

        let fitcashBalance: number = 0
            try {
                if (userContext.sessionInfo.isUserLoggedIn) {
                    const fitcashBalanceResponse: WalletBalance = await interfaces.fitcashService.balance(userContext.userProfile.userId, userContext.userProfile.city.country.currencyCode)
                    fitcashBalance = fitcashBalanceResponse.balance / 100 // paise to rupee
                }
            } catch (error) {
                const genericErr: GenericError = new GenericError({message: error.message})
                genericErr.statusCode = 500
               interfaces.rollbarService.handleError(genericErr)
            }

        const action = this.getPageAction(pageActionRequest, fitcashBalance, availabilityResults)
        return {
            ...(await this.buildCartView(interfaces, userContext, order, request, products[0], fitcashBalance)),
            actions: [action],
            pageActions: [action],
            alertInfo: this.getPostProcessingBasedAlerts(userContext, availabilityResults?.serviceabilityInfo),
            cart: {
                startDate: undefined, // todo can break
                deliveryInfos: option?.deliveryInfo
            },
            total: `${AppUtil.getCurrencySymbol(currency)} ${order.totalPayable}`,
            totalWithoutCurrency: order.totalPayable

        }
    }

    private getPageAction(pageActionRequest: PageActionRequest, fitcashBalance?: number, serviceability?: {
        serviceabilityInfo?: {
            isServiceable: boolean;
            addressId?: string;
        };
    }): Action {

        const userContext = pageActionRequest.userContext
        const order = pageActionRequest.order
        const numProducts = pageActionRequest.numProducts
        const currency = pageActionRequest.currency
        const address = pageActionRequest.address

        let action: Action
        if (!userContext.sessionInfo.isUserLoggedIn) {
            action = {
                actionType: "SHOW_ALERT_MODAL",
                title: "Login", meta: {
                    title: "Login Required!",
                    subTitle: "Please login to continue",
                    actions: [{ actionType: "LOGOUT", title: "Login" }]
                }
            }
            return action
        }
        if (numProducts === 0) {
            action = {
                actionType: "POP_ACTION",
                title: "Ok"
            }
        }

        if (!address ) {
            action = {
                actionType: "SHOW_CART_ADDRESS_MODAL",
                title: "Select Address",
                meta: {
                    mealSlot: undefined, // todo: can break
                    navigateToSlotPicker: false
                }
            }
        }

        if (address && serviceability?.serviceabilityInfo?.isServiceable === false) {
            action = {
                actionType: "SHOW_CART_ADDRESS_MODAL",
                title: "Change Address",
                meta: {
                    mealSlot: undefined, // todo: can break
                    navigateToSlotPicker: false
                }
            }
        }
        if (_.isNil(action)) {
            const totalAmountPayable = order.totalAmountPayable
            const toalFitCashPayable = order.totalFitCashPayable
            if ((totalAmountPayable + toalFitCashPayable) === 0) {
                action = {
                    title: "Get for free",
                    actionType: "PAY_FREE"
                }
            } else {
                action = {
                    title: `Pay ${AppUtil.getCurrencySymbol(currency)} ${totalAmountPayable}`,
                    actionType: "CART_PAY_NOW",
                    url: "curefit://payment"
                }
            }
        }
        return action

    }

    private async generateAlfredRequest(request: ReviewRequest, userContext: UserContext): Promise<OrderCreate> {

        const { cartReviewPayload, userDetailInfo, sessionData } = request
        const { userProfile, sessionInfo } = userContext
        const { deliveryInfo, eatDeliveryInstruction: eatDeliveryInstructionFromClient } = cartReviewPayload.option

        // let defaultDeliveryInstruction: DeliveryInstruction = {
        //     dropInstruction: "ME",
        //     contactInstruction: "CALL_ME",
        //     cutleryInstruction: "GIVE_CUTLERY"
        // }
        // if (eatDeliveryInstructionFromClient) {
        //     defaultDeliveryInstruction = {
        //         ...defaultDeliveryInstruction,
        //         ...eatDeliveryInstructionFromClient,
        //     }
        // }
        const userBusinessObject = kernel.get<IUserBusiness>(CUREFIT_API_TYPES.UserBusiness) // figure a better way out
        const orderCreate: OrderCreate = {
            userId: userProfile.userId,
            deviceId: sessionInfo.deviceId,
            products: request.cartReviewPayload.orderProducts,
            source: userDetailInfo.orderSource,
            cityId: sessionData.cityId,
            useOffersV2: false,
            dontCreateRazorpayOrder: true,
            useFitCash: cartReviewPayload.useFitCash ? cartReviewPayload.useFitCash : request.isFitcashEnabled || false,
            deliveryInfo: await Promise.all(_.map(deliveryInfo, async (info): Promise<DeliveryInfo> => {
                let address: UserDeliveryAddress
                if (info.addressId) {
                    address = await userBusinessObject.augmentStructuredAddress(userContext.userProfile.userId, info.addressId)
                    // const cutleryInstruction = info.cutleryInstruction
                    // address.eatDeliveryInstruction = {
                    //     ...defaultDeliveryInstruction,
                    //     cutleryInstruction: cutleryInstruction || defaultDeliveryInstruction.cutleryInstruction
                    // }
                }
                return {
                    address: address,
                    mealSlot: "ALL" // hack for making cultery widget work
                }
            })),
            listingBrand: "CULT_BIKE" as any,
            // tipAmount: !_.isNil(deliveryTip) ? this.computeDeliveryTip(deliveryTip) : undefined, // maybe later?
            tenant: AppUtil.getTenantFromUserContext(userContext),
            reserve: false,
            offersVersion: 3
        }

        return orderCreate
    }

    private async buildCartView(interfaces: CFServiceInterfaces, userContext: UserContext, order: Order, request: ReviewRequest, product: Product, fitcashBalance?: number): Promise<{
        widgets: ( IBaseWidget | WidgetView )[]
        showDivider: boolean
    }> {

        const { cartReviewPayload } = request
        const widgetViewPromises: Promise<WidgetView | BaseWidget>[] = []
        widgetViewPromises.push(this.getSummaryWidget(product))
        const getBillingWidgetPromise = this.getBillingWidget(userContext, order, request.cartReviewPayload?.option?.isFitcashEnabled, fitcashBalance)
        widgetViewPromises.push(getBillingWidgetPromise)
        if (!_.isEmpty(order?.offersApplied)) {
            const offers = _.values((await interfaces.offerServiceV3.getOffersByIds(order.offersApplied)).data)
            widgetViewPromises.push(Promise.resolve(CareUtil.getCareOfferWidget("Offers Applied", offers, userContext)))
        }
        widgetViewPromises.push(this.getWhatyouGetWidget(userContext))



        const eatAddressWidget = []
        if (!_.isEmpty(order.userAddress)) {
            const addressWidgetPromise = this.buildAddressWidget(userContext, order.userAddress, undefined, false, order.productSnapshots[0].isPack, undefined, this.showCartAddressModalAction(undefined, false), "ADDRESS_WIDGET_V2")
            eatAddressWidget.push(addressWidgetPromise)
        }
        if (eatAddressWidget.length > 0) {
            const eatAddressWidgetComponents = await Promise.all(eatAddressWidget)
            const eatAddressandDeliverySlotWidget = new EatAddressWidget(eatAddressWidgetComponents).buildView(interfaces, userContext, [])
            widgetViewPromises.push(eatAddressandDeliverySlotWidget)
        }
        return  {
            widgets: _.compact(await Promise.all(widgetViewPromises)),
            showDivider: false
        }
    }

    private async getWhatyouGetWidget(userContext: UserContext) {
        return CareUtil.getWhatYouGetInstructionWidget({
            type: "WHAT_YOU_GET",
            title: "What you get",
            children: [
                {
                    "desc": "Free delivery within 7 business days and free installation",
                    imageUrl: "image/vm/ff504082-a8f0-44dc-a5ff-221e9c72dcb4.png",
                },
                {
                    "desc": "Get 1 year warranty and free content membership till March 2023",
                    imageUrl: "image/vm/ff504082-a8f0-44dc-a5ff-221e9c72dcb4.png",
                },
                {
                    "desc": "10 days FREE TRIAL and full refund. No questions asked",
                    imageUrl: "image/vm/ff504082-a8f0-44dc-a5ff-221e9c72dcb4.png",
                },
                {
                    "desc": "No cost EMI available",
                    imageUrl: "image/vm/ff504082-a8f0-44dc-a5ff-221e9c72dcb4.png",
                }
            ]
        }, userContext)
    }

    private async getSummaryWidget(product: Product): Promise<any> {
        const checkupSummaryWidget: WidgetView & {
            productId: string;
            title: string;
            image: string;
            subTitle: string;
            hasDividerBelow: boolean;
        } = {
            widgetType: "HEALTHCHECKUP_SUMMARY_WIDGET",
            title: product.title || "cultbike",
            subTitle: "Your bike will be delivered in 7 business days",
            productId: product.productId,
            image: product.imageUrl,
            hasDividerBelow: false,
        }
        return checkupSummaryWidget
    }

    private async getCultBikeOrderPriceDetails(userContext: UserContext, order: Order, isFitcashEnabled?: boolean, fitcashBalance?: number): Promise<{
        components: PriceComponent[],
        cartValue: number
        currency: string
    }> {
        const { productTypeOptions, totalPayable, totalAmountPayable } = order
        const { subtotal, totalDiscount = 0, totalPartnerTaxes = 0 } = productTypeOptions
        const currency = order.productSnapshots[0].price.currency

        const priceDetails: PriceComponent[] = []

        priceDetails.push({
            title: "Item Total",
            value:  subtotal.toFixed(2),
            valueWithCurrency: `${AppUtil.getCurrencySymbol(currency)} ${subtotal.toFixed(2)}`
        })

        if (totalDiscount > 0) {
            priceDetails.push({
                title: "Discount",
                value: totalDiscount.toFixed(2),
                valueWithCurrency: `-${AppUtil.getCurrencySymbol(currency)}${totalDiscount.toFixed(2)}`,
                isDiscount: true
            })
        }

        if (totalPartnerTaxes > 0) {
            priceDetails.push({
                title: `GST`,
                value: Math.floor(totalPartnerTaxes)?.toFixed(2),
                valueWithCurrency: `${AppUtil.getCurrencySymbol(currency)}${Math.round(totalPartnerTaxes)?.toFixed(2)}`
            })
        }

        // Removed fitcash option
        // if (totalFitCashPayable >= 0 || fitcashBalance >= 0) {
        //     priceDetails.push({
        //         title: "Use FitCash",
        //         isFitcashPrice: true,
        //         isEnabled: isFitcashEnabled,
        //         fitcashBalance: fitcashBalance,
        //         value: totalFitCashPayable.toFixed(2),
        //         valueWithCurrency: `${AppUtil.getCurrencySymbol(currency)}${totalFitCashPayable.toFixed(2)}`
        //     })
        // }

        priceDetails.push({
            title: "Total Payable",
            value: totalAmountPayable.toFixed(2),
            valueWithCurrency: `${AppUtil.getCurrencySymbol(currency)}${totalAmountPayable.toFixed(2)}`
        })


        return {
            components: priceDetails,
            cartValue: totalAmountPayable,
            currency,
        }
    }


    async getBillingWidget(userContext: UserContext, order: Order, isFitcashEnabled?: boolean, fitcashBalance?: number): Promise<BillingWidget> {

        const priceDetails = await this.getCultBikeOrderPriceDetails(userContext, order, isFitcashEnabled, fitcashBalance)
        const widget: BillingWidget = {
            widgetType: "BILLING_WIDGET",
            title: undefined,
            priceDetails: priceDetails.components,
            cartValue: priceDetails.cartValue,
            fitClubSavings: undefined,
            fitcashBalance: fitcashBalance, // check
            isFitcashEnabled: isFitcashEnabled, // check
            currency: priceDetails.currency,
            deliveryTipTitle: undefined, // for later
            containerStyle: {
                paddingTop: 20,
                paddingBottom: 20,
            }
        }
        return widget

    }


    private async getProductsAvailability(userContext: UserContext, interfaces: CFServiceInterfaces, cartReviewPayload: CartReviewPayload, order: Order, preferredLocation: PreferredLocation): Promise<{
        serviceabilityInfo?: {isServiceable: boolean, addressId?: string}
    }> {
       return {
            serviceabilityInfo: {
                isServiceable: !_.isEmpty(await interfaces.segmentService.doesUserBelongToSegment(AppUtil.cultBikeServiceabilitySegment(), userContext)), // await interfaces.cultBikeClient.getProductServiceability(), // (order.userAddress?.structuredAddress?.city),
                addressId: order?.userAddress?.addressId || cartReviewPayload?.option?.addressId,
            }
       }
    }


}


export class FoodMarketplaceAvailabilityInfo {
    availableItems: {
        title: string,
        productId: string
        addons?: FoodMarketplaceVariantAddonInfo[]
        variants?: FoodMarketplaceVariantAddonInfo[]
    }[]
    unavailableItems: {
        title: string,
        productId: string
        addons?: FoodMarketplaceVariantAddonInfo[]
        variants?: FoodMarketplaceVariantAddonInfo[]
    }[]
}

export class FoodMarketplaceServiceabilityInfo {
    // todo use in future
    isServiceable: boolean
    latLong?: LatLong
    addressId?: string
    outletId: string
}
