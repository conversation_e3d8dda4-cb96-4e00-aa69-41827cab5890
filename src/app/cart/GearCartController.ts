import * as express from "express"
import * as _ from "lodash"
import { controller, httpPost } from "inversify-express-utils"
import { Container, inject } from "inversify"
import AuthMiddleware from "../auth/AuthMiddleware"
import { Logger, BASE_TYPES } from "@curefit/base"
import AppUtil from "../util/AppUtil"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import {
    OMS_API_CLIENT_TYPES,
    GearCartReviewRequest as OSGearCartReviewRequest,
    GearCartReviewResponse as OSGearCartReviewResponse,
    GearOrderCreateRequest as OSGearOrderCreateRequest,
    GearOrderCreateResponse as OSGearOrderCreateResponse,
    IOrderService
} from "@curefit/oms-api-client"
import IUserBusiness from "../user/IUserBusiness"
import { Session } from "@curefit/userinfo-common"
import { User, UserInfo } from "@curefit/user-common"
import { Tenant, UserAgentType as UserAgent } from "@curefit/base-common"
import { UserDeliveryAddress } from "@curefit/eat-common"
import { OrderProduct, OrderSource, CultsportMetadata } from "@curefit/order-common"
import { GearCartReviewView, GearCartViewBuilder, GearOrderCheckoutDetail } from "./GearCartViewBuilder"
import { UserContext } from "@curefit/userinfo-common"
import { CatalogueProduct } from "@curefit/gear-common"
import { GearUtil } from "@curefit/gearvault-client"
import GearCatalogueLandingPageService from "../page/GearCatalogueLandingPageService"
import { OFFER_SERVICE_CLIENT_TYPES, OfferServiceV3 } from "@curefit/offer-service-client"
import { IUserService, USER_CLIENT_TYPES } from "@curefit/user-client"
import { ErrorFactory } from "@curefit/error-client"
import { ErrorCodes } from "../error/ErrorCodes"
import { GearProductPricesResponse } from "@curefit/offer-common"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import MixpanelEventService from "../cultsport/MixpanelEventService"
import { PromiseCache } from "../util/VMUtil"


export interface GearCartReviewRequest {
    orderProducts: OrderProduct[]
    couponCode?: string
    option?: {
        addressId?: string
    }
    clientMetadata?: CultsportMetadata
}

export interface GearCartCheckoutRequest {
    orderProducts: OrderProduct[],
    addressId: string
    useFitCash?: boolean
    advertiserId?: string,
    couponCode?: string,
    clientMetadata?: CultsportMetadata
}

export function controllerFactory(kernel: Container) {
    @controller("/gear-cart/v1", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class GearCartController {
        constructor(
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(OMS_API_CLIENT_TYPES.OrderService) private omsApiClient: IOrderService,
            @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
            @inject(CUREFIT_API_TYPES.UserBusiness) private userBusiness: IUserBusiness,
            @inject(CUREFIT_API_TYPES.GearCartViewBuilder) private gearCartViewBuilder: GearCartViewBuilder,
            @inject(CUREFIT_API_TYPES.GearCatalogueLandingPageService) private gearCLPService: GearCatalogueLandingPageService,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
            @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) private offerServiceV3: OfferServiceV3,
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
            @inject(CUREFIT_API_TYPES.MixpanelEventService) protected mixpanelEventService: MixpanelEventService
        ) {

        }

        @httpPost("/review")
        async cartReview(req: express.Request): Promise<GearCartReviewView> {
            const session: Session = req.session
            const userId: string = session.userId
            const userAgent: UserAgent = req.session.userAgent
            const user = await this.userService.getUser(userId)
            const apiKey: string = req.headers["apikey"] as string
            const orderSource: OrderSource = AppUtil.callSource(apiKey)
            const deviceId: string = session.deviceId
            const requestPayload: GearCartReviewRequest = req.body
            const userContext: UserContext = req.userContext
            const timezone = userContext.userProfile.timezone
            const { couponCode } = requestPayload

            requestPayload.orderProducts = _.filter(requestPayload.orderProducts, (orderProduct) => orderProduct.quantity > 0)

            if (_.isEmpty(requestPayload.orderProducts)) {
                throw this.errorFactory.withCode(ErrorCodes.CART_EMPTY_ERR, 400).withDebugMessage("No items in cart").build()
            }

            userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)

            let address: UserDeliveryAddress

            if (requestPayload.option && requestPayload.option.addressId && !_.isEmpty(requestPayload.option.addressId)) {
                address = await this.userBusiness.augmentStructuredAddress(userId, requestPayload.option.addressId)
                await this.userBusiness.updatePincodeLocation(session, { addressId: address.addressId }, "GEAR")
            }
            const backFillCurefitAppOrderSourceClientMetaData = AppUtil.getTenantFromReq(req) === Tenant.CUREFIT_APP ? { orderSource: "CUREFIT_APP" } : undefined
            const clientMetadata = requestPayload.clientMetadata || backFillCurefitAppOrderSourceClientMetaData
            const orderServiceRequest: OSGearCartReviewRequest = {
                couponCode,
                user,
                deviceId,
                email: user.email,
                source: orderSource,
                products: requestPayload.orderProducts,
                address: address,
                clientMetadata
            }
            const showNewVoucher = await AppUtil.shouldUseNewVoucherFlow(userContext, this.serviceInterfaces.segmentService)

            const orderServiceResponse: OSGearCartReviewResponse = await this.omsApiClient.gearCartReview(orderServiceRequest)

            return this.gearCartViewBuilder.buildGearCartReviewView(userId, user, orderServiceResponse.order,
                orderServiceResponse.availabilities, req.userContext, orderServiceResponse.arrivalInfo, userAgent, timezone, true, showNewVoucher)
        }

        @httpPost("/checkout")
        async cartCheckout(req: express.Request): Promise<GearOrderCheckoutDetail> {
            const session: Session = req.session
            const userId: string = session.userId
            const apiKey: string = req.headers["apikey"] as string
            const orderSource: OrderSource = AppUtil.callSource(apiKey)
            const deviceId: string = session.deviceId
            const requestPayload: GearCartCheckoutRequest = req.body
            const addressId = requestPayload.addressId
            const userContext = req.userContext as UserContext
            const { couponCode } = requestPayload

            requestPayload.orderProducts = _.filter(requestPayload.orderProducts, (orderProduct) => orderProduct.quantity > 0)

            if (_.isEmpty(requestPayload.orderProducts)) {
                throw this.errorFactory.withCode(ErrorCodes.CART_EMPTY_ERR, 400).withDebugMessage("No items in cart").build()
            }

            let user: User, address: UserDeliveryAddress
            [user, address] = await Promise.all([
                this.userService.getUser(userId),
                this.userBusiness.getAddress(userId, addressId)
            ])

            if (!address) {
                throw this.errorFactory.withCode(ErrorCodes.NO_ADDRESS_ON_CHECKOUT_ERR, 400).withDebugMessage("Gear user delivery address not found").build()
            }
            userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)

            const result = AppUtil.getUserAlertInfo(user, userContext)
            const code: string = result.code
            if (!_.isEmpty(code)) {
                throw this.errorFactory.withCode(result.code, result.alertInfo.statusCode).build()
            }

            const countryId = _.get(userContext, "userProfile.city.countryId", null)
            if (countryId !== "IN") {
                requestPayload.useFitCash = false
            }
            const backFillCurefitAppOrderSourceClientMetaData = AppUtil.getTenantFromReq(req) === Tenant.CUREFIT_APP ? { orderSource: "CUREFIT_APP" } : undefined
            const clientMetadata = requestPayload.clientMetadata || backFillCurefitAppOrderSourceClientMetaData
            const orderServiceRequest: OSGearOrderCreateRequest = {
                user,
                deviceId,
                email: user.email,
                source: orderSource,
                products: requestPayload.orderProducts,
                address,
                couponCode,
                useFitCash: requestPayload.useFitCash ? requestPayload.useFitCash : false,
                clientMetadata: clientMetadata
            }
            if (!_.isNil(requestPayload.advertiserId)) orderServiceRequest.advertiserId = requestPayload.advertiserId

            const orderServiceResponse: OSGearOrderCreateResponse = await this.omsApiClient.gearCartCheckout(orderServiceRequest)
            this.mixpanelEventService.sendCartCheckoutEvents(userContext, orderServiceResponse.order)
            return this.gearCartViewBuilder.buildGearCartCheckoutView(user, orderServiceResponse.order)
        }
    }

    return GearCartController
}
export default controllerFactory
