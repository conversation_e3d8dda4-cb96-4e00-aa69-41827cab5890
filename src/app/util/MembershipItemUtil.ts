import { AttributeKeyType, Membership } from "@curefit/membership-commons"
import { UserContext } from "@curefit/userinfo-common"
import TimeUtil from "@curefit/util-common/dist/src/TimeUtil"
import { ProgressBar } from "../common/views/ProfileWidgetView"
import {
    PlayCitySLPMembershipInfo,
    PlayLimitedSessionMembershipInfo,
    PlaySLPMembershipInfo
} from "../pack/PlayPackDetailViewBuilder"
import PlayUtil from "./PlayUtil"
import * as _ from "lodash"
import { RollbarService } from "@curefit/error-common"
import { ProductType } from "@curefit/product-common"
import moment = require("moment")
import { ProductSubType } from "@curefit/pack-management-service-common"

export class MembershipItemUtil {
    public static getPrimaryColorForMembershipState(membershipState: string): string {
        switch (membershipState) {
            case "ACTIVE":
                return "statusGreen"
            case "UPCOMING":
                return "statusBlue"
            case "EXPIRING":
            case "PAUSED":
                return "statusYellow"
            case "EXPIRED":
                return "statusRed"
            default:
                return "transparent"
        }
    }

    public static isSelectMembership(membership: Membership): boolean {
        return membership?.attributes?.findIndex(a => a.attrKey == AttributeKeyType.ACCESS_CENTER.toString()) != -1
    }

    public static isPlusMembership(membership: Membership): boolean {
        return (membership?.metadata?.isAddonApplied && membership?.metadata?.addonType != null && membership?.metadata?.addonType == "BASE_ADDON")
            || (membership?.metadata?.productSubType === ProductSubType.PLUS)
    }

    public static isBoosterMembership(membership: Membership): boolean {
        return membership?.metadata?.isAddonApplied && membership?.metadata?.addonType != null && membership?.metadata?.addonType == "EXTERNAL_ADDON"
    }

    public static isPlaySportLevelMembership(membership: Membership): PlaySLPMembershipInfo {
        let isSLPPack = false
        let accessCenter = null
        let accessWorkout = null
        if (membership.attributes != null && membership.attributes.length > 1) {
            membership.attributes.map((benefit) => {
                if (benefit.attrKey == AttributeKeyType.ACCESS_CENTER) {
                    accessCenter = benefit.attrValue
                }
                if (benefit.attrKey == AttributeKeyType.ACCESS_SPORT_WORKOUT) {
                    accessWorkout = benefit.attrValue
                }
            })
            if (accessCenter != null && accessWorkout != null)
                isSLPPack = true
        }
        return {
            isSLPPack,
            accessCenter,
            accessWorkout
        }
    }

    public static isPlaySportCityLevelMembership(membership: Membership): PlayCitySLPMembershipInfo {
        let isCitySLPPack = false
        let accessCity = null
        let accessWorkout = null
        console.log("membership", JSON.stringify(membership))
        if (membership.attributes != null && membership.attributes.length > 1) {
            membership.attributes.map((benefit) => {
                if (benefit.attrKey == AttributeKeyType.ACCESS_CITY) {
                    accessCity = benefit.attrValue
                }
                if (benefit.attrKey == AttributeKeyType.ACCESS_SPORT_WORKOUT) {
                    accessWorkout = benefit.attrValue
                }
            })
            if (accessCity != null && accessWorkout != null)
                isCitySLPPack = true
        }
        return {
            isCitySLPPack,
            accessCity,
            accessWorkout
        }
    }

    public static isPlayLimitedSLPMembership(membership: Membership): PlayLimitedSessionMembershipInfo {
        let accessCenter = null
        let accessWorkout = null

        let isLimitedSessionPack = false
        let maxLimitedSession: number | null = null
        if (membership.attributes != null && membership.attributes.length > 1) {
            membership.attributes.map((benefit) => {
                if (benefit.attrKey == AttributeKeyType.ACCESS_CENTER) {
                    accessCenter = benefit.attrValue
                }
                if (benefit.attrKey == AttributeKeyType.ACCESS_SPORT_WORKOUT) {
                    accessWorkout = benefit.attrValue
                }
            })
        }

        if (membership.benefits != null && membership.benefits.length > 0) {
            membership.benefits.forEach((benefit) => {
                if (benefit.name === PlayUtil.LIMITED_PACK_BENEFIT_NAME) {
                    isLimitedSessionPack = true
                    maxLimitedSession = benefit.maxTickets
                }
            })
        }


        return {
            isPlayLimitedSLPMembership: isLimitedSessionPack,
            accessCenter,
            accessWorkout,
            maxLimitedSession
        }
    }

    public static getPrimaryColorForProgressBar(membershipState: string) {
        switch (membershipState) {
            case "ACTIVE":
                return "#0FE498"
            case "EXPIRING":
            case "PAUSED":
                return "#F7C744"
            default:
                return "transparent"
        }
    }

    public static getProgressBarDateForMembership(
        membershipState: string,
        userContext: UserContext,
        descriptionText: string,
        progress?: number,
        ): ProgressBar {
            const today = TimeUtil.todaysDateWithTimezone(userContext.userProfile.timezone)
            const progressBar: ProgressBar = {
                leftText: descriptionText,
                progressBarColor: this.getPrimaryColorForMembershipState(membershipState.toString()),
                progress: progress
            }
            return progressBar
    }

    public static getMembershipState(membership: Membership, userContext: UserContext): string {
        const endDate = new Date(membership.end)
        const startDate = new Date(membership.start)
        const today = new Date()
        const endDateString = TimeUtil.formatEpochInTimeZoneDateFns(userContext.userProfile.timezone, membership.end, "yyyy-MM-dd")
        const todayDateString = moment().format("yyyy-MM-dd")
        const numDaysToEndFromToday = TimeUtil.diffInDays(userContext.userProfile.timezone, todayDateString, endDateString)

        switch (membership.status) {
            case "PURCHASED": {
                if (startDate > today) {
                    return "UPCOMING"
                }
                if (endDate < today) {
                    return "EXPIRED"
                }
                if (numDaysToEndFromToday <= 15) {
                    return "EXPIRING"
                }
                return "ACTIVE"
            }
            case "SUSPENDED":
            case "CANCELLED": {
                return "CANCELLED"
            }
            case "PAUSED": {
                return "PAUSED"
            }
            default:
                return "CANCELLED"
        }
    }

    public static getMembershipAccessCredits(rollbarService: RollbarService, membership?: Membership): { remainingCredits: number, totalCredits: number, usedCredits: number } {
        if (_.isNil(membership)) return null
        const creditBenefit = membership && membership.benefits.find(benefit => benefit.name == "ACCESS_CREDITS")
        if (_.isNil(creditBenefit)) return null
        const membershipCredits: number = (creditBenefit.maxTickets ?? 0) - (creditBenefit.ticketsUsed ?? 0)
        if (membershipCredits < 0) {
            rollbarService.sendError(
                new Error("User Credits Left is in negative"),
                { extra: { membership: membership } },
            )
        }
        return { remainingCredits: membershipCredits ?? 0, totalCredits: creditBenefit.maxTickets ?? 0, usedCredits: creditBenefit.ticketsUsed ?? 0 }
    }
    public static getProductTypeFromMembership(membership: Membership): ProductType | null {
        if (membership.productId.startsWith("LUXPACK")) {
            return "LUX_FITNESS_PRODUCT"
        } else if (membership.productId.startsWith("GYMFIT")) {
            return "GYMFIT_FITNESS_PRODUCT"
        } else if (membership.productId.startsWith("CULTPACK")) {
            return "FITNESS"
        } else if (membership.productId.startsWith("GYMPT")) {
            return "GYM_PT_PRODUCT"
        } else if (membership.productId.startsWith("PLAYPACK")) {
            return "PLAY"
        } else if (membership.productId.startsWith("ONEPASS")) {
            return "ONEPASS_PRODUCT"
        } else if (membership.productId.startsWith("TRANSFORM")) {
            return "BUNDLE"
        } else if (membership.productId.startsWith("CFLIVE") || membership.productId.startsWith("CF_LIVE")) {
            return "CF_LIVE"
        }
        return null
    }

    public static isCurrentOrFutureActiveMembership(membership: Membership): boolean {
        const now = Date.now()
        return membership.end >= now && (membership.status === "PURCHASED" || membership.status === "PAUSED")
    }
}