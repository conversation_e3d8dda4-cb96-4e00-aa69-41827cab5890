import { UserContext } from "@curefit/userinfo-common"
import { Tenant } from "@curefit/user-common"
import { ELITE_MEMBERSHIP_PRIMARY_BENEFITS } from "./CultUtil"
import { GYM_MEMBERSHIP_PRIMARY_BENEFITS } from "./GymfitUtil"
import * as moment from "moment"
import * as _ from "lodash"
import { CenterResponse } from "@curefit/center-service-common"
import { PackStartDateAnalyticsEvent } from "../cfAnalytics/PackStartDateAnalyticsEvent"
import { AnalyticsEventName } from "../cfAnalytics/AnalyticsEventName"
import { IMembershipService } from "@curefit/membership-client"
import { ICenterService } from "@curefit/center-service-client"
import { ICFAnalytics } from "../cfAnalytics/CFAnalytics"
import { Logger } from "@curefit/base"
import { Action } from "@curefit/apps-common"
import { WidgetView } from "../common/views/WidgetView"
import { AugmentedOfflineFitnessPack, ExhaustivePackBenefit, OfflineFitnessPack } from "@curefit/pack-management-service-common"

export enum upgradeTypes {
    PRO_SELECT_TO_PRO = "pro_select_to_pro",
    PRO_SELECT_TO_ELITE = "pro_select_to_elite",
    ELITE_SELECT_TO_ELITE = "elite_select_to_elite",
    PRO_TO_ELITE = "pro_to_elite",
}
class FitnessUtil {

    public static async getEarliestStartDateForFitnessMembership(userContext: UserContext, durationInDays: number, centerServiceId: number | string, packName: string, membershipService: IMembershipService, centerService: ICenterService, logger?: Logger, cfAnalytics?: ICFAnalytics) {
        const earliestStartDateCult = await membershipService.getEarliestStartDate(userContext?.userProfile?.userId, Tenant.CUREFIT_APP, ELITE_MEMBERSHIP_PRIMARY_BENEFITS, durationInDays * 86400)
        const earliestStartDateGym = await membershipService.getEarliestStartDate(userContext?.userProfile?.userId, Tenant.CUREFIT_APP, GYM_MEMBERSHIP_PRIMARY_BENEFITS, durationInDays * 86400)
        const earliestStartDate = earliestStartDateGym.start > earliestStartDateCult.start ? earliestStartDateGym.start : moment(earliestStartDateCult.start).add(1, "days").startOf("day").valueOf()
        let earliestStartDateString = moment(earliestStartDate).tz(userContext.userProfile.timezone).format("YYYY-MM-DD")
        if (!_.isNil(centerServiceId)) {
            const centerServiceResponse: CenterResponse = await centerService.getCenterById(Number(centerServiceId))
            const centerLaunchDate = centerServiceResponse.launchDate
            if (!_.isNil(centerLaunchDate)) {
                const centerLaunchDateString = moment(centerLaunchDate).tz(userContext.userProfile.timezone).format("YYYY-MM-DD")
                earliestStartDateString = moment(centerLaunchDateString) > moment(earliestStartDateString) ? centerLaunchDateString : earliestStartDateString
            }
        }
        if (!_.isNil(logger)) {
            logger.info("startDateLog : CartController.getEarliestStartDateForFitnessMembership userId: " + userContext?.userProfile?.userId + ", cultBenefits: " + JSON.stringify(ELITE_MEMBERSHIP_PRIMARY_BENEFITS) + ", gymBenefits: "
                + JSON.stringify(GYM_MEMBERSHIP_PRIMARY_BENEFITS) + ", durationInSeconds: " + durationInDays * 86400 + ", earliestStartDateCult.start: " + earliestStartDateCult?.start + ", earliestStartDateGym.start: " + earliestStartDateGym?.start + ", earliestStartDate: " +
                earliestStartDate + ", earliestStartDateString: " + earliestStartDateString)
        }
        if (!_.isNil(cfAnalytics)) {
            cfAnalytics.sendEventFromUserContext(<PackStartDateAnalyticsEvent>{
                analyticsEventName: AnalyticsEventName.PACK_START_DATE,
                from: "CartController.getEarliestStartDateForFitnessMembership",
                packDuration: durationInDays * 86400,
                packName: packName,
                cultStartDate: moment(earliestStartDateCult?.start).tz(userContext.userProfile.timezone).format("YYYY-MM-DD"),
                gymfitStartDate: moment(earliestStartDateGym?.start).tz(userContext.userProfile.timezone).format("YYYY-MM-DD"),
                finalEarliestStartDate: moment(earliestStartDate).tz(userContext.userProfile.timezone).format("YYYY-MM-DD"),
                previousMembEndDate: moment(earliestStartDate).tz(userContext.userProfile.timezone).format("YYYY-MM-DD")
            }, userContext, false, true, true, false)
        }
        return earliestStartDateString
    }

    public static getUpgradeAction(upgradeType: upgradeTypes, membershipId: string, productId: string): Action {
        return {
            "actionType": "NAVIGATION",
            "url": `curefit://upgrade_membership?membershipServiceId=${membershipId}&upgradeType=${upgradeType}&productId=${productId}`,
            "iconUrl": "/image/icons/cult/upgrade.png",
            "title": "Upgrade to Cultpass " + upgradeType.toString().split("_to_")[1].toUpperCase(),
        }
    }

    public static getTransferMembershipPageAction(membershipServiceId: string): Action | any {
        return {
            iconUrl: "/image/icons/cult/transfer.png",
            title: "TRANSFER",
            actionType: "NAVIGATION",
            url: `curefit://get_transfer_details?membershipServiceId=${membershipServiceId}`
        }
    }

    public static getSkuPlusBenefitsList(pack: AugmentedOfflineFitnessPack): Promise<WidgetView> {
        const items: any = []
        if (pack) {
            pack?.augments?.exhaustiveBenefitList?.map((benefit: ExhaustivePackBenefit) => {
                const title: string = benefit?.displayTitle
                if (title) {
                    items.push({title})
                }
            })
        }
        if (items?.length === 0)  return null

        return items
    }
}

export default FitnessUtil