import "reflect-metadata"
import AppUtil, {
    CITY_SELECTION_ACTION_ANDROID,
    CITY_SELECTION_ACTION_IOS,
    PACK_CHANGE_START_DATE_ANDROID_APP_VERSION,
    PACK_CHANGE_START_DATE_IOS_APP_VERSION,
    PULSE_TRIAL_OPT_OUT_IOS,
    PULSE_TRIAL_OPT_OUT_ANDROID,
    PULSE_TRIAL_OPT_OUT_CP_ANDROID,
    PULSE_TRIAL_OPT_OUT_CP_IOS,
} from "../AppUtil"

const testSuiteId: string = "Test isPulseTrialOptOutSupported:"
const {
  isPulseTrialOptOutSupported,
} = AppUtil

describe(`${testSuiteId}`, () => {
  test(`returns true for v${PULSE_TRIAL_OPT_OUT_IOS + 1}- iOS`, () => {
    const appVersion = PULSE_TRIAL_OPT_OUT_IOS + 1
    const osName = "ios"
    expect(isPulseTrialOptOutSupported(osName, appVersion)).toBe(true)
  })
  test(`returns false for v${PULSE_TRIAL_OPT_OUT_IOS} - iOS`, () => {
    const appVersion = PULSE_TRIAL_OPT_OUT_IOS
    const osName = "ios"
    expect(isPulseTrialOptOutSupported(osName, appVersion)).toBe(false)
  })
  test(`returns true for v${PULSE_TRIAL_OPT_OUT_ANDROID + 1} - Android`, () => {
    const appVersion = PULSE_TRIAL_OPT_OUT_ANDROID + 1
    const osName = "android"
    expect(isPulseTrialOptOutSupported(osName, appVersion)).toBe(true)
  })
  test(`returns false for v${PULSE_TRIAL_OPT_OUT_ANDROID} - Android`, () => {
    const appVersion = PULSE_TRIAL_OPT_OUT_ANDROID
    const osName = "android"
    expect(isPulseTrialOptOutSupported(osName, appVersion)).toBe(false)
  })
  test(`returns true for v${PULSE_TRIAL_OPT_OUT_ANDROID}.${PULSE_TRIAL_OPT_OUT_CP_ANDROID} - Android`, () => {
    const appVersion = PULSE_TRIAL_OPT_OUT_ANDROID
    const osName = "android"
    const cpVersion = PULSE_TRIAL_OPT_OUT_CP_ANDROID
    expect(isPulseTrialOptOutSupported(osName, appVersion, cpVersion)).toBe(true)
  })
  test(`returns true for v${PULSE_TRIAL_OPT_OUT_IOS}.${PULSE_TRIAL_OPT_OUT_CP_IOS} - iOS`, () => {
    const appVersion = PULSE_TRIAL_OPT_OUT_IOS
    const osName = "ios"
    const cpVersion = PULSE_TRIAL_OPT_OUT_CP_IOS
    expect(isPulseTrialOptOutSupported(osName, appVersion, cpVersion)).toBe(true)
  })
  test(`returns false for v${PULSE_TRIAL_OPT_OUT_ANDROID}.${PULSE_TRIAL_OPT_OUT_CP_ANDROID - 1} - Android`, () => {
    const appVersion = PULSE_TRIAL_OPT_OUT_ANDROID
    const osName = "android"
    const cpVersion = PULSE_TRIAL_OPT_OUT_CP_ANDROID - 1
    expect(isPulseTrialOptOutSupported(osName, appVersion, cpVersion)).toBe(false)
  })
  test(`returns false for v${PULSE_TRIAL_OPT_OUT_IOS}.${PULSE_TRIAL_OPT_OUT_CP_IOS - 1} - iOS`, () => {
    const appVersion = PULSE_TRIAL_OPT_OUT_IOS
    const osName = "ios"
    const cpVersion = PULSE_TRIAL_OPT_OUT_CP_IOS - 1
    expect(isPulseTrialOptOutSupported(osName, appVersion, cpVersion)).toBe(false)
  })
  test(`returns true for v${PULSE_TRIAL_OPT_OUT_ANDROID}.${PULSE_TRIAL_OPT_OUT_CP_ANDROID + 1} - Android`, () => {
    const appVersion = PULSE_TRIAL_OPT_OUT_ANDROID
    const osName = "android"
    const cpVersion = PULSE_TRIAL_OPT_OUT_CP_ANDROID + 1
    expect(isPulseTrialOptOutSupported(osName, appVersion, cpVersion)).toBe(true)
  })
  test(`returns true for v${PULSE_TRIAL_OPT_OUT_IOS}.${PULSE_TRIAL_OPT_OUT_CP_IOS + 1} - iOS`, () => {
    const appVersion = PULSE_TRIAL_OPT_OUT_IOS
    const osName = "ios"
    const cpVersion = PULSE_TRIAL_OPT_OUT_CP_IOS + 1
    expect(isPulseTrialOptOutSupported(osName, appVersion, cpVersion)).toBe(true)
  })
})
