import "reflect-metadata"
import { upgradePackName } from "../CultUtil"
const testSuiteId: string = "Test Upgrade Pack Name Construction"

describe(`${testSuiteId} test`, () => {
  test("6 Month Select Pack => 6 Month Unlimited Cult Pack", () => {
    const selectPackName = "6 Month Select Pack"
    const targetPackName = "6 Month Unlimited Cult Pack"
    expect(upgradePackName(selectPackName)).toBe(targetPackName)
  })

  test("6 Month Select Pack - Launch Offer => 6 Month Unlimited Cult Pack", () => {
    const selectPackName = "6 Month Select Pack - Launch Offer"
    const targetPackName = "6 Month Unlimited Cult Pack"
    expect(upgradePackName(selectPackName)).toBe(targetPackName)
  })

  Array(24).fill(0).forEach((_, i) => {
    test(`${i + 1} Month Select Pack => ${i + 1} Month Unlimited Cult Pack`, () => {
      const selectPackName = `${i + 1} Month Select Pack - Launch Offer`
      const targetPackName = `${i + 1} Month Unlimited Cult Pack`
      expect(upgradePackName(selectPackName)).toBe(targetPackName)
    })
  })

  Array(24).fill(0).forEach((_, i) => {
    test(`${i + 1} Month Mind Pack => ${i + 1} Month Unlimited Cult Pack`, () => {
      const selectPackName = `${i + 1} Month Mind Pack - Launch Offer`
      const targetPackName = `${i + 1} Month Unlimited Cult Pack`
      expect(upgradePackName(selectPackName)).toBe(targetPackName)
    })
  })

  /**
   * Edge case: unknown pattern
   */
  test("Infosys Employee Pack - Launch Offer => 6 Month Unlimited Cult Pack", () => {
    const selectPackName = "Infosys Employee Pack - Launch Offer"
    const targetPackName = "Cult Upgraded Pack"
    expect(upgradePackName(selectPackName)).toBe(targetPackName)
  })
})