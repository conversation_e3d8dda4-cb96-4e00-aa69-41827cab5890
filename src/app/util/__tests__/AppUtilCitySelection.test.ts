import "reflect-metadata"
import AppUtil, {
    CITY_SELECTION_ACTION_ANDROID,
    CITY_SELECTION_ACTION_IOS,
    PACK_CHANGE_START_DATE_ANDROID_APP_VERSION,
    PACK_CHANGE_START_DATE_IOS_APP_VERSION,
    PULSE_TRIAL_OPT_OUT_IOS,
    PULSE_TRIAL_OPT_OUT_ANDROID,
    PULSE_TRIAL_OPT_OUT_CP_ANDROID,
    PULSE_TRIAL_OPT_OUT_CP_IOS,
} from "../AppUtil"

const {
  _isCitySelectionActionSupported,
} = AppUtil

describe("App Supported Tests", () => {
  test("return true when change city supported", () => {
    expect(_isCitySelectionActionSupported("android", CITY_SELECTION_ACTION_ANDROID + 0.01)).toBe(true)
  })
  test("return true when change city supported", () => {
    expect(_isCitySelectionActionSupported("ios", CITY_SELECTION_ACTION_IOS + 0.01)).toBe(true)
  })
})
