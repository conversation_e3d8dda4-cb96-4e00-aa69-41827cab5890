import "reflect-metadata"
import AppUtil, {
    PACK_CHANGE_START_DATE_ANDROID_APP_VERSION,
    PACK_CHANGE_START_DATE_IOS_APP_VERSION,
} from "../AppUtil"

const testSuiteId: string = "Test _isPackStartDateChangeSupported:"
const {
  _isPackStartDateChangeSuppported,
} = AppUtil

describe(`${testSuiteId} osName test`, () => {
  test("returns false for browser", () => {
    expect(
      _isPackStartDateChangeSuppported("browser", PACK_CHANGE_START_DATE_ANDROID_APP_VERSION)
    ).toBe(false)
  })
  test("returns false for any random string", () => {
    expect(
      _isPackStartDateChangeSuppported("random string", PACK_CHANGE_START_DATE_ANDROID_APP_VERSION)
    ).toBe(false)
  })
  test("returns false for non-android non-ios external user", () => {
    expect(
      _isPackStartDateChangeSuppported("blackberry", PACK_CHANGE_START_DATE_ANDROID_APP_VERSION + 0.01, undefined, false)
    ).toBe(false)
  })
  test("returns true for Android external user", () => {
    expect(
      _isPackStartDateChangeSuppported("android", PACK_CHANGE_START_DATE_ANDROID_APP_VERSION + 0.01, undefined, false)).toBe(true)
  })
  test("returns true for iOS external user", () => {
    expect(
      _isPackStartDateChangeSuppported("ios", PACK_CHANGE_START_DATE_IOS_APP_VERSION + 0.01, undefined, false)
    ).toBe(true)
  })
})

describe(`${testSuiteId} External User test`, () => {
  test(`Android external user on version ${PACK_CHANGE_START_DATE_ANDROID_APP_VERSION} returns false`, () => {
    expect(
      _isPackStartDateChangeSuppported("android",
        PACK_CHANGE_START_DATE_ANDROID_APP_VERSION,
        undefined,
        false
      )
    ).toBe(false)
  })

  test(`iOS external user on version ${PACK_CHANGE_START_DATE_IOS_APP_VERSION} returns false`, () => {
    expect(
      _isPackStartDateChangeSuppported("ios",
        PACK_CHANGE_START_DATE_IOS_APP_VERSION,
        undefined,
        false
      )
    ).toBe(false)
  })

  test(`iOS external user on version ${Number(PACK_CHANGE_START_DATE_IOS_APP_VERSION + 1.0 + Math.random()).toFixed(2)} returns true`, () => {
    expect(
      _isPackStartDateChangeSuppported("ios",
        PACK_CHANGE_START_DATE_IOS_APP_VERSION + 1.0 + Math.random(),
        undefined,
        false
      )
    ).toBe(true)
  })

  test(`Android external user on version ${Number(PACK_CHANGE_START_DATE_ANDROID_APP_VERSION + 1 + Math.random()).toFixed(2)} returns true`, () => {
    expect(
      _isPackStartDateChangeSuppported("android",
        PACK_CHANGE_START_DATE_ANDROID_APP_VERSION + 1 + Math.random(),
        undefined,
        false
      )
    ).toBe(true)
  })

  test(`Android external user on version ${Number(PACK_CHANGE_START_DATE_ANDROID_APP_VERSION - Math.random()).toFixed(2)} returns false`, () => {
    expect(
      _isPackStartDateChangeSuppported("android",
        PACK_CHANGE_START_DATE_ANDROID_APP_VERSION - Math.random(),
        undefined,
        false
      )
    ).toBe(false)
  })

  test(`iOS external user on version ${Number(PACK_CHANGE_START_DATE_ANDROID_APP_VERSION - Math.random()).toFixed(2)} returns false`, () => {
    expect(
      _isPackStartDateChangeSuppported("ios",
        PACK_CHANGE_START_DATE_ANDROID_APP_VERSION - Math.random(),
        undefined,
        false
      )
    ).toBe(false)
  })
})
