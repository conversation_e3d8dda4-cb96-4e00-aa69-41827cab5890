import { BadgeType, Vertical } from "@curefit/quest-common"

export const QUEST_VERSION_ANDROID = 6.2
export const QUEST_VERSION_IOS = 6.1

export const QUEST_VERSION_ANDROID_CP = 58
export const QUEST_VERSION_IOS_CP = 43

export const SHARE_TEXT = {
  GLOBAL: "Feeling fitter by the day! And a @becurefit Badge to remind me of my hard work. #BeBetterEveryday",
  CULT: "Mixing it up with <PERSON><PERSON> and making my workouts fun with @becurefit! #BeBetterEveryday",
  STEPS: "Here's to the best medicine! Walking my way to fitness. #BeBetterEveryday @becurefit",
  MIND: "Falling in love with taking care of my mind, body & soul. #BeBetterEveryday @becurefit",
  SLEEP: "Sleeping for a better today, tomorrow! #BeBetterEveryday @becurefit",
  EAT: "I don’t diet & exercise. I eat.fit & train. #BeBetterEveryday @becurefit",
  LEVELUP: "Guess who just levelled up? Me! Hard work and persistence always pays off. Here’s to good health and a fitter self. #BeBetterEveryday @becurefit",
  CHALLENGE: "Feeling fitter by the day! And a @becurefit Badge to remind me of my hard work. #BeBetterEveryday"
}

export const getShareText = (vertical: Vertical, type?: BadgeType): string => {
  switch (vertical) {
    case "GLOBAL":
      if (type === "CHALLENGE") {
        return SHARE_TEXT.CHALLENGE
      }
      return SHARE_TEXT.GLOBAL
    case "CULT":
      return SHARE_TEXT.CULT
    case "STEPS":
      return SHARE_TEXT.STEPS
    case "MIND":
      return SHARE_TEXT.MIND
    case "SLEEP":
      return SHARE_TEXT.SLEEP
    case "EAT":
      return SHARE_TEXT.EAT
    default:
      return ""
  }
}

export const CULT_LAUNCH_TERMS = "The offer cannot be clubbed with Cult Launch Offers. Conditions apply."
