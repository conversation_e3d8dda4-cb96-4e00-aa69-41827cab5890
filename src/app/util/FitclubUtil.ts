import { RewardItem } from "../common/views/RewardListingSummaryWidget"
import * as momentTz from "moment-timezone"
import * as _ from "lodash"
import { UserReward } from "@curefit/quest-common"
import { SavingsSource } from "@curefit/fitclub-models"
import { TimeUtil } from "@curefit/util-common"
import { FitcashReward, LedgerEntry, Response } from "@curefit/reward-common"
const REWARD_TITLES = {
    CULT_CLASS: "cult.fit class",
    MIND_CLASS: "mind.fit class",
    EAT_FIT_MEAL: "eat.fit meal",
    PT_CONSULTATION: "PT Consultation"
}

export class FitclubUtil {
    public static getClaimedRewardItem(reward: LedgerEntry<Response>, timezone: string, reportIssues: any): RewardItem {
        const subTitle = momentTz.tz(reward.date, timezone).format("Do MMM")
        const leftInfo: any = {
            type: "DESCRIPTION",
            title: 0
        }
       // _.forEach(reward.savings, (saving) => {
            switch (reward.rewardType) {
                case "FITCASH" :
                    const fitcashReward = <FitcashReward>reward.reward
                    leftInfo.icon = "GOLD"
                    leftInfo.title = fitcashReward.amount ? (fitcashReward.amount / 100) : 0
                    if (reward.context && reward.context.isFreeDelivery) {
                        leftInfo.subTitle = "+ Free Delivery"
                    }
                    break
                case "FREE_DELIVERY":
                    leftInfo.subTitle = "Free Delivery"
                    leftInfo.title = leftInfo.title > 0 ? leftInfo.title : 0
                    break
            /*  case "CULT_DAYS":
                    leftInfo.title = saving.value
                    leftInfo.subTitle = "Extra cult days"
                    break
                    */
            }
       // })
        return {
            title: reward.title,
            description: reward.subtitle,
            showDivider: true,
            subTitle: subTitle,
            // "subTitle": "08:00 PM, Cult Indiranagar, 80 feet road",
            // action: {
            //     actionType: "NAVIGATION",
            //     url: "curefit://cultclass?bookingNumber=IND80ZHJA7O90N"
            // },
            leftInfo: leftInfo,
            rightInfo: {
                moreAction: {
                    icon: "MANAGE",
                    actionType: "ACTION_LIST",
                    actions: [
                        {
                            isEnabled: true,
                            title: "Need Help",
                            actionType: "REPORT_ISSUE",
                            meta: {
                                title: "Help",
                                issues: reportIssues
                            }
                        }
                    ]
                }
            }
        }
    }
    public static getExpiredRewardItem(reward: UserReward): RewardItem {
        const subTitle = momentTz.tz(reward.date.date, reward.date.timezone).format("Do MMM")
        const leftInfo: any = {
            images: ["/image/icons/fitclub/expired_gift_icon.png"]
        }
        return {
            title: this.getUnclaimedRewardTitle(reward.fulfilmentId), //
            description: subTitle,
            showDivider: true,
            // "subTitle": "08:00 PM, Cult Indiranagar, 80 feet road",
            // action: {
            //     actionType: "NAVIGATION",
            //     url: "curefit://cultclass?bookingNumber=IND80ZHJA7O90N"
            // },
            leftInfo: leftInfo,
            rightInfo: {
                moreAction: {
                    icon: "MANAGE",
                    actionType: "ACTION_LIST",
                    actions: [
                        {
                            isEnabled: true,
                            // displayText: "Report Issue",
                            actionType: "REPORT_ISSUE"
                        }
                    ]
                }
            }
        }
    }
    public static getUnclaimedRewardTitle(fulfilmentId: string): string {
        if (!fulfilmentId)
            return "Activity"
        if (fulfilmentId.indexOf("EAT") !== -1) {
            return REWARD_TITLES.EAT_FIT_MEAL
        } else if (fulfilmentId.indexOf("CULT") !== -1) {
            return REWARD_TITLES.CULT_CLASS
        } else if (fulfilmentId.indexOf("MIND") !== -1) {
            return REWARD_TITLES.MIND_CLASS
        } else {
            return "Activity"
        }
    }
}
