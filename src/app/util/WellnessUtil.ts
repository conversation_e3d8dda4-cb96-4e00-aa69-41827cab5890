import * as _ from "lodash"
import { DeliverySubArea } from "@curefit/eat-common"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { City } from "@curefit/location-common"
import { GenericError } from "@curefit/error-client"
import { IDeliveryAreaService } from "@curefit/delivery-client"
import { RollbarService } from "@curefit/error-common"

export default class WellnessUtil {
    public static async getWellnessDefaultSubAreaForCity(deliveryAreaService: IDeliveryAreaService, city: City, rollbarService: RollbarService): Promise<DeliverySubArea> {

        let subArea
        const { defaultWellnessAreaId } = city

        if (defaultWellnessAreaId === "36") {
            return {
                areaId: "36",
                subArea: "Powai",
                representativeLatLong: {
                    lat : 19.1131127783294,
                    long : 72.9026568358936
                }
            }
        }
        if (defaultWellnessAreaId === "82") {
            return {
                areaId: "82",
                subArea: "T Nagar",
                representativeLatLong: {
                    lat : 13.0432165122147,
                    long : 80.2376749360639
                }
            }
        }

        try {
            subArea = _.first(await deliveryAreaService.getSubAreasForArea(defaultWellnessAreaId))
            if (!subArea) {
                const err = new GenericError({
                    message: `no subareas for areaId`,
                    context: {areaId: defaultWellnessAreaId}
                })
                rollbarService.sendError(err)
            }
        } catch (e) {
            // defaulting to eat subarea with city default latlong
            subArea = (await deliveryAreaService.findSubArea(city.representativeLatLong, "FOOD")).subArea
        }
        return { areaId: defaultWellnessAreaId, subArea: subArea?.name, representativeLatLong: subArea?.representativeLatLong }
    }
}
