import { EmiInterest, PaymentChannel, RazorpayEmiDetails } from "@curefit/payment-common"
import { BaseOrder, Order, OrderSource } from "@curefit/order-common"
import { PriceComponent } from "../order/OrderViewBuilder"
import * as _ from "lodash"
import { BillingInfo } from "@curefit/finance-common"
import { ProductSubType } from "@curefit/pack-management-service-common"

export const NEW_ORDER_CONFIRMATION_ANDROID_VERSION = 6.5
export const NEW_ORDER_CONFIRMATION_ANDROID_CP_VERSION = 56
export const NEW_ORDER_CONFIRMATION_IOS_VERSION = 6.4
export const NEW_ORDER_CONFIRMATION_IOS_CP_VERSION = 47

export const ORDER_CONFIRMATION_ACTION = "curefit://orderconfirmation"
export const CARE_ORDER_CONFIRMATION_ACTION = "curefit://careorderconfirmation"
export const ORDER_CONFIRMATION_V1_ACTION = "curefit://orderconfirmationv1"
export const FLUTTER_ORDER_CONFIRMATION_ACTION = "curefit://fl_orderconfirmation"
export const TRANSFORM_ORDER_CONFIRMATION_V1_ACTION = "curefit://tf_orderconfirmation"
export const CARE_AURORA_ORDER_CONFIRMATION_V1_ACTION = "curefit://fl_orderconfirmation"
export const CLASS_CONFIRMED_USER_JOURNEY_ACTION = "curefit://user_journey?showConfirmationLottie=true&&confirmationText=Class%20confirmed"
export const CLASS_WAITLIST_CONFIRMED_USER_JOURNEY_ACTION = "curefit://user_journey?showConfirmationLottie=true&&confirmationText=Added%20to%20Waitlist"
export const ORDER_CONFIRMATION_EAT_MARKETPLACE_ACTION = "curefit://eatorderstatuspage"

export const GEAR_ORDER_CONFIRMATION_ACTION = "curefit://gearorderconfirmation"

export const PACKAGING_CHARGE = 15
export const DEFAULT_DELIVERY_CHARGE = 25
export const DEFAULT_CUTLERY_CHARGE = 2

export const GST_RATE_INSTANT_DISCOUNT = 0.18

export const CHECKOUT_TIMER_STAGE_WIDGET_ID = "fe9ad20c-8ca9-46be-9612-1d94c9578bd7"
export const CHECKOUT_TIMER_PROD_WIDGET_ID = "e8937769-a26b-443b-8ecd-1dcfb4101329"

export const SUPPORTED_MICROAPPS_ORDER_SOURCE: Set<OrderSource> = new Set<OrderSource>(["PHONEPE_APP", "EAT_GOOGLE_PAY_APP", "PHONEPE_CUREFIT_CARE", "PHONEPE_CUREFIT_CULT", "PHONEPE_CUREFIT_MIND", "PHONEPE_CUREFIT_WHOLE", "PAYTM_PWA"])

class OrderUtil {

    public static isNewOrderConfirmationScreenSupported(osName: string, appVersion: number, codepushversion: number, orderSource?: OrderSource ): boolean {
        if ( orderSource && (orderSource === "SUGARFIT_APP" || orderSource === "ULTRAFIT_APP" || orderSource === "SUGARFIT_WEBSITE") ) {
            return true
        }
        if (osName === "android" && (appVersion > NEW_ORDER_CONFIRMATION_ANDROID_VERSION || (appVersion === NEW_ORDER_CONFIRMATION_ANDROID_VERSION && codepushversion >= NEW_ORDER_CONFIRMATION_ANDROID_CP_VERSION))) {
            return true
        }
        if (osName === "ios" && (appVersion > NEW_ORDER_CONFIRMATION_IOS_VERSION || (appVersion === NEW_ORDER_CONFIRMATION_IOS_VERSION && codepushversion >= NEW_ORDER_CONFIRMATION_IOS_CP_VERSION))) {
            return true
        }
        return false
    }

    public static getFormattedPaymentChannel(paymentChannel: PaymentChannel): PaymentChannel {
        if (paymentChannel && paymentChannel === "RAZORPAY_EAT") {
            return "RAZORPAY"
        }
        return paymentChannel
    }

    public static getNoCostEmiKnowMoreUrl(order: Order) {
        const payment = order.payments.find((payment) => payment.status === "paid")
        if (!payment.instantDiscount || !payment.instantDiscount.emiMeta) return ""
        const amountPayable = +(payment.amount / 100).toFixed(2)
        const orderValue = +(amountPayable + (payment.instantDiscount.amount / 100)).toFixed(2)
        const period = payment.instantDiscount.emiMeta.duration
        const gst = (orderValue - amountPayable) * GST_RATE_INSTANT_DISCOUNT
        const uri = encodeURIComponent(`https://vm-html-pages.s3.ap-south-1.amazonaws.com/nocostemiv2.html?orderValue=${orderValue}&amountPayable=${amountPayable}&period=${period}&gst=${gst}`)
        return `curefit://webview?uri=${uri}`
    }

    public static isFitcashApplied(order: BaseOrder): boolean {
        return order.totalFitCashPayable > 0 ? true : false
    }

    public static getSfProductPriceDiscount(billingInfo: BillingInfo): number {
        let cartDiscount = 0
        if (billingInfo?.orderDiscountsByOffer?.length && billingInfo.orderDiscountsWithTitle) {
            const filterOrderDiscount = billingInfo.orderDiscountsByOffer.filter(offer => offer.discount > 0)
            if (!_.isEmpty(filterOrderDiscount)) {
                filterOrderDiscount.forEach((discount, index) => {
                    cartDiscount += discount.discount
                })
            }
        }
        const productDiscount = billingInfo.discount - cartDiscount
        return productDiscount || 0
    }

    public static getDiscountPriceDetails (billingInfo: BillingInfo): PriceComponent[] {
        const priceDetails: PriceComponent[] = []
        const cartDiscountDetails: PriceComponent[] = []
        let cartDiscount = 0
        if (billingInfo?.orderDiscountsByOffer?.length && billingInfo.orderDiscountsWithTitle) {
            const filterOrderDiscount = billingInfo.orderDiscountsByOffer.filter(offer => offer.discount > 0)
            if (!_.isEmpty(filterOrderDiscount)) {
                let discountWithoutLabel = 0
                filterOrderDiscount.forEach((discount, index) => {
                    cartDiscount += discount.discount
                    const orderSummaryLabel = billingInfo.orderDiscountsWithTitle?.data && billingInfo.orderDiscountsWithTitle?.data[discount.offerId] ? billingInfo.orderDiscountsWithTitle?.data[discount.offerId]?.uiLabels?.orderSummaryLabel : billingInfo.orderDiscountsWithTitle[discount.offerId]?.uiLabels?.orderSummaryLabel
                    if (orderSummaryLabel) {
                        cartDiscountDetails.push({
                            title: orderSummaryLabel,
                            value: `${discount.discount.toFixed(2)}`,
                            isDiscount: true,
                        })
                    } else {
                        discountWithoutLabel += discount.discount
                    }
                })
                if (discountWithoutLabel > 0) {
                    cartDiscountDetails.push({
                        title: `Cart Discount`,
                        value: discountWithoutLabel.toFixed(2),
                        isDiscount: true,
                    })
                }
            }
        }
        const productDiscount = billingInfo.discount - cartDiscount
        if (productDiscount > 0) {
            priceDetails.push({
                title: "Price Discount",
                value: productDiscount.toFixed(2),
                isDiscount: true
            })
        }
        if (!_.isEmpty(cartDiscountDetails)) {
            priceDetails.push(...cartDiscountDetails)
        }
        return priceDetails
    }

    public static isPlusMembershipOrder(order?: Order): boolean {
        return (order?.productSnapshots?.[0]?.addOnSnapshots?.[0]?.addonType === ProductSubType.BASE_ADDON.toString())
            || (order?.productSnapshots?.[0]?.productSubType === ProductSubType.PLUS.toString())
    }
}
export default OrderUtil
