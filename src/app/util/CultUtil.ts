import {
    BookingTypes,
    ClassAttendingUser,
    ComplimentaryAccessList,
    CultBooking,
    CultCenter,
    CultClass,
    CultClassDetails,
    CultLocality,
    CultMembership,
    CultMoment,
    CultMomentFileType,
    CultMomentsForClass,
    CultMomentsResponse,
    CultMomentsResponseV2,
    CultMomentV2,
    CultPack,
    CultSummary,
    CultWorkout,
    FitnessSubscriptionPackDetails,
    GraphEntry,
    ImageFilter,
    MembershipDetails,
    NearByCenter,
    PulseProduct,
    RenewalCycleUnit,
    UserTrialEligibility,
    WaitlistProbability,
    WodDetailsResponse
} from "@curefit/cult-common"
import { CultPackProgress, CultService, ICultService, ICultServiceOld, IS_NEW_CULT_OFFER_VERSION } from "@curefit/cult-client"
import { AppTenant, SubscriptionType, UserAgentType as UserAgent, Vertical, } from "@curefit/base-common"
import { Product, ProductPrice, ProductType, UrlPathBuilder } from "@curefit/product-common"
import { User, } from "@curefit/user-common"
import * as momentTz from "moment-timezone"
import { CdnUtil, eternalPromise, pluralizeStringIfRequired, TimeUtil, Timezone } from "@curefit/util-common"
import * as _ from "lodash"
import { CultPackTransferMetadata, FoodFulfilment, isMembershipUpgradeClientMetadata, Order, OrderProduct } from "@curefit/order-common"
import { ActionUtil as BaseActionUtil, Constants, OfferUtil, SeoUrlParams } from "@curefit/base-utils"
import { CultProductPricesResponse, OfferAddonType, OfferMini, OfferV2, PackOfferItem, PackOffersResponse } from "@curefit/offer-common"
import { UserContext } from "@curefit/userinfo-common"
import CultPackPageConfig from "../pack/CultPackPageConfig"
import {
    ActionCardWidget,
    CallReminderSlotTime,
    CultBuddiesJoiningListLargeView,
    CultBuddiesJoiningListSmallView,
    CultCafeWidget,
    DescriptionWidget,
    Header,
    InfoCard,
    InstructionsWithMedia,
    ManageOptions,
    PauseInfo,
    ProductGridWidget,
    ProductListWidget,
    SlotSelectedAction
} from "../common/views/WidgetView"
import {
    CULT_SUBSCRIPTION_VERSION_ANDROID,
    CULT_SUBSCRIPTION_VERSION_IOS, IServiceInterfaces,
    MembershipProductType,
    Segment
} from "@curefit/vm-models"
import { CultUserType } from "@curefit/vm-common"
import { KiosksDemandService } from "@curefit/masterchef-client"
import AppUtil, { CUREFIT_LOGO } from "./AppUtil"
import ActionUtil from "./ActionUtil"
import { HamletBusiness } from "@curefit/hamlet-node-sdk"
import { AnnouncementBusiness } from "../announcement/AnnouncementBusiness"
import { CacheHelper } from "./CacheHelper"
import { AnnouncementDetails } from "../announcement/AnnouncementViewBuilder"
import {
    Action,
    ClassState,
    CultCenterCloseInfo,
    CultMemoriesProfileInfo,
    ManageOption,
    Moments,
    MovementsInfo,
    PageTypes,
    Streak,
    ToggleBarWidget,
    WidgetView,
    WODInfoWidget,
    WorkoutInstruction,
    WorkoutMember,
    WorkoutMovement
} from "@curefit/apps-common"
import { CultTrialUserType } from "@curefit/segment-common"
import { ClassInviteLinkCreator } from "../cult/invitebuddy/ClassInviteLinkCreator"
import { CenterLocation, ICultBusiness, Preference } from "../cult/CultBusiness"
import { ProfileAttributeEntryCode, StreakResponse, TagEntry, UserAttributeMappingEntry } from "@curefit/social-common"
import { ActiveBundleOrderDetail, BundleOrderStatus } from "@curefit/albus-client"
import { Banner, BannerCarouselWidget, CFBottomSheetActionWidget, CFMediaDataWidget, CFMediaWidget, DescriptionWidgetV2, Facility, WorkoutDetailWidget } from "../page/PageWidgets"
import { PreWorkoutGear } from "@curefit/cult-common/dist/src/CultCenter"
import { CafeCatalogType } from "@curefit/eat-common"
import { DisplayMovement, SimpleWod } from "@curefit/fitness-common"
import {
    convertMapStyleToStatic,
    CUSTOM_MAP_CULT_FIND_CENTER_STYLE_COLORED
} from "../page/vm/widgets/CultFindCenterWidgetView"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { ISegmentService } from "@curefit/vm-models/dist/src/services/ISegmentService"
import { CenterResponse, CenterVertical, SkuName } from "@curefit/center-service-common"
import { AttributeKeyType, Benefit, BenefitType, Membership } from "@curefit/membership-commons"
import { IMembershipService } from "@curefit/membership-client"
import { ICenterService } from "@curefit/center-service-client"
import GymfitUtil from "./GymfitUtil"
import { LocationDataKey, LocationPreferenceRequestEntity } from "./UserUtil"
import { PromiseCache } from "./VMUtil"
import { ICrudKeyValue } from "@curefit/redis-utils"
import { ONBOARDING_RECOMMENDED_LEVEL_ATTRIBUTE } from "../cult/ClassListViewBuilderV2"
import { IUserAttributeCacheClient, IUserAttributeClient } from "@curefit/rashi-client"
import { PageWidget } from "../page/Page"
import { MembershipItemUtil } from "./MembershipItemUtil"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"
import moment = require("moment")
import { ISegmentationCacheClient } from "@curefit/segmentation-service-client"
import CatalogueServiceUtilities from "./CatalogueServiceUtilities"

const clone = require("clone")

const numberToWords = require("number-to-words")

const CULT_PAUSE_PACK_VERSION_ANDROID = 5.3
const CULT_PAUSE_PACK_VERSION_IOS = 5.2

const CULT_CLP_PAUSE_PACK_VERSION_ANDROID = 5.9
const CULT_CLP_PAUSE_PACK_VERSION_IOS = 5.8

const CULT_PAUSE_CP_VERSION_ANDROID = 30
const CULT_PAUSE_CP_VERSION_IOS = 189

const PROGRAM_APP_VERSION_ANDROID = 6.34
const PROGRAM_APP_VERSION_IOS = 6.2
const PROGRAM_CP_VERSION_ANDROID = 48
const PROGRAM_CP_VERSION_IOS = 39


const NEW_PAUSE_PACK_VERSION_ANDROID = 6.97
const NEW_PAUSE_PACK_VERSION_IOS = 6.97
const NEW_PAUSE_PACK_CP_VERSION_ANDROID = 99
const NEW_PAUSE_PACK_CP_VERSION_IOS = 90

const MESSAGE_INTERVENTION_VERSION_ANDROID = 7.18
const MESSAGE_INTERVENTION_VERSION_IOS = 7.19
const MESSAGE_INTERVENTION_CP_VERSION_ANDROID = 122
const MESSAGE_INTERVENTION_CP_VERSION_IOS = 111

const BOOKING_PREFERENCE_VERSION_ANDROID = 7.57
const BOOKING_PREFERENCE_VERSION_IOS = 7.57

const PULSE_CLASS_REACH_EARLY_CUT_OFF_IN_MINUTES = 10

export const RUNNING_EVENT_WORKOUT_ID = 60
export const KICKSTART_WORKOUT_ID = 45

export const YOGA_WORKOUT_ID = 1
export const CULTSCORE_WORKOUT_CATEGORY_ID = 47
export const CULTSCORE_WORKOUT_IDS = [97, 107]
export const SWIMMING_WORKOUT_IDS = [153, 154]
export const UNLIMITED_FREE_AWAY_CLASSES_THRESHOLD: number = 200

export const CULT_DELHI_NON_COMPLIANT_CENTER_IDS = [91, 136, 188, 135, 101, 117, 244, 74, 92, 107, 36, 52, 71, 85, 121, 187, 109, 104, 108, 217, 203, 110]

export const CENTER_MAINTENANCE_WARNING = "/image/icons/warning.png"
export const CENTER_SHUTDOWN = "https://cdn-media.cure.fit/image/icons/announcement/shutdown.png"
export const CENTER_SHUTDOWN_ICON = "/image/icons/announcement/shutdown.png"
export const FITCLUB_ICON = "/image/icons/announcement/fitclub.png"
export const WORKOUT_PRO_TIP_ICON = "/image/icons/cult/workout-pro-tip-1.png"
export const DIET_PRO_TIP_ICON = "/image/icons/cult/diet-pro-tip-1.png"
export const ACHIEVEMENT_ICON = "https://cdn-media.cure.fit/image/icons/cult/achievement.png"
export const BLUR_PROFILE_IMAGES = [CdnUtil.getCdnUrl("curefit-content/image/icons/cult/blur-profile-0.jpg"), CdnUtil.getCdnUrl("curefit-content/image/icons/cult/blur-profile-1.jpg"), CdnUtil.getCdnUrl("curefit-content/image/icons/cult/blur-profile-2.jpg")]
export const USER_PROFILE_BACKGROUND_COLORS: string[] = ["#5d4037", "#0052cc", "#5244ab", "#de350c", "#172b4d", "#fa9010", "#4ab74a", "#ff3278", "#b00020"]
export const SHOW_INITIAL_IN_CULT_MEMORIES_VERSION = 8.88
export const UNPAUSE_AND_BOOK_CTA_VERSION = 8.91
export const WL_UNPAUSE_AND_BOOK_CTA_VERSION = 9.10

export const CULT_SPORT_CATEGORY_VERSION = 8.94
export const CULT_NEW_SCHEDULE_FLOW_VERSION = 9.45 // TODO - update this
export const CULT_SPORT_CATEGORY_PROD_CENTER_IDS = [306, 307, 308, 309, 310, 311]
export const CULT_SPORT_CATEGORY_PROD_WORKOUT_IDS = [317, 43]
export const CULT_SPORT_CATEGORY_PROD_WORKOUT_CATEGORY = [65]

export const CULT_SPORT_CATEGORY_STAGE_CENTER_IDS = [371, 372, 373, 374, 375, 376, 377, 378, 379]
export const CULT_SPORT_CATEGORY_STAGE_WORKOUT_IDS = [317, 318, 320, 321, 322, 43]
export const CULT_SPORT_CATEGORY_STAGE_WORKOUT_CATEGORY = [69]

export const PILATES_WORKOUT_IDS = [483, 484, 485, 489, 490, 494, 495]
export const WEEKEND_AT_CULT = [183, 364, 368, 435]
export const PILATES_CENTER_IDS = [3252, 3254]

export const BOOTCAMP_CATEGORY_STAGE_WORKOUT_CATEGORY = [77]
export const BOOTCAMP_CATEGORY_PROD_WORKOUT_CATEGORY = [77]

export const CULT_BADDYFIT_PROD_WORKOUT_IDS = [333]

export const CULT_BADDYFIT_STAGE_WORKOUT_IDS = [328]

export const CULT_BADDYFIT_CATEGORY_STAGE_EXPERIMENT_ID = 487

export const CULT_BADDYFIT_CATEGORY_PROD_EXPERIMENT_ID = 888

export const CULT_TRANSFORM_ADD_ON_STAGE_EXPERIMENT_ID = 574

export const CULT_TRANSFORM_ADD_ON_PROD_EXPERIMENT_ID = 1232

export const GOAL_SETTING_MIN_APP_VERSION = 9.91
export const HABIT_GAME_WIDGET_ENABLE = 10.97
export const GOAL_SETTING_PROD_BUCKET_ID = "2"
export const GOAL_SETTING_PROD_EXPERIMENT_ID = 1426
export const ACCOUNTABILITY_PARTNER_MIN_APP_VERSION = 9.91
export const ACCOUNTABILITY_PARTNER_PROD_BUCKET_ID = "2"
export const ACCOUNTABILITY_PARTNER_PROD_EXPERIMENT_ID = 1610
export const GOAL_SETTING_PROD_SEGMENT_ID = "43de7052-a521-4860-b354-d247e828653e"
export const ACCOUNTABILITY_PARTNER_PROD_SEGMENT_ID = "********-30b4-4bb3-a712-fece33510f23"
export const NEW_USER_ONBOARDING_FORM_SEGMENT_ID = "7e9ac507-9d53-4ea8-93cd-65d3292999a7"

export const MULTIPLAYER_HABIT_GAME_SEGMENT = "15e4dab9-ea0d-4514-81ed-4bf7f33b55c1"
export const HABIT_GAME_SEGMENT = "9aa263c6-616a-4087-b813-bfdb8e482894"


export const CULT_TRANSFORM_ADD_ON_VERSION = 9.67

export const CULT_RUN_STAGE_WORKOUT_ID = 43
export const CULT_RUN_PROD_WORKOUT_ID = 43
export const CULT_RUN_APP_VERSION = 9.28

export const BOOTCAMP_STAGE_WORKOUT_ID = 376
export const BOOTCAMP_PROD_WORKOUT_ID = 371
export const BOOTCAMP_APP_VERSION = 10.04

const INSTRUCTION_MODAL_SUPPORTED_ANDROID = 7.58
const INSTRUCTION_MODAL_SUPPORTED_IOS = 7.58

export const DUBAI_CULT_CITY_ID = 8
export const DUBAI_ACTUAL_CITY_ID = "Dubai"

export const BOOK_FREE_CLASS_TEXT_UPDATE_HAMLET_PRODUCTION_ID = "68"
export const BOOK_FREE_CLASS_TEXT_UPDATE_HAMLET_STAGE_ID = "110"
export const getBookFreeClassTextUpdateHamletID = (): string => {
    if (process.env.ENVIRONMENT === "PRODUCTION") {
        return BOOK_FREE_CLASS_TEXT_UPDATE_HAMLET_PRODUCTION_ID
    }
    return BOOK_FREE_CLASS_TEXT_UPDATE_HAMLET_STAGE_ID
}
export const BOOK_FREE_CLASS_TEXT_UPDATE_HAMLET_ID = getBookFreeClassTextUpdateHamletID()

export const CULT_CLASS_BOOK = "Book"
export const CULT_FREE_CLASS_BOOK = "Try For Free"

export const ENABLE_CENTER_SELECTION_FOR_PACK = true

export const MEMBERSHIP_AUDIT_TRAIL_EVENT_LIMIT = 10

export const CULT_RUN_WORKOUT_STAGE_EXPERIMENT_ID = 500

export const CULT_RUN_WORKOUT_PROD_EXPERIMENT_ID = 1021

export const QR_CODE_DISABLED_SEGMENT = "Users with QR disabled"

export const CULT_CHILDRENS_DAY_STAGE_WORKOUT_ID = 357
export const CULT_CHILDRENS_DAY_PROD_WORKOUT_ID = 357

export const SELECT_IMAGE_URL = "/image/packs/select.png"
export const PRO_IMAGE_URL = "/image/gymfit/gymfit.png"
export const ELITE_IMAGE_URL = "/image/packs/cult/CULTPACK38/80_mag.jpg"
export const DEFAULT_ELITE_IMAGE_URL_PRODUCTID = "CULTPACK38"
export const DEFAULT_IMAGE_URL = "/image/packs/cult/pack_banner.jpg"

export const CONTENT_CDN_BASE_PATH = "https://cdn-media.cure.fit/"
export const AUDIO_CDN_BASE_PATH = CONTENT_CDN_BASE_PATH + "audio/"
export const VIDEO_CDN_BASE_PATH = CONTENT_CDN_BASE_PATH + "video/"
export const CULT_MEDIA_CDN_BASE_PATH_DEPRECATED = "https://cdn-cult-media.cure.fit/"
export const CULT_MEDIA_IMAGE_CDN_BASE_PATH = "https://cdn-images.cure.fit/www-curefit-com/image/upload/cult-media/"
export const CULT_MEDIA_VIDEO_CDN_BASE_PATH = "https://cdn-videos.cure.fit/www-curefit-com/video/upload/cult-media/"
export const CULT_MEDIA_STAGING_CDN_BASE_PATH = "http://cdn-cult-media-staging.cure.fit/"

const imageExtensionPattern = /\.(jpg|jpeg|png|gif|bmp)$/

export const TransferPackNameByTransferType = {
    "ANOTHER_USER": "Another Member",
    "ANOTHER_CITY": "Another City"
}

export const TransferPackFlowTypes = {
    "select_transfer": "select_transfer",
    "elite_transfer": "elite_transfer"
}

export const BUDDIES_JOINING_STATE_LEGEND = [
    {
        state: "BOOKED",
        title: "Booked",
        color: "#44d7b6"
    },
    {
        state: "WAITLISTED",
        title: "Waitlisted",
        color: "#fadc4b"
    }
]

export interface CultInviteLazyPostBody {
    productType: ProductType
    message: string
    bookingNumber: string
    classId: number
}

export declare type ClientMembershipState = "ACTIVE" | "FUTURE" | "EXPIRED" | "PAUSED" | "UNKNOWN"
export interface CultPackProgressV2 {
    currentMembership: Membership
    packId: string
    total: number
    daysLeft: number
    current: number
    expiryMessage?: string
    startDate: string
    endDate: string
    lastPackEndDate: string
    isFutureMembershipPresent: boolean
    state: string
    clientMembershipState?: ClientMembershipState
}

export const BUDDIES_JOINING_SMALL_LIST_LIMIT = 50
export const BUDDIES_JOINING_SEPARATED_ICONS_LIMIT = 50

export const STREAK_LENGTH = 21
export const STREAK_GROUP_SIZE = 7

export const CULT_NAS_PRICE_HIKE_BANNER_ID = "e462e6a0-b264-4866-ae77-aa12c685b072"
export const MIND_NAS_PRICE_HIKE_BANNER_ID = "64267a55-e5a5-4344-b33c-7126903dd414"

export const PRO_NAS_PRICE_HIKE_BANNER_ID = "125d792e-58d5-4c39-a136-e32f7f7ab860"
export const NUDGE_BANNER_ID = "" // TODO: update this with nudge widgetID

export const FIRST_CLASS_PRE_WORKOUT_VIDEO = CdnUtil.getCdnUrl("curefit-content/video/Shwe+1-+Pre+First+Class.mp4")
export const FIRST_CLASS_POST_WORKOUT_VIDEO = CdnUtil.getCdnUrl("curefit-content/video/Rishabh+1-+Post+First+Class.mp4")
export const FIRST_CLASS_PRE_WORKOUT_THUMBNAIL = CdnUtil.getCdnUrl("curefit-content/image/icons/cultOnboarding/first_class_thumbnail.png")
export const FIRST_CLASS_POST_WORKOUT_THUMBNAIL = CdnUtil.getCdnUrl("curefit-content/image/icons/cultOnboarding/post_class_thumbnail.png")

export const CULT_WAITLIST_EXTENSION_RECOMMENDED_TIME = 30

// export const FITNESS_REPORT_EXPERIMENT_ID_STAGE = "40"
// export const FITNESS_REPORT_EXPERIMENT_ID_PROD = "36"
export const CULT_MEMORY_IMAGE_ASPECT_RATIO = 1.91

export type SWIMMING_SEGMENT_ID = "greater_that_50_swimming_usage" | "swimming_center_purchase"

export const CLASS_SCHEDULE_PAGE_BANNER_WIDGET_ID_PROD = "ed20a8af-d800-44a2-ad78-a1b1c8208245"
export const CLASS_SCHEDULE_PAGE_BANNER_WIDGET_ID_STAGE = "9a9ad5c8-b3a2-4b55-8782-e8622234d5c4"
export const CLASS_POST_CLASS_PAGE_BANNER_WIDGET_ID_PROD = "0aa86428-01f5-4e87-8fc4-4182069bfa86"

export const CLASS_POST_CLASS_CONFIRMATION_WIDGET_ID_PROD = "981ebdc8-3568-4435-93b3-7cfb4fd0df48"
export const CLASS_POST_CLASS_CONFIRMATION_WAITLIST_WIDGET_ID_PROD = "0aa86428-01f5-4e87-8fc4-4182069bfa86"

export const CULT_TRIAL_CLASS_SIPPER_WIDGET_ID_PROD = "a15da8a4-634e-42d7-aa8f-1d3c729f2531"
export const CULT_TRIAL_CLASS_SIPPER_WIDGET_ID_STAGE = "76622814-0e7d-416a-8ca3-90903c29318b"

export const DANCE_FITNESS_REVAMP_WORKOUT_ID = 383
export const STRENGTH_REVAMP_WORKOUT_ID = 374

export const CULT_CENTER_REOPEN_RELEASE_VERSION = 8.90
export const CULT_CENTER_REOPEN_IOS_CP_INTERNAL = 437
export const CULT_CENTER_REOPEN_IOS_CP_EXTERNAL = 192
export const CULT_CENTER_REOPEN_ANDROID_CP_INTERNAL = 466
export const CULT_CENTER_REOPEN_ANDROID_CP_EXTERNAL = 198

export const CULT_CENTER_SERVICE_MIGRATION_VERSION = 8.96

export const CULT_NEW_POLICIES_WIDGET_VERSION = 8.97
export const CULT_NEW_POLICIES_WIDGET_ANDROID_CP_INTERNAL = 472
export const CULT_NEW_POLICIES_WIDGET_IOS_CP_INTERNAL = 442
export const CULT_UPCOMING_TAG_VERSION = 9.12

export enum CenterServiceSKUs {
    CULTPASS_BLACK = 1,
    CULTPASS_GOLD = 2,
}

export const CULT_NEW_CENTER_DETAILS_PAGE_VERSION = 8.94
export const CULT_SHOW_NOTES_POST_BOOKING_CENTER_IDS = [128, 83]
export const CULT_SHOW_NOTES_POST_BOOKING_WORKOUT_IDS = [311, 19]
export const CULT_FULL_CAPACITY_CENTER_IDS: number[] = []
export const CULT_FULL_CAPACITY_UPCOMING_CENTER_IDS: number[] = []
export const CULT_FULL_CAPACITY_ACTIVE_FROM_DATE = "2021-11-24"
export const CULT_PRE_WORKOUT_FROM_DATE = "2022-05-08"
export const CULT_PRE_WORKOUT_UPCOMING_CENTER_IDS: number[] = []
export const CULT_PRE_WORKOUT_CENTER_IDS: number[] = []

export const BOXING_GLOVES_PROVIDED_CENTER_IDS: number[] = []
export const YOGA_MAT_PROVIDED_CENTER_IDS: number[]  = []

export const BOXING_BAG_WORKOUT_ID = 308
export const STRENGTH_PULSE = 447
export const HATHA_YOGA_WORKOUT_ID = 19
export const HRX_WORKOUT_ID = 22
export const DANCE_FITNESS_WORKOUT_ID = 84
export const CULT_RUN_WORKOUT_ID = 43
export const DUMMY_EVOLVE_YOGA_WORKOUT_ID = 441
export const DUMMY_STRENGTH_PLUS_WORKOUT_ID = 439

export const BEGINNER_FRIENDLY_WORKOUT_IDS = [HATHA_YOGA_WORKOUT_ID, HRX_WORKOUT_ID, DANCE_FITNESS_WORKOUT_ID, CULT_RUN_WORKOUT_ID]

const BOXING_GLOVES_PROVIDED_NOTES = [
    "Boxing gloves and handwraps will be provided at the center for use. You are not required to bring your own equipments."
]

const CLASS_FILMING_NOTES = [
    "We'll be filming the cult class today with a fitness Influencer for our Instagram handle, so you might spot yourself in the background. If you’d prefer not to be on camera, kindly let us know.",
    "Let’s have a strong session together!"
]

const BOXING_GLOVES_NOT_PROVIDED_NOTES = [
    "We request you to carry your own boxing gloves and hand wraps.",
    "Entry will not be allowed without gloves and hand wraps.",
    "You can pre-order gloves and wraps from the app or purchase them from the centre."
]

const UNBOUND_TERMS_AND_CONDITIONS = [
    "I am above 18 years of age.",
    "I am medically fit to participate in Cult Unbound.",
    "Cult will not be responsible for any injuries during the event.",
    "I agree not to use any drugs, performance-enhancing substances for the event."
]

const SMARTWATCH_CLASS_USERS_NOTES = [
    "Carry your smartwatch for the workout for an optimal experience.",
    "Only for Apple smartwatches.",
    "Do not forget to setup your Cult Watch app. Book this class to see how to setup."
]

export const SHOULD_NOT_SHOW_NEXT_STEPS_SEGMENT_ID = "d2495fa4-4f03-4114-a078-071d771de084"

export const ATLEAST_ONE_GX_CLASS_ATTENDED_SEGMENT = "26c27971-2021-4e29-9889-dded316261d3"

export const CULT_CLASS_ATTENDED_PLATFORM_SEGMENT = "Cult Class Attended"

export const HRX_USER_D30 = "HRX Class done in last 30 days"

export const CULT_MOVEMENT_PLACEHOLDER_IMAGE = "/image/placeholder/movement_image_placeholder_v2.png"
export const SOFTBOOKING_MODAL_PLACEHOLDER_IMAGE = "/image/placeholder/gym_softbooking_modal_placeholder.png"
export const SOFTBOOKING_MODAL_PLACEHOLDER_IMAGE_LUX = "/image/gymfit/lux_banner.jpg"

const ALL_CULT_BENEFIT_TICKET_EXHAUSTED = "/image/banners/sessions_exhausted.png"
const HAVE_REMAINING_CULT_BENEFIT_TICKET = "/image/banners/sessions_text.png"

export const workoutAbbreviationMap: any = {
    "S&C": "Strength & Conditioning",
    "HRX": "HRX - By Hrithik",
    "HRX Workout": "HRX - By Hrithik"
}

export const WORKOUT_RECOMMENDATION_MAP: any = {
    1: [314, 1],
    2: [5, 84, 43, 374, 12, 338],
    3: [5, 84, 43, 374, 12, 338]
}

export const ELITE_MEMBERSHIP_PRIMARY_BENEFITS: Benefit[] = [{
    name: "CULT",
    allowOverlap: false,
    type: BenefitType.STATIC,
}]


function _getSwimmingSegmentInternalToExternalMapping(): { [key in SWIMMING_SEGMENT_ID]: string } {
    const { APP_ENV } = process.env
    if (APP_ENV === "PRODUCTION" || APP_ENV === "ALPHA") {
        return {
            "greater_that_50_swimming_usage": "5dbff049cff47e00010bead3",
            "swimming_center_purchase": "5dba854acff47e00010bea63"
        }
    }
    return {
        "greater_that_50_swimming_usage": "5dbff000cff47e00010bead2",
        "swimming_center_purchase": "5dba84bacff47e00010bea5e"
    }
}

export declare type LivePTMembershipState = BundleOrderStatus
export declare type CultMembershipState = "ACTIVE" | "PAUSED" | "EXPIRING" | "EXPIRED" | "UPCOMING" | "ADVANCE_PAID" | "MANUAL_CANCELLED" | LivePTMembershipState

export function upgradePackName(originalPackName: string, productType?: string): string {
    const regExForPackName = /^[\d]+[\s]+Month/i
    if (!regExForPackName.test(originalPackName)) {
        // no match
        // return default string
        return "Cult Upgraded Pack"
    }
    const [match, ...rest] = regExForPackName.exec(originalPackName)
    return [
        match,
        `${productType && productType === "GYMFIT_FITNESS_PRODUCT" ? "Elite" : "Unlimited Cult"} Pack`
    ].join(" ")
}

export function isUpgradeMembershipOrder(order: Order): boolean {
    if (order.clientMetadata && isMembershipUpgradeClientMetadata(order.clientMetadata)) {
        return true
    }
    return false
}

export function isTransferMembershipOrder(order: Order): boolean {
    return (order.clientMetadata as CultPackTransferMetadata)?.isMembershipTransfer === true
}

class CultUtil {

    public static getCultCafeBlockAnalytics(booking: CultBooking): {
        eventKey: string,
        eventData: any
    } {
        return {
            eventKey: "widget_click",
            eventData: {
                widgetName: "add_supplement_click",
                bookingId: booking?.id,
                orderId: booking?.orderID,
                bookingType: booking?.bookingType,
                userId: booking?.userID
            }
        }
    }

    public static async isCultCenterReopenPageSupported(userContext: UserContext) {
        const { appVersion, osName, cpVersion } = userContext.sessionInfo
        if (appVersion >= CULT_CENTER_REOPEN_RELEASE_VERSION) {
            return true
        }
        if (osName.toLowerCase() === "ios") {
            const user = await userContext.userPromise
            if (user.isInternalUser) {
                return cpVersion >= CULT_CENTER_REOPEN_IOS_CP_INTERNAL
            } else {
                return cpVersion >= CULT_CENTER_REOPEN_IOS_CP_EXTERNAL
            }
        }

        if (osName.toLowerCase() === "android") {
            const user = await userContext.userPromise
            if (user.isInternalUser) {
                return cpVersion >= CULT_CENTER_REOPEN_ANDROID_CP_INTERNAL
            } else {
                return cpVersion >= CULT_CENTER_REOPEN_ANDROID_CP_EXTERNAL
            }
        }

        return false
    }

    public static async isCultCenterServiceMigrationSupported(userContext: UserContext, hamletBusiness: HamletBusiness) {
        if (AppUtil.isWeb(userContext)) {
            return true
        }

        const { appVersion } = userContext.sessionInfo
        if (appVersion >= CULT_CENTER_SERVICE_MIGRATION_VERSION) {
            return true
        }
        return false
    }

    public static isNewPolicyWidgetSupported(userContext: UserContext) {
        if (AppUtil.isWeb(userContext)) {
            return false
        }

        const { appVersion, osName, cpVersion } = userContext.sessionInfo
        if (appVersion >= CULT_NEW_POLICIES_WIDGET_VERSION) {
            return true
        } else {
            if (osName === "android") {
                return cpVersion >= CULT_NEW_POLICIES_WIDGET_ANDROID_CP_INTERNAL
            } else if (osName === "ios") {
                return cpVersion >= CULT_NEW_POLICIES_WIDGET_IOS_CP_INTERNAL
            }
        }
        return false
    }


    public static getCultCafeCTAText(cultCafeCatalogType?: CafeCatalogType): string {
        switch (cultCafeCatalogType) {
            case "FOOD":
                return "Supplement your workout with a Juice/Snack"
            case "FOOD_AND_GEAR":
                return "Pre-Order Workout Gear or a Snack"
            case "GEAR":
                return "Pre-Order Workout Gear"
            default:
                return "Supplement your workout with a Juice/Snack"
        }
    }

    public static isBuddyPackSupported(appVersion: number): boolean {
        if (appVersion >= 5.4)
            return true
    }

    public static async isUnpauseAndBookClassCtaSupported(userContext: UserContext, hamletBusiness: HamletBusiness) {
        const { appVersion } = userContext.sessionInfo
        return appVersion >= UNPAUSE_AND_BOOK_CTA_VERSION
    }

    public static async isWaitlistUnpauseBookSupported(userContext: UserContext) {
        const { appVersion } = userContext.sessionInfo
        return appVersion >= WL_UNPAUSE_AND_BOOK_CTA_VERSION
    }

    public static async isCultSportsCategorySupported(userContext: UserContext, hamletBusiness: HamletBusiness) {
        const { appVersion } = userContext.sessionInfo
        if (AppUtil.isWeb(userContext)) {
           return true
        }

        return appVersion >= CULT_SPORT_CATEGORY_VERSION
    }

    public static async isBaddyFitSupported(userContext: UserContext, hamletBusiness: HamletBusiness) {
        const { appVersion } = userContext.sessionInfo
        const experimentId = process.env.ENVIRONMENT === "STAGE" ? CULT_BADDYFIT_CATEGORY_STAGE_EXPERIMENT_ID : CULT_BADDYFIT_CATEGORY_PROD_EXPERIMENT_ID
        const isExperimentEnabled: boolean = await AppUtil.isExperimentEnabled(userContext, hamletBusiness, experimentId.toString(), "1")
        if (AppUtil.isWeb(userContext)) {
            return isExperimentEnabled
        }
        return isExperimentEnabled && appVersion >= CULT_SPORT_CATEGORY_VERSION
    }

    public static async isCultRunSupported(userContext: UserContext, hamletBusiness: HamletBusiness) {
        const { appVersion } = userContext.sessionInfo
        if (AppUtil.isWeb(userContext)) {
            return true
        }
        return  appVersion >= CULT_RUN_APP_VERSION
    }

    public static getBannerWidgetIdSkuPlus(months: number, isEliteFlow = true) {
        if (isEliteFlow) {
            switch (months) {
                case 12:
                    return "7db81680-a13a-4c6a-988d-0f767884779c"
                case 6:
                    return "da3a2b91-1271-4155-8bf2-05c4468f8e5f"
                case 3:
                    return "b5ce4a8c-4c8b-44c5-995c-d48a25ad977d"
                case 1:
                    return "b00c8b87-3bdd-4939-9257-b85b98633091"
                case 24:
                    return "d0fa7ce2-9b87-4755-a5a6-23f7ab6669e0"
                default:
                    return ""
            }
        } else {
            switch (months) {
                case 12:
                    return "634bb04b-9482-466a-958d-d85dbbdc123d"
                case 6:
                    return "2aed48af-6706-4bc6-b966-7b712acafe47"
                case 3:
                    return "0fadebe0-4c1b-421b-8d66-aef0933cc7ad"
                case 1:
                    return "3a40c249-7914-44b7-bbf2-83765a02f3cb"
                case 24:
                    return "097ad440-2a34-4bd8-a39c-d731041c57fe"
                default:
                    return ""
            }
        }
    }

    public static getBottomSheetBannerWidgetIdSkuPlus(months: number, isEliteFlow = true) {
        if (isEliteFlow) {
            switch (months) {
                case 12:
                    return "7db81680-a13a-4c6a-988d-0f767884779ca"
                case 6:
                    return "da3a2b91-1271-4155-8bf2-05c4468f8e5fa"
                case 3:
                    return "b5ce4a8c-4c8b-44c5-995c-d48a25ad977da"
                case 1:
                    return "b00c8b87-3bdd-4939-9257-b85b98633091a"
                case 24:
                    return "d0fa7ce2-9b87-4755-a5a6-23f7ab6669e0a"
                default:
                    return ""
            }
        } else {
            switch (months) {
                case 12:
                    return "634bb04b-9482-466a-958d-d85dbbdc123d1"
                case 6:
                    return "2aed48af-6706-4bc6-b966-7b712acafe471"
                case 3:
                    return "0fadebe0-4c1b-421b-8d66-aef0933cc7ad1"
                case 1:
                    return "3a40c249-7914-44b7-bbf2-83765a02f3cb1"
                case 24:
                    return "097ad440-2a34-4bd8-a39c-d731041c57fe1"
                default:
                    return ""
            }
        }
    }

    public static getBenefitTitleFromBenefitName(name: string, tickets?: number) {
        let title: string = undefined
        switch (name) {
            case "CULT":
                if (tickets) {
                    if (tickets >= 1000) title = "Unlimited"
                    else title = `${tickets} sessions`
                }
                break
            case "CULT_GYM":
                if (tickets) {
                    if (tickets >= 1000) title = "Unlimited"
                    else title = `${tickets} sessions`
                }
                break
            case "CULT_AWAY":
                if (tickets) {
                    title = `${tickets} sessions`
                }
                break
            case "PAUSE_DAYS":
                if (tickets) {
                    title = `${tickets} ${tickets > 1 ? "Days" : "Day"}`
                }
                break
            case "CF_LIVE":
                if (tickets) {
                    if (tickets >= 1000) title = "1000+"
                    else title = `${tickets}+`
                }
                break
            case "MEMBER_TRANSFER":
                title = "Free Transfer"
                break
            case "ADD_SEGMENT":
                title = "500 Off*"
                break
            case "PLAY":
                if (tickets) title = `${tickets} Extra`
                break
        }
        return title
    }

    public static getBenefitSubtitleFromBenefitName(name: string, metaText?: string, isElitePack?: boolean) {
        let subtitle: string = undefined
        switch (name) {
            case "CULT":
                subtitle = isElitePack ? "Group Class" : "Elite Center"
                break
            case "CULT_GYM":
                subtitle = "Gym Access"
                break
            case "CULT_AWAY":
                subtitle = "Across India"
                break
            case "PAUSE_DAYS":
                subtitle = "Pause"
                break
            case "CF_LIVE":
                subtitle = "Home Workouts"
                break
            case "MEMBER_TRANSFER":
                subtitle = metaText
                break
            case "ADD_SEGMENT":
                subtitle = "Cult Activewear"
                break
            case "PLAY":
                subtitle = "Play Sessions"
                break
        }
        return subtitle
    }

    public static getBenefitImageUrlFromBenefitName(name: string, isPlusPack?: boolean, isSelectPack?: boolean, isLitePack?: boolean, isElitePack?: boolean) {
        let imageUrl: string = undefined
        if (isPlusPack) {
            switch (name) {
                case "CULT":
                    imageUrl = isSelectPack ? "/image/sku_plus_icons/group+classes_sp.svg" : "/image/sku_plus_icons/group+classes_ep.svg"
                    break
                case "CULT_GYM":
                    imageUrl = isSelectPack ? "/image/sku_plus_icons/gym_sp.svg" : "/image/sku_plus_icons/gym_ep.svg"
                    break
                case "CULT_AWAY":
                    imageUrl = isSelectPack ? "/image/sku_plus_icons/membership_sp.svg" : "/image/sku_plus_icons/membership_ep.svg"
                    break
                case "PAUSE_DAYS":
                    imageUrl = isSelectPack ? "/image/sku_plus_icons/pause_sp.svg" : "/image/sku_plus_icons/pause_elite.svg"
                    break
                case "CF_LIVE":
                    imageUrl = isSelectPack ? "/image/sku_plus_icons/live_sp.svg" : "/image/sku_plus_icons/home_workouts_ep.svg"
                    break
                case "MEMBER_TRANSFER":
                    imageUrl = "/image/sku_plus_icons/membership_transfer_elite.svg"
                    break
                case "ADD_SEGMENT":
                    imageUrl = "/image/sku_plus_icons/cult+activewear_ep.svg"
                    break
                case "PLAY":
                    imageUrl = "/image/sku_plus_icons/play_sessions.svg"
                    break
            }
        } else if (isLitePack) {
            switch (name) {
                case "CULT":
                    imageUrl = isSelectPack ? "/image/sku_plus_icons/group_classes.svg" : "/image/sku_icons/group_class_el.svg"
                    break
                case "CULT_GYM":
                    imageUrl = isSelectPack ? "/image/sku_plus_icons/gym.svg" : "/image/sku_icons/gym_el.svg"
                    break
                case "CULT_AWAY":
                    imageUrl = isSelectPack ? "/image/sku_plus_icons/membership.svg" : "/image/sku_icons/membership_el.svg"
                    break
                case "PAUSE_DAYS":
                    imageUrl = isSelectPack ? "/image/sku_plus_icons/pause.svg" : "/image/sku_icons/pause_el.svg"
                    break
                case "CF_LIVE":
                    imageUrl = isSelectPack ? "/image/sku_plus_icons/home.svg" : "/image/sku_icons/home_el.svg"
                    break
                case "MEMBER_TRANSFER":
                    imageUrl = "/image/sku_icons/transfer_el.svg"
                    break
                case "ADD_SEGMENT":
                    imageUrl = "/image/sku_icons/cult_activewear_el.svg"
                    break
                case "PLAY":
                    imageUrl = "/image/sku_icons/play_el.svg"
                    break
            }
        } else if (isSelectPack) {
            switch (name) {
                case "CULT":
                    imageUrl = "/image/sku_icons/group_class_select.svg"
                    break
                case "CULT_GYM":
                    imageUrl = "/image/sku_icons/gym_select.svg"
                    break
                case "CULT_AWAY":
                    imageUrl = "/image/sku_icons/membership_select.svg"
                    break
                case "PAUSE_DAYS":
                    imageUrl = "/image/sku_icons/pause_select.svg"
                    break
                case "CF_LIVE":
                    imageUrl = "/image/sku_icons/live_select.svg"
                    break
                case "MEMBER_TRANSFER":
                    imageUrl = "/image/sku_plus_icons/transfer.svg"
                    break
            }
        } else {
            switch (name) {
                case "CULT":
                    imageUrl = isElitePack ? "/image/sku_icons/group_class_eg.svg" : "/image/sku_plus_icons/group_classes.svg"
                    break
                case "CULT_GYM":
                    imageUrl = isElitePack ? "/image/sku_icons/gym_eg.svg" : "/image/sku_plus_icons/gym.svg"
                    break
                case "CULT_AWAY":
                    imageUrl = isElitePack ? "/image/sku_icons/membership_eg.svg" : "/image/sku_plus_icons/membership.svg"
                    break
                case "PAUSE_DAYS":
                    imageUrl = isElitePack ? "/image/sku_icons/pause_eg.svg" : "/image/sku_plus_icons/pause.svg"
                    break
                case "CF_LIVE":
                    imageUrl = isElitePack ? "/image/sku_icons/home_eg.svg" : "/image/sku_plus_icons/home.svg"
                    break
                case "MEMBER_TRANSFER":
                    imageUrl = isElitePack ? "/image/sku_icons/transfer_eg.svg" : "/image/sku_plus_icons/transfer.svg"
                    break
                case "ADD_SEGMENT":
                    imageUrl = isElitePack ? "/image/sku_icons/cult_activewear_eg.svg" : "/image/sku_plus_icons/cult_activewear.svg"
                    break
                case "PLAY":
                    imageUrl = isElitePack ? "/image/sku_icons/play_eg.svg" : "/image/sku_plus_icons/play.svg"
                    break
            }
        }
        return imageUrl
    }

    public static getOfferIconUrlFromOfferType(offerType?: OfferAddonType) {
        if (!_.isNil(offerType)) {
            if (offerType === "CULT_EXTENSION" || offerType === "GYMFIT_EXTENSION") {
                return "image/icons/cult/extension.svg"
            } else if (offerType === "XOXODAY_COUPON") {
                return "/image/icons/cult/voucher.svg"
            }
        }
        return "/image/icons/cult/offer_tag.svg"
    }

    public static async iBootcampBookingSupported(userContext: UserContext, interfaces: CFServiceInterfaces) {
        userContext.userProfile.promiseMapCache ??= new PromiseCache(interfaces)
        const { appVersion } = userContext.sessionInfo
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        const bootcampPurchaseSegmentId: string = process.env.ENVIRONMENT === "PRODUCTION" || process.env.ENVIRONMENT === "ALPHA" ? "7d57c05d-5c87-422f-bfa0-cd73557e0a30" : "08eb38e2-52be-422a-b295-c0a9d6ad4d68"
        const bootcampPostPurchaseSegment = await interfaces.segmentService.doesUserBelongToSegment(bootcampPurchaseSegmentId, userContext)
        return appVersion >= BOOTCAMP_APP_VERSION && _.isEmpty(bootcampPostPurchaseSegment)
    }

    public static async getSportCategoryWorkoutIds(): Promise<number[]> {
        if (process.env.ENVIRONMENT === "STAGE") {
            return CULT_SPORT_CATEGORY_STAGE_WORKOUT_IDS
        }
        return CULT_SPORT_CATEGORY_PROD_WORKOUT_IDS
    }

    public static async getBaddyFitWorkoutIds(): Promise<number[]> {
        if (process.env.ENVIRONMENT === "STAGE") {
            return CULT_BADDYFIT_STAGE_WORKOUT_IDS
        }
        return CULT_BADDYFIT_PROD_WORKOUT_IDS
    }

    public static async getCultRunWorkoutIds(): Promise<number[]> {
        if (process.env.ENVIRONMENT === "PRODUCTION") {
            return [CULT_RUN_PROD_WORKOUT_ID]
        }
        return [CULT_RUN_STAGE_WORKOUT_ID]
    }

    public static async getSportCategoryCenterIds(): Promise<number[]> {
        if (process.env.ENVIRONMENT === "STAGE") {
            return CULT_SPORT_CATEGORY_STAGE_CENTER_IDS
        }
        return CULT_SPORT_CATEGORY_PROD_CENTER_IDS
    }

    public static async getBootcampWorkoutIds(): Promise<number[]> {
        if (process.env.ENVIRONMENT === "PRODUCTION") {
            return [BOOTCAMP_PROD_WORKOUT_ID]
        }
        return [BOOTCAMP_STAGE_WORKOUT_ID]
    }

    public static isBootcampClass(workoutID: number): boolean {
        if (process.env.ENVIRONMENT === "PRODUCTION") {
            return BOOTCAMP_PROD_WORKOUT_ID === workoutID
        }
        return BOOTCAMP_STAGE_WORKOUT_ID === workoutID
    }

    public static isCultScoreWorkout(workoutId: number): boolean {
        const index: number = _.findIndex(CULTSCORE_WORKOUT_IDS, workoutId)
        return (index >= 0) ? true : false
    }

    public static showCenterSelection(userContext: UserContext): boolean {
        if (userContext.sessionInfo.userAgent === "MBROWSER" || userContext.sessionInfo.userAgent === "DESKTOP") {
            return ENABLE_CENTER_SELECTION_FOR_PACK
        }

        if (userContext.sessionInfo.appVersion < 6.72)
            return true
        else if (userContext.sessionInfo.appVersion === 6.72) {
            if (userContext.sessionInfo.osName === "ios" && userContext.sessionInfo.cpVersion >= 59) {
                return ENABLE_CENTER_SELECTION_FOR_PACK
            } else if (userContext.sessionInfo.osName === "android" && userContext.sessionInfo.cpVersion >= 65) {
                return ENABLE_CENTER_SELECTION_FOR_PACK
            }
            return true
        } else {
            return ENABLE_CENTER_SELECTION_FOR_PACK
        }

    }

    public static async isCultFormatImpressionCompleted(userId: string, workoutID: number, crudDao: ICrudKeyValue): Promise<boolean> {
        let redisKey: string = ""
        if (workoutID == DANCE_FITNESS_REVAMP_WORKOUT_ID) {
             redisKey = "dance_fitness_impression_count_" + userId
        } else if (workoutID == STRENGTH_REVAMP_WORKOUT_ID) {
             redisKey = "strength_impression_count_" + userId
        }
        return crudDao.read(redisKey).then(function (payload) {
            if (payload) {
                const impressionCount = JSON.parse(payload)
                if (_.isNil(impressionCount["count"]) || _.isEmpty(impressionCount["count"])) {
                    crudDao.create(redisKey, `{"count":"0"}`)
                    return true
                }
                return !(parseInt(impressionCount["count"]) >= 4)
            } else {
                crudDao.create(redisKey, `{"count":"0"}`)
                return true
            }
        })
    }

    public static async increaseCultFormatImpressionCount(userId: string, workoutID: number, crudDao: ICrudKeyValue): Promise<boolean> {
        let redisKey: string = ""
        if (workoutID == DANCE_FITNESS_REVAMP_WORKOUT_ID) {
            redisKey = "dance_fitness_impression_count_" + userId
        } else if (workoutID == STRENGTH_REVAMP_WORKOUT_ID) {
            redisKey = "strength_impression_count_" + userId
        }
        let count = 1
        await crudDao.read(redisKey).then(async function (payload) {
            if (payload) {
                const impressionCount = JSON.parse(payload)
                count = parseInt(impressionCount["count"]) + 1
            }
        })
        const val = await crudDao.update(redisKey, `{"count":"${count}"}`)
        return true
    }

    public static async checkStateOfCultFormatImpressionCount(userId: string, workoutID: number, crudDao: ICrudKeyValue): Promise<boolean> {
        let redisKey: string = ""
        if (workoutID == DANCE_FITNESS_REVAMP_WORKOUT_ID) {
            redisKey = "dance_fitness_tooltip_status_" + userId
        } else if (workoutID == STRENGTH_REVAMP_WORKOUT_ID) {
            redisKey = "strength_tooltip_status_" + userId
        }
        return crudDao.read(redisKey).then(function (payload) {
            if (payload) {
                const data = JSON.parse(payload)
                if (_.isNil(data["status"]) || _.isEmpty(data["status"])) {
                    crudDao.create(redisKey, `{"status":"CREATED"}`)
                    return true
                }
                return data["status"] === "CREATED"
            } else {
                crudDao.create(redisKey, `{"status":"CREATED"}`)
                return true
            }
        })
    }


    public static isPauseResumeAllowed(userContext: UserContext, membership: CultMembership): boolean {
        if (CultUtil.isMembershipActive(userContext, membership) && membership.state !== "ADVANCE_PAID" && !CultUtil.isUpcomingPause(membership)) {
            return true
        }
        return false
    }

    public static isUpcomingPause(membership: CultMembership): boolean {
        return membership.state === "PURCHASED" && !_.isEmpty(membership.ActivePause)
    }

    public static isMessgaeInterventionSupported(userContext: UserContext): boolean {

        if (userContext.sessionInfo.osName === "android" && userContext.sessionInfo.appVersion > MESSAGE_INTERVENTION_VERSION_ANDROID) {
            return true
        }

        if (userContext.sessionInfo.osName === "ios" && userContext.sessionInfo.appVersion > MESSAGE_INTERVENTION_VERSION_IOS) {
            return true
        }

        if (userContext.sessionInfo.osName === "android" && userContext.sessionInfo.appVersion === MESSAGE_INTERVENTION_VERSION_ANDROID && userContext.sessionInfo.cpVersion >= MESSAGE_INTERVENTION_CP_VERSION_ANDROID) {
            return true
        }
        if (userContext.sessionInfo.osName === "ios" && userContext.sessionInfo.appVersion === MESSAGE_INTERVENTION_VERSION_IOS && userContext.sessionInfo.cpVersion >= MESSAGE_INTERVENTION_CP_VERSION_IOS) {
            return true
        }

        if (userContext.sessionInfo.osName === "browser" || userContext.sessionInfo.osName === "janus")
            return false

        return false

    }

    public static isProgramSupported(user: User, userAgent: UserAgent, appVersion: number, codepushVersion: number, osName: string): boolean {
        if (process.env.ENVIRONMENT === "STAGE")
            return true
        if (userAgent === "APP") {
            if (osName === "android" && (appVersion > PROGRAM_APP_VERSION_ANDROID || codepushVersion >= PROGRAM_CP_VERSION_ANDROID)) {
                return true
            }
            if (osName === "ios" && (appVersion > PROGRAM_APP_VERSION_IOS || codepushVersion >= PROGRAM_CP_VERSION_IOS)) {
                return true
            }
        } else {
            return true
        }
    }
    public static isOgmSupported(userAgent: UserAgent, appVersion: number): boolean {
        if (userAgent === "APP" && appVersion >= 5.971)
            return true
        return false
    }

    public static isPausePackOnCLPSupported(appVersion: number, osName: string): boolean {
        if (osName === "android" && appVersion >= CULT_CLP_PAUSE_PACK_VERSION_ANDROID) {
            return true
        }

        if (osName === "ios" && appVersion >= CULT_CLP_PAUSE_PACK_VERSION_IOS) {
            return true
        }
        return false
    }

    public static isBookingPreferenceSupported(userContext: UserContext): boolean {
        if ((userContext.sessionInfo.osName === "android" && userContext.sessionInfo.appVersion >= BOOKING_PREFERENCE_VERSION_ANDROID) || (userContext.sessionInfo.osName === "ios" && userContext.sessionInfo.appVersion >= BOOKING_PREFERENCE_VERSION_IOS)) {
            return true
        }
        return false
    }

    public static isCultInstructionModalSupported(userContext: UserContext): boolean {
        if ((userContext.sessionInfo.osName === "android" && userContext.sessionInfo.appVersion >= INSTRUCTION_MODAL_SUPPORTED_ANDROID) || (userContext.sessionInfo.osName === "ios" && userContext.sessionInfo.appVersion >= INSTRUCTION_MODAL_SUPPORTED_IOS)) {
            return true
        }
        return false
    }

    public static bookClassTitle(data: { memberships: CultMembership[], isEligibleForFreeClass: boolean }): string {
        /*        if (!data.membership && data.freeClassesAvailableCount && data.freeClassesAvailableCount > 0) {
                    if (data.freeClassesAvailableCount === CultFreeClassCount) {
                        return "Book 1st Cult Class"
                    }
                }*/
        return "Book a Cult Class"
    }

    /**
     *
     * @param {cultClass} - a workout name
     * @param {userContext} - determine if user should Pulse specific stuff
     * @returns {ClassName} - if it's a Pulse Class, pre-pend with the text "PULSE"
     * otherwise, return normal name
     */
    public static pulsifyClassName(cultClass: CultClass, userContext?: UserContext): string {
        if (this.isClassAvailableForPulse(cultClass, AppUtil.isCultPulseFeatureSupported(userContext))) {
            // it's a valid pulse class
            return ["PULSE", cultClass.Workout.name].join(" - ")
        }
        return cultClass.Workout.name || ""
    }

    public static getMembershipAudientEventLimit(): number {
        return MEMBERSHIP_AUDIT_TRAIL_EVENT_LIMIT
    }


    public static getCultCenterAddress(center: CultCenter, userContext?: UserContext) {
        let addressString = center.Address.addressLine1 + ", "
        const userAgent = _.get(userContext, "sessionInfo.userAgent", "")
        if (_.isNil(center.Address.displayCityName)) {
            addressString += center.Address.City.name
        } else {
            addressString += center.Address.displayCityName
        }
        if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
            const stateName = _.get(center, "Address.state", "")
            const pincode = _.get(center, "Address.pinCode", "")
            if (stateName) {
                addressString = `${addressString}, ${stateName}`
            }
            if (pincode) {
                addressString = `${addressString} ${pincode}`
            }
        }
        return addressString
    }

    public static offerDetails(data: { memberships: CultMembership[], isEligibleForFreeClass: boolean }): { title: string, subTitle: string } {
        if (_.isEmpty(data.memberships) && data.isEligibleForFreeClass) {
            return { title: "Introductory offer", subTitle: `"First two Classes are FREE. Book now!"` }
        }
        return undefined
    }

    public static getCallTime(minutesBefore: number, classStartTime: string): string {
        const startTimeArray = classStartTime.split(":")
        if (startTimeArray.length < 2) return ""
        const hourMin = {
            hour: (Number(startTimeArray[0])),
            min: (Number(startTimeArray[1]))
        }
        const callTime = TimeUtil.addHourMin(hourMin, -minutesBefore)
        const postfix = callTime.hour >= 12 ? "PM" : "AM"
        const formattedHours = callTime.hour > 12 ? callTime.hour % 12 : (callTime.hour === 0 ? 12 : callTime.hour)
        return `${formattedHours}:${Math.abs(callTime.min).toString().padStart(2, "0")} ${postfix}`
    }

    public static getCallTimeBefore(minutesBefore: number): string {
        const hours = Math.floor(minutesBefore / 60)
        const mins = minutesBefore % 60
        return `${hours} Hr ${mins.toString().padStart(2, "0")} Min`
    }

    public static getCallTimeBeforeV2(minutesBefore: number): string {
        const hours = Math.floor(minutesBefore / 60)
        const mins = minutesBefore % 60

        const hourString = hours > 0 ? `${hours} hour${hours > 1 ? "s" : ""}` : ""
        const minsString = mins > 0 ? `${mins} min${mins > 1 ? "s" : ""}` : ""

        return [hourString, minsString].filter(Boolean).join(" ")
    }

    public static getCallTimeBeforeLines(minutesBefore: number): string[] {
        const hours = Math.floor(minutesBefore / 60)
        const mins = minutesBefore % 60

        if (hours === 0) {
            return [`${mins}`, "mins"]
        } else if (mins === 0) {
            return [`${hours}`, `hour${hours > 1 ? "s" : ""}`]
        } else {
            const hourString: string = `${hours} hour${hours > 1 ? "s" : ""}`
            const minsString: string = `${mins} min${mins > 1 ? "s" : ""}`
            return [hourString, minsString]
        }
    }

    public static isMostOptedTime(currentMins: number): boolean {
        return (currentMins === 120)
    }

    public static getTimeSlots(ivrConfig: {
        status: boolean;
        isIVREditable: boolean;
        minutesBefore?: number;
        isIVRApplicable?: boolean;
        isIVRProcessed?: boolean;
        granularityMinutes?: number;
        minMinutesBefore?: number;
        maxMinutesBefore?: number;
        ivrState: string;
        alarmProcessingDateTimeUTC?: string;
    }) {
        const timeBlocks: any = []
        const timeSlots: any = []
        const selectedBlock = ivrConfig.minutesBefore ? {
            "key": ivrConfig.minutesBefore.toString(),
        } : null
        let currentMins = ivrConfig.minMinutesBefore
        while (currentMins <= ivrConfig.maxMinutesBefore) {
            const lines = CultUtil.getCallTimeBeforeLines(currentMins)
            const timeBlock = {
                "key": currentMins.toString(),
                "minutesBefore": currentMins,
                "line1": lines[0],
                "line2": lines[1],
                "isMostOpted": CultUtil.isMostOptedTime(currentMins),
            }
            const timeSlot = {
                "minutesBefore": currentMins,
                "callTimeBefore": CultUtil.getCallTimeBeforeV2(currentMins),
                "key": currentMins.toString(),
                "mostOpted": CultUtil.isMostOptedTime(currentMins)
            }
            timeBlocks.push(timeBlock)
            timeSlots.push(timeSlot)
            currentMins += ivrConfig.granularityMinutes
        }
        return [timeBlocks, timeSlots, selectedBlock]
    }

    public static getIVRReminderCardWidget(userContext: UserContext, ivrConfig: {
        status: boolean;
        isIVREditable: boolean;
        minutesBefore?: number;
        isIVRApplicable?: boolean;
        isIVRProcessed?: boolean;
        granularityMinutes?: number;
        minMinutesBefore?: number;
        maxMinutesBefore?: number;
        ivrState: string;
        alarmProcessingDateTimeUTC?: string;
    }, bookingNumber?: string, title?: string) {
        const data = this.getTimeSlots(ivrConfig)
        return {
            "widgetType": "IVR_REMINDER_CARD_WIDGET",
            "title": title ?? "Set a call Reminder",
            "bottomLabel": "Enable for all Upcoming classes",
            "bookingNumber": bookingNumber,
            "timeBlocks": [
                ...data[0],
                {
                    "key": "Other",
                    "line1": "Other",
                    "isMostOpted": false,
                    "action": {
                        "actionType": "SET_CALL_REMINDER",
                        "meta": {
                            "isFromIVRReminderCard": true,
                            "bookingNumber": bookingNumber,
                            "isEditable": true,
                            "slots": {
                                "title": "Set time for call reminder",
                                "slotTimes": data[1]
                            },
                        },
                        "analyticsData": {
                            "widgetName": "Call_Time_Selection_Click",
                            "bookingId": bookingNumber,
                            "userId": userContext.userProfile.userId
                        }
                    }
                },
            ],
            "selectedTimeBlock": data[2],
            "enableForFuture": true,
        }
    }
    public static canShowNewIVRReminderWidget(classDate: string, classTime: string, timeZone: Timezone): boolean {
        // if class timing is after 90 min of this moment now
        return TimeUtil.getMomentForDateString(classDate, timeZone, "YYYY-MM-DD").isAfter(TimeUtil.getMomentNow(timeZone)) ||
        TimeUtil.getMomentForDate(
            TimeUtil.getDate(classDate, Number(classTime.split(":")[0]), Number(classTime.split(":")[1]), timeZone), timeZone
        ).diff(TimeUtil.getMomentNow(timeZone), "minutes") >= 90
    }

    public static getSlotTimes(ivrConfig: any, addNoReminder: boolean, callTimeBeforePostFix: string, classStartTime?: string): CallReminderSlotTime[] {
        const slots = []
        if (addNoReminder) {
            slots.push({ callTimeBefore: "No Reminder", selected: ivrConfig.ivrStatus })
        }
        let currentMins = ivrConfig.minMinutesBefore
        while (currentMins <= ivrConfig.maxMinutesBefore) {
            // const callTimeBefore = `${CultUtil.getCallTimeBefore(currentMins)} ${callTimeBeforePostFix}`
            const slot: CallReminderSlotTime = {
                minutesBefore: currentMins,
                callTimeBefore: CultUtil.getCallTimeBeforeV2(currentMins),
                selected: ivrConfig.minutesBefore === currentMins,
                key: currentMins.toString(),
                mostOpted: CultUtil.isMostOptedTime(currentMins)
            }
            slots.push(slot)
            currentMins += ivrConfig.granularityMinutes
        }
        return slots
    }

    public static isHybridGymCenter(center: CenterResponse): boolean {
        return center.vertical === CenterVertical.GYMFIT && center.linkedCenterId != null
    }

    public static filterHybridGymCenters(centers: CenterResponse[], userContext: UserContext): CenterResponse[] {
        if (!AppUtil.isHybridCentersSupported(userContext)) {
            return centers
        }
        return _.filter(centers, (center) => {
            return !CultUtil.isHybridGymCenter(center)
        })
    }

    public static getSlotSelectedAction(isWaitlisted: boolean, ivrStatus: boolean): SlotSelectedAction {
        return {
            actionType: isWaitlisted ? "ALERT" : "TOAST",
            title: ivrStatus ? (isWaitlisted ? "Call reminder time changed" : "Call reminder time has been successfully changed") : "Call reminder removed",
            message: ivrStatus ? (isWaitlisted ? "If your waitlist gets confirmed then we will give you a call at the time selected by you" : "") : "",
            actionButton: isWaitlisted ? { title: "Ok", actionType: "HIDE_ALERT_MODAL", url: "" } : undefined
        }
    }

    public static getDescriptionFromPackId(productId: string, productType: ProductType = "FITNESS") {
        // TODO: Get this data from cult service
        switch (productId) {
            case "CULTPACK13":
                return "3 months pack"
            case "CULTPACK14":
                return "4 weekends pack"
            case "CULTPACK15":
                return "6 months pack"
            case "CULTPACK16":
                return "12 months pack"
            case "CULTPACK18":
                return "3 months buddy pack"
            case "CULTPACK19":
                return "3 months gift pack"
            case "CULTPACK25":
                return "10 months pack"
            case "CULTPACK32":
                return productType === "MIND" ? "3 months unlimited pack" : "Fitness pack at Cult"
            default:
                return productType === "FITNESS" ? "Fitness pack at Cult" : "mind.fit pack"
        }
    }

    public static getDescriptionFromProductId(productId: string, productType: string = "FITNESS") {
        // TODO: Get this data from cult service
        switch (productId) {
            case "CULTPACK13":
                return "3 months pack"
            case "CULTPACK14":
                return "4 weekends pack"
            case "CULTPACK15":
                return "6 months pack"
            case "CULTPACK16":
                return "12 months pack"
            default:
                return productType === "FITNESS" ? "Fitness pack at Cult" : "mind.fit pack"
        }
    }

    public static isCultEventOrder(product: OrderProduct): boolean {
        if (!_.isNil(product.option.fitnessEventId))
            return true
        return false
    }

    public static isMembershipExpired(userContext: UserContext, membership: Membership): boolean {
        const tz = userContext.userProfile.timezone
        const today = TimeUtil.todaysDateWithTimezone(tz)
        const membershipEndDate = TimeUtil.formatEpochInTimeZone(tz, membership.end)
        return (today > membershipEndDate)
    }

    public static isMembershipUpcoming(userContext: UserContext, membership: Membership): boolean {
        const tz = userContext.userProfile.timezone
        const today = TimeUtil.todaysDateWithTimezone(tz)
        const membershipStartDate = TimeUtil.formatEpochInTimeZone(tz, membership.start)
        return membershipStartDate > today
    }

    public static isMindFitAvailable(cityId: string): boolean {
        return cityId === "Bangalore" || cityId === "Hyderabad" ? true : false
    }
    public static getCurrentMembership(memberships: CultMembership[]): CultMembership {
        if (_.isEmpty(memberships)) {
            return undefined
        }

        const sortedMemeberships = memberships.sort((a, b) => {
            return a.startDate < b.startDate ? -1 : 1
        })

        const currentMembership = sortedMemeberships[0]
        return currentMembership
    }

    public static getCustomWorkoutName(workoutName: string): string {
        if (workoutName === "Hatha Beginner") {
            return "Hatha yoga"
        } else if (workoutName === "APM Beginner") {
            return "Asana, Pranayama, Meditation"
        } else if (workoutName === "Core Hatha Beginner") {
            return "Core Hatha Yoga"
        }
        return workoutName
    }

    public static getCenterLaunchDateText(userContext: UserContext, launchDate: string): string {
        const tz = userContext.userProfile.timezone
        if (launchDate) {
            const numDays = TimeUtil.diffInDaysReal(tz, launchDate, TimeUtil.todaysDate(tz, "YYYY-MM-DD"))
            if (numDays > 0) {
                const launchingDate = TimeUtil.getDayText(launchDate, tz)
                return `Launching ${launchingDate}`
            }
            return undefined
        }
    }

    public static cultPackProgress(userContext: UserContext, memberships: CultMembership[], selectedMembership?: CultMembership): CultPackProgress {
        const tz = userContext.userProfile.timezone

        if (_.isEmpty(memberships)) {
            return undefined
        }

        const membershipItems: CultMembership[] = clone(memberships)

        const sortedMemeberships = membershipItems.sort((a, b) => {
            return a.endDate < b.endDate ? -1 : 1
        })

        const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDateWithTimezone(tz), tz)
        let activeMembership
        let lastExpiredMembership
        let futureMembership

        if (selectedMembership) {
            const endDate = TimeUtil.getMomentForDateString(selectedMembership.endDate, tz)
            const startDate = TimeUtil.getMomentForDateString(selectedMembership.startDate, tz)
            if (startDate.isSameOrBefore(today, "day") && endDate.isSameOrAfter(today, "day")) {
                activeMembership = selectedMembership
            }
            if (!activeMembership) {
                if (startDate.isSameOrAfter(today, "day")) {
                    futureMembership = selectedMembership
                }
            }
            if (!futureMembership) {
                const now = TimeUtil.getMomentForDateString(TimeUtil.todaysDateWithTimezone(tz), tz)
                if (endDate.isBefore(now, "day") && endDate.isAfter(now.subtract(30, "days"), "day")) {
                    lastExpiredMembership = selectedMembership
                }
            }

        } else {

            activeMembership = _.first(_.filter(sortedMemeberships, (obj) => {
                const endDate = TimeUtil.getMomentForDateString(obj.endDate, tz)
                const startDate = TimeUtil.getMomentForDateString(obj.startDate, tz)
                return startDate.isSameOrBefore(today, "day") && endDate.isSameOrAfter(today, "day")
            }))

            if (!activeMembership) {
                futureMembership = _.first(_.filter(sortedMemeberships, (obj) => {
                    const startDate = TimeUtil.getMomentForDateString(obj.startDate, tz)
                    return startDate.isSameOrAfter(today, "day")
                }))
            }

            if (!futureMembership) {
                lastExpiredMembership = _.last(_.filter(sortedMemeberships, (obj) => {
                    const endDate = TimeUtil.getMomentForDateString(obj.endDate, tz)
                    const now = TimeUtil.getMomentForDateString(TimeUtil.todaysDateWithTimezone(tz), tz)
                    return endDate.isBefore(now, "day") && endDate.isAfter(now.subtract(30, "days"), "day")
                }))
            }
        }


        const finalMemberShip = activeMembership || futureMembership || lastExpiredMembership

        if (finalMemberShip) {
            const endDate = TimeUtil.getMomentForDateString(finalMemberShip.endDate, tz)
            const startDate = TimeUtil.getMomentForDateString(finalMemberShip.startDate, tz)

            const daysLeft = endDate.diff(today, "days")
            let expiryMessage: string = undefined
            if (daysLeft <= 7 && activeMembership) { // If there is only upto 7 days left and no future membership
                expiryMessage = "Pack expiring! Renew on-time for discount."
            }

            const total = endDate.diff(startDate, "days")
            const current = today.diff(startDate, "days")
            return {
                productId: finalMemberShip.productID,
                total: endDate.diff(startDate, "days"),
                current: Math.min(current, total), // If Expired, set current to total.
                expiryMessage: expiryMessage,
                startDate: finalMemberShip.startDate,
                endDate: finalMemberShip.endDate,
                lastPackEndDate: sortedMemeberships[sortedMemeberships.length - 1].endDate,
                daysLeft: daysLeft,
                isFutureMembershipPresent: sortedMemeberships.length > 1 ? true : false,
                currentMembership: finalMemberShip,
                state: finalMemberShip.state,
                clientMembershipState: activeMembership ? "ACTIVE" : futureMembership ? "FUTURE" : lastExpiredMembership ? "EXPIRED" : "UNKNOWN"
            }

        } else {
            return undefined
        }

    }

    public static cultPackProgressV2(userContext: UserContext, memberships: Membership[], selectedMembership: Membership): CultPackProgressV2 {
        const tz = userContext.userProfile.timezone

        if (_.isEmpty(memberships)) {
            return undefined
        }

        const membershipItems: Membership[] = clone(memberships)

        const sortedMemeberships = membershipItems.sort((a, b) => {
            return a.end < b.end ? -1 : 1
        })

        const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDateWithTimezone(tz), tz)
        let activeMembership
        let lastExpiredMembership
        let futureMembership

        if (selectedMembership) {
            const endDate = TimeUtil.getMomentForDateString(TimeUtil.formatEpochInTimeZone(tz, selectedMembership.end), tz)
            const startDate = TimeUtil.getMomentForDateString(TimeUtil.formatEpochInTimeZone(tz, selectedMembership.start), tz)
            if (startDate.isSameOrBefore(today, "day") && endDate.isSameOrAfter(today, "day")) {
                activeMembership = selectedMembership
            }
            if (!activeMembership) {
                if (startDate.isAfter(today, "day")) {
                    futureMembership = selectedMembership
                }
            }
            if (!futureMembership) {
                if (endDate.isBefore(today, "day") && endDate.isAfter(today.subtract(30, "days"), "day")) {
                    lastExpiredMembership = selectedMembership
                }
            }

        } else {

            activeMembership = _.first(_.filter(sortedMemeberships, (obj) => {
                const endDate = TimeUtil.getMomentForDateString(TimeUtil.formatEpochInTimeZone(tz, obj.end), tz)
                const startDate = TimeUtil.getMomentForDateString(TimeUtil.formatEpochInTimeZone(tz, obj.start), tz)
                return startDate.isSameOrBefore(today, "day") && endDate.isSameOrAfter(today, "day")
            }))

            if (!activeMembership) {
                futureMembership = _.first(_.filter(sortedMemeberships, (obj) => {
                    const startDate = TimeUtil.getMomentForDateString(TimeUtil.formatEpochInTimeZone(tz, obj.start), tz)
                    return startDate.isAfter(today, "day")
                }))
            }

            if (!futureMembership) {
                lastExpiredMembership = _.last(_.filter(sortedMemeberships, (obj) => {
                    const endDate = TimeUtil.getMomentForDateString(TimeUtil.formatEpochInTimeZone(tz, obj.end), tz)
                    return endDate.isBefore(today, "day") && endDate.isAfter(today.subtract(30, "days"), "day")
                }))
            }
        }


        const finalMemberShip = activeMembership || futureMembership || lastExpiredMembership

        if (finalMemberShip) {
            const endDateString = TimeUtil.formatEpochInTimeZone(tz, finalMemberShip.end)
            const startDateString = TimeUtil.formatEpochInTimeZone(tz, finalMemberShip.start)
            const endDate = TimeUtil.getMomentForDateString(endDateString, tz)
            const startDate = TimeUtil.getMomentForDateString(startDateString, tz)

            const daysLeft = endDate.diff(today, "days")
            let expiryMessage: string = undefined
            if (daysLeft <= 7 && activeMembership) { // If there is only upto 7 days left and no future membership
                expiryMessage = "Pack expiring! Renew on-time for discount."
            }

            const total = endDate.diff(startDate, "days")
            const current = today.diff(startDate, "days")
            return {
                packId: String(this.convertProductIdToCultPackId(finalMemberShip.productId)),
                total: endDate.diff(startDate, "days"),
                current: Math.min(current, total), // If Expired, set current to total.
                expiryMessage: expiryMessage,
                startDate: startDateString,
                endDate: endDateString,
                lastPackEndDate: TimeUtil.formatEpochInTimeZone(tz, sortedMemeberships[sortedMemeberships.length - 1].end),
                daysLeft: daysLeft,
                isFutureMembershipPresent: sortedMemeberships.length > 1 ? true : false,
                currentMembership: finalMemberShip,
                state: finalMemberShip.status,
                clientMembershipState: activeMembership ? "ACTIVE" : futureMembership ? "FUTURE" : lastExpiredMembership ? "EXPIRED" : "UNKNOWN"
            }

        } else {
            return undefined
        }

    }

    static getLivePTMembershipState(membership: ActiveBundleOrderDetail, tz: Timezone) {
        const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDateWithTimezone(tz), tz)
        const endDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.endDate)), tz)
        const startDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.startDate)), tz)
        const numDaysToEndFromToday = endDate.diff(today, "days")
        if (startDate > today) {
            return "UPCOMING"
        }
        if (endDate < today) {
            return "EXPIRED"
        }
        if (numDaysToEndFromToday < 10) {
            return "EXPIRING"
        }
        return "ACTIVE"
    }

    static getMembershipState(membership: CultMembership, tz: Timezone, membershipDetails?: MembershipDetails): CultMembershipState {
        const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDateWithTimezone(tz), tz)
        const endDate = TimeUtil.getMomentForDateString(membership.endDate, tz)
        const startDate = TimeUtil.getMomentForDateString(membership.startDate, tz)
        const numDaysToEndFromToday = endDate.diff(today, "days")

        switch (membership.state) {
            case "PURCHASED": {
                if (startDate > today) {
                    return "UPCOMING"
                }
                if (endDate < today) {
                    return "EXPIRED"
                }
                if (numDaysToEndFromToday < 10 && (membershipDetails ? !membershipDetails.pauseDetails : true)) {
                    return "EXPIRING"
                }
                return "ACTIVE"
            }
            case "PAUSED": {
                return "PAUSED"
            }
            case "ADVANCE_PAID": {
                if (startDate > today) {
                    return "UPCOMING"
                }
                if (endDate < today) {
                    return "EXPIRED"
                }
                if (numDaysToEndFromToday < 10 && (membershipDetails ? !membershipDetails.pauseDetails : true)) {
                    return "EXPIRING"
                }
                return "ADVANCE_PAID"
            }
            case "MANUAL_CANCELLED": {
                return "MANUAL_CANCELLED"
            }
            default:
                return membership.state
        }

    }

    public static getMembershipStateV2(membership: Membership, tz: Timezone): String {
        const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDate(tz), tz)
        const endDate = TimeUtil.getMomentForDateString(TimeUtil.formatEpochInTimeZone(tz, membership.end), tz)
        const startDate = TimeUtil.getMomentForDateString(TimeUtil.formatEpochInTimeZone(tz, membership.start), tz)
        const numDaysToEndFromToday = endDate.diff(today, "days")
        switch (membership.status) {
            case "PURCHASED": {
                if (startDate > today) {
                    return "UPCOMING"
                }
                if (endDate < today) {
                    return "EXPIRED"
                }
                if (numDaysToEndFromToday < 10) {
                    return "EXPIRING"
                }
                return "ACTIVE"
            }
            case "PAUSED": {
                return "PAUSED"
            }
            case "CANCELLED": {
                return "MANUAL_CANCELLED"
            }
            default:
                return membership.status
        }
    }

    public static getCultPackTagAndColor(membershipState: CultMembershipState | String, numDaysToEndFromToday: number) {
        switch (membershipState) {
            case "ADVANCE_PAID":
            case "ACTIVE":
                return {
                    tag: "ACTIVE",
                    color: "#5bdbb6"
                }
            case "PAUSED":
                return {
                    tag: "PAUSED",
                    color: "#f5a623"
                }
            case "UPCOMING":
                return {
                    tag: "UPCOMING",
                    color: "#6236ff"
                }
            case "EXPIRING":
            case "EXPIRING_SOON":
                return {
                    tag: "EXPIRING SOON",
                    color: "#ffa300"
                }
            case "FINISHING_SOON":
                return {
                    tag: "FINISHING SOON",
                    color: "#ffa300"
                }
            case "MANUAL_CANCELLED":
            case "EXPIRED":
                return {
                    tag: "EXPIRED",
                    color: "#b00020"
                }
            case "COMPLETED":
                return {
                    tag: "COMPLETED",
                    color: "#888e9e"
                }
        }
    }

    public static getProgressColor(progressVal: number, isHex: boolean) {
        if (progressVal < 0.5) {
            return isHex ? "#5bdbb6" : "statusGreen"
        } else if (progressVal < 0.75) {
            return isHex ? "#ffa300" : "statusYellow"
        } else {
            return isHex ? "#b00020" : "statusRed"
        }
    }

    public static getStartDatePageUrl(membershipId: string | number, productType: string): string {
        return `curefit://${PageTypes.ChangePackStartDate}?membershipId=${membershipId}&productType=${productType}`
    }

    static getChangeStartDateAction(membership: CultMembership, productType: ProductType | MembershipProductType): Action {
        return {
            iconUrl: "/image/icons/cult/calendar.png",
            title: "CHANGE\nSTART DATE",
            actionType: "NAVIGATION",
            url: this.getStartDatePageUrl(membership.id, productType)
        }
    }

    static getChangeStartDateActionV2(membership: Membership, productType: ProductType | MembershipProductType): Action {
        const membershipServiceId = membership.id
        if (membershipServiceId) {
            return {
                iconUrl: "/image/icons/cult/calendar.png",
                title: "CHANGE\nSTART DATE",
                actionType: "NAVIGATION",
                url: this.getStartDatePageUrl(membershipServiceId, productType)
            }
        }
    }

    public static isPackAvailableForBuy(cultPack: CultPack, centerId: number) {
        const preAppliedOffer = cultPack.preAppliedOfferObject
        if (preAppliedOffer) {
            const preAppliedOfferForCenter = preAppliedOffer.centerWiseSlots.find(centerWiseSlot => {
                return centerWiseSlot.centerId === centerId.toString()
            })
            if (preAppliedOfferForCenter && preAppliedOfferForCenter.slots <= 0) {
                return false
            } else {
                return true
            }
        }
        return true
    }

    public static getOfferDetails(packProduct: Product, packOffersResponse: PackOffersResponse, packOffersV3?: CultProductPricesResponse): {
        price: ProductPrice
        offers: OfferMini[]
    } {
        let price = packProduct.price
        let offers
        if (packOffersV3?.priceMap) {
            const productOffer = packOffersV3.priceMap[packProduct.productId]
            if (productOffer) {
                price = {
                    mrp: productOffer.product.price.mrp,
                    listingPrice: productOffer.product.price.sellingPrice,
                    currency: "INR"
                }
                offers = productOffer.offerIds.map(id => packOffersV3.offerMap[id])
            }
        } else if (packOffersResponse) {
            return OfferUtil.getPackOfferAndPrice(packProduct, packOffersResponse)
        }
        return { price, offers }
    }

    public static getOfferDetailsPMS(pack: OfflineFitnessPack, packOffersV3?: CultProductPricesResponse): {
        price: ProductPrice
        offers: OfferMini[]
    } {
        let price = pack.price
        let offers
        if (packOffersV3?.priceMap) {
            const productOffer = packOffersV3.priceMap[pack.id]
            if (productOffer) {
                price = {
                    mrp: productOffer.product.price.mrp,
                    listingPrice: productOffer.product.price.sellingPrice,
                    currency: "INR"
                }
                offers = productOffer.offerIds.map(id => packOffersV3.offerMap[id])
            }
        }
        return { price, offers }
    }

    public static getOffersMap(cultPack: CultPack, packOffersResponse: PackOffersResponse, packOffersV3?: CultProductPricesResponse): Map<string, Array<OfferV2>> {
        let offers
        if (packOffersV3?.priceMap) {
            const productOffer = packOffersV3.priceMap[cultPack.productId]
            if (productOffer) {
                offers = productOffer.offerIds.map(id => packOffersV3.offerMap[id])
            }
        } else if (packOffersResponse) {
            offers = packOffersResponse[cultPack.productId].offers
        }
        return OfferUtil.segregateNoCostEMIOffers((offers as OfferV2[]))
    }

    public static getOfferItem(packOffersV3: CultProductPricesResponse, productId: string): PackOfferItem {
        const productOffer = packOffersV3.priceMap[productId]
        if (productOffer) {
            return {
                product: {
                    price: {
                        mrp: productOffer.product.price.mrp,
                        listingPrice: productOffer.product.price.sellingPrice,
                        currency: "INR"
                    }
                },
                offers: productOffer.offerIds.map(id => packOffersV3.offerMap[id]) as OfferV2[],
                preBuzzOffers: []
            }
        }
    }

    public static getEmiTenureForPack(packDurationInDays: number, maxEmiTenure?: number): number {
        const packDurationInMonths = Math.floor(packDurationInDays / 30)
        if (packDurationInMonths >= 0 && packDurationInMonths <= 3) return 3
        else if (packDurationInMonths >= 4 && packDurationInMonths <= 6) return 6
        else if (packDurationInMonths >= 7 && packDurationInMonths <= 9) return 9
        else if (packDurationInMonths >= 10 && packDurationInMonths <= 12) return 12
        return maxEmiTenure || 12
    }

    public static getPackPriceAndOfferIdV2(fitnessPack: OfflineFitnessPack, packOffersV3?: CultProductPricesResponse): { price: ProductPrice, offerIds?: string[] } {
        if (IS_NEW_CULT_OFFER_VERSION) {
            const offerDetails = this.getOfferDetailsPMS(fitnessPack, packOffersV3)
            const offerIds = _.map(offerDetails.offers, offer => { return offer.offerId })
            return {
                price: offerDetails.price,
                offerIds: offerIds
            }
        }
    }

    public static async getCultPackWidgetId(userContext: UserContext, segmentService: ISegmentService) {
        const doesUserBelongToNewPackWidgetSegment = await segmentService.doesUserBelongToSegment("d5f76a3b-9654-41cc-98fc-b105075d2734", userContext)
        const newPackWidgetId = "7bc20ee5-38b6-4a33-bc0a-121fb1a11d75_v3"
        const oldPackWidgetId = "34b241ba-e841-4adf-87b0-79c584ff91d1"
        const prodPackWidgetId = doesUserBelongToNewPackWidgetSegment ? newPackWidgetId : oldPackWidgetId
        return process.env.APP_ENV === "STAGE" ? "c913e627-6804-4f03-a395-419574b5fe3e" : prodPackWidgetId
    }

    public static getOldCultPackWidgetId() {
        return process.env.APP_ENV === "STAGE" ? "c913e627-6804-4f03-a395-419574b5fe3e" : "34b241ba-e841-4adf-87b0-79c584ff91d1"
    }

    static getCultJuniorEmergencyRelations() {
        return [
            "Mother",
            "Father",
            "Guardian",
        ]
    }

    static getSubscriptionTypeInfo(renewalCycleUnit: RenewalCycleUnit): { subscriptionType: SubscriptionType, renewalCycleText: string } {
        switch (renewalCycleUnit) {
            case "DAY":
                return { subscriptionType: "DAILY", renewalCycleText: "day" }
            case "WEEK":
                return { subscriptionType: "WEEKLY", renewalCycleText: "week" }
            case "MONTH":
                return { subscriptionType: "MONTHLY", renewalCycleText: "month" }
        }
    }

    static isMembershipActive(userContext: UserContext, membership: CultMembership): boolean {
        const tz = userContext.userProfile.timezone
        const today = TimeUtil.todaysDateWithTimezone(tz)
        if (membership.state === "PAUSED")
            return true
        else if (membership.startDate <= today && membership.endDate >= today) {
            return true
        }
        return false
    }

    static isSubscriptionPackSupported(subscriptionPack: FitnessSubscriptionPackDetails, userContext: UserContext) {
        if (_.isEmpty(subscriptionPack)) {
            return true
        }
        const { sessionInfo } = userContext
        if (sessionInfo.userAgent === "DESKTOP" || sessionInfo.userAgent === "MBROWSER") {
            return false
        }
        const appVersion = sessionInfo.appVersion
        if (sessionInfo.osName === "android") {
            if (appVersion > CULT_SUBSCRIPTION_VERSION_ANDROID) {
                return true
            }
        }
        else if (sessionInfo.osName === "ios") {
            if (appVersion > CULT_SUBSCRIPTION_VERSION_IOS) {
                return true
            }
        }
        return false
    }

    static getCenterNameWithoutTenant(centerName: string, tenantId: number): string {
        if (tenantId === 1) {
            return centerName.replace("Cult ", "")
        }
        else if (tenantId === 2) {
            return centerName.replace("Mind.fit ", "")
        }
    }

    static getMinimumStartDateForCenter(cultCenter: CultCenter, startDate: string) {
        return _.max([cultCenter.launchDate, cultCenter.minMembershipStartDate, startDate])
    }

    static getPackStartDateOptions(userContext: UserContext, preferredCenter: CultCenter, canChangeStartDate: boolean, selectedDate: string, startDateWindow: number, minimumDate?: string, maximumDate?: string) {
        const tz = userContext.userProfile.timezone
        if (_.isNil(minimumDate)) {
            minimumDate = TimeUtil.todaysDateWithTimezone(tz)
        }
        if (preferredCenter) {
            minimumDate = this.getMinimumStartDateForCenter(preferredCenter, minimumDate)
        }
        if (!maximumDate) {
            // any large number for end date
            maximumDate = TimeUtil.addDays(tz, minimumDate, startDateWindow)
        }
        if (canChangeStartDate && (selectedDate < minimumDate || selectedDate > maximumDate)) {
            selectedDate = undefined
        }
        if (!canChangeStartDate && !selectedDate) {
            selectedDate = minimumDate
        }

        return { minimumDate, selectedDate, maximumDate }
    }

    static isClassAvailableForWaitlist(cultClass: CultClass): boolean {
        return cultClass.cultAppAvailableSeats <= 0 && cultClass.isWaitlistAvailable
    }

    static getwaitlistCnfProbability(cultClass: CultClass): WaitlistProbability {
        let wlStatus
        if (cultClass?.waitlistedUserCount < cultClass?.wlConfirmationThreshold) {
            wlStatus = WaitlistProbability.MEDIUM
        } else {
            wlStatus = WaitlistProbability.LOW
        }
        return wlStatus
    }
    static getwaitlistCnfProbabilityColor(cultClass: CultClass, getwaitlistCnfProbabilityColor: boolean): string {
        let wlStatusColor
        if (cultClass?.waitlistedUserCount < cultClass?.wlConfirmationThreshold) {
            if (!getwaitlistCnfProbabilityColor) wlStatusColor = "#FFB876"
            else wlStatusColor = "#FFFFFF"
        } else {
            if (!getwaitlistCnfProbabilityColor) wlStatusColor = "#FF6B74"
            else wlStatusColor = "#FFB876"
        }
        return wlStatusColor
    }

    static isClassAvailableForPulse(cultClass: CultClass, isPulseSupported: boolean): boolean {
        if (cultClass.PulseContext) {
            /**
             * We've to check for dead mode now
             * A Pulse class can be disabled later
             */
            return cultClass.isPulseEnabled && cultClass.PulseContext.isActive && isPulseSupported
        }
        if (cultClass.isPulseEnabled) {
            return isPulseSupported
        }
        return false
    }

    static isUserPPC(cultClass: CultClass | CultClassDetails): boolean {
        return cultClass.amount > 0
    }

    static isUserTrial(cultClass: CultClass | CultClassDetails): boolean {
        return cultClass.amount === 0 && cultClass.classType === "FREE"
    }

    static isUserMember(cultClass: CultClass | CultClassDetails): boolean {
        return cultClass.amount === 0 && cultClass.classType !== "MEMBERSHIP"
    }

    static packUpgradeAble(membership: CultMembership, userContext: UserContext): boolean {
        return membership.isMembershipUpgradeable
    }

    public static membershipAuditTrailAvailable(membership: CultMembership): boolean {
        return membership.isMembershipAuditAvailable
    }

    static getPulseClassReachEarlyCutoff() {
        return PULSE_CLASS_REACH_EARLY_CUT_OFF_IN_MINUTES
    }
    static pulsifyDeviceName(cultBooking: CultBooking | CultClass, expanded?: boolean): string {
        if (!_.isEmpty(cultBooking.pulseDeviceName)) {
            if (expanded) {
                return `Kit Number: ${cultBooking.pulseDeviceName}`
            }
            return `Kit #${cultBooking.pulseDeviceName}`
        }
        return cultBooking.pulseDeviceName
    }
    static isUnlimitedPulseAccessPack(pulsePack: PulseProduct): boolean {
        return pulsePack && pulsePack.isUnlimitedPack
    }
    static pauseCultPackAction(userContext: UserContext, productType: ProductType, membership: CultMembership, pageConfig: CultPackPageConfig, packInfo: OfflineFitnessPack): Action {
        const isPauseAllowed = membership.remainingPauseDays > 0
        if (!isPauseAllowed) {
            return
        }
        const tz = userContext.userProfile.timezone
        const pauseEndDate = membership.ActivePause ? membership.ActivePause.maxEndDate ? membership.ActivePause.maxEndDate : undefined : undefined
        return {
            actionType: "PAUSE_CULT_MEMEBERSHIP",
            shouldRefreshPage: true,
            meta: {
                membershipId: membership.id,
                productType: packInfo.productType,
                pauseMaxDays: membership.pauseMaxDays,
                remainingPauseDays: membership.remainingPauseDays,
                pauseEndDate: pauseEndDate,
                startDateParams: {
                    date: TimeUtil.todaysDate(tz),
                    limit: membership.endDate,
                    canEdit: false
                },
                pauseReason: {
                    options: [
                        { optionText: "I am travelling out of town", optionId: "TRAVEL_OUT" },
                        { optionText: "I am injured", optionId: "INJURED" },
                        { optionText: "I am unwell", optionId: "UNWELL" },
                        { optionText: "Work is keeping me busy", optionId: "WORK_BUSY" },
                        { optionText: "I have personal commitments", optionId: "PERSONAL_COMITMENTS" },
                        { optionText: "Shutdown due to COVID-19", optionId: "COVID19" }
                    ],
                    selectionOptionId: "TRAVEL_OUT",
                    allowOthers: true
                },
                description: "You can pause your pack as many times as you like until you reach this limit."
            }
        }
    }

    static getMonthsFromDays(days: number) {
        return Math.round(days / 30)
    }

    static getCultTrainerDescription(description: string[]) {
        let updatedDescription = ""
        if (Array.isArray(description)) {
            description.forEach((desc, index) => {
                if (index === 0) {
                    updatedDescription += `[\"${desc}\",`
                } else if (index === description.length - 1) {
                    updatedDescription += `\"${desc}\"]`
                } else {
                    updatedDescription += `\"${desc}\",`
                }
            })
        }
        return updatedDescription
    }

    public static getMiniGoalIntroCustomBottomSheetAction(action?: Action, userContext?: UserContext): Action {
        const meta: any  = {}
        const widgets: any = []
        const osName = userContext.sessionInfo.osName.toLowerCase()
        const mediaDataWidget: CFMediaDataWidget = new CFMediaDataWidget("/image/mem-exp/miniGoal/miniGoalInfoShortv4.png", "image", 600, null, 10, 15, 0, 0)
        const cfMediaWidget = new CFMediaWidget(mediaDataWidget)
        widgets.push(cfMediaWidget)
        let actionList: any = []
        if (action) {
            action.title = "GOT IT"
            actionList = new CFBottomSheetActionWidget([action], {"top": 0, "bottom": 10})
            widgets.push(actionList)
        }
        meta.showTopNotch = true
        meta.blurEnabled = true
        meta.backgroundColor = 1
        meta.height = osName === "android" ? 0.9 : 0.85
        meta.pageId = "mini_goal_modal"
        meta.widgets = widgets
        return {
            actionType: "SHOW_CUSTOM_BOTTOM_SHEET",
            title: "GOT IT",
            meta,
        }
    }

    public static getEmployeeOnlyCustomBottomSheetAction(centerId: Number, action: Action): Action {
        if (centerId === 1142) {
            action.title = "Confirm"
            const meta: any  = {}
            const widgets: any = []
            const mediaDataWidget: CFMediaDataWidget = new CFMediaDataWidget("image/mem-exp/afm_slot_booking.png", "image", 450, 450, 0, 15)
            const cfMediaWidget = new CFMediaWidget(mediaDataWidget)
            widgets.push(cfMediaWidget)
            const actionList = new CFBottomSheetActionWidget([action], {"top": 0, "bottom": 20})
            widgets.push(actionList)
            meta.showTopNotch = true
            meta.blurEnabled = true
            meta.widgets = [...CultUtil.getEmployeeOnlyDisclaimerWidgets("Classes at this centre are for employees working in Nirlon Knowledge Park. You will need to show your employee ID card to access this centre."), actionList]
            meta.height = 0.3
            meta.pageId = "employee_only_modal"
            return {
              actionType: "SHOW_CUSTOM_BOTTOM_SHEET",
              title: "Confirm",
              meta,
            }
        } else {
            return action
        }
    }

    static getEmployeeOnlyModalMetaForCenter(centerId: string): { title: string, message: string } {
        let alertMeta: { title: string, message: string }
        if (centerId === "75") {
            alertMeta = {
                title: "Are you sure?",
                message: "Classes at this centre are for microsoft employees ONLY.\n\nYou will need to show your employee ID card to access this centre."
            }
        }
        if (centerId === "76" || centerId === "77") {
            alertMeta = {
                title: "Are you sure?",
                message: "Classes at this centre are for employees working in Embassy Tech Village.\n\nYou will need to show your employee ID card to access this centre."
            }
        }
        if (centerId === "179") {
            alertMeta = {
                title: "Are you sure?",
                message: "Classes at this centre are for employees working in Nirlon Knowledge Park.\n\nYou will need to show your employee ID card to access this centre."
            }
        }
        if (centerId === "229") {
            alertMeta = {
                title: "Are you sure?",
                message: "Classes at this centre are for Infosys employees only.\n\nYou will need to show your employee ID card to access this centre."
            }
        }
        return alertMeta
    }

    static isCustomerBiometricSupportedInCenter(center: CultCenter) {
        return false
        // if (_.isEmpty(center)) {
        //     return false
        // }
        // // assume supported if metadata is null
        // if (_.isEmpty(center.metadata) || _.isEmpty(center.metadata.attendance) || _.isEmpty(center.metadata.attendance.customer)) {
        //     return true
        // }
        // return center.metadata.attendance.customer.includes("BIOMETRIC")
    }

   static getInstructionModalAction(action: Action, widget: WidgetView, continueActionTitle?: string): Action {
       const actionType = "SHOW_CULT_INSTRUCTION_MODAL"
       const title = action.title
       const variant = action.variant
       action.title = !_.isNil(continueActionTitle) ? continueActionTitle : "CONTINUE"
       const meta = {
            payload: {
                action: action,
                widget: widget
            }
       }
       return {
           actionType: actionType,
           title: title,
           variant: variant,
           meta: meta
       }
   }

    static getInstructionForWorkout(workoutId: number, centerId: number, isUnbound: Boolean): DescriptionWidgetV2 {
        let widget: DescriptionWidgetV2 = null
        let title = "Instruction to be followed"
        let instructions: string[] = []
        switch (workoutId) {
            case BOXING_BAG_WORKOUT_ID :
                instructions = this.getBoxingBagWorkoutInstructions(centerId)
                widget = new DescriptionWidgetV2(title, instructions, null, 50)
                break
            case DUMMY_EVOLVE_YOGA_WORKOUT_ID:
                instructions = CLASS_FILMING_NOTES
                widget = new DescriptionWidgetV2(title, instructions, null, 50)
                break
            case DUMMY_STRENGTH_PLUS_WORKOUT_ID:
                instructions = CLASS_FILMING_NOTES
                widget = new DescriptionWidgetV2(title, instructions, null, 50)
                break
        }
        if (isUnbound) {
            instructions = UNBOUND_TERMS_AND_CONDITIONS
            title = "Terms and Conditions"
            widget = new DescriptionWidgetV2(title, instructions, null, 50)
        }
        return widget
    }

    static getBoxingBagWorkoutInstructions(centerId: number): string[] {
        if (BOXING_GLOVES_PROVIDED_CENTER_IDS.includes(centerId)) {
            return BOXING_GLOVES_PROVIDED_NOTES
        } else {
            return BOXING_GLOVES_NOT_PROVIDED_NOTES
        }
    }

    static getInstructionForClass(workoutId: number, centerId?: string) {
        if (workoutId === 153) {
            return [
                {
                    showBullet: true,
                    text: "As per Reliance Club rules, members are requested to NOT wear shorts in the premises"
                },
                {
                    showBullet: true,
                    text: "Carry your swimming cap, goggles and towels for this session"
                }
            ]
        } else if (workoutId === 154) {
            return [
                {
                    showBullet: true,
                    text: "As per Reliance Club rules, members are requested to NOT wear shorts in the premises"
                },
                {
                    showBullet: true,
                    text: "For Swim Fitness classes, you must be able to swim at least one lap unaided"
                },
                {
                    showBullet: true,
                    text: "Carry your swimming cap, goggles and towels for this session"
                }
            ]
        } else if (workoutId === 308) {
            if (BOXING_GLOVES_PROVIDED_CENTER_IDS.includes(Number(centerId))) {
                return [
                    {
                        showBullet: false,
                        text: "Boxing gloves and handwraps will be provided at the center for use. You are not required to bring your own equipments."
                    }
                ]
            }
            return [
                {
                    showBullet: true,
                    text: "Boxing bags will be used in this class. To ensure safety for COVID-19, we request you to" +
                        " carry your own Boxing gloves and hand wraps"
                },
                {
                    showBullet: true,
                    text: "You can also pre order boxing gloves (handwraps included) from the app or purchase at the center"
                },
                {
                    showBullet: true,
                    text: "Please note that entry will not be allowed without gloves and hand wraps as this is a" +
                        " boxing bag workout"
                }
            ]
        } else if (workoutId === 43) {
            return [
                {
                    showBullet: true,
                    text: "The outdoor run class will happen outside the center."
                },
                {
                    showBullet: true,
                    text: "Please note that participation in class without running shoes will not be allowed."
                },
                {
                    showBullet: true,
                    text: "We request you to carry your own water bottles for the class or can opt to purchase them at the center."
                }
            ]
        }

        return []
    }

    static isCompletedClassCountZero(cultAndMindSummary: CultSummary) {
        return ((cultAndMindSummary.bookingSummary && cultAndMindSummary.bookingSummary.cult.completedCount === 0) || !cultAndMindSummary.bookingSummary)
    }

    static isCompletedClassCountLessThenThree(cultAndMindSummary: CultSummary) {
        return ((cultAndMindSummary.bookingSummary && cultAndMindSummary.bookingSummary.cult.completedCount < 3) || !cultAndMindSummary.bookingSummary)
    }

    static getCompletedClassCount(cultAndMindSummary: CultSummary) {
        return cultAndMindSummary.bookingSummary ? cultAndMindSummary.bookingSummary.cult.completedCount : 0
    }

    static getPackPauseResumeAction(pausePackData: any, membership: CultMembership): Action | null {
        if (pausePackData) {
            let meta = pausePackData.meta
            if (membership.state === "PAUSED") {
                meta = {
                    ...meta,
                    dateParam: {
                        ...meta.startDateParams,
                        pauseEndDate: meta.pauseEndDate
                    }
                }
            }
            return {
                iconUrl: membership.state === "PAUSED" ? "/image/icons/cult/resume.png" : "/image/icons/cult/pause.png",
                title: membership.state === "PAUSED" ? "MODIFY PAUSE" : "PAUSE",
                actionType: pausePackData.manageOptions.options[0].type,
                meta: meta
            }
        }
        return null
    }
    static getAlertWhileUpgradingDuringPause(membership: Membership, title: string, cancelPauseAction?: Action, packPauseResumeAction?: Action): Action | null {
        if (membership.status === "PAUSED" && packPauseResumeAction) {
            return {
                actionType: "SHOW_ALERT_MODAL",
                title,
                iconUrl: "/image/icons/cult/upgrade.png",
                isDisabled: true,
                meta: {
                    title: "Pack Paused",
                    subTitle: "Your membership is on pause. Please resume to upgrade the membership.",
                    actions: [{
                        "title": "Ok",
                        "actionType": "HIDE_ALERT_MODAL"
                    }]
                }
            }
        }
        if (cancelPauseAction) {
            return {
                actionType: "SHOW_ALERT_MODAL",
                title,
                iconUrl: "/image/icons/cult/upgrade.png",
                isDisabled: true,
                meta: {
                    title: "Pack Paused",
                    subTitle: "Please cancel the pause to upgrade the membership.",
                    actions: [{
                        "title": "Ok",
                        "actionType": "HIDE_ALERT_MODAL"
                    }]
                }
            }
        }
    }

    static async getPackPauseResumeDetails(membership: CultMembership, productType: string, userContext: UserContext, interfaces?: CFServiceInterfaces) {
        const tz: Timezone = userContext.userProfile.timezone
        if (CultUtil.isPauseResumeAllowed(userContext, membership)) {
            const isPauseAllowed = membership.remainingPauseDays > 0
            const isOptionEnabled = membership.state === "PAUSED" || isPauseAllowed
            const doesUserBelongtoSGTDiscoverSegment = await interfaces.segmentService.doesUserBelongToSegment("671", userContext)
            const isEditPauseDateSupported = AppUtil.isPauseEditDateSupported(userContext, (await userContext.userPromise).isInternalUser)
            const infoText = membership.state === "PAUSED" ? "You can also manually resume your pack before the specified date.\nOnce you resume, your pack will be extended by the number of days you were on pause"
                : "You can pause your pack when you can't use it like during travel or illness.\n\nOnce you resume, your pack is extended by the number of days you were on pause"
            const manageOptions: ManageOptions = {
                displayText: membership.state === "PAUSED" ? (isEditPauseDateSupported ? "Modify Pause" : "Resume pack") : "Pause pack",
                icon: membership.state === "PAUSED" ? "RESUME" : "PAUSE",
                options: [{
                    isEnabled: isOptionEnabled,
                    type: membership.state === "PAUSED" ? "RESUME_CULT_MEMEBERSHIP" : "PAUSE_CULT_MEMEBERSHIP",
                    displayText: membership.state === "PAUSED" ? (isEditPauseDateSupported ? "Modify Pause" : "Resume pack") : "Pause pack"
                }],
                info: {
                    title: "About Pause",
                    subTitle: infoText
                }
            }

            let pauseEndDate, pauseStartDate
            const limit = membership.endDate
            const pauseDaysUsedInThisCycle = membership.pauseDaysUsedInThisCycle
            if (membership.state === "PAUSED") {
                const pauseDate = membership.ActivePause ? membership.ActivePause.maxEndDate ? membership.ActivePause.maxEndDate : undefined : undefined
                pauseEndDate = pauseDate
                manageOptions.subTitle = pauseDate ? `Paused till ${TimeUtil.formatDateInTimeZone(tz, pauseDate, "DD MMM")
                    }` : "Your pack is paused"
                pauseStartDate = membership.ActivePause ? membership.ActivePause.startTime : undefined
            } else {
                if (isPauseAllowed) {
                    manageOptions.subTitle = `${membership.remainingPauseDays} pause days remaining`
                } else {
                    manageOptions.subTitle = "You have used all your pauses."
                }
            }

            const meta: any = {
                membershipId: membership.id,
                productType: productType,
                title: isEditPauseDateSupported ? "Resume/Edit Pause" : "Resume Pack",
                pauseMaxDays: membership.pauseMaxDays,
                remainingPauseDays: membership.remainingPauseDays,
                remainingDaysInCurrentDuration: pauseDaysUsedInThisCycle,
                pauseEndDate: pauseEndDate ? TimeUtil.formatDateInTimeZone(tz, pauseEndDate) : undefined,
                startDateParams: {
                    date: TimeUtil.formatDateInTimeZone(tz, pauseStartDate, "YYYY-MM-DD hh:mm A"),
                    limit,
                    canEdit: false,
                    pauseEndText: "You had paused your pack till"
                },
                action: {
                    primaryText: "RESUME",
                    secondaryText: isEditPauseDateSupported ? "EDIT" : "CANCEL"
                },
                editPauseAction: {
                    meta: { isEdit: true, membershipId: membership.id, productType },
                    actionType: "PAUSE_CULT_MEMEBERSHIP"
                },
                pauseInfoTitles: {
                    pauseUsed: "Pause days used",
                    membershipExtended: "Membership will be extended by",
                    membershipEndsOn: "Membership will now end on",
                    pauseLeft: "Pause days left"
                },
                pauseInfo: CultUtil.getPauseInfo(tz, membership.pauseDaysUsedTillDate, pauseDaysUsedInThisCycle, limit, (membership.remainingPauseDays - pauseDaysUsedInThisCycle), true),
                pauseReason: {
                    options: CultUtil.getPauseReasons(userContext, doesUserBelongtoSGTDiscoverSegment),
                    selectionOptionId: "TRAVEL_OUT",
                    allowOthers: true
                },
                description: "You can pause your pack as many times as you like until you reach this limit.",
                multiplePageActions: true,
            }
            return {
                manageOptions,
                meta,
                isDisabled: !isOptionEnabled,
                isPauseAllowed
            }
        }
    }

    static async getPackPauseResumeDetailsV2(membership: Membership, productType: string, userContext: UserContext, serviceInterfaces: CFServiceInterfaces) {
        const tz: Timezone = userContext.userProfile.timezone
        const isNewPauseEnabled = await AppUtil.doesUserBelongsToNewPauseExperiment(userContext, serviceInterfaces)
        if (GymfitUtil.isPauseResumeAllowed(userContext, membership)) {
            const cultMembershipId = membership.metadata["membershipId"]
            const isPauseAllowed = this.convertMillisToDays(membership.remainingPauseDuration) > 0
            const isOptionEnabled = membership.status === "PAUSED" || isPauseAllowed
            const infoText = membership.status === "PAUSED" ? "You can also manually resume your pack before the specified date.\nOnce you resume, your pack will be extended by the number of days you were on pause"
                : "You can pause your pack when you can't use it like during travel or illness.\n\nOnce you resume, your pack is extended by the number of days you were on pause"
            const manageOptions: ManageOptions = {
                displayText: membership.status === "PAUSED" ? "Modify Pause" : "Pause pack",
                icon: membership.status === "PAUSED" ? "RESUME" : "PAUSE",
                options: [{
                    isEnabled: isOptionEnabled,
                    type: membership.status === "PAUSED" ? "RESUME_CULT_MEMEBERSHIP" : isNewPauseEnabled ? "NAVIGATION" : "PAUSE_CULT_MEMEBERSHIP",
                    displayText: membership.status === "PAUSED" ? "Modify Pause" : "Pause pack"
                }],
                info: {
                    title: "About Pause",
                    subTitle: infoText
                }
            }

            let pauseEndDate, pauseStartDate
            let limit = TimeUtil.formatEpochInTimeZone(tz, membership.end)
            const today = TimeUtil.todaysDate(tz, "yyyy-mm-dd")
            let pauseDaysUsedInThisCycle = 0
            if (membership.status === "PAUSED") {
                const pauseDate = membership.activePause ? membership.activePause.end ? TimeUtil.parseDateFromEpochWithTimezone(tz, membership.activePause.end) : undefined : undefined
                pauseEndDate = pauseDate
                manageOptions.subTitle = pauseDate ? `Paused till ${moment(pauseEndDate).subtract(1, "day").format("DD MMM")}` : "Your pack is paused"
                pauseStartDate = membership.activePause ? TimeUtil.parseDateFromEpochWithTimezone(tz, membership.activePause.start) : undefined
                const startDate = membership.activePause.start ? TimeUtil.formatEpochInTimeZone(tz, membership.activePause.start) : undefined
                pauseDaysUsedInThisCycle = membership.activePause ? membership.activePause.start && membership.activePause.end ?
                    this.convertMillisToDays(membership.activePause.end - membership.activePause.start)
                    : 0 : 0
                limit = TimeUtil.formatEpochInTimeZone(tz, membership.end)
            } else {
                if (isPauseAllowed) {
                    manageOptions.subTitle = `${this.convertMillisToDays(membership.remainingPauseDuration)} pause days remaining`
                } else {
                    manageOptions.subTitle = "You have used all your pauses."
                }
            }
            const packId = this.convertProductIdToCultPackId(membership.productId)
            const isSelectMembership = MembershipItemUtil.isSelectMembership(membership)

            const pauseDaysUsedTillDate = this.convertMillisToDays(membership.maxPauseDuration - membership.remainingPauseDuration)
            const meta: any = {
                membershipId: membership.id,
                packId: this.convertProductIdToCultPackId(membership.productId),
                productType: productType,
                title: "Resume/Edit Pause",
                pauseMaxDays: this.convertMillisToDays(membership.maxPauseDuration),
                remainingPauseDays: this.convertMillisToDays(membership.remainingPauseDuration),
                remainingDaysInCurrentDuration: pauseDaysUsedInThisCycle,
                pauseEndDate: pauseEndDate ? moment(pauseEndDate).subtract(1, "day").format("YYYY-MM-DD") : undefined,
                startDateParams: {
                    date: TimeUtil.formatDateInTimeZone(tz, pauseStartDate, "YYYY-MM-DD hh:mm A"),
                    limit,
                    canEdit: false,
                    pauseEndText: "You had paused your pack till"
                },
                action: {
                    primaryText: "RESUME",
                    secondaryText: "EDIT"
                },
                editPauseAction: {
                    meta: { isEdit: true, membershipId: membership.id, productType },
                    actionType: "PAUSE_CULT_MEMEBERSHIP"
                },
                pauseInfoTitles: {
                    pauseUsed: "Pause days used",
                    membershipExtended: "Membership will be extended by",
                    membershipEndsOn: "Membership will now end on",
                    pauseLeft: "Pause days left"
                },
                pauseInfo: GymfitUtil.getPauseInfo(tz, pauseDaysUsedTillDate, pauseDaysUsedInThisCycle, limit, this.convertMillisToDays(membership.remainingPauseDuration) - pauseDaysUsedInThisCycle, true),
                pauseReason: {
                    options: GymfitUtil.getPauseReasons(userContext),
                    selectionOptionId: "TRAVEL_OUT",
                    allowOthers: true,
                    pauseReasonTitle: "Let us know, why are you pausing?",
                },
                description: "You can pause your pack as many times as you like until you reach this limit.",
                multiplePageActions: true,
                showEmotionMeter: false,
                onCompletionAction: {
                    actionType: "POP_AND_TRIGGER_ACTION",
                    meta: {
                      nextAction: {
                        actionType: "NAVIGATION",
                        url: await CatalogueServiceUtilities.getCultMembershipDetailsPageAction(membership.id.toString(), userContext),
                      },
                    },
                  },
                additionalInfo: "All existing booking will get cancelled",
                performanceTitle: "On break for",
                performanceSubtitle: [
                    "Rest well, come back stronger",
                    "Rest well, come back stronger",
                    "Most athletes experience a 5% decrease in cardio-vascular fitness and strength after more than 2 weeks of inactivity",
                    "Most athletes experience a 10% decrease in cardio-vascular fitness and strength after more than 3 weeks of inactivity",
                    "Most athletes experience a 20% decrease in cardio-vascular fitness and strength after more than 4 weeks of inactivity",
                ],
                startDateTitle: "Select start date for your pause",
                endDateTitle: "Select end date for your pause",
                bannerUrl: "image/pause/pause_banner_2.png",
                viaDeepLink: true,
                pauseConfirmationData: {
                    confirmationLottieUrl: "/image/mem-exp/lottie/Confirmation.json",
                    daysLeftText: "PAUSE DAYS LEFT",
                    pauseDaysText: "Membership paused for",
                    endDateText: "Pack now ends on ",
                    suggestionTitle: "Pick a way to stay active",
                    homeWorkoutText: "Maintain your weekly streak with at home workout",
                    homeWorkoutImageUrl: "/image/pause/yoga_live.png",
                    suggestionList: [
                        {
                            "iconUrl": "image/pause/Fitness.png",
                            "title": "Running or Cycling",
                            "isSelected": true,
                        },
                        {
                            "iconUrl": "image/pause/Shoes.png",
                            "title": "10K Steps Daily",
                            "isSelected": false,
                        },
                        {
                            "iconUrl": "image/pause/Sauna.png",
                            "title": "Not active",
                            "isSelected": false,
                        },
                    ]
                }
            }
            return {
                manageOptions,
                meta,
                isDisabled: !isOptionEnabled,
                isPauseAllowed
            }
        }
    }

    public static convertProductIdToCultPackId(productId: string) {
        const packId = productId.replace("CULTPACK", "")
        if (!_.isEmpty(packId)) {
            return Number(packId)
        }
        return null
    }


    public static convertMillisToDays(durationInMillis: number): number {
        durationInMillis = durationInMillis + 2000
        return Math.floor(durationInMillis / (24 * 60 * 60 * 1000))
    }

    public static convertMillisToMinutes(durationInMillis: number): number {
        return Math.floor(durationInMillis / (60 * 1000))
    }

    static getPauseReasons(userContext: UserContext, segment: Segment) {
        const appVersion  = userContext.sessionInfo.appVersion
        const options = [
            {
                optionText: "I am travelling out of town",
                optionId: "TRAVEL_OUT",
                actions: CultUtil.getActionsForPause("TRAVEL_OUT", userContext, segment)
            },
            {
                optionText: "I am injured",
                optionId: "INJURED" ,
                actions: CultUtil.getActionsForPause("INJURED", userContext, segment)
            },
            {
                optionText: "I am unwell",
                optionId: "UNWELL",
                actions: CultUtil.getActionsForPause("UNWELL", userContext, segment)
            },
            {
                optionText: "Work is keeping me busy",
                optionId: "WORK_BUSY",
                actions: CultUtil.getActionsForPause("WORK_BUSY", userContext, segment)
            },
            {
                optionText: "I have personal commitments",
                optionId: "PERSONAL_COMITMENTS",
                actions: CultUtil.getActionsForPause("PERSONAL_COMITMENTS", userContext, segment)
            },
            {
                optionText: "Shutdown due to COVID-19",
                optionId: "COVID19",
                actions: CultUtil.getActionsForPause("COVID19", userContext, segment)
            },
        ]
        if (appVersion >= 8.73) {
            options.push({
                optionText: "Others",
                optionId: "OTHERS",
                actions: CultUtil.getActionsForPause("OTHERS", userContext, segment)
            })
        }
        return options
    }

    static getActionsForPause(optionId: string, userContext: UserContext, segment: Segment) {
        if ((optionId === "COVID19" || optionId === "TRAVEL_OUT") && segment) {
            return [
                {
                    title: "Pause Now",
                    actionType: "PAUSE",
                },
                {
                    title: "Discover Online GX",
                    actionType: "NAVIGATION",
                    navigationType: "NAVIGATE_REPLACE",
                    url: "curefit://listpage?pageId=Free_GX",
                },
            ]
        } else {
            return [{
                title: "Pause Now",
                actionType: "PAUSE",
            }]
        }

    }

    static getMembershipUnpauseAction(membership: CultMembership, productType: ProductType | MembershipProductType, userContext: UserContext, isInternalUser: boolean): Action {
        const tz = userContext.userProfile.timezone
        let action: Action
        if (membership.state === "PAUSED") {
            const pauseStartDate = membership.ActivePause ? membership.ActivePause.startTime : undefined
            const pauseEndDate = membership.ActivePause ? membership.ActivePause.maxEndDate ? membership.ActivePause.maxEndDate : undefined : undefined
            const limit = membership.endDate
            let remainingDaysInCurrentDuration
            if (pauseEndDate && pauseStartDate) {
                remainingDaysInCurrentDuration = membership.pauseDaysUsedInThisCycle
            }
            action = {
                title: AppUtil.isPauseEditDateSupported(userContext, isInternalUser) ? "Modify Pause" : "RESUME",
                actionType: "RESUME_CULT_MEMEBERSHIP",
                meta: {
                    refreshPageOnCompletion: true,
                    membershipId: membership.id.toString(),
                    productType: productType,
                    title: AppUtil.isPauseEditDateSupported(userContext, isInternalUser) ? "Modify Pause" : "Resume Pack",
                    pauseMaxDays: membership.pauseMaxDays,
                    remainingPauseDays: membership.remainingPauseDays,
                    remainingDaysInCurrentDuration,
                    action: {
                        primaryText: "RESUME",
                        secondaryText: AppUtil.isPauseEditDateSupported(userContext, isInternalUser) ? "EDIT" : "CANCEL"
                    },
                    pauseInfoTitles: {
                        pauseUsed: "Pause days used",
                        membershipExtended: membership.isSubscription && !_.isEmpty(membership.userSubscription) ? `Rs.${membership.userSubscription.metaData.pauseRefundAmount} per pause day to be reduced from next month's bill. Pause days used` : "Membership will be extended by",
                        membershipEndsOn: membership.isSubscription ? "Membership will automatically resume on" : "Membership will now end on",
                        pauseLeft: "Pause days left"
                    },
                    pauseInfo: this.getPauseInfo(tz, membership.pauseDaysUsedTillDate, remainingDaysInCurrentDuration, limit, (membership.remainingPauseDays - remainingDaysInCurrentDuration), !membership.isSubscription),
                    dateParam: {
                        date: TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, pauseStartDate, "YYYY-MM-DD hh:mm A"),
                        limit,
                        pauseEndDate: pauseEndDate ? TimeUtil.formatDateInTimeZone(tz, pauseEndDate) : undefined,
                        pauseEndText: "You had paused your pack till"
                    },
                    editPauseAction: {
                        meta: { isEdit: true, membershipId: membership.id, productType },
                        actionType: "PAUSE_CULT_MEMEBERSHIP"
                    }
                }
            }
        } else if (CultUtil.isUpcomingPause(membership)) {
            const pauseStartDate = membership.ActivePause ? membership.ActivePause.startTime : undefined
            const pauseEndDate = membership.ActivePause ? membership.ActivePause.maxEndDate ? membership.ActivePause.maxEndDate : undefined : undefined
            const pauseEndDateFormatted = TimeUtil.formatDateInTimeZone(tz, pauseEndDate)
            let pauseEndDateWithTime
            if (pauseEndDate) {
                pauseEndDateWithTime = TimeUtil.getDefaultMomentForDateString(pauseEndDateFormatted, tz).startOf("day").subtract(1, "minute").toDate()
            }
            const startDateText = `${TimeUtil.formatDateInTimeZone(tz, pauseStartDate, "DD MMM hh:mm a")}`
            const remainingDaysInCurrentDuration = membership.pauseDaysUsedInThisCycle < 0 ? 0 : membership.pauseDaysUsedInThisCycle
            const limit = membership.endDate
            action = {
                title: "Cancel Pause",
                actionType: "CANCEL_CULT_PACK_PAUSE",
                meta: {
                    membershipId: membership.id,
                    productType: productType,
                    title: "Cancel Pause",
                    pauseMaxDays: membership.pauseMaxDays,
                    remainingPauseDays: membership.remainingPauseDays,
                    remainingDaysInCurrentDuration,
                    pauseEndDate: pauseEndDate ? TimeUtil.formatDateInTimeZone(tz, pauseEndDate) : undefined,
                    startDateParams: {
                        date: TimeUtil.todaysDate(tz),
                        limit,
                        canEdit: false,
                        pauseEndText: "Your pack is paused till"
                    },
                    action: {
                        primaryText: "YES",
                        secondaryText: "NO"
                    },
                    pauseInfoTitles: {
                        pauseUsed: "Pause days used",
                        membershipExtended: "Membership will be extended by",
                        membershipEndsOn: "Membership will now end on",
                        pauseLeft: "Pause days left"
                    },
                    pauseInfo: CultUtil.getPauseInfo(tz, membership.pauseDaysUsedTillDate, remainingDaysInCurrentDuration, limit, (membership.remainingPauseDays - remainingDaysInCurrentDuration), !membership.isSubscription),
                    dateParam: {
                        date: TimeUtil.formatDateInTimeZone(tz, pauseStartDate, "YYYY-MM-DD hh:mm A"),
                        limit,
                        pauseEndDate: pauseEndDateWithTime ? TimeUtil.formatDateInTimeZone(tz, pauseEndDateWithTime, "DD MMM hh:mm a") : undefined,
                        pauseEndText: `Pause starting ${startDateText} till`
                    },
                    subTitle: `You are cancelling pause starting tonight till ${TimeUtil.formatDateInTimeZone(tz, pauseEndDateWithTime, "DD MMM hh:mm a")}. Your pause days remain intact`,
                    refreshPageOnCompletion: true
                }
            }
        }
        return action
    }

    static getPauseInfo(tz: Timezone, pauseDaysUsedTillDate: number, pausedUsed: number, packEndDate: string, pauseDaysLeft: number, shouldAddExtendsOn: boolean): PauseInfo[] {
        const endsOn = TimeUtil.formatDateInTimeZone(tz, new Date(packEndDate), "DD MMM YYYY")
        const pauseInfo = [
            { title: "Pause Days Used till Date", value: AppUtil.appendDays(pauseDaysUsedTillDate) },
            { title: "Pause Days Left", value: AppUtil.appendDays(pauseDaysLeft) },
        ]
        if (shouldAddExtendsOn) {
            pauseInfo.push({ title: "Membership will now end on", value: endsOn })
        }
        return pauseInfo
    }

    static isDropoutAvailableForBooking(booking: CultBooking, isDropoutSupportedOnClient: boolean) {
        return isDropoutSupportedOnClient && booking.label === "Upcoming" && booking.isDropoutEnabled
    }

    static getFitnessReportFilter(userName: string) {
        return [
            { [ImageFilter.KUDOS_USER]: { text: `KUDOS\n${userName}` } },
            { [ImageFilter.KUDOS_USER_EMOJI]: { text: `KUDOS\n${userName}`, emoji: "😁" } },
            { [ImageFilter.TRAIN_HARD_EMOJI]: { text: `TRAIN\nHARD`, emoji: "💥" } },
            { [ImageFilter.BRAVO_USER_EMOJI]: { text: `BRAVO\n${userName}`, emoji: "🤘🏻" } },
            { [ImageFilter.NO_DAYS_OFF_EMOJI]: { text: `NO DAYS\nOFF`, emoji: "👌🏻" } },
            { [ImageFilter.GIRL_WHO_LIFT_EMOJI]: { text: `GIRLS WHO\nLIFT`, emoji: "🏋🏻‍♀️" } },
            { [ImageFilter.I_FELL_STRONG_EMOJI]: { text: `I FEEL\nSTRONGER`, emoji: "💪🏼" } },
            { [ImageFilter.ZEN_MODE_ON_GIRL_EMOJI]: { text: `ZEN MODE\nON`, emoji: "🧘🏻‍♀️" } },
            { [ImageFilter.ZEN_MODE_ON_BOY_EMOJI]: { text: `ZEN MODE\nON`, emoji: "🧘🏻‍♂️" } },
            { [ImageFilter.NO_EXCUSES_EMOJI]: { text: `NO EXCUSES`, emoji: "😎" } },
            { [ImageFilter.GAINS_ON_MY_MIND_EMOJI]: { text: `GAINS ON\nMY MIND`, emoji: "😎" } },
            { [ImageFilter.I_DIED_ALMOST_EMOJI]: { text: `I DIED,\nALMOST`, emoji: "🥺" } },
            { [ImageFilter.BEAST_MODE_ON_EMOJI]: { text: `BEAST MODE\nON`, emoji: "🔥" } },
            { [ImageFilter.BE_BETTER_EVERYDAY_EMOJI]: { text: `BE BETTER\nEVERYDAY`, emoji: "✌🏻" } },
            { [ImageFilter.YOGI_LIFE_EMOJI]: { text: `YOGI\nLIFE`, emoji: "🧘🏻‍♂️" } },
            { [ImageFilter.WE_ARE_CULT_EMOJI]: { text: `WE ARE\nCULT`, emoji: "🙌🏻" } },
            { [ImageFilter.CRUNCH_CURL_REPEAT_EMOJI]: { text: `CRUNCH,\nCURL,\nREPEAT`, emoji: "⚡️" } }
        ]
    }

    static getLastClassAttended(classesAttendedHistory: GraphEntry[]) {
        const classesAttendedHistoryCopy = [...classesAttendedHistory]
        classesAttendedHistoryCopy.reverse()
        let lastClassAttendedWeekCount = -1, classAttended = false
        classesAttendedHistoryCopy.forEach(classesAttended => {
            if (classesAttended.value === 0 && lastClassAttendedWeekCount < 5 && !classAttended) {
                lastClassAttendedWeekCount++
            } else {
                classAttended = true
                lastClassAttendedWeekCount = (lastClassAttendedWeekCount === -1) ? 0 : lastClassAttendedWeekCount
            }
        })
        return lastClassAttendedWeekCount
    }

    static getClassConfigByLastClassAttended(lastAttendedLastWeek: number | string) {
        switch (lastAttendedLastWeek) {
            case 0:
                return "ZERO_CLASSES_0"
            case 1:
                return "ZERO_CLASSES_1"
            case 2:
                return "ZERO_CLASSES_2"
            case 3:
                return "ZERO_CLASSES_3"
            case 4:
                return "ZERO_CLASSES_4"
            case "MEMBER_PREV_WEEK_ZERO_STATE":
                return "MEMBER_PREV_WEEK_ZERO_STATE"
            case "MEMBERSHIP_PAUSED":
                return "MEMBERSHIP_PAUSED"
            default:
                return "ZERO_CLASSES_4"
        }
    }

    static getLastWeekStartDate(userContext: UserContext) {
        const tz = userContext.userProfile.timezone
        const curr = new Date
        const firstDayOfLastWeek = curr.getDate() - curr.getDay() - 6
        const lastWeekStartDate = new Date(curr.setDate(firstDayOfLastWeek))
        const lastWeekStartDateFormated = momentTz.tz(lastWeekStartDate, tz).format("YYYY-MM-DD")
        return { lastWeekStartDate, lastWeekStartDateFormated }
    }
    static async getActionUrlForNuxBanner(cultFitService: ICultServiceOld, userId: string, userCache?: CacheHelper, announcementBusiness?: AnnouncementBusiness, userContext?: UserContext): Promise<string> {
        const classRecommendationStatus = await cultFitService.getNuxClassRecommendationStatus(userId, "CUREFIT_APP")
        if (classRecommendationStatus.hasSubmittedForm) {
            return "curefit://classrecommendation"
        }
        if (userCache && userContext && announcementBusiness) {
            const cultAndMindSummary = await userCache.getCultSummary(userContext.userProfile.userId)
            if (CultUtil.isCompletedClassCountZero(cultAndMindSummary)) {
                const announcementDetails = <AnnouncementDetails>await announcementBusiness.createAndGetAnnouncementDetails(userContext, `NUX_LANDING_PAGE`)
                if (announcementDetails.state === "CREATED") {
                    return "curefit://nuxlandingpage"
                }
            }
        }
        return "curefit://userform?formId=NUX_FORM"
    }

    static async getNuxLandingPageUrl(userContext: UserContext, currentUser: User, hamletBusiness: HamletBusiness, userCache: CacheHelper, announcementBusiness: AnnouncementBusiness) {
        const isNuxSupported = await AppUtil.isNUXSupported(userContext, currentUser.isInternalUser, "FITNESS", hamletBusiness)
        const cultAndMindSummary = await userCache.getCultSummary(userContext.userProfile.userId)
        if (isNuxSupported && CultUtil.isCompletedClassCountZero(cultAndMindSummary)) {
            const announcementDetails = <AnnouncementDetails>await announcementBusiness.createAndGetAnnouncementDetails(userContext, `NUX_LANDING_PAGE`)
            if (announcementDetails.state === "CREATED") {
                return "curefit://nuxlandingpage"
            }
        }
        return null
    }

    static getSwimmingPolicySegmentIds(): Array<SWIMMING_SEGMENT_ID> {
        return [
            "swimming_center_purchase",
            "greater_that_50_swimming_usage"
        ]
    }

    public static getSwimmingPolicyInternalSegmentId(segmentId: SWIMMING_SEGMENT_ID): string {
        return _getSwimmingSegmentInternalToExternalMapping()[segmentId]
    }

    public static getSwimmingPolicyExternalSegmentId(segmentId: string): SWIMMING_SEGMENT_ID | undefined {
        const mapping = _getSwimmingSegmentInternalToExternalMapping()
        if (Object.values(mapping).includes(segmentId)) {
            const index = Object.values(mapping).findIndex(x => x === segmentId)
            return Object.keys(mapping)[index] as SWIMMING_SEGMENT_ID
        }
    }

    static getCultMemoryOpenModalAction(
        imageUrl: string,
        momentId: number,
        classId: number,
        cultClass: CultClass,
        timezone: Timezone,
        aspectRatio?: number
    ): Action {
        const classStartTime = TimeUtil.formatDateStringInTimeZone(cultClass.date + " " + cultClass.startTime, timezone, "h:mm A")
        return {
            actionType: "SHOW_CULT_MEMORY_MODAL",
            meta: {
                analyticsData: {
                    momentId: momentId,
                    classId: classId,
                    type: "cult_memory",
                    fileType: "image"
                },
                aspectRatio: aspectRatio || CULT_MEMORY_IMAGE_ASPECT_RATIO,
                imageUrl: UrlPathBuilder.prefixSlash(imageUrl),
                leftCaption: "#WEARECULT",
                rightCaption: `${cultClass.Workout.name.toUpperCase()} | ${TimeUtil.formatDateStringInTimeZone(cultClass.date, timezone, "DD.MM.YYYY")} | ${classStartTime}`,
                shareAction: {
                    actionType: "SHARE_SCREENSHOT",
                    title: "SHARE MEMORY",
                    meta: {
                        shareMessage: `Athletes are not born, they are sculpted in the field #WEARECULT`
                    }
                }
            }
        }
    }

    static getCultMemoryBoomerangOpenModalAction(
        videoUrl: string,
        imageUrl: string,
        momentId: number,
        classId: number,
        aspectRatio?: number
    ): Action {
        return {
            actionType: "SHOW_CULT_MEMORY_MODAL",
            meta: {
                analyticsData: {
                    momentId: momentId,
                    classId: classId,
                    type: "cult_memory",
                    fileType: "boomerang"
                },
                aspectRatio: aspectRatio || CULT_MEMORY_IMAGE_ASPECT_RATIO,
                imageUrl: UrlPathBuilder.prefixSlash(imageUrl),
                videoUrl: CdnUtil.getCdnUrl(videoUrl),
                shareAction: {
                    actionType: "SHARE_ACTION",
                    title: "SHARE MEMORY",
                    meta: {
                        shareOptions: {
                            title: `Athletes are not born, they are sculpted in the field #WEARECULT`,
                            fileType: "video",
                            url: CdnUtil.getCdnUrl(videoUrl)
                        }
                    }
                }
            }
        }
    }

    static isMindPackIncludedInCultPack(cultPack: CultPack) {
        if (!_.isEmpty(cultPack.includedProducts) && cultPack.includedProducts[0].tenant === "mind.fit") {
            return true
        }
        return false
    }

    static async getShareAction(userContext: UserContext, productType: ProductType, classLink: string, message: string, cultMindBooking?: CultBooking, classInviteLinkCreator?: ClassInviteLinkCreator): Promise<Action> {
        let shareUrl
        if (classLink) {
            shareUrl = classLink
        }
        else if (classInviteLinkCreator && cultMindBooking) {
            const cultMindClass = cultMindBooking.Class
            shareUrl = await classInviteLinkCreator.getCultClassInviteLink(userContext, cultMindClass.id.toString(), productType, cultMindBooking.Center.id.toString())
        }
        if (shareUrl) {
            return {
                actionType: "SHARE_ACTION",
                meta: {
                    shareOptions: {
                        type: "image/png",
                        message: message + shareUrl,
                        title: "Invite your buddy"
                    },
                    shareChannel: "WHATSAPP",
                    analyticsData: {
                        type: "FITNESS_CLASS_INVITE"
                    }
                },
            }
        }
    }

    static getLazyInviteLinkAction(userContext: UserContext, productType: ProductType, cultMindBooking: CultBooking): Action {
        const body: CultInviteLazyPostBody = {
            productType,
            bookingNumber: cultMindBooking.bookingNumber || cultMindBooking.wlBookingNumber,
            classId: cultMindBooking.Class?.id || cultMindBooking.CultClass?.id,
            message: this.getIncompleteActionMessage(cultMindBooking, userContext)
        }
        return {
            actionType: "REST_API",
            showLoadingIndicator: true,
            meta: {
                method: "post",
                url: `/cult/inviteLink`,
                body,
            },
        }
    }

    public static async showFlutterOrderConfirmationScreenForWorkout(cultBooking: CultBooking, cultFitService: ICultServiceOld): Promise<Boolean> {
        const workoutId = cultBooking.CultClass.workoutID
        return await this.isUnboundWorkoutId(workoutId, cultFitService)
    }

    public static async isUnboundWorkoutId(workoutId: Number, cultFitService: ICultServiceOld): Promise<Boolean> {
        const cultUnboundWorkoutIds: String[] = await cultFitService.getUnboundWorkoutIds()
        return cultUnboundWorkoutIds.includes(workoutId.toString())
    }

    public static async isUnboundWorkoutIdV2(workoutIds: string[], cultFitService: ICultServiceOld, isCultUnbound: boolean): Promise<Boolean> {
        const cultUnboundWorkoutIds: String[] = await cultFitService.getUnboundWorkoutIds()
        workoutIds.forEach(workoutId => {
            if (cultUnboundWorkoutIds.includes(workoutId)) {
                return true
            }
        })
        return false
    }

    static getIncompleteActionMessage(cultMindBooking: CultBooking, userContext: UserContext) {
        const cultMindClass = cultMindBooking.Class ? cultMindBooking.Class : cultMindBooking.CultClass
        const classStartDateAndTime = `${TimeUtil.formatDateStringInTimeZone(cultMindClass.date, userContext.userProfile.timezone, "MMM D")}, ${TimeUtil.formatDateStringInTimeZone(cultMindClass.date + " " + cultMindClass.startTime, userContext.userProfile.timezone, "h:mm a")}`
        return `Hey, I am going for ${cultMindClass.Workout.name} at ${cultMindBooking.Center.name} on ${classStartDateAndTime}. Use this link to join me: `
    }

    static buildDiyShareActionFromShareLink(metaText: string, shareUrl: string, type: string): Action {
        const message = `${metaText} ${shareUrl}.`
        return {
            title: "INVITE",
            actionType: "SHARE_ACTION",
            meta: {
                shareOptions: {
                    type: "image/png",
                    message,
                    title: "Invite your buddy"
                },
                shareChannel: "WHATSAPP",
                analyticsData: {
                    type,
                    source: "diyInvite"
                }
            },
        }
    }

    static filterSelectedTag(tags: TagEntry[]) {
        if (!_.isEmpty(tags)) {
            return tags.filter(tag => tag.mapped === true)
        }
        return []
    }

    static getMomentsV2(
        cultMoments: CultMomentsResponseV2[],
        cultClass: CultClass,
        workoutName: string,
        tz: Timezone,
        getFooter?: boolean,
        hideCaption?: boolean,
        removeTopBorderRadius?: boolean,
        getAspectRatio?: boolean
    ) {
        return cultMoments.map(moment => {
            const cultMoment: Moments = {
                videoUrl: this.isBoomerang(moment.CultMoment) && CdnUtil.getCdnUrl(moment.CultMoment.fileURL),
                thumbnailURL: this.isBoomerang(moment.CultMoment) && UrlPathBuilder.prefixSlash(moment.CultMoment.thumbnailURL),
                imageUrl: UrlPathBuilder.prefixSlash(moment.CultMoment.imageURL),
                removeTopBorderRadius: removeTopBorderRadius ? true : false
            }
            if (!hideCaption) {
                const classStartTime = TimeUtil.formatDateStringInTimeZone(cultClass.date + " " + cultClass.startTime, tz, "h:mm A")
                cultMoment["caption"] = {
                    title: "#WEARECULT",
                    subTitle: `${workoutName} | ${cultClass.date} | ${classStartTime}`
                }
            }
            if (getFooter) {
                cultMoment["footer"] = {
                    user: {
                        profileImage: CUREFIT_LOGO,
                        fullName: "Cure Fit",
                        info: "cult.fit Trainer"
                    },
                    shareAction: !this.isBoomerang(moment.CultMoment) ? {
                        actionType: "SHARE_SCREENSHOT",
                        meta: {
                            analyticsData: {
                                momentId: moment.id,
                                classId: cultClass.id,
                                type: "cult_memory",
                                fileType: "image"
                            },
                            shareOptions: {
                            },
                            shareMessage: "Athletes are not born, they are sculpted in the field #WEARECULT",
                        }
                    } : {
                            actionType: "SHARE_ACTION",
                            title: "SHARE MEMORY",
                            meta: {
                                analyticsData: {
                                    momentId: moment.id,
                                    classId: cultClass.id,
                                    type: "cult_memory",
                                    fileType: "video"
                                },
                                shareOptions: {
                                    fileType: "video",
                                    url: CdnUtil.getCdnUrl(moment.CultMoment.fileURL)
                                },
                                shareMessage: "Athletes are not born, they are sculpted in the field #WEARECULT",

                            }
                        }
                }
            }
            if (getAspectRatio &&
                moment.CultMoment.meta &&
                moment.CultMoment.meta.fileOptions &&
                moment.CultMoment.meta.fileOptions.metadata
            ) {
                const aspectRatio = moment.CultMoment.meta.fileOptions.metadata?.width /
                    moment.CultMoment.meta.fileOptions.metadata?.height
                cultMoment["aspectRatio"] = Number(aspectRatio.toFixed(4))
                cultMoment["action"] = !this.isBoomerang(moment.CultMoment)
                    ? CultUtil.getCultMemoryOpenModalAction(moment.CultMoment.imageURL, moment.cultMomentID, moment.CultMoment.classID, cultClass, tz, aspectRatio)
                    : CultUtil.getCultMemoryBoomerangOpenModalAction(moment.CultMoment.fileURL, moment.CultMoment.imageURL, moment.cultMomentID, moment.CultMoment.classID, aspectRatio)
            } else {
                cultMoment["action"] = !this.isBoomerang(moment.CultMoment)
                    ? CultUtil.getCultMemoryOpenModalAction(moment.CultMoment.imageURL, moment.cultMomentID, moment.CultMoment.classID, cultClass, tz)
                    : CultUtil.getCultMemoryBoomerangOpenModalAction(moment.CultMoment.fileURL, moment.CultMoment.imageURL, moment.cultMomentID, moment.CultMoment.classID)
            }
            return cultMoment
        })
    }

    static async getAttributes(profileAttributes: UserAttributeMappingEntry[], userContext: UserContext) {
        const isCultLiveUserProfileSupported = await AppUtil.isCultLiveUserProfileSupported(userContext)
        return profileAttributes.filter(attribute => _.get(attribute, "attribute.code") !== ProfileAttributeEntryCode.STREAK).map(attribute => {
            return {
                title: attribute.attributeValue,
                subTitle: isCultLiveUserProfileSupported ? attribute.attribute.displayName.toUpperCase() : attribute.attribute.displayName.split(" ").join("\n").toUpperCase()
            }
        })
    }

    static getStreak(streakAttribute: UserAttributeMappingEntry, tz: Timezone): undefined | Streak {
        if (_.isEmpty(_.get(streakAttribute, "attributeValue"))) {
            return undefined
        }
        const streakResponse: StreakResponse[] = JSON.parse(streakAttribute.attributeValue)
        const streakLength = STREAK_LENGTH
        const attended = []
        const dateToday = TimeUtil.todaysDate(tz)
        for (let i = streakResponse.length - 1; i > -1; i--) {
            const streakDate = streakResponse[i].date
            const diffInDaysFromToday = TimeUtil.diffInDays(tz, streakDate, dateToday)
            if (diffInDaysFromToday > 0 && diffInDaysFromToday <= streakLength && streakResponse[i].count > 0) {
                /* Serves as index */
                const diffFromFirstStreakDay = streakLength - diffInDaysFromToday
                attended.push(diffFromFirstStreakDay)
            } else if (diffInDaysFromToday < 0) {
                /* If the streakDate is today */
                continue
            } else if (diffInDaysFromToday > streakLength) {
                /* If we have iterated out of interested range */
                break
            }
        }
        const attributeDisplayName: string = _.get(streakAttribute, "attribute.displayName")
        return !_.isEmpty(attended) ? {
            attended: attended.reverse(),
            groupSize: STREAK_GROUP_SIZE,
            numberOfClasses: streakLength,
            title: (attributeDisplayName && attributeDisplayName.toUpperCase()) || "3 WEEK STREAK"
        } : undefined

    }

    static async getCultMemoryItemV2(cultMomentsForClass: CultMomentsForClass, userContext: UserContext, userCache: CacheHelper, hideCaption?: boolean, removeTopBorderRadius?: boolean) {
        const currentUserProfile = cultMomentsForClass.userProfile
        const tz = userContext.userProfile.timezone
        const cultMoments = cultMomentsForClass.moments
        const cultClass = cultMomentsForClass.classDetails
        const classStartTime = TimeUtil.formatDateStringInTimeZone(cultClass.date + " " + cultClass.startTime, tz, "h:mm A")
        const subTitle = `${TimeUtil.formatDateStringInTimeZone(cultClass.date, tz, "ddd MMM D")}, ${classStartTime}, ${cultClass.Center.name}`
        const action: Action = {
            actionType: "NAVIGATION",
            title: "KNOW MORE",
            url: `curefit://cultclassmomentpage?classId=${cultClass.id}`
        }
        const workoutName = cultMomentsForClass.classDetails.Workout.name
        const moments = CultUtil.getMomentsV2(cultMoments, cultClass, workoutName, tz, false, hideCaption, removeTopBorderRadius)
        const { attendedUsers } = cultMomentsForClass
        let buddiesImages
        const buddiesProfileInfo: CultMemoriesProfileInfo[] = []
        if (userContext.sessionInfo.appVersion >= SHOW_INITIAL_IN_CULT_MEMORIES_VERSION) {
            const buddiesUserIds = attendedUsers.map((user) => user.userId)
            const buddiesUserInfo = await userCache.getUsers(buddiesUserIds)

            buddiesImages = await Promise.all(attendedUsers.map(async (user) => {
                const userDetails = buddiesUserInfo[user.userId]
                if (currentUserProfile.visibility === "PUBLIC" && user.visibility === "PUBLIC" && userDetails.profilePictureUrl) {
                    buddiesProfileInfo.push(null)
                    return userDetails.profilePictureUrl
                }

                if (userDetails?.firstName) {
                    const buddyProfileInfo: CultMemoriesProfileInfo = {
                        profileName: userDetails.firstName,
                        backgroundColor: await getProfileBackgroundColor(userDetails.firstName)
                    }
                    buddiesProfileInfo.push(buddyProfileInfo)
                    return null
                }
                buddiesProfileInfo.push(null)
                return BLUR_PROFILE_IMAGES[Math.floor(Math.random() * 3)]
            }))
        } else {
            buddiesImages = await Promise.all(attendedUsers.map(async (user) => {
                if (currentUserProfile.visibility === "PUBLIC" && user.visibility === "PUBLIC") {
                    const userDetails = await userCache.getUser(user.userId)
                    return userDetails.profilePictureUrl
                }
                return BLUR_PROFILE_IMAGES[Math.floor(Math.random() * 3)]
            }))
        }

        return {
            title: workoutName,
            subTitle: subTitle,
            moments: moments,
            buddies: {
                title: "YOU WORKED OUT WITH",
                imageUrl: buddiesImages,
                profileInfo: buddiesProfileInfo
            },
            action
        }
    }

    static getProfileClickAction(isCurrentUserProfilePublic: boolean, isOtherUserProfilePublic: boolean, profileIndex: number, classId: number | string, extraParams?: { [key: string]: any }): Action {
        let profileAction: Action = null
        if (!isCurrentUserProfilePublic) {
            profileAction = CultUtil.getMakeProfilePublicAction(isCurrentUserProfilePublic)
        } else if (isOtherUserProfilePublic) {
            profileAction = {
                actionType: "NAVIGATION",
                url: `curefit://cultsocialprofilelistpage`,
                meta: {
                    queryParams: {
                        profileIndex,
                        classId,
                        ...extraParams
                    }
                }
            }
        } else {
            profileAction = {
                actionType: "SHOW_TOAST_MESSAGE",
                title: "User profile is private",
                meta: {
                    toastDuration: "SHORT",
                    toastPosition: "TOP"
                }
            }
        }
        return profileAction
    }

    static getMakeProfilePublicAction(isCurrentUserProfilePublic: boolean, description?: string): Action {
        return {
            actionType: "SHOW_PROFILE_IS_PRIVATE_MODAL",
            meta: {
                title: "Private Mode",
                description: description || "Your profile is currently set in private mode. Make your profile “Public” to see who you worked out with",
                action: {
                    title: "MAKE PROFILE PUBLIC",
                    actionType: "REST_API",
                    meta: {
                        method: "PUT",
                        url: `/cult/social/profileUpdate`,
                        body: { "cultUserProfile": { "visibility": "PUBLIC" } },
                        title: "MAKE PROFILE PUBLIC"
                    }
                },
                analyticsData: {
                    profileVisibility: !isCurrentUserProfilePublic ? "PUBLIC" : "PRIVATE",
                    pageFrom: "Cult class moment page"
                }
            }
        }
    }


    static getBuddiesJoiningText(pageType: string, numberOfBuddiesJoining: number, attendingUsersData: User[], isDropppedOut?: boolean): {
        infoText: string,
        showOverlappingIcons: boolean
    } {
        let infoText
        const buddyName = attendingUsersData[0].firstName
        if (numberOfBuddiesJoining === 1) {
            infoText = `${buddyName} ${isDropppedOut ? "will miss you" : "is joining you"}`
        }
        else if (numberOfBuddiesJoining === 2) {
            infoText = `${buddyName} +${numberOfBuddiesJoining - 1} ${isDropppedOut ? "other will miss you" : "buddy joining you"}`
        }
        else {
            infoText = `${buddyName} +${numberOfBuddiesJoining - 1} ${isDropppedOut ? "others will miss you" : "buddies joining you"}`
        }
        const showOverlappingIcons = numberOfBuddiesJoining >= BUDDIES_JOINING_SEPARATED_ICONS_LIMIT
        return {
            infoText,
            showOverlappingIcons
        }
    }

    static async getBuddiesJoiningListSmallViewOld(attendingUsers: ClassAttendingUser[], userCache: CacheHelper, pageType: string, isDroppedOut?: boolean): Promise<CultBuddiesJoiningListSmallView> {
        /* Only getting the userData for users which will be shown */
        const attendingUserDataPromises = attendingUsers.slice(0, BUDDIES_JOINING_SMALL_LIST_LIMIT).map(async (item) => userCache.getUser(String(item.userID)))
        const attendingUsersData = await Promise.all(attendingUserDataPromises)
        const buddies = attendingUsers.slice(0, BUDDIES_JOINING_SMALL_LIST_LIMIT).map((item, index) => {
            const buddy = attendingUsersData[index]
            return {
                icon: !_.isNil(buddy.profilePictureUrl) ? buddy.profilePictureUrl : "",
                name: !_.isNil(buddy.firstName) ? buddy.firstName : "",
                state: item.bookingStatus
            }
        })
        let sceneStyle
        if (pageType === PageTypes.PreBookClass) {
            sceneStyle = { paddingHorizontal: 25 }
        }
        const { infoText, showOverlappingIcons } = CultUtil.getBuddiesJoiningText(pageType, attendingUsers.length, attendingUsersData, isDroppedOut)
        return {
            buddies,
            infoText,
            showOverlappingIcons,
            legendData: BUDDIES_JOINING_STATE_LEGEND,
            sceneStyle
        }
    }
    static async getBuddiesJoiningListSmallView(attendingUsers: number[], userCache: CacheHelper, pageType: string, isDroppedOut?: boolean): Promise<CultBuddiesJoiningListSmallView> {
        /* Only getting the userData for users which will be shown */
        const attendingUserDataPromises = attendingUsers.slice(0, BUDDIES_JOINING_SMALL_LIST_LIMIT).map(async (userId) => userCache.getUser(String(userId)))
        const attendingUsersData = await Promise.all(attendingUserDataPromises)
        const buddies = attendingUsers.slice(0, BUDDIES_JOINING_SMALL_LIST_LIMIT).map((item, index) => {
            const buddy = attendingUsersData[index]
            return {
                icon: !_.isNil(buddy.profilePictureUrl) ? buddy.profilePictureUrl : "",
                name: !_.isNil(buddy.firstName) ? buddy.firstName : "",
                state: "BOOKED"
            }
        })
        let sceneStyle
        if (pageType === PageTypes.PreBookClass) {
            sceneStyle = { paddingHorizontal: 25 }
        }
        const { infoText, showOverlappingIcons } = CultUtil.getBuddiesJoiningText(pageType, attendingUsers.length, attendingUsersData, isDroppedOut)
        return {
            buddies,
            infoText,
            showOverlappingIcons,
            legendData: BUDDIES_JOINING_STATE_LEGEND,
            sceneStyle
        }
    }

    static getDiyShareAction(packId: string, productId: string, productType: ProductType, isSession: boolean, packName: string): Action {
        return {
            icon: "SHARE",
            actionType: "REST_API",
            showLoadingIndicator: true,
            meta: {
                method: "post",
                url: `/pack/diy/inviteLink`,
                body: {
                    productId,
                    packId,
                    productType,
                    isSession
                },
            },
            analyticsData: {
                type: productType,
                eventType: "SHARE_EVENT",
                packName,
            }
        }
    }

    static async getBuddiesJoiningListLargeView(attendingUsers: number[], userCache: CacheHelper, pageType: string): Promise<CultBuddiesJoiningListLargeView> {
        const attendingUserDataPromises = attendingUsers.map(async (item) => userCache.getUser(String(item)))
        const attendingUserData = await Promise.all(attendingUserDataPromises)
        const buddies = attendingUsers.map((item, index) => {
            const buddy = attendingUserData[index]
            return {
                icon: buddy.profilePictureUrl,
                name: buddy.firstName,
                state: "BOOKED"
            }
        })

        return {
            legendData: BUDDIES_JOINING_STATE_LEGEND,
            buddies,
            title: "Buddies joining you"
        }
    }

    static getCultMemoryItem(moment: CultMomentsResponse, userContext: UserContext) {
        const tz = userContext.userProfile.timezone
        const cultMoment = moment.CultMoment
        const cultClass = cultMoment.CultClass
        const classStartTime = TimeUtil.formatDateStringInTimeZone(cultClass.date + " " + cultClass.startTime, tz, "h:mm A")
        const subTitle = `${TimeUtil.formatDateStringInTimeZone(cultClass.date, tz, "ddd D MMM")}, ${classStartTime}, ${cultClass?.Center?.name || ""}`
        const action = !this.isBoomerang(cultMoment) ? CultUtil.getCultMemoryOpenModalAction(cultMoment.imageURL, moment.cultMomentID, cultMoment.classID, cultClass, tz) : CultUtil.getCultMemoryBoomerangOpenModalAction(cultMoment.fileURL, cultMoment.imageURL, moment.cultMomentID, cultMoment.classID)
        return {
            videoUrl: this.isBoomerang(cultMoment) && CdnUtil.getCdnUrl(cultMoment.fileURL),
            thumbnailURL: this.isBoomerang(cultMoment) && UrlPathBuilder.prefixSlash(cultMoment.thumbnailURL),
            imageUrl: UrlPathBuilder.prefixSlash(cultMoment.imageURL),
            caption: {
                title: cultClass?.Workout?.name || "",
                subTitle: subTitle
            },
            action
        }
    }

    static isBoomerang(cultMoment: CultMoment | CultMomentV2): boolean {
        return cultMoment.meta && cultMoment.meta.fileOptions && cultMoment.meta.fileOptions.fileType === CultMomentFileType.BOOMERANG
    }
    static displayPrivateProfileName(value: string) {
        if (value) {
            return `${value[0].toUpperCase()}***`
        }
        return "****"
    }

    static async getCultClassRescheduleModalAction(productParams: any, userContext: UserContext, cultBusiness: ICultBusiness) {
        const type: ManageOption = "SHOW_CLASS_RESCHEDULE_MODAL"
        const cancellationInfo = await cultBusiness.getBookingCancellationInfo(userContext, productParams.bookingNumber)
        if (!cancellationInfo.showCancellationNudge) {
            return null
        }
        let modalMeta: any
        switch (cancellationInfo.type) {
            case "QA1": {
                const user = await userContext.userPromise
                modalMeta = {
                    toastMessage: "Please select a reason",
                    title: `Hi ${user.firstName}, ${cancellationInfo.question?.text}`,
                    emoji: "/image/icons/cult/reschedule-class/qa1-emoji-v1.png",
                    leftAlign: true,
                    options: cancellationInfo?.answers,
                    actions: [{
                        actionType: "CANCEL_CULT_CLASS",
                        title: "CANCEL CLASS",
                        meta: { ...productParams, cancelWithoutAlert: true },
                        subTitle: cancellationInfo.streak > 0 ? `${moment.localeData().ordinal(cancellationInfo.streak)} time in last ${cancellationInfo.streakDuration} days` : ""
                    }],
                    cancellationResponseMeta: {
                        questionId: cancellationInfo.question?.id,
                    }
                }
            }
                break
            case "QA2": {
                const user = await userContext.userPromise
                modalMeta = {
                    toastMessage: "Please select a reason",
                    title: `Hi ${user.firstName}, ${cancellationInfo.question?.text}`,
                    emoji: "/image/icons/cult/reschedule-class/qa2-emoji-v1.png",
                    leftAlign: true,
                    options: cancellationInfo?.answers,
                    actions: [{
                        actionType: "CANCEL_CULT_CLASS",
                        title: "CANCEL CLASS",
                        meta: { ...productParams, cancelWithoutAlert: true },
                        subTitle: cancellationInfo.streak > 0 ? `${moment.localeData().ordinal(cancellationInfo.streak)} time in last ${cancellationInfo.streakDuration} days` : ""
                    }],
                    cancellationResponseMeta: {
                        questionId: cancellationInfo.question?.id,
                    }
                }
            }
                break
            case "BUDDY": {
                const { buddies } = cancellationInfo
                let title = ""
                const buddiesLength = buddies.length
                if (buddiesLength === 1) {
                    title = `Looks like ${buddies[0].name} is joining you for this class`
                } else if (buddiesLength === 2) {
                    title = `Looks like ${buddies[0].name} and ${buddies[1].name} are joining you for this class`
                } else if (buddiesLength > 2) {
                    title = `Looks like ${buddies[0].name}, ${buddies[1].name} + ${buddiesLength - 2} ${buddiesLength - 2 === 1 ? "buddy" : "buddies"} are joining you for this class`
                }
                const buddiesProfileIcon = buddies.map((buddy: any) => buddy.profilePictureUrl)
                const user = await userContext.userPromise
                modalMeta = {
                    title: title,
                    emoji: "/image/icons/cult/reschedule-class/moment-emoji-v1.png",
                    buddiesProfileIcon: buddiesProfileIcon.length > 4 ? buddiesProfileIcon.slice(0, 4) : buddiesProfileIcon,
                    highlightText: `Why would you ditch us, ${user.firstName}?`,
                    actions: [{
                        actionType: "CANCEL_CULT_CLASS",
                        title: "CANCEL CLASS",
                        meta: { ...productParams, cancelWithoutAlert: true },
                        subTitle: cancellationInfo.streak > 0 ? `${moment.localeData().ordinal(cancellationInfo.streak)} time in last ${cancellationInfo.streakDuration} days` : ""
                    }]
                }
            }
                break
            case "MOMENT": {
                const user = await userContext.userPromise
                modalMeta = {
                    title: `You were doing pretty well, ${user.firstName}. Don’t fallback now!`,
                    emoji: "/image/icons/cult/reschedule-class/moment-emoji-v1.png",
                    momentImageUrl: cancellationInfo.imageURL,
                    actions: [{
                        actionType: "CANCEL_CULT_CLASS",
                        title: "CANCEL CLASS",
                        meta: { ...productParams, cancelWithoutAlert: true },
                        subTitle: cancellationInfo.streak > 0 ? `${moment.localeData().ordinal(cancellationInfo.streak)} time in last ${cancellationInfo.streakDuration} days` : ""
                    }]
                }
            }
                break
            case "RESCHEDULE":
            case "DEFAULT":
            default: {
                modalMeta = {
                    title: "Are you sure you want to cancel?",
                    emoji: "/image/icons/cult/reschedule-class/reschedule-emoji-v1.png",
                    actions: [{
                        actionType: "CANCEL_CULT_CLASS",
                        title: "CANCEL CLASS",
                        meta: { ...productParams, cancelWithoutAlert: true },
                    }]
                }
            }
                break
        }
        return {
            type: type,
            meta: {
                ...modalMeta,
                streak: cancellationInfo.streak,
                modalType: cancellationInfo.type,
                actions: [...modalMeta?.actions,
                {
                    isPrimaryButton: true,
                    actionType: "NAVIGATION",
                    url: `curefit://classbookingv2?productType=${productParams.productType}&pageFrom=${PageTypes.CultDetail}&rescheduleSourceBookingNumber=${productParams.bookingNumber}`,
                    title: "RESCHEDULE"
                }]
            }
        }
    }
    // TODO: TG: define for whom to support the reschedule
    static async isRescheduleClassSupported(userContext: UserContext, cultBooking: CultBooking, hamletBusiness: HamletBusiness, cafeFoodDetails?: FoodFulfilment) {
        return await AppUtil.isRescheduleClassSupported(userContext, hamletBusiness) && cultBooking.bookingNumber && [BookingTypes.MEMBER, BookingTypes.MEMBER_AWAY].includes(cultBooking.bookingType) && _.isEmpty(cafeFoodDetails)
    }

    static async isRescheduleClassV2Supported(userContext: UserContext) {
        const appVersion = userContext.sessionInfo.appVersion
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        if (appVersion >= 9.15) {
            return true
        }
        return false
    }


    static createWorkoutMember(
        isCurrentUserProfilePublic: boolean,
        isOtherUserProfilePublic: boolean,
        userDetails: User,
        action?: Action,
        showMemberInitial?: boolean,
    ): WorkoutMember {
        let profileInfo: CultMemoriesProfileInfo
        const showInitialPossible = showMemberInitial && userDetails?.firstName

        if (showInitialPossible) {
            profileInfo = {
                profileName: userDetails.firstName,
                backgroundColor: getProfileBackgroundColor(userDetails.firstName)
            }
        }

        const defaultProfile = showInitialPossible ? undefined : BLUR_PROFILE_IMAGES[Math.floor(Math.random() * 3)]
        const defaultName = showMemberInitial ? undefined : CultUtil.displayPrivateProfileName(userDetails.firstName)
        const showProfileImagePossible = (isCurrentUserProfilePublic && isOtherUserProfilePublic && userDetails.profilePictureUrl)

        return {
            name: (isCurrentUserProfilePublic && isOtherUserProfilePublic)
                ? _.get(userDetails.firstName, "length") > 0
                    ? userDetails.firstName[0].toUpperCase() + userDetails.firstName.slice(1)
                    : ""
                : defaultName,
            profileImage: showProfileImagePossible
                            ? userDetails.profilePictureUrl : defaultProfile,
            isProfilePublic: isOtherUserProfilePublic,
            action,
            memberProfileInfo: showInitialPossible && !showProfileImagePossible ? profileInfo : undefined,
        }
    }

    static getWorkoutKitWidget(preWorkoutGears: PreWorkoutGear[], headerStyle?: any, classDate?: string, centerId?: number): ProductGridWidget {
        const items: InfoCard[] = []
        const skipBoxingGloves = BOXING_GLOVES_PROVIDED_CENTER_IDS.includes(centerId)
        const skipYogaMat = YOGA_MAT_PROVIDED_CENTER_IDS.includes(centerId)
        preWorkoutGears.forEach(preWorkoutGear => {
            const skipCurrentGear = (skipYogaMat && preWorkoutGear.productName.toLowerCase() === "yoga mat") ||
            (skipBoxingGloves && preWorkoutGear.productName.toLowerCase() === "boxing gloves")
            if (!skipCurrentGear) {
                items.push({
                    title: preWorkoutGear.productName,
                    image: preWorkoutGear.productIcon
                })
            }
        })
        if (_.isEmpty(items)) {
            return null
        }
        const header: Header = {
            title: "Bring Your Own Kit " + `(${items.length} ${pluralizeStringIfRequired("item", items.length)})`,
            style: headerStyle
        }
        const workoutKitWidget: ProductGridWidget = new ProductGridWidget("ICON", header, items)
        return workoutKitWidget
    }

    static getWorkoutGearAuroraInfoCardList(preWorkoutGears: PreWorkoutGear[], centerId: number): InfoCard[] {
        const items: InfoCard[] = []
        const skipBoxingGloves = BOXING_GLOVES_PROVIDED_CENTER_IDS.includes(centerId)
        const skipYogaMat = YOGA_MAT_PROVIDED_CENTER_IDS.includes(centerId)
        preWorkoutGears.forEach(preWorkoutGear => {
            const skipCurrentGear = (skipYogaMat && preWorkoutGear.productName.toLowerCase() === "yoga mat") ||
            (skipBoxingGloves && preWorkoutGear.productName.toLowerCase() === "boxing gloves")
            if (!skipCurrentGear) {
                items.push({
                    title: preWorkoutGear.productName,
                    image: this.getPreWorkoutGearAuroraImageUrl(preWorkoutGear.productName)
                })
            }
        })
        return items
    }

    private static getPreWorkoutGearAuroraImageUrl(name: string): string {
        return "/image/icons/workoutGear/" + name.replace(" ", "_").toLowerCase() + "_aurora.png"
    }

    static getCenterFacilitiesList(cultCenter: CultCenter): Facility[] {
        const items: Facility[] = []
        if (_.isNil(cultCenter?.facilities) || _.isEmpty(cultCenter?.facilities)) {
            return items
        }
        cultCenter.facilities.forEach(facility => {
            if (facility.available) {
                const fileName = facility.name.replace(" ", "_").toLowerCase()
                items.push({
                    name: facility.name,
                    imageUrl: "/image/icons/facility/" + fileName + "_aurora_v2.png"
                })
            }
        })
        return items
    }

    static getCovidBeforeAttendanceInstructionWidgetForRunWorkout(): ProductListWidget {
        const header: Header = {
            title: "Reach 20 mins early",
            color: "#000000",
            style: { fontSize: 22, color: "#000000" }
        }
        const items: InfoCard[] = [
            {
                subTitle: "Center manager will check your body temperature, blood oxygen level and status on Aarogya Setu app"
            }]
        const widget = new ProductListWidget("BULLET", header, items)
        widget.style = {
            paddingHorizontal: 20
        }
        return widget
    }

    static getCovidAttendanceInstructionWidgetForRunWorkout(): ProductListWidget {
        const header: Header = {
            title: "Marking attendance",
            color: "#000000",
            style: { fontSize: 22, color: "#000000" }
        }
        const items: InfoCard[] = [
            {
                subTitle: "QR code will appear 20 mins before the session starts"
            },
            {
                subTitle: "Scan your QR code on the attendance app available with the Trainer"
            }]
        const widget = new ProductListWidget("BULLET", header, items)
        widget.style = {
            paddingHorizontal: 20
        }
        return widget
    }

    static getCovidBeforeAttendanceInstructionWidget(): ProductListWidget {
        const header: Header = {
            title: "Reach 15 mins early",
            color: "#000000",
            style: { fontSize: 22, color: "#000000" }
        }
        const items: InfoCard[] = [
            {
                subTitle: "Center manager will check your body temperature, blood oxygen level and status on Aarogya Setu app"
            }
            ]
        const widget = new ProductListWidget("BULLET", header, items)
        widget.style = {
            paddingHorizontal: 20
        }
        return widget
    }

    static getCovidAttendanceInstructionWidget(): ProductListWidget {
        const header: Header = {
            title: "Marking attendance",
            color: "#000000",
            style: { fontSize: 22, color: "#000000" }
        }
        const items: InfoCard[] = [
            {
                subTitle: "QR code appears 20 mins before the class starts"
            },
            {
                subTitle: "Scan the QR code on attendance tab and move to assigned workout station number"
            }
            ]
        const widget = new ProductListWidget("BULLET", header, items)
        widget.style = {
            paddingHorizontal: 20
        }
        return widget
    }

    static getCovidCentreChangesInstructionWidget(isSportCategory?: boolean, centerID?: number, date?: string): ProductListWidget {
        const header: Header = {
            title: "Facilities at your center",
            color: "#000000",
            style: { fontSize: 22, color: "#000000" }
        }
        let showerSubtitle = "Showers facilities have continued for this center. We will be providing the toiletries, please bring your own towels"
        let lockerSubtitle = "Lockers are available at centers to keep valuables"
        let equipmentSubtitle = "All equipment like weights, resistance bands are available at centers for your use"
        if (CULT_FULL_CAPACITY_CENTER_IDS.includes(centerID) || (CULT_FULL_CAPACITY_UPCOMING_CENTER_IDS.includes(centerID) && date >= CULT_FULL_CAPACITY_ACTIVE_FROM_DATE)) {
            showerSubtitle = "Showers facilities have continued for this center. We will be providing the toiletries, please bring your own towels."
            lockerSubtitle = "Locker facilities are now available at this center"
            equipmentSubtitle = "All the equipments like weight, resistance bands you might need would be santised and available at the center"
        }

        let items: InfoCard[] = [
            {
                subTitle: "Workout area is fully sanitised after every class"
            },
            {
                subTitle: equipmentSubtitle
            },
            {
                subTitle: lockerSubtitle
            },
            {
                subTitle: showerSubtitle
            }]
        if (isSportCategory) {
            items = [{
                subTitle: "Facility is fully sanitised after every session"
            },
            {
                subTitle: "Carry your own workout kit and equipments"
            },
            {
                subTitle: lockerSubtitle
            },
            {
                subTitle: showerSubtitle
            }]
        }
        const widget = new ProductListWidget("BULLET", header, items)
        widget.style = {
            paddingHorizontal: 20
        }
        return widget
    }

    static getCovidSafetyInstructionWidget(): ProductListWidget {
        const header: Header = {
            title: "We request your support",
            color: "#000000",
            style: { fontSize: 22, color: "#000000" }
        }
        const items: InfoCard[] = [
            {
                subTitle: "If you are unwell, we recommend staying at home for safety of fellow members and employees"
            }]
        const widget = new ProductListWidget("BULLET", header, items)
        widget.style = {
            paddingHorizontal: 20
        }
        return widget
    }

    static getNoteWidget(cultClass: CultClass): DescriptionWidget {
        if (!_.isEmpty(cultClass.notes)) {
          const infoCard: InfoCard = {
            title: "Note:",
            subTitle: cultClass.notes
          }
          const descriptionWidget = new DescriptionWidget([infoCard])
          return descriptionWidget
        }
    }

    static getWorkoutNotesWidget(cultClass: CultClass, layoutProps?: any): DescriptionWidgetV2 {
        if (!_.isEmpty(cultClass.notes)) {
            const notes = _.map(cultClass.notes.split("\n"), (note) => note.trim())
            return new DescriptionWidgetV2("Note", notes, layoutProps)
        }
    }

    static getWorkoutNotesWidgetV2(cultClass: CultClass, layoutProps?: any): DescriptionWidgetV2 {
        if (!_.isEmpty(cultClass.notes)) {
            const notes = _.map(cultClass.notes.split("\n"), (note) => note.trim().replaceAll("•", ""))
            return new DescriptionWidgetV2("Note", notes, layoutProps)
        }
    }

    static getFormatInfoWidget(assets: String[], layoutProps?: any): WorkoutDetailWidget {
        const data = assets.map((url) => {
            return {
                url: url,
                type: "IMAGE",
                repeat: true
            }
        })

        return new WorkoutDetailWidget("FORMAT INFORMATION", "All you need to know about the new format",
            true, data, layoutProps, true, true)
    }



    static getInstructionsWithMediaWidget(cultClass: any): InstructionsWithMedia {
        if (!_.isEmpty(cultClass.instructions)) {
            const instructionsData = cultClass.instructions
            for (let i = 0; i < instructionsData.length; i++) {
                const mediaLen = instructionsData[i].media.length
                for (let j = 0; j < mediaLen; j++) {
                        instructionsData[i].media[j].index = j
                        if (instructionsData[i].media[j].type === "VIDEO") {
                            const videoUrl = instructionsData[i].media[j].url
                            const mediaUrl = CultUtil.getCultVideoCdnUrl(videoUrl)
                            const videoPlayerUrl = `curefit://videoplayer?absoluteVideoUrl=${encodeURIComponent(mediaUrl)}`
                            instructionsData[i].media[j].videoUrl = mediaUrl
                            instructionsData[i].media[j].action = {actionType: "PLAY_VIDEO", url: videoPlayerUrl}
                            instructionsData[i].media[j].fullScreenIcon = "/image/gymfit/fullscreen.png"
                        }
                        else {
                            instructionsData[i].media[j].image = instructionsData[i].media[j].url
                        }
                }
            }
            const title = "Instructions"
            const instructionsWithMediaWidget = new InstructionsWithMedia(title, instructionsData)
            return instructionsWithMediaWidget
        }
    }

    static getImportantUpdatesWidget(
        userContext: UserContext,
        isSportCategory?: boolean,
        layoutProps?: any,
        headerStyle?: any,
        headerTitleProps?: any,
        center?: CultCenter,
        date?: string,
        isRunningSports?: boolean

    ): BannerCarouselWidget {
        const isDesktop = AppUtil.isDesktop(userContext)
        const bannerWidth = isDesktop ? 173 : 230
        const bannerHeight = isDesktop ? 50 : 75
        const centerID = center?.id
        const isVirtualCenter = center?.metadata?.isVirtualCenter
        let facilityImageUrl = "/image/icons/cult/workout_kit_v3.png"
        if (CULT_FULL_CAPACITY_CENTER_IDS.includes(centerID) || (CULT_FULL_CAPACITY_UPCOMING_CENTER_IDS.includes(centerID) && date >= CULT_FULL_CAPACITY_ACTIVE_FROM_DATE)) {
            facilityImageUrl = "/image/icons/cult/workout_kit_v3.png"
        }

        let banners: Banner[]

        if (isRunningSports || (isVirtualCenter === true)) {
            banners = [
                {
                    id: "attendance",
                    image: isDesktop ? "/image/icons/cult/attendance_instruction_web.png" : "/image/icons/cult/attendance_banner_1.png",
                    action: {
                        actionType: "SHOW_INFOGRAPHIC_MODAL",
                        meta: {
                            imageUrl: "/image/icons/cult/attendance_instruction.png",
                            closeIconUrl: "/image/icons/cult/cancel-modal.png",
                            widgets: [this.getCovidAttendanceInstructionWidgetForRunWorkout()]
                        }
                    }
                }]
        } else {
            banners = [
                {
                    id: "attendance",
                    image: isDesktop ? "/image/icons/cult/attendance_instruction_web.png" : "/image/icons/cult/attendance_banner_1.png",
                    action: {
                        actionType: "SHOW_INFOGRAPHIC_MODAL",
                        meta: {
                            imageUrl: "/image/icons/cult/attendance_instruction.png",
                            closeIconUrl: "/image/icons/cult/cancel-modal.png",
                            widgets: [this.getCovidBeforeAttendanceInstructionWidget(), this.getCovidAttendanceInstructionWidget()]
                        }
                    }
                },
                {
                    id: "center_facility",
                    image: isDesktop ? "/image/icons/cult/center_facility_banner_web.png" : "/image/icons/cult/center_facility_banner_2.png",
                    action: {
                        actionType: "SHOW_INFOGRAPHIC_MODAL",
                        meta: {
                            imageUrl: facilityImageUrl,
                            closeIconUrl: "/image/icons/cult/cancel-modal.png",
                            widgets: [this.getCovidCentreChangesInstructionWidget(isSportCategory, centerID, date), this.getCovidSafetyInstructionWidget()]
                        }
                    }
                }
            ]
        }

        const widget = new BannerCarouselWidget("230:90", banners, {
            bannerWidth: bannerWidth,
            bannerHeight: bannerHeight,
            v2: true,
            noVerticalPadding: true,
            backgroundColor: "#ffffff",
            noAutoPlay: true,
            ...layoutProps
        }, 4, false, false)
        widget.header = {
            title: "Safety Guidelines",
            titleProps: {
                style: {
                    fontSize: 18,
                    ...headerTitleProps
                }
            },
            style: headerStyle
        }
        if (AppUtil.isWeb(userContext)) {
            widget.widgetType = "CAROUSEL_WIDGET"
        }
        return widget
    }
    static getCultBookingNextStepsInfo(booking: CultBooking, isSportCategory?: boolean): InfoCard[] {
        if (CultUtil.isCultRunWorkout(booking.CultClass.workoutID)) {
            return [
                {
                    icon: "/image/icons/cult/qrcode.png",
                    subTitle: `Reach ${booking.qrCode.lowerLimitForQRCode} mins early & carry your phone, face mask and water bottle.`
                },
                {
                    icon: "/image/icons/cult/station_number.png",
                    subTitle: `Trainer will be available at Meet-up point and help in marking attendance.`
                }]
        }
        if (isSportCategory) {
            return [
                {
                    icon: "/image/icons/cult/qrcode.png",
                    subTitle: `Reach ${booking.qrCode.lowerLimitForQRCode} mins early & carry your equipments, phone, face mask and water bottle`
                },
                {
                    icon: "/image/icons/cult/station_number.png",
                    subTitle: `A slot or court number will be allotted once attendance is marked. Please process to same for the session.`
                }]
        }

        return [
            {
                icon: "/image/icons/cult/qrcode.png",
                subTitle: `Reach ${booking.qrCode.lowerLimitForQRCode} minutes early and carry your equipments, phone, mask and water.`
            },
            {
                icon: "/image/icons/cult/qrcode.png",
                subTitle: `We care for your safety and would not be able to allow you to enter the workout floor if you’ve missed first 5 mins of the warm-up to avoid injuries.`
            },
            {
                icon: "/image/icons/cult/station_number.png",
                subTitle: `A workout station number will be allotted once attendance is marked. Please proceed to your station for the workout.`
            }]
    }

    static getCultVideoCdnUrl(url: string) {
        return url.replace("cult-media/assets", "https://cdn-videos.cure.fit/www-curefit-com/video/upload/cult-media")
    }

    static getCultBookingNextStepsWidget(booking: CultBooking): ProductListWidget {
        const header: Header = {
            title: "Next Steps",
            color: "#000000"
        }
        const infoCards: InfoCard[] = CultUtil.getCultBookingNextStepsInfo(booking)
        const widget = new ProductListWidget("SMALL", header, infoCards)
        widget.hideSepratorLines = true
        return widget
    }
    static getWodMovements(wod: SimpleWod): string {
        let subTitle
        if (!_.isEmpty(wod)) {
            let mainMovements = _.flatten(wod.parts.map(part => {
                if (part.partType === "MAIN" && !_.isEmpty(part.movements)) {
                    return part.movements
                }
            }))
            mainMovements = _.filter(mainMovements, movement => !_.isEmpty(movement))
            if (_.isEmpty(mainMovements) && !_.isEmpty(wod.mainPart) && !_.isEmpty(wod.mainPart.movements)) {
                mainMovements = wod.mainPart.movements
            }
            if (!_.isEmpty(mainMovements)) {
                let movementNames = _.map(mainMovements, movement => {
                    if (movement) {
                        return movement.title
                    }
                })
                movementNames = _.filter(movementNames, movementName => !_.isEmpty(movementName))
                if (!_.isEmpty(movementNames)) {
                    subTitle = movementNames.join(", ")
                }
            }
        }
        return subTitle
    }
    static getWodBodyParts(wod: SimpleWod): string {
        let subTitle
        if (!_.isEmpty(wod)) {
            let mainMovements = _.flatten(wod.parts.map(part => {
                if (part.partType === "MAIN" && !_.isEmpty(part.movements)) {
                    return part.movements
                }
            }))
            mainMovements = _.filter(mainMovements, movement => !_.isEmpty(movement))
            if (_.isEmpty(mainMovements) && !_.isEmpty(wod.mainPart) && !_.isEmpty(wod.mainPart.movements)) {
                mainMovements = wod.mainPart.movements
            }
            if (!_.isEmpty(mainMovements)) {
                let bodyParts = _.uniq(_.flatten(mainMovements.map(movement => {
                    if (movement) {
                        return movement.bodyParts
                    }
                })))
                bodyParts = bodyParts.filter(bodyPart => !_.isEmpty(bodyPart))
                subTitle = bodyParts.join(", ")
            }
        }
        return subTitle
    }

    public static getGenderSelectionRecommendationSteps() {
        const steps = [
            {
                title: "Updating your fitness goal",
                time: 1000
            },
            {
                title: "Taking your fitness into account",
                time: 1000
            },
            {
                title: "Working on workout recommendations",
                time: 1000
            },
        ]
        return {
            title: "Changing your goal...",
            steps,
            action: {
                actionType: "NAVIGATION",
                url: "curefit://tabpage?pageId=cult&selectedTab=LiveSGT"
            }
        }
    }

    static getLivePTCrossGenderToggleBarWidget(crossGenderDetails: any, productId: string, showSeeMore?: boolean, containerStyle?: any) {
        const toggleBarWidget: ToggleBarWidget = {
            widgetType: "TOGGLE_BAR_WIDGET",
            title: `Assign only ${crossGenderDetails.genderText} Trainers : `,
            isEnabled: !crossGenderDetails.isCrossGenderEnabled,
            currentValueText: !crossGenderDetails.isCrossGenderEnabled ? "ON" : "OFF",
            toggleAction: {
                actionType: "UPDATE_PATIENT_PREFERENCE",
                analyticsData: {
                    eventKey: "widget_click",
                    eventData: {
                        widgetName: "Cross Gender Toggle Bar Widget",
                        crossGenderEnabled: !crossGenderDetails.isCrossGenderEnabled,
                    }
                },
                successAction: {
                    actionType: "REFRESH_CARE_SLOT_PAGE",
                    meta: {
                        patientId: crossGenderDetails.patient.id,
                        productId,
                    }
                },
                meta: {
                    requestBody: {
                        preference: {
                            patientId: crossGenderDetails.patient.id,
                            preferenceKey: "LIVE_PERSONAL_TRAINING_CROSS_GENDER_TRAINER_MATCH_ENABLED",
                            preferenceValue: !crossGenderDetails.isCrossGenderEnabled
                        },
                        tenant: "CULTFIT"
                    }
                }
            }
        }
        if (showSeeMore) {
            toggleBarWidget.seeMore = {
                seeMoreTitle: "KNOW MORE",
                seeLessTitle: "HIDE",
                text: `By default, we will only assign ${crossGenderDetails.genderText.toLowerCase()} trainers. You can choose to change the same to have access to more trainers and classes.`
            }
            toggleBarWidget.containerStyle = containerStyle
        }
        return toggleBarWidget
    }

    static getWorkoutCaloriesInfo(workout: CultWorkout): string {
        if (!_.isEmpty(workout) && !_.isEmpty(workout.otherAttributes)) {
            if (workout.otherAttributes.minCaloriesBurnt && workout.otherAttributes.maxCaloriesBurnt) {
                return workout.otherAttributes.minCaloriesBurnt + "-" + workout.otherAttributes.maxCaloriesBurnt + "Kcal"
            } else if (workout.otherAttributes.averageCaloriesBurnt) {
                return `${workout.otherAttributes.averageCaloriesBurnt} Kcal`
            }
        }
    }

    static getWodInfoWidget(wod: WodDetailsResponse, calorieText: string): WODInfoWidget {
        if (!_.isEmpty(wod)) {
            const warmUp = wod.parts.find(part => part.partType === "MOBILITY")
            const coolDown = wod.parts.find(part => part.partType === "COOL DOWN")
            let focusInfo: WorkoutInstruction
            if (!_.isEmpty(wod.focus)) {
                focusInfo = {
                    title: "FOCUS",
                    description: wod.focus
                }
            }
            let preWorkoutInstructions: WorkoutInstruction
            if (!_.isEmpty(warmUp) && !_.isEmpty(warmUp.movements)) {
                const warmUpMovementsArray = _.map(warmUp.movements, movement => movement.title)
                preWorkoutInstructions = {
                    title: "WARM UP",
                    description: warmUpMovementsArray.join(", ")
                }
            }
            let postWorkoutInstructions
            if (!_.isEmpty(coolDown) && !_.isEmpty(coolDown.movements)) {
                const coolDownMovementsArray = _.map(coolDown.movements, movement => movement.title)
                postWorkoutInstructions = {
                    title: "COOL DOWN",
                    description: coolDownMovementsArray.join(", ")
                }
            }
            let index = 0
            const mainMovements: MovementsInfo[] = wod.parts.map(part => {
                if ((part.partType === "MAIN WORKOUT" || part.partType === "MAIN") && !_.isEmpty(part.movements)) {
                    index++
                    return this.getWodMainPart(part.movements, `MAIN MOVEMENT ${index}`)
                } else if (part.partType === "FINISHER" && !_.isEmpty(part.movements)) {
                    return this.getWodMainPart(part.movements, `FINISHER`)
                }
            })

            let movementsInfo = _.filter(mainMovements, movement => !_.isEmpty(movement))
            if (_.isEmpty(movementsInfo) && !_.isEmpty(wod.mainPart) && !_.isEmpty(wod.mainPart.movements)) {
                movementsInfo = [this.getWodMainPart(wod.mainPart.movements, `MAIN MOVEMENT 1`)]
            }
            if (!_.isEmpty(movementsInfo)) {
                return {
                    widgetType: "WOD_INFO_WIDGET",
                    title: "Workout of the day",
                    description: calorieText ? `${calorieText}` : undefined,
                    focusInfo,
                    preWorkoutInstructions,
                    postWorkoutInstructions,
                    movementsInfo,
                    hasDividerBelow: false
                }
            }
        }
    }
    static getWodMainPart(movements: DisplayMovement[], partTitle: string) {
        const workoutMovements: WorkoutMovement[] = []
        movements.forEach(movement => {
            if (movement) {
                const video = _.find(movement.media, { type: "VIDEO" })
                let action: Action
                if (video && video.url) {
                    action = {
                        actionType: "NAVIGATION",
                        url: `curefit://videoplayer?videoUrl=${encodeURIComponent(video.url)}&absoluteVideoUrl=${encodeURIComponent(CdnUtil.getCdnUrl(video.url))}`,
                        analyticsData: { widgetName: "SGT_WOD_VIDEO_PLAYED" }
                    }
                }
                workoutMovements.push({
                    title: movement.title,
                    action
                })
            }
        })
        return {
            title: partTitle,
            movements: workoutMovements
        }
    }

    static getSGTUpgradeCommUrl() {
        return "curefit://webview?uri=" + encodeURIComponent("https://s3.ap-south-1.amazonaws.com/vm-html-pages/Online_GX_Cult_Merged-c74d4984-9407-4f64-a6ee-9fdf3806eb2e.html")
    }

    static hasSGTOneStepBooking(userContext: UserContext, userAgent: UserAgent, isLiveSGTDoctorType: boolean): boolean {
        return userContext.sessionInfo.appVersion >= 8.59 && userAgent === "APP" && isLiveSGTDoctorType
    }


    static getStaticImageUrlForMap(coordinate: { latitude: number, longitude: number }): string {
        const scale = 2
        const mapWidth = 73
        const mapHeight = 73
        const centerMarkerIconUrlMedium = CdnUtil.getCdnUrl("curefit-content/image/icons/cult/pink_marker.png")
        const zoom = 8
        const centerMarker = `anchor:bottom|scale:1|icon:${centerMarkerIconUrlMedium}|${coordinate.latitude},${coordinate.longitude}`
        const style = convertMapStyleToStatic(
            CUSTOM_MAP_CULT_FIND_CENTER_STYLE_COLORED
        )
        return `https://maps.googleapis.com/maps/api/staticmap?size=${mapWidth}x${mapHeight}&zoom=${zoom}&markers=${centerMarker}&scale=${scale}&style=${style}&key=${Constants.getGoogleApiKey()}`
    }

    static getCultMindClpBrowsePackAction(productType: ProductType): string {
        if (productType === "FITNESS") {
            if (process.env.ENVIRONMENT === "LOCAL" || process.env.ENVIRONMENT === "STAGE") {
                return "curefit://tabpage?pageId=cult&selectedTab=CultAtCenter&widgetId=e9d099cf-2af4-419b-ad9e-8b1d7528523c"
            }
            return "curefit://tabpage?pageId=cult&selectedTab=CultAtCenter&widgetId=34b241ba-e841-4adf-87b0-79c584ff91d1"
        } else {
            return "curefit://tabpage?pageId=mind&selectedTab=MindAtCenter&widgetId=facda0ee-dc2e-4037-a05c-c67192dfa9b0"
        }
    }

    static isNewCenterDetailPageSupported(userContext: UserContext): boolean {
        return userContext.sessionInfo.appVersion >= CULT_NEW_CENTER_DETAILS_PAGE_VERSION
    }

    static isInviteBuddySupportedForWorkout(workout: CultWorkout): boolean {
        return !workout.isSportCategory
    }

    static getSkuNameForSkuId(skuId: number) {
        switch (skuId) {
            case 1: return SkuName.BLACK
            case 2: return SkuName.GOLD
            case 3: return SkuName.THIRD_PARTY_GYM
            case 6: return SkuName.LUX
        }
        return null
    }

    static isChildrensDayWorkout(workoutId: number): boolean {
        const environmentWorkoutId = process.env.APP_ENV === "PRODUCTION" ? CULT_CHILDRENS_DAY_PROD_WORKOUT_ID : CULT_CHILDRENS_DAY_STAGE_WORKOUT_ID
        return workoutId === environmentWorkoutId
    }

    static isCultRunWorkout(workoutId: number): boolean {
        const environmentWorkoutId = process.env.APP_ENV === "PRODUCTION" ? CULT_RUN_PROD_WORKOUT_ID : CULT_RUN_STAGE_WORKOUT_ID
        return workoutId === environmentWorkoutId
    }

    static async getCultCenterIdFromCenterService(centerId: number, centerService: ICenterService) {
        try {
            const centerServiceResponse: CenterResponse = await centerService.getCenterById(Number(centerId))
            return centerServiceResponse.meta && centerServiceResponse.meta.cultCenterId ? String(centerServiceResponse.meta.cultCenterId) : undefined
        } catch (err) {
            return undefined
        }
    }

    static async getCultCenterIdsFromCenterServiceIds(centerIds: string[], centerService: ICenterService) {
        const allowedCultCenterIdsPromises = centerIds.map(async (centerServiceId: string) => {return await CultUtil.getCultCenterIdFromCenterService(Number(centerServiceId), centerService)})
        let allowedCultCenterIds = await Promise.all(allowedCultCenterIdsPromises)
        allowedCultCenterIds = allowedCultCenterIds.filter(id => id)
        return allowedCultCenterIds
    }

    public static getPreviousMembershipFromMembershipList(userContext: UserContext, membershipList: Membership[]): Membership {
        const expiredMembershipList = _.filter(membershipList, (membership) => this.isMembershipExpired(userContext, membership))
        expiredMembershipList.sort((a, b) => {
           return a.end < b.end ? 1 : -1
        })
        return expiredMembershipList[0]
    }

    public static getCurrentMembershipFromMembershipList(userContext: UserContext, membershipList: Membership[]): Membership {
        return _.find(membershipList, (membership) => isMembershipCurrent(userContext, membership))
    }

    public static getUpcomingMembershipFromMembershipList(userContext: UserContext, membershipList: Membership[]): Membership {
        const upcomingMembershipList = _.filter(membershipList, (membership) => this.isMembershipUpcoming(userContext, membership))
        upcomingMembershipList.sort((a, b) => {
           return a.start < b.start ? -1 : 1
        })
        return upcomingMembershipList[0]
    }

    public static async getMembershipIdByCultMembershipId(userContext: UserContext, cultMembershipId: String, membershipService: IMembershipService): Promise<string> {
        const membershipList = (await eternalPromise(membershipService.getCachedMembershipsForUser(userContext.userProfile.userId, AppUtil.getTenantFromUserContext(userContext), ["CULT", "CULT_GYM"]))).obj
        const requiredMembership = _.find(membershipList, (membership) => {
            const currentKey = membership.metadata["membershipId"]
            return currentKey && currentKey == cultMembershipId
        })
        return (!_.isNil(requiredMembership)) ? requiredMembership.id.toString() : ""
    }

    public static async getMembershipByCultMembershipId(userContext: UserContext, cultMembershipId: String, membershipService: IMembershipService): Promise<Membership> {
        const membershipList = (await eternalPromise(membershipService.getCachedMembershipsForUser(userContext.userProfile.userId, AppUtil.getTenantFromUserContext(userContext), ["CULT", "CULT_GYM"]))).obj
        const requiredMembership = _.find(membershipList, (membership) => {
            const currentKey = membership.metadata["membershipId"]
            return currentKey && currentKey == cultMembershipId
        })
        return (!_.isNil(requiredMembership)) ? requiredMembership : null
    }

    static async isEligibleForTrial(userContext: UserContext, cacheHelper: CacheHelper) {
        const cultSummary = cacheHelper.getCultSummary(userContext.userProfile.userId)
        return (await cultSummary).trialEligibility.cult
    }

    static getCultClassState(cultClass: CultClass): ClassState {
        let state: ClassState = "AVAILABLE"
        const availableSeats = Math.min(cultClass.cultAppAvailableSeats, 2)
        if (cultClass.bookingNumber) {
            state = "BOOKED"
        } else if (cultClass.wlBookingNumber) {
            state = "WAITLISTED"
        } else if (CultUtil.isClassAvailableForWaitlist(cultClass)) {
            state = "WAITLIST_AVAILABLE"
        } else if (availableSeats <= 0 && cultClass.isWaitlistFull) {
            state = "WAITLIST_FULL"
        } else if (availableSeats <= 0) {
            state = "SEAT_NOT_AVAILABLE"
        } else if (cultClass.allowTrial === false && cultClass.amount === 0) {
            state = "SEAT_NOT_AVAILABLE"
        } else if (cultClass.allowPPC === false) {
            state = "SEAT_NOT_AVAILABLE"
        }
        return state
    }

    public static async getUserLocationPreference(preference: Preference, locality: CultLocality): Promise<LocationPreferenceRequestEntity> {
        const userLocationPreference: LocationPreferenceRequestEntity = {
            prefLocationType: undefined
        }
        if (preference.currentSelection == "area" && locality) {
            userLocationPreference.prefLocationType = LocationDataKey.LOCALITY
            userLocationPreference.locality = locality.name
        } else if (preference.currentSelection == "userLocation") {
            userLocationPreference.prefLocationType = LocationDataKey.CURRENT_LOC
        }
        return userLocationPreference
    }

    public static async getEliteMembershipsFromMembershipService(userContext: UserContext, membershipService: IMembershipService): Promise<Membership[]> {
        const memberships: Membership[] = (await eternalPromise(membershipService.getCachedMembershipsForUser(userContext.userProfile.userId, AppUtil.getTenantFromUserContext(userContext), ["CULT"], ["PAUSED", "PURCHASED"]))).obj
        if (_.isEmpty(memberships)) {
            return memberships
        }
        return memberships.filter(membership => membership.productId.startsWith("CULTPACK"))
    }

    public static async getMembershipsWithCultBenefitFromMembershipService(userContext: UserContext, membershipService: IMembershipService): Promise<Membership[]> {
        const memberships: Membership[] = (await eternalPromise(membershipService.getCachedMembershipsForUser(userContext.userProfile.userId, AppUtil.getTenantFromUserContext(userContext), ["CULT", "GYMFIT_GA"], ["PAUSED", "PURCHASED"]))).obj
        if (_.isEmpty(memberships)) {
            return memberships
        }
        return memberships.filter(membership => membership.productId.startsWith("CULTPACK") || membership.productId.startsWith("LUXPACK") || membership.productId.startsWith("GYMFIT"))
    }

    public static getBuyCultPassEliteAction(title: string, centerLevelRenewalEnabled?: boolean): Action {
        const cultPassTabWidgetID = process.env.ENVIRONMENT === "PRODUCTION" || process.env.ENVIRONMENT === "ALPHA" ? "0c77be10-b296-4a7d-85af-79f7144282ed" : "11993f83-a7df-4a29-8571-e0951d98de87"
        const cultPackBrowseActionUrl = centerLevelRenewalEnabled ? "curefit://fl_listpage?pageId=select_renewal&hideTitle=true" : `curefit://listpage?pageId=SKUPurchasePage&selectedTab=black&scrollToWidgetId=${cultPassTabWidgetID}&widgetId=${cultPassTabWidgetID}`
        return {
            actionType: "NAVIGATION",
            title: title,
            url: cultPackBrowseActionUrl,
        }
    }


    static async getCreateCalendarEventAction(userContext: UserContext, cultBooking: CultBooking, hamletService: HamletBusiness): Promise<Action> {
        const timezone = userContext.userProfile.timezone || TimeUtil.IST_TIMEZONE
        const sessionInfo = userContext.sessionInfo
        const cultClass = cultBooking.Class
        const cultCenter = cultBooking.Center
        const alarmTime = { date: sessionInfo.osName.toLowerCase() === "ios" ? -30 : 30 }
        // since flutter event integration for calendar takes epoch time and react native flow takes iso string
        const isFlutterBookingPageEnabled = true
        const startDateFormatted = isFlutterBookingPageEnabled ? TimeUtil.getMomentForDateString(cultClass.date + " " + cultClass.startTime, timezone, TimeUtil.HH_MM_SS_DATE_FORMAT).valueOf() : TimeUtil.getMomentForDateString(cultClass.date + " " + cultClass.startTime, timezone, TimeUtil.HH_MM_SS_DATE_FORMAT).toISOString()
        const endDateFormatted = isFlutterBookingPageEnabled ? TimeUtil.getMomentForDateString(cultClass.date + " " + cultClass.endTime, timezone, TimeUtil.HH_MM_SS_DATE_FORMAT).valueOf() : TimeUtil.getMomentForDateString(cultClass.date + " " + cultClass.endTime, timezone, TimeUtil.HH_MM_SS_DATE_FORMAT).toISOString()
        return {
            title: "OK",
            actionType: "CREATE_CALENDAR_EVENT",
            payload: {
                showPromptAlways: false,
                calendarData: {
                    classId: cultClass.id.toString(),
                    title: cultClass.Workout.name,
                    location: `${cultCenter.name}\n${cultCenter.Address.addressLine1} ${cultCenter.Address.addressLine2}`,
                    startDate: startDateFormatted,
                    endDate: endDateFormatted,
                    toastText: "Class added to your calendar",
                    alarms: [alarmTime]
                },
                modalData: {
                    header: "INTRODUCING",
                    title: "Add class to your calendar",
                    subTitle: "Get reminders for this and all your future classes so you never miss a class again!",
                    actions: [
                        {
                            title: "ASK ME LATER"
                        },
                        {
                            title: "GIVE PERMISSION"
                        }
                    ]
                }
            }
        }
    }

    static getDeleteCalendarEventAction(userContext: UserContext, cultBooking: CultBooking): Action {
        const timezone = userContext.userProfile.timezone || TimeUtil.IST_TIMEZONE
        const cultClass = cultBooking.CultClass
        return {
            actionType: "DELETE_CALENDAR_EVENT",
            payload: {
                classId: cultClass.id.toString(),
                title: cultClass.Workout.name,
                startDate: TimeUtil.getMomentForDateString(cultClass.date + " " + cultClass.startTime, timezone, TimeUtil.HH_MM_SS_DATE_FORMAT).toISOString(),
                endDate: TimeUtil.getMomentForDateString(cultClass.date + " " + cultClass.endTime, timezone, TimeUtil.HH_MM_SS_DATE_FORMAT).toISOString(),
            }
        }
}

    public static async getActiveProMembership(userContext: UserContext, membershipService: IMembershipService): Promise<Membership> {
        const memberships: Membership[] = (await eternalPromise(membershipService.getCachedMembershipsForUser(userContext.userProfile.userId, AppUtil.getTenantFromUserContext(userContext), ["GYMFIT_GA", "GYMFIT_GX"], ["PAUSED", "PURCHASED"]))).obj
        if (_.isEmpty(memberships)) {
            return null
        }
        return CultUtil.getCurrentMembershipFromMembershipList(userContext, memberships)
    }

    public static async getActiveCultSelectMembership(userContext: UserContext, membershipService: IMembershipService): Promise<Membership> {
        const memberships: Membership[] = (await eternalPromise(membershipService.getCachedMembershipsForUser(userContext.userProfile.userId, AppUtil.getTenantFromUserContext(userContext), ["GYMFIT_GA", "GYMFIT_GX", "CULT"], ["PAUSED", "PURCHASED"]))).obj
        const _filteredMemberships: Membership[] = memberships.filter(m => m.attributes.findIndex(a => a.attrKey == AttributeKeyType.ACCESS_CENTER.toString()) != -1)
        if (_.isEmpty(_filteredMemberships)) {
            return null
        }
        return CultUtil.getCurrentMembershipFromMembershipList(userContext, _filteredMemberships)
    }

    public static async getActiveCultSelectMembershipV2(userContext: UserContext, membershipService: IMembershipService): Promise<Membership> {
        const memberships: Membership[] = (await eternalPromise(membershipService.getCachedMembershipsForUser(userContext.userProfile.userId, AppUtil.getTenantFromUserContext(userContext), ["GYMFIT_GA", "GYMFIT_GX", "CULT"], ["PAUSED", "PURCHASED"]))).obj
        const _filteredMemberships: Membership[] = memberships.filter(m => m.attributes.findIndex(a => a.attrKey == "accessCenterIds") != -1)
        if (_.isEmpty(_filteredMemberships)) {
            return null
        }
        return CultUtil.getCurrentMembershipFromMembershipList(userContext, _filteredMemberships)
    }

    public static async getActiveCreditBasedMembership(userContext: UserContext, membershipService: IMembershipService): Promise<Membership> {
        const memberships: Membership[] = (await eternalPromise(membershipService.getCachedMembershipsForUser(userContext.userProfile.userId, AppUtil.getTenantFromUserContext(userContext), ["GYMFIT_GA", "GYMFIT_GX", "CULT"], ["PAUSED", "PURCHASED"]))).obj
        if (_.isNil(memberships) || _.isEmpty(memberships)) {
            return null
        }
        const _filteredMemberships: Membership[] = memberships.filter(m => m.benefits.findIndex(benefit => benefit.name == "ACCESS_CREDITS") != -1)
        if (_.isEmpty(_filteredMemberships)) {
            return null
        }
        return CultUtil.getCurrentMembershipFromMembershipList(userContext, _filteredMemberships)
    }

    public static async getActiveLimitedEliteMembership(userContext: UserContext, membershipService: IMembershipService): Promise<Membership> {
        const memberships: Membership[] = (await eternalPromise(membershipService.getCachedMembershipsForUser(userContext.userProfile.userId, AppUtil.getTenantFromUserContext(userContext), ["CULT"], ["PAUSED", "PURCHASED"]))).obj
        const _filteredMemberships: Membership[] = memberships.filter(m => m.metadata?.limitedSessions === true || m.metadata?.isCultpassX === true)
        if (_.isEmpty(_filteredMemberships)) {
            return null
        }
        return CultUtil.getCurrentMembershipFromMembershipList(userContext, _filteredMemberships)
    }

    public static async isOnlyOneUpcomingBooking(userContext: UserContext, cultFitService: ICultService): Promise<boolean> {
        const upcomingBookings = await cultFitService.getUpcomingBookingsFromCache(Number(userContext.userProfile.userId))
        const bookingCount = upcomingBookings.bookings.length + upcomingBookings.waitLists.length
        return (bookingCount === 1)
    }

    public static getLimitedEliteSessionLeftText(limitedEliteMembership: Membership, userContext: UserContext) {
        const isAwayCity = limitedEliteMembership.metadata.cityId !== userContext.userProfile.cityId
        const cultBenefit = limitedEliteMembership.benefits.find(a => a.name === (isAwayCity ? "CULT_AWAY" : "CULT"))
        const ticketsRemaining = cultBenefit.maxTickets - cultBenefit.ticketsUsed
        const sessionsLeftText = ticketsRemaining <= 0 ? `No sessions left this month!` : `${ticketsRemaining} of ${cultBenefit.maxTickets} sessions left this month`
        return sessionsLeftText
    }

    public static async getSelectCenterNameForMembership(userContext: UserContext, membership: Membership, centerService: ICenterService) {
        let selectCenterName
        const centerIds: string[] = []
        membership.attributes.forEach((a) => {
            if (a.attrKey == AttributeKeyType.ACCESS_CENTER) {
                centerIds.push(a.attrValue)
            }
        })
        let gymName
        let cultCenterName
        for (const centerId of centerIds) {
                const center: CenterResponse = await centerService?.getCenterById(Number(centerId))
                if (center != null && center.vertical == CenterVertical.CULT) {
                 cultCenterName = center.name
                } else {
                 gymName = center != null ? center.name : ""
                }
        }
        selectCenterName = cultCenterName != null ? cultCenterName : gymName
        return selectCenterName
    }

    public static async getSelectCenterIdForMembership(userContext: UserContext, membership: Membership, centerService: ICenterService) {
        let selectCenterId
        const centerIds: string[] = []
        membership.attributes.forEach((a) => {
            if (a.attrKey == AttributeKeyType.ACCESS_CENTER) {
                centerIds.push(a.attrValue)
            }
        })
        for (const centerId of centerIds) {
                const center: CenterResponse = await centerService?.getCenterById(Number(centerId))
                if (center.vertical == CenterVertical.CULT) {
                    selectCenterId = center.id
                }
        }
        return selectCenterId
    }

    public static getRecommendationBanner(level: number) {
        return new BannerCarouselWidget("335:60", [{
            id: "recommendation_banner",
            image: (level === 1) ? "/image/icons/livept/cross_gender_banner_female.png" : "/image/icons/livept/cross_gender_banner_male.png",
            action: ""
        }], {
            bannerWidth: 335,
            bannerHeight: 49,
            backgroundColor: undefined
        }, 1)
    }

    public static async getTicketBanner(membership: Membership, userContext: UserContext, isSelectMembership: boolean): Promise<BannerCarouselWidget> {
        const banners: Banner[] = []
        let id: string
        let url: string
        if (isSelectMembership) {
            const centerAwayBenefit = membership.benefits.find(benefit => benefit.name == "CENTER_AWAY")
            if (centerAwayBenefit != null) {
                if (centerAwayBenefit.maxTickets === centerAwayBenefit.ticketsUsed) {
                    id = centerAwayBenefit.maxTickets.toString()
                    url = ALL_CULT_BENEFIT_TICKET_EXHAUSTED
                } else {
                    id = "0"
                    url = HAVE_REMAINING_CULT_BENEFIT_TICKET
                }
            }
        } else {
            const membershipCity: string = membership.metadata["cityId"]
            if (membershipCity === userContext.userProfile.cityId) {
                const cultBenefit = membership.benefits.find(benefit => benefit.name == "CULT")
                if (cultBenefit != null) {
                    if (cultBenefit.maxTickets === cultBenefit.ticketsUsed) {
                        id = cultBenefit.maxTickets.toString()
                        url = ALL_CULT_BENEFIT_TICKET_EXHAUSTED
                    } else {
                        id = "0"
                        url = HAVE_REMAINING_CULT_BENEFIT_TICKET
                    }
                }
            } else {
                const cultAwayBenefit = membership.benefits.find(benefit => benefit.name == "CULT_AWAY")
                if (cultAwayBenefit != null) {
                    if (cultAwayBenefit.maxTickets === cultAwayBenefit.ticketsUsed) {
                        id = cultAwayBenefit.maxTickets.toString()
                        url = ALL_CULT_BENEFIT_TICKET_EXHAUSTED
                    } else {
                        id = "0"
                        url = HAVE_REMAINING_CULT_BENEFIT_TICKET
                    }
                }
            }
        }
        if (_.isNil(url)) return null
        banners.push({
            id: id,
            image: url,
            action: undefined
        })

        return {
            ...new BannerCarouselWidget("345:80", banners, {
            bannerWidth: 345,
            bannerHeight: 80,
            verticalPadding: 0,
            edgeToEdge: false,
            noAutoPlay: false,
            v2: true,
            autoScroll: false,
            showPagination: false,
            }, 1, true)
        }
    }
    public static async isTransformAddOnSupported(userContext: UserContext, hamletBusiness: HamletBusiness) {
        const { appVersion } = userContext.sessionInfo
        const experimentId = process.env.ENVIRONMENT === "STAGE" ? CULT_TRANSFORM_ADD_ON_STAGE_EXPERIMENT_ID : CULT_TRANSFORM_ADD_ON_PROD_EXPERIMENT_ID
        const isExperimentEnabled: boolean = await AppUtil.isExperimentEnabled(userContext, hamletBusiness, experimentId.toString(), "1")
        if (AppUtil.isWeb(userContext)) {
            return isExperimentEnabled
        }
        return isExperimentEnabled && appVersion >= CULT_TRANSFORM_ADD_ON_VERSION
    }

    public static async doesUserBelongToOnboardingAutobooking(userContext: UserContext, hamletBusiness: HamletBusiness, segmentService: ISegmentService) {
        const { appVersion } = userContext.sessionInfo
        const doesUserBelongToOnboardingSegment = await segmentService.doesUserBelongToSegment(NEW_USER_ONBOARDING_FORM_SEGMENT_ID, userContext)
        const isExperimentEnabled: boolean = (doesUserBelongToOnboardingSegment && appVersion >= GOAL_SETTING_MIN_APP_VERSION)
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        return isExperimentEnabled
    }

    public static async doesUserBelongToHabitGameSegment(userContext: UserContext, segmentService: ISegmentService) {
        const { appVersion } = userContext.sessionInfo
        const doesUserBelongToHabitGameSegment = await segmentService.doesUserBelongToAnySegment([MULTIPLAYER_HABIT_GAME_SEGMENT, HABIT_GAME_SEGMENT], userContext)
        const isExperimentEnabled: boolean = (doesUserBelongToHabitGameSegment && appVersion >= HABIT_GAME_WIDGET_ENABLE)
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        return isExperimentEnabled
    }

    public static async getUserRecommendationLevel(userId: string, userAttributeClient: IUserAttributeClient): Promise<number> {
        const response = await userAttributeClient.getCachedUserAttributes(
            Number(userId),
            ONBOARDING_RECOMMENDED_LEVEL_ATTRIBUTE
        )
        const attributes: any = response.attributes
        let value: any
        for (const key in attributes) {
            value = attributes[key]
        }
        return Number(value)
    }

    public static getWorkoutRecommendationLevel(workoutName: string) {
        workoutName = workoutName.toLowerCase()
        if (workoutName.includes("yoga") || workoutName.includes("hrx") || workoutName.includes("aqua")) {
            return 1
        } else if (workoutName.includes("s&c") || workoutName.includes("dance") || workoutName.includes("run")
            || workoutName.includes("strength+") || workoutName.includes("cycling") || workoutName.includes("crossfit")) {
            return 2
        } else if (workoutName.includes("burn") || workoutName.includes("boxing")) {
            return 3
        }
        return 4
    }

    public static getRecommendationMatchWidget(recommended: boolean): (WidgetView | PageWidget) {
        return  {
            "widgetType": "CLASS_INTENSITY_INFO_WIDGET",
            "hasDivideBelow": false,
            "layoutProps": {
                "spacing": {"top": "0", "bottom": "20"}
            },
            "title": recommended ? "Perfect Match" : "Intense Workout",
            "titleStyle": "H2",
            "titleColor": recommended ? "#FFFFFF" : "#FF5942",
            "textStyle": "P2",
            "iconAlignment": "right",
            "itemSpacing": 10.0,
            "items": [
                {
                    "title": recommended ?
                        "Our experts recommend this as one of the best workouts to start with!"
                        : "This is a high intensity workout. Our experts recommend this workout after some experience.",
                    "width": 40,
                    "height": 40,
                    "lottieUrl": recommended ? "image/m1_experience/test/perfect.json" : "image/m1_experience/test/intense.json"
                }
            ],
        }
    }


    public static getEmployeeOnlyDisclaimerWidgets(text: string): any[] {
        return [{
            "widgetType": "RICH_TEXT_LIST_WIDGET",
            "hasDivideBelow": false,
            "layoutProps": {
                "spacing": {"top": "0", "bottom": "20"}
            },
            "textAlign": "center",
            "listItems": [
                [
                    {
                        "text": text
                    }
                ]
            ],
            "sideSpacing": 45.0,
            "maxLines": 6
        }]
    }

    public static getSneakPeakCollapsedWidgets(): any[] {
        return [{
            "widgetType": "CF_MEDIA_WIDGET",
            "hasDivideBelow": false,
            "mediaData": {
                "topPadding": 0,
                "bottomPadding": 4,
                "width": 30.0,
                "height": 40.0,
                "mediaUrl": "image/m1_experience/formats/sneakLottie2.json",
                "type": "lottie"
            }
        },
            {
                "widgetType": "RICH_TEXT_LIST_WIDGET",
                "hasDivideBelow": false,
                "layoutProps": {
                    "spacing": {"top": "0", "bottom": "20"}
                },
                "textAlign": "center",
                "listItems": [
                    [
                        {
                            "text":
                                "Swipe up to see sneak peak of format"
                        }
                    ]
                ],
                "sideSpacing": 45.0,
                "maxLines": 2
            }]
    }

    public static getSneakPeakExpandedWidgets(): any[] {
        return [
            {
                "widgetType": "FORMAT_DETAIL_WIDGET",
                "layoutProps": {
                    "spacing": {"top": "20", "bottom": "0"}
                },
                "videoUrl": "image/m1_experience/formats/Yoga.mp4",
                "title": "Yoga",
                "subtext": "50 mins workout",
                "description": "Cultivate flexibility, enhance mobility, and embrace relaxation with yoga, a holistic practice catering to both physical and mental well-being.",
                "action": {
                    "url": "curefit://classbookingv2?productType=FITNESS&workoutId=5",
                    "title": "BOOK NOW",
                    "actionType": "RESET_NAVIGATION",
                },
                "imageUrl": "image/m1_experience/formats/yoga_info1.png",
            },
            {
                "widgetType": "FORMAT_DETAIL_WIDGET",
                "layoutProps": {
                    "spacing": {"top": "20", "bottom": "0"}
                },
                "videoUrl": "image/m1_experience/formats/HRX.mp4",
                "title": "HRX",
                "subtext": "50 mins workout",
                "description": "Transform your body with a beginner-friendly strength training program, focusing on building lean muscle mass and improving overall strength for a stronger, healthier you.",
                "action": {
                    "url": "curefit://classbookingv2?productType=FITNESS&workoutId=69",
                    "title": "BOOK NOW",
                    "actionType": "RESET_NAVIGATION",
                },
                "imageUrl": "image/m1_experience/formats/hrx_info1.png",
            },
            {
                "widgetType": "FORMAT_DETAIL_WIDGET",
                "layoutProps": {
                    "spacing": {"top": "20", "bottom": "0"}
                },
                "videoUrl": "image/m1_experience/formats/Dance.mp4",
                "title": "Dance Fitness",
                "subtext": "50 mins workout",
                "description": "Energize your workouts with dynamic dance routines, a perfect blend of cardio and fun, making cardio an enjoyable journey.",
                "action": {
                    "url": "curefit://classbookingv2?productType=FITNESS&workoutId=56",
                    "title": "BOOK NOW",
                    "actionType": "RESET_NAVIGATION",
                },
                "imageUrl": "image/m1_experience/formats/dance_info1.png",
            },
            {
                "widgetType": "FORMAT_DETAIL_WIDGET",
                "layoutProps": {
                    "spacing": {"top": "20", "bottom": "0"}
                },
                "videoUrl": "image/m1_experience/formats/SNC.mp4",
                "title": "S&C",
                "subtext": "50 mins workout",
                "description": "Unleash your strength potential with high-intensity, functional workouts designed to sculpt your body, providing a challenging and empowering fitness experience.",
                "action": {
                    "url": "curefit://classbookingv2?productType=FITNESS&workoutId=69",
                    "title": "BOOK NOW",
                    "actionType": "RESET_NAVIGATION",
                },
                "imageUrl": "image/m1_experience/formats/snc_info1.png",
            },
            {
                "widgetType": "FORMAT_DETAIL_WIDGET",
                "layoutProps": {
                    "spacing": {"top": "20", "bottom": "0"}
                },
                "videoUrl": "image/m1_experience/formats/Boxing.mp4",
                "title": "Boxing",
                "subtext": "50 mins workout",
                "description": "Punch your way to improved upper body endurance and cardio fitness, enjoying a novel workout experience that packs a powerful punch.",
                "action": {
                    "url": "curefit://classbookingv2?productType=FITNESS&workoutId=8",
                    "title": "BOOK NOW",
                    "actionType": "RESET_NAVIGATION",
                },
                "imageUrl": "image/m1_experience/formats/boxing_info1.png",
            },
            {
                "widgetType": "FORMAT_DETAIL_WIDGET",
                "layoutProps": {
                    "spacing": {"top": "20", "bottom": "0"}
                },
                "videoUrl": "image/m1_experience/formats/Burn.mp4",
                "title": "Burn",
                "subtext": "50 mins workout",
                "description": "Ignite your calorie burn with intense cardio workouts and pushing your limits for a quick and effective fitness transformation.",
                "action": {
                    "url": "curefit://classbookingv2?productType=FITNESS&workoutId=66",
                    "title": "BOOK NOW",
                    "actionType": "RESET_NAVIGATION",
                },
                "imageUrl": "image/m1_experience/formats/burn_info1.png",
            }]
    }

    public static async doesUserBelongToAccPartnerExp(userContext: UserContext, serviceInterfaces: CFServiceInterfaces) {
        const { appVersion } = userContext.sessionInfo
        const isExperimentEnabledForAccPartner: boolean = await AppUtil.isExperimentEnabled(userContext, serviceInterfaces.hamletBusiness, ACCOUNTABILITY_PARTNER_PROD_EXPERIMENT_ID.toString(), ACCOUNTABILITY_PARTNER_PROD_BUCKET_ID)
        const isExperimentEnabled: boolean = ((isExperimentEnabledForAccPartner) && appVersion >= ACCOUNTABILITY_PARTNER_MIN_APP_VERSION)
        const userForm = await serviceInterfaces.formService.getLastCompletedUserForm("post_pack_purchase_onboarding_v2", userContext.userProfile.userId)
        const userFormOld = await serviceInterfaces.formService.getLastCompletedUserForm("post_pack_purchase_onboarding", userContext.userProfile.userId)
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        return isExperimentEnabled && (userForm == null || _.isEmpty(userForm))  && (userFormOld == null || _.isEmpty(userFormOld))
    }


    public static async  doesUserBelongToDeadBaseAlumniPassElitePlaySegment(userContext: UserContext, serviceInterfaces: CFServiceInterfaces) {
        const { appVersion } = userContext.sessionInfo
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        userContext.userProfile.promiseMapCache ??= new PromiseCache(serviceInterfaces)
        const doesUserBelongTo_HYD_Segment = await serviceInterfaces.segmentService.doesUserBelongToSegment("2154b780-9291-4824-b402-fceb688b3e35", userContext)
        const doesUserBelongTo_NCR_Segment = await serviceInterfaces.segmentService.doesUserBelongToSegment("fd996b04-34b0-452f-b64c-46635eaa3273", userContext)
        const doesUserBelongTo_BLR_Segment = await serviceInterfaces.segmentService.doesUserBelongToSegment("0f4120cd-861d-45d8-8bcb-787a13d548b7", userContext)
        return doesUserBelongTo_HYD_Segment || doesUserBelongTo_NCR_Segment || doesUserBelongTo_BLR_Segment
    }

    public static async doesUserBelongToDeadBaseAlumniPassEliteNonPlaySegment(userContext: UserContext, serviceInterfaces: CFServiceInterfaces) {
        const { appVersion } = userContext.sessionInfo
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        userContext.userProfile.promiseMapCache ??= new PromiseCache(serviceInterfaces)
        const doesUserBelongTo_NonPlay_Segment = await serviceInterfaces.segmentService.doesUserBelongToSegment("4b41b8f0-1a6d-4bac-a348-42e1d8dc9b9f", userContext)
        return doesUserBelongTo_NonPlay_Segment
    }

    public static async doesUserBelongToDeadBaseRecapReportSegment(userContext: UserContext, serviceInterfaces: CFServiceInterfaces) {
        const { appVersion } = userContext.sessionInfo
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        userContext.userProfile.promiseMapCache ??= new PromiseCache(serviceInterfaces)
        const doesUserBelongTo_DeadBaseRecapReport_Segment = await serviceInterfaces.segmentService.doesUserBelongToSegment("e42d2a30-8b19-4226-a635-610f286ae058", userContext)
        serviceInterfaces.logger.info("userYearReport2022Business check segment in cond ::: ", doesUserBelongTo_DeadBaseRecapReport_Segment)
        return doesUserBelongTo_DeadBaseRecapReport_Segment
    }

    public static async isUserEligibleForMiniGoal(userContext: UserContext, serviceInterfaces: CFServiceInterfaces, workoutID: number) {
        const { appVersion } = userContext.sessionInfo
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        const allowedWorkouts = [22]
        userContext.userProfile.promiseMapCache ??= new PromiseCache(serviceInterfaces)
        const isUserEligibleForMiniGoal_Segment = await serviceInterfaces.segmentService.doesUserBelongToSegment("1ba843e3-ffc7-40f0-8b57-bc10661d1143", userContext)
        serviceInterfaces.logger.debug("isUserEligibleForMiniGoal check segment in cond ::: ", isUserEligibleForMiniGoal_Segment)
        return isUserEligibleForMiniGoal_Segment && allowedWorkouts.includes(workoutID)
    }

    public static async isUserEligibleForMiniGoalBanner(userContext: UserContext, serviceInterfaces: CFServiceInterfaces, workoutID: number) {
        const { appVersion } = userContext.sessionInfo
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        const allowedWorkouts = [22]
        userContext.userProfile.promiseMapCache ??= new PromiseCache(serviceInterfaces)
        const isUserEligibleForMiniGoalBanner_Segment = await serviceInterfaces.segmentService.doesUserBelongToSegment("f270bd10-70ed-478f-84f8-4470d65ef184", userContext)
        serviceInterfaces.logger.debug("isUserEligibleForMiniGoalBanner check segment in cond ::: ", isUserEligibleForMiniGoalBanner_Segment)
        return isUserEligibleForMiniGoalBanner_Segment && allowedWorkouts.includes(workoutID)
    }

    public static async isUserEligibleForGXLogging(userContext: UserContext, serviceInterfaces: CFServiceInterfaces) {
        const { appVersion } = userContext.sessionInfo
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        userContext.userProfile.promiseMapCache ??= new PromiseCache(serviceInterfaces)
        const isUserEligibleForGXLogging_Segment = await serviceInterfaces.segmentService.doesUserBelongToSegment("46bfb211-af89-4da3-8855-58c3846f8ffd", userContext)
        serviceInterfaces.logger.debug("isUserEligibleForGXLogging check segment in cond ::: ", isUserEligibleForGXLogging_Segment)
        return isUserEligibleForGXLogging_Segment || appVersion >= 10.43
    }

    public static async shouldEnableGymLoggingForUserSegment(userContext: UserContext, serviceInterfaces: CFServiceInterfaces) {
        const { appVersion } = userContext.sessionInfo
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        userContext.userProfile.promiseMapCache ??= new PromiseCache(serviceInterfaces)
        const doesUserBelongTo_GymLogging_Segment = await serviceInterfaces.segmentService.doesUserBelongToSegment("5e5da684-fe8d-46b2-9039-27d377420a54", userContext)
        serviceInterfaces.logger.info("shouldEnableGymLoggingForUserSegment check segment in cond ::: ", doesUserBelongTo_GymLogging_Segment)
        return doesUserBelongTo_GymLogging_Segment
    }

    public static async shouldEnableGymLoggingForUserExperiment(userContext: UserContext, hamletBusiness: HamletBusiness) {
        const { appVersion } = userContext.sessionInfo
        const experimentId = "1672"
        const isExperimentEnabled: boolean = await AppUtil.isExperimentEnabled(userContext, hamletBusiness, experimentId, "2")
        return appVersion >= 10.17
    }

    public static async getCenterServiceIdFromCultCenterId(cultCenterId: number, centerService: ICenterService) {
        const centerServiceResponse = await centerService.getCentersByExternalIds(null, [cultCenterId])
        return centerServiceResponse?.cultCenters?.[0]?.id
    }

    public static async getCenterServiceIdsFromCultCenterIds(cultCenterIds: number[], centerService: ICenterService) {
        const centerServiceResponse = await centerService.getCentersByExternalIds(null, cultCenterIds)
        return centerServiceResponse?.cultCenters && centerServiceResponse.cultCenters.length > 0 ? centerServiceResponse.cultCenters.map(center => center.id) : []
    }

    public static cdnImageUrl(imageUrl: string): string {
        if (!imageUrl) {
            return imageUrl
        }

        if (imageUrl.startsWith("curefit-content/hercules")) {
            return imageUrl.replace("curefit-content/", "")
        } else if (imageUrl.startsWith("cult-media/assets/")) {
            return CultUtil.getCdnUrl(imageUrl)
        } else if (imageUrl.startsWith("cult-media/")) {
            return imageUrl
        }

        return CultUtil.getCdnUrl(imageUrl)
    }

    public static getCdnUrl(url: string): string {
        if (!url) {
            return url
        }

        if (url.startsWith("curefit-content/")) {
            return url.replace("curefit-content/", CONTENT_CDN_BASE_PATH)
        }

        const imageExtensionMatcher = imageExtensionPattern.test(url)
        if (url.startsWith("cult-media/assets/")) {
            return url.replace(
                "cult-media/assets/",
                imageExtensionMatcher ? CULT_MEDIA_IMAGE_CDN_BASE_PATH : CULT_MEDIA_VIDEO_CDN_BASE_PATH
            )
        }

        if (url.startsWith("cult-media/")) {
            return url.replace("cult-media/", CULT_MEDIA_CDN_BASE_PATH_DEPRECATED)
        }

        if (url.startsWith("cult-media-staging/")) {
            return url.replace("cult-media-staging/", CULT_MEDIA_STAGING_CDN_BASE_PATH)
        }

        return url
    }

}

export function transformCultSummary(cultSummary: CultSummary, vertical: Vertical): CultOrMindSummary {
    if (vertical === "CULT_FIT") {
        return {
            currentMembership: cultSummary.membershipSummary.current.cult,
            previousMembership: cultSummary.membershipSummary.previous.cult,
            upcomingMembership: cultSummary.membershipSummary.upcoming.cult,
            trialEligibility: cultSummary.trialEligibility.cult && _.isNil(cultSummary.membershipSummary.current.cult)
                && _.isNil(cultSummary.membershipSummary.previous.cult)
                && _.isNil(cultSummary.membershipSummary.upcoming.cult),
            complimentaryAccessList: cultSummary.complimentaryAccessList
        }
    } else if (vertical === "MIND_FIT") {
        return {
            currentMembership: cultSummary.membershipSummary.current.mind,
            previousMembership: cultSummary.membershipSummary.previous.mind,
            upcomingMembership: cultSummary.membershipSummary.upcoming.mind,
            trialEligibility: cultSummary.trialEligibility.mind && _.isNil(cultSummary.membershipSummary.current.mind)
                && _.isNil(cultSummary.membershipSummary.previous.mind)
                && _.isNil(cultSummary.membershipSummary.upcoming.mind),
            complimentaryAccessList: cultSummary.complimentaryAccessList
        }
    } else {
        return undefined
    }

}

export function transformCultSummaryMap(summaryMap: { [userId: string]: CultSummary }, vertical: Vertical): { [userId: string]: CultOrMindSummary } {
    const transformedSummaryMap: { [userId: string]: CultOrMindSummary } = {}
    if (summaryMap) {
        _.forEach(Object.keys(summaryMap), key => {
            transformedSummaryMap[key] = transformCultSummary(summaryMap[key], vertical)
        })
    }
    return transformedSummaryMap
}

export function isActiveOrFutureMembershipPresent(userContext: UserContext, membershipList: Membership[]): boolean {
    return _.some(membershipList, (membership) => !CultUtil.isMembershipExpired(userContext, membership))
}

export const isActiveOrFuturePassAccessPresent = (cultOrMindSummary: CultOrMindSummary, userContext: UserContext): boolean => {
    const tz = userContext.userProfile.timezone
    const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDateWithTimezone(tz), tz)
    let activePass = 0
    cultOrMindSummary.complimentaryAccessList.access.every((access) => {
        if (activePass > 0) {
            return false
        }
        const diffInDays = today.diff(access.endDate, "days")
        if (diffInDays < 0) {
            activePass++
        }
        return true
    })
    return activePass > 0
}

export function isUpForRenewal(userContext: UserContext, previousMembership: Membership, currentMembership: Membership, upcomingMembership: Membership): boolean {
    const tz = userContext.userProfile.timezone
    if (!_.isNil(upcomingMembership)) {
        return false
    }
    const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDateWithTimezone(tz), tz)

    if (!_.isNil(currentMembership)) {
        const diffInDays = today.diff(TimeUtil.formatEpochInTimeZone(tz, currentMembership.end), "days")
        if (diffInDays >= -14 && diffInDays <= 30) {
            return true
        }
    }

    if (!_.isNil(previousMembership)) {
        const diffInDays = today.diff(TimeUtil.formatEpochInTimeZone(tz, previousMembership.end), "days")
        if (diffInDays >= -14 && diffInDays <= 30) {
            return true
        }
    }
}

export interface CultOrMindSummary {
    currentMembership: MembershipDetails
    previousMembership: MembershipDetails
    upcomingMembership: MembershipDetails
    trialEligibility: boolean
    complimentaryAccessList: ComplimentaryAccessList
}

function getActiveMembershipDays(userContext: UserContext, currentMembership: Membership, upcomingMembership: Membership): number {
    let activeDays: number = 0
    const tz = userContext.userProfile.timezone
    if (!_.isNil(currentMembership)) {
        const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDateWithTimezone(tz), tz)
        const daysLeft = TimeUtil.getMomentForDateString(TimeUtil.formatEpochInTimeZone(tz, currentMembership.end), tz).diff(today, "days")
        activeDays += daysLeft > 0 ? daysLeft : 0
    }
    if (!_.isNil(upcomingMembership)) {
        const packDuration = TimeUtil.getMomentForDateString(TimeUtil.formatEpochInTimeZone(tz, upcomingMembership.end), tz).diff(TimeUtil.formatEpochInTimeZone(tz, upcomingMembership.start), "days")
        activeDays += packDuration > 0 ? packDuration : 0
    }
    return activeDays
}

export function getCultCafeWidget(cafeEatClpUrl: string, hasTopDivider?: boolean, hasDividerBelow?: boolean, cultCafeCatalogType?: CafeCatalogType, confirmedBooking?: CultBooking): CultCafeWidget {
    if (_.isNil(cafeEatClpUrl)) {
        return
    }
    const action: Action = {
        actionType: "NAVIGATION",
        title: "ORDER",
        url: cafeEatClpUrl
    }
    return {
        widgetType: "CULT_CAFE_WIDGET",
        title: "Pre order workout gear", // CultUtil.getCultCafeCTAText(cultCafeCatalogType),
        subTitle: undefined,
        action: action,
        hasTopDivider,
        hasDividerBelow,
        analyticsData: CultUtil.getCultCafeBlockAnalytics(confirmedBooking)
    }
}

export function getNewCultCafeWidget(cafeEatClpUrl: string, hasTopDivider?: boolean, hasDividerBelow?: boolean, confirmedBooking?: CultBooking): ActionCardWidget {
    if (_.isNil(cafeEatClpUrl)) {
        return
    }
    const action: Action = {
        actionType: "NAVIGATION",
        title: "ORDER",
        url: cafeEatClpUrl
    }

    return {
        widgetType: "ACTION_CARD_WIDGET",
        displayText: "Pre order workout gear",
        icon: {
            iconType: "IMAGE_URL",
            url: "/image/icons/cult/cafe_order.png"
        },
        action: action,
        hasDividerAbove: hasTopDivider,
        hasDividerBelow: hasDividerBelow,
        analyticsData: CultUtil.getCultCafeBlockAnalytics(confirmedBooking)
    }
}

export function getCafeEatClpUrl(userContext: UserContext, kiosksDemandService: KiosksDemandService, cultBooking: CultBooking, timeZone: Timezone) {
    const kioskId = kiosksDemandService.getKioskIdGivenCenterId(cultBooking.CultClass.centerID.toString())
    const cityId = kiosksDemandService.getKioskCityId(kioskId)
    if (cityId === DUBAI_ACTUAL_CITY_ID && !AppUtil.isEatCurrencySupported(userContext)) {
        return undefined
    } else {
        return ActionUtil.getEatClpUrl(kioskId, cultBooking, timeZone)
    }
}

export async function getCultUserTypes(userContext: UserContext, cultSummary: CultSummary, membershipList: Membership[]): Promise<CultUserType[]> {
    const cultOrMindSummary: CultOrMindSummary = transformCultSummary(cultSummary, "CULT_FIT")
    const cultUserTypes: CultUserType[] = ["ALL"]
    const tz = userContext.userProfile.timezone
    const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDateWithTimezone(tz), tz)

    const currentMembership = CultUtil.getCurrentMembershipFromMembershipList(userContext, membershipList)
    const previousMembership = CultUtil.getPreviousMembershipFromMembershipList(userContext, membershipList)
    const upcomingMembership = CultUtil.getUpcomingMembershipFromMembershipList(userContext, membershipList)

    const upForRenewal = isUpForRenewal(userContext, previousMembership, currentMembership, upcomingMembership)
    const isActiveOrFutureMembershipAvailable = isActiveOrFutureMembershipPresent(userContext, membershipList)
    const isActiveOrFuturePassAccessAvailable = isActiveOrFuturePassAccessPresent(cultOrMindSummary, userContext)

    const activeMembershipDays = getActiveMembershipDays(userContext, currentMembership, upcomingMembership)
    // Added check for user, that he is part of pack (active more than 90 days)
    if (activeMembershipDays > 90) {
        cultUserTypes.push("90_DAYS_MEMBERSHIP_REMAINING")
    } else {
        cultUserTypes.push("NOT_90_DAYS_MEMBERSHIP_REMAINING")
    }

    // Added check for user, that he is part of pack (active more than 60 days)
    if (activeMembershipDays > 60) {
        cultUserTypes.push("60_DAYS_MEMBERSHIP_REMAINING")
    } else {
        cultUserTypes.push("NOT_60_DAYS_MEMBERSHIP_REMAINING")
    }

    // Added check for user, that he is part of pack (active more than 30 days)
    if (activeMembershipDays > 30) {
        cultUserTypes.push("30_DAYS_MEMBERSHIP_REMAINING")
    } else {
        cultUserTypes.push("NOT_30_DAYS_MEMBERSHIP_REMAINING")
    }

    if (_.isNil(upcomingMembership) && _.isNil(currentMembership) && !_.isNil(previousMembership) && today.diff(TimeUtil.formatEpochInTimeZone(tz, previousMembership.end), "days") < 15) {
        cultUserTypes.push("EXPIRED_MEMBER")
    }
    if (_.isNil(upcomingMembership) && !_.isNil(currentMembership) && today.diff(TimeUtil.formatEpochInTimeZone(tz, currentMembership.end), "days") > -15) {
        cultUserTypes.push("EXPIRING_MEMBER")
    }

    if (upForRenewal) {
        cultUserTypes.push("UP_FOR_RENEWAL")
    }
    if (isActiveOrFutureMembershipAvailable || isActiveOrFuturePassAccessAvailable) {
        cultUserTypes.push("WITH_PACK")
    } else {
        if (cultSummary.trialEligibility.cult) {
            cultUserTypes.push("TRIAL_CLASS_USER")
        }
        if (_.isNil(previousMembership) && _.isNil(currentMembership) && _.isNil(previousMembership)) {
            cultUserTypes.push("PRE_REGISTRATION_USER")
        }
        cultUserTypes.push("WITHOUT_PACK")
        if (!_.isNil(previousMembership)) {
            cultUserTypes.push("PAST_MEMBER")
        }
    }

    return cultUserTypes
}

export function getCultTrialUserTypes(userContext: UserContext, userTrialEligibility: UserTrialEligibility): CultTrialUserType[] {
    const cultTrialUserTypes: CultTrialUserType[] = ["ALL"]
    if (!userTrialEligibility.trialEligibility.isEligible) {
        if (userTrialEligibility.trialEligibility.consumptionCount === userTrialEligibility.trialEligibility.maxCount) {
            cultTrialUserTypes.push("ALL_TRIALS_CONSUMED")
        }
    } else if (userTrialEligibility.trialEligibility.consumptionCount > 0) {
        cultTrialUserTypes.push("SOME_TRIALS_CONSUMED")
    } else if (userTrialEligibility.trialEligibility.maxCount > 0) {
        cultTrialUserTypes.push("TRIALS_NOT_USED")
    }
    const bookedTrial = _.filter(userTrialEligibility.trialBookings, (trialBooking: CultBooking) => trialBooking.label === "Upcoming")
    if (!_.isEmpty(bookedTrial)) {
        cultTrialUserTypes.push("TRIAL_BOOKED")
    }
    return cultTrialUserTypes
}

export function getMonday(d: Date) {
    d = new Date(d)
    const day = d.getDay(),
        diff = d.getDate() - day + (day == 0 ? -6 : 1) // adjust when day is sunday
    return new Date(d.setDate(diff))
}

export function getTimeSlotsForWaitlistExtensionWidget(cultClass: CultClass, tz: Timezone, classStartTime: string): { timeRemainingToClass: number, notificationTimeSlots: number[]} {
    let timeSlots = cultClass.wlNotificationSlots
    const currentTime: string = TimeUtil.todaysDate(tz, "YYYY-MM-DD HH:mm:ss")
    const timeRemainingToClass: number = TimeUtil.diffInMinutes(tz, currentTime, classStartTime)

    let finalSlots: number[] = []
    if (!_.isEmpty(timeSlots)) {
        timeSlots = timeSlots.sort()
        finalSlots = timeSlots.filter(slot => {
            if (slot <= timeRemainingToClass) {
                return true
            }
            return false
        })
    }
    return {
        timeRemainingToClass: timeRemainingToClass,
        notificationTimeSlots: finalSlots,
    }
}

export function getProfileBackgroundColor(profileName: string) {
    const numberOfColor: number = USER_PROFILE_BACKGROUND_COLORS.length
    let asciiSum: number = 0

    if (profileName) {
        for (let i = 0; i < profileName.length; i += 1) {
            asciiSum += profileName.charCodeAt(i)
        }
    }
    return USER_PROFILE_BACKGROUND_COLORS[asciiSum % numberOfColor]
}

export function getFindCenterLocationArray(
    centers: NearByCenter[],
    userContext: UserContext,
    centerType?: NearByCenter["type"],
    showBigSeparator?: boolean,
) {
    let categoryTitle: string
    if (centerType === "NEARBY") {
        categoryTitle = "Near by centers"
    } else if (centerType === "CONVENIENT" || centerType === "PREFERRED") {
        categoryTitle = "My preferred centres"
    }

    let firstCenter: boolean = true
    const lastCenterIndex = centers.length - 1
    const { appVersion } = userContext.sessionInfo
    return _.map(centers, (center, index) => {
        const seoParams: SeoUrlParams = {
            locality: center.address?.locality,
            productName: center?.name
        }

        let centerCloseProps = {}
        if (center.status && center.status !== "OPEN") {
            let centerStatus = "CLOSED"
            let centerOpenText = "reopens"
            if (appVersion >= CULT_UPCOMING_TAG_VERSION) {
                centerStatus = center.status === "CLOSED" ? "CLOSED" : "LAUNCHING SOON"
                centerOpenText = center.status === "CLOSED" ? "reopens" : "opens"
            }
            centerCloseProps = {
                centerStatus: centerStatus,
                alertAction: {
                    title: !center.isCenterStatusAlertEnabled
                        ? `NOTIFY WHEN CENTRE ${centerOpenText.toUpperCase()}`
                        : `*You'll be notified when the center ${centerOpenText}`,
                    actionType: "REST_API",
                    meta: {
                        method: "POST",
                        url: `/cult/notifyCenterReopen`,
                        body: { "centerIds": [center.id] }
                    },
                    shouldRefreshPage: true
                }
            }
        }

        let singleNotificationFirstCenter = {}
        if (firstCenter) {
            singleNotificationFirstCenter = {
                categoryTitle: categoryTitle,
            }
            firstCenter = false
        }

        const centerLocation: CenterLocation = {
            lat: center.address?.latitude,
            lon: center.address?.longitude,
            title: center.name,
            contentMetric: {
                contentId: center.id.toString()
            },
            address: (!_.isEmpty(center.address?.addressLine1) ? center.address?.addressLine1 : "") + " " + (!_.isEmpty(center.address?.addressLine2) ? center.address.addressLine2 : ""),
            distance: center.distance * 1000, // converting to metres
            action: {
                iconName: "chevron-right",
                title: "DETAILS",
                actionType: "NAVIGATION", url: center.tenantID === 1
                    ? BaseActionUtil.cultCenter(center.id.toString(), undefined, userContext.sessionInfo.userAgent, seoParams)
                    : BaseActionUtil.mindCenter(center.id.toString(), undefined, userContext.sessionInfo.userAgent, seoParams)
            },
            isCenterStatusAlertEnabled: center.isCenterStatusAlertEnabled,
            centerStatus: "ACTIVE",
            ...centerCloseProps,
            ...singleNotificationFirstCenter,
            showBigSeparator: index === lastCenterIndex && showBigSeparator ? true : false,
        }
        return centerLocation
    })
}

export function getCenterReopenBanner(
    banner: any,
    centers: CultCenter[]
) {
    const centerList: CultCenterCloseInfo[] = centers.map(center => {
        return {
            id: center.id,
            name: center.name,
            address:  (!_.isEmpty(center.Address?.addressLine1) ? center.Address?.addressLine1 : "") + " " + (!_.isEmpty(center.Address?.addressLine2) ? center.Address.addressLine2 : ""),
            isChecked: true,
            isDisabled: center.isCenterStatusAlertEnabled,
            alreadyNotifiedText: center.isCenterStatusAlertEnabled ? "*You'll be notified when the center reopens" : undefined
        }
    })

    let currentData = banner
    currentData = {
        ...currentData,
        action : {
            actionType: "SHOW_CULT_CENTER_REOPEN_MODAL",
            meta: {
                centerList: centerList,
                actionTitle:  "NOTIFY ME",
                title: "Choose Centres you want to be notified about"
            },
            shouldRefreshPage: true,
        }
    }

    return currentData
}

export function isMembershipCurrent( userContext: UserContext, membership: Membership): boolean {
    const tz = userContext.userProfile.timezone
    const today = TimeUtil.todaysDateWithTimezone(tz)
    const startDate = TimeUtil.formatEpochInTimeZone(tz, membership.start)
    const endDate = TimeUtil.formatEpochInTimeZone(tz, membership.end)
    return startDate <= today && endDate >= today
}

export async function isUserActiveMember(userContext: UserContext, membershipService: IMembershipService): Promise<boolean> {
    const userId = userContext.userProfile.userId
    const membershipList = (await eternalPromise(membershipService.getCachedMembershipsForUser(userId, AppUtil.getTenantFromUserContext(userContext), ["CULT"], ["PURCHASED", "PAUSED"]))).obj
    return _.some(membershipList, (membership) => isMembershipCurrent(userContext, membership))
}

export async function showInterventionAFMSlotBooking(userContext: UserContext, userAttributeService: IUserAttributeCacheClient, segmentationService: ISegmentService, classCenterID: number, serviceInterfaces: CFServiceInterfaces, segmentCacheClient: ISegmentationCacheClient) {

    const homeCenter  = [93, 831, 7, 132, 151]
    if (!homeCenter.includes(classCenterID)) {
        return false
    }

    serviceInterfaces.logger.info("primary action cult book class is User Part of Segment before", userContext.userProfile.userId, classCenterID)

    const segments = await segmentCacheClient.getUserSegments(userContext.userProfile.userId)

    serviceInterfaces.logger.info("primary action cult book class is User Part of Segment ", userContext.userProfile.userId, segments)

    const isUserPartOfCenter = segments.includes("Mandatory Onboarding Session FC Pilot Pre Booking")

    serviceInterfaces.logger.info("primary action cult book class is User Part of Segment ", userContext.userProfile.userId, isUserPartOfCenter)

    return isUserPartOfCenter && homeCenter.includes(classCenterID)
}

export default CultUtil
