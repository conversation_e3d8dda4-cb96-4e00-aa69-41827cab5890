import { CultCenter } from "@curefit/cult-common"
import { Center } from "@curefit/care-common"
import * as _ from "lodash"
import { ILocationService } from "@curefit/location-service"
import { LogUtil } from "@curefit/util-common"
import { Tenant } from "@curefit/user-common"
import { Session, SessionInfo, UserContext } from "@curefit/userinfo-common"
import { PromUtil } from "../../util/PromUtil"
import { UserCity } from "../user/IUserBusiness"
import * as express from "express"
import AppUtil from "./AppUtil"
import { ICityService } from "@curefit/location-mongo"
import { ILogger } from "@curefit/base"
import { DetectedCityResponseByIp, ICFAPICityService } from "../city/ICFAPICityService"
import IDeviceBusiness from "../device/IDeviceBusiness"
import { AttributeAction, EventType } from "@curefit/rashi-client"

const BENGALURE_CENTER_LAT = 12.97159
const BENGALURE_CENTER_LON = 77.59456
const DISTANCE_THRESHOLD = 30 // in kms
export class LocationUtil {

    static isWithinBangalore(lat: number, lon: number): boolean {
        // If lat-lng not passed, we treat as inside bangalore
        if (!lat || !lon)
            return true
        if (LocationUtil.getDistanceFromLatLonInKm(lat, lon, BENGALURE_CENTER_LAT, BENGALURE_CENTER_LON) < DISTANCE_THRESHOLD)
            return true
        else
            return false
    }

    static getDistanceFromLatLonInKm(lat1: number, lon1: number, lat2: number, lon2: number): number {
        const R = 6371 // Radius of the earth in km
        const dLat = LocationUtil.deg2rad(lat2 - lat1)  // deg2rad
        const dLon = LocationUtil.deg2rad(lon2 - lon1)
        const a =
            Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(LocationUtil.deg2rad(lat1)) * Math.cos(LocationUtil.deg2rad(lat2)) *
            Math.sin(dLon / 2) * Math.sin(dLon / 2)

        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
        const d = R * c // Distance in km
        return d
    }

    static deg2rad(deg: number): number {
        return deg * (Math.PI / 180)
    }

    static sortCentersByLocation(lat: number, lon: number, centers: CultCenter[]) {
        centers.sort(function (c1: CultCenter, c2: CultCenter): number {
            const lat1: number = c1.Address.latitude
            const lon1: number = c1.Address.longitude
            const lat2: number = c2.Address.latitude
            const lon2: number = c2.Address.longitude
            if (!_.isNumber(lat1) || !_.isNumber(lon1))
                return 1
            if (!_.isNumber(lat2) || !_.isNumber(lon2))
                return -1
            const distance1: number = LocationUtil.getDistanceFromLatLonInKm(lat1, lon1, lat, lon)
            const distance2: number = LocationUtil.getDistanceFromLatLonInKm(lat2, lon2, lat, lon)
            return distance1 < distance2 ? -1 : 1
        })
    }

    static sortCareCentersByLocation(lat: number, lon: number, centers: Center[]) {
        centers.sort(function (c1: Center, c2: Center): number {
            const lat1: number = c1.latitude
            const lon1: number = c1.longitude
            const lat2: number = c2.latitude
            const lon2: number = c2.longitude
            if (!_.isNumber(lat1) || !_.isNumber(lon1))
                return 1
            if (!_.isNumber(lat2) || !_.isNumber(lon2))
                return -1
            const distance1: number = LocationUtil.getDistanceFromLatLonInKm(lat1, lon1, lat, lon)
            const distance2: number = LocationUtil.getDistanceFromLatLonInKm(lat2, lon2, lat, lon)
            return distance1 < distance2 ? -1 : 1
        })
    }

    static async getLocationData(locationService: ILocationService, latitude: number, longitude: number) {
        try {
            const placeData = await locationService.getPlace(latitude, longitude, undefined)
            return placeData
        } catch (err) {
            LogUtil.error(`Error while fetching location data for latitude ${latitude} and longitude ${longitude}`, err)
            return null
        }
    }

    private static getUserCity(
        tenant: Tenant,
        cityService: ICityService,
        logger: ILogger,
        userId: string,
        selectedCityId?: string,
        lat?: number,
        lon?: number,
        detectedCityId?: string,
        deviceBusiness?: IDeviceBusiness,
        isNotWeb?: boolean,
    ): UserCity {
        let userCity = null
        try {
            if (selectedCityId) {
                userCity = <UserCity>{
                    city: cityService.getCityById(selectedCityId),
                    isCityManuallySelected: true,
                    reason: "SESSION"
                }
            }
            if (!userCity?.city && lat && lon) {
                userCity = <UserCity>{
                    city: cityService.getCityAndCountry(
                        tenant,
                        lat,
                        lon,
                        null
                    ).city,
                    isCityManuallySelected: true,
                    reason: "COORDINATES"
                }
                if (userCity?.city && deviceBusiness && userId && isNotWeb) {
                    deviceBusiness.publishUserEventToRashi(userId, {user_city_set_reason: "COORDINATES"}, AttributeAction.REPLACE, tenant)
                }
            }
            if (!userCity?.city && detectedCityId) {
                userCity = <UserCity>{
                    city: cityService.getCityById(detectedCityId),
                    isCityManuallySelected: false,
                    reason: "IP"
                }
                if (userCity?.city && deviceBusiness && userId && isNotWeb) {
                    deviceBusiness.publishUserEventToRashi(userId, {user_city_set_reason: "IP"}, AttributeAction.REPLACE, tenant)
                }
            }
            if (!userCity?.city) {
                if (userCity?.city && deviceBusiness && userId && isNotWeb) {
                    deviceBusiness.publishUserEventToRashi(userId, {user_city_set_reason: "DEFAULT_BANGALORE"}, AttributeAction.REPLACE, tenant)
                }
                userCity = <UserCity>{
                    city: cityService.getDefaultCity(tenant),
                    isCityManuallySelected: false,
                    reason: "DEFAULT_BANGALORE"
                }
            }
        } catch (e) {
            logger.error("Error in  getting the user City. Error: " + e)
        }
        if (!userCity?.city) {
            logger.error("Error in getting the User City")
        }
        return userCity
    }

    // LocationUtil.getUserCityFromRequest in CF-API java
    static getUserCityFromUserContext(
        userContext: UserContext,
        cityService: ICityService,
        logger: ILogger,
        cityIdInHeader?: string,
        detectedCityId?: string
    ): UserCity {
        let cityId: string = cityIdInHeader
        if (userContext?.sessionInfo?.sessionData?.isCityManuallySelected ?? false) {
            cityId = userContext?.sessionInfo?.sessionData?.cityId
        }
        const tenant: Tenant = AppUtil.getTenantFromUserContext(userContext)
        const isNotWeb = !AppUtil.isWeb(userContext)
        return this.getUserCity(
            tenant,
            cityService,
            logger,
            userContext?.userProfile?.userId,
            cityId,
            null,
            null,
            detectedCityId,
            null,
            isNotWeb
        )
    }

    // LocationUtil.getUserCityFromRequest in CF-API java
    static async getUserCityFromReq(
        req: express.Request,
        cityService: ICityService,
        logger: ILogger,
        CFAPICityService?: ICFAPICityService,
        deviceBusiness?: IDeviceBusiness,
    ): Promise<UserCity> {
        const userContext: UserContext = req?.userContext ?? null
        const lat: number = req.query.lat
        const lon: number = req.query.lon
        const session: Session = req?.session ?? null
        const sessionInfo: SessionInfo = userContext?.sessionInfo ?? null
        const tenant: Tenant = AppUtil.getTenantFromReq(req)
        let cityId: string = req.headers["cityid"] as string
        if (session?.sessionData?.isCityManuallySelected ?? false) {
            cityId = session?.sessionData?.cityId
        }
        let detectedCityResponseByIp: DetectedCityResponseByIp
        const ip = req.ip ?? req.connection.remoteAddress
        const isLocalIpDetected: boolean = ip.includes("*********")
        if (ip && CFAPICityService) {
            detectedCityResponseByIp = await CFAPICityService.getCityAndCountryByIp(tenant, ip)
        }
        const isNotWeb = !AppUtil.isWeb(userContext)
        return this.getUserCity(
            tenant,
            cityService,
            logger,
            session?.userId,
            cityId,
            lat ?? sessionInfo?.lat,
            lon ?? sessionInfo?.lon,
            (isLocalIpDetected) ? session?.sessionData?.cityId : detectedCityResponseByIp?.city?.cityId,
            deviceBusiness,
            isNotWeb
        )
    }

}

export default LocationUtil
