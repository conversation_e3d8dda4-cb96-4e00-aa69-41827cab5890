import { PreferredLocation, UserContext } from "@curefit/userinfo-common"
import * as express from "express"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import * as _ from "lodash"
import * as momentTz from "moment-timezone"
import { Constants } from "@curefit/base-utils"
import { ILogger } from "@curefit/base"
import { User } from "@curefit/user-common"
import { LatLong } from "@curefit/location-common"
import CFAPIJavaService from "../CFAPIJavaService"
import EatUtil from "./EatUtil"
import AppUtil from "./AppUtil"
import { randomUUID } from "crypto"

export const CLS_UTIL_REQ_HEADERS = "CLS_UTIL_REQ_HEADERS"

export class AuthUtil {

    static CULTSPORT_COOKIE_DOMAIN = ".cultsport.com"
    static CULTFIT_COOKIE_DOMAIN = ".cult.fit"

    public static getApiKeyFromReq(req: express.Request) {
        return req.headers["api-key"] as string || req.headers["apikey"] as string || req.query.apikey
    }

    static setCookies(st: string, at: string, deviceId: string, req: express.Request, res: express.Response, logger?: ILogger) {
        const userAgent: UserAgent = AuthUtil.getUserAgent(req)
        const apiKey: string = AuthUtil.getApiKeyFromReq(req)
        if (userAgent !== "APP") {
            if (logger) {
                logger.info("setting cookie for at as " + at)
            }
            // Its just an expiry date. No need to have timezone for this
            const cookieExpiryDate = new Date(momentTz().add(6, "months").toDate())
            let sameSite: any
            sameSite = (apiKey !== Constants.getGooglePayWebApiKey())
            if (logger) {
                logger.info(`sameSite: ${sameSite} for apiKey: ${apiKey}`)
            }

            const domain =  AuthUtil.getDomainFromApiKey(apiKey)

            res.cookie("at", at, { "httpOnly": true, domain, signed: true, expires: cookieExpiryDate, sameSite: sameSite, secure: true })
            res.cookie("st", st, { "httpOnly": true, domain, signed: true, expires: cookieExpiryDate, sameSite: sameSite, secure: true })
            if (deviceId) {
                res.cookie("deviceId", deviceId, {
                    "httpOnly": true,
                    domain,
                    signed: true,
                    expires: cookieExpiryDate,
                    secure: true,
                    sameSite: sameSite
                })
            }
        }
    }

    static getDomainFromApiKey(apiKey: string): string {
        if (AppUtil.isCultSportWebApiKey(apiKey)) {
            return this.CULTSPORT_COOKIE_DOMAIN
        }
        return this.CULTFIT_COOKIE_DOMAIN
    }

    static getAuthTokenFromRequest(req: express.Request): string {
        let authToken: string = req.headers["at"] as string
        const userAgent: UserAgent = AuthUtil.getUserAgent(req)
        authToken = authToken === "null" ? undefined : authToken
        if (userAgent == "DESKTOP" || userAgent == "MBROWSER") {
            authToken = authToken || req.signedCookies["at"] || req.cookies["at"]
        }
        return authToken
    }

    static isFixedGuestUser(userContext: UserContext): boolean {
        return userContext.userProfile.userId == "0" ? true : false
    }

    static isGuestUser(userContext: UserContext): boolean {
        return !userContext.sessionInfo.isUserLoggedIn
    }

    static async getRedirectUrlIfNeeded(cFAPIJavaService: CFAPIJavaService, req: express.Request, at: string, isNotLoggedIn: boolean): Promise<string> {
        if (req.body.client_id === "alexa" && req.originalUrl.indexOf("/deviceLogin") < 0 && !isNotLoggedIn) {
            const state = req.body.state
            const redirectUri = req.body.redirect_uri
            return `${redirectUri}#state=${state}&access_token=${at}&token_type=Bearer`
        } else if (!isNotLoggedIn && req.body.client_id === "slack") {
            const response = await cFAPIJavaService.registerSlackUser(req)
            return response.slackAppChannelUrl
        }
        return undefined
    }

    static clearCookies(res: express.Response, req: express.Request) {
        const domain = AuthUtil.getDomainFromApiKey(AuthUtil.getApiKeyFromReq(req))
        res.clearCookie("at", { "httpOnly": true, domain: domain, signed: true })
        res.clearCookie("st", { "httpOnly": true, domain: domain, signed: true })
    }

    static clearChangePasswordToken(res: express.Response, req: express.Request) {
        const domain = AuthUtil.getDomainFromApiKey(AuthUtil.getApiKeyFromReq(req))
        res.clearCookie("verificationToken", { "httpOnly": true, domain: domain, signed: true })
        res.clearCookie("emailToken", { "httpOnly": true, domain: domain, signed: true })
    }

    static isAuthTokenCookiePresent(req: express.Request): boolean {
        return !_.isNil(req.cookies["at"]) || !(_.isNil(req.signedCookies) || _.isNil(req.signedCookies["at"]))
    }

    static getUserAgent(req: express.Request): UserAgent {
        const uuid = randomUUID()
        const source: string = req.headers["user-agent"] as string
        const apiKey: string = req.headers["api-key"] as string || req.headers["apikey"] as string
        let userAgent: UserAgent = "APP"
        // Source might be null
        if (source) {
            if (req.useragent.isBot) {
                userAgent = "DESKTOP"
            } else if (apiKey === Constants.getCurefitWebApiKey() || apiKey === Constants.getJanusApiKey()
                || apiKey === Constants.getPhonePeWebApiKey() || apiKey === Constants.getGooglePayWebApiKey()
                || apiKey === Constants.getPhonePeCareApiKey() || apiKey === Constants.getPhonePeCultApiKey()
                || apiKey === Constants.getPhonePeMindApiKey() || apiKey === Constants.getPhonePeWholefitApiKey()
                || apiKey === Constants.getPaytmPwaApiKey() || apiKey === Constants.getInternationWebsiteApiKey()
                || apiKey === Constants.getTataNeuWebApiKey()
                || AppUtil.isCultSportWebApiKey(apiKey)
            ) {
                if (req.useragent.browser && req.useragent.isMobile) {
                    userAgent = "MBROWSER"
                } else {
                    userAgent = "DESKTOP"
                }
            } else {
                userAgent = "APP"
            }
        }
        return userAgent
    }

    static getClsReqStringifedHeaders(req: express.Request): string {
        return JSON.stringify(Object.fromEntries(this.getClsHeaderKeysToStore().map((key: string) => {
            return req.headers[key] ? [key, req.headers[key]] : []
        })))
    }

    static getClsHeaderKeysToStore() {
        return [
            "api-key",
            "apikey",
            "deviceid",
            "at",
            "st",
            "osname",
            "appversion",
            "clientversion",
            "lat",
            "lon",
            "codepushversion",
            "browsername",
            "ssotoken",
            "user-agent",
            "x-request-id",
            "timezone",
            "cityid",
            "authorization",
            "subuserid",
            "cookie",
            "appsource",
            "mixpanel-distinct-id"
        ]
    }

    static getLatLongFromUserContextAndParams(userContext: UserContext, params: any, userPrefLocationData: PreferredLocation): any {
        const latFromClient: number = params["lat"] || userContext?.sessionInfo?.lat
        const longFromClient: number = params["lon"] || userContext?.sessionInfo?.lon
        const {lat, long } = EatUtil.getLatLong(userPrefLocationData, !_.isNil(latFromClient) && !_.isNaN(latFromClient) ? Number(latFromClient) : undefined, !_.isNil(longFromClient) && !_.isNaN(longFromClient) ? Number(longFromClient) : undefined)
        return {
            lat,
            long, // Passing this since old app has check on this field
            lon: long,
        }
    }

}
export default AuthUtil
