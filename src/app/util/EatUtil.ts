import { PreferredLocation, Session, SessionInfo, UserContext } from "@curefit/userinfo-common"
import * as _ from "lodash"
import { CartOffer, EatOfferRequestParamsV3, FoodSinglePriceOfferResponse } from "@curefit/offer-common"
import {
    ContactInstruction,
    ContactInstructions, DayOfWeekPriority, ListingBrandIds, ListingBrandIdType, SalesChannel, SortOrder,
} from "@curefit/eat-common"
import {
    CutleryInstruction,
    CutleryInstructions,
} from "@curefit/eat-common"
import {
    DeliveryChannel,
    DeliveryInstruction,
} from "@curefit/eat-common"
import {
    DropInstruction,
    DropInstructions,
} from "@curefit/eat-common"
import { MealSlot, DeliveryArea } from "@curefit/eat-common"
import {
    FoodProduct as Product
} from "@curefit/eat-common"
import {
    MenuType,
} from "@curefit/eat-common"
import { ActionSheet, ActionSheetOption, ActionType, FormattedTextWidget } from "../common/views/WidgetView"
import { FoodShipmentStatus, OrderTrackingStatus } from "@curefit/eat-common"
import { MealItem } from "../page/PageWidgets"
import { RecommendedMeals } from "@curefit/vm-models"
import { OfferV2 } from "@curefit/offer-common"
import { SlotUtil } from "@curefit/eat-util"
import { CATEGORY_PRIORITY_MAP, MAX_CART_SIZE, OfferUtil } from "@curefit/base-utils"
import { capitalizeFirstLetter, pluralizeStringIfRequired, Timezone } from "@curefit/util-common"
import { FoodCategory, UserDeliveryAddress } from "@curefit/eat-common"
import { TimeUtil } from "@curefit/util-common"
import { MeasurementUnit } from "@curefit/food-common"
import { Order, OrderProductSnapshots } from "@curefit/order-common"
import { ALL_MEAL_SLOTS } from "@curefit/eat"
import { BillingDetails, CartAddon, EatBrand, FitClubAddon, OrderItemInfo } from "@curefit/eat-api-client"
import { SellerService } from "@curefit/finance-models"
import { IDeliveryAreaService } from "@curefit/delivery-client"
import { ICityService } from "@curefit/location-mongo"
import AppUtil from "./AppUtil"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { LatLong, StartEndTime } from "@curefit/base-common"
import OffersUtil from "./OffersUtil"
import { ClientCartOffer } from "../offer/OfferCommon"
import { FoodBooking } from "@curefit/alfred-client"
const clone = require("clone")

export const ATTACH_CATEGORIES = ["DRINKS", "DESSERTS", "HEALTH_BITES", "SNACKS"]
export const DEFAULT_TICKET_SIZE = 7
export const MAX_UPCOMING_MEALS = 7

export interface InstructionPayload {
    dropInstruction: DropInstruction | "NONE"
    contactInstruction: ContactInstruction
    cutleryInstruction: CutleryInstruction
}
export interface Instruction {
    displayName: string
    description?: string
    tag?: string
    payload: InstructionPayload
}

export const MINI_MEAL_SINGLES_MESSAGE_EXPIRY_SECS = 3 * 60 * 60
export const MINI_MEAL_PACK_MESSAGE_EXPIRY_SECS = 3 * 60 * 60
const ADD_ON_PRODUCSTS = ["DRINK", "DESSERT", "ADDONS_CF", "ADDONS_CF_01", "ADDONS_CF_02", "ADDONS_CF_03", "ADDONS_CF_04",
    "ADDONS_CF_05", "ADDONS_CF_06", "ADDONS_CF_07", "ADDONS_CF_08", "ADDONS_CF_09", "ADDONS_CF_10", "DRINK_REJOOV", "ADDONS_01", "ADDONS_02", "ADDONS_03", "ADDONS_04",
    "ADDONS_05", "ADDONS_06", "ADDONS_07", "ADDONS_08", "ADDONS_09", "ADDONS_10", "ADDON_SPECIAL_ROTI", "ADDON_SPECIAL_GRAVY", "ADDON_SPECIAL_SALAD", "ADDON_SPECIAL_RICE"]

export const MINI_MEALS_UPGRADE_MAP = new Map<string, string>()
MINI_MEALS_UPGRADE_MAP.set("DINNER_LIGHT_MEALS_VEG01", "DINNER_IND_VEG01")
MINI_MEALS_UPGRADE_MAP.set("DINNER_LIGHT_MEALS_NVEG01", "DINNER_IND_NVEG01")
MINI_MEALS_UPGRADE_MAP.set("LUNCH_LIGHT_MEALS_VEG01", "IND_VEG01")
MINI_MEALS_UPGRADE_MAP.set("LUNCH_LIGHT_MEALS_NVEG01", "IND_NVEG01")

export const DROP_INSTRUCTION_DISPLAY_NAME = new Map<DropInstruction | "NONE", string>()
DROP_INSTRUCTION_DISPLAY_NAME.set("ME", "Call on arrival")
DROP_INSTRUCTION_DISPLAY_NAME.set("NONE", "No delivery instructions")
DROP_INSTRUCTION_DISPLAY_NAME.set("RECEPTION", "Deliver at reception/security")
DROP_INSTRUCTION_DISPLAY_NAME.set("GATE", "Drop at door")
DROP_INSTRUCTION_DISPLAY_NAME.set("KIOSK", "Drop at kiosk")

export const CUTLERY_INSTRUCTION_NAME = new Map<CutleryInstruction, string>()
CUTLERY_INSTRUCTION_NAME.set("GIVE_CUTLERY", "Bring cutlery")
CUTLERY_INSTRUCTION_NAME.set("NO_CUTLERY", "No cutlery")

export const ADD_CHANGE_MEAL_UNSUPPORTED_BRANDS = [ListingBrandIds.DEALS, ListingBrandIds.CUREFOODS]
export function getCategoryPriority(clpCategory: FoodCategory, mealSlot: MenuType, deliveryChannel: DeliveryChannel, dayOfWeek?: number): number {
    const sortOrder: SortOrder = clpCategory.sortOrder
    if (_.isNil(sortOrder)) {
        return 1
    }

    if (deliveryChannel && sortOrder.channelOverride && sortOrder.channelOverride[deliveryChannel]) {
        return sortOrder.channelOverride[deliveryChannel]
    }

    const dayOfWeekPriorityOverride: DayOfWeekPriority = sortOrder.dayOfWeekPriorityOverride
    if (dayOfWeek && dayOfWeekPriorityOverride && dayOfWeekPriorityOverride[dayOfWeek.toString()]) {
        const priorityOverrides: any = dayOfWeekPriorityOverride[dayOfWeek.toString()]
        if (priorityOverrides) {
            const mealSlotPriorityOverride: { [mealSlot in MenuType]: number } = priorityOverrides.mealSlotPriority
            if (mealSlotPriorityOverride && mealSlotPriorityOverride[mealSlot]) {
                return mealSlotPriorityOverride[mealSlot]
            }
            if (priorityOverrides.clpPriority) {
                return priorityOverrides.clpPriority
            }
        }
    }
    const overrides: { [mealSlot in MenuType]: number } = sortOrder.mealSlotPriorityOverride
    if (overrides && overrides[mealSlot]) {
        return overrides[mealSlot]
    }

    const clpPriority: number = sortOrder.clpPriority
    if (clpPriority) {
        return clpPriority
    }
    return 1
}

export const DEFAULT_FOOD_CATEGORY_ID = "BALANCED_MEALS"

export const KioskStandingInstructionList: Instruction[] = [
    {
        displayName: "Drop at kiosk",
        payload: {
            dropInstruction: DropInstructions[3],
            contactInstruction: ContactInstructions[1],
            cutleryInstruction: CutleryInstructions[0]
        }
    }
]
export const NonKioskStandingInstructionList: Instruction[] = [
    {
        displayName: "Call on arrival",
        payload: {
            dropInstruction: DropInstructions[0],
            contactInstruction: ContactInstructions[0],
            cutleryInstruction: CutleryInstructions[0]
        }
    },
    {
        displayName: "Deliver at reception/security",
        payload: {
            dropInstruction: DropInstructions[1],
            contactInstruction: ContactInstructions[1],
            cutleryInstruction: CutleryInstructions[0]
        }
    },
    {
        displayName: "Drop at door",
        payload: {
            dropInstruction: DropInstructions[2],
            contactInstruction: ContactInstructions[1],
            cutleryInstruction: CutleryInstructions[0]
        }
    }
]

export const EatDeliveryInstructionList: Instruction[] = [
    {
        displayName: "No delivery instructions",
        description: "Opt for contact-less delivery to get your meal placed outside your door",
        payload: {
            dropInstruction: "NONE",
            contactInstruction: ContactInstructions[0],
            cutleryInstruction: CutleryInstructions[0]
        }
    },
    {
        displayName: "Deliver at reception/security",
        tag: "CONTACTLESS",
        description: "Your meal will be delivered at the reception/security at your apartment/workplace",
        payload: {
            dropInstruction: DropInstructions[1],
            contactInstruction: ContactInstructions[0],
            cutleryInstruction: CutleryInstructions[0]
        }
    },
    {
        displayName: "Drop at door",
        tag: "CONTACTLESS",
        description: "Your meal will be placed on a clean surface outside your door",
        payload: {
            dropInstruction: DropInstructions[2],
            contactInstruction: ContactInstructions[0],
            cutleryInstruction: CutleryInstructions[0]
        }
    }
]

export const EatContactInstructionList: Instruction[] = [
    {
        displayName: "Call me",
        description: "The delivery person will call you on arrival",
        payload: {
            dropInstruction: "NONE",
            contactInstruction: ContactInstructions[0],
            cutleryInstruction: CutleryInstructions[0]
        }
    },
    {
        displayName: "Don't call me",
        description: "The delivery person will not call you on arrival",
        payload: {
            dropInstruction: "NONE",
            contactInstruction: ContactInstructions[1],
            cutleryInstruction: CutleryInstructions[0]
        }
    }
    // Removing for now as there is no option to message the user from the pilot app
    // {
    //     displayName: "Message Me",
    //     description: "The delivery person will message you on arrival",
    //     payload: {
    //         dropInstruction: "NONE",
    //         contactInstruction: ContactInstructions[2],
    //         cutleryInstruction: CutleryInstructions[0]
    //     }
    // }
]

export const CONTACT_INSTRUCTION_DISPLAY_NAME = new Map<ContactInstruction, string>()
CONTACT_INSTRUCTION_DISPLAY_NAME.set("CALL_ME", EatContactInstructionList[0].displayName)
CONTACT_INSTRUCTION_DISPLAY_NAME.set("DONT_CALL", EatContactInstructionList[1].displayName)

export const EatInstructionList: Instruction[] = []
EatInstructionList.push(...NonKioskStandingInstructionList)
EatInstructionList.push(...KioskStandingInstructionList)

export const RECIPE_VERSION_ANDROID = 7.20
export const RECIPE_VERSION_IOS = 7.20
export const RECIPE_CPVERSION_INTERNAL_ANDROID = 259
export const RECIPE_CPVERSION_INTERNAL_IOS = 234

class EatUtil {
    public static getStandingInstruction(
        deliveryInstruction: DeliveryInstruction
    ): string {
        const contactInsName = CONTACT_INSTRUCTION_DISPLAY_NAME.get(deliveryInstruction.contactInstruction)
        const dropInsName = DROP_INSTRUCTION_DISPLAY_NAME.get(deliveryInstruction.dropInstruction)
        const cutleryInsName = CUTLERY_INSTRUCTION_NAME.get(deliveryInstruction.cutleryInstruction)
        let instructionName: string = deliveryInstruction.dropInstruction === "ME" ? "" : (contactInsName || "")
        if (dropInsName) {
            instructionName += instructionName.length > 0 ? (", " + dropInsName) : dropInsName
        }
        if (cutleryInsName) {
            instructionName += (", " + cutleryInsName)
        }
        return instructionName
    }

    public static getEatBrand(preferredLocation: PreferredLocation, listingBrand: ListingBrandIdType): EatBrand | "FOOD_MARKETPLACE" | "CULT_BIKE" {
        let eatBrand: EatBrand | "FOOD_MARKETPLACE" | "CULT_BIKE"
        if (listingBrand === "WHOLE_FIT") {
            eatBrand = "WHOLE_FIT"
        }
        else if (listingBrand === "EAT_3P") {
            eatBrand = "EAT_3P"
        }
        else if (listingBrand === "FOOD_MARKETPLACE") {
            eatBrand = "FOOD_MARKETPLACE"
        }
        else if ((listingBrand as string) === "CULT_BIKE") {
            eatBrand = "CULT_BIKE"
        }
        else if (preferredLocation.deliveryChannel === "ONLINE") {
            eatBrand = "ONLINE"
        }
        else if (preferredLocation.deliveryChannel === "KIOSK") {
            eatBrand = "KIOSK"
        } else {
            eatBrand = "CAFE"
        }
        return eatBrand
    }

    public static getQuantityTitle(product: Product): string {
        if (!product.isPackaged || (!_.isEmpty(product.componentProds) && product.componentProds.length > 1)) {
            return
        }
        if (_.isEmpty(product.componentProds)) {
            if (!_.isNil(product.servingQty) && !_.isNil(product.servingUnit)) {
                return product.servingQty + " " + product.servingUnit
            } else {
                return
            }
        }
        const componentProduct = product.componentProds[0]
        const variant = _.find(product.variants, variant => {
            return variant.productId === componentProduct.productId
        })
        if (_.isNil(variant)) {
            return
        }
        return variant.qty + " " + variant.unit + " - Pack of " + product.servingQty
    }

    public static getVariantTitle(product: Product, listingBrand: ListingBrandIdType): string {
        if (listingBrand === "EAT_FIT" && !_.isEmpty(product.variantTitle)) {
            return product.variantTitle
        } else {
            return this.getQuantityTitle(product)
        }
    }

    public static computeNutritionalInfo(product: Product, parentProduct: Product, listingBrand: ListingBrandIdType): any {
        if (_.isNil(product.attributes)) {
            return
        }
        const nutritionInfo: any = product.attributes.nutritionInfo
        if (listingBrand !== "WHOLE_FIT") { // Handle for EAT_FIT and other brands
            // Handling For Base Product
            if (_.isNil(parentProduct)) {
                if (!_.isNil(product.servingUnit) && !_.isNil(product.servingQty) && !_.isNil(nutritionInfo.qty) && !_.isNil(nutritionInfo.unit)) { // Do conversion to get nutritional info per serving
                    return this.convertNutritionalInfo(nutritionInfo, nutritionInfo.qty, nutritionInfo.unit, product.servingQty, product.servingUnit)
                }
                return this.convertNutritionalInfo(nutritionInfo, 1, "g", 1, "g") // return as is
            }
            // Handling For Variant
            if (_.isEmpty(product.componentProds)) { // not a combo
                if (!_.isNil(nutritionInfo.qty) && !_.isNil(nutritionInfo.unit)) {
                    return this.convertNutritionalInfo(nutritionInfo, nutritionInfo.qty, nutritionInfo.unit, product.servingQty, product.servingUnit)
                }
                // For variants base product and variant will both have a serving qty
                return this.convertNutritionalInfo(nutritionInfo, parentProduct.servingQty, parentProduct.servingUnit, product.servingQty, product.servingUnit)
            }
            // for combo simply multiply as variant can only be combo of parent
            const parentNutritionalInfo = this.computeNutritionalInfo(parentProduct, null, listingBrand)
            return this.multiplyNutritionalInfo(parentNutritionalInfo, product.servingQty)
        }
        // Handling for WHOLE_FIT
        // Handling For Base Product
        if (_.isNil(parentProduct)) {
            if (!_.isNil(nutritionInfo.qty) && !_.isNil(nutritionInfo.unit)) { // Do conversion to get nutritional info per serving
                return this.getStandardNutritionalInfo(nutritionInfo, nutritionInfo.qty, nutritionInfo.unit)
            }
            if (!_.isNil(product.servingQty) && !_.isNil(product.servingUnit)) { // Do conversion to get nutritional info per serving
                return this.getStandardNutritionalInfo(nutritionInfo, product.servingQty, product.servingUnit)
            }
            return nutritionInfo
        }

        // Handling For Variant
        if (_.isEmpty(product.componentProds)) { // not a combo
            if (!_.isNil(nutritionInfo.qty) && !_.isNil(nutritionInfo.unit)) {
                return this.getStandardNutritionalInfo(nutritionInfo, nutritionInfo.qty, nutritionInfo.unit)
            }
            // For variants base product and variant will both have a serving qty
            return this.getStandardNutritionalInfo(nutritionInfo, parentProduct.servingQty, parentProduct.servingUnit)
        }

        return this.computeNutritionalInfo(parentProduct, null, listingBrand)
    }

    // Will be used by EAT so removing unit and qty as it is per serving by default
    public static convertNutritionalInfo(nutritionInfo: any, fromQty: number, fromUnit: MeasurementUnit, toQty: number, toUnit: MeasurementUnit): any {
        if (fromUnit === "kg") {
            fromUnit = "g"
            fromQty = 1000 * fromQty
        }
        if (fromUnit === "ltr") {
            fromUnit = "ml"
            fromQty = 1000 * fromQty
        }
        if (toUnit === "kg") {
            toUnit = "g"
            toQty = 1000 * toQty
        }
        if (toUnit === "ltr") {
            toUnit = "ml"
            toQty = 1000 * toQty
        }
        if (fromUnit !== toUnit) {
            return nutritionInfo
        }
        const nutritionInfoPerUnit = this.divideNutritionalInfo(nutritionInfo, fromQty)
        const nutritionInfoForSpecifiedUnit = this.multiplyNutritionalInfo(nutritionInfoPerUnit, toQty)
        nutritionInfoForSpecifiedUnit.unit = undefined
        nutritionInfoForSpecifiedUnit.qty = undefined
        return this.roundOffNutritionalValues(nutritionInfoForSpecifiedUnit)
    }

    public static roundOffNutritionalValues(nutritionInfo: any) {
        const newNutritionalInfo = clone(nutritionInfo)
        if (!_.isNil(nutritionInfo.Calories)) {
            newNutritionalInfo.Calories["Total Calories"] = Math.ceil(nutritionInfo.Calories["Total Calories"])
        }
        if (!_.isNil(nutritionInfo.Protein)) {
            newNutritionalInfo.Protein = Math.ceil(nutritionInfo.Protein)
        }
        if (!_.isNil(nutritionInfo.Fat)) {
            newNutritionalInfo.Fat["Total Fat"] = Math.ceil(nutritionInfo.Fat["Total Fat"])
        }
        if (!_.isNil(nutritionInfo.Carbs)) {
            newNutritionalInfo.Carbs["Total Carbs"] = Math.ceil(nutritionInfo.Carbs["Total Carbs"])
        }
        if (!_.isNil(nutritionInfo.Fibre)) {
            newNutritionalInfo.Fibre = Math.ceil(nutritionInfo.Fibre)
        }
        return newNutritionalInfo
    }

    // Will be used by whole.fit
    public static getStandardNutritionalInfo(nutritionInfo: any, presentQty: number, presentUnit: MeasurementUnit): any {
        if (presentUnit === "kg") {
            presentQty *= 1000
            presentUnit = "g"
        }
        if (presentUnit === "ltr") {
            presentQty *= 1000
            presentUnit = "ml"
        }
        if (presentUnit !== "g" && presentUnit !== "ml") {
            return nutritionInfo
        }
        const newNutritionalInfo = this.convertNutritionalInfo(nutritionInfo, presentQty, presentUnit, 100, presentUnit)
        newNutritionalInfo.unit = presentUnit
        newNutritionalInfo.qty = 100
        return this.roundOffNutritionalValues(newNutritionalInfo)
    }

    public static multiplyNutritionalInfo(nutritionInfo: any, multiplier: number): any {
        if (_.isNil(nutritionInfo)) {
            return undefined
        }
        const newNutritionalInfo = clone(nutritionInfo)
        if (!_.isNil(nutritionInfo.Calories)) {
            newNutritionalInfo.Calories["Total Calories"] = nutritionInfo.Calories["Total Calories"] * multiplier
        }
        if (!_.isNil(nutritionInfo.Protein)) {
            newNutritionalInfo.Protein = nutritionInfo.Protein * multiplier
        }
        if (!_.isNil(nutritionInfo.Fat)) {
            newNutritionalInfo.Fat["Total Fat"] = nutritionInfo.Fat["Total Fat"] * multiplier
        }
        if (!_.isNil(nutritionInfo.Carbs)) {
            newNutritionalInfo.Carbs["Total Carbs"] = nutritionInfo.Carbs["Total Carbs"] * multiplier
        }
        if (!_.isNil(nutritionInfo.Fibre)) {
            newNutritionalInfo.Fibre = nutritionInfo.Fibre * multiplier
        }
        return newNutritionalInfo
    }

    public static divideNutritionalInfo(nutritionInfo: any, divider: number): any {
        if (_.isNil(nutritionInfo)) {
            return undefined
        }
        const newNutritionalInfo = clone(nutritionInfo)
        if (!_.isNil(nutritionInfo.Calories)) {
            newNutritionalInfo.Calories["Total Calories"] = nutritionInfo.Calories["Total Calories"] / divider
        }
        if (!_.isNil(nutritionInfo.Protein)) {
            newNutritionalInfo.Protein = nutritionInfo.Protein / divider
        }
        if (!_.isNil(nutritionInfo.Fat)) {
            newNutritionalInfo.Fat["Total Fat"] = nutritionInfo.Fat["Total Fat"] / divider
        }
        if (!_.isNil(nutritionInfo.Carbs)) {
            newNutritionalInfo.Carbs["Total Carbs"] = nutritionInfo.Carbs["Total Carbs"] / divider
        }
        if (!_.isNil(nutritionInfo.Fibre)) {
            newNutritionalInfo.Fibre = nutritionInfo.Fibre / divider
        }
        return newNutritionalInfo
    }

    public static convertDeliveryInstructionsToOptions(): ActionSheetOption[] {
        const options: ActionSheetOption[] = []
        NonKioskStandingInstructionList.forEach((instruction: Instruction, index: number) => {
            options.push({
                optionText: EatUtil.getStandingInstruction(instruction.payload),
                payload: instruction.payload
            })
        })
        return options
    }

    public static isRecipeSupported(osName: string, appVersion: number, codepushVersion: number, isInternalUser: boolean) {
        if (osName.toLowerCase() === "android") {
            return appVersion > RECIPE_VERSION_ANDROID || codepushVersion >= RECIPE_CPVERSION_INTERNAL_ANDROID
        } else {
            return appVersion > RECIPE_VERSION_IOS || codepushVersion >= RECIPE_CPVERSION_INTERNAL_IOS
        }
    }

    public static getUnit(quantity: number, unit: string) {
        if (unit === "units" || unit === "cloves") {
            if (quantity <= 1) {
                switch (unit) {
                    case "units":
                        return "unit"
                    case "cloves":
                        return "clove"
                }
            }
            return unit
        } else if (quantity > 1 && (unit === "pinch" || unit === "glass" || unit === "bunch")) {
            return unit + "es"
        } else if (unit === "ml" || unit === "g") {
            return unit
        }
        return pluralizeStringIfRequired(unit, quantity)
    }

    public static getEatFitDeliveryStatus(state: FoodShipmentStatus | "NOT_STARTED" | "CANCELLED", listingBrand: ListingBrandIdType = "EAT_FIT"): OrderTrackingStatus {
        switch (state) {
            case "NOT_STARTED":
                return undefined
            case "CREATED":
            case "LOT_ASSIGNED":
            case "COOKING":
            case "PACKING":
            case "DISPATCHING":
            case "RECEIVING":
            case "ACCEPTING":
                return listingBrand === "WHOLE_FIT" ? "PACKING" : "PREPARING"
            case "DRIVING":
            case "ARRIVING_AT_KIOSK":
                return "ON_ITS_WAY"
            case "DELIVERING":
            case "SCANNING":
                return "ARRIVED"
            case "DELIVERED":
                return "DELIVERED"
            case "REJECTED":
                return "REJECTED"
            case "CANCELLED":
                return undefined
        }
        return undefined
    }

    public static isEatFitAvailable(cityId: string): boolean {
        return cityId === "Bangalore" || cityId === "Gurgaon" || cityId === "Hyderabad" ? true : false
    }

    public static isAddOnCategory(categoryId: string) {
        if (ADD_ON_PRODUCSTS.indexOf(categoryId) >= 0)
            return true
        else
            return false
    }

    public static formatPreferredLocation(preferredLocation: PreferredLocation): string {
        if (preferredLocation.address) {
            return capitalizeFirstLetter(preferredLocation.address.addressType) + ": " + preferredLocation.address.addressLine1 + preferredLocation.address.addressLine2
        } else if (_.get(preferredLocation, "placeData.name")) {
            return preferredLocation.placeData.name
        } else if (preferredLocation.area) {
            return preferredLocation.area.subArea
        } else if (preferredLocation.defaultArea) {
            return preferredLocation.defaultArea.subArea
        } else if (preferredLocation.city) {
            return preferredLocation.city.name
        } else {
            return undefined
        }
    }


    public static cartOffersForUpsell(cartOffers: OfferV2[]): ClientCartOffer[] {
        return cartOffers.map((cartOffer: OfferV2) => {
            const offer: ClientCartOffer = {
                title: OffersUtil.getOfferTextForPreDefinedOffers(cartOffer.offerId) || cartOffer.description,
                shortTitle: OffersUtil.getOfferTextForPreDefinedOffers(cartOffer.offerId) || cartOffer.title,
                constraints: cartOffer.constraints,
                mealSlots: _.isNil(cartOffer.constraints.mealSlots) ? ALL_MEAL_SLOTS : cartOffer.constraints.mealSlots,
                priority: cartOffer.priority,
                offerId: cartOffer.offerId,
                isFitClubOffer: _.get(cartOffer, "addons[0].addonType", undefined) === "FITCASH",
                isFreeDeliveryOffer: _.get(cartOffer , "noDeliveryCharge", false) === true,
                addons: _.get(cartOffer, "addons", [])
            }
            return offer
        })
    }

    public static findDeliveryInstructionId(
        deliveryInstruction: DeliveryInstruction
    ): number {
        return EatInstructionList.reduce(
            (foundInstructionIndex: number, instruction: Instruction, index: number) => {
                return foundInstructionIndex !== undefined
                    ? foundInstructionIndex
                    : instruction.payload.contactInstruction ===
                        deliveryInstruction.contactInstruction &&
                        instruction.payload.dropInstruction ===
                        deliveryInstruction.dropInstruction
                        ? index
                        : foundInstructionIndex
            },
            undefined
        )
    }


    public static findEatDeliveryInstructionId(
        deliveryInstruction: InstructionPayload
    ): number {
        return EatDeliveryInstructionList.reduce(
            (foundInstructionIndex: number, instruction: Instruction, index: number) => {
                return foundInstructionIndex !== undefined
                    ? foundInstructionIndex
                    : instruction.payload.contactInstruction === deliveryInstruction.contactInstruction &&  instruction.payload.dropInstruction === deliveryInstruction.dropInstruction
                        ? index
                        : foundInstructionIndex
            },
            undefined
        )
    }

    public static findEatDeliveryDropInstructionId(
        deliveryInstruction: InstructionPayload
    ): number {
        return EatDeliveryInstructionList.reduce(
            (foundInstructionIndex: number, instruction: Instruction, index: number) => {
                return foundInstructionIndex !== undefined
                    ? foundInstructionIndex
                    : instruction.payload.dropInstruction ===
                        deliveryInstruction.dropInstruction
                        ? index
                        : foundInstructionIndex
            },
            undefined
        )
    }

    public static findEatContactInstructionId(
        instruction: InstructionPayload
    ): number {
        return EatContactInstructionList.reduce(
            (foundInstructionIndex: number, value: Instruction, index: number) => {
                return foundInstructionIndex !== undefined
                    ? foundInstructionIndex
                    : value.payload.contactInstruction ===
                    instruction.contactInstruction
                        ? index
                        : foundInstructionIndex
            },
            undefined
        )
    }

    public static getDeliveryChannel(address: UserDeliveryAddress) {
        if (address && address.addressType === "KIOSK") {
            if (address.kioskType === "CAFE") {
                return "CAFE"
            }
            return "KIOSK"
        }

        return "ONLINE"
    }

    public static getDeliveryChannelFromLocation(location: PreferredLocation) {
        if (!location || !location.address) {
            return "ONLINE"
        }
        return EatUtil.getDeliveryChannel(location.address)
    }

    public static getSelectedMealSlotIndex(availableMealSlots: MealSlot[], deliveryChannel: DeliveryChannel,
        timezone: Timezone): number {
        const currentTime = new Date().getTime()
        let selectedSlotIndex = 0
        for (let i = 1; i < availableMealSlots.length; i++) {
            const previousMealSlot = availableMealSlots[i - 1]
            const currentMealSlot = availableMealSlots[i]

            let cutOffTimeForPreviousMealSlot
            let cutOffTimeForCurrentMealSlot

            if (deliveryChannel === "ONLINE") {
                const onDemanTimingOfPreviousSlot = SlotUtil.getOnDemandTimings(previousMealSlot)
                const onDemanTimingOfCurrentsSlot = SlotUtil.getOnDemandTimings(currentMealSlot)

                cutOffTimeForPreviousMealSlot = TimeUtil.getDate(TimeUtil.todaysDateWithTimezone(timezone), onDemanTimingOfPreviousSlot.end.hour, onDemanTimingOfPreviousSlot.end.min, timezone).getTime()
                cutOffTimeForCurrentMealSlot = TimeUtil.getDate(TimeUtil.todaysDateWithTimezone(timezone), onDemanTimingOfCurrentsSlot.end.hour, onDemanTimingOfCurrentsSlot.end.min, timezone).getTime()
            } else {
                const previousMealSlots = SlotUtil.getDeliverySlotsForMealSlotAndChannel(previousMealSlot, deliveryChannel)
                const currentMealSlots = SlotUtil.getDeliverySlotsForMealSlotAndChannel(currentMealSlot, deliveryChannel)

                const lastDeliverySlotOfPreviousMealSlot = SlotUtil.getMaxSlot(previousMealSlots)
                const lastDeliverySlotOfCurrentMealSlot = SlotUtil.getMaxSlot(currentMealSlots)

                const cutOffForPreviousMealSlot = SlotUtil.getHardCutOff(lastDeliverySlotOfPreviousMealSlot.slotId)
                const cutOffForCurrentMealSlot = SlotUtil.getHardCutOff(lastDeliverySlotOfCurrentMealSlot.slotId)

                cutOffTimeForPreviousMealSlot = TimeUtil.getDate(TimeUtil.todaysDateWithTimezone(timezone), cutOffForPreviousMealSlot.hour, cutOffForPreviousMealSlot.min, timezone).getTime()
                cutOffTimeForCurrentMealSlot = TimeUtil.getDate(TimeUtil.todaysDateWithTimezone(timezone), cutOffForCurrentMealSlot.hour, cutOffForCurrentMealSlot.min, timezone).getTime()
            }


            if (currentTime >= cutOffTimeForPreviousMealSlot && currentTime <= cutOffTimeForCurrentMealSlot) {
                selectedSlotIndex = this.getMealOrNextAvailableMealIndex(currentMealSlot, availableMealSlots)
            }
        }
        return selectedSlotIndex
    }

    public static getMealOrNextAvailableMealIndex(mealSlot: MealSlot, availableMealSlots: MealSlot[]): number {
        // If meal slot is present, return that index.
        if (availableMealSlots.indexOf(mealSlot) >= 0) {
            return availableMealSlots.indexOf(mealSlot)
        }
        // Return the next available meal from the passed mealSlot.
        for (let i = 0; i < availableMealSlots.length; i++) {
            if (ALL_MEAL_SLOTS.indexOf(availableMealSlots[i]) > ALL_MEAL_SLOTS.indexOf(mealSlot)) {
                return i
            }
        }
        // Return the first index if the above two does not hold true
        return 0
    }

    public static getRecommendedMealMap(recommendedMealResult: { obj: RecommendedMeals[] }) {
        if (recommendedMealResult && recommendedMealResult.obj && !_.isEmpty(recommendedMealResult.obj)) {
            return _.keyBy(recommendedMealResult.obj, (recommendedMeal) => {
                return recommendedMeal.mealSlot + "-" + recommendedMeal.productId
            })
        }
        return undefined
    }

    public static sortMealItems(mealItems: MealItem[], recommendedMealMap: _.Dictionary<RecommendedMeals>, mealSlot: MenuType,
        productMap: {
            [productId: string]: Product;
        },
        clpCategories: { [id: string]: FoodCategory }, deliveryChannel: DeliveryChannel, dayOfWeek?: number): MealItem[] {
        return mealItems.sort((a, b) => {
            const clpCategoryA = clpCategories[a.foodCategoryId]
            const clpCategoryB = clpCategories[b.foodCategoryId]
            const categoryPriorityA = getCategoryPriority(clpCategoryA, mealSlot, deliveryChannel, dayOfWeek)
            const categoryPriorityB = getCategoryPriority(clpCategoryB, mealSlot, deliveryChannel, dayOfWeek)

            if (a.stock === 0)
                return 1
            if (b.stock === 0)
                return -1
            if (categoryPriorityA > categoryPriorityB) {
                return -1
            } else if (categoryPriorityA < categoryPriorityB) {
                return 1
            } else if (clpCategoryA.categoryId !== clpCategoryB.categoryId) {
                return clpCategoryA.categoryId.localeCompare(clpCategoryB.categoryId)
            } else if (ATTACH_CATEGORIES.includes(clpCategoryA.categoryId)) {
                if (a.price.listingPrice < b.price.listingPrice) {
                    return -1
                } else if (a.price.listingPrice > b.price.listingPrice) {
                    return 1
                }
            }
            const productA = productMap[a.productId]
            const productB = productMap[b.productId]
            if (recommendedMealMap) {
                const recommendedMealA = recommendedMealMap[mealSlot + "-" + a.productId]
                const recommendedMealB = recommendedMealMap[mealSlot + "-" + b.productId]
                if (recommendedMealA && recommendedMealB) {
                    if (recommendedMealA.preference > recommendedMealB.preference)
                        return -1
                    else if (recommendedMealA.preference < recommendedMealB.preference)
                        return 1
                    else
                        return 0
                } else if (recommendedMealA)
                    return -1
                else if (recommendedMealB)
                    return 1
            } else {
                const mealPriorityA = CATEGORY_PRIORITY_MAP.get(productA.categoryId) ? CATEGORY_PRIORITY_MAP.get(productA.categoryId) : 1000
                const mealPriorityB = CATEGORY_PRIORITY_MAP.get(productB.categoryId) ? CATEGORY_PRIORITY_MAP.get(productB.categoryId) : 1000
                // Price sorting for new user offer to show meals@99 first
                if (a.price.listingPrice < b.price.listingPrice) {
                    return -1
                } else if (a.price.listingPrice > b.price.listingPrice) {
                    return 1
                }
                if (mealPriorityA < mealPriorityB)
                    return -1
                else if (mealPriorityA > mealPriorityB) {
                    return 1
                } else {
                    return 0
                }
            }
        })
    }

    public static getCutleryInstructionAction(eatDeliveryInstruction: DeliveryInstruction, meta: any, actionType: ActionType): ActionSheet {
        const instruction = EatUtil.getStandingInstruction(eatDeliveryInstruction)
        return {
            actionType: "SHOW_EAT_INSTRUCTIONS_MODAL",
            options: [{ optionText: instruction }],
            selectedOption: 0,
            meta: {
                contactInstructions: ContactInstructions.filter((contactInstruction) => {
                    return contactInstruction !== "MESSAGE_ME"
                }).map((instruction) => {
                    return {
                        name: CONTACT_INSTRUCTION_DISPLAY_NAME.get(instruction),
                        instruction: instruction
                    }
                }),
                dropInstructions: ["NONE", ...DropInstructions].filter((dropInstruction) => {
                    return dropInstruction !== "KIOSK" && dropInstruction !== "ME"
                }).map((instruction: DropInstruction) => {
                    return {
                        name: DROP_INSTRUCTION_DISPLAY_NAME.get(instruction),
                        instruction: instruction
                    }
                }),
                cutleryInstructions: CutleryInstructions.map((instruction) => {
                    return {
                        name: CUTLERY_INSTRUCTION_NAME.get(instruction),
                        instruction: instruction
                    }
                }),
                selectedInstructions: {
                    contactInstruction: eatDeliveryInstruction.contactInstruction,
                    dropInstruction: eatDeliveryInstruction.dropInstruction,
                    cutleryInstruction: eatDeliveryInstruction.cutleryInstruction
                },
                actions: [{ title: "Cancel", actionType: "HIDE_ALERT_MODAL" }, { title: "Update", actionType: actionType, meta: meta }]
            }
        }
    }

    public static getMealSlotCamelCase(mealSlot: MenuType): string {
        switch (mealSlot) {
            case "BREAKFAST":
                return "Breakfast"
            case "LUNCH":
                return "Lunch"
            case "SNACKS":
                return "Snacks"
            case "DINNER":
                return "Dinner"
            case "ALL":
                return "Order"
        }
    }

    public static getListingBrandText(listingBrand: ListingBrandIdType) {
        if (listingBrand === "WHOLE_FIT") {
            return "whole.fit"
        }
        return "eat.fit"
    }

    public static getListingBrandFromOrder(order: Order): ListingBrandIdType {
        if (!_.isNil(order) && !_.isNil(order.eatOptions)) {
            return order.eatOptions.listingBrand ? order.eatOptions.listingBrand : "EAT_FIT"
        }
        return "EAT_FIT"
    }

    public static getListingBrandForPageId(pageId: string, req: any): ListingBrandIdType {
        const selectedTab = _.get(req, "query.selectedTab")
        const actualPageId = (pageId === "eatclp") ? selectedTab : pageId
        if (_.includes(pageId, "wellness") || _.includes(pageId, "foodmp") || _.includes(selectedTab, "foodmp")) {
            return "FOOD_MARKETPLACE"
        }
        if (actualPageId === "wholefit") {
            return "WHOLE_FIT"
        } else {
            return "EAT_FIT"
        }
    }

    public static isFitClubUpselWidgetSupported(addon: FitClubAddon, order: Order): boolean {
        const fitClubActiveMembership = addon.fitClubActiveMembership
        const numProducts = _.sum(order.productSnapshots.map(p => { return p.quantity }))
        const fitClubProduct = _.find(order.productSnapshots, (productSnapshot: OrderProductSnapshots) => { return productSnapshot.productType === "FIT_CLUB_MEMBERSHIP" })
        const isFitClubInCart = !_.isNil(fitClubProduct)
        const shouldShowFitClubUpsellWidget: boolean = _.isNil(fitClubActiveMembership) && !isFitClubInCart && !order.productSnapshots[0].isPack && numProducts < MAX_CART_SIZE
        return shouldShowFitClubUpsellWidget
    }

    public static isFitClubSavingsWidgetSupported(addon: FitClubAddon, order: Order, billingDetails: BillingDetails): boolean {
        const fitClubActiveMembership = addon.fitClubActiveMembership
        const numProducts = _.sum(order.productSnapshots.map(p => { return p.quantity }))
        const fitClubProduct = _.find(order.productSnapshots, (productSnapshot: OrderProductSnapshots) => { return productSnapshot.productType === "FIT_CLUB_MEMBERSHIP" })
        const isFitClubInCart = !_.isNil(fitClubProduct)
        const fitClubFitCash = billingDetails.fitClubFitCash
        const deliveryCharge: number = !_.isNil(order.deliveryCharges) ? order.deliveryCharges.total : 0
        const shouldShowFitClubSavingsWidget: boolean = (fitClubFitCash || deliveryCharge) && (!_.isNil(fitClubActiveMembership) || isFitClubInCart) && !order.productSnapshots[0].isPack && numProducts < MAX_CART_SIZE
        return shouldShowFitClubSavingsWidget
    }

    public static isAddressWidgetSupported(isOnlyFitClubInCart: boolean): boolean {
        if (!isOnlyFitClubInCart)
            return true
        return false
    }

    public static isStartDateWidgetSupported(isPack: boolean): boolean {
        if (isPack)
            return true
        return false
    }
    public static getProductId(product: Product | OrderItemInfo): string {
        let imageProductId = product.productId
        if (product.parentProductId && (_.isNil(product.imageVersion) || product.imageVersion === 0)) {
            imageProductId = product.parentProductId
        }
        return imageProductId
    }
    public static isDeliverySlotWidgetSupported(isOnlyFitClubInCart: boolean): boolean {
        if (isOnlyFitClubInCart)
            return false
        return true
    }
    public static isCutleryWidgetSupported(isOnlyFitClubInCart: boolean, isKiosk: boolean, listingBrand: ListingBrandIdType): boolean {
        if (!isOnlyFitClubInCart && !isKiosk && listingBrand != "WHOLE_FIT")
            return true
        return false
    }
    public static isCartAddonWidgetSupported(cartAddon: CartAddon, isOnlyFitClubInCart: boolean, isPack: boolean, listingBrand: ListingBrandIdType): boolean {
        if ((_.isNil(isPack) || isPack === false) && !isOnlyFitClubInCart && listingBrand !== "WHOLE_FIT")
            return true
        return false
    }
    public static async getFssaiNumber(sellerId: string, sellerService: SellerService, deliveryAreaService: IDeliveryAreaService, cityService: ICityService, preferredLocation: PreferredLocation): Promise<string> {
        const areaId = _.get(preferredLocation, "area.areaId")
        if (!areaId) {
            return undefined
        }
        const area = await deliveryAreaService.getDeliveryArea(areaId)
        const city = await cityService.getCityById(area.cityId)
        const countryCode = _.get(city, "country.countryCode")
        if (countryCode === "IN") {
            const seller = await sellerService.getSellerById(sellerId)
            return seller.fssaiNumber ? seller.fssaiNumber : undefined
        } else {
            return undefined
        }
    }

    public static async getSingleOffers(interfaces: CFServiceInterfaces, userContext: UserContext, session: SessionInfo, listingBrand: ListingBrandIdType = "EAT_FIT", productIds?: string[], day?: string): Promise<FoodSinglePriceOfferResponse> {
        const preferredLocation = await userContext.userProfile.preferredLocationPromise
        const areaId = preferredLocation.area ? preferredLocation.area.areaId : preferredLocation.defaultArea.areaId
        const wholefitDefaultMealSlot: MenuType = "ALL"
        const eatOfferRequestParams: EatOfferRequestParamsV3 = {
            areaId: areaId,
            userId: userContext.userProfile.userId,
            cityId: userContext.userProfile.cityId,
            deviceId: session.deviceId,
            listingBrand: listingBrand,
            productIds: productIds,
            source: AppUtil.callSourceFromContext(userContext),
            dateMealSlotMap: listingBrand === "WHOLE_FIT" ? { [!_.isNil(day) ? day : TimeUtil.todaysDate(userContext.userProfile.timezone)]: [wholefitDefaultMealSlot] } : await interfaces.menuService.getDateMealSlotsMap(areaId)
        }
        return Promise.resolve({} as FoodSinglePriceOfferResponse)
    }

    public static getCategoryIdforWholefit(product: Product, saleschannel: SalesChannel = "CUREFIT_WHOLEFIT") {
        if (product) {
            if (product.salesChannelConfig) {
                const salesChannel = product.salesChannelConfig.find(prod => prod.salesChannel === saleschannel)
                if (salesChannel) {
                    return salesChannel.categoryId
                } else {
                    return product.categoryId
                }
            }
        } else {
            return undefined
        }
    }

    public static getSelectedMealSlotAndDay(queryParams: any, deliveryArea: DeliveryArea, deliveryAreaTz: Timezone, mealSlots: MealSlot[], currentMealSlot: MealSlot, orderTimes: StartEndTime[]): {
        day: string
        mealSlot: MenuType
    } {
        let day
        if (queryParams.date) {
            const queryDate = TimeUtil.parseDate(queryParams.date, deliveryAreaTz)
            const todaysDate = new Date()
            if (queryDate.setHours(0, 0, 0, 0) - todaysDate.setHours(0, 0, 0, 0) < 0) {
                day = TimeUtil.todaysDate(deliveryAreaTz)
            } else {
                day = queryParams.date
            }
        } else {
            day = TimeUtil.todaysDate(deliveryAreaTz)
        }
        let selectedMealSlot: MenuType = queryParams.mealSlot ? <MealSlot>queryParams.mealSlot : ((deliveryArea.kioskType === "CAFE") ? <MealSlot>"ALL" : SlotUtil.getNextAvailableMealSlot(currentMealSlot, mealSlots, deliveryAreaTz).mealSlot)
        const { isServiceable, isAfterLastSlot } = SlotUtil.isServiceable(mealSlots, deliveryArea, deliveryAreaTz, orderTimes)
        if (_.isNil(queryParams.mealSlot)) {
            if (!isServiceable) {
                const nextAvailability = SlotUtil.getNextAvailableMealSlot(currentMealSlot, mealSlots, deliveryAreaTz)
                selectedMealSlot = nextAvailability.mealSlot
                day = nextAvailability.date
            } else {
                if (_.isNil(currentMealSlot)) {
                    selectedMealSlot = "ALL"
                }
                else {
                    selectedMealSlot = currentMealSlot
                }
            }
        }
        return { day: day, mealSlot: selectedMealSlot }
    }

    public static getLatLong(preferredLocation: PreferredLocation, lat: number, long: number) {
        const { area, defaultArea, address, latLong } = preferredLocation
        return {
            lat: lat || latLong?.lat || address?.latLong[1] || area?.representativeLatLong?.lat || defaultArea?.representativeLatLong?.lat,
           long: long || latLong?.long || address?.latLong[0] || area?.representativeLatLong?.long || defaultArea?.representativeLatLong?.long
        } as LatLong
     }

    public static isAddChangeMealSupportedForBrand(listingBrand: ListingBrandIdType) {
        return !_.includes(ADD_CHANGE_MEAL_UNSUPPORTED_BRANDS, listingBrand)
    }

     public static async getPasscodeWidget(foodBooking: FoodBooking): Promise<any> {
        const passcode = foodBooking.qrCodeString ? foodBooking.qrCodeString.slice(5) : ""
        const passcodeWidget: FormattedTextWidget = {
            widgetType: "FORMATTED_TEXT_WIDGET",
            data: [
                {
                    text: "Display the below passcode to the center manager and collect your order.\n\n",
                    fontWeight: "REGULAR",
                    fontSize: 14,
                    fontColor: "rgb(74, 74, 74)",
                    lineHeight: 20
                },
                {
                    text: "Passcode\n",
                    fontWeight: "MEDIUM",
                    fontSize: 18,
                    fontColor: "rgb(74, 74, 74)",
                    lineHeight: 25
                },
                {
                    text: `${passcode}`,
                    fontWeight: "BOLD",
                    fontSize: 24,
                    fontColor: "rgb(74, 74, 74)",
                    lineHeight: 35
                }
            ]
        }
        return passcodeWidget
    }
}

export default EatUtil
