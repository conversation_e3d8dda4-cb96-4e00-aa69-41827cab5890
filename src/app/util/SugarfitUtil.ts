import { OrderCreate } from "@curefit/oms-api-client"
import * as _ from "lodash"
import { FreemiumUserOffer } from "../common/views/WidgetView"
import { AppTenant } from "@curefit/base-common"

class SugarfitUtil {
  public static getPriceOverrideForFreemiumPack = (createOrderPayload: OrderCreate, freemiumUserOffers: FreemiumUserOffer[]): number =>  {
    let amountPayableOverride = null
      try {
        if (!_.isEmpty(freemiumUserOffers)) {
          freemiumUserOffers.forEach(offer => {
              const offerProductIndex = _.findIndex(createOrderPayload?.products, p => {
                  return p.productId === offer.packId
              })
              if (offerProductIndex != -1) {
                  amountPayableOverride = offer.offerPrice
              }
          })
        }
      } catch (error) {
          throw error
      }
    return amountPayableOverride
  }

  static getRashiNamespace(appTenant: AppTenant) {
    if (appTenant === AppTenant.SUGARFIT) {
      return "SUGARFIT"
    } else if (appTenant === AppTenant.ULTRAFIT) {
      return "ULTRAFIT"
    }
    return "GLOBAL"
  }
}

export default SugarfitUtil

