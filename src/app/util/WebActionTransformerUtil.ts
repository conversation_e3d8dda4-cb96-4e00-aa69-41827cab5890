import * as urlUtil from "url"
import { ParsedUrlQuery } from "querystring"
import * as _ from "lodash"
import { inject, injectable } from "inversify"
import { ICatalogueService, CATALOG_CLIENT_TYPES } from "@curefit/catalog-client"
import { FoodProduct as Product } from "@curefit/eat-common"
import { IWebActionTransformer } from "@curefit/util-common"
import { CareUtil } from "./CareUtil"
import { ConsultationProduct } from "@curefit/care-common"

@injectable()
export class WebActionTransformerUtil implements IWebActionTransformer {

    private urlConstant = "productsId"

    constructor(@inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService) {
    }
    public async convertActionToWebAction(actionUrl: string) {
        const parsedUrl = this.getParsedUrl(actionUrl)
        let webUrl = await this.getWebActionUrlForProduct(parsedUrl.route, parsedUrl.queryParams)
        const query = urlUtil.parse(actionUrl).search
        webUrl = `${webUrl}${query}`
        return webUrl
    }

    private getParsedUrl(actionUrl: string) {
        actionUrl = actionUrl.trim()
        const parsedUrl = urlUtil.parse(actionUrl, true)
        const webActionUrl = {
            route: parsedUrl.hostname,
            queryParams: parsedUrl.query,
        }
        return webActionUrl
    }

    private async getWebActionUrlForProduct(url: string, query: ParsedUrlQuery) {
        const productId = _.get(query, "id", _.get(query, "productId", "")) as string
        const product: Product = await this.catalogueService.getProduct(productId)
        url = url.toLowerCase()
        if (url.indexOf("carefittc") !== -1) {
            if (CareUtil.isTherapyProduct(<ConsultationProduct>product)) {
                url = "/mind/consultation"
            } else if (CareUtil.isLivePTProduct(<ConsultationProduct>product)) {
                url = "/cult/consultation"
            } else {
                url = "/care/consultation"
            }
        }
        const urlProductName = product.title.replace(/& /g, "").split(" ").join("-")
        url = `${url}/${urlProductName}/${productId}/${this.urlConstant}/${productId}`
        return url
    }
}
