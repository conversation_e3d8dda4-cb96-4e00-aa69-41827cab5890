import { IMetric } from "@curefit/metrics-common"
import { UserContext } from "@curefit/vm-models"
import AppUtil from "./AppUtil"

export enum MeasurementUnit {
  KG = "kg",
  CM = "cm",
  FEET = "feet",
  INCH = "inch"
  // GRAM = "gm"
}

interface IMeasurementRange {
  from: number
  to: number
  average: number
  interval: number
  unit: MeasurementUnit
}

interface IMeasurementValue {
  displayText: MeasurementUnit,
  range: Array<IMeasurementRange>
}

export const getMeasurementOptions = (metric: IMetric, userContext: UserContext, isInternalUser: boolean, defaultValue?: number) => {
  const values: Array<IMeasurementValue> = []

  switch (metric.unit) {
    case MeasurementUnit.KG:
      {
        let range: Array<IMeasurementRange> = [{
          from: metric.rangeMin,
          to: metric.rangeMax,
          average: defaultValue || 75,
          interval: 1,
          unit: MeasurementUnit.KG
        }]
        if (AppUtil.isDecimalWeightUpdateEnabled(userContext, isInternalUser)) {
          range = [{
            from: metric.rangeMin,
            to: metric.rangeMax,
            average: defaultValue || 75,
            interval: 0.1,
            unit: MeasurementUnit.KG
          }]
        }

        values.push({
          displayText: MeasurementUnit.KG,
          range
        })
      }
      break

    case MeasurementUnit.CM:
      {
        const rangeCM: Array<IMeasurementRange> = [{
          from: metric.rangeMin,
          to: metric.rangeMax,
          average: 180,
          interval: 1,
          unit: MeasurementUnit.CM
        }]

        values.push({
          displayText: MeasurementUnit.CM,
          range: rangeCM
        })

        const rangeFEET: Array<IMeasurementRange> = []

        rangeFEET.push({
          from: getFeetFromCM(metric.rangeMin),
          to: getFeetFromCM(metric.rangeMax),
          average: 5,
          interval: 1,
          unit: MeasurementUnit.FEET
        })

        rangeFEET.push({
          from: 0,
          to: 11,
          average: 8,
          interval: 1,
          unit: MeasurementUnit.INCH
        })

        values.push({
          displayText: MeasurementUnit.FEET,
          range: rangeFEET
        })
      }
      break
  }
  return values
}

const getFeetFromCM = (cm: number): number => {
  const realFeet = ((cm * 0.393700) / 12)
  return Math.floor(realFeet)
}

export const getCMFromFeet = (feet: number): number => {
  return feet * 30.48
}

export const getCMFromInches = (inch: number): number => {
  return inch * 2.54
}

// export const getKGFromGrams = (gram: number): number => {
//   return gram / 1000
// }
