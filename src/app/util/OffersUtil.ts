import * as _ from "lodash"
import { RUPEE_SYMBOL } from "@curefit/finance-common"
import { OfferServiceV3 } from "@curefit/offer-service-client"
import { CultProductPricesResponse } from "@curefit/offer-common"
import { UserInfo } from "@curefit/user-common"

export default class OffersUtil {
    public static getOfferTextForPreDefinedOffers(offerId: string): string {
        switch (offerId) {
            case "fu7-JeI6A": {
                return `Upto 200 fitcash on orders above ${RUPEE_SYMBOL}400`
            }
            case "KnDD2bTyP": {
                return `Upto 60 fitcash on orders above ${RUPEE_SYMBOL}250`
            }
            case "tbqZFA-6N": {
                return `Upto 75 fitcash on orders above ${RUPEE_SYMBOL}200`
            }
            case "sW3k1M4Ge": {
                return `Upto 150 fitcash on orders above ${RUPEE_SYMBOL}500`
            }
        }
        return undefined
    }

    public static async isFreeDeliveryOfferPresent(offerService: OfferServiceV3, offerIds: string[]): Promise<boolean> {
        if (_.isEmpty(offerIds)) {
            return false
        }
        return _.values((await offerService.getOffersByIds(offerIds)).data).some(offer => offer.noDeliveryCharge)
    }

    public static async isFreePackagingOfferPresent(offerService: OfferServiceV3, offerIds: string[]): Promise<boolean> {
        if (_.isEmpty(offerIds)) {
            return false
        }
        return _.values((await offerService.getOffersByIds(offerIds)).data).some(offer => offer.noPackagingCharge)
    }

    public static getCultPackPricesWithProductIds(offerService: OfferServiceV3, request: {
        userInfo: UserInfo;
        cityId: string;
        source: string;
        centerId?: string;
        centerServiceId?: string;
        cultCityId: number;
        userSegmentIds?: string[];
        applyGroupOffers?: boolean;
    }): (productIds: string[]) => Promise<CultProductPricesResponse> {
        return (productIds: string[]) => {
            return offerService.getCultPackPrices({
                ...request,
                productIds
            })
        }
    }
}
