import { AggregationRange, IMetric } from "@curefit/metrics-common"
import * as _ from "lodash"
import * as momentTz from "moment-timezone"
import { TimeUtil, Timezone } from "@curefit/util-common"

const MONTH_RANGE_ARRAY = [
  { start: 1, end: 7, days: 7 },
  { start: 8, end: 16, days: 9 },
  { start: 17, end: 24, days: 8 },
  { start: 25, end: 31, days: 7 }
]

export function getDateSortedGraphData(graphData: { [id: string]: number }, aggregationRange: AggregationRange, fromDate: string, toDate: string,
  weeklyAggregationEnabled: boolean = false, timezone: Timezone = TimeUtil.IST_TIMEZONE): Array<{ key: string, value: number }> {
  const graphArray = _.map(graphData, (item, key) => {

    const value = aggregationRange === AggregationRange.YEAR || aggregationRange === AggregationRange.SEMI_ANNUAL ? key : momentTz.tz(new Date(Number(key)), timezone).format("YYYY-MM-DD")
    return {
      key: value,
      value: item
    }
  })

  // sorting the array based on timestamp
  const graph = graphArray.sort((a, b) => {
    if (aggregationRange === AggregationRange.YEAR) {
      return Number(a.key) - Number(b.key)
    } else {
      return momentTz.tz(a.key, timezone).diff(momentTz.tz(b.key, timezone))
    }
  })

  let finalGraphArray: Array<{ key: string, value: number }> = []

  // adding empty date elements for the graph
  switch (aggregationRange) {
    case AggregationRange.WEEK:
      finalGraphArray = getFinalGraphArrayForWeekAndMonth(fromDate, toDate, graph, timezone)
      break
    case AggregationRange.MONTH:
      const weeklyAggregatedGraph: { key: string, value: number }[] = []
      let currentIndex = 0
      let startDate = TimeUtil.getMomentForDateString(fromDate, timezone)
      let endDate = TimeUtil.getMomentForDateString(fromDate, timezone).add(MONTH_RANGE_ARRAY[0].days, "days")
      for (let week = 0; week < 4; week++) {
        const weekAverageData = getWeekAverageData(graph, startDate.format("YYYY-MM-DD"), endDate.format("YYYY-MM-DD"), toDate, week, currentIndex, timezone)
        weeklyAggregatedGraph.push({
          key: weekAverageData.key,
          value: (weekAverageData.totalData / weekAverageData.totalValues)
        })
        currentIndex = currentIndex + weekAverageData.totalValues
        startDate = startDate.add(MONTH_RANGE_ARRAY[week].days, "days")
        endDate = week < 3 ? endDate.add(MONTH_RANGE_ARRAY[week + 1].days, "days") : endDate
      }
      finalGraphArray = weeklyAggregationEnabled ? getFinalGraphArrayForMonth(weeklyAggregatedGraph) : getFinalGraphArrayForWeekAndMonth(fromDate, toDate, graph, timezone)
      break
    case AggregationRange.YEAR:
      finalGraphArray = getFinalGraphArrayForYear(graph)
      break
    case AggregationRange.SEMI_ANNUAL:
      const startMonth = TimeUtil.getMomentForDateString(fromDate, timezone).month()
      const endMonth = TimeUtil.getMomentForDateString(toDate, timezone).month()
      finalGraphArray = getFinalGraphArrayForYear(graph, startMonth, endMonth)
      break
  }

  return finalGraphArray
}

export function getFinalGraphArrayForWeekAndMonth(fromDate: string, toDate: string, graph: Array<{ key: string, value: number }>, timezone: Timezone = TimeUtil.IST_TIMEZONE) {

  let i = 0
  const finalGraphArray = []
  for (const date = TimeUtil.getMomentForDateString(fromDate, timezone); date.isBefore(TimeUtil.getMomentForDateString(toDate, timezone).add(1, "days")); date.add(1, "days")) {
    if (graph.length < 1 || date.isBefore(momentTz.tz(graph[i].key, timezone)) || date.isAfter(momentTz.tz(graph[i].key, timezone))) {
      finalGraphArray.push({
        key: date.format("YYYY-MM-DD"),
        value: undefined
      })
    } else {
      finalGraphArray.push(graph[i])
      if (i < graph.length - 1) {
        i = i + 1
      }
    }
  }
  return finalGraphArray
}

export function getFinalGraphArrayForMonth(graph: Array<{ key: string, value: number }>, startWeek: number = 0, endWeek: number = 4) {
  let i = 0
  const filteredGraph = _.filter(graph, item => parseInt(item.key) >= startWeek)
  const finalGraphArray: Array<{ key: string, value: number }> = []
  for (let week = startWeek; week < endWeek; week++) {
    if (filteredGraph.length > 0 && week === parseInt(filteredGraph[i].key)) {
      finalGraphArray.push({
        key: filteredGraph[i].key,
        value: filteredGraph[i].value
      })
      if (i < filteredGraph.length - 1) {
        i = i + 1
      }
    } else {
      finalGraphArray.push({
        key: filteredGraph.toString(),
        value: undefined
      })
    }
  }
  return finalGraphArray
}

export function getFinalGraphArrayForYear(graph: Array<{ key: string, value: number }>, startMonth: number = 0, endMonth: number = 11) {
  let i = 0
  const filteredGraph = _.filter(graph, item => parseInt(item.key) >= startMonth)
  const finalGraphArray: Array<{ key: string, value: number }> = []
  for (let month = startMonth; month <= endMonth; month++) {
    if (filteredGraph.length > 0 && month === parseInt(filteredGraph[i].key)) {
      finalGraphArray.push({
        key: filteredGraph[i].key,
        value: filteredGraph[i].value
      })
      if (i < filteredGraph.length - 1) {
        i = i + 1
      }
    } else {
      finalGraphArray.push({
        key: month.toString(),
        value: undefined
      })
    }
  }
  return finalGraphArray
}

function getWeekAverageData(graph: Array<{ key: string, value: number }>, fromDate: string, toDate: string, finalEndDate: string, week: number, index: number,
  timezone: Timezone = TimeUtil.IST_TIMEZONE): { key: string, totalData: number, totalValues: number } {
  let totalData: number = 0
  let i = 0
  for (const date = TimeUtil.getMomentForDateString(fromDate, timezone); date.isBefore(TimeUtil.getMomentForDateString(toDate, timezone)) && date.isSameOrBefore(TimeUtil.getMomentForDateString(finalEndDate, timezone)); date.add(1, "days")) {
    if (graph.length < (index + 1) || date.isBefore(TimeUtil.getDefaultMomentForDateString(graph[index].key, timezone)) || date.isAfter(TimeUtil.getDefaultMomentForDateString(graph[index].key, timezone))) {
      totalData = totalData
    }
    else {
      totalData += graph[index].value
      if (i < graph.length - 1) {
        index++
      }
      i = i + 1
    }
  }
  return { key: week.toString(), totalData: totalData, totalValues: i }
}

export function getYAxisRange(values: { [id: string]: number }, metric: IMetric) {

  const arr = Object.keys(values).map(key => values[key])

  let min, max, interval
  if (arr.length === 1) {

    const value = Math.floor(Math.min(...arr))
    const interval = Math.floor(value * 0.05)

    return [
      value - 2 * interval,
      value - interval,
      value,
      value + interval
    ]

  } else if (arr.length > 1) {

    const a = Math.floor(Math.min(...arr))
    const b = Math.ceil(Math.max(...arr))

    min = Math.floor(a - (b - a) / 2)
    max = Math.ceil(a + (b - a) / 2)

    interval = Math.ceil((max - min) / 2)
    return [
      min,
      min + interval,
      min + 2 * interval,
      min + 3 * interval
    ]
  } else {
    min = metric.rangeMin
    max = metric.rangeMax
    return [
      Math.floor(min),
      Math.round(min + (max - min) / 3),
      Math.round(min + 2 * (max - min) / 3),
      Math.ceil(max)
    ]
  }
}
