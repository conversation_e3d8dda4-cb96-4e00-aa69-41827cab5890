import { City } from "@curefit/location-common"
import { AppSourceType, Session, SessionInfo, UserContext } from "@curefit/userinfo-common"
import { MiniAppId, Relation, SubUserRelation, Tenant, User } from "@curefit/user-common"
import { AppTenant, OrderSource, UserAgentType as UserAgent, VerticalType } from "@curefit/base-common"
import { Order, OrderProduct } from "@curefit/order-common"
import { Constants, MealUtil, RUPEE_SYMBOL } from "@curefit/base-utils"
import { AlertInfo } from "../common/views/WidgetView"
import { Action, LoginPageTypes, PageTypes } from "@curefit/apps-common"
import { CatalogueProduct as CGCatalogueProduct } from "@curefit/gear-common"
import {
    EMAIL_NOT_FOUND_ERROR_CODE,
    MOBILE_NUMBER_NOT_FOUND_ERROR_CODE,
    NAME_NOT_FOUND_ERROR_CODE,
} from "@curefit/error-client"
import {
    DIY_WITH_REMINDER_SUPPORTED_VERSION_ANDROID,
    DIY_WITH_REMINDER_SUPPORTED_VERSION_IOS,
    ISegmentService
} from "@curefit/vm-models"
import * as _ from "lodash"
import { ExternalDeviceType } from "@curefit/metrics-common"
import { ClientDefaults } from "@curefit/user-client"
import { HamletBusiness } from "@curefit/hamlet-node-sdk"
import { ProductType } from "@curefit/product-common"
import { ConfigQuery, HamletContext, Payload, UserAllocation, UserAssignment } from "@curefit/hamlet-common"
import { BASE_TYPES, ILogger, Logger } from "@curefit/base"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { EatClpOverrideConfig } from "@curefit/config-mongo"
import { OfferAddon, OfferV2 } from "@curefit/offer-common"
import * as express from "express"
import { ICityService, ICountryService } from "@curefit/location-mongo"
import { ConsultationProduct, SUB_CATEGORY_CODE } from "@curefit/care-common"
import { WalletBalance } from "@curefit/fitcash-common"
import { ICFAPICityService } from "../city/ICFAPICityService"
import { ErrorCodes } from "../error/ErrorCodes"
import { eternalPromise, TimeUtil } from "@curefit/util-common"
import {
    STORE_TAB_APP_SUPPORT_EXTERNAL,
    STORE_TAB_APP_SUPPORT_INTERNAL,
    STORE_TAB_CP_ANDROID,
    STORE_TAB_CP_IOS,
} from "../gear/constants"
import { AppConfigUtil } from "../appConfig/AppConfigService"
import kernel from "../../config/ioc/ioc"
import { IDIYFulfilmentService } from "@curefit/diy-client"
import { DIYUserAttribute, DIYUserAttributeState, LiveClass } from "@curefit/diy-common"
import { IHamletService } from "@curefit/hamlet-client"
import { RollbarService } from "@curefit/error-common"
import { ProductFeedback } from "@curefit/cultsport-feedback-client"
import { IAppConfigStoreService } from "../appConfig/AppConfigStoreService"
import { inject } from "inversify"
import { Membership } from "@curefit/membership-commons"
import AuthUtil from "./AuthUtil"
import { BookingSearchRequest } from "@curefit/cult-common/dist/src/Models"
import { CultMembership } from "@curefit/cult-client"
import { BookingState } from "@curefit/cult-common"
import { ICultServiceOld as ICultService } from "@curefit/cult-client/dist/src/ICultServiceOld"
import { ISegmentationCacheClient } from "@curefit/segmentation-service-client"
import * as moment from "moment/moment"
import { IMaxmindService } from "@curefit/maxmind-client"

export const CULTSPORT_BASE_URL: any = {
    "STAGE": "https://stage.cultsport.com",
    "ALPHA": "https://alpha.cultsport.com",
    "PRODUCTION": "https://cultsport.com",
}

export const ENTERPRISE_SSO_API_KEYS = new Map<string, any>()
ENTERPRISE_SSO_API_KEYS.set("caa8b54a-eb5e-4134-8ae2-a3946a428ec7", {"corporateCode": "BAJAJ0224", "base_url": "bajajfinserv.cult.fit"})
ENTERPRISE_SSO_API_KEYS.set("5042eaa9-f519-4c0b-8d2a-49d70989565f", {"corporateCode": "TestCultpassCORP", "base_url": "bajajfinserv.cult.fit"})

const IOS_SLEEP_CONFIG_UPDATE_VERSION_V1: number = 5.80

const APP_SUPPRT_VERSION_FOR_REFERRAL_INVITE_OFFER = 8.20

const ANDROID_JUSPAY_PAYTM_API = 7.43
const ANDROID_PAYPAL_VERSION = 7.46
const IOS_PAYPAL_VERSION = 7.46
const ANDROID_PAYPAL_PROFILE_LINK_VERSION = 7.46 // Todo -- Arjun - Fix this once feature is live on client
const IOS_PAYPAL_PROFILE_LINK_VERSION = 7.46 // Todo -- Arjun - Fix this once feature is live on client

export const CULT_BANNER_RATIO_MAP = new Map<UserAgent, string>()
CULT_BANNER_RATIO_MAP.set("APP", "1:1")
CULT_BANNER_RATIO_MAP.set("MBROWSER", "1:1")
CULT_BANNER_RATIO_MAP.set("DESKTOP", "3.2:1")

export const EAT_BANNER_RATIO_MAP = new Map<UserAgent, string>()
EAT_BANNER_RATIO_MAP.set("APP", "1242:743")
EAT_BANNER_RATIO_MAP.set("MBROWSER", "1:1")
EAT_BANNER_RATIO_MAP.set("DESKTOP", "3.2:1")

export const MIND_BANNER_RATIO_MAP = new Map<UserAgent, string>()
MIND_BANNER_RATIO_MAP.set("APP", "1:1")
MIND_BANNER_RATIO_MAP.set("MBROWSER", "1:1")
MIND_BANNER_RATIO_MAP.set("DESKTOP", "3.2:1")
export const CARE_BANNER_RATIO_MAP = new Map<UserAgent, string>()

const DIY_WITH_PLAY_LARGE_VERSION_ANDROID = 7.20
const DIY_WITH_PLAY_LARGE_VERSION_IOS = 7.20

const COMBINED_SCORE_GOALS_MAX_VERSION = 7.21

export const M1_ONBOARDING_SEGMENT_PROD = "317d9e9e-c41c-4da5-bffc-7b69b9849833"
export const M1_ONBOARDING_SEGMENT_STAGE = "a2109773-2b67-4105-9d08-64b1c57ff656"

export const DIY_RECIPE_CONTENT_CONSUME_ERROR_VERSION = 7.28

const DEVICES_MAP = new Map<ExternalDeviceType, any>()
DEVICES_MAP.set("VOICE_BP", { "ios": 10.0, "android": 10.0 })
DEVICES_MAP.set("GLUCOMETER", { "ios": 10.0, "android": 10.0 })

export const AWS_S3_BASE_URL = "https://s3.ap-south-1.amazonaws.com/"

export const SHOW_INSTRUCTION_MODAL_SUPPORTED_APP_VERSION = 7.21

export const PLAN_TABS_IN_USER_STATUS_VERSION = 7.21

const LIVE_PT_TRAINER_RECO_APP_VERSION = 8.26
const CHANGE_TRAINER_TOOLTIP_ANNOUNCEMENT_VERSION = 8.32

export const LIVE_SGT_SUPPORTED_VERSION = 8.35
export const MY_LIVE_CLASSES_WIDGET_V2_SUPPORTED = 8.40
export const LIVE_SGT_READ_MORE_SUPPORTED_VERSION = 8.38

export const PACK_BROWSE_PACK_INFO_SUPPORTED_VERSION = 8.39
export const LIVE_SGT_CHECKOUT_CHANGE_SUPPORTED_VERSION = 8.39

const VOD_SUPPORT_APP_VERSION = 8.35
const VOD_INTERNATIONAL_APP_VERSION = 1.2

export const FREEMIUM_APP_SUPPORT_VERSION = 8.40
export const CARE_BRAND_PAGE_V2_SUPPORTED = 8.47
export const CARE_COLLAPSIBLE_OFFER_SUPPORTED = 8.48
export const SLOT_TIMER_WIDGET_SUPPORTED = 8.51
export const STRIKE_THROUGH_PRICE_SUPPORTED_VERSION = 8.59
export const UNMAPPED_TESTS_WIDGET_SUPPORTED = 8.62

export const FOOD_MARKETPLACE_ROLLOUT_HAMLET_EXP_ID = "484"
export const FOOD_MARKETPLACE_SUPPORT_APP_VERSION = 8.92
export const FOOD_MARKETPLACE_IN_APP_ORDER_TRACKING_SUPPORT = 8.94
export const FOOD_MARKETPLACE_NEW_MEAL_CARD_SUPPORT = 8.94
export const CLASS_SLOT_ICON_URL_SUPPORTED_APP_VERSION = 8.99

export const TRANSFORM_CART_PAY_NOW_SUPPORTED_VERSION = 8.99

export const MIND_THERAPY_AURORA_THEME_SUPPORTED = 9.17

const INSTRUCTIONS_SUPPORT_SUPPORTED_APP_VERSION = 9.21
const BADGE_WALL_WIDGET_SUPPORTED_APP_VERSION = 9.26

export const MIND_THERAPY_AURORA_THEME_GUEST_USER_SUPPORTED = 9.21
export const CONSULTATION_FLOATING_JOIN_BUTTON_ADDED = 9.42

export const GYMPT_BOOK_SESSION_SUPPORTED = 9.37
export const AURORA_ALL_CENTERS_PAGE_SUPPORTED = 9.57
export const BOXING_INSTRUCTION_MODAL_SUPPORTED = 9.57
export const CULT_FLUTTER_BOOKING_FLOW_DISABLE_SUPPORT_VERSION = 9.61
export const AURORA_ALL_CENTERS_PAGE_PROD_EXP = "1145"
export const CULT_FLUTTER_BOOKING_FLOW_APP_VERSION = 9.86
export const CULTPASS_PLAY_USERS_PROD = "f5ef2862-ff42-43d3-a7d9-872eac3e68bf"
export const CULTPASS_PLAY_USERS_STAGE = "eb6807da-3108-4b1f-803e-c96622a0c2a3"
export const ONBOARDING_FORM_SEGMENT_ID = "a7d0f4a2-315f-4771-84b0-e42cb26fd90c"
export const ONBOARDING_FORM_ID = "user_onboarding"
export const showSkuPlusModalElitePlusKey = "showSkuPlusModalElitePlusKey"
export const showSkuPlusModalProPlusKey = "showSkuPlusModalProPlusKey"
export const noShowSkuPlusModalState = "no_show"

export interface ExperimentType {
    stage: string
    production: string
    integration?: string
    local?: string
    alpha?: string
}
export interface SleepConfig {
    startTimeMin: string
    startTimeMax: string
    endTimeMin: string
    endTimeMax: string
    disturbanceLevel: number
    minContinousSleep: number
    groupInterval: number
}

export interface PlanTab {
    pageId: string,
    name: string
}

export interface PlanTabsResponse {
    planTabs: PlanTab[],
    activeTabIndex: number
}


const ANDROID_SLEEP_CONFIG: SleepConfig = {
    startTimeMin: "20:00",
    startTimeMax: "06:00",
    endTimeMin: "23:59",
    endTimeMax: "12:00",
    disturbanceLevel: 15,
    minContinousSleep: 6,
    groupInterval: 300000
}

const IOS_SLEEP_CONFIG: SleepConfig = {
    startTimeMin: "20:00",
    startTimeMax: "06:00",
    endTimeMin: "23:59",
    endTimeMax: "12:00",
    disturbanceLevel: 3,
    minContinousSleep: 6,
    groupInterval: 300000
}

export const CUREFIT_WHITE_LOGO = "/image/icons/cult/curefit-white-logo.png"
export const CUREFIT_LOGO = "/image/icons/cult/curefit-logo.png"

export const getTrialClassMissedInterventionExperimentHamletId = (): string => {
    if (process.env.ENVIRONMENT === "PRODUCTION") {
        return "63"
    }
    return "107"
}

export const getCalendarSchedulingExperimentHamletId = (): string => {
    if (process.env.ENVIRONMENT === "PRODUCTION") {
        return "183"
    }
    return "166"
}

export const getEatWelcomeOfferAnnouncementExperimentHamletId = (): string => {
    if (process.env.ENVIRONMENT === "PRODUCTION") {
        return "145"
    }
    return "128"
}

export const AppFont = {
    Bold: "BrandonText-Bold", // App uses Inter-Bold
    Regular: "BrandonText-Regular", // Inter-Regular
    RegularItalic: "BrandonText-RegularItalic", // Inter-Italic
    Medium: "BrandonText-Medium", // Inter-Medium
    Black: "BrandonText-Black", // Inter-Bold
}


export const TRIAL_CLASS_MISSED_NO_SHOW_APP_SUPPORT = 7.87
export const TRIAL_CLASS_MISSED_HAMLET_ID = getTrialClassMissedInterventionExperimentHamletId()
export const MAX_TRIAL_CLASS_ALLOCATED = 3
export const CALENDAR_SCHEDULING_HAMLET_ID = getCalendarSchedulingExperimentHamletId()

const NEW_GMS_VERSION_ANDROID = 7.21
const NEW_GMS_VERSION_IOS = 7.21

const NEW_CLASS_BOOKING_VERSION = 7.22

const MANDATORY_START_DATE_VERSION_ANDROID = 7.22
const MANDATORY_START_DATE_VERSION_IOS = 7.22

const FITCASH_ON_REVIEW_PAGE = 7.73

const NEW_RETURN_ORDER_FLOW = 7.62

const PAUSE_EDIT_END_DATE = 7.68

const NEW_PAUSE_PACK_DATE_VERSION = 7.55
const NEW_PAUSE_PACK_DATE_ANDROID_INTERNAL_CP_VERSION = 306
const NEW_PAUSE_PACK_DATE_IOS_INTERNAL_CP_VERSION = 280

const NEW_PACK_ORDER_CONFIRMATION = 7.71
const FITCLUB_V2_PRE_MEMBERSHIP_PAGE = 7.69
const SKIP_CREATE_PATIENT_SUPPORTED_VERSION = 8.20

const DIY_WOD_SUPPORTED_VERSION = 8.21
const NEW_LIVE_DIY_PACK_SUPPORTED_VERSION = 8.23

const CALL_REMINDER_VERSION = 7.66
export const GYMFIT_LAUNCH_EXPERIMENT = "41"
export const GYMFIT_LAUNCH_STAGE_EXPERIMENT = "118"
export const PACK_CHANGE_START_DATE_IOS_APP_VERSION: number = 7.28
export const PACK_CHANGE_START_DATE_ANDROID_APP_VERSION: number = 7.28
export const PACK_UPGRADE_ANDROID_APP_VERSION: number = 7.87
export const PACK_UPGRADE_IOS_APP_VERSION: number = 7.87
export const GYM_PACK_UPGRADE_APP_VERSION: number = 9.25
export const CAPTCHA_APP_VERSION: number = 8.18
export const CITY_SELECTION_ACTION_IOS = 7.44
export const CITY_SELECTION_ACTION_ANDROID = 7.44

export const INAPP_NOTIFICATION_APPID = "CUREFIT"

export const NEW_CENTER_RESPOSE_APP_VERSION = 7.43

export const CHILD_BOOKING_SUPPORT_ANDROID = 7.45
export const CHILD_BOOKING_SUPPORT_IOS = 7.45

export const CARE_ANNOUNCEMENT_SUPPORTED_APP_VERSION = 7.46
export const CARE_ANNOUNCEMENT_SUPPORTED_CP_VERSION_INTERNAL_ANDROID = 296
export const CARE_ANNOUNCEMENT_SUPPORTED_CP_VERSION_INTERNAL_IOS = 268
export const CARE_ANNOUNCEMENT_SUPPORTED_CP_VERSION_EXTERNAL_ANDROID = 128
export const CARE_ANNOUNCEMENT_SUPPORTED_CP_VERSION_EXTERNAL_IOS = 118

export const CULT_CAFE_VERSION_ANDROID = 7.58
export const CULT_CAFE_VERSION_IOS = 7.58
export const CULT_CAFE_VERSION_CP_ANDROID = 135
export const CULT_CAFE_VERSION_CP_IOS = 127

const GYMFIT_VERSION = 8.12
const GYMFIT_CP_VERSION_ANDROID = 372
const GYMFIT_CP_VERSION_IOS = 349

export const WHOLE_FIT_VERSION_ANDROID = 7.68
export const WHOLE_FIT_VERSION_IOS = 7.68
export const WHOLE_FIT_INTERNAL_VERSION_CP_ANDROID = 317
export const WHOLE_FIT_INTERNAL_VERSION_CP_IOS = 287
export const WHOLEFIT_C1_CATEGORISATION_SUPPORT = 8.0

export const PULSE_IOS_APP = 7.50
export const PULSE_ANDROID_APP = 7.50
export const PULSE_MUSCLE_GROUP_ANDROID_APP = 7.62
export const PULSE_MUSCLE_GROUP_IOS_APP = 7.62
export const PULSE_RENTAL_PACK_IOS = 7.71
export const PULSE_RENTAL_PACK_ANDROID = 7.71
export const PULSE_NOTE_WIDGET_SUPPORTED_IOS = 7.76
export const PULSE_NOTE_WIDGET_SUPPORTED_ANDROID = 7.76
export const PULSE_RENTAL_PACK_VIDEO_IOS = 7.76
export const PULSE_RENTAL_PACK_VIDEO_ANDROID = 7.76
export const PULSE_OPT_OUT = 7.82
export const PULSE_TRIAL_OPT_OUT_ANDROID = 7.85
export const PULSE_TRIAL_OPT_OUT_IOS = 7.85
export const PULSE_TRIAL_OPT_OUT_CP_ANDROID = 156
export const PULSE_TRIAL_OPT_OUT_CP_IOS = 147

export const MEMBERSHIP_AUDIT_TRAIL_IOS = 8.16
export const MEMBERSHIP_AUDIT_TRAIL_ANDROID = 8.16
export const MEMBERSHIP_AUDIT_TRAIL_IOS_CODEPUSH = 361
export const MEMBERSHIP_AUDIT_TRAIL_ANDROID_CODEPUSH = 385

export const REDEEM_VOUCHER_SUPPORT_ANDROID = 7.58
export const REDEEM_VOUCHER_SUPPORT_IOS = 7.58

const CENTER_SHUTDOWN_SUPPORTED_ANDROID = 7.59
const CENTER_SHUTDOWN_SUPPORTED_IOS = 7.59

const SUPPORT_TICKETS_VIEW_SUPPORTED = 7.59

export const DROPOUT_SUPPORTED_VERSION = 7.58

export const PAYMENT_FITCASH_TEXT_SUPPORT_ANDROID = 7.59
export const PAYMENT_FITCASH_TEXT_SUPPORT_IOS = 7.59

export const DELIVERY_STRIKEOFF_ANDROID = 7.61
export const DELIVERY_STRIKEOFF_IOS = 7.61

export const MEMBERSHIP_CONTEXT_SUPPORTED_VERSION = 7.61

export const DIY_SESSION_PAGE_REMOVAL_VERSION = 7.66
export const LIVE_CLASS_SUPPORT_VERSION = 7.69
export const LIVE_CLASS_SUPPORT_CP_VERSION_INTERNAL_IOS = 307
export const LIVE_CLASS_SUPPORT_CP_VERSION_EXTERNAL_IOS = 132
export const LIVE_CLASS_SUPPORT_CP_VERSION_INTERNAL_ANDROID = 329
export const LIVE_CLASS_SUPPORT_CP_VERSION_EXTERNAL_ANDROID = 140

export const LIVE_PACK_SUPPORT_VERSION_IOS = 8.29
export const LIVE_PACK_SUPPORT_VERSION_ANDROID = 8.29

export const VIDEO_CARD_LAYOUT_SUPPORT_VERSION = 7.77
export const LIVE_CALENDAR_SUPPORT_VERSION = 7.78

export const WIDGETIZED_ORDER_CONFIRMATION_VERSION = 7.66

export const CLEAR_CART_FLAG_VERSION = 7.67
export const FITCLUB_ACTIVE_MEMBERSHIP_RENEWAL_VERSION = 7.66

export const FITCLUB_SCRATCH_CARD_VERSION = 7.67
export const FITCLUB_SCRATCH_CARD_EXTERNAL_VERSION = 7.68
const FITCLUB_SCRATCH_CARD_INTERNAL_CP_ANDROID = 325
const FITCLUB_SCRATCH_CARD_INTERNAL_CP_IOS = 293
const FITCLUB_SCRATCH_CARD_EXTERNAL_CP_ANDROID = 138
const FITCLUB_SCRATCH_CARD_EXTERNAL_CP_IOS = 129
export const FITCLUB_SCRATCH_CARD_VERSION_RELEASE_ALL = 7.69
export const NUTRITIONAL_TAG__FILTER_VERSION = 7.67
export const EAT_SOLDOUT_CART_ITEM_REMOVAL_SUPPORTED_VERSION = 7.70
export const THERAPIST_RECOMMENDATION_SUPPORTED_APP_VERSION = 7.69
export const EAT_VARIANTS_SUPPORTED_APP_VERSION = 7.85

const FITCLUB_CHECKOUT_ALERT_VERSION = 7.69

const FITCLUB_CHECKOUT_CP_VERSION_ANDROID = 139
const FITCLUB_CHECKOUT_CP_VERSION_IOS = 130

export const TRANSFER_MEMBERSHIP_SUPPORTED_VERSION = 7.69
export const NUX_SUPPORTED_VERSION = 7.71
export const NEW_WOD_VIEW_WIDGET_SUPPORTED_VERSION = 7.85
export const AURORA_THEME_WOD_VIEW_WIDGET_SUPPORTED_VERSION = 9.25
export const AURORA_THEME_WOD_VIEW_WIDGET_SEE_MORE_ACTION_SUPPORTED_VERSION = 9.29

export const GEAR_BRAND_NAME_SUPPORT_ANDROID = 7.75
export const GEAR_BRAND_NAME_SUPPORT_IOS = 7.75

export const TRUNCATE_GEAR_BRAND_NAME_ON_ANDROID_TILL = 7.76
export const TRUNCATE_GEAR_BRAND_NAME_ON_IOS_TILL = 7.76

export const ADD_BUDDY_INTERNATIONAL_APP_VERSION = 7.74
export const SHARE_ACTION_WIDGET_SUPPORTED_VERSION = 7.80
export const UPCOMING_ITEM_FOOTERS_ARRAY_SUPPORTED_VERSION = 7.80
export const UPCOMING_ITEM_TOOLTIP_SUPPORTED_VERSION = 7.80
export const BUDDIES_JOINING_CLASS_WIDGETS_SUPPORTED_VERSION = 7.88
export const BUDDIES_JOINING_CLASS_WIDGETS_SUPPORTED_ANDROID_CP_VERSION = 375
export const BUDDIES_JOINING_CLASS_WIDGET_SUPPORTED_IOS_CP_VERSION = 352
export const CULT_LIVE_USER_PROFILE_SUPPORTED_APP_VERSION = 8.17
export const CULT_LIVE_USER_PROFILE_SUPPORTED_ANDROID_CP_VERSION = 387
export const CULT_LIVE_USER_PROFILE_SUPPORTED_IOS_CP_VERSION = 363
export const CULT_LIVE_CLP_REPORT_CAROUSEL_SUPPORTED_VERSION = 8.19
export const CULT_LIVE_CLP_REPORT_CAROUSEL_EXTERNAL_SUPPORTED_VERSION = 8.21
export const CULT_LIVE_CLP_REPORT_CAROUSEL_ANDROID_CP_VERSION = 392
export const CULT_LIVE_CLP_REPORT_CAROUSEL_IOS_CP_VERSION = 368
export const LIVE_CLASS_BOOKING_PAGE_SUPPORTED_VERSION = 8.17
export const CULT_LIVE_LAZY_INVITE_LINK_SUPPORTED_VERSION = 8.20
export const DIY_REPORT_SUPPORTED = 8.27
export const LIVE_NEW_CONFIRMATION_CELL_SUPPORTED_VERSION = 8.21
export const LIVE_NEW_CONFIRMATION_DESIGN_SUPPORTED_VERSION = 8.26
export const CULT_LIVE_MOMENT_OF_DAY_SUPPORTED_VERSION = 8.23
export const LIVE_MOMENT_ON_REPORT_SUPPORTED_VERSION = 8.24
export const LIVE_SECOND_TRAINER_IMAGE_SUPPORTED_VERSION = 8.24
export const LIVE_NEW_CLASS_REPORT_SUPPORTED_VERSION = 8.24
export const RELAXING_MOMENT_AND_REPORT_SUPPORTED_VERSION = 8.24
export const SOCIAL_LEAGUES_WALL_HEADER_FOOTER_WIDGETIZED_VERSION = 8.28
export const SOCIAL_LEAGUES_WALL_HEADER_FOOTER_WIDGETIZED_ANDROID_CP_EXT = 168
export const SOCIAL_LEAGUES_WALL_HEADER_FOOTER_WIDGETIZED_IOS_CP_EXT = 159
export const SOCIAL_LEAGUES_WALL_HEADER_FOOTER_WIDGETIZED_ANDROID_CP_INT = 405
export const SOCIAL_LEAGUES_WALL_HEADER_FOOTER_WIDGETIZED_IOS_CP_INT = 379
export const SOCIAL_SQUAD_CHALLENGES_SUPPORTED_VERSION = 8.34
export const SQUAD_CHALLENGES_CAROUSEL_SUPPORTED_VERSION = 8.35
export const SQUAD_LEADERBOARD_PREVIEW_SUPPORTED_VERSION = 8.35
export const CONTACTS_SYNC_SUPPORTED_VERSION = 8.43
export const CONTACTS_SYNC_HAMLET_EXPERIMENT_STAGE = "180"
export const CONTACTS_SYNC_HAMLET_EXPERIMENT_PROD = "162"
const YOGA_REPORT_APP_SUPPORTED_VERSION = (isInternationalApp: boolean) => isInternationalApp ? 1.7 : 8.53
const YOGA_EM_SUPPORTED_VERSION = (isInternationalApp: boolean) => isInternationalApp ? 1.7 : 8.52
const YOGA_REPORT_INTERNATIONAL_APP_SUPPORTED_VERSION = 1.6
const YOGA_REPORT_HAMLET_PROD_EXPERIMENT_ID = "287"
const LIVE_VIDEO_PLAYER_BACKGROUND_SUPPORTED_VERSION = (isInternationalApp: boolean, isSugarFitOrUltraFitApp: boolean) => isInternationalApp ? 1.60 : isSugarFitOrUltraFitApp ? 1.17 : 8.51
export const DIY_ENERGY_METER_SUPPORTED_VERSION = (isInternationalApp: boolean) => isInternationalApp ? 1.6 : 8.51
export const USER_JOURNEY_SUPPORTED_VERSION = 8.56
export const CANCEL_SQUAD_INVITE_SUPPORTED_VERSION = 8.43
export const CULT_WORKOUT_TAB_PAGE_INDEX_FIX_NEEDED_MIN_VERSION = 8.27
export const CULT_WORKOUT_TAB_PAGE_INDEX_FIX_NEEDED_MAX_VERSION = 8.29
export const LIVE_MOMENT_OF_DAY_V2_SUPPORTED_VERSION = 8.31
export const ONBOARDING_CHECKOUT_HAMLET_EXPERIMENT = "24"
export const EAT_MEAL_SMALL_CARD_SUPPORTED_VERSION = 7.73

export const EAT_FLOATING_MENU_EXPERIMENT = "66"

export const APP_LAUNCH_SKIP_LOGIN_SUPPORTED_VERSION = 7.69

export const WORKOUT_VIEW_SUPPORTED_APP_VERSION = 7.70
export const FITNESS_REPORT_VERSION_SUPPORTED = 7.7004

export const SUBSCRIPTION_ADDON_SUPPORT_VERSION = 7.73

export const SUBSCRIPTION_ADDON_CP_VERSION_ANDROID = 333
export const SUBSCRIPTION_ADDON_CP_VERSION_IOS = 310

export const EAT_CLP_RECO_HAMLET_EXPERIMENT_STAGE = "41"
export const EAT_CLP_RECO_HAMLET_EXPERIMENT = "31"
export const EAT_CLP_RECO_SUPPORT_VERSION = 7.73

export const MIND_THERAPY_CONSULTATION_CARD_ANDROID = 8.22
export const MIND_THERAPY_CONSULTATION_CARD_IOS = 8.22

const EAT_MEAL_CARD_DEFAULT_BUCKET_ID = "1"

export const TABBED_FEEDBACK_FLOW_SUPPORTED_VERSION = 9.36
export const TABBED_FEEDBACK_FLOW_SUPPORTED_VERSION_SAT_MUSIC = 9.74


export const MY_GATE_SUPPORT_VERSION = 7.76
export const FITCLUB_NOT_LOGGED_IN_VERSION = 7.76

export const MY_GATE_CP_VERSION_ANDROID = 339
export const MY_GATE_CP_VERSION_IOS = 316

export const REORDER_EAT_APP_VERSION = 7.78
export const REORDER_EAT_CP_VERSION_ANDROID = 148
export const REORDER_EAT_CP_VERSION_IOS = 138

export const EAT_FLOATING_MENU_APP_VERSION = 8.0

export const RECOMMENDED_EATCLP_NEW_APP_VERSION = 7.80

export const REORDER_EAT_CP_VERSION_ANDROID_INTERNAL = 341
export const REORDER_EAT_CP_VERSION_IOS_INTERNAL = 318

export const DECIMAL_WEIGHT_UPDATE_ENABLED = 7.76
export const DECIMAL_WEIGHT_UPDATE_CP_VERSION_ANDROID = 344
export const DECIMAL_WEIGHT_UPDATE_CP_VERSION_IOS = 323

export const WHOLE_FIT_V2_REVIEW_SUPPORT = 8.0
export const WHOLE_FIT_V2_CP_INTERNAL_VERSION_ANDROID = 365
export const WHOLE_FIT_V2_CP_INTERNAL_VERSION_IOS = 343
export const WHOLE_FIT_V2_PAGE_SIZE = 10
export const WHOLE_FIT_DEFAULT_PAGE_NO = 0

export const EAT_CURRENCY_SUPPORTED_VERSION = 7.79


export const CULT_NEW_CENTER_PAGE_SUPPORTED_VERSION = 7.84

export const EAT_DELIVERY_WINDOW_SUPPORTED_VERSION = 7.81
export const EAT_DELIVERY_WINDOW__CP_VERSION_ANDROID = 153
export const EAT_DELIVERY_WINDOW__CP_VERSION_IOS = 144
export const EAT_DELIVERY_WINDOW__INTERNAL_CP_VERSION_ANDROID = 356
export const EAT_DELIVERY_WINDOW__INTERNAL_CP_VERSION_IOS = 334

export const NEW_CULT_PACK_APP_VERSION = 7.85

export const CHECKOUT_DOT_COM_INTEGRATION_IOS_VERSION = 7.82
export const CHECKOUT_DOT_COM_INTEGRATION_ANDROID_VERSION = 7.81

export const CULT_SOCIAL_EXPERIMENT = "69"
export const CULT_SOCIAL_APP_VERSION = 8.13

export const EAT_WELCOME_OFFER_SUPPORTED = 8.35

export const ORDER_FILTERING_SUPPORTED = 8.17

export const EAT_CLP_ORDER_TRACKING_SUPPORT = 8.17

const MY_REFFERAL_APP_VERSION = 7.81

const LIVE_SESSION_REPORT_VERSION = 7.85
const LIVE_SESSION_REPORT_SELFIE_SHARE_CARD_VERSION = 8.17
const LIVE_SESSION_CALENDAR_EVENT = 8.0

export const EAT_MARKETPLACE_SUPPORTED_VERSION = 7.94

export const EAT_MEAL_ITEM_IMAGE_EXPERIMENT_STAGE = "104"

export const EAT_MEAL_ITEM_IMAGE_EXPERIMENT_PROD = "62"

export const MORE_VERTICALS_SUPPORTED_VERSION_IOS = 7.9985

export const MORE_VERTICALS_SUPPORTED_VERSION_ANDROID = 7.9985

export const LIVE_SESSION_REPORT_FEEDBACK_VERSION = 7.88

const LIVE_SESSION_DETAIL_PAGE_V2_VERSION = 8.15

const MORE_VERTICALS_SUPPORTED_CP_VERSION_ANDROID = 376
const MORE_VERTICALS_SUPPORTED_CP_VERSION_IOS = 353

const CARDLESS_EMI_PAYMENT_OPTION_MIN_VERSION: number = 7.77
const CARDLESS_EMI_PAYMENT_OPTION_MAX_VERSION: number = 8.14
const NEW_CHEKOUT_VERSION: number = 8.12
const EAT_DELIVERY_INSTRUCTION_MIN_VERSION: number = 8.16

export const EAT_CLP_HCM_TIPPING_SUPPORT = 8.18

export const LIVE_PT_EXPERIMENT_ID = "92"
export const LIVE_PT_SUPPORTED_APP_VERSION = 8.18
export const LIVE_PT_SUPPORTED_CODEPUSH_VERSION_ANDROID = 389
export const LIVE_PT_SUPPORTED_CODEPUSH_VERSION_IOS = 365

export const EAT_RECIPE_CLP_NEW_FILTER_WIDGET_SUPPORT = 8.21
export const EAT_RECIPE_CLP_NEW_FILTER_V2_WIDGET_SUPPORT = 8.31
export const EAT_RECIPE_CLP_NEW_FILTER_V2_WIDGET_SUPPORT_CFINTL = 1.4
export const EAT_RECIPE_CLP_NEW_FILTER_V2_WIDGET_SUPPORT_ANDRIOD_CP_VERSION = 407
export const EAT_RECIPE_CLP_NEW_FILTER_V2_WIDGET_SUPPORT_IOS_CP_VERSION = 380

export const LIVE_FIT_V1_TO_CF_APP_VERSION_MAPPING = 8.26
export const LIVE_NEW_PRODUCT_PAGE_SUPPORTED_VERSION = 8.20
export const RECIPE_RATE_AGAIN_NOT_ALLOWED_SUPPORTED = 8.21
export const LIVE_CLASS_BOOKING_PAGE_VERSION = 8.25
export const DATE_WISE_LIVE_SLOT_SELECTION_VERSION = 8.25
export const FORMAT_FILTER_NAME_SUPPORT = 8.26
export const LIVE_NEW_PRODUCT_PAGE_MORE_ACTION_CHANGE_SUPPORTED = 8.21
export const TNC_INVITE_CTA_SUPPORT = 8.25
export const NUTRITIONIST_BUNDLE_SESSION_VERSION = 8.26
export const NUTRITIONIST_BUNDLE_SESSION_VERSION_CP_VERSION_ANDROID = 401
export const NUTRITIONIST_BUNDLE_SESSION_VERSION_CP_VERSION_IOS = 377
export const NUTRITIONIST_PDP_SUPPORTED_VERSION = 8.57

export const LIVE_PT_ONBOARDING_SUPPORTED = 8.23

export const CARE_CLP_NEW_DESIGN_SUPPORTED = 8.25

export const PACK_BROWSE_WIDGET_PRICE_DETAIL_SUPPORTED = 8.79

const DIY_PACK_REDESIGN_SUPPORTED = (isIntl: boolean, isSugarFit: boolean) => isIntl ? 1.7 : isSugarFit ? 1.17 : 8.38

export const RECIPE_VIDEO_AUTOPLAY_EXPERIMENT_ID_PROD = "111"
export const RECIPE_VIDEO_AUTOPLAY_EXPERIMENT_ID_STAGE = "141"
export const RECIPE_SHARE_ACTION_SUPPORT = 8.22
export const RECIPE_VIDEO_AUTOPLAY_SUPPORT = 8.23

export const APP_LAUNCH_SKIP_LOGIN_HAMLET_EXPERIMENT = "24"
export const APP_LAUNCH_SKIP_LOGIN_HAMLET_EXPERIMENT_STAGE = "39"

export const GENDER_SELECTION_MODAL_SUPPORTED = 8.25
export const CARE_PRESCRIPTION_CUSTOM_EMAIL = 8.25
export const NEW_LIVE_ORDER_CONFIRMATION_ICON_SUPPORTED = 8.27
export const NEW_CARE_FILTER_SUPPORT = 8.31

export const ON_DEMAND_COLLECTIONS_VIDEO_DEEPLINK_SHARE_SUPPORT = 8.31
export const ON_DEMAND_AUTOPLAY_SUPPORTED_VERSION = 8.31
export const DYNAMIC_INTERVENTION_SUPPORTED = 8.31

export const LIVE_PT_NEW_PRODUCT_PAGE_SUPPORTED = 8.31
export const ADVERTISEMENT_SUPPORT_VERSION = 8.32

const COACH_FIT_SUPPORTED_APP_VERSION = 8.32

export const CARE_NEW_PRESCRIPTION_LAB_TESTS_DESIGN_SUPPORTED = 8.33
export const CARE_NEW_PRESCRIPTION_MEDICINE_SUPPORTED = 8.35

const DEVOPTIONS_COUNTRY_SELECTOR_SUPPORTED = 1.2
const INTL_WIDGET_NAMES_VERSION = 1.4
const INTL_LIVE_IMMEDIATE_FEEDBACK_VERSION = 1.5

export const CALENDAR_SCHEDULING_SUPPORTED = 8.35
const UNIFIED_LOGIN_SUPPORTED_CFINTL = 1.2
const UNIFIED_LOGIN_SUPPORTED_CF = 8.36
const LIVE_PREMIERE_RELATED_RECIPES_SUPPORTED_VERSION = 8.36

export const THERAPIST_RECOMMENDATION_V2_SUPPORTED_APP_VERSION = 8.36
export const NEW_PRESCRIPTION_LAB_TEST_BOOKING_PAGE = 8.36
export const EAT_CLP_SHOW_MORE_LESS_SUPPORTED_APP_VERSION = 8.40

export const PHLEBO_CALLING_ACTION_SUPPORTED = 8.39

export const EAT_LIVE_VIDEO_SEARCH_RESULT_SUPPORT = 8.39
export const EAT_LIVE_CUSTOM_FILTER_SUPPORT = 8.39

export const SF_WELLNESS_AT_CENTER_SUPPORTED_APP_VERSION = 8.3
export const SF_SUGARFIT_STORE_TAB_SUPPORTED_APP_VERSION = 8.96

export const CARE_AGENT_FILTER_SUPPORT_APP_VERSION = 8.43
export const LIVE_PACK_TRIAL_HAMLET_EXPERIMENT_STAGE = "176"
export const LIVE_PACK_TRIAL_HAMLET_EXPERIMENT_PROD = "230"

export const ZOOM_IOS_HAMLET_EXPERIMENT_PROD = "187"

export const TWILIO_SGT_EXPERIMENT_PROD = "259"
export const EXTRA_FEEDBACK_EXPERIMENT_PROD = "1537"
export const FORCED_FEEDBACK_EXPERIMENT_PROD = "1576"
export const NEW_SGT_BOOKING_PROD = "433"

export const PT_CHAT_EXPERIMENT_PROD = "327"
export const SUGARFIT_APP_LIVE_CLASS_BOOKING_MIN_VERSION = 1.31
export const SUGARFIT_APP_NUX_V2_SUPPORTED_VERSION = 6.3
export const SUGARFIT_APP_SUBSCRIPTION_RENEWAL_V2_SUPPORTED_VERSION = 9.2
export const SUGARFIT_COACH_PENALTY_SUPPORTED_VERSION = 6.4
export const SUGARFIT_JUICE_SUPPORTED_VERSION = 7.8
export const CITY_SPLIT_SUPPORTED_VERSION = 9.62
export const CITY_SPLIT_LAUNCH_DATE: Date = new Date(1662409800000)
export const CITY_SPLIT_ENABLED_CITY_IDS = ["Mumbai"]
export const NAVI_MUMBAI_AND_THANE_CITY_ID = "Navi_Mum_And_Thane"
export const REST_OF_MUMBAI_CITY_ID = "Mumbai"

// TODO: remove socialLogin and mobileLogin once v8.35 is old enough
export type LoginPageTypesCompat = LoginPageTypes | "socialLogin" | "mobileLogin"

const PAGE_TYPE_COMPATIBILITY_MAP = {
    [PageTypes.UnifiedLogin]: "socialLogin",
    [PageTypes.SocialLogin]: "socialLogin",
    [PageTypes.PhoneLogin]: "mobileLogin",
    [PageTypes.ChronicCareLogin]: "chronicCareLogin"
} as const

export const ZOOM_WEB_SDK = "135"

export const SHOW_TRUNCATED_LIST_SUPPORT = 8.35
export const SHOW_TRUNCATED_LIST_SUPPORT_CP_ANDROID = 421
export const SHOW_TRUNCATED_LIST_SUPPORT_CP_IOS = 391

export const CLIENT_SIDE_CUTLERY_SELECT_SUPPORT = 8.43
export const EATCLP_PROGRESSIVE_CART_OFFERS_SUPPORT_INTERNAL = 8.40
export const EATCLP_PROGRESSIVE_CART_OFFERS_SUPPORT_CP_ANDROID = 423
export const EATCLP_PROGRESSIVE_CART_OFFERS_SUPPORT_EXTERNAL = 8.41

export const NC_PERSONAL_COACH_SUPPORT = 8.40
export const POST_RATING_INTERVENTION_SUPPORTED = 8.43
export const PARTIAL_VIDEO_WATCHED_INTERVENTION_SUPPORTED = 8.44
export const POST_RATING_INTERVENTION_ID = "post_rating_intervention"
export const CULT_REFERRAL_POST_RATING_INTERVENTION_ID = "cult_referral_post_rating_intervention"
export const PARTIAL_DIY_WATCHED_INTERVENTION_ID = "partial_diy_watched"
export const PARTIAL_LIVE_WATCHED_INTERVENTION_ID = "partial_live_watched"

export const VOUCHER_CAPTCHA_APP_VERSION = 8.46

export const CULT_OFFLINE_RELAUNCH_IOS_CP_INTERNAL = 397
export const CULT_OFFLINE_RELAUNCH_IOS_CP_EXTERNAL = 169
export const CULT_OFFLINE_RELAUNCH_APP_VERSION = 8.44

export const LIVE_SGT_NO_SHOW_SUPPORTED_IOS_CP_INTERNAL = 399
export const LIVE_SGT_NO_SHOW_SUPPORTED_IOS_CP_EXTERNAL = 171
export const LIVE_SGT_NO_SHOW_SUPPORTED_ANDROID = 8.46

export const TV_COMPANION_EXPERIENCE_SUPPORT_VERSION = 8.57 // TODO change to latest version of mobile app
export const MED_RECORDS_INSIDE_ACTIVITY_RECORDS_SUPPORTED_VERSION = 8.50
export const FITCASH_INSIDE_PAYMENTS_SUPPORTED = 8.50
export const CONTACT_US_SUPPORT_TICKET_CREATION_ENABLED = 8.57
export const SUPPORT_RECENT_ORDERS_VIEW_ENABLED = 8.58
export const SUPPORT_BOOKINGS_VIEW_ENABLED = 8.60
export const IS_SMALL_CARDS_DIAG_CLP_SUPPORTED = 8.58
export const IS_SMALL_CARDS_DIAG_LIST_CLP_SUPPORTED = 8.77

export const APPLE_IAP_SUPPORTED_VERSION_INDIA = 8.52
export const APPLE_IAP_SUPPORTED_VERSION_INTL = 1.7
export const ANDROID_IAP_SUPPORTED_VERSION_INTL = 1.7

export const LOGIN_OTP_CAPTCHA_SUPPORTED = 8.58

export const CULT_PACK_BROWSE_HIDE_OFFERS_EXPERIMENT = "233"
export const CULT_PACK_BROWSE_HIDE_CTA_EXPERIMENT = "236"
export const CULT_PACK_BROWSE_HIDE_PRICE_CUT_EXPERIMENT = "238"

const CULT_WAITLIST_CONFIRMATION_PROBABILITY_VERSION = 8.56
const CULT_WAITLIST_CONFIRMATION_PROD_EXPERIMENT_ID = "268"
const CULT_WAITLIST_CONFIRMATION_STAGE_EXPERIMENT_ID = "213"

const CULT_WAITLIST_EXTENSION_VERSION = 8.99
const CULT_WAITLIST_EXTENSION_IOS_CP_INTERNAL = 443
const CULT_WAITLIST_EXTENSION_IOS_CP_EXTERNAL = 195
const CULT_WAITLIST_EXTENSION_ANDROID_CP_INTERNAL = 473
const CULT_WAITLIST_EXTENSION_ANDROID_CP_EXTERNAL = 201

export const TRAINER_LED_SUPPORTED = "TRAINER_LED_SUPPORTED"
export const TRAINER_LED_TV_SUPPORTED = "TRAINER_LED_TV_SUPPORTED"

export const BOOKING_SCREEN_NUDGES_EXPERIMENT_ID = "290"

export const MEDIA_GATEWAY_REPORT_SUPPORT = 8.59
export const PRODUCT_HELP_BUTTON_SUPPORTED = 8.68
export const NOTIFICATION_MANAGER_SUPPORTED = 8.94
export const ONBOARDING_FLOW_SUPPORTED = 9.16

export const LIVE_PT_ZOOM_MEETING_JOIN_TITLE = "JOIN NOW"

// todo: Nisheet change these as per launch plan
export const WELLNESS_TAB_APP_SUPPORT_EXTERNAL = 8.81
export const WELLNESS_TAB_APP_SUPPORT_INTERNAL = 8.80
export const WELLNESS_TAB_CP_IOS = 428
export const WELLNESS_TAB_CP_ANDROID = 457
export const WELLNESS_TAB_HAMLET_EXP_ID_STAGE = "364"
export const WELLNESS_TAB_HAMLET_EXP_ID_PROD = "413"

export const DEV_OPTIONS_PAGE_SUPPORTED = 8.84
export const TRANSFORM_WAITLIST_SUPPORT = 8.96
export const TRANSFORM_CANCEL_ANYTIME_WIDGET_HAMLET_EXP_PROD_ID = "978"

export const NEW_ORDER_TRACKING_WIDGET_SUPPORT = 8.97

export const FITNESS_HUB_HAMLET_EXP_ID_STAGE = "409"
export const FITNESS_HUB_HAMLET_EXP_ID_PROD = "772"
export const FITNESS_TAB_SUPPORTED = 9.16

export const THERAPIST_EXPERT_LOGIC_EXP_ID_STAGE = "448" // https://cerebrum.stage.curefit.co/#/curefit/experiments/experiment/details/448
export const THERAPIST_EXPERT_LOGIC_EXP_ID_PROD = "805" // https://cerebrum.curefit.co/#/curefit/experiments/experiment/details/805

export const BOOTCAMP_BOOKING_EXP_ID_STAGE = "622"
export const BOOTCAMP_BOOKING_EXP_ID_PROD = "1514"

export const FOOD_MP_CART_SCREEN_ADDON_SUPPORT = 9.18
export const FOOD_MARKETPLACE_LOCATION_COMPULSORY_SUPPORT = 9.18
export const FOOD_MARKETPLACE_FEATURED_RESTAURANT_WIDGET_SUPPORT = 9.18

export const ENTERPRISE_CORP_CLP_SUPPORT = 9.33
export const AURORA_THEME_USER_PROFILE_WIDGET_SUPPORTED_VERSION = 9.25

export const REAL_LIVE_SESSION_SUPPORTED_APP_VERSION = 9.30
export const SF_PACK_NEW_IMAGES_SUPPORTED_APP_VERSION = 2.01

export const HYBRID_CENTERS_SUPPORTED_VERSION = 9.55

export const CULT_ROW_DEVICE_SUPPORT = 9.44

export const WEEKLY_GOAL_PAGE_SUPPORTED_VERSION = 9.60
export const ONBOARDING_FORM_V2_SUPPORTED_VERSION = 9.60

export const NEW_WAITLIST_PROBABILITY_VERSION = 9.87

export const COMMUNITY_STAGE_SEGMENT = "a2ff3f8e-a098-43cb-a330-187ea4f09ae1"
export const COMMUNITY_PROD_SEGMENT = "e506edf7-8cbf-4307-9c2e-387925389260"

export const MANDATORY_ONBOARDING_EXP_ID_STAGE = "583"
export const MANDATORY_ONBOARDING_EXP_ID_PROD = "1252"

export const GYM_PROGRESSION_EXP_ID_PROD = "1970"
export const FEEDBACK_REVAMP_EXPERIMENT_ID_PROD = "1952"

export const ATHOME_CLP_WEB_EXP_PROD = "1287"

const GIFT_CARD_APP_VERSION = 10.06

const UNIFIED_GIFT_CARD_APP_VERSION = 10.09

export const CANCELLATION_WINDOW_FOR_NO_SHOW = 8

export const CULT_HABIT_REMINDER_SEGMENT = "0117e3fb-631b-474d-9cce-2f39816652e7"

export const CLP_V2_VERSION = 10.37

export const CREDIT_PILL_ICON = "/image/icons/credit_pill2.png"

export const SUPPORT_DEEP_LINK = "curefit://fl_support"

export const SQUAD_DISABLED_PRODUCT_TYPES: ProductType[] = []
type UILabelType = "saleBannerLabel" | "orderSummaryLabel"


const FEEDBACK_USER_FORM_AB_TEST_CONFIG_QUERY = {
    experimentId: "1125",
    configKey: "isEnabled",
    defaultValue: false
}

const FEEDBACK_USER_FORM_AB_TEST_ORDER_SOURCES: OrderSource[] = ["CUREFIT_APP"]

export interface OfferV2WithTnc {
    tnc: string[]
    tncURL?: string
    description: string
    offerId: string
}

export interface CFTextData {
    text?: string,
    color?: string,
    typeScale?: string,
    richText?: boolean,
    gradient?: any,
    fontStyle?: string,
}

export function typesafeKeys<T extends object>(obj: T): Array<keyof T> {
    return Object.keys(obj) as Array<keyof T>
}
export function getAppUrl(appRoute: string, queryParams?: { [key: string]: number | string | boolean }) {
    if (!_.isEmpty(queryParams)) {
        return `curefit://${appRoute}?${typesafeKeys(queryParams).map(param => `${param}=${queryParams[param]}`).join("&")}`
    }
    return `curefit://${appRoute}`
}

export function addCommaInPriceINR(price: string): string {
    const numericPrice = price.replace(/[^0-9.]/g, "")
    const integerPart = numericPrice.split(".")[0]
    let decimalPart = numericPrice.split(".")[1]

    if (integerPart.length <= 3) {
        return integerPart + (decimalPart ? "." + decimalPart.slice(0, 2) : "")
    }

    const lastThree = integerPart.slice(-3)
    const remaining = integerPart.slice(0, integerPart.length - 3)
    const formattedIntegerPart = remaining.replace(/\B(?=(\d{2})+(?!\d))/g, ",") + "," + lastThree

    if (decimalPart) {
        decimalPart = decimalPart.slice(0, 2)
        return formattedIntegerPart + "." + decimalPart
    }
    return formattedIntegerPart
}
class AppUtil {

    public static isCultROWDeviceSupported(appVersion: number, osName: string): boolean {
        return appVersion >= CULT_ROW_DEVICE_SUPPORT && ["android", "ios"].includes(osName)
    }

    public static isFoodMarketplaceFeatureRestaurantWidgetSupported(userContext: UserContext): boolean {
        const { appVersion } = userContext?.sessionInfo
        return appVersion >= FOOD_MARKETPLACE_FEATURED_RESTAURANT_WIDGET_SUPPORT
    }

    public static isFoodMarketplaceCartAddonSupported(userContext: UserContext): boolean {
        const { appVersion } = userContext?.sessionInfo
        return appVersion >= FOOD_MP_CART_SCREEN_ADDON_SUPPORT
    }

    public static isFoodMarketplaceLocationCompulsionSupported(userContext: UserContext) {
        const { appVersion } = userContext.sessionInfo
        return appVersion >= FOOD_MARKETPLACE_LOCATION_COMPULSORY_SUPPORT
    }

    public static isMedRecordsInActivityRecordsSupported(userContext: UserContext) {
        const osName = userContext.sessionInfo.osName
        const appVersion = userContext.sessionInfo.appVersion
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        if (osName.toLowerCase() === "ios" || osName.toLowerCase() === "android") {
            return appVersion >= MED_RECORDS_INSIDE_ACTIVITY_RECORDS_SUPPORTED_VERSION
        }
        return false
    }

    public static isFitcashInsidePaymentsSupported(userContext: UserContext) {
        const osName = userContext.sessionInfo.osName
        const appVersion = userContext.sessionInfo.appVersion
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        if (osName.toLowerCase() === "ios" || osName.toLowerCase() === "android") {
            return appVersion >= FITCASH_INSIDE_PAYMENTS_SUPPORTED
        }
        return false
    }

    public static isVideoMergerSupported(userContext: UserContext) {
        const osName = userContext.sessionInfo.osName
        const appVersion = userContext.sessionInfo.appVersion
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        if (osName.toLowerCase() === "ios" || osName.toLowerCase() === "android") {
            return appVersion >= 8.46
        }
        return false
    }

    public static isNewLiveClassBookingPageSupported(userContext: UserContext) {
        const osName = userContext.sessionInfo.osName
        const appVersion = userContext.sessionInfo.appVersion
        if (AppUtil.isWeb(userContext)) {
            return true
        }
        if (osName.toLowerCase() === "ios" || osName.toLowerCase() === "android") {
            if (AppUtil.isSugarFitOrUltraFitApp(userContext))
                return appVersion >= SUGARFIT_APP_LIVE_CLASS_BOOKING_MIN_VERSION
            return appVersion >= LIVE_CLASS_BOOKING_PAGE_VERSION
        }
        return false
    }

    public static isDynamicInterventionSupported(userContext: UserContext) {
        const osName = userContext.sessionInfo.osName
        const appVersion = userContext.sessionInfo.appVersion
        if ((osName.toLowerCase() === "ios" || osName.toLowerCase() === "android") && !this.isInternationalApp(userContext)) {
            return appVersion >= DYNAMIC_INTERVENTION_SUPPORTED
        }
        return false
    }

    public static async isCalendarSchedulingSupported(userContext: UserContext, hamletBusiness: HamletBusiness): Promise<boolean> {
        const osName = userContext.sessionInfo.osName
        const clientVersion = userContext.sessionInfo.clientVersion
        const experimentId = CALENDAR_SCHEDULING_HAMLET_ID // Owner ayusch / ravi.singla
        const calendarSchedulingAllocations: UserAllocation = { assignmentsMap: {} }
        // const bucket = (calendarSchedulingAllocations && calendarSchedulingAllocations.assignmentsMap && !_.isEmpty(calendarSchedulingAllocations.assignmentsMap[CALENDAR_SCHEDULING_HAMLET_ID]) && !_.isEmpty(calendarSchedulingAllocations.assignmentsMap[CALENDAR_SCHEDULING_HAMLET_ID].bucket)) ? calendarSchedulingAllocations.assignmentsMap[CALENDAR_SCHEDULING_HAMLET_ID].bucket : undefined
        const payload = calendarSchedulingAllocations?.assignmentsMap[CALENDAR_SCHEDULING_HAMLET_ID]?.bucket?.payload
        if (!payload) {
            return false
        }
        const calendarSchedulingSupported = payload.find((pair: Payload) => {
            return pair.key === "enabled"
        })
        if ((osName.toLowerCase() === "ios" || osName.toLowerCase() === "android") && !this.isInternationalApp(userContext) && calendarSchedulingSupported?.value) {
            return clientVersion >= CALENDAR_SCHEDULING_SUPPORTED
        }
        return false
    }

    public static isPostRatingInterventionSupported(userContext: UserContext): boolean {
        const osName = userContext.sessionInfo.osName
        const clientVersion = userContext.sessionInfo.clientVersion
        if ((osName.toLowerCase() === "ios" || osName.toLowerCase() === "android") && !this.isInternationalApp(userContext)) {
            return clientVersion >= POST_RATING_INTERVENTION_SUPPORTED
        }
        return false
    }

    public static isPartialVideoWatchedInterventionSupported(userContext: UserContext): boolean {
        const osName = userContext.sessionInfo.osName
        const clientVersion = userContext.sessionInfo.clientVersion
        if ((osName.toLowerCase() === "ios" || osName.toLowerCase() === "android") && !this.isInternationalApp(userContext)) {
            return clientVersion >= PARTIAL_VIDEO_WATCHED_INTERVENTION_SUPPORTED
        }
        return false
    }

    public static formatFilterNameSupport(userContext: UserContext) {
        const osName = userContext.sessionInfo.osName
        const appVersion = userContext.sessionInfo.appVersion
        if (osName.toLowerCase() === "ios" || osName.toLowerCase() === "android") {
            return appVersion >= FORMAT_FILTER_NAME_SUPPORT
        }
        return false
    }

    public static isNewLiveOrderConfirmationIconSupported(userContext: UserContext) {
        const osName = userContext.sessionInfo.osName
        const appVersion = userContext.sessionInfo.appVersion
        if (osName.toLowerCase() === "ios" || osName.toLowerCase() === "android") {
            return appVersion >= NEW_LIVE_ORDER_CONFIRMATION_ICON_SUPPORTED
        }
        return false
    }

    public static isTncPageInviteCtaSupported(userContext: UserContext) {
        const osName = userContext.sessionInfo.osName
        const appVersion = userContext.sessionInfo.appVersion
        if (osName.toLowerCase() === "ios" || osName.toLowerCase() === "android") {
            return appVersion >= TNC_INVITE_CTA_SUPPORT
        }
        return false
    }

    public static async isCitySplitFeatureSupported(userContext: UserContext) {
        return true
    }

    public static isCitySplitSelectionSupported(userContext: UserContext) {
        const osName = userContext.sessionInfo.osName
        const appVersion = userContext.sessionInfo.appVersion
        if (osName.toLowerCase() === "ios" || osName.toLowerCase() === "android") {
            return appVersion >= CITY_SPLIT_SUPPORTED_VERSION
        }
        return true
    }

    public static isDateWiseLiveSlotWidgetSupported(userContext: UserContext): boolean {
        const { osName, appVersion } = userContext.sessionInfo
        if (AppUtil.isWeb(userContext)) {
            return true
        }
        const osNameLowerCase = osName.toLowerCase()
        if (osNameLowerCase === "ios" || osNameLowerCase === "android") {
            return appVersion >= DATE_WISE_LIVE_SLOT_SELECTION_VERSION
        }
        return false
    }

    static async getActiveOfferIfSupported(userContext: UserContext, hamletBusiness: HamletBusiness, cartOffers?: OfferV2[]): Promise<OfferV2[]> {
        const osName = userContext.sessionInfo.osName?.toLowerCase()
        const appVersion = userContext.sessionInfo.appVersion
        const experimentId = getEatWelcomeOfferAnnouncementExperimentHamletId() // owner shanawar
        const doesUserBelongToExperiment = false

        if (doesUserBelongToExperiment && (osName === "android" || osName === "ios") && appVersion >= EAT_WELCOME_OFFER_SUPPORTED) {
            let offers = cartOffers
            if (!offers) {
                offers = await userContext.userProfile.eatCartOffersPromise || []
            }
            return offers.filter(
                offer => {
                    return offer.displayContexts?.includes("LANDING_PAGE")
                }
            )
        }
        return []
    }

    public static async doesUserBelongToInAppReviewPromptSegment(segmentationClient: ISegmentService, userContext: UserContext): Promise<boolean> {
        if (AppUtil.isProdLike) {
            const segmentId = "08d2e2f7-1d47-4bac-8dcc-07a577cea88f"
            return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext))
        }
        return true
    }

    public static isNewCenterResponseSupported(appVersion: number, osName: string) {
        if (osName === "browser") {
            return false
        }
        if (appVersion >= NEW_CENTER_RESPOSE_APP_VERSION) {
            return true
        }
        return false
    }

    public static isProPreferredCenterSupported(appVersion: number, userAgent: string) {
        if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
            return false
        }
        return appVersion >= 10.68 || process.env.ENVIRONMENT === "STAGE"
    }

    public static getSleepConfig(appVersion: string, osName: string) {
        if (osName === "ios") {
            return IOS_SLEEP_CONFIG
        } else if (osName === "android") {
            return ANDROID_SLEEP_CONFIG
        } else {
            return ANDROID_SLEEP_CONFIG
        }
    }

    public static isSleepEnabled(city: City) {
        return city.countryId === "IN"
    }

    /**
     * Use this function to generate userContext consumed by appConfigUtil from request
     */
    public static createUserContextForAppConfig(req: express.Request): UserContext {
        const osName: string = req.headers["osname"] as string
        const clientVersion: number = Number(req.headers["clientversion"])
        let apiKey: string = req.headers["apikey"] as string
        // flipkart sends apiKey in authorization header in this format : (Bearer $token)
        if (!apiKey && req.headers["authorization"]) {
            apiKey = req.headers["authorization"].split(" ")?.[1]?.trim()
        }
        const orderSource: OrderSource = AppUtil.callSource(apiKey)
        const session: Session = (<any>req).session
        const userAgent: UserAgent = session?.userAgent
        const codePushVersion: number = req.headers["codepushversion"] && req.headers["codepushversion"] !== "undefined" ? Number(req.headers["codepushversion"]) : 0
        return {
            sessionInfo: {
                osName: osName,
                clientVersion: clientVersion,
                cpVersion: codePushVersion,
                orderSource: orderSource,
                userAgent: userAgent
            }
        } as UserContext
    }

    public static isAppVersionSupported(request: express.Request): boolean {
        const userContext = AppUtil.createUserContextForAppConfig(request)
        return AppConfigUtil.evaluateBoolean("APP_VERSION_SUPPORTED", userContext)
    }

    public static isJuspayUPISupported(userContext: UserContext): boolean {
        const appVersion: number = userContext.sessionInfo.appVersion
        const osName: string = userContext.sessionInfo.osName
        if (osName === "android" && appVersion >= ANDROID_JUSPAY_PAYTM_API) {
            return true
        } else {
            return false
        }
    }

    public static isTataNeuWebApp(userContext: UserContext) {
        return AppUtil.isWeb(userContext) && userContext.sessionInfo.apiKey === Constants.getTataNeuWebApiKey()
    }

    public static isUserSupportedForInviteOffer(userContext: UserContext): boolean {
        const appVersion: number = userContext.sessionInfo.appVersion
        const osName: string = userContext.sessionInfo.osName
        if ((osName === "android" || osName === "ios") && appVersion >= APP_SUPPRT_VERSION_FOR_REFERRAL_INVITE_OFFER) {
            return true
        } else {
            return false
        }
    }

    public static isFitclubV2PreMembershipPageSupported(userContext: UserContext): boolean {
        if (["android", "ios"].includes(userContext.sessionInfo && userContext.sessionInfo.osName)) {
            const appVersion: number = userContext.sessionInfo.appVersion
            return (appVersion >= FITCLUB_V2_PRE_MEMBERSHIP_PAGE)
        }
    }

    public static isSkipCreatePatientSupported(userContext: UserContext): boolean {
        if (["android", "ios"].includes(userContext.sessionInfo && userContext.sessionInfo.osName)) {
            const appVersion: number = userContext.sessionInfo.appVersion
            return (appVersion >= SKIP_CREATE_PATIENT_SUPPORTED_VERSION)
        }
    }

    public static isPaypalSupported(userContext: UserContext): boolean {
        const appVersion: number = userContext.sessionInfo.appVersion
        const osName: string = userContext.sessionInfo.osName
        if ((osName.toLowerCase() === "android" && appVersion >= ANDROID_PAYPAL_VERSION)
            || (osName.toLowerCase() === "ios" && appVersion >= IOS_PAYPAL_VERSION)
            || AppUtil.isWeb(userContext)) {
            return true
        }
        return false
    }

    public static isPaypalProfileLinkingSupported(userContext: UserContext): boolean {
        const appVersion: number = userContext.sessionInfo.appVersion
        const osName: string = userContext.sessionInfo.osName
        if ((osName.toLowerCase() === "android" && appVersion >= ANDROID_PAYPAL_PROFILE_LINK_VERSION)
            || (osName.toLowerCase() === "ios" && appVersion >= IOS_PAYPAL_PROFILE_LINK_VERSION)) {
            return true
        }
        return false
    }

    public static isCombinedGoalsAndScoreMetricsSupported(userContext: UserContext): boolean {
        const appVersion: number = userContext.sessionInfo.appVersion
        if (appVersion <= COMBINED_SCORE_GOALS_MAX_VERSION) {
            return true
        } else {
            return false
        }
    }

    public static isGoalManagementSupported(userContext: UserContext): boolean {

        const osName = userContext.sessionInfo.osName
        const appVersion = userContext.sessionInfo.appVersion

        if (osName === "ios" && Number(appVersion) >= Number(NEW_GMS_VERSION_IOS)) {
            return true
        } else if (osName === "android" && Number(appVersion) >= Number(NEW_GMS_VERSION_ANDROID)) {
            return true
        }
        return false
    }

    public static isVMPageOptimizationSupported(userContext: UserContext) {
        const sessionInfo = userContext.sessionInfo
        if (sessionInfo.osName == "ios" || sessionInfo.osName == "android") {
            return (Number(sessionInfo.appVersion) >= 7.22)
        }
        return this.isWeb(userContext)
    }

    public static isThirdPartyDevicesSupported(userContext: UserContext, deviceType: ExternalDeviceType): boolean {

        const osName = userContext.sessionInfo.osName
        const appVersion = userContext.sessionInfo.appVersion
        if (!_.isNil(deviceType) && DEVICES_MAP.has(deviceType)) {
            const supportedVersions = DEVICES_MAP.get(deviceType)
            const _supportedVersion: number = _.get(supportedVersions, osName)
            if (_.isFinite(_supportedVersion) && appVersion >= _supportedVersion) {
                return true
            }
        }
        return false
    }

    public static isRejectedOrderSupported(userContext: UserContext): boolean {
        const osName = userContext.sessionInfo.osName
        const appVersion = userContext.sessionInfo.appVersion
        if (osName.toLowerCase() === "browser") {
            return false
        }
        if (osName.toLowerCase() === "ios" || osName.toLowerCase() === "android") {
            return appVersion >= NEW_RETURN_ORDER_FLOW
        }
        return false
    }

    public static isNewPackOrderConformationSupported(userContext: UserContext, isInternalUser: boolean): boolean {
        const appVersion = userContext.sessionInfo.appVersion
        const osName = userContext.sessionInfo.osName.toLowerCase()
        if (osName === "android" || osName === "ios") {
            return appVersion >= NEW_PACK_ORDER_CONFIRMATION
        }
        return false
    }

    public static isPauseEditDateSupported(userContext: UserContext, isInternalUser: boolean): boolean {
        const appVersion = userContext.sessionInfo.appVersion
        const osName = userContext.sessionInfo.osName.toLowerCase()
        if (osName === "ios" || osName === "android") {
            return appVersion >= PAUSE_EDIT_END_DATE
        }
        return false
    }

    public static async isJavaServiceEnabled(userContext: UserContext, hamletBusiness: HamletBusiness): Promise<boolean> {
        try {
            if (process.env.ENVIRONMENT !== "PRODUCTION") {
                return true
            }
            const isJavaServiceEnabled = false
            if (isJavaServiceEnabled)
                return true
        } catch (error) {

        }
        return false
    }

    public static isNewLiveDiyPackSupported(userContext: UserContext) {
        const appVersion = userContext.sessionInfo.appVersion
        const osName = userContext.sessionInfo.osName.toLowerCase()
        if (osName === "android" || osName === "ios") {
            return appVersion >= NEW_LIVE_DIY_PACK_SUPPORTED_VERSION
        }
        return false
    }

    public static isPausePackStartDateSentAsToday(userContext: UserContext): boolean {
        const appVersion = userContext.sessionInfo.appVersion
        const cpVersion = userContext.sessionInfo.cpVersion || 0
        const osName = userContext.sessionInfo.osName.toLowerCase()
        if (AppUtil.isWeb(userContext)) {
            return true
        }
        if (appVersion > NEW_PAUSE_PACK_DATE_VERSION) {
            return false
        }
        return osName === "android" ? cpVersion < NEW_PAUSE_PACK_DATE_ANDROID_INTERNAL_CP_VERSION : cpVersion < NEW_PAUSE_PACK_DATE_IOS_INTERNAL_CP_VERSION
    }

    public static isNewClassBookingSuppoted(userContext: UserContext, isInternalUser?: boolean): boolean {
        const osName = userContext.sessionInfo.osName
        const appVersion = userContext.sessionInfo.appVersion
        if (osName.toLowerCase() === "ios" || osName.toLowerCase() === "android") {
            return appVersion >= NEW_CLASS_BOOKING_VERSION
        }
        return false
    }


    public static diyPackRedesignSupported(userContext: UserContext, isInternalUser: Boolean): boolean {
        if (!userContext) {
            return false
        }
        const isIntlApp = AppUtil.isInternationalApp(userContext)
        const isSugarFitOrUltraFitApp = AppUtil.isSugarFitOrUltraFitApp(userContext)
        const { clientVersion, userAgent } = userContext.sessionInfo
        return userAgent === "APP" && clientVersion >= DIY_PACK_REDESIGN_SUPPORTED(isIntlApp, isSugarFitOrUltraFitApp) && !this.isTVApp(userContext)
    }

    public static isNewSigninFlowSupported(userContext: UserContext): boolean {
        if (
          AppUtil.getTenantFromUserContext(userContext) ===
            Tenant.SUGARFIT_APP ||
          AppUtil.getTenantFromUserContext(userContext) === Tenant.ULTRAFIT_APP
        ) {
          return true
        }
        const { APP_ENV } = process.env
        if (APP_ENV === "STAGE" || APP_ENV === "ALPHA") {
            return true
        }
        const osName = userContext.sessionInfo.osName
        const appVersion = userContext.sessionInfo.appVersion
        if (osName === "browser")
            return true
        if (appVersion >= 8) {
            return true
        }
        return false
    }

    public static getPlanTabs(osName: string, appVersion: string, userPlanExists: boolean, internalUser: boolean): PlanTabsResponse {
        if (Number(appVersion) <= PLAN_TABS_IN_USER_STATUS_VERSION) {
            const planTabs: PlanTab[] = [{
                pageId: "todayview",
                name: "To do"
            }]
            planTabs.push({
                pageId: "activepacks",
                name: "My Packs"
            })
            planTabs.push({
                pageId: "mychallenges",
                name: "Challenges"
            })
            return {
                activeTabIndex: 0,
                planTabs: planTabs
            }
        }
        return undefined
    }

    public static isReminderSupportedVersion(osName: string, appVersion: number) {
        if (osName === "ios") {
            return (appVersion >= DIY_WITH_REMINDER_SUPPORTED_VERSION_IOS)
        }
        return (appVersion >= DIY_WITH_REMINDER_SUPPORTED_VERSION_ANDROID)
    }

    public static isLargePlayIconSupported(osName: string, appVersion: number) {
        if (osName === "ios") {
            return (appVersion >= DIY_WITH_PLAY_LARGE_VERSION_IOS)
        } else if (osName === "android") {
            return (appVersion >= DIY_WITH_PLAY_LARGE_VERSION_ANDROID)
        } else {
            return false
        }
    }

    public static isPulseReportSupported(osName: string, appVersion: number) {
        if (!(osName === "ios" || osName === "android")) {
            return false
        }
        if (osName === "ios" && appVersion < PULSE_IOS_APP) {
            return false
        }
        if (osName === "android" && appVersion < PULSE_ANDROID_APP) {
            return false
        }
        return true
    }

    public static isPulseMuscleGroupSupported(osName: string, appVersion: number) {
        if (!(osName === "ios" || osName === "android")) {
            return false
        }
        if (osName === "ios" && appVersion < PULSE_MUSCLE_GROUP_IOS_APP) {
            return false
        }
        if (osName === "android" && appVersion < PULSE_MUSCLE_GROUP_ANDROID_APP) {
            return false
        }
        return true
    }

    public static isPulseRentalPackSupported(osName: string, appVersion: number) {
        if (!(osName === "ios" || osName === "android")) {
            return false
        }
        if (osName === "ios" && Number(appVersion) < PULSE_RENTAL_PACK_IOS) {
            return false
        }
        if (osName === "android" && Number(appVersion) < PULSE_RENTAL_PACK_ANDROID) {
            return false
        }
        return true
    }

    public static isPulseNoteWidgetSupported(osName: string, appVersion: number) {
        if (!(osName === "ios" || osName === "android")) {
            return false
        }
        if (osName === "ios" && Number(appVersion) < PULSE_NOTE_WIDGET_SUPPORTED_ANDROID) {
            return false
        }
        if (osName === "android" && Number(appVersion) < PULSE_NOTE_WIDGET_SUPPORTED_IOS) {
            return false
        }
        return true
    }

    public static isNewOrderFilteringSupported(osName: string, appVersion: number, cpVersion?: number): boolean {
        if (osName !== "ios" && osName !== "android") {
            return false
        }
        if (Number(appVersion) < ORDER_FILTERING_SUPPORTED && osName === "ios") {
            return false
        }
        if (Number(appVersion) < ORDER_FILTERING_SUPPORTED && osName === "android") {
            return false
        }
        return true
    }

    public static isPulseOptOutSupported(osName: string, appVersion: number): boolean {
        if (!(osName === "ios" || osName === "android")) {
            return false
        }
        if (osName === "ios" && Number(appVersion) < PULSE_OPT_OUT) {
            return false
        }
        if (osName === "android" && Number(appVersion) < PULSE_OPT_OUT) {
            return false
        }
        return true
    }

    public static isAnxietyTherapyCardSupported(osName: string, appVersion: number, cpVersion?: number) {
        if (!(osName === "ios" || osName === "android")) {
            return false
        }
        if (osName === "android" && Number(appVersion) < MIND_THERAPY_CONSULTATION_CARD_ANDROID) {
            return false
        }
        if (osName === "ios" && Number(appVersion) < MIND_THERAPY_CONSULTATION_CARD_IOS) {
            return false
        }
        return true
    }

    public static isFitcashOnReviewPageSupported(userContext: UserContext) {
        if (userContext.sessionInfo.osName === "browser") {
            return false
        }
        // TODO: change to >=
        return userContext.sessionInfo.appVersion >= FITCASH_ON_REVIEW_PAGE
    }
    public static isPulseTrialOptOutSupported(osName: string, appVersion: number, cpVersion?: number): boolean {
        if (!(osName === "ios" || osName === "android")) {
            return false
        }
        if (osName === "ios" && Number(appVersion) < PULSE_TRIAL_OPT_OUT_IOS) {
            return false
        }
        if (osName === "android" && Number(appVersion) < PULSE_TRIAL_OPT_OUT_ANDROID) {
            return false
        }
        if (osName === "android" && Number(appVersion) === PULSE_TRIAL_OPT_OUT_ANDROID) {
            if (typeof cpVersion !== "undefined" && cpVersion) {
                return Number(cpVersion) >= PULSE_TRIAL_OPT_OUT_CP_ANDROID
            }
            return false
        }
        if (osName === "ios" && Number(appVersion) === PULSE_TRIAL_OPT_OUT_IOS) {
            if (typeof cpVersion !== "undefined" && cpVersion) {
                return Number(cpVersion) >= PULSE_TRIAL_OPT_OUT_CP_IOS
            }
            return false
        }
        return true
    }

    public static isPulseRentalVideoSupported(userContext: UserContext) {
        const { osName, appVersion } = userContext.sessionInfo

        if (!(osName === "ios" || osName === "android")) {
            return false
        }

        if (osName === "ios" && appVersion < PULSE_RENTAL_PACK_VIDEO_IOS) {
            return false
        }

        if (osName === "android" && appVersion < PULSE_RENTAL_PACK_ANDROID) {
            return false
        }

        return true
    }

    public static isNewReportIssueSupported(userContext: UserContext): boolean {
        if (userContext.sessionInfo.osName === "browser") {
            return false
        }
        return true
    }

    public static isMealPageReportIssueEnabled(userContext: UserContext): boolean {
        return userContext && userContext.sessionInfo && userContext.sessionInfo.osName !== "browser"
    }

    public static isNotLoggedinUser(session: Session): boolean {
        if (AppUtil.isNotLoggedinUserId(session.userId) || session.isNotLoggedIn) {
            return true
        } else {
            return false
        }
    }

    /**
     *
     * @param {osName} a lowerCased string
     * @param {appVersion} app version
     * @param {cpVersion} Codepush version
     * @param {isInternalUser} If the user is internal user
     */
    public static _isPackStartDateChangeSuppported(osName: string, appVersion: number, cpVersion?: number, isInternalUser?: boolean): boolean {
        // not Android or iOS
        if (!(osName === "android" || osName === "ios")) {
            return false
        }
        // external user
        // must be on higher app version
        if (appVersion <= PACK_CHANGE_START_DATE_ANDROID_APP_VERSION && osName === "android") {
            return false
        }
        if (appVersion <= PACK_CHANGE_START_DATE_IOS_APP_VERSION && osName === "ios") {
            return false
        }
        // external user
        // on higher app version
        return true
    }

    /**
     * check if cult or mind pack start date change is supported
     * @param {userContext} user context
     * @return true, if supported; false otherwise
     */
    public static isPackStartDateChangeSupported(userContext: UserContext, user: User): boolean {
        if (userContext.sessionInfo.osName && userContext.sessionInfo.appVersion) {
            const { osName, appVersion } = userContext.sessionInfo
            return this._isPackStartDateChangeSuppported(
                osName.toLowerCase(),
                Number(appVersion),
                userContext.sessionInfo.cpVersion,
                user.isInternalUser
            )
        }
        // in all other cases
        return false
    }

    /**
     * check if Membership Audit trail is supported
     *
     * @param {osName} Platform: "ios" | "android" | "web" | "desktop" | undefined
     * @param {appVersion} Version: version of the app / client-side code
     * @param {cpVersion} Codepush version of the app / client-side code
     */
    public static isMembershipAuditTrailSupported(
        osName: UserContext["sessionInfo"]["osName"],
        appVersion: UserContext["sessionInfo"]["appVersion"],
        cpVersion: UserContext["sessionInfo"]["cpVersion"]
    ): boolean {
        if (osName !== "ios" && osName !== "android") {
            return false
        }
        if (Number(appVersion) < MEMBERSHIP_AUDIT_TRAIL_IOS && osName === "ios") {
            return false
        }
        if (Number(appVersion) < MEMBERSHIP_AUDIT_TRAIL_ANDROID && osName === "android") {
            return false
        }
        return true
    }

    public static isPackUpgradeSupported(userContext: UserContext): boolean {
        const { osName, appVersion } = userContext.sessionInfo
        if (osName !== "android" && osName !== "ios") {
            return false
        }
        if (
            Number(appVersion) < PACK_UPGRADE_ANDROID_APP_VERSION &&
            osName === "android"
        ) {
            return false
        }
        if (Number(appVersion) < PACK_UPGRADE_IOS_APP_VERSION && osName === "ios") {
            return false
        }
        return true
    }

    public static isGymPackUpgradeSupported(userContext: UserContext): boolean {
        const { osName, appVersion } = userContext.sessionInfo
        if (osName !== "android" && osName !== "ios") {
            return false
        }
        return Number(appVersion) >= GYM_PACK_UPGRADE_APP_VERSION
    }


    public static isTransferMembershipSupported(userContext: UserContext, isInternalUser: boolean): boolean {
        if (userContext.sessionInfo.appVersion >= TRANSFER_MEMBERSHIP_SUPPORTED_VERSION) {
            return true
        }
        return false
    }

    public static async isNUXSupported(userContext: UserContext, isInternalUser: boolean, productType: ProductType, hamletBusiness: HamletBusiness): Promise<boolean> {
        if ((process.env.ENVIRONMENT === "LOCAL" || process.env.ENVIRONMENT === "STAGE") && productType === "FITNESS" && !AppUtil.isUserNotLoggedIn(userContext.userProfile.userId, userContext.sessionInfo.isUserLoggedIn)) {
            return true
        }
        return false
        // if (productType === "FITNESS" && userContext.sessionInfo.appVersion >= NUX_SUPPORTED_VERSION && !AppUtil.isUserNotLoggedIn(userContext.userProfile.userId, userContext.sessionInfo.isUserLoggedIn)) {
        //     return true
        // }
        // return false
    }

    public static async isGymfitVerticalEnabled(userContext: UserContext): Promise<boolean> {
        return false
    }

    public static async doesUserBelongToGymFitExperiment(userContext: UserContext): Promise<boolean> {
        return false
    }

    public static isAddBuddyInternationalSupported(userContext: UserContext): boolean {
        if (userContext.sessionInfo.osName && (userContext.sessionInfo.osName.toLowerCase() === "android" || userContext.sessionInfo.osName.toLowerCase() === "ios")) {
            return userContext.sessionInfo.appVersion >= ADD_BUDDY_INTERNATIONAL_APP_VERSION
        }
        else {
            return false
        }
    }

    public static isLiveClassBookingPageSupported(userContext: UserContext): boolean {
        const { osName, appVersion } = userContext.sessionInfo
        const osNameLowerCase = osName.toLowerCase()
        if (AppUtil.isSugarFitOrUltraFitApp(userContext))
            return appVersion >= SUGARFIT_APP_LIVE_CLASS_BOOKING_MIN_VERSION
        if (osNameLowerCase === "ios" || osNameLowerCase === "android") {
            return appVersion >= LIVE_CLASS_BOOKING_PAGE_SUPPORTED_VERSION
        }
    }

    public static isLiveLazyInviteLinkActionSupported(userContext: UserContext): boolean {
        if (AppUtil.isSugarFitOrUltraFitApp(userContext)) {
            return false
        }
        const { osName, appVersion } = userContext.sessionInfo
        const osNameLowerCase = osName?.toLowerCase()
        if (osNameLowerCase === "ios" || osNameLowerCase === "android") {
            return appVersion >= CULT_LIVE_LAZY_INVITE_LINK_SUPPORTED_VERSION
        }
    }

    public static isLiveNewConfirmationPageCellSupported(userContext: UserContext): boolean {
        const { osName, appVersion } = userContext.sessionInfo
        const osNameLowerCase = osName?.toLowerCase()
        if (osNameLowerCase === "ios" || osNameLowerCase === "android") {
            return appVersion >= LIVE_NEW_CONFIRMATION_CELL_SUPPORTED_VERSION
        }
    }

    public static isLiveNewConfirmationPageDesignSupported(userContext: UserContext): boolean {
        const { osName, appVersion } = userContext.sessionInfo
        const osNameLowerCase = osName?.toLowerCase()
        if (osNameLowerCase === "ios" || osNameLowerCase === "android") {
            return appVersion >= LIVE_NEW_CONFIRMATION_DESIGN_SUPPORTED_VERSION
        }
    }

    public static isSugarfitWellnessAtCenterSupportedAppVersion(userContext: UserContext): boolean {
        const { appVersion } = userContext.sessionInfo
        return AppUtil.isSugarFitApp(userContext) && appVersion >= SF_WELLNESS_AT_CENTER_SUPPORTED_APP_VERSION
    }

    public static async isSugarfitStoreTabSupportedUser(userContext: UserContext, segmentationCacheClient: ISegmentationCacheClient) {
        if (AppUtil.isSugarfitStoreTabSupportedAppVersion(userContext)) {
            const isSugarfitFreemiumUser = await AppUtil.isSugarfitFreemiumUser(userContext, segmentationCacheClient)
            if (isSugarfitFreemiumUser) {
                return false
            }
            const sf_store_tab_disabled_users = "sf_store_tab_disabled_users"
            const sf_store_tab_enabled_users = "sf_store_tab_enabled_users"
            const userSegments = await segmentationCacheClient.getUserSegments(userContext.userProfile.userId)
            return !_.isEmpty(userSegments) && _.includes(userSegments, sf_store_tab_enabled_users) && !_.includes(userSegments, sf_store_tab_disabled_users)
        }
        return false
    }

    public static async isSugarfitFreemiumUser(userContext: UserContext, segmentationCacheClient: ISegmentationCacheClient) {
        if (AppUtil.isSugarFitOrUltraFitApp(userContext)) {
            const SF_FREEMIUM_USERS = "SF_FREEMIUM_USERS"
            const userSegments = await segmentationCacheClient.getUserSegments(userContext.userProfile.userId)
            return !_.isEmpty(userSegments) && _.includes(userSegments, SF_FREEMIUM_USERS)
        }
        return false
    }

    public static isSugarfitStoreTabSupportedAppVersion(userContext: UserContext): boolean {
        const { appVersion } = userContext.sessionInfo
        return AppUtil.isSugarFitApp(userContext) && appVersion >= SF_SUGARFIT_STORE_TAB_SUPPORTED_APP_VERSION
    }

    public static isLiveMomentOfDaySupported(userContext: UserContext): boolean {
        return false
    }

    public static isLiveMomentOfDayV2Supported(userContext: UserContext): boolean {
        const { osName, appVersion } = userContext.sessionInfo
        const osNameLowerCase = osName?.toLowerCase()
        if (osNameLowerCase === "ios" || osNameLowerCase === "android") {
            return appVersion >= LIVE_MOMENT_OF_DAY_V2_SUPPORTED_VERSION
        }
    }

    public static isLiveMomentOnReportSupported(userContext: UserContext) {
        const { osName, appVersion } = userContext.sessionInfo
        const osNameLowerCase = osName?.toLowerCase()
        if (osNameLowerCase === "ios" || osNameLowerCase === "android") {
            return appVersion >= LIVE_MOMENT_ON_REPORT_SUPPORTED_VERSION
        }
    }

    public static isSquadChallengesSupported(productType: ProductType) {
        return false
    }

    public static isLeagueLeaderboardSupported(productType: ProductType) {
        return false
    }

    public static isSquadChallengesCarouselSupported(userContext: UserContext) {
        const { osName, appVersion } = userContext.sessionInfo
        const osNameLowerCase = osName?.toLowerCase()
        if (osNameLowerCase === "ios" || osNameLowerCase === "android") {
            return appVersion >= SQUAD_CHALLENGES_CAROUSEL_SUPPORTED_VERSION
        }
    }

    public static async shouldShowChallengesCarousel(userContext: UserContext) {
        return true
    }

    public static isSquadLeaderboardPreviewSupported(userContext: UserContext) {
        const { osName, appVersion } = userContext.sessionInfo
        const osNameLowerCase = osName?.toLowerCase()
        if (osNameLowerCase === "ios" || osNameLowerCase === "android") {
            return appVersion >= SQUAD_LEADERBOARD_PREVIEW_SUPPORTED_VERSION
        }
    }

    public static isDiyReportSupported(userContext: UserContext) {
        const { osName, appVersion } = userContext.sessionInfo
        const osNameLowerCase = osName?.toLowerCase()
        if (osNameLowerCase === "android" || osNameLowerCase === "ios") {
            return process.env.ENVIRONMENT === "STAGE" || (appVersion >= DIY_REPORT_SUPPORTED)
        }
        return false
    }

    public static isLiveSecondTrainerImageSupported(userContext: UserContext) {
        const { osName, appVersion } = userContext.sessionInfo
        const osNameLowerCase = osName?.toLowerCase()
        if (osNameLowerCase === "ios" || osNameLowerCase === "android") {
            return appVersion >= LIVE_SECOND_TRAINER_IMAGE_SUPPORTED_VERSION
        }
    }

    public static isLiveNewClassReportSupported(userContext: UserContext): boolean {
        const { osName, appVersion } = userContext.sessionInfo
        const osNameLowerCase = osName?.toLowerCase()
        if (osNameLowerCase === "ios" || osNameLowerCase === "android") {
            return appVersion >= LIVE_NEW_CLASS_REPORT_SUPPORTED_VERSION
        }
    }

    public static isRelaxingMomentAndReportSupported(userContext: UserContext): boolean {
        const { osName, appVersion } = userContext.sessionInfo
        const osNameLowerCase = osName?.toLowerCase()
        const todaysDateWithTimezone = TimeUtil.todaysDateWithTimezone(userContext.userProfile.timezone, TimeUtil.DEFAULT_DATE_FORMAT)
        const targetDate = "2020-05-10"
        const diffInDays = TimeUtil.diffInDays(userContext.userProfile.timezone, todaysDateWithTimezone, targetDate)
        if (osNameLowerCase === "ios" || osNameLowerCase === "android") {
            return (appVersion >= RELAXING_MOMENT_AND_REPORT_SUPPORTED_VERSION && diffInDays === 0) || process.env.ENVIRONMENT === "STAGE" || process.env.ENVIRONMENT === "LOCAL"
        }
    }

    public static isSocialLeaguesSupported(userContext: UserContext, vertical?: ProductType): boolean {
        return !vertical || !SQUAD_DISABLED_PRODUCT_TYPES.some(ver => ver.toLowerCase() === vertical.toLowerCase())
    }

    public static async isSocialLeaguesWallHeaderFooterWidgetized(userContext: UserContext): Promise<boolean> {
        const { osName, appVersion, cpVersion } = userContext.sessionInfo
        const osNameLowerCase = osName.toLowerCase()

        if (appVersion < SOCIAL_LEAGUES_WALL_HEADER_FOOTER_WIDGETIZED_VERSION) {
            return false
        }

        if (appVersion > SOCIAL_LEAGUES_WALL_HEADER_FOOTER_WIDGETIZED_VERSION) {
            return true
        }

        const isInternalUser = (await userContext.userPromise).isInternalUser

        if (isInternalUser) {
            if (osNameLowerCase === "ios") {
                return cpVersion >= SOCIAL_LEAGUES_WALL_HEADER_FOOTER_WIDGETIZED_IOS_CP_INT
            } else if (osNameLowerCase === "android") {
                return cpVersion >= SOCIAL_LEAGUES_WALL_HEADER_FOOTER_WIDGETIZED_ANDROID_CP_INT
            }
        }
        else {
            if (osNameLowerCase === "ios") {
                return cpVersion >= SOCIAL_LEAGUES_WALL_HEADER_FOOTER_WIDGETIZED_IOS_CP_EXT
            } else if (osNameLowerCase === "android") {
                return cpVersion >= SOCIAL_LEAGUES_WALL_HEADER_FOOTER_WIDGETIZED_ANDROID_CP_EXT
            }
        }
    }

    public static isAppleIAPEnabled(userContext: UserContext): boolean {
        if (userContext.sessionInfo.osName !== "ios") {
            return false
        }
        // Remove this check to enable IAP for intl.
        if (AppUtil.isInternationalApp(userContext)) {
            return false
        }
        const { clientVersion } = userContext.sessionInfo
        return this.isInternationalApp(userContext) ? clientVersion >= APPLE_IAP_SUPPORTED_VERSION_INTL : clientVersion >= APPLE_IAP_SUPPORTED_VERSION_INDIA

    }

    public static isAndroidPlayIAPEnabled(userContext: UserContext): boolean {
        if (userContext.sessionInfo.osName !== "android") {
            return false
        }
        // Remove this check to enable iap
        if (AppUtil.isInternationalApp(userContext)) {
            return false
        }
        const { clientVersion } = userContext.sessionInfo
        return this.isInternationalApp(userContext) ? clientVersion >= ANDROID_IAP_SUPPORTED_VERSION_INTL : false
    }

    public static isIAPEnabled(userContext: UserContext): boolean {
        if (userContext.sessionInfo.osName === "ios") {
            return AppUtil.isAppleIAPEnabled(userContext)
        }

        if (userContext.sessionInfo.osName === "android") {
            return AppUtil.isAndroidPlayIAPEnabled(userContext)
        }
        return false
    }

    public static getAppUpdateUrl(userContext: UserContext): string {
        if (AppUtil.isInternationalApp(userContext)) {
            return userContext.sessionInfo.osName === "ios" ? "https://apps.apple.com/us/app/cure-fit-live/id1510990054" : "https://play.google.com/store/apps/details?id=fit.cure.intl.android"
        }
        return userContext.sessionInfo.osName === "ios" ? "https://itunes.apple.com/in/app/cure-fit-health-and-wellness/id1217794588?mt=8" : "https://play.google.com/store/apps/details?id=fit.cure.android"
    }


    public static isContactsSyncSupported(userContext: UserContext) {
        const { osName, appVersion, cpVersion } = userContext.sessionInfo
        const osNameLowerCase = osName.toLowerCase()
        if (osNameLowerCase === "android" || osNameLowerCase === "ios") {
            return appVersion >= CONTACTS_SYNC_SUPPORTED_VERSION
        }
    }


    public static async isYogaEMSupported(userContext: UserContext, hamletBusiness: HamletBusiness) {
        const { osName, clientVersion } = userContext.sessionInfo
        const osNameLowerCase = osName.toLowerCase()
        if (osNameLowerCase === "android") {
            const experimentId = YOGA_REPORT_HAMLET_PROD_EXPERIMENT_ID // owner "<EMAIL>"
            const isReversedABEnabled = false
            return (process.env.APP_ENV !== "PRODUCTION" || !isReversedABEnabled) && clientVersion >= YOGA_EM_SUPPORTED_VERSION(AppUtil.isInternationalApp(userContext))
        }
    }

    public static async isWaitlistConfirmationSupported(userContext: UserContext, hamletBusiness: HamletBusiness) {
        const { appVersion } = userContext.sessionInfo
        return appVersion >= CULT_WAITLIST_CONFIRMATION_PROBABILITY_VERSION
    }

    public static async isWaitlistExtensionSupported(userContext: UserContext, hamletBusiness: HamletBusiness) {
        const {osName, appVersion, cpVersion} = userContext.sessionInfo
        const osNameLowerCase = osName.toLowerCase()
        const isInternalUser = (await userContext.userPromise).isInternalUser
        if (appVersion >= CULT_WAITLIST_EXTENSION_VERSION) {
            return true
        } else if (osNameLowerCase === "android") {
            if (isInternalUser) {
                return cpVersion >= CULT_WAITLIST_EXTENSION_ANDROID_CP_INTERNAL
            } else {
                return cpVersion >= CULT_WAITLIST_EXTENSION_ANDROID_CP_EXTERNAL
            }
        } else if (osNameLowerCase === "ios") {
            if (isInternalUser) {
                return cpVersion >= CULT_WAITLIST_EXTENSION_IOS_CP_INTERNAL
            } else {
                return cpVersion >= CULT_WAITLIST_EXTENSION_IOS_CP_EXTERNAL
            }
        }
        return false
    }

    public static async isYogaReportSupported(userContext: UserContext, hamletBusiness: HamletBusiness) {
        const { clientVersion } = userContext.sessionInfo
        return clientVersion >= YOGA_REPORT_APP_SUPPORTED_VERSION(AppUtil.isInternationalApp(userContext)) && await AppUtil.isYogaEMSupported(userContext, hamletBusiness)
    }

    public static async isDiyEnergyMeterSupported(userContext: UserContext, hamletBusiness: HamletBusiness) {
        return !AppUtil.isSugarFitOrUltraFitApp(userContext) ? AppConfigUtil.evaluateBoolean("DIY_ENERGY_METER_SUPPORTED", userContext) : userContext.sessionInfo.osName.toLowerCase() === "ios"
    }

    public static async isLiveVideoPlayerBackgroundSupported(userContext: UserContext, hamletBusiness: HamletBusiness) {
        const { userAgent, clientVersion } = userContext.sessionInfo
        const isIntlApp = AppUtil.isInternationalApp(userContext)
        const isSugarFitOrUltraFitApp = AppUtil.isSugarFitOrUltraFitApp(userContext)
        if (isSugarFitOrUltraFitApp && userContext.sessionInfo.osName.toLowerCase() === "android") return false
        return userAgent === "APP" && clientVersion >= LIVE_VIDEO_PLAYER_BACKGROUND_SUPPORTED_VERSION(isIntlApp, isSugarFitOrUltraFitApp)
    }

    public static isCancelSquadInviteSuported(userContext: UserContext) {
        const { osName, appVersion, cpVersion } = userContext.sessionInfo
        const osNameLowerCase = osName.toLowerCase()
        if (osNameLowerCase === "android" || osNameLowerCase === "ios") {
            return appVersion >= CANCEL_SQUAD_INVITE_SUPPORTED_VERSION || process.env.ENVIRONMENT === "STAGE"
        }
    }

    public static isCultWorkoutTabPageInitialIndexFixNeeded(userContext: UserContext): boolean {
        const appVersion = userContext.sessionInfo.appVersion
        const osName = userContext.sessionInfo.osName.toLowerCase()
        if (osName === "ios" || osName === "android") {
            return appVersion >= CULT_WORKOUT_TAB_PAGE_INDEX_FIX_NEEDED_MIN_VERSION && appVersion <= CULT_WORKOUT_TAB_PAGE_INDEX_FIX_NEEDED_MAX_VERSION
        }
    }

    public static isShareActionWidgetSupported(userContext: UserContext): boolean {
        if (userContext.sessionInfo.osName && (userContext.sessionInfo.osName.toLowerCase() === "android" || userContext.sessionInfo.osName.toLowerCase() === "ios")) {
            return userContext.sessionInfo.appVersion >= SHARE_ACTION_WIDGET_SUPPORTED_VERSION
        }
    }

    public static isUpcomingItemFootersArraySupported(userContext: UserContext): boolean {
        if (userContext.sessionInfo.osName && (userContext.sessionInfo.osName.toLowerCase() === "android" || userContext.sessionInfo.osName.toLowerCase() === "ios")) {
            return userContext.sessionInfo.appVersion >= UPCOMING_ITEM_FOOTERS_ARRAY_SUPPORTED_VERSION
        }
    }

    public static isUpcomingItemTooltipSupported(userContext: UserContext): boolean {
        if (userContext.sessionInfo.osName && (userContext.sessionInfo.osName.toLowerCase() === "android" || userContext.sessionInfo.osName.toLowerCase() === "ios")) {
            return userContext.sessionInfo.appVersion >= UPCOMING_ITEM_TOOLTIP_SUPPORTED_VERSION
        }
    }

    public static isDiyWodSupported(userContext: UserContext): boolean {
        const osName = userContext.sessionInfo.osName.toLowerCase()
        const appVersion = userContext.sessionInfo.appVersion
        if (this.isInternationalApp(userContext)) {
            return true
        }
        if (osName === "android" || osName === "ios") {
            return appVersion >= DIY_WOD_SUPPORTED_VERSION
        }
        return false
    }

    public static async isNewWodViewWidgetSupported(userContext: UserContext): Promise<boolean> {
        return userContext.sessionInfo.appVersion >= NEW_WOD_VIEW_WIDGET_SUPPORTED_VERSION
    }

    public static isAuroraThemeWodViewWidgetSupported(userContext: UserContext): boolean {
        return userContext.sessionInfo.appVersion >= AURORA_THEME_WOD_VIEW_WIDGET_SUPPORTED_VERSION
    }

    public static isAuroraThemeWodViewWidgetSeeMoreActionSupported(userContext: UserContext): boolean {
        return userContext.sessionInfo.appVersion >= AURORA_THEME_WOD_VIEW_WIDGET_SEE_MORE_ACTION_SUPPORTED_VERSION
    }

    /*
        osname, appVersion, cpVersion are used to perform segmentation (if any) on user eligible for experiment
     */
    public static getHamletContext(userContext: UserContext, experimentIds: string[], skipAssignmentLog?: boolean): HamletContext {
        const hamletContext: HamletContext = {
            userId: userContext.userProfile?.userId,
            deviceId: userContext.sessionInfo?.deviceId,
            osname: userContext.sessionInfo?.osName,
            appVersion: userContext.sessionInfo?.clientVersion,
            cpVersion: userContext.sessionInfo?.cpVersion,
            experimentIds: experimentIds,
            tenant: AppUtil.getTenantFromUserContext(userContext),
            skipAssignmentLog: skipAssignmentLog,
        }
        return hamletContext
    }

    public static getHamletContextFromUserIdAndDeviceId(userId: string, deviceId: string, experimentIds: string[]): HamletContext {
        const hamletContext: HamletContext = {
            userId: userId,
            deviceId: deviceId,
            experimentIds: experimentIds,
            tenant: Tenant.CUREFIT_APP
        }
        return hamletContext
    }


    public static async isBuddiesJoiningClassWidgetsSupported(userContext: UserContext): Promise<boolean> {
        const { appVersion, cpVersion, osName } = userContext.sessionInfo
        if (AppUtil.isWeb(userContext)) {
            return true
        }
        if (osName && (osName.toLowerCase() === "android" || osName.toLowerCase() === "ios")) {
            return process.env.ENVIRONMENT === "STAGE" ||
                (appVersion >= BUDDIES_JOINING_CLASS_WIDGETS_SUPPORTED_VERSION)
        }
    }

    static isUserNotLoggedIn(userId: string, isUserLoggedIn: boolean) {
        if (AppUtil.isNotLoggedinUserId(userId) || !isUserLoggedIn) {
            return true
        } else {
            return false
        }
    }

    public static async isCultLiveUserProfileSupported(userContext: UserContext) {
        const { sessionInfo: { osName, appVersion, cpVersion } } = userContext
        const osNameLowerCase = osName.toLowerCase()
        const isInternalUser = (await userContext.userPromise).isInternalUser
        if (osNameLowerCase === "ios" || osNameLowerCase === "android") {
            return process.env.ENVIRONMENT === "STAGE" || (
                (appVersion > CULT_LIVE_USER_PROFILE_SUPPORTED_APP_VERSION ||
                    (isInternalUser && appVersion === CULT_LIVE_USER_PROFILE_SUPPORTED_APP_VERSION && ((osNameLowerCase === "android" && cpVersion >= CULT_LIVE_USER_PROFILE_SUPPORTED_ANDROID_CP_VERSION) || (osNameLowerCase === "ios" && cpVersion >= CULT_LIVE_USER_PROFILE_SUPPORTED_IOS_CP_VERSION))
                    )))
        }
    }

    public static isAuroraThemeUserProfileWidgetSupported(userContext: UserContext): boolean {
        return userContext.sessionInfo.appVersion >= AURORA_THEME_USER_PROFILE_WIDGET_SUPPORTED_VERSION
    }

    public static async isUserProfilStreakSupported(userContext: UserContext) {
        return (await userContext.userPromise).isInternalUser
    }

    public static async isCultLiveClpReportCarouselSupported(userContext: UserContext) {
        const { sessionInfo: { osName, appVersion, cpVersion } } = userContext
        const osNameLowerCase = osName.toLowerCase()
        const isInternalUser = (await userContext.userPromise).isInternalUser
        if (osNameLowerCase === "ios" || osNameLowerCase === "android")
            return (isInternalUser && appVersion === CULT_LIVE_CLP_REPORT_CAROUSEL_SUPPORTED_VERSION && ((osNameLowerCase === "android" && cpVersion >= CULT_LIVE_CLP_REPORT_CAROUSEL_ANDROID_CP_VERSION) || (osNameLowerCase === "ios" && cpVersion >= CULT_LIVE_CLP_REPORT_CAROUSEL_IOS_CP_VERSION)))
                || (appVersion >= CULT_LIVE_CLP_REPORT_CAROUSEL_EXTERNAL_SUPPORTED_VERSION)
    }

    public static isMandatoryStartDateSupported(userContext: UserContext): boolean {
        if (userContext.sessionInfo.osName && userContext.sessionInfo.osName.toLowerCase() === "browser")
            return true

        if (userContext.sessionInfo.osName && userContext.sessionInfo.osName.toLowerCase() === "android") {
            return userContext.sessionInfo.appVersion >= MANDATORY_START_DATE_VERSION_ANDROID
        } else {
            return userContext.sessionInfo.appVersion >= MANDATORY_START_DATE_VERSION_IOS
        }
    }

    public static isCultParQEnabled(userAgent: UserAgent, appVersion: number, osName: string, codepushversion: number, isInternalUser?: boolean): boolean {
        if (userAgent === "MBROWSER" || userAgent === "DESKTOP") {
            return false
        } else {
            if (appVersion >= 7.20) {
                return true
            }
        }
        return false
    }

    public static isCallReminderSupported(userContext: UserContext): boolean {
        const osName = userContext.sessionInfo.osName.toLowerCase()
        const appVersion = userContext.sessionInfo.appVersion
        if (osName === "android" || osName === "ios") {
            return appVersion >= CALL_REMINDER_VERSION
        }
        return false
    }

    public static isAppIVRReminderSupported(userContext: UserContext): boolean {
        const osName = userContext.sessionInfo.osName.toLowerCase()
        const appVersion = userContext.sessionInfo.appVersion
        if (osName === "android" || osName === "ios") {
            return appVersion >= 10.57
        }
        return false
    }

    public static isCultPulseFeatureSupported(userContext: UserContext): boolean {
        return userContext.sessionInfo.userAgent === "APP"
    }

    public static isWholeFitSupported(userContext: UserContext): boolean {
        if (userContext.sessionInfo.osName.toLowerCase() === "android") {
            return userContext.sessionInfo.appVersion >= WHOLE_FIT_VERSION_ANDROID || userContext.sessionInfo.cpVersion >= WHOLE_FIT_INTERNAL_VERSION_CP_ANDROID
        }
        if (userContext.sessionInfo.osName.toLowerCase() === "ios") {
            return userContext.sessionInfo.appVersion >= WHOLE_FIT_VERSION_IOS || userContext.sessionInfo.cpVersion >= WHOLE_FIT_INTERNAL_VERSION_CP_IOS
        }
        return false
    }

    public static isNewNoShowInterventionSupported(userContext: UserContext): boolean {
        return userContext.sessionInfo.appVersion >= 7.43
    }

    public static isNewTrainerViewSupported(userContext: UserContext): boolean {
        return (userContext.sessionInfo.userAgent === "APP" && userContext.sessionInfo.appVersion >= 7.44) || AppUtil.isWeb(userContext)
    }

    public static isAnnouncementSupportedInCare(userContext: UserContext, isInternalUser: boolean): boolean {
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        if (userContext.sessionInfo.appVersion > CARE_ANNOUNCEMENT_SUPPORTED_APP_VERSION) {
            return true
        }
        const cpVersion = userContext.sessionInfo.cpVersion
        if (userContext.sessionInfo.osName.toLowerCase() === "android") {
            return isInternalUser ? cpVersion >= CARE_ANNOUNCEMENT_SUPPORTED_CP_VERSION_INTERNAL_ANDROID : cpVersion >= CARE_ANNOUNCEMENT_SUPPORTED_CP_VERSION_EXTERNAL_ANDROID
        }
        if (userContext.sessionInfo.osName.toLowerCase() === "ios") {
            return isInternalUser ? cpVersion >= CARE_ANNOUNCEMENT_SUPPORTED_CP_VERSION_INTERNAL_IOS : cpVersion >= CARE_ANNOUNCEMENT_SUPPORTED_CP_VERSION_EXTERNAL_IOS
        }
    }

    public static isAnyActionSupportedInManageOptions(userContext: UserContext): boolean {
        return (userContext.sessionInfo.userAgent === "APP" && userContext.sessionInfo.appVersion >= 7.43)
    }

    public static isMultiImageSupported(userContext: UserContext): boolean {
        return (userContext.sessionInfo.userAgent === "APP" && userContext.sessionInfo.appVersion >= 7.57)
    }

    public static isCultCafeSupported(userContext: UserContext): boolean {
        if (userContext.sessionInfo.osName.toLowerCase() === "android") {
            return userContext.sessionInfo.appVersion > CULT_CAFE_VERSION_ANDROID || userContext.sessionInfo.cpVersion >= CULT_CAFE_VERSION_CP_ANDROID
        }
        if (userContext.sessionInfo.osName.toLowerCase() === "ios") {
            return userContext.sessionInfo.appVersion > CULT_CAFE_VERSION_IOS || userContext.sessionInfo.cpVersion >= CULT_CAFE_VERSION_CP_IOS
        }
        return false
    }

    public static isPaymentFitcashTextSupported(userContext: UserContext): boolean {
        if (process.env.ENVIRONMENT === "STAGE") {
            return true
        }
        if (userContext.sessionInfo.osName.toLowerCase() === "android") {
            return userContext.sessionInfo.appVersion >= PAYMENT_FITCASH_TEXT_SUPPORT_ANDROID
        }
        if (userContext.sessionInfo.osName.toLowerCase() === "ios") {
            return userContext.sessionInfo.appVersion >= PAYMENT_FITCASH_TEXT_SUPPORT_IOS
        }
        return false
    }

    public static isDubaiEnabled(userContext: UserContext): boolean {
        return true
    }

    public static isCenterShutdownSupported(userContext: UserContext, isInternalUser: boolean): boolean {
        return (userContext.sessionInfo.osName === "android" && userContext.sessionInfo.appVersion >= CENTER_SHUTDOWN_SUPPORTED_ANDROID) || (userContext.sessionInfo.osName === "ios" && userContext.sessionInfo.appVersion >= CENTER_SHUTDOWN_SUPPORTED_IOS)
    }

    public static isCultCentersViewOnlyModeSupported(userContext: UserContext): boolean {
        return userContext.sessionInfo.userAgent === "APP" && userContext.sessionInfo.appVersion >= 7.53
    }

    public static isSupportTicketViewEnabled(userContext: UserContext): boolean {
        if (this.isInternationalApp(userContext)) {
            return true
        }
        return this.isWeb(userContext) || (userContext.sessionInfo.userAgent === "APP" && userContext.sessionInfo.appVersion >= SUPPORT_TICKETS_VIEW_SUPPORTED)
    }

    public static isContactUsSupportTicketCreationEnabled(userContext: UserContext): boolean {
        const osName = userContext.sessionInfo.osName
        const appVersion = userContext.sessionInfo.appVersion
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        if (osName.toLowerCase() === "ios" || osName.toLowerCase() === "android") {
            return appVersion >= CONTACT_US_SUPPORT_TICKET_CREATION_ENABLED
        }
        return false
    }

    public static isSupportRecentOrdersViewEnabled(userContext: UserContext): boolean {
        const osName = userContext.sessionInfo.osName
        const appVersion = userContext.sessionInfo.appVersion
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        if (osName.toLowerCase() === "ios" || osName.toLowerCase() === "android") {
            return appVersion >= SUPPORT_RECENT_ORDERS_VIEW_ENABLED
        }
        return false
    }

    public static isSupportBookingsScreenSupported(userContext: UserContext): boolean {
        const osName = userContext.sessionInfo.osName
        const appVersion = userContext.sessionInfo.appVersion
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        if (osName.toLowerCase() === "ios" || osName.toLowerCase() === "android") {
            return appVersion >= SUPPORT_BOOKINGS_VIEW_ENABLED
        }
        return false
    }

    public static isInternationalPaymentSupported(userContext: UserContext): boolean {
        if (AppUtil.isWeb(userContext))
            return true
        if (userContext.sessionInfo.appVersion > 7.52) {
            return true
        }
        if (userContext.sessionInfo.osName.toLowerCase() === "android") {
            return userContext.sessionInfo.cpVersion >= 133
        }
        if (userContext.sessionInfo.osName.toLowerCase() === "ios") {
            return userContext.sessionInfo.cpVersion >= 125
        }
    }

    public static isCultPauseCancelSupported(userContext: UserContext): boolean {
        return userContext.sessionInfo.userAgent === "APP"
    }

    public static priceCutSupportedInProductPage(userContext: UserContext): boolean {
        return (userContext.sessionInfo.userAgent === "APP" && userContext.sessionInfo.appVersion >= 7.58) || AppUtil.isWeb(userContext)
    }

    public static isMembershipContextSupported(userContext: UserContext, isInternalUser: boolean): boolean {
        return (userContext.sessionInfo.userAgent === "APP" && userContext.sessionInfo.appVersion > MEMBERSHIP_CONTEXT_SUPPORTED_VERSION)
    }

    public static isLiveSessionReportSupported(userContext: UserContext, user: User): boolean {
        return userContext.sessionInfo.userAgent === "APP" && userContext.sessionInfo.appVersion >= LIVE_SESSION_REPORT_VERSION
    }

    public static isLiveSessionReportSelfieShareCardSupported(userContext: UserContext): boolean {
        return userContext.sessionInfo.userAgent === "APP" && userContext.sessionInfo.appVersion >= LIVE_SESSION_REPORT_SELFIE_SHARE_CARD_VERSION
    }

    public static isCalendarEventSupportedForLive(sessionInfo: SessionInfo, user: User): boolean {
        if (sessionInfo.orderSource == "SUGARFIT_APP" || sessionInfo.orderSource == "ULTRAFIT_APP")
            return sessionInfo.userAgent === "APP" && sessionInfo.appVersion >= SUGARFIT_APP_LIVE_CLASS_BOOKING_MIN_VERSION
        return sessionInfo.userAgent === "APP" && sessionInfo.appVersion >= LIVE_SESSION_CALENDAR_EVENT
    }

    public static isLiveClassDetailPageV2(sessionInfo: SessionInfo, user: User, userContext?: UserContext, isWebEnabled?: boolean): boolean {
        if (isWebEnabled && userContext && AppUtil.isWeb(userContext)) {
            return true
        }
        if (userContext && AppUtil.isInternationalApp(userContext)) {
            return true
        }
        if (AppUtil.isSugarFitOrUltraFitApp(userContext))
            return sessionInfo.userAgent === "DESKTOP" || (sessionInfo.userAgent === "APP" && sessionInfo.appVersion >= SUGARFIT_APP_LIVE_CLASS_BOOKING_MIN_VERSION)
        return sessionInfo.userAgent === "DESKTOP" || (sessionInfo.userAgent === "APP" && sessionInfo.appVersion >= LIVE_SESSION_DETAIL_PAGE_V2_VERSION)
    }

    public static isLiveClassSupported(userContext: UserContext, user: User): boolean {
        if (AppUtil.isInternationalApp(userContext)) {
            return true
        }
        const countryId = _.get(userContext, "userProfile.city.countryId", null)
        if (countryId !== "IN") {
            return false
        }
        if (AppUtil.isWeb(userContext) || userContext.sessionInfo.appVersion > LIVE_CLASS_SUPPORT_VERSION) {
            return true
        }
        const cpVersion = userContext?.sessionInfo?.cpVersion
        const osName = userContext?.sessionInfo?.osName
        if (!osName) {
            return false
        }
        if (osName.toLowerCase() === "android") {
            return user.isInternalUser ? cpVersion >= LIVE_CLASS_SUPPORT_CP_VERSION_INTERNAL_ANDROID : cpVersion >= LIVE_CLASS_SUPPORT_CP_VERSION_EXTERNAL_ANDROID
        }
        if (osName.toLowerCase() === "ios") {
            return user.isInternalUser ? cpVersion >= LIVE_CLASS_SUPPORT_CP_VERSION_INTERNAL_IOS : cpVersion >= LIVE_CLASS_SUPPORT_CP_VERSION_EXTERNAL_IOS
        }
    }

    public static isClassSlotIconUrlSupported(userContext: UserContext) {
        const { clientVersion } = userContext.sessionInfo
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        return (
            process.env.APP_ENV === "ALPHA" ||
            (tenant === Tenant.CUREFIT_APP &&
                (clientVersion >= CLASS_SLOT_ICON_URL_SUPPORTED_APP_VERSION) || AppUtil.isWeb(userContext))
        )
    }

    public static isLivePackSupported(userContext: UserContext): boolean {
        if (userContext.userProfile.cityId === "Dubai") {
            return false
        }
        return true
    }

    public static isClassDropoutSupported(userContext: UserContext): boolean {
        if (userContext.sessionInfo.userAgent === "APP") {
            const appVersion = userContext.sessionInfo.appVersion
            return appVersion > DROPOUT_SUPPORTED_VERSION
        }
        return false
    }

    public static isNotLoggedinUserId(userId: string): boolean {
        if (userId.startsWith("dummy_user_")) {
            return true
        } else {
            return false
        }
    }
    public static getAppUpdateUrlMessage(osName: string): string {
        if (osName === "ios") {
            return JSON.stringify({ "title": "New version is available", "subTitle": "Please update the app to continue", "url": "https://itunes.apple.com/in/app/cure-fit-health-and-wellness/id1217794588?mt=8" })
        } else if (osName === "android") {
            return JSON.stringify({ "title": "New version is available", "subTitle": "Please update the app to continue", "url": "https://play.google.com/store/apps/details?id=fit.cure.android" })
        }
        return undefined
    }

    public static fitbitLoginParams(userId: string): string {
        const params: any = {
            client_id: ClientDefaults.getFitbitAppId(),
            response_type: "token",
            scope: "activity heartrate sleep location nutrition profile settings social weight",
            expires_in: "31536000",
            state: userId,
            prompt: "login consent"
        }
        const uriParams = Object.keys(params).map(function (key) {
            return encodeURIComponent(key) + "=" + encodeURIComponent(params[key])
        }).join("&")
        const url = Constants.getFitbitAuthURL("authorize") + "?" + uriParams
        return url
    }

    public static timeout(ms: number) {
        return new Promise(resolve => setTimeout(resolve, ms))
    }

    public static callSourceFromContext(userContext: UserContext): OrderSource {
        if (!userContext || !userContext.sessionInfo) {
            return undefined
        }
        return AppUtil.callSource(userContext.sessionInfo.apiKey)
    }

    public static callSource(apiKey: string): OrderSource {
        let orderSource: OrderSource = "CUREFIT_APP"
        if (!apiKey) {
            return orderSource
        }
        if (apiKey === Constants.getCurefitWebApiKey() || apiKey === Constants.getJanusApiKey()) {
            orderSource = "CUREFIT_NEW_WEBSITE"
        } else if (apiKey === Constants.getPhonePeWebApiKey()) {
            orderSource = "PHONEPE_APP"
        } else if (apiKey && apiKey === Constants.getTataNeuWebApiKey()) {
            orderSource = "TATA_NEU_WEB_APP"
        } else if (apiKey && apiKey === Constants.getPhonePeWholefitApiKey()) {
            orderSource = "PHONEPE_CUREFIT_WHOLE"
        } else if (apiKey && apiKey === Constants.getGooglePayWebApiKey()) {
            orderSource = "EAT_GOOGLE_PAY_APP"
        } else if (apiKey === Constants.getPhonePeCareApiKey()) {
            orderSource = "PHONEPE_CUREFIT_CARE"
        } else if (apiKey === Constants.getPhonePeCultApiKey()) {
            orderSource = "PHONEPE_CUREFIT_CULT"
        } else if (apiKey === Constants.getPhonePeMindApiKey()) {
            orderSource = "PHONEPE_CUREFIT_MIND"
        } else if (apiKey === Constants.getPaytmPwaApiKey()) {
            orderSource = "PAYTM_PWA"
        } else if (apiKey === Constants.getLiveFitApiKey()) {
            orderSource = "LIVEFIT_APP"
        } else if (apiKey === Constants.getPhonePeWholefitApiKey()) {
            orderSource = "PHONEPE_CUREFIT_WHOLE"
        } else if (apiKey === Constants.getInternationWebsiteApiKey()) {
            orderSource = "CUREFIT_INTL_WEBSITE"
        } else if (apiKey === Constants.getCureFitTVApiKey()) {
            orderSource = "CUREFIT_TV_APP"
        } else if (apiKey === Constants.getCureFitTVIntlApiKey()) {
            orderSource = "CUREFIT_TV_INTL_APP"
        } else if (apiKey === Constants.getSugarFitAppApiKey()) {
            orderSource = "SUGARFIT_APP"
        } else if (apiKey === Constants.getSugarFitWebApiKey()) {
            orderSource = "SUGARFIT_WEBSITE"
        } else if (apiKey === Constants.getCultSportWebApiKey()) {
            orderSource = "CULTSPORT_WEBSITE"
        } else if (apiKey === Constants.getCultstoreApiKey()) {
            orderSource = "CULTSTORE_WEBSITE"
        } else if (apiKey === Constants.getMindFitAppApiKey()) {
            orderSource = "MINDFIT_APP"
        } else if (apiKey === Constants.getMindFitWebApiKey()) {
            orderSource = "MINDFIT_WEBSITE"
        } else if (apiKey === Constants.getTataNeuCultSportWebApiKey()) {
            orderSource = "TATA_NEU_CULTSPORT_WEBSITE"
        } else if (apiKey === Constants.getUltraFitWebApiKey()) {
            orderSource = "ULTRAFIT_WEBSITE"
        } else if (apiKey === Constants.getCultSportAppWebApiKey()) {
            orderSource = "CULTSPORT_APP"
        } else if (apiKey === Constants.getUltraFitAppApiKey()) {
            orderSource = "ULTRAFIT_APP"
        } else if (apiKey === Constants.getCultSportAppEmbedWebApiKey()) {
            // this is for cs embed webview in cult app
            orderSource = "CULTSPORT_EMBED_APP"
        } else if (apiKey === Constants.getCultWatchAppApiKey()) {
            orderSource = "CULTWATCH_APP"
        }
        return orderSource
    }

    static getMiniAppId(apiKey: string): MiniAppId {
        if (!apiKey) {
            return MiniAppId.PHONEPE_CUREFIT_EAT
        } else if (apiKey === Constants.getPhonePeCareApiKey()) {
            return MiniAppId.PHONEPE_CUREFIT_CARE
        } else if (apiKey === Constants.getPhonePeCultApiKey()) {
            return MiniAppId.PHONEPE_CUREFIT_CULT
        } else if (apiKey === Constants.getPhonePeMindApiKey()) {
            return MiniAppId.PHONEPE_CUREFIT_MIND
        } else if (apiKey === Constants.getPhonePeWholefitApiKey()) {
            return MiniAppId.PHONEPE_CUREFIT_WHOLE
        } else {
            return MiniAppId.PHONEPE_CUREFIT_EAT
        }
    }

    public static isTataNeuWebFlow(source: string) {
        return source === "TATA_NEU_WEB_APP" || source === "TATA_NEU_CULTSPORT_WEBSITE"
    }

    static appendDays = (num: number) => {
        return num === 1 ? `${num} day` : `${num} days`
    };

    public static getUserAlertInfo(user: User, userContext: UserContext): { alertInfo: AlertInfo, code: string } {
        let alertInfo: AlertInfo = undefined
        let code: string = undefined
        if (!user.email && !AppUtil.isNewSigninFlowSupported(userContext)) {
            code = ErrorCodes.INVALID_EMAIL
            alertInfo = {
                title: "Invalid email",
                subTitle: "You cannot checkout without a valid email. Please update email address in your profile",
                actions: [{
                    actionType: "NAVIGATION",
                    title: "Update Info",
                    url: "curefit://me"
                }],
                statusCode: EMAIL_NOT_FOUND_ERROR_CODE
            }
        } else if (!user.phone && !AppUtil.isSugarFitOrUltraFitApp(userContext)) {
            code = ErrorCodes.INVALID_PHONE
            alertInfo = {
                title: "Invalid phone number",
                subTitle: "You cannot checkout without a verified phone number. Please update your phone number in your profile",
                actions: [{
                    actionType: "NAVIGATION",
                    title: "Update Info",
                    url: "curefit://updatephone?pageFrom=cartcheckout"
                }],
                statusCode: MOBILE_NUMBER_NOT_FOUND_ERROR_CODE
            }
        } else if (user.isPhoneChurned) {
            code = ErrorCodes.INVALID_PHONE_CHURNED
            alertInfo = {
                title: "Inactive phone number",
                subTitle: "For security reasons, please verify your phone number to proceed.",
                actions: [{
                    actionType: "NAVIGATION",
                    title: "Update Info",
                    url: "curefit://updatephone?pageFrom=cartcheckout"
                }],
                statusCode: MOBILE_NUMBER_NOT_FOUND_ERROR_CODE
            }
        } else {
            if (!user.firstName && MealUtil.isUpdateNameSupported(userContext)) {
                code = ErrorCodes.INVALID_NAME
                alertInfo = {
                    title: "Invalid name",
                    subTitle: "You cannot checkout without a valid name. Please update your name in your profile",
                    actions: [{
                        actionType: "NAVIGATION",
                        title: "Update Info",
                        url: "curefit://updatename"
                    }],
                    statusCode: NAME_NOT_FOUND_ERROR_CODE
                }
            }
        }
        return { alertInfo: alertInfo, code: code }
    }
    public static isSubscriptionTypePresent(orderProducts: OrderProduct[]): boolean {
        return !_.isEmpty(orderProducts.filter(product => product.option.subscriptionType !== undefined))
    }
    public static isAutoRenewEnabled(orderProducts: OrderProduct[]): boolean {
        return !_.isEmpty(orderProducts.filter(product => product.option.autorenewalEnabled === true))
    }
    public static getSubscriptionProductId(orderProducts: OrderProduct[]): string {
        return orderProducts.find(product => product.option.subscriptionType !== undefined).productId
    }
    public static isWeb(userContext: UserContext): boolean {
        return (_.get(userContext, "sessionInfo.userAgent") === "DESKTOP") || (_.get(userContext, "sessionInfo.userAgent") === "MBROWSER")
    }
    public static isWebAppWithReq(req: express.Request): boolean {
        const userAgent: UserAgent = AuthUtil.getUserAgent(req)
        return this.isWebUserAgent(userAgent)
    }
    public static isWebUserAgent(userAgent: UserAgent): boolean {
        return userAgent === "DESKTOP" || userAgent === "MBROWSER"
    }
    public static isMobileApp(userContext: UserContext): boolean {
        return !this.isTVApp(userContext) && (_.get(userContext, "sessionInfo.userAgent") === "APP")
    }
    public static isMobileAppWithReq(req: express.Request): boolean {
        const userAgent: UserAgent = AuthUtil.getUserAgent(req)
        return userAgent === "APP"
    }
    public static isMWeb(userContext: UserContext): boolean {
        return userContext.sessionInfo.userAgent === "MBROWSER"
    }
    public static isDesktop(userContext: UserContext): boolean {
        return userContext.sessionInfo.userAgent === "DESKTOP"
    }

    public static _isCitySelectionActionSupported(osName: string, appVersion: number) {
        if (osName.toLowerCase() === "android") {
            return appVersion >= CITY_SELECTION_ACTION_ANDROID
        }
        if (osName.toLowerCase() === "ios") {
            return appVersion >= CITY_SELECTION_ACTION_IOS
        }
        return false
    }

    public static isCitySelectionActionSupported(userContext: UserContext): boolean {
        return AppUtil._isCitySelectionActionSupported(userContext.sessionInfo.osName, userContext.sessionInfo.appVersion)
    }

    public static _isChildUserBookingSupported(osName: string, appVersion: number, cpVersion: number) {
        if (osName.toLowerCase() === "android") {
            return appVersion >= CHILD_BOOKING_SUPPORT_ANDROID
        }
        if (osName.toLowerCase() === "ios") {
            return appVersion >= CHILD_BOOKING_SUPPORT_IOS
        }
        return false
    }

    public static isGearBrandNameSupported(userContext: UserContext): boolean {
        return AppUtil.isWeb(userContext) || AppUtil._isGearBrandNameSupported(userContext.sessionInfo.osName, userContext.sessionInfo.appVersion)
    }

    public static _isGearBrandNameSupported(osName: string, appVersion: number): boolean {
        if (!osName) return false

        if (osName.toLowerCase() === "android") {
            return appVersion >= GEAR_BRAND_NAME_SUPPORT_ANDROID
        }
        if (osName.toLowerCase() === "ios") {
            return appVersion >= GEAR_BRAND_NAME_SUPPORT_IOS
        }
        return false
    }

    public static shouldTruncateGearBrandName(userContext: UserContext): boolean {
        return AppUtil.isWeb(userContext) || AppUtil._shouldTruncateGearBrandName(userContext.sessionInfo.osName, userContext.sessionInfo.appVersion)
    }

    public static _shouldTruncateGearBrandName(osName: string, appVersion: number): boolean {
        if (osName.toLowerCase() === "android") {
            return appVersion >= GEAR_BRAND_NAME_SUPPORT_ANDROID && appVersion < TRUNCATE_GEAR_BRAND_NAME_ON_ANDROID_TILL
        }
        if (osName.toLowerCase() === "ios") {
            return appVersion >= GEAR_BRAND_NAME_SUPPORT_IOS && appVersion < TRUNCATE_GEAR_BRAND_NAME_ON_IOS_TILL
        }
        return false
    }

    public static isRedeemVoucherSectionSupported(osName: string, appVersion: number): boolean {
        if (osName.toLowerCase() === "android") {
            return appVersion >= REDEEM_VOUCHER_SUPPORT_ANDROID
        }
        if (osName.toLowerCase() === "ios") {
            return appVersion >= REDEEM_VOUCHER_SUPPORT_IOS
        }
        return false
    }

    public static isChildUserBookingSupported(userContext: UserContext): boolean {
        return AppUtil._isChildUserBookingSupported(userContext.sessionInfo.osName, userContext.sessionInfo.appVersion, userContext.sessionInfo.cpVersion)
    }

    public static filterUsersType(users: SubUserRelation[], relation: Relation): SubUserRelation[] {
        if (_.isEmpty(users)) {
            return _.filter(users, { relation: relation })
        }
    }

    public static isDeliveryStrikeOffSupported(userContext: UserContext): boolean {
        if (process.env.ENVIRONMENT === "STAGE") {
            return true
        }
        if (userContext.sessionInfo.osName.toLowerCase() === "android") {
            return userContext.sessionInfo.appVersion > DELIVERY_STRIKEOFF_ANDROID
        }
        if (userContext.sessionInfo.osName.toLowerCase() === "ios") {
            return userContext.sessionInfo.appVersion > DELIVERY_STRIKEOFF_IOS
        }
        return false
    }

    public static isWidgetizedOrderConfirmationSupported(userContext: UserContext): boolean {
        return userContext.sessionInfo.appVersion >= WIDGETIZED_ORDER_CONFIRMATION_VERSION
    }
    public static isAuroraOrderConfirmationSupported(userContext: UserContext): boolean {
        if (AppUtil.isSugarFitOrUltraFitApp(userContext))
            return userContext.sessionInfo.appVersion >= SUGARFIT_APP_LIVE_CLASS_BOOKING_MIN_VERSION
        return userContext.sessionInfo.appVersion >= 8.97
    }

    public static isOrderConfirmationV2Supported(userContext: UserContext): boolean {
        if (AppUtil.isSugarFitOrUltraFitApp(userContext))
            return userContext.sessionInfo.appVersion >= SUGARFIT_APP_LIVE_CLASS_BOOKING_MIN_VERSION
        return userContext.sessionInfo.appVersion >= 8.71
    }

    public static isSfNuxV2Supported(userContext: UserContext): boolean {
        if (AppUtil.isSugarFitApp(userContext))
            return userContext.sessionInfo.appVersion >= SUGARFIT_APP_NUX_V2_SUPPORTED_VERSION
        return false
    }

    public static isSfSubscriptionRenewalV2Supported(userContext: UserContext): boolean {
        if (AppUtil.isSugarFitApp(userContext)) {
            const { osName, appVersion, cpVersion } = userContext.sessionInfo
            if (appVersion >= SUGARFIT_APP_SUBSCRIPTION_RENEWAL_V2_SUPPORTED_VERSION) {
                return true
            } else if (appVersion >= 9.1) {
                return (osName === "ios" && cpVersion >= 143) || (osName === "android" && cpVersion >= 143)
            } else if (appVersion >= 9.0) {
                return (osName === "ios" && cpVersion >= 142)
            } else if (appVersion >= 8.99) {
                return (osName === "android" && cpVersion >= 142)
            }
        }
        return false
    }

    public static isSfCoachPenaltySupported(userContext: UserContext): boolean {
        if (AppUtil.isSugarFitApp(userContext))
            return userContext.sessionInfo.appVersion >= SUGARFIT_COACH_PENALTY_SUPPORTED_VERSION
        return false
    }

    public static isSfJuiceSupported(userContext: UserContext): boolean {
        if (AppUtil.isSugarFitApp(userContext))
            return userContext.sessionInfo.appVersion >= SUGARFIT_JUICE_SUPPORTED_VERSION
        return false
    }

    public static isClearCartFlagSupported(appVersion: number): boolean {
        return appVersion >= CLEAR_CART_FLAG_VERSION
    }

    public static isFitClubActiveMembershipRenewalSupported(userContext: UserContext): boolean {
        return userContext.sessionInfo.appVersion >= FITCLUB_ACTIVE_MEMBERSHIP_RENEWAL_VERSION
    }

    public static isFitClubNotLoggedActionSupported(userContext: UserContext): boolean {
        return userContext.sessionInfo.appVersion >= FITCLUB_NOT_LOGGED_IN_VERSION
    }

    public static async isFitClubCheckoutAlertSupported(userContext: UserContext): Promise<boolean> {
        const user = await userContext.userPromise
        if (!user.isInternalUser) {
            if (userContext.sessionInfo.osName === "ios" && userContext.sessionInfo.cpVersion >= FITCLUB_CHECKOUT_CP_VERSION_IOS) {
                return true
            }
            if (userContext.sessionInfo.osName === "android" && userContext.sessionInfo.cpVersion >= FITCLUB_CHECKOUT_CP_VERSION_ANDROID) {
                return true
            }
        }
        return userContext.sessionInfo.appVersion >= FITCLUB_CHECKOUT_ALERT_VERSION
    }

    public static isFitclubV2Supported(userContext: UserContext, isInternalUser: boolean): boolean {
        const appVersion = userContext.sessionInfo.appVersion
        const cpVersion = userContext.sessionInfo.cpVersion || 0
        const osName = userContext.sessionInfo.osName.toLowerCase()
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        if (osName.toLowerCase() === "android") {
            return isInternalUser ? (appVersion >= FITCLUB_SCRATCH_CARD_VERSION_RELEASE_ALL || (appVersion >= FITCLUB_SCRATCH_CARD_VERSION && cpVersion >= FITCLUB_SCRATCH_CARD_INTERNAL_CP_ANDROID))
                : (appVersion >= FITCLUB_SCRATCH_CARD_VERSION_RELEASE_ALL || (appVersion >= FITCLUB_SCRATCH_CARD_EXTERNAL_VERSION && cpVersion >= FITCLUB_SCRATCH_CARD_EXTERNAL_CP_ANDROID))
        }
        if (osName.toLowerCase() === "ios") {
            return isInternalUser ? (appVersion >= FITCLUB_SCRATCH_CARD_VERSION_RELEASE_ALL || (appVersion >= FITCLUB_SCRATCH_CARD_VERSION && cpVersion >= FITCLUB_SCRATCH_CARD_INTERNAL_CP_IOS))
                : (appVersion >= FITCLUB_SCRATCH_CARD_VERSION_RELEASE_ALL || (appVersion >= FITCLUB_SCRATCH_CARD_EXTERNAL_VERSION && cpVersion >= FITCLUB_SCRATCH_CARD_EXTERNAL_CP_IOS))
        }
        return false
    }

    public static isTrialClassMissedInterventionSupported(userContext: UserContext, isInternalUser: boolean, hamletEnabledForTheUser: boolean): boolean {
        const appVersion = _.get(userContext, "sessionInfo.appVersion")
        const osName = _.get(userContext, "sessionInfo.osName", "").toLowerCase()
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        if (osName.toLowerCase() === "android") {
            return isInternalUser ? (appVersion >= TRIAL_CLASS_MISSED_NO_SHOW_APP_SUPPORT) : (appVersion >= TRIAL_CLASS_MISSED_NO_SHOW_APP_SUPPORT && hamletEnabledForTheUser)
        }
        if (osName.toLowerCase() === "ios") {
            return isInternalUser ? (appVersion >= TRIAL_CLASS_MISSED_NO_SHOW_APP_SUPPORT) : (appVersion >= TRIAL_CLASS_MISSED_NO_SHOW_APP_SUPPORT && hamletEnabledForTheUser)
        }
        return false
    }

    public static isNutritionalTagFilterSupported(userContext: UserContext): boolean {
        return userContext.sessionInfo.appVersion >= NUTRITIONAL_TAG__FILTER_VERSION
    }

    public static isEatVariantsSupported(userContext: UserContext): boolean {
        return userContext.sessionInfo.appVersion >= EAT_VARIANTS_SUPPORTED_APP_VERSION
    }

    public static isLiveContentLocked(userContext: UserContext, isLocked: boolean, isUserEligibleForMonetisation: boolean, isUserEligibleForTrial: boolean): boolean {
        if (this.isInternationalApp(userContext)) {
            return isLocked && (this.isIAPEnabled(userContext) || this.isTVApp(userContext))
        }
        return isLocked || (isUserEligibleForMonetisation && isUserEligibleForTrial)
    }

    public static isSubscriptionAttachSupported(userContext: UserContext): boolean {
        const cpVersion = userContext.sessionInfo.cpVersion || 0
        const osName = userContext.sessionInfo.osName.toLowerCase()
        if (userContext.sessionInfo.appVersion >= SUBSCRIPTION_ADDON_SUPPORT_VERSION) {
            return true
        } else if (osName.toLowerCase() === "android") {
            return cpVersion >= SUBSCRIPTION_ADDON_CP_VERSION_ANDROID
        } else if (osName.toLowerCase() === "ios") {
            return cpVersion >= SUBSCRIPTION_ADDON_CP_VERSION_IOS
        } else {
            return false
        }
    }

    public static isDecimalWeightUpdateEnabled(userContext: UserContext, isInternalUser: boolean): boolean {
        if (process.env.ENVIRONMENT === "STAGE") {
            return true
        }
        const cpVersion = userContext.sessionInfo.cpVersion || 0
        const osName = userContext.sessionInfo.osName.toLowerCase()
        if (userContext.sessionInfo.appVersion > DECIMAL_WEIGHT_UPDATE_ENABLED) {
            return true
        } else {
            return false
        }
    }

    public static async isEatReorderWidgetSupported(userContext: UserContext, experimentsPromise: Promise<{
        assignmentsMap: {
            [experimentId: string]: UserAssignment;
        };
    }>): Promise<boolean> {
        return false
        // disbaled for now

        // const experimentID = EAT_CLP_RECO_HAMLET_EXPERIMENT
        // const appVersion = userContext.sessionInfo.appVersion
        // const recosExperiment = (await experimentsPromise)
        // const cpVersion = userContext.sessionInfo.cpVersion || 0
        // const osName = userContext.sessionInfo.osName.toLowerCase()
        // const user = await userContext.userPromise
        // function hasSupportedAppVersion() {
        //     if (userContext.sessionInfo.appVersion >= REORDER_EAT_APP_VERSION) {
        //         return true
        //     } else if (osName.toLowerCase() === "android") {
        //         return (user.isInternalUser ? (cpVersion >= REORDER_EAT_CP_VERSION_ANDROID_INTERNAL) : (cpVersion >= REORDER_EAT_CP_VERSION_ANDROID))
        //     } else if (osName.toLowerCase() === "ios") {
        //         return (user.isInternalUser ? (cpVersion >= REORDER_EAT_CP_VERSION_IOS_INTERNAL) : (cpVersion >= REORDER_EAT_CP_VERSION_IOS))
        //     } else {
        //         return false
        //     }
        // }
        // if (hasSupportedAppVersion() && recosExperiment && recosExperiment.assignmentsMap && !_.isEmpty(recosExperiment.assignmentsMap[experimentID]) && !_.isEmpty(recosExperiment.assignmentsMap[experimentID].bucket)) {
        //     const bucket = recosExperiment.assignmentsMap[experimentID].bucket
        //     const showReorderPayload = bucket.payload.find((pair: Payload) => {
        //         return pair.key === "showReorder"
        //     })
        //     if (showReorderPayload && showReorderPayload.value === true) {
        //         return true
        //     } else {
        //         return false
        //     }
        // } else {
        //     return false
        // }
    }

    public static isEatSoldOutCartItemRemovalSupported(userContext: UserContext): boolean {
        return userContext.sessionInfo.appVersion >= EAT_SOLDOUT_CART_ITEM_REMOVAL_SUPPORTED_VERSION
    }

    public static async isAppLaunchSkipLoginSupported(userId: string, deviceId: string, appVersion: number, hamletBusiness: HamletBusiness, logger: ILogger): Promise<boolean> {
        const experimentID = process.env.ENVIRONMENT === "STAGE" ? APP_LAUNCH_SKIP_LOGIN_HAMLET_EXPERIMENT_STAGE : APP_LAUNCH_SKIP_LOGIN_HAMLET_EXPERIMENT // owner "<EMAIL>"
        const skipExperiment: UserAllocation = { assignmentsMap: {} }
        if (appVersion >= APP_LAUNCH_SKIP_LOGIN_SUPPORTED_VERSION && skipExperiment && skipExperiment.assignmentsMap && !_.isEmpty(skipExperiment.assignmentsMap[experimentID]) && !_.isEmpty(skipExperiment.assignmentsMap[experimentID].bucket)) {
            const bucket = skipExperiment.assignmentsMap[experimentID].bucket
            const skipLoginPayload = bucket.payload.find((pair: Payload) => {
                return pair.key === "skipLogin"
            })
            logger.info(`Hamlet Skip Login payload ${JSON.stringify(bucket)}`)
            if (skipLoginPayload && skipLoginPayload.value === true) {
                return true
            } else {
                return false
            }
        } else {
            return false
        }
    }

    public static getMonthsFromDays(days: number): number {
        return Math.floor(days / 30)
    }

    public static isTherapistRecommendationSupported(userContext: UserContext): boolean {
        return !AppUtil.isWeb(userContext) && userContext.sessionInfo.appVersion >= THERAPIST_RECOMMENDATION_SUPPORTED_APP_VERSION
    }

    public static isTherapistRecommendationV2UISupported(userContext: UserContext): boolean {
        return !AppUtil.isWeb(userContext) && userContext.sessionInfo.appVersion >= THERAPIST_RECOMMENDATION_V2_SUPPORTED_APP_VERSION
    }

    public static isTherapistRecommendationV2UINotSupported(userContext: UserContext): boolean {
        return !AppUtil.isWeb(userContext) && userContext.sessionInfo.appVersion < THERAPIST_RECOMMENDATION_V2_SUPPORTED_APP_VERSION
    }

    public static async isWorkoutViewSupportedInCenterSelection(userContext: UserContext, hamletBusiness: HamletBusiness): Promise<boolean> {
        return false
    }

    public static async isEatCLPRecommendationSupported(userContext: UserContext, experimentsPromise: Promise<{
        assignmentsMap: {
            [experimentId: string]: UserAssignment;
        };
    }>): Promise<boolean> {
        return true
        // supporting for web as well

        // const experimentID = process.env.ENVIRONMENT === "STAGE" ? EAT_CLP_RECO_HAMLET_EXPERIMENT_STAGE : EAT_CLP_RECO_HAMLET_EXPERIMENT
        // const appVersion = userContext.sessionInfo.appVersion
        // const skipExperiment = await experimentsPromise
        // if (appVersion >= EAT_CLP_RECO_SUPPORT_VERSION && skipExperiment && skipExperiment.assignmentsMap && !_.isEmpty(skipExperiment.assignmentsMap[experimentID]) && !_.isEmpty(skipExperiment.assignmentsMap[experimentID].bucket)) {
        //     const bucket = skipExperiment.assignmentsMap[experimentID].bucket
        //     const showCategoryPayload = bucket.payload.find((pair: Payload) => {
        //         return pair.key === "showCategory"
        //     })
        //     if (showCategoryPayload && showCategoryPayload.value === true) {
        //         return true
        //     } else {
        //         return false
        //     }
        // } else {
        //     return false
        // }
    }

    public static isEatCurrencySupported(userContext: UserContext): boolean {
        return userContext.sessionInfo.appVersion >= EAT_CURRENCY_SUPPORTED_VERSION
    }

    public static async isEatDeliveryWindowSupported(userContext?: UserContext, _user?: User): Promise<boolean> {
        const user = !_.isNil(_user) ? _user : await userContext.userPromise
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        if (userContext.sessionInfo.appVersion > EAT_DELIVERY_WINDOW_SUPPORTED_VERSION) {
            return true
        }
        const cpVersion = userContext.sessionInfo.cpVersion
        if (userContext.sessionInfo.osName.toLowerCase() === "android") {
            return user.isInternalUser ? cpVersion >= EAT_DELIVERY_WINDOW__INTERNAL_CP_VERSION_ANDROID : cpVersion >= EAT_DELIVERY_WINDOW__CP_VERSION_ANDROID
        }
        if (userContext.sessionInfo.osName.toLowerCase() === "ios") {
            return user.isInternalUser ? cpVersion >= EAT_DELIVERY_WINDOW__INTERNAL_CP_VERSION_IOS : cpVersion >= EAT_DELIVERY_WINDOW__CP_VERSION_IOS
        }
    }
    static isNotificationsSupportedInClassBookingPage(userContext: UserContext) {
        return userContext.sessionInfo.appVersion >= 7.77
    }

    static isWholeFitV2ReviewSupported(userContext: UserContext): boolean {

        const osName = userContext.sessionInfo.osName
        const cpVersion = userContext.sessionInfo.cpVersion
        const appVersion = userContext.sessionInfo.appVersion

        if (osName.toLowerCase() === "android") {
            return cpVersion >= WHOLE_FIT_V2_CP_INTERNAL_VERSION_ANDROID || appVersion >= WHOLE_FIT_V2_REVIEW_SUPPORT
        } else if (osName.toLowerCase() === "ios") {
            return cpVersion >= WHOLE_FIT_V2_CP_INTERNAL_VERSION_IOS || appVersion >= WHOLE_FIT_V2_REVIEW_SUPPORT
        } else if (osName.toLowerCase() === "browser") {
            return true
        } else {
            return false
        }
    }

    static isGearShippedItemCancelationSupported(userContext: UserContext) {
        return userContext.sessionInfo.appVersion >= 7.81
    }

    static isL2FeedbackSupported(userContext: UserContext) {
        return userContext.sessionInfo.appVersion >= 7.81
    }

    static isTabbedFeedbackFlowSupported(userContext: UserContext) {
        if (AppUtil.isSugarFitOrUltraFitApp(userContext)) {
            return false
        }
        const osName = userContext.sessionInfo.osName
        if (osName.toLowerCase() === "android" || osName.toLowerCase() === "ios") {
            return userContext.sessionInfo.appVersion >= TABBED_FEEDBACK_FLOW_SUPPORTED_VERSION
        }
        return false
    }

    static isTabbedFeedbackFlowSupportedForSATAndMusic(userContext: UserContext) {
        if (AppUtil.isSugarFitOrUltraFitApp(userContext)) {
            return false
        }
        const osName = userContext.sessionInfo.osName
        if (osName.toLowerCase() === "android" || osName.toLowerCase() === "ios") {
            return userContext.sessionInfo.appVersion >= TABBED_FEEDBACK_FLOW_SUPPORTED_VERSION
        }
        return false
    }

    static isWidgetsSupportedInGearCollection(userContext: UserContext) {
        return userContext.sessionInfo.appVersion >= 7.82
    }

    static isCongoWidgetSupportedVersion(userContext: UserContext) {
        return userContext.sessionInfo.appVersion >= 10.34
    }

    static getCurrencySymbol(currency: string): string {
        switch (currency) {
            case "INR":
                return RUPEE_SYMBOL
            case "AED":
                return "AED"
            case "USD":
                return "\u0024"
            default:
                return RUPEE_SYMBOL
        }
    }

    static getRazorpayId(): string {
        if (process.env.ENVIRONMENT === "STAGE") {
            return "rzp_test_pHt1qa7WezUWek"
        } else return "rzp_live_5ouvBOVAd2cA4J"
    }

    static getNewPackPageActionUrl(actionUrl: string) {
        return `${actionUrl}&navigateToNewPackPage=true`
    }

    static isNewPackPageSupported(userContext: UserContext) {
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        if (process.env.ENVIRONMENT === "STAGE" || process.env.ENVIRONMENT === "LOCAL") {
            return true
        } else if (userContext.sessionInfo.appVersion >= NEW_CULT_PACK_APP_VERSION) {
            return true
        }
        return false
    }

    static isCultSocialSupported(userContext: UserContext) {
        const osName = userContext.sessionInfo.osName.toLowerCase()
        const appVersion = userContext.sessionInfo.appVersion
        if (AppUtil.isWeb(userContext)) {
            return false
        } else if (process.env.ENVIRONMENT === "STAGE" || process.env.ENVIRONMENT === "LOCAL") {
            return true
        } else if ((osName === "ios" || osName === "android") && appVersion >= CULT_SOCIAL_APP_VERSION) {
            return true
        }
        return false
    }
    static async isRescheduleClassSupported(userContext: UserContext, hamletBusiness: HamletBusiness): Promise<boolean> {
        const appVersion = userContext.sessionInfo.appVersion
        if (AppUtil.isWeb(userContext)) {
            return false
        } else if (appVersion >= 8.56) {
            return true
        }
        return false
    }
    static async isBookingScreenNudgesSupported(userContext: UserContext, hamletBusiness: HamletBusiness): Promise<boolean> {
        const appVersion = userContext.sessionInfo.appVersion
        if (AppUtil.isWeb(userContext)) {
            return false
        } else if (appVersion >= 8.59) {
            if (process.env.ENVIRONMENT === "STAGE" || process.env.ENVIRONMENT === "LOCAL") {
                return true
            }
            return await AppUtil.isExperimentEnabled(userContext, hamletBusiness, BOOKING_SCREEN_NUDGES_EXPERIMENT_ID)
        }
        return false
    }

    static removeAnotherUserTransferMembership(userContext: UserContext): boolean {
        const appVersion = userContext.sessionInfo.appVersion
        if (AppUtil.isWeb(userContext)) {
            return false
        } else if (appVersion >= 8.71) {
            return true
        }
        return false
    }

    static isBookingScreenFilterNudgesSupported(userContext: UserContext): boolean {
        const appVersion = userContext.sessionInfo.appVersion
        if (AppUtil.isWeb(userContext)) {
            return false
        } else if (appVersion >= 8.67) {
            return true
        }
        return false
    }


    static async isEatFloatingMenuWidgetSupported(userContext: UserContext, experimentsPromise: Promise<{
        assignmentsMap: {
            [experimentId: string]: UserAssignment;
        };
    }>): Promise<boolean> {
        const experimentID = EAT_FLOATING_MENU_EXPERIMENT
        const appVersion = userContext.sessionInfo.appVersion
        const recosExperiment = (await experimentsPromise)
        function hasSupportedAppVersion() {
            if (appVersion >= EAT_FLOATING_MENU_APP_VERSION) {
                return true
            }
        }
        if (hasSupportedAppVersion() && recosExperiment && recosExperiment.assignmentsMap && !_.isEmpty(recosExperiment.assignmentsMap[experimentID]) && !_.isEmpty(recosExperiment.assignmentsMap[experimentID].bucket)) {
            const bucket = recosExperiment.assignmentsMap[experimentID].bucket
            const showFloatingMenu = bucket.payload.find((pair: Payload) => {
                return pair.key === "showFloatingMenu"
            })
            if (showFloatingMenu && showFloatingMenu.value === true) {
                return true
            } else {
                return false
            }
        } else {
            return false
        }
    }

    public static isNewRecommendationEatClpWidgetSupported(userContext: UserContext) {
        return false
        // todo add the app version for new recommendation widget when going live
        //  return userContext.sessionInfo.appVersion >= RECOMMENDED_EATCLP_NEW_APP_VERSION
    }

    static isNewCenterPageSupported(userContext: UserContext) {
        return userContext.sessionInfo.appVersion >= CULT_NEW_CENTER_PAGE_SUPPORTED_VERSION
    }

    public static isMyReferralsSupported(userContext: UserContext) {
        const { appVersion } = userContext.sessionInfo
        return appVersion >= MY_REFFERAL_APP_VERSION
    }

    public static async isGiftCardSupported(userContext: UserContext) {
        if (AppUtil.isCultSportWebApp(userContext)) {
            return true
        }
        const { appVersion } = userContext.sessionInfo
        return appVersion >= GIFT_CARD_APP_VERSION
    }

    public static async isUnifiedGiftCardPageSupported(userContext: UserContext) {
        const { appVersion } = userContext.sessionInfo
        return appVersion >= UNIFIED_GIFT_CARD_APP_VERSION
    }

    public static isCheckoutDotComSupported(userContext: UserContext) {
        if (process.env.ENVIRONMENT === "STAGE") {
            return true
        }
        if (AppUtil.isWeb(userContext)) {
            return true
        }
        if (userContext.sessionInfo.osName.toLowerCase() === "android") {
            return userContext.sessionInfo.appVersion >= CHECKOUT_DOT_COM_INTEGRATION_ANDROID_VERSION
        }
        if (userContext.sessionInfo.osName.toLowerCase() === "ios") {
            return userContext.sessionInfo.appVersion >= CHECKOUT_DOT_COM_INTEGRATION_IOS_VERSION
        }
        return false
    }

    public static isMarketplaceSupported(userContext: UserContext, isInternalUser: boolean): boolean {
        return false
    }

    public static getOffersWithAddonsAndLabels(offersList: OfferV2[], label: UILabelType): OfferV2WithTnc[] {
        const offerDataItems: OfferV2WithTnc[] = []
        offersList.forEach(offer => {
            if (offer.uiLabels && offer.uiLabels[label]) {
                offerDataItems.push({
                    description: offer.uiLabels[label],
                    tnc: offer.tNc,
                    tncURL: offer.tNcUrl,
                    offerId: offer.offerId
                })
            }
            if (!_.isEmpty(offer.addons)) {
                offer.addons.forEach((addOn: OfferAddon) => {
                    if (addOn.uiLabels && addOn.uiLabels[label]) {
                        offerDataItems.push({
                            description: addOn.uiLabels[label],
                            tnc: [],
                            tncURL: addOn.tncUrl,
                            offerId: offer.offerId
                        })
                    }
                })
            }
        })
        return offerDataItems
    }

    public static isCardlessEMIPaymentOptionSupported(userContext: UserContext): boolean {
        if (this.isWeb(userContext)) {
            return true
        }
        const appVersion: number = _.get(userContext, "sessionInfo.appVersion")
        if (_.isNil(appVersion)) {
            return false
        }
        return appVersion >= CARDLESS_EMI_PAYMENT_OPTION_MIN_VERSION
    }

    public static async isClassBookingPageRecommendationSupported(userContext: UserContext) {
        return false
        // if (AppUtil.isWeb(userContext)) {
        //     return false
        // }
        // return userContext.sessionInfo.appVersion >= 7.85
    }

    public static isSquadsSupportedInBookingV2(userContext: UserContext) {
        const { appVersion } = userContext.sessionInfo
        if (appVersion >= 9.96) {
            return true
        }
        return false
    }

    public static getRedeemVoucherAction(userContext: UserContext): Action {
        const { appVersion } = userContext.sessionInfo
        if (appVersion > 7.85) {
            return {
                actionType: "NAVIGATION",
                url: "curefit://redeemvoucherpage"
            }
        }
        return {
            actionType: "SSO",
            url: "me/vouchers"
        }
    }
    public static isFitClubApplicable(userContext: UserContext, isFitClubMember: boolean) {
        const { appVersion } = userContext.sessionInfo
        if (appVersion < 8) {
            return isFitClubMember
        }
        return true
    }

    public static isNewLiveClassProductPageSupported(userContext: UserContext) {
        const osName = userContext.sessionInfo.osName.toLowerCase()
        const appVersion = userContext.sessionInfo.appVersion
        if (AppUtil.isWeb(userContext)) {
            return true
        } else if ((process.env.ENVIRONMENT === "STAGE" || process.env.ENVIRONMENT === "LOCAL") && appVersion >= LIVE_NEW_PRODUCT_PAGE_SUPPORTED_VERSION) {
            return true
        } else if ((osName === "ios" || osName === "android") && appVersion >= LIVE_NEW_PRODUCT_PAGE_SUPPORTED_VERSION) {
            return true
        }
        return false
    }

    public static isNewLiveClassProductPageActionChangeSupported(userContext: UserContext) {
        const osName = userContext.sessionInfo.osName.toLowerCase()
        const appVersion = userContext.sessionInfo.appVersion
        if (AppUtil.isWeb(userContext)) {
            return true
        } else if (process.env.ENVIRONMENT === "STAGE" || process.env.ENVIRONMENT === "LOCAL") {
            return true
        } else if ((osName === "ios" || osName === "android") && appVersion >= LIVE_NEW_PRODUCT_PAGE_MORE_ACTION_CHANGE_SUPPORTED) {
            return true
        }
        return false
    }

    public static isLivePtTncEnabled(userContext: UserContext) {
        const osName = userContext.sessionInfo.osName.toLowerCase()
        const appVersion = userContext.sessionInfo.appVersion
        if (AppUtil.isWeb(userContext)) {
            return false
        } else if ((process.env.ENVIRONMENT === "STAGE" || process.env.ENVIRONMENT === "LOCAL")) {
            return true
        }
        else if (appVersion >= LIVE_PT_ONBOARDING_SUPPORTED) {
            return true
        }
        return false
    }

    public static isLivePtOldOnboardingSupported(userContext: UserContext, isInternalUser: boolean) {
        const osName = userContext.sessionInfo.osName.toLowerCase()
        const appVersion = userContext.sessionInfo.appVersion
        if (AppUtil.isWeb(userContext)) {
            return false
        } else if (osName === "android" && appVersion >= LIVE_PT_ONBOARDING_SUPPORTED && isInternalUser) {
            return true
        }
        return false
    }

    public static isZoomSdkSupported(userContext: UserContext) {
        const appVersion = userContext.sessionInfo.appVersion
        return appVersion >= 8.30
    }

    static zoomAppUpdateAction(osName: string): Action {
        return {
            actionType: "SHOW_ALERT_MODAL",
            title: "JOIN NOW",
            meta: {
                title: "App Update Required",
                subTitle: "Please update your app for a hassle free session experience.",
                actions: [{ actionType: "EXTERNAL_DEEP_LINK", title: "Update", url: osName === "ios" ? "https://itunes.apple.com/in/app/cure-fit-health-and-wellness/id1217794588?mt=8" : "https://play.google.com/store/apps/details?id=fit.cure.android" }]
            }
        }
    }

    public static async isLivePTNewOnboardingSupported(userContext: UserContext, hamletBusiness: HamletBusiness) {
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        if ((process.env.ENVIRONMENT === "STAGE" || process.env.ENVIRONMENT === "LOCAL")) {
            return true
        }
        if (userContext.sessionInfo.osName.toLowerCase() === "ios") {
            return false
        }
        const appVersion = userContext.sessionInfo.appVersion
        // if (userContext.sessionInfo.osName.toLowerCase() === "ios" && appVersion >= 8.25) {
        //     return await this.isExperimentEnabled(userContext, hamletBusiness, ZOOM_IOS_HAMLET_EXPERIMENT_PROD)
        // }
        return appVersion >= 8.25
    }

    public static isNewCheckoutSupported(userContext: UserContext): boolean {
        const appVersion = userContext.sessionInfo.appVersion
        return appVersion >= NEW_CHEKOUT_VERSION
    }

    public static isEatDeliveryInstructionSupported(userContext: UserContext): boolean {
        const appVersion: number = _.get(userContext, "sessionInfo.appVersion")
        if (_.isNil(appVersion)) {
            return false
        }
        return appVersion >= EAT_DELIVERY_INSTRUCTION_MIN_VERSION
    }

    public static allowPtSessionBooking(userContext: UserContext): boolean {
        const appVersion: number = _.get(userContext, "sessionInfo.appVersion")
        if (_.isNil(appVersion)) {
            return false
        }
        return appVersion >= 9.37
    }

    public static isFitClubAllowed(userContext: UserContext, isFitClubMember: boolean) {
        const { appVersion } = userContext.sessionInfo
        if (appVersion < 8) {
            return isFitClubMember
        }
        return false
    }

    public static isWholefitc1CategorisationSupported(userContext: UserContext) {
        return userContext.sessionInfo.appVersion >= WHOLEFIT_C1_CATEGORISATION_SUPPORT
    }

    public static isOrderedMenuSupported(user: User, interfaces: CFServiceInterfaces) {
        const eatClpConfigs = interfaces.configService.getConfig<EatClpOverrideConfig>("EAT_CLP_OVERRIDES").configs
        return (eatClpConfigs ? eatClpConfigs.enableOrdering : false) || (process.env.APP_ENV === "ALPHA")
    }

    public static isClubbedBFSAndEMIPaymentOptionSupported(userContext: UserContext): boolean {
        if (this.isWeb(userContext)) {
            return false
        }
        const appVersion: number = _.get(userContext, "sessionInfo.appVersion")
        if (_.isNil(appVersion)) {
            return false
        }
        return appVersion >= CARDLESS_EMI_PAYMENT_OPTION_MIN_VERSION && appVersion <= CARDLESS_EMI_PAYMENT_OPTION_MAX_VERSION
    }

    public static isEatclpOrderTrackingSupported(userContext: UserContext, isCultCafe: boolean) {
        return false
        // return userContext.sessionInfo.appVersion >= EAT_CLP_ORDER_TRACKING_SUPPORT && !AppUtil.isWeb(userContext) && !isCultCafe
    }

    public static isLivePTSupported(userContext: UserContext): boolean {
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        const appVersion = userContext.sessionInfo.appVersion
        const { APP_ENV } = process.env
        if (APP_ENV === "STAGE") {
            return true
        }
        return appVersion > LIVE_PT_SUPPORTED_APP_VERSION
    }

    public static isNewLivePtProductPageSupported(userContext: UserContext, subCategoryCode: SUB_CATEGORY_CODE) {
        if (["LIVE_PERSONAL_TRAINING", "LIVE_SGT"].includes(subCategoryCode)) {
            const osName = userContext.sessionInfo.osName.toLowerCase()
            const appVersion = userContext.sessionInfo.appVersion
            if (AppUtil.isWeb(userContext)) {
                return true
            } else if ((process.env.ENVIRONMENT === "STAGE" || process.env.ENVIRONMENT === "LOCAL") && (appVersion === 8.2308 || appVersion === 8.3008 || appVersion === 8.30044 || appVersion === 8.31004 || appVersion === LIVE_PT_NEW_PRODUCT_PAGE_SUPPORTED)) {
                return true
            } else if (appVersion >= LIVE_PT_NEW_PRODUCT_PAGE_SUPPORTED) {
                return true
            }
        }
        return false
    }

    public static isLiveSgtProductPageSupported(userContext: UserContext, subCategoryCode: SUB_CATEGORY_CODE) {
        if (["LIVE_PERSONAL_TRAINING", "LIVE_SGT"].includes(subCategoryCode)) {
            return AppUtil.isNewLivePTBookingPageSupported(userContext)
        }
        return false
    }

    public static isGenderSelectionModalSupported(userContext: UserContext): boolean {
        if (AppUtil.isWeb(userContext)) {
            return true
        }
        const appVersion = userContext.sessionInfo.appVersion
        const { APP_ENV } = process.env
        if (APP_ENV === "STAGE" || APP_ENV === "LOCAL") {
            return true
        }
        return appVersion > GENDER_SELECTION_MODAL_SUPPORTED
    }

    public static async isHCMTippingSupported(userContext: UserContext, order: Order, userPromise: Promise<User>, interfaces: CFServiceInterfaces) {
        if (userContext.sessionInfo.appVersion >= EAT_CLP_HCM_TIPPING_SUPPORT && !order.productSnapshots[0].isPack) {
            let fitcashBalanceResponse: WalletBalance
            try {
                fitcashBalanceResponse = await interfaces.fitcashService.balance(userContext.userProfile.userId, userContext.userProfile.city.country.currencyCode)
            } catch (e) {
                interfaces.rollbarService.sendError(e, {extra: { userId: userContext.userProfile.userId }})
            }

            const fitcashBalance: number = _.get(fitcashBalanceResponse, "balance", 0)
            if (Math.ceil(fitcashBalance / 100) > order.totalPayable) {
                return false
            } else {
                return true
            }

        }
        return false
    }

    public static getLiveFeedbackId(userContext: UserContext, contentId: string) {
        return `LIVE_CLASS_${contentId}_${userContext.userProfile.userId}`
    }

    public static getAppVersion(req: express.Request) {
        const apiKey = req.headers["api-key"] as string || req.headers["apikey"] as string || req.query.apikey
        if (AppUtil.isLiveFitApp(apiKey)) {
            return LIVE_FIT_V1_TO_CF_APP_VERSION_MAPPING.toString()
        }
        return req.headers["appversion"] as string
    }

    public static async isLiveQnASupported(userContext: UserContext) {
        if (AppUtil.isWeb(userContext) && AppUtil.getTenantFromUserContext(userContext) === Tenant.CUREFIT_APP) {
            return true
        }
        return AppConfigUtil.evaluateBoolean("LIVE_QNA_SUPPORTED", userContext)
    }

    public static async isLiveQnASupportedInNowLive(userContext: UserContext) {
        if (AppUtil.isInternationalApp(userContext)) {
            return false
        }
        return AppUtil.isLiveQnASupported(userContext)
    }


    public static isLiveVideoCallFeedback(feedbackId: string): boolean {
        return feedbackId.indexOf("LIVE_VIDEO_CALL") === 0
    }

    public static isLivePTNewClpSupported(userContext: UserContext): boolean {
        return userContext.sessionInfo.appVersion >= 8.21
    }

    public static getPageType(pageType: LoginPageTypes, tenant: Tenant, clientVersion: number): LoginPageTypesCompat {
        if (!AppUtil.isUnifiedLoginSupported(tenant, clientVersion)) {
            return PAGE_TYPE_COMPATIBILITY_MAP[pageType]
        }
        return pageType
    }

    public static async getDefaultLogin(req: express.Request, countryService: ICountryService, CFAPICityService: ICFAPICityService): Promise<LoginPageTypesCompat> {
        const ip: string = req.ip || req.connection.remoteAddress
        const tenant: Tenant = AppUtil.getTenantFromReq(req)
        const clientVersion = Number(req.headers.clientversion)

        if (!ip) return AppUtil.getPageType(PageTypes.SocialLogin, tenant, clientVersion)

        const [countries, detectedCityResponseByIp] = await Promise.all([
            countryService.listCountries(tenant),
            CFAPICityService.getCityAndCountryByIp(tenant, ip)
        ])

        if ((tenant === Tenant.CUREFIT_APP || tenant === Tenant.MINDFIT) && detectedCityResponseByIp.detectedCountryCode === "IN") {
            return AppUtil.getPageType(PageTypes.PhoneLogin, tenant, clientVersion)
        }

        if (tenant === Tenant.SUGARFIT_APP || tenant === Tenant.ULTRAFIT_APP) {
            return AppUtil.getPageType(PageTypes.ChronicCareLogin, tenant, clientVersion)
        }

        const phoneLoginSupported = countries.find(country =>
            country.phoneLoginSupported &&
            country.countryCode === detectedCityResponseByIp.detectedCountryCode
        )

        if (phoneLoginSupported) {
            return AppUtil.getPageType(
                tenant === Tenant.LIVEFIT_APP ? PageTypes.UnifiedLogin : PageTypes.SocialLogin,
                tenant,
                clientVersion
            )
        }

        return AppUtil.getPageType(PageTypes.SocialLogin, tenant, clientVersion)
    }

    public static isLiveFitApp(apiKey: string) {
        const orderSource = this.callSource(apiKey)
        return orderSource === "LIVEFIT_APP" || orderSource === "CUREFIT_TV_INTL_APP"
    }

    public static isMindFitAppKey(apiKey: string) {
        const orderSource = this.callSource(apiKey)
        if (orderSource === "MINDFIT_APP" || orderSource === "MINDFIT_WEBSITE") {
            return true
        }
        return false
    }

    public static isSugarFitAppKey(apiKey: string) {
        const orderSource = this.callSource(apiKey)
        if (orderSource === "SUGARFIT_APP" || orderSource === "SUGARFIT_WEBSITE") {
            return true
        }
        return false
    }

    public static isUltraFitAppKey(apiKey: string) {
        const orderSource = this.callSource(apiKey)
        if (orderSource === "ULTRAFIT_APP" || orderSource === "ULTRAFIT_WEBSITE") {
            return true
        }
        return false
    }

    public static isCultSportWebApiKey(apiKey: string) {
        return (
            Constants.getCultSportWebApiKey() === apiKey
            || Constants.getCultSportAppEmbedWebApiKey() === apiKey
            || Constants.getTataNeuCultSportWebApiKey() === apiKey
            || Constants.getCultSportAppWebApiKey() === apiKey
            || Constants.getCultstoreApiKey() === apiKey
        )
    }

    public static isCultSportAppApiKey(apiKey: string) {
        return (
            Constants.getCultSportAppWebApiKey() === apiKey
        )
    }

    public static isCultstoreShopifyApiKey(apiKey: string) {
        return (
            Constants.getCultstoreApiKey() === apiKey
        )
    }

    public static isCultWatchAppKey(apiKey: string) {
        const orderSource = this.callSource(apiKey)
        if (orderSource === "CULTWATCH_APP") {
            return true
        }
        return false
    }

    public static isSugarFitOrUltraFitApp(userContext: UserContext): boolean {
        return AppUtil.isSugarFitApp(userContext) || AppUtil.isUltraFitApp(userContext)
    }

    public static isSugarFitApp(userContext: UserContext): boolean {
        return AppUtil.getTenantFromUserContext(userContext) === Tenant.SUGARFIT_APP
    }

    public static isMindFitApp(userContext: UserContext): boolean {
        const { orderSource } = userContext.sessionInfo
        return AppUtil.getAppTenantFromUserContext(userContext) === AppTenant.MINDFIT || orderSource === "MINDFIT_APP" || orderSource === "MINDFIT_WEBSITE"
    }

    public static isUltraFitApp(userContext: UserContext): boolean {
        return AppUtil.getTenantFromUserContext(userContext) === Tenant.ULTRAFIT_APP
    }

    public static isCultWatchApp(userContext: UserContext): boolean {
        return AppUtil.getTenantFromUserContext(userContext) === Tenant.CULTWATCH
    }

    public static isCultSportWebApp(userContext: UserContext): boolean {
        const { apiKey } = userContext.sessionInfo
        return AppUtil.isWeb(userContext) && AppUtil.isCultSportWebApiKey(apiKey)
    }

    public static isCultSportTataNeuApp(userContext: UserContext): boolean {
        const { orderSource } = userContext.sessionInfo
        return orderSource === "TATA_NEU_CULTSPORT_WEBSITE"
    }

    public static isTVApp(userContext: UserContext): boolean {
        const orderSource = userContext.sessionInfo.orderSource
        return this.isTVAppWithOrderSource(orderSource)
    }

    public static isTVAppWithApiKey(apiKey: string): boolean {
        return apiKey === Constants.getCureFitTVApiKey() || apiKey === Constants.getCureFitTVIntlApiKey()
    }

    public static isTVAppWithOrderSource(orderSource: OrderSource): boolean {
        return orderSource === "CUREFIT_TV_APP" || orderSource === "CUREFIT_TV_INTL_APP"
    }
    public static isTVAppWithReq(req: express.Request): boolean {
        let apiKey: string = req.headers["apikey"] as string
        if (!apiKey && req.headers["authorization"]) {
            apiKey = req.headers["authorization"].split(" ")?.[1]?.trim()
        }
        const orderSource: OrderSource = AppUtil.callSource(apiKey)
        return this.isTVAppWithOrderSource(orderSource)
    }

    public static isInternationalWeb(apiKey: string) {
        return this.callSource(apiKey) === "CUREFIT_INTL_WEBSITE"
    }

    public static isInternationalAppFromReq(req: express.Request): boolean {
        return AppUtil.getTenantFromReq(req) === Tenant.LIVEFIT_APP || AppUtil.getTenantFromReq(req) === Tenant.SUGARFIT_APP
    }

    public static isInternationalApp(userContext: UserContext): boolean {
        return AppUtil.getTenantFromUserContext(userContext) === Tenant.LIVEFIT_APP
    }

    public static isInternationalTLApp(userContext: UserContext): boolean {
        return AppConfigUtil.evaluateBoolean(TRAINER_LED_SUPPORTED, userContext)
    }

    public static isInternationalTLTVApp(userContext: UserContext): boolean {
        return AppConfigUtil.evaluateBoolean(TRAINER_LED_TV_SUPPORTED, userContext)
    }

    public static isIAPYearlyPackSupported(userContext: UserContext): boolean {
        return AppConfigUtil.evaluateBoolean("IAP_YEARLY_PACK_SUPPORTED", userContext)
    }

    public static getTenantFromReq(req: express.Request): Tenant {
        const apiKey: string = req.headers["api-key"] ?? req.headers["apikey"] ?? req.query.apikey
        if (AppUtil.isLiveFitApp(apiKey) || AppUtil.isInternationalWeb(apiKey)) {
            return Tenant.LIVEFIT_APP
        } else if (AppUtil.isSugarFitAppKey(apiKey)) {
            return Tenant.SUGARFIT_APP
        } else if (AppUtil.isUltraFitAppKey(apiKey)) {
            return Tenant.ULTRAFIT_APP
        } else if (AppUtil.isCultSportWebApiKey(apiKey)) {
            return Tenant.CULTSPORT_APP
        } else if (AppUtil.isMindFitAppKey(apiKey)) {
            return Tenant.MINDFIT
        } else if (AppUtil.isCultWatchAppKey(apiKey)) {
            return Tenant.CULTWATCH
        }
        return Tenant.CUREFIT_APP
    }

    public static getAppTenantFromReq(req: express.Request): AppTenant {
        const apiKey = req.headers["api-key"] as string || req.headers["apikey"] as string || req.query.apikey
        if (AppUtil.isLiveFitApp(apiKey) || AppUtil.isInternationalWeb(apiKey)) {
            return AppTenant.LIVEFIT
        } else if (AppUtil.isSugarFitAppKey(apiKey)) {
            return AppTenant.SUGARFIT
        } else if (AppUtil.isUltraFitAppKey(apiKey)) {
            return AppTenant.ULTRAFIT
        } else if (AppUtil.isCultSportWebApiKey(apiKey)) {
            return AppTenant.CULTSPORT
        } else if (AppUtil.isMindFitAppKey(apiKey)) {
            return AppTenant.MINDFIT
        } else if (AppUtil.isCultWatchAppKey(apiKey)) {
            return AppTenant.CULTWATCH
        }
        return AppTenant.CUREFIT
    }

    public static getLoginModesForAppTenant(appTenant: AppTenant, appConfigStore: IAppConfigStoreService) {
        const loginModes = JSON.parse(appConfigStore.getConfig("loginModes", "{\"truecaller\":false,\"tenantToLoginModes\":{\"curefit\":{\"truecaller\":false}}}"))
        return loginModes[appTenant] || {truecaller: false}
    }

    public static getAppTenantFromString(appTenantString: string): AppTenant {
        if (appTenantString === "onyx") {
            return AppTenant.ONYX
        } else if (appTenantString === "livefit") {
            return AppTenant.LIVEFIT
        } else if (appTenantString === "sugarfit") {
            return AppTenant.SUGARFIT
        } else if (appTenantString === "cultsport") {
            return AppTenant.CULTSPORT
        } else if (appTenantString === "mindfit") {
            return AppTenant.MINDFIT
        } else if (appTenantString === "ultrafit") {
            return AppTenant.ULTRAFIT
        }
        return AppTenant.CUREFIT
    }

    public static getAppTenantFromUserContext(userContext: UserContext): AppTenant {
        const { orderSource } = userContext.sessionInfo
        if (orderSource === "LIVEFIT_APP" || orderSource === "CUREFIT_INTL_WEBSITE" || orderSource === "CUREFIT_TV_INTL_APP") {
            return AppTenant.LIVEFIT
        }
        if (orderSource === "SUGARFIT_APP" || orderSource === "SUGARFIT_WEBSITE") {
            return AppTenant.SUGARFIT
        }
        if (AppUtil.isCultSportWebApp(userContext)) {
            return AppTenant.CULTSPORT
        }
        if (orderSource === "MINDFIT_APP" || orderSource === "MINDFIT_WEBSITE") {
            return AppTenant.MINDFIT
        }
        if (orderSource === "ULTRAFIT_APP" || orderSource === "ULTRAFIT_WEBSITE") {
            return AppTenant.ULTRAFIT
        }
        return AppTenant.CUREFIT
    }

    public static getTenantForContentSource(userContext: UserContext): Tenant {
        // TODO Add sugarfit tenant once we have moved the complete users live membership to sugarfit
        const { orderSource } = userContext.sessionInfo
        if (orderSource === "LIVEFIT_APP" || orderSource === "CUREFIT_INTL_WEBSITE" || orderSource === "CUREFIT_TV_INTL_APP") {
            return Tenant.LIVEFIT_APP
        }
        if (AppUtil.isCultSportWebApp(userContext)) {
            return Tenant.CULTSPORT_APP
        }
        return Tenant.CUREFIT_APP
    }

    public static getTenantFromUserContext(userContext: UserContext): Tenant {
        // TODO Add sugarfit tenant once we have moved the complete users live membership to sugarfit
        if (_.isNil(userContext.sessionInfo)) {
            return Tenant.CUREFIT_APP
        }
        const { orderSource } = userContext.sessionInfo
        if (orderSource === "LIVEFIT_APP" || orderSource === "CUREFIT_INTL_WEBSITE" || orderSource === "CUREFIT_TV_INTL_APP") {
            return Tenant.LIVEFIT_APP
        }
        if (orderSource === "SUGARFIT_APP" || orderSource === "SUGARFIT_WEBSITE") {
            return Tenant.SUGARFIT_APP
        }
        if (orderSource === "MINDFIT_APP" || orderSource === "MINDFIT_WEBSITE") {
            return Tenant.MINDFIT
        }
        if (orderSource === "ULTRAFIT_APP" || orderSource === "ULTRAFIT_WEBSITE") {
            return Tenant.ULTRAFIT_APP
        }
        if (AppUtil.isCultSportWebApp(userContext)) {
            return Tenant.CULTSPORT_APP
        }
        if (orderSource === "CULTWATCH_APP") {
            return Tenant.CULTWATCH
        }
        return Tenant.CUREFIT_APP
    }

    public static isUnifiedLoginSupported(tenant: Tenant, clientVersion: number): boolean {
        return (
            (tenant === Tenant.CUREFIT_APP &&
                clientVersion >= UNIFIED_LOGIN_SUPPORTED_CF) ||
            (tenant === Tenant.LIVEFIT_APP &&
                clientVersion >= UNIFIED_LOGIN_SUPPORTED_CFINTL) || (tenant === Tenant.SUGARFIT_APP) || (tenant === Tenant.ULTRAFIT_APP)  || (tenant === Tenant.MINDFIT)
        )
    }

    public static isRelatedRecipesSupportedInPremiere(userContext: UserContext): boolean {
        const appVersion = userContext.sessionInfo.appVersion
        return appVersion >= LIVE_PREMIERE_RELATED_RECIPES_SUPPORTED_VERSION
    }

    public static async isEatClpShowMoreLessSupported(userContext: UserContext): Promise<boolean> {
        const { appVersion } = userContext.sessionInfo
        return appVersion >= EAT_CLP_SHOW_MORE_LESS_SUPPORTED_APP_VERSION
    }

    public static isDigitalServiceableAtLocation(tenant: Tenant, cityId: String, cityService: ICityService): boolean {
        if (tenant == Tenant.LIVEFIT_APP) {
            // default city for international is unserviceable
            return cityId != cityService.getDefaultCity(tenant).cityId
        }
        return true
    }

    public static isNewRecipeFilterWidgetSupported(userContext: UserContext) {
        const { clientVersion } = userContext.sessionInfo
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        return (
            (tenant === Tenant.CUREFIT_APP &&
                clientVersion >= EAT_RECIPE_CLP_NEW_FILTER_WIDGET_SUPPORT) ||
            tenant === Tenant.LIVEFIT_APP ||
            tenant === Tenant.SUGARFIT_APP ||
            tenant === Tenant.ULTRAFIT_APP
        )
    }

    public static async isRecipeFilterV2WidgetSupported(userContext: UserContext) {
        const { clientVersion } = userContext.sessionInfo
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        return (
            (tenant === Tenant.CUREFIT_APP &&
                clientVersion >= EAT_RECIPE_CLP_NEW_FILTER_V2_WIDGET_SUPPORT) ||
            (tenant === Tenant.LIVEFIT_APP &&
                clientVersion >= EAT_RECIPE_CLP_NEW_FILTER_V2_WIDGET_SUPPORT_CFINTL) ||
            tenant === Tenant.ULTRAFIT_APP ||
            tenant === Tenant.SUGARFIT_APP
        )
    }

    public static getCountryId(userContext: UserContext) {
        if (!AppUtil.isInternationalApp(userContext)) {
            return "IN"
        }
        return _.get(userContext, "userProfile.city.countryId")
    }
    public static RecipeRateAgainNotAllowedSupported(userContext: UserContext) {
        return userContext.sessionInfo.appVersion >= RECIPE_RATE_AGAIN_NOT_ALLOWED_SUPPORTED
    }

    public static async isRecipeShareActionSupported(userContext: UserContext) {
        return userContext.sessionInfo.appVersion >= RECIPE_SHARE_ACTION_SUPPORT || AppUtil.isWeb(userContext)
    }

    public static isShowingZeroPriceSupported(userContext: UserContext): boolean {
        return userContext.sessionInfo.appVersion >= 8.23
    }

    public static isPhoneNumberUpdateRequired(countryCode: string): boolean {
        return countryCode === "IN" // enabling phone login only for india
    }

    public static async playRecipeVideoByDefault(userContext: UserContext, hamletBusiness: HamletBusiness) {
        if (userContext.sessionInfo.appVersion >= RECIPE_VIDEO_AUTOPLAY_SUPPORT) {
            return true
            // const experimentId = process.env.APP_ENV === "PRODUCTION" ? RECIPE_VIDEO_AUTOPLAY_EXPERIMENT_ID_PROD : RECIPE_VIDEO_AUTOPLAY_EXPERIMENT_ID_STAGE
            //
            // if (userExperiments && userExperiments.assignmentsMap && !_.isEmpty(userExperiments.assignmentsMap[experimentId]) && !_.isEmpty(userExperiments.assignmentsMap[experimentId].bucket)) {
            //     const bucket = userExperiments.assignmentsMap[experimentId].bucket
            //     const playVideo = bucket.payload.find((pair: Payload) => {
            //         return pair.key === "playVideo"
            //     })
            //     if (playVideo && playVideo.value === true) {
            //         return true
            //     }
            // } else {
            //     return true // if user is not in any bucket, return true  for  him
            // }
        }
        return false
    }


    public static livePTFooterWidgetSupported(userContext: UserContext): boolean {
        return AppUtil.isWeb(userContext) || userContext.sessionInfo.appVersion >= 8.26
    }

    public static isNewNutritionistFlowSupported(userContext: UserContext) {
        return (userContext.sessionInfo.appVersion >= NUTRITIONIST_BUNDLE_SESSION_VERSION)
    }

    public static getRequestSource(userContext: UserContext) {
        const sessionInfo = userContext.sessionInfo
        const osName = !_.isEmpty(sessionInfo.osName) && (sessionInfo.osName.toLowerCase() === "ios" || sessionInfo.osName.toLowerCase() === "android") ? sessionInfo.osName.toLowerCase() : undefined
        const source: OrderSource = AppUtil.callSourceFromContext(userContext)
        if (osName) {
            return {
                source: source,
                osName: osName
            }
        }
        return {
            source: source
        }
    }

    public static isLivePTTrainerRecommendationSupported(userContext: UserContext): boolean {
        if (AppUtil.isWeb(userContext)) {
            return true
        }
        if (process.env.APP_ENV === "STAGE") {
            return true
        }
        const appVersion = userContext.sessionInfo.appVersion
        return appVersion > LIVE_PT_TRAINER_RECO_APP_VERSION
    }


    public static isChangeTrainerTooltipSupported(userContext: UserContext): boolean {
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        if (process.env.APP_ENV === "STAGE" || process.env.APP_ENV === "LOCAL") {
            return true
        }
        const appVersion = userContext.sessionInfo.appVersion
        return appVersion >= CHANGE_TRAINER_TOOLTIP_ANNOUNCEMENT_VERSION
    }

    public static isDeeplinkShareForOnDemandCollectionVideosSupported(userContext: UserContext): boolean {
        const appVersion = userContext.sessionInfo.appVersion
        return appVersion >= ON_DEMAND_COLLECTIONS_VIDEO_DEEPLINK_SHARE_SUPPORT || AppUtil.isWeb(userContext)
    }

    public static isNewNoteWidgetSuported(userContext: UserContext) {
        if (AppUtil.isWeb(userContext)) {
            return true
        }
        const appVersion = userContext.sessionInfo.appVersion
        return appVersion >= 8.31
    }

    public static isTVCompanionExperienceSupported(userContext: UserContext) {
        const userAgent = userContext.sessionInfo.userAgent
        // const clientVersion = userContext.sessionInfo.clientVersion
        const { APP_ENV } = process.env
        if (APP_ENV === "STAGE") {
            return true
        }
        if (!AppUtil.isTVApp(userContext) && !(userAgent === "DESKTOP" || userAgent === "MBROWSER")) {
            return true
        }
        return false
    }

    public static isDevOptionCountrySelectorSupported(userContext: UserContext) {
        return AppUtil.isInternationalApp(userContext) && userContext.sessionInfo.clientVersion >= DEVOPTIONS_COUNTRY_SELECTOR_SUPPORTED
    }

    public static isMinAppVersion(minAppVersion: number, userContext: UserContext) {
        if (!userContext) {
            return false
        }
        if (this.isInternationalApp(userContext)) {
            return false
        }
        const clientVersion = userContext.sessionInfo.clientVersion
        const osName = userContext.sessionInfo.osName.toLowerCase()
        if (osName === "android" || osName === "ios") {
            return clientVersion >= minAppVersion
        }
        return false
    }

    public static async isOnDemandAutoPlaySupported(userContext: UserContext): Promise<boolean> {
        const appVersion = userContext.sessionInfo.appVersion
        const user = await userContext.userPromise
        return appVersion >= ON_DEMAND_AUTOPLAY_SUPPORTED_VERSION && user.isInternalUser
    }

    static isLiveSGTSupported(userContext: UserContext) {
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        if (process.env.ENVIRONMENT === "LOCAL" || process.env.ENVIRONMENT === "STAGE") {
            return true
        }
        const appVersion = userContext.sessionInfo.appVersion
        if (appVersion >= LIVE_SGT_SUPPORTED_VERSION) {
            return true
        }
        return false
    }

    public static isLiveSGTReadMoreSupported(userContext: UserContext) {
        const appVersion = userContext.sessionInfo.appVersion
        if (process.env.ENVIRONMENT === "LOCAL" || process.env.ENVIRONMENT === "STAGE") {
            return true
        }
        if (appVersion >= LIVE_SGT_READ_MORE_SUPPORTED_VERSION) {
            return true
        }
    }

    public static isPackBrowsePackInfoSupported(userContext: UserContext) {
        if (process.env.ENVIRONMENT === "LOCAL" || process.env.ENVIRONMENT === "STAGE") {
            return true
        }
        const appVersion = userContext.sessionInfo.appVersion
        if (appVersion >= PACK_BROWSE_PACK_INFO_SUPPORTED_VERSION) {
            return true
        }
    }

    public static isLiveSGTCheckoutChangesSupported(userContext: UserContext) {
        if (process.env.ENVIRONMENT === "LOCAL" || process.env.ENVIRONMENT === "STAGE") {
            return true
        }
        const appVersion = userContext.sessionInfo.appVersion
        if (appVersion >= LIVE_SGT_CHECKOUT_CHANGE_SUPPORTED_VERSION) {
            return true
        }
    }

    static isMyClassesWidgetV2Supported(userContext: UserContext) {
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        const appVersion = userContext.sessionInfo.appVersion
        if (appVersion >= MY_LIVE_CLASSES_WIDGET_V2_SUPPORTED) {
            return true
        } else if (process.env.ENVIRONMENT === "STAGE" || process.env.ENVIRONMENT === "LOCAL" && appVersion === 8.3704) {
            return true
        }
        return false
    }

    public static async isAdvertisementSupported(userContext: UserContext, interfaces: CFServiceInterfaces): Promise<boolean> {
        // const user = await interfaces.userCache.getUser(userContext.userProfile.userId)

        return userContext.sessionInfo.appVersion >= ADVERTISEMENT_SUPPORT_VERSION

    }

    static isCoachFitVerticalSupported(userContext: UserContext) {
        return userContext.sessionInfo.appVersion >= COACH_FIT_SUPPORTED_APP_VERSION
    }

    static getCultPage(userContext: UserContext) {
        return (!this.isInternationalApp(userContext) ? "CultAtHome" : "CultLiveIntlPage")
    }

    static getMindPage(userContext: UserContext) {
        return (!this.isInternationalApp(userContext) ? "MindAtHome" : "MindLiveIntlPage")
    }

    static isShowTruncatedListSupported(userContext: UserContext) {
        const { appVersion, cpVersion, osName } = userContext.sessionInfo

        if (appVersion === SHOW_TRUNCATED_LIST_SUPPORT) {
            if (osName.toLowerCase() === "android" && cpVersion === SHOW_TRUNCATED_LIST_SUPPORT_CP_ANDROID) {
                return true
            } else if (osName.toLowerCase() === "ios" && cpVersion === SHOW_TRUNCATED_LIST_SUPPORT_CP_IOS) {
                return true
            }
        } else if (appVersion > SHOW_TRUNCATED_LIST_SUPPORT) {
            return true
        }
        return false
    }

    static isCovidHomeMonitoringNewUISupported(userContext: UserContext) {
        return userContext.sessionInfo.appVersion >= 8.38
    }

    static isNewLivePTBookingPageSupported(userContext: UserContext) {
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        return userContext.sessionInfo.appVersion >= LIVE_SGT_SUPPORTED_VERSION
    }

    static isIntlFitnessDeviceIntegrationEnabled(osName: string, clientVersion: number) {
        return clientVersion >= 1.2 && osName === "ios"
    }


    static isLivePTCrossGenderMatchingSupported(userContext: UserContext, gender: string) {
        return userContext.sessionInfo.appVersion >= 8.40 && gender === "Female"
    }

    public static isRecipeCustomFilterSupported(userContext: UserContext) {
        // todo: Nisheet enable this
        return userContext.sessionInfo.appVersion >= EAT_LIVE_CUSTOM_FILTER_SUPPORT && false
    }

    public static shouldShowEnergyMeterDisabledMessage(userContext: UserContext) {
        return userContext.sessionInfo.osName === "android" && userContext.sessionInfo.clientVersion === 8.40
    }

    public static isEatLiveVideoSearchResultSupported(userContext: UserContext) {
        return !AppUtil.isWeb(userContext) && userContext.sessionInfo.appVersion >= EAT_LIVE_VIDEO_SEARCH_RESULT_SUPPORT && false
        // dsiabled video search results for now
    }

    public static isIntlWidgetNamesSupported(userContext: UserContext) {
        return AppUtil.isInternationalApp(userContext) && userContext.sessionInfo.clientVersion >= INTL_WIDGET_NAMES_VERSION
    }

    public static isLiveImmediateFeedbackSupported(userContext: UserContext): boolean {
        return AppConfigUtil.evaluateBoolean("IMMEDIATE_LIVE_FEEDBACK_SUPPORTED", userContext)
    }

    public static isCareAgentFilterSupported(userContext: UserContext) {
        return AppUtil.isWeb(userContext) || userContext.sessionInfo.appVersion >= CARE_AGENT_FILTER_SUPPORT_APP_VERSION
    }

    public static isCutlerySelectClientSideHandleSupported(userContext: UserContext) {
        return userContext.sessionInfo.appVersion >= CLIENT_SIDE_CUTLERY_SELECT_SUPPORT
    }

    public static isAppVersionBelow(userContext: UserContext, internationalAppVersion: number, indianAppVersion: number): Boolean {
        const { sessionInfo } = userContext
        if (AppUtil.isInternationalApp(userContext)) return sessionInfo.clientVersion < internationalAppVersion
        return sessionInfo.userAgent === "APP" && sessionInfo.clientVersion < indianAppVersion
    }

    public static async isExperimentEnabled(userContext: UserContext, hamletBusiness: HamletBusiness, experimentId: string, bucketId?: string, skipAssignmentLog?: boolean): Promise<boolean> {
        const hamletContext = AppUtil.getHamletContext(userContext, [experimentId], skipAssignmentLog)
        const getUserAllocations = hamletBusiness.getUserAllocations(hamletContext)
        const supportAllocations = (await eternalPromise(getUserAllocations)).obj
        if (bucketId) {
            return bucketId === supportAllocations?.assignmentsMap[experimentId]?.bucket?.bucketId
        }
        const bucket = supportAllocations?.assignmentsMap[experimentId]?.bucket
        return !_.isEmpty(bucket)
    }

    static isLivePTPackPageWithOffersSupported(userContext: UserContext) {
        return userContext.sessionInfo.appVersion >= 8.43
    }

    public static isProgressiveCartOffersSupported(userContext: UserContext) {
        const { appVersion, cpVersion, osName } = userContext.sessionInfo
        if (appVersion === EATCLP_PROGRESSIVE_CART_OFFERS_SUPPORT_INTERNAL) {
            return osName.toLowerCase() === "android" && cpVersion === EATCLP_PROGRESSIVE_CART_OFFERS_SUPPORT_CP_ANDROID
        } else if (appVersion >= EATCLP_PROGRESSIVE_CART_OFFERS_SUPPORT_EXTERNAL) {
            return true
        }
        return false
    }

    public static async isCultAfterLockdownSupported(userContext: UserContext) {
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        const { appVersion, osName, cpVersion } = userContext.sessionInfo
        if (appVersion >= CULT_OFFLINE_RELAUNCH_APP_VERSION) {
            return true
        }
    }


    public static isPersonalCoachWidgetSupported(userContext: UserContext) {
        const { appVersion } = userContext.sessionInfo
        if (!AppUtil.isWeb(userContext) && appVersion >= NC_PERSONAL_COACH_SUPPORT) {
            return true
        }
        return false
    }

    public static async isCultOfflineEnabled(userContext: UserContext) {
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        return true
    }

    public static async isLivePTSGTNoShowEnabled(userContext: UserContext) {
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        const { APP_ENV } = process.env
        if (APP_ENV === "STAGE") {
            return true
        }
        const user = await userContext.userPromise
        const { appVersion, cpVersion, osName } = userContext.sessionInfo
        if (appVersion >= LIVE_SGT_NO_SHOW_SUPPORTED_ANDROID) {
            return true
        }
        if (osName.toLowerCase() === "ios") {
            return user.isInternalUser ? cpVersion >= LIVE_SGT_NO_SHOW_SUPPORTED_IOS_CP_INTERNAL : cpVersion >= LIVE_SGT_NO_SHOW_SUPPORTED_IOS_CP_EXTERNAL
        }
        return false
    }

    public static isNewTCPostBookingProductPageSupported(userContext: UserContext, product: ConsultationProduct) {
        const { appVersion } = userContext.sessionInfo

        return product.tenant !== "CULTFIT" && (AppUtil.isWeb(userContext) || appVersion >= 8.48)
    }

    public static HorizontalTabWidgetSupported(userContext: UserContext) {
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        const { clientVersion } = userContext.sessionInfo

        return (tenant === Tenant.CUREFIT_APP && clientVersion >= 8.48) ||
            (tenant === Tenant.LIVEFIT_APP && clientVersion >= 1.2) || tenant === Tenant.SUGARFIT_APP || tenant === Tenant.ULTRAFIT_APP
    }

    public static isStoreTabSupported(userContext: UserContext) {
        const { osName, appVersion, cpVersion } = userContext.sessionInfo

        const appVersionSupported = appVersion >= STORE_TAB_APP_SUPPORT_EXTERNAL
            || (appVersion === STORE_TAB_APP_SUPPORT_INTERNAL &&
                ((osName?.toLowerCase() === "ios" && cpVersion === STORE_TAB_CP_IOS) ||
                    (osName?.toLowerCase() === "android" && cpVersion === STORE_TAB_CP_ANDROID)))

        return (appVersionSupported) || AppUtil.isWeb(userContext)
    }

    public static isUnifiedDoctorCardUISupported(userContext: UserContext) {
        return userContext.sessionInfo.appVersion >= 8.49
    }

    public static async isUserPartOfCultHideOffersExperiment(userContext: UserContext, hamletBusiness: HamletBusiness) {
        if (userContext.sessionInfo.appVersion >= 8.50) {
            const experimentId = CULT_PACK_BROWSE_HIDE_OFFERS_EXPERIMENT // Owner "<EMAIL>"
            const doesUserBelongToExperiment = false
            return doesUserBelongToExperiment
        }
        return false
    }

    public static async isUserPartOfCultHideCTAExperiment(userContext: UserContext, hamletBusiness: HamletBusiness) {
        if (userContext.sessionInfo.appVersion >= 8.50) {
            const experimentId = CULT_PACK_BROWSE_HIDE_CTA_EXPERIMENT  // Owner "<EMAIL>"
            const doesUserBelongToExperiment = false
            return doesUserBelongToExperiment
        }
        return false
    }

    public static async isUserPartOfCultHidePriceCutExperiment(userContext: UserContext, hamletBusiness: HamletBusiness) {
        if (userContext.sessionInfo.appVersion >= 8.50) {
            const experimentId = CULT_PACK_BROWSE_HIDE_PRICE_CUT_EXPERIMENT // Owner "<EMAIL>"
            const doesUserBelongToExperiment = false
            return doesUserBelongToExperiment
        }
        return false
    }

    public static isNewDIYSeriesViewSupported(userContext: UserContext): boolean {
        if (!this.isInternationalApp(userContext) && !this.isWeb(userContext) && userContext.sessionInfo.clientVersion >= 8.50) {
            return true
        }
        return false
    }

    public static isNewTCCheckoutUISupported(userContext: UserContext, product: ConsultationProduct) {
        return userContext.sessionInfo.appVersion >= 8.55 && product.tenant !== "CULTFIT"
    }

    public static isDrivenBannerUISupported(userContext: UserContext) {
        return !this.isWeb(userContext) && userContext.sessionInfo.appVersion >= 8.55
    }

    public static async isLiveSGTTwilioSupported(userContext: UserContext, hamletBusiness: HamletBusiness) {
        const appVersion = userContext.sessionInfo.appVersion
        if ((appVersion >= 8.59 || userContext.sessionInfo.userAgent === "DESKTOP" || userContext.sessionInfo.userAgent === "MBROWSER") && (process.env.ENVIRONMENT === "STAGE" || process.env.ENVIRONMENT === "LOCAL")) {
            return true
        }
        if (appVersion >= 8.59 || userContext.sessionInfo.userAgent === "DESKTOP" || userContext.sessionInfo.userAgent === "MBROWSER") {
            return await this.isExperimentEnabled(userContext, hamletBusiness, TWILIO_SGT_EXPERIMENT_PROD)
        }
        return false
    }

    public static async isTwilioEnabled(userContext: UserContext, hamletBusiness: HamletBusiness) {
        if ((process.env.ENVIRONMENT === "STAGE" || process.env.ENVIRONMENT === "LOCAL")) {
            return true
        }
        return await this.isExperimentEnabled(userContext, hamletBusiness, TWILIO_SGT_EXPERIMENT_PROD)
    }


    public static async isGymSofbookingEnabled(userContext: UserContext, hamletBusiness: HamletBusiness) {
        if (userContext.sessionInfo.userAgent === "APP" && (process.env.ENVIRONMENT === "STAGE" || process.env.ENVIRONMENT === "LOCAL")) {
            return true
        }
        const appVersion = userContext.sessionInfo.appVersion
        if (appVersion >= 8.62) {
            return true
        }
        return false
    }

    public static isStrikeThroughPriceSupportedForLHR(userContext: UserContext): boolean {
        return (!this.isInternationalApp(userContext) && !this.isWeb(userContext) && userContext.sessionInfo.clientVersion >= STRIKE_THROUGH_PRICE_SUPPORTED_VERSION)
    }

    public static isExistingPackPurchaseForPatientSupported(userContext: UserContext) {
        return userContext.sessionInfo.appVersion >= 8.55
    }

    public static isPsyMultiPatientSupported(userContext: UserContext) {
        return userContext.sessionInfo.appVersion >= 8.55
    }


    public static isCultSGTUpgradeAppSupported(userContext: UserContext) {
        return userContext.sessionInfo.appVersion >= 8.58
    }

    public static isTCDoctorCardUISupported(userContext: UserContext) {
        return userContext.sessionInfo.appVersion >= 8.58
    }

    public static isDoctorConsentFlowSupported(userContext: UserContext) {
        return false
        // return userContext.sessionInfo.appVersion >= 8.61
    }

    public static async isLivePTChatSupported(userContext: UserContext, hamletBusiness: HamletBusiness) {
        if ((process.env.ENVIRONMENT === "STAGE" || process.env.ENVIRONMENT === "LOCAL")) {
            return true
        }
        const appVersion = userContext.sessionInfo.appVersion
        if (appVersion >= 8.62 && userContext.sessionInfo.userAgent === "APP") {
            const experimentId = PT_CHAT_EXPERIMENT_PROD // owner "<EMAIL>"
            return false
        }
    }

    public static hasNewLivePTCheckoutPageChanges(userContext: UserContext, userAgent: UserAgent, isLivePTDoctorType: boolean): boolean {
        return userContext.sessionInfo.appVersion >= 8.62 && userAgent === "APP" && isLivePTDoctorType
    }

    public static isNewKayaConsultImageSupported(userContext: UserContext) {
        return userContext.sessionInfo.appVersion >= 8.63
    }

    public static isNewWeightWiseIconSupported(userContext: UserContext) {
        return userContext.sessionInfo.appVersion >= 8.64 || this.isWeb(userContext)
    }

    public static isProductHelpButtonSupported(userContext: UserContext) {
        const osName = userContext.sessionInfo.osName.toLowerCase()
        const clientVersion = userContext.sessionInfo.clientVersion
        if (osName === "android" || osName === "ios") {
            return clientVersion >= PRODUCT_HELP_BUTTON_SUPPORTED
        }
        return false
    }

    public static isNotificationManagerSupported(userContext: UserContext, isInternalUser: boolean) {
        const osName = userContext.sessionInfo.osName.toLowerCase()
        const clientVersion = userContext.sessionInfo.clientVersion
        if (osName === "android" || osName === "ios") {
            return clientVersion >= NOTIFICATION_MANAGER_SUPPORTED
        }
        return false
    }

    public static isNewOnboardingFlowSupported(req: express.Request) {
        const clientVersion = Number(req.header("clientversion"))
        return clientVersion >= ONBOARDING_FLOW_SUPPORTED
    }

    static TwilioAppUpdateAction(osName: string): Action {
        return {
            actionType: "SHOW_ALERT_MODAL",
            title: "JOIN NOW",
            meta: {
                title: "App Update Required",
                subTitle: "Please update your app to join the online group class.",
                actions: [{ actionType: "EXTERNAL_DEEP_LINK", title: "Update", url: osName === "ios" ? "https://itunes.apple.com/in/app/cure-fit-health-and-wellness/id1217794588?mt=8" : "https://play.google.com/store/apps/details?id=fit.cure.android" }]
            }
        }
    }

    static isProdLike =
        process.env.ENVIRONMENT === "PRODUCTION" ||
        process.env.ENVIRONMENT === "ALPHA"

    static isAlphaEnv = process.env.ENVIRONMENT === "ALPHA"

    static getRedirectionUrlForStoreWebview() {
        return AppUtil.isAlphaEnv ? "https://alpha-cult-fit.cultsport.com"
            : "https://cult-fit.cultsport.com"
    }

    static isWellnessTabSupported(userContext: UserContext) {
        // userContext SHOULD have hamletExperimentMap already resolved
        const { appVersion, osName, cpVersion } = userContext?.sessionInfo
        const isWeb = AppUtil.isWeb(userContext)
        const isIndiaApp = AppUtil.getCountryId(userContext) === "IN"
        const experimentId = this.isProdLike ? WELLNESS_TAB_HAMLET_EXP_ID_PROD : WELLNESS_TAB_HAMLET_EXP_ID_STAGE

        let result = !isWeb
        try {
            const appVersionSupported = appVersion >= WELLNESS_TAB_APP_SUPPORT_EXTERNAL ||
                (appVersion === WELLNESS_TAB_APP_SUPPORT_INTERNAL && ((osName?.toLowerCase() === "ios" && cpVersion >= WELLNESS_TAB_CP_IOS) ||
                    osName?.toLowerCase() === "android" && cpVersion >= WELLNESS_TAB_CP_ANDROID))
            result = isIndiaApp && appVersionSupported
            // 1. App check
            // 2. India app
            // 3. Hamlet support
        } catch (e) {
            kernel.get<ILogger>(BASE_TYPES.ILogger).info("wellness tab not suppoorted for user " + userContext?.userProfile?.userId)
            result = false
        }

        return result

    }

    public static async isNewSGTBookingEnabled(userContext: UserContext, hamletBusiness: HamletBusiness) {
        const { appVersion, osName, cpVersion } = userContext?.sessionInfo
        if ((osName.toLowerCase() === "ios" || osName.toLowerCase() === "android") && appVersion >= 8.84) {
            if ((process.env.ENVIRONMENT === "STAGE" || process.env.ENVIRONMENT === "LOCAL")) {
                return true
            }
            return await this.isExperimentEnabled(userContext, hamletBusiness, NEW_SGT_BOOKING_PROD)
        }
        return false
    }

    static isDevOptionsPageSupported(userContext: UserContext) {
        const osName = userContext.sessionInfo.osName.toLowerCase()
        const clientVersion = userContext.sessionInfo.clientVersion
        if (osName === "android" || osName === "ios") {
            return clientVersion >= DEV_OPTIONS_PAGE_SUPPORTED
        }
        return false
    }

    static isUserformFeedBackSupported(userContext: UserContext) {
        return userContext?.sessionInfo?.appVersion >= 8.84
    }

    static isNCV3Supported(userContext: UserContext) {
        return (userContext.sessionInfo.appVersion >= NUTRITIONIST_PDP_SUPPORTED_VERSION)
    }

    static async isFoodMarketplaceSupported(userContext: UserContext, hamletService: HamletBusiness): Promise<boolean> {
        const { appVersion } = userContext.sessionInfo
        return appVersion >= FOOD_MARKETPLACE_SUPPORT_APP_VERSION
    }

    static isFoodMarketplaceInAppTrackingSupported(userContext: UserContext) {
        return userContext?.sessionInfo?.appVersion >= FOOD_MARKETPLACE_IN_APP_ORDER_TRACKING_SUPPORT
    }

    static isNewFMMealCardSupported(userContext: UserContext) {
        return userContext?.sessionInfo?.appVersion >= FOOD_MARKETPLACE_NEW_MEAL_CARD_SUPPORT
    }

    static async isTransformWaitlistSupported(userContext: UserContext) {
        const { appVersion } = userContext.sessionInfo
        const appSupport = appVersion >= TRANSFORM_WAITLIST_SUPPORT
        return appSupport
    }

    static isWhatsappCommunicationForLiveClassSupported(userContext: UserContext) {
        if (this.isInternationalApp(userContext)) {
            return false
        }
        if (this.isTVApp(userContext)) {
            return false
        }
        if (this.isWeb(userContext)) {
            return false
        }
        if (userContext.sessionInfo.clientVersion >= 8.94) {
            return true
        }
        return false
    }

    public static isNewOrderTrackingWidgetSupported(userContext: UserContext): boolean {
        const { appVersion } = userContext.sessionInfo
        const isWeb = AppUtil.isWeb(userContext)

        return !isWeb && appVersion >= NEW_ORDER_TRACKING_WIDGET_SUPPORT
    }

    public static isTranformCartActionSupported(userContext: UserContext): boolean {
        const { appVersion } = userContext.sessionInfo
        const isWeb = AppUtil.isWeb(userContext)
        return !isWeb && appVersion >= TRANSFORM_CART_PAY_NOW_SUPPORTED_VERSION
    }

    public static isFitnessCLPSupported(userContext: UserContext, isInternalUser: boolean) {
        if (this.isInternationalApp(userContext)) {
            return false
        }
        const osName = userContext.sessionInfo.osName?.toLowerCase()
        const clientVersion = userContext.sessionInfo.clientVersion
        if ((osName === "android" || osName === "ios") && clientVersion >= FITNESS_TAB_SUPPORTED) {
            return true
        }
        return false
    }

    public static async isAtHomeCLPSupported(userContext: UserContext, hamletBusiness: HamletBusiness) {

        if (AppUtil.isWeb(userContext) && !AppUtil.isMWeb(userContext)) {
            const isExpEnabled = await this.isExperimentEnabled(userContext, hamletBusiness, ATHOME_CLP_WEB_EXP_PROD, "2")
            if (isExpEnabled) {
                return true
            }
        }
        return false
    }

    public static isNewWaitListProbabilitySypported(userContext: UserContext) {
        if (userContext.sessionInfo.appVersion >= NEW_WAITLIST_PROBABILITY_VERSION) {
            return true
        }
        return false
    }

    public static async isAppCLPv2Supported(userContext: UserContext, segmentationClient: ISegmentService) {
        const segmentId = "4ae6d6c1-59dd-4205-8ab2-98f4fd621260"
        if (userContext.sessionInfo.appVersion >= CLP_V2_VERSION) {
            return AppUtil.isProdLike ? !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext)) : true
        }
        return false
    }

    public static async isCreditBasedMembershipCheck(userContext: UserContext, segmentationClient: ISegmentService) {
        const segmentId = "6b304bad-0509-441b-890c-16db52d6b2c2"
        if (userContext.sessionInfo.appVersion >= CLP_V2_VERSION || this.isWeb(userContext)) {
            return AppUtil.isProdLike ? !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext)) : true
        }
        return false
    }

    public static isWhatsappCommunicationForNudgesSupported(userContext: UserContext) {
        if (this.isSugarFitOrUltraFitApp(userContext)) {
            return false
        }
        if (this.isInternationalApp(userContext)) {
            return false
        }
        if (this.isTVApp(userContext)) {
            return false
        }
        if (this.isWeb(userContext)) {
            return false
        }
        if (userContext.sessionInfo.clientVersion >= 9.14) {
            return true
        }
        return false
    }

    public static async isFollowupScoreEnbled(userContext: UserContext, hamletBusiness: HamletBusiness): Promise<boolean> {
        if ((process.env.ENVIRONMENT === "STAGE" || process.env.ENVIRONMENT === "LOCAL")) {
            return await this.isExperimentEnabled(userContext, hamletBusiness, THERAPIST_EXPERT_LOGIC_EXP_ID_STAGE, "2")
        }
        return await this.isExperimentEnabled(userContext, hamletBusiness, THERAPIST_EXPERT_LOGIC_EXP_ID_PROD, "2")
    }

    public static isTrialCardWidgetSupported(userContext: UserContext) {
        if (this.isInternationalApp(userContext) || this.isTVApp(userContext) || this.isWeb(userContext)) {
            return false
        }
        if (userContext.sessionInfo.clientVersion >= 9.17) {
            return true
        }
        return false
    }

    public static isFromFlutterAppFlow(userContext: UserContext): boolean {
        return userContext.sessionInfo?.appSource === "flutter"
    }


    public static getFlutterAppSource(): AppSourceType {
        return "flutter"
    }

    public static isMindTherapyAuroraThemeSupported(userContext: UserContext): boolean {
        return userContext.sessionInfo.appVersion >= MIND_THERAPY_AURORA_THEME_GUEST_USER_SUPPORTED
    }

    public static isConsultationFloatingButtonEnabled(userContext: UserContext): boolean {
        return userContext.sessionInfo.appVersion >= CONSULTATION_FLOATING_JOIN_BUTTON_ADDED
    }

    public static async checkIfUserPartOfMindTherapyAuroraExperiment(userContext: UserContext, hamletBusiness: HamletBusiness) {
        if (!this.isMindTherapyAuroraThemeSupported(userContext)) {
            return false
        }

        if (userContext?.sessionInfo?.appVersion === 9.30) {
            return false
        }

        if ((process.env.ENVIRONMENT === "STAGE" || process.env.ENVIRONMENT === "LOCAL")) {
            return true
        }
        return await this.isExperimentEnabled(userContext, hamletBusiness, "817", "1")
    }

    public static isInteractiveFilterSupported(userContext: UserContext): boolean {
        const { appVersion } = userContext.sessionInfo
        const isWeb = AppUtil.isWeb(userContext)
        return isWeb || appVersion >= 9.17
    }

    public static isHorizontalInfoCardSupported(userContext: UserContext): boolean {
        const clientVersion = userContext.sessionInfo.clientVersion
        return clientVersion >= 9.17
    }

    public static isCultBikeOrderSupported(userContext: UserContext): boolean {
        const clientVersion = userContext.sessionInfo.clientVersion
        return clientVersion >= 9.21
    }

    public static cultBikeServiceabilitySegment(): string {
        if (AppUtil.isProdLike) {
            return "4253f9ec-d1da-4836-aa69-52f811dc9918"
        }
        return "cc75c456-72e8-4303-89d6-15e19dfedde8"
    }

    public static getLoginModalAction(): Action {
        return {
            actionType: "SHOW_LOGIN_MODAL",
            url: "curefit://loginmodal",
        }
    }

    public static isNewBadgeWidgetSupported(userContext: UserContext) {
        const { appVersion } = userContext.sessionInfo
        if (AppUtil.isWeb(userContext) || AppUtil.isInternationalApp(userContext)) {
            return false
        }
        return appVersion >= 9.21
    }

    public static isInstructionsWithMediaSupported(userContext: UserContext): boolean {
        const appVersion = userContext.sessionInfo.appVersion
        return appVersion >= INSTRUCTIONS_SUPPORT_SUPPORTED_APP_VERSION
    }

    public static async doesUserBelongToNewHomeAuroraCLP(segmentationClient: ISegmentService, userContext: UserContext): Promise<boolean> {
        const segmentId = AppUtil.isProdLike ? "4844702f-24bf-4c85-bc86-6bf69c4a5341" : "edffca60-d8ba-4fc9-a127-1d22db40749b"
        return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext))
    }

    public static async shouldEnableAppLanguageForSugarfit(segmentationCacheClient: ISegmentationCacheClient, userContext: UserContext): Promise<boolean> {
        if (AppUtil.isSugarFitOrUltraFitApp(userContext)) {
            const userSegments = await segmentationCacheClient.getUserSegments(userContext.userProfile.userId)
            return !_.isEmpty(userSegments) && _.includes(userSegments, "app-localisation-supported-users")
        }
        return false
    }

    public static isEnterpriseCLPSupported(userContext: UserContext) {
        const osName = userContext.sessionInfo.osName
        const appVersion = userContext.sessionInfo.appVersion
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        if (osName.toLowerCase() === "ios" || osName.toLowerCase() === "android") {
            return appVersion >= ENTERPRISE_CORP_CLP_SUPPORT
        }
        return false
    }
    static async isTransformCancelAnytimeWidgetSupported(userContext: UserContext, hamletService: HamletBusiness) {
        let showWidget = true
        try {
            const userExperiments = await hamletService.getUserAllocations(this.getHamletContext(userContext, [TRANSFORM_CANCEL_ANYTIME_WIDGET_HAMLET_EXP_PROD_ID]))
            showWidget = !_.isNil(userExperiments?.assignmentsMap[TRANSFORM_CANCEL_ANYTIME_WIDGET_HAMLET_EXP_PROD_ID]?.bucket?.bucketId) &&  userExperiments?.assignmentsMap[TRANSFORM_CANCEL_ANYTIME_WIDGET_HAMLET_EXP_PROD_ID]?.bucket?.bucketId === "1"
        } catch (e) {
            showWidget = true
        }
        return showWidget
    }

    public static isTherapyFlutterDoctorProfilePriceFixNotSupported(userContext: UserContext) {
        return this.isFromFlutterAppFlow(userContext) && userContext.sessionInfo.appVersion <= 9.25
    }

    public static isBadgeWallWidgetSupported(userContext: UserContext): boolean {
        const { appVersion } = userContext.sessionInfo
        if (AppUtil.isWeb(userContext) || AppUtil.isInternationalApp(userContext)) {
            return false
        }
        return appVersion >= BADGE_WALL_WIDGET_SUPPORTED_APP_VERSION
    }

    public static isTherapyRescheduleFlutterFixSupported(userContext: UserContext) {
        return this.isFromFlutterAppFlow(userContext) && userContext.sessionInfo.appVersion >= 9.32
    }


    public static isRealLiveSessionSupported(userContext: UserContext) {
        if (AppUtil.getTenantFromUserContext(userContext) === Tenant.CUREFIT_APP) {
            return !AppUtil.isTVApp(userContext) && AppUtil.isMinAppVersion(REAL_LIVE_SESSION_SUPPORTED_APP_VERSION, userContext)
        }
    }

    public static isSugarfitPackNewImagesSupported(userContext: UserContext) {
        if (
          AppUtil.getTenantFromUserContext(userContext) ===
            Tenant.SUGARFIT_APP ||
          AppUtil.getTenantFromUserContext(userContext) === Tenant.ULTRAFIT_APP
        ) {
          return AppUtil.isMinAppVersion(
            SF_PACK_NEW_IMAGES_SUPPORTED_APP_VERSION,
            userContext
          )
        }
        return false
    }

    public static isManageDevicesSupported(userContext: UserContext) {
        const osName: string = userContext.sessionInfo.osName
        const appVersion = userContext.sessionInfo.appVersion
        return (osName.toLowerCase() === "android" && appVersion >= 9.92)
            || (osName.toLowerCase() === "ios" && appVersion >= 9.52)
    }
    public static isAccountDeletionSupported(userContext: UserContext) {
        const osName: string = userContext.sessionInfo.osName
        return (osName.toLowerCase() !== "android")
    }
    public static async isGymPTSessionBookingSupported(segmentationClient: ISegmentService, userContext: UserContext) {
        if (process.env.ENVIRONMENT === "LOCAL" || process.env.ENVIRONMENT === "STAGE") {
            return true
        }
        const appVersion = userContext.sessionInfo.appVersion
        if (appVersion < GYMPT_BOOK_SESSION_SUPPORTED) {
            return false
        }
        const segmentId = AppUtil.isProdLike ? "568fb467-2d14-4290-9952-0de5e9473ba7" : "00cf8a42-f236-425f-aa5a-0377901c31fc"
        return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext))
    }

    public static async isPlaySupported(segmentationClient: ISegmentService, userContext: UserContext) {
        if (process.env.ENVIRONMENT === "LOCAL" || process.env.ENVIRONMENT === "STAGE") {
            return true
        }
        if (AppUtil.isWeb(userContext)) {
            return false
        }

        const segmentId = AppUtil.isProdLike ? CULTPASS_PLAY_USERS_PROD : CULTPASS_PLAY_USERS_STAGE
        return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext))
    }

    public static async isNewSupportInFlutterSupported(segmentationClient: ISegmentService, userContext: UserContext) {
        const {appVersion} = userContext.sessionInfo
        if (AppUtil.isWeb(userContext) || AppUtil.isInternationalApp(userContext)) {
            return false
        }

        const segmentId = AppUtil.isProdLike ? "fabb0f5e-7b3c-492f-8a8c-9c3c477da3fd" : "dfcf0bb1-3f9b-4783-b077-8172d8b865e7"
        return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext))
    }

    public static isNewCoachPacePreferenceSupported(userContext: UserContext) {
        return userContext.sessionInfo.appVersion >= 9.45
    }

    public static isTransformMultiSelectQuestionSupported(userContext: UserContext) {
        return userContext.sessionInfo.appVersion >= 9.77
    }

    public static async doesUserBelongToInGuidanceExperiment(userContext: UserContext, hamletService: HamletBusiness) {
        const expId = process.env.ENVIRONMENT === "STAGE" ? "529" : "1070"
        const hamletExperimentMap = await hamletService.getUserAllocations(AppUtil.getHamletContext(userContext, [expId]))
        const userAssignment = hamletExperimentMap ? hamletExperimentMap.assignmentsMap[expId] : undefined
        return userAssignment ? userAssignment.bucket.bucketId === "2" : false
    }

    public static async doesUserBelongsToUserFormFeedbackABExperiment(userContext: UserContext, hamletService: HamletBusiness, logger: ILogger, rollbarService: RollbarService) {
        try {
            const orderSource = userContext.sessionInfo.orderSource
            const tenant = AppUtil.getTenantFromUserContext(userContext)
            const userId = userContext.userProfile.userId

            const orderSourcesForExperiment: OrderSource[] = FEEDBACK_USER_FORM_AB_TEST_ORDER_SOURCES
            const hamletConfigQuery: ConfigQuery<boolean> = FEEDBACK_USER_FORM_AB_TEST_CONFIG_QUERY
            const env = process.env.ENVIRONMENT

            logger.info(`isUserPartOfFeedbackUserFormExperiment, userId:${userId}, tenant:${tenant}, orderSource:${orderSource}, env:${env}`)

            if (!orderSourcesForExperiment.includes(orderSource)) {
                logger.info(`isUserPartOfFeedbackUserFormExperiment, user not in orderSource, userId:${userId}, orderSource:${JSON.stringify(orderSourcesForExperiment)}, orderSource:${orderSource}, env:${env}`)
                return false
            }

            if (env === "STAGE" || env === "LOCAL") {
                const enabledUserIds = ["3958053", "4077777"]
                const res = enabledUserIds.includes(userId)
                logger.info(`isUserPartOfFeedbackUserFormExperiment:: in stage/local check, userId:${userId}, tenant:${tenant}, orderSource:${orderSource}, env:${env}, res:${res}`)
                return res
            }
            const isUserPartOfExperiment = await hamletService.getConfig({
                context: {
                    userId,
                    deviceId: undefined,
                    tenant
                },
                query: hamletConfigQuery
            })
            return isUserPartOfExperiment
        } catch (e) {
            rollbarService.sendError(e)
            logger.error(`isUserPartOfFeedbackUserFormExperiment error`, e)
            return false
        }
    }

    public static async hasUserEnteredInGuidanceExperiment(userContext: UserContext, diyService: IDIYFulfilmentService) {
        const userId = userContext.userProfile.userId
        const userAttribute: DIYUserAttribute = await diyService.getUserAttribute(userId, "ONB_RECOMMENDED_PACK_ATTRIBUTE", AppUtil.getTenantFromUserContext(userContext))
        if (userAttribute !== null) {
            return true
        }
        return false
    }

    public static async hasUserExitedInGuidanceExperiment(userContext: UserContext, diyService: IDIYFulfilmentService) {
        const userId = userContext.userProfile.userId
        const userAttribute: DIYUserAttribute = await diyService.getUserAttribute(userId, "ONB_RECOMMENDED_PACK_ATTRIBUTE", AppUtil.getTenantFromUserContext(userContext))
        if (userAttribute?.attributeState === DIYUserAttributeState.INACTIVE) {
            return true
        }
        return false
    }

    public static async doesUserBelongToCoachExperiment(userContext: UserContext, hamletService: HamletBusiness) {
        const expId = process.env.ENVIRONMENT === "STAGE" ? "534" : "1194"
        const hamletExperimentMap = await hamletService.getUserAllocations(AppUtil.getHamletContext(userContext, [expId]))
        const userAssignment = hamletExperimentMap ? hamletExperimentMap.assignmentsMap[expId] : undefined
        return userAssignment ? userAssignment.bucket.bucketId === "2" : false
    }

    public static async doesUserBelongToCoachV2Experiment(userContext: UserContext, hamletService: HamletBusiness) {
        const expId = process.env.ENVIRONMENT === "STAGE" ? "534" : "1207"
        const hamletExperimentMap = await hamletService.getUserAllocations(AppUtil.getHamletContext(userContext, [expId]))
        const userAssignment = hamletExperimentMap ? hamletExperimentMap.assignmentsMap[expId] : undefined
        return userAssignment ? userAssignment.bucket.bucketId === "2" : false
    }

    public static async doesUserBelongToCoachTrialExperiment(userContext: UserContext, hamletService: HamletBusiness) {
        const expId = process.env.ENVIRONMENT === "STAGE" ? "534" : "1210"
        const hamletExperimentMap = await hamletService.getUserAllocations(AppUtil.getHamletContext(userContext, [expId]))
        const userAssignment = hamletExperimentMap ? hamletExperimentMap.assignmentsMap[expId] : undefined
        return userAssignment ? userAssignment.bucket.bucketId === "2" : false
    }

    public static async showUserCoachClp(userContext: UserContext, segmentationClient: ISegmentService) {
        const segmentId = AppUtil.isProdLike ? "520dbc2c-773a-468f-a098-8522bc5d59bf" : "ea3b156c-e4f2-4122-9653-f2b6ac4c32df"
        return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext))
    }

    public static isHybridCentersSupported(userContext: UserContext) {
        const {appVersion} = userContext.sessionInfo
        if (AppUtil.isWeb(userContext) || AppUtil.isInternationalApp(userContext)) {
            return false
        }
        return appVersion >= HYBRID_CENTERS_SUPPORTED_VERSION
    }

    public static isFlutterAllCentersPageEnabled(userContext: UserContext) {
        if (userContext.sessionInfo.clientVersion < AURORA_ALL_CENTERS_PAGE_SUPPORTED) { return false }
        return true
    }

    public static async doesUserBelongToSmallWinsExperiment(userContext: UserContext, hamletService: HamletBusiness) {
        const expId = process.env.ENVIRONMENT === "STAGE" ? "549" : "1226"
        const hamletExperimentMap = await hamletService.getUserAllocations(AppUtil.getHamletContext(userContext, [expId]))
        const userAssignment = hamletExperimentMap ? hamletExperimentMap.assignmentsMap[expId] : undefined
        return userAssignment ? userAssignment.bucket.bucketId === "2" : false
    }

    public static async isCommunityEnabled(userContext: UserContext, segmentationClient: ISegmentService) {
        if (!AppUtil.isProdLike) {
            return true
        }
        const segmentId = COMMUNITY_PROD_SEGMENT
        return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext))
    }

    public static async isCultHabitsEnabled(userContext: UserContext, segmentationClient: ISegmentService) {
        const segmentId = CULT_HABIT_REMINDER_SEGMENT
        return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext)) && userContext.sessionInfo.appVersion >= 10.05
    }

    public static async isUserPartOfCoachSegment(userContext: UserContext, diyService: IDIYFulfilmentService) {
        const userId = userContext.userProfile.userId
        const diyUserAttribute = await diyService.getUserAttribute(userId, "HOME_GUIDANCE_ATTRIBUTE", AppUtil.getTenantFromUserContext(userContext))
        if (diyUserAttribute?.attributeState === DIYUserAttributeState.ACTIVE) {
            return true
        }
        return false
    }

    // // TODO: This is a hack fix used to navigate the users to the correct tab in fitnessclp.. tb removed when fix is deployed
    // public static async isTrialExperimentHackEnabled(userContext: UserContext, segmentationClient: ISegmentService) {
    //     if (!AppUtil.isProdLike) {
    //         return false
    //     }
    //     const segmentId = "67d5724e-ca90-4ea0-850b-5b3943c73017"
    //     return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext))
    // }

    public static async doesUserSupportSuccessModalV2(userContext: UserContext) {
        return userContext.sessionInfo.appVersion >= 9.61
    }

    public static isWeeklyGoalPageSupported(userContext: UserContext) {
        const {appVersion} = userContext.sessionInfo
        if (AppUtil.isWeb(userContext) || AppUtil.isInternationalApp(userContext)) {
            return false
        }
        return appVersion >= WEEKLY_GOAL_PAGE_SUPPORTED_VERSION
    }

    public static cultGear65KCODEnabledSegment(): string {
        return "a135890f-74b9-4fe2-b1bd-cc19112c57aa"
    }

    public static getCyclesProductIdsForExp() {
        return [
            "3604", "3605", "3646", "3647", "3648", "3649", "3451", "3452", "3453",
            "3454", "3455", "3804", "3805", "3806", "3807", "3808", "3809", "4054",
            "4055", "4400", "4401", "4406", "4407", "4417", "4418", "4419"
        ]
    }

    public static getTrainerKitProductIdsForExp() {
        return ["4775", "4776", "4777", "4660", "4387", "4378", "4778", "4779", "5002", "5003", "5004"]
    }

    public static doesUserHasTrainerKitProductIdInCart(productIds: string[] = []) {
        return !_.isEmpty(_.intersection(productIds, this.getTrainerKitProductIdsForExp()))
    }


    public static async doesUserBelongToCyclesCODV2EnabledSegment(productIds: string[] = [], userContext: UserContext, interfaces: CFServiceInterfaces) {
        if (!AppUtil.isProdLike) {
            return false
        }
        const segmentId = "1f83c612-6ac0-464d-b59a-c1729c7b35f9"
        return !_.isEmpty(_.intersection(productIds, this.getCyclesProductIdsForExp()))  && !_.isEmpty(await interfaces.segmentService.doesUserBelongToSegment(segmentId, userContext))
    }

    public static async doesUserBelongToCoachTrialExpiredSegment(userContext: UserContext, interfaces: CFServiceInterfaces) {
        if (!AppUtil.isProdLike) {
            return false
        }
        const segmentId = "fbb6e618-4ae2-497b-b1fe-c3ddb6c024e1"
        return !_.isEmpty(await interfaces.segmentService.doesUserBelongToSegment(segmentId, userContext))
    }

    public static async doesUserBelongToFlutterAccountViewSegment(segmentationClient: ISegmentService, userContext: UserContext) {
        if (AppUtil.isWeb(userContext) || AppUtil.isInternationalApp(userContext)) {
            return false
        }

        const segmentId = AppUtil.isProdLike ? "876b439c-2b4f-4a63-9147-74c82234c102" : "6b165ceb-5c66-4d7d-80c9-dad51ce20b25"
        return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext))
    }

    public static async doesUserBelongToEnterpriseUserSegment(segmentationClient: ISegmentService, userContext: UserContext) {
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        const segmentId = AppUtil.isProdLike ? "90992b49-89cb-48cf-8672-102a240199ee" : "1e8083de-562d-43f0-8672-2a2d7ef964c6"
        return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext))
    }

    public static async doesUserBelongToOptumCorpSegment(segmentationClient: ISegmentService, userContext: UserContext) {
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        const segmentId = AppUtil.isProdLike ? "8df82cbf-862c-44fb-b9fd-c65bbd06ba07" : "1e8083de-562d-43f0-8672-2a2d7ef964c6"
        return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext))
    }

    public static isEnterpriseCultPassCorpCardSupported(userContext: UserContext) {
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        return userContext.sessionInfo.appVersion >= 9.75
    }

    public static async doesUserBelongToTransformActiveMembershipSegment(userContext: UserContext, segmentationClient: ISegmentService) {
        const segmentId = AppUtil.isProdLike ? "41c3f775-ad75-48fb-bb9c-4c534ebbb771" : "1d7bc7f9-3abf-4e34-bd6e-3dc2f62497a0"
        return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext))
    }

    public static async getBucketIdForMandatoryOnboardingExp(userContext: UserContext, hamletService: HamletBusiness): Promise<any> {
        return null
        // const expId = process.env.ENVIRONMENT === "STAGE" ? MANDATORY_ONBOARDING_EXP_ID_STAGE : MANDATORY_ONBOARDING_EXP_ID_PROD
        // const hamletExperimentMap = await hamletService.getUserAllocations(AppUtil.getHamletContext(userContext, [expId]))
        // const userAssignment = hamletExperimentMap ? hamletExperimentMap.assignmentsMap[expId] : undefined
        // return userAssignment?.bucket.bucketId
    }

    public static async getBucketIdForGymProgressionExp(userId: string, hamletService: HamletBusiness, deviceId?: string, tenant?: Tenant): Promise<string> {
        // return null
        if ( process.env.ENVIRONMENT === "STAGE") {
            return "2"
        }
        const expId = GYM_PROGRESSION_EXP_ID_PROD
        const hamletContext: HamletContext = {
            userId: userId,
            tenant: tenant,
            deviceId: deviceId,
            experimentIds: [expId]
        }
        const hamletExperimentMap = await hamletService.getUserAllocations(hamletContext)
        const userAssignment = hamletExperimentMap ? hamletExperimentMap.assignmentsMap[expId] : undefined
        return userAssignment?.bucket.bucketId
    }

    public static async getBucketIdForFeedbackExp(userId: string, hamletService: HamletBusiness, deviceId?: string, tenant?: Tenant): Promise<string> {
        // return null
        if ( process.env.ENVIRONMENT === "STAGE") {
            return "2"
        }
        const expId = FEEDBACK_REVAMP_EXPERIMENT_ID_PROD
        const hamletContext: HamletContext = {
            userId: userId,
            tenant: tenant,
            deviceId: deviceId,
            experimentIds: [expId]
        }
        const hamletExperimentMap = await hamletService.getUserAllocations(hamletContext)
        const userAssignment = hamletExperimentMap ? hamletExperimentMap.assignmentsMap[expId] : undefined
        return userAssignment?.bucket.bucketId
    }

    public static isMandatoryOnboardingExpEnabled(userContext: UserContext, bucketId: string) {
        const isAppOnly = !AppUtil.isWeb(userContext) && !AppUtil.isTVApp(userContext) && !AppUtil.isInternationalApp(userContext)
        const versionCheck = userContext.sessionInfo.appVersion >= 9.67
        return isAppOnly && versionCheck
    }

    public static async doesUserBelongToGSTsplitExperiment(userContext: UserContext, interfaces: CFServiceInterfaces) {
        try {
            if (!AppUtil.isProdLike) {
                return true
            }
            const segmentIds = ["b2be7269-deb7-47f3-91ed-4f32843b5ae5", "41a8c3a4-f62f-4277-9622-c3052e907a25", "c7c3dbb4-71ac-4a9b-8964-fdee40ed5bfa"]
            return !_.isEmpty(await interfaces.segmentService.doesUserBelongToAnySegment(segmentIds, userContext))
        } catch (error) {
            return false
        }
    }

    public static getCSSeoSchema(product: CGCatalogueProduct, productFeedback?: ProductFeedback) {
        const productSpecs: any = (product as any)?.products_specification
        return {
            "@context": "https://schema.org",
            "@type": "Product",
            "name": product.name,
            "image": "https://cdn-images.cure.fit/www-curefit-com/image/upload/fl_progressive,f_auto,q_auto:eco,w_500,ar_3:4,c_fill/dpr_2/" + (product?.images?.[0]?.product_url || product?.master?.images?.[0]?.product_url),
            "sku": product.id?.toString(),
            "description": product.description,
            "brand": {
              "@type": "Brand",
              "name": product.brand
            },
            "offers": {
                "@type": "Offer",
                "priceCurrency": "INR",
                "availability": product.total_on_hand ? "InStock" : "OutOfStock",
                "price": product?.price?.toString(),
                "URL": `${CULTSPORT_BASE_URL[process.env.APP_ENV] || CULTSPORT_BASE_URL["PRODUCTION"]}/${product.slug}/product/${product.id}`
            },
            "AggregateRating": productFeedback && productFeedback?.count > 0 ? {
              "@type": "AggregateRating",
              "itemReviewed": product.name,
              "ratingCount": productFeedback.count,
              "ratingValue": productFeedback.rating
            } : undefined,
            "additionalProperty": _.isEmpty(productSpecs) ? undefined : Object.keys(productSpecs).map(key => {
                return {
                    "@type": "PropertyValue",
                    "name": key,
                    "value": productSpecs[key]
                  }
            })
          }
    }

    public static async doesUserBelongToTransformBootcampSegment(userContext: UserContext, segmentationClient: ISegmentService) {
        const segmentId = AppUtil.isProdLike ? "b522ff8e-7c36-401f-b5eb-ede66c592d76" : "8477f27a-200c-49ca-b5d1-7ab671e546d5"
        return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext))
    }

    public static getInstallationCancellationInfoText(installation_text: string, non_cancelable_text: string) {
        return `${installation_text ? installation_text + (non_cancelable_text ? ". " : "") : ""}${non_cancelable_text ? non_cancelable_text : ""}`
    }

    public static isOpenForEnquiryWidgetSupported(userContext: UserContext): boolean {
        const { appVersion } = userContext?.sessionInfo
        return appVersion >= 9.80
    }

    public static enquiryBugCheck(userContext: UserContext): boolean {
        const { appVersion } = userContext?.sessionInfo
        return appVersion >= 9.98
    }

    // public static async isOnlyInternalGrowthClp(userContext: UserContext, segmentationClient: ISegmentService): Promise<boolean> {
    //     const { appVersion } = userContext?.sessionInfo
    //     const segmentId = AppUtil.isProdLike ? "66d49a2d-bfbe-4a45-a4d0-fe3e7362ba26" : "ad2f0b4d-14e4-4e3c-9699-3433d47c366e"
    //     return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext))
    // }

    public static async isCenterLevelPricingSupportedWeb(userContext: UserContext, segmentationClient: ISegmentService): Promise<boolean> {
        const { appVersion } = userContext?.sessionInfo
        const segmentId = AppUtil.isProdLike ? "35aa9cde-6ebe-4f4e-b7e5-786a928f5bea" : "ad2f0b4d-14e4-4e3c-9699-3433d47c366e"

        // const isOnlyInternalGrowthClp = await this.isOnlyInternalGrowthClp(userContext, segmentationClient)

        return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext))
    }

    public static async isCenterLevelPricingV2SupportedWeb(userContext: UserContext, segmentationClient: ISegmentService): Promise<boolean> {
        const segmentId = AppUtil.isProdLike ? "2aac42f1-8758-4206-92e1-3137466fa8d8" : "219b66d6-26a1-4329-bdb3-3bb4972c26af"
        return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext))
    }

    public static async isCenterLevelPricingSupported(userContext: UserContext, segmentationClient: ISegmentService): Promise<boolean> {
        const { appVersion } = userContext?.sessionInfo
        const segmentId = AppUtil.isProdLike ? "31f89f78-c7c8-4fe9-9167-85bffd98d06d" : "52c3d3ae-35d6-4958-9b3c-5d86e0d19788"
        return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext)) && appVersion >= 10.07
    }

    public static async isCenterLevelRenewalEnabled(userContext: UserContext, segmentationClient: ISegmentService): Promise<boolean> {
        const segmentId = AppUtil.isProdLike ? "f4e9c1c0-1c90-44f7-acee-16c4c4f3bc69" : "f4e9c1c0-1c90-44f7-acee-16c4c4f3bc69"
        return (!_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext)) && AppUtil.isCenterLevelPricingSupported(userContext, segmentationClient)) || AppUtil.isCenterLevelRenewalEnabledForCLPV2(userContext, segmentationClient)
    }

    public static async isCenterLevelRenewalEnabledForCLPV2(userContext: UserContext, segmentationClient: ISegmentService): Promise<boolean> {
        const segmentId = AppUtil.isProdLike ? "1ed8b801-bced-46cd-bf14-27d62e1ecdb3" : "1ed8b801-bced-46cd-bf14-27d62e1ecdb3"
        return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext)) && AppUtil.isAppCLPv2Supported(userContext, segmentationClient)
    }

    public static getSugarfitEventTitle(classResponse: LiveClass): string {
        const intensity = classResponse.intensityLevel.charAt(0).toUpperCase() + classResponse.intensityLevel.slice(1).toLowerCase()
        if (classResponse && (classResponse?.tags.includes("WEBINAR") || classResponse?.tags.includes("EVENTS&TALK")))
            return classResponse.trainerName
        else `${classResponse.trainerName} • ${intensity}`
    }
    static isBatchSelectionSupportedForBootcamp(userContext: UserContext) {
        return userContext.sessionInfo.appVersion >= 9.86
    }
    public static getAppTitle(userContext: UserContext): string {
        if (AppUtil.getTenantFromUserContext(userContext) === Tenant.SUGARFIT_APP) return "sugar.fit"
        if (AppUtil.getTenantFromUserContext(userContext) === Tenant.ULTRAFIT_APP) return "ultra.fit"
        return "cult.fit"
    }

    public static async shouldUseNewVoucherFlow(userContext: UserContext, segmentationClient: ISegmentService) {
        if (!AppUtil.isProdLike) {
            return true
        }
        const segmentId = "fec5ab75-1e1e-4b3d-be9e-95190ccf5b3b"
        return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext))
    }


    public static isTransformCombinedClpSupported(userContext: UserContext): boolean {
        const { appVersion } = userContext?.sessionInfo
        return appVersion >= 9.93
    }

    public static isTransformFreePaymentSupported(userContext: UserContext): boolean {
        const { appVersion } = userContext?.sessionInfo
        return appVersion >= 9.96
    }

    public static isTransformAddressSelectionSupported(userContext: UserContext): boolean {
        const { appVersion } = userContext?.sessionInfo
        return appVersion >= 9.98
    }

    public static isTransformStartDateSelectionSupported(userContext: UserContext): boolean {
        const { appVersion } = userContext?.sessionInfo
        return appVersion >= 10.02
    }

    public static isEnterpriseNewVoucherNotSupported(userContext: UserContext): boolean {
        const isAppOnly = !AppUtil.isWeb(userContext) && !AppUtil.isTVApp(userContext) && !AppUtil.isInternationalApp(userContext)
        const versionCheck = userContext.sessionInfo.appVersion < 9.98
        return isAppOnly && versionCheck
    }

    public static isOneDayScheduleSupported(userContext: UserContext): boolean {
        const {appVersion} = userContext.sessionInfo
        return appVersion >= 10.02
    }

    public static isOnePassSupported(userContext: UserContext): boolean {
        const isAppOnly = !AppUtil.isWeb(userContext) && !AppUtil.isTVApp(userContext) && !AppUtil.isInternationalApp(userContext)
        const versionCheck = userContext.sessionInfo.appVersion >= 10.02
        return isAppOnly && versionCheck
    }

    public static async isBootcampBookingExperimentSupported(userContext: UserContext, hamletBusiness: HamletBusiness): Promise<boolean> {
        if ((process.env.ENVIRONMENT === "STAGE" || process.env.ENVIRONMENT === "LOCAL")) {
            return await this.isExperimentEnabled(userContext, hamletBusiness, BOOTCAMP_BOOKING_EXP_ID_STAGE, "2")
        }
        return await this.isExperimentEnabled(userContext, hamletBusiness, BOOTCAMP_BOOKING_EXP_ID_PROD, "2")
    }

    public static async isExtraRatingEnabled(userContext: UserContext, hamletBusiness: HamletBusiness) {
        return false
    }

    public static isBootcampReferralSupported(userContext: UserContext) {
        const appVersion = userContext.sessionInfo.appVersion
        return appVersion >= 10.07
    }

    public static async doesUserBelongsToTrainerAttireExperiment(userContext: UserContext, segmentationClient: ISegmentService) {
        if (AppUtil.isProdLike) {
            const segmentId = "7c5a2b61-01c3-41c4-9df9-374ce181c433"
            return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext))
        }
        return false
    }

    public static async doesUserBelongsToNewPauseExperiment(userContext: UserContext, interfaces: CFServiceInterfaces) {
        const appVersion = userContext.sessionInfo.appVersion
        if (appVersion < 10.29) return false
        if (AppUtil.isProdLike) {
            const newPauseSegmentId = "f2bffaf1-ba9c-4b56-b2ba-5b51b733970b"
            const onePassSegmentId = "c340b84a-d6d3-4845-8ff4-e9ac1a98a866"
            const newPauseEnabled = !_.isEmpty(await interfaces.segmentService.doesUserBelongToSegment(newPauseSegmentId, userContext))
            const onePassSegment = !_.isEmpty(await interfaces.segmentService.doesUserBelongToSegment(onePassSegmentId, userContext))
            const ret = newPauseEnabled && !onePassSegment
            return ret
        }
        return false
    }

    public static async isForcedFeedbackEnabled(userContext: UserContext, hamletBusiness: HamletBusiness) {
        return false
    }

    public static isTransformNewFeedbackSupported(userContext: UserContext) {
        if (AppUtil.isSugarFitOrUltraFitApp(userContext)) {
            return false
        }
        const appVersion = userContext.sessionInfo.appVersion
        return appVersion >= 10.07
    }
    public static isAppSelectCityTransferFLowSupported(userContext: UserContext) {
        const appVersion = userContext.sessionInfo.appVersion
        return appVersion >= 10.29
    }
    public static isAppCityMismatchModalSupported(userContext: UserContext) {
        const appVersion = userContext.sessionInfo.appVersion
        return appVersion >= 10.46
    }
    public static isEnterpriseMembership(membership: Membership) {
        return !_.isNil(membership) && membership.metadata?.source === "ENTERPRISE"
    }

    public static async doesUserPartOfChallengeTab(segmentationClient: ISegmentService, userContext: UserContext): Promise<boolean> {
        if (AppUtil.isProdLike) {
            const segmentId = "ab4f75e3-7d54-4397-8f25-8dc0764b6fe6"
            return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext))
        }
        return true
    }
    public static async doesUserPurchasedAnyMembershipBefore(segmentationClient: ISegmentService, userContext: UserContext): Promise<boolean> {
        if (AppUtil.isProdLike) {
            const segmentId = "eb06b4df-f006-403f-a18a-5a3c46161b9d"
            return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext))
        }
        return true
    }
    public static async isAppNewWaitlistColorCodingSupported(segmentationClient: ISegmentService, userContext: UserContext): Promise<boolean> {
        const appVersion = userContext.sessionInfo.appVersion
        if (AppUtil.isProdLike) {
            const segmentId = "e91e9b60-0151-4046-bb52-855f0d136325"
            return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext)) && appVersion >= 10.52
        }
        return appVersion >= 10.52
    }

    public static async isUserOnboardingFormSupported(segmentationClient: ISegmentService, userContext: UserContext, isSocialLoginFlow: boolean): Promise<boolean> {
        const appVersion = userContext.sessionInfo.appVersion
        if (isSocialLoginFlow) return false
        if (AppUtil.isProdLike) {
            return (!_.isEmpty(await segmentationClient.doesUserBelongToSegment(ONBOARDING_FORM_SEGMENT_ID, userContext)) && (isSocialLoginFlow ? appVersion >= 10.31 : appVersion >= 10.28))
        }
        return false
    }

    public static async doesUserBelongToM1RecommendationSegment(userContext: UserContext, segmentationClient: ISegmentService) {
        const segmentId = AppUtil.isProdLike ? M1_ONBOARDING_SEGMENT_PROD : M1_ONBOARDING_SEGMENT_STAGE
        return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext))
    }

    public static isNewCenterDetailsPageSupported(userContext: UserContext): boolean {
        const { appVersion } = userContext?.sessionInfo
        return appVersion >= 10.29
    }

    public static isAppLuxPauseSupported(userContext: UserContext): boolean {
        const { appVersion } = userContext?.sessionInfo
        return appVersion >= 10.48
    }

    public static isSfPaidDiagnosticSupported(userContext: UserContext): boolean {
        if (AppUtil.isSugarFitApp(userContext))
            return userContext.sessionInfo.appVersion >= 9.38
        return false
    }

    public static async isUserHavingActiveBooking(membershipServiceId: number, userId: string, dateRangeToInUTC: Date, cultFitService: ICultService, rollbarService: RollbarService): Promise<boolean> {
        try {
            const payload = <BookingSearchRequest>{
                userID: parseInt(userId),
                membershipServiceId: membershipServiceId,
                classDateRangeInUTC: {
                    from: TimeUtil.getDateNow("UTC"),
                    to: dateRangeToInUTC
                },
                state: BookingState.BOOKED
            }
            let allBookings = null
                allBookings = await cultFitService.getAllBookings(payload, userId)
            let isUserHavingFutureBookings: boolean = false
            if (!_.isNil(allBookings) && Array.isArray(allBookings) && allBookings?.length > 0) {
                isUserHavingFutureBookings = true
            }
            return isUserHavingFutureBookings
        } catch (e) {
            rollbarService.sendError(e)
            return false
        }
    }

    public static async isAppUserInCheckoutDropdownExperiment(userContext: UserContext, segmentationClient: ISegmentService): Promise<boolean> {
        if (!AppUtil.isProdLike) return false
        const segmentId = "f39693bc-34d6-4bb3-b3d7-5ed0c7d6daab"
        return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext))
    }
    public static isAppDynamicGIFBannerWidgetSupported(userContext: UserContext): boolean {
        const { appVersion } = userContext?.sessionInfo
        return appVersion >= 10.60
    }
    public static async isGymProgressionPageSupported(userId: string, appVersion: number, segmentationCacheClient: ISegmentationCacheClient): Promise<boolean> {
        if (!AppUtil.isProdLike) return true
        if (appVersion < 10.72) return false
        const MEMBER_EXPERIENCE_SEGMENT = "Member Experience Test - NEW"
        const GYM_PROGRESSION_INTERNAL_EMPLOYEES = "Gym progression internal employees testing"
        const CHAMPIONS_SEGMENT = "champions segment"
        const CULT_PASS_ELITE_MEMBERS = "cultpass ELITE Members"
        const CULT_PASS_PRO_MEMBERS = "cultpass PRO Members"

        const userSegments = await segmentationCacheClient.getUserSegments(userId)
        return !_.isEmpty(userSegments)
            && ( _.includes(userSegments, MEMBER_EXPERIENCE_SEGMENT)
                || _.includes(userSegments, GYM_PROGRESSION_INTERNAL_EMPLOYEES)
                || _.includes(userSegments, CHAMPIONS_SEGMENT)
                || _.includes(userSegments, CULT_PASS_ELITE_MEMBERS)
                || _.includes(userSegments, CULT_PASS_PRO_MEMBERS))
    }

    public static getTimeInMilliseconds(date: string) {
        if (date) {
            return moment(new Date(date)).utcOffset("+0530").valueOf()?.toString()
        }
        return "Please enter valid date"
    }

    public static async doesUserBelongsToBoosterPackSelectionPreModal(userContext: UserContext, segmentationClient: ISegmentService) {
        const segmentId = AppUtil.isProdLike ? "f49c3aa5-d4c4-48da-a66d-1413c6162a0b" : "d4a55b62-295b-4632-a972-2d2642c15874"
        const isInternalUser = (await userContext.userPromise).isInternalUser
        return isInternalUser
    }

    public static async doesUserBelongToLimitedGxExperiment(userContext: UserContext, segmentationClient: ISegmentService) {
        if (!AppUtil.isProdLike) return true
        const segmentId = "04169b70-a617-43c8-bcb5-ccf61407618f"
        return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext))
    }

    public static isBoosterPackFlowActive() {
        return false
    }


    public static async isInternationalUserFromLocation(ip: string, maxmindService: IMaxmindService) {
        const cityResponse = await maxmindService.getLocationDetailsByIp(ip)
        return !_.isEqual(cityResponse.country.iso_code, "IN") && !_.isEqual(cityResponse.country.iso_code, "IND")
    }

    public static async blockFitnessContentForInternationalUser(userContext: UserContext, maxmindService: IMaxmindService, logger: ILogger)  {
        const cityResponse = await maxmindService.getLocationDetailsByIp(userContext.sessionInfo.ip)
        const isInternationalUser = !_.isEqual(cityResponse.country.iso_code, "IN") && !_.isEqual(cityResponse.country.iso_code, "IND")

        if (isInternationalUser) {
            logger.info("Blocking diy/live content for userId: " + userContext.userProfile.userId + ", locationCode: " + cityResponse.country.iso_code)
        }
        return isInternationalUser
    }

    public static async hasUsedSWPInLast30Days(userId: string, segmentationCacheClient: ISegmentationCacheClient): Promise<boolean> {
        const AI_TRAINER_ACTIVE_USERS = "AI Trainer D30 Active users"
        const userSegments = await segmentationCacheClient.getUserSegments(userId)
        return !_.isEmpty(userSegments) && _.includes(userSegments, AI_TRAINER_ACTIVE_USERS)
    }
    public static isMembershipDetailV2PageSupported(userContext: UserContext): boolean {
        return !AppUtil.isWeb(userContext)
    }

    static getNewMembershipDetailPage(membershipServiceId: string, isLiveBenefit: boolean): string {
        return "curefit://membership_detail?membershipServiceId=" + membershipServiceId + "&isLiveBenefit=" + isLiveBenefit
    }

    public static isAppSupportedAddonPurchase(userContext: UserContext): boolean {
        const { appVersion } = userContext?.sessionInfo
        return appVersion >= 10.79
    }

    public static async doesUserBelongsToSkuPlusBottomsheetExperiment(userContext: UserContext, segmentationClient: ISegmentService) {
        if (!AppUtil.isProdLike) return false
        const segmentIds = ["1a46d6d5-44f0-47aa-86fb-26f647c11d15", "d7180884-ffb0-4277-88e1-c4933baf1593", "cc1dc212-47e3-4106-b7f0-5d8881864602"]
        return !_.isEmpty(await segmentationClient.doesUserBelongToAnySegment(segmentIds, userContext))
    }

    public static async doesUserBelongsToNewCheckoutPageExperiment(userContext: UserContext, segmentationClient: ISegmentService) {
        if (!AppUtil.isProdLike) return false
        const segmentIds = ["39edab72-51e7-4bac-8e24-45b172c12cb7"]
        return !_.isEmpty(await segmentationClient.doesUserBelongToAnySegment(segmentIds, userContext))
    }

    public static async doesUserBelongsToSkuPlusUpgradeBanner(userContext: UserContext, segmentationClient: ISegmentService) {
        if (!AppUtil.isProdLike) return false
        const segmentIds = ["9545f20a-c67a-4237-a2a2-b1e3c152e6ee", "d7180884-ffb0-4277-88e1-c4933baf1593", "cc1dc212-47e3-4106-b7f0-5d8881864602"]
        return !_.isEmpty(await segmentationClient.doesUserBelongToAnySegment(segmentIds, userContext))
    }

    public static async isUserPartOfCultClassRedirection(userContext: UserContext, segmentationClient: ISegmentService) {
        if (!AppUtil.isProdLike) return true
        const segmentId = "bfd622b2-0113-472f-88e7-78e67b5972d0"
        return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext))
    }
}

export default AppUtil
