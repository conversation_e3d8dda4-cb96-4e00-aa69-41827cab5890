import * as express from "express"
import * as _ from "lodash"
import {
  AccordionSectionItem,
  AccordionSectionList,
  AccountNotLoginWidget,
  HealthMetric,
  HealthMetricWidget,
  MembershipItem,
  MembershipListWidget,
  ProfileNameIconWidget,
  ProfileSummaryWidget,
  ScoreCardWidget,
  SwitchSectionItem
} from "../common/views/ProfileWidgetView"
import { User } from "@curefit/user-common"
import { City } from "@curefit/location-common"
import { CareUtil } from "./CareUtil"
import { ActionUtil } from "./ActionUtil"
import UserView from "../user/UserView"

import { Action, WidgetView } from "../common/views/WidgetView"
import { UserActivityDetails } from "@curefit/quest-common"
import { UserContext } from "@curefit/userinfo-common"
import AppUtil from "./AppUtil"
import { IRewardService } from "@curefit/reward-client"
import CultUtil from "./CultUtil"
import { MealUtil } from "@curefit/base-utils"
import { IvrCallTime, PreferenceDetail } from "../cult/CultBusiness"
import {
  AggregationRange,
  IAverageMetricData,
  IMetricOptions,
  METRICS_CATEGORY_NAME
} from "@curefit/metrics-common"
import { getMeasurementOptions } from "./UnitUtil"
import { IMetricServiceClient } from "@curefit/metrics"
import { FitclubBusiness } from "../fitclub/FitclubBusiness"
import * as momentTz from "moment-timezone"
import { NEW_REFERRAL_APP_VERSION, ReferralUtil } from "../referral/ReferralUtil"
import { ICityService } from "@curefit/location-mongo"
import EnterpriseUtil from "../util/EnterpriseUtil"
import { ProductType } from "@curefit/product-common"
import { ActivePackViewV1 } from "../user/ActivePackViewBuilderV1"
import { NeuPassClickAction } from "@curefit/third-party-integrations-client"
import { ISegmentService } from "@curefit/vm-models"
import { ICultServiceOld } from "@curefit/cult-client"

export const METRICS_ID = {
  BMI: 1,
  HEIGHT: 2,
  WEIGHT: 3,
  FAT_PERCENTAGE: 8
}

class ProfileUtil {
  public static getAccountSection(
    fitcashBalance: number,
    countryId: string,
    isInternationalApp: boolean,
    userContext: UserContext,
    city: City,
    healthMetricWidget: HealthMetricWidget
  ): AccordionSectionItem {
    const accountSectionObj: AccordionSectionItem = {
      title: "Account",
      isExpandable: true,
      icon: "ACCOUNT"
    }
    const data: AccordionSectionItem[] = []

    const isMindFitApp = AppUtil.isMindFitApp(userContext)

    if (!isInternationalApp && !isMindFitApp) {
      if (healthMetricWidget) {
        data.push({
          title: "Latest Weight",
          description: healthMetricWidget.metrics?.[0]?.value?.toString(),
          action: healthMetricWidget.metrics?.[0]?.action,
          icon: "NAVIGATION"
        })
        data.push({
          title: "Latest BMI",
          description: healthMetricWidget.metrics?.[1]?.value?.toString(),
          action: healthMetricWidget.metrics?.[1]?.action,
          icon: "NAVIGATION"
        })
      }
      if (!isMindFitApp) {
        data.push(ProfileUtil.getChangeCitySection(userContext, city))
      }

      // TODO: enable is when Address page move to flutter
      // data.push(
      //   {
      //     title: "Address",
      //     action: {
      //       actionType: "NAVIGATION",
      //       url: ActionUtil.getSavedAddressPage()
      //     },
      //     icon: "NAVIGATION"
      //   })
    }

    data.push({
      title: "Contact Details",
      action: {
        actionType: "NAVIGATION",
        url: ActionUtil.getAccountSettingsUrl()
      },
      icon: "NAVIGATION"
    })

    if (AppUtil.isManageDevicesSupported(userContext)) {
    data.push({
      title: "Manage Devices",
      action: {
        actionType: "NAVIGATION",
        url: ActionUtil.getManageDevicesUrl(userContext)
      },
      icon: "NAVIGATION"
    })
  }

    if (!isMindFitApp && !isInternationalApp && countryId === "IN" && !AppUtil.isFitcashInsidePaymentsSupported(userContext)) {
      const fitcashItem: AccordionSectionItem = {
        title: "Fitcash",
        description: fitcashBalance.toString(),
        action: {
          actionType: "NAVIGATION",
          url: ActionUtil.getFitcashPageUrl()
        },
        icon: "NAVIGATION"
      }
      data.push(fitcashItem)
    }

    data.push(
      {
        title: "Logout",
        action: {
          actionType: "SHOW_LOGOUT_ALERT",
          meta: {
            subTitle: "Are you sure you want to logout?"
          }
        },
        icon: "NAVIGATION"
      }
    )
    accountSectionObj.data = data
    return accountSectionObj
  }

  public static async getAccountSectionV2(
    cultPreference: PreferenceDetail,
    userContext: UserContext,
    isClientCalendarEventEnabled: boolean,
    isUserEligibleForCallReminder: boolean
  ): Promise<AccordionSectionItem> {
    const data: (AccordionSectionItem | SwitchSectionItem)[] = []
    if (cultPreference) {
      const user = await userContext.userPromise
      const bookingEmailPreference = cultPreference.bookingEmailPreference
      const ivrCallTime = cultPreference.ivrCallTime
      const bookingIVRPreference = cultPreference.ivrCallPreference
      const city = userContext.userProfile.city
      if (AppUtil.isNotificationManagerSupported(userContext, user.isInternalUser)) {
        data.push({
          title: "Notification Preferences",
          type: "NOTIFICATION_PREFERENCES",
          meta: {
            actionType: "NAVIGATION",
            url: "curefit://notificationmanager"
          }
        })
      }
      if (CultUtil.isBookingPreferenceSupported(userContext) && (!AppUtil.isNewSigninFlowSupported(userContext) || (user.email && user.isEmailVerified) || isClientCalendarEventEnabled)) {
        data.push({
          type: "CALENDER_SYNC",
          title: "Class Calendar",
          meta: {
            value: bookingEmailPreference,
            alertInfo: {
              title:
                "Your booked classes will now appear on your preferred calendar. Get notified in advance at a time decided by you on your calendar.\nAfter turning on this feature, you will be auto-subscribed to email notifications",
              actions: [{ title: "CANCEL" }, { title: "OK" }]
            }
          }
        })
      }
      if (isUserEligibleForCallReminder && city.countryId === "IN" && ivrCallTime && ivrCallTime.slots) {
        ivrCallTime.slots.minutesBefore = ivrCallTime.selectedMinutesBefore
          ? ivrCallTime.selectedMinutesBefore
          : 75
        const ivrMeta = {
          selectedMinutesBefore: ivrCallTime.selectedMinutesBefore
            ? ivrCallTime.selectedMinutesBefore
            : 75,
          selectedCallTimeBefore: CultUtil.getCallTimeBefore(
            ivrCallTime.selectedMinutesBefore
              ? ivrCallTime.selectedMinutesBefore
              : 75
          ),
          selectedCallTimeBeforePostfix: "before class",
          actionText: "CHANGE",
          slots: {
            title: "Set time for call reminder",
            slotTimes: CultUtil.getSlotTimes(ivrCallTime.slots, false, "")
          },
          isEditable: true,
          showCheckBox: false,
        }
        data.push({
          type: "CALL_REMINDERS",
          title: "Call Reminder",
          meta: {
            value: bookingIVRPreference,
            ivrMeta
          }
        })
      }
    }

    // TODO: enable is when Address page move to flutter
    // data.push(
    //     {
    //       title: "Address",
    //       action: {
    //         actionType: "NAVIGATION",
    //         url: ActionUtil.getSavedAddressPage()
    //       },
    //       icon: "NAVIGATION"
    //     }
    // )
    data.push({
      title: "Contact Details",
      action: {
        actionType: "NAVIGATION",
        url: ActionUtil.getAccountSettingsUrl()
      },
      icon: "NAVIGATION"
    })
    const paymentsSection: AccordionSectionItem = ProfileUtil.getPaymentsSection()
    if (paymentsSection) {
      data.push(paymentsSection)
    }
    if (AppUtil.isManageDevicesSupported(userContext)) {
      data.push({
        title: "Logged In Devices",
        action: {
          actionType: "NAVIGATION",
          url: ActionUtil.getManageDevicesUrl(userContext)
        },
        icon: "NAVIGATION"
      })
    }
    data.push(
      {
        title: "Logout",
        action: {
          actionType: "SHOW_LOGOUT_ALERT",
          meta: {
            subTitle: "Are you sure you want to logout?"
          }
        },
        icon: "NAVIGATION"
      }
    )
    return {
      title: "Account Settings",
      isExpandable: true,
      icon: "ACCOUNT",
      data: data,
      subtitle: "Notifications • Contact Details • Payments • Others"
    }

  }

  public static getPaymentsSection(): AccordionSectionItem {
    return {
      title: "Payments",
      action: {
        actionType: "NAVIGATION",
        url: ActionUtil.getSavedPaymentsUrl()
      },
      icon: "PAYMENT"
    }
  }

  public static getOrdersSection(): AccordionSectionItem {
    return {
      title: "Orders",
      icon: "ORDER",
      action: {
        actionType: "NAVIGATION",
        url: "curefit://order"
      },
      subtitle: "View All Orders"
    }
  }

  public static getChallengesSection(): AccordionSectionItem {
    return {
      title: "Challenges",
      icon: "NAVIGATION",
      action: {
        actionType: "NAVIGATION",
        url: "curefit://enrolledchallenges"
      }
    }
  }

  public static getNeuPassAction(neuPassSection: NeuPassClickAction): AccordionSectionItem {
    if (neuPassSection.action === "DONT_SHOW") {
      return
    }
    if (neuPassSection.action === "NEUPASS_ACCOUNT") {
      return {
        title: "My NeuPass",
        icon: "TATA_NEU",
        action: {
          actionType: "NAVIGATION",
          url: `curefit://webview?uri=${encodeURIComponent(neuPassSection.url)}&theme=dark&title=${encodeURIComponent("My NeuPass")}`,
        }
      }
    }
    if (neuPassSection.action === "KNOW_MORE") {
      return {
        title: "My NeuPass",
        icon: "TATA_NEU",
        action: {
          actionType: "NAVIGATION",
          url: `curefit://webview?uri=${encodeURIComponent(neuPassSection.url)}&theme=dark&title=${encodeURIComponent("About Neupass")}`,
          meta: {
            action: {
              actionType: "ACTIVATE_TATA_NEUPASS",
              title: "ACTIVATE NOW",
            },
          },
        }
      }
    }
    if (neuPassSection.action === "CONSENT_FLOW") {
      return {
        title: "My NeuPass",
        icon: "TATA_NEU",
        action: {
          actionType: "SHOW_DYNAMIC_INTERVENTION",
          meta: {
            announcementId: "tata-neu-activated-2",
            v2: true,
          },
        }
      }
    }
  }

  static async getReferralSection(userContext: UserContext): Promise<AccordionSectionItem> {

    if (AppUtil.isMindFitApp(userContext)) {
      return undefined
    }

    const isMyReferralsSupported = AppUtil.isMyReferralsSupported(userContext)
    const isGiftCardSupported = await AppUtil.isGiftCardSupported(userContext)
    const isNewReferralSupported = ReferralUtil.isNewReferralPageSupported(userContext)
    const { osName, clientVersion } = userContext.sessionInfo

    const referralSectionData = []

    if (isNewReferralSupported) {
      referralSectionData.push({
        title: "Refer a Friend",
        icon: "NAVIGATION",
        action: {
          actionType: "NAVIGATION",
          url: "curefit://fl_referral_page"
        }
      })
    }

    if (isMyReferralsSupported) {
      referralSectionData.push({
        title: "My Referrals",
        action: {
          actionType: "NAVIGATION",
          url: "curefit://myreferralpage"
        },
        icon: "NAVIGATION"
      })
    }

    if (AppUtil.isRedeemVoucherSectionSupported(osName, clientVersion)) {
      referralSectionData.push(ProfileUtil.getRedeemVoucherSection(userContext))
    }
    if (isGiftCardSupported) {
      referralSectionData.push({
        title: "Buy Gift Card",
        action: { actionType: "EXTERNAL_DEEP_LINK", url: "https://cultfit.woohoo.in/en-gb/e-gift-cards/cultfit-e-gift-card-processing"},
        icon: "NAVIGATION"
      })

      referralSectionData.push({
        title: "Redeem Gift Card",
        action: {
          actionType: "NAVIGATION",
          url: "curefit://redeemgiftcardpage"
        },
        icon: "NAVIGATION"
      })
    }

    if (_.isEmpty(referralSectionData)) {
      return undefined
    }

    return {
      title: isGiftCardSupported ? "Referral, Vouchers & Gift Cards" : "Referrals & Vouchers",
      isExpandable: true,
      icon: "SHARE",
      data: referralSectionData,
      subtitle: "Refer a Friend • My Referrals • Redeem Voucher"
    } as AccordionSectionItem
  }

  public static getFitclubSection(): AccordionSectionItem {
    const title = "Rewards"
    return {
      title,
      icon: "FITCLUB",
      action: {
        actionType: "NAVIGATION",
        url: "curefit://fitclubsummarypage",
        meta: {
          title
        }
      }
    }
  }

  public static getDownloadedPacksSection(): AccordionSectionItem {
    return {
      title: "Downloaded Sessions",
      icon: "DOWNLOAD_V2",
      action: {
        actionType: "NAVIGATION",
        url: "curefit://downloadedpackspage"
      }
    }
  }

  public static getSubscriptionSection(userContext: UserContext): AccordionSectionItem {
    if (AppUtil.isMindFitApp(userContext)) {
      return undefined
    }
    return {
      title: "Active Packs & Subscriptions",
      icon: "PACK",
      action: {
        actionType: "NAVIGATION",
        url: "curefit://activepacks"
      }
    }
  }

  static getRedeemVoucherSection(userContext: UserContext): AccordionSectionItem {
    return {
      title: "Redeem Voucher",
      icon: "NAVIGATION",
      action: AppUtil.getRedeemVoucherAction(userContext)
    }
  }

  public static getNewMedicalRecordsSection(): AccordionSectionItem {
    return {
      title: "Medical Records",
      icon: "NAVIGATION",
      isExpandable: false,
      action: {
        actionType: "NAVIGATION",
        url: ActionUtil.getMedicalRecordsURL()
      }
    }
  }

  public static getHistorySection(): AccordionSectionItem {
    return {
      title: "My History",
      icon: "NAVIGATION",
      isExpandable: false,
      action: {
        actionType: "NAVIGATION",
        url: "curefit://support?pageId=bookings&title=My%20activities",
      }
    }
  }

  public static getCultHabitSection(): AccordionSectionItem {
    return {
      title: "Habit Reminders",
      icon: "NAVIGATION",
      isExpandable: false,
      action: {
        actionType: "NAVIGATION",
        url: "curefit://habit_building"
      }
    }
  }

  public static getMemoriesSection(): AccordionSectionItem {
    return {
      title: "Memories",
      icon: "NAVIGATION",
      isExpandable: false,
      action: {
        actionType: "NAVIGATION",
        url: "curefit://cultmemories?pageNumber=1&pageSize=10",
      }
    }
  }

  public static getMedicalRecordsSection(
    city: City,
    clientVersion: number
  ): AccordionSectionItem {
    return {
      title: "Medical Records",
      icon: "HEALTH",
      isExpandable: true,
      data: CareUtil.addCareMappedActionsV2(
        _.get(city, "cityId", ""),
        clientVersion
      )
    }
  }

  public static async getDevicesSection(
    req: express.Request,
    fitbitInfo: any,
    userContext: UserContext,
    isNotLoggedIn: boolean,
    segmentationClient: ISegmentService,
  ): Promise<AccordionSectionItem> {
    if (AppUtil.isMindFitApp(req.userContext)) {
      return undefined
    }
    const osName = req.headers["osname"] as string
    const deviceData: (SwitchSectionItem | AccordionSectionItem)[] = [
      {
        type: osName === "ios" ? "APPLE_HEALTH" : "GOOGLE_FIT",
        title: osName === "ios" ? "Apple Health" : "Google Fit",
        meta:
          osName === "android"
            ? { howitworks: ActionUtil.getStepsHowItWorks() }
            : undefined
      }
    ]

    if (osName === "android" && req.query.showSleepSetting === "true") {
      deviceData.push({
        type: "SLEEP_COMPUTING",
        title: "Sleep Computing"
      })
    }

    if (fitbitInfo) {
      deviceData.push({
        type: "FITBIT",
        title: "Fitbit"
      })
    }

    if (await AppUtil.doesUserBelongToFlutterAccountViewSegment(segmentationClient, userContext)) {
      const tvConnectSection = ProfileUtil.getTvConnectSection(isNotLoggedIn, userContext)
      if (tvConnectSection) {
        deviceData.push(tvConnectSection)
      }

      deviceData.push({
        title: "Connect cultROW",
        icon: "ADD",
        action: {
          actionType: "NAVIGATION",
          url: "curefit://equipmentclp?pageId=cultrowlist&connect_device=true"
        }
      })
    }
    const isAndroid = userContext.sessionInfo.osName === "android"
    const devicesSection: AccordionSectionItem = {
      title: "Fitness Devices",
      icon: "GEAR",
      type: "FITNESS_DEVICES",
      isExpandable: true,
      data: deviceData,
      subtitle: ((isAndroid) ? "Google Fit" : "Apple Health") +  " • Fitbit • TV APP • cultROW"
    }
    return devicesSection
  }

  public static getEquipmentSection(req: express.Request): AccordionSectionItem {
    const osName = req.headers["osname"] as string
    const clientVersion: number = Number(req.headers["clientversion"])
    const equipmentData: AccordionSectionItem[] = []

    if (AppUtil.isMindFitApp(req.userContext)) {
      return undefined
    }

    if (AppUtil.isCultROWDeviceSupported(clientVersion, osName)) {
      equipmentData.push({
        title: "Connect cultROW",
        icon: "ADD",
        action: {
          actionType: "NAVIGATION",
          url: "curefit://equipmentclp?pageId=cultrowlist&connect_device=true"
        }
      })
    }

    const equipmentSection: AccordionSectionItem = {
      title: "Smart Equipment",
      icon: "GEAR",
      isExpandable: true,
      data: equipmentData
    }
    return equipmentSection
  }

  public static async getSupportSection(segmentationClient: ISegmentService, userContext: UserContext): Promise<AccordionSectionItem> {
    return {
      title: "Support",
      icon: "SUPPORT",
      action: {
        actionType: "NAVIGATION",
        url: (await AppUtil.isNewSupportInFlutterSupported(segmentationClient, userContext)) ? "curefit://fl_support" : "curefit://support"
      },
      subtitle: "Queries • Tickets • FAQs"
    }
  }

  public static getChangeCitySection(
    userContext: UserContext,
    city: City
  ): AccordionSectionItem | undefined {
    let cityName = city.name
    if (city.parentCityName) {
      cityName = city.parentCityName
    }
    if (AppUtil.isCitySelectionActionSupported(userContext)) {
      return {
        title: `Location`,
        description: cityName,
        icon: "LOCATION",
        action: ActionUtil.showCityAction(true)
      }
    }
  }

  public static getTvConnectSection(isNotLoggedIn: boolean, userContext: UserContext): AccordionSectionItem  {
    const userAgent = userContext.sessionInfo.userAgent
    if (AppUtil.isMindFitApp(userContext)) {
      return null
    }
    if (!isNotLoggedIn && AppUtil.isTVCompanionExperienceSupported(userContext)) {
      const clientVersion = userContext.sessionInfo.clientVersion
      return {
        title: `Connect TV App`,
        icon: "CONNECT_TV",
        action: {
        actionType: "NAVIGATION",
        // temp fix for showing update app pop up
        url: (!AppUtil.isInternationalApp(userContext) && (clientVersion < 8.57)) ?  "curefit://connectTVAppOld" : "curefit://connectTVApp"
      }
      }
    }

  return null
  }

  public static getActivityAndRecordsSection(isFitclubV2Supported: boolean, userContext: UserContext, city: City): AccordionSectionItem {
    const { osName, clientVersion } = userContext.sessionInfo
    const data: AccordionSectionItem[] = []

    if (AppUtil.isMindFitApp(userContext)) {
      const medicalRecords = ProfileUtil.getNewMedicalRecordsSection()
      medicalRecords.icon = "CHALLENGE"
      return medicalRecords
    }

    const careSection: AccordionSectionItem =
      city.countryId === "IN" &&
      ProfileUtil.getMedicalRecordsSection(city, clientVersion)
    const challengesSection: AccordionSectionItem = city.countryId === "IN" && ProfileUtil.getChallengesSection()
    if (challengesSection) {
      data.push(challengesSection)
    }
    if (isFitclubV2Supported) {
      const levelItem: AccordionSectionItem = {
        title: "Levels & Badges",
        action: {
          actionType: "NAVIGATION",
          url: ActionUtil.getScoreDashboardUrl()
        },
        icon: "NAVIGATION"
      }
      data.push(levelItem)
    }
    if (AppUtil.isMedRecordsInActivityRecordsSupported(userContext)) {
      data.push(ProfileUtil.getNewMedicalRecordsSection())
    }
    data.push({
      title: "Fitness Reports",
      icon: "NAVIGATION",
      action: {
        actionType: "NAVIGATION",
        url: "curefit://listpage?pageId=LiveActivity"
      }
    })
    return {
      title: `Activity & Records`,
      isExpandable: true,
      icon: "CHALLENGE",
      data: data
    }
  }

  public static async getActivityAndRecordsSectionV2(
    userContext: UserContext,
    fitclubBusiness: FitclubBusiness,
    healthMetricWidget: HealthMetricWidget,
    cultService: ICultServiceOld,
    city: City,
    segmentationClient: ISegmentService
    ): Promise<AccordionSectionItem> {
    const data: AccordionSectionItem[] = []

    data.push({
      title: "Fitness Reports",
      icon: "NAVIGATION",
      action: {
        actionType: "NAVIGATION",
        url: "curefit://listpage?pageId=LiveActivity"
      }
    })
    const userId: string = userContext.userProfile.userId
    const memoriesResponse = await cultService.getCultMoments({userId, appName: "CUREFIT_API", pageNumber: 1, pageSize: 1})
    const memoriesPresent = memoriesResponse.cultMoments.length > 0
    if (memoriesPresent) {
      data.push(ProfileUtil.getMemoriesSection())
    }

    const isCultHabitEnabled = await AppUtil.isCultHabitsEnabled(userContext, segmentationClient)
    if (isCultHabitEnabled) {
      data.push(ProfileUtil.getCultHabitSection())
    }

    const isFitclubV2Supported: boolean = await fitclubBusiness.checkIfUserIsEligibleForFitclubV2(userContext)
    if (isFitclubV2Supported) {
      const levelItem: AccordionSectionItem = {
        title: "Levels & Badges",
        action: {
          actionType: "NAVIGATION",
          url: ActionUtil.getScoreDashboardUrl()
        },
        icon: "NAVIGATION"
      }
      data.push(levelItem)
    }
    const description = (healthMetricWidget.metrics?.[0]?.value) ? healthMetricWidget.metrics?.[0]?.value?.toString() + " kg" : null
    if (healthMetricWidget) {
      data.push({
        title: "Latest Weight",
        description: description,
        action: healthMetricWidget.metrics?.[0]?.action,
        icon: "NAVIGATION"
      })
      data.push({
        title: "Latest BMI",
        description: healthMetricWidget.metrics?.[1]?.value?.toString(),
        action: healthMetricWidget.metrics?.[1]?.action,
        icon: "NAVIGATION"
      })
    }
    const challengesSection: AccordionSectionItem = city.countryId === "IN" && ProfileUtil.getChallengesSection()
    if (challengesSection) {
      data.push(challengesSection)
    }
    data.push(ProfileUtil.getHistorySection())
    data.push(ProfileUtil.getDownloadedPacksSection())
    return {
      title: `Activity & Records`,
      isExpandable: true,
      icon: "CHALLENGE",
      data: data,
      subtitle: "Report" + ((memoriesPresent) ?  " • Memories" : "")  + " • Badges • Logging • History • Downloads"
    }
  }

  public static getDeveloperOptions(userContext: UserContext, user: User, cityService: ICityService): AccordionSectionItem {
    if (!UserView.isInternalUser(user)) {
      return null
    }
    const switchOptions: SwitchSectionItem[] = [
      {
        type: "SERVER_SWITCH",
        title: "Stage server"
      },
      {
        type: "INTERNAL_USER_SWITCH",
        title: "Internal build"
      }
    ]

    if (AppUtil.isDevOptionCountrySelectorSupported(userContext)) {
      const cities = cityService.listCities(AppUtil.getTenantFromUserContext(userContext))
      const countrySwitch: SwitchSectionItem = {
        type: "COUNTRY_SWITCH",
        title: "Select country",
        meta: cities.map(({ cityId, countryId }) => ({ cityId, cityName: countryId })),
      }
      switchOptions.push(countrySwitch)
    }

    if (AppUtil.isMinAppVersion(8.83, userContext)) {
      const switchItem: SwitchSectionItem = {
        type: "APP_INSPECTOR_SWITCH",
        title: "Analytics Inspector"
      }
      switchOptions.push(switchItem)
    }

    if (AppUtil.isMinAppVersion(8.93, userContext)) {
      const switchItem: SwitchSectionItem = {
        type: "THEME_SWITCH",
        title: "Theme Switch"
      }
      switchOptions.push(switchItem)
    }

    return {
      type: "DEV_OPTIONS",
      title: "Dev Options",
      icon: "DEVMODE",
      isExpandable: true,
      data: switchOptions
    }
  }

  public static async getBookingPreference(
    bookingEmailPreference: boolean,
    expandedElement: string,
    bookingIVRPreference: boolean,
    userContext: UserContext,
    ivrCallTime: IvrCallTime,
    isClientCalendarEventEnabled: boolean,
    isUserEligibleForCallReminder: boolean
  ): Promise<AccordionSectionItem> {
    const data: SwitchSectionItem[] = []
    const user = await userContext.userPromise
    if (CultUtil.isBookingPreferenceSupported(userContext) && (!AppUtil.isNewSigninFlowSupported(userContext) || (user.email && user.isEmailVerified) || isClientCalendarEventEnabled)) {
      data.push({
        type: "CALENDER_SYNC",
        title: "Class Calendar",
        meta: {
          value: bookingEmailPreference,
          alertInfo: {
            title:
              "Your booked classes will now appear on your preferred calendar. Get notified in advance at a time decided by you on your calendar.\nAfter turning on this feature, you will be auto-subscribed to email notifications",
            actions: [{ title: "CANCEL" }, { title: "OK" }]
          }
        }
      })
    }
    const city = userContext.userProfile.city
    if (isUserEligibleForCallReminder && city.countryId === "IN" && ivrCallTime && ivrCallTime.slots) {
      ivrCallTime.slots.minutesBefore = ivrCallTime.selectedMinutesBefore
        ? ivrCallTime.selectedMinutesBefore
        : 75
      const ivrMeta = {
        selectedMinutesBefore: ivrCallTime.selectedMinutesBefore
          ? ivrCallTime.selectedMinutesBefore
          : 75,
        selectedCallTimeBefore: CultUtil.getCallTimeBefore(
          ivrCallTime.selectedMinutesBefore
            ? ivrCallTime.selectedMinutesBefore
            : 75
        ),
        selectedCallTimeBeforePostfix: "before class",
        actionText: "CHANGE",
        slots: {
          title: "Set time for call reminder",
          slotTimes: CultUtil.getSlotTimes(ivrCallTime.slots, false, "")
        },
        isEditable: true
      }
      data.push({
        type: "CALL_REMINDERS",
        title: "Class Call",
        meta: {
          value: bookingIVRPreference,
          ivrMeta
        }
      })
    }
    if (AppUtil.isNotificationManagerSupported(userContext, user.isInternalUser)) {
      data.push({
        title: "Notification Preferences",
        type: "NOTIFICATION_PREFERENCES",
        meta: {
          actionType: "NAVIGATION",
          url: "curefit://notificationmanager"
        }
      })
    }
    return {
      type: "BOOKING_PREFERENCE",
      title: "Notifications & Reminders",
      icon: "CLASS_REMINDER",
      isExpandable: true,
      isExpanded: expandedElement === "BOOKING_PREFERENCE",
      data
    }
  }

  public static getProfileWidget(
    userContext: UserContext,
    user: User,
    fitcashBalance: number,
    city: City,
  ): ProfileNameIconWidget {
    const isInternationalApp = AppUtil.isInternationalApp(userContext)
    const profileNameIconWidget: ProfileNameIconWidget = {
      widgetType: "PROFILE_NAME_ICON_WIDGET",
      name: UserView.getUserName(user),
      profilePictureUrl: user.profilePictureUrl,
      action: {
        actionType: "NAVIGATION",
        url: ActionUtil.getAccountSettingsUrl()
      },
      fitcashAction: {
        actionType: "NAVIGATION",
        url: ActionUtil.getFitcashPageUrl()
      },
      profileCompletion: isInternationalApp ? 100 : UserView.getProfileCompletionPercentage(user),
      fitcash: city.countryId === "IN" ? fitcashBalance : undefined,
      fitclubEnabled: true
    }
    return profileNameIconWidget
  }

  public static getProfileSummaryWidget(
    userContext: UserContext,
    user: User,
    city: City,
    membershipItems: MembershipItem[],
  ): ProfileSummaryWidget {
    const isInternationalApp = AppUtil.isInternationalApp(userContext)
    const profileSummaryWidget: ProfileSummaryWidget = {
      widgetType: "PROFILE_SUMMARY_WIDGET",
      name: (!user.firstName) ? "" : user.firstName,
      profilePictureUrl: user.profilePictureUrl,
      actions: [
        {
          actionType: "NAVIGATION",
          title: "VIEW PROFILE",
          url: ActionUtil.getSocialProfileUrl(user.profilePictureUrl != null ),
          // url: ActionUtil.getFlutterProfileInfoUrl(),
        },
      ],
      profileCompletion: isInternationalApp ? 100 : UserView.getProfileCompletionPercentage(user),
      membershipItems: membershipItems,
    }
    return profileSummaryWidget
  }

  public static getMembershipListWidget(
    membershipItems: MembershipItem[]
  ): MembershipListWidget {
    return {
      widgetType: "MEMBERSHIP_LIST_WIDGET",
      backgroundColor: "rgb(245,245,245)",
      data: membershipItems
    }
  }

  public static getScoreView(
    userActivityDetails: UserActivityDetails
  ): ScoreCardWidget {
    const numDays = 30
    // Build the dashboard summary from the user activity detail
    const globalActivity = _.find(
      userActivityDetails.activitiesBreakUp,
      activity => {
        return activity.vertical === "GLOBAL"
      }
    )

    return {
      widgetType: "SCORE_CARD_WIDGET",
      title: "LEVEL " + userActivityDetails.level.levelId.toString(),
      score: globalActivity.currentStreak,
      status: "STREAK",
      subtitle:
        userActivityDetails.activityPoints +
        (userActivityDetails.numActivities == 1
          ? " POINT IN "
          : " POINTS IN ") +
        numDays +
        " DAYS",
      action: {
        actionType: "NAVIGATION",
        url: ActionUtil.getScoreDashboardUrl()
      }
    }
  }

  static _getMetricDetailActions(
    data: IAverageMetricData,
    userContext: UserContext,
    completionAction?: Action,
    isInternalUser?: boolean
  ): Action {
    if (!data.metric.isLoggable) {
      return
    }
    const metricOption = getMeasurementOptions(data.metric, userContext, isInternalUser)
    return {
      actionType: "UPDATE_METRIC",
      title: `Log Current ${data.metric.name}`,
      meta: { ...data.metric, metricOption, completionAction }
    }
  }

  static async getHealthMetricWidget(
    userContext: UserContext,
    metricService: IMetricServiceClient,
    userId: string,
    isInternalUser: boolean
  ): Promise<HealthMetricWidget> {

    if (AppUtil.isMindFitApp(userContext)) {
      return undefined
    }
    const latestMetrics = await metricService.getMetricsForCategory(
      userId,
      METRICS_CATEGORY_NAME.VITAL
    )
    let heightRangeDataPromise
    const weight = latestMetrics[METRICS_ID.WEIGHT.toString()]
    const BMI = latestMetrics[METRICS_ID.BMI.toString()]
    const fatPercentage = latestMetrics[METRICS_ID.FAT_PERCENTAGE.toString()]
    const isBMINil = _.isNil(BMI)
    const tz = userContext.userProfile.timezone
    const fromDate = momentTz
      .tz(tz)
      .startOf("week")
      .format("YYYY-MM-DD")
    const toDate = momentTz
      .tz(tz)
      .startOf("week")
      .add(6, "day")
      .format("YYYY-MM-DD")
    const rangeOptions = { userId, fromDate, toDate, aggregationRange: AggregationRange.WEEK }

    let weightAction: Action = {
      actionType: "NAVIGATION",
      url: `curefit://metricDetail?metricId=${METRICS_ID.WEIGHT}`
    }

    let bmiAction: Action = {
      actionType: "NAVIGATION",
      url: `curefit://metricDetail?metricId=${METRICS_ID.BMI}`
    }

    if (isBMINil) {
      heightRangeDataPromise = metricService.getAverageMetricsForCategoryForRange({ ...rangeOptions, metricId: METRICS_ID.HEIGHT })
    }

    if (_.isNil(weight)) {
      const weightRangeData = await metricService.getAverageMetricsForCategoryForRange({ ...rangeOptions, metricId: METRICS_ID.WEIGHT })
      weightAction = ProfileUtil._getMetricDetailActions(weightRangeData, userContext, undefined, isInternalUser)
      if (isBMINil) {
        const heightAction = ProfileUtil._getMetricDetailActions(
          heightRangeDataPromise && await heightRangeDataPromise,
          userContext,
          undefined,
          isInternalUser
        )
        // If weight is nill, BMI will have weight followed by height action
        bmiAction = ProfileUtil._getMetricDetailActions(
          weightRangeData,
          userContext,
          heightAction,
          isInternalUser
        )
      }
    } else if (isBMINil) {
      // If weight is not nill, BMI will have only height action
      bmiAction = ProfileUtil._getMetricDetailActions(
        heightRangeDataPromise && await heightRangeDataPromise,
        userContext,
        undefined,
        isInternalUser
      )
    }

    const healthMetricWidget: HealthMetricWidget = {
      widgetType: "HEALTH_METRIC_WIDGET",
      fitclubEnabled: true,
      metrics: [
        {
          title: "WEIGHT",
          value: weight !== null ? weight.value : null,
          action: weightAction
        },
        {
          title: "BMI",
          value: BMI !== null ? BMI.value : null,
          action: bmiAction
        }
      ]
    }

    if (fatPercentage !== null) {
      const fatMetric: HealthMetric = {
        title: "BODY FAT (%)",
        value: fatPercentage.value,
        action: {
          actionType: "NAVIGATION",
          url: `curefit://metricDetail?metricId=${METRICS_ID.FAT_PERCENTAGE}`
        }
      }
      healthMetricWidget.metrics.push(fatMetric)
    }
    return healthMetricWidget
  }

  static async getWidgetsForInternationalApp(
      req: express.Request,
      city: City, cultPreference: PreferenceDetail,
      expandedElement: string,
      isNotLoggedIn: boolean,
      cityService: ICityService,
      tvConnectSection: any,
      segmentationClient: ISegmentService
  ): Promise<WidgetView[]> {
    const userContext: UserContext = req.userContext
    const { osName, clientVersion } = userContext.sessionInfo
    const supportSection: AccordionSectionItem = await ProfileUtil.getSupportSection(segmentationClient, userContext)
    if (isNotLoggedIn === true) {
      const accountNotLoginWidget: AccountNotLoginWidget = {
        widgetType: "ACCOUNT_NOT_LOGIN_WIDGET",
        title: "Login or Sign up",
        action: {
          actionType: "LOGOUT"
        }
      }
      const sectionListWidget: AccordionSectionList = {
        widgetType: "ACCORDION_SECTION_LIST",
        sections: [
          supportSection,
        ]
      }
      return [accountNotLoginWidget, sectionListWidget]
    }

    const user: User = await userContext.userPromise
    let profileNameIconWidget: ProfileNameIconWidget
    if (user) {
      profileNameIconWidget = ProfileUtil.getProfileWidget(
        userContext,
        user,
        0,
        city,
      )
    }
    const accountSection: AccordionSectionItem = ProfileUtil.getAccountSection(0, city.countryId, true, userContext, city, null)
    const devOptionsSection: AccordionSectionItem = ProfileUtil.getDeveloperOptions(userContext, user, cityService)
    const sections: Array<AccordionSectionItem> = [accountSection, supportSection]
    if (cultPreference) {
      const bookingPreference: AccordionSectionItem | undefined = await ProfileUtil.getBookingPreference(
        cultPreference.bookingEmailPreference,
        expandedElement,
        cultPreference.ivrCallPreference,
        userContext,
        cultPreference.ivrCallTime,
        AppUtil.isCalendarEventSupportedForLive(userContext.sessionInfo, user),
        AppUtil.isCallReminderSupported(userContext)
      )
      if (bookingPreference && bookingPreference.data.length > 0) {
        sections.push(bookingPreference)
      }
    }

    if (AppUtil.isIntlFitnessDeviceIntegrationEnabled(osName, clientVersion)) {
      const devicesSection: AccordionSectionItem = await ProfileUtil.getDevicesSection(req, null, userContext, isNotLoggedIn, segmentationClient)
      if (devicesSection) {
        sections.push(devicesSection)
      }
    }

    if (null !== tvConnectSection) {
      sections.push(tvConnectSection)
    }

    if (devOptionsSection) {
      sections.push(devOptionsSection)
    }
    const sectionListWidget: AccordionSectionList = {
      widgetType: "ACCORDION_SECTION_LIST",
      sections
    }

    return [profileNameIconWidget, sectionListWidget]
  }

  public static async addMePageWidgets(
    req: express.Request,
    fitcashBalance: number,
    city: City,
    fitbitInfo: any,
    isNotLoggedIn: boolean,
    userScoreCardWidget: ScoreCardWidget,
    cultService: ICultServiceOld,
    cultPreference: PreferenceDetail,
    expandedElement: string,
    metricService: IMetricServiceClient,
    fitclubBusiness: FitclubBusiness,
    cityService: ICityService,
    enterpriseUtil: EnterpriseUtil,
    membershipItems: MembershipItem[],
    isMembershipListWidgetSupported: boolean,
    neuPassClickAction: NeuPassClickAction,
    segmentationClient: ISegmentService
  ): Promise<WidgetView[]> {
    const userContext: UserContext = req.userContext
    const tvConnectSection = ProfileUtil.getTvConnectSection(isNotLoggedIn, userContext)
    if (AppUtil.isInternationalApp(userContext)) {
      return ProfileUtil.getWidgetsForInternationalApp(
          req,
          city,
          cultPreference,
          expandedElement,
          isNotLoggedIn,
          cityService,
          tvConnectSection,
          segmentationClient
      )
    }
    const { osName, clientVersion } = userContext.sessionInfo
    const user: User = await userContext.userPromise
    let profileNameIconWidget: ProfileNameIconWidget
    let profileSummaryWidget: ProfileSummaryWidget
    const isFitclubV2Supported: boolean = await fitclubBusiness.checkIfUserIsEligibleForFitclubV2(userContext)
    if (user) {
      profileNameIconWidget = ProfileUtil.getProfileWidget(
        userContext,
        user,
        fitcashBalance,
        city
      )
      if (await AppUtil.doesUserBelongToFlutterAccountViewSegment(segmentationClient, userContext)) {
        if (membershipItems.length === 0) {
          let ctaUrl = "curefit://listpage?pageId=SKUPurchasePage&selectedTab=black"
          if (await AppUtil.isCenterLevelPricingSupported(userContext, segmentationClient)) {
            ctaUrl = "curefit://tabpage?pageId=fitnesshub"
          }
          membershipItems.push({
            title: "You have no memberships!",
            iconDimensions: {
              iconTopSpacing: 40,
              iconHeight: 74,
              iconWidth: 67
            },
            bottomCtaAction: {
              actionType: "NAVIGATION",
              title: "BUY A CULTPASS",
              url: ctaUrl
            },
            membershipStateTextColor: "transparent",
          })
        }
        profileSummaryWidget = ProfileUtil.getProfileSummaryWidget(
          userContext,
          user,
          city,
          membershipItems,
        )
      }
    }
    let membershipfiltered = membershipItems
    if (AppUtil.isMindFitApp(userContext)) {
      membershipfiltered = membershipItems.filter(item => item.productType === "MIND_THERAPY")
    }
    const membershipListWidget: MembershipListWidget = ProfileUtil.getMembershipListWidget(membershipfiltered)
    const showMembershipListWidget = membershipListWidget.data.length > 0 && isMembershipListWidgetSupported
    const healthMetricWidget = await ProfileUtil.getHealthMetricWidget(
      userContext,
      metricService,
      user.id,
      user.isInternalUser
    )

    if (!AppUtil.isMindFitApp(userContext) && await AppUtil.doesUserBelongToFlutterAccountViewSegment(segmentationClient, userContext)) {
      const bookingPrefSupportForLive = AppUtil.isCalendarEventSupportedForLive(userContext.sessionInfo, user)
      const activityAndRecordsSection: AccordionSectionItem = await ProfileUtil.getActivityAndRecordsSectionV2(
        userContext, fitclubBusiness, healthMetricWidget, cultService, city, segmentationClient)
      const accountSection: AccordionSectionItem =  await ProfileUtil.getAccountSectionV2(
        cultPreference,
        userContext,
        bookingPrefSupportForLive,
        AppUtil.isCallReminderSupported(userContext))
      const devicesSection: AccordionSectionItem = await ProfileUtil.getDevicesSection(req, fitbitInfo, userContext, isNotLoggedIn, segmentationClient)
      const ordersSection: AccordionSectionItem = ProfileUtil.getOrdersSection()
      let cultPassCorpSection: AccordionSectionItem
      if (AppUtil.isEnterpriseCLPSupported(userContext)) {
        cultPassCorpSection = enterpriseUtil.getCultPassCorpAccordionSection()
      }
      const referralSection: AccordionSectionItem = await ProfileUtil.getReferralSection(userContext)
      const supportSection: AccordionSectionItem = await ProfileUtil.getSupportSection(segmentationClient, userContext)
      const devOptionsSection: AccordionSectionItem = ProfileUtil.getDeveloperOptions(userContext, user, cityService)
      if (devOptionsSection) {
        if (AppUtil.isDevOptionsPageSupported(userContext)) {
          devOptionsSection.action = {
            actionType: "NAVIGATION",
            url: "curefit://devoptions",
            meta: {
              devOptions: devOptionsSection.data
            }
          }
          devOptionsSection.isExpandable = false
          delete devOptionsSection.data
        }
      }
      let sections: Array<AccordionSectionItem> = [
        activityAndRecordsSection,
        accountSection,
        devicesSection,
        ordersSection,
        cultPassCorpSection,
        referralSection,
        supportSection,
        devOptionsSection
      ]
      sections = sections.filter(item => item)
      const sectionListWidget: AccordionSectionList = {
        widgetType: "ACCORDION_SECTION_LIST",
        sections: sections
      }
      return [profileNameIconWidget, profileSummaryWidget, sectionListWidget]
    }

    const subscriptionsSection: AccordionSectionItem = ProfileUtil.getSubscriptionSection(userContext)
    const supportSection: AccordionSectionItem = await ProfileUtil.getSupportSection(segmentationClient, userContext)
    const devicesSection: AccordionSectionItem = await ProfileUtil.getDevicesSection(req, fitbitInfo, userContext, isNotLoggedIn, segmentationClient)
    const equipmentSection: AccordionSectionItem = ProfileUtil.getEquipmentSection(req)
    const devOptionsSection: AccordionSectionItem = ProfileUtil.getDeveloperOptions(userContext, user, cityService)

    if (isNotLoggedIn === true) {
      const accountNotLoginWidget: AccountNotLoginWidget = {
        widgetType: "ACCOUNT_NOT_LOGIN_WIDGET",
        title: "Login or Sign up",
        action: {
          actionType: "LOGOUT"
        }
      }

      const sectionListWidget: AccordionSectionList = {
        widgetType: "ACCORDION_SECTION_LIST",
        sections: [
          subscriptionsSection,
          devicesSection,
          equipmentSection,
          supportSection
        ]
      }
      sectionListWidget.sections = sectionListWidget.sections.filter(
        section => !_.isEmpty(section)
      )
      return [accountNotLoginWidget, sectionListWidget]
    }

    const accountSection: AccordionSectionItem = ProfileUtil.getAccountSection(fitcashBalance, city.countryId, false, userContext, city, showMembershipListWidget && healthMetricWidget)
    const ordersSection: AccordionSectionItem = ProfileUtil.getOrdersSection()
    const paymentsSection = ProfileUtil.getPaymentsSection()

    const referralSection: AccordionSectionItem = await ProfileUtil.getReferralSection(userContext)
    // disabling rewards widget
    // const fitclubSection: AccordionSectionItem = ProfileUtil.getFitclubSection()

    let sections: Array<AccordionSectionItem> = [
      accountSection,
      ordersSection,
      paymentsSection
    ]

    let neuPassSection: AccordionSectionItem
    if (neuPassClickAction) {
      neuPassSection = await ProfileUtil.getNeuPassAction(neuPassClickAction)
    }

    if (neuPassSection) {
      sections.push(neuPassSection)
    }
    if (subscriptionsSection) {
      sections.push(subscriptionsSection)
    }


    const bookingPrefSupportForLive = AppUtil.isCalendarEventSupportedForLive(userContext.sessionInfo, user)

    const bookingPreference: AccordionSectionItem | undefined = cultPreference && await ProfileUtil.getBookingPreference(
      cultPreference.bookingEmailPreference,
      expandedElement,
      cultPreference.ivrCallPreference,
      userContext,
      cultPreference.ivrCallTime,
      bookingPrefSupportForLive,
      AppUtil.isCallReminderSupported(userContext)
    )

    if (bookingPreference && bookingPreference.data.length > 0) {
      sections.push(bookingPreference)
    }
    sections.push(ProfileUtil.getActivityAndRecordsSection(isFitclubV2Supported, userContext, city))
    const careSection: AccordionSectionItem =
      city.countryId === "IN" &&
      ProfileUtil.getMedicalRecordsSection(city, clientVersion)
    if (!AppUtil.isMindFitApp(userContext) && careSection && !AppUtil.isMedRecordsInActivityRecordsSupported(userContext)) {
      sections.push(careSection)
    }
    sections.push(devicesSection)
    if (!AppUtil.isMindFitApp(userContext) && AppUtil.isCultROWDeviceSupported(clientVersion, osName)) {
      sections.push(equipmentSection)
    }
    if (!AppUtil.isMindFitApp(userContext) && AppUtil.isEnterpriseCLPSupported(userContext)) {
      sections.push(enterpriseUtil.getCultPassCorpAccordionSection())
    }

    if (!_.isNil(referralSection)) {
      sections.push(referralSection)
    }
    if (!AppUtil.isMindFitApp(userContext)) {
      sections.push(ProfileUtil.getDownloadedPacksSection())
    }

    if (null !== tvConnectSection) {
      sections.push(tvConnectSection)
    }

    sections.push(supportSection)
    if (devOptionsSection) {
      if (AppUtil.isDevOptionsPageSupported(userContext)) {
        devOptionsSection.action = {
          actionType: "NAVIGATION",
          url: "curefit://devoptions",
          meta: {
            devOptions: devOptionsSection.data
          }
        }
        devOptionsSection.isExpandable = false
        delete devOptionsSection.data
      }
      sections.push(devOptionsSection)
    }

    sections = sections.filter(item => item)
    const sectionListWidget: AccordionSectionList = {
      widgetType: "ACCORDION_SECTION_LIST",
      sections: sections
    }

    if (city.countryId === "IN") {
      if (showMembershipListWidget) {
        return [profileNameIconWidget, membershipListWidget, sectionListWidget]
      }
      else if (isFitclubV2Supported) {
        if (healthMetricWidget) {
          return [profileNameIconWidget, healthMetricWidget, sectionListWidget]
        } else {
          return [profileNameIconWidget, sectionListWidget]
        }

      }
      return [profileNameIconWidget, userScoreCardWidget, sectionListWidget]
    }
    return [profileNameIconWidget, sectionListWidget]
  }
}

export default ProfileUtil
