import { Benefit, BenefitType, Membership } from "@curefit/membership-commons"
import { ISegmentService, UserContext } from "@curefit/vm-models"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { ManageOptions, PauseInfo } from "../common/views/WidgetView"

import * as _ from "lodash"
import AppUtil from "./AppUtil"
import { Action } from "@curefit/apps-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { UrlPathBuilder } from "@curefit/product-common"
import { AccessLevel, FitnessPack } from "@curefit/cult-common"
import { SubAccessLevel } from "@curefit/gymfit-common"
import { CultCenter } from "@curefit/cult-common"
import { CultPackStartDateOptions } from "../cult/cultpackpage/CultPackCommonViewBuilder"
import { CenterResponse } from "@curefit/center-service-common"
import * as moment from "moment/moment"


const BUFFER_TIME = 2000

export const PLAY_NAS_PRICE_HIKE_BANNER_ID = "e462e6a0-b264-4866-ae77-aa12c685b072_play"

export interface PlaySelectPackDetails {
    isSelectMembership: boolean,
    centerIds: number[]
}

export const PLAY_MEMBERSHIP_PRIMARY_BENEFITS: Benefit[] = [{
    name: "PLAY",
    allowOverlap: false,
    type: BenefitType.STATIC,
}]

class PlayUtil {

    static BOOKING_DEEPLINK: string = "curefit://fitso_classbooking"
    static WEB_BOOKING_DEEPLINK: string = "curefit://slotbooking"
    static PLAY_NOSHOW_POLICY_V2_VERSION: number = 10.01
    static PLAY_SELECT_UPGRADE_VERSION: number = 9.98
    static PLAY_PACK_HERO_IMAGE: string = "/image/packs/play/PLAYPACK1648/22.jpg"
    static PLAY_PACK_MAGAZINE_WEB: string = "/image/packs/play/PLAYPACK1648/22_mag_web.jpg"
    static PLAY_PACK_MAGAZINE_MOBILE: string = "/image/packs/play/PLAYPACK1648/22_mag.jpg"
    static LIMITED_PACK_BENEFIT_NAME: string = "PLAY_LIMITED"
    static PLAY_PACK_V2_PROD: string = "62736478-8e70-46f6-828d-80860c34445f"
    static PLAY_PACK_V2_STAGE: string = "c56c8c67-17ae-4476-b7b0-fa488c917c61"

    static getMagazineImage(userAgent: UserAgent) {
        if (userAgent === "DESKTOP") {
            return PlayUtil.PLAY_PACK_MAGAZINE_WEB
        } else {
            return PlayUtil.PLAY_PACK_MAGAZINE_MOBILE
        }
    }

    static async isPlayPackPageSupported(
        userContext: UserContext,
        segmentationClient: ISegmentService
    ): Promise<boolean> {
        const segmentId = AppUtil.isProdLike ? PlayUtil.PLAY_PACK_V2_PROD : PlayUtil.PLAY_PACK_V2_STAGE
        return !_.isEmpty(await segmentationClient.doesUserBelongToSegment(segmentId, userContext))
    }

    static isCurrentPlayMembership(membership: Membership) {
        const now = Date.now()
        return membership.start <= now && membership.end >= now
    }

    static isExpiredPlayMembership(membership: Membership) {
        const now = Date.now()
        return membership.end <= now
    }

    static async getPackPauseResumeDetails(membership: Membership, productType: string, userContext: UserContext) {
        const tz: Timezone = userContext.userProfile.timezone
        if (this.isPauseResumeAllowed(userContext, membership)) {
            const isPauseAllowed = this.convertMillisToDays(membership.remainingPauseDuration) > 0
            const isOptionEnabled = membership.status === "PAUSED" || isPauseAllowed
            const infoText = membership.status === "PAUSED" ? "You can also manually resume your pack before the specified date.\nOnce you resume, your pack will be extended by the number of days you were on pause"
                : "You can pause your pack when you can't use it like during travel or illness.\n\nOnce you resume, your pack is extended by the number of days you were on pause"
            const manageOptions: ManageOptions = {
                displayText: membership.status === "PAUSED" ? "Modify Pause" : "Pause pack",
                icon: membership.status === "PAUSED" ? "RESUME" : "PAUSE",
                options: [{
                    isEnabled: isOptionEnabled,
                    type: membership.status === "PAUSED" ? "RESUME_CULT_MEMEBERSHIP" : "PAUSE_CULT_MEMEBERSHIP",
                    displayText: membership.status === "PAUSED" ? "Modify Pause" : "Pause pack"
                }],
                info: {
                    title: "About Pause",
                    subTitle: infoText
                }
            }

            let pauseEndDate, pauseStartDate
            let limit = TimeUtil.formatEpochInTimeZone(tz, membership.end)
            const today = TimeUtil.todaysDate(tz, "yyyy-mm-dd")
            let pauseDaysUsedInThisCycle = 0
            if (membership.status === "PAUSED") {
                const pauseDate = membership.activePause ? membership.activePause.end ? TimeUtil.parseDateFromEpochWithTimezone(tz, membership.activePause.end) : undefined : undefined
                pauseEndDate = pauseDate
                manageOptions.subTitle = pauseDate ? `Paused till ${TimeUtil.formatDateInTimeZone(tz, pauseEndDate, "DD MMM")}` : "Your pack is paused"
                pauseStartDate = membership.activePause ? TimeUtil.parseDateFromEpochWithTimezone(tz, membership.activePause.start) : undefined
                const startDate = membership.activePause.start ? TimeUtil.formatEpochInTimeZone(tz, membership.activePause.start) : undefined
                pauseDaysUsedInThisCycle = membership.activePause ? membership.activePause.start && membership.activePause.end ?
                    this.convertMillisToDays(membership.activePause.end - membership.activePause.start)
                    : 0 : 0
                limit = TimeUtil.formatEpochInTimeZone(tz, membership.end)
            } else {
                if (isPauseAllowed) {
                    manageOptions.subTitle = `${this.convertMillisToDays(membership.remainingPauseDuration)} pause days remaining`
                } else {
                    manageOptions.subTitle = "You have used all your pauses."
                }
            }

            const pauseDaysUsedTillDate = this.convertMillisToDays(membership.maxPauseDuration - membership.remainingPauseDuration)
            const meta: any = {
                membershipId: membership.id,
                packId: membership.productId,
                productType: productType,
                title: "Resume/Edit Pause",
                pauseMaxDays: this.convertMillisToDays(membership.maxPauseDuration),
                remainingPauseDays: this.convertMillisToDays(membership.remainingPauseDuration),
                remainingDaysInCurrentDuration: pauseDaysUsedInThisCycle,
                pauseEndDate: pauseEndDate ? TimeUtil.formatDateInTimeZone(tz, pauseEndDate) : undefined,
                startDateParams: {
                    date: TimeUtil.formatDateInTimeZone(tz, pauseStartDate, "YYYY-MM-DD hh:mm A"),
                    limit,
                    canEdit: false,
                    pauseEndText: "You had paused your pack till"
                },
                action: {
                    primaryText: "RESUME",
                    secondaryText: "EDIT"
                },
                editPauseAction: {
                    meta: { isEdit: true, membershipId: membership.id, productType },
                    actionType: "PAUSE_CULT_MEMEBERSHIP"
                },
                pauseInfoTitles: {
                    pauseUsed: "Pause days used",
                    membershipExtended: "Membership will be extended by",
                    membershipEndsOn: "Membership will now end on",
                    pauseLeft: "Pause days left"
                },
                pauseInfo: this.getPauseInfo(tz, pauseDaysUsedTillDate, pauseDaysUsedInThisCycle, limit, this.convertMillisToDays(membership.remainingPauseDuration) - pauseDaysUsedInThisCycle, true),
                pauseReason: {
                    options: this.getPauseReasons(userContext),
                    selectionOptionId: "TRAVEL_OUT",
                    allowOthers: true
                },
                description: "You can pause your pack as many times as you like until you reach this limit.",
                multiplePageActions: true,
            }
            return {
                manageOptions,
                meta,
                isDisabled: !isOptionEnabled,
                isPauseAllowed
            }
        }
    }

    public static isPauseResumeAllowed(userContext: UserContext, membership: Membership) {
        if ((membership.status === "PAUSED" || this.isCurrentPlayMembership(membership)) && !this.isUpcomingPause(membership)) {
            return true
        }
        return false
    }

    public static isUpcomingPause(membership: Membership): boolean {
        return membership.status === "PURCHASED" && !_.isEmpty(membership.activePause)
    }
    public static convertMillisToDays(durationInMillis: number): number {
        durationInMillis = durationInMillis + BUFFER_TIME
        return Math.floor(durationInMillis / (24 * 60 * 60 * 1000))
    }

    static getPauseInfo(tz: Timezone, pauseDaysUsedTillDate: number, pausedUsed: number, packEndDate: string, pauseDaysLeft: number, shouldAddExtendsOn: boolean): PauseInfo[] {
        const endsOn = TimeUtil.formatDateInTimeZone(tz, new Date(packEndDate), "DD MMM YYYY")
        const pauseInfo = [
            { title: "Pause Days Used till Date", value: AppUtil.appendDays(pauseDaysUsedTillDate) },
            { title: "Pause Days Left", value: AppUtil.appendDays(pauseDaysLeft) },
        ]
        if (shouldAddExtendsOn) {
            pauseInfo.push({ title: "Membership will now end on", value: endsOn })
        }
        return pauseInfo
    }

    static getPauseReasons(userContext: UserContext) {
        const options = [
            {
                optionText: "I am travelling out of town",
                optionId: "TRAVEL_OUT",
                actions: this.getActionsForPause("TRAVEL_OUT", userContext)
            },
            {
                optionText: "I am injured",
                optionId: "INJURED" ,
                actions: this.getActionsForPause("INJURED", userContext)
            },
            {
                optionText: "I am unwell",
                optionId: "UNWELL",
                actions: this.getActionsForPause("UNWELL", userContext)
            },
            {
                optionText: "Work is keeping me busy",
                optionId: "WORK_BUSY",
                actions: this.getActionsForPause("WORK_BUSY", userContext)
            },
            {
                optionText: "I have personal commitments",
                optionId: "PERSONAL_COMITMENTS",
                actions: this.getActionsForPause("PERSONAL_COMITMENTS", userContext)
            },
            {
                optionText: "Shutdown due to COVID-19",
                optionId: "COVID19",
                actions: this.getActionsForPause("COVID19", userContext)
            },
        ]
        options.push({
            optionText: "Others",
            optionId: "OTHERS",
            actions: this.getActionsForPause("OTHERS", userContext)
        })
        return options
    }

    static getActionsForPause(optionId: string, userContext: UserContext) {
        return [{
            title: "Pause Now",
            actionType: "PAUSE",
        }]
    }

    static getPackPauseResumeAction(pausePackData: any, membership: Membership): Action | null {
        if (pausePackData) {
            let meta = pausePackData.meta
            if (membership.status === "PAUSED") {
                meta = {
                    ...meta,
                    dateParam: {
                        ...meta.startDateParams,
                        pauseEndDate: meta.pauseEndDate
                    }
                }
            }
            return {
                iconUrl: membership.status === "PAUSED" ? "/image/icons/cult/resume.png" : "/image/icons/cult/pause.png",
                title: membership.status === "PAUSED" ? "MODIFY PAUSE" : "PAUSE",
                actionType: pausePackData.manageOptions.options[0].type,
                meta: meta
            }
        }
        return null
    }

    static async getPlayMembershipDetailsUrl(userContext: UserContext, playMembership: Membership) {
        if (AppUtil.isWeb(userContext)) {
            return `curefit://cultpack?productId=${playMembership?.productId}&membershipId=${playMembership?.id}`
        } else if (await AppUtil.isMembershipDetailV2PageSupported(userContext)) {
            return AppUtil.getNewMembershipDetailPage(playMembership.id.toString(), false)
        }
        return `curefit://playpack?membershipId=${playMembership.id}`
    }

    static isPlaySelectMembership(playMembership: Membership): PlaySelectPackDetails {
        const playBenefit: Benefit = playMembership.benefits.filter((benefit) => {
            return benefit.name === "PLAY"
        })[0]

        let playAttribute
        if (playMembership.attributes != null) {
            playAttribute = playMembership.attributes?.filter((attribute) => {
                attribute.attrKey === "ACCESS_CENTER"
            })
        }

        let centerId = playBenefit?.meta["allowedCenterIDs"]
        let isCenterSpecificAcces = false
        if (playAttribute && playAttribute.length > 0) {
            isCenterSpecificAcces = true
            centerId = playAttribute[0].attrValue
        }

        return {
            isSelectMembership:  !_.isEmpty(playBenefit?.meta["allowedCenterIDs"]) || isCenterSpecificAcces,
            centerIds: centerId
        }
    }

    static isEnterPriseLimitedMembership(membership: Membership): boolean {
       return membership != null && membership.metadata?.limitedSessions === true
    }

    static getPlayClassBookingPageUrl() {
        return PlayUtil.BOOKING_DEEPLINK
    }

    static getPlayWebBookSlotUrl(pageFrom?: string, centerId?: string, workoutId?: string): string {
        const queryObj: any = {}
        if (pageFrom) {
            queryObj.pageFrom = pageFrom
        }
        if (centerId) {
            queryObj.centerId = centerId
        }
        if (workoutId) {
            queryObj.workoutId = workoutId
        }
        return UrlPathBuilder.appendQueryParamToUrl(PlayUtil.WEB_BOOKING_DEEPLINK, queryObj)
    }

    static getPlayWebBookSlotActionMeta(pageFrom?: string, centerId?: string, workoutId?: string): any {
        const meta: any = {
            productType: "PLAY"
        }
        if (pageFrom) {
            meta.pageFrom = pageFrom
        }
        if (centerId) {
            meta.centerId = centerId
        }
        if (workoutId) {
            meta.workoutId = workoutId
        }
        return meta
    }

    static getPlayWebBookSlotAction(pageFrom?: string, centerId?: string, workoutId?: string): Action {
        const playBookSlotActionMeta: any = {
            productType: "PLAY"
        }
        return {
            url: this.getPlayWebBookSlotUrl(pageFrom, centerId, workoutId),
            title: "BOOK NOW",
            actionType: "NAVIGATION",
            meta: this.getPlayWebBookSlotActionMeta(pageFrom, centerId, workoutId)
        }
    }

    static isPlaySelectUpgradeSupported(userContext: UserContext) {
        const { appVersion } = userContext?.sessionInfo
        return appVersion >= PlayUtil.PLAY_SELECT_UPGRADE_VERSION
    }


    static getSuffixAndPackHeaderForPlaySelectPack(centerName: string): { suffix: string, packHeader: string } {
        const centerWords: string[] = centerName.split(" ")
        if (centerWords.length > 1) {
            const midWay = Math.floor(centerWords.length / 2)
            let suffix = ""
            let packHeader = ""
            for (let i = 0; i < centerWords.length; i++) {
                if (i < midWay) {
                    if (suffix.length > 0) suffix += " "
                    suffix += centerWords[i]
                } else {
                    if (packHeader.length > 0) {
                        packHeader += " "
                    }
                    packHeader += centerWords[i]
                }
            }

            return {
                suffix,
                packHeader
            }
        }

        return {
            suffix: "",
            packHeader: centerName
        }
    }

    static getSuffixAndPackHeaderForPlaySportPack(centerName: string, workoutName: string): { suffix: string, packHeader: string } {
        return {
            suffix: "cultpass " + workoutName,
            packHeader: centerName
        }
    }

    static getSuffixAndPackHeaderForPlayPack() {
        return {
            suffix: "cultpass",
            packHeader: "PLAY"
        }
    }

    static getSuffixAndPackHeaderForPlayCityLevelSportsPack(workoutName: string) {
        return {
            suffix: "cultpass",
            packHeader: workoutName
        }
    }

    static isPlayNoShowPolicyV2Supported(userContext: UserContext): boolean {
        const { appVersion } = userContext?.sessionInfo
        return appVersion >= PlayUtil.PLAY_NOSHOW_POLICY_V2_VERSION
    }

    static getNumberOrdinal(num: number): string {
        const sufixes: string[] = [ "th", "st", "nd", "rd", "th", "th", "th", "th", "th", "th" ]
        switch (num % 100) {
            case 11:
            case 12:
            case 13:
                return num + "th"
            default:
                return num + sufixes[num % 10]

        }
    }

    static getSportNameById(workoutId: string): string {
        switch (workoutId) {
            case "350":
                return "Badminton"
            case "351":
                return "Swimming"
            case "352":
                return "Table Tennis"
            case "353":
                return "Tennis"
            case "354":
                return "Squash"

        }
    }

    static getPackImageByWorkoutId(workoutId: string): string {
        switch (workoutId) {
            case "350":
                return "/image/icons/fitsoImages/pack_badminton.png"
            case "351":
                return "/image/icons/fitsoImages/pack_swimming.png"
            case "352":
                return "/image/icons/fitsoImages/pack_tt.png"
            case "353":
                return "/image/icons/fitsoImages/pack_tennis.png"
            case "354":
                return "/image/icons/fitsoImages/pack_squash.png"

        }
    }

    static getSupportWhitePageIcon(workoutId: string): string {
        switch (workoutId) {
            case "350":
                return "/image/icons/fitsoImages/support_badminton.png"
            case "351":
                return "/image/icons/fitsoImages/support_swimming.png"
            case "352":
                return "/image/icons/fitsoImages/support_tt.png"
            case "353":
                return "/image/icons/fitsoImages/support_tennis.png"
            case "354":
                return "/image/icons/fitsoImages/support_squash.png"
        }
    }

    static getUpgradePlayIcon(activityId: string): string {
        switch (activityId) {
            case "350":
                return "/image/icons/fitsoImages/play_badminton_hd.png"
            case "351":
                return "/image/icons/fitsoImages/play_swim_hd.png"
            case "352":
                return "/image/icons/fitsoImages/play_tt_hd.png"
            case "353":
                return "/image/icons/fitsoImages/play_tennis_hd.png"
            case "354":
                return "/image/icons/fitsoImages/play_squash_hd.png"
            default:
                return "/image/icons/fitsoImages/play_badminton_hd.png"
        }
    }

    static getWorkoutIdsByCityId(cityId: string): number[] {
        switch (cityId?.toUpperCase()) {
            case "GURGAON":
                return [350, 351, 352, 353, 354]
            case "BANGALORE":
            case "HYDERABAD":
            default:
                return [350, 351, 352]
        }
    }

    static getWorkoutWebPageAction(workoutId: string): string {
        const trimmedWorkoutName = this.getSportNameById(workoutId).toLowerCase().trim().replace(" ", "-")
        return `/play/sport/${trimmedWorkoutName}/${workoutId}?workoutId=${workoutId}&productType=PLAY&pageType=cultworkoutv2`
    }

    static getPlayOrderDetailSportIcon(activityId: string): string {
        switch (activityId) {
            case "350":
                return "/image/icons/fitsoImages/order_detail_badminton.png"
            case "351":
                return "/image/icons/fitsoImages/order_detail_swim.png"
            case "352":
                return "/image/icons/fitsoImages/order_detail_tt.png"
            case "353":
                return "/image/icons/fitsoImages/order_detail_tennis.png"
            case "354":
                return "/image/icons/fitsoImages/order_detail_squash.png"
            default:
                return "/image/icons/fitsoImages/order_detail_play.png"
        }
    }

    static isSLPPack(pack: FitnessPack) {
        return pack?.accessLevel === AccessLevel.CENTER && pack?.subLevelAccessListings?.filter(i => i?.subAccessLevel === SubAccessLevel.ACTIVITY)?.length > 0
    }

    static isCitySLPPack(pack: FitnessPack) {
        return pack?.accessLevel === AccessLevel.CITY && pack?.subLevelAccessListings?.filter(i => i?.subAccessLevel === SubAccessLevel.ACTIVITY)?.length > 0
    }

    static isPlayWebRequest(userContext: UserContext, productType: string) {
        return AppUtil.isWeb(userContext) && productType === "PLAY"
    }

    static getPlayCityNameFromCityId(cityId: number ): string {
        switch (cityId) {
            case 1:
                return "Bangalore"
            case 2:
                return "Gurgaon"
            case 3:
                return "Hyderabad"
        }
        return ""
    }

    static getPackStartDateOptions(
        userContext: UserContext,
        preferredCenter: CultCenter,
        canChangeStartDate: boolean,
        selectedDate: string,
        startDateWindow: number,
        minimumDate?: string,
        maximumDate?: string
    ) {
        const tz = userContext.userProfile.timezone
        if (_.isNil(minimumDate)) {
            minimumDate = TimeUtil.todaysDateWithTimezone(tz)
        }
        if (preferredCenter) {
            minimumDate = this.getMinimumStartDateForCenter(preferredCenter, minimumDate)
        }
        if (!maximumDate) {
            // any large number for end date
            maximumDate = TimeUtil.addDays(tz, minimumDate, startDateWindow)
        }
        if (canChangeStartDate && (selectedDate < minimumDate || selectedDate > maximumDate)) {
            selectedDate = undefined
        }
        if (!canChangeStartDate && !selectedDate) {
            selectedDate = minimumDate
        }

        return { minimumDate, selectedDate, maximumDate }
    }

    static getMinimumStartDateForCenter(cultCenter: CultCenter, startDate: string) {
        return _.max([cultCenter.launchDate, cultCenter.minMembershipStartDate, startDate])
    }

    static async getDatePickerAction(title: string, startDateOptions: CultPackStartDateOptions, productId: string): Promise<Action> {
        return  {
            title: title,
            actionType: "SHOW_PLAY_SELECT_DATE_PICKER",
            meta: {
                firstDate: startDateOptions.minEligibleDate,
                lastDate: startDateOptions.maxEligibleDate,
                selectedDate: startDateOptions.selectedDate,
                action: {
                    actionType: "UPDATE_CHECKOUT_V2_BLOC",
                    meta: {
                        refreshPage: true,
                        productId: productId,
                    }
                }
            }
        }
    }

    static async getMinimumAndMaximumStartDate(
        centerResponsePromise: Promise<CenterResponse>,
        earliestStartDatePlay: any,
        packDuration: number
    ) {
        let centerInfo: CenterResponse
        let centerLaunchDate = null
        centerInfo = await centerResponsePromise
        if (centerInfo?.launchDate) {
            const centerLaunchDateMs = centerInfo.launchDate
            centerLaunchDate = centerLaunchDateMs ? moment(centerLaunchDateMs).startOf("day").valueOf() : null
        }

        let earliestStartDate = earliestStartDatePlay.start // moment(earliestStartDatePlay.start).add(1, "days").startOf("day").valueOf()
        if (centerLaunchDate) {
            earliestStartDate = Math.max(earliestStartDate, centerLaunchDate)
        }

        let centerEndDate = null
        if (centerInfo?.terminationDate) {
            const centerEndDateMs = centerInfo.terminationDate
            if (centerEndDateMs) {
                const packEndDateMs = centerInfo.terminationDate  - (packDuration * 1000)
                centerEndDate = moment(packEndDateMs).startOf("day").valueOf()
            }
        }

        const minStartDate = moment(earliestStartDate).format("YYYY-MM-DD")
        const maxStartDate = centerEndDate ? moment(centerEndDate).format("YYYY-MM-DD") : null

        return {
            minStartDate,
            maxStartDate
        }
    }

}

export default PlayUtil
