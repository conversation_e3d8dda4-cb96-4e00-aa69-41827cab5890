import * as _ from "lodash"
import * as momentTz from "moment-timezone"
import { CdnUtil, eternalPromise, TimeUtil, Timezone } from "@curefit/util-common"
import { Segment } from "@curefit/segment-common"
import { ActionUtil as BaseUtils, ActionUtilV1, PLAYER_THEME, SeoUrlParams } from "@curefit/base-utils"
import {
    CycleProgress,
    DigitalCatalogueEntryV1,
    DIYFitnessProductExtended,
    DIYPack,
    DIYPackFulfilment,
    DIYProduct,
    DIYRecipeView,
    DIYSeries,
    DIYUserAttribute,
    DIYUserAttributeState,
    FitnessAssessmentProductTypeEnum,
    FitnessAssessmentProgress,
    FitnessProgramProductResponse,
    ImageData,
    LiveClass,
    LiveClassSlot,
    LiveFitWorkoutFormat,
    OnDemandVideoCategory,
    PreferredStreamType,
    SocialDataResponse,
    StreamData,
    SubscriptionStatus,
    UserScoreMetricsResponse,
    VideoCallMeta,
    VideoStatus
} from "@curefit/diy-common"
import { Membership } from "@curefit/membership-commons"
import { Product, ProductType, UrlPathBuilder } from "@curefit/product-common"
import { ISegmentService, SessionInfo, UserContext, VideoCardItem, } from "@curefit/vm-models"
import { BannerCarouselWidget, MerchantryWidget } from "../page/PageWidgets"
import { Action, CultBuddiesJoiningListLargeView, CultBuddiesJoiningListSmallView } from "../common/views/WidgetView"
import { AllAction, CardListItemRightInfo } from "../page/vm/widgets/card/CardListWidget"
import { Tenant, User } from "@curefit/user-common"
import { HourMin, UserAgentType as UserAgent } from "@curefit/base-common"
import { ClassInviteLinkCreator } from "../cult/invitebuddy/ClassInviteLinkCreator"
import AppUtil, { CUREFIT_WHITE_LOGO, ONBOARDING_FORM_V2_SUPPORTED_VERSION } from "./AppUtil"
import { CacheHelper } from "./CacheHelper"
import {
    BuddiesInviteJoiningListWidgetV2,
    Buddy,
    BuddyWithOverlay,
    Label,
    LeaguePostBookingInfoWidget,
    LeagueRequestStatusSectionListWidget,
    PageTypes,
    ProfileTag,
    RouteNames,
    UserProfileDetailWidget,
    WorkoutMember
} from "@curefit/apps-common"
import CultUtil, { BLUR_PROFILE_IMAGES } from "./CultUtil"
import { WODLiveCardItem } from "@curefit/vm-models/dist/src/models/widgets/component/WODLiveCardItem"
import DigitalReportViewBuilder from "../digital/DigitalReportViewBuilder"
import { IDIYFulfilmentService } from "@curefit/diy-client"
import ActionUtil from "./ActionUtil"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import {
    CommunitiesResponse,
    CommunityRecommendationSource,
    CommunityType,
    CommunityUserInviteRequest,
    ProfileAttributeEntryCode,
    ProfileVisibility,
    UserProfileEntry
} from "@curefit/social-common"
import { TAG_COLORS } from "../cult/cultSocial/CultUserProfileViewBuilder"
import DigitalLeagueSectionViewBuilder, {
    CLP_RECOMMENDATION_LIMIT,
    CommonData,
    DUMMY_USER_IMAGE,
    LEAGUE_LIST_MEMBER_LIMIT,
    LeagueListSection,
    LeagueSectionSource,
    PendingInvitesSection,
    UserMappingStateToastMessages
} from "../digital/DigitalLeagueSectionViewBuilder"
import { ISocialService } from "@curefit/social-client"
import { ILogger, Logger } from "@curefit/base"
import { ActionType as VMActionType } from "@curefit/vm-common"
import { AnnouncementBusiness } from "../announcement/AnnouncementBusiness"
import { AdvertisementUtil } from "./AdvertisementUtil"
import * as querystring from "querystring"
import DigitalSocialLeagueTabPageViewBuilder, {
    ChallengeIdVsChallenge,
    IdVsValueMap,
    LEAGUE_LEADERBOARD_ROW_ELEMENTS_LIMIT,
    LEAGUE_LEADERBOARD_SOURCE
} from "../digital/DigitalSocialLeagueTabPageViewBuilder"
import { ChallengeCache } from "@curefit/riddler-cache"
import { IRiddlerService, RiddlerCacheService } from "@curefit/riddler-client"
import { LivePackUtil } from "./LivePackUtil"
import { ICatalogueService } from "@curefit/catalog-client"
import { IBreadCrumb } from "../page/ondemand/OnDemandCommon"
import * as express from "express"
import CFAPIJavaService from "../CFAPIJavaService"
import { HamletBusiness } from "@curefit/hamlet-node-sdk"
import { Badge, BadgeSubType } from "@curefit/quest-common"
import { UserFormRequest } from "@curefit/cfs-common"
import moment = require("moment-timezone")

const urlParse = require("url-parse")
const crypto = require("crypto")

export type LIVE_SESSION_ACTION_SOURCE = "clp_banner_cta" | "calendar_cta" | "session_detail_page_cta" | "upcoming_widget_cta" | "class_booking_slot" | "live_class_recommendation_widget" | "live_session_page_three_dot_menu" | "my_live_classes_widget" | "league_wall" | "recipe_pack_widget" | "on_demand_eat_session" | "on_demand_pop_session"
    | "recipe_list_widget" | "diy_series_detail_widget" | "diy_page_action" | "diy_page_session_item" | "diy_pack_widget_clp" | "eat_recipe_search_suggestion" | "eat_live_clp" | "eat_recipe_clp" | "diy_wod" | "diy_mod" | "live_membership_widget" | "days_remaining_widget" | "diy_favorite_pack_widget" | "now_live_widget" | "todays_widget" | "eatlive_search"
    | "digital_report" | "find_your_first_class" | "user_journey_modal" | "tl_live_class" | "diy_pack_widget"
export type LiveFitMasterClassFormat = "MASTER CLASS"
export type LiveFitInteractiveFormat = "LIVE INTERACTIVE"

export type LIVE_CLASS_REPORT_SOURCE = "class_moments_page"
export type LIVE_CLASS_REPORT_PREV_PAGE = "videoplayer"
const BUDDIES_JOINING_SMALL_LIST_LIMIT = 3
const BUDDIES_JOINING_SEPARATED_ICONS_LIMIT = 3
export const CULT_LIVE_CAMPAIGN_ID = "cult_live_25_fitcash"
const BUDDIES_JOINING_STATE_LEGEND = [{
    state: "SUBSCRIBED",
    title: "Subscribed",
    color: "#44d7b6"
}]
export const INTERACTIVE_ICON_URL = "image/diy/interactive-session-badge.gif"
type Iteratee = (value: any) => any
const NEW_UNIFIED_CLP_MEMBERS_CONVERTED_AFTER_4_NOV_9_28 = "89e7b8a7-bb10-485e-8752-52acfd1f724b"
const UNIFIED_FITNESS_TAB_MEMBER_SEGMENT = "4844702f-24bf-4c85-bc86-6bf69c4a5341"
const UPDATE_MODAL_PROMPT = "To provide you the best experience, this session is available only on our latest app version. Please update the app now!"


interface GetLeaderboardPreviewInterfaces {
    digitalSocialLeagueTabPageViewBuilder: DigitalSocialLeagueTabPageViewBuilder,
    riddlerService: IRiddlerService,
    riddlerCacheService: RiddlerCacheService,
    challengeCache: ChallengeCache,
    logger: ILogger,
    socialService: ISocialService
}

interface GetClpRequestStatusSectionListParams extends GetLeaderboardPreviewInterfaces {
    productType?: ProductType
    userCache: CacheHelper
    digitalLeagueSectionViewBuilder: DigitalLeagueSectionViewBuilder
}

interface SquadRecommendationSortItem {
    name: string
    source: CommunityRecommendationSource
}

interface ReferralBannerInfo {
    image: string
    url: string
    segment: string
    widgetName: string
}

enum ReferralBannerEnum {
    LIVE = "LIVE",
    CULT = "CULT"
}
interface ReferralBanner {
    [ReferralBannerEnum.LIVE]: ReferralBannerInfo
    [ReferralBannerEnum.CULT]: ReferralBannerInfo
}

const BANNER_INFO: ReferralBanner = {
    [ReferralBannerEnum.LIVE]: {
        image: "/image/banners/referral_live_member.png",
        url: "curefit://giftvoucherpage?campaignId=live_referral_live_members_1609",
        segment: "11258605-cbd4-4ab6-843f-c75cd6a6b8c8",
        widgetName: "referral_live_reportpage_widget"
    },
    [ReferralBannerEnum.CULT]: {
        image: "/image/banners/referral_cult_member.png",
        url: "curefit://giftvoucherpage?campaignId=live_referral_cult_members_1609",
        segment: "e0147403-31c0-4761-8c4c-d088f0f97884",
        widgetName: "referral_cult_reportpage_widget"
    }
}

export const masterClassFormat = "MASTER CLASS"
export const allFormats = "ALL"
export const RECIPE_PRODUCT_TYPE = "RECIPE"
export const liveInteractiveClassFormat = "LIVE INTERACTIVE"

export const LEAGUE_POST_BOOKING_INFO_WIDGET_USER_LIMIT = 3
export const BUDDIES_INVITE_JOINING_LIST_WIDGET_USER_LIMIT = 5

export const poseOfDaySupportedFormats: LiveFitWorkoutFormat[] = ["SNC", "STRENGTH", "CARDIO", "HRX", "DANCE", "YOGA", "BOXING", "PILATES", "HIIT", "DANCE_FIT_JUNIOR", "WALK_FITNESS", "AMA"]

export const squadPageReportAllowedFormats: LiveFitWorkoutFormat[] = ["SNC", "STRENGTH", "CARDIO", "HRX", "DANCE", "YOGA", "BOXING", "PILATES", "HIIT", "DANCE_FIT_JUNIOR", "EQUIPMENT", "WALK_FITNESS", "AMA"]

export const ReportHeaderMessageWorkoutFormats = ["SNC", "STRENGTH", "CARDIO", "HRX", "DANCE", "BOXING", "DANCE_FIT_JUNIOR", "TABATA", "HIIT"]

export const LEAGUE_POST_BOOKING_INFO_TEXT = "We’ve informed your squad members about your booking"


export const LIVE_MEMORY_ASPECT_RATIO = 1.5
export const LIVE_REPORT_V2_ASPECT_RATIO = 335 / 260
export const LIVE_MOMENT_ASPECT_RATIO_1 = 1
export const LIVE_REPORT_V2_ASPECT_RATIO_STR = "335:260"

export const CUREFIT_LIVE = "cure.fit live"
export const CUREFIT_SUBSCRIPTION = "cure.fit subscription"

export const CULT_LIVE = "cultpass HOME"
export const CULT_HOME = "cultpass HOME"
export const CULT_LIVE_MEMBERSHIP = CULT_LIVE + " Membership"

export const CUREFIT_LIVE_TITLE = "image/livefit/app/curefit_live_title.png"
export const CULT_LIVE_TITLE = "image/livefit/app/cultpass_home.svg"
export const CUREFIT_LIVE_TITLE_INTL = "image/livefit/app/curefit_membership_title_intl.png"

// starts atleast 10 mins later
export const FIND_YOUR_FIRST_CLASS_LAST_CLASS_LATER = 60 * 60 * 1000
export const FIND_YOUR_FIRST_CLASS_MILLISECONDS = 10 * 60 * 1000
export const FIND_YOUR_FIRST_CLASS_LAST_CLASS = 7 * 60 * 60 * 1000
export const FIND_YOUR_FIRST_CLASS_LAST_CLASS_NOW = 10 * 60 * 60 * 1000

export const RECOMMENDED_CONTACTS_SECTION_ID = "RECOMMENDED_SECTION"

export const POSE_OF_DAY_MOTHERS_DAY_START_TEXT = [{ text: "Dedicate this\nworkout to your Mom!\n" }, { text: "#HappyMothersDay\n\n", color: "#1ee0ff" }, { text: "Keep your phone away and follow the pose of the day" }]
// url for live pack trial interstitial
const CUREFIT_LIVE_TRIAL_WEB = "/image/livefit/app/web_interstitial_banner.png"
const CUREFIT_LIVE_TRIAL_IOS = "/image/livefit/app/trial_interstitial.png"
const CUREFIT_LIVE_TRIAL_OTHERS = "/image/livefit/app/trial_interstitial.png"

export const LIVE_FREE_TRIAL_EXPIRED_SEGMENT = "cflive-free-trial-expired"
export const LIVE_FREE_TRIAL_ONGOING_SEGMENT = "cflive-free-trial-started"

export const FEEDBACK_USER_FORM_AB_TEST_USER_FORM_ID = "Feedback_form_Exp"

export const DEFAULT_PREFERRED_NUM_WORKOUT_DAYS = 3
export const SUNDAY_WEEKDAY_NUM = 7

export const LIVE_DISCOVERY_TABS_WIDGET_WEB_PROD = "90e9d42d-4455-44d0-a1dc-5b1952c285bb"

const ZOOM_APP_KEY = "WBipROKkgV8VN3MmjUNCFbOEVIS6yiPr0saF"
const ZOOM_APP_SECRET = "43SNny66877nWFT1xB3UPkWjpQOgEnAs3TcS"

export const productTypeLivePackMonetisationCheck: Map<(ProductType | LiveFitWorkoutFormat), boolean> = new Map<(ProductType | LiveFitWorkoutFormat), boolean>()
productTypeLivePackMonetisationCheck.set("DIY_FITNESS", true)
productTypeLivePackMonetisationCheck.set("DIY_MEDITATION", true)
productTypeLivePackMonetisationCheck.set("LIVE_FITNESS", true)
productTypeLivePackMonetisationCheck.set("MIND", true)
productTypeLivePackMonetisationCheck.set("MIND_PODCAST", true)
productTypeLivePackMonetisationCheck.set("SNC", true)
productTypeLivePackMonetisationCheck.set("HRX", true)
productTypeLivePackMonetisationCheck.set("CARDIO", true)
productTypeLivePackMonetisationCheck.set("DANCE", true)
productTypeLivePackMonetisationCheck.set("YOGA", true)
productTypeLivePackMonetisationCheck.set("MEDITATION", true)
productTypeLivePackMonetisationCheck.set("BOXING", true)
productTypeLivePackMonetisationCheck.set("DANCE_FIT_JUNIOR", true)
productTypeLivePackMonetisationCheck.set("BARRE", true)
productTypeLivePackMonetisationCheck.set("PILATES", true)
productTypeLivePackMonetisationCheck.set("HIIT", true)
productTypeLivePackMonetisationCheck.set("RECOVERY", true)
productTypeLivePackMonetisationCheck.set("DIY_FITNESS_PACK", true)
productTypeLivePackMonetisationCheck.set("DIY_MEDITATION_PACK", true)
productTypeLivePackMonetisationCheck.set("WALK_FITNESS", true)
productTypeLivePackMonetisationCheck.set("AMA", true)

const trialImages = [
    {
        startTime: "2020-06-10 23:00:00",
        endTime: "2020-06-19 00:00:00",
        mobileImage: "/image/livefit/app/start_trial_fitstart.png",
        webImage: "/image/livefit/app/web_interstitial_banner.png",
        ar: 0.3
    },
]

export const RENO_CLASS_CONTENT_IDS = [
    "5f30bab1f5f5bf00b3965acb",
    "5f34c829dd727100b333f0ea",
    "5f34c8296160f000e4f7319b",
    "5f34c829955b1d00c2e7c827",
    "5f34c8295d4bcf0091198844",
    "5f34c8298f900200f21f62b1",
    "5f34c89812399a00987d4929",
    "5f34c8987c5e8900fa3eeab1",
    "5f34c89882498e00b30581df",
    "5f34c898d068b600d7e03687",
    "5f34c898abddca00f2ad5880",
    "5f34c898ef29ec00ac9646d6",
    "5f34c898d8af8100d67d2c4a",
    "5f34c898766eb800d7f7c139",
    "5f34c898ef29ec00ac9646d8",
    "5f34c8985d4bcf0091198847",
    "5f34c8984c84da00b39cd35b",
    "5f34c89805ccc4009eba29e1",
    "5f34c899ea515400d0964b59",
    "5f34c89839a05200f9db5141",
    "5f34c898ef29ec00ac9646da",
    "5f34c89812399a00987d492b",
    "5f34c8985d4bcf0091198849",
    "5f34c898c058d800fae606b7",
    "5f34c898e2f81200c18ae110"
]

const CREATE_SQUAD_MODAL_TITLE = "Join the Challenge"

const CREATE_SQUAD_MODAL_SUBTITLE = "To join the challenge you need to create your squad or be a part of your friend’s squad"

export const CONTACTS_SYNC_BANNER_RATIO = 750 / 320

export const SURYA_NAMASKARS = "SURYANAMASKARS"
export const YOGA_BAR_GRAPH_STOPS = [
    {
        value: 10,
        color: "#FD8A8B",
    },
    {
        value: 20,
        color: "#FDC42E",
    },
    {
        value: 30,
        color: "#73DCFF",
    },
]
export const YOGA_BAR_GRAPH_LEGEND = [
    {
        type: 1,
        color: "#FD8A8B",
        value: 10,
        legendText: "Perfect",
    },
    {
        type: 2,
        color: "#FDC42E",
        value: 20,
        legendText: "Very good",
    },
    {
        type: 3,
        color: "#73DCFF",
        value: 30,
        legendText: "Good",
    },
]
export const getLiveReportFilters = (userNameCapitals: string, contentId?: string, tenant: Tenant = Tenant.CUREFIT_APP) => {
    const data: IdVsValueMap<{ text: string, emoji?: string }>[] = [
        {
            KUDOS_USER: {
                text: `KUDOS\n${userNameCapitals}`
            }
        },
        {
            KUDOS_USER_EMOJI: {
                text: `KUDOS\n${userNameCapitals}`,
                emoji: "😁"
            }
        },
        {
            TRAIN_HARD_EMOJI: {
                text: "TRAIN\nHARD",
                emoji: "💥"
            }
        },
        {
            BRAVO_USER_EMOJI: {
                text: `BRAVO\n${userNameCapitals}`,
                emoji: "🤘🏻"
            }
        },
        {
            NO_DAYS_OFF_EMOJI: {
                text: "NO DAYS\nOFF",
                emoji: "👌🏻"
            }
        },
        {
            GIRL_WHO_LIFT_EMOJI: {
                text: "GIRLS WHO\nLIFT",
                emoji: "🏋🏻‍♀️"
            }
        },
        {
            I_FELL_STRONG_EMOJI: {
                text: "I FEEL\nSTRONGER",
                emoji: "💪🏼"
            }
        },
        {
            ZEN_MODE_ON_GIRL_EMOJI: {
                text: "ZEN MODE\nON",
                emoji: "🧘🏻‍♀️"
            }
        },
        {
            ZEN_MODE_ON_BOY_EMOJI: {
                text: "ZEN MODE\nON",
                emoji: "🧘🏻‍♂️"
            }
        },
        {
            NO_EXCUSES_EMOJI: {
                text: "NO EXCUSES",
                emoji: "😎"
            }
        },
        {
            GAINS_ON_MY_MIND_EMOJI: {
                text: "GAINS ON\nMY MIND",
                emoji: "😎"
            }
        },
        {
            I_DIED_ALMOST_EMOJI: {
                text: "I DIED,\nALMOST",
                emoji: "🥺"
            }
        },
        {
            BEAST_MODE_ON_EMOJI: {
                text: "BEAST MODE\nON",
                emoji: "🔥"
            }
        },
        {
            BE_BETTER_EVERYDAY_EMOJI: {
                text: "BE BETTER\nEVERYDAY",
                emoji: "✌🏻"
            }
        },
        {
            YOGI_LIFE_EMOJI: {
                text: "YOGI\nLIFE",
                emoji: "🧘🏻‍♂️"
            }
        },
        {
            CRUNCH_CURL_REPEAT_EMOJI: {
                text: "CRUNCH,\nCURL,\nREPEAT",
                emoji: "⚡️"
            }
        },
        {
            MOTHERS_DAY: {
                text: "FOR YOU\nMOM ",
                emoji: "😊"
            }
        },
    ]
    if (tenant === Tenant.CUREFIT_APP) {
        data.push(
            {
                WE_ARE_CULT_EMOJI: {
                    text: "WE ARE\nCULT",
                    emoji: "🙌🏻"
                }
            },
            {
                WE_ARE_CULT_STAR: {
                    text: "WE ARE\nCULT!",
                    emoji: "🌟"
                }
            }
        )
    }
    if (contentId && RENO_CLASS_CONTENT_IDS.includes(contentId)) {
        data.unshift({
            RENO_MASTERCLASS: {
                text: `I've got the #MovesLikeRemo`,
                emoji: "✌️"
            }
        })
    }
    return data
}

export class LiveUtil {

    static getLiveBranding(userContext: UserContext): string {
        return AppUtil.isInternationalApp(userContext) ? CUREFIT_LIVE : CULT_LIVE
    }

    static getCultLiveBranding(userContext: UserContext): string {
        return CULT_LIVE
    }

    static getCultLiveSubscriptionTitle(userContext: UserContext): string {
        return AppUtil.isInternationalApp(userContext) ? CUREFIT_SUBSCRIPTION : CULT_HOME
    }

    public static getCultLiveTitleImage(userContext: UserContext): string {
        return AppUtil.isInternationalApp(userContext) ? CUREFIT_LIVE_TITLE : CULT_LIVE_TITLE
    }

    static async getReferralBanner(userContext: UserContext, segmentService: ISegmentService) {

        const segment: Segment = await segmentService.doesUserBelongToAnySegment([BANNER_INFO[ReferralBannerEnum.LIVE].segment, BANNER_INFO[ReferralBannerEnum.CULT].segment], userContext)

        if (segment && !AppUtil.isSugarFitOrUltraFitApp(userContext)) {
            const banner = _.find(BANNER_INFO, banner => banner.segment === segment.segmentId)
            const action: Action = {
                "title": "",
                "viewType": "LINK",
                "actionType": "NAVIGATION",
                url: banner.url,
            }

            return {
                ...new BannerCarouselWidget("1008:450", [
                    {
                        id: "referral_banner",
                        image: banner.image,
                        action,
                    },
                ], {
                    showPagination: false,
                    v2: true,
                    alignment: "center",
                    backgroundColor: "",
                    autoScroll: false,
                    enableSnap: false,
                    useShadow: false,
                    roundedCorners: true,
                    noVerticalPadding: true,
                    edgeToEdge: false,
                    interContentSpacing: 0,
                    bannerOriginalWidth: 1008,
                    bannerOriginalHeight: 450,
                    bannerWidth: 1008,
                    bannerHeight: 450,
                }, 1, false, false, false),
                hasDividerBelow: false,
                hasTopPadding: false,
                widgetMetric: {
                    widgetName: banner.widgetName
                }
            }
        }
    }


    static getReferralBannerWidget(userContext: UserContext, contentId?: string, showCTA: boolean = false): BannerCarouselWidget {
        const url = `curefit://giftvoucherpage?campaignId=cult_live_25_fitcash`

        const action: Action = {
            "title": "How it works",
            "viewType": "LINK",
            "actionType": "NAVIGATION",
            url,
            "meta": {
                "title": "How it works"
            }
        }

        return {
            ...new BannerCarouselWidget("1002:351", [
                {
                    id: "banner_refer_info",
                    image: "/image/banners/share_your_achievement_v10.png",
                    action,
                },
            ], {
                showPagination: false,
                v2: true,
                alignment: "center",
                backgroundColor: "",
                autoScroll: false,
                enableSnap: false,
                useShadow: false,
                roundedCorners: true,
                noVerticalPadding: true,
                edgeToEdge: false,
                interContentSpacing: 0,
                bannerOriginalWidth: 1002,
                bannerOriginalHeight: 351,
                bannerWidth: 1002,
                bannerHeight: 351,
            }, 1, false, false, false),
            hasDividerBelow: false,
            hasTopPadding: false
        }
    }

    static getMembershipBannerWidget(userSegments: string[]) {
        const url = `curefit://livefitnessbrowsepage`

        const action: Action = {
            "title": "Explore",
            "actionType": "NAVIGATION",
            url,
        }

        const bannerImage = userSegments.includes(LIVE_FREE_TRIAL_EXPIRED_SEGMENT) ? "image/livefit/app/membership/trial_expired.png" : "/image/livefit/app/membership/explore_membership.png"

        return {
            ...new BannerCarouselWidget("1008:507", [
                {
                    id: "banner_explore_membership",
                    image: bannerImage,
                    action,
                    contentMetric: {
                        widgetName: "report_membership_banner",
                        contentId: "banner_explore_membership",
                        widgetType: "BANNER_CAROUSEL_WIDGET"
                    }
                },
            ], {
                showPagination: false,
                v2: true,
                alignment: "bottom",
                backgroundColor: "",
                autoScroll: false,
                enableSnap: false,
                useShadow: false,
                roundedCorners: true,
                noVerticalPadding: true,
                edgeToEdge: false,
                interContentSpacing: 0,
                bannerOriginalWidth: 1008,
                bannerOriginalHeight: 507,
                bannerWidth: 1008,
                bannerHeight: 507
            }, 1, false, false, false),
            hasDividerBelow: false,
            hasTopPadding: false,
        }
    }

    static isLive(userContext: UserContext, videoResponse: DigitalCatalogueEntryV1) {
        return videoResponse.playerStartTimeEpoch < momentTz.tz(userContext.userProfile.timezone).valueOf()
    }

    static async getStatusBasedSessionAction(user: User, userContext: UserContext, videoResponse: DigitalCatalogueEntryV1, subscribedVideos: string[], sessionInfo: SessionInfo,
        numberOfSessions: number, source: LIVE_SESSION_ACTION_SOURCE, cultPreference: boolean, classInviteLinkCreator: ClassInviteLinkCreator, isLocked: boolean, serviceInterfaces: CFServiceInterfaces, isUserEligibleForLiveClassBooking: boolean,
        isUserEligibleForTrial: boolean, bucketId: string, socialDataResponse?: SocialDataResponse, timezone: Timezone = TimeUtil.IST_TIMEZONE , card?: any, isSubscribed?: boolean, firestoreEnabled?: boolean, announcementBusiness?: AnnouncementBusiness): Promise<Action> {
        const isLive: boolean = videoResponse.playerStartTimeEpoch < momentTz.tz(timezone).valueOf()
        const blockInternationalUser = await AppUtil.blockFitnessContentForInternationalUser(userContext, serviceInterfaces.maxmindService, serviceInterfaces.logger)

        if (!sessionInfo.isUserLoggedIn) {
            return {
                title: isLive ? "JOIN" : "BOOK",
                actionType: "SHOW_LOGIN_MODAL",
                url: "curefit://loginmodal",
            }
        }

        if (LiveUtil.isRealLiveSession(videoResponse.preferredStreamType) && !AppUtil.isRealLiveSessionSupported(userContext)) {
            return ActionUtil.appUpdateAction(userContext, isLive ? "JOIN" : "BOOK", UPDATE_MODAL_PROMPT)
        }

        let action: Action
        if (isLive) {
            action = LiveUtil.getLiveVideoPlayerAction(userContext, user, videoResponse, sessionInfo, source)
        } else {
            action = await LiveUtil.getVideoSubscribeAction(user, userContext, videoResponse, subscribedVideos, sessionInfo, source, cultPreference, classInviteLinkCreator, timezone, socialDataResponse, card, isSubscribed, announcementBusiness)
        }
        return this.getLiveSessionAction(isUserEligibleForLiveClassBooking, isLocked, action, isUserEligibleForTrial, userContext, videoResponse.format, source, bucketId, blockInternationalUser)
    }

    static getLivePackTrialAction(): Action {
        return {
            title: "ACTIVATE FREE TRIAL",
            actionType: "START_LIVE_PACK_TRIAL",
            analyticsData: {
                eventKey: "start_live_pack_trial_clicked",
                eventData: {
                    status: "ACTIVE"    // not sure if this is the correct field
                }
            }
        }
    }

    static async getLiveWODTimeslotActionV2(classId: string, status: VideoStatus, sessionInfo: SessionInfo, source: LIVE_SESSION_ACTION_SOURCE, timezone: Timezone = TimeUtil.IST_TIMEZONE, serviceInterfaces: CFServiceInterfaces, userContext: UserContext, isLocked: boolean, bucketId: string, liveClassId?: string,
        isUserEligibleForLivePackMonetisation?: boolean, isUserEligibleForLivePackTrial?: boolean): Promise<Action> {
        const blockInternationalUser = await AppUtil.blockFitnessContentForInternationalUser(userContext, serviceInterfaces.maxmindService, serviceInterfaces.logger)
        let action: Action
        if (status === "LIVE") {
            // if the user is not logged and session is live, then only show login modal
            if (!sessionInfo.isUserLoggedIn) {
                return {
                    title: "LOGIN",
                    actionType: "SHOW_LOGIN_MODAL",
                    url: "curefit://loginmodal",
                }
            } else {
                const liveSession = await serviceInterfaces.diyService.getDigitalCatalogueEntry(classId)
                const user = await userContext.userPromise
                action = LiveUtil.getLiveVideoPlayerAction(userContext, user, liveSession, sessionInfo, source)
                return this.getLiveSessionAction(isUserEligibleForLivePackMonetisation, isLocked, action, isUserEligibleForLivePackTrial, userContext, liveSession.format, source, bucketId, blockInternationalUser)
            }
        } else {
            // even if the user is not logged in and clicks on future booking slots, then redirect to product detail page
            const liveSession = await serviceInterfaces.diyService.getDigitalCatalogueEntry(classId)
            action = LiveUtil.getLiveSessionDetailActionFromLiveClassId(liveClassId, classId, source, liveSession.contentCategory, userContext)
            return action
        }
    }

    static getLiveSessionDetailActionFromClassId(classId: string, source: LIVE_SESSION_ACTION_SOURCE, title?: string): AllAction {
        const action: AllAction = {
            "actionType": "NAVIGATION",
            "title": _.isEmpty(title) ? "KNOW MORE" : title,
            "url": `curefit://liveclassdetail?bookingNumber=${encodeURIComponent(classId)}&productType=LIVE_FITNESS`
        }
        if (!_.isEmpty(source)) {
            action.url = `${action.url}&pageFrom=${source}`
        }
        return action
    }

    static getLiveSessionDetailActionFromLiveClassId(liveClassId: string, classId: string, source: LIVE_SESSION_ACTION_SOURCE, category: any, userContext: UserContext, title?: string): AllAction {
        const action: AllAction = {
            "actionType": "NAVIGATION",
            "title": _.isEmpty(title) ? "KNOW MORE" : title,
            "url": `curefit://liveclassdetail?liveClassId=${encodeURIComponent(liveClassId)}&bookingNumber=${encodeURIComponent(classId)}&productType=LIVE_FITNESS`
        }
        if (!_.isEmpty(source)) {
            action.url = `${action.url}&pageFrom=${source}`
        }
        if (AppUtil.isWeb(userContext)) {
            const queryParams = ActionUtil.serializeAsQueryParams({ liveClassId: encodeURIComponent(liveClassId), bookingNumber: encodeURIComponent(classId) })
            action.url = `/live/exercise-videos/${category}${queryParams}`
        }
        return action
    }

    static getDIYSessionDetailAction(diyProduct: DIYProduct | FitnessProgramProductResponse, packId: string, source: LIVE_SESSION_ACTION_SOURCE, category: any, userContext: UserContext, title?: string): AllAction {
        const action: AllAction = {
            "actionType": "NAVIGATION",
            "title": _.isEmpty(title) ? "KNOW MORE" : title,
            "url": `curefit://liveclassdetail?liveClassId=${encodeURIComponent(diyProduct.productId)}&bookingNumber=${encodeURIComponent(diyProduct.productId)}&productType=LIVE_FITNESS&isDIY=true&packId=${packId}`
        }
        if (!_.isEmpty(source)) {
            action.url = `${action.url}&pageFrom=${source}`
        }
        if (AppUtil.isWeb(userContext)) {
            const queryParams = ActionUtil.serializeAsQueryParams({ liveClassId: encodeURIComponent(diyProduct.productId), bookingNumber: encodeURIComponent(diyProduct.productId), isDIY: true, packId })
            action.url = `/live/exercise-videos/${category}${queryParams}`
        }
        return action
    }

    static getSessionActionForBlockedInternationalUser(title?: string): Action {
        const action: Action = {
            actionType: "SHOW_ALERT_MODAL",
            title: title,
            meta: {
                title: `Sorry`,
                subTitle: "This video is not available for users outside India.",
                actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
            }
        }
        return action
    }

    static getRightInfoForUpcomingWidget(user: User, context: UserContext, videoResponse: DigitalCatalogueEntryV1, sessionInfo: SessionInfo, source: LIVE_SESSION_ACTION_SOURCE, timezone: Timezone = TimeUtil.IST_TIMEZONE): CardListItemRightInfo {
        const possibleActions: Action[] = []
        const unsubscribeAction = LiveUtil.getVideoUnsubscribeAction(user, videoResponse, sessionInfo, source, timezone)
        if (unsubscribeAction) {
            possibleActions.push(unsubscribeAction)
        }
        const moreAction: Action = {
            icon: "MANAGE",
            actionType: "ACTION_LIST",
            actions: possibleActions
        }
        return {
            action: LiveUtil.getLiveSessionDetailAction(videoResponse, "upcoming_widget_cta", context),
            moreAction: moreAction
        }
    }

    static shouldShowUpdateModal(userContext: UserContext, preferredStreamType: PreferredStreamType): Boolean {
        const { sessionInfo } = userContext
        if (AppUtil.isTVApp(userContext)) {
            return false
        }
        if (AppUtil.isInternationalApp(userContext)) {
            return sessionInfo.clientVersion < 1.4
        }
        if (AppUtil.isSugarFitOrUltraFitApp(userContext)) {
            return false
        }
        if (LiveUtil.isRealLiveSession(preferredStreamType) && !AppUtil.isRealLiveSessionSupported(userContext)) {
            return false
        }

        return sessionInfo.userAgent === "APP" && sessionInfo.clientVersion < 8.45
    }


    static getLiveInteractiveSessionRestApiAction(userContext: UserContext, contentId: String, user: User, videoCallMeta: VideoCallMeta): Action {
        const action: Action = {
            title: "JOIN",
            actionType: "REST_API",
            meta: {
                method: "post",
                url: "/digital/videoCallJoin",
                body: {
                    contentId,
                }
            }
        }
        if (AppUtil.isSugarFitOrUltraFitApp(userContext)) {
            action.analyticsData = {
                tags: videoCallMeta && videoCallMeta.uiTags || [],
            }
        }
        return action
    }

    static getLiveInteractionSessionJoinActionFromUrl(userContext: UserContext, contentId: String, user: User, liveSession: DigitalCatalogueEntryV1): Action {
        const videoCallMeta = liveSession.videoCallMeta
        const isWeb = AppUtil.isWeb(userContext)
        const parsedZoomLink = urlParse(videoCallMeta?.joinUrl, true)
        const pathName = parsedZoomLink.pathname
        const queryParams = parsedZoomLink.query
        const paths = pathName.split("/")
        const meetingId = paths[paths.length - 1]
        const meetingPassword = queryParams.pwd
        // const action: Action = {
        //     actionType: "JOIN_ZOOM_MEETING",
        //     title: "JOIN NOW",
        //     trigger: isWeb,
        //     meta: {
        //         displayName: user.firstName + "" + (user.lastName ? " " + user.lastName : ""),
        //         meetingId,
        //         patientId: user.id,
        //         meetingPassword,
        //         bookingId: undefined,
        //         zoomParticipantId: user.id,
        //         muteOnJoin: true,
        //         externalMeetingId: videoCallMeta?.externalMeetingId,
        //         contentId,
        //         leaveUrlParams: isWeb ? {itemId: contentId, feedbackId: LiveUtil.getLiveVideoCallFeedbackId(contentId, user.id) } : undefined,
        //         skipped: true,
        //         contentType: "LIVE_VIDEO_CALL",
        //         liveSession
        //     }
        // }

        const action: Action = {
            title: "JOIN NOW",
            actionType: "EXTERNAL_DEEP_LINK",
            url: videoCallMeta?.joinUrl,
            trigger: isWeb,
            meta: {
                displayName: user.firstName + "" + (user.lastName ? " " + user.lastName : ""),
                meetingId,
                patientId: user.id,
                meetingPassword,
                bookingId: undefined,
                zoomParticipantId: user.id,
                muteOnJoin: true,
                externalMeetingId: videoCallMeta?.externalMeetingId,
                contentId,
                leaveUrlParams: isWeb ? {itemId: contentId, feedbackId: LiveUtil.getLiveVideoCallFeedbackId(contentId, user.id) } : undefined,
                skipped: true,
                contentType: "LIVE_VIDEO_CALL",
                liveSession,
                deepLinkType: "ZOOM",
                deeplinkType: "ZOOM" // since app is using this variable (small L)
            }
        }

        if (!isWeb) {
            // not sending for web
            _.assign(action.meta, {
                appKey: ZOOM_APP_KEY,
                appSecret: ZOOM_APP_SECRET,
            })
        }

        return action
    }

    static getLiveVideoCallFeedbackId(contentId: String, userId: String) {
        return `LIVE_VIDEO_CALL_${contentId}_${userId}`
    }

    static async getVideoSubscribeAction(user: User, userContext: UserContext, videoResponse: DigitalCatalogueEntryV1, subscribedVideos: string[], sessionInfo: SessionInfo, source: LIVE_SESSION_ACTION_SOURCE, cultPreference: boolean, classInviteLinkCreator: ClassInviteLinkCreator, timezone: Timezone, socialDataResponse?: SocialDataResponse, card?: any, isSubscribed?: boolean, announcementBusiness?: AnnouncementBusiness): Promise<Action> {
        if ((subscribedVideos.length && _.includes(subscribedVideos, (<any>videoResponse)._id)) || isSubscribed) {
            if (LiveUtil.shouldShowUpdateModal(userContext, videoResponse.preferredStreamType)) {
                return ActionUtil.appUpdateAction(userContext, source === "upcoming_widget_cta" ? "Cancel" : "CANCEL", UPDATE_MODAL_PROMPT)
            }
            let action = undefined
            const supported = AppUtil.isDynamicInterventionSupported(userContext)
            const announcement = await announcementBusiness.getAnnouncementToShow(userContext, "referral_invite_announcement", { classId: (<any>videoResponse)._id })
            if (supported && announcement) {
                action = {
                    actionType: "SHOW_DYNAMIC_INTERVENTION" as VMActionType,
                    title: "INVITE",
                    meta: {
                        announcementId: "referral_invite_announcement",
                        classId: (<any>videoResponse)._id
                    }
                }
            } else {
                action = LiveUtil.getShareAction(userContext, classInviteLinkCreator, videoResponse, socialDataResponse, card)
            }

            return !LiveUtil.isVanillaLiveFitFormat(videoResponse.format) ? action : undefined
        } else {
            if (LiveUtil.shouldShowUpdateModal(userContext, videoResponse.preferredStreamType)) {
                return ActionUtil.appUpdateAction(userContext, "COUNT ME IN", UPDATE_MODAL_PROMPT)
            }
            const completionActions = AppUtil.isLiveClassBookingPageSupported(userContext) ? [{
                actionType: "BOOK_LIVE_CLASS",
                meta: {
                    classId: (<any>videoResponse)._id,
                    productType: "LIVE_FITNESS"
                }
            }] : cultPreference && AppUtil.isCalendarEventSupportedForLive(sessionInfo, user) ? [
                {
                    actionType: "SHOW_ALERT_MODAL",
                    meta: {
                        title: "You're In!",
                        subTitle: `We'll send you a reminder 30 mins prior to your ${videoResponse?.title || ""} session.`,
                        meta: {
                            modalHeight: 260,
                            preventModalBackdropDismiss: true,
                        },
                        actions: [
                            await LiveUtil.getCreateCalendarEventAction(userContext, videoResponse, classInviteLinkCreator, cultPreference)
                        ],
                    },
                },
            ] : [{
                actionType: "SHOW_ALERT_MODAL",
                meta: {
                    title: "You're In!",
                    subTitle: `We'll send you a reminder 30 mins prior to your ${videoResponse?.title || ""} session.`,
                    meta: {
                        modalHeight: 260,
                        preventModalBackdropDismiss: true,
                    },
                    actions: [
                        {
                            title: "OK",
                            actionType: "HIDE_ALERT_MODAL"
                        },
                    ],
                },
            }]
            const action: Action = {
                title: "BOOK",
                actionType: "VIDEO_SUBSCRIBE",
                meta: {
                    subscriptionType: "VIDEO",
                    catalogueEntryId: (<any>videoResponse)._id,
                    status: "SUBSCRIBED",
                    completionActions: completionActions,
                    noCalendarAddEvent: !AppUtil.isLiveClassBookingPageSupported(userContext) && (source === "session_detail_page_cta" || source === "live_class_recommendation_widget")
                },
                analyticsData: {
                    eventKey: "button_click_event",
                    eventData: {
                        source: source,
                        productType: "LIVE_FITNESS",
                        contentId: (<any>videoResponse)._id,
                        title: videoResponse.title,
                        actionType: "VIDEO_SUBSCRIBE",
                        status: "SUBSCRIBED",
                        format: videoResponse.format
                    }
                },
                shouldRefreshPage: true
            }
            if (AppUtil.isSugarFitOrUltraFitApp(userContext)) {
                action.analyticsData = {
                    tags: videoResponse && videoResponse.tags || [],
                }
            }
            return action
        }
    }

    static async getDIYCalendarEventAction(userContext: UserContext, diyProduct: DIYProduct, classInviteLinkCreator: ClassInviteLinkCreator, startTime?: Date): Promise<Action> {
        const timezone = userContext.userProfile.timezone || TimeUtil.IST_TIMEZONE
        const branchUrl = await classInviteLinkCreator.getDIYClassLink(userContext, diyProduct)
        const startDate = startTime !== undefined ? startTime.toISOString() : "0"
        const endDate = startTime
        if (startTime !== undefined) {
            endDate.setMilliseconds(startTime.getMilliseconds() + diyProduct.duration)
        }
        return {
            title: "OK",
            actionType: "CREATE_CALENDAR_EVENT",
            payload: {
                calendarData: {
                    classId: diyProduct.productId,
                    title: diyProduct.title,
                    description: `Join the session on ${branchUrl}`,
                    startDate,
                    endDate: startTime !== undefined ? endDate.toISOString() : 0,
                    toastText: "Class added to your calendar",
                    duration: diyProduct.duration
                },
                modalData: {
                    header: "INTRODUCING",
                    title: "Add class to your calendar",
                    subTitle: "Get reminders for this and all your future classes so you never miss a class again!",
                    actions: [
                        {
                            title: "ASK ME LATER"
                        },
                        {
                            title: "GIVE PERMISSION"
                        }
                    ]
                }
            }
        }
    }

    static async getCreateCalendarEventAction(userContext: UserContext, videoResponse: DigitalCatalogueEntryV1, classInviteLinkCreator: ClassInviteLinkCreator, cultPreference: boolean): Promise<Action> {
        const timezone = userContext.userProfile.timezone || TimeUtil.IST_TIMEZONE
        const user: User = await userContext.userPromise
        const sessionInfo = userContext.sessionInfo
        if (!(cultPreference && AppUtil.isCalendarEventSupportedForLive(sessionInfo, user))) {
            return undefined
        }
        const branchUrl = await classInviteLinkCreator.getLiveClassLink(userContext, videoResponse)
        return {
            title: "OK",
            actionType: "CREATE_CALENDAR_EVENT",
            payload: {
                calendarData: {
                    classId: (<any>videoResponse)._id,
                    title: videoResponse.title,
                    description: `Join the LIVE class on ${branchUrl}`,
                    startDate: TimeUtil.parseDateFromEpochWithTimezone(timezone, videoResponse.scheduledTimeEpoch).toISOString(),
                    endDate: TimeUtil.parseDateFromEpochWithTimezone(timezone, videoResponse.scheduledTimeEpoch + videoResponse.duration).toISOString(),
                    toastText: "Class added to your calendar"
                },
                modalData: {
                    header: "INTRODUCING",
                    title: "Add class to your calendar",
                    subTitle: "Get reminders for this and all your future classes so you never miss a class again!",
                    actions: [
                        {
                            title: "ASK ME LATER"
                        },
                        {
                            title: "GIVE PERMISSION"
                        }
                    ]
                }
            }
        }
    }

    static getDeleteCalendarEventAction(videoResponse: DigitalCatalogueEntryV1, cultPreference: boolean, sessionInfo: SessionInfo, user: User, timezone: Timezone = TimeUtil.IST_TIMEZONE): Action {
        if (!(cultPreference && AppUtil.isCalendarEventSupportedForLive(sessionInfo, user))) {
            return undefined
        }
        return {
            actionType: "DELETE_CALENDAR_EVENT",
            payload: {
                classId: (<any>videoResponse)._id,
                title: videoResponse.title,
                startDate: TimeUtil.parseDateFromEpochWithTimezone(timezone, videoResponse.scheduledTimeEpoch).toISOString(),
                endDate: TimeUtil.parseDateFromEpochWithTimezone(timezone, videoResponse.scheduledTimeEpoch + videoResponse.duration).toISOString()
            }
        }
    }

    static getVideoUnsubscribeAction(user: User, videoResponse: DigitalCatalogueEntryV1, sessionInfo: SessionInfo, source: LIVE_SESSION_ACTION_SOURCE, timezone: Timezone = TimeUtil.IST_TIMEZONE): Action {
        const completionActions = AppUtil.isCalendarEventSupportedForLive(sessionInfo, user) ? [
            {
                actionType: "DELETE_CALENDAR_EVENT",
                payload: {
                    classId: (<any>videoResponse)._id,
                    title: videoResponse.title,
                    startDate: TimeUtil.parseDateFromEpochWithTimezone(timezone, videoResponse.scheduledTimeEpoch).toISOString(),
                    endDate: TimeUtil.parseDateFromEpochWithTimezone(timezone, videoResponse.scheduledTimeEpoch + videoResponse.duration).toISOString()
                }
            }
        ] : undefined
        if (sessionInfo.userAgent === "DESKTOP" || sessionInfo.userAgent === "MBROWSER") {
            return {
                title: (source === "upcoming_widget_cta" || source === "live_session_page_three_dot_menu") ? "Cancel" : "CANCEL",
                actionType: "VIDEO_SUBSCRIBE",
                disabled: false,
                meta: {
                    subscriptionType: "VIDEO",
                    catalogueEntryId: (<any>videoResponse)._id,
                    status: "UNSUBSCRIBED",
                    completionActions: completionActions
                },
                analyticsData: {
                    eventKey: "button_click_event",
                    eventData: {
                        source: source,
                        productType: "LIVE_FITNESS",
                        contentId: (<any>videoResponse)._id,
                        title: videoResponse.title,
                        actionType: "VIDEO_SUBSCRIBE",
                        status: "UNSUBSCRIBED",
                        format: videoResponse.format
                    }
                },
                shouldRefreshPage: true
            }
        }
        return {
            title: "Cancel Booking",
            actionType: "SHOW_ALERT_MODAL",
            meta: {
                title: "Are you sure you want to cancel?",
                subTitle: "",
                actions: [
                    {
                        actionType: "VIDEO_SUBSCRIBE",
                        meta: {
                            subscriptionType: "VIDEO",
                            catalogueEntryId: (<any>videoResponse)._id,
                            status: "UNSUBSCRIBED",
                            successMessage: "Your class has been cancelled successfully",
                            completionActions: completionActions
                        },
                        icon: "CANCEL",
                        title: "Yes",
                        analyticsData: {
                            eventKey: "button_click_event",
                            eventData: {
                                source: source,
                                productType: "LIVE_FITNESS",
                                contentId: (<any>videoResponse)._id,
                                title: videoResponse.title,
                                actionType: "VIDEO_SUBSCRIBE",
                                status: "UNSUBSCRIBED",
                                format: videoResponse.format
                            }
                        },
                        shouldRefreshPage: true
                    },
                    { actionType: "HIDE_ALERT_MODAL", title: "No" },
                ]
            },
            analyticsData: {
                eventKey: "button_click_event",
                eventData: {
                    source: source,
                    productType: "LIVE_FITNESS",
                    contentId: (<any>videoResponse)._id,
                    title: videoResponse.title,
                    actionType: "SHOW_ALERT_MODAL"
                }
            }
        }
    }

    static getCalendarImage(imageData: ImageData): string {
        if (_.isEmpty(imageData)) {
            return undefined
        }
        return UrlPathBuilder.prefixSlash(imageData.mobileImage)
    }

    static getImage(imageData: ImageData, sessionInfo: SessionInfo, numberOfSessions: number): string {
        if (_.isEmpty(imageData)) {
            return undefined
        }
        if (sessionInfo.userAgent === "DESKTOP") {
            if (numberOfSessions === 1) {
                return UrlPathBuilder.prefixSlash(imageData.webImageLarge)
            } else {
                return UrlPathBuilder.prefixSlash(imageData.webImage)
            }
        } else if (sessionInfo.userAgent === "MBROWSER") {
            return UrlPathBuilder.prefixSlash(imageData.mwebImage)
        }
        return UrlPathBuilder.prefixSlash(imageData.mobileImage)
    }

    static getLiveVideoUrl(sessionInfo: SessionInfo, videoData: StreamData[], isVOD: boolean, isInternalUser: boolean, isChromecast: boolean): string {
        let url: URL
        videoData.forEach((data) => {
            if (data.type.toLowerCase() === "hls") {
                url = new URL(data.url)
            }
        })
        if (isVOD) {
            url.host = `live-videos.cure.fit`
        }
        if (url) {
            return url.toString()
        }
        return undefined
    }

    static getLiveVideoPlayerAction(userContext: UserContext, user: User, videoResponse: DigitalCatalogueEntryV1, sessionInfo: SessionInfo, source: LIVE_SESSION_ACTION_SOURCE, buttonType?: string): Action {
        if (videoResponse.status === "ENDED") {
            return {
                title: "SESSION ENDED",
                actionType: "NAVIGATION",
                isEnabled: false,
                disabled: true
            }
        }

        if (LiveUtil.isInteractiveSession(videoResponse?.preferredStreamType)) {
            return LiveUtil.getLiveInteractiveSessionRestApiAction(userContext, videoResponse._id, user, videoResponse.videoCallMeta)
        }

        let action: Action
        if (LiveUtil.shouldShowUpdateModal(userContext, videoResponse.preferredStreamType)) {
            action = ActionUtil.appUpdateAction(userContext, "JOIN", UPDATE_MODAL_PROMPT)
        } else {
            const contentId = (<any>videoResponse)._id
            action = {
                title: "JOIN",
                actionType: "NAVIGATION",
                url: this.videoUrl(contentId),
                analyticsData: {
                    eventKey: "button_click_event",
                    eventData: {
                        source: source,
                        productType: "LIVE_FITNESS",
                        contentId: (<any>videoResponse)._id,
                        title: videoResponse.title,
                        actionType: "LIVE_SESSION_JOIN",
                        buttonType: buttonType,
                        format: videoResponse.format
                    }
                }
            }
        }
        // TODO locked construct
        return action
    }

    /**
     * Here we only pass the contentId and fetch all the other metadata once user enters the player
     * @param contentId
     * @param backgroundImageUrl
     */
    static videoUrl(contentId: string, playerTheme?: PLAYER_THEME): string {
        return `curefit://videoplayer?newAction=true&contentId=${contentId}&playerTheme=${playerTheme}&category=LIVE`
    }

    static getLiveSessionDetailAction(liveSession: DigitalCatalogueEntryV1, source: LIVE_SESSION_ACTION_SOURCE, context: UserContext, classId?: string): AllAction {
        const action: AllAction = {
            "actionType": "NAVIGATION",
            "title": "KNOW MORE",
            "url": `curefit://liveclassdetail?bookingNumber=${encodeURIComponent(classId || (<any>liveSession)._id)}&productType=LIVE_FITNESS&liveClassId=${encodeURIComponent(liveSession.originalContentId ? liveSession.originalContentId : (<any>liveSession)._id)}`
        }
        if (AppUtil.isWeb(context)) {
            action.url = `/live/exercise-videos/${liveSession.contentCategory}?bookingNumber=${encodeURIComponent((<any>liveSession)._id)}&liveClassId=${encodeURIComponent(liveSession.originalContentId ? liveSession.originalContentId : (<any>liveSession)._id)}`
        }
        if (!_.isEmpty(source)) {
            action.url = `${action.url}&pageFrom=${source}`
        }
        return action
    }
    static getNumberOfSessions(user: User, liveVideosResponse: DigitalCatalogueEntryV1[]): number {
        if (user.isInternalUser) {
            return liveVideosResponse.length
        }
        let sessionCount = liveVideosResponse.length
        for (let i = 0; i < liveVideosResponse.length; i++) {
            const videoResponse = liveVideosResponse[i]
            if (_.includes(videoResponse.tags, "TEST_SESSION")) {
                sessionCount--
            }
        }
        return sessionCount
    }

    static async isFirestoreEnabled(diyService: IDIYFulfilmentService, contentId: string): Promise<boolean> {
        const isInSessionAPIEnabled = await diyService.isInSessionAPIEnabled(contentId)
        return isInSessionAPIEnabled ? !isInSessionAPIEnabled.isAPIEnabled : true
    }

    static async getLiveVideoCards(user: User, userContext: UserContext, liveVideosResponse: LiveClass[], isCalendarView: boolean, cultCalendarPreference: boolean, services: CFServiceInterfaces): Promise<VideoCardItem[]> {
        const { userProfile, sessionInfo } = userContext
        const sessionCount = LiveUtil.getNumberOfSessions(user, liveVideosResponse)
        const diyService = services.diyService
        const classInviteLinkCreator = services.classInviteLinkCreator

        const videoCards = []
        const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(services.cultBusiness, services.diyService, userContext)
        const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, services.hamletBusiness)
        for (let i = 0; i < liveVideosResponse.length; i++) {
            const videoResponse = liveVideosResponse[i]
            if (_.includes(videoResponse.tags, "TEST_SESSION") && !user.isInternalUser || !videoResponse.slots.length) {
                continue
            }
            const slot = videoResponse.slots[0]
            const liveSession = await diyService.getDigitalCatalogueEntry(slot.classId)
            const durationHourMin: HourMin = TimeUtil.convertDurationSecondsToHourMin(liveSession.duration / 1000)
            let formattedTimeString: string = durationHourMin.min > 0 ? durationHourMin.min + " Min" : ""
            if (durationHourMin.hour > 0) {
                formattedTimeString = durationHourMin.hour + " Hr " + formattedTimeString
            }
            const isSubscribed = slot.subscriptionStatus === "SUBSCRIBED"
            const cardSource = isCalendarView ? "calendar_cta" : "clp_banner_cta"
            // const firestoreEnabled = await this.isFirestoreEnabled(diyService, slot.classId)
            const buttonAction = (await eternalPromise(LiveUtil.getStatusBasedSessionAction(user, userContext, liveSession, [], sessionInfo, sessionCount, cardSource, cultCalendarPreference, classInviteLinkCreator, slot.locked, services, isUserEligibleForMonetisation, isUserEligibleForTrial, bucketId, null, userProfile.timezone, null, isSubscribed, false))).obj
            const videoDate: Date = TimeUtil.parseDateFromEpochWithTimezone(userProfile.timezone, liveSession.scheduledTimeEpoch)
            const date = TimeUtil.formatDateInTimeZone(userProfile.timezone, videoDate)
            const timeHourMinString = TimeUtil.formatEpochInTimeZone(userProfile.timezone, liveSession.scheduledTimeEpoch, "h:mm A") + " " + TimeUtil.getDayText(date, userProfile.timezone)

            const cardItem: any = {
                image: LiveUtil.getImage(liveSession.bannerImages, sessionInfo, sessionCount),
                title: liveSession.title,
                subTitle: `by ${liveSession.trainerName} | ${formattedTimeString} `,
                isRetelecast: false, // videoResponse.isRetelecast,
                footer: {
                    icon: "LIVE_STREAM",
                    startTimeEpoch: liveSession.scheduledTimeEpoch,
                    duration: liveSession.duration,
                    status: liveSession.status,
                    formattedTime: timeHourMinString
                },
                durationHourMin: formattedTimeString,
                trainerName: liveSession.trainerName,
                playerStartTime: liveSession.playerStartTimeEpoch,
                cardAction: LiveUtil.getLiveSessionDetailAction(liveSession, cardSource, userContext),
                action: buttonAction,
                contentMetric: {
                    contentId: (<any>videoResponse)._id
                },
                isSubscribed: slot.subscriptionStatus === "SUBSCRIBED",
            }
            if (isCalendarView) {
                cardItem.image = LiveUtil.getCalendarImage(liveSession.calendarImages)
            }
            videoCards.push(cardItem)
        }
        return videoCards
    }

    static async getLiveWODCards(user: User, userContext: UserContext, liveVideosResponse: LiveClass[], bookingPageActionUrl: string, services: CFServiceInterfaces): Promise<WODLiveCardItem[]> {
        const { sessionInfo } = userContext
        const sessionCount = LiveUtil.getNumberOfSessions(user, liveVideosResponse)
        const timezone = userContext.userProfile.timezone
        const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(services.cultBusiness, services.diyService, userContext)
        const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, services.hamletBusiness)
        const isInternationalApp = AppUtil.isInternationalApp(userContext)
        const wodLiveCards: WODLiveCardItem[] = await Promise.all(liveVideosResponse.map(async session => {
            if (_.includes(session.tags, "TEST_SESSION") && !user.isInternalUser) {
                return
            }
            const durationHourMin: HourMin = TimeUtil.convertDurationSecondsToHourMin(session.duration / 1000)
            let formattedTimeString: string = durationHourMin.min > 0 ? durationHourMin.min + " Min" : ""
            if (durationHourMin.hour > 0) {
                formattedTimeString = durationHourMin.hour + " Hr " + formattedTimeString
            }
            let subTitle = `by ${session.trainerName}`
            subTitle += session.intensityLevel && !(session.format === "MIND_PODCAST") && !LiveUtil.isVanillaLiveFitFormat(session.format) ? ` | ${session.intensityLevel}` : ""
            const wodCard: any = {
                image: LiveUtil.getImage(session.bannerImages, sessionInfo, sessionCount),
                title: session.title,
                subtitle: subTitle,
                duration: formattedTimeString,
                timeslots: [],
                isMasterClass: session.isMasterClass,
                isLocked: isInternationalApp && AppUtil.isLiveContentLocked(userContext, session.slots[0].locked, isUserEligibleForMonetisation, isUserEligibleForTrial),
                refreshCardEpoch: session.slots[0].scheduledTimeEpoch,
                cardAction: await LiveUtil.getLiveWODTimeslotActionV2(session.slots[0].classId, "PREPARED", sessionInfo, "clp_banner_cta", timezone, services, userContext, session.slots[0].locked, bucketId, session.liveClassId, isUserEligibleForMonetisation, isUserEligibleForTrial)
            }
            const NUM_SLOTS = 2
            const slots = session.slots.slice(0, NUM_SLOTS)
            wodCard.timeslots = await Promise.all(slots.map(async slot => {
                let action: Action = null
                if (AppUtil.isNewLiveClassProductPageSupported(userContext)) {
                    action = await LiveUtil.getLiveWODTimeslotActionV2(slot.classId, slot.status, sessionInfo, "clp_banner_cta", timezone, services, userContext, slot.locked, bucketId, session.liveClassId, isUserEligibleForMonetisation, isUserEligibleForTrial)
                } else {
                    action = await LiveUtil.getLiveWODTimeslotActionV2(slot.classId, slot.status, sessionInfo, "clp_banner_cta", timezone, services, userContext, slot.locked, bucketId , undefined, isUserEligibleForMonetisation, isUserEligibleForTrial)
                }
                return {
                    title: momentTz(slot.scheduledTimeEpoch).tz(timezone).format("hh:mm A"),
                    isLocked: isInternationalApp && AppUtil.isLiveContentLocked(userContext, slot.locked, isUserEligibleForMonetisation, isUserEligibleForTrial),
                    action: action,
                    isScheduled: slot.subscriptionStatus === "SUBSCRIBED",
                    isLive: slot.status === "LIVE"
                }
            }))
            if (session.slots.length > NUM_SLOTS) {
                if (AppUtil.isNewLiveClassProductPageSupported(userContext)) {
                    const action = await LiveUtil.getLiveWODTimeslotActionV2(session.slots[NUM_SLOTS].classId, session.slots[NUM_SLOTS].status, sessionInfo, "clp_banner_cta", timezone, services, userContext, session.slots[NUM_SLOTS].locked, bucketId, session.liveClassId, isUserEligibleForMonetisation, isUserEligibleForTrial)
                    wodCard.timeslots.push({
                        title: "MORE",
                        action: action
                    })
                } else {
                    wodCard.timeslots.push({
                        title: "MORE",
                        action: {
                            actionType: "NAVIGATION",
                            url: bookingPageActionUrl
                        }
                    })
                }

            }
            return wodCard.timeslots.length > 0 ? wodCard : undefined
        }))

        return wodLiveCards.filter(card => card)
    }

    static getDeepLinkFromCardDetails(card: any): string {
        let link = _.get(card, "dynamicLink.applink")

        if (!link) {
            return
        }

        const sourceRegx = /source=.*?(&|$)/
        const redirectionUrlRegx = /redirectUrl=.*?(&|$)/

        if (link.match(redirectionUrlRegx)) {
            link = `${link.replace(redirectionUrlRegx, `redirectUrl=${encodeURIComponent(card.redirectionUrl)}&`)}`
        } else {
            link += `&redirectUrl=${encodeURIComponent(card.redirectionUrl)}`
        }

        return `${link.replace(sourceRegx, `source=${card.source}&`)}`
    }

    static getShareText(source: string, className: string, classStartDate: string, classStartTime: string, shareUrl: string, message: string): string {
        switch (source) {
            case "order-confirmation":
            case "live-upcoming-widget":
            case "class-details-invite":
                return `Hey! Join me for a *${className} session on ${classStartDate} at ${classStartTime}* on the cult.fit app. Let's join this LIVE session together from home on ${shareUrl}`
            case "live-class-report":
                return `I just finished a Dance Fitness LIVE class and I feel awesome! You too can workout from home for Free on the cult.fit app *${shareUrl}*`
            default: return message
        }
    }

    static async getShareAction(userContext: UserContext, classInviteLinkCreator: ClassInviteLinkCreator, liveSession: DigitalCatalogueEntryV1, socialDataResponse?: SocialDataResponse, card?: any): Promise<Action> {

        let shareUrl = ""
        if (AppUtil.isLiveLazyInviteLinkActionSupported(userContext)) {
            return LiveUtil.getInviteBuddyLazyLoadAction((<any>liveSession)._id, card)
        }
        if (!shareUrl && !_.isNil(socialDataResponse) && !_.isEmpty(socialDataResponse.classLink) && !AppUtil.isSugarFitOrUltraFitApp(userContext)) {
            shareUrl = socialDataResponse.classLink
        } if (!shareUrl) {
            shareUrl = await classInviteLinkCreator.getLiveClassInviteLink(userContext, (<any>liveSession)._id, liveSession.title, liveSession.originalContentId)
        }
        if (shareUrl) {
            return LiveUtil.buildShareActionFromShareLink(userContext, liveSession, shareUrl, card)
        }
    }

    static buildShareActionFromShareLink(userContext: UserContext, liveSession: DigitalCatalogueEntryV1, shareUrl: string, card?: any): Action {
        const tz = userContext.userProfile.timezone
        const className = liveSession.title
        const classStartDate = `${TimeUtil.formatEpochInTimeZone(tz, liveSession.scheduledTimeEpoch, "MMM D")}`
        const classStartTime = `${TimeUtil.formatEpochInTimeZone(tz, liveSession.scheduledTimeEpoch, "h:mm a")}`
        const appName = AppUtil.isSugarFitOrUltraFitApp(userContext) ? "sugar.fit" : "cult.fit"
        let message = `Hey! Join me for ${className} on ${classStartDate} at ${classStartTime} on the ${appName} app. Book your spot for this ${LiveUtil.getCultLiveBranding(userContext)} class and let's workout together on ${shareUrl}.`
        if (card) {
            message = LiveUtil.getShareText(card.source, className, classStartDate, classStartTime, shareUrl, message)
        }
        if (AppUtil.isSugarFitApp(userContext) && !_.isEmpty(liveSession.tags)) {
            return {
                title: "Yet to start",
                actionType: "SHARE_ACTION",
                isEnabled: false,
                disabled: true,
                meta: {
                    isEnabled: false,
                    disabled: true,
                },
            }
        }
        return {
            title: "INVITE",
            actionType: "SHARE_ACTION",
            meta: {
                shareOptions: {
                    type: "image/png",
                    message,
                    title: "Invite your buddy"
                },
                shareChannel: "WHATSAPP",
                analyticsData: {
                    type: "LIVE_CLASS_INVITE",
                    source: card && card.source || "live-upcoming-widget"
                }
            },
        }
    }

    static getBuddiesJoiningText(attendingUsers: { userId: string, status?: SubscriptionStatus }[], attendingUsersData: User[], isSubscribed: boolean, appendableText?: string, maxBuddies?: number): {
        infoText: string,
        showOverlappingIcons: boolean
    } {
        let infoText
        const numberOfBuddiesJoining = attendingUsers.length
        const buddyName = attendingUsersData[0].firstName
        if (numberOfBuddiesJoining === 1) {
            infoText = `${buddyName} ${appendableText ? appendableText : isSubscribed ? "is joining you" : "will miss you"}`
        }
        else if (numberOfBuddiesJoining === 2) {
            infoText = `${buddyName} +${numberOfBuddiesJoining - 1} ${appendableText ? appendableText : isSubscribed ? "buddy joining you" : "other will miss you"}`
        }
        else {
            infoText = `${buddyName} +${numberOfBuddiesJoining - 1} ${appendableText ? appendableText : isSubscribed ? "buddies joining you" : "others will miss you"}`
        }
        const showOverlappingIcons = numberOfBuddiesJoining >= (maxBuddies ? maxBuddies : BUDDIES_JOINING_SEPARATED_ICONS_LIMIT)
        return {
            infoText,
            showOverlappingIcons
        }
    }

    static async getBuddiesJoiningListSmallView(attendingUsers: { userId: string, status?: SubscriptionStatus }[], userCache: CacheHelper, pageType: string, isSubscribed: boolean, appendableText?: string, maxBuddies?: number): Promise<CultBuddiesJoiningListSmallView> {
        /* Only getting the userData for users which will be shown */
        const attendingUserDataPromises = attendingUsers.slice(0, BUDDIES_JOINING_SMALL_LIST_LIMIT).map(async (item) => userCache.getUser(String(item.userId)))
        const attendingUsersData = await Promise.all(attendingUserDataPromises)
        const buddies = attendingUsers.slice(0, BUDDIES_JOINING_SMALL_LIST_LIMIT).map((item, index) => {
            const buddy = attendingUsersData[index]
            return {
                icon: buddy.profilePictureUrl,
                name: buddy.firstName,
                state: item.status
            }
        })
        const { infoText, showOverlappingIcons } = LiveUtil.getBuddiesJoiningText(attendingUsers, attendingUsersData, isSubscribed, appendableText, maxBuddies)

        let sceneStyle
        if (pageType === PageTypes.LiveClassDetail || pageType === PageTypes.CultDIYPack) {
            sceneStyle = { paddingHorizontal: 25 }
        }

        return {
            buddies,
            infoText,
            showOverlappingIcons,
            legendData: BUDDIES_JOINING_STATE_LEGEND,
            sceneStyle
        }
    }

    static async getBuddiesJoiningListLargeView(attendingUsers: { userId: string, status: SubscriptionStatus }[], userCache: CacheHelper, pageType: string): Promise<CultBuddiesJoiningListLargeView> {
        const attendingUserDataPromises = attendingUsers.map(async (item) => userCache.getUser(item.userId))
        const attendingUserData = await Promise.all(attendingUserDataPromises)
        const buddies = attendingUsers.map((item, index) => {
            const buddy = attendingUserData[index]
            return {
                icon: buddy.profilePictureUrl,
                name: buddy.firstName,
                state: item.status
            }
        })

        return {
            legendData: BUDDIES_JOINING_STATE_LEGEND,
            buddies,
            title: "Buddies joining you"
        }
    }

    static getReportCarouselItem(userContext: UserContext, session: DigitalCatalogueEntryV1 | DIYFitnessProductExtended, contentId: string, userScoreMetrics: UserScoreMetricsResponse, userData: User) {
        const isEnergyMeterSupported = session.features?.includes("ENERGY") && !_.isNil(userScoreMetrics.energyMeterState) && userScoreMetrics.energyMeterState !== "DEVICE_UNSUPPORTED"
        const shouldShowEnergyInfolet = (!_.isNil(userScoreMetrics?.score) || isEnergyMeterSupported) && !DigitalReportViewBuilder.shouldOmitReportMetric(session, "ENERGY")
        const recommendEnergyMeter = (isEnergyMeterSupported && _.isNil(userScoreMetrics.score))

        const formattedTime = DigitalReportViewBuilder.millisToMinutesAndSeconds(userScoreMetrics.playbackMillis)

        const workoutDurationInfolet = {
            fancyTextHeader: ((<DigitalCatalogueEntryV1>session).format === "MEDITATION" || (<DIYProduct>session).productType === "DIY_MEDITATION") ? "Meditation\nDuration" : "Workout\nDuration",
            data: `${formattedTime}`,
            units: "Min"
        }

        const energyScoreInfolet = {
            fancyTextHeader: "Energy\nScore",
            data: recommendEnergyMeter ? "-" : `${Math.round(userScoreMetrics.score)}`,
            units: "",
            headerColor: "#f1506e",
            [recommendEnergyMeter ? "dataColor" : undefined]: "#949494"
        }

        const pulse_metrics = [workoutDurationInfolet]
        if (shouldShowEnergyInfolet) {
            pulse_metrics.push(energyScoreInfolet)
        }

        const shareUrl = AppUtil.isInternationalApp(userContext) ? "https://cfintl.app.link" : "https://cure.app.link/RAbQSTjzFkb"

        const pulseSharedCardWidget = {
            gradientBg: true,
            pulse_metrics: pulse_metrics,
            user: {
                userId: userContext.userProfile.userId,
                firstName: userData.firstName,
                profilePictureUrl: userData.profilePictureUrl,
                score: userScoreMetrics.score
            },
            ["share_button"]: {
                text: "SHARE YOUR ACHIEVEMENT",
                iconUri:
                    CdnUtil.getCdnUrl("curefit-content/image/pulse/share-icon.png"),
                action: {
                    actionType: "SHARE_SCREENSHOT",
                    meta: {
                        shareTitle: `LIVE Class Report`,
                        shareMessage: `Just completed a ${session.title} LIVE class on the cult.fit app! You too can workout from home on ${shareUrl}!`
                    }
                }
            },
            removeMargins: true,
            removeTopBorderRadius: true,
            noShadow: true,
            extraDividerMargin: 7
        }
        return pulseSharedCardWidget

    }

    static getRandomWorkoutMembers(size: number, action: Action): WorkoutMember[] {
        const randomWorkoutMembers = []
        for (let i = 0; i < size; i++) {
            const name = String.fromCharCode(65 + Math.floor(Math.random() * 26)) + "****"
            const profileImage = BLUR_PROFILE_IMAGES[Math.floor(Math.random() * 3)]
            randomWorkoutMembers.push({
                name,
                profileImage,
                isProfilePublic: false,
                action
            })
        }
        return randomWorkoutMembers
    }

    static getFormattedTimeString(duration: number) {
        const durationHourMin: HourMin = TimeUtil.convertDurationSecondsToHourMin(duration / 1000)
        let formattedTimeString: string = durationHourMin.min > 0 ? durationHourMin.min + " Min" : ""
        if (durationHourMin.hour > 0) {
            formattedTimeString = durationHourMin.hour + " Hr " + formattedTimeString
        }
        return formattedTimeString
    }

    static getInviteBuddyLazyLoadAction(classId: string, card?: any, title = "INVITE", analyticsData: any = undefined): Action {
        return {
            title,
            actionType: "REST_API",
            showLoadingIndicator: true,
            meta: {
                method: "post",
                url: `/digital/inviteLink`,
                body: {
                    contentId: classId,
                    card
                },
            },
            analyticsData: analyticsData
        }
    }

    static getSendZoomLinkAction(liveClass: LiveClass, title = "INVITE", source: string): Action {
        return {
            title: "Send Zoom Link",
            actionType: "SHARE_ACTION",
            meta: {
                shareOptions: {
                    type: "image/png",
                    message: "" + liveClass.videoCallMeta?.joinUrl,
                    title: "Share Zoom Link"
                },
                shareChannel: "WHATSAPP",
                analyticsData: {
                    type: "LIVE_CLASS_INVITE",
                    source: source
                }
            },
        }
    }

    static getFormatsArrayForLiveClass(videoResponse: LiveClass) {
        const formats: Array<LiveFitWorkoutFormat | LiveFitMasterClassFormat | LiveFitInteractiveFormat> = [videoResponse.format]
        if (videoResponse.isMasterClass) {
            formats.push(masterClassFormat)
        }
        if (LiveUtil.isConsideredInteractiveSessionForPresentation(videoResponse.preferredStreamType)) {
            formats.push(liveInteractiveClassFormat)
        }
        return formats
    }

    static getLiveClassBookingPageAction(userContext: UserContext, productType: ProductType, pageFrom?: string, user?: User, workoutId?: string, centerId?: string, classId?: string, selectedDate?: string) {
        let selectedUserId
        if (user) {
            selectedUserId = (userContext.userProfile.userId !== user.id) ? user.id : undefined
        }
        let url = LiveUtil.getLiveClassBookingBaseUrl(userContext, productType)
        const obj = { productType, pageFrom, centerId, workoutId, classId, selectedDate, isLiveBookingPage: true, selectedUserId }
        url += ActionUtil.serializeAsQueryParams(obj)
        const action: Action = {
            actionType: "NAVIGATION",
            title: "Book",
            url
        }
        return action
    }

    static getLiveClassBookingBaseUrl(userContext: UserContext, productType: ProductType) {
        if (AppUtil.isNewLiveClassBookingPageSupported(userContext)) {
            return "curefit://liveclassbooking"
        }
        else if (AppUtil.isNewClassBookingSuppoted(userContext)) {
            return "curefit://classbookingv2"
        }
        else if (productType === "FITNESS") {
            return "curefit://classbooking"
        }
        else {
            return "curefit://mindclassbooking"
        }
    }

    static getLiveClassSlotButtonAction(userContext: UserContext, videoResponse: LiveClass, slot: LiveClassSlot, source: LIVE_SESSION_ACTION_SOURCE, isUserEligibleForMonetisation: boolean, isUserEligibleForLiveTrialPack: boolean, user: User, bucketId: string, blockInternationalUser: boolean, buttonType?: string): Action {
        let action: Action
        if (slot.status === "LIVE") {
            action = LiveUtil.getLiveVideoPlayerAction(userContext, user, videoResponse, userContext.sessionInfo, source, buttonType)
        } else if (!userContext.sessionInfo.isUserLoggedIn) {
            action = LiveUtil.getLiveSessionDetailActionFromLiveClassId(videoResponse.liveClassId, slot.classId, source, videoResponse.contentCategory, userContext, "JOIN")
        } else {
            const completionActions = AppUtil.isLiveClassBookingPageSupported(userContext) ? [{
                actionType: "BOOK_LIVE_CLASS",
                meta: {
                    classId: (<any>slot).classId,
                    productType: "LIVE_FITNESS"
                }
            }] : []
            action = {
                title: "BOOK",
                actionType: "VIDEO_SUBSCRIBE",
                meta: {
                    subscriptionType: "VIDEO",
                    catalogueEntryId: (<any>slot).classId,
                    status: "SUBSCRIBED",
                    completionActions: completionActions,
                    noCalendarAddEvent: !AppUtil.isLiveClassBookingPageSupported(userContext) && (source === "session_detail_page_cta" || source === "live_class_recommendation_widget")
                },
                analyticsData: {
                    eventKey: "button_click_event",
                    eventData: {
                        source: source,
                        productType: "LIVE_FITNESS",
                        contentId: (<any>slot).classId,
                        title: videoResponse.title,
                        actionType: "VIDEO_SUBSCRIBE",
                        buttonType: buttonType,
                        status: "SUBSCRIBED",
                        format: videoResponse.format
                    }
                },
                shouldRefreshPage: true
            }
        }
        return this.getLiveSessionAction(isUserEligibleForMonetisation, slot.locked, action, isUserEligibleForLiveTrialPack, userContext, videoResponse.format, source, bucketId, blockInternationalUser)
    }

    public static isVanillaLiveFitFormat(format: (LiveFitWorkoutFormat | LiveFitMasterClassFormat | LiveFitInteractiveFormat)): boolean {
        const vanillaFormats: (LiveFitWorkoutFormat | LiveFitMasterClassFormat | LiveFitInteractiveFormat)[] = ["EAT", "HOBBY"]
        return vanillaFormats.includes(format)
    }

    public static classFormatTitle(userContext: UserContext, type: ProductType): string {
        switch (type) {
            case "FOOD":
                return "eat.live"
            case "HOBBY":
                return "pop.live"
            default:
                return LiveUtil.getCultLiveBranding(userContext)
        }
    }

    public static getFormatsBasedOnProductType(productType: ProductType): LiveFitWorkoutFormat[] {
        let formats: LiveFitWorkoutFormat[] = []
        if (productType === "MIND") {
            formats = ["YOGA", "MEDITATION", "MIND_PODCAST"]
        } else if (productType === "FITNESS") {
            formats = ["DANCE", "SNC", "YOGA", "CARDIO", "BOXING", "HRX", "STRENGTH", "DANCE_FIT_JUNIOR", "BARRE", "TABATA", "PILATES", "HIIT", "RECOVERY", "EQUIPMENT", "AMA", "WALK_FITNESS"]
        } else if (productType === "DIY_MEDITATION") {
            formats = ["MEDITATION"]
        } else if (productType === "FOOD") {
            formats = ["EAT"]
        } else if (productType === "HOBBY") {
            formats = ["HOBBY"]
        } else if (productType === "MIND_PODCAST") {
            formats = ["MIND_PODCAST"]
        }
        return formats
    }

    public static getFormatName(format: LiveFitWorkoutFormat | LiveFitMasterClassFormat | LiveFitInteractiveFormat): string {
        switch (format) {
            case "MIND_PODCAST":
                return "MIND MAP"
            case "MASTER CLASS":
                return "MASTERCLASS"
            case "SNC":
                return "S&C"
            case "DANCE_FIT_JUNIOR":
                return "DANCE JR."
            case "LIVE INTERACTIVE":
                return "INTERACTIVE"
            default:
                return format
        }
    }

    public static getTagColorForFormat(format: LiveFitWorkoutFormat): string {
        switch (format) {
            case "SNC":
                return "#ffa71d"
            case "STRENGTH":
                return "#9c68e2"
            case "CARDIO":
                return "#68c7e2"
            case "HRX":
                return "#d044ed"
            case "DANCE":
                return "#083cc9"
            case "YOGA":
                return "#c9b208"
            case "MEDITATION":
                return "#08c965"
            case "BOXING":
                return "#c90838"
            case "EAT":
                return "#7b3382"
            case "HOBBY":
                return "#11cc0a"
            case "MIND_PODCAST":
                return "#8f4338"
            default:
                return "#85306a"
        }
    }

    public static getLiveFitMembershipState(userContext: UserContext, membership: Membership, product: Product, isTrialMembership?: boolean) {
        const tz = userContext.userProfile.timezone
        const now = Date.now()
        const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDateWithTimezone(tz), tz)
        const endDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.end)), tz)
        const numDaysToEndFromToday = endDate.diff(today, "days")
        const isIOS = userContext.sessionInfo.osName === "ios"

        if (membership.start > now) {
            return "UPCOMING"
        }

        if (membership.end < now) {
            return "EXPIRED"
        }

        if (numDaysToEndFromToday < 15 && !isTrialMembership && !product?.appleIAPProductDetails?.productId && !isIOS && !product?.androidIAPProductDetails?.productId) {
            return "EXPIRING"
        }

        return "ACTIVE"
    }

    public static getDefaultLivePackUrl(userContext: UserContext): string {
        return AppUtil.isInternationalApp(userContext) ? "image/livefit/app/packs/default.png" : "image/livefit/app/packs/cultpasslive_pack_1.png"
    }

    public static filterInteractiveSessionAndRealLive(liveClasses: LiveClass[]) {
        return liveClasses.filter(liveClass => !LiveUtil.isConsideredInteractiveSessionForPresentation(liveClass?.preferredStreamType))
    }

    public static filterInteractiveSessionOrRealLiveWhenNotSupported<T extends {preferredStreamType?: PreferredStreamType}>(liveClasses: T[], isLiveQnASupported: boolean,  userContext: UserContext) {
        return liveClasses.filter(liveClass => {
            if (LiveUtil.isInteractiveSession(liveClass?.preferredStreamType)) {
                return isLiveQnASupported
            } else if (LiveUtil.isRealLiveSession(liveClass?.preferredStreamType)) {
                return AppUtil.isRealLiveSessionSupported(userContext)
            }
            return true
        })
    }


    public static getIntlLiveSessionAction(userContext: UserContext, isLocked: boolean, activeMembershipAction: any, source: LIVE_SESSION_ACTION_SOURCE): Action {
        if (!userContext.sessionInfo.isUserLoggedIn) {
            return {
                title: activeMembershipAction.title,
                actionType: "SHOW_LOGIN_MODAL",
                url: "curefit://loginmodal",
            }
        }

        if (!AppUtil.isIAPEnabled(userContext) && !AppUtil.isTVApp(userContext)) {
            // return ActionUtil.appUpdateAction(userContext) Will be enabled for forceupdate
            return activeMembershipAction
        }
        if (isLocked) {
            return LiveUtil.getMembershipExpiredModalAction(activeMembershipAction.title, userContext.sessionInfo.osName === "ios", source)
        }
        return activeMembershipAction
    }

    public static getLiveSessionAction(isUserEligibelForMonetisation: boolean, isLocked: boolean, activeMembershipAction: any, isUserEligibleForLivePackTrial: boolean, userContext: UserContext, productFormatType: (LiveFitWorkoutFormat | ProductType), source: LIVE_SESSION_ACTION_SOURCE, bucketId: string, blockInternationalUser: boolean): Action {
        if (AppUtil.isInternationalApp(userContext)) {
            return this.getIntlLiveSessionAction(userContext, isLocked, activeMembershipAction, source)
        }
        if (blockInternationalUser && productFormatType === "DANCE") {
            return LiveUtil.getSessionActionForBlockedInternationalUser(activeMembershipAction.title)
        }

        const isTrialEnabledForProductType = productTypeLivePackMonetisationCheck.has(productFormatType) && productTypeLivePackMonetisationCheck.get(productFormatType)
        if (!isLocked && !(isUserEligibleForLivePackTrial && isTrialEnabledForProductType)) {
            return activeMembershipAction
        }
        if (!userContext.sessionInfo.isUserLoggedIn) {
            return {
                title: activeMembershipAction.title,
                actionType: "SHOW_LOGIN_MODAL",
                url: "curefit://loginmodal",
            }
        }
        if (!isUserEligibelForMonetisation) {
            return activeMembershipAction
        }
        if (!isUserEligibleForLivePackTrial) {
            const isIOS = userContext.sessionInfo.osName && userContext.sessionInfo.osName === "ios" && userContext.sessionInfo?.appVersion < 8.34
            return LiveUtil.getMembershipExpiredModalAction(activeMembershipAction.title, isIOS, source)
        }
        return this.getLivePackTrialStartAction(activeMembershipAction, userContext, source, bucketId)
    }

    static getMembershipExpiredModalAction(title: string, isIOS: boolean, source: LIVE_SESSION_ACTION_SOURCE): Action {
        // if (isIOS) {
        //     return {
        //         actionType: "NAVIGATION",
        //         title,
        //         url: "curefit://livefitnessbrowsepage",
        //         analyticsData: {
        //             eventKey: "page_view",
        //             eventData: {
        //                 pageType: "livefreetrialexpired",
        //                 widgetFrom: source
        //             }
        //         },
        //         meta: {
        //             postAction: "HIDE_MEMBERSHIP_EXPIRED_MODAL",
        //         }
        //     }
        // }
        return {
            actionType: "SHOW_MEMBERSHIP_EXPIRED_MODAL",
            title,
            analyticsData: {
                eventKey: "page_view",
                eventData: {
                    pageType: "livefreetrialexpired",
                    widgetFrom: source
                }
            },
            meta: {
                postAction: "HIDE_MEMBERSHIP_EXPIRED_MODAL",
            }
        }
    }

    public static async isUserEligibleForLivePackTrial(userContext: UserContext, diySerivce: IDIYFulfilmentService): Promise<boolean> {
        const userId = userContext.userProfile.userId
        if (AppUtil.isSugarFitOrUltraFitApp(userContext)) return false
        return await diySerivce.isEligibleForTrial(userId, AppUtil.getTenantFromUserContext(userContext))
    }

    public static getIntlDIYSessionAction(userContext: UserContext, isLocked: boolean, activeMembershipAction: any, source: LIVE_SESSION_ACTION_SOURCE) {
        if (!userContext.sessionInfo.isUserLoggedIn) {
            return {
                title: activeMembershipAction.title,
                actionType: "SHOW_LOGIN_MODAL",
                url: "curefit://loginmodal",
            }
        }
        if (!AppUtil.isIAPEnabled(userContext) && !AppUtil.isTVApp(userContext)) {
            // return ActionUtil.appUpdateAction(userContext) will be enabled for force update
            return activeMembershipAction
        }
        if (isLocked) {
            return LiveUtil.getMembershipExpiredModalAction(activeMembershipAction.title, userContext.sessionInfo.osName === "ios", source)
        }
        return activeMembershipAction
    }

    public static getDIYSessionAction(userContext: UserContext, isUserEligibleForMonetisation: boolean, isLocked: boolean, activeMembershipAction: any, isUserEligibleForLiveTrialPack: boolean, productType: (ProductType | LiveFitWorkoutFormat), source: LIVE_SESSION_ACTION_SOURCE, bucketId: string, blockInternationalUser: boolean, format?: LiveFitWorkoutFormat) {
        if (AppUtil.isInternationalApp(userContext)) {
            return this.getIntlDIYSessionAction(userContext, isLocked, activeMembershipAction, source)
        }

        if (blockInternationalUser && (productType === "DANCE" || (format === "DANCE"))) {
            return LiveUtil.getSessionActionForBlockedInternationalUser(activeMembershipAction.title)
        }

        const isTrialEnabledForProductType = productTypeLivePackMonetisationCheck.has(productType) && productTypeLivePackMonetisationCheck.get(productType)
        if (!isLocked && !(isUserEligibleForLiveTrialPack && isTrialEnabledForProductType)) {
            return activeMembershipAction
        }
        if (!userContext.sessionInfo.isUserLoggedIn) {
            return {
                title: activeMembershipAction.title,
                actionType: "SHOW_LOGIN_MODAL",
                url: "curefit://loginmodal",
            }
        }

        if (!isUserEligibleForMonetisation) {
            return activeMembershipAction
        }
        if (!isUserEligibleForLiveTrialPack) {
            const isIOS = userContext.sessionInfo.osName && userContext.sessionInfo.osName.toLowerCase() === "ios"
            return LiveUtil.getMembershipExpiredModalAction(activeMembershipAction.title, isIOS, source)
        }
        return this.getLivePackTrialStartAction(activeMembershipAction, userContext, source, bucketId)
    }

    public static getDIYDownloadAction(userContext: UserContext, productFormatType: (LiveFitWorkoutFormat | ProductType), blockInternationalUser: boolean): Action {

        if (blockInternationalUser && productFormatType === "DANCE") {
            return LiveUtil.getSessionActionForBlockedInternationalUser(null)
        }

        if (!userContext.sessionInfo.isUserLoggedIn) {
            return {

                actionType: "SHOW_LOGIN_MODAL",
                url: "curefit://loginmodal",
            }
        }
        return {
            actionType: "DIY_DOWNLOAD",
            icon: "DOWNLOAD"
        }
    }

    public static getDIYFavAction(userContext: UserContext, productId: string, productType: ProductType, bookmarked: boolean): Action {
        if (!userContext.sessionInfo.isUserLoggedIn) {
            return {
                actionType: "SHOW_LOGIN_MODAL",
                url: "curefit://loginmodal",
            }
        }
        return {
            actionType: "TOGGLE_FAVOURITE",
            meta: { productId, productType },
            isEnabled: bookmarked
        }
    }

    public static getDIYRecipeAction(item: DIYRecipeView, userAgent: UserAgent): Action {
        const seoParams: SeoUrlParams = {
            productName: item.title
        }
        return {
            actionType: "NAVIGATION",
            url: BaseUtils.recipeHomePage(item.id, seoParams, userAgent)
        }
    }

    public static getLivePackTrialStartAction(postAction: Action, userContext: UserContext, source: LIVE_SESSION_ACTION_SOURCE, bucketId: string): Action {
        const isWebOnly = AppUtil.isWeb(userContext) && !AppUtil.isMWeb(userContext)
        const isIos = userContext.sessionInfo.osName && userContext.sessionInfo.osName === "ios"
        const trialImageUrl = isIos ? CUREFIT_LIVE_TRIAL_IOS : (isWebOnly ? CUREFIT_LIVE_TRIAL_WEB : CUREFIT_LIVE_TRIAL_OTHERS)
        const defaultImageObj = {
            imageUrl: trialImageUrl,
            ar: 0.427
        }
        const trialImageObj = LiveUtil.getTrialInterstitialImage(defaultImageObj)
        if (!trialImageObj?.imageUrl) {
            trialImageObj.imageUrl = isWebOnly ? trialImageObj?.webImage : trialImageObj?.mobileImage
        }

        if (AppUtil.isMandatoryOnboardingExpEnabled(userContext, bucketId)) {
            return {
                actionType: "NAVIGATION",
                title: postAction?.title,
                url: "curefit://fl_form?formId=Home_Guidance_Onboarding_v2&fillPreviousResponse=true&prefillFormId=Home_Guidance_Onboarding&activateTrial=true"
            }
        }

        return {
            actionType: "SHOW_TRIAL_INTERSTITIAL_MODAL",
            title: postAction?.title,
            meta: {
                postAction: postAction,
                titleUrl: LiveUtil.getCultLiveTitleImage(userContext),
                width: 375,
                modalAction: this.getLivePackTrialAction(),
                ...trialImageObj
            },
            analyticsData: {
                eventKey: "page_view",
                eventData: {
                    pageType: "startlivefreetrial",
                    // pageFrom: "source",  // get source here
                    widgetFrom: source,

                }
            }
        }
    }

    public static getProTipWidget(source: string): BannerCarouselWidget {
        return new BannerCarouselWidget("336:110", [{
            id: "pro_tip",
            image: "/image/crosssell/transform_banner_pro_tip.jpg",
            action: {
                actionType: "NAVIGATION",
                url: `curefit://tf_clp?pagefrom=${source}`
            }
        }], {
            bannerWidth: 337,
            bannerHeight: 111,
            noVerticalPadding: false,
            backgroundColor: "white",
            roundedCorners: false
        }, 1, false, false, false)
    }

    public static async generateOnDemandContentVideoNavigationUrl(videoUrlFromBackend: string, videoId: string, category: OnDemandVideoCategory, userContext: UserContext, interfaces: CFServiceInterfaces) {
        const isAutoPlaySupported = await AppUtil.isOnDemandAutoPlaySupported(userContext)
        const isAdvertisementSupported = await AppUtil.isAdvertisementSupported(userContext, interfaces)

        let url = ""
        url = url + `curefit://videoplayer?videoUrl=curefit-content/${videoUrlFromBackend}`
        url = url + `&absoluteVideoUrl=https://cdn-media.cure.fit/${videoUrlFromBackend}`
        url = url + `&consumptionRequired=true`
        url = url + `&contentId=${videoId}`
        url = url + `&activityId=${videoId}`
        url = url + `&activityType=ON_DEMAND_VIDEO`
        url = url + `&subType=${category}`
        url = url + `&onDemandCategory=${category}`
        url = url + `&autoPlayNext=true`

        // const contentCategory: CPASupportedCategories = AdvertisementUtil.getAdvertisementTypeFromContent(category)
        if (isAdvertisementSupported) {
            const adParams = AdvertisementUtil.getAddParams(category as ProductType, videoId)

            const adQueryString = querystring.stringify(adParams)

            url = url + `&${adQueryString}`
        }

        return url
        // return `curefit://videoplayer?videoUrl=curefit-content/${videoUrlFromBackend}&absoluteVideoUrl=http://cdn-media.cure.fit/${videoUrlFromBackend}&consumptionRequired=true&contentId=${videoId}&activityId=${videoId}&activityType=ON_DEMAND_VIDEO&subType=${category}&onDemandCategory=${category}&autoPlayNext=true`
    }

    public static async getUserProfileItem(userContext: UserContext, userCache: CacheHelper, usersProfile: UserProfileEntry[], selectedProfileIndex?: number, userFilter?: string, offset?: number): Promise<{ data: UserProfileDetailWidget[], snapToIndex: number }> {
        const publicUserDetails = await userCache.getUsers(usersProfile.map(user => user.userId.toString()))
        let publicProfilesVisited = 0
        let snapToIndex = 0
        const workedOutUsersPromises: UserProfileDetailWidget[] = []
        for (let index = 0; index < usersProfile.length; index++) {
            const user = usersProfile[index]
            const isUserProfilePublic = user.visibility === ProfileVisibility.PUBLIC
            if (_.isNumber(offset)) {
                if (userFilter === "RANKS" && !isUserProfilePublic) {
                    continue
                }
                publicProfilesVisited++
                const profileIndex = offset + index
                if (selectedProfileIndex === profileIndex) {
                    if (userFilter === "RANKS") {
                        snapToIndex = publicProfilesVisited - 1
                    }
                    else {
                        snapToIndex = index
                    }
                }
            }

            const userDetails = publicUserDetails[user.userId]
            const tagColorsLength = TAG_COLORS.length
            const userTags: ProfileTag[] = []
            if (!_.isEmpty(user.tags)) {
                user.tags.forEach((tag, index) => {
                    if (tag.mapped === true) {
                        userTags.push({
                            title: tag.displayName.toUpperCase(),
                            selectedColors: TAG_COLORS[index % tagColorsLength],
                            isSelected: tag.mapped,
                            id: tag.id
                        })
                    }
                })
                const streak = (await AppUtil.isUserProfilStreakSupported(userContext)) ? CultUtil.getStreak(user.profileAttributes.find((attribute) => {
                    return _.get(attribute, "attribute.code") === ProfileAttributeEntryCode.STREAK
                }), userContext.userProfile.timezone) : undefined
                workedOutUsersPromises.push({
                    widgetType: "USER_PROFILE_DETAIL_WIDGET",
                    imageUrl: userDetails.profilePictureUrl || DUMMY_USER_IMAGE,
                    curefitLogoUrl: CUREFIT_WHITE_LOGO,
                    name: `${userDetails.firstName ? userDetails.firstName : ""} ${userDetails.lastName ? userDetails.lastName : ""}`,
                    attributes: await CultUtil.getAttributes(user.profileAttributes, userContext),
                    tags: userTags,
                    streak,
                    profileIndex: _.isNumber(offset) ? offset + index : undefined
                })
            }
        }
        const workedOutUsers = await Promise.all(workedOutUsersPromises)
        return {
            data: workedOutUsers,
            snapToIndex
        }
    }
    static getMappingStateToastMessages(mappingState: string, isSuccess?: boolean, userId?: string, targetUserId?: string): string {
        let messages
        if (userId && userId === targetUserId && mappingState === "LEFT") {
            messages = UserMappingStateToastMessages.LEAVE
        } else if (mappingState === "LEFT") {
            messages = UserMappingStateToastMessages.REMOVE
        } else {
            messages = UserMappingStateToastMessages[mappingState]
        }
        return isSuccess ? messages?.success : messages?.failed
    }

    static async getLeaguePostBookingInfoWidget(infoText: string, socialService: ISocialService, userContext: UserContext, userCache: CacheHelper, logger: Logger): Promise<LeaguePostBookingInfoWidget | undefined> {
        const buddiesUserIds = await socialService.getAllUsersAcrossCommunities(userContext.userProfile.userId, 0, 50)
        const buddies = await userCache.getUsers(buddiesUserIds)
        const buddiesToShow: LeaguePostBookingInfoWidget["buddies"] = buddiesUserIds.slice(0, LEAGUE_POST_BOOKING_INFO_WIDGET_USER_LIMIT).map(buddyUserId => {
            const buddy = buddies[buddyUserId]
            return {
                icon: buddy.profilePictureUrl || DUMMY_USER_IMAGE,
            }
        })
        if (buddiesUserIds.length > LEAGUE_POST_BOOKING_INFO_WIDGET_USER_LIMIT) {
            buddiesToShow[buddiesToShow.length - 1].overlayText = `+${buddiesUserIds.length - LEAGUE_POST_BOOKING_INFO_WIDGET_USER_LIMIT}`
            buddiesToShow[buddiesToShow.length - 1].isOverlay = true
        }
        if (_.isEmpty(buddiesToShow)) {
            logger.info("empty buddies to show in getLeaguePostBookingInfoWidget", { buddiesUserIds })
            return undefined
        }
        const widget: LeaguePostBookingInfoWidget = {
            widgetType: "LEAGUE_POST_BOOKING_INFO_WIDGET",
            buddies: buddiesToShow,
            infoText,
        }
        if (AppUtil.isAuroraOrderConfirmationSupported(userContext)) {
            widget.containerBackgroundColor = "transparent"
            widget.showCheck = false
        }
        return widget
    }

    static getBuddiesInviteJoiningListWidgetV2(buddies: Buddy[]): BuddiesInviteJoiningListWidgetV2 {
        const buddiesToShow: BuddyWithOverlay[] = buddies.slice(0, BUDDIES_INVITE_JOINING_LIST_WIDGET_USER_LIMIT).map((buddy, index) => {
            let isOverlay = false
            let overlayText
            let name = buddy.name
            if (index === BUDDIES_INVITE_JOINING_LIST_WIDGET_USER_LIMIT - 1 && BUDDIES_INVITE_JOINING_LIST_WIDGET_USER_LIMIT < buddies.length) {
                name = "More"
                overlayText = `+${buddies.length - BUDDIES_INVITE_JOINING_LIST_WIDGET_USER_LIMIT}`
                isOverlay = true
            }
            return {
                ...buddy,
                icon: buddy.icon || DUMMY_USER_IMAGE,
                overlayText,
                isOverlay
            }
        })
        return {
            buddies: buddiesToShow,
            title: "BUDDIES JOINING YOU",
            hasDividerAbove: true,
            widgetType: "BUDDIES_INVITE_JOINING_LIST_WIDGET_V2"
        }
    }

    static async getClpRequestStatusSectionListWidgetView(userContext: UserContext, { productType, userCache, digitalLeagueSectionViewBuilder, socialService, digitalSocialLeagueTabPageViewBuilder, logger, challengeCache, riddlerCacheService, riddlerService }: GetClpRequestStatusSectionListParams, passCallParams?: boolean) {
        const sections = []
        const commonData: CommonData = {}
        const userId = userContext.userProfile.userId
        const socialLeaguesSupported = await AppUtil.isSocialLeaguesSupported(userContext, productType)
        if (!socialLeaguesSupported) {
            return undefined
        }
        const currentUser = await userCache.getUser(userId)
        const leagueListsPromise = digitalLeagueSectionViewBuilder.getLeagueListSection({ currentUser, offset: 0, limit: LEAGUE_LIST_MEMBER_LIMIT, source: LeagueSectionSource.CLP, refreshEnabled: true, commonData })
        const invitesPromise = digitalLeagueSectionViewBuilder.getPendingReceivedInvitationsSection(currentUser, CLP_RECOMMENDATION_LIMIT, 0, LeagueSectionSource.CLP, true)
        const [leaguesList, invites]: [LeagueListSection, PendingInvitesSection] = await Promise.all([leagueListsPromise, invitesPromise])

        const data = []
        if (_.isEmpty(invites?.data) || leaguesList?.userLeagueHasBeenCreatedFE) {
            data.push(...(leaguesList?.data || []).slice(0, 1))
        }
        data.push(...(invites?.data || []).slice(0, 1))
        data.forEach((item) => {
            item.descriptionContainerStyle = { paddingVertical: 5 }
        })
        if (!_.isEmpty(data)) {
            data[data.length - 1].hasDividerBelow = false
        }

        if (!_.isEmpty(leaguesList)) {
            sections.push({ ...leaguesList, data })
        }

        /* since the response will have the same sections not adding the sections to callparams */
        const callParams = [{ source: LeagueSectionSource.CLP, productType: productType }]

        const header = {
            title: "Squads",
            seemore: {
                actionType: "NAVIGATION",
                url: "curefit://leaguewalltabbedpage?selectedTab=ALL_LEAGUES&productType=" + productType,
                title: "VIEW"
            } as Action,
            style: {
                paddingHorizontal: 20,
                marginBottom: 18,
                marginTop: 20,
            },
        }

        const listHeaderImage = "/image/diy/Leagues/League_group.png"

        const userIsMemberOrHasNonEmptyLeague = leaguesList.numOtherCommunitiesJoined > 0 || leaguesList.totalUsersInOwnCommunity > 1

        const listHeaderAction: LeagueRequestStatusSectionListWidget["listHeaderAction"] = {
            actionType: "NAVIGATION",
            url: `curefit://leaguewalltabbedpage?selectedTab=${userIsMemberOrHasNonEmptyLeague ? "WALL" : "ALL_LEAGUES"}&pageFrom=live_class_detail&productType=${productType}`,
            title: userIsMemberOrHasNonEmptyLeague ? "VIEW SQUAD ACTIVITY" : "GET STARTED",
            isCardAction: true
        }

        const leaderboardPreview = await LiveUtil.getLeaderboardPreview(userContext, { challengeCache, digitalSocialLeagueTabPageViewBuilder, logger, riddlerCacheService, riddlerService, socialService }, commonData, invites)

        const hasListHeader = true

        return {
            sections,
            callParams: passCallParams ? callParams as any : undefined,
            /*hasListFooter,
            footerAction,*/
            header,
            listHeaderImage: !leaderboardPreview ? listHeaderImage : undefined,
            leaderboardPreview,
            listHeaderAction,
            hasListHeader,
            numInvites: invites?.data?.length,
            usersHasMembersAcrossCommunities: leaguesList.totalUsersInOwnCommunity > 1 || leaguesList.numOtherCommunitiesJoined > 0,
            showCta: Boolean(listHeaderAction)
        }

    }

    static getBellyBurnBanner() {
        const action = {
            actionType: "NAVIGATION",
            url: `curefit://externalDeepLink?placeUrl=${encodeURIComponent("https://www.facebook.com/groups/bellyburnwithshwe/")}`
        }
        const image = "image/livefit/fb_group_2.png"
        const bannerRatio = "1125:420"
        const bannerDimensions = {
            bannerOriginalWidth: 1125,
            bannerOriginalHeight: 420,
            bannerWidth: 374,
            bannerHeight: 140,
        }
        const bannerId = "belly_burn_facebook_banner"
        return {
        ...new BannerCarouselWidget(bannerRatio, [
            {
                id: bannerId,
                image,
                action: action as any,
            },
        ], {
            showPagination: false,
            v2: true,
            alignment: "center",
            backgroundColor: "",
            autoScroll: false,
            enableSnap: false,
            useShadow: false,
            roundedCorners: true,
            noVerticalPadding: true,
            edgeToEdge: false,
            interContentSpacing: 0,
            ...bannerDimensions,
            containerStyle: {
                marginVertical: 15,
            },
        }, 1, false, false, false),
            contentMetric: { bannerId }
        }
    }

    static async getLeaderboardPreview(userContext: UserContext, interfaces: GetLeaderboardPreviewInterfaces, commonData: CommonData, invites?: PendingInvitesSection) {
        const { digitalSocialLeagueTabPageViewBuilder, riddlerService, riddlerCacheService, challengeCache, logger, socialService } = interfaces
        const userId = userContext.userProfile.userId
        let leaderboardPreview
        if (AppUtil.isSquadLeaderboardPreviewSupported(userContext)) {
            let leaderboardPromise
            if (!_.isEmpty(commonData?.communitiesResponse?.communities)) {
                const challengeIdVsChallenge: ChallengeIdVsChallenge = {}

                const recentEnrolments = (await riddlerCacheService.getRecentEnrolments(userId)).recentEnrolments
                    .filter(enrolment => {
                        if (!challengeIdVsChallenge[enrolment.challengeId]) {
                            challengeIdVsChallenge[enrolment.challengeId] = challengeCache.getChallenge(enrolment.challengeId)
                        }
                        const { hasSquadTag, hasTestTag } = DigitalSocialLeagueTabPageViewBuilder.challengeHasCorrectTagsForSquads(challengeIdVsChallenge[enrolment.challengeId])
                        return enrolment.status !== "FORCE_EXIT" && hasSquadTag && !hasTestTag
                    })
                    .map(enrolment => {
                        /* converting into the form required for getEnrolmentToShow */
                        return {
                            enrolment
                        }
                    })

                if (!_.isEmpty(recentEnrolments)) {
                    /* We still dont have info about the squad which is enrolled, so we need to get groupEnrolments info */
                    const { enrolment } = digitalSocialLeagueTabPageViewBuilder.getEnrolmentToShow(recentEnrolments, LEAGUE_LEADERBOARD_SOURCE.CLP, challengeIdVsChallenge)

                    const communityIdVsCommunity = _.mapKeys(commonData?.communitiesResponse?.communities, (community) => community.id)

                    const recentEnrolmentsForChallenge = (await riddlerService.getEnrolmentsForChallenge({ userId, challengeId: enrolment.challengeId, withStandings: true, entries: 10, withGroupEnrolments: true, withGroup: true })).enrolments.filter(enrolment => enrolment.status !== "FORCE_EXIT")
                    let selectedEnrolment, selectedCommunity
                    for (const enrolment of recentEnrolmentsForChallenge) {
                        if (!(selectedEnrolment && selectedCommunity) || communityIdVsCommunity[enrolment?.groupEnrolments?.[0]?.group?.meta?.squadId]?.creatorNode.entityId === userId) {
                            /* there might be selectedEnrolment without any selectedCommunity because of EXPIRED enrolments of squads not currently part of */
                            selectedEnrolment = { enrolment, groupEnrolment: enrolment?.groupEnrolments?.[0] }
                            selectedCommunity = communityIdVsCommunity[DigitalSocialLeagueTabPageViewBuilder.communityIdFromGroupEnrolment(enrolment.groupEnrolments[0])]
                        }
                    }

                    if (selectedCommunity && selectedEnrolment) {
                        leaderboardPromise = digitalSocialLeagueTabPageViewBuilder.createLeaderboardUsingCommunityAndEnrolment(selectedCommunity, userId, LEAGUE_LEADERBOARD_SOURCE.CLP, selectedEnrolment, challengeIdVsChallenge[selectedEnrolment.enrolment.challengeId], undefined, true)
                    }
                }
            }
            if (!leaderboardPromise) {
                /* Show either own community or any other community the user is part of*/
                const communityForLeaderboardPreview = commonData?.communitiesResponse?.communities.find((community) => community.creatorNode.entityId == userId) || commonData?.communitiesResponse?.communities?.[0]
                /* Either own community exists or no other community exists and has some pending invites */
                if (!(communityForLeaderboardPreview?.creatorNode?.entityId !== userId) && !_.isEmpty(invites?.inviterEntries)) {
                    const inviterEntry = invites.inviterEntries[0]

                    const invitersCommunityMembers = (await socialService.getCommunityMembers(inviterEntry.entry.communityId, inviterEntry.creator.id, LEAGUE_LIST_MEMBER_LIMIT, 0)).map(entry => entry.userId)

                    leaderboardPromise = digitalSocialLeagueTabPageViewBuilder.createLeaderboardForAdhocData({
                        title: DigitalLeagueSectionViewBuilder.getLeagueName(inviterEntry.creator),
                        userIds: [...invitersCommunityMembers, userId]
                    }, LEAGUE_LEADERBOARD_SOURCE.CLP, true)
                } else if (communityForLeaderboardPreview) {
                    /* Else just show any other community user is part of  */
                    leaderboardPromise = digitalSocialLeagueTabPageViewBuilder.createLeaderboardUsingCommunityAndEnrolment(communityForLeaderboardPreview, userId, LEAGUE_LEADERBOARD_SOURCE.CLP, undefined, undefined, undefined, true)
                }
            }

            const leaderboard = await leaderboardPromise

            if (!_.isEmpty(leaderboard?.userList)) {
                leaderboardPreview = {
                    leaderboard: { ...leaderboard, userList: leaderboard.userList.slice(0, LEAGUE_LEADERBOARD_ROW_ELEMENTS_LIMIT), sceneStyle: { elevation: 9 } },
                    numOtherSquads: 2
                }
            }
        }

        return leaderboardPreview
    }

    static getTrialInterstitialImage(defaultImageObj: any) {
        const timeNow = moment().valueOf()
        const imageObj = trialImages.find(image => {
            if (moment(image.startTime).valueOf() <= timeNow && moment(image.endTime).valueOf() > timeNow) {
                return true
            }
        })
        return imageObj?.mobileImage ? imageObj : defaultImageObj
    }

    static async getCreateSquadView(userContext: UserContext, userCache: CacheHelper, digitalLeagueSectionViewBuilder: DigitalLeagueSectionViewBuilder, socialService: ISocialService, logger: Logger, source: LeagueSectionSource): Promise<LeagueRequestStatusSectionListWidget | undefined> {
        const userId = userContext.userProfile.userId
        const currentUser = await userCache.getUser(userId)
        const [{ obj: invites, err: err1 }, { obj: communitiesResponse, err: err2 }] = await Promise.all([eternalPromise(digitalLeagueSectionViewBuilder.getPendingReceivedInvitationsSection(currentUser, 10, 0, LeagueSectionSource.CREATE_SQUAD_MODAL)), eternalPromise(socialService.getUserCommunities(userId, CommunityType.LEAGUE, false, LEAGUE_LIST_MEMBER_LIMIT, 0))])
        if (err1 || err2) {
            logger.info(err1 || err2)
        }
        let header, footerExtraInfo
        const listFooterStyle: { borderTopWidth?: number, paddingBottom: number } = {
            paddingBottom: 0
        }
        if (!_.isEmpty(invites?.data)) {
            header = {
                title: CREATE_SQUAD_MODAL_TITLE,
                subTitle: CREATE_SQUAD_MODAL_SUBTITLE
            }
            footerExtraInfo = {
                text: "- OR -",
            }
        } else {
            header = {
                title: CREATE_SQUAD_MODAL_TITLE,
                subTitle: CREATE_SQUAD_MODAL_SUBTITLE
            }
        }
        listFooterStyle.borderTopWidth = 0

        const footerAction = communitiesResponse.communities.findIndex(community => community.creatorNode.entityId === userId) > -1 ? undefined : DigitalLeagueSectionViewBuilder.getCreateCommunityAction("CREATE YOUR SQUAD", undefined, undefined, source)
        if (footerAction) {
            footerAction.hasCtaStyle = true
        }
        return !_.isEmpty(footerAction) || !_.isEmpty(invites?.data) ? {
            widgetType: "LEAGUE_REQUEST_STATUS_SECTION_LIST_WIDGET",
            sections: [{ ...invites, data: invites?.data || [], header, hasFooter: false }],
            footerAction,
            listFooterStyle,
            footerExtraInfo,
            hasListFooter: true,
            refreshEnabled: true,
            callParams: [{ source: LeagueSectionSource.CREATE_SQUAD_MODAL } as any]
        } : undefined

    }

    static async getActiveMembershipDates(userContext: UserContext, diyService: IDIYFulfilmentService, catalogueService: ICatalogueService) {
        const userProfile = userContext.userProfile
        const { timezone } = userProfile
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        const userId = userProfile.userId
        if (userId === "0") {
            return undefined
        }
        let memberships: Membership[] = await diyService.getAllMembershipDetails(userProfile.userId, tenant)
        let lastUserMembership
        if (_.isEmpty(memberships)) {
            try {
                lastUserMembership = await diyService.getMembershipDetails(userProfile.userId, tenant)
            } catch (e) {
                return undefined
            }
            memberships = [lastUserMembership]
        }
        const activeMembership = memberships.find(async (membership) => {
            const isTrialMembership = !_.isEmpty(membership.metadata) && membership.metadata.isTrial
            const product = await catalogueService.getProduct(membership.productId)
            const membershipState = LiveUtil.getLiveFitMembershipState(userContext, membership, product, isTrialMembership)
            return (membershipState === "ACTIVE" || membershipState === "EXPIRING")
        })
        if (activeMembership) {
            return { start: activeMembership.start, end: activeMembership.end }
        }
        return undefined
    }

    public static createCommunityUserInviteRequest(communityId: number, inviteeUserId?: string, countryCallingCode?: string, phoneNumber?: string, name?: string): CommunityUserInviteRequest {
        return {
            communityId,
            countryCallingCode: phoneNumber ? `+${countryCallingCode || "91"}` : undefined,
            phoneNumber,
            name,
            userId: inviteeUserId
        }
    }

    public static getLeagueRecommendedUserName(user: User, canShowPhoneNumber: boolean) {
        return ((user.firstName || "") + (user.lastName ? ` ${user.lastName}` : "")) || (canShowPhoneNumber ? user.phone : "")
    }

    public static createBooleanMapping(arr: any[], iteratee: string | Iteratee) {
        return arr.reduce((acc, value) => {
            let mapKey
            if (typeof iteratee === "string") {
                mapKey = _.get(value, iteratee)
            } else {
                mapKey = iteratee(value)
            }
            if (mapKey) {
                acc[mapKey] = true
            }
            return acc
        }, {} as IdVsValueMap<boolean>)
    }

    public static async getSelfCommunityId(userId: string, socialService: ISocialService, communitiesResponse?: CommunitiesResponse): Promise<number | undefined> {
        return (communitiesResponse || (await socialService.getUserCommunities(userId, CommunityType.LEAGUE, false, LEAGUE_LIST_MEMBER_LIMIT, 0)))
            .communities.find(community => community.creatorNode.entityId === userId)?.id
    }

    public static getLeagueInviteLimit(numCommunityMembers: number, numInvitesResponse: number) {
        return Math.max(0, LEAGUE_LIST_MEMBER_LIMIT - (numCommunityMembers + numInvitesResponse)) || 0
    }

    public static async isContactsSyncSupportedAndSquadExists(userContext: UserContext, socialService: ISocialService, selfCommunityId?: number, communitiesResponse?: CommunitiesResponse) {
        return (AppUtil.isContactsSyncSupported(userContext))
            && (selfCommunityId || await this.getSelfCommunityId(userContext.userProfile.userId, socialService, communitiesResponse))
    }

    public static getFormatIcon(format: any): string {
        switch (format) {
            case "SNC":
                return "/image/tvAppIcons/snc.png"
            case "STRENGTH":
                return "/image/tvAppIcons/strength.png"
            case "CARDIO":
                return "/image/tvAppIcons/cardio.png"
            case "DANCE":
                return "/image/tvAppIcons/dance.png"
            case "YOGA":
                return "/image/tvAppIcons/yoga.png"
            case "ALL":
                return "/image/tvAppIcons/all.png"
            case "MASTER CLASS":
                return "/image/tvAppIcons/master.png"
            case "BOXING":
                return "/image/tvAppIcons/boxing.png"
            case "LIVE INTERACTIVE":
                return INTERACTIVE_ICON_URL
            default:
                return "/image/tvAppIcons/strength.png"
        }
    }
    public static canShowNewYogaReport(isYogaReportSupported: boolean, userScoreMetrics: UserScoreMetricsResponse) {
        return isYogaReportSupported && userScoreMetrics?.reportType === "YOGA_REPORT"
    }

    public static isLiveFitnessProduct(productType: ProductType): boolean {
        return ["DIY_FITNESS", "DIY_FITNESS_PACK"].includes(productType)
    }

    public static isLiveMeditationProduct(productType: ProductType): boolean {
        return ["DIY_MEDITATION", "DIY_MEDITATION_PACK"].includes(productType)
    }

    public static getDIYCLPBreadcrumbs(productType: ProductType, isInternational: boolean): IBreadCrumb[] {
        const breadcrumbs: IBreadCrumb[] = [{ title: "Home", link: "/" }]

        if (!isInternational) {
            breadcrumbs.push({ title: "Live", link: RouteNames.Livefit })
        }

        if (this.isLiveFitnessProduct(productType)) {
            breadcrumbs.push(isInternational ? { title: "Workouts", link: "/workouts" } : { title: "Fitness", link: RouteNames.LiveFitness })
        } else if (this.isLiveMeditationProduct(productType)) {
            breadcrumbs.push(isInternational ? { title: "Meditation", link: "/meditation" } : { title: "Mindfulness", link: RouteNames.LiveMindfulness })
        }

        return breadcrumbs
    }

    public static getDIYCategoryPageBreadCrumbs(diySeries: DIYSeries, isInternational: boolean): IBreadCrumb[] {
        const breadcrumbs: IBreadCrumb[] = this.getDIYCLPBreadcrumbs(diySeries.productType, isInternational)

        breadcrumbs.push({ title: diySeries.name, link: ActionUtilV1.diySeriesPage(diySeries, "DESKTOP") })

        return breadcrumbs
    }

    public static getDIYSubCategoryPageBreadCrumbs(diyPack: DIYPack, isInternational: boolean, diySeries?: DIYSeries): IBreadCrumb[] {
        const breadcrumbs: IBreadCrumb[] = this.getDIYCLPBreadcrumbs(diyPack.productType, isInternational)

        diySeries && breadcrumbs.push({ title: diySeries.name, link: ActionUtilV1.diySeriesPage(diySeries, "DESKTOP") })

        breadcrumbs.push({ title: diyPack.title, link: ActionUtilV1.diyPackProductPage(diyPack, "DESKTOP") })

        return breadcrumbs
    }

    public static getNextDIYSessionIdx(pack: DIYPack, packFulfilment: DIYPackFulfilment): number {
        if (packFulfilment && packFulfilment.status === "SUBSCRIBED" && packFulfilment.completedProductIds !== undefined) {
            for (let i = 0; i < pack.sessionIds.length; i++) {
                if (!packFulfilment.completedProductIds.includes(pack.sessionIds[i])) {
                    return i
                }
            }
        }
        return 0
    }

    public static getSessionMapAwareNextDIYSessionIdx(pack: DIYPack, packFulfilment: DIYPackFulfilment, sessionMap: Record<string, Product>): number {
        if (packFulfilment && packFulfilment.status === "SUBSCRIBED" && packFulfilment.completedProductIds !== undefined) {
            for (let i = 0; i < pack.sessionIds.length; i++) {
                // find the first i for which sessionId exists in map and not in completedProductIds
                if (sessionMap?.[pack.sessionIds[i]] && !packFulfilment.completedProductIds.includes(pack.sessionIds[i])) {
                    return i
                }
            }
        }
        return 0
    }

    public static getDIYShareAction(packId: string, productId: string, productType: ProductType, packName: string, productName: string): Action {
        return {
            icon: "SHARE",
            actionType: "REST_API",
            showLoadingIndicator: true,
            meta: {
                method: "post",
                url: `/pack/diy/inviteLink`,
                body: {
                    productId,
                    packId,
                    productType,
                    isSession: true
                },
            },
            analyticsData: {
                type: productType,
                eventType: "SHARE_EVENT",
                packName,
                productName
            }
        }
    }

    public static getLiveShareAction(productId: string, productName: string , productType: string, source: string): Action {
        return {
            icon: "SHARE",
            actionType: "REST_API",
            showLoadingIndicator: true,
            meta: {
                method: "post",
                url: `/digital/inviteLink`,
                body: {
                    contentId: productId,
                    card : {
                        source
                    }
                },
            },
            analyticsData: {
                type: productType,
                eventType: "SHARE_EVENT",
                productName
            }
        }
    }

    /*
    * Used to simulate pushPlaybackMetrics call made by the client
    * */
    public static async updatePlaybackMetricsSynthetically(userContext: UserContext, contentId: string, preferredStreamType: string, req: express.Request, diyFulfilmentService: IDIYFulfilmentService, userCache: CacheHelper, userName?: string) {
        const userId = userContext.userProfile.userId
        return diyFulfilmentService.updatePlaybackmetrics({
            userSessionId: `${contentId}_${userId}`,
            contentType: "LIVE",
            contentId,
            userId,
            playbackID: crypto.randomUUID(),
            playbackOffset: 0,
            gameViewConfig: "DISABLED",
            latitude: userContext.sessionInfo.lat,
            longitude: userContext.sessionInfo.lon,
            selectedCityId: userContext.userProfile.cityId,
            selectedCountryId: userContext.userProfile.city.countryId,
            detectedCityId: userContext.userProfile.cityId,
            detectedCountryId: userContext.userProfile.city.countryId,
            tenant: AppUtil.getTenantFromUserContext(userContext),
            codePushVersion: userContext.sessionInfo.cpVersion,
            streamType: preferredStreamType,
            playbackBandwidth: 0,
            cityName: userContext.userProfile.city.name,
            platform: JSON.stringify({osName: userContext.sessionInfo.osName, brand: req.header("deviceBrand"), model: req.header("deviceModel")}),
            appVersion: userContext.sessionInfo.appVersion,
            serverTimestamp: Date.now(),
            isBuffering: false,
            eventType: "BUFFER",
            cameraPermission: true,
            casting: null,
            playbackType: "LIVE",
            ctsEpoch: 0,
            pts: -1,
            bufferRemainingInSeconds: 0,
            userName
        }, false)
    }

    public static getLiveInteractiveLabels(): Label[] {
        return [
            {
                title: "INTERACTIVE",
                textColor: "white",
                backgroundColor: "statusGreen",
                iconUrl: "image/diy/interactive-session-badge.gif",
            },
        ]
    }

    public static getCapitalize(text: string): string {
        return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase()
    }
    public static getFormatText(format: LiveFitWorkoutFormat): string {
        if (format === "HRX" || format === "SNC" || format === "HIIT" || format === "AMA") {
            return format
        }
        return this.getCapitalize(format.toString())
    }
    public static getBadgeUnit(badgeType: BadgeSubType) {
        switch (badgeType) {
            case "DISTANCE":
                return "Kms"
            case "CALORIE":
                return "Cals"
            case "ENERGY_SCORE":
                return "Pts"
            case "DURATION":
                return "Mins"
            case "BOOSTER":
                return "Boosters"
            case "COUNT":
                return "Steps"
            default:
                return ""

        }
    }
    public static getIsBadgeTypeDuration(badge: Badge) {
        return badge.activityCount && badge.subType === "DURATION"
    }
    public static async getLiveNotificationObject(userContext: UserContext, hamletBusiness: HamletBusiness, cFAPIJavaService: CFAPIJavaService, logger: Logger, isLiveClassConfirmation: boolean) {
        const expId = process.env.ENVIRONMENT === "STAGE" ? "415" : "780"
        const hamletExperimentMap = await hamletBusiness.getUserAllocations(AppUtil.getHamletContext(userContext, [expId]))
        const userAssignment = hamletExperimentMap ? hamletExperimentMap.assignmentsMap[expId] : undefined
        const doesUserBelongToWhatsappTrialExperiment = userAssignment ? userAssignment.bucket.bucketId === "1" : process.env.ENVIRONMENT === "STAGE"
        let notification = undefined
        try {
            const response = await cFAPIJavaService.getUserNotificationPreference(userContext.userProfile.userId)
            if (!_.isEmpty(response)) {
                notification = {
                    title: "Get positive affirmations, class reminders and recommendations on Whatsapp",
                    options: [{
                        title: "REMINDERS",
                        status: response.children[0].subscriptionStatus,
                        id: response.children[0].id
                    }, {
                        title: "RECOMMENDATIONS & MORE",
                        status: response.children[1].subscriptionStatus,
                        id: response.children[1].id
                    }],
                    userId: userContext.userProfile.userId
                }
                if (!doesUserBelongToWhatsappTrialExperiment || !AppUtil.isWhatsappCommunicationForNudgesSupported(userContext) || isLiveClassConfirmation) {
                    notification = {
                        title: "Set Whatsapp reminders",
                        subtitle: "Get reminders for your live classes and other fitness updates",
                        status: response.children[0].subscriptionStatus,
                        id: response.children[0].id,
                        userId: userContext.userProfile.userId
                    }
                }
            }
            logger.info("userId, notification preference: " + JSON.stringify(response))
        } catch (error) {
            // we will not show the toggle option if the api fails
            logger.error("notification preference error: " + JSON.stringify(error))
        }
        return notification
    }

    public static async getLivePageDeepLink(segmentService: ISegmentService, userContext: UserContext) {
        // members who converted from unified clp or old members part of aurora experiment
        const unifiedCLPMember = await segmentService.doesUserBelongToAnySegment(["a2ff3f8e-a098-43cb-a330-187ea4f09ae1", NEW_UNIFIED_CLP_MEMBERS_CONVERTED_AFTER_4_NOV_9_28, UNIFIED_FITNESS_TAB_MEMBER_SEGMENT], userContext)
        if (!_.isEmpty(unifiedCLPMember)) {
            return "curefit://tabpage?pageId=fitnesshub&selectedTab=cultpassLIVE-Members_NEW"
        }
        // new users
        const newUserSegment = await segmentService.doesUserBelongToAnySegment(["37202c47-6ee4-4c1e-8a4c-ba35fb66c442"], userContext)
        if (!_.isEmpty(newUserSegment)) {
            return "curefit://listpage?pageId=CultPassLiveSKU"
        }
        return "curefit://tabpage?pageId=live&selectedTab=CultAtHome"
    }

    public static isInteractiveSession(preferredStreamType: PreferredStreamType) {
        return preferredStreamType === "VIDEO_CALL"
    }

    public static isConsideredInteractiveSessionForPresentation(preferredStreamType: PreferredStreamType) {
        return LiveUtil.isRealLiveSession(preferredStreamType) || LiveUtil.isInteractiveSession(preferredStreamType)
    }

    public static isRealLiveSession(preferredStreamType: PreferredStreamType) {
        return preferredStreamType === "LIVE"
    }

    public static isVodSession(preferredStreamType: PreferredStreamType) {
        return preferredStreamType === "VOD"
    }

    public static async getOnboardingOrderConfirmationBanner(userContext: UserContext, segmentService: ISegmentService, hamletService: HamletBusiness, diyService: IDIYFulfilmentService) {

        const doesUserBelongToExperiment = await AppUtil.doesUserBelongToInGuidanceExperiment(userContext, hamletService)
        const hasUserEnteredExperiment = await AppUtil.hasUserEnteredInGuidanceExperiment(userContext, diyService)

        const formUrl = process.env.ENVIRONMENT === "STAGE" ? "curefit://userform?formId=Live_OnboardingExp"
            : "curefit://userform?formId=Program_based_guidance"

        if (doesUserBelongToExperiment && !hasUserEnteredExperiment) {
            const banner = new MerchantryWidget({
                    type: "CAROUSEL",
                    data: {
                        variant: "12",
                        aspectRatio: "335:445",
                    },
                    spacing: {
                        top: "0",
                        bottom: "70",
                    },
                },
                [
                    {
                        image: "/image/onboarding/335X445-Welcome!+Get+your+free+workout+plan.jpg",
                        action: {
                            url: formUrl,
                            title: "GET MY PLAN",
                            actionType: "NAVIGATION",
                            variant: "primarySmall",
                        },
                    },
                ]
            )

            banner.analyticsData = {
                eventKey: "button_click_event",
                eventData: {
                    source: "pack_confirmation_screen",
                    actionType: "inguidance_pack_purchase_banner_click"
                }
            }

            return banner
        }

        return null
    }

    public static async getCoachOrderConfirmationBanner(userContext: UserContext, segmentService: ISegmentService, hamletService: HamletBusiness, diyService: IDIYFulfilmentService) {

        const doesUserBelongToExperiment = await AppUtil.doesUserBelongToCoachExperiment(userContext, hamletService)
        // const doesUserBelongToExperiment2 = await AppUtil.doesUserBelongToCoachV2Experiment(userContext, hamletService)
        const doesUserBelongToTrialExperiment = await AppUtil.doesUserBelongToCoachTrialExperiment(userContext, hamletService)
        const isUserAlreadyEnrolled = await LiveUtil.isUserEnrolledInGuidanceExp(userContext, diyService)

        const formUrl = "curefit://userform?formId=Home_Guidance_Onboarding"
        const formUrlV2 = "curefit://fl_form?formId=Home_Guidance_Onboarding_v2"

        if ((doesUserBelongToExperiment || doesUserBelongToTrialExperiment) && !isUserAlreadyEnrolled) {
            // const banner = new MerchantryWidget({
            //         type: "CAROUSEL",
            //         data: {
            //             variant: "12",
            //             aspectRatio: "335:445",
            //         },
            //         spacing: {
            //             top: "0",
            //             bottom: "70",
            //         },
            //     },
            //     [
            //         {
            //             image: "/image/onboarding/Group 13540 (5).png",
            //             action: {
            //                 url: formUrl,
            //                 title: "",
            //                 actionType: "NAVIGATION",
            //                 variant: "primarySmall",
            //             },
            //         },
            //     ]
            // )
            //

            const banner = new MerchantryWidget({
                    type: "CAROUSEL",
                    data: {
                        variant: "12",
                        aspectRatio: "335:445",
                    },
                    spacing: {
                        top: "0",
                        bottom: "70",
                    },
                },
                [
                    {
                        image: "/image/onboarding/get your plan (3).jpg",
                        action: {
                            url: userContext.sessionInfo.appVersion >= ONBOARDING_FORM_V2_SUPPORTED_VERSION ? formUrlV2 : formUrl,
                            title: "GET MY PLAN",
                            actionType: "NAVIGATION",
                            variant: "primarySmall",
                        },
                    },
                ]
            )

            banner.analyticsData = {
                eventKey: "button_click_event",
                eventData: {
                    source: "pack_confirmation_screen",
                    actionType: "coach_onboarding_pack_purchase_banner_click"
                }
            }

            return banner
        }

        return null
    }

    public static async isUserEnrolledInGuidanceExp(userContext: UserContext, diyService: IDIYFulfilmentService) {
        const userId = userContext.userProfile.userId
        const diyUserAttribute = await diyService.getUserAttribute(userId, "HOME_GUIDANCE_ATTRIBUTE", AppUtil.getTenantFromUserContext(userContext))
        if (diyUserAttribute === null || diyUserAttribute.attributeState === DIYUserAttributeState.INACTIVE) {
            return false
        }
        return true
    }

    public static isFirstWeekProgress(cycleProgress: CycleProgress, diyUserAttribute: DIYUserAttribute) {
        // Checks if this is the first week of user enrollment to change the workout day logic later on
        const createdOrNewDate = (<any>diyUserAttribute).createdDate ?? new Date().toISOString()
        const createdDate = new Date(createdOrNewDate).toISOString().split("T")[0]
        const cycleStartDate = cycleProgress.currentCycleStartDate
        const cycleEndDate = cycleProgress.currentCycleEndDate

        return cycleStartDate <= createdDate && createdDate <= cycleEndDate
    }

    public static isRestDayToday(diyUserAttribute: DIYUserAttribute, curProgress: FitnessAssessmentProgress) {
        // Checks if the day user enrolled in the flow was a rest day or not according to their preferred workout days.
        // This is for the first week logic so that if user enrolls on a rest day we can add one active day to the remaining
        // preferred workout days because the first day shouldn't be a rest day.
        const preferredActiveDays = diyUserAttribute?.meta?.preferredWeekDays
        if (_.isEmpty(preferredActiveDays)) {
            return false
        }
        const createdOrNewDate = (<any>diyUserAttribute).createdDate ?? new Date().toISOString()
        let createdDate = new Date(createdOrNewDate).getDay()
        if (createdDate === 0) {
            createdDate = SUNDAY_WEEKDAY_NUM
        }
        return !_.includes(preferredActiveDays, createdDate)
    }

    public static getDefaultWorkoutDays() {
        return [5, 6, 7]
    }
    public static getRemainingPreferredWorkoutDays(diyUserAttribute: DIYUserAttribute, curProgress: FitnessAssessmentProgress) {
        // Checks the day of the week for progress createdDate and uses it to find number of remaining preferred
        // workout days for first week. For eg. if user's preferred days are [Mon, Wed, Fri] and user enrolls on Thurs
        // then this returns 1
        let preferredActiveDays = diyUserAttribute?.meta?.preferredWeekDays
        if (_.isEmpty(preferredActiveDays)) {
            preferredActiveDays = this.getDefaultWorkoutDays()
        }
        const createdOrNewDate = (<any>diyUserAttribute).createdDate ?? new Date().toISOString()
        let createdDate = new Date(createdOrNewDate).getDay()
        if (createdDate === 0) {
            createdDate = SUNDAY_WEEKDAY_NUM
        }
        return preferredActiveDays.reduce((acc: any, curVal: number) => curVal >= createdDate ? acc + 1 : acc, 0)
    }

    public static async getWeeklyScreenUrl(userContext: UserContext, diyService: IDIYFulfilmentService) {
        const userId = userContext.userProfile.userId
        const tz = userContext.userProfile.timezone
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        if (!AppUtil.isWeeklyGoalPageSupported(userContext)) {
            return null
        }
        const weeklyGoalInfo = await diyService.getWeeklyGoalInfo(userId, tenant, tz)

        if (_.isNil(weeklyGoalInfo)) {
            return null
        }
        const preferredActiveDays = weeklyGoalInfo.numPreferredWorkoutDays
        const curActiveDays = weeklyGoalInfo.numCompletedWorkoutDays
        const numClassesDoneToday = weeklyGoalInfo.numClassesDoneToday

        // Don't show weekly goal/weekly streak screen if user has worked on more than preferred active day or
        // if it's not the first class of the day
        if (curActiveDays > preferredActiveDays || numClassesDoneToday > 1) {
            return null
        }

        if (curActiveDays == 1)
            return "curefit://weeklystreakpage"
        else
            return "curefit://weeklygoalpage"

    }
    public static getExitActionForWeeklyScreen(url: string, nextUrl: string, isFlutter?: boolean) {
        return {
            exitAction: {
                actionType: "NAVIGATION",
                url: url,
                meta: {
                    nextAction: {
                        actionType: isFlutter ? "RESET_NAVIGATION" : "NAVIGATION",
                        url: nextUrl,
                        analyticsData: {
                            source: url.split("//")[1]
                        }
                    }
                }
            }
        }
    }

    // public static async isLiveTrialExpired(userContext: UserContext, interfaces: CFServiceInterfaces) {
    //     const userId = userContext.userProfile.userId
    //     const memberships = await interfaces.diyService.getAllMembershipDetails(userId, Tenant.CUREFIT_APP)
    //     const trialMembershipList = memberships.filter(membership => membership.type === MembershipType.TRIAL)
    //     if (trialMembershipList.length > 0) {
    //         return trialMembershipList[0].end < Date.now()
    //     }
    //     return true
    // }

    public static getPrefilledOnboardingFormScreenData(prevFormResponse: UserFormRequest) {
        const prevScreenData = prevFormResponse.screenDataResponse
        return {
            "Home_Guidance_v2_gender_final" : prevScreenData["@Home Guidance_Gender"],
            "Home_Guidance_v2_Height" : prevScreenData["@Home Guidance_Height"],
            "Home_Guidance_v2_weight" : prevScreenData["@Home Guidance_Weight"],
            "Home_Guidance_v2_fitness_level" : prevScreenData["Home_Guidance_Weekly_Workouts"],
            "Home_Guidance_v2_Fitness_Goal" : prevScreenData["Home_Guidance_Fitness_Goal"],
            "Home_Guidance_v2_Workout_days": prevScreenData["Home_Guidance_Workout_days"],
            "Home_Guidance_v2_workout_time": prevScreenData["@Home Time"],
            "Home_Guidance_v2_Fitness_Format": prevScreenData["Home_Guidance_Fitness_Format"]
        }
    }
}



export default LiveUtil
