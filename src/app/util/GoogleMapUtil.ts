// Documentation : https://developers.google.com/maps/documentation/maps-static/get-api-key#sample-code-for-url-signing

"use strict"

const crypto = require("crypto")
const url = require("url")
export const GOOGLE_STATIC_MAP_URL = "https://maps.googleapis.com/maps/api/staticmap"
export const GOOGLE_MAP_SECRET_CODE = "BfVBfV-RsVhM0GFv0k7c0W5DqoM="
export const GOOGLE_MAP_KEY = "AIzaSyCKGgl_YAQeMavme0hDq3kaOPzT9GLfTGg"
// /**
//  * Convert from 'web safe' base64 to true base64.
//  *
//  * @param  {string} safeEncodedString The code you want to translate
//  *                                    from a web safe form.
//  * @return {string}
//  */
function removeWebSafe(safeEncodedString: string) {
  return safeEncodedString.replace(/-/g, "+").replace(/_/g, "/")
}

// /**
//  * Convert from true base64 to 'web safe' base64
//  *
//  * @param  {string} encodedString The code you want to translate to a
//  *                                web safe form.
//  * @return {string}
//  */
function makeWebSafe(encodedString: string) {
  return encodedString.replace(/\+/g, "-").replace(/\//g, "_")
}

// /**
//  * Takes a base64 code and decodes it.
//  *
//  * @param  {string} code The encoded data.
//  * @return {string}
//  */
function decodeBase64Hash(code: string) {
  // "new Buffer(...)" is deprecated. Use Buffer.from if it exists.
  return Buffer.from ? Buffer.from(code, "base64") : new Buffer(code, "base64")
}

// /**
//  * Takes a key and signs the data with it.
//  *
//  * @param  {string} key  Your unique secret key.
//  * @param  {string} data The url to sign.
//  * @return {string}
//  */
function encodeBase64Hash(key: any, data: string) {
  return crypto.createHmac("sha1", key).update(data).digest("base64")
}

// /**
//  * Sign a URL using a secret key.
//  *
//  * @param  {string} path   The url you want to sign.
//  * @param  {string} secret Your unique secret key.
//  * @return {string}
//  *
function sign(path: string, secret: string) {
  const uri = url.parse(path)
  const safeSecret = decodeBase64Hash(removeWebSafe(secret))
  const hashedSignature = makeWebSafe(encodeBase64Hash(safeSecret, uri.path))
  return url.format(uri) + "&signature=" + hashedSignature
}

export function getMapURL(lat: number, long: number) {
    const url = `${GOOGLE_STATIC_MAP_URL}?center=${lat},${
        long
      }&zoom=17&size=480x200
      &maptype=roadmap&markers=${lat},${long}
      &scale=2&key=${
        GOOGLE_MAP_KEY
      }`
      return sign(url, GOOGLE_MAP_SECRET_CODE)
}
