import * as _ from "lodash"
import { ActivityDS } from "@curefit/logging-common"
import { UserActivity } from "../user/TimelineView"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { ActivityState } from "@curefit/logging-common"
import { FoodShipmentStatus } from "@curefit/eat-common"
import { CultBooking } from "@curefit/cult-common"
import { SleepActivity, WalkActivity } from "@curefit/atlas-client"
import { TimelineActivity } from "@curefit/albus-client"
import { CareUtil } from "./CareUtil"
import { Action, ActionList } from "../common/views/WidgetView"

const TODAY_FROM_SERVER_IOS_APP_VERSION = 6.2
const TODAY_FROM_SERVER_ANDROID_APP_VERSION = 6.32
const TODAY_FROM_SERVER_IOS_CP_VERSION = 34
const TODAY_FROM_SERVER_ANDROID_CP_VERSION = 40
class TimelineUtil {

    public static isTodayStepsFromServerSupported(userAgent: UserAgent, appVersion: number, codepushVersion?: number, osName?: string) {
        if (userAgent === "DESKTOP" || userAgent === "MBROWSER")
            return true
        if ((osName === "ios" && (appVersion > TODAY_FROM_SERVER_IOS_APP_VERSION || codepushVersion >= TODAY_FROM_SERVER_IOS_CP_VERSION))
            || (osName === "android" && (appVersion > TODAY_FROM_SERVER_ANDROID_APP_VERSION || codepushVersion >= TODAY_FROM_SERVER_ANDROID_CP_VERSION))) {
            return true
        }
        return false
    }
    public static getCultStatus(booking: CultBooking): ActivityState {
        let status: ActivityState = "TODO"
        switch (booking.label) {
            case "Completed":
                status = "DONE"
                break
            case "Upcoming":
                status = "TODO"
                break
            case "Ongoing":
                status = "TODO"
                break
            case "No Show":
                status = "SKIPPED"
                break
            case "Cancelled":
                status = "DELETED"
                break
        }
        return status
    }

    public static getCareStatus(activity: TimelineActivity, isMissed: boolean): ActivityState {
        const status: ActivityState = "TODO"
        if (activity.carefitActivity !== "CONSULTATION" && activity.userActivityStatus) {
            switch (activity.userActivityStatus) {
                case "DONE": return "DONE"
                case "MISSED": return "SKIPPED"
                case "SCHEDULED":
                case "NONE": return "TODO"
            }
        }
        if (isMissed === true) {
            return "SKIPPED"
        } else {
            if (CareUtil.isComplteted(activity.bookingInfo))
                return "DONE"
            return "TODO"
        }
    }

    public static getEatFitDeliveryState(state: FoodShipmentStatus | "NOT_STARTED" | "CANCELLED"): string {
        switch (state) {
            case "NOT_STARTED":
                return undefined
            case "LOT_ASSIGNED":
            case "COOKING":
            case "PACKING":
            case "DISPATCHING":
            case "RECEIVING":
            case "ACCEPTING":
                return "Preparing"
            case "DRIVING":
                return "On its way"
            case "DELIVERING":
                return "Arriving"
            case "DELIVERED":
                return undefined
            case "REJECTED":
                return "Returned"
            case "CANCELLED":
                return "Cancelled"
        }
        return undefined
    }

    public static getEat3PStatus(state: string): ActivityState {
        switch (state) {
            case "PLACED":
            case "CONFIRMED":
            case "FOOD_READY":
            case "PICKED_UP":
            case "REACHED_LOCATION":
            case "DELIVERING":
            case "UNDELIVERED":
            case "REJECTED":
                return "TODO"
            case "DELIVERED":
                return "DONE"
            case "CANCELLED":
            case "ORDER_PLACE_FAILED":
                return "SKIPPED"
        }
    }

    public static getEatFitStatus(state: FoodShipmentStatus | "NOT_STARTED" | "CANCELLED"): ActivityState {
        switch (state) {
            case "CREATED":
            case "NOT_STARTED":
            case "LOT_ASSIGNED":
            case "COOKING":
            case "PACKING":
            case "DISPATCHING":
            case "RECEIVING":
            case "DRIVING":
            case "DELIVERING":
            case "ACCEPTING":
            case "ARRIVING_AT_KIOSK":
            case "SCANNING":
            case "REJECTED":
                return "TODO"
            case "DELIVERED":
                return "DONE"
            case "CANCELLED":
                return "SKIPPED"
        }
    }
    public static createSleepAtlasActivity(activityS: ActivityDS, isReviewed: boolean): SleepActivity {
        const sourceTypeSleep = activityS.meta.deviceId === "USER-UPDATE-YES" && !_.isNil(activityS.meta.sleep.referenceSourceType) ? activityS.meta.sleep.referenceSourceType : activityS.meta.sleep.sourceType

        const sleepActivity: SleepActivity = {
            date: activityS.date,
            duration: activityS.meta.sleep.duration / 1000,
            hasReviewed: isReviewed,
            timeRange: {
                start: activityS.meta.sleep.startTime,
                end: activityS.meta.sleep.endTime
            },
            type: "SLEEP",
            status: activityS.score > 0 ? "DONE" : "SKIPPED",
            source: sourceTypeSleep
        }
        return sleepActivity

    }

    public static createStepsAtlasActivity(activityS: ActivityDS): WalkActivity {
        const stepActivity: WalkActivity = {
            date: activityS.date,
            steps: {
                done: _.ceil(activityS.meta.steps.done),
                goal: _.ceil(activityS.meta.steps.goal)
            },
            type: "WALK",
            status: activityS.score > 0 ? "DONE" : "SKIPPED",
            source: activityS.meta.platform

        }
        return stepActivity

    }


    public static createUserActivity(activityDS: ActivityDS): UserActivity {


        if (activityDS.activityType === "SLEEP") {

            const act: UserActivity = {
                date: activityDS.date,
                activityType: "SLEEP",
                title: "Target: Sleep 7 to 9 hrs",
                activityName: "Sleep",
                userActivityId: "SLEEP",
                status: activityDS.score > 0 ? "DONE" : "SKIPPED",
                duration: activityDS.meta.sleep.duration / 1000,
                messageText: "Tap for more Details",
                action: `curefit://sleepdetails?reviewed=true&date=${activityDS.date}&startTime=${activityDS.meta.sleep.startTime}&endTime=${activityDS.meta.sleep.endTime}`
            }
            return act
        }


        if (activityDS.activityType === "WALK") {
            const act: UserActivity = {
                date: activityDS.date,
                activityType: "WALK",
                title: "",
                userActivityId: "WALK",
                activityName: "Steps taken",
                status: activityDS.score > 0 ? "DONE" : "SKIPPED",
                steps: activityDS.meta.steps,
                action: `curefit://stepdetails?done=${activityDS.meta.steps.done}&goal=${activityDS.meta.steps.goal}&date=${activityDS.date}`

            }
            return act
        }
    }

    public static camelcaseSource(source: string): string {
        switch (source) {
            case "FITBIT":
                return "Fitbit"
            case "fitbit":
                return "Fitbit"
            case "GOOGLE_FIT":
                return "Google Fit"
            case "APPLE_HEALTH":
                return "Apple Health"
            case "CUREFIT":
                return "Curefit"
            case "CUREFIT-APP":
                return "Curefit App"
            case "USER":
                return "User Update"
            case "APPLE_HEALTH_KIT":
                return "Apple Health"
            default:
                return source

        }
    }

    public static createSmartManageOptionActions(actions: Action[]): Action[] {
        let smartActions: Action[] = []
        if (actions.length > 2) {
            const primaryAction = actions[0]
            primaryAction.viewType = "PRIMARY_BUTTON"
            smartActions.push(primaryAction)
            actions.splice(0, 1)
            const moreAction: ActionList = {
                title: "More",
                viewType: "SECONDARY_BUTTON",
                icon: "TRIPLE_DOT",
                actionType: "ACTION_LIST",
                actions: actions
            }
            smartActions.push(moreAction)
        }
        else {
            smartActions = actions
        }
        return smartActions
    }
}

export default TimelineUtil
