import { ActivityType, ProductType } from "@curefit/product-common"
import { UrlPathBuilder } from "@curefit/product-common"
import { DIYPack, DIYProduct, FitnessProgramProductResponse } from "@curefit/diy-common"
import { AdvertisementUtil } from "./AdvertisementUtil"
import * as _ from "lodash"

const numberToWords = require("number-to-words")
export interface DiyContentDetail {
    id: string
    type: string
    format: string
    URL: string,
    absoluteUrl: string,
    downloadUrl: string
}

export interface DiyContentMeta {
    activityId: string
    activityType: ActivityType | ProductType
    packId: string
    contentId: string
    consumptionRequired: boolean
    title: string
    image: string
    contentCategory?: ProductType
    adsEnabled?: boolean
}

export class AtlasUtil {

    static getDiyContentDetailsV2(session: DIYProduct): DiyContentDetail {
        return {
            id: session.contentId,
            type: session.contentType,
            format: session.contentFormat,
            URL: session.contentType === "audio" ? UrlPathBuilder.getAudioPath(session.contentId, session.contentFormat) :
                UrlPathBuilder.getVideoPath(session.contentId, session.contentFormat),
            absoluteUrl: AtlasUtil.getDIYAbsoluteUrl(session),
            downloadUrl: session.downloadURL
        }
    }

    static getDiyMetaInfoV2(session: DIYProduct, pack: DIYPack): DiyContentMeta {
        const productType = session.productType
        let image
        if (productType === "DIY_MEDITATION")
            image = UrlPathBuilder.getPackImagePathV1("APP", pack.productId, productType, "TODAY")
        else if (productType === "DIY_FITNESS")
            image = UrlPathBuilder.getSingleImagePathV1(session.contentId, productType, "TODAY")

        return {
            activityId: session.productId,
            packId: pack ? pack.productId : undefined,
            contentId: session.contentId,
            consumptionRequired: true,
            activityType: session.productType,
            title: session.title,
            image: image
        }
    }

    static getContentDetailV2(session: DIYProduct): DiyContentDetail {
        let URL = session.contentType === "audio" ? UrlPathBuilder.getAudioPath(session.contentId, session.contentFormat) : UrlPathBuilder.getVideoPath(session.contentId, session.contentFormat)
        if (session.isPastLiveV2 && session.contentType === "video") {
            URL = UrlPathBuilder.getDIYV2VideoPath(session.contentId)
        }
        return {
            id: session.contentId,
            type: session.contentType,
            format: session.contentFormat,
            URL,
            absoluteUrl: AtlasUtil.getDIYAbsoluteUrl(session),
            downloadUrl: session.downloadURL
        }
    }

    static getContentDetailV3(session: FitnessProgramProductResponse): DiyContentDetail {
        if (session.productType != "DIY") {
            return null
        }
        let URL = session.contentType === "audio" ? UrlPathBuilder.getAudioPath(session.contentId, session.contentFormat) : UrlPathBuilder.getVideoPath(session.contentId, session.contentFormat)
        if (session.isPastLiveV2 && session.contentType === "video") {
            URL = UrlPathBuilder.getDIYV2VideoPath(session.contentId)
        }
        return {
            id: session.contentId,
            type: session.contentType,
            format: session.contentFormat,
            URL,
            absoluteUrl: AtlasUtil.getDIYAbsoluteUrl(session),
            downloadUrl: session.downloadUrl
        }
    }

    static getDIYPackDetailsTVUrl(productType: string, packId: string) {
        const url = productType === "DIY_FITNESS" ? "curefit://cultdiypackold" : "curefit://diymeditationpackold"
        return url + "?packId=" + packId
    }

    static getDIYAbsoluteUrl(session: DIYProduct | FitnessProgramProductResponse): string {
        if (session.isPastLiveV2) {
            return `https://diy-vod.cure.fit${session.contentId}`
        }
        return session.contentType === "audio" ? UrlPathBuilder.getAudioAbsolutePath(session.contentId, session.contentFormat) :
            UrlPathBuilder.getVideoAbsolutePath(session.contentId, session.contentFormat)
    }


    static getContentMetaV2(session: DIYProduct, pack?: DIYPack): DiyContentMeta {
        const activityType = session.productType
        // disabled ads for diy meditation
        const adParams = activityType !== "DIY_MEDITATION" ? AdvertisementUtil.getAddParams(activityType, session.productId) : undefined
        return {
            activityId: session.productId,
            packId: pack?.productId,
            contentId: session.contentId,
            consumptionRequired: true,
            activityType: activityType,
            title: session.title,
            image: UrlPathBuilder.prefixSlash(session.imageDetails.todayImage),
            contentCategory: _.get(adParams, "contentCategory", undefined),
            adsEnabled: _.get(adParams, "adsEnabled", undefined)
        }
    }

    static getContentMetaV3(session: FitnessProgramProductResponse, pack?: DIYPack): DiyContentMeta {
        if (session.productType != "DIY") {
            return null
        }
        const activityType = "DIY_FITNESS"
        // disabled ads for diy meditation
        const adParams = AdvertisementUtil.getAddParams(activityType, session.productId)
        return {
            activityId: session.productId,
            packId: pack?.productId,
            contentId: session.contentId,
            consumptionRequired: true,
            activityType: activityType,
            title: session.title,
            image: UrlPathBuilder.prefixSlash(session.imageUrl),
            contentCategory: _.get(adParams, "contentCategory", undefined),
            adsEnabled: _.get(adParams, "adsEnabled", undefined)
        }
    }

    static getProductTypeForActivity(type: ActivityType): ProductType {
        switch (type) {
            case "DIY_FITNESS":
                return "DIY_FITNESS"
            case "DIY_MEDITATION":
                return "DIY_MEDITATION"
        }
    }
}

export default AtlasUtil
