import { ProductBenefitWidget, ProductBenefit, Action } from "@curefit/apps-common"
import { UserContext, Action as VMAction } from "@curefit/vm-models"
import { Patient } from "@curefit/care-common"
import _ = require("lodash")
import { CareUtil } from "./CareUtil"
import { ActiveBundleOrderDetail, Booking, BookingDetail } from "@curefit/albus-client"
import { Timezone, TimeUtil } from "@curefit/util-common"
import { NCIcon, NCIconDetails } from "@curefit/vm-common"
import { NCIconWidgetStyle } from "../eat/nutritionist/NCStyles"
import AppUtil from "./AppUtil"

export function getProductBenefitWidget(): ProductBenefitWidget {
  const data: ProductBenefit[] = [
    {
      title: "Hassle free access",
      subTitle: "Easy, hassle-free access to dieticians, reports, and plans on the app",
      icon: "image/icons/nc/tailored_nc2.png"
    },
    {
      title: "Manage Your Health",
      subTitle: "Specialists in lifestyle disease management",
      icon: "image/icons/nc/health_nc2.png"
    },
    {
      title: "Goal-focused",
      subTitle: "Lifestyle guidance along with goal based diet plan",
      icon: "image/icons/nc/goal_nc2.png"
    },
    {
      title: "Learn from Experts",
      subTitle: "Certified experts with years of experience",
      icon: "image/icons/nc/expert_nc.png"
    }]
  return {
    widgetType: "PRODUCT_BENEFIT_WIDGET",
    header: { title: "Why Dietician Consulting?" },
    data: data
  }
}

export function getPreBookingActions(userContext: UserContext, numberOfSessions: number, showPatientSelectionModal: boolean, offerIds: string[], url: string, patientsList: Patient[], title?: string): any[] {
  if (!userContext.sessionInfo.isUserLoggedIn) {
    return [
      {
        actionType: "SHOW_ALERT_MODAL",
        title: !_.isNil(title) ? title : `Book ${numberOfSessions} Sessions`,
        meta: {
          title: "Login Required!",
          subTitle: "Please login to continue",
          actions: [{ actionType: "LOGOUT", title: "Login" }]
        }
      }
    ]
  } else {
    const actionTitle = !_.isNil(title) ? title  : `Book ${numberOfSessions} ${numberOfSessions > 1 ? "Sessions" : "Session"}`
    if (!_.isEmpty(offerIds)) {
      url += `&offerIds=${offerIds.join(",")}`
    }
    const action: Action = {
      title: actionTitle,
      actionType: "NAVIGATION",
      url: url,
    }
    if (showPatientSelectionModal) {
      const pageAction = CareUtil.getPatientSelectionModalAction(patientsList, action, actionTitle)
      return [pageAction]
    } else {
      return [action]
    }
  }
}

export function getMembershipState(membership: BookingDetail, tz: Timezone) {
  if (_.get(membership, "bundleOrderResponse.startTimeEpoch") && _.get(membership, "bundleOrderResponse.expiryTimeEpoch")) {
  const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDateWithTimezone(tz), tz)
  const endDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.bundleOrderResponse.expiryTimeEpoch)), tz)
  const startDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.bundleOrderResponse.startTimeEpoch)), tz)
  const numDaysToEndFromToday = endDate.diff(today, "days")
  if (startDate > today) {
      return "UPCOMING"
  }
  if (endDate < today) {
      return "EXPIRED"
  }
  if (numDaysToEndFromToday < 10) {
      return "EXPIRING"
  }
  return "ACTIVE"
  } else {
  return undefined
  }
}

export function getNutritionistCLPUrl() {
  return "curefit://nutritionistclp"
}

export function getSingleSessionAboutSection() {
  return "Our nutrition expert will use this 60-minute session to chat with you about your lifestyle, habits, diet, sleep, and general wellness, to identify trouble spots. They will then work with you to curate a diet plan that is easy to follow and sustainable, based on your food preferences and health goals.\n\nA 1 session pack is ideal for someone looking to change their eating habits, and to set & achieve simple goals."
}

export function getNCProductDescriptionPageAction(productId: string, userContext: UserContext, slugValue?: string, subCategoryCode?: string): VMAction {
  return {
    actionType: "NAVIGATION",
    title: "VIEW",
    url: AppUtil.isWeb(userContext) ? `/care/dietician/${slugValue}/${productId}` : `curefit://nutritionistpdp?productId=${productId}&subCategoryCode=${subCategoryCode}`
  }
}

export const getNCIconFromType = (NCIcon: NCIcon): {
  title?: string;
  icon?: NCIcon;
  style?: any;
} => {
  const iconDetails = NCIconDetails[NCIcon]
  if (_.isNil(iconDetails)) {
    return undefined
  }
  return {
    ...iconDetails,
    style: NCIconWidgetStyle
  }

}
