import { ISegmentService, UserContext } from "@curefit/vm-models"
import AppUtil, { SUPPORT_DEEP_LINK } from "../util/AppUtil"
import { Action, InfoCard, PageTypes } from "@curefit/apps-common"
import * as moment from "moment"
import { Banner, BannerCarouselWidget } from "../page/PageWidgets"
import { CenterSchedule, Header, ManageOptions, PauseInfo, ProductListWidget, Timing } from "../common/views/WidgetView"
import {
    CenterEffectiveHoliday,
    GymfitCenter,
    GymFitCenterSchedule,
    GymfitCenterType,
    GymfitCheckIn,
    GymfitCheckInState,
    GymfitCity,
    GymfitMembership,
} from "@curefit/gymfit-common"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { TimeSlotOption } from "../cult/ClassListViewBuilderV2"
import { IGymfitService } from "@curefit/gymfit-client"
import { capitalise } from "../util/StringUtil"
import ActionUtil from "./ActionUtil"
import { ComplimentaryAccessMembership } from "@curefit/cult-client"
import { MoneyBackOfferDetail } from "../cult/CultBusiness"
import { Order } from "@curefit/order-common"
import { IOrderService } from "@curefit/oms-api-client"
import GymfitPackPageConfig from "../gymfit/GymfitPackPageConfig"
import { Logger } from "@curefit/base"
import { AlertError } from "../common/errors/AlertError"
import { SeoUrlParams } from "@curefit/base-utils"
import { Benefit, BenefitType, Membership } from "@curefit/membership-commons"
import { CardDescription } from "@curefit/vm-models/dist/src/models/widgets/HorizontalCardListingWidget"
import { HamletBusiness } from "@curefit/hamlet-node-sdk"
import { ISegmentationClient } from "@curefit/segmentation-service-client"
import { IMembershipService } from "@curefit/membership-client"
import CultUtil, { isMembershipCurrent } from "./CultUtil"
import { LocationDataKey, LocationPreferenceRequestEntity } from "./UserUtil"
import { CenterResponse, CenterScheduleResponse } from "@curefit/center-service-common"
import { PtSession } from "@curefit/personal-training-v2-common"
import { ProductType } from "@curefit/product-common"
import { OfflineFitnessPack, Visibility } from "@curefit/pack-management-service-common"
import _ = require("lodash")
import { MembershipItemUtil } from "./MembershipItemUtil"
import WidgetBuilder from "../page/vm/WidgetBuilder"

const DAYS_IN_WEEK = ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"]
const DAYS_TITLE_MAP: { [key: string]: string } = {
    MONDAY: "Mon",
    TUESDAY: "Tue",
    WEDNESDAY: "Wed",
    THURSDAY: "Thu",
    FRIDAY: "Fri",
    SATURDAY: "Sat",
    SUNDAY: "Sun"
}
const DAYS_IN_WEEK_PRIORITY: { [key: string]: number } = {
    MONDAY: 0,
    TUESDAY: 1,
    WEDNESDAY: 2,
    THURSDAY: 3,
    FRIDAY: 4,
    SATURDAY: 5,
    SUNDAY: 6
}
export const GYM_CLOSED_TITLE = "Closed"
export declare type GymMembershipState = "CREATED" | "CANCELLED" | "EXPIRED" | "UPCOMING" | "EXPIRING" | "ACTIVE" | "PAUSED"
export const CHECKIN_ALLOWED_SEGMENTS = ["AI Trainer - trainers new", "AI Trainer beta launch trainers", "New AI Trainer beta users", "Internal Users",
    "VIP Trial members", "Gym Pack Purchased [End Date after today]", "cult_members"]
export const CHECKIN_SUPPORTED_VERSION = 8.93
export const OPEN_PLAN_ACTION_SUPPORTED_VERSION = 9.34
const SEARCH_WIDGET_ID = process.env.ENVIRONMENT === "PRODUCTION" || process.env.ENVIRONMENT === "ALPHA" ? "73c5c701-6cee-4eb3-8774-6a1e2e411663" : "4a223e7e-a84b-4dfa-a489-26e22d60967f"
const PROD_CHECKIN_QUEUE = "production-gymfit-checkin-event-notification-queue"
const STAGE_CHECKIN_QUEUE = "gymfit-checkin-event-notification-queue"
export const SHOW_PREMIUM_GYMS_VERSION = 8.97
export const GYM_MEMBERSHIP_PAUSE_ENABLED_VERSION = 9.16 // todo: Update the pause version
const BUFFER_TIME = 2000 // 2 seconds
export const ELITE_CENTER_TAG = "/image/icons/cult/elite_tag_detail.png"
export const PRO_CENTER_TAG = "/image/icons/cult/pro_tag_detail.png"
export const ONEPASS_GYM_ACTIVITY_ID = 35
export const GYM_MEMBERSHIP_PRIMARY_BENEFITS: Benefit[] = [
    {
        name: "GYMFIT_GA",
        allowOverlap: false,
        type: BenefitType.STATIC,
    },
]
export const PRO_MEMBERSHIPS_PRIMARY_BENEFITS: Benefit[] = [
    {
        name: "GYMFIT_GA",
        allowOverlap: false,
        type: BenefitType.STATIC,
    }
]
export const LUX_MEMBERSHIPS_PRIMARY_BENEFITS: Benefit[] = [{
    name: "LUX",
    allowOverlap: false,
    type: BenefitType.STATIC,
}]

export const LUX_MEMBERSHIPS_ALLOWOVERLAP_BENEFITS: Benefit[] = [{
    name: "LUX",
    allowOverlap: true,
    type: BenefitType.STATIC,
}]
export const GYM_PT_MEMBERSHIPS_PRIMARY_BENEFITS: Benefit[] = [{
    name: "GYMFIT_PERSONAL_TRAINING",
    allowOverlap: false,
    type: BenefitType.STATIC,
}]

class GymfitUtil {

    static DISTANCE_THREHOLD = 25 * 1000
    static LOCALITY_SEARCH_THRESHOLD = 5 * 1000
    static DISTANCE_SEARCH_THRESHOLD = 10 * 1000
    static GYM_PAGE_PARAMS = `pageName=CultGyms&pageType=CultGyms&pageId=gymfitList&subVertical=gymsclp`
    static LOCALITY_SEARCH_KEY = "current_location"
    static NEW_SLOT_BOOKING_VERSION = 8.62
    static BRAND_MAG_IMAGE = "/image/packs/gymfit/GYMFIT24/2_mag.jpg"
    static PREMIUM_GYMS_SUPPORTED_SEGMENTS = "Elite gyms roll out"

    static ENTERPRISE_JPMC_PRODUCT_IDS = ["CULTPACK1760", "CULTPACK1761", "CULTPACK1762", "CULTPACK1763", "CULTPACK1764", "CULTPACK1765", "CULTPACK1766"] // PMS::TODO: add pmsIds

    static getImportantUpdatesWidget(userContext: UserContext, layoutProps?: any, headerStyle?: any, isModal?: boolean, titleStyle?: any): BannerCarouselWidget {
        const bannerWidth = 134
        const bannerHeight = 188
        // {
        //     id: "surface_coating",
        //     image: "/image/gymfit/surface_coating_new.png",
        //     action: {
        //         actionType: "SHOW_INFOGRAPHIC_MODAL",
        //         meta: {
        //             imageUrl: "/image/icons/cult/attendance_instruction.png",
        //             closeIconUrl: "/image/icons/cult/cancel-modal.png",
        //             widgets: [this.getSurfaceCoatingWidget()],
        //             style
        //         }
        //     }
        // },
        const banners: Banner[] = [
            {
                id: "sanitization",
                image: "/image/gymfit/services/sanitization_v2.png",
                action: {
                    actionType: "SHOW_INFOGRAPHIC_MODAL",
                    meta: {
                        imageUrl: "/image/gymfit/services/cleaning.png",
                        closeIconUrl: "/image/icons/cult/cancel-modal.png",
                        widgets: [this.getSanitizationInfoWidget()]
                    }
                }
            },
            {
                id: "social_distance",
                image: "/image/gymfit/services/social_distance_v2.png",
                action: {
                    actionType: "SHOW_INFOGRAPHIC_MODAL",
                    meta: {
                        imageUrl: "/image/gymfit/services/social_distance.png",
                        closeIconUrl: "/image/icons/cult/cancel-modal.png",
                        widgets: [this.getSocialDistanceInfoWidget()]
                    }
                }
            },
            {
                id: "check_in",
                image: "/image/gymfit/services/safety_v2.png",
                action: {
                    actionType: "SHOW_INFOGRAPHIC_MODAL",
                    meta: {
                        imageUrl: "/image/gymfit/services/safety_check.png",
                        closeIconUrl: "/image/icons/cult/cancel-modal.png",
                        widgets: [this.getGymSafetyInfoWidget()]
                    }
                }
            }]
        const widget = new BannerCarouselWidget("134:188", banners, {
            bannerWidth: bannerWidth,
            bannerHeight: bannerHeight,
            v2: true,
            noVerticalPadding: true,
            backgroundColor: "#ffffff",
            noAutoPlay: true,
            containerStyle: { marginLeft: 20 },
            ...layoutProps
        }, 4, false, false)
        widget.hasDividerBelow = false
        widget.header = {
            title: "Safety Measures",
            titleProps: {
                style: { ...GymfitUtil._getWebCarouselWidgetTitleStyle(userContext, isModal), ...titleStyle }
            },
            style: headerStyle
        }
        if (AppUtil.isWeb(userContext)) {
            widget.widgetType = "WEB_CAROUSEL_WIDGET"
        } else {
            widget.widgetType = "IMAGE_LIST_WIDGET"
        }
        return widget
    }

    static getWhatYouGetWidget(userContext: UserContext, cultBenefit?: Benefit, layoutProps?: any, headerStyle?: any): BannerCarouselWidget {
        const bannerWidth = 134
        const bannerHeight = 180

        const banners: Banner[] = [
            {
                id: "unlimited_banner",
                image: "/image/gymfit/services/gym_access_1.png",
                action: {
                    actionType: "SHOW_INFOGRAPHIC_MODAL",
                    meta: {
                        imageUrl: "/image/gymfit/services/gym_access_header.png",
                        closeIconUrl: "/image/icons/cult/cancel-modal.png",
                        widgets: [this.getUnlimitedClassesWidget()]
                    }
                }
            }]
        if (userContext.sessionInfo.osName.toLowerCase() !== "ios") {
            banners.push({
                id: "unlimited_live_workout",
                image: "/image/gymfit/services/unlimited_live.png",
                action: {
                    actionType: "SHOW_INFOGRAPHIC_MODAL",
                    meta: {
                        imageUrl: "/image/gymfit/services/live_workouts.png",
                        closeIconUrl: "/image/icons/cult/cancel-modal.png",
                        widgets: [this.getUnlimitedLiveWorkoutWidget()]
                    }
                }
            })
        }
        const cultClassBenefitUrl = "/image/banners/elite_access.png"
        banners.push({
            id: "free_cult_banner",
            image: cultClassBenefitUrl,
            action: {
                actionType: "SHOW_INFOGRAPHIC_MODAL",
                meta: {
                    imageUrl: "/image/gymfit/services/free_cult_classes.png",
                    closeIconUrl: "/image/icons/cult/cancel-modal.png",
                    widgets: cultBenefit != null ? [this.getFreeCultClassesWidget(cultBenefit)] : null
                }
            }
        },
            {
                id: "quality_banner",
                image: "/image/gymfit/services/safety_quality_1.png",
                action: {
                    actionType: "SHOW_INFOGRAPHIC_MODAL",
                    meta: {
                        imageUrl: "/image/gymfit/services/best_quality.png",
                        closeIconUrl: "/image/icons/cult/cancel-modal.png",
                        widgets: [this.getSafetyAndQualityWidget()]
                    }
                }
            },
            {
                id: "plan_banner",
                image: "/image/gymfit/services/fp_1.png",
                action: {
                    actionType: "SHOW_INFOGRAPHIC_MODAL",
                    meta: {
                        imageUrl: "/image/gymfit/services/workout_plan.png",
                        closeIconUrl: "/image/icons/cult/cancel-modal.png",
                        widgets: [this.getFitnessPlanWidget()]
                    }
                }
            },
            {
                id: "cult_certified_trainer",
                image: "/image/gymfit/services/cult_certified_trainers.png",
                action: {
                    actionType: "SHOW_INFOGRAPHIC_MODAL",
                    meta: {
                        imageUrl: "/image/gymfit/services/certified_trainer.png",
                        closeIconUrl: "/image/icons/cult/cancel-modal.png",
                        widgets: [this.getCultTrainerWidget()]
                    }
                }
            })
        const widget = new BannerCarouselWidget("134:188", banners, {
            bannerWidth: bannerWidth,
            bannerHeight: bannerHeight,
            v2: true,
            noVerticalPadding: true,
            backgroundColor: "#ffffff",
            noAutoPlay: true,
            containerStyle: { marginLeft: 20 },
            ...layoutProps
        }, 4, false, false)
        widget.hasDividerBelow = false
        widget.header = {
            title: "What You Get",
            titleProps: {
                style: {
                    fontSize: 18,
                    marginLeft: 5,
                }
            },
            style: headerStyle
        }
        if (AppUtil.isWeb(userContext)) {
            widget.widgetType = "WEB_CAROUSEL_WIDGET"
        } else {
            widget.widgetType = "IMAGE_LIST_WIDGET"
        }
        return widget
    }

    static getUnlimitedClassesWidget(): ProductListWidget {
        const header: Header = {
            title: "Unlimited access to PRO gyms",
            color: "#000000",
            style: { fontSize: 22, color: "#000000" }
        }
        const items: InfoCard[] = [
            {
                subTitle: "Unlimited access to all PRO gyms in your city"
            },
            {
                subTitle: "No restrictions on time spent at the gym or the number of times you can workout in a day"
            }]
        const widget = new ProductListWidget("BULLET", header, items)
        widget.style = {
            paddingHorizontal: 20
        }
        return widget
    }

    static getUnlimitedLiveWorkoutWidget(): ProductListWidget {
        const header: Header = {
            title: "Get unlimited access to all the LIVE workouts",
            color: "#000000",
            style: { fontSize: 22, color: "#000000" }
        }
        const items: InfoCard[] = [
            {
                subTitle: "Fitness, Dance & Meditation classes"
            },
            {
                subTitle: "Celebrity Masterclasses"
            },
            {
                subTitle: "LIVE leaderboard with energy meter"
            }]
        const widget = new ProductListWidget("BULLET", header, items)
        widget.style = {
            paddingHorizontal: 20
        }
        return widget
    }

    static getFreeCultClassesWidget(cultBenefit?: Benefit): ProductListWidget {
        const header: Header = {
            title: `${cultBenefit?.maxTickets} sessions per month at ELITE gyms or cult centers`,
            color: "#000000",
            style: { fontSize: 22, color: "#000000" }
        }
        const items: InfoCard[] = [
            {
                subTitle: "Try out sessions at any of the ELITE gyms or cult centers in your city"
            },
            {
                subTitle: "Access all cult formats including (Dance Fitness, S&C, HRX, Boxing and Yoga)"
            }]
        const widget = new ProductListWidget("BULLET", header, items)
        widget.style = {
            paddingHorizontal: 20
        }
        return widget
    }

    static getSafetyAndQualityWidget(): ProductListWidget {
        const header: Header = {
            title: "Best-in-class quality & safety",
            color: "#000000",
            style: { fontSize: 22, color: "#000000" }
        }
        const items: InfoCard[] = [
            {
                subTitle: "Temperature & SpO2 check at entry"
            },
            {
                subTitle: "Machines sanitized before every use"
            },
            {
                subTitle: "Guided movement to ensure social distancing"
            },
            {
                subTitle: "Best in class equipment & amenities"
            }]
        const widget = new ProductListWidget("BULLET", header, items)
        widget.style = {
            paddingHorizontal: 20
        }
        return widget
    }

    static getFitnessPlanWidget(): ProductListWidget {
        const header: Header = {
            title: "Personalised workout plan",
            color: "#000000",
            style: { fontSize: 22, color: "#000000" }
        }
        const items: InfoCard[] = [
            {
                subTitle: "Body composition check"
            },
            {
                subTitle: "Fitness test"
            },
            {
                subTitle: "Personalised workout plan based on fitness goal and level"
            }]
        const widget = new ProductListWidget("BULLET", header, items)
        widget.style = {
            paddingHorizontal: 20
        }
        return widget
    }

    static getCultTrainerWidget(): ProductListWidget {
        const header: Header = {
            title: "Workout under the supervision of cult certified trainers",
            color: "#000000",
            style: { fontSize: 22, color: "#000000" }
        }
        const items: InfoCard[] = [
            {
                subTitle: "Expert trainers to assist you with your workout"
            },
            {
                subTitle: "Demonstrations and instructions for new exercises"
            },
            {
                subTitle: "Posture correction and guidance to workout the right way"
            }]
        const widget = new ProductListWidget("BULLET", header, items)
        widget.style = {
            paddingHorizontal: 20
        }
        return widget
    }

    static getCovidCentreChangesInstructionWidget(): ProductListWidget {
        const header: Header = {
            title: "Changes in center facilities",
            color: "#000000",
            style: { fontSize: 22, color: "#000000" }
        }
        const items: InfoCard[] = [
            {
                subTitle: "All the equipment & floor will be sanitised after every session"
            },
            {
                subTitle: "Weights & resistance bands will be placed in your station number. You can also self sanitise the equipment before using"
            },
            {
                subTitle: "Please don’t carry any valuables to the center, lockers will not be accessible"
            },
            {
                subTitle: "Shower facility will not be accessible, you can use washrooms but please wait in the queue for your turn"
            }]
        const widget = new ProductListWidget("BULLET", header, items)
        widget.style = {
            paddingHorizontal: 20
        }
        return widget
    }

    static getCovidSafetyInstructionWidget(): ProductListWidget {
        const header: Header = {
            title: "We request your support",
            color: "#000000",
            style: { fontSize: 22, color: "#000000" }
        }
        const items: InfoCard[] = [
            {
                subTitle: "In case you are unwell, we request you to stay at home and rest. It’s important not only for your safety but also for the health of your fellow members and employees"
            },
            {
                subTitle: "Be honest and update your health status on the Aarogya setu app, we will be using the same to gauge risks if any"
            }]
        const widget = new ProductListWidget("BULLET", header, items)
        widget.style = {
            paddingHorizontal: 20
        }
        return widget
    }

    static getSurfaceCoatingWidget(): ProductListWidget {
        const header: Header = {
            title: "Antimicrobial Surface coating",
            color: "#000000",
            style: { fontSize: 22, color: "#000000" }
        }
        const items: InfoCard[] = [
            {
                subTitle: " Self sanitizing coating"
            },
            {
                subTitle: "Kills all virus/bacteria on contact"
            },
            {
                subTitle: "All high touch surfaces protected"
            },
            {
                subTitle: "TiO2 (Titanum Dioxide) based"
            },
            {
                subTitle: "Once done, protects for a year"
            }]
        const widget = new ProductListWidget("BULLET", header, items)
        widget.style = {
            paddingHorizontal: 20
        }
        return widget
    }

    static getSanitizationInfoWidget(): ProductListWidget {
        const header: Header = {
            title: "Best-in-class safety",
            color: "#000000",
            style: { fontSize: 22, color: "#000000" }
        }
        const items: InfoCard[] = [
            {
                subTitle: "Sanitizer sprays & wipes to remove sweat"
            },
            {
                subTitle: "Used before and after every use"
            },
            {
                subTitle: "Members encouraged to self-sanitise"
            },
            {
                subTitle: "Housekeeping staff available if members forget"
            },
            {
                subTitle: "Continuous ventilation by exhaust fans"
            }]
        const widget = new ProductListWidget("BULLET", header, items)
        widget.style = {
            paddingHorizontal: 20
        }
        return widget
    }

    static getSocialDistanceInfoWidget(): ProductListWidget {
        const header: Header = {
            title: "Social distancing at all times",
            color: "#000000",
            style: { fontSize: 22, color: "#000000" }
        }
        const items: InfoCard[] = [
            {
                subTitle: " Distance between machines"
            },
            {
                subTitle: "Floor marked waiting boxes"
            },
            {
                subTitle: "Check-ins to limit crowd"
            }]
        const widget = new ProductListWidget("BULLET", header, items)
        widget.style = {
            paddingHorizontal: 20
        }
        return widget
    }

    static getGymSafetyInfoWidget(): ProductListWidget {
        const header: Header = {
            title: "Safety checks at gym entry",
            color: "#000000",
            style: { fontSize: 22, color: "#000000" }
        }
        const items: InfoCard[] = [
            {
                subTitle: "Temperature check via IR thermometer"
            },
            {
                subTitle: "SpO2 check via Oximeter"
            },
            {
                subTitle: "Shoe sanitisation before changing to separate workout shoes"
            },
            {
                subTitle: "Arrogya Setu app check on phone"
            },
            {
                subTitle: " Check-in via QR code on cult.fit app"
            }]
        const widget = new ProductListWidget("BULLET", header, items)
        widget.style = {
            paddingHorizontal: 20
        }
        return widget
    }

    static getMarkAttendanceAction(tz: Timezone, checkIn: GymfitCheckIn, isOnePassCenter?: boolean): Action {
        if (!_.isNil(checkIn.qrCodeAttendance) && checkIn.state === GymfitCheckInState.CREATED) {
            const currentTime = TimeUtil.getEpochFromDate(TimeUtil.getDateNow(tz))
            const endTime = TimeUtil.getEpochFromDate(new Date(checkIn.endTime))
            const startTime = TimeUtil.getEpochFromDate(new Date(checkIn.startTime))
            // For onepass only enable check in button 30 mins before class time
            const isDisabled = isOnePassCenter && (startTime - currentTime) >= 30 * 60
            if (currentTime > endTime) {
                return null
            }
            return {
                actionType: "SHOW_QR_CODE_MODAL",
                title: "CHECK-IN",
                meta: {
                    closeIconUrl: "/image/icons/cult/cancel-modal.png",
                    title: "Check-in",
                    qrCodeString: isDisabled ? null : checkIn.qrCodeAttendance?.qrString,
                    passCode: isDisabled ? null : checkIn.passCode,
                    noQRCodeMessage: "",
                    footer: {
                        title: "",
                        subTitle: isDisabled ? "Check-in QR code will be generated 30 mins before check-in time" : "To check-in, scan QR code at gym entry"
                    },
                    eventData: {
                        extraParams: {
                            source: "gymfit_booked_session"
                        }
                    }
                }
            }
        }
        return null
    }

    static getUpComingFitnessPlanAction(tz: Timezone, checkIn: GymfitCheckIn): Action {

        return {
            actionType: "NAVIGATION",
            title: "Workout Plan",
            url: "curefit://fitnessplanpage?productId=FITNESS_COACH",
            meta: {
                closeIconUrl: "/image/icons/cult/cancel-modal.png",
                viewStyle: {
                    backgroundColor: "rgba(241, 242, 246,1.0)",
                    paddingHorizontal: 10,
                    paddingVertical: 10,
                    borderRadius: 5,
                    flexDirection: "row",
                    flex: 1,
                    justifyContent: "space-between",
                    alignContent: "center",
                    alignItems: "center",
                },
                textStyle: {
                    fontFamily: "Inter-Medium",
                    fontSize: 13,
                    fontWeight: "500",
                    color: "#000000"
                },
                imageStyle: {
                    height: 10,
                    width: 10,
                    tintColor: "rgba(111, 111, 111,1.0)",

                },
                showImage: true,
                checkin: true
            },
        }
    }

    static getGymClassCheckoutAction(checkIn: GymfitCheckIn): Action {
        return {
            actionType: "GYM_CHECKOUT",
            title: "Checkout",
            url:  SUPPORT_DEEP_LINK,
            meta: { url: `/gymfit/checkout/${checkIn.id}` },
        }
    }

    static getFormattedGymDistance(distanceInMt?: number): string {
        if (!_.isFinite(distanceInMt) || distanceInMt > GymfitUtil.DISTANCE_THREHOLD) {
            return ""
        }
        const distanceInKm = distanceInMt / 1000
        return `${Math.round(distanceInKm * 10) / 10} km`
        // if (distanceInMt >= 1000) {
        //     const distanceInKm = distanceInMt / 1000
        //     return `${Math.round(distanceInKm * 10) / 10} km` // round to nearest one digit
        // } else {
        //     return `${Math.round(distanceInMt)} m` // round to nearest mtr
        // }
    }

    static async shouldShowGymLocalitySelector(serviceInterfaces: CFServiceInterfaces, userContext: UserContext): Promise<boolean> {
        if (userContext.sessionInfo.lat && userContext.sessionInfo.lon) {
            return false
        } else if (userContext.sessionInfo.isUserLoggedIn) {
            const locality = await serviceInterfaces.gymfitBusiness.getLocalityPreference(userContext)
            return !locality
        }
        return false
    }

    static getTimeSlotOptions(): TimeSlotOption[] {
        return [
            {
                "slot": "morning",
                "icon": "morning",
                "displayText": "Morning",
                "startTime": "06:00:00"
            },
            {
                "slot": "evening",
                "icon": "evening",
                "displayText": "Evening",
                "startTime": "16:00:00"
            }
        ]
    }

    static getCreateCalendarEventAction(userContext: UserContext, session: PtSession|{startTime: number, endTime: number, id: number} , centerDetails: CenterResponse, hamletService: HamletBusiness): Action {
        const sessionInfo = userContext.sessionInfo
        const alarmTime = { date: sessionInfo.osName.toLowerCase() === "ios" ? -30 : 30 }

        return {
            title: "OK",
            actionType: "CREATE_CALENDAR_EVENT",
            payload: {
                showPromptAlways: false,
                calendarData: {
                    classId: session.id.toString(),
                    title: `Cult PT`,
                    location: `${centerDetails.name}\n${centerDetails.fullAddress1} ${centerDetails.fullAddress2}`,
                    startDate: moment.utc(session.startTime).valueOf(),
                    endDate: moment.utc(session.endTime).valueOf(),
                    toastText: "Class added to your calendar",
                    alarms: [alarmTime]
                },
                modalData: {
                    header: "INTRODUCING",
                    title: "Add class to your calendar",
                    subTitle: "Get reminders for this and all your future classes so you never miss a class again!",
                    actions: [
                        {
                            title: "ASK ME LATER"
                        },
                        {
                            title: "GIVE PERMISSION"
                        }
                    ]
                }
            }
        }
    }

    static getDeleteCalendarEventAction(userContext: UserContext,  session: PtSession): Action {

        return {
            actionType: "DELETE_CALENDAR_EVENT",
            payload: {
                classId: session.id.toString(),
                title: `Cult PT`,
                startDate: moment.utc(session.startTime).valueOf(),
                endDate: moment.utc(session.endTime).valueOf(),
            }
        }
    }

    static async getDefaultLocality(gymfitService: IGymfitService, cityId: string): Promise<string> {
        const defaultLocalities: GymfitCity[] = await gymfitService.getDefaultLocalities(cityId) || []
        const defaultLocalityForCity: GymfitCity = defaultLocalities.find(city => city.cityId === cityId)
        return defaultLocalityForCity && defaultLocalityForCity.defaultLocality
    }

    static getExploreGymsAction(ctaText?: string, queryString?: string): Action {
        return { actionType: "NAVIGATION", title: `${ctaText ? ctaText : "BOOK SESSION"}`, url: `curefit://allgyms?centerType=GYM${queryString ? "&" + queryString : ""}` }
    }

    static getOnePassSlotBookingAction(userContext: UserContext, centerId: number, actionTitle?: string): Action {
        return { actionType: "NAVIGATION", title: actionTitle ?? "BOOK", url: `curefit://selectCareDateV1?productId=GYM_WORKOUT&vertical=ONEPASS&centerId=${centerId}&isGymPage=true&isOnePassGymOnly=true` }
    }
    static getSlotBookingAction(userContext: UserContext, centerId: number, actionTitle?: string): Action {
        if (userContext.sessionInfo.appVersion >= GymfitUtil.NEW_SLOT_BOOKING_VERSION) {
            return { actionType: "NAVIGATION", title: actionTitle ?? "BOOK", url: `curefit://selectCareDateV1?productId=GYM_WORKOUT&vertical=CULTFIT&centerId=${centerId}&isGymPage=true` }
        }
        return { actionType: "NAVIGATION", title: actionTitle ?? "BOOK", url: `curefit://centergroupschedule?centerId=${centerId}` }
    }

    static getGymHolidayMessage(userContext: UserContext, gymfitCenter: GymfitCenter, holiday: CenterEffectiveHoliday) {
        const timezone = userContext.userProfile.timezone
        const startTime = TimeUtil.formatEpochInTimeZone(timezone, holiday.startTime, "h:mm a")
        const endTime = TimeUtil.formatEpochInTimeZone(timezone, holiday.endTime, "h:mm a")
        const holidayStartDay = TimeUtil.parseDateFromEpochWithTimezone(timezone, holiday.startTime)
        const holidayEndDay = TimeUtil.parseDateFromEpochWithTimezone(timezone, holiday.endTime)
        if (holidayStartDay.getDate() === holidayEndDay.getDate()) {
            return `${gymfitCenter.name} ${gymfitCenter.locality ? `(${gymfitCenter.locality}) ` : ""}will be closed on ${TimeUtil.formatEpochInTimeZone(timezone, holiday.startTime, "dddd MMMM Do YYYY")} from ${startTime} to ${endTime} due to ${holiday.reason}`
        }
        return `${gymfitCenter.name} ${gymfitCenter.locality ? `(${gymfitCenter.locality}) ` : ""}will be closed from ${startTime} on ${TimeUtil.formatEpochInTimeZone(timezone, holiday.startTime, "dddd MMMM Do YYYY")} to ${endTime} on ${TimeUtil.formatEpochInTimeZone(timezone, holiday.endTime, "dddd MMMM Do YYYY")} due to ${holiday.reason}`
    }

    static getScheduleTimes(centerSchedules: GymFitCenterSchedule[], scheduleTimeSeparator?: string): CenterSchedule[] {
        const separator = scheduleTimeSeparator || ", "
        const schedules: CenterSchedule[] = []
        const scheduleByDay: { [key: string]: number } = {}
        centerSchedules.sort((day1, day2) => DAYS_IN_WEEK_PRIORITY[day1.dayOfWeek] < DAYS_IN_WEEK_PRIORITY[day2.dayOfWeek] ? -1 : 1)
        centerSchedules.forEach((centerSchedule) => {
            const fromTimeSplit = centerSchedule.fromTime.split(":")
            const toTimeSplit = centerSchedule.toTime.split(":")
            if (scheduleByDay.hasOwnProperty(centerSchedule.dayOfWeek)) {
                const existingSchedule = schedules[scheduleByDay[centerSchedule.dayOfWeek]]
                if (fromTimeSplit.length > 2 && toTimeSplit.length > 2) {
                    existingSchedule.value = `${existingSchedule.value}${separator}${TimeUtil.get12HourTimeFormat(parseInt(fromTimeSplit[0]), parseInt(fromTimeSplit[1]), true)} - ${TimeUtil.get12HourTimeFormat(parseInt(toTimeSplit[0]), parseInt(toTimeSplit[1]), true)}`
                    existingSchedule.timing.push(GymfitUtil._getTiming(fromTimeSplit, toTimeSplit))
                }
            } else {
                const title = capitalise(centerSchedule.dayOfWeek.toUpperCase())
                let value = ""
                const timing: Timing[] = []
                if (fromTimeSplit.length > 2 && toTimeSplit.length > 2) {
                    value = `${TimeUtil.get12HourTimeFormat(parseInt(fromTimeSplit[0]), parseInt(fromTimeSplit[1]), true)} - ${TimeUtil.get12HourTimeFormat(parseInt(toTimeSplit[0]), parseInt(toTimeSplit[1]), true)}`
                    timing.push(GymfitUtil._getTiming(fromTimeSplit, toTimeSplit))
                }
                scheduleByDay[centerSchedule.dayOfWeek] = schedules.push({
                    title,
                    value,
                    dimText: false,
                    timing
                }) - 1
            }
        })
        for (let dayIndex = 0; dayIndex < DAYS_IN_WEEK.length; dayIndex++) {
            if (!schedules[dayIndex] || capitalise(schedules[dayIndex].title) !== capitalise(DAYS_IN_WEEK[dayIndex])) {
                schedules.splice(dayIndex, 0, { title: DAYS_IN_WEEK[dayIndex], value: GYM_CLOSED_TITLE, dimText: true })
            }
        }
        return schedules
    }

    static getTodaysSchedule(centerSchedules: CenterSchedule[]): CenterSchedule | undefined {
        const dayOfWeek = capitalise(DAYS_IN_WEEK[(new Date().getDay() + 6) % DAYS_IN_WEEK.length])
        return centerSchedules.find((schedule: CenterSchedule) => schedule.title === dayOfWeek)
    }

    static isGymOpen(schedule: CenterSchedule, gymfitCenter: GymfitCenter): boolean {
        if (!gymfitCenter.isOperational) {
            return false
        }
        if (schedule) {
            if (schedule.value === GYM_CLOSED_TITLE) {
                return false
            }
            const date = new Date().getTime()
            const hasActiveHoliday = gymfitCenter?.effectiveHolidays?.some(holiday => holiday.startTime <= date && holiday.endTime >= date)
            if (hasActiveHoliday) {
                return false
            }
            return schedule?.timing.some(timing => timing.startTime <= date && date <= timing.endTime)
        }
        return false
    }

    /**
     * helper function to merge Gyms with same timing for consecutive days
     * @param schedule
     */
    static mergeSameDaySchedule(schedule: CenterSchedule[]): CenterSchedule[] {
        const mergedSchedule: CenterSchedule[] = []
        if (_.isEmpty(schedule)) {
            return []
        }
        let prevStartDayIndex = 0
        let prevEndDayIndex = -1
        for (let i = 1; i < schedule.length; i++) {
            if (schedule[prevStartDayIndex].value === schedule[i].value) {
                prevEndDayIndex = i
            } else {
                if (prevEndDayIndex === -1) {
                    mergedSchedule.push({
                        title: GymfitUtil.getDayOfWeekShortName(schedule[prevStartDayIndex].title),
                        value: schedule[prevStartDayIndex].value,
                        dimText: schedule[prevStartDayIndex].dimText,
                    })
                } else {
                    mergedSchedule.push({
                        title: `${GymfitUtil.getDayOfWeekShortName(schedule[prevStartDayIndex].title)} - ${GymfitUtil.getDayOfWeekShortName(schedule[prevEndDayIndex].title)}`,
                        value: schedule[prevStartDayIndex].value,
                        dimText: schedule[prevStartDayIndex].dimText,
                    })
                }
                prevStartDayIndex = i
                prevEndDayIndex = -1
            }
        }
        if (prevEndDayIndex === -1) {
            mergedSchedule.push({
                title: GymfitUtil.getDayOfWeekShortName(schedule[prevStartDayIndex].title),
                value: schedule[prevStartDayIndex].value,
                dimText: schedule[prevStartDayIndex].dimText,
            })
        } else {
            mergedSchedule.push({
                title: `${GymfitUtil.getDayOfWeekShortName(schedule[prevStartDayIndex].title)} - ${GymfitUtil.getDayOfWeekShortName(schedule[prevEndDayIndex].title)}`,
                value: schedule[prevStartDayIndex].value,
                dimText: schedule[prevStartDayIndex].dimText,
            })
        }
        return mergedSchedule
    }

    static mergeTimingsForSameDay(schedule: CenterScheduleResponse[]): CenterScheduleResponse[] {
        const mergedScehdule: CenterScheduleResponse[] = []
        if (_.isEmpty(schedule)) {
            return []
        }
        for (let i = 0; i < schedule.length; i++) {
            const dayAlreadyPresentIndex = mergedScehdule.findIndex(item => item.dayOfWeek === schedule[i].dayOfWeek)
            if (dayAlreadyPresentIndex === -1) {
                mergedScehdule.push(schedule[i])
            } else {
                mergedScehdule[dayAlreadyPresentIndex].openingTime = mergedScehdule[dayAlreadyPresentIndex].openingTime + "," + schedule[i].openingTime
                mergedScehdule[dayAlreadyPresentIndex].closingTime = mergedScehdule[dayAlreadyPresentIndex].closingTime + "," + schedule[i].closingTime
                mergedScehdule[dayAlreadyPresentIndex].operationMode = mergedScehdule[dayAlreadyPresentIndex].operationMode + "," + schedule[i].operationMode
            }
        }
        return mergedScehdule
    }

    private static getDayOfWeekShortName(day: string = ""): string {
        return DAYS_TITLE_MAP[day.toUpperCase()]
    }

    static isNewBookingFlowSupported(userContext: UserContext) {
        const appVersion = userContext.sessionInfo.appVersion
        if (appVersion > 8.53) {
            return true
        }
        return false
    }

    static addTitleInActionQuery(action: Action): Action {
        if (action && action.url && action.title) {
            action.url = ActionUtil.addPageParamsToUrl(action.url, `CTA=${(action.title)}`)
        }
        return action
    }

    static getGymEnabledUserSegments(): string[] {
        if (process.env.ENVIRONMENT === "LOCAL" || process.env.ENVIRONMENT === "STAGE") {
            return ["501662ea-0205-47f8-b033-d07baaaab269", "236d9e09-2dea-4695-a0e7-f054c9a4f90f", "a5f877ba-45f5-41c2-9d60-2b9fa43ee05c"]
        }
        return ["3a9bfcc7-e7cc-433b-9b4f-f545ce2de80c", "ca7cbdfa-e1a0-43d4-b1a3-370911593748", "2fa7f706-7224-4474-99b5-8373bee28560"]
    }

    static getGymTrialExhaustedActions(forClp?: boolean): Action[] {
        return forClp ? [{ actionType: "SCROLL_TO_WIDGET", title: "Explore Offers", meta: { widgetId: "e9d099cf-2af4-419b-ad9e-8b1d7528523c-gym" } }] : [{ actionType: "NAVIGATION", title: "Explore Offers", url: "curefit://tabpage?pageId=cult&selectedTab=gymfitList" }]
    }

    static getDayOfWeekShortNameByNumber(day: number) {
        return GymfitUtil.getDayOfWeekShortName(GymfitUtil.getDayOfWeekName(day))
    }

    static async getMembershipPackDetailsUrl(gymfitMembership: GymfitMembership, userContext: UserContext) {
        if (AppUtil.isMembershipDetailV2PageSupported(userContext)) {
            return AppUtil.getNewMembershipDetailPage(gymfitMembership.id.toString(), false)
        }
        return `curefit://gympackdetails?membershipId=${gymfitMembership.id}`
    }

    static async getGoldMembershipDetailsUrl(goldMembership: Membership, userContext: UserContext) {
        if (AppUtil.isMembershipDetailV2PageSupported(userContext)) {
            return AppUtil.getNewMembershipDetailPage(goldMembership.id.toString(), false)
        }
        return `curefit://gympackdetails?membershipId=${goldMembership.id}`
        // return `curefit://gympackdetails?membershipId=${goldMembership.id}&listingCategoryId=${gymfitMembership.listing.listingCategory.id}`
    }

    static async getLuxMembershipDetailsUrl(luxMembership: Membership, userContext: UserContext) {
        if (AppUtil.isMembershipDetailV2PageSupported(userContext)) {
            return AppUtil.getNewMembershipDetailPage(luxMembership.id.toString(), false)
        }
        return `curefit://gympackdetails?membershipId=${luxMembership.id}`
    }

    static getPresentGoldMembership(memberships: Membership[]) {
        const now = Date.now()
        return memberships.find(membership => membership.start <= now && membership.end >= now)
    }

    static async buildBannerWidget(userContext: UserContext, widgetId: string, widgetBuilder: WidgetBuilder, serviceInterfaces: CFServiceInterfaces): Promise<any> {
        const widgetResponse = await widgetBuilder.buildWidgets([widgetId], serviceInterfaces, userContext, undefined, undefined)
        if (!_.isEmpty(widgetResponse) && !_.isEmpty(widgetResponse.widgets)) {
            const widget: any = widgetResponse.widgets[0] && widgetResponse.widgets[0].widgetType === "BANNER_CAROUSEL_WIDGET" ? widgetResponse.widgets[0] : undefined
            return widget
        }
        return null
    }

    static isCurrentGoldMembership(membership: Membership) {
        const now = Date.now()
        return membership.start <= now && membership.end >= now
    }

    static isCurrentMembership(membership: Membership) {
        const now = Date.now()
        return membership.start <= now && membership.end >= now
    }

    private static getDayOfWeekName(day: number = 0): string {
        return DAYS_IN_WEEK[day]
    }

    private static _getWebCarouselWidgetTitleStyle(userContext: UserContext, isModal?: boolean): any {
        const style: any = {
            fontSize: 18,
        }
        if (isModal) return style
        if (AppUtil.isDesktop(userContext)) {
            style.fontSize = 24
        } else if (AppUtil.isMWeb(userContext)) {
            style.fontSize = 18
        }
        return style
    }

    static hasComplimentaryAccess(userContext: UserContext, memberships: ComplimentaryAccessMembership[]): boolean {
        const today = TimeUtil.todaysDateWithTimezone(userContext.userProfile.timezone)
        return memberships.some(membership => today >= membership.startDate && today <= membership.endDate)
    }
    static getGymMembershipState(membershipDetails: GymfitMembership, tz: Timezone): GymMembershipState {
        const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDateWithTimezone(tz), tz)
        const endDate = TimeUtil.getMomentForDateString(membershipDetails.endDate, tz)
        const startDate = TimeUtil.getMomentForDateString(membershipDetails.startDate, tz)
        const numDaysToEndFromToday = endDate.diff(today, "days")

        switch (membershipDetails.state) {
            case "CREATED": {
                if (startDate > today) {
                    return "UPCOMING"
                }
                if (endDate < today) {
                    return "EXPIRED"
                }
                return "ACTIVE"
            }
            case "CANCELLED": {
                return "CANCELLED"
            }
            case "EXPIRED": {
                return "EXPIRED"
            }
            default:
                return membershipDetails.state
        }
    }

    static getGoldMembershipState(membershipDetails: Membership, tz: Timezone): GymMembershipState {
        const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDateWithTimezone(tz), tz)
        const endDate = TimeUtil.getMomentForDate(new Date(membershipDetails.end), tz)
        const startDate = TimeUtil.getMomentForDate(new Date(membershipDetails.start), tz)
        const numDaysToEndFromToday = endDate.diff(today, "days")

        switch (membershipDetails.status) {
            case "PURCHASED": {
                if (startDate > today) {
                    return "UPCOMING"
                }
                if (endDate < today) {
                    return "EXPIRED"
                }
                return "ACTIVE"
            }
            case "SUSPENDED":
            case "CANCELLED": {
                return "CANCELLED"
            }
            case "PAUSED": {
                return "PAUSED"
            }
            default:
                return "CANCELLED"
        }
    }

    public static getGymPackTagAndColor(membershipState: GymMembershipState, numDaysToEndFromToday: number) {
        switch (membershipState) {
            case "ACTIVE":
                return {
                    tag: "ACTIVE",
                    color: "#5bdbb6",
                }

            case "UPCOMING":
                return {
                    tag: "UPCOMING",
                    color: "#6236ff",
                }
            case "EXPIRING":
            case "EXPIRED":
            case "CANCELLED":
                return {
                    tag: membershipState === "CANCELLED" ? "CANCELLED" : "EXPIRED",
                    color: "#b00020",
                }
            case "PAUSED":
                return {
                    tag: "PAUSED",
                    color: "#f5a623"
                }
            default:
                return {
                    tag: "EXPIRED",
                    color: "#b00020",
                }
        }
    }

    private static _getTiming(fromTimeSplit: string[], toTimeSplit: string[]): Timing {
        const start = new Date()
        const end = new Date()
        start.setHours(parseInt(fromTimeSplit[0]), parseInt(fromTimeSplit[1]))
        end.setHours(parseInt(toTimeSplit[0]), parseInt(toTimeSplit[1]))
        return {
            startTime: start.getTime(),
            endTime: end.getTime()
        }
    }

    static async getOngoingCultMembership(userContext: UserContext, membershipService: IMembershipService): Promise<Membership | null> {
        const membershipList = await CultUtil.getEliteMembershipsFromMembershipService(userContext, membershipService)
        const currentMembership = _.find(membershipList, (membership) => isMembershipCurrent(userContext, membership))
        return currentMembership
    }

    static async checkMoneyBackOfferOnMembership(membership: Membership, userContext: UserContext, omsApiClient: IOrderService, gymfitPackPageConfig: GymfitPackPageConfig, logger: Logger): Promise<MoneyBackOfferDetail> {
        let isOfferApplied = false
        let isOfferExpired = false
        const tz = userContext.userProfile.timezone
        if (membership.orderId) {
            let order: Order
            try {
                 order = await omsApiClient.getOrder(membership.orderId)
            }
            catch (err) {
                logger.error("Error while fetching membership order", {orderId: membership.orderId, err})
            }
            if (!_.isEmpty(order)) {
                logger.info(`Moneyback offer -> Checking for money back offer for orderId: ${membership.orderId}`)
                const offerConfig = gymfitPackPageConfig.moneybackOfferInfo
                if (!_.isEmpty(order.offersApplied) && !_.isEmpty(_.intersection(order.offersApplied, offerConfig.offerIds))) {
                    logger.info(`Moneyback offer -> Offer Config: ${JSON.stringify(offerConfig)}`)
                    isOfferApplied = true
                    // const limitInDays: number = offerConfig.limitInDays
                    // const packBuyDate: string = TimeUtil.formatDateInTimeZone(tz, order.createdDate)
                    // const endDate: string = TimeUtil.addDays(tz, packBuyDate, limitInDays)
                    const cancellationWindow = { startDate: TimeUtil.formatEpochInTimeZone(tz, membership.start), endDate: offerConfig.cancellationEndDate }
                    const currentMillis = TimeUtil.getCurrentEpoch()
                    // const endDateInMillis = TimeUtil.getEpochInMillis(tz, endDate)
                    if (currentMillis > offerConfig.offerRedemptionTill) {
                        isOfferExpired = true
                    }
                    logger.info(`Final offer details for User ${userContext.userProfile.userId} is isOfferApplied: ${isOfferApplied} isOfferExpired: ${isOfferExpired} cancellationWindow: ${JSON.stringify(cancellationWindow)}`)
                    return { isOfferApplied, isOfferExpired, cancellationWindow }
                }
            }
        }
    }

    public static getCrossCityCheckinError(ex: any): AlertError {
        const actions: Action[] = [
            {
                actionType: "NAVIGATION",
                title: "Change City",
                url: `curefit://cultfitclp?pageId=cult&selectedTab=gymfitList&widgetId=${SEARCH_WIDGET_ID}`
            },
            {
                actionType: "NAVIGATION",
                title: "Report Issue",
                url: "curefit://support"
            }
        ]
        return new AlertError("Access Denied", ex.meta.message, actions, ex)
    }

    public static getNoActiveMembershipError(ex: any, centerType?: GymfitCenterType): AlertError {
        const actions: Action[] = [
            {
                actionType: "HIDE_ALERT_MODAL",
                title: "Ok"
            }
        ]
        if (centerType) {
            return new AlertError(`Buy ${GymfitUtil.getNewCultPassTitleFromOld(centerType)}`, `You do not have any active membership, please purchase ${GymfitUtil.getNewCultPassTitleFromOld(centerType)} to check-in.`, actions, ex)
        } else {
            return new AlertError("Buy a cultpass", "You do not have any active membership, please purchase cultpass that suits your needs to check-in", actions, ex)
        }
    }

    public static isParqEnabledForQrCheckin(userContext: UserContext) {
        return AppUtil.isWeb(userContext) ? false : userContext.sessionInfo.appVersion > 8.64
    }

    public static async showPremiumGyms(userContext: UserContext, hamletBusiness: HamletBusiness, hasActiveMembership: boolean, segmentationClient: ISegmentationClient) {
        return this.isPremiumGymSupported(userContext)
    }

    public static isPremiumGymSupported(userContext: UserContext) {
        if (AppUtil.isWeb(userContext)) {
            return true
        } else {
            const { appVersion } = userContext.sessionInfo
            return appVersion >= SHOW_PREMIUM_GYMS_VERSION
        }
    }

    public static async getCenterListForUser(gymfitCenters: GymfitCenter[], userContext: UserContext, hamletBusiness: HamletBusiness, hasAnyActiveMembership: boolean, segmentationClient: ISegmentationClient, segmentService: ISegmentService): Promise<GymfitCenter[]> {
        if (!_.isEmpty(gymfitCenters) && !await GymfitUtil.showPremiumGyms(userContext, hamletBusiness, hasAnyActiveMembership, segmentationClient)) {
            return gymfitCenters.filter(center => center.type === GymfitCenterType.GOLD)
        } else {
            const LUX_GYMS_SEGMENT_ID = "8a500172-fc50-4ad6-9e2c-d40af20db3f2"
            let isPartOfLuxSegment  = false
            try {
                isPartOfLuxSegment  = !! await segmentService.doesUserBelongToSegment(LUX_GYMS_SEGMENT_ID, userContext)
            } catch (e) {
                console.log("Segment call failed" + e)
            }

            if (!isPartOfLuxSegment) return gymfitCenters.filter(center => center.type !== GymfitCenterType.LUX)
            return gymfitCenters
        }
    }

    public static async hasAnyActiveMembership(userContext: UserContext, goldMemberships: Membership[], hasComplimentaryAccess: boolean, membershipService: IMembershipService) {
        const presentGoldMembership = GymfitUtil.getPresentGoldMembership(goldMemberships)
        if (hasComplimentaryAccess || !_.isEmpty(presentGoldMembership) || await this.getOngoingCultMembership(userContext, membershipService)) {
            return true
        } else {
            return false
        }
    }

    static getGoldMembershipPackDetailsWebUrl(gymfitMembership: Membership) {
        return `/cult/cult-pass/membership/${gymfitMembership.id}?membershipId=${gymfitMembership.id}`
    }

    public static isLuxCenter(gymfitCenter: GymfitCenter) {
        return gymfitCenter.type === GymfitCenterType.LUX
    }

    public static isLuxMembership(membership: Membership) {
        return membership.benefits.some(b => b.name === "LUX" && !b.allowOverlap)
    }
    public static getNewCultPassTitleFromOld(centerType: GymfitCenterType): string {
        if (centerType == GymfitCenterType.BLACK) {
            return "cultpass ELITE"
        } else if (centerType == GymfitCenterType.GOLD) {
            return "cultpass PRO"
        } else if (centerType == GymfitCenterType.LUX) {
            return "LUX Membership"
        }
        return centerType.toString()
     }

    public static getTrialExhaustedError(ex: any, centerType: GymfitCenterType): AlertError {
        const actions: Action[] = [
            {
                actionType: "HIDE_ALERT_MODAL",
                title: "Ok"
            }
        ]
        if (centerType != null) {
            if ( centerType === GymfitCenterType.LUX) {
                return new AlertError(`Buy Membership`, `You have used your 2 free trials for luxury gyms. Please purchase membership to workout at this gym.`, actions, ex)
            } else {
                return new AlertError(`Buy ${GymfitUtil.getNewCultPassTitleFromOld(centerType)}`, `Your free trials are over. Please purchase ${GymfitUtil.getNewCultPassTitleFromOld(centerType)} to check-in.`, actions, ex)
            }
        } else {
            return new AlertError("Buy a cultpass", "Your free trials are over. Please purchase cultpass that suits your needs to check-in", actions, ex)
        }
    }

    public static getAwayCheckinUserError(ex: any): AlertError {
        const actions: Action[] = [
            {
                actionType: "HIDE_ALERT_MODAL",
                title: "Ok"
            }
        ]
        return new AlertError(`Away check-in limit exceeded`, `You have used all your intercity checkins for this month. You can still workout in your membership city.`, actions, ex, {accessObject: ex.meta?.accessObject})
    }

    public static getAwayCheckinNotAllowedError(ex: any): AlertError {
        const actions: Action[] = [
            {
                actionType: "HIDE_ALERT_MODAL",
                title: "Ok"
            }
        ]
        return new AlertError(`Away center check-in not allowed`, `You do not have access to this center. You can still workout in your membership center.`, actions, ex, {accessObject: ex.meta?.accessObject})
    }

    public static getAwayCreditsUsedError(ex: any): AlertError {
        const actions: Action[] = [
            {
                actionType: "HIDE_ALERT_MODAL",
                title: "Ok"
            }
        ]
        return new AlertError(`You don't have enough credits to access this center`, `You have used all your away credits. You can still workout in your membership center.`, actions, ex, {accessObject: ex.meta?.accessObject})
    }

    public static getCultSessionsUsedError(ex: any): AlertError {
        const actions: Action[] = [
            {
                actionType: "HIDE_ALERT_MODAL",
                title: "Ok"
            }
        ]
        return new AlertError(`Cult class check-in limit exceeded`, `You have used all your cult class checkins for this month.`, actions, ex, {accessObject: ex.meta?.accessObject})
    }

    public static getSelectAwayCityError(ex: any): AlertError {
        const actions: Action[] = [
            {
                actionType: "HIDE_ALERT_MODAL",
                title: "Ok"
            }
        ]
        return new AlertError(`Away center check-in not allowed`, `You dont have access to other cities with your Select Membership. You can still workout in your membership city.`, actions, ex)
    }

    static getCenterSeoUrlParam(center: GymfitCenter): SeoUrlParams {
        return {
            city: center.address?.city.name,
            locality: center.locality,
            productName: center.name
        }
    }

    static getDefaultMagazineImage(): string {
        return "/image/gymfit/gymfit.png"
    }

    static getDefaultLuxMagazineImage(): string {
        return "/image/gymfit/lux_banner.jpg"
    }

    static getRashiNamespace() {
        return "GYMFIT"
    }

    static getCheckinNotificationPreferenceKey() {
        return "gym_checkin_create_plan_push_opt_out"
    }

    static getQueueName() {
        return process.env.ENVIRONMENT === "PRODUCTION" ? PROD_CHECKIN_QUEUE : STAGE_CHECKIN_QUEUE
    }

    public static getCenterDescription(centerType: GymfitCenterType): CardDescription[] {
        const description: CardDescription[] = []
        if (centerType === GymfitCenterType.LUX ) {
            const cardDescription: CardDescription = {
                text: "Download app for gym access ",
                color: "#55565B",
                bold: false
            }
            description.push(cardDescription)
            return description
        }
        const cardDescription: CardDescription = {
            text: "Unlimited access with cultpass ",
            color: "#55565B",
            bold: false
        }
        description.push(cardDescription)
        const cardDescriptionForBlack: CardDescription = {
            text: "ELITE",
            color: "#D4AF37",
            bold: true
        }
        description.push(cardDescriptionForBlack)
        if (centerType === GymfitCenterType.GOLD) {
            const cardDescriptionForAnd: CardDescription = {
                text: " & ",
                color: "#55565B",
                bold: false
            }
            description.push(cardDescriptionForAnd)
            const cardDescriptionForGold: CardDescription = {
                text: "PRO",
                color: "#838383",
                bold: true
            }
            description.push(cardDescriptionForGold)
        }
        return description
    }

    public static getIconBasisGymfitCenterType(userContext: UserContext, centerType: GymfitCenterType): string {
        if (this.isPremiumGymSupported(userContext)) {
            if (centerType === GymfitCenterType.BLACK) {
                return "/image/icons/cult/elite_tag_clp.png"
            } else if (centerType === GymfitCenterType.GOLD) {
                return "/image/icons/cult/pro_tag_clp.png"
            }
        }
        return null
    }

    public static isPauseResumeAllowed(userContext: UserContext, membership: Membership) {
        if ((membership.status === "PAUSED" || this.isCurrentGoldMembership(membership)) && !GymfitUtil.isUpcomingPause(membership)) {
            return true
        }
        return false
    }
    public static isLuxPauseResumeAllowed(membership: Membership) {
        return GymfitUtil.isLuxMembership(membership) && this.isCurrentMembership(membership)
    }
    public static isLuxActivePause(membership: Membership) {
        return GymfitUtil.isLuxMembership(membership) && this.isCurrentMembership(membership) && !_.isEmpty(membership.activePause)
    }
    public static isLuxUpcomingPause(membership: Membership) {
        return GymfitUtil.isLuxMembership(membership) && membership.status === "PURCHASED" && !_.isEmpty(membership.activePause)
    }

    static async getPackPauseResumeDetails(membership: Membership, productType: string, userContext: UserContext, isNewPauseEnabled: boolean = false) {
        const tz: Timezone = userContext.userProfile.timezone
        if (GymfitUtil.isPauseResumeAllowed(userContext, membership) || GymfitUtil.isLuxPauseResumeAllowed(membership)) {
            const isPauseAllowed = this.convertMillisToDays(membership.remainingPauseDuration) > 0
            const isActivePause = membership.status === "PAUSED" || GymfitUtil.isLuxActivePause(membership)
            const isOptionEnabled = isActivePause || isPauseAllowed
            const infoText = isActivePause ? "You can also manually resume your pack before the specified date.\nOnce you resume, your pack will be extended by the number of days you were on pause"
                : "You can pause your pack when you can't use it like during travel or illness.\n\nOnce you resume, your pack is extended by the number of days you were on pause"
            const manageOptions: ManageOptions = {
                displayText: isActivePause ? "Modify Pause" : "Pause pack",
                icon: isActivePause ? "RESUME" : "PAUSE",
                options: [{
                    isEnabled: isOptionEnabled,
                    type: GymfitUtil.isLuxUpcomingPause(membership) ? "CANCEL_CULT_PACK_PAUSE" : (isActivePause ? "RESUME_CULT_MEMEBERSHIP" : isNewPauseEnabled ? "NAVIGATION" : "PAUSE_CULT_MEMEBERSHIP"),
                    displayText: isActivePause ? "Modify Pause" : "Pause pack"
                }],
                info: {
                    title: "About Pause",
                    subTitle: infoText
                }
            }

            let pauseEndDate, pauseStartDate
            let limit = TimeUtil.formatEpochInTimeZone(tz, membership.end, "yyyy-MM-dd")
            let pauseDaysUsedInThisCycle = 0
            if (isActivePause) {
                pauseEndDate = membership.activePause ? membership.activePause.end ? TimeUtil.parseDateFromEpochWithTimezone(tz, membership.activePause.end - 1000) : undefined : undefined
                manageOptions.subTitle = pauseEndDate ? `Paused till ${TimeUtil.formatDateInTimeZone(tz, pauseEndDate, "DD MMM")}` : "Your pack is paused"
                pauseStartDate = membership.activePause ? TimeUtil.parseDateFromEpochWithTimezone(tz, membership.activePause.start) : undefined
                pauseDaysUsedInThisCycle = membership.activePause ? membership.activePause.start && membership.activePause.end ?
                    this.convertMillisToDays(membership.activePause.end - membership.activePause.start)
                    : 0 : 0
                limit = TimeUtil.formatEpochInTimeZone(tz, membership.end)
            } else {
                if (isPauseAllowed) {
                    manageOptions.subTitle = `${this.convertMillisToDays(membership.remainingPauseDuration)} pause days remaining`
                } else {
                    manageOptions.subTitle = "You have used all your pauses."
                }
            }

            const pauseDaysUsedTillDate = this.convertMillisToDays(membership.maxPauseDuration - membership.remainingPauseDuration)
            const startDateText = `${TimeUtil.formatDateInTimeZone(tz, pauseStartDate, "DD MMM hh:mm a")}`
            const meta: any = {
                membershipId: membership.id,
                productType: productType,
                title: GymfitUtil.isLuxUpcomingPause(membership) ? "Cancel Pause" : "Resume/Edit Pause",
                pauseMaxDays: this.convertMillisToDays(membership.maxPauseDuration),
                remainingPauseDays: this.convertMillisToDays(membership.remainingPauseDuration),
                remainingDaysInCurrentDuration: pauseDaysUsedInThisCycle,
                pauseEndDate: pauseEndDate ? TimeUtil.formatDateInTimeZone(tz, pauseEndDate, GymfitUtil.isLuxUpcomingPause(membership) ? "DD MMM hh:mm a" : null) : undefined,
                startDateParams: {
                    date: TimeUtil.formatDateInTimeZone(tz, pauseStartDate, "YYYY-MM-DD hh:mm A"),
                    limit,
                    canEdit: false,
                    pauseEndText: `Pause starting ${startDateText} till`
                },
                action: GymfitUtil.isLuxUpcomingPause(membership) ? {
                    primaryText: "YES",
                    secondaryText: "NO"
                } : {
                    primaryText: "RESUME",
                    secondaryText: "EDIT"
                },
                editPauseAction: {
                    meta: { isEdit: true, membershipId: membership.id, productType },
                    actionType: "PAUSE_CULT_MEMEBERSHIP"
                },
                pauseInfoTitles: {
                    pauseUsed: "Pause days used",
                    membershipExtended: "Membership will be extended by",
                    membershipEndsOn: "Membership will now end on",
                    pauseLeft: "Pause days left"
                },
                pauseInfo: GymfitUtil.getPauseInfo(tz, pauseDaysUsedTillDate, pauseDaysUsedInThisCycle, limit, this.convertMillisToDays(membership.remainingPauseDuration) - pauseDaysUsedInThisCycle, true),
                pauseReason: {
                    options: GymfitUtil.getPauseReasons(userContext),
                    selectionOptionId: "TRAVEL_OUT",
                    allowOthers: true,
                    pauseReasonTitle: "Let us know, why are you pausing?",
                },
                description: "You can pause your pack as many times as you like until you reach this limit.",
                subTitle: GymfitUtil.isLuxUpcomingPause(membership)
                    ? `You are cancelling pause starting tonight till ${TimeUtil.formatDateInTimeZone(tz, pauseEndDate, "DD MMM hh:mm a")}. Your pause days remain intact`
                    : null,
                multiplePageActions: true,
                showEmotionMeter: false,
                onCompletionAction: {
                    actionType: "POP_AND_TRIGGER_ACTION",
                    meta: {
                      nextAction: {
                        actionType: "NAVIGATION",
                        url: await GymfitUtil.getGoldMembershipDetailsUrl(membership, userContext),
                      },
                    },
                  },
                additionalInfo: "All existing booking will get cancelled",
                performanceTitle: "On break for",
                performanceSubtitle: [
                    "Rest well, come back stronger",
                    "Rest well, come back stronger",
                    "Most athletes experience a 5% decrease in cardio-vascular fitness and strength after more than 2 weeks of inactivity",
                    "Most athletes experience a 10% decrease in cardio-vascular fitness and strength after more than 3 weeks of inactivity",
                    "Most athletes experience a 20% decrease in cardio-vascular fitness and strength after more than 4 weeks of inactivity",
                ],
                startDateTitle: "Select start date for your pause",
                endDateTitle: "Select end date for your pause",
                bannerUrl: "image/pause/pause_banner_2.png",
                viaDeepLink: true,
                pauseConfirmationData: {
                    confirmationLottieUrl: "/image/mem-exp/lottie/Confirmation.json",
                    daysLeftText: "PAUSE DAYS LEFT",
                    pauseDaysText: "Membership paused for",
                    endDateText: "Pack now ends on ",
                    suggestionTitle: "Pick a way to stay active",
                    homeWorkoutText: "Maintain your weekly streak with at home workout",
                    homeWorkoutImageUrl: "/image/pause/yoga_live.png",
                    suggestionList: [
                        {
                            "iconUrl": "image/pause/Fitness.png",
                            "title": "Running or Cycling",
                            "isSelected": true,
                        },
                        {
                            "iconUrl": "image/pause/Shoes.png",
                            "title": "10K Steps Daily",
                            "isSelected": false,
                        },
                        {
                            "iconUrl": "image/pause/Sauna.png",
                            "title": "Not active",
                            "isSelected": false,
                        },
                    ]
                }
            }
            return {
                manageOptions,
                meta,
                isDisabled: !isOptionEnabled,
                isPauseAllowed
            }
        }
    }

    static getMembershipUnpauseAction(membership: Membership, productType: string, userContext: UserContext, isInternalUser: boolean): Action {
        const tz = userContext.userProfile.timezone
        let action: Action
        if (membership.status === "PAUSED") {
            const pauseStartDate = membership.activePause ? TimeUtil.parseDateFromEpochWithTimezone(tz, membership.activePause.start) : undefined
            const pauseEndDate = membership.activePause ? membership.activePause.end ? TimeUtil.parseDateFromEpochWithTimezone(tz, membership.activePause.end) : undefined : undefined
            let remainingDaysInCurrentDuration
            const pauseDaysUsedInThisCycle = this.convertMillisToDays(membership.activePause.end - membership.activePause.start)
            const limit = TimeUtil.formatEpochInTimeZone(tz, membership.end)
            if (pauseEndDate && pauseStartDate) {
                remainingDaysInCurrentDuration = this.convertMillisToDays(membership.remainingPauseDuration - pauseDaysUsedInThisCycle)
            }
            const pauseDaysUsedTillDate = this.convertMillisToDays(membership.maxPauseDuration - membership.remainingPauseDuration)
            action = {
                title: AppUtil.isPauseEditDateSupported(userContext, isInternalUser) ? "Modify Pause" : "RESUME",
                actionType: "RESUME_CULT_MEMEBERSHIP",
                meta: {
                    refreshPageOnCompletion: true,
                    membershipId: membership.id.toString(),
                    productType: productType,
                    title: AppUtil.isPauseEditDateSupported(userContext, isInternalUser) ? "Modify Pause" : "Resume Pack",
                    pauseMaxDays: this.convertMillisToDays(membership.maxPauseDuration),
                    remainingPauseDays: this.convertMillisToDays(membership.remainingPauseDuration),
                    remainingDaysInCurrentDuration,
                    action: {
                        primaryText: "RESUME",
                        secondaryText: AppUtil.isPauseEditDateSupported(userContext, isInternalUser) ? "EDIT" : "CANCEL"
                    },
                    pauseInfoTitles: {
                        pauseUsed: "Pause days used",
                        membershipExtended: "Membership will be extended by",
                        membershipEndsOn: "Membership will now end on",
                        pauseLeft: "Pause days left"
                    },
                    pauseInfo: GymfitUtil.getPauseInfo(tz, pauseDaysUsedTillDate, pauseDaysUsedInThisCycle, limit, this.convertMillisToDays(membership.remainingPauseDuration) - pauseDaysUsedInThisCycle, true),
                    dateParam: {
                        date: TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, pauseStartDate, "YYYY-MM-DD hh:mm A"),
                        limit,
                        pauseEndDate: pauseEndDate ? TimeUtil.formatDateInTimeZone(tz, pauseEndDate) : undefined,
                        pauseEndText: "You had paused your pack till"
                    },
                    editPauseAction: {
                        meta: { isEdit: true, membershipId: membership.id, productType },
                        actionType: "PAUSE_CULT_MEMEBERSHIP"
                    }
                }
            }
        } else if (GymfitUtil.isUpcomingPause(membership)) {
            const pauseStartDate = membership.activePause ? TimeUtil.parseDateFromEpochWithTimezone(tz, membership.activePause.start) : undefined
            const pauseEndDate = membership.activePause ? membership.activePause.end ? TimeUtil.parseDateFromEpochWithTimezone(tz, membership.activePause.end) : undefined : undefined
            const pauseEndDateFormatted = TimeUtil.formatDateInTimeZone(tz, pauseEndDate)
            let pauseEndDateWithTime
            if (pauseEndDate) {
                pauseEndDateWithTime = TimeUtil.getDefaultMomentForDateString(pauseEndDateFormatted, tz).startOf("day").subtract(1, "minute").toDate()
            }
            const startDateText = `${TimeUtil.formatDateInTimeZone(tz, pauseStartDate, "DD MMM hh:mm a")}`
            const remainingDaysInCurrentDuration = TimeUtil.diffInDays(tz, TimeUtil.formatDateInTimeZone(tz, pauseStartDate), TimeUtil.formatDateInTimeZone(tz, pauseEndDate))
            const limit = TimeUtil.formatEpochInTimeZone(tz, membership.end)
            const pauseDaysUsedTillDate = this.convertMillisToDays(membership.maxPauseDuration - membership.remainingPauseDuration)
            const pauseDaysUsedInThisCycle = membership.activePause?.start && membership.activePause?.end
                ? this.convertMillisToDays(membership.activePause.end - membership.activePause.start) : 0
            action = {
                title: "Cancel Pause",
                actionType: "CANCEL_CULT_PACK_PAUSE",
                meta: {
                    membershipId: membership.id,
                    productType: productType,
                    title: "Cancel Pause",
                    pauseMaxDays: this.convertMillisToDays(membership.maxPauseDuration),
                    remainingPauseDays: this.convertMillisToDays(membership.remainingPauseDuration),
                    remainingDaysInCurrentDuration,
                    pauseEndDate: pauseEndDate ? TimeUtil.formatDateInTimeZone(tz, pauseEndDate) : undefined,
                    startDateParams: {
                        date: TimeUtil.todaysDate(tz),
                        limit,
                        canEdit: false,
                        pauseEndText: "Your pack is paused till"
                    },
                    action: {
                        primaryText: "YES",
                        secondaryText: "NO"
                    },
                    pauseInfoTitles: {
                        pauseUsed: "Pause days used",
                        membershipExtended: "Membership will be extended by",
                        membershipEndsOn: "Membership will now end on",
                        pauseLeft: "Pause days left"
                    },
                    pauseInfo: GymfitUtil.getPauseInfo(tz, pauseDaysUsedTillDate, pauseDaysUsedInThisCycle, limit, this.convertMillisToDays(membership.remainingPauseDuration), true),
                    dateParam: {
                        date: TimeUtil.formatDateInTimeZone(tz, pauseStartDate, "YYYY-MM-DD hh:mm A"),
                        limit,
                        pauseEndDate: pauseEndDateWithTime ? TimeUtil.formatDateInTimeZone(tz, pauseEndDateWithTime, "DD MMM hh:mm a") : undefined,
                        pauseEndText: `Pause starting ${startDateText} till`
                    },
                    subTitle: `You are cancelling pause starting tonight till ${TimeUtil.formatDateInTimeZone(tz, pauseEndDateWithTime, "DD MMM hh:mm a")}. Your pause days remain intact`,
                    refreshPageOnCompletion: true
                }
            }
        }
        return action
    }

    static getPauseInfo(tz: Timezone, pauseDaysUsedTillDate: number, pausedUsed: number, packEndDate: string, pauseDaysLeft: number, shouldAddExtendsOn: boolean): PauseInfo[] {
        const endsOn = TimeUtil.formatDateInTimeZone(tz, new Date(packEndDate), "DD MMM YYYY")
        const pauseInfo = [
            { title: "Pause Days Used till Date", value: AppUtil.appendDays(pauseDaysUsedTillDate) },
            { title: "Pause Days Left", value: AppUtil.appendDays(pauseDaysLeft) },
        ]
        if (shouldAddExtendsOn) {
            pauseInfo.push({ title: "Membership will now end on", value: endsOn })
        }
        return pauseInfo
    }

    static getPackPauseResumeAction(pausePackData: any, membership: Membership, isNewPauseEnabled: boolean = false): Action | null {
        if (pausePackData) {
            const isActivePaused = membership.status === "PAUSED" || GymfitUtil.isLuxActivePause(membership)
            let meta = pausePackData.meta
            if (isActivePaused) {
                meta = {
                    ...meta,
                    dateParam: {
                        ...meta.startDateParams,
                        pauseEndDate: meta.pauseEndDate
                    }
                }
            } else if (isNewPauseEnabled) {
                meta = {
                    ...meta,
                    "viaDeepLink": true
                }
            }
            return {
                iconUrl: isActivePaused ? "/image/icons/cult/resume.png" : "/image/icons/cult/pause.png",
                title: GymfitUtil.isLuxUpcomingPause(membership) ? "CANCEL PAUSE" : (isActivePaused ? "MODIFY PAUSE" : "PAUSE"),
                actionType: pausePackData.manageOptions.options[0].type,
                url: `curefit://membership_pause_screen?&membershipId=${meta.membershipId.toString()}&disableAnimation=true`,
                meta: meta
            }
        }
        return null
    }

    static getPauseReasons(userContext: UserContext) {
        const appVersion = userContext.sessionInfo.appVersion
        const options = [
            {
                optionText: "I am travelling out of town",
                optionId: "TRAVEL_OUT",
                optionSubtext: "Travelling",
                actions: GymfitUtil.getActionsForPause("TRAVEL_OUT", userContext)
            },
            {
                optionText: "I am injured",
                optionId: "INJURED",
                optionSubtext: "Injury",
                actions: GymfitUtil.getActionsForPause("INJURED", userContext)
            },
            {
                optionText: "I am unwell",
                optionId: "UNWELL",
                optionSubtext: "Unwell",
                actions: GymfitUtil.getActionsForPause("UNWELL", userContext)
            },
            {
                optionText: "Work is keeping me busy",
                optionId: "WORK_BUSY",
                optionSubtext: "Busy with work",
                actions: GymfitUtil.getActionsForPause("WORK_BUSY", userContext)
            },
            {
                optionText: "I have personal commitments",
                optionId: "PERSONAL_COMITMENTS",
                optionSubtext: "Personal Commitment",
                actions: GymfitUtil.getActionsForPause("PERSONAL_COMITMENTS", userContext)
            },
            {
                optionText: "Taking a break for festival/occasion",
                optionId: "FESTIVAL",
                optionSubtext: "Festival",
                actions: GymfitUtil.getActionsForPause("COVID19", userContext)
            },
        ]
        if (appVersion >= 8.73) {
            options.push({
                optionText: "Others",
                optionId: "OTHERS",
                optionSubtext: "Others",
                actions: GymfitUtil.getActionsForPause("OTHERS", userContext)
            })
        }
        return options
    }

    static getActionsForPause(optionId: string, userContext: UserContext) {
        return [{
            title: "Pause Now",
            actionType: "PAUSE",
        }]
    }

    public static isUpcomingPause(membership: Membership): boolean {
        return membership.status === "PURCHASED" && !_.isEmpty(membership.activePause)
    }

    public static getMembershipStateMeta(membership: Membership, userContext: UserContext, logger: Logger) {
        const tz = userContext.userProfile.timezone
        switch (membership.status) {
            case "PAUSED": {
                const pauseEndDate = membership.activePause ? membership.activePause.end ? TimeUtil.formatEpochInTimeZone(tz, membership.activePause.end, "yyyy-mm-dd") : undefined : undefined
                if (pauseEndDate) {
                    TimeUtil.formatEpochInTimeZone(tz, membership.activePause.start, "DD MMM")
                    const todaysEpoch = new Date().setHours(0, 0, 0, 0).valueOf()
                    const diffDays = GymfitUtil.convertMillisToDays(membership.activePause.end - todaysEpoch)
                    const endDateText = diffDays > 1
                        ? `in ${diffDays - 1} days` : "tonight"
                    return { title: `Resumes ${endDateText}` }
                }
            }
            case "PURCHASED": {
                const isUpcomingPause = GymfitUtil.isUpcomingPause(membership)
                if (isUpcomingPause) {
                    const todaysEpoch = new Date().setHours(0, 0, 0, 0).valueOf()
                    const pauseStart = TimeUtil.formatEpochInTimeZone(tz, membership.activePause.start, "yyyy-mm-dd")
                    const startDateText = GymfitUtil.convertMillisToDays(membership.activePause.start - todaysEpoch) > 1
                        ? `on ${TimeUtil.formatEpochInTimeZone(tz, membership.activePause.start, "DD MMM")}` : "tonight"
                    return { title: `Pause starts ${startDateText}` }
                }
                return null
            }
            default:
                return null
        }
    }

    public static convertMillisToDays(durationInMillis: number): number {
        durationInMillis = durationInMillis + BUFFER_TIME
        return Math.floor(durationInMillis / (24 * 60 * 60 * 1000))
    }
    public static convertDaysToMillis(days: number): number {
        return days * (24 * 60 * 60 * 1000)
    }

    public static isPauseSupported(userContext: UserContext): boolean {
        if (AppUtil.isWeb(userContext)) {
            return false
        }
        if (userContext.sessionInfo.appVersion >= GYM_MEMBERSHIP_PAUSE_ENABLED_VERSION) {
            return true
        }
        return false
    }

    public static isPauseEnabled(): boolean {
        return true
    }

    static isPackUpgradeAble(membership: Membership): boolean {
        return membership.end > Date.now()
    }

    public static getCultBenefit(membership: Membership): Benefit {
        return membership.benefits.find(benefit => benefit.name === "CULT" || benefit.name === "GYMFIT_CULT")
    }

    public static capitalizeFirstLetterOfAllWords(sentance: string) {
        if (!sentance) {
            return
        }
        return sentance.replace(/(^\w{1})|(\s+\w{1})/g, letter => letter.toUpperCase())
    }

    static getUserLocationPreferenceFromLocality(locality: string): LocationPreferenceRequestEntity {
        const userLocationPreference: LocationPreferenceRequestEntity = {
            prefLocationType: undefined
        }
        if (locality === GymfitUtil.LOCALITY_SEARCH_KEY) {
            userLocationPreference.prefLocationType = LocationDataKey.CURRENT_LOC
        } else {
            userLocationPreference.prefLocationType = LocationDataKey.LOCALITY
            userLocationPreference.locality = locality
        }
        return userLocationPreference
    }

    static isPackSupportedinApp(pack: OfflineFitnessPack): Boolean {
        const visibilities = pack.constraints?.visibilities
        return !visibilities || visibilities.includes(Visibility.ALL) || visibilities.includes(Visibility.APP)
    }

    static isPackSupportedinWeb(pack: OfflineFitnessPack): Boolean {
        const visibilities = pack.constraints?.visibilities
        return !visibilities || visibilities.includes(Visibility.ALL)  || visibilities.includes(Visibility.WEBSITE)
    }

    static getChangeStartDateAction(membership: Membership, productType: ProductType): Action[] {
        const actions: Action[] = []
        const membershipId = membership.id
        const isCurrentMembership = GymfitUtil.isCurrentMembership(membership)
        if (membershipId && !isCurrentMembership) {
            actions.push({
                iconUrl: "/image/icons/cult/calendar.png",
                title: "CHANGE\nSTART DATE",
                actionType: "NAVIGATION",
                url: CultUtil.getStartDatePageUrl(membershipId, productType)
            })
        }
        return actions
    }
}

export default GymfitUtil
