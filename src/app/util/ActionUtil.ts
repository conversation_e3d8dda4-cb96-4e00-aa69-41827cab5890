import * as _ from "lodash"
import { LoggableActivityType } from "../logging/ActivityLogging"
import { Action, ManageOptionPayload } from "../common/views/WidgetView"
import { BookingTypes, CultBooking, CultWorkout } from "@curefit/cult-common"
import { City } from "@curefit/location-common"
import { CustomerIssueType } from "@curefit/issue-common"
import { HourMin } from "@curefit/base-common"
import { ProductType } from "@curefit/product-common"
import { Session } from "@curefit/userinfo-common"
import { User } from "@curefit/user-common"
import { ActionUtil as EtherActionUtil, MustacheUtils } from "@curefit/base-utils"
import { DIYPack, DIYProduct } from "@curefit/diy-common"
import { SessionInfo, UserContext } from "@curefit/userinfo-common"
import { AtlasUtil } from "./AtlasUtil"
import { UrlPathBuilder } from "@curefit/product-common"
import AppUtil, { SUPPORT_DEEP_LINK } from "./AppUtil"
import {
    BookingDetail,
    IHealthfaceService,
    RecommendedTimelineActivity,
    TimelineActivity,
    TimelineActivityV2,
    ConsultationInstructionResponse,
    Patient
} from "@curefit/albus-client"
import IProductBusiness from "../product/IProductBusiness"
import { SleepActivity, WalkActivity } from "@curefit/atlas-client"
import { TimeUtil, ILogger, Timezone } from "@curefit/util-common"
import CareUtil from "./CareUtil"
import { QuickAction } from "../user/UserControllerModels"
import { User as UserCache } from "@curefit/user-common"
import { IssueDetailParams } from "../crm/IssueBusiness"
import * as momentTz from "moment-timezone"
import { SlotUtil } from "@curefit/eat-util"
import { ActionV2, ActionUtil as VMActionUtil } from "@curefit/vm-common"
import { Action as VMAction } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import * as mustache from "mustache"
import CultUtil from "./CultUtil"
import { FoodBooking } from "@curefit/alfred-client"
import { ManageOption } from "@curefit/apps-common"
const qs = require("qs")
import { EntityType } from "@curefit/riddler-common"
import { HamletBusiness } from "@curefit/hamlet-node-sdk"
import { ISegmentService } from "@curefit/vm-models"
import { COMBINED_WEIGHT_LOSS_CLP_DEEPLINK, TransformUtil } from "./TransformUtil"


export interface UserActionCache extends UserCache {
    action?: Action
}

export const PRIMARY_ACTION_VARIANT = "primary"
export const SECONDARY_ACTION_VARIANT = "secondary"

export class ActionUtil {
    static addLoggableActivityUrl(loggableActivity: LoggableActivityType, queryParamsObj?: any) {
        let url = "curefit://addLogActivity"
        const obj: any = { "logTypeId": loggableActivity }
        _.assign(obj, queryParamsObj)
        url += ActionUtil.serializeAsQueryParams(obj)
        return url
    }

    static formServiceUrl(formId: string, queryParamsObj?: any) {
        let url = "curefit://userform"
        const obj: any = { "formId": formId }
        _.assign(obj, queryParamsObj)
        url += ActionUtil.serializeAsQueryParams(obj)
        return url
    }

    static serializeAsQueryParams(obj: any) {
        const qs = _.reduce(obj, function (result, value, key) {
            return !_.isNull(value) && !_.isUndefined(value) ? (result += key + "=" + value + "&") : result
        }, "").slice(0, -1)

        if (qs && qs.length > 0) {
            return "?" + qs
        }
        return qs
    }

    static serializeAsUrlParam(obj: any) {
        const params = _.reduce(obj, function (result, value, key) {
            return !_.isNull(value) && !_.isUndefined(value) ? (result += encodeURIComponent(value.replace(/'/g, "")) + "/") : result
        }, "")
        return `/${params}`
    }

    static centerNavigationAction(placeUrl: string): Action {
        const action: Action = {
            actionType: "EXTERNAL_DEEP_LINK",
            url: placeUrl,
            icon: "LOCATION",
            title: "Navigate"
        }
        return action
    }

    static showCityAction(canSkip: boolean): Action {
        return {
            actionType: "SHOW_CHANGE_CITY",
            meta: {
                canSkip: canSkip
            }
        }
    }

    static trainerDetailAction(trainerId: string): Action {
        return {
            actionType: "NAVIGATION",
            url: `curefit://trainerdetails?id=${trainerId}`,
        }
    }

    static async cancelClassAction(options: ManageOptionPayload[], userContext: UserContext, foodBooking?: FoodBooking, cultBooking?: CultBooking, hamletBusiness?: HamletBusiness): Promise<Action> {
        const tz = userContext.userProfile.timezone
        let manageOptionType = "CANCEL_CULT_CLASS"
        const isRescheduleClassSupported = await CultUtil.isRescheduleClassSupported(userContext, cultBooking, hamletBusiness)
        let cancelClassOption = undefined
        if (isRescheduleClassSupported) {
            manageOptionType = "SHOW_CLASS_RESCHEDULE_MODAL"
            cancelClassOption = _.find(options, manageOption => {
                return manageOption.type === manageOptionType
            })
            if (!cancelClassOption) {
                manageOptionType = "CANCEL_CULT_CLASS",
                cancelClassOption = _.find(options, manageOption => {
                    return manageOption.type === manageOptionType
                })
            }
        } else {
            cancelClassOption = _.find(options, manageOption => {
                return manageOption.type === manageOptionType
            })
        }

        if (cancelClassOption) {
            if (cancelClassOption.isEnabled && foodBooking) {
                if (isRescheduleClassSupported && cancelClassOption?.type === "SHOW_CLASS_RESCHEDULE_MODAL") {
                    const cancelClassAction = {
                        title: "CANCEL",
                        actionType: cancelClassOption.type,
                        meta: cancelClassOption.meta
                    }
                    const cancelCafeActionType: ManageOption = "CANCEL_CULT_MEAL_CLASS_MODAL"
                    return {
                        ...cancelClassOption,
                        meta: {
                            ...cancelClassOption.meta,
                            actions: cancelClassOption.meta.actions.map((action: Action) => {
                                if (action.actionType === "CANCEL_CULT_CLASS") {
                                    return {
                                        title: "CANCEL",
                                        actionType: cancelCafeActionType,
                                        meta: {
                                            title: `Cancel class at  ${cultBooking.Center.name}`,
                                            subTitle: `${TimeUtil.formatDateStringInTimeZone(cultBooking.Class.date, tz, "ddd, D MMM")} ${momentTz.tz(cultBooking.Class.startTime, "hh:mm:ss", tz).format("hh:mm A")} - ${momentTz.tz(cultBooking.Class.endTime, "hh:mm:ss", tz).format("hh:mm A")}`,
                                            cancelOptions: [{
                                                title: "Cancel Workout Only",
                                                type: "CANCEL_CLASS",
                                                meta: action.meta
                                            }, {
                                                title: "Cancel Workout + Workout Snacks",
                                                type: "CANCEL_CLASS_AND_MEAL",
                                                meta: {
                                                    ...action.meta,
                                                    fulfilmentId: foodBooking.fulfilmentId,
                                                    date: TimeUtil.formatDateStringInTimeZone(foodBooking.deliveryDate, tz)
                                                }
                                            }],
                                            action: { ...cancelClassAction, actionType: "CANCEL_CULT_CLASS" }
                                        }
                                    }
                                } else if (action.actionType === "NAVIGATION" && action.title === "RESCHEDULE") {
                                    return {
                                        ...action,
                                        url: `${action.url}&fulfilmentId=${foodBooking.fulfilmentId}&date=${TimeUtil.formatDateStringInTimeZone(foodBooking.deliveryDate, tz)}`
                                    }
                                }
                                return action
                            })
                        },
                        actionType: cancelClassOption.type
                    }
                }
                return {
                    ...cancelClassOption,
                    actionType: cancelClassOption.type
                }
            }
            const cancelClassAction: Action = {
                title: cancelClassOption.displayText,
                icon: "CANCEL",
                actionType: cancelClassOption.type,
                disabled: !cancelClassOption.isEnabled,
                meta: cancelClassOption.meta,
                analyticsData: {
                    classCredit: cultBooking?.creditCost
                }
            }
            return cancelClassAction
        } else {
            return undefined
        }
    }

    static classDropoutAction(options: ManageOptionPayload[]): Action {
        const classDropoutOption = _.find(options, manageOption => {
            return manageOption.type === "CULT_CLASS_DROPOUT"
        })
        if (classDropoutOption) {
            const classDropoutAction: Action = {
                title: classDropoutOption.displayText,
                icon: "CANCEL",
                actionType: classDropoutOption.type,
                disabled: !classDropoutOption.isEnabled,
                meta: classDropoutOption.meta
            }
            return classDropoutAction
        }
    }

    static cultScoreLogAction(options: ManageOptionPayload[]): Action {
        const cultScoreLogOption = _.find(options, manageOption => {
            return manageOption.type === "LOG_CULT_SCORE"
        })
        if (cultScoreLogOption) {
            const cultScoreLogOptionAction: Action = {
                title: "Log",
                icon: "LOG",
                actionType: "LOG_CULT_SCORE",
                url: cultScoreLogOption.action as string,
                meta: cultScoreLogOption.meta
            }
            return cultScoreLogOptionAction
        } else {
            return undefined
        }
    }

    public static getJuniorClassBookingAppUpdateAction(userContext: UserContext): Action {
        return {
            actionType: "USER_SELECTION_MODAL_V2"
        }
    }

    public static getClassBookingAction(userContext: UserContext, productType: ProductType, user: UserCache, subUsers?: UserCache[], pageFrom?: string, centerId?: string, workoutId?: string, classId?: string, selectedDate?: string, isParentUserIncluded: boolean = true): Action {

        const isNewClassBookingSuppoted = AppUtil.isNewClassBookingSuppoted(userContext, user.isInternalUser)
        if (false && !_.isEmpty(subUsers) && AppUtil.isChildUserBookingSupported(userContext)) {

        } else {
            const selectedUserId = (userContext.userProfile.userId !== user.id) ? user.id : undefined
            const url = productType === "FOOD" ? EtherActionUtil.getBookEatLiveClassUrl(productType, pageFrom, classId, selectedDate) : EtherActionUtil.getBookCultClassUrl(productType, isNewClassBookingSuppoted, pageFrom, workoutId, centerId, undefined, selectedDate, selectedUserId)
            return {
                actionType: "NAVIGATION",
                title: "Book",
                url: url
            }
        }
    }

    static getCultJuniorClassBookingAction(userContext: UserContext, productType: ProductType, user: UserCache, subUsers?: UserCache[], pageFrom?: string, selectedDate?: string): Action {
        return this.getClassBookingAction(userContext, productType, user, subUsers, pageFrom, undefined, undefined, undefined, selectedDate, false)
    }

    static getBookClassAction(workout: CultWorkout, productType: ProductType, isNewClassBookingSupported: boolean, pageFrom: string, centerId?: string): Action {
        if (!_.isEmpty(workout.centers)) {
            const centerHasPreferredWorkout = _.find(workout.centers, center => {
                if (centerId === center.id.toString())
                    return true
                return false
            })
            centerId = centerHasPreferredWorkout ? centerId : workout.centers[0].id.toString()
        }
        const action: Action = {
            actionType: "NAVIGATION",
            title: "Book Class",
            url: EtherActionUtil.getBookCultClassUrl(productType, isNewClassBookingSupported, pageFrom, workout.workoutCategoryID.toString(), centerId)
        }
        return action
    }

    static getBookClassActionMultipleWorkouts(workouts: CultWorkout[], productType: ProductType, isNewClassBookingSupported: boolean, pageFrom: string, centerId?: string): Action {
        if (_.isEmpty(workouts)) return undefined
        const workout: CultWorkout = workouts[0]
        if (!_.isEmpty(workout.centers)) {
            const centerHasPreferredWorkout = _.find(workout.centers, center => {
                if (centerId === center.id.toString())
                    return true
                return false
            })
            centerId = centerHasPreferredWorkout ? centerId : workout.centers[0].id.toString()
        }
        const workoutIdList: string[] = _.map(workouts, (workout: CultWorkout) => {
            return workout.workoutCategoryID.toString()
        })
        const action: Action = {
            actionType: "NAVIGATION",
            title: "Book Class",
            url: EtherActionUtil.getBookCultClassMultipleWorkoutsUrl(productType, isNewClassBookingSupported, pageFrom, workoutIdList, centerId)
        }
        return action
    }

    static diyActionsFromSessionDetailV2(session: DIYProduct, pack: DIYPack, sessionInfo: SessionInfo): Action[] {
        const content = AtlasUtil.getContentDetailV2(session)
        const meta = AtlasUtil.getContentMetaV2(session, pack)
        const diyAction: Action[] = []
        diyAction.push({
            title: undefined,
            icon: "DOWNLOAD_OR_PLAY",
            meta: {
                content: content,
                meta: meta
            },
            actionType: "DOWNLOAD_OR_PLAY"
        })
        if (AppUtil.isReminderSupportedVersion(sessionInfo.osName, sessionInfo.appVersion)) {
            diyAction.push({
                title: "Stream Online",
                icon: (AppUtil.isLargePlayIconSupported(sessionInfo.osName, sessionInfo.appVersion)) ? "PLAY_LARGE" : "PLAY",
                url: (content.type === "audio" ? EtherActionUtil.audioUrl(content.URL, UrlPathBuilder.prefixSlash(pack.imageDetails.heroImage), meta) : EtherActionUtil.videoUrl(content.URL, UrlPathBuilder.prefixSlash(pack.imageDetails.heroImage), meta)),
                meta: {
                    content: content,
                    meta: meta
                },
                actionType: "NAVIGATION"
            })
        }
        return diyAction
    }

    static getCareQuickActions(userContext: UserContext, activity: TimelineActivity, issuesMap: Map<string, CustomerIssueType[]>, user: User, productBusiness: IProductBusiness, logger: ILogger, enableReportIssue?: boolean, rootBooking?: BookingDetail): Action[] {
        const bookingDetail = activity.bookingInfo
        enableReportIssue = _.isNil(enableReportIssue) ? true : enableReportIssue
        switch (activity.carefitActivity) {
            // TODO change this to take backend actions
            case "CONSULTATION": return productBusiness.getTeleconsultationManageOptions(userContext, user, true, enableReportIssue, issuesMap, bookingDetail, bookingDetail.booking.cfOrderId, rootBooking)
            case "DIAGNOSTICS": return productBusiness.getDiagnosticsManageOptions(activity.bookingInfo, activity.activityActions)
            default: logger.error(`getCareQuickActions not handled for ${activity.carefitActivity}`)
        }
    }

    static async getCareQuickActionsV2(
        userContext: UserContext,
        user: User,
        activity: TimelineActivityV2,
        productBusiness: IProductBusiness,
        logger: ILogger,
        consultationInstruction: ConsultationInstructionResponse[],
        patientsList: Patient[],
        hamletBusiness?: HamletBusiness
    ): Promise<Action[]> {
        switch (activity.type) {
            case "CONSULTATION": return await productBusiness.getTeleconsultationManageOptionsV2(userContext, user, activity, consultationInstruction, patientsList, hamletBusiness)
            case "DIAGNOSTICS": return productBusiness.getDiagnosticsManageOptionsV2(activity)
            default: logger.error(`getCareQuickActions not handled for ${activity}`)
        }
    }

    static getStepsUrl(walkActivity: WalkActivity): string {
        return `curefit://stepdetails?done=${walkActivity.steps.done}&goal=${walkActivity.steps.goal}&date=${walkActivity.date}`
    }

    static getChallengeDetailsUrl(challengeId: string, ref: EntityType, refId: string) {
        return `curefit://challengedetails?id=${challengeId}&ref=${ref}&refId=${refId}`
    }

    static getSleepUrl(sleepActivity: SleepActivity, userContext: UserContext): string {
        const today: string = TimeUtil.todaysDateWithTimezone(userContext.userProfile.timezone)
        const startTime = sleepActivity.timeRange.start
        const endTime = sleepActivity.timeRange.end
        if (today === sleepActivity.date) {
            return `curefit://sleepdetails?reviewed=${sleepActivity.hasReviewed}&date=${sleepActivity.date}&startTime=${startTime}&endTime=${endTime}`
        } else {
            return `curefit://sleepdetails?reviewed=true&date=${sleepActivity.date}&startTime=${startTime}&endTime=${endTime}`
        }
    }

    static getLogSleepUrl(date: string, sleepStartTime: number, sleepEndTime: number): string {
        return `curefit://sleepdetails?addEntry=true&date=${date}&startTime=${sleepStartTime}&endTime=${sleepEndTime}`
    }

    static getCultMindClassUrl(productType: ProductType, bookingNumber: string): string {
        return productType === "FITNESS" ? `curefit://cultclass?bookingNumber=${bookingNumber}` : `curefit://mindclass?bookingNumber=${bookingNumber}`
    }

    static getCareRecommendedActivityQuickActions(userContext: UserContext, activity: RecommendedTimelineActivity, issuesMap: Map<string, CustomerIssueType[]>, productBusiness: IProductBusiness, logger: ILogger): Action[] {
        const bookingInfo = activity.bookingInfo
        switch (activity.careFitRecommendedActivityType) {
            case "VIEW_PRESCRIPTION": return productBusiness.getPrescriptionManageOptions(userContext, true, false, issuesMap, activity.bookingInfo)
            case "BOOK_TESTS": return [{
                actionType: "NAVIGATION",
                title: "Book",
                url: EtherActionUtil.carefitbundle(bookingInfo.booking.productCode, bookingInfo.booking.subCategoryCode, bookingInfo.booking.id.toString())
            }]
            case "BOOK_CONSULTATION": return [{
                url: EtherActionUtil.carefitbundle(bookingInfo.booking.productCode, bookingInfo.booking.subCategoryCode, bookingInfo.booking.id.toString()),
                // url: `curefit://selectCareDate?productId=CARE_CONSULTATION&centerId=1&parentBookingId=${bookingInfo.booking.id}&patientId=${bookingInfo.booking.patientId}`,
                title: "Book",
                actionType: "NAVIGATION"
            }]
            case "HEALTH_ASSESSMENTS":
                return [{
                    // meta: {
                    //     nextAction: {
                    //         meta: {
                    //             assessmentId: patientAssessment.id,
                    //             assessmentStatus: "COMPLETED"
                    //         },
                    //         actionType: "UPDATE_PATIENT_ASSESSMENT"
                    //     }
                    // },
                    title: "Start now",
                    url: EtherActionUtil.carefitbundle(bookingInfo.booking.productCode, bookingInfo.booking.subCategoryCode, bookingInfo.booking.id.toString()), // ActionUtil.webview(patientAssessment.assessmentUrl),
                    actionType: "NAVIGATION"// "OPEN_ASSESSMENT_FORM"
                }]
            default: logger.error(`getCareRecommendedActivityQuickActions not handled for ${activity.careFitRecommendedActivityType}`)
        }
    }

    static getCareRecommendedActivityAction(userContext: UserContext, activity: RecommendedTimelineActivity, logger: ILogger): Action {
        const bookingInfo = activity.bookingInfo
        const vertical = CareUtil.getVerticalForConsultation(bookingInfo.consultationOrderResponse?.consultationProduct?.doctorType)
        switch (activity.callToAction) {
            case "VIEW_REPORT_PRESCRIPTION": return {
                actionType: "NAVIGATION",
                url: EtherActionUtil.teleconsultationSingle(userContext, bookingInfo.booking.productCode, bookingInfo.consultationOrderResponse?.consultationProduct.urlPath, bookingInfo.booking.id.toString(), undefined, vertical)
            }
            case "TAKE_HEALTH_ASSESSMENT":
            case "BOOK_TESTS":
            case "BOOK_CONSULTATION": return {
                actionType: "NAVIGATION",
                url: EtherActionUtil.carefitbundle(bookingInfo.booking.productCode, bookingInfo.booking.subCategoryCode, bookingInfo.booking.id.toString())
            }
            default: logger.error(`getCareRecommendedActivityAction not handled for ${activity.callToAction}`)
        }
    }

    static async getCareActivityAction(userContext: UserContext, activity: TimelineActivity, healthfaceService: IHealthfaceService, logger: ILogger): Promise<Action> {
        const bookingInfo = activity.bookingInfo
        switch (activity.carefitActivity) {
            case "CONSULTATION":
                const consultationProduct = bookingInfo.consultationOrderResponse?.consultationProduct
                const vertical = CareUtil.getVerticalForConsultation(consultationProduct?.doctorType)

                return {
                    title: "VIEW",
                    actionType: "NAVIGATION",
                    url: EtherActionUtil.teleconsultationSingle(userContext, bookingInfo.booking.productCode, consultationProduct?.urlPath, bookingInfo.booking.id.toString(), undefined, vertical)
                }
            case "DIAGNOSTICS":
                if (bookingInfo.booking.rootBookingId !== -1) {
                    const rootBooking = await healthfaceService.getBookingDetail(bookingInfo.booking.rootBookingId)
                    if (rootBooking.booking.categoryCode === "BUNDLE") {
                        return {
                            title: "VIEW",
                            actionType: "NAVIGATION",
                            url: EtherActionUtil.carefitbundle(rootBooking.booking.productCode, rootBooking.booking.subCategoryCode, rootBooking.booking.id.toString())
                        }
                    }
                }
                return {
                    title: "VIEW",
                    actionType: "NAVIGATION",
                    url: EtherActionUtil.diagnostics(bookingInfo.booking.productCode, bookingInfo.booking.id.toString())
                }
            default: logger.error(`getCareActivityAction not handled for ${activity.carefitActivity}`)
        }
    }

    static getCareActivityActionV2(userContext: UserContext, activity: TimelineActivityV2, urlPath: string, logger: ILogger): Action {
        switch (activity.type) {
            case "CONSULTATION": {
                const vertical = CareUtil.getVerticalForConsultation(activity.doctorType)
                return {
                    title: "VIEW",
                    actionType: "NAVIGATION",
                    url: EtherActionUtil.teleconsultationSingle(userContext, activity.productCode, urlPath, activity.bookingId.toString(), undefined, vertical)
                }
            }
            case "DIAGNOSTICS":
                if (activity.rootBookingId && activity.rootBookingId !== -1) {
                    if (activity.rootBookingCategoryCode === "BUNDLE") {
                        return {
                            title: "VIEW",
                            actionType: "NAVIGATION",
                            url: EtherActionUtil.carefitbundle(activity.rootBookingProductCode, activity.rootBookingSubCategoryCode, activity.rootBookingId.toString())
                        }
                    }
                }
                return {
                    title: "VIEW",
                    actionType: "NAVIGATION",
                    url: EtherActionUtil.diagnostics(activity.productCode, activity.bookingId.toString())
                }
            default: {
                logger.error(`getCareActivityAction not handled for ${activity.type}`)
                return undefined
            }
        }
    }

    static getCancelPlanAction(): Action {
        const cancelPlanAction: Action = {
            title: "Cancel Plan",
            actionType: "SHOW_ALERT_MODAL",
            meta: {
                title: "Plan cancellation!",
                subTitle: "Are you sure you want to cancel your plan?",
                actions: [
                    {
                        actionType: "REST_API",
                        meta: {
                            method: "POST",
                            url: "/user/myplan/cancel",
                            body: {},
                            successMessage: "Your plan has been cancelled successfully"
                        },
                        icon: "CANCEL",
                        title: "Yes"
                    },
                    { actionType: "HIDE_ALERT_MODAL", title: "No" },
                ]
            }
        }

        return cancelPlanAction
    }

    static appUpdateAction(userContext: UserContext, title?: string, metaSubTitle?: string, metaTitle?: string): Action {
        return {
            actionType: "SHOW_ALERT_MODAL",
            title,
            meta: {
                title: metaTitle ? metaTitle : "App version not supported",
                subTitle: metaSubTitle ? metaSubTitle : "This action is available only on the latest version of this app. Kindly update the app",
                actions: [{ actionType: "EXTERNAL_DEEP_LINK", title: "Update", url: AppUtil.getAppUpdateUrl(userContext) }]
            }
        }
    }

    static getStepsHowItWorks(): Action {
        return {
            actionType: "SHOW_CAROUSEL_LIST",
            meta: {
                type: "JOURNEY_LIST",
                bundleProducts: [{
                    ar: 0.596,
                    imageUrl: "/image/steps/googlefit/fit_permission_1_final.png"
                }, {
                    ar: 0.596,
                    imageUrl: "/image/steps/googlefit/fit_permission_2_final.png"
                }, {
                    ar: 0.596,
                    imageUrl: "/image/steps/googlefit/fit_permission_3_updated.png"
                }, {
                    ar: 0.596,
                    imageUrl: "/image/steps/googlefit/fit_permission_4_updated.png"
                }, {
                    ar: 0.596,
                    imageUrl: "/image/steps/googlefit/fit_permission_5_updated.png"
                }]
            }
        }
    }

    static getQuickActions(session: Session, user: User, userContext: UserContext, city: City): QuickAction[] {
        const isNewClassBookingSuppoted = AppUtil.isNewClassBookingSuppoted(userContext, user.isInternalUser)

        const quickActions: QuickAction[] = []

        if (!_.isEmpty(city.availableOfferings)) {
            if (city.availableOfferings.includes("FITNESS")) {
                quickActions.push(
                    {
                        type: "Cult", // Required
                        title: "cult.fit class", // Optional, if empty, `type` will be used instead
                        icon: "cult", // Make sure logo passed is available in native resource of both platforms
                        userInfo: {
                            url: EtherActionUtil.getBookCultClassUrl("FITNESS", isNewClassBookingSuppoted, "addActivity", undefined) // provide custom data, like in-app url you want to open
                        }
                    }
                )
            } else if (city.availableOfferings.includes("DIY_FITNESS")) {
                quickActions.push(
                    {
                        type: "Cult", // Required
                        title: "cult.fit at home", // Optional, if empty, `type` will be used instead
                        icon: "cult", // Make sure logo passed is available in native resource of both platforms
                        userInfo: {
                            url: `curefit://listpage?pageId=CultAtHome`
                        }
                    }
                )
            }

            if (city.availableOfferings.includes("MIND")) {
                quickActions.push(
                    {
                        type: "Mind",
                        title: "mind.fit class",
                        icon: "mind",
                        userInfo: {
                            url: EtherActionUtil.getBookCultClassUrl("MIND", isNewClassBookingSuppoted, "addActivity", undefined)
                        }
                    }
                )
            } else if (city.availableOfferings.includes("DIY_MEDITATION")) {
                quickActions.push(
                    {
                        type: "Mind",
                        title: "mind.fit at home",
                        icon: "mind",
                        userInfo: {
                            url: `curefit://listpage?pageId=MindAtHome`
                        }
                    }
                )
            }

            if (city.availableOfferings.includes("FOOD") || city.availableOfferings.includes("RECIPE")) {
                quickActions.push(
                    {
                        type: "Eat",
                        title: "eat.fit meal",
                        icon: "eat",
                        userInfo: {
                            url: EtherActionUtil.eatFitClp()
                        }
                    }
                )
            }
        }

        const cityId = session.sessionData.cityId
        if (CareUtil.isCarePresentInCity(cityId)) {
            quickActions.push({
                type: "Care",
                title: "care.fit consultation",
                icon: "care",
                userInfo: {
                    url: ActionUtil.getConsultationClpPage()
                }
            })
        }
        return quickActions
    }

    static getConsultationClpPage() {
        return `curefit://listpage?pageId=clpconsultation`
    }

    static getAccountSettingsUrl(): string {
        return `curefit://accountsettings`
    }

    static getManageDevicesUrl(userContext: UserContext): string {
        if (!AppUtil.isAccountDeletionSupported(userContext)) {
            return `curefit://managedevices?disableAccountDeletion=true`
        }
        return `curefit://managedevices`
    }

    static getScoreDashboardUrl(): string {
        return `curefit://dashboardv2`
    }

    static getSavedPaymentsUrl(): string {
        return `curefit://savedpayments`
    }

    static getMedicalRecordsURL(): string {
        return `curefit://medicalrecords`
    }

    static getFitcashPageUrl(): string {
        return `curefit://fitcash`
    }

    static getGiftCardTransactionsPageUrl(): string {
        return `curefit://gifttransactions`
    }

    static getSavedAddressPage(): string {
        return `curefit://savedaddress`
    }

    static getGiftVoucherPage(campaignId: string): string {
        return `curefit://giftvoucherpage?campaignId=${campaignId}`
    }

    static transformCLP(userContext: UserContext) {
        if (AppUtil.isWeb(userContext)) {
            return "/"
        } else if (AppUtil.isTransformCombinedClpSupported(userContext)) {
            return COMBINED_WEIGHT_LOSS_CLP_DEEPLINK
        }
        return COMBINED_WEIGHT_LOSS_CLP_DEEPLINK
    }

    static getIssuesUrl(): string {
            return SUPPORT_DEEP_LINK
    }

    static getIssuesUrlOld(issueDetailParams: IssueDetailParams): string {
        const productType = issueDetailParams.productType
        const productStates = issueDetailParams.productStates
        const meta = issueDetailParams.meta
        const productStatesStringified = qs.stringify({ productStates }, { encode: false })
        const metaStringified = qs.stringify({ meta }, { encode: false })
        return `curefit://reportissue?productType=${productType}&${productStatesStringified}&${metaStringified}`
    }

    static eatFitClp(selectedTabIndex?: number) {
        let url = `curefit://eatfitclp`
        const obj: any = { selectedTabIndex }
        url += ActionUtil.serializeAsQueryParams(obj)
        return url
    }
    static getEatClpUrl(kioskId: string, cultBooking: CultBooking, timeZone: Timezone, launchNewInstance?: boolean) {
        let url = ActionUtil.eatFitClp()
        if (kioskId) {
            url += "?kioskId=" + kioskId
            const startTime = momentTz.tz(cultBooking.Class.date + " " + cultBooking.Class.startTime, "YYYY-MM-DD hh:mm:ss", timeZone)
            const startHourMin: HourMin = {
                hour: startTime.get("hours"),
                min: startTime.get("minutes")
            }
            url += "&date=" + cultBooking.Class.date + "&mealSlot=ALL" + "&cultBookingNumber=" + cultBooking.bookingNumber + "&classStartTime=" + TimeUtil.formatHourMin(startHourMin)
            if (launchNewInstance) {
                url += "&launchNewInstance=true"
            }
            // To fix the issue introduced in app version 8.75
            url += "&pageId=eatclp"
            return url
        }
        return undefined
    }
    static gearfetchIssues(): string {
        return "/issues"
    }

    static addPageParamsToUrl(url: string, params: string): string {
        if (url) {
            return `${url}${url.indexOf("?") > -1 ? "&" : "?"}${params}`
        }
        return url
    }

    static async getCultPackBrowseWidgetNavigationUrl(userContext: UserContext, segmentService: ISegmentService) {
        return await AppUtil.isCenterLevelRenewalEnabled(userContext, segmentService) ? "curefit://fl_listpage?pageId=select_renewal&hideTitle=true" : (`curefit://tabpage?pageId=cult&selectedTab=CultAtCenter&widgetId=${await CultUtil.getCultPackWidgetId(userContext, segmentService)}`)
    }

    static getOldCultPackBrowseWidgetNavigationAction() {
        return `curefit://tabpage?pageId=cult&selectedTab=CultAtCenter&widgetId=${CultUtil.getOldCultPackWidgetId()}`
    }

    static getDIYSessionDetailUrl(userContext: UserContext, productId: string, packId: string, category: string) {
        if (AppUtil.isWeb(userContext)) {
            const queryParams = ActionUtil.serializeAsQueryParams({ liveClassId: encodeURIComponent(productId), bookingNumber: encodeURIComponent(productId), isDIY: true, packId })
            return  `/live/exercise-videos/${category}${queryParams}`
        }
        return `curefit://liveclassdetail?liveClassId=${encodeURIComponent(productId)}&bookingNumber=${encodeURIComponent(productId)}&productType=LIVE_FITNESS&isDIY=true&packId=${packId}`
    }
    static getFormatPageNavigationAction(formatId: string): Action {
        return {
            actionType: "NAVIGATION",
            url: `curefit://formatpage?formatId=${formatId}`,
        }
    }
    static getTrainerPageNavigationAction(trainerId: string): Action {
        return {
            actionType: "NAVIGATION",
            url: `curefit://trainerpage?trainerId=${trainerId}`,
        }
    }

    static getFlutterProfileInfoUrl(): string {
        return `curefit://fl_profile_info`
    }

    static getSocialProfileUrl(doesProfilePicExist: boolean): string {
        return doesProfilePicExist ? "curefit://social_user_profile" : "curefit://edit_profile"
    }
}

export default ActionUtil
