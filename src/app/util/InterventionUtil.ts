export const INTERVENTION_ANDROID_VERSION = 6.31
export const INTERVENTION_IOS_VERSION = 6.2

class InterventionUtil {
    public static isInterventionSupported(appVersion: number, osName: string): boolean {
        if (osName === "android" && appVersion >= INTERVENTION_ANDROID_VERSION) {
                return true
        }
        if (osName === "ios" && appVersion >= INTERVENTION_IOS_VERSION) {
                return true
        }
        return false
    }

}

export default InterventionUtil
