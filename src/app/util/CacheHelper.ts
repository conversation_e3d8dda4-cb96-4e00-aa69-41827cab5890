import { CACHE_TYPES, ICultReadOnlyCache, IWalletBalanceCache, IEatReadOnlyCache } from "@curefit/cache-utils"
import { CULT_CLIENT_TYPES, ICultServiceOld as ICultService } from "@curefit/cult-client"
import { CultSummary } from "@curefit/cult-common"
import { User } from "@curefit/user-common"
import { inject, injectable } from "inversify"
import { CacheConfig, CfApiRateLimitConfig, PaypalConfig } from "@curefit/config-mongo"
import { ConfigService, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import { IUserService } from "@curefit/user-client"
import { IUserMappingReadOnlyDao, USER_MODELS_TYPES } from "@curefit/user-models"
import { UserMapping } from "@curefit/issue-common"
import * as _ from "lodash"
import { USER_CLIENT_TYPES } from "@curefit/user-client"
import { WalletBalance } from "@curefit/fitcash-common"
import { EatSummary } from "@curefit/eat-common"


@injectable()
export class CacheHelper {
    constructor(
        @inject(CACHE_TYPES.CultReadOnlyCache) private cultCache: ICultReadOnlyCache,
        @inject(CACHE_TYPES.WalletBalanceCache) protected walletBalanceCache: IWalletBalanceCache,
        @inject(CACHE_TYPES.EatReadOnlyCache) protected eatCache: IEatReadOnlyCache,

        @inject(PAGE_CONFIG_TYPES.ConfigService) private configService: ConfigService,
        @inject(CULT_CLIENT_TYPES.CultFitService) private cultService: ICultService,
        @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
        @inject(USER_MODELS_TYPES.UserMappingReadOnlyDao) private userMappingReadOnlyDao: IUserMappingReadOnlyDao
    ) {

    }

    async getWalletBalance(userId: string): Promise<WalletBalance> {
        return this.walletBalanceCache.getBalance(userId)
    }

    async getUser(userId: string): Promise<User> {
        return await this.userService.getUser(userId)
    }

    async getUsers(userIds: string[]): Promise<{ [userId: string]: User }> {
        const users = await this.userService.getUsersCached(userIds)
        return users.reduce<{ [userId: string]: User }>((accumulator, user) => {
            accumulator[user.id] = user
            return accumulator
        }, {})
    }

    async getCultSummary(userId: string): Promise<CultSummary> {
        const offersConfig: CacheConfig = this.configService.getConfig<CacheConfig>("CF_API_CACHE")
        const cultSummary: CultSummary = await this.cultService.getCultSummary(userId)
        return cultSummary
    }

    async getCultSummaryForAllSubUsers(userId: string): Promise<{ [userId: string]: CultSummary }> {
        let subUserIds: string[] = []
        const subUserRelations = (await this.userService.getUser(userId)).subUserRelations
        if (!_.isEmpty(subUserRelations)) {
            subUserIds = _.map(subUserRelations, subUserRelation => subUserRelation.subUserId)
        }
        const userIds = [userId, ...subUserIds]
        const cultSummaryMapPromise: Promise<{ [userId: string]: CultSummary }> = this.cultService.getCultSummaries(userIds)
        return cultSummaryMapPromise
    }

    async getUserMappedSegments(userId: string): Promise<UserMapping> {
        const offersConfig: any = this.configService.getConfig("TEST_USERS")
        let userMappedSegment: UserMapping
        if (offersConfig.configs && offersConfig.configs["cyclops_users"].includes(userId)) {
            userMappedSegment = await this.userMappingReadOnlyDao.findOne({
                userId,
                active: true
            })
        }
        return userMappedSegment
    }

    async getCfApiRateLimitConfig(): Promise<CfApiRateLimitConfig> {
        return this.configService.getConfig<CfApiRateLimitConfig>("CF_API_RATE_LIMIT")
    }

    getCfApiRateLimitConfigSync(): CfApiRateLimitConfig {
        return this.configService.getConfig<CfApiRateLimitConfig>("CF_API_RATE_LIMIT")
    }

    async getPaypalConfig(): Promise<PaypalConfig> {
        return this.configService.getConfig<PaypalConfig>("PAYPAL")
    }

    async getEatSummary(userId: string): Promise<EatSummary> {
        return this.eatCache.getEatSummary(userId)
    }

    async setCfApiRateLimitConfig(config: CfApiRateLimitConfig): Promise<CfApiRateLimitConfig> {
        return this.configService.setConfig<CfApiRateLimitConfig>(config)
    }
}
