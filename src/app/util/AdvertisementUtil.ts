import { TimeUtil } from "@curefit/util-common"
import { UserContext } from "@curefit/userinfo-common"
import { ProductType } from "@curefit/product-common"
import { User } from "@curefit/user-common"

export class AdvertisementUtil {

    public static getSessionIdForAdvertisement(userContext: UserContext, user: User): string {
        const userId = userContext.userProfile.userId
        const date = TimeUtil.todaysDate(userContext.userProfile.timezone, "YYYY-MM-DD")

        // if (user.isInternalUser) {
        //     return uuid.v1()
        // }

        // for once in 12 hours case
        // const isPM = new Date().getHours() > 11
        // follows userId-dateString-AM/PM
        // return `${userId}-${date}-${isPM ? "PM" : "AM"}`

        // for once in 24 hours
        return `${userId}-${date}`
    }

    // public static getAdvertisementTypeFromContent(contentType: AdvertisementSupportedCategories) {
    //     switch (contentType) {
    //         case "EAT":
    //             return CPASupportedCategories.EAT_ON_DEMAND_VIDEO
    //         case "POP_LIVE":
    //             return CPASupportedCategories.POP_LIVE_ON_DEMAND_VIDEO
    //         default:
    //             return undefined
    //     }
    // }

    public static getAddParams(contentCategory: ProductType, contentId: string) {
        return {
            contentCategory: contentCategory,
            adsEnabled: true
        }
    }

}
