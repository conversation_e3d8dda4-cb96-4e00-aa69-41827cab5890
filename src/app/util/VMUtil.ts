import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { ReviewRequest } from "@curefit/eat-api-client"
import { GenericError } from "@curefit/error-client"
import { Lat<PERSON>ong, Tenant } from "@curefit/base-common"
import { OrderSource } from "@curefit/order-common"
import { ProductType } from "@curefit/product-common"
import { UserContext } from "@curefit/userinfo-common"

/**********************************************************************
 STEPS TO FOLLOW :-

 1. Define the interface for your request and include in RequestTypes
 2. Typecast to the required interface before creating promise
 3. Create the promise with appropriate parameters from request and store in map
 ***********************************************************************/

export type RequestTypes =  IPromiseRequest | IWholefitWidgetData | IWholefitServicabilityRequest | IEatLiveWidgetDataRequest | INCCartOfferRequest | ISegmentOverrideRequest | ISkinHairSegmentRequest

export interface INCCartOfferRequest {
    productType: ProductType,
    userContext: UserContext,
    userId: string,
    deviceId: string,
    productIds?: string[],
    source: OrderSource,
    cityId: string
}
export interface IPromiseRequest {
    request?: ReviewRequest
    areaId?: string
    productId?: string
    userId?: string
}

interface IWholefitServicabilityRequest {
    latLong: LatLong
    cityId: string
}

interface IWholefitWidgetData {
    areaId: string
    userId: string
    deviceId: string
    source: OrderSource
}

export interface ISegmentOverrideRequest {
    userId: string
}

export interface IEatLiveWidgetDataRequest {
    newRecipesRequired: boolean,
    trendingRecipesRequired: boolean,
    recipeoftheDayRequired: boolean,
    date: string
    userId: string
    vegOnly: boolean
    location: string
    tenant: Tenant
}

export interface ICultMindSummaryRequest {
    userId: string
}

export interface ICultComplimentaryRequest {
    userId: string
}

export interface IGoldMembershipRequest {
    userId: string
}

export interface IGoldTrialRequest {
    userId: string
}

export interface ISkinHairSegmentRequest {
    segmentId: string
    userContext: UserContext
}

export const SEGMENT_OVERRIDE = "segment-override-data"
export const SEGMENT_OVERRIDE_REDIS_KEY = "cfapi:segmentOverride"
export const SKIN_HAIR_SEGMENT_VALIDATION_API_KEY = "skin-hair-segment-validation"
export const FOOD_MARKETPLACE_CLP_RESPONSE = "cfapi:foodmp-wellnessclp"

export class PromiseCache {
    promiseMap: Map<string, Promise<any>>
    interfaces: CFServiceInterfaces
    constructor(interfaces: CFServiceInterfaces) {
        this.promiseMap = new Map<string, Promise<any>>()
        this.interfaces = interfaces
    }

    getPromise(key: string, promiseProperties: RequestTypes): Promise<any> {
        if (this.promiseMap.get(key)) {
            return this.promiseMap.get(key)
        } else {
            const promise: Promise<any> = this.fetchPromise(key, promiseProperties)
            this.promiseMap.set(key, promise)
            return promise
        }
    }

    fetchPromise(key: string, promiseProperties: RequestTypes): Promise<any> {
        let req
        switch (key) {
            case "eat-api":
                req = promiseProperties as IPromiseRequest
                return this.interfaces.eatApiClientService.getEatReviewResponse(req.request)
            case "eat-available-meal-slot-promise":
                req = promiseProperties as IWholefitWidgetData
                return this.interfaces.deliveryAreaService.getMealSlotsForArea(req.areaId)
            case "wholefit-v2-clp-data":
                req = promiseProperties as IWholefitWidgetData
                return this.interfaces.wholefitService.getCLPData(req.areaId, req.userId, req.deviceId, req.source)
            case "wholefit-serviceability":
                req = promiseProperties as IWholefitServicabilityRequest
                return this.interfaces.wholefitService.getServiceability(req.latLong, req.cityId)
            case "user-platform-segments":
                req = promiseProperties as IPromiseRequest
                return this.interfaces.clsUtil.getPlatformSegments()
            case "eatlive-clp-data":
                req = promiseProperties as IEatLiveWidgetDataRequest
                return this.interfaces.diyService.getDIYCustomRecipes(req.userId, req.vegOnly, req.newRecipesRequired, req.recipeoftheDayRequired, req.trendingRecipesRequired, req.tenant, req.date, req.location)
            case "cult-mind-summary":
                req = promiseProperties as ICultMindSummaryRequest
                return this.interfaces.userCache.getCultSummary(req.userId)
            case SEGMENT_OVERRIDE:
                req = promiseProperties as ISegmentOverrideRequest
                return this.interfaces.redisDao.getHashField(SEGMENT_OVERRIDE_REDIS_KEY, req.userId)
            case "cult-complimentary-access":
                req = promiseProperties as ICultComplimentaryRequest
                return this.interfaces.cultFitServiceNew.getComplimentaryMemberships({userId: req.userId, appName: "CUREFIT_APP"})
            case "cultpass-gold-memberships":
                req = promiseProperties as IGoldMembershipRequest
                return this.interfaces.membershipService.getMembershipsForUser(req.userId, "curefit", ["GYMFIT_GA", "GYMFIT_GX"])
            case "cultpass-trial-usage":
                req = promiseProperties as IGoldTrialRequest
                return this.interfaces.gymfitService.getTrialUsage(req.userId)
            case SKIN_HAIR_SEGMENT_VALIDATION_API_KEY:
                req = promiseProperties as ISkinHairSegmentRequest
                return this.interfaces.segmentService.doesUserBelongToSegment(req.segmentId, req.userContext)
            default:
                const genericErr: GenericError = new GenericError({message: "Error while fetching required params"})
                genericErr.statusCode = 500
                return Promise.resolve(genericErr)
        }
    }
}
