import { UserContext } from "@curefit/userinfo-common"
import AppUtil from "./AppUtil"
import * as _ from "lodash"
import { FoodPackBooking, SubFoodPackBooking } from "@curefit/shipment-common"
import { MealSlot, PackState } from "@curefit/eat-common"
import { ICapacityService } from "@curefit/masterchef-client"

export class EatSubscriptionUtil {
    static offerText: string = "Upto 35% off on Add-ons"
    static ADD_CHANGE_MEAL_NOT_SUPPORTED_TAG = "ADD_CHANGE_MEAL_NOT_SUPPORTED"
    constructor() { }

    static async createPackAvailabilityMap(capacityService: ICapacityService, areaId: string, mealSlot: MealSlot): Promise<Map<string, boolean>>  {
        const inventoryResult = await capacityService.getInventoryForPacksBasedOnMenuAlone(areaId, mealSlot)
        const packAvailabilityMap = new Map<string, boolean>()
        Object.keys(inventoryResult).forEach(day => {
            const inventoryForDay = inventoryResult[day]
            Object.keys(inventoryForDay).forEach(packId => {
                const inventoryForPack =  inventoryForDay[packId]
                if (inventoryForPack.total > 0) {
                    packAvailabilityMap.set(packId, true)
                }
            })
        })
        return packAvailabilityMap
    }

    static includeAddPackWidget( userContext: UserContext, packState: PackState ): boolean {
        return AppUtil.isSubscriptionAttachSupported(userContext) && packState === "ACTIVE" && false // removing the addon widget temporarily
    }

    static isSubscriptionAddonSupported(userContext: UserContext, subPackBooking: SubFoodPackBooking[]): boolean {
        return !AppUtil.isSubscriptionAttachSupported(userContext) || (AppUtil.isSubscriptionAttachSupported(userContext) && _.isEmpty(subPackBooking))
    }

    static isAddonPresentInPack(userContext: UserContext, subPackBooking: SubFoodPackBooking[]): boolean {
        return AppUtil.isSubscriptionAttachSupported(userContext) && !_.isEmpty(subPackBooking)
    }

    static shouldWidgetBeIncludedForNoAttachSubscription( userContext: UserContext, foodPackBooking: FoodPackBooking ): boolean {
        return (AppUtil.isSubscriptionAttachSupported(userContext) && !_.isNil(foodPackBooking) && _.isEmpty(foodPackBooking.subPackBookings) )
    }

    static isNotSubscriptionPack(userContext: UserContext, foodPackBooking: FoodPackBooking): boolean {
        return (AppUtil.isSubscriptionAttachSupported(userContext) && _.isNil(foodPackBooking))
    }

    static isSubscriptionAttachPurchased(userContext: UserContext, foodPackBooking: FoodPackBooking): boolean {
        return AppUtil.isSubscriptionAttachSupported(userContext) && !_.isNil(foodPackBooking) && !_.isEmpty(foodPackBooking.subPackBookings)
    }


}
