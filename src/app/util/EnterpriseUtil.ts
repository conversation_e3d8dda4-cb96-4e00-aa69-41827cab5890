import { ENTERPISE_CLIENT_TYPES, IEnterpriseClientService } from "@curefit/enterprise-client"
import { Corporate } from "@curefit/enterprise-common"
import { inject, injectable } from "inversify"
import { AccordionSectionItem } from "../common/views/ProfileWidgetView"
import { RASHI_CLIENT_TYPES, IUserAttributeCacheClient } from "@curefit/rashi-client"
import { Membership } from "@curefit/membership-commons"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { MembershipItem } from "@curefit/vm-models"

@injectable()
class EnterpriseUtil {
    constructor(@inject(ENTERPISE_CLIENT_TYPES.IEnterpriseClientService) private enterpriseClient: IEnterpriseClientService,
                @inject(RASHI_CLIENT_TYPES.UserAttributeCacheClient) private userAttributeClient: IUserAttributeCacheClient,
                @inject(BASE_TYPES.ILogger) private logger: ILogger) {
        this.getCorpLogo = this.getCorpLogo.bind(this)
    }

    public async getCorpLogo(userId: string) {
        return await this.enterpriseClient.getCorporateByUserId(userId)
    }

    public async getCorpMembershipCard(userId: string): Promise<any> {
        try {
            const corpBenefits = await this.enterpriseClient.fetchCorporateForProfileCard(userId)
            const benefitCount = corpBenefits.benefitCount ?? 0
            if (corpBenefits != null) {
                return {
                    title: "cultpass Corp",
                    subtitle: `${corpBenefits.corporate.name} • ${benefitCount}${benefitCount <= 1 ? " Benefit" : " Benefits" }`,
                    membershipState: "ACTIVE",
                    membershipStateTextColor: "statusGreen",
                    backImage: "image/vm/4cb02738-286c-4ec8-a783-3b7b9327e662.png",
                    cardAction: {
                        actionType: "NAVIGATION",
                        title: "VIEW ALL BENEFITS",
                        url: "curefit://enterpriseclp"
                    },
                    iconDimensions: {
                        iconTopSpacing: 40,
                        iconHeight: 74,
                        iconWidth: 67
                    },
                    itemType: "CULT_PASS_CORP"
                }
            }
            return null
        } catch (e) {
            this.logger.error(`EnterpriseUtils::getCorpMembershipCard Error while build Corp Membership card  for user id : ${userId} with error ` + e)
            return null
        }
    }
    // Note: This is only for production testing
    // public async getCultPassCorpAccordionSection(userId: string): Promise<AccordionSectionItem> {
    //     const attributeToFetch = ["cult_pass_corp_code_tobe_activated"]
    //     const userAttributesResponse = await this.userAttributeClient.getCachedUserAttributes(parseInt(userId), attributeToFetch)
    //     const userAttributes = userAttributesResponse.attributes
    //     if (userAttributes.get("cult_pass_corp_code_tobe_activated")) {
    //         return {
    //             title: "cultpass CORP",
    //             tag: "NEW",
    //             icon: "CORP",
    //             action: {
    //                 actionType: "NAVIGATION",
    //                 url: "curefit://enterpriseclp"
    //             }
    //         }
    //     }
    //     const corp: Corporate = await this.enterpriseClient.getCorporateByUserId(userId)
    //     if (corp) {
    //         return {
    //             title: "cultpass CORP",
    //             tag: "NEW",
    //             icon: "CORP",
    //             action: {
    //                 actionType: "NAVIGATION",
    //                 url: "curefit://enterpriseclp"
    //             }
    //         }
    //     }
    //     return null
    // }

    public getCultPassCorpAccordionSection(): AccordionSectionItem {
        return {
            title: "cultpass CORP",
            icon: "CORP",
            action: {
                actionType: "NAVIGATION",
                url: "curefit://enterpriseclp"
            },
            subtitle: "View All Benefits"
        }
    }
}

export default EnterpriseUtil
