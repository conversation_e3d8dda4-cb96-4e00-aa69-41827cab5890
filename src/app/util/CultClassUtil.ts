import { UserContext } from "@curefit/userinfo-common"
import * as moment from "moment"
import * as _ from "lodash"
import { DisplayMovement, SimplePart, SimpleWod } from "@curefit/fitness-common"
import { CollapsibleProperties, MiniGoalBannerWidget, MiniGoalWidget, WODInfoWidgetV2, WorkoutComponent, WorkoutMovementInfo } from "../page/PageWidgets"
import { CdnUtil, eternalPromise } from "@curefit/util-common"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { CultClass } from "@curefit/cult-common"
import { ActivityAttributeNamespace, ActivityStoreAttributeSearchRequest } from "@curefit/logging-common"
import { Action } from "@curefit/apps-common"
import CultUtil from "./CultUtil"
import { injectable } from "inversify"
import WidgetBuilder from "../page/vm/WidgetBuilder"

@injectable()
class CultClassUtil {
    public getWodInfoWidgetV2(userContext: UserContext, wod: SimpleWod, isFirstTimeUser: boolean, loggedMovements?: any): WODInfoWidgetV2 {
      if (_.isEmpty(wod.parts)) {
        return null
      }
      const yogaSupportedMuslces = ["Vyayama", "Suryanamaskara", "Asana", "Pranayama", "Yoganidra", "Dharana", "Dhyana", "Meditation"]
      const muscleFocusLower = wod.muscleFocus?.toLowerCase() // Handle potential null/undefined for muscleFocus
      const isYogaMuscleFocus = yogaSupportedMuslces.some(term => term.toLowerCase() === muscleFocusLower)
      const title = isYogaMuscleFocus ? "Practice of the day" : "Workout of the day"

      const focus = wod.focus
      const collapsibleProperties: CollapsibleProperties = {
        isCollapsible: true,
        isCollapsed: !isFirstTimeUser && (_.isEmpty(loggedMovements))
      }
      const workoutInfo = this.getWorkoutInfoForWOD(userContext, wod, loggedMovements)
      if (_.isNil(workoutInfo) || _.isEmpty(workoutInfo)) {
        return null
      }

      const warmUp = wod.parts.find(part => part.partType === "MOBILITY")
      const coolDown = wod.parts.find(part => part.partType === "COOL DOWN")
      // const preWorkoutInstructions = this.getPreWorkoutInstructions(warmUp)
      // const postWorkoutInstructions = this.getPostWorkoutInstructions(coolDown)

      return new WODInfoWidgetV2(title, focus, workoutInfo, null, null, collapsibleProperties, {
        spacing: {
          top: 20,
          bottom: 0
        }
      })
    }

    public getWorkoutInfoForWOD(userContext: UserContext, wod: SimpleWod, loggedMovements?: any): WorkoutMovementInfo[] {
        const workoutInfo: WorkoutMovementInfo[] = []

        const mainWorkoutList = wod.parts.filter(part => part.partType === "MAIN" || part.partType === "MAIN WORKOUT")
        const finisher = wod.parts.find(part => part.partType === "FINISHER")

              const mainWorkoutInstructionsList = mainWorkoutList.map((part, index) => this.getWorkoutInstructions(userContext, part, `Main Workout ${index + 1}`, loggedMovements))
        const finisherInstructions = this.getWorkoutInstructions(userContext, finisher, "FINISHER")

        if (!_.isEmpty(mainWorkoutInstructionsList)) { workoutInfo.push(...mainWorkoutInstructionsList) }
        if (!_.isNil(finisherInstructions)) { workoutInfo.push(finisherInstructions) }

        return _.filter(workoutInfo, movementInfo => !_.isEmpty(movementInfo))
      }

      public getWorkoutInstructions(userContext: UserContext, part: SimplePart, title: string, loggedMovements?: any): WorkoutMovementInfo {
        if (_.isNil(part) || _.isNil(part.movements) || _.isEmpty(part.movements)) {
          return null
        }
        let workoutComponents: WorkoutComponent[] = part.movements.map(movement => this.getWorkoutComponentsFromMovements(userContext, movement, loggedMovements))

        if (_.find(workoutComponents, (component: WorkoutComponent)  => _.isNil(component.image))) {
          workoutComponents = workoutComponents.map((component: WorkoutComponent) => {
              return {...component, image: null}
          })
     }

        return {
          title: title,
          note: part.notes,
          goal: part.goal ?? part.subPartType ?? title,
          subPartType: part.goal != null ? part.subPartType : null,
          strategy: part.strategy,
          showGoalView: true,
          workoutComponents: workoutComponents
        }
      }

      public getWorkoutComponentsFromMovements(userContext: UserContext, movement: DisplayMovement, loggedMovements?: any): WorkoutComponent {
        const video = _.find(movement.media, { type: "VIDEO" })
        let action: Action
        if (!_.isNil(video) && !_.isNil(video.url)) {
          action = {
            actionType: "NAVIGATION",
            url: `curefit://fl_videoplayer?videoUrl=${encodeURIComponent(video.url)}&absoluteVideoUrl=${encodeURIComponent(CdnUtil.getCdnUrl(video.url))}`,
            analyticsData: { widgetName: "SGT_WOD_VIDEO_PLAYED" }
          }
        }
        const image = _.find(movement.media, { type: "THUMBNAIL_IMAGE"})
        let imageUrl
        if (!_.isNil(image)) {
          imageUrl = image.url
          if (!_.isNil(imageUrl) && imageUrl.startsWith("curefit-content/")) {
            imageUrl = imageUrl.split("curefit-content/")[1]
          }
          imageUrl = "https://cdn-images.cure.fit/www-curefit-com/image/upload/w_350,f_auto,q_auto:eco/" + imageUrl
        }

        let description
        let secondaryTitle
        let secondaryTitleStyle

        if (!_.isEmpty(loggedMovements) && loggedMovements.id === movement._id) {
          secondaryTitle = !_.isEmpty(loggedMovements.workoutSet) ? loggedMovements.workoutSet.weight[0].toString() + " kgs x " + loggedMovements.workoutSet.reps.toString() + " Reps" : null
          description =  "Last Logged on " + moment(loggedMovements.date).format("DD MMM")
          secondaryTitleStyle = { fontSize: 14, fontWeight: "700", color: "#0FE498" }
        }

        return {
          title: movement.title,
          image: imageUrl,
          action: action,
          description: description,
          secondaryTitle: secondaryTitle,
          secondaryTitleStyle: secondaryTitleStyle
        }
      }

      public async getMiniGoalDetails(userId: string, serviceInterfaces: CFServiceInterfaces, wod: SimpleWod, classStartDate?: string): Promise<{
        miniGoal?: {
          goalTitle: string
          minClass: number
          numClassesAttended: number
          goalDescription?: string
          goalImageUrl?: string
        },
        focusArea?: {
          title?: string
          focusDescription?: string
          imageUrl?: string
        }
      }> {

              try {
                serviceInterfaces.logger.info(`MINIGOAL: 02: ${userId}`, wod?.miniGoalId)
                const miniGoalPromise = !_.isEmpty(wod.miniGoalId) ? serviceInterfaces.herculeService.getActiveMiniGoalByBenefitId(wod.miniGoalId, classStartDate) : null
                const benefitPromise = serviceInterfaces.herculeService.getMiniGoalBenefitBySimpleWodId(wod._id)
                const activityCountPromise = !_.isEmpty(wod.miniGoalId) ? serviceInterfaces.loggingService.getActivityCountFor({
                    userId: [userId],
                    activityType: ["CULT_CLASS"],
                    scoreRange: {
                      gt: 0
                    },
                    miniGoalId: wod.miniGoalId,
                    dateRange: {
                        gte: moment().startOf("month").format("YYYY-MM-DD"),
                        lte: moment().endOf("month").format("YYYY-MM-DD")
                    }
                }) : null
                const appFocusAreaPromise = !_.isEmpty(wod.description) ? serviceInterfaces.herculeService.getAppFocusAreaByName(encodeURIComponent(wod.description)) : null

                const [miniGoal, benefit, activityCount, appFocusArea] = await Promise.all([miniGoalPromise, benefitPromise, activityCountPromise, appFocusAreaPromise])

                serviceInterfaces.logger.info(`MINIGOAL: 03: ${userId}`, miniGoal)
                serviceInterfaces.logger.info(`MINIGOAL: 04: ${userId}`, benefit)
                serviceInterfaces.logger.info(`MINIGOAL: 05: ${userId}`, activityCount)

                const res: {
                  miniGoal?: {
                    goalTitle: string
                    minClass: number
                    numClassesAttended: number
                    goalDescription?: string
                    goalImageUrl?: string
                  },
                  focusArea?: {
                    title?: string
                    focusDescription?: string
                    imageUrl?: string
                  }
                } = {
                  miniGoal: null,
                  focusArea: null
                }

                if (!_.isEmpty(miniGoal) && !_.isEmpty(benefit)) {
                  // serviceInterfaces.logger.info(`MINIGOAL: 06: ${userId}`, benefit, miniGoal)
                    res.miniGoal = {
                        goalTitle: benefit.title,
                        minClass: miniGoal.minClass,
                        numClassesAttended: activityCount?.count,
                        goalDescription: benefit.description,
                        goalImageUrl: benefit?.url
                    }
                }
                if (!_.isEmpty(appFocusArea) && !_.isEmpty(wod.description)) {
                  const filteredAppFocusArea =  appFocusArea.filter(obj => obj.title === wod.description)
                  if (!_.isEmpty(filteredAppFocusArea) && !_.isEmpty(filteredAppFocusArea[0]?.url)) {
                  res.focusArea = {
                    title: wod.description,
                    focusDescription: filteredAppFocusArea[0]?.description ?? "",
                    imageUrl: filteredAppFocusArea[0]?.url
                  }
                }
                }
                // serviceInterfaces.logger.info(`MINIGOAL: 07: ${userId}`, res)
                return res
              } catch (error) {
                serviceInterfaces.logger.error(`Error while fetching miniGoal details for user: ${userId}, wod: ${wod?.miniGoalId}`, error)
              }
          return null
      }

      public async getPreLoggedMovementInfo(userContext?: UserContext, serviceInterfaces?: CFServiceInterfaces, wod?: SimpleWod, cultClass?: CultClass): Promise<any> {
        const loggingEnabledMovementIds: string[] = []

        for (const part of wod.parts) {
          if (part.movements) {
            for (const movement of part.movements) {
              if (movement.loggingEnabled) {
                loggingEnabledMovementIds.push(movement._id)
              }
            }
          }
        }

          let loggedMovements

          if (!_.isEmpty(loggingEnabledMovementIds)) {
            try {

                const searchRequest: ActivityStoreAttributeSearchRequest = {
                  exerciseId: loggingEnabledMovementIds,
                  userId: [userContext.userProfile.userId],
                  namespace: [ActivityAttributeNamespace.GX],
                  activityType: "WEIGHT_LOGGING",
                  dateRange: {
                    lte: cultClass.date
                  },
                  sortFields: [{
                    field: "sessionId",
                    order: -1
                  }],
                  limit: 1
                }


                const activityAttributes: any = await serviceInterfaces.loggingService.getActivityStoreAttribute(searchRequest)


                if (!_.isEmpty(activityAttributes)) {
                  const workoutSet = !_.isEmpty(activityAttributes[0].exercises) && !_.isEmpty(activityAttributes[0].exercises[0].exerciseExecution)
                    && !_.isEmpty(activityAttributes[0].exercises[0].exerciseExecution.workoutSets)
                      ? activityAttributes[0].exercises[0].exerciseExecution.workoutSets[0] : null
                  loggedMovements = {
                    id: loggingEnabledMovementIds[0],
                    workoutSet: workoutSet,
                    date: activityAttributes[0].date,
                  }
                }
            } catch (error) {
              serviceInterfaces.logger.error("MINIGOAL prelog failed", error)
            }
          }
          return loggedMovements
      }

      public getMiniGoalWidgetPromise(miniGoalDetails: {
        miniGoal?: {
          goalTitle: string
          minClass: number
          numClassesAttended: number
          goalDescription?: string
          goalImageUrl?: string
        },
        focusArea?: {
          title?: string
          focusDescription?: string
          imageUrl?: string
        }} , date: string, userContext: UserContext): MiniGoalWidget {

        return new MiniGoalWidget(moment(date).format("MMMM") + " Mini goal", miniGoalDetails?.miniGoal?.goalTitle ?? "", miniGoalDetails?.miniGoal?.minClass ?? 0, miniGoalDetails?.miniGoal?.numClassesAttended ?? 7, true, false, null, !_.isEmpty(miniGoalDetails?.miniGoal?.goalImageUrl) ? CultUtil.cdnImageUrl(miniGoalDetails?.miniGoal?.goalImageUrl) : null)
      }

      public getMiniGoalBannerWidgetPromise(miniGoalDetails: {
        miniGoal?: {
          goalTitle: string
          minClass: number
          numClassesAttended: number
          goalDescription?: string
          goalImageUrl?: string
        },
        focusArea?: {
          title?: string
          focusDescription?: string
          imageUrl?: string
        }} , userContext: UserContext): MiniGoalBannerWidget {

        return new MiniGoalBannerWidget(miniGoalDetails?.focusArea?.title ?? "", "Focus for Today", miniGoalDetails?.focusArea?.focusDescription, !_.isEmpty(miniGoalDetails?.focusArea?.imageUrl) ? CultUtil.cdnImageUrl(miniGoalDetails?.focusArea?.imageUrl) : null)
      }

      public async buildBannerWidget(userContext: UserContext, widgetId: string, widgetBuilder: WidgetBuilder, serviceInterfaces: CFServiceInterfaces): Promise<any> {
        const widgetResponse = await widgetBuilder.buildWidgets([widgetId], serviceInterfaces, userContext, undefined, undefined)
        if (!_.isEmpty(widgetResponse) && !_.isEmpty(widgetResponse.widgets)) {
            const widget: any = widgetResponse.widgets[0] && widgetResponse.widgets[0].widgetType === "BANNER_CAROUSEL_WIDGET" ? widgetResponse.widgets[0] : undefined
            return widget
        }
        return null
      }

}

export default CultClassUtil