import { GearInventoryUnit } from "@curefit/gear-common"
import { GearService, GearUtil } from "@curefit/gearvault-client"
import { CultsportMetadata, GearProductSnapshots, Order, OrderStatusChange } from "@curefit/order-common"
import { User<PERSON>eli<PERSON>yAddress } from "@curefit/eat-common"
import { ILogger } from "@curefit/base"
import * as _ from "lodash"

export const MIXPANEL_DISTINCT_ID = "MIXPANEL_DISTINCT_ID"
export default class CultsportUtil {
  public static getCollectionRedirectionMap(): Record<string, string> {
    return {
      "CS-GearedCycle": "geared-cycle",
      "w-tees": "womens-tshirt",
      "w-sweatshirts": "winterwear-women",
      "knockout-men": "mens-shorts",
      yogamat: "yoga-mats",
      men: "mens-apparel",
      Snacks: "protein-snacks",
      "Women-Leggings": "leggings",
      "Women-Leggings ": "leggings",
      "fs-towels": "towels",
      "fsg-yoga": "yoga-mats",
      "tees-599": "mens-tshirts",
      "Trainer-Nutrition-New": "supplements",
      highlightswomen: "womens-shorts",
      "MB.FIT": "supplements",
      TheJoints: "supplements",
      "Cycles-CS-New": "Cycle-fit",
      "Accessories-40": "accessories",
      "fsg-women-leggings": "leggings",
      Vitamins: "vitamins",
      "Trainer-Nutrition": "supplements",
      "Treadmill-1": "treadmills",
      "Yoga-Mat": "yoga-mats",
      "Yoga-Mat ": "yoga-mats",
      "Jacket-Mens": "winterwear-men",
      "dbtee-men": "mens-tshirts",
      "Skin-Care": "Personal-care",
      "skin-disorders": "Personal-care",
      "women-shorts": "women-shorts",
      Face: "Personal-care",
      Boldfit: "Personal-care",
      "New-Cycles-CS": "Cycle-fit",
      "Joggers-Mens": "joggers-and-trackpants-men",
      "fsg-men": "mens-tshirts",
      "NEW-All-Day": "all-day-play",
      SETU: "supplements",
      Namhya: "supplements",
      "tshirts-clt-women": "womens-tshirt",
      GNCIndia: "whey-protein-powder",
      "Leggings-DIA": "leggings",
      "gearup-604": "winterwear-women",
      "fab-men": "mens-tshirts",
      "btf-tees-599": "mens-tshirts",
      "fsg-yoga-women": "yoga-mats",
      "AllDayT-S": "all-day-play",
      "cardio-2021": "accessories",
      OneFitPlus: "cardio",
      "fsg-leggings": "leggings",
      Nutrainix: "supplements",
      "Bestsellers-sup": "supplements",
      "shoes-cp": "mens-footwear",
      "Cardio-special": "cardio",
      "gus-dance-men": "womens-shorts",
      womenshorts699: "womens-shorts",
      "jackets-collection": "winterwear-men",
      xxxx: "joggers-and-trackpants-men",
      "flexoft-ss-women": "winterwear-women",
      "mens-joggers": "joggers-and-trackpants-men",
      "Women-joggers-1": "womens-joggers",
      "gearedcycles-noncs": "geared-cycle",
      "25-Equipments": "equipments",
      "DanceT-Shirt": "mens-tshirts",
      "new-collection": "all-day-play",
      "deals-on-cardio": "cardio",
      "mens-tees": "mens-tshirts",
      "%20j": "exercise-bikes",
      " j": "exercise-bikes",
      dbkb: "equipments",
      "fs-shorts-women": "womens-shorts",
      "Women-Shorts": "womens-shorts",
      gymstarter: "equipments",
      "fs-40-men": "mens-tshirts",
      "sweatshirts-men": "winterwear-men",
      "Footwearmen": "mens-footwear",
      "SpinBike": "exercise-bikes",
      "footwear-test": "Footwear",
      "Footwear-rohit": "Footwear",
      "Shorts-Mens": "mens-shorts",
      "womens-sports-bra-lp": "womens-sports-bra",
      "tights": "leggings",
      "leggings-lp": "leggings",
      "immunity-booster-supplements": "vitamins",
      "Cycle-fit": "cycles",
      "cardio": "gym-machines",
      "womens-sports-bra": "sports-bra",
      "smartwatch": "smart-watch",
      "Coords": "gym-co-ords",
      "winterwear-men": "men-jackets-sweatshirts",
      "winterwear-women": "women-jackets-sweatshirts",
      "equipments": "gym-equipments",
      "strength-collection": "gym-weights",
      "whey-protein-powder": "protein-powder",
      "gloves": "gym-gloves",
      "Treadmill": "treadmills",
      "cardio-collection-new": "gym-machines",
      "mens-sports-shoes": "mens-footwear",
      "Footwear": "footwear",
      "new-massager": "massagers",
      "recovery": "massagers",
      "massage-chairs": "massage-chair",
      "others-equipment": "fitness-equipment",
      "accessories": "fitness-accessories"
    }
  }

  public static getDeletedCollectionList(): string[] {
    return [
      "nike-womens-footwear",
      "Isopure",
      "CULTBIKE",
      "fs-tshirts-men",
      "220",
      "fs-60",
      "cultsport-smartwatch",
      "fsg-bs",
      "test-tt",
      "test-gt",
      "test-gte-lte",
      "nike-womens-tees",
      "fs-footwear",
      "fsj-50-1",
      "fsg-polos-women",
      "nike-mens-lowers",
      "wds-40-men",
      "trainerwomenshoes",
      "street",
      "fsg-tees-women",
      "ggs-bottoms-1399-women",
      "ggs-40-men",
      "slay-at-home-women",
      "supercottest",
      "sale-womens-tees-under-999",
      "fsg-nl-women",
      "relaunch-tees-men-999",
      "flydry-cotton-women",
      "nike",
      "code",
      "bogo-3p-june",
      "fs-fw-men",
      "ggs-tees-999-men",
      "bogo-3p",
      "relaunch-cardio-women",
      "ggs-40-women",
      "womens",
      "Flat-35-gear",
      "fitstart-womens-tees",
      "relaunch-cardio-men",
      "ggs-50-women",
      "sale-mens-bottoms-1499",
      "relaunch-1-in-1-women",
      "highlightsmen",
      "fitstart-womens-leggings-1399",
      "sale-25-off",
      "15-off-store",
      "25-off-store",
      "new-launches",
      "fsj-40-1",
      "caps",
      "Fast&UP",
      "footgear-workout",
      "fsg-polos-men",
      "trainers-picks",
      "mobilestand",
      "fsg-footgear",
      "women",
      "ggs-60-men",
      "fsg-mm-men",
      "relaunch-tees-women-999",
      "street-women",
      "slay-at-home-men",
      "fs-fw-women",
      "fsg-bs-women",
      "cm-kit",
      "Flat-gear",
      "gus-mens-tees-under-999",
      "gus-joggers-1199",
      "fsg-shorts-men",
      "220-men",
      "flydry-cotton",
      "Footwear-Mens",
      "lucky-size",
      "fsg-nl",
      "RPM-Fitness",
      "fs-footwear-men",
      "Nutracology",
      "fitstart-20-off-store",
      "womens-tops",
      "btf-shoes-1699",
      "relaunch-1-in-1-men",
      "flydry-cotton-men",
      "bogo-men-tees",
      "ggs-tees-999-women",
      "bogo-men-shorts",
      "sale-15-off",
      "fsg-women-footgear",
      "trainer-v2-tees",
      "gus-womens-leggings-under-1399",
      "wds-40-women",
      "jarin",
      "testsale",
      "fitstart-womens-tees-999",
      "wds-50-men",
      "gus-mens-lowers-1399",
      "Taali",
      "trainer-v2-shoes",
      "fsg-sc-women",
      "fsj-tees",
      "fsj-60-2",
      "fsj-60",
      "fitstart-mens-bottom-1399",
      "nike-men",
      "everydays",
      "flydry",
      "dumbbell",
      "fsg-new-women",
      "fsg-boxing-men",
      "fsg-polos",
      "fsg-sc-men",
      "gym-clothes-for-men",
      "fsg-shorts-women",
      "IN2",
      "fitstart-best-sellers",
      "fsg-acc-backpacks",
      "fsj-40-2",
      "ggs-50-men",
      "mens-tracks"
    ]
  }

  public static getCancelAnalyticsEvent(
    inventoryUnit: GearInventoryUnit,
    order: Order
  ) {
    const productSnapshot = order?.productSnapshots?.filter((product) => {
      return (
        product?.masterProductId === inventoryUnit?.product?.id?.toString()
      )
    })?.[0]
    return productSnapshot
      ? {
          name: "refund",
          type: "CUSTOM_EVENT",
          payload: {
            ecommerce: {
              transaction_id: inventoryUnit.id,
              value: Math.round(inventoryUnit.total_amount_including_fitcash),
              tax: 0,
              currency: "INR",
              coupon: order.couponCode || "NA",
              items: [
                {
                  item_name: productSnapshot.title,
                  item_id: productSnapshot.masterProductId,
                  item_brand: productSnapshot.gearBrandName,
                  currency: "INR",
                  coupon: order.couponCode || "NA",
                  discount: (productSnapshot.price.mrp - productSnapshot.price.listingPrice) || "NA",
                  price: productSnapshot.price.listingPrice,
                  quantity: 1,
                  item_category: productSnapshot.gearCategory,
                  item_category2: productSnapshot.gearArticleType,
                  item_variant: productSnapshot?.attributes?.size || "NA",
                  item_list_name: "NA",
                  item_list_id: "NA",
                  locationId: "NA",
                  index: "NA",
                  promotion_name: "NA",
                },
              ],
            },
          },
        }
      : undefined
  }

  public static getAnalyticsDataForOrderItem(
    order: Order,
    productSnapshot: GearProductSnapshots
  ) {
    return {
      orderId: order.orderId,
      gearOrderId: order.gearOrderId,
      productId: productSnapshot.productId,
      sku: GearService.cfProductIdToGearSkuName(productSnapshot.productId),
      quantity: productSnapshot.quantity,
      masterProductId: productSnapshot.masterProductId,
      productName: productSnapshot.title,
      superCategory: productSnapshot.gearCategory,
      category: productSnapshot.gearArticleType,
      articleType: productSnapshot.gearArticleType,
      brand: productSnapshot.gearBrandName,
      mrp: productSnapshot.price?.mrp,
      listingPrice: productSnapshot.price?.listingPrice,
      size: productSnapshot?.attributes?.size,
      discountPercentage: Math.round(
        ((productSnapshot.price?.mrp - productSnapshot.price?.listingPrice) *
          100) /
          productSnapshot.price?.mrp
      ),
      offersApplied: productSnapshot?.option?.offersInfo?.map(
        (offer) => offer.offerId
      ),
    }
  }

  public static getAnalyticsDataForOrder(
    order: Order
  ) {
    const paymentInitStatus = order?.statusHistory?.find((x: OrderStatusChange) => x.status === "PAYMENT_INITIATED")
    const paymentInitSource = paymentInitStatus?.source
    let paymentChannel = order?.payments?.[0]?.channel
    paymentChannel = paymentChannel === "COD_V2" && order?.payments?.[0]?.data?.fromJuspayRP ? "JUSPAY_RP" : paymentChannel
    const paymentMethod = order?.payments?.[0]?.data?.selectedPaymentMode
    return {
        orderId: order.orderId,
        gearOrderId: order.gearOrderId,
        noOfItems: order.productSnapshots.length,
        totalPayable: order.totalPayable,
        totalAmountPayable: order.totalAmountPayable,
        totalFitCashPayable: order.totalFitCashPayable,
        offersApplied: order.offersApplied,
        totalAmountWithoutDiscount:
          order?.priceDetails?.total_without_discount,
        totalDiscount: order?.priceDetails?.discount,
        paymentInitSource,
        paymentChannel,
        paymentMethod,
        paymentSource:
          order?.statusHistory?.[order?.statusHistory.length - 1]?.source,
        paymentStatus:
          order?.statusHistory?.[order?.statusHistory.length - 1]?.status,
        couponCode: order.couponCode,
        couponOfferId: (order.clientMetadata as CultsportMetadata)?.offerId,
        discountPercentage:
          order?.priceDetails?.total_without_discount >= 0
            ? Math.round(
                ((order?.priceDetails?.total_without_discount -
                  order?.totalPayable) *
                  100) /
                  order?.priceDetails?.total_without_discount
              )
            : 0,
        productIds: order.productSnapshots?.map(
          (productSnapshot) => productSnapshot.masterProductId
        ),
        superCategory: order.productSnapshots
          ?.map((productSnapshot) => productSnapshot.gearCategory)
          ?.join(","),
        category: order.productSnapshots
          ?.map((productSnapshot) => productSnapshot.gearArticleType)
          ?.join(","),
        articleType: order.productSnapshots
          ?.map((productSnapshot) => productSnapshot.gearArticleType)
          ?.join(","),
        brand: order.productSnapshots
          ?.map((productSnapshot) => productSnapshot.gearBrandName)
          ?.join(","),
        sku: order.productSnapshots
          ?.map((productSnapshot) =>
            GearService.cfProductIdToGearSkuName(productSnapshot.productId)
          )
          ?.join(","),
        size: order.productSnapshots?.map(
          (productSnapshot) => productSnapshot?.attributes?.size
        ),
    }
  }

  public static getCollectionSiteMap() {
    return {
      "men": {
        title: "Men",
        urlList: [
          {
            "url": "/mens-sportswear",
            "title": "Men's Sportswear"
          },
          {
            "url": "/mens-footwear",
            "title": "Men's Footwear"
          },
          {
            "url": "/mens-tshirts",
            "title": "Men's Tshirt"
          },
          {
            "url": "/mens-shorts",
            "title": "Men's Shorts",
          },
          {
            "url": "/mens-shirts-and-pants",
            "title": "Men's Shirt & Pants"
          },
          {
            "url": "/joggers-and-trackpants-men",
            "title": "Men's Joggers & Trackpants",
          },
          {
            "url": "/winterwear-men",
            "title": "Men's Shirt & Pants"
          },
        ],
      },
      "women": {
        title: "Women",
        urlList: [
          {
            "url": "/womens-sportswear",
            "title": "Women's Sportswear"
          },
          {
            "url": "/womens-footwear",
            "title": "Women's Footwear"
          },
          {
            "url": "/womens-tshirt",
            "title": "Women's Tshirt",
          },
          {
            "url": "/kurti-collection",
            "title": "Kurti Collection",
          },
          {
            "url": "/Coords",
            "title": "Women's Co-ord Sets"
          },
          {
            "url": "/womens-sports-bra",
            "title": "Women's Sports Bra",
          },
          {
            "url": "/leggings",
            "title": "Women's Leggings & Tights",
          },
          {
            "url": "/womens-shorts",
            "title": "Women's Shorts",
          },
          {
            "url": "/joggers-and-trackpants-women",
            "title": "Women's Joggers and Track Pants",
          },
          {
            "url": "/winterwear-women",
            "title": "Women's Jacket & Sweatshirts",
          },
          {
            "url": "/boyshorts",
            "title": "Boyshorts",
          },
        ],
      },
      "cardio": {
        title: "Cardio",
        urlList: [
          {
            "url": "/cardio",
            "title": "Cardio Machines"
          },
          {
            "url": "/exercise-bikes",
            "title": "Exercise Bikes"
          },
          {
            "url": "/treadmills",
            "title": "Treadmills",
          },
          {
            "url": "/smartcross-b1-bluetooth-enabled-elliptical-cross-trainer/product/4261",
            "title": "Smartcross b1: Bluetooth enabled elliptical cross trainer",
          },
          {
            "url": "/rowers",
            "title": "Rowing Machines"
          },
        ],
      },
      "cycles": {
        title: "Cycles",
        urlList: [
          {
            "url": "/Cycle-fit",
            "title": "Cycles"
          },
          {
            "url": "/geared-cycle",
            "title": "Geared Cycle"
          },
          {
            "url": "/single-speed-cycle",
            "title": "Non-geared Cycle",
          },
          {
            "url": "/kids-cycle",
            "title": "Kids Cycle",
          }
        ],
      },
      "accessories": {
        title: "Accessories",
        urlList: [
          {
            "url": "/accessories",
            "title": "Sports Accessories"
          },
          {
            "url": "/towels",
            "title": "Towel for gym & fitness"
          },
          {
            "url": "/gloves",
            "title": "Gloves & Gym Gloves",
          },
          {
            "url": "/masks",
            "title": "Face Mask",
          },
          {
            "url": "/duffle-bags",
            "title": "Duffle Bags & Travel Bags",
          },
          {
            "url": "/socks",
            "title": "Socks",
          },
          {
            "url": "/yoga-mats",
            "title": "Yoga Mats & Exercise Mats",
          },
          {
            "url": "/jumping-rope",
            "title": "Jumping Rope",
          }
        ],
      },
      "workout-equipment": {
        title: "Workout Equipment",
        urlList: [
          {
            "url": "/equipments",
            "title": "Workout Equipment"
          },
          {
            "url": "/strength-collection",
            "title": "Gym & fitness equipment"
          },
          {
            "url": "/others-equipment",
            "title": "Equipment",
          },
        ],
      },
      "supplements": {
        title: "Supplements",
        urlList: [
          {
            "url": "/cs-nutra",
            "title": "Nutrition"
          },
          {
            "url": "/Personal-care",
            "title": "Personal Care"
          },
          {
            "url": "/whey-protein-powder",
            "title": "Whey Protein",
          },
          {
            "url": "/protein-snacks",
            "title": "Healthy Snacks",
          },
          {
            "url": "/staple-food",
            "title": "Staple Food",
          },
          {
            "url": "/vitamins",
            "title": "Vitamins",
          },
          {
            "url": "/weight-loss-supplements",
            "title": "Weight Loss Supplements",
          },
          {
            "url": "/immunity-booster-supplements",
            "title": "Vitamin and Mineral Supplements",
          },
          {
            "url": "/muscle-recovery-supplements",
            "title": "Mass Gainer Proteins and Supplements",
          },
        ],
      },
      "watch": {
        title: "Watch",
        urlList: [
          {
            "url": "/smartwatch",
            "title": "Smartwatch"
          },
        ],
      },
      "news": {
        title: "News",
        urlList: [
          {
            "url": "/stories.cultsport.com/",
            "title": "Sports News and Health & Fitness Tips"
          },
        ],
      },
      "best-sellers": {
        title: "Best Sellers",
        urlList: [
          {
            "url": "/best-selling-womens-collection",
            "title": "Sportswear for women"
          },
          {
            "url": "/best-selling-mens-collection",
            "title": "Sportswear for men"
          },
          {
            "url": "/best-selling-cycles-st",
            "title": "Outdoor Speed Cycle",
          },
          {
            "url": "/best-selling-cardio-st",
            "title": "Cardio Equipments",
          },
        ],
      }
    }
  }

  public static getCompanySiteMap() {
    // {
    //   "url": "https://static.cult.fit/terms_cult.html#5",
    //   "title": "Refund Policy",
    // },
    return {
      "company": {
        title: "Company",
        urlList: [
          {
            "url": "/contactUs",
            "title": "Contact us"
          },
          {
            "url": "/cultsport-privacy-policy.html",
            "title": "Privacy Policy"
          },
          {
            "url": "/cultsport-terms-of-use.html",
            "title": "Terms Of Use",
          },
          {
            "url": "/lp/watch-support",
            "title": "Watch Support",
          },
          {
            "url": "/download/android-app",
            "title": "Download Android App",
          },
        ],
      }
    }
  }

  public static doesGearAddressContainFloorString(addressLine: string) {
    return addressLine.includes("Floor No:") || addressLine.includes("floor no:") // maybe more cases
  }

  public static correctGearDeliveryAddressLine1(address: UserDeliveryAddress, logger: ILogger): UserDeliveryAddress {
    let clonedAddress: UserDeliveryAddress = _.cloneDeep(address)
    const floorNumber: number = CultsportUtil.extractFloorNumberFromDeliveryAddressLine1(clonedAddress.addressLine1)
    logger.info("for addressId: " + clonedAddress.addressId + " " + "Extracted Floor No: " + floorNumber)
    let newAddressLine1 = ""
    if (_.isInteger(floorNumber) && !_.isNaN(floorNumber)) {
      const addressSplits = clonedAddress.addressLine1.split(",")
      const indexOfFloorString = _.indexOf(addressSplits, "Floor No: " + floorNumber)
      logger.info("for addressId: " + clonedAddress.addressId + " " + "indexOfFloorString: " + indexOfFloorString)
      if (indexOfFloorString === -1) {
        newAddressLine1 = clonedAddress.addressLine1
      } else {
        addressSplits.forEach((a: string, index: number) => {
          if (index !== indexOfFloorString) {
            newAddressLine1 += a?.trim()
          }
          if (index !== addressSplits?.length - 1) {
            newAddressLine1 += ", "
          }
        })
        clonedAddress.addressLine1 = newAddressLine1
      }
      logger.info("for addressId: " + clonedAddress.addressId + " " + "newAddressLine1: " + newAddressLine1)
      clonedAddress = {
        ...clonedAddress,
        gearDeliveryInfo: {
          ...(clonedAddress?.gearDeliveryInfo ?? {}),
          floorNumber: String(floorNumber)
        }
      }
    }
    return clonedAddress
  }

  public static extraLocalityFromAddressLine2(address: UserDeliveryAddress): string {
    // cleanup as per this
    // newAddress.addressLine2 = `${newAddress.addressLine2}, ${newAddress?.structuredAddress?.locality}, ${newAddress?.structuredAddress?.city}, ${newAddress?.structuredAddress?.state} - ${newAddress?.structuredAddress?.pincode} `
    let finalLocality = address.addressLine2
    if (!_.isEmpty(address.structuredAddress)) {
      let finalAddressLine2 = address.addressLine2
      const { city = "", locality = "", pincode = "", state = ""} = address.structuredAddress ?? {}
      finalAddressLine2 = finalAddressLine2.replace(", " + city, "")
      // finalAddressLine2 = finalAddressLine2.replace(", " + locality, "")
      finalAddressLine2 = finalAddressLine2.replace("- " + pincode, "")
      finalAddressLine2 = finalAddressLine2.replace(", " + state, "")

      finalLocality = finalAddressLine2?.trim()
    }
    return finalLocality
  }

  private static extractFloorNumberFromDeliveryAddressLine1(addressLine1: string): number {
    const addressLine1Clone = `${addressLine1}`
    if (CultsportUtil.doesGearAddressContainFloorString(addressLine1Clone)) {
      const splitLine1 = addressLine1Clone.split(",")
      const floorNumberString = splitLine1.find(l => CultsportUtil.doesGearAddressContainFloorString(l))
      const extractedFloorNumber = floorNumberString?.replace("Floor No:", "")?.replace("floor no:", "")?.trim() ?? undefined
      if (!_.isEmpty(extractedFloorNumber) && !_.isNaN(Number(extractedFloorNumber))) {
        return Number(extractedFloorNumber)
      }
    }
    return undefined
  }
}
