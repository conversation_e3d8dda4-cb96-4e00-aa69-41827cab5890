import { C<PERSON><PERSON><PERSON>kout, FitnessProduct, CultPreRegistration, FitnessOfferProduct, FitnessEvent, CultPackType, AccessLevel } from "@curefit/cult-common"
import { MindPack, MindProduct } from "@curefit/mind-common"
import { SELLER_CULT_GGN, SELLER_CULT_HYD, SELLER_CULT_BLR, SELLER_CULT_GEAR, SELLER_DIAGNOSTICS } from "@curefit/finance-common"
import { ProductType, ProductPrice, ProductTaxAndMargin, ProgramPackProduct, UrlPathBuilder, ImageCategoryType } from "@curefit/product-common"
import { FoodProduct as Product } from "@curefit/eat-common"
import * as _ from "lodash"
import { CatalogueVariant as CGCatalogueVariant, GEAR_PRODUCT_ID_PREFIX, CatalogueProduct as CGCatalogueProduct } from "@curefit/gear-common"
import { Status, UserAgent, UserAgentType } from "@curefit/base-common"
import { ProgramPack } from "@curefit/program-client"
import { ConsultationProduct, DiagnosticProductResponse, DiagnosticProduct } from "@curefit/care-common"
import { CatalogueServiceV2Utilities, ICatalogueService, ICatalogueServicePMS, PMSReverseDataMapper } from "@curefit/catalog-client"
import {
    BenefitEntry,
    BenefitType,
    Namespace,
    OfflineFitnessPack,
    PackSearchRequest,
    ProductSubType,
    Visibility
} from "@curefit/pack-management-service-common"
import { GymfitAccessLevel } from "@curefit/gymfit-common"
import { IOfflineFitnessPackService, OfflineFitnessPackService } from "@curefit/pack-management-service-client"
import { ActionUtil, ActionUtilV1, SeoUrlParams } from "@curefit/base-utils"
import AppUtil from "./AppUtil"
import { DEFAULT_IMAGE_VERSION } from "../common/Constants"
import { DEFAULT_ELITE_IMAGE_URL_PRODUCTID, SELECT_IMAGE_URL } from "./CultUtil"
import { request } from "node:http"
import { OfferServiceV3 } from "@curefit/offer-service-client"
import { UserContext } from "@curefit/userinfo-common"

export class CatalogueServiceUtilities {

    static async getCultPMSPacks(OfflineFitnessPackService: IOfflineFitnessPackService, userId: string, cityId: string): Promise<OfflineFitnessPack[]> {
        const searchRequest: PackSearchRequest = {
            namespace: Namespace.OFFLINE_FITNESS,
            productTypes: ["FITNESS"],
            productSubType: ProductSubType.GENERAL,
            visibility: Visibility.APP,
            status: "ACTIVE",
            saleEnabled: true,
        }
        if (userId) searchRequest.userId = userId
        if (cityId) searchRequest.restrictions = { cities: [cityId] }
        const cultPacks = await OfflineFitnessPackService.searchCachedPacks(searchRequest)
        return cultPacks.sort((a, b) => a.sortOrder - b.sortOrder)
    }

    static getSeller(cityId: number) {
        if (cityId === 2) {
            return SELLER_CULT_GGN
        } else if (cityId === 3) {
            return SELLER_CULT_HYD
        } else {
            return SELLER_CULT_BLR
        }
    }

    static gearCatalogueVariantToProduct(variant: CGCatalogueVariant): Product {
        const productId = GEAR_PRODUCT_ID_PREFIX + variant.sku
        return {
            productId,
            title: variant.name,
            productType: "GEAR" as ProductType,
            isPack: false,
            categoryId: "GEAR",
            price: {
                mrp: _.toNumber(variant.price),
                listingPrice: _.toNumber(variant.price),
                currency: "INR"
            } as ProductPrice,
            status: "LIVE" as Status,
            imageVersion: 17,
            // TODO@kapad - use the correct image version
            vendorId: "GEAR",
            subTitle: variant.description,
            taxAndMargin: {
                cfMarginPercent: 0,
                taxes: [{
                    type: "GST",
                    label: "CGST + SGST",
                    percent: 0
                }]
            } as ProductTaxAndMargin,
            sellerId: SELLER_CULT_GEAR,
            imageUrl: _.get(_.first(variant.images), "product_url"),
            imageUrls: _.map(variant.images, "product_url")
        } as Product
    }

    static gearCatalogueProductToProduct(product: CGCatalogueProduct): Product {
        const productId = `${product.id}`
        return {
            productId,
            title: product.name,
            productType: "GEAR" as ProductType,
            isPack: false,
            categoryId: "GEAR",
            price: {
                mrp: _.toNumber(product.price),
                listingPrice: _.toNumber(product.price),
                currency: "INR"
            },
            status: "LIVE" as Status,
            imageVersion: 17,
            vendorId: "GEAR",
            subTitle: product.description,
            taxAndMargin: {
                cfMarginPercent: 0,
                taxes: [{
                    type: "GST",
                    label: "CGST + SGST",
                    percent: 0
                }]
            } as ProductTaxAndMargin,
            sellerId: SELLER_CULT_GEAR,
            imageUrl: _.get(_.first(product.images), "product_url"),
            imageUrls: _.map(product.images, "product_url")
        }
    }

    toCultWorkoutProduct(workout: CultWorkout): FitnessProduct {
        return this.toProduct(workout, "FITNESS")
    }

    toMindWorkoutProduct(workout: CultWorkout): MindProduct {
        return this.toProduct(workout, "MIND")
    }

    toProduct(workout: CultWorkout, productType: ProductType): FitnessProduct | MindProduct {
        const cityId = (workout.centers && workout.centers.length > 0) ? workout.centers[0].cityID : 1
        return {
            productId: workout.productId,
            cultWorkoutId: workout.id,
            cultEventId: null,
            title: workout.name,
            productType: productType,
            isPack: false,
            isEvent: false,
            categoryId: productType,
            price: { // TODO
                mrp: 300,
                listingPrice: 300,
                currency: "INR"
            },
            status: "LIVE",
            imageVersion: 7,
            vendorId: "CULT",
            subTitle: workout.info,
            taxAndMargin: {
                cfMarginPercent: 25,
                gstTaxPercent: 18,
                taxes: [{
                    type: "GST",
                    label: "CGST + SGST",
                    percent: 18
                }]
            },
            sellerId: CatalogueServiceUtilities.getSeller(cityId),
            isTransfer: false,
            isUpgrade: false
        }
    }

    static cultOfferToProduct(cultOffer: CultPreRegistration): FitnessOfferProduct {
        return {
            allowStartDateSelection: cultOffer.allowStartDateSelection,
            maxMembershipStartDate: cultOffer.maxMembershipStartDate,
            fitnessOfferConstruct: cultOffer.offerConstruct,
            productId: CatalogueServiceV2Utilities.getCultPreRegistrationProductId(cultOffer.id),
            centerId: cultOffer.centerID,
            cultOfferId: cultOffer.id,
            isPack: false,
            categoryId: "FITNESS_OFFER",
            price: {
                mrp: cultOffer.advancePrice,
                listingPrice: cultOffer.advancePrice,
                currency: "INR"
            },
            vendorId: "CULT",
            taxAndMargin: {
                cfMarginPercent: 25,
                gstTaxPercent: 18,
                taxes: [{
                    type: "GST",
                    label: "CGST + SGST",
                    percent: 18
                }]
            },
            title: cultOffer.name,
            subTitle: cultOffer.info,
            productType: "FITNESS_PREREGISTRATION",
            status: "LIVE",
            imageVersion: 6,
            sellerId: SELLER_CULT_BLR // TODO: Santhosh
        }
    }

    static toDiagnosticsProduct(diagnosticsProduct: DiagnosticProductResponse): DiagnosticProduct {
        return {
            productId: diagnosticsProduct.productCode,
            title: diagnosticsProduct.productName,
            productType: "DIAGNOSTICS",
            isPack: false,
            subCategoryCode: diagnosticsProduct.subCategoryCode,
            categoryId: diagnosticsProduct.categoryCode,
            duration: diagnosticsProduct.duration,
            price: {
                mrp: diagnosticsProduct.mrp,
                listingPrice: diagnosticsProduct.listingPrice,
                currency: "INR"
            },
            status: "LIVE",
            imageVersion: 1,
            vendorId: "DIAGNOSTICS",
            subTitle: diagnosticsProduct.productDescription,
            taxAndMargin: {
                cfMarginPercent: 0,
                gstTaxPercent: 0,
                taxes: [{
                    type: "GST",
                    label: "CGST + SGST",
                    percent: 0
                }]
            },
            testCount: diagnosticsProduct.testCount,
            baseTestCode: diagnosticsProduct.baseTestCode,
            imageUrl: diagnosticsProduct.imageUrl,
            sellerId: SELLER_DIAGNOSTICS,
            slugValue: diagnosticsProduct.slugValue
        }
    }


    static toBundleProduct(diagnosticsProduct: DiagnosticProductResponse): DiagnosticProduct {
        return {
            productId: diagnosticsProduct.productCode,
            title: diagnosticsProduct.productName,
            productType: "BUNDLE",
            isPack: false,
            subCategoryCode: diagnosticsProduct.subCategoryCode,
            categoryId: diagnosticsProduct.subCategoryCode,
            price: {
                mrp: diagnosticsProduct.mrp,
                listingPrice: diagnosticsProduct.listingPrice,
                currency: "INR"
            },
            status: diagnosticsProduct.productStatus,
            duration: diagnosticsProduct.duration,
            imageVersion: 1,
            vendorId: "DIAGNOSTICS",
            subTitle: diagnosticsProduct.productDescription,
            taxAndMargin: {
                cfMarginPercent: !_.isEmpty(diagnosticsProduct.productPriceDetails) && diagnosticsProduct.productPriceDetails[0].taxRate ? diagnosticsProduct.productPriceDetails[0].taxRate : 0,
                gstTaxPercent: !_.isEmpty(diagnosticsProduct.productPriceDetails) && diagnosticsProduct.productPriceDetails[0].taxRate ? diagnosticsProduct.productPriceDetails[0].taxRate : 0,
                taxes: [{
                    type: "GST",
                    label: "CGST + SGST",
                    percent: !_.isEmpty(diagnosticsProduct.productPriceDetails) && diagnosticsProduct.productPriceDetails[0].taxRate ? diagnosticsProduct.productPriceDetails[0].taxRate : 0
                }]
            },
            testCount: diagnosticsProduct.testCount,
            childProducts: diagnosticsProduct.childProducts,
            baseTestCode: diagnosticsProduct.baseTestCode,
            imageUrl: diagnosticsProduct.imageUrl,
            heroImageUrl: diagnosticsProduct.heroImageUrl,
            sellerId: SELLER_DIAGNOSTICS,
            slugValue: diagnosticsProduct.slugValue
        }
    }

    static toDeviceProduct(diagnosticsProduct: DiagnosticProductResponse): Product {
        return {
            productId: diagnosticsProduct.productCode,
            title: diagnosticsProduct.productName,
            productType: "DEVICE",
            isPack: false,
            categoryId: diagnosticsProduct.categoryCode,
            price: {
                mrp: diagnosticsProduct.mrp,
                listingPrice: diagnosticsProduct.listingPrice,
                currency: "INR"
            },
            status: "LIVE",
            imageVersion: 1,
            vendorId: "DIAGNOSTICS",
            subTitle: diagnosticsProduct.productDescription,
            taxAndMargin: {
                cfMarginPercent: 0,
                gstTaxPercent: 12,
                taxes: [{
                    type: "GST",
                    label: "CGST + SGST",
                    percent: 12
                }]
            },
            imageUrl: diagnosticsProduct.imageUrl,
            sellerId: diagnosticsProduct.infoSection.sellerCode
        }
    }

    static toConsultationProduct(diagnosticsProduct: DiagnosticProductResponse): ConsultationProduct {
        const doctorType = diagnosticsProduct.consultationProduct.doctorType
        return {
            productId: diagnosticsProduct.productCode,
            title: diagnosticsProduct.productName,
            productType: "CONSULTATION",
            isPack: false,
            categoryId: diagnosticsProduct.subCategoryCode,
            price: {
                mrp: diagnosticsProduct.mrp,
                listingPrice: diagnosticsProduct.listingPrice,
                currency: "INR"
            },
            consultationMode: diagnosticsProduct.consultationProduct.consultationMode,
            status: "LIVE",
            imageVersion: 1,
            vendorId: "DIAGNOSTICS",
            hasPlan: diagnosticsProduct.consultationProduct.hasPlan,
            hasPrescription: diagnosticsProduct.consultationProduct.hasPrescription,
            subTitle: diagnosticsProduct.productDescription,
            taxAndMargin: {
                cfMarginPercent: 0,
                gstTaxPercent: doctorType === "MIND_THERAPIST" ? 18 : 0, // TODO - hack to add 18% tax for therapy consultation,
                taxes: [{
                    type: "GST",
                    label: "CGST + SGST",
                    percent: doctorType === "MIND_THERAPIST" ? 18 : 0 // TODO - hack to add 18% tax for therapy consultation
                }]
            },
            duration: diagnosticsProduct.consultationProduct.duration,
            imageUrl: diagnosticsProduct.imageUrl,
            doctorType: diagnosticsProduct.consultationProduct.doctorType,
            heroImageUrl: diagnosticsProduct.heroImageUrl,
            sellerId: SELLER_DIAGNOSTICS,
            tenant: diagnosticsProduct.consultationProduct.tenant,
            timelineTitle: diagnosticsProduct.consultationProduct.timelineTitle,
            centerToConsultationProductOverrideMap: (diagnosticsProduct && diagnosticsProduct.centerToConsultationProductOverrideMap) || {},
            familyType: diagnosticsProduct.consultationProduct.familyType,
            taxRate: diagnosticsProduct.consultationProduct.taxRate
        }
    }

    static mindOfferToProduct(cultOffer: CultPreRegistration): FitnessOfferProduct {
        return {
            allowStartDateSelection: cultOffer.allowStartDateSelection,
            maxMembershipStartDate: cultOffer.maxMembershipStartDate,
            fitnessOfferConstruct: cultOffer.offerConstruct,
            productId: CatalogueServiceV2Utilities.getMindPreRegistrationProductId(cultOffer.id),
            centerId: cultOffer.centerID,
            cultOfferId: cultOffer.id,
            isPack: false,
            categoryId: "MIND_OFFER",
            price: {
                mrp: cultOffer.advancePrice,
                listingPrice: cultOffer.advancePrice,
                currency: "INR"
            },
            vendorId: "CULT",
            taxAndMargin: {
                cfMarginPercent: 25,
                gstTaxPercent: 18,
                taxes: [{
                    type: "GST",
                    label: "CGST + SGST",
                    percent: 18
                }]
            },
            title: cultOffer.name,
            subTitle: cultOffer.info,
            productType: "MIND_PREREGISTRATION",
            status: "LIVE",
            imageVersion: 6,
            sellerId: SELLER_CULT_BLR
        }
    }

    static toFitnessEventProduct(event: FitnessEvent): FitnessProduct {
        return CatalogueServiceUtilities.toEventProduct(event, "FITNESS")
    }

    static toMindEventProduct(event: FitnessEvent): FitnessProduct {
        return CatalogueServiceUtilities.toEventProduct(event, "MIND")
    }

    static toEventProduct(event: FitnessEvent, productType: ProductType): FitnessProduct {
        return {
            productId: event.productId,
            productType: productType,
            cultWorkoutId: null,
            cultEventId: event.id,
            title: event.name,
            subTitle: event.info,
            isPack: false,
            isEvent: true,
            categoryId: productType,
            price: {
                mrp: 0,
                listingPrice: 0,
                currency: "INR"
            },
            status: "LIVE",
            imageVersion: 6,
            vendorId: "CULT",
            taxAndMargin: {
                cfMarginPercent: 25,
                gstTaxPercent: 18,
                taxes: [{
                    type: "GST",
                    label: "CGST + SGST",
                    percent: 18
                }]
            },
            sellerId: SELLER_CULT_BLR,
            isTransfer: false,
            isUpgrade: false
        }
    }

    static toProgramPack(programPack: ProgramPack, productType: ProductType): ProgramPackProduct {
        const productId = CatalogueServiceV2Utilities.getProgramPackProductId(programPack.id)
        const cityId = programPack.cityId ? parseInt(programPack.cityId) : 1
        return {
            productId: productId,
            tenantId: programPack.tenantId,
            packId: programPack.id,
            productType: productType,
            title: programPack.name,
            subTitle: programPack.info,
            isPack: true,
            categoryId: productType,
            price: {
                mrp: programPack.price,
                listingPrice: programPack.price,
                currency: "INR"
            },
            status: "LIVE",
            imageVersion: Number(programPack.meta.mediaVersion),
            vendorId: "CULT",
            taxAndMargin: {
                cfMarginPercent: 25,
                gstTaxPercent: 18,
                taxes: [{
                    type: "GST",
                    label: "CGST + SGST",
                    percent: 18
                }]
            },
            sellerId: CatalogueServiceUtilities.getSeller(cityId)
        }
    }
    static getWorkoutImage(workoutId: number): string {
        if ([1, 2, 3, 4, 5, 6, 22, 27, 28, 29, 33, 34, 35, 36, 37, 38, 39].indexOf(workoutId) !== -1)
            return "/image/icons/workouts/" + CatalogueServiceV2Utilities.getCultWorkoutProductId(workoutId) + "/11.png"
        return "/image/icons/workouts/CULT/11.png"
    }

    static getWorkoutImageByProductId(productId: string): string {
        const workoutId: number = parseInt(productId.replace("CULTWORKOUT", ""))
        return this.getWorkoutImage(workoutId)
    }

    static getAccessLevel(product: OfflineFitnessPack): AccessLevel | GymfitAccessLevel {
        if (!_.isEmpty((product as OfflineFitnessPack)?.restrictions?.centers)) {
            return AccessLevel.CENTER
        }
        return AccessLevel.CITY
    }

    static getExternalAccessLevelId(product: OfflineFitnessPack): string {
        switch (this.getAccessLevel(product)) {
            case GymfitAccessLevel.CENTER:
                return String((product as OfflineFitnessPack).restrictions.centers[0])
            case GymfitAccessLevel.CITY:
                return (product as OfflineFitnessPack).restrictions.cities[0]
        }
    }

    static extractPackId(productId: string): number {
        if (_.isEmpty(productId)) return null
        if (CatalogueServiceV2Utilities.isProdIdMigratedToPMS(productId)) {
            return 0 // TEMP: Remove this once transfer is migrated to new flow
        }
        if (productId?.startsWith("CULTPACKNEW")) {
            return 0
        } else if (productId?.startsWith("CULTPACK")) {
            return parseInt(productId.replace("CULTPACK", ""))
        } else if (productId.startsWith("CULTUPGRADE")) {
            return parseInt(productId.replace("CULTUPGRADE", ""))
        } else if (productId.startsWith("CULTTRANSFER")) {
            return parseInt(productId.replace("CULTTRANSFER", ""))
        } else if (productId.startsWith("CULTWORKOUT")) {
            return parseInt(productId.replace("CULTWORKOUT", ""))
        } else {
            throw new Error("Invalid pack id")
        }
    }

    static isPack(product: OfflineFitnessPack): boolean {
        if (!product.product.productSubType) return false
        if ([ProductSubType.UPGRADE, ProductSubType.TRANSFER].includes(product.product.productSubType)) return false
        switch (product.productType) {
            case "FITNESS":
                if (product.id?.startsWith("CULTWORKOUT")) return false
                return true
            case "GYMFIT_FITNESS_PRODUCT":
            case "PLAY":
            case "GYM_PT_PPC_PRODUCT":
            case "GYM_PT_PRODUCT":
            case "LUX_FITNESS_PRODUCT":
                return true
            default:
                return false
        }
    }

    static getPackPageAction(product: OfflineFitnessPack, userAgent?: UserAgentType, seoParams?: SeoUrlParams, siteMap?: boolean, pageFrom?: string, newPackPage?: boolean): string {
        // if (product.link) return product.link
        if (_.isEmpty(userAgent) || userAgent == "APP") return CatalogueServiceUtilities.getPackDetailsPageAction(product.id)
        const action = ActionUtilV1.cultFitPackV2(seoParams, product.id, false, pageFrom, siteMap)
        return newPackPage ? AppUtil.getNewPackPageActionUrl(action) : action
    }

    static getPackWebAction(product: OfflineFitnessPack): string {
        if (product.product.productType === "FITNESS") {
            return CatalogueServiceUtilities.getPackPageAction(product, "DESKTOP", null, false)
        } else if (product.product.productType === "GYMFIT_FITNESS_PRODUCT") {
            return `/cult/cult-pass/membership/${product.productId}`
        } else return null
    }

    static getPackDetailsPageAction(productId: string) {
        return `curefit://checkout_v2?productId=${productId}`
    }

    static async getCultMembershipDetailsPageAction(membershipId: string, userContext: UserContext, userAgent?: UserAgentType, pageFrom?: string, newPackPage?: boolean) {
        if (userAgent === "APP" && AppUtil.isMembershipDetailV2PageSupported(userContext)) {
            return AppUtil.getNewMembershipDetailPage(membershipId, false)
        }
        const actionUrl = ActionUtil.cultFitPack("0", membershipId, false, pageFrom, userAgent)
        return newPackPage ?  AppUtil.getNewPackPageActionUrl(actionUrl) : actionUrl
    }

    static getGymfitProductPoints(pack: OfflineFitnessPack, osName: string): string[] {
        const productPoints: string[] = []
        const gymBenefit = pack.product.benefits.find(benefit => benefit.name === "GYMFIT_GA" || benefit.name === "GYMFIT_GX")
        const liveBenefit = pack.product.benefits.find(benefit => benefit.name === "LIVE")
        const acessCreditBenefit = pack.product.benefits.find(benefit => benefit.name === "ACCESS_CREDITS")
        const cultAwayBenefit = pack.product.benefits.find(benefit => benefit.name === "CULT_AWAY")
        const centerAwayBenefit = pack.product.benefits.find(benefit => benefit.name === "CENTER_AWAY")

        if (gymBenefit) productPoints.push("•  Unlimited gym access")
        if (liveBenefit && osName.toLowerCase() !== "ios") productPoints.push("•  Unlimited live workouts")
        if (acessCreditBenefit) productPoints.push(`•  Access up to ${acessCreditBenefit.tickets} sessions`)
        else if (centerAwayBenefit) productPoints.push(`•  ${centerAwayBenefit.tickets} free cult classes per month`)
        else if (cultAwayBenefit) productPoints.push(`•  ${cultAwayBenefit.tickets} free cult classes for non home city per month`)
        return productPoints
    }

    static getFitnessProductImage(product: OfflineFitnessPack, category: ImageCategoryType, userAgent: UserAgentType, customSuffix?: string): string {
        if (this.getAccessLevel(product) === GymfitAccessLevel.CENTER) return SELECT_IMAGE_URL
        return UrlPathBuilder.getPackImagePath(DEFAULT_ELITE_IMAGE_URL_PRODUCTID, product.productType, category, DEFAULT_IMAGE_VERSION, userAgent, customSuffix)
    }

    static getCultSubtitle(product: OfflineFitnessPack): string {
        const isAllIndiaPack = CatalogueServiceUtilities.isAllIndiaPack(product)
        const durationInMonth = Math.max(Math.floor((product.product?.durationInDays ?? 0) / 30), 1)
        if (isAllIndiaPack) {
            return `Book unlimited Classes anytime at any center across cities for ${durationInMonth} months\n\nEvery Cult centre offers a plethora of group workout formats designed and run by highly qualified fitness experts. These workouts are great for newbies and fitness veterans alike, and are guaranteed to show results.`
        }
        return `Book unlimited Classes anytime at any centre in your city for ${durationInMonth} months\n\nEvery Cult centre offers a plethora of group workout formats designed and run by highly qualified fitness experts. These workouts are great for newbies and fitness veterans alike, and are guaranteed to show results.`
    }

    static getFitnessDisplayName(fitnessPack: OfflineFitnessPack): string {
        if (fitnessPack.productType === "FITNESS" && !_.isEmpty(fitnessPack?.restrictions?.cities)) { // Elite packs
            if (CatalogueServiceUtilities.isAllIndiaPack(fitnessPack)) return "All India Elite"
            else return "Cultpass ELITE"
        }
        return !_.isEmpty(fitnessPack.displayName) ? fitnessPack.displayName : fitnessPack.title
    }

    static isAllIndiaPack (fitnessPack: OfflineFitnessPack) {
        const awayBenefit = fitnessPack.product?.benefits?.find(benefit => benefit.name === "CULT_AWAY")
        return (!_.isNil(awayBenefit) && awayBenefit.type === BenefitType.MONTHLY && !_.isNil(awayBenefit.tickets) && awayBenefit.tickets >= 100)

    }

    static async getPackByReferenceId(offlineFitnessPackService: IOfflineFitnessPackService, referenceId: string) {
        try {
            return offlineFitnessPackService.getPackByReferenceId(referenceId)
        } catch (error) {
            return null
        }
    }
}

export default CatalogueServiceUtilities
