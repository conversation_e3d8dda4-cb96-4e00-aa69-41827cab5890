import { PageWidgetM<PERSON> } from "../page/Page"
import { UserContext } from "@curefit/userinfo-common"

export const FLEXI_PACK_VERSION = 5.3
export const STANDING_INSTRUCTION_ANDROID_VERSION = 5.6
export const STANDING_INSTRUCTION_IOS_VERSION = 5.51
export const STANDING_INSTRUCTION_CPANDROID_VERSION = 65
export const STANDING_INSTRUCTION_CPIOS_VERSION = 224
export const MAX_CART_SIZE = 10
export const WHOLE_FIT_V2_CART_LIMIT = 50
export const MAX_CART_SIZE_FOR_INTRO_OFFER = 5
export const EDIT_NAME_VERSION_IOS = 5.66
export const EDIT_NAME_VERSION_ANDROID = 5.8
export const WHY_PAGES_ANDROID_VERSION = 6.2
export const WHY_PAGES_IOS_VERSION = 6.1
export const WHY_PAGES_CPANDROID_VERSION = 28
export const WHY_PAGES_CPIOS_VERSION = 23
export const PACK_REDESIGN_VERSION_IOS = 6.1
export const PACK_REDESIGN_VERSION_ANDROID = 6.3
export const PACK_REDESIGN_CPVERSION_IOS = 31
export const PACK_REDESIGN_CPVERSION_ANDROID = 37

export const AUTO_RENEWAL_VERSION_IOS = 6.2
export const AUTO_RENEWAL_VERSION_ANDROID = 6.33
export const AUTO_RENEWAL_CPVERSION_IOS = 38
export const AUTO_RENEWAL_CPVERSION_ANDROID = 47

export const SUBSCRIPTION_VERSION_IOS = 6.5
export const SUBSCRIPTION_VERSION_ANDROID = 6.6
export const SUBSCRIPTION_CPVERSION_IOS = 51
export const SUBSCRIPTION_CPVERSION_ANDROID = 60

export const DEFAULT_ADDRESS_VERSION_IOS = 6.4
export const DEFAULT_ADDRESS_VERSION_ANDROID = 6.5
export const DEFAULT_ADDRESS_CPVERSION_IOS = 46
export const DEFAULT_ADDRESS_CPVERSION_ANDROID = 55

export const ADD_MEAL_ANDROID = 6.93
export const ADD_MEAL_ANDROID_CP = 89
export const ADD_MEAL_IOS = 6.93
export const ADD_MEAL_IOS_CP = 81
export const SUBS_DEBIT_CARD_ANDROID_VERSION = 6.91
export const SUBS_DEBIT_CARD_IOS_VERSION = 6.91
export const SUBS_DEBIT_CARD_CPVERSION_ANDROID = 73
export const SUBS_DEBIT_CARD_CPVERSION_IOS = 65

export const RENEWAL_WINDOW_DAYS = 3

class MealUtil {
    public static mealSeeMore(userContext: UserContext, isPack: boolean, isKiosk: boolean): PageWidgetMore {
        let action
        if (isPack) {
            if (isKiosk) {
                action = "curefit://infopage?contentId=whyeatfitkiosksubscription&pageType=LIST"
            } else if (userContext.sessionInfo.userAgent === "DESKTOP") {
                action = "curefit://eat-whysubscribe"
            } else
                action = "curefit://infopage?contentId=whyeatfitsubscription&pageType=LIST"
        }
        return {
            title: isPack ? "Why subscribe" : "Why eat.fit",
            text: undefined,
            action: action
        }
    }
}

export default MealUtil
