import * as _ from "lodash"
import { BannerItem, BaseWidget, UserContext } from "@curefit/vm-models"
import { ExtendedPackItem } from "../page/vm/widgets/FitnessPackBrowseWidgetView"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { RUPEE_SYMBOL } from "@curefit/finance-common"
import { WIDGET_ID_TO_PRODUCT_TYPE } from "../page/vm/widgets/sale/BannerUtil"
import { PER_MONTH_PRICE_VARIABLE, PER_MONTH_PRICE_VARIABLE_REGEX } from "../page/vm/constants"

export class SkuPackUtil {

    public static async getStartingPerMonthPrice(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }, widgetId: string) {
        const buildWidgetResponse = await interfaces.widgetBuilder.buildWidgets([widgetId], interfaces, userContext, queryParams, {})
        if (!_.isEmpty(buildWidgetResponse) && !_.isEmpty(buildWidgetResponse.widgets)) {
            const fitnessPackBrowseWidget = buildWidgetResponse.widgets.find((widget) => widget.widgetType === "FITNESS_PACK_BROWSE_WIDGET")
            return !_.isEmpty(fitnessPackBrowseWidget) ? this.getPerMonthPrice(fitnessPackBrowseWidget) : null
        }
        return null
    }

    public static getPerMonthPrice (widget: BaseWidget): string {
        return this.getLowestPricePack(widget).perMonthPriceWithoutDuration
    }

    public static getLowestPricePack (widget: any): ExtendedPackItem {
        const fitnessPackBrowseWidget = widget
        let lowestPricePack = {} as ExtendedPackItem
        for (const pack of fitnessPackBrowseWidget?.expandedMembershipPack?.membershipPacks || []) {
            if (_.isEmpty(lowestPricePack)) {
                lowestPricePack = pack
            }
            const newPrice = Number(pack.perMonthPriceWithoutDuration)
            const lowestPrice = Number(lowestPricePack.perMonthPriceWithoutDuration)
            if (newPrice && lowestPrice && newPrice < lowestPrice) {
                lowestPricePack = pack
            }
        }
        return lowestPricePack
    }

    // To evaluate banner title dynamically
    public static async getSkuTitle(banner: BannerItem, interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }) {
        if (banner.title && banner.title.trim()) {
            const hasPerMonthPriceTag = banner.title.includes(PER_MONTH_PRICE_VARIABLE)
            if (hasPerMonthPriceTag && banner.skuProductType) {
                const widgetId = WIDGET_ID_TO_PRODUCT_TYPE[banner.skuProductType] || ""
                const perMonthPrice = await this.getStartingPerMonthPrice(interfaces, userContext, queryParams, widgetId)
                if (perMonthPrice !== null) {
                    banner.title = (banner.title.replaceAll(PER_MONTH_PRICE_VARIABLE_REGEX, "" + RUPEE_SYMBOL + perMonthPrice))
                }
            }
        }
    }
}