import { ReviewRequest } from "@curefit/eat-api-client"
import { UserContext } from "@curefit/userinfo-common"
import { OrderCreate } from "@curefit/oms-api-client"
import { City, DeliveryInstruction, UserDeliveryAddress } from "@curefit/location-common"
import * as _ from "lodash"
import { DeliveryInfo, FoodProduct } from "@curefit/eat-common"
import AppUtil from "./AppUtil"
import kernel from "../../config/ioc/ioc"
import IUserBusiness from "../user/IUserBusiness"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { OrderProductOption } from "@curefit/order-common"
import { ShipmentStatus } from "@curefit/gandalf-common"
import { Action } from "@curefit/apps-common"
import { CityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { MealAction } from "../common/views/WidgetView"
import { MAX_CART_SIZE } from "@curefit/base-utils"
import { FoodMarketPlaceVariantAddonGroup } from "@curefit/product-common"

export default class FoodMarketplaceUtil {

    private static DUMMY_IMAGE_URL_TEMPLATE = `/image/foodmarketplace/foodmp_dum_NUM.png`
    private static DUMMY_IMAGE_URL_TEMPLATE_KEY = "NUM"
    public static FOODMP_MAX_CART_SIZE = 10
    public static FOOD_MARKETPLACE_DEFAULT_DELIVERY_CHARGE = 25
    public static RESTAURANT_SCREEN_ADDRESS_NUDGE_THRESHOLD_MIN = 10

    public static async generateAlfredRequest(request: ReviewRequest, userContext: UserContext): Promise<OrderCreate> {

        const { cartReviewPayload, userDetailInfo, sessionData } = request
        const { userProfile, sessionInfo } = userContext
        const { deliveryInfo, eatDeliveryInstruction: eatDeliveryInstructionFromClient } = cartReviewPayload.option

        let defaultDeliveryInstruction: DeliveryInstruction = {
            dropInstruction: "ME",
            contactInstruction: "CALL_ME",
            cutleryInstruction: "GIVE_CUTLERY"
        }
        if (eatDeliveryInstructionFromClient) {
            defaultDeliveryInstruction = {
                ...defaultDeliveryInstruction,
                ...eatDeliveryInstructionFromClient,
            }
        }
        const userBusinessObject = kernel.get<IUserBusiness>(CUREFIT_API_TYPES.UserBusiness) // figure a better way out
        const orderCreate: OrderCreate = {
            userId: userProfile.userId,
            deviceId: sessionInfo.deviceId,
            products: request.cartReviewPayload.orderProducts,
            source: userDetailInfo.orderSource,
            cityId: sessionData.cityId,
            useOffersV2: false,
            dontCreateRazorpayOrder: true,
            useFitCash: cartReviewPayload.useFitCash ? cartReviewPayload.useFitCash : false,
            deliveryInfo: await Promise.all(_.map(deliveryInfo, async (info): Promise<DeliveryInfo> => {
                let address: UserDeliveryAddress
                if (info.addressId) {
                    address = await userBusinessObject.augmentStructuredAddress(userContext.userProfile.userId, info.addressId)
                    const cutleryInstruction = info.cutleryInstruction
                    address.eatDeliveryInstruction = {
                        ...defaultDeliveryInstruction,
                        cutleryInstruction: cutleryInstruction || defaultDeliveryInstruction.cutleryInstruction
                    }
                }
                return {
                    address: address,
                    mealSlot: "ALL" // hack for making cultery widget work
                }
            })),
            listingBrand: "FOOD_MARKETPLACE",
            // tipAmount: !_.isNil(deliveryTip) ? this.computeDeliveryTip(deliveryTip) : undefined, // maybe later?
            tenant: AppUtil.getTenantFromUserContext(userContext),
            reserve: false,
            offersVersion: 3
        }

        return orderCreate
    }

    public static generateProductAddonVariantComboKey(productId: string, options: OrderProductOption): string {
        const { addons, variants } = options
        let addonsKey = ""
        addons && addons.forEach(a => {
            addonsKey = addonsKey + a.id
        })
        let variantsKey = ""
        variants &&  variants.forEach( v => {
            variantsKey = variantsKey + v.id
        })

        return productId + addonsKey + variantsKey
    }

    public static mapGandalfStatusToFrontendStatus(status: ShipmentStatus): FoodMarketplaceFESupportedStatus {
        switch (status) {
            case ShipmentStatus.QUEUED:
            case ShipmentStatus.PLACED:
                return FoodMarketplaceFESupportedStatus.PLACED
            case ShipmentStatus.ACKNOWLEDGED_BY_RESTAURANT:
            case ShipmentStatus.DELIVERY_ORDER_PLACED:
            case ShipmentStatus.FOOD_READY:
                return FoodMarketplaceFESupportedStatus.PREPARING
            case ShipmentStatus.RIDER_ALLOTED:
                return FoodMarketplaceFESupportedStatus.RIDER_ASSIGNED
            case ShipmentStatus.RIDER_ARRIVED:
                return FoodMarketplaceFESupportedStatus.RIDER_ARRIVED_AT_RESTAURANT
            case ShipmentStatus.DISPATCHED:
            case ShipmentStatus.RIDER_ARRIVED_CUSTOMER_DOORSTEP:
                return FoodMarketplaceFESupportedStatus.ON_ITS_WAY
            case ShipmentStatus.DELIVERED:
                return FoodMarketplaceFESupportedStatus.DELIVERED
            case ShipmentStatus.CANCELLED:
            case ShipmentStatus.CANCELLED_BY_ADMIN:
            case ShipmentStatus.CANCELLED_BY_RESTAURANT:
            case ShipmentStatus.RETURNED_TO_SELLER:
                return FoodMarketplaceFESupportedStatus.REJECTED
            default:
                return FoodMarketplaceFESupportedStatus.PLACED
        }
    }

    public static getVariantTitle(option: OrderProductOption): string {

        let title = ""
        const variants = option?.variants

        variants?.forEach( (v, index) => {
            title = title + (v.title ?? "")
            title = index === (variants?.length - 1) ? title + "" :  title + ", "
        })
        if (_.isEmpty(title)) {
            title = undefined
        }

        return title
    }

    public static getAddonTitle(option: OrderProductOption): string {

        let title = ""
        const addons = option?.addons

        addons?.forEach((a, index) => {
            title = title + (a.title ?? "")
            title = index === (addons?.length - 1) ? title + "" :  title + ", "
        })
        if (_.isEmpty(title)) {
            title = undefined
        }

        return title
    }

    public static getFoodMarketplaceOrderTrackingUrl(trackingUrl: string) {
        return `curefit://webview?uri=${trackingUrl}&processUrl=true&title=Track Your Order`
    }


    public static getTextCorrespondingToStatus(status: FoodMarketplaceFESupportedStatus): string {
        switch (status) {
            case "PLACED":  return "Order Placed"
            case "PREPARING": return "Order Confirmed"
            case "RIDER_ASSIGNED": return  "Delivery partner assigned"
            case "RIDER_ARRIVED_AT_RESTAURANT": return  "Delivery partner arrived"
            case "ON_ITS_WAY" : return  "Food Enroute"
            case "DELIVERED": return  "Order Delivered"
            case "REJECTED": return  "Order Cancelled"
        }
        return ""
    }

    public static getDummyMealCardImageUrl(index?: number): string {

        const num = index || Math.floor(Math.random() * 3)
        return this.DUMMY_IMAGE_URL_TEMPLATE.replace(this.DUMMY_IMAGE_URL_TEMPLATE_KEY, "" + num)

    }

    public static generateFoodMarketplaceOrderTrackingUrl(externalServiceFulfilmentId: string, orderId: string, listingBrand: string = "FOOD_MARKETPLACE"): string {
        return `curefit://cartdetails?marketplaceOrderId=` + externalServiceFulfilmentId + "&orderId=" + orderId + "&listingBrand=" + listingBrand
    }

    public static generateBrowserRedirectActionForOrderTracking(trackingUrl: string): Action {
        return {
            actionType: "EXTERNAL_DEEP_LINK",
            url: trackingUrl,
            title: "Track Order",
            meta: {
                icon: "LOCATION_PIN"
            },
        }
    }

    public static generateInAppOrderTrackingAction (trackUrl: string): Action {
        return {
            actionType: "NAVIGATION",
            title: "Track Order",
            url:  FoodMarketplaceUtil.getFoodMarketplaceOrderTrackingUrl(trackUrl)
        }
    }

    public static createDropEtaString(eta: number): string {
        return eta ? (`${eta} minute` + (eta === 1 ? "" : "s")) : undefined
    }

    public static getOutletListingScreenBannerWidgetId() {
        return AppUtil.isProdLike ? "8b978c73-fe9b-4007-ae53-df253e8cf6b8" : "d584da03-3591-43e7-865e-b0d1f5de4ad3"
    }

    public static getCallPartnerAction(phoneNumber: string): Action {
        const callCrewAction: Action = {
            title: "Call Partner",
            actionType: "CALL_NUMBER" as any,
            meta: {phoneNumber: phoneNumber}
        }
        return callCrewAction
    }

    public static getFoodMarketplaceSupportedCities(userContext: UserContext): string[] {
        const cityService = kernel.get<CityService>(LOCATION_TYPES.CityService)
        let result: string[] = []
        try {
            const cities = cityService.listCities(AppUtil.getTenantFromUserContext(userContext), AppUtil.getCountryId(userContext))
            result = cities.filter(city => city?.availableOfferings?.includes("FOOD_MARKETPLACE"))?.map(c => c.cityId)
        } catch (e) {
            result = []
        }
        return result
    }

    public static isFoodMarketplaceOrderCancelled(status: ShipmentStatus): boolean {
        return status === ShipmentStatus.CANCELLED || status === ShipmentStatus.CANCELLED_BY_ADMIN || status === ShipmentStatus.CANCELLED_BY_RESTAURANT
    }

    public static isFoodMarketplaceOrderDelivered(status: ShipmentStatus): boolean {
        return status === ShipmentStatus.DELIVERED
    }

    public static getFoodCardAction(product: FoodProduct, outletId: string, brandId: string, day: string, stock: number) {
        /*
         @param product: the price in this should be the final price (after offers consideration)
         */

        const hasVariants = product.variantGroups && product.variantGroups.length > 0
        const hasAddons = product.addonGroups && product.addonGroups.length > 0
        let action: MealAction
        if (hasVariants || hasAddons) {
            action = {
                price: product.price,
                image: undefined,
                actionType: "SHOW_FM_VARIANT_ADDON_MODAL",
                title: "ADD",
                url: "curefit://cartcheckout",
                date: day,
                customisable: true,
                meta: {
                    variantGroups: product?.variantGroups,
                    addonGroups: product?.addonGroups,
                    outletId: outletId,
                    brandId: brandId
                },
                mealSlot: {
                    name: "ALL",
                    id: "ALL"
                },
                productId: product.productId,
                productName: product.title,
                maxCartSize: MAX_CART_SIZE,
                stock: stock,
                listingBrand: "FOOD_MARKETPLACE"
            }
        } else {
            action = {
                price: product.price,
                image: undefined,
                actionType: "ADD_TO_FC_CART",
                title: "ADD",
                url: "curefit://cartcheckout",
                date: day,
                mealSlot: {
                    name: "ALL",
                    id: "ALL"
                },
                meta: {
                    outletId: outletId,
                    brandId: brandId
                },
                productId: product.productId,
                productName: product.title,
                maxCartSize: MAX_CART_SIZE,
                stock: stock,
                listingBrand: "FOOD_MARKETPLACE"
            }
        }
        return action
    }

    public static setAddonVariantAvailabilityInProduct(product: FoodProduct, sidesAvailabilityMap: {[id: string]: Boolean }): void {
        _.map(product?.variantGroups, v => this.setAvailabilityFlagInSides(v, sidesAvailabilityMap))
        _.map(product?.addonGroups, a => this.setAvailabilityFlagInSides(a, sidesAvailabilityMap))
    }

    public static setAvailabilityFlagInSides(group: FoodMarketPlaceVariantAddonGroup, sidesAvailabilityMap: {[id: string]: Boolean }): void {

        group?.variants?.forEach(v => v.inStock = !!sidesAvailabilityMap[v.id])
        group?.addons?.forEach(a => a.inStock = !!sidesAvailabilityMap[a.id])
    }

    public static isWithinLocationSelectionThreshold(userContext: UserContext): boolean {
        return (userContext?.sessionInfo?.sessionData?.locationPreferenceData?.lastUserUpdatedTimestamp ?? false) &&
            (((new Date().getTime() - userContext?.sessionInfo?.sessionData?.locationPreferenceData?.lastUserUpdatedTimestamp) / 60000) < this.RESTAURANT_SCREEN_ADDRESS_NUDGE_THRESHOLD_MIN)
    }
}


export enum FoodMarketplaceFESupportedStatus {
    PLACED = "PLACED",
    PREPARING = "PREPARING",
    RIDER_ASSIGNED = "RIDER_ASSIGNED",
    RIDER_ARRIVED_AT_RESTAURANT = "RIDER_ARRIVED_AT_RESTAURANT",
    ON_ITS_WAY = "ON_ITS_WAY",
    DELIVERED = "DELIVERED",
    REJECTED = "REJECTED" // FE understands rejected and not cancelled

}
