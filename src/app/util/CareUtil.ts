import { CareDoctorSearchPageParams } from "./../care/CareDoctorSearchPageView"
import {
    ClpCalloutWidget,
    DoctorDetailWidget,
    GradientCard,
    WidgetView,
    ProductGridWidget,
    DoctorListWidgetV2,
    getOffersWidget,
    SuggestionWidget,
    OfferCalloutWidget,
    OfferData,
    ProductSummaryWidgetV2,
    RoundedImageGridWidget,
    SlotTimerWidget,
    ProductFeedbackWidget,
    CareServiceFulfillmentWidget,
    MembershipContextWidget,
    CalloutWidget
} from "./../common/views/WidgetView"
import { IServiceInterfaces, MembershipProductType, getTherapyUserTypes, Orientation } from "@curefit/vm-models"
import { Action as EtherAction } from "@curefit/apps-common"
import {
    ActionV2,
    ActionWithContext,
    AppointmentActionsWithContext,
    BookingDetail,
    Center,
    CONSULTATION_STATUS,
    ConsultationInstructionResponse,
    ConsultationOrderResponse,
    DiagnosticAllowedLocations,
    DiagnosticEmailedTestReportItem,
    DiagnosticEmailedTestReportResponse,
    DiagnosticReportInfo,
    DiagnosticReportV2,
    DiagnosticSlot,
    DiagnosticsTestOrderResponse,
    DOCTOR_TYPE,
    FollwUpContext, IHealthfaceService,
    ManagedPlanPackInfo,
    ManagedPlanSellableProduct,
    MPChildProduct,
    Patient,
    PreferredCenterResponse,
    PreferredDoctorType,
    StepInfo,
    TCAvailableSlotsDetails,
    TestDetails,
    TestType,
    Doctor,
    AggregatedMembershipInfo,
    ConsultationSellableProduct,
    DoctorAvlImmediateResponse,
    BundleSessionSellableProduct,
    Consultation,
    CareTeam,
    UserMembershipInfo,
    AtHomeDiagnosticOrder,
    SampleCollectionLocationResponse,
    AgentAttributeFilter,
    PrescriptionInfo,
    LabTest,
    AgentFilterInfos,
    AgentFilterValue,
    DoctorListingFilterResponse,
    TwilioUser,
    ActiveConsultationResponse,
    ICovidMetricUserStateLevel,
    ICovidMetricsInfoResponse,
    ICovidMetricInfo,
    ICovidMetricItem,
    SGTWorkoutDetails,
    ConsultationSlotInfoMetadata,
    TCAvailableSlotsResponse,
    TCAvailableSlotsMetadata
} from "@curefit/albus-client"
import * as _ from "lodash"
import {
    Action,
    ActionCard,
    ActionType,
    CenterSelectionWidget,
    DescriptionWidget,
    Header,
    InfoCard,
    InstructionsWidget,
    ManageOptionPayload,
    NavigationCardWidget,
    PricingWidgetRecurringValue,
    ProductDetailPage,
    ProductListWidget,
    SelectCenterAction,
    UserSelectionWidget,
    BillingAddressWidget,
    StartDateWidget,
} from "../common/views/WidgetView"
import {
    ActionableCardItem,
    DateWiseSlots,
    DiagnosticsEmailTestReportSummaryWidget,
    DiagnosticsTestReportSummaryWidget,
    ReportStatusWidget,
    Status,
    StepStateCard,
    TestReportDetail,
    TimeSlotCategory,
    HorizontalActionableCardListingWidget, PreferedDoctor, AvailableProductTags
} from "../page/PageWidgets"
import {
    ConsultationProduct,
    DiagnosticProductResponse,
    SKIN_HAIR_SUBCATEGORIES,
    SUB_CATEGORY_CODE,
    HealthSegment,
    HealthfaceTenant,
    LIVE_PT_DOCTOR_TYPES,
    DoctorRecommendationAffinity,
    CONSULTATION_PACK_SUBCATEGORIES, AgentFilter, IDoctorBookingQueryParam, IDoctorBookingQueryParamWeb, DiagnosticTestProduct
} from "@curefit/care-common"
import { InstructionItem, ProductPrice, ProductType, } from "@curefit/product-common"
import { UserAgentType as UserAgent, UserAgentType } from "@curefit/base-common"
import HCUDetailsPageConfig from "../care/HCUDetailsPageConfig"
import {
    ActionUtil, ActionUtilV1,
    CareUtil as BaseCareUtil,
    NEW_SPECIALITY_BOOKING_PAGE_SUPPORTED,
    OfferUtil
} from "@curefit/base-utils"
import { DividerType, WidgetWithMetric } from "@curefit/vm-common"
import { SubscriptionState } from "aws-sdk/clients/shield"
import { RUPEE_SYMBOL } from "@curefit/finance-common"
import { PackOffersResponse, OfferV2 } from "@curefit/offer-common"
import { OrderProduct, MembershipPaymentType } from "@curefit/order-common"
import { TimeUtil, Timezone, titleCase, pluralizeStringIfRequired, CdnUtil } from "@curefit/util-common"
import { UserContext, UserProfile } from "@curefit/userinfo-common"
import TeleconsultationDetailsPageConfig from "../care/TeleconsultationDetailsPageConfig"
import { AccordionSectionItem } from "../common/views/ProfileWidgetView"
import { DATE_PICKER_HEADER_TYPE } from "../page/Page"
import {
    Action as AppAction,
    CareProductInfoWidget,
    Gender,
    PageTypes,
    ConsultationInfoWidget,
    ConsultationInfoItem,
    ProductBenefitWidget,
    ProductBenefit,
    NoteListWidget,
    NoteListItem,
    ProductRadioSelectionWidget,
    ProductRow,
    ConsultationProductItem,
    MediaAsset,
    PartnerInfoAddWidget,
    WhyTherapyFeedbackWidget,
    RouteNames,
    IBreadCrumb,
    ProductOffer,
    IFullFilledDetailsV2,
    ICollapsibleOfferWidget,
    ViewStyle,
    CheckoutInfoItem,
    CareCheckoutInfoItemWidget, WorkoutSelectionListWidget, ColoredCodedInfoItem
} from "@curefit/apps-common"
import { CallToAction, MRNFormResponse } from "@curefit/albus-client"
import { AnnouncementBusiness } from "../announcement/AnnouncementBusiness"
import { ICFSClient as IFormService } from "@curefit/cfs-client"
import {
    User
} from "@curefit/user-common"
import AppUtil, { SHOW_INSTRUCTION_MODAL_SUPPORTED_APP_VERSION, ZOOM_WEB_SDK, AppFont, PHLEBO_CALLING_ACTION_SUPPORTED } from "./AppUtil"
const urlParse = require("url-parse")
import { ImageCarouselAnnouncementView } from "../announcement/AnnouncementViewBuilder"
import { CacheHelper } from "./CacheHelper"
import { CareDoctorSearchPagePromises } from "../care/CareDoctorSearchPageView"
import { HamletBusiness } from "@curefit/hamlet-node-sdk"
import { ICatalogueService } from "@curefit/catalog-client"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { IOfferServiceV2 } from "@curefit/offer-service-client"
import { ProductPayByMembershipMap } from "@curefit/oms-api-client"
import { Feedback } from "@curefit/feedback-common"
import { CustomerIssueType } from "@curefit/issue-common"
import { DiagnosticsCart } from "@curefit/fuse-node-client/dist/src/FuseClientTypes"
import { Logger } from "@curefit/base"
import { UserAllocation } from "@curefit/hamlet-common"
import * as momentTz from "moment-timezone"
import { ClassByDateV2SGT, ClassByTimeV2SGT } from "../care/ConsultationDatePickerViewV1"
import deleteProperty = Reflect.deleteProperty
import { all } from "inversify-express-utils"

export const MIND_THERAPIST_RECOMMENDATION_MAX_LENGTH = 10

const CAREFIT_ENABLE_ANDROID: number = 6.91
const CAREFIT_ENABLE_IOS: number = 6.91
export const CareActionIdsByPriority = ["JOIN_CALL", "MESSAGE", "CANCEL", "RESCHEDULE", "REPORT_ISSUE"]
export const NORMAL_STATE_COLOR = "#24e795"
export const ABNORMAL_STATE_COLOR = "#f52eb5"
export const DIAGNOSCTIC_PAGE_CPVERSION_ANDROID = 72
export const DIAGNOSCTIC_PAGE_CPVERSION_IOS = 65
export const DIAGNOSCTIC_PAGE_CPVERSION_INTERNAL_ANDROID = 163
export const DIAGNOSCTIC_PAGE_CPVERSION_INTERNAL_IOS = 145
export const DIAGNOSCTIC_PAGE_VERSION_ANDROID = 6.91
export const DIAGNOSCTIC_PAGE_VERSION_IOS = 6.91

export const SPLIT_CONSULTATION_PAGE_VERSION = 6.95
// TODO change the version once release is done
export const NEW_CLP_VERSION = 6.97

export const CARE_RECO_CENTER_IDS = [3, 4, 5, 6, 10, 12, 41, 43]
export const CARE_RECO_MIND_CENTER_IDS = [33, 23, 14]
export const CARE_RECO_EAT_AREA_IDS = ["1", "3", "4"]
export const NON_CARE_CONSULTATION_DOCTOR_TYPES = ["MIND_THERAPIST", "COUPLE_THERAPIST", "COVID_THERAPIST", "SLEEP_THERAPIST", "PERSONAL_TRAINER", "PERSONAL_TRAINER_L2", "PERSONAL_TRAINER_L3", ...LIVE_PT_DOCTOR_TYPES]
export const MULTI_CENTER_SUPPORTED_DOCTOR_TYPES = ["MIND_THERAPIST", "COUPLE_THERAPIST", "COVID_THERAPIST", "SLEEP_THERAPIST", "PSYCHIATRIST", "PERSONAL_TRAINER", "PERSONAL_TRAINER_L2", "PERSONAL_TRAINER_L3"]
export const CENTER_CHANGE_NOT_RESTRICTED_DOCTOR_TYPES = ["MIND_THERAPIST", "COUPLE_THERAPIST", "COVID_THERAPIST", "SLEEP_THERAPIST", "PSYCHIATRIST", "PERSONAL_TRAINER", "PERSONAL_TRAINER_L2", "PERSONAL_TRAINER_L3", "LC"]
export const SF_FREEMIUM_FULL_BODY_TEST_LIST = [
    "HbA1c - Glycosylated Hemoglobin",
    "Random Blood Sugar",
    "Lipid Profile",
    "Liver Function Test (LFT)",
    "Kidney Function Test1 (KFT1)",
    "Urine Routine & Microscopy Extended",
    "Complete Haemogram",
    "Thyroid Stimulating Hormone (TSH)"
  ]
export const SUPPORTED_MY_CHAT = 7.30
export const TWILIO_CHAT_EXIPRED_SUPPORTED = 7.30
export const SUPPORTED_DIAGNOSTIC_INSTRUCTION = 7.30
export const MULTI_CENTER_SUPPORTED = 7.31
export const HCU_MP_NEW_DEEPLINK_VERSION = 7.40
export const CAROUSEL_MODAL_SUPPORTED = 7.44
export const CARE_STATE_ITEM_MPV2_SUPPORTED = 7.46
export const CARE_TEAM_V2_SUPPORTED = 7.46
export const EMAIL_TEST_REPORT_SUPPORTED = 7.48
export const CARE_NEW_CENTER_SELECTION_SUPPORTED = 7.48
export const CARE_HCU_FORWARD_FLOW_SUPPORTED = 8.12
export const DOCTOR_LIST_WIDGET_V2_SUPPORTED = 7.48
export const SUPPORTED_NEW_SCHEDULE_TEST_ACTION = 7.51
export const FOOTER_WIDGET_DATE_PICKER_SUPPORTED = 7.54
export const EMAIL_NEW_PLAN_SUPPORTED = 7.56
export const NEW_MIND_THERAPY_SPECIALITY_BOOKING_PAGE_SUPPORTED = 7.58
export const NEW_DIAG_PACK_INSTRUCTION_SUPPORTED = 7.62
export const NEW_CARE_3P_ANNOUNCEMENT = 7.68
export const CARE_FOOTER_INFO_WIDGET_SUPPORTED = 7.77
export const DOCTOR_UNAVAILABLE_UI_SUPPORTED = 7.80

export const NEW_EMAILED_REPORT_SUPPORTED = 7.67
export const CARE_DOCTOR_PAGE_SUPPORTED = 8.20
export const DOCTOR_DETAIL_WIDGET_V2_SUPPORTED = 8.18
export const NEW_SUMMARY_WIDGET_FOR_TC_SUPPORTED = 8.25
export const DOCTOR_UNAVAILABLE_TEXT = "Doctor is unavailable for your consultation. We request you to reschedule your appointment to a different slot. Apologize for the inconvenience."
export const DOCTOR_UNAVAILABLE_SHORTER_TEXT = "Doctor is unavailable for your consultation. Please reschedule."

export const LIVE_PT_SNC_PRODUCT_ID = "CONS_CULT_LIVE_PT_SnC"
export const LIVE_PT_YOGA_PRODUCT_ID = "CONS_CULT_LIVE_PT_YOGA"
export const LIVE_SGT_SNC_PRODUCT_ID = "CONS_CULT_LIVE_SGT_SnC"

export const GP99_PRODUCT_ID = "CONS_GP_STANDARD_ONLINE"

export const NEW_FILTER_VERSION_ANDROID = 8.30
export const NEW_FILTER_VERSION_IOS = 8.30
export const NEW_FILTER_CPVERSION_INTERNAL_ANDROID = 408
export const NEW_FILTER_CPVERSION_INTERNAL_IOS = 382
export const NEW_FILTER_CPVERSION_ANDROID = 171
export const NEW_FILTER_CPVERSION_IOS = 161

export const IS_VALIDATE_ADDRESS_SUPPORTED = 8.40
export const IS_VALIDATE_ADDRESS_SUPPORTED_ANDROID = 8.40
export const IS_VALIDATE_ADDRESS_SUPPORTED_IOS = 8.40
export const IS_VALIDATE_ADDRESS_SUPPORTED_CPVERSION_INTERNAL_ANDROID = 423
export const IS_VALIDATE_ADDRESS_SUPPORTED_CPVERSION_INTERNAL_IOS = 395
export const IS_VALIDATE_ADDRESS_SUPPORTED_CPVERSION_ANDROID = 178
export const IS_VALIDATE_ADDRESS_SUPPORTED_CPVERSION_IOS = 170

export const IS_CARE_DIAGNOSTIC_CART_SUPPORTED = 8.57
export const MESSAGE_CARD_WIDGET_IN_PRODUCT_PAGE_SUPPORTED = 8.30
export const IS_NEW_OFFER_ICON_SUPPORTED = 8.54

export const LAB_TEST_CYCLOP_SEGMENT = "ab855c06-0b89-42ac-a3fe-559157e70f11"

export enum CareTherapyQuestion {
    Rating = "rating",
    Feedback = "feedback",
    WhatWentWell = "tags",
    WhatCanBeImproved = "tags_q2",
}

export const ConsultationInfoEmptyDisabledAction: Action = {
    actionType: "NAVIGATION",
    disabled: true
}
export const ConsultationInfoSubTitleStyle = {
    color: "#8d93a0",
    fontSize: 12,
    fontFamily: AppFont.Regular,
}
export const ConsultationInfoTitleStyle = {
    color: "#1b1c1e",
    fontSize: 14,
    fontFamily: AppFont.Bold,
}

export const ConsultationInfoSingleItemWidgetContainerStyle = {
    paddingTop: 12,
    paddingBottom: 2,
    marginTop: 11,
    marginBottom: 11
}

interface StatusInfo {
    homeStatus: any,
    centerStatus: any,
    sampleCollectionStartDateAtHome: number,
    sampleCollectionStartDateAtCenter: number
}

interface TitleObj {
    title: string,
    completionTitle: string,
    startText: string,
    endText: string
}

export interface IProductSummaryParams {
    title: string
    description: string
    imageUrl: string
    productCode: string
}

export interface HomeCenterSelector {
    sampleCollectionLocationResponse: SampleCollectionLocationResponse,
    diagnosticTestInstruction: any
}

export interface Participant {
    userId: string,
    name: string,
    profileImage?: string,
    identity?: string,
    trackId?: string,
    type?: string
}

export interface TWLParticipant {
    identity: string,
    tracKId?: string
}

export interface TwilioParticipants {
    participant: TWLParticipant
}

export interface SGTParticipants {
    participant: Participant
}

export interface MessageProps {
    icon: string,
    text: string,
    action: {
        meta: {
            text: string,
            steps: string[],
            action: {
                title: string,
            }
        }
    }
}

export const messages = [
    {
        icon: "CANT_SEE",
        text: "CANNOT SEE",
        id: "CANNOT_SEE",
        action: {
            actionType: "SHOW_INSTRUCTION_MODAL",
            meta: {
                text:
                    "If you are not able to see or hear the trainer, try the following steps:",
                steps: [
                    "1. Check your device volume",
                    "2. Close other app, tabs on your device",
                    "3. You can exit the session and join back",
                ],
                action: {
                    actionType: "HIDE_INSTRUCTION_MODAL",
                    title: "OK, GOT IT",
                },
            },
        },
    },
    {
        icon: "CANT_HEAR",
        text: "CANNOT HEAR",
        id: "CANNOT_HEAR",
        action: {
            actionType: "SHOW_INSTRUCTION_MODAL",
            meta: {
                text:
                    "If you are not able to see or hear the trainer, try the following steps:",
                steps: [
                    "1. Check your device volume",
                    "2. Close other app, tabs on your device",
                    "3. You can exit the session and join back",
                ],
                action: {
                    actionType: "HIDE_INSTRUCTION_MODAL",
                    title: "OK, GOT IT",
                },
            },
        },
    }
]


const SCHEDULED_STATES = ["CONFIRMED", "SCHEDULED", "RESCHEDULED", "ONBOARDING_USERS"]
const STARTED_STATES = ["STARTED", "CONSULTATION_STARTED", "PRESCRIPTION_IN_DRAFT"]
const MISSED_STATES = ["MISSED", "CUSTOMER_MISSED", "AGENT_MISSED"]
const COMPLTETED_STATES = ["COMPLETED", "CONSULTATION_COMPLETED", "PRESCRIPTION_GENERATED"]
export const CUSTOM_SUPPORTED_DOCTOR_FILTER = ["CITY_FILTER", "LANGUAGE_FILTER"]

export interface SGTClass extends SGTWorkoutDetails, TCAvailableSlotsDetails {
    startTime: number,
    endTime: number,
    bookedByPatient?: boolean,
    action?: Action,
    buttonAction?: Action,
    format?: string[],
    parentBookingId?: number,
    productId?: string,
    date?: string,
    duration: number,
    icon?: string
    calorie?: string
    workoutTitle?: string
    sgtConsultationInventoryBookingDetails?: any

}

export interface ClassByTimeList {
    id: string,
    classes: SGTClass[],
}

export class CareUtil extends BaseCareUtil {

    public static isDoctorUnavailabilitySupported(userContext: UserContext): boolean {
        return userContext.sessionInfo.appVersion >= DOCTOR_UNAVAILABLE_UI_SUPPORTED
    }

    public static isOnlineConsultation(consultationItem: Consultation) {
        return consultationItem.booking.subCategoryCode === "CF_ONLINE_CONSULTATION"
    }

    public static getVideoChannel(bookingDetail: BookingDetail, logger?: Logger): string {
        const actionWithContext: ActionWithContext = bookingDetail.consultationOrderResponse.appointmentActionsWithContext.videoActionWithContext

        // logger && logger?.info(`Video Channel Action Context For BookingId: ${bookingDetail.booking.id}: ${JSON.stringify(actionWithContext)}`)

        if (!_.isEmpty(actionWithContext) && actionWithContext.action.actionPermitted) {
            return actionWithContext.context.twilioCommunicationMode.modeName
        }
    }

    public static isVideoLogEnabled(): boolean {
        return true
    }

    public static isCoupleTherapist(doctorType: string) {
        return doctorType === "COUPLE_THERAPIST"
    }

    public static isSugarfitPhleboConsult(id: string) {
        return id === "CGMINSTALL001"
    }

    public static getCareCenterObjFromArray(centers: Center[]) {
        // Creating a map object of center with center id as key
        return centers ? centers.reduce((obj: any, item: Center) => {
            obj[item.id] = item
            return obj
        }, {}) : {}
    }

    public static async getCareCenterObjFromArrayPromise(centerPromise: Promise<Center[]>) {
        const centers = await centerPromise
        return this.getCareCenterObjFromArray(centers)
    }

    public static async getCareProductInfoWidget(product: ConsultationProduct, pagePromises: CareDoctorSearchPagePromises, isPriceAvailableInListingApi: boolean): Promise<CareProductInfoWidget> {
        const isCoupleTherapy = CareUtil.isCoupleTherapist(product.doctorType)
        const moreIndex = isCoupleTherapy ? 400 : 100
        let subtitle = product.subTitle || ""
        if (isCoupleTherapy) {
            subtitle += `\nNote: Partners need to join the session from the same device`
        }
        let offerIds: any
        const offerDetails: {
            price: ProductPrice;
            offers: OfferV2[];
        } = await this.getOfferDetailsInDoctorListing(product, pagePromises, isPriceAvailableInListingApi)
        if (offerDetails) {
            const offers = offerDetails?.offers?.filter(offer => !_.isEmpty(offer) && !offer?.displayContexts?.includes("NONE") && offer.description)
            offerIds = _.map(offers, offer => { return offer.offerId })
        }
        return new CareProductInfoWidget(product.title, subtitle, product.heroImageUrl, !_.isEmpty(offerIds) ? { marginBottom: -70 } : { marginBottom: -50 }, subtitle.length > moreIndex, moreIndex, "See less", "Know more")
    }

    public static async getOfferDetailsInDoctorListing(product: ConsultationProduct, pagePromises: CareDoctorSearchPagePromises, isPriceAvailableInListingApi: boolean) {
        let offerDetails: {
            price: ProductPrice;
            offers: OfferV2[];
        }
        if (isPriceAvailableInListingApi || CareUtil.isTherapyOnlyDoctorType(product.doctorType)) {
            offerDetails = OfferUtil.getPackOfferAndPrice(product, <PackOffersResponse>await pagePromises.offerPromise)
        } else {
            const centers = await pagePromises.centersPromise
            if (!_.isEmpty(centers) && pagePromises.offerPromise) {
                const centerId = centers[0].id.toString()
                const centerOfferDetails = OfferUtil.getOfferAndPriceForCareCenter(product, [centerId], await pagePromises.offerPromise as { [key: string]: PackOffersResponse })
                offerDetails = centerOfferDetails[centerId]
            }
        }
        return offerDetails

    }

    public static getCoronaCareInstructionModal(userContext: UserContext, action: Action) {
        const userAgent = userContext.sessionInfo.userAgent
        if (CareUtil.isInstructionModalSupportedInApp(userContext) || userAgent === "MBROWSER" || userAgent === "DESKTOP") {
            const instructionAction: Action = {
                actionType: "SHOW_CARE_INSTRUCTION_MODAL",
                title: action.title,
                meta: {
                    header: {
                        title: "Important Advisory"
                    },
                    instructions: this.getCoronaAdvisoryInstructions(),
                    action: { ...action, title: "CONTINUE" },
                    showBookLater: true
                }
            }
            return instructionAction
        }
        return action
    }

    public static getCoronaAdvisoryInstructions() {
        return [
            {
                iconType: "INFORMATION",
                text: "Patients with international travel history (or) contact with any person exhibiting flu-like symptoms post travel are advised to go directly to"
            },
            {
                iconType: "REPORT",
                text: "Bangalore Medical College & Research Institute (or)"
            },
            {
                iconType: "REPORT",
                text: "NIV Field Unit - Rajiv Gandhi Institute of Chest Diseases"
            },
            {
                iconType: "INFORMATION",
                text: "Call 104 - helpline number for any assistance"
            }
        ]
    }

    public static getContentIdforWeb(type: string): { video: string, centre: string } {
        switch (type) {
            case "LC":
                return {
                    video: "Care_clp_lp_video_cal",
                    centre: "Care_clp_lp_incentre"
                }
            case "PHYSIOTHERAPIST":
                return {
                    video: "Care_physio_video_call",
                    centre: "Care_physio_incentre"
                }
            default:
                return {
                    video: "Care_clp_video_call",
                    centre: "Care_clp_incentre"
                }
        }

    }

    public static getChatChannel(bookingDetail: BookingDetail): string {
        if (!_.isEmpty(bookingDetail) && bookingDetail.consultationOrderResponse && bookingDetail.consultationOrderResponse.appointmentActionsWithContext) {
            return this.getChatChannelWithAppointmentContext(bookingDetail.consultationOrderResponse.appointmentActionsWithContext)
        }
    }

    public static getChatChannelWithAppointmentContext(appointmentActionsWithContext: AppointmentActionsWithContext): string {
        if (appointmentActionsWithContext) {
            const actionWithContext: ActionWithContext = appointmentActionsWithContext.chatActionWithContext
            if (!_.isEmpty(actionWithContext) && !_.isEmpty(actionWithContext.context) && !_.isEmpty(actionWithContext.context.twilioCommunicationMode) && actionWithContext.context.twilioCommunicationMode.modeName) {
                return actionWithContext.context.twilioCommunicationMode.modeName
            }
        }
    }

    public static getVideoEnabled(bookingDetail: BookingDetail): boolean {
        if (!_.isEmpty(bookingDetail) && bookingDetail.consultationOrderResponse && bookingDetail.consultationOrderResponse.appointmentActionsWithContext) {
            const actionWithContext: ActionWithContext = bookingDetail.consultationOrderResponse.appointmentActionsWithContext.videoActionWithContext
            if (!_.isEmpty(actionWithContext)) {
                return actionWithContext.action.actionPermitted
            }
        }
        return false
    }

    public static getAudioEnabled(bookingDetail: BookingDetail): boolean {
        if (!_.isEmpty(bookingDetail) && bookingDetail.consultationOrderResponse && bookingDetail.consultationOrderResponse.appointmentActionsWithContext) {
            const actionWithContext: ActionWithContext = bookingDetail.consultationOrderResponse.appointmentActionsWithContext.audioActionWithContext
            if (!_.isEmpty(actionWithContext)) {
                return actionWithContext.action.actionPermitted
            }
        }
        return false
    }

    public static getZoomLinkEnabled(bookingDetail: BookingDetail): boolean {
        if (!_.isEmpty(bookingDetail) && bookingDetail.consultationOrderResponse && bookingDetail.consultationOrderResponse.appointmentActionsWithContext) {
            const actionWithContext: ActionWithContext = bookingDetail.consultationOrderResponse.appointmentActionsWithContext.zoomLinkActionWithContext
            if (!_.isEmpty(actionWithContext)) {
                return actionWithContext.action.actionPermitted
            }
        }
        return false
    }

    public static getZoomLink(bookingDetail: BookingDetail) {
        if (!_.isEmpty(bookingDetail) && bookingDetail.consultationOrderResponse && bookingDetail.consultationOrderResponse.appointmentActionsWithContext) {
            const actionWithContext: ActionWithContext = bookingDetail.consultationOrderResponse.appointmentActionsWithContext.zoomLinkActionWithContext
            if (!_.isEmpty(actionWithContext) && !_.isEmpty(actionWithContext.context) && !_.isEmpty(actionWithContext.context.twilioCommunicationMode)) {
                return actionWithContext.context.twilioCommunicationMode.modeSid
            }
        }
    }

    public static getTwilioEnabled(appointmentActionsWithContext: AppointmentActionsWithContext): boolean {
        if (!_.isEmpty(appointmentActionsWithContext)) {
            const actionWithContext: ActionWithContext = appointmentActionsWithContext.videoActionWithContext
            if (!_.isEmpty(actionWithContext)) {
                return actionWithContext.action.actionPermitted
            }
        }
        return false
    }

    public static getChatEnabled(bookingDetail: BookingDetail): boolean {
        if (!_.isEmpty(bookingDetail) && bookingDetail.consultationOrderResponse && bookingDetail.consultationOrderResponse.appointmentActionsWithContext) {
            const actionWithContext: ActionWithContext = bookingDetail.consultationOrderResponse.appointmentActionsWithContext.chatActionWithContext
            if (!_.isEmpty(actionWithContext)) {
                return actionWithContext.action.actionPermitted
            }
        }
        return false
    }

    public static getChatValiditybookingDetail(bookingDetail: BookingDetail, timezone: Timezone): string {
        const time = _.get(bookingDetail, "consultationOrderResponse.appointmentActionsWithContext.chatActionWithContext.context.twilioCommunicationMode.expiry")
        const isActive = _.get(bookingDetail, "consultationOrderResponse.appointmentActionsWithContext.chatActionWithContext.action.actionPermitted", false)
        return time && isActive ? `Available till ${TimeUtil.formatEpochInTimeZone(timezone, time, "DD MMM")}` : `Chat window expired`
    }

    public static getUnreadMessageCount(bookingDetail: BookingDetail): number {
        const unreadMessageMap = _.get(bookingDetail, "consultationOrderResponse.appointmentActionsWithContext.chatActionWithContext.context.twilioUnreadMessageCountViewMap", null)
        if (!unreadMessageMap) {
            return null
        }
        return unreadMessageMap["PATIENT"]
    }

    public static getWhatToExpectForAnxietyTherapistBookingConfirmation(): ProductListWidget {
        const header: Header = {
            title: "What to expect",
            color: "#000000"
        }
        const cards: GradientCard[] = [
            {
                title: "Mental health first aid",
                subTitle: "Understand how stress impacts you and how to respond",
                shadowColor: "rgba(112, 231, 156, 0.86)",
                gradientColors: ["rgb(118, 233, 151)", "rgb(44, 194, 211)"],
                icon: "ANXIETY_HEAD"
            },
            {
                title: "Private and safe",
                subTitle: "Talk to your coach in a safe and private space",
                shadowColor: "rgb(252, 172, 140)",
                gradientColors: ["rgb(252, 172, 140)", "rgb(239, 109, 101)"],
                icon: "ANXIETY_PRIVATE"
            },
            {
                title: "Timely start",
                subTitle: "Wait less see your therapist on-time",
                shadowColor: "rgba(92, 176, 222, 0.75)",
                gradientColors: ["rgb(23, 216, 229)", "rgb(172, 154, 255)"],
                icon: "ANXIETY_TIME"
            },
            {
                title: "Get linked with recommended services/specialists",
                subTitle: "Your coach will suggest specific programs or referrals as needed",
                shadowColor: "rgba(94, 216, 248, 0.73)",
                gradientColors: ["rgb(137, 212, 228)", "rgb(42, 183, 222)"],
                icon: "ANXIETY_SOCIAL"
            }
        ]
        return { ...new ProductListWidget("GARDIENT_CARD", header, cards), dividerType: "LARGE" }
    }

    public static getMessageActionText(bookingDetail: BookingDetail): string {
        const unreadMessageMap: any = CareUtil.getUnreadMessageCount(bookingDetail)
        const msgdisplayText = (!_.isEmpty(unreadMessageMap) && unreadMessageMap.unreadMessageCount > 0) ? `Message (${unreadMessageMap.unreadMessageCount})` : `Message`
        return msgdisplayText
    }

    public static getChatMessageAction(userContext: UserContext, chatActionWithContext: any, patientId: number, docName: string, channel: string, doctorImage: string, doctorQualification: string, appointmentId?: number, bookingId?: number, pageFrom?: string, noIcon?: boolean): any {
        const action: Action = this.getChatMessageActionWithoutContext(userContext, patientId, docName, channel, doctorImage, doctorQualification, appointmentId, bookingId, pageFrom, noIcon)
        if (!_.isEmpty(action)) {
            const unreadMessageCount = _.get(chatActionWithContext, "context.twilioUnreadMessageCountViewMap.PATIENT", null)
            action.title = (!_.isEmpty(unreadMessageCount) && unreadMessageCount.unreadMessageCount > 0) ? `Message (${unreadMessageCount.unreadMessageCount})` : `Message`
            if (chatActionWithContext && chatActionWithContext.action && chatActionWithContext.action.actionPermitted) {
                return action
            } else {
                const sid = _.get(chatActionWithContext, "context.twilioCommunicationMode.modeSid", null)
                if (sid && userContext.sessionInfo.appVersion >= TWILIO_CHAT_EXIPRED_SUPPORTED) {
                    action.url = `${action.url}&chatExpired=true&sid=${sid}`
                    return action
                }
                return null
            }
        } else {
            return null
        }
    }

    public static getChatMessageActionWithoutContext(userContext: UserContext, patientId: number, docName: string, channel: string, docImage: string, qualification: string, appointmentId?: number, bookingId?: number, pageFrom?: string, noIcon?: boolean): Action {
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "APP")
        const url = ActionUtil.chatMessageActionUrl(patientId, channel, docName, docImage, qualification, appointmentId, bookingId, pageFrom)

        return {
            title: "Message",
            actionType: (userAgent === "DESKTOP" || userAgent === "MBROWSER") ? "OPEN_MESSAGE_DRAWER" : "NAVIGATION",
            icon: noIcon ? undefined : "MESSAGE",
            url
        }
    }

    public static isPending(bookingDetail: BookingDetail): boolean {
        return ["SCHEDULED", "STARTED", "DOCTOR_UNAVAILABLE", "PRESCRIPTION_IN_DRAFT", "ONBOARDING_USERS", "RESCHEDULED"].includes(bookingDetail?.consultationOrderResponse?.consultationUserState)
    }

    public static isComplteted(bookingDetail: BookingDetail): boolean {
        if (COMPLTETED_STATES.includes(bookingDetail?.consultationOrderResponse?.consultationUserState))
            return true
        if (!_.isEmpty(bookingDetail) && !_.isEmpty(bookingDetail.diagnosticsTestOrderResponse) && (bookingDetail.diagnosticsTestOrderResponse[0].status === "COMPLETED"))
            return true
        return false
    }

    public static isScheduledConsultation(bookingDetail: BookingDetail): boolean {
        return SCHEDULED_STATES.includes(bookingDetail?.consultationOrderResponse?.consultationUserState) ||
            SCHEDULED_STATES.includes(bookingDetail?.consultationOrderResponse?.status)
    }

    public static isMissed(bookingDetail: BookingDetail): boolean {
        return MISSED_STATES.includes(bookingDetail?.consultationOrderResponse?.consultationUserState) ||
            MISSED_STATES.includes(bookingDetail?.consultationOrderResponse?.status)
    }

    public static isConsultationStarted(bookingDetail: BookingDetail): boolean {
        return STARTED_STATES.includes(bookingDetail?.consultationOrderResponse?.consultationUserState) ||
            STARTED_STATES.includes(bookingDetail?.consultationOrderResponse?.status)
    }

    public static isCancelledConsultation(bookingDetail: BookingDetail): boolean {
        return "CANCELLED" === bookingDetail?.consultationOrderResponse?.consultationUserState
    }

    public static isCustomerMissed(bookingDetail: BookingDetail): boolean {
        return ["CUSTOMER_MISSED"].includes(bookingDetail?.consultationOrderResponse?.status)
    }

    public static isDoctorUnavailable(bookingDetail: BookingDetail): boolean {
        return CareUtil.isDoctorNonAvailable(bookingDetail?.consultationOrderResponse?.consultationUserState) ||
            CareUtil.isDoctorNonAvailable(bookingDetail?.consultationOrderResponse?.status)
    }

    public static isDoctorNonAvailable(status: CONSULTATION_STATUS) {
        return status === "DOCTOR_UNAVAILABLE"
    }

    public static mergeMeta(manageOptions: ManageOptionPayload[]) {
        const meta = {}
        for (let i = 0; i < manageOptions.length; i++) {
            if (manageOptions[i].type !== "REPORT_ISSUE")
                _.merge(meta, manageOptions[i].meta)
        }
        return meta
    }

    public static getRescheduleEnabled(bookingDetail: BookingDetail): boolean {
        if (!_.isEmpty(bookingDetail)) {
            const actionWithContext: ActionWithContext = bookingDetail.consultationOrderResponse?.appointmentActionsWithContext?.rescheduleActionWithContext
            if (!_.isEmpty(actionWithContext)) {
                return actionWithContext.action.actionPermitted
            }
        }
        return false
    }

    /**
     *
     * @param {segmentIds} SegmentIds retrived from albus
     * @returns {true} if user has active mind therapy pack
     */
    public static userHasMindTherapyPack(segmentIds?: HealthSegment[]): boolean {
        if (_.isUndefined(segmentIds)) {
            return false
        }
        return segmentIds.length > 0 && segmentIds.includes("SEGMENT_MIND")
    }


    public static userHasBookedMindSession(segmentIds?: HealthSegment[]): boolean {
        if (_.isUndefined(segmentIds)) {
            return false
        }
        return getTherapyUserTypes(segmentIds).includes("THERAPY_SESSION_NOT_DONE")
    }


    public static isPartOfMP(bookingDetail: BookingDetail): boolean {
        if (!_.isEmpty(bookingDetail) && !_.isEmpty(bookingDetail.consultationOrderResponse)) {
            return bookingDetail.consultationOrderResponse.isPartOfMultiConsultation
        }
        return false
    }

    public static getCancelEnabled(bookingDetail: BookingDetail): boolean {
        if (!_.isEmpty(bookingDetail)) {
            const actionWithContext: ActionWithContext = bookingDetail.consultationOrderResponse?.appointmentActionsWithContext?.cancelActionWithContext
            if (!_.isEmpty(actionWithContext)) {
                return actionWithContext.action.actionPermitted
            }
        }
        return false
    }

    public static isCareSupported(osName: string, appVersion: number, cityId: string): boolean {
        return cityId === "Bangalore" && (!_.isEmpty(osName) && (osName.toLowerCase() === "ios" && appVersion >= CAREFIT_ENABLE_IOS) ||
            (osName.toLowerCase() === "android" && appVersion >= CAREFIT_ENABLE_ANDROID))
    }

    public static checkMPOnboardingNotDone(bundleBookingInfo: BookingDetail): boolean {
        return bundleBookingInfo.stepInfosV2.find(stepInfo => stepInfo.stepInfoType === "CONSULTATION").stepState !== "PLAN_GENERATED"
    }

    public static isCarePresentInCity(cityId: string): boolean {
        return cityId === "Bangalore"
    }

    public static getMPSubscriptioDisplayText(userContext: UserContext, expiryTimeEpoch: number, state: SubscriptionState): string {
        const tz = userContext.userProfile.timezone
        switch (state) {
            case "SUBSCRIPTION_ACTIVE":
                return `Active till ${TimeUtil.formatEpochInTimeZone(tz, expiryTimeEpoch, "D MMM YYYY")}`
            case "SUBSCRIPTION_ACTIVE_WITH_AUTO_RENEW":
                return `Renews on ${TimeUtil.formatEpochInTimeZone(tz, expiryTimeEpoch, "D MMM YYYY")}`
            case "EXPIRED":
                return `Expired on ${TimeUtil.formatEpochInTimeZone(tz, expiryTimeEpoch, "D MMM YYYY")}`
        }
    }

    public static getPreferredDoctorType(preferredDoctorType: PreferredDoctorType, doctorCode: DOCTOR_TYPE): string {
        if (preferredDoctorType === "PERSONAL_DOCTOR" && LIVE_PT_DOCTOR_TYPES.indexOf(doctorCode) !== -1) {
            return "Personal Trainer"
        }
        switch (preferredDoctorType) {
            case "PERSONAL_DOCTOR":
                switch (doctorCode) {
                    case "GP":
                    case "GYNAECOLOGIST":
                        return "Personal Doctor"
                    case "SPL":
                        return "Personal Specialist"
                    case "LC":
                        return "Personal Health Coach"
                    case "MIND_THERAPIST":
                        return "Personal Therapist"
                    case "COVID_THERAPIST":
                        return "Personal Covid Therapist"
                    case "SLEEP_THERAPIST":
                        return "Personal Sleep Therapist"
                    case "COUPLE_THERAPIST":
                        return "Personal Couple Therapist"
                    case "PSYCHIATRIST":
                        return "Personal Psychiatrist"
                    case "PHYSIOTHERAPIST":
                        return "Personal Physiotherapist"
                    case "PERSONAL_TRAINER":
                    case "PERSONAL_TRAINER_L2":
                    case "PERSONAL_TRAINER_L3":
                        return "Personal Trainer"
                    case "ORTHOPEDIC":
                        return "Personal Orthopedician"
                    case "INTERNAL_MEDICINE":
                        return "Personal IM Specialist"
                    case "ENT":
                        return "Personal ENT"
                    case "ENDOCRINOLOGIST":
                        return "Personal Endocrinologist"
                    default:
                        return "Personal Doctor"
                }
            case "PERSONAL_HEALTH_COACH":
                return "Personal Health Coach"
            case "FOLLOWUP_DOCTOR":
            case "FOLLOWUP_HEALTH_COACH":
                return "Follow up"
            case "RESCHEDULED_HEALTH_COACH":
            case "RESCHEDULED_DOCTOR":
                return "Reschedule"
        }
    }

    public static getTestReportDetailsView(reportInfo: TestDetails | DiagnosticReportInfo, sendZero: boolean = false): TestReportDetail[] {
        const testInfos: TestReportDetail[] = []
        const normalCount = reportInfo.normalCount
        const abnormalCount = reportInfo.abnormalCount
        const totalTestCount = reportInfo.testCount
        const normalCountRatio = normalCount / totalTestCount
        const abnormalCountRatio = abnormalCount / totalTestCount
        if (normalCount > 0 || sendZero) {
            testInfos.push({
                stateColor: NORMAL_STATE_COLOR,
                testCount: `${normalCount}`, // `${appendZeroInSingleDigitNumber(normalCount)}`,
                testState: "Normal",
                lineWidthRatio: normalCountRatio
            })
        }
        if (abnormalCount > 0 || sendZero) {
            testInfos.push({
                stateColor: ABNORMAL_STATE_COLOR,
                testCount: `${abnormalCount}`, // `${appendZeroInSingleDigitNumber(abnormalCount)}`,
                testState: "Out of range",
                lineWidthRatio: abnormalCountRatio
            })
        }
        return testInfos
    }

    public static isTestReportIsNull(reportInfo: DiagnosticReportInfo) {
        return reportInfo && reportInfo.normalCount === 0 && reportInfo.testCount === 0
    }

    public static getTestReportNullAction(userContext: UserContext, diagnosticOrderResponse: DiagnosticsTestOrderResponse, reportInfo: DiagnosticReportInfo): NavigationCardWidget {
        if (userContext.sessionInfo.appVersion >= EMAIL_TEST_REPORT_SUPPORTED && CareUtil.isTestReportIsNull(reportInfo)) {
            return new NavigationCardWidget(
                "Report Generated",
                "Email Now",
                CareUtil.getDiagnosticsEmailTestReportAction(diagnosticOrderResponse && diagnosticOrderResponse.orderId, "Email Report"),
                false, undefined, userContext.sessionInfo.userAgent
            )
        } else {
            return null
        }
    }

    public static getDiagnosticCancelEnabled(bookingDetail: BookingDetail) {
        if (!_.isEmpty(bookingDetail.allowedActions) && bookingDetail.allowedActions.indexOf("CANCEL") != -1) {
            return true
        }
    }

    public static isDiagnosticPageSupported(osName: string, appVersion: number, codepushVersion: number, isInternalUser: boolean) {
        if (isInternalUser) {
            if (osName.toLowerCase() === "android") {
                return appVersion > DIAGNOSCTIC_PAGE_VERSION_ANDROID || codepushVersion >= DIAGNOSCTIC_PAGE_CPVERSION_INTERNAL_ANDROID
            } else {
                return appVersion > DIAGNOSCTIC_PAGE_VERSION_IOS || codepushVersion >= DIAGNOSCTIC_PAGE_CPVERSION_INTERNAL_IOS
            }
        } else {
            if (osName.toLowerCase() === "android") {
                return appVersion > DIAGNOSCTIC_PAGE_VERSION_ANDROID || codepushVersion >= DIAGNOSCTIC_PAGE_CPVERSION_ANDROID
            } else {
                return appVersion > DIAGNOSCTIC_PAGE_VERSION_IOS || codepushVersion >= DIAGNOSCTIC_PAGE_CPVERSION_IOS
            }
        }
    }

    public static isNewFilterSupported(osName: string, appVersion: number, codepushVersion: number, isInternalUser: boolean) {
        if (isInternalUser) {
            if (osName.toLowerCase() === "android") {
                return appVersion > NEW_FILTER_VERSION_ANDROID || (appVersion >= NEW_FILTER_VERSION_ANDROID && codepushVersion >= NEW_FILTER_CPVERSION_INTERNAL_ANDROID)
            } else {
                return appVersion > NEW_FILTER_VERSION_IOS || (appVersion >= NEW_FILTER_VERSION_IOS && codepushVersion >= NEW_FILTER_CPVERSION_INTERNAL_IOS)
            }
        } else {
            if (osName.toLowerCase() === "android") {
                return appVersion > NEW_FILTER_VERSION_ANDROID || (appVersion >= NEW_FILTER_VERSION_ANDROID && codepushVersion >= NEW_FILTER_CPVERSION_ANDROID)
            } else {
                return appVersion > NEW_FILTER_VERSION_IOS || (appVersion >= NEW_FILTER_VERSION_IOS && codepushVersion >= NEW_FILTER_CPVERSION_IOS)
            }
        }
    }

    public static isValidateAdressSupported(osName: string, appVersion: number, codepushVersion: number, isInternalUser: boolean, isWeb: boolean) {
        if (isWeb) {
            return true
        } else {
            return appVersion >= IS_VALIDATE_ADDRESS_SUPPORTED
        }
    }
    public static isCareDiagnosticCartSupported(userContext: UserContext) {
        if (userContext.sessionInfo.userAgent === "APP" && userContext.sessionInfo.appVersion >= IS_CARE_DIAGNOSTIC_CART_SUPPORTED) {
            return true
        }
        return false
    }

    public static showUpdateForPurchase(osName: string, appVersion: number, codepushVersion: number, isInternalUser: boolean, userAgent: UserAgent) {
        if (userAgent !== "APP") {
            return false
        }
        if (isInternalUser) {
            if (osName.toLowerCase() === "android") {
                return appVersion < IS_VALIDATE_ADDRESS_SUPPORTED_ANDROID || (appVersion === IS_VALIDATE_ADDRESS_SUPPORTED_ANDROID && codepushVersion < IS_VALIDATE_ADDRESS_SUPPORTED_CPVERSION_INTERNAL_ANDROID)
            } else {
                return appVersion < IS_VALIDATE_ADDRESS_SUPPORTED_IOS
            }
        } else {
            if (osName.toLowerCase() === "android") {
                return appVersion < IS_VALIDATE_ADDRESS_SUPPORTED_ANDROID || (appVersion === IS_VALIDATE_ADDRESS_SUPPORTED_ANDROID && codepushVersion < IS_VALIDATE_ADDRESS_SUPPORTED_CPVERSION_ANDROID)
            } else {
                return appVersion < IS_VALIDATE_ADDRESS_SUPPORTED_IOS
            }
        }
    }

    public static isSplitConsultationSupported(osName: string, appVersion: number, codepushVersion: number, isInternalUser: boolean) {
        return appVersion >= SPLIT_CONSULTATION_PAGE_VERSION
    }

    public static isDiagnosticPack(subCategoryCode: string) {
        return subCategoryCode === "DIAGNOSTICS" // To bo Changed for cart flow
    }

    public static getBookTestActions(userContext: UserContext, bookingInfo: BookingDetail, isHomeCollectionEnabled: boolean): Action[] {
        const productId = bookingInfo.bundleOrderResponse.productCode
        const productCodeCsv = [bookingInfo.bundleOrderResponse.hcuTestDetails.baseTestCode, ...bookingInfo.bundleOrderResponse.hcuTestDetails.addonTestCodes]
        return this.testBookingModalAction(userContext, bookingInfo.booking.patientId, productId, bookingInfo.booking.id, productCodeCsv, undefined, bookingInfo.booking.subCategoryCode)
    }

    private static getDiagnosticTestInstruction(userContext: UserContext, homeCenterNotAllowed?: boolean, subCategoryCode?: SUB_CATEGORY_CODE) {
        const isNewInstructionSupported = subCategoryCode && (userContext.sessionInfo.appVersion >= NEW_DIAG_PACK_INSTRUCTION_SUPPORTED || userContext.sessionInfo.userAgent !== "APP")
        const oldInstructions = {
            homeCenterInstruction: homeCenterNotAllowed
                ? [{ title: "Get samples collected from convenience of your home", icon: "HOME" }]
                : [
                    { title: "Fasting not required for in-center tests", icon: "EAT" },
                    { title: "Urine & Blood samples collected from home", icon: "SAMPLE" },
                    { title: "More slots available for in-center tests", icon: "SLOTS" }
                ],
            centerInstruction: [
                { title: "Fasting required for in-center tests", icon: "DONT_EAT" },
                { title: "Morning slots only", icon: "SLOT" }
            ]
        }
        if (!isNewInstructionSupported) {
            return oldInstructions
        }
        switch (subCategoryCode) {
            case "DIAG_PACK":
            case "DIAG_PACK_OT":
                return {
                    homeCenterInstruction: [{
                        title: "Get samples collected from convenience of your home",
                        icon: "HOME"
                    }],
                    centerInstruction: [{ title: "Experience our state of the art medical centres", icon: "CENTER" }],
                }
            case "HCU_PACK":
            case "HCU_PACK_OT":
                return {
                    homeCenterInstruction: homeCenterNotAllowed
                        ? [{ title: "Get samples collected from convenience of your home", icon: "HOME" }]
                        : [{ title: "Blood & Urine samples collected from home", icon: "SAMPLE" },
                        { title: "Remaining tests at centre", icon: "CENTER" }
                        ],
                    centerInstruction: [{ title: "Complete all tests at centre", icon: "CENTER" }]
                }
            default:
                return oldInstructions
        }
    }

    public static testBookingModalActionV2(userContext: UserContext, patientId: number, productId: string, parentBookingId: number, productCodeCsv: string[], subCategoryCode?: SUB_CATEGORY_CODE, homeCenterSelector?: HomeCenterSelector): Action[] {
        const diagnosticsTestAllowedLocation = homeCenterSelector.sampleCollectionLocationResponse?.sampleCollectionLocations
        const homeCenterNotAllowed = !_.isEmpty(diagnosticsTestAllowedLocation) && diagnosticsTestAllowedLocation.indexOf("HOME_PLUS_CENTRE") === -1
        const { homeCenterInstruction, centerInstruction } = this.getDiagnosticTestInstruction(userContext, homeCenterNotAllowed, subCategoryCode)
        if (_.isEmpty(diagnosticsTestAllowedLocation)) {
            return [{ ...CareUtil.showErrorToast(homeCenterSelector.sampleCollectionLocationResponse?.errorMessage), title: "Schedule test", icon: "SCHEDULE_TEST" }]
        } else if (diagnosticsTestAllowedLocation && diagnosticsTestAllowedLocation.length === 1) {
            const isAllAtHome = homeCenterNotAllowed && diagnosticsTestAllowedLocation[0] === "ALL_AT_HOME"
            if (homeCenterSelector.diagnosticTestInstruction) {
                return [{
                    actionType: "SHOW_CARE_INSTRUCTION_MODAL",
                    title: "Schedule test",
                    icon: "SCHEDULE_TEST",
                    meta: {
                        header: {
                            title: "Instructions"
                        },
                        instructions: homeCenterSelector.diagnosticTestInstruction,
                        action: {
                            actionType: isAllAtHome ? "SELECT_ADDRESS_AND_NAVIGATE" : "NAVIGATION",
                            url: `curefit://selectCareDateV1?patientId=${patientId}&productId=${productCodeCsv[0]}&parentBookingId=${parentBookingId}&type=DIAGNOSTICS&category=${isAllAtHome ? "AT_HOME_SLOT" : "IN_CENTRE_SLOT"}&nextAction=checkout&productCodes=${productCodeCsv.join(",")}${isAllAtHome ? "&title=Select your home address" : ""}`,
                            title: "CONTINUE"
                        },
                        showBookLater: true
                    }
                }]

            } else {
                return [{
                    title: "BOOk",
                    actionType: isAllAtHome ? "SELECT_ADDRESS_AND_NAVIGATE" : "NAVIGATION",
                    url: `curefit://selectCareDateV1?patientId=${patientId}&productId=${productCodeCsv[0]}&parentBookingId=${parentBookingId}&type=DIAGNOSTICS&category=${isAllAtHome ? "AT_HOME_SLOT" : "IN_CENTRE_SLOT"}&nextAction=checkout&productCodes=${productCodeCsv.join(",")}${isAllAtHome ? "&title=Select your home address" : ""}`
                }]
            }
        } else {
            const newCards = [
                {
                    type: "Center",
                    title: "All at Centre",
                    text: "Limited slots available only in morning\nFasting required before centre Visit",
                    instructionMeta: {
                        type: "DIAGNOSTICS",
                        testType: "IN_CENTRE",
                        productCodeCsv
                    },
                    eventId: "HCU_All_Center",
                    listItems: centerInstruction,
                    action: {
                        actionType: "NAVIGATION",
                        url: `curefit://selectCareDateV1?patientId=${patientId}&productId=${productCodeCsv[0]}&parentBookingId=${parentBookingId}&type=DIAGNOSTICS&category=IN_CENTRE_SLOT&nextAction=checkout&productCodes=${productCodeCsv.join(",")}`
                    },
                    url: `curefit://selectCareDateV1?patientId=${patientId}&productId=${productCodeCsv[0]}&parentBookingId=${parentBookingId}&type=DIAGNOSTICS&category=IN_CENTRE_SLOT&nextAction=checkout&productCodes=${productCodeCsv.join(",")}`
                },
                {
                    type: "HomeCenter",
                    title: homeCenterNotAllowed ? "All at Home" : "Home + Centre",
                    disabled: false,
                    comingsoonTag: false,
                    // tagText: "RECOMMENDED",
                    instructionMeta: {
                        type: "DIAGNOSTICS",
                        testType: homeCenterNotAllowed ? "AT_HOME" : "BOTH",
                        productCodeCsv
                    },
                    eventId: homeCenterNotAllowed ? "HCU_All_Home" : "HCU_Home_Center",
                    text:
                        "Morning/Evening slots at center\nFasting sample collected at home",
                    listItems: homeCenterInstruction,
                    action: {
                        actionType: "SELECT_ADDRESS_AND_NAVIGATE",
                        url: `curefit://selectCareDateV1?patientId=${patientId}&productId=${productCodeCsv[0]}&parentBookingId=${parentBookingId
                            }&type=DIAGNOSTICS&category=AT_HOME_SLOT&nextAction=${homeCenterNotAllowed ? "checkout" : "incentreSlot"}&productCodes=${productCodeCsv.join(",")}&title=Select your home address`
                    },
                    url: `curefit://selectCareDateV1?patientId=${patientId}&productId=${productCodeCsv[0]}&parentBookingId=${parentBookingId
                        }&type=DIAGNOSTICS&category=AT_HOME_SLOT&nextAction=${homeCenterNotAllowed ? "checkout" : "incentreSlot"}&productCodes=${productCodeCsv.join(",")}&title=Select your home address`
                }
            ]
            return [
                {
                    actionType: "SCHEDULE_TEST",
                    title: "Schedule test",
                    icon: "SCHEDULE_TEST",
                    meta: {
                        productId: productId,
                        parentBookingId: parentBookingId,
                        cards: userContext.sessionInfo.appVersion >= SUPPORTED_NEW_SCHEDULE_TEST_ACTION ? newCards : {
                            HomeCenter: newCards.find(item => item.type === "HomeCenter"),
                            Center: newCards.find(item => item.type === "Center")
                        }
                    }
                }
            ]
        }
    }

    public static testBookingModalAction(userContext: UserContext, patientId: number, productId: string, parentBookingId: number, productCodeCsv: string[], diagnosticAllowedLocations?: DiagnosticAllowedLocations[], subCategoryCode?: SUB_CATEGORY_CODE): Action[] {
        const homeCenterNotAllowed = !_.isEmpty(diagnosticAllowedLocations) && diagnosticAllowedLocations.indexOf("HOME_PLUS_CENTRE") === -1
        const { homeCenterInstruction, centerInstruction } = this.getDiagnosticTestInstruction(userContext, homeCenterNotAllowed, subCategoryCode)
        const newCards = [
            {
                type: "Center",
                title: "All at Centre",
                text: "Limited slots available only in morning\nFasting required before centre Visit",
                instructionMeta: {
                    type: "DIAGNOSTICS",
                    testType: "IN_CENTRE",
                    productCodeCsv
                },
                eventId: "HCU_All_Center",
                listItems: centerInstruction,
                url: `curefit://selectCareDateV1?patientId=${patientId}&productId=${productCodeCsv[0]}&parentBookingId=${parentBookingId}&type=DIAGNOSTICS&category=IN_CENTRE_SLOT&nextAction=checkout&productCodes=${productCodeCsv.join(",")}`
            },
            {
                type: "HomeCenter",
                title: homeCenterNotAllowed ? "All at Home" : "Home + Centre",
                disabled: false,
                comingsoonTag: false,
                // tagText: "RECOMMENDED",
                instructionMeta: {
                    type: "DIAGNOSTICS",
                    testType: homeCenterNotAllowed ? "AT_HOME" : "BOTH",
                    productCodeCsv
                },
                eventId: homeCenterNotAllowed ? "HCU_All_Home" : "HCU_Home_Center",
                text:
                    "Morning/Evening slots at center\nFasting sample collected at home",
                listItems: homeCenterInstruction,
                url: `curefit://selectCareDateV1?patientId=${patientId}&productId=${productCodeCsv[0]}&parentBookingId=${parentBookingId
                    }&type=DIAGNOSTICS&category=AT_HOME_SLOT&nextAction=${homeCenterNotAllowed ? "checkout" : "incentreSlot"}&productCodes=${productCodeCsv.join(",")}&title=Select your home address`
            }
        ]
        return [
            {
                actionType: "SCHEDULE_TEST",
                title: "Schedule test",
                icon: "SCHEDULE_TEST",
                meta: {
                    productId: productId,
                    parentBookingId: parentBookingId,
                    cards: userContext.sessionInfo.appVersion >= SUPPORTED_NEW_SCHEDULE_TEST_ACTION ? newCards : {
                        HomeCenter: newCards.find(item => item.type === "HomeCenter"),
                        Center: newCards.find(item => item.type === "Center")
                    }
                }
            }
        ]
    }

    public static getDoctorTypeAsString(doctorType: DOCTOR_TYPE): string {
        switch (doctorType) {
            case "GP":
                return "Doctor"
            case "AI_LC":
            case "LC":
                return "Health Coach"
            default:
                return "Specialist"
        }
    }

    public static getBookTestActionsFromActionContext(userContext: UserContext, patientId: number, action: ActionV2, subCategoryCode: SUB_CATEGORY_CODE, homeCenterSelector?: HomeCenterSelector): Action[] {
        const productId = action.actionContext.parentProductCode
        const productCodeCsv = action.actionContext.testProductCodes
        const diagnosticAllowedLocations: DiagnosticAllowedLocations[] = action.actionContext.diagnosticAllowedLocations
        return this.testBookingModalActionV2(userContext, patientId, productId, action.actionContext.parentBookingId, productCodeCsv, subCategoryCode, homeCenterSelector)
    }

    public static getHealthAssessmentActions(bookingInfo: BookingDetail, title: string): Action[] {
        const indexOfAssessmentStep = bookingInfo.bundleSetupInfo.bundleStepInfos.findIndex((stepInfo => {
            return stepInfo.setupStep === "HEALTH_ASSESSMENT"
        }))
        const patientAssessment = bookingInfo.bundleSetupInfo.bundleStepInfos[indexOfAssessmentStep].patientAssessment
        if (_.isNil(patientAssessment) || _.isEmpty(patientAssessment)) {
            return []
        }
        return [
            {
                meta: {
                    nextAction: {
                        meta: {
                            assessmentId: patientAssessment.id,
                            assessmentStatus: "COMPLETED"
                        },
                        actionType: "UPDATE_PATIENT_ASSESSMENT"
                    }
                },
                title: title,
                url: ActionUtil.webview(patientAssessment.assessmentUrl),
                icon: "FILL_FORM",
                actionType: "OPEN_ASSESSMENT_FORM"
            }
        ]
    }

    public static getHealthAssessmentActionsFromActionContext(action: ActionV2, title: string): Action[] {
        const actionContext = action.actionContext
        return [
            {
                meta: {
                    nextAction: {
                        meta: {
                            assessmentId: actionContext.assessmentClientAssessmentId,
                            assessmentStatus: "COMPLETED"
                        },
                        actionType: "UPDATE_PATIENT_ASSESSMENT"
                    }
                },
                title: title,
                url: ActionUtil.webview(actionContext.assessmentUrl),
                icon: "FILL_FORM",
                actionType: "OPEN_ASSESSMENT_FORM"
            }
        ]
    }

    public static getViewPrescriptionActions(bookingInfo: BookingDetail): Action[] {
        const indexOfConsultationStep = bookingInfo.bundleSetupInfo.bundleStepInfos.findIndex((stepInfo => {
            return stepInfo.setupStep === "CONSULTATION"
        }))
        const bookingId = bookingInfo.bundleSetupInfo.bundleStepInfos[indexOfConsultationStep].consultationBookingInfos[0].booking.id
        return [
            {
                actionType: "NAVIGATION",
                url: `curefit://carefitPrescription?tcBookingId=${bookingId}`,
                icon: "PRESCRIPTION",
                title: "View prescription"
            }
        ]
    }

    public static getBookConsultationActionUrl(bookingId: string, patientId: number, meta: any): string {
        // TODO handle for two product codes
        const actionUrl = `curefit://selectCareDateV1?productId=${meta[0].productCodes[0]}&parentBookingId=${bookingId}&patientId=${patientId}`
        return actionUrl
    }

    public static getTestListingActionUrl(patientId: number, parentBookingId: number, category?: string, testCodes?: string, selectedDiagnosticTests?: string): string {
        const actionUrl = `curefit://testsListing${ActionUtil.serializeAsQueryParams({ patientId, parentBookingId, category, testCodes, selectedDiagnosticTests })}`
        return actionUrl
    }

    public static followUpActionRequired(consultationUserState: CONSULTATION_STATUS, followUpContext: FollwUpContext) {
        return COMPLTETED_STATES.includes(consultationUserState) && followUpContext && followUpContext.enabled
    }

    public static getFollowupAction(userContext: UserContext, parentBookingId: number, products: ConsultationProduct[], followUpConsultationId: number, patientId: number, doctorId: number, centerId: number, title?: string): Action {
        const cards: any[] = []
        const userAgent = userContext.sessionInfo.userAgent
        const cityId = userContext.userProfile.cityId
        products.map(product => {
            const queryParams = {
                productId: product.productId,
                followUpConsultationId,
                patientId,
                parentBookingId,
                doctorId,
                centerId
            }
            const actionUrl = `curefit://selectCareDateV1${ActionUtil.serializeAsQueryParams(queryParams)}`
            if (product.consultationMode === "ONLINE") {
                const action = {
                    title: "Video call",
                    icon: "VIDEO",
                    action: {
                        actionType: userAgent === "DESKTOP" || userAgent === "MBROWSER" ? "SELECT_CARE_DATE" : "NAVIGATION",
                        url: actionUrl
                    }
                }
                if (this.isInstructionModalSupported(product) && this.isInstructionModalSupportedInApp(userContext)) {
                    cards.push({
                        title: "Video call",
                        icon: "VIDEO",
                        action: {
                            actionType: "SHOW_CARE_INSTRUCTION_MODAL",
                            meta: {
                                header: {
                                    title: "Instructions"
                                },
                                instructions: this.getInstructionsForAppointments(),
                                action: { ...action.action, title: "CONTINUE" },
                                showBookLater: true
                            }
                        }
                    })
                } else {
                    cards.push(action)
                }
            } else if (product.consultationMode === "INCENTRE" && cityId === "Bangalore") {
                cards.push({
                    title: "At Centre",
                    icon: "CENTRE",
                    action: {
                        actionType: userAgent === "DESKTOP" || userAgent === "MBROWSER" ? "SELECT_CARE_DATE" : "NAVIGATION",
                        url: actionUrl
                    }
                })
            }
        })
        if (cards.length === 1) {
            let extraFields = null
            if (cards[0].action.actionType === "SHOW_CARE_INSTRUCTION_MODAL") {
                extraFields = {
                    meta: {
                        header: {
                            title: "Instructions"
                        },
                        instructions: this.getInstructionsForAppointments(),
                        action: { ...cards[0].action.meta.action, title: "CONTINUE" },
                        showBookLater: true
                    }
                }
            } else {
                extraFields = { url: cards[0].action.url }
            }
            return {
                title: title ? title : "Follow up",
                icon: "FOLLOW_UP",
                actionType: cards[0].action.actionType,
                ...extraFields
            }
        } else {
            return {
                title: title ? title : "Follow up",
                actionType: "SHOW_CARE_ACTION_SHEET",
                icon: "FOLLOW_UP",
                meta: {
                    title: "Schedule your follow up",
                    cards
                }
            }
        }

    }

    public static getTherapistSelectionModalAction(productCodesMap: Map<DOCTOR_TYPE, string>, offerIds: string[], patientsList: Patient[], actionTitle?: string, parentBookingId?: number, pageFrom?: string): EtherAction {
        return {
            actionType: "SHOW_THERAPIST_SELECTION_MODAL",
            title: actionTitle,
            meta: {
                pageFrom: pageFrom,
                title: "Select an Expert",
                cards: [
                    {
                        title: "Therapist",
                        subText: "50 mins",
                        description: "Aids in identifying and solving personal problems",
                        gradientColors: [
                            "#46e499",
                            "#51d3d9"
                        ],
                        shadowColor: "#77ebdf",
                        action: this.getTherapyConsultationAction(productCodesMap.get("MIND_THERAPIST"), patientsList, parentBookingId)
                    },
                    {
                        title: "Psychiatrist",
                        subText: "25 mins",
                        description: "Prescribes medicines for mental health issues",
                        gradientColors: [
                            "#4195f6",
                            "#5cc9db"
                        ],
                        shadowColor: "#78cfff",
                        action: this.getTherapyConsultationAction(productCodesMap.get("PSYCHIATRIST"), patientsList, parentBookingId)
                    }
                ]
            }
        }
    }

    static getTherapyMemberShipState(membership: BookingDetail, tz: Timezone) {
        const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDateWithTimezone(tz), tz)
        const endDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.bundleOrderResponse.expiryTimeEpoch)), tz)
        const startDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.bundleOrderResponse.startTimeEpoch)), tz)
        const numDaysToEndFromToday = endDate.diff(today, "days")
        if (startDate > today) {
            return "UPCOMING"
        }
        if (endDate < today) {
            return "EXPIRED"
        }
        if (numDaysToEndFromToday < 10) {
            return "EXPIRING"
        }
        return "ACTIVE"
    }

    static getOtherRelation() {
        return [
            "Other"
        ]
    }

    public static async getOrCreatePatient(userContext: UserContext, userCache: CacheHelper, healthfaceService: IHealthfaceService, tenant?: HealthfaceTenant): Promise<Patient> {
        const patients: Patient[] = userContext?.userProfile?.carePatientPromise ? await userContext?.userProfile?.carePatientPromise : await healthfaceService.getAllPatients(userContext.userProfile.userId, tenant)
        let selfPatient: Patient = patients.find(patient => patient.relationship === "Self")
        if (!_.isEmpty(selfPatient)) {
            return selfPatient
        }

        const userInfo = await userCache.getUser(userContext.userProfile.userId)

        selfPatient = {
            name: `${userInfo.firstName ? userInfo.firstName.trim() : ""} ${userInfo.lastName ? userInfo.lastName.trim() : ""}`.trim(),
            gender: _.isEmpty(userInfo.gender) ? undefined : (userInfo.gender === Gender.FEMALE ? "Female" : "Male"),
            emailId: userInfo.email,
            phoneNumber: userInfo.phone,
            relationship: "Self",
            dateOfBirth: userInfo.birthday,
            curefitUserId: userContext.userProfile.userId,
            displayImage: userInfo.profilePictureUrl,
            firstName: userInfo.firstName ? userInfo.firstName.trim() : undefined,
            lastName: userInfo.lastName ? userInfo.lastName.trim() : undefined,
        }
        return await healthfaceService.createPatient(selfPatient, userContext.userProfile.userId, tenant)
    }

    public static async createOrUpdatePatient(userContext: UserContext, userCache: CacheHelper, healthfaceService: IHealthfaceService, gender?: "Female" | "Male", tenant?: HealthfaceTenant): Promise<Patient> {
        const patientsList = await healthfaceService.getAllPatients(userContext.userProfile.userId, tenant)
        let selfPatient = !_.isEmpty(patientsList) ? _.find(patientsList, patient => patient.relationship === "Self") : undefined

        if (_.isEmpty(selfPatient) || _.isEmpty(selfPatient.gender)) {
            if (_.isEmpty(selfPatient)) {
                const userInfo = await userCache.getUser(userContext.userProfile.userId)
                const selfPatientRequest: Patient = {
                    name: `${userInfo.firstName ? userInfo.firstName.trim() : ""} ${userInfo.lastName ? userInfo.lastName.trim() : ""}`.trim(),
                    gender: gender,
                    emailId: userInfo.email,
                    phoneNumber: userInfo.phone,
                    relationship: "Self",
                    dateOfBirth: userInfo.birthday,
                    curefitUserId: userContext.userProfile.userId,
                    displayImage: userInfo.profilePictureUrl,
                    firstName: userInfo.firstName ? userInfo.firstName.trim() : undefined,
                    lastName: userInfo.lastName ? userInfo.lastName.trim() : undefined,
                }
                selfPatient = await healthfaceService.createPatient(selfPatientRequest, userContext.userProfile.userId, tenant)
            } else if (gender) {
                selfPatient.gender = gender
                selfPatient = await healthfaceService.updatePatient(selfPatient, userContext.userProfile.userId, tenant)
            }
        }
        return selfPatient
    }


    public static getPatientSelectionModalAction(patientsList: Patient[], nextAction: Action, title?: string, formUserType?: string, calloutText?: string, isMalePreselected?: boolean): Action {
        if (!_.isEmpty(patientsList)) {
            const isSelfPatientPresent = patientsList.find(patient => patient.relationship === "Self")
            const defaultRelationShip = isSelfPatientPresent ? { patientRelation: "Other" } : {}
            return {
                actionType: "SHOW_PATIENT_SELECTION",
                title: title ? title : "Book your appointment",
                calloutText: calloutText,
                meta: {
                    // url: nextAction.url,
                    action: nextAction,
                    patientsList: patientsList,
                    relations: isSelfPatientPresent ? CareUtil.getOtherRelation() : CareUtil.getRelations(),
                    emergencyContactRelations: CareUtil.getEmergencyRelations(),
                    guardianRelations: CareUtil.getGuardianRelations(),
                    reqParams: {
                        formUserType: formUserType ? formUserType : "CARE_USER",
                        ...defaultRelationShip, isMalePreselected,
                    },
                }
            }
        } else {
            return {
                actionType: "ADD_PATIENT",
                title: title ? title : "Book your appointment",
                meta: {
                    // url: nextAction.url,
                    action: nextAction,
                    relations: CareUtil.getRelations(),
                    emergencyContactRelations: CareUtil.getEmergencyRelations(),
                    guardianRelations: CareUtil.getGuardianRelations(),
                    reqParams: {
                        formUserType: formUserType ? formUserType : "CARE_USER"
                    }
                }
            }
        }
    }

    public static isNewClpSupported(appVersion: number) {
        return appVersion >= NEW_CLP_VERSION
    }

    public static getTestInstructions(instructions1: InstructionItem[], instructions2?: InstructionItem[]): InstructionItem[] {
        if (!_.isEmpty(instructions1) && !_.isEmpty(instructions2)) {
            return _.uniqBy(_.flatten([instructions1, instructions2]), "code")
        } else if (_.isEmpty(instructions1)) {
            return instructions2
        } else if (_.isEmpty(instructions2)) {
            return instructions1
        }
    }

    public static getConsulationInstructions(bookingDetail: BookingDetail, pageConfig: TeleconsultationDetailsPageConfig): InstructionItem[] {
        const online = bookingDetail.booking.subCategoryCode === "CF_ONLINE_CONSULTATION"
        const doctorType = bookingDetail.consultationOrderResponse.consultationProduct.doctorType
        return this.getInstructionsBasedOnDoctorType(doctorType, online, pageConfig)
    }

    public static getInstructionsBasedOnDoctorType(doctorType: string, online: boolean, pageConfig: TeleconsultationDetailsPageConfig): InstructionItem[] {
        if (CareUtil.isPTDoctorType(doctorType)) {
            return pageConfig.ptSessionInstructions
        } else if (CareUtil.isMindDoctorType(doctorType) || CareUtil.isPsychiatry(doctorType)) {
            return online ? pageConfig.therapyOnlineInstructions : pageConfig.therapyOfflineInstructions
        }
        return online ? pageConfig.onlineInstructions : pageConfig.offlineInstructions
    }

    public static isNotDoctorConsulation(consultation: ConsultationOrderResponse): boolean {
        return NON_CARE_CONSULTATION_DOCTOR_TYPES.indexOf(consultation.consultationProduct.doctorType) >= 0
    }

    public static isNotCareProduct(product: ConsultationProduct): boolean {
        return NON_CARE_CONSULTATION_DOCTOR_TYPES.indexOf(product.doctorType) >= 0
    }


    public static isTherapyConsultation(consultation: ConsultationOrderResponse): boolean {
        const validMindDoctorTypes: Array<DOCTOR_TYPE> = ["PSYCHIATRIST"]
        return validMindDoctorTypes.includes(consultation.consultationProduct.doctorType) || this.isTherapyOnlyDoctorType(consultation?.consultationProduct?.doctorType)
    }

    public static isTherapyOnlyConsultation(consultation: ConsultationOrderResponse): boolean {
        return this.isTherapyOnlyDoctorType(consultation.consultationProduct.doctorType)
    }

    public static isFollowUpConsultationAllowed(consultation: ConsultationOrderResponse): boolean {
        if (!consultation.consultationProduct || !consultation.consultationProduct.doctorType) {
            return true
        }
        const doctorType = consultation.consultationProduct.doctorType
        return this.isMindDoctorType(doctorType) || this.isPTDoctorType(doctorType)
                || this.isLivePTDoctorType(doctorType) || this.isLiveSGTDoctorType(doctorType)
                || doctorType === "LC" || doctorType === "PSYCHIATRIST"
    }

    public static isTherapyOnlyDoctorType(doctorType: string): boolean {
        return this.isMindDoctorType(doctorType)
    }

    public static isSupportGroupProduct(product: ConsultationProduct): boolean {
        return this.isSupportGroupDoctorType(product.doctorType)
    }

    public static isTherapyProduct(product: ConsultationProduct): boolean {
        return this.isTherapyOnlyDoctorType(product.doctorType)
    }

    public static getCareConsultationProductVertical(product: ConsultationProduct): string {
        return this.isTherapyProduct(product) ? "MINDFIT" : "CAREFIT"
    }

    public static isPTSessionConsultation(consultation: ConsultationOrderResponse): boolean {
        return CareUtil.isPTDoctorType(consultation.consultationProduct.doctorType)
    }

    public static isLivePTSessionConsultation(consultation: ConsultationOrderResponse): boolean {
        return CareUtil.isLivePTDoctorType(consultation.consultationProduct.doctorType)
    }

    public static isTransformSessionConsultation(consultation: ConsultationOrderResponse): boolean {
        return CareUtil.isTransformDoctorType(consultation.consultationProduct.doctorType)
    }

    public static isLiveSGTSessionConsultation(consultation: ConsultationOrderResponse): boolean {
        return CareUtil.isLiveSGTDoctorType(consultation.consultationProduct.doctorType)
    }

    public static isCultPTProduct(consultationProduct: ConsultationProduct): boolean {
        return consultationProduct.tenant === "CULTFIT" && CareUtil.isPTDoctorType(consultationProduct.doctorType)
    }

    public static isLiveSGTProduct(consultationProduct: ConsultationProduct): boolean {
        return consultationProduct.tenant === "CULTFIT" && CareUtil.isLiveSGTDoctorType(consultationProduct.doctorType)
    }

    public static isLivePTProduct(consultationProduct: ConsultationProduct): boolean {
        return consultationProduct.tenant === "CULTFIT" && CareUtil.isLivePTDoctorType(consultationProduct.doctorType)
    }

    public static isPhysiotherapyProduct(consultationProduct: ConsultationProduct): boolean {
        return consultationProduct.tenant === "CARE" && consultationProduct.doctorType === "PHYSIOTHERAPIST"
    }

    public static isSkinProduct(consultationProduct: ConsultationProduct): boolean {
        return consultationProduct.tenant === "CARE" && (consultationProduct.doctorType === "DERMATOLOGIST" || consultationProduct.doctorType === "TRICHOLOGIST")
    }

    public static isLHRProduct(clubCode: string): boolean {
        return clubCode === "LHR_FULL_BODY_PACKS_SELECT_BODY_PARTS"
    }

    public static isLCProduct(consultationProduct: ConsultationProduct): boolean {
        return consultationProduct.tenant === "CARE" && consultationProduct.doctorType === "LC"
    }

    public static getTherapySessionTncAction(): AppAction {
        return {
            actionType: "NAVIGATION",
            url: "curefit://webview?uri=http%3A%2F%2Fstatic.cure.fit%2Fterms.html"
        }
    }

    public static getTherapySessionPolicyAction(): AppAction {
        return {
            actionType: "NAVIGATION",
            url: "curefit://webview?uri=http%3A%2F%2Fstatic.cure.fit%2Fprivacy.html"
        }
    }

    public static getBookinConsultationAction(userContext: UserContext, parentBookingId: number, patientId: number, products: DiagnosticProductResponse[], alwaysShowSelection: boolean = false, doctorId?: string, doctorCenterId?: Center["id"]): Action {
        if (products.length === 1 && !alwaysShowSelection) {
            return {
                actionType: "NAVIGATION",
                title: "BOOK CONSULTATION",
                icon: "DATE",
                url: `curefit://selectCareDateV1?productId=${products[0].productCode}&patientId=${patientId}&parentBookingId=${parentBookingId}${doctorId ? "&doctorId=" + doctorId : ""}${doctorCenterId ? `&centerId=${doctorCenterId}` : ""}`
            }
        }
        return {
            title: "BOOK CONSULTATION",
            icon: "DATE",
            actionType: "SHOW_CARE_ACTION_SHEET",
            meta: {
                title: "Schedule your consultation",
                cards: products.map(product => {
                    const action = {
                        actionType: "NAVIGATION",
                        url: `curefit://selectCareDateV1?productId=${product.productCode}&patientId=${patientId}&parentBookingId=${parentBookingId}${doctorId ? "&doctorId=" + doctorId : ""}${doctorCenterId ? `&centerId=${doctorCenterId}` : ""}`
                    }
                    if (product.consultationProduct.consultationMode === "ONLINE") {
                        if (this.isInstructionModalSupported(product.consultationProduct) && this.isInstructionModalSupportedInApp(userContext)) {
                            return {
                                title: "Video call",
                                actionType: "SHOW_CARE_INSTRUCTION_MODAL",
                                icon: "VIDEO",
                                meta: {
                                    header: {
                                        title: "Instructions"
                                    },
                                    instructions: this.getInstructionsForAppointments(),
                                    action: {
                                        ...action,
                                        title: "CONTINUE"
                                    },
                                    showBookLater: true
                                }
                            }
                        }
                        return {
                            title: "Video call",
                            icon: "VIDEO",
                            action
                        }
                    } else if (product.consultationProduct.consultationMode === "INCENTRE") {
                        return {
                            title: "At Centre",
                            icon: "CENTRE",
                            action
                        }
                    }
                })
            }
        }
    }

    public static getBookinConsultationActionV2(userContext: UserContext, parentBookingId: number, patientId: number, products: DiagnosticProductResponse[]): Action[] {
        if (_.isEmpty(products)) {
            return undefined
        }
        if (products.length === 1) {
            return [{
                actionType: "NAVIGATION",
                title: products[0].consultationProduct.consultationMode === "ONLINE" ? "VIDEO CALL" : "VISIT CENTER",
                icon: "DATE",
                url: `curefit://selectCareDateV1?productId=${products[0].productCode}&patientId=${patientId}&parentBookingId=${parentBookingId}`
            }]
        }
        return products.map((product): Action => {
            const action: Action = {
                actionType: "NAVIGATION",
                url: `curefit://selectCareDateV1?productId=${product.productCode}&patientId=${patientId}&parentBookingId=${parentBookingId}`
            }
            if (product.consultationProduct.consultationMode === "ONLINE") {
                if (this.isInstructionModalSupported(product.consultationProduct) && this.isInstructionModalSupportedInApp(userContext)) {
                    return {
                        title: "QUICK CALL",
                        actionType: "SHOW_CARE_INSTRUCTION_MODAL",
                        icon: "VIDEO",
                        meta: {
                            header: {
                                title: "Instructions"
                            },
                            instructions: this.getInstructionsForAppointments(),
                            action: {
                                ...action,
                                title: "CONTINUE"
                            },
                            showBookLater: true
                        }
                    }
                }
                return {
                    title: "QUICK CALL",
                    ...action
                }
            } else if (product.consultationProduct.consultationMode === "INCENTRE") {
                return {
                    title: "VISIT CENTER",
                    ...action
                }
            }
        })
    }

    public static getRenewMPSubscriptionActionMeta(userContext: UserContext, subsBooking: BookingDetail, renewAction: ActionV2, offers: PackOffersResponse,
        subscriptionStartDate: number, subCategoryCode?: string): any {
        const duration = subsBooking.bundleOrderResponse.sellableProduct.duration / 30
        const currentMonthlePrice = Math.ceil(subsBooking.bundleOrderResponse.sellableProduct.listingPrice / duration)
        return {
            header: {
                title: "Renew your plan"
            },
            footerText: `Current Plan | ${RUPEE_SYMBOL}${currentMonthlePrice} per month`,
            sections: [{
                title: subCategoryCode && subCategoryCode === "MP_V2" ? "CHOOSE A LONGER DURATION PLAN" : "CHOOSE A SUBSCRIPTION PLAN",
                type: "RECURRING",
                value: this.getRecurringSectionAndAction(userContext, subsBooking, renewAction.actionContext.subscriptionProducts, offers, subscriptionStartDate, subCategoryCode)
            }]
        }
    }

    private static getRecurringSectionAndAction(userContext: UserContext, subsBooking: BookingDetail, subscriptionProducts: MPChildProduct[], offers: PackOffersResponse,
        subscriptionStartDate: number, subCategoryCode?: string): PricingWidgetRecurringValue[] {
        const tz = userContext.userProfile.timezone
        const recurringSection: PricingWidgetRecurringValue[] = []
        const isMPV2 = subCategoryCode && subCategoryCode === "MP_V2"
        subscriptionProducts.map(subscriptionMainProduct => {
            const subscriptionProduct = subscriptionMainProduct.baseSellableProduct
            const isTrialPack = _.get(subscriptionProduct, "infoSection.trial", false)
            const offerDetail = OfferUtil.getPackOfferAndPriceForCare(subscriptionProduct, offers)
            const subscriptionProductListingPrice = offerDetail.price.listingPrice
            const subscriptionCode = subsBooking.booking.productCode
            const perMonthPrice = Math.ceil(subscriptionProductListingPrice / (subscriptionProduct.duration / 30))
            const duration = subscriptionProduct.duration / 30
            const startDate = TimeUtil.formatEpochInTimeZone(tz, subscriptionStartDate)
            const endDate = TimeUtil.addDays(tz, startDate, subscriptionProduct.duration)
            const orderProducts: OrderProduct[] = [{
                productId: subscriptionProduct.productCode,
                productType: "BUNDLE",
                quantity: 1,
                option: {
                    offerV2Ids: !_.isEmpty(offerDetail.offers) ? offerDetail.offers.map(offer => offer.offerId) : [],
                    patientId: subsBooking.booking.patientId,
                    parentBookingId: subsBooking.booking.rootBookingId,
                    startDate,
                    endDate,
                    categoryCode: subscriptionProduct.categoryCode,
                    subCategoryCode: subscriptionProduct.subCategoryCode,
                    groupCode: subscriptionMainProduct.groupCode,
                    childProductType: subscriptionMainProduct.childProductType,
                    subscriptionType: subscriptionProduct.infoSection.subscriptionType,
                    autorenewalEnabled: true
                }
            }]
            if (isMPV2) {
                delete orderProducts[0].option.subscriptionType
                delete orderProducts[0].option.autorenewalEnabled
            }
            const finalPrice = {
                listingPrice: subscriptionProductListingPrice,
                mrp: subscriptionProduct.mrp,
                currency: offerDetail.price.currency
            }
            if (!isTrialPack) {
                recurringSection.push({
                    title: subscriptionProduct.infoSection.shortname,
                    price: finalPrice,
                    priceMeta: perMonthPrice === subscriptionProductListingPrice ? `Per Month` : `${RUPEE_SYMBOL} ${perMonthPrice}/Month`,
                    action: {
                        actionType: "RENEW_MP_SUBSCRIPTION",
                        meta: {
                            subscriptionCode: subscriptionProduct.productCode,
                            bottomCTA: this.getRenewCTA(finalPrice, subscriptionProductListingPrice, duration, orderProducts, isMPV2)
                        }
                    },
                    selected: subscriptionCode === subscriptionProduct.productCode
                })
            }
        })
        const getSelected = recurringSection.find(item => item.selected)
        if (_.isEmpty(getSelected) && recurringSection.length > 0) {
            recurringSection[0].selected = true
        }
        return recurringSection
    }

    private static getRenewCTA(finalPrice: ProductPrice, recurringPrice: number, duration: number, orderProducts: OrderProduct[], disableSubText: boolean): Action {
        const subText = disableSubText ? undefined : duration === 1 ? `${RUPEE_SYMBOL} ${recurringPrice} charged monthly starting next month` : `${RUPEE_SYMBOL} ${recurringPrice} charged every ${duration} months`
        return {
            actionType: "RENEW_MP_SUBSCRIPTION",
            meta: {
                subText: subText,
                title: "Total Pay",
                price: finalPrice,
                showCheckoutAction: true,
                orderProducts: orderProducts
            }
        }
    }

    public static addCareMappedActions(appVersion: number, cityId: string, isInternalUser: boolean) {
        const actions = [{
            title: "Consultations",
            icon: "RIGHT_TICK",
            action: {
                type: "NAVIGATE",
                route: "consultations"
            }
        }]
        if (appVersion >= SUPPORTED_MY_CHAT) {
            actions.push({
                title: "My Chat",
                icon: "RIGHT_TICK",
                action: {
                    type: "NAVIGATE",
                    route: "chathistory"
                }
            })
        }

        const hcuPacksRoute = (appVersion < HCU_MP_NEW_DEEPLINK_VERSION) ? "HCUPacks" : "hcupacks"
        // const mpPacksRoute = (appVersion < HCU_MP_NEW_DEEPLINK_VERSION) ? "MPPacks" : "mppacks"

        switch (cityId) {
            case "Bangalore":
                actions.push(
                    {
                        title: "Full Body Health Check-Ups",
                        icon: "RIGHT_TICK",
                        action: {
                            type: "NAVIGATE",
                            route: hcuPacksRoute
                        }
                    },
                    {
                        title: "Diagnostic tests",
                        icon: "RIGHT_TICK",
                        action: {
                            type: "NAVIGATE",
                            route: "diagnostictests"
                        }
                    }
                )
                return actions
            default:
                return actions
        }
    }

    public static addCareMappedActionsV2(cityId: string, appVersion: number, isLabTestSegmentSupported?: boolean): AccordionSectionItem[] {
        const actions: AccordionSectionItem[] = [{
            title: "Consultations",
            action: {
                actionType: "NAVIGATION",
                url: "curefit://consultations?title=Consultations&consultationType=CONSULTATION"
            },
            icon: "NAVIGATION"
        }, {
            title: "Procedure",
            action: {
                actionType: "NAVIGATION",
                url: "curefit://consultations?title=Procedures&consultationType=PROCEDURE"
            },
            icon: "NAVIGATION"
        }]

        if (appVersion >= SUPPORTED_MY_CHAT) {
            actions.push({
                title: "My Chat",
                icon: "NAVIGATION",
                action: {
                    actionType: "NAVIGATION",
                    url: "curefit://chathistory"
                }
            })
        }

        switch (cityId) {
            case "Other":
                return actions
            default: {
                if (isLabTestSegmentSupported) {
                    actions.push(
                        {
                            title: "Lab Tests",
                            action: {
                                actionType: "NAVIGATION",
                                url: "curefit://hcupacks",
                                meta: {
                                    title: "Lab Tests"
                                }
                            },
                            icon: "NAVIGATION"
                        }
                    )
                }
                return actions
            }
        }
    }

    public static getDiagnosticTestWidget(userContext: UserContext, diagnosticTest: DiagnosticsTestOrderResponse): ActionableCardItem {
        let title = diagnosticTest.firstProductName
        if (diagnosticTest.productCodes.length > 1) {
            title += ` | +${diagnosticTest.productCodes.length - 1} TESTS`
        }
        const actions: Action[] = this.getDiagosticTestActions(userContext, diagnosticTest)
        const footers = []
        let type
        if (!_.isEmpty(diagnosticTest.atHomeDiagnosticOrder) && !_.isEmpty(diagnosticTest.inCentreDiagnosticOrder)) {
            type = "HOME+CENTRE"
            footers.push(this.getAtHomeFooterDetails(userContext, diagnosticTest))
            footers.push(this.getInCentreFooterDetails(userContext, diagnosticTest))
        } else if (!_.isEmpty(diagnosticTest.atHomeDiagnosticOrder)) {
            type = "ATHOME"
            footers.push(this.getAtHomeFooterDetails(userContext, diagnosticTest))
        } else if (!_.isEmpty(diagnosticTest.inCentreDiagnosticOrder)) {
            type = "INCENTRE"
            footers.push(this.getInCentreFooterDetails(userContext, diagnosticTest))
        }
        return {
            title: title,
            subTitle: `For ${diagnosticTest.patient.name}`,
            icon: type,
            footer: footers,
            actions: actions,
            cardAction: {
                actionType: "NAVIGATION",
                url: ActionUtil.diagnostics("DIAGNOSTIC_TEST", diagnosticTest.bookingId.toString())
            }
        }
    }

    public static getTimestampAndTimezone(userContext: UserContext, diagnosticTest: DiagnosticsTestOrderResponse): { timestamp: number, timezone: string } {
        let timestamp: number
        const timezone = userContext.userProfile.timezone
        if (!_.isEmpty(diagnosticTest.atHomeDiagnosticOrder) && !_.isEmpty(diagnosticTest.inCentreDiagnosticOrder)) {
            if (diagnosticTest.atHomeDiagnosticOrder.startTime > diagnosticTest.inCentreDiagnosticOrder.slot.workingStartTime) {
                timestamp = diagnosticTest.atHomeDiagnosticOrder.startTime
            } else {
                timestamp = diagnosticTest.inCentreDiagnosticOrder.slot.workingStartTime
            }
        } else if (!_.isEmpty(diagnosticTest.atHomeDiagnosticOrder)) {
            timestamp = diagnosticTest.atHomeDiagnosticOrder.startTime
        } else if (!_.isEmpty(diagnosticTest.inCentreDiagnosticOrder)) {
            timestamp = diagnosticTest.inCentreDiagnosticOrder.slot.workingStartTime
        }
        return {timestamp, timezone}
    }

    private static getAtHomeFooterDetails(userContext: UserContext, diagnosticTest: DiagnosticsTestOrderResponse): { text: string, icon: string, status: Status } {
        const status = this.getTestStatus(diagnosticTest.atHomeStepInfo, diagnosticTest.atHomeDiagnosticOrder.startTime, diagnosticTest.atHomeDiagnosticOrder.endTime)
        return {
            text: `At Home | ${this.getHomeCollectionTimeText(diagnosticTest.atHomeDiagnosticOrder.startTime, diagnosticTest.atHomeDiagnosticOrder.endTime, userContext)}`,
            icon: "home",
            status: status
        }
    }

    private static getInCentreFooterDetails(userContext: UserContext, diagnosticTest: DiagnosticsTestOrderResponse): { text: string, icon: string, status: Status } {
        const status = this.getTestStatus(diagnosticTest.inCentreStepInfo, diagnosticTest.inCentreDiagnosticOrder.slot.workingStartTime, diagnosticTest.inCentreDiagnosticOrder.slot.workingEndTime)
        return {
            text: `At Centre | ${TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, diagnosticTest.inCentreDiagnosticOrder.slot.workingStartTime, "ddd, D MMM, h:mm A")}`,
            icon: "incentre",
            status: status
        }
    }

    private static getTestStatus(stepInfo: StepInfo, startTime: number, endTime: number): Status {
        const confirmedState = { text: "CONFIRMED", colour: "#000000" }
        const cancelledState = { text: "CANCELLED", colour: "#e05343" }
        const missedState = { text: "MISSED", colour: "#d1d1d1" }
        const completedState = { text: "COMPLETED", colour: "#50d166" }
        const currentTimeInEpochInMillis = new Date().getTime()

        if (stepInfo.stepState === "COMPLETED" || stepInfo.stepState === "SAMPLE_COLLECTED") {
            return completedState
        } else if (stepInfo.stepState === "NOT_BOOKED") {
            return cancelledState
        } else if (stepInfo.stepState === "BOOKED") {
            if (startTime <= currentTimeInEpochInMillis && endTime >= currentTimeInEpochInMillis) {
                return confirmedState
            } else {
                return missedState
            }
        }
    }

    public static getDiagosticTestActions(userContext: UserContext, diagnosticTest: DiagnosticsTestOrderResponse, reportActions?: Map<string, any>): Action[] {
        const actions: Action[] = []
        const userAgent = userContext.sessionInfo.userAgent
        if (diagnosticTest.status === "REPORT_GENERATED") {
            const reportInfo: DiagnosticReportInfo = _.get(diagnosticTest, "finalDiagnosticReport.diagnosticCheckUpReportInfo")
            if (CareUtil.isTestReportIsNull(reportInfo) || userAgent !== "APP") {
                actions.push(CareUtil.getDiagnosticsEmailTestReportAction(diagnosticTest.orderId, "Email Report", "REPORT"))
            } else {
                actions.push({
                    actionType: "NAVIGATION",
                    icon: "REPORT",
                    url: ActionUtil.diagnosticReportPage(diagnosticTest.orderId, diagnosticTest.carefitOrderId),
                    title: "View Report"
                })
            }
        } else if (diagnosticTest.status !== "CANCELLED" && reportActions && userAgent === "APP") {
            let cta
            switch (reportActions.get(diagnosticTest.carefitOrderId)) {
                case "VIEW_REPORT":
                    cta = "View Report"
                    break
                case "TRACK_REPORT":
                    cta = "Track Report"
                    break
                case "NONE":
                default:
                    return actions
            }
            return [{
                actionType: "NAVIGATION",
                icon: "REPORT",
                url: ActionUtil.diagnosticReportPage(diagnosticTest.orderId, diagnosticTest.carefitOrderId),
                title: cta
            }]
        }
        return actions
    }

    public static getDiagnosticTestHomeCenterStatusInfo(diagnosticsTestOrderResponse: DiagnosticsTestOrderResponse): StatusInfo {
        return {
            homeStatus: _.get(diagnosticsTestOrderResponse, "atHomeStepInfo.stepState", null),
            centerStatus: _.get(diagnosticsTestOrderResponse, "inCentreStepInfo.stepState", null),
            sampleCollectionStartDateAtHome: _.get(diagnosticsTestOrderResponse, "atHomeDiagnosticOrder.startTime", 0),
            sampleCollectionStartDateAtCenter: _.get(diagnosticsTestOrderResponse, "inCentreDiagnosticOrder.slot.workingStartTime", 0)
        }
    }

    public static getDiagnosticsStateCardV2(type: string, diagnosticsTestOrderResponse: DiagnosticsTestOrderResponse, statusInfo: StatusInfo, titleObj: TitleObj, topAction: Action, rescheduleAction: Action, style: any): StepStateCard {
        const stepState = type === "AT_CENTER" ? diagnosticsTestOrderResponse.inCentreStepInfo.stepState : diagnosticsTestOrderResponse.atHomeStepInfo.stepState
        switch (stepState) {
            case "BOOKED":
                let isExpanded = true
                if (type === "AT_CENTER" && statusInfo.homeStatus && statusInfo.sampleCollectionStartDateAtHome < statusInfo.sampleCollectionStartDateAtCenter) {
                    isExpanded = (statusInfo.homeStatus === "NOT_BOOKED" || statusInfo.homeStatus === "COMPLETED" || statusInfo.homeStatus === "SAMPLE_COLLECTED") ? true : false
                }
                if (type === "AT_HOME" && statusInfo.centerStatus && statusInfo.sampleCollectionStartDateAtCenter < statusInfo.sampleCollectionStartDateAtHome) {
                    isExpanded = (statusInfo.centerStatus === "NOT_BOOKED" || statusInfo.centerStatus === "COMPLETED" || statusInfo.centerStatus === "SAMPLE_COLLECTED") ? true : false
                }
                const actions = []
                if (topAction) {
                    actions.push({
                        ...topAction,
                        title: topAction.title.toUpperCase(),
                        isHighLight: true
                    })
                }
                if (rescheduleAction) {
                    actions.push({
                        ...rescheduleAction,
                        title: rescheduleAction.title.toUpperCase(),
                        bgColor: ["white", "white"],
                        color: "#ff316d",
                        isHighLight: false
                    })
                }
                return {
                    state: "STARTED",
                    viewType: "INSTRUCTION_CARD",
                    views: [
                        {
                            title: titleObj.title,
                            text: titleObj.startText,
                            meta: {
                                instructions: type === "AT_CENTER" ? diagnosticsTestOrderResponse.inCentreInstructions : diagnosticsTestOrderResponse.atHomeInstructions,
                                expandObj: {
                                    isExpandable: true,
                                    isExpanded
                                },
                                style: style || {},
                                actions: actions,
                                title: titleObj.title,
                                subtitle: titleObj.startText
                            }
                        }
                    ]
                }
            case "COMPLETED":
            case "SAMPLE_COLLECTED":
                return {
                    state: "COMPLETED",
                    viewType: "TEXT_ACTION",
                    views: [
                        {
                            title: titleObj.completionTitle,
                            text: titleObj.endText
                        }
                    ]
                }
        }
    }

    public static getDiagnosticTestType(location: DiagnosticAllowedLocations): TestType {
        switch (location) {
            case "ALL_AT_CENTRE":
                return "IN_CENTRE"
            case "ALL_AT_HOME":
                return "AT_HOME"
            case "HOME_PLUS_CENTRE":
                return "BOTH"
        }
    }

    static getHomeCollectionTimeText(startTime: number, endTime: number, userContext: UserContext) {
        // Temp hack Adding 30 mins for home slots alone
        const totalTimeOfSlot = userContext.userProfile.cityId === "Bangalore" ? 1800000 : 3600000
        if (startTime && endTime) {
            return `${TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, startTime, "ddd, DD MMM, hh:mm")} - ${TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, endTime, "hh:mm A")}`
        }
        return undefined
    }

    static isDiagnosticInstructionSupported(userContext: UserContext) {
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "APP")
        return userAgent === "DESKTOP" || userAgent === "MBROWSER" || userContext.sessionInfo.appVersion >= SUPPORTED_DIAGNOSTIC_INSTRUCTION
    }

    static isMultiCenterSupported(userContext: UserContext, hcuConfig: HCUDetailsPageConfig, product?: ConsultationProduct) {
        if (!_.isEmpty(product) && CareUtil.isCultPTProduct(product)) {
            return true
        }

        if (!_.isEmpty(product) && product.consultationMode === "INCENTRE" && product.tenant === "CARE") {
            return true
        }
        if (userContext.userProfile.cityId !== "Bangalore") {
            return false
        }
        if (!_.isEmpty(product) && MULTI_CENTER_SUPPORTED_DOCTOR_TYPES.indexOf(product.doctorType) !== -1) {
            return true
        }
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "APP")
        return this.multiCenterForSomeUsers(hcuConfig, _.get(userContext, "userProfile.userId", "")) && (userAgent === "DESKTOP" || userAgent === "MBROWSER" || userContext.sessionInfo.appVersion >= MULTI_CENTER_SUPPORTED)
    }

    static multiCenterForSomeUsers(hcuConfig: HCUDetailsPageConfig, userId: string): boolean {
        return hcuConfig.isMultiCenterSupported || hcuConfig.homeCollectionEnableIds.indexOf(userId) !== -1
    }

    static isAnxietyTherapy(productId: ConsultationProduct["productId"]) {
        return productId === "CONS_MIND_ANXIETY_THP_ONLINE"
    }

    static isKayaTrailConsult(productId: ConsultationProduct["productId"]) {
        return ["CONS_KAYA_UNDERARMS_TRIAL_INCENTER", "CONS_KAYA_TRIAL_INCENTER", "KAYA_INSTACLARITY_TRIAL_INCENTER"].includes(productId)
    }

    public static isPsychiatry(doctorType: string) {
        return doctorType === "PSYCHIATRIST"
    }

    public static isAnxietyTherapyDoctorType(doctorType: DOCTOR_TYPE) {
        return doctorType === "ANXIETY_THERAPIST"
    }

    static getDatePickerViewHeader(isMultiCenterSupported: boolean, center: Center, title?: string, isCareCenterBrowseFlowSupported?: boolean): { title: string, type?: DATE_PICKER_HEADER_TYPE, action?: Action } {
        if (isMultiCenterSupported) {
            return {
                title: center.name,
                type: "CENTER_SELECTOR",
                action: {
                    actionType: "SELECT_CENTER",
                    meta: isCareCenterBrowseFlowSupported ? {
                        showCenterPricing: true
                    } : undefined
                }
            }
        }
        return {
            title: title ? title : "Select a slot",
            type: "STATIC"
        }
    }

    static getNewCarouselListActionType(userContext: UserContext, oldType: ActionType): ActionType {
        const userAgent = userContext.sessionInfo.userAgent
        if ((userAgent === "MBROWSER" || userAgent === "DESKTOP") || (userContext.sessionInfo.appVersion < CAROUSEL_MODAL_SUPPORTED)) {
            return oldType
        }
        return "SHOW_CAROUSEL_LIST"
    }

    static buildUserSelectionWidget(isNotLoggedIn: boolean, isSelf: boolean, patientsList: Patient[], nextAction: Action, title: string, patientId?: number, name?: string, dividerType?: DividerType, calloutText?: string, meta?: any): UserSelectionWidget {
        if (!name && patientId) {
            const patient = patientsList.find(item => item.id == patientId)
            if (patient) {
                name = `${patient.name}`
            }
        }
        const UserSelectionWidget: UserSelectionWidget = {
            title: title ? title : "Select Customer",
            widgetType: "USER_PICKER_WIDGET",
            action: isNotLoggedIn
                ? CareUtil.loginAlertModal(title)
                : isSelf
                    ? CareUtil.getSelfPatientSelectionModalAction(patientsList, nextAction, undefined, calloutText, meta)
                    : CareUtil.getPatientSelectionModalAction(patientsList, nextAction, title, calloutText),
            prefixText: "For: ",
            selectedUserName: name ? name : undefined,
            dividerType: dividerType ? dividerType : "LARGE"
        }
        return UserSelectionWidget
    }

    static loginAlertModal(title: string): Action {
        return {
            actionType: "SHOW_ALERT_MODAL",
            title: title ? title : "Get pack",
            meta: {
                title: "Login Required!",
                subTitle: "Please login to continue",
                actions: [{ actionType: "LOGOUT", title: "Login" }]
            }
        }
    }

    static getCenterSelectionWidget(preferredCenter: PreferredCenterResponse, action: SelectCenterAction, patientId?: number, showCustomSeparator?: boolean): CenterSelectionWidget {
        const prefixText = "Preferred centre: "
        const centerSelectionWidget: CenterSelectionWidget = {
            title: "Pick a center",
            prefixText: prefixText,
            canChangeCenter: true,
            preferredCenterId: _.get(preferredCenter, "centerResponse.id", undefined),
            preferredCenterName: _.get(preferredCenter, "centerResponse.name", undefined),
            widgetType: "CENTER_PICKER_WIDGET",
            action: action,
            disabledToastText: patientId ? undefined : "Select patient to proceed further",
            showCustomSeparator: showCustomSeparator ? showCustomSeparator : false,
            disabledText: patientId ? false : true,
        }
        return centerSelectionWidget
    }

    static getCenterSelectionAction(product: ManagedPlanSellableProduct | DiagnosticProductResponse, patientId?: number, isNextStepCheckout?: boolean, isNextValidateAddress?: boolean): SelectCenterAction {
        return {
            title: "Pick a center",
            showHelp: true,
            showFavourite: false,
            actionType: "SELECT_CARE_CENTER",
            meta: {
                productId: product.productCode,
                subCategoryCode: product.subCategoryCode,
                patientId: patientId ? patientId : undefined,
                isNextStepCheckout,
                isNextValidateAddress
            },
            productType: "BUNDLE"
        }
    }

    static getSelectSpecialityAction(userContext: UserContext, title: string, product: ConsultationProduct, parentBookingId?: number, patientId?: number, showTitle?: boolean): any {
        // TODO: change this to !== "MIND_THERAPIST" once anxiety coach is removed
        if (product.doctorType === "PSYCHIATRIST") {
            return {
                ...CareUtil.specialistListingAction(userContext, product, false, patientId || undefined, parentBookingId, undefined, undefined, true),
                actionType: "NAVIGATION",
                title: "BOOK SESSION",
                // url: `curefit://doctorlisting?productId=${product.productId}&doctorType=${product.doctorType}`,
                // meta: { // commented out to fix the header
                //     name: `Find a ${CareUtil.getDoctorText(doctorType)}`
                // }
            }
        }

        // const isNewTherapyUIFlow = AppUtil.isTherapistRecommendationV2UISupported(userContext)
        // const nextAction = {
        //     actionType: "NAVIGATION",
        //     title: title,
        //     url: isNewTherapyUIFlow
        //         ? `curefit://userform?formId=THERAPIST_RECOMMENDATION_FORM`
        //         : `curefit://selectspecialist?productId=${productId}&doctorType=${doctorType}${parentBookingId ? `&parentBookingId=${parentBookingId}` : ""}`,
        //     meta: {
        //         name: `Find a ${CareUtil.isMindDoctorType(doctorType) ? "Therapist" : CareUtil.getDoctorText(doctorType)}`
        //     }
        // }

        // Removing triage flow and doctorlisting only
        let nextAction
        if (CareUtil.isSupportGroupProduct(product)) {
            nextAction = {
                title: "BOOK SESSION",
                url: "curefit://fl_slot_selection?parentBookingId=" + parentBookingId + "&patientId=" + patientId
                        + "&productId=" + product.productCode + "&doctorType=" + product.doctorType,
                actionType: "NAVIGATION",
            }
        } else {
            nextAction = {
                ...CareUtil.specialistListingAction(userContext, product, false, patientId || undefined, parentBookingId, undefined, undefined, true, "#FDFDFD,#EEF2F5"),
                title: title,
                meta: showTitle ? {
                    name: `Find a ${CareUtil.isMindTherapist(product.doctorType) ? "Therapist" : CareUtil.getDoctorText(product.doctorType)}`
                } : undefined
            }
        }
        if (!patientId) {
            return CareUtil.getSelfPatientSelectionModalAction([], nextAction, title, undefined, { formUserType: "THERAPY_USER" })
        }
        return nextAction
    }


    static getDiagnosticsEmailTestReportAction(testOrderId: number, title: string, icon?: string): any {
        return {
            title,
            icon,
            actionType: "EMAIL_TEST_REPORT",
            meta: {
                testOrderId
            }
        }
    }

    static isNewEmailedReportSupported(userContext: UserContext) {
        return userContext.sessionInfo.appVersion >= NEW_EMAILED_REPORT_SUPPORTED
    }

    static getDiagnosticsEmailTestReportSummaryWidget(diagnosticsEmailedTestReportDetails: DiagnosticEmailedTestReportResponse): DiagnosticsEmailTestReportSummaryWidget {
        const len = diagnosticsEmailedTestReportDetails.productDetails.length
        const subTitle = `${len} Tests`
        let description: string = ""
        diagnosticsEmailedTestReportDetails.productDetails.map((item: DiagnosticEmailedTestReportItem, index: number) => {
            description += item.productName + (index < len - 1 ? ", " : "")
        })
        return new DiagnosticsEmailTestReportSummaryWidget("Emailed Reports", subTitle, description, CareUtil.getDiagnosticsEmailTestReportAction(diagnosticsEmailedTestReportDetails.orderId, "RESEND"))
    }

    static getCareAnnouncementEligibility(userContext: UserContext) {
        return userContext.sessionInfo.appVersion >= NEW_CARE_3P_ANNOUNCEMENT
    }

    static isCareCenterChangeRestricted(userContext: UserContext, product: ConsultationProduct, isReschedule?: string, followUpConsultationId?: string) {
        return CareUtil.isNewCenterSpecifiFlowSupported(userContext) && CareUtil.isCareCenterRestrictedProduct(product) && (isReschedule === "true" || !_.isEmpty(followUpConsultationId))
    }

    public static isCareCenterRestrictedProduct(product: ConsultationProduct): boolean {
        return CENTER_CHANGE_NOT_RESTRICTED_DOCTOR_TYPES.indexOf(product.doctorType) === -1
    }

    public static getLivePThowItWorksWidget(userContext: UserContext): ProductListWidget {
        const header: Header = {
            title: "How it works",
            color: "#000000"
        }
        const osName = userContext.sessionInfo.osName
        const infoCards: InfoCard[] = [
            {
                icon: "/image/icons/howItWorks/video.png",
                subTitle: "Link to join the class will be enabled 10 minutes before session start time"
            },
            {
                icon: "/image/icons/howItWorks/hitButton_3.png",
                subTitle: "Join the class on cult.fit app at least 5 mins in advance and test your network"
            },
            {
                icon: "/image/icons/ptAtHomeOnboarding/mobile.png",
                subTitle: "Set up your phone/laptop and position yourself so that you and your trainer can see each other"
            },
            {
                icon: "/image/icons/howItWorks/voila.png",
                subTitle: "Voila! You are all set. Enjoy an awesome workout from Home."
            }
        ]
        const widget = new ProductListWidget("SMALL", header, infoCards)
        return widget
    }

    static getTherapistRecommendationAction(title: string, isMedicalDataRecorded: boolean, patientId?: number, parentBookingId?: number, doctorTypes?: DOCTOR_TYPE[]): any {
        // no need to go to userform if already data is recorded
        if (isMedicalDataRecorded) {
            const queryParams = {
                subCategoryCode: "MIND_THERAPY",
                userFormSubmitted: true,
                patientId: patientId,
                doctorTypes: !_.isEmpty(doctorTypes) ? doctorTypes.join(",") : undefined,
                parentBookingId: !_.isNil(parentBookingId) ? parentBookingId : undefined
            }
            return {
                actionType: "NAVIGATION",
                title: title,
                url: `curefit://doctorrecommendations${ActionUtil.serializeAsQueryParams(queryParams)}`
            }
        }
        const formAction: Action = {
            actionType: "NAVIGATION",
            title: title,
            url: "curefit://userform?formId=THERAPIST_RECOMMENDATION_FORM"
        }
        let action: Action = formAction
        if (!patientId) {
            action = {
                actionType: "ADD_PATIENT",
                title: title,
                meta: {
                    url: formAction.url,
                    action: formAction,
                    emergencyContactRelations: CareUtil.getEmergencyRelations(),
                    guardianRelations: CareUtil.getGuardianRelations(),
                    reqParams: {
                        patientRelation: "Self",
                        formUserType: "THERAPY_USER"
                    }
                }
            }
        }
        return action
    }

    static isPartOfSkinProducts(subcategoryCode: SUB_CATEGORY_CODE): boolean {
        return SKIN_HAIR_SUBCATEGORIES.indexOf(subcategoryCode) !== -1
    }

    static getConsultationInstructionWidget(bookingDetail: BookingDetail, pageConfig: TeleconsultationDetailsPageConfig, userAgent?: UserAgent, consultationInstruction?: ConsultationInstructionResponse[]): InstructionsWidget {
        return new InstructionsWidget(CareUtil.getConsulationInstructions(bookingDetail, pageConfig), userAgent, true, consultationInstruction || [])
    }

    static getPackContent(packageProduct: DiagnosticProductResponse | ConsultationSellableProduct | BundleSessionSellableProduct) {
        let howItWorksItem,
            packContentsDetailed,
            packOffering,
            packQnA,
            packBenefit,
            packSummary,
            packFAQ,
            packHowItHelp,
            bundleInstructionPreBooking,
            consultInstructionPreBooking,
            howItWorksItemV2,
            packHowItHelpV2,
            packConfirmationInstruction,
            packPdpHowItWorks

        (packageProduct?.infoSection?.children || []).map(infoSection => {
            switch (infoSection.type) {
                case "PACK_STEPS":
                    howItWorksItem = infoSection
                    break
                case "PACK_CONTENTS_DETAILED":
                    packContentsDetailed = infoSection
                    break
                case "PACK_OFFERING":
                    packOffering = infoSection
                    break
                case "PACK_Q_AND_A":
                    packQnA = infoSection
                case "PACK_BENEFITS":
                    packBenefit = infoSection
                    break
                case "PACK_CONTENTS_SUMMARY":
                    packSummary = infoSection
                    break
                case "WHAT_YOU_GET":
                    packHowItHelp = infoSection
                    break
                case "FAQ":
                    packFAQ = infoSection
                    break
                case "INSTRUCTION_PRE_BOOKING_BUNDLE":
                    bundleInstructionPreBooking = infoSection
                    break
                case "INSTRUCTION_PRE_BOOKING_CONSULT_SESSION":
                    consultInstructionPreBooking = infoSection
                    break
                case "PACK_STEPS_V2": // Used for flutter app
                    howItWorksItemV2 = infoSection
                    break
                case "WHAT_YOU_GET_V2": // Used for flutter app
                    packHowItHelpV2 = infoSection
                    break
                case "PACK_PDP_HOW_IT_WORKS":
                    packPdpHowItWorks = infoSection
                    break
                case "PACK_CONFIRMATION":
                    packConfirmationInstruction = infoSection
                    break
            }
        })
        return {
            howItWorksItem,
            packContentsDetailed,
            packOffering,
            packQnA,
            packBenefit,
            packSummary,
            packFAQ,
            packHowItHelp,
            bundleInstructionPreBooking,
            consultInstructionPreBooking,
            howItWorksItemV2,
            packHowItHelpV2,
            packConfirmationInstruction,
            packPdpHowItWorks
        }
    }

    static getPackQnAWidget(userAgent: UserAgentType, packInfo: ManagedPlanPackInfo): ProductListWidget {
        if (_.isEmpty(packInfo)) {
            return undefined
        }
        const header: Header = {
            title: packInfo.title,
            color: "#000000"
        }
        const actionCards: ActionCard[] = []
        if (packInfo.children) {
            actionCards.push({
                subTitle: packInfo.subtitle,
                seeMoreText: "Learn More",
                icon: "/image/icons/howItWorks/charges_3.png",
                cardAction: {
                    actionType: userAgent === "APP" ? "NAVIGATION" : "OPEN_INFO_PAGE_MODAL",
                    url: `curefit://infopage?contentId=${packInfo.type}&pageType=LIST`,
                    meta: {
                        contentId: packInfo.type,
                        data: this.getQnAInfoPage(packInfo)
                    }
                }
            })
        }

        const widget = new ProductListWidget("SMALL", header, actionCards)
        if (userAgent === "DESKTOP") {
            widget.orientation = "RIGHT"
        }
        widget.hasDividerBelow = true
        widget.dividerType = "SMALL"
        return widget
    }

    public static getKayaFulfillmentWidget(): CareServiceFulfillmentWidget {
        return {
            widgetType: "CARE_SERVICE_FULFILLMENT_WIDGET",
            title: "Fulfilled By",
            imageUrl: "https://cdn-media.cure.fit/image/carefit/skin/kaya_logo.png",
            pageItems: [
                "Only UVC sanitised clinic chain in India",
                "60+ WHO compliant safety measures",
            ],
            backgroundColor: "#ffede6",
            borderColor: "#e9c8b8",
            action: {
                actionType: "SHOW_CARE_FULLFILLED_SELLER_MODAL",
                title: "KNOW MORE",
                meta: {
                    aboutTitle: "ABOUT",
                    imageUrl: "https://cdn-media.cure.fit/image/carefit/skin/kaya_logo.png",
                    description: "Expert dermatologists at the Kaya clinics have been helping people enhance the look and feel of their skin since 2002. Using updated technologies from across the world, they conform to the highest international quality and safety standards.",
                    sellerSteps: [
                        {
                            image: "https://cdn-media.cure.fit/image/carefit/skin/kaya_icon_1.png",
                            item: "Schedule & manage your sessions on the cult.fit app"
                        },
                        {
                            image: "https://cdn-media.cure.fit/image/carefit/skin/kaya_icon_2.png",
                            item: "Walk into the Kaya clinic at your schedule time and get the procedure done without any hassles"
                        },
                        {
                            image: "https://cdn-media.cure.fit/image/carefit/skin/kaya_icon_3.png",
                            item: "Follow up with your doctor through online consultations and chat"
                        },
                    ],
                },
            },
        }
    }

    public static getQnAInfoPage(packInfo: ManagedPlanPackInfo): ProductDetailPage {
        const page = new ProductDetailPage()
        page.widgets = []
        const header: Header = {
            title: `\n\n${packInfo.title}`
        }
        page.widgets.push(new ProductListWidget("SMALL", header, []))
        packInfo.children.forEach((item) => {
            const descriptions: InfoCard[] = [{
                title: item.question,
                subTitle: item.answer
            }]
            const widget = new DescriptionWidget(descriptions)
            widget.dividerType = "SMALL"
            widget.iconType = null
            page.widgets.push(widget)
        })
        page.actions.push({
            actionType: "POP_ACTION",
            title: "Got it"
        })
        return page
    }

    public static getInProgressTestsPage(reportData: DiagnosticReportV2, testOrderId: number, totalCount: number): ProductDetailPage {
        const page = new ProductDetailPage()
        page.widgets = []
        const header: Header = {
            title: ""
        }
        page.widgets.push(new ProductListWidget("SMALL", header, []))
        page.widgets.push(new ReportStatusWidget(totalCount, reportData.inProgressTestsDetails.testCount, "IN PROGRESS", "INPROGRESS"))
        page.widgets = page.widgets.concat(CareUtil.getInProgressDiagnosticChildReportSummaryWidget(reportData.inProgressTestsDetails.testDetails, testOrderId))
        return page
    }

    public static getPendingTestsPage(reportData: DiagnosticReportV2, testOrderId: number, totalCount: number): ProductDetailPage {
        const page = new ProductDetailPage()
        page.widgets = []
        const header: Header = {
            title: ""
        }
        page.widgets.push(new ProductListWidget("SMALL", header, []))
        page.widgets.push(new ReportStatusWidget(totalCount, reportData.pendingTestsDetails.testCount, "TESTS PENDING", "PENDING"))
        if (reportData.pendingTestsDetails.testDetails && !_.isEmpty(reportData.pendingTestsDetails.testDetails)) {
            page.widgets.push(CareUtil.getDiagnosticChildReportSummaryWidget(reportData.pendingTestsDetails.testDetails, testOrderId, undefined, undefined, false))
        }
        return page
    }

    private static getInProgressDiagnosticChildReportSummaryWidget(reportInfo: TestDetails[], testOrderId: number): DiagnosticsTestReportSummaryWidget[] {
        const childReportInfos = reportInfo.filter(reportInfo => (!_.isNil(reportInfo.testId)))
        const today = new Date()
        const tomorrow = new Date(today)
        tomorrow.setDate(tomorrow.getDate() + 1)
        const reportItemsTodayDelayed: TestDetails[] = []
        const reportItemsToday: TestDetails[] = []
        const reportItemsMap = new Map<string, TestDetails[]>()
        _.forEach(childReportInfos, testDetail => {
            const dat = new Date(testDetail.reportTat)
            if (dat.getDate() === today.getDate()) {
                reportItemsToday.push(testDetail)
            } else if (dat.getTime() < today.getTime()) {
                reportItemsTodayDelayed.push(testDetail)
            } else {
                let key: string
                if (tomorrow.getDate() === dat.getDate()) {
                    key = "TOMORROW"
                } else {
                    const dateNum = dat.getDate()
                    const month = dat.toLocaleString("default", { month: "short" })
                    key = dateNum + " " + month
                }
                if (reportItemsMap.get(key)) {
                    reportItemsMap.get(key).push(testDetail)
                } else {
                    const reportItem: TestDetails[] = []
                    reportItem.push(testDetail)
                    reportItemsMap.set(key, reportItem)
                }
            }
        })
        const widgets: DiagnosticsTestReportSummaryWidget[] = []
        if (reportItemsTodayDelayed && !_.isEmpty(reportItemsTodayDelayed)) {
            widgets.push(CareUtil.getDiagnosticChildReportSummaryWidget(reportItemsTodayDelayed, testOrderId, "REPORTS BY: TODAY", "DELAYED", false))
        }
        if (reportItemsToday && !_.isEmpty(reportItemsToday)) {
            widgets.push(CareUtil.getDiagnosticChildReportSummaryWidget(reportItemsToday, testOrderId, "REPORTS BY: TODAY", undefined, false))
        }
        reportItemsMap.forEach((value, key) => {
            if (value && !_.isEmpty(value)) {
                widgets.push(CareUtil.getDiagnosticChildReportSummaryWidget(value, testOrderId, "REPORTS BY: " + key, undefined, false))
            }
        })
        return widgets
    }

    public static getDiagnosticChildReportSummaryWidget(reportInfo: TestDetails[], testOrderId: number, title?: string, info?: string, isClickable: boolean = true): DiagnosticsTestReportSummaryWidget {
        const childReportInfos = reportInfo.filter(reportInfo => (!_.isNil(reportInfo.testId)))

        const reportItems = _.map(childReportInfos, (childReportInfo) => {
            let action: Action = undefined
            if (isClickable) {
                action = {
                    url: `curefit://diagnostictestreport?testOrderId=${testOrderId}&testId=${childReportInfo.testId}`,
                    actionType: "NAVIGATION"
                }
            }
            return {
                header: {
                    title: childReportInfo.testName,
                    subtitle: `${childReportInfo.testCount} tests`,
                    action: action
                },
            }
        })
        return new DiagnosticsTestReportSummaryWidget(reportItems, true, title ? { title, info } : undefined)
    }

    public static getTimeSlotMap(dateAppointments: TCAvailableSlotsDetails[] | DiagnosticSlot[], timezone: Timezone): { [key: string]: DiagnosticSlot[] } | { [key: string]: TCAvailableSlotsDetails[] } {
        return _.groupBy(dateAppointments, (x: TCAvailableSlotsDetails | DiagnosticSlot) => {
            const startHour = Number(TimeUtil.formatEpochInTimeZoneDateFns(timezone, x.startTime, "HH"))
            if (startHour >= 0 && startHour < 3) {
                return "00:00-02:59"
            } else if (startHour >= 3 && startHour < 6) {
                return "03:00-05:59"
            } else if (startHour >= 6 && startHour < 9) {
                return "06:00-08:59"
            } else if (startHour >= 9 && startHour < 12) {
                return "09:00-11:59"
            } else if (startHour >= 12 && startHour < 15) {
                return "12:00-14:59"
            } else if (startHour >= 15 && startHour < 18) {
                return "15:00-17:59"
            } else if (startHour >= 18 && startHour < 21) {
                return "18:00-20:59"
            } else if (startHour >= 21 && startHour <= 23) {
                return "21:00-23:59"
            }
        })
    }

    public static getTimeSlotTitleFromKey(key: string): string {
        switch (key) {
            case "00:00-02:59":
                return "12AM - 3AM"
            case "03:00-05:59":
                return "3AM - 6AM"
            case "06:00-08:59":
                return "6AM - 9AM"
            case "09:00-11:59":
                return "9AM - 12PM"
            case "12:00-14:59":
                return "12PM - 3PM"
            case "15:00-17:59":
                return "3PM - 6PM"
            case "18:00-20:59":
                return "6PM - 9PM"
            case "21:00-23:59":
                return "9PM - 12AM"
            default:
                ""
        }
    }

    public static getDatewiseSlot(
        date: string,
        tz: Timezone,
        timeslots: TimeSlotCategory[],
        isSlotNotAvailableToday: boolean,
        slotAvailableDates: string[],
        icon: string,
        imageUrl?: string,
        noSlotText?: string,
        productId?: string,
        parentBookingId?: number,
        patiendId?: string,
        isRescheduled?: boolean,
        logger?: Logger
    ): DateWiseSlots {
        const parentBookingIdentity = parentBookingId ? parentBookingId : -1
        // don't show alternate doctor slots while rescheduling a consult
        const noSlotsAvailableForSugarfitDoctor = isSlotNotAvailableToday && productId === "SUGAR001" && (isRescheduled === undefined || isRescheduled === false)
        const otherAvailableAgentSlotPageAction: Action = noSlotsAvailableForSugarfitDoctor ? {
            url: `curefit://selectCareDateV1?productId=${productId}&isExternal=true&patientId=${patiendId}&parentBookingId=${parentBookingIdentity}`,
            actionType: "NAVIGATION",
            isEnabled: true
        } : undefined
        const nextAvailableSlotInfo = isSlotNotAvailableToday && !_.isEmpty(slotAvailableDates) ? {
            title: noSlotsAvailableForSugarfitDoctor ? `No Slots Available` : "NEXT AVAILABLE DATE",
            nextDate: slotAvailableDates[0],
            subtitle: noSlotsAvailableForSugarfitDoctor ? "However you can book consultation with other doctor.\nNote: Your assigned doctor will not be changed.\n" : TimeUtil.getMomentForDateString(slotAvailableDates[0], tz).format("dddd DD MMM"),
        } : noSlotsAvailableForSugarfitDoctor ? {
            title: `No Slots Available`,
            nextDate: undefined,
            subtitle: "However you can book consultation with other doctor.\nNote: Your assigned doctor will not be changed.\n",
        } : undefined
        logger && logger?.info(`Next Available slot info :: ${JSON.stringify(nextAvailableSlotInfo)}`)
        logger && logger?.info(`otherAvailableAgentSlotPageAction :: ${JSON.stringify(otherAvailableAgentSlotPageAction)}`)
        return {
            date,
            dateType: TimeUtil.getMomentForDateString(date, tz).isoWeekday() >= 6 ? "WEEKEND" : "WEEKDAY",
            timeZones: timeslots,
            noSlotsAvailable: isSlotNotAvailableToday,
            noSlotText: noSlotText || "Sorry, No slots available today",
            icon,
            imageUrl,
            nextAvailableSlotInfo: nextAvailableSlotInfo,
            otherAvailableAgentSlotPageAction: otherAvailableAgentSlotPageAction
        }
    }

    public static getDoctorUnavailablityWidget(useShorterText: boolean = false, action?: Action, hasDividerBelow: boolean = true): ClpCalloutWidget {
        return new ClpCalloutWidget({
            widgetType: "CLP_CALLOUT_WIDGET",
            subTitle: useShorterText ? DOCTOR_UNAVAILABLE_SHORTER_TEXT : DOCTOR_UNAVAILABLE_TEXT,
            linearGradientColors: ["rgb(252,193,97)", "rgb(250,159,134)"],
            icon: "IMPORTANT",
            hasDividerBelow: hasDividerBelow,
            dividerType: "SMALL",
            subTitleColor: "#000000",
            iconBackgroundColor: "rgba(255,255,255, 0.3)",
            borderRadius: 10,
            action: action ? action : undefined
        })
    }

    public static getConsultationTag(consultationUserState: CONSULTATION_STATUS, status: CONSULTATION_STATUS) {
        return consultationUserState === "SCHEDULED" || status === "SCHEDULED"
            ? "UPCOMING"
            : consultationUserState === "DOCTOR_UNAVAILABLE" || status === "DOCTOR_UNAVAILABLE"
                ? "DOCTOR UNAVAILABLE"
                : undefined
    }

    public static isZoomLinkActionEnabled(activityActions: CallToAction[]): boolean {
        const zoomLinkAction = _.find(activityActions, action => action === "JOIN_ZOOM_MEETING")
        return zoomLinkAction ? true : false
    }

    public static async getPTSessionBookAction(userContext: UserContext, cultPTService: IHealthfaceService, consultationProduct: ConsultationProduct, patientId: number, productCode: string, parentBookingId?: number): Promise<Action> {
        let action
        const preferedDoctor = await cultPTService.getYourTeam(userContext.userProfile.userId, patientId, productCode)
        if (userContext.sessionInfo.appVersion >= NEW_SPECIALITY_BOOKING_PAGE_SUPPORTED && _.isEmpty(preferedDoctor)) {
            action = {
                ...CareUtil.specialistListingAction(userContext, consultationProduct, true, patientId, parentBookingId, undefined, undefined, true, "#f1f4f7,#f1f4f7"),
                title: "BOOK"
            }
        }
        let url = ActionUtil.selectCareDateV1(consultationProduct.productId, undefined, parentBookingId)
        url += `&patientId=${patientId}`
        const sessionBookingAction: Action = action ? action : {
            actionType: "NAVIGATION",
            title: "BOOK",
            url: url
        }
        return sessionBookingAction
    }

    public static isNutritionistDoctor(doctorType: DOCTOR_TYPE) {
        return doctorType === "LC"
    }
    public static getConsultationCancelCutOffTime(bookingDetail: BookingDetail, timezone: Timezone): string {
        const time = _.get(bookingDetail, "consultationOrderResponse.appointmentActionsWithContext.cancelActionWithContext.action.thresholdEpoch")
        return TimeUtil.formatEpochInTimeZone(timezone, time, "h:mm a")
    }

    public static getLivePTCancellationCutoffWidget(bookingDetail: BookingDetail, timezone: Timezone): DescriptionWidget {
        const cancellationWindowInHours = (bookingDetail.hasOldCancellationWindow ? 180 : bookingDetail.consultationOrderResponse.consultationProduct.cancellationWindowInMins) / 60
        const cancelThresholdEpoch = bookingDetail.consultationOrderResponse.startTime - ((bookingDetail.hasOldCancellationWindow ? 180 : bookingDetail.consultationOrderResponse.consultationProduct.cancellationWindowInMins) * 60 * 1000)
        const cutoffRemainingTime = cancelThresholdEpoch - TimeUtil.getCurrentEpoch()
        const title = cutoffRemainingTime > 0 ? `Cancellation cutoff - ${TimeUtil.formatEpochInTimeZone(timezone, cancelThresholdEpoch, "h:mm a")}` : "Cancellation Policy"
        const subtitle = cutoffRemainingTime > 0 ? `Cancel ${cancellationWindowInHours} hours before the scheduled start time.` : `You can cancel your session upto ${cancellationWindowInHours} hours before the scheduled start time`
        const infoCard: InfoCard = {
            title: title,
            subTitle: subtitle
        }
        const widget = new DescriptionWidget([infoCard])
        return widget
    }

    public static getLivePTMembershipContextWidget(bookingDetail: BookingDetail, userContext: UserContext): MembershipContextWidget {
        const { bundleConsultationRejectionReason, message } = bookingDetail.bundleConsultationRejectionContext
        const isLivePTSessionConsultation = CareUtil.isLivePTSessionConsultation(bookingDetail.consultationOrderResponse)
        const subCategoryCode: SUB_CATEGORY_CODE = isLivePTSessionConsultation ? "LIVE_PERSONAL_TRAINING" : "LIVE_SGT"
        let title, description, action: Action = null
        let categoryType = ""
        switch (bundleConsultationRejectionReason) {
            case "EXPIRED": {
                title = "Membership expired"
                description = message
                action = {
                    actionType: "NAVIGATION",
                    title: "RENEW",
                    url: `curefit://bundlesession?subCategoryCode=${subCategoryCode}`
                }
                categoryType = isLivePTSessionConsultation ? "LIVE_PT_MEMBERSHIP_EXPIRED" : "LIVE_SGT_MEMBERSHIP_EXPIRED"
                break
            }
            case "PAUSED": {
                title = "Membership paused"
                description = message
                categoryType = isLivePTSessionConsultation ? "LIVE_PT_MEMBERSHIP_PAUSED" : "LIVE_SGT_MEMBERSHIP_PAUSED"
                break
            }
        }
        if (title && description) {
            const analyticsData = {
                eventName: "MEMBERSHIP_CONTEXT_VIEWED",
                meta: {
                    category: categoryType
                }
            }
            const analyticsDataOnClick = {
                eventName: "MEMBERSHIP_CONTEXT_CLICKED",
                meta: {
                    category: categoryType
                }
            }
            const widget: MembershipContextWidget = {
                widgetType: "INFO_ACTION_ACCORDION_WIDGET",
                title: title,
                description: description,
                analyticsData: analyticsData,
                analyticsDataOnClick: analyticsDataOnClick
            }
            if (action) {
                widget["action"] = action
            }
            return widget
        }
    }


    public static getZoomSdkActionFromUrl(userContext: UserContext, zoomLink: string, user: User, patientId: number, doctorType: DOCTOR_TYPE, bookingId: number, zoomParticipantId?: string): Action {
        const parsedZoomLink = urlParse(zoomLink, true)
        const pathName = parsedZoomLink.pathname
        const queryParams = parsedZoomLink.query
        const paths = pathName.split("/")
        const meetingId = paths[paths.length - 1]
        const meetingPassword = queryParams.pwd
        const sessionInfo = userContext.sessionInfo
        const isAndroidUser = sessionInfo.osName.toLowerCase() === "android"
        return {
            actionType: "EXTERNAL_DEEP_LINK",
            title: "JOIN NOW",
            url: zoomLink,
            meta: {
                displayName: user.firstName + "" + (user.lastName ? " " + user.lastName : ""),
                meetingId,
                patientId,
                meetingPassword,
                bookingId,
                zoomParticipantId,
                muteOnJoin: CareUtil.isLiveSGTDoctorType(doctorType) && isAndroidUser ? true : false
            }
        }
    }
    public static async isZoomWebSDKEnabled(userAgent: string, hamletBusiness: HamletBusiness, userContext: UserContext): Promise<boolean> {
        let isZoomWebSDKEnabled = false
        if (userAgent === "MBROWSER" || userAgent === "DESKTOP") {
            const experimentId = ZOOM_WEB_SDK // owner "<EMAIL>"
            const { assignmentsMap }: UserAllocation = { assignmentsMap: {} }
            if (assignmentsMap && assignmentsMap[ZOOM_WEB_SDK]) {
                const zoomSDKBucket = assignmentsMap[ZOOM_WEB_SDK].bucket
                if (!_.isEmpty(zoomSDKBucket)) {
                    isZoomWebSDKEnabled = true
                }
            }
        }
        return isZoomWebSDKEnabled
    }
    public static getLivePtJoinClassOnboardingView(nextAction: Action): Action {
        const livePtJoinClassAnnouncementView: ImageCarouselAnnouncementView = {
            type: "IMAGE_CAROUSEL_POPUP",
            action: nextAction,
            announcementData: [{ url: "/image/icons/ptAtHomeOnboarding/live-pt-join-class-onboarding-1.png" }, { url: "/image/icons/ptAtHomeOnboarding/live-pt-join-class-onboarding-2.png" }],
        }
        return {
            actionType: "SHOW_ANNOUNCEMENT",
            title: "JOIN NOW",
            meta: livePtJoinClassAnnouncementView
        }
    }
    public static getDoctorListingTitle(doctorType?: DOCTOR_TYPE) {
        switch (doctorType) {
            case "MIND_THERAPIST":
            case "PSYCHIATRIST":
            case "PHYSIOTHERAPIST":
            case "LC":
            case "COUPLE_THERAPIST":
            case "AYURVEDA":
            case "COVID_THERAPIST":
            case "SLEEP_THERAPIST":
                return `My ${titleCase(CareUtil.getDoctorText(doctorType))}`

            default:
                return "My Doctor"
        }
    }

    public static getDoctorMessageText(doctorType?: DOCTOR_TYPE) {
        switch (doctorType) {
            case "MIND_THERAPIST":
            case "PSYCHIATRIST":
            case "PHYSIOTHERAPIST":
            case "LC":
            case "ANXIETY_THERAPIST":
            case "COUPLE_THERAPIST":
            case "AYURVEDA":
            case "COVID_THERAPIST":
            case "SLEEP_THERAPIST":
                return `MESSAGE ${CareUtil.getDoctorText(doctorType).toUpperCase()}`
            default:
                return "MESSAGE DOCTOR"
        }
    }

    public static getDoctorSearchFilterModalItems(
        userContext: UserContext,
        doctorType?: DOCTOR_TYPE,
        filterData?: DoctorListingFilterResponse,
        parentBookingId?: number
    ) {
        const sortItems = []
        const isDesktop = AppUtil.isDesktop(userContext)
        const isNewSpecialityFilterSupported = AppUtil.isCareAgentFilterSupported(userContext)
        if (!CareUtil.isTherapyOnlyDoctorType(doctorType)) {
            if (_.isNil(parentBookingId)) {
                if (doctorType !== "LC") {
                    sortItems.push({
                        title: isDesktop ? "Price (↓)" : "Price: High to Low",
                        sort_by: "PRICE",
                        sort_order: "DESC",
                    }, {
                        title: isDesktop ? "Price (↑)" : "Price: Low to High",
                        sort_by: "PRICE",
                        sort_order: "ASC",
                    })
                }
            }
            sortItems.push(
                {
                    title: isDesktop ? "Experience" : "Years of Experience",
                    sort_by: "EXPERIENCE",
                    sort_order: "DESC",
                },
                {
                    title: isDesktop ? "Availability" : "Earliest Available",
                    sort_by: "AVAILABILITY",
                    sort_order: "ASC",
                }
            )
        }
        const titleItems = {
            header: "Simplify Your Search",
            sortTitle: "Sort By",
            apply: "APPLY",
            cancel: "CANCEL",
            clearAll: "Clear All"
        }
        const filterItems = [] as any[]
        (filterData?.agentFilterInfos || []).map((item: AgentFilterInfos) => {
            if (CUSTOM_SUPPORTED_DOCTOR_FILTER.includes(item?.internalName)) {
                filterItems.push(this.getStaticFilters(item))
            } else if (isNewSpecialityFilterSupported) {
                filterItems.push(this.getStaticFilters(item))
            }
        })
        filterItems.push(this.getAvailabiltyFilterItems())
        const filterModalItems = { sortItems: sortItems, titleItems: titleItems, filterItems: filterItems.filter((item => item?.items?.length)) }
        return filterModalItems
    }


    public static getStaticFilters(filterItem: AgentFilterInfos) {
        const isCity = "CITY_FILTER" === filterItem?.internalName ? "city" : ""
        const isLanguage = "LANGUAGE_FILTER" === filterItem?.internalName ? "language" : ""
        const sendMeta = !(isCity || isLanguage)
        const items = [] as any[]
        (filterItem?.values || []).map((item: AgentFilterValue) => {
            if (item) {
                items.push({
                    displayName: item.displayName,
                    filterCode: item.internalName,
                    filterType: sendMeta ? filterItem?.internalName : isCity || isLanguage,
                    meta: sendMeta && filterItem?.agentAttributeDetail ? {
                        agentAttributeDetail: filterItem?.agentAttributeDetail
                    } : undefined
                })
            }
        })
        return {
            title: filterItem.displayName,
            filterType: sendMeta ? filterItem.internalName : undefined,
            items
        }
    }

    public static getAvailabiltyFilterItems() {
        const items: any[] = []
        items.push(
            {
                displayName: "Today",
                filterCode: "TODAY",
                filterType: "availability"
            },
            {
                displayName: "Tomorrow",
                filterCode: "TOMORROW",
                filterType: "availability"
            },
            {
                displayName: "Weekend",
                filterCode: "WEEKEND",
                filterType: "availability"
            }
        )
        const availabiltyFilterItems = { title: "Availability", items }
        return availabiltyFilterItems
    }

    public static createAgentAttributeFilters(selectedFilters: any, categoryFilters: AgentFilterInfos[]): AgentAttributeFilter[] {
        const attributeFilters: AgentAttributeFilter[] = []
        for (const key in selectedFilters) {
            const filter = categoryFilters.find((item) => {
                return item.internalName.toLowerCase() === key.toLowerCase()
            })
            if (filter) {
                const valueString = selectedFilters[key]
                const values = valueString.split(",")
                const attributeFilter: AgentAttributeFilter = {
                    name: filter.agentAttributeDetail.name,
                    type: filter.agentAttributeDetail.type,
                    values: values
                }
                attributeFilters.push(attributeFilter)
            }
        }
        return attributeFilters
    }

    public static createCategoryFilters(filterItems: AgentFilterInfos[]) {
        const items = filterItems.map((item) => {
            const items = item.values.map((value: any) => {
                return {
                    displayName: value.displayName,
                    filterCode: value.internalName,
                    filterType: item.internalName,
                    meta: {
                        agentAttributeDetail: item.agentAttributeDetail
                    }
                }
            })
            return {
                title: item.displayName,
                filterType: item.internalName,
                items
            }
        })
        return items
    }

    public static getDoctorSearchFilterModalItemsIndices(sort_index?: number) {
        const selectedFilterItems = { sortIndex: sort_index }
        return selectedFilterItems
    }

    public static getDoctorSearchFilterModalItemsV2(
        sort_index: number,
        filterModalItems: any,
        filtersData: DoctorListingFilterResponse,
        queryCityFilters?: string[],
        queryLanguageFilters?: string[],
        queryAvailabiltyFilters?: string[],
        filterInfo?: { appliedFilters: any, categoryFilters: any }
    ) {
        const selectedFilterItems = []
        const filterCities = filtersData.agentFilterInfos.find(item => item.internalName === "CITY_FILTER")
        const filterLanguages = filtersData.agentFilterInfos.find(item => item.internalName === "LANGUAGE_FILTER")
        if (sort_index >= 0) {
            const sortItem = filterModalItems.sortItems[sort_index]
            if (!_.isEmpty(sortItem)) {
                selectedFilterItems.push({ displayName: sortItem.title, filterCode: sort_index, filterType: "SORT" })
            }
        }
        if (!_.isEmpty(queryCityFilters)) {
            const filterCityItems: any[] = this.getStaticFilters(filterCities).items
            queryCityFilters.map((city) => {
                selectedFilterItems.push(...filterCityItems.filter((cityFilter) => { return cityFilter.filterCode === city }))
            })
        }
        if (!_.isEmpty(queryLanguageFilters)) {
            const filterLanguageItems: any[] = this.getStaticFilters(filterLanguages).items
            queryLanguageFilters.map((language) => {
                selectedFilterItems.push(...filterLanguageItems.filter((languageFilter) => { return languageFilter.filterCode === language }))
            })
        }
        if (!_.isEmpty(queryAvailabiltyFilters)) {
            const filterAvailableItems: any[] = this.getAvailabiltyFilterItems().items
            queryAvailabiltyFilters.map((availabilty) => {
                selectedFilterItems.push(...filterAvailableItems.filter((availableFilter) => { return availableFilter.filterCode === availabilty }))
            })
        }
        if (filterInfo && filterInfo.appliedFilters && filterInfo.categoryFilters) {
            const selectedCategoryFilters: any = []
            const appliedFilters = filterInfo.appliedFilters
            const categoryFilters = filterInfo.categoryFilters
            for (const key in appliedFilters) {
                if (!_.isNumber(appliedFilters[key])) {
                    const values = appliedFilters[key].split(",")
                    const categoryFilter = categoryFilters.find((filter: any) => {
                        return filter.filterType.toLowerCase() == key.toLowerCase()
                    })
                    if (categoryFilter) {
                        values.forEach((value: string) => {
                            const filter = categoryFilter.items.find((item: any) => {
                                return item.filterCode === value
                            })
                            if (filter) {
                                selectedCategoryFilters.push(filter)
                            }
                        })
                    }
                }
            }
            selectedFilterItems.push(...selectedCategoryFilters)
        }
        return selectedFilterItems
    }

    public static filterDoctorBookingScreenActionQuery(userContext: UserContext, params: any, isWebUrlNeeded?: boolean): IDoctorBookingQueryParam {
        const { patientId, isReschedule, parentBookingId, productId, showPrice }: IDoctorBookingQueryParamWeb = params

        if (AppUtil.isWeb(userContext) || isWebUrlNeeded) {
            return {
                patientId,
                isReschedule,
                parentBookingId,
                productId,
                showPrice: showPrice ? true : undefined
            }
        }

        return params
    }

    public static getDoctorBookingScreenAction(title: string, params: IDoctorBookingQueryParam, userContext: UserContext, isWebUrlNeeded?: boolean): Action {
        const queryParams = this.filterDoctorBookingScreenActionQuery(userContext, params, isWebUrlNeeded),
            queryString = ActionUtil.serializeAsQueryParams(queryParams),
            url = AppUtil.isFromFlutterAppFlow(userContext) ? "curefit://fl_doctorbooking" : "curefit://doctorbooking"
        const baseUrl = AppUtil.isWeb(userContext) || isWebUrlNeeded ? `/care/doctor-booking/${params.slugValue}` : url

        return {
            actionType: "NAVIGATION",
            title,
            url: `${baseUrl}${queryString}`
        }
    }

    public static isSugarfitExperienceCenterProduct(product: ConsultationProduct) {
        return product.productId === "SFEXP001" || product.productCode === "SFEXP001"
    }

    public static isSugarfitExperienceCenterDiagnosticProduct(product: ConsultationProduct) {
        return product.productId === "SF_AT_CENTER_DIAGNOSTIC" || product.productCode === "SF_AT_CENTER_DIAGNOSTIC"
    }

    public static isSugarfitPyschologyProduct(product: ConsultationProduct) {
        return product.productId === "SFPD001"
    }

    public static isSugarfitWellnessAtCenterProduct(product: ConsultationProduct) {
        return product.productId === "SFVRCHROMO001" || product.productCode === "SFVRCHROMO001"
        || product.productId === "SFMUSIC001" || product.productCode === "SFMUSIC001"
        || product.productId === "BCA001" || product.productCode === "BCA001"
        || product.productId === "SFBODYTONING001" || product.productCode === "SFBODYTONING001"
        || product.productId === "SFMASSAGE001" || product.productCode === "SFMASSAGE001"
        || product.productId === "SF_INCENTER_GROUP_CLASS" || product.productCode === "SF_INCENTER_GROUP_CLASS"
    }

    public static isSugarfitWellnessAtCenterProductCode(productCode: string) {
        return productCode === "SFVRCHROMO001"
        || productCode === "SFMUSIC001"
        || productCode === "BCA001"
        || productCode === "SFBODYTONING001"
        || productCode === "SFMASSAGE001"
        || productCode === "SF_INCENTER_GROUP_CLASS"
    }

    public static isCenterIdSkipableProduct(product: ConsultationProduct): boolean {
        return (
            CareUtil.isLivePTDoctorType(product.doctorType) ||
            CareUtil.isLiveSGTDoctorType(product.doctorType) ||
            CareUtil.isGP99DoctorType(product.doctorType) ||
            CareUtil.isAnxietyTherapyDoctorType(product.doctorType) ||
            CareUtil.isTransformDoctorType(product.doctorType) ||
            CareUtil.isSugarfitExperienceCenterDiagnosticProduct(product) ||
            CareUtil.isSugarfitPyschologyProduct(product)
        )
    }

    public static isGP99DoctorType(doctorType: string): boolean {
        return doctorType === "GP_STANDARD"
    }

    public static getDoctorDetailWidget(doctor: Doctor): DoctorDetailWidget {
        return {
            widgetType: "DOCTOR_DETAIL_WIDGET",
            displayImage: doctor.displayImage,
            name: doctor.name,
            qualification: CareUtil.getFormatDetailedNameFromDoctorType(doctor.primarySubServiceType.code),
            experience: doctor.experience ? `${doctor.experience} ${pluralizeStringIfRequired("year", doctor.experience)} of experience` : undefined,
            id: doctor.id,
            action: {
                actionType: "SHOW_DOCTOR_DETAILS_MODAL_V2",
                meta: {
                    image: doctor.displayImage,
                    title: doctor.name,
                    subtitles: [
                        CareUtil.getFormatDetailedNameFromDoctorType(doctor.primarySubServiceType.code),
                        doctor.qualification,
                        doctor.experience ? `${doctor.experience} ${pluralizeStringIfRequired("year", doctor.experience)} of experience` : ""
                    ],
                    richDescriptions: CareUtil.getLivePTTrainerRichDescriptions(doctor.recommendationAffinity),
                    description: doctor.description
                }
            }
        }
    }

    public static getDoctorDetailWidgetV3(doctor: Doctor): DoctorDetailWidget {
        return {
            widgetType: "DOCTOR_DETAIL_WIDGET_V3",
            displayImage: doctor.displayImage,
            name: doctor.name,
            experience: doctor.experience ? `${doctor.experience} ${pluralizeStringIfRequired("year", doctor.experience)} of experience` : undefined,
            id: doctor.id,
            action: {
                actionType: "SHOW_DOCTOR_DETAILS_MODAL_V2",
                meta: {
                    image: doctor.displayImage,
                    title: doctor.name,
                    subtitles: [
                        CareUtil.getFormatDetailedNameFromDoctorType(doctor.primarySubServiceType.code),
                        doctor.qualification,
                        doctor.experience ? `${doctor.experience} ${pluralizeStringIfRequired("year", doctor.experience)} of experience` : ""
                    ],
                    richDescriptions: CareUtil.getLivePTTrainerRichDescriptions(doctor.recommendationAffinity),
                    description: doctor.description
                }
            }
        }
    }

    public static getWorkoutDetailWidget(tz: Timezone, consultation: ConsultationOrderResponse): WorkoutSelectionListWidget {
        const date: string = `${TimeUtil.formatEpochInTimeZone(tz, consultation.startTime, "ddd MMM DD")}`
        const time: string = `${TimeUtil.formatEpochInTimeZone(tz, consultation.startTime, "h:mm a")} - ${TimeUtil.formatEpochInTimeZone(tz, consultation.endTime, "h:mm a")}`
        const workoutDetail: WorkoutSelectionListWidget = {
            widgetType: "WORKOUT_SELECTION_LIST_WIDGET",
            title: consultation.consultationProduct.productSpecs.cultWorkoutName,
            image: consultation.consultationProduct.productSpecs.workoutImageUrl,
            date: date,
            timeSlot: time,
            workouts: [],
            defaultFocusAreaId: "",
            workoutContainerStyle: {
                marginTop: 25,
                marginBottom: 25
            }
        }
        return workoutDetail
    }

    public static getSlotTimerWidget(): SlotTimerWidget {
        return {
            widgetType: "SLOT_TIMER_WIDGET",
            countdownTime: 7,
            header: "SLOT EXPIRES IN",
            slotExpired: "SLOT EXPIRED",
            meta: {
                title: "SLOT EXPIRED",
                expiryText: "Your current slot has expired. Request you to select a new slot to proceed",
                ctaText: "SELECT NEW SLOT",
            }
        }
    }

    public static getFormatNameFromDoctorType(doctorType: DOCTOR_TYPE | string): string {
        switch (doctorType) {
            case "LIVE_PERSONAL_TRAINER_SnC":
                return "S&C"
            case "LIVE_PERSONAL_TRAINER_YOGA":
                return "Yoga"
            case "LIVE_PERSONAL_TRAINER_BOXING":
                return "Boxing"
            case "LIVE_PERSONAL_TRAINER_DF":
                return "Dance Fitness"
        }
    }
    public static getFormatIconFromDoctorType(doctorType: DOCTOR_TYPE): string {
        switch (doctorType) {
            case "LIVE_PERSONAL_TRAINER_SnC":
                return "/image/icons/cult/snc.png"
            case "LIVE_PERSONAL_TRAINER_YOGA":
                return "/image/icons/cult/yoga.png"
            case "LIVE_PERSONAL_TRAINER_BOXING":
                return "/image/icons/cult/boxing.png"
            case "LIVE_PERSONAL_TRAINER_DF":
                return "/image/icons/cult/dance_fitness.png"
            default:
                return "/image/icons/cult/snc.png"
        }
    }


    public static getFormatDetailedNameFromDoctorType(doctorType: DOCTOR_TYPE | string): string {
        switch (doctorType) {
            case "LIVE_PERSONAL_TRAINER_SnC":
                return "Strength and conditioning"
            default:
                return this.getFormatNameFromDoctorType(doctorType)
        }
    }
    public static getCareHowToJoinVideoCallWidgetId(): string {
        const STAGE_WIDGET_ID = "d9aca12f-6d17-46cb-953a-27c4c1633580"
        const PROD_WIDGET_ID = "3c87d29e-d8e2-43c7-9e0e-a80d54ec3f34"
        return process.env.ENVIRONMENT === "PRODUCTION" || process.env.ENVIRONMENT === "ALPHA" ? PROD_WIDGET_ID : STAGE_WIDGET_ID
    }

    public static async getBannerCarouselWidget(userContext: UserContext, interfaces: IServiceInterfaces, bannerWidgetId: string) {
        return this.getVmWidget(userContext, interfaces, bannerWidgetId, "BANNER_CAROUSEL_WIDGET")
    }

    public static async getVmWidget(userContext: UserContext, interfaces: CFServiceInterfaces | IServiceInterfaces, widgetId: string, widgetType: string) {
        let widget: WidgetWithMetric
        if (widgetId) {
            const widgetResponse = await interfaces.widgetBuilder.buildWidgets([widgetId], interfaces, userContext, {}, undefined)
            if (!_.isEmpty(widgetResponse) && !_.isEmpty(widgetResponse.widgets) && Array.isArray(widgetResponse.widgets)) {
                widget = !_.isEmpty(widgetResponse.widgets[0]) && widgetResponse.widgets[0].widgetType === widgetType ? widgetResponse.widgets[0] : undefined
            }
        }
        return widget
    }
    public static videoChangeChannelAction(
        bookingDetail: BookingDetail,
        isVideoChangeEnabled: boolean,
        disabled: boolean = false
    ): Action {
        if (!isVideoChangeEnabled) {
            return { ...this.showErrorToast("Sorry, you cannot change mode of consultation right now."), disabled }
        }
        return {
            actionType: "UPDATE_CONSULTATION_MODE",
            disabled,
            meta: {
                appointmentId: bookingDetail.consultationOrderResponse.id,
                param: {
                    consultationChannel: "VIDEO",
                }
            },
            analyticsData: {
                eventKey: "widget_click",
                eventData: {
                    actionType: "UPDATE_CONSULTATION_MODE",
                    productCode: bookingDetail.booking.productCode,
                    oldConsultationMode: bookingDetail.consultationOrderResponse.channel,
                    newConsultationMode: "VIDEO"
                }
            },
        }
    }

    public static audioChannelUpdateNumberAction(
        title: string,
        isAudioChangeEnabled: boolean,
        bookingDetail: BookingDetail,
        audioModeName: string,
        isEditFlow: boolean,
        disabled: boolean = false
    ): Action {
        // is Edit flow will be only true if audio enabled
        if (isEditFlow) {
            return {
                actionType: "SHOW_UPDATE_CONSULTATION_MODE_MODAL",
                disabled,
                title: title || "EDIT NUMBER",
                meta: {
                    title: "UPDATE PHONE NUMBER",
                    phoneNo: audioModeName,
                    ctaText: "SAVE NUMBER",
                    action: {
                        actionType: "UPDATE_CONSULTATION_MODE",
                        meta: {
                            appointmentId: bookingDetail.consultationOrderResponse.id,
                            param: {
                                consultationChannel: "AUDIO",
                            }
                        },
                        analyticsData: {
                            eventKey: "widget_click",
                            eventData: {
                                actionType: "UPDATE_CONSULTATION_MODE",
                                productCode: bookingDetail.booking.productCode,
                                oldConsultationMode: bookingDetail.consultationOrderResponse.channel,
                                newConsultationMode: "AUDIO",
                                isEditNumberFlow: "true"
                            }
                        },
                    }
                }
            }
        }
        if (!isAudioChangeEnabled) {
            return { ...this.showErrorToast("Sorry, you cannot change mode of consultation right now."), disabled }
        }
        return {
            actionType: "UPDATE_CONSULTATION_MODE",
            disabled,
            meta: {
                appointmentId: bookingDetail.consultationOrderResponse.id,
                param: {
                    consultationChannel: "AUDIO",
                    phoneNo: audioModeName
                }
            },
            analyticsData: {
                eventKey: "widget_click",
                eventData: {
                    actionType: "UPDATE_CONSULTATION_MODE",
                    productCode: bookingDetail.booking.productCode,
                    oldConsultationMode: bookingDetail.consultationOrderResponse.channel,
                    newConsultationMode: "AUDIO",
                    isEditNumberFlow: "false"
                }
            },
        }

    }

    public static getVideoJoinCallAction(
        userContext: UserContext,
        patientsList: Patient[],
        user: User,
        product: ConsultationProduct,
        bookingDetail: BookingDetail,
        consultationInstruction: ConsultationInstructionResponse[],
        pageConfig: TeleconsultationDetailsPageConfig,
        logger?: Logger
    ): Action[] {
        if (CareUtil.isSupportGroupProduct(product)) {
            const status = bookingDetail.consultationOrderResponse.status
            if (status === "COMPLETED" || status === "CUSTOMER_MISSED") {
                return undefined
            }
            const zoomLink = bookingDetail.consultationOrderResponse.patientZoomLink
            if (!zoomLink) {
                return undefined
            }
            const isActionDisabled = new Date().getTime() < bookingDetail.consultationOrderResponse.startTime - 900000
                    || new Date().getTime() > bookingDetail.consultationOrderResponse.endTime

            return [{
                title: "JOIN SESSION",
                subtitle: isActionDisabled ? "Link will activate 15 mins before session" : undefined,
                url: zoomLink,
                actionType: AppUtil.isWeb(userContext) ? "EXTERNAL_DEEP_LINK" : "OPEN_WEBPAGE",
                disabled: isActionDisabled,
                isEnabled: !isActionDisabled
            }]
        }
        const videoCallJoinAction = this.getVideoCallJoinActionWithParams(
            userContext,
            patientsList,
            user,
            product,
            bookingDetail.consultationOrderResponse.doctor,
            bookingDetail.consultationOrderResponse.startTime,
            bookingDetail.booking.id,
            bookingDetail.consultationOrderResponse.id,
            bookingDetail.consultationOrderResponse.patient.id,
            CareUtil.getVideoChannel(bookingDetail, logger),
            CareUtil.getChatChannel(bookingDetail),
            consultationInstruction,
            CareUtil.getConsulationInstructions(bookingDetail, pageConfig),
            bookingDetail.consultationOrderResponse?.secondaryPatientsResponse
        ) as Action[]

        logger && logger?.info(`Video Call Join Action For BookingId ${bookingDetail.booking.id}: ${JSON.stringify(videoCallJoinAction)}`)

        return videoCallJoinAction
    }

    public static getVideoCallJoinActionWithParams(
        userContext: UserContext,
        patientsList: Patient[],
        user: User,
        product: ConsultationProduct,
        doctor: Doctor,
        startTime: number,
        bookingId: number,
        appointmentId: number,
        patientId: number,
        videoChannel: string,
        chatChannel: string,
        consultationInstruction: ConsultationInstructionResponse[],
        oldInstruction: any,
        secondaryPatientsResponse?: BookingDetail["consultationOrderResponse"]["secondaryPatientsResponse"]
    ): Action[] {
        const currentTime: number = TimeUtil.getCurrentEpoch()
        const isFromFlutterAppFlow = AppUtil.isFromFlutterAppFlow(userContext)
        const timeRemaininginMilliSeconds = startTime - currentTime
        const timeRemaininginSeconds = isNaN(timeRemaininginMilliSeconds) || timeRemaininginMilliSeconds < 0 ? 0 : timeRemaininginMilliSeconds / 1000

        if (isFromFlutterAppFlow) {
            return [{
                actionType: "TC_JOIN_CALL",
                title: "JOIN SESSION",
                url: `curefit://videochat?pageFrom=productpage&appointmentId=${appointmentId}&productId=${product.productId}&bookingId=${bookingId}&identity=Patient-${patientId}&docName=${doctor.name}&doctorId=${doctor.id}&channel=${videoChannel}&chatChannel=${chatChannel}&timeRemaining=${timeRemaininginSeconds}&enableLog=${CareUtil.isVideoLogEnabled()}&docImage=${doctor.displayImage}&userImage=${user.profilePictureUrl}`
            }]
        }
        const action: Action = {
            title: "Join Video Call",
            actionType: "SHOW_CARE_INSTRUCTION_MODAL",
            meta: {
                header: {
                    title: !AppUtil.isWeb(userContext) ? " " : "Instructions"
                },
                instructions: oldInstruction,
                instructionMap: consultationInstruction,
                action: {
                    actionType: "TC_JOIN_CALL",
                    title: "JOIN SESSION",
                    url: `curefit://videochat?pageFrom=productpage&appointmentId=${appointmentId}&productId=${product.productId}&bookingId=${bookingId}&identity=Patient-${patientId}&docName=${doctor.name}&doctorId=${doctor.id}&channel=${videoChannel}&chatChannel=${chatChannel}&timeRemaining=${timeRemaininginSeconds}&enableLog=${CareUtil.isVideoLogEnabled()}&docImage=${doctor.displayImage}&userImage=${user.profilePictureUrl}`
                },
                showBookLater: false
            },
        }
        // To do remove it when launching couple therapist in web
        if (!AppUtil.isWeb(userContext) && !AppUtil.isFromFlutterAppFlow(userContext) && CareUtil.isCoupleTherapist(product.doctorType) && !_.get(secondaryPatientsResponse, "0")) {
            return [{
                ...CareUtil.getPartnerPatientSelectionModalAction(appointmentId, patientsList, action, "Join Video Call"),
                title: "Join Video Call",
                icon: "JOIN_CALL",
                actionId: "JOIN_CALL",
            }]
        }
        return [action]
    }

    public static getAudioModeName(bookingDetail: BookingDetail, user: User): string {
        const actionWithContext: ActionWithContext = _.get(bookingDetail, "consultationOrderResponse.appointmentActionsWithContext.audioActionWithContext")
        let audioModeName
        if (!_.isEmpty(actionWithContext) && !_.isEmpty(actionWithContext.context)) {
            audioModeName = actionWithContext.context.twilioCommunicationMode.modeName
        }
        return audioModeName || user.phone
    }

    public static isAudioBookingConsultation(bookingDetail: BookingDetail) {
        return _.get(bookingDetail, "consultationOrderResponse.channel") === "AUDIO"
    }

    public static isAudioBookingFromConsultation(consultation: Consultation | ConsultationOrderResponse) {
        return _.get(consultation, "channel") === "AUDIO"
    }

    public static isVideoBookingFromConsultation(consultation: Consultation | ConsultationOrderResponse) {
        return _.get(consultation, "channel") === "VIDEO"
    }

    public static isVideoBookingConsultation(bookingDetail: BookingDetail): boolean {
        return _.get(bookingDetail, "consultationOrderResponse.channel") === "VIDEO"
    }

    public static showErrorToast(text: string, meta?: {
        duration: number;
        position: number;
        textColor?: string;
        backgroundColor?: string;
    }): Action {
        return {
            actionType: "SHOW_ERROR_TOAST",
            meta: {
                data: text || "Something went wrong",
                duration: 3500,
                position: 1,
                ...(meta || {})

            }
        }
    }


    public static getConsultationModeImageUrl(isSelected: boolean, isFromFlutterApp: boolean): string {
        if (isFromFlutterApp) {
            return isSelected ? "image/icons/consultationInfoWidget/selected_white_radio.png" : "image/icons/consultationInfoWidget/unSelected_white_radio.png"
        }
        return isSelected ? "image/icons/consultationInfoWidget/radio-button-selected.png" : "image/icons/consultationInfoWidget/radio-button.png"
    }

    public static getConsultationModeWidget(
        userContext: UserContext,
        patientsList: Patient[],
        user: User,
        product: ConsultationProduct,
        bookingDetail: BookingDetail,
        consultationInstruction: ConsultationInstructionResponse[],
        pageConfig: TeleconsultationDetailsPageConfig,
        disableJoinNow?: boolean,
        isFromConfirmation?: boolean
    ): WidgetView {
        const isIncenterConsultation = _.get(bookingDetail, "consultationOrderResponse.consultationProduct.consultationMode") !== "ONLINE"
        const bookingAllowedChannels = _.get(bookingDetail, "consultationOrderResponse.consultationProduct.productSpecs.allowed_channels", [])
        const isBothAudioAndVideoSupported = bookingAllowedChannels.includes("AUDIO") && bookingAllowedChannels.includes("VIDEO")
        if (isIncenterConsultation || !isBothAudioAndVideoSupported) {
            return undefined
        }
        const isAudio = this.isAudioBookingConsultation(bookingDetail)
        const isVideo = this.isVideoBookingConsultation(bookingDetail)
        if (isAudio || isVideo) {
            const isAudioEnabled = CareUtil.getAudioEnabled(bookingDetail)
            const audioModeName = this.getAudioModeName(bookingDetail, user)
            const showDifferentSubTitleStyle = !isFromConfirmation && AppUtil.isTCDoctorCardUISupported(userContext)
            const isFromFlutterApp = AppUtil.isFromFlutterAppFlow(userContext)
            const items: ConsultationInfoItem[] = [
                {
                    title: "Video Consultation",
                    subtitle: isVideo ? "Join video call from your app or laptop browser" : undefined,
                    imageUrl: this.getConsultationModeImageUrl(isVideo, isFromFlutterApp),
                    isSelected: isVideo,
                    itemClickAction: this.videoChangeChannelAction(bookingDetail, isAudioEnabled, isVideo),
                    titleAction: !disableJoinNow && isVideo && CareUtil.getVideoEnabled(bookingDetail)
                        ? {
                            ...this.getVideoJoinCallAction(
                                userContext,
                                patientsList,
                                user,
                                product,
                                bookingDetail,
                                consultationInstruction,
                                pageConfig
                            )[0],
                            title: "JOIN CALL",
                            disabled: false
                        }
                        : { title: " ", disabled: true, actionType: "NAVIGATION" },
                    ...(isFromFlutterApp
                        ? {}
                        : {
                            itemContainerStyle: isFromConfirmation ? {
                                flexDirection: "row-reverse",
                                alignItems: isVideo ? "flex-start" : "center",
                                paddingBottom: isAudio ? 5 : undefined,
                            } : undefined,
                            imageStyle: isFromConfirmation ? {
                                marginLeft: 11,
                                marginRight: 0
                            } : undefined,
                            titleStyle: isFromConfirmation ? {
                                fontFamily: AppFont.Medium,
                            } : undefined,
                            subtitleStyle: showDifferentSubTitleStyle ? {
                                color: "#888e9e",
                                fontFamily: AppFont.Regular,
                                fontSize: 12
                            } : {}
                        })
                },
                {
                    title: "Phone Consultation",
                    subtitle: isAudio && audioModeName ? `You will receive a phone call on +91 - ${audioModeName}` : undefined,
                    imageUrl: this.getConsultationModeImageUrl(isAudio, isFromFlutterApp),
                    isSelected: isAudio,
                    itemClickAction: this.audioChannelUpdateNumberAction(undefined, isAudioEnabled, bookingDetail, audioModeName, audioModeName.length !== 10, isAudio),
                    titleAction: isAudio && isAudioEnabled
                        ? this.audioChannelUpdateNumberAction(undefined, isAudioEnabled, bookingDetail, audioModeName, true, false)
                        : { title: " ", disabled: true, actionType: "NAVIGATION" },
                    ...(isFromFlutterApp ? {} : {
                        itemContainerStyle: isFromConfirmation ? {
                            flexDirection: "row-reverse",
                            alignItems: isAudio ? "flex-start" : "center",
                            paddingBottom: 5,
                        } : (isAudio && audioModeName ? {} : { paddingBottom: 6 }),
                        imageStyle: isFromConfirmation ? {
                            marginLeft: 11,
                            marginRight: 0
                        } : undefined,
                        titleStyle: isFromConfirmation ? {
                            fontFamily: AppFont.Medium,
                        } : undefined,
                        subtitleStyle: showDifferentSubTitleStyle ? {
                            color: "#888e9e",
                            fontFamily: AppFont.Regular,
                            fontSize: 12
                        } : {}
                    })
                }
            ]
            return {
                ...new ConsultationInfoWidget(items, "Mode of consultation", undefined, isFromConfirmation ? { marginTop: 24, marginBottom: 20, paddingBottom: 4, paddingTop: 25, paddingLeft: 25, paddingRight: 25 } : { marginTop: 11, marginBottom: 11 }, isFromConfirmation ? { fontSize: 18 } : {}),
                hasDividerBelow: false
            }
        }
        return undefined
    }

    public static showHowToVideoCallBanner(userContext: UserContext, product: ConsultationProduct, bookingDetail: BookingDetail) {
        return (
            userContext.sessionInfo.userAgent === "APP" &&
            _.get(bookingDetail, "consultationOrderResponse.consultationProduct.consultationMode") === "ONLINE" &&
            _.get(bookingDetail, "consultationOrderResponse.channel") !== "AUDIO" &&
            CareUtil.isPending(bookingDetail) &&
            !this.isLiveWorkoutConsultationDoctorType(product.doctorType)
        )
    }

    public static getCityAvailabilityForPrescriptionDiagTests(cityId: string): boolean {
        const cities = ["Bangalore", "Ahmedabad", "Gurgaon", "Ludhiana", "Hyderabad", "Mumbai", "Pune", "Nagpur", "Kanpur", "Lucknow", "Vadodara", "Chandigarh", "Amritsar", "Jaipur", "Chennai", "Visakhapatnam"]
        return cities.indexOf(cityId) !== -1
    }

    public static getVerticalForConsultation(doctorType: DOCTOR_TYPE) {
        if (CareUtil.isTransformDoctorType(doctorType)) {
            return "TRANSFORM"
        }
        if (CareUtil.isLiveWorkoutConsultationDoctorType(doctorType) || CareUtil.isPTDoctorType(doctorType)) {
            return "CULTFIT"
        }
        if (CareUtil.isMindDoctorType(doctorType)) {
            return "MINDFIT"
        }
        return "CAREFIT"
    }

    public static getVerticalForBundle(subCategoryCode: SUB_CATEGORY_CODE) {
        if (subCategoryCode === "LIVE_PERSONAL_TRAINING" || subCategoryCode === "LIVE_SGT" || subCategoryCode === "PERSONAL_TRAINING") {
            return "CULTFIT"
        }
        if (subCategoryCode === "MIND_THERAPY") {
            return "MINDFIT"
        }
        return "CAREFIT"
    }

    public static getLivePTSessionNoteWidget(): DescriptionWidget {
        const infoCard: InfoCard = {
            title: "Note:",
            subTitle: "Video link for the session will be enabled right before the session start time."
        }
        const widget = new DescriptionWidget([infoCard])
        return widget
    }

    public static getConsultationMRNWidget(
        userContext: UserContext,
        bookingDetail: BookingDetail,
        mrnFormConfigResponse: MRNFormResponse
    ): WidgetView {
        if (userContext.sessionInfo.appVersion < 8.29) {
            return undefined
        }
        if (!CareUtil.isPending(bookingDetail)) {
            return undefined
        }
        if (mrnFormConfigResponse &&
            mrnFormConfigResponse.configName === "MRN_FormAction" &&
            mrnFormConfigResponse.configValue
        ) {
            const action = {
                actionType: "NAVIGATION",
                disabled: false,
                url: `curefit://userform?formId=${mrnFormConfigResponse.configValue}&bookingId=${bookingDetail.booking.id}`,
                icon: "RIGHT_ARROW_WHITE",
                analyticsData: {
                    eventKey: "widget_click",
                    eventData: {
                        actionType: "MRN_REGISTER_CLICK",
                        centerId: bookingDetail.consultationOrderResponse.center.id,
                        productCode: bookingDetail.booking.productCode
                    }
                }
            } as unknown as Action
            if (AppUtil.isTCDoctorCardUISupported(userContext)) {
                return {
                    widgetType: "CARE_CHECKOUT_INFO_ITEM_WIDGET",
                    items: [
                        {
                            title: "Register with the hospital",
                            subTitle: "Link hospital account using your MRN",
                            imageType: "SMALL",
                            imageUrl: "image/icons/care_checkout_info_item/mrn.png",
                            showArrow: true,
                            titleStyleType: "MEDIUM",
                            subTitleStyleType: "REGULAR_GRAY",
                            action: action,
                            isCommonImageSpacing: true
                        }
                    ],
                    hasDividerBelow: false
                } as CareCheckoutInfoItemWidget
            }
            return {
                ...new ConsultationInfoWidget(
                    [
                        {
                            title: "REGISTER WITH THE HOSPITAL",
                            subtitle: "Link hospital account using your MRN",
                            imageUrl: "/image/icons/hospitalNew.png",
                            isSelected: true,
                            titleAction: action,
                            itemClickAction: action,
                            subtitleStyle: ConsultationInfoSubTitleStyle,
                            titleStyle: ConsultationInfoTitleStyle,
                        }
                    ],
                    undefined,
                    undefined,
                    ConsultationInfoSingleItemWidgetContainerStyle
                ),
                hasDividerBelow: false
            }
        }
        return undefined
    }

    public static getCareConsultationSubType(product: ConsultationProduct) {
        if (CareUtil.isLivePTProduct(product)) {
            return "LIVE_PT_SESSION"
        }
        if (CareUtil.isTherapyProduct(product)) {
            return "THERAPY_CONSULTATION"
        }
        if (CareUtil.isNutritionistDoctor(product.doctorType)) {
            return "NUTRITIONIST_CONSULTATION"
        }
        return "CARE_CONSULTATION"
    }

    public static getLivePTTrainerRichDescriptions(recommendationAffinity: DoctorRecommendationAffinity): [{
        text: string
        color: string
    }] {
        let textColor
        if (!_.isEmpty(recommendationAffinity) && recommendationAffinity.value) {
            switch (recommendationAffinity.type) {
                case "CULT_CLASS":
                case "MIND_CLASS":
                    textColor = "#3888ff"
                    break
                case "LIVE_PT_CLASS":
                    textColor = "#ffa300"
                    break
                default:
                    textColor = "#3888ff"
            }
            return [{
                text: recommendationAffinity.value,
                color: textColor
            }]
        }
    }

    public static getFulfilledDetails(sellerDetails: any, isPrescriptionTests?: boolean) {
        if (sellerDetails) {
            const isExternal = sellerDetails.seller === "HEALTHIANS"
            const title = isExternal ? "FULFILLED BY HEALTHIANS PARTNER LAB" : "FULFILLED BY CAREFIT"
            const subTitle = sellerDetails.nabl_certified ? "NABL Certified Lab" : undefined
            const leftColor = isExternal ? "#5db082" : (isPrescriptionTests ? "#63b1fe" : "#de4d8a")
            const rightColor = isExternal ? ["#5db082", "#5db082"] : ["#c16098", "#57acff"]
            return { title, subTitle, isExternal, leftColor, rightColor }
        } else {
            return undefined
        }
    }

    public static getFullfilledDetailsV2(userContext: UserContext, sellerDetails: any): IFullFilledDetailsV2 | Action {
        if (sellerDetails) {
            const title = "Fulfilled By",
                certifications = ["DMLT and WHO certified phlebotomists", "46 touch-point technology for assured quality collection & testing"],
                isExternal = sellerDetails.seller === "HEALTHIANS",
                imageUrl = isExternal ? CdnUtil.getCdnUrl("curefit-content/image/carefit/healthians_logo.png") : CdnUtil.getCdnUrl("curefit-content/image/carefit/carefit-logo.png"),
                meta = {
                    imageUrl,
                    description: "Healthians is India’s largest provider of health test at-home service, creating a new benchmark in quality. 600+ trained technicians. 46 touch-point technology for assured quality collection & testing. DMLT & WHO certified phlebotomists",
                    sellerSteps: [{ image: CdnUtil.getCdnUrl("curefit-content/image/carefit/diagnostics/bloodsample.png"), item: "Sample collected by and processed by Healthians and its partner labs" },
                    { image: CdnUtil.getCdnUrl("curefit-content/image/carefit/digital.png"), item: "Schedule, track and view your reports on the care.fit app" }],
                    aboutTitle: "ABOUT",
                }

            if (AppUtil.isWeb(userContext)) {
                return {
                    title,
                    imageUrl,
                    certifications,
                    isExternal,
                    action: {
                        actionType: "SHOW_CARE_FULLFILLED_SELLER_MODAL",
                        title: "Know More",
                        meta
                    }
                } as IFullFilledDetailsV2
            }

            return {
                actionType: "SHOW_CARE_FULLFILLED_SELLER_MODAL",
                meta: {
                    ...meta,
                    backgroundColor: "ffffff",
                    borderColor: "#f4f4f4",
                    productPageItems: ["DMLT and WHO certified phlebotomists", "46 touch-point technology for assured quality collection & testing"],
                    productPageCtaTitle: "Know More",
                    title: "Fulfilled By"
                }
            }
        } else {
            return undefined
        }
    }

    public static getDiagnosticsServiceability() {
        const diagnosticServiceability = {
            title: "Check Serviceability",
            initialCta: "CHECK SERVICEABILITY",
            finalCta: "DONE",
            available: "Your location is serviceable. You can continue with your booking",
            availableHeader: "Home Sample Collection Available",
            notAvailable: "Home Sample collection is not available in your area as of now.",
            notAvailableHeader: "Sorry! Apologies for the Inconvenience",
            productPageText: "Due to COVID-19 restrictions, services in certain localities may be paused. Enter your pincode to check serviceability.",
            productPageCTA: "CHECK WITH PINCODE",
            productPageHeader: "DISCLAIMER",
        }
        return diagnosticServiceability
    }

    public static getCareReferalBannerWidgetId(): string {
        const STAGE_WIDGET_ID = "ff3d7c55-eaaa-443e-9fbe-663d1428eb15"
        const PROD_WIDGET_ID = "fce399d9-54d0-43d3-ac5f-e4bab6d6b1c6"
        return process.env.ENVIRONMENT === "PRODUCTION" || process.env.ENVIRONMENT === "ALPHA" ? PROD_WIDGET_ID : STAGE_WIDGET_ID
    }

    public static isCultTransferCareProducts(productId: string) {
        return ["CULT_MEMBERSHIP_TRANSFER_GP99", "CULT_MEMBERSHIP_TRANSFER_SPECIALITIES"].includes(productId)
    }

    public static getLivePTPackHowItWorksWidget(userContext: UserContext): ProductListWidget {
        const header: Header = {
            title: "Let's Begin",
            color: "#000000"
        }
        const infoCards: InfoCard[] = [
            {
                icon: "/image/icons/livept/goal_journey.png",
                subTitle: "Set/Change your fitness goal and get daily workout recommendations."
            },
            {
                icon: "/image/icons/livept/wide_range.png",
                subTitle: "Choose from a wide range of available workout formats."
            },
            {
                icon: "/image/icons/livept/trainer.png",
                subTitle: "Get matched with the best of cult trainers based on format and trainer availability."
            },
            {
                icon: "/image/icons/livept/level_up.png",
                subTitle: "Achieve your goals with regular sessions and assessments."
            }
        ]
        const widget = new ProductListWidget("SMALL", header, infoCards)
        widget.orientation = "RIGHT"
        return widget
    }

    public static getLiveSGTPackHowItWorksWidget(userContext: UserContext): ProductListWidget {
        const header: Header = {
            title: "Let's Begin",
            color: "#000000"
        }
        const infoCards: InfoCard[] = [
            {
                icon: "/image/icons/livept/wide_range.png",
                subTitle: "Choose from a wide range of available workout formats."
            },
            {
                icon: "/image/icons/livept/goal_journey.png",
                subTitle: "Set/Change your fitness goal and get daily workout recommendations."
            },
            {
                icon: "/image/icons/howItWorks/group_workout.png",
                subTitle: "Enjoy an awesome At-Home group workout experience"
            }
        ]
        const widget = new ProductListWidget("SMALL", header, infoCards)
        widget.orientation = "RIGHT"
        return widget
    }

    public static isNewPhysioMindTherapyProductPageSupported(userContext: UserContext, subCategoryCode: SUB_CATEGORY_CODE) {
        return ["PHYSIOTHERAPY", "MIND_THERAPY"].includes(subCategoryCode) && userContext.sessionInfo.appVersion >= 8.29
    }

    public static isMindTherapyPageWeb(userContext: UserContext, subCategoryCode: SUB_CATEGORY_CODE | MembershipProductType) {
        return ["MIND_THERAPY"].includes(subCategoryCode) && AppUtil.isWeb(userContext)
    }

    public static getWhyPackWidget(whatsInPackItem: ManagedPlanPackInfo, userAgent?: UserAgentType, title?: string): ProductListWidget {
        if (_.isEmpty(whatsInPackItem)) {
            return undefined
        }
        const header: Header = {
            title: title ? title : whatsInPackItem.title,
            color: "#000000"
        }
        const cards: GradientCard[] = []
        whatsInPackItem.children.forEach(item => {
            cards.push({
                title: item.title,
                subTitle: item.desc,
                shadowColor: item.shadowColor,
                gradientColors: item.gradientColors,
                icon: item.type || item.icon
            })
        })
        const widget = new ProductListWidget("GARDIENT_CARD", header, cards)
        widget.hasDividerBelow = false
        if (userAgent === "DESKTOP") {
            widget.orientation = "RIGHT"
        }
        return widget
    }

    public static addedOfferingsWidget(): ProductListWidget {
        const header: Header = {
            title: "How It Works"
        }
        const infoCards: InfoCard[] = []
        infoCards.push({
            subTitle: "Personal Transform Coach",
            icon: ""
        })
        infoCards.push({
            subTitle: "Mini Habit Courses",
            icon: ""
        })
        infoCards.push({
            subTitle: "Unlimited Cult.live Content",
            icon: ""
        })
        const widget = new ProductListWidget("SMALL", header, infoCards)
        return widget
    }

    public static getHowItWorksWidget(howItWorksItem: ManagedPlanPackInfo, userAgent?: UserAgentType, disableOrientation?: boolean, layoutProps?: any): ProductListWidget {
        if (_.isEmpty(howItWorksItem)) {
            return undefined
        }
        const header: Header = howItWorksItem?.title ? {
            title: howItWorksItem?.title,
            style: {
                fontSize: 18,
            },
            color: "#000000"
        } : undefined
        const infoCards: InfoCard[] = []
        howItWorksItem.children.forEach(item => {
            item.desc = item.desc.replace("${DEGREE}", "\u00b0")
            infoCards.push({
                subTitle: item.desc,
                icon: item.imageUrl
            })
        })
        const widget = new ProductListWidget("SMALL", header, infoCards)
        widget.hasDividerBelow = false
        if (userAgent === "DESKTOP" && !disableOrientation) {
            widget.orientation = "RIGHT"
        }
        widget.layoutProps = layoutProps
        return widget
    }

    public static getBillingAddress(orientation?: Orientation, liveClassFlow: boolean = false, startDateTimeInfo = {}): BillingAddressWidget {
        const billingPlaceholderInfo = {
            header: "Enter Billing address",
            subheader: "Your address",
            icon: "/image/icons/gymfit/location.png",
            chevronIcon: "/image/icons/gymfit/chevron_down.png",
            liveClassFlow
        }
        const startDateWidgetInfo = {
            header: "Starts on",
            subheader: "Pick start date",
            icon: "/image/icons/gymfit/schedule_confirm.png",
            chevronIcon: "/image/icons/gymfit/chevron_down.png",
            startDateTimeInfo,
        }
        const widget = new BillingAddressWidget(billingPlaceholderInfo)
        widget.orientation = orientation
        widget.startDateInfo = !liveClassFlow ? startDateWidgetInfo : {}
        return widget
    }

    public static getStartDateInfo(startDateInfo: any) {
        return new StartDateWidget(startDateInfo)
    }

    public static getHowItHelpsWidget(howItHelps: ManagedPlanPackInfo): WidgetView {
        if (_.isEmpty(howItHelps)) {
            return undefined
        }
        const data: any[] = []
        howItHelps.children.forEach(item => {
            data.push({
                title: item.title,
                description: item.desc,
                image: item.imageUrl,
                contentMetric: {
                    contentId: item.title
                }
            })
        })
        return {
            widgetType: "HOW_IT_HELPS_WIDGET",
            showDivider: false,
            dividerType: "NONE",
            header: {
                title: howItHelps?.title,
                color: "#000000",
                titleStyle: {
                    fontSize: 20,
                }
            },
            data,
            action: {
                actionType: "SHOW_DIAGNOSTICS_TEST_DETAIL_LIST"
            },
            hasDividerBelow: false
        }
    }


    public static getWhatYouGetInstructionWidget(whatYouGet: ManagedPlanPackInfo, userContext: UserContext, orientation?: Orientation): WidgetView {
        if (_.isEmpty(whatYouGet)) {
            return undefined
        }
        const widget: InstructionsWidget = new InstructionsWidget([], userContext.sessionInfo.userAgent, true, [{
            title: whatYouGet?.title || "What you get",
            priority: 0,
            instructionResponseList: (whatYouGet?.children || []).map((item: { desc: string; imageUrl: string; }) => {
                return {
                    text: item.desc,
                    iconURL: item.imageUrl
                }
            })
        }])
        widget.blurEnabled = false
        widget.orientation = orientation
        return widget
    }

    public static getCareProductBenefitWidget(userContext: UserContext, packBenefit: ManagedPlanPackInfo): ProductGridWidget | ProductListWidget | ProductBenefitWidget {
        if (!_.isEmpty(packBenefit)) {
            if (userContext.sessionInfo.appVersion >= 8.26 && userContext.sessionInfo.userAgent === "APP") {
                return {
                    widgetType: "PRODUCT_BENEFIT_WIDGET",
                    header: { title: packBenefit.title || "Key Benefits", titleStyle: { fontSize: 18 } },
                    hasDividerBelow: false,
                    data: packBenefit.children.map(item => {
                        return {
                            title: item.title,
                            subTitle: item.desc,
                            icon: item.imageUrl
                        }
                    })
                }
            }
            return CareUtil.getWhyPackWidget(packBenefit, userContext.sessionInfo.userAgent)
        }
        return undefined
    }

    public static getFilteredDoctors(doctorsList: Doctor[], careSelectedCenter?: Center): Doctor[] {
        const isCareSelectedCenterAvailable = !_.isEmpty(careSelectedCenter)
        if (isCareSelectedCenterAvailable) {
            return doctorsList.filter((doctor: Doctor) => {
                return doctor && doctor.doctorCenterMapping && _.findIndex(doctor.doctorCenterMapping, doctorMap => {
                    return doctorMap.centerId === careSelectedCenter.id
                }) > -1
            })
        }
        return doctorsList
    }

    public static getDoctorListWidget(userContext: UserContext, title: string, doctorsList: Doctor[], centers: any, careSelectedCenter?: Center, skipDoctorCenterMappingInfo?: boolean): DoctorListWidgetV2 {
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "DESKTOP")
        const filteredDocList = CareUtil.getFilteredDoctors(doctorsList, careSelectedCenter)
        const doctors: any[] = filteredDocList.map(doctor => {
            const detail = userAgent === "APP" ? CareUtil.getDoctorDetailsApp(doctor, centers, careSelectedCenter, skipDoctorCenterMappingInfo) : CareUtil.getDoctorDetailsWeb(doctor, centers, careSelectedCenter, skipDoctorCenterMappingInfo)
            delete detail.id
            return detail
        }).filter(Boolean)
        if (_.isEmpty(doctors)) {
            return undefined
        }
        const doctorListWidget: DoctorListWidgetV2 = {
            widgetType: "DOCTOR_LIST_WIDGET_V2",
            title: title ? title : (careSelectedCenter ? "Doctors in this Clinic" : "Our Doctors"),
            doctors: doctors,
            hasDividerBelow: false
        }
        if (userAgent === "DESKTOP") {
            doctorListWidget.orientation = "RIGHT"
        }
        if (userAgent === "MBROWSER") {
            doctorListWidget.hideSepratorLines = false
        }
        return doctorListWidget
    }

    public static getDoctorDetailsWeb(doctor: Doctor, centers: any, careSelectedCenter?: Center, skipDoctorCenterMappingInfo?: boolean): any {
        if (doctor) {
            // const doctorCenterMapping = !skipDoctorCenterMappingInfo && doctor.doctorCenterMapping && doctor.doctorCenterMapping.length ? doctor.doctorCenterMapping.map((center: any) => {
            //     const centerDetails = centers[center.centerId]
            //     if (centerDetails && centerDetails.name && centerDetails.placeUrl) {
            //         return {
            //             name: centerDetails.name,
            //             placeUrl: centerDetails.placeUrl,
            //             mapText: "VIEW MAP"
            //         }
            //     }
            // }).filter((item: any) => !!item) : []
            // const length = doctorCenterMapping.length
            // const centerName = !_.isEmpty(careSelectedCenter) ? careSelectedCenter.name : (length ? doctorCenterMapping[0].name : undefined)
            CareUtil.removeDoctorUnusedField(doctor, true)
            return {
                ...doctor,
                experience: `${doctor.experience} yrs of experience`,
                doctorCenterMapping: [], // doctorCenterMapping,
                action: {
                    actionType: "SHOW_INFO_MODAL",
                    meta: {
                        type: "DOCTOR_LIST"
                    },
                },
                // footer: length ? (
                //     length > 1
                //     ? { name: (centerName + ` | +${String(length - 1)} more`) || undefined }
                //     : length === 1 ? { name: centerName } : undefined
                // ) : undefined,
                footer: undefined,
                // centerHeader: "AVAILABLE AT",
                doctorInfoText: "ABOUT"
            }
        } else {
            return undefined
        }
    }

    public static getDoctorDetailsApp(doctor: Doctor, centers: any, careSelectedCenter?: Center, skipDoctorCenterMappingInfo?: boolean): any {
        if (doctor) {
            // const doctorCenterMapping = !skipDoctorCenterMappingInfo && doctor.doctorCenterMapping && doctor.doctorCenterMapping.length ? doctor.doctorCenterMapping.map((doctorCenterMappingItem) => {
            //     const centerDetails = centers && centers[doctorCenterMappingItem.centerId]
            //     if (centerDetails && centerDetails.name && centerDetails.placeUrl) {
            //         return {
            //             name: centerDetails.name || undefined,
            //             placeUrl: centerDetails.placeUrl || undefined,
            //             mapText: "VIEW MAP"
            //         }
            //     } else {
            //         return {
            //             name: doctorCenterMappingItem.centerId,
            //             placeUrl: undefined,
            //             mapText: undefined
            //         }
            //     }
            // }).filter((item: any) => !!item) : []
            // const length = doctorCenterMapping.length
            CareUtil.removeDoctorUnusedField(doctor)
            return {
                ...doctor,
                doctorCenterMapping: [], // doctorCenterMapping,
                action: {
                    actionType: "SHOW_INFO_MODAL",
                    meta: {
                        type: "DOCTOR_LIST"
                    },
                },
                // footer: length ? (
                //     length > 1
                //         ? {
                //             name: !_.isEmpty(careSelectedCenter) ? careSelectedCenter.name : doctorCenterMapping[0].name || undefined,
                //             actionText: length > 1 ? `| +${String(length - 1)} more` : undefined
                //         }
                //         : length === 1
                //             ? {
                //                 name: !_.isEmpty(careSelectedCenter) ? careSelectedCenter.name : doctorCenterMapping[0].name || undefined,
                //                 actionText: undefined
                //             } : undefined
                // ) : undefined,
                footer: undefined,
                // centerHeader: "AVAILABLE AT",
                doctorInfoText: "ABOUT"
            }
        } else {
            return undefined
        }
    }

    static isPartOfConsultationPackProducts(subcategoryCode: SUB_CATEGORY_CODE): boolean {
        return CONSULTATION_PACK_SUBCATEGORIES.indexOf(subcategoryCode) !== -1
    }

    static isPartOfConsultationCovidPackProducts(code: string): boolean {
        return code ? ["COVID_HOME_MONITORING_SET", "COVID_HOME", "COVID-HOME"].some(v => code.includes(v)) : false
    }

    static isPartOfConsultationPackAyurvedaProducts(code: string): boolean {
        return code ? ["AYURVEDA_PACK_SET"].some(v => code.includes(v)) : false
    }

    static getConsultationPackProgressInfo(
        userContext: UserContext,
        aggregatedMembershipInfo: AggregatedMembershipInfo,
        isSessionBasedPackProgress: boolean
    ) {
        let total, completed
        const tz = userContext.userProfile.timezone
        const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDateWithTimezone(tz), tz)
        const startDateFormatted = TimeUtil.formatEpochInTimeZone(tz, Number(aggregatedMembershipInfo.startEndEpoch.startEpoch), "D MMM YYYY")
        const endDateFormatted = TimeUtil.formatEpochInTimeZone(tz, Number(aggregatedMembershipInfo.startEndEpoch.endEpoch), "D MMM YYYY")
        if (isSessionBasedPackProgress && aggregatedMembershipInfo?.totalTickets) {
            total = aggregatedMembershipInfo.totalTickets
            completed = aggregatedMembershipInfo.totalTicketsConsumed
        } else {
            const endDate = TimeUtil.getDefaultMomentForDateString(endDateFormatted, tz)
            const startDate = TimeUtil.getDefaultMomentForDateString(startDateFormatted, tz)
            const numDaysToEndFromToday = endDate.diff(today, "days")
            total = endDate.diff(startDate, "days")
            completed = numDaysToEndFromToday > 0 ? today.diff(startDate, "days") : endDate.diff(startDate, "days")
        }
        return {
            total,
            completed,
            isCompleted: total - completed < 1,
            endDateFormatted: `Ends: ${endDateFormatted}`
        }
    }

    public static getCareConsultationPacksBannerId(doctorType: string): string {
        const STAGE_WIDGET_ID = "19d64334-23aa-4844-97f9-4b189f322986"
        const DEFAULT_PROD_WIDGET_ID = "cae6616b-c32b-4ecc-a30a-62f594f22a88"
        if (process.env.ENVIRONMENT === "PRODUCTION" || process.env.ENVIRONMENT === "ALPHA") {
            switch (doctorType) {
                case "DERMATOLOGIST": return "b029af16-a4c4-4aba-93e4-1856aaa2ac82"
                case "GYNAECOLOGIST": return "4e5f7d70-e1f8-41fe-99c3-c93e1275a416"
                case "SEXOLOGIST": return "f32ca86d-1358-4eef-8ea1-b13667e0732d"
                default: return DEFAULT_PROD_WIDGET_ID
            }
        }
        return STAGE_WIDGET_ID
    }

    public static isCareConsultationProduct(product: ConsultationProduct) {
        return !(CareUtil.isMindDoctorType(product.doctorType) ||
            CareUtil.isLivePTDoctorType(product.doctorType) ||
            CareUtil.isLiveSGTDoctorType(product.doctorType) ||
            CareUtil.isLivePTProduct(product) ||
            product.tenant === "CULTFIT" ||
            CareUtil.isNutritionistDoctor(product.doctorType)
        )
    }

    public static showCareFooterInCheckout(consultation: ConsultationOrderResponse, userContext: UserContext) {
        return (userContext.sessionInfo.userAgent === "MBROWSER" ||
            CareUtil.isCareConsultationProduct(consultation.consultationProduct) ||
            CareUtil.isMindDoctorType(consultation.consultationProduct?.doctorType) ||
            CareUtil.isNutritionistDoctor(consultation.consultationProduct?.doctorType) ||
            (userContext.sessionInfo.appVersion >= 8.19 && !(CareUtil.isLivePTSessionConsultation(consultation) || CareUtil.isLiveSGTSessionConsultation(consultation))))
    }

    public static getTestSuggestion(testFrequency: any): string {
        if (testFrequency && testFrequency.unit) {
            switch (testFrequency.unit) {
                case "STAT":
                case "SOS":
                    return "Do now"
                case "HOUR":
                    return "Do in " + testFrequency.count + (testFrequency.count > 1 ? " Hours" : " Hour")
                case "DAY":
                    return "Do in " + testFrequency.count + (testFrequency.count > 1 ? " Days" : " Day")
                case "MONTH":
                    return "Do in " + testFrequency.count + (testFrequency.count > 1 ? " Months" : " Month")
                case "YEAR":
                    return "Do in " + testFrequency.count + (testFrequency.count > 1 ? " Years" : " Year")
                case "WEEK":
                    return "Do in " + testFrequency.count + (testFrequency.count > 1 ? " Weeks" : " Week")
                default:
                    return ""
            }
        } else {
            return undefined
        }
    }

    public static getDoctorText(type: string): string {
        if (this.isLivePTDoctorType(type)) {
            return "trainer"
        }
        switch (type) {
            case "GP":
            case "SPL":
            case "GP_STANDARD":
            case "GENERAL_PHYSICIAN":
            case "HCU_GP_IM":
                return "general physician"
            case "AYURVEDA": return "ayurveda specialist"
            case "INTERNAL_MEDICINE": return "internal medicine"
            case "LC":
                return "dietician"
            case "AI_LC":
                return "health coach"
            case "GYNAECOLOGIST":
                return "gynaecologist"
            case "LIVE_PERSONAL_TRAINER_SnC":
            case "LIVE_PERSONAL_TRAINER_YOGA":
            case "LIVE_PERSONAL_TRAINER_BOXING":
            case "LIVE_PERSONAL_TRAINER_DF":
            case "PERSONAL_TRAINER":
            case "PERSONAL_TRAINER_L2":
            case "PERSONAL_TRAINER_L3":
                return "trainer"
            case "PHYSIOTHERAPIST":
                return "physiotherapist"
            case "ANXIETY_THERAPIST": return "stress coach"
            case "MIND_THERAPIST": return "therapist"
            case "COUPLE_THERAPIST": return "couple therapist"
            case "COVID_THERAPIST": return "covid therapist"
            case "SLEEP_THERAPIST": return "sleep therapist"
            case "PSYCHIATRIST": return "psychiatrist"
            case "ORTHOPEDIC": return "orthopedic"
            case "ENT": return "ENT specialist"
            case "DERMATOLOGIST": return "dermatologist"
            case "OPHTHALMOLOGIST": return "ophthalmologist"
            case "PEDIATRICIAN": return "pediatrician"
            case "UROLOGIST": return "urologist"
            case "PULMONOLOGIST": return "pulmonologist"
            case "GASTROENTEROLOGIST": return "gastroenterologist"
            case "ENDOCRINOLOGIST": return "endocrinologist"
            case "DENTIST": return "dentist"
            case "NEUROLOGIST": return "neurologist"
            case "CARDIOLOGIST": return "cardiologist"
            case "RHEUMATOLOGIST": return "rheumatologist"
            case "NEPHROLOGIST": return "nephrologist"
            case "GENERAL_SURGEON": return "general surgeon"
            case "GASTRO_SURGEON": return "gastric surgeon"
            case "PLASTIC_SURGEON": return "plastic surgeon"
            case "ONCO_SURGEON": return "surgical oncologist"
            case "NEURO_SURGEON": return "neurosurgeon"
            case "TRICHOLOGIST": return "trichologist"
            case "ONCOLOGIST": return "oncologist"
            case "ARTHROSCOPIC_SURGEON": return "arthroscopic surgeon"
            case "SEXOLOGIST": return "sexologist"
            case "BARIATRIC_SURGEON": return "bariatric surgeon"
            case "CARDIOTHORACIC_SURGEON": return "cardiothoracic surgeon"
            case "CARDIO_SURGEON": return "cardio surgeon"
            case "LASER_HAIR_THERAPY": return "trichologist"
            case "NEONATOLOGIST": return "neonatologist"
            case "ORTHOPEDIC_SURGEON": return "orthopedic surgeon"
            case "PSYCHOLOGIST": return "psychologist"
            case "RADIOLOGIST": return "radiologist"
            case "SURGEON": return "surgeon"
            case "VASCULAR_SURGEON": return "vascular surgeon"
            case "CARDIAC_VASCULAR_SURGEON": return "cardiac vascular surgeon"
            case "NEONATOLOGIST": return "neonatologist"
            case "NUTRITIONIST_AND_LIFESTYLE_COACH": return "dietician"
            case "OBSTETRICIAN": return "obstetrician"
            case "ORTHOPEDIC_SURGEON": return "orthopedic surgeon"
            case "PAIN_MANAGEMENT": return "pain management"
            case "CARDIOTHORACIC_SURGEON": return "cardiothoracic surgeon"
            case "CARDIO_SURGEON": return "cardio surgeon"
            case "HAEMATO_ONCOLOGIST": return "haemato oncologist"
            case "HEMATOLOGIST": return "hematologist"
            case "INFERTILITY_SPECIALIST": return "infertility specialist"
            case "ANDROLOGIST": return "andrologist"
            case "ANESTHESIOLOGIST": return "anestheiologist"
            case "ACNE_LASER_TREATMENT":
            case "ACNE_PEELS_TREATMENT":
            case "ACNE_PEEL_ARMS":
            case "ACNE_PEEL_BACK":
            case "BOTOX_BUNNYLINES":
            case "BOTOX_CHIN":
            case "BOTOX_CROWSFEET":
            case "BOTOX_FOREHEAD":
            case "BOTOX_FROWN":
            case "BOTOX_LIPS_MOUTH":
            case "BOTOX_NECK":
            case "BOTOX_UPPERFACE":
            case "FACE_GLOW":
            case "FACE_POLISH":
            case "FILLER_1":
            case "FILLER_2":
            case "FILLER_3":
            case "FILLER_4": return "dermatologist"
            case "LASER_HAIR_THERAPY":
            case "LHR_ARMS":
            case "LHR_BACK":
            case "LHR_BIKINI":
            case "LHR_CHEST_AND_ABDOMEN":
            case "LHR_CHIN":
            case "LHR_EYEBROWS":
            case "LHR_FACE":
            case "LHR_FULL_BODY":
            case "LHR_LEGS":
            case "LHR_NECK":
            case "LHR_UNDERARMS":
            case "LHR_UPPERLIPS": return "trichologist"
            case "MDA_ARMS":
            case "MDA_BACK":
            case "MDA_FACE":
            case "MEDIFACIAL": return "dermatologist"
            case "COVID_SPECIALIST_PHYSICIAN_WITH_FOLLOWUPS":
            case "COVID_SPECIALIST_PHYSICIAN_WITH_FOLLOW_UPS": return "covid specialist"
            default: return "doctor"
        }
    }


    static getLivePTSessionNoteListWidget(bookingDetail: BookingDetail, userContext: UserContext, bgColor: string, duration?: number, isLiveSGTProduct?: boolean): NoteListWidget {
        const cancellationWindowInHours = (bookingDetail.hasOldCancellationWindow ? 180 : bookingDetail.consultationOrderResponse.consultationProduct.cancellationWindowInMins) / 60
        const items: NoteListItem[] = [{
            icon: "/image/icons/livept/video_30.png",
            info: "Join link will be shared 10 minutes before the session"
        }, {
            icon: "/image/icons/livept/cancel_30.png",
            info: `Cancel up to ${cancellationWindowInHours} ${cancellationWindowInHours > 1 ? "hours" : "hour"} before the scheduled start time.`,
            description: `Cut-off: ${TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, bookingDetail.consultationOrderResponse.startTime - ((bookingDetail.hasOldCancellationWindow ? 180 : bookingDetail.consultationOrderResponse.consultationProduct.cancellationWindowInMins) * 60000), "Do MMM, hh:mm a")}`
        }, {
            icon: "/image/icons/livept/attendance_30.png",
            info: `The session will be marked 'Missed' if you don’t join within ${duration ? duration : 20} mins of the start time`
        }]
        if (isLiveSGTProduct) {
            items.push({
                icon: "/image/icons/livept/Laptop_big.png",
                info: "We recommend joining the session from the website/laptop for the best experience.",
            })
            items[0] = {
                icon: "/image/icons/livept/video_30.png",
                info: "This is a live workout session. It is mandatory to switch on your camera for this session.",
                description: "Video link will be shared 5 mins before the session.",
            }
            if (bookingDetail.booking.productCode === "CONS_CULT_LIVE_SGT_BOXING") {
                items.unshift({
                    icon: "image/live-pt/sgt-focus-area-images/<EMAIL>",
                    info: "No boxing equipment is needed.",
                })
            }
        }
        return {
            widgetType: "NOTE_LIST_WIDGET",
            note: {
                title: "NOTE",
                color: "#000000"
            },
            data: items,
            bgColor
        }
    }

    public static getCareOfferWidget(
        title: string,
        offers: OfferV2[],
        userContext: UserContext,
        isBookingPage?: boolean,
        isV2Version?: boolean,
        hideOrientation?: boolean
    ) {
        if (_.isEmpty(offers)) {
            return undefined
        }
        const userAgent: UserAgentType = userContext.sessionInfo.userAgent
        if (isV2Version && userContext.sessionInfo.appVersion >= 8.35) {
            const widgetData = getOffersWidget(undefined, offers, userAgent, hideOrientation)
            return widgetData?.offers?.length > 0 ? {
                ...widgetData,
                containerStyle: {
                    backgroundColor: "#f0f9ff",
                    borderColor: "#d1dde5",
                    borderWidth: 1,
                    borderRadius: 5,
                    borderStyle: "dashed",
                    padding: 15,
                },
                margin: 20,
                backgroundColor: "transparent",
                gradientColors: ["white", "white"],
                offerIconStyle: { tintColor: "black" },
                offerTextStyle: { fontSize: 14, color: "#55565b", fontFamily: AppFont.Regular },
                hasDividerBelow: false
            } : undefined
        }
        const widgetData = getOffersWidget(title, offers, userAgent, hideOrientation)
        return widgetData?.offers?.length > 0 ? {
            ...widgetData,
            ...(isBookingPage ? {
                titleStyle: { fontSize: 14, color: "black" },
                backgroundColor: "transparent",
                gradientColors: ["white", "white"],
                offerTextStyle: { fontSize: 14, color: "#55565b", fontFamily: AppFont.Regular }
            } : {
                titleStyle: { color: "black" },
                backgroundColor: "transparent",
                gradientColors: ["white", "white"],
                offerTextStyle: { fontSize: 14, color: "#55565b", fontFamily: AppFont.Regular }
            }),
            hasDividerBelow: false
        } : undefined
    }

    public static getPrescriptionEmailAction(bookingDetail: BookingDetail, userContext: UserContext): Action {
        if (_.isEmpty(bookingDetail) || !this.isComplteted(bookingDetail) || !AppUtil.isWeb(userContext) || this.isTherapyConsultation(bookingDetail.consultationOrderResponse)) {
            return
        }
        return this.getPrescriptionSendEmailAction(bookingDetail)
    }

    public static getPrescriptionSendEmailAction(bookingDetail: BookingDetail): Action {
        if (!bookingDetail.consultationOrderResponse || !bookingDetail.consultationOrderResponse.hasPrescription || _.isEmpty(bookingDetail.consultationOrderResponse.prescription) || !bookingDetail.consultationOrderResponse.hasDigitalDocument) {
            return
        }

        return {
            actionType: "EMAIL_PLAN",
            meta: {
                tcBookingId: bookingDetail.booking.id,
                consultationId: bookingDetail.consultationOrderResponse.id
            },
            title: "SEND EMAIL"
        }
    }

    public static getTCBookingAction(patientsList: Patient[], product: ConsultationProduct, actionTitle?: string, nextAction?: Action) {
        const { productId } = product
        const actionStringOnline: string = ActionUtil.selectCareDateV1(productId)
        const relations = CareUtil.getRelations()
        let action: Action = null
        if (!_.isEmpty(patientsList)) {
            const isSelfPatientPresent = patientsList.find(patient => patient.relationship === "Self")
            const defaultRelationShip = isSelfPatientPresent ? { patientRelation: "Other" } : {}
            if (CareUtil.isAnxietyTherapy(productId)) {
                action = CareUtil.getTherapyConsultationAction(productId, patientsList, undefined, undefined, undefined, undefined, "Book your appointment")
            } else {
                action = {
                    actionType: "SHOW_PATIENT_SELECTION",
                    title: actionTitle || "Book your appointment",
                    meta: {
                        url: nextAction?.url || actionStringOnline,
                        action: nextAction ? nextAction : {
                            actionType: "NAVIGATION",
                            url: actionStringOnline
                        },
                        patientsList: patientsList,
                        relations: isSelfPatientPresent ? CareUtil.getOtherRelation() : relations,
                        emergencyContactRelations: CareUtil.getEmergencyRelations(),
                        guardianRelations: CareUtil.getGuardianRelations(),
                        reqParams: {
                            formUserType: "CARE_USER",
                            ...defaultRelationShip
                        },
                    }
                }
            }

        } else {
            action = {
                actionType: "ADD_PATIENT",
                title: actionTitle || "Book your appointment",
                meta: {
                    url: nextAction?.url || actionStringOnline,
                    action: nextAction ? nextAction : {
                        actionType: "NAVIGATION",
                        url: actionStringOnline
                    },
                    reqParams: {
                        formUserType: CareUtil.isAnxietyTherapy(productId) ? "THERAPY_USER" : "CARE_USER",
                        [CareUtil.isAnxietyTherapy(productId) ? "patientRelation" : undefined]: "Self"
                    },
                    relations: relations,
                    emergencyContactRelations: CareUtil.getEmergencyRelations(),
                    guardianRelations: CareUtil.getGuardianRelations()
                }
            }
        }
        return action
    }

    public static getGP99CallNowItemInfo(product: ConsultationProduct, doctAvlInfo: DoctorAvlImmediateResponse, isSelected: boolean): ConsultationProductItem {
        return {
            isSelected,
            showRadioButton: true,
            title: "Get call Now",
            subTitle: "Consult with a doctor immediately",
            footer: isSelected && !doctAvlInfo.immediateSlotAvailable ? {
                icon: "/image/icons/careBenefit/repeat.png",
                text: "Sorry, no doctor available at the moment. You can schedule an appointment instead."
            } : undefined,
            slotsInfo: undefined,
            slotAction: undefined,
            action: {
                isDisabled: isSelected,
                actionType: "NAVIGATION",
                url: `curefit://${PageTypes.CarefitTC}?id=${product.productId}&isCallFlow=true`,
                meta: {
                    isCallFlow: true
                }
            },
            isSelectedImageUrl: isSelected ? "image/icons/selectedPink.png" : "image/icons/packUnselected%403x.png",
        }
    }

    public static getGP99ScheduleItemInfo(product: ConsultationProduct, doctAvlInfo: DoctorAvlImmediateResponse, isSelected: boolean): ConsultationProductItem {
        return {
            isSelected,
            showRadioButton: true,
            title: "Schedule an appointment",
            subTitle: "Choose any slot and schedule a booking",
            footer: undefined,
            slotsInfo: undefined,
            slotAction: undefined,
            action: {
                isDisabled: isSelected,
                actionType: "NAVIGATION",
                url: `curefit://${PageTypes.CarefitTC}?id=${product.productId}&isCallFlow=false`,
                meta: {
                    isCallFlow: false
                }
            },
            isSelectedImageUrl: isSelected ? "image/icons/selectedPink.png" : "image/icons/packUnselected%403x.png",
        }
    }

    public static getGP99ProductItem(product: ConsultationProduct, doctAvlInfo: DoctorAvlImmediateResponse, isCallNowFlow: boolean): ConsultationProductItem[] {
        return [
            CareUtil.getGP99CallNowItemInfo(product, doctAvlInfo, isCallNowFlow),
            CareUtil.getGP99ScheduleItemInfo(product, doctAvlInfo, !isCallNowFlow),
        ]
    }

    public static useNewGP99ProductFlow(userContext: UserContext, baseProduct: ConsultationProduct, bookingId?: number) {
        return _.isEmpty(bookingId) && CareUtil.isGP99DoctorType(baseProduct.doctorType) && (userContext.sessionInfo.appVersion >= 8.36)
    }

    public static getGP99CallNowHowItWorksWidget(): WidgetView {
        return {
            "type": "SMALL",
            "header": {
                "title": "How it works",
                "color": "#000000"
            },
            "items": [
                {
                    "subTitle": "You can get a call from a qualified doctor in next 20 mins",
                    "icon": "/image/icons/howItWorks/call.png"
                },
                {
                    "subTitle": "Book for anyone in your family",
                    "icon": "/image/icons/howItWorks/buddy_2.png"
                },
                {
                    "subTitle": "Complete Payment and Expect a call to your phone from doctor",
                    "icon": "/image/icons/howItWorks/card.png"
                },
                {
                    "subTitle": "Prescription will be made available on the app or website",
                    "icon": "/image/icons/howItWorks/prescription.png"
                }
            ],
            "hideSepratorLines": false,
            "widgetType": "PRODUCT_LIST_WIDGET"
        }
    }

    public static getGP99CallNowAudioWidget(): WidgetView {
        return {
            "type": "SMALL",
            "header": {
                "title": "Consulting through Audio",
                "color": "#000000"
            },
            "items": [
                {
                    "subTitle": "Doctor will call on the registered phone number",
                    "icon": "/image/icons/howItWorks/tick.png"
                },
                {
                    "subTitle": "Expect call within the slot duration",
                    "icon": "/image/icons/howItWorks/tick.png"
                },
                {
                    "subTitle": "Share any reports, documents and images before the consultation",
                    "icon": "/image/icons/howItWorks/tick.png"
                }
            ],
            "hideSepratorLines": true,
            "widgetType": "PRODUCT_LIST_WIDGET"
        }
    }

    public static getUpcomingConsultationFooterInfo(consultation: Consultation | ConsultationOrderResponse, tz: Timezone, isV2Icon?: boolean, atCenterConsultationFooterText?: string) {
        const isAudio = !CareUtil.isSupportGroupProduct(consultation.consultationProduct) && CareUtil.isAudioBookingFromConsultation(consultation)
        const isVideo = CareUtil.isSupportGroupProduct(consultation.consultationProduct) || CareUtil.isVideoBookingFromConsultation(consultation)
        if (isV2Icon) {
            return [{
                text: `${isVideo || isAudio ? `${isAudio ? "Audio" : "Video"} | ${TimeUtil.formatEpochInTimeZone(tz, consultation.startTime, "ddd, D MMM, h:mm A")}` : (atCenterConsultationFooterText || `At Centre | ${TimeUtil.formatEpochInTimeZone(tz, consultation.startTime, "ddd, D MMM, h:mm A")}`)}`,
                icon: isAudio ? "DISCUSS" : isVideo ? "VIDEO" : "INCENTRE"
            }]
        }
        return [{
            text: `${isVideo || isAudio ? `${isAudio ? "Audio" : "Video"} | ${TimeUtil.formatEpochInTimeZone(tz, consultation.startTime, "ddd, D MMM, h:mm A")}` : (atCenterConsultationFooterText || `At Centre | ${TimeUtil.formatEpochInTimeZone(tz, consultation.startTime, "ddd, D MMM, h:mm A")}`)}`,
            icon: isAudio ? "DISCUSS" : (isVideo ? "video" : "incentre")
        }]
    }

    public static getPreferredDoctorBookingWidget(userContext: UserContext, item: CareTeam, patientId: number): SuggestionWidget {
        if (!_.isEmpty(item.consultationSellableProductUserProductInfos)) {
            const parentBookingId = !_.isEmpty(item.consultationSellableProductUserProductInfos[0].userMembershipInfos) ? item.consultationSellableProductUserProductInfos[0].userMembershipInfos[0].bookingId : null
            /**
             * For Mind Therapy, we have to filter out virtual center for EFA
             * This is because now a doctor can have more than one center mapped to them in roster
             */
            const doctorCenterMappingWithoutVirtualCenter = item.doctor.doctorCenterMapping.filter(
                mapping => mapping.centerId !== BaseCareUtil.getVirtualCenterId()
            )
            const products = <DiagnosticProductResponse[]>item.consultationSellableProductUserProductInfos.map((product) => product.baseSellableProduct).filter((product: DiagnosticProductResponse) => product?.consultationProduct?.consultationMode === "ONLINE")
            const cardAction = CareUtil.getBookinConsultationAction(
                userContext,
                parentBookingId,
                patientId,
                products,
                true,
                item.doctor.id.toString(),
                Array.isArray(item.doctor?.doctorCenterMapping)
                    ? doctorCenterMappingWithoutVirtualCenter[0]?.centerId
                    : undefined
            )
            return {
                widgetType: "SUGGESTION_WIDGET",
                title: `Book with my ${item.doctorTypeCode.code === "MIND_THERAPIST" ? "therapist" : "psychiatrist"}`,
                contentImage: item.doctor.displayImage,
                contentTitle: item.doctor.name,
                contentSubtitle: item.doctor.detailedQualification,
                cardAction: cardAction
            }
        }
    }

    public static getDoctorFooterCardAction(userContext: UserContext, product: ConsultationProduct, patientsList: Patient[], widgetParams: CareDoctorSearchPageParams) {
        let doctorCardFooterAction
        if (userContext.sessionInfo.isUserLoggedIn) {
            doctorCardFooterAction = CareUtil.consultationCheckoutAction(userContext, product, patientsList, undefined, undefined, widgetParams.patientId, undefined, widgetParams.parentBookingId, widgetParams.isReschedule)
            if (AppUtil.isPsyMultiPatientSupported(userContext) && this.isPsychiatry(product.doctorType)) {
                if (widgetParams.patientId) {
                    doctorCardFooterAction = {
                        ...this.checkPsyConsentAction(doctorCardFooterAction),
                        title: "BOOK APPOINTMENT"
                    }
                } else {
                    doctorCardFooterAction = {
                        ...doctorCardFooterAction,
                        meta: {
                            ...doctorCardFooterAction?.meta,
                            action: this.checkPsyConsentAction(doctorCardFooterAction?.meta?.action)
                        },
                        title: "BOOK APPOINTMENT"
                    }
                }
            } else {
                doctorCardFooterAction = {
                    ...doctorCardFooterAction,
                    title: "BOOK APPOINTMENT"
                }
            }
        } else {
            const loginAction = AppUtil.isFromFlutterAppFlow(userContext) ? AppUtil.getLoginModalAction() : CareUtil.getLoginAlertAction()
            doctorCardFooterAction = {
                ...loginAction,
                title: "BOOK APPOINTMENT"
            }
        }
        return doctorCardFooterAction
    }

    public static getDoctorEarliestAvailabilitySlotAction(
        userContext: UserContext,
        doctor: Doctor,
        productId: string,
        patientId: number,
        parentBookingId?: number,
        product?: ConsultationProduct,
        centerId?: number,
        hasPreferredDoctor?: boolean,
        sentSlotForAllDate?: boolean,
    ) {
        const tz = userContext.userProfile.timezone
        const url = AppUtil.isFromFlutterAppFlow(userContext) ? "curefit://fl_slot_checkout" : "curefit://carecheckout"
        const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDateWithTimezone(tz), tz)
        const firstSlot = doctor.earliestAvailabilityList?.[0]?.startTime
        const firstSlotDate = firstSlot ? TimeUtil.getDefaultMomentForDateString(TimeUtil.formatEpochInTimeZone(tz, Number(firstSlot), "D MMM YYYY"), tz) : undefined
        return _.map(doctor.earliestAvailabilityList, slot => {
            const slotDate = TimeUtil.getDefaultMomentForDateString(TimeUtil.formatEpochInTimeZone(tz, Number(slot.startTime), "D MMM YYYY"), tz)
            const doctorCenterMappingItem = doctor.doctorCenterMapping.find(item => item.centerId === centerId)
            if (slotDate.isSame(today, "day") ||  (sentSlotForAllDate && slotDate.isSame(firstSlotDate, "day"))) {
                const showConsentFlow = AppUtil.isDoctorConsentFlowSupported(userContext) && hasPreferredDoctor && !doctor.preferredDoctor && product?.doctorType === "LC"
                const checkoutAction: Action = {
                    actionType: "NAVIGATION",
                    url: `${url}?productId=${productId}&parentBookingId=${parentBookingId}&centerId=${slot.centerId}${patientId ? `&patientId=${patientId}` : ""}${product?.doctorType ? `&doctorType=${product?.doctorType}` : ""}`,
                    meta: {
                        selectedSlot: {
                            ...slot,
                            doctorIdList: [doctor.id],
                            listingCode: doctorCenterMappingItem && doctorCenterMappingItem.listingCode
                        },
                        ...slot,
                        doctorIdList: [doctor.id],
                        productId: productId,
                        patientId: patientId,
                        parentBookingId: parentBookingId,
                        doctorId: doctor.id,
                        centerId: slot.centerId,
                        doctorType: product?.doctorType,
                        listingCode: doctorCenterMappingItem && doctorCenterMappingItem.listingCode
                    }
                }
                const loginAction = AppUtil.isFromFlutterAppFlow(userContext) ? AppUtil.getLoginModalAction() : CareUtil.getLoginAlertAction()
                return {
                    title: TimeUtil.formatEpochInTimeZone(tz, slot.startTime, "h:mm A"),
                    startTime: slot.startTime,
                    endTime: slot.endTime,
                    centerId: slot.centerId,
                    action: userContext.sessionInfo.isUserLoggedIn ? (showConsentFlow ?
                        CareUtil.getDoctorConsentInfoModal(checkoutAction, product.doctorType, String(doctor.id), String(patientId))
                     :  checkoutAction) : loginAction as Action
                }
            }
            return null
        }).filter(Boolean)
    }

    public static getOffersApplied(bundleProducts: BundleSessionSellableProduct[], selectedProduct: BundleSessionSellableProduct, bundleoffers: PackOffersResponse): OfferV2[] {
        const appliedOffers: OfferV2[] = []
        bundleProducts.forEach((product: BundleSessionSellableProduct) => {
            const offerDetails = OfferUtil.getPackOfferAndPriceForCare(product, bundleoffers)
            if (product.productCode === selectedProduct.productCode && offerDetails && !_.isEmpty(offerDetails.offers)) {
                appliedOffers.push(...offerDetails.offers)
            }
        })
        return appliedOffers
    }

    public static getOfferDataList(appliedOffers: OfferV2[]): OfferData[] {
        let offerDataList: OfferData[] = _.map(appliedOffers, offer => {
            let offerText = undefined
            if (offer.uiLabels && !_.isEmpty(offer.uiLabels.cartLabel)) {
                offerText = offer.uiLabels.cartLabel
            } else {
                _.forEach(offer.addons, addon => {
                    if (addon && addon.uiLabels && !_.isEmpty(addon.uiLabels.cartLabel)) {
                        offerText = addon.uiLabels.cartLabel
                    }
                })
            }
            if (!_.isEmpty(offerText)) {
                return {
                    title: offer.title,
                    description: offerText,
                    tnc: offer.tNc,
                    tncURL: offer.tNcUrl
                }
            }
        })
        offerDataList = offerDataList.filter(offerItem => !_.isEmpty(offerItem))
        return offerDataList
    }

    public static getOffersWidget(title: string, appliedOffers: OfferV2[], userAgent: UserAgentType): OfferCalloutWidget {
        const offerDataList: OfferData[] = this.getOfferDataList(appliedOffers)
        let hideSepratorLines: boolean = true
        if (userAgent === "MBROWSER" || userAgent === "DESKTOP") {
            hideSepratorLines = false
        }
        return new OfferCalloutWidget(
            title,
            offerDataList,
            undefined,
            hideSepratorLines
        )
    }

    public static getProductInfoWidget(selectedProduct: BundleSessionSellableProduct, packDescription: ManagedPlanPackInfo, productInfoSubTitleContainerStyle?: any, isLivePTPackPageWithOffersSupported?: boolean) {
        const header = {
            title: selectedProduct.infoSection.packTitle,
            subTitle: selectedProduct.infoSection.packDescription,
            style: { paddingLeft: 0 },
            subTitleProps: { style: { paddingLeft: 0, paddingRight: 0, fontSize: 12, color: "#888e9e", marginTop: 5 } },
            titleProps: { style: { fontSize: 22, fontFamily: "BrandonText-Bold", lineHeight: 30 } },
            subTitleContainerStyle: productInfoSubTitleContainerStyle
        }
        const titleStyle = {
            color: "#888e9e",
            fontSize: 12,
            fontFamily: "BrandonText-Regular",
        }
        const productInfo = new ProductListWidget("DYNAMIC_ICON", header, [])
        productInfo["noTopPadding"] = true
        if (isLivePTPackPageWithOffersSupported) {
            productInfo["noBottomPadding"] = true
        }
        return productInfo
    }

    public static getProductRadioSelectionWidget(bundleProducts: BundleSessionSellableProduct[], selectedProduct: BundleSessionSellableProduct, bundleoffers: PackOffersResponse, appliedOffers: any[], productPayByMembershipMap?: ProductPayByMembershipMap, useCultMemberShipForPayment?: boolean, isMWebView?: boolean, isLivePTPackPageWithOffersSupported?: boolean, containerStyle?: any, rowContainerStyle?: any, membershipPaymentType?: MembershipPaymentType, hasDividerBelow?: boolean, toggleSelected?: boolean): ProductRadioSelectionWidget {
        const data: ProductRow[] = []
        bundleProducts.forEach((product: BundleSessionSellableProduct, index: number) => {
            let payByMembershipDetails
            if (productPayByMembershipMap) {
                payByMembershipDetails = productPayByMembershipMap[product.productCode]
            }
            const offerDetails = OfferUtil.getPackOfferAndPriceForCare(product, bundleoffers)
            product.listingPrice = offerDetails.price.listingPrice
            const isEligibleToPayByMembership = CareUtil.isEligibleToPayByMembership(product, productPayByMembershipMap)
            const isSelected = product.productCode === selectedProduct.productCode
            const isLiveSGT = product.subCategoryCode === "LIVE_SGT"
            const isLivePT = product.subCategoryCode === "LIVE_PERSONAL_TRAINING"
            const isMindTherapy = product.subCategoryCode === "MIND_THERAPY"
            const leftTitle = isMindTherapy ? (isMWebView ? "" : product.infoSection.headerDescription) : (isLiveSGT ? "" : `Valid for ${product.duration} days`)
            const packDetails: ProductRow = {
                id: product.productCode,
                title: `${product.infoSection.sellingTitle}`,
                price: {
                    mrp: product.mrp,
                    listingPrice: (useCultMemberShipForPayment && isEligibleToPayByMembership) ? 0 : product.listingPrice,
                    showPriceCut: (useCultMemberShipForPayment && isEligibleToPayByMembership) ? true : product.listingPrice < product.mrp,
                    currency: offerDetails.price.currency
                },
                leftInfoText: {
                    title: leftTitle
                },
                isSelected: isSelected,
                action: {
                    actionType: "NAVIGATION",
                    meta: {
                        sellableProductId: product.productCode
                    }
                },
                hasDividerBelow: index === (bundleProducts.length - 1) ? false : hasDividerBelow
            }
            if (rowContainerStyle) {
                packDetails["containerStyle"] = rowContainerStyle
            }
            if (isLivePTPackPageWithOffersSupported && !useCultMemberShipForPayment) {
                const productOffers: any[] = CareUtil.getOffersApplied(bundleProducts, product, bundleoffers)
                const productOfferDataList: OfferData[] = this.getOfferDataList(productOffers)
                if (productOfferDataList && productOfferDataList.length > 0) {
                    packDetails["offerView"] = {
                        title: `${productOfferDataList.length} ${pluralizeStringIfRequired("offer", productOfferDataList.length)}`,
                        icon: "/image/icons/cult/offer.png",
                        offers: productOfferDataList,
                        isExpended: product.productCode === selectedProduct.productCode
                    }
                }
            }
            if (isLiveSGT && ((payByMembershipDetails.cult && payByMembershipDetails.cult.isMember) || (payByMembershipDetails.mind && payByMembershipDetails.mind.isMember))) {
                packDetails["infoText"] = {
                    title: (payByMembershipDetails.cult && payByMembershipDetails.cult.isEligible) ? `Swap ${payByMembershipDetails.cult.numDaysToDeduct} cult days` : (payByMembershipDetails.mind && payByMembershipDetails.mind.isEligible) ? `Swap ${payByMembershipDetails.mind.numDaysToDeduct} mind days` : "insufficient days!",
                    style: ((payByMembershipDetails.cult && payByMembershipDetails.cult.isEligible) || (payByMembershipDetails.mind && payByMembershipDetails.mind.isEligible)) ? { color: "#000000" } : { color: "#ffa300" },
                }
            }

            if (isLivePT || isMindTherapy) {
                const perSessionPrice = Math.floor(product.listingPrice / product.infoSection.numberOfSessions)
                packDetails["infoText"] = {
                    title: `${RUPEE_SYMBOL}${perSessionPrice}/Session`,
                    style: { color: "#000000" }
                }
            }
            let isMindMember, isCultMember
            if (!_.isEmpty(productPayByMembershipMap)) {
                payByMembershipDetails = productPayByMembershipMap[product.productCode]
                isCultMember = (payByMembershipDetails.cult && payByMembershipDetails.cult.isMember)
                isMindMember = (payByMembershipDetails.mind && payByMembershipDetails.mind.isMember)
                if (isCultMember) {
                    // check for auto selecting cult membership option for first time
                    if (!membershipPaymentType) {
                        useCultMemberShipForPayment = true
                        membershipPaymentType = MembershipPaymentType.CULT
                    }
                } else if (isMindMember) {
                    if (!isCultMember && !membershipPaymentType) {
                        useCultMemberShipForPayment = true
                        membershipPaymentType = MembershipPaymentType.MIND
                    }
                }
            }
            if (isLivePTPackPageWithOffersSupported && (isCultMember || isMindMember) && (isLivePT || isLiveSGT)) {
                let isDisabled, disabledDescription, description
                if (membershipPaymentType === "CULT") {
                    isDisabled = payByMembershipDetails.cult ? !payByMembershipDetails.cult.isEligible : false
                    disabledDescription = (useCultMemberShipForPayment && toggleSelected) ? "insufficient days" : ""
                    description = (useCultMemberShipForPayment && toggleSelected) ? `${payByMembershipDetails.cult?.numDaysToDeduct} ${membershipPaymentType.toLowerCase()} days` : ""
                } else {
                    isDisabled = payByMembershipDetails.mind ? !payByMembershipDetails.mind.isEligible : false
                    disabledDescription = (useCultMemberShipForPayment && toggleSelected) ? "insufficient days" : ""
                    description = (useCultMemberShipForPayment && toggleSelected) ? `${payByMembershipDetails.mind?.numDaysToDeduct} ${membershipPaymentType.toLowerCase()} days` : ""
                }
                packDetails["infoText"] = {
                    title: isDisabled ? disabledDescription : description,
                    style: isDisabled ? { color: "#ffa300" } : { color: "#888e9e" }
                }
            }
            data.push(packDetails)
        })
        return {
            data,
            widgetType: "PRODUCT_RADIO_SELECTION_WIDGET",
            containerStyle: containerStyle || { marginTop: -16, marginBottom: 6 },
        }
    }

    public static isEligibleToPayByMembership(product: BundleSessionSellableProduct, productPayByMembershipMap?: ProductPayByMembershipMap) {
        let payByMembershipDetails
        if (productPayByMembershipMap) {
            payByMembershipDetails = productPayByMembershipMap[product.productCode]
            return ((payByMembershipDetails.cult && payByMembershipDetails.cult.isEligible) || (payByMembershipDetails.mind && payByMembershipDetails.mind.isEligible))
        }
        return false
    }

    public static getMindBundlePreBookingActions(
        userContext: UserContext,
        numberOfSessions: number,
        showPatientSelectionModal: boolean,
        offerIds: string[],
        url: string,
        patientsList: Patient[],
        productCode: string
    ): Action[] {
        const actionTitle = `Book ${numberOfSessions} ${numberOfSessions > 1 ? "Sessions" : "Session"}`
        if (!userContext.sessionInfo.isUserLoggedIn) {
            return [
                {
                    actionType: "SHOW_ALERT_MODAL",
                    title: actionTitle,
                    meta: {
                        title: "Login Required!",
                        subTitle: "Please login to continue",
                        actions: [{ actionType: "LOGOUT", title: "Login" }]
                    }
                }
            ]
        } else {
            if (!_.isEmpty(offerIds)) {
                url += `&offerIds=${offerIds.join(",")}`
            }
            const selfPatient = !_.isEmpty(patientsList) ? _.find(patientsList, patient => patient.relationship === "Self") : undefined
            const patientId = selfPatient ? selfPatient.id : undefined
            const action: Action = {
                title: actionTitle,
                actionType: "NAVIGATION",
                url: `${url}${patientId ? `&patientId=${patientId}` : ""}`,
            }
            if (showPatientSelectionModal && !patientId) {
                const calloutText = "Our Therapy packs are available only for yourself. This ensures data security and privacy."
                return [CareUtil.getSelfPatientSelectionModalAction(patientsList, action, actionTitle, calloutText, { formUserType: "THERAPY_USER" })]
            } else if (AppUtil.isExistingPackPurchaseForPatientSupported(userContext)) {
                return [
                    {
                        actionType: "CHECK_BUNDLE_EXISTS",
                        title: actionTitle,
                        meta: {
                            productCode,
                            patientId,
                            onSuccessAction: CareUtil.getBundleConsentInfoModal(action),
                            onFailureAction: action
                        }
                    }
                ]
            }
            return [action]
        }
    }

    public static getPsyConsentInfoModal(action: Action) {
        return {
            actionType: "SHOW_CONSENT_INFO_MODAL",
            meta: {
                title: "CONSENT REQUIRED",
                subTitle: "You are booking a consultation for a <b>family member</b>.\n\nTo protect the confidentiality of the patient, the psychiatrist will take consent from the patient <b>during the appointment</b> to share the prescription and medical records to your account. You can also choose to create a new app account for the patient and book a consultation.",
                actions: [
                    {
                        actionType: "HIDE_CONSENT_INFO_MODAL",
                        title: "CANCEL",
                        isPrimary: false
                    },
                    {
                        actionType: "UPDATE_PSY_CONSENT",
                        meta: {
                            ...action
                        },
                        title: "PROCEED",
                        isPrimary: true
                    },
                ]
            }
        }
    }

    public static getDoctorConsentInfoModal(action: Action, doctorType: DOCTOR_TYPE, doctorId: string, patientId: string): Action {
        let message = ""

        switch (doctorType) {
            case "LC":
                message = "If you want to proceed, please provide consent to share your nutrition history with your new Dietician for continuity of experience."
                break
            default:
                message = "If you want to proceed, please provide consent to share your  history with your new doctor for continuity of experience."
        }
        return {
            actionType: "SHOW_CONSENT_INFO_MODAL",
            meta: {
                doctorId,
                patientId,
                title: "CONSENT REQUIRED",
                subTitle: message,
                actions: [
                    {
                        actionType: "HIDE_CONSENT_INFO_MODAL",
                        title: "CANCEL",
                        isPrimary: false
                    },
                    {
                        actionType: "SAVE_DOCTOR_CONSENT",
                        meta: {
                            successAction: { ...action }
                        },
                        title: "PROCEED",
                        isPrimary: true
                    },
                ]
            }
        }
    }

    public static checkPsyConsentAction(action: Action, doctorId?: number) {
        return {
            actionType: "CHECK_PSY_CONSENT",
            title: action?.title,
            meta: {
                doctorId,
                onSuccessAction: action,
                onFailureAction: CareUtil.getPsyConsentInfoModal(action)
            }
        }
    }

    public static getBundleConsentInfoModal(action: Action): Action {
        return {
            actionType: "SHOW_CONSENT_INFO_MODAL",
            meta: {
                subTitle: "You already have an active pack of the same type.\nDo you want to continue?",
                actions: [
                    {
                        actionType: "HIDE_CONSENT_INFO_MODAL",
                        title: "CANCEL",
                        isPrimary: false
                    },
                    {
                        ...action,
                        title: "PROCEED",
                        isPrimary: true
                    },
                ]
            }
        }
    }

    public static getProductSummaryWidget(params: IProductSummaryParams, productType: ProductType, offerIds?: string[], userAgent?: UserAgentType): ProductSummaryWidgetV2 {
        const imageAssets: MediaAsset[] = []
        imageAssets.push({
            assetType: "IMAGE",
            assetUrl: params.imageUrl
        })
        const summaryWidget: ProductSummaryWidgetV2 = {
            widgetType: "PRODUCT_SUMMARY_WIDGET_V2",
            title: params.title,
            subTitle: params.description,
            assets: imageAssets,
            productId: params.productCode,
            offerIds,
            productType,
            hasDividerBelow: false
        }
        return summaryWidget
    }

    public static getConsultationPackPostBookingActions(
        userContext: UserContext,
        aggregatedMembershipInfo: AggregatedMembershipInfo[],
        bookingInfo: BookingDetail,
        careTeam: CareTeam[] = [],
        isMultipleBenefitFlow?: boolean,
        product?: DiagnosticProductResponse
    ): Action[] {
        if (!aggregatedMembershipInfo) {
            return
        }
        if (!aggregatedMembershipInfo[0]) {
            return
        }
        if (_.isEmpty(aggregatedMembershipInfo[0].startEndEpoch)) {
            return
        }

        const { isCompleted } = CareUtil.getConsultationPackProgressInfo(userContext, aggregatedMembershipInfo[0], false)
        if (isCompleted) {
            return
        }
        const bookingId = bookingInfo.booking.id
        const patientId = bookingInfo.booking.patientId
        const userMembershipInfos = _.flatMap(aggregatedMembershipInfo.map(item => item.userMembershipInfos))
        if (_.isEmpty(userMembershipInfos)) {
            return
        }
        const consultationProducts: { productDetail: ConsultationProduct, consultationInstruction: ConsultationInstructionResponse[] }[] = userMembershipInfos.map((userMembershipInfo: UserMembershipInfo) => {
            if (userMembershipInfo) {
                if (isMultipleBenefitFlow) {
                    if (userMembershipInfo.tickets - userMembershipInfo.ticketsConsumed < 1) {
                        return undefined
                    }
                }
                return {
                    productDetail: <ConsultationProduct>userMembershipInfo.productDetail,
                    consultationInstruction: userMembershipInfo.consultationInstruction || []
                }
            }
        }).filter(Boolean)
        if (!_.isEmpty(consultationProducts)) {
            if (consultationProducts.length > 1) {
                return this.getConsultationPackGroupBookAction(userContext, consultationProducts, patientId, bookingId, careTeam)
            } else if (consultationProducts.length === 1) {
                const { productDetail, consultationInstruction } = consultationProducts[0]
                return [this.getConsultationPackIndividualBookAction(userContext, productDetail, patientId, bookingId, consultationInstruction, careTeam)]
            }
        }
        return []
    }

    public static getConsultationPackIndividualBookAction(userContext: UserContext, product: ConsultationProduct, patientId: number, bookingId: number, instructions: ConsultationInstructionResponse[], careTeam: CareTeam[]): Action {
        let doctorId, centerId, action
        if (!_.isEmpty(careTeam)) {
            const doctorItem: CareTeam = careTeam.find((item: CareTeam) => item.doctor.doctorTypes.find(doctorTypeItem => doctorTypeItem.type.code === product.doctorType))
            if (doctorItem) {
                doctorId = doctorItem.doctor.id
                centerId = doctorItem.doctor.doctorCenterMapping[0].centerId
            }
        }
        if (doctorId && centerId) {
            const url = ActionUtil.selectCareDateV1(product.productId, undefined, bookingId, userContext)
            action = {
                actionType: "NAVIGATION",
                title: "BOOK CONSULTATION",
                url: `${url}&patientId=${patientId}&doctorId=${doctorId}&centerId=${centerId}`
            } as Action
        } else {
            action = {
                ...CareUtil.specialistListingAction(userContext, product, false, patientId, bookingId, false, undefined, false, undefined, undefined, undefined, true, "Select a specialist"),
                meta: {
                    name: "Select a specialist"
                },
                title: "BOOK CONSULTATION"
            }
        }
        if (!_.isEmpty(instructions)) {
            return {
                actionType: "SHOW_CARE_INSTRUCTION_MODAL",
                title: "BOOK CONSULTATION",
                meta: {
                    header: {
                        title: "Must follow Instructions"
                    },
                    instructions: [],
                    instructionMap: instructions,
                    action: { ...action, title: "CONTINUE" },
                    showBookLater: true
                }
            }
        }
        return action
    }

    public static getConsultationPackGroupBookAction(userContext: UserContext, consultationProducts: { productDetail: ConsultationProduct, consultationInstruction: ConsultationInstructionResponse[] }[], patientId: number, bookingId: number, careTeam: CareTeam[]): any[] {
        const actions = consultationProducts.map(consultationProduct => {
            const { productDetail, consultationInstruction } = consultationProduct
            return {
                ...this.getConsultationPackIndividualBookAction(userContext, productDetail, patientId, bookingId, consultationInstruction, careTeam),
                title: productDetail.title
            }
        })
        return [
            {
                actionType: "ACTION_LIST",
                title: "BOOK CONSULTATION",
                actions
            }
        ]
    }

    public static getBundleConsultationsWidget(userContext: UserContext, consultations: BookingDetail[]): HorizontalActionableCardListingWidget {
        const cardItems: ActionableCardItem[] = []
        consultations.map(consultation => cardItems.push(this.getConsultationActionableCardWidget(userContext, consultation)))
        return {
            widgetType: "HORIZONTAL_ACTIONAL_CARD_LISTING_WIDGET",
            title: "My Bookings",
            type: "CONSULTATION",
            cardItems,
            orientation: userContext.sessionInfo.userAgent === "DESKTOP" ? "RIGHT" : undefined,
            useNewWidgetInWeb: userContext.sessionInfo.userAgent !== "APP",
            hasDividerBelow: false
        }
    }

    public static getConsultationActionableCardWidget(userContext: UserContext, booking: BookingDetail): ActionableCardItem {
        const tz = userContext.userProfile.timezone
        const actions: Action[] = this.getConsultationActions(userContext, booking)
        const vertical = CareUtil.getVerticalForConsultation(booking.consultationOrderResponse?.consultationProduct?.doctorType)
        return {
            tag: CareUtil.getConsultationTag(booking.consultationOrderResponse.consultationUserState, booking.consultationOrderResponse.status),
            title: `Consultation with ${booking.consultationOrderResponse.doctor.name}`,
            subTitle: `for ${booking.consultationOrderResponse.patient.name}`,
            imageUrl: booking.consultationOrderResponse.doctor.displayImage,
            footer: CareUtil.getUpcomingConsultationFooterInfo(booking.consultationOrderResponse, tz),
            actions: actions,
            cardAction: {
                actionType: "NAVIGATION",
                url: ActionUtil.teleconsultationSingle(userContext, booking.booking.productCode, booking.consultationOrderResponse?.consultationProduct.urlPath, booking.consultationOrderResponse.bookingId.toString(), undefined, vertical)
            }
        }
    }

    public static getConsultationActions(userContext: UserContext, booking: BookingDetail): Action[] {
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "DESKTOP")
        const actions: Action[] = []
        const consultationOrderResponse = booking.consultationOrderResponse
        if (CareUtil.isComplteted(booking)) {
            if (!CareUtil.isPTSessionConsultation(consultationOrderResponse) && consultationOrderResponse.doctor.type !== "LIFESTYLE_COACH" && consultationOrderResponse.hasPrescription) {
                actions.push({
                    title: "Prescription",
                    actionType: "NAVIGATION",
                    icon: "PRESCRIPTION",
                    url: `curefit://carefitPrescription?tcBookingId=${booking.booking.id}&productId=${booking.booking.productCode}`
                })
            }
            if (consultationOrderResponse.doctorType === "PHYSIOTHERAPIST" && booking.consultationProduct && booking.consultationProduct.hasPlan) {
                actions.push({
                    actionType: "EMAIL_PLAN",
                    title: "Email Plan",
                    icon: "EMAIL",
                    meta: {
                        bookingId: booking.booking.id,
                        appointmentId: booking.consultationOrderResponse.id
                    }
                })
            }
        }

        if (consultationOrderResponse?.patient?.id && consultationOrderResponse.appointmentActionsWithContext && consultationOrderResponse.appointmentActionsWithContext.chatActionWithContext && consultationOrderResponse.appointmentActionsWithContext.chatActionWithContext.action.actionPermitted) {
            actions.push({
                title: "Message",
                actionType: (userAgent === "DESKTOP" || userAgent === "MBROWSER") ? "OPEN_MESSAGE_DRAWER" : "NAVIGATION",
                icon: "MESSAGE",
                url: ActionUtil.chatMessageActionUrl(consultationOrderResponse.patient.id, CareUtil.getChatChannelWithAppointmentContext(consultationOrderResponse.appointmentActionsWithContext), consultationOrderResponse.doctor.name, consultationOrderResponse.doctor.displayImage, consultationOrderResponse.doctor.qualification, booking.booking.id, undefined, "mindtherapy")
            })
        }
        if (actions.length <= 2) {
            if (consultationOrderResponse?.center?.id && consultationOrderResponse?.patient?.id && consultationOrderResponse.appointmentActionsWithContext && consultationOrderResponse.appointmentActionsWithContext.rescheduleActionWithContext && consultationOrderResponse.appointmentActionsWithContext.rescheduleActionWithContext.action.actionPermitted) {
                actions.push({
                    title: "Reschedule",
                    isEnabled: true,
                    actionType: "RESCHEDULE_TC",
                    icon: "RESCHEDULE",
                    url: `curefit://rescheduleTc?parentBookingId=${consultationOrderResponse.bookingId}&isReschedule=true`,
                    meta: {
                        "tcBookingId": consultationOrderResponse.bookingId,
                        "centerId": consultationOrderResponse.center.id,
                        "productId": booking.booking.productCode,
                        "patientId": consultationOrderResponse.patient.id
                    }
                })
            }
        }
        return actions
    }

    public static getFAQWidget(item: ManagedPlanPackInfo): WidgetView {
        if (_.isEmpty(item)) {
            return undefined
        }
        const widget: WidgetView & {
            header?: any,
            data?: any
        } = {
            widgetType: "FAQ_WIDGET",
            header: {
                seemore: {
                    actionType: "NAVIGATION",
                    title: "SEE ALL",
                    url: item.action && item.action.url ? item.action.url.includes("curefit://") ? item.action.url : `curefit://webview?uri=${item.action.url}` : undefined
                },
                title: item.title ? item.title : undefined,
                titleStyle: {
                    fontSize: 18
                }
            },
            data: !_.isEmpty(item.children) ? item.children.map(data => { return this.getfaqData(data) }) : []
        }
        return widget
    }

    public static getfaqData(data: any): any {
        return {
            questions: !_.isEmpty(data.children) ? data.children.map((item: any) => { return this.getFaqQuestions(item) }) : [],
            title: data.title ? data.title : undefined,
            subTitle: data.desc ? data.desc : undefined,
            titleStyle: {
                fontSize: 14,
            },
            action: {
                actionType: "NAVIGATION",
                url: data.action && data.action.url ? data.action.url.includes("curefit://") ? data.action.url : `curefit://webview?uri=${data.action.url}` : undefined
            }
        }
    }

    public static getFaqQuestions(item: any): any {
        return {
            supportLink: item.desc ? item.desc : undefined,
            question: item.title ? item.title : undefined
        }
    }

    public static getDoctorIdFromDeeplink(doctorId: string) {
        const doctorIdRegexPattern = /[0-9]{1,}/
        if (doctorId) {
            doctorId = String(doctorId)
            if (doctorId.includes("link=")) {
                const result = doctorId.match(doctorIdRegexPattern)
                if (!_.isEmpty(result)) {
                    return Number(result[0])
                } else {
                    return undefined
                }
            } else {
                return doctorId
            }
        }
        return doctorId
    }

    public static getAtHomeDetailTopAction(userContext: UserContext, atHomeDiagnosticOrder: AtHomeDiagnosticOrder, atHomeStepInfo: StepInfo): Action {
        const isBangalore = userContext.userProfile.cityId === "Bangalore"
        const isPhleboCallActive = atHomeStepInfo?.allowedActions?.indexOf("PHLEBO_CALLING") !== -1
        const phoneNumber = atHomeDiagnosticOrder?.phleboMobileNumber
        if (isPhleboCallActive && phoneNumber) {
            // if (userContext.sessionInfo.appVersion >= PHLEBO_CALLING_ACTION_SUPPORTED) {
            //     return {
            //         actionType: "PHLEBO_CALLING" as any,
            //         icon: "PHONE_CALL",
            //         title: " Associate",
            //         meta: {
            //             phoneNumber: phoneNumber,
            //             toastMessage: {
            //                 data: "You will be receiving a call from the Phlebotomist"
            //             }
            //         },
            //         bgColor: ["#ff3278", "#ff5972"],
            //         color: "#ffffff",
            //     }
            // } else {
            //     return {
            //         actionType: "REST_API",
            //         icon: "PHONE_CALL",
            //         title: " Associate",
            //         meta: {
            //             method: "POST",
            //             url: `care/diagnosticPhleboCall?phleboPhone=${phoneNumber}`,
            //             successMessage: "You will be receiving a call from the Phlebotomist"
            //         },
            //         bgColor: ["#ff3278", "#ff5972"],
            //         color: "#ffffff",
            //     }
            // }
            return {
                actionType: "PHONE_CALL_NAVIGATION",
                icon: "PHONE_CALL",
                title: " Associate",
                meta: {
                    phoneNumber: phoneNumber
                }
            }
        } else {
            return {
                actionType: "SHOW_ERROR_TOAST",
                icon: "PHONE_CALL",
                meta: {
                    data: "Available One Hour before your slot",
                },
                title: " Associate",
                bgColor: ["#55565b", "#55565b"],
                color: "#ffffff",
            }
        }
    }

    public static getDoctorListingPaginationViewMoreCount(doctorType: string) {
        switch (doctorType) {
            case "MIND_THERAPIST":
            case "COUPLE_THERAPIST":
            case "COVID_THERAPIST":
            case "SLEEP_THERAPIST":
                return {
                    isViewMoreEnabled: true,
                    viewCount: 7
                }
            case "LC":
            default: return {
                isViewMoreEnabled: true,
                viewCount: 25
            }
        }
    }

    public static getCareCreateCalendarEventAction(user: User, userContext: UserContext, bookingDetail: BookingDetail, tenant?: HealthfaceTenant): Action {
        const sessionInfo = userContext.sessionInfo
        if (!(AppUtil.isCalendarEventSupportedForLive(sessionInfo, user))) {
            return undefined
        }
        const alarmTime = { date: sessionInfo.osName.toLowerCase() === "ios" ? -30 : 30 }
        return {
            title: "OK",
            actionType: "CREATE_CALENDAR_EVENT",
            payload: {
                calendarData: {
                    classId: bookingDetail.booking.id,
                    title: tenant === "TRANSFORM" ? `Call with ${bookingDetail.consultationOrderResponse.doctor.name}` : "Consultation",
                    description: tenant === "TRANSFORM" ? `Join the Call from the cult.fit app` : `Join the Consultation from cult.fit app`,
                    startDate: bookingDetail?.consultationOrderResponse?.startTime,
                    endDate: bookingDetail?.consultationOrderResponse?.endTime,
                    toastText: tenant === "TRANSFORM" ? "Call added to your calendar" : "Consultation added to your calendar",
                    alarms: [alarmTime]
                },
                modalData: {
                    header: "INTRODUCING",
                    title: tenant === "TRANSFORM" ? "Add this call to your calendar?" : "Add Consultation to your calendar",
                    subTitle: "Get reminder for Consultation so you never miss it!",
                    actions: [
                        {
                            title: "ASK ME LATER"
                        },
                        {
                            title: "GIVE PERMISSION"
                        }
                    ]
                }
            }
        }
    }

    public static getDiagnosticHomeCollectionCalendarEventAction(userContext: UserContext, bookingDetail: BookingDetail): Action {
        const sessionInfo = userContext.sessionInfo
        const alarmTime = { date: sessionInfo.osName.toLowerCase() === "ios" ? -60 : 60 }
        return {
            title: "OK",
            actionType: "CREATE_CALENDAR_EVENT",
            payload: {
                calendarData: {
                    classId: bookingDetail.booking.id,
                    title: "Care.fit Home Collection",
                    description: `Phlebotomist will call you on Arrival.`,
                    startDate: bookingDetail?.diagnosticsTestOrderResponse[0]?.atHomeDiagnosticOrder.startTime,
                    endDate: bookingDetail?.diagnosticsTestOrderResponse[0]?.atHomeDiagnosticOrder.endTime,
                    toastText: "Home Collection added to your calendar",
                    alarms: [alarmTime]
                },
                modalData: {
                    header: "INTRODUCING",
                    title: "Add Home Collection to your calendar",
                    subTitle: "Get reminder for home sample collection so you don't miss it!",
                    actions: [
                        {
                            title: "ASK ME LATER"
                        },
                        {
                            title: "GIVE PERMISSION"
                        }
                    ]
                }
            }
        }
    }

    public static async getPatientListFromUserProfile(healthfaceService: IHealthfaceService, userProfile: UserProfile) {
        if (userProfile.carePatientPromise) {
            return await userProfile.carePatientPromise
        }
        if (healthfaceService) {
            return await healthfaceService.getAllPatients(userProfile.userId)
        }
        return []
    }

    public static getPackDoctorListingWidgets(userContext: UserContext, doctors: Doctor[], doctorTypes: string[], packageProduct: DiagnosticProductResponse) {
        if (_.isEmpty(doctors) && _.isEmpty(doctorTypes)) {
            return []
        }
        const doctorInfo = _.get(packageProduct, "infoSection.doctorInfo")
        if (!_.isEmpty(doctorInfo) && Array.isArray(doctorInfo)) {
            return !_.isEmpty(doctors) ? doctorTypes.map(doctorType => {
                const doctorInfoItem = doctorInfo.find((item: { doctorType: string, title: string }) => item.doctorType === doctorType)
                return CareUtil.getDoctorListWidget(
                    userContext,
                    doctorInfoItem && doctorInfoItem.title ? doctorInfoItem.title : (doctorType ? `Our ${titleCase(CareUtil.getDoctorText(doctorType))}s` : "Our Doctors"),
                    // doctors.filter(doctor => _.get(doctor, "primarySubServiceType.code") === doctorType).filter(Boolean), Commenting out since we have only one product as of now
                    doctors,
                    {},
                    undefined,
                    true
                )
            }).filter(Boolean) : []
        } else {
            const finalTitle = (doctorTypes && doctorTypes[0] ? `Our ${titleCase(CareUtil.getDoctorText(doctorTypes[0]))}s` : "Our Doctors")
            return !_.isEmpty(doctors) ? [
                CareUtil.getDoctorListWidget(
                    userContext,
                    finalTitle,
                    doctors,
                    {},
                    undefined,
                    true
                )
            ] : []
        }
    }

    public static getDoctorSubSpecialityValue(categoryFilters: AgentFilterInfos[], doctor: Doctor, isArrayOutput?: boolean, seperator?: string) {
        const map: { [key: string]: string } = {};
        (categoryFilters || []).forEach((item: AgentFilterInfos) => {
            const items = item.values.forEach((value: any) => {
                map[value.internalName] = value.displayName
            })
        })
        const subSpecialities = (doctor?.subSpecialities || []).map((item: string) => {
            return map[item]
        }).filter(Boolean)
        if (_.isEmpty(subSpecialities)) {
            return undefined
        }
        if (isArrayOutput) {
            return subSpecialities
        }
        return subSpecialities.join(seperator || ", ")
    }

    public static getNutritionistCuisineSpecialityValue(categoryFilters: AgentFilterInfos[], doctor: Doctor, isArrayOutput?: boolean) {
        const map: { [key: string]: string } = {};
        (categoryFilters || []).forEach((item: AgentFilterInfos) => {
            const items = item.values.forEach((value: any) => {
                map[value.internalName] = value.displayName
            })
        })
        const cuisineSpecialities = (doctor?.cuisines || []).map((item: string) => {
            return map[item]
        }).filter(Boolean)
        if (_.isEmpty(cuisineSpecialities)) {
            return undefined
        }
        if (isArrayOutput) {
            return cuisineSpecialities
        }
        return cuisineSpecialities.join(", ")
    }

    public static getInstructionTenant(product: ConsultationProduct) {
        if (CareUtil.isTherapyOnlyDoctorType(product.doctorType)) {
            return "MIND"
        }
        return product.tenant
    }

    public static getPartnerUpdateInfoWidget(userContext: UserContext, product: ConsultationProduct, bookingDetail: BookingDetail, patientsList: Patient[], completionAction: Action) {
        // To do remove it when launching couple therapist in web
        if (!AppUtil.isWeb(userContext) && CareUtil.isCoupleTherapist(product.doctorType) && !CareUtil.isCancelledConsultation(bookingDetail)) {
            const secondaryPatient = bookingDetail?.consultationOrderResponse?.secondaryPatientsResponse?.[0]
            const action: Action = CareUtil.isComplteted(bookingDetail) || CareUtil.isMissed(bookingDetail) || CareUtil.isConsultationStarted(bookingDetail)
                ? undefined
                : CareUtil.getPartnerPatientSelectionModalAction(bookingDetail.consultationOrderResponse.id, patientsList, completionAction, "ADD DETAILS", undefined, secondaryPatient?.id)
            if (secondaryPatient?.id) {
                return {
                    ...new PartnerInfoAddWidget(
                        "Your partner’s name and contact details",
                        secondaryPatient.name,
                        secondaryPatient.emailId || secondaryPatient.phoneNumber || secondaryPatient.userPhoneNumber,
                        action
                    ),
                    hasDividerBelow: false
                }
            }
            return {
                ...new PartnerInfoAddWidget(
                    "Please enter your partner’s name and contact details",
                    undefined,
                    undefined,
                    action
                ),
                hasDividerBelow: false
            }
        }
        return undefined
    }

    public static getWhyTherapyFeedbackWidget(product: ConsultationProduct, bookingDetail: BookingDetail, reasons: string[]) {
        if (CareUtil.isTherapyOnlyDoctorType(product.doctorType) && !_.isEmpty(reasons)) {
            return new WhyTherapyFeedbackWidget(
                `Please provide your reason to seek ${product.doctorType === "COUPLE_THERAPIST" ? "Couples Therapy" : "Therapy"}`,
                reasons,
                {
                    "actionType": "UPDATE_THERAPY_FEEDBACK_WIDGET",
                    "title": "SUBMIT",
                    meta: {
                        bookingId: bookingDetail.booking.id,
                        userId: bookingDetail.booking.userId,
                        patientId: bookingDetail.booking.patientId,
                        appointmentId: bookingDetail.consultationOrderResponse.id,
                        productCode: bookingDetail.booking.productCode,
                        subServiceType: bookingDetail.consultationOrderResponse.doctorType
                    }
                },
                " ( optional )",
                "Please let us know more (max. 20 words)"
            )
        }
        return undefined
    }

    public static getCoupleTherapyInfoWidget(product: ConsultationProduct) {
        if (CareUtil.isCoupleTherapist(product.doctorType)) {
            const action = {
                actionType: "NAVIGATION",
                disabled: false,
                url: `curefit://userform?formId=ONBOARDING`,
                icon: "RIGHT_ARROW_WHITE",
                iconStyle: { tintColor: "white" }
            } as any
            return {
                hasDividerBelow: false,
                ...new ConsultationInfoWidget([
                    {
                        title: "Wondering what to expect from your first Couples Therapy session?",
                        subtitle: undefined,
                        titleAction: action,
                        itemClickAction: action,
                        imageUrl: `/image/carefit/covidPack/coupleTherapy.png`,
                        itemContainerStyle: {
                            padding: 0,
                            backgroundColor: "transparent"
                        },
                        titleStyle: {
                            fontSize: 14,
                            color: "white",
                            fontFamily: AppFont.Regular
                        },
                        imageStyle: { width: 33, height: 50 },
                        isSelected: true
                    }
                ], undefined, undefined, {
                    paddingHorizontal: 15,
                    paddingTop: 15,
                    paddingBottom: 10,
                    backgroundColor: "#86b3ff"
                })
            }
        }
        return undefined
    }

    public static getConsultationMode(product: ConsultationProduct, bookingDetail?: BookingDetail, consultation?: Consultation) {
        if (product?.consultationMode === "INCENTRE") {
            return "incenter"
        }
        if (product && CareUtil.isSupportGroupProduct(product)) {
            return "online"
        }
        if ((consultation && CareUtil.isAudioBookingFromConsultation(consultation)) ||
            (bookingDetail && CareUtil.isAudioBookingConsultation(bookingDetail))
        ) {
            return "audio"
        }
        return "video"
    }

    public static getCancellationInfo(
        userContext: UserContext,
        bookingDetail: BookingDetail,
        isCheckout?: boolean
    ) {
        let cancellationInfo
        const tz = userContext.userProfile.timezone
        const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDateWithTimezone(tz), tz)
        const slotDate = TimeUtil.getDefaultMomentForDateString(TimeUtil.formatEpochInTimeZone(tz, Number(bookingDetail?.consultationOrderResponse?.startTime), "D MMM YYYY"), tz)
        const format = slotDate.isSame(today, "day") ? "hh:mm a" : "DD MMM, hh:mm a"
        if (bookingDetail?.consultationOrderResponse?.appointmentActionsWithContext) {
            const rescheduleEnabled = CareUtil.getRescheduleEnabled(bookingDetail)
            const cancelEnabled = CareUtil.getCancelEnabled(bookingDetail)
            const rescheduleThresholdTime = _.get(bookingDetail, "consultationOrderResponse.appointmentActionsWithContext.rescheduleActionWithContext.action.thresholdEpoch")
            const cancelThresholdTime = _.get(bookingDetail, "consultationOrderResponse.appointmentActionsWithContext.cancelActionWithContext.action.thresholdEpoch")
            if (rescheduleEnabled && cancelEnabled) {
                cancellationInfo = {
                    text: `Reschedule or Cancel before ${TimeUtil.formatEpochInTimeZone(tz, rescheduleThresholdTime > cancelThresholdTime ? cancelThresholdTime : rescheduleThresholdTime, format)}`,
                    icon: "RESCHEDULE_OR_CANCEL"
                }
            } else if (rescheduleEnabled) {
                cancellationInfo = {
                    text: `Rescheduling allowed until ${TimeUtil.formatEpochInTimeZone(tz, rescheduleThresholdTime, format)}`,
                    icon: "RESCHEDULE"
                }
            } else if (cancelEnabled) {
                cancellationInfo = {
                    text: `Cancellation allowed until ${TimeUtil.formatEpochInTimeZone(tz, cancelThresholdTime, format)}`,
                    icon: "CANCEL"
                }
            } else {
                cancellationInfo = {
                    text: `You cannot reschedule or cancel this appointment`,
                    icon: "CANCEL"
                }
            }
        } else if (isCheckout && (bookingDetail?.consultationOrderResponse?.cancellationThresholdEpoch || bookingDetail?.consultationOrderResponse?.rescheduleThresholdEpoch)) {
            const rescheduleThresholdTime = _.get(bookingDetail, "consultationOrderResponse.rescheduleThresholdEpoch")
            const cancelThresholdTime = _.get(bookingDetail, "consultationOrderResponse.cancellationThresholdEpoch")
            const currentTime = TimeUtil.getDefaultMomentForDateString(TimeUtil.todaysDateWithTimezone(tz, TimeUtil.HH_MM_SS_DATE_FORMAT), tz).valueOf()
            const rescheduleEnabled = rescheduleThresholdTime && currentTime < rescheduleThresholdTime
            const cancelEnabled = cancelThresholdTime && currentTime < cancelThresholdTime
            if (rescheduleEnabled && cancelEnabled) {
                cancellationInfo = {
                    text: `Reschedule or Cancel before ${TimeUtil.formatEpochInTimeZone(tz, rescheduleThresholdTime > cancelThresholdTime ? cancelThresholdTime : rescheduleThresholdTime, format)}`,
                    icon: "RESCHEDULE_OR_CANCEL"
                }
            } else if (rescheduleEnabled) {
                cancellationInfo = {
                    text: `Rescheduling allowed until ${TimeUtil.formatEpochInTimeZone(tz, rescheduleThresholdTime, format)}`,
                    icon: "RESCHEDULE"
                }
            } else if (cancelEnabled) {
                cancellationInfo = {
                    text: `Cancellation allowed until ${TimeUtil.formatEpochInTimeZone(tz, cancelThresholdTime, format)}`,
                    icon: "CANCEL"
                }
            } else {
                cancellationInfo = {
                    text: `You cannot reschedule or cancel this appointment`,
                    icon: "CANCEL"
                }
            }
        }
        return cancellationInfo
    }

    public static getPartnerPatientSelectionModalAction(appointmentId: number, patientsList: Patient[], action: Action, title: string, calloutText?: string, skipPatientId?: number): Action {
        const nextAction = {
            actionType: "UPDATE_OR_ADD_PARTNER_INFO",
            meta: {
                appointmentId,
                onComplete: action
            }
        }
        if (!_.isEmpty(patientsList)) {
            let patientsListFinal = patientsList.filter(patient => patient.relationship !== "Self")
            if (skipPatientId) {
                patientsListFinal = patientsListFinal.filter(patient => patient.id !== skipPatientId)
            }
            if (!_.isEmpty(patientsListFinal)) {
                return {
                    actionType: "SHOW_PATIENT_SELECTION",
                    title: title,
                    calloutText: calloutText,
                    meta: {
                        action: nextAction,
                        patientsList: patientsListFinal,
                        relations: CareUtil.getOtherRelation(),
                        emergencyContactRelations: CareUtil.getEmergencyRelations(),
                        guardianRelations: CareUtil.getGuardianRelations(),
                        reqParams: {
                            formUserType: "COUPLE_PARTNER_USER",
                            patientRelation: "Partner"
                        },
                    }
                }
            }
        }
        return {
            actionType: "ADD_PATIENT",
            title: title,
            meta: {
                action: nextAction,
                relations: CareUtil.getRelations(),
                emergencyContactRelations: CareUtil.getEmergencyRelations(),
                guardianRelations: CareUtil.getGuardianRelations(),
                reqParams: {
                    formUserType: "COUPLE_PARTNER_USER",
                    patientRelation: "Partner"
                }
            }
        }
    }

    public static getSeekingReasonSegment(doctorType: string) {
        const map = {
            "STAGE": {
                "MIND_THERAPIST": "2e25360d-18bf-4f1f-bb4a-25391063944f",
                "COUPLE_THERAPIST": "b4b329c4-38a9-4fe9-89c3-1313c3539697"
            },
            "PROD": {
                "MIND_THERAPIST": "e945f69f-eff7-4366-b6e4-64e054543921",
                "COUPLE_THERAPIST": "a23da0e4-818b-484e-9115-0c46a6ccad0c"
            }
        }
        const mapObj = process.env.ENVIRONMENT === "PRODUCTION" || process.env.ENVIRONMENT === "ALPHA" ? map["PROD"] : map["STAGE"]
        return _.get(mapObj, doctorType)
    }

    public static getChatWidget(
        userContext: UserContext,
        bookingDetail: BookingDetail,
        product: ConsultationProduct,
    ) {
        const isWeb = AppUtil.isWeb(userContext)
        const isConsultationInfoWidgetSupported = userContext.sessionInfo.appVersion >= MESSAGE_CARD_WIDGET_IN_PRODUCT_PAGE_SUPPORTED || isWeb
        let title: string
        let subtitle: string
        if (!isConsultationInfoWidgetSupported || product.tenant === "CULTFIT") {
            return undefined
        }
        let unReadMessageCount = 0
        let chatAction = CareUtil.getChatMessageAction(
            userContext,
            _.get(bookingDetail.consultationOrderResponse, "appointmentActionsWithContext.chatActionWithContext", null),
            bookingDetail.consultationOrderResponse.patient.id,
            bookingDetail.consultationOrderResponse.doctor?.name,
            CareUtil.getChatChannel(bookingDetail),
            bookingDetail.consultationOrderResponse.doctor?.displayImage,
            bookingDetail.consultationOrderResponse.doctor?.qualification,
            bookingDetail.booking.id
        )
        if (chatAction && chatAction.title.includes("(") && chatAction.title.includes(")")) {
            unReadMessageCount = Number(chatAction.title.match(/\(([^)]+)\)/)[1])
        }
        if (!chatAction) {
            return undefined
        }
        chatAction = {
            ...chatAction,
            superText: unReadMessageCount ? unReadMessageCount : undefined,
            icon: "RIGHT_ARROW_WHITE",
            title: undefined,
            disabled: false
        } as any
        const isSession = ["LC", "PHYSIOTHERAPIST"].includes(product?.doctorType) || CareUtil.isTherapyOnlyDoctorType(product?.doctorType)
        title = CareUtil.getDoctorMessageText(product.doctorType)
        subtitle = CareUtil.isComplteted(bookingDetail)
            ? CareUtil.getChatValiditybookingDetail(bookingDetail, userContext.userProfile.timezone)
            : (CareUtil.isTherapyOnlyDoctorType(product?.doctorType)
                ? undefined
                : `Share reports and images before your ${isSession ? "session" : "consultation"}`)
        return isWeb ? new NavigationCardWidget(title, subtitle, chatAction, true, undefined, userContext.sessionInfo.userAgent, undefined, undefined, undefined, "MESSAGE") : {
            ...new ConsultationInfoWidget(
                [
                    {
                        title,
                        subtitle,
                        imageUrl: "image/icons/consultationInfoWidget/chat2.png",
                        itemClickAction: chatAction,
                        titleAction: chatAction,
                        isSelected: true,
                        subtitleStyle: ConsultationInfoSubTitleStyle,
                        titleStyle: ConsultationInfoTitleStyle,
                    }
                ],
                undefined,
                undefined,
                ConsultationInfoSingleItemWidgetContainerStyle
            ),
            hasDividerBelow: false
        }
    }

    public static getChatWidgetWithDoctorCard(
        userContext: UserContext,
        bookingDetail: BookingDetail,
        product: ConsultationProduct,
    ) {
        if (product.tenant === "CULTFIT") {
            return undefined
        }
        let unReadMessageCount = 0
        let chatAction = CareUtil.getChatMessageAction(
            userContext,
            _.get(bookingDetail.consultationOrderResponse, "appointmentActionsWithContext.chatActionWithContext", null),
            bookingDetail.consultationOrderResponse.patient.id,
            bookingDetail.consultationOrderResponse.doctor?.name,
            CareUtil.getChatChannel(bookingDetail),
            bookingDetail.consultationOrderResponse.doctor?.displayImage,
            bookingDetail.consultationOrderResponse.doctor?.qualification,
            bookingDetail.booking.id
        )
        if (chatAction && chatAction.title.includes("(") && chatAction.title.includes(")")) {
            unReadMessageCount = Number(chatAction.title.match(/\(([^)]+)\)/)[1])
        }
        const isSession = ["LC", "PHYSIOTHERAPIST"].includes(product?.doctorType) || CareUtil.isTherapyOnlyDoctorType(product?.doctorType)
        const items: CheckoutInfoItem[] = []
        const { patient, doctor } = bookingDetail.consultationOrderResponse
        const isFromFlutterApp = AppUtil.isFromFlutterAppFlow(userContext)
        const name = doctor?.name
        const qualification = doctor?.qualification
        const displayImage = doctor?.displayImage
        items.push({
            title: name,
            subTitle: "View Profile",
            imageType: "MEDIUM",
            imageUrl: displayImage,
            showArrow: true,
            showDivider: chatAction ? true : false,
            titleStyleType: "MEDIUM",
            subTitleStyleType: "REGULAR",
            action: {
                actionType: "SHOW_DOCTOR_DETAILS_MODAL",
                meta: {
                    ...bookingDetail.consultationOrderResponse.doctor,
                    experience: CareUtil.isTherapyOnlyDoctorType(product?.doctorType) ? undefined : bookingDetail?.consultationOrderResponse?.doctor?.experience
                }
            },
            isCommonImageSpacing: true
        })
        if (chatAction) {
            chatAction = {
                ...chatAction,
                superText: unReadMessageCount ? unReadMessageCount : undefined,
                icon: "RIGHT_ARROW_WHITE",
                title: undefined,
                disabled: false
            } as any
            const subTitle = unReadMessageCount ? `${unReadMessageCount} unread ${unReadMessageCount > 1 ? "messages" : "message"}`
                : CareUtil.isComplteted(bookingDetail)
                    ? CareUtil.getChatValiditybookingDetail(bookingDetail, userContext.userProfile.timezone)
                    : (CareUtil.isTherapyOnlyDoctorType(product?.doctorType)
                        ? undefined
                        : `Share reports and images before your ${isSession ? "session" : "consultation"}`)
            items.push({
                title: titleCase(CareUtil.getDoctorMessageText(product.doctorType)),
                subTitle,
                imageType: isFromFlutterApp ? "MEDIUM" : "SMALL",
                imageUrl: isFromFlutterApp ? "image/vm/f34ae50c-95f5-4b19-b6bd-e1f13aa05550.png" : "image/icons/care_checkout_info_item/chat.png",
                showArrow: true,
                titleStyleType: "MEDIUM",
                subTitleStyleType: "REGULAR_GRAY",
                imageSuperText: unReadMessageCount ? `${unReadMessageCount}` : undefined,
                showSubTitleBullet: unReadMessageCount ? true : false,
                isCommonImageSpacing: true,
                action: CareUtil.getChatMessageActionWithoutContext(userContext, patient.id, name, CareUtil.getChatChannel(bookingDetail), displayImage, qualification, undefined, bookingDetail.booking.id)
            })
        }
        if (_.isEmpty(items)) {
            return undefined
        }
        return {
            widgetType: "CARE_CHECKOUT_INFO_ITEM_WIDGET",
            items,
            hasDividerBelow: false
        } as CareCheckoutInfoItemWidget
    }

    public static getPrescriptionWidget(
        userContext: UserContext,
        bookingDetail: BookingDetail,
        product: ConsultationProduct
    ) {
        const isConsultationInfoWidgetSupported = userContext.sessionInfo.appVersion >= MESSAGE_CARD_WIDGET_IN_PRODUCT_PAGE_SUPPORTED
        if (bookingDetail.consultationOrderResponse && bookingDetail.consultationOrderResponse.hasPrescription && !_.isEmpty(bookingDetail.consultationOrderResponse.prescription)) { // todo correct it to use hasPrescription once backend fixes
            let action = ConsultationInfoEmptyDisabledAction
            if (bookingDetail.consultationOrderResponse.hasDigitalDocument) {
                const url = `curefit://carefitPrescription?tcBookingId=${bookingDetail.booking.id}&productId=${product.productId}`
                action = {
                    actionType: "NAVIGATION",
                    icon: "RIGHT_ARROW_WHITE",
                    title: undefined,
                    disabled: false,
                    url
                } as any
                if (AppUtil.isTCDoctorCardUISupported(userContext)) {
                    return {
                        widgetType: "CARE_CHECKOUT_INFO_ITEM_WIDGET",
                        items: [
                            {
                                title: "Prescription Generated",
                                subTitle: "View Now",
                                imageType: "SMALL",
                                imageUrl: "image/icons/care_checkout_info_item/prescription.png",
                                showArrow: true,
                                titleStyleType: "MEDIUM",
                                subTitleStyleType: "REGULAR_GRAY",
                                action: action,
                                isCommonImageSpacing: true
                            }
                        ],
                        hasDividerBelow: false
                    } as CareCheckoutInfoItemWidget
                }
                return isConsultationInfoWidgetSupported ? {
                    ...new ConsultationInfoWidget(
                        [
                            {
                                title: "PRESCRIPTION GENERATED",
                                subtitle: "View Now",
                                imageUrl: "image/icons/consultationInfoWidget/prescription2.png",
                                itemClickAction: action,
                                titleAction: action,
                                isSelected: true,
                                subtitleStyle: ConsultationInfoSubTitleStyle,
                                titleStyle: ConsultationInfoTitleStyle,
                            }
                        ],
                        undefined,
                        undefined,
                        ConsultationInfoSingleItemWidgetContainerStyle
                    ),
                    hasDividerBelow: false
                }
                    : new NavigationCardWidget("Prescription generated", "View now", action, true, url, userContext.sessionInfo.userAgent, undefined, undefined, undefined, "PRESCRIPTION")
            } else {
                if (AppUtil.isTCDoctorCardUISupported(userContext)) {
                    return {
                        widgetType: "CARE_CHECKOUT_INFO_ITEM_WIDGET",
                        items: [
                            {
                                title: "Handwritten prescription provided for this consultation",
                                imageType: "SMALL",
                                imageUrl: "image/icons/care_checkout_info_item/prescription.png",
                                showArrow: true,
                                titleStyleType: "MEDIUM",
                                action: action,
                                isCommonImageSpacing: true
                            }
                        ],
                        hasDividerBelow: false
                    } as CareCheckoutInfoItemWidget
                }
                return isConsultationInfoWidgetSupported ? {
                    ...new ConsultationInfoWidget(
                        [
                            {
                                title: undefined,
                                subtitle: "Handwritten prescription provided for this consultation",
                                imageUrl: "image/icons/consultationInfoWidget/prescription2.png",
                                itemClickAction: ConsultationInfoEmptyDisabledAction,
                                titleAction: ConsultationInfoEmptyDisabledAction,
                                isSelected: true,
                                subtitleStyle: ConsultationInfoSubTitleStyle,
                                titleStyle: ConsultationInfoTitleStyle,
                            }
                        ],
                        undefined,
                        undefined,
                        ConsultationInfoSingleItemWidgetContainerStyle
                    ),
                    hasDividerBelow: false
                } : new NavigationCardWidget("Handwritten prescription provided for this consultation", undefined, undefined, undefined, undefined, undefined, undefined, undefined, true, "PRESCRIPTION")
            }
        } else if (bookingDetail.consultationOrderResponse?.consultationProduct?.hasPlan) {
            const action = {
                actionType: "EMAIL_PLAN",
                icon: "RIGHT_ARROW_WHITE",
                title: undefined,
                disabled: false,
                meta: {
                    bookingId: bookingDetail.booking.id,
                    appointmentId: bookingDetail.consultationOrderResponse.id
                }
            } as any
            if (AppUtil.isTCDoctorCardUISupported(userContext)) {
                return {
                    widgetType: "CARE_CHECKOUT_INFO_ITEM_WIDGET",
                    items: [
                        {
                            title: "Plan Generated",
                            subTitle: "Email Now",
                            imageType: "SMALL",
                            imageUrl: "image/icons/care_checkout_info_item/mail.png",
                            showArrow: true,
                            titleStyleType: "MEDIUM",
                            subTitleStyleType: "REGULAR_GRAY",
                            action: action,
                            isCommonImageSpacing: true
                        }
                    ],
                    hasDividerBelow: false
                } as CareCheckoutInfoItemWidget
            }
            return isConsultationInfoWidgetSupported ? {
                ...new ConsultationInfoWidget(
                    [
                        {
                            title: "PLAN GENERATED",
                            subtitle: "Email Now",
                            imageUrl: "image/icons/consultationInfoWidget/mail2.png",
                            itemClickAction: action,
                            titleAction: action,
                            isSelected: true,
                            subtitleStyle: ConsultationInfoSubTitleStyle,
                            titleStyle: ConsultationInfoTitleStyle,
                        }
                    ],
                    undefined,
                    undefined,
                    ConsultationInfoSingleItemWidgetContainerStyle
                ),
                hasDividerBelow: false
            } : new NavigationCardWidget("Plan Generated", "Email Now", action, false, undefined, userContext.sessionInfo.userAgent, undefined, undefined, undefined, "PRESCRIPTION")
        }
        return undefined
    }

    public static getLabTestWidget(
        userContext: UserContext,
        bookingDetail: BookingDetail
    ) {
        const isWeb = AppUtil.isWeb(userContext)
        const isConsultationInfoWidgetSupported = userContext.sessionInfo.appVersion >= MESSAGE_CARD_WIDGET_IN_PRODUCT_PAGE_SUPPORTED
        let title: string
        let subtitle: string
        if (!isConsultationInfoWidgetSupported && !isWeb) {
            return undefined
        }
        const age = bookingDetail.consultationOrderResponse?.patient?.formattedAge?.numOfYears
        const cityAvailability = CareUtil.getCityAvailabilityForPrescriptionDiagTests(userContext.userProfile.cityId)
        const diagnosticLabtestInfo: PrescriptionInfo["diagnosticLabtestInfo"] = bookingDetail?.consultationOrderResponse?.prescriptionInfo?.diagnosticLabtestInfo
        if (!_.isEmpty(diagnosticLabtestInfo)) {
            const { atHomeLabTests = [], inCentreLabTests = [] } = diagnosticLabtestInfo
            const totalTests: LabTest[] = [].concat(atHomeLabTests, inCentreLabTests).filter(Boolean)
            if (!age || age < 12 || !cityAvailability || _.isEmpty(totalTests)) {
                return undefined
            }
            const testCodesCsv = totalTests.map(test => test.code).join(",")
            const action = {
                actionType: "NAVIGATION",
                url: CareUtil.getTestListingActionUrl(bookingDetail.consultationOrderResponse.patient.id, bookingDetail.booking.id, "AT_HOME_SLOT", testCodesCsv, testCodesCsv),
                icon: "RIGHT_ARROW_WHITE",
                title: undefined,
                disabled: false
            } as any

            title = "BOOK PRESCRIBED TESTS"
            subtitle = "Safe Home Collection and reports will be shared with your doctor"
            if (AppUtil.isTCDoctorCardUISupported(userContext)) {
                return {
                    widgetType: "CARE_CHECKOUT_INFO_ITEM_WIDGET",
                    items: [
                        {
                            title,
                            subTitle: subtitle,
                            imageType: "SMALL",
                            imageUrl: "image/icons/care_checkout_info_item/labTest.png",
                            showArrow: true,
                            titleStyleType: "MEDIUM",
                            subTitleStyleType: "REGULAR_GRAY",
                            action: action,
                            isCommonImageSpacing: true
                        }
                    ],
                    hasDividerBelow: false
                } as CareCheckoutInfoItemWidget
            }
            return isWeb ? new NavigationCardWidget(title, subtitle, action, true, undefined, userContext.sessionInfo.userAgent, undefined, undefined, undefined, "LABTEST") : {
                ...new ConsultationInfoWidget(
                    [
                        {
                            title,
                            subtitle,
                            imageUrl: "image/icons/consultationInfoWidget/labTest2.png",
                            itemClickAction: action,
                            titleAction: action,
                            isSelected: true,
                            subtitleStyle: ConsultationInfoSubTitleStyle,
                            titleStyle: ConsultationInfoTitleStyle,
                        }
                    ],
                    undefined,
                    undefined,
                    ConsultationInfoSingleItemWidgetContainerStyle
                ),
                hasDividerBelow: false
            }
        }
        return undefined
    }

    public static getProductFeedBackWidgetV2(userContext: UserContext, feedback: Feedback) {
        return new CalloutWidget(
            "Rate your Therapy session experience",
            userContext.sessionInfo.userAgent,
            true,
            undefined,
            "How was your session?",
            {
                scene: {
                    marginHorizontal: 20,
                    paddingLeft: 22,
                    paddingRight: 22,
                    marginVertical: 15,
                    borderRadius: 10,
                },
                title: { marginBottom: 5, color: "black" },
                subTitle: { color: "#717375" },
                navArrow: { tintColor: "black" }
            },
            {
                actionType: "NAVIGATION",
                disabled: false,
                url: `curefit://userform?formId=${feedback.cfsFormId}&feedbackId=${feedback.feedbackId}&isSessionFeedbackForm=true&getPartial=false`
            },
            ["#bfe0ff", "#dbf9f3"]
        )
    }

    public static getProductFeedBackWidget(
        userContext: UserContext,
        feedbackQuestion: string,
        feedback: Feedback
    ) {
        const isV2StyleSuppported = userContext.sessionInfo.appVersion >= 8.44
        return {
            ...new ProductFeedbackWidget(
                feedbackQuestion ? feedbackQuestion.toUpperCase() : "RATE YOUR EXPERIENCE",
                feedback.feedbackId,
                undefined,
                undefined,
                undefined,
                undefined,
                isV2StyleSuppported,
                !isV2StyleSuppported ? {
                    backgroundColor: "#f4f4f4",
                    marginTop: 11,
                    marginBottom: 11,
                    marginLeft: 20,
                    marginRight: 20,
                    paddingLeft: 12,
                    paddingRight: 12,
                    borderRadius: 10,
                    paddingTop: 10,
                } : {},
                !isV2StyleSuppported ? {
                    marginTop: 10,
                    marginBottom: 10,
                } : {},
                !isV2StyleSuppported ? { color: "#1b1c1e", paddingLeft: 5, paddingTop: 4, fontSize: 12 } : { color: "#1b1c1e" }
            ),
            hasDividerBelow: false
        }
    }

    public static getTCIssueList(issuesMap: Map<string, CustomerIssueType[]>): { code: string, title: string, confirmation: string }[] {
        const issueList: { code: string, title: string, confirmation: string }[] = []
        issuesMap.get("TeleconsultationSingleBooked").forEach(customerIssueType => {
            issueList.push({
                code: customerIssueType.code,
                title: customerIssueType.subject,
                confirmation: customerIssueType.confirmation
            })
        })
        return issueList
    }

    public static getDoctorListingPageBreadcrumbs(userAgent: UserAgent, product: ConsultationProduct): IBreadCrumb[] {
        const breadcrumb: IBreadCrumb[] = [{ text: "Home", link: "/" }]
        let productName: string = (product.title || product.name)
        const consultationWordIndex = productName.lastIndexOf("Consultation")

        if ((product.tenant === "MINDFIT" || CareUtil.isMindDoctorType(product.doctorType))) {
            breadcrumb.push({ text: "Mind", link: RouteNames.Mind }, { text: "Mind Therapy", link: RouteNames.Mind })
        } else {
            breadcrumb.push({ text: "Care", link: RouteNames.Care }, { text: "Doctor Consultation", link: RouteNames.CareDoctorConsultation })
        }

        // Remove "Consultation" from the end of product
        if (consultationWordIndex !== -1 && (consultationWordIndex + "Consultation".length === productName.length)) {
            productName = productName.substr(0, consultationWordIndex).trim()
        }

        breadcrumb.push({ text: productName, link: this.getDoctorListingWebRoute(product) })

        return breadcrumb
    }

    public static getDoctorBookingPageBreadcrumbs(userAgent: UserAgent, product: ConsultationProduct, doctor: Doctor): IBreadCrumb[] {
        const breadcrumb: IBreadCrumb[] = this.getDoctorListingPageBreadcrumbs(userAgent, product)

        if (!breadcrumb.length) {
            return []
        }

        breadcrumb.push({ text: doctor.name })

        return breadcrumb
    }

    public static getLabTestPageBreadcrumbs(userContext: UserContext, product: DiagnosticProductResponse): IBreadCrumb[] {
        const breadCrumb: IBreadCrumb[] = [{ text: "Home", link: "/" }, { text: "Care", link: RouteNames.Care }, { text: "Diagnostic Tests", link: RouteNames.CareDiagnosticTests }]

        if (!AppUtil.isDesktop(userContext)) {
            return []
        }

        breadCrumb.push({ text: product.productName, link: ActionUtilV1.diagnosticTestProductPage(product.slugValue) })

        return breadCrumb
    }

    public static getCollapsibleOfferWidget(offers: OfferV2[], userContext: UserContext, homeCollectionChargesApplicable?: boolean, orientation?: Orientation, containerStyle?: ViewStyle, dontShowTnC?: boolean): ICollapsibleOfferWidget | null {
        const isWeb = userContext.sessionInfo.userAgent !== "APP"
        const uniqueOfferIds: string[] = []
        const isNewOfferImageSupported = userContext.sessionInfo.appVersion >= IS_NEW_OFFER_ICON_SUPPORTED || isWeb
        const iconType = isNewOfferImageSupported ? "/image/favourite/bookmark-black.png" : "/image/icons/cult/tick.png"
        const contentContainerStyle = isWeb ? { ...(containerStyle || {}) } : {
            marginBottom: 10,
            marginTop: 10,
            backgroundColor: "#f0f9ff",
            ...(containerStyle || {})
        }
        if (!_.isEmpty(offers)) {
            const actionType: ActionType = "SHOW_OFFERS_TNC_MODAL"
            const offerItems: ProductOffer[] = []
            offers.map(offer => {
                if (!_.isEmpty(offer)) {
                    if (uniqueOfferIds.indexOf(offer.offerId) === -1 && !offer.displayContexts?.includes("NONE")) {
                        uniqueOfferIds.push(offer.offerId)
                        if (offer.noCollectionCharge && homeCollectionChargesApplicable === false) {
                            return
                        }
                        offerItems.push({
                            title: offer.description.toString(),
                            iconType: iconType,
                            tnc: dontShowTnC ? undefined : {
                                title: "T&C",
                                action: {
                                    actionType: actionType,
                                    meta: {
                                        title: "Offer Details",
                                        dataItems: offer.tNc,
                                        url: offer.tNcUrl
                                    }
                                }
                            }
                        })
                    }
                }
            })

            if (offerItems.length > 0) {
                return {
                    widgetType: "COLLAPSIBLE_OFFER_WIDGET",
                    offerItems: offerItems,
                    dividerType: "NONE",
                    contentContainerStyle,
                    hideDashedBorder: true,
                    offersBackground: "#f0f9ff",
                    collapseLimit: 2,
                    orientation,
                    hasDividerBelow: false,
                }
            } else {
                return undefined
            }
        } else {
            return undefined
        }
    }

    public static getConsultationProductPageUrl(userContext: UserContext, productId: string, productSlug: string, bookingId?: number, appointmentId?: number, vertical?: string): string {
        return ActionUtil.teleconsultationSingle(userContext, productId, productSlug, bookingId ? bookingId.toString() : undefined, appointmentId, vertical)
    }

    public static isOfflineConsultation(bookingDetail: BookingDetail) {
        return bookingDetail.booking.subCategoryCode === "CF_INCENTRE_CONSULTATION" || bookingDetail.booking.subCategoryCode === "EXTERNAL_INCENTRE_CONSULTATION"
    }

    public static getCheckoutConsultTitle(
        consultation: ConsultationOrderResponse,
        offline: boolean,
        isExternal: boolean,
        tz: Timezone,
        isNewCheckoutView?: boolean
    ): string {
        if (CareUtil.isPTSessionConsultation(consultation)) {
            return "Personal Training Session"
        }
        if (isNewCheckoutView && consultation?.consultationProduct?.tenant !== "CULTFIT") {
            return String(_.get(consultation, "consultationProduct.name", offline ? "Consultation" : "Video call")).toLocaleLowerCase().replace("online", "").replace("teleconsultation", "").replace("consultations", "").replace("consultation", "").trim().toUpperCase()
        }
        let title = CareUtil.isNotDoctorConsulation(consultation)
            ? "Session"
            : _.get(consultation, "consultationProduct.name", offline ? "Consultation" : "Video call")
        const additionalWaitingTimeInMillis = CareUtil.isSugarfitExperienceCenterProduct(consultation.consultationProduct) ? 900000 : 0
        title += isNewCheckoutView ? "" : ` at ${TimeUtil.formatEpochInTimeZone(tz, consultation.startTime - additionalWaitingTimeInMillis, "h:mm a")}`
        if (CareUtil.isAnxietyTherapy(consultation.consultationProduct?.productCode)) {
            title = `${consultation.consultationProduct.name} at ${TimeUtil.formatEpochInTimeZone(tz, consultation.startTime, "h:mm a")}`
        }
        if (CareUtil.isLivePTSessionConsultation(consultation) || CareUtil.isLiveSGTSessionConsultation(consultation)) {
            title = consultation.consultationProduct.description
        }
        if (!_.isEmpty(consultation?.metadata?.focusAreaName)) {
            title += `\n${consultation.metadata.focusAreaName}`
        }
        return title
    }

    public static getSGTTwilioParticipants(userContext: UserContext, consultations: Consultation[], consultationResponse: ConsultationOrderResponse): Participant[] {
        const userId = userContext.userProfile.userId
        const participants: Participant[] = []
        if (!_.isEmpty(consultations) && !_.isEmpty(consultationResponse)) {
            const localParticipant = consultations.find((consultation: Consultation) => {
                return consultation.patient.curefitUserId === userId
            })
            if (consultationResponse.appointmentActionsWithContext.videoActionWithContext && !_.isEmpty(consultationResponse.appointmentActionsWithContext.videoActionWithContext.context)) {
                const twilioUsers = consultationResponse?.appointmentActionsWithContext?.videoActionWithContext?.context?.twilioCommunicationMode?.twilioUsers
                if (!_.isNil(twilioUsers)) {
                    twilioUsers.forEach((twilioUser: TwilioUser) => {
                        const userId = twilioUser.userId
                        const user = consultations.find((consultation: Consultation) => {
                            return consultation.patient.curefitUserId === userId
                        })
                        if (user) {
                            participants.push({
                                userId: user.patient.curefitUserId,
                                name: user.patient.firstName,
                                profileImage: user.patient.displayImage,
                                identity: twilioUser.converseId,
                                type: "OBSERVER"
                            })
                        }
                    })
                    const doctor = twilioUsers.find((user: TwilioUser) => {
                        return user.twilioCommUserType === "DOCTOR"
                    })
                    if (_.get(localParticipant, "doctor") && doctor) {
                        participants.push({
                            userId: localParticipant.doctor.id.toString(),
                            name: localParticipant.doctor.name,
                            profileImage: localParticipant.doctor.displayImage,
                            identity: doctor.converseId,
                            type: "CONTROLLER",
                        })
                    } else {
                        participants.push({
                            userId: doctor.userId,
                            name: "LIVE TRAINER",
                            profileImage: null,
                            identity: doctor.converseId,
                            type: "CONTROLLER",
                        })
                    }
                }
            }

        }
        return participants

    }

    public static getTwilioAction(userContext: UserContext, consultations: Consultation[], consultationResponse: ConsultationOrderResponse, productId: string): any {
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "APP")
        const userId = userContext.userProfile.userId
        const participants: TwilioParticipants[] = []
        let sgtParticipants: Participant[] = []
        let roomId, localParticipantId, trainerParticipantId
        if (!_.isEmpty(consultations)) {
            if (!_.isEmpty(consultationResponse) && consultationResponse.appointmentActionsWithContext) { // localParticipant
                const startTime = consultationResponse.startTime
                const endTime = consultationResponse.endTime
                sgtParticipants = this.getSGTTwilioParticipants(userContext, consultations, consultationResponse)
                if (consultationResponse.appointmentActionsWithContext.videoActionWithContext && !_.isNull(consultationResponse.appointmentActionsWithContext.videoActionWithContext.context)) {
                    roomId = consultationResponse?.appointmentActionsWithContext.videoActionWithContext.context.twilioCommunicationMode.modeName
                    const twilioUsers = consultationResponse?.appointmentActionsWithContext?.videoActionWithContext?.context?.twilioCommunicationMode?.twilioUsers
                    if (!_.isNil(twilioUsers)) {
                        const localUser = twilioUsers.find((user: TwilioUser) => {
                            return user.userId === userId && user.twilioCommUserType === "PATIENT"
                        })
                        const trainerTwilioUser = twilioUsers.find((user: TwilioUser) => {
                            return user.userId !== userId && user.twilioCommUserType === "DOCTOR"
                        })
                        localParticipantId = localUser.converseId
                        trainerParticipantId = trainerTwilioUser.converseId
                        participants.push({ participant: { identity: trainerParticipantId } })
                        twilioUsers.forEach((user: TwilioUser) => {
                            if (user.twilioCommUserType === "PATIENT") {
                                participants.push({ participant: { identity: user.converseId } })
                            }
                        })
                    }
                    const messages: MessageProps[] = CareUtil.getMessage()
                    if (!_.isNil(roomId) || !_.isNil(localParticipantId)) {
                        const twilioInfo = {
                            roomId: roomId,
                            localParticipantId: localParticipantId,
                            trainerParticipantId: trainerParticipantId,
                            participants: participants,
                            messages: messages,
                        }

                        let action: Action
                        if (userAgent === "APP") {
                            action = {
                                title: "JOIN NOW",
                                actionType: "NAVIGATION",
                                navigationType: "NAVIGATE_REPLACE",
                                url: "curefit://groupvideochat",
                                meta: {
                                    SGTParticipants: sgtParticipants,
                                    twilioInfo: twilioInfo,
                                    startTime: startTime,
                                    endTime: endTime
                                },
                                isEnabled: true
                            }
                            return action
                        } else {
                            action = {
                                title: "JOIN NOW",
                                actionType: "NAVIGATION",
                                navigationType: "NAVIGATE_REPLACE",
                                url: `/sgtconverse?patientId=${consultationResponse.patient.id}&tcBookingId=${consultationResponse.bookingId}&productId=${productId}`,
                                meta: {
                                    SGTParticipants: sgtParticipants,
                                    twilioInfo: twilioInfo,
                                    startTime: startTime,
                                    endTime: endTime
                                },
                                isEnabled: true
                            }
                            return action
                        }
                    } else {
                        const action: Action = {
                            title: "JOIN NOW",
                            actionType: "NAVIGATION",
                            url: "curefit://groupvideochat",
                            meta: {
                                info: consultationResponse.appointmentActionsWithContext.videoActionWithContext.action.reasonForProhibition,
                            },
                            isEnabled: false
                        }
                        return action
                    }
                }
            } else {
                const action: Action = {
                    title: "JOIN NOW",
                    actionType: "NAVIGATION",
                    url: "curefit://groupvideochat",
                    isEnabled: false
                }
                return action
            }
        }
    }
    public static getMessage(): MessageProps[] {
        return messages
    }

    public static getOnboardingAction(nextScreenTime: number, action: Action, patientId: number, userAgent: UserAgent, bookingId: number) {

        if (userAgent === "APP") {
            return {
                screenOne: {
                    title: "Place the phone in landscape mode",
                    subTitle: "Place your phone horizontally on the floor and move few steps back so that you are entirely visible to the trainer",
                    icon: CdnUtil.getCdnUrl("curefit-content/image/icons/ptAtHomeOnboarding/mobile_landscape.png"),
                    nextScreenTime: nextScreenTime,
                    skipAction: action,
                    action: {
                        actionType: "NAVIGATION",
                        navigationType: "NAVIGATE_REPLACE",
                        url: `curefit://${PageTypes.LivePtOnboardingTwo}?patientId=${patientId}&tcBookingId=${bookingId}`
                    }
                },
                screenTwo: {
                    title: "Position yourself such that your head, feet and mat are visible on the screen",
                    nextScreenTime: 10000,
                    skipAction: action,
                    action: action
                }
            }
        } else {
            return {
                screenTwo: {
                    title: "Communicate with hand gestures",
                    nextScreenTime: nextScreenTime,
                    acceptAction: {
                        title: "Thumbs-up if you got the instructions",
                        action: {
                            type: "ACCEPT"
                        }
                    },
                    rejectAction: {
                        title: "Thumbs-down if you did not",
                        action: {
                            type: "REJECT"
                        }
                    },
                    skipAction: action,
                    action: action
                },
                screenOne: {
                    title: "Position yourself such that your head, feet and mat are visible on the screen",
                    nextScreenTime: 10000,
                    skipAction: action,
                    action: {
                        actionType: "NAVIGATION",
                        navigationType: "NAVIGATE_REPLACE",
                        url: `curefit://${PageTypes.LivePtOnboardingTwo}?patientId=${patientId}&tcBookingId=${bookingId}`
                    }
                }
            }
        }
    }

    public static getTwilioQuickJoinAction(userContext: UserContext, activeConsultation: ActiveConsultationResponse, actionUrl: string): Action {
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "APP")
        const appointmentActionWithContext: AppointmentActionsWithContext = activeConsultation.appointmentActionsWithContext
        let action: Action
        if (!_.isEmpty(appointmentActionWithContext)) {
            if (userAgent === "APP") {
                if (userContext.sessionInfo.osName === "android" && userContext.sessionInfo.appVersion >= 8.70) {
                    action = {
                        actionType: "SSOWEB",
                        title: "JOIN NOW",
                        url: `sgtconverse?patientId=${activeConsultation.patientId}&tcBookingId=${activeConsultation.bookingId}&productId=${activeConsultation.productCode}&redirectToAppOnEndCall=${true}&isMWebTwilio=${true}`,
                    }
                } else if (userContext.sessionInfo.osName === "ios" && userContext.sessionInfo.appVersion >= 8.71) {
                    action = {
                        actionType: "NAVIGATION",
                        title: "JOIN NOW",
                        url: `curefit://liveptonboardingone?isTwilio=${true}&patientId=${activeConsultation.patientId}&tcBookingId=${activeConsultation.bookingId}&productId=${activeConsultation.productCode}`
                    }

                } else {
                    action = AppUtil.TwilioAppUpdateAction(userContext.sessionInfo.osName)
                }
                action.isEnabled = CareUtil.getTwilioEnabled(appointmentActionWithContext)
                return action
            } else {
                action = {
                    actionType: "NAVIGATION",
                    title: "JOIN NOW",
                    url: `/sgtconverse?patientId=${activeConsultation.patientId}&tcBookingId=${activeConsultation.bookingId}&productId=${activeConsultation.productCode}`,
                    isEnabled: CareUtil.getTwilioEnabled(appointmentActionWithContext)
                }
                return action
            }
        } else {
            action = {
                actionType: "NAVIGATION",
                title: "VIEW",
                url: actionUrl
            }
            return action
        }
    }

    public static processCart(cart: DiagnosticsCart, patientDetails?: Patient, diagnosticTestTitle?: string) {
        const patientDetail = patientDetails ? `For ${patientDetails.name} | ${patientDetails.gender} | ${patientDetails.age} Yrs` : undefined
        let productCodes = []
        productCodes = cart?.diagnosticCartItems?.map((item) => { return item.productCode })
        const extraTestsTitle = (productCodes?.length >= 2 && diagnosticTestTitle) ? " + " + (productCodes.length - 1) + (productCodes.length >= 3 ? " Items" : " Item") : ""
        const title = diagnosticTestTitle ? diagnosticTestTitle + extraTestsTitle : undefined
        return {
            productCodes,
            title,
            patientDetail
        }
    }

    public static countParameters(product: DiagnosticTestProduct) {
        const childProductList: string[] = []
        this.getLeafProductCodes(product, childProductList)
        return new Set(childProductList).size
    }

    public static getLeafProductCodes(product: DiagnosticTestProduct, childNodes: string[]) {
        if (product?.items?.length > 0) {
            product.items.map((item) => {
                this.getLeafProductCodes(item, childNodes)
            })
        } else {
            childNodes.push(product.code)
        }
    }

    public static processRecommendedAddons(recommendedAddonsResponse: DiagnosticTestProduct[], productCodes: string[]): { recommendedAddons: DiagnosticTestProduct[], addonProductCodes: string[] } {
        const recommendedAddons: DiagnosticTestProduct[] = []
        const addonProductCodes: string[] = []
        recommendedAddonsResponse.map(addon => {
            if (productCodes.indexOf(addon.code) === -1 && addonProductCodes.indexOf(addon.code) === -1) {
                recommendedAddons.push(addon)
                addonProductCodes.push(addon.code)
            }
        })
        return { recommendedAddons, addonProductCodes }
    }

    public static getItemAction(isNotLoggedIn: boolean, careCart: DiagnosticsCart, productCode: string, patientsList: Patient[]): Action {
        if (isNotLoggedIn === true) {
            return {
                actionType: "SHOW_ALERT_MODAL",
                title: "Buy Now",
                meta: {
                    title: "Login Required!",
                    subTitle: "Please login to continue",
                    actions: [{ actionType: "LOGOUT", title: "Login" }]
                }
            }
        }
        if (_.isEmpty(careCart?.diagnosticCartItems)) {
            return this.patientSelectionAction(patientsList, productCode)
        }
        if (careCart?.diagnosticCartItems?.some(item => item.productCode === productCode)) {
            return undefined
        } else {
            const patientId = careCart?.cartMetaData?.patientId
            return this.getAddToCartAction(productCode, patientId)
        }
    }

    public static patientSelectionAction(patientsList: Patient[], productCode: string): Action {
        const nextAction = this.getAddToCartAction(productCode)
        if (!_.isEmpty(patientsList)) {
            const isSelfPatientPresent = patientsList.find(patient => patient.relationship === "Self")
            const defaultRelationShip = isSelfPatientPresent ? { patientRelation: "Other" } : {}
            return {
                actionType: "SHOW_PATIENT_SELECTION",
                title: "ADD",
                meta: {
                    action: nextAction,
                    patientsList: patientsList,
                    relations: isSelfPatientPresent ? CareUtil.getOtherRelation() : CareUtil.getRelations(),
                    emergencyContactRelations: CareUtil.getEmergencyRelations(),
                    guardianRelations: CareUtil.getGuardianRelations(),
                    reqParams: {
                        formUserType: "CARE_USER",
                        ...defaultRelationShip
                    },
                }
            }
        } else {
            return {
                actionType: "ADD_PATIENT",
                title: "ADD",
                meta: {
                    action: nextAction,
                    relations: CareUtil.getRelations(),
                    emergencyContactRelations: CareUtil.getEmergencyRelations(),
                    guardianRelations: CareUtil.getGuardianRelations(),
                    reqParams: {
                        formUserType: "CARE_USER"
                    }
                }
            }
        }
    }

    public static getAddToCartAction(productCode: string, patientId?: number): Action {
        return {
            actionType: "ADD_ITEM_TO_DIAGNOSTIC_CART",
            title: "ADD",
            meta: {
                productCode,
                patientId
            }
        }
    }

    public static isPartOfConsultationPackDiabeticProducts(code: string): boolean {
        return code ? ["CONSULTATION_PACK_DIABETES_INDIVIDUAL"].includes(code) : false
    }

    public static getVitalColorInfoForUserState(covidMetricDetails: ICovidMetricsInfoResponse) {
        let imageType, vitalTitle, vitalSubtile, vitalColor, vitalBackgroundColor = "white"
        switch (covidMetricDetails.userCurrentState) {
            case ICovidMetricUserStateLevel.DOING_WELL: {
                imageType = "normal"
                vitalTitle = "On Right Track"
                vitalSubtile = "Continue the regimen recommended by the doctor. Continue reporting vitals and consult doctor everyday"
                vitalColor = "#01678a"
                vitalBackgroundColor = "#eaf9f0"
                break
            }
            case ICovidMetricUserStateLevel.BE_WATCHFUL: {
                imageType = "medium"
                vitalTitle = "Watch Out"
                vitalSubtile = "Monitor your vitals every 2 hours now and consult/message your doctor if not done so today"
                vitalColor = "#C78F02"
                vitalBackgroundColor = "#fdf2e4"
                break
            }
            case ICovidMetricUserStateLevel.EMERGENCY: {
                imageType = "danger"
                vitalTitle = "Emergency"
                vitalSubtile = "Immediate Action required. Please call an ambulance/visit a hospital."
                vitalColor = "#ed6240"
                vitalBackgroundColor = "#fcefec"
                break
            }
        }
        return { imageType, vitalTitle, vitalSubtile, vitalColor, vitalBackgroundColor }
    }

    public static getEachVitalInfo(covidMetricDetails: ICovidMetricsInfoResponse, fieldName: string, showColorCode: boolean) {
        let value = "-", lastUpdatedTime = 0
        const color = "black"
        const metricInfo: ICovidMetricInfo = _.get(covidMetricDetails, `${fieldName}`, {})
        const metricsLength = metricInfo?.metrics?.length || 0
        if (metricsLength) {
            const metric: ICovidMetricItem = metricInfo?.metrics[metricsLength - 1]
            value = String(metric?.value || value)
            lastUpdatedTime = metric?.metricDate || 0

            // color = showColorCode ? "black" : color To do updtae color as per individual value
        }
        return { value, color: color, lastUpdatedTime }
    }

    public static getVitalUpdateAction(userContext: UserContext, subUserId: number): Action {
        return {
            actionType: "NAVIGATION",
            title: "UPDATE",
            titleStyle: {
                fontSize: 12
            },
            url: `curefit://userform?formId=COVID_HOME_MONITORING&userId=${userContext.userProfile.userId}&subUserId=${subUserId}`,
            disabled: false
        }
    }

    public static getVitalSubtitleText(timezone: Timezone, lastUpdatedMoment?: momentTz.Moment) {
        let vitalSubTitle = "Update the vitals at 9am, 2pm and 7pm", isUpdateNow = false
        const updateVitalNowText = "Update the vitals at now"
        if (lastUpdatedMoment) {
            const currentMoment = TimeUtil.getMomentNow(timezone)
            const currentHour = currentMoment.hour()
            const lastUpdatedHour = lastUpdatedMoment.hour()
            const isSameDay = lastUpdatedMoment.isSame(currentMoment, "day")
            const isBeforeDay = lastUpdatedMoment.isBefore(currentMoment, "day")
            if (isBeforeDay) {
                if (currentHour < 9) {
                    vitalSubTitle = "Update the vitals at 9am"
                    isUpdateNow = false
                } else if (currentHour >= 9) {
                    vitalSubTitle = updateVitalNowText
                    isUpdateNow = true
                }
            } else if (isSameDay) {
                if (lastUpdatedHour >= 19) {
                    vitalSubTitle = "Update the vitals tomorrow"
                    isUpdateNow = false
                } else if (lastUpdatedHour >= 14) {
                    vitalSubTitle = "Update the vitals at 7pm"
                    isUpdateNow = false
                    if (currentHour >= 19) {
                        vitalSubTitle = updateVitalNowText
                        isUpdateNow = true
                    }
                } else if (lastUpdatedHour >= 9) {
                    vitalSubTitle = "Update the vitals at 2pm"
                    isUpdateNow = false
                    if (currentHour >= 14) {
                        vitalSubTitle = updateVitalNowText
                        isUpdateNow = true
                    }
                } else if (lastUpdatedHour < 9) {
                    if (currentHour < 9) {
                        vitalSubTitle = "Update the vitals at 9am"
                        isUpdateNow = false
                    } else if (currentHour >= 9) {
                        vitalSubTitle = updateVitalNowText
                        isUpdateNow = true
                    }
                }
            }
        } else {
            isUpdateNow = true // If there is no last updated, show update action
        }
        return { vitalSubTitle, isUpdateNow }
    }

    public static getCovidVitalInfoWidget(
        userContext: UserContext,
        subUserId: number,
        covidMetricDetails: ICovidMetricsInfoResponse,
        isCompleted: boolean
    ) {
        const items: ConsultationInfoItem[] = []
        const timezone = userContext.userProfile.timezone
        const vitalUpdateAction = !isCompleted ? CareUtil.getVitalUpdateAction(userContext, subUserId) : ConsultationInfoEmptyDisabledAction
        if (covidMetricDetails?.userCurrentState) {
            const { imageType, vitalTitle, vitalSubtile, vitalColor, vitalBackgroundColor } = CareUtil.getVitalColorInfoForUserState(covidMetricDetails)
            const isVitalInfoUISupported = AppUtil.isCovidHomeMonitoringNewUISupported(userContext)
            const [pulse, temp, bp, oxygenated] = [
                CareUtil.getEachVitalInfo(covidMetricDetails, "pulseStats", false),
                CareUtil.getEachVitalInfo(covidMetricDetails, "temperatureStats", false),
                CareUtil.getEachVitalInfo(covidMetricDetails, "bpStats", false),
                CareUtil.getEachVitalInfo(covidMetricDetails, "oxygenationStats", false)
            ]
            const lastUpdatedTime = Math.max(pulse.lastUpdatedTime, temp.lastUpdatedTime, bp.lastUpdatedTime, oxygenated.lastUpdatedTime)
            // const { vitalSubTitle, isUpdateNow } = this.getVitalSubtitleText(timezone, lastUpdatedTime ? TimeUtil.getDefaultMomentForDateString(TimeUtil.formatEpochInTimeZone(timezone, lastUpdatedTime, TimeUtil.HH_MM_SS_DATE_FORMAT), timezone) : undefined)
            // const isUpdateNow = false
            items.push(
                {
                    title: "VITALS",
                    subtitle: "Records vitals at 10am, 3pm and 8pm",
                    imageUrl: "/image/carefit/covidPack/heart.png",
                    isSelected: true,
                    titleAction: vitalUpdateAction,
                    itemClickAction: ConsultationInfoEmptyDisabledAction,
                    itemContainerStyle: {
                        paddingHorizontal: 15,
                    },
                    subtitleStyle: ConsultationInfoSubTitleStyle,
                    titleStyle: ConsultationInfoTitleStyle
                },
                {
                    title: vitalTitle,
                    subtitle: vitalSubtile,
                    titleAction: undefined,
                    itemClickAction: ConsultationInfoEmptyDisabledAction,
                    imageUrl: isVitalInfoUISupported ? `/image/carefit/covidPack/${imageType}_large.png` : `/image/carefit/covidPack/${imageType}_small.png`,
                    itemContainerStyle: {
                        paddingVertical: isVitalInfoUISupported ? 20 : 17.5,
                        marginTop: -15,
                        flexDirection: "row-reverse",
                        backgroundColor: vitalBackgroundColor,
                        paddingHorizontal: 15,
                        position: "relative"
                    },
                    titleStyle: {
                        fontSize: 16,
                        color: vitalColor,
                        fontFamily: AppFont.Bold
                    },
                    subtitleStyle: {
                        color: "rgba(85, 86, 91, 0.6)",
                        fontSize: 12,
                        fontFamily: AppFont.Regular
                    },
                    imageStyle: { width: 40, height: 40, marginLeft: 15 },
                    isSelected: true
                }
            )

            if (isVitalInfoUISupported) {
                items.push({
                    title: undefined,
                    subtitle: undefined,
                    imageUrl: undefined,
                    itemContainerStyle: {
                        marginTop: -10,
                        position: "relative",
                        paddingHorizontal: 15
                    },
                    infoItems: [
                        {
                            title: "PULSE",
                            subtitle: pulse.value,
                            subtitleStyle: { color: pulse.color },
                            containerStyle: { alignItems: "flex-start" },
                        },
                        {
                            title: "TEMP.(F)",
                            subtitle: temp.value,
                            subtitleStyle: { color: temp.color },
                            containerStyle: { alignItems: "center" },
                        },
                        // {
                        //     title: "BP(MM HG)",
                        //     subtitle: bp.value,
                        //     subtitleStyle: { color: bp.color },
                        //     containerStyle: { alignItems: "center" },
                        // },
                        {
                            title: "O2%",
                            subtitle: oxygenated.value,
                            subtitleStyle: { color: oxygenated.color },
                            containerStyle: { alignItems: "flex-end" },
                        },
                    ],
                    isSelected: false,
                    itemClickAction: ConsultationInfoEmptyDisabledAction,
                })
                const symptoms: ColoredCodedInfoItem[] = covidMetricDetails.userActiveSymptoms ? covidMetricDetails.userActiveSymptoms.map(item => {
                    if (item?.metric?.name) {
                        return {
                            title: item?.metric?.name,
                            subtitle: undefined,
                            titleStyle: {
                                color: "black",
                                fontFamily: AppFont.Regular
                            },
                            isBackgroundedItem: true
                        }
                    }
                    return null
                }).filter(Boolean) : []
                if (!_.isEmpty(symptoms)) {
                    items.push({
                        title: "SYMPTOMS",
                        subtitle: undefined,
                        titleStyle: {
                            fontSize: 12,
                            color: "#55565b",
                            marginBottom: 5
                        },
                        imageUrl: undefined,
                        itemContainerStyle: {
                            marginTop: -10,
                            paddingHorizontal: 15,
                        },
                        isSelected: false,
                        itemClickAction: ConsultationInfoEmptyDisabledAction,
                        infoItemContainerStyle: {
                            alignItems: "center",
                            justifyContent: "flex-start",
                            paddingBottom: 8,
                        },
                        infoItems: symptoms,
                        infoItemScrollable: true
                    })
                }
            }
            if (lastUpdatedTime) {
                items.push({
                    title: `Last Updated at ${TimeUtil.formatEpochInTimeZone(timezone, lastUpdatedTime, "ddd D MMM, hh:mm A")}`,
                    subtitle: undefined,
                    imageUrl: undefined,
                    isSelected: false,
                    itemContainerStyle: {
                        marginTop: -20,
                        paddingTop: 5,
                        paddingHorizontal: 15
                    },
                    titleStyle: {
                        color: "#8d93a0",
                        fontSize: 14,
                        fontFamily: AppFont.Regular
                    },
                    titleAction: ConsultationInfoEmptyDisabledAction,
                    // titleAction: {
                    //     ...vitalUpdateAction,
                    //     titleStyle: {
                    //         fontSize: 12
                    //     }
                    // },
                    itemClickAction: ConsultationInfoEmptyDisabledAction,
                })
            }
        } else {
            items.push({
                title: "VITALS",
                subtitle: "Records vitals at 10am, 3pm and 8pm",
                imageUrl: "/image/carefit/covidPack/heart.png",
                itemContainerStyle: {
                    paddingHorizontal: 15
                },
                isSelected: true,
                titleAction: vitalUpdateAction,
                itemClickAction: ConsultationInfoEmptyDisabledAction
            })
        }

        if (!_.isEmpty(items)) {
            return new ConsultationInfoWidget(items, undefined, undefined, { paddingHorizontal: 0 })
        }
        return undefined
    }

    public static isCovidSpecialist(product: ConsultationProduct) {
        return ["COVID_SPECIALIST_PHYSICIAN_WITH_FOLLOWUPS", "COVID_SPECIALIST_PHYSICIAN_WITH_FOLLOW_UPS"].includes(product?.doctorType)
    }

    public static getIfChronicCareFromUserContext(userContext: UserContext) {
        if (userContext.sessionInfo.orderSource === "SUGARFIT_APP" || userContext.sessionInfo.orderSource === "ULTRAFIT_APP" || userContext.sessionInfo.orderSource === "SUGARFIT_WEBSITE" || userContext.sessionInfo.orderSource === "ULTRAFIT_WEBSITE") {
            return true
        }
        return false
    }

    public static getCrossSellPageTypeFromProduct(productType: ProductType, isPack: boolean): string {
        if (isPack) {
            if (productType === "FITNESS") {
                return "CULT_MEMBERSHIP_ORDER_CONFIRMATION"
            } else if (productType === "LIVE_FITNESS" || productType === "CF_LIVE") {
                return "LIVE_MEMBERSHIP_ORDER_CONFIRMATION"
            }
        } else if (productType === "FITNESS") {
            return "CULT_CLASS_BOOKING_CONFIRMATION"
        } else if (productType === "LIVE_FITNESS" || productType === "CF_LIVE") {
            return "LIVE_CLASS_BOOKING_CONFIRMATION"
        }
        return undefined
    }

    public static getActionForButton(userContext: UserContext, parentBookingId: number, productId: string, focusAreaId: number, isBooked: boolean, patientId?: string, startTime?: number, endTime?: number) {
        if (isBooked) {
            const action: Action = {
                title: "BOOK",
                actionType: "SHOW_ALERT_MODAL",
                meta: {
                    title: "Overlapped Booking",
                    subTitle: "You have another class booked at this time slot.",
                    actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
                }
            }
            return action
        } else {
            const action: Action = {
                title: "BOOK",
                actionType: "BOOK_SGT_CLASS",
                meta: {
                    parentBookingId,
                    productId,
                    focusAreaId,
                    patientId,
                    startTime,
                    endTime
                }
            }
            return action
        }
    }

    public static getWorkoutName(workoutName: string, focusAreaName: string) {
        if (_.isNull(focusAreaName) || workoutName === focusAreaName) {
            return workoutName
        }
        return workoutName + ": " + focusAreaName
    }

    public static getCalorieString(calorieCountMap: any, wodId: string) {
        let calorieString = ""
        if (calorieCountMap) {
            calorieString = calorieCountMap[wodId]
        } else {
            calorieString = "600"
        }
        calorieString = calorieString + " kcal"
        return calorieString
    }

    public static getClassesForDate(userContext: UserContext, parentBookingId: number, productId: string, slotResponse: TCAvailableSlotsDetails, tcAvailableSlotsMetadata: TCAvailableSlotsMetadata, DateStringFromStartTime: string, patientId: string, calorieCountMap: any, consultationProduct: any) {
        const classes: SGTClass[] = []
        const dateWorkoutDetailsMap = tcAvailableSlotsMetadata.dateWorkoutDetailsMap
        if (_.isNull(slotResponse.slotInfoMetadata)) {
            if (dateWorkoutDetailsMap.hasOwnProperty(DateStringFromStartTime)) {
                const dateWorkoutList = dateWorkoutDetailsMap[DateStringFromStartTime]
                dateWorkoutList.forEach((allclass: SGTWorkoutDetails) => {
                    const newclassObj: SGTClass = {
                        ...slotResponse,
                        ...allclass,
                        parentBookingId,
                        productId,
                        doctorMetaList: null,
                        doctorIdList: null,
                        calorie: this.getCalorieString(calorieCountMap, allclass.wodId),
                        icon: this.getIconForSGT(allclass.workoutName, allclass.focusAreaId),
                        preferredDoctorIdList: null,
                        format: [productId],
                        workoutTitle: this.getWorkoutName(allclass.workoutName, allclass.focusAreaName),
                        action: {
                            title: "JOIN",
                            actionType: "NAVIGATION",
                            url: `curefit://carefittc?id=${productId}&vertical=CULTFIT&parentBookingId=${parentBookingId}&productId=${productId}&wodId=${allclass.wodId}&startTime=${slotResponse.startTime}&endTime=${slotResponse.endTime}&focusAreaId=${allclass.focusAreaId}&patientId=${patientId}&isPreBookingDetail=true`
                        },
                        sgtConsultationInventoryBookingDetails: slotResponse.sgtconsultationInventoryBookingDetails,
                        duration: consultationProduct.productSpecs?.actualClassDuration ? consultationProduct?.productSpecs?.actualClassDuration : Math.floor((slotResponse.endTime - slotResponse.startTime) / 60000),
                        buttonAction: this.getActionForButton(userContext, parentBookingId, productId, allclass.focusAreaId, slotResponse.bookedByPatient, patientId, slotResponse.startTime, slotResponse.endTime)
                    }
                    classes.push(newclassObj)
                })
            }
        } else {
            const slotInfoMetadata: ConsultationSlotInfoMetadata = slotResponse.slotInfoMetadata
            slotInfoMetadata.workoutDetails.forEach((classMetaItem: SGTWorkoutDetails) => {
                const newclassObj: SGTClass = {
                    ...classMetaItem,
                    ...slotResponse,
                    parentBookingId,
                    productId,
                    doctorMetaList: null,
                    doctorIdList: null,
                    preferredDoctorIdList: null,
                    calorie: this.getCalorieString(calorieCountMap, classMetaItem.wodId),
                    format: [productId],
                    workoutTitle: this.getWorkoutName(classMetaItem.workoutName, classMetaItem.focusAreaName),
                    icon: this.getIconForSGT(classMetaItem.workoutName, classMetaItem.focusAreaId),
                    sgtConsultationInventoryBookingDetails: slotResponse.sgtconsultationInventoryBookingDetails,
                    action: {
                        title: "JOIN",
                        actionType: "NAVIGATION",
                        url: `curefit://carefittc?id=${productId}&vertical=CULTFIT&parentBookingId=${parentBookingId}&productId=${productId}&wodId=${classMetaItem.wodId}&startTime=${slotResponse.startTime}&endTime=${slotResponse.endTime}&focusAreaId=${classMetaItem.focusAreaId}&patientId=${patientId}&isPreBookingDetail=true`
                    },
                    duration: consultationProduct.productSpecs?.actualClassDuration ? consultationProduct?.productSpecs?.actualClassDuration : Math.floor((slotResponse.endTime - slotResponse.startTime) / 60000),
                    buttonAction: this.getActionForButton(userContext, parentBookingId, productId, classMetaItem.focusAreaId, slotResponse.bookedByPatient, patientId, slotResponse.startTime, slotResponse.endTime)
                }
                classes.push(newclassObj)
            })
        }
        return classes
    }

    public static getIconForSGT(workoutName: string, focusAreaId: number) {
        const address = "image/live-pt/sgt-focus-area-images/"
        switch (workoutName) {
            case "S&C":
                if (focusAreaId === 1) return address + "<EMAIL>"
                if (focusAreaId === 2) return address + "<EMAIL>"
                return address + "<EMAIL>"
            case "Yoga": return address + "<EMAIL>"
            case "Boxing": return address + "<EMAIL>"
            case "Dance Fitness": return address + "<EMAIL>"
            case "HRX with Single Dumbell": return address + "<EMAIL>"
            default: return address + "<EMAIL>"
        }
    }

    public static getNewSgtBookingResponse(userContext: UserContext, classByDateMap: { [id: string]: { [id: number]: any } }, productId: string, availableSlotsResponse: TCAvailableSlotsResponse, parentBookingId: number, patientId: string, availableProductTags?: AvailableProductTags[], calorieCountMap?: any) {
        const tz = userContext.userProfile.timezone

        const formattedAvailableSlotResponseList = availableSlotsResponse.formattedAvailableSlotResponseList
        const consultationProduct = availableSlotsResponse.consultationProduct
        // formattedAvailableSlotResponseList.sort((a, b) => (a.startTime > b.startTime) ? 1 : ((b.startTime > a.startTime) ? -1 : 0))
        formattedAvailableSlotResponseList.forEach((slotResponse: TCAvailableSlotsDetails) => {
            const DateStringFromStartTime = TimeUtil.formatEpochInTimeZone(tz, slotResponse.startTime)
            if (classByDateMap.hasOwnProperty(DateStringFromStartTime)) {
                const classes = this.getClassesForDate(
                    userContext,
                    parentBookingId,
                    productId,
                    slotResponse,
                    availableSlotsResponse.metadata,
                    DateStringFromStartTime,
                    patientId,
                    calorieCountMap,
                    consultationProduct
                )
                const classByDateMapForDate = classByDateMap[DateStringFromStartTime]
                if (classByDateMapForDate.hasOwnProperty(slotResponse.startTime)) {
                    const classlist = classByDateMapForDate[slotResponse.startTime]
                    classlist.push(...classes)
                    classByDateMapForDate[slotResponse.startTime] = classlist
                    classByDateMap[DateStringFromStartTime] = classByDateMapForDate
                } else {
                    const classByTimeMap: { [id: number]: any } = {}
                    classByDateMapForDate[slotResponse.startTime] = classes
                    classByDateMap[DateStringFromStartTime] = classByDateMapForDate
                }

            } else {
                const classByTimeMap: { [id: number]: any } = {}
                classByTimeMap[slotResponse.startTime] = this.getClassesForDate(
                    userContext,
                    parentBookingId,
                    productId,
                    slotResponse,
                    availableSlotsResponse.metadata,
                    DateStringFromStartTime,
                    patientId,
                    calorieCountMap,
                    consultationProduct
                )
                classByDateMap[DateStringFromStartTime] = classByTimeMap
            }
        })
        return classByDateMap
    }

    public static modifyResponse(userContext: UserContext, classByDateMapRaw: { [id: string]: { [id: number]: any } }) {
        const tz = userContext.userProfile.timezone
        const classByDateMap: { [id: string]: any } = {}
        const KeysList = Object.keys(classByDateMapRaw)
        KeysList.sort((a, b) => (a > b) ? 1 : ((b > a) ? -1 : 0))
        KeysList.forEach((date: string) => {
            const classByDate = classByDateMapRaw[date]
            const classByTimeList: any = []
            Object.keys(classByDate).forEach((time: string) => {
                const TimeStringFromStartDate = TimeUtil.formatEpochInTimeZone(tz, Number(time), "hh:mm A")
                const value = classByDate[Number(time)]
                classByTimeList.push({
                    id: TimeStringFromStartDate,
                    classes: value
                })
            })
            classByDateMap[date] = {
                id: date,
                classByTimeList,
                formats: [
                    "ALL",
                    "CONS_CULT_LIVE_SGT_DF",
                    "CONS_CULT_LIVE_SGT_SnC",
                    "CONS_CULT_LIVE_SGT_YOGA",
                    "CONS_CULT_LIVE_SGT_BOXING",
                    "CONS_CULT_LIVE_SGT_HRX"
                ],
            }
        })
        return classByDateMap
    }

    public static getworkoutFormatFilters(productId: string) {
        const workoutFormatFilters = [
            {
                id: "ALL",
                name: "ALL",
                selected: productId === "SGT"
            },
            {
                id: "CONS_CULT_LIVE_SGT_DF",
                name: "DANCE",
                selected: productId === "CONS_CULT_LIVE_SGT_DF"
            },
            {
                id: "CONS_CULT_LIVE_SGT_SnC",
                name: "S&C",
                selected: productId === "CONS_CULT_LIVE_SGT_SnC"
            },
            {
                id: "CONS_CULT_LIVE_SGT_YOGA",
                name: "YOGA",
                selected: productId === "CONS_CULT_LIVE_SGT_YOGA"
            },
            {
                id: "CONS_CULT_LIVE_SGT_BOXING",
                name: "BOXING",
                selected: productId === "CONS_CULT_LIVE_SGT_BOXING"
            },
            {
                id: "CONS_CULT_LIVE_SGT_HRX",
                name: "HRX",
                selected: productId === "CONS_CULT_LIVE_SGT_HRX"

            }
        ]
        return workoutFormatFilters
    }

    public static async getDaysRemainingInBundleOrder(healthfaceService: IHealthfaceService, userContext: UserContext, subcategoryCode: SUB_CATEGORY_CODE, tenant: HealthfaceTenant): Promise<{hasBundleOrder: boolean, daysLeft: number}> {
     const response =  await healthfaceService.getActiveBundleOrders(userContext.userProfile.userId, "BUNDLE", subcategoryCode, true, tenant)
     if (response.length > 0) {
         response.sort((a, b) => {
             return a.endDate > b.endDate ? -1 : 1
         })
         const todaysDate = TimeUtil.todaysDateWithTimezone(userContext.userProfile.timezone)
         const endDate = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, response[response.length - 1].endDate)
         const daysLeft = TimeUtil.diffInDaysReal(userContext.userProfile.timezone, todaysDate, endDate)
         return {hasBundleOrder: true, daysLeft}
     }
     return {hasBundleOrder: false, daysLeft: 0}
    }

    public static removeDoctorUnusedField(doctorWithAction: any, doNotDeleteConsultationProduct?: boolean) {
        // deleted it to reduce the response sent
        if (!doNotDeleteConsultationProduct) {
            delete doctorWithAction?.consultationProducts
            delete doctorWithAction?.consultationSellableProductUserProductInfos
        }
        delete doctorWithAction?.agentAttributeResponses
        delete doctorWithAction?.doctorRoomMapping
        delete doctorWithAction?.doctorShifts
        delete doctorWithAction?.phoneNumber
        delete doctorWithAction?.emailId
    }

    public static getConsultationClpListPage() {
        return "curefit://listpage?pageId=clpconsultation"
    }

    public static getLabTestClpListPage() {
        return "curefit://listpage?pageId=clphcu"
    }

}

export default CareUtil
