import {
    CFLiveMembershipType,
    CFLiveProduct,
    DIYFitnessProductExtended,
    FitnessProgramProductResponse
} from "@curefit/diy-common"
import { ILivePackOffer, LivePack, LivePackPickerWidget, ProductPriceWithBreakup } from "../digital/ILivePackPage"
import LiveUtil, { CULT_HOME } from "./LiveUtil"
import { Action, StyledTouchableTextWidget, TextWidget } from "@curefit/apps-common"
import { OrderProduct, OrderSource } from "@curefit/order-common"
import AppUtil from "./AppUtil"
import { Product, ProductPrice } from "@curefit/product-common"
import { Membership } from "@curefit/membership-commons"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { BannerCarouselWidget } from "../page/PageWidgets"
import * as _ from "lodash"
import { DiscountBreakup, LivePricesResponse, OfferV2 } from "@curefit/offer-common"
import { WidgetType } from "@curefit/vm-common"
import { UserContext } from "@curefit/vm-models"
import { Session } from "@curefit/userinfo-common"
import { OrderCreate } from "@curefit/oms-api-client"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { IDIYFulfilmentService } from "@curefit/diy-client"
import { ICultBusiness } from "../cult/CultBusiness"
import { IdVsValueMap } from "../digital/DigitalSocialLeagueTabPageViewBuilder"

export class LivePackUtil {

    public static getLivePackPickerWidget(userContext: UserContext, cfLivePacks: CFLiveProduct[], osName: string, livePricesResponse: LivePricesResponse, offerIdOfferMap: { [offerId: string]: OfferV2 }, isMonetisationEnabled: boolean,
        source: string, selectedProductId?: string, isTrial?: boolean, widgetType?: WidgetType, bucketId?: string, livePriceBreakupResponse?: Record<string, any>, isPartOfGstSegment?: boolean ) {
        const isIAPEnabled = AppUtil.isIAPEnabled(userContext)
        const isInternationalApp = AppUtil.isInternationalApp(userContext)
        const livePackPickerWidget: LivePackPickerWidget = new LivePackPickerWidget("LIVE_PACK_PICKER_WIDGET")
        livePackPickerWidget.title = isInternationalApp ? "cure.fit Membership" : CULT_HOME
        livePackPickerWidget.subTitle = "CHOOSE SUBSCRIPTION PLAN"
        livePackPickerWidget.tag = "NEW"
        livePackPickerWidget.widgetType = widgetType ? widgetType : "LIVE_PACK_PICKER_WIDGET"
        livePackPickerWidget.rootStyle = { marginTop: -40 }
        livePackPickerWidget.showRestore = AppUtil.isAppleIAPEnabled(userContext)
        livePackPickerWidget.showCheck = (osName !== "ios" || isIAPEnabled) && isMonetisationEnabled && !isTrial
        livePackPickerWidget.showOffers = true
        livePackPickerWidget.restoreAction = {
            actionType: "RESTORE_PURCHASE",
            title: "RESTORE PURCHASES",
            analyticsData: {
                eventKey: "applerestoreclicked",
                eventData: {}
            }
        }
        let maxIndex = 0
        let maxPrice = -1
        let trialIndex = -1
        livePackPickerWidget.packs = this.filterPacks(userContext, cfLivePacks, true).map((cfLiveProduct, index) => {
            const livePack = new LivePack()
            livePack.productId = cfLiveProduct.productId
            livePack.title = cfLiveProduct.title
            livePack.duration = cfLiveProduct.subscriptionOptions.duration
            livePack.price = cfLiveProduct.price
            if (cfLiveProduct.membershipType === CFLiveMembershipType.TRIAL) {
                livePack.title = cfLiveProduct.subTitle
                trialIndex = index
            }
            else {
                const prices = livePricesResponse.priceMap[cfLiveProduct.productId]
                const livePackOffers: ILivePackOffer[] = []
                if (prices) {
                    livePack.price = {
                        mrp: prices.mrp,
                        listingPrice: prices.sellingPrice,
                        currency: cfLiveProduct.price.currency,
                        showPriceCut: cfLiveProduct.price.showPriceCut,
                        discountText: "" + prices.discount,
                    ...(livePriceBreakupResponse ? {priceBreakup : livePriceBreakupResponse[cfLiveProduct?.productId]} : {})
                    }
                    if (!_.isEmpty(prices.breakup)) {
                        prices.breakup.forEach((discountBreakup: DiscountBreakup) => {
                            const offer: OfferV2 = offerIdOfferMap[discountBreakup.offerId]
                            const isOfferEmpty = _.isEmpty(offer)
                            if (!isOfferEmpty && !_.isEmpty(offer.addons)) {
                                offer.addons.forEach(addOn => {
                                    if (addOn && addOn.description && !_.isEmpty(addOn.description.trim())) {
                                        const tncAction: Action = (addOn.tncUrl || !_.isEmpty(addOn.tncUrl)) ? {
                                            actionType: "SHOW_OFFERS_TNC_MODAL",
                                            title: "T&C",
                                            meta: {
                                                title: "Terms & Conditions",
                                                url: addOn.tncUrl
                                            },
                                        } : undefined
                                        /*if (offer.offerId === "uoK72UQKc") {
                                            tncAction = (addOn.tncUrl || !_.isEmpty(addOn.tncUrl)) ? {
                                                actionType: (osName === "android" || osName === "ios") ? "NAVIGATION" : "SHOW_OFFERS_TNC_MODAL",
                                                title: "T&C",
                                                url: (osName === "android" || osName === "ios") ? "curefit://listpage?pageId=CF_live_bau_sale" : "https://www.cure.fit/lp/CF_live_bau_sale",
                                                meta: {
                                                    title: "Terms & Conditions",
                                                    url: "https://s3.ap-south-1.amazonaws.com/vm-html-pages/Privilege%20Bundle-12f11ca0-1346-4f62-8247-c8e934f1b5e9.html"
                                                },
                                            } : undefined
                                        }*/
                                        const livePackOfferAddOn: ILivePackOffer = {
                                            title: addOn.description,
                                            tncAction: tncAction
                                        }
                                        livePackOffers.push(livePackOfferAddOn)
                                    }
                                })
                            }
                            else if (!isOfferEmpty && offer.description) {
                                const tncAction: Action = (offer.tNcUrl || !_.isEmpty(offer.tNc)) ? {
                                    actionType: "SHOW_OFFERS_TNC_MODAL",
                                    title: "T&C",
                                    meta: {
                                        title: "Terms & Conditions",
                                        url: offer.tNcUrl
                                    },
                                } : undefined
                                /*if (offer.offerId === "uoK72UQKc") {
                                    tncAction = (offer.tNcUrl || !_.isEmpty(offer.tNc)) ? {
                                        actionType: (osName === "android" || osName === "ios") ? "NAVIGATION" : "SHOW_OFFERS_TNC_MODAL",
                                        title: "T&C",
                                        url: (osName === "android" || osName === "ios") ? "curefit://listpage?pageId=CF_live_bau_sale" : "https://s3.ap-south-1.amazonaws.com/vm-html-pages/Privilege%20Bundle-12f11ca0-1346-4f62-8247-c8e934f1b5e9.html",
                                        meta: {
                                            title: "Terms & Conditions",
                                            url: "https://s3.ap-south-1.amazonaws.com/vm-html-pages/Privilege%20Bundle-12f11ca0-1346-4f62-8247-c8e934f1b5e9.html"
                                        },
                                    } : undefined
                                }*/
                                const livePackOffer: ILivePackOffer = {
                                    title: offer.description,
                                    tncAction: tncAction
                                }
                                livePackOffers.push(livePackOffer)
                            }
                        })
                    }
                }
                livePack.perMonthPrice = "" + this.getPerMonthPrice(cfLiveProduct, livePack.price, isPartOfGstSegment)
                // livePack.offerText = "" + this.getPerMonthPrice(cfLiveProduct, livePack.price) + "/month"
                livePack.offers = livePackOffers
                livePack.offerSummary = livePackOffers.length > 1 ? "+" + (livePackOffers.length - 1) + " offers" : undefined
                const heroOffer = livePackOffers.length > 0 ? livePackOffers[0].title : undefined
                livePack.heroOffer = livePackOffers.length > 1 ? heroOffer + " | " : heroOffer
            }
            livePack.packAction = this.getPackAction(userContext, cfLiveProduct, osName, livePack.price, source, bucketId)
            if (livePack.price.listingPrice > maxPrice) {
                maxPrice = livePack.price.listingPrice
                maxIndex = index
            }
            if (osName === "ios" || isIAPEnabled) {
                livePack.price.mrp = livePack.price.listingPrice
                livePack.offers = []
                livePack.heroOffer = ""
                livePack.offerSummary = ""
            }
            return livePack
        })
        let selectedIndex = trialIndex >= 0 ? trialIndex : maxIndex
        const packIndex = cfLivePacks.findIndex(pack => pack.productId === selectedProductId)
        selectedIndex = selectedProductId && packIndex >= 0 ? packIndex : selectedIndex
        let productId
        let selectedPackAction
        const selectedPack = livePackPickerWidget.packs[selectedIndex]
        if (selectedPack) {
            productId = selectedPack.productId
            selectedPackAction = selectedPack.packAction
            if (!isMonetisationEnabled) {
                productId = undefined
                selectedPackAction = undefined
            }
        }
        return {
            livePackPickerWidget: livePackPickerWidget,
            selectedProductId: productId,
            selectedPackAction: selectedPackAction
        }
    }

    public static getPerMonthPrice(pack: CFLiveProduct, price: ProductPriceWithBreakup, isExcludingTax?: boolean) {
        if (pack.subscriptionOptions.duration.unit === "MONTH") {
            if (isExcludingTax && price?.priceBreakup?.basePrice) {
                return Math.floor(price?.priceBreakup?.basePrice / pack.subscriptionOptions.duration.value)
            }
            return Math.floor(price.listingPrice / pack.subscriptionOptions.duration.value)
        }
        if (pack.subscriptionOptions.duration.unit === "YEAR") {
            if (isExcludingTax && price?.priceBreakup?.basePrice) {
                return Math.floor(price?.priceBreakup?.basePrice / (12 * pack.subscriptionOptions.duration.value))
            }
            return Math.floor(price.listingPrice / (12 * pack.subscriptionOptions.duration.value))
        }
        return ""
    }

    public static getOfferText(price: ProductPrice) {
        if (price.listingPrice === price.mrp) {
            return ""
        }
        return "SAVE " + Math.floor(((price.mrp - price.listingPrice) * 100) / price.mrp) + "%"
    }

    public static getPackAction(userContext: UserContext, product: CFLiveProduct, osName: string, price: ProductPrice, source: string, bucketId?: string): Action {
        const isLoggedIn: boolean = userContext.sessionInfo.isUserLoggedIn
        const orderProduct: OrderProduct = {
            productId: product.productId,
            productType: "CF_LIVE",
            quantity: 1,
            appleIAPProductDetails: product.appleIAPProductDetails,
            androidIAPProductDetails: product.androidIAPProductDetails,
            option: {
                isPack: true,
            }
        }
        const createOrderPayload: {
            orderProducts: OrderProduct[],
            useFitCash: boolean,
            offersVersion: number
        } = {
            orderProducts: [orderProduct],
            useFitCash: osName !== "ios",
            offersVersion: 3
        }
        const payText = AppUtil.isInternationalApp(userContext) ? "PAY" : ("PAY " + AppUtil.getCurrencySymbol(price.currency) + price.listingPrice)
        if (product.membershipType === CFLiveMembershipType.TRIAL && AppUtil.isMandatoryOnboardingExpEnabled(userContext, bucketId)) {
            return {
                actionType: "NAVIGATION",
                title: "ACTIVATE FREE TRIAL",
                url: "curefit://fl_form?formId=Home_Guidance_Onboarding_v2&fillPreviousResponse=true&prefillFormId=Home_Guidance_Onboarding&activateTrial=true"
            }
        }
        return {
            title: price.listingPrice > 0 ? payText : "ACTIVATE FREE TRIAL",
            payload: createOrderPayload,
            actionType: isLoggedIn ? "GET_LIVE_PACK" : "SHOW_ALERT_MODAL",
            url: "curefit://payment",
            source,
            ...(isLoggedIn ? {} : {
                meta: {
                    title: "Login Required!",
                    subTitle: "Please login to continue",
                    actions: [{  actionType: "LOGOUT", title: "Login" }]
                }
            })
        }
    }

    public static getProductMap(products: Product[]) {
        const productMap: { [productId: string]: Product } = {}
        products.forEach((product) => {
            productMap[product.productId] = product
        })
        return productMap
    }

    public static getProductV2Map(products: FitnessProgramProductResponse[]) {
        const productMap: { [productId: string]: FitnessProgramProductResponse } = {}
        products.forEach((product) => {
            productMap[product.productId] = product
        })
        return productMap
    }

    public static getDiyEnergyMeterSupportedMap(products: DIYFitnessProductExtended[], requiredFeatures = ["ENERGY", "SCORE"], isDiyEMSupported: boolean, isLegacyVideoBackgroundSuppported: boolean): IdVsValueMap<boolean> {
        const diyEnergyMeterSupportedMap: IdVsValueMap<boolean> = {}
        for (const product of products) {
            const allRequiredFeaturesExist = _.intersection(product.features, requiredFeatures).length === requiredFeatures.length
            diyEnergyMeterSupportedMap[product.productId] = allRequiredFeaturesExist && ((product.legacyEMContent && isLegacyVideoBackgroundSuppported) || (!product.legacyEMContent && isDiyEMSupported))
        }
        return diyEnergyMeterSupportedMap
    }

    public static getFAQWidget(userContext: UserContext): any {
        return {
            "widgetType": "FAQ_WIDGET",
            "showDivider": false,
            isFullWidth: false,
            widgetStyle: {
                paddingHorizontal: 10,
            },
            "data": [
                {
                    "title": "Membership",
                    "subTitle": "9 Answers",
                    "action": {
                        "actionType": "NAVIGATION",
                        "url": `curefit://webview?uri=${encodeURIComponent("https://support.cure.fit/support/solutions/folders/25000032396")}`
                    },
                    "webIcon": "image/vm/caa57dc1-4341-4f3e-a981-6de2ec7e2325.png",
                    "questions": [
                        {
                            "supportLink": "http://support.cure.fit/support/solutions/articles/25000023140-what-is-a-cure-fit-live-membership-",
                            "question": `What is a ${LiveUtil.getLiveBranding(userContext)} membership?`
                        },
                        {
                            "question": "What are the services accessible to a cure fit live member?",
                            "supportLink": "http://support.cure.fit/support/solutions/articles/25000023151-what-are-the-services-accessible-to-a-cure-fit-live-member"
                        }
                    ]
                },
                {
                    "title": "Live Classes",
                    "subTitle": "15 Answers",
                    "action": {
                        "actionType": "NAVIGATION",
                        "url": `curefit://webview?uri=${encodeURIComponent("https://support.cure.fit/support/solutions/folders/25000032397")}`
                    },
                    "webIcon": "image/vm/90a083f5-ab0c-4a03-b76f-cc2870d8c824.png",
                    "questions": [
                        {
                            "supportLink": "http://support.cure.fit/support/solutions/articles/25000023141-what-are-live-classes-on-curefit-",
                            "question": "What are LIVE Classes on Curefit?"
                        },
                        {
                            "supportLink": "http://support.cure.fit/support/solutions/articles/25000023156-what-platforms-can-i-watch-live-classes-on-",
                            "question": "What platforms can I watch LIVE classes on?"
                        }
                    ]
                },
                {
                    "title": "Energy Meter and Reports",
                    "subTitle": "10 Answers",
                    "action": {
                        "actionType": "NAVIGATION",
                        "url": `curefit://webview?uri=${encodeURIComponent("https://support.cure.fit/support/solutions/folders/25000032398")}`
                    },
                    "webIcon": "image/vm/5f346078-3d6a-43b1-8f6e-da04cc3d6403.png",
                    "questions": [
                        {
                            "supportLink": "http://support.cure.fit/support/solutions/articles/25000023142-how-does-the-energy-meter-work-",
                            "question": "How does the Energy Meter work?"
                        },
                        {
                            "supportLink": "http://support.cure.fit/support/solutions/articles/25000023169-why-do-live-classes-require-my-camera-access-",
                            "question": "Why do LIVE classes require my camera access?"
                        }
                    ]
                },
                {
                    "title": "Tech Issues",
                    "subTitle": "3 Answers",
                    "action": {
                        "actionType": "NAVIGATION",
                        "url": `curefit://webview?uri=${encodeURIComponent("https://support.cure.fit/support/solutions/folders/25000032399")}`
                    },
                    "webIcon": "image/vm/d8f0daba-c612-4e54-b0e5-e09ca2bbee37.png",
                    "questions": [
                        {
                            "supportLink": "http://support.cure.fit/support/solutions/articles/25000023143-my-class-is-not-loading-what-should-i-do-",
                            "question": "My class is not loading, what should I do?"
                        },
                        {
                            "supportLink": "http://support.cure.fit/support/solutions/articles/25000023178-my-network-internet-connection-dropped-can-i-rejoin-the-session-from-where-i-left-",
                            "question": "My network/internet connection dropped, can I rejoin the session from where I left?"
                        }
                    ]
                }
            ],
            "header": {
                "title": "FAQs",
                "subTitle": "Browse through to find answers to your queries",
                "seemore": {
                    "actionType": "NAVIGATION",
                    "url": `curefit://webview?uri=${encodeURIComponent("http://support.cure.fit/support/solutions/categories/***********")}`,
                    "title": "SEE ALL"
                }
            }
        }
    }

    public static getTermsNConditionsWidget(isIOS: boolean, containerStyle?: any) {
        let content = "Monthly subscription is charged through your Apple account at confirmation of purchase and gives you access to all Live and On-Demand Content on cult.fit. Subscription automatically renews unless you turn off auto-renew at least 24 hours before the end of the current period. You can turn off auto renew upto 24 hours before the end of your current period. You can turn off auto-renew in your Apple settings."
        if (!isIOS) {
            content = "Subscription is charged through your account at confirmation of purchase and gives you access to all Live and On-Demand Content on cult.fit."
        }
        const textWidget: TextWidget = {
            widgetType: "TEXT_WIDGET",
            content,
            fontFace: "regular",
            contentStyle: {
                fontSize: 12,
                textAlign: "center",
                color: "#888e9e"
            },
            containerStyle: containerStyle ? containerStyle : {
                marginVertical: 0,
                paddingVertical: 15,
                paddingBottom: 90
            },
            maxNumberOfLines: 10
        }
        return textWidget
    }

    public static getTnCWidget(userContext: UserContext): StyledTouchableTextWidget {
        const isInternationalApp = AppUtil.isInternationalApp(userContext)
        const lightStyle = {
            color: "#888e9e"
        }
        const highlightStyle = {
            fontFamily: "BrandonText-Bold"
        }
        const tncActions: Action[] = [
            {
                actionType: "HIDE_MEMBERSHIP_EXPIRED_MODAL"
            },
            {
                "url": "curefit://webview?uri=http%3A%2F%2Fstatic.cure.fit%2Fterms.html",
                actionType: "NAVIGATION"
            }
        ]

        const tnc: StyledTouchableTextWidget = {
            widgetType: "STYLED_TOUCHABLE_TEXT_WIDGET",
            data: [
                {
                    title: `By subscribing to ${LiveUtil.getCultLiveSubscriptionTitle(userContext)}, you agree to our`,
                    style: lightStyle,
                    actions: tncActions
                },
                {
                    title: "terms of service",
                    style: highlightStyle,
                    actions: tncActions
                },
            ],
            containerStyle: {
                paddingBottom: 100
            },
            textContainerStyle: {
                maxWidth: 250,
            }
        }
        return tnc
    }

    public static getTotalMembershipDaysDetailsAndExpiryStatus(membership: Membership, timezone: Timezone) {
        const endDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(timezone, TimeUtil.parseDateFromEpoch(membership.end)), timezone)
        const startDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(timezone, TimeUtil.parseDateFromEpoch(membership.start)), timezone)
        const totalMembershipDays = endDate.diff(startDate, "days")
        const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDate(timezone), timezone)
        const isExpired = endDate.diff(today, "days") < 0
        return { totalMembershipDays, isExpired }
    }

    public static getSingleBannerCarouselWidget(imageUrl: string, width: number, height: number, action?: Action, backgroundColor?: string) {
        return new BannerCarouselWidget(`${width}:${height}`, [{
            id: imageUrl,
            image: imageUrl,
            action,
        }], {
            bannerHeight: height,
            bannerWidth: width,
            backgroundColor: backgroundColor || "#ffffff"
        }, 1, false, true, false)
    }

    public static getProductIds(packs: CFLiveProduct[]) {
        if (_.isEmpty(packs)) {
            return []
        }
        const productIds: string[] = []
        packs.forEach(pack => {
            productIds.push(pack.productId)
        })
        return productIds
    }

    public static async startCurefitLiveTrial(serviceInterfaces: CFServiceInterfaces, userContext: UserContext, orderSource: OrderSource, session: Session) {
        const trialPack: CFLiveProduct = await serviceInterfaces.diyService.getTrialCFLiveProduct(userContext.userProfile.userId, AppUtil.getRequestSource(userContext), AppUtil.getTenantFromUserContext(userContext))
        const orderProduct: OrderProduct = {
            productId: trialPack.productId,
            productType: "CF_LIVE",
            quantity: 1,
            option: {
                isPack: true
            }
        }
        const dontCreateRazorpayOrder: boolean = true
        const userId: string = userContext.userProfile.userId
        const useFitCash: boolean = false
        // Temp hack as website is passing offer id as empty
        orderProduct.option.offerId = !_.isEmpty(orderProduct.option.offerId) ? orderProduct.option.offerId : undefined

        const orderCreate: OrderCreate = {
            userId: userId,
            deviceId: session.deviceId,
            products: [orderProduct],
            source: orderSource,
            dontCreateRazorpayOrder: dontCreateRazorpayOrder,
            useOffersV2: true,
            cityId: session.sessionData.cityId,
            useFitCash: useFitCash,
            tenant: AppUtil.getTenantFromUserContext(userContext),
            osName: userContext.sessionInfo.osName,
            appVersion: (userContext.sessionInfo.appVersion ?? 0).toString()
        }

        const order = await serviceInterfaces.orderService.createOrder(orderCreate)
        const data = {
            paymentData: {
                amount: 0,
                currency: "INR",
            }
        }
        const source = order.source === "CUREFIT_APP" || order.source === "CUREFIT_WEBSITE" ? "APP" : "WEBHOOK"
        const orderPaymentSuccess = await serviceInterfaces.orderService.paymentSuccess(order.orderId, data, source)
        return !_.isEmpty(orderPaymentSuccess)
    }

    public static async checkIfMonetisationEnabledAndEligibleForTrial(cultBusiness: ICultBusiness, diySerivce: IDIYFulfilmentService, userContext: UserContext) {
        const isUserEligibleForMonetisation = await cultBusiness.checkIfUserIsEligibleForLivePackMonetisation(userContext)
        let isUserEligibleForTrial = true
        if (isUserEligibleForMonetisation) {
            isUserEligibleForTrial = await LiveUtil.isUserEligibleForLivePackTrial(userContext, diySerivce)
        }
        return { isUserEligibleForMonetisation, isUserEligibleForTrial }
    }

    public static filterPacks(userContext: UserContext, livePacks: CFLiveProduct[], isTrialNeeded: boolean): CFLiveProduct[] {
        if (AppUtil.isAppleIAPEnabled(userContext)) {
            const iosPacks = livePacks.filter(livePack => livePack.appleIAPProductDetails?.productId || (isTrialNeeded && livePack.membershipType === CFLiveMembershipType.TRIAL))
            if (!AppUtil.isIAPYearlyPackSupported(userContext)) {
                return iosPacks.filter(livePack => livePack.price?.mrp < 1000)
            }
            return iosPacks
        } else if (AppUtil.isAndroidPlayIAPEnabled(userContext)) {
            return livePacks.filter(livePack => livePack.androidIAPProductDetails?.productId || (isTrialNeeded && livePack.membershipType === CFLiveMembershipType.TRIAL))
        } else {
            return livePacks.filter(livePack => (!livePack.appleIAPProductDetails?.productId && !livePack.androidIAPProductDetails?.productId) || (isTrialNeeded && livePack.membershipType === CFLiveMembershipType.TRIAL))
        }
    }

    public static canShowPackLevelEnergyIcon(diyEnergyMeterSupportedMap: IdVsValueMap<boolean>) {
        return !_.isEmpty(diyEnergyMeterSupportedMap) && _.every(diyEnergyMeterSupportedMap, _.identity)
    }

    public static getLivePackBenefitsWidget(isWeb: boolean) {
        const action: Action = {
            actionType: "NAVIGATION",
            title: "BECOME A MEMBER",
            subtitle: "STARTS at 249/MONTH",
            url: "curefit://livefitnessbrowsepage",
            analyticsData: {
                eventKey: "widget_click",
                eventData: {
                    widgetName: "Become A Member",
                    pageFrom: "liveSessionDetail"
                }
            }
        }
        const widget = new BannerCarouselWidget("375:340", [
            {
                id: "benefit_1",
                image: isWeb ? "/image/livefit/app/pack_benefits_1.png" : "/image/livefit/app/live_pack_benefit_1_v4.png",
                action: action
            },
            {
                id: "benefit_2",
                image: isWeb ? "/image/livefit/app/live_pack_benefits_web_2.png" : "/image/livefit/app/live_pack_benefit_2_v4.png",
                action: action
            },
            {
                id: "benefit_3",
                image: isWeb ? "/image/livefit/app/live_pack_benefits_web_3.png" : "/image/livefit/app/live_pack_benefit_3_v4.png",
                action: action
            },
        ], {
            showPagination: true,
            v2: true,
            alignment: "center",
            backgroundColor: "",
            autoScroll: false,
            enableSnap: false,
            useShadow: false,
            roundedCorners: true,
            noVerticalPadding: true,
            edgeToEdge: true,
            interContentSpacing: 0,
            bannerOriginalWidth: 505,
            bannerOriginalHeight: 405,
            bannerWidth: 375,
            bannerHeight: 340,
        }, 3, true, false, false, undefined, action)
        widget.widgetType = "LIVE_BENEFITS_CAROUSEL_WIDGET"
        widget.isFullWidthDesktop = false
        return widget
    }

    public static isProductV2DIY(products: FitnessProgramProductResponse[]) {
        let isDIY = true
        for ( const product of products) {
            if (product.productType != "DIY") {
                isDIY = false
                break
            }
        }
        return isDIY
    }

    public static getProductV2SessionIds(products: FitnessProgramProductResponse[]) {
        return products.map(product => product.productId)
    }

    public static getProductV2DIYSessionIds(products: FitnessProgramProductResponse[]) {
        return products.filter(product => product.productType === "DIY")
            .map(product => product.productId)
    }
}
