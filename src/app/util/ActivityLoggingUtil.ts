import * as _ from "lodash"
import { DietDishItem } from "@curefit/maverick-common"
import {
    DietMeasurementUnitDetails,
    LoggableActivityInfo,
    LoggableActivityMetricInfo,
    LoggableActivityMetricViewModel,
    LoggableActivitySlot,
    SlotwiseOrganisedLoggableActivityInfo
} from "../logging/ActivityLogging"
import { MeasurementUnitMeta } from "@curefit/food-common"
import { ActivityDS, LoggedWorkoutIntensity, WorkoutMetric } from "@curefit/logging-common"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { IMetric } from "@curefit/metrics-common"
import { Action } from "../common/views/WidgetView"
import { UserContext } from "@curefit/userinfo-common"
import AppUtil from "./AppUtil"
import LoggableSlotsMetaCache from "../logging/LoggableSlotsMetaCache"
import { InternalValueDisplayValue } from "@curefit/gmf-common"
const clone = require("clone")

const loggableSlotPriorityMap: Map<LoggableActivitySlot, number> = new Map<LoggableActivitySlot, number>()
// Eat related slots
loggableSlotPriorityMap.set("BREAKFAST", 1)
loggableSlotPriorityMap.set("LUNCH", 2)
loggableSlotPriorityMap.set("SNACKS", 3)
loggableSlotPriorityMap.set("DINNER", 4)
// Workout related slots
loggableSlotPriorityMap.set("MORNING", 1)
loggableSlotPriorityMap.set("AFTERNOON", 2)
loggableSlotPriorityMap.set("EVENING", 3)

const measurementUnitIconPathPrefix: string = "/image/icons/logging/"

export class ActivityLoggingUtil {

    public static getDefaultMeasurementUnit(dishItem: DietDishItem): DietMeasurementUnitDetails {
        const caloriesPer100Grams: number = dishItem.calories
        const defaultMeasurementUnitDetails: DietMeasurementUnitDetails = {
            unitName: "g",
            caloriesPerUnit: caloriesPer100Grams / 100
        }
        for (const unitDetails of dishItem.units) {
            if (unitDetails.unit !== "g") {
                defaultMeasurementUnitDetails.unitName = unitDetails.unit
                defaultMeasurementUnitDetails.caloriesPerUnit = unitDetails.conversionToGrams * caloriesPer100Grams / 100
                break
            }
        }
        return defaultMeasurementUnitDetails
    }

    public static getSupportedMeasurementUnitDetails(dishItem: DietDishItem): DietMeasurementUnitDetails[] {
        const supportedUnitDetails: DietMeasurementUnitDetails[] = []
        const caloriesPer100Grams: number = dishItem.calories

        for (const unitDetails of dishItem.units) {
            if (unitDetails.unit === "g") continue // Don't support logging in grams
            supportedUnitDetails.push({
                unitName: unitDetails.unit,
                caloriesPerUnit: unitDetails.conversionToGrams * caloriesPer100Grams / 100
            })
        }
        return supportedUnitDetails
    }

    public static getSlotMetaList(loggableSlots: LoggableActivitySlot[], slotCache: LoggableSlotsMetaCache): {
        id: LoggableActivitySlot,
        name: string
    }[] {
        const slotMetaList: {
            id: LoggableActivitySlot,
            name: string
        }[] = []
        loggableSlots.forEach((loggableSlot: LoggableActivitySlot) => {
            const slotMeta: InternalValueDisplayValue = slotCache.getDietSlotById(loggableSlot)
            if (!_.isNil(slotMeta)) {
                slotMetaList.push({
                    id: <LoggableActivitySlot>slotMeta.internalValue,
                    name: slotMeta.displayValue
                })
            }
        })
        return slotMetaList
    }

    public static getActionforMetric(metric: IMetric, date: string, userContext: UserContext): Action {

        let formAction: Action
        if (!_.isNil(metric.measureMeta.assessmentFormId)) {
            formAction = {
                actionType: "NAVIGATION",
                url: "curefit://userform?formId=" + metric.measureMeta.assessmentFormId
            }
        }

        if (!_.isNil(metric.measureMeta.supportedDevice) && (TimeUtil.todaysDate(userContext.userProfile.timezone) === date)) {
            const deviceInfo: any = metric.measureMeta.supportedDevice
            if (AppUtil.isThirdPartyDevicesSupported(userContext, deviceInfo.deviceType)) {
                const deviceAction: Action = {
                    actionType: "SHOW_EXT_DEVICE_MODAL",
                    meta: {
                        deviceType: deviceInfo.deviceType,
                        metrics: deviceInfo.metrics,
                        manualAction: formAction
                    }
                }
                return deviceAction
            }
        }
        return formAction

    }

    public static getMeasurementUnitIconPath(measurementUnitMeta: MeasurementUnitMeta): string {
        if (_.isNil(measurementUnitMeta) || _.isNil(measurementUnitMeta.icon)) return undefined
        return measurementUnitIconPathPrefix + measurementUnitMeta.icon
    }

    public static getMeasurementUnitVolumeString(measurementUnitMeta: MeasurementUnitMeta): string {
        if (_.isNil(measurementUnitMeta) || _.isNil(measurementUnitMeta.volume)) return undefined
        return measurementUnitMeta.volume + " ml"
    }

    public static transformWorkoutMetricToLoggableActivityMetric(workoutMetric: {
        name: string
        displayName: string
        units: {
            name: string
            displayName: string
        }[]
    }): LoggableActivityMetricViewModel {
        if (_.isEmpty(workoutMetric.units)) return this.transformUnitLessMetrics(workoutMetric)
        const supportedMetrics: LoggableActivityMetricInfo[] = []
        workoutMetric.units.forEach((unit) => {
            supportedMetrics.push({
                metricName: _.capitalize(workoutMetric.displayName),
                metricUnit: unit.displayName,
                multiplier: this.getMultiplierForUnit(unit.displayName)
            })
        })
        return {
            selectedMetric: supportedMetrics[0],
            viewType: "TEXT",
            quantity: "0",
            supportedMetrics: supportedMetrics
        }
    }

    private static transformUnitLessMetrics(workoutMetric: {
        name: string
        displayName: string
        units: {
            name: string
            displayName: string
        }[]
    }): LoggableActivityMetricViewModel {
        if (workoutMetric.name === "sets" || workoutMetric.name === "reps") {
            const supportedMetrics: LoggableActivityMetricInfo[] = []
            supportedMetrics.push({
                metricName: _.capitalize(workoutMetric.displayName),
                metricUnit: "",
                multiplier: 1
            })
            return {
                selectedMetric: supportedMetrics[0],
                viewType: "TEXT",
                quantity: "0",
                supportedMetrics: supportedMetrics
            }
        } else {
            return undefined
        }
    }

    public static getMultiplierForUnit(metricName: string): number {
        if (metricName === "km") {
            return 0.001
        } else if (metricName === "m") {
            return 1000
        }
        return 1
    }

    public static getIntensityMetric(allowedIntensities: LoggedWorkoutIntensity[]): LoggableActivityMetricViewModel {
        if (_.isEmpty(allowedIntensities)) return undefined
        const intensityMetric: LoggableActivityMetricInfo = {
            metricName: "Intensity",
            metricUnit: "Intensity",
            multiplier: 1,
            possibleQuantities: allowedIntensities
        }
        return {
            selectedMetric: intensityMetric,
            viewType: "SEGMENT",
            quantity: allowedIntensities[0]
        }
    }

    public static isIntensityMetric(metric: LoggableActivityMetricInfo): boolean {
        return (metric.metricName === "Intensity") ? true : false
    }

    public static isSlotMetric(metric: LoggableActivityMetricInfo): boolean {
        return (metric.metricName === "When") ? true : false
    }

    public static isTimeMetric(metricName: string): boolean {
        return (metricName === "hourMin" || metricName === "min") ? true : false
    }

    public static getSlotForWorkoutDate(activityDate: Date, timezone: Timezone = TimeUtil.IST_TIMEZONE): LoggableActivitySlot {
        const activityDateMoment = TimeUtil.getMomentForDate(activityDate, timezone)
        const hours: number = activityDateMoment.hours()
        if (hours >= 3 && hours < 12) {
            return "MORNING"
        } else if (hours >= 12 && hours <= 16) {
            return "AFTERNOON"
        } else {
            return "EVENING"
        }
    }

    public static sortFitnessLoggableActivities(fitnessActivities: LoggableActivityInfo[]): void {
        fitnessActivities.sort((a1: LoggableActivityInfo, a2: LoggableActivityInfo) => {
            const slot1: LoggableActivitySlot = ActivityLoggingUtil.getSlotForWorkoutActivity(a1)
            const slot2: LoggableActivitySlot = ActivityLoggingUtil.getSlotForWorkoutActivity(a2)
            if (loggableSlotPriorityMap.get(slot1) < loggableSlotPriorityMap.get(slot2))
                return -1
            else if (loggableSlotPriorityMap.get(slot1) > loggableSlotPriorityMap.get(slot2))
                return 1
            else if (a1.name <= a2.name)
                return -1
            else return 1
        })
    }

    public static addSlotMetricToWorkout(workoutActivity: LoggableActivityInfo, selectedSlot: LoggableActivitySlot): void {
        workoutActivity.metrics.push({
            selectedMetric: {
                metricName: "When",
                metricUnit: "slot",
                multiplier: 1,
                possibleQuantities: ["Morning", "Afternoon", "Evening"]
            },
            viewType: "SEGMENT",
            quantity: _.capitalize(selectedSlot)
        })
    }

    public static getSlotForWorkoutActivity(workoutActivity: LoggableActivityInfo): LoggableActivitySlot {
        const slotMetric: LoggableActivityMetricViewModel = _.find(workoutActivity.metrics, (metricViewModel: LoggableActivityMetricViewModel) => {
            return ActivityLoggingUtil.isSlotMetric(metricViewModel.selectedMetric)
        })
        return _.isNil(slotMetric) ? "MORNING" : <LoggableActivitySlot>_.toUpper(slotMetric.quantity)
    }

    public static convertWorkoutSlotToEpoch(userContext: UserContext, loggableActivitySlot: LoggableActivitySlot, date: string): number {
        const timezone = userContext.userProfile.timezone
        let parsedDate: Date = TimeUtil.parseDate(date, timezone)
        if (loggableActivitySlot === "MORNING") {
            parsedDate = TimeUtil.getDefaultMomentForDateString(date, timezone).add(1, "day").startOf("day").subtract(16, "hours").toDate()
            // parsedDate.setHours(8)
        } else if (loggableActivitySlot === "AFTERNOON") {
            parsedDate = TimeUtil.getDefaultMomentForDateString(date, timezone).add(1, "day").startOf("day").subtract(12, "hours").toDate()
            // parsedDate.setHours(12)
        } else if (loggableActivitySlot === "EVENING") {
            parsedDate = TimeUtil.getDefaultMomentForDateString(date, timezone).add(1, "day").startOf("day").subtract(6, "hours").toDate()
            // parsedDate.setHours(18)
        }
        return parsedDate.getTime()
    }

    public static isFoodActivity(activity: ActivityDS): boolean {
        if (activity.activityType === "FOOD_CONSUMPTION"
            || activity.activityType === "EATFIT_MEAL"
            || activity.activityType === "GMF_FOOD_RECOMMENDATION"
        ) return true
        return false
    }

    public static isFitnessActivity(activity: ActivityDS): boolean {
        if (activity.activityType === "WALK"
            || activity.activityType === "CULT_SCORE_DIY"
            || activity.activityType === "CULT_CLASS"
            || activity.activityType === "DIY_FITNESS"
            || activity.activityType === "WORKOUT_ACTIVITY"
            || activity.activityType === "GMF_FITNESS_RECOMMENDATION"
            || activity.activityType === "CONSULTATION_PT"

        ) return true
        return false
    }

    public static isMeditationActivity(activity: ActivityDS): boolean {
        if (activity.activityType === "SLEEP"
            || activity.activityType === "MIND_CLASS"
            || activity.activityType === "DIY_MEDITATION"
            || activity.activityType === "MIND_WORKOUT_ACTIVITY"
            || activity.activityType === "GMF_MIND_RECOMMENDATION"

        ) return true
        return false
    }

    public static isCareActivity(activity: ActivityDS): boolean {
        if (activity.activityType === "CONSULTATION"
            || activity.activityType === "AT_HOME_SAMPLE_COLLECTION"
            || activity.activityType === "IN_CENTRE_VISIT_FOR_TEST"
            || activity.activityType === "MEDICINE_INTAKE"

        ) return true
        return false
    }

    public static isTodayWalkActivity(userContext: UserContext, activity: ActivityDS): boolean {
        const today: string = TimeUtil.todaysDate(userContext.userProfile.timezone)
        if (activity.activityType === "WALK" && activity.date === today) return true
        return false
    }

    public static stringifyTimeWorkoutMetric(timeMetric: WorkoutMetric): string {
        let result: string = ""
        if (timeMetric.unit === "min") {
            if (timeMetric.value > 0) {
                result = timeMetric.value + ((timeMetric.value === 1) ? " min" : " mins")
            }
        } else if (timeMetric.unit === "hourMin") {
            const totalMins: number = timeMetric.value
            const mins: number = totalMins % 60
            const hours: number = (totalMins - mins) / 60
            if (hours > 0) {
                result = hours + ((hours === 1) ? " hr" : " hrs")
            }
            if (mins > 0) {
                if (!_.isEmpty(result)) result += " "
                result += mins + ((mins === 1) ? " min" : " mins")
            }
        }
        return result
    }

    public static mergeSlottedData(slottedDataA: SlotwiseOrganisedLoggableActivityInfo, slottedDataB: SlotwiseOrganisedLoggableActivityInfo): SlotwiseOrganisedLoggableActivityInfo {
        const slottedDataMerged: SlotwiseOrganisedLoggableActivityInfo = clone(slottedDataA)
        slottedDataB.slots.forEach((slotMeta: {
            id: LoggableActivitySlot,
            name: string
        }) => {
            const loggedActivityInfoList: LoggableActivityInfo[] = _.get(slottedDataB, "slotDetails." + slotMeta.id)
            if (_.isArray(loggedActivityInfoList)) {
                const mergedLoggedInfoList: LoggableActivityInfo[] = _.get(slottedDataMerged, "slotDetails." + slotMeta.id)
                if (_.isNil(mergedLoggedInfoList)) {
                    slottedDataMerged.slots.push({
                        id: slotMeta.id,
                        name: _.capitalize(slotMeta.id)
                    })
                    slottedDataMerged.slotDetails[slotMeta.id] = []
                }
                loggedActivityInfoList.forEach((loggedActivityInfo: LoggableActivityInfo) => {
                    slottedDataMerged.slotDetails[slotMeta.id].push(clone(loggedActivityInfo))
                })
            }
        })
        return slottedDataMerged
    }

}
export default ActivityLoggingUtil
