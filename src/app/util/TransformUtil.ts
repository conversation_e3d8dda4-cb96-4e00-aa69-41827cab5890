import { UserContext } from "@curefit/vm-models"
import { Activity } from "../atlas/FitbitCronService"
import { ActionUtil, Constants } from "@curefit/base-utils"
import { IQueueService } from "@curefit/sqs-client"
import {
    CardClickableWidget,
    CheckoutActionsWidget,
    CheckoutActionsWidgetV2,
    CheckoutStepItem,
    CoachInfoCard,
    CoachInfoWidget,
    Header,
    InfoCard,
    InstructionsWidget,
    ProductListWidget,
    TransformEMIItem,
    TransformNoCostEMIWidget,
} from "../common/views/WidgetView"
import { WidgetView } from "@curefit/apps-common"
import { OfferV2 } from "@curefit/offer-common"
import CultUtil, {
    CULT_NAS_PRICE_HIKE_BANNER_ID,
    CULT_TRIAL_CLASS_SIPPER_WIDGET_ID_PROD, CULT_TRIAL_CLASS_SIPPER_WIDGET_ID_STAGE,
    MIND_NAS_PRICE_HIKE_BANNER_ID
} from "./CultUtil"
import { DiagnosticProduct, SUB_CATEGORY_CODE, TRANSFORM_SUB_CATEGORY_CODE_TYPE } from "@curefit/care-common"
import * as _ from "lodash"
import { Action } from "@curefit/apps-common/dist/src/actions/actions"
import { ManagedPlanPackInfo } from "@curefit/albus-client"
import { UserAgentType } from "@curefit/base-common"
import AppUtil from "./AppUtil"
import { RUPEE_SYMBOL } from "@curefit/finance-common"
import { ICrudKeyValue } from "@curefit/redis-utils"
import { ForbiddenError } from "@curefit/base"
import { CultPackPageRequestParams } from "../cult/cultpackpage/CultPackCommonViewBuilder"
import { inject } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import WidgetBuilder from "../page/vm/WidgetBuilder"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import moment = require("moment")

const clone = require("clone")

export const COMBINED_WEIGHT_LOSS_CLP_DEEPLINK = "curefit://tf_weight_loss_tab"

export class TransformUtil {

    static async pushVideoGuideActivity(userContext: UserContext, contentId: string, duration: number, queueService: IQueueService) {
        const activities = [] as Activity[]
        activities.push(this.getVideoActivityPayload(userContext, contentId, duration))
        await this.updateActivitiesAsync(userContext.userProfile.userId, activities, queueService)
        return true
    }

    static getVideoActivityPayload(userContext: UserContext, contentId: string, duration: number) {
        const onDemandActivityPayload: any = {
            contentId: contentId,
            activityId: contentId,
            deviceId: userContext.sessionInfo.deviceId,
            subType: "TRANSFORM_SKILL_RESOURCE",
            uid: userContext.userProfile.userId,
            duration: duration,
        }
        return {
            activityMsgType: "ON_DEMAND_VIDEO",
            activityData: onDemandActivityPayload,
        }
    }

    public static async updateActivitiesAsync(userId: string, activitiesList: Activity[], queueService: IQueueService): Promise<boolean> {
        activitiesList.forEach((activity) => {
            const attributes = new Map()
            attributes.set("userId", userId)
            attributes.set("type", activity.activityMsgType)
            queueService.sendMessageAsync(Constants.getSQSQueue("ACTIVITYSTORE"), activity.activityData, attributes)
        })
        return true
    }

    public static getCoachInfoBootcamp(): WidgetView {
        const coachCard: CoachInfoCard = {
            tagTitle: "ABOUT BOOTCAMP",
            coachInfoItems: [
                {
                    title: "45 Days",
                    subtitle: "DURATION",
                    imageUrl: "image/transform/bootcamp_duration.png"
                },
                {
                    title: "8th Jan",
                    subtitle: "START DATE",
                    imageUrl: "image/transform/bootcamp_start.png"
                },
            ]
        }

        const layoutProps = {
            "spacing": {
                "top": "40",
                "bottom": "30"
            }
        }

        return new CoachInfoWidget([coachCard], layoutProps, 20)
    }

    public static getCheckoutActionsBootcamp(productId: string): WidgetView {

        const stepItems: CheckoutStepItem[] = [
            {
                id: "CENTER_SELECTION",
                imageUrl: "image/transform/location.png",
                hintText: "Pick your center",
                nextId: "SLOT_SELECTION",
                visible: true,
                showDivider: true,
            },
            {
                id: "SLOT_SELECTION",
                imageUrl: "image/transform/time.png",
                hintText: "Pick your slot",
                nextId: "SUBMISSION",
                visible: true,
                showDivider: false
            },
            {
                id: "SUBMISSION",
                nextId: "END",
                visible: false,
                showDivider: false
            }
        ]

        const actionsMap = {
            "CENTER_SELECTION": {
                "title": "SELECT CENTER",
                "actionType": "NAVIGATION",
                "url": "curefit://tf_center_selector_new"
            },
            "SLOT_SELECTION": {
                "title": "SELECT SLOT",
                "actionType": "SHOW_TF_SLOT_SELECTION_MODAL"
            },
            "SUBMISSION": {
                "meta": {
                    "consentInfo": {
                        "message": "I consent to being contacted via whatsapp by my coaches during this membership"
                    },
                },
                "rnAction": {
                    "actionType": "ADD_CARE_CART_USING_PARAMS",
                    "meta": {
                        productId: productId,
                        useFitcash: true,
                    }
                },
                "title": "Pay NOW",
                "actionType": "CARE_CART_PAY_NOW",
                "url": "curefit://payment?vertical=CARE"
            }
        }

        return new CheckoutActionsWidget(stepItems, actionsMap, false)
    }

    public static getCheckoutActionsV2Bootcamp(productId: string, isLiftProduct: boolean, isBootcampPulse: boolean): WidgetView {

        const stepItems: CheckoutStepItem[] = [
            {
                id: "BATCH_SELECTION",
                imageUrl: "image/transform/location.png",
                hintText: "Pick a Batch",
                nextId: "SUBMISSION",
                visible: true,
                showDivider: true,
            },
            {
                id: "SUBMISSION",
                nextId: "END",
                visible: false,
                showDivider: false
            }
        ]

        const actionsMap = {
            "BATCH_SELECTION": {
                "title": "SELECT YOUR BATCH",
                "actionType": "POP_AND_NAVIGATION",
                "url": isLiftProduct ? "curefit://tf_batch_selector?subCategoryCode=LIFT" : isBootcampPulse ? "curefit://tf_batch_selector?filter=btc_pulse&subCategoryCode=BOOTCAMP" : "curefit://tf_batch_selector"
            },
            "SUBMISSION": {
                "meta": {
                    "consentInfo": {
                        "message": "I consent to being contacted via whatsapp by my coaches during this membership"
                    },
                },
                "rnAction": {
                    "actionType": "ADD_CARE_CART_USING_PARAMS",
                    "meta": {
                        productId: productId,
                        useFitcash: true,
                    }
                },
                "title": "Pay NOW",
                "actionType": "CARE_CART_PAY_NOW",
                "url": "curefit://payment?vertical=CARE"
            }
        }
        const subCategoryCode = isLiftProduct ? "LIFT" : ""
        return new CheckoutActionsWidgetV2(stepItems, actionsMap, subCategoryCode)
    }

    public static getCheckoutActionsTransform(productId: string, subCategoryCode: SUB_CATEGORY_CODE, totalAmountPayable: number, userContext: UserContext, startDateData: any): WidgetView {
        const isTransformPlusProduct: boolean = subCategoryCode === TRANSFORM_SUB_CATEGORY_CODE_TYPE.TRANSFORM_PLUS
        let stepItems: CheckoutStepItem[] = [
            {
                id: "ADDRESS_SELECTION",
                imageUrl: "image/transform/location.png",
                hintText: "Enter billing address",
                nextId: "SUBMISSION",
                visible: true,
                showDivider: true,
            },
            {
                id: "SUBMISSION",
                nextId: "END",
                visible: false,
                showDivider: false
            }
        ]
        if (AppUtil.isTransformStartDateSelectionSupported(userContext)) {
            stepItems = [
                {
                    id: "START_DATE_SELECTION",
                    imageUrl: "image/transform/time.png",
                    hintText: "Pick your start date",
                    nextId: "SUBMISSION",
                    visible: true,
                    showDivider: false,
                },
                {
                    id: "SUBMISSION",
                    nextId: "END",
                    visible: false,
                    showDivider: false
                }
            ]
        }

        const actionsMap = {
            "ADDRESS_SELECTION": {
                "title": "ENTER ADDRESS",
                "actionType": "NAVIGATION",
                "url": "curefit://tf_address_selector"
            },
            "START_DATE_SELECTION": {
                "title": "SELECT START DATE",
                "actionType": "SHOW_TF_START_DATE_SELECTION_MODAL",
                "meta": {
                    "startDateEpoch": startDateData.startEpoch,
                    "endDateEpoch": startDateData.startEpoch + startDateData.dayWindow * 86400000,
                    "errorMessage": "Please pick the billing address in order to purchase the pack"
                },
            },
            "SUBMISSION": {
                "meta": {
                    "consentInfo": {
                        "message": isTransformPlusProduct ? "I consent to being contacted via whatsapp and call by my coaches during this membership" : "I consent to being contacted via whatsapp by my coach during this membership"
                    },
                    "freePayment": totalAmountPayable === 0
                },
                "rnAction": {
                    "actionType": "ADD_CARE_CART_USING_PARAMS",
                    "meta": {
                        productId: productId,
                        useFitcash: true,
                    }
                },
                "title": `Pay ${RUPEE_SYMBOL}${totalAmountPayable}`,
                "actionType": totalAmountPayable === 0 ? "CARE_PAY_FREE" : "CARE_CART_PAY_NOW",
                "url": totalAmountPayable === 0 ? "curefit://transform_free" : "curefit://payment?vertical=CARE",
            }
        }

        return new CheckoutActionsWidget(stepItems, actionsMap, true)
    }

    public static getTransformNoCostEMIWidget(product: DiagnosticProduct, emiOffer: OfferV2, totalPayable?: number): WidgetView {

        let maxEmiTenure = 12
        emiOffer.addons.forEach((addOn) => {
            // Max supported emi duration in months
            if (addOn.config && addOn.config.maxEmiTenure) {
                maxEmiTenure = addOn.config.maxEmiTenure
            }
        })

        const numberOfMonths = CultUtil.getEmiTenureForPack(product.duration, maxEmiTenure)
        const item: TransformEMIItem = {
            imageUrl: "image/transform/EMI.png",
            title: "No Cost EMI starts at just",
            symbol: "₹",
            price: `${Math.round((totalPayable ?? product.price.listingPrice) / numberOfMonths)}`,
            suffix: "month*",
            action: {
                actionType: "NAVIGATION",
                url: `curefit://nocostemipage?packId=${product.productId}&productType=${product.productType}`,
                title: "DETAILS"
            }
        }

        const title = "Know more about No Cost EMI"
        return new TransformNoCostEMIWidget(title, [item])
    }


    public static getCultpassElitePolicyWidget() {
        const title = "Policies"
        const subtitle = "Know more about Cultpass ELITE policies"
        const action: Action = {
            url: ActionUtil.infoPage("cultpassblackpolicy"),
            actionType: "NAVIGATION",
        }
        const layoutProps = {
            "spacing": {
                "top": "30",
                "bottom": "25"
            }
        }

        return new CardClickableWidget(title, subtitle, action, layoutProps)
    }

    public static getBootcampBookingConfirmationWidget(product: DiagnosticProduct) {
        let confirmationWidget: any
        product.infoSection.children.map(infoSection => {
            switch (infoSection.type) {
                case "POST_PURCHASE_THANK_YOU":
                    confirmationWidget = infoSection
                    break
            }
        })

        if (!_.isNil(confirmationWidget)) {
            const widget: InstructionsWidget = new InstructionsWidget([], null, true, [{
                title: confirmationWidget?.title || "Thank you for your purchase",
                subtitle: confirmationWidget?.subtitle || "Few things to note",
                priority: 0,
                instructionResponseList: (confirmationWidget?.children || []).map((item: { desc: string; imageUrl: string; }) => {
                    return {
                        text: item.desc,
                        iconURL: item.imageUrl
                    }
                })
            }])
            widget.orientation = "VERTICAL"
            widget.blurEnabled = true
            widget.showHeader = true
            return widget

        }
        return null
    }

    public static getWhatYouGetInstructionWidget(whatYouGet: ManagedPlanPackInfo, userContext: UserContext): WidgetView {
        if (_.isEmpty(whatYouGet)) {
            return undefined
        }
        const widget: InstructionsWidget = new InstructionsWidget([], userContext.sessionInfo.userAgent, true,
            [{
                title: whatYouGet?.title || "What you get",
                priority: 0,
                instructionResponseList: (whatYouGet?.children || []).map((item: { desc: string; imageUrl: string; }) => {
                    return {
                        text: item.desc,
                        iconURL: item.imageUrl
                    }
                })
            }], true, {
                title: whatYouGet?.title || "What you get"
            })
        widget.hideHeaderBullet = true
        widget.orientation = "VERTICAL"
        widget.blurEnabled = false
        return widget
    }

    public static getHowItWorksLayoutBootcamp() {
        return {
            "spacing": {
                "top": "0",
                "bottom": "200"
            }
        }
    }

    public static getHowItWorksLayoutTransform() {
        return {
            "spacing": {
                "top": "0",
                "bottom": "150"
            }
        }
    }

    public static getHowItWorksWidget(howItWorksItem: ManagedPlanPackInfo, userAgent?: UserAgentType, disableOrientation?: boolean): ProductListWidget {
        if (_.isEmpty(howItWorksItem)) {
            return undefined
        }
        const header: Header = howItWorksItem?.title ? {
            title: howItWorksItem?.title,
            style: {
                fontSize: 18,
            },
            color: "#000000"
        } : undefined
        const infoCards: InfoCard[] = []
        howItWorksItem.children.forEach(item => {
            item.desc = item.desc.replace("${DEGREE}", "\u00b0")
            infoCards.push({
                subTitle: item.desc,
                icon: item.imageUrl
            })
        })
        const widget = new ProductListWidget("SMALL", header, infoCards)
        widget.hasDividerBelow = false
        if (userAgent === "DESKTOP" && !disableOrientation) {
            widget.orientation = "RIGHT"
        }
        widget.layoutProps = this.getHowItWorksLayoutBootcamp()
        widget.showSquareIcons = true
        return widget
    }

    public static getTransformMembershipUrl(subCategoryCode: SUB_CATEGORY_CODE): string {
        switch (subCategoryCode) {
            case TRANSFORM_SUB_CATEGORY_CODE_TYPE.TRANSFORM_PLUS:
                return COMBINED_WEIGHT_LOSS_CLP_DEEPLINK
            case TRANSFORM_SUB_CATEGORY_CODE_TYPE.BOOTCAMP:
                return COMBINED_WEIGHT_LOSS_CLP_DEEPLINK
            case TRANSFORM_SUB_CATEGORY_CODE_TYPE.LIFT:
                return "curefit://lift"
        }
        return COMBINED_WEIGHT_LOSS_CLP_DEEPLINK
    }

    public static getTransformMembershipName(subCategoryCode: SUB_CATEGORY_CODE): string {
        switch (subCategoryCode) {
            case TRANSFORM_SUB_CATEGORY_CODE_TYPE.TRANSFORM_PLUS:
                return "cult Transform PLUS"
            case TRANSFORM_SUB_CATEGORY_CODE_TYPE.BOOTCAMP:
                return "cult Bootcamp"
            case TRANSFORM_SUB_CATEGORY_CODE_TYPE.LIFT:
                return "cult Lift"
        }
        return "cult Transform"
    }

    public static getBootcampModalAction(): Action {
        const action: Action = {
            actionType: "SHOW_PRODUCT_INFO_MODAL",
            meta: {
                "productInfoModalData": {
                    "productType": "BOOTCAMP",
                    "headerSubtitle": "INTRODUCING",
                    "headerTitle": "cult Bootcamp",
                    "priceSubTitle": "ONLY AT",
                    "priceTitle": "₹ 7,999",
                    "imageUrl": "image/transform/bootcamp_plc.png",
                    "title": "Lose up to 5 kgs in 6 weeks",
                    "description": "Enroll in the Bootcamp membership for exclusive weight loss classes at this centre, which covers:",
                    "productPoints": [
                        {
                            "imageUrl": "image/transform/workout_icon_blur.png",
                            "title": "Small group workouts"
                        },
                        {
                            "imageUrl": "image/transform/nutrition_icon_blur.png",
                            "title": "1:1 Nutrition coaching"
                        },
                        {
                            "imageUrl": "image/transform/community_icon_blur.png",
                            "title": "Strong & accountable community"
                        },
                        {
                            "imageUrl": "image/transform/plan_icon_blur.png",
                            "title": "Maintenance plan"
                        },
                    ],
                    "actionList": [
                        {
                            "actionType": "NAVIGATION",
                            "title": "TELL ME MORE",
                            "description": `⚡ Weight Loss Program ⚡️`,
                            "url": "curefit://fl_listpage?pageId=transform_bootcamp&disableAnimation=true"
                        }
                    ],
                }
            },
        }
        return action
    }

    public static async isBootcampImpressionCompleted(userId: string, crudDao: ICrudKeyValue): Promise<boolean> {
        const redisKey: string = "bootcamp_impression_count_" + userId
        return crudDao.read(redisKey).then(function (payload) {
            if (payload) {
                const impressionCount = JSON.parse(payload)
                if (_.isNil(impressionCount["count"]) || _.isEmpty(impressionCount["count"])) {
                    crudDao.create(redisKey, `{"count":"0"}`)
                    return false
                }
                return parseInt(impressionCount["count"]) >= 4
            } else {
                crudDao.create(redisKey, `{"count":"0"}`)
                return false
            }
        })
    }

    public static async isBootcampToolTipVisible(userId: string, crudDao: ICrudKeyValue): Promise<boolean> {
        const redisKey: string = "bootcamp_tooltip_status_" + userId
        return crudDao.read(redisKey).then(function (payload) {
            if (payload) {
                const data = JSON.parse(payload)
                if (_.isNil(data["status"]) || _.isEmpty(data["status"])) {
                    crudDao.create(redisKey, `{"status":"CREATED"}`)
                    return true
                }
                return data["status"] === "CREATED"
            } else {
                crudDao.create(redisKey, `{"status":"CREATED"}`)
                return true
            }
        })
    }

    public static async getTimerBannerWidget(userContext: UserContext, widgetBuilder: WidgetBuilder, serviceInterfaces: CFServiceInterfaces, subCategoryCode: SUB_CATEGORY_CODE): Promise<any> {
        const widgetId = this.getTransformTimerWidgetId(subCategoryCode)
        const widgetResponse = await widgetBuilder.buildWidgets([widgetId], serviceInterfaces, userContext, undefined, undefined)
        if (!_.isEmpty(widgetResponse) && !_.isEmpty(widgetResponse.widgets)) {
            const widget: any = widgetResponse.widgets[0] && widgetResponse.widgets[0].widgetType === "BANNER_CAROUSEL_WIDGET" ? widgetResponse.widgets[0] : undefined
            // updating layoutprops in same object was causing issue in same widget displaying in other place
            const bannerWidget = clone(widget)
            bannerWidget.layoutProps.backgroundColor = null
            bannerWidget.hasDividerBelow = false
            bannerWidget.layoutProps.v2 = true
            bannerWidget.dividerType = "DIVIDER30"
            bannerWidget.layoutProps.useShadow = true
            return bannerWidget
        }
        return undefined
    }

    public static getTransformTimerWidgetId(subCategoryCode: SUB_CATEGORY_CODE): string {
        switch (subCategoryCode) {
            case TRANSFORM_SUB_CATEGORY_CODE_TYPE.TRANSFORM_PLUS:
                return "0153e3e9-4578-4439-83ce-da0777de5d63_plus"
            case TRANSFORM_SUB_CATEGORY_CODE_TYPE.BOOTCAMP:
                return "0153e3e9-4578-4439-83ce-da0777de5d63_bootcamp"
            case TRANSFORM_SUB_CATEGORY_CODE_TYPE.LIFT:
                return "0153e3e9-4578-4439-83ce-da0777de5d63_lift"
        }
        return "0153e3e9-4578-4439-83ce-da0777de5d63"
    }

    public static async getBootcampOutdoorBannerWidget(userContext: UserContext, widgetBuilder: WidgetBuilder, serviceInterfaces: CFServiceInterfaces, subCategoryCode: SUB_CATEGORY_CODE): Promise<any> {
        const widgetId = process.env.APP_ENV === "PRODUCTION" || process.env.APP_ENV === "ALPHA" ? "8ef22b43-ebde-49f6-ad4d-be050a21a797" : "7bed6672-2f28-4a60-be86-aca858154d04"
        const widgetResponse = await widgetBuilder.buildWidgets([widgetId], serviceInterfaces, userContext, undefined, undefined)
        if (!_.isEmpty(widgetResponse) && !_.isEmpty(widgetResponse.widgets)) {
            const widget: any = widgetResponse.widgets[0] && widgetResponse.widgets[0].widgetType === "BANNER_CAROUSEL_WIDGET" ? widgetResponse.widgets[0] : undefined
            // updating layoutprops in same object was causing issue in same widget displaying in other place
            const bannerWidget = clone(widget)
            bannerWidget.layoutProps.backgroundColor = null
            bannerWidget.hasDividerBelow = false
            bannerWidget.layoutProps.v2 = true
            bannerWidget.dividerType = "DIVIDER30"
            bannerWidget.layoutProps.useShadow = true
            return bannerWidget
        }
        return undefined
    }
}