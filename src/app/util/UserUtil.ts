import { City, LatLong } from "@curefit/location-common"
import { ICityService } from "@curefit/location-mongo"
import { IUserAttributeClient } from "@curefit/rashi-client"
import { SessionData, UserContext } from "@curefit/userinfo-common"
import _ = require("lodash")
import AppUtil from "./AppUtil"


export interface UserPreferenceV2 {
    settings: {
        key: string
        value: any
    }[]
}

export interface LocationPreferenceEntity {
    prefLocationType: LocationPreferenceSelection
    coordinates?: LatLong
    coordinatesName?: string
    locality?: string
}

export interface LocationPreferenceRequestEntity extends LocationPreferenceEntity {
    pageFrom?: string
}

export interface LocationPreferenceResponseEntity extends LocationPreferenceEntity {
    currentLocation?: LatLong
}

export interface UpdateStatusResponseEntity {
    success: boolean
    message?: string
    currentCityName?: string
    detectedCity?: City
}

export enum LocationDataKey {
    LOC_PREF_TYPE = "location_setting",
    COORDINATES = "coordinates",
    COORDINATES_NAME = "coordinates_name",
    LOCALITY = "locality",
    CURRENT_LOC = "current_location"
}

export type LocationPreferenceSelection = LocationDataKey.COORDINATES | LocationDataKey.LOCALITY | LocationDataKey.CURRENT_LOC

class UserUtil {

    static async updateLocationData(userContext: UserContext, userId: string, cityId: string, key: string, value: string, userAttributeClient: IUserAttributeClient, userPreference: UserPreferenceV2) {
        const prefKey = this.getPrefenceKey(cityId, key)
        if (!userContext.sessionInfo.isUserLoggedIn) {
            userPreference.settings.push({
                key: prefKey,
                value: value,
            })
        } else {
            await userAttributeClient.setUserAttributes({
                userId: Number(userId),
                attribute: prefKey,
                attrValue: value,
                namespace: this.getRashiNamespace(),
                description: `preferred ${key}`,
                dataType: "STRING",
                occuredAt: new Date().getTime()
            })
        }
    }

    public static async getLocationDataFromRashi(userId: string, cityId: string, key: string, userAttributeClient: IUserAttributeClient) {
        const prefKey = this.getPrefenceKey(cityId, key)
        const response = await userAttributeClient.getCachedUserAttributes(
            Number(userId),
            prefKey
        )
        const attributes: any = response.attributes
        let value: any
        for (const key in attributes) {
            value = attributes[key]
        }
        return value
    }

    static async getLocationPreference(userId: string, cityId: string, userAttributeClient: IUserAttributeClient, locationPreference: LocationPreferenceResponseEntity) {
        locationPreference.prefLocationType = await this.getLocationDataFromRashi(userId, cityId, LocationDataKey.LOC_PREF_TYPE, userAttributeClient)
        switch (locationPreference.prefLocationType) {
            case LocationDataKey.COORDINATES:
                locationPreference.coordinates = this.stringToLatLong(await this.getLocationDataFromRashi(userId, cityId, LocationDataKey.COORDINATES, userAttributeClient))
                locationPreference.coordinatesName = await this.getLocationDataFromRashi(userId, cityId, LocationDataKey.COORDINATES_NAME, userAttributeClient)
                break
            case LocationDataKey.LOCALITY:
                locationPreference.locality = await this.getLocationDataFromRashi(userId, cityId, LocationDataKey.LOCALITY, userAttributeClient)
                break
            case LocationDataKey.CURRENT_LOC:
                locationPreference.currentLocation = this.stringToLatLong(await this.getLocationDataFromRashi(userId, cityId, LocationDataKey.CURRENT_LOC, userAttributeClient))
                break
        }
    }

    static async getLocationPreferenceForCoordinates(userId: string, cityId: string, userAttributeClient: IUserAttributeClient, locationPreference: LocationPreferenceResponseEntity, prefLocationType: LocationPreferenceSelection) {
        locationPreference.prefLocationType = prefLocationType
        if (locationPreference.prefLocationType == LocationDataKey.COORDINATES) {
            locationPreference.coordinates = this.stringToLatLong(await this.getLocationDataFromRashi(userId, cityId, LocationDataKey.COORDINATES, userAttributeClient))
        }
    }

    static getLocationPreferenceResponseEntityFromSessionData(sessionData: SessionData, cityId: string, locationPreference: LocationPreferenceResponseEntity) {
        const preference = sessionData.fitnessLocationPreference
        if (_.isNil(preference)) {
            return locationPreference
        }
        preference.settings.forEach( (setting) => {
            switch (setting.key) {
                case this.getPrefenceKey(cityId, LocationDataKey.LOC_PREF_TYPE):
                    locationPreference.prefLocationType = setting.value
                    break
                case this.getPrefenceKey(cityId, LocationDataKey.COORDINATES):
                    locationPreference.coordinates = this.stringToLatLong(setting.value)
                    break
                case this.getPrefenceKey(cityId, LocationDataKey.COORDINATES_NAME):
                    locationPreference.coordinatesName = setting.value
                    break
                case this.getPrefenceKey(cityId, LocationDataKey.LOCALITY):
                    locationPreference.locality = setting.value
                    break
                case this.getPrefenceKey(cityId, LocationDataKey.CURRENT_LOC):
                    locationPreference.currentLocation = this.stringToLatLong(setting.value)
                    break
            }
        })
    }

    private static stringToLatLong(str: string): LatLong {
        if (_.isNil(str)) {
            return null
        }
        const [lat, long] = str.split(",").map((s) => Number(s))
        return {lat, long}
    }

    private static getPrefenceKey(cityId: string, key: string): string {
        return `fitness_preference_${cityId.toLowerCase()}_${key}`
    }

    private static getRashiNamespace() {
        return "GLOBAL"
    }

    static async isCoordinateInCity(userContext: UserContext, cityId: string, lat: number, lon: number, cityService: ICityService) {
        const detectedCity = cityService.getCityAndCountry(AppUtil.getTenantFromUserContext(userContext), lat, lon).city
        const detectedCityId = detectedCity ? detectedCity.cityId : undefined
        const currentCity = cityService.getCityById(cityId)
        const currentCityName = !_.isNil(currentCity) ? currentCity.name : undefined

        if (userContext.sessionInfo.appVersion > 9.42 && cityId !== detectedCityId) {
            return Promise.resolve({ ok: false, currentCityName: currentCityName, detectedCity: detectedCity })
        }

        return { ok: true, currentCityName: "", detectedCity: undefined}
    }

    public static isValidCoordinates(lat: any, lon: any) {
        if (!lat || !lon || lat == 0 || lon == 0 || isNaN(lat) || isNaN(lon)) {
            return false
        }
        return true
    }

}

export default UserUtil