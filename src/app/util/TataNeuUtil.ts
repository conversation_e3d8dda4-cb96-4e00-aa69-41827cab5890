import { IUserSegmentClient } from "@curefit/segmentation-service-client"
import { UserContext } from "@curefit/userinfo-common"
import { Action } from "@curefit/apps-common"

export class TataNeuUtil {
    static NEUPASS_BENEFITS_TOAST_MESSAGE_VERSION = 9.47
    public static getSegmentsForTataNeu() {
        // TODO: Update PROD segment IDs after SIT testing.
        const map = {
            "STAGE": {
                "POTENTIAL_TATA_USER": 673,
                "NEUPASS_USER": 674,
                "NEUPASS_NOT_ACTIVATED": 675,
                "NEUPASS_CONSENT_PENDING": 676,
                "TATA_USER": 677
            },
            "PROD": {
                "POTENTIAL_TATA_USER": 7278,
                "NEUPASS_USER": 7277,
                "NEUPASS_NOT_ACTIVATED": 7599,
                "NEUPASS_CONSENT_PENDING": 7275,
                "TATA_USER": 7274
            }
        }
        return process.env.ENVIRONMENT === "PRODUCTION" ? map["PROD"] : map["STAGE"]
    }

    public static doesUserBelongToTataSegment(segmentationClient: IUserSegmentClient, segmentId: number, userId: string): Promise<boolean> {
        return segmentationClient.checkIfUserInSegment(segmentId, Number(userId)).then(userExistenceInSegment => {
            return userExistenceInSegment?.userExists
        }).catch(e => {
            return false
        })
    }

    public static async isNeuPassBenefitsToastMessageSupported(userContext: UserContext) {
        const { appVersion } = userContext.sessionInfo
        return appVersion >= TataNeuUtil.NEUPASS_BENEFITS_TOAST_MESSAGE_VERSION
    }

    static getNeuPassBenefitsToastMessageAction() {
        const action: Action = {
            actionType: "SHOW_TOAST_MESSAGE",
            meta: {
                message: "Your NeuPass benefits are now active",
                duration: "LONG",
                position: "BOTTOM"
            }
        }
        return {
            action
        }
    }
}