import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { IRedisDao } from "@curefit/redis-utils"
import { Announcement, AnnouncementId, AnnouncementKey, AnnouncementState, AnnouncementDisplayTimeKey } from "./Announcement"
import {
    IAnnouncement,
    ImagePopupAnnouncement,
    ImageCarouselAnnouncement,
    PolicyChangeAnnouncement,
    NewFeatureAnnouncement,
    IEventInterventionMapping
} from "@curefit/vm-common"
import {
    AnnouncementView,
    AnnouncementViewBuilder,
    AnnouncementDetails,
} from "./AnnouncementViewBuilder"
import { Logger, BASE_TYPES } from "@curefit/base"
import { eternalPromise, TimeUtil } from "@curefit/util-common"
import * as Mongoose from "mongoose"
import { UserContext } from "@curefit/userinfo-common"
import { Action, ActionType } from "@curefit/apps-common"
import * as _ from "lodash"
import { UserAllocation } from "@curefit/hamlet-common"
import AppUtil, { PARTIAL_LIVE_WATCHED_INTERVENTION_ID } from "../util/AppUtil"
import { CacheHelper } from "../util/CacheHelper"
import CultUtil, { FITCLUB_ICON, isUserActiveMember } from "../util/CultUtil"
import { IAnnouncementReadOnlyDao, VM_MODELS_TYPES, IPageService, ISegmentService } from "@curefit/vm-models"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"

const EXPIRY_FOR_NO_SHOW_POLICY_ANNOUNCEMENT = 30 * 24 * 60 * 60 // 1 month
const EXPIRY_FOR_SGT_NO_SHOW_POLICY_ANNOUNCEMENT = 30 * 24 * 60 * 60 // 1 month
const EXPIRY_FOR_FIRST_CLASS = 7 * 24 * 60 * 60 // 7 days
const DEFAULT_EXPIRY = 60 * 24 * 60 * 60 // 2 months
const REPORT_EXPIRY = 3 * 24 * 60 * 60 // 3 day
const EXPIRY_FOR_PT_TRAINER_CHANGE_ANNOUNCEMENT = 7 * 24 * 60 * 60

export const EXPIRY_DATE_FOR_ANNOUNCEMENT_MAP: { [key: string]: string } = {
    "waitlist_feature": "2019-04-25",
    "no_show_policy": "2019-03-15",
    "sgt_no_show_policy": "2020-09-25",
    "mb_sale_prebuzz": "2019-03-25",
    "therapy_policy": "2019-04-13",
    "pt_policy": "2019-08-01",
    "pulse": "2019-08-01",
    "pulse_rental": "2020-01-01",
    "booking_preference": "2019-08-01",
    "dropout_feature": "2019-08-01",
    "call_reminder": "2019-09-01",
    "care_3p_policy": "2020-12-01",
    "swimming_ppc_policy_center_purchase": "2020-01-01",
    "swimming_ppc_policy_low_usage": "2020-01-01",
    "swimming_ppc_policy_high_usage": "2020-01-01",
    "introducing_gym_fit": "2020-05-01",
    "cult_social": "2020-04-14",
    "cult_corona": "2020-04-01",
    "eat_corona": "2020-04-01",
    "live_pt": "2020-06-01",
    "care_tc": "2020-05-03",
    "eat_nutritionist_consulation": "2020-04-18",
    "change_trainer_tooltip": "2020-08-01",
    "referral_invite_announcement": "2020-08-03",
}

export const INTERVENTION_EVENTS = {
    partialLiveWatchedEvent: "partial_live_watched_event",
    partialDiyWatchedEvent: "partial_diy_watched_event",
    postRatingEvent: "post_rating_event",
}

@injectable()
export class AnnouncementBusiness {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
        @inject(CUREFIT_API_TYPES.AnnouncementRedisDao) private announcementDao: IRedisDao<AnnouncementKey, Announcement>,
        @inject(CUREFIT_API_TYPES.AnnouncementDisplayTimeRedisDao) private announcementDisplayTimeDao: IRedisDao<AnnouncementDisplayTimeKey, Number>,
        @inject(VM_MODELS_TYPES.AnnouncementReadOnlyDao) private announcementReadOnlyDoa: IAnnouncementReadOnlyDao,
        @inject(CUREFIT_API_TYPES.AnnouncementViewBuilder) public announcementViewBuilder: AnnouncementViewBuilder,
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) private membershipService: IMembershipService,
    ) {
    }

    public async getAllValidAnnouncements(pageService: IPageService, userContext: UserContext, autoTrigger: Boolean, ids?: string[]): Promise<IAnnouncement[]> {
        const announcements = await pageService.getAnnouncement()
        const currentTime = new Date().getTime()
        const validAnnouncements = announcements.filter((announcement: IAnnouncement) => {
            if ((announcement && new Date(announcement.startDate.date).getTime() <= currentTime) &&
                (announcement.expiryDate && new Date(announcement.expiryDate.date).getTime() >= currentTime) &&
                announcement.tenant.some(tenant => AppUtil.getTenantFromUserContext(userContext) === tenant)) {
                if (autoTrigger !== announcement.autoTrigger) {
                    return false
                }
                if (!_.isEmpty(ids) && ids.some((id: string) => announcement.announcementId === id) === false) {
                    return false
                }
                return true
            }
            return false
        })
        return validAnnouncements
    }

    /**
     * @param autoTrigger if true announcement with autoTrigger will be return only  and vice-versa
     * @param [ids] fetch particular announcement if no id is passed it will fetch all the records
     */
    public async getSingleActiveAnnouncement(pageService: IPageService, segmentService: ISegmentService, userContext: UserContext, autoTrigger: Boolean, ids?: string[], customExpiryInSecs?: number): Promise<IAnnouncement> {
        try {
            const validAnnouncements = await this.getAllValidAnnouncements(pageService, userContext, autoTrigger, ids)

            // all valid announcement are sorted based on priority
            if (validAnnouncements.length) {
                for (let i = 0; i < validAnnouncements.length; i++) {
                    let isValid = await this.shouldShowAnnouncement(userContext, validAnnouncements[i], (validAnnouncements[i]?.coolOff && validAnnouncements[i]?.coolOff > 0) ? validAnnouncements[i].coolOff : customExpiryInSecs)
                    if (validAnnouncements[i].segment?.length) {
                        // checking if user eligible for segement
                        const segments = await Promise.all(validAnnouncements[i].segment.map(segment => segmentService.doesUserBelongToSegment(segment, userContext)))
                        if (!segments.every(segment => segment)) {
                            this.logger.info("User not in announcement's segment" + JSON.stringify(segments.map(s => s?.segmentId)) + " announcementId: " + validAnnouncements[i].announcementId)
                            isValid = false
                        }
                    }
                    // check if login is required
                    if (validAnnouncements[i].requireUserLoggedIn && !userContext.sessionInfo.isUserLoggedIn) {
                        this.logger.info("Announcemenet requires user to be loggedin. AnnouncementId: " + validAnnouncements[i].announcementId)
                        isValid = false
                    }
                    if (isValid) {
                        return validAnnouncements[i]
                    }
                }
            }
        } catch (err) {
            this.logger.info("Error while fetching announcement: " + err)
        }
    }

    public async createAnnouncement(announcement: Announcement): Promise<boolean> {
        const isCreated = await this.announcementDao.createWithExpiry(AnnouncementKey.from(announcement.userId, announcement.announcementId), announcement, announcement.expiryInSecs)
        this.logger.debug(`announcement created for userId ${announcement.userId} and announcementId ${announcement.announcementId}`)
        return isCreated
    }

    public async updateAnnouncementState(userId: string, announcementId: AnnouncementId, state: AnnouncementState): Promise<boolean> {
        const announcement = await this.announcementDao.read(AnnouncementKey.from(userId, announcementId))
        this.logger.info(`AnnouncementBusiness.updateAnnouncementState`, {
            announcement: announcement, state: state, userId: userId, announcementId: announcementId, announcementKey: AnnouncementKey.from(userId, announcementId).lookupKey()
        })
        let isUpdated = false
        if (announcement) {
            announcement.state = state
            const announcementUpdateEpoch: number = TimeUtil.getCurrentEpoch()
            const announcementPromises: Promise<any>[] = []
            announcementPromises.push(this.announcementDao.updateWithExpiry(AnnouncementKey.from(announcement.userId, announcement.announcementId), announcement, announcement.expiryInSecs))
            announcementPromises.push(this.announcementDisplayTimeDao.upsertWithExpiry(AnnouncementDisplayTimeKey.from(userId), announcementUpdateEpoch, TimeUtil.TIME_IN_SECONDS.DAY))
            await Promise.all(announcementPromises)
            this.logger.info(`announcement updated for userId ${announcement.userId} and announcementId ${announcement.announcementId}. Announcement display epoch: ${announcementUpdateEpoch}`)
            isUpdated = true
        }
        return isUpdated
    }

    public async getAnnouncementToShow(userContext: UserContext, announcementId: AnnouncementId, meta?: any): Promise<AnnouncementView | Announcement | AnnouncementDetails> {
        const isAnnouncementValid = this.isAnnouncementValid(userContext, announcementId)
        if (!isAnnouncementValid) {
            return Promise.resolve(undefined)
        }
        const userId = userContext.userProfile.userId
        let announcement = await this.announcementDao.read(AnnouncementKey.from(userId, announcementId))
        if (!announcement) {
            const date = new Date()
            announcement = {
                userId: userId,
                announcementId: announcementId,
                state: "CREATED",
                expiryInSecs: this.getAnnouncementExpiry(userContext, announcementId),
                createdInMillis: date.getTime()
            }
            await this.createAnnouncement(announcement)
        }
        if (announcement.state === "CREATED") {
            return this.announcementViewBuilder.buildAnnouncementView(userContext, announcement, meta)
        }

    }

    public async shouldShowAnnouncement(userContext: UserContext, currentAnnouncement: IAnnouncement, customExpiryInSecs?: number): Promise<Boolean> {
        const userId = userContext.userProfile.userId
        let announcement = await this.announcementDao.read(AnnouncementKey.from(userId, currentAnnouncement.announcementId))
        if (!announcement) {
            const date = new Date()
            announcement = {
                userId: userId,
                announcementId: currentAnnouncement.announcementId,
                state: "CREATED",
                expiryInSecs: customExpiryInSecs ? customExpiryInSecs : Math.ceil((currentAnnouncement.expiryDate.date.getTime() - date.getTime() + TimeUtil.TIME_IN_MILLISECONDS.MONTH) / 1000),
                createdInMillis: date.getTime()
            }
            await this.createAnnouncement(announcement)
        }
        if (announcement.state === "CREATED") {
            return true
        }
    }

    public async isAnnouncementAlreadyShown(userContext: UserContext, announcementId: string): Promise<Boolean> {
        const userId = userContext.userProfile.userId
        const announcement = await this.announcementDao.read(AnnouncementKey.from(userId, announcementId))
        return announcement?.state === "DISMISSED"
    }

    public async createAndGetAnnouncementDetails(userContext: UserContext, announcementId: AnnouncementId): Promise<AnnouncementDetails> {
        const userId = userContext.userProfile.userId
        let announcement = await this.announcementDao.read(AnnouncementKey.from(userId, announcementId))
        if (!announcement) {
            const date = new Date()
            announcement = {
                userId: userId,
                announcementId: announcementId,
                state: "CREATED",
                expiryInSecs: this.getAnnouncementExpiry(userContext, announcementId),
                createdInMillis: date.getTime()
            }
            await this.createAnnouncement(announcement)
        }
        return this.announcementViewBuilder.getAnnouncementDetails(announcement)
    }

    public async createAndGetChangeTrainerTooltipAnnouncementDetails(userContext: UserContext) {
        const announcementDetails = await this.createAndGetAnnouncementDetails(userContext, "change_trainer_tooltip")
        return {
            ...announcementDetails,
            title: "Want to change your trainer? We might have a few recommendations for you.",
            dismissAction: {
                actionType: "REST_API",
                title: "DISMISS",
                icon: "/image/icons/cult/cancel_white.png",
                meta: {
                    method: "POST",
                    url: `/user/announcement/${announcementDetails.announcementId}`,
                    body: { "state": "DISMISSED" }
                }
            }
        }
    }

    public async createNuxFirstClassCompletedAnnouncement(userId: string, announcementId: AnnouncementId) {
        let announcement = await this.announcementDao.read(AnnouncementKey.from(userId, announcementId))
        if (!announcement) {
            const date = new Date()
            announcement = {
                userId: userId,
                announcementId: announcementId,
                state: "CREATED",
                expiryInSecs: EXPIRY_FOR_FIRST_CLASS,
                createdInMillis: date.getTime()
            }
            await this.createAnnouncement(announcement)
        }
    }

    public async getNuxFirstClassCompletedAnnouncement(userId: string, announcementId: AnnouncementId) {
        const announcement = await this.announcementDao.read(AnnouncementKey.from(userId, announcementId))
        if (!announcement) {
            return Promise.resolve(undefined)
        }
        return this.announcementViewBuilder.getAnnouncementDetails(announcement)
    }

    public async createReportGeneratedAnnouncement(userId: string, reportDate: string): Promise<boolean> {
        const announcementId = `FITNESS_REPORT_${reportDate}`
        let announcement = await this.announcementDao.read(AnnouncementKey.from(userId, announcementId))
        if (!announcement) {
            const date = new Date()
            announcement = {
                userId: userId,
                announcementId: announcementId,
                state: "CREATED",
                expiryInSecs: REPORT_EXPIRY,
                createdInMillis: date.getTime()
            }
            return await this.createAnnouncement(announcement)
        }
        return true
    }

    public async createAndGetLivePTTnCAnnouncement(userContext: UserContext, nextAction: Action) {
        const userId = userContext.userProfile.userId
        const announcementId = "live_pt_tnc"
        let announcement = await this.announcementDao.read(AnnouncementKey.from(userId, announcementId))
        if (!announcement) {
            const date = new Date()
            announcement = {
                userId: userId,
                announcementId: announcementId,
                state: "CREATED",
                expiryInSecs: this.getAnnouncementExpiry(userContext, announcementId),
                createdInMillis: date.getTime()
            }
            await this.createAnnouncement(announcement)
        }
        if (announcement.state === "CREATED") {
            return this.announcementViewBuilder.buildLivePTTncAnnouncementView(userContext, announcement, nextAction)
        }
    }

    public async getReportGeneratedAnnouncement(userId: string, reportDate: string) {
        const announcementId = `FITNESS_REPORT_${reportDate}`
        const announcement = await this.announcementDao.read(AnnouncementKey.from(userId, announcementId))
        if (!announcement) {
            return Promise.resolve(undefined)
        }
        return this.announcementViewBuilder.getAnnouncementDetails(announcement)
    }

    private isAnnouncementValid(userContext: UserContext, announcementId: AnnouncementId): boolean {
        const today = TimeUtil.todaysDateWithTimezone(userContext.userProfile.timezone)
        return today < EXPIRY_DATE_FOR_ANNOUNCEMENT_MAP[announcementId]
    }

    private getAnnouncementExpiry(userContext: UserContext, announcementId: AnnouncementId): number {
        switch (announcementId) {
            case "pt_trainer_change":
                return EXPIRY_FOR_PT_TRAINER_CHANGE_ANNOUNCEMENT
            case "no_show_policy":
                return EXPIRY_FOR_NO_SHOW_POLICY_ANNOUNCEMENT
            case "sgt_no_show_policy":
                return EXPIRY_FOR_SGT_NO_SHOW_POLICY_ANNOUNCEMENT
            case "mb_sale_prebuzz":
                const saleEndDate = TimeUtil.getDate(EXPIRY_DATE_FOR_ANNOUNCEMENT_MAP[announcementId], 0, 0, userContext.userProfile.timezone)
                return saleEndDate.getTime() - new Date().getTime()
            default: return DEFAULT_EXPIRY
        }
    }


    public async getCareAnnouncement(userContext: UserContext): Promise<AnnouncementView | null> {
        const announcementResult = await eternalPromise(this.getAnnouncementToShow(userContext, "care_tc"))
        return announcementResult && announcementResult.obj ? <AnnouncementView>announcementResult.obj : null
    }

    public async getEatCoronaAnnouncement(userContext: UserContext): Promise<AnnouncementView | null> {
        let announcement = null
        const eatSummary = await this.userCache.getEatSummary(userContext.userProfile.userId)
        const eatSingleOrders = eatSummary && !_.isNil(eatSummary.numEatOrders) ? eatSummary.numEatOrders : 0
        const eatPackOrders = eatSummary && !_.isNil(eatSummary.numEatPackOrders) ? eatSummary.numEatPackOrders : 0

        if (eatSingleOrders > 0 || eatPackOrders > 0) {
            const announcementResult = await eternalPromise(this.getAnnouncementToShow(userContext, "eat_corona"))
            announcement = announcementResult && announcementResult.obj ? <AnnouncementView>announcementResult.obj : null
        }
        return announcement
    }

    public async getNutritionistConsultantationAnnouncement(userContext: UserContext): Promise<AnnouncementView | null> {
        const announcementResult = await eternalPromise(this.getAnnouncementToShow(userContext, "eat_nutritionist_consulation"))
        return announcementResult && announcementResult.obj ? <AnnouncementView>announcementResult.obj : null
    }

    public async getGymFitAnnouncement(userContext: UserContext): Promise<AnnouncementView | null> {
        if (await AppUtil.doesUserBelongToGymFitExperiment(userContext)) {
            const announcementResult = await eternalPromise(this.getAnnouncementToShow(userContext, "introducing_gym_fit"))
            const announcement = announcementResult && announcementResult.obj ? <AnnouncementView>announcementResult.obj : null
            return announcement
        }
    }

    public async getAnnouncementOnDemand(userContext: UserContext, announcementId: string, pageService: IPageService, segmentService: ISegmentService, customExpiryInSecs?: number): Promise<ImagePopupAnnouncement | ImageCarouselAnnouncement | PolicyChangeAnnouncement | NewFeatureAnnouncement | null> {
        const announcementResult = await eternalPromise(this.getSingleActiveAnnouncement(pageService, segmentService, userContext, false, [announcementId], customExpiryInSecs))
        if (!_.isEmpty(announcementResult?.err)) {
            this.logger.error("Error while fetching announcement:" + JSON.stringify(announcementResult?.err, ["message", "stack"]))
        }
        return announcementResult?.obj?.announcementData
    }

    public async getCultSocialAnnouncement(userContext: UserContext): Promise<AnnouncementView | null> {
        let announcement = null
        if (AppUtil.isCultSocialSupported(userContext)) {
            const cultAndMindSummary = await this.userCache.getCultSummary(userContext.userProfile.userId)
            const completedClassCount = CultUtil.getCompletedClassCount(cultAndMindSummary)
            if (completedClassCount > 0) {
                const announcementResult = await eternalPromise(this.getAnnouncementToShow(userContext, "cult_social"))
                announcement = announcementResult && announcementResult.obj ? <AnnouncementView>announcementResult.obj : null
            }
        }
        return announcement
    }

    public async getCultCoronaAnnouncement(userContext: UserContext): Promise<AnnouncementView | null> {
        let announcement = null
        if (AppUtil.isCultSocialSupported(userContext)) {
            if (await isUserActiveMember(userContext, this.membershipService)) {
                const announcementResult = await eternalPromise(this.getAnnouncementToShow(userContext, "cult_corona"))
                announcement = announcementResult && announcementResult.obj ? <AnnouncementView>announcementResult.obj : null
            }
        }
        return announcement
    }

    public async getLivePTAnnouncementView(userContext: UserContext): Promise<AnnouncementView | null> {
        let announcement = null
        if (AppUtil.isLivePTSupported(userContext)) {
            const announcementResult = await eternalPromise(this.getAnnouncementToShow(userContext, "live_pt"))
            announcement = announcementResult && announcementResult.obj ? <AnnouncementView>announcementResult.obj : null
        }
        return announcement
    }

    async getFitClubRewardAnnouncement(userContext: UserContext): Promise<any> {
        const announcementDetails = <AnnouncementDetails>await this.createAndGetAnnouncementDetails(userContext, "CULT-FITCLUB-REWARD")
        if (announcementDetails.state === "CREATED") {
            const fitClubRewardAnnouncement: any = {
                description: "Daily reward on attending cult/mind class",
                icon: FITCLUB_ICON,
                action: {
                    title: "KNOW MORE",
                    actionType: "NAVIGATION",
                    url: "curefit://listPage?pageId=Fitclub_campaign"
                },
                dontDismissOnOtherActions: true,
                dismissAction: {
                    actionType: "REST_API",
                    title: "DISMISS",
                    meta: {
                        method: "POST",
                        url: `/user/announcement/${announcementDetails.announcementId}`,
                        body: { "state": "DISMISSED" }
                    }
                }
            }
            return fitClubRewardAnnouncement
        }
        return undefined
    }

    public async getAnnouncement(userContext: UserContext, announcementId: string, announcementActionType: ActionType, pageService: IPageService, segmentService: ISegmentService, customExpiryInSecs?: number): Promise<Action | null> {
        const announcementResult = await this.getAnnouncementOnDemand(userContext, announcementId, pageService, segmentService, customExpiryInSecs)
        return announcementResult ? { actionType: announcementActionType, meta: announcementResult } : null
    }

    public async getReferralAnnouncement(userContext: UserContext, announcementId: string, announcementActionType: ActionType, meta?: any): Promise<Action | null> {
        const announcementResult = await eternalPromise(this.getAnnouncementToShow(userContext, announcementId, meta))
        const data = announcementResult?.obj
        return data ? { actionType: announcementActionType, meta: data } : null
    }

    public async getActiveAnnouncement(userContext: UserContext): Promise<AnnouncementView | null> {
        const nutritionistPromise = this.getNutritionistConsultantationAnnouncement(userContext)
        const carePromise = this.getCareAnnouncement(userContext)
        const cultCoronaPromise = this.getCultCoronaAnnouncement(userContext)
        const livePtPromise = this.getLivePTAnnouncementView(userContext)
        const eatCoronaPromise = this.getEatCoronaAnnouncement(userContext)
        const cultSocialPromise = this.getCultSocialAnnouncement(userContext)
        const gymFitPromise = this.getGymFitAnnouncement(userContext)

        const nutritionistAnnouncement = await nutritionistPromise
        if (nutritionistAnnouncement) {
            return nutritionistAnnouncement
        }
        const careAnnouncement = await carePromise
        if (careAnnouncement) {
            return careAnnouncement
        }
        const cultCoronaAnnouncement = await cultCoronaPromise
        if (cultCoronaAnnouncement) {
            return cultCoronaAnnouncement
        }
        const livePtAnnouncement = await livePtPromise
        if (livePtAnnouncement) {
            return livePtAnnouncement
        }
        const eatCoronaAnnouncement = await eatCoronaPromise
        if (eatCoronaAnnouncement) {
            return eatCoronaAnnouncement
        }
        const cultSocialAnnouncement = await cultSocialPromise
        if (cultSocialAnnouncement) {
            return cultSocialAnnouncement
        }
        const gymFitAnnouncement = await gymFitPromise
        if (gymFitAnnouncement) {
            return gymFitAnnouncement
        }
        return null
    }

}
