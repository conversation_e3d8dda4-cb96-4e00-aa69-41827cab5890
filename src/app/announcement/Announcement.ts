import { Key } from "@curefit/redis-utils"

const NAMESPACE = "cf-app:"

export class Announcement<PERSON><PERSON> implements Key {
  userId: string
  key: string

  constructor(userId: string, announcementId: string) {
    this.userId = userId
    this.key = `${NAMESPACE}announcement:${userId}:${announcementId}`
  }

  lookupKey(): string {
    return this.key
  }

  static from(userId: string, announcementId: string) {
    return new AnnouncementKey(userId, announcementId)
  }
}

export class AnnouncementDisplayTimeKey implements Key {
  userId: string
  key: string

  constructor(userId: string) {
    this.userId = userId
    this.key = `${NAMESPACE}announcement:lastDisplayTime:${userId}`
  }

  lookupKey(): string {
    return this.key
  }

  static from(userId: string) {
    return new AnnouncementDisplayTimeKey(userId)
  }
}

export type AnnouncementId =
  | "waitlist_feature"
  | "no_show_policy"
  | "sgt_no_show_policy"
  | "mb_sale_prebuzz"
  | "therapy_policy"
  | "pt_policy"
  | "pulse"
  | "pulse_rental"
  | "booking_preference"
  | "dropout_feature"
  | "call_reminder"
  | "care_3p_policy"
  | "swimming_ppc_policy_center_purchase"
  | "swimming_ppc_policy_low_usage"
  | "swimming_ppc_policy_high_usage"
  | "introducing_gym_fit"
  | "cult_social"
  | "cult_corona"
  | "eat_corona"
  | "live_pt"
  | "care_tc"
  | "live_pt_tnc"
  | "change_trainer_tooltip"
  | "referral_invite_announcement"
  | "pt_trainer_change"
  | string

export type AnnouncementState = "CREATED" | "DISMISSED"

export interface Announcement {
  userId: string
  expiryInSecs: number
  announcementId: AnnouncementId
  state: AnnouncementState
  createdInMillis: number
}
