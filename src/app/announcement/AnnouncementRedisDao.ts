import { inject } from "inversify"
import { BaseRedisDaoImpl, IMultiCrudKeyValue, REDIS_TYPES } from "@curefit/redis-utils"
import { Announcement, AnnouncementKey, AnnouncementDisplayTimeKey } from "./Announcement"

export class AnnouncementRedisDao extends BaseRedisDaoImpl<AnnouncementKey, Announcement> {
    constructor(@inject(REDIS_TYPES.MultiCrudKeyValueDao) multiCrudKeyValueDao: IMultiCrudKeyValue) {
        super(multiCrudKeyValueDao)
    }
}

export class AnnouncementDisplayTimeRedisDao extends BaseRedisDaoImpl<AnnouncementDisplayTimeKey, Number> {
    constructor(@inject(REDIS_TYPES.MultiCrudKeyValueDao) multiCrudKeyValueDao: IMultiCrudKeyValue) {
        super(multiCrudKeyValueDao)
    }
}
