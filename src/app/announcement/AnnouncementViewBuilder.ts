import { injectable } from "inversify"
import { Announcement, AnnouncementState, AnnouncementId } from "./Announcement"
import { SWIMMING_SEGMENT_ID } from "../util/CultUtil"
import { Action } from "../common/views/WidgetView"
import { UserContext } from "@curefit/userinfo-common"
import LiveUtil from "../util/LiveUtil"
import { ViewStyle } from "@curefit/apps-common"

export type AnnouncementViewType = "NEW_FEATURE" | "POLICY_CHANGE" | "IMAGE_POPUP" | "IMAGE_CAROUSEL_POPUP" | "WIDGETIZED_GENERIC"

export interface AnnouncementView {
    type: AnnouncementViewType,
    position?: "bottom" | "center"
    analyticsData?: any,
}

export interface FeatureAnnouncementView extends AnnouncementView {
    tag: string
    title: string
    message: string
    dismissAction: Action
    otherActions: Action[]
}

export interface PolicyAnnouncementView extends AnnouncementView {
    url: string
    ackMessage: string
    acceptAction?: Action,
    dismissAction?: Action
    ackRequired?: boolean
}

export interface ImagePopupAnnouncementView extends AnnouncementView {
    imageUrl: string
    dismissAction: Action
    action: Action
    layout: { width: number, height: number }
}

export interface ImageCarouselAnnouncementView extends AnnouncementView {
    announcementData: { url: string }[]
    action: Action
    dismissAction?: Action
}

export interface WidgetizedAnnouncementMeta {
    position: string
    modalStyle?: ViewStyle
    containerStyle?: ViewStyle
    swipeToClose: boolean
    showHeader: boolean
    closeButton?: { icon: string, action: Action[] }
    actionButtonContainer?: ViewStyle
}

export interface WidgetAction {
    title: string
    buttonStyle?: any
    action: Action[]
}

export interface WidgetizedAnnouncement extends AnnouncementView {
    action: WidgetAction[]
    widgets: any
    actionText?: string
    meta: WidgetizedAnnouncementMeta
}

export interface AnnouncementDetails {
    announcementId: AnnouncementId,
    state: AnnouncementState
}

@injectable()
export class AnnouncementViewBuilder {

    async buildAnnouncementView(userContext: UserContext, announcement: Announcement, meta?: any): Promise<AnnouncementView | Announcement> {
        switch (announcement.announcementId) {
            case "waitlist_feature": return this.buildWaitlistFeatureAnnouncementView(announcement)
            case "no_show_policy": return this.buildNoShowPolicyAnnouncementView(announcement)
            case "sgt_no_show_policy": return this.buildSgtNoShowPolicyAnnouncementView(announcement)
            case "mb_sale_prebuzz": return announcement
            case "therapy_policy": return this.buildTherapyPolicyAnnouncementView(announcement)
            case "pt_policy": return this.buildPTPolicyAnnouncementView(announcement)
            case "pulse": return this.buildPulseAnnouncementView(announcement)
            case "pulse_rental": return this.buildPulseAnnouncementView(announcement)
            case "booking_preference": return this.buildBookingPreferenceAnnouncementView(announcement)
            case "dropout_feature": return this.buildDropoutFeatureAnnouncementView(announcement)
            case "call_reminder": return this.buildCallReminderFeatureAnnouncementView(announcement)
            case "care_3p_policy": return this.buildCare3pPolicyAnnouncementView(announcement)
            case "swimming_ppc_policy_center_purchase": return this.buildSwimmingPPCPolicyChange(announcement, "swimming_center_purchase")
            case "swimming_ppc_policy_high_usage": return this.buildSwimmingPPCPolicyChange(announcement, "greater_that_50_swimming_usage")
            case "introducing_gym_fit": return this.buildIntroducingGymFit(announcement)
            case "cult_social": return this.buildCultSocialAnnouncementView(announcement)
            case "live_pt": return this.buildLivePTAnnouncementView(announcement)
            case "cult_corona": return this.buildCultCoronaAnnouncementView(userContext, announcement)
            case "eat_corona": return this.buildEatCoronaAnnouncementView(userContext, announcement)
            case "care_tc": return this.buildCareTCAnnouncementView(userContext, announcement)
            case "referral_invite_announcement": return this.buildInviteLinkAnnouncement(announcement, meta.classId)
            case "eat_nutritionist_consulation": return this.buildNutritionistAnnouncementView(userContext, announcement)
            default: return
        }
    }

    async getAnnouncementDetails(announcement: Announcement): Promise<AnnouncementDetails> {
        return { announcementId: announcement.announcementId, state: announcement.state }
    }

    private async buildInviteLinkAnnouncement(announcement: Announcement, classId: string): Promise<ImageCarouselAnnouncementView> {
        const dismissAction: Action = {
            actionType: "REST_API",
            iconUrl: "/image/icons/cult/cancel-modal.png",
            meta: {
                method: "POST",
                url: `/user/announcement/${announcement.announcementId}`,
                body: { "state": "DISMISSED" }
            }
        }
        return {
            type: "IMAGE_CAROUSEL_POPUP",
            action: await LiveUtil.getInviteBuddyLazyLoadAction(classId, { "source": "cult_live" }),
            announcementData: [{ url: "/image/livefit/app/referral/invite_1.png" }, { url: "/image/livefit/app/referral/invite_2.png" }, { url: "/image/livefit/app/referral/invite_3.png" }],
            dismissAction,
            analyticsData: {
                announcement_id: announcement.announcementId
            }
        }
    }

    private async buildSwimmingPPCPolicyChange(announcement: Announcement, segmentId: SWIMMING_SEGMENT_ID): Promise<PolicyAnnouncementView> {
        const acceptAction: Action = {
            actionType: "REST_API",
            title: "Got it",
            meta: {
                method: "POST",
                url: `/user/announcement/${announcement.announcementId}`,
                body: { "state": "DISMISSED" }
            }
        }
        let url = ""
        switch (segmentId) {
            case "greater_that_50_swimming_usage":
                url = "https://s3.ap-south-1.amazonaws.com/vm-html-pages/Swimming%20high%20usage-e23dcd09-faf8-41c4-98ad-811ca29ae661.html"
                break
            case "swimming_center_purchase":
                url = "https://s3.ap-south-1.amazonaws.com/vm-html-pages/Swimming%20purchase%20centre-87a3fdc3-1672-4d52-b611-ccce25e9c4cd.html"
                break
            default:
                return
        }
        return {
            type: "POLICY_CHANGE",
            url,
            ackMessage: "I have read and acknowledge these policy changes",
            acceptAction: acceptAction,
            analyticsData: {
                announcement_id: announcement.announcementId
            }
        }
    }

    private async buildPulseAnnouncementView(announcement: Announcement): Promise<FeatureAnnouncementView> {
        const dismissAction: Action = {
            actionType: "REST_API",
            title: "DISMISS",
            meta: {
                method: "POST",
                url: `user/announcement/${announcement.announcementId}`,
                body: {
                    state: "DISMISSED"
                }
            }
        }

        return {
            type: "NEW_FEATURE",
            tag: "NEW",
            title: "PULSE workouts",
            message: "Workouts powered by PULSE wearable kits, to help measure your performance and achieve better results",
            dismissAction,
            otherActions: [
                {
                    actionType: "SHOW_ONBOARDING",
                    title: "KNOW MORE",
                    onboardingType: "pulse",
                }
            ]
        }
    }


    private async buildBookingPreferenceAnnouncementView(announcement: Announcement): Promise<FeatureAnnouncementView> {
        const dismissAction: Action = {
            actionType: "REST_API",
            title: "DISMISS",
            meta: {
                method: "POST",
                url: `user/announcement/${announcement.announcementId}`,
                body: {
                    state: "DISMISSED"
                }
            }
        }

        return {
            type: "NEW_FEATURE",
            tag: "NEW",
            title: "Introducing Calendar Notifications",
            message: "Your booked classes will now appear on your Google calendar. Get notified in advance at a time decided by you on your calendar.",
            dismissAction,
            otherActions: [
                {
                    actionType: "NAVIGATION",
                    title: "KNOW MORE",
                    url: "curefit://accountview?expanded=BOOKING_PREFERENCE"
                }
            ],
            analyticsData: {
                announcement_id: announcement.announcementId
            }
        }
    }

    private async buildWaitlistFeatureAnnouncementView(announcement: Announcement): Promise<FeatureAnnouncementView> {
        const dismissAction: Action = {
            actionType: "REST_API",
            title: "DISMISS",
            meta: {
                method: "POST",
                url: `/user/announcement/${announcement.announcementId}`,
                body: { "state": "DISMISSED" }
            }
        }
        return {
            type: "NEW_FEATURE",
            tag: "NEW",
            title: "Introducing Waitlist",
            message: "Now join waitlist for classes that are running full and get a confirmed booking as soon as there is a cancellation",
            dismissAction: dismissAction,
            otherActions: [
                {
                    actionType: "NAVIGATION",
                    title: "KNOW MORE",
                    url: "curefit://listpage?pageId=waitlisthiw"
                }
            ],
            analyticsData: {
                announcement_id: announcement.announcementId
            }
        }
    }

    private async buildNoShowPolicyAnnouncementView(announcement: Announcement): Promise<PolicyAnnouncementView> {
        const acceptAction: Action = {
            actionType: "REST_API",
            title: "Got it",
            meta: {
                method: "POST",
                url: `/user/announcement/${announcement.announcementId}`,
                body: { "state": "DISMISSED" }
            }
        }
        return {
            type: "POLICY_CHANGE",
            url: "https://s3.ap-south-1.amazonaws.com/vm-html-pages/noshow_policy.html",
            ackMessage: "I have read and acknowledge the No-Show changes",
            acceptAction: acceptAction,
            analyticsData: {
                announcement_id: announcement.announcementId
            }
        }
    }

    private async buildSgtNoShowPolicyAnnouncementView(announcement: Announcement): Promise<PolicyAnnouncementView> {
        const acceptAction: Action = {
            actionType: "REST_API",
            title: "GOT IT",
            meta: {
                method: "POST",
                url: `/user/announcement/${announcement.announcementId}`,
                body: { "state": "DISMISSED" }
            }
        }
        return {
            type: "POLICY_CHANGE",
            position: "bottom",
            url: "https://s3.ap-south-1.amazonaws.com/vm-html-pages/sgt_noshow_policy.html",
            ackMessage: "I have read and acknowledge the policy changes",
            acceptAction: acceptAction,
            analyticsData: {
                announcement_id: announcement.announcementId
            }
        }
    }

    private async buildTherapyPolicyAnnouncementView(announcement: Announcement): Promise<PolicyAnnouncementView> {
        const acceptAction: Action = {
            actionType: "REST_API",
            title: "Got it",
            meta: {
                method: "POST",
                url: `/user/announcement/${announcement.announcementId}`,
                body: { "state": "DISMISSED" }
            }
        }
        return {
            type: "POLICY_CHANGE",
            url: "https://s3.ap-south-1.amazonaws.com/vm-html-pages/therapy_policy.html",
            ackMessage: "I have read and acknowledge the policy changes",
            acceptAction: acceptAction,
            analyticsData: {
                announcement_id: announcement.announcementId
            }
        }
    }

    private async buildPTPolicyAnnouncementView(announcement: Announcement): Promise<PolicyAnnouncementView> {
        const acceptAction: Action = {
            actionType: "REST_API",
            title: "Got it",
            meta: {
                method: "POST",
                url: `/user/announcement/${announcement.announcementId}`,
                body: { "state": "DISMISSED" }
            }
        }
        return {
            type: "POLICY_CHANGE",
            url: "https://s3.ap-south-1.amazonaws.com/vm-html-pages/pt_policy.html",
            ackMessage: "I have read and acknowledge the policy changes",
            acceptAction: acceptAction,
            analyticsData: {
                announcement_id: announcement.announcementId
            }
        }
    }

    private async buildDropoutFeatureAnnouncementView(announcement: Announcement): Promise<FeatureAnnouncementView> {
        const dismissAction: Action = {
            actionType: "REST_API",
            title: "DISMISS",
            meta: {
                method: "POST",
                url: `/user/announcement/${announcement.announcementId}`,
                body: { "state": "DISMISSED" }
            }
        }
        return {
            type: "NEW_FEATURE",
            tag: "NEW",
            title: "Introducing ‘Last minute dropout’",
            message: "Last Minute Dropout feature gives you the convenience of dropping out of a class after cancellation time limit.",
            dismissAction: dismissAction,
            otherActions: [
                {
                    actionType: "NAVIGATION",
                    title: "KNOW MORE",
                    url: "curefit://listpage?pageId=dropouthiw"
                }
            ],
            analyticsData: {
                announcement_id: announcement.announcementId
            }
        }
    }

    private async buildCallReminderFeatureAnnouncementView(announcement: Announcement): Promise<FeatureAnnouncementView> {
        const dismissAction: Action = {
            actionType: "REST_API",
            title: "DISMISS",
            meta: {
                method: "POST",
                url: `/user/announcement/${announcement.announcementId}`,
                body: { "state": "DISMISSED" }
            }
        }
        return {
            type: "NEW_FEATURE",
            tag: "NEW",
            title: "Introducing ‘Call Reminders’",
            message: "Now get a call reminder for your early morning classes. You can change the reminder time as per your convenience",
            dismissAction: dismissAction,
            otherActions: [
                {
                    actionType: "NAVIGATION",
                    title: "KNOW MORE",
                    url: "curefit://listpage?pageId=callreminderhiw"
                }
            ],
            analyticsData: {
                announcement_id: announcement.announcementId
            }
        }
    }

    private async buildCare3pPolicyAnnouncementView(announcement: Announcement): Promise<PolicyAnnouncementView> {
        const acceptAction: Action = {
            actionType: "REST_API",
            title: "Got it",
            meta: {
                method: "POST",
                url: `/user/announcement/${announcement.announcementId}`,
                body: { "state": "DISMISSED" }
            }
        }
        return {
            type: "POLICY_CHANGE",
            url: "https://s3.ap-south-1.amazonaws.com/vm-html-pages/powered_by_carefit.html",
            ackMessage: "Have read",
            acceptAction: acceptAction,
            ackRequired: false,
            analyticsData: {
                announcement_id: announcement.announcementId
            }
        }
    }

    private async buildIntroducingGymFit(announcement: Announcement): Promise<ImagePopupAnnouncementView> {
        const dismissAction: Action = {
            actionType: "REST_API",
            title: "DISMISS",
            meta: {
                method: "POST",
                url: `/user/announcement/${announcement.announcementId}`,
                body: { "state": "DISMISSED" }
            }
        }
        return {
            imageUrl: "/image/gymfit/gymfit_launch_announcement.png",
            type: "IMAGE_POPUP",
            action: {
                actionType: "NAVIGATION",
                url: "curefit://tabpage?pageId=gymfitclp"
            },
            layout: {
                width: 347,
                height: 570
            },
            dismissAction
        }
    }

    private async buildCultSocialAnnouncementView(announcement: Announcement): Promise<ImageCarouselAnnouncementView> {
        const dismissAction: Action = {
            actionType: "REST_API",
            iconUrl: "/image/icons/cult/cancel-modal.png",
            meta: {
                method: "POST",
                url: `/user/announcement/${announcement.announcementId}`,
                body: { "state": "DISMISSED" }
            }
        }
        return {
            type: "IMAGE_CAROUSEL_POPUP",
            action: {
                title: "VIEW MY PROFILE",
                actionType: "NAVIGATION",
                url: (process.env.ENVIRONMENT === "STAGE" || process.env.ENVIRONMENT === "LOCAL") ? "curefit://tabpage?pageId=cult&widgetId=9be7f6b7-7a61-4474-8522-938cc063bceb" : "curefit://tabpage?pageId=cult&widgetId=10c5a31e-de18-4d59-9947-8b9ed94dfcab"
            },
            announcementData: [{ url: "/image/icons/cult/social-announcement-1-v3.png" }, { url: "/image/icons/cult/social-announcement-2-v4.png" }, { url: "/image/icons/cult/social-announcement-3-v2.png" }],
            dismissAction,
            analyticsData: {
                announcement_id: announcement.announcementId
            }
        }
    }

    private async buildEatCoronaAnnouncementView(userContext: UserContext, announcement: Announcement): Promise<ImageCarouselAnnouncementView> {
        const dismissAction: Action = {
            actionType: "REST_API",
            iconUrl: "/image/icons/cult/transparent.png",
            meta: {
                method: "POST",
                url: `/user/announcement/${announcement.announcementId}`,
                body: { "state": "DISMISSED" }
            }
        }
        const announcementData = [{ url: "/image/icons/eat/corona_1_v2.png" }, { url: "/image/icons/eat/corona_2.png" }, { url: "/image/icons/eat/corona_3.png" }, { url: "/image/icons/eat/corona_4.png" }]
        return {
            type: "IMAGE_CAROUSEL_POPUP",
            action: {
                title: "GOT IT",
                actionType: "NAVIGATION",
            },
            announcementData,
            dismissAction,
            analyticsData: {
                announcement_id: announcement.announcementId
            }
        }
    }

    private async buildNutritionistAnnouncementView(userContext: UserContext, announcement: Announcement): Promise<ImageCarouselAnnouncementView> {
        const dismissAction: Action = {
            actionType: "REST_API",
            iconUrl: "/image/icons/cult/cancel-modal.png",
            meta: {
                method: "POST",
                url: `/user/announcement/${announcement.announcementId}`,
                body: { "state": "DISMISSED" }
            }
        }
        const announcementData = [{ url: "/image/icons/eat/nutritionist_consultation.jpg" }]
        return {
            type: "IMAGE_CAROUSEL_POPUP",
            action: {
                title: "GET STARTED",
                actionType: "NAVIGATION",
                url: "curefit://listpage?pageId=clplc"
            },
            announcementData,
            dismissAction,
            analyticsData: {
                announcement_id: announcement.announcementId
            }
        }
    }

    private async buildCareTCAnnouncementView(userContext: UserContext, announcement: Announcement): Promise<ImageCarouselAnnouncementView> {
        const dismissAction: Action = {
            actionType: "REST_API",
            iconUrl: "/image/icons/cult/transparent.png",
            meta: {
                method: "POST",
                url: `/user/announcement/${announcement.announcementId}`,
                body: { "state": "DISMISSED" }
            }
        }
        const announcementData = [{ url: "/image/care_consult_1.png" }, { url: "/image/care_consult_2.png" }, { url: "/image/care_consult_3.png" }]
        return {
            type: "IMAGE_CAROUSEL_POPUP",
            action: {
                title: "GET STARTED",
                actionType: "NAVIGATION",
                url: "curefit://tabpage?pageId=careclptab&selectedTab=clpconsultation"
            },
            announcementData,
            dismissAction,
            analyticsData: {
                announcement_id: announcement.announcementId
            }
        }
    }

    private async buildCultCoronaAnnouncementView(userContext: UserContext, announcement: Announcement): Promise<ImageCarouselAnnouncementView> {
        const dismissAction: Action = {
            actionType: "REST_API",
            iconUrl: "/image/icons/cult/transparent.png",
            meta: {
                method: "POST",
                url: `/user/announcement/${announcement.announcementId}`,
                body: { "state": "DISMISSED" }
            }
        }
        const announcementData = [{ url: "/image/icons/cult/corona_1_v2.png" }, { url: "/image/icons/cult/corona_2.png" }, { url: "/image/icons/cult/corona_3_v2.png" }, { url: "/image/icons/cult/corona_4.png" }]
        if (userContext.userProfile.city.countryId === "IN") {
            announcementData.push({
                url: "/image/icons/cult/corona_5_v3.png"
            })
        }
        return {
            type: "IMAGE_CAROUSEL_POPUP",
            action: {
                title: "GOT IT",
                actionType: "NAVIGATION",
            },
            announcementData,
            dismissAction,
            analyticsData: {
                announcement_id: announcement.announcementId
            }
        }
    }


    private async buildLivePTAnnouncementView(announcement: Announcement): Promise<ImageCarouselAnnouncementView> {
        const dismissAction: Action = {
            actionType: "REST_API",
            iconUrl: "/image/icons/cult/cancel-modal.png",
            meta: {
                method: "POST",
                url: `/user/announcement/${announcement.announcementId}`,
                body: { "state": "DISMISSED" }
            }
        }
        return {
            type: "IMAGE_CAROUSEL_POPUP",
            action: {
                title: "GET STARTED",
                actionType: "NAVIGATION",
                url: "curefit://tabpage?pageId=cult&selectedTab=LivePT"
            },
            announcementData: [{ url: "/image/icons/cult/live_pt_1.png" }, { url: "/image/icons/cult/live_pt_3.png" }, { url: "/image/icons/cult/live_pt_2.png" }],
            dismissAction,
            analyticsData: {
                announcement_id: announcement.announcementId
            }
        }
    }

    public buildLivePTTncAnnouncementView(userContext: UserContext, announcement: Announcement, nextAction: Action) {
        const dismissAction: Action = {
            actionType: "REST_API",
            title: "Got it",
            meta: {
                method: "POST",
                url: `/user/announcement/${announcement.announcementId}`,
                body: { "state": "DISMISSED" }
            }
        }
        return {
            type: "POLICY_CHANGE",
            url: "https://s3.ap-south-1.amazonaws.com/vm-html-pages/live-pt-tnc.html",
            ackMessage: "I have read and acknowledge the policy terms",
            acceptAction: {
                ...nextAction,
                productType: "LIVE_PERSONAL_TRAINING"
            },
            dismissAction,
            analyticsData: {
                announcement_id: announcement.announcementId
            }
        }
    }
}