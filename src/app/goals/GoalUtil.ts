import * as _ from "lodash"
import { GoalAdherenceStatusType, GoalMetricTarget } from "@curefit/gmf-common"
import { IMetric } from "@curefit/metrics-common"

export class GoalUtil {
    static mapMetricValueToDisplayLabel(rangeBuckets: any[], value: number): string {
        if (_.isEmpty(rangeBuckets)) {
            return `${value}`
        }
        const range = _.find(rangeBuckets, bucket => value >= bucket.rangeMin && value <= bucket.rangeMax)
        return !_.isEmpty(range) ? _.capitalize(range.displayLabel) : undefined
    }

    static findRangeLabelForMetric(rangeBuckets: any[], rangeMin: number, rangeMax: number): string {
        if (_.isEmpty(rangeBuckets)) {
            return `${rangeMin} - ${rangeMax}`
        }
        const range = _.find(rangeBuckets, bucket => bucket.rangeMin >= rangeMin && bucket.rangeMax <= rangeMax)
        return !_.isEmpty(range) ? _.capitalize(range.displayLabel) : undefined
    }

    static getGoalTargetDisplayValue(targetMetric: GoalMetricTarget, metricDetail: IMetric): string {
        const goalTarget = targetMetric.idealValue
        const targetDisplayValue = !_.isEmpty(metricDetail.rangeBuckets) ? this.mapMetricValueToDisplayLabel(metricDetail.rangeBuckets, goalTarget.value) : `${_.round(goalTarget.value, 2)}`
        return goalTarget.range && goalTarget.range.min && goalTarget.range.max ? this.findRangeLabelForMetric(metricDetail.rangeBuckets, goalTarget.range.min, goalTarget.range.max) : targetDisplayValue
    }
    static getGoalGradientColor(adherenceStatus: GoalAdherenceStatusType): string[] {
        if (adherenceStatus === "Great") {
            return ["#48c9b3", "#01b4bf"]
        } else if (adherenceStatus === "Average") {
            return ["#f29153", "#ff9635"]
        } else if (adherenceStatus === "Too Early to Calculate") {
            return ["#bbbbbb", "#929292"]
        }
        else if (adherenceStatus === "Poor") {
            return ["#f9707d", "#e56060"]
        }
        else {
            return ["#f29153", "#ff9635"]
        }
    }
}
