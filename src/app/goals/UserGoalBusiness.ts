import { inject, injectable } from "inversify"
import * as _ from "lodash"
import * as momentTz from "moment-timezone"
import { Logger, BASE_TYPES } from "@curefit/base"
import { IGMFClient as IGoalManagementService, GMF_CLIENT_TYPES } from "@curefit/gmf-client"
import { WidgetView } from "../common/views/WidgetView"
import { AdherenceMetricRequest, UserGoalDetail, UserGoalsSummary } from "@curefit/gmf-common"
import { GoalDetailWidget } from "../common/views/GoalDetailWidget"
import { GoalMetricsGraphWidget, IGoalMetricsWidgetBuilderParams } from "../common/views/GoalMetricsGraphWidget"
import { IMetricServiceClient as IMetricService, METRIC_TYPES } from "@curefit/metrics"
import { AggregationRange, IMetricOptions } from "@curefit/metrics-common"
import {
    GoalAdherenceGraphWidget,
    IAdherenceMetricsWidgetBuilderParams
} from "../common/views/GoalAdherenceGraphWidget"
import { AdherenceAggregationLevel } from "@curefit/gmf-common"
import { TimeUtil } from "@curefit/util-common"
import { UserContext } from "@curefit/userinfo-common"
import { IHealthfaceService, ALBUS_CLIENT_TYPES } from "@curefit/albus-client"
import { eternalPromise } from "@curefit/util-common"

export interface IUserGoalBusiness {
  getUserGoalDetailView(
    userId: string,
    goalId: string,
    userContext: UserContext
  ): Promise<UserGoalDetailView>
  getUserGoalMetricsGraphView(
    userId: string,
    queryParams: any,
    userContext: UserContext
  ): Promise<GoalMetricsGraphWidget>
  getUserGoalAdherenceGraphView(
    userContext: UserContext,
    userId: string,
    queryParams: any
  ): Promise<GoalAdherenceGraphWidget>
}

export enum ADHERENCE_STATUS {
  EARLY = "Too Early to Calculate",
  GREAT = "Great",
  AVERAGE = "Average",
  POOR = "Poor"
}

export interface UserGoalDetailView {
  widgets: WidgetView[]
}

@injectable()
export class UserGoalBusiness implements IUserGoalBusiness {
  constructor(
    @inject(BASE_TYPES.ILogger) private logger: Logger,
    @inject(GMF_CLIENT_TYPES.IGMFClient) private gmsService: IGoalManagementService,
    @inject(METRIC_TYPES.MetricServiceClient) private metricService: IMetricService,
    @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
  ) { }

  public async getUserGoalDetailView(
    userId: string,
    goalId: string,
    userContext: UserContext
  ): Promise<UserGoalDetailView> {
    const userGoalResponse: UserGoalsSummary = await this.gmsService.getUserGoalDetails(
      userId,
      goalId,
      false,
      true
    )
    const userGoalDetail = userGoalResponse.userGoalDetail[0]
    const targetMetric = _.maxBy(userGoalDetail.userGoal.goalMetricTargets, metricTarget => metricTarget.priority)
    const fromDate = momentTz.tz(userContext.userProfile.timezone)
      .startOf("week")
      .format("YYYY-MM-DD")
    const toDate = momentTz.tz(userContext.userProfile.timezone)
      .startOf("week")
      .add(6, "day")
      .format("YYYY-MM-DD")
    const metricGraphParans = {
      metricId: targetMetric.metricId,
      graphType: AggregationRange.WEEK,
      fromDate,
      toDate
    }
    const adherenceGraphParams = {
      graphType: AggregationRange.WEEK,
      goalId: goalId,
      adherenceCategory: userGoalDetail.goalAdherenceStatus.allCategories[0],
      fromDate,
      toDate,
      adherenceStatus: userGoalDetail.goalAdherenceStatus.overAllStatus
    }
    const userGoalDetailView: UserGoalDetailView = {
      widgets: []
    }
    const goalWidgetPromises: Promise<WidgetView>[] = []
    goalWidgetPromises.push(this.buildGoalDetailWidget(userContext, userId, userGoalDetail, targetMetric.metricId))
    goalWidgetPromises.push(this.buildGoalAdherenceGraphWidget(userContext, userId, adherenceGraphParams))
    goalWidgetPromises.push(this.buildGoalMetricsGraphWidget(userId, userGoalDetail, metricGraphParans, userContext))

    userGoalDetailView.widgets = await Promise.all(goalWidgetPromises)
    return userGoalDetailView
  }

  public async getUserGoalMetricsGraphView(
    userId: string,
    queryParams: any,
    userContext: UserContext
  ): Promise<GoalMetricsGraphWidget> {
    const userGoalResponse: UserGoalsSummary = await this.gmsService.getUserGoalDetails(
      userId,
      queryParams.goalId
    )
    return this.buildGoalMetricsGraphWidget(
      userId,
      userGoalResponse.userGoalDetail[0],
      queryParams,
      userContext
    )
  }

  public async getUserGoalAdherenceGraphView(
    userContext: UserContext,
    userId: string,
    queryParams: any
  ): Promise<GoalAdherenceGraphWidget> {
    return this.buildGoalAdherenceGraphWidget(userContext, userId, queryParams)
  }

  private async buildGoalDetailWidget(userContext: UserContext, userId: string, userGoalDetail: UserGoalDetail, metricId: number): Promise<GoalDetailWidget> {
    const metricDetailPromise = this.metricService.getMetricById(metricId)
    const activePackBookingsPromise = eternalPromise(this.healthfaceService.getActivePacksByBookingType(userId, undefined, "CARE", "BUNDLE", "MP"))
    return new GoalDetailWidget(userContext, userGoalDetail, await metricDetailPromise, (await activePackBookingsPromise).obj)
  }

  private async buildGoalMetricsGraphWidget(
    userId: string,
    userGoalDetail: UserGoalDetail,
    queryParams: any,
    userContext: UserContext
  ): Promise<GoalMetricsGraphWidget> {
    const options: IMetricOptions = {
      userId: userId,
      metricId: queryParams.metricId,
      aggregationRange: queryParams.graphType === AggregationRange.SEMI_ANNUAL ? "YEAR" : queryParams.graphType,
      fromDate: queryParams.fromDate,
      toDate: queryParams.toDate
    }
    const metricsDataPromise = this.metricService.getAverageMetricsForCategoryForRange(
      options
    )
    const latestMetricsPromise = this.metricService.getLatestMetricValueFor(
      userId,
      queryParams.metricId
    )
    const widgetBuilderParams: IGoalMetricsWidgetBuilderParams = {
      userGoal: userGoalDetail.userGoal,
      userContext: userContext,
      metricId: Number(queryParams.metricId),
      fromDate: queryParams.fromDate,
      toDate: queryParams.toDate,
      graphType: queryParams.graphType
    }
    return new GoalMetricsGraphWidget(
      await metricsDataPromise,
      await latestMetricsPromise,
      widgetBuilderParams
    )
  }

  private async buildGoalAdherenceGraphWidget(
    userContext: UserContext,
    userId: string,
    queryParams: any
  ): Promise<GoalAdherenceGraphWidget> {
    const tz = userContext.userProfile.timezone
    const adherenceMetricOptions: AdherenceMetricRequest = {
      aggregateLevel: this.mapToAdherenceAggregationRange(queryParams.graphType),
      startEpoch: TimeUtil.getEpochInMillis(tz, queryParams.fromDate),
      endEpoch: TimeUtil.getEpochInMillis(tz, queryParams.toDate, "day", false)
    }
    const adherenceMetrics = await this.gmsService.getAdherenceMetricsForCategory(
      userId,
      queryParams.goalId,
      queryParams.adherenceCategory,
      adherenceMetricOptions
    )
    const widgetBuilderParams: IAdherenceMetricsWidgetBuilderParams = {
      goalAdherenceMetrics: adherenceMetrics,
      adherenceCategory: queryParams.adherenceCategory,
      graphType: queryParams.graphType,
      fromDate: queryParams.fromDate,
      toDate: queryParams.toDate,
      adherenceStatus: queryParams.adherenceStatus
    }
    return new GoalAdherenceGraphWidget(userContext, widgetBuilderParams)
  }

  private mapToAdherenceAggregationRange(
    graphType: AggregationRange
  ): AdherenceAggregationLevel {
    switch (graphType) {
      case AggregationRange.MONTH:
        return "WEEK"
      case AggregationRange.WEEK:
        return "DAY"
      case AggregationRange.YEAR:
      case AggregationRange.SEMI_ANNUAL:
        return "MONTH"
    }
  }
}
