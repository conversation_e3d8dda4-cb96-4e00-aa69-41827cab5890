import { Container, inject } from "inversify"
import { controller, httpGet } from "inversify-express-utils"
import AuthMiddleware from "../auth/AuthMiddleware"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CULT_CLIENT_TYPES } from "@curefit/cult-client"
import { ICultServiceOld as ICultService } from "@curefit/cult-client/dist/src/ICultServiceOld"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { CATALOG_CLIENT_TYPES, ICatalogueService, ICatalogueServicePMS } from "@curefit/catalog-client"
import { ErrorFactory } from "@curefit/error-client"
import WidgetBuilder from "../page/vm/WidgetBuilder"
import * as express from "express"
import { ActionPageWidgetView, WidgetView } from "../common/views/WidgetView"
import { UserContext } from "@curefit/vm-models"
import FitnessTransferMembershipViewBuilder from "./FitnessTransferMembershipViewBuilder"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import { GymfitAccessLevel, GymfitListingType, GymfitProductType, GymfitStatus } from "@curefit/gymfit-common"
import { CENTER_SERVICE_CLIENT_TYPES, ICenterService } from "@curefit/center-service-client"
import { CenterServiceSKUs } from "../util/CultUtil"
import { CenterStatus } from "@curefit/center-service-common"
import { CenterDetails } from "../cult/CultBusiness"
import _ = require("lodash")
import { GYMFIT_CLIENT_TYPES, IGymfitService } from "@curefit/gymfit-client"
import { AccessLevel, CultPackType } from "@curefit/cult-common"
import { Namespace, ProductSubType } from "@curefit/pack-management-service-common"
import CatalogueServiceUtilities from "../util/CatalogueServiceUtilities"
import CenterViewV3 from "../cult/CenterViewV3"
import { IOfflineFitnessPackService, PACK_CLIENT_TYPES } from "@curefit/pack-management-service-client"

export function fitnessControllerFactory(kernel: Container) {
    @controller("/fitness",
        kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class FitnessController {
        constructor(
            @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
            @inject(LOCATION_TYPES.CityService) private cityService: ICityService,
            @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
            @inject(CATALOG_CLIENT_TYPES.CatalogServiceReaderPMS) private catalogueServicePMS: ICatalogueServicePMS,
            @inject(PACK_CLIENT_TYPES.OfflineFitnessPackService) private offlineFitnessPackService: IOfflineFitnessPackService,
            @inject(CENTER_SERVICE_CLIENT_TYPES.CenterService) private centerService: ICenterService,
            @inject(CUREFIT_API_TYPES.FitnessTransferMembershipViewBuilder) private selectTransferMembershipViewBuilder: FitnessTransferMembershipViewBuilder,
            @inject(CUREFIT_API_TYPES.WidgetBuilder) public widgetBuilder: WidgetBuilder,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
            @inject(CULT_CLIENT_TYPES.MindFitService) private mindFitService: ICultService,
            @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) public membershipService: IMembershipService,
            @inject(GYMFIT_CLIENT_TYPES.GymfitService) private gymfitService: IGymfitService,
        ) {
        }

        @httpGet("/transfermembership")
        async getTransferMembership(req: express.Request): Promise<{ widgets: WidgetView[] }> {
            const membershipServiceId = req.query.membershipServiceId
            const userContext: UserContext = req.userContext as UserContext
            const originalMembershipData = await this.membershipService.getMembershipById(Number(membershipServiceId))
            const product = await this.catalogueServicePMS.getProduct(originalMembershipData.productId)
            return this.selectTransferMembershipViewBuilder.buildView(membershipServiceId, originalMembershipData, product, userContext)
        }

        @httpGet("/transfermembership/details")
        async transferPackToOtherUser(req: express.Request): Promise<ActionPageWidgetView> {
            const { membershipServiceId, productId, selectedCenterId, cityId } = req.query // selectedCenterId is centerServiceId
            const userId = req.session.userId
            const userContext: UserContext = req.userContext as UserContext
            const membershipData = await this.membershipService.getMembershipById(Number(membershipServiceId))
            return this.selectTransferMembershipViewBuilder.transferDetails(membershipServiceId, membershipData, productId, selectedCenterId, cityId, userId, userContext)
        }

        @httpGet("/centers") // Only to fetch the centers for the select membership transfer
        async getFitnessCenters(req: express.Request): Promise<CenterDetails> {
            const cityId: string = req.query.cityId
            const productId = req.query.productId
            const flow = req.query.flow
            const product = await this.catalogueServicePMS.getProduct(productId)
            const userContext: UserContext = req.userContext as UserContext
            let centerServiceCenters = await this.centerService.getCentersBySkus([CenterServiceSKUs.CULTPASS_BLACK, CenterServiceSKUs.CULTPASS_GOLD], [CenterStatus.ACTIVE], cityId)

            const centerLevelTransferPacks = await this.offlineFitnessPackService.searchCachedPacks({
                namespace: Namespace.OFFLINE_FITNESS,
                productTypes: ["FITNESS", "GYMFIT_FITNESS_PRODUCT"],
                productSubType: ProductSubType.TRANSFER,
                status: "ACTIVE",
            }).then(packs => packs.filter(pack => CatalogueServiceUtilities.getAccessLevel(pack) === "CENTER"))

            const eligibleTransferCenters = centerLevelTransferPacks.map(pack => CatalogueServiceUtilities.getExternalAccessLevelId(pack))

            centerServiceCenters = _.filter(centerServiceCenters, (center) => {
                return eligibleTransferCenters.includes(String(center.id))
                    && !center.meta?.isVirtualCenter
                    && !(center.category === "HYBRID" && center.vertical === "GYMFIT")
                    && center.id !== product?.restrictions?.centers?.[0]
            })

            const centerServiceBlackCenters: CenterViewV3[] = _.map(centerServiceCenters, centerServiceCenter => {
                return new CenterViewV3(userContext, centerServiceCenter, "FITNESS", null, flow)
            })
            return {
                centers: centerServiceBlackCenters,
            }
        }
        @httpGet("/select/cities")
        async getSelectAvailableCities(req: express.Request): Promise<any> {
            return {
                "countries": [
                    {
                        "title": "INDIA",
                        "countryId": "IN",
                        "cities": [
                            {
                                "isSelected": false,
                                "cityId": "Gurgaon",
                                "countryId": "IN",
                                "name": "Delhi NCR",
                                "image": "/image/cities/ncr_new.png",
                                "timezone": "Asia/Kolkata",
                                "lat": 28.556753,
                                "lon": 77.203353,
                                "isPopular": true
                            },
                            {
                                "isSelected": false,
                                "cityId": "Mumbai",
                                "countryId": "IN",
                                "name": "Mumbai",
                                "image": "/image/cities/mumbai_selected.png",
                                "timezone": "Asia/Kolkata",
                                "lat": 19.083444,
                                "lon": 72.879126,
                                "isPopular": true
                            },
                            {
                                "isSelected": false,
                                "cityId": "Hyderabad",
                                "countryId": "IN",
                                "name": "Hyderabad",
                                "image": "/image/cities/hyd_new.png",
                                "timezone": "Asia/Kolkata",
                                "lat": 17.385,
                                "lon": 78.4867,
                                "isPopular": true
                            },
                            {
                                "isSelected": false,
                                "cityId": "Bangalore",
                                "countryId": "IN",
                                "name": "Bangalore",
                                "image": "/image/cities/blr_new.png",
                                "timezone": "Asia/Kolkata",
                                "lat": 12.9081,
                                "lon": 77.6476,
                                "isPopular": true
                            }
                        ]
                    }
                ],
                "cityTextInfo": {
                    "popularCityText": "Available cities for select membership transfer",
                    "otherCityText": "OTHERS"
                }
            }
        }

        @httpGet("/skuplus/cities")
        async getSkuPlusAvailableCities(req: express.Request): Promise<any> {
            return {
                "countries": [
                    {
                        "title": "INDIA",
                        "countryId": "IN",
                        "cities": [
                            {
                                "isSelected": false,
                                "cityId": "Gurgaon",
                                "countryId": "IN",
                                "name": "Delhi NCR",
                                "image": "/image/cities/ncr_new.png",
                                "timezone": "Asia/Kolkata",
                                "lat": 28.556753,
                                "lon": 77.203353,
                                "isPopular": true
                            },
                            {
                                "isSelected": false,
                                "cityId": "Hyderabad",
                                "countryId": "IN",
                                "name": "Hyderabad",
                                "image": "/image/cities/hyd_new.png",
                                "timezone": "Asia/Kolkata",
                                "lat": 17.385,
                                "lon": 78.4867,
                                "isPopular": true
                            },
                            {
                                "isSelected": false,
                                "cityId": "Bangalore",
                                "countryId": "IN",
                                "name": "Bangalore",
                                "image": "/image/cities/blr_new.png",
                                "timezone": "Asia/Kolkata",
                                "lat": 12.9081,
                                "lon": 77.6476,
                                "isPopular": true
                            }
                        ]
                    }
                ],
                "cityTextInfo": {
                    "popularCityText": "Available cities for sku plus membership transfer",
                    "otherCityText": "OTHERS"
                }
            }
        }

    }

    return FitnessController
}

export default fitnessControllerFactory