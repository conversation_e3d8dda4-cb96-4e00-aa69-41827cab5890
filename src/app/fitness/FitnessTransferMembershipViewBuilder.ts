import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CULT_CLIENT_TYPES, CultMembership, ICultServiceOld as ICultService } from "@curefit/cult-client"
import {
    ActionPageWidgetView,
    CenterSelectionWidget,
    InfoSeeMoreWidget,
    SelectCenterAction,
    TransferMembershipTypeInfo,
    TransferMembershipWidget,
    WidgetView
} from "../common/views/WidgetView"
import { CacheHelper } from "../util/CacheHelper"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { City } from "@curefit/location-common"
import { CATALOG_CLIENT_TYPES, ICatalogueService, ICatalogueServicePMS } from "@curefit/catalog-client"
import { UserContext } from "@curefit/userinfo-common"
import { Membership } from "@curefit/membership-commons"
import { CenterResponse } from "@curefit/center-service-common"
import { CENTER_SERVICE_CLIENT_TYPES, ICenterService } from "@curefit/center-service-client"
import AppUtil from "../util/AppUtil"
import * as _ from "lodash"
import { BookingSearchRequest } from "@curefit/cult-common/dist/src/Models"
import { TimeUtil } from "@curefit/util-common"
import { BookingState } from "@curefit/cult-common"
import { Action, TextWidget } from "@curefit/apps-common"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { OfflineFitnessPack, ProductSubType } from "@curefit/pack-management-service-common"


@injectable()
class FitnessTransferMembershipViewBuilder {
    constructor(@inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
                @inject(CULT_CLIENT_TYPES.MindFitService) private mindFitService: ICultService,
                @inject(CENTER_SERVICE_CLIENT_TYPES.CenterService) private centerService: ICenterService,
                @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
                @inject(LOCATION_TYPES.CityService) private cityService: ICityService,
                @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) protected catalogueService: ICatalogueService,
                @inject(CATALOG_CLIENT_TYPES.CatalogServiceReaderPMS) protected catalogueServicePMS: ICatalogueServicePMS,
                @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) public membershipService: IMembershipService,
                @inject(BASE_TYPES.ILogger) private logger: ILogger,
                @inject(ERROR_COMMON_TYPES.RollbarService) public rollbarService: RollbarService
    ) {
    }

    async buildView(membershipId: string, membershipData: Membership, product: OfflineFitnessPack, userContext: UserContext): Promise<{
        widgets: WidgetView[]
    }> {
        const widgets: WidgetView[] = []
        widgets.push(await this.getTransferMembershipWidget(membershipId, membershipData, product, userContext))
        widgets.push(this.footerKnowMoreWidget())
        return { widgets }
    }

    async transferDetails(membershipId: string, membershipData: Membership, productId: string, selectedCenterId?: string, cityId?: string, selectedUserId?: string, userContext?: UserContext): Promise<ActionPageWidgetView> {
        const transferMembershipPage: any = {}
        const widgetPromises: Promise<WidgetView>[] = []
        cityId = AppUtil.isAppSelectCityTransferFLowSupported(userContext) ? cityId : membershipData?.metadata?.cityId
        const city = cityId ? await this.cityService.getCityById(cityId) : null
        const selectedCenter = selectedCenterId ? await this.centerService.getCenterById(Number(selectedCenterId)) : null
        widgetPromises.push(this.buildCitySelectionWidget(city, userContext))
        if (city) {
            widgetPromises.push(this.buildCenterInfoWidget(selectedCenter, city.cityId, selectedCenterId, productId))
        }
        transferMembershipPage.widgets = await Promise.all(widgetPromises)
        let title = "Proceed", action: any = { actionType: "TRANSFER_MEMBERSHIP" }, orderProduct: any = {}, meta: any = {
            selectedCityId: city ? city.cityId : null,
            selectedCenterId
        }
        if (!city) {
            title = "Pick a city"
            action = {
                actionType: "SHOW_SELECT_CITY",
                url: "/fitness/select/cities"
            }
            meta = {
                selectedUserId
            }
        }
        else if (!selectedCenterId) {
            title = "Select preferred center"
            action = this.getPreferredCenterAction(city.cityId, productId)
            meta = {
                selectedCityId: city.cityId,
                ...action.meta
            }
        } else {
            const centerSkuMapping = await this.centerService.getCenterSkusByCenterIds([Number(selectedCenterId)])
            const centerSku = centerSkuMapping[0]
            let transferPack, centerId
            if (centerSku.skuId === 1) {
                 transferPack = await this.catalogueServicePMS.getCultPacksWithFilter({
                    centerId: Number(selectedCenterId),
                }).then(packs => packs.find(pack => pack.status === "ACTIVE" && pack.product.productSubType === ProductSubType.TRANSFER))
                centerId = selectedCenter.meta.cultCenterId
            } else if (centerSku.skuId === 2) {
                transferPack = await this.catalogueService.getProCenterTransferPack(selectedCenterId)
                centerId = selectedCenter.meta.gymfitCenterId
            }
            orderProduct = {
                productId: transferPack.productId,
                option: {
                    productId: transferPack.productId,
                    membershipId,
                    isTransferringMembership: true,
                    centerId: centerId,
                    centerServiceCenterId: selectedCenterId,
                    ...meta
                }
            }
        }
        transferMembershipPage.actions = [{
            title: title,
            ...action,
            meta: meta,
            orderProduct
        }]
        return transferMembershipPage
    }

    private async getTransferMembershipWidget(membershipId: string, membershipData: Membership, product: OfflineFitnessPack, userContext: UserContext): Promise<TransferMembershipWidget> {
        let originalMembership: Membership
        let alertWidgetAction: Action = null
        if (product.productType == "FITNESS") {
            originalMembership = await this.membershipService.getMembershipById(Number(membershipId))
            const isUserHavingFutureBookings: boolean = await AppUtil.isUserHavingActiveBooking(originalMembership.id, userContext.userProfile.userId, TimeUtil.epochToZonedTime("UTC", originalMembership.end), this.cultFitService, this.rollbarService)
            if (isUserHavingFutureBookings) {
                alertWidgetAction = {
                    actionType: "SHOW_ALERT_MODAL",
                    title: "Transfer Membership",
                    meta: {
                        title: "Transfer Membership",
                        subTitle: "All of your existing class bookings will be cancelled automatically once you transfer the pack to another city",
                    }
                }
            }
        }
        const transferDetailAction: Action = {
            title: "SELECT",
            actionType: "NAVIGATION",
            url: `curefit://enterTransferDetails?membershipServiceId=${membershipId}&isSelectMembership=true&productId=${membershipData.productId}`
        }
        let transferAction: Action = null
        if (!_.isNil(alertWidgetAction) && alertWidgetAction != null) {
            transferDetailAction.title = "Ok"
            alertWidgetAction.meta.actions = [transferDetailAction]
            transferAction = alertWidgetAction
        } else {
            transferAction = transferDetailAction
        }
        const transferMembershipType: TransferMembershipTypeInfo[] = [
            {
                title: "Another Center",
                description: "Remaining pack days may be changed based on the center selected",
                packTransferType: "ANOTHER_CENTER",
                action: transferAction
            }
        ]

        return {
            widgetType: "TRANSFER_MEMBERSHIP_WIDGET",
            title: "Transfer my membership to",
            separatorText: "OR",
            hasDividerAbove: true,
            transferMembershipType: transferMembershipType
        }
    }

    private footerKnowMoreWidget(): InfoSeeMoreWidget {
        return {
            widgetType: "INFO_SEE_MORE_WIDGET",
            title: "Want to know more about transfers?",
            seemore: {
                title: "Know more",
                actionType: "NAVIGATION",
                url: "curefit://infopage?contentId=transferMembershipPolicy&pageType=LIST"
            }
        }
    }

    private async buildCitySelectionWidget(selectedCity: City, userContext: UserContext): Promise<CenterSelectionWidget> {
        return {
            widgetType: "CENTER_PICKER_WIDGET",
            header: "Which city are you moving to?",
            title: selectedCity ? selectedCity.name : "Pick a City",
            canChangeCenter: AppUtil.isAppSelectCityTransferFLowSupported(userContext),
            action: {
                actionType: "SHOW_SELECT_CITY",
                url: "/fitness/select/cities"
            },
            showHighlightedText: !selectedCity
        }
    }

    private getPreferredCenterAction(selectedCityId: string, productId: string): SelectCenterAction {
        return {
            title: "Pick a center",
            showHelp: true,
            showFavourite: false,
            actionType: "SELECT_CENTER",
            meta: {
                ageCategory: "ADULT",
                cityId: selectedCityId,
                useCenterServiceId: true,
                showFitnessCenters: true,
                productId,
            },
            productType: "FITNESS",
            queryParams: {
                cityId: selectedCityId
            }
        }
    }

    private async buildCenterInfoWidget(selectedCenter: CenterResponse, selectedCityId: string, selectedCenterId: string, productId: string): Promise<CenterSelectionWidget> {
        const action: SelectCenterAction = this.getPreferredCenterAction(selectedCityId, productId)
        return {
            title: "Select center",
            prefixText: "",
            header: "Which center would the membership be in?",
            canChangeCenter: true,
            preferredCenterId: selectedCenterId ? Number(selectedCenterId) : undefined,
            preferredCenterName: selectedCenter ? selectedCenter.name : undefined,
            widgetType: "CENTER_PICKER_WIDGET",
            action: action,
            showHighlightedText: !selectedCenter
        }
    }
}

export default FitnessTransferMembershipViewBuilder
