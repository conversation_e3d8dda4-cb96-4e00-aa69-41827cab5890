import { IBaseWidget, BaseWidget, <PERSON><PERSON>ard<PERSON>ist, UserContext, CardsDetails, CareWidgetUtil } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../page/vm/ServiceInterfaces"
import * as _ from "lodash"
import { NCCardStyles, NCIconWidgetStyle } from "./NCStyles"
import { INCCartOfferRequest } from "../../util/VMUtil"
import AppUtil from "../../util/AppUtil"
import { OfferV2, PackOffersResponse } from "@curefit/offer-common"
import { HealthfaceTenant, ProductPrice } from "@curefit/product-common"
import { ActionUtil, CareUtil, OfferUtil } from "@curefit/base-utils"
import { BundleSessionSellableProduct, HealthfaceProductInfo } from "@curefit/albus-client"
import { getNCIconFromType, getNCProductDescriptionPageAction } from "../../util/NCUtil"

export class NutritionistPackViewWidget extends Pack<PERSON>ardList {

    async buildView(interfaces?: CFServiceInterfaces, userContext?: UserContext, queryParams?: {[filter: string]: any}): Promise<IBaseWidget> {

        // returning if there are no card entries
        if (_.isEmpty(this.cards)) {
            return undefined
        }

        const healthfaceTenant: HealthfaceTenant = CareUtil.getHealthfaceTenant("NUTRITIONIST")
        const bundleProductIds = !_.isEmpty(this.cards) ? _.map(this.cards, card => card.productid) : undefined

        const bundleProductsPromises: Promise<HealthfaceProductInfo[]>[] = _.map(bundleProductIds, (productId) => {
            return interfaces.healthfaceService.getProductInfoDetails("BUNDLE", { subCategoryCode: "NUTRITIONIST", productCodeCsv: productId }, healthfaceTenant)
        })


        const packOffers: PackOffersResponse = await CareWidgetUtil.getCareProductOffersFromUserContext(
            userContext,
            "BUNDLE",
            bundleProductIds,
            AppUtil.callSourceFromContext(userContext),
            interfaces,
            true
        )
        const healthfaceProducts = await Promise.all(bundleProductsPromises)
        const bundleProduct: BundleSessionSellableProduct[]  = healthfaceProducts.map( (healthfaceProduct: HealthfaceProductInfo[]) => {
            return <BundleSessionSellableProduct>healthfaceProduct[0].baseSellableProduct
        })

        const bundleProductsByProductCode: {[productCode: string]: BundleSessionSellableProduct } = _.keyBy(bundleProduct, (product: BundleSessionSellableProduct) => product.productCode)


        if (!_.isEmpty(this.tagIcons) && _.isArray(this.tagIcons)) {
            for (let i = 0; i < this.tagIcons.length; i = i + 1) {
                let icon = this.tagIcons[i]
                icon = getNCIconFromType(icon.icon)
                if (_.isNil(icon)) {
                    // if the selected Icon type is not registered, then we dont push it in the widget
                    this.tagIcons[i] = undefined
                    continue
                }
                this.tagIcons[i] = { ...this.tagIcons[i], ...icon}
            }
        }


        if (!_.isEmpty(this.cards) && _.isArray(this.cards)) {
            for (let i = 0; i < this.cards.length; i = i + 1) {
                let cardDetails = this.cards[i]
                const productId = cardDetails.productid
                const slugValue = cardDetails.slugValue
                const style = NCCardStyles[ i % 2 ]
                const product = bundleProductsByProductCode[productId]
                const desktopImageUrl = cardDetails.desktopImageUrl
                const backGroundImageUrl = cardDetails.backGroundImageUrl

                if (_.isNil(product)) {
                    // invalid productId case
                    this.cards[i] = undefined
                    continue
                }

                const productOffer = OfferUtil.getPackOfferAndPriceForCare(product, packOffers)
                const price: ProductPrice = this.getBundlePrice(product.listingPrice, product.listingPrice, productOffer)
                const cardOfferText = this.getOffer(_.get(productOffer, "offers", undefined))
                cardDetails = this.fetchOverriddenCardDetails(cardDetails, product, style)

                const card: CardsDetails = {
                    ...cardDetails,
                    desktopImageUrl,
                    backGroundImageUrl,
                    offerText: cardOfferText,
                    action: cardDetails?.isMultiProductPackView && !AppUtil.isWeb(userContext) ? { actionType: "NAVIGATION", url: ActionUtil.carefitbundle(product.productCode, product.subCategoryCode), title: "VIEW" } :  getNCProductDescriptionPageAction(productId, userContext, slugValue, product?.subCategoryCode),
                    price: price
                }
                this.cards[i] = card

            }
        }

        // limiting to 2 cards
        _.compact(this.cards.slice(0, 2).sort((a: CardsDetails, b: CardsDetails) => {
            return a.priority - b.priority
        }))

        this.tagIcons = _.compact(this.tagIcons)
        this.cards = _.compact(this.cards)
        // can be used to enforce a horizontal flatlist structure if number of cards is more than 2
        this.isHorizontal = undefined
        // used to tell cleitn what will be the number of cols, this and isHorizontal cannot be clubbed
        this.numberOfCol = 2

        return this

    }

    private getBundlePrice(listingPrice: number, mrp: number, productOffer: {price: ProductPrice, offers: OfferV2[]}): ProductPrice {
        let finalProductPrice: ProductPrice
        finalProductPrice = {
            listingPrice,
            mrp,
            showPriceCut: listingPrice !== mrp,
            currency: "INR"
        }
        if (!_.isNil(productOffer)) {
            const offerPrice = productOffer.price
            finalProductPrice = {
                ...finalProductPrice,
                listingPrice: offerPrice.listingPrice,
                mrp: offerPrice.mrp,
                showPriceCut: offerPrice.listingPrice !== offerPrice.mrp,
            }
        }

        return finalProductPrice
    }

    private fetchOverriddenCardDetails(cardDetails: CardsDetails, productInfo: BundleSessionSellableProduct, style:
        {
            cardColor?: string[],
            locations?: number[],
            bannerColor?: string,
            titleColor?: string
        }): CardsDetails {
        const finalCardDetails: CardsDetails = {
            title: cardDetails.title || productInfo.productName || undefined,
            subtitle: cardDetails.subtitle,
            bannerColor: cardDetails.bannerColor || style.bannerColor,
            titleColor: cardDetails.titleColor || style.titleColor,
            locations: cardDetails.locations || style.locations,
            cardColor: cardDetails.cardColor || style.cardColor,
            isMultiProductPackView: cardDetails?.isMultiProductPackView
        }

        return finalCardDetails
    }

    private getOffer(offers: OfferV2[]) {
        if (offers && !_.isEmpty(offers)) {
            const packOffer = offers.find((offer) => {
                return !_.isNil(offer?.uiLabels?.cartLabel) && !_.isEmpty(offer?.uiLabels?.cartLabel)
            })
            if (packOffer?.uiLabels?.cartLabel) {
                return packOffer.uiLabels.cartLabel
            }
        }
        return undefined
    }
}