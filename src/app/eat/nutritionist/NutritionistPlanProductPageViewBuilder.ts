import { injectable, inject } from "inversify"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { OFFER_SERVICE_CLIENT_TYPES, IOfferServiceV2, PackOffersResponse } from "@curefit/offer-service-client"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import ICRMIssueService from "../../crm/ICRMIssueService"
import {
  GMF_CLIENT_TYPES, IGMFClient as IGoalManagementService
} from "@curefit/gmf-client"
import { ALBUS_CLIENT_TYPES, IHealthfaceService, SUB_CATEGORY_CODE, BundleFilterType, BundleSessionSellableProduct, CLPPackItem, HealthfaceProductInfo } from "@curefit/albus-client"
import IssueBusiness from "../../crm/IssueBusiness"
import TherapyPageConfig from "../../therapy/TherapyPageConfig"
import { <PERSON><PERSON><PERSON>elper } from "../../util/CacheHelper"
import { CFS_TYPES } from "@curefit/cfs-client"
import { CULT_CLIENT_TYPES, ICultService } from "@curefit/cult-client"
import { ICFSClient as IFormService } from "@curefit/cfs-client"
import { UserContext, Orientation, TimerWidgetV2, CareWidgetUtil } from "@curefit/vm-models"
import { MembershipPaymentType } from "@curefit/order-common"
import { HealthfaceTenant, DiagnosticProductResponse, ConsultationProduct, Patient } from "@curefit/care-common"
import { OfferUtil, RUPEE_SYMBOL } from "@curefit/base-utils"
import { CareUtil, IProductSummaryParams } from "../../../app/util/CareUtil"

import _ = require("lodash")
import AppUtil from "../../util/AppUtil"
import { ProductDetailPage, ProductPricingWidget, PricingWidgetRecurringValue, WidgetView, ProductSummaryWidgetV2, getOffersWidget } from "../../common/views/WidgetView"
import { UserAgentType } from "@curefit/base-common"
import { MediaAsset, ProductBenefitWidget, ProductBenefit, UserTestimonialView, Action } from "@curefit/apps-common"
import { CFServiceInterfaces } from "../../page/vm/ServiceInterfaces"

import TimerWidgetV2View from "../../page/vm/widgets/TimerWidgetV2View"
import { IBaseWidget } from "@curefit/vm-common"
import { getSingleSessionAboutSection, getProductBenefitWidget, getPreBookingActions } from "../../util/NCUtil"

interface IOfferTimerWidget {
  timerStyle: any
  offerEndDate: any
  timerTitle: string
}

@injectable()
export class NutritionistPlanProductPageViewBuilder {

  constructor(
    @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) protected catalogueService: ICatalogueService,
    @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) private offerService: IOfferServiceV2,
    @inject(CUREFIT_API_TYPES.CRMIssueService) private CRMIssueService: ICRMIssueService,
    @inject(GMF_CLIENT_TYPES.IGMFClient) public gmfService: IGoalManagementService,
    @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
    @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness,
    @inject(CUREFIT_API_TYPES.TherapyPageConfig) private therapyPageConfig: TherapyPageConfig,
    @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
    @inject(CFS_TYPES.ICFSClient) public formService: IFormService,
    @inject(CULT_CLIENT_TYPES.CultFitService) public cultFitService: ICultService,
    @inject(CULT_CLIENT_TYPES.MindFitService) public mindFitService: ICultService,
    @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
) {

}

  async getBeforeBookingBundleSessionsPage(
    userContext: UserContext,
    productId: string,
    subCategoryCode: SUB_CATEGORY_CODE,
    filterType?: BundleFilterType,
    setCode?: string,
    clubCode?: string
  ) {
    const healthfaceTenant: HealthfaceTenant = CareUtil.getHealthfaceTenant(subCategoryCode)
    const patientsListPromise = this.healthfaceService.getAllPatients(userContext.userProfile.userId)

    let bundleProductIds: string[] = []
    if (filterType) {
      const bundleSellableProductResponse = await this.healthfaceService.getBundleSellableProducts(subCategoryCode, healthfaceTenant)
      const filteredBundleType = _.find(bundleSellableProductResponse.bundleTypes, bundleType => bundleType.type === filterType)
      if (!_.isEmpty(filteredBundleType)) {
        bundleProductIds = filteredBundleType.products
      }
    } else {
      const isNutritionistPDPSupported = AppUtil.isNCV3Supported(userContext)
      const products: DiagnosticProductResponse[] = await CareWidgetUtil.getCareProductFromUserContext(userContext, this.serviceInterfaces, "BUNDLE", subCategoryCode, isNutritionistPDPSupported ? setCode : undefined, isNutritionistPDPSupported ? clubCode : undefined)
      bundleProductIds = products.map(product => product.productCode)
    }
    const offerPromise = CareWidgetUtil.getCareProductOffersFromUserContext(
      userContext,
      "BUNDLE",
      bundleProductIds,
      AppUtil.callSourceFromContext(userContext),
      this.serviceInterfaces,
      true
    )
    const bundleProductsPromises = _.map(bundleProductIds, async (productId) => {
      return this.healthfaceService.getProductInfoDetails("BUNDLE", { subCategoryCode: subCategoryCode, productCodeCsv: productId }, healthfaceTenant)
    })
    const healthfaceProducts = await Promise.all(bundleProductsPromises)
    const bundleProducts = _.map(healthfaceProducts, (healthfaceProduct: HealthfaceProductInfo[]) => {
      return <BundleSessionSellableProduct>healthfaceProduct[0].baseSellableProduct
    })

    const selectedProduct = _.find(bundleProducts, product => product.productCode === productId)
    let singleSessionProduct
    if (!selectedProduct) {
      const productDetails = await this.serviceInterfaces.catalogueService.getProducts([productId]) as ConsultationProduct[]
      singleSessionProduct = _.find(productDetails, product => product.productId === productId)
    }
    return new NutritionistPackBeforeBookingPageView().buildView(userContext, this.serviceInterfaces, bundleProducts, selectedProduct, singleSessionProduct, await offerPromise, await patientsListPromise)
  }
}

  class NutritionistPackBeforeBookingPageView extends ProductDetailPage {
    async buildView(
      userContext: UserContext,
      interfaces: CFServiceInterfaces,
      bundleProducts?: BundleSessionSellableProduct[],
      bundleProduct?: BundleSessionSellableProduct,
      singleSessionProduct?: ConsultationProduct,
      bundleoffers?: PackOffersResponse,
      patientsList?: Patient[],
    ) {
      let offerIds = []
      let appliedOffers: any[] = []
      const isNutritionistPDPSupported = AppUtil.isNCV3Supported(userContext)
      const title = "Nutrition/Dietician Consultation"
      let productSummaryWidget: ProductSummaryWidgetV2
      let selectedProductCode

      if (bundleProduct) {
        selectedProductCode = bundleProduct.productCode
        const offerDetails = OfferUtil.getPackOfferAndPriceForCare(bundleProduct, bundleoffers)
        offerIds = _.map(offerDetails.offers, (offer: any) => { return offer.offerId })
        appliedOffers = []
        productSummaryWidget = this.summaryWidget({
          title: isNutritionistPDPSupported ? bundleProduct.productName : title,
          description: this.getHeaderDescription(),
          imageUrl: bundleProduct.heroImageUrl,
          productCode: bundleProduct.productCode
        }, offerIds, userContext.sessionInfo.userAgent)
        this.widgets.push(productSummaryWidget)
      } else if (singleSessionProduct) {
        selectedProductCode = singleSessionProduct.productId
        productSummaryWidget = this.summaryWidget({
          title: title,
          description: this.getHeaderDescription(),
          imageUrl: singleSessionProduct.heroImageUrl,
          productCode: singleSessionProduct.productId
        }, [], userContext.sessionInfo.userAgent)
        this.widgets.push(productSummaryWidget)
      }
      const actionString = bundleProduct ? `curefit://carecartcheckout?productId=${selectedProductCode}&subCategoryCode=${bundleProduct?.subCategoryCode}` : this.getSingleSessionActionUrl(patientsList, CareUtil.careDoctorBrowseAction(userContext, singleSessionProduct).url)
      const productPricingWidget = await this.getProductPricingWidget(bundleProducts, selectedProductCode, userContext, interfaces, bundleoffers, appliedOffers, patientsList)
      this.widgets.push(productPricingWidget)
      this.widgets.push(getProductBenefitWidget())
      if (!_.isEmpty(appliedOffers)) {
        if (!isNutritionistPDPSupported) {
          productSummaryWidget.offerTimerWidget = await this.offerTimerWidget({
            offerEndDate: appliedOffers[0].endDate, timerTitle: "OFFER EXPIRES IN:", timerStyle: {
              background: "#3788ff",
              borderTopRightRadius: 5,
              borderTopLeftRadius: 5,
            }
          }, interfaces, userContext)
        }
        this.widgets.push(getOffersWidget("Offers applied", appliedOffers, userContext.sessionInfo.userAgent))
      }

      let howItWorksItem, whatsInPackItem
      if (bundleProducts.length > 0) {
        bundleProducts[0].infoSection.children.map(infoSection => {
          switch (infoSection.type) {
            case "PACK_STEPS":
              howItWorksItem = infoSection
              break
            case "PACK_CONTENTS_DETAILED":
              whatsInPackItem = infoSection
              break
          }
        })
        this.widgets.push(CareUtil.getHowItWorksWidget(howItWorksItem, userContext.sessionInfo.userAgent))
      }
      this.widgets = this.widgets.filter(Boolean).map(widget => ({ ...widget, hasDividerBelow: false }))
      const numberOfSessions = bundleProduct ? bundleProduct.infoSection.numberOfSessions : 1
      this.actions = getPreBookingActions(userContext, numberOfSessions, !_.isNil(bundleProduct), offerIds, actionString, patientsList)
      return this
    }


  private getPackItemDetailsList(productDetails: ConsultationProduct[], offers: PackOffersResponse, appliedOffers?: any): CLPPackItem[]  {
    const minPricedProduct = _.minBy(productDetails, product => {
      const offerDetails = OfferUtil.getPackOfferAndPrice(product, offers)
      return Math.ceil(offerDetails.price.listingPrice)
    })
    const offerDetails = OfferUtil.getPackOfferAndPrice(minPricedProduct, offers)
    if (appliedOffers) {
      appliedOffers.push(...offerDetails.offers)
    }
    return [{
      title: "1 Session",
      price: offerDetails.price.mrp,
      discountPrice: offerDetails.price.listingPrice,
      hasOfferTag: false,
      currency: offerDetails.price.currency || "INR",
      showPriceCut: offerDetails.price.listingPrice < offerDetails.price.mrp
      }]
    }

  /*
  private getUserTestimonialWidget() {
    const data: UserTestimonialView[] = []
    data.push({
      type: "VIDEO",
      action: {
        actionType: "NAVIGATION",
        url: "curefit://videoplayer?videoUrl=curefit-content/video/popfit/hr_1.mp4&absoluteVideoUrl=http://cdn-media.cure.fit/video/popfit/hr_1.mp4"
      },
      meta: {
        thumbnailUrl: "image/carefit/consultation/psychiatrist-consultation-web-2.png"
      }
    })
    data.push({
      type: "TEXT",
      meta: {
        profileImageUrl: "/image/icons/addActivity/meditation2.png",
        text: "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type",
        rating: 4,
        name: "Shivtosh Kumar"
      }
    })
    const widget = new UserTestimonialWidget(data, "What Our Customers say?")
    return widget
  }
*/
    private async getSingleSessionProduct(userContext: UserContext, interfaces: CFServiceInterfaces, sellableProductId: string, appliedOffers: any, patientsList: Patient[]): Promise<PricingWidgetRecurringValue> {
      const consultationSellableProduct = await interfaces.healthfaceService.getConsultationSellableProducts(userContext.userProfile.cityId, "LC")
      const { products } = consultationSellableProduct.consultationTypes[0]
      const productCodes = products.map(product => product.code)
      const offerPromise = CareWidgetUtil.getCareProductOffersFromUserContext(
        userContext,
        "CONSULTATION",
        productCodes,
        AppUtil.callSourceFromContext(userContext),
        interfaces,
        true
      )
      const productDetails = await interfaces.catalogueService.getProducts(productCodes) as ConsultationProduct[]
      const currency = "INR"
      const offersResponse = await offerPromise
      const packItemDetails: CLPPackItem[] = this.getPackItemDetailsList(productDetails, offersResponse, productDetails[0].productId === sellableProductId ? appliedOffers : undefined)
      const action: Action = {
        actionType: "NAVIGATION",
        meta: {
          sellableProductId: productCodes[0]
        }
      }
      const offerIds = _.map(offersResponse.offers, (offer: any) => { return offer.offerId })
      const url = this.getSingleSessionActionUrl(patientsList, CareUtil.careDoctorBrowseAction(userContext, productDetails[0]).url)
      const infoAction: Action = getPreBookingActions(userContext, 1, true, offerIds, url, patientsList)[0]
      const data = {
        title: `1 Session. Kickstarter`,
        subTitle: "Personalised Plans. Lifestyle changes.",
        action: action,
        description: getSingleSessionAboutSection(),
        priceMeta: ``,
        price: {
          title: packItemDetails[0].title,
          listingPrice: packItemDetails[0].discountPrice,
          discountText: packItemDetails.length > 1 ? packItemDetails[1].title : undefined,
          mrp: packItemDetails[0].price,
          currency,
          showPriceCut: packItemDetails[0].showPriceCut
        },
        selected: productDetails[0].productId === sellableProductId,
        infoAction
      }
      return data
    }

    private async getProductPricingWidget(bundleProducts: BundleSessionSellableProduct[], sellableProductId: string, userContext: UserContext, interfaces: CFServiceInterfaces, bundleoffers?: PackOffersResponse, appliedOffers?: any, patientsList?: Patient[]): Promise<ProductPricingWidget> {
      const recurringSection: PricingWidgetRecurringValue[] = []
      const isNutritionistPDPSupported = AppUtil.isNCV3Supported(userContext)
      if (!isNutritionistPDPSupported && !AppUtil.isWeb(userContext)) {
        recurringSection.push(await this.getSingleSessionProduct(userContext, interfaces, sellableProductId, appliedOffers, patientsList))
      }
      bundleProducts.forEach((product: BundleSessionSellableProduct) => {
        const offerDetails = OfferUtil.getPackOfferAndPriceForCare(product, bundleoffers)
        if (product.productCode === sellableProductId && offerDetails && offerDetails.offers) {
          appliedOffers.push(...offerDetails.offers)
        }
        product.listingPrice = offerDetails.price.listingPrice
        const perSessionPrice = Math.floor(product.listingPrice / product.infoSection.numberOfSessions)
        const offerIds = _.map(offerDetails.offers, (offer: any) => { return offer.offerId })
        const infoAction: Action = getPreBookingActions(userContext, product.infoSection.numberOfSessions, true, offerIds, `curefit://carecartcheckout?productId=${product.productCode}&subCategoryCode=${product.subCategoryCode}`, patientsList)[0]
        recurringSection.push({
          title: _.get(product, "infoSection.sellingTitle") ? product.infoSection.sellingTitle : `${product.infoSection.numberOfSessions} Sessions`,
          subTitle: product.infoSection.headerDescription,
          description: product.infoSection.aboutSection,
          price: {
            mrp: product.mrp,
            listingPrice: product.listingPrice,
            showPriceCut: product.listingPrice < product.mrp,
            currency: offerDetails.price.currency
          },
          priceMeta: `${RUPEE_SYMBOL}${perSessionPrice}/Session`,
          selected: product.productCode === sellableProductId,
          action: {
            actionType: "NAVIGATION",
            meta: {
              sellableProductId: product.productCode
            }
          },
          infoAction
        })
      })
      return {
        widgetType: "PRODUCT_PRICING_WIDGET",
        header: {
          title: isNutritionistPDPSupported ? "Choose" : "Select Plans - Weight, Health, Fitness"
        },
        sections: [{
          type: "RECURRING",
          value: recurringSection
        }],
        style: {
          // backgroundColor: "#f2f4f8"
        }
      }
    }

    private getHeaderDescription() {
      return "Our experts will understand your preferences and lifestyle to develop your personalised plan"
    }

    private async offerTimerWidget(params: IOfferTimerWidget, interfaces: CFServiceInterfaces, userContext: UserContext): Promise<IBaseWidget> {
      const timerWidgetV2 = new TimerWidgetV2View()
      timerWidgetV2.timerStyle = params.timerStyle
      timerWidgetV2.roundedCorners = false
      timerWidgetV2.data = {
          timerEndTimeWithTz: {
              date: params.offerEndDate,
              timezone: userContext.userProfile.timezone
          },
          title: params.timerTitle,
          timerEndTime: undefined,
          privateOfferId: undefined,
        style: undefined,
        action: undefined
      }
      return timerWidgetV2.buildView(interfaces, userContext, undefined)
    }

    private summaryWidget(params: IProductSummaryParams, offerIds?: string[], userAgent?: UserAgentType): ProductSummaryWidgetV2 {
      const imageAssets: MediaAsset[] = []
      imageAssets.push({
        assetType: "IMAGE",
        assetUrl: params.imageUrl
      })
      const summaryWidget: ProductSummaryWidgetV2 = {
        widgetType: "PRODUCT_SUMMARY_WIDGET_V2",
        title: params.title,
        subTitle: params.description,
        assets: imageAssets,
        productId: params.productCode,
        offerIds: offerIds,
        productType: "NUTRITIONIST_CONSULTATION",
      }
      return summaryWidget
    }


    private getSingleSessionActionUrl(patientsList: Patient[], url: string) {
      const selfPatient = !_.isEmpty(patientsList) ? _.find(patientsList, patient => patient.relationship === "Self") : undefined
      if (selfPatient) {
      //  url += `&patientId=${selfPatient.id}`
      }
       url += `&showSearchWidget=true&name=Select%20a%20Nutritionist`
       return url
    }
}
