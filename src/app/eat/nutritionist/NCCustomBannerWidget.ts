import { BaseWidget, UserContext, IBaseWidget } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../page/vm/ServiceInterfaces"
import * as _ from "lodash"
import WidgetBuilder from "../../page/vm/WidgetBuilder"
import kernel from "../../../config/ioc/ioc"
import CUREFIT_API_TYPES from "../../../config/ioc/types"

export class NCCustomBannerWidget extends BaseWidget implements IBaseWidget {
    bannerMap: {[productId: string]: string}
    mapKeyParam?: string
    constructor() {
        super("NC_CUSTOM_BANNER_WIDGET")
    }

    async buildView(interfaces?: CFServiceInterfaces, userContext?: UserContext, queryParams?: {[param: string]: any}): Promise<IBaseWidget[]> {
        const bannerMap = this.bannerMap
        const keyParam = this.mapKeyParam || "productId"
        if (_.isNil(queryParams)) {
            queryParams = {}
        }
        const value = queryParams[keyParam]
        const bannerWidgetId = bannerMap[value]

        if (_.isNil(value) || _.isNil(bannerWidgetId)) {
            interfaces.logger.error(`nc banner widget broke for ${keyParam} and user: ${userContext.userProfile.userId}`)
            return undefined
        }

        const widgetBuilderResponse = await kernel.get<WidgetBuilder>(CUREFIT_API_TYPES.WidgetBuilder).buildWidgets([bannerWidgetId], interfaces, userContext, queryParams, {}, {})
        return _.get(widgetBuilderResponse, "widgets[0]", undefined)
    }
}
