import { controller, httpGet } from "inversify-express-utils"
import { Container, inject } from "inversify"
import AuthMiddleware from "../auth/AuthMiddleware"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { LatLong } from "@curefit/location-common"
import * as express from "express"
import { PreferredLocation, UserContext } from "@curefit/userinfo-common"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import IUserBusiness from "../user/IUserBusiness"
import { OutletsForLocationAndCuisineRequest } from "@curefit/foodway-common"
import { FMRestaurantListBuilder, FMRestaurantListPage } from "./FCPage/FMRestaurantListBuilder"
import CartViewBuilder from "../cart/CartViewBuilder"
import { BASE_TYPES, ILogger } from "@curefit/base"

export function EatMarketplaceControllerFactory(kernel: Container) {

    @controller("/foodmarketplace", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class MarketPlaceController {
        constructor(
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
            @inject(CUREFIT_API_TYPES.UserBusiness) private userBusiness: IUserBusiness,
            @inject(CUREFIT_API_TYPES.FMRestaurantListViewBuilder) private fmRestaurantListViewBuilder: FMRestaurantListBuilder,
            @inject(CUREFIT_API_TYPES.CartViewBuilder) private cartViewBuilder: CartViewBuilder,
            @inject(BASE_TYPES.ILogger) private logger: ILogger
        ) {}

        @httpGet("/list")
        async getRestaurantListing(req: express.Request): Promise<FMRestaurantListPage> {
                const userContext: UserContext = req.userContext as UserContext
                const fcId = req.params.fcId
                const cuisine = req.query.cuisine
                const lat: number = req.query.lat
                const lon: number = req.query.lon
                const preferredLocation = await this.userBusiness.getPreferredLocation(userContext, userContext.userProfile.userId, userContext.sessionInfo.sessionData, lon, lat, undefined, undefined, "FOOD_MARKETPLACE")
                const latLong: LatLong = this.getLatLong(preferredLocation, lat, lon)

                const request: OutletsForLocationAndCuisineRequest = {
                    latitude: latLong.lat,
                    longitude: latLong.long,
                    cuisineOptional: cuisine
                }
                 const response = await this.serviceInterfaces.foodwayService.getOutletsForLocationAndCuisine(request)
                if (response && response.outletsList && response.brandList) {
                    return await this.fmRestaurantListViewBuilder.build(response.outletsList, response.brandList, userContext, latLong)
                }
                else {
                    return undefined
                }
        }

        @httpGet("/status")
        async getFoodMarketplaceOrderStatus(req: express.Request) {
            const foodOrderId = req.query.foodOrderId
            const userContext: UserContext = req.userContext as UserContext

            const status = await this.serviceInterfaces.foodwayService.getUpcomingScreenDetails(foodOrderId)
            return this.cartViewBuilder.getFoodMarketplaceOrderTrackingWidget(status, userContext)
        }

        private getLatLong(preferredLocation: PreferredLocation, lat: number, long: number) {
            const { city, area, defaultArea, address, latLong } = preferredLocation

            return {
                lat: lat || latLong?.lat || address?.latLong[0] || area?.representativeLatLong?.lat || defaultArea?.representativeLatLong?.lat,
                long: long || latLong?.long || address?.latLong[1] || area?.representativeLatLong?.long || defaultArea?.representativeLatLong.long
            } as LatLong
        }
    }
}
