import { IBaseWidget, UserContext, WidgetType } from "@curefit/vm-models"
import { FCMenuCategoryWidget } from "../FCPage/IFCPage"

export interface MktOrderConfirmationContainerWidget {
    widgetType: WidgetType
    widgets: IBaseWidget[]
}

export interface EatMktPlaceOrderConfirmationResponse {
    pollDurationInSecs: number
    mktOrderConfirmationContainerWidget?: MktOrderConfirmationContainerWidget
    orderStatesWidgets: IBaseWidget[]
}

export interface IEatMktPlaceOrderConfirmationPage {

    getOrderInfo(userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<EatMktPlaceOrderConfirmationResponse>

    getOrderStatesInfo(userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<EatMktPlaceOrderConfirmationResponse>

}