import {
    Action,
    IBaseWidget,
    UserContext
} from "@curefit/vm-models"
import { MktPlaceOrderConfirmationInterface, MktPlaceOrderConfirmationStatesInterface } from "@curefit/eat-api-common"
import { CFServiceInterfaces } from "../../page/vm/ServiceInterfaces"
import { OrderDetailsWidget } from "@curefit/vm-models/dist/src/models/widgets/eat/marketplace/OrderDetailsWidget"
import { TrackingDetailsWidget } from "@curefit/vm-models/dist/src/models/widgets/eat/marketplace/TrackingDetailsWidget"
import { OrderConfirmationStatesWidget } from "@curefit/vm-models/dist/src/models/widgets/eat/marketplace/OrderConfirmationStatesWidget"
import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { BASE_TYPES, Logger } from "@curefit/base"
import {
    IEatMktPlaceOrderConfirmationPage,
    EatMktPlaceOrderConfirmationResponse,
} from "./IEatMktPlaceOrderConfirmationPage"
import { OrderSummaryWidget } from "@curefit/vm-models/dist/src/models/widgets/eat/marketplace/OrderSummaryWidget"
import { Action as AppAction } from "@curefit/apps-common"
import * as _ from "lodash"


@injectable()
export class EatMktPlaceOrderConfirmationPageBuilder implements IEatMktPlaceOrderConfirmationPage {

    constructor(@inject(BASE_TYPES.ILogger) private logger: Logger,
                @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
    ) {

    }

    private async getOrderStatesWidgets(orderStates: MktPlaceOrderConfirmationStatesInterface[], userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget[]> {
        const finalWidgetsPromise: Promise<IBaseWidget>[] = []
        for (const state of orderStates) {
            const action: AppAction = {
                actionType: "PHONE_CALL_NAVIGATION",
                meta: {
                    phoneNumber: !_.isNil(state.pilotInfo) ? state.pilotInfo.pilotNumber : undefined,
                }
            }
            finalWidgetsPromise.push(new OrderConfirmationStatesWidget(state, action).buildView(this.serviceInterfaces, userContext, queryParams))
        }
        const finalWidgets = await Promise.all(finalWidgetsPromise)
        return finalWidgets
    }

    public async getOrderInfo(userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<EatMktPlaceOrderConfirmationResponse> {
        const orderId: string = queryParams["orderId"]
        this.logger.info(`Getting order confirmation info for orderId ${orderId}`)
        const mktOrderConfirmationContainerWidgetPromise: Promise<IBaseWidget>[] = []
        try {
            const orderInfo: MktPlaceOrderConfirmationInterface = await this.serviceInterfaces.eatApiClientService.getMarketplaceOrderConfirmationInfo(orderId)
            const action: Action = {
                actionType: "NAVIGATION",
                url: `curefit://myorderdetail?orderId=${orderId}`
            }
            const trackingWidgetPromise = new TrackingDetailsWidget(orderInfo.orderTrackingInfo.orderInfo.locationPins).buildView(this.serviceInterfaces, userContext, queryParams)
            const orderDetailsWidgetPromise = new OrderDetailsWidget(orderId, orderInfo.orderInfo.itemsOrdered, orderInfo.paymentInfo.totalAmount, orderInfo.paymentInfo.currency).buildView(this.serviceInterfaces, userContext, queryParams)
            const orderSummaryWidgetPromise = new OrderSummaryWidget(orderInfo.orderInfo.restaurantName, orderInfo.orderInfo.restaurantImage, orderInfo.orderInfo.productDetails, action).buildView(this.serviceInterfaces, userContext, queryParams)
            mktOrderConfirmationContainerWidgetPromise.push(orderDetailsWidgetPromise)
            mktOrderConfirmationContainerWidgetPromise.push(trackingWidgetPromise)
            mktOrderConfirmationContainerWidgetPromise.push(orderSummaryWidgetPromise)
            const mktOrderConfirmationContainerWidgets = await Promise.all(mktOrderConfirmationContainerWidgetPromise)
            return {
                mktOrderConfirmationContainerWidget: {
                    widgetType: "MKT_CONTAINER_WIDGET",
                    widgets: mktOrderConfirmationContainerWidgets
                },
                orderStatesWidgets: await this.getOrderStatesWidgets(orderInfo.states, userContext, queryParams),
                pollDurationInSecs: orderInfo.pollDurationInMillis
            }
        } catch (err) {
            this.logger.error("Error build Marketplace confirmation page for User ID : " + userContext.userProfile.userId + err.stack)
            throw err
        }
    }

    public async getOrderStatesInfo(userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<EatMktPlaceOrderConfirmationResponse> {
        const orderId: string = queryParams.orderId
        this.logger.info(`Getting order confirmation info for orderId ${orderId}`)
        try {
            const orderInfo: MktPlaceOrderConfirmationInterface = await this.serviceInterfaces.eatApiClientService.getMarketplaceOrderConfirmationInfo(orderId)
            return {
                orderStatesWidgets: await this.getOrderStatesWidgets(orderInfo.states, userContext, queryParams),
                pollDurationInSecs: orderInfo.pollDurationInMillis
            }
        }
        catch (err) {
            this.logger.error("Error build Marketplace confirmation page for User ID : " + userContext.userProfile.userId + err.stack)
            throw err
        }
    }
}

