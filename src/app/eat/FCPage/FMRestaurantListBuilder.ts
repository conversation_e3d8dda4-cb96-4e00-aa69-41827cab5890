import { inject, injectable } from "inversify"
import { CATALOG_CLIENT_TYPES, ICatalogServiceV3Reader, ICategoryService } from "@curefit/catalog-client"
import * as _ from "lodash"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { IBaseWidget, IWidgetBuilder, SoldOutTitleWidget, UserContext } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../page/vm/ServiceInterfaces"
import { IOfferServiceV2, OFFER_SERVICE_CLIENT_TYPES } from "@curefit/offer-service-client"
import { OutletDetails } from "@curefit/foodway-common"
import { RestaurantCardWidget } from "../marketplace/widgets/RestaurantCardWidget"
// import { CarouselCardWidget } from "../marketplace/widgets/CarouselCardWidget" todo
import { BrandWithOutlet } from "@curefit/foodway-common/dist/src/models/appUI/BrandWithOutlet"
import FoodMarketplaceUtil from "../../util/FoodMarketplaceUtil"
import { LatLong } from "@curefit/location-common"
import { FeaturedRestaurantWidget } from "../marketplace/widgets/FeaturedRestaurantWidget"
import AppUtil from "../../util/AppUtil"

export class FMRestaurantListPage  {
    body: (IBaseWidget[] | IBaseWidget)[] = []
    analyticsData?: any
}


@injectable()
export class FMRestaurantListBuilder {
    constructor(
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceV3Reader) private catalogService: ICatalogServiceV3Reader,
        @inject(CATALOG_CLIENT_TYPES.CategoryService) private categoryService: ICategoryService,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private interfaces: CFServiceInterfaces,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) private offerServiceV2: IOfferServiceV2,
        @inject(CUREFIT_API_TYPES.WidgetBuilder) private widgetBuilder: IWidgetBuilder
    ) {
    }
    async build(outlets: OutletDetails[], brandDetail: BrandWithOutlet[], userContext: UserContext, latLong: LatLong): Promise<FMRestaurantListPage> {
        const openOutlets: OutletDetails[] = []
        const closedOutlets: OutletDetails[] = []
        let widgetPromise: Promise<IBaseWidget>[] = []
        const bannerWidgetId = FoodMarketplaceUtil.getOutletListingScreenBannerWidgetId()

        try {
            const bannerWidget = (await this.widgetBuilder.buildWidgets([bannerWidgetId], this.interfaces, userContext, {}, {}, {})).widgets
            widgetPromise.push(Promise.resolve(bannerWidget[0]))
        } catch (e) {
            this.interfaces.logger.error("Error building branding banner for foodmp brand listing screen for user: " + userContext.userProfile.userId)
            widgetPromise = []
        }

        if (_.isEmpty(outlets)) {
            outlets = []
            widgetPromise.push(new SoldOutTitleWidget("NO RESULTS").buildView(this.interfaces, userContext, {}))
        }
        const outletLogoMap = this.createOutletLogoMap(brandDetail)

        for (let i = 0; i < outlets?.length ?? 0; i += 1) {
            outlets[i].backgroundImageUrl = outlets[i].imageUrl
            outlets[i].imageUrl = outletLogoMap[outlets[i].outletId]
            if (outlets[i]?.isOpen) {
                openOutlets.push(outlets[i])
            } else {
                closedOutlets.push(outlets[i])
            }
        }
        const featuredOutletIndex = Math.min(2, openOutlets?.length - 1) // can be dynamic in future based on ratings
        const isFeaturedRestaurantWidgetSupported = AppUtil.isFoodMarketplaceFeatureRestaurantWidgetSupported(userContext)

        openOutlets?.forEach((o, index) => {
            if (isFeaturedRestaurantWidgetSupported && index === featuredOutletIndex) {
                widgetPromise.push(new FeaturedRestaurantWidget(o).buildView(this.interfaces, userContext, {}))
            } else {
                widgetPromise.push(new RestaurantCardWidget(o).buildView())
            }
        })

        // todo widgetPromise.push(new CarouselCardWidget(featuredOutlets, this.interfaces).buildView(this.interfaces, userContext, {}))

        if (!_.isEmpty(closedOutlets)) {
            widgetPromise.push(new SoldOutTitleWidget("CLOSED OUTLETS").buildView(this.interfaces, userContext, {}))
            closedOutlets.forEach(o => {
                widgetPromise.push(new RestaurantCardWidget(o).buildView())
            })
        }

        return {
            body: _.compact(await Promise.all(widgetPromise)),
            analyticsData: {
                pageId: "foodmpoutletlist",
                userId: userContext.userProfile.userId,
                latLong,
                openOutletIds: openOutlets?.map(o => o.outletId),
                closedOutletIds: closedOutlets?.map(c => c.outletId),
                city: userContext?.userProfile?.cityId
            }
        }
    }

    private createOutletLogoMap(brandDetails: BrandWithOutlet[]): {[outletId: string]: string } {

        const map: {[outletId: string]: string } = {}
        brandDetails?.forEach(b => {
            const outletUnderBrand = b.foodOutletIds
            outletUnderBrand.forEach(o => {
                map[o] = b.imageUrl
            })
        })

        return map
    }
}
