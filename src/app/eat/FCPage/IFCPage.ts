import { UserContext } from "@curefit/userinfo-common"
import { ProductPrice } from "@curefit/product-common"
import { Action } from "../../common/views/WidgetView"
import { BaseWidget, IBaseWidget, IServiceInterfaces, WidgetType } from "@curefit/vm-models"
import { MenuType } from "@curefit/eat-common"

export interface FCMealCardRequest {
    title: string
    subTitle: string
    calories: string
    offer?: string
    isVeg: boolean
    stock: number
    price: ProductPrice
    action: Action
    image: string
    mealSlot: {id: MenuType, name: MenuType}
    nutritionTags: string[]
    productListingId: string
    isServiceable: boolean
}

export class FCMealCardWidget {
    widgetType: WidgetType
    title: string
    subTitle: string
    calories: string
    offer?: string
    isVeg: boolean
    stock: number
    price: ProductPrice
    action: Action
    image: string
    nutritionTags: string[]
    mealSlot: {id: MenuType, name: MenuType}
    productListingId: string
    isServiceable: boolean


    constructor(fcMealCardRequest: FCMealCardRequest) {
    this.widgetType = "FC_MEAL_CARD_WIDGET"
    this.title = fcMealCardRequest.title
    this.subTitle = fcMealCardRequest.subTitle
    this.calories = fcMealCardRequest.calories
    this.offer = fcMealCardRequest.offer
    this.isVeg = fcMealCardRequest.isVeg
    this.stock = fcMealCardRequest.stock
    this.price = fcMealCardRequest.price
    this.action = fcMealCardRequest.action
    this.image = fcMealCardRequest.image
    this.nutritionTags = fcMealCardRequest.nutritionTags
    this.mealSlot = fcMealCardRequest.mealSlot
    this.productListingId = fcMealCardRequest.productListingId
    this.isServiceable = fcMealCardRequest.isServiceable
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<FCMealCardWidget> {
        return this
    }
}

export interface OfferCard {
    title: string
    subTitle: string
    action: Action
}

export interface RestaurantDetailRequest {
    image: string
    restaurantName: string
    cuisineType: string
    price: ProductPrice
    priceSuffix: string
    rating: string
    deliveryTime: string
    deliveryTag: string
    description: string
    offers: OfferCard[]
    isServiceable: boolean
}

export interface RetailerFcId {
    retailerId: string
    fcId: string
    retailerName?: string
    image?: string
}

export class RetailerInfoWidget {
    widgetType: WidgetType
    retailerName: string
    image: string
    retailerFcIds: RetailerFcId[]
    isFcSelected: boolean

    constructor(retailerName: string, image: string, retailerFcIds: RetailerFcId[], isFcSelected: boolean) {
        this.widgetType = "RETAILER_INFO_WIDGET"
        this.retailerName = retailerName
        this.image = image
        this.retailerFcIds = retailerFcIds
        this.isFcSelected = isFcSelected
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<RetailerInfoWidget> {
        return this
    }
}

export class RetailerListWidget extends BaseWidget {
    widgets: RetailerInfoWidget[]

    constructor(retailerInfoWidgets: RetailerInfoWidget[]) {
        super("RETAILER_LIST_WIDGET")
        this.widgets = retailerInfoWidgets
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {
        return this
    }
}

export class RestaurantDetailWidget extends BaseWidget {
    image: string
    restaurantName: string
    cuisineType: string
    price: ProductPrice
    priceSuffix: string
    rating: string
    deliveryTime: string
    deliveryTag: string
    description: string
    offers: OfferCard[]
    isServiceable: boolean

    constructor(restaurantDetailRequest: RestaurantDetailRequest) {
        super("RESTAURANT_DETAIL_WIDGET")
        this.image = restaurantDetailRequest.image
        this.restaurantName = restaurantDetailRequest.restaurantName
        this.cuisineType = restaurantDetailRequest.cuisineType
        this.price = restaurantDetailRequest.price
        this.priceSuffix = restaurantDetailRequest.priceSuffix
        this.rating = restaurantDetailRequest.rating
        this.deliveryTime = restaurantDetailRequest.deliveryTime
        this.deliveryTag = restaurantDetailRequest.deliveryTag
        this.description = restaurantDetailRequest.description
        this.offers = restaurantDetailRequest.offers
        this.isServiceable = restaurantDetailRequest.isServiceable

    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {
        return this
    }
}

export class FCMenuRecommendationWidget  extends BaseWidget {
    title: string
    subtitle: string
    widgets: FCMealCardWidget[]

    constructor(title: string, subTitle: string, widgets: FCMealCardWidget[]) {
        super("FC_MENU_RECOMMENDATION_WIDGET")
        this.title = title
        this.subtitle = subTitle
        this.widgets = widgets
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {
        return this
    }
}

export class FCMenuSignatureWidget extends BaseWidget {
    title: string
    widgets: FCMealCardWidget[]

    constructor(title: string, widgets: FCMealCardWidget[]) {
        super("FC_MENU_SIGNATURE_WIDGET")
        this.title = title
        this.widgets = widgets
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {
        return this
    }
}

export class FCMenuCategoryWidget {
    widgetType: WidgetType
    title: string
    image: string
    widgets: FCMealCardWidget[]

    constructor(title: string, categoryImage: string, widgets: FCMealCardWidget[]) {
        this.widgetType = "FC_MENU_CATEGORY_WIDGET"
        this.title = title
        this.image = categoryImage
        this.widgets = widgets
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<FCMenuCategoryWidget> {
        return this
    }
}

export class FCMenuCategoryContainerWidget extends BaseWidget {
    title: string
    widgets: FCMenuCategoryWidget[]

    constructor(title: string, widgets: FCMenuCategoryWidget[]) {
        super("FC_MENU_CATEGORY_CONTAINER_WIDGET")
        this.title = title
        this.widgets = widgets
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {
        return this
    }
}

export class FCDetailPage  {
    pageAction: Action[] = []
    widgets: (IBaseWidget[] | IBaseWidget)[] = []
}


export interface FCPageRequest {
    userContext: UserContext
    date: Date
    lat: number
    long: number
    fcId: string
}

export interface IFCPage {
    build(fcPageRequest: FCPageRequest, userContext: UserContext): Promise<FCDetailPage>
}