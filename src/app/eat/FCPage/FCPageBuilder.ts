import {
    FCDetailPage,
    FCMealCardRequest,
    FCMealCardWidget,
    FCMenuCategoryContainerWidget,
    FCMenuCategoryWidget,
    FCMenuRecommendationWidget,
    FCMenuSignatureWidget,
    FCPageRequest,
    IFCPage, OfferCard,
    RestaurantDetailRequest,
    RestaurantDetailWidget,
    RetailerFcId,
    RetailerInfoWidget,
    RetailerListWidget
} from "./IFCPage"
import { LatLong } from "@curefit/location-common"
import { inject, injectable } from "inversify"
import { EAT_API_CLIENT_TYPES, IEatApiService } from "@curefit/eat-api-client"
import {
    FCPageResponse,
    MarketplaceMenuResponse,
    MenuAvailability,
    MenuTag,
    RetailerListing
} from "@curefit/eat-api-common"
import { CATALOG_CLIENT_TYPES, ICatalogServiceV3Reader, ICategoryService } from "@curefit/catalog-client"
import * as _ from "lodash"
import {
    ImagePathBuilder,
    ProductType,
    RetailerImageCategory,
    SellerFoodTypeImageCategory
} from "@curefit/product-common"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { IBaseWidget, IServiceInterfaces, UserContext } from "@curefit/vm-models"
import { Action } from "../../common/views/WidgetView"
import { MarketplaceProductInfo } from "@curefit/order-common/src/Order"
import { FoodProduct } from "@curefit/eat-common"
import { CFServiceInterfaces } from "../../page/vm/ServiceInterfaces"
import { UserInfo } from "@curefit/user-common"
import { Category } from "@curefit/catalog-common"
import { MarketplaceOfferRequestParams, OfferV2, ProductIdOfferIdMap } from "@curefit/offer-common"
import { IOfferServiceV2, OFFER_SERVICE_CLIENT_TYPES } from "@curefit/offer-service-client"
import { OrderSource } from "@curefit/order-common"
import { Vertical } from "@curefit/offer-common/src/Offer"

@injectable()
export class FCPageBuilder implements IFCPage {
    constructor(
        @inject(EAT_API_CLIENT_TYPES.IEatApiService) private eatApiClientService: IEatApiService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceV3Reader) private catalogService: ICatalogServiceV3Reader,
        @inject(CATALOG_CLIENT_TYPES.CategoryService) private categoryService: ICategoryService,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private interfaces: CFServiceInterfaces,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) private offerServiceV2: IOfferServiceV2
    ) {
    }
    private getProductListingIdToAvailability(fcResponse: FCPageResponse): Map<string, number> {
        const productListingIdToAvailability = new Map<string, number>()
        const menuAvailability = fcResponse.menu.availabilities
        if (_.isNil(menuAvailability))
            return undefined
        for (const menu of menuAvailability ) {
            const productListingId = menu.productListingId
            const available = menu.available
            productListingIdToAvailability.set(productListingId, available)
        }
        return productListingIdToAvailability
    }
    private getUniqueCategoryIds(products: FoodProduct[]): string[] {
        if (_.isNil(products))
            return []
        const categoryIds = _.uniq(products.map(product => {
            return product.categoryId
        }))

        return categoryIds
    }

    private getProductListingIds(menus: MenuAvailability[]): string[] {
        if (_.isNil(menus))
            return []
        const productListingIds: string[] = []
        for (const menu of menus ) {
            const productListingId = menu.productListingId
            productListingIds.push(productListingId)
        }
        return productListingIds
    }

    private getProductListingIdsBasedOnTags(menus: MenuAvailability[], menuTag: MenuTag): string[] {
        if (_.isNil(menus))
            return []
        const productListingIds: string[] = []
        for (const menu of menus ) {
            const productListingId = menu.productListingId
            const tags = menu.tags
            const isTagPresent = !_.isNil(tags) && _.some(tags, tag => {
                if (tag === menuTag)
                    return true
            })
            if (isTagPresent) {
                productListingIds.push(productListingId)
            }
        }
        return productListingIds
    }

    private async getCategories(categoryIds: string[], sellerId: string): Promise<Category[]> {
        const categoriesPromise: Promise<Category>[] = []
        for (const categoryId of categoryIds ) {
            const categoryPromise: Promise<Category> = this.categoryService.getCLPCategory(sellerId, categoryId)
            categoriesPromise.push(categoryPromise)
        }
        const categories = Promise.all(categoriesPromise)
        return categories
    }

    private getClpCategoryToCategoryMap(categoryIds: string[], categories: Category[]): { [clpCategoryId: string]: string } {
        const clpCategoryMap: { [clpCategoryId: string]: string } = {}
        for (let index = 0; index < categoryIds.length; index++) {
            const id = categoryIds[index]
            clpCategoryMap[id] = categories[index].id
        }
        return clpCategoryMap
    }

    async getProducts(fcResponse: FCPageResponse, tag?: MenuTag): Promise<FoodProduct[]> {
        const marketplaceMenuResponse: MarketplaceMenuResponse = fcResponse.menu
        const retailerListing: RetailerListing = fcResponse.retailerListing
        const fc = retailerListing.fcs[0].fc
        const menus: MenuAvailability[] = marketplaceMenuResponse.availabilities
        const locationFQN = fc.locationFQN
        const listingIds: string[] = tag ? this.getProductListingIdsBasedOnTags(menus, tag) : this.getProductListingIds(menus)
        if (_.isNil(listingIds) || _.isEmpty(listingIds)) {
            return undefined
        }
        const products: Promise<FoodProduct[]> = this.catalogService.getFoodProductListings(listingIds, "ONLINE", locationFQN)
        return products
    }

    private getSortedCategories(categories: Category[]): Category[] {
        const sortedCategories = categories.sort((a, b) => {
            return a.sortOrder < b.sortOrder ? -1 : 1
        })
        return sortedCategories
    }

    private async getFCMealCardsFromFoodProducts(fcResponse: FCPageResponse, productIdOfferIdMap: ProductIdOfferIdMap, foodProducts: FoodProduct[], userContext: UserContext, isCategoryWidget: boolean): Promise<FCMealCardWidget[]> {
        const productListingIdToAvailability = this.getProductListingIdToAvailability(fcResponse)
        const fcMealCardWidgetsPromise: Promise<FCMealCardWidget>[] = []
        const retailerListing: RetailerListing = fcResponse.retailerListing
        const fc = retailerListing.fcs[0].fc
        const fcId = fc.fcId
        const retailer = retailerListing.retailer
        const retailerId = retailer.retailerId
        const offers = retailerListing.fcs[0].offers
        const offerText = !_.isEmpty(offers) ? offers[0].title : undefined
        for (const foodProduct of foodProducts) {
            const productListingId = foodProduct.listingId
            const marketplaceProductInfo: MarketplaceProductInfo = {
                productListingId: productListingId
            }
            const stock = productListingIdToAvailability.get(productListingId)
            const productPrice = productIdOfferIdMap[productListingId].product
            const calories = foodProduct.attributes.nutritionInfo.Calories["Total Calories"]
            const fcMealCardRequest: FCMealCardRequest = {
                productListingId: productListingId,
                title: foodProduct.title,
                subTitle: foodProduct.subTitle,
                calories: !_.isNil(calories) ? `${calories} cal` : undefined,
                isVeg: foodProduct.isVeg,
                stock: stock,
                price: productPrice.price,                                                          // This price is the final price after offers are applied
                mealSlot: {id: "ALL", name: "ALL"},
                action: {
                    title: (stock > 0) ?  "ADD" : "SOLD OUT",
                    actionType: "ADD_TO_FC_CART",
                    meta: {
                        marketplaceFCId: fcId,
                        marketplaceSalesChannel: "ONLINE",
                        marketplaceProductInfo: marketplaceProductInfo,
                        productId: foodProduct.productId,
                        listingBrand: "EAT_3P",
                        retailerId: retailerId,
                        productListingId: productListingId,
                        stock: productListingIdToAvailability.get(productListingId),
                        price: foodProduct.price,
                        mealSlot: { id: "ALL", name: "ALL" },
                    }
                },
                image: !isCategoryWidget ? foodProduct.imageUrl : undefined,
                nutritionTags: foodProduct.nutritionTags,
                offer: offerText,
                isServiceable: retailerListing.isServiceable
            }
            const fcMealCardWidgetPromise = new FCMealCardWidget(fcMealCardRequest).buildView(this.interfaces, userContext, undefined)
            fcMealCardWidgetsPromise.push(fcMealCardWidgetPromise)
        }
        const fcMealCardWidgets = Promise.all(fcMealCardWidgetsPromise)
        return fcMealCardWidgets
    }

    private async getFCMenuCategoryWidgets(fcResponse: FCPageResponse, productIdOfferIdMap: ProductIdOfferIdMap , sellerId: string, foodProducts: FoodProduct[], userContext: UserContext): Promise<FCMenuCategoryWidget[]> {
        const fcMenuCategoryWidgetsPromise: Promise<FCMenuCategoryWidget>[] = []
        const categoryIds = this.getUniqueCategoryIds(foodProducts)
        const categories = await this.getCategories(categoryIds, sellerId)
        const clpCategoryToCategoryMap = this.getClpCategoryToCategoryMap(categoryIds, categories)
        const uniqueCategories = _.uniq(categories)
        const sortedCategories = this.getSortedCategories(uniqueCategories)
        for (const category of sortedCategories ) {
            const categoryId = category.id
            const categoryName = category.name
            const categoryBasedFoodProducts = _.filter(foodProducts,   foodProduct => {
                if (clpCategoryToCategoryMap[foodProduct.categoryId] === categoryId)
                    return true
            })
            const fcMealCardWidgets = await this.getFCMealCardsFromFoodProducts(fcResponse, productIdOfferIdMap, categoryBasedFoodProducts, userContext, true)
            const retailer =  fcResponse.retailerListing.retailer
            const sellerId = retailer.sellers[0].sellerId
            const imageVersions = retailer.imageVersions
            const categoryImage = !_.isNil(imageVersions) ? ImagePathBuilder.getSellerCategoryImagePath(sellerId, categoryId, SellerFoodTypeImageCategory.THUMBNAIL, imageVersions.thumbnail) : undefined
            const fcMenuCategoryWidget = new FCMenuCategoryWidget(categoryName, categoryImage, fcMealCardWidgets).buildView(this.interfaces, userContext, undefined)
            fcMenuCategoryWidgetsPromise.push(fcMenuCategoryWidget)
        }
        const fcMenuCategoryWidgets = await Promise.all(fcMenuCategoryWidgetsPromise)
        return fcMenuCategoryWidgets
    }

    private getFCRetailerIdPayload(availableRetailers: {
        retailerId: string;
        fcId: string;
        retailerName: string;
    }[]): RetailerFcId[] {
        const retailerFcIds = _.map(availableRetailers, availableRetailer => {
            const retailerFcId: RetailerFcId = {
                retailerId: availableRetailer.retailerId,
                fcId: availableRetailer.fcId,
                retailerName: availableRetailer.retailerName,
                image: ImagePathBuilder.getRetailerImagePath(availableRetailer.retailerId, RetailerImageCategory.LOGO, 2)
            }
            return retailerFcId
        })
        return retailerFcIds
    }

    async getRetailerListWidgetPromise(fcResponse: FCPageResponse, productIdOfferIdMap: ProductIdOfferIdMap, userContext: UserContext): Promise<IBaseWidget> {
        const availableRetailers = fcResponse.availableRetailers
        const retailerListWidgetsPromise: Promise<RetailerInfoWidget>[] = []
        const selectedFCId = fcResponse.menu.fcId
        const fcRetailerIdPayload = this.getFCRetailerIdPayload(availableRetailers)
        const allrestaurantsImage = "/image/marketplace/entities/resto_all.png"
        const retailerInfoWidgetPromise = new RetailerInfoWidget("ALL", allrestaurantsImage , fcRetailerIdPayload, false).buildView(this.interfaces, userContext, undefined)
        retailerListWidgetsPromise.push(retailerInfoWidgetPromise)
        for (const availableRetailer of availableRetailers) {
            const retailerId = availableRetailer.retailerId
            const fcId = availableRetailer.fcId
            const image = ImagePathBuilder.getRetailerImagePath(retailerId, RetailerImageCategory.LOGO, 2)
            const retailerName = availableRetailer.retailerName
            const isFcSelected = selectedFCId === fcId ? true : false
            const retailerInfoWidgetPromise = new RetailerInfoWidget(retailerName, image, [{retailerId, fcId}], isFcSelected).buildView(this.interfaces, userContext, undefined)
            retailerListWidgetsPromise.push(retailerInfoWidgetPromise)
        }
        const retailerInfoWidgets = await Promise.all(retailerListWidgetsPromise)
        const retailerListWidgetPromise = new RetailerListWidget(retailerInfoWidgets).buildView(this.interfaces, userContext, undefined)
        return retailerListWidgetPromise
    }

    private getOfferCardFromOffers(offers: OfferV2[]): OfferCard[] {
        if (_.isEmpty(offers))
            return undefined
        const offerCards: OfferCard[] = []
        for (const offer of offers) {
            const offerCard: OfferCard = {
                title: offer.title,
                subTitle: offer.description,
                action: {
                    actionType: "SHOW_OFFERS_TNC_MODAL"
                }
            }
            offerCards.push(offerCard)
        }
        return offerCards
    }

    async getRestaurantDetailWidgetPromise(fcResponse: FCPageResponse, productIdOfferIdMap: ProductIdOfferIdMap, userContext: UserContext): Promise<IBaseWidget> {
        const retailerListing: RetailerListing = fcResponse.retailerListing
        const retailer = retailerListing.retailer
        const retailerId = retailer.retailerId
        const fc = retailerListing.fcs[0].fc
        const cuisines = _.get(fc, "configs.food.cuisines", [""])
        const rating = _.get(fc, "configs.food.rating")
        const etaInMin = retailerListing.etaInMin
        const costForTwo = _.get(fc, "configs.food.costForTwo")

        const deliveryCharge = _.get(fc, "configs.extraCharges.deliveryCharge.amount", 0)
        const deliveryTag = deliveryCharge === 0 ? "Free delivery" : undefined
        const description = retailer.display.description
        const offers = retailerListing.fcs[0].offers
        const offerCards = this.getOfferCardFromOffers(offers)
        const isServiceable = retailerListing.isServiceable
        const restaurantDetailRequest: RestaurantDetailRequest = {
            image: ImagePathBuilder.getRetailerImagePath(retailerId, RetailerImageCategory.THUMBNAIL, 2),
            restaurantName: retailer.name,
            cuisineType: _.join(cuisines, ", "),
            price: {
                listingPrice: costForTwo,
                mrp: costForTwo,
                currency: retailerListing.currency
            },
            priceSuffix: "for two",
            rating: rating,
            deliveryTime: `${etaInMin} mins`,
            deliveryTag: deliveryTag,
            description: description,
            offers: offerCards,
            isServiceable: isServiceable
        }
        const restaurantDetailWidgetPromise = new RestaurantDetailWidget(restaurantDetailRequest).buildView(this.interfaces, userContext, undefined)
        return restaurantDetailWidgetPromise
    }

    async getSignatureDishesWidgetPromise(fcResponse: FCPageResponse, productIdOfferIdMap: ProductIdOfferIdMap, userContext: UserContext): Promise<IBaseWidget> {
        const signatureProducts: FoodProduct[] = await this.getProducts(fcResponse, "SIGNATURE")
        if (_.isEmpty(signatureProducts)) {
            return undefined
        }
        const fcMealCardWidgets = await this.getFCMealCardsFromFoodProducts(fcResponse, productIdOfferIdMap, signatureProducts, userContext, false)
        const fcMenuSignatureWidget = new FCMenuSignatureWidget("Healthy Options", fcMealCardWidgets).buildView(this.interfaces, userContext, undefined)
        return fcMenuSignatureWidget
    }

    async getRecommendedDishesWidgetPromise(fcResponse: FCPageResponse, productIdOfferIdMap: ProductIdOfferIdMap, userContext: UserContext): Promise<IBaseWidget> {
        const recommendedProducts = await this.getProducts(fcResponse, "RECOMMENDED")
        if (_.isNil(recommendedProducts)) {
            return undefined
        }
        const fcMealCardWidgets = await this.getFCMealCardsFromFoodProducts(fcResponse, productIdOfferIdMap, recommendedProducts, userContext, false)
        const fcMenuRecommendationWidget = new FCMenuRecommendationWidget("Healthy Options", "Healthy options from our partners", fcMealCardWidgets).buildView(this.interfaces, userContext, undefined)
        return fcMenuRecommendationWidget
    }

    async getMenuCategoryContainerWidgetPromise(fcResponse: FCPageResponse, productIdOfferIdMap: ProductIdOfferIdMap, userContext: UserContext): Promise<IBaseWidget> {
        const marketplaceMenuResponse: MarketplaceMenuResponse = fcResponse.menu
        const retailerListing: RetailerListing = fcResponse.retailerListing
        const fc = retailerListing.fcs[0].fc
        const menus: MenuAvailability[] = marketplaceMenuResponse.availabilities
        const locationFQN = fc.locationFQN
        const productListingIds: string[] = this.getProductListingIds(menus)
        const sellerId = retailerListing.retailer.sellers[0].sellerId
        const foodProducts: FoodProduct[] = await this.catalogService.getFoodProductListings(productListingIds, "ONLINE", locationFQN)
        if (_.isNil(foodProducts)) {
            return undefined
        }
        const fcMenuCategoryWidgets = await this.getFCMenuCategoryWidgets(fcResponse, productIdOfferIdMap, sellerId, foodProducts, userContext)
        const fcMenuCategoryContainerWidget = new FCMenuCategoryContainerWidget("FULL MENU", fcMenuCategoryWidgets).buildView(this.interfaces, userContext, undefined)
        return fcMenuCategoryContainerWidget
    }

    private getFCPageAction(userContext: UserContext): Action[] {
        const actions: Action[] = []
        const sessionInfo = userContext.sessionInfo
        if (!sessionInfo.isUserLoggedIn) {
            const action: Action = {
                actionType: "SHOW_ALERT_MODAL",
                title: "BUY",
                meta: {
                    title: "Login Required!",
                    subTitle: "Please login to continue",
                    actions: [{ actionType: "LOGOUT", title: "Login" }]
                }
            }
            actions.push({ ...action, title: "ADD" })
            return actions
        }
        return actions
    }
    async build(fcRequest: FCPageRequest, userContext: UserContext): Promise<FCDetailPage> {
        const latLong: LatLong = {
            lat: fcRequest.lat,
            long: fcRequest.long
        }
        const fcId = fcRequest.fcId
        const date = fcRequest.date
        const user = await this.interfaces.userCache.getUser(userContext.userProfile.userId)
        const userInfo: UserInfo = {
            userId: user.id,
            deviceId: userContext.sessionInfo.deviceId,
            email: user.email,
            phone: user.phone,
            workEmail: user.workEmail
        }
        const fcResponse = await this.eatApiClientService.getMarketplaceFCPage(latLong, fcId, "ONLINE", date, userInfo)
        const fcDetailPagePromise = this.buildView(fcResponse, userContext, userInfo)
        return fcDetailPagePromise
    }

    async buildView(fcResponse: FCPageResponse, userContext: UserContext, userInfo: UserInfo): Promise<FCDetailPage> {
        const marketplaceMenuResponse: MarketplaceMenuResponse = fcResponse.menu
        const retailerListing: RetailerListing = fcResponse.retailerListing
        const retailer = retailerListing.retailer
        const fc = retailerListing.fcs[0].fc
        const menus: MenuAvailability[] = marketplaceMenuResponse.availabilities
        const marketplaceOfferRequestParams: MarketplaceOfferRequestParams = {
            fcId: fc.fcId,
            sellerId: retailer.sellers[0].sellerId,
            location: fc.locationFQN,
            salesChannel: "ONLINE",
            productListingIds: this.getProductListingIds(menus),
            userId: userContext.userProfile.userId,
            deviceId: userContext.sessionInfo.deviceId,
            source: userContext.sessionInfo.orderSource,
            productType: "FOOD",
            vertical: "MARKETPLACE",
            cityId: userContext.userProfile.cityId
        }
        const productIdOfferIdMap = await this.offerServiceV2.getMarketplaceProductPrices(marketplaceOfferRequestParams)
        const widgetPromises: Promise<IBaseWidget | IBaseWidget[]>[] = []
        const retailerListWidgetPromise = this.getRetailerListWidgetPromise(fcResponse, productIdOfferIdMap, userContext)
        const restaurantDetailWidgetPromise = this.getRestaurantDetailWidgetPromise(fcResponse, productIdOfferIdMap, userContext)
        const recommendedMenuWidgetPromise = this.getRecommendedDishesWidgetPromise(fcResponse, productIdOfferIdMap, userContext)
        const signatureMenuWidgetPromise = this.getSignatureDishesWidgetPromise(fcResponse, productIdOfferIdMap, userContext)
        const fullMenuWidgetPromise = this.getMenuCategoryContainerWidgetPromise(fcResponse, productIdOfferIdMap,  userContext)

        widgetPromises.push(retailerListWidgetPromise)
        widgetPromises.push(restaurantDetailWidgetPromise)
        widgetPromises.push(recommendedMenuWidgetPromise)
        widgetPromises.push(signatureMenuWidgetPromise)
        widgetPromises.push(fullMenuWidgetPromise)
        const widgets = await Promise.all(widgetPromises)
        const filterWidgets = widgets.filter( widget => {
            if (_.isNil(widget))
                return false
            return true
        })
        return { pageAction: this.getFCPageAction(userContext), widgets: filterWidgets }
    }
}