import { EatMealsWidget } from "@curefit/vm-models"
import {
  IBaseWidget,
  UserContext
} from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../../page/vm/ServiceInterfaces"
import { WidgetDataProviderRequest } from "../../clp/IWidgetDataProvider"
import { Filter } from "@curefit/eat-common"
export class EatMarketPlaceWidget extends EatMealsWidget {
  declare mealWidgets: IBaseWidget[]
  async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: any }, sharedData?: any): Promise<IBaseWidget | IBaseWidget[]> {

    let filters = <Filter[]>queryParams.appliedFilters
    if (queryParams.key && queryParams.values) {
      const values = queryParams.values.split(",")
      const filter: Filter = {
        values: values,
        key: queryParams.key,
        type: "MULTISELECT",
        title: ""
      }
      if (filters) {
        filters.push(filter)
      } else {
        filters = [filter]
      }
    }
    const request: WidgetDataProviderRequest = {
      params: {
        appliedFilters: filters,
        appliedSort: queryParams.appliedSort,
        quickFilters: queryParams.quickFilters,
        quickSorts: queryParams.quickSorts,
        pageId: queryParams.pageId
      }
    }
    return interfaces.marketplaceDataProvider.create(request, interfaces, userContext)
  }
}

