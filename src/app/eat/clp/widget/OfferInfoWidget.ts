import { Action } from "@curefit/vm-models"
import { BaseWidget, IBaseWidget, IServiceInterfaces, UserContext } from "@curefit/vm-models"

export class OfferInfoWidget extends BaseWidget {
    private imageUrl: string
    private title: string
    private subTitle: string
    private action: Action

    constructor(imageUrl: string, title: string, subTitle: string, action: Action) {
        super("OFFER_INFO_WIDGET")
        this.imageUrl = imageUrl
        this.title = title
        this.subTitle = subTitle
        this.action = action
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {
        return this
    }
}
