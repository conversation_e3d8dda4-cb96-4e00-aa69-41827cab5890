import { IBaseWidget, UserContext } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../page/vm/ServiceInterfaces"
export class WidgetDataProviderRequest {
  params: {
    appliedFilters: any
    appliedSort: any
    quickFilters: any
    quickSorts: any
    pageId: any
  }
}
export default interface IWidgetDataProvider {
  create(request: WidgetDataProviderRequest, interfaces: CFServiceInterfaces, userContext: UserContext): Promise<IBaseWidget[]>
}