import IWidgetDataProvider, { WidgetDataProviderRequest } from "./IWidgetDataProvider"
import {
  EatFilterContainerWidgetView,
  EatTitleWidget,
  IBaseWidget,
  IServiceInterfaces,
  MealStoreCardRequest,
  SortFilterRequest, SortFilterWidget, TypographyWidget,
  UserContext
} from "@curefit/vm-models"
import { inject, injectable } from "inversify"
import { EAT_API_CLIENT_TYPES, IEatApiService } from "@curefit/eat-api-client"
import { LatLong } from "@curefit/location-common"
import { MealStoreCardWidget } from "@curefit/vm-models"
import { MarketplaceStoreListingResponse, RetailerListing } from "@curefit/eat-api-common"
import * as _ from "lodash"
import {
  QuickFilterRequest,
  QuickFilterWidget
} from "@curefit/vm-models"
import { HourMin } from "@curefit/base-common"
import { ImagePathBuilder, RetailerImageCategory } from "@curefit/product-common"
import { TimeUtil } from "@curefit/util-common"
import { UserInfo } from "@curefit/user-common"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { CacheHelper } from "../../util/CacheHelper"
import { CFServiceInterfaces } from "../../page/vm/ServiceInterfaces"

@injectable()
export default class EatMarketplaceDataProvider implements IWidgetDataProvider {

  constructor(@inject(EAT_API_CLIENT_TYPES.IEatApiService) private eatApiClientService: IEatApiService,
              @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper) {}
  private getTypographyWidget(interfaces: IServiceInterfaces, userContext: UserContext, lineOneText: string): IBaseWidget {
    const typographyWidget = new TypographyWidget()
    typographyWidget.data = [{
      text: lineOneText,
      fontColor: "#6e6e6e",
      fontSize: 20,
      desktopFontSize: 35

    }]
    typographyWidget.buildView(interfaces, userContext, undefined)
    return typographyWidget
  }

  private getEtaSubtitle(time: HourMin): string {
    let subTitle: string = ""
    if (time.hour > 0) {
      subTitle = subTitle + `${time.hour}` + (time.hour === 1 ? " hour " : " hours ")
    }
    subTitle = subTitle + `${time.min}` + " mins"
    return subTitle
  }

  private getFreeDeliverySubtitle(deliveryCharge: number) {
    if (deliveryCharge === 0)
      return "Free Delivery"
    return ""
  }

  private getStoreInfo(rating: string, etaInMin: number, deliveryCharge: number): string {
    const time: HourMin = TimeUtil.getHourMinFromMinuteOffset(etaInMin)
    const ratingVal = rating ? rating : "NA"
    const eta = this.getEtaSubtitle(time)
    const freeDelivery = this.getFreeDeliverySubtitle(deliveryCharge)
    const storeInfo: string = "⭑ " + `${ratingVal}   ` + "• " +  `${eta}   ` + "• " + `${freeDelivery}`
    return storeInfo
  }

  async getStoreListing(request: WidgetDataProviderRequest, userContext: UserContext): Promise<MarketplaceStoreListingResponse> {
    const preferredLocation = await userContext.userProfile.preferredLocationPromise
    const user = await this.userCache.getUser(userContext.userProfile.userId)
    const userInfo: UserInfo = {
      userId: user.id,
      deviceId: userContext.sessionInfo.deviceId,
      email: user.email,
      phone: user.phone,
      workEmail: user.workEmail
    }
    const latLong: LatLong = preferredLocation.latLong
    const appliedFilters = request.params.appliedFilters
    const appliedSort = request.params.appliedSort
    const listing = await this.eatApiClientService.getStoreListingsForMarketplace(latLong, appliedFilters, appliedSort, "ONLINE", new Date(), userInfo)
    return listing
  }

  async getMealStoreCardWidget(interfaces: IServiceInterfaces, userContext: UserContext, storeListing: MarketplaceStoreListingResponse): Promise<IBaseWidget[]> {
    const retailerListings: RetailerListing[] = storeListing.retailerListings
    const mealStoreCardsWidgetPromise: Promise<IBaseWidget>[] = _.map(retailerListings, retailerListing => {
      const retailer = retailerListing.retailer
      const etaInMin = retailerListing.etaInMin
      const isServiceable = retailerListing.isServiceable
      const fc = retailerListing.fcs[0].fc
      const offers = retailerListing.fcs[0].offers
      const offerText = !_.isNil(offers) ? offers[0].title : ""
      if (!_.isNil(fc)) {
        const costForTwo = _.get(fc, "configs.food.costForTwo")
        const currency = retailerListing.currency
        const cuisines = _.get(fc, "configs.food.cuisines", [""])
        const rating = _.get(fc, "configs.food.rating")
        const deliveryCharge = _.get(fc, "configs.extraCharges.deliveryCharge.amount", 0)
        const retailerId = retailer.retailerId
        const imageVersions = retailer.imageVersions
        const mealStoreCardRequest: MealStoreCardRequest = {
          title: retailer.name,
          subTitle: _.join(cuisines, ", "),
          storeInfo: this.getStoreInfo(rating, etaInMin, deliveryCharge),
          price: {
            listingPrice: costForTwo,
            mrp: costForTwo,
            currency: currency,
          },
          isServiceable: isServiceable,
          priceSuffix: "for two",
          imageUrl: !_.isNil(imageVersions) ? ImagePathBuilder.getRetailerImagePath(retailerId, RetailerImageCategory.THUMBNAIL, imageVersions.thumbnail) : undefined,
          brandUrl: !_.isNil(imageVersions) ? ImagePathBuilder.getRetailerImagePath(retailerId, RetailerImageCategory.LOGO, imageVersions.logo) : undefined,
          offer: offerText,
          action: {
            actionType: "NAVIGATION",
            url: `curefit://restaurantpage?fcId=${fc.fcId}`
          }
        }
        const mealStoreCardWidgetPromise = new MealStoreCardWidget(mealStoreCardRequest).buildView(interfaces, userContext, undefined)
        return mealStoreCardWidgetPromise
      }
    })
    const mealStoreCardsWidget = await Promise.all(mealStoreCardsWidgetPromise)
    return mealStoreCardsWidget
  }

  async getSortFiltersWidget(interfaces: IServiceInterfaces, userContext: UserContext, storeListing: MarketplaceStoreListingResponse): Promise<IBaseWidget> {
    const sortFilterRequest: SortFilterRequest = {
      availableFilters: storeListing.availableFilters,
      availableSorts: storeListing.availableSorts,
      appliedSort : storeListing.appliedSort,
      appliedFilters : storeListing.appliedFilters,
      eatClpTab: "MARKETPLACE"
    }
    return new SortFilterWidget(sortFilterRequest).buildView(interfaces, userContext, undefined)
  }

  async getQuickFiltersWidget(interfaces: IServiceInterfaces, userContext: UserContext, storeListing: MarketplaceStoreListingResponse): Promise<IBaseWidget> {
    const quickFilterRequest: QuickFilterRequest = {
      quickFilters: storeListing.quickFilters,
      quickSorts: storeListing.quickSorts,
      eatClpTab: "MARKETPLACE"
    }
    return new QuickFilterWidget(quickFilterRequest).buildView(interfaces, userContext, undefined)
  }

  async getEatfilterContainerWidget(interfaces: IServiceInterfaces, userContext: UserContext, storeListing: MarketplaceStoreListingResponse, pageId: string): Promise<IBaseWidget> {
    if (pageId === "eatfclistingpage")
      return undefined
    const eatTitleWidgetPromise = new EatTitleWidget("ALL RESTAURANTS").buildView(interfaces, userContext, undefined)
    const sortFilterWidgetPromise = this.getSortFiltersWidget(interfaces, userContext, storeListing)
    const quickFilterWidgetPromise = this.getQuickFiltersWidget(interfaces, userContext, storeListing)
    const widgetsPromise: Promise<IBaseWidget> [] = []
    widgetsPromise.push(eatTitleWidgetPromise)
    widgetsPromise.push(sortFilterWidgetPromise)
    widgetsPromise.push(quickFilterWidgetPromise)
    const widgets = await Promise.all(widgetsPromise)
    const eatFilterContainerWidget = new EatFilterContainerWidgetView(widgets).buildView(interfaces, userContext, undefined)
    return eatFilterContainerWidget
  }

  async create(request: WidgetDataProviderRequest, interfaces: IServiceInterfaces, userContext: UserContext): Promise<IBaseWidget[]> {
    const storeListing = await this.getStoreListing(request, userContext)
    if (_.isEmpty(storeListing.retailerListings)) {
      const widgets: IBaseWidget[] = []
      const typographyWidget =  this.getTypographyWidget(interfaces, userContext, "Address not serviceable")
      widgets.push(typographyWidget)
      const filterWidgets = widgets.filter( widget => {
        if (_.isNil(widget))
          return false
        return true
      })
      return filterWidgets
    }
    else {
      const pageId = request.params.pageId
      const eatFilterContainerWidgetPromise = this.getEatfilterContainerWidget(interfaces, userContext, storeListing, pageId)
      const mealStoreCardWidgetsPromise = this.getMealStoreCardWidget(interfaces, userContext, storeListing)
      const widgets: IBaseWidget[] = []
      widgets.push(await eatFilterContainerWidgetPromise)
      widgets.push(... await mealStoreCardWidgetsPromise)
      const filterWidgets = widgets.filter( widget => {
        if (_.isNil(widget))
          return false
        return true
      })
      return filterWidgets
    }

  }
}