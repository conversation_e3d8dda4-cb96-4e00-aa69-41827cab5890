import * as express from "express"
import { controller, httpGet } from "inversify-express-utils"
import { Container, inject } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { Logger, BASE_TYPES } from "@curefit/base"
import { IFlashService, FLASH_CLIENT_TYPES } from "@curefit/flash-client"
import * as admin from "firebase-admin"
import OrderTrackFirebaseService from "./OrderTrackFirebaseService"
import AuthMiddleware from "../auth/AuthMiddleware"
import { TimeUtil } from "@curefit/util-common"
import { UserContext } from "@curefit/userinfo-common"


export function LiveTrackingControllerFactory(kernel: Container) {
    /**
     * Class for Eatfit - order tracking related rest end-points
     */
    @controller("/eat", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class LiveTrackingController {
        constructor(
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(FLASH_CLIENT_TYPES.FlashService) private flashService: IFlashService,
            @inject(CUREFIT_API_TYPES.OrderTrackFirebaseService) private orderTrackFirebaseService: OrderTrackFirebaseService,
        ) {
        }

        /**
         * TODO: Change this to use firebase password instead of userId to generate he token
         * @param request
         */
        @httpGet("/getFirebaseCustomToken")
        public async getFirebaseAccessToken(request: express.Request) {
            const customToken: string = await this.getCustomToken(request.session.userId)
            if (customToken) {
                return customToken
            }
            this.logger.error("Custom token generation failed for user : " + request.session.userId)
            return null

        }

        /**
         * Create custom token for firebase login using userId
         *
         * @param userId
         */
        getCustomToken(userId: string): Promise<string> {
            try {
                return admin.auth().createCustomToken(userId)
            }
            catch (error) {
                this.logger.error("Error creating custom token for  userId: " + userId, error)
                return null
            }
        }

        /**
         * This method is to be called from the user's android app, with fulfillmentId to fetch firebase details for order tracking,
         * order state, location pins to be shown and other details in {@link LiveTrackingResponse}
         *
         * If lot is not yet created, we return OrderState.PROCESSING
         * If created, we check the state of the consignment delivery and return states accordingly.
         *
         * @param request
         */
        @httpGet("/order-tracking/live/:fulfillmentId")
        public async liveTrackingDetails(request: express.Request) {
            const userContext = request.userContext as UserContext
            this.logger.info("liveTrackingDetails Request for fulfillmentId " + request.params.fulfillmentId + "received for date " + request.query.date)
            const date = request.query.date ? request.query.date : TimeUtil.todaysDate(userContext.userProfile.timezone)
            return await this.flashService.liveTrackingDetails(request.params.fulfillmentId, date)
        }
    }

    return LiveTrackingController
}
