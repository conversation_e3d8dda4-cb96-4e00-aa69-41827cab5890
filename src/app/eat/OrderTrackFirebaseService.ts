import { inject, injectable } from "inversify"
import { Logger, BASE_TYPES } from "@curefit/base"

import * as admin from "firebase-admin"
import * as path from "path"

@injectable()
class OrderTrackFirebaseService {

    constructor(@inject(BASE_TYPES.ILogger) private logger: Logger ) {
        this.logger.info("Initialising firebase")
        admin.initializeApp({
            credential: admin.credential.cert(this.getFireBaseServiceAccount()),
            databaseURL: this.getFireBaseDbUrl()
        })
    }

    private getFireBaseServiceAccount() {
        // if (process.env.ENVIRONMENT === "PRODUCTION") {
            console.log(__dirname)
            return path.resolve(__dirname + "/service-account.json")
        // } else {
        //     return path.resolve(__dirname + "/service-account-stage.json")
        // }
    }

    private getFireBaseDbUrl() {
        // if (process.env.ENVIRONMENT === "PRODUCTION") {
            return "https://order-tracking.firebaseio.com"
        // } else {
        //     return "https://order-tracking-stage.firebaseio.com"
        // }
    }

}

export default OrderTrackFirebaseService
