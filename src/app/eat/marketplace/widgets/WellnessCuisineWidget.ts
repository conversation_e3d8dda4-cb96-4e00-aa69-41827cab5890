import * as _ from "lodash"
import { Action, BaseWidget, IBaseWidget, UserContext } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../../page/vm/ServiceInterfaces"
import { Cuisine, CuisinesForLocationRequest } from "@curefit/foodway-common"
import EatUtil from "../../../util/EatUtil"
import AppUtil from "../../../util/AppUtil"

export class WellnessCuisineWidget extends BaseWidget {
    title: string
    subtitle: string
    seemore: Action
    cuisineList: {
      cuisine: Cuisine,
      action: Action
    }[]
  constructor() {
    super("WELLNESS_CUISINES_WIDGET")
      this.title = "Order healthy homely meals"
      this.subtitle = "Taste and health in every bite"
  }
  async buildView(interfaces?: CFServiceInterfaces, userContext?: UserContext, queryParams?: {[param: string]: any}): Promise<IBaseWidget> {

     if (! (await AppUtil.isFoodMarketplaceSupported(userContext, interfaces.hamletBusiness))) {
         return undefined
     }
     if (userContext.userProfile.preferredLocationPromise) {
         const preferredLocation = await userContext.userProfile.preferredLocationPromise
         const latLong = EatUtil.getLatLong(preferredLocation, undefined, undefined)
         const req: CuisinesForLocationRequest = {
            latitude: latLong.lat,
             longitude: latLong.long
         }
         const response = await interfaces.foodwayService.getCuisinesForLocation(req)
         if (response && !_.isEmpty(response?.cuisinesList)) {
             this.cuisineList = response.cuisinesList.map((item: Cuisine) => {
                 const action: Action = {
                    actionType: "NAVIGATION",
                    url: "curefit://tabpage?pageId=foodmpoutletlist"
                 }
                 return {
                    cuisine: item,
                    action: action
                 }
             })
             return this
         }
     }
     return undefined
  }
}
