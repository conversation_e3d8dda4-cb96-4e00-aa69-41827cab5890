import * as _ from "lodash"
import { OutletsForLocationAndCuisineRequest } from "@curefit/foodway-common"
import { BrandWithOutlet } from "@curefit/foodway-common/dist/src/models/appUI/BrandWithOutlet"
import { Action, ActionTypes, BaseWidget, IBaseWidget, IServiceInterfaces, UserContext } from "@curefit/vm-models"
import ActionUtil from "../../../util/ActionUtil"
import EatUtil from "../../../util/EatUtil"
import AppUtil from "../../../util/AppUtil"
import { CFServiceInterfaces } from "../../../page/vm/ServiceInterfaces"


interface Brand extends BrandWithOutlet {
    coverImageUrl: string
}
interface BrandInfo {
    brand: Brand
    action: Action
}
export class BrandListingWidget extends BaseWidget {

    title: string
    subtitle: string
    brandList: BrandInfo[]
    seemore: Action
    constructor() {
        super("FM_BRAND_LISTING_WIDGET")
        this.title = "Restaurants near you"
        this.subtitle = "Healthy meals around your location"
        this.seemore = {
            actionType: "NAVIGATION",
            url: "curefit://tabpage?pageId=foodmpoutletlist"
        }
      }
    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: any }): Promise<IBaseWidget> {

        if (! (await AppUtil.isFoodMarketplaceSupported(userContext, interfaces.hamletBusiness))) {
            return undefined
        }
        let latFromClient: number = queryParams["lat"]
        let longFromClient: number = queryParams["lon"]

        if (userContext.userProfile.preferredLocationPromise) {
            const preferredLocation = await userContext.userProfile.preferredLocationPromise
            if (preferredLocation?.defaultArea?.representativeLatLong || preferredLocation?.area?.representativeLatLong || preferredLocation?.address?.latLong) {
                // since we're using a deafult area, we should consider its latlong rather than user's
                latFromClient = undefined
                longFromClient = undefined
            }
            const latLong = EatUtil.getLatLong(preferredLocation, latFromClient, longFromClient)
            const req: OutletsForLocationAndCuisineRequest = {
               latitude: latLong.lat,
                longitude: latLong.long
            }
            const response = await interfaces.foodwayService.getOutletsForLocationAndCuisine(req)
            if (response?.brandList && response.brandList.length > 0) {
                response.outletsList.map(outlet => outlet.imageUrl)
                this.brandList = response.brandList.map((brandWithOutlet: BrandWithOutlet) => {
                    let actionUrl = ""
                    if (brandWithOutlet.foodOutletIds.length > 0) {
                        actionUrl = ActionUtil.eatFitClp()
                        actionUrl += ActionUtil.serializeAsQueryParams({ outletId: brandWithOutlet.foodOutletIds[0]})
                    }
                    const brandInfo: BrandInfo = {
                        brand: {
                            ...brandWithOutlet,
                            coverImageUrl: response.outletsList.find(outlet => outlet.outletId === brandWithOutlet?.foodOutletIds?.[0]).imageUrl,
                        },
                        action: {
                            actionType: "NAVIGATION",
                            url: actionUrl
                        }
                    }
                    return brandInfo
                })
                return this
            }
        }
        return undefined
    }
}
