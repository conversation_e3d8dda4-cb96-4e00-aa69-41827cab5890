import * as _ from "lodash"
import { Action, BaseWidget, IBaseWidget } from "@curefit/vm-models"
import { ProductPrice } from "@curefit/product-common"
import { CFServiceInterfaces } from "../../../page/vm/ServiceInterfaces"
import { UserContext } from "@curefit/userinfo-common"
import { FoodProduct } from "@curefit/eat-common"
import { OutletDetails } from "@curefit/foodway-common"
import { MAX_CART_SIZE } from "@curefit/base-utils"
import { MealAction } from "../../../common/views/WidgetView"
import ActionUtil from "../../../util/ActionUtil"
import FoodMarketplaceUtil from "../../../util/FoodMarketplaceUtil"
import { FoodMarketplaceProductNutritionProfile } from "../Interfaces"
import { RestaurantCardWidget } from "./RestaurantCardWidget"



export interface FeaturedRestaurantCard {
    image: string
    title: string
    isVeg: boolean
    attributes: string[]
    price: ProductPrice
    action: MealAction
    listingBrand?: string
    productId: string
    mealSlot: string
    date?: string
    stock: number
    calories?: string
}

export class FeaturedRestaurantWidget extends BaseWidget implements IBaseWidget {
    action?: Action
    header?: {
        title?: string
        subtitle?: string
        icon?: string
        eta?: string
    }
    cards: FeaturedRestaurantCard[]
    outlet: OutletDetails
    constructor(outlet: OutletDetails) {
        super("FEATURED_RESTAURANT_WIDGET")
        this.outlet = outlet // purge once used, this is of no use to the app
        this.cards = []
        this.action = {
            actionType: "NAVIGATION",
            title: "FULL MENU",
            url: ActionUtil.eatFitClp() + ActionUtil.serializeAsQueryParams({ outletId: outlet.outletId}),
        }
        this.header = {
            title: outlet.name,
            subtitle: outlet.cuisinesList?.join(", ") ?? undefined,
            icon: outlet.imageUrl,
            eta: undefined // wire this
        }
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: {[param: string]: any}): Promise<IBaseWidget> {
        // 1. use product ids and get the products from redis
        // 2. make call to the offer service to fetch the offer prices todo
        // 3. create card
        // 4. purge the @Param productIds

        if (_.isEmpty(this.outlet?.recommendedProducts?.recommendedProductIds)) {
            // if the selected product does not have products, render the normal view
            return new RestaurantCardWidget(this.outlet).buildView()
        }
        // Assuming the product ids will be in stock
        const { recommendedProductIds } = this.outlet?.recommendedProducts
        const catalogMap = await interfaces.catalogueService.getProductMap(recommendedProductIds)
        for (let i = 0; i < recommendedProductIds?.length; i += 1) {
            const product: FoodProduct = catalogMap[recommendedProductIds[i]]
            if (_.isEmpty(product))
                continue
            const { imageUrl: image, title, foodType, price } = product
            const card: FeaturedRestaurantCard = {
                attributes: this.buildCardAttributes(product), // revisit
                image,
                title,
                action: FoodMarketplaceUtil.getFoodCardAction(product, this.outlet.outletId, this.outlet.brandId, "", MAX_CART_SIZE),
                isVeg: foodType === "Veg",
                price, // look for offer price here
                listingBrand: "FOOD_MARKETPLACE",
                productId: product.productId,
                mealSlot: "ALL",
                date: "",
                stock: MAX_CART_SIZE,
            }
            this.cards.push(card)
        }

        if (_.isEmpty(this.cards)) {
            // if the catalog products are missing from redis
            return new RestaurantCardWidget(this.outlet).buildView()
        }
        delete this.outlet
        return this
    }

    private buildCardAttributes(product: FoodProduct) {
        const response: string[] = [];
        (product?.attributes?.nutritionalInfo?.nutritionFields ?? []).forEach((entry: FoodMarketplaceProductNutritionProfile) => {
            if (entry?.nutritionParameter && entry?.value) {
                response.push(entry?.value + entry?.nutritionParameter?.unitLabel + " " + entry?.nutritionParameter?.name)
            }
        })
        return response
    }
}
