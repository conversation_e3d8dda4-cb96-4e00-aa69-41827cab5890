import { BaseWidget, IBaseWidget, UserContext } from "@curefit/vm-models"
import _ = require("lodash")
import { CFServiceInterfaces } from "../../../page/vm/ServiceInterfaces"
import { OutletDetails } from "@curefit/foodway-common"
import { ActionUtil } from "@curefit/base-utils"
import { Action } from "@curefit/vm-models"

export class RestaurantCardWidget extends BaseWidget {

  outlet: OutletDetails
  action: Action
  disabled?: boolean

  constructor(outlet: OutletDetails) {
    super("FM_RESTAURANT_CARD_WIDGET")
    this.outlet = outlet
    let url = ActionUtil.eatFitClp()
    url += ActionUtil.serializeAsQueryParams({ outletId: this.outlet.outletId})
      this.action = {
          actionType: "NAVIGATION",
          url: url,
      }
    this.disabled = !(!!outlet.isOpen)
  }
  async buildView(interfaces?: CFServiceInterfaces, userContext?: UserContext, queryParams?: {[param: string]: any}): Promise<IBaseWidget> {
       return this
    }
}
