export interface FMMealNutritionProfile {
    displayAttributes?: FMMealNutritionDisplayAttributes
}

export interface FMMealDisplayAttributes {
    value: string | number
    postFix: string
}

export interface FMMealNutritionDisplayAttributes {
    values: FMMealDisplayAttributes[]
    seeMore?: {
        text: string,
        icon: string
    }
    seeLess?: {
        text: string,
        icon: string
    }
    valuesToDisplay?: number
}
