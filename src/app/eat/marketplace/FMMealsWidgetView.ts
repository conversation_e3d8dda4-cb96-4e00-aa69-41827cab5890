import { MAX_CART_SIZE } from "@curefit/base-utils"
import { FoodCategory, FoodProduct, MenuTypes } from "@curefit/eat-common"
import { Category, MenuForOutletRequest, OutletDetails, Subcategory, SubcategoryItem } from "@curefit/foodway-common"
import { FoodMarketPlaceVariantAddonGroup, Product, UrlPathBuilder } from "@curefit/product-common"
import { Action } from "@curefit/apps-common"
import { EatCategoryNavWidget, EatCategoryWidget, EatFilterContainerWidgetView, EatFilterWidget, EatMealsWidget, EatMenuWidget, EatTitleWidget, IBaseWidget, IServiceInterfaces, SessionInfo, UserContext } from "@curefit/vm-models"
import _ = require("lodash")
import { MealAction, WidgetView } from "../../common/views/WidgetView"
import { MealItem, TypographyWidget } from "../../page/PageWidgets"
import { CFServiceInterfaces } from "../../page/vm/ServiceInterfaces"
import { MealCardWidget } from "../../page/vm/widgets/MealsCardWidget"
import AppUtil from "../../util/AppUtil"
import EatUtil  from "../../util/EatUtil"
import FoodMarketplaceUtil from "../../util/FoodMarketplaceUtil"
import { FMMealNutritionProfile } from "./FMMealNutritionProfile"


export class FMMealsWidgetView extends EatMealsWidget {

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }, sharedData?: any): Promise<any[]> {
        const outletId = queryParams.outletId
        const productId = queryParams?.productId

        if (!outletId) {
            return undefined
        }
        this.eatClpTab = "MARKETPLACE"

        const selectedFoodProduct: FoodProduct = productId ? await interfaces.catalogueService.getProduct(productId) : null

        const request: MenuForOutletRequest = {
            outletId:  outletId
        }
        const finalWidgets: (IBaseWidget | WidgetView)[] = []
        const isNewMealCardSupported = AppUtil.isNewFMMealCardSupported(userContext)

        const response = await interfaces.foodwayService.getMenuForOutlet(request)
        const { outletDetails, outletMenu } = response

        const { allItemsAvailability, allAddonsAvailability, allVariantsAvailability }  = outletMenu
        let { categories = [] } = outletMenu
        const sidesAvailablity: { [id: string]: Boolean } = { ...(allVariantsAvailability || {}), ...(allAddonsAvailability || {}) }
        const { name } = outletDetails

        // Sorting Category as per the selected food product
        if (selectedFoodProduct) {
            categories = _.sortBy(categories, category => category.id === selectedFoodProduct?.categoryId ? 0 : 1)
        }

        const foodCategories = _.map(categories, (category) => {
            const foodCategory: FoodCategory = {
              categoryId: category.id,
              name: category.name,
              isLeaf: false,
              useForClpListing: true,
             parentCategoryId: null,
             listingBrand: "FOOD_MARKETPLACE",
             childCategoryIds: _.map(category.subcategories, (subcategory) => subcategory.id)}
        return foodCategory
        })

        // Category widgets
        const categoryWidgetMap = await this.createCategoryWidgetMap(foodCategories)
        const clpCategoriesByCategoryId: { [categoryId: string]: FoodCategory } = _.keyBy(foodCategories, item => item.categoryId)

        const categoryNavWidgetPromise = new EatCategoryNavWidget(_.map(categories, (category) => {
            const foodCategory: FoodCategory = {
              categoryId: category.id,
              name: category.name,
              isLeaf: false,
              useForClpListing: true,
             parentCategoryId: null,
             listingBrand: "FOOD_MARKETPLACE",
             childCategoryIds: _.map(category.subcategories, (subcategory) => subcategory.id)
            }
            return foodCategory
        }), this.eatClpTab).buildView(interfaces, userContext, queryParams)

        const filterListWidgets: IBaseWidget[] = []
        const [filterWidget, categoryNavWidget] = await Promise.all([
            new EatFilterWidget([], this.eatClpTab).buildView(interfaces, userContext, queryParams),
            categoryNavWidgetPromise
        ])
        filterListWidgets.push(filterWidget)
        filterListWidgets.push(categoryNavWidget)
        const filterListWidget = await new EatFilterContainerWidgetView(filterListWidgets).buildView(interfaces, userContext, queryParams)
        finalWidgets.push(filterListWidget)

        let outOfStockCount = 0, inStockCount = 0
        const catalogProducts: FoodProduct[] = _.compact(await interfaces.catalogueService.getProducts(Object.entries(allItemsAvailability).map(([key, value]) =>  key)))
        const mealItems = _.map(catalogProducts,  (product) => {
            const stock = allItemsAvailability[product.productId] && outletDetails.isOpen ? MAX_CART_SIZE : 0
            if (stock > 0) {
                inStockCount += 1
            } else {
                outOfStockCount += 1
            }
            const hasVariants = product.variantGroups && product.variantGroups.length > 0
            const hasAddons = product.addonGroups && product.addonGroups.length > 0
            const actions: (Action | MealAction)[] = this.getMealActions(product, stock, "",  userContext, outletDetails, sidesAvailablity)
            const mealItem: MealItem = {
                title: product.title,
                calories: this.getCalorieInfoForMealItem(product),
                nutritionProfileInfo: this.buildNutritionProfileInfo(product),
                titleWithoutUnits: product.titleWithoutUnits,
                price: product.price,
                date: "",
                image: product.imageUrl,
                imageThumbnail: product.imageUrl,
                isInventorySet: true,
                stock,
                actions: actions,
                categoryId: product.categoryId,
                productId: product.productId,
                isVeg: product.foodType === "Veg",
                shipmentWeight: product.shipmentWeight,
                foodCategoryId: product.categoryId,
                nutritiontags: product.nutritionTags,
                displayUnitQty: EatUtil.getQuantityTitle(product),
                variantTitle: EatUtil.getVariantTitle(product, "EAT_FIT"),
                listingBrand: "FOOD_MARKETPLACE",
                customisableText: hasAddons || hasVariants ? "customisable" : undefined,
                description: product.subTitle
            }
            return mealItem
        })

        const mealWidgetPromises: Promise<IBaseWidget>[] = _.map(mealItems, (mealItem: MealItem) => {
            return new MealCardWidget(mealItem, "ALL", clpCategoriesByCategoryId, isNewMealCardSupported ? "FM_MEAL_CARD" : "SMALL_CARD", undefined, false, false, "", false, true).buildView(interfaces, userContext, queryParams)
        })
        const mealWidgets: IBaseWidget[] = await Promise.all(mealWidgetPromises)
        const mealWidgetMap: {[id: string]: IBaseWidget} = _.keyBy(mealWidgets, w => (w as MealCardWidget).productId)

        categories?.forEach((c1: Category, index: number) => {
            const { subcategories, id: c1Id } = c1
            const categoryHeader: EatCategoryWidget = categoryWidgetMap[c1Id] as EatCategoryWidget
            if (index === 0 ) {
                categoryHeader.showTopDivider = false
            }
            finalWidgets.push(categoryHeader)
            subcategories?.forEach((c2: Subcategory) => {

                const { id: c2Id, name: c2Name } = c2
                let { items } = c2
                if (selectedFoodProduct && c1Id === selectedFoodProduct?.categoryId) {
                    items = _.sortBy(items, item => item.id === selectedFoodProduct?.productId ? 0 : 1)
                }
                if (c1Id !== c2Id) {
                    const c2Header = new TypographyWidget()
                    c2Header.widgetType = "TYPOGRAPHY_WIDGET"
                    c2Header.marginBottom = 10
                    c2Header.marginTop = 20
                    c2Header.backgroundColor = "#FFFFFF"
                    c2Header.data = [{
                        text: c2Name?.toUpperCase(),
                        fontColor: "#757575",
                        fontSize: 14
                    }]
                    c2Header.tagColor = undefined
                    finalWidgets.push(c2Header)
                }

                const outOfStockCards: MealCardWidget[] = []
                const noImageCards: MealCardWidget[] = []
                _.forEach(items, (i: SubcategoryItem) => {
                    const card = mealWidgetMap[i.id] as MealCardWidget
                    if (card.stock <= 0) {
                        if (_.isEmpty(card.image))
                            this.addDummyImage(card, isNewMealCardSupported)
                        outOfStockCards.push(card)
                    } else if (_.isEmpty(card.image)) {
                        this.addDummyImage(card, isNewMealCardSupported)
                        noImageCards.push(card)
                    } else {
                        finalWidgets.push(card)
                    }
                })
                finalWidgets.push(...noImageCards)
                finalWidgets.push(...outOfStockCards)

            })
        })
        sharedData["outletName"] = name // for setting the outletname in eatclp hook
        sharedData["outletId"] = outletId
        sharedData["outOfStockCount"] = outOfStockCount
        sharedData["inStockCount"] = inStockCount
        return finalWidgets
    }

    private addDummyImage(card: MealCardWidget, isNewMealCardSupported: boolean): void {
        const dummyImage = isNewMealCardSupported ? undefined : FoodMarketplaceUtil.getDummyMealCardImageUrl()
        card.image = dummyImage
        card.imageThumbnail = dummyImage
    }
    getMealActions(product: FoodProduct, stock: number, day: string, userContext: UserContext, outletDetails: OutletDetails, sidesAvailabilityMap: {[id: string]: Boolean }): (Action | MealAction)[] {
        const actions: (MealAction | Action)[] = []
        if (!AppUtil.isWeb(userContext) && !userContext.sessionInfo.isUserLoggedIn) {
            const action: Action = {
                actionType: "SHOW_ALERT_MODAL",
                title: "ADD",
                meta: {
                    title: "Login Required!",
                    subTitle: "Please login to continue",
                    actions: [{ actionType: "LOGOUT", title: "Login" }]
                }
            }
            actions.push(action)
            return actions
        }

        // setting addon/variants availability
        FoodMarketplaceUtil.setAddonVariantAvailabilityInProduct(product, sidesAvailabilityMap)

        const action = FoodMarketplaceUtil.getFoodCardAction(product, outletDetails.outletId, outletDetails.brandId, day, stock)
        actions.push(action)

        return actions
    }

    private async createCategoryWidgetMap(foodCategories: FoodCategory[]) {

        const categoryWidgetPromises = _.map(foodCategories, (category) => {
            return new EatCategoryWidget(category.name, category.categoryId).buildView(null, null, null)
        })
        const categoryWidgets = await Promise.all(categoryWidgetPromises)
        const categoryWidgetMap = _.mapKeys(categoryWidgets, (widget: IBaseWidget) => {
            const categoryWidget = <EatCategoryWidget>widget
            return categoryWidget.categoryId
        })
        return categoryWidgetMap
    }

    private getCalorieInfoForMealItem(product: Product): number {

        // todo: @Nisheet integrate calorie info once data is flowing
        return undefined
    }

    private buildNutritionProfileInfo(product: Product): FMMealNutritionProfile {
        // todo: @nisheet wire this
        return undefined
    }
}
