import { Container, inject } from "inversify"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import AuthMiddleware from "../auth/AuthMiddleware"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { BASE_TYPES, Logger } from "@curefit/base"
import * as _ from "lodash"
import { FLASH_CLIENT_TYPES, IMyGateService } from "@curefit/flash-client"
import { ErrorFactory } from "@curefit/error-client"
import { ErrorCodes } from "../error/ErrorCodes"

export function MyGateControllerFactory(kernel: Container) {
    @controller("/mygate", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class MyGateController {

        constructor(
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(FLASH_CLIENT_TYPES.MyGateService) private myGateService: IMyGateService,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        ) {
        }

        @httpPost("/approvalStatus")
        public async addUserApprovalStatus(request: any): Promise<any> {
            const userId: string = request.body.userId
            const status = request.body.status
            const orderId: string = request.body.orderId
            const source: string = request.body.source
            if (_.isEmpty(userId) || _.isEmpty(status) || _.isEmpty(orderId) || _.isEmpty(source)) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("incomplete body in mygate approval status").build()
            }
            return await this.myGateService.addUserApprovalStatus(userId, status, orderId, source)
        }
    }
}
