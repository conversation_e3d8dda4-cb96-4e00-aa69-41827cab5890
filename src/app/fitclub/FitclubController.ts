import { Container, inject } from "inversify"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import AuthMiddleware from "../auth/AuthMiddleware"
import kernel from "../../config/ioc/ioc"
import { BASE_TYPES, Logger } from "@curefit/base"
import * as express from "express"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { IUserService, USER_CLIENT_TYPES } from "@curefit/user-client"
import { ApiKeyConf, AUTH_MODELS_TYPES } from "@curefit/auth-models"
import { FITCLUB_CLIENT_TYPES, IFitClubService } from "@curefit/fitclub-client"
import WidgetBuilder from "../page/vm/WidgetBuilder"
import { FitclubMembershipSummaryPageView, Savings } from "./FitclubMembershipSummaryPageView"
import { Session, UserContext } from "@curefit/userinfo-common"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { IQuestService, QUEST_CLIENT_TYPES } from "@curefit/quest-client"
import { User } from "@curefit/user-common"
import { PaginatedUserRewards, RewardType, UserReward } from "@curefit/quest-common"
import { LedgerResponse } from "@curefit/fitclub-models"
import { MathUtil, TimeUtil } from "@curefit/util-common"
import {
    RewardClaimedWidget,
    RewardExpiredWidget,
    RewardUnclaimedWidget
} from "../common/views/RewardListingSummaryWidget"
import * as _ from "lodash"
import { CacheHelper } from "../util/CacheHelper"
import IssueBusiness from "../crm/IssueBusiness"
import { FitclubBusiness } from "./FitclubBusiness"
import { IRewardService, REWARD_CLIENT_TYPES } from "@curefit/reward-client"
import { LedgerEntry, Response, DownstreamResponse } from "@curefit/reward-common"
const LOG_PREFIX = "FitclubController"

const REWARD_TYPES = {
    CLAIMED: "claimed",
    UNCLAIMED: "unclaimed",
    EXPIRED: "expired"
}
const zeroRewardQuotes = [
    "The only reward today is the fitness level you achieved!",
    "But you have more stamina than when you started. That's today's reward!",
    "But you've earned our respect for showing up and pushing yourself.",
    "But you are one step closer to the new you. That's today's reward!",
    "But hey! nothing is more rewarding than the calories you burnt",
    "Flushed face, high energy, and burned calories. That's today's reward",
    "But look at you go! It's like you don't need fitcash for motivation ;)",
    "This time, the reward is the calories you burnt!",
    "Fitcash comes & goes. But fitness is forever, if you keep at it",
]

export function FitclubControllerFactory(kernal: Container) {

    @controller("/fitclub", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class FitclubController {
        constructor(
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(FITCLUB_CLIENT_TYPES.IFitClubService) private fitclubService: IFitClubService,
            @inject(REWARD_CLIENT_TYPES.IRewardService) private rewardService: IRewardService,
            @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
            @inject(CUREFIT_API_TYPES.WidgetBuilder) private VMWidgetBuilder: WidgetBuilder,
            @inject(QUEST_CLIENT_TYPES.IQuestService) private questService: IQuestService,
            @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
            @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness,
            @inject(AUTH_MODELS_TYPES.ApiKeyConfiguration) private apiKeysConfs: ApiKeyConf[],
            @inject(CUREFIT_API_TYPES.FitclubBusiness) private fitclubBusiness: FitclubBusiness
        ) {
        }

        @httpGet("/rewards/:rewardType")
        async getRewardsByRewardType(request: express.Request): Promise<any> {
            const session: Session = request.session
            const userContext = request.userContext as UserContext
            const userId = session.userId
            const rewardType = request.params.rewardType
            const pageNumber = (request.query.pageNumber !== undefined && request.query.pageNumber !== "undefined") ? Number(request.query.pageNumber) : 0
            const limit = (request.query.pageSize !== undefined && request.query.pageSize !== "undefined") ? Number(request.query.pageSize) : 20
            switch (rewardType) {
                case REWARD_TYPES.CLAIMED:
                    return this.getClaimedRewards(userId, userContext, pageNumber, limit)
                case REWARD_TYPES.UNCLAIMED:
                    return this.getUnclaimedRewards(userId, userContext, pageNumber, limit)
                case REWARD_TYPES.EXPIRED:
                    return this.getExpiredRewards(userId, userContext, pageNumber, limit)

            }
        }

        @httpPost("/redeemReward")
        public async redeemReward(req: express.Request): Promise<any> {
            const session: Session = req.session
            const userId: string = session.userId
            const rewardId: string = req.body.userRewardId
            const rewardRedeemResponse: UserReward = await this.questService.redeemReward({userId, rewardId})
            const user = await this.userCache.getUser(userId)
            if (rewardRedeemResponse.value > 0) {
                return {
                    successfulReward: rewardRedeemResponse.value > 0,
                    title: `Congrats, ${user.firstName} `,
                    // subtitle: user.firstName,
                    rewardContent: {
                        title: "You won",
                        priceText: rewardRedeemResponse.value,
                        subtitle: this.getTitleByRewardType(rewardRedeemResponse.rewardType)
                    }
                }
            } else {
                const selectedQuoteIndex = MathUtil.getRandomNumberInRange(0, zeroRewardQuotes.length - 1)
                return {
                    successfulReward: false,
                    title: `No fitcash today!`,
                    rewardContent: {
                        title: `${zeroRewardQuotes[selectedQuoteIndex]}`
                    }
                }
            }
        }

        private getTitleByRewardType(rewardType: RewardType): string {
            switch (rewardType) {
                case "FIT_CASH":
                    return "fitcash"
                case "CULT_CLASS_EXTENSION":
                    return "cult days"
            }
        }

        @httpGet("/membershipSummary")
        async getFitclubMembershipSummary(request: express.Request): Promise<FitclubMembershipSummaryPageView> {
            const userContext = request.userContext as UserContext
            const session: Session = request.session
            const userId = session.userId
            // todo(@hunny): make calls in parallel
            const activeMembershipSavings = <DownstreamResponse<Savings>>await this.rewardService.getUserSummary(userId)
            const {isMember, expiry} = await this.fitclubBusiness.getFitclubMembership(userId)
            const primaryUser: User = await userContext.userPromise
            const unclaimedRewardsReq = {
                userId: userId,
                redeemed: false,
                fromDate: TimeUtil.getMomentNow(userContext.userProfile.timezone).subtract(30, "days").toISOString(),
            }
            const unclaimedRewardsResponse: PaginatedUserRewards = await this.questService.getUserRewards(unclaimedRewardsReq)
            const expiredRewardsReq = {
                userId: userId,
                redeemed: false,
                toDate: TimeUtil.getMomentNow(userContext.userProfile.timezone).subtract(30, "days").toISOString(),
                skip: 0,
                limit: 3
            }
            const expiredRewardsResponse: PaginatedUserRewards = await this.questService.getUserRewards(expiredRewardsReq)
            const claimedRewards: { data: LedgerEntry<Response>[] } = await this.rewardService.getLedger(userId, {
                skip: 0,
                limit: 3
            })
            return new FitclubMembershipSummaryPageView().buildView(
                userContext,
                primaryUser,
                activeMembershipSavings,
                isMember,
                expiry,
                unclaimedRewardsResponse.results,
                claimedRewards.data,
                expiredRewardsResponse.results,
                this.issueBusiness,
                undefined
            )
        }

        private async getExpiredRewards(userId: string, userContext: UserContext, pageNumber: number, limit: number): Promise<any> {
            const expiredRewardsReq = {
                userId: userId,
                redeemed: false,
                toDate: TimeUtil.getMomentNow(userContext.userProfile.timezone).subtract(30, "days").toISOString(),
                skip: pageNumber * limit,
                limit: limit
            }
            const expiredRewardsResponse: PaginatedUserRewards = await this.questService.getUserRewards(expiredRewardsReq)
            const widgets: RewardExpiredWidget[] = _.map(expiredRewardsResponse.results, (reward) => {
                return new RewardExpiredWidget([reward])
            })
            return {
                widgets: widgets,
                pageNumber: pageNumber ? pageNumber + 1 : 1
            }
        }

        private async getUnclaimedRewards(userId: string, userContext: UserContext, pageNumber: number, limit: number): Promise<any> {
            const unclaimedRewardsReq = {
                userId: userId,
                redeemed: false,
                fromDate: TimeUtil.getMomentNow(userContext.userProfile.timezone).subtract(30, "days").toISOString(),
                skip: pageNumber * limit,
                limit: limit
            }

            const unclaimedRewardsResponse: PaginatedUserRewards = await this.questService.getUserRewards(unclaimedRewardsReq)
            const widgets: RewardUnclaimedWidget[] = _.map(unclaimedRewardsResponse.results, (item: UserReward) => {
                return new RewardUnclaimedWidget(item, userContext)
            })
            return {
                widgets: widgets,
                pageNumber: pageNumber ? pageNumber + 1 : 1
            }
        }

        private async getClaimedRewards(userId: string, userContext: UserContext, pageNumber: number, limit: number): Promise<any> {
            const claimedRewards: {data: LedgerEntry<Response>[] } = await this.rewardService.getLedger(userId, {
                skip: pageNumber * limit,
                limit: limit
            })
            const widgets: RewardClaimedWidget[] = _.isNil(claimedRewards.data) ? [] : await Promise.all(claimedRewards.data.map(async (reward) => {
                const issueParams = this.issueBusiness.getFitclubRewardIssueParams(reward)
                const reportIssues = await this.issueBusiness.getIssues(issueParams.productType, issueParams.productStates, issueParams.meta, userContext)
                return new RewardClaimedWidget([reward], userContext, reportIssues)
            }))
            return {
                widgets: widgets,
                pageNumber: pageNumber ? pageNumber + 1 : 1
            }
        }
    }

    return FitclubController
}

export default FitclubControllerFactory
