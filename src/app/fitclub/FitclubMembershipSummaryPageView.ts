import { UserContext } from "@curefit/userinfo-common"
import * as _ from "lodash"
import { ProductDetailPage, WidgetType } from "../common/views/WidgetView"
import * as momentTz from "moment-timezone"
import { BaseWidget, FitClubSavings } from "@curefit/vm-models"
import { Action } from "@curefit/apps-common"
import { DownstreamResponse, LedgerEntry, Response } from "@curefit/reward-common"
import { FitClubDeliverySavings } from "@curefit/fitclub-models"
import { CdnUtil, TimeUtil } from "@curefit/util-common"
import { User } from "@curefit/user-common"
import { ActionableCardItem, HorizontalActionableCardListingWidget } from "../page/PageWidgets"
import { RewardItem, RewardListingSummaryWidget, RewardWidgetHeader } from "../common/views/RewardListingSummaryWidget"
import { UserReward } from "@curefit/quest-common"
import { FitclubUtil } from "../util/FitclubUtil"
import IssueBusiness from "../crm/IssueBusiness"
import AppUtil from "../util/AppUtil"
import moment = require("moment")

export interface Savings {
    freeDeliveries: number,
    fitcash: number,
    cultExtension: number,
    offer: number
}

interface IFitClubActiveMembershipWidget extends BaseWidget {
    data?: FitClubSavings[]
    totalMembershipDays: number
    remainingMembershipDays: number
    expireText: string
    startDate: string
    endDate: string
    description: string
    icon: string
}

export class FitclubMembershipSummaryPageView extends ProductDetailPage {

    async buildView(
        userContext: UserContext,
        user: User,
        fitClubSavingsApiResponse: DownstreamResponse<Savings>,
        isFitclubMember: boolean,
        expiry: string,
        unclaimedRewards: UserReward[],
        claimedRewards: LedgerEntry<Response>[],
        expiredRewards: UserReward[],
        issueBusiness: IssueBusiness,
        actions: Action[]
    ): Promise<FitclubMembershipSummaryPageView> {
        if (AppUtil.isFitClubApplicable(userContext, isFitclubMember)) {
            this.widgets.push(this.getSummaryWidget(fitClubSavingsApiResponse, expiry, userContext))
        }
        // this.widgets.push(this.getRewardsWidget())
        if (!_.isNull(unclaimedRewards) && !_.isEmpty(unclaimedRewards)) {
            this.widgets.push(this.getUnclaimedRewardsWidget(unclaimedRewards, userContext))
        }
        // else {
        //     this.widgets.push(this.getActivitiesWidget(userContext, user))
        // }
        if (!_.isNull(claimedRewards) && !_.isEmpty(claimedRewards)) {
            this.widgets.push(await this.getClaimedRewardsWidget(claimedRewards, userContext, issueBusiness))
        }
        if (!_.isNull(expiredRewards) && !_.isEmpty(expiredRewards)) {
            this.widgets.push(this.getExpiredRewards())
        }
        // if (isFitclubMember) {
        //     this.widgets.push(this.getFaqWidget())
        // }
        if (actions) {
            this.actions = actions
        }
        return this
    }

    private getSummaryWidget(FitClubSavingsApiResponse: DownstreamResponse<Savings>, expiry: string, userContext: UserContext): IFitClubActiveMembershipWidget {
        const savings = FitClubSavingsApiResponse.data
        const freeDeliverySavings = savings.freeDeliveries
        const fitcashSavings = savings.fitcash
        const deliveryChargesSavings = savings.freeDeliveries * 25
        const tz = userContext.userProfile.timezone
        const remainingMembershipDays = TimeUtil.diffInDays(tz, expiry, TimeUtil.todaysDate(tz))
        const expireText = remainingMembershipDays < 3 ? `Expires in ${remainingMembershipDays} days` : "Valid till " + moment(expiry).format("Do MMM YYYY")
        const data: FitClubSavings[] = [
            {
                value: fitcashSavings ? fitcashSavings / 100 : 0,
                title: "FITCASH\nEARNED",
                savingType: "EARN_FITCASH"
            },
            {
                value: (freeDeliverySavings ? freeDeliverySavings : Math.round(deliveryChargesSavings ? deliveryChargesSavings : 0 / FitClubDeliverySavings)),
                title: "FREE\nDELIVERIES",
                savingType: "FREE_DELIVERY"
            }]
        return {
            widgetType: "FIT_CLUB_ACTIVE_MEMBERSHIP_WIDGET",
            data: data,
            dividerType: "LARGE",
            showDivider: true,
            startDate: "N/A",
            endDate: expiry,
            totalMembershipDays: 10,
            remainingMembershipDays: 9,
            expireText,
            description: "1 fitcash = ₹1 Off",
            buildView: undefined,
            validate: undefined,
            getTemplatePackIDFromUrl: undefined,
            getPackIdUrlFromTemplate: undefined,
            getPMSTemplateFromUrl: undefined,
            icon: CdnUtil.getCdnUrl("curefit-content/image/banners/fitcash_banner.png")
        }
    }

    private getUnclaimedRewardsWidget(unclaimedRewards: UserReward[], userContext: UserContext): HorizontalActionableCardListingWidget {
        if (unclaimedRewards.length < 1) {
            return null
        }
        const unclaimedItems: ActionableCardItem[] = _.map(unclaimedRewards, (reward) => {
            const subTitle = momentTz.tz(reward.date.date, reward.date.timezone).format("Do MMM")
            const diffTimeInDays = TimeUtil.diffInDays(
                userContext.userProfile.timezone,
                TimeUtil.getMomentForDate(reward.date.date, userContext.userProfile.timezone).toDate().toDateString(),
                TimeUtil.getDateNow(userContext.userProfile.timezone).toDateString())
            const expiringInDays = 30 - diffTimeInDays > 0 ? 30 - diffTimeInDays : 1 // 30 days hardcoded logic
            return {
                imageUrl: "/image/icons/fitclub/gift_icon.png", // s3 hardcoded url
                title: FitclubUtil.getUnclaimedRewardTitle(reward.idempotenceKey), // extracting from title=
                subTitle: subTitle, // extracting from created date
                description: `Expires in ${expiringInDays} days`,
                backgroundColor: "#552c47",
                cardAction: {
                    actionType: "CLAIM_FITCLUB_REWARD",
                    meta: {
                        userRewardId: reward.userRewardId
                    }
                }
            }
        })
        const unclaimedRewardsTitle = `Unclaimed Rewards`
        const widgetType: WidgetType = "HORIZONTAL_ACTIONAL_CARD_LISTING_WIDGET"
        const unclaimedRewardsWidget: HorizontalActionableCardListingWidget = {
            widgetType: widgetType,
            title: `Unbox daily rewards (${unclaimedRewards.length})`,
            type: "UNCLAIMED_REWARDS",
            backgroundColor: "#fdfdfd,#eef2f5",
            cardItems: unclaimedItems
        }
        if (unclaimedRewards.length > 2) {
            unclaimedRewardsWidget.header = {
                action: {
                    title: "VIEW ALL",
                    actionType: "NAVIGATION",
                    url: `curefit://rewardlistpage?rewardType=unclaimed&title=${unclaimedRewardsTitle}`
                }
            }
        }
        return unclaimedRewardsWidget
    }

    private async getClaimedRewardsWidget(claimedRewards: LedgerEntry<Response>[], userContext: UserContext, issueBusiness: IssueBusiness): Promise<RewardListingSummaryWidget> {
        if (claimedRewards.length < 1) {
            return null
        }
        const header: RewardWidgetHeader = {
            title: "Rewards Earned",
        }
        if (claimedRewards.length > 2) {
            header.action = {
                title: "VIEW ALL",
                actionType: "NAVIGATION",
                url: "curefit://rewardlistpage?rewardType=claimed&title=Rewards%20Earned"
            }
        }
        const claimedItems: RewardItem[] = await Promise.all(claimedRewards.map(async (reward) => {
            const issueParams = issueBusiness.getFitclubRewardIssueParams(reward)
            const reportIssues = await issueBusiness.getIssues(issueParams.productType, issueParams.productStates, issueParams.meta, userContext)
            return FitclubUtil.getClaimedRewardItem(reward, userContext.userProfile.timezone, reportIssues)
        }))
        return new RewardListingSummaryWidget(header, claimedItems, "LARGE", true)
    }

    private getExpiredRewards(): RewardListingSummaryWidget {
        const header = {
            title: "Expired Rewards",
            action: {
                title: "VIEW ALL",
                actionType: "NAVIGATION",
                url: "curefit://rewardlistpage?rewardType=expired&title=Expired%20Rewards"
            }
        }
        return new RewardListingSummaryWidget(header, [], "LARGE", true)
    }
}
