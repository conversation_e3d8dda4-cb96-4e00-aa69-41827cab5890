import { UserContext } from "@curefit/userinfo-common"
import { inject, injectable } from "inversify"
import AppUtil from "../util/AppUtil"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CacheHelper } from "../util/CacheHelper"
import { BASE_TYPES, Logger } from "@curefit/base"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { FITCLUB_CLIENT_TYPES, FitClubUtil, SnapshotService } from "@curefit/fitclub-client"

@injectable()
export class FitclubBusiness {
    constructor(
        @inject(CUREFIT_API_TYPES.CacheHelper) protected cacheHelper: CacheHelper,
        @inject(BASE_TYPES.ILogger) protected logger: Logger,
        @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
        @inject(FITCLUB_CLIENT_TYPES.FitclubSnapshot) private fitclubSnapshot: SnapshotService
    ) {
    }

    async checkIfUserIsEligibleForFitclubV2(userContext: UserContext): Promise<boolean> {
        const user = await userContext.userPromise
        const isInternalUser: boolean = user.isInternalUser
        return AppUtil.isFitclubV2Supported(userContext, isInternalUser)
    }

    static async isFitClubSupported(userContext: UserContext): Promise<boolean> {
        if (process.env.ENVIRONMENT === "STAGE" && userContext.sessionInfo.osName === "APP")
            return true
        return await FitClubUtil.isFitClubOffersEnabled({
            codePushVersion: userContext.sessionInfo.cpVersion,
            appVersion: userContext.sessionInfo.appVersion,
            osName: userContext.sessionInfo.osName,
            userId: userContext.userProfile.userId
        })
    }

    public async isFitclubMember(userId: string): Promise<boolean> {
        return (await this.getFitclubMembership(userId)).isMember
    }

    public async getFitclubMembership(userId: string): Promise<{isMember: boolean, expiry?: string}> {
        return await this.fitclubSnapshot.getFitclubMembership(userId)
    }
}
