import IEventInterventionMappingBusiness from "./IEventInterventionMappingBusiness"
import { IEventInterventionMapping } from "@curefit/vm-common"
import { inject, injectable } from "inversify"
import { IPageService, ISegmentService, REDIS_EVENT_INTERVENTION_MAPPING_KEY } from "@curefit/vm-models"
import * as _ from "lodash"
import { ICrudKeyValue, IMultiCrudKeyValue, REDIS_TYPES } from "@curefit/redis-utils"
import { BASE_TYPES, Logger } from "@curefit/base"
import { UserContext } from "@curefit/userinfo-common"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { AnnouncementBusiness } from "../announcement/AnnouncementBusiness"
import { PromiseCache } from "../util/VMUtil"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"

@injectable()
export class EventInterventionMappingBusiness implements IEventInterventionMappingBusiness {
    private redisCrudDao: ICrudKeyValue

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(REDIS_TYPES.MultiCrudKeyValueDao) private multiCrudKeyValueDao: IMultiCrudKeyValue,
        @inject(CUREFIT_API_TYPES.AnnouncementBusiness) public announcementBusiness: AnnouncementBusiness,
    ) {
        this.redisCrudDao = this.multiCrudKeyValueDao.getICrudKeyValue("VM-CACHE")
    }

    /**
     *
     * @param eventId
     * @param pageService
     * @param segmentService
     * @param userContext
     *
     * Make sure PromiseMapCache is set for Announcements, (future interventions) to work!
     */
    public async buildInterventionAction(eventId: string, pageService: IPageService, segmentService: ISegmentService, userContext: UserContext): Promise<any> {
        const mapping = await this.getEventMapping(eventId)
        if (_.isEmpty(mapping?.announcementIds)) return undefined
        const announcement = await this.announcementBusiness.getSingleActiveAnnouncement(pageService, segmentService, userContext, false, mapping.announcementIds)
        if (_.isEmpty(announcement)) return undefined
        return {
            actionType: "SHOW_DYNAMIC_INTERVENTION",
            meta: {
                announcementId: announcement.announcementId
            }
        }
    }

    async getEventMapping(eventId: string): Promise<IEventInterventionMapping> {
        let interventionString: string
        try {
            interventionString = await this.redisCrudDao.read(eventId, REDIS_EVENT_INTERVENTION_MAPPING_KEY)
        } catch (e) {
            this.logger.error("Error getting intervention from redis for eventId: " + eventId)
        }
        const intervention = <IEventInterventionMapping>JSON.parse(interventionString)
        if (_.isEmpty(intervention)) return undefined
        return intervention
    }

}
