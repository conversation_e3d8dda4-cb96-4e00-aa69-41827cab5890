import InterventionHand<PERSON>, { InterventionPayload, InterventionView } from "./InterventionHandler"
import { inject, injectable } from "inversify"
import { Logger, BASE_TYPES } from "@curefit/base"
import { IInterventionReadWriteDao, INTERVENTION_MODELS_TYPES } from "@curefit/intervention-models"
import { ErrorFactory, GenericError } from "@curefit/error-client"
import {
    Intervention,
    Interventions,
    InterventionStatus,
    MessageInterventionData
} from "@curefit/intervention-common"
import { UserContext } from "@curefit/userinfo-common"
import * as _ from "lodash"
import CultUtil from "../util/CultUtil"
import { TimeUtil } from "@curefit/util-common"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { IInterventionRedisCache } from "./InterventionRedisCache"
import { ErrorCodes } from "../error/ErrorCodes"

const crypto = require("crypto")

@injectable()
class MessageInterventionHandler implements InterventionHandler {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(INTERVENTION_MODELS_TYPES.InterventionReadWriteDao) private interventionDao: IInterventionReadWriteDao,
        @inject(CUREFIT_API_TYPES.InterventionRedisCache) private interventionRedisCache: IInterventionRedisCache,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
    ) {
    }

    async handleInterventionRequest(payload: InterventionPayload): Promise<boolean> {
        // create intervention data for Message Intervention and store in db
        const messageInterventionPayload = <MessageInterventionData>payload.interventionData
        const messageInterventionData: MessageInterventionData = {
            url: messageInterventionPayload.url,
            action: messageInterventionPayload.action
        }
        let interventionId = crypto.randomUUID()
        if (payload.interventionId) {
            const intervention = await this.interventionRedisCache.getInterventionById(payload.interventionId)
            if (intervention) {
                this.logger.info(`Intervention is already created with the given intervationId: ${intervention.interventionId}`)
                return true
            } else {
                interventionId = payload.interventionId
            }
        }
        let validFrom = TimeUtil.getDateNow(TimeUtil.IST_TIMEZONE)
        if (payload.validFrom) {
            validFrom = payload.validFrom
        }
        const date7DaysAfter = TimeUtil.addDays(TimeUtil.IST_TIMEZONE, TimeUtil.todaysDate(TimeUtil.IST_TIMEZONE), 7)
        let validTo = TimeUtil.getDate(date7DaysAfter, 0, 0, TimeUtil.IST_TIMEZONE)
        if (payload.validTo) {
            validTo = payload.validTo
        }

        const intervention: Intervention = {
            userId: payload.userId,
            interventionId: interventionId,
            type: payload.interventionType,
            status: "NOT_SHOWN",
            createdDate: new Date(),
            interventionData: messageInterventionData,
            validFrom: validFrom,
            validTo: validTo
        }
        try {
            return await this.interventionRedisCache.storeIntervention(intervention)
        }
        catch (error) {
            this.logger.error("Intervention creation error: " + error)
            return false
        }
    }

    async getWidgets(intervention: Intervention, userContext: UserContext): Promise<InterventionView> {
        throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("Not implemented for Message Intervention").build()
    }

    async getInterventionToShow(userId: string, userContext: UserContext): Promise<Intervention> {
        const tz = userContext.userProfile.timezone
        if (!CultUtil.isMessgaeInterventionSupported(userContext)) {
            return undefined
        }
        const currEpoch: number = TimeUtil.getDateNow(tz).getTime()
        const activeInterventions: Intervention[] = await this.interventionRedisCache.getAllActiveInterventionsForUser(userId)
        if (_.isEmpty(activeInterventions)) return undefined
        let chosenInterventionForDisplay: Intervention
        let chosenInterventionCreationEpoch: number = -Infinity
        activeInterventions.forEach((intervention: Intervention) => {
            const interventionCreationEpoch: number = intervention.createdDate.getTime()
            if (intervention.type === Interventions.MESSAGE_INTERVENTION
                && intervention.validFrom.getTime() <= currEpoch
                && currEpoch <= intervention.validTo.getTime()
            ) {
                if (chosenInterventionCreationEpoch < interventionCreationEpoch) {
                    chosenInterventionForDisplay = intervention
                    chosenInterventionCreationEpoch = interventionCreationEpoch
                }
            }
        })
        return chosenInterventionForDisplay
    }

    async handleSubmitInterventionResponse(userId: string, interventionId: string, response: any, status: InterventionStatus): Promise<boolean> {
        // handle submit action data and save in db
        const intervention: Intervention = await this.interventionRedisCache.getInterventionById(interventionId)
        if (!intervention) {
            const genericErr: GenericError = new GenericError({message: "No intervention found for the interventionId" + interventionId})
            genericErr.statusCode = 400
            return Promise.reject(genericErr)
        }
        else if (intervention.status === "NOT_SHOWN") {
            intervention.status = status
            await this.interventionDao.findOneAndUpdate({ interventionId: interventionId, userId: userId }, intervention, { upsert: true })
            await this.interventionRedisCache.purgeActiveInterventionForUser(interventionId, userId)
            return Promise.resolve(true)
        }
        return true
    }

}
export default MessageInterventionHandler
