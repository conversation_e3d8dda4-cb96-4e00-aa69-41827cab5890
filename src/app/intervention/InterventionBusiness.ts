import IInterventionBusiness from "./IInterventionBusiness"
import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { Intervention, InterventionType } from "@curefit/intervention-common"
import { Session } from "@curefit/userinfo-common"
import InterventionHandlerFactory from "./InterventionHandlerFactory"
import { UserContext } from "@curefit/userinfo-common"
import { IInterventionRedisCache } from "./InterventionRedisCache"
import { TimeUtil } from "@curefit/util-common"
import { IInterventionReadOnlyDao, INTERVENTION_MODELS_TYPES } from "@curefit/intervention-models"
import { Logger, BASE_TYPES } from "@curefit/base"

@injectable()
class InterventionBusiness implements IInterventionBusiness {

    constructor(
        @inject(CUREFIT_API_TYPES.InterventionHandlerFactory) private interventionHandlerFactory: InterventionHandlerFactory,
        @inject(CUREFIT_API_TYPES.InterventionRedisCache) private interventionRedisCache: IInterventionRedisCache,
        @inject(INTERVENTION_MODELS_TYPES.InterventionReadOnlyDao) private interventionDao: IInterventionReadOnlyDao,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
    ) {
    }

    async getInterventionToShow(session: Session, userContext: UserContext, userId: string): Promise<Intervention> {
        const interventionsByPriority: InterventionType[] = this.buildInterventionByPriority()
        // iterate over interventions map and get
        const promises: Promise<any>[] = []
        for (let i = 0; i < interventionsByPriority.length; i++) {
            const interventionType = interventionsByPriority[i]
            const promise = this.interventionHandlerFactory.getHandler(interventionType).getInterventionToShow(userId, userContext)
            promises.push(promise)
        }
        const result = await Promise.all(promises)
        this.purgeOldActiveInterventions(userId, userContext)
        for (let i = 0; i < interventionsByPriority.length; i++) {
            if (result[i]) {
                return result[i]
            }
        }
        return undefined
    }

    public async backfillInterventions(): Promise<number> {
        const currEpoch: number = TimeUtil.getCurrentEpoch()
        const fifteenDaysBeforeEpoch: number = currEpoch - TimeUtil.TIME_IN_MILLISECONDS.DAY * 15
        const recentInterventions: Intervention[] = await this.interventionDao.find({
            condition: {
                "createdDate": {
                    "$gte": fifteenDaysBeforeEpoch
                },
                "status": "NOT_SHOWN"
            }
        })
        const cacheStoragePromises: Promise<boolean>[] = []
        recentInterventions.forEach((intervention: Intervention) => {
            cacheStoragePromises.push(this.interventionRedisCache.storeIntervention(intervention))
        })
        this.logger.info(`Backfilling intervention cache. Entries: ${cacheStoragePromises.length}`)
        await Promise.all(cacheStoragePromises)
        return cacheStoragePromises.length
    }

    private buildInterventionByPriority(): InterventionType[] {
        return ["CULT_NO_SHOW", "MESSAGE_INTERVENTION", "TRIAL_CLASS_MISSED"]
    }

    private async purgeOldActiveInterventions(userId: string, userContext: UserContext): Promise<void> {
        const tz = userContext.userProfile.timezone
        const eightDaysBeforeEpoch: number = TimeUtil.getMomentNow(tz).subtract(8, "days").toDate().getTime()
        const activeInterventions: Intervention[] = await this.interventionRedisCache.getAllActiveInterventionsForUser(userId)
        const interventionClearancePromises: Promise<boolean>[] = []
        activeInterventions.forEach((intervention: Intervention) => {
            const interventionCreationEpoch: number = intervention.createdDate.getTime()
            if (interventionCreationEpoch < eightDaysBeforeEpoch) {
                interventionClearancePromises.push(this.interventionRedisCache.purgeActiveInterventionForUser(intervention.interventionId, userId))
            }
        })
        await interventionClearancePromises
    }
}
export default InterventionBusiness
