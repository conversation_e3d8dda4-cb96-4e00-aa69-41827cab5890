import { injectable, inject } from "inversify"
import { <PERSON><PERSON><PERSON>, BASE_TYPES } from "@curefit/base"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>Value, REDIS_TYPES } from "@curefit/redis-utils"
import * as _ from "lodash"
import { TimeUtil } from "@curefit/util-common"
import { Intervention } from "@curefit/intervention-common"

const ACTIVE_INTERVENTIONS_REDIS_KEY: string = "activeInterventions"
const INTERVENTION_DATA_STORE_REDIS_KEY: string = "interventionStore"

export interface IInterventionRedisCache {
    getAllActiveInterventionsForUser(userId: string): Promise<Intervention[]>
    storeIntervention(intervention: Intervention): Promise<boolean>
    purgeAllInterventionsForUser(userId: string): Promise<boolean>
    purgeActiveInterventionForUser(interventionId: string, userId: string): Promise<boolean>
    getInterventionById(interventionId: string): Promise<Intervention>
}

// MAX_INTERVENTION_REDIS_TTL_SECONDS is set to 15 days as of now because as per current logic only those interventions
// which are less than 2 weeks old can be shown to a user.
const MAX_INTERVENTION_REDIS_TTL_SECONDS: number = TimeUtil.TIME_IN_SECONDS.DAY * 15

/**
 * @description There are 2 different caches being maintained for interventions.
 * 1. intervention-store cache: Stores the entire intervention details. Any intervention can be retrieved via its ID.
 * 2. user-intervention cache (active interventions): Stores the intervention IDs in a hash set against a user ID. These are the active
 *    interventions which are possible candidates for being shown to the user.
 */
@injectable()
export class InterventionRedisCache implements IInterventionRedisCache {

    constructor(
        @inject(REDIS_TYPES.RedisDao) private redisDao: ICrudKeyValue,
        @inject(BASE_TYPES.ILogger) private logger: ILogger
    ) {
    }

    /**
     * @description All possible candidates of interventions which can be shown to the user are returned from this function
     * It first checks in the user-intervention cache to get the list of possible interventionId.
     * Then it hits the intervention-store cache to get the actual content of the intervention.
     */
    public async getAllActiveInterventionsForUser(userId: string): Promise<Intervention[]> {
        const activeInterventionIdList: string[] = await this.redisDao.getMembersOfSet(this.geActiveInterventionsTokenKey(userId))
        if (_.isEmpty(activeInterventionIdList)) return []
        const activeInterventionList: Intervention[] = await this.retrieveMultipleInterventions(activeInterventionIdList)
        return activeInterventionList
    }

    /**
     * @description Stores the intervention in the intervention-store cache. Also stores the interventionId in the
     * user-intervention cache
     */
    public async storeIntervention(intervention: Intervention): Promise<boolean> {
        this.logger.info(`Storing Intervention in Redis`, { intervention: intervention })
        const redisStoragePromises: Promise<any>[] = [
            this.redisDao.upsertWithExpiry(
                this.getInterventionDataStoreTokenKey(intervention.interventionId),
                JSON.stringify(intervention),
                MAX_INTERVENTION_REDIS_TTL_SECONDS
            ),
            this.redisDao.addToSet(
                this.geActiveInterventionsTokenKey(intervention.userId),
                intervention.interventionId
            )
        ]
        await Promise.all(redisStoragePromises)
        this.redisDao.setExpiry(this.geActiveInterventionsTokenKey(intervention.userId), MAX_INTERVENTION_REDIS_TTL_SECONDS)
        return true
    }

    /**
     * @description Removes all entries from the user-intervention cache for a particular user
     */
    public async purgeAllInterventionsForUser(userId: string): Promise<boolean> {
        this.logger.info(`Purging all Interventions in Redis for user Id: ${userId}`)
        return this.redisDao.delete(
            this.geActiveInterventionsTokenKey(userId)
        )
    }

    /**
     * @description Removes a single interventionId from a user's user-intervention-cache
     */
    public async purgeActiveInterventionForUser(interventionId: string, userId: string): Promise<boolean> {
        this.logger.info(`Removing intervention in Redis for user Id: ${userId}, interventionId: ${interventionId}`)
        return this.redisDao.removeFromSet(
            this.geActiveInterventionsTokenKey(userId),
            interventionId
        )
    }

    /**
     * @description Gets the entire intervention details from the intervention-store cache
     */
    public async getInterventionById(interventionId: string): Promise<Intervention> {
        const interventionStr: string = await this.redisDao.read(this.getInterventionDataStoreTokenKey(interventionId))
        if (!_.isString(interventionStr)) {
            return undefined
        }
        return this.parseInterventionJsonString(interventionStr)
    }

    private async retrieveMultipleInterventions(interventionIdList: string[]): Promise<Intervention[]> {
        const interventionPromises: Promise<Intervention>[] = []
        interventionIdList.forEach((interventionId: string) => {
            interventionPromises.push(this.getInterventionById(interventionId))
        })
        await Promise.all(interventionPromises)
        const result: Intervention[] = []
        for (let i = 0; i < interventionPromises.length; i++) {
            const intervention: Intervention = await interventionPromises[i]
            if (!_.isNil(intervention)) result.push(intervention)
        }
        return result
    }

    private geActiveInterventionsTokenKey(userId: string): string {
        return `${ACTIVE_INTERVENTIONS_REDIS_KEY}:${userId}`
    }

    private getInterventionDataStoreTokenKey(InterventionId: string): string {
        return `${INTERVENTION_DATA_STORE_REDIS_KEY}:${InterventionId}`
    }

    private parseInterventionJsonString(interventionJsonStr: string): Intervention {
        if (_.isEmpty(interventionJsonStr)) return undefined
        const intervention: Intervention = <Intervention>JSON.parse(interventionJsonStr)
        // JSON.parse will not parse dates. Hence forcefully converting string to Date
        if (!_.isNil(intervention.createdDate)) intervention.createdDate = new Date(intervention.createdDate)
        if (!_.isNil(intervention.validFrom)) intervention.validFrom = new Date(intervention.validFrom)
        if (!_.isNil(intervention.validTo)) intervention.validTo = new Date(intervention.validTo)
        return intervention
    }
}