export enum TRIAL_CLASS_MISSED_ENUM {
    FORGOT_CLASS = 1,
    FORGOT_TO_MARK_ATTENDANCE = 2,
    NOT_WORKING = 3,
    FORGOT_TO_CANCEL = 4
}

export enum CLASS_NOT_ATTENDED {
    FORGOT_CLASS = TRIAL_CLASS_MISSED_ENUM.FORGOT_CLASS,
    NOT_WORKING = TRIAL_CLASS_MISSED_ENUM.NOT_WORKING,
    FORGOT_TO_CANCEL = TRIAL_CLASS_MISSED_ENUM.FORGOT_TO_CANCEL
}

import {
    Payload
} from "@curefit/intervention-common"
import { Action } from "@curefit/vm-models"

export const TRIAL_CLASS_MISSED_OPTIONS = [
    {
        text: "Forgot to mark attendance",
        id: TRIAL_CLASS_MISSED_ENUM.FORGOT_TO_MARK_ATTENDANCE
    }
    ,
    {
        text: "Forgot about this class",
        id: TRIAL_CLASS_MISSED_ENUM.FORGOT_CLASS
    },
    {
        text: "Didn't feel like working out",
        id: TRIAL_CLASS_MISSED_ENUM.NOT_WORKING
    },
    {
        text: "Couldn't attend due to personal reasons",
        id: TRIAL_CLASS_MISSED_ENUM.FORGOT_TO_CANCEL
    }
]

const HIDE_TRIAL_CLASS_MISSED_RESPONSE_MODAL: Action = {
    actionType: "HIDE_TRIAL_CLASS_BOOK_MODAL", meta: { custom: true }
}

export const ATTENDANCE_MARKED_PAYLOAD: Payload = {
    title: "We've marked your attendance as a one-time exception",
    subTitle: "Please note",
    options: [
        {
            text: "Please complete your biometric registration with the help of Center Manager",
            icon: "TOUCH_ICON"
        },
        {
            text: "Mark your attendance. Not marking your attendance leads to a No-show",
            icon: "FINGERPRINT_ICON"
        },
        {
            text: "If you are unable to attend a class, please cancel upto 60 mins in advance",
            icon: "TIME_ICON"
        }
    ],
    submitText: "GOT IT",
    action: HIDE_TRIAL_CLASS_MISSED_RESPONSE_MODAL,
    closeButton: {
        icon: "CLOSE_ICON",
        action: HIDE_TRIAL_CLASS_MISSED_RESPONSE_MODAL
    }
}

export const getExtraClassPayload = (productType: string = "FITNESS"): Payload => {
    const payload: Payload = {
        title: "We missed you on the workout floor. As a one-time exception,",
        subTitle: "Here's an",
        topIcon: "SMILE_EMOJI",
        content: {
            leftIcon: "LEFT_ICON",
            rightIcon: "RIGHT_ICON",
            heading: "Here's an",
            text: "EXTRA\nFREE TRIAL",
        },
        submitText: "Yay! Book a Class Now",
        action: [
            HIDE_TRIAL_CLASS_MISSED_RESPONSE_MODAL
            , {
                actionType: "NAVIGATION",
                title: "Book",
                url: `curefit://classbookingv2?productType=${productType}&pageFrom=bookClass`,
            }],
        closeButton: {
            icon: "CLOSE_ICON",
            action: HIDE_TRIAL_CLASS_MISSED_RESPONSE_MODAL
        }
    }
    return payload
}
