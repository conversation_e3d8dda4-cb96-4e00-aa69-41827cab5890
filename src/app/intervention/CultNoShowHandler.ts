import InterventionHand<PERSON>, { InterventionPayload, InterventionView } from "./InterventionHandler"
import { inject, injectable } from "inversify"
import {
    CultNoShowData,
    Intervention,
    Interventions,
    InterventionStatus,
    InterventionSubmitPayload,
    InterventionType,
    Payload
} from "@curefit/intervention-common"
import {
    Action,
    ActionCardWidget,
    ColorCodedDescriptionWidget,
    Header,
    InfoCard,
    InfoWidget,
    ManageOptions,
    ManageOptionsWidget,
    ProductListWidget,
    PulseRentalRemainingWidget,
    TrialClassMissedWidget,
    WidgetView
} from "../common/views/WidgetView"
import { CultBooking, CultBookingResponse, CultClass, ITrialClases, UserRemainingAccess } from "@curefit/cult-common"
import {
    CULT_CLIENT_TYPES,
    ICultService as ICultServiceNew,
    ICultServiceOld as ICultService,
    IPulseService
} from "@curefit/cult-client"
import { TimeUtil } from "@curefit/util-common"
import { BASE_TYPES, Logger } from "@curefit/base"
import { IInterventionReadWriteDao, INTERVENTION_MODELS_TYPES } from "@curefit/intervention-models"
import { GenericError } from "@curefit/error-client"
import { UserContext } from "@curefit/userinfo-common"
import * as momentTz from "moment-timezone"
import { RUPEE_SYMBOL } from "@curefit/finance-common"
import { CacheHelper } from "../util/CacheHelper"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import * as _ from "lodash"
import CultUtil from "../util/CultUtil"
import {
    ATTENDANCE_MARKED_PAYLOAD,
    CLASS_NOT_ATTENDED,
    getExtraClassPayload,
    TRIAL_CLASS_MISSED_OPTIONS
} from "./InterventionConstants"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import AppUtil, { MAX_TRIAL_CLASS_ALLOCATED, TRIAL_CLASS_MISSED_HAMLET_ID } from "../util/AppUtil"
import { CFUserProfile } from "../page/vm/CFUserProfile"
import { IInterventionRedisCache } from "./InterventionRedisCache"
import { ISportsApi, SPORT_API_CLIENT_TYPES } from "@curefit/sports-api-client-node"
import { PlaySessionDetails } from "@curefit/sports-api-common"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import { Membership } from "@curefit/membership-commons"
import PlayUtil from "../util/PlayUtil"


export class CultNoShowView implements InterventionView {
    public interventionType: InterventionType = "CULT_NO_SHOW"
    public widgets: WidgetView[] = []
    public actions: Action[] = []
    constructor(userContext: UserContext, public interventionId: string, noShowData: CultNoShowData, cultBooking: CultBooking, pulseAccess?: UserRemainingAccess) {
        const isNewNoShowSupported = AppUtil.isNewNoShowInterventionSupported(userContext)
        this.interventionId = interventionId
        this.widgets.push(this.buildTaggedDescriptionWidget(noShowData, cultBooking, userContext))
        if (noShowData.type === "PENALTY") {
            if (noShowData.isSubscription) {
                this.widgets.push(this.buildPenaltyInfoWidget(noShowData, cultBooking, isNewNoShowSupported))
            } else if (!_.isEmpty(cultBooking.Membership)) {
                this.widgets.push(this.buildMembershipEndDateInfoWidget(noShowData, cultBooking, userContext))
                // if user has pulse pack and it's not an unlimited pack
                if (pulseAccess && !pulseAccess.isUnlimitedPack) {
                    this.widgets.push(new PulseRentalRemainingWidget("PULSE Rental Not Used", "Remaining: ", pulseAccess.remainingAccess, "rgba(102, 102, 102, 1.0)"))
                }
            }
        }
        if (noShowData.type === "DROPOUT_SUCCESS") {
            this.widgets.push(this.buildClassInfoWidget(cultBooking, userContext))
        }
        else {
            this.widgets.push(this.buildNotesWidget(noShowData, cultBooking))
        }
        // since old apps by default disable action button until an option is submitted
        if (isNewNoShowSupported) {
            this.actions.push({
                title: "Got it",
                actionType: "SUBMIT_INTERVENTION"
            })
        }
    }
    buildTaggedDescriptionWidget(noShowData: CultNoShowData, cultBooking: CultBooking, userContext: UserContext): ColorCodedDescriptionWidget {
        const tz = userContext.userProfile.timezone
        const dayText: string = TimeUtil.getDayText(cultBooking.Class.date, tz).toLowerCase()
        const classTime: string = TimeUtil.formatDateStringInTimeZone(cultBooking.Class.date + " " + cultBooking.Class.startTime, tz, "h:mm A")
        const className = CultUtil.pulsifyClassName(cultBooking.CultClass, userContext)
        if (noShowData.type === "DROPOUT_SUCCESS") {
            return {
                widgetType: "COLOR_CODED_DESCRIPTION_WIDGET",
                descriptions: [{
                    title: `Dropout Successful.\nNo show waived off.`,
                    subTitle: "As such this was a no-show but since the slot you dropped out of was used, we’re waiving off the No-show.",
                    tag: {
                        text: "YOU GOT LUCKY",
                        color: "#5bdbb6",
                        textColor: "#000000"
                    }
                }]
            }
        } else if (noShowData.type === "WARNING") {
            let subTitle = noShowData.isDroppedBooking ? "You opted for ‘Last minute dropout’, but the slot couldn't be used by another member.\n" : ""
            subTitle += noShowData.isSubscription ? "As such, this is a no-show. But we understand and as an exception, no-show charges has not been added to your next month’s bill this time." : "As such, this is a no-show. But we understand and as an exception, your membership duration has not been reduced this time."
            return {
                widgetType: "COLOR_CODED_DESCRIPTION_WIDGET",
                descriptions: [{
                    title: `We missed you at ${className} class ${dayText}`,
                    subTitle: subTitle,
                    tag: {
                        text: noShowData.isDroppedBooking ? "DROPOUT UNSUCCESSFUL" : "ALERT",
                        color: "#e05303"
                    }
                }]
            }
        } else {
            const subTitle = noShowData.isDroppedBooking ? "You opted for ‘Last minute dropout’, but the slot couldn't be used by another member.\n" : `We missed you at ${className} class ${dayText} at ${classTime}`
            const title = noShowData.isSubscription ? `Uh oh! Rs.${noShowData.penaltyAmount} has been added to next month’s charges due to no-show` : "Uh oh! 1 day of membership reduced due to no-show"
            return {
                widgetType: "COLOR_CODED_DESCRIPTION_WIDGET",
                descriptions: [{
                    title: title,
                    subTitle,
                    tag: {
                        text: noShowData.isDroppedBooking ? "DROPOUT UNSUCCESSFUL" : "NOTE",
                        color: "#e05303"
                    }
                }]
            }
        }
    }

    buildNotesWidget(noShowData: CultNoShowData, cultBooking: CultBooking): ProductListWidget {
        const header: Header = {
            title: noShowData.type === "WARNING" ? "Please note" : "How to avoid a no-show"
        }
        const infoCards: InfoCard[] = []
        const attendanceNote = this.getAttendanceNote(noShowData, cultBooking)
        if (!noShowData.isDroppedBooking && !_.isEmpty(attendanceNote)) {
            infoCards.push({
                subTitle: attendanceNote,
                icon: "/image/icons/howItWorks/hitButton_1.png"
            })
        }
        infoCards.push({
            subTitle: "If you are unable to attend a class, please cancel upto 60 mins in advance",
            icon: "/image/icons/howItWorks/today_3.png"
        })
        const widget = new ProductListWidget("SMALL", header, infoCards)
        widget.hideSepratorLines = true
        return widget
    }

    getAttendanceNote(noShowData: CultNoShowData, cultBooking: CultBooking) {
        if (cultBooking.info.signInMedium === "OTP") {
            return "Use the last 4 digits of your phone number to sign in before every class"
        } else if (cultBooking.info.signInMedium === "BIOMETRIC" && cultBooking.info.isBiometricsRegistered) {
            return "Mark your attendance. We will remind you in case you forget"
        } else if (CultUtil.isCustomerBiometricSupportedInCenter(cultBooking.Center)) {
            return "Meet the Center Manager to complete your biometric registration"
        }
    }

    buildPenaltyInfoWidget(noShowData: CultNoShowData, cultBooking: CultBooking, isNewNoShowSupported: boolean): InfoWidget {
        const penaltyAmount = noShowData.penaltyAmount
        return {
            widgetType: "INFO_WIDGET",
            icon: isNewNoShowSupported ? "NO_DATE" : "DATE",
            title: `No show Penalty      ${RUPEE_SYMBOL} ${penaltyAmount}`,
            subTitle: "Added to your next month’s charges"
        }
    }


    buildMembershipEndDateInfoWidget(noShowData: CultNoShowData, cultBooking: CultBooking, userContext: UserContext): ManageOptionsWidget {
        const endDate = TimeUtil.formatDateStringInTimeZone(cultBooking.Membership.endDate, userContext.userProfile.timezone, "D MMM YYYY")
        const manageOptions: ManageOptions = {
            displayText: `Membership now ends on:`,
            helperText: `${endDate}`,
            icon: "DATE",
            options: []
        }
        return new ManageOptionsWidget(manageOptions, undefined, true)
    }

    buildClassInfoWidget(cultBooking: CultBooking, userContext: UserContext): ActionCardWidget {
        const classTime: string = momentTz.tz(cultBooking.Class.date + " " + cultBooking.Class.startTime,
            userContext.userProfile.timezone).format("h:mm A")
        return {
            widgetType: "ACTION_CARD_WIDGET",
            displayText: `${CultUtil.pulsifyClassName(cultBooking.CultClass, userContext)} at ${cultBooking.Center.name}`,
            meta: {
                text: classTime
            },
            icon: {
                iconType: "TEXT"
            }
        }
    }
}

export class PlayNoShowView implements  InterventionView {
    public interventionType: InterventionType = "PLAY_NO_SHOW"
    public widgets: WidgetView[] = []
    public actions: Action[] = []
    public hideDivider: boolean = false

    constructor(userContext: UserContext, public interventionId: string, noShowData: CultNoShowData, playBooking: PlaySessionDetails, playMembership: Membership) {
        this.interventionId = interventionId

        const isPlayNoShowPolicyV2 = PlayUtil.isPlayNoShowPolicyV2Supported(userContext)

        if (isPlayNoShowPolicyV2 && noShowData.bookingStartTime != null && noShowData.workoutName != null && noShowData.noShowCount != null) {
            this.widgets.push(this.buildTaggedDescriptionWidgetV2(noShowData, userContext))
        } else {
            this.widgets.push(this.buildTaggedDescriptionWidget(noShowData, playBooking, userContext))
        }
        if (noShowData.type === "PENALTY") {
            if (isPlayNoShowPolicyV2 && noShowData.bookingStartTime != null && noShowData.workoutName != null && noShowData.noShowCount != null) {
                this.widgets.push(this.buildNoShowPolicyWidget())
            } else if (!_.isEmpty(playMembership)) {
                this.widgets.push(this.buildMembershipEndDateInfoWidget(noShowData, playMembership, userContext))
            }
        }

        if (noShowData.type === "DROPOUT_SUCCESS") {
            this.widgets.push(this.buildClassInfoWidget(playBooking, userContext))
        } else {
            if (isPlayNoShowPolicyV2 && noShowData.bookingStartTime != null && noShowData.workoutName != null && noShowData.noShowCount != null) {
                this.widgets.push(this.buildNotesWidgetV2(noShowData))
            } else {
                this.widgets.push(this.buildNotesWidget(noShowData))
            }
        }

        // since old apps by default disable action button until an option is submitted
        this.actions.push({
            title: "Got it",
            actionType: "SUBMIT_INTERVENTION"
        })

        if (isPlayNoShowPolicyV2 && noShowData.bookingStartTime != null && noShowData.workoutName != null && noShowData.noShowCount != null) {
            this.hideDivider = true
        }
    }

    buildTaggedDescriptionWidget(noShowData: CultNoShowData, playBooking: PlaySessionDetails, userContext: UserContext): ColorCodedDescriptionWidget {
        const tz = userContext.userProfile.timezone
        const dayText: string = TimeUtil.getDayText(playBooking.date, tz).toLowerCase()
        const startTimes = playBooking.startTime.split(":")
        const startTimeHourMin = { hour: parseInt(startTimes[0]), min: parseInt(startTimes[1]) }
        const localStartTimeHourMin = TimeUtil.addHourMin(startTimeHourMin, 330)
        const classStartTime = TimeUtil.formatHourMin(localStartTimeHourMin) + ":00"

        const classTime: string = TimeUtil.formatDateStringInTimeZone(playBooking.date + " " + classStartTime, tz, "h:mm A")
        const className: string = playBooking.workout

        if (noShowData.type === "DROPOUT_SUCCESS") {
            return {
                widgetType: "COLOR_CODED_DESCRIPTION_WIDGET",
                descriptions: [{
                    title: `Dropout Successful.\nNo show waived off.`,
                    subTitle: "As such this was a no-show but since the slot you dropped out of was used, we’re waiving off the No-show.",
                    tag: {
                        text: "YOU GOT LUCKY",
                        color: "#5bdbb6",
                        textColor: "#000000"
                    }
                }]
            }
        }

        const subTitle =  noShowData.isDroppedBooking ? "You opted for ‘Last minute dropout’, but the slot couldn't be used by another member.\n" : `We missed you at ${className} class ${dayText} at ${classTime}`
        const title = "Uh oh! 1 day of membership reduced due to no-show"
        return {
            widgetType: "COLOR_CODED_DESCRIPTION_WIDGET",
            descriptions: [{
                title: title,
                subTitle,
                tag: {
                    text: noShowData.isDroppedBooking ? "DROPOUT UNSUCCESSFUL" : "NOTE",
                    color: "#e05303"
                }
            }]
        }
    }

    buildTaggedDescriptionWidgetV2(noShowData: CultNoShowData, userContext: UserContext): ColorCodedDescriptionWidget {
        const tz = userContext.userProfile.timezone
        const dateString: string = TimeUtil.formatEpochInTimeZoneDateFns(tz, noShowData.bookingStartTime)
        const dayText: string = TimeUtil.getDayText(dateString, tz).toLowerCase()

        const classTime: string = TimeUtil.formatEpochInTimeZoneDateFns(tz, noShowData.bookingStartTime, "h:mm a")
        const className: string = noShowData.workoutName

        if (noShowData.type === "DROPOUT_SUCCESS") {
            return {
                widgetType: "COLOR_CODED_DESCRIPTION_WIDGET",
                descriptions: [{
                    title: `Dropout Successful.\nNo show waived off.`,
                    subTitle: "As such this was a no-show but since the slot you dropped out of was used, we’re waiving off the No-show.",
                    tag: {
                        text: "YOU GOT LUCKY",
                        color: "#5bdbb6",
                        textColor: "#000000"
                    }
                }]
            }
        }

        const subTitle =  noShowData.isDroppedBooking ? "You opted for ‘Last minute dropout’, but the slot couldn't be used by another member.\n" : `We missed you at ${className} class ${dayText} at ${classTime}`

        const extraText: string = noShowData.noShowCount > 2 ? "" : " 1 day reduced from membership"
        const noShowOrdinal: string = PlayUtil.getNumberOrdinal(noShowData.noShowCount)
        const title = `Uh oh! ${noShowOrdinal} no show of this month.` + extraText

        const blockedDatesFormatted: string[] = []
        for (let i = 0; i < noShowData.bookingBlockedDates.length; i++) {
            const dateMoment: string = TimeUtil.getMomentForDateString(noShowData.bookingBlockedDates[i], tz).format("MMM DD")
            blockedDatesFormatted.push(dateMoment)
        }

        let blockedDatesString: string = ""
        for (let i = 0; i < blockedDatesFormatted.length; i++) {
            if (i == blockedDatesFormatted.length - 1 && i > 0) {
                blockedDatesString += " and "
            } else if (i > 0) {
                blockedDatesString += ", "
            }

            blockedDatesString += blockedDatesFormatted[i]
        }
        const highlightedTitle: string = noShowData.noShowCount > 2 ? (`Booking will be blocked on ` + blockedDatesString) : undefined
        return {
            widgetType: "COLOR_CODED_DESCRIPTION_WIDGET",
            descriptions: [{
                title: title,
                subTitle,
                highlightedTitle,
                tag: {
                    text: noShowData.isDroppedBooking ? "DROPOUT UNSUCCESSFUL" : "NOTE",
                    color: "#e05303"
                },
                titleStyle: {
                    fontSize: 16,
                    marginTop: 10,
                },
                highlightedTitleStyle: {
                    fontSize: 16
                }
            }]
        }
    }

    buildNoShowPolicyWidget(): ColorCodedDescriptionWidget {
        return {
            widgetType: "COLOR_CODED_DESCRIPTION_WIDGET",
            descriptions: [{
                title: "From 3rd no show of the month, bookings will be blocked for the next 2 days.",
                subTitle: "No show count will be reset, end of every month",
                topTitle: "NO SHOW POLICY",
                titleStyle: {
                    fontSize: 16,
                    marginTop: 5,
                },
            }]
        }
    }

    buildNotesWidget(noShowData: CultNoShowData): ProductListWidget {
        const noShowTitle: string = "How to avoid a no-show"

        const header: Header = {
            title: noShowData.type === "WARNING" ? "Please note" : noShowTitle,
        }
        const infoCards: InfoCard[] = []
        infoCards.push({
            subTitle: "If you are unable to attend a class, please cancel upto 60 mins in advance",
            icon: "/image/icons/howItWorks/today_3.png",
        })
        const widget = new ProductListWidget("SMALL", header, infoCards)
        widget.hideSepratorLines = true
        return widget
    }

    buildNotesWidgetV2(noShowData: CultNoShowData): ProductListWidget {
        const noShowTitle: string = "HOW TO AVOID A NO-SHOW"

        const header: Header = {
            title: noShowData.type === "WARNING" ? "Please note" : noShowTitle,
            style: {
                color: "#00000099",
                fontSize: 14,
                paddingLeft: 25,
            }
        }
        const infoCards: InfoCard[] = []
        infoCards.push({
            subTitle: "If you are unable to attend a class, please cancel upto 60 mins in advance",
            icon: "/image/icons/howItWorks/today_3.png",
            layoutProps: {
                iconStyle: {
                    height: 30,
                    width: 30,
                    alignItems: "flex-start"
                }
            },
            subtitleStyle: {
                maxWidth: 400,
            },
            cellStyle: {
                paddingTop: 0,
                alignItems: "flex-start"
            }
        })
        const widget = new ProductListWidget("SMALL", header, infoCards)
        widget.hideSepratorLines = true
        return widget
    }

    buildClassInfoWidget(playBooking: PlaySessionDetails, userContext: UserContext): ActionCardWidget {
        const tz = userContext.userProfile.timezone
        const startTimes = playBooking.startTime.split(":")
        const startTimeHourMin = { hour: parseInt(startTimes[0]), min: parseInt(startTimes[1]) }
        const localStartTimeHourMin = TimeUtil.addHourMin(startTimeHourMin, 330)
        const classStartTime = TimeUtil.formatHourMin(localStartTimeHourMin) + ":00"

        const classTime: string = TimeUtil.formatDateStringInTimeZone(playBooking.date + " " + classStartTime, tz, "h:mm A")
        const className: string = playBooking.workout

        return {
            widgetType: "ACTION_CARD_WIDGET",
            displayText: `${className} at ${playBooking.center}`,
            meta: {
                text: classTime
            },
            icon: {
                iconType: "TEXT"
            }
        }
    }

    buildMembershipEndDateInfoWidget(noShowData: CultNoShowData, playMembership: Membership, userContext: UserContext): ManageOptionsWidget {
        const endDate = TimeUtil.formatEpochInTimeZoneDateFns(userContext.userProfile.timezone, playMembership.end, "dd MMM yyyy")
        const manageOptions: ManageOptions = {
            displayText: `Membership now ends on:`,
            helperText: `${endDate}`,
            icon: "DATE",
            options: []
        }
        return new ManageOptionsWidget(manageOptions, undefined, true)
    }


}

export class CultTrailClassMissedView implements InterventionView {

    public interventionType: InterventionType = "TRIAL_CLASS_MISSED"
    public widgets: WidgetView[] = []
    public actions: Action[] = []
    constructor(userContext: UserContext, public interventionId: string, noShowData: CultNoShowData, cultBooking: CultBooking, ) {
        this.interventionId = interventionId
        // create widget trial class missed widget
        this.widgets.push(this.trialClassMissedWidget(cultBooking.CultClass, userContext))
        // since old apps by default disable action button until an option is submitted
        this.actions.push({
            title: "SUBMIT",
            actionType: "SUBMIT_INTERVENTION",
            rightIconType: "RIGHT_ARROW_WHITE",
            imageContainerStyle: { height: 15, width: 15 },
        })
    }

    trialClassMissedWidget(cultClass: CultClass, userContext: UserContext): TrialClassMissedWidget {
        const workoutType = _.get(cultClass, "Workout.name", "")
        const tz = userContext.userProfile.timezone
        const dayText: string = TimeUtil.getDayText(cultClass.date, tz).toLowerCase()
        return {
            widgetType: "TRIAL_CLASS_MISSED",
            title: `We missed you at ${workoutType} ${dayText}`,
            subTitle: "Tell us what happned",
            options: TRIAL_CLASS_MISSED_OPTIONS
        }
    }
}

@injectable()
class CultNoShowHandler implements InterventionHandler {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(INTERVENTION_MODELS_TYPES.InterventionReadWriteDao) private interventionDao: IInterventionReadWriteDao,
        @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
        @inject(CULT_CLIENT_TYPES.MindFitService) private mindFitService: ICultService,
        @inject(CULT_CLIENT_TYPES.PulseService) private pulseService: IPulseService,
        @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
        @inject(CULT_CLIENT_TYPES.ICultService) private cultService: ICultServiceNew,
        @inject(CULT_CLIENT_TYPES.IMindService) private mindService: ICultServiceNew,
        @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
        @inject(CUREFIT_API_TYPES.InterventionRedisCache) private interventionRedisCache: IInterventionRedisCache,
        @inject(SPORT_API_CLIENT_TYPES.SportsApi) private sportsApiService: ISportsApi,
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) public membershipService: IMembershipService,
    ) {
    }

    async handleInterventionRequest(payload: InterventionPayload): Promise<boolean> {
        // create intervention data for cult no show and store in db
        const noShowPayload = <CultNoShowData>payload.interventionData
        const noShowData: CultNoShowData = {
            classId: noShowPayload.classId,
            bookingNumber: noShowPayload.bookingNumber,
            userId: payload.userId,
            productType: noShowPayload.productType,
            noShowsRemaining: noShowPayload.noShowsRemaining,
            response: undefined,
            type: noShowPayload.type,
            warningCount: noShowPayload.warningCount,
            penaltyAmount: noShowPayload.penaltyAmount,
            isSubscription: noShowPayload.isSubscription,
            isDroppedBooking: noShowPayload.isDroppedBooking
        }
        const intervention: Intervention = {
            userId: payload.userId,
            interventionId: payload.interventionType + "_" + noShowPayload.bookingNumber,
            type: payload.interventionType,
            status: "NOT_SHOWN",
            createdDate: new Date(),
            interventionData: noShowData
        }
        try {
            if (intervention.type === "TRIAL_CLASS_MISSED") {
                const experimentId = TRIAL_CLASS_MISSED_HAMLET_ID // owner "<EMAIL>"
                const isTrialClassMissedInterventionSupportedForUser = false
                if (!isTrialClassMissedInterventionSupportedForUser) {
                    return true
                }
            }
            await this.interventionRedisCache.storeIntervention(intervention)
            return true
        }
        catch (error) {
            this.logger.error("Intervention creation error: " + error)
            return false
        }
    }

    async handleSubmitInterventionResponse(userId: string, interventionId: string, response: any, status: InterventionStatus): Promise<any> {
        // handle submit action data and save in db
        const intervention: Intervention = await this.interventionRedisCache.getInterventionById(interventionId)

        if (!intervention) {
            const genericErr: GenericError = new GenericError({message: "No intervention found for the interventionId" + interventionId})
            genericErr.statusCode = 400
            return Promise.reject(genericErr)
        }
        else if (intervention.status === "NOT_SHOWN") {
            intervention.status = status
            await this.interventionDao.findOneAndUpdate({ interventionId: interventionId, userId: intervention.userId }, intervention, { upsert: true })
            await this.interventionRedisCache.purgeActiveInterventionForUser(interventionId, userId)
            await this.clearOtherInterventions(intervention.userId, intervention)

            if (status === "SUBMITTED" && intervention.type === "TRIAL_CLASS_MISSED") {
                return this.trialClassMissedSubmitResponse(response, userId, intervention)
            }
            return Promise.resolve(true)
        }
    }

    async trialClassMissedSubmitResponse(response: any, userId: string, intervention: Intervention): Promise<any> {

        const noShowData: CultNoShowData = <CultNoShowData>intervention.interventionData
        const productType = noShowData.productType
        let trialClassData: ITrialClases

        if (productType === "MIND") {
            trialClassData = await this.mindService.getTrialClases({ userId, appName: "CUREFIT_APP" })
        } else {
            trialClassData = await this.cultService.getTrialClases({ userId, appName: "CUREFIT_APP" })
        }

        const { consumption = 0, maxCount = 0 } = trialClassData

        let payload: InterventionSubmitPayload = {
            markAttendence: true,
            freeClass: false,
            payload: ATTENDANCE_MARKED_PAYLOAD
        }
        const giveExtraClass = consumption <= maxCount && maxCount < MAX_TRIAL_CLASS_ALLOCATED
        // Giving one extra class to user
        if (response.selectedOptionId && parseInt(response.selectedOptionId) in CLASS_NOT_ATTENDED && giveExtraClass) {
            let extraClassPayload: Payload = getExtraClassPayload("FITNESS")
            if (productType === "MIND") {
                extraClassPayload = getExtraClassPayload("MIND")
                await this.mindService.updateTrialClases({ maxCount: maxCount + 1, comment: "Extra class alloted", userId, agentId: userId, appName: "CUREFIT_APP" })
            } else {
                await this.cultService.updateTrialClases({ maxCount: maxCount + 1, comment: "Extra class alloted", userId, agentId: userId, appName: "CUREFIT_APP" })
            }

            payload = {
                markAttendence: false,
                freeClass: true,
                payload: extraClassPayload
            }
        }
        return Promise.resolve({
            action: {
                actionType: "SHOW_TRIAL_CLASS_BOOK_MODAL",
                payload
            }
        })
    }

    async isInterventionSupported(userContext: UserContext): Promise<boolean> {
        const user = await userContext.userPromise
        const cfUserProfile = <CFUserProfile>userContext.userProfile
        const experimentId = TRIAL_CLASS_MISSED_HAMLET_ID // owner shanawar
        const isTrialClassMissedInterventionSupportedForUser = false
        if (AppUtil.isTrialClassMissedInterventionSupported(userContext, user.isInternalUser, isTrialClassMissedInterventionSupportedForUser)) {
            return true
        }
        return false
    }

    async getWidgets(intervention: Intervention, userContext: UserContext): Promise<InterventionView> {
        const noShowData: CultNoShowData = <CultNoShowData>intervention.interventionData
        const productType = noShowData.productType
        if (intervention.type == "PLAY_NO_SHOW") {
            const playSessionDetails: PlaySessionDetails = await this.sportsApiService.getPlaySessionData(Number(noShowData.bookingNumber), Number(intervention.userId), true)
            const playMembership: Membership = await this.membershipService.getMembershipById(Number(noShowData.membershipId))

            return new PlayNoShowView(userContext, intervention.interventionId, noShowData, playSessionDetails, playMembership)
        }

        let cultBookingResponse: CultBookingResponse
        switch (productType) {
            case "FITNESS":
                cultBookingResponse = await this.cultFitService.getBookingV2(noShowData.bookingNumber, noShowData.userId)
                break
            case "MIND":
                cultBookingResponse = await this.mindFitService.getBookingV2(noShowData.bookingNumber, noShowData.userId)
                break
        }
        if (cultBookingResponse && cultBookingResponse.booking) {
            // If its a trial class booking
            if (intervention.type === "TRIAL_CLASS_MISSED") {
                return new CultTrailClassMissedView(userContext, intervention.interventionId, noShowData, cultBookingResponse.booking)
            }

            // if it was a PULSE booking, we need to fetch remaining useracess
            if (CultUtil.isClassAvailableForPulse(cultBookingResponse.booking.CultClass, AppUtil.isCultPulseFeatureSupported(userContext))) {
                const userAccess: UserRemainingAccess = await this.pulseService.getUserAccess(userContext.userProfile.userId)
                return new CultNoShowView(userContext, intervention.interventionId, noShowData, cultBookingResponse.booking, userAccess)
            }
            return new CultNoShowView(userContext, intervention.interventionId, noShowData, cultBookingResponse.booking)
        }
    }

    async getInterventionToShow(userId: string, userContext: UserContext): Promise<Intervention> {
        const tz = userContext.userProfile.timezone
        const sevenDaysBeforeEpoch: number = TimeUtil.getMomentNow(tz).subtract(7, "days").toDate().getTime()
        const user = await this.userCache.getUser(userId)
        const subUserIds = user.subUserRelations && user.subUserRelations.map(subUserRelation => {
            return subUserRelation.subUserId
        }) || []
        const allUserIds = [userId, ...subUserIds]
        const interventionPromises: Promise<Intervention[]>[] = []
        allUserIds.forEach((userId: string) => {
            interventionPromises.push(this.interventionRedisCache.getAllActiveInterventionsForUser(userId))
        })
        await Promise.all(interventionPromises)
        let activeInterventions: Intervention[] = []
        for (let i = 0; i < interventionPromises.length; i++) {
            const interventionList: Intervention[] = await interventionPromises[i]
            activeInterventions = activeInterventions.concat(interventionList)
        }
        if (_.isEmpty(activeInterventions)) {
            return undefined
        }
        const isTrialClassInterventionSupported = await this.isInterventionSupported(userContext)
        let chosenInterventionForDisplay: Intervention
        let chosenInterventionCreationEpoch: number = -Infinity
        activeInterventions.forEach((intervention: Intervention) => {
            const interventionCreationEpoch: number = intervention.createdDate.getTime()
            if ((intervention.type === Interventions.CULT_NO_SHOW || intervention.type === Interventions.TRIAL_CLASS_MISSED)
                && sevenDaysBeforeEpoch <= interventionCreationEpoch
            ) {
                if (!isTrialClassInterventionSupported && intervention.type === Interventions.TRIAL_CLASS_MISSED) return
                if (chosenInterventionCreationEpoch < interventionCreationEpoch) {
                    chosenInterventionForDisplay = intervention
                    chosenInterventionCreationEpoch = interventionCreationEpoch
                }
            }
        })
        return chosenInterventionForDisplay
    }

    private async clearOtherInterventions(userId: string, shownIntervention: Intervention): Promise<void> {
        const activeInterventions: Intervention[] = await this.interventionRedisCache.getAllActiveInterventionsForUser(userId)
        const shownInterventionCreationEpoch: number = shownIntervention.createdDate.getTime()
        const interventionClearancePromises: Promise<boolean>[] = []
        activeInterventions.forEach((intervention: Intervention) => {
            if (intervention.type === Interventions.CULT_NO_SHOW || intervention.type === Interventions.TRIAL_CLASS_MISSED) {
                if (intervention.createdDate.getTime() < shownInterventionCreationEpoch) {
                    interventionClearancePromises.push(this.interventionRedisCache.purgeActiveInterventionForUser(intervention.interventionId, userId))
                }
            }
        })
        await Promise.all(interventionClearancePromises)
    }
}
export default CultNoShowHandler
