import InterventionHandler, { InterventionPayload, InterventionView } from "./InterventionHandler"
import { inject, injectable } from "inversify"
import {
    CultNoShowData,
    Intervention, Interventions,
    InterventionStatus,
    InterventionSubmitPayload, InterventionType, LivePTNoShowData, Payload,
    SGTNoShowData
} from "@curefit/intervention-common"
import { BASE_TYPES, Logger } from "@curefit/base"
import { UserContext } from "@curefit/userinfo-common"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import * as _ from "lodash"
import { IInterventionRedisCache } from "./InterventionRedisCache"
import { GenericError } from "@curefit/error-client"
import { CultBooking, CultBookingResponse, ITrialClases, UserRemainingAccess } from "@curefit/cult-common"
import { ATTENDANCE_MARKED_PAYLOAD, CLASS_NOT_ATTENDED, getExtraClassPayload } from "./InterventionConstants"
import AppUtil, { MAX_TRIAL_CLASS_ALLOCATED, TRIAL_CLASS_MISSED_HAMLET_ID } from "../util/AppUtil"
import { CFUserProfile } from "../page/vm/CFUserProfile"
import CultUtil from "../util/CultUtil"
import { CultNoShowView, CultTrailClassMissedView } from "./CultNoShowHandler"
import { IInterventionReadWriteDao, INTERVENTION_MODELS_TYPES } from "@curefit/intervention-models"
import {
    Action, ActionCardWidget,
    ColorCodedDescriptionWidget, Header, InfoCard, InfoWidget, ManageOptions, ManageOptionsWidget,
    ProductListWidget,
    PulseRentalRemainingWidget,
    WidgetView
} from "../common/views/WidgetView"
import { TimeUtil } from "@curefit/util-common"
import { RUPEE_SYMBOL } from "@curefit/finance-common"
import * as momentTz from "moment-timezone"

export class SGTNoShowView implements InterventionView {
    public interventionType: InterventionType = "SGT_NO_SHOW"
    public widgets: WidgetView[] = []
    public actions: Action[] = []
    constructor(userContext: UserContext, public interventionId: string, noShowData: SGTNoShowData) {
        this.interventionId = interventionId
        this.widgets.push(this.buildTaggedDescriptionWidget(noShowData, userContext))
        if (noShowData.type === "NOTE" && noShowData.bookingType === "BUNDLE") {
            this.widgets.push(this.buildMembershipEndDateInfoWidget(noShowData, userContext))
        }
        this.widgets.push(this.buildNotesWidget(noShowData))
        this.actions.push({
            title: "GOT IT",
            actionType: "SUBMIT_INTERVENTION"
        })
    }
    buildTaggedDescriptionWidget(noShowData: SGTNoShowData, userContext: UserContext): ColorCodedDescriptionWidget {
        const tz = userContext.userProfile.timezone
        const classStartTime = TimeUtil.formatEpochInTimeZone(tz, noShowData.startTime)
        const dayText: string = TimeUtil.getDayText(classStartTime, tz).toLowerCase()
        const classTime: string = TimeUtil.formatEpochInTimeZone(tz, noShowData.startTime, "h:mm A")
        if (noShowData.type === "WARNING") {
            const subTitle = "As such, this is a no-show. But we understand and as an exception, your membership duration has not been reduced this time."
            return {
                widgetType: "COLOR_CODED_DESCRIPTION_WIDGET",
                descriptions: [{
                    title: `We missed you at ${noShowData.cultWorkoutName} Online GX class ${dayText}`,
                    subTitle: subTitle,
                    tag: {
                        text: "WARNING",
                        color: "#ffa300"
                    }
                }]
            }
        } else {
            let title = ""
            if (noShowData.bookingType === "TRIAL") {
                title = "Your one trial class has been consumed"
            } else if (noShowData.bookingType === "BUNDLE") {
                title = "Uh oh! 1 day of membership reduced due to no-show"
            } else if (noShowData.bookingType === "SINGLE") {
                title = `We missed you at ${noShowData.cultWorkoutName} Online GX class ${dayText} at ${classTime}`
            }
            const subTitle =  noShowData.bookingType === "SINGLE" ? undefined : `We missed you at ${noShowData.cultWorkoutName} Online GX class ${dayText} at ${classTime}`
            return {
                widgetType: "COLOR_CODED_DESCRIPTION_WIDGET",
                descriptions: [{
                    title,
                    subTitle,
                    tag: {
                        text: "ALERT",
                        color: "#b00020"
                    }
                }]
            }
        }
    }

    buildNotesWidget(noShowData: SGTNoShowData): ProductListWidget {
        const header: Header = {
            title: noShowData.type === "WARNING" ? "Please note" : "How to avoid a no-show"
        }
        const infoCards: InfoCard[] = []
        const cancellationWindowInHrs = noShowData.cancellationWindowInMinutes / 60
        const cancellationWindowText = noShowData.cancellationWindowInMinutes > 60 ? `${cancellationWindowInHrs} hours` : `${noShowData.cancellationWindowInMinutes} mins`
        infoCards.push({
            subTitle: `If you are unable to attend a class, please cancel upto ${cancellationWindowText} in advance`,
            icon: "/image/icons/howItWorks/cancel_3.png"
        })
        infoCards.push({
            subTitle: "Always join the class on time. Joining 10 mins after the class has started is not allowed as missing warmup increases the risk of injuries during a workout",
            icon: "/image/icons/howItWorks/hitButton_1.png"
        })
        const widget = new ProductListWidget("SMALL", header, infoCards)
        widget.hideSepratorLines = true
        return widget
    }


    buildMembershipEndDateInfoWidget(noShowData: SGTNoShowData, userContext: UserContext): ManageOptionsWidget {
        const endDate = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, noShowData.membershipEndDate, "D MMM YYYY")
        const manageOptions: ManageOptions = {
            displayText: `Membership now ends on:`,
            helperText: `${endDate}`,
            icon: "DATE",
            options: []
        }
        return new ManageOptionsWidget(manageOptions, undefined, true)
    }
}

export class LivePTNoShowView implements InterventionView {
    public interventionType: InterventionType = "LPT_NO_SHOW"
    public widgets: WidgetView[] = []
    public actions: Action[] = []
    constructor(userContext: UserContext, public interventionId: string, noShowData: LivePTNoShowData) {
        this.interventionId = interventionId
        this.widgets.push(this.buildTaggedDescriptionWidget(noShowData, userContext))
        this.widgets.push(this.buildNotesWidget(noShowData))
        this.actions.push({
            title: "GOT IT",
            actionType: "SUBMIT_INTERVENTION"
        })
    }
    buildTaggedDescriptionWidget(noShowData: SGTNoShowData, userContext: UserContext): ColorCodedDescriptionWidget {
        const tz = userContext.userProfile.timezone
        const classStartTime = TimeUtil.formatEpochInTimeZone(tz, noShowData.startTime)
        const dayText: string = TimeUtil.getDayText(classStartTime, tz).toLowerCase()
        return {
            widgetType: "COLOR_CODED_DESCRIPTION_WIDGET",
            descriptions: [{
                title: `We missed you at ${noShowData.cultWorkoutName} Online PT class ${dayText}`,
                subTitle: undefined,
                tag: {
                    text: "CLASS MISSED",
                    color: "#ffa300"
                }
            }]
        }
    }

    buildNotesWidget(noShowData: SGTNoShowData): ProductListWidget {
        const header: Header = {
            title: noShowData.type === "WARNING" ? "Please note" : "How to avoid a no-show"
        }
        const infoCards: InfoCard[] = []
        const cancellationWindowInHrs = noShowData.cancellationWindowInMinutes / 60
        infoCards.push({
            subTitle: "Join within 5 mins from the class start time. We will remind you before the class through email and immediately at session start through trainer calls",
            icon: "/image/icons/howItWorks/hitButton_1.png"
        })
        infoCards.push({
            subTitle: `If you are unable to attend a class, please cancel upto ${cancellationWindowInHrs} hours in advance`,
            icon: "/image/icons/howItWorks/cancel_3.png"
        })
        const widget = new ProductListWidget("SMALL", header, infoCards)
        widget.hideSepratorLines = true
        return widget
    }
}


@injectable()
class PTSGTNoShowHandler implements InterventionHandler {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(INTERVENTION_MODELS_TYPES.InterventionReadWriteDao) private interventionDao: IInterventionReadWriteDao,
        @inject(CUREFIT_API_TYPES.InterventionRedisCache) private interventionRedisCache: IInterventionRedisCache,
    ) {
    }

    async handleInterventionRequest(payload: InterventionPayload): Promise<boolean> {
        // create intervention data for sgt no show and store in redis
        const noShowPayload = <LivePTNoShowData>payload.interventionData
        const intervention: Intervention = {
            userId: payload.userId,
            interventionId: payload.interventionType + "_" + noShowPayload.appointmentID,
            type: payload.interventionType,
            status: "NOT_SHOWN",
            createdDate: new Date(),
            interventionData: noShowPayload
        }
        try {
            await this.interventionRedisCache.storeIntervention(intervention)
            return true
        }
        catch (error) {
            this.logger.error("Intervention creation error: " + error)
            return false
        }
    }

    async handleSubmitInterventionResponse(userId: string, interventionId: string, response: any, status: InterventionStatus): Promise<any> {
        const intervention: Intervention = await this.interventionRedisCache.getInterventionById(interventionId)

        if (!intervention) {
            const genericErr: GenericError = new GenericError({message: "No intervention found for the interventionId" + interventionId})
            genericErr.statusCode = 400
            return Promise.reject(genericErr)
        }
        else if (intervention.status === "NOT_SHOWN") {
            intervention.status = status
            await this.interventionDao.findOneAndUpdate({ interventionId: interventionId, userId: intervention.userId }, intervention, { upsert: true })
            await this.interventionRedisCache.purgeActiveInterventionForUser(interventionId, userId)
            await this.clearOtherInterventions(intervention.userId, intervention)
            return Promise.resolve(true)
        }
    }

    async getWidgets(intervention: Intervention, userContext: UserContext): Promise<InterventionView> {
        if (intervention.type === "LPT_NO_SHOW") {
            return new LivePTNoShowView(userContext, intervention.interventionId, <LivePTNoShowData>intervention.interventionData)
        }
        return new SGTNoShowView(userContext, intervention.interventionId, <SGTNoShowData>intervention.interventionData)
    }

    private async clearOtherInterventions(userId: string, shownIntervention: Intervention): Promise<void> {
        const activeInterventions: Intervention[] = await this.interventionRedisCache.getAllActiveInterventionsForUser(userId)
        const shownInterventionCreationEpoch: number = shownIntervention.createdDate.getTime()
        const interventionClearancePromises: Promise<boolean>[] = []
        activeInterventions.forEach((intervention: Intervention) => {
            if (intervention.type === shownIntervention.type) {
                if (intervention.createdDate.getTime() < shownInterventionCreationEpoch) {
                    interventionClearancePromises.push(this.interventionRedisCache.purgeActiveInterventionForUser(intervention.interventionId, userId))
                }
            }
        })
        await Promise.all(interventionClearancePromises)
    }

    async getInterventionToShow(userId: string, userContext: UserContext): Promise<Intervention> {
        // this is implemented in cf-api java service
        return null
    }
}
export default PTSGTNoShowHandler
