import { BaseDelayedBatchedQ<PERSON><PERSON><PERSON><PERSON><PERSON>, BaseDelayedQueueHandler, Message } from "@curefit/sqs-client"
import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { IQueueService, SQS_CLIENT_TYPES } from "@curefit/sqs-client"
import { Constants } from "@curefit/base-utils"
import { Logger, BASE_TYPES } from "@curefit/base"
import { InterventionPayload } from "./InterventionHandler"
import InterventionHandlerFactory from "./InterventionHandlerFactory"

@injectable()
class InterventionQueueListener extends BaseDelayedBatchedQueueHandler {

    constructor(
        @inject(SQS_CLIENT_TYPES.QueueService) queueService: IQueueService,
        @inject(CUREFIT_API_TYPES.InterventionHandlerFactory) private interventionHandlerFactory: InterventionHandlerFactory,
        @inject(BASE_TYPES.ILogger) private logger: Logger
    ) {
        super(Constants.getSQSQueue("INTERVENTION_QUEUE"), 10, queueService, 60 * 1000)
    }

    async handle(body: Message[]): Promise<boolean[]> {
        return Promise.all(body.map(message => this.handleMessage(message.data, message.attributes)))
    }

    async handleMessage(message: string, attributes: { [key: string]: any }): Promise<boolean> {
        this.logger.debug("Intervention message: " + message)
        const interventionPayload = <InterventionPayload>JSON.parse(message)
        return this.interventionHandlerFactory.getHandler(interventionPayload.interventionType).handleInterventionRequest(interventionPayload)
    }
}

export default InterventionQueueListener
