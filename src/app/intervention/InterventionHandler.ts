import {
    CultNoShowResponse,
    Intervention,
    InterventionStatus,
    InterventionType, SGTNoShowData
} from "@curefit/intervention-common"
import { Action, WidgetView } from "../common/views/WidgetView"
import { ProductType } from "@curefit/product-common"
import { UserContext } from "@curefit/userinfo-common"

export interface CultNoShowPayload {
    classId: string
    bookingNumber: string
    productType: ProductType
    noShowsRemaining?: number
    type: "WARNING" | "PENALTY" | "DROPOUT_SUCCESS"
    warningCount?: number
    penaltyAmount?: number
    isSubscription?: boolean
    isDroppedBooking?: boolean
}

export interface InterventionPayload {
    userId: string
    interventionType: InterventionType
    interventionData: InterventionPayloadDataTypes
    interventionId?: string
    validTo?: Date
    validFrom?: Date
}

export interface MessageInterventionData {
    url: string
    action: Action
}
export interface InterventionView {
    interventionId: string
    interventionType: InterventionType
    widgets: WidgetView[]
    actions: Action[]
    hideDivider?: boolean
}

// define your payload data type interface name here
export type InterventionPayloadDataTypes = CultNoShowPayload | MessageInterventionData | SGTNoShowData
// define your response type interface name here
export type InterventionResponseTypes = CultNoShowResponse

interface InterventionHandler {
    handleInterventionRequest(payload: InterventionPayload): Promise<boolean>
    handleSubmitInterventionResponse(userId: string, interventionId: string, responseData: any, status: InterventionStatus): Promise<any>
    getWidgets(intervention: Intervention, userContext: UserContext): Promise<InterventionView>
    getInterventionToShow(userId: string, userContext: UserContext): Promise<Intervention>
}

export default InterventionHandler
