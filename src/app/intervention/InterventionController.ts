import * as express from "express"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import { Container, inject } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import AuthMiddleware from "../auth/AuthMiddleware"
import { InterventionView } from "./InterventionHandler"
import { Intervention } from "@curefit/intervention-common"
import { IInterventionReadOnlyDao, INTERVENTION_MODELS_TYPES } from "@curefit/intervention-models"
import InterventionHandlerFactory from "./InterventionHandlerFactory"
import { UserContext } from "@curefit/userinfo-common"
import { IInterventionRedisCache } from "./InterventionRedisCache"
import IInterventionBusiness from "./IInterventionBusiness"
import { ErrorFactory } from "@curefit/error-client"
import { ErrorCodes } from "../error/ErrorCodes"


export function controllerFactory(kernel: Container) {
    @controller("/intervention",
        kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class InterventionController {
        constructor(
            @inject(CUREFIT_API_TYPES.InterventionHandlerFactory) private interventionHandlerFactory: InterventionHandlerFactory,
            @inject(INTERVENTION_MODELS_TYPES.InterventionReadOnlyDao) private interventionDao: IInterventionReadOnlyDao,
            @inject(CUREFIT_API_TYPES.InterventionRedisCache) private interventionRedisCache: IInterventionRedisCache,
            @inject(CUREFIT_API_TYPES.InterventionBusiness) private interventionBusiness: IInterventionBusiness,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        ) { }

        @httpPost("/submit")
        public async submit(req: express.Request, res: express.Response): Promise<any> {
            const userId: string = req.session.userId
            const interventionId: string = req.body.interventionId
            if (!interventionId) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid intervention id").build()
            }
            const interventionType = req.body.interventionType
            if (!interventionType) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid intervention type").build()
            }
            const status = req.body.status
            const response: any = req.body.response
            return this.interventionHandlerFactory.getHandler(interventionType).handleSubmitInterventionResponse(userId, interventionId, response, status)
        }

        @httpGet("/getWidgets/:interventionId")
        public async getWidgets(req: express.Request, res: express.Response): Promise<InterventionView> {
            const interventionId: string = req.params.interventionId
            if (!interventionId) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid intervention id").build()
            }
            const intervention: Intervention = await this.interventionRedisCache.getInterventionById(interventionId)
            return this.interventionHandlerFactory.getHandler(intervention.type).getWidgets(intervention, req.userContext as UserContext)
        }

        @httpGet("/getDetails/:interventionId")
        public async getInterventionDetails(req: express.Request, res: express.Response): Promise<Intervention> {
            const userId: string = req.session.userId
            const interventionId: string = req.params.interventionId
            if (!interventionId) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid intervention id").build()
            }
            const intervention: Intervention = await this.interventionRedisCache.getInterventionById(interventionId)
            return intervention
        }

        @httpPost("/backfillCache")
        public async backfillCache(req: express.Request, res: express.Response): Promise<any> {
            const userId: string = req.session.userId
            const apiSecret: string = req.body.apiSecret
            if (apiSecret !== "9565a495-5623-4b1d-a422-5d39762e8d97") {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid credentials").build()
            }
            const backfillCount: number = await this.interventionBusiness.backfillInterventions()
            return { success: true, count: backfillCount }
        }

    }
    return InterventionController
}

export default controllerFactory
