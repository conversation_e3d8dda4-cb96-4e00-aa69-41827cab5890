import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import InterventionHandler from "./InterventionHandler"
import { InterventionType } from "@curefit/intervention-common"

@injectable()
class InterventionHandlerFactory {

    private INTERVENTION_TYPE_TO_HANDLER_MAP = new Map<InterventionType, InterventionHandler>()

    constructor(@inject(CUREFIT_API_TYPES.CultNoShowHandler) private cultNoShowHandler: InterventionHandler,
                @inject(CUREFIT_API_TYPES.MessageInterventionHandler) private messageInterventionHandler: InterventionHandler,
                @inject(CUREFIT_API_TYPES.PTSGTNoShowHandler) private ptsgtNoShowHandler: InterventionHandler,
    ) {
        this.INTERVENTION_TYPE_TO_HANDLER_MAP.set("CULT_NO_SHOW", cultNoShowHandler)
        this.INTERVENTION_TYPE_TO_HANDLER_MAP.set("MESSAGE_INTERVENTION", messageInterventionHandler)
        this.INTERVENTION_TYPE_TO_HANDLER_MAP.set("TRIAL_CLASS_MISSED", cultNoShowHandler)
        this.INTERVENTION_TYPE_TO_HANDLER_MAP.set("PLAY_NO_SHOW", cultNoShowHandler)
        this.INTERVENTION_TYPE_TO_HANDLER_MAP.set("SGT_NO_SHOW", ptsgtNoShowHandler)
        this.INTERVENTION_TYPE_TO_HANDLER_MAP.set("LPT_NO_SHOW", ptsgtNoShowHandler)
    }
    getHandler(interventionType: InterventionType): InterventionHandler {
        return this.INTERVENTION_TYPE_TO_HANDLER_MAP.get(interventionType)
    }
}
export default InterventionHandlerFactory
