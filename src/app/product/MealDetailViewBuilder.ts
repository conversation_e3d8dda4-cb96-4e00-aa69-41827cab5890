import {
    Action,
    ActionCard,
    ActionSheet,
    ActionSheetWidget,
    CalloutWidget,
    DescriptionWidget,
    getOffersWidget,
    InfoCard, ManageOptionPayload,
    ManageOptions,
    ManageOptionsWidget,
    MealAction,
    MealProductDetailPage,
    PackInfoWidget,
    ProductDetailPage,
    ProductFeedbackWidget,
    WidgetView
} from "../common/views/WidgetView"
import OrderTrackingStatusWidget from "../common/views/OrderTrackingStatusWidget"
import { FoodBooking, FoodPackBooking } from "@curefit/shipment-common"
import {
    CustomerIssueType,
} from "@curefit/issue-common"
import {
    FoodPack, ListingBrandIdType,
} from "@curefit/eat-common"
import {
    MenuType,
} from "@curefit/eat-common"
import {
    FoodProduct as Product,
} from "@curefit/eat-common"
import {
    ProductPrice,
} from "@curefit/product-common"
import {
    UserAgentType as UserAgent
} from "@curefit/base-common"
import { ActionUtil, OrderUtil, SeoUrlParams } from "@curefit/base-utils"
import * as _ from "lodash"
import { TimeUtil } from "@curefit/util-common"
import { SlotUtil } from "@curefit/eat-util"
import { UrlPathBuilder } from "@curefit/product-common"
import { Feedback } from "@curefit/feedback-common"
import { RUPEE_SYMBOL } from "@curefit/finance-common"
import FeedbackPageConfigV2Cache from "../ugc/FeedbackPageConfigV2Cache"
import { PreferredLocation } from "@curefit/userinfo-common"
import { OfferUtil } from "@curefit/base-utils"
import { capitalizeFirstLetter, pad } from "@curefit/util-common"
import { inject, injectable } from "inversify"
import IProductBusiness from "./IProductBusiness"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { Logger, BASE_TYPES } from "@curefit/base"
import { MagazineListWidget, RecipeActionCard } from "../page/PageWidgets"
import { MAX_CART_SIZE, MealUtil } from "@curefit/base-utils"
import EatUtils from "../util/EatUtil"
import EatUtil from "../util/EatUtil"
import { FoodSinglePriceOfferResponse } from "@curefit/offer-common"
import { IOfferServiceV2 } from "@curefit/offer-service-client"
import { OfferV2, OfferV2Lite } from "@curefit/offer-common"
import { UserContext } from "@curefit/userinfo-common"
import IssueBusiness from "../crm/IssueBusiness"
import { DIYRecipeView } from "@curefit/diy-common"
import { IBaseWidget, FssaiLicenseWidget } from "@curefit/vm-models"
import { FitClubUtil } from "@curefit/fitclub-client"
import { OFFER_SERVICE_CLIENT_TYPES } from "@curefit/offer-service-client"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { FoodProduct } from "@curefit/eat-common"
import { FOOD_MONGO_TYPES, IIngredientReadonlyDao } from "@curefit/food-mongo"
import AppUtil from "../util/AppUtil"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { FINANCE_MODELS_TYPES, SellerService } from "@curefit/finance-models"
import { FITCASH_CLIENT_TYPES, IFitcashService } from "@curefit/fitcash-client"
import { FitclubSavingsCalloutWidgetV2 } from "./FitclubSavingsCalloutWidgetV2"
import { FitclubBusiness } from "../fitclub/FitclubBusiness"
import { EatSubscriptionUtil } from "../util/EatSubscriptionUtil"
import { CAFE_API_CLIENT_TYPES, CafeProductService } from "@curefit/cafe-api-client"
import { OMS_API_CLIENT_TYPES, IOrderService } from "@curefit/oms-api-client"
import { DEFAULT_DELIVERY_CHARGE } from "../util/OrderUtil"
const clone = require("clone")


@injectable()
class MealDetailViewBuilder {

    constructor(@inject(CUREFIT_API_TYPES.ProductBusiness) private productBusiness: IProductBusiness,
        @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness,
        @inject(FINANCE_MODELS_TYPES.SellerService) private sellerService: SellerService,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) private offerServiceV2: IOfferServiceV2,
        @inject(FOOD_MONGO_TYPES.IngredientReadonlyDao) private ingredientDao: IIngredientReadonlyDao,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(OMS_API_CLIENT_TYPES.OrderService) private omsApiClient: IOrderService,
        @inject(FITCASH_CLIENT_TYPES.FitcashService) private fitcashService: IFitcashService,
        @inject(CUREFIT_API_TYPES.FitclubBusiness) private fitclubBusiness: FitclubBusiness,
        @inject(CAFE_API_CLIENT_TYPES.CafeProductService) private cafeProductService: CafeProductService,
    ) {

    }

    async getView(
        userContext: UserContext, mealSlot: MenuType,
        preferredLocation: PreferredLocation,
        product: Product, date: string, issuesMap: Map<string, CustomerIssueType[]>, foodPack: FoodPack, foodBooking: FoodBooking, foodPackBooking: FoodPackBooking, isPackActive: boolean = true, enableBuy: boolean = false,
        slotInventory?: { [campaignType: string]: { [date: string]: string[] } },
        stockInventory?: { [campaignType: string]: { [packId: string]: { left: number, total: number } } },
        singleOffersResponse?: FoodSinglePriceOfferResponse,
        cartOffers?: OfferV2[],
        feedback?: Feedback,
        FeedbackPageConfigV2Cache?: FeedbackPageConfigV2Cache, loggedoutAppFlow?: boolean, recipes?: DIYRecipeView[], isInternalUser?: boolean,
        useWidgetsV2: boolean = false, listingBrand?: ListingBrandIdType): Promise<ProductDetailPage> {
        const isAddChangeMealSupportedForBrand = EatUtil.isAddChangeMealSupportedForBrand(foodBooking?.listingBrand)
        if (foodBooking) {
            if (foodBooking.listingBrand === "DEALS" || foodBooking.listingBrand === "CUREFOODS") {
                foodBooking.listingBrand = "EAT_FIT"
            }
            listingBrand = foodBooking.listingBrand
        }

        if (foodBooking && foodBooking.listingBrand === "WHOLE_FIT") {
            mealSlot = "ALL"
        }
        const tz = userContext.userProfile.timezone
        const mealDetailView: MealProductDetailPage = new MealProductDetailPage()
        if (!_.isEmpty(cartOffers)) {
            mealDetailView.cartOffers = EatUtil.cartOffersForUpsell(cartOffers)
        } else {
            mealDetailView.cartOffers = []
        }
        const timezone = userContext.userProfile.timezone
        // Allow  buy option only for today's  and tmrw meal
        const today: string = TimeUtil.todaysDateWithTimezone(timezone)
        const days: string[] = TimeUtil.getDaysFrom(timezone, today, 2)
        const tmrw: string = days[1]

        const isFitclubMember = await this.fitclubBusiness.isFitclubMember(userContext.userProfile.userId)

        let offerPrice: ProductPrice
        let offers: OfferV2Lite[]
        let offerIds: string[]
        let stock: number
        let inventoryToCheck
        let offerBasedTitle: string
        let isMealAvailable: boolean
        let parentProduct: Product
        if (!_.isNil(product.parentProductId)) {
            parentProduct = await this.catalogueService.getProduct(product.parentProductId)
        }
        const nutritionalInfo = EatUtil.computeNutritionalInfo(product, parentProduct, listingBrand)
        const maxCartSize = MAX_CART_SIZE
        const isPrefLocationCafe = preferredLocation && preferredLocation.deliveryChannel === "CAFE"
        const isCafe = foodBooking ? foodBooking.address && foodBooking.address.kioskType === "CAFE" : isPrefLocationCafe
        if (enableBuy) {
            const offerAndPriceDetails = OfferUtil.getSingleOfferAndPrice(product, date, singleOffersResponse, mealSlot)
            stock = OfferUtil.getAvailabilityV1(product, stockInventory) ? OfferUtil.getAvailabilityV1(product, stockInventory).left : 0
            stock = product.maxCartQty ? Math.min(product.maxCartQty, stock) : stock
            stock = offerAndPriceDetails.price.listingPrice === 0 && stock > 0 ? 1 : stock
            isMealAvailable = stock > 0
            _.forEach(product.variants, variant => {
                const variantProduct = clone(product)
                variantProduct.productId = variant.productId
                const variantStock = OfferUtil.getAvailabilityV1(variantProduct, stockInventory).left ? OfferUtil.getAvailabilityV1(variantProduct, stockInventory).left : 0
                isMealAvailable = variantStock > 0 || isMealAvailable
            })
            if (!isMealAvailable) {
                this.logger.info(`Getting availability for productId ${product.productId}, userId ${userContext.userProfile.userId}, stock - ${stock} and isMealAvailable - ${isMealAvailable}`)
            }
            offerPrice = offerAndPriceDetails.price
            offers = offerAndPriceDetails.offers
            offerIds = !_.isEmpty(offers) ? _.map(offers, offer => {
                return offer.offerId
            }) : undefined
            inventoryToCheck = slotInventory["NONE"]
            if (offerPrice.listingPrice === 0)
                offerBasedTitle = "Try for free"
            else
                offerBasedTitle = `Try for ${RUPEE_SYMBOL}${offerPrice.listingPrice}`
        }
        const enableBuyOption = !foodBooking && enableBuy && isMealAvailable
        this.logger.info("enable buy option : " + enableBuyOption + " isFoodBooking : " + JSON.stringify(foodBooking) + " and enableBuy : " + enableBuy + " and isMealAvailable : " + isMealAvailable)
        const mealSummaryWidget = useWidgetsV2 ? await this.getMealSummaryWidgetV2(userContext, isMealAvailable, isCafe, offerIds, offerPrice, enableBuyOption, date, issuesMap, product, foodBooking, isPackActive, maxCartSize, singleOffersResponse, stockInventory, mealSlot, nutritionalInfo, listingBrand) :
            await this.getMealSummaryWidget(userContext, isMealAvailable, offerIds, offerPrice, enableBuyOption, date, issuesMap, product, foodBooking, isPackActive)
        mealDetailView.widgets.push(mealSummaryWidget)
        if (!_.isEmpty(product.ingredients)) {
            mealDetailView.widgets.push(this.getIngredientWidget(await this.getIngredientNames(product)))
        }

        if (foodBooking) {
            const isPackOrder = foodBooking.packId ? true : false
            const mealManageOptions = await this.productBusiness.getMealManageOptions(userContext, foodBooking, issuesMap, isPackOrder, isPackActive, true, false, false, foodBooking.deliveryType === "ON_DEMAND")
            const isCancelCutOffPassed: boolean = SlotUtil.isCancelCutOffPassedForChannel(foodBooking.deliveryWindow, foodBooking.deliveryChannel, timezone)
            const isDelivered: boolean = foodBooking.state === "DELIVERED" ? true : false

            if (feedback && (feedback.rating === "NOT_RATED" || feedback.rating === "DISMISSED")) {
                mealDetailView.widgets.push(new ProductFeedbackWidget(await FeedbackPageConfigV2Cache.getQuestionV2(feedback), feedback.feedbackId))
            } else if (foodBooking && foodBooking.state) {
                const orderTackingWidget = new OrderTrackingStatusWidget(foodBooking, mealSlot, userContext)
                if (orderTackingWidget && orderTackingWidget.values && orderTackingWidget.values.length > 0) {
                    mealDetailView.widgets.push(orderTackingWidget)
                }
            }
            if (foodBooking.state !== "DELIVERED" && foodBooking.state !== "CANCELLED" && foodBooking.state !== "REJECTED" && foodBooking.address.addressType === "KIOSK") {
                this.logger.info("UserId: " + `${userContext.userProfile.userId},` + "FoodBooking state: " + `${foodBooking.state}` + " for passcode widget")
                const passcodeWidget = await EatUtil.getPasscodeWidget(foodBooking)
                mealDetailView.widgets.push(passcodeWidget)

            }
            if (isDelivered || foodBooking.state === "REJECTED") {
                const mealReportIssue = await this.productBusiness.getMealReportIssue(userContext, foodBooking, issuesMap, isPackOrder)
                mealDetailView.widgets.push(new ManageOptionsWidget(mealReportIssue.manageOptions, mealReportIssue.meta))
            }
            // const fitCashOffer = _.find(cartOffers, (offer: OfferV2) => {
            //     return offer.addons[0] && offer.addons[0].addonType === "FITCASH"
            // })
            await this.addFitclubWidget(mealDetailView, isFitclubMember, foodBooking, cartOffers, offerPrice, product, userContext, isCafe)
            let rescheduleOrCancelWidget
            if (!isDelivered) {
                let addressText = foodBooking.address.addressLine1 ? foodBooking.address.addressLine1 + ", " : ""
                addressText = addressText + foodBooking.address.addressLine2
                const addressOptions: ManageOptions = {
                    displayText: foodBooking.address.addressType + ": " + addressText,
                    subHelper: addressText,
                    options: [],
                    icon: "LOCATION"
                }
                if (MealUtil.isCancelMealAllowed(foodPack) && foodBooking.state !== "REJECTED") {
                    mealDetailView.widgets.push(await this.getCancelMealWidget(foodBooking, isPackOrder, isPackActive, false, tz))
                }
                if (!isPackOrder)
                    mealDetailView.widgets.push(new ManageOptionsWidget(addressOptions, undefined, !isCancelCutOffPassed || foodBooking.state === "REJECTED"))
                if (foodBooking.deliveryType !== "ON_DEMAND") {
                    const isAddChangeMealNotSupportedTagPresent = !_.isEmpty(foodPack) && foodPack.tags?.indexOf(EatSubscriptionUtil.ADD_CHANGE_MEAL_NOT_SUPPORTED_TAG) > -1
                    rescheduleOrCancelWidget = this.getRescheduleWidget(mealManageOptions.manageOptions, mealManageOptions.meta, foodBooking.address.addressType === "KIOSK")
                    if (isAddChangeMealSupportedForBrand && !isAddChangeMealNotSupportedTagPresent && MealUtil.isAddMealSupported(userContext) && foodBooking.address.kioskType !== "CAFE" && foodBooking.state !== "REJECTED" && foodBooking.listingBrand !== "WHOLE_FIT") {
                        mealDetailView.widgets.push(this.getChangeMealWidget(userContext, mealSlot, foodBooking, isCancelCutOffPassed, true))
                    }
                    if (isPackOrder) {
                        const upcomingShipment = this.productBusiness.getUpcomingShipment(foodPackBooking, tz)
                        if (!isAddChangeMealNotSupportedTagPresent && upcomingShipment && upcomingShipment.deliveryDate === foodBooking.deliveryDate && !MealUtil.isAddMealSupported(userContext) && foodBooking.state !== "REJECTED" && foodBooking.listingBrand !== "WHOLE_FIT") {
                            mealDetailView.widgets.push(this.getChangeMealWidget(userContext, mealSlot, foodBooking, isCancelCutOffPassed, isPackActive))
                        }
                        if (rescheduleOrCancelWidget) {
                            mealDetailView.widgets.push(rescheduleOrCancelWidget)
                        }
                        const isChangeAddressAllowed = MealUtil.isChangeAddressAllowed(foodPack)
                        mealDetailView.widgets.push(this.getChangeAddressWidget(userContext, foodBooking, isCancelCutOffPassed, isPackActive, !isChangeAddressAllowed))
                        if (MealUtil.isStandingInstructionSupported(userContext) && !foodBooking.address.kioskId)
                            mealDetailView.widgets.push(this.getChangeInstructionWidget(foodBooking, isCancelCutOffPassed, isPackActive, userContext))
                    } else {
                        if (rescheduleOrCancelWidget) {
                            mealDetailView.widgets.push(rescheduleOrCancelWidget)
                        }
                    }
                    const cancelCutoffTime = SlotUtil.getCancelCutOffByChannelOrSlot(foodBooking.deliverySlot ? foodBooking.deliverySlot.slotId : undefined, foodBooking.deliveryWindow, foodBooking.deliveryChannel, timezone)
                    if (isCancelCutOffPassed) {
                        if (foodBooking.state !== "REJECTED") {
                            mealDetailView.widgets.push(new CalloutWidget(`Order cannot be modified after ${MealUtil.getCancelCutOffDisplayTextForTime(cancelCutoffTime)}`))
                        }
                    } else {
                        mealDetailView.widgets.push(new CalloutWidget(`Change settings by ${MealUtil.getCancelCutOffDisplayTextForTime(cancelCutoffTime)}`))
                    }
                }
            }
        }
        if (enableBuy && listingBrand !== "WHOLE_FIT") {  // TODO - this is a hack and upsell should be based on offer availability
            await this.addFitclubWidget(mealDetailView, isFitclubMember, foodBooking, cartOffers, offerPrice, product, userContext, isCafe)
        }
        if (enableBuy || _.isNil(foodBooking)) {
            let showOffers: (OfferV2 | OfferV2Lite)[] = []
            if (!_.isNil(offers) && _.isArray(offers)) {
                showOffers.push(...offers)
            }
            if (!AppUtil.isFitClubApplicable(userContext, isFitclubMember) && !_.isNil(cartOffers) && _.isArray(cartOffers)) {
                const fitcashOffer = _.maxBy(cartOffers.filter(offer => offer.addons[0] && offer.addons[0].addonType === "FITCASH"), "priority")
                // showOffers.push(...cartOffers.filter(offer => !(offer.addons[0] && offer.addons[0].addonType === "FITCASH") || offer.offerId === fitcashOffer.offerId))
                if (fitcashOffer) {
                    showOffers.push(fitcashOffer)
                }
            }
            showOffers = showOffers.filter(o => {
                const offer = o as OfferV2
                return !(!_.isEmpty(offer.displayContexts) && offer.displayContexts.includes("NONE"))
            })
            if (!_.isEmpty(showOffers)) {
                mealDetailView.widgets.push(getOffersWidget("Offers applied", showOffers))
            }
        }
        const nutritionWidget = useWidgetsV2 ? this.getNutritionWidgetV2(nutritionalInfo, userContext) : this.getNutritionWidget(nutritionalInfo, product.shipmentWeight > 1)
        if (!_.isNil(nutritionWidget)) {
            mealDetailView.widgets.push(nutritionWidget)
        }

        const productDescription: string = product.subTitle
        const components: {
            name: string
            description: string
        }[] = product.attributes.components

        const validComponents = _.filter(components, component => {
            return component.name || component.description ? true : false
        })

        if (productDescription || !_.isEmpty(validComponents))
            mealDetailView.widgets.push(this.getMealDetailWidget(productDescription, validComponents))

        const fssaiLicenseNumber = await EatUtil.getFssaiNumber(product.sellerId, this.sellerService, this.serviceInterfaces.deliveryAreaService, this.serviceInterfaces.cityService, preferredLocation)
        if (!_.isNil(fssaiLicenseNumber)) {
            const licenseWidget = await new FssaiLicenseWidget(fssaiLicenseNumber).buildView(undefined, userContext, undefined)
            mealDetailView.widgets.push(licenseWidget)
        }

        if (foodPack) {
            mealDetailView.widgets.push(this.getPackInfoWidget(foodPack, foodBooking, userContext))
        }

        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "APP")
        const osName = userContext.sessionInfo.osName
        const appVersion = userContext.sessionInfo.appVersion
        const codepushVersion = userContext.sessionInfo.cpVersion
        if (!_.isEmpty(recipes) && userAgent === "APP" && EatUtil.isRecipeSupported(osName, appVersion, codepushVersion, isInternalUser)) {
            const actioncards: RecipeActionCard[] = _.map(recipes, recipe => {
                return {
                    action: ActionUtil.recipeHomePage(recipe.id, null, null),
                    image: UrlPathBuilder.prefixSlash(recipe.imageDetails.thumbnailImage),
                    skipPrice: true,
                    isOfferApplied: true,
                    isVeg: recipe.isVeg,
                    duration: recipe.preparationTime / 60 + " Min",
                    type: "RECIPE",
                    analyticsData: {
                        recipeId: recipe.id
                    }
                }
            })
            const recipeTitle = { title: "Make at Home" }
            const magazineListWidget: MagazineListWidget = {
                widgetType: "MAGAZINE_LIST_WIDGET",
                actioncards: actioncards,
                widgetTitle: recipeTitle
            }
            mealDetailView.widgets.push(
                magazineListWidget
            )
        }

        const websiteDetailPage: boolean = isMealAvailable && preferredLocation && !(preferredLocation.address && preferredLocation.area) && (userContext.sessionInfo.userAgent === "MBROWSER" || userContext.sessionInfo.userAgent === "DESKTOP")
        mealDetailView.pageActions = []

        // Actions for wholefit meal view page
        if (useWidgetsV2) {
            const showloginAction = !userContext.sessionInfo.isUserLoggedIn
            return this.populateV2Actions(mealDetailView, enableBuy, enableBuyOption, foodBooking, userContext, showloginAction, websiteDetailPage, maxCartSize, product, mealSlot, date)
        }

        if (!enableBuy || foodBooking || (!websiteDetailPage && !userContext.sessionInfo.isUserLoggedIn)) {
            mealDetailView.pageAction = {
                title: "Done",
                action: "POP_ACTION"
            }
            mealDetailView.pageActions.push({
                title: "Done",
                actionType: "POP_ACTION"
            })
            mealDetailView.actions.push({
                title: "Done",
                actionType: "POP_ACTION"
            })
        } else if (enableBuyOption || loggedoutAppFlow || websiteDetailPage) {
            mealDetailView.pageAction = {
                title: offerBasedTitle,
                action: "BUY_MEAL"
            }
            const buyMealAction: MealAction = {
                title: offerBasedTitle,
                actionType: "BUY_MEAL",
                productName: product.title,
                url: `curefit://cartcheckout?listingBrand=${listingBrand}`,
                productId: product.productId,
                listingBrand: listingBrand,
                price: offerPrice,
                stock: stock,
                date: date,
                mealSlot: {
                    id: mealSlot,
                    name: capitalizeFirstLetter(mealSlot.toLowerCase())
                },
                maxCartSize: maxCartSize,
                offerId: !_.isEmpty(offerIds) ? offerIds[0] : undefined,
                offerIds: offerIds,
            }
            mealDetailView.pageActions.push(buyMealAction)
            mealDetailView.actions.push(buyMealAction)
            const image = UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, product.productType, "THUMBNAIL", product.imageVersion)
            const addToCartAction: MealAction = {
                title: "Add to cart",
                actionType: "ADD_TO_CART",
                image: image,
                listingBrand: listingBrand,
                productName: product.title,
                productId: product.productId,
                date: date,
                stock: stock,
                price: offerPrice,
                mealSlot: {
                    id: mealSlot,
                    name: capitalizeFirstLetter(mealSlot.toLowerCase())
                },
                maxCartSize: maxCartSize,
                offerId: !_.isEmpty(offerIds) ? offerIds[0] : undefined,
                offerIds: offerIds
            }
            mealDetailView.pageActions.push(addToCartAction)
            mealDetailView.actions.push(addToCartAction)
        } else {
            if (!isMealAvailable) {
                mealDetailView.pageAction = {
                    title: "Sold out",
                    action: "POP_ACTION"
                }
                mealDetailView.pageActions.push({
                    title: "Sold out",
                    actionType: "POP_ACTION"
                })
                mealDetailView.actions.push({
                    title: "Sold out",
                    actionType: "POP_ACTION"
                })
            }
        }
        if (mealDetailView.widgets) {
            mealDetailView.widgets = _.filter(mealDetailView.widgets, w => {
                return !_.isNil(w)
            })
        }
        return mealDetailView

    }

    async getCafeView(userContext: UserContext, kioskId: string, product: Product, loggedoutAppFlow: boolean, date: string, listingBrand: ListingBrandIdType): Promise<ProductDetailPage> {
        const mealDetailView: MealProductDetailPage = new MealProductDetailPage()
        mealDetailView.cartOffers = []
        const productList: {productId: string, availability: {left: number}}[]
            = await this.cafeProductService.getProducts(kioskId)
        const availablityMap = _.keyBy(productList, p => p.productId)
        const availability = availablityMap[product.productId].availability
        const isMealAvailable = availability.left > 0
        const maxCartSize = product.maxCartQty ? product.maxCartQty : MAX_CART_SIZE
        const nutritionInfo = EatUtil.computeNutritionalInfo(product, undefined, listingBrand)

        const mealSummaryWidget = await this.getCafeMealSummaryWidgetV2(userContext, product, listingBrand, isMealAvailable,
            nutritionInfo, maxCartSize, true, date, availability.left, kioskId)
        mealDetailView.widgets.push(mealSummaryWidget)
        if (!_.isEmpty(product.ingredients)) {
            mealDetailView.widgets.push(this.getIngredientWidget(await this.getIngredientNames(product)))
        }
        mealDetailView.pageActions = []

        const nutritionWidget = this.getNutritionWidget(nutritionInfo, product.shipmentWeight > 1)
        if (!_.isNil(nutritionWidget)) {
            mealDetailView.widgets.push(nutritionWidget)
        }

        const productDescription: string = product.subTitle
        const components: {
            name: string
            description: string
        }[] = product.attributes.components

        const validComponents = _.filter(components, component => {
            return component.name || component.description ? true : false
        })

        if (productDescription || !_.isEmpty(validComponents))
            mealDetailView.widgets.push(this.getMealDetailWidget(productDescription, validComponents))

        const fssaiLicenseNumber = await EatUtil.getFssaiNumber(product.sellerId, this.sellerService, this.serviceInterfaces.deliveryAreaService,
            this.serviceInterfaces.cityService, {deliveryChannel: "CAFE", isWithinServicableCity: true, isInServicableArea: true})
        if (!_.isNil(fssaiLicenseNumber)) {
            const licenseWidget = await new FssaiLicenseWidget(fssaiLicenseNumber).buildView(undefined, userContext, undefined)
            mealDetailView.widgets.push(licenseWidget)
        }

        if (loggedoutAppFlow) {
            const loginAction: Action = {
                actionType: "SHOW_ALERT_MODAL",
                title: "Login", meta: {
                    title: "Login Required!",
                    subTitle: "Please login to continue",
                    actions: [{ actionType: "LOGOUT", title: "Login" }]
                }
            }
            mealDetailView.pageActions.push(loginAction)
            mealDetailView.actions.push(loginAction)
            return mealDetailView
        }

        if (!isMealAvailable) {
            mealDetailView.pageAction = {
                title: "Sold out",
                action: "POP_ACTION"
            }
            mealDetailView.pageActions.push({
                title: "Sold out",
                actionType: "POP_ACTION"
            })
            mealDetailView.actions.push({
                title: "Sold out",
                actionType: "POP_ACTION"
            })
        }
        return mealDetailView
    }
    public setMessage(productDetailPage: ProductDetailPage, title: string, subTitle: string, actions?: Action[], listItems?: { title: string, subTitle: string }[]) {
        productDetailPage.message = { title, subTitle }
        if (actions) {
            productDetailPage.alertInfo = {
                title: title,
                subTitle: subTitle,
                actions: actions,
                listItems: listItems,
                meta: {
                    modalHeight: listItems && listItems.length > 1 ? 400 : 300
                }
            }
        }
    }

    private async getIngredientNames(product: Product): Promise<string[]> {
        const ingredientWithNames = await this.ingredientDao.find({ condition: { ingredientId: { $in: product.ingredients } } })
        const ingredients: string[] = []
        _.forEach(product.ingredients, ingredientId => {
            const ingredient = _.find(ingredientWithNames, ing => {
                return ing.ingredientId === ingredientId
            })
            if (!_.isNil(ingredient)) {
                ingredients.push(ingredient.name)
            }
        })
        return ingredients
    }

    private async getCancelMealWidget(foodBooking: FoodBooking, isPackOrder: boolean, isPackActive: boolean, isOnDemand: boolean,
        timezone: string = TimeUtil.IST_TIMEZONE): Promise<ManageOptionsWidget> {
        const manageOptions = await this.productBusiness.getCancelManageOptions(foodBooking, isPackOrder, isPackActive, false, isOnDemand, undefined, timezone)
        manageOptions.manageOptions.options = manageOptions.manageOptions.options.filter((option: ManageOptionPayload) => {
            return option.isEnabled
        })
        const isEnabled = manageOptions.manageOptions.options.length > 0
        const manageOptionWidget = new ManageOptionsWidget(manageOptions.manageOptions, manageOptions.meta)
        manageOptionWidget.isDisabled = !isEnabled
        return manageOptionWidget
    }

    private getRescheduleWidget(manageOptions: ManageOptions, meta: any, isDisabled: boolean) {
        const manageOptionsWidget = new ManageOptionsWidget(manageOptions, meta)
        manageOptions.options = manageOptions.options.filter((option) => {
            return option.isEnabled
        })
        const isEnabled = manageOptions.options.length > 0
        manageOptionsWidget.isDisabled = !isEnabled || isDisabled
        return manageOptionsWidget
    }

    private getChangeMealWidget(userContext: UserContext, mealSlot: MenuType, foodBooking: FoodBooking, isCancelCutOffPassed: boolean, isPackActive: boolean): ManageOptionsWidget {
        const isEnabled = !isCancelCutOffPassed && foodBooking.state !== "CANCELLED" && foodBooking.state !== "DELIVERED" && isPackActive && foodBooking.changeMealAllowed && foodBooking.listingBrand !== "WHOLE_FIT"
        const displayText = MealUtil.isAddMealSupported(userContext) ? foodBooking.packId ? "Change / Add items" : "Add items" : "Change meal"
        const manageOptions: ManageOptions = {
            displayText: displayText,
            helperText: "",
            options: [
                {
                    displayText: displayText,
                    action: ActionUtil.changeMealPage(foodBooking.productId, foodBooking.deliveryDate, mealSlot, foodBooking.fulfilmentId, foodBooking.packId === undefined),
                    type: "CHANGE_MEAL",
                    isEnabled: isEnabled
                }
            ],
            icon: "CHANGE_MEAL",
        }

        return {
            widgetType: "MANAGE_OPTIONS_WIDGET",
            manageOptions: manageOptions,
            isDisabled: !isEnabled,
            meta: this.getMealProductMeta(foodBooking)
        }
    }

    private getChangeAddressWidget(userContext: UserContext, foodBooking: FoodBooking, isCancelCutOffPassed: boolean, isPackActive: boolean, isDisabled: boolean = false): ManageOptionsWidget {
        let changeAddressText
        const tz = userContext.userProfile.timezone
        if (!isPackActive) {
            changeAddressText = `Pack is paused`
        }
        else if (!isCancelCutOffPassed) {
            const cancelCutoffTime = SlotUtil.getCancelCutOffByChannelOrSlot(foodBooking.deliverySlot ? foodBooking.deliverySlot.slotId : undefined, foodBooking.deliveryWindow, foodBooking.deliveryChannel, tz)
            changeAddressText = `Change address (cut-off: ${MealUtil.getCancelCutOffDisplayTextForTime(cancelCutoffTime)})`
        }
        else
            changeAddressText = "Delivery address"

        const isEnabled = !isCancelCutOffPassed && isPackActive && foodBooking.state !== "CANCELLED" && foodBooking.state !== "DELIVERED" && !foodBooking.address.kioskId && !isDisabled
        let addressText = foodBooking.address.addressLine1 ? foodBooking.address.addressLine1 + ", " : ""
        addressText = addressText + foodBooking.address.addressLine2
        let manageOptions: ManageOptions = {
            displayText: changeAddressText,
            helperText: foodBooking.address.addressType,
            subHelper: addressText,
            options: [
                {
                    displayText: "Change address",
                    action: "curefit://selectAddress",
                    type: "CHANGE_MEAL_ADDRESS",
                    isEnabled: isEnabled
                }
            ],
            icon: "DROP_DOWN",
        }

        manageOptions = {
            displayText: foodBooking.address.addressType + ": " + addressText,
            options: [
                {
                    displayText: "Change address",
                    action: "curefit://selectAddress",
                    type: "CHANGE_MEAL_ADDRESS",
                    isEnabled: isEnabled
                }
            ],
            icon: "LOCATION",
            secondaryIcon: "DROP_DOWN",
        }


        return {
            widgetType: "MANAGE_OPTIONS_WIDGET",
            manageOptions: manageOptions,
            invertStyle: true,
            isDisabled: !isEnabled,
            meta: this.getMealProductMeta(foodBooking)
        }
    }

    private getChangeInstructionWidget(foodBooking: FoodBooking, isCancelCutOffPassed: boolean, isPackActive: boolean, userContext: UserContext): ActionSheetWidget {
        const isEnabled = !isCancelCutOffPassed && isPackActive && foodBooking.state !== "CANCELLED" && foodBooking.state !== "DELIVERED"
        const address = foodBooking.address
        let actionSheet: ActionSheet = {
            selectedOption: foodBooking.address ? EatUtils.findDeliveryInstructionId(foodBooking.address.eatDeliveryInstruction) : 0,
            options: EatUtils.convertDeliveryInstructionsToOptions(),
            meta: { productId: foodBooking.productId, fulfilmentId: foodBooking.fulfilmentId, date: foodBooking.deliveryDate, packId: foodBooking.packId },
            actionType: "UPDATE_MEAL_INSTRUCTION"
        }
        if (MealUtil.isCutleryChangeSupported(userContext)) {
            actionSheet = EatUtil.getCutleryInstructionAction(address.eatDeliveryInstruction, actionSheet.meta, "UPDATE_MEAL_INSTRUCTION")
        }
        return {
            widgetType: "ACTION_SHEET_WIDGET",
            icon: "INSTRUCTION",
            isDisabled: !isEnabled,
            action: actionSheet
        }
    }

    private getMealProductMeta(foodBooking: FoodBooking): any {
        return {
            fulfilmentId: foodBooking.fulfilmentId,
            date: foodBooking.deliveryDate,
            packId: foodBooking.packId,
            productId: foodBooking.productId
        }
    }

    private getNutritionWidget(nutritionInfo: any, isMultimeal: boolean): WidgetView {
        if (isNaN(Math.ceil(nutritionInfo.Calories["Total Calories"])) || Math.ceil(nutritionInfo.Calories["Total Calories"]) < 1) {
            return
        }
        const nutritionWidget: WidgetView & {
            title: string,
            values: {
                unit: string, type: string, quantity: number
            }[]
        } = {
            widgetType: "NUTRITION_WIDGET",
            title: "Nutrition" + (isMultimeal ? " per serving" : ""),
            values: [
                {
                    type: "Calories",
                    quantity: Math.ceil(nutritionInfo.Calories["Total Calories"]),
                    unit: "Cal"
                },
                {
                    type: "Protein",
                    quantity: Math.ceil(nutritionInfo.Protein),
                    unit: "g"
                },
                {
                    type: "Fat",
                    quantity: Math.ceil(nutritionInfo.Fat["Total Fat"]),
                    unit: "g"
                },
                {
                    type: "Carbs",
                    quantity: Math.ceil(nutritionInfo.Carbs["Total Carbs"]),
                    unit: "g"
                },
                {
                    type: "Fibre",
                    quantity: Math.ceil(nutritionInfo.Fibre),
                    unit: "g"
                }
            ]
        }
        return nutritionWidget
    }

    private async getMealSummaryWidget(userContext: UserContext, isMealAvailable: boolean, offerIds: string[], productPrice: ProductPrice, enableBuyOption: boolean, date: string, issuesMap: Map<string, CustomerIssueType[]>, product: Product, foodBooking: FoodBooking, isPackActive: boolean): Promise<WidgetView> {
        const tz = userContext.userProfile.timezone
        const mealSummaryWidget: WidgetView & {
            isMealAvailable: boolean
            productId: string
            packId?: string
            fulfilmentId?: string
            date?: string
            image: string,
            title: string,
            subTitle: string,
            price?: ProductPrice,
            orderId?: string,
            offerId?: string,
            offerIds?: string[],
            meta?: any
            deliveryDetail?: {
                date: string
                slotStart: string
                slotEnd: string
                state: string
                cutOffMessage?: string
            },
            isVeg: boolean,
            manageOptions?: ManageOptions,
            images?: string[]
        } = {
            widgetType: "MEAL_SUMMARY",
            image: UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, "FOOD", "HERO", product.imageVersion),
            isVeg: AppUtil.isMultiImageSupported(userContext) ? product.attributes["isVeg"] === "TRUE" : product.attributes["isVeg"],
            title: product.title,
            subTitle: "",
            productId: product.productId,
            price: productPrice,
            isMealAvailable: isMealAvailable,
            images: []
        }
        mealSummaryWidget.images.push(mealSummaryWidget.image)
        let imageNo: number = 1
        while (imageNo <= product.secondaryImageCount) {
            const imageVersion: number = product.secondaryImageVersion
            if (!_.isNil(imageVersion)) {
                const url: string = UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, "FOOD", "HERO", imageVersion, undefined, undefined, imageNo)
                mealSummaryWidget.images.push(url)
            }
            imageNo = imageNo + 1
        }
        if (!_.isEmpty(offerIds)) {
            mealSummaryWidget.offerId = offerIds[0]
            mealSummaryWidget.offerIds = offerIds
        }

        if (foodBooking) { // Already bought case
            mealSummaryWidget.orderId = foodBooking.orderId
            mealSummaryWidget.fulfilmentId = foodBooking.fulfilmentId
            mealSummaryWidget.date = foodBooking.deliveryDate
            mealSummaryWidget.packId = foodBooking.packId
            mealSummaryWidget.subTitle = this.getSummarySubTitle(foodBooking, userContext)
            const deliveryWindowStartMoment = TimeUtil.getMomentForDate(foodBooking.deliveryWindow.start, tz)
            const deliveryWindowEndMoment = TimeUtil.getMomentForDate(foodBooking.deliveryWindow.end, tz)
            mealSummaryWidget.deliveryDetail = {
                date: foodBooking.deliveryDate,
                slotStart: pad(deliveryWindowStartMoment.hours(), 2) + ":" + pad(deliveryWindowStartMoment.minutes(), 2) + ":00",
                slotEnd: pad(deliveryWindowEndMoment.hours(), 2) + ":" + pad(deliveryWindowEndMoment.minutes(), 2) + ":00",
                state: foodBooking.state
            }
            const isAlreadyCancelled: boolean = this.isAlreadyCancelled(foodBooking)
            const isCutOffPassed: boolean = SlotUtil.isCancelCutOffPassedForChannel(foodBooking.deliveryWindow, foodBooking.deliveryChannel, tz)
            const isPackOrder = foodBooking.packId ? true : false
            const mealManageOptions = await this.productBusiness.getMealManageOptions(userContext, foodBooking, issuesMap, isPackOrder, isPackActive, false, foodBooking.state !== "DELIVERED")
            if (!_.isEmpty(mealManageOptions.manageOptions.options))
                mealSummaryWidget.manageOptions = mealManageOptions.manageOptions
            mealSummaryWidget.meta = mealManageOptions.meta
            if (isCutOffPassed && foodBooking.deliveryType !== "ON_DEMAND") {
                const cancelCutoffTime = SlotUtil.getCancelCutOffByChannelOrSlot(foodBooking.deliverySlot ? foodBooking.deliverySlot.slotId : undefined, foodBooking.deliveryWindow, foodBooking.deliveryChannel, tz)
                mealSummaryWidget.deliveryDetail.cutOffMessage = `Meal cannot be modified after ${MealUtil.getCancelCutOffDisplayTextForTime(cancelCutoffTime)}`
            }

            if (isAlreadyCancelled && foodBooking.deliveryType !== "ON_DEMAND") {
                mealSummaryWidget.deliveryDetail.cutOffMessage = `Meal cancelled`
            }

        } else if (enableBuyOption) {  // Getting the slot options for single meal buy
            /*mealSummaryWidget.manageOptions = null
            {
                displayText: "Manage",
                options: []
            }
            */
            mealSummaryWidget.meta = {
                productId: product.productId
            }
            mealSummaryWidget.date = date

            // const deliverySlots = SlotUtil.getDeliverySlots(mealSlot)
            // TODO: It's been called multiple time. Refactor needed.
            // const availableDeliverySlots = this.getAvailableDeliveySlots(mealSlot, date, inventoryToCheck)
            /*availableDeliverySlots.forEach(deliverySlot => {
                mealSummaryWidget.manageOptions.options.push({
                    isEnabled: true,
                    displayText: MealUtil.getSlotDisplayText(deliverySlot),
                    type: "MEAL_SLOT_OPTION",
                    meta: deliverySlot
                })
            })

            const firstAvailableSlot = mealSummaryWidget.manageOptions.options[0].meta

            mealSummaryWidget.deliveryDetail = {
                date: date,
                slotStart: pad(firstAvailableSlot.displayStartTime.hour, 2) + ":" + pad(firstAvailableSlot.displayStartTime.min, 2) + ":00",
                slotEnd: pad(firstAvailableSlot.displayEndTime.hour, 2) + ":" + pad(firstAvailableSlot.displayEndTime.min, 2) + ":00",
                state: "NOT_BOUGHT"
            }
*/
        }
        return mealSummaryWidget
    }


    private getSummarySubTitle(foodBooking: FoodBooking, userContext: UserContext): string {
        const timezone = userContext.userProfile.timezone
        if (foodBooking.state === "DELIVERED")
            return `Meal delivered ${TimeUtil.getDayText(foodBooking.deliveryDate, timezone).toLowerCase()}`
        else if (foodBooking.state === "CANCELLED")
            return "Meal cancelled"
        else if (foodBooking.state === "SCANNING")
            return "Meal arrived"
        else if (foodBooking.state === "REJECTED")
            return ""
        else {
            if (foodBooking.deliveryType === "ON_DEMAND") {
                if (_.isNil(foodBooking.state) || foodBooking.state === "ACCEPTING" || foodBooking.state === "LOT_ASSIGNED" || foodBooking.state === "NOT_STARTED")
                    return `Arriving ${MealUtil.getSlotDisplayTextFoodBooking(foodBooking)}`
                else
                    return ""
            } else {
                return `Arriving ${TimeUtil.getDayText(foodBooking.deliveryDate, timezone).toLowerCase()}, ${MealUtil.getSlotDisplayTextFoodBooking(foodBooking)}`
            }
        }
    }

    private isAlreadyCancelled(foodBooking: FoodBooking): boolean {
        if (foodBooking) {
            if (foodBooking.state === "CANCELLED" || foodBooking.state === "REJECTED") {
                return true
            }
        }
        return false
    }

    private isAlreadyDelivered(foodBooking: FoodBooking): boolean {
        if (foodBooking) {
            if (foodBooking.state === "DELIVERED") {
                return true
            }
        }
        return false
    }

    private getMealDetailWidget(productDescription: string, components: { name: string, description: string }[]): DescriptionWidget {

        const descriptions: InfoCard[] = []
        if (productDescription) {
            descriptions.push({
                subTitle: productDescription
            })
        }

        if (!_.isEmpty(components)) {
            descriptions.push({
                title: "Your meal will include:"
            })
            components.forEach(component => {
                if (component.name) {
                    descriptions.push({
                        title: component.name
                    })
                }

                if (component.description) {
                    descriptions.push({
                        subTitle: component.description
                    })
                }
            })
        }
        return new DescriptionWidget(descriptions)
    }

    private getPackInfoWidget(foodPack: FoodPack, foodPackBooking: FoodBooking, userContext: UserContext): WidgetView {
        const seoParams: SeoUrlParams = {
            productName: foodPack.title
        }
        const action: ActionCard = {
            title: "SEE PACK",
            action: ActionUtil.foodPack(foodPack.productId, foodPackBooking ? foodPackBooking.fulfilmentId : "", undefined, false, userContext.sessionInfo.userAgent, seoParams)
        }
        return new PackInfoWidget(foodPack.productId, foodPack.title, action)
    }

    // ----------------------------------------------------------V2 Widgets---------------------------------------------------------- //

    // Will be used later
    private getNutritionWidgetV2(nutritionInfo: any, userContext: UserContext): WidgetView {
        if (isNaN(Math.ceil(nutritionInfo.Calories["Total Calories"])) || Math.ceil(nutritionInfo.Calories["Total Calories"]) < 1) {
            return
        }
        const nutritionWidget: WidgetView & {
            title: string,
            qty?: number,
            unit?: string,
            calories: string,
            values: {
                unit: string, color: string, type: string, quantity: number
            }[]
        } = {
            widgetType: "NUTRITION_WIDGETV2",
            title: "Nutritional Info.",
            calories: Math.ceil(nutritionInfo.Calories["Total Calories"]) + " Cal",
            qty: nutritionInfo.qty,
            unit: nutritionInfo.unit,
            values: [
                {
                    type: "Protein",
                    color: "#4fc6ff",
                    quantity: Math.ceil(nutritionInfo.Protein),
                    unit: "g"
                },
                {
                    type: "Fat",
                    color: "#ffa522",
                    quantity: Math.ceil(nutritionInfo.Fat["Total Fat"]),
                    unit: "g"
                },
                {
                    type: "Carbs",
                    color: "#f46da0",
                    quantity: Math.ceil(nutritionInfo.Carbs["Total Carbs"]),
                    unit: "g"
                },
                {
                    type: "Fibre",
                    color: "#916cae",
                    quantity: Math.ceil(nutritionInfo.Fibre),
                    unit: "g"
                }
            ],
            layoutProps: AppUtil.isWeb(userContext) ? {
                showDivider: true
            } : undefined
        }
        return nutritionWidget
    }

    private async getMealSummaryWidgetV2(userContext: UserContext, isMealAvailable: boolean, isCafe: boolean, offerIds: string[], productPrice: ProductPrice, enableBuyOption: boolean, date: string, issuesMap: Map<string, CustomerIssueType[]>, product: FoodProduct, foodBooking: FoodBooking, isPackActive: boolean, maxCartSize: number, singleOffersResponse: FoodSinglePriceOfferResponse, stockInventory: { [p: string]: { [p: string]: { left: number; total: number } } }, mealSlot: MenuType, nutritionInfo: any, listingBrand?: ListingBrandIdType): Promise<WidgetView> {
        const tz = userContext.userProfile.timezone
        let stock: number = 0
        if (enableBuyOption && stockInventory) {
            stock = OfferUtil.getAvailabilityV1(product, stockInventory).left
            stock = product.maxCartQty ? Math.min(stock, product.maxCartQty) : stock
            stock = productPrice.listingPrice === 0 && stock > 0 ? 1 : stock
        }
        const imageProductId = EatUtil.getProductId(product)
        const imageNumber = 1
        const mealSummaryWidget: WidgetView & {
            isMealAvailable: boolean
            productId: string
            packId?: string
            fulfilmentId?: string
            date?: string
            images: string[],
            title: string,
            titleWithoutUnits: string,
            subTitle: string,
            price?: ProductPrice,
            orderId?: string,
            offerId?: string,
            offerIds?: string[],
            meta?: any
            calories: any
            nutritionalQty: any
            nutritionalUnit: any
            qty?: number
            unit?: string,
            displayUnitQty?: string,
            variantTitle?: string,
            stock: number,
            maxCartSize: number,
            mealSlot: any,
            deliveryDetail?: {
                date: string
                slotStart: string
                slotEnd: string
                state: string
                cutOffMessage?: string
            },
            isVeg: boolean,
            manageOptions?: ManageOptions
            variants: any[],
            actions: Action[]
        } = {
            widgetType: "MEAL_SUMMARYV2",
            images: [UrlPathBuilder.getSingleImagePath(imageProductId, "FOOD", "HERO", product.imageVersion, imageNumber)],
            isVeg: product.attributes["isVeg"],
            title: product.title,
            titleWithoutUnits: product.titleWithoutUnits,
            subTitle: "",
            qty: product.servingQty,
            unit: product.servingUnit,
            displayUnitQty: EatUtil.getQuantityTitle(product),
            variantTitle: EatUtil.getVariantTitle(product, listingBrand),
            productId: product.productId,
            price: productPrice,
            isMealAvailable: isMealAvailable,
            nutritionalQty: nutritionInfo.qty,
            nutritionalUnit: nutritionInfo.unit,
            variants: [],
            calories: Math.ceil(nutritionInfo.Calories["Total Calories"]) + " Cal",
            maxCartSize: maxCartSize,
            stock: stock,
            mealSlot: {
                id: mealSlot,
                name: capitalizeFirstLetter(mealSlot.toLowerCase())
            },
            hasDividerBelow: false,
            actions: !userContext.sessionInfo.isUserLoggedIn ? [{
                actionType: "SHOW_ALERT_MODAL",
                title: "ADD", meta: {
                    title: "Login Required!",
                    subTitle: "Please login to continue",
                    actions: [{ actionType: "LOGOUT", title: "Login" }]
                }
            }] : (enableBuyOption ? [{
                actionType: "ADD_TO_CART",
                url: "curefit://cartcheckout",
                title: "ADD",
                image: UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, "FOOD", "HERO", product.imageVersion, imageNumber),
                productId: product.productId,
                mealSlot: {
                    id: mealSlot,
                    name: capitalizeFirstLetter(mealSlot.toLowerCase())
                },
                date: date,
                stock: stock,
                price: productPrice,
                maxCartSize: maxCartSize,
                listingBrand: listingBrand,
                productName: product.title
            }] : [])
        }

        // Add variants only for whole_fit and eat_fit
        if ((listingBrand === "WHOLE_FIT" || (!isCafe && AppUtil.isEatVariantsSupported(userContext) && listingBrand === "EAT_FIT")) && !_.isEmpty(product.variants) && _.isNil(foodBooking)) {
            // Filter variants based on listing  brand and add price info
            for (const productVariant of product.variants) {
                if (listingBrand && !productVariant.listingBrands.includes(listingBrand)) {
                    continue
                }
                const variantProduct = await this.catalogueService.getProduct(productVariant.productId)
                const variant: any = clone(productVariant)
                const offerAndPriceDetails = OfferUtil.getSingleOfferAndPrice(variantProduct, date, singleOffersResponse, "ALL")
                variant.stock = OfferUtil.getAvailabilityV1(variantProduct, stockInventory).left
                const total = OfferUtil.getAvailabilityV1(variantProduct, stockInventory).total
                const offerPrice = offerAndPriceDetails.price
                const offers = offerAndPriceDetails.offers
                const variantOfferIds = !_.isEmpty(offers) ? _.map(offers, offer => {
                    return offer.offerId
                }) : undefined
                variant.offerId = !_.isEmpty(variantOfferIds) ? variantOfferIds[0] : undefined
                variant.offers = variantOfferIds
                variant.price = offerPrice
                variant.displayUnitQty = EatUtil.getQuantityTitle(variantProduct)
                variant.variantTitle = EatUtil.getVariantTitle(variantProduct, listingBrand)
                if (total > 0) {
                    mealSummaryWidget.variants.push(variant)
                }
            }
        }

        const secondaryImageUrls = []
        if (!_.isNil(product.secondaryImageVersion) && product.secondaryImageCount > 0) {
            for (let i = 1; i <= product.secondaryImageCount; i++) {
                const secondaryImage = UrlPathBuilder.getSingleImagePath(imageProductId, "FOOD", "HERO", product.secondaryImageVersion, undefined, null, i)
                secondaryImageUrls.push(secondaryImage)
            }
            mealSummaryWidget.images.push(...secondaryImageUrls)
        }
        if (!_.isEmpty(offerIds)) {
            mealSummaryWidget.offerId = offerIds[0]
            mealSummaryWidget.offerIds = offerIds
        }
        if (foodBooking) { // Already bought case
            mealSummaryWidget.orderId = foodBooking.orderId
            mealSummaryWidget.fulfilmentId = foodBooking.fulfilmentId
            mealSummaryWidget.date = foodBooking.deliveryDate
            mealSummaryWidget.packId = foodBooking.packId
            mealSummaryWidget.subTitle = this.getSummarySubTitle(foodBooking, userContext)
            const deliveryWindowStartMoment = TimeUtil.getMomentForDate(foodBooking.deliveryWindow.start, tz)
            const deliveryWindowEndMoment = TimeUtil.getMomentForDate(foodBooking.deliveryWindow.end, tz)
            mealSummaryWidget.deliveryDetail = {
                date: foodBooking.deliveryDate,
                slotStart: pad(deliveryWindowStartMoment.hours(), 2) + ":" + pad(deliveryWindowStartMoment.minutes(), 2) + ":00",
                slotEnd: pad(deliveryWindowEndMoment.hours(), 2) + ":" + pad(deliveryWindowEndMoment.minutes(), 2) + ":00",
                state: foodBooking.state
            }
            const isAlreadyCancelled: boolean = this.isAlreadyCancelled(foodBooking)
            const isCutOffPassed: boolean = SlotUtil.isCancelCutOffPassedForChannel(foodBooking.deliveryWindow, foodBooking.deliveryChannel, tz)
            const isPackOrder = foodBooking.packId ? true : false
            const mealManageOptions = await this.productBusiness.getMealManageOptions(userContext, foodBooking, issuesMap, isPackOrder, isPackActive, false, foodBooking.state !== "DELIVERED")
            if (!_.isEmpty(mealManageOptions.manageOptions.options))
                mealSummaryWidget.manageOptions = mealManageOptions.manageOptions
            mealSummaryWidget.meta = mealManageOptions.meta
            if (isCutOffPassed && foodBooking.deliveryType !== "ON_DEMAND") {
                const cancelCutoffTime = SlotUtil.getCancelCutOffByChannelOrSlot(foodBooking.deliverySlot ? foodBooking.deliverySlot.slotId : undefined, foodBooking.deliveryWindow, foodBooking.deliveryChannel, tz)
                mealSummaryWidget.deliveryDetail.cutOffMessage = `Meal cannot be modified after ${MealUtil.getCancelCutOffDisplayTextForTime(cancelCutoffTime)}`
            }

            if (isAlreadyCancelled && foodBooking.deliveryType !== "ON_DEMAND") {
                mealSummaryWidget.deliveryDetail.cutOffMessage = `Meal cancelled`
            }

        } else if (enableBuyOption) {  // Getting the slot options for single meal buy
            /*mealSummaryWidget.manageOptions = null
            {
                displayText: "Manage",
                options: []
            }
            */
            mealSummaryWidget.meta = {
                productId: product.productId
            }
            mealSummaryWidget.date = date
        }
        return mealSummaryWidget
    }

    public async getCafeMealSummaryWidgetV2(userContext: UserContext, product: Product, listingBrand: ListingBrandIdType,
                                  isMealAvailable: boolean, nutritionInfo: any, maxCartSize: number, enableBuy: boolean,
                                      date: string, stock: number, kioskId: string): Promise<WidgetView> {
        const imageProductId = EatUtil.getProductId(product)
        const imageNumber = 1
        const mealSummaryWidget: WidgetView & {
            isMealAvailable: boolean
            productId: string
            packId?: string
            fulfilmentId?: string
            date?: string
            images: string[],
            title: string,
            titleWithoutUnits: string,
            subTitle: string,
            price?: ProductPrice,
            orderId?: string,
            offerId?: string,
            offerIds?: string[],
            meta?: any
            calories: any
            nutritionalQty: any
            nutritionalUnit: any
            qty?: number
            unit?: string,
            displayUnitQty?: string,
            variantTitle?: string,
            stock: number,
            maxCartSize: number,
            mealSlot: any,
            deliveryDetail?: {
                date: string
                slotStart: string
                slotEnd: string
                state: string
                cutOffMessage?: string
            },
            isVeg: boolean,
            manageOptions?: ManageOptions
            variants: any[],
            actions: Action[]
        } = {
            widgetType: "MEAL_SUMMARYV2",
            images: [UrlPathBuilder.getSingleImagePath(imageProductId, "FOOD", "HERO", product.imageVersion, imageNumber)],
            isVeg: product.attributes["isVeg"],
            title: product.title,
            titleWithoutUnits: product.titleWithoutUnits,
            subTitle: "",
            qty: product.servingQty,
            unit: product.servingUnit,
            displayUnitQty: EatUtil.getQuantityTitle(product),
            variantTitle: EatUtil.getVariantTitle(product, listingBrand),
            productId: product.productId,
            price: product.price,
            isMealAvailable: isMealAvailable,
            nutritionalQty: nutritionInfo.qty,
            nutritionalUnit: nutritionInfo.unit,
            variants: [],
            calories: Math.ceil(nutritionInfo.Calories["Total Calories"]) + " Cal",
            maxCartSize: maxCartSize,
            stock: stock,
            date: date,
            mealSlot: {
                id: "ALL",
                name: "All"
            },
            meta: {
                productId: product.productId
            },
            hasDividerBelow: false,
            actions: !userContext.sessionInfo.isUserLoggedIn ? [{
                actionType: "SHOW_ALERT_MODAL",
                title: "ADD", meta: {
                    title: "Login Required!",
                    subTitle: "Please login to continue",
                    actions: [{ actionType: "LOGOUT", title: "Login" }]
                }
            }] : (enableBuy ? [{
                actionType: "ADD_TO_CART",
                url: "curefit://cartcheckout",
                title: "ADD",
                image: UrlPathBuilder.getSingleImagePath( product.productId, "FOOD", "HERO", product.imageVersion, imageNumber),
                productId: product.productId,
                mealSlot: {
                    id: "ALL",
                    name: "All"
                },
                date: date,
                stock: stock,
                option: {
                    kioskId: kioskId
                },
                price: product.price,
                maxCartSize: maxCartSize,
                listingBrand: listingBrand,
                productName: product.title
            }] : [])
        }
        return mealSummaryWidget
    }

    private getIngredientWidget(ingredients: string[]): WidgetView {
        const ingredientWidget: WidgetView & {
            ingredients: string[],
        } = {
            widgetType: "INGREDIENT_WIDGET",
            ingredients: ingredients
        }
        return ingredientWidget
    }

    private populateV2Actions(mealDetailView: MealProductDetailPage, enableBuy: boolean, enableBuyOption: boolean, foodBooking: FoodBooking, userContext: UserContext, loggedoutAppFlow: boolean, websiteDetailPage: boolean, maxCartSize: number, product: FoodProduct, mealSlot: MenuType, date: string): MealProductDetailPage {
        if (loggedoutAppFlow) {
            const loginAction: Action = {
                actionType: "SHOW_ALERT_MODAL",
                title: "Login", meta: {
                    title: "Login Required!",
                    subTitle: "Please login to continue",
                    actions: [{ actionType: "LOGOUT", title: "Login" }]
                }
            }
            mealDetailView.pageActions.push(loginAction)
            mealDetailView.actions.push(loginAction)
        }
        return mealDetailView
    }

    private async addFitclubWidget(mealDetailView: MealProductDetailPage, isFitclubMember: boolean, foodBooking: FoodBooking, cartOffers: OfferV2[], offerPrice: ProductPrice, product: Product, userContext: UserContext, isCafe: boolean) {
        let fitCash: number
        let freeDelivery: boolean
        if (foodBooking) {
            const order = await this.omsApiClient.getOrder(foodBooking.orderId)
            freeDelivery = _.isNil(order.deliveryCharges) || order.deliveryCharges.total == 0
            fitCash = 0
        } else {
            const fitCashOffer = _.maxBy(_.filter(cartOffers, (offer: OfferV2) => { return offer.addons[0] && offer.addons[0].addonType === "FITCASH" }), "priority")
            const price = offerPrice ? offerPrice.listingPrice : product.price.listingPrice
            freeDelivery = _.some(cartOffers, (offer: OfferV2) => offer.noDeliveryCharge)
            fitCash = FitClubUtil.getFitCash(fitCashOffer, price, true)
        }
        let finalText: string
        if (foodBooking) {
            const fitcashText = fitCash > 0 ? `${fitCash} Fitcash` : ""
            const deliveryText = freeDelivery ? " and free delivery" : ""
            finalText = fitcashText + deliveryText + " earned on this order"
        } else {
            const deliveryText = ""
            const fitcashText = fitCash > 0 ? `Upto ${fitCash} Fitcash with this order` : ""
            const divider = (deliveryText !== "" && fitcashText !== "") ? " • " : ""
            finalText = fitcashText + divider + deliveryText
        }
        const freeDeliverySavings = freeDelivery ? DEFAULT_DELIVERY_CHARGE : 0
        if (AppUtil.isFitClubAllowed(userContext, isFitclubMember) && fitCash > 0 && !isCafe) {
            mealDetailView.widgets.push(new FitclubSavingsCalloutWidgetV2(userContext, fitCash, freeDeliverySavings, "Meal detail PAGE", true, true, !foodBooking))
        }
        if (fitCash > 0) {
            mealDetailView.widgets.push({
                widgetType: "FIT_CASH_SAVING_WIDGET",
                data: [{
                    text: finalText,
                    style: {
                        color: "#000",
                        fontSize: 12,
                        fontFamily: "BrandonText-Medium",
                        lineHeight: 16
                    }
                }],
                style: {
                    background: {
                        backgroundColor: "#eeebf0",
                        paddingVertical: 7,
                        paddingHorizontal: 20,
                        marginHorizontal: 25,
                        marginTop: 15
                    }
                },
                showDivider: false,
                hasDividerBelow: false
            })
        }
    }
}

export default MealDetailViewBuilder
