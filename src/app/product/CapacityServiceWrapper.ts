import { inject, injectable } from "inversify"
import { LatLong } from "@curefit/base-common"
import { ListingBrandIdType, MenuType } from "@curefit/eat-common"
import { User } from "@curefit/user-common"
import * as _ from "lodash"
import { ICapacityService, MASTERCHEF_CLIENT_TYPES } from "@curefit/masterchef-client"
import { MASTERCHEF_MODELS_TYPES } from "@curefit/masterchef-models"
import { SlotUtil } from "@curefit/eat-util"
import { CacheHelper } from "../util/CacheHelper"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { TimeUtil, Timezone } from "@curefit/util-common"

@injectable()
class CapacityServiceWrapper {

    constructor(@inject(MASTERCHEF_CLIENT_TYPES.CapacityService) private capacityService: ICapacityService,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper) {

    }

    formatResponseForNonInternalUser(user: User, response: { [campaignType: string]: { [date: string]: string[] } }) {

        if (!(!_.isNil(user) && user.isInternalUser)) {
            const keysstr = Object.keys(response)
            for (let i: number = 0; i < keysstr.length; i++) {
                const vals = response[keysstr[i]]
                const valsDate = Object.keys(vals)
                for (let m = 0; m < valsDate.length; m++) {
                    let slots = vals[valsDate[m]]
                    slots = slots.filter((x) => {
                        if (x === SlotUtil.ON_DEMAND_SLOT)
                            return false
                        return true
                    })
                    vals[valsDate[m]] = slots
                }

            }
        }
        return response
    }


    async getNextAvailableSlotsForPackByType(userId: string, packId: string, areaId: string, latLong?: LatLong, timezone: Timezone = TimeUtil.IST_TIMEZONE): Promise<{ [campaignType: string]: { [date: string]: string[] } }> {
        const response: { [campaignType: string]: { [date: string]: string[] } } = await this.capacityService.getNextAvailableSlotsForPackByType(packId, areaId, latLong)

        // const user = await this.userService.getUser(userId)

        // return this.formatResponseForNonInternalUser(user, response)
        return response
    }

    async getNextAvailableSlotsForPackForMealSlot(userId: string, packId: string, areaId: string, mealSlot: MenuType, latLong?: LatLong,
        listingBrand?: ListingBrandIdType, timezone: Timezone = TimeUtil.IST_TIMEZONE): Promise<{ [campaignType: string]: { [date: string]: string[] } }> {
        const response: { [campaignType: string]: { [date: string]: string[] } } = await this.capacityService.getNextAvailableSlotsForPackForMealSlot(packId, areaId, mealSlot, latLong, listingBrand)
        // const user = await this.userService.getUser(userId)
        // return this.formatResponseForNonInternalUser(user, response)
        return response

    }


}

export default CapacityServiceWrapper
