import {
    CustomerIssueType,
} from "@curefit/issue-common"
import {
    FoodPack, ListingBrandIdType,
} from "@curefit/eat-common"
import {
    MealSlot,
} from "@curefit/eat-common"
import {
    MenuType,
} from "@curefit/eat-common"
import { FoodProduct as Product } from "@curefit/eat-common"
import {
    ProductMealSlotMenu,
} from "@curefit/eat-common"
import {
    ProductMenu,
} from "@curefit/eat-common"
import { ActionType, MealActionType, WidgetView } from "../common/views/WidgetView"
import { User } from "@curefit/user-common"
import { ProductType } from "@curefit/product-common"
import { PreferredLocation } from "@curefit/userinfo-common"
import { FoodBooking, FoodPackBooking } from "@curefit/shipment-common"
import { Action, AlertInfo, ManageOptionPayload, ManageOptions } from "../common/views/WidgetView"
import { CultBooking, CultMembership, CultWaitlistBooking } from "@curefit/cult-common"
import { BookingDetail, CallToAction, TimelineActivityV2, ConsultationInstructionResponse, Patient } from "@curefit/albus-client"
import { UserContext } from "@curefit/userinfo-common"
import { MoneyBackOfferDetail } from "../cult/CultBusiness"
import { DeliverySlot } from "@curefit/eat-common"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { BaseOrder, FoodFulfilment } from "@curefit/order-common"
import { FoodSinglePriceOfferResponse, FoodPackOffersResponseV2 } from "@curefit/offer-common"
import { ProductInventory } from "@curefit/masterchef-models"
import { ChangeMealItemWidget } from "../page/PageWidgets"
import * as _ from "lodash"
import { ISegmentService, RecommendedMeals } from "@curefit/vm-models"
import { HamletBusiness } from "@curefit/hamlet-node-sdk"
import { Membership } from "@curefit/membership-commons"
import { MembershipTransferable } from "../cult/cultpackpage/CultPackCommonViewBuilder"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"

export interface MenuAvailabilityResult {
    day: string
    inventoryResult: { [campaignType: string]: { [packId: string]: { left: number, total: number } } }
    menus: ProductMenu[]
}

export interface CurrentMenuAvailabilityResult {
    inventoryResult: { [campaignType: string]: { [packId: string]: { left: number, total: number } } }
    currentMenu: ProductMealSlotMenu[],
    menuType: MenuType
}

interface IProductBusiness {
    getNextAvailableMenu(userContext: UserContext, mealSlot: MealSlot, preferredLocation: PreferredLocation, excludeHiddenItems?: boolean): Promise<MenuAvailabilityResult>
    // enableProductOptions is kept to cater to old app where we need to show product options as well under manage setting icon. We can clean that up once we kill old app
    getChangeMealOption(foodBooking: FoodBooking, userContext: UserContext): ManageOptionPayload
    getMealReportIssue(userContext: UserContext, foodBooking: FoodBooking, issuesMap: Map<string, CustomerIssueType[]>, isPackOrder: boolean): Promise<{ manageOptions: ManageOptions, meta: any }>,
    getMealManageOptions(userContext: UserContext, foodBooking: FoodBooking, issuesMap: Map<string, CustomerIssueType[]>,
        isPackOrder: boolean, isPackActive: boolean, enableProductOptions: boolean, enableReportIssue: boolean, isTodayScreen?: boolean, onDemand?: boolean): Promise<{ manageOptions: ManageOptions, meta: any }>
    getCultManageOptions(userContext: UserContext, productType: ProductType, cultBooking: CultBooking, issuesMap: Map<string, CustomerIssueType[]>, enableProductOptions: boolean,
        enableReportIssue: boolean, cafeFoodDetails?: FoodFulfilment): Promise<{ manageOptions: ManageOptions, meta: any }>
    getCultWaitlistManageOptions(userContext: UserContext, productType: ProductType, cultWaitlistBooking: CultWaitlistBooking, isWaitlistExtensionSupported?: boolean): Promise<{ manageOptions: ManageOptions, meta: any }>
    getCultPackManageOptions(userContext: UserContext, packInfo: OfflineFitnessPack, membership: Membership, issuesMap: Map<string, CustomerIssueType[]>, segmentService: ISegmentService, moneyBackOfferDetail?: MoneyBackOfferDetail, membershipTransferable?: MembershipTransferable): Promise<{ manageOptions: ManageOptions, meta: any }>
    getMealPackManageOptions(
        userContext: UserContext,
        menu: { [date: string]: string },
        packBooking: FoodPackBooking,
        issuesMap: Map<string, CustomerIssueType[]>,
        enableProductOptions: boolean,
        enableReportIssue: boolean, isWeb: boolean, packInfo: FoodPack):
        Promise<{ manageOptions: ManageOptions, meta: any }>
    getDiagnosticsManageOptions(bookingDetail: BookingDetail, actions: CallToAction[]): Action[]
    getTeleconsultationManageOptions(userContext: UserContext, user: User, enableActions: boolean, enableReportIssue: boolean, issuesMap: Map<string, CustomerIssueType[]>, bookingDetail: BookingDetail, orderId?: string, rootBooking?: BookingDetail): Action[]
    getUpcomingShipment(foodPackBooking: FoodPackBooking, timezone: Timezone): FoodBooking
    getChangeableShipmentsFromFutureBookings(futureBookings: FoodBooking[], userContext: UserContext): Map<FoodBooking, boolean>
    getCancelDifference(foodBooking: FoodBooking): Promise<number>
    getPrescriptionManageOptions(userContext: UserContext, enableActions: boolean, enableReportIssue: boolean, issuesMap: Map<string, CustomerIssueType[]>, bookingDetail: BookingDetail, orderId?: string): Action[]
    getCancelManageOptions(foodBooking: FoodBooking,
        isPackOrder: boolean, isPackActive: boolean, isTodayScreen?: boolean, onDemand?: boolean, isCartPage?: boolean, timezone?: string): Promise<{ manageOptions: ManageOptions, meta: any }>
    filterOutAddMealHiddenProducts(menuList: ProductMenu[]): Promise<ProductMenu[]>
    getOrderStatusForFoodBooking(foodBooking: FoodBooking, userContext: UserContext): { eta: Date, status: string, statusText: string }
    getPossibleActionsForFoodBooking(foodBooking: FoodBooking, userContext: UserContext, foodFulfilment?: FoodFulfilment, date?: string, supportedActionTypes?: MealActionType[], foodPackBooking?: FoodPackBooking): Promise<Action[]>
    getNavigationUrlForFoodBooking(userContext: UserContext, foodBooking: FoodBooking): Promise<string>
    isValidAddOn(products: Product[], addOn: Product): boolean
    getTeleconsultationManageOptionsV2(userContext: UserContext, user: User, activity: TimelineActivityV2, consultationInstruction: ConsultationInstructionResponse[], patientsList: Patient[], hamletBusiness?: HamletBusiness): Promise<Action[]>
    getDiagnosticsManageOptionsV2(activity: TimelineActivityV2): Action[]
    getFoodMarketplaceManageOptions(userContext: UserContext, gnadalfBooking: any, issuesMap: Map<string, CustomerIssueType[]>): Promise<{ manageOptions: ManageOptions, meta: any }>
}
export default IProductBusiness
