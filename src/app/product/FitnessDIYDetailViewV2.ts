import * as _ from "lodash"
import {
    ColorCodedTagsWidget,
    Header,
    InfoCard,
    ProductFeedbackWidget,
    ProductListWidget,
    Tag
} from "../common/views/WidgetView"
import { ActivityType } from "@curefit/product-common"
import { FoodProduct as Product } from "@curefit/eat-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { Feedback } from "@curefit/feedback-common"
import FeedbackPageConfigV2Cache from "../ugc/FeedbackPageConfigV2Cache"
import ContentDetailViewV2 from "./ContentDetailViewV2"
import { DIYFitnessProductExtended, DIYUserFitnessPack } from "@curefit/diy-common"

class FitnessDIYDetailViewV2 extends ContentDetailViewV2 {
    private constructor(userAgent: UserAgent, appVersion: number, userPack: DIYUserFitnessPack, sessionId: string, sessionMap: { [productId: string]: Product }, feedback?: Feedback, feedbackPageConfigV2Cache?: FeedbackPageConfigV2Cache, feedbackQuestion?: string) {
        super(userAgent, userPack, sessionMap)
        const session = <DIYFitnessProductExtended>sessionMap[sessionId]
        this.widgets.push(this.getDiySummaryWidget(session))
        this.widgets.push(this.getSessionPlayWidget(userPack, sessionMap, sessionId))
        if (feedback && (feedback.rating === "NOT_RATED" || feedback.rating === "DISMISSED")) {
            // todo check product type
            this.widgets.push(new ProductFeedbackWidget(feedbackQuestion, feedback.feedbackId))
        }

        if (!_.isEmpty(session.exercises)) {
            this.widgets.push(this.getExercisesWidget(session.exercises))
        }

        if (!_.isEmpty(session.benefits)) {
            this.widgets.push(this.getBenefitsWidget(session.benefits))
        }

        if (userPack.fulfilment && userPack.fulfilment.status === "SUBSCRIBED") {
            this.widgets.push(this.getPackProgressActionCard(userAgent, userPack))
        }
    }

    public static async getView(userAgent: UserAgent, appVersion: number, userPack: DIYUserFitnessPack, sessionId: string, sessionMap: { [productId: string]: Product }, feedback?: Feedback, feedbackPageConfigV2Cache?: FeedbackPageConfigV2Cache): Promise<FitnessDIYDetailViewV2> {
        const question: string = await feedbackPageConfigV2Cache.getQuestionV2(feedback)
        const view: FitnessDIYDetailViewV2 = new FitnessDIYDetailViewV2(userAgent, appVersion, userPack, sessionId, sessionMap, feedback, feedbackPageConfigV2Cache, question)
        return view
    }

    private getBenefitsWidget(benefits: string[]): ProductListWidget {
        const header: Header = {
            title: "Benefits"
        }

        const infoCards: InfoCard[] = []
        benefits.forEach(benefit => {
            infoCards.push({
                subTitle: benefit
            })
        })
        return new ProductListWidget("SMALL", header, infoCards)
    }

    protected getPackScreenBaseURL(): string {
        return "curefit://cultdiypack"
    }

    private getExercisesWidget(exercises: string[]): ColorCodedTagsWidget {
        const header: Header = {
            title: "Exercises"
        }

        const tags: Tag[] = exercises.map(exercise => {
            return { title: exercise }
        })
        return new ColorCodedTagsWidget(header, tags)
    }

    protected getActivityScreenBaseURL(): string {
        return "curefit://diycultsingles"
    }

    protected getActivityType(): ActivityType {
        return "DIY_FITNESS"
    }
}

export default FitnessDIYDetailViewV2
