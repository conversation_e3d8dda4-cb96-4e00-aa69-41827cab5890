import {
    DescriptionWidget,
    DiyPlaySessionWidget,
    DiySummaryWidget,
    InfoCard,
    WidgetView
} from "../common/views/WidgetView"
import { PackProgressActionCard } from "../page/PageWidgets"
import { UrlPathBuilder } from "@curefit/product-common"
import AtlasUtil from "../util/AtlasUtil"
import * as momentTz from "moment-timezone"
import { FoodProduct as Product } from "@curefit/eat-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { ActivityType } from "@curefit/product-common"
import { DIYProduct, DIYUserFitnessPack, DIYUserMeditationPack } from "@curefit/diy-common"
import { ActionUtil, ActionUtilV1, SeoUrlParams } from "@curefit/base-utils"

abstract class ContentDetailViewV2 {
    constructor(userAgent: UserAgent, userPack: (DIYUserFitnessPack | DIYUserMeditationPack),
        sessionMap: { [productId: string]: Product }) {
    }

    protected getDiySummaryWidget(session: DIYProduct): DiySummaryWidget {
        const diySummaryWidget: DiySummaryWidget = new DiySummaryWidget()
        diySummaryWidget.title = session.title
        diySummaryWidget.productType = session.productType
        diySummaryWidget.content = {
            id: session.contentId,
            image: UrlPathBuilder.prefixSlash(session.imageDetails.heroImage),
            type: session.contentType,
            format: session.contentFormat,
            URL: session.contentType === "audio" ? UrlPathBuilder.getAudioPath(session.contentId, session.contentFormat) :
                UrlPathBuilder.getVideoPath(session.contentId, session.contentFormat),
            absoluteUrl: AtlasUtil.getDIYAbsoluteUrl(session)
        }

        // if (!_.isEmpty(session.exercises)) {
        //     diySummaryWidget.exercies = []
        //     activity.exercises.forEach(exercise => {
        //         diySummaryWidget.exercies.push(exercise.name)
        //     })
        // }
        diySummaryWidget.subTitle = momentTz.duration(session.duration).humanize()
        return diySummaryWidget
    }

    protected getDescriptionWidget(description: string): DescriptionWidget {
        const descriptions: InfoCard[] = []
        descriptions.push({ subTitle: description })
        return new DescriptionWidget(descriptions)
    }


    protected getSessionPlayWidget(userPack: (DIYUserFitnessPack | DIYUserMeditationPack), sessionMap: { [productId: string]: Product }, sessionId: string): DiyPlaySessionWidget {
        const pack = userPack.pack
        const currentSession = <DIYProduct>sessionMap[sessionId]
        const currentSessionNo = pack.sessionIds.indexOf(sessionId)
        const playSessionWidget: DiyPlaySessionWidget = {
            widgetType: "DIY_SESSION_PLAY_WIDGET",
            // isActive: activity.isActive,
            isActive: true, // Making all activity as enabled
            content: AtlasUtil.getDiyContentDetailsV2(currentSession),
            meta: AtlasUtil.getDiyMetaInfoV2(currentSession, pack)
        }

        if (currentSessionNo > 0) {
            const previousSessionId = pack.sessionIds[currentSessionNo - 1]
            const previousSession = <DIYProduct>sessionMap[previousSessionId]
            playSessionWidget.previousActivity = this.getActivityScreenBaseURL() + "?packId=" + pack.productId + "&activityId=" + previousSession.productId + "&activityType=" + previousSession.productType
        }

        if (currentSessionNo < pack.sessionIds.length - 1) {
            const nextSessionId = pack.sessionIds[currentSessionNo + 1]
            const nextSession = <DIYProduct>sessionMap[nextSessionId]
            playSessionWidget.nextActivity = this.getActivityScreenBaseURL() + "?packId=" + pack.productId + "&activityId=" + nextSession.productId + "&activityType=" + nextSession.productType
        }
        return playSessionWidget
    }

    protected getPackProgressActionCard(userAgent: UserAgent, userPack: (DIYUserFitnessPack | DIYUserMeditationPack)): (PackProgressActionCard & WidgetView) {
        const pack = userPack.pack
        const packFulfilment = userPack.fulfilment
        const seoParams: SeoUrlParams = {
          productName: pack.title
        }
        const packProgressWidget: (PackProgressActionCard & WidgetView) = {
            productType: AtlasUtil.getProductTypeForActivity(this.getActivityType()),
            title: pack.title,
            widgetType: "PACK_PROGRESS_WIDGET",
            subTitle: `${packFulfilment.completedProductIds ? packFulfilment.completedProductIds.length : 0} of ${pack.sessionIds.length} sessions done`,
            action: ActionUtilV1.diyPackProductPage(userPack.pack, userAgent),
            packId: pack.productId,
            state: packFulfilment.status,
            image: UrlPathBuilder.prefixSlash((userAgent === "APP" ? pack.imageDetails.magazineImage : pack.imageDetails.magazineWebImage)),
            total: pack.sessionIds.length,
            completed: packFulfilment.completedProductIds ? packFulfilment.completedProductIds.length : 0
        }
        return packProgressWidget
    }

    protected abstract getActivityScreenBaseURL(): string
    protected abstract getActivityType(): ActivityType
    public widgets: WidgetView[] = []
}

export default ContentDetailViewV2
