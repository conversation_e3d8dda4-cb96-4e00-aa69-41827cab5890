import { NutritionTag } from "@curefit/food-common"
import * as express from "express"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import { Container, inject } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import {
    FoodCategory,
    FoodInventory,
    FoodPack,
    FoodPackOption,
    FoodProduct as Product, ListingBrandIdType,
    MealSlot,
    MenuType, ProductDetailsResponse, ProductMealSlotMenu
} from "@curefit/eat-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { Session, UserContext } from "@curefit/userinfo-common"
import { Feedback } from "@curefit/feedback-common"
import { ProductDetailPage } from "../common/views/WidgetView"
import { IProductReadonlyDao, PRODUCT_MONGO_TYPES } from "@curefit/product-mongo"
import { IFeedbackReadOnlyDao, FEEDBACK_MONGO_TYPES } from "@curefit/feedback-mongo"
import { ErrorFactory } from "@curefit/error-client"
import { TimeUtil, Timezones } from "@curefit/util-common"
import AuthMiddleware from "../auth/AuthMiddleware"
import MealDetailViewBuilder from "./MealDetailViewBuilder"
import { FoodBooking, FoodPackBooking, SubFoodPackBooking } from "@curefit/shipment-common"
import { IShipmentService, ALFRED_CLIENT_TYPES } from "@curefit/alfred-client"
import { IFulfilmentService } from "@curefit/alfred-client"
import { IPackService } from "@curefit/alfred-client"
import { IMenuService, CAESAR_CLIENT_TYPES } from "@curefit/caesar-client"
import { ICatalogueService, CATALOG_CLIENT_TYPES } from "@curefit/catalog-client"
import { IFoodPackReadonlyDao, CAESAR_MODELS_TYPES } from "@curefit/caesar-models"
import { MealListView, MealView } from "./MealListView"
import ICRMIssueService from "../crm/ICRMIssueService"
import { ActionUtil, CATEGORY_PRIORITY_MAP, MealUtil, SeoUrlParams } from "@curefit/base-utils"
import IProductBusiness from "./IProductBusiness"
import { SlotUtil } from "@curefit/eat-util"
import * as _ from "lodash"
import FeedbackPageConfigV2Cache from "../ugc/FeedbackPageConfigV2Cache"
import IUserBusiness from "../user/IUserBusiness"
import { Logger, BASE_TYPES } from "@curefit/base"
import { ICapacityService, MASTERCHEF_CLIENT_TYPES } from "@curefit/masterchef-client"
import { MASTERCHEF_MODELS_TYPES } from "@curefit/masterchef-models"
import { ISessionBusiness as ISessionService, OfferUtil as BaseOfferUtil } from "@curefit/base-utils"
import EatUtil from "../util/EatUtil"
import CapacityServiceWrapper from "../product/CapacityServiceWrapper"
import {
    EatOfferRequestParamsV3,
    FoodPackOffersResponseV2,
    GearBulkOffersResponse,
    GearProductPricesResponse,
} from "@curefit/offer-common"
import {
    FoodSinglePriceOfferResponse,
} from "@curefit/offer-common"
import {
    IOfferServiceV2, OfferServiceV3
} from "@curefit/offer-service-client"
import FitnessDIYDetailViewV2 from "./FitnessDIYDetailViewV2"
import MindDIYDetailViewV2 from "./MindDIYDetailViewV2"
import { IUserService } from "@curefit/user-client"

import { PaymentPageConfig } from "../payment/PaymentPageConfig"
import { IGearService, GEARVAULT_CLIENT_TYPES } from "@curefit/gearvault-client"
import { GearUtil } from "@curefit/gearvault-client"
import GearProductViewBuilder, {
    GearProductIndexView,
    GearProductView,
    GearProductViewV2
} from "./GearProductViewBuilder"
import { CatalogueProduct as CGCatalogueProduct } from "@curefit/gear-common"
import { IDIYFulfilmentService, DIY_CLIENT_TYPES } from "@curefit/diy-client"
import { eternalPromise } from "@curefit/util-common"
import { IEatInventoryService } from "@curefit/masterchef-models"
import { CacheHelper } from "../util/CacheHelper"
import { OFFER_SERVICE_CLIENT_TYPES } from "@curefit/offer-service-client"
import { USER_CLIENT_TYPES } from "@curefit/user-client"
import { DELIVERY_CLIENT_TYPES, IDeliverySlotService } from "@curefit/delivery-client"
import AppUtil from "../util/AppUtil"
import { ICerberusService, ISegmentService } from "@curefit/vm-models"
import { EatLocationUtil } from "@curefit/eat"
import { EAT_API_CLIENT_TYPES, IEatApiService } from "@curefit/eat-api-client"
import { WholefitProductDetailViewBuilder } from "../wholefit/WholefitProductDetailViewBuilder"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { FITCASH_CLIENT_TYPES, IFitcashService } from "@curefit/fitcash-client"
import { IWholeFitService, WHOLE_FIT_API_CLIENT_TYPES } from "@curefit/wholefit-api-client"
import { ErrorCodes } from "../error/ErrorCodes"
import { DIYRecipeView } from "@curefit/diy-common"
import { ConsultationProduct, HealthfaceTenant } from "@curefit/care-common"
import { CareUtil } from "../util/CareUtil"
import { Action } from "@curefit/apps-common"
import { ALBUS_CLIENT_TYPES, IHealthfaceService } from "@curefit/albus-client"
import { ICareBusiness } from "../care/CareBusiness"
import { UserInfo } from "@curefit/user-common"
import { OrderSource } from "@curefit/order-common"
import { CFUserProfile } from "../page/vm/CFUserProfile"
import { MAX_CART_SIZE } from "../util/MealUtil"
import { CLOUDINARY_PREFIX, ProductType, UrlPathBuilder } from "@curefit/product-common"
import { EatMealsWidgetViewV2, MealItemRequest } from "../page/vm/widgets/EatMealsWidgetViewV2"
import { MealItem } from "../page/PageWidgets"
import { MealCardWidget } from "../page/vm/widgets/MealsCardWidget"
import { CULTSPORT_FEEDBACK_CLIENT_TYPES, ICultsportFeedbackService, ProductFeedback } from "@curefit/cultsport-feedback-client"
import { wrapWithMethodCache, CacheService, CACHE_CLIENT_TYPES, cacheKey } from "@curefit/cache-client"
import { SiteMapUrl, SiteMapImage } from "../sitemap/SitemapController"
import { PAYMENT_MODELS_TYPES, EmiInterestReadonlyDaoMongoImpl } from "@curefit/payment-models"
import { ICultsportEmiOptionsResponse } from "./IGearProductViewBuilder"
import { OMS_API_CLIENT_TYPES, IOrderService } from "@curefit/oms-api-client"
import { PromiseCache } from "../util/VMUtil"
const clone = require("clone")

export function controllerFactory(kernel: Container) {

    @controller("/product", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class ProductController {

        constructor(
            @inject(PRODUCT_MONGO_TYPES.ProductReadonlyDao) private productDao: IProductReadonlyDao,
            @inject(CUREFIT_API_TYPES.MealDetailViewBuilder) private mealDetailViewBuilder: MealDetailViewBuilder,
            @inject(CAESAR_MODELS_TYPES.FoodPackReadonlyDao) private foodPackDao: IFoodPackReadonlyDao,
            @inject(FEEDBACK_MONGO_TYPES.FeedbackReadOnlyDao) private feedbackDao: IFeedbackReadOnlyDao,
            @inject(CAESAR_CLIENT_TYPES.MenuService) private menuService: IMenuService,
            @inject(CUREFIT_API_TYPES.UserBusiness) private userBusiness: IUserBusiness,
            @inject(ALFRED_CLIENT_TYPES.ShipmentService) private shipmentService: IShipmentService,
            @inject(ALFRED_CLIENT_TYPES.FulfilmentService) private fulfilmentService: IFulfilmentService,
            @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
            @inject(CUREFIT_API_TYPES.CRMIssueService) private CRMIssueService: ICRMIssueService,
            @inject(CUREFIT_API_TYPES.ProductBusiness) private productBusiness: IProductBusiness,
            @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) private offerServiceV2: IOfferServiceV2,
            @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) private offerServiceV3: OfferServiceV3,
            @inject(CUREFIT_API_TYPES.FeedbackPageConfigV2Cache) private feedbackPageConfigV2Cache: FeedbackPageConfigV2Cache,
            @inject(MASTERCHEF_CLIENT_TYPES.CapacityService) private capacityService: ICapacityService,
            @inject(CUREFIT_API_TYPES.CapacityServiceWrapper) private capacityServiceWrapper: CapacityServiceWrapper,
            @inject(CUREFIT_API_TYPES.SessionService) private sessionBusiness: ISessionService,
            @inject(OMS_API_CLIENT_TYPES.OrderService) private omsApiClient: IOrderService,
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(CUREFIT_API_TYPES.PaymentPageConfig) private paymentPageConfig: PaymentPageConfig,
            @inject(ALFRED_CLIENT_TYPES.PackService) private packService: IPackService,
            @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
            @inject(GEARVAULT_CLIENT_TYPES.GearService) private gearService: IGearService,
            @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) private DIYFulfilmentService: IDIYFulfilmentService,
            @inject(MASTERCHEF_MODELS_TYPES.EatInventoryService) private eatInventorySvc: IEatInventoryService,
            @inject(CUREFIT_API_TYPES.GearProductViewBuilder) private gearProductViewBuilder: GearProductViewBuilder,
            @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
            @inject(DELIVERY_CLIENT_TYPES.DeliverySlotService) private deliverySlotService: IDeliverySlotService,
            @inject(CUREFIT_API_TYPES.CerberusServiceV2) protected cerberusService: ICerberusService,
            @inject(EAT_API_CLIENT_TYPES.IEatApiService) public eatApiClientService: IEatApiService,
            @inject(WHOLE_FIT_API_CLIENT_TYPES.IWholeFitService) public wholeFitService: IWholeFitService,
            @inject(CUREFIT_API_TYPES.WholefitProductDetailPageBuilder) public wholefitProductDetailBuilder: WholefitProductDetailViewBuilder,
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
            @inject(FITCASH_CLIENT_TYPES.FitcashService) private fitcashService: IFitcashService,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
            @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
            @inject(CUREFIT_API_TYPES.CareBusiness) public careBusiness: ICareBusiness,
            @inject(CULTSPORT_FEEDBACK_CLIENT_TYPES.Service) public cultsportFeedbackService: ICultsportFeedbackService,
            @inject(PAYMENT_MODELS_TYPES.EmiInterestReadonlyDao) private paymentDao: EmiInterestReadonlyDaoMongoImpl,
            @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService,
        ) {
        }

        @httpGet("/eat/")
        async getMeals(req: express.Request): Promise<MealListView> {
            const session: Session = req.session
            const userId: string = session.userId
            const mealSlot: MealSlot = req.query.mealSlot ? req.query.mealSlot : "LUNCH"
            const deviceId: string = session.deviceId
            const userContext: UserContext = req.userContext as UserContext
            const preferredLocation = await this.userBusiness.getPreferredLocation(userContext, userId, session.sessionData, undefined, undefined, mealSlot)
            const nextAvailableMenu = await this.productBusiness.getNextAvailableMenu(userContext, mealSlot, preferredLocation, true)
            const preferredLocationAttributes = EatLocationUtil.preferredLocationAttributes(preferredLocation)

            // Get single offers
            const date = nextAvailableMenu.day
            const eatOfferRequestParams: EatOfferRequestParamsV3 = {
                userId: userId,
                cityId: userContext.userProfile.cityId,
                deviceId: deviceId,
                areaId: preferredLocationAttributes.areaId,
                dateMealSlotMap: { [date]: [mealSlot] },
                source: AppUtil.callSourceFromContext(userContext)
            }

            const productMap = new Map<string, Product>()
            const promises: Promise<MealView>[] = _.map(nextAvailableMenu.menus, async (menu) => {
                const product = await this.catalogueService.getProduct(menu.productId)
                productMap.set(product.productId, product)
                return new MealView(product, menu.ordering, mealSlot, nextAvailableMenu.day, nextAvailableMenu.inventoryResult, {} as FoodSinglePriceOfferResponse, userContext)
            })
            const mealViews: MealView[] = await Promise.all(promises)
            const mealWithInventorySet = mealViews.filter(mealView => {
                return mealView.isInventorySet
            })
            mealWithInventorySet.sort((a, b) => {
                const productA = productMap.get(a.productId)
                const productB = productMap.get(b.productId)
                const productACategory = a.isAvailable && CATEGORY_PRIORITY_MAP.get(productA.categoryId) ? CATEGORY_PRIORITY_MAP.get(productA.categoryId) : 1000
                const productBCategory = b.isAvailable && CATEGORY_PRIORITY_MAP.get(productB.categoryId) ? CATEGORY_PRIORITY_MAP.get(productB.categoryId) : 1000
                if (productACategory < productBCategory)
                    return -1
                else if (productACategory > productBCategory) {
                    return 1
                } else {
                    return 0
                }
            })
            return new MealListView(nextAvailableMenu.day, mealWithInventorySet, mealSlot, userContext)
        }

        @httpGet("/gear")
        async getGearProducts(req: express.Request): Promise<GearProductIndexView> {
            const userId = req.session.userId
            const deviceId: string = req.session.deviceId

            const response: { products: CGCatalogueProduct[] } = await this.gearService.listProducts()

            const orderSource: OrderSource = AppUtil.callSourceFromContext(req.userContext)

            const userInfo: UserInfo = {userId: userId, deviceId: deviceId}
            const productOffers: GearProductPricesResponse = await this.offerServiceV3.getGearProductPrices(_.map(response.products, p => `${p.id}`), userInfo, orderSource)
            return _.extend(
                response,
                { products: GearUtil.addDiscountInformationToProducts(_.map(response.products, p => GearUtil.convertProductVariantsToGearSKUs(p)), productOffers.priceMap) }
            )
        }


        @httpGet("/gear/:productId/emiOptions")
        async getCultSportEmiOption(req: express.Request): Promise<ICultsportEmiOptionsResponse> {
            const productId = req.params.productId
            const productType = req.query.productType
            const userContext = req.userContext as UserContext
            const session: Session = req.session
            const user = await userContext.userPromise
            const userInfo = {
                userId: userContext?.userProfile?.userId,
                deviceId: userContext.sessionInfo.deviceId,
                phone: user?.phone,
                email: user?.email,
                workEmail: user?.workEmail
            }
            const productOffersPromise: Promise<GearProductPricesResponse> = this.serviceInterfaces.offerServiceV3.getGearProductPrices(
                [productId],
                userInfo,
                userContext.sessionInfo.orderSource
            )
            const gearcartOffersPromise: Promise<GearBulkOffersResponse> = this.serviceInterfaces.offerServiceV3.getGearBulkOffers(
                ["GEAR_CART"],
                userInfo,
                userContext.sessionInfo.orderSource
            )
            const [product] = GearUtil.addDiscountInformationToProducts(
                [GearUtil.convertProductVariantsToGearSKUs(await this.serviceInterfaces.gearService.showProduct(productId))],
                (await productOffersPromise).priceMap
            )
            const emiOptions = await this.paymentDao.find({ condition: {} })
            return this.gearProductViewBuilder.buildCultsportProductEmiOptions(product, emiOptions, await productOffersPromise, (await gearcartOffersPromise)?.cartOffers)
        }

        @httpGet("/gear/:productId")
        async getGearProductDetail(req: express.Request, res: express.Response): Promise<GearProductView> {
            const session: Session = req.session
            const userId = session.userId
            const productId: string = req.params.productId
            const deviceId: string = req.session.deviceId
            const user = await this.userCache.getUser(session.userId)
            const userContext: UserContext = req.userContext
            const timezone = userContext?.userProfile?.timezone || "Asia/Kolkata"
            const variantId = req.query?.variantId
            const orderSource: OrderSource = AppUtil.callSourceFromContext(req.userContext)

            const userInfo: UserInfo = {userId: userId, deviceId: deviceId}
            const productOffers: GearProductPricesResponse = await this.offerServiceV3.getGearProductPrices([productId], userInfo, orderSource)
            const [product] = GearUtil.addDiscountInformationToProducts(
                [GearUtil.convertProductVariantsToGearSKUs(await this.gearService.showProduct(productId))],
                productOffers.priceMap
            )
            let productFeedback: ProductFeedback
            try {
                productFeedback = await this.getCultsportProductRating(productId)
            } catch (e) {}
            res.locals.seoSchema = AppUtil.getCSSeoSchema(product, productFeedback)
            res.locals.canonical = ["https://cultsport.com/" + product.slug + "/product/" + product.id]
            res.locals.productPrice = product.price
            res.locals.productName = product.name
            userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
            return this.gearProductViewBuilder.buildProductDetailView(product, session, user.isInternalUser, timezone, userContext, variantId, await AppUtil.doesUserBelongsToTrainerAttireExperiment(userContext, this.segmentService))
        }

        @httpGet("/v2/gear/:productId")
        async getGearProductDetailV2(req: express.Request): Promise<GearProductViewV2> {
            const session: Session = req.session
            const userId = session.userId
            const productId: string = req.params.productId
            const deviceId: string = req.session.deviceId
            const user = await this.userCache.getUser(session.userId)
            const userContext: UserContext = req.userContext
            const timezone = userContext.userProfile.timezone
            const orderSource: OrderSource = AppUtil.callSourceFromContext(req.userContext)

            const userInfo: UserInfo = {userId: userId, deviceId: deviceId}
            const productOffers: GearProductPricesResponse = await this.offerServiceV3.getGearProductPrices([productId], userInfo, orderSource)
            const [product] = GearUtil.addDiscountInformationToProducts(
                [GearUtil.convertProductVariantsToGearSKUs(await this.gearService.showProduct(productId))],
                productOffers.priceMap
            )
            return this.gearProductViewBuilder.buildProductDetailViewV2(product, session, user.isInternalUser, timezone, userContext)
        }

        private getRecipeViewPromise(productId: string): Promise<{ obj: DIYRecipeView[], err?: any }> {
            return eternalPromise(this.DIYFulfilmentService.getDIYRecipesForEatFitProduct(productId))
        }

        @httpGet("/wholefit/:productId")
        async getWholefitProductDetails(req: express.Request): Promise<ProductDetailPage> {
            const userContext = req.userContext as UserContext
            const productId = req.params.productId
            const session: Session = req.session
            const userId = session.userId
            const lat: number = req.query.lat
            const lon: number = req.query.lon
            const mealSlot: MenuType = "ALL"
            const defaultListingBrand: ListingBrandIdType = "WHOLE_FIT"
            const preferredLocationPromise = this.userBusiness.getPreferredLocation(userContext, userId, session.sessionData, lon, lat, mealSlot, true, defaultListingBrand)
            /*
             * Updating preferredLocationPromise for wholefit
             */
            userContext.userProfile.preferredLocationPromise = preferredLocationPromise
            const preferredLocation = await preferredLocationPromise
            const areaId = preferredLocation.area ? preferredLocation.area.areaId : preferredLocation.defaultArea.areaId
            const productPromise = this.wholeFitService.getProductDetails(areaId, productId)
            const productDetails: ProductDetailsResponse = await productPromise
            const variants = productDetails.productResponse.variants
            const day = (await this.wholeFitService.getNextAvailableSlot(areaId)).date
            let productIds: string[] = [productDetails.productResponse.productId] // added parentProductId at first
            if (variants && variants.length > 0) {
                _.forEach(productDetails.productResponse.variants, variant => {
                    productIds.push(variant.productId)
                })
            }
            productIds = _.uniq(productIds)
            const variantPromise = this.catalogueService.getProducts(productIds)
            const singleOffersPromise = EatUtil.getSingleOffers(this.serviceInterfaces, userContext, userContext.sessionInfo, defaultListingBrand, productIds, day)
            return this.wholefitProductDetailBuilder.build(userContext, productDetails, singleOffersPromise, variantPromise, productIds)
        }

        @httpGet("/eat/v2/:productId")
        async getMealDetailV2(req: express.Request): Promise<ProductDetailPage> {
            const productId: string = req.params.productId
            const kioskId: string = req.query.kioskId
            const userContext: UserContext = req.userContext
            const timezone = userContext.userProfile.timezone
            let date: string
            if (req.query.date) {
                const queryDate = TimeUtil.parseDate(req.query.date, timezone)
                const todaysDate = new Date()
                if (queryDate.setHours(0, 0, 0, 0) - todaysDate.setHours(0, 0, 0, 0) < 0) {
                    date = TimeUtil.todaysDateWithTimezone(timezone)
                } else {
                    date = req.query.date
                }
            } else {
                date = TimeUtil.todaysDateWithTimezone(timezone)
            }
            const product = await this.catalogueService.getProduct(productId)
            return this.mealDetailViewBuilder.getCafeView(userContext, kioskId, product, false, date, "EAT_FIT")
        }

        @httpGet("/cultDIY/:productId")
        async getCultDIYDetail(req: express.Request): Promise<FitnessDIYDetailViewV2> {
            const session: Session = req.session
            const userId: string = session.userId
            const userAgent: UserAgent = session.userAgent
            const date: string = req.query.date
            const packId: string = req.query.packId
            const productId: string = req.params.productId
            const appVersion: number = Number(req.headers["appversion"])

            const diyPack = await this.packService.getFitnessDIYPackV2(packId, session.userId)
            const productMap = await this.catalogueService.getProductMap(diyPack.pack.sessionIds)
            const feedback: Feedback = await this.feedbackDao.findOne({ itemId: productId, userId: session.userId })
            return FitnessDIYDetailViewV2.getView(userAgent, appVersion, diyPack, productId, productMap, feedback, this.feedbackPageConfigV2Cache)
        }

        @httpGet("/mind/:productId")
        async getMindDIYDetail(req: express.Request): Promise<MindDIYDetailViewV2> {
            const session: Session = req.session
            const userAgent: UserAgent = session.userAgent
            const date: string = req.query.date
            const packId: string = req.query.packId
            const productId: string = req.params.productId
            const userId: string = session.userId
            const appVersion: number = Number(req.headers["appversion"])

            const diyPack = await this.packService.getMindDIYPackV2(packId, session.userId)
            const productMap = await this.catalogueService.getProductMap(diyPack.pack.sessionIds)
            const feedback: Feedback = await this.feedbackDao.findOne({ itemId: productId, userId: session.userId })
            return MindDIYDetailViewV2.getView(userAgent, appVersion, diyPack, productId, productMap, feedback, this.feedbackPageConfigV2Cache)
        }

        @httpGet("/getMeetingAction")
        async getZoomMeetingDetails(req: express.Request): Promise<any> {
            const bookingId: number = req.query.bookingId
            const productCode: string = req.query.productCode
            const userContext = req.userContext as UserContext
            if (!userContext.sessionInfo.isUserLoggedIn) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_LOGGED_IN_ERR, 400).build()
            }
            const baseProduct: ConsultationProduct = <ConsultationProduct>await this.catalogueService.getProduct(productCode)
            const tenant: HealthfaceTenant = CareUtil.getCareProductTenant(baseProduct)
            const bookingDetail = await this.healthfaceService.getBookingDetailForBookingId(bookingId, tenant)
            if (!CareUtil.isComplteted(bookingDetail)) {
                const zoomUrl = CareUtil.getZoomLink(bookingDetail)
                const isActionEnabled = CareUtil.getZoomLinkEnabled(bookingDetail)
                if (isActionEnabled) {
                    const action: Action = await this.careBusiness.getJoinZoomMeetingActionfromUrl(userContext, zoomUrl, bookingDetail.booking.patientId, bookingDetail.consultationOrderResponse.doctorType, bookingDetail.booking.id)
                    return { action }
                }
            }
        }

        @httpPost("/crossSell/eatsingle")
        async getCrossSellEatSingleDetails(req: express.Request) {
            const userContext = req.userContext as UserContext
            const userProfile = userContext.userProfile as CFUserProfile
            const sessionInfo = userContext.sessionInfo
            const reqProductIds: string[] = req.body.productIds || []
            const groupType: string = req.body.groupType
            const groupValues: string [] = req.body.groupValues || []
            let collections: any [] = []
            // Await for mandatory parameter needed for all apis
            const preferredLocation = await this.userBusiness.getPreferredLocation(userContext, userProfile.userId, sessionInfo.sessionData, sessionInfo.lon, sessionInfo.lat, undefined, undefined, "EAT_FIT")
            if (preferredLocation?.isInServicableArea && preferredLocation?.isInServicableArea &&
                (!_.isEmpty(reqProductIds) || (!_.isEmpty(groupValues)))) {
                const deliveryArea = await this.serviceInterfaces.deliveryAreaService.getDeliveryArea(userProfile.areaId)
                const [deliveryAreaTz, mealSlots] = await Promise.all([
                    this.serviceInterfaces.deliveryAreaService.getTimeZoneForAreaId(deliveryArea.areaId),
                    this.serviceInterfaces.deliveryAreaService.getMealSlotsForArea(userProfile.areaId)
                ])
                const currentMealSlot = SlotUtil.getCurrentMealSlotFrom(mealSlots, deliveryArea.channel, deliveryAreaTz)
                const selectedMealSlotAndDay = EatUtil.getSelectedMealSlotAndDay({}, deliveryArea, deliveryAreaTz,
                    mealSlots, currentMealSlot, undefined)
                const day = selectedMealSlotAndDay.day
                const cityWiseMaxCartSize = _.get(this.serviceInterfaces.configService.getConfig("EAT_CLP_OVERRIDES").configs, "citywiseMaxCartSize")
                const maxAllowedCartSize = _.get(cityWiseMaxCartSize, deliveryArea.cityId, MAX_CART_SIZE)
                let tabSlot: MenuType = selectedMealSlotAndDay.mealSlot
                const menuAvailabilityPromise = this.serviceInterfaces.eatApiClientService.getOrderedMenuByMenuType(userProfile.userId, tabSlot, day, preferredLocation, true, undefined, deliveryAreaTz, "EAT_FIT" as ListingBrandIdType, undefined, [])
                let productMenus: ProductMealSlotMenu[] = []
                let inventoryResult: FoodInventory
                const menuAvailability = await menuAvailabilityPromise
                productMenus = menuAvailability.menus
                inventoryResult = menuAvailability.inventoryResult
                inventoryResult
                if (tabSlot === "ALL") {
                    tabSlot = SlotUtil.getMealSlotExact(TimeUtil.now(userContext.userProfile.timezone))
                }
                const delayMode = deliveryArea?.delayMode?.find((delayMode) => {
                    return delayMode.mealSlot === tabSlot
                })
                const stopOrders = delayMode !== undefined && (delayMode.mode === "STOP_ORDERS" || delayMode.mode === "STOP_ORDERS_ENABLE_CANCEL")

                // Filter only the required product ids
                const allProductIds = _.map(productMenus, menu => menu.productId)
                let productIds = []
                if (!_.isEmpty(reqProductIds)) {
                    productIds = reqProductIds.filter(productId => allProductIds.includes(productId))
                } else {
                    productIds = allProductIds
                }

                const productMap = await this.serviceInterfaces.catalogueService.getProductMap(productIds)

                // Logic for variants
                const productToVariantMap: { [productId: string]: string[] } = {}
                let allProducts: Product[] = clone(_.values(productMap))
                const parentProductMap = await this.serviceInterfaces.catalogueService.getProductMap(_.compact(allProducts.map(p => p.parentProductId)))


                // Filter out require group values
                if (groupType && !_.isEmpty(groupValues)) {
                    if (groupType === "EAT_FIT_NUTRITION_TAG") {
                        allProducts = _.compact(allProducts).filter(product => groupValues.some((tag: NutritionTag) => product?.nutritionTags?.includes(tag)))
                    } else if (groupType === "EAT_FIT_FOOD_CATEGORY") {
                        allProducts = _.compact(allProducts).filter(product => groupValues.some((category: string) => category?.includes(product?.categoryId) || product?.categoryId?.includes(category)))
                    } else {
                        allProducts = []
                    }
                } else {
                    allProducts = _.compact(allProducts)
                }

                // Removing all products stockout products
                allProducts = allProducts.filter(product => {
                    const result = BaseOfferUtil.getAvailabilityV1(product, inventoryResult)
                    return result.total > 0 && result.left > 0
                })
                if (!_.isEmpty(allProducts)) {
                    const singleOfferResultPromise: Promise<FoodSinglePriceOfferResponse> = Promise.resolve({} as FoodSinglePriceOfferResponse)
                    const byProductId: { [productId: string]: Product } = _.keyBy(allProducts, product => product.productId)
                    productMenus = productMenus.filter(menu => {
                        return !_.isNil(byProductId[menu.productId])
                    })
                    const eatMealWidgetViewV2 = new EatMealsWidgetViewV2()
                    eatMealWidgetViewV2.handleParentProductNotPresent(allProducts, byProductId)
                    allProducts.forEach(product => {
                        const parentProductId = product.parentProductId
                        if (!_.isNil(parentProductId)) {
                            if (!_.isNil(productToVariantMap[parentProductId])) {
                                productToVariantMap[parentProductId].push(product.productId)
                            } else {
                                productToVariantMap[parentProductId] = [product.productId]
                            }
                        }
                    })
                    const singleOfferResult =  await singleOfferResultPromise
                    const mealItems: MealItem[] = _.map(productMenus, menu => {
                        const product = byProductId[menu.productId]
                        if (_.isNil(product)) {
                            return
                        }
                        const request: MealItemRequest = {
                            sessionInfo: sessionInfo,
                            product: product,
                            stopOrders: stopOrders,
                            interfaces: this.serviceInterfaces,
                            singleOfferResult: singleOfferResult,
                            day: day,
                            inventoryResult: inventoryResult,
                            tabSlot: tabSlot,
                            preferredLocation: preferredLocation,
                            userContext: userContext,
                            menu: menu,
                            queryParams: {}
                        }
                        return eatMealWidgetViewV2.getMealItem(request, false, false, 1, false, false, parentProductMap, productToVariantMap, maxAllowedCartSize)
                    }).filter(item => !!item)
                    eatMealWidgetViewV2.handleParentProductSoldOut(mealItems)
                    const mealItemsWithInventory = _.filter(mealItems, mealItem => { return mealItem.isInventorySet })
                    const categoryIdArray = _.uniq(mealItemsWithInventory.map((mealItem) => {
                        return mealItem.foodCategoryId
                    }))
                    const clpCategories: { [id: string]: FoodCategory } = this.serviceInterfaces.foodCategoryService.getCLPCategories(categoryIdArray, "EAT_FIT")
                    const mealWidgetPromises = _.map(mealItemsWithInventory, (mealItem: MealItem) => {
                        return new MealCardWidget(mealItem, tabSlot, clpCategories, "SMALL_CARD", undefined, false, false, undefined).buildView(this.serviceInterfaces, userContext, {})
                    })

                    collections = await Promise.all(mealWidgetPromises)
                    collections = productIds.map(productId => {
                        const data  = collections.find(item => item?.productId === productId)
                        return data ? data : null
                    }).filter(Boolean)
                }

            }
            return {
                collections
            }

        }

        @httpPost("/crossSell/eatMealPlan")
        async getCrossSellEatMealPlanDetails(req: express.Request) {
            const userContext = req.userContext as UserContext
            const { userProfile, sessionInfo } = userContext
            const productIds: string[] = req.body.productIds
            let collections: any[] = []
            // Await for mandatory parameter needed for all apis
            const preferredLocation = await this.userBusiness.getPreferredLocation(userContext, userProfile.userId, sessionInfo.sessionData, sessionInfo.lon, sessionInfo.lat, undefined, undefined, "EAT_FIT")
            if (!_.isEmpty(productIds) && preferredLocation.isInServicableArea && preferredLocation.isWithinServicableCity) {
                const deliveryArea = await this.serviceInterfaces.deliveryAreaService.getDeliveryArea(userProfile.areaId)
                const tz = this.serviceInterfaces.deliveryAreaService.getTimeZoneForArea(deliveryArea)
                const allSlots: MealSlot[] = await this.serviceInterfaces.deliveryAreaService.getMealSlotsForArea(userProfile.areaId)
                const currentMealSlot = SlotUtil.getCurrentMealSlotFrom(allSlots, deliveryArea.channel, tz)
                const foodPacks: FoodPack[] = await this.serviceInterfaces.catalogueService.getFoodPacksByMealSlot(currentMealSlot, deliveryArea.channel, "PRIMARY")

                if (foodPacks?.length > 0) {
                    const inventoryResult: FoodInventory = await this.serviceInterfaces.capacityService.getInventoryForPacksBasedOnMenuAlone(userProfile.areaId, currentMealSlot)
                    const packAvailabilityMap = new Map<string, boolean>()
                    Object.keys(inventoryResult).forEach(day => {
                        const inventoryForDay = inventoryResult[day]
                        Object.keys(inventoryForDay).forEach(packId => {
                            const inventoryForPack = inventoryForDay[packId]
                            if (inventoryForPack.total > 0) {
                                packAvailabilityMap.set(packId, true)
                            }
                        })
                    })

                    const validFoodPacks = foodPacks.filter(foodPack => {
                        return foodPack.status === "LIVE" && packAvailabilityMap.get(foodPack.productId)
                    })

                    const finalValidFoodPacks = productIds.map(productId => {
                        const foodPack = validFoodPacks.find(foodPack => foodPack.productId === productId)
                        return foodPack ? foodPack : null
                    }).filter(Boolean)

                    if (!_.isEmpty(finalValidFoodPacks)) {
                        const eatPackOffersResult = {} as FoodPackOffersResponseV2
                        collections = finalValidFoodPacks.map(foodPack => {
                            const foodPackOption: FoodPackOption = MealUtil.findDefaultSubscriptionOption(foodPack, eatPackOffersResult)
                            const result = BaseOfferUtil.getFoodPackOfferAndPrice(foodPack, foodPackOption, eatPackOffersResult)
                            if (foodPackOption.subscriptionType !== undefined) {
                                result.price.listingPrice = Math.round(result.price.listingPrice / foodPackOption.numTickets)
                                result.price.mrp = Math.round(result.price.mrp / foodPackOption.numTickets)
                            }
                            const seoParams: SeoUrlParams = {
                                productName: foodPack.title
                            }
                            return {
                                title: foodPack.title,
                                imageUrl: UrlPathBuilder.getPackImagePath(foodPack.productId, "FOOD", "MAGAZINE", foodPack.imageVersion, sessionInfo.userAgent),
                                thumbNailImageUrl: UrlPathBuilder.getPackImagePath(foodPack.productId, "FOOD", "THUMBNAIL", foodPack.imageVersion, sessionInfo.userAgent),
                                price: result.price,
                                productId: foodPack.productId,
                                action: {
                                    actionType: "NAVIGATION",
                                    url: ActionUtil.foodPack(foodPack.productId, undefined, undefined, true, userContext.sessionInfo.userAgent, seoParams, undefined, "EAT_FIT")
                                }
                            }
                        })
                    }
                }
            }

            return {
                collections
            }
        }

        private fetchFoodBooking(foodPackBooking: FoodPackBooking | SubFoodPackBooking, date: string): FoodBooking {
            let foodBooking: FoodBooking
            if (foodPackBooking.activeShipment && foodPackBooking.activeShipment.deliveryDate === date) {
                foodBooking = foodPackBooking.activeShipment
            }
            if (!foodBooking && foodPackBooking.futureShipment) {
                foodPackBooking.futureShipment.forEach(booking => {
                    if (booking.deliveryDate === date) {
                        foodBooking = booking
                    }
                })
            }
            if (!foodBooking && foodPackBooking.pastShipment) {
                foodPackBooking.pastShipment.forEach(booking => {
                    if (booking.deliveryDate === date) {
                        foodBooking = booking
                    }
                })
            }
            return foodBooking
        }

        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), TimeUtil.TIME_IN_SECONDS.DAY)
        private async getCultsportProductRating(@cacheKey productId: string): Promise<ProductFeedback> {
            return await this.cultsportFeedbackService.getAggregateProductFeedback(productId, false)
        }
    }
    return ProductController
}

export default controllerFactory
