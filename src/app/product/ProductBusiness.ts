import { inject, injectable } from "inversify"
import { IGateService, DELIVERY_CLIENT_TYPES, IDeliverySlotService } from "@curefit/delivery-client"
import { IPackService, FoodBusinessUtil } from "@curefit/alfred-client"
import { IMenuService, CAESAR_CLIENT_TYPES } from "@curefit/caesar-client"
import { SlotUtil } from "@curefit/eat-util"
import { capitalizeFirstLetter, eternalPromise, TimeUtil, Timezone } from "@curefit/util-common"
import { MAX_CART_SIZE, MealUtil, SeoUrlParams } from "@curefit/base-utils"
import { ActionUtil } from "@curefit/base-utils"
import { ActionUtil as AppActionUtil } from "../util/ActionUtil"
import EatUtil, { DEFAULT_FOOD_CATEGORY_ID } from "../util/EatUtil"
import { EatLocationUtil } from "@curefit/eat"
import { FoodBooking } from "@curefit/shipment-common"
import { FoodPackBooking } from "@curefit/shipment-common"
import { IShipmentService, ALFRED_CLIENT_TYPES } from "@curefit/alfred-client"
import { ICapacityService, MASTERCHEF_CLIENT_TYPES } from "@curefit/masterchef-client"
import { MASTERCHEF_MODELS_TYPES, ProductInventory } from "@curefit/masterchef-models"
import { EAT_API_CLIENT_TYPES, IEatApiService } from "@curefit/eat-api-client"
import {
    CustomerIssueType,
} from "@curefit/issue-common"
import {
    DeliveryChannel, FoodCategory, ListingBrandIdType,
} from "@curefit/eat-common"
import {
    FoodPack,
} from "@curefit/eat-common"
import {
    HourMin,
} from "@curefit/base-common"
import {
    IssueCategory,
} from "@curefit/issue-common"
import {
    MealSlot,
} from "@curefit/eat-common"
import {
    MenuType,
} from "@curefit/eat-common"
import {
    FoodProduct as Product
} from "@curefit/eat-common"
import {
    ProductCategory, UrlPathBuilder,
} from "@curefit/product-common"
import {
    ProductMealSlotMenu,
} from "@curefit/eat-common"
import {
    ProductMenu,
} from "@curefit/eat-common"
import {
    ProductType,
} from "@curefit/product-common"
import {
    User,
} from "@curefit/user-common"
import {
    UserDeliveryAddress
} from "@curefit/eat-common"
import { BaseOrder, FoodFulfilment } from "@curefit/order-common"
import { RUPEE_SYMBOL } from "@curefit/finance-common"
import * as _ from "lodash"
import { ICatalogueService, CATALOG_CLIENT_TYPES } from "@curefit/catalog-client"
import IProductBusiness, { CurrentMenuAvailabilityResult, MenuAvailabilityResult } from "./IProductBusiness"
import { CultBooking, CultMembership, CultPack } from "@curefit/cult-common"
import { PreferredLocation } from "@curefit/userinfo-common"
import {
    Action,
    AlertInfo,
    ManageOptionPayload,
    ManageOptions,
    POSSIBLE_FOOD_BOOKING_ACTION_TYPES,
    MealActionType,
    WidgetView, ListWidget, ListHeaderWidget, ManageOption
} from "../common/views/WidgetView"
import * as momentTz from "moment-timezone"
import { max } from "moment-timezone"
import { IHealthfaceService, BookingDetail, CallToAction, Doctor, ALBUS_CLIENT_TYPES, TimelineActivityV2, ConsultationInstructionResponse } from "@curefit/albus-client"
import CareUtil, { CareActionIdsByPriority } from "../util/CareUtil"
import { IFulfilmentService } from "@curefit/alfred-client"
import { OfferUtil } from "@curefit/base-utils"
import { Logger, BASE_TYPES } from "@curefit/base"
import { EatOfferRequestParamsV3, FoodPackOffersResponse, FoodSinglePriceOfferResponse, FoodPackOffersResponseV2 } from "@curefit/offer-common"
import { IOfferServiceV2 } from "@curefit/offer-service-client"
import { UserContext } from "@curefit/userinfo-common"
import { IUserTestService, USERTEST_CLIENT_TYPES } from "@curefit/user-test-client"
import UserTestUtil from "../usertest/UserTestUtil"
import { CULTSCORE_TESTID } from "@curefit/user-common"
import CultUtil, { CULTSCORE_WORKOUT_IDS, DUBAI_ACTUAL_CITY_ID, DUBAI_CULT_CITY_ID, isMembershipCurrent } from "../util/CultUtil"
import { ProductCategoryService, PRODUCT_SERVICE_TYPES } from "@curefit/product-service"
import OrderTrackingStatusWidget from "../common/views/OrderTrackingStatusWidget"
import IssueBusiness from "../crm/IssueBusiness"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import AppUtil, { SHOW_INSTRUCTION_MODAL_SUPPORTED_APP_VERSION, SUPPORT_DEEP_LINK, getAppUrl } from "../util/AppUtil"
import { IChangeMealService } from "@curefit/alfred-client"
import TeleconsultationDetailsPageConfig from "../care/TeleconsultationDetailsPageConfig"
import { OrderTrackingStatus } from "@curefit/eat-common"
import { ICultBusiness, MoneyBackOfferDetail } from "../cult/CultBusiness"
import { DeliverySlot } from "@curefit/eat-common"
import { OFFER_SERVICE_CLIENT_TYPES } from "@curefit/offer-service-client"
import ICRMIssueService from "../crm/ICRMIssueService"
import { ICerberusService, ISegmentService } from "@curefit/vm-models"
import { EAT_TYPES, IFoodCategoryService } from "@curefit/eat"
import { EatSubscriptionUtil } from "../util/EatSubscriptionUtil"
import { PAYMENT_TYPES, IPaymentClient } from "@curefit/payment-client"

import { PageTypes } from "@curefit/apps-common"
import { AnnouncementBusiness } from "../announcement/AnnouncementBusiness"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { ICareBusiness } from "../care/CareBusiness"
import { Patient } from "@curefit/care-common"
import { UpcomingOrderDetailsResponse } from "@curefit/gandalf-common"
import FoodMarketplaceUtil from "../util/FoodMarketplaceUtil"
import { Membership } from "@curefit/membership-commons"
import { MembershipTransferable } from "../cult/cultpackpage/CultPackCommonViewBuilder"
import { OMS_API_CLIENT_TYPES, IOrderService as IOMSService } from "@curefit/oms-api-client"
import { ErrorFactory } from "@curefit/error-client"
import { ErrorCodes } from "../error/ErrorCodes"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"
import CatalogueServiceUtilities from "../util/CatalogueServiceUtilities"
import { MembershipItemUtil } from "../util/MembershipItemUtil"
import FitnessUtil from "../util/FitnessUtil"
const util = require("util")
const clone = require("clone")

interface AddMoneyAction extends Action {
    payload: any
}
@injectable()
class ProductBusiness implements IProductBusiness {

    constructor(@inject(DELIVERY_CLIENT_TYPES.GateService) private gateService: IGateService,
        @inject(ALFRED_CLIENT_TYPES.PackService) private packService: IPackService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(CAESAR_CLIENT_TYPES.MenuService) private menuService: IMenuService,
        @inject(MASTERCHEF_CLIENT_TYPES.CapacityService) private capacityService: ICapacityService,
        @inject(CUREFIT_API_TYPES.CRMIssueService) private CRMIssueService: ICRMIssueService,
        @inject(ALFRED_CLIENT_TYPES.ShipmentService) private shipmentService: IShipmentService,
        @inject(ALFRED_CLIENT_TYPES.FulfilmentService) private fulfilmentService: IFulfilmentService,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) private offerServiceV2: IOfferServiceV2,
        @inject(USERTEST_CLIENT_TYPES.UserTestService) private userTestService: IUserTestService,
        @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness,
        @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
        @inject(PRODUCT_SERVICE_TYPES.ProductCategoryService) private categoryService: ProductCategoryService,
        @inject(ALFRED_CLIENT_TYPES.ChangeMealService) private changeMealService: IChangeMealService,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(CUREFIT_API_TYPES.TeleconsultationDetailsPageConfig) private tcDetailsPageConfig: TeleconsultationDetailsPageConfig,
        @inject(CUREFIT_API_TYPES.CultBusiness) private cultBusiness: ICultBusiness,
        @inject(EAT_API_CLIENT_TYPES.IEatApiService) private eatApiService: IEatApiService,
        @inject(DELIVERY_CLIENT_TYPES.DeliverySlotService) private deliverySlotService: IDeliverySlotService,
        @inject(CUREFIT_API_TYPES.CerberusServiceV2) protected cerberusService: ICerberusService,
        @inject(EAT_TYPES.FoodCategoryService) protected foodCategoryService: IFoodCategoryService,
        @inject(PAYMENT_TYPES.IPaymentClient) protected paymentClient: IPaymentClient,
        @inject(CUREFIT_API_TYPES.AnnouncementBusiness) private announcementBusiness: AnnouncementBusiness,
        @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness,
        @inject(CUREFIT_API_TYPES.CareBusiness) public careBusiness: ICareBusiness,
        @inject(OMS_API_CLIENT_TYPES.OrderService) private omsApiClient: IOMSService,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory
    ) {
    }

    private getUserAddress(foodFulfilment: FoodFulfilment, date: string): UserDeliveryAddress {
        let address = foodFulfilment.userAddress
        if (foodFulfilment.addressOverride) {
            // It is important to go over all overrides to take the last valid entry.
            foodFulfilment.addressOverride.forEach(override => {
                if (override.day === date) {
                    address = override.userAddress
                }
            })
        }
        return address
    }

    getAlertInfo(product: Product, date: string, mealSlot: MealSlot, fulfilmentId: string, amountPayable: number, userContext: UserContext): AlertInfo {
        const seoParams: SeoUrlParams = {
            productName: product.title
        }
        const foodSingleAction = ActionUtil.foodSingle(product.productId, date, mealSlot, false, fulfilmentId, userContext.sessionInfo.userAgent, seoParams)
        let subTitle = `Your meal for ${TimeUtil.getDayText(date, userContext.userProfile.timezone).toLowerCase()} has been successfully changed to ${product.title}.`
        if (amountPayable > 0) {
            subTitle = `${subTitle}\n ${RUPEE_SYMBOL}${amountPayable} has been charged to your Paytm account.`
        } else if (amountPayable < 0) {
            subTitle = `${subTitle}\n ${RUPEE_SYMBOL}${-amountPayable} has been added to your account.`
        }
        const alertInfo: AlertInfo = {
            title: "Success",
            subTitle: subTitle,
            actions: [{
                title: "Ok",
                actionType: "POP_REFRESH_ACTION",
                url: foodSingleAction
            }]
        }
        return alertInfo
    }

    async getNextAvailableMenu(userContext: UserContext, mealSlot: MealSlot,
        preferredLocation: PreferredLocation, excludeHiddenItems?: boolean): Promise<MenuAvailabilityResult> {
        const tz = userContext.userProfile.timezone
        const preferredLocationAttributes = EatLocationUtil.preferredLocationAttributes(preferredLocation)
        const today: string = TimeUtil.todaysDateWithTimezone(tz)
        let days: string[] = TimeUtil.getDaysFrom(tz, today, 5)
        const slots = SlotUtil.getDeliverySlotsForMealSlotAndChannel(mealSlot, preferredLocation.deliveryChannel)
        const maxSlot = SlotUtil.getMaxSlot(slots)
        const isTodayCutOffPassed = SlotUtil.isHardCutOffPassed(TimeUtil.todaysDateWithTimezone(tz), tz, maxSlot.slotId)
        const onDemanSlot = preferredLocation.deliveryChannel === "ONLINE" ? SlotUtil.getOnDemandMealSlot(tz) : undefined
        days = (!isTodayCutOffPassed || onDemanSlot === mealSlot) ? days : days.slice(1)
        let filteredMenus: ProductMenu[] = []
        let day: string
        let inventoryResult
        for (let i = 0; i < days.length; i++) {
            filteredMenus = await this.menuService.getMenuForDateMealSlot(days[i], mealSlot, preferredLocationAttributes.areaId)
            if (!_.isEmpty(filteredMenus)) {
                inventoryResult = await this.capacityService.getPackAvailabilityForMealSlotDetailed(days[i], preferredLocationAttributes.areaId, mealSlot, preferredLocation.latLong)
                day = days[i]
                filteredMenus.sort((a, b) => {
                    return a.ordering < b.ordering ? -1 : 1
                })
                break
            }
        }
        if (excludeHiddenItems) {
            filteredMenus = <ProductMenu[]>await this.capacityService.filterOutHiddenProducts(filteredMenus)
        }
        return { day: day, menus: filteredMenus, inventoryResult: inventoryResult }
    }

    private isProductDisabledForEatFit(product: Product) {
        if (!_.isEmpty(product.listingBrands) && !_.includes(product.listingBrands, "EAT_FIT")) {
            return true
        }
        return false
    }

    async filterOutAddMealHiddenProducts(menuList: ProductMenu[]): Promise<ProductMenu[]> {
        if (_.isEmpty(menuList)) {
            return []
        }
        const productMap = await this.catalogueService.getProductMap(menuList.map(menu => menu.productId))
        return menuList.filter(menu => {
            const product = productMap[menu.productId]
            if (!product) {
                return true
            }
            const category = this.categoryService.getCategoryById(product.categoryId)
            if (category && category.hideFromAddMeal) {
                return false
            }

            if (this.isProductDisabledForEatFit(product)) {
                return false
            }
            return true
        })
    }

    getUpcomingShipment(foodPackBooking: FoodPackBooking, timezone: Timezone): FoodBooking {
        let foodBooking: FoodBooking
        if (foodPackBooking.activeShipment &&
            !SlotUtil.isCancelCutOffPassed(foodPackBooking.activeShipment.deliveryDate, timezone, foodPackBooking.activeShipment.deliverySlot.slotId)) {
            foodBooking = foodPackBooking.activeShipment
            return foodBooking
        }
        if (!_.isEmpty(foodPackBooking.futureShipment)) {
            return foodPackBooking.futureShipment[0]
        }
        return null
    }

    getChangeableShipmentsFromFutureBookings(futureBookings: FoodBooking[], userContext: UserContext): Map<FoodBooking, boolean> {
        const result: Map<FoodBooking, boolean> = new Map<FoodBooking, boolean>()
        if (MealUtil.isAddMealSupported(userContext)) {
            const changeableBookings = futureBookings.filter(function (booking: FoodBooking) {
                const bookingTz = booking.timezone
                return !SlotUtil.isCancelCutOffPassed(booking.deliveryDate, bookingTz, booking.deliverySlot ? booking.deliverySlot.slotId : undefined, booking.deliveryWindow, booking.deliveryChannel)
                    && !this.isAlreadyCancelled(booking) && !this.isAlreadyDelivered(booking)
            }.bind(this))
            changeableBookings.forEach((booking) => {
                result.set(booking, true)
            })
        } else {
            const changeablefulfilmentBookingMap: Map<string, FoodBooking> = new Map<string, FoodBooking>()
            _.each(futureBookings, function (booking: FoodBooking) {
                const bookingTz = booking.timezone
                const changeableBooking = changeablefulfilmentBookingMap.get(booking.fulfilmentId)
                if (!SlotUtil.isCancelCutOffPassed(booking.deliveryDate, bookingTz, booking.deliverySlot ? booking.deliverySlot.slotId : undefined, booking.deliveryWindow, booking.deliveryChannel)
                    && !this.isAlreadyCancelled(booking) && !this.isAlreadyDelivered(booking)) {
                    if (_.isNil(changeableBooking)
                        || momentTz.tz(changeableBooking.deliveryDate, bookingTz).isAfter(momentTz.tz(booking.deliveryDate, bookingTz))) {
                        changeablefulfilmentBookingMap.set(booking.fulfilmentId, booking)
                    }
                }
            }.bind(this))
            changeablefulfilmentBookingMap.forEach(function (booking: FoodBooking, fulfilmentId: string) {
                result.set(booking, true)
            })
        }
        return result
    }

    public getOrderStatusForFoodBooking(foodBooking: FoodBooking, userContext: UserContext): { eta: Date, status: string, statusText: string } {
        let etaDate: Date
        let statusText: string
        let status: string
        const bookingTz = foodBooking.timezone
        const mealSlot: MenuType = foodBooking.mealSlot ? foodBooking.mealSlot : SlotUtil.getMealSlot(foodBooking.deliverySlot)

        // Note: OrderTrackingStatusWidget will start generating ETA once delivery guy has picked up the order
        const orderTrackingWidget = new OrderTrackingStatusWidget(foodBooking, mealSlot, userContext)
        if (orderTrackingWidget.values) {
            const activeState = orderTrackingWidget.values.find(value => {
                return value.active
            })
            if (activeState && activeState.status !== "DELIVERED") {
                etaDate = activeState.expectedDeliveryTime ? new Date(foodBooking.eta) : undefined
                status = activeState.status
                statusText = activeState.title
            }
        }

        if (!_.isNil(etaDate)) return {
            eta: etaDate,
            status: status,
            statusText: statusText
        }

        // Now handle cases where delivery guy has not yet picked up the order
        if (foodBooking.deliveryType === "ON_DEMAND") {
            const endHourMin: HourMin = {
                hour: momentTz.tz(foodBooking.deliveryWindow.end, bookingTz).get("hours"),
                min: momentTz.tz(foodBooking.deliveryWindow.end, bookingTz).get("minutes")
            }
            etaDate = foodBooking.eta ? new Date(foodBooking.eta) : TimeUtil.getDate(foodBooking.deliveryDate, endHourMin.hour, endHourMin.min, bookingTz)
        } else if (foodBooking.address.addressType === "KIOSK") {
            // As the Delivery Slot Timings are in IST Format, we are defaulting it IST_TIMEZONE
            const deliveryWindowStartMoment = TimeUtil.getMomentForDate(foodBooking.deliveryWindow.start, bookingTz)
            etaDate = TimeUtil.getDate(foodBooking.deliveryDate, deliveryWindowStartMoment.hours(), deliveryWindowStartMoment.minutes(), bookingTz)
        } else {
            // Take middle time of delivery window and round off to nearest 5min
            const deliveryWindowStartMoment = TimeUtil.getMomentForDate(foodBooking.deliveryWindow.start, bookingTz)
            const deliveryWindowEndMoment = TimeUtil.getMomentForDate(foodBooking.deliveryWindow.end, bookingTz)
            const startDate = TimeUtil.getDate(foodBooking.deliveryDate, deliveryWindowStartMoment.hours(), deliveryWindowStartMoment.minutes(), bookingTz)
            const endDate = TimeUtil.getDate(foodBooking.deliveryDate, deliveryWindowEndMoment.hours(), deliveryWindowEndMoment.minutes(), bookingTz)
            const midTime = startDate.getTime() + ((endDate.getTime() - startDate.getTime()) / 2)
            const midTimeRoundedToNearestFiveMin = Math.ceil(midTime / (5 * 60 * 1000)) * (5 * 60 * 1000) // rounding off to nearest 5 min ceil
            etaDate = foodBooking.eta ? new Date(foodBooking.eta) : TimeUtil.parseDateFromEpoch(midTimeRoundedToNearestFiveMin)
        }
        return {
            eta: etaDate,
            status: status,
            statusText: ""
        }
    }

    public async getNavigationUrlForFoodBooking(userContext: UserContext, foodBooking: FoodBooking): Promise<string> {
        let url: string
        const mealSlot: MenuType = foodBooking.mealSlot ? foodBooking.mealSlot : SlotUtil.getMealSlotForSlotId(foodBooking.deliverySlot.slotId, foodBooking.timezone)
        if (foodBooking.products.length > 1) {
            if (AppUtil.isWeb(userContext)) {
                url = `curefit://orderpage?orderId=${foodBooking.orderId}`
            } else {
                url = ActionUtil.foodCart(foodBooking.fulfilmentId, mealSlot, foodBooking.deliveryDate)
            }
        } else {
            const product = await this.catalogueService.getProduct(foodBooking.productId)
            const seoParams: SeoUrlParams = {
                productName: product.title
            }
            if (foodBooking.packId) {
                url = ActionUtil.foodSingleFromPack(foodBooking.productId, foodBooking.deliveryDate, mealSlot, false, foodBooking.packId, foodBooking.fulfilmentId, userContext.sessionInfo.userAgent, seoParams, foodBooking.listingBrand)
            } else {
                url = ActionUtil.foodSingle(foodBooking.productId, foodBooking.deliveryDate, mealSlot, false, foodBooking.fulfilmentId, userContext.sessionInfo.userAgent, seoParams, undefined, foodBooking.listingBrand)
            }
        }
        return url
    }

    async getFoodMarketplaceManageOptions(userContext: UserContext, gnadalfBooking: UpcomingOrderDetailsResponse, issuesMap: Map<string, CustomerIssueType[]>): Promise<{ manageOptions: ManageOptions, meta: any }> {

        const manageOptions: ManageOptions = {
            displayText: "Your Food order",
            icon: "RESCHEDULE_OR_CANCEL",
            options: [],
        }

        const issueList: { code: string, title: string, confirmation: string }[] = []
        issuesMap.get("EatPack").forEach(customerIssueType => { // todo: Nisheet put our own issue map key
            issueList.push({
                code: customerIssueType.code,
                title: customerIssueType.subject,
                confirmation: customerIssueType.confirmation
            })
        })
        manageOptions.options.push({
            isEnabled: true, // assuming this should be always present
            type: "REPORT_ISSUE",
            meta: issueList,
            action: SUPPORT_DEEP_LINK,
            displayText: "Need Help"
        })


        manageOptions.options.push({
            isEnabled: !_.isNil(gnadalfBooking.trackUrl),
            type: "NAVIGATION",
            meta: undefined,
            action: FoodMarketplaceUtil.getFoodMarketplaceOrderTrackingUrl(gnadalfBooking.trackUrl),
            displayText: "Order Tracking"
        })

        return {
            manageOptions,
            meta: undefined
        }
    }

    async getMealManageOptions(userContext: UserContext, foodBooking: FoodBooking, issuesMap: Map<string, CustomerIssueType[]>, isPackOrder: boolean,
        isPackActive: boolean, enableProductOptions: boolean, enableReportIssue: boolean, isTodayScreen: boolean, onDemand?: boolean, rescheduleDisplayText?: string): Promise<{ manageOptions: ManageOptions, meta: any }> {

        const tz = foodBooking.timezone
        const manageOptions: ManageOptions = {
            displayText: "",
            icon: "RESCHEDULE_OR_CANCEL",
            options: [],
        }
        const startDate = TimeUtil.getMomentForDate(foodBooking.deliveryWindow.start, tz)
        const endDate = TimeUtil.getMomentForDate(foodBooking.deliveryWindow.end, tz)
        const startTime: HourMin = {
            hour: startDate.hours(),
            min: startDate.minutes()
        }
        const endTime: HourMin = {
            hour: endDate.hours(),
            min: endDate.minutes()
        }
        const isAlreadyDelivered: boolean = this.isAlreadyDelivered(foodBooking)
        const isAlreadyCancelled: boolean = this.isAlreadyCancelled(foodBooking)
        const mealSlot: MenuType = foodBooking.mealSlot ? foodBooking.mealSlot : (foodBooking.deliverySlot ? SlotUtil.getMealSlotForSlotId(foodBooking.deliverySlot.slotId, tz) : SlotUtil.getClosestMealSlot(startTime))
        const deliveryChannel: DeliveryChannel = EatUtil.getDeliveryChannel(foodBooking.address)
        const isCutOffPassed: boolean = SlotUtil.isCancelCutOffPassed(foodBooking.deliveryDate, tz, foodBooking.deliverySlot ? foodBooking.deliverySlot.slotId : undefined, foodBooking.deliveryWindow)
        const isEnabled = !isAlreadyDelivered && !isCutOffPassed && isPackActive
        const timestring = foodBooking.deliveryDate + " 00:00"
        const dateString = TimeUtil.formatDateStringInTimeZone(timestring, tz, "ddd, DD MMM")
        // Showing undo only for the single meal bought part of pack
        let productParams: {
            fulfilmentId: string,
            date: string,
            productId: string,
            packId?: string
        }
        if (foodBooking.packId) {
            productParams = {
                fulfilmentId: foodBooking.fulfilmentId,
                date: foodBooking.deliveryDate,
                packId: foodBooking.packId,
                productId: foodBooking.productId
            }
        } else {
            productParams = {
                fulfilmentId: foodBooking.fulfilmentId,
                date: foodBooking.deliveryDate,
                // packId: foodBooking.packId,
                productId: foodBooking.productId
            }
        }

        const isKioskOrCafe = deliveryChannel === "KIOSK" || deliveryChannel === "CAFE"
        manageOptions.displayText = MealUtil.getSlotDisplayTextFromTime(startTime, endTime, isKioskOrCafe)
        manageOptions.icon = "RESCHEDULE_OR_CANCEL"
        if (!isAlreadyCancelled && !isAlreadyDelivered && !onDemand) {
            manageOptions.displayText = "Reschedule meal"
            manageOptions.icon = "RESCHEDULE_OR_CANCEL"
            if (foodBooking.listingBrand && foodBooking.listingBrand !== "WHOLE_FIT") {
                const deliverySlots: DeliverySlot[] = foodBooking.address.kioskType === "CAFE" ? SlotUtil.getAllDeliverySlotForCafe() : this.deliverySlotService.getDeliverySlotsForListingBrand(mealSlot, deliveryChannel, foodBooking.listingBrand)
                deliverySlots.forEach(deliverySlot => {
                    const isSlotOrderCutOffPassed = SlotUtil.isHardCutOffPassed(foodBooking.deliveryDate, tz, deliverySlot.slotId)
                    if (!isSlotOrderCutOffPassed) {
                        manageOptions.displayText = rescheduleDisplayText || MealUtil.getSlotDisplayTextFromTime(startTime, endTime, isKioskOrCafe)
                        manageOptions.icon = "RESCHEDULE_OR_CANCEL"
                        const displayText = MealUtil.getSlotDisplayTextFromTime(deliverySlot.startTime, deliverySlot.endTime, isKioskOrCafe)
                        const rescheduleMeta = Object.assign({}, deliverySlot, productParams, { displayText: displayText })
                        manageOptions.options.push({
                            isEnabled: enableProductOptions && isEnabled,
                            displayText: displayText,
                            type: foodBooking.products.length > 1 ? "RESCHEDULE_CART" : "RESCHEDULE_MEAL",
                            meta: rescheduleMeta,
                            listingBrand: foodBooking.listingBrand
                        })
                    }
                })
            } else {
                const deliverySlots: DeliverySlot[] = this.deliverySlotService.getDeliverySlotsForListingBrand(mealSlot, deliveryChannel, foodBooking.listingBrand)
                const selectedSlot = this.deliverySlotService.getDeliverySlotFromDeliveryWindow(foodBooking.deliveryWindow, deliveryChannel, foodBooking.listingBrand, mealSlot, tz)
                manageOptions.displayText = MealUtil.getSlotDisplayTextFromTime(startTime, endTime, isKioskOrCafe, momentTz.tz(foodBooking.deliveryDate, tz).toDate())
                manageOptions.icon = "RESCHEDULE_OR_CANCEL"
                const slots: any[] = []
                const availableSlots: any[] = []
                const now = TimeUtil.now(tz)
                _.forEach(deliverySlots, slot => {
                    let isCutOffPassed: boolean = false
                    if (foodBooking.deliveryDate === TimeUtil.todaysDate(tz)) {
                        const deliveryWindowForSlot = SlotUtil.getDeliveryWindowFromHourMin(foodBooking.deliveryDate, slot.startTime, slot.endTime, tz)
                        const hardCutOffByChannelOrSlot = SlotUtil.getHardCutOffByChannelOrSlot(undefined, deliveryWindowForSlot, deliveryChannel, tz)
                        isCutOffPassed = SlotUtil.compare(now, hardCutOffByChannelOrSlot) > 0
                    }
                    const slotObj: any = {}
                    slotObj.isEnabled = enableProductOptions && isEnabled && !isCutOffPassed
                    slotObj.title = MealUtil.getDisplayTextForSlot(slot)
                    slotObj.slotId = slot.slotId
                    slotObj.meta = clone(slot)
                    availableSlots.push(slotObj)
                })
                slots.push({
                    deliveryDate: foodBooking.deliveryDate,
                    deliverySlots: availableSlots,
                    title: TimeUtil.getDayText(foodBooking.deliveryDate, foodBooking.timezone)
                })

                manageOptions.options.push({
                    isEnabled: enableProductOptions && isEnabled,
                    displayText: MealUtil.getSlotDisplayTextFromTime(startTime, endTime, isKioskOrCafe, momentTz.tz(foodBooking.deliveryDate, tz).toDate()),
                    type: foodBooking.products.length > 1 ? "RESCHEDULE_CARTV2" : "RESCHEDULE_MEALV2",
                    slots: slots,
                    listingBrand: foodBooking.listingBrand,
                    deliveryDate: foodBooking.deliveryDate,
                    deliverySlot: selectedSlot ? selectedSlot.slotId : undefined
                })

            }
        }
        if (!isAlreadyCancelled && !isAlreadyDelivered) {
            const status = EatUtil.getEatFitDeliveryStatus(foodBooking.state, foodBooking.listingBrand)
            if (isTodayScreen && status !== undefined && status !== "REJECTED") {
                const product = await this.catalogueService.getProduct(foodBooking.productId)
                const seoParams: SeoUrlParams = {
                    productName: product.title
                }
                manageOptions.options.push({
                    isEnabled: enableProductOptions,
                    displayText: `Track Order`,
                    type: "BOOK_TC", // should be NAVIGATION, revert after killing appversion
                    action: MealUtil.isOrderTrackingSupported(userContext) ? ActionUtil.trackFoodOrder(foodBooking.fulfilmentId, foodBooking.deliveryDate) : foodBooking.products.length > 1 ? ActionUtil.foodCart(foodBooking.fulfilmentId, mealSlot, foodBooking.deliveryDate) : ActionUtil.foodSingle(foodBooking.productId, foodBooking.deliveryDate, mealSlot, false, foodBooking.fulfilmentId, userContext.sessionInfo.userAgent, seoParams),
                })
            }
        }
        if (AppUtil.isNewReportIssueSupported(userContext)) {
            const reportIssueParams = await this.issueBusiness.getMealBookingIssueParams(foodBooking, tz)
            const issues = await this.issueBusiness.getIssues(reportIssueParams.productType, reportIssueParams.productStates, reportIssueParams.meta, userContext)
            manageOptions.options.push(this.issueBusiness.toManageOptionPayload(issues, enableReportIssue))
        } else {
            AppUtil.isMealPageReportIssueEnabled(userContext) && manageOptions.options.push({
                isEnabled: enableReportIssue,
                displayText: "Need Help",
                type: "REPORT_ISSUE",
                meta: this.getMealIssueList(issuesMap, isAlreadyDelivered, isPackOrder),
                action: SUPPORT_DEEP_LINK
            })
        }
        return { manageOptions: manageOptions, meta: productParams }
    }

    public async getPossibleActionsForFoodBooking(foodBooking: FoodBooking, userContext: UserContext, foodFulfilment?: FoodFulfilment, date?: string, supportedActionTypes?: MealActionType[], foodPackBooking?: FoodPackBooking): Promise<Action[]> {
        const possibleActions: Action[] = []
        const isWeb = ["DESKTOP", "MBROWSER"].includes(userContext.sessionInfo.userAgent)
        supportedActionTypes = supportedActionTypes || POSSIBLE_FOOD_BOOKING_ACTION_TYPES

        // whole fit actions not supported on web
        if (isWeb && foodBooking.listingBrand && foodBooking.listingBrand === "WHOLE_FIT") {
            return possibleActions
        }
        const timezone = userContext.userProfile.timezone
        const isAlreadyDelivered: boolean = this.isAlreadyDelivered(foodBooking)
        const isAlreadyCancelled: boolean = this.isAlreadyCancelled(foodBooking)
        const onDemand: boolean = foodBooking.deliveryType === "ON_DEMAND"
        const canBeCancelledDueToRain: boolean = _.isNil(foodBooking.canBeCancelled) ? false : foodBooking.canBeCancelled
        const deliveryChannel: DeliveryChannel = EatUtil.getDeliveryChannel(foodBooking.address)
        const isKioskOrCafe = deliveryChannel === "KIOSK" || deliveryChannel === "CAFE"
        const productParams = {
            fulfilmentId: foodBooking.fulfilmentId,
            date: foodBooking.deliveryDate,
            packId: foodBooking.packId,
            productId: foodBooking.productId
        }
        const isCancelCutoffPassed: boolean = SlotUtil.isCancelCutOffPassed(foodBooking.deliveryDate, timezone, foodBooking.deliverySlot ? foodBooking.deliverySlot.slotId : undefined, foodBooking.deliveryWindow, foodBooking.deliveryChannel)
        const isCutOffPassed: boolean = canBeCancelledDueToRain === true ? false : isCancelCutoffPassed
        const isEnabled: boolean = !isAlreadyCancelled && !isAlreadyDelivered && !isCutOffPassed
        const mealSlot: MenuType = foodBooking.mealSlot ? foodBooking.mealSlot : SlotUtil.getMealSlot(foodBooking.deliverySlot)
        let packInfo: FoodPack = undefined
        if (foodBooking.packId) {
            packInfo = await this.catalogueService.getFoodPack(foodBooking.packId)
        }
        const isAddChangeMealNotSupportedTagPresent: boolean = !_.isEmpty(packInfo) && packInfo.tags?.indexOf(EatSubscriptionUtil.ADD_CHANGE_MEAL_NOT_SUPPORTED_TAG) > -1
        const isChangeAddMealSupported: boolean = (
            !isAddChangeMealNotSupportedTagPresent
            && isEnabled
            && (MealUtil.isAddMealSupported(userContext) || foodBooking.packId !== undefined)
            && (foodBooking.address.kioskType !== "CAFE")
            && foodBooking.listingBrand !== "WHOLE_FIT"
        )
        // QR Code Action for KIOSK
        if (
            foodBooking.state !== "DELIVERED"
            && foodBooking.state != "CANCELLED"
            && foodBooking.address.addressType === "KIOSK"
            && supportedActionTypes.includes("PICKUP_MEAL")
        ) {
            possibleActions.push({
                title: "QR code",
                actionType: "NAVIGATION",
                icon: "PICKUP_MEAL",
                url: await this.getNavigationUrlForFoodBooking(userContext, foodBooking)
            })
        } else {
            // Track Order action
            const currentStatus: OrderTrackingStatus = EatUtil.getEatFitDeliveryStatus(foodBooking.state, foodBooking.listingBrand)
            if (
                currentStatus
                && currentStatus !== "REJECTED"
                && foodBooking.address.addressType !== "KIOSK"
                && foodBooking.disableOrderTracking !== true
                && supportedActionTypes.includes("TRACK_MEAL")
            ) {
                possibleActions.push({
                    title: "Track",
                    actionType: "NAVIGATION",
                    icon: "LOCATION",
                    url: MealUtil.isOrderTrackingSupported(userContext) ?
                        ActionUtil.trackFoodOrder(foodBooking.fulfilmentId, foodBooking.deliveryDate) :
                        await this.getNavigationUrlForFoodBooking(userContext, foodBooking)
                })
            }
        }

        // Change meal Action
        if (isChangeAddMealSupported && supportedActionTypes.includes("CHANGE_MEAL")) {
            let url
            if (foodPackBooking && AppUtil.isSubscriptionAttachSupported(userContext) && !_.isNil(foodPackBooking.subPackBookings) && !_.isEmpty(foodPackBooking.subPackBookings)) {
                url = ActionUtil.changeMealPageSubPackBooking(foodBooking.deliveryDate, mealSlot, foodBooking.fulfilmentId)
            } else {
                url = ActionUtil.changeMealPage(foodBooking.productId, foodBooking.deliveryDate, mealSlot, foodBooking.fulfilmentId, foodBooking.packId === undefined)
            }
            possibleActions.push({
                actionType: "NAVIGATION",
                icon: "CHANGE_MEAL",
                url: url,
                title: MealUtil.isAddMealSupported(userContext) ? (foodBooking.packId ? "Change / Add items" : "Add items") : "Change meal"
            })
        }

        // Cancel Action, the assumption made here is that if UNDO_CANCEL_MEAL is supported the UNDO_CANCEL_CART will also be supported (same for CANCEL_MEAL)
        if (isAlreadyCancelled && foodBooking.packId && !isCancelCutoffPassed && supportedActionTypes.includes("UNDO_CANCEL_MEAL")) {
            possibleActions.push({
                actionType: foodBooking.products.length === 1 ? "UNDO_CANCEL_MEAL" : "UNDO_CANCEL_CART",
                icon: "UNDO",
                title: "Undo Cancel",
                meta: {
                    fulfilmentId: foodBooking.fulfilmentId,
                    date: foodBooking.deliveryDate,
                    packId: foodBooking.packId,
                    productId: foodBooking.productId
                }

            })
        } else if (isEnabled && supportedActionTypes.includes("CANCEL_MEAL")) {
            possibleActions.push({
                actionType: foodBooking.products.length === 1 ? "CANCEL_MEAL" : "CANCEL_CART",
                icon: "CANCEL",
                title: "Cancel",
                meta: {
                    fulfilmentId: foodBooking.fulfilmentId,
                    date: foodBooking.deliveryDate,
                    packId: foodBooking.packId,
                    productId: foodBooking.productId
                }
            })
        }

        // Reschedule Actions
        if (!isAlreadyCancelled && !isAlreadyDelivered && foodFulfilment && !onDemand && supportedActionTypes.includes("MEAL_RESCHEDULE_OPTIONS")) {
            if (foodBooking.listingBrand && foodBooking.listingBrand !== "WHOLE_FIT") {
                const activeDeliverySlot: string = FoodBusinessUtil.getDeliverySlot(foodFulfilment, date)
                const deliverySlots: DeliverySlot[] = (
                    foodBooking.address.kioskType === "CAFE"
                        ? SlotUtil.getAllDeliverySlotForCafe()
                        : this.deliverySlotService.getDeliverySlotsForListingBrand(mealSlot, deliveryChannel, foodBooking.listingBrand)
                )
                const options = deliverySlots.map(deliverySlot => {
                    const isSlotOrderCutOffPassed = SlotUtil.isHardCutOffPassed(foodBooking.deliveryDate, timezone, deliverySlot.slotId)
                    if (!isSlotOrderCutOffPassed) {
                        const displayText = MealUtil.getSlotDisplayTextFromTime(deliverySlot.startTime, deliverySlot.endTime, isKioskOrCafe)
                        const rescheduleMeta = Object.assign({}, deliverySlot, productParams, { displayText: displayText })
                        return {
                            listingBrand: foodBooking.listingBrand,
                            isEnabled: isEnabled,
                            displayText: displayText,
                            type: foodBooking.products.length > 1 ? "RESCHEDULE_CART" : "RESCHEDULE_MEAL",
                            meta: rescheduleMeta
                        }
                    }
                })
                possibleActions.push({
                    actionType: "MEAL_RESCHEDULE_OPTIONS",
                    meta: Object.assign({}, productParams, {
                        options,
                        activeDeliverySlot
                    }),
                    title: "CHANGE TIME"
                })
            }
        }

        return possibleActions
    }

    async getCancelDifference(foodBooking: FoodBooking, timezone: Timezone = TimeUtil.IST_TIMEZONE): Promise<number> {
        // Temp hack (foodBooking.products.length > 1) as pack id is coming in cart single purcahse as well
        if (foodBooking.products.length > 1 || !foodBooking.packId)
            return 0
        const orderId = foodBooking.orderId
        const deliveryDate = foodBooking.deliveryDate
        const orderPromise = this.omsApiClient.getOrder(orderId)
        const fulfilmentPromise = this.fulfilmentService.getFoodFulfilmentByFulfilmentId(foodBooking.fulfilmentId)
        const packMenuPromise = this.menuService.getPackMenuForDate(deliveryDate,
            foodBooking.packId, foodBooking.address.areaId)
        const packMenu = await packMenuPromise
        const product = await this.catalogueService.getProduct(packMenu.productId)
        const [order, fulfilment] = await Promise.all([orderPromise, fulfilmentPromise])
        return this.changeMealService.getDiffAmountToPay(order, fulfilment, product, deliveryDate, null, foodBooking.packId)

    }

    getChangeMealOption(foodBooking: FoodBooking, userContext: UserContext): ManageOptionPayload {
        const timezone = userContext.userProfile.timezone
        const mealSlot: MenuType = foodBooking.mealSlot ? foodBooking.mealSlot : SlotUtil.getMealSlotForSlotId(foodBooking.deliverySlot.slotId, timezone)
        const isEnabled: boolean = this.canModifyBooking(foodBooking, timezone) && foodBooking.changeMealAllowed
        const manageOptionPayload: ManageOptionPayload = {
            displayText: MealUtil.isAddMealSupported(userContext) ? (foodBooking.packId ? "Change / Add items" : "Add items") : "Change meal",
            action: ActionUtil.changeMealPage(foodBooking.productId, foodBooking.deliveryDate, mealSlot, foodBooking.fulfilmentId, foodBooking.packId === undefined),
            type: "CHANGE_MEAL",
            listingBrand: foodBooking.listingBrand,
            isEnabled: (MealUtil.isAddMealSupported(userContext) || foodBooking.packId !== undefined) && isEnabled
        }
        return manageOptionPayload
    }

    async getMealReportIssue(userContext: UserContext, foodBooking: FoodBooking, issuesMap: Map<string, CustomerIssueType[]>, isPackOrder: boolean): Promise<{ manageOptions: ManageOptions, meta: any }> {
        const timezone = userContext.userProfile.timezone
        const manageOptions: ManageOptions = {
            displayText: "Need Help",
            icon: "REPORT_ISSUE",
            options: [],
        }
        if (AppUtil.isNewReportIssueSupported(userContext)) {
            const reportIssueParams = await this.issueBusiness.getMealBookingIssueParams(foodBooking, timezone)
            const issues = await this.issueBusiness.getIssues(reportIssueParams.productType, reportIssueParams.productStates, reportIssueParams.meta, userContext)
            manageOptions.options.push(this.issueBusiness.toManageOptionPayload(issues, true))
        } else {
            const isAlreadyDelivered: boolean = this.isAlreadyDelivered(foodBooking)
            manageOptions.options.push({
                isEnabled: true,
                displayText: "Need Help",
                type: "REPORT_ISSUE",
                meta: this.getMealIssueList(issuesMap, isAlreadyDelivered, isPackOrder),
                action: SUPPORT_DEEP_LINK
            })
        }

        return { manageOptions: manageOptions, meta: {} }
    }

    canModifyBooking(foodBooking: FoodBooking, timezone: Timezone): boolean {
        const isAlreadyDelivered: boolean = this.isAlreadyDelivered(foodBooking)
        const isAlreadyCancelled: boolean = this.isAlreadyCancelled(foodBooking)
        const canBeCancelledDueToRain: boolean = _.isNil(foodBooking.canBeCancelled) ? false : foodBooking.canBeCancelled
        const isCancelCutoffPassed: boolean = SlotUtil.isCancelCutOffPassed(foodBooking.deliveryDate, timezone, foodBooking.deliverySlot.slotId)
        const isCutOffPassed: boolean = canBeCancelledDueToRain === true ? false : isCancelCutoffPassed
        return !isAlreadyCancelled && !isAlreadyDelivered && !isCutOffPassed
    }

    // TODO: TG: add support for credit in the reschedule modal
    async getCultManageOptions(userContext: UserContext, productType: ProductType, cultBooking: CultBooking,
        issuesMap: Map<string, CustomerIssueType[]>,
        enableProductOptions: boolean, enableReportIssue: boolean, cafeFoodDetails?: FoodFulfilment): Promise<{ manageOptions: ManageOptions, meta: any }> {
        const tz = userContext.userProfile.timezone
        const isCancelEnabled = enableProductOptions && cultBooking.label !== "Cancelled" && cultBooking.isBookingCancellable
        const cancelCutOffTime = TimeUtil.getMomentForDateString(cultBooking.Class.startTime, tz, "hh:mm:ss")
            .subtract(cultBooking.Class.bookingCancellationLimit, "minutes").format("h:mm A")
        const helperText = isCancelEnabled ? `Cancel by ${cancelCutOffTime}` : ""
        const isUserEligibleForDropout = AppUtil.isClassDropoutSupported(userContext)
        const isDropoutSupportedForBooking = enableProductOptions && CultUtil.isDropoutAvailableForBooking(cultBooking, isUserEligibleForDropout)
        const manageOptions: ManageOptions = {
            displayText: "Cancel this session",
            helperText: helperText,
            options: [],
            icon: "CANCEL",
            creditCost: cultBooking.creditCost || null
        }


        const productParams = {
            productType: productType,
            bookingNumber: cultBooking.bookingNumber,
            classId: !_.isNil(cultBooking.Class) ? cultBooking.Class.id.toString() : null
        }

        if (isCancelEnabled || isDropoutSupportedForBooking) {
            const cancelClassInfo = {
                displayText: isDropoutSupportedForBooking ? `Cancel (till ${cancelCutOffTime})` : "Cancel class",
                isEnabled: isCancelEnabled,
                showDisabled: true
            }
            manageOptions.options.push({
                ...cancelClassInfo,
                type: "CANCEL_CULT_CLASS",
                meta: {
                    ...productParams,
                    cancellationDialogMessage: "Class Cancellation Successful",
                    cancellationDialogSuccessMessage: "Your timely cancellation gives others the chance to workout \n\nThank You!",
                    cancellationDialogLottieUrl: !_.isNil(cultBooking.creditCost) ? "/image/mem-exp/lottie/refund_lottie.json" : null,
                    cancellationDialogLottieTextList: !_.isNil(cultBooking.creditCost)
                        ? [{originalText: "CREDITS_TEXT_PLACEHOLDER", finalText: cultBooking.creditCost + " Credit" + ((cultBooking.creditCost === 1) ? "" : "s") + " refunded"}]
                        : null,
                    pageAnalytics: {
                        "classCredit": cultBooking.creditCost,
                        "bookingNumber": cultBooking.bookingNumber
                    }
                },
            })
        }

        if (isDropoutSupportedForBooking) {
            const dropoutStartTime = momentTz.tz(cultBooking.Class.dropoutStartTime, "hh:mm:ss", tz).format("h:mm A")
            const timeRange = cultBooking.Class.dropoutStartTime.split(":")
            const dropoutTimeEpoch = TimeUtil.getDate(cultBooking.Class.date, Number(timeRange[0]), Number(timeRange[1]), tz).getTime()
            const currentTime = TimeUtil.getCurrentEpoch()
            const isDropoutOptionEnabled = isDropoutSupportedForBooking && currentTime > dropoutTimeEpoch
            manageOptions.options.push({
                displayText: isDropoutOptionEnabled ? `Dropout (from ${dropoutStartTime})` : `Dropout (after ${dropoutStartTime})`,
                type: "CULT_CLASS_DROPOUT",
                meta: {
                    ...productParams,
                    confirmationMessage: "Since this is last minute, if another member books the slot you are dropping out of then only the No-show will be waived for you.",
                    successMessage: "Dropped out successfully"
                },
                isEnabled: isDropoutOptionEnabled,
                showDisabled: true,
            })
        }

        const isUpcomingClass: boolean = cultBooking.label === "Upcoming"
        if (AppUtil.isNewReportIssueSupported(userContext)) {
            const reportIssueParams = this.issueBusiness.getCultOrMindCenterBookingIssueParams(cultBooking, productType)
            const issues = await this.issueBusiness.getIssues(reportIssueParams.productType, reportIssueParams.productStates, reportIssueParams.meta, userContext)
            manageOptions.options.push(this.issueBusiness.toManageOptionPayload(issues, enableReportIssue))
        } else {
            manageOptions.options.push({
                isEnabled: enableReportIssue,
                displayText: "Need Help",
                type: "REPORT_ISSUE",
                meta: enableReportIssue ? this.getCultIssueList(issuesMap, isUpcomingClass, cultBooking.Membership) : [],
                action: SUPPORT_DEEP_LINK,
            })
        }

        await this.addLogCultScoreOptionInCultManageOptions(cultBooking, userContext, manageOptions)

        return { manageOptions: manageOptions, meta: productParams }
    }

    async addLogCultScoreOptionInCultManageOptions(cultBooking: CultBooking, userContext: UserContext, manageOptions: ManageOptions) {
        const isOngoingOrCompletedClass: boolean = cultBooking.label === "Ongoing" || cultBooking.label == "Completed"
        if (isOngoingOrCompletedClass && CULTSCORE_WORKOUT_IDS.indexOf(cultBooking.Class.Workout.id) > -1) {
            let userTestDetail
            const userTest = await this.userTestService.searchUserTest(userContext.userProfile.userId, "CULT_SCORE_CLASS", cultBooking.Class.id.toString())
            if (_.isEmpty(userTest)) {
                // if user test is not there, create user test
                const requestBody = {
                    userId: userContext.userProfile.userId,
                    source: "CULT_SCORE_CLASS",
                    sourceId: cultBooking.Class.id
                }
                userTestDetail = await this.userTestService.createUserTest(CULTSCORE_TESTID, requestBody)
            } else {
                userTestDetail = userTest[0]
            }
            const userTestId = userTestDetail.id
            if (userTestDetail.completed === false) {
                manageOptions.displayText = "Log Score"
                manageOptions.helperText = ""
                manageOptions.icon = "LOG"
                manageOptions.options = []
                const testDetail = await this.userTestService.getTestDetail(userTestDetail.testId)
                const stepUserInputs = UserTestUtil.getStepUserInputData(testDetail.testSteps)
                manageOptions.options.push({
                    isEnabled: isOngoingOrCompletedClass,
                    displayText: "Log Score",
                    type: "LOG_CULT_SCORE",
                    meta: {
                        title: "CULT SCORE",
                        subTitle: "You can edit the Scores",
                        stepUserInputs: stepUserInputs,
                        userTestId: userTestId,
                        actions: [
                            {
                                actionType: "NAVIGATION",
                                title: "View Score",
                                url: ActionUtil.userTestCompletionPage(testDetail.id, userTestId)
                            }
                        ]
                    },
                    action: ActionUtil.userTestScoreInputPage(testDetail.id, userTestId)
                })
            }
        }
    }

    async getCultWaitlistManageOptions(
        userContext: UserContext,
        productType: ProductType,
        cultWaitlistBooking: CultBooking,
        isWaitlistExtensionSupported?: boolean
    ): Promise<{ manageOptions: ManageOptions, meta: any }> {
        const currentTime: string = TimeUtil.todaysDate(userContext.userProfile.timezone, "YYYY-MM-DD HH:mm:ss")
        const classStartTime = cultWaitlistBooking.Class.date + "T" + cultWaitlistBooking.Class.startTime + cultWaitlistBooking.Class.timezone
        const timeRemainingToClass: number = TimeUtil.diffInMinutes(userContext.userProfile.timezone, currentTime, classStartTime)

        const isCancelEnabled = cultWaitlistBooking.state === "PENDING" && timeRemainingToClass > cultWaitlistBooking.Class.wlNotificationTime
        const cancelCutOffTime = TimeUtil.getMomentForDateString(cultWaitlistBooking.Class.startTime, userContext.userProfile.timezone, "hh:mm:ss")
            .subtract(cultWaitlistBooking.Class.wlNotificationTime, "minutes").format("h:mm A")
        const manageOptions: ManageOptions = {
            displayText: `Cancel by ${cancelCutOffTime}`,
            options: [],
            icon: "CANCEL",
        }
        const productParams: any = {
            isWaitlistBooking: true,
            productType: productType,
            classId: !_.isNil(cultWaitlistBooking.Class) ? cultWaitlistBooking.Class.id.toString() : null,
            bookingNumber: cultWaitlistBooking.wlBookingNumber,
            cancellationDialogMessage: "Waitlist cancelled",
            cancellationDialogSuccessMessage: "Your timely cancellation gives others the chance to workout \n\nThank You!",
            cancellationMessage: "Are you sure you want to leave the waitlist?",
            cancellationSuccessMessage: "Waitlist cancelled",
            cancellationDialogLottieUrl: !_.isNil(cultWaitlistBooking.creditCost) ? "/image/mem-exp/lottie/refund_lottie.json" : null,
            cancellationDialogLottieTextList: !_.isNil(cultWaitlistBooking.creditCost)
                ? [{originalText: "CREDITS_TEXT_PLACEHOLDER", finalText: cultWaitlistBooking.creditCost + " Credit" + ((cultWaitlistBooking.creditCost === 1) ? "" : "s") + " refunded"}]
                : null,
            pageAnalytics: {
                "classCredit": cultWaitlistBooking.creditCost,
                "bookingNumber": cultWaitlistBooking.bookingNumber
            }
        }

        if (isWaitlistExtensionSupported) {
            const waitlistNotificationTimeInfo: ManageOptionPayload = {
                displayText: "Waitlist confirmation slot",
                isEnabled: true,
                type: "SHOW_CULT_WAITLIST_NOTIFICATION_MODAL"
            }
            manageOptions.options.push(waitlistNotificationTimeInfo)
        }

        const waitlistClassInfo = {
            displayText: "Leave Waitlist",
            isEnabled: isCancelEnabled,
            showDisabled: !isCancelEnabled,
        }
        manageOptions.options.push({
            ...waitlistClassInfo,
            type: "CANCEL_CULT_CLASS",
            meta: productParams,
        })
        const reportIssueParams = this.issueBusiness.getCultOrMindCenterBookingIssueParams(cultWaitlistBooking, productType)
        const issues = await this.issueBusiness.getIssues(reportIssueParams.productType, reportIssueParams.productStates, reportIssueParams.meta, userContext)
        manageOptions.options.push(this.issueBusiness.toManageOptionPayload(issues, true))

        return { manageOptions: manageOptions, meta: productParams }
    }

    async getCultPackManageOptions(userContext: UserContext,
        packInfo: OfflineFitnessPack, membership: Membership, issuesMap: Map<string, CustomerIssueType[]>, segmentService: ISegmentService, moneyBackOfferDetail?: MoneyBackOfferDetail, membershipTransferable?: MembershipTransferable): Promise<{ manageOptions: ManageOptions, meta: any }> {
        let reportIssueOption: ManageOptionPayload
        if (AppUtil.isNewReportIssueSupported(userContext)) {
            const issues = await this.issueBusiness.getCultOrMindSubscriptionIssuesV2(userContext, membership, packInfo.productType)
            reportIssueOption = this.issueBusiness.toManageOptionPayload(issues, true)
        }
        const manageOptions: ManageOptions = {
            displayText: "Manage",
            options: reportIssueOption ? [reportIssueOption] : []
        }
        /**
         * Add manage options cult change start date
         * only for app and other version checks
         */
        const user: User = await userContext.userPromise
        const tz = userContext.userProfile.timezone
        if (AppUtil.isPackStartDateChangeSupported(userContext, user)) {
            /**
             * check if membershipStartDate is Editable
             */
            const membershipStartDateNotEditable = (membership.status === "PURCHASED" && isMembershipCurrent(userContext, membership)) || membership.status === "PAUSED"
            if (!membershipStartDateNotEditable && membership.metadata["membershipId"]) {
                manageOptions.options.push({
                    isEnabled: true,
                    displayText: "Change Start Date",
                    type: "NAVIGATION",
                    action: CultUtil.getStartDatePageUrl(membership.metadata["membershipId"], packInfo.productType)
                })
            } else if (AppUtil.isAnyActionSupportedInManageOptions(userContext)) {
                manageOptions.options.push({
                    isEnabled: true,
                    displayText: "Change Start Date",
                    type: "SHOW_ALERT_MODAL",
                    meta: {
                        title: "Start date cannot be changed",
                        subTitle: "Membership start date cannot be changed as membership already started",
                        actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
                    }
                })
            }
        }

        if (!MembershipItemUtil.isPlusMembership(membership) && AppUtil.isTransferMembershipSupported(userContext, user.isInternalUser) && packInfo.clientMetadata.cityId !== DUBAI_ACTUAL_CITY_ID && membership.metadata["membershipId"]) {

            if (membershipTransferable && membershipTransferable.value === false) {
                manageOptions.options.push({
                    isEnabled: true,
                    displayText: "Transfer Membership",
                    type: "SHOW_ALERT_MODAL",
                    meta: {
                        title: "Membership can't be transferred",
                        subTitle: membershipTransferable.message,
                        actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
                    }
                })
            } else {
                manageOptions.options.push(FitnessUtil.getTransferMembershipPageAction(membership.id.toString()))
            }
        }

        const cultBenefit = membership.benefits.find(benefit => (benefit.name == "CULT" || benefit.name == "CULT_GYM"))
        if (AppUtil.isPackUpgradeSupported(userContext)) {
            const isPackUpgradable = !membership.metadata["isTransferredPack"] && (membership.status == "PURCHASED" && cultBenefit.meta["allowedCenterServiceCenterIDs"])
            if (isPackUpgradable && membership.metadata["membershipId"]) {
                const upgradeCallOutText = "Upgrade to Cult Unlimited"
                const queryParams = {
                    membershipId:  membership.metadata["membershipId"],
                    productType: packInfo.productType,
                    packId: packInfo.productId,
                    title: upgradeCallOutText
                }
                manageOptions.options.push({
                    isEnabled: true,
                    displayText: upgradeCallOutText,
                    type: "NAVIGATION",
                    action: getAppUrl(PageTypes.UpgradeMembership, queryParams)
                })
            }
        }

        const meta: any = {
            // packId: CatalogueServiceUtilities.extractPackId(packInfo.productId),
            productId: packInfo.productId
        }
        return { manageOptions: manageOptions, meta: meta }
    }


    async getMealPackManageOptions(
        userContext: UserContext,
        menu: { [date: string]: string },
        packBooking: FoodPackBooking,
        issuesMap: Map<string, CustomerIssueType[]>,
        enableProductOptions: boolean,
        enableReportIssue: boolean, isWeb: boolean, packInfo: FoodPack):
        Promise<{ manageOptions: ManageOptions, meta: any }> {
        const manageOptions: ManageOptions = {
            displayText: "Manage",
            icon: "MANAGE",
            options: [],
        }
        const tz = userContext.userProfile.timezone
        const today: string = TimeUtil.todaysDateWithTimezone(tz)
        const days: string[] = TimeUtil.getDaysFrom(tz, today, 5)
        const tmrw: string = days[1]

        const isTodayAvailable: boolean = today in menu && !SlotUtil.isCancelCutOffPassed(today, tz, packBooking.packSlot.slotId)
        let nextAvailableDate: string = undefined

        if (isTodayAvailable) {
            nextAvailableDate = today
        } else {
            nextAvailableDate = Object.keys(menu).filter(val => val !== today).sort()[0]
        }

        const foodBooking: FoodBooking = this.getUpcomingShipment(packBooking, tz)
        let difference = 0
        if (foodBooking)
            difference = await this.getCancelDifference(foodBooking)
        const resumeText = packBooking.subscriptionType === undefined ? "Resume" : "Undo Cancel"
        let pauseText = packBooking.subscriptionType === undefined ? "Pause" : "Cancel upcoming meals"
        if (nextAvailableDate && foodBooking && packBooking.subscriptionType === undefined) {
            pauseText = TimeUtil.getDayText(nextAvailableDate, tz, {
                sameDay: "[" + pauseText + " from today]",
                nextDay: "[" + pauseText + " from tomorrow]",
                nextWeek: "[" + pauseText + " from] dddd"
            })
        }

        if (packBooking.packState === "PAUSED" || (packBooking.packState === "ACTIVE" && packBooking.pauseWindow && TimeUtil.getMomentNow(tz).isBefore(packBooking.pauseWindow.start))) {
            const startDate = TimeUtil.formatDateInTimeZone(tz, packBooking.pauseWindow.start, "Do MMM")
            const endDate = TimeUtil.formatDateInTimeZone(tz, packBooking.pauseWindow.end, "Do MMM")
            manageOptions.displayText = resumeText
            manageOptions.helperText = (packBooking.subscriptionType === undefined ? "Paused: " : "Cancelled: ") +
                (startDate !== endDate ? startDate + " - " + endDate : endDate)
            manageOptions.icon = "UNDO"
            if (packBooking.schedule.ticketsLeft > 0) {
                manageOptions.options.push({
                    isEnabled: enableProductOptions,
                    type: "RESUME_MEAL_PACK",
                    displayText: resumeText,
                })
            }
        } else if (packBooking.packState === "ACTIVE" && MealUtil.isSkipMealsAllowed(packInfo)) {
            const minDate = TimeUtil.getMomentForDateString(nextAvailableDate, tz)
            const maxDate = packBooking.subscriptionType === undefined ? max(momentTz.tz(packBooking.packEndDate, tz).subtract(packBooking.schedule.ticketsLeft, "days"), minDate) : momentTz.tz(new Date(), tz).add(packBooking.schedule.ticketsTotal, "days")
            manageOptions.displayText = pauseText
            manageOptions.icon = packBooking.subscriptionType === undefined ? "PAUSE" : "CANCEL"
            if (packBooking.schedule.ticketsLeft > 0) {
                manageOptions.options.push({
                    isEnabled: maxDate.isAfter(minDate) && enableProductOptions && packBooking.packState === "ACTIVE",
                    type: packBooking.subscriptionType === undefined ? "PAUSE_MEAL_PACK" : "SKIP_MEALS",
                    displayText: pauseText,
                    meta: {
                        nextAvailableDate: nextAvailableDate,
                        difference: difference,
                        defaultDate: minDate.format("YYYY-MM-DD"),
                        defaultStartDate: minDate.format("YYYY-MM-DD"),
                        deliveryDate: foodBooking ? foodBooking.deliveryDate : undefined,
                        maximumDate: maxDate.format("YYYY-MM-DD"),
                        minimumDate: minDate.format("YYYY-MM-DD")
                    }
                })
            }
        }
        if (AppUtil.isNewReportIssueSupported(userContext)) {
            if (enableReportIssue) {
                const issues = await this.issueBusiness.getMealSubscriptionIssues(packBooking.packState, packBooking.orderId, userContext)
                manageOptions.options.push(this.issueBusiness.toManageOptionPayload(issues, true))
            }
        } else {
            const issueList: { code: string, title: string, confirmation: string }[] = []
            issuesMap.get("EatPack").forEach(customerIssueType => {
                issueList.push({
                    code: customerIssueType.code,
                    title: customerIssueType.subject,
                    confirmation: customerIssueType.confirmation
                })
            })
            manageOptions.options.push({
                isEnabled: enableReportIssue,
                type: "REPORT_ISSUE",
                meta: issueList,
                action: SUPPORT_DEEP_LINK,
                displayText: "Need Help"
            })
        }

        if (MealUtil.isCancelSubscriptionAllowed(packInfo) && (packBooking.packState === "ACTIVE" || packBooking.packState === "PAUSED" || packBooking.packState === "PENDING") && packBooking.subscriptionType !== undefined) {
            const manageOption: ManageOptionPayload = {
                isEnabled: enableReportIssue,
                type: "NAVIGATION",
                action: `curefit://cancelsubscriptionpage?pageType=food&orderId=${packBooking.orderId}&fulfilmentId=${packBooking.fulfilmentId}&packId=${packBooking.packId}`,
                displayText: "Cancel Subscription"
            }
            if (EatSubscriptionUtil.isAddonPresentInPack(userContext, packBooking.subPackBookings)) { // array of packIds to be passed only when app is new and user has an addon pack along woth the main pack
                const attachIds: any[] = []
                for (const pack in packBooking.subPackBookings) {
                    if (packBooking.subPackBookings[pack].packState !== "CANCELLED") { // TO SHOW THE OPTION TO CANCEL THE ADDON PACK ONLY IF ITS NOT CANCELLED ALREADY
                        const attachDetails = await this.packService.getFoodPack(packBooking.subPackBookings[pack].packId, userContext.userProfile.userId, userContext.userProfile.areaId, packBooking.fulfilmentId)
                        attachIds.push({
                            packId: packBooking.subPackBookings[pack].packId,
                            title: attachDetails.packInfo.title,
                            subtitle: "Add on"
                        })
                    }
                }
                const mainPackInfo = await this.packService.getFoodPack(packBooking.packId, userContext.userProfile.userId, userContext.userProfile.areaId, packBooking.fulfilmentId)
                const packIdmeta = {
                    mainPackInfo: {
                        packId: packBooking.packId,
                        title: "All Packs",
                        subtitle: `${mainPackInfo.packInfo.title}` + ((packBooking.subPackBookings.length > 0) ? `+ ${packBooking.subPackBookings.length} Add-ons` : ""),
                    },
                    attachPacks: attachIds,
                    action: manageOption

                }
                if (!_.isEmpty(attachIds)) {
                    manageOptions.options.push({
                        isEnabled: enableReportIssue,
                        type: "SHOW_CANCEL_ADDON",
                        action: "SHOW_CANCEL_ADDON",
                        displayText: "Cancel Subscription",
                        meta: { packDetailMeta: packIdmeta, Title: "Cancel Pack/s" }
                    })
                } else {
                    manageOptions.options.push(manageOption)

                }
            } else {
                manageOptions.options.push(manageOption)
            }
        }
        const meta = {
            packId: packBooking.packId,
            fulfilmentId: packBooking.fulfilmentId
        }
        manageOptions.options = manageOptions.options.filter((option) => {
            return option.isEnabled
        })
        return { manageOptions: manageOptions, meta: meta }
    }

    getDiagnosticsManageOptions(bookingDetail: BookingDetail, activityActions: CallToAction[]): Action[] {
        const actions: Action[] = []
        if (!_.isEmpty(activityActions)) {
            activityActions.map(activityAction => {
                switch (activityAction) {
                    case "VIEW_MAP_DIAGNOSTICS":
                        actions.push({
                            actionType: "NAVIGATION",
                            title: "Navigate",
                            url: ActionUtil.viewMap(bookingDetail.diagnosticsTestOrderResponse[0].inCentreDiagnosticOrder.slot.diagnosticCentre.placeUrl)
                        })
                        break
                    case "RESCHEDULE_DIAGNOSTICS_INCENTRE":
                        actions.push({
                            title: "Reschedule",
                            actionType: "DIAGNOSTIC_TEST_RESCHEDULE",
                            meta: {
                                parentBookingId: bookingDetail.booking.id,
                                type: "DIAGNOSTICS",
                                patientId: bookingDetail.booking.patientId,
                                centerId: bookingDetail.diagnosticsTestOrderResponse[0].inCentreDiagnosticOrder.slot.diagnosticCentre.id,
                                centreCode: bookingDetail.diagnosticsTestOrderResponse[0].inCentreDiagnosticOrder.slot.diagnosticCentre.code,
                                productId: bookingDetail.diagnosticsTestOrderResponse[0].productCodes[0],
                                category: "IN_CENTRE_SLOT",
                                hasAtHome: !_.isEmpty(bookingDetail.diagnosticsTestOrderResponse[0]) && bookingDetail.diagnosticsTestOrderResponse[0].atHomeDiagnosticOrder ? true : undefined
                            }
                        })
                        break
                   case "RESCHEDULE_DIAGNOSTICS_HOME":
                        actions.push({
                            title: "Reschedule",
                            actionType: "DIAGNOSTIC_TEST_RESCHEDULE",
                            meta: {
                                parentBookingId: bookingDetail.booking.id,
                                patientId: bookingDetail.booking.patientId,
                                productId: bookingDetail.diagnosticsTestOrderResponse[0].productCodes[0],
                                type: "DIAGNOSTICS",
                                category: "AT_HOME_SLOT"
                            }
                        })
                        break
                    default:
                        this.logger.error(`getDiagnosticsManageOptions not handled for ${activityAction}`)
                }
            })
        }
        return actions

    }

    getDiagnosticsManageOptionsV2(activity: TimelineActivityV2): Action[] {
        const actions: Action[] = []
        if (activity && !_.isEmpty(activity.activityActions)) {
            activity.activityActions.map(activityAction => {
                switch (activityAction as string) {
                    case "NAVIGATE": {
                        actions.push(AppActionUtil.centerNavigationAction(activity.placeUrl))
                        break
                    }
                    case "RESCHEDULE": {
                        if (activity.isAtHome) {
                            if (activity.addressId) {
                                actions.push({
                                    title: "Reschedule",
                                    actionType: "NAVIGATION",
                                    url: `curefit://selectCareDateV1?patientId=${activity.patientId}&productId=${activity.productCode}&parentBookingId=${activity.bookingId}
                                        &type=DIAGNOSTICS&category=AT_HOME_SLOT&isReschedule=true&addressIdV1=${activity.addressId}`
                                })
                            } else {
                                actions.push({
                                    title: "Reschedule",
                                    actionType: "DIAGNOSTIC_TEST_RESCHEDULE",
                                    meta: {
                                        parentBookingId: activity.bookingId,
                                        patientId: activity.patientId,
                                        productId: activity.productCode,
                                        type: "DIAGNOSTICS",
                                        category: "AT_HOME_SLOT"
                                    }
                                })
                            }
                        } else {
                            actions.push({
                                title: "Reschedule",
                                actionType: "DIAGNOSTIC_TEST_RESCHEDULE",
                                meta: {
                                    parentBookingId: activity.bookingId,
                                    type: "DIAGNOSTICS",
                                    patientId: activity.patientId,
                                    centreCode: activity.centerCode,
                                    productId: activity.productCode,
                                    category: "IN_CENTRE_SLOT",
                                    hasAtHome: activity.hasAtHome ? true : undefined
                                }
                            })
                        }
                        break
                    }
                    case "CANCEL": {
                        actions.push({
                            actionType: "CANCEL_HCU_TEST",
                            title: "Cancel",
                            meta: {
                                tcBookingId: activity.bookingId
                            }
                        })
                        break
                    }
                    case "PHLEBO_CALLING":
                        if ((activity as any)?.phleboMobileNo) {
                            actions.push({
                                actionType: "PHONE_CALL_NAVIGATION",
                                icon: "PHONE_CALL",
                                title: "Call Associate",
                                meta: {
                                  phoneNumber: (activity as any)?.phleboMobileNo
                                }
                            })
                        }
                        break
                    default:
                        this.logger.error(`getDiagnosticsManageOptionsV2 not handled for ${activityAction}`)
                }
            })
        }
        return actions
    }

    private getIssueList(issuesMap: Map<string, CustomerIssueType[]>, category: IssueCategory): { code: string, title: string, confirmation: string }[] {
        const issueList: { code: string, title: string, confirmation: string }[] = []
        issuesMap.get(category).forEach(customerIssueType => {
            issueList.push({
                code: customerIssueType.code,
                title: customerIssueType.subject,
                confirmation: customerIssueType.confirmation
            })
        })
        const sortedIssueList = issueList.sort((a, b) => {
            return a.code < b.code ? -1 : 1
        })
        return sortedIssueList
    }

    getTeleconsultationManageOptions(userContext: UserContext, user: User, enableActions: boolean, enableReportIssue: boolean, issuesMap: Map<string, CustomerIssueType[]>, bookingDetail: BookingDetail, orderId?: string, rootBooking?: BookingDetail): Action[] {
        const actions: Action[] = []
        const meta = {
            "tcBookingId": bookingDetail.booking.id,
            "orderId": orderId,
            "centerId": bookingDetail.consultationOrderResponse.center.id,
            "productId": bookingDetail.booking.productCode,
            "code": "TELECONSULTATION03"
        }
        // const manageOptionsObj: ManageOptionPayload[] = []

        if (enableReportIssue) {
            let reportIssues: { code: string, title: string, confirmation: string }[] = []

            if (CareUtil.isComplteted(bookingDetail) || CareUtil.isPending(bookingDetail)) {
                reportIssues = this.getIssueList(issuesMap, "TeleconsultationSingleBooked")
            } else {
                reportIssues = this.getIssueList(issuesMap, "TeleconsultationSingle")
            }
            actions.push({
                title: "Need Help",
                icon: "REPORT_ISSUE",
                actionType: "REPORT_ISSUE",
                actionId: "REPORT_ISSUE",
                meta: reportIssues
            })
        }
        if (enableActions) {
            const partOfMp = CareUtil.isPartOfMP(bookingDetail)
            if (partOfMp && !_.isEmpty(rootBooking)) {
                actions.push({
                    title: "Manage",
                    icon: "CANCEL",
                    actionId: "CANCEL",
                    actionType: "NAVIGATION",
                    meta: {
                        url: `curefit://carefitmp?id=${rootBooking.booking.productCode}&subCategoryCode=MP&bookingId=${bookingDetail.booking.rootBookingId}`
                    }
                })
            }
            if (!partOfMp && CareUtil.getCancelEnabled(bookingDetail)) {
                actions.push({
                    title: "Cancel",
                    icon: "CANCEL",
                    actionId: "CANCEL",
                    actionType: "CANCEL_TC",
                    meta: meta
                })
            }
            if (!partOfMp && CareUtil.getRescheduleEnabled(bookingDetail)) {
                actions.push({
                    title: "Reschedule",
                    icon: "RESCHEDULE",
                    actionId: "RESCHEDULE",
                    actionType: "RESCHEDULE_TC",
                    meta: {
                        ...meta,
                        parentBookingId: bookingDetail.consultationOrderResponse.id,
                        patientId: bookingDetail.booking.patientId
                    }
                })
            }
            const chatAction: any = CareUtil.getChatMessageAction(
                userContext,
                _.get(bookingDetail.consultationOrderResponse, "appointmentActionsWithContext.chatActionWithContext", null),
                bookingDetail.consultationOrderResponse.patient.id,
                bookingDetail.consultationOrderResponse.doctor?.name,
                CareUtil.getChatChannel(bookingDetail),
                bookingDetail.consultationOrderResponse.doctor?.displayImage,
                bookingDetail.consultationOrderResponse.doctor?.qualification,
                null,
                bookingDetail.booking.id
            )
            if (!_.isEmpty(chatAction)) {
                chatAction.title = !_.isNil(chatAction.title) ? chatAction.title.charAt(0).toUpperCase() + chatAction.title.substr(1).toLowerCase() : chatAction.title
                actions.push(chatAction)
            }
            if (CareUtil.getVideoEnabled(bookingDetail)) {
                const doctor: Doctor = bookingDetail.consultationOrderResponse.doctor
                const action: Action = {
                    title: "Join Video Call",
                    icon: "JOIN_CALL",
                    actionId: "JOIN_CALL",
                    actionType: "TC_JOIN_CALL",
                    url: `curefit://videochat?pageFrom=today&appointmentId=${bookingDetail.consultationOrderResponse.id}&bookingId=${bookingDetail.booking.id}&identity=Patient-${bookingDetail.consultationOrderResponse.patient.id}&docName=${doctor.name}&docImage=${doctor.displayImage}&userImage=${user.profilePictureUrl}&doctorId=${doctor.id}&channel=${CareUtil.getVideoChannel(bookingDetail)}&chatChannel=${CareUtil.getChatChannel(bookingDetail)}&enableLog=${CareUtil.isVideoLogEnabled()}`
                }
                if (userContext.sessionInfo.appVersion >= SHOW_INSTRUCTION_MODAL_SUPPORTED_APP_VERSION) {
                    actions.push({
                        title: "Join Video Call",
                        icon: "JOIN_CALL",
                        actionId: "JOIN_CALL",
                        actionType: "SHOW_CARE_INSTRUCTION_MODAL",
                        meta: {
                            header: {
                                title: "Instructions"
                            },
                            instructions: CareUtil.getConsulationInstructions(bookingDetail, this.tcDetailsPageConfig),
                            action: { ...action, title: "CONTINUE" },
                            showBookLater: false
                        },
                    })
                } else {
                    actions.push(action)
                }
            }
        }
        /*const manageOptions: ManageOptions = {
            displayText: "...",
            options: manageOptionsObj,
        }*/
        actions.sort(
            function (a: Action, b: Action) {
                if (a.actionId && b.actionId) {
                    if (CareActionIdsByPriority.indexOf(a.actionId) > CareActionIdsByPriority.indexOf(b.actionId))
                        return 1
                    else if (CareActionIdsByPriority.indexOf(a.actionId) < CareActionIdsByPriority.indexOf(b.actionId))
                        return -1
                    else
                        return 0
                }
                return 0
            }
        )
        return actions
    }

    async getTeleconsultationManageOptionsV2(
        userContext: UserContext,
        user: User,
        activity: TimelineActivityV2,
        consultationInstruction: ConsultationInstructionResponse[],
        patientsList: Patient[],
        hamletBusiness?: HamletBusiness
    ): Promise<Action[]> {
        let actionsPromises: Promise<Action>[] = []
        const meta = {
            "tcBookingId": activity.bookingId,
            "orderId": activity.orderId,
            "productId": activity.productCode,
            "code": "TELECONSULTATION03"
        }
        if (activity && !_.isEmpty(activity.activityActions)) {
            actionsPromises = activity.activityActions.map(async activityAction => {
                switch (activityAction) {
                    case "NAVIGATE": {
                        return AppActionUtil.centerNavigationAction(activity.placeUrl)
                        break
                    }
                    case "RESCHEDULE": {
                        const action: Action = {
                            title: "Reschedule",
                            icon: "RESCHEDULE",
                            actionId: "RESCHEDULE",
                            actionType: "RESCHEDULE_TC",
                            meta: {
                                ...meta,
                                parentBookingId: activity.bookingId,
                                patientId: activity.patientId
                            }
                        }
                        return action
                    }
                    case "CANCEL": {
                        const action: Action = {
                            title: "Cancel",
                            icon: "CANCEL",
                            actionId: "CANCEL",
                            actionType: "CANCEL_TC",
                            meta: meta
                        }
                        return action
                    }
                    case "MESSAGE": {
                        const chatAction = CareUtil.getChatMessageActionWithoutContext(userContext,
                            activity.patientId,
                            activity.doctorName,
                            activity.twilioChatCommunicationModeName,
                            activity.doctorDisplayImage,
                            "",
                            activity.appointmentId,
                            activity.bookingId
                        )
                        if (!_.isEmpty(chatAction)) {
                            return chatAction
                        }
                    }
                    case "JOIN_CALL": {
                        let newAction: Action
                        const isSgtType = CareUtil.isLiveSGTDoctorType(activity.doctorType)
                        if (isSgtType && await AppUtil.isLiveSGTTwilioSupported(userContext, hamletBusiness) ) {
                            const isEnabled = CareUtil.getTwilioEnabled(activity.appointmentActionsWithContext)
                             newAction = {
                                title: "Join Video Call",
                                icon: "JOIN_CALL",
                                actionId: "JOIN_CALL",
                                actionType: "NAVIGATION",
                                 url:  `/sgtconverse?patientId=${activity.patientId}&tcBookingId=${activity.bookingId}&productId=${activity.productCode}`,
                                isEnabled: CareUtil.getTwilioEnabled(activity.appointmentActionsWithContext)
                            }
                            return newAction
                        } else if (activity.doctorType === "SUPPORT_GROUP") {
                            if (activity.zoomLink) {
                                const isActionDisabled = new Date().getTime() < activity.timestamp - 900000
                                        || activity.status === "COMPLETED" || activity.status === "CUSTOMER_MISSED"
                                newAction = {
                                    title: "Join Video Call",
                                    icon: "JOIN_CALL",
                                    url: activity.zoomLink,
                                    actionType: "EXTERNAL_DEEP_LINK",
                                    disabled: isActionDisabled,
                                    isEnabled: !isActionDisabled
                                }
                            }
                        } else {
                             newAction = {
                                title: "Join Video Call",
                                icon: "JOIN_CALL",
                                actionId: "JOIN_CALL",
                                actionType: "SHOW_CARE_INSTRUCTION_MODAL",
                                meta: {
                                    header: {
                                        title: "Instructions"
                                    },
                                    instructions: CareUtil.getInstructionsBasedOnDoctorType(activity.doctorType, true, this.tcDetailsPageConfig),
                                    instructionMap: consultationInstruction,
                                    action: {
                                        icon: "JOIN_CALL",
                                        actionId: "JOIN_CALL",
                                        actionType: "TC_JOIN_CALL",
                                        url: `curefit://videochat?pageFrom=today&appointmentId=${activity.appointmentId}&bookingId=${activity.bookingId}&identity=Patient-${activity.patientId}&docName=${activity.doctorName}&docImage=${activity.doctorDisplayImage}&userImage=${user.profilePictureUrl}&doctorId=${activity.doctorId}&channel=${activity.twilioVideoCommunicationModeName}&chatChannel=${activity.twilioChatCommunicationModeName}&enableLog=${CareUtil.isVideoLogEnabled()}`,
                                        title: "CONTINUE"
                                    },
                                    showBookLater: false
                                },
                            }
                        }
                        // To do remove it when launching couple therapist in web
                        if (!AppUtil.isWeb(userContext) && CareUtil.isCoupleTherapist(activity.doctorType) && _.isEmpty(activity.secondaryPatientIds)) {
                            return {
                                ...CareUtil.getPartnerPatientSelectionModalAction(activity.appointmentId, patientsList, newAction, "Join Call"),
                                title: "Join Video Call",
                                icon: "JOIN_CALL",
                                actionId: "JOIN_CALL"
                            } as Action
                        }
                        return newAction
                    }
                    case "JOIN_ZOOM_MEETING": {
                        return await this.careBusiness.getJoinZoomMeetingActionfromUrl(userContext, activity.zoomLink, activity.patientId, activity.doctorType, activity.bookingId)
                    }
                    default:
                        this.logger.error(`getTeleconsultationManageOptionsV2 not handled for ${activityAction}`)
                }
            })
        }
        return await Promise.all(actionsPromises)
    }

    getPrescriptionManageOptions(userContext: UserContext, enableActions: boolean, enableReportIssue: boolean, issuesMap: Map<string, CustomerIssueType[]>, bookingDetail: BookingDetail, orderId?: string): Action[] {
        const actions: Action[] = []
        const meta = {
            "tcBookingId": bookingDetail.booking.id,
            "orderId": orderId,
            "centerId": bookingDetail.consultationOrderResponse.center.id,
            "productId": bookingDetail.booking.productCode,
            "code": "TELECONSULTATION03"
        }
        // const manageOptionsObj: ManageOptionPayload[] = []

        if (enableReportIssue) {
            let reportIssues: { code: string, title: string, confirmation: string }[] = []

            if (CareUtil.isComplteted(bookingDetail) || CareUtil.isPending(bookingDetail)) {
                reportIssues = this.getIssueList(issuesMap, "TeleconsultationSingleBooked")
            } else {
                reportIssues = this.getIssueList(issuesMap, "TeleconsultationSingle")
            }
            actions.push({
                title: "Need Help",
                icon: "REPORT_ISSUE",
                actionType: "REPORT_ISSUE",
                meta: reportIssues
            })
        }
        actions.push({
            title: "See Report",
            icon: "SEE_REPORT",
            actionType: "NAVIGATION",
            url: `curefit://carefitPrescription?tcBookingId=${bookingDetail.booking.id}&productId=${bookingDetail.booking.productCode}`
        })
        const chatAction: any = CareUtil.getChatMessageAction(
            userContext,
            _.get(bookingDetail.consultationOrderResponse, "appointmentActionsWithContext.chatActionWithContext", null),
            bookingDetail.consultationOrderResponse.patient.id,
            bookingDetail.consultationOrderResponse.doctor?.name,
            CareUtil.getChatChannel(bookingDetail),
            bookingDetail.consultationOrderResponse.doctor?.displayImage,
            bookingDetail.consultationOrderResponse.doctor?.qualification,
            null,
            bookingDetail.booking.id
        )
        chatAction && actions.push(chatAction)
        return actions
    }

    private isAlreadyDelivered(foodBooking: FoodBooking): boolean {
        if (foodBooking) {
            if (foodBooking.state === "DELIVERED") {
                return true
            }
        }
        return false
    }

    private isAlreadyCancelled(foodBooking: FoodBooking): boolean {
        if (foodBooking) {
            if (foodBooking.state === "CANCELLED" || foodBooking.state === "REJECTED") {
                return true
            }
        }
        return false
    }

    private getCultPackIssueList(issuesMap: Map<string, CustomerIssueType[]>): { code: string, title: string, confirmation: string }[] {
        const issueList: { code: string, title: string, confirmation: string }[] = []
        issuesMap.get("CultPack").forEach(customerIssueType => {
            issueList.push({
                code: customerIssueType.code,
                title: customerIssueType.subject,
                confirmation: customerIssueType.confirmation
            })
        })
        return issueList
    }

    private getCultIssueList(issuesMap: Map<string, CustomerIssueType[]>, isUpcomingClass: boolean, membership: CultMembership): { code: string, title: string, confirmation: string }[] {
        const issueList: { code: string, title: string, confirmation: string }[] = []
        let issueKey: IssueCategory = undefined
        if (membership) {
            if (isUpcomingClass)
                issueKey = "CultPackSessionBooked"
            else
                issueKey = "CultPackSessionAttended"
        } else {
            if (isUpcomingClass)
                issueKey = "CultSingleSessionBooked"
            else
                issueKey = "CultPackSessionAttended"
        }
        issuesMap.get(issueKey).forEach(customerIssueType => {
            issueList.push({
                code: customerIssueType.code,
                title: customerIssueType.subject,
                confirmation: customerIssueType.confirmation
            })
        })
        return issueList
    }

    async getCancelManageOptions(foodBooking: FoodBooking,
        isPackOrder: boolean, isPackActive: boolean, isTodayScreen?: boolean, onDemand?: boolean, isCartPage?: boolean,
        timezone: Timezone = TimeUtil.IST_TIMEZONE): Promise<{ manageOptions: ManageOptions, meta: any }> {
        const cancelDisplayText = foodBooking.listingBrand === "WHOLE_FIT" ? "Cancel Order" : "Cancel Meal"
        const manageOptions: ManageOptions = {
            displayText: cancelDisplayText,
            icon: "CANCEL",
            options: []
        }
        const isAlreadyDelivered: boolean = this.isAlreadyDelivered(foodBooking)
        const isAlreadyCancelled: boolean = this.isAlreadyCancelled(foodBooking)
        const canBeCancelledDueToRain: boolean = _.isNil(foodBooking.canBeCancelled) ? false : foodBooking.canBeCancelled
        const isCancelCutoffPassed: boolean = SlotUtil.isCancelCutOffPassedForChannel(foodBooking.deliveryWindow, foodBooking.deliveryChannel, timezone)
        const isCutOffPassed: boolean = canBeCancelledDueToRain === true ? false : isCancelCutoffPassed
        const isEnabled: boolean = !isAlreadyCancelled && !isAlreadyDelivered && !isCutOffPassed
        // Showing undo only for the single meal bought part of pack
        let meta: any = {
            fulfilmentId: foodBooking.fulfilmentId,
            date: foodBooking.deliveryDate,
            packId: foodBooking.packId,
            productId: foodBooking.productId
        }
        if (isAlreadyCancelled && foodBooking.packId && !isCancelCutoffPassed) {
            manageOptions.displayText = "Undo cancel meal"
            manageOptions.icon = "UNDO"
            manageOptions.options.push({
                isEnabled: true,
                displayText: "Undo cancel",
                type: isCartPage ? "UNDO_CANCEL_CART" : "UNDO_CANCEL_MEAL"
            })
        } else if (isEnabled || !isTodayScreen) {
            const difference: number = await this.getCancelDifference(foodBooking)
            meta = {
                ...meta,
                difference: difference,
                deliveryDate: foodBooking.deliveryDate
            }
            manageOptions.displayText = cancelDisplayText
            manageOptions.icon = "CANCEL"
            manageOptions.options.push({
                isEnabled: isEnabled,
                displayText: cancelDisplayText,
                type: foodBooking.products.length > 1 ? "CANCEL_CART" : "CANCEL_MEAL",
                meta: meta
            })
        }
        return { manageOptions: manageOptions, meta: meta }
    }

    private getMealIssueList(issuesMap: Map<string, CustomerIssueType[]>, isAlreadyDelivered: boolean, isPackOrder: boolean): { code: string, title: string, confirmation: string }[] {
        const issueList: { code: string, title: string, confirmation: string }[] = []
        let issueKey: IssueCategory = undefined
        if (isPackOrder) {
            if (isAlreadyDelivered)
                issueKey = "EatPackMealDelivered"
            else
                issueKey = "EatPackMealNotDelivered"
        } else {
            if (isAlreadyDelivered)
                issueKey = "EatSingleMealDelivered"
            else
                issueKey = "EatSingleMealNotDelivered"
        }
        issuesMap.get(issueKey).forEach(customerIssueType => {
            issueList.push({
                code: customerIssueType.code,
                title: customerIssueType.subject,
                confirmation: customerIssueType.confirmation
            })
        })
        return issueList
    }

    public isValidAddOn(products: Product[], addOn: Product): boolean {

        // Check if meal category is present in list of products
        const category: ProductCategory = this.categoryService.getCategoryById(addOn.categoryId)

        if (!category || !category.mealCategoryIds) {
            return true
        } else {
            for (const product of products) {
                if (category.mealCategoryIds.indexOf(product.categoryId) !== -1) {
                    return true
                }
            }
        }

        return false
    }


}
export default ProductBusiness
