import { BaseWidget, FitClubSavings, IBaseWidget, IServiceInterfaces, isFitclubV2Supported } from "@curefit/vm-models"
import { FitClubDeliverySavings } from "@curefit/fitclub-models"
import { UserContext } from "@curefit/userinfo-common"
import { Action } from "@curefit/vm-models"

export class FitclubSavingsCalloutWidgetV2 extends BaseWidget {

    fitclubSavings: FitClubSavings[]
    navAction: Action
    progress: number
    total: number
    savingsDurationMonths: number
    savingMessage?: string
    isSingleOrder: boolean
    freeDeliverySavings: number
    mealSlot?: string
    orderDate?: string

    constructor(userContext: UserContext, fitCashSavings: number, freeDeliverySavings: number, pageFrom: string, isSingleOrder?: boolean, showSavingMessageOnly?: boolean, isPrePurchase?: boolean, mealSlot?: string, orderDate?: string) {
        super("FITCLUB_SAVINGS_CALLOUT_WIDGET")
        this.fitclubSavings = []
        this.isSingleOrder = isSingleOrder
        this.freeDeliverySavings = freeDeliverySavings
        this.orderDate = orderDate
        this.fitclubSavings.push({title: "FITCASH\nEARNED", savingType: "EARN_FITCASH", value: fitCashSavings})
        this.savingsDurationMonths = 6
        this.mealSlot = mealSlot
        if (showSavingMessageOnly) {
            if (isPrePurchase) {
                if (fitCashSavings) {
                    this.savingMessage = `Earn upto ${fitCashSavings} Fitcash`
                    if (freeDeliverySavings) {
                        this.savingMessage += ` and free delivery`
                    }
                } else if (freeDeliverySavings) {
                    this.savingMessage = `Save ₹${freeDeliverySavings} with free delivery`
                }
            } else {
                if (fitCashSavings) {
                    this.savingMessage = `${fitCashSavings} Fitcash`
                    if (freeDeliverySavings) {
                        this.savingMessage += ` and free delivery`
                    }
                    this.savingMessage += ` earned on this order`
                } else if (freeDeliverySavings) {
                    `₹${freeDeliverySavings} saved with free delivery on this order`
                }
            }
        }
        const isFitclubV2enabled: boolean = isFitclubV2Supported(userContext)
        this.navAction = {
            actionType: "NAVIGATION",
            title: "MEMBERSHIP",
            url: isFitclubV2enabled ? "curefit://fitclubsummarypage" : undefined
        }
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        return this
    }

}
