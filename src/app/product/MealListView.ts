import { MealSlot, FoodProduct as Product } from "@curefit/eat-common"
import { ProductPrice } from "@curefit/product-common"
import { UrlPathBuilder } from "@curefit/product-common"
import { TimeUtil } from "@curefit/util-common"
import { Header } from "../common/views/WidgetView"
import { OfferUtil, SeoUrlParams } from "@curefit/base-utils"
import { FoodSinglePriceOfferResponse } from "@curefit/offer-common"
import { UserContext } from "@curefit/userinfo-common"
import { ActionUtil } from "@curefit/base-utils"

export class MealView {
    constructor(product: Product, ordering: number, mealSlot: MealSlot, date: string, inventoryResult: { [campaignType: string]: { [packId: string]: { left: number, total: number } } }, singleOffersResponse: FoodSinglePriceOfferResponse, userContext: UserContext) {
        const offerAndPrice = OfferUtil.getSingleOfferAndPrice(product, date, singleOffersResponse, mealSlot)
        const availabilityResult = OfferUtil.getAvailabilityV1(product, inventoryResult)
        const seoParams: SeoUrlParams = {
            productName: product.title
        }
        this.title = product.title
        this.price = offerAndPrice.price
        this.productId = product.productId
        this.image = UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, "FOOD", "THUMBNAIL", product.imageVersion)
        this.action = ActionUtil.foodSingle(product.productId, date, mealSlot, true, undefined, userContext.sessionInfo.userAgent, seoParams)
        this.isAvailable = availabilityResult.left > 0 ? true : false
        this.isInventorySet = availabilityResult.total > 0 ? true : false
        this.calories = `${product.attributes.nutritionInfo.Calories["Total Calories"]} cal`
        this.ordering = ordering
    }
    public title: string
    public price: ProductPrice
    public image: string
    public productId: string
    public isAvailable: boolean
    public isInventorySet: boolean
    public action: string
    public calories: string
    public ordering: number
}

export class MealListView {
    public header: Header & { timeToClose?: number }
    public items: MealView[]
    constructor(date: string, items: MealView[], mealSlot: MealSlot, userContext: UserContext) {
        const tz = userContext.userProfile.timezone
        this.items = items
        const dayText = TimeUtil.getDayText(date, tz)
        if (!date) {
            this.header = {
                title: "No upcoming meals",
                subTitle: "No upcoming meals. Please check later"
            }
        } else if (TimeUtil.todaysDateWithTimezone(tz) === date) {
            this.header = {
                title: dayText,
                // subTitle: `Last orders at ${MealUtil.getCutOffTimeDisplayText(maxSlot.slotId)}`,
            }
        } else {
            this.header = {
                title: dayText,
                // subTitle: `Orders for today closed. You can still order for ${dayText}.`,
            }
        }
    }
}
