import { FoodProduct as Product } from "@curefit/eat-common"
import { CampaignOffer } from "@curefit/offer-common"
import { UrlPathBuilder } from "@curefit/product-common"
import {
    AccessoriesAction,
    CenterSelectionWidget,
    DescriptionWidget,
    InfoCard,
    ProductDetailPage,
    ProductListWidget,
    ProductSummaryWidget
} from "../common/views/WidgetView"
import * as _ from "lodash"
import { CultCenter } from "@curefit/cult-common"
import { OrderProduct } from "@curefit/order-common"

class FitnessAccessoriesDetailView extends ProductDetailPage {
    public pageActions: AccessoriesAction[] = []
    public actions: AccessoriesAction[] = []

    constructor(product: Product, center: CultCenter, offers: { [offerType: string]: CampaignOffer[] }) {
        super()
        let productCampaign: CampaignOffer
        Object.keys(offers).forEach(campaignType => {
            const campaignOffer = offers[campaignType]
            const campaign = campaignOffer[0].campaign
            if (campaign.productType === product.productType && campaign.productIds.indexOf(product.productId) >= 0) {
                if (campaign.offerItemType in ["ANY", "SINGLE"] && !campaign.autoApply) {
                    productCampaign = campaignOffer[0]
                }
            }
        })

        this.widgets.push(this.getProductSummaryWidget(product, productCampaign))
        if (productCampaign) {
            this.widgets.push(this.getCenterSelectionWidget(center))
            this.widgets.push(this.getDescriptionWidget(product))
            this.widgets.push(this.getProductFeaturesWidget(product))
            this.widgets.push(this.getOfferWidget())
        } else {
            this.widgets.push(this.getOfferWidget())
            this.widgets.push(this.getDescriptionWidget(product))
            this.widgets.push(this.getProductFeaturesWidget(product))
        }

        if (productCampaign) {
            const orderProduct: OrderProduct = {
                productId: product.productId,
                quantity: 1,
                option: {
                    offerId: productCampaign.offerId,
                    centerId: center.id.toString()
                }
            }
            const createOrderPayload: {
                orderProduct: OrderProduct
            } = {
                orderProduct: orderProduct
            }

            this.pageActions = [{
                productId: product.productId,
                offerId: productCampaign.offerId,
                title: "Buy now",
                url: `curefit://checkoutv1?payload=${JSON.stringify(createOrderPayload)}`,
                actionType: "NAVIGATION"
            }]
            this.actions = this.pageActions
        } else {
            this.pageActions = [{
                productId: product.productId,
                title: "Done",
                actionType: "POP_ACTION"
            }]
            this.actions = this.pageActions
        }
    }

    getProductSummaryWidget(product: Product, productCampaign: CampaignOffer): ProductSummaryWidget {
        const productSummary: ProductSummaryWidget = {
            widgetType: "PRODUCT_SUMMARY_WIDGET",
            title: product.title,
            subTitle: "",
            price: {
                mrp: product.price.mrp,
                listingPrice: productCampaign ? productCampaign.price : product.price.mrp,
                currency: product.price.currency
            },
            image: UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, product.productType, "HERO", product.imageVersion)
        }
        return productSummary
    }

    getOfferWidget(): DescriptionWidget {
        const offerDescription: InfoCard = {
            title: "Exclusive offer only for cult members",
            subTitle: "1. Members can collect Mi Band – HRX Edition between 18th & 30th September from the center you select while making the payment.\n2. Offer is only applicable to Cult active members.\n3. Only one Mi band can be purchased by a member."
        }
        return new DescriptionWidget([offerDescription])
    }
    getDescriptionWidget(product: Product): DescriptionWidget {
        const productDescription: InfoCard = {
            title: "About the band",
            subTitle: product.subTitle
        }
        return new DescriptionWidget([productDescription])
    }
    getCenterSelectionWidget(center: CultCenter): CenterSelectionWidget {
        const centerSelection: CenterSelectionWidget = {
            widgetType: "CENTER_SELECTION_WIDGET",
            title: "Select a center to pick your Mi band",
            canChangeCenter: true,
            preferredCenterId: center.id,
            preferredCenterName: center.name,
            action: {
                actionType: "NAVIGATION"
            }
        }
        return centerSelection
    }

    getProductFeaturesWidget(product: Product): ProductListWidget {

        const infoCards: InfoCard[] = _.map(product.attributes.features, (feature: string) => {
            return {
                subTitle: feature
            }
        })
        const productListWidget: ProductListWidget = {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "SMALL",
            hideSepratorLines: false,
            header: { title: "Product features" },
            items: infoCards
        }
        return productListWidget
    }

}

export default FitnessAccessoriesDetailView
