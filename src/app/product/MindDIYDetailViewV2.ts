import { ProductFeedbackWidget } from "../common/views/WidgetView"
import { ActivityType } from "@curefit/product-common"
import { FoodProduct as Product } from "@curefit/eat-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { Feedback } from "@curefit/feedback-common"
import FeedbackPageConfigV2Cache from "../ugc/FeedbackPageConfigV2Cache"
import ContentDetailViewV2 from "./ContentDetailViewV2"
import { DIYMeditationProduct, DIYUserMeditationPack } from "@curefit/diy-common"

class MindDIYDetailViewV2 extends ContentDetailViewV2 {
    private constructor(userAgent: UserAgent, appVersion: number, userPack: DIYUserMeditationPack, sessionId: string, sessionMap: { [productId: string]: Product }, feedback?: Feedback, feedbackPageConfigV2Cache?: FeedbackPageConfigV2Cache, feedbackQuestion?: string) {
        super(userAgent, userPack, sessionMap)
        const session = <DIYMeditationProduct>sessionMap[sessionId]
        this.widgets.push(this.getDiySummaryWidget(session))
        this.widgets.push(this.getSessionPlayWidget(userPack, sessionMap, sessionId))
        if (feedback && (feedback.rating === "NOT_RATED" || feedback.rating === "DISMISSED")) {
            // todo check product type
            this.widgets.push(new ProductFeedbackWidget(feedbackQuestion, feedback.feedbackId))
        }

        if (userPack.fulfilment && userPack.fulfilment.status === "SUBSCRIBED") {
            this.widgets.push(this.getPackProgressActionCard(userAgent, userPack))
        }
    }

    public static async getView(userAgent: UserAgent, appVersion: number, userPack: DIYUserMeditationPack, sessionId: string, sessionMap: { [productId: string]: Product }, feedback?: Feedback, feedbackPageConfigV2Cache?: FeedbackPageConfigV2Cache): Promise<MindDIYDetailViewV2> {
        const question: string = await feedbackPageConfigV2Cache.getQuestionV2(feedback)
        const view: MindDIYDetailViewV2 = new MindDIYDetailViewV2(userAgent, appVersion, userPack, sessionId, sessionMap, feedback, feedbackPageConfigV2Cache, question)
        return view
    }

    protected getPackScreenBaseURL(): string {
        return "curefit://mindpack"
    }

    protected getActivityScreenBaseURL(): string {
        return "curefit://mindsingles"
    }

    protected getActivityType(): ActivityType {
        return "DIY_MEDITATION"
    }
}

export default MindDIYDetailViewV2
