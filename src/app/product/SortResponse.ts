import { ISortOption } from "./ISortOption"
import { SortOrder } from "@curefit/mongo-utils"

class SortResponse {

    static _instance: SortResponse

    public static get instance() {
        if (this._instance == null) {
            this._instance = new SortResponse()
        }
        return this._instance
    }

    sortOptions: ISortOption[]
    sortOptionViews: { id: string, name: string }[]

    constructor() {
        const t = require("./SortOptions.json")
        this.sortOptions = t.sortOptions
        this.sortOptionViews = this.sortOptions.map(a => { return { id: a.id, name: a.text } })
    }

    public getSortOptions(selectedId: string) {
        if (selectedId == null) {
            selectedId = "POPULAR"
        }
        return this.sortOptionViews.map(sortOption => {
            return {
                id: sortOption.id,
                name: sortOption.name,
                selected: (sortOption.id === selectedId) ? true : false
            }
        })
    }

    public getSortField(id: string): string {
        if (id == null) {
            id = "POPULAR"
        }
        const sortOption = this.sortOptions.filter(a => a.id === id)[0]
        if (sortOption != null) {
            return sortOption.field
        }
    }

    public getSortOrder(id: string): SortOrder {
        if (id == null) {
            id = "POPULAR"
        }
        const sortOption = this.sortOptions.filter(a => a.id === id)[0]
        if (sortOption != null) {
            return sortOption.sortOrder
        }
    }
}

export default SortResponse
