import * as _ from "lodash"
import { inject, injectable } from "inversify"
import { ActionUtil, OfferUtil } from "@curefit/base-utils"
import {
    ProductTagType,
    CatalogueProduct,
    ProductOffer,
    ServiceabilityType,
    Annotation,
    ProductDescriptionItem,
    TextType
} from "@curefit/gear-common"
import { Logger, BASE_TYPES } from "@curefit/base"
import { RUPEE_SYMBOL } from "@curefit/finance-common"
import { Action } from "../common/views/WidgetView"
import { SessionData, LocationPreferenceData, UserContext, Session } from "@curefit/userinfo-common"
import { IGearService, GEARVAULT_CLIENT_TYPES, getGearRewardOffers, GearService } from "@curefit/gearvault-client"
import { TimeUtil, Timezone } from "@curefit/util-common"
import AppUtil from "../util/AppUtil"
import {
    FREE_DELIVERY,
    RETURN_POLICY_TEXT,
    CASH_ON_DELIVERY_NO_CONTACT,
    UNSERVICEABLE_LOCATION,
    COVID_RESTRICTIONS,
    NO_RETURN_EXCHANGE,
    GEAR_ANNOTATION_BORDER_COLOR,
    GEAR_ANNOTATION_COLOR,
    GEAR_ANNOTATION_BG_COLOR,
    getAllowedProductProperties,
    ONLY_EXCHANGE,
    ONLY_RETURN,
    NO_EXCHANGE,
    NO_RETURN,
    SLA_INSTALLATION_TEXT_WITHOUT_PINCODE,
    LOCATION_EMPTY_STRING,
} from "../gear/constants"
import { GearProductPricesResponse, OfferV2 } from "@curefit/offer-common"
import { EmiInterest } from "@curefit/payment-common"
import { GST_RATE_INSTANT_DISCOUNT } from "../util/OrderUtil"
import { ICultsportEmiOptions, ICultsportEmiOptionsResponse } from "./IGearProductViewBuilder"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import MixpanelEventService from "../cultsport/MixpanelEventService"
import { ISegmentService } from "@curefit/segmentation-client"

export interface GearProductView {
    product: CatalogueProduct
    actions: GearAction[]
    locationInfo?: LocationInfo
}

export interface GearProductIndexView {
    products: CatalogueProduct[]
}

export interface GearAction extends Action {
    enabled?: boolean
}

export interface GearProductViewV2 {
    presentation: ProductPresentation
    information: ProductInformation
    locationInfo?: LocationInfo
    actions: GearAction[]
}

export interface ProductPresentation {
    overview: ProductOverview
    features: ProductFeature
    related?: ProductInformation[]
    theme: ProductTheme
}

export interface ProductOverview {
    slides: Slide[]
}

export interface ProductPresentation {
    overview: ProductOverview
    features: ProductFeature
    related?: ProductInformation[]
    theme: ProductTheme
}

export interface Slide {
    type: SlideEnum
    url: string
    highlight?: ProductHighlight
}

export enum SlideEnum {
    VIDEO = "VIDEO",
    IMAGE = "IMAGE"
}

export interface ProductHighlight {
    title: string
    description: string
    icon?: string
}

export interface ProductFeature {
    slides: Slide[]
}

export interface IReturnPolicy {
    text: string
}

export interface ProductInformation {
    productId: number
    title?: string
    brand: string
    mrp?: string
    listingPrice?: string
    discountPercent?: number
    sizes?: ProductSize[]
    description?: string
    productDescriptionItems?: ProductDescriptionItem[]
    instructions?: string[]
    quantity?: number
    option?: {}
    sizeGuide?: GearSizeGuide
    appliedOffers?: ProductOffer[]
    returnPolicy?: IReturnPolicy
}

export interface ProductSize {
    title: string
    name: string
    stock: number
    sku: string
}


export interface GearSizeGuide {
    illustration: string
    chart: string
}

export interface ProductTheme {
    backgroundColor: string
}

export enum TextStyle {
    REGULAR = "regular",
    BOLD = "bold"
}

interface TextBuilder {
    text: string
    style: TextStyle
}

export interface LocationInfo {
    header: {
        title: string
        subTitle?: string
    },
    info: TextBuilder[][]
    locationPreference?: LocationPreferenceData
    ctaText?: string
}

const TAG_VARIANT_SIZE = "Size"
const DEFAULT_THEME_BACKGROUND_COLOR = "#000000"
const PRIMARY_IMAGE_TAG = "BOGO_IMAGE"

@injectable()
export default class GearProductViewBuilder {
    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(GEARVAULT_CLIENT_TYPES.GearService) private gearService: IGearService,
        @inject(CUREFIT_API_TYPES.MixpanelEventService) protected mixpanelEventService: MixpanelEventService
    ) {}


    public async buildCultsportProductEmiOptions(product: CatalogueProduct, emiOptions: EmiInterest[], productOffers: GearProductPricesResponse, cartOffers: OfferV2[]): Promise<ICultsportEmiOptionsResponse> {
        const productId =  product.id.toString()
        let offerItem

        const productOffer = productOffers.priceMap[productId]
         if (productOffer) {
            offerItem = {
                product: {
                    price: {
                        mrp: productOffer.product.price.mrp,
                        listingPrice: productOffer.product.price.sellingPrice,
                        currency: "INR"
                    }
                },
                offers: productOffer.offers,
                preBuzzOffers: []
            }
        }

        if (!offerItem) {
            return undefined
        }
        if (!_.isEmpty(cartOffers)) {
            offerItem?.offers?.push(...cartOffers)
        }
        const offersMap = OfferUtil.segregateNoCostEMIOffers(offerItem.offers)
        const emiOffers = offersMap?.get("NO_COST_EMI")
        if (!emiOffers.length) {
            return undefined
        }
        let maxEmiTenure = 12
        emiOffers.forEach((emiOffer) => {
            emiOffer.addons.forEach((addOn) => {
                if (addOn.config && addOn.config.maxEmiTenure) {
                    maxEmiTenure = addOn.config.maxEmiTenure
                }
            })
        })
        // const offerCalloutWidget: OfferCalloutCardWidget = {
        //     widgetType: "CLP_CALLOUT_WIDGET",
        //     icon: "SAVINGS",
        //     subTitle: "Zero effective interest: you get upfront discount equal to the interest charged by bank"
        // }
        // emiWidgets.push(offerCalloutWidget)
        const packAmount = Number(product.price)

        let minEmiInstallment: number = packAmount
        const emiData: ICultsportEmiOptions[] = []
        _.map(emiOptions, (emiOption) => {
            const emiValues: string[][] = []
            emiOption.interestRates
                .filter(interestRate => interestRate.duration <= maxEmiTenure)
                .forEach((interestRate) => {
                    const months = interestRate.duration
                    if (months) {
                        const monthlyAmount = Math.round(packAmount / months)
                        minEmiInstallment = Math.min(minEmiInstallment, monthlyAmount)
                        emiValues.push([`${RUPEE_SYMBOL}${monthlyAmount.toString()}`, months.toString(), "No Cost", `${RUPEE_SYMBOL}${packAmount.toString()}*`])
                    }
                })
            const cultsportEmiOption: ICultsportEmiOptions = {
                title: emiOption.bank,
                info: {
                    titles: ["Installment", "Duration(m)", "Interest", "Total Cost"],
                    values: emiValues
                },
                // tnc: emiOption.tnc,
                // tncTitle: "How it works",
                // disclaimer: "",//`*${GST_RATE_INSTANT_DISCOUNT * 100}% GST extra on interest/discount amount shown above`
            }
            emiData.push(cultsportEmiOption)
        })
        return {
            title: "No Cost EMI",
            subTitle: `Zero effective interest: No Cost EMI starting from ${RUPEE_SYMBOL}${minEmiInstallment}/mo`,
            emiData: emiData
        }
    }

    async buildProductDetailView(
        product: CatalogueProduct,
        session: Session,
        isInternalUser: boolean,
        timezone: Timezone,
        userContext: UserContext,
        selectedVariantSku: string,
        isPartOfTrainerAttire?: boolean
    ): Promise<GearProductView> {
        const {sessionData, isNotLoggedIn} = session
        const {annotations = []} = product
        const actions: GearAction[] = this._getProductActions(product, isNotLoggedIn)

        product.mrp = product.discount_percent ? product.mrp : undefined
        product.discount_percent = product.discount_percent || undefined
        const [primaryImages, otherImages] = _.partition(product.master.images, i => _.includes(i.tags, PRIMARY_IMAGE_TAG))
        product.images = _.concat(primaryImages, otherImages)
        product = {
            ...product,
            productDescriptionItems: this._getProductDescription(product),
            annotations: _.map(annotations, (annotation: string) => GearProductViewBuilder.getStyledAnnotation(annotation)),
            returnPolicy: {
                text: this.getProductReturnOrExchangeText(product)
            },
            description: this.getProductDescriptionInfo(userContext, product),
            master: {
                ...product?.master,
                description: this.getProductDescriptionInfo(userContext, product),
            } as CatalogueProduct["master"]
        }
        const locationInfo = product.variants.some(variant => variant.isOrderable) ? await this._getLocationInfo(sessionData, timezone, product, userContext, selectedVariantSku) : null
        product.technology_highlights = {features: []}
        delete product.total_on_hand
        delete product.master.total_on_hand
        product.variants = product.variants.map((variant) => {
            if (!isPartOfTrainerAttire && ["5610", "5611"].includes(product.id.toString())) {
                product.master = {
                    ...product.master,
                    in_stock: false
                } as any
                variant.isOrderable = false as any
                (variant as any).in_stock = false
            }
            delete variant.total_on_hand
            return variant
        })
        return {product, actions, locationInfo}
    }

    private getSlideEnumFromAssetType(assetType: string): SlideEnum {
        switch (assetType) {
            case "video/mp4":
                return SlideEnum.VIDEO
            case "image/png":
                return SlideEnum.IMAGE
            default:
                return SlideEnum.IMAGE
        }
    }

    private getProductReturnOrExchangeText(product: CatalogueProduct) {
        const {replaceable = false, returnable = false, return_window = 30, exchange_window = 30} = product
        if (replaceable && returnable) {
            return RETURN_POLICY_TEXT.replace("{DAYS}", "" + return_window)
        } else if (returnable) {
            return ONLY_RETURN.replace("{DAYS}", "" + return_window)
        } else if (replaceable) {
            return ONLY_EXCHANGE.replace("{DAYS}", "" + exchange_window)
        } else {
            return NO_RETURN_EXCHANGE
        }
    }

    static getStyledAnnotation = (text: string): Annotation => ({
        text,
        borderColor: GEAR_ANNOTATION_BORDER_COLOR,
        color: GEAR_ANNOTATION_COLOR,
        backgroundColor: GEAR_ANNOTATION_BG_COLOR
    })

    private _getProductActions(product: CatalogueProduct, isNotLoggedIn: boolean): GearAction[] {
        if (product.is_reward) {
            const rewardOffers = getGearRewardOffers()
            const isUserEligible = (_.intersection(rewardOffers, product.appliedOffers).length > 0) && Number(product.price) === 0
            this.logger.info(`Reward product info: id: ${product.id}, appliedOffers: ${product.appliedOffers}, rewardOffers: ${rewardOffers}, price: ${product.price}, isUserEligible: ${isUserEligible}`)
            const primaryAction: GearAction = isNotLoggedIn ? {
                title: "Login to claim reward",
                actionType: "SHOW_LOGIN_MODAL",
                url: "curefit://loginmodal",
                enabled: true
            } : {
                actionType: "ADD_TO_CART",
                title: isUserEligible ? "Get for Free" : "Sorry, you are not eligible for this reward",
                enabled: isUserEligible
            }
            return [primaryAction, ...(isUserEligible ? [{
                actionType: "GEAR_BUY_NOW",
                url: ActionUtil.gearCartCheckout(),
                title: "Buy Now",
                enabled: isUserEligible
            } as GearAction] : [])]
        }
        return [{
            actionType: "ADD_TO_CART",
            title: "Add to Cart",
            enabled: true
        }, {
            actionType: "GEAR_BUY_NOW",
            url: ActionUtil.gearCartCheckout(),
            title: "Buy Now",
            enabled: true
        }]
    }

    private _getProductDescription(product: CatalogueProduct): ProductDescriptionItem[] {
        const productDescriptionItems: ProductDescriptionItem[] = []

        if (_.some(product.tags, {tag_type: ProductTagType.SUITABILITY})) {
            productDescriptionItems.push({
                title: "Suitable For",
                body: _.map(_.filter(product.tags, {tag_type: ProductTagType.SUITABILITY}), "name"),
                bodyType: TextType.BULLETS
            })
        }

        if (_.some(product.tags, {tag_type: ProductTagType.FABRIC})) {
            productDescriptionItems.push({
                title: "Fabric",
                body: _.map(_.filter(product.tags, {tag_type: ProductTagType.FABRIC}), "name"),
                bodyType: TextType.BULLETS
            })
        }

        // Disabling due to duplicate content in desktop
        // if (!_.isEmpty(product.product_features)) {
        //     productDescriptionItems.push({
        //         title: "Material and Care",
        //         body: product.product_features,
        //         bodyType: TextType.BULLETS
        //     })
        // }

        const productSpecifications = _.filter(product.product_properties, p =>
            _.includes(getAllowedProductProperties(), p.name)
        )

        if (!_.isEmpty(productSpecifications)) {
            productDescriptionItems.push({
                title: "Specifications",
                body: productSpecifications.map(prop => `${prop.presentation || prop.name}: ${prop.value}`),
                bodyType: TextType.PLAIN
            })
        }

        return productDescriptionItems
    }

    private async _getLocationInfo(sessionData: SessionData, timezone: Timezone, product: CatalogueProduct, userContext: UserContext, selectedVariantSku?: string): Promise<LocationInfo> {
        const gearLocation = sessionData.gearLocationPreference
        const {replaceable = false, returnable = false, return_window = 30, exchange_window = 30} = product
        selectedVariantSku = selectedVariantSku || GearService.cfProductIdToGearSkuName(product?.variants?.find(variant => variant.isOrderable)?.sku || product?.variants?.[0]?.sku)
        let title, subTitle
        const returnPolicyInfo = returnable ? {
            text: ONLY_RETURN.replace("{DAYS}", "" + return_window),
            style: TextStyle.REGULAR
        } : {
            text: NO_RETURN,
            style: TextStyle.BOLD
        }

        const exchangePolicyInfo = replaceable ? {
            text: ONLY_EXCHANGE.replace("{DAYS}", "" + exchange_window),
            style: TextStyle.REGULAR
        } : {
            text: NO_EXCHANGE,
            style: TextStyle.BOLD
        }

        const info = []
        if (_.isNil(gearLocation) || _.isEmpty(gearLocation.pincode)) { /* no gear location preference present */
            title = ""
            info.push(
                [
                    {
                        text: FREE_DELIVERY,
                        style: TextStyle.REGULAR
                    }
                ],
                [
                    returnPolicyInfo
                ],
                [
                    exchangePolicyInfo
                ],
            )
            if (product.category && ["LARGE EQUIPMENT"].includes(product.category) && product.article_type && !["Single Speed Cycle", "Kids Cycle"].includes(product.article_type.toString())) {
                info.push([
                    {
                        text : SLA_INSTALLATION_TEXT_WITHOUT_PINCODE,
                        style: TextStyle.REGULAR
                    }
                ])
            }
            if (!AppUtil.isTataNeuWebApp(userContext) && !AppUtil.isCultSportTataNeuApp(userContext)) {
                info.push(
                    [
                        {
                            text: LOCATION_EMPTY_STRING,
                            style: TextStyle.REGULAR
                        }
                    ]
                )
            }
        } else {
            const serviceabilityInfo: any = await this.gearService.getOrderServiceability(gearLocation.pincode, selectedVariantSku, [ServiceabilityType.COD], undefined, userContext.sessionInfo.isUserLoggedIn ? userContext.userProfile.userId : undefined)
            this.mixpanelEventService.sendPDPServiceabilityEvents(userContext, {
                "produtId": product.id,
                "category": product.article_type,
                "superCategory": product.category,
                "articleType": product.article_type,
                "isFreeInstallation": serviceabilityInfo?.isFreeInstallation,
                "installationCharges": serviceabilityInfo?.installationCharges,
                "isInstallationRequired": serviceabilityInfo?.isInstallationRequired,
                "orderCutoffTime": serviceabilityInfo?.orderCutoffTime,
                "isSkuSddEligibleForPincode": serviceabilityInfo?.isSkuSddEligibleForPincode,
                "isSkuNddEligibleForPincode": serviceabilityInfo?.isSkuNddEligibleForPincode,
                "prepaid": serviceabilityInfo?.prepaid,
                "pincode": gearLocation.pincode,
                "source": "PDP",
                "cod": serviceabilityInfo?.cod,
                "installation_sla": serviceabilityInfo?.installation_sla,
                ...(serviceabilityInfo?.edd ? {
                    slaInMinutes: TimeUtil.diffInMinutes(userContext.userProfile.timezone,
                            TimeUtil.todaysDate(userContext.userProfile.timezone, "YYYY-MM-DD HH:mm:ss"),
                            TimeUtil.formatDateInTimeZone(timezone, new Date(serviceabilityInfo.edd), "YYYY-MM-DD HH:mm:ss")
                        )
                } : {})
            })
            const unparseableLocation = !gearLocation.placeName || gearLocation.placeName === "Location not found"
            title = `Deliver to ${unparseableLocation ? "" : gearLocation.placeName + " - "}${gearLocation.pincode}`

            if (gearLocation.addressId) { /* check if location picked is from an address */
                subTitle = gearLocation.placeAddress
            }

            const isCodAvailable = serviceabilityInfo && serviceabilityInfo.cod
            const isPrepaidAvailable = serviceabilityInfo && serviceabilityInfo.prepaid
            const slaInstallation = serviceabilityInfo && serviceabilityInfo.installation_sla
            let slaInstallationInfo
            if (slaInstallation && slaInstallation.length != 0 && parseInt(slaInstallation) > 0) {
                slaInstallationInfo = {
                    text: "Door-step installation by cultsport team in " + slaInstallation + (parseInt(slaInstallation) > 1 ? " days" : " day") + " after delivery",
                    style: TextStyle.REGULAR
                }
            }
            if (isPrepaidAvailable) {
                info.push(
                    [
                        {
                            text: "Free delivery by",
                            style: TextStyle.REGULAR
                        },
                        {
                            text: TimeUtil.formatDateInTimeZone(timezone, new Date(serviceabilityInfo.edd), "dddd, Do MMMM"),
                            style: TextStyle.BOLD
                        }
                    ],
                    [
                        returnPolicyInfo
                    ],
                    [
                        exchangePolicyInfo
                    ]
                )
                if (!AppUtil.isTataNeuWebApp(userContext) && !AppUtil.isCultSportTataNeuApp(userContext)) {
                    info.push(
                        [
                            {
                                text: isCodAvailable ? "Pay on delivery available" : CASH_ON_DELIVERY_NO_CONTACT,
                                style: isCodAvailable ? TextStyle.REGULAR : TextStyle.BOLD
                            }
                        ]
                    )
                }
                if (slaInstallationInfo) {
                    info.push([slaInstallationInfo])
                }
            } else {
                info.push(
                    [
                        {
                            text: UNSERVICEABLE_LOCATION,
                            style: TextStyle.REGULAR
                        }
                    ]
                )
            }
        }

        return {
            locationPreference: gearLocation,
            header: {
                title,
                subTitle
            },
            info: info,
            ctaText: gearLocation?.pincode?.length === 6 ? "CHANGE" : "CHECK"
        }
    }

    async buildProductDetailViewV2(product: CatalogueProduct, session: Session, isInternalUser: boolean,
                                   timezone: Timezone, userContext: UserContext): Promise<GearProductViewV2> {
        const {sessionData, isNotLoggedIn} = session
        const actions: GearAction[] = this._getProductActions(product, isNotLoggedIn)

        /* overview slide */
        const [primaryImages, otherImages] = _.partition(product.master.images, i => _.includes(i.tags, PRIMARY_IMAGE_TAG))
        // Bring primary images to the front
        const overviewSlides: Slide[] = _.concat(primaryImages, otherImages).map(image => {
            return {
                type: SlideEnum.IMAGE,
                url: image.product_url
            }
        })

        /* feature slides */
        const productFeatureSlides: Slide[] = _.map(_.filter(product.technology_highlights.features, feature => feature.assets.length > 0), feature => {
            return {
                type: this.getSlideEnumFromAssetType(feature.assets[0].type),
                url: feature.assets[0].url,
                highlight: {
                    title: feature.name,
                    description: feature.description,
                    icon: feature.images.icon
                }
            }
        })

        /* sizes available for the product */
        const variantSizes: ProductSize[] = product.variants
            .map(variant => {
                return variant.option_values
                    .filter(opt => opt.option_type_name === TAG_VARIANT_SIZE)
                    .map(opt => {
                        return {
                            title: opt.presentation,
                            name: opt.name,
                            stock: variant.total_on_hand,
                            sku: variant.sku
                        }
                    })
            })
            .reduce(function (pre, cur) {
                // The nested map function returns an array of ProductSize[][].
                // This reduce fxn flattens the array into ProductSize[]
                return pre.concat(cur)
            })

        /* get product background theme color */
        product.background_color = _.isEmpty(product.background_color)
            ? DEFAULT_THEME_BACKGROUND_COLOR
            : product.background_color

        /* product description widgets */
        const productDescriptionItems: ProductDescriptionItem[] = await this._getProductDescription(product)

        const presentation: ProductPresentation = {
            overview: {
                slides: overviewSlides
            },
            features: {
                slides: productFeatureSlides
            },
            theme: {
                backgroundColor: product.background_color
            }
        }

        const isGearBrandNameSupported = AppUtil.isGearBrandNameSupported(userContext)

        const information: ProductInformation = {
            productId: product.id,
            title: isGearBrandNameSupported || product.brand == null ? product.name : `${product.brand.toUpperCase()} ${product.name}`,
            brand: product.brand,
            mrp: product.discount_percent ? `${RUPEE_SYMBOL}${product.mrp}` : undefined,
            listingPrice: `${RUPEE_SYMBOL}${product.price}`,
            discountPercent: product.discount_percent || undefined,
            description: this.getProductDescriptionInfo(userContext, product),
            productDescriptionItems: productDescriptionItems,
            instructions: product.product_features,
            sizes: variantSizes,
            sizeGuide: {
                illustration: product.size_chart.measurements_illustration,
                chart: product.size_chart.size_table
            },
            appliedOffers: product.offers,
            returnPolicy: {
                text: this.getProductReturnOrExchangeText(product)
            }
        }

        const locationInfo = await this._getLocationInfo(sessionData, timezone, product, userContext)

        return {
            presentation,
            information,
            actions,
            locationInfo
        } as GearProductViewV2
    }

    private getProductDescriptionInfo(userContext: UserContext, product: CatalogueProduct): string {
        if (_.isEmpty(product.description)) {
            return undefined
        }
        if (AppUtil.isCultSportWebApp(userContext)) {
            if (product.description.includes("|||")) {
                return product.description.split("|||").map(item => {
                    item = item.trim()
                    if (item?.includes("||")) {
                        return item.split("||").map(text => text?.trim()).filter(Boolean).map(text => " • " + text + "\n").join("")
                    }
                    return item
                }).filter(Boolean).join("\n")
            } else if (product.description.includes("||")) {
                return product.description.split("||").map(text => text?.trim()).filter(Boolean).map(text => " • " + text + "\n").join("")
            }
        }
        return product.description
    }
}
