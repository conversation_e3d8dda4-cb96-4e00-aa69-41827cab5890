import { injectable, inject } from "inversify"
import { <PERSON>og<PERSON>, BASE_TYPES } from "@curefit/base"
import { IC<PERSON>KeyValue, REDIS_TYPES } from "@curefit/redis-utils"
import * as _ from "lodash"
import { TimeUtil } from "@curefit/util-common"
import { Action } from "@curefit/apps-common"
import { DUPLICATE_SUBMISSION_FORM_ID } from "./constants"

const ACTIVE_CFS_FORM_REDIS_KEY: string = "activeCfsForm"

export interface ICfsFormCache {
    getActiveCfsFormActionForUser(userId: string): Promise<Action>
    purgeActiveCfsFormActionForUser(userId: string, formId?: string): Promise<boolean>
}

/**
 * @description
 */
@injectable()
export class CfsFormCache implements ICfsFormCache {

    constructor(
        @inject(REDIS_TYPES.RedisDao) private redisDao: ICrudKeyValue,
        @inject(BASE_TYPES.ILogger) private logger: ILogger
    ) {
    }

    /**
     * @description Returns active CFS Form action for user if present
     */
    public async getActiveCfsFormActionForUser(userId: string): Promise<Action> {
        const cfsFormActionStr: string = await this.redisDao.read(this.getActiveCfsFormTokenKey(userId))
        if (!_.isString(cfsFormActionStr)) {
            return undefined
        }
        return this.parseCfsFormActionJsonString(cfsFormActionStr)
    }

    /**
     * @description Removes all entries from the activeCfsForm cache for a particular user
     */
    public async purgeActiveCfsFormActionForUser(userId: string, formId?: string): Promise<boolean> {
        this.logger.info(`Purging CFS Form Action in Redis for user Id: ${userId}`)
        if (!(_.isEmpty(formId) || formId === DUPLICATE_SUBMISSION_FORM_ID)) {
            const activeCfsFormAction = await this.getActiveCfsFormActionForUser(userId)
            if (_.isEmpty(activeCfsFormAction)) {
                return false
            }
            const metaFormId = _.get(activeCfsFormAction, "meta.formId")
            if (!_.isEmpty(metaFormId) && metaFormId != formId) {
                return false
            }
        }
        return this.redisDao.delete(
          this.getActiveCfsFormTokenKey(userId)
        )
    }

    private getActiveCfsFormTokenKey(userId: string): string {
        return `${ACTIVE_CFS_FORM_REDIS_KEY}:${userId}`
    }

    private parseCfsFormActionJsonString(cfsFormActionStr: string): Action {
        if (_.isEmpty(cfsFormActionStr)) return undefined
        const action: Action = <Action>JSON.parse(cfsFormActionStr)
        return action
    }
}