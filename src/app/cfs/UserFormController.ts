import * as express from "express"
import { Container, inject } from "inversify"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { UserForm, UserFormRequest, UserScreenDataResponse } from "@curefit/cfs-common"
import { Session, UserContext } from "@curefit/userinfo-common"
import { ICultServiceOld as ICultService, CULT_CLIENT_TYPES, } from "@curefit/cult-client"
import { ICFSClient as IFormService, EvaluateExpression, CFS_TYPES } from "@curefit/cfs-client"
import { IMaverickService, MAVERICK_TYPES } from "@curefit/maverick-client"
import AuthMiddleware from "../auth/AuthMiddleware"
import * as _ from "lodash"
import { AssessmentScoreView, ProductListWidget, WidgetView } from "../common/views/WidgetView"
import { SignedUrlResponse } from "@curefit/user-client"
import { ICultBusiness } from "../cult/CultBusiness"
import { pluralizeStringIfRequired, TimeUtil } from "@curefit/util-common"
import { ICrudKeyValue, IRedisDao, REDIS_TYPES } from "@curefit/redis-utils"
import { AppFeedbackCountKey, AppFeedbackKey, AppFeedbackSuccessCountKey } from "../feedback/Feedback"
import { IAppFeedback } from "../page/vm/services/AppFeedbackService"
import { CareTherapyQuestion } from "../util/CareUtil"
import { Tag } from "@curefit/feedback-common"
import IFeedbackBusiness from "../ugc/IFeedbackBusiness"
import AppUtil from "../util/AppUtil"
import {
    DUPLICATE_SUBMISSION_ERROR_MESSAGE,
    DUPLICATE_SUBMISSION_FORM_ID
} from "./constants"
import LiveUtil from "../util/LiveUtil"

enum EmotionalWelTitle {
    lowScore = "Low",
    lowModerateScore = "Low to moderate",
    goodModerateScore = "Moderate to Good",
    goodScore = "Good"
}

enum EmotionalWelColor {
    lowScore = "red",
    lowModerateScore = "blue",
    goodModerateScore = "green",
    goodScore = "green"
}

const ONE_WEEK_EXPIRY = 604800

export function controllerFactory(kernel: Container) {
    @controller("/userform", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class UserFormController {
        constructor(
            @inject(MAVERICK_TYPES.IMaverickService) private maverickService: IMaverickService,
            @inject(CFS_TYPES.ICFSClient) private formService: IFormService,
            @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
            @inject(CUREFIT_API_TYPES.CultBusiness) private cultBusiness: ICultBusiness,
            @inject(REDIS_TYPES.RedisDao) private feedbackCountCache: ICrudKeyValue,
            @inject(CUREFIT_API_TYPES.AppFeedbackRedisDao) private feedbackRedisDao: IRedisDao<AppFeedbackKey, UserFormRequest>,
            @inject(CUREFIT_API_TYPES.AppFeedbackService) private appFeedbackService: IAppFeedback,
            @inject(CUREFIT_API_TYPES.FeedbackBusiness) private feedbackBusiness: IFeedbackBusiness
        ) {
        }


        @httpGet("/:formId")
        async getUserForm(req: express.Request): Promise<any> {
            const formId: string = req.params.formId
            const session: Session = req.session
            const userId = session.userId
            const fillPreviousResponse = req.query.fillPreviousResponse === "true"
            const prefillFormId = req.query.prefillFormId

            let formDetails
            let userResponse, filledUserResponse, swapPrefillUserResponse
            try {
                formDetails = await this.formService.getFormPageResponse(formId)
                userResponse = await this.formService.getUserFormData(userId, formId, null, req.query)
                if (fillPreviousResponse) {
                    filledUserResponse = await this.formService.getLastCompletedUserForm(formId, userId)
                    if (!_.isEmpty(filledUserResponse)) {
                        // Check if user has previously submitted the form
                        userResponse.screenResponse = filledUserResponse.screenResponse
                        userResponse.screenDataResponse = filledUserResponse.screenDataResponse
                    } else if (!_.isEmpty(prefillFormId)) {
                        // If user has not filled the form check if they have filled the old form
                        swapPrefillUserResponse = await this.formService.getLastCompletedUserForm(prefillFormId, userId)
                        if (!_.isEmpty(swapPrefillUserResponse)) {
                            // Logic of prefilling
                            const curScreenDataResponse = LiveUtil.getPrefilledOnboardingFormScreenData(swapPrefillUserResponse)
                            userResponse.screenDataResponse = curScreenDataResponse
                        }
                    }
                }
            } catch (error) {
                if (DUPLICATE_SUBMISSION_ERROR_MESSAGE === error.message) {
                    formDetails = await this.formService.getFormPageResponse(DUPLICATE_SUBMISSION_FORM_ID)
                    userResponse = await this.formService.getUserFormData(userId, DUPLICATE_SUBMISSION_FORM_ID, null, req.query)
                } else {
                    throw error
                }
            }
            return {
                screenDetails: formDetails,
                userResponse: userResponse
            }
        }

        @httpGet("/fetch/:userId/:formId")
        async getUserFormForUser(req: express.Request): Promise<any> {
            const formId: string = req.params.formId
            const userId: string = req.params.userId
            const formDetails = this.formService.getFormPageResponse(formId)
            const userResponse = this.formService.getUserFormData(userId, formId, null, req.query)
            return {
                screenDetails: await formDetails,
                userResponse: await userResponse
            }
        }

        @httpGet("/mediaUploadUrl/:userFormId")
        async getMediaUploadUrl(request: express.Request): Promise<SignedUrlResponse> {
            const userFormId: string = request.params.userFormId
            const { fileName } = request.query
            return await this.formService.getUserFormDataMediaUploadUrl(fileName, userFormId)
        }
        @httpPost("/submitWithoutLogin/:userId/:userFormId")
        async submitWithoutLogin(req: express.Request): Promise<boolean> {
            const userFormId: string = req.params.userFormId
            const formResponse: UserFormRequest = req.body
            const userId = req.params.userId
            const response: boolean = await this.formService.submitUserFormData(userId, userFormId, formResponse)
            return response
        }
        @httpPost("/submit/:userFormId")
        async submitForm(req: express.Request): Promise<boolean> {
            const userFormId: string = req.params.userFormId
            const session: Session = req.session
            const userId = session.userId
            const formResponse: UserForm = req.body
            const response: boolean = await this.formService.submitUserFormData(userId, userFormId, formResponse)
            if (this.appFeedbackService.doesFormBelongToAnyUserResearchFeedback(formResponse.formId)) {
                if (formResponse.formCompletedStatus === "COMPLETED") {
                    this.feedbackCountCache.increment(AppFeedbackSuccessCountKey.get(formResponse.formId))
                }
                this.feedbackCountCache.increment(AppFeedbackCountKey.get(formResponse.formId))
                this.feedbackRedisDao.createWithExpiry(AppFeedbackKey.from(userId), formResponse, ONE_WEEK_EXPIRY)
            }
            if (formResponse?.meta?.feedbackId) {
                const {
                    feedbackId,
                    rating,
                    review,
                    selectedTags,
                } = this.getUserFormFeedBackData(formResponse)
                await this.feedbackBusiness.submitFeedBackV2(req.userContext, userId, feedbackId, undefined, rating, review, selectedTags, [], [], AppUtil.getTenantFromReq(req))
            }
            return response
        }

        @httpPost("/evaluate/condition/:userFormId")
        async executeCondition(req: express.Request): Promise<EvaluateExpression> {
            const userFormId: string = req.params.userFormId
            const session: Session = req.session
            const userId = session.userId
            const requestBody: any = req.body
            const evaluatorName: string = req.query.evaluatorName && !_.isNaN(req.query.evaluatorName) ? req.query.evaluatorName : ""
            return await this.formService.executeCondition(userId, userFormId, evaluatorName, requestBody)
        }


        @httpPost("/fetchScreenData/:serviceName")
        async fetchScreenData(req: express.Request): Promise<any> {
            const serviceName: string = req.params.serviceName
            const session: Session = req.session
            const userId = session.userId
            const requestBody: any = req.body
            const userContext = req.userContext as UserContext
            switch (serviceName) {
                case "MAVERICK": return await this.mavrickResopnse(requestBody)
                case "FORM_SERVICE": return await this.formServiceResopnse(userId, serviceName, requestBody)
                case "CULT_SERVICE": return await this.cultServiceResponse(requestBody, userContext, session)
            }
        }

        async formServiceResopnse(userId: string, serverFunctionName: string, requestBody: any): Promise<WidgetView> {
            switch (requestBody.resultType) {
                case "MetricAssessmentScoreOutput": {
                    let url = requestBody.url
                    const urlVariables: string[] = requestBody.urlVariables
                    urlVariables.map((urlVar: string) => {
                        switch (urlVar) {
                            case "USER_FORM_ID": url = url.replace("${" + urlVar + "}", requestBody.formId)
                            case "USER_ID": url = url.replace("${" + urlVar + "}", userId)
                        }
                    })
                    const response = await this.formService.postExecution(url, { userForm: requestBody.userForm, serverFunctionName: "METRIC_SUM" })
                    return this.getScoreView(response)
                }
                case "StringValueOutput": {
                    if (requestBody.url === "/livept") {
                        return this.getLivePTHowItWorksView()
                    } else if (["/highrisk", "/mediumrisk", "/lowrisk1", "/lowrisk2"].includes(requestBody.url)) {
                        return this.getCareCovidRiskView(requestBody.url)
                    } else if (["/lowScore", "/lowModerateScore", "/goodModerateScore", "/goodScore"].includes(requestBody.url)) {
                        return this.getEmotionalWellnessView(requestBody.url)
                    }
                }
            }
        }

        async cultServiceResponse(requestBody: any, userContext: UserContext, session: Session): Promise<any> {
            const url = requestBody.url
            if (url === "locality") {
                const userId: string = userContext.userProfile.userId
                const cityId: string = session.sessionData.cityId
                const cultCityId: number = await this.cultBusiness.getCultCityId(userContext, cityId, userId)
                const localities = await this.cultFitService.browseLocality(cultCityId, "CUREFIT_API", userContext.userProfile.subUserId || userId)
                const title = "Select One"
                const areaList = localities.map(locality => {
                    return {
                        ...locality,
                        displayText: locality.centersCount > 0 ? `${locality.name} (${locality.centersCount} ${pluralizeStringIfRequired("centre", locality.centersCount)})` : locality.name
                    }
                })
                return { title, areaList }
            } else {
                return null
            }
        }
        getScoreView(response: any): AssessmentScoreView {
            const resultRange = response.metric.rangeBuckets.find((range: any) => {
                return response.sum >= range.rangeMin && response.sum <= range.rangeMax
            })
            return {
                widgetType: "ASSESSMENT_SCORE_VIEW",
                gradientView: {
                    colors: [resultRange.meta.startColor, resultRange.meta.endColor],
                    title: response.metric.name,
                    total: response.total,
                    resultCount: response.score,
                    resultValue: resultRange.displayLabel
                },
                ranges: response.metric.rangeBuckets.map((rangeBucket: any) => {
                    return {
                        color: rangeBucket.meta.primaryColor,
                        name: rangeBucket.displayLabel
                    }
                })
            }
        }

        async mavrickResopnse(requestBody: any) {
            let url = requestBody.url
            const query = requestBody.query

            const inputSource = requestBody.userInputSources.find((source: any) => source.inputType === "KEYBOARD")
            url = url.replace("${" + inputSource.answerVariable + "}", query)
            const items = await this.maverickService.getItemsForUrl(url)
            return items.map((item: any) => {
                return {
                    possibleAnswerId: item[requestBody.possibleAnswerIdKey],
                    displayText: item[requestBody.displayTextKey],
                    groupName: "CENTRE"
                }
            })
        }

        private getLivePTHowItWorksView(): WidgetView {
            const items = [
                {
                    subTitle: `Choose to buy between a pack or a single session`,
                    icon: "/image/icons/ptAtHomeOnboarding/choose.png"
                },
                {
                    subTitle: `Flexibility to choose from the wide list of workouts available`,
                    icon: "/image/icons/ptAtHomeOnboarding/flexibility.png"
                },
                {
                    subTitle: "Enjoy personal attention at your convenient time from expert cult trainer",
                    icon: "/image/icons/ptAtHomeOnboarding/athome.png"
                },
                {
                    subTitle: "You need just a mobile to experience benefits from the comfort of your home",
                    icon: "/image/icons/ptAtHomeOnboarding/mobile.png"
                }
            ]
            const widget = new ProductListWidget(
                "SMALL",
                undefined,
                items
            )
            widget.noTopPadding = true
            widget.noBottomPadding = true
            return widget
        }

        private getCareCovidRiskView(url: string): any {
            let items: any[] = []
            switch (url) {
                case "/highrisk":
                    items = [
                        {
                            subTitle: `Do not panic. Consult a doctor immediately`,
                            icon: "/image/icons/howItWorks/consultTransparent2.png"
                        },
                        {
                            subTitle: `You should get tested for Covid-19. Call 1075 Toll Free Number for additional information`,
                            icon: "/image/icons/howItWorks/hospitalTransparent2.png"
                        },
                        {
                            subTitle: "Isolate Yourself immediately.",
                            icon: "/image/icons/howItWorks/takeCareTransparent2.png"
                        }
                    ]
                    break
                case "/mediumrisk":
                    items = [
                        {
                            subTitle: `Isolate yourself immediately. `,
                            icon: "/image/icons/howItWorks/takeCareTransparent2.png"
                        },
                        {
                            subTitle: `Consult a doctor if any symptoms develop`,
                            icon: "/image/icons/howItWorks/consultTransparent2.png"
                        },
                        {
                            subTitle: "Call 1075 Toll Free Number for additional information",
                            icon: "/image/icons/howItWorks/hospitalTransparent2.png"
                        }
                    ]
                    break
                case "/lowrisk1":
                    items = [
                        {
                            subTitle: `It might not be COVID-19, but consult a doctor to get your symptoms checked`,
                            icon: "/image/icons/howItWorks/consultTransparent2.png"
                        },
                        {
                            subTitle: `Practise social distancing and adequate care`,
                            icon: "/image/icons/howItWorks/takeCareTransparent2.png"
                        },
                        {
                            subTitle: "Visit www.mohfw.gov.in for latest information",
                            icon: "/image/icons/howItWorks/hospitalTransparent2.png"
                        }
                    ]
                    break
                default: items = [
                    {
                        subTitle: `Practise social distancing`,
                        icon: "/image/icons/howItWorks/takeCareTransparent2.png"
                    },
                    {
                        subTitle: `Consult a doctor if any symptoms develop`,
                        icon: "/image/icons/howItWorks/consultTransparent2.png"
                    },
                    {
                        subTitle: "Retake this assessment if you come in contact with a positive case",
                        icon: "/image/icons/howItWorks/hitButtonTransparent2.png"
                    },
                    {
                        subTitle: "Visit www.mohfw.gov.in for latest information",
                        icon: "/image/icons/howItWorks/hospitalTransparent2.png"
                    }
                ]
                    break
            }
            const widget = new ProductListWidget(
                "SMALL",
                undefined,
                items
            )
            widget.noTopPadding = true
            widget.noBottomPadding = true
            return widget
        }

        private getEmotionalWellnessView(url: string): any {
            const urlStr = url.slice(1)
            let widgetItems: any[] = []
            const items: any[] = [
                {
                    subTitle: "Recommendations from our experts on how you can improve your well-being"
                },
                {
                    title: `Exercise`,
                    subTitle: `Upto 150 minutes of exercise a week can have a positive impact on both physical and mental health`,
                    icon: "/image/icons/emotionalWellness/Workout1.png"
                },
                {
                    title: `Meditation`,
                    subTitle: `Meditation can help you focus, calm the mind, and sleep better`,
                    icon: "/image/icons/emotionalWellness/Meditation.png"
                },
                {
                    title: `Therapy`,
                    subTitle: `Sharing your experiences with a trusted therapist can begin the process of recovery from stress, anxiety, and more`,
                    icon: "/image/icons/emotionalWellness/Therapy.png"
                },
                {
                    title: `Sleep Health`,
                    subTitle: `Experts recommend 7-9 hours of sleep every night. Try disconnecting from all devices 1 hour before you sleep`,
                    icon: "/image/icons/emotionalWellness/Sleep.png"
                },
            ]

            if (urlStr == "lowScore") {
                widgetItems = [items[0], items[3], items[4], items[1], items[2]]
            } else if (urlStr == "lowModerateScore") {
                widgetItems = [items[0], items[2], items[3], items[1], items[4]]
            } else if (urlStr == "goodScore") {
                widgetItems = [items[0], items[1], items[2], items[4]]
            } else if (urlStr == "goodModerateScore") {
                widgetItems = [items[0], items[1], items[2], items[4],  items[3]]
            } else {
                widgetItems = items
            }

            const widget = new ProductListWidget(
                "SMALL",
                {
                    title: `${EmotionalWelTitle[urlStr as keyof typeof EmotionalWelTitle]}`,
                    color: `${EmotionalWelColor[urlStr as keyof typeof EmotionalWelColor]}`,
                    style: {
                        fontFamily: "BrandonText-Bold",
                        fontWeight: "bold",
                        fontSize: 25,
                    }
                },
                widgetItems
            )
            widget.noTopPadding = true
            widget.noBottomPadding = true
            return widget
        }

        private getAnswerProperty(userForm: UserForm, propertyName: string): Object {
            for (const key of _.keys(userForm.screenDataResponse)) {
                const screenDataResponse: UserScreenDataResponse = userForm.screenDataResponse[key]
                if ( !_.isEmpty(screenDataResponse) && !_.isEmpty(screenDataResponse.response)) {
                    if ( _.has(screenDataResponse.response, propertyName) ) {
                        return screenDataResponse.response[propertyName]
                    }
                }
            }
            return undefined
        }

        private getUserFormFeedBackData(userForm: UserForm) {
            const feedbackId = userForm.meta.feedbackId
            const rating = this.getAnswerPropertySafe(userForm, CareTherapyQuestion.Rating)?.possibleAnswerId
            const wentWell = this.getAnswerPropertySafe(userForm, CareTherapyQuestion.WhatWentWell)
            const canBeImproved = this.getAnswerPropertySafe(userForm, CareTherapyQuestion.WhatCanBeImproved)
            let review = this.getAnswerPropertySafe(userForm, CareTherapyQuestion.Feedback)

            const selectedTags: Tag[] = []
            if (!_.isEmpty(wentWell)) {
                wentWell?.forEach((item: any) => {
                    selectedTags.push({ text: item?.displayText || item?.possibleAnswerId })
                })
            } else if (!_.isEmpty(canBeImproved)) {
                canBeImproved?.forEach((item: any) => {
                    selectedTags.push({ text: item?.displayText || item?.possibleAnswerId })
                })
            }

            // Added a server level fix to avoid wrong type sent in the review
            if (review && typeof review !== "string") {
                review = undefined
            }

            return {
                feedbackId,
                rating: rating || "DISMISSED",
                review,
                selectedTags,
            }
        }

        private getAnswerPropertySafe(form: UserForm, propertyName: string): any {
            try {
                return this.getAnswerProperty(form, propertyName)
            } catch ( e ) {
                return null
            }
        }
    }
    return UserFormController
}

export default controllerFactory
