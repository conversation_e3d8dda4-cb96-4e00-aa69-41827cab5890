import { controller, httpGet } from "inversify-express-utils"
import { Container, inject } from "inversify"
import * as express from "express"
import { BASE_TYPES, Logger } from "@curefit/base"
import { EAT_API_CLIENT_TYPES, IEatApiService } from "@curefit/eat-api-client"

export function ProductFeedControllerFactory(kernel: Container) {
    @controller("/productFeed")
    class ProductFeedController {
        constructor(
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(EAT_API_CLIENT_TYPES.IEatApiService) public eatApiClientService: IEatApiService,
        ) {}

        @httpGet("/:appId/eat")
        async getEatProductList(request: express.Request) {
            const apiKey = request.headers["apikey"] as string
            const appId = request.params.appId
            return this.eatApiClientService.getEatProductListForCurrentMealSlot(appId, apiKey)
        }
    }
    return ProductFeedController

}

export default ProductFeedControllerFactory
