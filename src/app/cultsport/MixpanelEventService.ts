import { BASE_TYPES, CLSUtil, Logger } from "@curefit/base"
import { GearService } from "@curefit/gearvault-client"
import { HamletBusiness, HAMLET_TYPES } from "@curefit/hamlet-node-sdk"
import { ShipmentArrivalInfo } from "@curefit/oms-api-client"
import { CultsportMetadata, OrderStatusChange, GearProductSnapshots, Order, ProductAvailability } from "@curefit/order-common"
import { UserContext } from "@curefit/userinfo-common"
import { TimeUtil } from "@curefit/util-common"
import { inject, injectable } from "inversify"
import * as MixpanelSDK from "mixpanel"
import { Event } from "mixpanel"
import {
  GearCartLineItem,
  GearCartListWidget,
} from "../cart/GearCartViewBuilder"
import { PaymentOptionLegacyView } from "../payment/paymentModels"
import AppUtil from "../util/AppUtil"
import _ = require("lodash")
import { CLS_UTIL_REQ_HEADERS } from "../util/AuthUtil"
import CultsportUtil from "../util/CultsportUtil"

const mixPanel = MixpanelSDK.init("2dff75fbec437c4e3189a34a89f625a2", {
  host: "api-eu.mixpanel.com",
})

@injectable()
class MixpanelEventService {
  constructor(
    @inject(BASE_TYPES.ILogger) private logger: Logger,
    @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
    @inject(BASE_TYPES.ClsUtil) private clsUtil: CLSUtil
  ) {}

  async sendEvent(eventData: Event): Promise<any> {
    try {
      if (process.env.ENVIRONMENT === "PRODUCTION") {
        mixPanel.track(eventData?.event, eventData, undefined)
      }
    } catch (ex) {
      this.logger.error("Error while sending mixpanel events", ex)
    }
  }

  async sendEvents(events: Event[]): Promise<any> {
    try {
      if (process.env.ENVIRONMENT === "PRODUCTION") {
        mixPanel.track_batch(events)
      }
    } catch (ex) {
      this.logger.error("Error while sending mixpanel events", ex)
    }
  }

  private async  getDistinctIdFromUserContext(userContext: UserContext) {
    return userContext.sessionInfo.deviceId
    // const apiHeaders: any = JSON.parse(this.clsUtil.getNamespace().get(CLS_UTIL_REQ_HEADERS) ?? "{}")
    // return userContext.sessionInfo.isUserLoggedIn ? userContext.userProfile.userId : ( apiHeaders?.["mixpanel-distinct-id"] || userContext.sessionInfo.deviceId)
  }

  private async getExperimentDetails(userContext: UserContext) {
    try {
      const userExperimentDataMapObject =
        await this.hamletBusiness.getUserAllocations(
          AppUtil.getHamletContext(userContext, [])
        )
      const userExperimentList = _.map(
        _.keys(userExperimentDataMapObject.assignmentsMap),
        (experimentId) => {
          const userAssignment =
            userExperimentDataMapObject.assignmentsMap[experimentId]
          return `E:${userAssignment.experimentId}_${
            userAssignment.bucket && userAssignment.bucket.bucketId
          }_${userAssignment.versionIdentifier}`
        }
      )
      return userExperimentList.toString()
    } catch (ex) {
      this.logger.error("Error while fetch experiment for mixpanel events", ex)
    }
    return ""
  }

  private async getDefaultEventProperties(userContext: UserContext) {
    const user = await userContext.userPromise
    return {
      experiment: await this.getExperimentDetails(userContext),
      userId: userContext.userProfile.userId,
      isInternalUser: user?.isInternalUser,
      deviceId: userContext.sessionInfo.deviceId,
      webOrigin:
        userContext.sessionInfo.orderSource === "CULTSPORT_EMBED_APP"
          ? "CUREFIT_APP"
          : userContext.sessionInfo.orderSource,
      userAgent: userContext.sessionInfo.userAgent,
      isLoggedIn: userContext.sessionInfo.isUserLoggedIn,
      distinct_id: await this.getDistinctIdFromUserContext(userContext)
    }
  }

  async sendOrderSuccessEvents(userContext: UserContext, order: Order) {
    try {
      const events: Event[] = []
      const defaultProps = await this.getDefaultEventProperties(userContext)
      order.productSnapshots?.map(
        (productSnapshot: GearProductSnapshots, index: number) => {
          events.push({
            event: "GearPurchaseDoneItem_Backend",
            properties: {
              ...defaultProps,
              index,
              ...CultsportUtil.getAnalyticsDataForOrderItem(order, productSnapshot),
            }
          })
        }
      )
      events.push({
        event: "GearPurchaseDoneOrder_Backend",
        properties: {
          ...defaultProps,
          ...CultsportUtil.getAnalyticsDataForOrder(order)
        },
      })
      await this.sendEvents(events)
    } catch (ex) {
      this.logger.error("Error in Gear Mixpanel Event", ex)
    }
  }

  async sendCartReviewEvents(
    userContext: UserContext,
    order: Order,
    cartListWidgetPromise: Promise<GearCartListWidget>,
    arrivalInfo: ShipmentArrivalInfo,
    availabilitiesMap: _.Dictionary<ProductAvailability>
  ) {
    try {
      const events: Event[] = []
      const defaultProps = await this.getDefaultEventProperties(userContext)
      const cartListWidget = await cartListWidgetPromise
      cartListWidget?.orderItems?.forEach(
        (orderItem: GearCartLineItem, index: number) => {
          events.push({
            event: "CultsportCartReviewItem_Backend",
            properties: {
              ...defaultProps,
              index,
              productId: orderItem.productId,
              quantity: orderItem.quantity,
              sku: GearService.cfProductIdToGearSkuName(orderItem.productId),
              masterProductId: orderItem.masterProductId,
              productName: orderItem.productName,
              superCategory: orderItem.superCategory,
              category: orderItem.category,
              articleType: orderItem.category,
              brand: orderItem.productBrand,
              mrp: orderItem.price?.mrp,
              listingPrice: orderItem.price?.listingPrice,
              discountPercentage: Math.round(
                ((orderItem.price?.mrp - orderItem.price?.listingPrice) * 100) /
                  orderItem.price?.mrp
              ),
              offersApplied: order?.productSnapshots?.[
                index
              ]?.option?.offersInfo?.map((offer) => offer.offerId),
              slaDuration: orderItem?.productEdd
                ? TimeUtil.diffInMinutes(
                    userContext.userProfile.timezone,
                    TimeUtil.todaysDate(
                      userContext.userProfile.timezone,
                      "YYYY-MM-DD HH:mm:ss"
                    ),
                    TimeUtil.formatDateInTimeZone(
                      userContext.userProfile.timezone,
                      new Date(orderItem?.productEdd),
                      "YYYY-MM-DD HH:mm:ss"
                    )
                  )
                : undefined,
              isInStock: orderItem.in_stock,
              isServiceable: orderItem.is_serviceable,
            },
          })
        }
      )

      events.push({
        event: "CultsportCartReview_Backend",
        properties: {
          ...defaultProps,
          noOfItems: order.productSnapshots.length,
          totalPayable: order.totalPayable,
          totalAmountPayable: order.totalAmountPayable,
          totalFitCashPayable: order.totalFitCashPayable,
          offersApplied: order.offersApplied,
          totalAmountWithoutDiscount:
            order?.priceDetails?.total_without_discount,
          totalDiscount: order?.priceDetails?.discount,
          couponCode: order.couponCode,
          couponOfferId: (order.clientMetadata as CultsportMetadata)?.offerId,
          discountPercentage:
            order?.priceDetails?.total_without_discount >= 0
              ? Math.round(
                  ((order?.priceDetails?.total_without_discount -
                    order?.totalPayable) *
                    100) /
                    order?.priceDetails?.total_without_discount
                )
              : 0,
          productId: order.productSnapshots
            ?.map((productSnapshot) => productSnapshot.masterProductId)
            ?.join(","),
          productIds: order.productSnapshots?.map(
            (productSnapshot) => productSnapshot.masterProductId
          ),
          superCategory: order.productSnapshots
            ?.map((productSnapshot) => productSnapshot.gearCategory)
            ?.join(","),
          category: order.productSnapshots
            ?.map((productSnapshot) => productSnapshot.gearArticleType)
            ?.join(","),
          articleType: order.productSnapshots
            ?.map((productSnapshot) => productSnapshot.gearArticleType)
            ?.join(","),
          brand: order.productSnapshots
            ?.map((productSnapshot) => productSnapshot.gearBrandName)
            ?.join(","),
          sku: order.productSnapshots
            ?.map((productSnapshot) =>
              GearService.cfProductIdToGearSkuName(productSnapshot.productId)
            )
            ?.join(","),
        },
      })
      await this.sendEvents(events)
    } catch (ex) {
      this.logger.error("Error in Cart Review Mixpanel Event", ex)
    }
  }

  async sendCartCheckoutEvents(userContext: UserContext, order: Order) {
    try {
      const events: Event[] = []
      const defaultProps = await this.getDefaultEventProperties(userContext)
      order.productSnapshots?.map(
        (productSnapshot: GearProductSnapshots, index: number) => {
          events.push({
            event: "CultsportCartCheckoutItem_Backend",
            properties: {
              ...defaultProps,
              index,
              orderId: order.orderId,
              gearOrderId: order.gearOrderId,
              productId: productSnapshot.productId,
              sku: GearService.cfProductIdToGearSkuName(
                productSnapshot.productId
              ),
              quantity: productSnapshot.quantity,
              masterProductId: productSnapshot.masterProductId,
              productName: productSnapshot.title,
              superCategory: productSnapshot.gearCategory,
              category: productSnapshot.gearArticleType,
              articleType: productSnapshot.gearArticleType,
              brand: productSnapshot.gearBrandName,
              mrp: productSnapshot.price?.mrp,
              listingPrice: productSnapshot.price?.listingPrice,
              discountPercentage: Math.round(
                ((productSnapshot.price?.mrp -
                  productSnapshot.price?.listingPrice) *
                  100) /
                  productSnapshot.price?.mrp
              ),
              offersApplied: productSnapshot?.option?.offersInfo?.map(
                (offer) => offer.offerId
              ),
            },
          })
        }
      )
      events.push({
        event: "CultsportCartCheckout_Backend",
        properties: {
          ...defaultProps,
          orderId: order.orderId,
          gearOrderId: order.gearOrderId,
          noOfItems: order.productSnapshots.length,
          totalPayable: order.totalPayable,
          totalAmountPayable: order.totalAmountPayable,
          totalFitCashPayable: order.totalFitCashPayable,
          offersApplied: order.offersApplied,
          totalAmountWithoutDiscount:
            order?.priceDetails?.total_without_discount,
          totalDiscount: order?.priceDetails?.discount,
          paymentSource:
            order?.statusHistory?.[order?.statusHistory.length - 1]?.source,
          paymentStatus:
            order?.statusHistory?.[order?.statusHistory.length - 1]?.status,
          couponCode: order.couponCode,
          couponOfferId: (order.clientMetadata as CultsportMetadata)?.offerId,
          discountPercentage:
            order?.priceDetails?.total_without_discount >= 0
              ? Math.round(
                  ((order?.priceDetails?.total_without_discount -
                    order?.totalPayable) *
                    100) /
                    order?.priceDetails?.total_without_discount
                )
              : 0,
          productId: order.productSnapshots
            ?.map((productSnapshot) => productSnapshot.masterProductId)
            ?.join(","),
          productIds: order.productSnapshots?.map(
            (productSnapshot) => productSnapshot.masterProductId
          ),
          superCategory: order.productSnapshots
            ?.map((productSnapshot) => productSnapshot.gearCategory)
            ?.join(","),
          category: order.productSnapshots
            ?.map((productSnapshot) => productSnapshot.gearArticleType)
            ?.join(","),
          articleType: order.productSnapshots
            ?.map((productSnapshot) => productSnapshot.gearArticleType)
            ?.join(","),
          brand: order.productSnapshots
            ?.map((productSnapshot) => productSnapshot.gearBrandName)
            ?.join(","),
          sku: order.productSnapshots
            ?.map((productSnapshot) =>
              GearService.cfProductIdToGearSkuName(productSnapshot.productId)
            )
            ?.join(","),
        },
      })
      await this.sendEvents(events)
    } catch (ex) {
      this.logger.error("Error in Cart Checkout Mixpanel Event", ex)
    }
  }

  async sendPDPServiceabilityEvents(userContext: UserContext, eventProps: any) {
    try {
      const events: Event[] = []
      const defaultProps = await this.getDefaultEventProperties(userContext)
      events.push({
        event: "CultsportPDPServiceability_Backend",
        properties: {
          ...defaultProps,
          ...eventProps,
        },
      })
      await this.sendEvents(events)
    } catch (ex) {
      this.logger.error("Error in PDP Serviceability Mixpanel Event", ex)
    }
  }

  async sendCultsportPaymentOptionsEvents(
    userContext: UserContext,
    order: Order,
    result: PaymentOptionLegacyView
  ) {
    try {
      const events: Event[] = []
      const defaultProps = await this.getDefaultEventProperties(userContext)
      events.push({
        event: "CultsportPaymentOptions_Backend",
        properties: {
          ...defaultProps,
          ...result.paymentSummary,
          orderId: order.orderId,
          noOfItems: order.productSnapshots.length,
          totalPayable: order.totalPayable,
          totalAmountPayable: order.totalAmountPayable,
          totalFitCashPayable: order.totalFitCashPayable,
          offersApplied: order.offersApplied,
          totalAmountWithoutDiscount:
            order?.priceDetails?.total_without_discount,
          totalDiscount: order?.priceDetails?.discount,
          discountPercentage:
            order?.priceDetails?.total_without_discount >= 0
              ? Math.round(
                  ((order?.priceDetails?.total_without_discount -
                    order?.totalPayable) *
                    100) /
                    order?.priceDetails?.total_without_discount
                )
              : 0,
          couponCode: order.couponCode,
          couponOfferId: (order.clientMetadata as CultsportMetadata)?.offerId,
          productId: order.productSnapshots
            ?.map((productSnapshot) => productSnapshot.masterProductId)
            ?.join(","),
          productIds: order.productSnapshots?.map(
            (productSnapshot) => productSnapshot.masterProductId
          ),
          superCategory: order.productSnapshots
            ?.map((productSnapshot) => productSnapshot.gearCategory)
            ?.join(","),
          category: order.productSnapshots
            ?.map((productSnapshot) => productSnapshot.gearArticleType)
            ?.join(","),
          articleType: order.productSnapshots
            ?.map((productSnapshot) => productSnapshot.gearArticleType)
            ?.join(","),
          brand: order.productSnapshots
            ?.map((productSnapshot) => productSnapshot.gearBrandName)
            ?.join(","),
          sku: order.productSnapshots
            ?.map((productSnapshot) =>
              GearService.cfProductIdToGearSkuName(productSnapshot.productId)
            )
            ?.join(","),
          gatewayTypes: result.paymentOptions.map((paymentOption) =>
            paymentOption?.gatewayType === paymentOption?.type
              ? paymentOption?.gatewayType
              : paymentOption?.gatewayType + "_" + paymentOption?.type
          ),
        },
      })
      await this.sendEvents(events)
    } catch (ex) {
      this.logger.error("Error in Payment Options Mixpanel Event", ex)
    }
  }
}
export default MixpanelEventService
