import { Action } from "@curefit/vm-models"
import { IBaseWidget, UserContext } from "@curefit/vm-models"
import {
    CFLiveProduct,
    DIYRecipeIngredient,
    DIYRecipeProduct,
    DIYRecipeView,
    DIYRecipeStep,
    EatContentTile,
    OnDemandVideoCategory
} from "@curefit/diy-common"
import { Filter, SelectValue, SuggestionType } from "@curefit/eat-common"
import * as _ from "lodash"
import { RecipePackWidget } from "../page/vm/widgets/RecipePackWidget"
import { BrowseDoubleMagazineWidget } from "../page/vm/widgets/RecipeBrowseWidgetView"
import LiveUtil, { RECIPE_PRODUCT_TYPE } from "../util/LiveUtil"
import { IIngredientDetails } from "./RecipeIngredientsListWidget"
import { titleCase } from "@curefit/util-common"
import EatUtil from "../util/EatUtil"
import { ActionUtil, SeoUrlParams } from "@curefit/base-utils"
import AppUtil from "../util/AppUtil"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { PageTypes } from "@curefit/apps-common"
const clone = require("clone")


// INTERFACES

export interface IStructuredDataRecipe {
    name: string
    images: string[]
    datePublished: string
    description: string
    recipeCuisine: string
    prepTime: string
    cookTime?: string
    totalTime: string
    keywords: string
    serving: number
    category: string
    calories: number
    ingredients: any[]
    instructions: DIYRecipeStep[]
    video: any
    difficulty: any
}

// INTERFACES
export interface IRecipeAutoFill {
    displayText: string
    searchText: string
    action?: Action,
    meta?: {
        searchText: string
        type: SuggestionType,
        categoryId?: string
    }
    analyticsData: {
        eventName: string
        meta: {
            searchTerm: string
            suggestedTerm: string
            suggestionType: SuggestionType
            navigateTo: string
            recipeId?: string
            categoryId?: string
        }
    }
}

export interface IRecipeParams {
    pageNumber?: number
    vegOnly: boolean
    categoryId: string
    bookMarkedOnly: boolean
    pageSize?: number
    recipe: DIYRecipeView[]
    searchText?: string
    filters?: Filter[]
    customFilters?: Filter[]
}

export interface IRecipePagenatedResponse {
    title?: string
    body: IBaseWidget[]
    hasNextPage: boolean
}

export interface IDIYRecipe extends DIYRecipeProduct {
    id: string
}

// util functions

export async function createRecipeCards(item: DIYRecipeView, userContext: UserContext, isUserEligibleForLivePackTrial: boolean, isUserEligibleForMonetisation: boolean, bookmarkedOnly?: boolean): Promise<IBaseWidget> {

    const isRecipeBookmarked = bookmarkedOnly ? true : _.get(item, "userMeta.isSubscribed", false)
    const seoParams: SeoUrlParams = {
        productName: item.title
    }
    const navigationAction: Action = {
        actionType: "NAVIGATION",
        url: ActionUtil.recipeHomePage(item.id, seoParams, userContext.sessionInfo.userAgent)
    }
    let diyAction =  navigationAction
    // For web, no monetisation for recipes as it will hamper SEO
    if (!AppUtil.isWeb(userContext)) {
        diyAction = LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, item.locked, navigationAction, isUserEligibleForLivePackTrial, RECIPE_PRODUCT_TYPE, "recipe_pack_widget", "", false)
    }
    const cardPromise = new RecipePackWidget(item.imageDetails.thumbnailImage, item.title, item.isVeg, diyAction, item.preparationTime, item.id, isRecipeBookmarked, item.locked && isUserEligibleForMonetisation).buildView()
    return cardPromise
}

export const buildRecipeDoubleMagazinePromise  = (recipeItems: DIYRecipeView[], isUserEligibleForLivePackTrial: boolean, bookMarkedOnly: boolean = false, pageFrom: string, userContext: UserContext, isUserEligibleForMonetisation: boolean): Promise<IBaseWidget>[] => {
    const widgetPromise: Promise<IBaseWidget>[] = []
    for (let i = 0; i < recipeItems.length; i = i + 2) {
        const isRecipe1Bookmarked = bookMarkedOnly ? true : _.get(recipeItems[i], "userMeta.isSubscribed", false)
        const isLocked1 = recipeItems[i].locked && isUserEligibleForMonetisation
        const action1 = LiveUtil.getDIYRecipeAction(recipeItems[i], userContext.sessionInfo.userAgent)
        action1.url = pageFrom  ? `${action1.url}${ActionUtil.serializeAsQueryParams({pageFrom})}` : action1.url
        const diyAction1 = LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, isLocked1, action1, isUserEligibleForLivePackTrial, RECIPE_PRODUCT_TYPE, "recipe_pack_widget", "", false)
        const magazine1 = new RecipePackWidget(recipeItems[i].imageDetails.thumbnailImage, recipeItems[i].title, recipeItems[i].isVeg, diyAction1,
            recipeItems[i].preparationTime, recipeItems[i].id, isRecipe1Bookmarked, isLocked1)
        let magazine2
        const isRecipe2Bookmarked = bookMarkedOnly ? true :  _.get(recipeItems[i + 1], "userMeta.isSubscribed", false)
        if (recipeItems[i + 1] !== undefined) {
            const isLocked2 = recipeItems[i + 1].locked && isUserEligibleForMonetisation
            const action2 = LiveUtil.getDIYRecipeAction(recipeItems[i + 1], userContext.sessionInfo.userAgent)
            action2.url = pageFrom ? `${action2.url}${ActionUtil.serializeAsQueryParams({pageFrom})}` : action2.url
            const diyAction2 = LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, isLocked2, action2, isUserEligibleForLivePackTrial, RECIPE_PRODUCT_TYPE, "recipe_pack_widget", "", false)
            magazine2 = new RecipePackWidget(recipeItems[i + 1].imageDetails.thumbnailImage, recipeItems[i + 1].title, recipeItems[i + 1].isVeg, diyAction2,
                recipeItems[i + 1].preparationTime, recipeItems[i + 1].id, isRecipe2Bookmarked, isLocked2)
        }
        widgetPromise.push(new BrowseDoubleMagazineWidget(magazine1, magazine2, undefined, undefined, undefined).buildView())
    }

    return widgetPromise
}



export function getIngredientGridWidgetList(ingredientList: DIYRecipeIngredient[]): Map<string, IIngredientDetails[]> {
    const categoryMap = new Map<string, IIngredientDetails[]>()
    const CATEGORY_OTHER = "Other"
    for (let i = 0; i < ingredientList.length; i++) {
        const ingredient = ingredientList[i]
        if (ingredient.ingredientDetails.ingredientCategory === undefined) {
            ingredient.ingredientDetails.ingredientCategory = CATEGORY_OTHER
        }
        if (categoryMap.has(ingredient.ingredientDetails.ingredientCategory)) {
            categoryMap.get(ingredient.ingredientDetails.ingredientCategory).push({
                image: "/" + ingredient.ingredientDetails.imageUrl,
                title: titleCase(ingredient.ingredientDetails.name),
                quantity: ingredient.quantity,
                unit: EatUtil.getUnit(ingredient.quantity, ingredient.unit)
            })
        } else {
            const categoryIngredientList: IIngredientDetails[] = [{
                image: "/" + ingredient.ingredientDetails.imageUrl,
                title: titleCase(ingredient.ingredientDetails.name),
                quantity: ingredient.quantity,
                unit: EatUtil.getUnit(ingredient.quantity, ingredient.unit)
            }]
            categoryMap.set(ingredient.ingredientDetails.ingredientCategory === undefined || ingredient.ingredientDetails.ingredientCategory === CATEGORY_OTHER ? CATEGORY_OTHER : ingredient.ingredientDetails.ingredientCategory, categoryIngredientList)
        }
    }

    // sort category map to have others at the end
    const sortedMap = new Map<string, IIngredientDetails[]>()
    let othersValue: IIngredientDetails[]
    categoryMap.forEach((value: IIngredientDetails[], key: string) => {
        if (key === CATEGORY_OTHER) {
            othersValue = value
        } else {
            sortedMap.set(key, value)
        }
    })
    if (othersValue !== undefined) {
        sortedMap.set(CATEGORY_OTHER, othersValue)
    }
    return sortedMap
}

export const buildVideoCardPromise = async (videoItem: EatContentTile, userContext: UserContext, pageFrom?: string, isSearchResult?: boolean): Promise<IBaseWidget> => {

    let image: string = _.get(videoItem, "media.thumbnailImage")
    image = image.substr(1)
    // since the image from backend has a slash and the recipepack widget also adds a backslash
    const title = _.get(videoItem, "title")
    const isVeg: any = undefined
    const prepTime: any  = undefined
    const icon = "VIDEO_PLAY"
    const description = !_.isEmpty(_.get(videoItem, "meta.instructorInfo.name", undefined)) ?
        `By ${_.get(videoItem, "meta.instructorInfo.name")}` : title
    let url = `curefit://${PageTypes.LivePremiereDetailsPage}?id=${videoItem.id}&category=${videoItem.type === "EAT_LIVE_ON_DEMAND_VIDEO" ? "EAT" : "POP_LIVE"}` + (pageFrom ? `&pageFrom=${pageFrom}` : ``)
    url = isSearchResult ? url + "&isSearchResult=true" : url
    const action: Action = {
        actionType: "NAVIGATION",
        title: "VIEW DETAILS",
        url: url
    }

    const videoCardWidget = new RecipePackWidget(image, title, isVeg, action, prepTime, videoItem.id, undefined, undefined, description, true, title, icon)
    // since we dont support bookmark on videos
    videoCardWidget.bookmarkMeta = undefined

    return videoCardWidget.buildView()
}

export const getSearchVideoCardsPromise = (videoItems: EatContentTile[], userContext: UserContext, pageFrom?: string, isSearchResult?: boolean): Promise<IBaseWidget>[] => {
    const widgetPromise: Promise<IBaseWidget>[] = []
    videoItems.forEach(video => {
        widgetPromise.push(buildVideoCardPromise(video, userContext, pageFrom, isSearchResult))
    })
    return _.compact(widgetPromise)
}

export function getDIYRecipeViewFromEatContentTile(item: EatContentTile, interfaces: CFServiceInterfaces): DIYRecipeView {
    let result: DIYRecipeView
    try {
        result = {
            id: item.id,
            title: item.title,
            isVeg: item.meta.isVeg,
            imageDetails: { heroImage: item.media.heroImage, thumbnailImage: item.media.thumbnailImage},
            preparationTime: item.meta.preparationTime,
            userMeta: item.meta.userMeta,
            locked: item.meta.locked,
            namedId: item.namedId
        }
    } catch (e) {
        interfaces.rollbarService.sendError(e, {extra: {productId: item.id, title: item.title }})
    } finally {
        return result
    }
}

export function mergeFilters(filterSet1: Filter[] = [], filterSet2: Filter[] = []): Filter[] {
    const allFilters = _.concat(filterSet1, filterSet2)
    const toReturn: Filter[] = []
    const groupedFilters = _.groupBy(allFilters, filter => { return filter.key })
    _.forEach(_.keys(groupedFilters), filterKey => {
        const filtersWithSameKey: Filter[] = groupedFilters[filterKey]
        switch (filtersWithSameKey[0].type) {
            case "TOGGLE":
                toReturn.push(filtersWithSameKey[0])
                break
            case "SELECT":
            case "MULTISELECT":
                let values: SelectValue[] = []
                _.forEach(filtersWithSameKey, filter => {
                    values.push(...<SelectValue[]>filter.values)
                })
                values = _.uniqBy(values, value => { return value.key })
                const newFilter = clone(filtersWithSameKey[0])
                newFilter.values = values
                toReturn.push(newFilter)
            case "RANGE":
            // TODO Mohak implement range query
        }
    })
    return _.filter(toReturn, filter => !_.isNil(filter.values) && !_.isEmpty(filter.values))
}

export async function getVegFilter(vegOnly: boolean = false) {
    return {
        type: "VEG",
        meta: {
            isEnabled: vegOnly,
            title: "VEG",
            liveTab: "RECIPES"
        },
    }
}

export function getBreadCrumbsData(title: string, category: OnDemandVideoCategory, isInternationalUser: boolean): {
    title: string,
    pathname?: string
}[] {
    const isPopCategory = category === "POP_LIVE"

    const pagePathObj = { title, pathname: "" }
    const breadCrumbData = [
        {
            title: "Home",
            pathname: "/"
        }
    ]
    if (isPopCategory) {
        const talksPath = { title: "Talks", pathname: "/talks" }
        breadCrumbData.push(talksPath, pagePathObj)
    } else {
        if (isInternationalUser) {
            const categoryPathObj = { title: "Recipe", pathname: "/recipes" }
            breadCrumbData.push(categoryPathObj, pagePathObj)
            return breadCrumbData
        } else {
            const livePathObj = { title: "Live", pathname: "/live" }
            const categoryPathObj = { title: "Recipe", pathname: "/live/recipe" }
            breadCrumbData.push(livePathObj, categoryPathObj, pagePathObj)
        }
    }
    return breadCrumbData
}
