import * as _ from "lodash"
import { DescriptionWidget, <PERSON>er, ListWidget, ProductDetailPage } from "../common/views/WidgetView"
import { DIYRecipeIngredient, DIYRecipeProduct } from "@curefit/diy-common"
import { RecipeVideoWidget } from "./RecipeVideoWidget"
import { NutritionWidget } from "../common/views/NutritionWidget"
import { IIngredientDetails } from "./RecipeIngredientsListWidget"
import { ActionUtil } from "@curefit/base-utils"
import { RecipeStepsView } from "./RecipeStepsView"
import { TitleWidget } from "../page/PageWidgets"
import { RecipeIngredientsGridWidget } from "./RecipeIngredientsGridWidget"
import EatUtil from "../util/EatUtil"
import { titleCase } from "@curefit/util-common"
import { InfoCard, WidgetView } from "@curefit/apps-common"
import { UserContext } from "@curefit/userinfo-common"
import { getIngredientGridWidgetList } from "./RecipeCommon"

export class RecipeDetailView extends ProductDetailPage {

    constructor(recipe: DIYRecipeProduct, deeplink: string, autoPlayVideo: boolean) {
        super()
        if (_.isNil(recipe)) return

        // Recipe Video Widget
        const recipeVideoWidget: RecipeVideoWidget = new RecipeVideoWidget(recipe, deeplink, autoPlayVideo)
        if (recipeVideoWidget) {
            this.widgets.push(recipeVideoWidget)
        }

        // Recipe Nutrition Widget
        const recipeNutritionWidget: NutritionWidget = new NutritionWidget(recipe)
        if (recipeNutritionWidget) {
            this.widgets.push(recipeNutritionWidget)
        }

        const recipeIngredientsWidget = getIngredientListingWidget(recipe.ingredients)
        if (recipeIngredientsWidget) {
            this.widgets.push(recipeIngredientsWidget)
        }

        const stepsView: RecipeStepsView = new RecipeStepsView(recipe, deeplink)
        this.actions.push({
            title: "View step-by-step guide",
            actionType: "NAVIGATION",
            url: ActionUtil.recipeSteps(recipe.productId),
            meta: stepsView
        })
    }
}



export function getIngredientBulletInfoWidget(ingredients: DIYRecipeIngredient[]): any[] {
    const header: Header = {
        title: "Ingredients",
        titleProps: {
            style: {
                paddingLeft: 25,
                fontSize: 18
            }
        }
    }
    return [{
        widgetType: "PRODUCT_LIST_WIDGET",
        type: "BULLET",
        hideSepratorLines: true,
        header: header,
        items:  _.map(ingredients, (ingredient) => {
            return { subTitle: `${titleCase(ingredient.ingredientDetails.name)} ${ingredient.quantity} ${EatUtil.getUnit(ingredient.quantity, ingredient.unit)}`}
        }),
        noTopPadding: true
    }]
}

export function getIngredientListingWidget(ingredients: DIYRecipeIngredient[]) {
    const titleWidget: TitleWidget = {
        widgetType: "TITLE_WIDGET",
        title: "INGREDIENTS",
        titleFontWeight: "BOLD"
    }
    const categoryMap: Map<string, IIngredientDetails[]> = getIngredientGridWidgetList(ingredients)
    const gridWidgets: RecipeIngredientsGridWidget[] = []
    categoryMap.forEach(async (value: IIngredientDetails[], key: string) => {
        const recipeIngredientsWidget: RecipeIngredientsGridWidget = new RecipeIngredientsGridWidget(key, value)
        gridWidgets.push(recipeIngredientsWidget)
    })

    const recipeIngredientsWidget: ListWidget = {
        widgetType: "LIST_WIDGET",
        hideSepratorLines: true,
        headerWidget: titleWidget,
        widgets: gridWidgets
    }
    return recipeIngredientsWidget
}