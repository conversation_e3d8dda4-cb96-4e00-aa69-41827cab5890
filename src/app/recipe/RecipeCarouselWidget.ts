import { WidgetType, WidgetView } from "../common/views/WidgetView"
import { DIYRecipeProduct } from "@curefit/diy-common"
import { IIngredientDetails } from "./RecipeIngredientsListWidget"
import { UrlPathBuilder } from "@curefit/product-common"
import { ActionUtil } from "@curefit/base-utils"
import EatUtil from "../util/EatUtil"
import { titleCase } from "@curefit/util-common"
import { CONTENT_CDN_BASE_PATH } from "@curefit/util-common"

export interface IStepDetails {
    stepType: string
    image?: string
    title?: string
    description: string
    ingredients?: IIngredientDetails[]
    actions?: any
    videoUrl?: string
    shareUrl?: string
    feedback?: any
    meta?: any
}

export class RecipeCarouselWidget implements WidgetView {
    widgetType: WidgetType
    steps: IStepDetails[]

    constructor(recipe: DIYRecipeProduct, deeplink: string) {
        this.widgetType = "RECIPE_CAROUSEL_WIDGET"
        this.steps = []

        for (let i = 0; i < recipe.steps.length; i++) {
            const step = recipe.steps[i]
            const ingredients = []
            for (let i = 0; i < step.ingredients.length; i++) {
                const ingredient = step.ingredients[i]
                ingredients.push({
                    image: UrlPathBuilder.prefixSlash(ingredient.ingredientDetails.imageUrl),
                    title: titleCase(ingredient.ingredientDetails.name),
                    quantity: ingredient.quantity,
                    unit: EatUtil.getUnit(ingredient.quantity, ingredient.unit)
                })
            }
            this.steps.push({
                stepType: "COOKING_STEP",
                image: UrlPathBuilder.prefixSlash(step.imageUrl),
                description: step.text,
                ingredients: ingredients,
                actions: step.diyAction !== undefined ? [
                    step.diyAction
                ] : undefined
            })
        }

        this.steps.push({
            stepType: "FEEDBACK_STEP",
            title: "Success!",
            description: "We hope you had fun making it! Enjoy the meal. Share your thoughts.",
            videoUrl: UrlPathBuilder.getVideoAbsolutePath(recipe.completionVideo, recipe.contentFormat),
            actions: deeplink ? [
                {
                    actionType: "SHARE_ACTION",
                    title: "Share",
                    meta: {
                        isShareSingle: true,
                        videoUrl: UrlPathBuilder.getVideoAbsolutePath(recipe.contentId, recipe.contentFormat),
                        shareOptions: {
                            message: `I just prepared ${recipe.title} by following a step-by-step guide on the cult.fit app. Cooking at home got simpler! Explore 100s of healthy recipes on the cult.fit app. * ${deeplink} * `
                        }
                    }
                }
            ] : undefined,
            meta: {
                recipeId: recipe.productId
            }
        })
    }

}