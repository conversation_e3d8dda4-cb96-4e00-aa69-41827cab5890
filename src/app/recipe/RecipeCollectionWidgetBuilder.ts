import {
    Action,
    BaseWidget,
    EatliveRecipeWidget,
    IBaseWidget,
    IServiceInterfaces,
    UserContext
} from "@curefit/vm-models"
import { SortOrder } from "@curefit/mongo-utils"
import AppUtil from "../util/AppUtil"
import { DIYRecipeCollection, DIYRecipeCollectionResponse, DIYRecipeView } from "@curefit/diy-common"
import { UrlPathBuilder } from "@curefit/product-common"
import { RecipePackWidget } from "../page/vm/widgets/RecipePackWidget"
import * as _ from "lodash"
import { EatLiveRecipeWidgetBuilder } from "../page/vm/widgets/EatLiveRecipeWidgetBuilder"
import { EatliveRecipeCollectionWidget } from "@curefit/vm-models/dist/src/models/widgets/eatlive/EatliveRecipeCollectionWidget"
import { DataError } from "@curefit/error-client"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import LiveUtil from "../util/LiveUtil"
import { ActionUtil, SeoUrlParams } from "@curefit/base-utils"
import { NewRecipesWidget } from "../live/eatlive/EatLiveWidgets"
import { LivePackUtil } from "../util/LivePackUtil"

export class RecipeCollectionWidgetBuilder extends EatliveRecipeCollectionWidget {
    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {

        let bookmarkedOnly = String(_.get(queryParams, "filters.bookmarkedOnly", false)) === "true"
        let vegOnlyFilter = String(_.get(queryParams, "filters.vegOnly", false)) === "true"

        if (AppUtil.isWeb(userContext)) {
            bookmarkedOnly = String(_.get(queryParams, "bookmarkedOnly", false)) === "true"
            vegOnlyFilter = String(_.get(queryParams, "vegOnly", false)) === "true"
        }
        if (bookmarkedOnly) {
            // not showing this widget in case bookmark is enabled
            return undefined
        }

        if (_.isNil(this.productCollectionId)) {
            throw new DataError("recipe CollectionId not present")
        }
        const userId = userContext.userProfile.userId
        const countryId = AppUtil.getCountryId(userContext)
        const pageFrom = _.get(queryParams, "selectedTab") ? _.get(queryParams, "selectedTab") : _.get(queryParams, "pageId", undefined)
        const recipeCollection: DIYRecipeCollectionResponse = await interfaces.diyService.getRecipeCollectionbyId(vegOnlyFilter, userId, this.productCollectionId)
        const widgetCards: IBaseWidget[] = []

        if (_.isNil(recipeCollection) || _.isNull(recipeCollection)) {
            interfaces.rollbarService.sendError( new DataError(`recipe Collection not present for collectionId: ${this.productCollectionId}`) )
            return undefined
        }

        const subcollections: DIYRecipeCollection[] = _.get(recipeCollection, "subCollections", [])
        const recipes: DIYRecipeView[] = _.get(recipeCollection, "recipes", [])
        const widgetTitle = this.getTitle(_.get(recipeCollection, "title", undefined) )

        if (!_.isNil(subcollections) && !_.isEmpty(subcollections)) {
            const collectionCards = this.getRecipeCollectionCards(subcollections, userContext, vegOnlyFilter, pageFrom)
            widgetCards.push(... collectionCards)
        } else if (!_.isNil(recipes) && !_.isNull(recipes)) {
            const recipeCards = await this.getRecipeCard(recipes, userContext, interfaces)
            widgetCards.push(... recipeCards)
        } else {
            interfaces.rollbarService.sendError( new DataError(`recipe Collection has no subcollection/recipes for collectionId: ${this.productCollectionId}`) )
            return  undefined
        }

        const collectionWidget = new NewRecipesWidget()
        collectionWidget.title = widgetTitle
        collectionWidget.items = widgetCards
        collectionWidget.layoutProps = {
            height: 187,
            width: 150
        }


        return collectionWidget
    }

    async getRecipeCard(recipes: DIYRecipeView[], userContext: UserContext, interfaces: CFServiceInterfaces): Promise<IBaseWidget[]> {

        const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(interfaces.cultBusiness, interfaces.diyService, userContext)

        let cards: IBaseWidget[] = []

        for (const recipe of recipes) {
            // todo: Nisheet make it parallel
            // const liveTrialPack = await LiveUtil.getLiveTrialPackForUser(userContext, interfaces.diyService)
            const seoParams: SeoUrlParams = {
                productName: recipe.title
            }
            const action = {
                actionType: "NAVIGATION",
                url: ActionUtil.recipeHomePage(recipe.id, seoParams, userContext.sessionInfo.userAgent)
            }
            // todo: Nisheet fix the analytics source in diyaction
            const diyAction = LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, recipe.locked, action, isUserEligibleForTrial, "RECIPE", "eat_recipe_clp", "", false)
            const isBookMarked = _.get(recipe, "userMeta.isSubscribed", false) === true
            const mins = Math.floor(recipe.preparationTime / 60)
            const description = "Takes " + mins + " Mins"
            const widget = {
                widgetType: "RECIPE_PACK_WIDGET",
                image: UrlPathBuilder.prefixSlash(recipe.imageDetails.thumbnailImage),
                title: recipe.title,
                recipeId: recipe.id,
                isVeg: recipe.isVeg,
                action: diyAction,
                preparationTime: recipe.preparationTime,
                icon: recipe.isVeg ? "VEG" : "NON_VEG",
                bookmarkMeta: {
                    status: isBookMarked, // current bookmark status of the recipe
                    action: {
                        actionType: isBookMarked ? "UNBOOKMARK_RECIPE" : "BOOKMARK_RECIPE",
                        meta: {
                            recipeId: recipe.id,
                            isBookMarked: !isBookMarked, // tells the client what the user wants to do
                        }
                    }
                },
                description: description,
                analyticsData: {
                    eventName: "RECIPE_CLICK_EVENT",
                    meta: {
                        id: recipe.id,
                        title: recipe.title,
                        isVeg: recipe.isVeg
                    }
                }
            } as unknown as RecipePackWidget

            cards.push(widget)
        }
        cards = cards.filter(v => !_.isNil(v) && !_.isNull(v))

        return cards
    }

    getRecipeCollectionCards(subCollections: DIYRecipeCollection[], userContext: UserContext, vegOnlyFilter: boolean = false, pageFrom?: string): IBaseWidget[] {
        let cards: IBaseWidget[] = []
        subCollections.forEach(collection => {

            let recipesInCollection = _.get(collection, "recipeIds.length", undefined)
            if (vegOnlyFilter) {
                recipesInCollection = _.get(collection, "vegItemCount", undefined) || recipesInCollection
            }
            const isVeg = _.get(collection, "isVeg", undefined)
            const isDesktop = AppUtil.isDesktop(userContext)
            const userAgent = userContext.sessionInfo.userAgent

            const imageUrl = isDesktop ? _.get(collection, "imageDetails.web.thumbnailImage") : _.get(collection, "imageDetails.app.thumbnailImage")
            const seoParams: SeoUrlParams = {
                productName: collection.title
            }
            let url = ActionUtil.recipeCollectionPage(collection.productCollectionId, seoParams, "RECIPE", pageFrom, userAgent)
            url = vegOnlyFilter ? url + `&vegOnly=true` : url
            const widget = {
                widgetType: "RECIPE_PACK_WIDGET",
                image: UrlPathBuilder.prefixSlash(imageUrl),
                title: collection.title,
                recipeId: collection.productCollectionId,
                isVeg: collection.isVeg,
                action: {
                    actionType: "NAVIGATION",
                    url: url
                },
                preparationTime: undefined,
                description: `${recipesInCollection} Recipes`,
                icon: !_.isNil(isVeg) ? (isVeg ? "VEG" : "NON_VEG" ) : undefined,
                analyticsData: {
                    eventName: "RECIPE_COLLECTION_CLICK_EVENT",
                    meta: {
                        id: collection.productCollectionId,
                        title: collection.title,
                        isVeg: collection.isVeg
                    }
                }
            } as unknown as RecipePackWidget
            cards.push(widget)
        })
        cards = cards.filter(v => !_.isNil(v) && !_.isNull(v))
        return cards
    }

    getTitle(collectionTitle: string) {
        if (!_.isNil(this.title) && !_.isNull(this.title)) {
            return this.title
        } else if ( collectionTitle) {
            return collectionTitle
        } else {
            return "Recipe collections"
        }
    }
}