import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { UserContext } from "@curefit/userinfo-common"
import { SortOrder } from "@curefit/mongo-utils"
import { IBaseWidget } from "@curefit/vm-models"
import { DIYRecipeView } from "@curefit/diy-common"
import * as _ from "lodash"
import { injectable } from "inversify"
import { buildRecipeDoubleMagazinePromise, IRecipePagenatedResponse, IRecipeParams } from "./RecipeCommon"
import { Filter } from "@curefit/eat-common"
import LiveUtil from "../util/LiveUtil"
import AppUtil from "../util/AppUtil"
import { LivePackUtil } from "../util/LivePackUtil"

@injectable()
export class RecipeClpProductsViewBuilder {
    constructor() {
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filter: string]: any }, reqBody: IRecipeParams): Promise<IRecipePagenatedResponse> {
        const pageNumber = _.get(reqBody, "pageNumber", 1) // since we're using this call for page > 0 unless and until stated by client
        const categoryId = _.get(reqBody, "categoryId", "recipes_all")
        const vegOnly = String(_.get(reqBody, "vegOnly", false)) === "true"
        const bookMarkedOnly = String(_.get(reqBody, "bookMarkedOnly", false)) === "true"
        const pageSize = _.get(reqBody, "pageSize", 10)
        const recipe = _.get(reqBody, "recipe", []) as DIYRecipeView[] // if this is populated, no other param is honoured
        const userId = userContext.userProfile.userId
        const countryId = AppUtil.getCountryId(userContext)
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        let hasNextPage: boolean = false

        interfaces.logger.debug(`reqBody in recipeClpProductsViewBuilder: ${JSON.stringify(reqBody)}`)

        const start = pageNumber * pageSize
        /*
            The priority of fetching recipes:
            1. If the recipes are already passed
            2. bookmarked recipes
            3. from categoryId if present
            4. all recipes
         */
        let recipeItemsPromise: Promise<DIYRecipeView[]>
        if (!_.isNil(recipe) && !_.isEmpty(recipe)) {
            recipeItemsPromise = Promise.resolve(recipe)
        } else if (bookMarkedOnly) {
            recipeItemsPromise = interfaces.diyService.getAllSubscribedRecipesForUser(userId, tenant, start, pageSize)
        } else {
            // fetching one extra item to figure out the  next page  call
            if (categoryId === "recipes_all") {
                recipeItemsPromise = interfaces.diyService.getAllRecipes(vegOnly, tenant, start, pageSize, "productId", SortOrder.DESC, userId, countryId)
            } else {
                recipeItemsPromise = interfaces.diyService.getRecipesForCategory(categoryId, vegOnly, tenant, start, pageSize, "productId", SortOrder.DESC, userId, countryId)
            }
        }
        let recipeItems: DIYRecipeView[] = await recipeItemsPromise
        if (recipeItems.length >= pageSize) {
            recipeItems = recipeItems.splice(0, pageSize)
            hasNextPage = true
        }

        if (_.isEmpty(recipeItems)) {
            // this api call is being made for page = 1,2,3.... (not page 0) so there is no need to return the empty widget
            // since it would just mean end of page to client
            hasNextPage = false
        }
        const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(interfaces.cultBusiness, interfaces.diyService, userContext)
        const widgetPromise: Promise<IBaseWidget>[] = buildRecipeDoubleMagazinePromise(recipeItems, isUserEligibleForTrial, bookMarkedOnly, undefined, userContext, isUserEligibleForMonetisation)

        const widget = await Promise.all(widgetPromise)
        const body = widget.filter(v => !_.isNil(v))

        return {
            body,
            hasNextPage: hasNextPage
        }
    }
}