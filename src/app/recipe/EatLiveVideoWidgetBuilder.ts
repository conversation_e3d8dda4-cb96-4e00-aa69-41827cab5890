import {
    Action,
    BaseWidget,
    CardVideoFooter, CardVideoItem,
    CardVideoWidget,
    IBaseWidget,
    IServiceInterfaces,
    UserContext
} from "@curefit/vm-models"
import { CdnUtil, DateUtil } from "@curefit/util-common"
import { DIYRecipeCustomizedResponse, DIYRecipeProduct, DIYRecipeView } from "@curefit/diy-common"
import { IEatLiveWidgetDataRequest } from "../util/VMUtil"
import * as _ from "lodash"
import LiveUtil, { RECIPE_PRODUCT_TYPE } from "../util/LiveUtil"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { ActionUtil, SeoUrlParams } from "@curefit/base-utils"
import { LivePackUtil } from "../util/LivePackUtil"
import AppUtil from "../util/AppUtil"

export class EatLiveVideoWidgetBuilder extends BaseWidget {
    header: string
    productType: string
    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget | IBaseWidget[]> {
        const req: IEatLiveWidgetDataRequest = {
            newRecipesRequired: false,
            recipeoftheDayRequired: true,
            trendingRecipesRequired: false,
            date: DateUtil.todaysDate("YYYY-MM-DD", userContext.userProfile.timezone),
            userId: userContext.userProfile.userId,
            vegOnly: false,
            location: AppUtil.getCountryId(userContext),
            tenant: AppUtil.getTenantFromUserContext(userContext)

        }
        const clpDataResponse: DIYRecipeCustomizedResponse = await userContext.userProfile.promiseMapCache.getPromise("eatlive-clp-data", req)
        const recipes = clpDataResponse.recipesOfTheDay
        if (_.isNil(recipes) || _.isEmpty(recipes)) {
            interfaces.logger.error("Empty [] for recipe of the day")
            return undefined
        }
        const startRecipe: DIYRecipeView = recipes[0]
        const seoParams: SeoUrlParams = {
            productName: startRecipe.title
        }
        const footerAction: Action = {
            actionType: "NAVIGATION",
            url: ActionUtil.recipeHomePage(startRecipe.id, seoParams, userContext.sessionInfo.userAgent)
        }
        const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(interfaces.cultBusiness, interfaces.diyService, userContext)
        const isLocked = startRecipe.locked
        const diyAction = LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, isLocked, footerAction, isUserEligibleForTrial, RECIPE_PRODUCT_TYPE, "eat_live_clp", "", false)
        const footer: CardVideoFooter = {
            title: startRecipe.title,
            subTitle: undefined,
            rightText: "VIEW STEPS",
            action: diyAction
        }

        const startRecipeProduct: DIYRecipeProduct = await interfaces.diyService.getRecipeById(startRecipe.id, AppUtil.getTenantFromUserContext(userContext))

        const item: CardVideoItem = {
            header: "Recipe of the day",
            image: startRecipe.imageDetails.thumbnailImage,
            videoUri: encodeURIComponent("https://cdn-media.cure.fit/video/" + startRecipeProduct.contentId + "." + (startRecipeProduct.contentFormat || "mp4")),
            footer: footer,
            thumbnailVideoUri: undefined
        }
        const widget = new CardVideoWidget(item)
        widget.displayPlayIcon = true
        widget.action = this.getAction(startRecipeProduct)


        return widget
    }

    getAction(startRecipeProduct: DIYRecipeProduct): Action {
        let url = `curefit://videoplayer?videoUrl=${encodeURIComponent(`https://cdn-media.cure.fit/video/` + startRecipeProduct.contentId + "." + (startRecipeProduct.contentFormat || "mp4"))}&absoluteVideoUrl=${encodeURIComponent(`https://cdn-media.cure.fit/video/` + startRecipeProduct.contentId + "." + (startRecipeProduct.contentFormat || "mp4"))}`
        url = url + `&activityType=DIY_RECIPE`
        url = url + `&consumptionRequired=true`
        url = url + `&contentId=${startRecipeProduct.productId}`
        url = url + `&activityId=${startRecipeProduct.productId}`
        return {
            actionType: "NAVIGATION",
            url: url
        }
    }
}
