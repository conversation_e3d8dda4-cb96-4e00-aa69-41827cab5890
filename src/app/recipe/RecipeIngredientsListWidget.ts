import { Action, WidgetType, WidgetView } from "../common/views/WidgetView"
import { DIYRecipeProduct } from "@curefit/diy-common"
import { ActionUtil } from "@curefit/base-utils"
import { RecipeIngredientsView } from "./RecipeIngredientsView"
import EatUtil from "../util/EatUtil"

export interface IRecipeIngredientsWidget extends WidgetView {
    title: string
    ingredients: IIngredientDetails[]
    action?: Action
}

export interface IIngredientDetails {
    image: string
    title: string
    quantity: number
    unit: string
}

export class RecipeIngredientsListWidget implements IRecipeIngredientsWidget {
    widgetType: WidgetType
    title: string
    ingredients: IIngredientDetails[]
    action: Action

    constructor(recipe: DIYRecipeProduct) {
        this.widgetType = "RECIPE_INGREDIENTS_LIST_WIDGET"
        this.ingredients = []
        this.title = "Ingredients"
        const ingredientsView: RecipeIngredientsView = new RecipeIngredientsView(recipe)
        this.action = {
            actionType: "NAVIGATION",
            url: ActionUtil.recipeIngredients(recipe.productId),
            meta: ingredientsView
        }
        for (let i = 0; i < Math.min(7, recipe.ingredients.length); i++) {
            const ingredient = recipe.ingredients[i]
            this.ingredients.push({
                image: "/" + ingredient.ingredientDetails.imageUrl,
                title: ingredient.ingredientDetails.name,
                quantity: ingredient.quantity,
                unit: EatUtil.getUnit(ingredient.quantity, ingredient.unit)
            })
        }
    }
}
