import { BaseWidget, IBaseWidget, IServiceInterfaces, UserContext, WebListWidget } from "@curefit/vm-models"
import { SortOrder } from "@curefit/mongo-utils"
import { createRecipeCards } from "./RecipeCommon"
import * as _ from "lodash"
import { DIYRecipeView } from "@curefit/diy-common"
import LiveUtil from "../util/LiveUtil"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import AppUtil from "../util/AppUtil"
import { LivePackUtil } from "../util/LivePackUtil"

export class RecipeWebBrowseBuilder extends BaseWidget {
    title: string
    tabs: {
        title: string
        categoryId: string
        isSelected: boolean
        recipes: IBaseWidget[]
        pageOffset: { // this will have details of page cleint will call
            pageNumber: number
            pageSize: number
        }
        hasNextPage?: boolean
    }[]
    hasNextPage: boolean
    recipeCards: IBaseWidget[]
    selectedCategoryId: string
    pageOffset: { // this will have details of page cleint will call (only for pagenated calls)
        pageNumber: number
        pageSize: number
    }
    constructor() {
        super("RECIPE_BROWSE_WEB_WIDGET")
        this.title = "Browse Recipes"
        this.tabs = []
        this.hasNextPage = false
        this.recipeCards = undefined
        this.pageOffset = undefined
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {

        const categoryId = _.get(queryParams, "categoryId", "recipes_all") as string
        const pageNumber = _.get(queryParams, "pageNumber", 0) as number
        const pageSize = _.get(queryParams, "pageSize", 20) as number
        const bookmarkedOnly = String(_.get(queryParams, "bookmarkedOnly", false)) === "true"
        const vegOnly = String(_.get(queryParams, "vegOnly", false)) === "true"
        const onlyCardsRequired = _.get(queryParams, "onlyCardsRequired", false) as boolean
        const userId = userContext.userProfile.userId
        let hasNextPage = false
        const location = AppUtil.getCountryId(userContext) // country filter
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        const recipeCategoriesPromise = interfaces.diyService.getRecipeCategories(location)

        const start = pageNumber * pageSize
        let recipeItems: DIYRecipeView[]

        if (bookmarkedOnly) {
            recipeItems = await interfaces.diyService.getAllSubscribedRecipesForUser(userId, tenant, start, pageSize + 1)
        } else if (categoryId && categoryId !== "recipes_all") {
            recipeItems = await interfaces.diyService.getRecipesForCategory(categoryId, vegOnly, tenant, start, pageSize + 1, "productId", SortOrder.DESC, userId, location)
        } else {
            recipeItems = await interfaces.diyService.getAllRecipes(vegOnly, tenant, start, pageSize + 1, "productId", SortOrder.DESC, userId, location)
        }

        const recipeCardPromise: Promise<IBaseWidget>[] = []
        if (_.isNil(recipeItems) || _.isEmpty(recipeItems)) {
            if (pageNumber === 0) {
                recipeCardPromise.push({
                    message: "No recipes to show.",
                    widgetType: "NO_DATA_MESSAGE_WIDGET"
                } as unknown as Promise<IBaseWidget>)
            }
            hasNextPage = false
        }

        if (recipeItems.length > pageSize) {
            recipeItems = recipeItems.splice(0, pageSize)
            hasNextPage = true
        }

        const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(interfaces.cultBusiness, interfaces.diyService, userContext)
        for (let i = 0; i < recipeItems.length; i = i + 1) {
            recipeCardPromise.push(createRecipeCards(recipeItems[i], userContext, isUserEligibleForTrial, isUserEligibleForMonetisation, bookmarkedOnly))
        }

        const recipeMealCards = await Promise.all(recipeCardPromise)

        if (AppUtil.isWeb(userContext) && bookmarkedOnly) {
            const webListWidget = await new WebListWidget("Bookmarked Recipes", recipeMealCards).buildView(undefined, undefined, undefined)

            return webListWidget
        }

        if (onlyCardsRequired) {
            this.title = undefined
            this.tabs = undefined
            this.recipeCards = recipeMealCards
            this.pageOffset = {
                pageSize: pageSize,
                pageNumber: pageNumber + 1
            }
            this.hasNextPage = hasNextPage
            return this
        }

        const allCategory = {
            title: "All",
            categoryId: "recipes_all",
            isSelected: _.isNil(categoryId) || categoryId === "recipes_all",
            recipes: _.isNil(categoryId) || categoryId === "recipes_all" ? recipeMealCards : [],
            pageOffset: _.isNil(categoryId) || categoryId === "recipes_all" ? {
                pageSize: pageSize,
                pageNumber: pageNumber + 1
            } : {
                    pageNumber: 0,
                    pageSize: 20
                },
            hasNextPage: _.isNil(categoryId) || categoryId === "recipes_all" ? hasNextPage : true // client assumes there are pages possible on the unexplored category
        }
        this.tabs.push(allCategory)

        let recipeCategories = await recipeCategoriesPromise
        recipeCategories = recipeCategories.filter(c => c.status === "LIVE")
        recipeCategories.forEach(cat => {
            this.tabs.push({
                title: cat.name,
                isSelected: (<any>cat)._id === categoryId,
                categoryId: (<any>cat)._id,
                recipes: (<any>cat)._id === categoryId ? recipeMealCards : [],
                pageOffset: (<any>cat)._id === categoryId ? {
                    pageSize: pageSize,
                    pageNumber: pageNumber + 1
                } : {
                        pageNumber: 0,
                        pageSize: 20
                    },
                hasNextPage: (<any>cat)._id === categoryId ? hasNextPage : true // client assumes there are pages possible on the unexplored category
            })
        })
        this.selectedCategoryId = categoryId

        return this
    }
}