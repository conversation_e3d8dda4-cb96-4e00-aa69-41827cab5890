import * as express from "express"
import { Container, inject } from "inversify"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { Logger, BASE_TYPES } from "@curefit/base"
import AuthMiddleware from "../auth/AuthMiddleware"
import { DIYRecipeProduct } from "@curefit/diy-common"
import { RecipeDetailView } from "./RecipeDetailView"
import { IDIYFulfilmentService, DIY_CLIENT_TYPES } from "@curefit/diy-client"
import { Feedback } from "@curefit/feedback-common"
import { IFeedbackReadOnlyDao, FEEDBACK_MONGO_TYPES } from "@curefit/feedback-mongo"
import { Session } from "@curefit/userinfo-common"
import IFeedbackBusiness from "../ugc/IFeedbackBusiness"
import { Action, IBaseWidget, UserContext } from "@curefit/vm-models"
import { RecipeListWidget } from "../page/vm/widgets/RecipeListWidget"
import { TabItem } from "@curefit/vm-models"
import FeedbackUtil from "../util/FeedbackUtil"
import * as _ from "lodash"
import { RecipeClpProductsViewBuilder } from "./RecipeClpProductsViewBuilder"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { ErrorFactory } from "@curefit/error-client"
import {
    buildRecipeDoubleMagazinePromise,
    IRecipeAutoFill,
    IRecipePagenatedResponse,
    IRecipeParams
} from "./RecipeCommon"
import {
    Autocomplete,
    Filter,
    FILTER_TYPE,
    RangeValue,
    SelectValue,
    SuggestionType,
    ToggleValue
} from "@curefit/eat-common"
import { ActionUtil, SeoUrlParams } from "@curefit/base-utils"
import AppUtil from "../util/AppUtil"
import { RecipeSearchBuilder } from "./RecipeSearchBuilder"
import { PageTypes } from "@curefit/apps-common"
import { RecipeDetailWebView } from "../live/eatlive/EatLiveWidgets"
import { RecipeWebBrowseBuilder } from "./RecipeWebBrowseBuilder"
import { ErrorCodes } from "../error/ErrorCodes"
import { SortOrder } from "@curefit/mongo-utils"
import { RecipeCollectionDetailViewBuilder } from "./RecipeCollectionDetailViewBuilder"
import LiveUtil, { RECIPE_PRODUCT_TYPE } from "../util/LiveUtil"
import { LivePackUtil } from "../util/LivePackUtil"

export function controllerFactory(kernel: Container) {

    @controller("/recipes",
        kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession
    )
    class RecipeController {
        constructor(
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) private diyFulfilmentService: IDIYFulfilmentService,
            @inject(FEEDBACK_MONGO_TYPES.FeedbackReadOnlyDao) private feedbackDao: IFeedbackReadOnlyDao,
            @inject(CUREFIT_API_TYPES.FeedbackBusiness) private feedbackBusiness: IFeedbackBusiness,
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
            @inject(CUREFIT_API_TYPES.RecipeClpProductsViewBuilder) private recipeClpProductsViewBuilder: RecipeClpProductsViewBuilder,
            @inject(CUREFIT_API_TYPES.RecipeSearchBuilder) private recipeSearchBuilder: RecipeSearchBuilder,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        ) {
        }

        @httpGet("/details/:id")
        public async getRecipeById(req: express.Request, res: express.Response): Promise<RecipeDetailView> {
            const session: Session = req.session
            const id: string = req.params.id
            const userContext = req.userContext as UserContext
            const tenant = AppUtil.getTenantFromUserContext(userContext)
            const countryId = AppUtil.getCountryId(userContext)
            const recipe: DIYRecipeProduct = await this.diyFulfilmentService.getRecipeById(id, tenant, session.userId, countryId)
            let deeplink: string
            const isInternationalUser = AppUtil.isInternationalApp(userContext)

            const isDeeplinkShareUrlSupported = await AppUtil.isRecipeShareActionSupported(userContext)
            const autoPlayRecipeVideo = await AppUtil.playRecipeVideoByDefault(userContext, this.serviceInterfaces.hamletBusiness)
            if (isDeeplinkShareUrlSupported) {
                deeplink = await this.serviceInterfaces.deeplinkService.getRecipeDeeplink(tenant, recipe)
            }
            if (AppUtil.isWeb(userContext)) {
                const categories = await this.diyFulfilmentService.getRecipeCategories(countryId)
                return new RecipeDetailWebView().buildView(recipe, deeplink, userContext, categories, isInternationalUser)
            }
            const recipeView: RecipeDetailView = new RecipeDetailView(recipe, deeplink, autoPlayRecipeVideo)
            return recipeView
        }

        @httpGet("/feedback/:id")
        public async getRecipeFeedback(req: express.Request, res: express.Response): Promise<any> {
            const session: Session = req.session
            const id: string = req.params.id
            const userContext = req.userContext as UserContext
            let feedback: Feedback = await this.feedbackDao.findOne({ itemId: id, userId: session.userId })
            /**
             * Not allowing the user to again give a feedback if he has already rated the recipe
             * and if his earlier rating does not lie in {DISMISSED, NOT_RATED}
             */
            if (AppUtil.RecipeRateAgainNotAllowedSupported(userContext) && !_.isNil(feedback) && !["DISMISSED", "NOT_RATED"].includes(feedback.rating)) {
                return {
                    title: "You have rated this recipe!",
                    feedbackId: feedback.feedbackId
                }
            }
            if (feedback === undefined || feedback === null) {
                feedback = await this.feedbackBusiness.createDIYRecipeFeedback(session.userId, id)
            }
            const feedbackData = {
                title: "Rate this recipe",
                feedbackId: feedback.feedbackId,
                icons: FeedbackUtil.getFeedbackIcons()
            }
            return feedbackData

        }

        @httpGet("/category/:categoryId")
        async fetchTabData(req: express.Request, res: express.Response): Promise<any> {
            const userContext = req.userContext as UserContext
            const tabId = req.params.categoryId
            return this.getTab(tabId, userContext, req.query)
        }

        // using this for fetching the data for recipe clp for page > 0, basically supporting pagenation
        @httpPost("/products")
        async getExtendedClpResponse(req: express.Request, res: express.Response): Promise<any> {
            const userContext = req.userContext as UserContext
            const body = req.body
            const filters = body.filters

            const params: IRecipeParams = {
                pageNumber: _.get(body, "pageNumber", 1) as number,
                vegOnly: String(_.get(filters, "vegOnly", false)) === "true",
                bookMarkedOnly: String(_.get(filters, "bookmarkedOnly", false)) === "true",
                categoryId: _.get(filters, "categoryId", "recipes_all") as string,
                pageSize: _.get(body, "pageSize", 10),
                recipe: undefined
            }
            if (AppUtil.isWeb(userContext)) {
                return new RecipeWebBrowseBuilder().buildView(this.serviceInterfaces, userContext, { ...params, onlyCardsRequired: true } as any)
            } else if (AppUtil.isSugarFitOrUltraFitApp(userContext)) {
                return {
                    title: "",
                    body: [],
                    hasNextPage: false,
                }
            } else {
                return await this.recipeClpProductsViewBuilder.buildView(this.serviceInterfaces, userContext, {}, params)
            }
        }

        @httpPost("/search")
        async getSearchResponse(req: express.Request): Promise<IRecipePagenatedResponse> {
            // we get everything  back inside meta of auto suggest response and then we make the filters here
            /*
             * this is used for recipe search
             * this is used for rendering the new eat live clp when filters are applied
             */
            const body = req.body
            const userContext = req.userContext as UserContext
            const searchText: string = _.get(body, "searchText", undefined) as string
            const pageNumber: number = _.get(body, "pageNumber", 0) as number
            const pageSize: number = _.get(body, "pageSize", 10) as number
            const categoryId: string = _.get(body, "categoryId", undefined) as string
            const type: SuggestionType = _.get(body, "type", undefined)
            const customAppliedFilters = _.get(body, "customFilter", []) as Filter[]
            const vegOnly = String(_.get(body, "vegOnly", false)) === "true"
            const bookmarkOnly = String(_.get(body, "bookmarkOnly", false)) === "true"
            const countryId = AppUtil.getCountryId(userContext)

            let queryParams = !_.isNil(req.query) ? req.query : {}
            queryParams = !_.isNil(req.body) ? { ...queryParams, ...req.body } : queryParams

            let filters: Filter[] = []
            if (type === "ITEM_IN_CATEGORY") {
                filters = [
                    {
                        title: searchText,
                        type: "MULTISELECT",
                        key: type,
                        values: [
                            {
                                title: categoryId,
                                key: categoryId
                            }
                        ]
                    }
                ]
            }

            if (vegOnly) {
                // we make veg only as a toggle filter only and pass along
                // hardcoded veg quick filter
                const vegFilter: Filter = {
                    type: "TOGGLE",
                    key: "IS_VEG",
                    title: "Veg",
                    isQuickFilter: true,
                    values: {
                        selected: vegOnly
                    }
                }
                customAppliedFilters.push(vegFilter)
            }

            if (AppUtil.isInternationalApp(userContext)) {
                // Location Filter for International search
                const locationFilter: Filter = {
                    type: "MULTISELECT",
                    key: "LOCATION_FILTER",
                    title: "Location",
                    values: [
                        {
                            title: countryId,
                            key: countryId
                        }
                    ]
                }
                filters.push(locationFilter) // Internal filter for location
            }

            const params: IRecipeParams = {
                searchText: searchText,
                pageSize: pageSize,
                pageNumber: pageNumber,
                bookMarkedOnly: bookmarkOnly,
                vegOnly: vegOnly,
                recipe: undefined,
                categoryId: undefined,
                filters: filters, // filters from auto suggest, can contain location filter too
                customFilters: customAppliedFilters // filters from the clp
            }
            return await this.recipeSearchBuilder.buildView(this.serviceInterfaces, userContext, {}, params)
        }

        @httpPost("/bookmark")
        async bookMarkRecipe(req: express.Request, res: express.Response): Promise<any> {
            const body = req.body
            const recipeId: string = _.get(body, "recipeId", undefined)

            if (_.isNil(recipeId)) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("no receipe id provided").build()
            }

            const isBookMarkedbyUser = _.get(body, "isBookmarked", false) as boolean
            // this boolean has the expected new state for the current recipe
            const userContext = req.userContext as UserContext
            const userId = userContext.userProfile.userId

            if (isBookMarkedbyUser === true) {
                // App is telling backend that recipeId: "XYZ"  isBookmarked by user. Now the backend should change its status flag `isBookmarked  to true
                try {
                    const result = await this.serviceInterfaces.diyService.subscribeRecipeForUser(recipeId, userId, AppUtil.getTenantFromUserContext(userContext))
                    return {
                        isBookmarked: isBookMarkedbyUser
                    } // returns if the operation was successful
                } catch (e) {
                    throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("error in subscribing receipe for user").build()
                }
            } else {
                try {
                    const result = await this.serviceInterfaces.diyService.unsubscribeRecipeForUser(recipeId, userId)
                    return {
                        isBookmarked: isBookMarkedbyUser
                    } // returns if the operation was successful
                } catch (e) {
                    throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("error in unsubscribing receipe for user").build()
                }
            }
        }

        @httpGet("/autosuggest")
        async getRecipeAutoFillSuggestions(req: express.Request): Promise<IRecipeAutoFill[]> {
            const searchText = _.get(req, "query.searchText")
            const userId = _.get(req, "session.userId")
            const userContext = req.userContext as UserContext
            const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(this.serviceInterfaces.cultBusiness, this.diyFulfilmentService, userContext)
            const countryId = AppUtil.getCountryId(userContext)
            const tenant = AppUtil.getTenantFromUserContext(userContext)
            const result = await this.serviceInterfaces.diyService.getAutocompleteResponseForRecipes(userId, tenant, searchText, countryId)
            const autofillResponse: IRecipeAutoFill[] = []
            if (!_.isEmpty(result)) {
                result.forEach((fill: Autocomplete) => {
                    if (fill.type === "RECIPE") {
                        // direct recipe product case
                        const recipeId: string = _.get(fill, "suggestionMeta.entityId")
                        const seoParams: SeoUrlParams = {
                            productName: fill.displayText
                        }

                        const action: Action = {
                            actionType: "NAVIGATION",
                            url: ActionUtil.recipeHomePage(recipeId, seoParams, userContext.sessionInfo.userAgent)
                        }
                        const diyAction: Action = LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, fill.locked, action, isUserEligibleForTrial, RECIPE_PRODUCT_TYPE, "eat_recipe_search_suggestion", "", false)
                        autofillResponse.push({
                            searchText: fill.searchText,
                            displayText: fill.displayText,
                            action: diyAction,
                            analyticsData: {
                                eventName: "recipe_search_suggestion_click",
                                meta: {
                                    searchTerm: searchText,
                                    suggestedTerm: fill.displayText,
                                    suggestionType: fill.type,
                                    navigateTo: PageTypes.RecipeSingles,
                                    recipeId: recipeId,
                                }
                            }
                        })
                    } else if (fill.type === "ITEM_IN_CATEGORY") {
                        // category case
                        const categoryId = _.get(fill, "suggestionMeta.entityId", undefined)
                        autofillResponse.push({
                            displayText: fill.displayText,
                            searchText: fill.searchText,
                            meta: {
                                searchText: fill.searchText, // still searching for same text but now  in a category
                                categoryId: categoryId,
                                type: fill.type
                            },
                            analyticsData: {
                                eventName: "recipe_search_suggestion_click",
                                meta: {
                                    searchTerm: searchText,
                                    suggestedTerm: fill.searchText,
                                    suggestionType: fill.type,
                                    navigateTo: PageTypes.EatRecipeSearchPage,
                                    categoryId: categoryId,
                                }
                            }
                        })
                    } else {
                        // type: ingredient | recipe_category case (generic cases)
                        autofillResponse.push({
                            searchText: fill.searchText,
                            displayText: fill.displayText,
                            meta: {
                                searchText: fill.displayText, // searching for the ingredient now
                                type: fill.type,
                            },
                            analyticsData: {
                                eventName: "recipe_search_suggestion_click",
                                meta: {
                                    searchTerm: searchText,
                                    suggestedTerm: fill.displayText,
                                    suggestionType: fill.type,
                                    navigateTo: PageTypes.EatRecipeSearchPage
                                }
                            }
                        })
                    }
                })
            }
            return autofillResponse
        }

        // ----------------------------------------- PRIVATE METHODS ---------------------------------------------------

        async getTab(tabId: string, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<TabItem> {
            const tabName = queryParams.tabName
            // TODO : send the tabId to this method in queryParams itself
            queryParams.diyCategory = tabId
            const itemList = await new RecipeListWidget().buildView(this.serviceInterfaces, userContext, queryParams)
            const fetchedWidgetCount = (<RecipeListWidget>itemList).widgets.length
            let nextQuery
            if (fetchedWidgetCount >= (+queryParams.count)) {
                nextQuery = {
                    tabId: tabId,
                    start: (+queryParams.start) + (+queryParams.count),
                    count: +queryParams.count
                }
            }
            return {
                tabId: tabId,
                vegFilter: (queryParams.vegOnly === "true") ? "veg" : "all",
                title: tabName,
                itemList: itemList,
                nextQuery: nextQuery
            }
        }

    }
    return RecipeController
}

export default controllerFactory

