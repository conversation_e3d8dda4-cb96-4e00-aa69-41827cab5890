import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { UserContext } from "@curefit/userinfo-common"
import {
    buildRecipeDoubleMagazinePromise, buildVideoCardPromise,
    getDIYRecipeViewFromEatContentTile, getSearchVideoCardsPromise, IRecipePagenatedResponse,
    IRecipeParams, mergeFilters
} from "./RecipeCommon"
import { Filter, PageInfo, SearchInfo, Sort } from "@curefit/eat-common"
import * as _ from "lodash"
import {
    Action,
    AppliedFilterWidget,
    EatTitleWidget,
    IBaseWidget,
    IServiceInterfaces,
    ListWidgetV3,
    EatSearchAutoCompleteWidget,
    WebListWidget
} from "@curefit/vm-models"
import { injectable } from "inversify"
import LiveUtil from "../util/LiveUtil"
import AppUtil from "../util/AppUtil"
import { DIYRecipeView, EatContentTile, EatLiveUnifiedSearchResponse } from "@curefit/diy-common"
import { ListWidgetV2, PageTypes } from "@curefit/apps-common"
import { RecipeFilterContainerWidgetV2Builder } from "./RecipeFilterContainerWidgetV2Builder"
import { RecipePackWidget } from "../page/vm/widgets/RecipePackWidget"
import { NewRecipesWidget } from "../live/eatlive/EatLiveWidgets"
import { IDIYFulfilmentService } from "@curefit/diy-client"
import { RecipeWebBrowseBuilder } from "./RecipeWebBrowseBuilder"
import { BrowseDoubleMagazineWidget } from "../page/vm/widgets/RecipeBrowseWidgetView"
import { ProductType } from "@curefit/product-common"
import { LivePackUtil } from "../util/LivePackUtil"

@injectable()
export class RecipeSearchBuilder {
    constructor() {

    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filter: string]: any }, params: IRecipeParams): Promise<IRecipePagenatedResponse> {
        const searchText = params.searchText
        const filter: Filter[] = _.get(params, "filters", []) // filters passed on auto suggestions
        const hasNextPage = false // no pagenation in search, we may populate it for bookmakr though
        const userId = userContext.userProfile.userId
        const customAppliedFilters = params.customFilters // filters applied by the user
        const bookmarkedOnly = params.bookMarkedOnly
        const pageNumber = params.pageNumber
        const pageSize = params.pageSize
        const pageFrom = _.get(queryParams, "selectedTab") ? _.get(queryParams, "selectedTab") : _.get(queryParams, "pageId", undefined)


        const widgetPromise: Promise<IBaseWidget>[] = []


        const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(interfaces.cultBusiness, interfaces.diyService, userContext)

        if (bookmarkedOnly && AppUtil.isWeb(userContext) === false) {
            const start = pageSize * pageNumber
            return await this.buildBookmarkResponse(interfaces, userContext, start, pageSize, customAppliedFilters)
        }

        const tenant = AppUtil.getTenantFromUserContext(userContext)

        // todo: remove dummy integration
        const recipeItemsPromise = interfaces.diyService.searchEatLiveContent("RECIPE", userId, tenant, { searchText: searchText }, [...filter, ...customAppliedFilters])
        const videoItemsPromise = AppUtil.isEatLiveVideoSearchResultSupported(userContext)
            ? interfaces.diyService.searchEatLiveContent("EAT_LIVE_ON_DEMAND_VIDEO", userId, tenant, { searchText: searchText }, [...filter, ...customAppliedFilters]) : undefined

        const [recipeItemsResponse, videoItemsResponse] = await Promise.all([recipeItemsPromise, videoItemsPromise])
        const recipeItems: DIYRecipeView[] = _.compact(_.map(_.get(recipeItemsResponse, "contentTiles", []), tile => getDIYRecipeViewFromEatContentTile(tile, interfaces)))
        const videoItems: EatContentTile[] = _.get(videoItemsResponse, "contentTiles", [])

        const recipeCardsPromise = buildRecipeDoubleMagazinePromise(recipeItems, isUserEligibleForTrial, false, "eatrecipesearchpage", userContext, isUserEligibleForMonetisation)
        // video reesults disabled for web
        const videoCardsPromise = getSearchVideoCardsPromise(videoItems, userContext, pageFrom, true) // buildVideoCardsPromise(interfaces, userContext, videoItems, isUserEligibleForLivePackTrial, "eatrecipesearchpage", isUserEligibleForMonetisation)

        const videoCards = await Promise.all(videoCardsPromise)
        if (AppUtil.isWeb(userContext)) {
            const recipeCards = await Promise.all(recipeCardsPromise)
            const webRecipeCards: IBaseWidget[] = []
            recipeCards.forEach((card: BrowseDoubleMagazineWidget) => webRecipeCards.push(...card.items))

            const searchBarWidget = await new EatSearchAutoCompleteWidget().buildView(interfaces, userContext, { ...queryParams, ...params }) as EatSearchAutoCompleteWidget
            searchBarWidget.placeholder = "Search for recipes"
            widgetPromise.push(Promise.resolve(searchBarWidget))
            if (!_.isEmpty(webRecipeCards)) {
                const gridWidgetPromise = new WebListWidget(undefined, webRecipeCards).buildView(undefined, undefined, {})
                widgetPromise.push(gridWidgetPromise)
            }
        } else {
            if (!_.isEmpty(customAppliedFilters)) {
                // showing seach bar and applied filters widget only when a filter is applied on the clp
                const videoAvailableFilters = (videoItemsResponse ? videoItemsResponse.availableFilters : [])
                const recipeAvailableFilters = recipeItemsResponse.availableFilters
                const availableFilters = mergeFilters(recipeAvailableFilters, videoAvailableFilters)

                widgetPromise.push(new RecipeFilterContainerWidgetV2Builder().buildView(interfaces, userContext, {
                    "filters": {
                        vegOnly: !!params.vegOnly,
                        bookmarkedOnly: bookmarkedOnly
                    },
                    "showBookmarkFilter": false, // not showing bookmakr when filters is applied
                    "sendNavigationAction": false, // we dont want to again navigate once on the search page, we just make the api call to fetch data as per filter
                    "appliedFilters": customAppliedFilters,
                    "availableFilters": availableFilters
                }))
            }
            if (!_.isEmpty(videoCards)) {
                const listWidget = new NewRecipesWidget()
                listWidget.title = "Results for shows"
                listWidget.items = videoCards
                listWidget.layoutProps = {
                    height: 160,
                    width: 160
                }
                widgetPromise.push(Promise.resolve(listWidget))
            }
            if (!_.isEmpty(recipeCardsPromise)) {
                widgetPromise.push(new EatTitleWidget("Results for recipes").buildView(interfaces, userContext, {}))
                widgetPromise.push(...recipeCardsPromise)
            }
        }

        if (_.isEmpty(recipeItems) && _.isEmpty(videoItems)) {
            widgetPromise.push({
                message: "No recipes to show.",
                widgetType: "NO_DATA_MESSAGE_WIDGET"
            } as unknown as Promise<IBaseWidget>)
        }

        const body = _.compact(await Promise.all(widgetPromise))

        return {
            body,
            hasNextPage: hasNextPage //  since we dont have pagenation for  search api till now
        }
    }

    async buildBookmarkResponse(interfaces: CFServiceInterfaces, userContext: UserContext, start: number = 0, count: number = 10, appliedFilters: Filter[] = []): Promise<IRecipePagenatedResponse> {

        const widgetPromise: Promise<IBaseWidget>[] = []
        let hasNextPage = false
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        let bookmarkedRecipes = await interfaces.diyService.getAllSubscribedRecipesForUser(userContext.userProfile.userId, tenant, start, count + 1)

        if (_.isNil(bookmarkedRecipes) || _.isEmpty(bookmarkedRecipes)) {
            widgetPromise.push({
                message: "No recipes to show.",
                widgetType: "NO_DATA_MESSAGE_WIDGET"
            } as unknown as Promise<IBaseWidget>)
            return {
                body: _.compact(await Promise.all(widgetPromise)),
                hasNextPage: hasNextPage
            }
        }

        if (bookmarkedRecipes.length >= count) {
            bookmarkedRecipes = bookmarkedRecipes.splice(0, count)
            hasNextPage = true
        }
        const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(interfaces.cultBusiness, interfaces.diyService, userContext)

        widgetPromise.push(new RecipeFilterContainerWidgetV2Builder().buildView(interfaces, userContext, {
            "filters": {
                vegOnly: false, // if bookmakr is enabled, other fields are not honoured
                bookmarkedOnly: true
            },
            "showBookmarkFilter": true,
            "sendNavigationAction": false, // we dont want to again navigate once on the search page, we just make the api call to fetch data as per filter
            "appliedFilters": appliedFilters
        }))
        widgetPromise.push(Promise.resolve(new EatTitleWidget("Bookmarked Recipes")))
        const recipeCardsPromise = buildRecipeDoubleMagazinePromise(bookmarkedRecipes, isUserEligibleForTrial, true, "recipesearchpage", userContext, isUserEligibleForMonetisation)
        widgetPromise.push(...recipeCardsPromise)

        const body = _.compact(await Promise.all(widgetPromise))

        return {
            hasNextPage: hasNextPage,
            body: body
        }



    }
}
