import * as _ from "lodash"
import { ProductDetailPage } from "../common/views/WidgetView"
import { DIYRecipeIngredient, DIYRecipeProduct } from "@curefit/diy-common"
import { RecipeIngredientsGridWidget } from "./RecipeIngredientsGridWidget"
import { IIngredientDetails } from "./RecipeIngredientsListWidget"
import EatUtil from "../util/EatUtil"

export class RecipeIngredientsView extends ProductDetailPage {

    constructor(recipe: DIYRecipeProduct) {
        super()
        if (_.isNil(recipe)) return
        const categoryMap: Map<string, IIngredientDetails[]> = this.getIngredientGridWidgetList(recipe.ingredients)
        categoryMap.forEach(async (value: IIngredientDetails[], key: string) => {
            const recipeIngredientsWidget: RecipeIngredientsGridWidget = new RecipeIngredientsGridWidget(key, value)
            this.widgets.push(recipeIngredientsWidget)
        })
    }

    getIngredientGridWidgetList(ingredientList: DIYRecipeIngredient[]): Map<string, IIngredientDetails[]> {
        const categoryMap = new Map<string, IIngredientDetails[]>()
        for (let i = 0; i < ingredientList.length; i++) {
            const ingredient = ingredientList[i]
            if (categoryMap.has(ingredient.ingredientDetails.ingredientCategory)) {
                categoryMap.get(ingredient.ingredientDetails.ingredientCategory).push({
                    image: "/" + ingredient.ingredientDetails.imageUrl,
                    title: ingredient.ingredientDetails.name,
                    quantity: ingredient.quantity,
                    unit: EatUtil.getUnit(ingredient.quantity, ingredient.unit)
                })
            } else {
                const categoryIngredientList: IIngredientDetails[] = [{
                    image: "/" + ingredient.ingredientDetails.imageUrl,
                    title: ingredient.ingredientDetails.name,
                    quantity: ingredient.quantity,
                    unit: EatUtil.getUnit(ingredient.quantity, ingredient.unit)
                }]
                categoryMap.set(ingredient.ingredientDetails.ingredientCategory === undefined ? "Others" : ingredient.ingredientDetails.ingredientCategory, categoryIngredientList)
            }
        }
        return categoryMap
    }
}
