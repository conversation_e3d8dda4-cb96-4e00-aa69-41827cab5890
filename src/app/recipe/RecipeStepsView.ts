import * as _ from "lodash"
import { ProductDetailPage } from "../common/views/WidgetView"
import { DIYRecipeProduct } from "@curefit/diy-common"
import { RecipeCarouselWidget } from "./RecipeCarouselWidget"

export class RecipeStepsView extends ProductDetailPage {

    // maybe I shouldn't use a widget here, it's a very specific page
    constructor(recipe: DIYRecipeProduct, deeplink: string) {
        super()

        if (_.isNil(recipe)) return

        // Recipe Carousel Widget
        const recipeCarouselWidget: RecipeCarouselWidget = new RecipeCarouselWidget(recipe, deeplink)
        this.widgets.push(recipeCarouselWidget)
    }

}
