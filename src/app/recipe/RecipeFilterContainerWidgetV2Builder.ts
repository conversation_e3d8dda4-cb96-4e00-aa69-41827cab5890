import { AppliedFilterWidget, BaseWidget, IBaseWidget, IServiceInterfaces, UserContext } from "@curefit/vm-models"
import * as _ from "lodash"
import AppUtil from "../util/AppUtil"
import { PageTypes } from "@curefit/apps-common"
import { Filter } from "@curefit/eat-common"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { getVegFilter, mergeFilters } from "./RecipeCommon"

export class RecipeFilterContainerWidgetV2Builder extends BaseWidget {
    widgets: IBaseWidget[]
    constructor() {
        super("RECIPE_FILTER_CONTAINER_WIDGET_V2")
        this.widgets = []
    }
    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: any }): Promise<IBaseWidget> {
        let bookmarkedOnly = String(_.get(queryParams, "filters.bookmarkedOnly", false)) === "true"
        let vegOnly = String(_.get(queryParams, "filters.vegOnly", false)) === "true"
        const showBookmarkFilter = _.get(queryParams, "showBookmarkFilter", true)
        const sendNavigationAction = _.get(queryParams, "sendNavigationAction", true)
        const appliedFilters: Filter[] = _.get(queryParams, "appliedFilters", [])
        let availableFilters: Filter[] = _.get(queryParams, "availableFilters", undefined)

        if (_.isNil(availableFilters)) {
            availableFilters = await this.getAvailableFilters(interfaces as CFServiceInterfaces, userContext)
        }

        if (AppUtil.isWeb(userContext)) {
            // supporting the queryParams for web
            bookmarkedOnly = String(_.get(queryParams, "bookmarkedOnly", false)) === "true"
            vegOnly = String(_.get(queryParams, "vegOnly", false)) === "true"
        }
        if (bookmarkedOnly) {
            vegOnly = false // when bookmark is enabled, all other filters are void
        }

        const isRecipeCustomFilterSupported = AppUtil.isRecipeCustomFilterSupported(userContext) && !_.isNil(appliedFilters) && !_.isEmpty(appliedFilters)
        const [searchBarWidget, vegFilter, bookmarkFiter, appliedFiltersWidget, availableCustomFilters] = await Promise.all([
            this.getSearchBarWidget(),
            getVegFilter(vegOnly),
            this.getBookmarkFilter(bookmarkedOnly),
            this.getAppliedFiltersWidget(appliedFilters),
            isRecipeCustomFilterSupported ? this.getCustomFilters(availableFilters, appliedFilters, sendNavigationAction) : undefined
        ])

        const filters = []
        if (bookmarkedOnly) {
            filters.push(bookmarkFiter)
        } else {
            filters.push(vegFilter, availableCustomFilters)
            showBookmarkFilter ? filters.push(bookmarkFiter) : null
        }

        const filterWidget = {
            widgetType: "RECIPE_FILTER_WIDGET_V2",
            filters: _.compact(filters)
        } as unknown as IBaseWidget

        this.widgets.push(searchBarWidget, filterWidget)
        !_.isNil(appliedFiltersWidget) ? this.widgets.push(appliedFiltersWidget) : null
        return this
    }

    getAppliedFiltersWidget(appliedFilters: Filter[]): Promise<IBaseWidget> {
        if (_.isNil(appliedFilters) || _.isEmpty(appliedFilters)) {
            return undefined
        }
        const filterWidget = new AppliedFilterWidget()
        filterWidget.list = []
        filterWidget.list.push({
            title: appliedFilters.length === 1 ? "1 FILTER APPLIED" : `${appliedFilters.length} FILTERS APPLIED`,
            action: {
                actionType: "NAVIGATE_BACK"
            }
        })
        return filterWidget.buildView()
    }

    getCustomFilters = async (availableFilters: Filter[], appliedFilter: Filter[], sendNavigationAction: boolean = true) => {
        return {
            type: "RECIPE_FILTERS",
            action: {
                actionType: "SHOW_RECIPE_FILTERS",
                meta: {
                    filterModalWidget: {
                        availableFilters: availableFilters,
                        appliedFilters: appliedFilter,
                        eatClpTab: "RECIPES",
                        widgetType: "SORT_FILTER_WIDGET",
                        action: sendNavigationAction ? {
                            actionType: "NAVIGATION",
                            url: `curefit://eatrecipe`
                        } : undefined
                    }
                }
            }
        }
    }

    async getAvailableFilters(interfaces: CFServiceInterfaces, userContext: UserContext): Promise<Filter[]> {
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        const recipeAvailableFilters = (await interfaces.diyService.searchEatLiveContent("RECIPE", userContext.userProfile.userId, tenant, { searchText: undefined })).availableFilters
        const videoAvailableFilters = (await interfaces.diyService.searchEatLiveContent("EAT_LIVE_ON_DEMAND_VIDEO", userContext.userProfile.userId, tenant, { searchText: undefined })).availableFilters

        return Promise.resolve(mergeFilters(recipeAvailableFilters, videoAvailableFilters))

    }

    async getSearchBarWidget() {
        return {
            widgetType: "EAT_SEARCH_AUTO_COMPLETE_WIDGET_V2",
            initialSearchText: "",
            placeholder: "Search",
        } as unknown as IBaseWidget
    }

    async getBookmarkFilter(bookmarkOnly: boolean = false) {
        return {
            type: "BOOKMARKED",
            meta: {
                isEnabled: bookmarkOnly,
            },
        }
    }
}