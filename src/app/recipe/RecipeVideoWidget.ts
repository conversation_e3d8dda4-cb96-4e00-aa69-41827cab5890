import { WidgetType, WidgetView } from "../common/views/WidgetView"
import { DifficultyLevel, DIYRecipeProduct } from "@curefit/diy-common"
import { ActivityType } from "@curefit/product-common"
import { HourMin } from "@curefit/base-common"
import { CONTENT_CDN_BASE_PATH, TimeUtil } from "@curefit/util-common"
import { UrlPathBuilder } from "@curefit/product-common"
import { Action } from "@curefit/vm-models"
import * as _ from "lodash"
import { ActionUtil } from "@curefit/base-utils"
import AppUtil from "../util/AppUtil"
import { UserContext } from "@curefit/userinfo-common"
import { IBaseWidget } from "@curefit/vm-models"

export interface IRecipeDetails {
    key: string
    value: string
}

export const difficultyDisplayMap: Map<DifficultyLevel, string> = new Map<DifficultyLevel, string>()
difficultyDisplayMap.set("DIFFICULT", "Difficult")
difficultyDisplayMap.set("EASY", "Easy")
difficultyDisplayMap.set("INTERMEDIATE", "Intermediate")

export class RecipeVideoWidget implements WidgetView {
    widgetType: WidgetType
    id: string
    title: string
    videoUrl: string
    recipeDetails: IRecipeDetails[]
    consumptionData: {
        activityId: string
        activityName: string
        activityType: ActivityType
        consumptionRequired: boolean
    }
    bookmarkMeta?: {
        status: boolean,
        action: Action
    }
    shareAction?: Action
    playVideoByDefault?: boolean
    widgets?: WidgetView[] // just for web
    breadCrumbs?: { // just for web
        title: string,
        link?: string
    }[]
    description?: string // for web recipe description

    constructor(recipe: DIYRecipeProduct, deepLink: string, autoPlayVideo: boolean = true) {
        this.widgetType = "RECIPE_VIDEO_WIDGET"
        this.recipeDetails = []
        this.id = recipe.productId
        this.title = recipe.title
        this.videoUrl = UrlPathBuilder.getVideoAbsolutePath(recipe.contentId, recipe.contentFormat)
        const preparationTime: HourMin = TimeUtil.convertDurationSecondsToHourMin(recipe.preparationTime)
        let preparationTimeString: string = preparationTime.min > 0 ? preparationTime.min + " Min" : ""
        if (preparationTime.hour > 0) {
            preparationTimeString = preparationTime.hour + " Hr " + preparationTimeString
        }
        this.recipeDetails.push(
            {
                key: "Time",
                value: preparationTimeString
            },
            {
                key: "Difficulty",
                value: difficultyDisplayMap.get(recipe.difficultyLevel)
            },
            {
                key: "Serves",
                value: recipe.serving + ""
            }
        )
        this.bookmarkMeta = {
            status: _.get(recipe, "userMeta.isSubscribed",  false), // current recipe bookmark state
            action: {
                actionType: _.get(recipe, "userMeta.isSubscribed", false) ? "UNBOOKMARK_RECIPE" : "BOOKMARK_RECIPE",
                meta: {
                    recipeId: recipe.productId,
                    isBookMarked: !_.get(recipe, "userMeta.isSubscribed", false) // what the user wants to do with recipe
                }
            }
        }
        this.shareAction = deepLink ? {
                    actionType: "SHARE_ACTION",
                    title: "Share",
                    meta: {
                        videoUrl: UrlPathBuilder.getVideoAbsolutePath(recipe.contentId, recipe.contentFormat),
                        isShareSingle: true,
                        shareOptions: {
                            message: `Hey! Check out this recipe for ${recipe.title}! Explore 100s of healthy recipes with step-by-step guide on the cult.fit app. * ${deepLink} *`
                        }
                    }
                } : undefined

        this.consumptionData = {
            activityId: recipe.productId,
            activityName: recipe.title,
            activityType: "DIY_RECIPE",
            consumptionRequired: true
        }
        this.playVideoByDefault = autoPlayVideo
        this.widgets = []
        this.breadCrumbs = undefined
        this.description = recipe.subTitle
    }
    async buildView() {
        return this
    }
}
