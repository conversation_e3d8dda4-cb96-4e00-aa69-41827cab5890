import { injectable } from "inversify"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { UserContext } from "@curefit/userinfo-common"
import { buildRecipeDoubleMagazinePromise, getBreadCrumbsData, getVegFilter, IRecipePagenatedResponse } from "./RecipeCommon"
import { DIYRecipeCollectionResponse, DIYRecipeView } from "@curefit/diy-common"
import * as _ from "lodash"
import { CollectionsSummaryWidget } from "../page/vm/widgets/ondemand/CollectionsSummaryWidget"
import LiveUtil from "../util/LiveUtil"
import { IBaseWidget, WebListWidget } from "@curefit/vm-models"
import AppUtil from "../util/AppUtil"
import { BrowseDoubleMagazineWidget } from "../page/vm/widgets/RecipeBrowseWidgetView"
import { IDIYFulfilmentService } from "@curefit/diy-client"
import { IOnDemandPageResponse } from "../page/ondemand/OnDemandCommon"
import { LivePackUtil } from "../util/LivePackUtil"

@injectable()
export class RecipeCollectionDetailViewBuilder {
    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: {[filter: string]: any}, isInternationalUser: boolean): Promise<IOnDemandPageResponse> {
        const collectionId = queryParams["collectionId"]
        const vegOnly = String(_.get(queryParams, "vegOnly", false)) === "true"
        const userId = userContext.userProfile.userId
        const user = await interfaces.userCache.getUser(userId)
        const isWeb = AppUtil.isWeb(userContext)
        const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(interfaces.cultBusiness, interfaces.diyService, userContext)

        const diyService = interfaces.diyService as IDIYFulfilmentService
        const collectionPromise = diyService.getRecipeCollectionbyId(vegOnly, userId, collectionId)
        const collection: DIYRecipeCollectionResponse = await collectionPromise

        const recipes = collection.recipes

        const bannerPromise = this.getBanner(collection, userContext, vegOnly, isInternationalUser)
        const cardsPromise = buildRecipeDoubleMagazinePromise(recipes, isUserEligibleForTrial, false, "eatrecipecollectionpage", userContext, isUserEligibleForMonetisation )

        const cards = _.compact(await Promise.all(cardsPromise))
        const banner = await bannerPromise

        const widgets: IBaseWidget[] = []

        widgets.push(banner)
        if (isWeb) {
            const webCards: IBaseWidget[] = []
            cards.forEach((c: BrowseDoubleMagazineWidget) => {
                webCards.push(...c.items)
            })
            const gridWidget = await new WebListWidget(undefined, _.compact(webCards)).buildView()
            widgets.push(gridWidget)
        } else {
            widgets.push(...cards)
        }

        if (_.isNil(recipes) || _.isEmpty(recipes)) {
            widgets.push({ message: "Oops! No items to show.", widgetType: "NO_DATA_MESSAGE_WIDGET" } as unknown as IBaseWidget)
        }

        return {
            title: "",
            body: widgets,
            hasNextPage: false, // no pagenation support
            filters: {
                widgetType: "RECIPE_FILTER_WIDGET_V2",
                filters: [
                    {
                        type: "VEG",
                        meta: {
                            isEnabled: vegOnly,
                            title: "VEG"
                        }
                    }]
                }
            }
    }

    async getBanner(collection: DIYRecipeCollectionResponse, userContext: UserContext , vegOnly: boolean = false, isInternationalUser: boolean): Promise<IBaseWidget> {

        let numItems: number = _.get(collection, "recipes.length", undefined)
        if (vegOnly) {
            numItems = _.get(collection, "vegItemCount", undefined)
        }
        const widget = new CollectionsSummaryWidget()
        widget.title = collection.title
        widget.subTitle = !_.isNil(numItems) ? `${numItems} Recipes` : undefined
        const isDesktop = AppUtil.isDesktop(userContext)
        widget.imageUrl = isDesktop ? _.get(collection, "imageDetails.web.heroImage") : _.get(collection, "imageDetails.app.heroImage")
        const isWeb = AppUtil.isWeb(userContext)
        if ( isWeb ) {
            widget.breadCrumbs = getBreadCrumbsData(collection.title, "EAT", isInternationalUser)
        }

        return widget.buildView()
    }
}