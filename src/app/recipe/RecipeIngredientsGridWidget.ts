import { WidgetType } from "../common/views/WidgetView"
import { IIngredientDetails, IRecipeIngredientsWidget } from "./RecipeIngredientsListWidget"

export class RecipeIngredientsGridWidget implements IRecipeIngredientsWidget {
    widgetType: WidgetType
    title: string
    ingredients: IIngredientDetails[]

    constructor(category: string, ingredients: IIngredientDetails[]) {
        this.widgetType = "INGREDIENTS_GRID_WIDGET"
        this.ingredients = []

        this.title = category
        this.ingredients = ingredients
    }
}
