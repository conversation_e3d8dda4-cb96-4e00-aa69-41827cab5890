import { BaseAnalyticsEvent } from "./BaseAnalyticsEvent"
import { inject, injectable } from "inversify"
import { BASE_TYPES, FetchUtilV2, Logger } from "@curefit/base"
import { Tenant } from "@curefit/user-common"
import { TimeUtil } from "@curefit/util-common"
import AppUtil from "../util/AppUtil"
import { UserContext } from "@curefit/vm-models"
import * as express from "express"
import AuthUtil from "../util/AuthUtil"
import { Session } from "@curefit/userinfo-common"
import _ = require("lodash")
import fetch from "node-fetch"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import Denque = require("denque")

export interface ICFAnalytics {
    sendEventFromReq(
        data: BaseAnalyticsEvent,
        req: express.Request,
        enableForAlpha: boolean,
        enableForApp: boolean,
        enableForWeb: boolean,
        enableForTV: boolean
    ): void
    sendEventFromUserContext(data: BaseAnalyticsEvent,
         userContext: UserContext,
         enableForAlpha: boolean,
         enableForApp: boolean,
         enableForWeb: boolean,
         enableForTV: boolean
    ): void
}

export interface ICFAnalyticsConfig {
    url: string,
    apiKey: string,
    isEnable: boolean
}

interface CachedEventList {
    [eventName: string]: Denque<any>
}

@injectable()
export class CFAnalytics implements ICFAnalytics {
    cachedEventList: CachedEventList = {}
    CACHE_REFRESH_TIME = 5 * 1000 // cache refreshing after every 5 seconds
    MAX_METABASE_EVENT = 50 // batch size to 50 events per payload
    THRESHOLD_FOR_EVENTS_WARNING = 1000
    THRESHOLD_FOR_EVENTS_ALERT = 10000
    constructor(
        @inject(BASE_TYPES.FetchUtilV2) protected fetchHelper: FetchUtilV2,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(CUREFIT_API_TYPES.CFAnalyticsConfig) private cfAnalyticsConfig: ICFAnalyticsConfig,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
    ) {
        setInterval(async () => {
            try {
                if (cfAnalyticsConfig.isEnable) {
                    const headers = {
                        "Content-type": "application/json",
                        "x-api-key": cfAnalyticsConfig.apiKey
                    }
                    for (const [eventName, dequeList] of Object.entries(this.cachedEventList)) {
                        const listFromDeque = []
                        if (dequeList.length > this.THRESHOLD_FOR_EVENTS_ALERT) {
                            this.rollbarService.sendError(new Error("Threadhold for inMemory datalake event queue reached. Current event queue size: " + dequeList.length))
                        } else if (dequeList.length > this.THRESHOLD_FOR_EVENTS_WARNING) {
                            this.rollbarService.sendWarningMessage("Threadhold for inMemory datalake event queue reached. Current event queue size: " + dequeList.length)
                        }
                        while (dequeList.length > 0) {
                            listFromDeque.push(dequeList.shift())
                            if (listFromDeque.length >= this.MAX_METABASE_EVENT || dequeList.length == 0) {
                                const request: any = {
                                    eventName: eventName,
                                    payload: listFromDeque
                                }
                                await fetch(
                                    cfAnalyticsConfig.url,
                                    this.fetchHelper.post({headers: headers, body: request})
                                )
                                listFromDeque.length = 0
                            }
                        }
                    }
                }
            } catch (e) {
                this.rollbarService.sendWarningMessage("Error while sending events to Datalake from node with error: " + e)
            }
        }, this.CACHE_REFRESH_TIME)
    }

    private objectToMap<extendedEventInterface extends BaseAnalyticsEvent>(obj: extendedEventInterface, userId: string, userAgent: string, appVersion: number, appTenant: Tenant, at: string, cityId: string): any {
        let body = {
            ...obj
        }
        try {
            delete body.analyticsEventName
            body = {
                ...body,
                userId: userId,
                userAgent: userAgent,
                appVersion: appVersion,
                environment: process.env.APP_ENV,
                createdAt: TimeUtil.getMomentNow("Asia/Kolkata").format("YYYY-MM-DDTHH:mm:ss.SSS") + "Z",
                timeZone: "Asia/Kolkata",
                tenant: appTenant.toString(),
                source: "CF-API-NODE",
                at: at
            }
            if (!body.hasOwnProperty("cityId")) {
                body = {
                    ...body,
                    cityId
                }
            }
            return body
        } catch (e) {
            this.logger.error("TestingCFAnalytics: error in making final body: " + e)
        }
        return null
    }

    private publishEvent(data: BaseAnalyticsEvent, userId: string, userAgent: string, appVersion: number, appTenant: Tenant, at: string, cityId: string): void {
        try {
            const body = this.objectToMap(data, userId, userAgent, appVersion, appTenant, at, cityId)
            if (body == null) {
                return
            }
            if (_.isNil(this.cachedEventList[data.analyticsEventName.toString()])) {
                this.cachedEventList[data.analyticsEventName.toString()] = new Denque<any>()
            }
            this.cachedEventList[data.analyticsEventName.toString()].push(body)
        } catch (e) {
            this.logger.error("Error while sending metabase event, error: ", e)
        }
    }

    public sendEventFromReq(
        data: BaseAnalyticsEvent,
        req: express.Request,
        enableForAlpha: boolean,
        enableForApp: boolean,
        enableForWeb: boolean,
        enableForTV: boolean
    ): void {
        let canPublishEvent: boolean
        const userId = req?.session?.userId ?? "0"
        const userAgent = AuthUtil.getUserAgent(req)
        const appVersion = Number(req.headers["appversion"])
        const appTenant = AppUtil.getTenantFromReq(req)
        const at = req.headers["at"] as string
        if (enableForApp && AppUtil.isMobileAppWithReq(req)) canPublishEvent = true
        else if (enableForWeb && AppUtil.isWebAppWithReq(req)) canPublishEvent = true
        else if (enableForTV && AppUtil.isTVAppWithReq(req)) canPublishEvent = true
        else canPublishEvent = false
        if (canPublishEvent) {
            if (process.env.APP_ENV.toUpperCase() === "ALPHA") {
                canPublishEvent = enableForAlpha
            } else {
                canPublishEvent = (process.env.APP_ENV.toUpperCase() === "PRODUCTION")
            }
        }
        const session: Session = req?.session ?? null
        let cityId: string = req.headers["cityid"] as string
        if (session?.sessionData?.isCityManuallySelected ?? false) {
            cityId = session?.sessionData?.cityId
        }
        if (canPublishEvent) {
            this.publishEvent(data, userId, userAgent, appVersion, appTenant, at, cityId)
        }
    }
    public sendEventFromUserContext(
        data: BaseAnalyticsEvent,
        userContext: UserContext,
        enableForAlpha: boolean,
        enableForApp: boolean,
        enableForWeb: boolean,
        enableForTV: boolean
    ): void {
        let canPublishEvent: boolean
        if (enableForApp && AppUtil.isMobileApp(userContext)) canPublishEvent = true
        else if (enableForWeb && AppUtil.isWeb(userContext)) canPublishEvent = true
        else if (enableForTV && AppUtil.isTVApp(userContext)) canPublishEvent = true
        else canPublishEvent = false
        if (canPublishEvent) {
            if (process.env.APP_ENV.toUpperCase() === "ALPHA") {
                canPublishEvent = enableForAlpha
            } else {
                canPublishEvent = (process.env.APP_ENV.toUpperCase() === "PRODUCTION")
            }
        }
        if (canPublishEvent) {
            this.publishEvent(data, userContext.userProfile.userId,
                userContext.sessionInfo.userAgent,
                userContext.sessionInfo.appVersion,
                AppUtil.getTenantFromUserContext(userContext),
                userContext.sessionInfo.at,
                userContext?.sessionInfo?.sessionData?.cityId)
        }
    }
}