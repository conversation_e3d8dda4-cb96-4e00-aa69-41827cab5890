import * as express from "express"
import { controller, httpGet } from "inversify-express-utils"
import { Container, inject } from "inversify"
import { BASE_TYPES } from "@curefit/base"
import { Tenant } from "@curefit/user-common"
import { FetchUtilV2 } from "@curefit/base"

const fetch = require("node-fetch")

const cleverTapAccountId = "R55-ZZ7-WW5Z"
const cleverTapAccountPassCode = "ETO-ZOX-YSKL"
const cleverTapAccountIdLivefit = "4W9-K46-585Z"
const cleverTapAccountPassCodeLivefit = "ACS-RAY-UIKL"
const cleverTapAccountIdStage = "K55-ZZ7-WW5Z"
const cleverTapAccountPassCodeStage = "CTO-ZOX-YSKL"

export function controllerFactory(kernel: Container) {
    @controller("/cleverTap")
    class CleverTapController {

        constructor(
            @inject(BASE_TYPES.FetchUtilV2) private fetchHelper: FetchUtilV2,
        ) {
        }

        @httpGet("/unsubscribeUserEmail/livefit")
        async unsubscribeUserEmaillivefit(req: express.Request) {
            return this.unSubscribeUser(req, Tenant.LIVEFIT_APP)
        }

        @httpGet("/unsubscribeUserEmail/curefit")
        async unsubscribeUserEmail(req: express.Request) {
            return this.unSubscribeUser(req, Tenant.CUREFIT_APP)
        }

        private async unSubscribeUser(req: express.Request, Tenant: Tenant) {
            const queryParam: string = req.query.queryParam
            const headers = this.getHeader(Tenant)
            const uploadHeader = this.getHeader(Tenant, true)
            const Url = `https://eu1.api.clevertap.com/1/decrypt/?type=email&e=${queryParam}&decode=true`
            const response: any = await fetch(Url, this.fetchHelper.get({ headers: headers }))
            const { email } = await this.fetchHelper.parseResponse<any>(response)
            const body: any = {
                "d": [
                    {
                        "identity": email,
                        "type": "profile",
                        "profileData": {
                            "MSG-email": false
                        }
                    }
                ]
            }
            const UrlUpload = `https://api.clevertap.com/1/upload`
            const responseUpload: any = await fetch(UrlUpload, this.fetchHelper.post({ body: body, headers: uploadHeader }))
            return this.fetchHelper.parseResponse(responseUpload)
        }

        @httpGet("/email/curefit")
        async cleverTapEmail(req: express.Request) {
            return this.getUserEmail(req, Tenant.CUREFIT_APP)
        }

        @httpGet("/email/livefit")
        async cleverTapEmailLivefit(req: express.Request) {
            return this.getUserEmail(req, Tenant.LIVEFIT_APP)
        }

        private async getUserEmail(req: express.Request, Tenant: Tenant) {
            const queryParam: string = req.query.queryParam
            const Url = `https://eu1.api.clevertap.com/1/decrypt/?type=email&e=${queryParam}&decode=true`
            const headers = this.getHeader(Tenant)
            const response: any = await fetch(Url, this.fetchHelper.get({ headers: headers }))
            return this.fetchHelper.parseResponse(response)
        }

        private getHeader(tenant: Tenant, hasContent?: boolean) {
            if (tenant === "curefit") {
                const headers = {
                    "X-CleverTap-Account-Id": process.env.ENVIRONMENT !== "PRODUCTION" ? cleverTapAccountIdStage : cleverTapAccountId,
                    "X-CleverTap-Passcode": process.env.ENVIRONMENT !== "PRODUCTION" ? cleverTapAccountPassCodeStage : cleverTapAccountPassCode,
                }
                const headersWithContent = {
                    ...headers,
                    "Content-Type": "application/json"
                }
                return hasContent ? headersWithContent : headers
            } else if (tenant === "livefit") {
                const headers = {
                    "X-CleverTap-Account-Id": process.env.ENVIRONMENT !== "PRODUCTION" ? cleverTapAccountIdStage : cleverTapAccountIdLivefit,
                    "X-CleverTap-Passcode": process.env.ENVIRONMENT !== "PRODUCTION" ? cleverTapAccountPassCodeStage : cleverTapAccountPassCodeLivefit,
                }
                const headersWithContent = {
                    ...headers,
                    "Content-Type": "application/json"
                }
                return hasContent ? headersWithContent : headers
            }
        }
    }
    return CleverTapController
}

export default controllerFactory
