import { inject, injectable } from "inversify"
import { WidgetView } from "../common/views/WidgetView"
import { IUserService, USER_CLIENT_TYPES } from "@curefit/user-client"
import { EXPRESSION_TYPES, ExpressionBuilderFactory } from "@curefit/expression-utils"
import { IRewardService, REWARD_CLIENT_TYPES } from "@curefit/reward-client"
import { IRiddlerService, RIDDLER_CLIENT_TYPES } from "@curefit/riddler-client"
import { BaseWidget, UserContext } from "@curefit/vm-models"
import { PageWidget } from "../page/Page"
import {
    FitnessReportPageView,
    GraphValue,
    InfoSection,
    ReportGraphWidget,
    ReportType
} from "../cult/fitnessreport/FitnessReportPageView"
import { EnrolmentResponse } from "@curefit/riddler-common"
import { Timezone } from "@curefit/util-common"

@injectable()
class ChallengeReportViewBuilder {

    constructor(
        @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
        @inject(RIDDLER_CLIENT_TYPES.RiddlerService) private riddlerService: IRiddlerService,
        @inject(EXPRESSION_TYPES.ExpressionBuilderFactory) private expressionBuilderFactory: ExpressionBuilderFactory,
        @inject(REWARD_CLIENT_TYPES.IRewardService) private rewardService: IRewardService
    ) {
    }

    public async buildView(userContext: UserContext, enrolment: EnrolmentResponse): Promise<FitnessReportPageView> {
        const reportType: ReportType = "CHALLENGE"
        const tz = userContext.userProfile.timezone
        const widgets: (BaseWidget | WidgetView | PageWidget)[] = []
        const header = {
            pageTitle: "Details"
        }
        const reportWidget = await this.getClassesGraphWidget(enrolment, tz)
        widgets.push(reportWidget)
        return new FitnessReportPageView(widgets, header, reportType)
    }

    private async getClassesGraphWidget(enrolment: EnrolmentResponse, tz: Timezone): Promise<ReportGraphWidget> {

        if (!enrolment.challenge.uiConfig.reportPageDataExpression) {
            return undefined
        }
        const reportData: any = await this.expressionBuilderFactory
            .getBuilder()
            .expression(enrolment.challenge.uiConfig.reportPageDataExpression)
            .context({challenge: enrolment.challenge, enrolment: enrolment, tz: tz})
            .build()
            .eval()
        const footer: InfoSection = reportData["footer"]

        const graphData: GraphValue[] = reportData["graphData"]
        const classesAttendedGraphWidget: ReportGraphWidget = {
            widgetType: "REPORT_GRAPH_WIDGET",
            header: reportData["header"],
            backgroundColor: reportData["backgroundColor"],
            graphData: graphData,
            footer: footer
        }
        classesAttendedGraphWidget["dividerType"] = "LARGE"
        return classesAttendedGraphWidget
    }
}

export default ChallengeReportViewBuilder