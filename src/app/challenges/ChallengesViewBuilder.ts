import { injectable } from "inversify"
import {
    Challenge,
    CultResponse,
    EnrolmentResponse,
    EnrolmentStatus,
    InviteResponse,
    Progress,
    ProgressText
} from "@curefit/riddler-common"
import { UserContext } from "@curefit/userinfo-common"
import { TimeUtil, Timezone } from "@curefit/util-common"
import ActionUtil from "../util/ActionUtil"
import * as _ from "lodash"
import * as momentTz from "moment-timezone"
import { IActiveChallengeWidget } from "../common/views/Social"
import { ExpressionBuilderFactory } from "@curefit/expression-utils"
import { Action } from "@curefit/apps-common"

const fillTemplate = require("es6-dynamic-template")


@injectable()
class ChallengesViewBuilder {
    buildMyChallengesView(enrolments: EnrolmentResponse[], userContext: UserContext): IActiveChallengeWidget[] {
        return enrolments
            .map((enrolment) => {
                const challenge = enrolment.challenge
                return {
                    widgetType: "ACTIVE_CHALLENGE_WIDGET",
                    title: challenge.title,
                    subTitle: ChallengesViewBuilder._getChallengesSubTitle(
                        userContext.userProfile.timezone,
                        ChallengesViewBuilder._getStartDate(enrolment, userContext.userProfile.timezone),
                        enrolment.endDate
                    ),
                    leftSubText: "", // TODO ??
                    rightSubText: "", // TODO ??
                    metric: "", // TODO ??
                    imageUrl: challenge.uiConfig.myChallengesImageUrl,
                    label: this._getLabel(challenge),
                    rank: _.get(enrolment, "standing.position"), // TODO: change to rank
                    totalParticipants: undefined,
                    action: this._getAction(enrolment)
                }
            })
    }

    _getAction(enrolment: EnrolmentResponse): Action {
        if (this.isSquadChallenge(enrolment.challenge)) {
            return {
                actionType: "NAVIGATION",
                url: `curefit://squadchallengepage?challengeId=${enrolment.challengeId}&prevPage=enrolledchallenges`
            }
        }
        return {
            actionType: "NAVIGATION",
            url: ActionUtil.getChallengeDetailsUrl(enrolment.challengeId, "ENROLMENT", enrolment.enrolmentId)
        }
    }

    isSquadChallenge(challenge: Challenge) {
        return !_.isEmpty(challenge.tags) && challenge.tags.includes("squad")
    }

    _getLabel(challenge: Challenge) {
        switch (challenge.challengeType) {
            case "PRIVATE":
                return {
                    text: "EXCLUSIVE",
                    colors: ["#57e1b1", "#83eb95"]
                }
        }
    }

    static _getStartDate(enrolment: EnrolmentResponse, tz: Timezone): Date {
        switch (enrolment.challenge.challengeType) {
            case "PRIVATE":
                return enrolment.startDate
            case "PUBLIC":
            case "TEAM":
                return momentTz(enrolment.challenge.startDate).tz(tz).startOf("day").toDate()
        }
    }

    static _getChallengesSubTitle(tz: Timezone, startDate: Date, endDate: Date): string {
        if (new Date(endDate).getTime < new Date().getTime) {
            return `Ended on ${TimeUtil.formatDateInTimeZone(tz, startDate, "MMMM DD, YYYY")}`
        } else if (new Date(startDate).getTime() > new Date().getTime()) {
            return `Challenge starts on ${TimeUtil.formatDateInTimeZone(tz, startDate, "MMMM DD, YYYY")}`
        }
        return `${TimeUtil.formatDateInTimeZone(tz, startDate, "MMMM DD")} - ${TimeUtil.formatDateInTimeZone(tz, endDate, "MMMM DD")}`
    }

    static _getProgressText(progress: Progress, page: "DETAILS" | "HOME_WIDGET", status: EnrolmentStatus): ProgressText {
        switch (page) {
            case "DETAILS":
                return progress.detailTexts.find(_ => _.status === status)
            case "HOME_WIDGET":
                return progress.homeWidgetTexts.find(_ => _.status === status)
        }
    }

    static _getCultRewardVars(enrolment: EnrolmentResponse, tz: Timezone) {
        const milestone = enrolment.milestones &&
            enrolment.milestones.find(_ => _.rewards && _.rewards.some(_ => _.reward.rewardType === "CULT"))
        const cultReward = milestone && milestone.rewards && milestone.rewards.find(_ => _.reward.rewardType === "CULT")
        const cultResponse = cultReward && cultReward.fulfilmentResponse.data as CultResponse
        const cultEndDate = cultResponse && (cultResponse.endDate || (cultResponse.membership && cultResponse.membership.endDate))
        const formattedDate = cultEndDate && TimeUtil.formatDateStringInTimeZone(cultEndDate, tz, "DD MMM YYYY")
        return {cultEndDate: formattedDate}
    }

    static _getVars(enrolment: EnrolmentResponse, progress: Progress, tz: Timezone, vars: any) {
        const metric = enrolment.metrics.find(_ => _.metricType === progress.metric)
        const completed = metric ? metric.value : 0
        const today = TimeUtil.getMomentNow(tz).startOf("day").toISOString()
        const daysToStart = Math.max(TimeUtil.diffInDaysReal(tz, enrolment.startDate.toISOString(), today), 0)
        const daysToEnd = Math.max(TimeUtil.diffInDaysReal(tz, enrolment.endDate.toISOString(), today), 0)
        const total = progress.goal
        return {
            completed,
            daysToEnd,
            remaining: total - completed,
            total,
            daysToStart,
            ...ChallengesViewBuilder._getCultRewardVars(enrolment, tz),
            ...vars
        }
    }

    static _getProgress(tz: Timezone, enrolment: EnrolmentResponse, page: "DETAILS" | "HOME_WIDGET", exprVars: any) {
        if (_.isEmpty(enrolment.challenge.uiConfig.progress)) {
            return undefined
        } // TODO @rohittjob-cf: hardcoding 1st goal
        const uiConfig = enrolment.challenge.uiConfig
        const progress = uiConfig.progress[0]
        const vars = ChallengesViewBuilder._getVars(enrolment, progress, tz, exprVars)
        const text = ChallengesViewBuilder._getProgressText(progress, page, enrolment.status) // TODO: handle undefined

        return {
            title: text.title && fillTemplate(text.title, vars),
            subTitle: text.subTitle && fillTemplate(text.subTitle, vars),
            data: {
                status: ["ACTIVE", "MISSED", "FORCE_EXIT"].includes(enrolment.status) ?
                    {
                        completed: vars.completed,
                        total: vars.total,
                    } :
                    undefined,
                alt: text.alt && {
                    type: text.alt.type,
                    data: text.alt.data,
                    percentage: text.alt.percentage
                }
            },
            line: page === "DETAILS"
        }
    }

    static async getVariables(expressionBuilderFactory: ExpressionBuilderFactory, refData: InviteResponse | EnrolmentResponse, tz: Timezone): Promise<any> {
        const variables = refData.challenge.uiConfig.variables
        if (_.isEmpty(variables)) {
            return {}
        }

        const vars: any = {enrolment: refData, invite: refData, challenge: refData.challenge}
        for (const variable of variables) {
            vars[variable.name] = await expressionBuilderFactory
                .getBuilder()
                .expression(variable.expression)
                .context({challenge: refData.challenge, enrolment: refData, invite: refData, tz: tz})
                .build()
                .eval()
        }
        return vars
    }
}

export default ChallengesViewBuilder
