import { inject, injectable } from "inversify"
import ChallengesViewBuilder from "./ChallengesViewBuilder"
import {
    Challenge,
    DetailWidgetConfig,
    EnrolmentResponse,
    EntityType,
    Group,
    InviteResponse,
    LeaderBoardUIConfig,
    RankedChallenge,
    Standing,
    WidgetType
} from "@curefit/riddler-common"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { DescriptionWidget } from "../common/views/WidgetView"
import {
    ChallengeState,
    IChallengeActionWidget,
    IChallengeParticipants,
    IChallengeSection,
    ILeaderboardWidget,
    IUserPerformanceWidget,
    IWidgetSection,
    ParticipantType,
    SocialColors
} from "../common/views/Social"
import * as _ from "lodash"
import { User } from "@curefit/user-common"
import { IUserService, USER_CLIENT_TYPES } from "@curefit/user-client"
import * as mustache from "mustache"
import { EXPRESSION_TYPES, ExpressionBuilderFactory } from "@curefit/expression-utils"
import { Badge } from "@curefit/quest-common"
import { IRewardService, REWARD_CLIENT_TYPES } from "@curefit/reward-client"
import { BadgeFulfilmentResponse } from "@curefit/reward-common"
import { UrlPathBuilder } from "@curefit/product-common"
import { getShareText } from "../util/QuestUtils"
import { IRiddlerService, RIDDLER_CLIENT_TYPES } from "@curefit/riddler-client"
import * as momentTz from "moment-timezone"
import { ActionType, UserContext, ISegmentService } from "@curefit/vm-models"
import { RiddlerServiceConfig } from "./ChallengesController"
import { ConfigService, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import { ChallengeScoreWidget, DataInfo } from "@curefit/vm-models/dist/src/models/widgets/ChallengeScoreWidget"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { HamletContext } from "@curefit/hamlet-common"
import { ISocialService, SOCIAL_CLIENT_TYPES } from "@curefit/social-client"
import { CommunityType } from "@curefit/social-common"
import DigitalLeagueChallengeViewBuilder, {
    MAX_NEARBY_SQUADS_TO_SHOW,
    TeamParticipantDetails
} from "../digital/DigitalLeagueChallengeViewBuilder"
import { BASE_TYPES, Logger } from "@curefit/base"
import AppUtil from "../util/AppUtil"
import LiveUtil from "../util/LiveUtil"
import CUREFIT_API_TYPES from "../../config/ioc/types"

const CUSTOM_GOAL_SUPPORT_VERSION = 7.88
const MY_SCORE_SUPPORT_VERSION = 8.26
const MY_SCORE_WITH_PROGRESS_BAR_SUPPORT_VERSION = 8.35


type UserMap = { [userId: string]: User }
type GroupMap = { [groupId: string]: Group }
export interface ParticipantDetails {
    userMap?: UserMap
    groupMap?: GroupMap
}

@injectable()
class ChallengeDetailsViewBuilder {

    constructor(
        @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
        @inject(RIDDLER_CLIENT_TYPES.RiddlerService) private riddlerService: IRiddlerService,
        @inject(PAGE_CONFIG_TYPES.ConfigService) private configService: ConfigService,
        @inject(EXPRESSION_TYPES.ExpressionBuilderFactory) private expressionBuilderFactory: ExpressionBuilderFactory,
        @inject(REWARD_CLIENT_TYPES.IRewardService) private rewardService: IRewardService,
        @inject(HAMLET_TYPES.HamletBusiness)  private hamletBusiness: HamletBusiness,
        @inject(SOCIAL_CLIENT_TYPES.SocialService) private socialService: ISocialService,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService
    ) {
    }

    public async buildChallengeDetailsView(reference: EntityType, refData: EnrolmentResponse | InviteResponse, tz: Timezone, userContext: UserContext) {
        const {challenge} = refData
        const sectionHeader = {
            widgetType: "CHALLENGE_SUMMARY",
            challengeId: challenge.challengeId,
            imageUrl: challenge.uiConfig.bannerUrl,
            title: challenge.title
        }

        const participantDetails: ParticipantDetails = await this._getParticipantDetails(reference, refData as EnrolmentResponse)
        const vars = reference === "ENROLMENT" ? await ChallengesViewBuilder.getVariables(this.expressionBuilderFactory, refData, tz) : {}

        const widgetConfig = this.getDetailWidgetConfig(reference, refData)
        let sections = await this.constructWidgets(widgetConfig, reference, refData, tz, userContext.sessionInfo.appVersion, participantDetails, vars)
        if (!_.isNil(sections)) {
            for (const section of sections) {
                if (!_.isNil(section) && section.hasOwnProperty("data") && !_.isNil(section.data)) {
                    for (const data of section.data) {
                        if (data.widgetType === "CHALLENGE_INFO_WIDGET") {
                            sectionHeader.title = undefined
                            break
                        }
                    }
                }
            }
        }
        if (_.isNil(sections)) {
            sections = [
                await this._getChallengePerformanceSection(reference, refData as EnrolmentResponse, participantDetails.userMap, vars),
                this._getGoalWidget(reference, refData, tz, userContext.sessionInfo.appVersion, vars),
                this._getProgress(tz, refData as EnrolmentResponse, reference, vars),
                this._getRewardsWidget(challenge),
                this._getHowItWorksWidget(challenge),
                this._getLeaderboardWidget(reference, refData as EnrolmentResponse, participantDetails, false, false, userContext.sessionInfo.appVersion),
                this._getDescriptionWidget(challenge),
                this._getEndedGoalWidget(reference, refData, tz, userContext.sessionInfo.appVersion, vars),
                this._getLeaveChallengeSection(reference, refData)
            ]
        }

        return {
            sectionHeader,
            sections: _.compact(sections || []),
            actionLayout: "vertical",
            actions: await  this._getActions(reference, refData, tz, widgetConfig, userContext)
        }
    }


    private _getBannerWidget(refData: EnrolmentResponse | InviteResponse) {
        const bannerConf = refData.challenge.uiConfig.bannerWidget
        return {
            data: [{
                widgetType: "BANNER_CAROUSEL_WIDGET",
                maxNumBanners: 1,
                edgeToEdge: true,
                bannerRatio: "990:213",
                backgroundColor: "",
                showDivider: false,
                data: [
                    {
                        action: bannerConf.action,
                        image: bannerConf.bgImage,
                    }
                ],
                layoutProps: {
                    "showPagination": false,
                    "v2": false,
                    "alignment": "center",
                    "backgroundColor": "",
                    "autoScroll": false,
                    "enableSnap": false,
                    "useShadow": false,
                    "roundedCorners": true,
                    "noVerticalPadding": false,
                    "edgeToEdge": false,
                    "interContentSpacing": 0,
                    "bannerOriginalWidth": 990,
                    "bannerOriginalHeight": 213,
                    "bannerWidth": 990,
                    "bannerHeight": 213,
                    "containerStyle": { paddingBottom: 30, paddingTop: 5 },
                },
            }]
        }
    }

    private _getCompletedBannerWidget(reference: EntityType, refData: EnrolmentResponse | InviteResponse) {
        if (reference == "ENROLMENT" && ((refData as EnrolmentResponse).status) == "COMPLETED") {
            return {
                data: [{
                    widgetType: "BANNER_CAROUSEL_WIDGET",
                    maxNumBanners: 1,
                    edgeToEdge: true,
                    bannerRatio: "335:281",
                    backgroundColor: "",
                    showDivider: false,
                    data: [
                        {
                            image: "image/challenges/unpauselife/challenge_complete_widget.png", // temporary image - needs to come from Riddler
                        }
                    ],
                    layoutProps: {
                        "showPagination": false,
                        "v2": false,
                        "alignment": "center",
                        "backgroundColor": "",
                        "autoScroll": false,
                        "enableSnap": false,
                        "useShadow": false,
                        "roundedCorners": true,
                        "noVerticalPadding": false,
                        "edgeToEdge": false,
                        "interContentSpacing": 0,
                        "bannerOriginalWidth": 335,
                        "bannerOriginalHeight": 281,
                        "bannerWidth": 335,
                        "bannerHeight": 281,
                        "containerStyle": { paddingBottom: 30, paddingTop: 5 },
                    },
                }]
            }
        }
    }

    private _getChallengeInfo(reference: EntityType, refData: EnrolmentResponse | InviteResponse, tz: Timezone, appVersion: number, vars: any): any {
        if (appVersion < MY_SCORE_SUPPORT_VERSION) {  // show old widgets to users on older installs
            return this._getGoalWidget(reference, refData, tz, appVersion, vars, true)
        }
        let challengeEndDate
        let challengeStartDate
        let enrolmentStatus

        if (reference === "ENROLMENT") {
            challengeStartDate = (refData as EnrolmentResponse).startDate
            challengeEndDate = (refData as EnrolmentResponse).endDate
            enrolmentStatus = (refData as EnrolmentResponse).status
        } else if (reference === "INVITE") {
            challengeStartDate = momentTz(refData.challenge.startDate).tz(tz).startOf("day").toDate()
            challengeEndDate = momentTz(refData.challenge.endDate).tz(tz).endOf("day").toDate()
        }

        const dateNow = TimeUtil.getDateNow(tz)
        let challengeStatus
        if (challengeStartDate > dateNow) {
            challengeStatus = "UPCOMING"
        } else if (dateNow >= challengeStartDate && dateNow <= challengeEndDate) {
            challengeStatus = "ONGOING"
        } else if (enrolmentStatus && enrolmentStatus == "COMPLETED") {
            challengeStatus = "COMPLETED"
        } else {
            challengeStatus = "ENDED"
        }

        let info = reference === "INVITE" ? refData.challenge.uiConfig.inviteGoalText || refData.challenge.uiConfig.goalText : refData.challenge.uiConfig.goalText

        // Temporary hack for fitstart challenge
        if (refData.challenge.title == "FITSTART Challenge" && challengeStatus && challengeStatus == "COMPLETED") {
            info = "Your 2 months extension will be credited by Feb 15"
        }

        return {
            data: [{
                widgetType: "CHALLENGE_INFO_WIDGET",
                status: {
                    data: [
                        {
                            text: challengeStatus, // from challenge status
                            style: {
                                backgroundColor: challengeStatus === "ONGOING" || challengeStatus === "UPCOMING" || challengeStatus === "COMPLETED" ? "#4ab74a" : "#b32d00",
                                color: "#fff",
                                fontSize: 12,
                                fontFamily: "BrandonText-Bold",
                            }
                        }
                    ],
                    style: {
                        backgroundColor: challengeStatus === "ONGOING" || challengeStatus === "UPCOMING" || challengeStatus === "COMPLETED" ? "#4ab74a" : "#b32d00",
                        alignSelf: "flex-start",
                        borderRadius: 3,
                        paddingVertical: 3,
                        paddingHorizontal: 6
                    }
                },
                name: refData.challenge.title, // from challenge
                timeLine: _.isNil(challengeStartDate) || _.isNil(challengeEndDate) ? "" : `${TimeUtil.formatDateInTimeZone(tz, challengeStartDate, "DD MMMM YYYY")} - ${TimeUtil.formatDateInTimeZone(tz, challengeEndDate, "DD MMMM YYYY")}`,
                info: mustache.render(info, vars)  // from ui config
            }
            ]
        }
    }


    private _getGoalWidget(reference: EntityType, refData: EnrolmentResponse | InviteResponse, tz: Timezone, appVersion: number, vars: any, override: boolean = false): any {
        if (!override && reference === "ENROLMENT" && refData.challenge.challengeType === "PUBLIC" && refData.status === "EXPIRED") {
            return undefined
        }
        const list: { icon: string; value: string; style?: any }[] = [{
            icon: appVersion < CUSTOM_GOAL_SUPPORT_VERSION ? "GOAL" : "https://cdn-images.cure.fit/www-curefit-com/image/upload/image/challenges/reward_badge.png",
            value: mustache.render(reference === "INVITE" ? refData.challenge.uiConfig.inviteGoalText || refData.challenge.uiConfig.goalText : refData.challenge.uiConfig.goalText, vars),
            style: appVersion < CUSTOM_GOAL_SUPPORT_VERSION ? undefined : {
                icon: {
                    height: 31,
                    width: 40
                }
            }
        }]

        if (reference === "ENROLMENT") {
            const enrolment = refData as EnrolmentResponse
            list.push({
                icon: "DATE",
                value: ChallengesViewBuilder._getChallengesSubTitle(tz, ChallengesViewBuilder._getStartDate(enrolment, tz), enrolment.endDate)
            })
        }
        return {
            data: [{
                widgetType: "GOAL_WIDGET",
                list
            }]
        }
    }


    private _getHowItWorksWidget(challenge: Challenge) {
        return {
            data: [
                {
                    widgetType: "PRODUCT_LIST_WIDGET",
                    type: "SMALL",
                    hideSepratorLines: true,
                    header: {
                        title: challenge.uiConfig.hiws.title || "HOW IT WORKS"
                    },
                    items: challenge.uiConfig.hiws.items.map(item => {
                        return {icon: item.icon, subTitle: item.description}
                    })
                }
            ]
        }
    }

    private _getRewardsWidget(challenge: Challenge) {
        const rewardsInfo = challenge.uiConfig.rewards
        if (_.isEmpty(rewardsInfo.items)) {
            return undefined
        }
        return {
            data: [
                {
                    widgetType: "REWARDS_WIDGET",
                    title: challenge.uiConfig.rewards.title || "REWARDS",
                    rewards: challenge.uiConfig.rewards.items.map(item => {
                        return {
                            badge: {
                                name: "",
                                imageUrl: item.icon
                            },
                            title: item.title,
                            subTitle: "",
                            description: item.description,
                            bgColor: item.bgColor
                        }
                    })
                }
            ]
        }
    }

    private _getDescriptionWidget(challenge: Challenge) {
        return {
            data: [
                new DescriptionWidget(
                    [{title: challenge.uiConfig.description.title, subTitle: challenge.uiConfig.description.text}],
                    {
                        tncLinkTitle: challenge.uiConfig.tncs.tncLinkTitle,
                        tncModalTitle: challenge.uiConfig.tncs.tncModalTitle
                    },
                    !_.isEmpty(challenge.uiConfig.tncs.tncs) && challenge.uiConfig.tncs.tncs
                )
            ]
        }
    }

    private async _getActions(reference: EntityType, refData: EnrolmentResponse | InviteResponse, tz: Timezone, config: DetailWidgetConfig, userContext: UserContext) {
        switch (reference) {
            case "ENROLMENT": {
                const enrolment = refData as EnrolmentResponse
                return await Promise.all(((config && config.actions) || (enrolment.status !== "ACTIVE" && refData.challenge.uiConfig.detailActions) || []).map(async action => {
                    if (action.url === "curefit://hometab?pageId=hometab&widgetId=44d2936a-f11a-4876-89d5-1b0d384bc9a8") {
                        return {
                            actionType: action.actionType,
                            title: "",
                            leftInfo: {
                                title: action.title,
                                subTitle: "Navigate to Levels in Activity and records",
                            },
                            rightInfo: {
                               title: "",
                            },
                            // Temp hack since riddler config does not support segment based action building
                            url: "curefit://accountview",
                            meta: {
                                textColor: SocialColors.reddishPink,
                                challengeId: refData.challenge.challengeId
                            }
                        }
                    }
                    return {
                        actionType: action.actionType,
                        title: action.title,
                        // Temp hack since riddler config does not support segment based action building
                        url: action.url.includes("pageId=CultAtHome") ? await LiveUtil.getLivePageDeepLink(this.segmentService, userContext) : action.url,
                        meta: {
                            textColor: SocialColors.reddishPink,
                            challengeId: refData.challenge.challengeId
                        }
                    }
                    }
                ))
            }
            case "INVITE": {
                const invite = refData as InviteResponse
                switch (invite.inviteType) {
                    case "CUSTOM": {
                        return [
                            {
                                actionType: "SHOW_DATETIME_PICKER_MODAL",
                                title: "Pick a Start Date",
                                selectedOption: 0,
                                meta: {
                                    maximumDate: TimeUtil.addDays(tz, new Date().toISOString(), invite.challenge.uiConfig.customDateMaxDelay),
                                    pickDateAction: {
                                        actionType: "ACCEPT_INVITE",
                                        challengeId: invite.challengeId,
                                        inviteId: invite.inviteId
                                    }
                                }
                            }
                        ]
                    }
                    case "PLEDGE": {
                        const badgeInfo = []
                        const riddlerServiceConfig: RiddlerServiceConfig = this.configService.getConfig("RIDDLER")
                        if (riddlerServiceConfig && riddlerServiceConfig.configs && riddlerServiceConfig.configs.pledgeChallengeConfig) {
                            const pledgeConfig = riddlerServiceConfig.configs.pledgeChallengeConfig
                            for (const conf of pledgeConfig.daysChallengeMapping) {
                                badgeInfo.push({
                                    badgeTitle: conf.badgeTitle || pledgeConfig.defaultBadgeTitle,
                                    badgeImageUrl: conf.badgeImageUrl || pledgeConfig.defaultBadgeImageUrl,
                                    days: conf.days,
                                    week: conf.weeks
                                })
                            }
                        }
                        let bucketId: string = "3"
                        try {
                            const hamletContext: HamletContext = {
                                userId: userContext.userProfile.userId,
                                deviceId: null,
                                experimentIds: ["171"], // owner "<EMAIL>"
                                tenant: AppUtil.getTenantFromUserContext(userContext)
                            }
                            bucketId = undefined
                            this.logger.info(`Bucket id for user ${userContext.userProfile.userId} is ${bucketId}`)
                        } catch (e) {
                            this.logger.error(`Error while creating pledge challenge action ${JSON.stringify(e)}`)
                        }
                        if (bucketId === "3") {
                            return [{
                                actionType: "SHOW_PLEDGE_CHALLENGE_MODAL",
                                title: "Select",
                                meta: {
                                    shouldShowPledgeModal: true,
                                    type: "PICK",
                                    isOneWeek: true,
                                    BadgeINfo: badgeInfo,
                                    title: "Give it a strong start with a pledge to be consistent in the next 7 days!",
                                    action: {
                                        actionType: "UPDATE_PLEDGE_CHALLENGE",
                                        title: "TAKE PLEDGE",
                                        url: `/challenge/pledge`
                                    },
                                    daysInfo: {
                                        title: "Choose the number of workout days",
                                        defaultDays: 3,
                                        totalDays: 6
                                    },
                                    weeksInfo: {
                                        title: "Select number of weeks",
                                        defaultWeeks: 1,
                                        totalWeeks: 6
                                    }
                                }
                            }]
                        } else if (bucketId === "1") {
                            return [{
                                actionType: "SHOW_PLEDGE_CHALLENGE_MODAL",
                                title: "Select",
                                meta: {
                                    shouldShowPledgeModal: true,
                                    type: "PICK",
                                    isOneWeek: false,
                                    BadgeINfo: badgeInfo,
                                    title: "Pledge for consistent workouts for few weeks to kickstart this habit",
                                    action: {
                                        actionType: "UPDATE_PLEDGE_CHALLENGE",
                                        title: "TAKE PLEDGE",
                                        url: `/challenge/pledge`
                                    },
                                    daysInfo: {
                                        title: "Select workout days per week",
                                        defaultDays: 3,
                                        totalDays: 6
                                    },
                                    weeksInfo: {
                                        title: "Select number of weeks",
                                        defautWeeks: 3,
                                        totalWeeks: 6
                                    }
                                }
                            }]
                        } else {
                            return []
                        }
                    }
                    case "DEFAULT": { // TODO: could be configurized
                        return [
                            {
                                actionType: "ACCEPT_INVITE_AND_JOIN",
                                title: "Enter Challenge",
                                meta: {
                                    challengeId: invite.challengeId,
                                    inviteId: invite.inviteId
                                }
                            }
                        ]
                    }
                }
            }
        }
    }

    _getLeaveChallengeSection(reference: EntityType, refData: EnrolmentResponse | InviteResponse, override: boolean = false): IWidgetSection {
        if (!override && reference === "INVITE" || refData.status !== "ACTIVE") {
            return undefined
        }
        const leaveChallengeWidget: IChallengeActionWidget = {
            widgetType: "CHALLENGE_ACTION_WIDGET",
            action: {
                title: "Leave Challenge",
                actionType: "SHOW_ALERT_MODAL",
                meta: {
                    title: "Don’t want to complete the challenge?",
                    subTitle: "Once you leave, you cannot rejoin the challenge. Are you sure you want to leave?",
                    actions: [{
                        actionType: "HIDE_ALERT_MODAL",
                        title: "CANCEL"
                    }, {
                        actionType: "LEAVE_CHALLENGE",
                        title: "I'M SURE",
                        meta: {
                            textColor: SocialColors.purple,
                            enrolmentId: (refData as EnrolmentResponse).enrolmentId
                        }
                    }]
                }
            }
        }
        return {
            data: [leaveChallengeWidget]
        }
    }

    _getProgress(tz: Timezone, refData: EnrolmentResponse, reference: EntityType, vars: any, override: boolean = false): any {
        if (!override && reference === "INVITE") {
            return undefined
        }
        const progress = ChallengesViewBuilder._getProgress(tz, refData, "DETAILS", vars)
        if (_.isNil(progress)) {
            return undefined
        }
        return {
            data: [{
                widgetType: "CHALLENGE_PROGRESS",
                progress: progress
            }]
        }
    }

    private getDefaultRanking(enrolment: EnrolmentResponse): {rank?: number, score?: number} {
        if (_.isEmpty(enrolment.challengeStandings)) {
            return {}
        }
        const defaultLeaderboardConfigId = (enrolment.challenge as RankedChallenge).defaultLeaderboardConfigId || "DEFAULT"
        // get the  standing in this leaderboard
        const standings = enrolment.challengeStandings.find(_ => _.leaderboardId.split("::")[0] === defaultLeaderboardConfigId)
        return {rank: standings.standings.rank, score: standings.standings.score}
    }


    private _getMyScoreWidget(reference: EntityType, enrolment: EnrolmentResponse, userMap: UserMap, appVersion: number, vars: any) {
        if (appVersion < MY_SCORE_SUPPORT_VERSION) {
            return this._getChallengePerformanceSection(reference, enrolment, userMap, vars, true)
        }
        if (reference === "INVITE" ) {
            return undefined
        }
        const user = userMap[enrolment.userId]
        const uiConfig = enrolment.challenge.uiConfig
        const {rank, score} = this.getDefaultRanking(enrolment)
        if (appVersion < MY_SCORE_WITH_PROGRESS_BAR_SUPPORT_VERSION) {
            return {
                "data": [{
                    "widgetType": "CARD_LIST_WIDGET",
                    "hasDivideBelow": false,
                    "title": "My Score",
                    "contentType": "CHALLENGE_INFO",
                    "data": [{
                        "title": `${(user.firstName || "Curefit User").trim()} ${(user.lastName || "").trim()}`, // from user info
                        "subTitle": mustache.render(uiConfig.challengePerformance.description, {
                            rank: rank,
                            score: score,
                            ...vars
                        }),  // stored in ui with variables
                        "viewType": "CHALLENGE_INFO",
                        "showDivider": false,
                        "leftInfo": {
                            "images": [
                                user.profilePictureUrl  // user profile pic
                            ]
                        }
                    }],
                    "maxItemsToDisplay": 3
                }]
            }
        }

        const widget = new ChallengeScoreWidget()
        widget.widgetType = "CHALLENGE_SCORE_WIDGET"
        widget.hasDividerBelow = true
        widget.title = "My Score"
        widget.contentType = "CHALLENGE_INFO"
        widget.data = []
        widget.data.push({
            hasDividerBelow: true,
            title: `${(user.firstName || "Curefit User").trim()} ${(user.lastName || "").trim()}`, // from user info
            subTitle: mustache.render(uiConfig.challengePerformance.description, {
                rank: rank,
                score: score,
                ...vars
            }),  // stored in ui with variables
            viewType: "CHALLENGE_INFO",
            leftInfo: {
                images: [
                    user.profilePictureUrl  // user profile pic
                ]
            }
        })

        // create progress widget
        if (uiConfig.progressConfig && !_.isEmpty(uiConfig.progressConfig.progressViewInfo)) {
            const progressView = uiConfig.progressConfig.progressViewInfo.find(_ => _.status === enrolment.status)
            if (progressView) {
                // create the progress bar
                const progressDataInfo: DataInfo = {
                    viewType: "CHALLENGE_PROGRESS"
                }
                progressDataInfo.title = mustache.render(progressView.title, vars)
                progressDataInfo.hasDividerBelow = false
                progressDataInfo.progressBar = {
                    showFlag: true,
                    rightText: mustache.render(progressView.progressBar.barText, vars), // {{{metrics::("WEEK_STREAK")}}} / 5 weeks
                    total: Number(mustache.render(uiConfig.progressConfig.goal, vars)), // 5
                    completed: Number(mustache.render(uiConfig.progressConfig.currentProgress, vars)), // {{{metrics::("WEEK_STREAK")}}}
                    type: "CHALLENGE",
                    noPadding: false,
                    progressBarColor: progressView.progressBar.barColor,
                    progressBarTextStyle: {fontFamily: "BrandonText-Bold"}
                }
                widget.data.push(progressDataInfo)

                // create subprogress view
                if (progressView.subProgress && !_.isEmpty(progressView.subProgress)) {
                    for (const subProgress of progressView.subProgress) {
                        const subProgressDataInfo: DataInfo = {
                            viewType: "CHALLENGE_SUB_PROGRESS"
                        }
                        const subProgessScore = Number(mustache.render(subProgress.scoreExpression, vars))
                        const subProgessGoal = subProgress.goal ? Number(mustache.render(subProgress.goal, vars)) : 0
                        subProgressDataInfo.leftInfo = {
                            icon: "FLAG",
                            isGreyScale: subProgessScore < subProgessGoal ? true : false,
                            title: mustache.render(subProgress.leftInfo, vars)
                        }
                        subProgressDataInfo.rightInfo = {
                            color: subProgessScore < subProgessGoal ?  "#ffb400" : "#4ab74a",
                            title: mustache.render(subProgress.rightInfo, vars),
                            number: subProgessScore
                        }
                        subProgressDataInfo.hasDividerBelow = false
                        widget.data.push(subProgressDataInfo)
                    }
                }
            }
        }

        for (let i = 1; i < widget.data.length ; i++) {
            widget.data[i].hasDividerBelow = true
        }

        // add action
        if (uiConfig.challengePerformance.action && widget.data.length > 1) {
            widget.data[widget.data.length - 1].action = {
                url: uiConfig.challengePerformance.action.url,
                actionType: uiConfig.challengePerformance.action.actionType as ActionType,
                title: uiConfig.challengePerformance.action.title || "VIEW DETAILS",
                meta: {
                    enrolmentId: enrolment.enrolmentId
                }
            }
        }
        widget.maxItemsToDisplay = 3
        return {
            "data": [widget]
        }
    }

    async _getChallengePerformanceSection(reference: EntityType, enrolment: EnrolmentResponse, userMap: UserMap, vars: any, override: boolean = false): Promise<{ data: [IUserPerformanceWidget] }> {
        if (!override && (reference === "INVITE" || enrolment.challenge.challengeType !== "PUBLIC" || enrolment.status !== "EXPIRED")) {
            return undefined
        }
        const user = userMap[enrolment.userId]
        const uiConfig = enrolment.challenge.uiConfig
        let badge: Badge
        if (!_.isEmpty(enrolment.milestones)) {
            for (const milestone of enrolment.milestones) {
                const badgeReward = milestone.rewards && milestone.rewards.find(r => r.rewardType === "BADGE")
                if (badgeReward) {
                    const ledgerEntry = await this.rewardService.getReward<BadgeFulfilmentResponse>(enrolment.userId, badgeReward.rewardId)
                    badge = ledgerEntry.data && ledgerEntry.data.fulfilmentResponse.data.badge
                }
            }

        }
        // get the default global leaderboard
        const {rank, score} = this.getDefaultRanking(enrolment)
        return {
            data: [{
                widgetType: "CHALLENGE_PERFORMANCE",
                title: badge ? "Congratulations!" : "",
                userDetails: {
                    name: `${(user.firstName || "Curefit User").trim()} ${(user.lastName || "").trim()}`,
                    profileImageUrl: user.profilePictureUrl,
                    result: mustache.render(uiConfig.challengePerformance.description, {
                        rank: rank,
                        score: score,
                        ...vars
                    }),
                    rank: rank,
                    score: score
                },
                badge: badge && {
                    ...badge,
                    url: UrlPathBuilder.getBadgeImagePath(badge.badgeId),
                    largeImgUrl: UrlPathBuilder.getBadgeImagePath(badge.badgeId, true),
                    shareImgUrl: UrlPathBuilder.getShareImageUrl(badge.badgeId, badge.name),
                    shareText: getShareText(badge.vertical, badge.type),
                    isAchieved: true
                },
                badgeUrl: badge && UrlPathBuilder.getBadgeImagePath(badge.badgeId)
            }]
        }
    }

    _getDefaultLearboardConfig(enrolment: EnrolmentResponse): LeaderBoardUIConfig {
        return {
            displayText: "Global",
            leaderBoardConfigId: "DEFAULT",
            scoreText: enrolment.challenge.uiConfig.scoreText
        }
    }

    _getLeaderboardWidget(reference: EntityType, enrolment: EnrolmentResponse, participantDetails: ParticipantDetails, override: boolean = false, sendChallengeId: boolean = false, appVersion?: number): IWidgetSection {
        if (!override && reference === "INVITE" || !(enrolment.challenge.challengeType === "PUBLIC" || (enrolment.challenge.challengeType === "TEAM"))) {
            return undefined
        }
        const lists: { [leaderboardId: string]: IChallengeParticipants[] } = {}
        const sections: Array<IChallengeSection> = []
        if (!_.isEmpty(enrolment.challengeStandings)) {
            const standingsMap = _.keyBy(enrolment.challengeStandings, s => s.leaderboardId.split("::")[0])
            let headerHack = false
            if (enrolment.challengeStandings.length > 1 && (!appVersion || appVersion < 8.64)) {
                headerHack = true
            }
            for (const leaderboardUiConfig of enrolment.challenge.uiConfig.leaderboardUIConfigs || [this._getDefaultLearboardConfig(enrolment)]) {
                const standings = standingsMap[leaderboardUiConfig.leaderBoardConfigId]
                lists[standings.leaderboardId] = this._getLeaderboardParticipantList(enrolment.userId, enrolment.challenge, standings.nearbyStandings, leaderboardUiConfig.scoreText, participantDetails, false, enrolment.groupEnrolments?.[0].groupId)
                if (headerHack) {
                    lists[standings.leaderboardId].unshift({
                        rank: " ",
                        highlight: false,
                        name: "Name",
                        showImage: false,
                        imageUrl: undefined,
                        score: "",
                        value: "Score",
                        type: ParticipantType.GLOBAL,
                        progress: undefined
                    })
                }
                sections.push({
                    id: standings.leaderboardId,
                    name: leaderboardUiConfig.displayText,
                    total: "MORE" // challengeDetails.totalEnrollments
                })
            }
        }
        if (_.isEmpty(sections) || _.isEmpty(lists)) {
            return undefined
        }
        return {
            data: [{
                widgetType: "LEADERBOARD_WIDGET",
                challengeState: ChallengeState.COMPLETED, // TODO: check where this is used
                // leaderboardId: undefined,
                sections: sections,
                lists,
                challengeId: sendChallengeId ? enrolment.challengeId : undefined
            } as ILeaderboardWidget]
        }
    }

    _getLeaderboardParticipantList(userId: string, challenge: Challenge, standings: Standing[], scoreText: string, participantDetails: ParticipantDetails | TeamParticipantDetails, isLeaderBoardPage = false, groupId?: string): IChallengeParticipants[] {
        const isSquadTeamChallenge = DigitalLeagueChallengeViewBuilder.isSquadTeamChallenge(challenge)
        if (scoreText) {
            const list = standings.map(standing => {
                let name = ""
                if (standing.userId) {
                    name = `${participantDetails.userMap[standing.userId].firstName || "Curefit User"} ${(participantDetails.userMap[standing.userId].lastName || "").trim()}`
                } else if (standing.groupId) {
                    name = participantDetails.groupMap[standing.groupId].name
                }
                let userMapIndex: string
                if (isSquadTeamChallenge) {
                    userMapIndex = (participantDetails as TeamParticipantDetails).communityIdVsUserId?.[DigitalLeagueChallengeViewBuilder.communityIdFromGroupId(standing.groupId)]
                } else {
                    userMapIndex = standing.userId
                }
                const imageUrl = userMapIndex ? participantDetails.userMap[userMapIndex]?.profilePictureUrl : undefined
                return {
                    rank: standing.rank,
                    highlight: standing.userId && standing.userId === userId,
                    name: name,
                    showImage: !!userMapIndex,
                    imageUrl,
                    score: standing.score.toString(),
                    value: mustache.render(scoreText, {score: standing.score}),
                    type: ParticipantType.GLOBAL,
                    progress: undefined
                }
            })
            if (isSquadTeamChallenge && !isLeaderBoardPage) {
                const currentUserIndex = standings.findIndex(standing => standing.groupId === groupId)
                const lowIndex = Math.max(0, currentUserIndex - MAX_NEARBY_SQUADS_TO_SHOW)
                return list.slice(lowIndex, lowIndex + 2 * MAX_NEARBY_SQUADS_TO_SHOW + 1)
            }
            return list
        }
    }

    _getEndedGoalWidget(reference: EntityType, refData: EnrolmentResponse | InviteResponse, tz: Timezone, appVersion: number, vars: any) {
        if (reference === "ENROLMENT" && refData.challenge.challengeType === "PUBLIC" && refData.status === "EXPIRED") {
            return this._getGoalWidget(reference, refData, tz, appVersion, vars, true)
        }
        return undefined
    }

    async _getParticipantDetails(reference: EntityType, enrolment: EnrolmentResponse): Promise<ParticipantDetails | TeamParticipantDetails> {
        if (reference !== "ENROLMENT") {
            return { userMap: {}, groupMap: {}}
        }
        const isSquadTeamChallenge = DigitalLeagueChallengeViewBuilder.isSquadTeamChallenge(enrolment.challenge)
        const userIds: string[] = []
        const groupIds: string[] = []
        if (!_.isEmpty(enrolment.challengeStandings)) {
            for (const challengeStanding of enrolment.challengeStandings) {
                if (!isSquadTeamChallenge) {
                    userIds.push(...challengeStanding.nearbyStandings.map(_ => _.userId))
                }
                groupIds.push(..._.compact(challengeStanding.nearbyStandings.map(_ => _.groupId)))
            }
        }
        const groupIdVsUserId: {[groupId: string]: string} = {}
        if (isSquadTeamChallenge) {
            const participantDetails = await this.getTeamParticipantDetailsForSquads(groupIds)
            userIds.push(...participantDetails.userIds)
            Object.assign(groupIdVsUserId, participantDetails.communityIdVsUserId)
        }
        if (_.isEmpty(userIds)) {
            userIds.push(enrolment.userId)
        }
        const { groups } = await this.riddlerService.getGroups(_.uniq(_.compact(groupIds)))
        return {
            groupMap: _.keyBy(groups, "groupId"),
            userMap: _.keyBy(await this.userService.getUsers(_.uniq(_.compact(userIds))), "id"),
            communityIdVsUserId: groupIdVsUserId
        }
    }

    private async constructWidgets(config: DetailWidgetConfig, reference: EntityType, refData: EnrolmentResponse | InviteResponse, tz: Timezone, appVersion: number, participantDetails: ParticipantDetails, vars: any): Promise<any[]> {
        if (_.isNil(config)) {
            return undefined
        }
        return await Promise.all(config.widgets.map(async type => await this._getWidget(type, reference, refData, tz, appVersion, participantDetails, vars)))
    }

    private getDetailWidgetConfig(reference: EntityType, refData: EnrolmentResponse | InviteResponse): DetailWidgetConfig {
        const configs = refData.challenge.uiConfig.detailWidgetConfigs
        if (_.isEmpty(configs)) {
            return undefined
        }
        return configs.find(config => config.refType === reference && config.statuses.includes(refData.status as any))
    }

    async getTeamParticipantDetailsForSquads(groupIds: string[]) {
        const userIds: string[] = []
        const communityIdVsUserId: {[groupId: string]: string} = {}
        const {elements} = await this.socialService.getCommunitiesByIdBulk(groupIds.map(gId => DigitalLeagueChallengeViewBuilder.communityIdFromGroupId(gId)), CommunityType.LEAGUE)
        if (!_.isEmpty(elements)) {
            for (const communityEntry of elements) {
                communityIdVsUserId[communityEntry?.id] = communityEntry?.creatorNode?.entityId
            }
        }
        userIds.push(...elements.map(_ => _.creatorNode?.entityId))
        return {userIds, communityIdVsUserId: communityIdVsUserId}
    }

    async _getWidget(widgetType: WidgetType, reference: EntityType, refData: EnrolmentResponse | InviteResponse, tz: Timezone, appVersion: number, participantDetails: ParticipantDetails, vars: any) {
        const {challenge} = refData
        switch (widgetType) {
            case "GOAL_WIDGET":
                return this._getGoalWidget(reference, refData, tz, appVersion, vars, true)
            case "CHALLENGE_INFO_WIDGET":
                return this._getChallengeInfo(reference, refData, tz, appVersion, vars)
            case "MY_SCORE_WIDGET":
                return this._getMyScoreWidget(reference, refData as EnrolmentResponse, participantDetails.userMap, appVersion, vars)
            case "CHALLENGE_PROGRESS":
                return this._getProgress(tz, refData as EnrolmentResponse, reference, vars, true)
            case "REWARDS_WIDGET":
                return this._getRewardsWidget(challenge)
            case "HOW_IT_WORKS_WIDGET":
                return this._getHowItWorksWidget(challenge)
            case "LEADERBOARD_WIDGET":
                return this._getLeaderboardWidget(reference, refData as EnrolmentResponse, participantDetails, true, false, appVersion)
            case "DESCRIPTION_WIDGET":
                return this._getDescriptionWidget(challenge)
            case "CHALLENGE_PERFORMANCE":
                return await this._getChallengePerformanceSection(reference, refData as EnrolmentResponse, participantDetails.userMap, vars, true)
            case "LEAVE_CHALLENGE":
                return this._getLeaveChallengeSection(reference, refData)
            case "BANNER_CAROUSEL_WIDGET":
                return this._getBannerWidget(refData)
            case "BANNER_WIDGET":
                return this._getCompletedBannerWidget(reference, refData)
        }

    }

}

export default ChallengeDetailsViewBuilder
