import { Container, inject } from "inversify"
import { Request } from "express"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import AuthMiddleware from "../auth/AuthMiddleware"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { IRiddlerService, RIDDLER_CLIENT_TYPES, RiddlerAdminService } from "@curefit/riddler-client"
import { Session, UserContext } from "@curefit/userinfo-common"
import ChallengesViewBuilder from "./ChallengesViewBuilder"
import ChallengeDetailsViewBuilder, { ParticipantDetails } from "./ChallengeDetailsViewBuilder"
import { Challenge, Enrolment, EnrolmentResponse, EntityType } from "@curefit/riddler-common"
import * as _ from "lodash"
import { IUserService, USER_CLIENT_TYPES } from "@curefit/user-client"
import { IChallengeParticipants } from "../common/views/Social"
import { IPageService, ISegmentService } from "@curefit/vm-models"
import { ErrorFactory } from "@curefit/error-client"
import { ErrorCodes } from "../error/ErrorCodes"
import { PromiseCache } from "../util/VMUtil"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { BaseServiceConfig, ConfigService, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import { TimeUtil } from "@curefit/util-common"
import ChallengeReportViewBuilder from "./ChallengeReportViewBuilder"
import DigitalLeagueChallengeViewBuilder, { TeamParticipantDetails } from "../digital/DigitalLeagueChallengeViewBuilder"

interface ChallengeDetailParams {
    id: string
    ref: EntityType
    refId: string
}

export interface DaysChallengeMap {
    days: number
    weeks: number
    challengeId: string
    badgeTitle?: string
    badgeImageUrl?: string
}

export interface PledgeEnrolmentSuccessMessage {
    title: string
    subtitle: string
    imageUrl: string
    actionTitle: string
}

export interface PledgeConfig {
    pledgeSegmentId?: string
    pledgeReportSegmentId?: string
    pledgeReportCyclopsSegmentId?: string
    daysChallengeMapping?: DaysChallengeMap[]
    defaultBadgeImageUrl?: string
    defaultBadgeTitle?: string
    enrolmentSuccessMessage?: PledgeEnrolmentSuccessMessage
}

export interface RiddlerServiceConfig extends BaseServiceConfig {
    configs?: {
        pledgeChallengeConfig?: PledgeConfig;
    }
}

export function ChallengesControllerFactory(kernel: Container) {

    @controller("/challenges", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class ChallengesController {
        constructor(
            @inject(RIDDLER_CLIENT_TYPES.RiddlerService) private riddlerService: IRiddlerService,
            @inject(RIDDLER_CLIENT_TYPES.RiddlerAdminService) private riddlerAdminService: RiddlerAdminService,
            @inject(CUREFIT_API_TYPES.ChallengesViewBuilder) private challengesViewBuilder: ChallengesViewBuilder,
            @inject(CUREFIT_API_TYPES.ChallengeDetailsViewBuilder) private challengeDetailsViewBuilder: ChallengeDetailsViewBuilder,
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
            @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
            @inject(CUREFIT_API_TYPES.PageService) protected pageService: IPageService,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
            @inject(CUREFIT_API_TYPES.ChallengeReportViewBuilder) private challengeReportViewBuilder: ChallengeReportViewBuilder,
            @inject(PAGE_CONFIG_TYPES.ConfigService) private configService: ConfigService,
            @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService,
            @inject(CUREFIT_API_TYPES.DigitalLeagueChallengeViewBuilder) private digitalLeagueChallengeViewBuilder: DigitalLeagueChallengeViewBuilder,
        ) {
        }

        @httpGet("/:id/details")
        async getChallengeDetails(req: Request) {
            const [{id}, {ref, refId}] = [req.params as { id: string }, req.query as { ref: EntityType | "CHALLENGE", refId: string }]
            const userId = this.getUserId(req)
            const userContext: UserContext = req.userContext as UserContext
            userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
            const tz = userContext.userProfile.timezone
            switch (ref) {
                case "ENROLMENT": {
                    const enrolment = (await this.riddlerService.getEnrolment(userId, refId, true, true)).enrolment
                    if (!enrolment) {
                        throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid enrollment").build()
                    }
                    if (enrolment.challengeId !== id) {
                        throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Enrolment invalid for challenge: " + id).build()
                    }
                    return this.challengeDetailsViewBuilder.buildChallengeDetailsView(ref, enrolment, tz, userContext)
                }
                case "INVITE": {
                    const invite = (await this.riddlerService.getInvite(userId, refId, true)).invite
                    if (invite.challengeId !== id) {
                        throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invite invalid for challenge: " + id).build()
                    }
                    switch (invite.status) {
                        case "ACTIVE":
                            return this.challengeDetailsViewBuilder.buildChallengeDetailsView(ref, invite, tz, userContext)
                        case "ACCEPTED": {
                            const enrolment = await this.getEnrolmentForInvite(userId, invite.inviteId, true)
                            return this.challengeDetailsViewBuilder.buildChallengeDetailsView("ENROLMENT", enrolment, tz, userContext)
                        }
                        default:
                            throw this.errorFactory.withCode(ErrorCodes.INVITE_EXPIRED_ERR, 400).withDebugMessage("Invite has expired").build()
                    }
                }
                case "CHALLENGE": {
                    const {enrolments} = await this.riddlerService.getEnrolmentsForChallenge({
                        userId,
                        challengeId: id,
                        withChallenge: true,
                        withStandings: true
                    })
                    const enrolment = enrolments.find(_ => _)
                    if (enrolment) {
                        return this.challengeDetailsViewBuilder.buildChallengeDetailsView("ENROLMENT", enrolment, tz, userContext)
                    }
                    const {invites} = await this.riddlerService.getActiveInvites(userId, true)
                    const invite = invites.find(_ => _.challengeId === id && _.status === "ACTIVE")
                    if (invite) {
                        return this.challengeDetailsViewBuilder.buildChallengeDetailsView("INVITE", invite, tz, userContext)
                    }
                    const {invites: autoInvites} = await this.riddlerService.getActiveInvites("AUTO", true)
                    const autoInvite = autoInvites.find(_ => _.challengeId === id && _.status === "ACTIVE")
                    if (autoInvite) {
                        if (!userContext.userProfile.promiseMapCache) {
                            userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
                        }
                        if (autoInvite.challenge.segmentExpression) {
                            const segments = autoInvite.challenge.segmentExpression.split("||")
                            for (const segment of segments) {
                                const [type, value] = segment.split("|")
                                if (type === "CYCLOPS") {
                                    const segment = await this.segmentService.doesUserBelongToSegment(value, userContext)
                                    if (segment) {
                                        return this.challengeDetailsViewBuilder.buildChallengeDetailsView("INVITE", autoInvite, tz, userContext)
                                    }

                                }
                            }
                        }
                    }
                }
            }
            throw this.errorFactory.withCode(ErrorCodes.CHALLENGE_ACCESS_ERR, 400).withDebugMessage("Unable to access challenge").build()
        }

        @httpPost("/pledge")
        async createPledgeEnrolment(req: Request): Promise<any> {
            const {days, weeks, sourcePageId} = req.body
            const riddlerServiceConfig: RiddlerServiceConfig = this.configService.getConfig("RIDDLER")
            const pledgeConfig = riddlerServiceConfig.configs.pledgeChallengeConfig
            const daysChallengeMap: DaysChallengeMap = pledgeConfig.daysChallengeMapping.find(conf => conf.days == days && conf.weeks == weeks)
            // create enrolment
            const userId = this.getUserId(req)
            const userContext: UserContext = req.userContext as UserContext
            const tz = userContext.userProfile.timezone
            const endDate: Date = TimeUtil.parseDate(TimeUtil.addDays(tz, TimeUtil.todaysDate(tz), 7 * weeks - 1), tz)
            const {enrolment} = await this.riddlerAdminService.autoEnrol({
                challengeId: daysChallengeMap.challengeId,
                userId: userId,
                startDate: TimeUtil.getDateNow(tz),
                endDate: endDate,
                source: "AUTO"
            }, tz)
            if (enrolment) {
                this.serviceInterfaces.segmentationClient.removeUsersFromSegment(pledgeConfig.pledgeReportSegmentId, [userId])
                this.serviceInterfaces.segmentationClient.removeUsersFromSegment(pledgeConfig.pledgeSegmentId, [userId])
            }
            return {
                type: "DONE",
                title: pledgeConfig.enrolmentSuccessMessage.title,
                subtitle: pledgeConfig.enrolmentSuccessMessage.subtitle,
                imageUrl: pledgeConfig.enrolmentSuccessMessage.imageUrl,
                action: {
                    actionType: "HIDE_PLEDGE_CHALLENGE_MODAL",
                    title: pledgeConfig.enrolmentSuccessMessage.actionTitle,
                    ref: "ENROLMENT",
                    refId: enrolment.enrolmentId,
                    challengeId: enrolment.challengeId
                }
            }
        }

        @httpPost("/pledge/dismissmodal")
        async handleDismissPledgeModalCTA(req: Request): Promise<any> {
            const userId = this.getUserId(req)
            const riddlerServiceConfig: RiddlerServiceConfig = this.configService.getConfig("RIDDLER")
            const reportSegmentId = riddlerServiceConfig.configs.pledgeChallengeConfig.pledgeReportSegmentId
            return await this.serviceInterfaces.segmentationClient.removeUsersFromSegment(reportSegmentId, [userId])
        }

        @httpGet("/pledge/report")
        async getChallengeReport(request: Request) {
            const userContext: UserContext = request.userContext as UserContext
            const userId = this.getUserId(request)
            const enrolmentId: string = request.query.enrolmentId
            const enrolment: EnrolmentResponse = (await this.riddlerService.getEnrolment(userId, enrolmentId, true)).enrolment
            return await this.challengeReportViewBuilder.buildView(userContext, enrolment)
        }


        @httpPost("/invites/:inviteId/accept")
        async acceptInvite(req: Request): Promise<ChallengeDetailParams> {
            const [{inviteId}, {startDate}] = [req.params, req.body]
            const userId = this.getUserId(req)
            const userContext: UserContext = req.userContext as UserContext
            const tz = userContext.userProfile.timezone
            const {invite} = await this.riddlerService.getInvite(userId, inviteId)
            if (_.isNil(invite)) {
                throw this.errorFactory.withCode(ErrorCodes.INVITE_INVALID_ERR, 400).withDebugMessage("Invite seems to be invalid. Please restart the application if issue persists").build()
            }
            switch (invite.status) {
                case "ACTIVE": {
                    const {enrolment} = await this.riddlerService.acceptInvite(inviteId, userId, tz, false, startDate)
                    return {
                        id: enrolment.challengeId,
                        ref: "ENROLMENT",
                        refId: enrolment.enrolmentId
                    }
                }
                case "ACCEPTED": {
                    const enrolment = await this.getEnrolmentForInvite(userId, inviteId, false)
                    return {
                        id: enrolment.challengeId,
                        ref: "ENROLMENT",
                        refId: enrolment.enrolmentId
                    }
                }
                default:
                    throw this.errorFactory.withCode(ErrorCodes.INVITE_INVALID_ERR, 400).withDebugMessage("Invite seems to be invalid. Please restart the application if issue persists").build()
            }
        }

        private async getEnrolmentForInvite(userId: string, inviteId: string, withChallenge: boolean): Promise<Enrolment> {
            const {recentEnrolments} = await this.riddlerService.getRecentEnrolments(userId, withChallenge, withChallenge)
            const enrolment = recentEnrolments.find(enrolment => enrolment.inviteId === inviteId)
            if (_.isNil(enrolment)) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Unable to find enrollment").build()
            }
            return enrolment
        }

        @httpPost("/invites/:inviteId/decline")
        async declineInvite(req: Request): Promise<boolean> {
            const {inviteId} = req.params
            const userId = this.getUserId(req)
            await this.riddlerService.declineInvite(inviteId, userId, false)
            return true
        }

        @httpGet("/")
        async getUserChallenges(req: Request): Promise<{ challenges: any[] }> {
            const userId = this.getUserId(req)
            const {enrolments} = await this.riddlerService.getAllEnrolments(userId, true)
            const filteredEnrolments = []
            const squadChallengeIds = new Set<string>()
            for (const enrolment of enrolments) {
                // hardcoding filtering out specific enterprise challenges
                if (enrolment.challengeId === "4d1e3ad9-a36f-4e57-8707-7d97ef8a9ee8" || enrolment.challengeId === "e5913ef0-a169-48e0-9f64-46dfd0703afe") {
                    continue
                }
                // Hack to prevent duplicate squad entries
                if (this.isSquadChallenge(enrolment.challenge)) {
                    if (!squadChallengeIds.has(enrolment.challengeId)) {
                        squadChallengeIds.add(enrolment.challengeId)
                        filteredEnrolments.push(enrolment)
                    }
                } else {
                    filteredEnrolments.push(enrolment)
                }
            }
            return {
                challenges: this.challengesViewBuilder.buildMyChallengesView(
                    filteredEnrolments.filter(e => e.status !== "FORCE_EXIT"),
                    req.userContext
                )
            }
        }

        private isSquadChallenge(challenge: Challenge): boolean {
            return challenge.tags?.includes("squad")
        }

        @httpPost("/leave")
        async leaveChallenge(req: Request): Promise<ChallengeDetailParams> {
            const userId = this.getUserId(req)
            const enrolmentId = req.body.enrolmentId
            const {enrolment} = await this.riddlerService.leaveChallenge(userId, enrolmentId)
            return {
                id: enrolment.challengeId,
                ref: "ENROLMENT",
                refId: enrolment.enrolmentId
            }
        }

        @httpGet("/:challengeId/leaderboard")
        async getLeaderboard(req: Request): Promise<IChallengeParticipants[]> {
            const session: Session = req.session
            const userId: string = session.userId

            const leaderboardId: string = req.query.id

            if (req.params.challengeId === undefined) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Please send a challengeId").build()
            }
            if (req.query.offset === undefined) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Please send an offset").build()
            }
            if (req.query.limit === undefined) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Please send a limit").build()
            }

            const challengeId = req.params.challengeId
            const offset = req.query.offset
            const limit = req.query.limit

            const {entries, challenge} = await this.riddlerService.getLeaderboard(challengeId, parseInt(offset), parseInt(limit), leaderboardId)
            if (!_.isEmpty(entries)) {
                let userIds = _.compact(entries.map(_ => _.userId))
                const groupIds = _.compact(entries.map(_ => _.groupId))

                let communityIdVsUserId: {[groupId: string]: string} = {}
                const isSquadsTeamChallenge = DigitalLeagueChallengeViewBuilder.isSquadTeamChallenge(challenge)

                if (isSquadsTeamChallenge) {
                    ({userIds, communityIdVsUserId} = await this.challengeDetailsViewBuilder.getTeamParticipantDetailsForSquads(groupIds))
                }

                const userMap = !_.isEmpty(userIds) ? _.keyBy(await this.userService.getUsers(userIds), "id") : {}
                const {groups} = await this.riddlerService.getGroups(groupIds)
                const groupMap = !_.isEmpty(groups) ? _.keyBy(groups, "groupId") : {}

                const leaderBoardUIConfig = challenge.uiConfig.leaderboardUIConfigs ? challenge.uiConfig.leaderboardUIConfigs.find(_ => _.leaderBoardConfigId === leaderboardId) : undefined
                const participantDetails: TeamParticipantDetails | ParticipantDetails = {
                    groupMap: groupMap,
                    userMap: userMap,
                    communityIdVsUserId
                }
                const scoreText = leaderBoardUIConfig ? leaderBoardUIConfig.scoreText : challenge.uiConfig.scoreText
                return this.challengeDetailsViewBuilder._getLeaderboardParticipantList(userId, challenge, entries, scoreText, participantDetails, true)
            }
            return []
        }

        private getUserId(req: Request): string {
            return (req.session as Session).userId
        }
    }

    return ChallengesController
}

export default ChallengesControllerFactory
