import { Gift<PERSON>ardP<PERSON>y, RewardRequest, RewardType, CultPassRequest, CultLuxRequest } from "@curefit/constello-common"
import { Vertical } from "@curefit/base-common"
import { ProductType } from "@curefit/product-common"
import { UserOfferEligibilityResponse } from "@curefit/offer-common"
import { CreateCCAorExtendMembershipResponse } from "@curefit/cult-common"
import { WalletTransaction } from "@curefit/fitcash-common"
import { ActivityTypeDS } from "@curefit/logging-common"
import { injectable } from "inversify"
import { Timezone } from "@curefit/util-common"
import { Membership } from "@curefit/membership-commons"
import { LedgerEntry, OfferEligibilityResponse, Response, LiveMembershipReward, OfferReward as OfferReward_1 } from "@curefit/reward-common"

export type ConversionEventType = "CLASS" | "PURCHASE" | "DUMMY_PACK_PURCHASE" | "DUMMY_TRIAL_CLASS"

export const DEFAULT_CULT_EXTENSION_DAYS = 30

export interface ClassEventData {
    vertical: Vertical
    classDate: Date
}

export interface PurchaseEventData {
    purchaseDate: Date
    productType: ProductType
}

export interface FitcashReward {
    amount: number
    currency: string
}

export interface CultExtension {
    startDate: Date
    endDate: Date
    prevEndDate: Date
}

export interface CultPlayExtension extends CultExtension {
}

export interface CultLuxExtension extends CultExtension {
}

export interface CultCompAccess {
    startDate: Date
    endDate: Date
}

export interface LiveReward {
    duration: number
    startDate: Date
    endDate: Date
}

export interface CultExtensionReward {
    isExtension: boolean
    duration: number
    metadata: CultCompAccess | CultExtension
    isSubscription: boolean
}

export interface CultPlayExtensionReward extends CultExtensionReward {
}

export interface CultLuxExtensionReward extends CultExtensionReward {
}

export interface OfferReward {
    offerId: string
    endDate: Date
}

export type RewardDataType = FitcashReward | CultExtensionReward | LiveReward | OfferReward

export interface Reward {
    rewardType: RewardType
    rewardData: RewardDataType
}

export interface ConversionEvent {
    eventType: ConversionEventType
    eventData?: ClassEventData | PurchaseEventData
}

export interface Conversion {
    conversionEvent: ConversionEvent
    rewards: Reward[]
    date: Date
}

export interface Referee {
    name: string
    phone: string
}

export type System = "REFERRAL" | "GIFT_CARDS"

export interface MyReferral {
    system: System
    referralId: string
    referee: Referee
    activationDate: Date
    endDate: Date
    conversions: Conversion[]
    policy?: GiftCardPolicy
}

@injectable()
export abstract class TransformationService {

    constructor() {
    }

    public abstract transformReferralsResponse(response: any, tz?: Timezone): Promise<MyReferral[]>

    protected abstract transformConversionEvent(conversionData: any): Promise<ConversionEvent>

    async transformReward(rewardType: RewardType, fulfilmentResponse: UserOfferEligibilityResponse | CreateCCAorExtendMembershipResponse | WalletTransaction | LedgerEntry<Response | Membership>, rewardRequest?: RewardRequest): Promise<Reward> {
        let rewardData: RewardDataType
        if (!rewardType) {
            return {
                rewardType: "FITCASH",
                rewardData: {
                    amount: 30000,
                    currency: "INR"
                }
            }
        }
        switch (rewardType) {
            case "CULTPASS": {
                const cultPassResponse = (fulfilmentResponse as any).fulfilmentResponse ? (fulfilmentResponse as any).fulfilmentResponse?.data : (fulfilmentResponse as any)
                const isSubscription = cultPassResponse.membership ? (cultPassResponse.membership as any).isSubscription : undefined
                const duration = (rewardRequest.hasOwnProperty("duration")) ? (rewardRequest as CultPassRequest).duration : DEFAULT_CULT_EXTENSION_DAYS
                rewardData = {
                    isExtension: true,
                    isSubscription: isSubscription,
                    duration: duration,
                    metadata: {
                        startDate: new Date(cultPassResponse.startDate),
                        endDate: new Date(cultPassResponse.endDate)
                    }
                }
                break
            }

            case "CULT": {
                const cultResponse = (fulfilmentResponse as any).fulfilmentResponse ? (fulfilmentResponse as any).fulfilmentResponse?.data : (fulfilmentResponse as any)
                const isSubscription = cultResponse.membership ?
                    (cultResponse.membership as any).isSubscription : undefined
                if (cultResponse.status === "EXTENDED") {
                    rewardData = {
                        isExtension: true,
                        isSubscription: isSubscription,
                        duration: cultResponse.duration,
                        metadata: {
                            startDate: new Date(cultResponse.membership.startDate),
                            endDate: new Date(cultResponse.membership.endDate),
                            prevEndDate: new Date(cultResponse.membership.prevEndDate)
                        }
                    }
                } else {
                    rewardData = {
                        isExtension: false,
                        isSubscription: isSubscription,
                        duration: cultResponse.duration,
                        metadata: {
                            startDate: new Date(cultResponse.startDate),
                            endDate: new Date(cultResponse.endDate)
                        }
                    }
                }
                break
            }

            case "CULT_PLAY": {
                const cultPlayResponse = (fulfilmentResponse as any).fulfilmentResponse ? (fulfilmentResponse as any).fulfilmentResponse?.data : (fulfilmentResponse as any)
                const isSubscription = cultPlayResponse.membership ?
                    (cultPlayResponse.membership as any).isSubscription : undefined
                if (cultPlayResponse.status === "EXTENDED") {
                    rewardData = {
                        isExtension: true,
                        isSubscription: isSubscription,
                        duration: cultPlayResponse.duration,
                        metadata: {
                            startDate: new Date(cultPlayResponse.membership.startDate),
                            endDate: new Date(cultPlayResponse.membership.endDate),
                            prevEndDate: new Date(cultPlayResponse.membership.prevEndDate)
                        }
                    }
                } else {
                    rewardData = {
                        isExtension: false,
                        isSubscription: isSubscription,
                        duration: cultPlayResponse.duration,
                        metadata: {
                            startDate: new Date(cultPlayResponse.startDate),
                            endDate: new Date(cultPlayResponse.endDate)
                        }
                    }
                }
                break
            }

            case "LUXURY": {
                const cultLuxResponse = (fulfilmentResponse as any).fulfilmentResponse ? (fulfilmentResponse as any).fulfilmentResponse?.data : (fulfilmentResponse as any)
                const isSubscription = cultLuxResponse.membership ?
                    (cultLuxResponse.membership as any).isSubscription : undefined
                const duration = (rewardRequest.hasOwnProperty("duration")) ? (rewardRequest as CultLuxRequest).duration : DEFAULT_CULT_EXTENSION_DAYS
                rewardData = {
                    isExtension: true,
                    isSubscription: isSubscription,
                    duration: duration,
                    metadata: {
                        startDate: new Date(cultLuxResponse.startDate),
                        endDate: new Date(cultLuxResponse.endDate)
                    }
                }
                break
            }

            case "FITCASH": {
                const fitcashResponse = fulfilmentResponse as any
                rewardData = {
                    amount: fitcashResponse.amount || fitcashResponse.reward?.amount,
                    currency: fitcashResponse.currency  || fitcashResponse.reward?.currency
                }
                break
            }

            case "LIVE_MEMBERSHIP": {
                const ledgerEntry = (fulfilmentResponse as LedgerEntry<Membership>)
                const reward = ledgerEntry.reward as LiveMembershipReward
                const liveMembership = ledgerEntry.fulfilmentResponse?.data
                rewardData = {
                    duration: reward.days,
                    startDate: new Date(liveMembership.start),
                    endDate: new Date(liveMembership.end)
                }
                break
            }

            case "OFFER": {
                const ledgerEntry = (fulfilmentResponse as LedgerEntry<OfferEligibilityResponse>)
                const reward = ledgerEntry.reward as OfferReward_1
                rewardData = {
                    offerId: reward.offerId,
                    endDate: new Date(reward.endDate)
                }
            }
        }

        return {
            rewardType: rewardType,
            rewardData: rewardData
        }
    }

    getVertical(activityType: ActivityTypeDS): Vertical {
        switch (activityType) {
            case "CULT_CLASS":
                return "CULT_FIT"
            case "MIND_CLASS":
                return "MIND_FIT"
            case "LIVE_SESSION":
                return "LIVE_FIT"
        }
    }
}

export const ConversionProductTypes: ProductType[] = ["GYMFIT_FITNESS_PRODUCT", "FITNESS", "MIND", "BUNDLE", "FOOD", "CONSULTATION", "CF_LIVE", "PLAY", "LUX_FITNESS_PRODUCT"] // TODO: remove hardcoding
