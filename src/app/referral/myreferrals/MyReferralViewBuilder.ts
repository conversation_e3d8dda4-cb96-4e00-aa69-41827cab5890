import { inject, injectable } from "inversify"
import {
    ClassEventData,
    Conversion,
    ConversionEvent,
    CultExtension,
    CultExtensionReward,
    FitcashReward, LiveReward,
    MyReferral, OfferReward,
    PurchaseEventData,
    Reward,
    DEFAULT_CULT_EXTENSION_DAYS, CultPlayExtensionReward, CultPlayExtension, CultLuxExtensionReward, CultLuxExtension
} from "./MyReferral"
import { UserContext } from "@curefit/userinfo-common"
import { ActionUtil } from "@curefit/base-utils"
import {
    MyReferralsDetail,
    MyReferralsDetailReward,
    MyReferralsDetailViewModel,
    MyReferralsEmptyViewModel,
    MyReferralsViewModel,
    ReferralReward,
    ReferralRewardType
} from "@curefit/apps-common"
import { TimeUtil, Timezone } from "@curefit/util-common"
import * as _ from "lodash"
import { capitalize, now } from "lodash"
import * as mustache from "mustache"
import { OFFER_SERVICE_CLIENT_TYPES, OfferServiceV3 } from "@curefit/offer-service-client"
import LiveUtil from "../../util/LiveUtil"
import { BASE_TYPES, Logger } from "@curefit/base"
import { ReferralUtil } from "../ReferralUtil"

const imageUrl = {
    curefitVMan: "/image/icons/referral/curefitVMan.png",
    emptyMyReferrals: "/image/icons/referral/empty_my_referrals.png",
    extended: "/image/icons/referral/extended.png",
    noRewards: "/image/icons/referral/no_rewards.png",
    reward: "/image/icons/referral/reward.png",
    tick: "/image/icons/referral/tick.png",
    nounFriends: "/image/icons/referral/nounFriends.png"
}


@injectable()
class MyReferralViewBuilder {

    constructor(
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) private offerServiceV3: OfferServiceV3,
        @inject(BASE_TYPES.ILogger) private logger: Logger
    ) {
    }

    private getSubtitle(myReferral: MyReferral, tz: Timezone) {
        switch (myReferral.system) {
            case "REFERRAL":
                return `Referral invite valid from ${TimeUtil.formatDateInTimeZone(tz, myReferral.activationDate, "MMM DD, YYYY")} - ${TimeUtil.formatDateInTimeZone(tz, myReferral.endDate, "MMM DD, YYYY")}`
            case "GIFT_CARDS": {
                if (myReferral.conversions.length > 0) {
                    if (myReferral?.policy?.uiConfig?.myReferral?.listing?.subtitle?.converted) {
                        return mustache.render(myReferral.policy.uiConfig.myReferral.listing.subtitle.converted, {
                            conversionDate: TimeUtil.formatDateInTimeZone(tz, myReferral.conversions[0].date, "MMM DD, YYYY")
                        })
                    }
                } else {
                    if (myReferral?.policy?.uiConfig?.myReferral?.listing?.subtitle?.notConverted) {
                        return mustache.render(myReferral.policy.uiConfig.myReferral.listing.subtitle.notConverted, {
                            activationDate: TimeUtil.formatDateInTimeZone(tz, myReferral.activationDate, "MMM DD, YYYY"),
                            conversionEndDate: TimeUtil.formatDateInTimeZone(tz, myReferral.endDate, "MMM DD, YYYY")
                        })
                    }
                }
            }
        }
    }

    buildMyReferralCardView(userContext: UserContext, myReferrals: MyReferral[], tz: Timezone): MyReferralsViewModel[] | MyReferralsEmptyViewModel {
        const sortedReferrals = _.reverse(_.sortBy(myReferrals, o => new Date(o.activationDate)))
        if (_.isArray(sortedReferrals) && !_.isEmpty(sortedReferrals)) {
            return sortedReferrals.map(myReferral => {
                return {
                    title: myReferral.referee.name || myReferral.referee.phone,
                    subTitle: this.getSubtitle(myReferral, tz),
                    rewards: myReferral.conversions.length ? this.getRewardsView(userContext, tz, myReferral.conversions) : this.getNoRewardsView(myReferral, tz),
                    action: {
                        actionType: "NAVIGATION",
                        url: ActionUtil.myReferralDetail(myReferral.referralId, myReferral.system),
                        title: "View Details"
                    }
                }
            })
        }
        const isNewFlowSupported: boolean = ReferralUtil.isNewReferralPageSupported(userContext)
        return {
            imageUrl: imageUrl.emptyMyReferrals,
            message: "None of your friends\nhave accepted your referral\ninvite in the last 6 months!",
            action: {
                actionType: "NAVIGATION",
                url: isNewFlowSupported ? "curefit://fl_referral_page" : "curefit://referralpagev2",
                title: "Refer a friend now"
            }
        } as MyReferralsEmptyViewModel
    }

    getRewardsView(userContext: UserContext, tz: Timezone, conversions: Conversion[]): ReferralReward[] {
        return conversions.reduce((res, _) => {
            res = [...res, ..._.rewards.map(reward => this.getReward(userContext, tz, reward))]
            return res
        }, [])
    }

    getReward(userContext: UserContext, tz: Timezone, reward: Reward): ReferralReward {
        switch (reward.rewardType) {
            case "FITCASH":
                const fitcashReward = reward.rewardData as FitcashReward
                return {
                    type: reward.rewardType,
                    iconUrl: this.getIconUrl(reward.rewardType),
                    desc: "Fitcash you earned",
                    value: `${fitcashReward.amount / 100}`
                }
            case "CULTPASS":
                const cultPassExtensionReward = reward.rewardData as CultExtensionReward
                return {
                    type: reward.rewardType,
                    iconUrl: this.getIconUrl(reward.rewardType),
                    desc: "Your pack was extended by",
                    value: `${cultPassExtensionReward.duration} Days`
                }
            case "CULT":
                const cultExtensionReward = reward.rewardData as CultExtensionReward
                return {
                    type: reward.rewardType,
                    iconUrl: this.getIconUrl(reward.rewardType),
                    desc: cultExtensionReward.isExtension ? "Your pack was extended by" : "A pack was created for",
                    value: `${cultExtensionReward.duration} Days`
                }
            case "CULT_PLAY":
                const cultPlayExtensionReward = reward.rewardData as CultExtensionReward
                return {
                    type: reward.rewardType,
                    iconUrl: this.getIconUrl(reward.rewardType),
                    desc: cultPlayExtensionReward.isExtension ? "Your pack was extended by" : "A pack was created for",
                    value: `${cultPlayExtensionReward.duration} Days`
                }
            case "LUXURY":
                const cultLuxExtensionReward = reward.rewardData as CultExtensionReward
                return {
                    type: reward.rewardType,
                    iconUrl: this.getIconUrl(reward.rewardType),
                    desc: cultLuxExtensionReward.isExtension ? "Your pack was extended by" : "A pack was created for",
                    value: `${cultLuxExtensionReward.duration} Days`
                }
            case "OFFER":
                return {
                    type: reward.rewardType,
                    iconUrl: this.getIconUrl(reward.rewardType),
                    desc: "Offers you earned",
                    value: "1"
                }
            case "LIVE_MEMBERSHIP":
                const liveReward = reward.rewardData as LiveReward
                return {
                    type: reward.rewardType,
                    iconUrl: this.getIconUrl(reward.rewardType),
                    desc: `A ${LiveUtil.getLiveBranding(userContext)} membership was created for`,
                    value: `${liveReward.duration} Days`
                }
        }
    }

    getIconUrl(rewardType: ReferralRewardType): string {
        switch (rewardType) {
            case "FITCASH":
                return imageUrl.curefitVMan
            case "LIVE_MEMBERSHIP":
            case "CULTPASS":
            case "CULT":
            case "OFFER":
            case "CULT_PLAY":
            case "LUXURY":
                return imageUrl.extended
            case "NO_REWARD":
                return imageUrl.noRewards
        }
    }

    private getNoRewardsEarnedText(myReferral: MyReferral, tz: Timezone): { title: string, subtitle: string } {
        const expired = new Date(myReferral.endDate).getTime() < now()
        switch (myReferral.system) {
            case "REFERRAL": {
                return {
                    title: expired ? "No rewards earned" : "No rewards yet",
                    subtitle: expired ?
                        `Your friend did not make a purchase while the referral invite was valid` :
                        `You can earn referral rewards until the referral invite is valid (${TimeUtil.formatDateInTimeZone(tz, myReferral.endDate, "MMM DD, YYYY")}). Hurry Up!`
                }
            }
            case "GIFT_CARDS": {
                const config = myReferral?.policy?.uiConfig?.myReferral?.listing?.noRewards
                const vars = {
                    conversionEndDate: TimeUtil.formatDateInTimeZone(tz, myReferral.endDate, "MMM DD, YYYY")
                }
                return config ? {
                    title: expired ? (config.expired.title || "No rewards earned") : (config.active.title || "No rewards yet"),
                    subtitle: expired ?
                        mustache.render(config.expired.subtitle, vars) :
                        mustache.render(config.active.subtitle, vars)
                } : {
                    title: "No rewards yet",
                    subtitle: ""
                }
            }
        }
    }

    getNoRewardsView(myReferral: MyReferral, tz: Timezone): ReferralReward[] {
        const NO_REWARD = "NO_REWARD"
        const {title, subtitle} = this.getNoRewardsEarnedText(myReferral, tz)
        return [{
            type: NO_REWARD,
            iconUrl: this.getIconUrl(NO_REWARD),
            desc: title,
            subDesc: subtitle
        }]
    }

    async buildMyReferralDetailCardView(userContext: UserContext, myReferral: MyReferral, tz: Timezone): Promise<MyReferralsDetailViewModel> {
        return {
            heading: {
                name: myReferral.referee.name,
                phone: myReferral.referee.phone,
                iconUrl: imageUrl.nounFriends,
            },
            details: await this.getMyReferralDetail(userContext, myReferral, tz),
            action: {
                actionType: "NAVIGATION",
                url: "curefit://support",
                title: "Need help?"
            }
        }
    }

    private getDetailAcceptedText(myReferral: MyReferral, tz: Timezone, name: string): { title: string, subtitle: string } {
        const expired = new Date(myReferral.endDate).getTime() < now()
        switch (myReferral.system) {
            case "REFERRAL": {
                return {
                    title: `${name} accepted the invite`,
                    subtitle: `${TimeUtil.formatDateInTimeZone(tz, myReferral.activationDate, "MMM DD, YYYY")} | Referral ${expired ? "expired" : "expires"} on ${TimeUtil.formatDateInTimeZone(tz, myReferral.endDate, "MMM DD, YYYY")}`,
                }
            }
            case "GIFT_CARDS": {
                const config = myReferral.policy.uiConfig.myReferral.details.accepted
                const vars = {
                    name,
                    activationDate: TimeUtil.formatDateInTimeZone(tz, myReferral.activationDate, "MMM DD, YYYY"),
                    conversionEndDate: TimeUtil.formatDateInTimeZone(tz, myReferral.endDate, "MMM DD, YYYY"),
                    policyEndDate: TimeUtil.formatDateInTimeZone(tz, myReferral.policy.endDate, "MMM DD, YYYY")
                }
                return {
                    title: mustache.render(config.title, vars),
                    subtitle: mustache.render(config.subtitle, vars)
                }
            }
        }
    }

    async getMyReferralDetail(userContext: UserContext, myReferral: MyReferral, tz: Timezone): Promise<MyReferralsDetail[]> {
        const name = myReferral.referee.name ? capitalize(myReferral.referee.name.split(" ")[0]) : "Your friend"
        const {title, subtitle} = this.getDetailAcceptedText(myReferral, tz, name)
        return [
            {
                title,
                subTitle: subtitle,
                iconUrl: imageUrl.tick
            },
            ... await Promise.all(myReferral.conversions.map(conversion => this.getMyReferralConversion(userContext, tz, conversion, name)))
        ]
    }

    async getMyReferralConversion(userContext: UserContext, tz: Timezone, conversion: Conversion, name: String): Promise<MyReferralsDetail> {
        return {
            ...this.getConversionTitle(userContext, tz, conversion.conversionEvent, name),
            iconUrl: imageUrl.tick,
            conversion: {
                heading: "YOU EARNED",
                iconUrl: imageUrl.reward,
                rewards: await Promise.all(conversion.rewards.map(reward => this.getDetailReward(userContext, tz, reward)))
            }
        }
    }

    private getPurchaseTitle(userContext: UserContext, name: String, event: PurchaseEventData) {
        switch (event.productType) {
            case "FOOD":
                return `${name} placed an eat order`
            case "CONSULTATION":
                return `${name} did a consultation`
            case "DIAGNOSTICS":
                return `${name} did a diagnostic test`
            case "GYMFIT_FITNESS_PRODUCT":
                return `${name} bought a gym pack`
            case "FITNESS":
            case "FITNESS_PREREGISTRATION":
                return `${name} bought a cult pack`
            case "PLAY":
                return `${name} bought a play pack`
            case "LUX_FITNESS_PRODUCT":
                return `${name} bought a luxury pack`
            case "MIND":
            case "MIND_PREREGISTRATION":
                return `${name} bought a mind pack`
            case "CF_LIVE":
                return `${name} bought a ${LiveUtil.getLiveBranding(userContext)} membership`
        }
    }

    getConversionTitle(userContext: UserContext, tz: Timezone, conversionEvent: ConversionEvent, name: String): MyReferralsDetailReward {
        switch (conversionEvent.eventType) {
            case "CLASS": {
                const eventData = conversionEvent.eventData as ClassEventData
                return {
                    title: `${name} attended a class`,
                    subTitle: `on ${TimeUtil.formatDateInTimeZone(tz, eventData.classDate, "MMM DD, YYYY")}`
                }
            }
            case "PURCHASE": {
                const eventData = conversionEvent.eventData as PurchaseEventData
                return {
                    title: this.getPurchaseTitle(userContext, name, eventData),
                    subTitle: `on ${TimeUtil.formatDateInTimeZone(tz, eventData.purchaseDate, "MMM DD, YYYY")}`
                }
            }
            case "DUMMY_PACK_PURCHASE":
                return {
                    title: `${name} bought a cult pack`
                }
            case "DUMMY_TRIAL_CLASS":
                return {
                    title: `${name} attended a trial class`
                }
        }
    }

    async getDetailReward(userContext: UserContext, tz: Timezone, reward: Reward): Promise<MyReferralsDetailReward> {
        switch (reward.rewardType) {
            case "FITCASH":
                const fitcashReward = reward.rewardData as FitcashReward
                return {
                    title: `Fitcash worth ${fitcashReward.amount / 100}`
                }
            case "CULTPASS":
                const cultPassExtensionReward = reward.rewardData as CultExtensionReward
                const cultPassReward = cultPassExtensionReward.metadata as CultExtension
                const  prevEndDate = new Date(cultPassReward.endDate)
                prevEndDate.setDate(prevEndDate.getDate() - cultPassExtensionReward.duration )
                return {
                    title: `${cultPassExtensionReward.duration} Day extension on existing pack`,
                    subTitle: `Pack end date changed from ${TimeUtil.formatDateInTimeZone(tz, prevEndDate, "MMM DD, YYYY")} to ${TimeUtil.formatDateInTimeZone(tz, cultPassReward.endDate, "MMM DD, YYYY")}`
                }
            case "CULT":
                const cultExtensionReward = reward.rewardData as CultExtensionReward
                const cultReward = cultExtensionReward.metadata as CultExtension
                if (cultExtensionReward.isSubscription) {
                    return {
                        title: `${cultExtensionReward.duration} Day extension on existing pack`,
                        subTitle: `₹${cultExtensionReward.duration * 100} adjusted in your next billing cycle`
                    }
                } else if (cultExtensionReward.isExtension) {
                    return {
                        title: `${cultExtensionReward.duration} Day extension on existing pack`,
                        subTitle: `Pack end date changed from ${TimeUtil.formatDateInTimeZone(tz, cultReward.prevEndDate, "MMM DD, YYYY")} to ${TimeUtil.formatDateInTimeZone(tz, cultReward.endDate, "MMM DD, YYYY")}`
                    }
                } else {
                    return {
                        title: `${cultExtensionReward.duration} Day pack was created`,
                        subTitle: `Pack valid from ${TimeUtil.formatDateInTimeZone(tz, cultReward.startDate, "MMM DD, YYYY")} to ${TimeUtil.formatDateInTimeZone(tz, cultReward.endDate, "MMM DD, YYYY")}`
                    }
                }
            case "CULT_PLAY":
                const cultPlayExtensionReward = reward.rewardData as CultPlayExtensionReward
                const cultPlayReward = cultPlayExtensionReward.metadata as CultPlayExtension
                if (cultPlayExtensionReward.isSubscription) {
                    return {
                        title: `${cultPlayExtensionReward.duration} Day extension on existing pack`,
                        subTitle: `x₹${cultPlayExtensionReward.duration * 100} adjusted in your next billing cycle`
                    }
                } else if (cultPlayExtensionReward.isExtension) {
                    return {
                        title: `${cultPlayExtensionReward.duration} Day extension on existing pack`,
                        subTitle: `Pack end date changed from ${TimeUtil.formatDateInTimeZone(tz, cultPlayReward.prevEndDate, "MMM DD, YYYY")} to ${TimeUtil.formatDateInTimeZone(tz, cultPlayReward.endDate, "MMM DD, YYYY")}`
                    }
                } else {
                    return {
                        title: `${cultPlayExtensionReward.duration} Day pack was created`,
                        subTitle: `Pack valid from ${TimeUtil.formatDateInTimeZone(tz, cultPlayReward.startDate, "MMM DD, YYYY")} to ${TimeUtil.formatDateInTimeZone(tz, cultPlayReward.endDate, "MMM DD, YYYY")}`
                    }
                }
            case "LUXURY":
                const cultLuxExtensionReward = reward.rewardData as CultExtensionReward
                const cultLuxReward = cultLuxExtensionReward.metadata as CultExtension
                const  prevEndDateLux = new Date(cultLuxReward.endDate)
                prevEndDateLux.setDate(prevEndDateLux.getDate() - cultLuxExtensionReward.duration )
                return {
                    title: `${cultLuxExtensionReward.duration} Day extension on existing pack`,
                    subTitle: `Pack end date changed from ${TimeUtil.formatDateInTimeZone(tz, prevEndDateLux, "MMM DD, YYYY")} to ${TimeUtil.formatDateInTimeZone(tz, cultLuxReward.endDate, "MMM DD, YYYY")}`
                }
            case "LIVE_MEMBERSHIP": {
                const liveReward = reward.rewardData as LiveReward
                return {
                    title: `${liveReward.duration} days ${LiveUtil.getLiveBranding(userContext)} membership was created`,
                    subTitle: `Pack valid from ${TimeUtil.formatDateInTimeZone(tz, liveReward.startDate, "MMM DD, YYYY")} to ${TimeUtil.formatDateInTimeZone(tz, liveReward.endDate, "MMM DD, YYYY")}`
                }
            }
            case "OFFER": {
                const offerReward = reward.rewardData as OfferReward
                const {data} = await this.offerServiceV3.getOffersByIds([offerReward.offerId])
                return {
                    title: `${data[offerReward.offerId]?.description}`,
                    subTitle: `Valid till ${TimeUtil.formatDateInTimeZone(tz, offerReward.endDate, "MMM DD, YYYY")}`
                }
            }
        }
    }

}

export default MyReferralViewBuilder
