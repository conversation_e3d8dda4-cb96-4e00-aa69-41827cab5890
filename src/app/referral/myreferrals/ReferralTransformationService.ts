import { inject, injectable } from "inversify"
import { IUserService, USER_CLIENT_TYPES } from "@curefit/user-client"
import {
    ConversionEvent,
    ConversionProductTypes,
    MyReferral,
    PurchaseEventData,
    TransformationService
} from "./MyReferral"
import { Referral, ReferralReward, ReferralStatusHistory } from "@curefit/referral-common"
import { User } from "@curefit/user-common"
import { IActivityStoreReadonlyDao, LOGGING_MODELS_TYPES } from "@curefit/logging-models"
import { ActivityTypeDS } from "@curefit/logging-common"
import { ProductType } from "@curefit/product-common"
import * as _ from "lodash"
import { OMS_API_CLIENT_TYPES, IOrderService } from "@curefit/oms-api-client"


@injectable()
export class ReferralTransformationService extends TransformationService {
    constructor(
        @inject(OMS_API_CLIENT_TYPES.OrderService) private omsApiClient: IOrderService,
        @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
        @inject(LOGGING_MODELS_TYPES.ActivityStoreReadonlyDao) private activityDao: IActivityStoreReadonlyDao,
    ) {
        super()
    }

    public async transformReferralsResponse(referrals: Referral[]): Promise<MyReferral[]> {
        const myRefferals = await Promise.all(referrals.map(async referral => {
            try {
                let user: User
                if (referral.refereeUserId) {
                    user = await this.userService.getUser(referral.refereeUserId)
                }

                let globalAssignedRewards: ReferralReward[] = []

                const conversions = (await Promise.all(
                    referral.statusHistory
                        .filter(_ => _.status === "CONVERTED")
                        .map(async conversion => {
                            const conversionEvent = await this.transformConversionEvent(conversion)
                            const assignedRewards = referral.referrerRewards.filter(_ => this.rewardMatchesConversion(_, conversionEvent, globalAssignedRewards))
                            globalAssignedRewards = globalAssignedRewards.concat(assignedRewards)
                            return {
                                conversionEvent: conversionEvent,
                                rewards: await Promise.all(
                                    assignedRewards
                                        .map(async reward => {
                                            return await this.transformReward(reward.reward ? reward.reward.rewardType : undefined, reward.downstreamResponse ? reward.downstreamResponse.payload : undefined, reward?.reward?.params)
                                        })),
                                date: conversion.timestamp
                            }
                        })))
                    .filter(_ => _.rewards && _.rewards.length > 0)

                if (referral.referrerRewards && globalAssignedRewards.length !== referral.referrerRewards.length) {

                    const dummyTrialConversion: ConversionEvent = {
                        eventType: "DUMMY_TRIAL_CLASS"
                    }
                    const dummyPackConversion: ConversionEvent = {
                        eventType: "DUMMY_PACK_PURCHASE"
                    }
                    const trialRewards = referral.referrerRewards.filter(_ => this.rewardMatchesConversion(_, dummyTrialConversion, globalAssignedRewards))
                    const packRewards = referral.referrerRewards.filter(_ => this.rewardMatchesConversion(_, dummyPackConversion, globalAssignedRewards))
                    if (packRewards.length > 0) {
                        conversions.push({
                            conversionEvent: dummyPackConversion,
                            rewards: await Promise.all(
                                trialRewards.concat(packRewards)
                                    .map(async reward => {
                                        return await this.transformReward(reward.reward.rewardType, reward.downstreamResponse.payload, reward?.reward?.params)
                                    })),
                            date: undefined
                        })
                    } else {
                        conversions.push({
                            conversionEvent: dummyTrialConversion,
                            rewards: await Promise.all(
                                trialRewards
                                    .map(async reward => {
                                        return await this.transformReward(reward.reward.rewardType, reward.downstreamResponse.payload, reward?.reward?.params)
                                    })),
                            date: undefined
                        })
                    }
                }

                return {
                    referralId: referral.referralId,
                    system: "REFERRAL" as const,
                    referee: {
                        name: user ? (user.firstName ? `${user.firstName} ${user.lastName || ""}` : undefined) : undefined,
                        phone: referral.refereePhone
                    },
                    activationDate: referral.statusHistory.find(_ => _.status === "ACCEPTED").timestamp, // TODO: assuming all referrals have accepted
                    endDate: referral.expiringAt,
                    conversions: conversions
                }
            } catch (e) {
                return undefined
            }
        }))
        return _.compact(myRefferals)
    }

    protected async transformConversionEvent(conversionData: ReferralStatusHistory): Promise<ConversionEvent> {
        switch (conversionData.conversionEventType) {
            case "ACTIVITY": {
                const activity = await this.activityDao.findOne({
                    idempotenceKey: conversionData.conversionEventId
                })
                switch (activity.activityType) {
                    case "CULT_CLASS":
                    case "MIND_CLASS":
                        return {
                            eventType: "CLASS",
                            eventData: {
                                classDate: new Date(activity.date),
                                vertical: this.getVertical(activity.activityType)
                            }
                        }

                    default:
                        return {
                            eventType: "PURCHASE",
                            eventData: {
                                purchaseDate: new Date(activity.date),
                                productType: this.getProductTypeFromActivity(activity.activityType)
                            }
                        }
                }
            }
            case "ORDER": {
                const order = await this.omsApiClient.getOrder(conversionData.conversionEventId)
                return {
                    eventType: "PURCHASE",
                    eventData: {
                        purchaseDate: new Date(order.createdDate),
                        productType: order.productSnapshots
                            .map(_ => _.productType).filter(productType => ConversionProductTypes.includes(productType))
                            .find(_ => true)
                    }
                }
            }
        }
        return undefined
    }

    private rewardMatchesConversion(reward: ReferralReward, conversion: ConversionEvent, assignedRewards: ReferralReward[]): boolean {
        const consumedRewards = assignedRewards.map(_ => _.consumptionId)
        if (consumedRewards.includes(reward.consumptionId)) {
            return false
        }
        switch (conversion.eventType) {
            case "PURCHASE": {
                const data = conversion.eventData as PurchaseEventData
                if (["CONSULTATION", "BUNDLE", "DIAGNOSTICS", "FOOD"].includes(data.productType) && reward.consumptionId === "FITCASH300") {
                    return true
                } else if (!reward.consumptionId) {
                    return true
                } else return ["GYMFIT_FITNESS_PRODUCT", "FITNESS", "FITNESS_PREREGISTRATION", "MIND", "MIND_PREREGISTRATION", "PLAY", "LUX_FITNESS_PRODUCT"].includes(data.productType)
            }
            case "CLASS":
                if (!reward.consumptionId) {
                    return true
                }
            case "DUMMY_TRIAL_CLASS": {
                return ["FITCASH300", "5_DAY_CULT_EXTENSION", "UAE_CULT_EXTENSION_ACTIVITY"].includes(reward.consumptionId)
            }
            case "DUMMY_PACK_PURCHASE": {
                return ["30_DAY_CULT_EXTENSION", "UAE_CULT_EXTENSION_ORDER"].includes(reward.consumptionId)
            }
        }
    }

    private getProductTypeFromActivity(activityType: ActivityTypeDS): ProductType {
        switch (activityType) {
            case "EATFIT_MEAL":
                return "FOOD"
            case "CONSULTATION":
                return "CONSULTATION"
            case "AT_HOME_SAMPLE_COLLECTION":
                return "DIAGNOSTICS"
            case "IN_CENTRE_VISIT_FOR_TEST":
                return "DIAGNOSTICS"
        }
    }
}
