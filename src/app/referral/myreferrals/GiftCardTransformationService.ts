import { inject, injectable } from "inversify"
import { IUserService, USER_CLIENT_TYPES } from "@curefit/user-client"
import { ConversionInfo } from "@curefit/constello-common"
import { ConversionEvent, ConversionProductTypes, MyReferral, TransformationService } from "./MyReferral"
import { OrderPurchaseEvent } from "@curefit/events-common"
import { ActivityDS } from "@curefit/logging-common"
import { GiftCard, GiftCardPolicy } from "@curefit/constello-common/dist/src/GiftCard"
import * as _ from "lodash"
import * as momentTz from "moment-timezone"
import { Timezone } from "@curefit/util-common"

@injectable()
export class GiftCardTransformationService extends TransformationService {
    constructor(
        @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService
    ) {
        super()
    }

    public async transformReferralsResponse(giftCardsData: { cards: GiftCard[], policy: GiftCardPolicy }[], tz: Timezone): Promise<MyReferral[]> {
        return _.flatMap(
            await Promise.all(
                giftCardsData
                    .map(async data =>
                        await Promise.all(data.cards.map(async card => this.transformCard(card, data.policy, tz)))
                    )
            )
        )
    }

    private async transformCard(card: GiftCard, policy: GiftCardPolicy, tz: Timezone) {
        const user = await this.userService.getUser(card.consumedUser.userId)
        const endDate = momentTz(card.consumptionDate).tz(tz).add(policy.uiConfig.referralExpiry, "days").endOf("day").toDate()
        return {
            referralId: card.referralId,
            system: "GIFT_CARDS" as const,
            referee: {
                name: user.firstName ? `${user.firstName} ${user.lastName || ""}` : undefined,
                phone: card.consumedUser.phone
            },
            activationDate: card.consumptionDate,
            endDate,
            conversions: await Promise.all(card.conversions.map(async conversion => {
                return {
                    conversionEvent: await this.transformConversionEvent(conversion),
                    rewards: await Promise.all(conversion.rewards.map(async reward => {
                        return await this.transformReward(reward.rewardInfo.reward.rewardType, reward.rewardFulfilmentResponse)
                    })),
                    date: conversion.date
                }
            })),
            policy
        }
    }

    protected async transformConversionEvent(conversionData: ConversionInfo): Promise<ConversionEvent> {
        const eventType = conversionData.eventType
        switch (eventType) {
            case "ORDER_PLACE": {
                const orderEvent = conversionData.event as OrderPurchaseEvent
                return {
                    eventType: "PURCHASE",
                    eventData: {
                        purchaseDate: conversionData.date, // TODO: hack, orderDate not present in event data, might've to call alfred
                        productType: orderEvent.productSnapshots
                            .map(_ => _.productType).filter(productType => ConversionProductTypes.includes(productType))
                            .find(_ => true)
                    }
                }
            }

            case "CULT_CLASS":
            case "MIND_CLASS":
            case "LIVE_SESSION": {
                const classEvent = conversionData.event as ActivityDS
                return {
                    eventType: "CLASS",
                    eventData: {
                        classDate: new Date(classEvent.date),
                        vertical: this.getVertical(classEvent.activityType)
                    }
                }
            }
            case "EAT_FOOD_DELIVERED": {
                return {
                    eventType: "PURCHASE",
                    eventData: {
                        purchaseDate: conversionData.date,
                        productType: "FOOD"
                    }
                }
            }
            case "AT_HOME_SAMPLE_COLLECTION":
            case "IN_CENTRE_VISIT_FOR_TEST":
                return {
                    eventType: "PURCHASE",
                    eventData: {
                        purchaseDate: conversionData.date,
                        productType: "DIAGNOSTICS"
                    }
                }
            case "CONSULTATION":
                return {
                    eventType: "PURCHASE",
                    eventData: {
                        purchaseDate: conversionData.date,
                        productType: "CONSULTATION"
                    }
                }
        }
    }
}
