import { CodeState } from "@curefit/apps-common"
import * as mustache from "mustache"
import { GiftVoucherWidget } from "../common/views/WidgetView"
import { ReferralUtil } from "./ReferralUtil"
import { CardConfig, GiftCardPolicy, GiftCardPolicyEntity } from "@curefit/constello-common"
import ActionUtil from "../util/ActionUtil"
import * as _ from "lodash"
import { IGiftCardService } from "@curefit/constello-client"
import { UserContext } from "@curefit/userinfo-common"

const STAGE_EXPERIMENT_ID = "106"
const PRODUCTION_EXPERIMENT_ID = "59"

const STAGE_EXPERIMENTS = ["111", "116", "117"]
const PRODUCTION_EXPERIMENTS = ["67", "72", "73"]

export interface UnavailableCopy {
    icon?: string
    title: string
    subTitle: string
}

export class GiftCardUtil {

    static getCountryFromPhone(countryCallingCode: string) {
        switch (countryCallingCode) {
            case "+91":
                return "IN"
            case "+971":
                return "UAE"
            default:
                return "INVALID"
        }
    }

    static isGiftCardActive(policies: GiftCardPolicyEntity[]): boolean {
        return !_.isNil(policies.find(_ => _.policy.vertical === "CULT_FIT"))
    }

    static getWhatsappMessage(message: string, data: any): string {
        return mustache.render(message, {...data, baseUrl: this.getBaseUrl()})
    }

    static getBaseUrl(): string {
        return process.env.ENVIRONMENT === "PRODUCTION" ? "https://www.cure.fit" : "https://stage1234.cure.fit"
    }

    static getVoucherCardWidget(userContext: UserContext, campaignId: string, policy: GiftCardPolicy): GiftVoucherWidget {
        const {vertical, uiConfig} = policy
        const theme = ReferralUtil.getThemeOfVoucher(userContext, vertical)
        const giftVoucherWidget: GiftVoucherWidget = {
            widgetType: "REFER_TO_WIDGET",
            action: [
                {
                    actionType: "NAVIGATION",
                    url: ActionUtil.getGiftVoucherPage(campaignId),
                    meta: {
                        textStyle: {
                            color: "black",
                            marginLeft: 0,
                            fontFamily: "BrandonText-Bold",
                            fontWeight: "bold",
                            fontSize: 22,
                        },
                        title: uiConfig.pageTitle
                    },
                },
            ],
            style: {
                marginTop: 25,
                ...theme.style
            },
            heading: {
                text: uiConfig.discovery.title,
                textStyle: {},
                icon: uiConfig.discovery.iconUrl,
            },
            domain: vertical === "TRANSFORM" ? null : {
                text: theme.title,
                textStyle: {},
                style: {},
            },
            headingContainer: {
                ...theme.headingContainer
            },
            itemHeadTextStyle: {
                ...theme.itemHeadTextStyle
            },
            itemDescriptionTextStyle: {
                color: "black"
            },
            items: [
            ],
            footer: {
                icon: theme.bottomIcon,
            },
        }
        if (uiConfig.discovery.giftText) {
            const giftText = {
                description: uiConfig.discovery.giftText,
                head: "Gift"
            }
            giftVoucherWidget.items.push(giftText)
        }
        if (uiConfig.discovery.earnText) {
            const earnText = {
                description: uiConfig.discovery.earnText,
                head: "Earn"
            }
            giftVoucherWidget.items.push(earnText)
        }
        return giftVoucherWidget
    }

    static getCouponTitle(state: CodeState, cardConfig: CardConfig): string {
        switch (state) {
            case CodeState.ACTIVE:
                return "GIFT"
            case CodeState.CONSUMED:
                return "USED"
            case CodeState.EXPIRED:
                return "EXPIRED"
        }
    }

    static isNewPageEnabled(policies: GiftCardPolicyEntity[]): boolean {
        return !!policies.find(_ => _.policy.vertical != "CULT_FIT")
    }
}
