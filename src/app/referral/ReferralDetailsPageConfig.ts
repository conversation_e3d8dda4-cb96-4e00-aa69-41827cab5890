import { inject, injectable } from "inversify"
import { InMemoryCacheService } from "@curefit/memory-cache"
import { IPageConfigReadOnlyDao, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import * as _ from "lodash"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

@injectable()
class ReferralDetailsPageConfig extends InMemoryCacheService<any> {

    constructor(
        @inject(PAGE_CONFIG_TYPES.PageConfigReadOnlySecondaryDao) protected pageConfigDao: IPageConfigReadOnlyDao,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger, 13 * 60)
        this.load("ReferralDetailsPageConfig")
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    loadData(): Promise<any> {
        return this.pageConfigDao.findOne({ pageId: { $eq: "ReferralDetailsPageConfig" } }).then(pageConfig => {
            const data = pageConfig.data
            this.howItWorksItemList = _.map(<{icon: string, subTitle: string}[]>data.howItWorksItemList, item => {
                return {
                    subTitle: item.subTitle,
                    icon: item.icon
                }
            })

            this.termsAndConditionsTitle = data.termsAndConditionsTitle
            this.termsAndConditionsItemList = _.map(<{number: string, subTitle: string}[]>data.termsAndConditionsItemList, item => {
                return {
                    number: item.number,
                    subTitle: item.subTitle
                }
            })
            this.termsAndConditionsProperties = data.termsAndConditionsProperties
        })
    }

    public howItWorksItemList: {icon: string, subTitle: string}[]
    public termsAndConditionsTitle: string
    public termsAndConditionsItemList: {number: string, subTitle: string}[]
    public termsAndConditionsProperties: {backgroundColor: string, titleColor: string}

}

export default ReferralDetailsPageConfig
