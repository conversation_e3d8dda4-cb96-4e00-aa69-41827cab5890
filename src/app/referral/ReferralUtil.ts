import {
    Action,
    FormattedTextWidget,
    GiftVoucherWidget,
    ProductListCustomRowWidget,
    ProductListWidget
} from "../common/views/WidgetView"
import { ReferrerStatus, ReferrerWithStatus } from "@curefit/referral-common"
import { ReferralPageData, ReferralTermsWithStatus } from "./ReferPageConfig"
import * as mustache from "mustache"
import { Vertical } from "@curefit/base-common"
import { UserContext } from "@curefit/userinfo-common"
import * as _ from "lodash"
import AppUtil from "../util/AppUtil"
import { IServiceInterfaces } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { WidgetWithMetric } from "@curefit/vm-common"
import * as ts from "typescript/lib/tsserverlibrary"
import { Logger } from "@curefit/base"

export const NEW_REFERRAL_APP_VERSION = 8.0
export const STAGE_EAT_EXPERIMENT = "119"
export const PRODUCTION_EAT_EXPERIMENT = "74"

export interface Discovery {
    icon: string
    title: string
    items: {
        description: string
        head: string
    }[]
}

export class ReferralUtil {
    static getWidgetsForReferrer(referrerStatus: ReferrerStatus, segments: string[], referralPageData: ReferralPageData): (FormattedTextWidget | ProductListWidget)[] {
        switch (referrerStatus) {
            case "ENABLED":
                return this.getWidgetsForReferrerWithEnabledStatus(segments, referralPageData)
            case "NOT_AVAILABLE":
                return this.getWidgetsForReferrerWithNotAvailableStatus()
            case "BLACKLISTED":
                return this.getWidgetsForReferrerWithBlacklistedStatus()
            case "CONVERSION_MAX_LIMIT_REACHED":
                return this.getWidgetsForReferrerWithMaxLimitReachedStatus()
            case "NUMBER_NOT_APPLICABLE":
                return this.getWidgetForReferrerWithNonApplicableNumber()
            default:
                return null
        }
    }

    static async isNewReferralSupported(userContext: UserContext): Promise<boolean> {
        return userContext.sessionInfo.appVersion >= NEW_REFERRAL_APP_VERSION
    }

    static getActionsForReferrer(referrerWithStatus: ReferrerWithStatus, whatsAppBannerUrl: string, whatsAppLinkText: string, osName: string, userContext: UserContext): Action[] {
        switch (referrerWithStatus.status) {
            case "ENABLED":
                return this.getActionsForReferrerWithEnabledStatus(whatsAppBannerUrl, whatsAppLinkText, referrerWithStatus.referrer.referrerCode, osName, userContext)
            case "NOT_AVAILABLE":
                return this.getActionsForReferrerWithNotAvailableStatus()
            case "BLACKLISTED":
                return this.getActionsForReferrerWithBlacklistedStatus()
            case "CONVERSION_MAX_LIMIT_REACHED":
                return this.getActionsForReferrerWithMaxLimitReachedStatus()
            default:
                return []
        }
    }

    static getHiwWidgets(referrerWithStatus: ReferrerWithStatus, referralPageData: ReferralPageData, userContext: UserContext): ProductListWidget[] {
        switch (referrerWithStatus.status) {
            case "ENABLED":
            case "NOT_AVAILABLE":
            case "BLACKLISTED":
                return this.getHiwWidgetsForReferrerWithEnabledStatus(referralPageData.referralTermsForEnabled, referralPageData.referralSteps, userContext)
            case "CONVERSION_MAX_LIMIT_REACHED":
                return this.getHiwWidgetsForReferrerWithMaxLimitReachedStatus(referralPageData.referralTermsForEnabled, userContext)
            default:
                return []
        }
    }

    static getHiwWidgetsForReferrerWithEnabledStatus(referralTermsWithStatus: ReferralTermsWithStatus, referralSteps: { icon: string, subTitle: string }[], userContext: UserContext): ProductListWidget[] {
        const isNewFlowSupported: boolean = this.isNewReferralPageSupported(userContext)
        const widgets = []
        const hiwWidget: ProductListWidget = {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "SMALL",
            items: referralSteps,
            iconBackgroundOpacity: 0,
            hideSepratorLines: true,
            noTopPadding: true,
            header: undefined,
            layoutProps: {
                spacing: {
                    top: 40,
                    bottom: 20
                }
            }
        }
        const referralTnc: ProductListWidget = {
            widgetType: "PRODUCT_LIST_WIDGET",
            hideSepratorLines: true,
            collapsable: false,
            type: "NUMBERED",
            footerAction: isNewFlowSupported ? {
                    title: referralTermsWithStatus.tnc.title,
                    url: referralTermsWithStatus.tnc.actionLink,
                    actionType : "NAVIGATION",
                    variant: "tertiary"
                } : null,
            footer: {
                title: referralTermsWithStatus.tnc.title,
                action: referralTermsWithStatus.tnc.actionLink
            },
            backgroundColor: referralTermsWithStatus.termsAndConditionsProperties.backgroundColor,
            header: {
                title: referralTermsWithStatus.termsAndConditionsTitle,
                color: referralTermsWithStatus.termsAndConditionsProperties.titleColor
            },
            items: referralTermsWithStatus.termsAndConditionsItemList
        }
        widgets.push(hiwWidget)
        widgets.push(referralTnc)
        return widgets
    }

    static getHiwWidgetsForReferrerWithBlacklistedStatus(referralTermsWithStatus: ReferralTermsWithStatus): ProductListWidget[] {
        const widgets = []
        const tncItems: { number: string, subTitle: string }[] = [
            {
                subTitle: "We use data algorithms to detect potential abuse of every referral link",
                number: "1"
            },
            {
                subTitle: "Your friends will no longer be able to accept your referral invite",
                number: "2"
            },
            {
                subTitle: "Friends who have already accepted your referral invite can still avail the referral offer.",
                number: "3"
            },
            {
                subTitle: "You can only earn benefits for referrals once your link has been enabled again",
                number: "4"
            }
        ]
        const referralTnc: ProductListWidget = {
            widgetType: "PRODUCT_LIST_WIDGET",
            hideSepratorLines: true,
            type: "NUMBERED",
            footer: {
                title: referralTermsWithStatus.tnc.title,
                action: referralTermsWithStatus.tnc.actionLink
            },
            backgroundColor: "rgb(248, 248, 252)",
            header: undefined,
            items: tncItems
        }
        widgets.push(referralTnc)
        return widgets
    }

    static getHiwWidgetsForReferrerWithMaxLimitReachedStatus(referralTermsWithStatus: ReferralTermsWithStatus, userContext: UserContext): ProductListWidget[] {
        const widgets = []
        const tncItems: { number: string, subTitle: string }[] = [
            {
                subTitle: "You can earn no more than Fitcash worth Rs 1500 through referrals. Don’t worry, this is a temporary limit & we’ll notify you when it is relaxed",
                number: "1"
            },
            {
                subTitle: "Your friends will no longer be able to accept your referral invite",
                number: "2"
            },
            {
                subTitle: "Friends who have already accepted your referral invite can still avail the referral offer. However, you will not earn any referral benefits for it.",
                number: "3"
            },
            {
                subTitle: "You can only earn benefits for referrals accepted after this referral limit is relaxed",
                number: "4"
            }
        ]
        const referralTnc: ProductListWidget = {
            widgetType: "PRODUCT_LIST_WIDGET",
            hideSepratorLines: true,
            type: "NUMBERED",
            footer: {
                title: referralTermsWithStatus.tnc.title,
                action: referralTermsWithStatus.tnc.actionLink
            },
            backgroundColor: "rgb(248, 248, 252)",
            header: undefined,
            items: tncItems
        }
        widgets.push(referralTnc)
        return widgets
    }

    static getActionsForReferrerWithEnabledStatus(whatsAppBannerUrl: string, whatsAppLinkText: string, referrerCode: string, osName: string, userContext: UserContext): Action[] {
        const isNewFlowSupported: boolean = this.isNewReferralPageSupported(userContext)
        const isAndroid: boolean = osName === "android"
        const shareAction: Action = {
            title: "Share Your Link",
            actionType: "SHARE_ACTION",
            meta: {
                shareOptions: {
                    url: whatsAppBannerUrl,
                    type: "image/png",
                    message: this.getWhatsAppReferralLinkCopy(whatsAppLinkText, referrerCode),
                    title: "Curefit referrals",
                    whatsAppNumber: isAndroid ?  "" : undefined
                },
                shareChannel: isAndroid ? "WHATSAPP" : undefined
            }
        }
        if (isAndroid) {
            shareAction.iconUrl = "/image/icons/referral/whatsapp.png"
            shareAction.title = "WhatsApp Your Link"
        }
        return [
            {
                title: "How it works",
                viewType: "LINK",
                actionType: "NAVIGATION",
                url: isNewFlowSupported ? "curefit://fl_hiw_referral" : "curefit://referraltncpage",
                meta: {
                    title: "How referral works"
                }
            },
            shareAction
        ]
    }

    static getActionsForReferrerWithNotAvailableStatus(): Action[] {
        return [
            {
                title: "How it works",
                viewType: "LINK",
                actionType: "NAVIGATION",
                url: "curefit://referraltncpage",
                meta: {
                    title: "How referral works"
                }
            },
            {
                title: "Notify Me",
                actionType: "REFERRAL_NOTIFY",
                meta: {
                    message: "Thanks for your interest!",
                    subtitle: `We will notify you once we make 'Refer a Friend' available for you.`,
                    notifySegment: "NOT_AVAILABLE"
                }
            }
        ]
    }

    static getActionsForReferrerWithBlacklistedStatus(): Action[] {
        return [
            {
                title: "What does this mean?",
                viewType: "LINK",
                actionType: "NAVIGATION",
                url: "curefit://referraltncpage",
                meta: {
                    title: "All about referrals"
                }
            },
            {
                title: "Notify Me",
                actionType: "REFERRAL_NOTIFY",
                meta: {
                    message: "Thanks for your interest!",
                    subtitle: "We will notify you once we make 'Refer a Friend' available for you.",
                    notifySegment: "BLACKLISTED"
                }
            }
        ]
    }

    static getActionsForReferrerWithMaxLimitReachedStatus(): Action[] {
        return [
            {
                title: "What is referral limit?",
                viewType: "LINK",
                actionType: "NAVIGATION",
                url: "curefit://referraltncpage",
                meta: {
                    title: "All about referrals"
                }
            },
            {
                title: "Notify Me",
                actionType: "REFERRAL_NOTIFY",
                meta: {
                    message: "Thanks for your interest!",
                    subtitle: "We will notify you once we make 'Refer a Friend' available for you.",
                    notifySegment: "CONVERSION_MAX_LIMIT_REACHED"
                }
            }
        ]
    }

    static compareSegments(array1: string[], array2: string[]): boolean {
        if (!array1 && !array2) {
            return true
        }
        if (!array1 || !array2) {
            return false
        }
        if (array1.length != array2.length) {
            return false
        }
        for (const arr of array1) {
            if (array2.indexOf(arr) === -1) {
                return false
            }
        }
        return true
    }

    static getWidgetsForReferrerWithEnabledStatus(segments: string[], referPageConfig: any): (FormattedTextWidget | ProductListWidget)[] {
        const widgets = []
        const headerWidget: FormattedTextWidget = {
            widgetType: "FORMATTED_TEXT_WIDGET",
            subHeader: referPageConfig.header,
            data: []
        }
        const items = referPageConfig.howItWorksItemList
        const filtered = items.filter(function (item: any) {
            return (!item.segments || item.segments.length === 0) || ReferralUtil.compareSegments(item.segments, segments)
        })
        const referralDetails: ProductListWidget = {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "DYNAMIC_ICON",
            noTopPadding: true,
            hideSepratorLines: true,
            header: undefined,
            iconBackgroundOpacity: 0,
            items: filtered,
            layoutProps: {
                spacing: {
                    top: 40,
                    bottom: 20
                }
            }
        }
        // widgets.push(headerWidget)
        widgets.push(referralDetails)
        return widgets
    }

    static getWidgetsForReferrerWithNotAvailableStatus(): (FormattedTextWidget | ProductListWidget)[] {
        const widgets = []
        const textWidget: FormattedTextWidget = {
            widgetType: "FORMATTED_TEXT_WIDGET",
            subHeader: {
                title: "Currently Unavailable"
            },
            data: [
                {
                    text: "'Refer a friend'",
                    fontWeight: "BOLD",
                    fontSize: 15,
                    fontColor: "rgb(74, 74, 74)",
                    lineHeight: 25
                },
                {
                    text: " is now only available to a select set of users. Please click on 'Notify Me' button below and we'll notify you when it's available for you.",
                    fontWeight: "MEDIUM",
                    fontSize: 15,
                    fontColor: "rgb(74, 74, 74)",
                    lineHeight: 25
                }
            ]
        }
        widgets.push(textWidget)
        return widgets
    }

    static getWidgetForReferrerWithNonApplicableNumber(): (FormattedTextWidget | ProductListWidget)[] {
        const widgets = []
        const textWidget: FormattedTextWidget = {
            widgetType: "FORMATTED_TEXT_WIDGET",
            subHeader: {
                title: "Currently Unavailable"
            },
            data: [
                {
                    text: "'Refer a friend'",
                    fontWeight: "BOLD",
                    fontSize: 15,
                    fontColor: "rgb(74, 74, 74)",
                    lineHeight: 25
                },
                {
                    text: " is now only available to Indian Users.",
                    fontWeight: "MEDIUM",
                    fontSize: 15,
                    fontColor: "rgb(74, 74, 74)",
                    lineHeight: 25
                }
            ]
        }
        widgets.push(textWidget)
        return widgets
    }

    static getWidgetsForReferrerWithBlacklistedStatus(): (FormattedTextWidget | ProductListWidget)[] {
        const widgets = []
        const blackListedWidget: FormattedTextWidget = {
            widgetType: "FORMATTED_TEXT_WIDGET",
            subHeader: {
                title: "Currently Unavailable"
            },
            data: [
                {
                    text: "'Refer a friend'",
                    fontWeight: "BOLD",
                    fontSize: 15,
                    fontColor: "rgb(74, 74, 74)",
                    lineHeight: 25
                },
                {
                    text: " is now only available to a select set of users. Please click on 'Notify Me' button below and we'll notify you when it's available for you.",
                    fontWeight: "MEDIUM",
                    fontSize: 15,
                    fontColor: "rgb(74, 74, 74)",
                    lineHeight: 25
                }
            ]
        }
        const widget2: FormattedTextWidget = {
            widgetType: "FORMATTED_TEXT_WIDGET",
            data: [
                {
                    text: "Get notified when you can start referring again.",
                    fontWeight: "MEDIUM",
                    fontSize: 15,
                    fontColor: "rgba(0, 0, 0, 0.5)",
                    lineHeight: 25
                }
            ]
        }
        widgets.push(blackListedWidget)
        widgets.push(widget2)
        return widgets

    }

    static getWidgetsForReferrerWithMaxLimitReachedStatus(): (FormattedTextWidget | ProductListWidget)[] {
        const widgets = []
        const conversionLimitReachedWidget: FormattedTextWidget = {
            widgetType: "FORMATTED_TEXT_WIDGET",
            subHeader: {
                title: "You’ve maxed out!"
            },
            data: [
                {
                    text: "For now, you’ve reached the limit of referrals you can send.",
                    fontWeight: "MEDIUM",
                    fontSize: 15,
                    fontColor: "rgba(0, 0, 0, 0.5)",
                    lineHeight: 25
                }
            ]
        }
        const conversionLimitReachedWidget_2: FormattedTextWidget = {
            widgetType: "FORMATTED_TEXT_WIDGET",
            data: [
                {
                    "text": "Get notified when you can start referring again.",
                    "fontWeight": "MEDIUM",
                    "fontSize": 15,
                    "fontColor": "rgba(0, 0, 0, 0.5)",
                    "lineHeight": 25
                }
            ]
        }
        widgets.push(conversionLimitReachedWidget)
        widgets.push(conversionLimitReachedWidget_2)
        return widgets
    }

    static getIcon(icon: string): string {
        return `/image/icons/referral/vertical/${icon}.png`
    }

    static getThemeOfVoucher(userContext: UserContext, vertical: Vertical): any {
        switch (vertical) {
            case "CARE_FIT":
                return {
                    style: {
                        backgroundColor: "rgb(242, 238, 255)",
                        shadowOpacity: 0.07,
                        shadowColor: "#000000",
                        shadowOffset: {
                            width: 0,
                            height: 2
                        },
                        shadowRadius: 5
                    },
                    headingContainer: {
                        backgroundColor: "rgba(177, 164, 220, 0.2)"
                    },
                    itemHeadTextStyle: {
                        color: "rgb(93, 113, 77)"
                    },
                    icon: ReferralUtil.getIcon("care"),
                    bottomIcon: ReferralUtil.getIcon("care-bottom"),
                    title: "care.fit"
                }
            case "CULT_FIT":
                return {
                    style: {
                        backgroundColor: "rgb(232, 247, 250)",
                        shadowOpacity: 0.07,
                        shadowColor: "#000000",
                        shadowOffset: {
                            width: 0,
                            height: 2
                        },
                        shadowRadius: 5
                    },
                    headingContainer: {
                        backgroundColor: "rgb(215, 242, 248)"
                    },
                    itemHeadTextStyle: {
                        color: "rgb(44, 124, 141)"
                    },
                    icon: ReferralUtil.getIcon("cult"),
                    bottomIcon: ReferralUtil.getIcon("cult-bottom"),
                    title: "cultpass"
                }
            case "EAT_FIT":
                return {
                    style: {
                        backgroundColor: "rgb(239, 246, 233)",
                        shadowOpacity: 0.07,
                        shadowColor: "#000000",
                        shadowOffset: {
                            width: 0,
                            height: 2
                        },
                        shadowRadius: 5
                    },
                    headingContainer: {
                        backgroundColor: "rgba(181, 209, 157, 0.2)"
                    },
                    itemHeadTextStyle: {
                        color: "rgb(93, 113, 77)"
                    },
                    icon: ReferralUtil.getIcon("eat"),
                    bottomIcon: ReferralUtil.getIcon("eat-bottom"),
                    title: "eat.fit"
                }

            case "LIVE_FIT":
                return {
                    style: {
                        backgroundColor: "#E8EFFA",
                        shadowOpacity: 0.07,
                        shadowColor: "#000000",
                        shadowOffset: {
                            width: 0,
                            height: 2
                        },
                        shadowRadius: 5
                    },
                    headingContainer: {
                        backgroundColor: "#D8E5F9"
                    },
                    itemHeadTextStyle: {
                        color: "#536CAC"
                    },
                    icon: ReferralUtil.getIcon("live_fit_2"),
                    bottomIcon: ReferralUtil.getIcon("live_fit-bottom-1"),
                    title: "cultpass"
                }
            case "TRANSFORM":
                return {
                    style: {
                        backgroundColor: "rgb(242, 238, 255)",
                        shadowOpacity: 0.07,
                        shadowColor: "#000000",
                        shadowOffset: {
                            width: 0,
                            height: 2
                        },
                        shadowRadius: 5
                    },
                    headingContainer: {
                        backgroundColor: "rgba(177, 164, 220, 0.2)"
                    },
                    itemHeadTextStyle: {
                        color: "rgb(93, 113, 77)"
                    },
                    icon: ReferralUtil.getIcon("care"),
                    bottomIcon: ReferralUtil.getIcon("care-bottom"),
                    title: "transform"
                }
        }
    }

    static getVoucherCardWidget(userContext: UserContext, data: Discovery): GiftVoucherWidget {
        const theme = ReferralUtil.getThemeOfVoucher(userContext, "CULT_FIT")
        return {
            widgetType: "REFER_TO_WIDGET",
            action: [
                {
                    actionType: "NAVIGATION",
                    url: `curefit://referralpage`
                }
            ],
            style: {
                marginTop: 25,
                ...theme.style
            },
            heading: {
                text: data.title,
                textStyle: {},
                icon: data.icon,
            },
            domain: {
                text: theme.title,
                textStyle: {},
                style: {},
            },
            headingContainer: {
                ...theme.headingContainer,
                ...{backgrounColor: "rgba(0,0,0,0.5)"}
            },
            itemHeadTextStyle: {
                ...theme.itemHeadTextStyle
            },
            itemDescriptionTextStyle: {
                color: "black"
            },
            items: data.items,
            footer: {
                icon: "/image/icons/referral/vertical/cult-bottom.png",
            },
        }
    }

    static getTermWidget(): ProductListCustomRowWidget {
        return {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "CUSTOM_ROW",
            noTopPadding: true,
            noBottomPadding: true,
            items: [
                {
                    text: "You can only refer friend who are new to cure.fit ",
                    icon: "PINK_TICK",
                    textStyle: {
                        fontWeight: "500"
                    }
                },
                {
                    text: "Your friend can only avail one of these offers",
                    icon: "PINK_TICK",
                    textStyle: {
                        fontWeight: "500"
                    }
                }
            ]
        }
    }

    static getWhatsAppReferralLinkCopy(whatsAppText: string, referrerCode: string): string {
        return mustache.render(whatsAppText, {referrerCode})
    }


    static isNewReferralPageSupported(userContext: UserContext) {
        if (AppUtil.isWeb(userContext)) {
            return true
        }
        const appVersion = userContext.sessionInfo.appVersion
        return appVersion >= 10.83
    }

    public static getBannerWidgetId() {
        return process.env.APP_ENV === "PRODUCTION" || process.env.APP_ENV === "ALPHA" ? "ff8bd91c-9c22-45bd-ada4-1f2b7af1713e" : "f12efeab-13ff-43fd-b159-f45f3300a072"
    }

    public static async getBannerCarouselWidget(logger: Logger, userContext: UserContext, interfaces: IServiceInterfaces, bannerWidgetId: string) {
        return this.getVmWidget(logger, userContext, interfaces, bannerWidgetId, "BANNER_CAROUSEL_WIDGET")
    }

    public static async getVmWidget(logger: Logger, userContext: UserContext, interfaces: CFServiceInterfaces | IServiceInterfaces, widgetId: string, widgetType: string) {
        let widget: WidgetWithMetric
        if (widgetId) {
            const widgetResponse = await interfaces.widgetBuilder.buildWidgets([widgetId], interfaces, userContext, {}, undefined)
            await logger.info("Referral Banner: " + JSON.stringify(widgetResponse))
            if (!_.isEmpty(widgetResponse) && !_.isEmpty(widgetResponse.widgets) && Array.isArray(widgetResponse.widgets)) {
                widget = !_.isEmpty(widgetResponse.widgets[0]) && widgetResponse.widgets[0].widgetType === widgetType ? widgetResponse.widgets[0] : undefined
            }
        }
        return widget
    }
}
