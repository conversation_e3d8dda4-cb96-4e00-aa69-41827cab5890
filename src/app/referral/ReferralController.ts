import { Container, inject } from "inversify"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import AuthMiddleware from "../auth/AuthMiddleware"
import kernel from "../../config/ioc/ioc"
import { BASE_TYPES, Logger } from "@curefit/base"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import * as express from "express"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { ErrorFactory } from "@curefit/error-client"
import { Action, ProductListWidget, WidgetView } from "../common/views/WidgetView"
import { Session, UserContext } from "@curefit/userinfo-common"
import { IReferralReadOnlyDao, IReferralService, REFERRAL_CLIENT_TYPES } from "@curefit/referral-client"
import { Referral, ReferrerInvite, ReferrerWithStatus } from "@curefit/referral-common"
import * as _ from "lodash"
import { CONSTELLO_CLIENT_TYPES, IGiftCardService } from "@curefit/constello-client"
import { GiftCardTransformationService } from "./myreferrals/GiftCardTransformationService"
import { ReferralTransformationService } from "./myreferrals/ReferralTransformationService"
import { MyReferral } from "./myreferrals/MyReferral"
import { MyReferralsDetailViewModel, MyReferralsEmptyViewModel, MyReferralsViewModel } from "@curefit/apps-common"
import MyReferralViewBuilder from "./myreferrals/MyReferralViewBuilder"
import { GiftCardUtil } from "./GiftCardUtil"
import { ErrorCodes } from "../error/ErrorCodes"
import { SortOrder } from "@curefit/mongo-utils"
import { IUserService, USER_CLIENT_TYPES } from "@curefit/user-client"
import ReferPageConfig, { ReferralPage } from "./ReferPageConfig"
import { ICaptchaBusiness } from "./CaptchaBusiness"
import { CULT_CLIENT_TYPES, ICultService } from "@curefit/cult-client"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { Discovery, ReferralUtil } from "./ReferralUtil"
import LiveUtil from "../util/LiveUtil"
import { ReferralBannerCarouselWidget } from "./ReferralViewBuilder"
import AppUtil from "../util/AppUtil"
import AuthUtil from "../util/AuthUtil"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { ReferralUtilV2 } from "./ReferralUtilV2"

export function ReferralControllerFactory(kernal: Container) {

    @controller("/referral", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class ReferralController {
        constructor(
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
            @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
            @inject(CUREFIT_API_TYPES.ReferPageConfig) protected referPageConfig: ReferPageConfig,
            @inject(CUREFIT_API_TYPES.CaptchaBusiness) private captchaBusiness: ICaptchaBusiness,
            @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
            @inject(CULT_CLIENT_TYPES.MindFitService) private mindFitService: ICultService,
            @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness,
            @inject(REFERRAL_CLIENT_TYPES.ReferralReadOnlyDao) private referralDao: IReferralReadOnlyDao,
            @inject(CONSTELLO_CLIENT_TYPES.GiftCardService) private giftCardService: IGiftCardService,
            @inject(CUREFIT_API_TYPES.GiftCardTransformationService) private giftCardTransformationService: GiftCardTransformationService,
            @inject(CUREFIT_API_TYPES.ReferralTransformationService) private referralTransformationService: ReferralTransformationService,
            @inject(CUREFIT_API_TYPES.MyReferralViewBuilder) private myReferralViewBuilder: MyReferralViewBuilder,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
            @inject(REFERRAL_CLIENT_TYPES.ReferralService) private referralService: IReferralService
        ) {
        }

        @httpGet("/invite/:code")
        async getInvitation(req: express.Request) {
            const referrerCode: string = req.params.code
            const userAgent: UserAgent = req.session.userAgent
            const referrerInvite: ReferrerInvite = await this.referralService.getInviteByReferrerId(referrerCode)
            const referralPage: ReferralPage = _.find(this.referPageConfig.referralPages, (referralPage) => {
                return referralPage.pageId === referrerInvite.referrer.referPageConfigId
            })
            const referralInvitePage = referralPage.data.referralInvitePage
            const image = userAgent === "DESKTOP" ? referralInvitePage.bannerImage.desktop : referralInvitePage.bannerImage.mweb
            const user = referrerInvite.user
            return {
                title: user.firstName ? `${user.firstName} ${user.lastName ?? ""} ${referralInvitePage.title}`
                    : `Your friend has ${referralInvitePage.title}`,
                subtitle: referralInvitePage.subTitle,
                imageUrl: image,
                countryId: referrerInvite.countryId
            }
        }

        @httpPost("/acceptInvite")
        async acceptInvitation(req: express.Request) {
            const body: any = req.body
            const captchaResponse = body.captchaResponse
            const userAgent: UserAgent = req.session.userAgent
            if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
                const verifyCaptchaResp: any = await this.captchaBusiness.verifyCaptchaLogin(captchaResponse, AppUtil.callSource(AuthUtil.getApiKeyFromReq(req)))
                if (!(verifyCaptchaResp.success && verifyCaptchaResp.score > 0.6)) {
                    this.logger.error(`ReferralController:Failed to verify captcha Error: ${verifyCaptchaResp["error-codes"][0]}`)
                    throw this.errorFactory.withCode(ErrorCodes.GENERIC_ERROR_WITH_MESSAGE, 400).withMeta({ message: verifyCaptchaResp["error-codes"][0] }).build()
                }
            }

            try {
                const acceptInviteResponse: Referral = await this.referralService.acceptInvite(body.referrerCode, body.phoneNumber)
                const referralPage: ReferralPage = _.find(this.referPageConfig.referralPages, (referralPage) => {
                    return referralPage.pageId === acceptInviteResponse.referPageConfigId
                })
                return {
                    title: referralPage.data.referralConfirmation.title,
                    subtitle: userAgent === "DESKTOP" ? referralPage.data.referralConfirmation.desktopSubTitle
                        : referralPage.data.referralConfirmation.subTitle
                }
            } catch (e) {
                this.logger.error(`ReferralController:acceptInvitation() Error: ${JSON.stringify(e)}`)
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_ERROR_WITH_MESSAGE, 400).withMeta({ message: e.message }).build()
            }
        }

        @httpGet("/hiw")
        public async getHowItWorks(request: express.Request): Promise<{ actions: Array<Action>, widgets: Array<ProductListWidget> }> {
            const userContext = request.userContext as UserContext
            const session: Session = request.session
            const userId: string = session.userId
            const source: string = request.query.source
            const contentId: string = request.query.contentId
            const showCTA: boolean = request.query.showCTA ? (request.query.showCTA.toLowerCase() === "true") : false

            let referralPage: ReferralPage
            const analyticsData = { eventKey: "button_click_event", eventData: { source: "referraltncpage", button_cta: "INVITE" } }

            switch (source) {
                case "cult_live":
                    referralPage = _.find(this.referPageConfig.referralPages, (referralPage) => {
                        return referralPage.pageId === "ReferPageConfig_CultLive"
                    })
                    const hiwCultLiveWidgets: ProductListWidget[] = ReferralUtil.getHiwWidgetsForReferrerWithEnabledStatus(referralPage.data.referralTermsForEnabled, referralPage.data.referralSteps, userContext)
                    return {
                        actions: showCTA ? [LiveUtil.getInviteBuddyLazyLoadAction(contentId, { source: source }, "INVITE", analyticsData)] : undefined,
                        widgets: hiwCultLiveWidgets
                    }
                default:
                    const referrerWithStatus: ReferrerWithStatus = await this.referralService.getReferrerByUserId(userId)
                    referralPage = _.find(this.referPageConfig.referralPages, (referralPage) => {
                        return referralPage.pageId === referrerWithStatus.referrer.referPageConfigId
                    })
                    this.logger.info(" Referral HIW Status:" +  JSON.stringify(referrerWithStatus))
                    this.logger.info(" Referral HIW Data:" +  JSON.stringify(referralPage))
                    const hiwWidgets: ProductListWidget[] = ReferralUtil.getHiwWidgets(referrerWithStatus, referralPage.data, userContext)
                    return {
                        actions: showCTA ? [LiveUtil.getInviteBuddyLazyLoadAction(contentId, { source: source }, "INVITE", analyticsData)] : undefined,
                        widgets: hiwWidgets
                    }
            }
        }

        @httpGet("/hiw/v2")
        public async getHowItWorksV2(request: express.Request): Promise<{ actions: Array<Action>, widgets: Array<ProductListWidget>, pageTitle: string }> {
            const userContext = request.userContext as UserContext
            const session: Session = request.session
            const userId: string = session.userId
            const source: string = request.query.source
            const contentId: string = request.query.contentId
            const showCTA: boolean = request.query.showCTA ? (request.query.showCTA.toLowerCase() === "true") : false

            let referralPage: ReferralPage
            const analyticsData = { eventKey: "button_click_event", eventData: { source: "fl_hiw_referral", button_cta: "INVITE" } }

            switch (source) {
                case "cult_live":
                    referralPage = _.find(this.referPageConfig.referralPages, (referralPage) => {
                        return referralPage.pageId === "ReferPageConfig_CultLive"
                    })
                    const hiwCultLiveWidgets: ProductListWidget[] = ReferralUtilV2.getHiwWidgetsForReferrerWithEnabledStatus(
                        referralPage.data.referralTermsForEnabled,
                        referralPage.data.referralSteps,
                        referralPage.data.processSteps,
                        referralPage.data.rewardSteps,
                        userContext)
                    return {
                        actions: showCTA ? [LiveUtil.getInviteBuddyLazyLoadAction(contentId, { source: source }, "INVITE", analyticsData)] : undefined,
                        widgets: hiwCultLiveWidgets,
                        pageTitle: "How referral works"
                    }
                default:
                    const referrerWithStatus: ReferrerWithStatus = await this.referralService.getReferrerByUserId(userId)
                    referralPage = _.find(this.referPageConfig.referralPages, (referralPage) => {
                        return referralPage.pageId === referrerWithStatus.referrer.referPageConfigId
                    })
                    this.logger.info(" Referral HIW Status:" +  JSON.stringify(referrerWithStatus))
                    this.logger.info(" Referral HIW Data:" +  JSON.stringify(referralPage))
                    const hiwWidgets: ProductListWidget[] = ReferralUtilV2.getHiwWidgets(referrerWithStatus, referralPage.data, userContext)
                    return {
                        actions: showCTA ? [LiveUtil.getInviteBuddyLazyLoadAction(contentId, { source: source }, "INVITE", analyticsData)] : undefined,
                        widgets: hiwWidgets,
                        pageTitle: "How referral works"
                    }
            }
        }

        @httpGet("/referrer/v2")
        public async getReferrerByUserIdV2(request: express.Request): Promise<{
            widgets: Array<WidgetView>,
            actionLayout?: string,
            actions?: Array<Action>,
            pageTitle?: string
        }> {
            const userContext = request.userContext as UserContext
            const session: Session = request.session
            const userId: string = session.userId
            const osName: string = userContext.sessionInfo.osName as string

            const [referrerWithStatus] = await Promise.all([
                this.referralService.getReferrerByUserId(userId),
                this.referralService.getReferralsByUserId(userId, {
                    startDate: "2019-12-17T17:30:00",
                    endDate: "2019-12-30T18:29:59",
                    referralStatus: "CONVERTED",
                    productTypes: ["FITNESS", "MIND", "PLAY", "LUX_FITNESS_PRODUCT"]
                })
            ])

            this.logger.info(" Referral Page Status:" +  JSON.stringify(referrerWithStatus))
            this.logger.info(" Referral Pages:" +  JSON.stringify(this.referPageConfig.referralPages))
            const widgets: any = []
            const referralPage: ReferralPage = _.find(this.referPageConfig.referralPages, (referralPage) => {
                return referralPage.pageId === referrerWithStatus.referrer.referPageConfigId
            })
            if (referrerWithStatus.status === "ENABLED" && !_.isEmpty(referralPage.data.bannerV2)) {
                const bannerData = referralPage.data.bannerV2
                const data: any = [{
                    image: bannerData.imageUrl,
                    action: bannerData.action
                }]
                const banner: ReferralBannerCarouselWidget = new ReferralBannerCarouselWidget(bannerData.layoutProps, 1, bannerData.layoutProps.edgeToEdge ?? true, bannerData.bannerRatio, data)
                widgets.push(banner)
            }
            if (!_.isEmpty(referralPage.data.benefitBanner)) {
                const bannerData = referralPage.data.benefitBanner
                const data: any = [{
                    image: bannerData.imageUrl,
                    action: bannerData.action
                }]
                const banner: ReferralBannerCarouselWidget = new ReferralBannerCarouselWidget(bannerData.layoutProps, 1, bannerData.layoutProps.edgeToEdge ?? false, bannerData.bannerRatio, data)
                widgets.push(banner)
            }

            const segments: string[] = referrerWithStatus.referrer ? referrerWithStatus.referrer.segments || [] : []
            const actions = ReferralUtilV2.getActionsForReferrer(referrerWithStatus, referralPage.data.whatsAppBannerUrl, referralPage.data.whatsAppLinkText, osName, userContext)
            const referrerWidgets = ReferralUtilV2.getWidgetsForReferrer(referrerWithStatus, segments, referralPage.data)
            let finalWidgets
            if (referrerWidgets) {
                finalWidgets = widgets.concat(referrerWidgets)
            } else {
                finalWidgets = widgets
            }
            return {
                widgets: finalWidgets,
                actionLayout: "vertical",
                actions: actions,
                pageTitle: ""
            }
        }

        @httpGet("/referrer")
        public async getReferrerByUserId(request: express.Request): Promise<{
            widgets: Array<WidgetView>,
            actionLayout?: string,
            actions?: Array<Action>
        }> {
            const userContext = request.userContext as UserContext
            const session: Session = request.session
            const userId: string = session.userId
            const osName: string = userContext.sessionInfo.osName as string
            this.logger.info("Fetching referer doc for User " + userId)

            const [referrerWithStatus, referrals] = await Promise.all([
                this.referralService.getReferrerByUserId(userId),
                this.referralService.getReferralsByUserId(userId, {
                    startDate: "2019-12-17T17:30:00",
                    endDate: "2019-12-30T18:29:59",
                    referralStatus: "CONVERTED",
                    productTypes: ["FITNESS", "MIND", "PLAY", "LUX_FITNESS_PRODUCT"]
                })
            ])

            this.logger.info(" Referral Page Status:" +  JSON.stringify(referrerWithStatus))
            const widgets: any = []
            const referralPage: ReferralPage = _.find(this.referPageConfig.referralPages, (referralPage) => {
                return referralPage.pageId === referrerWithStatus.referrer.referPageConfigId
            })
            this.logger.info(" Referral Pages:" +  JSON.stringify(this.referPageConfig.referralPages))

            if (!_.isEmpty(referralPage.data.banner)) {
                const bannerData = referralPage.data.banner
                const data: any = [{
                    image: bannerData.imageUrl,
                    action: bannerData.action
                }]
                const banner: ReferralBannerCarouselWidget = new ReferralBannerCarouselWidget(bannerData.layoutProps, 1, false, bannerData.bannerRatio, data)
                widgets.push(banner)
            }

            let miniChallengeBanner: ReferralBannerCarouselWidget
            if (!_.isEmpty(referralPage.data.miniChallengeBanner) && referrerWithStatus.status === "ENABLED") {
                const miniChallengeBannerData = referralPage.data.miniChallengeBanner
                const { layoutProps, bannerRatio, images } = miniChallengeBannerData

                let imageIndex = 0
                if (!_.isEmpty(referrals) && referrals.length < images.length) {
                    imageIndex = referrals.length
                } else if (!_.isEmpty(referrals) && referrals.length >= images.length) {
                    imageIndex = images.length - 1 // just showing 4 max referral, handling out of bounds error more than 4 referrals, though highly unlikely
                }

                const miniChallengeData: any = [{
                    image: images[imageIndex],
                    action: ""
                }]

                miniChallengeBanner = new ReferralBannerCarouselWidget(layoutProps, 1, false, bannerRatio, miniChallengeData)
            }

            const segments: string[] = referrerWithStatus.referrer ? referrerWithStatus.referrer.segments || [] : []
            const referrerWidgets = ReferralUtil.getWidgetsForReferrer(referrerWithStatus.status, segments, referralPage.data)
            let finalWidgets
            if (referrerWidgets) {
                finalWidgets = widgets.concat(referrerWidgets)
            } else {
                finalWidgets = widgets
            }
            /* adding hack banner for fitstart Dec 2019 */
            miniChallengeBanner && finalWidgets.push(miniChallengeBanner)
            const actions = ReferralUtil.getActionsForReferrer(referrerWithStatus, referralPage.data.whatsAppBannerUrl, referralPage.data.whatsAppLinkText, osName, userContext)
            await this.logger.info("Referral Page Response : " + JSON.stringify(finalWidgets))
            await this.logger.info("Referral Page Actions : " + JSON.stringify(actions))
            return {
                widgets: finalWidgets,
                actionLayout: "vertical",
                actions: actions
            }
        }

        @httpGet("/referrerItems")
        public async getReferrerItemsByUserId(request: express.Request): Promise<{
            widgets: Array<WidgetView>,
            actionLayout?: string,
            actions?: Array<Action>
        }> {
            const session: Session = request.session
            const userId: string = session.userId
            const userContext: UserContext = request.userContext as UserContext
            const { countryCallingCode } = await userContext.userPromise
            const { policies } = await this.giftCardService.getAllApplicablePolicies(
                userId,
                GiftCardUtil.getCountryFromPhone(countryCallingCode)
            )
            const referralPage: ReferralPage = _.find(this.referPageConfig.referralPages, (referralPage) => {
                return referralPage.pageId === "ReferPageConfig"
            })
            const defaultDiscovery: Discovery = {
                icon: "/image/icons/referral/vertical/cult_new_2.png",
                title: "Gift ₹1000 off",
                items: [
                    {
                        description: "₹1000 OFF to your friend on cult packs above ₹4000",
                        head: "Gift"
                    },
                    {
                        description: "45 day extension if they buy within 30 days",
                        head: "Earn"
                    },
                ]
            }

            const fWidgets = [
                ...policies
                    .filter(p => p.policy.vertical === "CULT_FIT" || !(["tvos", "ios"].includes(userContext.sessionInfo?.osName?.toLowerCase()) && p.policy.vertical === "LIVE_FIT"))
                    .map(policyEntity => GiftCardUtil.getVoucherCardWidget(userContext, policyEntity.policyIdentifier.campaignId, policyEntity.policy)),
                ReferralUtil.getVoucherCardWidget(userContext, referralPage ? referralPage.data.discovery : defaultDiscovery)
            ]
            await this.logger.info("Referral Detail Page Response : " + JSON.stringify(fWidgets))
            return {
                widgets: fWidgets
            }
        }

        @httpGet("/myreferrals")
        public async getReferrals(request: express.Request): Promise<MyReferralsViewModel[] | MyReferralsEmptyViewModel> {
            const userId = request.session.userId
            const tz = request.userContext.userProfile.timezone
            const [giftCardResponse, referralResponse] = await Promise.all([
                this.giftCardService.getAllConsumedCards(userId),
                this.referralDao.find({
                    condition: { userId },
                    sortField: "createdDate",
                    sortOrder: SortOrder.DESC
                })
            ])
            if (!giftCardResponse.success) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Error fetching cards for userId: " + userId).build()
            }
            const giftCardReferrals = await this.giftCardTransformationService.transformReferralsResponse(giftCardResponse.data, tz)
            const referrals: MyReferral[] = giftCardReferrals
                .concat(await this.referralTransformationService.transformReferralsResponse(referralResponse))
                .filter(_ => new Date(_.activationDate).getTime() >= new Date("2019-07-01T00:00").getTime())
            return this.myReferralViewBuilder.buildMyReferralCardView(request.userContext, referrals, tz)
        }

        @httpGet("/myreferrals/:id")
        public async getReferralDetails(request: express.Request): Promise<MyReferralsDetailViewModel> {
            const userId = request.session.userId
            const system = request.query.system
            const id = request.params.id
            const tz = request.userContext.userProfile.timezone
            if (_.isNil(system) || _.isNil(id)) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("System and id should be present").build()
            }
            if (!_.includes(["REFERRAL", "GIFT_CARDS"], system)) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Not a valid system").build()
            }
            let myReferral: MyReferral
            if (system === "REFERRAL") {
                const referral = await this.referralDao.findOne({ referralId: id })
                if (referral.userId !== userId) {
                    throw this.errorFactory.withCode(ErrorCodes.REFFERAL_NOT_VALID_FOR_USER_ERR, 400).withDebugMessage("Referral not applicable to user").build()
                }
                myReferral = (await this.referralTransformationService.transformReferralsResponse(
                    [referral] // TODO: can throw error if id is invalid
                )).find(_ => true)
            } else {
                const { data, success } = await this.giftCardService.getGiftCard(id)
                if (!success) {
                    throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Error getting gift cards for userId: " + userId + " with id: " + id).build()
                }
                if (_.isEmpty(data) || _.isEmpty(data[0].cards) || data[0].cards[0].userId !== userId) {
                    throw this.errorFactory.withCode(ErrorCodes.REFFERAL_NOT_VALID_FOR_USER_ERR, 400).withDebugMessage("Referral not applicable to user").build()
                }
                myReferral = (await this.giftCardTransformationService.transformReferralsResponse(data, tz))
                    .find(_ => _.referralId === id) // TODO: will be undefined of id is invalid
            }
            return this.myReferralViewBuilder.buildMyReferralDetailCardView(request.userContext, myReferral, tz)
        }
    }

    return ReferralController
}

export default ReferralControllerFactory
