import { ReferrerStatus, ReferrerWithStatus } from "@curefit/referral-common"
import { ItemList, ReferralPageData, ReferralTermsWithStatus } from "./ReferPageConfig"
import {
    Action, CenterHeaderWidget, CfMediaWidget,
    FormattedTextWidget,
    GiftVoucherWidget,
    ProductListWidget,
    ShareActionWidgetV2
} from "../common/views/WidgetView"
import { UserContext } from "@curefit/userinfo-common"
import { Vertical } from "@curefit/base-common"
import * as mustache from "mustache"
import AppUtil from "../util/AppUtil"
import { Logger } from "@curefit/base"
import { IServiceInterfaces } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { WidgetWithMetric } from "@curefit/vm-common"
import * as _ from "lodash"
import { Discovery } from "./ReferralUtil"

export class ReferralUtilV2 {

    static getWidgetsForReferrer(referrerWithStatus: ReferrerWithStatus, segments: string[], referralPageData: ReferralPageData): (FormattedTextWidget | ProductListWidget| ShareActionWidgetV2 | CenterHeaderWidget | CfMediaWidget)[] {
        switch (referrerWithStatus.status) {
            case "ENABLED":
                return this.getWidgetsForReferrerWithEnabledStatus(referralPageData, referrerWithStatus)
            case "NOT_AVAILABLE":
            case "BLACKLISTED":
                return this.getWidgetsForReferrerWithBlacklistedStatus()
            case "CONVERSION_MAX_LIMIT_REACHED":
                return this.getWidgetsForReferrerWithMaxLimitReachedStatus(referralPageData.referralLink)
            case "NUMBER_NOT_APPLICABLE":
                return this.getWidgetForReferrerWithNonApplicableNumber()
            default:
                return null
        }
    }

    static getActionsForReferrer(referrerWithStatus: ReferrerWithStatus, whatsAppBannerUrl: string, whatsAppLinkText: string, osName: string, userContext: UserContext): Action[] {
        switch (referrerWithStatus.status) {
            case "ENABLED":
                return this.getActionsForReferrerWithEnabledStatus(whatsAppBannerUrl, whatsAppLinkText, referrerWithStatus.referrer.referrerCode, osName, userContext)
            case "NOT_AVAILABLE":
                return this.getActionsForReferrerWithNotAvailableStatus()
            case "BLACKLISTED":
                return this.getActionsForReferrerWithBlacklistedStatus()
            case "CONVERSION_MAX_LIMIT_REACHED":
                return this.getActionsForReferrerWithMaxLimitReachedStatus()
            default:
                return []
        }
    }

    static getHiwWidgets(referrerWithStatus: ReferrerWithStatus, referralPageData: ReferralPageData, userContext: UserContext): ProductListWidget[] {
        switch (referrerWithStatus.status) {
            case "ENABLED":
            case "NOT_AVAILABLE":
            case "BLACKLISTED":
                return this.getHiwWidgetsForReferrerWithEnabledStatus(referralPageData.referralTermsForEnabled, null, referralPageData.processSteps, referralPageData.rewardSteps, userContext)
            case "CONVERSION_MAX_LIMIT_REACHED":
                return this.getHiwWidgetsForReferrerWithMaxLimitReachedStatus(referralPageData.referralTermsForEnabled, userContext)
            default:
                return []
        }
    }

    static getHiwWidgetsForReferrerWithEnabledStatus(referralTermsWithStatus: ReferralTermsWithStatus, referralSteps: { icon: string, subTitle: string }[], processSteps: ItemList[], rewardSteps: ItemList[], userContext: UserContext): ProductListWidget[] {
        const isNewFlowSupported: boolean = this.isNewReferralPageSupported(userContext)
        const widgets = []
        let rewardWidget: ProductListWidget
        let processWidget: ProductListWidget
        let hiwWidget: ProductListWidget
        if (referralSteps)
            hiwWidget = {
                widgetType: "PRODUCT_LIST_WIDGET",
                type: "SMALL",
                items: referralSteps,
                collapsable: false,
                iconBackgroundOpacity: 0,
                hideSepratorLines: true,
                noTopPadding: true,
                header: undefined,
                layoutProps: {
                    spacing: {
                        top: 100,
                        bottom: 20
                    }
                }
            }
        if (processSteps)
            processWidget = {
                widgetType: "PRODUCT_LIST_WIDGET",
                type: "SMALL",
                items: processSteps,
                iconBackgroundOpacity: 0,
                hideSepratorLines: true,
                collapsable: false,
                noTopPadding: true,
                header: {
                    title: "The Process",
                    color: "#ffffff"
                },
                layoutProps: {
                    spacing: {
                        top: 100
                    }
                }
            }
        if (rewardSteps)
            rewardWidget = {
                widgetType: "PRODUCT_LIST_WIDGET",
                collapsable: false,
                type: "SMALL",
                items: rewardSteps,
                iconBackgroundOpacity: 0,
                hideSepratorLines: true,
                noTopPadding: true,
                header: {
                    title: "The Reward",
                    color: "#ffffff"
                },
            }
        const referralTnc: ProductListWidget = {
            widgetType: "PRODUCT_LIST_WIDGET",
            hideSepratorLines: true,
            collapsable: false,
            type: "NUMBERED",
            footerAction: isNewFlowSupported ? {
                title: referralTermsWithStatus.tnc.title,
                url: referralTermsWithStatus.tnc.actionLink,
                actionType : "NAVIGATION",
                variant: "tertiary"
            } : null,
            footer: {
                title: referralTermsWithStatus.tnc.title,
                action: referralTermsWithStatus.tnc.actionLink
            },
            backgroundColor: referralTermsWithStatus.termsAndConditionsProperties.backgroundColor,
            header: {
                title: referralTermsWithStatus.termsAndConditionsTitle,
                color: referralTermsWithStatus.termsAndConditionsProperties.titleColor
            },
            items: referralTermsWithStatus.termsAndConditionsItemList
        }
        if (hiwWidget)
            widgets.push(hiwWidget)
        if (processWidget)
            widgets.push(processWidget)
        if (rewardWidget)
            widgets.push(rewardWidget)
        if (referralTnc)
            widgets.push(referralTnc)
        return widgets
    }

    static getHiwWidgetsForReferrerWithMaxLimitReachedStatus(referralTermsWithStatus: ReferralTermsWithStatus, userContext: UserContext): ProductListWidget[] {
        const widgets = []
        const tncItems = [
            {
                subTitle: "You can earn no more than Fitcash worth Rs 1500 through referrals. Don’t worry, this is a temporary limit & we’ll notify you when it is relaxed",
                number: "1",
                padding: 0,
                subTitleFont: "P5",
                subTitleColor: "#E0FFFFFF",
            },
            {
                subTitle: "Your friends will no longer be able to accept your referral invite",
                number: "2",
                padding: 0,
                subTitleFont: "P5",
                subTitleColor: "#E0FFFFFF",
            },
            {
                subTitle: "Friends who have already accepted your referral invite can still avail the referral offer. However, you will not earn any referral benefits for it.",
                number: "3",
                padding: 0,
                subTitleFont: "P5",
                subTitleColor: "#E0FFFFFF",
            },
            {
                subTitle: "You can only earn benefits for referrals accepted after this referral limit is relaxed",
                number: "4",
                padding: 0,
                subTitleFont: "P5",
                subTitleColor: "#E0FFFFFF",
            }
        ]
        const referralTnc: ProductListWidget = {
            widgetType: "PRODUCT_LIST_WIDGET",
            hideSepratorLines: true,
            type: "NUMBERED",
            footer: {
                title: referralTermsWithStatus.tnc.title,
                action: referralTermsWithStatus.tnc.actionLink
            },
            layoutProps: {
                spacing: {
                    top: 60
                }
            },
            iconBackgroundOpacity: 0,
            backgroundColor: "rgb(248, 248, 252)",
            header: undefined,
            items: tncItems
        }
        widgets.push(referralTnc)
        return widgets
    }

    static getActionsForReferrerWithEnabledStatus(whatsAppBannerUrl: string, whatsAppLinkText: string, referrerCode: string, osName: string, userContext: UserContext): Action[] {
        const isNewFlowSupported: boolean = this.isNewReferralPageSupported(userContext)
        const isAndroid: boolean = osName === "android"
        const shareAction: Action = {
            title: "Share Your Link",
            actionType: "SHARE_ACTION",
            meta: {
                shareOptions: {
                    url: whatsAppBannerUrl,
                    type: "image/png",
                    message: this.getWhatsAppReferralLinkCopy(whatsAppLinkText, referrerCode),
                    title: "Curefit referrals",
                    whatsAppNumber: isAndroid ?  "" : undefined
                },
                shareChannel: isAndroid ? "WHATSAPP" : undefined
            }
        }
        if (isAndroid) {
            shareAction.iconUrl = "/image/icons/referral/whatsapp.png"
            shareAction.title = "WhatsApp Your Link"
        }
        return [
            {
                title: "How it works",
                viewType: "LINK",
                actionType: "NAVIGATION",
                url: isNewFlowSupported ? "curefit://fl_hiw_referral" : "curefit://referraltncpage",
                meta: {
                    title: "How referral works"
                }
            },
            shareAction
        ]
    }

    static getActionsForReferrerWithNotAvailableStatus(): Action[] {
        return [
            {
                title: "How it works",
                viewType: "LINK",
                actionType: "NAVIGATION",
                url: "curefit://referraltncpage",
                meta: {
                    title: "How referral works"
                }
            },
            {
                title: "Notify Me",
                actionType: "REFERRAL_NOTIFY",
                meta: {
                    message: "Thanks for your interest!",
                    subtitle: `We will notify you once we make 'Refer a Friend' available for you.`,
                    notifySegment: "NOT_AVAILABLE"
                }
            }
        ]
    }

    static getActionsForReferrerWithBlacklistedStatus(): Action[] {
        return [
            {
                title: "What does this mean?",
                viewType: "LINK",
                actionType: "NAVIGATION",
                url: "curefit://referraltncpage",
                meta: {
                    title: "All about referrals"
                }
            },
            {
                title: "Notify Me",
                actionType: "REFERRAL_NOTIFY",
                meta: {
                    message: "Thanks for your interest!",
                    subtitle: "We will notify you once we make 'Refer a Friend' available for you.",
                    notifySegment: "BLACKLISTED"
                }
            }
        ]
    }

    static getActionsForReferrerWithMaxLimitReachedStatus(): Action[] {
        return [
            {
                title: "What is referral limit?",
                viewType: "LINK",
                actionType: "NAVIGATION",
                url: "curefit://referraltncpage",
                meta: {
                    title: "All about referrals"
                }
            },
            {
                title: "Notify Me",
                actionType: "REFERRAL_NOTIFY",
                meta: {
                    message: "Thanks for your interest!",
                    subtitle: "We will notify you once we make 'Refer a Friend' available for you.",
                    notifySegment: "CONVERSION_MAX_LIMIT_REACHED"
                }
            }
        ]
    }

    static getWidgetsForReferrerWithEnabledStatus(referralPageData: ReferralPageData, referrerStatus: ReferrerWithStatus): (ShareActionWidgetV2)[] {
        const widgets = []
        const actionWidget: ShareActionWidgetV2 = {
            widgetType: "SHARE_ACTION_WIDGET_V2",
            layoutProps: {
                spacing: {
                    top: "40",
                    bottom: "20"
                }
            },
            showCopyIcon: false,
            referralLink: {
                text: "https://www.cult.fit/referral/invite?referralCode=" + referrerStatus.referrer.referrerCode,
                color: "#00BEFF",
                maxLine: "1",
                alignment: "left",
                typeScale: "H4",
                letterSpacing: 0,
                richText: false
            },
            actionList : [
                {
                    title: "SHARE YOUR CODE",
                    actionType: "SHARE_ACTION",
                    variant: "primary",
                    meta: {
                        shareOptions: {
                            url: referralPageData.whatsAppBannerUrl,
                            type: "image/png",
                            message: this.getWhatsAppReferralLinkCopy(referralPageData.whatsAppLinkText, referrerStatus.referrer.referrerCode),
                            title: "Curefit referrals",
                            whatsAppNumber: ""
                        },
                        shareChannel: "WHATSAPP",
                        analyticsData: [
                            {
                                type : "REFER_A_FRIEND"
                            }
                        ]
                    },
                },
                {
                    title: "HOW IT WORKS",
                    variant: "tertiary",
                    viewType: "LINK",
                    actionType: "NAVIGATION",
                    url: "curefit://fl_hiw_referral",
                    meta: {
                        title: "How referral works"
                    }
                }
            ]
        }
        widgets.push(actionWidget)
        return widgets
    }

    static getWidgetForReferrerWithNonApplicableNumber(): (CfMediaWidget | CenterHeaderWidget)[] {
        const widgets = []
        const mediaWidget: CfMediaWidget = {
            widgetType: "CF_MEDIA_WIDGET",
            hasDivideBelow: false,
            hasDividerTop: false,
            mediaData: {
                mediaUrl: "/image/icons/referral/referral_banned.png",
                type: "image",
                width: 100,
                height: 100,
                topPadding: 220,
                bottomPadding: 10
            }
        }
        const contentWidget: CenterHeaderWidget = {
            widgetType: "CENTER_HEADER_WIDGET",
            layoutProps: {
                spacing: {
                    topPadding: 10,
                    bottomPadding: 70
                }
            },
            title: {
                text: "Refer a friend is\nunavailable",
                color: "#FFFFFF",
                maxLine: "4",
                alignment: "center",
                typeScale: "H9",
                letterSpacing: 0,
                richText: false
            },
            subtitle: {
                text: "\nThe Refer a Friend program is now exclusively available to users who have an Indian mobile number.",
                color: "#66FFFFFF",
                opacity: 0.6,
                maxLine: "4",
                alignment: "center",
                typeScale: "P5",
                letterSpacing: 0,
                richText: false
            }
        }
        widgets.push(mediaWidget)
        widgets.push(contentWidget)
        return widgets
    }

    static getWidgetsForReferrerWithBlacklistedStatus(): (CenterHeaderWidget | CfMediaWidget | ShareActionWidgetV2)[] {
        const widgets = []
        const mediaWidget: CfMediaWidget = {
            widgetType: "CF_MEDIA_WIDGET",
            hasDivideBelow: false,
            hasDividerTop: false,
            mediaData: {
                mediaUrl: "/image/icons/referral/referral_banned.png",
                type: "image",
                width: 100,
                height: 100,
                topPadding: 220,
                bottomPadding: 10
            }
        }
        const contentWidget: CenterHeaderWidget = {
            widgetType: "CENTER_HEADER_WIDGET",
            layoutProps: {
                spacing: {
                    topPadding: 10,
                    bottomPadding: 70
                }
            },
            title: {
                text: "Refer a friend is\ncurrently unavailable",
                color: "#FFFFFF",
                maxLine: "4",
                alignment: "center",
                typeScale: "H9",
                letterSpacing: 0,
                richText: false
            },
            subtitle: {
                text: "\nRefer a friend is now only available to a select set of users. Please click on “Notify Me” button below and we’ll notify you when it is available for you",
                color: "#66FFFFFF",
                opacity: 0.6,
                maxLine: "4",
                alignment: "center",
                typeScale: "P5",
                letterSpacing: 0,
                richText: false
            }
        }
        const actionWidget: ShareActionWidgetV2 = {
            widgetType: "SHARE_ACTION_WIDGET_V2",
            layoutProps: {
                spacing: {
                    top: "40",
                    bottom: "20"
                }
            },
            title: {
                text: "Get notified when you can start referring again",
                color: "#99FFFFFF",
                opacity: 0.6,
                maxLine: "2",
                alignment: "center",
                typeScale: "P5",
                letterSpacing: 0,
                richText: false
            },
            actionList : [
                {
                    title: "NOTIFY ME",
                    actionType: "NAVIGATION",
                    url: "curefit://fl_referral_notify",
                    variant: "primary",
                    meta: {
                        title: "Thanks for your interest!",
                        description: "We will notify you once we make 'Refer a Friend' available for you.",
                        notifySegment: "NOT_AVAILABLE"
                    }
                },
                // {
                //     title: "WHAT DOES THIS MEAN",
                //     variant: "tertiary",
                //     viewType: "LINK",
                //     actionType: "NAVIGATION",
                //     url: "curefit://referraltncpage",
                //     meta: {
                //         title: "All about referrals"
                //     }
                // }
            ]
        }
        widgets.push(mediaWidget)
        widgets.push(contentWidget)
        widgets.push(actionWidget)
        return widgets

    }

    static getWidgetsForReferrerWithMaxLimitReachedStatus(referralLink?: string): (FormattedTextWidget | ProductListWidget | ShareActionWidgetV2)[] {
        const widgets = []
        const actionWidget: ShareActionWidgetV2 = {
            widgetType: "SHARE_ACTION_WIDGET_V2",
            layoutProps: {
                spacing: {
                    top: "40",
                    bottom: "20"
                }
            },
            title: {
                text: "You’ve reached the limit to send referrals ",
                color: "#FFFFFF",
                opacity: 0.6,
                maxLine: "2",
                alignment: "center",
                typeScale: "TAG_TEXT",
                letterSpacing: 0,
                richText: false
            },
            referralLink: {
                text: referralLink ?? "",
                color: "#00BEFF",
                maxLine: "1",
                alignment: "left",
                typeScale: "H4",
                letterSpacing: 0,
                richText: false
            },
            actionList : [
                {
                    title: "SHARE YOUR CODE",
                    actionType: "NAVIGATION",
                    variant: "primary",
                    isEnabled: false,
                },
                {
                    title: "HOW IT WORKS",
                    variant: "tertiary",
                    viewType: "LINK",
                    actionType: "NAVIGATION",
                    url: "curefit://fl_hiw_referral",
                    meta: {
                        title: "How referral works"
                    }
                }
            ]
        }
        widgets.push(actionWidget)
        return widgets
    }

    static getIcon(icon: string): string {
        return `/image/icons/referral/vertical/${icon}.png`
    }

    static getThemeOfVoucher(userContext: UserContext, vertical: Vertical): any {
        switch (vertical) {
            case "CARE_FIT":
                return {
                    style: {
                        backgroundColor: "rgb(242, 238, 255)",
                        shadowOpacity: 0.07,
                        shadowColor: "#000000",
                        shadowOffset: {
                            width: 0,
                            height: 2
                        },
                        shadowRadius: 5
                    },
                    headingContainer: {
                        backgroundColor: "rgba(177, 164, 220, 0.2)"
                    },
                    itemHeadTextStyle: {
                        color: "rgb(93, 113, 77)"
                    },
                    icon: ReferralUtilV2.getIcon("care"),
                    bottomIcon: ReferralUtilV2.getIcon("care-bottom"),
                    title: "care.fit"
                }
            case "CULT_FIT":
                return {
                    style: {
                        backgroundColor: "rgb(232, 247, 250)",
                        shadowOpacity: 0.07,
                        shadowColor: "#000000",
                        shadowOffset: {
                            width: 0,
                            height: 2
                        },
                        shadowRadius: 5
                    },
                    headingContainer: {
                        backgroundColor: "rgb(215, 242, 248)"
                    },
                    itemHeadTextStyle: {
                        color: "rgb(44, 124, 141)"
                    },
                    icon: ReferralUtilV2.getIcon("cult"),
                    bottomIcon: ReferralUtilV2.getIcon("cult-bottom"),
                    title: "cultpass"
                }
            case "EAT_FIT":
                return {
                    style: {
                        backgroundColor: "rgb(239, 246, 233)",
                        shadowOpacity: 0.07,
                        shadowColor: "#000000",
                        shadowOffset: {
                            width: 0,
                            height: 2
                        },
                        shadowRadius: 5
                    },
                    headingContainer: {
                        backgroundColor: "rgba(181, 209, 157, 0.2)"
                    },
                    itemHeadTextStyle: {
                        color: "rgb(93, 113, 77)"
                    },
                    icon: ReferralUtilV2.getIcon("eat"),
                    bottomIcon: ReferralUtilV2.getIcon("eat-bottom"),
                    title: "eat.fit"
                }

            case "LIVE_FIT":
                return {
                    style: {
                        backgroundColor: "#E8EFFA",
                        shadowOpacity: 0.07,
                        shadowColor: "#000000",
                        shadowOffset: {
                            width: 0,
                            height: 2
                        },
                        shadowRadius: 5
                    },
                    headingContainer: {
                        backgroundColor: "#D8E5F9"
                    },
                    itemHeadTextStyle: {
                        color: "#536CAC"
                    },
                    icon: ReferralUtilV2.getIcon("live_fit_2"),
                    bottomIcon: ReferralUtilV2.getIcon("live_fit-bottom-1"),
                    title: "cultpass"
                }
            case "TRANSFORM":
                return {
                    style: {
                        backgroundColor: "rgb(242, 238, 255)",
                        shadowOpacity: 0.07,
                        shadowColor: "#000000",
                        shadowOffset: {
                            width: 0,
                            height: 2
                        },
                        shadowRadius: 5
                    },
                    headingContainer: {
                        backgroundColor: "rgba(177, 164, 220, 0.2)"
                    },
                    itemHeadTextStyle: {
                        color: "rgb(93, 113, 77)"
                    },
                    icon: ReferralUtilV2.getIcon("care"),
                    bottomIcon: ReferralUtilV2.getIcon("care-bottom"),
                    title: "transform"
                }
        }
    }

    static getVoucherCardWidget(userContext: UserContext, data: Discovery): GiftVoucherWidget {
        const theme = ReferralUtilV2.getThemeOfVoucher(userContext, "CULT_FIT")
        return {
            widgetType: "REFER_TO_WIDGET",
            action: [
                {
                    actionType: "NAVIGATION",
                    url: `curefit://referralpage`
                }
            ],
            style: {
                marginTop: 25,
                ...theme.style
            },
            heading: {
                text: data.title,
                textStyle: {},
                icon: data.icon,
            },
            domain: {
                text: theme.title,
                textStyle: {},
                style: {},
            },
            headingContainer: {
                ...theme.headingContainer,
                ...{backgrounColor: "rgba(0,0,0,0.5)"}
            },
            itemHeadTextStyle: {
                ...theme.itemHeadTextStyle
            },
            itemDescriptionTextStyle: {
                color: "black"
            },
            items: data.items,
            footer: {
                icon: "/image/icons/referral/vertical/cult-bottom.png",
            },
        }
    }

    static compareSegments(array1: string[], array2: string[]): boolean {
        if (!array1 && !array2) {
            return true
        }
        if (!array1 || !array2) {
            return false
        }
        if (array1.length != array2.length) {
            return false
        }
        for (const arr of array1) {
            if (array2.indexOf(arr) === -1) {
                return false
            }
        }
        return true
    }

    static getWhatsAppReferralLinkCopy(whatsAppText: string, referrerCode: string): string {
        return mustache.render(whatsAppText, {referrerCode})
    }

    static isNewReferralPageSupported(userContext: UserContext) {
        if (AppUtil.isWeb(userContext)) {
            return true
        }
        const appVersion = userContext.sessionInfo.appVersion
        return appVersion >= 10.82
    }

    public static getBannerWidgetId() {
        return process.env.APP_ENV === "PRODUCTION" || process.env.APP_ENV === "ALPHA" ? "ff8bd91c-9c22-45bd-ada4-1f2b7af1713e" : "f12efeab-13ff-43fd-b159-f45f3300a072"
    }

    public static async getBannerCarouselWidget(logger: Logger, userContext: UserContext, interfaces: IServiceInterfaces, bannerWidgetId: string) {
        return this.getVmWidget(logger, userContext, interfaces, bannerWidgetId, "BANNER_CAROUSEL_WIDGET")
    }

    public static async getVmWidget(logger: Logger, userContext: UserContext, interfaces: CFServiceInterfaces | IServiceInterfaces, widgetId: string, widgetType: string) {
        let widget: WidgetWithMetric
        if (widgetId) {
            const widgetResponse = await interfaces.widgetBuilder.buildWidgets([widgetId], interfaces, userContext, {}, undefined)
            await logger.info("Referral Banner: " + JSON.stringify(widgetResponse))
            if (!_.isEmpty(widgetResponse) && !_.isEmpty(widgetResponse.widgets) && Array.isArray(widgetResponse.widgets)) {
                widget = !_.isEmpty(widgetResponse.widgets[0]) && widgetResponse.widgets[0].widgetType === widgetType ? widgetResponse.widgets[0] : undefined
            }
        }
        return widget
    }
}