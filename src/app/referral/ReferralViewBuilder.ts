import { WidgetType, WidgetView } from "../common/views/WidgetView"

interface BannerCarouselData {
    webImage: string
    image: string
}

export class ReferralBannerCarouselWidget implements WidgetView {
    public widgetType: WidgetType = "BANNER_CAROUSEL_WIDGET"

    constructor(public layoutProps: any, public maxNumBanners: number, public edgeToEdge: boolean, public bannerRatio: string, public data: BannerCarouselData[]) {
    }
}
