import { inject, injectable } from "inversify"
import { Logger, BASE_TYPES } from "@curefit/base"
import { FetchUtil } from "@curefit/base"
import { Response } from "node-fetch"
import { OrderSource } from "@curefit/base-common"
const fetch = require("node-fetch")

const captchaVerificationUrl = "https://www.google.com/recaptcha/api/siteverify"
const STAGE_CAPTCHA_SECRET = "6LdYI38UAAAAAFbVNyfhXWFqbzgXc3rxlW9lWQBp"
const PROD_CAPTCHA_SECRET = "6LfeLX8UAAAAAPCG6276U6bWSNoSiMoUoi_SAclx"
const CAPTCHA_SECRET_LOGIN = "6LeggMUhAAAAAClbesaDeKBMhrGXfIubrywpF0i2"
const CULTSPORT_CAPTCHA_SECRET_LOGIN = "6Lew_IceAAAAAKiHxa_lbXmBC1eSOcT_Z42<PERSON>hel"
const CULTSPORT_CAPTCHA_v3_SECRET_LOGIN = "6LcxaykhAAAAAHS8TmFy-orsmkU91KNbPU3wiqz4"
const CULTSTORE_CAPTCHA_v3_SECRET_LOGIN = "6Lfi0TEqAAAAAFn8H8gHPBqUOVaCm5F0TxThUd2H"
const SUGARFIT_CAPTCHA_SECRET_LOGIN = "6Ldhak8mAAAAAMPr5-axZBbSxSCQ7uMRdOo3xFo8"

export interface ICaptchaBusiness {
    verifyCaptcha(captchaResponse: string): Promise<any>
    verifyCaptchaLogin(captchaResponse: string, orderSource: OrderSource): Promise<any>

}

@injectable()
export class CaptchaBusiness implements ICaptchaBusiness {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(BASE_TYPES.FetchUtil) private fetchHelper: FetchUtil
    ) {
    }

    public async verifyCaptcha(response: string): Promise<any> {
        const secret = this.getCaptchaSecret()
        return fetch(captchaVerificationUrl, this.fetchHelper.postForm(`secret=${secret}&response=${response}`)).then((response: Response) => {
            return this.fetchHelper.parseResponse<any>(response)
        })
    }

    public async verifyCaptchaLogin(response: string, orderSource: OrderSource): Promise<any> {
        const secret = this.getOrderSourceBasedCaptchaSecret(orderSource)
        return fetch(captchaVerificationUrl, this.fetchHelper.postForm(`secret=${secret}&response=${response}`)).then((response: Response) => {
            return this.fetchHelper.parseResponse<any>(response)
        })
    }

    private getCaptchaSecret() {
        return ((process.env.ENVIRONMENT === "PRODUCTION") ? PROD_CAPTCHA_SECRET : STAGE_CAPTCHA_SECRET)
    }

    private getOrderSourceBasedCaptchaSecret(orderSource: OrderSource): string {
        switch (orderSource) {
            case "CULTSPORT_WEBSITE":
            case "CULTSPORT_APP":
            case "CULTSPORT_EMBED_APP": // this used for CS Embed Webview Order source
            case "TATA_NEU_CULTSPORT_WEBSITE": return CULTSPORT_CAPTCHA_v3_SECRET_LOGIN
            case "CULTSTORE_WEBSITE": return CULTSTORE_CAPTCHA_v3_SECRET_LOGIN
            case "SUGARFIT_WEBSITE": return SUGARFIT_CAPTCHA_SECRET_LOGIN
            default: return CAPTCHA_SECRET_LOGIN
        }

    }


}