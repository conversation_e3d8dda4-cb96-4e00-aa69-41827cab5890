import { inject, injectable } from "inversify"
import { InMemoryCacheService } from "@curefit/memory-cache"
import { IPageConfigReadOnlyDao, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import * as _ from "lodash"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { Discovery } from "./ReferralUtil"
import { UnavailableCopy } from "./GiftCardUtil"

interface ReferralBannerConfig {
    imageUrl: string,
    images?: string,
    bannerRatio: string,
    layoutProps: {
        bannerHeight: string,
        bannerWidth: string,
        noVerticalPadding: boolean
        edgeToEdge: boolean,
        spacing: {
            top: number,
            bottom: number
        }
    }
    action: {
        actionType: string,
        url: string,
    }
    referralInvitePage: {
        bannerImage: {
            desktop: string,
            mweb: string
        },
        title: string,
        subTitle: string
    },
    referralConfirmation: {
        title: string,
        subTitle: string,
        desktopSubTitle: string
    }
}

export interface ReferralPageData {
    howItWorksItemList: { icon: string, subTitle: string, iconSize?: number, segments?: string[] }[]
    banner?: ReferralBannerConfig,
    bannerV2?: ReferralBannerConfig,
    benefitBanner?: ReferralBannerConfig,
    challengeBanner?: ReferralBannerConfig,
    miniChallengeBanner?: ReferralBannerConfig,
    header: { title: string },
    referralConfirmation: {
        title: string,
        subTitle: string,
        desktopSubTitle: string
    },
    referralInvitePage: {
        bannerImage: {
            desktop: string,
            mweb: string
        },
        title: string,
        subTitle: string
    },
    whatsAppBannerUrl: string
    whatsAppLinkText: string
    referralLink?: string
    referralSteps: { icon: string, subTitle: string }[]
    processSteps: ItemList[]
    rewardSteps: ItemList[]
    referralTermsForEnabled: ReferralTermsWithStatus
    discovery?: Discovery
    unavailableCopy?: UnavailableCopy
}

export interface ItemList {
    icon: string,
    iconSize: number,
    padding: number,
    number: string,
    subTitle: string,
    subTitleFont: string,
    subTitleColor: string,
}

export interface ReferralTermsWithStatus {
    termsAndConditionsTitle: string
    termsAndConditionsItemList: { number: string, subTitle: string }[]
    termsAndConditionsItemListV2: ItemList[]
    termsAndConditionsProperties: { backgroundColor: string, titleColor: string }
    tnc: {
        title: string
        actionLink: string
    }
}
export interface ReferralPage {
    pageId: string
    data: ReferralPageData
}

@injectable()
class ReferPageConfig extends InMemoryCacheService<any> {

    constructor(
        @inject(PAGE_CONFIG_TYPES.PageConfigReadOnlySecondaryDao) protected pageConfigDao: IPageConfigReadOnlyDao,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger, 21 * 60)
        this.load("ReferPageConfig")
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    loadData(): Promise<any> {
        return this.pageConfigDao.findOne({ pageId: { $eq: "ReferPageConfig_V2" } }).then(pageConfig => {
            this.referralPages = pageConfig.data.pages
            return pageConfig.data
        })
    }
    public referralPages: ReferralPage[]
}

export default ReferPageConfig
