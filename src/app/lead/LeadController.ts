import * as express from "express"
import { controller, httpPost } from "inversify-express-utils"
import { Container, inject } from "inversify"
import { ILeadReadWriteDao, VM_MODELS_TYPES } from "@curefit/vm-models"

export function controllerFactory(kernel: Container) {
    @controller("/leads")
    class LeadController {

        constructor(
            @inject(VM_MODELS_TYPES.LeadReadwriteDao) private leadDao: ILeadReadWriteDao
        ) {}

        @httpPost("/")
        public createLead(req: express.Request): Promise<any> {

            const leadData = req.body
            return this.leadDao.findOne({ email: leadData.email }).then(existingLead => {
                if (!existingLead) {
                    return this.leadDao.create(leadData)
                } else {

                    if (existingLead.email === leadData.email) {
                        existingLead.apartmentName = leadData.apartmentName
                        existingLead.companyName = leadData.companyName
                    }

                    return this.leadDao.findOneAndUpdate({ email: leadData.email }, existingLead)
                }
            })
        }

    }

    return Lead<PERSON><PERSON>roller
}

export default controllerFactory
