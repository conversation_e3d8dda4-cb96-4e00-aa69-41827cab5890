import * as _ from "lodash"
import { CultBookingResponse , QueryClassResponse } from "./VoiceModel"
import { UserContext } from "@curefit/vm-models"

interface IVoiceBusiness {
    classBooking(date: string, time: string, workoutID: string, userId: string, cityId: string, userContext: UserContext): Promise<CultBookingResponse>
    queryClass(date: string , time: string, userId: string, cityId: string, userContext: UserContext): Promise<QueryClassResponse>
}
export default IVoiceBusiness