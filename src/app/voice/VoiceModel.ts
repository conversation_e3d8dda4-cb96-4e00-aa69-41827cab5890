export interface CultBookingResponse {
    isMember: boolean
    isCitySelected: boolean
    selectedCityName: string
    isDateValid: boolean
    isPreferenceSet: boolean
    availableClassID: number
    isWaitlistAvailable: boolean
    waitlistClassID: number
    productType: string
    centerName: string
}

export interface QueryClassResponse {
    isMember: boolean
    isCitySelected: boolean
    selectedCityName: string
    isDateValid: boolean
    isPreferenceSet: boolean
    classes: QueriedClass[]
}

export interface QueriedClass {
    workoutName: string
    workoutID: number
    classID: number
    cultAppAvailableSeats: number,
    waitlistedUserCount: number
    productType: string
    centerName: string
}

export interface EligibilityResponse {
    cultEligible: boolean
    mindEligible: boolean
}