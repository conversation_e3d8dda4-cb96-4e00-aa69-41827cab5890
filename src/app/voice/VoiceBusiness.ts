import { inject, injectable } from "inversify"
import { CultClass, CultClassesResponseV2 } from "@curefit/cult-common"
import { ICultServiceOld as ICultService, CULT_CLIENT_TYPES } from "@curefit/cult-client"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import AppUtil from "../util/AppUtil"
import * as _ from "lodash"
import { CultBookingResponse, QueryClassResponse, QueriedClass, EligibilityResponse } from "./VoiceModel"
import IVoiceBusiness from "./IVoiceBusiness"
import { CacheHelper } from "../util/CacheHelper"
import { UserContext } from "@curefit/vm-models"

@injectable()
class VoiceBusiness implements IVoiceBusiness {

    constructor(
        @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
        @inject(CULT_CLIENT_TYPES.MindFitService) private mindFitService: ICultService,
        @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper
    ) {
    }

    private checkIfPreferenceSet(userSettings: { key: string, value: any }[]) {
        if (_.isEmpty(userSettings)) {
            return false
        }
        else {
            const userSettingMap = _.keyBy(userSettings, userSetting => userSetting.key)
            if (!userSettingMap["USER_BOOKING_V2_FAVOURITE_CENTER"] && !userSettingMap["USER_BOOKING_V2_LAT_LONG"] && !userSettingMap["USER_BOOKING_V2_LOCALITY"]) {
                return false
            }
            return true
        }
    }
    private async checkActiveMember(userId: string): Promise<EligibilityResponse> {
        const cultSummary = await this.userCache.getCultSummary(userId)
        if ((cultSummary.membershipSummary.current.cult != null && cultSummary.membershipSummary.current.cult.state == "ACTIVE") || (cultSummary.trialEligibility.cult)) {
            return { cultEligible: true, mindEligible: true }
        }
        else if ((cultSummary.membershipSummary.current.mind != null && cultSummary.membershipSummary.current.mind.state == "ACTIVE") || (cultSummary.trialEligibility.mind)) {
            return { cultEligible: false, mindEligible: true }
        }
        else {
            return { cultEligible: false, mindEligible: false }
        }
    }

    async classBooking(date: string, time: string, workoutID: string, userId: string, cityId: string, userContext: UserContext): Promise<CultBookingResponse> {
        const classBookingResponse: CultBookingResponse = { isMember: true, selectedCityName: undefined, isCitySelected: true, isDateValid: true, isPreferenceSet: true, availableClassID: undefined, isWaitlistAvailable: false, waitlistClassID: undefined, productType: undefined, centerName: undefined }
        const checkActiveMemberResponse = await this.checkActiveMember(userId)
        if (!checkActiveMemberResponse.cultEligible && !checkActiveMemberResponse.mindEligible) {
            classBookingResponse.isMember = false
            return classBookingResponse
        }
        if (!cityId || cityId == "Other") {
            classBookingResponse.isCitySelected = false
            return classBookingResponse
        }
        else {
            const cultCityId = userContext.userProfile.city.cultCityId
            classBookingResponse.selectedCityName = userContext.userProfile.city.name
            const classSchedule = (checkActiveMemberResponse.cultEligible) ? await this.cultFitService.browseFitnessClassV2(cultCityId,
                userId, "CUREFIT_APP", null, null, userContext.sessionInfo.deviceId) : await this.mindFitService.browseFitnessClassV2(cultCityId, userId, "CUREFIT_APP", null, null, userContext.sessionInfo.deviceId)
            if (date > classSchedule.dates[classSchedule.dates.length - 1] || date < classSchedule.dates[0]) {
                classBookingResponse.isDateValid = false
                return classBookingResponse
            }
            else {
                const userSettings = classSchedule.userSettings
                if (!this.checkIfPreferenceSet(userSettings)) {
                    classBookingResponse.isPreferenceSet = false
                    return classBookingResponse
                }
                else {
                    const classes = classSchedule.classes
                    const workoutIdToWorkoutDetailMap = new Map()
                    const centerIdToNameMap = new Map()
                    for (let i = 0; i < classSchedule.workouts.length; i++) {
                        workoutIdToWorkoutDetailMap.set(classSchedule.workouts[i].id, classSchedule.workouts[i])
                    }
                    for (let i = 0; i < classSchedule.centers.length; i++) {
                        centerIdToNameMap.set(classSchedule.centers[i].id, classSchedule.centers[i].name)
                    }
                    let cultClass: CultClass
                    let minWaitlist = Number.MAX_VALUE
                    for (let i = 0; i < classes.length; i++) {
                        const element = classes[i]
                        if (element.date === date && element.startTime.slice(0, 5) === time && element.workoutID.toString() == workoutID) {
                            if (element.cultAppAvailableSeats > 0) { // available
                                cultClass = element
                                break
                            }
                            else { // waitlist
                                if (element.isWaitlistAvailable) {
                                    if (element.waitlistedUserCount < minWaitlist) {
                                        cultClass = element
                                        minWaitlist = element.waitlistedUserCount
                                    }
                                }
                            }
                        }
                    }
                    if (!cultClass) { // no class is available with this date,time and workout
                        return classBookingResponse
                    }
                    classBookingResponse.productType = (workoutIdToWorkoutDetailMap.get(cultClass.workoutID).tenantID == 1) ? "FITNESS" : "MIND"
                    classBookingResponse.centerName = centerIdToNameMap.get(cultClass.centerID)
                    if (cultClass.cultAppAvailableSeats > 0) {
                        classBookingResponse.availableClassID = cultClass.id
                        return classBookingResponse
                    }
                    else {
                        classBookingResponse.isWaitlistAvailable = cultClass.isWaitlistAvailable
                        classBookingResponse.waitlistClassID = cultClass.id
                        if (classBookingResponse.isWaitlistAvailable) {
                            return classBookingResponse
                        }
                    }
                }
            }
        }
    }
    private addAvailableClassToMap(workoutIdToClassDetailMap: Map<number, QueriedClass>, cultClass: CultClass, workoutName: string, tenantID: number, centerName: string) {
        const workout = workoutIdToClassDetailMap.get(cultClass.workoutID)
        if (workout) { // this class has been seen before
            if (workout.cultAppAvailableSeats > 0) { // it was available
                if (workout.cultAppAvailableSeats < cultClass.cultAppAvailableSeats) { // workout name and workoutID will remain same
                    workout.classID = cultClass.id
                    workout.cultAppAvailableSeats = cultClass.cultAppAvailableSeats
                    workout.centerName = centerName
                }
            }
            else { // the workout that was stored before was waitlisted, so replace that workout with this available class
                workout.classID = cultClass.id
                workout.cultAppAvailableSeats = cultClass.cultAppAvailableSeats
                workout.centerName = centerName
            }
        }
        else { // this workout has not been seen before
            workoutIdToClassDetailMap.set(cultClass.workoutID, this.buildCultClass(workoutName, tenantID, cultClass.workoutID, cultClass.id, cultClass.cultAppAvailableSeats, cultClass.waitlistedUserCount, centerName))
        }
    }

    private addWaitlistClassToMap(workoutIdToClassDetailMap: Map<number, QueriedClass>, cultClass: CultClass, workoutName: string, tenantID: number, centerName: string) {
        const workout = workoutIdToClassDetailMap.get(cultClass.workoutID)
        if (workout) {
            if (workout.cultAppAvailableSeats === 0) {
                if (workout.waitlistedUserCount > cultClass.waitlistedUserCount) { // in this case cultAppAvailableSeats will remanin same along with name and workoutid
                    workout.classID = cultClass.id
                    workout.waitlistedUserCount = cultClass.waitlistedUserCount
                    workout.centerName = centerName
                }
            }
            // else if cultAppAvailableSeats are greater than 0 it means we already have an available class for this workout
        }
        else { // this workout has not been seen before
            workoutIdToClassDetailMap.set(cultClass.workoutID, this.buildCultClass(workoutName, tenantID, cultClass.workoutID, cultClass.id, cultClass.cultAppAvailableSeats, cultClass.waitlistedUserCount, centerName))
        }
    }
    private buildCultClass(workoutName: string, tenantID: number, workoutID: number, classID: number, cultAppAvailableSeats: number, waitlistedUserCount: number, centerName: string): QueriedClass {
        const cultClass: QueriedClass = {
            workoutID: workoutID,
            classID: classID,
            cultAppAvailableSeats: cultAppAvailableSeats,
            waitlistedUserCount: waitlistedUserCount,
            workoutName: workoutName,
            productType: (tenantID === 1) ? "FITNESS" : "MIND",
            centerName: centerName
        }
        return cultClass
    }

    async queryClass(date: string, time: string, userId: string, cityId: string, userContext: UserContext): Promise<QueryClassResponse> {
        const classQueryResponse: QueryClassResponse = { isMember: true, selectedCityName: undefined, isCitySelected: true, isDateValid: false, isPreferenceSet: true, classes: [] }
        const checkActiveMemberResponse = await this.checkActiveMember(userId)
        if (!checkActiveMemberResponse.cultEligible && !checkActiveMemberResponse.mindEligible) {
            classQueryResponse.isMember = false
            return classQueryResponse
        }
        if (!cityId || cityId == "Other") {
            classQueryResponse.isCitySelected = false
            return classQueryResponse
        }
        else {
            const cultCityId = userContext.userProfile.city.cultCityId
            classQueryResponse.selectedCityName = userContext.userProfile.city.name
            const classSchedule = (checkActiveMemberResponse.cultEligible) ? await this.cultFitService.browseFitnessClassV2(cultCityId,
                userId, "CUREFIT_APP", null, null, userContext.sessionInfo.deviceId) : await this.mindFitService.browseFitnessClassV2(cultCityId, userId, "CUREFIT_APP", null, null, userContext.sessionInfo.deviceId)
            if (date > classSchedule.dates[classSchedule.dates.length - 1] || date < classSchedule.dates[0]) {
                return classQueryResponse
            }
            else {
                classQueryResponse.isDateValid = true
                const userSettings = classSchedule.userSettings
                if (!this.checkIfPreferenceSet(userSettings)) {
                    classQueryResponse.isPreferenceSet = false
                    return classQueryResponse
                }
                else {
                    const classes = classSchedule.classes
                    const workoutIdToClassDetailMap = new Map()
                    const workoutIdToWorkoutDetailMap = new Map()
                    const centerIdToNameMap = new Map()
                    for (let i = 0; i < classSchedule.workouts.length; i++) {
                        workoutIdToWorkoutDetailMap.set(classSchedule.workouts[i].id, classSchedule.workouts[i])
                    }
                    for (let i = 0; i < classSchedule.centers.length; i++) {
                        centerIdToNameMap.set(classSchedule.centers[i].id, classSchedule.centers[i].name)
                    }
                    for (let i = 0; i < classes.length; i++) {
                        const cultClass = classes[i]
                        if (cultClass.date === date && cultClass.startTime.slice(0, 5) === time) {
                            if (cultClass.cultAppAvailableSeats > 0) { // we have got an available class
                                this.addAvailableClassToMap(workoutIdToClassDetailMap, cultClass, workoutIdToWorkoutDetailMap.get(cultClass.workoutID).name, workoutIdToWorkoutDetailMap.get(cultClass.workoutID).tenantID, centerIdToNameMap.get(cultClass.centerID))
                            }
                            else if (cultClass.isWaitlistAvailable) {
                                this.addWaitlistClassToMap(workoutIdToClassDetailMap, cultClass, workoutIdToWorkoutDetailMap.get(cultClass.workoutID).name, workoutIdToWorkoutDetailMap.get(cultClass.workoutID).tenantID, centerIdToNameMap.get(cultClass.centerID))
                            }
                        }
                    }
                    workoutIdToClassDetailMap.forEach((value) => {
                        classQueryResponse.classes.push(value)
                    })
                    return classQueryResponse
                }
            }
        }
    }
}
export default VoiceBusiness
