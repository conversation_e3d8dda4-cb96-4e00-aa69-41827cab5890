import * as express from "express"
import { controller, httpGet , httpPost } from "inversify-express-utils"
import { Container, inject } from "inversify"
import TYPES from "../../config/ioc/types"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import AuthMiddleware from "../auth/AuthMiddleware"
import { Session } from "@curefit/userinfo-common"
import { UserContext } from "@curefit/userinfo-common"
import * as _ from "lodash"
import { CultBookingResponse , QueryClassResponse } from "./VoiceModel"
import IVoiceBusiness from "./IVoiceBusiness"

export function controllerFactory(kernel: Container) {
    @controller("/voiceInterface",
        kernel.get<AuthMiddleware>(TYPES.AuthMiddleware).validateSession)
    class VoiceController {
        constructor(
            @inject(CUREFIT_API_TYPES.VoiceBusiness) private voiceBusiness: IVoiceBusiness,
        ) {
        }

        @httpGet("/cult/classBooking")
        public async cultClassBooking(req: express.Request): Promise<CultBookingResponse> {
            const session: Session = req.session
            const userId = session.userId
            const userContext: UserContext = req.userContext as UserContext
            const cityId = session.sessionData.cityId
            const date = req.query.date
            const time = req.query.time
            const workoutID = req.query.workoutID
            return await this.voiceBusiness.classBooking(date, time, workoutID, userId , cityId , userContext)
        }

        @httpGet("/cult/classQuery")
        public async cultClassQuery(req: express.Request): Promise<QueryClassResponse> {
            const session: Session = req.session
            const userId = session.userId
            const userContext: UserContext = req.userContext as UserContext
            const cityId = session.sessionData.cityId
            const date = req.query.date
            const time = req.query.time
            return await this.voiceBusiness.queryClass(date , time , userId, cityId, userContext)
        }
    }
    return VoiceController
}
export default controllerFactory



