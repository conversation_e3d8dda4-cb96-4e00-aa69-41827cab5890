import { UserContext } from "@curefit/userinfo-common"
import {
    WidgetView
} from "../common/views/WidgetView"
import { TimeUtil, Timezone } from "@curefit/util-common"
import * as _ from "lodash"
import { Membership } from "@curefit/membership-commons"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import CartViewBuilder from "../cart/CartViewBuilder"
import { Doctor } from "@curefit/care-common"
import { DoctorAssetsResponse } from "@curefit/ollivander-node-client/dist/src/OllivanderCityService"
import { CenterResponse } from "@curefit/center-service-common"
import GymfitUtil, { GymMembershipState } from "../util/GymfitUtil"
import AppUtil from "../util/AppUtil"


export default class GymPtMembershipViewBuilder {
    constructor(
    ) {}

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, membership: Membership, center: CenterResponse, trainerDetails: Doctor, trainerName: string, addNotes?: boolean): Promise<{ widgets: (WidgetView)[], header: any, actions: any }> {
        const membershipCard: WidgetView = await this.getMembershipWidget(interfaces, membership, userContext)
        const doctorAssets: DoctorAssetsResponse = await interfaces.ollivanderService.getDoctorAssets(trainerDetails.id)
        const ptServiceType = trainerDetails?.resourceServiceMapping.find(mapping => mapping.subServiceType?.groupType && mapping.subServiceType.groupType === "GYMFIT_PERSONAL_TRAINING")
        const trainerLevel: string = CartViewBuilder.getTrainersLevelText(ptServiceType?.subServiceTypeCode)
        const widgets = addNotes ?
            [membershipCard, this.getYourTrainerWidget(trainerName, trainerLevel, center?.name, doctorAssets?.mediaList?.[0]?.mediaUrl), this.addNoteWidget(), this.getQuickLinkWidget()] :
            [membershipCard, this.getYourTrainerWidget(trainerName, trainerLevel, center?.name, doctorAssets?.mediaList?.[0]?.mediaUrl), this.getQuickLinkWidget()]
        return {
            widgets: widgets,
            header: {
                image: "/image/gymfit/gympt_banner.png"
            },
            actions: widgets[0].actions
        }
    }

    private getYourTrainerWidget(trainerName: string, trainerLevel: string, centerName: string, image: string): WidgetView {
        return {
            widgetType: "YOUR_TRAINER_WIDGET",
            heading: trainerLevel,
            title: GymfitUtil.capitalizeFirstLetterOfAllWords(trainerName),
            description: centerName,
            image
        }
    }

    private addNoteWidget(): WidgetView {
        return {
            widgetType: "TOPIC_DETAILS_LIST_WIDGET",
            title: "Note",
            items: [
                {
                    "subTitle": "In order to use your personal training pack, you will need an active cultpass with unlimited access to your prefered gym.",
                    "icon": "image/icons/cult/time_up.png"
                }
            ],
        }
    }

    private getQuickLinkWidget(): WidgetView {
        return {
            widgetType: "FAQ_WIDGET_V2",
            title: "Help",
            layoutProps: {
                spacing: {
                    top: "0",
                    bottom: "0"
                }
            },
            links: [{
                question: "When does my PT pack expire?",
                answer: "Your PT pack will expire when you have either attended all your sessions, or your pack validity has expired."
                }, {
                question: "Can I change my personal trainer?",
                answer: "It is highly recommended that you take all your personal training sessions with the same trainer so that they can personalise the workouts for you. If you are unhappy with your current trainer and would like to change trainers, please reach out to <NAME_EMAIL>"
                }, {
                question: "Can I use my PT sessions while my cultpass is on pause?",
                answer: "No. In order to use your PT sessions you will have to have an active cultpass."
                }, {
                question: "What happens to my PT sessions after my cultpass expires?",
                answer: "PT sessions at the gym cannot be taken without an active cultpass, however you can complete these sessions once you renew your membership."
            }]
        }
    }

    async getMembershipWidget(interfaces: CFServiceInterfaces, membership: Membership, userContext: UserContext): Promise<WidgetView> {
        const tz = userContext.userProfile.timezone
        let tag, color
        const productPromise = interfaces.catalogueService.getProduct(membership.productId)
        const product = await productPromise
        const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDate(tz), tz)
        const endDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.end)), tz)
        const endDateFormatted = endDate.format("D MMM YYYY")
        const benefit = membership.benefits.find(benefit => benefit.name === "GYMFIT_PERSONAL_TRAINING")
        if (_.isEmpty(benefit)) {
            return undefined
        }
        const total = benefit?.maxTickets || 0
        const completed = benefit?.ticketsUsed || 0
        const sessionRemaining = total - completed
        const membershipState = this.getGymPtMembershipState(membership, tz, sessionRemaining)
        let daysDiff = endDate.diff(today, "day")
        if (membershipState === "PAUSED" && membership.activePause) {
            const pauseEnd = membership.activePause.actualEnd ? membership.activePause.actualEnd : membership.activePause.end
            const pauseEndDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(pauseEnd)), tz)
            const daysDiffTillPauseEnd = pauseEndDate.diff(today, "day")
            daysDiff = daysDiff - daysDiffTillPauseEnd
        }
        const leftTextSuffix = daysDiff >= 0 ? ` • ${daysDiff} days remaining` : ""
        const packTagAndColor = this.getGymPtPackTagAndColor(membershipState)
        const progressBar = {
            leftText: `${sessionRemaining}/${total} Session Remaining` + leftTextSuffix,
            total,
            completed,
            type: "FITNESS",
            noPadding: true,
            progressBarColor: "#008300",
            progressBarBackgroundColor: "#066543",
            progressBarTextStyle: {},
            isSplitView: true,
            leftTextStyle: { color: "#8d93a0" },
            rightTextStyle: { color: "#8d93a0" },
            rightText: ""
        }
        interfaces.logger.info(`membership progressbar ${JSON.stringify(progressBar)}`)
        color = packTagAndColor.color
        tag = membershipState
        if (membershipState === "EXPIRED") {
            progressBar.leftText = `${completed}/${total} Session Completed`
            progressBar.rightText = `Expired: ${endDateFormatted}`
            progressBar.progressBarColor = "#767676"
        } else if (membershipState === "PAUSED") {
            progressBar.progressBarColor = "#F7C744"
            progressBar.progressBarBackgroundColor = "#6B571F"
        } else {
            if (sessionRemaining === 0) {
                tag = "COMPLETED"
                color = "#55565b"
                progressBar.leftText = `All Sessions Completed`
                progressBar.rightText = undefined
            } else if (membershipState === "ACTIVE") {
                progressBar.leftText = (completed === 0 ? `Book your first session!` : `${sessionRemaining}/${total} Session Remaining`) + leftTextSuffix,
                progressBar.rightText = undefined
            } else if (membershipState === "UPCOMING") {
                progressBar.leftText = `${sessionRemaining}/${total} Session Remaining` + ` • Starts: ` + TimeUtil.formatEpochInTimeZone(tz, membership.start, "DD MMM YYYY"),
                progressBar.rightText = undefined
                progressBar.progressBarColor = "transparent"
                progressBar.progressBarBackgroundColor = "transparent"
            }
        }
        const docterId: string = membership.metadata.preferredTrainerId
        const centerId: string = membership.metadata.preferredCenterId
        const userId: string = userContext.userProfile.userId
        const membershipId: string = membership.id.toString()
        const membershipData =  {
            membershipState: tag,
            tag: {
                title: tag,
                color
            },
            progressBar,
            title: product.subTitle,
            imageUrl: "image/vm/e839ce61-54d3-44e7-ab6b-8dd668b30045.png",
            cardBackgroundStyle: {},
            cardTextStyle: {}
        }
        interfaces.logger.info(`membership data ${JSON.stringify(membershipData)}`)
        const membershipCard: WidgetView = {
            data: [membershipData],
            widgetType: "MEMBERSHIP_WIDGET_V2",
            layoutProps: {
                spacing: {
                    top: "0",
                    bottom: "0"
                }
            },
            productType: "GYM_PT_PRODUCT",
            showDivider: true,
            widgetMetric: {
                isWidgetImpressionRequired: false,
                widgetType: "MEMBERSHIP_WIDGET_V2",
                widgetId: "c7541550-deac-4e6e-bd21-87c5ccbb47cc",
                widgetName: "Therapy Aurora - Membership Widget"
            },
            actions: tag === "ACTIVE" && await AppUtil.isGymPTSessionBookingSupported(interfaces.segmentService, userContext) ? [
                {
                    actionType: "NAVIGATION",
                    actionTitle: "BOOK",
                    url: "curefit://pt_slot_selection?productId=" + product.productId + "&doctorId=" + docterId + "&centerId=" + centerId + "&patientId=" + userId + "&parentBookingId=" + membershipId + "&isRescheduled=false"
                }
            ] : undefined
        }
        interfaces.logger.info(`membership card ${JSON.stringify(membershipCard)}`)
        return membershipCard
    }

    private getGymPtMembershipState(membershipDetails: Membership, tz: Timezone, sessionRemaining: number): GymMembershipState {
        const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDateWithTimezone(tz), tz)
        const endDate = TimeUtil.getMomentForDate(new Date(membershipDetails.end), tz)
        const startDate = TimeUtil.getMomentForDate(new Date(membershipDetails.start), tz)

        switch (membershipDetails.status) {
            case "PURCHASED": {
                if (startDate > today) {
                    return "UPCOMING"
                }
                if (endDate < today) {
                    return "EXPIRED"
                }
                return "ACTIVE"
            }
            case "SUSPENDED":
            case "CANCELLED": {
                return "CANCELLED"
            }
            case "PAUSED": {
                return "PAUSED"
            }
            default:
                return "CANCELLED"
        }
    }

    private getGymPtPackTagAndColor(membershipState: GymMembershipState) {
        switch (membershipState) {
            case "ACTIVE":
                return {
                    tag: "ACTIVE",
                    color: "#5bdbb6",
                }

            case "UPCOMING":
                return {
                    tag: "UPCOMING",
                    color: "#6236ff",
                }
            case "EXPIRING":
            case "EXPIRED":
            case "CANCELLED":
                return {
                    tag: membershipState === "CANCELLED" ? "CANCELLED" : "EXPIRED",
                    color: "#b00020",
                }
            case "PAUSED":
                return {
                    tag: "PAUSED",
                    color: "#f5a623"
                }
            default:
                return {
                    tag: "EXPIRED",
                    color: "#b00020",
                }
        }
    }
}
