import { Action } from "@curefit/apps-common"
import { TransientQRCodeResponse } from "@curefit/gymfit-common"
import { injectable } from "inversify"
import { Header, QRWidget, WidgetView } from "../common/views/WidgetView"
import * as _ from "lodash"
import { UserContext } from "@curefit/userinfo-common"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"

@injectable()
export default class GymUniversalCheckinViewBuilder {

    async buildView(userContext: UserContext, serviceInterfaces: CFServiceInterfaces, transientQRCode: TransientQRCodeResponse, sessionsLeftText: string): Promise<{ header: Header, widgets: WidgetView[], onDismissAction: Action }> {
        const header: Header = {
            title: undefined,
            icon: "/image/icons/gymfit/cross_new.png"
        }
        const widgets: WidgetView[] = []
        const qrCodeWidget: QRWidget = {
            widgetType: "QR_WIDGET",
            data: {
                header: {
                    "title": "Check-in"
                },
                qrCodeInfo: {
                    type: "DYNAMIC",
                    qrCodeString: transientQRCode.qrString,
                    refreshUrl: "/gymfit/checkinQr",
                    refreshInterval: transientQRCode.timeOfExpiration / 1000,
                    refreshIcon: "/image/icons/gymfit/refresh.png",
                    refreshText: "Click to reload QR code"
                },
                footer: {
                    title: "",
                    description: "To check-in to a gym, scan this QR code"
                },
                sessionsLeftText,
                eventData: {
                    extraParams: {
                        source: "universal QR"
                    }
                }
            },
            containerStyle: {
                paddingTop: 0,
                paddingBottom: 20,
            }
        }
        const onDismissAction: Action = {
            actionType: "REFRESH_PAGE"
        }
        widgets.push(qrCodeWidget)

        const bannerWidget = await this.getBannerWidget(userContext, serviceInterfaces)
        if (bannerWidget != undefined)
            widgets.push(bannerWidget)

        return {
            header,
            widgets,
            onDismissAction
        }
    }

    private async getBannerWidget(userContext: UserContext, serviceInterfaces: CFServiceInterfaces) {
        const widgetId = process.env.APP_ENV === "STAGE" ? "f12efeab-13ff-43fd-b159-f45f3300a072" : "ff8bd91c-9c22-45bd-ada4-1f2b7af1713e"
        const widgetsResponse = await serviceInterfaces.widgetBuilder.buildWidgets([widgetId], serviceInterfaces, userContext, undefined, null)
        if (!_.isEmpty(widgetsResponse) && !_.isEmpty(widgetsResponse.widgets)) {
            return widgetsResponse.widgets[0]
        } else
            return undefined
    }
}
