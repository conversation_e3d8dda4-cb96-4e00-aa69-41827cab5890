import { ActionCard, InfoCard, ProductListWidget, SinglesOrderConfirmationWidget, WidgetView } from "../common/views/WidgetView"
import { PageWidget } from "../page/Page"
import { UserContext } from "@curefit/userinfo-common"
import {
    GymfitCenter,
    GymfitCheckIn,
    GymfitCheckInClassification,
    GymfitEvent,
    GymfitStatus,
    UserMembershipsAndTrialUsages
} from "@curefit/gymfit-common"
import { GymfitCodeWidget, BannerCarouselWidget } from "../page/PageWidgets"
import { TimeUtil } from "@curefit/util-common"
import { InfoBlockProps } from "@curefit/apps-common"
import GymfitUtil from "../util/GymfitUtil"
import AppUtil from "../util/AppUtil"
import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import SoftBookingBuilder from "./SoftBookingBuilder"

export interface WhatToDo {
    title: string,
    subtitle: string,
    icon: string
}

class GymfitCheckinConfirmationView {
    public widgets: (WidgetView | PageWidget)[] = []
    public body: {[key: string]: string} = { activityType: "GYM_FIT_CLASS" }
}

export default GymfitCheckinConfirmationView
