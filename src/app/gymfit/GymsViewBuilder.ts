import { inject, injectable } from "inversify"
import { CenterCategory, GymfitAmenity, GymfitCenterCategory, GymfitLocality, TrialUsage } from "@curefit/gymfit-common"
import { GymCard, GymSearchAndFilter, SearchGymsResponse } from "../common/views/WidgetView"
import { UserContext } from "@curefit/userinfo-common"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { IGymfitBusiness } from "./GymfitBusiness"
import GymfitUtil from "../util/GymfitUtil"
import { CultSummary } from "@curefit/cult-common"
import { CULT_CLIENT_TYPES, ICultService, ICultServiceOld } from "@curefit/cult-client"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import AppUtil from "../util/AppUtil"
import { ActionUtil } from "@curefit/base-utils"
import { Membership } from "@curefit/membership-commons"
import { CardDescription, ISegmentService } from "@curefit/vm-models"
import { CenterMediaTag, CenterResponse, CenterVertical, MediaType, SkuName } from "@curefit/center-service-common"
import { Action } from "@curefit/apps-common"
import { GYMFIT_CLIENT_TYPES, IGymfitService } from "@curefit/gymfit-client"
import { CENTER_SERVICE_CLIENT_TYPES, ICenterService } from "@curefit/center-service-client"
import CultUtil from "../util/CultUtil"
import _ = require("lodash")
import CreditsView from "../common/views/CreditsViewWidget"

@injectable()
export default class GymsViewBuilder {
    constructor(
        @inject(CUREFIT_API_TYPES.GymfitBusiness) private gymfitBusiness: IGymfitBusiness,
        @inject(CULT_CLIENT_TYPES.ICultService) private cultService: ICultService,
        @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness,
        @inject(GYMFIT_CLIENT_TYPES.GymfitService) private gymfitService: IGymfitService,
        @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultServiceOld,
        @inject(CENTER_SERVICE_CLIENT_TYPES.CenterService) private centerService: ICenterService,
        @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService
    ) {
    }

    async buildGymFitCard(userContext: UserContext, centers: CenterResponse[], gymFitAmenities: GymfitAmenity[], gymFitLocalities: GymfitLocality[], centerCategories: CenterCategory[], appliedAmenityFilters: string[], appliedLocalityFilters: string[], categories: GymfitCenterCategory[], onlyOpen: boolean, membershipDetails: Membership[], goldTrialDetails: TrialUsage, cultSummary: CultSummary, meta: any): Promise<SearchGymsResponse> {
        // const gymFilters: GymFilter[] = []
        // const amenityFilters = gymFitAmenities.map((gymFitAmenity) => {
        //     return {id: gymFitAmenity.id, name: gymFitAmenity.name, selected: appliedAmenityFilters.includes(gymFitAmenity.name)}
        // })
        // const localityFilters = gymFitLocalities
        //     .filter((locality) => locality.status)
        //     .map((locality) => {
        //         return {id: locality.id, name: locality.name, selected: appliedLocalityFilters.includes(locality.name)}
        //     })
        // gymFilters.push({
        //     displayType: "TOGGLE_FILTER",
        //     title: "OPEN NOW",
        //     dataType: "OPEN_ONLY",
        //     toggleSelected: onlyOpen
        // })
        // const filters = centerCategories.map((centerCategory) => {
        //     return { id: centerCategory.code, name: centerCategory.name, selected: categories.includes(centerCategory.code as GymfitCenterCategory) }
        // })
        // gymFilters.push({
        //     displayType: "HORIZONTAL_LIST_FILTER",
        //     title: "TYPE",
        //     dataType: "TYPE",
        //     filters
        // })
        // gymFilters.push({
        //     displayType: "HORIZONTAL_LIST_FILTER",
        //     title: "AMENITIES",
        //     dataType: "AMENITIES",
        //     filters: amenityFilters
        // })
        // gymFilters.push({
        //     displayType: "HORIZONTAL_LIST_FILTER",
        //     title: "LOCALITY",
        //     dataType: "LOCALITY",
        //     filters: localityFilters
        // })
        const userId = userContext.userProfile.userId
        const cityId = userContext.userProfile.cityId
        let centerSkuMappingPromise
        if (!_.isEmpty(centers)) {
            const centerIds = centers.map(center => center.id)
            centerSkuMappingPromise = this.centerService.getCenterSkusByCenterIds(centerIds)
        }
        const complimentaryMemberships = await this.cultService.getComplimentaryMemberships({userId: userContext.userProfile.userId, appName: "CUREFIT_APP"})
        const hasComplimentaryAccess = GymfitUtil.hasComplimentaryAccess(userContext, complimentaryMemberships)
        const searchAndFilter: GymSearchAndFilter = {
            searchPlaceholder: "Search by center name or locality",
            isSearchEnabled: true,
            isFilterEnabled: false,
            gymFilters: []
        }
        const params = { isSoftbookingEnabled: await AppUtil.isGymSofbookingEnabled(userContext, this.hamletBusiness)}
        const gymCards: GymCard[] = []
        const centerSkuMapping = centerSkuMappingPromise ? await centerSkuMappingPromise : null
        const creditCenterDetails = centers.map(ele => ({
            accessCenterId: ele.id,
            centerSku: centerSkuMapping ?  centerSkuMapping.find(centerSkuMappingItem => centerSkuMappingItem.centerId === ele.id)?.skuId : 0
        }))
        const creditsForCenters = await this.cultFitService.getCenterCredits({userId, appName: "CUREFIT_APP", cityId, centerDetails: creditCenterDetails})
        const centerIds = centers.filter(center => center.vertical === CenterVertical.CULT).map((center) => {
            return center.meta.cultCenterId.toString()
        })
        const sportsCategoryDetails = centerIds.length > 0 ?  await this.cultFitService.getCenterSportCategDetails(centerIds) : []
        for (const center of centers) {
            const centerSkuMappingForCenter = centerSkuMapping ? centerSkuMapping.find(centerSkuMappingItem => centerSkuMappingItem.centerId === center.id) : null
            const creditsResponse = creditsForCenters.response
            const centerCreditInfo = creditsResponse[Object.keys(creditsResponse).find(ele => Number(ele) === center.id)]
            const imageUrls: string[] = []
            if (center.centerMedias != undefined) {
                if (center.vertical === CenterVertical.GYMFIT) {
                    center.centerMedias.filter(item => item.tag === CenterMediaTag.HERO).forEach(
                        item => {
                            item.mediaDetails.forEach(mediaItem => {
                                if (mediaItem.type === MediaType.IMAGE) {
                                    imageUrls.push(mediaItem.cdnUrl)
                                }
                            })
                        }
                    )
                } else {
                    center.centerMedias.filter(item => item.tag === CenterMediaTag.PRODUCT_BANNER).forEach(
                        item => {
                            item.mediaDetails.forEach(mediaItem => {
                                if (mediaItem.type === MediaType.IMAGE) {
                                    imageUrls.push(mediaItem.cdnUrl)
                                }
                            })
                        }
                    )
                }
            }
            let extraAction: Action = undefined
            const skuNameForCenter = CultUtil.getSkuNameForSkuId(centerSkuMappingForCenter?.skuId || 1)
            if (center.vertical === CenterVertical.GYMFIT) {
                const centerId = center.meta?.gymfitCenterId ? center.meta.gymfitCenterId : ""
                const gymfitCenter = await this.gymfitService.getGymfitCenterById(centerId.toString(), userContext.userProfile.userId)
                extraAction = GymfitUtil.addTitleInActionQuery(await this.gymfitBusiness.getCheckinCTAForUser(userContext, gymfitCenter.center, membershipDetails, goldTrialDetails, hasComplimentaryAccess, params))
                if (!_.isEmpty(center.centerMedias)) {
                    center.centerMedias.filter(item => item.tag === CenterMediaTag.HERO).forEach(
                        item => {
                            item.mediaDetails.forEach(mediaItem => {
                                if (mediaItem.type === MediaType.IMAGE) {
                                    imageUrls.push(mediaItem.cdnUrl)
                                }
                            })
                        }
                    )
                }
            } else {
                const centerId = center.meta?.cultCenterId.toString() ? center.meta.cultCenterId.toString() : ""
                const isSportsCategory = sportsCategoryDetails.find(center => center.centerId.toString() === centerId)?.isSportCategory
                if (isSportsCategory) {
                    extraAction = {
                        actionType: "NAVIGATION",
                        title: "EXPLORE",
                        url: AppUtil.isWeb(userContext) ? `/cult/center/${center.locality?.toLowerCase()}/${center.name?.trim().replace(/\s/g, "-")}/${center.meta?.cultCenterId}` : `curefit://cultcenter?centerId=${centerId}`
                    }
                    if (!_.isEmpty(center.centerMedias)) {
                        center.centerMedias.filter(item => item.tag === CenterMediaTag.PRODUCT_BANNER).forEach(
                            item => {
                                item.mediaDetails.forEach(mediaItem => {
                                    if (mediaItem.type === MediaType.IMAGE) {
                                        imageUrls.push(mediaItem.cdnUrl)
                                    }
                                })
                            }
                        )
                    }
                } else {
                    const isNewClassBookingSupported = AppUtil.isNewClassBookingSuppoted(userContext, undefined)
                    extraAction = {
                        actionType: "NAVIGATION",
                        title: (cultSummary.trialEligibility && cultSummary.trialEligibility.cult) ? "TRY FOR FREE" : "BOOK NOW",
                        url: ActionUtil.getBookCultClassUrl("FITNESS", isNewClassBookingSupported, "allgyms", undefined, centerId)
                    }
                }
            }
            const description: CardDescription[] = this.getCenterDescription(skuNameForCenter)

            const LUX_GYMS_SEGMENT_ID = "8a500172-fc50-4ad6-9e2c-d40af20db3f2"
            let isPartOfLuxSegment  = false
            try {
                isPartOfLuxSegment  = !! await this.segmentService.doesUserBelongToSegment(LUX_GYMS_SEGMENT_ID, userContext)
            } catch (e) {
                console.log("Segment call failed" + e)
            }
            if (skuNameForCenter === SkuName.LUX && isPartOfLuxSegment && AppUtil.isWeb(userContext)) {
                gymCards.push({
                    gymName: center.name,
                    id: center.id.toString(),
                    location: this.getCenterLocation(userContext, center),
                    images: imageUrls.length > 0 ? imageUrls : ["/image/icons/cult/gyms_default.jpg"],
                    cardAction: {
                        actionType: "NAVIGATION",
                        url:  `/luxury/gyms/${center.city?.toLowerCase()}/${center.name?.trim().replace(/\s/g, "-")}/${center.meta?.gymfitCenterId}`
                    },
                    address: {
                        latLong: {
                            lat: center.latitude,
                            long: center.longitude
                        }
                    },
                    extraAction: extraAction,
                    icon: this.getIconBasisCenterType(skuNameForCenter),
                    creditsData: centerCreditInfo ? new CreditsView(centerCreditInfo.centerCreditCost, `${centerCreditInfo.centerCreditCost > 1 ? "Credits" : "Credit"} per session`) : null,
                    description
                })
            } else if (!AppUtil.isWeb(userContext) || skuNameForCenter !== SkuName.LUX) {
                gymCards.push({
                    gymName: center.name,
                    id: center.id.toString(),
                    location: this.getCenterLocation(userContext, center),
                    images: imageUrls.length > 0 ? imageUrls : ["/image/icons/cult/gyms_default.jpg"],
                    cardAction: {
                        actionType: "NAVIGATION",
                        url: AppUtil.isWeb(userContext) ? (center.vertical === CenterVertical.GYMFIT
                                ? `/cult/cult-pass/${center.city?.toLowerCase()}/${center.name?.trim().replace(/\s/g, "-")}/${center.meta?.gymfitCenterId}`
                                : `/cult/center/${center.locality?.toLowerCase()}/${center.name?.trim().replace(/\s/g, "-")}/${center.meta?.cultCenterId}`
                        ) : (
                            center.vertical === CenterVertical.GYMFIT ? `curefit://gymfitcenter?centerId=${center.meta.gymfitCenterId}` : `curefit://cultcenter?centerId=${center.meta.cultCenterId}`
                        )
                    },
                    address: {
                        latLong: {
                            lat: center.latitude,
                            long: center.longitude
                        }
                    },
                    extraAction: extraAction,
                    icon: this.getIconBasisCenterType(skuNameForCenter),
                    creditsData: centerCreditInfo ? new CreditsView(centerCreditInfo.centerCreditCost, `${centerCreditInfo.centerCreditCost > 1 ? "Credits" : "Credit"} per session`) : null,
                    description
                })
            }

        }
        return new SearchGymsResponse(searchAndFilter, gymCards, {text: "No centers found", icon: "/image/gymfit/no_gym.jpg"}, meta)
    }

    private getCenterLocation(userContext: UserContext, gymCenter: CenterResponse): string {
        let gymDistance = ""
        if (GymsViewBuilder.shouldShowMapDistance(userContext)) {
            gymDistance = GymfitUtil.getFormattedGymDistance(gymCenter.distanceFromSearchOrigin)
        }
        if (gymCenter.locality) {
            return `${gymCenter.locality} ${gymDistance ? `• ${gymDistance}` : ""}`
        }
        return gymDistance ? gymDistance : "N/A"
    }

    private getCenterDescription(skuName: SkuName): CardDescription[] {
        const description: CardDescription[] = []

        if (skuName === SkuName.LUX) {
            const cardDescription: CardDescription = {
                text: "Download app for gym access ",
                color: "#55565B",
                bold: false
            }
            description.push(cardDescription)
            return description
        }
        const cardDescription: CardDescription = {
            text: "Unlimited access with cultpass ",
            color: "#55565B",
            bold: false
        }
        description.push(cardDescription)
        const cardDescriptionForBlack: CardDescription = {
            text: "ELITE",
            color: "#D4AF37",
            bold: true
        }
        description.push(cardDescriptionForBlack)
        if (skuName === SkuName.GOLD || skuName === SkuName.THIRD_PARTY_GYM) {
            const cardDescriptionForAnd: CardDescription = {
                text: " & ",
                color: "#55565B",
                bold: false
            }
            description.push(cardDescriptionForAnd)
            const cardDescriptionForGold: CardDescription = {
                text: "PRO",
                color: "#838383",
                bold: true
            }
            description.push(cardDescriptionForGold)
        }
        return description
    }

    private getIconBasisCenterType(skuName: SkuName): string {
        if (skuName === SkuName.LUX) {
            return "/image/icons/cult/lux_clp_tag.svg"
        }
        return skuName === SkuName.BLACK ? "/image/icons/cult/elite_tag_clp.png" : "/image/icons/cult/pro_tag_clp.png"
    }

    static shouldShowMapDistance(userContext: UserContext) {
        return userContext.sessionInfo.appVersion > 8.69
    }
}
