import { inject, injectable } from "inversify"
import { DEFAULT_CACHE_REFRESH_TIME_IN_SECONDS, InMemoryCacheService } from "@curefit/memory-cache"
import { IPageConfigReadOnlyDao, IPageConfigReadWriteDao, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import { BASE_TYPES, ILogger } from "@curefit/base"
import * as _ from "lodash"
import { HowItWorksItem, UrlPathBuilder } from "@curefit/product-common"
import { GuranteeInfo } from "@curefit/vm-models"
import { MoneyBackOfferInfo } from "../pack/CultPackPageConfig"
import { InfoCard } from "../common/views/WidgetView"

@injectable()
class GymfitPackPageConfig extends InMemoryCacheService<any> {

    constructor(
        @inject(PAGE_CONFIG_TYPES.PageConfigReadOnlySecondaryDao) protected pageConfigDao: IPageConfigReadOnlyDao,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger, 39 * 60)
        this.load()
    }

    loadData(): Promise<any> {
        return this.pageConfigDao.findOne({ pageId: { $eq: "GymfitPackPageConfig" } }).then(pageConfig => {
            const data = pageConfig.data
            this.howItWorksTitle = data.howItWorksTitle
            this.guranteeInfo = data.guranteeInfo
            this.howItWorksItemList = _.map(<HowItWorksItem[]>data.howItWorksItemList, item => {
                return {
                    text: item.text,
                    icon: item.icon
                }
            })
            this.howItWorksItemListIOS = _.map(<HowItWorksItem[]>data.howItWorksItemListIOS, item => {
                return {
                    text: item.text,
                    icon: item.icon
                }
            })
            this.moneybackOfferInfo = data.moneybackOfferInfo
            this.whyPackItems = data.whyPackItems
            return data
        })
    }

    public howItWorksTitle: string
    public howItWorksItemList: HowItWorksItem[]
    public howItWorksItemListIOS: HowItWorksItem[]
    public guranteeInfo: GuranteeInfo[]
    public moneybackOfferInfo: MoneyBackOfferInfo
    public whyPackItems: InfoCard[]

}
export default GymfitPackPageConfig
