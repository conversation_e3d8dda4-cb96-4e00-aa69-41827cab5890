import { inject, injectable } from "inversify"
import { TimeUtil } from "@curefit/util-common"
import { BASE_TYPES, Logger } from "@curefit/base"
import { GymfitCheckIn, GymfitCheckInState, GymfitCheckInType } from "@curefit/gymfit-common"
import { RASHI_CLIENT_TYPES, UserAttributeClient } from "@curefit/rashi-client"
import { BaseDelayedBatchedQueueHandler, IQueueService, Message, SQS_CLIENT_TYPES } from "@curefit/sqs-client"

import CUREFIT_API_TYPES from "../../config/ioc/types"

import IDeviceBusiness from "../device/IDeviceBusiness"
import IFirebaseService from "../common/firebase/IFirebaseService"
import GymfitUtil, {
    CHECKIN_ALLOWED_SEGMENTS,
    CHECKIN_SUPPORTED_VERSION,
    OPEN_PLAN_ACTION_SUPPORTED_VERSION
} from "../util/GymfitUtil"
import { IGymfitBusiness } from "./GymfitBusiness"
import { Tenant } from "@curefit/user-common"
import { ISegmentationCacheClient } from "@curefit/segmentation-service-client/dist"
import { SEGMENTATION_CLIENT_TYPES } from "@curefit/segmentation-service-client"
import { Device } from "@curefit/device-common"
import * as _ from "lodash"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import MetricsUtil from "../metrics/MetricsUtil"
import { CheckinNotificationState } from "./CheckinNotificationState"
import { ICampaignService, IRIS_CLIENT_TYPES, SendCampaignNotificationsRequest } from "@curefit/iris-client"
import { SendCampaignNotificationsResponse, UserTags } from "@curefit/iris-common/dist/src/campaign"
import { AppTenant } from "@curefit/base-common"
import { UserAttributesResponse } from "@curefit/rashi-client/src/rest/IUserAttributeClient"
import { IPersonalTrainingService, PERSONAL_TRAINING_CLIENT_TYPES } from "@curefit/personal-training-v2-client"
import {
    PtSession,
    PtSessionAccessType,
    PtSessionStatus,
    SessionSearchRequest,
    SortOrder
} from "@curefit/personal-training-v2-common"
import { UFS_CLIENT_TYPES, UserFitnessPlanControllerServiceInterface } from "@curefit/ufs-client"
import { HeadersUtil } from "../../util/HeadersUtil"
import { UserContext } from "@curefit/userinfo-common"
import { ILoggingService, LOGGING_CLIENT_TYPES } from "@curefit/logging-client"
import { ActivityDSSearchRequest, ActivityTypeDSEnum } from "@curefit/logging-common"
import { ActivityType } from "@curefit/gear-common"
import { ActivityTypeDS } from "@curefit/logging-common/dist/src/models/ActivityStore"
import AppUtil from "../util/AppUtil"

export interface GymfitCheckinEventPayload {
    eventTime: number
    eventType: GymfitCheckInState
    checkIn: GymfitCheckIn
}

const POST_CHECKIN_TIME_LIMIT_MINUTES = 5
const TRAINER_LED_ONBOARDING_PILOT_GYMS = [351, 397, 170, 70, 282, 134, 71, 386, 175, 356, 350, 121, 509]
const CULT_SMARTWORKOUT_PLAN_CHECKIN = "CULT_SMARTWORKOUT_PLAN_CHECKIN"
// const PUSH_NOTIFICATION_SMARTWORKOUT_PLAN_CHECKIN = "PUSH_NOTIFICATION_SMARTWORKOUT_PLAN_CHECKIN"
const PUSH_NOTIFICATION_CEREBRUM_SMARTWORKOUT_PLAN_CHECKIN_V2_505 = "PUSH_NOTIFICATION_CEREBRUM_SMARTWORKOUT_PLAN_CHECKIN_V2_505"
const PUSH_NOTIFICATION_AUTO_ASSESSMENT_TO_MEMBER_CHECKIN = "PUSH_NOTIFICATION_AUTO_ASSESSMENT_TO_MEMBER_CHECKIN"
const PUSH_NOTIFICATION_GYM_PROGRESSION_CHECKIN = "PUSH_NOTIFICATION_CEREBRUM_GYM_LOGGING_-_SESSION_START_110"
const WHATSAPP_GYM_PROGRESSION_CHECKIN = "WHATSAPP_CEREBRUM_GYM_PROGRESSION_NUDGE_2_149"
const NUX_USER_JOURNEY_PILOT_GYMS_PROD = [175]
const AUTO_ASSESSMENT_ON_CHECKIN_GYMS_PROD = [53]
const NUX_USER_JOURNEY_PILOT_GYMS_STAGE = [23]
const GYM_TENANT_ID = 1
const userAttributesMap = {
    cult_pack_start_date: "cultpackstartdate",
    last_gym_assessment_date: "last_gym_assessment_date",
    gym_session_attended_count_d180: "gym_session_attended_count_d180",
    gymfit_fitness_plan_status: "gymfit_fitness_plan_status"
}
const pilotStartDateMap = {
    175: 1677133800000,
    53: 1677133800000
}

@injectable()
class GymfitCheckinQueueListener extends BaseDelayedBatchedQueueHandler {
    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(SQS_CLIENT_TYPES.QueueService) queueService: IQueueService,
        @inject(CUREFIT_API_TYPES.FirebaseService) private firebaseService: IFirebaseService,
        @inject(RASHI_CLIENT_TYPES.UserAttributeClient) public userAttributeClient: UserAttributeClient,
        @inject(CUREFIT_API_TYPES.DeviceBusiness) private deviceBusiness: IDeviceBusiness,
        @inject(CUREFIT_API_TYPES.GymfitBusiness) private gymfitBusiness: IGymfitBusiness,
        @inject(SEGMENTATION_CLIENT_TYPES.SegmentationCacheClient) private segmentationCacheClient: ISegmentationCacheClient,
        @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness,
        @inject(CUREFIT_API_TYPES.MetricsUtil) private metricsUtil: MetricsUtil,
        @inject(IRIS_CLIENT_TYPES.IrisCampaignService) private campaignService: ICampaignService,
        @inject(PERSONAL_TRAINING_CLIENT_TYPES.PersonalTrainingService) private ptService: IPersonalTrainingService,
        @inject(UFS_CLIENT_TYPES.UserFitnessPlanControllerService) private userFitnessPlanControllerService: UserFitnessPlanControllerServiceInterface,
        @inject(LOGGING_CLIENT_TYPES.LoggingService) private loggingService: ILoggingService,
    ) {
        super(GymfitUtil.getQueueName(), 10, queueService, 60 * 1000)
    }

    async handle(body: Message[]): Promise<boolean[]> {
        return Promise.all(body.map(message => this.handleMessage(message.data, message.attributes)))
    }

    async handleMessage(message: string, attributes: { [key: string]: any }): Promise<boolean> {
        try {
            const notifMessage = JSON.parse(message)?.Message
            const payload = <GymfitCheckinEventPayload>(JSON.parse(notifMessage))
            const userId = payload?.checkIn?.userId
            let logs: string = "gymfcmcheckin uID - " + userId
            const userSegments = await this.segmentationCacheClient.getUserSegments(userId)

            this.metricsUtil.reportCheckinNotificationState(CheckinNotificationState.CHECKIN_EVENT_RECEIVED)

            // To handle notification for trial/walkin users adding check since trial/walkin users won't be part of any segments
            const centerId: number = Number(payload?.checkIn?.centerOffering?.centerId)
            let isUserPartOfAllowedSegments = process.env.ENVIRONMENT === "PRODUCTION" ? NUX_USER_JOURNEY_PILOT_GYMS_PROD.includes(centerId) : NUX_USER_JOURNEY_PILOT_GYMS_STAGE.includes(centerId)
            if (!isUserPartOfAllowedSegments) {
                for (let i = 0; i < CHECKIN_ALLOWED_SEGMENTS.length; i++) {
                    if (userSegments.includes(CHECKIN_ALLOWED_SEGMENTS[i])) {
                        isUserPartOfAllowedSegments = true
                        break
                    }
                }
            }

            if (!isUserPartOfAllowedSegments) {
                return true
            }

            this.metricsUtil.reportCheckinNotificationState(CheckinNotificationState.PART_OF_ALLOWED_SEGMENT)
            const device = await this.validateAndGetDeviceToken(payload, logs)

            if (device) {
                const appVersion = device.activeDevice.appVersion
                if (appVersion > CHECKIN_SUPPORTED_VERSION) {
                    const deviceToken = device?.activeDevice?.pushNotificationToken
                    if (deviceToken) {
                        // const hamletContext: HamletContext = {
                        //     userId: userId,
                        //     deviceId: device.deviceId,
                        //     osname: device?.activeDevice?.osName,
                        //     appVersion: appVersion,
                        //     experimentIds: [BETTER_ONBOARDING_EXPERIMENT_ID],
                        //     tenant: Tenant.CUREFIT_APP
                        // }
                        const result = await this.notifyDevice(deviceToken, payload, logs, appVersion, "")
                    } else {
                        this.metricsUtil.reportCheckinNotificationState(CheckinNotificationState.PUSH_NOTIFICATION_TOKEN_UNDEFINED)
                        logs = logs + " device token undefined "
                        this.logger.info(logs)
                    }
                } else {
                    logs = logs + " appversion not supported - " + appVersion
                    this.metricsUtil.reportCheckinNotificationState(CheckinNotificationState.APP_VERSION_NOT_SUPPORTED)
                    this.logger.info(logs)
                }
            } else {
                this.metricsUtil.reportCheckinNotificationState(CheckinNotificationState.DEVICE_UNDEFINED)
            }
        } catch (err) {
            this.logger.error("gymfcmcheckin Failed to process gymfit checkin event:" + message, err)
        }

        return true
    }

    private async validateAndGetDeviceToken(payload: GymfitCheckinEventPayload, logs: string): Promise<Device | undefined> {
        try {
            // const userId = payload?.checkIn?.userId
            // let logs: string = "validateAndGetDeviceToken uID - " + userId
            const eventType = payload?.eventType
            if (eventType !== GymfitCheckInState.VALIDATED) {
                this.metricsUtil.reportCheckinNotificationState(CheckinNotificationState.NOT_VALIDATED)
                logs = logs + " device_is_undefined eventType !== GymfitCheckInState.VALIDATED " + eventType
                this.logger.info(logs)
                return
            }

            const checkInTime = payload?.checkIn?.validatedAt
            this.logger.debug(`gymfcmcheckin 2 checkInTime: ${checkInTime}`)
            if (!checkInTime) {
                logs = logs + " device_is_undefined checkinTime is undefined "
                this.metricsUtil.reportCheckinNotificationState(CheckinNotificationState.CHECKIN_TIME_UNDEFINED)
                this.logger.info(logs)
                return
            }

            // Calculate minutes elapsed since check in by comparing epoch time (milliseconds)
            const minutesElapsedSinceCheckin = (TimeUtil.getCurrentEpoch() - checkInTime) / 1000 / 60
            if (minutesElapsedSinceCheckin > POST_CHECKIN_TIME_LIMIT_MINUTES) {
                logs = logs + " device_is_undefined checkin time limit not met "
                this.metricsUtil.reportCheckinNotificationState(CheckinNotificationState.CHECKIN_TIME_LIMIT_CROSSED)
                this.logger.info(logs)
                return
            }

            const deviceId = payload?.checkIn?.deviceId
            if (!deviceId) {
                logs = logs + " device_is_undefined deviceId is undefined  "
                this.metricsUtil.reportCheckinNotificationState(CheckinNotificationState.DEVICE_ID_UNDEFINED)
                this.logger.info(logs)
                return
            }

            const device = await this.deviceBusiness.getDeviceByDeviceId(deviceId, Tenant.CUREFIT_APP)

            return device
        } catch (err) {
            this.logger.error("gymfcmcheckin Failed to get device push token:", err, payload)
        }
    }

    public async notifyDevice(deviceToken: string, payload: GymfitCheckinEventPayload, logs: string, appVersion: number, userid: string): Promise<boolean> {
        try {
            const userId = payload?.checkIn?.userId || userid
            const checkInId = payload?.checkIn?.id

            let appLoggingUrl

            this.logger.info(logs + " checkInId " + checkInId)

            // Handle NUX user journey events
            const centerId: number = Number(payload?.checkIn?.centerOffering?.centerId)
            const trainerId = payload?.checkIn?.trainerId
            const isPtTrainer = payload?.checkIn?.isPtTrainerAssigned
            const isNuxJourneyPilotGym = (process.env.ENVIRONMENT === "PRODUCTION" ? NUX_USER_JOURNEY_PILOT_GYMS_PROD.includes(centerId) : NUX_USER_JOURNEY_PILOT_GYMS_STAGE.includes(centerId)) && appVersion >= 9.90
            let isNuxJourneyCheckin = false
            if (isNuxJourneyPilotGym) {
                if (payload?.checkIn?.checkInType === GymfitCheckInType.TRIAL) {
                    isNuxJourneyCheckin = true
                } else {
                    const attributes: string[] = [userAttributesMap.cult_pack_start_date, userAttributesMap.last_gym_assessment_date, userAttributesMap.gym_session_attended_count_d180, userAttributesMap.gymfit_fitness_plan_status]
                    const userAttributesResponse: UserAttributesResponse = await this.userAttributeClient.getBulkCachedUserAttributes(Number(userId), attributes, AppTenant.CUREFIT)
                    const startDateObject = (Object)(userAttributesResponse.attributes)[userAttributesMap.cult_pack_start_date]
                    const packStartDate: Date = new Date(Number(startDateObject))
                    // @ts-ignore
                    const pilotStartDate: Date = new Date(pilotStartDateMap[centerId])
                    let diffInTime: number = packStartDate.getTime() - pilotStartDate.getTime()
                    const daysSincePackPurchased = Math.ceil(diffInTime / (1000 * 60 * 60 * 24))
                    if (daysSincePackPurchased > 30 && daysSincePackPurchased < 60) {
                        const bcaDoneDateObject = (Object)(userAttributesResponse.attributes)[userAttributesMap.last_gym_assessment_date]
                        if (bcaDoneDateObject !== null && !_.isEmpty(bcaDoneDateObject)) {
                            const bcaDoneDate: Date = TimeUtil.parseDateFromEpoch(Number(startDateObject))
                            diffInTime = new Date().getTime() - bcaDoneDate.getTime()
                            const daysSinceBCa = Math.ceil(diffInTime / (1000 * 60 * 60 * 24))
                            if (daysSinceBCa > 30) {
                                const checkinCountObject = (Object)(userAttributesResponse.attributes)[userAttributesMap.gym_session_attended_count_d180]
                                if (!_.isEmpty(checkinCountObject)) {
                                    const checkInCount = Number(checkinCountObject)
                                    if (checkInCount > 5) {
                                        // Reassessment case
                                        isNuxJourneyCheckin = true
                                    }
                                }
                            }
                        }
                    } else if ((daysSincePackPurchased >= 0 && daysSincePackPurchased < 30) || (daysSincePackPurchased < 0 && (Object)(userAttributesResponse.attributes)[userAttributesMap.gym_session_attended_count_d180] == null)) {
                        // Generic Case
                        const fitnessPlanStatusObject = (Object)(userAttributesResponse.attributes)[userAttributesMap.gymfit_fitness_plan_status]
                        if (_.isEmpty(fitnessPlanStatusObject) || fitnessPlanStatusObject === "NO") {
                            isNuxJourneyCheckin = true
                        }
                    }
                }
            }
            const isAutoAssessmentPilotGym =  AUTO_ASSESSMENT_ON_CHECKIN_GYMS_PROD.includes(centerId) && trainerId && appVersion >= 10.30
            let isGymProgressionEnabled = false
            let action
            if (isAutoAssessmentPilotGym) {
                action = {
                    actionType: "OPEN_POST_CHECKIN_AUTO_ASSESSMENT_PAGE",
                    meta: {
                        postCheckinUrl: "curefit://auto_assessment",
                        trainerId: trainerId.toString(),
                        centerId: centerId.toString(),
                        isPtTrainer
                    },
                    analyticsData: this.getAnalyticsEventData(),
                }
                this.logger.info("My logg checkin event consumed auto assessment notification" + action)
            }
            else if (isNuxJourneyCheckin && appVersion >= 9.90) {
                action = {
                    actionType: "OPEN_POST_CHECKIN_NUX_JOURNEY_PAGE",
                    meta: {
                        postCheckinUrl: "curefit://gympostcheckinscreen",
                        trainerId,
                        centerId,
                        isPtTrainer
                    },
                    analyticsData: this.getAnalyticsEventData(),
                }
            } else {
                action = await this.generateWorkoutPlanActionIfEligible(userId, centerId, trainerId, isPtTrainer, payload, appVersion, isNuxJourneyCheckin, logs)

                if (_.isEmpty(action) || userId == "88706405" || userId == "94114972") {
                    isGymProgressionEnabled = await AppUtil.isGymProgressionPageSupported(userId, appVersion, this.segmentationCacheClient)
                    if (isGymProgressionEnabled) {
                        action = await this.generateGymProgressionAction(userId, checkInId, logs, payload?.checkIn?.deviceId)
                        if (!_.isEmpty(action.url)) {
                            appLoggingUrl = action.url
                        }
                    } else {
                        this.logger.info(logs + " user not eligible for generateGymProgressionActionIfEligible")
                    }
                }
            }

            if (_.isEmpty(action)) {
                logs = logs + "notify device action is null"
                this.logger.info(logs)
                return false
            }

            const actionVar = JSON.stringify(action)

            this.logger.info(logs + actionVar)
            this.logger.info(logs + " appLoggingUrl: " + appLoggingUrl)

            const smartWorkoutPlanContext: UserTags[] = [{
                userId: userId,
                tags: {
                    actionVar: actionVar,
                    appLoggingLink: appLoggingUrl
                }
            }]

            const creativeIds = isAutoAssessmentPilotGym ? [PUSH_NOTIFICATION_AUTO_ASSESSMENT_TO_MEMBER_CHECKIN] :
                isGymProgressionEnabled ? [PUSH_NOTIFICATION_GYM_PROGRESSION_CHECKIN] : [PUSH_NOTIFICATION_CEREBRUM_SMARTWORKOUT_PLAN_CHECKIN_V2_505]

            if ( userId == "88706405" ) {
                creativeIds.push(WHATSAPP_GYM_PROGRESSION_CHECKIN)
            }

            const request: SendCampaignNotificationsRequest = {
                campaignId: CULT_SMARTWORKOUT_PLAN_CHECKIN,
                creativeIds: creativeIds,
                userContexts: smartWorkoutPlanContext
            }
            const result: SendCampaignNotificationsResponse = await this.campaignService.sendCampaignMessages(request)
            const resultVal = result[userId][0].sent
            // const result = await this.firebaseService.sendFirebaseMessage(firebasePayload)
            // const resultVal = result?.success ?? false
            logs = logs + " device notified - " + resultVal
            if (resultVal) {
                this.metricsUtil.reportCheckinNotificationState(CheckinNotificationState.SUCCESSFULLY_SENT)
            } else {
                this.metricsUtil.reportCheckinNotificationState(CheckinNotificationState.ERROR_IN_SENDING_NOTIFICATION)
            }
            this.logger.info(logs)
        } catch (err) {
            logs = logs + " Failed to notify check-in event to device. "
            this.logger.error(logs, {deviceToken, err})
        }

        return false
    }

    private async generateWorkoutPlanActionIfEligible(userId: string, centerId: number, trainerId: number, isPtTrainer: boolean, payload: GymfitCheckinEventPayload, appVersion: number, isNuxJourneyCheckin: boolean, logs: string) {
        // const [hasWorkoutPlan, userAttributes] = await Promise.all([
        //     // Check if user has an existing workout plan
        //     this.gymfitBusiness.hasWorkoutPlan(userId),
        //
        //     // Check if user opt-ed out of the check-in notification
        //     this.userAttributeClient.getUserAttributes(
        //         Number(userId),
        //         optOutPreferenceKey,
        //         GymfitUtil.getRashiNamespace()
        //     )
        // ])

        const activeSWPUser = await AppUtil.hasUsedSWPInLast30Days(userId, this.segmentationCacheClient)
        if (!activeSWPUser) {
            this.logger.info(logs + " is not active SWP User")
            return null
        }

        const optOutPreferenceKey = GymfitUtil.getCheckinNotificationPreferenceKey()

        // Current time +-15 min
        const nowMinusFifteenMin = Date.now() - 15 * 60 * 1000
        const nowPlusFifteenMin = Date.now() + 15 * 60 * 1000

        const searchRequest: SessionSearchRequest = {
            tenantId: 1,
            userIds: [userId.toString()],
            startTimeFrom: nowMinusFifteenMin,
            startTimeTo: nowPlusFifteenMin,
            statuses: [PtSessionStatus.BOOKED],
            pageNo: 0,
            pageSize: 1
        }

        const promises = [
            this.gymfitBusiness.hasWorkoutPlan(userId),
            this.ptService.searchSessions(searchRequest, HeadersUtil.getCommonHeaders({userProfile: {userId: userId}} as UserContext) )
        ]

        const [hasWorkoutPlan, sessions] = await Promise.all<any>(promises)

        if (sessions.length > 0) return null

        const userAttributes: UserAttributesResponse = await this.userAttributeClient.getBulkCachedUserAttributes(Number(userId), [optOutPreferenceKey], AppTenant.CUREFIT)
        // const hasWorkoutPlanObj = (Object) (userAttributes.attributes)[userAttributesMap.gymfit_fitness_plan_status]
        // const hasWorkoutPlan = !_.isEmpty(hasWorkoutPlanObj) && hasWorkoutPlanObj !== "NO"
        // doesUserHasWorkoutPlan = hasWorkoutPlan

        const hasUserOptOutObj = (Object)(userAttributes.attributes)[optOutPreferenceKey]
        const hasUserOptOut = !_.isEmpty(hasUserOptOutObj) && hasUserOptOutObj === "true"

        if (!_.isEmpty(userAttributes)) {
            logs = logs + " userAtr - " + JSON.stringify(userAttributes)
            this.logger.info(logs)
        }
        // const hasUserOptOut = !_.isEmpty(userAttributes) ? userAttributes[0]?.attrValue ?? false : false

        if (hasUserOptOut === true) {
            logs = logs + " opted out"
            this.metricsUtil.reportCheckinNotificationState(CheckinNotificationState.OPTED_OUT)
            this.logger.info(logs)
            return null
        }

        if (!hasWorkoutPlan && TRAINER_LED_ONBOARDING_PILOT_GYMS.includes(centerId)) {
            logs = logs + " hasWorkoutPlan= false"
            this.logger.info(logs)
            this.metricsUtil.reportCheckinNotificationState(CheckinNotificationState.PILOT_GYM_DISABLE_CREATE_PLAN_NOTIFICATION)
            return null
        }
        // Push message to device
        // const firebasePayload: FirebasePayload = {
        //     time_to_live: 5 * 60, // FCM stores message and tries delivery for "5 minutes", after which the message is discarded if undelivered
        //     priority: "high", // (Android) High priority ensures message gets delivered ASAP
        //     "content-available": true, // (iOS) High priority
        //     notification: {
        //         tag: "cultpass_checkin_notification",
        //         title: "Click here to create a workout plan",
        //         body: "Faster, personalized workouts made for you"
        //     },
        //     data: {
        //         action: (
        //             hasUserOptOut ? {
        //                 actionType: "REFRESH_PAGE",
        //                 analyticsData: this.getAnalyticsEventData(),
        //             } : (
        //                 hasWorkoutPlan ?
        //                     this.getViewWorkoutPlanAction(payload, appVersion) :
        //                     await this.getCreateWorkoutPlanAction(appVersion)
        //             )
        //         ),
        //     },
        //     registration_ids: [deviceToken],
        //     collapse_key: "cultpass_checkin_notification" // ensure one message delivery
        // }

        logs = logs + " doesUserHasPlan - " + hasWorkoutPlan
        this.logger.info(logs)
        if (hasWorkoutPlan) {
            // swap the wods if today is rest day
            await this.userFitnessPlanControllerService.setTodayAsWorkout(userId, GYM_TENANT_ID)
        }

        const action = hasUserOptOut ? {
            actionType: "REFRESH_PAGE",
            analyticsData: this.getAnalyticsEventData(),
        } : (
            hasWorkoutPlan ?
                this.getViewWorkoutPlanAction(payload, appVersion, isNuxJourneyCheckin) :
                await this.getCreateWorkoutPlanAction(appVersion)
        )
        if (isNuxJourneyCheckin) {
            action.actionType = "OPEN_POST_CHECKIN_NUX_JOURNEY_PAGE"
                // @ts-ignore
                action.meta = {
                // @ts-ignore
                ...action.meta,
                postCheckinUrl: "curefit://gympostcheckinscreen",
                trainerId,
                centerId,
                isPtTrainer
            }
        }

        return action
    }


    private async generateGymProgressionAction(userId: string, sessionId: string, logs: string, deviceId?: string) {
        // Added cerebrum segment and app version check since user context is not available for cyclops segment check
        const searchRequest: any = {}
        searchRequest["userId"] = [userId]
        searchRequest["namespace"] = ["GYM"]
        searchRequest["activityType"] = "WEIGHT_LOGGING"

        const result = await this.loggingService.getActivityStoreAttribute(searchRequest)
        logs = logs + " number of gym activity logged: " + result?.length + ", for searchRequest: " + JSON.stringify(searchRequest)
        this.logger.info(logs)

        // Experiment Check if control
        const bucketIdGym: string = await Promise.resolve(AppUtil.getBucketIdForGymProgressionExp(userId, this.hamletBusiness, deviceId, Tenant.CUREFIT_APP))
        const bucketIdFeedback: string = await Promise.resolve(AppUtil.getBucketIdForFeedbackExp(userId, this.hamletBusiness, deviceId, Tenant.CUREFIT_APP))
        this.logger.info(`progression bucket :- ${bucketIdGym}, feedback bucket :- ${bucketIdFeedback}`)

        if (!_.isEmpty(result) && result.length > 0) {
            // existing user
            return {
                actionType: "NAVIGATION",
                url: this.getGymLoggingPageUrl(sessionId, bucketIdGym, bucketIdFeedback)
            }
        } else {
            // new user
            return {
                actionType: "NAVIGATION",
                url: "curefit://insight_tracker?completionActionUrl=" + encodeURIComponent(this.getGymLoggingPageUrl(sessionId, bucketIdGym, bucketIdFeedback))
            }
        }

        // else if treatment
    }


    private getAnalyticsEventData() {
        return {
            eventKey: "page_view",
            eventData: {
                pageType: "gymcheckinsuccess"
            }
        }
    }

    private async getCreateWorkoutPlanAction(appVersion: number) {
        return {
            actionType: "SHOW_CULTPASS_CHECKIN_SUCCESS_MODAL",
            analyticsData: this.getAnalyticsEventData(),
            meta: {
                title: "Create a Workout Plan",
                subTitle: "Personalized workouts • Faster results",
                dismissAction: {
                    text: "NOT NOW"
                },
                confirmAction: {
                    text: "GET PLAN",
                    action: {
                        actionType: "NAVIGATION",
                        url: await this.getCreatePlanUrl(appVersion),
                        analyticsData: {
                            eventKey: "button_click_event",
                            eventData: {
                                actionType: "cultpass_checkin_modal_create_plan_clicked",
                            }
                        }
                    }
                },
                checkin: true
            }
        }
    }

    private getOpenWorkoutPlanAction(appVersion: number, isNuxJourneyPilotGym: boolean) {
        return appVersion >= OPEN_PLAN_ACTION_SUPPORTED_VERSION ? {
                actionType: "OPEN_FP_PLAN_PAGE_AFTER_CHECKIN",
                meta: {
                    checkin: true,
                    actionType: "NAVIGATION",
                    url: "curefit://fitnessplanpage?productId=FITNESS_COACH&tenantId=1",
                    pageName: isNuxJourneyPilotGym ? "First Checkin Post plan creation" : null
                },
            } :
            {
                actionType: "NAVIGATION",
                url: "curefit://fitnessplanpage?productId=FITNESS_COACH&tenantId=1",
                meta: {
                    checkin: true
                },
            }
    }

    private getViewWorkoutPlanAction(payload: GymfitCheckinEventPayload, appVersion: number, isNuxJourneyPilotGym: boolean) {
        return {
            ...this.getOpenWorkoutPlanAction(appVersion, isNuxJourneyPilotGym),
            analyticsData: this.getAnalyticsEventData(),
        }
    }

    private async getCreatePlanUrl(appVersion: number) {
        if (appVersion < 9.37) {
            return "curefit://userform?formId=FITNESS_PLANNER_ONBOARDING_3"
        }
        return "curefit://userform?formId=ai-trainer-gymfit-fitness-plan-creation"
    }

    private getGymLoggingPageUrl(sessionId: string, bucketIdGym: string, bucketIdFeedback: string) {
        if (bucketIdGym === "2" && bucketIdFeedback === "1") {
            this.logger.info("Hello from experiment")
            return `curefit://exercise_logging_screen?muscle=[]&checkInId=${sessionId}`
        } else {
            return `curefit://fl_listpage?pageId=gym_activity_logging&fromPage=feedback_page&checkinId=${sessionId}&fromAutoLaunch=true`
        }
    }
}

export default GymfitCheckinQueueListener