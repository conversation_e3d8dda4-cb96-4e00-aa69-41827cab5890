import { inject, injectable } from "inversify"
import { GymfitCheckIn, MediaType } from "@curefit/gymfit-common"
import { SupportListPageView } from "../crm/SupportListPageView"
import { SupportActionableCardWidget, SupportEmptyListingWidget } from "../page/PageWidgets"
import { PageWidget } from "../page/Page"
import { Action } from "@curefit/apps-common"
import AppUtil, { SUPPORT_DEEP_LINK } from "../util/AppUtil"
import { TimeUtil } from "@curefit/util-common"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { IGymfitBusiness } from "./GymfitBusiness"
import { UserContext } from "@curefit/userinfo-common"
import * as _ from "lodash"
import IssueBusiness from "../crm/IssueBusiness"
import { ISegmentService } from "@curefit/vm-models"

@injectable()
export default class GymListPageViewBuilder {
    constructor(
        @inject(CUREFIT_API_TYPES.GymfitBusiness) private gymfitBusiness: IGymfitBusiness,
        @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness,
        @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService,
    ) {}

    async buildGymClassListPageViewBuilder(userContext: UserContext, gymfitCheckIns: GymfitCheckIn[], pageNumber: number, isOnePassPage?: boolean): Promise<SupportListPageView> {
        const widgets: PageWidget[] = []
        let actions: Action[]
        if (_.isEmpty(gymfitCheckIns) && !(await AppUtil.isNewSupportInFlutterSupported(this.segmentService, userContext))) {
            if (pageNumber === 1) {
                widgets.push(new SupportEmptyListingWidget("CULT", "No Workouts Yet", "Checkout gyms and get your fitness journey going with the best gyms in your city #BeBetterEveryday"))
                actions = [{
                    actionType: "NAVIGATION",
                    url: isOnePassPage ? "curefit://allgyms?centerType=ONEPASS" : "curefit://allgyms?centerType=GYM",
                    title: "Explore Gyms"
                }]
            }
        } else {
            for (const checkIn of gymfitCheckIns) {
                widgets.push(await this.getCheckInCard(userContext, checkIn))
            }
        }
        return new SupportListPageView(widgets, actions)
    }

    async buildGymClassListWidgetsBuilder(userContext: UserContext, gymfitCheckIns: GymfitCheckIn[]): Promise<SupportActionableCardWidget[]> {
        const widgets: SupportActionableCardWidget[] = []
        if (!_.isEmpty(gymfitCheckIns)) {
            for (const checkIn of gymfitCheckIns) {
                widgets.push(await this.getCheckInCard(userContext, checkIn))
            }
        }
        return widgets
    }

    private async getCheckInCard(userContext: UserContext, checkIn: GymfitCheckIn): Promise<SupportActionableCardWidget> {
        const tz = userContext.userProfile.timezone
        const imageMedia = checkIn.event.baseEvent.center.media && checkIn.event.baseEvent.center.media.clpMedia ?
            checkIn.event.baseEvent.center.media.clpMedia.find(media => media.type === MediaType.IMAGE) : undefined
        const issues = await this.issueBusiness.getGymfitFitnessCheckinIssues(parseInt(checkIn.id),
            this.gymfitBusiness.getGymCheckInStatus(userContext.userProfile.timezone, checkIn), userContext)
        const meta = !(await AppUtil.isNewSupportInFlutterSupported(this.segmentService, userContext)) ? { issues } : {
            issues,
            title: checkIn.event.baseEvent.activity ? checkIn.event.baseEvent.activity.name : undefined,
            subTitle: "What is your primary concern with the activity"
        }
        return {
            widgetType: "SUPPORT_ACTIONABLE_CARD_WIDGET",
            title: checkIn.event.baseEvent.activity ? checkIn.event.baseEvent.activity.name : undefined,
            subTitle: checkIn.event.baseEvent.center ? checkIn.event.baseEvent.center.name : undefined,
            footer: [{
                text: `${TimeUtil.formatDateInTimeZone(tz, new Date(checkIn.startTime), "D MMM | h:mm A")}`,
            }],
            cardAction: {
                actionType: "NAVIGATION",
                url: SUPPORT_DEEP_LINK,
                meta: meta,
            },
            imageUrl: imageMedia ? imageMedia.mediaUrl : "image/<EMAIL>",
            status: this.gymfitBusiness.getCheckInStatus(tz, checkIn),
            time: AppUtil.isWeb(userContext) ? `${TimeUtil.formatDateInTimeZone(tz, new Date(checkIn.startTime), "D MMM | h:mm A")}` : undefined,
            timestamp: checkIn.startTime,
            timezone: tz
        }
    }
}
