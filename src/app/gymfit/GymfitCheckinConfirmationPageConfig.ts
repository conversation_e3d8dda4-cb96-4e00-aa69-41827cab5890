import { inject, injectable } from "inversify"
import { DEFAULT_CACHE_REFRESH_TIME_IN_SECONDS, InMemoryCacheService } from "@curefit/memory-cache"
import { IPageConfigReadOnlyDao, IPageConfigReadWriteDao, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import { BASE_TYPES, ILogger } from "@curefit/base"
import * as _ from "lodash"
import { InstructionItem } from "@curefit/product-common"
import { WhatToDo } from "./GymfitCheckinConfirmationView"

@injectable()
class GymfitCheckinConfirmationPageConfig extends InMemoryCacheService<any> {

    constructor(
        @inject(PAGE_CONFIG_TYPES.PageConfigReadOnlySecondaryDao) protected pageConfigDao: IPageConfigReadOnlyDao,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger, 38 * 60)
        this.load()
    }

    loadData(): Promise<any> {
        return this.pageConfigDao.findOne({pageId: {$eq: "GymfitCheckinConfirmationPageConfig"}}).then(pageConfig => {
            const data = pageConfig.data
            this.whatToDo = _.map(<InstructionItem[]>data.instructionItem, intruction => {
                return {
                    title: intruction.text,
                    subtitle: intruction.code,
                    icon: intruction.iconType
                }
            })
            return data
        })
    }

    public whatToDo: WhatToDo[]
}

export default GymfitCheckinConfirmationPageConfig
