import { UserContext } from "@curefit/userinfo-common"
import {
    GymfitCenter,
    GymfitCenterCategory,
    GymFitCenterSchedule,
    GymfitCenterType,
    GymfitCheckIn,
    GymfitCheckInClassification,
    GymfitEvent,
    GymfitMembershipRestriction,
    GymfitStatus,
    GymfitUserAccessType,
    MediaType,
    RestrictionType,
    TransientQRCodeResponse,
    TransientQRCodeWithAccessDetails,
    TrialUsage,
    UserAccessDetail,
    UserMembershipsAndTrialUsages,
} from "@curefit/gymfit-common"
import {
    Action,
    ActionCardWidget,
    CenterSchedule,
    CreditsFooter,
    DescriptionWidget,
    ExpandableInfoWidget,
    Header,
    InfoCard,
    ProductListWidget,
    QRWidget,
    WidgetView
} from "../common/views/WidgetView"
import { PageWidget } from "../page/Page"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { CreditPillData, HeaderWidget } from "../page/PageWidgets"
import * as _ from "lodash"
import AppUtil, { SUPPORT_DEEP_LINK } from "../util/AppUtil"
import { inject, injectable } from "inversify"
import { GymCheckinSummaryWidget, WidgetType } from "@curefit/apps-common"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { IGymfitBusiness } from "./GymfitBusiness"
import IssueBusiness from "../crm/IssueBusiness"
import GymfitUtil from "../util/GymfitUtil"
import { MembershipDetails } from "@curefit/cult-common"
import SoftBookingBuilder from "./SoftBookingBuilder"
import { AlertError } from "../common/errors/AlertError"
import { ActionUtilV1, SeoUrlParams } from "@curefit/base-utils"
import { Benefit, Membership } from "@curefit/membership-commons"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { ISegmentService } from "@curefit/vm-models"
import CreditsView from "../common/views/CreditsViewWidget"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import WidgetBuilder from "../page/vm/WidgetBuilder"
import { NUDGE_BANNER_ID } from "../util/CultUtil"

interface CenterInfoWidget {
    widgetType: WidgetType,
    address: {
        addressString: string,
        pincode: string,
        latLong: { lat: number, long: number }
    },
    mapUrl: string
    eventData?: any
}

interface GymCheckinInfoWidget {
    widgetType: WidgetType,
    premiumTagUri?: string,
    centerName: string,
    isPremiumGymSessionsExhausted?: boolean,
    message?: string
}

@injectable()
export default class GymfitCheckinViewBuilder {
    constructor(
        @inject(CUREFIT_API_TYPES.GymfitBusiness) private gymfitBusiness: IGymfitBusiness,
        @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness,
        @inject(CUREFIT_API_TYPES.SoftBookingBuilder) private softBookingBuilder: SoftBookingBuilder,
        @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
        @inject(CUREFIT_API_TYPES.SegmentService) public segmentService: ISegmentService,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(CUREFIT_API_TYPES.WidgetBuilder) public widgetBuilder: WidgetBuilder,
    ) {}

    // ASK: will adding async here cause any error
    async buildView(userContext: UserContext, gymfitCenter: GymfitCenter, membershipDetails: Membership[], gymfitEvent: GymfitEvent, checkInClassification: string, centerSchedule: GymFitCenterSchedule[]): Promise<{ widgets: (WidgetView | PageWidget)[]; actions?: Action[]; eventData?: any; meta: any} > {
        const widgets: (WidgetView | PageWidget)[] = []
        const actions: Action[] = []
        // for web new url support
        const meta = {
            city: gymfitCenter.address?.city?.name,
            gymName: gymfitCenter.name,
        }
        let eventData: any
        if (checkInClassification === GymfitCheckInClassification.SOFTBOOKING) {
            widgets.push(...await this.softBookingBuilder.getSoftBookingPopupView(userContext, gymfitCenter, centerSchedule, this.hamletBusiness, this.segmentService))
            eventData =  { type: "Cult_Pass_Soft_Booking_Modal" }
        } else {
            widgets.push(this.getHeaderWidget(userContext, gymfitCenter))
            const bannerwidgetPromise = GymfitUtil.buildBannerWidget(userContext, NUDGE_BANNER_ID, this.widgetBuilder, this.serviceInterfaces)
            if (bannerwidgetPromise) {
                widgets.push(await bannerwidgetPromise)
            }
            if (AppUtil.isWeb(userContext)) {
                const descriptionWidget = new DescriptionWidget([this.getTimingInfoCard(userContext, gymfitCenter, gymfitEvent), this.getAddressInfoCard(gymfitCenter)])
                widgets.push(descriptionWidget)
            } else {
                widgets.push(this.getTimingWidget(userContext, gymfitCenter, gymfitEvent))
                if (gymfitEvent && gymfitEvent.baseEvent && gymfitEvent.baseEvent.meta && gymfitEvent.baseEvent.meta.whatToCarry) {
                    widgets.push(this.getNotesWidget(gymfitEvent.baseEvent.meta.whatToCarry, true))
                } else if (gymfitCenter.baseEvents && gymfitCenter.baseEvents.length && gymfitCenter.baseEvents[0].meta && gymfitCenter.baseEvents[0].meta.whatToCarry) {
                    widgets.push(this.getNotesWidget(gymfitCenter.baseEvents[0].meta.whatToCarry, true))
                }
                widgets.push(this.getAddressWidget(gymfitCenter))
            }

            const presentGoldMembership = GymfitUtil.getPresentGoldMembership(membershipDetails)
            const benefit = this.getBenefitForGymCheckin(userContext, presentGoldMembership, gymfitCenter)

            actions.push(this.getCheckinAction(gymfitCenter, gymfitEvent, gymfitCenter.isOperational, benefit))
        }
        return { widgets, actions, eventData, meta }
    }

    async buildDetailedView(userContext: UserContext, gymfitCenter: GymfitCenter, checkIn: GymfitCheckIn): Promise<{ widgets: (WidgetView | PageWidget)[], actions: Action[] }> {
        const widgets: (WidgetView | PageWidget)[] = []
        const actions: Action[] = []
        widgets.push(await this.getCheckInInfoCard(userContext, checkIn))
        widgets.push(this.getCenterInfoActionCard(userContext, gymfitCenter))
        widgets.push(this.getCheckInTimeAndAction(userContext.userProfile.timezone, checkIn))
        widgets.push(this.getCenterAddressInfo(gymfitCenter))
        widgets.push(GymfitUtil.getImportantUpdatesWidget(userContext))
        const gymfitEvent = checkIn.event
        if (gymfitEvent && gymfitEvent.baseEvent && gymfitEvent.baseEvent.meta && gymfitEvent.baseEvent.meta.whatToCarry) {
            widgets.push(this.getNotesWidget(gymfitEvent.baseEvent.meta.whatToCarry, false))
        } else if (gymfitCenter.baseEvents && gymfitCenter.baseEvents.length && gymfitCenter.baseEvents[0].meta && gymfitCenter.baseEvents[0].meta.whatToCarry) {
            widgets.push(this.getNotesWidget(gymfitCenter.baseEvents[0].meta.whatToCarry, false))
        }
        // Disabling biometric temporarily
        // if (checkIn.user.metadata && !checkIn.user.metadata.isBiometricRegistered) {
        //     widgets.push(this.gymfitBusiness.getBiometricWidget())
        // }
        const isOnePassCenter = gymfitCenter.category === GymfitCenterCategory.ONEPASS
        widgets.push(this.gymfitBusiness.getGymCheckinWhatToDo(userContext.userProfile.timezone, checkIn))
        const markAttendanceAction = GymfitUtil.getMarkAttendanceAction(userContext.userProfile.timezone, checkIn, isOnePassCenter)
        if (!_.isNil(markAttendanceAction)) {
            actions.push(markAttendanceAction)
        }
        return {widgets, actions}
    }

    async buildQuickCheckinView(userContext: UserContext, gymfitCenter: GymfitCenter, trialUsage: TrialUsage, cultCurrentMembership: MembershipDetails, hasComplimentaryAccess: boolean, centerId: number, centerSchedule: GymFitCenterSchedule[], transientQRCodeWithAccessDetails: TransientQRCodeWithAccessDetails | null, onLoadAlert?: AlertError, hasCultSelectPackAccess?: boolean, isLimitedElitePack?: boolean, isLimitedCenterElitePack?: boolean, defaultSessionLeftText?: string): Promise<{ header: Header, widgets: WidgetView[], onLoadAlert?: AlertError }> {
        const widgets: WidgetView[] = []
        const header: Header = {title: "Check-in"}
        let isPremiumGymSessionsExhausted: boolean = false
        let isGymSessionsExhausted: boolean = false
        let isLuxSessionsExhausted: boolean = false
        let userBlackCenterAccess: UserAccessDetail
        let userGoldCenterAccess: UserAccessDetail
        let userLuxCenterAccess: UserAccessDetail
        const isOnePassPage = gymfitCenter.type === "ONEPASS"
        if (transientQRCodeWithAccessDetails?.userAccessDetails) {
            userBlackCenterAccess = _.find(transientQRCodeWithAccessDetails.userAccessDetails, access => access.centerType === GymfitCenterType.BLACK)
            userGoldCenterAccess = _.find(transientQRCodeWithAccessDetails.userAccessDetails, access => access.centerType === GymfitCenterType.GOLD)
            userLuxCenterAccess = _.find(transientQRCodeWithAccessDetails.userAccessDetails, access => access.centerType === GymfitCenterType.LUX)
        }
        if (userBlackCenterAccess?.accessType === GymfitUserAccessType.LIMITED) {
            isPremiumGymSessionsExhausted = userBlackCenterAccess.usedCount >= userBlackCenterAccess.maxCount
        }

        if (userGoldCenterAccess?.accessType === GymfitUserAccessType.LIMITED) {
            isGymSessionsExhausted = userGoldCenterAccess.usedCount >= userGoldCenterAccess.maxCount
        }

        if (userLuxCenterAccess?.accessType === GymfitUserAccessType.LIMITED) {
            isLuxSessionsExhausted = userLuxCenterAccess.usedCount >= userLuxCenterAccess.maxCount
        }

        let sessionsLeftText = defaultSessionLeftText
        if (gymfitCenter.type === GymfitCenterType.BLACK) {
            sessionsLeftText = userBlackCenterAccess?.note ?? sessionsLeftText
        } else if (gymfitCenter.type === GymfitCenterType.GOLD) {
            sessionsLeftText = userGoldCenterAccess?.note ?? sessionsLeftText
        } else if (gymfitCenter.type === GymfitCenterType.LUX) {
            sessionsLeftText = userGoldCenterAccess?.note ?? sessionsLeftText
        }

        let creditCostRequired = null
        if (onLoadAlert && onLoadAlert?.meta) {
            const accessObj = onLoadAlert?.meta["accessObject"]
            if (accessObj && accessObj?.accessThroughBenefit === "ACCESS_CREDITS") {
                creditCostRequired = accessObj?.requiredCount || null
            }
        } else {
            if (gymfitCenter.type === GymfitCenterType.BLACK) {
                if (userBlackCenterAccess?.accessThroughBenefit === "ACCESS_CREDITS") {
                    creditCostRequired = userBlackCenterAccess?.requiredCount || null
                }
            } else if (gymfitCenter.type === GymfitCenterType.GOLD) {
                if (userGoldCenterAccess?.accessThroughBenefit === "ACCESS_CREDITS") {
                    creditCostRequired = userGoldCenterAccess?.requiredCount || null
                }
            }
        }

        let lockQrDescription: string = null
        if (onLoadAlert) {
            lockQrDescription = onLoadAlert?.title || null
        }

        widgets.push(this.getQrWidget(userContext, trialUsage, cultCurrentMembership, hasComplimentaryAccess, transientQRCodeWithAccessDetails?.transientQRCodeResponse ? transientQRCodeWithAccessDetails?.transientQRCodeResponse : null, gymfitCenter, isPremiumGymSessionsExhausted, isGymSessionsExhausted, sessionsLeftText, creditCostRequired, lockQrDescription))

        if (gymfitCenter.type === GymfitCenterType.BLACK) {
            widgets.push(this.getPremiumCenterInfoCard(gymfitCenter, userBlackCenterAccess, isPremiumGymSessionsExhausted, sessionsLeftText))
        }

        if (gymfitCenter.type === GymfitCenterType.GOLD && userGoldCenterAccess?.accessType === GymfitUserAccessType.LIMITED) {
            widgets.push(this.getGoldCenterInfoCard(gymfitCenter, userGoldCenterAccess, isGymSessionsExhausted, sessionsLeftText))
        }

        if (gymfitCenter.type === GymfitCenterType.LUX && userGoldCenterAccess?.accessType === GymfitUserAccessType.LIMITED) {
            widgets.push(this.getGoldCenterInfoCard(gymfitCenter, userLuxCenterAccess, isLuxSessionsExhausted, sessionsLeftText))
        }

        widgets.push(this.getGymScehduleDetails(userContext, centerSchedule, gymfitCenter))
        if (trialUsage?.status === GymfitStatus.ACTIVE && !cultCurrentMembership && !hasComplimentaryAccess && !isOnePassPage) {
            header.title = "Try For Free"
            widgets.push(this.getTrialDetails())
        }
        // NOTE_WIDGET REMOVAL IN CASE OF CLPV2
        if (!(await AppUtil.isCreditBasedMembershipCheck(userContext, this.segmentService))) {
            if (centerId) {
                const noteWidget = this.getQuickCheckinNote(userContext, centerId, isOnePassPage)
                if (userContext?.sessionInfo?.clientVersion && userContext?.sessionInfo?.clientVersion >= 10.02) {
                    noteWidget.widgetType = "NOTE_WIDGET"
                }
                widgets.push(noteWidget)
            }
        }
        return {header, widgets, onLoadAlert}
    }

    private getPremiumCenterInfoCard(gymfitCenter: GymfitCenter, userBlackCenterAccess: UserAccessDetail, isPremiumGymSessionsExhausted: boolean, message: string): GymCheckinInfoWidget {
        return {
            widgetType: "GYM_CHECKIN_INFO_WIDGET",
            premiumTagUri: "/image/icons/cult/elite_tag.png",
            centerName: gymfitCenter.name,
            isPremiumGymSessionsExhausted,
            message
        }
    }

    private getGoldCenterInfoCard(gymfitCenter: GymfitCenter, userGoldCenterAccess: UserAccessDetail, isPremiumGymSessionsExhausted: boolean, message: string): GymCheckinInfoWidget {
        return {
            widgetType: "GYM_CHECKIN_INFO_WIDGET",
            premiumTagUri: "/image/icons/cult/pro_tag.png",
            centerName: gymfitCenter.name,
            isPremiumGymSessionsExhausted,
            message
        }
    }

    private async getCheckInInfoCard(userContext: UserContext, checkIn: GymfitCheckIn): Promise<GymCheckinSummaryWidget> {
        let image = ""
        if (checkIn.event.baseEvent.center.media && checkIn.event.baseEvent.center.media.clpMedia) {
            const media = checkIn.event.baseEvent.center.media.clpMedia.find((media) => media.type === MediaType.IMAGE)
            image = media ? media.mediaUrl : ""
        }
        let title = "Gym Session"
        if (checkIn.centerOffering.center.category === GymfitCenterCategory.GX || checkIn.centerOffering.center.category === GymfitCenterCategory.ONEPASS) {
            title = `${checkIn.event.baseEvent.activity.name} session`
        }
        let subTitle = TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, new Date(checkIn.startTime), "ddd, MMM DD")
        if (checkIn.checkInClassification === GymfitCheckInClassification.SOFTBOOKING) {
            subTitle = TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, new Date(checkIn.endTime), "ddd, MMM DD")
        }
        return {
            widgetType: "GYM_CHECKIN_SUMMARY_WIDGET",
            title,
            subTitle: subTitle,
            action: await this.buildMoreActions(userContext, checkIn),
            image
        }
    }

    private async buildMoreActions(userContext: UserContext, checkin: GymfitCheckIn): Promise<Action> {
        const issues = await this.issueBusiness.getGymfitFitnessCheckinIssues(parseInt(checkin.id),
            this.gymfitBusiness.getGymCheckInStatus(userContext.userProfile.timezone, checkin), userContext)
        const actions: Action[] = []
        actions.push({
            actionType: "REPORT_ISSUE",
            actionId: "REPORT_ISSUE",
            title: "Need Help",
            url: SUPPORT_DEEP_LINK,
            meta: {issues}
        })
        return {actionType: "ACTION_LIST", actions}
    }

    private getCenterInfoActionCard(userContext: UserContext, gymfitCenter: GymfitCenter): ActionCardWidget {
        const seoUrlParam: SeoUrlParams = GymfitUtil.getCenterSeoUrlParam(gymfitCenter)
        const isLux = GymfitUtil.isLuxCenter(gymfitCenter)
        const webUrl = ActionUtilV1.getGymCenterWebUrl(seoUrlParam, gymfitCenter.id)
        const webUrlLux = webUrl.replace(/\/cult\/cult-pass\//g, "/luxury/gyms/")
        const isNewCenterDetailsPageSupported =  AppUtil.isNewCenterDetailsPageSupported(userContext)
        return {
            widgetType: "ACTION_CARD_WIDGET",
            displayText: `${gymfitCenter.name}, ${gymfitCenter.locality}`,
            icon: {
                iconType: "HOME_ICON"
            },
            action: {
                actionType: "NAVIGATION",
                title: "VIEW",
                url: AppUtil.isWeb(userContext) ? isLux ? webUrlLux : webUrl : isNewCenterDetailsPageSupported ? `curefit://fitnesscenter?centerId=${gymfitCenter.centerServiceId}` : `curefit://gymfitcenter?centerId=${gymfitCenter.id}`
            }
        }
    }

    private getCheckInTimeAndAction(tz: Timezone, checkIn: GymfitCheckIn): ActionCardWidget {
        const isDisabled = !(this.gymfitBusiness.isUpcomingCheckIn(tz, checkIn))
        let action: Action = undefined
        if (!isDisabled) {
            action = {
                actionType: "CANCEL_GYM_CHECKIN",
                meta: {
                    checkinId: checkIn.id
                },
                completionAction: {
                    actionType: "POP_ACTION"
                },
                title: "CANCEL"
            }
        }
        return {
            widgetType: "ACTION_CARD_WIDGET",
            icon: {
                iconType: "CANCEL",
            },
            action,
            isDisabled,
            displayText: this.getCheckinTiming(tz, checkIn.startTime, checkIn.endTime, checkIn.checkInClassification),
        }
    }

    private getCenterAddressInfo(gymfitCenter: GymfitCenter): CenterInfoWidget {
        return {
            widgetType: "CENTER_INFO_WIDGET",
            mapUrl: gymfitCenter.address.mapUrl,
            address: {
                addressString: gymfitCenter.address.addressLine1,
                pincode: gymfitCenter.address.pincode ? gymfitCenter.address.pincode.toString() : "",
                latLong: {lat: gymfitCenter.address.latitude, long: gymfitCenter.address.longitude }
            },
            eventData: {
                pageFrom: "GymsCenter",
                pageType: "navigate_center",
            }
        }
    }

    private getCheckinTiming(tz: Timezone, startTime: number, endTime: number, checkInClassification: GymfitCheckInClassification) {
        const start = new Date(startTime)
        const end = new Date(endTime)
        if (checkInClassification === GymfitCheckInClassification.SOFTBOOKING) {
            return `Before ${TimeUtil.get12HRTimeFormat(end, tz, true)}, ${GymfitUtil.getDayOfWeekShortNameByNumber(end.getDay() - 1)}` // zero index
        }
        const day = TimeUtil.getDayText(TimeUtil.formatDateInTimeZone(tz, start), tz)
        return `${day}, ${TimeUtil.get12HRTimeFormat(start, tz, true)} - ${TimeUtil.get12HRTimeFormat(end, tz, true)}`
    }

    private getHeaderWidget(userContext: UserContext, gymfitCenter: GymfitCenter): PageWidget {
        const headerWidget = new HeaderWidget({
            title: `${gymfitCenter.name}, ${gymfitCenter.locality}`
        }, true)
        headerWidget.hasBottomPadding = true
        headerWidget.hasDividerBelow = false
        return headerWidget
    }

    private getSubTitleFromCenterCategory(centerCategory: GymfitCenterCategory) {
        switch (centerCategory) {
            case GymfitCenterCategory.GA:
                return "Gym Workout"
            default:
                return ""
        }
    }

    private getNotesWidget(whatToCarry: string, showDivider: boolean): DescriptionWidget {
        const descriptionWidget = new DescriptionWidget([{
            title: "Notes",
            subTitle: whatToCarry
        }])
        descriptionWidget.showWidgetDivider = showDivider
        return descriptionWidget
    }

    private getTimingWidget(userContext: UserContext, gymfitCenter: GymfitCenter, gymfitEvent: GymfitEvent): DescriptionWidget {
        const descriptionWidget = new DescriptionWidget([this.getTimingInfoCard(userContext, gymfitCenter, gymfitEvent)])
        descriptionWidget.showWidgetDivider = true
        return descriptionWidget
    }

    private getTimingInfoCard(userContext: UserContext, gymfitCenter: GymfitCenter, gymfitEvent: GymfitEvent): InfoCard {
        let todaysSchedule: GymFitCenterSchedule = undefined
        _.forEach(gymfitCenter.schedules, schedule => {
            if (schedule.dayOfWeek === this.getDay(userContext)) {
                todaysSchedule = schedule
            }
        })
        const tz = userContext.userProfile.timezone
        const checkinValidityLimitMinutes = gymfitCenter.offerings[0].duration * 60
        let checkinValidityMins = undefined
        let subTitle
        if (gymfitEvent) {
            const fromDate = new Date(gymfitEvent.fromTimeUTC)
            const toDate = new Date(gymfitEvent.toTimeUTC)
            subTitle = `${TimeUtil.get12HRTimeFormat(fromDate, tz, true)} - ${TimeUtil.get12HRTimeFormat(toDate, tz, true)}`
        } else if (todaysSchedule) {
            const gymEndDate = this.getTime(todaysSchedule.toTime, tz)
            checkinValidityMins = TimeUtil.diffInMinutes(tz, TimeUtil.formatDateInTimeZone("UTC", gymEndDate, "YYYY-MM-DD HH:mm:ss"), TimeUtil.formatDateInTimeZone(tz, TimeUtil.getDateNow(tz), "YYYY-MM-DD HH:mm:ss"))
            const startCheckinTime = TimeUtil.formatDateInTimeZone(tz, TimeUtil.getDateNow(tz), "h:mm A")
            const dateNow = TimeUtil.getDateNow(tz)
            dateNow.setMinutes(dateNow.getMinutes() + checkinValidityLimitMinutes)
            const endCheckinTime = checkinValidityMins < checkinValidityLimitMinutes ? TimeUtil.formatDateInTimeZone("UTC", gymEndDate, "h:mm A") : TimeUtil.formatDateInTimeZone(tz, dateNow, "h:mm A")
            const maxCheckinDuration = Math.min(checkinValidityLimitMinutes, checkinValidityMins)
            const hours = Math.floor(maxCheckinDuration / 60)
            const mins = maxCheckinDuration % 60
            subTitle = `${startCheckinTime} - ${endCheckinTime}. Valid for ${hours > 0 ? hours + (hours === 1 ? " hour" : " hours") : ""}${hours > 0 && mins > 0 ? " and " : ""}${mins > 0 ? mins + " minutes" : ""}`
        } else {
            subTitle = "Closed now"
        }
        return {
            title: gymfitEvent ? gymfitEvent.baseEvent.activity.name : "Timing",
            state: (checkinValidityMins && (checkinValidityMins < checkinValidityLimitMinutes)) ? "Closing soon" : undefined,
            subTitle,
        }
    }

    private getAddressWidget(gymfitCenter: GymfitCenter): DescriptionWidget {
        const descriptionWidget = new DescriptionWidget([this.getAddressInfoCard(gymfitCenter)])
        return descriptionWidget
    }

    private getAddressInfoCard(gymfitCenter: GymfitCenter): InfoCard {
        const address = gymfitCenter.address
        const city = address.city ? address.city.name : address.cityId
        const state = address.city && address.city.state ? address.city.state : ""
        const country = address.city ? (address.city.country ? address.city.country.name : address.city.countryId) : ""
        const addressLine = (address.addressLine1 ? address.addressLine1 + ", " : "") + (address.addressLine2 ? address.addressLine2 : "")
        const addressText = `${addressLine} (${city}, ${state} ${address.pincode}, ${country})`
        return {
            title: "Address",
            subTitle: addressText,
            knowMore: {
                actionType: "OPEN_MAP",
                url: gymfitCenter.address.mapUrl,
                title: "NAVIGATE",
                meta: {
                    titleProps: {
                        style: {
                            textAlign: "left",
                            padding: 0,
                            paddingVertical: 12
                        }
                    }
                }
            },
        }
    }

    private getQrWidget(userContext: UserContext, trialUsage: TrialUsage, cultCurrentMembership: MembershipDetails, hasComplimentaryAccess: boolean, transientQRCode: TransientQRCodeResponse | null, gymfitCenter: GymfitCenter, isPremiumSessionsExhausted: boolean, isGymSessionsExhausted: boolean, sessionsLeftText?: string, creditCostRequired?: number, lockQrDescription?: string): QRWidget {
        // CREDITS LOGIC
        const creditCost = creditCostRequired
        const creditsData = AppUtil.isWeb(userContext) && creditCost ? new CreditsView(creditCost, `${creditCost > 1 ? "Credits" : "Credit"} required to check-in at the centre`) : null
        const creditPillData: CreditPillData = creditCost ? {
            credit: creditCost.toString()
        } : null
        const creditsFooter: CreditsFooter = {
            creditPillData: creditPillData,
            description: creditCost ? "Credit" + ((creditCost === 1) ? "" : "s") + " required to check-in at the center" : null,
        }

        const qrCodeWidget: QRWidget = {
            widgetType: "QR_WIDGET",
            showWidgetDivider: true,
            data: {
                qrCodeInfo: {
                  type: "DYNAMIC",
                  qrCodeString: transientQRCode?.qrString ?? "We Are Cult!!!",
                  refreshUrl: "/gymfit/checkinQr",
                  refreshInterval: transientQRCode?.timeOfExpiration ? transientQRCode?.timeOfExpiration / 1000 : 0,
                  refreshIcon: "/image/icons/gymfit/refresh.png",
                  refreshText: transientQRCode?.qrString ? "Click to reload QR code" : "Click to load QR code"
                },
                footer: {
                  title: "",
                  description: trialUsage?.status === GymfitStatus.ACTIVE && !cultCurrentMembership && !hasComplimentaryAccess ? "Scan the QR code at the gym entry to avail free workout" : "To check-in, scan QR code at gym entry"
                },
                creditsFooter,
                lockQrDescription: transientQRCode?.qrString ? null : lockQrDescription
            },
            creditsData,
            containerStyle: {color: (gymfitCenter.type === GymfitCenterType.BLACK && isPremiumSessionsExhausted) || ((gymfitCenter.type === GymfitCenterType.GOLD || gymfitCenter.type === GymfitCenterType.LUX) && isGymSessionsExhausted) ? "#F3EDED" : "#000000"}
        }
        return qrCodeWidget
    }

    private getGymScehduleDetails(userContext: UserContext, centerSchedule: GymFitCenterSchedule[], gymfitCenter: GymfitCenter): ExpandableInfoWidget {
        const isWeb = AppUtil.isWeb(userContext)
        const schedule: CenterSchedule[] = GymfitUtil.getScheduleTimes(centerSchedule, isWeb ? ", " : "\n")
        const todaysScehdule = GymfitUtil.getTodaysSchedule(schedule)
        const mergedScehdule = GymfitUtil.mergeSameDaySchedule(schedule)
        const isGymOpen = GymfitUtil.isGymOpen(todaysScehdule, gymfitCenter)
        const items: { title: string, subTitle: string } [] = []
        mergedScehdule.forEach(day => items.push({
            title: day.title,
            subTitle: day.value
        }))
        const gymScheduleWidget: ExpandableInfoWidget = {
            widgetType: "EXPANDABLE_INFO_WIDGET",
            data: {
                header: {
                    title: "Gym Opening Hours",
                    style: { fontSize: 18 }
                },
                title: isGymOpen ? "Open" : "Closed",
                titleStyle: isGymOpen ? { color: "#4ab74a"} : { color: "#b00020" },
                subTitle: isGymOpen ? todaysScehdule.value : "Check Timings",
                itemsHighlight: isGymOpen ? todaysScehdule.value.replace("\n", " &\n") : "",
                expandedItems: items,
            }
        }
        return gymScheduleWidget
    }

    private getTrialDetails(): ProductListWidget {
        const header: Header = {
            title: "What To Expect",
            color: "#000000",
            style: { fontSize: 18, color: "#000000" }
        }
        const items: InfoCard[] = [
            {
                subTitle: "Guided gym tour"
            },
            {
                subTitle: "Free body composition check"
            },
            {
                subTitle: "Free workout with trainer guidance"
            }
        ]
        const trialDetailsWidget = new ProductListWidget("BULLET", header, items)
        trialDetailsWidget.style = { paddingHorizontal: 15 }
        trialDetailsWidget.styles = {
            bulletContainer: { width: 9 },
            bulletTextStyle: {
                width: 4,
                height: 4,
                backgroundColor: "#888e9e"
            }
        }
        trialDetailsWidget.hideSepratorLines = true
        return trialDetailsWidget
    }

    private getQuickCheckinNote(userContext: UserContext, centerId: number, isOnePassPage?: boolean): DescriptionWidget {
        const knowMoreAction = isOnePassPage ? GymfitUtil.getOnePassSlotBookingAction(userContext, centerId, "BOOK SLOT") : GymfitUtil.getSlotBookingAction(userContext, centerId, "BOOK SLOT")
        const knowMoreMeta = knowMoreAction.meta || {}
        knowMoreAction.meta = {
            ...knowMoreMeta,
            titleProps: {
                style: {
                textAlign: "left",
                padding: 0,
                paddingVertical: 8
                }
            }
        }
        const quickCheckinNote: DescriptionWidget = new DescriptionWidget([
            {
                title: "Note",
                subTitle: "Limited capacity at Gym. We recommend booking.",
                knowMore: knowMoreAction,
                style: { fontSize: 18, color: "#4c4c4c" },
                readMoreProps: { textStyle: { lineHeight: 22, color: "#4c4c4c", fontFamily: "Inter-Medium" }}
            }
        ])
        quickCheckinNote.showDivider = false
        return quickCheckinNote
    }

    private getCheckinAction(gymfitCenter: GymfitCenter, gymfitEvent: GymfitEvent, isCenterOperational: boolean, benefit: Benefit): Action {
        return {
            actionType: "GYMFIT_CHECKIN",
            title: "BOOK",
            disabled: !this.isBookEnabled(isCenterOperational, benefit),
            url: "curefit://orderconfirmationv1?pageName=booking_confirmation_gym",
            meta: {
                centerOfferingId: gymfitEvent ? undefined : gymfitCenter.offerings[0].id,
                eventId: gymfitEvent ? gymfitEvent.id : undefined,
                centerId: gymfitCenter.id
            }
        }
    }

    private getDay(userContext: UserContext) {
        return TimeUtil.getMomentNow(userContext.userProfile.timezone).format("dddd").toUpperCase()
    }

    private getTime(time: string, tz: Timezone): Date {
        const times: string[] = time.split(":")
        const date = TimeUtil.getDateNow(tz)
        date.setHours(parseInt(times[0]), parseInt(times[1]), parseInt(times[2]))
        return date
    }

    getBenefitTypeFromCenterCategory(centerCategory: GymfitCenterCategory) {
        if (centerCategory === GymfitCenterCategory.GA) {
            return "GYMFIT_GA"
        }
        if (centerCategory === GymfitCenterCategory.GX) {
            return "GYMFIT_GX"
        }
        return ""
    }

    private getMembershipRestriction(userContext: UserContext, membershipDetails: UserMembershipsAndTrialUsages, gymfitCenter: GymfitCenter): GymfitMembershipRestriction | null {
        if (!membershipDetails.membershipSummary || !membershipDetails.membershipSummary.activeMembership || !membershipDetails.membershipSummary.activeMembership.restrictions) {
            return null
        }
        const timeNow = TimeUtil.getDateNow(userContext.userProfile.timezone).getTime()
        return _.find(membershipDetails.membershipSummary.activeMembership.restrictions, restriction => {
            const restrictionType = restriction.restrictionType
            if (restrictionType === RestrictionType.UNLIMITED) {
                return restriction.centerCategories.includes(gymfitCenter.category)
            } else if (restrictionType === RestrictionType.MONTHLY) {
                const startDate = TimeUtil.getMomentForDateString(restriction.startDate, userContext.userProfile.timezone).toDate().getTime()
                const endDate = TimeUtil.getMomentForDateString(restriction.endDate, userContext.userProfile.timezone).toDate().getTime()
                return restriction.centerCategories.includes(gymfitCenter.category) && timeNow > startDate && timeNow < endDate
            } else if (restrictionType === RestrictionType.LIMITED) {
                return restriction.centerCategories.includes(gymfitCenter.category)
            }
            return false
        })
    }

    private getBenefitForGymCheckin(userContext: UserContext, presentGoldMembership: Membership | null, gymfitCenter: GymfitCenter): Benefit | null {
        if (!presentGoldMembership) {
            return null
        }
        const benefitTypeRequired = this.getBenefitTypeFromCenterCategory(gymfitCenter.category)
        for (let currentBenefitIndex = 0; currentBenefitIndex < presentGoldMembership.benefits.length; currentBenefitIndex++) {
            const benefitName = presentGoldMembership.benefits[currentBenefitIndex].name
            if (benefitName === benefitTypeRequired) {
                return presentGoldMembership.benefits[currentBenefitIndex]
            }
        }
        return null
    }

    private isBookEnabled(isCenterOperational: boolean, benefit: Benefit): boolean {
        if (!isCenterOperational) return false
        if (benefit)
            return benefit.maxTickets > benefit.ticketsUsed
        return true
    }
}
