import * as express from "express"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import { Container, inject } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import AuthMiddleware from "../auth/AuthMiddleware"
import { CATALOG_CLIENT_TYPES, ICatalogueService, ICatalogueServicePMS } from "@curefit/catalog-client"
import {
    Action,
    AllMovements,
    AllMovementsPage,
    DatePickerWidget,
    GymLocalitySelector,
    Header,
    InfoWidget,
    MovementCard,
    MovementsFilter,
    ProductDetailPage,
    SearchGymsResponse
} from "../common/views/WidgetView"
import { BASE_TYPES, Logger } from "@curefit/base"
import { IOfferServiceV2, OFFER_SERVICE_CLIENT_TYPES, OfferServiceV3 } from "@curefit/offer-service-client"
import { Session, UserContext } from "@curefit/userinfo-common"
import { ProductType } from "@curefit/product-common"
import GymfitCenterDetailsWebViewBuilder from "./GymfitCenterDetailsWebViewBuilder"
import {
    CenterCategory,
    CheckInCreationRequest,
    GymfitAmenity,
    GymfitCenter,
    GymfitCenterCategory,
    GymfitCenterResponse,
    GymFitCenterSchedule,
    GymFitCenterSearchFilters,
    GymfitCenterType,
    GymfitCheckIn,
    GymfitCheckInClassification,
    GymfitCheckInSource,
    GymfitEvent,
    GymfitLocality,
    GymPtProduct,
    GymSchedule,
    ICancelCheckInParams,
    LuxFitnessProduct,
    TransientQRCodeResponse,
    TransientQRCodeWithAccessDetails
} from "@curefit/gymfit-common"
import GymfitCenterPageConfig from "./GymfitCenterPageConfig"
import * as _ from "lodash"
import AppUtil, { AWS_S3_BASE_URL } from "../util/AppUtil"
import GymsViewBuilder from "./GymsViewBuilder"
import { GYMFIT_CLIENT_TYPES, IGymfitService } from "@curefit/gymfit-client"
import GymfitCenterDetailsAppViewBuilder from "./GymfitCenterDetailsAppViewBuilder"
import GymfitPackDetailViewBuilder from "../pack/GymfitPackDetailViewBuilder"
import GymfitCheckinConfirmationView from "./GymfitCheckinConfirmationView"
import GymfitCheckinConfirmationPageConfig from "./GymfitCheckinConfirmationPageConfig"
import { ICultBusiness, PreferenceDetail } from "../cult/CultBusiness"
import { HERCULES_CLIENT_TYPES, IHerculesService } from "@curefit/hercules-client"
import { BodyPart, Movement } from "@curefit/fitness-common"
import { ClassScheduleContainer, Tenant, WidgetView } from "@curefit/apps-common"
import GymfitClassScheduleViewBuilder from "./GymfitClassScheduleViewBuilder"
import { CacheHelper } from "../util/CacheHelper"
import { CareDatePickerView, PageWidget } from "../page/Page"
import GymfitCheckinViewBuilder from "./GymfitCheckinViewBuilder"
import { IGymfitBusiness } from "./GymfitBusiness"
import { ErrorFactory } from "@curefit/error-client"
import { ErrorCodes } from "../error/ErrorCodes"
import GymLocalitySelectorViewBuilder from "./GymLocalitySelectorViewBuilder"
import GymfitUtil from "../util/GymfitUtil"
import GymUniversalCheckinViewBuilder from "./GymUniversalCheckinViewBuilder"
import { CultSummary, PauseMembershipParams } from "@curefit/cult-common"
import GymfitCheckinConfirmationViewBuilder from "./GymfitCheckinConfirmationViewBuilder"
import { CareCenterPageView } from "../care/CareCenterViewBuilder"
import { ComplimentaryAccessMembership, CULT_CLIENT_TYPES, ICultService, ICultServiceOld } from "@curefit/cult-client"
import serviceInterfaces, { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { PromiseCache } from "../util/VMUtil"
import { AlertError } from "../common/errors/AlertError"
import {
    GapHandling,
    IMembershipService,
    MEMBERSHIP_CLIENT_TYPES,
    PauseRequest,
    UnpauseRequest
} from "@curefit/membership-client"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import {
    CENTER_SERVICE_CLIENT_TYPES,
    ICenterScheduleService,
    ICenterService,
    ILocalityService
} from "@curefit/center-service-client"
import {
    CenterResponse,
    CenterScheduleResponse,
    CenterSearchRequest,
    CenterStatus,
    CenterType,
    CenterVertical,
    SkuName,
    SortBy
} from "@curefit/center-service-common"
import { ISegmentationClient, SEGMENTATION_CLIENT_TYPES } from "@curefit/segmentation-service-client"
import { eternalPromise, TimeUtil } from "@curefit/util-common"
import { AttributeKeyType, Benefit, BenefitType, Membership, PauseInitiator } from "@curefit/membership-commons"
import GymPausePackViewBuilder from "../pack/GymPausePackViewBuilder"
import {
    Audit,
    PersonalTrainingRescheduleRequest,
    PtBookingSource,
    PtSession,
    PtSessionAccessType,
    PtSessionStatus,
    SessionSearchRequest,
    SortOrder,
    Source
} from "@curefit/personal-training-v2-common"
import { IdentityResponse } from "@curefit/identity-common"
import { GymFitPersonalTrainingSlotConfirmationView } from "../order/OrderConfirmationViewBuilder"
import { IDENTITY_CLIENT_TYPES, IIdentityService } from "@curefit/identity-client"
import { IPersonalTrainingService, PERSONAL_TRAINING_CLIENT_TYPES } from "@curefit/personal-training-v2-client"
import { Order } from "@curefit/order-common"
import { PaymentData, PrePaymentData } from "@curefit/payment-common"
import GymPtMembershipViewBuilder from "./GymPtMembershipViewBuilder"
import { Doctor } from "@curefit/albus-client"
import { IOllivanderCityService, OLLIVANDER_CLIENT_TYPES } from "@curefit/ollivander-node-client"
import { MultiDeviceHandlingMiddleware } from "../middleware/MultiDeviceHandlingMiddleware"
import { IOrderService, OMS_API_CLIENT_TYPES } from "@curefit/oms-api-client"
import CultUtil, { PILATES_CENTER_IDS } from "../util/CultUtil"
import { CenterBookingCache } from "../cult/CenterBookingCache"
import moment = require("moment")
import { HeadersUtil } from "../../util/HeadersUtil"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"
import WidgetBuilder from "../page/vm/WidgetBuilder"
import { GYM_PT_MUSCLE_BLAZE_BANNER_WIDGET_FIRST } from "../common/Constants"

const crypto = require("crypto")

export function gymfitControllerFactory(kernel: Container) {
    @controller("/gymfit",
        kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)

    class GymfitController {

        constructor(
            @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
            @inject(CATALOG_CLIENT_TYPES.CatalogServiceReaderPMS) private catalogueServicePMS: ICatalogueServicePMS,
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(CUREFIT_API_TYPES.CultBusiness) private cultBusiness: ICultBusiness,
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
            @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) private offerServiceV2: IOfferServiceV2,
            @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) private offerServiceV3: OfferServiceV3,
            @inject(CUREFIT_API_TYPES.GymfitCenterPageConfig) private gymfitCenterPageConfig: GymfitCenterPageConfig,
            @inject(CUREFIT_API_TYPES.GymsViewBuilder) private gymsViewBuilder: GymsViewBuilder,
            @inject(CUREFIT_API_TYPES.GymfitCheckinConfirmationPageConfig) private gymfitCheckinConfirmationPageConfig: GymfitCheckinConfirmationPageConfig,
            @inject(GYMFIT_CLIENT_TYPES.GymfitService) private gymfitService: IGymfitService,
            @inject(CUREFIT_API_TYPES.GymfitCenterDetailsWebViewBuilder) private gymfitCenterDetailsWebViewBuilder: GymfitCenterDetailsWebViewBuilder,
            @inject(CUREFIT_API_TYPES.GymfitCenterDetailsAppViewBuilder) private gymfitCenterDetailsAppViewBuilder: GymfitCenterDetailsAppViewBuilder,
            @inject(CUREFIT_API_TYPES.GymfitPackDetailViewBuilder) private gymfitPackDetailViewBuilder: GymfitPackDetailViewBuilder,
            @inject(HERCULES_CLIENT_TYPES.IHerculesService) private herculeService: IHerculesService,
            @inject(CUREFIT_API_TYPES.GymfitClassScheduleViewBuilder) private gymfitClassScheduleViewBuilder: GymfitClassScheduleViewBuilder,
            @inject(CUREFIT_API_TYPES.GymfitCheckinViewBuilder) private gymfitCheckinViewBuilder: GymfitCheckinViewBuilder,
            @inject(CUREFIT_API_TYPES.GymLocalitySelectorViewBuilder) private gymLocalitySelectorViewBuilder: GymLocalitySelectorViewBuilder,
            @inject(CUREFIT_API_TYPES.GymUniversalCheckinViewBuilder) private gymUniversalCheckinViewBuilder: GymUniversalCheckinViewBuilder,
            @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
            @inject(CUREFIT_API_TYPES.GymfitBusiness) private gymfitBusiness: IGymfitBusiness,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
            @inject(CUREFIT_API_TYPES.CacheHelper) protected cacheHelper: CacheHelper,
            @inject(CUREFIT_API_TYPES.GymfitCheckinConfirmationViewBuilder) private gymfitCheckinConfirmationViewBuilder: GymfitCheckinConfirmationViewBuilder,
            @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) public membershipService: IMembershipService,
            @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness,
            @inject(CULT_CLIENT_TYPES.ICultService) private cultService: ICultService,
            @inject(CENTER_SERVICE_CLIENT_TYPES.LocalityService) private localityServiceClient: ILocalityService,
            @inject(SEGMENTATION_CLIENT_TYPES.SegmentationClient) private segmentationClient: ISegmentationClient,
            @inject(CENTER_SERVICE_CLIENT_TYPES.CenterService) private centerService: ICenterService,
            @inject(CUREFIT_API_TYPES.GymPausePackViewBuilder) private gymPausePackViewBuilder: GymPausePackViewBuilder,
            @inject(PERSONAL_TRAINING_CLIENT_TYPES.PersonalTrainingService) private ptService: IPersonalTrainingService,
            @inject(IDENTITY_CLIENT_TYPES.IdentityService) protected identityService: IIdentityService,
            @inject(OMS_API_CLIENT_TYPES.OrderService) private omsApiClient: IOrderService,
            @inject(OLLIVANDER_CLIENT_TYPES.IOllivanderCityService) private ollivanderService: IOllivanderCityService,
            @inject(CENTER_SERVICE_CLIENT_TYPES.CenterScheduleService) private centerScheduleService: ICenterScheduleService,
            @inject(CUREFIT_API_TYPES.CenterBookingCache) public centerBookingCache: CenterBookingCache,
            @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultServiceOld,
            @inject(CUREFIT_API_TYPES.WidgetBuilder) public widgetBuilder: WidgetBuilder,
        ) {
        }

        /*
            This API is used to get center info details for OTHER GYMS
         */
        @httpGet("/center/:centerId")
        async getOtherGymsGymFitCenterInfo(req: express.Request): Promise<ProductDetailPage> {
            const centerId: number = req.params.centerId
            return this.baseThirdPartyGymfitCenterDetails(req, centerId, "GYMFIT_FITNESS_PACK")
        }

        @httpGet("/personalTrainer/bookSession")
        async bookPtSession(req: express.Request): Promise<GymFitPersonalTrainingSlotConfirmationView> {
            const centerId: number = req.query.centerId
            const service: string = "GYMFIT_PERSONAL_TRAINING"
            const trainerId: number = req.query.trainerId
            const startTime: number = req.query.startTime
            const endTime: number = req.query.endTime
            const userId: number = req.query.userId
            const productId: string = req.query.productId
            const userContext = req.userContext as UserContext

            const date: string = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, Number(startTime), "dddd, MMM DD • h:mm a")

            const doesNoShowCancellationPolicyV1Apply = await this.ptService.isNoShowCancellationPolicyV1ApplicableInCenter(centerId, userId, HeadersUtil.getCommonHeaders(userContext))

            let durationInDays
            if (doesNoShowCancellationPolicyV1Apply.doesApply) {
                const ptProduct: GymPtProduct = await this.catalogueService.getGymPtProductById(productId)

                if (ptProduct?.durationInDays) {
                    durationInDays = ptProduct?.durationInDays
                }
            }

            const searchRequest: SessionSearchRequest = {
                tenantId: 1,
                userIds: [userId.toString()],
                identityIds: [trainerId.toString()],
                centerIds: [centerId.toString()],
                startTimeTo: startTime,
                startTimeFrom: startTime,
                endTimeTo: endTime,
                endTimeFrom: endTime,
                statuses: [PtSessionStatus.CREATED],
                accessTypes: [PtSessionAccessType.MEMBERSHIP],
                createdOnSortOrder: SortOrder.DESC,
                pageNo: 0,
                pageSize: 1
            }
            const sessions: PtSession[] = await this.ptService.searchSessions(searchRequest, HeadersUtil.getCommonHeaders(userContext))
            if (sessions.length > 0) {
                const confirmAudit: Audit = {
                    agentHost: "curefit-api",
                    agentId: userId.toString(),
                    annotation: "Confirm Session",
                    source: Source.CUREFIT_API
                }
                const bookedSession = sessions[0]
                await this.ptService.confirmPtSession(sessions[0].id, confirmAudit, HeadersUtil.getCommonHeaders(userContext))
                const identityResponse: IdentityResponse = await this.identityService.getIdentityById("CUREFIT", trainerId)
                const centerResponse: CenterResponse = await this.centerService.getCenterById(centerId)

                let calendarEventAction
                const classCalendarPreference: PreferenceDetail = (
                  await eternalPromise(
                    this.cultBusiness.getClassCalendarPreference(
                      userContext,
                      userContext.userProfile.userId,
                      "FITNESS"
                    )
                  )
                ).obj
                if (
                  classCalendarPreference &&
                  classCalendarPreference.bookingEmailPreference
                ) {
                  try {
                    calendarEventAction =
                      [GymfitUtil.getCreateCalendarEventAction(
                        userContext,
                        bookedSession,
                        centerResponse,
                        this.hamletBusiness
                      )]
                  } catch (error) {
                    this.logger.error(
                      "Error creating calendar event action: ",
                      error
                    )
                  }
                }
                const banners = await this.widgetBuilder.buildWidgets([GYM_PT_MUSCLE_BLAZE_BANNER_WIDGET_FIRST], this.serviceInterfaces, userContext, undefined, undefined)
                let muscleBlazeBanner = null
                if (!_.isEmpty(banners)) {
                    muscleBlazeBanner = banners.widgets[0]
                }
                return new GymFitPersonalTrainingSlotConfirmationView( date, identityResponse, centerResponse, doesNoShowCancellationPolicyV1Apply.doesApply, durationInDays, userContext, null, calendarEventAction, muscleBlazeBanner)
            }
        }

        @httpPost("/personalTrainer/:sessionId/reschedule")
        async reschedulePtSession(req: express.Request): Promise<GymFitPersonalTrainingSlotConfirmationView> {
            const centerId: number = req.body.centerId
            const service: string = "GYMFIT_PERSONAL_TRAINING"
            const trainerId: number = req.body.trainerId
            const startTime: number = req.body.startTime
            const endTime: number = req.body.endTime
            const userId: number = req.body.userId
            const sessionId: number = req.params.sessionId
            const productId: string = req.body.productId
            const userContext = req.userContext as UserContext
            const rescheduleAudit: Audit = {
                agentHost: "curefit-api",
                agentId: userId.toString(),
                annotation: "Reschdule Session",
                source: Source.CUREFIT_API
            }
            const date: string = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, Number(startTime), "dddd, MMM DD • h:mm a")

            this.logger.info("Request Query Params: " + centerId + ", " + trainerId + ", " + startTime + ", " + endTime + ", " + userId + ", " + userContext.userProfile.timezone + ", " + date)

            const doesNoShowCancellationPolicyV1Apply = await this.ptService.isNoShowCancellationPolicyV1ApplicableInCenter(centerId, userId, HeadersUtil.getCommonHeaders(userContext))

            let durationInDays
            if (doesNoShowCancellationPolicyV1Apply.doesApply) {
                const ptProduct: GymPtProduct = await this.catalogueService.getGymPtProductById(productId)

                if (ptProduct?.durationInDays) {
                    durationInDays = ptProduct?.durationInDays
                }
            }

            const rescheduleRequest: PersonalTrainingRescheduleRequest = {
                "startTime": startTime,
                "endTime": endTime,
                "oldSessionId": sessionId,
                "sessionBookingSource": PtBookingSource.CUREFIT_APP,
                source: Source.CUREFIT_API,
                "audit": rescheduleAudit
            }

            const oldSessionRequest: SessionSearchRequest = {
                ids: [sessionId],
                tenantId: 1,
            }

            const oldSession: PtSession[] = await this.ptService.searchSessions(oldSessionRequest, HeadersUtil.getCommonHeaders(userContext))
            const session: PtSession = await this.ptService.rescheduleSession(rescheduleRequest, HeadersUtil.getCommonHeaders(userContext))
            this.logger.info("fetching trainer details for identity id : " + trainerId)
            const identityResponse: IdentityResponse = await this.identityService.getIdentityById("CUREFIT", trainerId)
            const centerResponse: CenterResponse = await this.centerService.getCenterById(centerId)

            const confirmAction: Action[] = []
                const classCalendarPreference: PreferenceDetail = (
                  await eternalPromise(
                    this.cultBusiness.getClassCalendarPreference(
                      userContext,
                      userContext.userProfile.userId,
                      "FITNESS"
                    )
                  )
                ).obj

            if (
                classCalendarPreference &&
                classCalendarPreference.bookingEmailPreference
              ) {
                try {
                  const newCalendarAction = GymfitUtil.getCreateCalendarEventAction(
                      userContext,
                      session,
                      centerResponse,
                      this.hamletBusiness
                    )
                    const removeOldSessionAction = oldSession.length > 0 && GymfitUtil.getDeleteCalendarEventAction(userContext, oldSession[0])
                    const removeOldSessionActionTwo = oldSession.length > 0 && GymfitUtil.getDeleteCalendarEventAction(userContext, {...oldSession[0], id: oldSession[0].startTime})

                    if (removeOldSessionAction) {
                        confirmAction.push(removeOldSessionAction)
                    }

                    if (removeOldSessionActionTwo) {
                        confirmAction.push(removeOldSessionActionTwo)
                    }

                    if (newCalendarAction) {
                        confirmAction.push(newCalendarAction)
                    }

                } catch (error) {
                  this.logger.error(
                    "Error creating calendar event action: ",
                    error
                  )
                }
              }

            return new GymFitPersonalTrainingSlotConfirmationView( date, identityResponse, centerResponse, doesNoShowCancellationPolicyV1Apply.doesApply, durationInDays, userContext, null, confirmAction)
        }

        @httpPost("/personalTrainer/:sessionId/cancel")
        async cancelSession(req: express.Request): Promise<any> {
            this.logger.info("agent-host deployment check, to be removed after testing")
            const sessionId: number = req.params.sessionId
            const userContext = req.userContext as UserContext
            const userId = userContext.userProfile.userId
            const audit: Audit = {
                agentHost: "curefit-api",
                agentId: userId,
                annotation: "Cancel Session",
                source: Source.CUREFIT_API
            }
            const searchRequest: SessionSearchRequest = {
                tenantId: 1,
                userIds: [userId],
                ids: [sessionId],
                statuses: [PtSessionStatus.BOOKED]
            }
            const sessions: PtSession[] = await this.ptService.searchSessions(searchRequest, HeadersUtil.getCommonHeaders(userContext))

            let completionAction
            try {
                completionAction = [GymfitUtil.getDeleteCalendarEventAction(userContext, sessions[0]), GymfitUtil.getDeleteCalendarEventAction(userContext, {...sessions[0], id: sessions[0].startTime})]
            } catch (error) {
                this.logger.error("Error in delete calendar event action: ", error)
            }

            if (!sessions?.length) {
                return {message: "Booking Cancelled", confirmAction: completionAction}
            }

            if (sessions[0].accessType === PtSessionAccessType.PPC) {
                const order: Order = await this.omsApiClient.getOrder(sessions[0].accessId)
                const paymentdata: PaymentData = order.payments.find((x) => x.status === "paid")
                const prePaymentData: PrePaymentData = order.prePayments.find((x) => x.status === "paid")
                const paymentAmount: number = paymentdata ? paymentdata.amount : 0
                const prePaymentAmount: number = prePaymentData ? prePaymentData.amount : 0
                const refundAmount: number = paymentAmount + prePaymentAmount

                this.logger.info("Attempting to cancel order at OMS", {orderId: order.orderId})
                const response = await this.omsApiClient.refundOrderOMS(order.orderId, undefined, "CUSTOMER_CANCELLED", false, undefined, undefined, userContext.userProfile.userId, undefined, undefined, undefined, undefined, {
                    requestSource: Source.CUREFIT_API,
                    agentId: userContext.userProfile.userId
                })
                this.logger.info("Cancel order at OMS results", {response})
                if (refundAmount === 0) {
                    return {message: "Booking Cancelled", confirmAction: completionAction}
                } else {
                    return {message: "Booking Cancelled, Refund Initated", confirmAction: completionAction}
                }
            } else {
                try {
                    await this.ptService.cancelSession(sessions[0].id, audit, HeadersUtil.getCommonHeaders(userContext))

                    return {message: "Booking Cancelled", confirmAction: completionAction}
                } catch (e) {
                    this.logger.error(`NO_SHOW pilot logs ${JSON.stringify(e)}`)
                    if (e?.statusCode == 400 && e?.context?.upstream?.responseBody?.message != null) {
                        return {message: e?.context?.upstream?.responseBody?.message}
                    }
                    throw e
                }
            }
        }

        /*
            This API is used to get centerInfo details for GYMFIT vertical
         */
        @httpGet("/centerInfo/:centerId")
        async getGymFitCenterInfo(req: express.Request): Promise<ProductDetailPage> {
            const centerId: number = req.params.centerId
            const userContext = req.userContext as UserContext
            const latitude: number = userContext.sessionInfo.lat
            const longitude: number = userContext.sessionInfo.lon
            const checkinId: number = req.query.checkinId
            const userId = userContext.userProfile.userId

            let gymfitCenterResponse: GymfitCenterResponse
            if (latitude && longitude) {
                gymfitCenterResponse = await this.gymfitService.getGymfitCenterById(centerId + "", userId, {latitude, longitude})
            } else {
                gymfitCenterResponse = await this.gymfitService.getGymfitCenterById(centerId + "", userId)
            }
            return this.gymfitCenterDetailsAppViewBuilder.getView(userContext, gymfitCenterResponse.center, checkinId)
        }


        @httpGet("/personalTrainer/membershipDetails")
        async getPtMembershipDetails(req: express.Request): Promise<{ widgets: (WidgetView)[], header: any, actions: any }> {
            const membershipId: number = req.query.membershipId
            const membership = await this.membershipService.getMembershipById(membershipId)
            const userContext = req.userContext as UserContext
            const trainerId = membership?.metadata?.preferredTrainerId
            const centerId = membership?.metadata?.preferredCenterId

            if (!trainerId || !centerId ) {
                return undefined
            }

            const identityResponse: IdentityResponse = await this.identityService.getIdentityById("CUREFIT", Number(trainerId))
            const centerResponse: CenterResponse = await this.centerService.getCenterById(Number(centerId))
            const agentDetails: Doctor[] = await this.ollivanderService.getDoctorDetailsByIdentityId(trainerId)
            return await new GymPtMembershipViewBuilder().buildView(this.serviceInterfaces, userContext, membership, centerResponse, agentDetails[0] , identityResponse.name, !PILATES_CENTER_IDS.includes(centerId))
        }


        @httpGet("/centers")
        async getConsultationCenters(req: express.Request): Promise<CareCenterPageView> {
            const productId = req.query.productId
            const userContext = req.userContext as UserContext
            const cityId = userContext.userProfile.cityId
            const userId = userContext.userProfile.userId
            const searchParams: GymFitCenterSearchFilters = { sortBy: "DISTANCE", cityId, userId }
            if (req.query.centerType) {
                searchParams["types"] = [req.query.centerType]
            }
            const gymCenters = await this.gymfitService.searchCenter(cityId, searchParams, userContext.userProfile.userId, true)
            return this.gymfitClassScheduleViewBuilder.getChangeCenterView(userContext, gymCenters, productId)
        }

        @httpGet("/packInfo")
        async getPackInfo(req: express.Request): Promise<ProductDetailPage> {
            const productId: string = req.query.productId
            const selectedStartDate: string = req.query.startDate
            const membershipId: string = req.query.membershipId
            const userContext: UserContext = req.userContext as UserContext
            if (_.isEmpty(membershipId)) {
                return this.gymfitPackDetailViewBuilder.getPrePurchaseView(userContext, productId, selectedStartDate)
            } else {
                return this.gymfitPackDetailViewBuilder.getPostPurchaseView(userContext, parseInt(membershipId))
            }
        }

        @httpGet("/changeStartDate/:membershipId")
        public async getPackChangeStartDateData(req: express.Request) {
            const session: Session = req.session
            const membershipId: number = parseInt(req.params.membershipId, 10)
            const productType: string = req.query.productType
            const userContext: UserContext = req.userContext as UserContext
            const tz = userContext.userProfile.timezone
            const membership = await this.membershipService.getMembershipById(membershipId)
            return this.gymfitPackDetailViewBuilder.getGymfitStartDateResponse(membership, userContext, tz, membershipId, productType)
        }

        @httpPost("/changeStartDate")
        public async setPackStartDate(req: express.Request) {
            const session: Session = req.session
            const userId: string = session.userId
            const userAgent: string = req.session.userAgent
            const membershipId: string = req.body.membershipId
            const newStartDate: string = req.body.newStartDate
            const productType: string = req.body.productType
            const userContext: UserContext = req.userContext as UserContext
            let response
            if (productType === "LUX_FITNESS_PRODUCT") {
                const data = await this.gymfitService.editLuxStartDate(membershipId, userId, {
                    startDate: newStartDate,
                    agent: "system",
                    agentType: "system",
                    comment: "Edit Lux start date"
                })
            } else if (productType === "GYMFIT_FITNESS_PRODUCT") {
                const membership = await this.membershipService.getMembershipById(parseInt(membershipId, 10))
                const gymfitMembershipId = membership.metadata["membershipId"]
                const data = await this.gymfitService.editStartDate(gymfitMembershipId, userId, {
                    startDate: newStartDate,
                    agent: "system",
                    agentType: "system",
                    comment: "Edit pro pack start date"
                })
            }
            response = {
                productType,
                message: `Pack start date has been updated`
            }
            return response
        }

        @httpGet("/checkin/details", kernel.get<MultiDeviceHandlingMiddleware>(CUREFIT_API_TYPES.MultiDeviceHandlingMiddleware).validateSession)
        async getCheckinInfo(req: express.Request): Promise<{widgets: (WidgetView | PageWidget)[], actions?: Action[], eventData?: any}> {
            const userContext: UserContext = req.userContext as UserContext
            const session: Session = req.session
            const userId = session.userId
            const deviceId = session.deviceId
            const centerId = req.query.centerId
            const eventId = req.query.eventId
            const checkinId = req.query.checkinId
            const isDetailed = req.query.isDetailed
            const checkInClassification = req.query.checkInClassification
            const latitude: number = userContext.sessionInfo.lat
            const longitude: number = userContext.sessionInfo.lon
            const user = await this.userCache.getUser(userId)
            let gymfitCenterResponse: GymfitCenterResponse
            if (latitude && longitude) {
                gymfitCenterResponse = await this.gymfitService.getGymfitCenterById(centerId + "", userId, {latitude, longitude})
            } else {
                gymfitCenterResponse = await this.gymfitService.getGymfitCenterById(centerId + "", userId)
            }
            if (isDetailed) {
                try {
                    const checkIn = await this.gymfitService.getGymfitCheckInById(userId, checkinId, {deviceId: deviceId})
                    return await this.gymfitCheckinViewBuilder.buildDetailedView(userContext, gymfitCenterResponse.center, checkIn)
                } catch (ex) {
                    this.logger.error(`Exception while generating transient qr code ${JSON.stringify(ex)}`)
                    if (ex.statusCode !== 412) {
                        throw ex
                    }
                    if (this.gymfitBusiness.checkQrError(ex)) {
                        throw this.gymfitBusiness.getQrError(ex)
                    }
                }
            }
            let gymfitEvent: GymfitEvent
            if (eventId) {
                gymfitEvent = await this.gymfitService.getEventById(eventId)
            }
            const result = AppUtil.getUserAlertInfo(user, userContext)
            if (!userContext.sessionInfo.isUserLoggedIn) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_LOGGED_IN_ERR, result.alertInfo.statusCode).build()
            }
            const code: string = result.code
            if (!_.isEmpty(code)) {
                throw this.errorFactory.withCode(result.code, result.alertInfo.statusCode).build()
            }
            const centerSchedule: GymFitCenterSchedule[] = await this.gymfitService.getCenterSchedule(centerId)
            const membershipDetails = await this.membershipService.getMembershipsForUser(userId, "curefit", ["GYMFIT_GA", "GYMFIT_GX"])
            return await this.gymfitCheckinViewBuilder.buildView(userContext, gymfitCenterResponse.center, membershipDetails, gymfitEvent, checkInClassification, centerSchedule)
        }

        @httpGet("/quickCheckin", kernel.get<MultiDeviceHandlingMiddleware>(CUREFIT_API_TYPES.MultiDeviceHandlingMiddleware).validateSession)
        async getQuickCheckinInfo(req: express.Request): Promise<{header: Header, widgets: (WidgetView | PageWidget)[], onLoadAlert?: AlertError}> {
            const userContext: UserContext = req.userContext
            const centerId = req.query.centerId
            const userId = req.session.userId
            const cityId = userContext.userProfile.cityId
            const user = await this.userCache.getUser(userId)
            const result = AppUtil.getUserAlertInfo(user, userContext)
            if (!userContext.sessionInfo.isUserLoggedIn) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_LOGGED_IN_ERR, result.alertInfo.statusCode).build()
            }
            const code: string = result.code
            if (!_.isEmpty(code)) {
                throw this.errorFactory.withCode(result.code, result.alertInfo.statusCode).build()
            }
            const gymfitCenter: GymfitCenter = (await this.gymfitService.getGymfitCenterById(centerId, userId)).center
            let trialDetails
            if ( gymfitCenter.type !== GymfitCenterType.LUX) {
                trialDetails = await this.gymfitService.getTrialUsage(userId, userContext.sessionInfo.deviceId)
            } else {
                trialDetails = await this.gymfitService.getTrialUsage(userId, userContext.sessionInfo.deviceId, GymfitCenterType.LUX.toString())
            }
            let transientQRCodeWithAccessDetails: TransientQRCodeWithAccessDetails
            let onLoadAlert
            const { membershipSummary: { current: { cult } } }: CultSummary = await this.cacheHelper.getCultSummary(userContext.userProfile.userId)
            try {
                const deviceId: string = req.session.deviceId
                this.logger.info("request", {userId, cityId, centerId: gymfitCenter.id})
                transientQRCodeWithAccessDetails = await this.gymfitService.getTransientQRCodeWithAccessDetails(userId, cityId, GymfitUtil.isParqEnabledForQrCheckin(userContext), deviceId, gymfitCenter.id)
            } catch (ex) {
                this.logger.error(`Exception while generating transient qr code ${JSON.stringify(ex)}`)
                if (ex.statusCode !== 412) {
                    throw ex
                }
                try {
                    if (this.gymfitBusiness.checkQrError(ex)) {
                        throw this.gymfitBusiness.getQrError(ex, gymfitCenter.type)
                    }
                    await this.cultBusiness.checkForParQ(ex, userContext, "gymfit")
                } catch (e) {
                    onLoadAlert = e
                }
            }
            this.logger.info("transientQRCodeWithAccessDetails", {transientQRCodeWithAccessDetails})
            userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
            const complimentaryMemberships: ComplimentaryAccessMembership[] = await userContext.userProfile.promiseMapCache.getPromise("cult-complimentary-access", { userId: userContext.userProfile.userId })
            const hasComplimentaryAccess = GymfitUtil.hasComplimentaryAccess(userContext, complimentaryMemberships)
            const selectMembership = await CultUtil.getActiveCultSelectMembership(userContext, this.membershipService)
            const selectMembershipV2 = await CultUtil.getActiveCultSelectMembershipV2(userContext, this.membershipService)
            const isLimitedCenterElitePack = !_.isEmpty(selectMembershipV2)
            const isCultSelectPack = !_.isEmpty(selectMembership)
            const limitedEliteMembership = await CultUtil.getActiveLimitedEliteMembership(userContext, this.membershipService)
            const isLimitedElitePack = !_.isEmpty(limitedEliteMembership)
            let sessionsLeftText
            if (isCultSelectPack && selectMembership.metadata.cityId == gymfitCenter.address.cityId) {
                const selectCenters: string[] = []
                selectMembership.attributes.forEach(a => {
                    if (a.attrKey == AttributeKeyType.ACCESS_CENTER) {
                        selectCenters.push(a.attrValue)
                    }
                })
                const isJpmcMembership = selectMembership?.metadata?.jpmcMembership ? true : false
                if (!selectCenters.includes(gymfitCenter.centerServiceId.toString())) {
                    const cultBenefit = selectMembership.benefits.find(benefit => benefit.name == "CENTER_AWAY")
                    if (cultBenefit) {
                        const ticketsRemaining = cultBenefit.maxTickets - cultBenefit.ticketsUsed
                        sessionsLeftText = ticketsRemaining <= 0 ? "No free other center sessions left this month!" : `${ticketsRemaining} of ${cultBenefit.maxTickets} free other center sessions left this month`
                        if (isJpmcMembership) {
                            sessionsLeftText = "Sessions not allowed at this center"
                        }
                    }
                }
                if (isJpmcMembership) {
                    if (await this.centerBookingCache.isDailyLimitBreachedOnCenter(gymfitCenter.centerServiceId, TimeUtil.todaysDate(userContext.userProfile.timezone, "YYYY-MM-DD"))) {
                        sessionsLeftText = "Your company has exhausted daily limit of allowed sessions"
                    }
                }
            }
            if (isLimitedCenterElitePack) {
                const selectCenters = JSON.parse(selectMembershipV2?.attributes?.find(attr => attr.attrKey === "accessCenterIds")?.attrValue ?? "[]")?.map(String)
                if (selectMembershipV2.metadata.cityId != gymfitCenter.address.cityId || !selectCenters.includes(gymfitCenter.centerServiceId.toString())) {
                    sessionsLeftText = "Sessions not allowed at this center"
                }
            }
            if (isLimitedElitePack) {
                sessionsLeftText = CultUtil.getLimitedEliteSessionLeftText(limitedEliteMembership, userContext)
            }
            const centerSchedule: GymFitCenterSchedule[] = await this.gymfitService.getCenterSchedule(centerId)
            return await this.gymfitCheckinViewBuilder.buildQuickCheckinView(userContext, gymfitCenter, trialDetails, cult, hasComplimentaryAccess, centerId, centerSchedule, transientQRCodeWithAccessDetails, onLoadAlert, isCultSelectPack, isLimitedElitePack, isLimitedCenterElitePack, sessionsLeftText)
        }

        @httpGet("/checkinQr", kernel.get<MultiDeviceHandlingMiddleware>(CUREFIT_API_TYPES.MultiDeviceHandlingMiddleware).validateSession)
        async getCheckinQRString(req: express.Request): Promise<{qrString: string, validityTimInSecs: number}> {
            const userContext: UserContext = req.userContext as UserContext
            const cityId = userContext.userProfile.cityId
            try {
                const deviceId = req.session.deviceId
                this.logger.info("gymfcmcheckin /checkinqr deviceId++++ " + deviceId + " /checkinqr userId++++" + userContext.userProfile.userId)
                const transientQRCode: TransientQRCodeResponse = await this.gymfitService.getTransientQRCodeByCityId(userContext.userProfile.userId, cityId, GymfitUtil.isParqEnabledForQrCheckin(userContext), deviceId)
                return { qrString: transientQRCode.qrString, validityTimInSecs: transientQRCode.timeOfExpiration / 1000 }
            } catch (ex) {
                this.logger.error(`Exception while generating checkin qr code ${JSON.stringify(ex)}`)
                if (this.gymfitBusiness.checkQrError(ex)) {
                    throw this.gymfitBusiness.getQrError(ex)
                }
                await this.cultBusiness.checkForParQ(ex, userContext, "gymfit")
            }
        }

        @httpPost("/checkout/:checkinId")
        async getCheckoutGym(req: express.Request): Promise<boolean> {
            const checkinId = req.params.checkinId
            const userContext: UserContext = req.userContext as UserContext
            return await this.gymfitService.checkOut(checkinId, userContext.userProfile.userId)
        }

        @httpGet("/checkin/confirmation")
        async checkin(req: express.Request): Promise<GymfitCheckinConfirmationView> {
            const userContext: UserContext = req.userContext as UserContext
            const session: Session = req.session
            const userId = session.userId
            const eventId = req.query.eventId
            const centerOfferingId = req.query.centerOfferingId
            const checkInClassification = req.query.checkInClassification
            const user = await this.userCache.getUser(userId)
            const result = AppUtil.getUserAlertInfo(user, userContext)
            if (!userContext.sessionInfo.isUserLoggedIn) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_LOGGED_IN_ERR, result.alertInfo.statusCode).build()
            }
            const code: string = result.code
            if (!_.isEmpty(code)) {
                throw this.errorFactory.withCode(result.code, result.alertInfo.statusCode).build()
            }
            const checkinRequest: CheckInCreationRequest = {
                userId,
                centerOfferingId,
                eventId,
                enablePARQCheck: !AppUtil.isWeb(userContext),
                checkInClassification,
                source: userContext.sessionInfo.userAgent as GymfitCheckInSource,
                deviceId: session.deviceId
            }
            try {
                const trialDetails = await this.gymfitService.getTrialUsage(userId, session.deviceId)
                if (session.sessionData.attributionSource == GymfitCheckInSource.FITTERNITY && trialDetails.used < trialDetails.maxCount) {
                    checkinRequest.source = GymfitCheckInSource.FITTERNITY
                }
                const checkin: GymfitCheckIn = await this.gymfitService.createCheckIn(checkinRequest)
                let centerId: string = ""
                let centerSchedule: GymFitCenterSchedule[]
                if (checkInClassification === GymfitCheckInClassification.SOFTBOOKING) {
                    centerId = checkin.centerOffering.centerId
                    centerSchedule = await this.gymfitService.getCenterSchedule(centerId)
                } else {
                    centerId = checkin.event.baseEvent.centerId.toString()
                }
                const center: GymfitCenter = (await this.gymfitService.getGymfitCenterById(centerId, userId)).center
                const whatToDo = this.gymfitBusiness.getGymCheckinWhatToDo(userContext.userProfile.timezone, checkin)
                const biometricWidget = this.gymfitBusiness.getBiometricWidget()
                let gymfitEvent: GymfitEvent
                if (eventId) {
                    gymfitEvent = await this.gymfitService.getEventById(eventId)
                }
                const { membershipSummary: { current: { cult } } }: CultSummary = await this.cacheHelper.getCultSummary(userContext.userProfile.userId)
                userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
                const complimentaryMemberships: ComplimentaryAccessMembership[] = await userContext.userProfile.promiseMapCache.getPromise("cult-complimentary-access", { userId: userContext.userProfile.userId })
                const hasComplimentaryAccess = GymfitUtil.hasComplimentaryAccess(userContext, complimentaryMemberships)
                return this.gymfitCheckinConfirmationViewBuilder.buildView(userContext, checkin, center, cult, hasComplimentaryAccess, whatToDo, gymfitEvent, biometricWidget, trialDetails, checkInClassification, centerSchedule)
            } catch (ex) {
                await this.cultBusiness.checkForParQ(ex, userContext, "gymfit")
            }
        }

        @httpPost("/checkin/:checkinId/cancel")
        async cancelCheckin(req: express.Request): Promise<any> {
            const userContext: UserContext = req.userContext as UserContext
            const session: Session = req.session
            const userId = session.userId
            const checkinId = req.params.checkinId
            const cancelCheckinParams: ICancelCheckInParams = { agent: userId, agentType: "USER", comment: "cancel_gym_checkin" }
            return await this.gymfitService.cancelCheckIn(checkinId, userId, cancelCheckinParams)
        }

        @httpGet("/search")
        async searchGymCenters(req: express.Request): Promise<SearchGymsResponse> {
            const userContext = req.userContext as UserContext
            const userId = userContext.userProfile.userId
            const session: Session = req.session
            const name = req.query.searchText || ""
            const sortBy = req.query.sortBy || "DISTANCE"
            const pageNumber = req.query.pageNumber
            const includeCultCenters = req.query.includeCultCenters || false
            const amenities: string[] = req.query.amenities ? req.query.amenities : []
            const localities: string[] = req.query.localities ? req.query.localities : []
            const categories: GymfitCenterCategory[] = req.query.categories ? req.query.categories : []
            const onlyOpen: boolean = req.query.onlyOpen === "true"
            const latitude = userContext.sessionInfo.lat
            const longitude = userContext.sessionInfo.lon
            const cityId = userContext.userProfile.cityId
            const enrichWithMapDistance = GymsViewBuilder.shouldShowMapDistance(userContext)
            const pageSize = pageNumber && this.gymfitCenterPageConfig.allGymsPageSize
            const cultSummary: CultSummary = await this.cacheHelper.getCultSummary(userContext.userProfile.userId)
            const complimentaryMemberships = await this.cultService.getComplimentaryMemberships({userId: userContext.userProfile.userId, appName: "CUREFIT_APP"})
            const currentTime = Date.now()
            const memberships = await this.membershipService.getMembershipsForUser(userId, "curefit", ["GYMFIT_GA", "GYMFIT_GX"], [], currentTime, currentTime)
            const goldTrialDetails = await this.gymfitService.getTrialUsage(userId, session.deviceId)
            const hasComplimentaryAccess = GymfitUtil.hasComplimentaryAccess(userContext, complimentaryMemberships)
            const types: GymfitCenterType[] = []
            // const centerTypes: SkuName[] = []
            // centerTypes.push(SkuName.GOLD)
            // types.push(GymfitCenterType.GOLD)
            // if (await GymfitUtil.showPremiumGyms(userContext, this.hamletBusiness, await GymfitUtil.hasAnyActiveMembership(userContext, memberships, cultSummary, hasComplimentaryAccess), this.segmentationClient)) {
            //     types.push(GymfitCenterType.BLACK)
            //     centerTypes.push(SkuName.BLACK)
            // }
            const searchParams: GymFitCenterSearchFilters = { sortBy, pageNumber, pageSize, name: name.trim(), amenities, localities, categories, types, onlyOpen, cityId, latitude, longitude, userId, enrichWithMapDistance }
            // const skuIds: number[] = centerTypes.includes(SkuName.BLACK) ? [1, 2] : [2]
            let gymsSearchParams: CenterSearchRequest = {
                searchStr: name.trim().length > 0 ? name.trim().length : undefined,
                offset: (pageNumber - 1) * pageSize,
                limit: pageSize,
                city: cityId,
                statuses: [CenterStatus.ACTIVE],
                latitude: latitude,
                longitude: longitude,
                enrichWithMapDistance: (latitude && longitude) ? enrichWithMapDistance : false,
                sortBy: (latitude && longitude) ? SortBy.DISTANCE : SortBy.RELEVANCE,
                type: includeCultCenters ? undefined : CenterType.GYM,
            }
            if (!_.isEmpty(localities)) {
                gymsSearchParams = {...gymsSearchParams, localities: localities}
            }
            if (!_.isEmpty(name.trim())) {
                gymsSearchParams = {...gymsSearchParams, searchStr: name.trim()}
            }
            this.logger.info(`Gyms search params= ${JSON.stringify(gymsSearchParams)}`)
            const gymCentersUnfiltered = await this.centerService.searchCenters(gymsSearchParams)
            const gymCenters = _.filter(gymCentersUnfiltered, (gymCenter) => (gymCenter.vertical === CenterVertical.GYMFIT || gymCenter.vertical === CenterVertical.CULT) && !(gymCenter.meta?.isVirtualCenter)) // filtering virtual centers
            this.logger.info(`Gyms response=${gymCenters.length}`)
            // if (name.trim().length > 0) {
            //     const nameSearch = name.trim()
            //     gymCenters.filter((item) => {
            //        return (nameSearch === item.name) || (item.name.length > nameSearch.length && item.name.substr(0, nameSearch.length) === nameSearch)
            //     })
            // }
            // if (localities.length > 0) {
            //     gymCenters.filter((item) => {
            //         return localities.find(locality  => locality === item.locality) != undefined
            //     })
            // }
            // if (onlyOpen) {
            //     gymCenters.filter((item) => {
            //         return item.status === CenterStatus.ACTIVE
            //     })
            // }
            // const gymFitAmenities = await this.gymfitService.getAllAmenities(cityId)
            // const gymfitCategories = await this.gymfitService.getAllCategories()
            // const gymFitLocalities: GymfitLocality[] = await this.gymfitService.getGymfitLocalitiesByCityId(cityId)

            const gymFitAmenities: GymfitAmenity[] = []
            const gymfitCategories: CenterCategory[] = []
            const gymFitLocalities: GymfitLocality[] = []
            const meta: any = req.query.scrollToGymId && !pageNumber ? { scrollToGymId: req.query.scrollToGymId } : {} // disable auto-scroll for pagination
            const hasNextPage = pageNumber && gymCentersUnfiltered.length == pageSize
            meta.hasNextPage = hasNextPage
            return await this.gymsViewBuilder.buildGymFitCard(userContext, gymCenters, gymFitAmenities, gymFitLocalities, gymfitCategories, amenities, localities, categories, onlyOpen, memberships, goldTrialDetails, cultSummary, meta)
        }

        @httpGet("/allMovements")
        async getAllMovements(req: express.Request): Promise<AllMovementsPage | WidgetView[]> {
            const bodyPartId: string = req.query.bodyPartId
            const onlyMovements: string = req.query.onlyMovements
            const allMovements: Movement[] = await this.herculeService.searchMovements(["GYMFIT"], bodyPartId ? [bodyPartId] : undefined)
            return this.buildAllMovements(req.userContext, allMovements, onlyMovements === "true")
        }

        @httpGet("/classSchedule")
        async getClassScheduleForCenter(req: express.Request): Promise<ClassScheduleContainer | CareDatePickerView> {
            const centerId = req.query.centerId
            const userContext = req.userContext as UserContext
            const userId = userContext.userProfile.userId
            const cityId = userContext.userProfile.cityId
            const isOnePassPage = req.query.vertical === "ONEPASS"
            const isOnePassGymOnly = req.query.isOnePassGymOnly === true || req.query.isOnePassGymOnly === "true"
            const gymSchedule: GymSchedule = await this.gymfitService.getClassScheduleForUser(userId, cityId, [centerId])
            const currentTimeInEpochInMillis = new Date().getTime() + 1800000
            gymSchedule.schedules = gymSchedule?.schedules?.filter(gymSchedule => gymSchedule.fromTimeUTC > currentTimeInEpochInMillis)
            const centerResponse: GymfitCenterResponse = await this.gymfitService.getGymfitCenterById(centerId)
            const gymScheduleV2: CenterScheduleResponse[] = await this.centerScheduleService.getCenterScheduleByCenterId(centerResponse.center.centerServiceId)
            const limitedEliteMembership = await CultUtil.getActiveLimitedEliteMembership(userContext, this.membershipService)
            const isLimitedElitePack = !_.isEmpty(limitedEliteMembership)
            if (userContext.sessionInfo.appVersion >= GymfitUtil.NEW_SLOT_BOOKING_VERSION) {
                return this.gymfitClassScheduleViewBuilder.getNewSlotBookingView(userContext, gymSchedule, centerResponse.center, gymScheduleV2, isOnePassPage, isOnePassGymOnly,
                    isLimitedElitePack, limitedEliteMembership)
            }
            return this.gymfitClassScheduleViewBuilder.getView(userContext, gymSchedule, centerResponse.center)
        }

        @httpPost("/preference")
        async updatePreference(req: express.Request): Promise<any> {
            const userContext = req.userContext as UserContext
            const locality = req.body.locality
            return this.gymfitBusiness.updateLocalityPreference(userContext, locality)
        }

        @httpGet("/localities")
        async getLocalities(req: express.Request): Promise<GymLocalitySelector> {
            const userContext = req.userContext as UserContext
            const cityId = userContext.userProfile.cityId
            const localities = await this.localityServiceClient.getAllLocalities([SkuName.GOLD, SkuName.BLACK], cityId, false)
            const currentLocalityPreference = await this.gymfitBusiness.getLocalityPreference(userContext)
            const defaultLocality = await GymfitUtil.getDefaultLocality(this.gymfitService, cityId)
            return this.gymLocalitySelectorViewBuilder.buildView(userContext, localities, currentLocalityPreference, defaultLocality)
        }

        @httpGet("/universalCheckin", kernel.get<MultiDeviceHandlingMiddleware>(CUREFIT_API_TYPES.MultiDeviceHandlingMiddleware).validateSession)
        async getUniversalCheckin(req: express.Request): Promise<{ header: Header, widgets: WidgetView[] }> {
            const userContext: UserContext = req.userContext
            const userId = req.session.userId
            const deviceId: string = req.session.deviceId
            const cityId = userContext.userProfile.cityId
            const user = await this.userCache.getUser(userId)
            const result = AppUtil.getUserAlertInfo(user, userContext)
            if (!userContext.sessionInfo.isUserLoggedIn) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_LOGGED_IN_ERR, result.alertInfo.statusCode).build()
            }
            const code: string = result.code
            if (!_.isEmpty(code)) {
                throw this.errorFactory.withCode(result.code, result.alertInfo.statusCode).build()
            }
            const selectMembership = await CultUtil.getActiveCultSelectMembership(userContext, this.membershipService)
            const isCultSelectPack = !_.isEmpty(selectMembership)
            let sessionsLeftText
            if (isCultSelectPack && selectMembership.metadata.cityId == userContext.userProfile.cityId) {
                const cultBenefit = selectMembership.benefits.find(benefit => benefit.name == "CENTER_AWAY")
                const ticketsRemaining = _.isNil(cultBenefit) ? 0 : cultBenefit.maxTickets - cultBenefit.ticketsUsed
                sessionsLeftText = ticketsRemaining <= 0 ? "No free other center sessions left this month!" : `${ticketsRemaining} of ${cultBenefit.maxTickets} free other center sessions left this month`
            }
            const limitedEliteMembership = await CultUtil.getActiveLimitedEliteMembership(userContext, this.membershipService)
            const isLimitedElitePack = !_.isEmpty(limitedEliteMembership)
            if (isLimitedElitePack) {
                sessionsLeftText = CultUtil.getLimitedEliteSessionLeftText(limitedEliteMembership, userContext)
            }
            try {
                this.logger.info("gymfcmcheckin /universalChecki deviceId++++ " + deviceId + " /universalChecki UserId++++ " + userId)
                const transientQRCode: TransientQRCodeResponse = await this.gymfitService.getTransientQRCodeByCityId(userContext.userProfile.userId, cityId, GymfitUtil.isParqEnabledForQrCheckin(userContext), deviceId)
                return this.gymUniversalCheckinViewBuilder.buildView(userContext, this.serviceInterfaces, transientQRCode, sessionsLeftText)
            } catch (ex) {
                this.logger.error(`Exception while generating universal checkin qr code ${JSON.stringify(ex)}`)
                if (this.gymfitBusiness.checkQrError(ex)) {
                    throw this.gymfitBusiness.getQrError(ex)
                }
                await this.cultBusiness.checkForParQ(ex, userContext, "gymfit")
            }
        }

        @httpPost("/optOut/createPlanPush")
        async optOutOfcreatePlanPushNotification(req: express.Request) {
          const userId = req.session.userId
          let logs =  "gymfcmcheckin /optOut/createPlanPush uid - " + userId
          try {
            await this.gymfitBusiness.optOutCreatePlanPushNotification(userId)
          } catch (err) {
              logs = logs + " error"
              this.logger.info(logs)
              return
          }
          this.logger.info(logs + " success")
        }

        @httpPost("/membership/v2/:membershipId/pause")
        async pauseGymFitMembership(req: express.Request): Promise<ProductDetailPage> {
            const params = req.body
            const userContext = req.userContext as UserContext
            const pauseMembershipParams: PauseMembershipParams = {
                pauseDurationDays: params.pauseDurationDays,
                pauseStartDate: params.pauseStartDate,
                agentID: userContext.userProfile.userId,
                comment: params.comment
            }
            this.logger.info("Gym membership pause params:" + pauseMembershipParams)
            return this.pauseGymMembership(req.params.membershipId, pauseMembershipParams, userContext, params.cancelAllActivities)
        }

        @httpPost("/membership/:membershipId/resume")
        async resumeGymfitMembership(req: express.Request): Promise<ProductDetailPage> {
            const session: Session = req.session
            const userContext = req.userContext as UserContext
            this.logger.info("Gym membership resume request:" + req.params.membershipId)
            return this.resumeGymMembership(req.params.membershipId, session.userId, userContext)
        }

        private async baseThirdPartyGymfitCenterDetails(req: express.Request, centerId: number, productType: ProductType): Promise<ProductDetailPage> {
            const session: Session = req.session
            const userId = session.userId
            const userContext: UserContext = req.userContext
            const deviceId: string = session.deviceId
            const selectedStartDate: string = req.query.startDate
            const selectedPlan: string = req.query.selectedPlan
            let gymfitCenter: GymfitCenter
            gymfitCenter = await this.catalogueService.getGymfitCenterById(centerId + "")

            let gymfitCenterProducts: OfflineFitnessPack[] = [] // All 3rd party packs are deprecated
            gymfitCenterProducts = _.sortBy(gymfitCenterProducts, "product.durationInDays")
            const user = await userContext.userPromise
            const packOffersV3 = await this.offerServiceV3.getGymFitProductPrices({
                productIds: gymfitCenterProducts.map(p => p.productId),
                userInfo: {
                    userId: userId,
                    deviceId: deviceId,
                    phone: user?.phone,
                    email: user?.email,
                    workEmail: user?.workEmail
                },
                cityId: userContext.userProfile.cityId,
                source: AppUtil.callSourceFromContext(userContext)
            })
            return this.gymfitCenterDetailsWebViewBuilder.getView(userContext, packOffersV3, gymfitCenter, gymfitCenterProducts, this.gymfitCenterPageConfig, selectedPlan, selectedStartDate)
        }

        private buildAllMovements(userContext: UserContext, allMovements: Movement[], onlyMovements: boolean): AllMovementsPage | WidgetView[] {
            const widgetViews: WidgetView[] = []
            const movementCards: MovementCard[] = allMovements.map((movement, index) => {
                const imageMedia = movement.media ? movement.media.find(media => media.type === "IMAGE") : null
                const videoMedia = movement.media ? movement.media.find(media => media.type === "VIDEO") : null
                return {
                    action: {
                        actionType: "NAVIGATION",
                        url: `curefit://videoplayer?videoUrl=${videoMedia ? videoMedia.url : null}`
                    },
                    title: movement.title,
                    description: movement.bodyParts.join(", "),
                    media: movement.media && movement.media.length ? movement.media[0] : null,
                    imageThumbnail: imageMedia ? AWS_S3_BASE_URL + imageMedia.url : null,
                    type: "SMALL",
                    showDivider: index < allMovements.length - 1,
                    bodyParts: movement.bodyParts
                }
            })
            if (onlyMovements) {
                const allMovementsCards: AllMovements = {
                    widgetType: "MOVEMENT_CARD",
                    type: "MOVEMENT_CARD",
                    title: "Movements",
                    movementCards,
                    emptyState: {
                        text: "No movements found",
                        icon: "/image/gymfit/no_gym.jpg"
                    }
                }
                widgetViews.push(allMovementsCards)
                return widgetViews
            }
            const existingMovements: {title: string, color: string, action: Action}[] = []
            const movementTypes: string[] = []
            const movementsFilter: MovementsFilter = {
                widgetType: "COLORED_INFO_WIDGET",
                type: "MOVEMENT_AREAS",
                title: "WORKOUT BY AREA",
                filters: existingMovements
            }
            const movementAreaColors = ["#ffc8c8", "#acd9fb", "#bade88", "#e8b47c"]
            allMovements.forEach((movement, index) => {
                if (movement.bodyPartObjects) {
                    movement.bodyPartObjects.forEach((bodyPart, partIndex) => {
                        if (!movementTypes.includes(bodyPart._id)) {
                            movementTypes.push(bodyPart._id)
                            existingMovements.push({
                                color: movementAreaColors[(index + partIndex) % 4],
                                title: bodyPart.title,
                                action: this.getMovementFilterAction(userContext, bodyPart)
                            })
                        }
                    })
                }
            })
            const allMovementsCards: AllMovements = {
                widgetType: "MOVEMENT_CARD",
                type: "MOVEMENT_CARD",
                title: "All Movements",
                movementCards,
                emptyState: {
                    text: "No movements found",
                    icon: "/image/gymfit/no_gym.jpg"
                }
            }
            widgetViews.push(movementsFilter)
            widgetViews.push(allMovementsCards)
            return {
                searchPlaceHolder: "Search movement",
                widgets: widgetViews
            }
        }

        private getMovementFilterAction(userContext: UserContext, bodyPart: BodyPart): Action {
            if (AppUtil.isWeb(userContext)) {
                return {
                    actionType: "FILTER_MOVEMENTS",
                    meta: {
                        id: bodyPart._id
                    }
                }
            } else {
                return {
                    actionType: "NAVIGATION",
                    url: `curefit://movementsbyarea?bodyPartId=${bodyPart._id}&bodyPart=${bodyPart.title}`
                }
            }
        }

        private async pauseGymMembership(membershipId: number, pauseParams: PauseMembershipParams, userContext: UserContext, cancelAllActivities: boolean = true): Promise<ProductDetailPage> {
            const membership = await this.membershipService.getMembershipById(membershipId)
            const userId = userContext.userProfile.userId

            if (!membership) {
                throw new Error("Membership not found")
            } else if (membership.userId !== userId) {
                throw new Error("Membership does not belong to the user")
            }

            const start = TimeUtil.getEpochInMillis(userContext.userProfile.timezone, pauseParams.pauseStartDate, "day", true)
            const endDate = TimeUtil.addDays(userContext.userProfile.timezone, pauseParams.pauseStartDate, pauseParams.pauseDurationDays - 1)
            // Adding 1000 so that 1 second is added in the end time and the pause end time is stored as 16:30:00 and not 16:29:59
            // This is because when unpause takes place, the difference between start and end is calculated which results in 1 extra second
            // deducted when calculating end when pausing and 1 extra second being added when membership is unpaused
            const end = TimeUtil.getEpochInMillis(userContext.userProfile.timezone, endDate, "day", false) + 1000
            const pauseRequest: PauseRequest = {
                idempotenceId: crypto.randomUUID(),
                membershipId: membershipId,
                start: start,
                end: end,
                initiator: PauseInitiator.CUSTOMER,
                reason: pauseParams.comment
            }
            if (cancelAllActivities) {
                const checkins: GymfitCheckIn[] = await this.gymfitService.getGymfitCheckinByDate(userContext.userProfile.userId, start, end)
                if (checkins) {
                    checkins.forEach((checkin) => {
                        if (checkin.state === "CREATED" && checkin.startTime > TimeUtil.getCurrentEpoch()) {
                            const cancelCheckInParams: ICancelCheckInParams = {
                                agent: "system",
                                agentType: "system",
                                comment: "Cancel for pause membership"
                            }
                            this.gymfitService.cancelCheckIn(checkin.id, userContext.userProfile.userId, cancelCheckInParams)
                        }
                    })
                }
                const cultEndDate = TimeUtil.addDays(userContext.userProfile.timezone, pauseParams.pauseStartDate, pauseParams.pauseDurationDays)
                await this.cultFitService.cancelUpcomingBookingsForMembershipInDateRangeV2(membershipId, userId, pauseParams.pauseStartDate, cultEndDate, process.env.APP_NAME)
            }
            const updatedMembership: Membership = await this.membershipService.createPause(pauseRequest, userContext.userProfile.userId, pauseParams.comment)
            return this.gymfitPackDetailViewBuilder.getPostPurchaseView(userContext, membershipId)
        }

        private async resumeGymMembership(membershipId: number, userId: string, userContext: UserContext): Promise<ProductDetailPage> {
            const unpauseRequest: UnpauseRequest = {
                membershipId: membershipId,
                gapHandling: GapHandling.FILL
            }
            this.logger.info("Gymfit membership unpause request:" + unpauseRequest)
            const membership: Membership = await this.membershipService.unpauseMembership(unpauseRequest, userId, "Unpause user request")
            return this.gymfitPackDetailViewBuilder.getPostPurchaseView(userContext, membershipId)
        }
    }

    return GymfitController
}

export default gymfitControllerFactory
