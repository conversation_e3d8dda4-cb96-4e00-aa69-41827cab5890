export enum CheckinNotificationState {
    CHECKIN_EVENT_RECEIVED = "CHECKIN_EVENT_RECEIVED",
    PART_OF_ALLOWED_SEGMENT = "PART_OF_ALLOWED_SEGMENT",
    DEVICE_ID_UNDEFINED = "DEVICE_ID_UNDEFINED",
    DEVICE_UNDEFINED = "DEVICE_UNDEFINED",
    OPTED_OUT = "OPTED_OUT",
    SUCCESSFULLY_SENT = "SUCCESSFULLY_SENT",
    ERROR_IN_SENDING_NOTIFICATION = "ERROR_IN_SENDING_NOTIFICATION",
    NOT_VALIDATED = "NOT_VALIDATED",
    CHECKIN_TIME_UNDEFINED = "CHECKIN_TIME_UNDEFINED",
    CHECKIN_TIME_LIMIT_CROSSED = "CHECKIN_TIME_LIMIT_CROSSED",
    CHECKIN_FIREBASE_ERROR = "CHECKIN_FIREBASE_ERROR",
    APP_VERSION_NOT_SUPPORTED = "APP_VERSION_NOT_SUPPORTED",
    PUSH_NOTIFICATION_TOKEN_UNDEFINED = "PUSH_NOTIFICATION_TOKEN_UNDEFINED",
    PILOT_GYM_DISABLE_CREATE_PLAN_NOTIFICATION = "PILOT_GYM_DISABLE_CREATE_PLAN_NOTIFICATION",
}
