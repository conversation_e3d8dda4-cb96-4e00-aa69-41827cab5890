import {
    G<PERSON>fitCenter,
    GymfitCenterCategory,
    GymFitCenterSchedule,
    GymfitCheckIn,
    GymfitCheckInClassification,
    GymfitEvent,
    GymfitStatus,
    TrialUsage
} from "@curefit/gymfit-common"
import { UserContext } from "@curefit/vm-models"
import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import SoftBookingBuilder from "./SoftBookingBuilder"
import { ActionCard, ProductListWidget, SinglesOrderConfirmationWidget, WidgetView } from "../common/views/WidgetView"
import { InfoBlockProps } from "@curefit/apps-common"
import GymfitUtil from "../util/GymfitUtil"
import { BannerCarouselWidget, GymfitCodeWidget } from "../page/PageWidgets"
import { TimeUtil } from "@curefit/util-common"
import GymfitCheckinConfirmationView from "./GymfitCheckinConfirmationView"
import AppUtil from "../util/AppUtil"
import { PageWidget } from "../page/Page"
import { MembershipDetails } from "@curefit/cult-common"

@injectable()
class GymfitCheckinConfirmationViewBuilder {

    constructor(
        @inject(CUREFIT_API_TYPES.SoftBookingBuilder) private softBookingBuilder: SoftBookingBuilder,
    ) {}

    async buildView(userContext: UserContext, gymfitCheckin: GymfitCheckIn, center: GymfitCenter, cultCurrentMembership: MembershipDetails, hasComplimentaryAccess: boolean, whatToDo: ProductListWidget, gymfitEvent: GymfitEvent, biometricWidget: InfoBlockProps, trialDetails: TrialUsage, checkInClassification: string, centerSchedule?: GymFitCenterSchedule[]): Promise<GymfitCheckinConfirmationView> {
        const confimationView = new GymfitCheckinConfirmationView()
        const widgets: (WidgetView | PageWidget)[] = []
        if (checkInClassification === GymfitCheckInClassification.SOFTBOOKING) {
            widgets.push(...await this.softBookingBuilder.buildView(userContext, center, gymfitCheckin, centerSchedule))
        } else {
            widgets.push(this.getGymConfirmationWidget(userContext, gymfitCheckin, center, gymfitEvent))
            if (trialDetails?.status === GymfitStatus.ACTIVE && !cultCurrentMembership && !hasComplimentaryAccess) {
                if (whatToDo) {
                    widgets.push(whatToDo)
                }
                // this.widgets.push(this.getGymfitCenterWidget(center))
                if (!AppUtil.isWeb(userContext)) {
                    widgets.push(this.getImportantUpdatesWidget(userContext, true))
                }
                // if (gymfitCheckin.user.metadata && !gymfitCheckin.user.metadata.isBiometricRegistered) {
                //     this.widgets.push(biometricWidget)
                // }
            }
        }
        confimationView.widgets = widgets
        return confimationView
    }

    private getGymConfirmationWidget(userContext: UserContext, gymfitCheckin: GymfitCheckIn, center: GymfitCenter, gymfitEvent: GymfitEvent) {
        const activityTitle = `${gymfitCheckin.event.baseEvent.activity.name} session`
        const orderCellWidget: SinglesOrderConfirmationWidget = {
            widgetType: "SINGLES_ORDER_CONFIRMATION_WIDGET",
            activityType: "CULT_CLASS",
            title: `${TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, new Date(gymfitCheckin.startTime), "D MMM, h:mm A")} - ${TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, new Date(gymfitCheckin.endTime), "h:mm A")}`,
            subTitle: gymfitEvent ? `${center.name}, ${center.locality}` : center.locality,
            activityName: (center.category === GymfitCenterCategory.ONEPASS) ? activityTitle : "Gym Workout",
        }
        return orderCellWidget
    }

    private getGymfitCodeWidget(userContext: UserContext, gymfitCheckin: GymfitCheckIn, center: GymfitCenter, gymfitEvent: GymfitEvent) {
        const gymfitCodeWidget: GymfitCodeWidget = {
            widgetType: "GYMFIT_CODE_WIDGET",
            title: gymfitEvent ? gymfitEvent.baseEvent.activity.name : center.name,
            titleIcon: "",
            subTitle: gymfitEvent ? `${center.name}, ${center.locality}` : center.locality,
            iconType: "TICK_GREEN",
            description: `${TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, new Date(gymfitCheckin.startTime), "D MMM, h:mm A")} - ${TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, new Date(gymfitCheckin.endTime), "h:mm A")}`,
        }
        return gymfitCodeWidget
    }

    private getGymfitCenterWidget(center: GymfitCenter) {
        const address = center.address
        const city = address.city ? address.city.name : address.cityId
        const state = address.city && address.city.state ? address.city.state : ""
        const country = address.city ? (address.city.country ? address.city.country.name : address.city.countryId) : ""
        const addressLine = (address.addressLine1 ? address.addressLine1 + ", " : "") + (address.addressLine2 ? address.addressLine2 : "")
        const addressText = `${addressLine} (${city}, ${state} ${address.pincode}, ${country})`
        const items: ActionCard[] = []
        items.push({
            subTitle: addressText,
            icon: "/image/icons/gymfit/location.png",
            cardAction: {
                actionType: "OPEN_MAP",
                title: "NAVIGATE",
                url: center.address.mapUrl
            }
        })

        if (center.amenities) {
            let amenities: string = ""
            center.amenities.forEach(amenity => {
                amenities = amenities.concat(amenity.name + " | ")
            })
            const indexOf = amenities.lastIndexOf("|")
            items.push({
                subTitle: "Center facilities: \n" + amenities.substring(0, indexOf),
                icon: "/image/icons/gymfit/facilities.png"
            })
        }

        return new ProductListWidget("SMALL", {title: "ABOUT YOUR CENTRE"}, items, undefined, undefined, undefined, false)
    }

    private getImportantUpdatesWidget(userContext: UserContext, isModal?: boolean): BannerCarouselWidget {
        return GymfitUtil.getImportantUpdatesWidget(userContext, null, null, isModal)
    }
}

export default GymfitCheckinConfirmationViewBuilder
