import { UserContext } from "@curefit/userinfo-common"
import {
    Action,
    CenterAddressWidget, CenterInfoWidget, CenterSchedule, CenterSummaryWidget, GymAccessInfoWidget,
    Header,
    ProductDetailPage,
    WidgetView,
    MediaData,
    GymMedia,
    ProductListWidget, ProductGridWidget, InfoCard, StructuredSchemaVideoWidget
} from "../common/views/WidgetView"
import {
    GymfitCenter,
    GymfitCenterCategory,
    GymfitCenterEquipment,
    GymfitCenterType,
    GymfitCheckInState,
    MediaType,
    TrialUsage,
    UserMembershipsAndTrialUsages
} from "@curefit/gymfit-common"
import * as _ from "lodash"
import { inject, injectable } from "inversify"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { GYMFIT_CLIENT_TYPES, IGymfitService } from "@curefit/gymfit-client"
import { ImageInfoCard, ImageInfoWidget } from "../page/PageWidgets"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CFAPIGymfitCheckinActionParams, IGymfitBusiness } from "./GymfitBusiness"
import IssueBusiness from "../crm/IssueBusiness"
import { HERCULES_CLIENT_TYPES, IHerculesService } from "@curefit/hercules-client"
import { Movement } from "@curefit/fitness-common"
import AppUtil, { AWS_S3_BASE_URL, SUPPORT_DEEP_LINK } from "../util/AppUtil"
import GymfitUtil, { ELITE_CENTER_TAG, GYM_CLOSED_TITLE, PRO_CENTER_TAG } from "../util/GymfitUtil"
import { CultSummary } from "@curefit/cult-common"
import { CULT_CLIENT_TYPES, ICultService, ICultServiceOld } from "@curefit/cult-client"
import CFActionUtil from "../util/ActionUtil"
import {
    CardDescription,
    HorizontalCardListingCardItem
} from "@curefit/vm-models/dist/src/models/widgets/HorizontalCardListingWidget"
import { CultPassImageTextCarousel, HorizontalCardListingWidget, ISegmentService } from "@curefit/vm-models"
import { HamletBusiness, HAMLET_TYPES } from "@curefit/hamlet-node-sdk"
import { CultPassImageTextCarouselView } from "../page/vm/widgets/cultPass/CultPassImageTextCarouselView"
import { ActionUtilV1, SeoUrlParams } from "@curefit/base-utils"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import { BASE_TYPES, Logger } from "@curefit/base"
import moment = require("moment")
import { Membership } from "@curefit/membership-commons"
import { ISegmentationClient, SEGMENTATION_CLIENT_TYPES } from "@curefit/segmentation-service-client"
import { GymfitCenterStatus } from "@curefit/gymfit-common/dist/src/Models"
import CreditsView from "../common/views/CreditsViewWidget"

@injectable()
export default class GymfitCenterDetailsAppViewBuilder {
    constructor(
        @inject(GYMFIT_CLIENT_TYPES.GymfitService) private gymfitService: IGymfitService,
        @inject(CUREFIT_API_TYPES.GymfitBusiness) private gymfitBusiness: IGymfitBusiness,
        @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness,
        @inject(HERCULES_CLIENT_TYPES.IHerculesService) private herculeService: IHerculesService,
        @inject(CULT_CLIENT_TYPES.ICultService) private cultService: ICultService,
        @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultServiceOld,
        @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness,
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) public membershipService: IMembershipService,
        @inject(BASE_TYPES.ILogger) protected logger: Logger,
        @inject(SEGMENTATION_CLIENT_TYPES.SegmentationClient) private segmentationClient: ISegmentationClient,
        @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService,
    ) {
    }

    async getView(userContext: UserContext, gymfitCenter: GymfitCenter, checkinId: number): Promise<ProductDetailPage> {
        const productDetailPage: ProductDetailPage = new ProductDetailPage()
        const userId = userContext.userProfile.userId
        const currentTime = Date.now()
        const memberships = await this.membershipService.getMembershipsForUser(userId, "curefit", ["GYMFIT_GA", "GYMFIT_GX"], [], currentTime, currentTime)
        const goldTrialDetails = await this.gymfitService.getTrialUsage(userId, userContext.sessionInfo.deviceId)
        const complimentaryMemberships = await this.cultService.getComplimentaryMemberships({userId, appName: "CUREFIT_APP"})
        const hasComplimentaryAccess = GymfitUtil.hasComplimentaryAccess(userContext, complimentaryMemberships)
        const params = { isSoftbookingEnabled: await AppUtil.isGymSofbookingEnabled(userContext, this.hamletBusiness), forGymDetailsPage: true }
        const cityId = userContext.userProfile.cityId
            const creditCenterDetails = [{
                accessCenterId: gymfitCenter.centerServiceId,
                centerSku: gymfitCenter.type === GymfitCenterType.BLACK ? 1 : gymfitCenter.type === GymfitCenterType.GOLD ? 2 : 0
            }]
            const creditsForCenters = await this.cultFitService.getCenterCredits({userId, appName: "CUREFIT_APP", cityId, centerDetails: creditCenterDetails})
        this.logger.info("goldMembershipDetails", JSON.stringify(memberships))

        productDetailPage.widgets.push(await this.getSummaryWidget(userContext, gymfitCenter, checkinId, creditsForCenters.response))

        const holidayWidget = this.gymfitBusiness.getNextGymHolidayWidget(userContext, gymfitCenter)
        if (holidayWidget) {
            productDetailPage.widgets.push(holidayWidget)
        }
        productDetailPage.widgets.push(this.getGymAccessInfoWidget(gymfitCenter))
        productDetailPage.widgets.push(this.getAddressWidget(gymfitCenter))
        productDetailPage.widgets.push(GymfitUtil.getImportantUpdatesWidget(
            userContext,
            { containerStyle: { marginLeft: 15 } },
            null,
            null,
            { color: "#222222", fontSize: 18 }
        ))
        if (gymfitCenter.amenities) {
            productDetailPage.widgets.push(this.getAmenitiesWidget(userContext, gymfitCenter))
        }
        // const centerEquipments = await this.getCenterWorkouts(userContext, gymfitCenter.equipments)
        // if (centerEquipments) {
        //     productDetailPage.widgets.push(centerEquipments)
        // }
        const nearbyGymWidget = await this.getNearbyGymsWidget(userContext, gymfitCenter, memberships, goldTrialDetails, hasComplimentaryAccess, params)
        if (nearbyGymWidget) {
            productDetailPage.widgets.push(nearbyGymWidget)
        }
        productDetailPage.actions.push(await this.getAction(userContext, gymfitCenter, memberships, goldTrialDetails, hasComplimentaryAccess, params))
        productDetailPage.meta = {
            city: gymfitCenter?.address?.city.name,
            gymName: gymfitCenter.name,
            isCenterOperational: this.isCenterOperational(gymfitCenter)
        }
        productDetailPage.widgets.push(this.getSeoData(gymfitCenter))
        return productDetailPage
    }

    private async getAction(userContext: UserContext, gymfitCenter: GymfitCenter, membershipDetails: Membership[], goldTrialDetails: TrialUsage, hasComplimentaryAccess: boolean, params?: CFAPIGymfitCheckinActionParams): Promise<Action> {
        return GymfitUtil.addTitleInActionQuery(await this.gymfitBusiness.getCheckinCTAForUser(userContext, gymfitCenter, membershipDetails, goldTrialDetails, hasComplimentaryAccess, params))
    }

    private async getSummaryWidget(userContext: UserContext, gymfitCenter: GymfitCenter, checkinId: number, creditsData: any): Promise<WidgetView> {
        const mediaData: MediaData[] = []
        const isWeb = AppUtil.isWeb(userContext)
        const images: GymMedia[] = gymfitCenter.media ? gymfitCenter.media.heroMedia.map((media) => {
            if (media.type === MediaType.VIDEO) {
                const mediaUrl = `https://cdn-media.cure.fit${media.mediaUrl}`
                mediaData.push({
                    media: {...media, mediaUrl  },
                    action: { actionType: "PLAY_VIDEO", url: `curefit://videoplayer?absoluteVideoUrl=${encodeURIComponent(mediaUrl)}`},
                    fullScreenIcon: "/image/gymfit/fullscreen.png",
                })
                return {...media, mediaUrl: `https://cdn-media.cure.fit${media.mediaUrl}`, assetUrl: media.thumbnailUrl}
            }
            mediaData.push({media: {...media}})
            return { ...media, assetUrl: media.mediaUrl }
        }) : []
        const centerSchedule = await this.gymfitService.getCenterSchedule(gymfitCenter.id)
        const schedules = GymfitUtil.getScheduleTimes(centerSchedule, isWeb ? ", " : "\n")
        const todaysSchedule = GymfitUtil.getTodaysSchedule(schedules)
        const mergedSchedule = GymfitUtil.mergeSameDaySchedule(schedules)
        let nextOpenSchedule = Object.assign({}, todaysSchedule)
        if (!nextOpenSchedule?.value) {
            nextOpenSchedule = schedules.find(schedule => schedule.value && schedule.value !== GYM_CLOSED_TITLE)
        }

        const actions: Action[] = []
        if (checkinId) {
            const checkIn = await this.gymfitService.getCheckinById(checkinId, userContext.userProfile.userId)
            if (checkIn.state === GymfitCheckInState.CREATED && checkIn.endTime > TimeUtil.getDateNow(userContext.userProfile.timezone).getTime()) {
                actions.push({
                    actionType: "CANCEL_GYM_CHECKIN",
                    meta: {
                        checkinId: checkIn.id
                    },
                    title: "Cancel"
                })
            }
            const issues = await this.issueBusiness.getGymfitFitnessCheckinIssues(checkinId,
                this.gymfitBusiness.getGymCheckInStatus(userContext.userProfile.timezone, checkIn), userContext)
            actions.push({
                actionType: "REPORT_ISSUE",
                actionId: "REPORT_ISSUE",
                title: "Need Help",
                url: SUPPORT_DEEP_LINK,
                meta: { issues }
            })
        }
        const isGymOpen = GymfitUtil.isGymOpen(todaysSchedule, gymfitCenter)
        const tagIcon = gymfitCenter.type === "BLACK" ? ELITE_CENTER_TAG : PRO_CENTER_TAG
        const centerCredits = creditsData[String(gymfitCenter.centerServiceId)]?.centerCreditCost
        const summaryWidget: CenterSummaryWidget = {
            widgetType: "GYM_PACK_SUMMARY",
            title: gymfitCenter.name,
            subTitle: {
                text: isGymOpen ? "OPEN NOW" : "CLOSED NOW",
                color: isGymOpen ? this.getOpenColor(userContext) : this.getClosedColor(userContext)
            },
            mediaData,
            distanceFromSearchOrigin: GymfitUtil.getFormattedGymDistance(gymfitCenter.distanceFromSearchOrigin),
            images: images,
            widgets: [],
            centerId: gymfitCenter.id + "",
            tagIcon: tagIcon,
            schedule: GymfitUtil.getScheduleTimes(centerSchedule, "\n"),
            weeksSchedule: mergedSchedule,
            todaysSchedule: nextOpenSchedule?.value?.replace("\n", " &\n") || "Closed",
            subType: "GYM_FIT_PRODUCT",
            creditsData: centerCredits ? new CreditsView(centerCredits, `${centerCredits > 1 ? "credits" : "credit"} required for one session`) : null
        }
        if (actions.length) {
            summaryWidget.data = {
                moreActions: {
                    icon: "MANAGE",
                    actionType: "ACTION_LIST",
                    actions: actions
                }
            }
        }
        return summaryWidget
    }

    private getGymAccessInfoWidget(gymfitCenter: GymfitCenter): GymAccessInfoWidget {
        const gymfitCenterType = gymfitCenter.type
        const gymAccessInfoItems = gymfitCenterType !== undefined && gymfitCenterType == "BLACK" ? [
            {
                title: "Unlimited sessions with cultpass",
                icons: ["/image/icons/cult/elite_access_info_1.png"],
                highlightTextInGold: "ELITE",
                highlightType: "gold",
            },
            {
                title: "2 sessions per month with cultpass",
                icons: ["/image/icons/cult/pro_access_info_1.png"],
                highlightTextInBlack: "PRO",
                highlightType: "black",
            }
        ] : [
            {
              title: "Unlimited sessions with cultpass",
              icons: [
                "/image/icons/cult/elite_access_info_1.png",
                "/image/icons/cult/pro_access_info_1.png"
              ],
                highlightTextInGold: "ELITE",
                highlightTextInBlack: "PRO",
              notHighlightText: "&",
              highlightType: "both",
            },
        ]

        const gymAccessInfoWidget: GymAccessInfoWidget = {
            widgetType: "CP_GYM_ACCESS_INFO_WIDGET",
            header: "Access",
            items: gymAccessInfoItems,
        }

        return gymAccessInfoWidget
    }

    private getDisplayNameFromCategory(category: GymfitCenterCategory) {
        // Temp till backend gives display text along with category
        switch (category) {
            case GymfitCenterCategory.GA:
                return "Gym access"
            case GymfitCenterCategory.GX:
                return "Group classes"
            default:
                return category
        }
    }

    private getAddressWidget(gymfitCenter: GymfitCenter): CenterAddressWidget {
        const address = gymfitCenter.address
        const city = address.city ? address.city.name : address.cityId
        const state = address.city && address.city.state ? address.city.state : ""
        const country = address.city ? (address.city.country ? address.city.country.name : address.city.countryId) : ""
        const addressLine = (address.addressLine1 ? address.addressLine1 + ", " : "") + (address.addressLine2 ? address.addressLine2 : "")
        const addressText = `${addressLine} (${city}, ${state} ${address.pincode}, ${country})`
        const addressWidget: CenterAddressWidget = {
            widgetType: "CENTER_MAP_WIDGET",
            header: {
                title: "Address",
            },
            mapUrl: gymfitCenter.address.mapUrl,
            latLong: {
                lat: gymfitCenter.address.latitude,
                long: gymfitCenter.address.longitude
            },
            addressText: addressText,
            footer: "NAVIGATE",
        }
        return addressWidget
    }

    private getAmenitiesWidget(userContext: UserContext, gymfitCenter: GymfitCenter): ProductGridWidget | ProductListWidget {
        if (AppUtil.isWeb(userContext)) {
            const header: Header = {
                title: "Amenities"
            }
            const amenities: InfoCard[] = []
            gymfitCenter.amenities.forEach(amenity => {
                amenities.push({
                    title: amenity.name,
                    image: amenity.imageUrls.clpImageUrl
                })
            })
            return new ProductGridWidget("ICON", header, amenities)
        }

        return {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "DYNAMIC_ICON",
            hideSepratorLines: true,
            header: {
                title: "Amenities",
                style: {
                    fontSize: 18,
                    fontFamily: "BrandonText-Bold",
                    color: "#212223",
                    paddingBottom: 10,
                }
            },
            style: {
                paddingVertical: 22,
            },
            items: gymfitCenter.amenities.map((amenity, index) => ({
                title: amenity.name,
                icon: amenity.imageUrls.clpImageUrl,
                rowStyle: {
                    flexDirection: "row-reverse",
                    justifyContent: "space-between",
                    alignItems: "center",
                    paddingTop: index ? 10 : 0,
                },
                rowContainer: {
                    marginLeft: 0,
                },
                titleStyle: {
                    color: "#333747",
                    fontSize: 16,
                    fontFamily: "BrandonText-Medium",
                    fontWeight: "500",
                    textAlign: "center",
                },
                iconSize: 35
            })),
        }
    }

    private async getCenterWorkouts(userContext: UserContext, equipments: GymfitCenterEquipment[]): Promise<ImageInfoWidget> {
        if (!equipments) return null

        const equipmentIds: string[] = equipments.map((equipment) => equipment.id)
        const allMovements: Movement[] = await this.herculeService.searchMovements(["GYMFIT"], null, equipmentIds, true)
        if (!allMovements) return null
        const cards: ImageInfoCard[] = allMovements.map((movement) => {
            const imageMedia = movement.media ? movement.media.find(media => media.type === "IMAGE") : null
            const videoMedia = movement.media ? movement.media.find(media => media.type === "VIDEO") : null
            return {
                title: movement.title,
                imageUrl: imageMedia ? AWS_S3_BASE_URL + imageMedia.url : "",
                action: {
                    actionType: "NAVIGATION",
                    url: `curefit://videoplayer?videoUrl=${videoMedia ? videoMedia.url : null}`
                }
            }
        })
        const imageInfoWidget: ImageInfoWidget = {
            widgetType: "IMAGE_INFO_WIDGET",
            productType: "GYMFIT_WORKOUT",
            header: {title: "Workouts Available"},
            cardType: AppUtil.isWeb(userContext) ? "MEDIUM" : "LARGE",
            cards,
        }
        return imageInfoWidget
    }

    private async getNearbyGymsWidget(userContext: UserContext, gymfitCenter: GymfitCenter, membershipDetails: Membership[], goldTrialDetails: TrialUsage, hasComplimentaryAccess: boolean, params?: CFAPIGymfitCheckinActionParams): Promise<WidgetView> {
        const cityId = userContext.userProfile.cityId
        const userId = userContext.userProfile.userId
        // const searchParams: GymFitCenterSearchFilters = {
        //     sortBy: "DISTANCE",
        //     latitude: gymfitCenter.address ? gymfitCenter.address.latitude : userContext.sessionInfo.lat,
        //     longitude: gymfitCenter.address ? gymfitCenter.address.longitude : userContext.sessionInfo.lon,
        //     pageNumber: 1,
        //     pageSize: 5,
        //     cityId,
        //     userId
        // }
        let gymfitCenters: GymfitCenter[] = await this.gymfitService.getGymfitCentersByLocalitySortedByDistance({
            lat: userContext.sessionInfo.lat,
            long: userContext.sessionInfo.lon,
            cityId,
            localityNames: [gymfitCenter.locality],
            userId: userContext.userProfile.userId
        })
        gymfitCenters = await GymfitUtil.getCenterListForUser(gymfitCenters, userContext, this.hamletBusiness, await GymfitUtil.hasAnyActiveMembership(userContext, membershipDetails, hasComplimentaryAccess, this.membershipService), this.segmentationClient, this.segmentService)
        // const gymfitCenters: GymfitCenter[] = await this.gymfitService.searchCenter(cityId, searchParams, userId, true)
        if (_.isEmpty(gymfitCenters)) {
            return undefined
        }

        if (AppUtil.isWeb(userContext) || userContext.sessionInfo.appVersion < 8.60) {
            const cards: ImageInfoCard[] = []
            for (const center of gymfitCenters.splice(0, 5)) {
                if (center.id === gymfitCenter.id) {
                    continue
                }
                const gymDistance = GymfitUtil.getFormattedGymDistance(center.distanceFromSearchOrigin)
                // const logoMedia = center.media && center.media.clpMedia ? center.media.clpMedia.find(media => media.type === MediaType.IMAGE) : undefined
                const imageMedia = center.media && center.media.heroMedia ? center.media.heroMedia.find(media => media.type === MediaType.IMAGE) : undefined
                const seoUrlParam: SeoUrlParams = GymfitUtil.getCenterSeoUrlParam(center)
                const card: ImageInfoCard = {
                    title: center.name,
                    subTitle: center.locality,
                    imageUrl: imageMedia ? imageMedia.mediaUrl : undefined,
                    action: {actionType: "NAVIGATION", url: AppUtil.isWeb(userContext) ? ActionUtilV1.getGymCenterWebUrl(seoUrlParam, center.id) : `curefit://gymfitcenter?centerId=${center.id}`},
                    rightInfo: {
                        title: gymDistance
                    }
                }
                cards.push(card)
            }
            if (_.isEmpty(cards)) {
                return undefined
            }
            const imageInfoWidget: ImageInfoWidget = {
                widgetType: "IMAGE_INFO_WIDGET",
                productType: "GYMFIT_NEARBY_GYMS",
                header: {
                    title: "Nearby Gyms",
                    titleProps: {
                        style: {
                            fontSize: 18
                        }
                    }
                },
                cardType: AppUtil.isWeb(userContext) ? "MEDIUM" : "LARGE",
                cards : cards,
            }
            return imageInfoWidget
        }

        const cardItems: HorizontalCardListingCardItem[] = []
        for (const center of gymfitCenters.splice(0, 5)) {
            if (center.id === gymfitCenter.id) {
                continue
            }
            const gymDistance = GymfitUtil.getFormattedGymDistance(center.distanceFromSearchOrigin)
            // const logoMedia = center.media && center.media.clpMedia ? center.media.clpMedia.find(media => media.type === MediaType.IMAGE) : undefined
            const imageMedia = center.media && center.media.heroMedia ? center.media.heroMedia.find(media => media.type === MediaType.IMAGE) : undefined
            const videoMedia = center.media &&  center.media.heroMedia ? center.media.heroMedia.find((media) => media.type === MediaType.VIDEO) : undefined
            const action = GymfitUtil.addTitleInActionQuery(await this.gymfitBusiness.getCheckinCTAForUser(userContext, gymfitCenter, membershipDetails, goldTrialDetails, hasComplimentaryAccess, params))
            if (action.url) {
                action.url = CFActionUtil.addPageParamsToUrl(action.url, GymfitUtil.GYM_PAGE_PARAMS)
            }
            const description: CardDescription[] = GymfitUtil.getCenterDescription(center.type)
            const seoUrlParam: SeoUrlParams = GymfitUtil.getCenterSeoUrlParam(center)
            const card: HorizontalCardListingCardItem = {
                cardType: "GYM_INFO",
                title: center.name,
                subTitle: center.locality,
                rightText: gymDistance,
                imageUrl: imageMedia ? imageMedia.mediaUrl : undefined,
                videoUrl: videoMedia ? `https://cdn-media.cure.fit${videoMedia.mediaUrl}` : undefined,
                cardAction: {actionType: "NAVIGATION", url: AppUtil.isWeb(userContext) ? ActionUtilV1.getGymCenterWebUrl(seoUrlParam, center.id) : CFActionUtil.addPageParamsToUrl(`curefit://gymfitcenter?centerId=${center.id}`, GymfitUtil.GYM_PAGE_PARAMS)},
                actions: [action],
                description,
                icon: GymfitUtil.getIconBasisGymfitCenterType(userContext, center.type),
            }
            cardItems.push(card)
        }
        if (_.isEmpty(cardItems)) {
            return undefined
        }
        const horizontalCardListingWidget: WidgetView = {
            widgetType: "HORIZONTAL_ACTIONAL_CARD_LISTING_WIDGET",
            title: "Nearby Gyms",
            type: "NEARBY_GYMS",
            titleStyling: {
                fontSize: 18,
            },
            itemType: "NEARBY_GYMS",
            header: {
                action: {
                    actionType: "NAVIGATION",
                    url: "curefit://allgyms?centerType=GYM",
                    title: "VIEW ALL"
                },
                style: {
                    nearbyheader: {
                        marginTop: 10
                    },
                    title: {
                        fontSize: 18,
                        marginLeft: 25,
                    }
                },
            },
            cardItems,
        }
        return horizontalCardListingWidget
    }


    private getDay(userContext: UserContext) {
        return TimeUtil.getMomentNow(userContext.userProfile.timezone).format("dddd").toUpperCase()
    }

    private getTime(time: string) {
        const timez: string[] = time.split(":")
        const date = new Date()
        date.setHours(+timez[0])
        date.setMinutes(+timez[1])
        date.setSeconds(+timez[2])
        return `${TimeUtil.formatDateInTimeZone("UTC", date, "h:mm A")}`
    }

    private getOpenColor(userContext: UserContext): string {
        return AppUtil.isWeb(userContext) ? "#50d166" : "#4AB74A"
    }

    private getClosedColor(userContext: UserContext): string {
        return AppUtil.isWeb(userContext) ? "#e05343" : "#FF3278"
    }

    private isCenterOperational(gymfitCenter: GymfitCenter): boolean {
        return gymfitCenter.status === GymfitCenterStatus.ACTIVE
    }

    private getSeoData(gymfitCenter: GymfitCenter): StructuredSchemaVideoWidget {
        const { name, locality, launchDate } = gymfitCenter,
            thumbnailImages: any = []

        let videoUrl = ""

        gymfitCenter?.media?.heroMedia.forEach((media) => {
            if (media.type === MediaType.VIDEO) {
                videoUrl = `https://cdn-media.cure.fit${media.mediaUrl}`
            }
            thumbnailImages.push(`https://cdn-images.cure.fit/www-curefit-com/image/upload/${media.mediaUrl}`)
        })

        return new StructuredSchemaVideoWidget(name, locality, thumbnailImages, `${launchDate}`, videoUrl)
    }

}
