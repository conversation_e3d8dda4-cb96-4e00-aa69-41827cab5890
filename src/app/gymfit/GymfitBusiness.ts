import { inject, injectable } from "inversify"
import { UserContext } from "@curefit/userinfo-common"
import { GYMFIT_CLIENT_TYPES, IGymfitService } from "@curefit/gymfit-client"
import {
    GymfitCenter,
    GymfitCenterCategory, GymfitCenterType,
    GymfitCheckIn,
    GymfitCheckInState, GymfitMembership,
    GymfitStatus,
    TrialUsage,
    UserMembershipsAndTrialUsages
} from "@curefit/gymfit-common"
import { TimeUtil, Timezone } from "@curefit/util-common"
import * as _ from "lodash"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { Action, InfoBlockProps } from "@curefit/apps-common"
import { Status } from "../page/PageWidgets"
import AppUtil from "../util/AppUtil"
import { ProductListWidget } from "../common/views/WidgetView"
import GymfitUtil from "../util/GymfitUtil"
import { RASHI_CLIENT_TYPES, UserAttributeClient } from "@curefit/rashi-client"
import { CacheHelper } from "../util/CacheHelper"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CultSummary, MembershipDetails, MembershipStates } from "@curefit/cult-common"
import SoftBookingBuilder from "./SoftBookingBuilder"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { UserFitnessPlanControllerServiceInterface, UFS_CLIENT_TYPES } from "@curefit/ufs-client"
import { FitnessPlanDetails } from "@curefit/ufs-client/dist/src/model/fitnessPlanDetails"
import { AlertError } from "../common/errors/AlertError"
import { ICFApiSessionBusiness } from "../auth/SessionBusiness"
import { CultPassImageTextCarousel, CultPassImageTextCarouselItem } from "@curefit/vm-models"
import { Membership } from "@curefit/membership-commons"
import { BASE_TYPES, Logger } from "@curefit/base"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { City } from "@curefit/location-common"
import IUserBusiness from "../user/IUserBusiness"
import { ErrorCodes } from "../error/ErrorCodes"
import { ErrorFactory } from "@curefit/error-client"
import { HTTP_CODE } from "@curefit/error-client/dist/src/GenericError"
import CatalogueServiceUtilities from "../util/CatalogueServiceUtilities"

export interface IGymfitBusiness {
    getCheckinActionForUser(userContext: UserContext, center: GymfitCenter, membershipDetails: UserMembershipsAndTrialUsages, hasComplimentaryAccess: boolean, params?: CFAPIGymfitCheckinActionParams): Promise<Action | null>
    getCheckinCTAForUser(userContext: UserContext, center: GymfitCenter, membershipDetails: Membership[], goldTrialDetails: TrialUsage, hasComplimentaryAccess: boolean, params?: CFAPIGymfitCheckinActionParams): Promise<Action | null>
    getActiveCheckInCode(userContext: UserContext, center: GymfitCenter, centerCheckinId: string): Promise<Action | null>
    getCheckInStatus(tz: Timezone, checkIn: GymfitCheckIn): Status
    isOngoingCheckIn(tz: Timezone, checkIn: GymfitCheckIn): boolean
    isUpcomingCheckIn(tz: Timezone, checkIn: GymfitCheckIn): boolean
    getGymCheckinWhatToDo(tz: Timezone, checkIn: GymfitCheckIn): ProductListWidget
    getBiometricWidget(): InfoBlockProps
    getCheckInActionUrl(userContext: UserContext, gymfitCenter: GymfitCenter, checkInId: string): string
    updateLocalityPreference(userContext: UserContext, locality: string): Promise<{ success: boolean, currentCityName: string, detectedCity: City}>
    getLocalityPreference(userContext: UserContext): Promise<string>
    getPastCheckInAction(userContext: UserContext, tz: Timezone, checkIn: GymfitCheckIn, membershipDetails: Membership[], goldTrialDetails: TrialUsage, hasComplimentaryAccess: boolean, params?: CFAPIGymfitCheckinActionParams): Promise<Action>
    getGymCheckInStatus(tz: Timezone, checkIn: GymfitCheckIn): CFAPIGymfitCheckInState
    hasWorkoutPlan(userId: string): Promise<boolean>
    checkQrError(ex: any): boolean
    getQrError(ex: any, centerType?: GymfitCenterType): any
    getNextGymHolidayWidget(userContext: UserContext, gymfitCenter: GymfitCenter): CultPassImageTextCarousel | null
    optOutCreatePlanPushNotification(userId: string): Promise<boolean>
}

export type CFAPIGymfitCheckInState = "UPCOMING" | "ONGOING" | "MISSED" | "CANCELLED" | "COMPLETED"
const PACK_BROWSE_WIDGET_ID = process.env.ENVIRONMENT === "PRODUCTION" || process.env.ENVIRONMENT === "ALPHA" ? "3f6ba0b1-5631-415a-a3c7-35325e2d8cb4" : "e9d099cf-2af4-419b-ad9e-8b1d7528523c-gym"
const GYMFIT_TENANT_ID = "1"
export interface CFAPIGymfitCheckinActionParams {
    forClp?: boolean
    actionTitle?: string
    trialUserActionTitle?: string
    isSoftbookingEnabled?: boolean
    eventData?: any
    forGymDetailsPage?: boolean
}

export const GYM_CHECK_IN_PAGE_VERSION = 8.15
const GYM_LOCALITY_PREFERENCE = "GYM_LOCALITY_PREFERENCE"
const GYM_LOCALITIES = "gym_localities"
const MIGRATION = "MIGRATION"
@injectable()
export class GymfitBusiness implements IGymfitBusiness {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(GYMFIT_CLIENT_TYPES.GymfitService) private gymfitService: IGymfitService,
        @inject(RASHI_CLIENT_TYPES.UserAttributeClient) public userAttributeClient: UserAttributeClient,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected cacheHelper: CacheHelper,
        @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
        @inject(CUREFIT_API_TYPES.SessionService) private sessionBusiness: ICFApiSessionBusiness,
        @inject(UFS_CLIENT_TYPES.UserFitnessPlanControllerService) private userFitnessPlanControllerService: UserFitnessPlanControllerServiceInterface,
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) private membershipService: IMembershipService,
        @inject(LOCATION_TYPES.CityService) private cityService: ICityService,
        @inject(CUREFIT_API_TYPES.UserBusiness) private userBusiness: IUserBusiness,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
    ) {

    }

    async getCheckinActionForUser(userContext: UserContext, center: GymfitCenter, membershipDetails: UserMembershipsAndTrialUsages, hasComplimentaryAccess: boolean, params: CFAPIGymfitCheckinActionParams = {}): Promise<Action | null> {
        // 1. Active Trial
        // 2. Trial finished and no membership
        // 3. Trail INACTIVE and no active membership
        // 4. Membership and sound checkin
        // 5. Membership and false checkin --> Upgrade
        // 6. If Cult, show book a class
        const tz = userContext.userProfile.timezone
        // if (!center.isOperational || !center.allowCheckIn) {
        if (!center.isOperational) {
            return { actionType: "NAVIGATION", title: "CLOSED NOW", isEnabled: false, disabled: true }
        }
        if (center.category === GymfitCenterCategory.CULT) {
            return this.getCultClassBookingAction(userContext, center)
        }
        if (!userContext.sessionInfo.isUserLoggedIn) {
            return {
                actionType: "SHOW_ALERT_MODAL",
                title: membershipDetails.trialUsage?.status === GymfitStatus.ACTIVE ? "TRY FOR FREE " : params.actionTitle ?? "CHECK-IN",
                meta: {
                    title: "Login Required!",
                    subTitle: "Please login to continue",
                    actions: [
                        {
                            actionType: "LOGOUT",
                            title: "Login"
                        }]
                }
            }
        }
        // const groupWorkout = center.offerings ? center.offerings.find(offering => offering.offering && offering.offering.code === "GX") : undefined
        // if (groupWorkout) {
        //     return {actionType: "NAVIGATION", title: "BOOK", url: `curefit://centergroupschedule?centerId=${center.id}`}
        // }
        const cultMembership = await GymfitUtil.getOngoingCultMembership(userContext, this.membershipService)
        if (cultMembership) {
            if (cultMembership.status === "PURCHASED") {
                return this.getClassBookingAction(userContext, center, params.actionTitle)
            }
            if (cultMembership.status === "PAUSED" && _.isEmpty(membershipDetails.membershipSummary.activeMembership)) {
                return await this.getUnPauseCultClassAction(cultMembership, userContext)
            }
        }

        if (hasComplimentaryAccess) {
            return this.getClassBookingAction(userContext, center, params.actionTitle)
        }

        if (membershipDetails.trialUsage && membershipDetails.trialUsage.status === GymfitStatus.ACTIVE) {
            const futureBookings = membershipDetails.trialUsage.futureBookingCount ?? 0
            if (membershipDetails.trialUsage.used + futureBookings < membershipDetails.trialUsage.maxCount) {
                const ctaTitle = params.trialUserActionTitle ? params.trialUserActionTitle : params.actionTitle ? params.actionTitle : "TRY FOR FREE"
                if (AppUtil.isWeb(userContext)) {
                    return { actionType: "WEB_SHOW_GYM_QUICK_CHECKIN_MODAL", title: "TRY FOR FREE", meta: { centerId: center.id } }
                }
                if (!GymfitUtil.isNewBookingFlowSupported(userContext)) {
                    return GymfitUtil.getSlotBookingAction(userContext, Number(center.id), ctaTitle)
                }
                if (params.isSoftbookingEnabled) {
                    return { ...SoftBookingBuilder.getBookingAction(ctaTitle, center.id), eventData: { ...(params.eventData || {}), actionTarget: "Cult_Pass_Soft_Booking_Modal" } }
                }
                return { actionType: "NAVIGATION", title: ctaTitle, url: `curefit://gymquickcheckin?centerId=${center.id}`, eventData: { ...(params.eventData || {}), actionTarget: "gymquickcheckin" } }
            } else {
                return {
                    actionType: "SHOW_ALERT_MODAL",
                    title: params.actionTitle ? params.actionTitle : "CHECK-IN",
                    meta: {
                        title: `Buy cultpass ${center.type}`,
                        subTitle: `Your free trials are over. Please purchase cultpass ${center.type} to check-in.`,
                        actions: [params.forClp ?
                            { actionType: "SCROLL_TO_WIDGET", title: "Buy Pack", meta: { widgetId: process.env.ENVIRONMENT === "STAGE" ? "e9d099cf-2af4-419b-ad9e-8b1d7528523c-gym" : "3f6ba0b1-5631-415a-a3c7-35325e2d8cb4" } }
                            : { actionType: "HIDE_ALERT_MODAL", title: "OK" }],
                        meta: { subTitleStyle: { fontSize: 14 } },
                        // actions: [pageId === "clp" ? {actionType: "SCROLL_TO_WIDGET", title: "Buy Pack", meta: {widgetId: "gymfit_clp_packs"}} : {actionType: "NAVIGATION", title: "Buy Pack", url: "curefit://tabpage?pageId=gymfitclp"} ]
                    },
                    eventData: params.eventData
                }
            }
        }
        if (_.isEmpty(membershipDetails.membershipSummary.activeMembership)) {
            if (params.forGymDetailsPage) {
                // return {
                //     actionType: "NAVIGATION",
                //     title: "Buy Pack",
                //     url: `curefit://cultfitclp?pageId=cult&selectedTab=gymfitList&widgetId=${PACK_BROWSE_WIDGET_ID}`,
                //     eventData: params.eventData
                // }
                return {
                    actionType: "SHOW_ALERT_MODAL",
                    title: "CHECK-IN",
                    meta: {
                        title: "Buy Pack",
                        subTitle: "You do not have any active membership, please purchase pack to check-in.",
                        actions: [{ actionType: "HIDE_ALERT_MODAL", title: "OK" }],
                        meta: userContext.sessionInfo.appVersion >= 8.63 ? { titleStyle: { fontSize: 16 }, subTitleStyle: { fontSize: 14 } } : { positiveActionTextStyle: { fontSize: 14 } }
                    },
                    eventData: params.eventData
                }
            }
            return {
                actionType: "SHOW_ALERT_MODAL",
                title: "CHECK-IN",
                meta: {
                    title: "No active membership",
                    subTitle: "You do not have any active membership, please purchase pack to continue using gyms.",
                    actions: [params.forClp ?
                        { actionType: "SCROLL_TO_WIDGET", title: "BUY PACK", meta: { widgetId: PACK_BROWSE_WIDGET_ID } }
                        : { actionType: "HIDE_ALERT_MODAL", title: "OK" }],
                    meta: userContext.sessionInfo.appVersion >= 8.63 ? { titleStyle: { fontSize: 16 }, subTitleStyle: { fontSize: 14 } } : { positiveActionTextStyle: { fontSize: 14 } }
                    // actions: [pageId === "clp" ? {actionType: "SCROLL_TO_WIDGET", title: "Buy Pack", meta: {widgetId: "gymfit_clp_packs"}} : {actionType: "NAVIGATION", title: "Buy Pack", url: "curefit://tabpage?pageId=gymfitclp"} ]
                },
                eventData: params.eventData
            }
        }
        const timeNow = TimeUtil.getDateNow(tz).getTime()
        for (let i = 0; i < membershipDetails.membershipSummary.activeMembership.restrictions.length; i++) {
            const restriction = membershipDetails.membershipSummary.activeMembership.restrictions[i]
            if (restriction.centerCategories.includes(center.category)) {
                if (restriction.restrictionType === "UNLIMITED") {
                    // User has unlimited access to this gym category
                    return this.getClassBookingAction(userContext, center, params.actionTitle)
                } else if (restriction.restrictionType === "MONTHLY") {
                    const startDate = TimeUtil.getMomentForDateString(restriction.startDate, tz).toDate().getTime()
                    const endDate = TimeUtil.getMomentForDateString(restriction.endDate, tz).toDate().getTime()
                    if (timeNow < startDate || timeNow > endDate || restriction.usedCount >= restriction.maxCount) {
                        continue
                    }
                    // User has unused classes which are within the restriction start and end date
                    return this.getClassBookingAction(userContext, center, params.actionTitle, params.eventData)
                } else if (restriction.restrictionType === "LIMITED") {
                    if (restriction.usedCount < restriction.maxCount) {
                        return this.getClassBookingAction(userContext, center, params.actionTitle, params.eventData)
                    } else {
                        return {
                            actionType: "SHOW_ALERT_MODAL",
                            title: "CHECK-IN",
                            meta: {
                                title: "Limited Access Exhausted",
                                subTitle: "Your limited access is exhausted",
                                actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
                            }
                        }
                    }
                }
            }
        }
    }

    async getCheckinCTAForUser(userContext: UserContext, center: GymfitCenter, membershipDetails: Membership[], goldTrialDetails: TrialUsage, hasComplimentaryAccess: boolean, params?: CFAPIGymfitCheckinActionParams): Promise<Action | null> {
        // 1. Active Trial
        // 2. Trial finished and no membership
        // 3. Trail INACTIVE and no active membership
        // 4. Membership and sound checkin
        // 5. Membership and false checkin --> Upgrade
        // 6. If Cult, show book a class
        const tz = userContext.userProfile.timezone
        // if (!center.isOperational || !center.allowCheckIn) {
        if (!center.isOperational) {
            return { actionType: "NAVIGATION", title: "CLOSED NOW", isEnabled: false, disabled: true }
        }
        if (center.category === GymfitCenterCategory.CULT) {
            return this.getCultClassBookingAction(userContext, center)
        }
        if (!userContext.sessionInfo.isUserLoggedIn) {
            return {
                actionType: "SHOW_ALERT_MODAL",
                title: goldTrialDetails.status === GymfitStatus.ACTIVE ? "TRY FOR FREE " : params.actionTitle ?? "CHECK-IN",
                meta: {
                    title: "Login Required!",
                    subTitle: "Please login to continue",
                    actions: [
                        {
                            actionType: "LOGOUT",
                            title: "Login"
                        }]
                }
            }
        }
        // const groupWorkout = center.offerings ? center.offerings.find(offering => offering.offering && offering.offering.code === "GX") : undefined
        // if (groupWorkout) {
        //     return {actionType: "NAVIGATION", title: "BOOK", url: `curefit://centergroupschedule?centerId=${center.id}`}
        // }
        const cultMembership = await GymfitUtil.getOngoingCultMembership(userContext, this.membershipService)
        if (cultMembership) {
            if (cultMembership.status === "PURCHASED") {
                return this.getClassBookingAction(userContext, center, params.actionTitle)
            }
            if (cultMembership.status === "PAUSED" && _.isEmpty(membershipDetails)) {
                return await this.getUnPauseCultClassAction(cultMembership, userContext)
            }
        }

        if (hasComplimentaryAccess) {
            return this.getClassBookingAction(userContext, center, params.actionTitle)
        }

        if (goldTrialDetails.status === GymfitStatus.ACTIVE) {
            const futureBookings = goldTrialDetails.futureBookingCount ?? 0
            if (goldTrialDetails.used + futureBookings < goldTrialDetails.maxCount) {
                const ctaTitle = params.trialUserActionTitle ? params.trialUserActionTitle : params.actionTitle ? params.actionTitle : "TRY FOR FREE"
                if (AppUtil.isWeb(userContext)) {
                    return { actionType: "WEB_SHOW_GYM_QUICK_CHECKIN_MODAL", title: "TRY FOR FREE", meta: { centerId: center.id } }
                }
                if (!GymfitUtil.isNewBookingFlowSupported(userContext)) {
                    return GymfitUtil.getSlotBookingAction(userContext, Number(center.id), ctaTitle)
                }
                if (params.isSoftbookingEnabled) {
                    return { ...SoftBookingBuilder.getBookingAction(ctaTitle, center.id), eventData: { ...(params.eventData || {}), actionTarget: "Cult_Pass_Soft_Booking_Modal" } }
                }
                return { actionType: "NAVIGATION", title: ctaTitle, url: `curefit://gymquickcheckin?centerId=${center.id}`, eventData: { ...(params.eventData || {}), actionTarget: "gymquickcheckin" } }
            } else {
                return {
                    actionType: "SHOW_ALERT_MODAL",
                    title: params.actionTitle ? params.actionTitle : "CHECK-IN",
                    meta: {
                        title: "Buy cultpass PRO",
                        subTitle: "Your free trials are over. Please purchase cultpass PRO to check-in.",
                        actions: [params.forClp ?
                            { actionType: "SCROLL_TO_WIDGET", title: "Buy Pack", meta: { widgetId: process.env.ENVIRONMENT === "STAGE" ? "e9d099cf-2af4-419b-ad9e-8b1d7528523c-gym" : "3f6ba0b1-5631-415a-a3c7-35325e2d8cb4" } }
                            : { actionType: "HIDE_ALERT_MODAL", title: "OK" }],
                        meta: { subTitleStyle: { fontSize: 14 } },
                        // actions: [pageId === "clp" ? {actionType: "SCROLL_TO_WIDGET", title: "Buy Pack", meta: {widgetId: "gymfit_clp_packs"}} : {actionType: "NAVIGATION", title: "Buy Pack", url: "curefit://tabpage?pageId=gymfitclp"} ]
                    },
                    eventData: params.eventData
                }
            }
        }
        const presentGoldMembership = GymfitUtil.getPresentGoldMembership(membershipDetails)
        if (_.isEmpty(presentGoldMembership)) {
            if (params.forGymDetailsPage) {
                return {
                    actionType: "NAVIGATION",
                    title: "Buy Pack",
                    url: `curefit://cultfitclp?pageId=cult&selectedTab=gymfitList&widgetId=${PACK_BROWSE_WIDGET_ID}`,
                    eventData: params.eventData
                }
            }
            return {
                actionType: "SHOW_ALERT_MODAL",
                title: "CHECK-IN",
                meta: {
                    title: "No active membership",
                    subTitle: "You do not have any active membership, please purchase pack to continue using gyms.",
                    actions: [params.forClp ?
                        { actionType: "SCROLL_TO_WIDGET", title: "BUY PACK", meta: { widgetId: PACK_BROWSE_WIDGET_ID } }
                        : { actionType: "HIDE_ALERT_MODAL", title: "OK" }],
                    meta: userContext.sessionInfo.appVersion >= 8.63 ? { titleStyle: { fontSize: 16 }, subTitleStyle: { fontSize: 14 } } : { positiveActionTextStyle: { fontSize: 14 } }
                    // actions: [pageId === "clp" ? {actionType: "SCROLL_TO_WIDGET", title: "Buy Pack", meta: {widgetId: "gymfit_clp_packs"}} : {actionType: "NAVIGATION", title: "Buy Pack", url: "curefit://tabpage?pageId=gymfitclp"} ]
                },
                eventData: params.eventData
            }
        }
        if (presentGoldMembership.status === "PAUSED") {
            return await this.getUnPauseGymMembershipClassAction(presentGoldMembership, userContext)
        }

        const benefitTypeRequired = this.getBenefitTypeFromCenterCategory(center.category)
        for (let currentBenefitIndex = 0; currentBenefitIndex < presentGoldMembership.benefits.length; currentBenefitIndex++) {
            const benefitName = presentGoldMembership.benefits[currentBenefitIndex].name
            if (benefitName === benefitTypeRequired) {
                return this.getClassBookingAction(userContext, center, params.actionTitle)
            }
        }
    }

    getBenefitTypeFromCenterCategory(centerCategory: GymfitCenterCategory) {
        if (centerCategory === GymfitCenterCategory.GA) {
            return "GYMFIT_GA"
        }
        if (centerCategory === GymfitCenterCategory.GX) {
            return "GYMFIT_GX"
        }
        return ""
    }

    getNextGymHolidayWidget(userContext: UserContext, gymfitCenter: GymfitCenter): CultPassImageTextCarousel | null {
        const holidaysList: CultPassImageTextCarouselItem[] = []
        gymfitCenter.effectiveHolidays?.sort((a, b) => a.startTime < b.startTime ? -1 : 1).splice(0, 1).map(holiday => {
            holidaysList.push({
                title: GymfitUtil.getGymHolidayMessage(userContext, gymfitCenter, holiday),
                imageUrl: "/image/gymfit/info.png",
                imageWidth: 40,
                imageAspectRatio: 1,
            })
        })
        if (!_.isEmpty(holidaysList)) {
            const centerHolidayWidget = new CultPassImageTextCarousel()
            centerHolidayWidget.items = holidaysList
            centerHolidayWidget.itemWidth = 335
            return centerHolidayWidget
        }
        return null
    }

    async getActiveCheckInCode(userContext: UserContext, center: GymfitCenter, centerCheckinId: string): Promise<Action | null> {
        const activeCheckins = await this.gymfitService.getUpcomingGymfitCheckIns(userContext.userProfile.userId)
        // Check if the centerCheckinId supplied is active
        const activeCheckin = activeCheckins.find((activeCheckin) => activeCheckin.id === centerCheckinId)
        if (activeCheckin) {
            return { actionType: "NAVIGATION", title: activeCheckin.passCode ? `CODE: ${activeCheckin.passCode}` : "" }
        }
        return null
    }

    async hasWorkoutPlan(userId: string): Promise<boolean> {
        const fitnessPlanDetails = await this.userFitnessPlanControllerService.getUserFitnessPlanDetailsV2(userId, GYMFIT_TENANT_ID)
        return fitnessPlanDetails?.userMesoCycle?.id ? true : false
    }
    getCheckInStatus(tz: Timezone, checkIn: GymfitCheckIn): Status {
        if (checkIn.state === GymfitCheckInState.CREATED) {
            const currentTime = TimeUtil.getEpochFromDate(TimeUtil.getDateNow(tz))
            const endTime = TimeUtil.getEpochFromDate(new Date(checkIn.endTime))
            return currentTime > endTime ? {
                text: "MISSED",
                colour: "#b00020"
            } : {
                    text: "UPCOMING",
                    colour: "#ffa300"
                }
        } else if (checkIn.state === GymfitCheckInState.CANCELLED || checkIn.state === GymfitCheckInState.FORCED_CANCELLED) {
            return {
                text: "CANCELLED",
                colour: "#b00020"
            }
        } else if (checkIn.state === GymfitCheckInState.VALIDATED) {
            const currentTime = TimeUtil.getEpochFromDate(TimeUtil.getDateNow(tz))
            const endTime = TimeUtil.getEpochFromDate(new Date(checkIn.endTime))
            return currentTime > endTime ? {
                text: "COMPLETED",
                colour: "#4ab74a"
            } : {
                    text: "ONGOING",
                    colour: "#3888ff"
                }
        }
    }

    getGymCheckInStatus(tz: Timezone, checkIn: GymfitCheckIn): CFAPIGymfitCheckInState {
        if (checkIn.state === GymfitCheckInState.CREATED) {
            const currentTime = TimeUtil.getEpochFromDate(TimeUtil.getDateNow(tz))
            const endTime = TimeUtil.getEpochFromDate(new Date(checkIn.endTime))
            return currentTime > endTime ? "MISSED" : "UPCOMING"
        } else if (checkIn.state === GymfitCheckInState.CANCELLED || checkIn.state === GymfitCheckInState.FORCED_CANCELLED) {
            return "CANCELLED"
        } else if (checkIn.state === GymfitCheckInState.VALIDATED) {
            const currentTime = TimeUtil.getEpochFromDate(TimeUtil.getDateNow(tz))
            const endTime = TimeUtil.getEpochFromDate(new Date(checkIn.endTime))
            return currentTime > endTime ? "COMPLETED" : "ONGOING"
        }
    }

    isOngoingCheckIn(tz: Timezone, checkIn: GymfitCheckIn): boolean {
        if (!checkIn) return false
        if (checkIn.state === GymfitCheckInState.VALIDATED) {
            const currentTime = TimeUtil.getEpochFromDate(TimeUtil.getDateNow(tz))
            const startTime = TimeUtil.getEpochFromDate(new Date(checkIn.startTime))
            const endTime = TimeUtil.getEpochFromDate(new Date(checkIn.endTime))
            // if (currentTime >= startTime && currentTime <= endTime) {
            if (currentTime <= endTime) {
                return true
            }
        }
        return false
    }

    isUpcomingCheckIn(tz: Timezone, checkIn: GymfitCheckIn): boolean {
        if (!checkIn) return false
        if (checkIn.state === GymfitCheckInState.CREATED) {
            const currentTime = TimeUtil.getEpochFromDate(TimeUtil.getDateNow(tz))
            const endTime = TimeUtil.getEpochFromDate(new Date(checkIn.endTime))
            if (currentTime < endTime) {
                return true
            }
        }
        return false
    }

    async updateLocalityPreference(userContext: UserContext, locality: string): Promise<{ success: boolean, currentCityName: string, detectedCity: City}> {
        const userId = userContext.userProfile.userId
        const cityId = userContext.userProfile.cityId

        try {
            await this.userBusiness.setLocationPreference(userContext, userId, cityId, GymfitUtil.getUserLocationPreferenceFromLocality(locality))
        } catch (err) {
            this.logger.error(`ERROR: Can't update user location preference while updating gymfit locality preference for userID - ${userId} and cityID - ${cityId}`)
        }

        const cityAndCountry = await this.cityService.getCityAndCountry(AppUtil.getTenantFromUserContext(userContext), userContext.sessionInfo.lat, userContext.sessionInfo.lon)
        const newCityId = cityAndCountry.city ? cityAndCountry.city.cityId : undefined
        const oldCity = await this.getCityName(cityId)
        this.logger.debug("updateLocalityPreference lat: " + userContext.sessionInfo.lat + " lon: " + userContext.sessionInfo.lon)
        this.logger.debug("updateLocalityPreference old cityID: " + cityId + " newCityID: " + newCityId)

        if (locality === GymfitUtil.LOCALITY_SEARCH_KEY && userContext.sessionInfo.appVersion > 9.42 && cityId !== newCityId) {
            return Promise.resolve({ success: false, currentCityName: oldCity, detectedCity: cityAndCountry.city})
        }

        if (!userContext.sessionInfo.isUserLoggedIn) {
            const sessionData = userContext.sessionInfo.sessionData
            sessionData.gymLocalityPreference = `${cityId},${locality}`
            await this.sessionBusiness.updateSessionData(userContext.sessionInfo.at, sessionData)
        } else {
            const key = this.getLocalityPreferenceKey(cityId)
            await this.userAttributeClient.setUserAttributes({
                userId: Number(userId),
                attribute: key,
                attrValue: locality,
                namespace: GymfitUtil.getRashiNamespace(),
                description: "preferred locality",
                dataType: "STRING",
                occuredAt: new Date().getTime()
            })
        }
        return Promise.resolve({ success: true, currentCityName: "", detectedCity: undefined})
    }

    async getCityName(cityId: string): Promise<string> {
        const cityPromise = !_.isNil(cityId) ? this.cityService.getCityById(cityId) : undefined
        if (cityPromise != undefined) {
            const city = await cityPromise
            return city ? city.name : undefined
        }
        return null
    }

    async optOutCreatePlanPushNotification(userId: string): Promise<boolean> {
      await this.userAttributeClient.setUserAttributes({
          userId: Number(userId),
          attribute: GymfitUtil.getCheckinNotificationPreferenceKey(),
          attrValue: true,
          namespace: GymfitUtil.getRashiNamespace(),
          description: "checkin notification preference",
          dataType: "STRING",
          occuredAt: new Date().getTime()
      })
      return true
    }

    async getLocalityPreference(userContext: UserContext): Promise<string> {
        try {
            const userId = userContext.userProfile.userId
            const cityId = userContext.userProfile.cityId
            if (!userContext.sessionInfo.isUserLoggedIn) {
                const  { gymLocalityPreference = "" } = userContext.sessionInfo.sessionData
                const [prefCityId, prefLocality] = gymLocalityPreference.split(/,(.+)/)
                return prefCityId === cityId ? prefLocality : undefined
            }
            const key = this.getLocalityPreferenceKey(cityId)
            const response = await this.userAttributeClient.getUserAttributes(
                Number(userId),
                key,
                GymfitUtil.getRashiNamespace()
            )
            return !_.isEmpty(response) ? response[0].attrValue : undefined
        } catch (err) {
            return undefined
        }
    }

    checkQrError(ex: any): boolean {
        if (ex.statusCode === 412) {
            switch (ex.meta?.status) {
                case "CITY_UNMATCHED":
                case "NO_ACTIVE_MEMBERSHIP":
                case "TRIAL_EXHAUSTED":
                case "AWAY_CHECKIN_USED":
                case "AWAY_CHECKIN_NOT_ALLOWED":
                case "ERR_CHECKIN_FORBID_VOUCHER_DEVICE_MISMATCH":
                case "SELECT_CITY_UNMATCHED":
                case "ACCESS_CREDITS_EXHAUSTED":
                    return true
                default:
                    return false
            }
        }
        return false
    }

    getQrError(ex: any, centerType?: GymfitCenterType): AlertError {
        switch (ex.meta?.status) {
            case "CITY_UNMATCHED":
                return GymfitUtil.getCrossCityCheckinError(ex)
            case "NO_ACTIVE_MEMBERSHIP":
                return GymfitUtil.getNoActiveMembershipError(ex, centerType)
            case "TRIAL_EXHAUSTED":
                return GymfitUtil.getTrialExhaustedError(ex, centerType)
            case "AWAY_CHECKIN_USED":
                return GymfitUtil.getAwayCheckinUserError(ex)
            case "AWAY_CHECKIN_NOT_ALLOWED":
                return GymfitUtil.getAwayCheckinNotAllowedError(ex)
            case "ACCESS_CREDITS_EXHAUSTED":
                return GymfitUtil.getAwayCreditsUsedError(ex)
            case "CULT_SESSIONS_USED":
                return GymfitUtil.getCultSessionsUsedError(ex)
            case "ERR_CHECKIN_FORBID_VOUCHER_DEVICE_MISMATCH":
                throw this.errorFactory.withCode(ErrorCodes.ERR_CHECKIN_FORBID_VOUCHER_DEVICE_MISMATCH, HTTP_CODE.PRECONDITION_FAILED).build()
            case "SELECT_CITY_UNMATCHED":
                return GymfitUtil.getSelectAwayCityError(ex)
            default:
                return undefined
        }
    }

    getGymCheckinWhatToDo(tz: Timezone, checkIn: GymfitCheckIn): ProductListWidget {
        // const checkinMins = TimeUtil.diffInMinutes(tz, TimeUtil.formatDateInTimeZone(tz, new Date(checkIn.startTime), "YYYY-MM-DD HH:mm:ss"), TimeUtil.formatDateInTimeZone(tz, new Date(checkIn.endTime), "YYYY-MM-DD HH:mm:ss"))
        // const hours = Math.floor(checkinMins / 60)
        // const mins = checkinMins % 60
        // const hourText = hours > 0 ? hours === 1 ? "1 hour" : hours + " hours" : ""
        // const minsText = mins > 0 ? " " + mins + " mins" : ""
        // let firstPointTitle = `Check-in within ${hourText}${minsText}`
        // let firstPointSubTitle = `Arrive within ${hourText}${minsText} of booking. Use the gym-fit tab at the center to check-in`
        // if (checkIn.event.baseEvent.center.category === GymfitCenterCategory.GX) {
        //     firstPointTitle = "Check-in before your class starts"
        //     firstPointSubTitle = "Arrive before your class starts. Use the gym-fit tab at the center to check-in"
        // }
        const items = [
            {
                title: `Contactless Check-in`,
                subTitle: `Arrive at the gym and use QR code on the app to check-in`,
                icon: "/image/icons/howItWorks/checkin_new.png"
            },
            {
                title: "Guided gym tour",
                subTitle: "Know about gym facilities",
                icon: "/image/icons/howItWorks/gym_tour_new.png"
            },
            {
                title: "Workout guidance",
                subTitle: "One of the cult certified trainers will guide you",
                icon: "/image/icons/howItWorks/workout_guidence_new.png"
            },
            {
                title: "Free body composition check",
                subTitle: "Find your body fat, metabolic rate and more",
                icon: "/image/icons/howItWorks/body_checkup_new.png"
            }
        ]
        const widget = new ProductListWidget(
            "SMALL",
            { title: "What To Expect", titleProps: { style: { fontSize: 18 } } },
            items
        )
        widget.hideSepratorLines = true
        return widget
    }

    async getPastCheckInAction(userContext: UserContext, tz: Timezone, checkIn: GymfitCheckIn, membershipDetails: Membership[], goldTrialDetails: TrialUsage, hasComplimentaryAccess: boolean, params: CFAPIGymfitCheckinActionParams = {}): Promise<Action> {
        if (this.isOngoingCheckIn(tz, checkIn)) {
            const hasWorkoutPlan = await this.hasWorkoutPlan(userContext.userProfile.userId)
            if (hasWorkoutPlan && !AppUtil.isWeb(userContext) && userContext.sessionInfo.appVersion > 8.65) {
                return GymfitUtil.getUpComingFitnessPlanAction(tz, checkIn)
            }
            if (!AppUtil.isWeb(userContext) && !GymfitUtil.isNewBookingFlowSupported(userContext)) {
                return { actionType: "NAVIGATION" }
            }
            return { actionType: "GYM_CHECKOUT", title: "CHECKOUT", meta: { url: `/gymfit/checkout/${checkIn.id}` } }
        }

        if (this.isUpcomingCheckIn(tz, checkIn)) {
            return GymfitUtil.getMarkAttendanceAction(tz, checkIn)
        }

        return await this.getCheckinCTAForUser(userContext, checkIn.event.baseEvent.center, membershipDetails, goldTrialDetails, hasComplimentaryAccess, { ...params, actionTitle: "CHECK-IN AGAIN" })
    }

    getCheckInActionUrl(userContext: UserContext, gymfitCenter: GymfitCenter, checkInId: string): string {
        return `curefit://gymcheckindetailspage?centerId=${gymfitCenter.id}&checkinId=${checkInId}`
    }

    getBiometricWidget(): InfoBlockProps {
        return {
            widgetType: "INFO_BLOCK_WIDGET",
            infoBlock: {
                title: "BIO-MERIC REGISTRATION",
                message: "Register your bio-metric on the gym.fit tab at the center & do seamless check-in at any cult or gym.fit centers in the future.",
                calloutView: {
                    title: "BIO-MERIC REGISTRATION",
                    subTitle: "Register your bio-metric on the gym.fit tab at the center & do seamless check-in at any cult or gym.fit centers in the future.",
                    tag: "PENDING",
                    bgColor: "#df5532"
                }
            }
        }
    }

    private getClassBookingAction(userContext: UserContext, center: GymfitCenter, actionTitle?: string, eventData?: any): Action {
        if (center.category === GymfitCenterCategory.CULT) {
            return this.getCultClassBookingAction(userContext, center)
        }
        if (AppUtil.isWeb(userContext)) {
            return { actionType: "WEB_SHOW_GYM_QUICK_CHECKIN_MODAL", title: actionTitle ? actionTitle : "CHECK-IN", meta: { centerId: center.id } }
        }
        if (!GymfitUtil.isNewBookingFlowSupported(userContext)) {
            return GymfitUtil.getSlotBookingAction(userContext, Number(center.id), "BOOK")
        }
        return { actionType: "NAVIGATION", title: actionTitle ? actionTitle : "CHECK-IN", url: `curefit://gymquickcheckin?centerId=${center.id}`, eventData: { ...(eventData || {}), actionTarget: "gymquickcheckin" } }
    }

    private getCultClassBookingAction(userContext: UserContext, center: GymfitCenter): Action {
        if (AppUtil.isWeb(userContext)) {
            return { actionType: "NAVIGATION", title: "CHECK-IN", url: `curefit://classbooking?productType=FITNESS&centerId=${center.externalId}` }
        }
        return { actionType: "NAVIGATION", title: "CHECK-IN", url: `curefit://classbookingv2?productType=FITNESS&centerId=${center.externalId}` }
    }

    private async getUnPauseCultClassAction(membership: Membership, userContext: UserContext): Promise<Action | null> {
        return {
            actionType: "SHOW_ALERT_MODAL",
            title: "CHECK-IN",
            meta: {
                title: "Resume Cult Membership",
                subTitle: "Your cultpass ELITE membership is paused right now. Please resume it to access gyms for free.",
                actions: [{ actionType: "NAVIGATION", title: "RESUME", url: await CatalogueServiceUtilities.getCultMembershipDetailsPageAction(membership.id?.toString(), userContext) }],
                meta: { subTitleStyle: { fontSize: 14 } }
            }
        }
    }

    private async getUnPauseGymMembershipClassAction(membership: Membership, userContext: UserContext): Promise<Action> {
        return {
            actionType: "SHOW_ALERT_MODAL",
            title: "CHECK-IN",
            meta: {
                title: "Resume cultpass PRO Membership",
                subTitle: "Your cultpass PRO membership is paused right now. Please resume it to access gyms.",
                actions: [{ actionType: "NAVIGATION", title: "RESUME", url: await GymfitUtil.getGoldMembershipDetailsUrl(membership, userContext) }],
                meta: { subTitleStyle: { fontSize: 14 } }
            }
        }
    }


    private getLocalityPreferenceKey(cityId: string): string {
        return cityId === "Bangalore" ? GYM_LOCALITY_PREFERENCE : `${cityId}_gym_pref`
    }
}

export default GymfitBusiness
