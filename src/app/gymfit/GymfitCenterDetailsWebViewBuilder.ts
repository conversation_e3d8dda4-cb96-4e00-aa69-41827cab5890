import { injectable } from "inversify"
import {
    Action,
    CenterAddressWidget,
    DatePickerWidget,
    getOffersWidget,
    Header,
    InfoCard,
    ProductDetailPage,
    ProductGridWidget,
    ProductListWidget,
    WidgetView
} from "../common/views/WidgetView"
import { UserContext } from "@curefit/userinfo-common"
import { PackOfferItem, GymFitProductPricesResponse } from "@curefit/offer-common"
import { GymfitCenter } from "@curefit/gymfit-common"
import { ProductPrice } from "@curefit/product-common"
import { TimeUtil } from "@curefit/util-common"
import { Currency } from "@curefit/finance-common"
import GymfitCenterPageConfig from "./GymfitCenterPageConfig"
import * as _ from "lodash"
import { OrderProduct } from "@curefit/order-common"
import AppUtil from "../util/AppUtil"
import * as momentTz from "moment-timezone"
import CultUtil from "../util/CultUtil"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"
import { DEFAULT_GYMFIT_PRODUCT_SERVICE } from "../common/Constants"

@injectable()
export default class GymfitCenterDetailsWebViewBuilder {
    constructor() {}

    async getView(userContext: UserContext, packOffersV3: GymFitProductPricesResponse, gymfitCenter: GymfitCenter, gymfitCenterProducts: OfflineFitnessPack[], gymfitCenterPageConfig: GymfitCenterPageConfig, selectedPlanId: string, selectedStartDate: string): Promise<ProductDetailPage> {
        const productDetailPage: ProductDetailPage = new ProductDetailPage()
        selectedPlanId = this.getSelectedPlanId(selectedPlanId, gymfitCenterProducts)
        const selectedPlan: OfflineFitnessPack = gymfitCenterProducts.find(p => p.productId === selectedPlanId)
        const offerItem = CultUtil.getOfferItem(packOffersV3, selectedPlanId)
        productDetailPage.actions = [this.getAction(selectedStartDate, gymfitCenter, selectedPlan, offerItem)]
        productDetailPage.widgets.push(this.getSummaryWidget(userContext, packOffersV3, gymfitCenter, gymfitCenterProducts, selectedPlanId, selectedStartDate, selectedPlan, offerItem))
        if (offerItem && offerItem.offers && offerItem.offers.length > 0) {
            productDetailPage.widgets.push(getOffersWidget("Offer", offerItem.offers))
        }
        productDetailPage.widgets.push(this.getIncludedInMembershipWidget(selectedPlan))
        productDetailPage.widgets.push(this.getAvailableAtGymWidget(gymfitCenter, selectedPlan))
        productDetailPage.widgets.push(this.getAddressWidget(gymfitCenter))
        if (gymfitCenter.amenities) {
            productDetailPage.widgets.push(this.getAmenitiesWidget(gymfitCenter))
        }
        productDetailPage.widgets.push(this.getHowItWorksWidget(gymfitCenterPageConfig))
        return productDetailPage
    }


    private getAction(selectedStartDate: string, gymfitCenter: GymfitCenter, selectedPlan: OfflineFitnessPack, offerItem: PackOfferItem): Action {
        if (!selectedStartDate) {
            return {
                title: "Pick Start Date",
                actionType: "SHOW_DATE_PICKER"
            }
        } else {
            const orderProduct: OrderProduct = {
                productId: selectedPlan.productId,
                option: {
                    centerId: gymfitCenter.id,
                    startDate: selectedStartDate,
                    offerId: offerItem.offers.length > 0 ? offerItem.offers[0].offerId : null
                },
                quantity: 1
            }
            return {
                title: "Buy Now",
                actionType: "GET_GYMFIT_PACK",
                meta: {
                    orderProduct: orderProduct,
                    useFitCash: true
                }
            }
        }
    }


    private getSummaryWidget(userContext: UserContext, packOffersV3: GymFitProductPricesResponse, gymfitCenter: GymfitCenter, gymfitCenterProducts: OfflineFitnessPack[],
                             selectedPlanId: string, selectedStartDate: string, selectedPlan: OfflineFitnessPack, offerItem: PackOfferItem): WidgetView {
        const images = AppUtil.isDesktop(userContext) ? (gymfitCenter.imageUrls ? gymfitCenter.imageUrls.heroWebImageUrls || [] : []) :
            (gymfitCenter.imageUrls ? gymfitCenter.imageUrls.heroImageUrls || [] : [])

        const summaryWidget: WidgetView & {
            title: string,
            images: string[],
            widgets: WidgetView[]
            centerId: string,
            pageActions?: Action[],
            hideSepratorLines: boolean
        } = {
            widgetType: "GYM_PACK_SUMMARY",
            centerId: gymfitCenter.id + "",
            images: images,
            title: "",
            widgets: [],
            pageActions: [this.getAction(selectedStartDate, gymfitCenter, selectedPlan, offerItem)],
            hideSepratorLines: false
        }

        const centerNamePrefix = (gymfitCenter.seller && gymfitCenter.seller.brand) ? gymfitCenter.seller.brand.name + " - " : ""
        summaryWidget.title = centerNamePrefix + gymfitCenter.name
        summaryWidget.widgets.push(this.getSelectorWidget(packOffersV3, gymfitCenterProducts, selectedPlanId))
        const startDateOptions = this.getStartDateOptions(userContext)
        summaryWidget.widgets.push(this.getStartDateWidget(userContext, startDateOptions.startDate, startDateOptions.canChangeStartDate, selectedStartDate))
        return summaryWidget
    }

    private getSelectorWidget(packOffersV3: GymFitProductPricesResponse, gymfitCenterProducts: OfflineFitnessPack[], selectedPlanId: string): WidgetView {
        const selectorWidget: WidgetView & {
            plans: GymfitPlan[],
            selectedPlan: string,
            title: string
        } = {
            widgetType: "GYM_PLAN_SELECTOR_WIDGET",
            plans: [],
            selectedPlan: "",
            title: "SELECT MEMBERSHIP"
        }
        selectorWidget.selectedPlan = this.getSelectedPlanId(selectedPlanId, gymfitCenterProducts)

        for (const gymfitCenterProduct of gymfitCenterProducts) {
            const offerItem = CultUtil.getOfferItem(packOffersV3, gymfitCenterProduct.productId)
            const price: ProductPrice = offerItem.product.price
            selectorWidget.plans.push({
                title: gymfitCenterProduct.title,
                productId: gymfitCenterProduct.productId,
                price: {
                    mrp: price.mrp,
                    listingPrice: price.listingPrice,
                    currency: price.currency
                },
                offerIds: offerItem.offers.map(offer => offer.offerId)
            })
        }
        return selectorWidget
    }

    private getStartDateOptions(userContext: UserContext) {
        const tz = userContext.userProfile.timezone
        const dates = TimeUtil.getDaysFrom(tz, TimeUtil.todaysDate(tz), 2)
        let startDate: string = ""
        if (dates.length === 2) {
            startDate = dates[1]
        }

        const futureDate: momentTz.Moment = TimeUtil.getDefaultMomentForDateString("2020-04-16", tz)
        if (futureDate.isAfter(TimeUtil.getDefaultMomentForDateString(startDate, tz))) {
            startDate = futureDate.format(TimeUtil.DEFAULT_DATE_FORMAT)
        }
        return { startDate, canChangeStartDate: true }
    }

    private getStartDateWidget(userContext: UserContext, startDate: string, canChangeStartDate: boolean, selectedStartDate: string): DatePickerWidget {
        let endDate
        const tz = userContext.userProfile.timezone
        if (canChangeStartDate) {
            const allowedDates = TimeUtil.getDaysFrom(tz, startDate, 30, false)
            endDate = allowedDates[allowedDates.length - 1]
        } else {
            endDate = startDate
        }
        const datePickerWidget: DatePickerWidget = {
            startDate: startDate,
            endDate: endDate,
            selectedDate: selectedStartDate ? selectedStartDate : startDate,
            canChangeStartDate: canChangeStartDate,
            widgetType: "DATE_PICKER_WIDGET"
        }
        return datePickerWidget
    }

    private getAddressWidget(gymfitCenter: GymfitCenter): CenterAddressWidget {
        const address = gymfitCenter.address
        const city = address.city ? address.city.name : address.cityId
        const state = address.city && address.city.state ? address.city.state : ""
        const country = address.city ? (address.city.country ? address.city.country.name : address.city.countryId) : ""
        const addressLine = (address.addressLine1 ? address.addressLine1 + ", " : "") + (address.addressLine2 ? address.addressLine2 : "")
        const addressText = `${addressLine} (${city}, ${state} ${address.pincode}, ${country})`
        const addressWidget: CenterAddressWidget = {
            widgetType: "CENTER_ADDRESS_WIDGET",
            header: {
                title: "Address",
            },
            mapUrl: gymfitCenter.address.mapUrl,
            latLong: {
                lat: gymfitCenter.address.latitude,
                long: gymfitCenter.address.longitude
            },
            addressText: addressText
        }
        return addressWidget
    }

    private getAmenitiesWidget(gymfitCenter: GymfitCenter): ProductGridWidget {
        const header: Header = {
            title: "Amenities"
        }
        const amenities: InfoCard[] = []
        gymfitCenter.amenities.forEach(amenity => {
            amenities.push({
                title: amenity.name,
                image: amenity.imageUrl
            })
        })
        const amenitiesWidget: ProductGridWidget = new ProductGridWidget("ICON", header, amenities)
        return amenitiesWidget
    }

    private getHowItWorksWidget(gymfitCenterPageConfig: GymfitCenterPageConfig): ProductListWidget {

        try {
            const header: Header = {
                title: gymfitCenterPageConfig.howItWorksTitle
            }

            const infoCards: InfoCard[] = []
            const howItWorksItemList = gymfitCenterPageConfig.howItWorksItemList
            howItWorksItemList.forEach(item => {
                infoCards.push({
                    subTitle: item.text,
                    icon: item.icon
                })
            })
            return new ProductListWidget("SMALL", header, infoCards)
        }
        catch (e) {
            return undefined
        }
    }

    private getIncludedInMembershipWidget(gymfitProduct: OfflineFitnessPack): ProductListWidget {
        const productListWidget: ProductListWidget = {
            header: {
                title: "Included in Membership"
            },
            hideSepratorLines: false,
            items: [],
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "MEDIUM_CARD"
        }
        productListWidget.items = [{
                title: DEFAULT_GYMFIT_PRODUCT_SERVICE.name,
                subTitle: DEFAULT_GYMFIT_PRODUCT_SERVICE.description,
                image: DEFAULT_GYMFIT_PRODUCT_SERVICE.imageUrls.listingImageUrl
            }] as InfoCard[]
        return productListWidget
    }

    private getAvailableAtGymWidget(gymfitCenter: GymfitCenter, selectedPlan: OfflineFitnessPack): ProductListWidget {
        const productListWidget: ProductListWidget = {
            header: {
                title: "Available at Gym"
            },
            hideSepratorLines: false,
            items: [],
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "MEDIUM_CARD"
        }

        const filteredServices = _.differenceWith(gymfitCenter.services, [DEFAULT_GYMFIT_PRODUCT_SERVICE], (s1, s2) => s1.id === s2.id)

        productListWidget.items = _.map(filteredServices, service => {
            const infocard: InfoCard = {
                title: service.name,
                subTitle: service.description,
                image: service.imageUrl
            }
            return infocard
        })
        return productListWidget
    }

    private getSelectedPlanId(selectedPlanId: string, gymfitCenterProducts: OfflineFitnessPack[]): string {
        return selectedPlanId ? selectedPlanId : (gymfitCenterProducts.length ? gymfitCenterProducts[0].productId : "")
    }
}

interface GymfitPlan {
    title: string
    productId: string
    price: {
        mrp: number
        listingPrice: number
        currency: Currency
    },
    offerIds: string[]
}
