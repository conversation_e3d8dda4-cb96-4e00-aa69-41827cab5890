import { GymfitLocality, GymfitStatus } from "@curefit/gymfit-common"
import { UserContext } from "@curefit/userinfo-common"
import { Action } from "@curefit/apps-common"
import { inject, injectable } from "inversify"
import { GymLocalitySelector } from "../common/views/WidgetView"
import _ = require("lodash")
import GymfitUtil from "../util/GymfitUtil"
import { LocalityResponse } from "@curefit/center-service-common"

@injectable()
export default class GymLocalitySelectorViewBuilder {

    buildView(userContext: UserContext, gymLocalities: LocalityResponse[], preferenceLocality: string, defaultLocality?: string): GymLocalitySelector {
        const localities: {
            title: string
            localityId: number
            isSelected: boolean
            action?: Action
          }[] = gymLocalities.map(locality => {
            if (_.isNil(locality)) {
                return undefined
            }
            return {
                title: locality.name,
                localityId: locality.id,
                isSelected: preferenceLocality === locality.name,
                action: {
                    actionType: "UPDATE_GYM_LOCALITY_PREFERENCE",
                    meta: {
                        locality: locality.name
                    }
                }
            }
        })
        localities.filter(locality => locality)
        let onDismissAction: Action = undefined
        if (!preferenceLocality && (!userContext.sessionInfo.lat || !userContext.sessionInfo.lon) && defaultLocality) {
            onDismissAction = {
                actionType: "UPDATE_GYM_LOCALITY_PREFERENCE",
                meta: {
                    locality: defaultLocality
                }
            }
        }
        return {
            header: {
                title: "Select Location",
                subTitle: "To see gyms near you"
            },
            gpsSearch: {
                title: "PICK MY LOCATION",
                icon: "",
                action: {
                    actionType: "UPDATE_GYM_LOCALITY_PREFERENCE",
                    meta: {
                        locality: GymfitUtil.LOCALITY_SEARCH_KEY
                    }
                }
            },
            searchBar: {
                title: "Search locality",
                icon: ""
            },
            localities,
            onDismissAction,
        }
    }
}