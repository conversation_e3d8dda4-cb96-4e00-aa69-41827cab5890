import { inject, injectable } from "inversify"
import {
    CenterSchedule,
    ExpandableHeaderWidget,
    OrderSuccessWidget,
    WidgetView
} from "../common/views/WidgetView"
import { GymfitCenter, GymFitCenterSchedule, GymfitCenterType, GymfitCheckIn, GymfitCheckInClassification } from "@curefit/gymfit-common"
import { BaseAddress } from "@curefit/location-common"
import GymfitUtil, { ELITE_CENTER_TAG, PRO_CENTER_TAG } from "../util/GymfitUtil"
import { Action } from "@curefit/apps-common"
import { UserContext } from "@curefit/userinfo-common"
import { HeaderWidget } from "@curefit/apps-common"
import { IGymfitBusiness } from "./GymfitBusiness"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import AppUtil from "../util/AppUtil"
import { HamletBusiness } from "@curefit/hamlet-node-sdk"
import _ = require("lodash")
import { SOFTBOOKING_MODAL_PLACEHOLDER_IMAGE, SOFTBOOKING_MODAL_PLACEHOLDER_IMAGE_LUX } from "../util/CultUtil"
import { CENTER_SERVICE_CLIENT_TYPES, ICenterScheduleService } from "@curefit/center-service-client"
import { TimeUtil } from "@curefit/util-common"
import { IUserSegmentClient, SEGMENTATION_CLIENT_TYPES } from "@curefit/segmentation-service-client"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { ISegmentService } from "@curefit/vm-models"

const SUCCESS_TICK_ICON = "/image/gymfit/success_tick.png"
const WEIGHT_ICON = "/image/gymfit/weight1.png"
const GEAR_ICON = "/image/gymfit/gear.png"
const SAFETY_MEASURES_ICON = "/image/gymfit/safety+checks.png"
const PEOPLE_ICON = "/image/gymfit/people1.png"
const GYM_ICON = "/image/gymfit/gym_icon1.png"
const BOOKING_SLOT = "/image/gymfit/booking_slot.png"
const WALKIN = "/image/gymfit/walkin.png"
const BOOKING_SLOT_AURORA_ICON_NAME = "Schedule"
const WALKIN_AURORA_ICON_NAME = "Fitness"


@injectable()
export default class SoftBookingBuilder {

    constructor(
        @inject(CUREFIT_API_TYPES.GymfitBusiness) private gymfitBusiness: IGymfitBusiness,
        @inject(CENTER_SERVICE_CLIENT_TYPES.CenterScheduleService) private centerScheduleService: ICenterScheduleService,
    ) { }

    async buildView(userContext: UserContext, center: GymfitCenter, checkin: GymfitCheckIn, centerSchedule: GymFitCenterSchedule[]): Promise<WidgetView[]> {
        return [
            this.getSuccessWidget(),
            this.getCheckInWidget(userContext, checkin),
            await this.getGymScehduleDetails(center, centerSchedule, userContext),
            this.whatYouGetWidget(),
        ]
    }

    static getBookingAction(title: string, centerId: string): Action {
        return {
            actionType: "SHOW_GYM_CHECKIN_MODAL",
            title: title,
            meta: {
                centerId,
                checkInClassification: GymfitCheckInClassification.SOFTBOOKING
            }
        }
    }

    getSuccessWidget() {
        const widget: OrderSuccessWidget = {
            widgetType: "ORDER_SUCCESS_WIDGET",
            icon: SUCCESS_TICK_ICON,
            title: "We are looking forward to your visit in the next 48 hrs.",
            gradientColors: ["#4ab74a", "#4ab74a"],
            containerStyle: {
                marginHorizontal: 20,
                marginTop: 25,
            },
            iconContainerStyle: {
                backgroundColor: "#4ab74a",
                width: 80,
                height: 80,
            },
            iconStyle: {
                width: 34,
                height: 34,
            },
            titleStyle: {
                fontSize: 16,
                lineHeight: 24,
                color: "#333747",
                marginTop: 20,
                marginHorizontal: 45,
            }
        }
        return widget
    }

    async getGymScehduleDetails(center: GymfitCenter, centerSchedule: GymFitCenterSchedule[], userContext: UserContext) {
        const scheduleTimes = GymfitUtil.getScheduleTimes(centerSchedule)
        const schedules = GymfitUtil.mergeSameDaySchedule(scheduleTimes)
        const todaysSchedule = GymfitUtil.getTodaysSchedule(scheduleTimes)
        const isGymOpen = GymfitUtil.isGymOpen(todaysSchedule, center)
        const scheduleDetails = await this.centerScheduleService.getCenterScheduleByCenterId(center.centerServiceId)
        const mergedScheduleDetails = GymfitUtil.mergeTimingsForSameDay(scheduleDetails)
        const enquiryBugCheck = AppUtil.enquiryBugCheck(userContext)

        const widget: ExpandableHeaderWidget = {
            widgetType: "EXPANDABLE_HEADER_WIDGET",
            showAddExerciseButton: false,
            layoutProps: {
                isOpen: true,
            },
            header: {
                title: {
                    text: "Opening Hours",
                    style: {},
                },
                subTitle: {
                    text: isGymOpen ? "Open" : "Closed Now",
                    style: {
                        color: isGymOpen ? "#50d166" : "#e05343"
                    }
                },
            },
            widgets: [
                {
                    widgetType: "PRODUCT_LIST_WIDGET",
                    type: "CUSTOM_ROW",
                    noBottomPadding: true,
                    items: schedules.map(schedule => ({
                        text: schedule.title,
                        subText: schedule.value,
                        style: {
                            marginHorizontal: 20,
                            marginTop: 20,
                            marginBottom: 0,
                        },
                        textStyle: {
                            color: "#333747",
                            fontFamily: "BrandonText-Medium",
                            fontSize: 16,
                            lineHeight: 24,
                        },
                    }))
                },
            ],
        }
        const widgetV1: ExpandableHeaderWidget = {
            widgetType: "EXPANDABLE_HEADER_WIDGET",
            showAddExerciseButton: false,
            layoutProps: {
                isOpen: true,
            },
            header: {
                title: {
                    text: "Opening Hours",
                    style: {},
                },
                subTitle: {
                    text: isGymOpen ? "Open" : "Closed Now",
                    style: {
                        color: isGymOpen ? "#50d166" : "#e05343"
                    }
                },
            },
            widgets: [
                {
                    widgetType: "PRODUCT_LIST_WIDGET",
                    type: "CUSTOM_ROW",
                    noBottomPadding: true,
                    items: mergedScheduleDetails.map(schedule => {
                        const openingTimesArray = schedule.openingTime.split(",")
                        const closingTimeArray = schedule.closingTime.split(",")
                        const operationModeArray = schedule.operationMode.split(",")
                        const subTextArray = []
                        for (let i = 0; i < openingTimesArray.length; i++) {
                            const openingTimeSplit = openingTimesArray[i].split(":")
                            const closingTimeSplit = closingTimeArray[i].split(":")
                            subTextArray.push(`${TimeUtil.get12HourTimeFormat(parseInt(openingTimeSplit[0]), parseInt(openingTimeSplit[1]), true)} - ${TimeUtil.get12HourTimeFormat(parseInt(closingTimeSplit[0]), parseInt(closingTimeSplit[1]), true)}`)
                        }
                        const operationModes = []
                        for (let i = 0; i < operationModeArray.length; i++) {
                            if (operationModeArray[i] === "NORMAL") {
                                operationModes.push("")
                            } else if (operationModeArray[i] === "ENQUIRY_ONLY") {
                                operationModes.push("• Open only for enquiries")
                            }
                        }
                        return enquiryBugCheck ? {
                            title: schedule.dayOfWeek,
                            subTextArray: subTextArray,
                            operationMode: operationModes,
                            style: {},
                            textStyle: {
                                color: "#333747",
                                fontFamily: "BrandonText-Medium",
                                fontSize: 16,
                                lineHeight: 24,
                            },
                        } : {
                            text: schedule.dayOfWeek,
                            subText: subTextArray,
                            operationMode: operationModes,
                            style: {
                                marginHorizontal: 20,
                                marginTop: 20,
                                marginBottom: 0,
                            },
                            textStyle: {
                                color: "#333747",
                                fontFamily: "BrandonText-Medium",
                                fontSize: 16,
                                lineHeight: 24,
                            },
                        }
                    })
                },
            ],

        }
        if (AppUtil.isOpenForEnquiryWidgetSupported(userContext)) {
            return widgetV1
        }
        return widget
    }

    getSafetyMeasuresWidget() {
        const widget: ExpandableHeaderWidget = {
            widgetType: "EXPANDABLE_HEADER_WIDGET",
            header: {
                title: {
                    text: "Safety Measures",
                    style: {},
                },
            },
            widgets: [
                {
                    widgetType: "PRODUCT_LIST_WIDGET",
                    type: "SMALL",
                    hideSepratorLines: true,
                    noBottomPadding: true,
                    items: [
                        {
                            title: "Checks at Entry",
                            icon: SAFETY_MEASURES_ICON,
                            subTitle: "Oximeter, Temperature, Aarogya Setu App, Mask",
                            cellStyle: {
                                paddingLeft: 0,
                                paddingTop: 0,
                                paddingBottom: 0,
                                marginTop: 20,
                                marginHorizontal: 20,
                            },
                            subtitleStyle: {
                                color: "#888e9e",
                                lineHeight: 18,
                                fontFamily: "BrandonText-Regular",
                                fontSize: 13,
                                marginTop: 4,
                            },
                            layoutProps: {
                                iconStyle: {
                                    height: 34,
                                    width: 34,
                                },
                                titleStyle: {
                                    fontFamily: "BrandonText-Medium",
                                    fontSize: 16,
                                    lineHeight: 24,
                                    color: "#333747",
                                },
                            },
                        },
                        {
                            title: "Bring your Gear",
                            icon: GEAR_ICON,
                            subTitle: "Phone, Workout Shoes, Water Bottle, Yoga Mat, Towel, Face Shield",
                            cellStyle: {
                                paddingLeft: 0,
                                paddingTop: 0,
                                paddingBottom: 0,
                                marginTop: 18,
                                marginHorizontal: 20,
                            },
                            subtitleStyle: {
                                color: "#888e9e",
                                lineHeight: 18,
                                fontFamily: "BrandonText-Regular",
                                fontSize: 13,
                                marginTop: 4,
                            },
                            layoutProps: {
                                iconStyle: {
                                    height: 34,
                                    width: 34,
                                },
                                titleStyle: {
                                    fontFamily: "BrandonText-Medium",
                                    fontSize: 16,
                                    lineHeight: 24,
                                    color: "#333747",
                                },
                            },
                        },
                    ],
                },
            ],
        }
        return widget
    }

    whatYouGetWidget() {
        const widget: ExpandableHeaderWidget = {
            widgetType: "EXPANDABLE_HEADER_WIDGET",
            showAddExerciseButton: false,
            header: {
                title: { text: "What you get", color: "" },
            },
            widgets: [
                {
                    widgetType: "HORIZONTAL_INFO_IMAGE_WIDGET",
                    isScrollable: true,
                    layoutProps: {
                        height: 146,
                        width: 104,
                    },
                    items: [
                        {
                            icon: PEOPLE_ICON,
                            subTitle: "Guided Gym Tour",
                            action: {
                                actionType: "NAVIGATION",
                                url: ""
                            }
                        },
                        {
                            icon: GYM_ICON,
                            subTitle: "Workout Guidance",
                            action: {
                                actionType: "NAVIGATION",
                                url: ""
                            }
                        },
                        {
                            icon: WEIGHT_ICON,
                            subTitle: "Free Body Fat Check",
                            action: {
                                actionType: "NAVIGATION",
                                url: ""
                            }
                        },
                    ],
                },
            ],
        }
        return widget
    }

    getCheckInWidget(userContext: UserContext, checkin: GymfitCheckIn) {
        let moreAction = GymfitUtil.getMarkAttendanceAction(userContext.userProfile.timezone, checkin)
        if (moreAction) {
            moreAction = {
                ...moreAction,
                analyticsData: {
                    eventData: {
                        pageName: "cultpass_soft_booking_confirmation"
                    }
                }
            }
        }
        const widget: HeaderWidget = {
            widgetType: "HEADER_WIDGET",
            widgetTitle: {
                title: "Already at the Gym?",
                subTitle: null,
            },
            style: {
                marginVertical: 40,
                marginHorizontal: 20,
            },
            titleStyle: {
                fontSize: 18,
                marginLeft: 0,
                fontFamily: "BrandonText-Bold",
            },
            moreAction,
        }
        return widget
    }

    async getSoftBookingPopupView(userContext: UserContext, gymfitCenter: GymfitCenter, centerSchedule: GymFitCenterSchedule[], hamletService: HamletBusiness, segmentService: ISegmentService): Promise<WidgetView[]> {
        const scheduleTimes = GymfitUtil.getScheduleTimes(centerSchedule)
        const todaysSchedule = GymfitUtil.getTodaysSchedule(scheduleTimes)
        const isGymOpen = GymfitUtil.isGymOpen(todaysSchedule, gymfitCenter)
        const widgets: WidgetView[] = []
        const isFlutterRequest = userContext.sessionInfo.appSource === "flutter"
        const isNewAppUtil = isFlutterRequest ? AppUtil.isFlutterAllCentersPageEnabled(userContext) : false
        if (isNewAppUtil) {
            widgets.push(this.getHeaderDescriptionWidgetV2(gymfitCenter, todaysSchedule, isGymOpen, userContext, segmentService))
            const holidayWidget = this.gymHolidayListWidget(userContext, gymfitCenter)
            if (holidayWidget) {
                widgets.push(holidayWidget)
            }
            widgets.push(...this.getSoftBookingCheckInOptionsWidgetV2(userContext, gymfitCenter))
        } else {
            widgets.push(this.getHeaderDescriptionWidget(gymfitCenter, todaysSchedule, isGymOpen))
            const holidayWidget = this.gymfitBusiness.getNextGymHolidayWidget(userContext, gymfitCenter)
            if (holidayWidget) {
                widgets.push(holidayWidget)
            }
            widgets.push(this.getSoftBookingCheckInOptionsWidget(userContext, gymfitCenter))
        }
        return widgets
    }

    private gymHolidayListWidget(userContext: UserContext, gymfitCenter: GymfitCenter): WidgetView {
        const warning: string[] = []
        gymfitCenter.effectiveHolidays?.sort((a, b) => a.startTime < b.startTime ? -1 : 1).splice(0, 1).map(holiday => {
            warning.push(
                GymfitUtil.getGymHolidayMessage(userContext, gymfitCenter, holiday),
            )
        })
        if (_.isEmpty(warning)) { return null }
        return {
            "widgetType": "CENTER_WARNING_TEXT_WIDGET",
            "warning": warning
        }
    }

    private getHeaderDescriptionWidget(gymfitCenter: GymfitCenter, todaysSchedule: CenterSchedule, isGymOpen: boolean): WidgetView {
        return {
            "widgetType": "HEADER_DESCRIPTION_WIDGET",
            "title": gymfitCenter.name,
            "subTitle": this.getAddressInfo(gymfitCenter.address, gymfitCenter),
            "description": {
                "tag": {
                    text: isGymOpen ? "Open" : "Closed Now",
                    color: isGymOpen ? "#50d166" : "#e05343"
                },
                "text": todaysSchedule?.value,
            }
        }
    }

    private getHeaderDescriptionWidgetV2(gymfitCenter: GymfitCenter, todaysSchedule: CenterSchedule, isGymOpen: boolean, userContext: UserContext, segmentService: ISegmentService): WidgetView {
        const softBookingPlaceHolderImage: string = gymfitCenter?.type === GymfitCenterType.LUX ? SOFTBOOKING_MODAL_PLACEHOLDER_IMAGE_LUX : SOFTBOOKING_MODAL_PLACEHOLDER_IMAGE
        const imageUrl = !_.isNil(gymfitCenter.imageUrls) && !_.isEmpty(gymfitCenter.imageUrls.heroImageUrls) ? gymfitCenter.imageUrls.heroImageUrls[0] : softBookingPlaceHolderImage
        let tagUrlV2: string = null
        if (gymfitCenter?.type === GymfitCenterType.LUX) {
            tagUrlV2 = "/image/luxury/luxury_cardv3.svg"

        } else if (gymfitCenter?.type === GymfitCenterType.BLACK) {
            tagUrlV2 = "/image/icons/cult/elite_tag_v2.svg"
        } else {
            tagUrlV2 = "/image/icons/cult/pro_tag_v2.svg"
        }
        let tagIcon: string = null
        let tagTitle: string = null

        AppUtil.isCenterLevelPricingSupported(userContext, segmentService).then(flag => {
            if (flag) {
                if (gymfitCenter?.type === GymfitCenterType.GOLD) {
                    tagIcon = "/image/cl_pricing/pro_star.svg"
                    tagTitle = "PRO GYM"
                    tagUrlV2 = null
                } else if (gymfitCenter?.type === GymfitCenterType.BLACK) {
                    tagIcon = "/image/cl_pricing/elite_star.svg"
                    tagTitle = "ELITE GYM"
                    tagUrlV2 = null
                }
            }
        })

        return {
            "widgetType": "CENTER_DETAILS_LIST_VIEW_WIDGET",
            "title": gymfitCenter.name,
            "subTitle": this.getAddressInfo(gymfitCenter.address, gymfitCenter),
            "imageUrl": imageUrl,
            "tagUrl": gymfitCenter?.type === GymfitCenterType.BLACK ? ELITE_CENTER_TAG : PRO_CENTER_TAG,
            "tagUrlV2": tagUrlV2,
            "isV2Tag": true,
            "imageSize": 85,
            "tagTitle": tagTitle,
            "tagIcon": tagIcon,
            "headingList": [
                {
                    text: isGymOpen ? "OPEN" : "CLOSED NOW",
                    color: isGymOpen ? "50d166" : "e05343"
                },
                {
                    "text": todaysSchedule?.value,
                }
            ]
        }
    }

    private getSoftBookingCheckInOptionsWidget(userContext: UserContext, gymfitCenter: GymfitCenter): WidgetView {
        return {
            "widgetType": "HORIZONTAL_INFO_IMAGE_WIDGET",
            "isScrollable": false,
            "isCenterAligned": true,
            "layoutProps": {
                "height": 176
            },
            "items": [
                {
                    "icon": WALKIN,
                    "title": "Anytime Walk-In",
                    "subTitle": "Valid for next 48 hours",
                    "action": {
                        "actionType": "GYMFIT_CHECKIN",
                        "url": "curefit://orderconfirmationv1?pageName=soft_booking_confirmed_cultpass&checkInClassification=SOFTBOOKING",
                        "meta": {
                            centerId: gymfitCenter.id,
                            checkInClassification: GymfitCheckInClassification.SOFTBOOKING,
                            centerOfferingId: gymfitCenter.offerings[0]?.id
                        },
                    },
                },
                {
                    "icon": BOOKING_SLOT,
                    "title": "Book Slot",
                    "subTitle": "Recommended during peak hours",
                    "action": GymfitUtil.getSlotBookingAction(userContext, Number(gymfitCenter.id))
                }
            ]
        }
    }

    private getSoftBookingCheckInOptionsWidgetV2(userContext: UserContext, gymfitCenter: GymfitCenter): WidgetView[] {
        const widgetList: WidgetView[] = []
        widgetList.push({
            "widgetType": "CENTER_ACTION_LIST_VIEW_WIDGET",
            "icon": WALKIN_AURORA_ICON_NAME,
            "title": "Anytime Walk-In",
            "subTitle": "Valid for next 48 hours",
            "isV2Tag": true,
            "action": {
                "actionType": "GYMFIT_CHECKIN",
                "url": "curefit://orderconfirmationv1?pageName=soft_booking_confirmed_cultpass&checkInClassification=SOFTBOOKING",
                "meta": {
                    centerId: gymfitCenter.id,
                    checkInClassification: GymfitCheckInClassification.SOFTBOOKING,
                    centerOfferingId: gymfitCenter.offerings[0]?.id
                },
            }
        })
        widgetList.push({
            "widgetType": "CENTER_ACTION_LIST_VIEW_WIDGET",
            "icon": BOOKING_SLOT_AURORA_ICON_NAME,
            "title": "Book Slot",
            "subTitle": "Recommended during peak hours",
            "isV2Tag": true,
            "action": GymfitUtil.getSlotBookingAction(userContext, Number(gymfitCenter.id))
        })
        return widgetList
    }


    getAddressInfo(address: BaseAddress, gymfitCenter: GymfitCenter) {
        let gymAddress = gymfitCenter.locality
        if (address.addressLine2) {
            gymAddress = address.addressLine2
        }
        const gymDistance = GymfitUtil.getFormattedGymDistance(gymfitCenter.distanceFromSearchOrigin)
        return `${gymAddress} ${gymDistance ? ` • ${gymDistance}` : ""}`
        // return `${(address?.addressLine1 ? address.addressLine1 + " • " : "")}${(address?.addressLine2 ? address.addressLine2 : "")}`
    }
}
