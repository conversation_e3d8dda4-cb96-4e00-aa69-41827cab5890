import { inject, injectable } from "inversify"
import { ActivityCategory, GymfitCenter, GymFitCenterSchedule, GymfitCheckIn, GymfitEvent, GymSchedule, MediaType } from "@curefit/gymfit-common"
import {
    Action,
    BrowseClassListWidget, ClassInCenter,
    ClassScheduleContainer,
    ClassScheduleContainerWidget,
    ClassTimeListWidget,
    WidgetView
} from "@curefit/apps-common"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { UserContext } from "@curefit/userinfo-common"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { IGymfitBusiness } from "./GymfitBusiness"
import AppUtil from "../util/AppUtil"
import GymfitUtil, { ONEPASS_GYM_ACTIVITY_ID } from "../util/GymfitUtil"
import * as _ from "lodash"
import { CareDatePickerView, PageWidget } from "../page/Page"
import { CalloutPageWidget, DatesAvailableWidget, DateWiseSlots, TimeSlot, TimeSlotCategory } from "../page/PageWidgets"
import CareUtil from "../util/CareUtil"
import { CareCenterHeaderView, CareCenterPageView, CareCenterView } from "../care/CareCenterViewBuilder"
import { Center } from "@curefit/care-common"
import { CenterScheduleResponse } from "@curefit/center-service-common"
import { Membership } from "@curefit/membership-commons"
import CultUtil from "../util/CultUtil"

export const MONTHS_LIST = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
const DAYS_LIST = ["SUNDAY", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY"]

@injectable()
export default class GymfitClassScheduleViewBuilder {

    constructor(
        @inject(CUREFIT_API_TYPES.GymfitBusiness) private gymfitBusiness: IGymfitBusiness,
    ) {}

    getView(userContext: UserContext, gymSchedule: GymSchedule, center: GymfitCenter): ClassScheduleContainer {
        const widgets: WidgetView[] = []
        const workoutFilters = this.getClassFilters(gymSchedule.activityCategories)
        const days = this.getDays(userContext.userProfile.timezone, gymSchedule.schedules)
        const classByDateList = this.getClassesByDateList(userContext, gymSchedule, center, days)
        const classScheduleContainerWidget: ClassScheduleContainerWidget = {
            widgetType: "CLASS_SCHEDULE_CONTAINER_WIDGET",
            days,
            workoutFilters,
            timeSlotOptions: GymfitUtil.getTimeSlotOptions(),
            classByDateList,
            emptyState: {
                text: "No classes available"
            }
        }
        if (AppUtil.isWeb(userContext)) {
            const imageMedia = center.media && center.media.clpMedia ? center.media.clpMedia.find((media) => media.type === MediaType.IMAGE) : undefined
            classScheduleContainerWidget.header = {
                image: imageMedia ? imageMedia.mediaUrl : undefined,
                title: center.name,
                widgetType: "HEADER"
            }
        }
        widgets.push(classScheduleContainerWidget)
        if (AppUtil.isWeb(userContext)) {
            widgets.push(GymfitUtil.getImportantUpdatesWidget(userContext))
        }
        return {
            header: {
                title: `${center.name}, ${center.locality}`
            },
            widgets
        }
    }

    getGroupedScheduleForSlotBookingView(gymSchedule: GymSchedule, timezone: Timezone, isOnePassPage?: boolean, isOnePassGymOnly?: boolean) {
        let finalSchedules = gymSchedule.schedules
        if (isOnePassPage) {
            finalSchedules = isOnePassGymOnly ? finalSchedules.filter(schedule => schedule.baseEvent.activityId === ONEPASS_GYM_ACTIVITY_ID)
                : finalSchedules.filter(schedule => schedule.baseEvent.activityId !== ONEPASS_GYM_ACTIVITY_ID)
        }
        return _.groupBy(finalSchedules, schedule => {
            const date = new Date(schedule.fromTimeUTC)
            const dateInTimezone = TimeUtil.parseDateUTC(TimeUtil.formatDateInTimeZone(timezone, date), timezone)
            return TimeUtil.formatDateInTimeZone(timezone, dateInTimezone)
        })
    }

    getNewSlotBookingView(userContext: UserContext, gymSchedule: GymSchedule, center: GymfitCenter, gymScheduleV2?: CenterScheduleResponse[], isOnePassPage?: boolean, isOnePassGymOnly?: boolean, isLimitedElitePack?: boolean, limitedEliteMembership?: Membership): CareDatePickerView {
        const timezone = userContext.userProfile.timezone
        const widgets: PageWidget[] = []
        const dates: string[] = TimeUtil.getDays(userContext.userProfile.timezone, 4)
        const dateEventsMap = this.getGroupedScheduleForSlotBookingView(gymSchedule, timezone, isOnePassPage, isOnePassGymOnly)
        const datesAvailable: DateWiseSlots[] = []
        for (let i = 0; i < dates.length ; i++) {
            const dateAppointments: GymfitEvent[] = dateEventsMap[dates[i]]
            // if (!_.isEmpty(dateAppointments)) {
            //     const timeslots: TimeSlotCategory[] = this.getHourSplitTimeSlots(dateAppointments, userContext, gymSchedule.userContext.checkIns)
            //     if (!_.isEmpty(timeslots)) {
            //         datesAvailable.push(this.getDatewiseSlot(
            //             dates[i],
            //             userContext.userProfile.timezone,
            //             timeslots,
            //             false,
            //             "DIAGNOSTICS")
            //         )
            //     }
            // }
            const timeslots: TimeSlotCategory[] = this.getHourSplitTimeSlots(dateAppointments, userContext, gymSchedule.userContext.checkIns, isOnePassPage, isOnePassGymOnly,
                isLimitedElitePack, limitedEliteMembership)
            let enquiryText = undefined
            if (_.isEmpty(timeslots)) {
                gymScheduleV2.map((schedule) => {
                    if (schedule.dayOfWeek === DAYS_LIST[new Date(dates[i]).getDay()]) {
                        if (schedule.operationMode === "ENQUIRY_ONLY") {
                            const openingTimeSplit = schedule.openingTime.split(":")
                            const closingTimeSplit = schedule.closingTime.split(":")
                            enquiryText = `No slots are available today for the workout. But the Gym is open for enquiries from ${TimeUtil.get12HourTimeFormat(parseInt(openingTimeSplit[0]), parseInt(openingTimeSplit[1]), true)} to ${TimeUtil.get12HourTimeFormat(parseInt(closingTimeSplit[0]), parseInt(closingTimeSplit[1]), true)}`
                        }
                    }
                })
            }
            datesAvailable.push(this.getDatewiseSlot(
                dates[i],
                userContext.userProfile.timezone,
                timeslots,
                "GYM",
                undefined,
                enquiryText)
            )
        }
        const action: Action = {
            actionType: "NAVIGATION",
            url: "",
            meta: {}
        }
        let sessionLeftText
        if (isLimitedElitePack) {
            sessionLeftText = CultUtil.getLimitedEliteSessionLeftText(limitedEliteMembership, userContext)
        }
        const dateWidget = new DatesAvailableWidget(datesAvailable, "", action)
        dateWidget.backgroundColor = "#f2f4f8"
        widgets.push(dateWidget)
        const title = `${center.name}, ${center.locality}`
        return {
            key: "GYM_WORKOUT",
            header: {
                title: title.length > 30 ? `${title.substring(0, 30)}...` : title,
                type: isOnePassPage ? "STATIC" : "CENTER_SELECTOR"
            },
            widgets,
            footerWidget: [this.getSlotTimeCalloutWidget(sessionLeftText)]
        }
    }

    getChangeCenterView(userContext: UserContext, gymCenters: GymfitCenter[], productId: string): CareCenterPageView {
        const header: CareCenterHeaderView = { title: "Select center"}
        const centerViews: CareCenterView[] = []
        gymCenters.forEach(center => {
            const action: Action = {
                actionType: "UPDATE_CARE_CENTER",
                meta: {
                    isGymCenter: true,
                    centerInfo: {
                        id: Number(center.id),
                        name: center.name
                    }
                }
            }
            const item = new CareCenterView({} as Center, action)
            item.id = Number(center.id)
            item.name = center.name
            item.address = {
                addressString: center.locality,
                latLong: {
                    lat: center.address.latitude,
                    long: center.address.longitude
                },
                pincode: center.code,
                mapUrl: center.address.mapUrl
            }
            item.mapUrl = center.address.mapUrl
            centerViews.push(item)
        })
        return new CareCenterPageView(header, centerViews)
    }

    private getClassFilters(activityCategories: ActivityCategory[]) {
        return activityCategories.map((activityCategory) => {
            return {
                id: activityCategory.id,
                name: activityCategory.name,
                displayText: activityCategory.name
            }
        })
    }

    private getDays(tz: Timezone, schedules: GymfitEvent[]) {
        const daysIncluded: string[] = []
        const days: {
            id: string,
            day: string,
            month: string
        }[] = []
        schedules.map((schedule) => {
            const date = new Date(schedule.fromTimeUTC)
            const dateInTimezone = TimeUtil.parseDateUTC(TimeUtil.formatDateInTimeZone(tz, date), tz)
            const dateText = TimeUtil.formatDateInTimeZone(tz, dateInTimezone)
            if (!daysIncluded.includes(dateText)) {
                daysIncluded.push(dateText)
                const splitDate = dateText.split("-")
                let month = ""
                if (splitDate.length === 3) {
                    month = MONTHS_LIST[parseInt(splitDate[1]) - 1]
                }
                days.push({
                    id: dateText,
                    day: TimeUtil.getDayText(dateText, tz, {
                        sameDay: "dddd",
                        nextDay: "dddd",
                        nextWeek: "dddd",
                        lastDay: "dddd",
                        lastWeek: "dddd",
                        sameElse: "dddd"
                    }),
                    month
                })
            }
        })
        return days
    }

    private getClassesByDateList(userContext: UserContext, gymSchedule: GymSchedule, center: GymfitCenter, days: { id: string, day: string, month: string }[]): BrowseClassListWidget[] {
        const tz = userContext.userProfile.timezone
        const browseClassListWidgets: BrowseClassListWidget[] = []
        days.map((day) => {
            browseClassListWidgets.push({
                widgetType: "BROWSE_CLASS_LIST",
                id: day.id,
                classByTimeList: []
            })
        })
        gymSchedule.schedules.map((gymfitEvent) => {
            const fromDate = new Date(gymfitEvent.fromTimeUTC)
            const toDate = new Date(gymfitEvent.toTimeUTC)
            const dateText = TimeUtil.formatDateInTimeZone(tz, fromDate, "YYYY-MM-DD")
            const classesOnDay = browseClassListWidgets.find((browseClassListWidget) => browseClassListWidget.id === dateText)
            if (classesOnDay) {
                const startTime = TimeUtil.get12HRTimeFormat(fromDate, tz, true)
                const classesForTime = classesOnDay.classByTimeList.find((browseClass) => browseClass.id === startTime)
                let classTimeListWidget: ClassInCenter[]
                if (classesForTime) {
                    classTimeListWidget = classesForTime.classes
                } else {
                    const newClassTimeListWidget: ClassTimeListWidget = {
                        id: startTime,
                        centerId: center.id,
                        centerName: center.name,
                        classes: []
                    }
                    classesOnDay.classByTimeList.push(newClassTimeListWidget)
                    classTimeListWidget = newClassTimeListWidget.classes
                }
                const state = this.getClassBookingState(gymSchedule, gymfitEvent)
                classTimeListWidget.push({
                    id: gymfitEvent.id,
                    productType: "GYMFIT_FITNESS_PRODUCT",
                    date: classesOnDay.id,
                    startTime,
                    endTime: TimeUtil.get12HRTimeFormat(toDate, tz, true),
                    workoutId: gymfitEvent.baseEvent.activity.activityCategoryId,
                    centerId: center.id,
                    workoutName: gymfitEvent.baseEvent.activity.name,
                    state,
                    action: this.getClassAction(userContext, state, center, gymfitEvent, gymSchedule)
                })
            }
        })
        return browseClassListWidgets
    }

    private getClassBookingState(gymSchedule: GymSchedule, gymfitEvent: GymfitEvent) {
        if (gymSchedule.userContext && gymSchedule.userContext.checkIns && gymSchedule.userContext.checkIns[gymfitEvent.id]) {
            return "BOOKED"
        } else if (gymfitEvent.usedCapacity < gymfitEvent.maxCapacity) {
            return "AVAILABLE"
        } else {
            return "SEAT_NOT_AVAILABLE"
        }
    }

    private getSlotState(gymCheckins: {[eventId: number]: GymfitCheckIn } = {}, gymfitEvent: GymfitEvent) {
        if (gymCheckins[gymfitEvent.id]) {
            return "BOOKED"
        } else if (gymfitEvent.usedCapacity < gymfitEvent.maxCapacity) {
            return "AVAILABLE"
        } else {
            return "SEAT_NOT_AVAILABLE"
        }
    }

    private getClassAction(userContext: UserContext, state: string, center: GymfitCenter, gymfitEvent: GymfitEvent, gymSchedule: GymSchedule): Action {
        if (state === "BOOKED") {
            const checkIn = gymSchedule.userContext.checkIns[gymfitEvent.id]
            return {
                actionType: "NAVIGATION",
                url: this.gymfitBusiness.getCheckInActionUrl(userContext, center, checkIn.id)
            }
        } else if (state === "AVAILABLE") {
            return {
                actionType: "SHOW_GYM_CHECKIN_MODAL",
                meta: {
                    centerId: center.id,
                    eventId: gymfitEvent.id
                }
            }
        }
        return undefined
    }

    private getLimitedEliteSlotAction(userContext: UserContext, state: string, center: GymfitCenter, gymfitEvent: GymfitEvent, gymCheckins: {[eventId: number]: GymfitCheckIn }, limitedEliteMembership?: Membership): Action {
        const isAwayCity = limitedEliteMembership.metadata.cityId !== userContext.userProfile.cityId
        const cultBenefit = limitedEliteMembership.benefits.find(a => a.name === (isAwayCity ? "CULT_AWAY" : "CULT"))
        if (cultBenefit.ticketsUsed >= cultBenefit.maxTickets && state !== "BOOKED") {
            return {
                actionType: "SHOW_ALERT_MODAL",
                title: "Invalid Data",
                meta: {
                    title: `Invalid Data`,
                    subTitle: `You have consumed all ${cultBenefit.maxTickets} session${cultBenefit.maxTickets > 1 ? "s" : ""} this month. You'll get ${cultBenefit.maxTickets} session${cultBenefit.maxTickets > 1 ? "s" : ""} next month`,
                    actions: [{actionType: "HIDE_ALERT_MODAL", title: "Ok"}]
                }
            }
        }
        return this.getSlotAction(userContext, state, center, gymfitEvent, gymCheckins)
    }

    private getSlotAction(userContext: UserContext, state: string, center: GymfitCenter, gymfitEvent: GymfitEvent, gymCheckins: {[eventId: number]: GymfitCheckIn }): Action {
        if (state === "BOOKED") {
            const checkIn = gymCheckins[gymfitEvent.id]
            return {
                actionType: "NAVIGATION",
                url: this.gymfitBusiness.getCheckInActionUrl(userContext, center, checkIn.id)
            }
        } else if (state === "AVAILABLE") {
            return {
                actionType: "SHOW_GYM_CHECKIN_MODAL",
                meta: {
                    centerId: center.id,
                    eventId: gymfitEvent.id
                }
            }
        }
        return undefined
    }

    private getActivitySlotMap(dateAppointments: GymfitEvent[], timezone: Timezone): { [key: string]: GymfitEvent[] } {
        return _.groupBy(dateAppointments, (x: GymfitEvent) => {
            return x.baseEvent?.activityId
        })
    }

    private getTimeSlotMap(dateAppointments: GymfitEvent[], timezone: Timezone): { [key: string]: GymfitEvent[] } | { [key: string]: GymfitEvent[] } {
        return _.groupBy(dateAppointments, (x: GymfitEvent) => {
            const startHour = Number(TimeUtil.formatEpochInTimeZone(timezone, x.fromTimeUTC, "HH"))
            if (startHour >= 0 && startHour < 3) {
                return "00:00-02:59"
            } else if (startHour >= 3 && startHour < 6) {
                return "03:00-05:59"
            } else if (startHour >= 6 && startHour < 9) {
                return "06:00-08:59"
            } else if (startHour >= 9 && startHour < 12) {
                return "09:00-11:59"
            } else if (startHour >= 12 && startHour < 15) {
                return "12:00-14:59"
            } else if (startHour >= 15 && startHour < 18) {
                return "15:00-17:59"
            } else if (startHour >= 18 && startHour < 21) {
                return "18:00-20:59"
            } else if (startHour >= 21 && startHour <= 23) {
                return "21:00-23:59"
            }
        })
    }

    getHourSplitTimeSlots(dateAppointments: GymfitEvent[], userContext: UserContext, gymCheckins: {[eventId: number]: GymfitCheckIn }, isOnePassPage?: boolean, isOnePassGymOnly?: boolean, isLimitedElitePack?: boolean, limitedEliteSessionMembership?: Membership ): TimeSlotCategory[] {
        const timeslots: TimeSlotCategory[] = []
        let timeSlotsMap: any
        if (isOnePassPage && !isOnePassGymOnly) {
            timeSlotsMap = <{[key: string]: GymfitEvent[] }>this.getActivitySlotMap(dateAppointments, userContext.userProfile.timezone)
        } else {
            timeSlotsMap = <{[key: string]: GymfitEvent[] }>this.getTimeSlotMap(dateAppointments, userContext.userProfile.timezone)
        }
        if (!_.isEmpty(timeSlotsMap)) {
            Object.keys(timeSlotsMap).sort().map(timeSlotKey => {
                if (!_.isEmpty(timeSlotsMap[timeSlotKey])) {
                    timeslots.push(this.parseToViewFormat(
                        timeSlotKey,
                        timeSlotsMap[timeSlotKey],
                        userContext,
                        gymCheckins,
                        isOnePassPage,
                        isOnePassGymOnly,
                        isLimitedElitePack,
                        limitedEliteSessionMembership
                    ))
                }
            })
        }
        return timeslots
    }

    private getTimeText(element: GymfitEvent, userContext: UserContext, isHomeSlot?: boolean) {
        let text
        if (element && element.fromTimeUTC) {
            text = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, element.fromTimeUTC, "hh:mm")
        }
        // Temp hack Adding 30 mins for home slots alone
        if (text && isHomeSlot && element.toTimeUTC) {
            text += " - " + TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, element.toTimeUTC, "hh:mm")
        }
        return text
    }

    private parseToViewFormat(timeSlotKey: string, slots: GymfitEvent[], userContext: UserContext, gymCheckins: {[eventId: number]: GymfitCheckIn }, isOnePassPage?: boolean, isOnePassGymOnly?: boolean, isLimitedElitePack?: boolean, limitedEliteMembership?: Membership): { timeSlots: TimeSlot[], title: string } {
        const timeSlots: TimeSlot[] = []
        const tz = userContext.userProfile.timezone
        slots.forEach(event => {
            const slotState = this.getSlotState(gymCheckins, event)
            let meta = undefined
            if (slotState === "BOOKED") {
                meta = { skipSlotBookedAlert: true }
            } else if (slotState === "SEAT_NOT_AVAILABLE") {
                meta = { alertMessage: "The selected slot is full" }
            }
            const timeslot: TimeSlot = {
                availableType: slotState === "SEAT_NOT_AVAILABLE" ? "UNAVAILABLE" : "AVAILABLE",
                bookedByPatient: !_.isEmpty(gymCheckins[event.id]),
                text: this.getTimeText(event, userContext),
                startTime: event.fromTimeUTC,
                endTime: event.toTimeUTC,
                showSelected: false,
                slotClubbingBulletColor: "#ff3278",
                action: isLimitedElitePack ? this.getLimitedEliteSlotAction(userContext, slotState, event.baseEvent.center, event, gymCheckins, limitedEliteMembership) : this.getSlotAction(userContext, slotState, event.baseEvent.center, event, gymCheckins),
                meta
            }
            timeSlots.push(timeslot)
        })
        return {
            timeSlots,
            title: (isOnePassPage && !isOnePassGymOnly) ? slots[0].baseEvent?.activity?.name : CareUtil.getTimeSlotTitleFromKey(timeSlotKey)
        }
    }

    private getDatewiseSlot(
        date: string,
        tz: Timezone,
        timeslots: TimeSlotCategory[],
        icon: string,
        imageUrl?: string,
        noSlotText?: string
    ): DateWiseSlots {
        return {
            date,
            dateType: TimeUtil.getMomentForDateString(date, tz).isoWeekday() >= 6 ? "WEEKEND" : "WEEKDAY",
            timeZones: timeslots,
            noSlotsAvailable: _.isEmpty(timeslots),
            noSlotText: noSlotText || "Sorry, No slots available today",
            icon,
            imageUrl
        }
    }

    private getSlotTimeCalloutWidget(subtitle?: string) {
        return new CalloutPageWidget("NOTE_VIEW", subtitle ?? "Each slot is valid for 90 minutes")
    }
}
