import { NowLiveWidget, IBaseWidget } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { UserContext } from "@curefit/userinfo-common"
import * as _ from "lodash"
import AppUtil from "../util/AppUtil"
import {
    DigitalCatalogueEntryV2,
    LiveFitWorkoutFormat,
    NowLiveClass,
    NowLiveClassesResponse, VideoStatus
} from "@curefit/diy-common"
import LiveUtil, { LIVE_SESSION_ACTION_SOURCE } from "../util/LiveUtil"
import { NowLiveSessionWidget } from "./NowLiveSessionWidget"
import { Buddy, WidgetHeader } from "@curefit/apps-common"
import { User } from "@curefit/user-common/dist/src/User"
import { Action } from "../common/views/WidgetView"
import { HourMin } from "@curefit/base-common"
import { TimeUtil } from "@curefit/util-common"
import { LivePackUtil } from "../util/LivePackUtil"

export class NowLiveWidgetViewBuilder extends NowLiveWidget {
    title: string
    widgets: NowLiveSessionWidget[]
    header: WidgetHeader

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams?: { [filterName: string]: string; }): Promise<IBaseWidget> {
        const countryId = AppUtil.getCountryId(userContext)
        const sessionInfo = userContext.sessionInfo
        const userId = userContext.userProfile.userId
        const user: User = await interfaces.userCache.getUser(userId)
        let formats: LiveFitWorkoutFormat[] = ["SNC", "STRENGTH", "CARDIO", "HRX", "BOXING", "DANCE", "YOGA", "MEDITATION", "MIND_PODCAST", "DANCE_FIT_JUNIOR", "BARRE", "TABATA", "PILATES", "HIIT", "RECOVERY", "WALK_FITNESS", "AMA"]
        if (!_.isEmpty(this.productType)) {
            if (this.productType === "MIND") {
                formats = ["YOGA", "MEDITATION"]
            } else if (this.productType === "DIY_MEDITATION") {
                formats = ["MEDITATION"]
            } else if (this.productType === "FITNESS") {
                formats = ["SNC", "STRENGTH", "CARDIO", "HRX", "BOXING", "DANCE", "YOGA", "DANCE_FIT_JUNIOR", "BARRE", "TABATA", "PILATES", "HIIT", "RECOVERY", "WALK_FITNESS", "AMA"]
            } else if (this.productType === "FOOD") {
                formats = ["EAT"]
            } else if (this.productType === "MIND_PODCAST") {
                formats = ["MIND_PODCAST"]
            } else if (this.productType === "HOBBY") {
                formats = ["HOBBY"]
            }
        }
        const nowLiveClassesResponse: NowLiveClassesResponse = await interfaces.diyService.getCurrentlyLiveClasses(userId, AppUtil.getTenantFromUserContext(userContext), formats, countryId)
        if (_.isEmpty(nowLiveClassesResponse) || _.isEmpty(nowLiveClassesResponse.nowLiveClasses)) {
            return undefined
        }
        const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(interfaces.cultBusiness, interfaces.diyService, userContext)
        this.title = "NOW LIVE"
        const sessionWidgets: NowLiveSessionWidget[] = []
        const isInternationalApp = AppUtil.isInternationalApp(userContext)
        const isLiveQnASupported = AppUtil.isLiveQnASupportedInNowLive(userContext)
        for (const nowLiveClass of nowLiveClassesResponse.nowLiveClasses) {
            const session = nowLiveClass.digitalCatalogue
            if (session?.preferredStreamType === "VIDEO_CALL" && !isLiveQnASupported) {
                continue
            }
            const sessionAction = LiveUtil.getLiveSessionDetailActionFromLiveClassId(session.originalContentId, session.classId, "now_live_widget", nowLiveClass.digitalCatalogue.contentCategory, userContext)
            const buddies: Buddy[] = _.isEmpty(nowLiveClass.socialData) || _.isEmpty(nowLiveClass.socialData.attendingFriendsUserIds) ? []
                : await this.getClassBuddyData(interfaces, nowLiveClass.socialData.attendingFriendsUserIds)
            const durationHourMin: HourMin = TimeUtil.convertDurationSecondsToHourMin(session.duration / 1000)
            let formattedTimeString: string = durationHourMin.min > 0 ? durationHourMin.min + " Min" : ""
            if (durationHourMin.hour > 0) {
                formattedTimeString = durationHourMin.hour + " Hr " + formattedTimeString
            }
            const nowLiveSessionWidget: NowLiveSessionWidget = {
                widgetType: "NOW_LIVE_SESSION_WIDGET",
                contentId: session._id,
                title: session.title,
                image: LiveUtil.getImage(session.bannerImages, sessionInfo, 1),
                action: sessionAction,
                liveUserCount: nowLiveClass.liveUserCount.countString,
                buddies: buddies,
                scheduleEpochTime: session.scheduledTimeEpoch,
                videoStatus: session.status,
                isLocked: isInternationalApp && AppUtil.isLiveContentLocked(userContext, session.locked, isUserEligibleForMonetisation, isUserEligibleForTrial),
                cardAction: sessionAction,
                intensity: session.intensityLevel,
                duration: formattedTimeString,
                renderLiveIcon: true,
                shouldRepeat: false,
            }
            if (session?.preferredStreamType === "VIDEO_CALL") {
                nowLiveSessionWidget.url = "https://cdn-media.cure.fit/livefit-content/app/onboarding_guide.mp4"
                nowLiveSessionWidget.videoDisabled = true
            }
            sessionWidgets.push(nowLiveSessionWidget)
        }
        this.widgets = sessionWidgets
        const bookingPageAction = LiveUtil.getLiveClassBookingPageAction(userContext, this.productType)
        const scheduleAction: Action = {
            actionType: "NAVIGATION",
            url: bookingPageAction.url,
            title: "SCHEDULE",
            meta: {
                showArrowImage: true
            }
        }
        this.header = {
            title: this.title,
            seemore: scheduleAction
        }
        return this
    }

    async getClassBuddyData(interfaces: CFServiceInterfaces, userIds: string[]): Promise<Buddy[]> {
        const buddies: Buddy[] = []
        for (const userId of userIds) {
            const user: User = await interfaces.userCache.getUser(userId)
            const buddy: Buddy = {
                icon: user.profilePictureUrl,
                name: user.firstName,
                state: "no-state"
            }
            buddies.push(buddy)
        }
        return buddies
    }

    // private async getSlotAction(liveSession: DigitalCatalogueEntryV2,
    //     interfaces: CFServiceInterfaces, userContext: UserContext, user: User, isUserEligibleForLivePackMonetisation: boolean,
    //     isUserEligibleForLivePackTrial: boolean, source: LIVE_SESSION_ACTION_SOURCE): Promise<Action> {
    //     let action: Action
    //     if (liveSession.status === "LIVE") {
    //         action = LiveUtil.getLiveVideoPlayerAction(userContext, user, liveSession, userContext.sessionInfo, source)
    //     } else if (liveSession.subscriptionStatus === "SUBSCRIBED") {
    //         return {
    //             actionType: "NAVIGATION",
    //             title: "BOOKED",
    //         }
    //     } else {
    //         action = await LiveUtil.getVideoSubscribeAction(user, userContext, liveSession, [], userContext.sessionInfo, source, false, interfaces.classInviteLinkCreator, userContext.userProfile.timezone)
    //         action.title = "BOOK"
    //     }
    //     return LiveUtil.getLiveSessionAction(isUserEligibleForLivePackMonetisation, liveSession.locked, action, isUserEligibleForLivePackTrial, userContext, liveSession.format, source, "0")
    // }
}
