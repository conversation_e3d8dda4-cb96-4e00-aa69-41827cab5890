import { YOGA_BAR_GRAPH_LEGEND, YOGA_BAR_GRAPH_STOPS } from "../util/LiveUtil"
import { inject, injectable } from "inversify"
import * as _ from "lodash"
import { UserScoreMetricsResponse } from "@curefit/diy-common"
import { BASE_TYPES, Logger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

@injectable()
class YogaBarGraphWidgetViewBuilder {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService
        ) {}

    buildView(userScoreMetrics: UserScoreMetricsResponse) {
        try {
            return {
                widgetType: "PULSE_BAR_GRAPH_WIDGET",
                hasDividerBelow: false,
                noMargin: true,
                graph_data: {
                    type: "normal",
                    xUnit: "Reps",
                    noYUnit: true,
                    title: "Surya Namaskar Performance",
                    isScreenshot: true,
                    sceneStyle: this.getSceneStyle(),
                    legendData: {legend: YOGA_BAR_GRAPH_LEGEND},
                    colorStops: YOGA_BAR_GRAPH_STOPS,
                    data: this.createData(userScoreMetrics as any)
                }
            }
        }
        catch (e) {
            this.rollbarService.sendError(e)
            return undefined
        }
    }

    createData(userScoreMetrics: UserScoreMetricsResponse) {
        const data = _.keys(userScoreMetrics.repHistory).sort().map(timestamp => {
            return {
                x: timestamp,
                y: userScoreMetrics.repHistory[timestamp] * 100
            }
        })
        if (data.length < userScoreMetrics.totalRepetitions) {
            data.push(...Array.from(Array(userScoreMetrics.totalRepetitions - data.length)).map((__, i) => {
                return {
                    x: `${data.length + i}`,
                    y: 0
                }
            }))
        }
        return data
    }

    getSceneStyle() {
        return {
            borderTopLeftRadius: 0,
            borderTopRightRadius: 0,
        }
    }
}

export default YogaBarGraphWidgetViewBuilder



