import { BaseWidget, IBaseWidget } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { UserContext } from "@curefit/userinfo-common"
import AppUtil from "../util/AppUtil"
import { CFLiveProduct } from "@curefit/diy-common"
import { UserInfo } from "@curefit/user-common"
import { LivePackUtil } from "../util/LivePackUtil"
import { LivePricesResponse, OfferV2 } from "@curefit/offer-common"
import * as _ from "lodash"
import { DiscountBreakup } from "@curefit/offer-common/src/OfferV3"
import { LivePack } from "./ILivePackPage"

export class LivePackPickerWidgetView extends BaseWidget {

    offerText: string
    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams?: {
        [filterName: string]: string;
    }): Promise<IBaseWidget> {
        const requestSource = AppUtil.getRequestSource(userContext)
        const userId = userContext.userProfile.userId
        const user = await interfaces.userCache.getUser(userId)
        const deviceId = userContext.sessionInfo.deviceId
        const livePacks: CFLiveProduct[] = await interfaces.diyService.getCFLiveProducts(userId, requestSource, AppUtil.getTenantFromUserContext(userContext), deviceId)
        const userInfo: UserInfo = {
            userId: userId,
            deviceId: userContext.sessionInfo.deviceId,
            email: user.email,
            phone: user.phone,
            workEmail: user.workEmail
        }
        const productIds = LivePackUtil.getProductIds(livePacks)
        const livePricesResponse: LivePricesResponse = await interfaces.offerServiceV3.getLivePackPrices(LivePackUtil.getProductIds(livePacks), userInfo)
        let offerIdOfferMap: { [offerId: string]: OfferV2 } = {}
        const offerIds: string[] = []
        productIds.forEach(productId => {
            const prices = livePricesResponse.priceMap[productId]
            if (prices && !_.isEmpty(prices.breakup)) {
                prices.breakup.forEach((priceBreakup: DiscountBreakup) => {
                    offerIds.push(priceBreakup.offerId)
                })
            }
        })
        if (!_.isEmpty(offerIds)) {
            const offerResposne = await interfaces.offerServiceV3.getOffersByIds(offerIds)
            offerIdOfferMap = offerResposne.data
        }
        const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, interfaces.hamletBusiness)
        const isMonetisationEnabled = AppUtil.isLivePackSupported(userContext)
        const { livePackPickerWidget, selectedProductId } = LivePackUtil.getLivePackPickerWidget(userContext, livePacks, userContext.sessionInfo.osName, livePricesResponse, offerIdOfferMap, isMonetisationEnabled, "live_pack_purchase_page", undefined, true, "LIVE_PACK_PICKER_WIDGET_DARK", bucketId)
        const index = livePackPickerWidget.packs.findIndex((livePack: LivePack) => {
            if (livePack.productId === selectedProductId) {
                return true
            }
            return false
        })
        livePackPickerWidget.packs[index].isSelected = true
        livePackPickerWidget.offerText = this.offerText
        livePackPickerWidget.showOffers = false
        return livePackPickerWidget
    }
}