import { Action } from "@curefit/apps-common"

declare class TodayTimeSlot {
    title: string
    action?: Action
    isScheduled?: boolean
    isLive?: boolean
}

export interface TodaysClassWidget {
    widgetType: string
    image: string
    intensity: string
    title: string
    subTitle?: string
    timeslots: TodayTimeSlot[]
    cardAction: Action
    duration: string
    isMasterClass: boolean
    isLocked: boolean
    refreshCardEpoch?: number
    moreAction?: Action
}

export interface RecommendedClassWidget extends TodaysClassWidget {
    description: string
    action: Action
    scheduleTime: number
    durationInMillis: number
    format: string
    trainers: string
    videoUrl: string
}
