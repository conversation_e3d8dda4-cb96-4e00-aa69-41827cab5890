import { Container, inject } from "inversify"
import AuthMiddleware from "../auth/AuthMiddleware"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import * as express from "express"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService } from "@curefit/diy-client"
import { UserContext } from "@curefit/vm-models"
import { InSessionAdjacentUserProfile, InteractionParams } from "@curefit/diy-common"
import { MAXMIND_CLIENT_TYPES, IMaxmindService } from "@curefit/maxmind-client"
import { CLSUtil, ILogger, BASE_TYPES } from "@curefit/base"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import * as _ from "lodash"
import AppUtil from "../util/AppUtil"

export function controllerFactory(kernel: Container) {

    @controller("/liveInSession", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class DigitalInSessionController {
        constructor(
            @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) private diyFulfilmentService: IDIYFulfilmentService,
            @inject(MAXMIND_CLIENT_TYPES.IMaxmindService) private maxmindService: IMaxmindService,
            @inject(BASE_TYPES.ILogger) protected logger: ILogger,
            @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
            @inject(BASE_TYPES.ClsUtil) private clsUtil: CLSUtil
        ) {
        }

        @httpGet("/contentStatus/:contentId")
        async getContentStatus(request: express.Request) {
            return this.diyFulfilmentService.getContentStatus(request.params.contentId)
        }

        @httpGet("/data/:contentId")
        async getInSessionData(request: express.Request) {
            const userContext: UserContext = request.userContext as UserContext
            const userSessionId = request.query.userSessionId
            if (userSessionId) {
                return this.diyFulfilmentService.getInSessionDatav2(request.params.contentId, userSessionId)
            }
            return this.diyFulfilmentService.getInSessionData(request.params.contentId, userContext.userProfile.userId)
        }

        @httpGet("/topRankers/:contentId/")
        public async getTopScorers(request: express.Request): Promise<InSessionAdjacentUserProfile[]> {
            return this.diyFulfilmentService.getTopScorers(request.params.contentId)
        }

        @httpGet("/friendsInSessionData")
        public async getFriendsInSessionData(req: express.Request) {
            const userContext: UserContext = req.userContext as UserContext
            const { contentId, contentType } = req.query
            return this.diyFulfilmentService.getFriendsInSessionData(userContext.userProfile.userId, contentId, _.isEmpty(contentType) ? "DIY_FITNESS" : contentType)
        }

        @httpPost("/resetScoreMetrics")
        public async resetScoreMetrics(req: express.Request) {
            const userContext: UserContext = req.userContext as UserContext
            return this.diyFulfilmentService.resetScore(req.body.userSessionId, req.body.contentType)
        }

        @httpGet("/experiences")
        public async getInSessionExperiences(request: express.Request): Promise<any[]> {
            /* Legacy endpoint deprecated along with diyFulfilmentService.getInSessionExperiences */
            return []
        }

        @httpPost("/experiences")
        public async updateTriggeredInSessionExperiences(request: express.Request) {
            const contentId: string = request.query.contentId
            const userSessionId: string = request.query.userSessionId
            const contentType: string = request.query.contentType
            const triggeredInSessionExperiences = request.body.data
            const userContext: UserContext = request.userContext as UserContext
            return this.diyFulfilmentService.updateTriggeredInSessionExperiences(userContext.userProfile.userId, contentId, userSessionId, contentType, triggeredInSessionExperiences)
        }

        @httpGet("/experiences/introduce")
        public async getExperienceIntroductionTag(request: express.Request) {
            /* Legacy endpoint deprecated along with diyFulfilmentService.updateInSessionExperienceIntroduction */
            return {}
        }

        /*
        * Called when the Gesture is detected on cf-mobile
        * */
        @httpPost("/experiences/interaction")
        public async updateInSessionExperienceInteraction(request: express.Request) {
            const userContext: UserContext = request.userContext
            const userId = userContext.userProfile.userId
            const contentId: string = request.body.contentId
            const userSessionId: string = request.body.userSessionId
            const contentType: string = request.body.contentType
            const interactionParams: InteractionParams = request.body.interactionParams
            return this.diyFulfilmentService.updateInSessionExperienceInteraction(userId, contentId, userSessionId, contentType, interactionParams)
        }

        /*
        * Called on LiveMediaPlayer didMount, data may or may not be returned depending on back-end logic
        * */
        @httpGet("/experiences/inSessionsStart")
        public async getInSessionExperiencesAtStart(request: express.Request) {
            const userContext: UserContext = request.userContext
            const userId = userContext.userProfile.userId
            const contentId: string = request.query.contentId
            const userSessionId: string = request.query.userSessionId
            const contentType: string = request.query.contentType
            return this.diyFulfilmentService.getInSessionExperiencesAtStart(userId, contentId, userSessionId, contentType)
        }
    }
    return DigitalInSessionController
}

export default controllerFactory
