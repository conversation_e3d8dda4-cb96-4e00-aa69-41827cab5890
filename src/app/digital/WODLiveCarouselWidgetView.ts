import { UserContext } from "@curefit/userinfo-common"
import * as _ from "lodash"
import { DigitalCatalogueSearchRequest, LiveClass, LiveFitWorkoutFormat } from "@curefit/diy-common"
import { TimeUtil } from "@curefit/util-common"
import { IBaseWidget, WODLiveCarouselWidget } from "@curefit/vm-models"
import * as momentTz from "moment-timezone"
import { User } from "@curefit/user-common"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import LiveUtil from "../util/LiveUtil"
import AppUtil from "../util/AppUtil"

export class WODLiveCarouselWidgetView extends WODLiveCarouselWidget {

    layoutProps: any
    declare bannerRatio: string
    NUMBER_OF_CAROUSEL_ITEMS = 10

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext): Promise<IBaseWidget> {
        const { userProfile } = userContext
        const timezone = userContext.userProfile.timezone
        const user: User = await userContext.userPromise
        const countryId = AppUtil.getCountryId(userContext)
        const searchRequest: DigitalCatalogueSearchRequest = {
            statuses: ["UPCOMING", "PREPARED", "LIVE"],
            categories: ["LIVE"],
            scheduledTimeEpochOptions: {
                endEpoch: momentTz.tz(timezone).valueOf() + TimeUtil.TIME_IN_MILLISECONDS.DAY * 4
            }
        }
        let formats: LiveFitWorkoutFormat[] = ["SNC", "STRENGTH", "CARDIO", "HRX", "BOXING", "DANCE", "YOGA", "MEDITATION", "MIND_PODCAST", "DANCE_FIT_JUNIOR", "BARRE", "TABATA", "PILATES", "HIIT", "RECOVERY", "AMA", "WALK_FITNESS"]

        let title = "Live & Upcoming Classes"
        if (!_.isEmpty(this.productType)) {
            if (this.productType === "MIND") {
                formats = ["YOGA", "MEDITATION"]
            } else if (this.productType === "DIY_MEDITATION") {
                formats = ["MEDITATION"]
            } else if (this.productType === "FITNESS") {
                if (AppUtil.isIntlWidgetNamesSupported(userContext)) {
                    title = "Upcoming Workouts"
                }
                formats = ["SNC", "STRENGTH", "CARDIO", "HRX", "BOXING", "DANCE", "YOGA", "DANCE_FIT_JUNIOR", "BARRE", "TABATA", "PILATES", "HIIT", "RECOVERY", "AMA", "WALK_FITNESS"]
            } else if (this.productType === "FOOD") {
                formats = ["EAT"]
                title = "Live & Upcoming Shows"
            } else if (this.productType === "MIND_PODCAST") {
                formats = ["MIND_PODCAST"]
                title = "Mind Map"
            } else if (this.productType === "HOBBY") {
                title = "Live & Upcoming Shows"
                formats = ["HOBBY"]
            }
        }
        let liveVideosResponse: LiveClass[] = await interfaces.diyService.getUpcomingLiveClasses(userProfile.userId, AppUtil.getTenantFromUserContext(userContext), formats, countryId)
        liveVideosResponse = LiveUtil.filterInteractiveSessionAndRealLive(liveVideosResponse)
        liveVideosResponse = liveVideosResponse.slice(0, this.NUMBER_OF_CAROUSEL_ITEMS)
        // if (sessionInfo.userAgent === "APP") {
        //     const liveVideosMasterClasses: LiveClass[] = []
        //     const liveVideosOtherClasses: LiveClass[] = []
        //     for (let i = 0; i < liveVideosResponse.length; i++) {
        //         if (liveVideosResponse[i].isMasterClass) {
        //             liveVideosMasterClasses.push(liveVideosResponse[i])
        //         } else {
        //             liveVideosOtherClasses.push(liveVideosResponse[i])
        //         }
        //     }
        //     if (liveVideosMasterClasses.length < this.NUMBER_OF_CAROUSEL_ITEMS) {
        //         const classesToAppend = this.NUMBER_OF_CAROUSEL_ITEMS - liveVideosMasterClasses.length
        //         liveVideosMasterClasses.concat(liveVideosOtherClasses.slice(0, classesToAppend))
        //     }
        //     liveVideosResponse = liveVideosMasterClasses
        // }
        const productType = _.isEmpty(this.productType) ? "FITNESS" : this.productType
        const bookingPageAction = LiveUtil.getLiveClassBookingPageAction(userContext, productType)
        const liveWodCards = await LiveUtil.getLiveWODCards(user, userContext, liveVideosResponse, bookingPageAction.url, interfaces)
        this.data = liveWodCards
        if (_.isEmpty(this.data)) {
            return undefined
        }
        this.header = {
            title: title
        }
        this.header.seemore = {
            actionType: "NAVIGATION",
            url: bookingPageAction.url,
            title: "VIEW ALL"
        }
        this.bannerRatio = 303 + ":" + 368
        this.layoutProps = this.setLayoutProps()
        return this
    }

    setLayoutProps(): any {
        return {
            bannerWidth: 303,
            bannerHeight: 368,
            bannerRatio: "303:368",
            edgeToEdge: false,
            borderRadius: 14,
            showPagination: false,
            interContentSpacing: 16
        }
    }
}
