import { inject, injectable } from "inversify"
import * as momentTz from "moment-timezone"
import {
    Action,
    LiveSessionReportMessage,
    ProductDetailPage,
    ProductFeedbackWidget,
    ProductListWidget
} from "../common/views/WidgetView"
import { PageWidget } from "../page/Page"
import {
    DigitalCatalogueEntryV1,
    DIYFitnessProductExtended,
    SessionContentType,
    SessionReportStatus,
    UserScoreMetricsResponse
} from "@curefit/diy-common"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService } from "@curefit/diy-client"
import { Action as WidgetAction } from "@curefit/vm-models"
import {
    CardVideoItem,
    CardVideoWidget,
    ChallengeStatusWidget,
    IPageService,
    ISegmentService,
    UserContext
} from "@curefit/vm-models"
import { integer } from "twilio/lib/base/deserialize"
import { BodyPart, SimpleWod } from "@curefit/fitness-common"
import AppUtil, { AppFont } from "../util/AppUtil"
import { HERCULES_CLIENT_TYPES, IHerculesService } from "@curefit/hercules-client"
import * as _ from "lodash"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CacheHelper } from "../util/CacheHelper"
import { VideoCardCarouselWidgetView } from "../page/vm/widgets/VideoCardCarouselWidgetView"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { Feedback } from "@curefit/feedback-common"
import { FEEDBACK_MONGO_TYPES, IFeedbackReadOnlyDao } from "@curefit/feedback-mongo"
import IFeedbackBusiness from "../ugc/IFeedbackBusiness"
import FeedbackPageConfigV2Cache from "../ugc/FeedbackPageConfigV2Cache"
import { CdnUtil, eternalPromise, TimeUtil } from "@curefit/util-common"
import { ICultBusiness } from "../cult/CultBusiness"
import { RewardFulfilment } from "@curefit/riddler-common"
import { IQuestService, QUEST_CLIENT_TYPES } from "@curefit/quest-client"
import LiveUtil, {
    CULT_LIVE_CAMPAIGN_ID,
    getLiveReportFilters,
    LIVE_CLASS_REPORT_PREV_PAGE,
    LIVE_CLASS_REPORT_SOURCE,
    LIVE_FREE_TRIAL_EXPIRED_SEGMENT,
    LIVE_FREE_TRIAL_ONGOING_SEGMENT,
    LIVE_REPORT_V2_ASPECT_RATIO,
    LIVE_REPORT_V2_ASPECT_RATIO_STR,
    RENO_CLASS_CONTENT_IDS,
    ReportHeaderMessageWorkoutFormats
} from "../util/LiveUtil"
import { BadgeFulfilmentResponse } from "@curefit/reward-common"
import { ClassInviteLinkCreator } from "../cult/invitebuddy/ClassInviteLinkCreator"
import {
    AllAction,
    CardListItem,
    CardListItemLeftInfo,
    CardListItemRightInfo,
    CardListItemViewType,
    CardListWidgetView
} from "../page/vm/widgets/card/CardListWidget"
import { UrlPathBuilder } from "@curefit/product-common"
import { postWorkoutMeditations } from "./PostWorkoutMeditationDetails"
import { Tenant } from "@curefit/social-common"
import { ISocialService, SOCIAL_CLIENT_TYPES } from "@curefit/social-client"
import * as path from "path"
import { ICrudKeyValue, REDIS_TYPES } from "@curefit/redis-utils"
import { BASE_TYPES, Logger } from "@curefit/base"
import { PledgeConfig, RiddlerServiceConfig } from "../challenges/ChallengesController"
import { ConfigService, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import { RIDDLER_CLIENT_TYPES, RiddlerCacheService } from "@curefit/riddler-client"
import { IRewardService, REWARD_CLIENT_TYPES } from "@curefit/reward-client"
import { Badge, InSessionBadge, InSessionBadgeResponse } from "@curefit/quest-common"
import { getShareText } from "../util/QuestUtils"
import { ChallengeBadgeWidget } from "@curefit/vm-models/dist/src/models/widgets/ChallengeBadgeWidget"
import { PromiseCache } from "../util/VMUtil"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { Session } from "@curefit/userinfo-common"
import { OrderSource } from "@curefit/order-common"
import { ErrorFactory } from "@curefit/error-client"
import { LivePackUtil } from "../util/LivePackUtil"
import { HamletContext } from "@curefit/hamlet-common"
import YogaReportCardWidgetViewBuilder from "./YogaReportCardWidgetViewBuilder"
import YogaBarGraphWidgetViewBuilder from "./YogaBarGraphWidgetViewBuilder"
import { INTERVENTION_EVENTS } from "../announcement/AnnouncementBusiness"
import { EventInterventionMappingBusiness } from "../intervention/EventInterventionMappingBusiness"
import YogaTimeStampedProgressPills from "./YogaTimeStampedProgressPills"
import { ActionUtil } from "@curefit/base-utils"
import { BannerCarouselWidget } from "../page/PageWidgets"
import { BadgeWallWidgetProps, BadgeWallWidget, BadgeWidget } from "@curefit/apps-common"

const REMO_WIDGET_DATA = {
    bannerImage: "/image/remo-banner-report-v2.png",
    bannerVideo: "https://cdn-media.cure.fit/video/vm/remo-introduction-mute.mp4",
    video: "https://cdn-media.cure.fit/test/ep02+fitstep+song+new.540.mp4"
}

const TRAINER_LED_BAR_STOP_COLORS = [
    {
        value: 10,
        color: "rgb(0, 0, 0)"
    },
    {
        value: 30,
        color: "rgb(0, 0, 0)"
    },
    {
        value: 60,
        color: "rgb(0, 0, 0)"
    },
    {
        value: 100,
        color: "rgb(0, 0, 0)"
    },
    {
        value: 200,
        color: "rgb(0, 0, 0)"
    }]

const INDIA_BAR_STOP_COLORS = [
    {
        value: 10,
        color: "rgb(231, 182, 254)"
    },
    {
        value: 30,
        color: "rgb(117, 220, 255)"
    },
    {
        value: 60,
        color: "rgb(184, 233, 134)"
    },
    {
        value: 100,
        color: "rgb(255, 205, 100)"
    },
    {
        value: 200,
        color: "rgb(255, 138, 140)"
    }]

class ReportView extends ProductDetailPage {
    message?: LiveSessionReportMessage
    meta?: any
    pageLoadAction?: Action
    backAction?: any

    constructor(widgets: PageWidget[], actions: Action[], message?: LiveSessionReportMessage, meta?: any, pageLoadAction?: Action, backAction?: any) {
        super()
        this.widgets = widgets
        this.actions = actions
        this.message = message
        this.meta = meta
        this.pageLoadAction = pageLoadAction
        this.backAction = backAction
    }
}

class PostWorkMeditationCardListItem implements CardListItem {
    action: AllAction
    header: { title: string }
    leftInfo: CardListItemLeftInfo
    note: string
    rightInfo: CardListItemRightInfo
    subTitle: string
    title: string
    viewType: CardListItemViewType
}

interface MessageType {
    shareTitle: string
    shareMessage: string
}

interface SelfieShareCardParams {
    session: DigitalCatalogueEntryV1,
    userContext: UserContext,
    userScoreMetrics: UserScoreMetricsResponse,
    card?: any,
    source?: LIVE_CLASS_REPORT_SOURCE,
    momentsImage?: string,
    showingMessage?: boolean,
    userCache: CacheHelper,
    socialService: ISocialService,
    diyFulfilmentService: IDIYFulfilmentService,
    logger: Logger,
    shareMessage?: MessageType
    contentType?: SessionContentType
    userSessionId?: string
    isDIY?: boolean,
    yogaTimeStampedProgressPillsBuilder?: YogaTimeStampedProgressPills
    showingNewYogaReport?: boolean
    achievementImageUrl?: string
}

type MetricType = "ENERGY" | "SCORE" | "RANK" | "CALORIES"

const proTips = [
    "Try to exercises at least 30 minutes a day, at least five days a week for a healthy heart.",
    "Working out with a friend can be so much more fun! Invite your buddy for your next class to push your limits.",
    "Relieve post-class soreness with a cold water shower for 10 to 15 minutes.",
    "If your sneakers are more than two years old, it's time to say goodbye to your fave pair for new sneaks!",
    "Planning on picking up the pace for your next class? Opt for a high-carbohydrate meal that will help keep you going strong!",
    "It's hard to avoid that 3 p.m. stomach rumble. Instead of chips or cookies, reach out for something healthier like an energy bar or a fruit bowl.",
    "If you’re trying to get fit, shed pounds and improve heart health, fiber is your best friend.",
    "You don't need a gym or fancy gear to stay fit. Designate and declutter a spot in your home for your daily workouts.",
    "It's easier to commit to a fitness goal and be successful if you do it for a set amount of time.",
    "Exercise at a steady pace. Keep a pace that allows you to still talk during the activity.",
    "Make sure you stay hydrated. It is important to drink water even before you feel thirsty, especially on hot days."
]

@injectable()
class DigitalReportViewBuilder {
    shareMessage: MessageType
    constructor(
        @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) private diyFulfilmentService: IDIYFulfilmentService,
        @inject(HERCULES_CLIENT_TYPES.IHerculesService) private herculesService: IHerculesService,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
        @inject(RIDDLER_CLIENT_TYPES.RiddlerCacheService) private riddlerCacheService: RiddlerCacheService,
        @inject(CUREFIT_API_TYPES.PageService) protected pageService: IPageService,
        @inject(PAGE_CONFIG_TYPES.ConfigService) private configService: ConfigService,
        @inject(REWARD_CLIENT_TYPES.IRewardService) private rewardService: IRewardService,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(FEEDBACK_MONGO_TYPES.FeedbackReadOnlyDao) private feedbackDao: IFeedbackReadOnlyDao,
        @inject(CUREFIT_API_TYPES.FeedbackBusiness) private feedbackBusiness: IFeedbackBusiness,
        @inject(CUREFIT_API_TYPES.ClassInviteLinkCreator) private classInviteLinkCreator: ClassInviteLinkCreator,
        @inject(CUREFIT_API_TYPES.FeedbackPageConfigV2Cache) private feedbackPageConfigV2Cache: FeedbackPageConfigV2Cache,
        @inject(CUREFIT_API_TYPES.CultBusiness) protected cultBusiness: ICultBusiness,
        @inject(SOCIAL_CLIENT_TYPES.SocialService) private socialService: ISocialService,
        @inject(REDIS_TYPES.RedisDao) private redisDao: ICrudKeyValue,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService,
        @inject(CUREFIT_API_TYPES.YogaBarGraphWidgetViewBuilder) private yogaBarGraphWidgetViewBuilder: YogaBarGraphWidgetViewBuilder,
        @inject(CUREFIT_API_TYPES.YogaReportCardWidgetViewBuilder) private yogaReportCardWidgetViewBuilder: YogaReportCardWidgetViewBuilder,
        @inject(CUREFIT_API_TYPES.EventInterventionMappingBusiness) private eventInterventionMappingBusiness: EventInterventionMappingBusiness,
        @inject(CUREFIT_API_TYPES.YogaTimeStampedProgressPills) private yogaTimeStampedProgressPills: YogaTimeStampedProgressPills,
        @inject(QUEST_CLIENT_TYPES.IQuestService) private questService: IQuestService,
    ) {
    }

    async buildView(userContext: UserContext, contentId: string, prevPage?: LIVE_CLASS_REPORT_PREV_PAGE, orderSource?: OrderSource, session?: Session, isDIY?: boolean, userSessionId?: string, contentType?: SessionContentType): Promise<ReportView> {
        const widgetPromises = []
        const actions: Action[] = []
        if (!userContext.userProfile.promiseMapCache) {
            userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
        }
        const [userSegments, { obj: userScoreMetrics }, { card, supported }, showCityEnergyWidget, momentsMedia, isYogaReportSupported, classResponse] = await Promise.all([
            this.serviceInterfaces.clsUtil.getPlatformSegments(),
            eternalPromise(isDIY ? this.diyFulfilmentService.getUserScoreMetricsV2(userSessionId, contentType) : this.diyFulfilmentService.getUserScoreMetrics(contentId, userContext.userProfile.userId)),
            this.cultBusiness.isUserEligibleForInviteOffer(userContext, CULT_LIVE_CAMPAIGN_ID),
            this.redisDao.read("SHOULD-SHOW-CITY-CHALLENGE"),
            this.getMomentsMedia(userContext, contentId, isDIY),
            AppUtil.isYogaReportSupported(userContext, this.hamletBusiness),
            this.getClassResponse(userContext, contentId, isDIY)
        ])

        const achievedBadgeInfo = await this.getAchievedBadgeInfo(userContext, userScoreMetrics)
        let backAction
        if (await AppUtil.showUserCoachClp(userContext, this.segmentService) && prevPage === "videoplayer") {
            backAction = {
                actionType: "NAVIGATION",
                url: "curefit://tabpage?pageId=fitnesshub&selectedTab=cultpassLIVE-Members_NEW&forceShowLoader=true",
            }
        }

        const showPostWorkoutWidget = classResponse.format !== "MEDITATION" && classResponse.format !== "MIND_PODCAST"
        const isVanillaFormat = LiveUtil.isVanillaLiveFitFormat(classResponse?.format)
        const isSessionComplete = userScoreMetrics?.reportStatus === SessionReportStatus.COMPLETE && !isVanillaFormat
        let message = undefined
        if (isDIY && (await AppUtil.isDiyEnergyMeterSupported(userContext, this.hamletBusiness)) && !_.isNil(userScoreMetrics?.rank)) {
            message = {
                title: "",
                subTitle: "Your rank is calculated based on scores of users who have already completed this class.",
                containerStyle: { backgroundColor: "#e4eefd" },
                textStyle: { color: "#2f6dc9" }
            }
        } else if (prevPage === "videoplayer" && ReportHeaderMessageWorkoutFormats.includes(classResponse?.format) && userScoreMetrics?.reportStatus === SessionReportStatus.COMPLETE) {
            message = {
                title: "",
                subTitle: "Your final energy scores and rank will be updated in a few minutes."
            }
        }
        const isYogaWorkout = classResponse.format === "YOGA"

        // if (AppUtil.isVideoMergerSupported(userContext)) {
        //     widgetPromises.push(this.getRemoCardVideoWidget(userContext))
        // }

        if (AppUtil.isTVApp(userContext)) {
            let shareCardWidget, performaceWidget, barGraphWidget
            shareCardWidget = await DigitalReportViewBuilder.getReportSelfieShareCardWidget({ session: classResponse as DigitalCatalogueEntryV1, userContext, userScoreMetrics, momentsImage: momentsMedia, showingMessage: !!message, shareMessage: this.shareMessage, diyFulfilmentService: this.diyFulfilmentService, logger: this.logger, socialService: this.socialService, userCache: this.userCache, contentType })

            const filteredEnergyHistory = this.filterAndMapEnergyScore(userScoreMetrics, (<any>classResponse).scheduledTimeEpoch ? (<any>classResponse).scheduledTimeEpoch : userScoreMetrics?.startTime, classResponse.duration, isDIY)
            if (userScoreMetrics && !_.isNil(userScoreMetrics?.score)) {
                const percentileCalculation = this.isPercentileCalculationPossible(userScoreMetrics)
                if (percentileCalculation) {
                    performaceWidget = this.getClassPerformanceWidget(userContext, userScoreMetrics, classResponse, null, null)
                }
                if (_.size(filteredEnergyHistory) > 2) {
                    barGraphWidget = this.getBarGraphWidget(userContext, filteredEnergyHistory)
                }
            }

            widgetPromises.push(this.getFitnessReportWidget(shareCardWidget, performaceWidget, barGraphWidget))
            const feedbackWidget = this.getFeedbackWidget(userContext.userProfile.userId, userScoreMetrics)
            widgetPromises.push(feedbackWidget)

            const widgets = _.compact(await Promise.all(widgetPromises))
            return new ReportView(widgets, actions, message)
        }

        const [{ meta, badgeData }, shareMessage, { singleBannerWidget, pageLoadAction }] = await Promise.all([
            this.getMetaAndBadgeData(userContext, isSessionComplete),
            isDIY ? this.getDIYShareTitleAndMessage(userContext, <DIYFitnessProductExtended>classResponse, card && { ...card, source: "live-class-report" }) : this.getShareTitleAndMessage(userContext, <DigitalCatalogueEntryV1>classResponse, card && { ...card, source: "live-class-report" }),
            this.startLiveTrialAndGetPageLoadActionAndBannerWidget(userContext, isSessionComplete, orderSource, session)
        ])

        this.shareMessage = shareMessage

        const ChallengeStatusWidgetPromise = this.getChallengeStatusWidget(userContext)

        widgetPromises.push(singleBannerWidget)

        const submitAction: Action =  await this.getSubmitAction(userContext, this.segmentService)

        if (submitAction) {
            actions.push(submitAction)
        }

        if (_.isNil(userScoreMetrics)) {
            widgetPromises.push(ChallengeStatusWidgetPromise)
            if (AppUtil.isLiveSessionReportSelfieShareCardSupported(userContext)) {
                widgetPromises.push(DigitalReportViewBuilder.getReportSelfieShareCardWidget({ session: classResponse as DigitalCatalogueEntryV1, userContext, userScoreMetrics, card: card && { ...card, source: "live-class-report" }, momentsImage: momentsMedia, showingMessage: !!message, shareMessage: this.shareMessage, diyFulfilmentService: this.diyFulfilmentService, logger: this.logger, socialService: this.socialService, userCache: this.userCache, contentType, userSessionId, isDIY }))
            }

            // adding referral banner
            widgetPromises.push(LiveUtil.getReferralBanner(userContext, this.segmentService))

            if (!_.isNil(badgeData)) {
                widgetPromises.push(badgeData)
            }

            if (AppUtil.isLiveNewClassReportSupported(userContext)) {
                widgetPromises.push(this.getMySquadCardWidget(classResponse, userContext, userScoreMetrics, contentType, isDIY))
            }

            if (supported) {
                widgetPromises.push(LiveUtil.getReferralBannerWidget(userContext))
            }

            if (this.isTrialOrTrialExpiredOnlyUser(userSegments, userContext)) {
                widgetPromises.push(LiveUtil.getMembershipBannerWidget(userSegments))
            }

            // widgets.push(await this.getRecommendationWidget(userContext, contentId))
            if (showPostWorkoutWidget) {
                widgetPromises.push(this.getRecommendedPostWorkoutWidget(contentId))
            }
            const widgets = _.compact(await Promise.all(widgetPromises))
            return new ReportView(widgets, actions, message, meta, pageLoadAction, backAction)
        }
        // new badge widget call
        // if (AppUtil.isNewBadgeWidgetSupported(userContext)) {
        //     const newBadgeWidget = this.getBadgeWidget(achievedBadgeInfo, userContext)
        //     if (!_.isNull(newBadgeWidget)) {
        //         widgetPromises.push(newBadgeWidget)
        //     }
        // }

        widgetPromises.push(ChallengeStatusWidgetPromise)

        const noReportFormats = ["HOBBY"]
        const canShowReport = !(classResponse?.format && noReportFormats.includes(classResponse?.format))
        const showingNewYogaReport = LiveUtil.canShowNewYogaReport(isYogaReportSupported, userScoreMetrics)
        const showingYogaCorrectDeviceSetUpBanner = showingNewYogaReport && (userScoreMetrics?.score > 0 || _.some(userScoreMetrics?.energyHistory, Boolean) || _.some(userScoreMetrics?.repHistory, Boolean))

        if (canShowReport) {
            if (AppUtil.isLiveSessionReportSelfieShareCardSupported(userContext)) {
                widgetPromises.push(DigitalReportViewBuilder.getReportSelfieShareCardWidget({
                    session: classResponse as DigitalCatalogueEntryV1, userContext, userScoreMetrics, card: card && { ...card, source: "live-class-report" },
                    momentsImage: momentsMedia, showingMessage: !!message, diyFulfilmentService: this.diyFulfilmentService, logger: this.logger, socialService: this.socialService, shareMessage: this.shareMessage, userCache: this.userCache,
                    contentType, userSessionId, isDIY, showingNewYogaReport, yogaTimeStampedProgressPillsBuilder: this.yogaTimeStampedProgressPills, achievementImageUrl: achievedBadgeInfo?.imageUrl
                }))
            } else {
                widgetPromises.push(this.getReportShareCardWidget(<DigitalCatalogueEntryV1>classResponse, userContext, userScoreMetrics, contentType, card && { ...card, source: "live-class-report" }))
            }

            // adding referral banner
            widgetPromises.push(LiveUtil.getReferralBanner(userContext, this.segmentService))
        }
        // if (AppUtil.isBadgeWallWidgetSupported(userContext)) {
        //     const badgeWallWidget = this.getBadgeWallWidget(userContext, achievedBadgeInfo)
        //     if (!_.isNull(badgeWallWidget)) {
        //         widgetPromises.push(badgeWallWidget)
        //     }
        // }
        if (!_.isNil(badgeData)) {
            widgetPromises.push(badgeData)
        }

        this.logger.info(`showCityEnergyWidget condition in redis: ${showCityEnergyWidget}, isyogaworkout: ${isYogaWorkout}`)
        if (Number(showCityEnergyWidget)) {
            let score: number = 0
            if (isYogaWorkout) {
                score = 2500
            }
            if (!_.isNil(userScoreMetrics.score)) {
                score = userScoreMetrics.score
            }
            if (score && this.isCityEnergyyWidgetSupported(userScoreMetrics.playbackMillis, userContext.userProfile.cityId)) {
                widgetPromises.push(this.getCityEnergyWidget(userContext, score, userContext.userProfile.city.name))
            }
        }

        if (AppUtil.isLiveNewClassReportSupported(userContext)) {
            widgetPromises.push(this.getMySquadCardWidget(classResponse, userContext, userScoreMetrics, contentType, isDIY))
        }

        const filteredEnergyHistory = this.filterAndMapEnergyScore(userScoreMetrics, (<any>classResponse).scheduledTimeEpoch ? (<any>classResponse).scheduledTimeEpoch : userScoreMetrics.startTime, classResponse.duration, isDIY)
        if (!_.isNil(userScoreMetrics.score)) {
            const multipleShareSupported = AppUtil.isLiveNewClassReportSupported(userContext)
            const percentileCalculation = this.isPercentileCalculationPossible(userScoreMetrics)
            if (percentileCalculation && !DigitalReportViewBuilder.shouldOmitReportMetric(classResponse as DigitalCatalogueEntryV1, "ENERGY")) {
                widgetPromises.push(this.getClassPerformanceWidget(userContext, userScoreMetrics, classResponse, filteredEnergyHistory, multipleShareSupported))
            }
            if (_.size(filteredEnergyHistory) > 2 && (!multipleShareSupported || !percentileCalculation) && !DigitalReportViewBuilder.shouldOmitReportMetric(classResponse as DigitalCatalogueEntryV1, "ENERGY")) {
                widgetPromises.push(this.getBarGraphWidget(userContext, filteredEnergyHistory))
            }
            if (supported) {
                widgetPromises.push(LiveUtil.getReferralBannerWidget(userContext))
            }
            if (this.isTrialOrTrialExpiredOnlyUser(userSegments, userContext)) {
                widgetPromises.push(LiveUtil.getMembershipBannerWidget(userSegments))
            }
            if (showingNewYogaReport) {
                widgetPromises.push(this.yogaAsanaInfo(userScoreMetrics))
            }
            if (showingYogaCorrectDeviceSetUpBanner) {
                widgetPromises.push(this.yogaCorrectDeviceSetUpBanner())
            }
            if (showPostWorkoutWidget) {
                widgetPromises.push(this.getRecommendedPostWorkoutWidget(contentId))
            }
            if (!AppUtil.isInternationalTLApp(userContext) && !AppUtil.isSugarFitOrUltraFitApp(userContext)) {
                widgetPromises.push(LiveUtil.getProTipWidget("daily_report_pro_tip"))
            }
            const widgets = _.compact(await Promise.all(widgetPromises))
            return new ReportView(widgets, actions, message, meta, pageLoadAction, backAction)
        }

        if (userScoreMetrics.energyMeterState === "USER_DISABLED") {
            widgetPromises.push(this.getBlurMetricsWidget())
        }
        if (supported) {
            widgetPromises.push(LiveUtil.getReferralBannerWidget(userContext))
        }
        if (this.isTrialOrTrialExpiredOnlyUser(userSegments, userContext)) {
            widgetPromises.push(LiveUtil.getMembershipBannerWidget(userSegments))
        }
        if (showingNewYogaReport) {
            widgetPromises.push(this.yogaAsanaInfo(userScoreMetrics))
        }
        if (showingYogaCorrectDeviceSetUpBanner) {
            widgetPromises.push(this.yogaCorrectDeviceSetUpBanner())
        }
        if (showPostWorkoutWidget) {
            widgetPromises.push(this.getRecommendedPostWorkoutWidget(contentId))
        }

        //         if (userContext.sessionInfo.appVersion >= LIVE_SESSION_REPORT_FEEDBACK_VERSION) {
        //             const feedbackWidget = await this.getReportFeedbackWidget(userContext.userProfile.userId, userScoreMetrics.reportId)
        //             if (!_.isNil(feedbackWidget)) {
        //                 widgets.push(feedbackWidget)
        //             }
        //         }
        // widgets.push(await this.getRecommendationWidget(userContext, contentId))

        const widgets = _.compact(await Promise.all(widgetPromises))
        return new ReportView(widgets, actions, message, meta, pageLoadAction, backAction)
    }

    private async getFitnessReportWidget(shareCardWidget: any, performaceWidget: any, barGraphWidget: any): Promise<any> {
        return {
            widgetType: "FITNESS_REPORT_WIDGET",
            headingData: shareCardWidget,
            graphData: barGraphWidget,
            auxiliaryData: performaceWidget
        }
    }

    private async getFeedbackWidget(userId: string, userScoreMetrics: UserScoreMetricsResponse): Promise<any> {
        const playedTime = userScoreMetrics?.playbackMillis || 0
        const { minutes } = DigitalReportViewBuilder.getMinutesAndSeconds(playedTime)
        const isReportIncomplete = (_.isNil(userScoreMetrics) || userScoreMetrics?.reportStatus === SessionReportStatus.INCOMPLETE)

        this.logger.info(`minutes played ${minutes}, isIncomplete ${isReportIncomplete}, userScoreMetric ${JSON.stringify(userScoreMetrics)}  `)

        if (minutes >= 10 || !isReportIncomplete) {
            const itemId = `${userScoreMetrics?.userSessionId}`
            let feedback = await this.feedbackDao.findOne({ itemId, userId })
            if (feedback === undefined || feedback === null) {
                feedback = await this.feedbackBusiness.createLiveSessionReportFeedback(userId, itemId)
            }
            if (feedback && (feedback.rating === "NOT_RATED" || feedback.rating === "DISMISSED")) {
                return {
                    widgetType: "FEEDBACK_WIDGET",
                    feedbackId: feedback.feedbackId,
                }
            }
        }
        return undefined
    }

    private async getMetaAndBadgeData(userContext: UserContext, isSessionComplete: boolean) {
        let meta: any
        let badgeData: ChallengeBadgeWidget
        try {
            const riddlerServiceConfig: RiddlerServiceConfig = this.configService.getConfig("RIDDLER")
            const pledgeConfig = riddlerServiceConfig.configs.pledgeChallengeConfig
            const segmentId = pledgeConfig.pledgeReportCyclopsSegmentId
            const segment = await this.segmentService.doesUserBelongToSegment(segmentId, userContext)
            if (isSessionComplete && segment) {
                meta = await this.getPledgeChallengeData(pledgeConfig, userContext)
            } else {
                badgeData = await this.getChallengeAchievementBadge(userContext)
                if (!_.isNil(badgeData)) {
                    meta = this.getBadgeData(badgeData)
                }

            }
        } catch (e) {
            this.logger.info(`Error while creating pledge challenge modal ${JSON.stringify(e)}`)
        }
        if (isSessionComplete && !meta) {
            meta = await this.getCalendarSchedulingData(userContext)
        }
        return { meta, badgeData }
    }

    private async startLiveTrialAndGetPageLoadActionAndBannerWidget(userContext: UserContext, isSessionComplete: boolean, orderSource?: OrderSource, session?: Session) {
        let pageLoadAction: Action = undefined
        let singleBannerWidget
        // this is for conducting curefit live trial experiments

        const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, this.hamletBusiness)
        if (isSessionComplete && !AppUtil.isInternationalApp(userContext)) {
            const isUserEligibleForTrial = await LiveUtil.isUserEligibleForLivePackTrial(userContext, this.diyFulfilmentService)
            if (isUserEligibleForTrial) {
                // TODO remove once diy fixes it
                const isUserAddedToSegment = await this.serviceInterfaces.segmentationClient.addUsersToSegment("cult-live", [userContext.userProfile.userId])
                const isUserPartOfExperiment1 = await this.serviceInterfaces.cultBusiness.checkIfUserPartOfLiveTrialExperiment1(userContext)
                if (isUserPartOfExperiment1) {
                    const trialStarted = await LivePackUtil.startCurefitLiveTrial(this.serviceInterfaces, userContext, orderSource, session)
                    pageLoadAction = LiveUtil.getLivePackTrialStartAction(undefined, userContext, "digital_report", bucketId)
                    singleBannerWidget = LivePackUtil.getSingleBannerCarouselWidget("image/livefit/app/trial.png", 334, 57, undefined, "transparent")
                }
            }
        } else if (!AppUtil.isInternationalApp(userContext)) {
            // if user leaves the session early, we show him an announcement
            pageLoadAction = await this.eventInterventionMappingBusiness.buildInterventionAction(INTERVENTION_EVENTS.partialLiveWatchedEvent, this.serviceInterfaces.pageService, this.serviceInterfaces.segmentService, userContext)
        }
        return { singleBannerWidget, pageLoadAction }
    }

    private async getMomentsMedia(userContext: UserContext, contentId: string, isDIY: boolean) {
        let momentsMedia: string
        // momentsMedia not supported for DIY, use the contentType if needed
        if (AppUtil.isLiveMomentOnReportSupported(userContext) && !isDIY) {
            const postResponse = (await eternalPromise(this.socialService.getUserPostsForCommunity(Number(userContext.userProfile.userId), contentId, Tenant.LIVE, 1, 0))).obj
            const media = postResponse?.contentList.elements?.[0]?.medias?.[0]
            if (media && media.fileCDNPrefix && media.fileName && media.fileExtension) {
                momentsMedia = media.fileCDNPrefix + media.fileName + "." + media.fileExtension
            }
        }
        return momentsMedia
    }

    private getRemoCardVideoWidget(userContext: UserContext) {
        const action: WidgetAction = {
            actionType: "SHOW_VIDEO_MERGING_MODAL",
            "title": "",
            "meta": {
                url: REMO_WIDGET_DATA.video,
                processingText:
                    "Syncing your steps with Remo.\nThis might take a few seconds.",
                sharingText: userContext.sessionInfo.osName === "android" ? "I just did a #fitstep with Remo on cult.fit app. Give it a go if you've also got #moveslikeRemo https://cure.app.link/GEQM57NJv9" : null
            },
        }
        const remoWidgetData: CardVideoItem = {
            image: REMO_WIDGET_DATA.bannerImage,
            videoUri: REMO_WIDGET_DATA.bannerVideo,
            hasDividerBelow: false,
            thumbnailVideoUri: "",
            action,
            footer: {
                title: "Create your video with Remo",
                subTitle: "",
                rightText: "START",
                action
            }
        }
        return new CardVideoWidget(remoWidgetData)
    }

    private async getCalendarSchedulingData(userContext: UserContext): Promise<any> {
        const userId = userContext.userProfile.userId
        const key = `cfapp:calendarScheduling:${userId}`
        const [alreadyShown, supported] = await Promise.all([this.redisDao.read(key), AppUtil.isCalendarSchedulingSupported(userContext, this.hamletBusiness)])
        if (alreadyShown || !supported) {
            return undefined
        }
        const localNotificationMessage = userContext?.sessionInfo?.clientVersion >= 8.41 ? "" : "Workout scheduled - Stick to your committed schedule :)"
        // create an entry for 1 month - this is a hack supposed to be run for 1 month.
        // should it work, will be implemented in a better way.
        await this.redisDao.createWithExpiry(key, "userEligible", 30 * 24 * 60 * 60)
        return {
            shouldShowScheduleCalendarModal: true,
            eventTitle: "cure.fit Fitness Commitment",
            calendarDuration: 60,
            description: "Bravo! You've blocked time for your workouts and no matter how busy you are, you deserve it. This single act has helped thousands of our members in turning fitness into a lasting habit. PS: If you edit or delete this event and choose \"this and following events\" - the changes only apply to 1 weekday at a time.",
            successMessage: "Your workout has been scheduled.",
            recurrence: "weekly",
            alarmOffset: -15,
            localNotificationTitle: "Reminder ⏰⏰⏰",
            localNotificationMessage,
            localNotificationOffset: 1,
            notificationInvalidTimeRange: [21, 8],
            notificationFallbackHour: 21,
            actionTitle: "SUBMIT",
            calendarSelectionSubtitle: "(Work calendar recommended)",
        }
    }

    private async getAchievedBadgeInfo(userContext: UserContext, userScoreMetric: UserScoreMetricsResponse): Promise<Badge> {
        if (!AppUtil.isNewBadgeWidgetSupported(userContext) || _.isEmpty(userScoreMetric)) {
            return null
        }
        const achievedBadgesList = userScoreMetric.achievedBadges
        if (_.isEmpty(achievedBadgesList)) {
            return null
        }
        // Only showing one badge currently
        const curBadge = achievedBadgesList[0]
        return this.questService.getBadgeById(curBadge.badgeId)
    }

    private async getBadgeWallWidget(userContext: UserContext, achievedBadge: Badge): Promise<BadgeWallWidget> {
        // Show one achieved badge and 2 upcoming ones
        const achievableBadges = await this.questService.getAchievableBadgesForUser(userContext.userProfile.userId, userContext.userProfile.timezone)
        const badgeList: BadgeWallWidgetProps[] = []
        const isAchievedBadgePresent = _.isEmpty(achievedBadge) ? false : true
        if (isAchievedBadgePresent) {
            // duration badges have their activity in millisecond instead of minutes so divide by 60000
            const newBadgeLevel = LiveUtil.getIsBadgeTypeDuration(achievedBadge) ? Math.floor(achievedBadge.activityCount / 60000) : achievedBadge.activityCount
            badgeList.push({
                title: achievedBadge.name,
                subtitle: "Achieved",
                imageUrl: achievedBadge.imageUrl,
                badgeLevel: newBadgeLevel,
                unit: LiveUtil.getBadgeUnit(achievedBadge.subType),
                isAchieved: true,
            })
        }
        if (!_.isEmpty(achievableBadges?.badges)) {
            // Show 2 upcoming badges if achieved badge present else 3
            const badgeListAdditions: BadgeWallWidgetProps[] = await Promise.all(
                achievableBadges.badges.slice(0, isAchievedBadgePresent ? 2 : 3).map(async (achievableBadge: InSessionBadge) => {
                    const curBadge = await this.questService.getBadgeById(achievableBadge.badgeId)
                    const isBadgeTypeDuration = LiveUtil.getIsBadgeTypeDuration(curBadge)
                    // duration badges have their activity in millisecond instead of minutes so divide by 60000
                    const newBadgeLevel = isBadgeTypeDuration ? Math.floor(curBadge.activityCount / 60000) : curBadge.activityCount
                    const newUserLevel = isBadgeTypeDuration ? Math.floor((curBadge.activityCount - achievableBadge.milestoneLeft) / 60000)
                        : (curBadge.activityCount - achievableBadge.milestoneLeft)
                    return {
                        isAchieved: false,
                        title: achievableBadge.name,
                        subtitle: "Upcoming",
                        imageUrl: achievableBadge.imageUrl,
                        badgeLevel: newBadgeLevel,
                        curLevel: newUserLevel,
                        unit: LiveUtil.getBadgeUnit(achievableBadge.type),
                    }
                })
            )
            badgeList.push(...badgeListAdditions)
        }
        if (_.isEmpty(badgeList)) {
            return null
        }
        const widget: BadgeWallWidget = {
            widgetType: "BADGE_WALL_WIDGET",
            title: "Badges",
            action: {
                actionType: "NAVIGATION",
                url: "curefit://allbadgespage",
            },
            badges: badgeList,
        }
        return widget
    }

    private getBadgeWidget(fullBadgeInfo: Badge, userContext: UserContext): BadgeWidget {
        if (_.isEmpty(fullBadgeInfo)) return undefined
        const isAndroid: boolean = userContext.sessionInfo.osName.toLowerCase() === "android"
        const widget: BadgeWidget = {
            widgetType: "BADGE_WIDGET",
            title: fullBadgeInfo.name,
            subtitle: fullBadgeInfo.description,
            imageUrl: fullBadgeInfo.imageUrl,
            button: {
                shareIcon: {
                    name: isAndroid ? "share-2" : "share",
                    size: "16",
                },
                text: "Share",
                action: {
                    actionType: "SHARE_SCREENSHOT",
                    meta: {
                        ...this.shareMessage
                    }
                }
            },
        }
        return widget
    }

    private async getChallengeAchievementBadge(user: UserContext): Promise<ChallengeBadgeWidget> {

        // get all recent enrolments for user
        const { recentEnrolments } = await this.riddlerCacheService.getRecentEnrolments(user.userProfile.userId, true)
        const badgeRewards: RewardFulfilment[] = []
        for (const enrolment of recentEnrolments) {
            if (!enrolment.challenge || !enrolment.challenge.tags || !enrolment.challenge.tags.includes("PLEDGE_CHALLENGE")) {
                continue
            }
            for (const milestone of enrolment.milestones) {
                const badgeReward: RewardFulfilment = milestone.rewards && milestone.rewards.find(r => r.rewardType === "BADGE")
                if (badgeReward) {
                    const dateNow = new Date()
                    const diffInhrs = (dateNow.getTime() - badgeReward.date.getTime()) / (1000 * 60 * 60)
                    if (diffInhrs <= 1) { // hack to show only the badges which he has received in last 1 hour
                        badgeRewards.push(badgeReward)
                    }
                }
            }
        }
        if (_.isEmpty(badgeRewards)) {
            return null
        }
        badgeRewards.sort(function (a, b) { // get the most recent badge
            return b.date.getTime() - a.date.getTime()
        })
        this.logger.info(`Badges for user ${JSON.stringify(badgeRewards)}`)
        const ledgerEntry = await this.rewardService.getReward<BadgeFulfilmentResponse>(user.userProfile.userId, badgeRewards[0].rewardId)
        const badge: Badge = ledgerEntry.data && ledgerEntry.data.fulfilmentResponse.data.badge
        this.logger.info(`Ledger Response badge for user ${JSON.stringify(badge)}`)
        if (!badge) {
            return null
        }
        const widget = new ChallengeBadgeWidget()
        widget.widgetType = "CHALLENGE_ACHIEVEMENT_BADGE_WIDGET"
        widget.badgeId = badge.badgeId
        widget.badgeCategory = badge.badgeCategory
        widget.action = {
            title: "SHARE",
            actionType: "SHARE_ACTION"
        }
        widget.title = badge.name
        widget.description = badge.description
        widget.largeImgUrl = UrlPathBuilder.getBadgeImagePath(badge.badgeId, true)
        widget.shareImgUrl = UrlPathBuilder.getShareImageUrl(badge.badgeId, badge.name)
        widget.shareText = getShareText(badge.vertical, badge.type)
        return widget
    }

    private getBadgeData(badgeData: ChallengeBadgeWidget) {
        return {
            showBigBadge: true,
            action: {
                actionType: "NAVIGATION",
                url: `curefit://badgepage`,
                presentationMode: "ZOOMIN",
                meta: {
                    isAuto: true,
                    badgeId: badgeData.badgeId,
                    vertical: "CULT",
                    type: "ACTIVITY",
                    name: badgeData.title,
                    imageUrl: badgeData.imageUrl,
                    description: badgeData.description,
                    badgeCategory: badgeData.badgeCategory,
                    largeImgUrl: badgeData.largeImgUrl,
                    shareImgUrl: badgeData.shareImgUrl,
                    isAchieved: false,
                    shareText: badgeData.shareText,
                }
            }
        }
    }

    private async getPledgeChallengeData(pledgeConfig: PledgeConfig, userContext: UserContext) {
        const badgeInfo = []
        for (const conf of pledgeConfig.daysChallengeMapping) {
            badgeInfo.push({
                badgeTitle: conf.badgeTitle || pledgeConfig.defaultBadgeTitle,
                badgeImageUrl: conf.badgeImageUrl || pledgeConfig.defaultBadgeImageUrl,
                days: conf.days,
                week: conf.weeks
            })
        }
        const experimentId = "171" // owner "<EMAIL>"
        const bucketId: string = undefined
        this.logger.info(`Bucket id for user ${userContext.userProfile.userId} is ${bucketId}`)
        if (bucketId === "1") {
            return {
                shouldShowPledgeModal: true,
                type: "PICK",
                isOneWeek: false,
                BadgeINfo: badgeInfo,
                title: "Pledge for consistent workouts for few weeks to kickstart this habit",
                action: {
                    actionType: "UPDATE_PLEDGE_CHALLENGE",
                    title: "TAKE PLEDGE",
                    url: `/challenge/pledge`
                },
                daysInfo: {
                    title: "Select workout days per week",
                    defaultDays: 3,
                    totalDays: 6
                },
                weeksInfo: {
                    title: "Select number of weeks",
                    defautWeeks: 3,
                    totalWeeks: 6
                }
            }
        }
        else if (bucketId === "3") {
            return {
                shouldShowPledgeModal: true,
                type: "PICK",
                isOneWeek: true,
                BadgeINfo: badgeInfo,
                title: "Give it a strong start with a pledge to be consistent in the next 7 days!",
                action: {
                    actionType: "UPDATE_PLEDGE_CHALLENGE",
                    title: "TAKE PLEDGE",
                    url: `/challenge/pledge`
                },
                daysInfo: {
                    title: "Choose the number of workout days",
                    defaultDays: 3,
                    totalDays: 6
                },
                weeksInfo: {
                    title: "Select number of weeks",
                    defaultWeeks: 1,
                    totalWeeks: 6
                }
            }
        }
    }

    private async getChallengeStatusWidget(user: UserContext): Promise<ChallengeStatusWidget> {
        try {
            let goal: string, currentWorkout: number, diff: number
            let hasEnrollled = false
            let enrolmentId: string
            let challengeId: string
            const { recentEnrolments } = await this.riddlerCacheService.getRecentEnrolments(user.userProfile.userId, true)
            for (const enrolment of recentEnrolments) {
                if (enrolment.challenge && enrolment.challenge.tags && enrolment.challenge.tags.includes("PLEDGE_CHALLENGE")) {
                    hasEnrollled = true
                    goal = enrolment.challenge.constants.find(_ => _.name === "MIN_WEEKLY_WORKOUT_DAYS_TO_ACHIEVE_STREAK").value
                    const metrics = enrolment.metrics.find(_ => _.metricType === "CURRENT_WEEK_WORKOUT_DAYS")
                    if (!_.isNil(metrics)) {
                        currentWorkout = metrics.value // workout done in current week
                    } else {
                        continue
                    }
                    let startDate = TimeUtil.formatDateInTimeZone(enrolment.userTimezone, enrolment.startDate)
                    const currentDate = TimeUtil.formatDateInTimeZone(enrolment.userTimezone, new Date())
                    while (TimeUtil.addDays(enrolment.userTimezone, startDate, 7) < currentDate) {
                        startDate = TimeUtil.addDays(enrolment.userTimezone, startDate, 7)
                    }
                    startDate = TimeUtil.addDays(enrolment.userTimezone, startDate, 7)
                    diff = TimeUtil.diffInDays(enrolment.userTimezone, startDate, currentDate)
                    enrolmentId = enrolment.enrolmentId
                    challengeId = enrolment.challengeId
                    // break
                }
            }
            if (hasEnrollled) {
                if (diff > 0 && Number(goal) - currentWorkout <= diff) {
                    const widget = new ChallengeStatusWidget()
                    if (currentWorkout === 1) {
                        widget.title = `Update on pledge: ${currentWorkout} workout done, ${Number(goal) - currentWorkout} more to go in the next ${diff} days. Keep it up!`
                    } else {
                        widget.title = `Update on pledge: ${currentWorkout} workouts done, ${Number(goal) - currentWorkout} more to go in the next ${diff} days. Keep it up!`
                    }
                    widget.action = {
                        actionType: "NAVIGATION",
                        url: `curefit://challengedetails?id=${challengeId}&ref=${"ENROLMENT"}&refId=${enrolmentId}`,
                        meta: {
                            enrolmentId: enrolmentId
                        }
                    }
                    return widget
                } else {
                    return null
                }
            }
            return null
        }
        catch (e) {
            this.logger.info(`Error while creating pledge challenge status widget ${JSON.stringify(e)}`)
            return null
        }
    }

    private isTrialOrTrialExpiredOnlyUser(userSegments: string[], userContext: UserContext) {
        return !AppUtil.isInternationalApp(userContext) && !_.isEmpty(userSegments) && (userSegments.includes(LIVE_FREE_TRIAL_ONGOING_SEGMENT) || userSegments.includes(LIVE_FREE_TRIAL_EXPIRED_SEGMENT))
    }

    private async getShareTitleAndMessage(userContext: UserContext, session: DigitalCatalogueEntryV1, card?: any) {
        const defaultUrl = AppUtil.isInternationalApp(userContext) ? "https://cfintl.app.link" : "https://cure.app.link/RAbQSTjzFkb"
        let shareMessage = `Just completed a ${session.title} LIVE class on the cult.fit app! You too can workout from home on ${defaultUrl}!`
        if (card) {
            const shareUrl = await this.classInviteLinkCreator.getLiveClassReferralLink(userContext, (<any>session)._id, session.title, session.description, session.format, { ...card, redirectionUrl: "curefit://classbookingv2?productType=LIVE_FITNESS", oneTimeLink: true })
            shareMessage = `I just finished a ${session.title} LIVE class and I feel awesome! You too can workout from home on the cult.fit app *${shareUrl || defaultUrl}*`
        }
        return {
            shareTitle: `LIVE Class Report`,
            shareMessage
        }
    }

    private async getDIYShareTitleAndMessage(userContext: UserContext, session: DIYFitnessProductExtended, card?: any) {
        const defaultUrl = AppUtil.isInternationalApp(userContext) ? "https://cfintl.app.link" : "https://cure.app.link/RAbQSTjzFkb"
        let shareMessage = `Just completed a ${session.title} DIY class on the cult.fit app! You too can workout from home on ${defaultUrl}!`
        if (card) {
            const shareUrl = await this.classInviteLinkCreator.getLiveClassReferralLink(userContext, (<any>session)._id, session.title, session.subTitle, session.format, { ...card, redirectionUrl: "curefit://classbookingv2?productType=LIVE_FITNESS", oneTimeLink: true })
            shareMessage = `I just finished a ${session.title} LIVE class and I feel awesome! You too can workout from home on the cult.fit app *${shareUrl || defaultUrl}*`
        }
        return {
            shareTitle: `LIVE Class Report`,
            shareMessage
        }
    }

    private async getClassResponse(userContext: UserContext, contentId: string, isDIY?: boolean) {
        if (isDIY) {
            return (await this.diyFulfilmentService.getDIYFitnessProductsByProductIds(userContext.userProfile.userId, [contentId], AppUtil.getTenantFromUserContext(userContext)))[0]
        } else {
            return this.diyFulfilmentService.getDigitalCatalogueEntry(contentId)
        }
    }

    private async getSubmitAction(userContext: UserContext, segmentService: ISegmentService): Promise<Action> {

        if (!AppUtil.isLiveNewClassReportSupported(userContext)) {
            return null
        }

        const isCommunityEnabled = await AppUtil.isCommunityEnabled(userContext, segmentService)
        const isAndroid = _.get(userContext, "sessionInfo.osName", "").toLowerCase() === "android"

        const mutipleShareAction: Action = {
            title: "SHARE YOUR ACHIEVEMENT",
            actionType: "SHARE_SCREENSHOT",
            meta: {
                ...this.shareMessage,
                showOptions: isCommunityEnabled
            }
        }

        const metaAction: Action = {
            actionType: "NAVIGATION",
            url: "curefit://sharingcarousel?title=Share Report",
            meta: {
                action: mutipleShareAction,
            },
            analyticsData: {
                eventKey: "button_click_event",
                eventData: {
                    source: "reportsharefloatingCTA",
                    productType: "liveclassreport",
                    actionType: "sharefullreport"
                }
            }
        }

        const submitAction: Action = {
            actionType: "SHARE_MUTIPLE_CARD",
            title: isAndroid ? "SHARE FULL REPORT" : "SHARE REPORT",
            meta: {
                action: metaAction
            }
        }
        return submitAction

    }

    private async getRecommendedPostWorkoutWidget(classId: string) {
        const meditation = postWorkoutMeditations[classId.charCodeAt(classId.length - 1) % 3]
        const contentId = meditation.contentId
        const cardAction: Action = {
            actionType: "PLAY_VIDEO",
            title: "BEGIN",
            meta: {
                content: {
                    id: contentId,
                    type: meditation.contentType,
                    format: meditation.contentFormat,
                    URL: meditation.contentType === "audio" ? UrlPathBuilder.getAudioPath(contentId, meditation.contentFormat) : UrlPathBuilder.getVideoPath(contentId, meditation.contentType),
                    absoluteUrl: meditation.contentType === "audio" ? UrlPathBuilder.getAudioAbsolutePath(contentId, meditation.contentFormat) : UrlPathBuilder.getVideoPath(contentId, meditation.contentFormat)
                },
                queryParams: {
                    activityId: meditation.activityId,
                    packId: meditation.packId,
                    contentId: contentId,
                    consumptionRequired: true,
                    activityType: "DIY_MEDITATION",
                    title: meditation.title,
                    image: meditation.imageUrl
                },
                title: meditation.title,
                checkDownloadStatus: true
            }
        }
        const postWorkoutMeditationListItem: CardListItem = new PostWorkMeditationCardListItem()
        postWorkoutMeditationListItem.action = cardAction
        postWorkoutMeditationListItem.leftInfo = {
            images: [meditation.imageUrl]
        }
        postWorkoutMeditationListItem.rightInfo = {
            action: cardAction,
            moreAction: undefined
        }
        postWorkoutMeditationListItem.subTitle = "Taking care of your mind is #justasimportant"
        postWorkoutMeditationListItem.title = "Complete your workout with a 5-min meditation"
        return new CardListWidgetView("Post-workout meditation", "MEDICAL_REPORTS", [postWorkoutMeditationListItem], undefined, undefined, { marginHorizontal: 20, marginVertical: 10 })
    }

    static async getAttendingUsersData(userId: string, contentId: string, diyFulfilmentService: IDIYFulfilmentService, userCache: CacheHelper, contentType: SessionContentType): Promise<any[]> {
        if (_.isEmpty(userId) || _.isEmpty(contentId)) return null

        const socialPeers = (await eternalPromise(diyFulfilmentService.getSocialPeersForSession(userId, contentId))).obj
        if (_.isEmpty(socialPeers)) return null

        const attendingUserProfilesPromise = userCache.getUsers(socialPeers)
        const attendingUserMetricsPromise = diyFulfilmentService.getUserScoreMetricsBulkV2(socialPeers, contentId, contentType)
        const profilesMetricsResponse = (await eternalPromise(Promise.all([attendingUserProfilesPromise, attendingUserMetricsPromise]))).obj
        if (_.isEmpty(profilesMetricsResponse)) return null

        const [attendingUserProfiles, attendingUserMetrics] = profilesMetricsResponse
        if (_.isEmpty(attendingUserProfiles) || _.isEmpty(attendingUserMetrics)) return null

        const allAttendingUsersData = attendingUserMetrics.map((attendingUserMetric) => {
            if (_.isEmpty(attendingUserMetric)) return null
            const profile = attendingUserProfiles[attendingUserMetric.userId]
            if (_.isEmpty(profile)) return null
            return {
                userId: profile.id,
                firstName: profile.firstName,
                profilePictureUrl: profile.profilePictureUrl,
                score: attendingUserMetric?.score,
                rank: attendingUserMetric?.rank,
                displayName: `${profile.firstName}`
            }
        })

        if (_.isEmpty(allAttendingUsersData)) return null
        return allAttendingUsersData.filter(val => val)
    }

    private isPercentileCalculationPossible(userScoreMetrics: UserScoreMetricsResponse) {
        return userScoreMetrics.rank > 0
    }

    private filterAndMapEnergyScore(userScoreMetrics: UserScoreMetricsResponse, startTime: number, contentDuration: number, isDIY: boolean): { [time: string]: number } {

        let energyHistory: { [time: string]: number } = userScoreMetrics?.energyHistory || {}
        const filteredAndMappedEnergyHistory: { [time: string]: number } = {}

        let atleastOneNonZero = false

        if (isDIY && !_.isEmpty(userScoreMetrics?.energyHistoryV2)) {
            energyHistory = userScoreMetrics.energyHistoryV2
            _.forEach(energyHistory, (value, time) => {
                if (!atleastOneNonZero && value > 0) {
                    atleastOneNonZero = true
                }
                const timeInteger = integer(time)
                filteredAndMappedEnergyHistory[(Math.round(timeInteger / 60000)).toString()] = value
            })
        } else {
            _.forEach(energyHistory, (value, time) => {
                const timeInteger = integer(time)
                if (timeInteger - startTime > 0 && timeInteger - startTime < contentDuration) {
                    if (!atleastOneNonZero && value > 0) {
                        atleastOneNonZero = true
                    }
                    const mappedKey = (Math.round((timeInteger - startTime) / 60000)).toString()
                    filteredAndMappedEnergyHistory[mappedKey] = value
                }
            })
        }
        filteredAndMappedEnergyHistory["0"] = 0
        filteredAndMappedEnergyHistory[(contentDuration / 60000).toString()] = 0
        if (atleastOneNonZero) {
            return filteredAndMappedEnergyHistory
        }
        return {}
    }

    private async getRecommendationWidget(userContext: UserContext, contentId: string) {
        const sessionRecommendationWidget = await new VideoCardCarouselWidgetView().buildView(this.serviceInterfaces, userContext, { sessionId: contentId })
        if (!_.isNil(sessionRecommendationWidget.layoutProps)) {
            sessionRecommendationWidget.layoutProps.verticalMargin = true
            sessionRecommendationWidget.layoutProps.backgroundColor = "rgb(244, 244, 244)"
        }
        return sessionRecommendationWidget
    }

    private async getReportFeedbackWidget(userId: string, reportId: string): Promise<any> {
        let feedback: Feedback = await this.feedbackDao.findOne({ itemId: reportId, userId })
        if (feedback === undefined || feedback === null) {
            feedback = await this.feedbackBusiness.createLiveSessionReportFeedback(userId, reportId)
        }
        if (feedback && (feedback.rating === "NOT_RATED" || feedback.rating === "DISMISSED")) {
            const { ratings } = await this.feedbackPageConfigV2Cache.getFeedbackObjV2(feedback)
            const icons = ratings.map(rating => rating.icon)
            return new ProductFeedbackWidget(await this.feedbackPageConfigV2Cache.getQuestionV2(feedback), feedback.feedbackId, undefined, icons, "CENTER", "Your feedback will help us serve you better")
        }
        return undefined
    }

    static isTopPerformer(userScoreMetrics: UserScoreMetricsResponse) {
        return userScoreMetrics?.rank >= 1 && userScoreMetrics?.rank <= 3
    }

    static getOriginalSessionId(session: any, isDIY: boolean) {
        if (isDIY) {
            return session.productId
        }
        return session.originalContentId || session._id
    }

    static getSessionId(session: any, isDIY: boolean) {
        if (isDIY) {
            return session.productId
        }
        return session._id
    }

    static async getReportSelfieShareCardWidget({ userContext, source, userScoreMetrics, session, momentsImage, showingMessage, card, userCache, diyFulfilmentService, logger, shareMessage, contentType, userSessionId, isDIY, showingNewYogaReport, yogaTimeStampedProgressPillsBuilder, achievementImageUrl }: SelfieShareCardParams): Promise<any> {
        const userData = await userCache.getUser(userContext.userProfile.userId)
        let timestampedProgressData
        if (showingNewYogaReport && yogaTimeStampedProgressPillsBuilder && !_.isEmpty(userScoreMetrics)) {
            timestampedProgressData = yogaTimeStampedProgressPillsBuilder.buildView(userScoreMetrics)
        }
        const userName = _.isNil(userData) ? "" : userData.firstName
        const userNameCapitals = userName ? userName.toUpperCase() : ""

        const isReportIncomplete = AppUtil.isRelaxingMomentAndReportSupported(userContext) ? false : (_.isNil(userScoreMetrics) || userScoreMetrics?.reportStatus === SessionReportStatus.INCOMPLETE)
        const isEnergyMeterSupported = session.features?.includes("ENERGY") && !_.isNil(userScoreMetrics?.energyMeterState) && userScoreMetrics.energyMeterState !== "DEVICE_UNSUPPORTED"
        const shouldShowEnergyInfolet = (!_.isNil(userScoreMetrics?.score) || isEnergyMeterSupported) && !DigitalReportViewBuilder.shouldOmitReportMetric(session, "ENERGY")
        const shouldShowRankInfolet = (!_.isNil(userScoreMetrics?.rank) || isEnergyMeterSupported) && !DigitalReportViewBuilder.shouldOmitReportMetric(session, "RANK")
        const recommendEnergyMeter = (isEnergyMeterSupported && _.isNil(userScoreMetrics?.score))
        const formattedTime = !AppUtil.isTVApp(userContext) ? DigitalReportViewBuilder.millisToMinutesAndSeconds(userScoreMetrics?.playbackMillis || 0) : DigitalReportViewBuilder.millisToTimeUnit(userScoreMetrics?.playbackMillis || 0)
        const timeUnit = !AppUtil.isTVApp(userContext) ? "Min" : DigitalReportViewBuilder.getMillisTimeUnit(userScoreMetrics?.playbackMillis || 0)

        const isLiveNewClassReportSupported = AppUtil.isLiveNewClassReportSupported(userContext)
        const isLiveMomentsOnReportSupported = AppUtil.isLiveMomentOnReportSupported(userContext)
        const isSecondTrainerImageSupported = AppUtil.isLiveSecondTrainerImageSupported(userContext)
        const tenant = AppUtil.getTenantFromUserContext(userContext)

        const isDark = userContext?.sessionInfo.isDark

        const workoutDurationInfolet = {
            title: (session.format === "MEDITATION") ? isLiveNewClassReportSupported ? "MEDITATION DURATION" : "MEDITATION\nDURATION" : isLiveNewClassReportSupported ? "WORKOUT DURATION" : "WORKOUT\nDURATION",
            data: `${formattedTime}`,
            units: timeUnit,
            headerColor: isDark ? "#888e9e" : "#ef8685"
        }

        const metrics: { title: string, data: string, units: string, headerColor?: string, extraData?: any }[] = [workoutDurationInfolet]

        if (AppUtil.isTVApp(userContext)) {
            if (_.isFinite(userScoreMetrics?.caloriesBurnt)) {
                metrics.push({
                    title: "CALORIES BURNT",
                    data: `${userScoreMetrics.caloriesBurnt}`,
                    units: "Cal",
                    headerColor: isDark ? "#ff7d7d" : "rgba(0,0,0,0.5)"
                })
            }
            if (shouldShowEnergyInfolet) {
                metrics.push({
                    title: "ENERGY SCORE",
                    data: recommendEnergyMeter ? "-" : `${Math.round(userScoreMetrics?.score)}`,
                    units: "",
                    headerColor: isDark ? "#ffba6d" : "#f5bb86",
                })
            }
            if (userScoreMetrics && (userScoreMetrics?.booster75 || userScoreMetrics?.booster100)) {
                metrics.push({
                    title: "BOOSTERS EARNED",
                    data: "",
                    units: "",
                    headerColor: isDark ? "#fb9871" : "rgb(251, 152, 113)",
                    extraData: [
                        userScoreMetrics.booster75 && {
                            "icon": "/image/tv/booster-green.png",
                            "text": userScoreMetrics.booster75
                        },
                        userScoreMetrics.booster100 && {
                            "icon": "/image/tv/booster-red.png",
                            "text": userScoreMetrics.booster100
                        }
                    ].filter(item => item)
                })
            }
        } else {
            if (shouldShowRankInfolet) {
                metrics.push({
                    title: isLiveNewClassReportSupported ? "CLASS RANK" : "CLASS\nRANK",
                    data: recommendEnergyMeter ? "-" : `${userScoreMetrics.rank || "-"}`,
                    units: _.isNil(userScoreMetrics?.totalCount) ? "" : `/${userScoreMetrics.totalCount}`,
                    headerColor: "#eabf5f",
                })
            }
            if (shouldShowEnergyInfolet) {
                metrics.push({
                    title: isLiveNewClassReportSupported ? "ENERGY SCORE" : "ENERGY\nSCORE",
                    data: recommendEnergyMeter ? "-" : `${Math.round(userScoreMetrics?.score)}`,
                    units: "",
                    headerColor: "#f5bb86",
                })
            }
            const caloriesBurnt = userScoreMetrics?.caloriesBurnt

            if (!_.isNil(caloriesBurnt) && _.isFinite(caloriesBurnt) && !DigitalReportViewBuilder.shouldOmitReportMetric(session, "CALORIES")) {
                metrics.push({
                    title: "Appx Calorie Burnt".toUpperCase(),
                    data: `${caloriesBurnt}`,
                    units: "Cal",
                })
            }

            const averageHeartRate: number = userScoreMetrics?.averageHeartRate
            if (!_.isNil(averageHeartRate) && _.isFinite(averageHeartRate)) {
                metrics.push({
                    title: "Avg Heart Rate".toUpperCase(),
                    data: `${Math.round(averageHeartRate)}`,
                    units: "BPM"
                })
            }
        }

        const sessionTrainerImage = (isSecondTrainerImageSupported && session?.trainerImages?.mobileImageV2) ? session.trainerImages.mobileImageV2 : session?.trainerImages?.mobileImage
        const defaultImage = session?.format?.includes("YOGA") ? "image/diy/DefaultYogaBannerImage2.png" : "image/diy/CurefitDefaultImage1.png"

        const celebrityImageUrl = session?.isMasterClass && sessionTrainerImage && `https://cdn-images.cure.fit/www-curefit-com/image/upload/${sessionTrainerImage}`
        let celebrityImageFilter: string
        if (!isReportIncomplete && session?.isMasterClass) {
            if (RENO_CLASS_CONTENT_IDS.includes(this.getOriginalSessionId(session, isDIY))) {
                celebrityImageFilter = "RENO_MASTERCLASS"
            } else {
                celebrityImageFilter = "WE_ARE_CULT_STAR"
            }
        }
        const achievement = isReportIncomplete ? "Workout longer to get detail report here" : (session?.isMasterClass && session?.trainerName) ? `I worked out with ${session.trainerName}!` : "You did great today!"

        let userUploadedImage
        if (userScoreMetrics?.imageDetails?.imageUrl) {
            if (userScoreMetrics.imageDetails.imageUrl.includes("social-media/image")) {
                userUploadedImage = "social-media-image"
            } else {
                userUploadedImage = (process.env.ENVIRONMENT === "STAGE" || process.env.ENVIRONMENT === "LOCAL") ?
                    "cult-api-fitness-report-pictures-stage" : "cult-api-fitness-report-pictures-prod"
            }
            userUploadedImage += "/" + path.basename(userScoreMetrics.imageDetails.imageUrl)
        }
        let imageUrl
        if (userUploadedImage) {
            imageUrl = userUploadedImage
        } else if (userScoreMetrics?.imageDetails?.imageUrl === "") {
            imageUrl = ""
        } else {
            imageUrl = momentsImage || celebrityImageUrl || defaultImage
        }

        const startTime = userScoreMetrics?.startTime ? userScoreMetrics.startTime : session.scheduledTimeEpoch
        const timeFormatted = startTime
            ? momentTz
                .tz(
                    startTime,
                    userContext.userProfile.timezone
                )
                .format(isLiveNewClassReportSupported ? "ddd MMM D | hh:mm A" : "ddd MMM D") : undefined
        const digitalShareCardWidget = {
            widgetType: "DIGITAL_SHARE_CARD_WIDGET",
            header: {
                title: session.title,
                subTitle: `${timeFormatted
                    ? isLiveNewClassReportSupported
                        ? timeFormatted
                        : timeFormatted.toUpperCase()
                    : "COMPLETED"
                    } | ${Math.round(session.duration / 60000)} ${isLiveNewClassReportSupported ? "Min" : "MIN"
                    }`,
                backgroundImageUrl: isReportIncomplete
                    ? "/image/livefit/app/report-header-bg-incomplete.png"
                    : DigitalReportViewBuilder.isTopPerformer(userScoreMetrics)
                        ? "/image/livefit/app/report-header-bg-top.png"
                        : "/image/livefit/app/report-header-bg-all.png",
                noPaddingTop: source === "class_moments_page" || showingMessage,
            },
            metrics: metrics,
            user: {
                userId: userContext.userProfile.userId,
                firstName: userData.firstName,
                profilePictureUrl: userData.profilePictureUrl,
                score: userScoreMetrics?.score,
                inClassSession: !_.isEmpty(userScoreMetrics?.achievedGoals) ? {
                    achievedGoals: userScoreMetrics?.achievedGoals,
                    imageUrl: (userScoreMetrics?.achievedGoals[0].info as any).image, // todo fix
                    cheerText: "Awesome!",
                    totalAchievedGoalText:
                        userScoreMetrics?.achievedGoals.length == 1 ? "You have unlocked an achievement" : "",
                    action: {
                        actionType: "NAVIGATION",
                        title: "View All",
                        url: "curefit://allbadgespage",
                    },
                } : {},
            },
            friends: !isLiveNewClassReportSupported
                ? await DigitalReportViewBuilder.getAttendingUsersData(
                    userContext.userProfile.userId,
                    (<any>session)._id,
                    diyFulfilmentService,
                    userCache,
                    contentType
                )
                : undefined,
            button: {
                text: isLiveNewClassReportSupported
                    ? "EDIT AND SHARE YOUR ACHIEVEMENT"
                    : "SHARE YOUR ACHIEVEMENT",
                secondaryText: isLiveNewClassReportSupported
                    ? "SHARE YOUR ACHIEVEMENT"
                    : undefined,
                shareIcon: isLiveNewClassReportSupported
                    ? "/image/pulse/share-icon-android.png"
                    : undefined,
                action: {
                    actionType: "SHARE_SCREENSHOT",
                    meta: {
                        ...shareMessage,
                    },
                },
            },
            descriptionImage:
                isLiveNewClassReportSupported &&
                    DigitalReportViewBuilder.isTopPerformer(userScoreMetrics)
                    ? "image/diy/TopPerformer2.png"
                    : undefined,
            isReportV2: isLiveNewClassReportSupported,
            isMoment: false,
            imageRatio: isLiveMomentsOnReportSupported
                ? LIVE_REPORT_V2_ASPECT_RATIO
                : undefined,
            imageRatioStr: LIVE_REPORT_V2_ASPECT_RATIO_STR,
            cardV2SceneStyle: {
                marginTop: 0,
                marginBottom: 10,
            },
            useGAuto: userUploadedImage || momentsImage ? true : false,
            imageUrl,
            noCardContainerMargin:
                isLiveNewClassReportSupported && showingMessage,
            fallbackImageUrl: celebrityImageUrl,
            imageFilter:
                userScoreMetrics?.imageDetails?.imageFilter || celebrityImageFilter,
            achievement:
                !userUploadedImage && momentsImage
                    ? undefined
                    : isReportIncomplete
                        ? "Workout longer to see a detailed report here."
                        : achievement,
            isReportIncomplete,
            contentId: this.getSessionId(session, isDIY),
            userSessionId,
            fitnessReportFilters: getLiveReportFilters(
                userNameCapitals,
                this.getOriginalSessionId(session, isDIY)
            ),
            containerStyle: card && { marginBottom: 0 },
            hasDividerBelow: !card,
            ...(showingNewYogaReport
                ? {
                    hideFilterOverlay: true,
                    theme: "LIGHT",
                    timestampedProgressData,
                    overlayTitle: `Kudos, ${userData.firstName}`,
                }
                : undefined),
            labels: LiveUtil.isConsideredInteractiveSessionForPresentation(session.preferredStreamType) ? LiveUtil.getLiveInteractiveLabels() : undefined,
            achievementImageUrl
        }

        try {
            logger.info(`DigitalReportViewBuilder_selfieShareCard, userId: ${userContext.userProfile.userId}, contentId: ${(<any>session)._id}, response: ${JSON.stringify(digitalShareCardWidget)}`)
        }
        catch (e) {
            logger.info(`DigitalReportViewBuilder_selfieShareCard Failed to log response for userId: ${userContext.userProfile.userId}, contentId: ${(<any>session)._id}`)
        }


        return digitalShareCardWidget
    }

    getYogaHeaderWidget(userContext: UserContext, session: DigitalCatalogueEntryV1) {
        const subTitleDate = momentTz.tz(session.scheduledTimeEpoch, userContext.userProfile.timezone).format("D MMM")
        return {
            widgetType: "HEADER_WIDGET",
            titleStyle: {
                fontSize: 20,
            },
            widgetTitle: {
                title: "Performance Report",
                subTitle: `${subTitleDate} -  ${session.title} `,
            },
            headerStyle: "HEADER_WIDGET_HEADER_STYLE",
            subTitleStyle: {
                color: "#BBBCBC",
                fontFamily: AppFont.Medium,
                marginTop: 0,
            },
            hasDividerBelow: false,
        }
    }

    async getYogaImageOverlayCardContainerWidget(userContext: UserContext, session: DigitalCatalogueEntryV1, userScoreMetrics: UserScoreMetricsResponse) {
        const widgets = _.compact(await Promise.all([
            this.yogaReportCardWidgetViewBuilder.buildView(userContext, session, userScoreMetrics as any, this.shareMessage),
            this.yogaBarGraphWidgetViewBuilder.buildView(userScoreMetrics as any)
        ]))
        return !_.isEmpty(widgets) ? {
            widgetType: "IMAGE_OVERLAY_CARD_CONTAINER_WIDGET",
            customOverlayContainerStyle: {
                marginTop: 0,
            },
            noInnerPadding: true,
            sceneStyle: {
                marginVertical: 20,
            },
            widgets,
        } : undefined
    }

    yogaAsanaInfo(userScoreMetrics: UserScoreMetricsResponse): ProductListWidget {
        const items = userScoreMetrics?.poseQualityMap?.map((poseQuality: any) => {
            const action = !_.isEmpty(poseQuality.videoUrl) ? {
                actionType: "PLAY_VIDEO",
                title: "WATCH",
                url: ActionUtil.videoUrl(poseQuality.videoUrl)
            } : undefined
            return {
                title: poseQuality.name,
                imageV2: {
                    url: poseQuality.imageUrl,
                    cloudinaryConfig: ["c_fill", "g_face"],
                },
                image: poseQuality.imageUrl,
                cardAction: action || {},
                moreAction: action,
                roundedCorners: true,
                imagePropStyle: { height: 56, width: 76 },
                sceneStyle: { minHeight: null, alignItems: "center" } as any,
                imageContainerStyle: { height: 56, width: 76 },
            }
        })

        if (!_.isEmpty(items)) {
            return {
                type: "MEDIUM",
                header: { title: "Based on your performance, focus on" },
                items,
                hideSepratorLines: true,
                hasDividerBelow: true,
                widgetType: "PRODUCT_LIST_WIDGET",
                style: {
                    marginHorizontal: 20,
                    shadowColor: "#000000",
                    shadowOffset: {
                        width: 0,
                        height: 1,
                    },
                    shadowRadius: 2,
                    shadowOpacity: 0.1,
                    elevation: 2,
                    backgroundColor: "white",
                    borderRadius: 9,
                    marginVertical: 10
                },
            }
        }
    }

    yogaCorrectDeviceSetUpBanner() {
        const carouselItems = [{
            id: "yoga_correct_setup_banner",
            image: "image/diy/CorrectYogaOrientationBannerCropped.png",
            action: undefined as Action
        }]
        return {
            ...new BannerCarouselWidget("746:670", carouselItems,
                {
                    showPagination: false,
                    v2: true,
                    alignment: "center",
                    backgroundColor: "",
                    autoScroll: false,
                    enableSnap: false,
                    useShadow: false,
                    roundedCorners: true,
                    noVerticalPadding: true,
                    verticalPadding: 0,
                    edgeToEdge: false,
                    interContentSpacing: 20,
                    bannerOriginalWidth: 746,
                    bannerOriginalHeight: 670,
                    bannerWidth: 335,
                    bannerHeight: 300,
                    noAutoPlay: true,
                    containerStyle: {
                        marginVertical: 10,
                        shadowColor: "#000000",
                        shadowOffset: {
                            width: 0,
                            height: 1,
                        },
                        shadowRadius: 2,
                        shadowOpacity: 0.1,
                        elevation: 2,
                    },
                }, carouselItems.length, false, false, false),
            contentMetric: { bannerId: "yoga_correct_setup_banner" },
            hasTopPadding: false,
            hasDividerBelow: false,
        }
    }

    async getReportShareCardWidget(session: DigitalCatalogueEntryV1, userContext: UserContext, userScoreMetrics: UserScoreMetricsResponse, contentType: SessionContentType, card?: any, source?: LIVE_CLASS_REPORT_SOURCE): Promise<any> {
        const userData = await this.userCache.getUser(userContext.userProfile.userId)
        const userName = _.isNil(userData) ? "" : userData.firstName
        // let bodyPartIds = session.bodyParts
        let bodyPartIds: string[] = []
        if (!_.isEmpty(session.wodId)) {
            const wod: SimpleWod = await this.herculesService.getSimpleWodById(session.wodId)
            if (!_.isEmpty(wod) && !_.isEmpty(wod.parts)) {
                wod.parts.forEach(part => {
                    part.movements.forEach(movement => {
                        if (movement && movement.publish) {
                            if (movement.bodyParts) {
                                movement.bodyParts.forEach(tag => {
                                    bodyPartIds.push(tag)
                                })
                            }
                        }
                    })
                })
            }
        }
        if (_.isEmpty(bodyPartIds)) {
            bodyPartIds = session.bodyParts
        }
        const bodyPartMap = new Map<string, BodyPart>()
        await this.herculesService.getAllBodyParts().then(allBodyParts => {
            allBodyParts.forEach(bodyPart => bodyPartMap.set(bodyPart._id, bodyPart))
        })
        const bodyParts = _.isNil(bodyPartIds) ? [] : bodyPartIds.map(bodyPartId => bodyPartMap.get(bodyPartId))
        const muscleGroupData: BodyPart[] = bodyParts
            .filter(
                bodyPart =>
                    !_.isNil(bodyPart) && Array.isArray(bodyPart.media) &&
                    bodyPart.media[0] &&
                    !_.isEmpty(bodyPart.media[0].url) &&
                    bodyPart.media[0].url.startsWith("/image")
            )
            .slice(0, 3)

        const shouldMuscleGroupBeDisplayed = muscleGroupData.length > 0 && AppUtil.isPulseMuscleGroupSupported(userContext.sessionInfo.osName, userContext.sessionInfo.appVersion)

        const isEnergyMeterSupported = session.features.includes("ENERGY") && !_.isNil(userScoreMetrics.energyMeterState) && userScoreMetrics.energyMeterState !== "DEVICE_UNSUPPORTED"
        const shouldShowEnergyInfolet = !_.isNil(userScoreMetrics?.score) || isEnergyMeterSupported
        const recommendEnergyMeter = (isEnergyMeterSupported && _.isNil(userScoreMetrics?.score))
        const isLiveNewClassReportSupported = AppUtil.isLiveNewClassReportSupported(userContext)

        const formattedTime = DigitalReportViewBuilder.millisToMinutesAndSeconds(userScoreMetrics.playbackMillis)

        const workoutDurationInfolet = {
            fancyTextHeader: (session.format === "MEDITATION") ? "Meditation\nDuration" : "Workout\nDuration",
            data: `${formattedTime}`,
            units: "Min"
        }

        const energyScoreInfolet = {
            fancyTextHeader: "Energy\nScore",
            data: recommendEnergyMeter ? "-" : `${Math.round(userScoreMetrics?.score)}`,
            units: "",
            headerColor: "#f1506e",
            [recommendEnergyMeter ? "dataColor" : undefined]: "#949494"
        }

        const pulse_metrics = [workoutDurationInfolet]
        if (shouldShowEnergyInfolet) {
            pulse_metrics.push(energyScoreInfolet)
        }

        const pulseSharedCardWidget = {
            widgetType: "PULSE_SHARE_CARD_WIDGET",
            header: {
                title: `Kudos${userName ? ` ${userName}` : ""}!`,
                backgroundImageUrl: DigitalReportViewBuilder.isTopPerformer(userScoreMetrics) ? "/image/livefit/report_bg_top_performer.png" : "/image/pulse/report-bg-image.png"
            },
            gradientBg: true,
            pulse_metrics: pulse_metrics,
            [shouldMuscleGroupBeDisplayed ? "muscle_groups" : undefined]: {
                title: "Muscle Groups",
                data: muscleGroupData.map(x => ({
                    imageUri: x.media[0].url,
                    caption: x.title
                }))
            },
            footers: [
                {
                    text: `${session.title}`,
                    bold: true
                },
                {
                    text: session.scheduledTimeEpoch
                        ? momentTz.tz(session.scheduledTimeEpoch, userContext.userProfile.timezone).format("ddd MMM D")
                        : "",
                    bold: false
                },
                {
                    text: `${session.duration / 60000} Min`,
                    bold: false
                }
            ],
            user: {
                userId: userContext.userProfile.userId,
                firstName: userData.firstName,
                profilePictureUrl: userData.profilePictureUrl,
                score: userScoreMetrics?.score
            },
            friends: !isLiveNewClassReportSupported ? await DigitalReportViewBuilder.getAttendingUsersData(userContext.userProfile.userId, (<any>session)._id, this.diyFulfilmentService, this.userCache, contentType) : undefined,
            ["share_button"]: {
                text: "SHARE YOUR ACHIEVEMENT",
                iconUri:
                    CdnUtil.getCdnUrl("curefit-content/image/pulse/share-icon.png"),
                action: {
                    actionType: "SHARE_SCREENSHOT",
                    meta: {
                        ...this.shareMessage
                    }
                }
            },
            containerTopPadding: source === "class_moments_page" ? 40 : undefined,
            noCardBackground: source === "class_moments_page"
        }
        return pulseSharedCardWidget
    }

    static millisToMinutesAndSeconds(millis: number) {
        const { minutes, seconds } = this.getMinutesAndSeconds(millis)
        return minutes + ":" + (seconds < 10 ? "0" : "") + seconds
    }

    static getMinutesAndSeconds(millis: number) {
        return { minutes: Math.floor(millis / 60000), seconds: Math.floor((millis % 60000) / 1000) }
    }

    static millisToTimeUnit(millis: number) {
        const { minutes, seconds } = this.getMinutesAndSeconds(millis)
        return (minutes > 0 ? minutes + ":" : "") + (seconds < 10 ? "0" : "") + seconds
    }

    static getMillisTimeUnit(millis: number) {
        const { minutes } = this.getMinutesAndSeconds(millis)
        if (minutes > 0) {
            return "Min"
        } else {
            return "Sec"
        }
    }

    private getClassPerformanceWidget(userContext: UserContext, userScoreMetrics: UserScoreMetricsResponse, session: any, filteredEnergyHistory: { [time: string]: number }, multipleShareSupported: boolean) {
        const formattedTime = DigitalReportViewBuilder.millisToMinutesAndSeconds(userScoreMetrics.playbackMillis)
        const percentile = ((userScoreMetrics.totalCount - userScoreMetrics.rank) * 100 / (userScoreMetrics.totalCount - 1))
        let meta
        if (multipleShareSupported) {
            meta = {
                title: "Performance report",
                shareConfig: {
                    text: "SHARE",
                    action: {
                        actionType: "SHARE_SCREENSHOT",
                        meta: {
                            ...this.shareMessage,
                            "source": "performace-report"
                        }
                    }
                },
                footer: filteredEnergyHistory && _.size(filteredEnergyHistory) > 2 && this.getBarGraphWidget(userContext, filteredEnergyHistory)
            }
        }
        const sep = AppUtil.isInternationalTLApp(userContext) ? " " : "\n"
        return {
            widgetType: "DIGITAL_CLASS_PERFORMANCE_WIDGET",
            hasDividerBelow: false,
            intensity_info: {
                fancyTextHeader: (session.format === "MEDITATION") ? `Meditation${sep}Duration` : `Workout${sep}Duration`,
                data: `${formattedTime}`,
                units: "Min",
                headerColor: "#ffc431",
                big: true,
            },
            auxiliary_data: [
                {
                    header: `Better${sep}Than`,
                    dataText: `${(percentile > 0) ? (Number.isInteger(percentile) ? percentile.toFixed(0) : percentile.toFixed(2)) : 0} %`,
                    subText: "of attendees",
                    vertical: true,
                },
                {
                    header: `Boosters${sep}Earned`,
                    type: "booster",
                    yellowBoosterCount: userScoreMetrics.booster75 ? userScoreMetrics.booster75 : "0",
                    redBoosterCount: userScoreMetrics.booster100 ? userScoreMetrics.booster100 : "0",
                    wide: true,
                },
                {
                    header: `Class${sep}Rank`,
                    dataText: `${userScoreMetrics.rank}`,
                    subText: `/${userScoreMetrics.totalCount}`,
                },
            ],
            meta: meta,
            containerStyle: { marginVertical: 10 }
        }
    }

    private getBarGraphWidget(userContext: UserContext, energyMap: { [time: string]: number }) {
        return {
            widgetType: "PULSE_BAR_GRAPH_WIDGET",
            hasDividerBelow: false,
            graph_data: {
                type: "normal",
                yUnit: "Energy",
                title: "Minute by Minute Performance Graph",
                colorStops: AppUtil.isInternationalTLApp(userContext) ? TRAINER_LED_BAR_STOP_COLORS : INDIA_BAR_STOP_COLORS,
                data: _.keys(energyMap).map(time => ({
                    x: integer(time),
                    y: energyMap[time]
                }))
                    .slice(0, 60)
            }
        }
    }

    private getBlurMetricsWidget() {
        return {
            widgetType: "IMAGE_BANNER_WIDGET",
            bannerHeight: 485,
            topMargin: true,
            imageUri:
                CdnUtil.getCdnUrl("curefit-content/image/livefit/report_energy_disabled_blur.png"),
            lowResImageUri:
                CdnUtil.getCdnUrl("curefit-content/image/livefit/report_energy_disabled_blur.png"),
            imageDescription:
                "Enable Energy Meter in your next session to view a detailed workout report.",
            hasDividerBelow: true,
        }
    }

    private getRandomProWidgetString(): string {
        return proTips[(new Date().getDate() % proTips.length)]
    }

    private isCityEnergyyWidgetSupported(playbackMillis: number, cityId: string) {
        return (["Gurgaon", "Bangalore", "Mumbai", "Hyderabad"].includes(cityId) && playbackMillis >= 600000)
    }

    private getCityEnergyWidget(userContext: UserContext, score: number = 0, city: string) {
        return {
            widgetType: "SUBSCRIPTION_LIVE_COUNT_WIDGET",
            title: [{
                text: `You've contributed ${score} points to ${city} in ${LiveUtil.getLiveBranding(userContext)} Fitness League!`, style: {
                    color: "#474747",
                    fontSize: 12,
                    fontFamily: "BrandonText-Bold",
                    fontWeight: "bold",
                }
            }],
            subTitle: [{
                text: `Know more`, style: {
                    color: "#ff3278",
                    fontSize: 14,
                    fontFamily: "BrandonText-Bold",
                }
            }],
            imageUrl: CdnUtil.getCdnUrl("curefit-content/image/userReport/report_widget.png"),
            action: {
                actionType: "NAVIGATION",
                url: "curefit://listpage?pageId=fpl"
            }
        }
    }

    private async getMySquadCardWidget(session: any, userContext: UserContext, userScoreMetrics: UserScoreMetricsResponse, contentType: SessionContentType, isDIY?: boolean): Promise<any> {
        const allUsersPromise = DigitalReportViewBuilder.getAttendingUsersData(userContext.userProfile.userId, session.productId || (<any>session)._id, this.diyFulfilmentService, this.userCache, contentType)
        const userDataPromise = this.userCache.getUser(userContext.userProfile.userId)

        const [allUsers, userData] = await Promise.all([allUsersPromise, userDataPromise])

        if (!allUsers || allUsers.length < 1) return undefined
        // adding the user himself.

        allUsers.push({
            userId: userScoreMetrics.userId,
            rank: userScoreMetrics.rank,
            score: userScoreMetrics.score,
            profilePictureUrl: userData.profilePictureUrl,
            displayName: "You",
        })

        allUsers.sort(this.sortAscendingOnRank)
        const currentUserIndex = allUsers.findIndex(user => user.userId === userScoreMetrics.userId)

        let featuredUsers: any[] = []
        const remainingUsers: any[] = []
        const MAX_UPFRONT_USERS = 4

        if (allUsers.length < MAX_UPFRONT_USERS) {
            featuredUsers.push(...allUsers)
        } else {
            if (currentUserIndex === allUsers.length - 1) {
                // if User is the last on leaderboard, just feature immediate left person.
                featuredUsers.push(...allUsers.splice(currentUserIndex - 1, 2))
            } else if (currentUserIndex === 0) {
                // if User is first on leaderboard, just feature immediate right person.
                featuredUsers.push(...allUsers.splice(0, 2))
            } else {
                featuredUsers.push(...allUsers.splice(currentUserIndex - 1, 3))
            }
            remainingUsers.push(...allUsers)
        }

        if (isDIY) {
            featuredUsers = this.convertRanksToRelative(featuredUsers)
        }

        const footer = {
            title: "VIEW ALL",
            action: {
                actionType: "NAVIGATION",
                url: "curefit://squadleaderboard"
            }
        }

        const shareConfig = {
            text: "SHARE",
            action: {
                actionType: "SHARE_SCREENSHOT",
                meta: {
                    ...this.shareMessage
                }
            }
        }

        return {
            widgetType: "SQUAD_CARD_WIDGET",
            widgetTitle: "My Squad",
            hasDividerBelow: false,
            data: {
                currentUserId: userContext.userProfile.userId,
                sections: [{
                    id: "Friends",
                    name: `My Friends (${featuredUsers.length + remainingUsers.length - 1})`
                }],
                featuredUsers: featuredUsers,
                remainingUsers: remainingUsers,
                listHeaderLeft: "RANK",
                listHeaderRight: "SCORE",
                footer: remainingUsers.length > MAX_UPFRONT_USERS ? footer : undefined,
                maxExcerptCount: Math.min(MAX_UPFRONT_USERS, remainingUsers.length),
                shareConfig: shareConfig,
            }
        }
    }

    private sortAscendingOnRank(a: UserScoreMetricsResponse, b: UserScoreMetricsResponse) {
        // if (a?.rank < b?.rank) return -1
        // if (a?.rank > b?.rank) return 1
        if (a.rank && !b.rank) return -1
        if (!a.rank && b.rank) return 1
        if (a.rank < b.rank) return -1
        if (a.rank > b.rank) return 1
        return 0
    }

    private convertRanksToRelative(users: any[]) {
        return users.map((val, index) => {
            return {
                ...val,
                rank: index + 1
            }
        })
    }

    static shouldOmitReportMetric(session: DigitalCatalogueEntryV1 | DIYFitnessProductExtended, metricType: MetricType) {
        if (!session) return true
        const isInteractiveSession =  ("preferredStreamType" in session && LiveUtil.isInteractiveSession(session.preferredStreamType))
        const isYoga = session.format?.includes("YOGA")
        switch (metricType) {
            case "CALORIES": return isInteractiveSession
            case "ENERGY":
            case "RANK": return isInteractiveSession || isYoga
            default: return false
        }
    }

}

export default DigitalReportViewBuilder
