import { injectable } from "inversify"
import { CFLiveProduct } from "@curefit/diy-common"
import * as _ from "lodash"
import { LiveMembershipPacksPage } from "./ILivePackPage"
import { TextWidget, StyledTouchableTextWidget, Action } from "@curefit/apps-common"
import { ProductListWidget } from "../common/views/WidgetView"
import { SessionInfo, UserContext } from "@curefit/userinfo-common"
import { LivePackUtil } from "../util/LivePackUtil"
import { Membership } from "@curefit/membership-commons"
import AppUtil from "../util/AppUtil"
import { LivePricesResponse, OfferV2 } from "@curefit/offer-common"
import { CULT_LIVE_TITLE, CUREFIT_LIVE_TITLE_INTL } from "../util/LiveUtil"

@injectable()
class LiveMembershipExpiredModalViewBuilder {

    LIVE_IOS_PACK_BANNER = "image/livefit/app/ios_info_banner.png"
    LIVE_IOS_PACK_BANNER_TRIAL_EXPIRED = "image/livefit/app/live_ios_pack_banner_new.png"

    public buildView(userContext: UserContext, cfLivePacks: CFLiveProduct[], membership: Membership, livePricesResponse: LivePricesResponse,
        offerIdOfferMap: { [offerId: string]: OfferV2 }, isMonetisationEnabled: boolean, showTrialBanner: boolean): LiveMembershipPacksPage {
        const { sessionInfo, userProfile } = userContext
        const liveMembershipPacksPage = new LiveMembershipPacksPage()

        const isIOS = sessionInfo.osName === "ios"
        const { livePackPickerWidget, selectedProductId, selectedPackAction } = LivePackUtil.getLivePackPickerWidget(userContext, cfLivePacks, sessionInfo.osName, livePricesResponse, offerIdOfferMap, isMonetisationEnabled, "live_pack_expiry_modal")

        livePackPickerWidget.rootStyle = { paddingBottom: 10, backgroundColor: "#ffffff" }
        livePackPickerWidget.cardStyle = { marginTop: -20 }
        liveMembershipPacksPage.selectedProductId = selectedProductId
        liveMembershipPacksPage.pageAction = selectedPackAction
        liveMembershipPacksPage.title = "UNLIMITED ACCESS TO"
        liveMembershipPacksPage.packs = cfLivePacks

        let title = ""

        if (AppUtil.isInternationalApp(userContext)) {
            title = "Become a member"
        } else {
            const { totalMembershipDays, isExpired } = LivePackUtil.getTotalMembershipDaysDetailsAndExpiryStatus(membership, userProfile.timezone)
            title = this.getTitleText(totalMembershipDays, isExpired)
        }

        liveMembershipPacksPage.widgets.push(this.getTitleWidget(title))

        liveMembershipPacksPage.widgets.push(this.getProductListWidget(userContext, "UNLIMITED ACCESS TO"))
        liveMembershipPacksPage.widgets.push(livePackPickerWidget)
        const containerStyle = {
            marginTop: -1,
            marginBottom: 0,
            paddingVertical: 15,
            paddingBottom: 30
        }
        /*if (isIOS) {
            const banner = !showTrialBanner ? undefined : this.LIVE_IOS_PACK_BANNER
            const bannerHeight = !showTrialBanner ? 55 : 130
            if (banner) {
                liveMembershipPacksPage.widgets.push(LivePackUtil.getSingleBannerCarouselWidget(banner, 334, bannerHeight))
            }
        }*/
        if (!isIOS || AppUtil.isIAPEnabled(userContext)) {
            liveMembershipPacksPage.widgets.push(LivePackUtil.getTermsNConditionsWidget(isIOS, containerStyle))
            liveMembershipPacksPage.widgets.push(LivePackUtil.getTnCWidget(userContext))
        }
        liveMembershipPacksPage.titleImage = AppUtil.isInternationalApp(userContext) ? CUREFIT_LIVE_TITLE_INTL : CULT_LIVE_TITLE

        return liveMembershipPacksPage
    }

    private getProductListWidget(userContext: UserContext, title: string): ProductListWidget {
        const iconStyle = {
            tintColor: "#ffffff",
            width: 18,
            height: 18
        }
        const textStyle = {
            fontSize: 14,
            color: "#ffffff",
            fontFamily: "BrandonText-Medium"
        }
        const style = {
            marginVertical: 9
        }
        const isInternationalApp = AppUtil.isInternationalApp(userContext)

        const hiwWidget: ProductListWidget = {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "CUSTOM_ROW",
            header: {
                title: title,
                color: "#ffffff",
                style: {
                    fontFamily: "BrandonText-Bold",
                    fontSize: 22,
                    width: "97%",
                    textAlign: "center",
                    marginTop: 20,
                }
            },
            styles: {
                marginBottom: 60
            },
            style: {
                paddingBottom: 50
            },
            hideSepratorLines: true,
            items: [
                {
                    "text": isInternationalApp ? "Wide variety of Certified Trainers" : "Fitness, Dance and Meditation Classes",
                    "icon": "CIRCULAR_TICK_WHITE",
                    textStyle,
                    iconStyle,
                    style,
                },
                {
                    "text": isInternationalApp ? "Live experience with fit meter and leaderboard" : "LIVE experience with Energy Meter and Leaderboard",
                    "icon": "CIRCULAR_TICK_WHITE",
                    textStyle,
                    iconStyle,
                    style,
                },
                {
                    "text": isInternationalApp ? "Fitness, Yoga and Meditation" : "Exclusive Masterclasses with Celebrities",
                    "icon": "CIRCULAR_TICK_WHITE",
                    textStyle,
                    iconStyle,
                    style,
                }
            ],
            backgroundColor: "#131417",

        }
        return hiwWidget
    }

    private getTitleWidget(title: string): StyledTouchableTextWidget {
        const textStyle = {
            fontFamily: "BrandonText-Bold",
            fontSize: 22,
            margin: 0,
            color: "#ffffff"
        }

        const tnc: StyledTouchableTextWidget = {
            widgetType: "STYLED_TOUCHABLE_TEXT_WIDGET",
            data: [
                {
                    title,
                    style: textStyle,
                }
            ],
            containerStyle: {
                paddingBottom: 0,
                backgroundColor: "#131417"
            },
        }
        return tnc
    }

    private getTitleText(totalMembershipDays: number, isExpired: boolean) {
        return !isExpired ? "Become a member" : (totalMembershipDays > 14 ? "Your membership has expired" : "Your free trial has expired")
    }

}

export default LiveMembershipExpiredModalViewBuilder