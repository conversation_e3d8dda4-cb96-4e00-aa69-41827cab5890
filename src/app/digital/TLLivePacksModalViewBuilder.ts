import { injectable } from "inversify"
import { CFLiveProduct } from "@curefit/diy-common"
import * as _ from "lodash"
import { LiveMembershipPacksPage } from "./ILivePackPage"
import { UserContext } from "@curefit/userinfo-common"
import { LivePackUtil } from "../util/LivePackUtil"
import { LivePricesResponse } from "@curefit/offer-common"
import { Action, TLFeaturesWidget, TLImageHeaderWidget, TLLivePacksWidget } from "@curefit/apps-common"

const INTERNATIONAL_TNC = "http://static.cure.fit/termsInternational.html"

@injectable()
class TLLivePacksModalViewBuilder {
    public buildView(userContext: UserContext, cfLivePacks: CFLiveProduct[], livePricesResponse: LivePricesResponse): LiveMembershipPacksPage {
        const { sessionInfo } = userContext
        const liveMembershipPacksPage = new LiveMembershipPacksPage()
        const livePacksWidget = this.getLivePacksWidget(userContext, cfLivePacks, sessionInfo.osName, livePricesResponse, userContext.sessionInfo.orderSource)
        const selectedPack = livePacksWidget.packs.find(pack => pack.isSelected) || livePacksWidget.packs[0]
        if (selectedPack) {
            liveMembershipPacksPage.selectedProductId = selectedPack.productId
            liveMembershipPacksPage.pageAction = selectedPack.packAction
        }
        liveMembershipPacksPage.title = "Curefit Membership"
        liveMembershipPacksPage.packs = cfLivePacks
        liveMembershipPacksPage.widgets.push(this.getHeader())
        liveMembershipPacksPage.widgets.push(this.getFeatures())
        liveMembershipPacksPage.widgets.push(livePacksWidget)
        return liveMembershipPacksPage
    }

    private getHeader(): TLImageHeaderWidget {
        return {
            widgetType: "TL_IMAGE_HEADER_WIDGET",
            title: "Cure.fit Membership",
            image: "image/livefit/app/live_pack_cover_image_intl_v1.jpg"
        }
    }

    private getFeatures(): TLFeaturesWidget {
        return {
            widgetType: "TL_FEATURES_WIDGET",
            features: [
                "Unlock full access to all content",
                "Live leaderboard with fit meter",
                "Cast using chromecast, Apple TV",
                "Compatible with Apple watch"
            ]
        }
    }

    public getLivePacksWidget(userContext: UserContext, cfLivePacks: CFLiveProduct[], osName: string, livePricesResponse: LivePricesResponse, source: string): TLLivePacksWidget {
        const restoreAction: Action = {
            actionType: "RESTORE_PURCHASE",
            title: "RESTORE PURCHASES",
            analyticsData: {
                eventKey: "applerestoreclicked",
                eventData: {}
            }
        }
        const tncAction: Action = {
            "url": `curefit://webview?uri=${decodeURIComponent(INTERNATIONAL_TNC)}`,
            actionType: "NAVIGATION"
        }

        const packs = LivePackUtil.filterPacks(userContext, cfLivePacks, false).map((pack) => {
            const { mrp, sellingPrice, discount } = livePricesResponse.priceMap[pack.productId]
            const price = {
                mrp: mrp,
                listingPrice: sellingPrice,
                currency: pack.price.currency,
                showPriceCut: pack.price.showPriceCut,
                discountText: "" + discount
            }
            return {
                productId: pack.productId,
                title: pack.title,
                duration: pack.subscriptionOptions.duration,
                price,
                perMonthPrice: "" + LivePackUtil.getPerMonthPrice(pack, price),
                packAction: LivePackUtil.getPackAction(userContext, pack, osName, price, source),
                offerText: "",
                isSelected: pack.ordering == 0,
                isPack: true,
                quantity: 1,
                tags: pack.tags
            }
        })

        return {
            widgetType: "TL_LIVE_PACKS_WIDGET",
            packs,
            showRestore: osName === "ios",
            restoreAction,
            tncAction
        }
    }
}

export default TLLivePacksModalViewBuilder