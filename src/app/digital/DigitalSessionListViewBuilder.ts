import { injectable, inject } from "inversify"
import { ProductDetailPage, Action, CultBuddiesJoiningListSmallView } from "../common/views/WidgetView"
import { PageWidget } from "../page/Page"
import {
    DIYUserFitnessPack,
    DIYProduct,
    DIYFitnessProductExtended,
    DIYFitnessPack,
    DIYPack,
    DIYPackFulfilment,
    Trainer, LiveClassFormat
} from "@curefit/diy-common"
import { UserContext, DaysRemainingWidget } from "@curefit/vm-models"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import * as _ from "lodash"
import { WidgetView, MultiSelectFilterWidget, SingleSelectFilterWidget, SwitchFilterWidget, SessionFilterOption } from "@curefit/apps-common"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { FoodProduct as Product } from "@curefit/eat-common"
import AppUtil from "../util/AppUtil"
import { LivePackUtil } from "../util/LivePackUtil"
import LiveUtil, { LIVE_SESSION_ACTION_SOURCE } from "../util/LiveUtil"
import { IdVsValueMap } from "./DigitalSocialLeagueTabPageViewBuilder"
import AtlasUtil from "../util/AtlasUtil"
import { DIYSessionWidget } from "./DIYSessionWidget"
import { ProductType, UrlPathBuilder } from "@curefit/product-common"
import { AllAction } from "../page/vm/widgets/card/CardListWidget"
import ActionUtil from "../util/ActionUtil"
import { BASE_TYPES, Logger } from "@curefit/base"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService } from "@curefit/diy-client"
import { IMaxmindService, MAXMIND_CLIENT_TYPES } from "@curefit/maxmind-client"

const clone = require("clone")

class FiltersPageView extends ProductDetailPage {

    clearAction: Action

  constructor(widgets: PageWidget[], actions: Action[], clearAction: Action) {
    super()
    this.widgets = widgets
    this.actions = actions
    this.clearAction = clearAction
  }
}

class DigitalSessionListView extends ProductDetailPage {

    filters: FiltersPageView
    subTitle: string
    description: string

  constructor(widgets: PageWidget[], actions: Action[], filters: FiltersPageView, title: string, subTitle: string, description: string) {
    super()
    this.widgets = widgets
    this.actions = actions
    this.filters = filters
    this.title = title
    this.subTitle = subTitle
    this.description = description
  }
}

@injectable()
class DigitalSessionListViewBuilder {

  constructor(
    @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
    @inject(BASE_TYPES.ILogger) private logger: Logger,
    @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) private diyFulfilmentService: IDIYFulfilmentService,
  ) {
  }

  // protected getSessionWidgetItemAction(session: DIYProduct, pack: DIYPack, packFulfilment: DIYPackFulfilment, isUserEligibleForTrial: boolean, isLocked: boolean, userContext: UserContext, isUserEligibleForMonetisaiton: boolean, blockInternationalUser: boolean) {
  //   if (!userContext.sessionInfo.isUserLoggedIn) {
  //       return {
  //           actionType: "SHOW_LOGIN_MODAL",
  //           url: "curefit://loginmodal",
  //       }
  //   }
  //   const action = {
  //       actionType: "PLAY_VIDEO",
  //       content: AtlasUtil.getContentDetailV2(session),
  //       meta: {
  //           content: AtlasUtil.getContentDetailV2(session),
  //           queryParams: AtlasUtil.getContentMetaV2(session, pack),
  //           title: session.title,
  //           packId: pack?.productId,
  //           productType: session.productType,
  //           checkDownloadStatus: true
  //       }
  //   }
  //   return LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisaiton, isLocked, action, isUserEligibleForTrial, session.productType, "diy_page_session_item", "", true)
  // }

  protected getShareAction(packId: string, productId: string, productType: ProductType, packName: string, productName: string): Action {
    return {
        icon: "SHARE",
        actionType: "REST_API",
        showLoadingIndicator: true,
        meta: {
            method: "post",
            url: `/pack/diy/inviteLink`,
            body: {
                productId,
                packId,
                productType,
                isSession: true
            },
        },
        analyticsData: {
            type: productType,
            eventType: "SHARE_EVENT",
            packName,
            productName
        }
    }
  }

    protected getDIYSessionShareAction(productId: string, productName: string): Action {
        return {
            icon: "SHARE",
            actionType: "REST_API",
            showLoadingIndicator: true,
            meta: {
                method: "get",
                url: `/v2/digital/diy/session/inviteLink`,
                body: {
                    productId,
                    isSession: true
                },
            },
            analyticsData: {
                type: "FITNESS",
                eventType: "SHARE_EVENT",
                productName: productName
            }
        }
    }

  private getAutoPlayUrl(diyProduct: DIYProduct, userContext: UserContext): string {
    let url
    if (diyProduct.autoPlayStreamData) {
      url = LiveUtil.getLiveVideoUrl(userContext.sessionInfo, diyProduct.autoPlayStreamData, false, false, false)
    }
    if (!_.isEmpty(url)) return url
    return AtlasUtil.getDIYAbsoluteUrl(diyProduct)
  }

  getSessionMovements(movements: string[]): any {
      const items = []
      for (let i = 0; i < movements.length; i++) {
        const movement = movements[i]
        items.push({
            subText: movement,
            subtextStyle: {
                color: "#000000",
                marginLeft: 5
            }
        })
      }
      return items
  }

  getSessionDetailAction(duration: string, session: DIYProduct, packId: string, source?: string): Action {
    const action: AllAction = {
      "actionType": "NAVIGATION",
      "url": `curefit://liveclassdetail?liveClassId=${encodeURIComponent(session.productId)}&bookingNumber=${encodeURIComponent(session.productId)}&isDIY=true&productType=LIVE_FITNESS`
    }
    if (!_.isNil(packId)) {
        action.url = `${action.url}&packId=${packId}`
    }
    if (!_.isEmpty(source)) {
      action.url = `${action.url}&pageFrom=${source}`
    }
    return action
  }

  async buildView(appVersion: number, userPackInfo: DIYUserFitnessPack, sessionMap: { [productId: string]: Product }, packRecommendedByBuddies: CultBuddiesJoiningListSmallView,
    sessionRecommendedByBuddiesMap: { [sessionId: string]: CultBuddiesJoiningListSmallView }, blockInternationalUser: boolean, daysRemainingWidget?: DaysRemainingWidget, userContext?: UserContext, isInternalUser?: Boolean, isUserEligibleForTrial?: boolean,
    isUserEligibleForMonetisaiton?: boolean, userHasActiveMembership?: { start: number, end: number } | undefined, diyEnergyMeterSupportedMap?: IdVsValueMap<boolean>, sessionFilters?: any, reqBody?: any): Promise<DigitalSessionListView> {

    let widgets: (WidgetView | PageWidget)[] = []
    const actions: Action[] = []
    let filters: FiltersPageView

    const allSessionsHaveEnergyMeterSupported = LivePackUtil.canShowPackLevelEnergyIcon(diyEnergyMeterSupportedMap)
    const pack = userPackInfo?.pack
    const packFulfilment = userPackInfo?.fulfilment
    const productIds = Object.keys(sessionMap)
    let completedSessionIds: string[] = []
    if (packFulfilment !== undefined && packFulfilment.completedProductIds !== undefined) {
        completedSessionIds = packFulfilment.completedProductIds
    }

    for (let i = 0; i < productIds.length; i++) {
        try {
        const productId = productIds[i]
        const session = <DIYProduct>sessionMap[productId]
        if (_.isEmpty(session)) {
            continue
        }
        const formatText = session.format ? LiveUtil.getFormatText(session.format) : undefined
        const intensity = session.intensityLevel ? LiveUtil.getCapitalize(session.intensityLevel.toString()) : undefined
        const widgetActions: Action[] = []
        const isSugarFitOrUltraFitApp = AppUtil.isSugarFitOrUltraFitApp(userContext)
        const diyEnergyIconSupported = diyEnergyMeterSupportedMap?.[productId] && !allSessionsHaveEnergyMeterSupported && session.productType === "DIY_FITNESS"
        const isSessionCompleted = completedSessionIds.includes(productId)
        const duration = LiveUtil.getFormattedTimeString(typeof session.duration === "string" ? parseInt(session.duration) : session.duration)
        const isLocked = session.locked === undefined ? isUserEligibleForMonetisaiton : (session.locked && isUserEligibleForMonetisaiton)
        const typeCastedSession = <DIYFitnessProductExtended>session
        const shareAction = !isSugarFitOrUltraFitApp ? pack ? this.getShareAction(pack.productId, productId, session.productType, pack.title, session.title) : this.getDIYSessionShareAction(session.productId, session.title) : undefined
        const downloadAction = (!isSugarFitOrUltraFitApp && pack) ? LiveUtil.getDIYDownloadAction(userContext, session.format, blockInternationalUser) : undefined
        shareAction && widgetActions.push(shareAction)
        downloadAction && widgetActions.push(downloadAction)
        const sessionDetailAction = this.getSessionDetailAction(duration, session, pack?.productId, "diysessionlistingpage")
        const caption = duration + (intensity ? " \u2022 " + intensity : "") + (formatText ? " \u2022 " + formatText : "")
        widgets.push({
            widgetType: "DIY_SESSION_WIDGET",
            title: session.title,
            action: sessionDetailAction,
            image: UrlPathBuilder.prefixSlash(session.imageDetails.heroImage),
            contentId: session.productId,
            contentType: "DIY_FITNESS",
            showDivider: false,
            hasDividerBelow: false,
            showWidgetDivider: false,
            buddies: [],
            cardAction: sessionDetailAction,
            intensity: session.intensityLevel,
            duration: duration,
            durationString: duration,
            durationMs: typeof session.duration === "string" ? parseInt(session.duration, 10) : session.duration,
            isLocked: AppUtil.isLiveContentLocked(userContext, session.locked, isUserEligibleForMonetisaiton, isUserEligibleForTrial),
            shouldRepeat: true, // based on videoDisabled
            url: this.getAutoPlayUrl(session, userContext),
            videoDisabled: false,
            trainers: session.trainerName,
            format: session.format,
            renderPlay: true,
            isCompleted: isSessionCompleted,
            content: AtlasUtil.getContentDetailV2(session),
            actions: widgetActions,
            shareAction: shareAction && shareAction,
            subTitle: caption,
        })
        } catch (e) {
            this.logger.error(`Error pushing digital session widget to list ${e.message}`)
        }

    }

    if (!_.isEmpty(sessionFilters) && sessionFilters.hasOwnProperty("duration") && !_.isEmpty(sessionFilters["duration"])) {
        const filteredWidgets = widgets.filter(widget => {
          if (widget.widgetType !== "DIY_SESSION_WIDGET") return false
          const durationMs: number = (<DIYSessionWidget>widget).durationMs
          const durationText = this.getDurationFilterText(durationMs)
            return sessionFilters["duration"].includes(durationText)
        })
        widgets = clone(filteredWidgets)
    }

    if (!_.isEmpty(sessionFilters) && sessionFilters.hasOwnProperty("format") && !_.isEmpty(sessionFilters["format"])) {
        const filteredWidgets = widgets.filter(widget => {
            return widget.widgetType !== "DIY_SESSION_WIDGET" || sessionFilters["format"].includes((<DIYSessionWidget>widget).format)
        })
        widgets = clone(filteredWidgets)
    }

    if (!_.isEmpty(sessionFilters) && sessionFilters.hasOwnProperty("intensity") && !_.isEmpty(sessionFilters["intensity"])) {
        const filteredWidgets = widgets.filter(widget => {
            return widget.widgetType !== "DIY_SESSION_WIDGET" || sessionFilters["intensity"].includes((<DIYSessionWidget>widget).intensity)
        })
        widgets = clone(filteredWidgets)
    }

    if (!_.isEmpty(sessionFilters) && sessionFilters.hasOwnProperty("trainer") && !_.isEmpty(sessionFilters["trainer"])) {
        const filteredWidgets = widgets.filter(widget => {
            return widget.widgetType !== "DIY_SESSION_WIDGET" || sessionFilters["trainer"].includes((<DIYSessionWidget>widget).trainers)
        })
        widgets = clone(filteredWidgets)
    }

    if (!_.isEmpty(sessionFilters) && sessionFilters.hasOwnProperty("incomplete") && !_.isEmpty(sessionFilters["incomplete"])) {
      const filteredWidgets = widgets.filter(widget => {
        if (widget.widgetType !== "DIY_SESSION_WIDGET" || sessionFilters["incomplete"].includes("all")) return false
        return !(<DIYSessionWidget>widget).isCompleted
      })
      widgets = clone(filteredWidgets)
    }

    filters = this.getFilters(pack, sessionMap, completedSessionIds, reqBody)

    let title: string = pack?.title
    const subTitle: string = `${widgets.length} Sessions`
    let description: string = pack?.description

      if (!_.isNil(reqBody)) {

          if (!_.isNil(reqBody.trainerId)) {
              const trainer: Trainer = await this.diyFulfilmentService.getTrainerById(reqBody.trainerId)
              title = `More sessions by ${trainer.name}`
          }
          if (!_.isNil(reqBody.formatId)) {
              const format: LiveClassFormat = await this.diyFulfilmentService.getFormatById(reqBody.formatId)
              title = `More ${format.formatName} sessions`
          }
          if (_.isNil(reqBody.formatId) || _.isNil(reqBody.trainerId)) {
              description = "Switch it up with new home workout every day"
          }

      }


    return new DigitalSessionListView(widgets, actions, filters, title, subTitle, description)
  }

  getDurationFilterText(duration: number): string {
    if (duration <= 1000 * 60 * 15) {
      return "15 min"
    } else if (duration <= 1000 * 60 * 30) {
      return "30 min"
    } else if (duration <= 1000 * 60 * 45) {
      return "45 min"
    } else if (duration <= 1000 * 60 * 60) {
      return "60 min"
    } else {
      return "90+ min"
    }
  }

  getDurationFilterWidget(sessionMap: { [productId: string]: Product }): MultiSelectFilterWidget {
    const productIds = Object.keys(sessionMap)
    const durations: string[] = []
    for (let i = 0; i < productIds.length; i++) {
        const productId = productIds[i]
        const session = <DIYProduct>sessionMap[productId]
        if (_.isEmpty(session)) {
            continue
        }
        const durationMs = typeof session.duration === "string" ? parseInt(session.duration) : session.duration
        const duration = this.getDurationFilterText(durationMs) // LiveUtil.getFormattedTimeString(durationMs)
        if (!durations.includes(duration)) {
            durations.push(duration)
        }
    }
    durations.sort((a, b) => {
      if (a.startsWith("<")) {
          return -1
      } else if (b.startsWith("<")) {
        return 1
      } else {
          return a > b ? 1 : -1
      }
    })
    const options: SessionFilterOption[] = []
    for (const duration of durations) {
        options.push({
            key: duration,
            value: duration
        })
    }

    if (options.length <= 1)  return undefined

    return {
        widgetType: "MULTI_SELECT_FILTER_WIDGET",
        hasDividerBelow: false,
        title: "Duration",
        category: "duration",
        action: {
          actionType: "UPDATE_SESSION_FILTERS",
        },
        options
    }
  }

  getFormatFilterWidget(sessionMap: { [productId: string]: Product }): MultiSelectFilterWidget {
    const productIds = Object.keys(sessionMap)
    const formats: string[] = []
    for (let i = 0; i < productIds.length; i++) {
        const productId = productIds[i]
        const session = <DIYProduct>sessionMap[productId]
        if (_.isEmpty(session)) {
            continue
        }
        if (!_.isEmpty(session.format) && !formats.includes(session.format)) {
            formats.push(session.format)
        }
    }
    formats.sort()
    const options: SessionFilterOption[] = []
    for (const format of formats) {
        options.push({
            key: format,
            value: format
        })
    }
    if (options.length <= 1)  return undefined

    return {
        widgetType: "MULTI_SELECT_FILTER_WIDGET",
        hasDividerBelow: false,
        title: "Formats",
        category: "format",
        action: {
          actionType: "UPDATE_SESSION_FILTERS",
        },
        options
    }
  }

  getIntensityFilterWidget(sessionMap: { [productId: string]: Product }): MultiSelectFilterWidget {
    const productIds = Object.keys(sessionMap)
    const intensityArray: string[] = []
    for (let i = 0; i < productIds.length; i++) {
        const productId = productIds[i]
        const session = <DIYProduct>sessionMap[productId]
        if (_.isEmpty(session)) {
            continue
        }
        if (!_.isEmpty(session.intensityLevel) && !intensityArray.includes(session.intensityLevel)) {
            intensityArray.push(session.intensityLevel)
        }
    }
    intensityArray.sort()
    const options: SessionFilterOption[] = []
    for (const intensity of intensityArray) {
        options.push({
            key: intensity,
            value: intensity
        })
    }
    if (options.length <= 1)  return undefined

    return {
        widgetType: "MULTI_SELECT_FILTER_WIDGET",
        hasDividerBelow: false,
        title: "Intensity",
        category: "intensity",
        action: {
          actionType: "UPDATE_SESSION_FILTERS",
        },
        options
    }
  }

  getTrainerFilterWidget(sessionMap: { [productId: string]: Product }): SingleSelectFilterWidget {
    const productIds = Object.keys(sessionMap)
    const trainers: string[] = []
    for (let i = 0; i < productIds.length; i++) {
        const productId = productIds[i]
        const session = <DIYProduct>sessionMap[productId]
        if (_.isEmpty(session)) {
            continue
        }
        if (!_.isEmpty(session.trainerName) && !trainers.includes(session.trainerName)) {
            trainers.push(session.trainerName)
        }
    }
    trainers.sort()
    const options: SessionFilterOption[] = []
    for (const trainer of trainers) {
        options.push({
            key: trainer,
            value: trainer
        })
    }
    if (options.length <= 1)  return undefined

    return {
        widgetType: "SINGLE_SELECT_FILTER_WIDGET",
        hasDividerBelow: false,
        title: "Trainers",
        category: "trainer",
        action: {
          actionType: "UPDATE_SESSION_FILTERS",
        },
        options
    }
  }

  getCompletedSessionsFilterWidget(pack: DIYFitnessPack, sessionMap: { [productId: string]: Product }, completedSessionIds: string[]): SwitchFilterWidget {
    if (_.isEmpty(completedSessionIds)) {
      return undefined
    }
    return {
      widgetType: "SWITCH_FILTER_WIDGET",
      hasDividerBelow: false,
      title: "Incomplete sessions only",
      category: "incomplete",
      action: {
        actionType: "UPDATE_SESSION_FILTERS",
      },
      options: [
        {
          key: "all",
          value: "all"
        },
        {
          key: "incomplete",
          value: "incomplete"
        }
      ],
      default: {
        key: "all",
        value: "all"
      }
    }
  }

  getFilters(pack: DIYFitnessPack, sessionMap: { [productId: string]: Product }, completedSessionIds: string[], reqBody: any): FiltersPageView {
    const clearAction: Action = {
        actionType: "UPDATE_SESSION_FILTERS",
        data: {
          filterType: "clear"
        },
        completionActions: [
          {
              actionType: "FETCH_SESSION_LISTING_DATA",
              params: {
                packId: pack?.productId,
                formatId: reqBody?.formatId,
                trainerId: reqBody?.trainerId
              }
          }
        ]
    }

    const actions: Action[] = []
    actions.push({
        actionType: "UPDATE_SESSION_FILTERS",
        data: {
            filterType: "apply"
        },
        title: "SHOW ALL",
        completionActions: [
            {
                actionType: "FETCH_SESSION_LISTING_DATA",
                params: {
                    packId: pack?.productId,
                    formatId: reqBody?.formatId,
                    trainerId: reqBody?.trainerId
                }
            },
            {
              actionType: "HIDE_SESSION_FILTERS_MODAL"
            }
        ]
    })

    const widgets: (WidgetView | PageWidget)[] = []

    const completedSessionsFilterWidget = this.getCompletedSessionsFilterWidget(pack, sessionMap, completedSessionIds)
    if (completedSessionsFilterWidget !== undefined) {
      widgets.push(completedSessionsFilterWidget)
    }

    // const energyMeterFilterWidget = this.getEnergyMeterFilterWidget(pack, sessionMap)
    // if (energyMeterFilterWidget !== undefined) {
    //   widgets.push(energyMeterFilterWidget)
    // }

    const durationFilterWidget = this.getDurationFilterWidget(sessionMap)
    if (durationFilterWidget !== undefined) {
      widgets.push(durationFilterWidget)
    }

    const formatFilterWidget = this.getFormatFilterWidget(sessionMap)
    if (formatFilterWidget !== undefined) {
      widgets.push(formatFilterWidget)
    }

    const intensityFilterWidget = this.getIntensityFilterWidget(sessionMap)
    if (intensityFilterWidget !== undefined) {
      widgets.push(intensityFilterWidget)
    }

    const trainerFilterWidget = this.getTrainerFilterWidget(sessionMap)
    if (trainerFilterWidget !== undefined) {
      widgets.push(trainerFilterWidget)
    }

    return new FiltersPageView(widgets, actions, clearAction)
  }
}

export default DigitalSessionListViewBuilder
