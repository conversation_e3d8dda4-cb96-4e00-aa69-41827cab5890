import { <PERSON>, Buddy } from "@curefit/apps-common"

export interface DIYSessionWidget {
    title: string
    widgetType: string
    action: Action
    image: string
    contentId: string
    buddies: <PERSON>[]
    videoStatus: string
    cardAction: Action
    intensity: string
    duration: string
    durationMs: number
    isLocked: boolean
    shouldRepeat: boolean
    url: string
    description: string
    videoDisabled: boolean
    trainers: string
    format: string
    renderPlay: boolean
    isCompleted: boolean
    shareAction: Action
    subTitle: string
}
