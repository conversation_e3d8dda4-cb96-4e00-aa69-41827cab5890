import { Action, Buddy } from "@curefit/apps-common"
import { PreferredStreamType } from "@curefit/diy-common"
import LiveUtil from "../util/LiveUtil"

export interface NowLiveSessionWidget {
    title: string
    widgetType: string
    action: Action
    image: string
    contentId: string
    liveUserCount: string
    buddies: Buddy[]
    scheduleEpochTime: number
    videoStatus: string
    cardAction: Action
    intensity: string
    duration: string
    isLocked: boolean
    renderLiveIcon: boolean
    shouldRepeat: boolean,
    videoDisabled?: boolean,
    url?: string
}