import { eternalPromise, TimeUtil, Timezone } from "@curefit/util-common"
import { HeaderWidget } from "../page/PageWidgets"
import { inject, injectable } from "inversify"
import { UserContext } from "@curefit/vm-models"
import { DigitalCatalogueEntryV1 } from "@curefit/diy-common"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import DigitalReportViewBuilder from "./DigitalReportViewBuilder"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService } from "@curefit/diy-client"
import { PostsResponse, Tenant, UserProfileEntry, UserProfileEntryPaged } from "@curefit/social-common"
import {
    Action,
    CultSocialProfileListPageResponse,
    LiveMemoriesListWidget,
    WorkoutMember,
    WorkoutMemberHistoryListWidget
} from "@curefit/apps-common"
import CultUtil from "../util/CultUtil"
import { CacheHelper } from "../util/CacheHelper"
import { SOCIAL_CLIENT_TYPES, SocialService } from "@curefit/social-client"
import * as _ from "lodash"
import AppUtil from "../util/AppUtil"
import DigitalWorkoutMemberHistoryBuilder, {
    ITEMS_PER_ROW,
    NUM_ROWS_RANKS,
    PAYLOAD_SIZE_FOR_PROFILES_PAGE
} from "./DigitalWorkoutMemberHistoryBuilder"
import { User, Tenant as AppTenant } from "@curefit/user-common"
import LiveUtil, { LIVE_MOMENT_ASPECT_RATIO_1, LIVE_REPORT_V2_ASPECT_RATIO } from "../util/LiveUtil"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { BASE_TYPES, Logger } from "@curefit/base"
import YogaTimeStampedProgressPills from "./YogaTimeStampedProgressPills"


@injectable()
class DigitalMomentsViewBuilder {
    constructor(
        @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) private diyFulfilmentService: IDIYFulfilmentService,
        @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
        @inject(SOCIAL_CLIENT_TYPES.SocialService) private socialService: SocialService,
        @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(CUREFIT_API_TYPES.YogaTimeStampedProgressPills) private yogaTimeStampedProgressPills: YogaTimeStampedProgressPills,
    ) {}

    async buildView(contentId: string, userContext: UserContext): Promise<any> {
        // currently this page is not opened for DIY sessions, use contentType in Tenant if needed
        const {socialService, diyFulfilmentService, userCache, logger} = this
        const userId = userContext.userProfile.userId
        const liveVideoResponse: DigitalCatalogueEntryV1 = await this.diyFulfilmentService.getDigitalCatalogueEntry(contentId)
        const userProfile = await this.socialService.getUserProfileByUserId(userId)
        const numMembersToFetch = 10
        const widgets = []
        const isLiveMomentOnReportSupported = AppUtil.isLiveMomentOnReportSupported(userContext)
        const isLiveMomentOfDaySupported = AppUtil.isLiveMomentOfDaySupported(userContext)
        const tenant = AppUtil.getTenantFromUserContext(userContext)

        widgets.push(this.getHeaderWidget(userContext, liveVideoResponse, userContext.userProfile.timezone, tenant))

        let postsPromise
        if (isLiveMomentOfDaySupported) {
            postsPromise = eternalPromise(this.socialService.getUserPostsForCommunity(Number(userId), contentId, Tenant.LIVE, 10, 0))
        }

        const userScoreMetricsEternalPromise = eternalPromise(this.diyFulfilmentService.getUserScoreMetrics(contentId, userContext.userProfile.userId))
        const userProfileEntryPromise = eternalPromise(this.socialService.getCommunityUsers(userId, undefined, contentId, Tenant.LIVE, undefined, 0, numMembersToFetch, false))

        const [userProfileEntryPagedObj, postResponseObj, userScoreMetricsObj, isYogaReportSupported] = await Promise.all([userProfileEntryPromise, postsPromise, userScoreMetricsEternalPromise, AppUtil.isYogaReportSupported(userContext, this.hamletBusiness)])
        const postResponse = postResponseObj?.obj
        const userScoreMetrics = userScoreMetricsObj?.obj
        const userProfileEntryPaged = userProfileEntryPagedObj?.obj
        let momentsMedia
        if (postResponse && isLiveMomentOnReportSupported) {
          const media = postResponse?.contentList.elements?.[0]?.medias?.[0]
          if (media && media.fileCDNPrefix && media.fileName && media.fileExtension) {
            momentsMedia = media.fileCDNPrefix + media.fileName + "." + media.fileExtension
          }
        }
        let reportShareCardWidgetPromise
        if (userScoreMetrics) {
            reportShareCardWidgetPromise = eternalPromise(DigitalReportViewBuilder.getReportSelfieShareCardWidget({session: liveVideoResponse, userContext, userScoreMetrics, source: "class_moments_page", momentsImage: momentsMedia, diyFulfilmentService, logger, socialService, userCache, showingNewYogaReport: LiveUtil.canShowNewYogaReport(isYogaReportSupported, userScoreMetrics), yogaTimeStampedProgressPillsBuilder: this.yogaTimeStampedProgressPills}))
        }

        let memberListWidgetPromise
        if (userProfileEntryPaged && !_.isEmpty(userProfileEntryPaged.elements)) {
            memberListWidgetPromise = eternalPromise(this.getMemberListWidget(userProfileEntryPaged.elements, contentId, userProfile, "NONE", userProfileEntryPaged.limit))
        }

        const [memberListWidgetObj, reportShareCardWidgetObj] = await Promise.all([memberListWidgetPromise, reportShareCardWidgetPromise])
        const reportShareCardWidget = reportShareCardWidgetObj?.obj
        const memberListWidget = memberListWidgetObj?.obj

        if (memberListWidget) {
            widgets.push(memberListWidget)
        }

        if (reportShareCardWidget) {
            const overlayAction = {
                actionType: "NAVIGATION",
                url: `curefit://livesessionreport?contentId=${contentId}`
            }
            widgets.push({...reportShareCardWidget, overlayAction})
        }

        if (isLiveMomentOfDaySupported) {
            const postsResponse = (await postsPromise).obj
            const user = await this.userCache.getUser(userId)
            const memoryListWidget = DigitalMomentsViewBuilder.getLiveMemoriesListWidget(postsResponse, user, userContext)
            if (memoryListWidget) {
                widgets.push(memoryListWidget)
            }
        }
        return {widgets}
    }

    private getHeaderWidget(userContext: UserContext, videoResponse: DigitalCatalogueEntryV1, tz: Timezone, tenant: AppTenant): HeaderWidget {
        const { title } = videoResponse
        const formattedTime = TimeUtil.formatEpochInTimeZone(tz , videoResponse.scheduledTimeEpoch, "ddd MMM D, h:mm A")
        const branding = LiveUtil.getCultLiveBranding(userContext)
        const subTitle = tenant === AppTenant.CUREFIT_APP ? `${formattedTime}, ${branding}` : formattedTime
        return {
            hasTopPadding: false,
            hasBottomPadding: true,
            titleStyle: {
                fontFamily: "BrandonText-Bold",
                fontSize: 22,
                color: "#000000",
            },
            subTitleStyle: {
                fontFamily: "BrandonText-Regular",
                fontSize: 12,
                color: "#666666",
            },
            widgetTitle: {
                title,
                subTitle
            },
            widgetType: "HEADER_WIDGET",
        }
    }

    async getMemberListWidget(attendedUsers: UserProfileEntry[], classId: string, userSocialProfile: UserProfileEntry, userFilter = "NONE", limit: number): Promise<WorkoutMemberHistoryListWidget> {
        const isCurrentUserProfilePublic = userSocialProfile.visibility === "PUBLIC"
        const memberList: WorkoutMember[] = await Promise.all(attendedUsers.map(async (user, index) => {
            const isOtherUserProfilePublic = user.visibility === "PUBLIC"
            const userDetails = await this.userCache.getUser(user.userId)
            const profileAction: Action = CultUtil.getProfileClickAction(isCurrentUserProfilePublic, isOtherUserProfilePublic, index, classId, {
                productType: "LIVE_FITNESS",
                paginationEnabled: true,
                offset: DigitalWorkoutMemberHistoryBuilder.getOffsetForUserProfile(index),
                userFilter, payloadSize: PAYLOAD_SIZE_FOR_PROFILES_PAGE,
                limit
            })
            return CultUtil.createWorkoutMember(isCurrentUserProfilePublic, isOtherUserProfilePublic, userDetails, profileAction)
        }))

        const workoutMemberWidget: WorkoutMemberHistoryListWidget = {
            widgetType: "WORKOUT_MEMBER_HISTORY_LIST_WIDGET",
            header: {
                title: "You worked out with",
                seemore: {
                    title: "VIEW ALL",
                    actionType: "NAVIGATION",
                    url: `curefit://workoutmemberviewallpage?classId=${classId}`
                }
            },
            isCurrentUserProfilePublic: isCurrentUserProfilePublic,
            memberList
        }
        return workoutMemberWidget
    }

    async getUsersProfileByClass(usersProfileEntry: UserProfileEntryPaged, selectedProfileIndex: number, userContext: UserContext, offset: number, payloadSize: number, userFilter = "NONE"): Promise<CultSocialProfileListPageResponse> {
        const usersProfile = usersProfileEntry.elements
        const {data, snapToIndex} = await LiveUtil.getUserProfileItem(userContext, this.userCache, usersProfile, selectedProfileIndex, userFilter, offset)
        const newProfileLastIndex = offset + usersProfileEntry.elements.length - 1
        const hasReachedEndOfPublicProfiles = usersProfileEntry.elements.length < payloadSize
        const nextQuery = {
            leastProfileIndexAllowed: 0,
            maxProfileIndexAllowed: userFilter === "RANKS" ? ITEMS_PER_ROW * NUM_ROWS_RANKS - 1 : hasReachedEndOfPublicProfiles ? newProfileLastIndex : usersProfileEntry.totalElements - 1
        }
        return {
            header: {
                title: "You worked out with",
            },
            data,
            snapToIndex,
            nextQuery,
            payloadSize,
            preloadThreshold: 4
        }
    }

    async getUsersProfileByUserIds(userContext: UserContext, usersProfiles: UserProfileEntry[], title: string): Promise<CultSocialProfileListPageResponse> {
        const {data, snapToIndex} = await LiveUtil.getUserProfileItem(userContext, this.userCache, usersProfiles, 0)
        return {
            header: {
                title
            },
            data,
            snapToIndex,
        }
    }

    static getLiveMemoriesListWidget(postsResponse: PostsResponse, user: User, userContext: UserContext): LiveMemoriesListWidget | undefined {
      const memories: LiveMemoriesListWidget["memories"] = []
      const memoryHeader = {
        profileImage: user.profilePictureUrl,
        userName: `${user.firstName || ""} ${user.lastName || ""}`
      }
      _.forEach(postsResponse?.contentList?.elements, (element) => {
        const media = element && element?.medias && element.medias[0]
        let memoryUrl
        if (media && media.fileCDNPrefix && media.fileName && media.fileExtension) {
          memoryUrl = media.fileCDNPrefix + media.fileName + "." + media.fileExtension
        }
        if (memoryUrl) {
          const ctaAction: Action = {
            actionType: "SHARE_SCREENSHOT",
            title: "SHARE ON YOUR SOCIAL MEDIA",
            meta: {shareOptions: {
                shareTitle: "Share moment",
                shareMessage: "",
                url: memoryUrl
              },
              shareRemoteUrl: true,
            },
            analyticsData: {
              type: "cult.live memories"
            },
            colors: ["#ff3278", "#ff3278"],
            titleColor: "#ffffff",
          }
          memories.push({
            imageUrl: memoryUrl,
            ctaAction,
            memoryHeader,
            aspectRatio: LIVE_MOMENT_ASPECT_RATIO_1
          })
        }
      })

      if (!_.isEmpty(memories)) {
        return {
          widgetType: "LIVE_MEMORIES_LIST_WIDGET",
          header: userContext.sessionInfo.appVersion >= 8.25 ? undefined : {title: "Memories"},
          memories
        }
      }
      return undefined
    }
}

export default DigitalMomentsViewBuilder
