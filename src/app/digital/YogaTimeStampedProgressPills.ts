import { inject, injectable } from "inversify"
import { UserScoreMetricsResponse } from "@curefit/diy-common"
import * as _ from "lodash"
import { TimestampedProgressPills } from "@curefit/apps-common"
import { BASE_TYPES, Logger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

const PERFECT_REP_THRESHOLD = 0.80

const INTENSITY_MAP = [
  {val: PERFECT_REP_THRESHOLD, color: "#3888ff"},
  {val: 0.0, color: "rgb(213,227, 255)"}
    ]

const WORKOUT_NAME = "Surya Namaskar"

const NUM_TIMESTAMP_INTERVALS = 6

@injectable()
class YogaTimeStampedProgressPills {

    constructor(@inject(BASE_TYPES.ILogger) private logger: Logger, @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService) {
    }

    buildView(userScoreMetrics: UserScoreMetricsResponse): TimestampedProgressPills {
        this.logger.info(`YogaTimeStampedProgressPills_userScoreMetrics, ${JSON.stringify(userScoreMetrics)}`)
        try {
            const progressData = this.progressData(userScoreMetrics)
            if (!_.isEmpty(progressData)) {
                return {
                    progressData,
                    numPills: userScoreMetrics.totalRepetitions,
                    title: this.getTitle(userScoreMetrics),
                    timestamps: this.getTimeStamps(userScoreMetrics)
                }
            }
            this.logger.info("YogaTimeStampedProgressPills_emptyProgressData")
        }
        catch (e) {
            this.rollbarService.sendError(e)
        }
    }

    progressData(userScoreMetrics: UserScoreMetricsResponse) {
        return _.keys(userScoreMetrics.repHistory).sort().map(repIndex => {
            const repPerformance = userScoreMetrics.repHistory[repIndex]
            return {
                backgroundColor: INTENSITY_MAP.find(({val}) => {
                    return val <= repPerformance
                })?.color
            }
        })
    }

    getTimeStamps(userScoreMetrics: UserScoreMetricsResponse) {
        // can use a different NUM_TIMESTAMP_INTERVALS if need
      const diff = Math.ceil((userScoreMetrics.totalRepetitions - 1) / NUM_TIMESTAMP_INTERVALS)
      return [..._.range(1, userScoreMetrics.totalRepetitions, diff), userScoreMetrics.totalRepetitions].map(String)
    }

    getTitle(userScoreMetrics: UserScoreMetricsResponse) {
        let numPerfect = 0
        let numCompleted = 0
        _.forEach(userScoreMetrics.repHistory, (repPerformance) => {
            if (repPerformance > 0) {
                numCompleted += 1
            }
            if (repPerformance > PERFECT_REP_THRESHOLD) {
                numPerfect += 1
            }
        })
        const completionString = `${numCompleted} ${WORKOUT_NAME}${numCompleted > 1 ? "s" : ""} completed`
        const perfectlyDoneString = numPerfect > 0 ? `, ${numPerfect} done perfectly` : ""
        return numCompleted > 0 ? `${completionString}${perfectlyDoneString}!` : undefined
    }
}

export default YogaTimeStampedProgressPills
