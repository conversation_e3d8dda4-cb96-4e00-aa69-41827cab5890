import { UserContext } from "@curefit/userinfo-common"
import * as _ from "lodash"
import { TimeUtil } from "@curefit/util-common"
import { IBaseWidget, DigitalUserJourneyWidget } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import LiveUtil from "../util/LiveUtil"
import AppUtil from "../util/AppUtil"
import { DigitalCatalogueEntryV1, DIYPack, DIYProduct, LiveClassSlot, SessionContentType } from "@curefit/diy-common"
import AtlasUtil from "../util/AtlasUtil"
import DigitalReportViewBuilder from "./DigitalReportViewBuilder"
import { Action } from "@curefit/apps-common"

export class DigitalUserJourneyWidgetView extends DigitalUserJourneyWidget {

    title: string
    negativeCTA: Action
    positiveCTA: Action
    footerTitle: string
    futureSlots: {
        title: string,
        action: Action
    }[] = []
    workoutMetrics: { title: string, data: string, units: string, headerColor?: string, extraData?: any }[] = []

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams?: {
        [filterName: string]: string
      }): Promise<IBaseWidget> {

        const userId = userContext.userProfile.userId
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        const { classId, duration, contentType, userSessionId, liveClassId } = queryParams
        this.title = "LEAVING ?"

        let diyProduct
        if (contentType === "DIY_FITNESS") {
            const diyProductArray = await interfaces.diyService.getDIYFitnessProductsByProductIds(userId, [classId], tenant)
            diyProduct = diyProductArray[0]
            this.negativeCTA = {
                actionType: "HIDE_DIGITAL_JOURNEY_RETENTION_INTERSTITIAL",
                title: "NO",
                completionAction: DigitalUserJourneyWidgetView.getSessionPlayAction(diyProduct),
                analyticsData: {
                    eventKey: "button_click_event",
                    eventData: {
                        contentId: classId,
                        productType: "DIY_FITNESS",
                        actionType: "overlay_close",
                        source: "back_to_workout"
                    }
                }
            }
        } else {
            this.negativeCTA = {
                actionType: "HIDE_DIGITAL_JOURNEY_RETENTION_INTERSTITIAL",
                title: "NO",
                completionAction: {
                    actionType: "NAVIGATION",
                    title: "NO",
                    url: LiveUtil.videoUrl(classId)
                },
                analyticsData: {
                    eventKey: "button_click_event",
                    eventData: {
                        contentId: classId,
                        productType: "LIVE_FITNESS",
                        actionType: "overlay_close",
                        source: "back_to_workout"
                    }
                }
            }
            // this.negativeCTA = {
            //     actionType: "NAVIGATION",
            //     title: "NO",
            //     url: LiveUtil.videoUrl(classId)
            // }
        }

        this.positiveCTA = {
            actionType: "HIDE_DIGITAL_JOURNEY_RETENTION_INTERSTITIAL",
            title: "YES",
            analyticsData: {
                eventKey: "button_click_event",
                eventData: {
                    contentId: classId,
                    productType: contentType === "DIY_FITNESS" ? "DIY_FITNESS" : "LIVE_FITNESS",
                    actionType: "overlay_close",
                    source: "quit_workout"
                }
            }
        }

        if (Number(duration) < 1000 * 60 * 3) {
            this.footerTitle = "Want to workout later? Pick another slot"
            if (contentType == "DIY_FITNESS") {
                await this.getDIYFutureSlots(interfaces, userContext, diyProduct)
            } else {
                await this.getLiveFutureSlots(interfaces, classId, liveClassId, userContext)
            }
            // await this.getFutureSlots(interfaces, classId, userContext, contentType)
        } else {
            this.footerTitle = "Achievements so far"
            await this.getWorkoutMetrics(interfaces, classId, userContext, userSessionId, <SessionContentType>contentType)
        }

        return this
    }

    static getSessionPlayAction(session: DIYProduct): Action {
        return {
            actionType: "PLAY_VIDEO",
            meta: {
                content: AtlasUtil.getContentDetailV2(session),
                queryParams: AtlasUtil.getContentMetaV2(session),
                title: session.title,
                checkDownloadStatus: true
            }
        }
    }

    private async getLiveFutureSlots(interfaces: CFServiceInterfaces, classId: string, liveClassId: string, userContext: UserContext) {
        const userId = userContext.userProfile.userId
        // const currentLiveClass = await interfaces.diyService.getLiveClass(classId, userId, AppUtil.getCountryId(userContext))
        const liveClass = await interfaces.diyService.getLiveClass(liveClassId, userId, AppUtil.getTenantFromUserContext(userContext), AppUtil.getCountryId(userContext))
        // const videoResponse = await interfaces.diyService.getDigitalCatalogueEntry(classId)
        const slots: LiveClassSlot[] = liveClass.slots
        let futureSlotsInfo: LiveClassSlot[] = []
        slots.forEach((slot) => {
            if (slot.scheduledTimeEpoch > Date.now()) {
                futureSlotsInfo.push(slot)
            }
        })
        futureSlotsInfo.sort(function (slotA: LiveClassSlot, slotB: LiveClassSlot): number {
            return slotA.scheduledTimeEpoch < slotB.scheduledTimeEpoch ? -1 : 1
        })
        if (futureSlotsInfo.length > 3) {
            futureSlotsInfo = futureSlotsInfo.slice(0, 3)
        }
        futureSlotsInfo.forEach((slot: LiveClassSlot) => {
            const time = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, slot.scheduledTimeEpoch, "h:mm A")
            const completionActions = [{
                actionType: "BOOK_LIVE_CLASS",
                meta: {
                    classId: slot.classId,
                    productType: "LIVE_FITNESS"
                }
            }, {
                actionType: "HIDE_DIGITAL_JOURNEY_RETENTION_INTERSTITIAL",
                analyticsData: {
                    eventKey: "button_click_event",
                    eventData: {
                        contentId: classId,
                        productType: "LIVE_FITNESS",
                        actionType: "overlay_close",
                        source: "schedule_for_later"
                    }
                }
            }]
            const action: any = {
                title: time,
                actionType: "VIDEO_SUBSCRIBE",
                meta: {
                    subscriptionType: "VIDEO",
                    catalogueEntryId: slot.classId,
                    status: "SUBSCRIBED",
                    completionActions: completionActions,
                    noCalendarAddEvent: false
                },
                analyticsData: {
                    eventKey: "button_click_event",
                    eventData: {
                        source: "digital_user_journey_widget",
                        productType: "LIVE_FITNESS",
                        contentId: slot.classId,
                        title: liveClass.title,
                        actionType: "VIDEO_SUBSCRIBE",
                        status: "SUBSCRIBED",
                        format: liveClass.format
                    }
                },
                shouldRefreshPage: true
            }
            this.futureSlots.push({
                title: time,
                action
            })
        })
        const currentSlot = slots?.find(slot => slot.status === "LIVE")
        this.futureSlots.push({
            title: "MORE",
            action: {
                title: "MORE",
                actionType: "HIDE_DIGITAL_JOURNEY_RETENTION_INTERSTITIAL",
                completionAction: LiveUtil.getLiveSessionDetailAction(liveClass, "user_journey_modal", userContext, currentSlot?.classId),
                analyticsData: {
                    eventKey: "button_click_event",
                    eventData: {
                        contentId: classId,
                        productType: "LIVE_FITNESS",
                        actionType: "overlay_close",
                        source: "schedule_for_later"
                    }
                }
            }
        })
    }

    private async getDIYFutureSlots(interfaces: CFServiceInterfaces, userContext: UserContext, diyProduct: DIYProduct) {
        const slotsArray = [5, 6, 7, 8, 18, 19, 20]
        const hourNow = TimeUtil.now(userContext.userProfile.timezone).hour
        let nextSlotIndex: number = 0
        let dateOffset: number = 0
        for (const slot of slotsArray) {
            if (hourNow < slot) {
                break
            }
            nextSlotIndex++
        }
        for (let i = 0; i < 3; i++) {
            if (nextSlotIndex >= slotsArray.length) {
                nextSlotIndex = 0
                dateOffset++
            }
            const slotDate = TimeUtil.getDate(TimeUtil.todaysDate(userContext.userProfile.timezone), slotsArray[nextSlotIndex], 0, userContext.userProfile.timezone)
            slotDate.setDate(slotDate.getDate() + dateOffset)
            const slotAction = await LiveUtil.getDIYCalendarEventAction(userContext, diyProduct, interfaces.classInviteLinkCreator, slotDate)
            this.futureSlots.push({
                title: this.getSlotName(slotsArray[nextSlotIndex]),
                action: {
                    actionType: "HIDE_DIGITAL_JOURNEY_RETENTION_INTERSTITIAL",
                    title: "NO",
                    completionAction: slotAction,
                    analyticsData: {
                        eventKey: "button_click_event",
                        eventData: {
                            contentId: diyProduct.productId,
                            productType: "DIY_FITNESS",
                            actionType: "overlay_close",
                            source: "schedule_for_later"
                        }
                    }
                }
            })
            nextSlotIndex++
        }
        if (!AppUtil.isAppVersionBelow(userContext, 100, 8.57)) {
            const moreAction = await LiveUtil.getDIYCalendarEventAction(userContext, diyProduct, interfaces.classInviteLinkCreator)
            moreAction.completionAction = {
                actionType: "HIDE_DIGITAL_JOURNEY_RETENTION_INTERSTITIAL",
                analyticsData: {
                    eventKey: "button_click_event",
                    eventData: {
                        contentId: diyProduct.productId,
                        productType: "DIY_FITNESS",
                        actionType: "overlay_close",
                        source: "schedule_for_later"
                    }
                }
            }
            this.futureSlots.push({
                title: "MORE",
                action: moreAction
            })
        }
    }

    private getSlotName(slotNumber: number): string {
        if (slotNumber === 0) {
            return "12 AM"
        } else if (slotNumber < 12) {
            return slotNumber + " AM"
        } else if (slotNumber === 12) {
            return slotNumber + " PM"
        } else {
            return (slotNumber - 12) + " PM"
        }
    }

    private async getWorkoutMetrics(interfaces: CFServiceInterfaces, classId: string, userContext: UserContext, userSessionId: string, contentType: SessionContentType) {
        const userId = userContext.userProfile.userId
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        const metricsResponse = contentType === "DIY_FITNESS" ? await interfaces.diyService.getUserScoreMetricsV2(userSessionId, contentType) : await interfaces.diyService.getUserScoreMetrics(classId, userContext.userProfile.userId)
        let liveSession
        if (contentType === "DIY_FITNESS") {
            liveSession = (await interfaces.diyService.getDIYFitnessProductsByProductIds(userContext.userProfile.userId, [classId], tenant))[0]
        } else {
            liveSession = await interfaces.diyService.getDigitalCatalogueEntry(classId)
        }
        const formattedDuration = DigitalReportViewBuilder.millisToMinutesAndSeconds(metricsResponse.playbackMillis)
        const workoutDuration = {
            title: "Workout duration",
            data: `${formattedDuration}`,
            units: "Min"
        }
        this.workoutMetrics.push(workoutDuration)
        const caloriesBurnt = metricsResponse?.caloriesBurnt
        if (!_.isNil(caloriesBurnt) && _.isFinite(caloriesBurnt)) {
            this.workoutMetrics.push({
                title: "Appx Calorie Burnt",
                data: `${caloriesBurnt}`,
                units: "Cal",
            })
        }

        const isEnergyMeterSupported = liveSession.features?.includes("ENERGY") && !_.isNil(metricsResponse?.energyMeterState) && metricsResponse.energyMeterState !== "DEVICE_UNSUPPORTED"
        const shouldShowEnergyInfolet = (!_.isNil(metricsResponse?.score) || isEnergyMeterSupported) && !DigitalReportViewBuilder.shouldOmitReportMetric(<DigitalCatalogueEntryV1>liveSession, "ENERGY")
        const recommendEnergyMeter = (isEnergyMeterSupported && _.isNil(metricsResponse.score))
        if (shouldShowEnergyInfolet) {
            this.workoutMetrics.push({
                title: "ENERGY SCORE",
                data: recommendEnergyMeter ? "-" : `${Math.round(metricsResponse.score)}`,
                units: "",
            })
        }

        const shouldShowRankInfolet = (!_.isNil(metricsResponse?.rank) || isEnergyMeterSupported) && !DigitalReportViewBuilder.shouldOmitReportMetric(<DigitalCatalogueEntryV1>liveSession, "RANK")
        if (shouldShowRankInfolet) {
            this.workoutMetrics.push({
                title: "CLASS RANK",
                data: recommendEnergyMeter ? "-" : `${metricsResponse.rank || "-"}`,
                units: _.isNil(metricsResponse) ? "" : `/${metricsResponse.totalCount}`,
            })
        }
    }

}