import { CFLiveProduct, ImageData } from "@curefit/diy-common"
import { AppleIAPProductType, Duration, ProductPrice } from "@curefit/product-common"
import { WidgetView } from "@curefit/apps-common"
import { WidgetType } from "@curefit/vm-common"
import { BaseWidget, IBaseWidget, IServiceInterfaces, UserContext } from "@curefit/vm-models"
import { Action } from "@curefit/apps-common"
import { PageWidget } from "../page/Page"
import { TemplateWidget } from "@curefit/vm-common"

export class LiveMembershipPacksPage {
    selectedProductId: string
    pageAction: Action
    packs: CFLiveProduct[]
    widgets: (BaseWidget | WidgetView | PageWidget | TemplateWidget | IBaseWidget)[] = []
    titleImage?: string
    title?: string
    bannerImages: ImageData
    assets?: AssetDetails[]
}

export class AssetDetails {
    type: string
    mediaUrl: string
    thumbnailUrl?: string
    hlsUrl?: string
    dashUrl?: string
    assetUrl: string
}

export interface ProductPriceWithBreakup extends ProductPrice {
    priceBreakup?: any
}

export class LivePack {
    productId: string
    duration: Duration
    title: string
    offerText: string
    isSelected: boolean = false
    price: ProductPriceWithBreakup
    isPack: boolean
    quantity: number = 1
    packAction: Action
    perMonthPrice?: string
    offers?: ILivePackOffer[]
    offerSummary?: string
    heroOffer?: string
}

export class ILivePackOffer {
    title: string
    tncAction?: Action
}

export class LivePackPickerWidget extends BaseWidget {
    buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget | IBaseWidget[]> {
        throw new Error("Method not implemented.")
    }
    widgetType: WidgetType
    title: string
    subTitle: string
    tag?: string
    packs: LivePack[]
    showRestore: boolean = true
    restoreAction: Action
    rootStyle?: any
    cardStyle?: any
    titleStyle?: any
    showCheck?: any
    offerText?: string
    showOffers?: boolean
}
