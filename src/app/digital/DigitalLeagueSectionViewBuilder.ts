import { inject, injectable } from "inversify"
import { UserContext } from "@curefit/vm-models"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CacheHelper } from "../util/CacheHelper"
import { ISocialService, SOCIAL_CLIENT_TYPES } from "@curefit/social-client"
import {
  CommunitiesResponse,
  CommunityRecommendationSource,
  CommunityRecommendedUserEntry,
  CommunityType,
  CommunityUserMappingEntry,
  CommunityUserMappingState,
  PendingCommunityInviteResponse,
  UserProfileEntry
} from "@curefit/social-common"
import * as _ from "lodash"
import {
  Action,
  LeagueLeaderBoardWidget,
  LeagueRequestStatusSectionListWidget,
  LeagueRequestStatusWidget,
  LeagueSection,
  User
} from "@curefit/apps-common"
import CultUtil from "../util/CultUtil"
import { eternalPromise } from "@curefit/util-common"
import AppUtil from "../util/AppUtil"
import { IQuestService, QUEST_CLIENT_TYPES } from "@curefit/quest-client"
import { ProductType, UrlPathBuilder } from "@curefit/product-common"
import { BASE_TYPES, Logger } from "@curefit/base"
import { IdVsValueMap } from "./DigitalSocialLeagueTabPageViewBuilder"
import moment = require("moment")
import LiveUtil, { CONTACTS_SYNC_BANNER_RATIO } from "../util/LiveUtil"
import { CommunityInviteType } from "@curefit/social-common/dist/src/enums"
import { Invite } from "@curefit/riddler-common"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { RollbarService } from "@curefit/error-common"

export interface CommonData {
  membersMappingEntryResponse?: CommunityUserMappingEntry[]
  recommendationsResponse?: CommunityRecommendedUserEntry[]
  pendingInvitesResponse?: PendingCommunityInviteResponse[]
  communitiesResponse?: CommunitiesResponse
  currentUserCommunityId?: number
}

export interface PendingInvitesSection extends LeagueSection {
  inviterEntries?: { entry: CommunityUserMappingEntry, creator: User}[]
}

export interface LeagueListSection extends LeagueSection {
  userLeagueHasBeenCreatedFE?: boolean
}

interface PendingSentInviteEntry {userId: string, mappingEntryId: number, inviteType: CommunityInviteType}
interface PendingSentInviteGuestEntry {name: string, mappingEntryId: number, inviteType: CommunityInviteType}

export const RECOMMENDED_USERS_LIMIT = 10
export const PENDING_INVITES_LIMIT = 50
export const RECOMMENDED_USERS_TO_SHOW = 50
export const LEAGUE_LIST_MEMBER_LIMIT = 50
export const MEMBERS_INITIAL_LIMIT = 50
export const CLP_RECOMMENDATION_LIMIT = 2

export const RECOMMENDED_USER_SMALL_ICON_LIMIT = 3

export const UserMappingStateToastMessages: {[key: string]: {success: string, failed: string}} = {
  "REJECTED": {
    success: "Invite rejected",
    failed: "Failed to reject invite"
  },
  "LEAVE": {
    success: "Squad left",
    failed: "Failed to leave the Squad"
  },
  "REMOVE": {
    success: "Successfully removed member",
    failed: "Failed to remove the member"
  },
  "PENDING": {
    success: "Invite sent",
    failed: "Failed to sent invite"
  },
  "CONFIRMED": {
    success: "You are in the Squad!",
    failed: "Failed to accept invite"
  },
  "CREATE": {
    success: "Squad created successfully",
    failed: "Failed to create squad"
  },
  "INTERNATIONAL_PENDING": {
    failed: "Non-Indian members not supported as of now",
    success: "Invite sent"
  },
  "CANCEL": {
    failed: "Failed to cancel invite",
    success: "Invite cancelled"
  }
}

export const LEAGUE_BORWSE_USER_PROFILE_TITLE = "Squad member"

export const Colors = {
  reddishPinkFour: "#ff3278",
  lightDivider: "#f4f4f4"
}

const BADGE_GRADIENT_INFO = [{color: "#ffffff", opacity: 0}, {color: "#008300", opacity: 0.12}]

const CREATE_LEAGUE_SUBTITLE = "Create your Squad with friends you work out with"

export const DUMMY_USER_IMAGE = "/image/diy/Leagues/dummyUser.png"
export const CULT_LOGO = "/image/community/cult_logo.png"



export const Fonts = {
  Bold: "BrandonText-Bold",
  Regular: "BrandonText-Regular",
}

const REJECT_PROMPT = {
  title: "Reject Invite?",
  subTitle: "Your friends were waiting for you to join the Squad but it's okay, we will inform them that you are not looking to join the Squad as of now."
}

const CANCEL_PROMPT = {
  title: "Cancel Invite?",
  subTitle: "Are you sure you want to cancel this invite?"
}

export const ACCEPT_PROMPT = {
  title: "You are in the Squad!",
  subTitle: "See what your friends are upto"

}

export const REMOVE_MEMBER_PROMPT = {
  title: "",
  subTitle: ""
}

export enum LeaguePageId {
  WALL = "WALL",
  ALL_LEAGUES = "ALL_LEAGUES"
}


export enum LeagueSectionId {
  RECEIVED_INVITES= "RECEIVED_INVITES",
  SENT_INVITES = "SENT_INVITES",
  RECOMMENDATION = "RECOMMENDATION",
  MEMBERS = "MEMBERS",
  LEAGUES = "LEAGUES"
}

export enum LeagueSectionSource {
  WALL_PAGE = "WALL_PAGE",
  MODAL = "MODAL", // LEAGUE_ADD_CONTACT_MODAL
  CLP = "CLP",
  CREATE_SQUAD_MODAL = "CREATE_SQUAD_MODAL",
  SQUAD_CHALLENGE_PAGE = "SQUAD_CHALLENGE_PAGE"
}

@injectable()
class DigitalLeagueSectionViewBuilder {
  constructor(
    @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
    @inject(SOCIAL_CLIENT_TYPES.SocialService) private socialService: ISocialService,
    @inject(QUEST_CLIENT_TYPES.IQuestService) private questService: IQuestService,
    @inject(BASE_TYPES.ILogger) private logger: Logger,
    @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
  ) {}

  async buildView(userContext: UserContext, extraParams: { id: LeaguePageId, callParams: LeagueRequestStatusSectionListWidget["callParams"], communityId?: number, paginationEnabled?: boolean}): Promise<any> {
    const {callParams,  paginationEnabled, id} = extraParams
    const userId = userContext.userProfile.userId
    const currentUser = await this.userCache.getUser(userId)
    const sectionIdsPresent: {[key: string]: LeagueRequestStatusSectionListWidget["callParams"][0]} = {}
    callParams.forEach((param) => {
      sectionIdsPresent[param.sectionId] = param
    })
    const membersCallParam = sectionIdsPresent[LeagueSectionId.MEMBERS]
    const commonData: CommonData = membersCallParam ?  await this.getCommonData({currentUser, communityId: membersCallParam.communityId, limit: membersCallParam.limit, offset: membersCallParam.offset, currentUserIsCreator: membersCallParam.currentUserIsCreator}, sectionIdsPresent) : {}

    let badgeDescriptionListPromise
    const source = callParams.find(param => param.source).source as LeagueSectionSource
    const productType = callParams.find(param => param?.productType)?.productType as ProductType
    const leagueAddContactModalCallParam = callParams.find((param) => param.source === LeagueSectionSource.MODAL)
    if (leagueAddContactModalCallParam) {
      badgeDescriptionListPromise = eternalPromise(this.getBadgeDescriptionList(userId, leagueAddContactModalCallParam.communityId, true))
    }

    const sectionsPromise = Promise.all(callParams.map((param) => {
      switch (param.sectionId) {
        case LeagueSectionId.LEAGUES: {
          return eternalPromise(this.getLeagueListSection({currentUser, offset: param.offset, limit: param.limit, source: param.source as LeagueSectionSource, paginationEnabled: false, commonData}))
        }
        case LeagueSectionId.MEMBERS: {
          return eternalPromise(this.getLeagueMembersSection({currentUser, communityId: param.communityId, limit: param.limit, offset: param.offset, currentUserIsCreator: param.currentUserIsCreator}, commonData))
        }
        case LeagueSectionId.RECOMMENDATION: {
          return eternalPromise(this.getRecommendedUsersSection(userContext, param.communityId, commonData, param.source as LeagueSectionSource))
        }
        case LeagueSectionId.SENT_INVITES: {
          return eternalPromise(this.getSentInvitesSection(userContext, param.communityId, commonData, param.source as LeagueSectionSource))
        }
        case LeagueSectionId.RECEIVED_INVITES: {
          return eternalPromise(this.getPendingReceivedInvitationsSection(currentUser, param.limit, param.offset, param.source as LeagueSectionSource))
        }
      }
    }))

    const bodyResponse = await Promise.all([sectionsPromise, badgeDescriptionListPromise])
    const sections = bodyResponse[0].map((sectionResponse) => sectionResponse?.obj).filter(item => item && !_.isEmpty(item.data))
    const badgeDescriptionList = bodyResponse[1]?.obj

    let listHeaderAction: Action
    let listHeaderImage: string
    let hasListHeader =  Boolean(badgeDescriptionList)
    let bannerImageRatio: number
    let listHeaderStyle
    if (await LiveUtil.isContactsSyncSupportedAndSquadExists(userContext, this.socialService, commonData.currentUserCommunityId) && source === LeagueSectionSource.WALL_PAGE) {
      listHeaderAction = {
        actionType: "NAVIGATION",
        url: "curefit://leagueinvitepage?title=Invite"
      }
      listHeaderImage = productType === "FITNESS" ? "image/diy/Leagues/CultContactSyncBanner.png" : "image/diy/Leagues/ContactsSyncBannerV4.png"
      hasListHeader = true
      bannerImageRatio = CONTACTS_SYNC_BANNER_RATIO
      listHeaderStyle = {marginTop: 20}
    }
    return {
      sections,
      paginationEnabled: false,
      callParams: undefined, // no pagination for now,
      refreshEnabled: true,
      badgeDescriptionList,
      listHeaderAction,
      listHeaderImage,
      hasListHeader,
      bannerImageRatio,
      listHeaderStyle
    }
  }

  async getCommonData(params: {currentUser: User, communityId: number, limit: number, offset: number, currentUserIsCreator: boolean}, sectionIdsPresent: {[id: string]: LeagueRequestStatusSectionListWidget["callParams"][0]}) {
     /* can reuse commonData since each section will have the same community if the MEMBERS section is present, add stricter checks if the candition changes */
    const {offset, communityId, currentUser, limit, currentUserIsCreator} = params
    const currentUserId = currentUser.id

    const [{obj: membersMappingEntryResponse}, {obj: recommendationsResponse}, {obj: pendingInvitesResponse}] = await Promise.all(
      [eternalPromise(this.socialService.getCommunityMembers(communityId, currentUserId, 50, 0)),
        eternalPromise(this.socialService.getCommunityRecommendationsV2(currentUserId)),
        eternalPromise(this.socialService.getCommunityPendingInvites(communityId, currentUserId, PENDING_INVITES_LIMIT, 0))
      ])

    return {membersMappingEntryResponse, recommendationsResponse, pendingInvitesResponse}
  }

  async getSentInvitesSection(userContext: UserContext, communityId: number, commonData: CommonData, source: LeagueSectionSource) {
    const userId = userContext.userProfile.userId
    const {obj: pendingInvites} =  commonData?.pendingInvitesResponse ? {obj: commonData.pendingInvitesResponse} : await eternalPromise(this.socialService.getCommunityPendingInvites(communityId, userId, PENDING_INVITES_LIMIT, 0))

    const invitePendingEntries: PendingSentInviteEntry[] = []
    const invitePendingGuestUsersName: PendingSentInviteGuestEntry[] = []

    pendingInvites?.forEach((pendingInvite) => {
      if (pendingInvite.userId) {
        invitePendingEntries.push({userId: pendingInvite.userId, mappingEntryId: (<any>pendingInvite).id, inviteType: pendingInvite.inviteType})
      } else if (pendingInvite.name || pendingInvite.phoneNumber) {
        invitePendingGuestUsersName.push({name: pendingInvite.name || pendingInvite.phoneNumber, mappingEntryId: (<any>pendingInvite).id, inviteType: pendingInvite.inviteType})
      }
    })

    const invitedGuestUsers = this.getGuestInviteSectionData(invitePendingGuestUsersName, source, communityId, userContext)
    const invitePendingUsers = await this.createRecommendationOrInviteSectionItem(invitePendingEntries, communityId, source, userContext, LeagueSectionId.SENT_INVITES)

    return {
      header: {
        title: "Invites sent"
      },
      data: [...invitePendingUsers, ...invitedGuestUsers],
      key: LeagueSectionId.SENT_INVITES,
      hasFooter: source !== LeagueSectionSource.CLP
    }

  }

  async getLeagueListSection(params: {currentUser: User, offset: number, limit: number, source: LeagueSectionSource, paginationEnabled?: boolean, refreshEnabled?: boolean, commonData?: CommonData, sendUserLeagueHasBeenCreatedFE?: boolean}): Promise<LeagueListSection> {
    const { limit = LEAGUE_LIST_MEMBER_LIMIT, offset = 0, currentUser, source, commonData, sendUserLeagueHasBeenCreatedFE } = params
    const userId = currentUser.id
    const {obj: communitiesResponse} = await eternalPromise(this.socialService.getUserCommunities(userId, CommunityType.LEAGUE, true, limit, offset))
    if (commonData) {
      commonData.communitiesResponse = communitiesResponse
    }
    const recommendedUsers = !_.isEmpty(communitiesResponse?.communityRecommendedUserEntry) ? await this.getRecommendedUsers(_.map(communitiesResponse.communityRecommendedUserEntry, "userId")) : undefined
    let currentUserLeagueCommunityId: number

    const data =  communitiesResponse ? (await Promise.all(communitiesResponse.communities.map(async (community) => {
      if (userId.toString() === community.creatorNode.entityId.toString()) {
        currentUserLeagueCommunityId = community.id
        return undefined
      }
      let name = community.name
      let profilePictureUrl = DUMMY_USER_IMAGE
      try {
        if (community.creatorNode.entityType !== "SYSTEM") {
          const creator = await this.userCache.getUser(community.creatorNode.entityId)
          profilePictureUrl = creator.profilePictureUrl || DUMMY_USER_IMAGE
          name = community.name || DigitalLeagueSectionViewBuilder.getLeagueName(creator)
        } else {
          profilePictureUrl = CULT_LOGO
        }
      } catch (error) {
        this.logger.error(error)
      }
      return DigitalLeagueSectionViewBuilder.getLeagueListSectionItem([{text: name, style: {fontFamily: Fonts.Bold}}], profilePictureUrl, [DigitalLeagueSectionViewBuilder.getShowLeagueAddContactModalAction("VIEW", name, false, LeagueSectionSource.MODAL, community.id, Colors.reddishPinkFour)])
    }))).filter(item => item) : []

    let userLeagueHasBeenCreatedFE // user has created league based on front end definition i.e has a member or pending invite

    let totalUsersInOwnCommunity

    if (currentUserLeagueCommunityId) {
      if (commonData) {
        commonData.currentUserCommunityId = currentUserLeagueCommunityId
      }
      const [{obj: usersInCurrentUserCommunity}, {obj: pendingInvites}] = await Promise.all([
        eternalPromise(this.socialService.getCommunityMembers(currentUserLeagueCommunityId, userId, 2, 0)),
        eternalPromise(this.socialService.getCommunityPendingInvites(currentUserLeagueCommunityId, userId, 1, 0))
      ])
      totalUsersInOwnCommunity = usersInCurrentUserCommunity?.length
      userLeagueHasBeenCreatedFE =  DigitalLeagueSectionViewBuilder.userLeagueHasBeenCreatedFE(usersInCurrentUserCommunity?.length, pendingInvites?.length)
    }

    let leagueAction: Action
    if (!currentUserLeagueCommunityId) {
      leagueAction = DigitalLeagueSectionViewBuilder.getCreateCommunityAction("CREATE", Colors.reddishPinkFour)
    } else {
      const actionTitle = userLeagueHasBeenCreatedFE ? "MANAGE" : "CREATE"
      leagueAction = DigitalLeagueSectionViewBuilder.getShowLeagueAddContactModalAction(actionTitle,  "Your Squad", true, LeagueSectionSource.MODAL, currentUserLeagueCommunityId, Colors.reddishPinkFour)
    }

    const numOtherCommunitiesJoined = (communitiesResponse?.communities?.length - (currentUserLeagueCommunityId ? 1 : 0)) || undefined

    const userLeagueDescription = [{text: "Your Squad", style: {fontFamily: Fonts.Bold}}, ...!currentUserLeagueCommunityId ? [{text: `\n${CREATE_LEAGUE_SUBTITLE}`}] : []]
    const userLeagueItem = DigitalLeagueSectionViewBuilder.getLeagueListSectionItem(userLeagueDescription, currentUser.profilePictureUrl || DUMMY_USER_IMAGE, [leagueAction], null, recommendedUsers)

    return {
      data: [userLeagueItem, ...data],
      key: LeagueSectionId.LEAGUES,
      hasFooter: source !== LeagueSectionSource.CLP,
      numOtherCommunitiesJoined,
      totalUsersInOwnCommunity,
      userLeagueHasBeenCreatedFE: sendUserLeagueHasBeenCreatedFE ? userLeagueHasBeenCreatedFE : undefined
    }
  }

  async getRecommendedUsersSection(userContext: UserContext, communityId: number, commonData: CommonData, source: LeagueSectionSource): Promise<LeagueSection> {
    const userId = userContext.userProfile.userId
    const [{obj: recommendationsResponse}, {obj: pendingInvitesResponse}, {obj: membersMappingEntryResponse}] = await Promise.all([
      commonData?.recommendationsResponse ? eternalPromise(Promise.resolve(commonData.recommendationsResponse)) : eternalPromise(this.socialService.getCommunityRecommendationsV2(userId)),
      commonData?.pendingInvitesResponse ? eternalPromise(Promise.resolve(commonData.pendingInvitesResponse)) : eternalPromise(this.socialService.getCommunityPendingInvites(communityId, userId, PENDING_INVITES_LIMIT, 0)),
      commonData?.membersMappingEntryResponse ? eternalPromise(Promise.resolve(commonData.membersMappingEntryResponse)) : eternalPromise(this.socialService.getCommunityMembers(communityId, userId, MEMBERS_INITIAL_LIMIT, 0))
    ])

    const memberUserIdsSet = LiveUtil.createBooleanMapping(membersMappingEntryResponse, "userId")

    const invitePendingUserIdsSet = LiveUtil.createBooleanMapping(pendingInvitesResponse, "userId")

    if (await LiveUtil.getLeagueInviteLimit(membersMappingEntryResponse.length, pendingInvitesResponse.length) <= 0) {
      return undefined
    }

    const recommendationsToBeShown: CommunityRecommendedUserEntry[] = recommendationsResponse.filter(recommendationResponse => recommendationResponse && !memberUserIdsSet[recommendationResponse.userId] && !invitePendingUserIdsSet[recommendationResponse.userId])

    const recommendationItems = (await this.createRecommendationOrInviteSectionItem(recommendationsToBeShown, communityId, source, userContext, LeagueSectionId.RECOMMENDATION)).slice(0, RECOMMENDED_USERS_TO_SHOW)

    return {
      header: {
        title: "Recommended Profiles",
        subTitle: "Add members to your Squad based on your past invites"
      },
      data: recommendationItems,
      key: LeagueSectionId.RECOMMENDATION,
      hasFooter: source !== LeagueSectionSource.CLP
    }
  }

  async getLeagueMembersSection(params: {currentUser: User, communityId: number, limit: number, offset: number, currentUserIsCreator: boolean}, commonData: CommonData): Promise<LeagueSection> {
    const {offset, communityId, currentUser, limit, currentUserIsCreator} = params
    const currentUserId = currentUser.id

    const membersMappingEntryResponse = commonData?.membersMappingEntryResponse || await this.socialService.getCommunityMembers(communityId, currentUserId, 50, 0)
    const userIdVsMappingEntryId = new Map<string, number>(membersMappingEntryResponse.map((entry) => [entry.userId, entry.id]))
    const members = membersMappingEntryResponse.map((entry) => entry.userId)

    const [users, userSocialEntries] = await Promise.all([this.userCache.getUsers(members), this.socialService.getUserProfileByUserIdBulk(members)])

    const userProfileEntryVsId: {[key: string]: UserProfileEntry} = {}
    userSocialEntries.forEach((entry) => {
      if (entry?.userId) {
        userProfileEntryVsId[entry.userId] = entry
      }
    })

    const userIds = _.keys(users)

    let currentUserItem

    const data = userIds.map(id => {
      const user = users[id]
      const name = user.firstName + (user.lastName ? ` ${user.lastName}` : "")
      const mappingEntryId = userIdVsMappingEntryId.get(id)

      if (id.toString() === currentUserId.toString()) {
        const actions = []
        if (!currentUserIsCreator) {
          const leaveGroupAction = DigitalLeagueSectionViewBuilder.getSetCommunityMappingStateAction("LEAVE GROUP", CommunityUserMappingState.LEFT, mappingEntryId, id, null, null, true, undefined, undefined, true)
          actions.push(leaveGroupAction)
        }
        currentUserItem = {
          imageUrl: user.profilePictureUrl || DUMMY_USER_IMAGE,
          hasDividerBelow: true,
          dividerColor: Colors.lightDivider,
          description: [{text: "You", style: {fontFamily: "BrandonText-Bold"}}],
          actions
        }
        return undefined
      } else {
        const actions = []

        if (currentUserIsCreator) {
          const removeAction = DigitalLeagueSectionViewBuilder.getSetCommunityMappingStateAction("REMOVE", CommunityUserMappingState.LEFT, mappingEntryId)
          actions.push(removeAction)
        }
        if (userProfileEntryVsId[id]) {
          const viewProfileAction = {
            ...CultUtil.getProfileClickAction(true, true, 0, "", {
              byUserIds: true,
              targetUserIds: [id]
            }),
            title: "VIEW PROFILE",
            textColor: Colors.reddishPinkFour,
            shouldCloseModal: true,
            source: "leagues"
          }
          actions.push(viewProfileAction)
        }

        return {
          imageUrl: user.profilePictureUrl || DUMMY_USER_IMAGE,
          hasDividerBelow: true,
          dividerColor: Colors.lightDivider,
          description: [{text: name, style: {fontFamily: "BrandonText-Bold"}}],
          actions
        }
      }
    }).filter(item => item)

    if (currentUserItem) {
      data.unshift(currentUserItem)
    }
    return {
      header: {
        title: "Members",
      },
      data,
      key: LeagueSectionId.MEMBERS,
    }
  }


  async createRecommendationOrInviteSectionItem(entries: (PendingSentInviteEntry | CommunityRecommendedUserEntry)[], selfCommunityId: number, source: LeagueSectionSource, userContext: UserContext, sectionId: LeagueSectionId.SENT_INVITES | LeagueSectionId.RECOMMENDATION): Promise<Array<Omit<LeagueRequestStatusWidget, "widgetType">>> {
    const isSentInvitesSection = sectionId === LeagueSectionId.SENT_INVITES
    const userIdVsEntries = _.keyBy(entries, "userId")
    const users = await this.userCache.getUsers(entries.map(({userId}) => {
      return userId
    }))

    const data = entries.map(({userId: id}) => {
      const user = users[id]
      const entry = userIdVsEntries[id]
      const name = LiveUtil.getLeagueRecommendedUserName(user, ((<PendingSentInviteEntry>entry).inviteType === CommunityInviteType.PHONE_NUMBER) || ((<CommunityRecommendedUserEntry>entry).recommendationSource === CommunityRecommendationSource.CONTACT_SYNC))
      if (_.isEmpty(name)) {
        return undefined
      }
      return {
        imageUrl: user.profilePictureUrl || DUMMY_USER_IMAGE,
        hasDividerBelow: true,
        dividerColor: Colors.lightDivider,
        description: [{text: name}],
        check: isSentInvitesSection ? {text: "Invite Sent"} : undefined,
        actions: !isSentInvitesSection ? [DigitalLeagueSectionViewBuilder.getInviteOthersAction("ADD", selfCommunityId, id, Colors.reddishPinkFour, true)] :
            AppUtil.isCancelSquadInviteSuported(userContext) && ("mappingEntryId" in entry) ?
                [DigitalLeagueSectionViewBuilder.getCancelInvitePopUpAction(entry.mappingEntryId, source, selfCommunityId, entry.inviteType, id)] :
                undefined
      }
    }).filter(item => item)

    if (sectionId === LeagueSectionId.SENT_INVITES) {
      data.sort((i1, i2) => {
            return i1.description[0]?.text < i2.description[0]?.text ? -1 : 1
          })
    }


    return data
  }

  getGuestInviteSectionData(guestUserNames: PendingSentInviteGuestEntry[], source: LeagueSectionSource, selfCommunityId: number, userContext: UserContext): Array<Omit<LeagueRequestStatusWidget, "widgetType">> {
    return guestUserNames.map(({name, mappingEntryId, inviteType}) => {
      return {
        imageUrl: DUMMY_USER_IMAGE,
        hasDividerBelow: true,
        dividerColor: Colors.lightDivider,
        description: [{text: name}],
        check: {text: "Invite Sent"},
        actions: AppUtil.isCancelSquadInviteSuported(userContext) ? [DigitalLeagueSectionViewBuilder.getCancelInvitePopUpAction(mappingEntryId, source, selfCommunityId, inviteType)] : undefined
      }
    })
  }

  async getPendingReceivedInvitationsSection(currentUser: User, limit: number, offset: number, source?: LeagueSectionSource, sendInviterData?: boolean): Promise<PendingInvitesSection> {
    const currentUserId = currentUser.id
    const receivedPendingInvitesEntries = await this.socialService.getPendingInvitesForUser(currentUserId, true, limit, offset, )
    const inviterEntries: PendingInvitesSection["inviterEntries"] = []
    const data = (await Promise.all(receivedPendingInvitesEntries.map(async (pendingInviteEntry) => {
      try {
        const community = pendingInviteEntry.community
        const creator = await this.userCache.getUser(community.creatorNode.entityId)
        inviterEntries.push({entry: pendingInviteEntry, creator})
        const rejectAction = DigitalLeagueSectionViewBuilder.getPopUpAction("REJECT", REJECT_PROMPT.title, REJECT_PROMPT.subTitle,
          [{
            title: "Cancel",
            actionType: "HIDE_ALERT_MODAL"
          },
            DigitalLeagueSectionViewBuilder.getSetCommunityMappingStateAction("Reject", CommunityUserMappingState.REJECTED, pendingInviteEntry.id, currentUserId, null, LeaguePageId.WALL, source === LeagueSectionSource.CREATE_SQUAD_MODAL, source)
          ]
        )
        const acceptAction = DigitalLeagueSectionViewBuilder.getSetCommunityMappingStateAction("ACCEPT", CommunityUserMappingState.CONFIRMED, pendingInviteEntry.id, currentUserId, Colors.reddishPinkFour, undefined, false, source)

        const actions = [rejectAction, acceptAction]

        const description = []
        if (source == LeagueSectionSource.CLP || LeagueSectionSource.SQUAD_CHALLENGE_PAGE) {
          description.push({text: "You are invited to"})
          description.push({text: ` ${DigitalLeagueSectionViewBuilder.getLeagueName(creator)}`, style: {fontFamily: Fonts.Bold}})
        } else {
          description.push({text: community.name, style: {fontFamily: Fonts.Bold}})
        }

        return DigitalLeagueSectionViewBuilder.getLeagueListSectionItem(description, creator.profilePictureUrl, actions)

      }
      catch (e) {
        this.logger.error(e)
        return undefined
      }
    }))).filter(item => item)

    return !_.isEmpty(data) ? {
      header: {
        title: `Invitations (${data.length})`,
        subTitle: "Invitations from your friends to join their Squads"
      },
      data,
      key: LeagueSectionId.RECOMMENDATION,
      hasFooter: source !== LeagueSectionSource.CLP,
      inviterEntries: sendInviterData ? inviterEntries : undefined
    } : undefined
  }

  async getBadgeDescriptionList(userId: string, communityId: number, isDescriptionList?: boolean) {
    const activitySummary = await this.questService.getGroupActivitySummary(String(communityId), "SOCIAL")
    const badgeList: LeagueRequestStatusSectionListWidget["badgeDescriptionList"]["badgeList"] | LeagueLeaderBoardWidget["badgeList"] = await Promise.all(activitySummary.badges
      .sort((b1, b2) => moment(b1.awardedAt).valueOf() - moment(b2.awardedAt).valueOf())
      .map(async ({badgeId}) => {
      const badgeData = await this.questService.getBadgeById(badgeId)
      return {
        name: isDescriptionList ? badgeData.name : undefined,
        imageUrl: UrlPathBuilder.getBadgeImagePath(badgeId),
        gradientInfo: !isDescriptionList ? BADGE_GRADIENT_INFO : undefined,
      }
    }))
    return {
      title: isDescriptionList ? `Badges Earned (${badgeList.length})` : undefined,
      badgeList
    }
  }


  static getShowLeagueAddContactModalAction(title: string, modalHeaderTitle: string, currentUserIsCreator?: boolean, source?: LeagueSectionSource, communityId?: number, textColor?: string, disableAddContactCta?: boolean): Action {
    const callParams = currentUserIsCreator ?  [
      {
        sectionId: LeagueSectionId.SENT_INVITES,
        offset: 0,
        limit: PENDING_INVITES_LIMIT,
        communityId,
        source
      },
      {
        sectionId: LeagueSectionId.RECOMMENDATION,
        offset: 0,
        limit: RECOMMENDED_USERS_LIMIT,
        communityId,
        source,
      },
      {
        sectionId: LeagueSectionId.MEMBERS,
        offset: 0,
        limit: MEMBERS_INITIAL_LIMIT,
        currentUserIsCreator: true,
        communityId,
        source,
      }
    ] : [
      {
        sectionId: LeagueSectionId.MEMBERS,
        offset: 0,
        limit: MEMBERS_INITIAL_LIMIT,
        communityId,
        source
      }
    ]
    const leagueSectionListWidget: LeagueRequestStatusSectionListWidget = {
      widgetType: "LEAGUE_REQUEST_STATUS_SECTION_LIST_WIDGET",
      sections: [],
      paginationEnabled: false,
      refreshEnabled: true,
      callParams,
    }

    return {
      title,
      actionType: "SHOW_LEAGUES_ADD_CONTACT_MODAL",
      textColor,
      meta: {
        modalHeader: {
          title: modalHeaderTitle
        },
        modalCallParams: {communityId, currentUserIsCreator},
        leagueSectionListWidget
      }}
  }

  static getCancelInvitePopUpAction(mappingEntryId: number, source: LeagueSectionSource, selfCommunityId: number, inviteType: CommunityInviteType, targetUserId?: string) {
    return DigitalLeagueSectionViewBuilder.getPopUpAction("CANCEL", CANCEL_PROMPT.title, CANCEL_PROMPT.subTitle,
        [
          DigitalLeagueSectionViewBuilder.getSetCommunityMappingStateAction("Yes", CommunityUserMappingState.CANCEL, mappingEntryId, targetUserId, undefined, undefined, false, source, inviteType),
          {
          title: "No",
          actionType: "HIDE_ALERT_MODAL"
        },
        ],
        true,
        DigitalLeagueSectionViewBuilder.getShowLeagueAddContactModalAction("", "Your Squad", true, LeagueSectionSource.MODAL, selfCommunityId)
    )
  }



  static getLeagueListSectionItem(description: {text: string, style?: any}[], imageUrl: string, actions: Action[], check?: {text: string} | boolean, recommendedUsers?: LeagueRequestStatusWidget["recommendedUsers"]): Omit<LeagueRequestStatusWidget, "widgetType"> {
    return {
      hasDividerBelow: true,
      dividerColor: Colors.lightDivider,
      description,
      actions,
      imageUrl,
      check,
      recommendedUsers
    }
  }

  static getSetCommunityMappingStateAction(title: string, inviteState: CommunityUserMappingState, mappingEntryId: number, targetUserId?: string, textColor?: string, goToTabPage?: LeaguePageId, shouldCloseModal?: boolean, source?: LeagueSectionSource, inviteType?: CommunityInviteType, disableRowAction?: boolean): Action {
    return {
      title,
      textColor,
      actionType: "REST_API",
      shouldRefreshTabPage: true,
      shouldRefreshModal: true,
      shouldRefreshAllTabPages: true,
      shouldCloseModal,
      goToTabPage,
      disableRowAction,
      meta: {
        method: "post",
        url: `/digital/leagues/setInviteState`,
        body: {
          inviteState,
          mappingEntryId,
          targetUserId,
          source,
          inviteType
        },
      }
    }
  }

  static getInviteOthersAction(title: string, communityId: number, inviteeUserId: string, textColor?: string, disableRowAction?: boolean): Action {
    return {
      title,
      textColor,
      disableRowAction,
      shouldRefreshTabPage: true,
      shouldRefreshModal: true,
      shouldRefreshAllTabPages: true,
      actionType: "REST_API",
      meta: {
        method: "post",
        url: `/digital/leagues/inviteUser`,
        body: {
          inviteeUserId,
          communityId,
        },
      }
    }
  }

  static getCreateCommunityAction(title: string, textColor?: string, successMessage?: string, source?: LeagueSectionSource): Action {
    return {
      title,
      textColor,
      shouldRefreshTabPage: true,
      shouldRefreshModal: true,
      shouldRefreshAllTabPages: true,
      actionType: "REST_API",
      meta: {
        successMessage,
        method: "post",
        url: `/digital/leagues/create`,
        body: {
          source
        },
      }
    }
  }

  static getPopUpAction(title: string, promptTitle: string, promptSubTitle: string, actions: Action[], shouldCloseModal?: boolean, nextAction?: Action): Action {
    return {
      title,
      shouldCloseModal,
      textColor: "#000000",
      actionType: "SHOW_ALERT_MODAL",
      nextAction,
      meta: {
        title: promptTitle,
        subTitle: promptSubTitle,
        meta: {
          modalHeight: 260,
        },
        actions
      },
    }
  }

  static getTabbedPageNavigationAction(title: string, extraParams?: {[key: string]: any}): Action {
    return {
      actionType: "NAVIGATION",
      url: `curefit://leaguewalltabbedpage?selectedTab=ALL_LEAGUES?pageFrom=live_class_detail`,
      title: "View Squad Activity",
      ...extraParams
    }
  }

  async getRecommendedUsers(userIds: string[]) {
    const userIdData = await this.userCache.getUsers(userIds.slice(0, RECOMMENDED_USER_SMALL_ICON_LIMIT))
    const slicedUserIds = _.keys(userIdData)
    const buddies = slicedUserIds.map((id) => {
      return {
        icon: userIdData[id].profilePictureUrl,
        state: "1",
        name: ""
      }
    })
    return {
      infoText: (slicedUserIds.length < userIds.length ? `+${userIds.length - slicedUserIds.length}` : userIds.length) + " Recommended Friends",
      noReverse: true,
      infoTextStyle: {
        fontSize: 14,
        color: "#000000",
        fontFamily: Fonts.Regular
      }, containerStyle: {
        height: 20,
        width: 20,
        marginLeft: -10
      },
      iconStyle: {
        height: 16,
        width: 16,
        borderRadius: 8
      },
      buddies,
      legendData: [{color: "#ffffff", state: "1", title: ""}],
      showOverlappingIcons: true,
    }
  }

  static getLeagueName(creator: User) {
    return `${creator.firstName}'s Squad`
  }

  static userLeagueHasBeenCreatedFE(numUsersInCurrentUserCommunity: number, numPendingInvites: number) {
    return numUsersInCurrentUserCommunity > 1 || numPendingInvites > 0
  }
}

export default DigitalLeagueSectionViewBuilder
