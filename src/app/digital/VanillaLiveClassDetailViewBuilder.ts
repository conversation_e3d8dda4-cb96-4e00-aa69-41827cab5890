import {
    DigitalCatalogueEntryV1,
    SocialDataResponse,
    ChefDetail,
    RecipeDetail,
    DIYRecipeProduct, DIYRecipeView, LiveClass
} from "@curefit/diy-common"
import { PreferenceDetail } from "../cult/CultBusiness"
import { UserContext, BaseWidget, IBaseWidget } from "@curefit/vm-models"
import { LiveClassDetailView } from "../cult/LiveClassDetailViewBuilder"
import { WidgetView, PageWidget, ImageOverlayCardContainerWidget, HeaderWidget, Action, ExpandableSlotSelectionWidget, IWidgetType, SlotsInfo, LiveSlotSelectionWidget, DateWiseLiveSlots } from "@curefit/apps-common"
import { TemplateWidget, PageTypes } from "@curefit/vm-common"
import { User } from "@curefit/user-common"
import { eternalPromise, TimeUtil, Timezone } from "@curefit/util-common"
import LiveUtil from "../util/LiveUtil"
import { getIngredientBulletInfoWidget } from "../recipe/RecipeDetailView"
import _ = require("lodash")
import { ClassInviteLinkCreator } from "../cult/invitebuddy/ClassInviteLinkCreator"
import { ManageOptionsWidgetV2, DescriptionWidget, InfoCard, CultBuddiesJoiningListLargeView } from "../common/views/WidgetView"
import { HourMin } from "@curefit/base-common"
import { EatLiveRecipeWidgetBuilder } from "../page/vm/widgets/EatLiveRecipeWidgetBuilder"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { LiveClassDetailViewV2 } from "../cult/LiveClassDetailViewBuilderV2"
import { IDIYRecipe, IRecipeParams } from "../recipe/RecipeCommon"
import AppUtil from "../util/AppUtil"


export async function builVanillaLiveClassViewV2(classResponse: LiveClass, subscribedVideos: string[], preference: PreferenceDetail, userContext: UserContext, classId: string, classInviteLinkCreator: ClassInviteLinkCreator, interfaces: CFServiceInterfaces, selectedClassResponse?: DigitalCatalogueEntryV1, isNoSlotSelected?: boolean, isLocked?: boolean, isUserEligibleForMonetisation?: boolean): Promise<LiveClassDetailViewV2> {
    const actions: Action[] = []
    const widgetPromise: Promise<(BaseWidget | WidgetView | PageWidget | TemplateWidget)>[] = []
    const user: User = await userContext.userPromise
    const bannerImages = classResponse.bannerImages
    const isSubscribed = selectedClassResponse ? _.includes(subscribedVideos, (<any>selectedClassResponse)._id) : false
    widgetPromise.push(getImageOverlayCardContainerWidgetV2(user, classId, classResponse, selectedClassResponse, userContext, isSubscribed, undefined, userContext.userProfile.timezone, isLocked, undefined, isNoSlotSelected))
    const bookingPref = (_.isEmpty(preference)) ? false : preference.bookingEmailPreference
    const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, interfaces.hamletBusiness)
    let isUserEligibleForTrial = true
    if (isUserEligibleForMonetisation) {
        isUserEligibleForTrial = await LiveUtil.isUserEligibleForLivePackTrial(userContext, interfaces.diyService)
    }
    let pageAction = (await eternalPromise(LiveUtil.getStatusBasedSessionAction(user, userContext, selectedClassResponse, subscribedVideos, userContext.sessionInfo, 2, "session_detail_page_cta", bookingPref, classInviteLinkCreator, isLocked, interfaces, isUserEligibleForMonetisation, isUserEligibleForTrial, bucketId))).obj
    if (isNoSlotSelected) {
        pageAction = {
            title: "JOIN",
            actionType: "NAVIGATION",
            disabled: true
        }
    }
    if (pageAction) {
        actions.push(pageAction)
    }
    widgetPromise.push(getDescriptionWidget(classResponse, "About session"))
    if (classResponse.format === "EAT") {
        if (_.get(classResponse, "eatLiveMeta.chefDetail")) {
            widgetPromise.push(getFoodNoteWidget(classResponse.eatLiveMeta.chefDetail))
        }
        if (_.get(classResponse, "eatLiveMeta.ingredients") && !_.isEmpty(classResponse.eatLiveMeta.ingredients)) {
            widgetPromise.push(...getIngredientBulletInfoWidget(classResponse.eatLiveMeta.ingredients))
        }
        if (_.get(classResponse, "eatLiveMeta.recipeDetails")) {
            const relatedRecipesWidgetPromise = getRelatedRecipesWidget(interfaces, userContext, classResponse.eatLiveMeta.recipeDetails)
            if (relatedRecipesWidgetPromise)
                widgetPromise.push(relatedRecipesWidgetPromise)
        }
    }
    let widgets = await Promise.all(widgetPromise)
    widgets = widgets.filter(v => !_.isNil(v))
    return new LiveClassDetailViewV2(widgets, actions, true, bannerImages, false, classResponse.scheduledTimeEpoch, classResponse.duration, isLocked, classResponse.liveClassId)
}

function getManageOptionsWidgetV2(user: User, userContext: UserContext, classId: string, selectedClassResponse: DigitalCatalogueEntryV1, isSubscribed: boolean, isLocked: boolean, isNoSlotSelected?: boolean): ManageOptionsWidgetV2 {
    const actions: Action[] = []
    let moreAction: Action = undefined
    if (isSubscribed && selectedClassResponse) {
        actions.push(LiveUtil.getVideoUnsubscribeAction(user, selectedClassResponse, userContext.sessionInfo, "live_session_page_three_dot_menu"))
        moreAction = {
            actionType: "ACTION_LIST",
            actions
        }
    }
    let durationHourMin: HourMin = null
    let formattedTimeString: string = ""
    if (durationHourMin && selectedClassResponse) {
        durationHourMin = TimeUtil.convertDurationSecondsToHourMin(selectedClassResponse.duration / 1000)
        formattedTimeString = durationHourMin.min > 0 ? durationHourMin.min + " Min" : ""
        if (durationHourMin.hour > 0) {
            formattedTimeString = durationHourMin.hour + " Hr " + 2
        }
    }
    const isRetelecast = (selectedClassResponse && selectedClassResponse.isRetelecast) ? true : false
    const isPremiere = !isNoSlotSelected && !isRetelecast && classId
    let title = `LIVE | ${isPremiere ? "PREMIERE" : "AT HOME"} ${formattedTimeString ? "| " + formattedTimeString : ""}`
    title = AppUtil.isWeb(userContext) ? title + `| ${getTimeInHourMinString(TimeUtil.convertDurationSecondsToHourMin(selectedClassResponse.duration / 1000))}` : title
    return {
        widgetType: "MANAGE_OPTIONS_WIDGET_V2",
        title: title,
        moreAction,
        style: isPremiere ? { backgroundColor: "#3888ff" } : undefined,
        icon: isPremiere ? "/image/livefit/app/star_white.png" : undefined,
        isLocked
    }
}

export function getTimeInHourMinString(time: HourMin): string {
    const hour = time.hour
    const min = time.min
    let ans = ""
    ans = hour > 0 ? ans + `${hour} hr ` : ans
    ans = min > 0 ? ans + `${min} min` : ans

    return ans
}

async function getImageOverlayCardContainerWidgetV2(user: User, classId: string, classResponse: LiveClass, selectedClassResponse: DigitalCatalogueEntryV1, userContext: UserContext, isSubscribed: boolean, socialData: SocialDataResponse, tz: Timezone, isLocked: boolean, card?: any, isNoSlotSelected?: boolean): Promise<ImageOverlayCardContainerWidget> {
        const imageOverlayContainerWidget = new ImageOverlayCardContainerWidget()
        imageOverlayContainerWidget.assets.push({
            assetType: "IMAGE",
            assetUrl: classResponse.bannerImages.mobileImage
        })
        imageOverlayContainerWidget.widgets.push(getManageOptionsWidgetV2(user, userContext, classId, selectedClassResponse, isSubscribed, isLocked, isNoSlotSelected))
        const subTitle = selectedClassResponse ? TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, selectedClassResponse.scheduledTimeEpoch, "llll") : TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, classResponse.scheduledTimeEpoch, "ddd, MMM Do, YYYY")
        const headerWidget: HeaderWidget = {
            widgetType: "HEADER_WIDGET",
            widgetTitle: {
                title: classResponse.title,
                subTitle: subTitle
            },
            headerStyle: {
                marginLeft: 0,
                fontSize: 24,
                color: "#000000"
            },
            subTitleStyle: {
                marginLeft: 0
            }
        }
        imageOverlayContainerWidget.widgets.push(headerWidget)
        const slotsInfo = getTimeSlotsInfo(classResponse, tz, classId, isNoSlotSelected)

        if (AppUtil.isDateWiseLiveSlotWidgetSupported(userContext)) {
            const liveSlotSelectionWidget: LiveSlotSelectionWidget = {
                widgetType: "LIVE_SLOT_SELECTION_WIDGET",
                datesAvailable: getDatewiseSlotInfo(userContext, classResponse, tz, classId, isNoSlotSelected)
            }
            imageOverlayContainerWidget.widgets.push(liveSlotSelectionWidget)
        } else {
            const expandableSlotSelectionWidget: ExpandableSlotSelectionWidget = {
                widgetType: "EXPANDABLE_SLOT_SELECTION_WIDGET",
                title: slotsInfo.length > 0 ? "SLOTS AVAILABLE" : "NO SLOTS AVAILABLE",
                slotsInfo: slotsInfo
            }
            imageOverlayContainerWidget.widgets.push(expandableSlotSelectionWidget)
        }
        return imageOverlayContainerWidget
}

function getDatewiseSlotInfo(userContext: UserContext, classResponse: LiveClass, tz: Timezone, classId: string, isNoSlotSelected?: boolean): DateWiseLiveSlots[] {
    const dateWiseLiveSlots: DateWiseLiveSlots[] = []
    const dateWiseSlotMap: { [id: string]: SlotsInfo[] } = {}

    classResponse.slots.map(slot => {
        const isSelected = isNoSlotSelected ? false : slot.classId === classId
        const isScheduled = slot.subscriptionStatus === "SUBSCRIBED"
        const slotInfo: SlotsInfo = {
            time: TimeUtil.formatEpochInTimeZone(tz, slot.scheduledTimeEpoch, "hh:mm A"),
            classId: slot.classId,
            status: slot.status,
            subscriptionStatus: slot.subscriptionStatus,
            isSelected: isSelected,
            isScheduled: isScheduled,
            isLive: slot.isPremier ? false : slot.status === "LIVE",
            icon: slot.isPremier ? (isScheduled || isSelected) ? "/image/livefit/app/star_white.png" : "/image/livefit/app/star_blue.png" : undefined
        }
        const slotDate = TimeUtil.formatEpochInTimeZone(tz, slot.scheduledTimeEpoch)
        if (dateWiseSlotMap[slotDate] === undefined) {
            dateWiseSlotMap[slotDate] = []
        }
        dateWiseSlotMap[slotDate].push(slotInfo)
    })

    Object.keys(dateWiseSlotMap).forEach(date => {
        dateWiseLiveSlots.push({
            date,
            timeSlots: dateWiseSlotMap[date],
            noSlotsAvailable: dateWiseSlotMap[date].length === 0,
            noSlotText: "Sorry, no slots available today!",
        })
    })

    return dateWiseLiveSlots
}

function getTimeSlotsInfo(classResponse: LiveClass, tz: Timezone, classId: string, isNoSlotSelected?: boolean): SlotsInfo[] {
    return classResponse.slots.map(slot => {
        const isSelected = isNoSlotSelected ? false : slot.classId === classId
        const isScheduled = slot.subscriptionStatus === "SUBSCRIBED"
        return {
            time: TimeUtil.formatEpochInTimeZone(tz, slot.scheduledTimeEpoch, "hh:mm A"),
            classId: slot.classId,
            status: slot.status,
            subscriptionStatus: slot.subscriptionStatus,
            isSelected: isSelected,
            isScheduled: isScheduled,
            isLive: slot.isPremier ? false : slot.status === "LIVE",
            icon: slot.isPremier ? (isScheduled || isSelected) ? "/image/livefit/app/star_white.png" : "/image/livefit/app/star_blue.png" : undefined
        }
    })
}

export async function builVanillaLiveClassView(classResponse: DigitalCatalogueEntryV1, subscribedVideos: string[], preference: PreferenceDetail, userContext: UserContext, classId: string, classInviteLinkCreator: ClassInviteLinkCreator, interfaces: CFServiceInterfaces, isUserEligibleForLivePackMonetisation: boolean, isUserEligibleForLivePackTrial: boolean, bucketId: string): Promise<LiveClassDetailView> {
  const widgetPromise: Promise<(BaseWidget | WidgetView | PageWidget | TemplateWidget)>[] = []
  const actions: Action[] = []
  const user: User = await userContext.userPromise
  const bannerImages = classResponse.bannerImages
  const isSubscribed = _.includes(subscribedVideos, (<any>classResponse)._id)
  widgetPromise.push( getImageOverlayCardContainerWidget(user, classId, classResponse, userContext, isSubscribed, undefined))

  const bookingPref = (_.isEmpty(preference)) ? false : preference.bookingEmailPreference
  const pageAction = (await eternalPromise(LiveUtil.getStatusBasedSessionAction(user, userContext, classResponse, subscribedVideos, userContext.sessionInfo, 2, "session_detail_page_cta", bookingPref, classInviteLinkCreator, true, interfaces, isUserEligibleForLivePackMonetisation, isUserEligibleForLivePackTrial, bucketId))).obj
  if (pageAction) {
      actions.push(pageAction)
  }
  widgetPromise.push(getDescriptionWidget(classResponse, "About session"))
  if (_.get(classResponse, "eatLiveMeta.chefDetail")) {
    widgetPromise.push(getFoodNoteWidget(classResponse.eatLiveMeta.chefDetail))
    }

    if (_.get(classResponse, "eatLiveMeta.ingredients") && !_.isEmpty(classResponse.eatLiveMeta.ingredients)) {
        widgetPromise.push(...getIngredientBulletInfoWidget(classResponse.eatLiveMeta.ingredients))
    }

  if (_.get(classResponse, "eatLiveMeta.recipeDetails")) {
      const relatedRecipesWidgetPromise = getRelatedRecipesWidget(interfaces, userContext, classResponse.eatLiveMeta.recipeDetails)
      if (relatedRecipesWidgetPromise)
      widgetPromise.push(relatedRecipesWidgetPromise)
  }

  let widgets = await Promise.all(widgetPromise)
  widgets = widgets.filter(v => !_.isNil(v))

  return new LiveClassDetailView(widgets, actions, true, bannerImages, false, classResponse.scheduledTimeEpoch)
}

async function getFoodNoteWidget(chefDetail: ChefDetail): Promise<DescriptionWidget> {
  const infoCard: InfoCard = {
    title: "About the expert",
    subTitle: chefDetail.about
  }
  const descriptionWidget = new DescriptionWidget([infoCard])
  return descriptionWidget
}

async function getImageOverlayCardContainerWidget(user: User, classId: string, classResponse: DigitalCatalogueEntryV1, userContext: UserContext, isSubscribed: boolean, socialData: SocialDataResponse): Promise<ImageOverlayCardContainerWidget> {
    const imageOverlayContainerWidget = new ImageOverlayCardContainerWidget()
    if (_.get(classResponse, "bannerImages.mobileImage")) {
        imageOverlayContainerWidget.assets.push({
            assetType: "IMAGE",
            assetUrl: _.get(classResponse, "bannerImages.mobileImage")
        })
    }
  imageOverlayContainerWidget.widgets.push(getManageOptionsWidget(user, userContext, classId, classResponse, isSubscribed))
  const headerWidget: HeaderWidget = {
      widgetType: "HEADER_WIDGET",
      widgetTitle: {
          title: classResponse.title,
          subTitle: TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, classResponse.scheduledTimeEpoch, "llll")
      },
      headerStyle: {
          marginLeft: 0,
          fontSize: 24,
          color: "#000000"
      },
      subTitleStyle: {
            marginLeft: 0
        }
    }
    imageOverlayContainerWidget.widgets.push(headerWidget)
    return imageOverlayContainerWidget
}


async function getDescriptionWidget(classResponse: DigitalCatalogueEntryV1, title: string): Promise<DescriptionWidget> {
    const descriptionWidget: DescriptionWidget = {
        widgetType: "DESCRIPTION_WIDGET",
        showDivider: false,
        hasDividerBelow: false,
        descriptions: [{
            title: title,
            subTitle: classResponse.description
            // moreIndex: 168
        }],
        containerStyle: {
            paddingTop: 0,
        },
    }
    return descriptionWidget
}

function getManageOptionsWidget(user: User, userContext: UserContext, classId: string, classResponse: DigitalCatalogueEntryV1, isSubscribed: boolean): ManageOptionsWidgetV2 {
  const actions: Action[] = []
  let moreAction: Action = undefined
  if (isSubscribed) {
      actions.push(LiveUtil.getVideoUnsubscribeAction(user, classResponse, userContext.sessionInfo, "live_session_page_three_dot_menu"))
      moreAction = {
          actionType: "ACTION_LIST",
          actions
      }
  }
  const durationHourMin: HourMin = TimeUtil.convertDurationSecondsToHourMin(classResponse.duration / 1000)
  let formattedTimeString: string = durationHourMin.min > 0 ? durationHourMin.min + " Min" : ""
  if (durationHourMin.hour > 0) {
      formattedTimeString = durationHourMin.hour + " Hr " + formattedTimeString
  }
  return {
      widgetType: "MANAGE_OPTIONS_WIDGET_V2",
      title: `LIVE | AT HOME | ${formattedTimeString}`, // classResponse.isRetelecast ? `RETELECAST | AT HOME` : `LIVE | AT HOME`,
      moreAction
  }
}

function getRelatedRecipesWidget(interfaces: CFServiceInterfaces, userContext: UserContext, recipeResponse: RecipeDetail[]): Promise<IBaseWidget> {
    if (!_.isEmpty(recipeResponse)) {
        const recipeDetails: DIYRecipeView[] = []
        recipeResponse.forEach((prod: RecipeDetail) => {
            if (!_.isNil(prod.recipe)) {
                const recipeObj: IDIYRecipe = {
                    id: prod.recipeId,
                    ...prod.recipe
                }
                recipeDetails.push(recipeObj)
            }

        })
        const recipeParams: IRecipeParams = {
            recipe: recipeDetails,
            bookMarkedOnly: false,
            vegOnly: false,
            categoryId: undefined
        }
        const widgetPromise = new EatLiveRecipeWidgetBuilder().buildView(interfaces, userContext,
            {
                ... recipeParams,
                includeExploreAllRecipeCard: false
            }
        )
        return widgetPromise
    } else {
        return undefined
    }
}
