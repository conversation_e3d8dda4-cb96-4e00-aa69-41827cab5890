import { inject, injectable } from "inversify"
import { UserContext } from "@curefit/vm-models"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CacheHelper } from "../util/CacheHelper"
import { ISocialService, SOCIAL_CLIENT_TYPES } from "@curefit/social-client"
import {
  CommunitiesResponse,
  CommunityEntry,
  CommunityType,
  ProfileAttributes,
  Tenant,
  UserAttributeMappingEntry,
  UserProfileEntry
} from "@curefit/social-common"
import DigitalLeagueSectionViewBuilder, {
  DUMMY_USER_IMAGE,
  Fonts,
  LEAGUE_LIST_MEMBER_LIMIT,
  LeaguePageId,
  LeagueSectionId,
  LeagueSectionSource,
  PENDING_INVITES_LIMIT
} from "./DigitalLeagueSectionViewBuilder"
import {
  BadgeWithGradient,
  LeagueClassStreamSmallCellWidget,
  LeagueClassStreamWidget,
  LeagueLeaderBoardListWidget,
  LeagueLeaderBoardUser,
  LeagueLeaderBoardWidget,
  SquadFutureBookingWidget
} from "@curefit/apps-common/dist/src/widgets/social/interfaces"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService } from "@curefit/diy-client"
import * as _ from "lodash"
import CultUtil from "../util/CultUtil"
import { DigitalCatalogueEntryV1, SessionReportStatus, UserScoreMetricsResponse } from "@curefit/diy-common"
import { eternalPromise, TimeUtil, Timezone } from "@curefit/util-common"
import { Action } from "../common/views/WidgetView"
import AppUtil from "../util/AppUtil"
import LiveUtil, { LIVE_SESSION_ACTION_SOURCE, squadPageReportAllowedFormats } from "../util/LiveUtil"
import { BannerCarouselWidget } from "../page/PageWidgets"
import { ILoggingService, LOGGING_CLIENT_TYPES } from "@curefit/logging-client"
import DigitalReportViewBuilder from "./DigitalReportViewBuilder"
import { BASE_TYPES, Logger } from "@curefit/base"
import { SortOrder } from "@curefit/mongo-utils"
import { ChallengeCache, RIDDLER_CACHE_TYPES } from "@curefit/riddler-cache"
import DigitalLeagueChallengeViewBuilder, {
  LIVE_CHALLENGE_TAG,
  SQUAD_CHALLENGE_TAG,
  TEAM_CHALLENGE_TYPE,
  TEST_TAG
} from "./DigitalLeagueChallengeViewBuilder"
import {
  Challenge,
  ChallengeType,
  Enrolment,
  EnrolmentResponse,
  GroupEnrolment,
  GroupEnrolmentResponse,
  RankedChallenge
} from "@curefit/riddler-common"
import { IRiddlerService, RIDDLER_CLIENT_TYPES } from "@curefit/riddler-client"
import { IQuestService, QUEST_CLIENT_TYPES } from "@curefit/quest-client"
import { ProductType, UrlPathBuilder } from "@curefit/product-common"
import { IRewardService, REWARD_CLIENT_TYPES } from "@curefit/reward-client"
import { PageTypes, SquadChallengeBadgeWidget, User } from "@curefit/apps-common"
import { Badge, GroupActivitySummary } from "@curefit/quest-common"
import { ICrudKeyValue, REDIS_TYPES } from "@curefit/redis-utils"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { CULT_CLIENT_TYPES } from "@curefit/cult-client"
import { ICultServiceOld as ICultService, SquadClassBooking } from "@curefit/cult-client/dist/src/ICultServiceOld"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import moment = require("moment")

const momentTz = require("moment-timezone")

type CommunityIdVsChallenge = {[id: string]: Challenge}

const LEAGUE_LEADERBOARD_COMMUNITIES_LIMIT = 10
export const LEAGUE_LEADERBOARD_ROW_ELEMENTS_LIMIT = 5

const ALL_USERS_ACROSS_COMMUNITIES_LIMIT = 50
const CLASS_WALL_TOTAL_REPORTS_LIMIT = 20
const CLASS_WALL_TOTAL_REPORTS_DAYS_LIMIT = 3
export enum LEAGUE_LEADERBOARD_SOURCE {
  WALL = "WALL",
  JOIN_CHALLENGE_MODAL = "JOIN_CHALLENGE_MODAL",
  SQUAD_CHALLENGE_PAGE = "SQUAD_CHALLENGE_PAGE",
  SQUAD_CHALLENGE_BADGE_WIDGET = "SQUAD_CHALLENGE_BADGE_WIDGET",
  CLP = "CLP"
}
export enum SQUAD_CHALLENGES_SOURCE {
  WALL_CHALLENGES_CAROUSEL = "WALL_CHALLENGES_CAOROUSEL"
}

export enum JOIN_CHALLENGE_ACTION_SOURCE {
  SQUAD_CHALLENGE_PAGE = "SQUAD_CHALLENGE_PAGE",
}


const TARGET_METRIC_NAME = "CHALLENGE_MILESTONE_VALUE"
export const CHALLENGE_DESCRIPTION_NAME = "CHALLENGE_LEADERBOARD_DESCRIPTION"
const SQUAD_CHALLENGE_WALL_BANNER_IMAGE_NAME = "SQUAD_CHALLENGE_WALL_BANNER_IMAGE"
const SQUAD_MEMBER_SCORE_TYPE = "SQUAD_MEMBER_SCORE_TYPE"

enum CHALLENGE_PROGRESS_STATUS {
  ONGOING = "ONGOING",
  FAILED = "FAILED",
  COMPLETED = "COMPLETED",
  UPCOMING = "UPCOMING"
}

const Colors = {
  green: "#008300",
  yellow: "#ffb61d",
  red: "#d61212"
}

const USER_PROGRESS_STATUS_COLORS = {
  [CHALLENGE_PROGRESS_STATUS.COMPLETED]: Colors.green,
  [CHALLENGE_PROGRESS_STATUS.ONGOING]: Colors.yellow,
  [CHALLENGE_PROGRESS_STATUS.FAILED]: Colors.red
}

enum USER_PROGRESS_STATUS {
  COMPLETED = "COMPLETED",
  ONGOING = "ONGOING",
  FAILED = "FAILED",
}

const CHALLENGE_LEADERBOARD_STATUS_TEXT = {
  [CHALLENGE_PROGRESS_STATUS.COMPLETED]: {
    title: "CHALLENGE COMPLETED",
    backgroundColor: Colors.green
  },
  [CHALLENGE_PROGRESS_STATUS.FAILED]: {
    title: "CHALLENGE: LOST",
    backgroundColor: Colors.red
  },
  [CHALLENGE_PROGRESS_STATUS.ONGOING]: {
    title: "CHALLENGE MODE: ON",
    backgroundColor: Colors.yellow
  },
  [CHALLENGE_PROGRESS_STATUS.UPCOMING]: {
    title: "CHALLENGE: UPCOMING",
    backgroundColor: Colors.yellow
  }
}

export enum CHALLENGE_STATUS {
  UPCOMING = "UPCOMING",
  ONGOING = "ONGOING",
  ENDED = "ENDED"
}

export const CHALLENGE_STATUS_TEXT = {
  [CHALLENGE_STATUS.UPCOMING]: {
    title: "UPCOMING",
    backgroundColor: Colors.yellow
  },
  [CHALLENGE_STATUS.ONGOING]: {
    title: "ONGOING",
    backgroundColor: Colors.yellow
  },
  [CHALLENGE_STATUS.ENDED]: {
    title: "ENDED",
    backgroundColor: Colors.red
  }
}

interface UserSquadDetails {hasOwnCommunity: boolean, numOtherCommunitiesJoined: number, usersInOwnCommunity: number}
export interface ExtraData {communityIdVsUserList?: CommunityIdVsUserList, communitiesResponse?: CommunitiesResponse,
  communityIdVsSelectedChallengeEnrolment?: CommunityIdVsEnrolmentResponse, currentUserSquadLeaderboard?: LeagueLeaderBoardListWidget["data"][0], challengeIdVsEnrolmentResponses?: ChallengeIdVsEnrolmentResponse}


interface GetLeaderboardParams {
  userId: string,
  community?: CommunityEntry,
  source: LEAGUE_LEADERBOARD_SOURCE,
  extraData?: ExtraData,
  noUserAction?: boolean,
  challenge?: Challenge,
  recentCommunityEnrolment?: EnrolmentResponseObj
  adhocData?: {
    userIds?: string[],
    title?: string
  }
}

type UnPromisify<T> = T extends Promise<infer U> ? U : T

type ProfileAttributesPromise = Promise<{obj: UnPromisify<ReturnType<ISocialService["getProfileAttribute"]>>, err?: string}>

const behindTodayDiffToWord: {[key: string]: string} = {"0": "TODAY", "1": "YESTERDAY", "2": "DAY BEFORE YESTERDAY", "-1": "TOMORROW"}

const BOLD_FONT_STYLE = {fontFamily: Fonts.Bold}

const EXPIRY_TWO_DAYS = 2 * 24 * 60 * 60

interface ChallengeScore {
  score?: number
  rank: number
}

type TimeStampedWidget<W>  = W & {
  maxTime: number
}

type GetEnrolmentToShowEnrolmentResponses =  Array<{enrolment: EnrolmentResponse, groupEnrolment?: GroupEnrolment} | EnrolmentResponseObj>

interface EnrolmentResponseObj { enrolment: EnrolmentResponse, groupEnrolment: GroupEnrolment }

type LeaderboardSortFields = {rank?: number, challengeDetails?: {type: ChallengeType, isActive?: boolean}}
type LeaderboardWidgetWithSortFields = LeagueLeaderBoardListWidget["data"][0] & LeaderboardSortFields

type TSSquadChallengeBadgeWidget = TimeStampedWidget<SquadChallengeBadgeWidget>
type TSSquadFutureBookingWidget = TimeStampedWidget<SquadFutureBookingWidget>

export interface IdVsValueMap<P> {[id: string]: P}
export type CommunityIdVsEnrollmentFlag = IdVsValueMap<boolean>
type CommunityIdVsEnrolmentResponse = IdVsValueMap<EnrolmentResponseObj[]>
type ChallengeIdVsEnrolmentResponse = IdVsValueMap<EnrolmentResponse[]>
type ContentIdVsUserScoreMetrics = IdVsValueMap<UserScoreMetricsResponse[]>
type ContentIdVsCatalogue = IdVsValueMap<DigitalCatalogueEntryV1>
type CommunityIdVsUserList = IdVsValueMap<LeagueLeaderBoardUser[]>
export type ChallengeIdVsChallenge = IdVsValueMap<Challenge>
type UserIdVsChallengeScore = IdVsValueMap<ChallengeScore>

const NO_RANK_METRIC_OBJ: {[key: string]: undefined} = {
  rank: undefined,
  borderColor: undefined,
  checkColor: undefined,
  metric: undefined,
  borderWidth: undefined,
  padding: undefined
}

const MAX_DIFF_DAYS_FOR_SHOWING_CHALLENGE = 7


@injectable()
class DigitalSocialLeagueTabPageViewBuilder {
  constructor(
    @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
    @inject(SOCIAL_CLIENT_TYPES.SocialService) private socialService: ISocialService,
    @inject(CUREFIT_API_TYPES.DigitalLeagueSectionViewBuilder) private digitalLeagueSectionViewBuilder: DigitalLeagueSectionViewBuilder,
    @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) private diyFulfilmentService: IDIYFulfilmentService,
    @inject(LOGGING_CLIENT_TYPES.LoggingService) public loggingService: ILoggingService,
    @inject(BASE_TYPES.ILogger) private logger: Logger,
    @inject(RIDDLER_CACHE_TYPES.ChallengeCache) private challengeCache: ChallengeCache,
    @inject(RIDDLER_CLIENT_TYPES.RiddlerService) private riddlerService: IRiddlerService,
    @inject(QUEST_CLIENT_TYPES.IQuestService) private questService: IQuestService,
    @inject(REWARD_CLIENT_TYPES.IRewardService) private rewardService: IRewardService,
    @inject(REDIS_TYPES.RedisDao) private redisDao: ICrudKeyValue,
    @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
    @inject(CULT_CLIENT_TYPES.CultFitService) public cultFitService: ICultService,
    @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService
  ) {}

  async buildView(userContext: UserContext, id: LeaguePageId, productType: ProductType): Promise<any> {
    const userId = userContext.userProfile.userId
    // creating redis entry for notification bell icon on homepage.
    const key = `cfapp:homeSquadNotification:${userId}`
    this.redisDao.createWithExpiry(key, "shown", EXPIRY_TWO_DAYS)

    if (id === LeaguePageId.ALL_LEAGUES) {
      return {
        sectionListWidget: {
          sections: [],
          refreshEnabled: true,
          paginationEnabled: false,
          source: LeagueSectionSource.WALL_PAGE,
          callParams: [
            {
              sectionId: LeagueSectionId.LEAGUES,
              offset: 0,
              limit: LEAGUE_LIST_MEMBER_LIMIT,
              source: LeagueSectionSource.WALL_PAGE,
              productType: productType
            },
            {
              sectionId: LeagueSectionId.RECEIVED_INVITES,
              offset: 0,
              limit: PENDING_INVITES_LIMIT,
              source: LeagueSectionSource.WALL_PAGE,
              productType: productType
            }
          ]
        }
      }
    } else if (id === LeaguePageId.WALL) {
      // not live
      const productTypeIsFitness = productType === "FITNESS"
      const isSquadChallengesSupported = AppUtil.isSquadChallengesSupported(productType)
      const isLeagueLeaderboardSupported = AppUtil.isLeagueLeaderboardSupported(productType)
      const extraData: ExtraData = {}
      const cultFutureClassesWidgetPromise = productTypeIsFitness ? this.getCultPeersFutureClasses(userContext) : Promise.resolve(undefined)
      const liveFutureClassesWidgetPromise = !productTypeIsFitness ? this.getLivePeersFutureClasses(userContext) : Promise.resolve(undefined)
      const leaderboardPromise = isLeagueLeaderboardSupported ? this.getLeagueLeaderBoardListWidget({userContext, source: LEAGUE_LEADERBOARD_SOURCE.WALL, extraData, productType}) : Promise.resolve(undefined)
      const reportsPromise = !productTypeIsFitness ? this.getReports(userContext) : Promise.resolve(undefined)
      const [{obj: leagueLeaderBoard, err: err1}, {obj: userSquadDetails}, {obj: cultPeerFutureClassesData, err: err2}, {obj: livePeerFutureClassesData, err: err3}, {obj: reportsData, err: err5}, shouldShowChallengesCarousel] = await Promise.all([
        eternalPromise(leaderboardPromise, "Leaderboard"),
        eternalPromise(this.userSquadDetails(userId), "SquadDetails"),
        eternalPromise (cultFutureClassesWidgetPromise, "CultPeerFutureClasses"),
        eternalPromise(liveFutureClassesWidgetPromise, "LivePeerFutureClasses"),
        eternalPromise(reportsPromise, "Reports"),
        AppUtil.shouldShowChallengesCarousel(userContext)
      ])
      const {obj: squadsBadges, err: err4} = await eternalPromise(this.getSquadChallengeBadgesList(userContext, extraData.communityIdVsUserList))

      const errors = [err1, err2, err3, err4, err5]
      errors.forEach((err) => {
        if (err) {
          this.logger.info(err)
        }
      })

      const sectionsWidget = this.createSections(_.compact(_.flatten([cultPeerFutureClassesData, livePeerFutureClassesData, reportsData, squadsBadges])), userContext)

      const userIsActiveInSquads = userSquadDetails.numOtherCommunitiesJoined > 0 || userSquadDetails.usersInOwnCommunity > 1
      const challengesCarousel = await this.getChallengesCarousel(userContext, extraData, productType)

      const footerWidgets = []
      const headerWidgets = []
      const sections = sectionsWidget?.sections || []

      if (!_.isEmpty(leagueLeaderBoard?.data)) {
        headerWidgets.push(leagueLeaderBoard)
      }

      if (userIsActiveInSquads) {
        if (!_.isEmpty(extraData?.currentUserSquadLeaderboard?.userList)) {
          footerWidgets.push(this.getLeagueClassStreamCell(extraData.currentUserSquadLeaderboard.userList, userContext))
        }

        if (_.isEmpty(sections)) {
          headerWidgets.push(this.getBookClassBanner(productType))
        }
      } else if (isSquadChallengesSupported) {// intro data as a widget is supported for these app version
        headerWidgets.unshift(this.getLeagueIntroData())
      }

      if (shouldShowChallengesCarousel && challengesCarousel) {
        headerWidgets.push(challengesCarousel)
      }

      // headerWidgets will never be empty if isSquasChallengesSupported is true
      if (!_.isEmpty(sections) || !_.isEmpty(headerWidgets) || !_.isEmpty(footerWidgets)) {
        return {
          leagueClassStreamWidget: {
            sections,
            footerWidgets,
            headerWidgets
          }
        }
      } else {
        return {
          introData: this.getLeagueIntroData()
        }
      }
    }
  }

  /* productType: not needed for source == SQUAD_CHALLENGE_PAGE */
  async getLeagueLeaderBoardListWidget({userContext, source, selectedChallengeId, noHeader, extraData, productType}:
                                         {userContext: UserContext,  source: LEAGUE_LEADERBOARD_SOURCE, selectedChallengeId?: string, noHeader?: boolean, extraData?: ExtraData, productType?: ProductType}): Promise<LeagueLeaderBoardListWidget> {
    const userId = userContext.userProfile.userId

    const isSquadChallengesSupported = AppUtil.isSquadChallengesSupported(productType)

    /*
    * All the challenges will have the squadTag
    * if this is empty there will definitely be no enrolments
    * */
    const squadChallengesNeeded: Challenge[] = this.getChallengesNeeded(userContext, source, selectedChallengeId, productType)

    const challengeIdVsChallenge = _.mapKeys(squadChallengesNeeded, (challenge) => challenge.challengeId)

    /* we get enrolments for the challenges that we selected */
    const [communitiesResponse, challengesVsEnrolments] = await Promise.all([
        this.socialService.getUserCommunities(userId, CommunityType.LEAGUE, false, LEAGUE_LEADERBOARD_COMMUNITIES_LIMIT, 0),
      Promise.all(squadChallengesNeeded.map((challenge) => this.riddlerService.getEnrolmentsForChallenge({userId, challengeId: challenge.challengeId, withStandings: true, withGroupEnrolments: true, entries: 10, withGroup: true})))
    ])

    /* communityIdVsSelectedChallengeEnrolments: Will contain only enrolments with GroupEnrolments for that community
    * challengeIdVsEnrolmentResponses: Will contain any enrolment for that challenge(group or not)
    *  */
    const {communityIdVsSelectedChallengeEnrolments, challengeIdVsEnrolmentResponses} = this.getChallengeAndCommunityVsEnrolments(challengesVsEnrolments)

    if (extraData) {
      extraData.communitiesResponse = communitiesResponse
      extraData.communityIdVsSelectedChallengeEnrolment = communityIdVsSelectedChallengeEnrolments
      extraData.challengeIdVsEnrolmentResponses = challengeIdVsEnrolmentResponses
    }


    const communities = this.getCommunitiesToShow(communitiesResponse, source, challengeIdVsEnrolmentResponses, communityIdVsSelectedChallengeEnrolments, selectedChallengeId)

    const data: Omit<LeagueLeaderBoardWidget, "widgetType">[] = (await Promise.all(communities.map(async (community) => {
      /* If we had a GroupEnrolment for this community to one of the challenges we selected in squadChallengesNeeded or
      * we get the last GroupEnrolment for this community (which was to a challenge no in the squadChallengesNeeded, we need this because we are only getting Enrolments/GroupEnrolments for challenges which are UPCOMING, ACTIVE or ended within MAX_DIFF_DAYS_FOR_SHOWING_CHALLENGE
      *  */
      let recentCommunityEnrolment: EnrolmentResponseObj
      let challenge: Challenge
      if (isSquadChallengesSupported) {
        recentCommunityEnrolment = this.getEnrolmentToShow(communityIdVsSelectedChallengeEnrolments[community.id], source, challengeIdVsChallenge, selectedChallengeId) as EnrolmentResponseObj || await this.getGroupEnrolmentForCommunity(userId, String(community.id))
        challenge = recentCommunityEnrolment ? challengeIdVsChallenge[recentCommunityEnrolment.enrolment?.challengeId] || recentCommunityEnrolment.enrolment.challenge : undefined
      }
      return this.createLeaderboardUsingCommunityAndEnrolment(community, userId, source, recentCommunityEnrolment, challenge, extraData)
    })))
      .filter((item) => item)

    /* sort with largest leaderboard first */
    data.sort(DigitalSocialLeagueTabPageViewBuilder.sortLeaderboard)


    return !_.isEmpty(data) ? {
      widgetType: "LEAGUE_LEADER_BOARD_LIST_WIDGET",
      header: !noHeader ? {
        title: "Leaderboard",
        seemore: {
          actionType: "SHOW_ALERT_MODAL",
          meta: {
            title: "About Leaderboard",
            subTitle: "Ranks are shown based on the number of classes attended in a week i.e Mon-Sun. This leaderboard refreshes every Monday. \nIn case your Squad joins a challenge, scores are calculated based on the goals of a challenge.",
            actions: [
              {
                actionType: "HIDE_ALERT_MODAL",
                title: "CLOSE",
              },
            ],
          },
        },
      } : undefined,
      data
    } : undefined
  }

  async createLeaderboardUsingCommunityAndEnrolment(community: CommunityEntry, userId: string, source: LEAGUE_LEADERBOARD_SOURCE, recentCommunityEnrolment?: EnrolmentResponseObj, challenge?: Challenge, extraData?: ExtraData, noUserAction?: boolean): Promise<LeaderboardWidgetWithSortFields> {
    const creator = await this.userCache.getUser(community.creatorNode.entityId)
    const isCurrentUserCreatedLeague = userId === community.creatorNode.entityId
    const communityId = community.id
    const title = (isCurrentUserCreatedLeague ? "Your" : `${creator.firstName}'s`) + " Squad"

    /* needed so that types are inferred automatically for Promise.all */
    const profileAttributesPromise: ProfileAttributesPromise = this.hasNearbyStandings(recentCommunityEnrolment?.enrolment) ?
        eternalPromise(this.socialService.getProfileAttribute(communityId, userId, ProfileAttributes.WEEKLY_CLASS_COUNT, true, LEAGUE_LEADERBOARD_COMMUNITIES_LIMIT, 0)) :
        eternalPromise(Promise.resolve(undefined))

    const [communityMembers, {obj: profileAttributes}] = await Promise.all([
      this.socialService.getCommunityMembers(communityId, userId, LEAGUE_LIST_MEMBER_LIMIT, 0),
      profileAttributesPromise])

    const communityMemberUserIds = communityMembers.map(attr => attr.userId)
    const atleastOneMemberHasAttendedClass = profileAttributes?.some(attr => {
      return attr.attributeValue && Number(attr.attributeValue) > 0
    })

    const userIdsVsScore = this.getUserIdsVsScore(communityId, userId, recentCommunityEnrolment, profileAttributes)
    communityMemberUserIds.sort(this.sortCommunityMemberByScoreFunc(userIdsVsScore))

    const {users, userIdVsProfile} = await this.getUserData(communityMemberUserIds)

    const userList = this.createLeaderboardUserList(communityMemberUserIds, users, source, noUserAction, atleastOneMemberHasAttendedClass, userIdVsProfile, challenge, recentCommunityEnrolment, userIdsVsScore)

    const badgeList = this.shouldShowLeaderboardBadgeList(source) ? (await eternalPromise(this.digitalLeagueSectionViewBuilder.getBadgeDescriptionList(userId, communityId))).obj?.badgeList as BadgeWithGradient[] : undefined

    const {rank, subTitle, tag} = this.getLeaderBoardPropsForEnrolment(recentCommunityEnrolment, source, challenge)

    const item: LeaderboardWidgetWithSortFields = {
      title,
      userList,
      tag,
      badgeList,
      subTitle,
      rank,
      challengeDetails: {
        isActive: recentCommunityEnrolment?.enrolment?.status === "ACTIVE",
        type: challenge?.challengeType
      }
    }

    if (isCurrentUserCreatedLeague && extraData) {
      extraData.currentUserSquadLeaderboard = item
    }

    return item
  }


  async createLeaderboardForAdhocData(adhocData: { userIds: string[], title?: string }, source: LEAGUE_LEADERBOARD_SOURCE, noUserAction?: boolean): Promise<LeaderboardWidgetWithSortFields> {
    const title = adhocData.title || "Squad"
    const {users, userIdVsProfile} = await this.getUserData(adhocData.userIds)
    const userList = this.createLeaderboardUserList(adhocData.userIds, users, source, noUserAction, false, userIdVsProfile)
    return {
      title,
      userList,
    }
  }

  createLeaderboardUserList(userIds: string[], users: IdVsValueMap<User>, source: LEAGUE_LEADERBOARD_SOURCE, noUserAction?: boolean, atleastOneMemberHasAttendedClass?: boolean, userIdVsProfile?: IdVsValueMap<UserProfileEntry>, challenge?: Challenge, recentCommunityEnrolment?: EnrolmentResponseObj, userIdsVsScore?: UserIdVsChallengeScore) {
    return _.compact(userIds.map((id, index: number) => {
      const user = users[id]
      const userStanding = userIdsVsScore?.[id]

      /* Only when the squad is/was enrolled in a challenge */
      let challengeProgressStatus
      if ((source === LEAGUE_LEADERBOARD_SOURCE.WALL || source === LEAGUE_LEADERBOARD_SOURCE.SQUAD_CHALLENGE_PAGE || source === LEAGUE_LEADERBOARD_SOURCE.CLP) && recentCommunityEnrolment && challenge) {
        challengeProgressStatus = this.getChallengeProgressStatusViewProps(recentCommunityEnrolment, userStanding, challenge)
      }

      let rank
      if (recentCommunityEnrolment || source === LEAGUE_LEADERBOARD_SOURCE.SQUAD_CHALLENGE_BADGE_WIDGET) {
        rank = undefined
      } else if (atleastOneMemberHasAttendedClass) {
        rank = `#${index + 1}`
      } else if (source === LEAGUE_LEADERBOARD_SOURCE.WALL) {
        rank = "--"
      }

      return {
        imageUrl: user.profilePictureUrl || DUMMY_USER_IMAGE,
        name: user.firstName,
        rank,
        action: !noUserAction && userIdVsProfile?.[id] ? CultUtil.getProfileClickAction(true, true, 0, "", {
          byUserIds: true,
          targetUserIds: [id]
        }) : undefined,
        ...challengeProgressStatus
      }
    }))
  }

  async getLivePeersFutureClasses(userContext: UserContext): Promise<Array<TSSquadFutureBookingWidget>> {
    const userId = userContext.userProfile.userId
    const subscriptionEntriesForValidClasses = await this.diyFulfilmentService.getUserSubscriptionsForActiveClasses(userId)
    const contentIdGroups: { [contentId: string]: { userIds: string[], maxTime: number } } = {}


    subscriptionEntriesForValidClasses.forEach((subscription) => {
      const contentId = subscription.catalogueEntryId

      const updatedDateEpoch = this.getUTCMillsFromISO((<any>subscription).updatedDate)

      if (contentIdGroups[contentId]) {
        const originalGroup = contentIdGroups[contentId]
        const newMaxTime = Math.max(updatedDateEpoch, originalGroup.maxTime)
        const originalUserIds = [...originalGroup.userIds, subscription.userId]
        contentIdGroups[contentId] = {userIds: originalUserIds, maxTime: newMaxTime}
      } else {
        const newGroup = [subscription.userId]
        const maxTime = updatedDateEpoch
        contentIdGroups[contentId] = {userIds: newGroup, maxTime}
      }
    })


    const sortedContentIds = _.keys(contentIdGroups).sort((id1, id2) => {
      return contentIdGroups[id1].maxTime > contentIdGroups[id2].maxTime ? -1 : 1
    })


    const contentIdData = await Promise.all( sortedContentIds.map(id => this.diyFulfilmentService.getDigitalCatalogueEntry(id)))

    const idVsCatalogue = contentIdData.reduce((acc, catalogueEntry) => {
      acc[(<any>catalogueEntry)._id] = catalogueEntry
      return acc
    }, {} as ContentIdVsCatalogue)

    return await Promise.all(_.keys(contentIdGroups).map(async (sectionedContentId) => {
      const catalogueEntry = idVsCatalogue[sectionedContentId]
      const userForSection = contentIdGroups[sectionedContentId].userIds
      const maxTime = contentIdGroups[sectionedContentId].maxTime
      const latestUserForSection = await this.userCache.getUser(userForSection[0])
      const description = this.getClassStreamSectionFooterText(userForSection.length, latestUserForSection.firstName, catalogueEntry.title, catalogueEntry.scheduledTimeEpoch, userContext)
      const bottomAction = await this.getClassAction("league_wall", userContext, catalogueEntry)
      return {
        classImage: catalogueEntry?.bannerImages?.mobileImage || catalogueEntry.sessionStartImages?.mobileImage,
        profileImage: latestUserForSection.profilePictureUrl,
        description,
        bottomAction,
        useGAuto: true,
        widgetType: "SQUAD_FUTURE_BOOKING_WIDGET",
        maxTime
      } as TSSquadFutureBookingWidget
    }))
  }

  async getCultPeersFutureClasses(userContext: UserContext): Promise<Array<TSSquadFutureBookingWidget>> {
    const userId = userContext.userProfile.userId
    const cultPeersFutureClasses = await this.cultFitService.getUpcomingClassesForSquad(userId, "CUREFIT_API")
    const classIdGroups: { [classId: string]: { userIds: string[], maxTime: number } } = {}


    cultPeersFutureClasses.forEach((booking) => {
      const classId = booking.class.id.toString()
      const bookingUserId = booking.userId.toString()

      const classDate = TimeUtil.parseDateAndTime(booking.class.date, booking.class.startTime, userContext.userProfile.timezone)
      const updatedDateEpoch = classDate.getTime()

      if (classIdGroups[classId]) {
        const originalGroup = classIdGroups[classId]
        const newMaxTime = Math.max(updatedDateEpoch, originalGroup.maxTime)
        const originalUserIds = [...originalGroup.userIds, bookingUserId]
        classIdGroups[classId] = {userIds: originalUserIds, maxTime: newMaxTime}
      } else {
        const newGroup = [bookingUserId]
        const maxTime = updatedDateEpoch
        classIdGroups[classId] = {userIds: newGroup, maxTime}
      }
    })

    return await Promise.all(_.keys(classIdGroups).map(async (sectionedClassId) => {
      const bookingEntry = _.find(cultPeersFutureClasses, booking => booking.class.id.toString() === sectionedClassId)
      const workoutDocument = _.find(bookingEntry.workout.documents, { tagName: "PRODUCT_BNR" })
      const classImage = workoutDocument.URL
      const classDate = TimeUtil.parseDateAndTime(bookingEntry.class.date, bookingEntry.class.startTime, userContext.userProfile.timezone)
      const classTimeEpoch = classDate.getTime()
      const userForSection = classIdGroups[sectionedClassId].userIds
      const maxTime = classIdGroups[sectionedClassId].maxTime
      const latestUserForSection = await this.userCache.getUser(userForSection[0])
      const description = this.getClassStreamSectionFooterText(userForSection.length, latestUserForSection.firstName, bookingEntry.workout.name, classTimeEpoch, userContext, bookingEntry.center.name)
      const bottomAction = this.getCultClassAction(userContext, bookingEntry, classTimeEpoch)
      return {
        classImage,
        profileImage: latestUserForSection.profilePictureUrl,
        description,
        bottomAction,
        useGAuto: true,
        widgetType: "SQUAD_FUTURE_BOOKING_WIDGET",
        maxTime
      } as TSSquadFutureBookingWidget
    }))
  }

  getCultClassAction(userContext: UserContext, bookingEntry: SquadClassBooking, classTimeEpoch: number): Action {
    let action: Action
    const nowEpoch = TimeUtil.getCurrentEpoch()
    let title
    if (classTimeEpoch < nowEpoch) {
      return
    } else {
      title = "JOIN"
    }
    const url = `curefit://classbookingv2?classId=${bookingEntry.class.id.toString()}&centerId=${bookingEntry.center.id.toString()}`
    action = {
      actionType: "NAVIGATION",
      url,
      title: _.isEmpty(title) ? "KNOW MORE" : title
    }
    return action
  }

  async getChallengesCarousel(userContext: UserContext, extraData: ExtraData, productType: ProductType) {
    const squadChallenges = this.getChallengesNeeded(userContext, SQUAD_CHALLENGES_SOURCE.WALL_CHALLENGES_CAROUSEL, productType)
    if (_.isEmpty(squadChallenges)) {
      return undefined
    }

    const carouselItems = await Promise.all(squadChallenges.map(async challenge => {
      let tagText
      const challengeStatus = DigitalLeagueChallengeViewBuilder.getChallengeStatusAccordingToTimezone(userContext.userProfile.timezone, challenge)
      if (challengeStatus === CHALLENGE_STATUS.ONGOING || challengeStatus === CHALLENGE_STATUS.UPCOMING) {
        const hasEnrolmentWithStatus: IdVsValueMap<boolean> = {}
        extraData?.challengeIdVsEnrolmentResponses?.[challenge.challengeId]?.forEach((enrolment) => {
          if (enrolment.status) {
            hasEnrolmentWithStatus[enrolment.status] = true
          }
        })

        const {canShowJoinButton} = await DigitalLeagueChallengeViewBuilder.canShowChallengeJoinButton(extraData, userContext, this.socialService, challenge)

        /* Need atLeast one free community to join a challenge, there should be no enrolment for this challenge */
        if ((canShowJoinButton && !(hasEnrolmentWithStatus["ACTIVE"] || hasEnrolmentWithStatus["UPCOMING"] || hasEnrolmentWithStatus["EXPIRED"]))) {
          tagText = "JOIN"
        } else if (hasEnrolmentWithStatus["ACTIVE"] || challengeStatus === CHALLENGE_STATUS.ONGOING) {
          tagText = "LIVE"
        } else if (hasEnrolmentWithStatus["UPCOMING"] || challengeStatus === CHALLENGE_STATUS.UPCOMING) {
          tagText = "UPCOMING"
        }
      } else {
        tagText = "ENDED"
      }
      return {
        id: challenge.challengeId,
        image: challenge.constants.find(constant => constant.name === SQUAD_CHALLENGE_WALL_BANNER_IMAGE_NAME)?.value || challenge.uiConfig?.bannerUrl,
        action: this.getSquadChallengePageNavigationAction(challenge.challengeId, PageTypes.LeagueWallTabbedPage),
        tagText
      }
    }))

    if (await LiveUtil.isContactsSyncSupportedAndSquadExists(userContext, this.socialService, undefined, extraData.communitiesResponse)) {
      carouselItems.unshift({
        id: "contacts_sync_banner",
        image: "image/diy/Leagues/ContactsSyncWallBannerRevised.png",
        action: {
          actionType: "NAVIGATION",
          url: "curefit://leagueinvitepage?title=Invite"
        },
        tagText: undefined
      })
    }

    return {
      ...new BannerCarouselWidget("375:115", carouselItems,
        {
          showPagination: false,
          v2: true,
          alignment: "center",
          backgroundColor: "",
          autoScroll: false,
          enableSnap: false,
          useShadow: false,
          roundedCorners: true,
          noVerticalPadding: true,
          edgeToEdge: false,
          interContentSpacing: 20,
          bannerOriginalWidth: 620, // todo update with latest image dimensions
          bannerOriginalHeight: 240,
          bannerWidth: 310,
          bannerHeight: 120,
          noAutoPlay: true,
          containerStyle: {
            marginVertical: 15,
          },
          tagStyle: {
            placement: {
              position: "BOTTOM_LEFT",
              offset: 10
            },
            text: {
              bgColor: "#ffffff",
              fontColor: "#ff3278",
              fontSize: 12,
              borderRadius: 11,
              paddingHorizontal: 10
            }
          }
        }, carouselItems.length, false, false, false, {
          title: "New in Squads", titleProps: {
            style: {
              fontSize: 18,
              paddingHorizontal: 5
            }
          }
        }),
      contentMetric: {bannerId: "challenge_info_banner"}
    }
  }


  async getReports(userContext: UserContext) {
    const currentUserId = userContext.userProfile.userId


    const allUserIds = await this.socialService.getAllUsersAcrossCommunities(currentUserId, 0, ALL_USERS_ACROSS_COMMUNITIES_LIMIT)
    allUserIds.push(currentUserId)


    const [loggedActivities, userIdVsUserData] = await Promise.all([
      this.loggingService.getActivitiesFor({
      userId: allUserIds,
      show: [true],
      activityType: ["LIVE_SESSION"],
      sortFields: [{field: "date", order: SortOrder.DESC}],
      start: 0,
      count: CLASS_WALL_TOTAL_REPORTS_LIMIT
    }), this.userCache.getUsers(allUserIds)])


    const sortedFilteredContentIds = loggedActivities.activities.map(activity => activity.meta.contentId)


    const contentIdVsUserIds = loggedActivities.activities.reduce((acc, activity) => {
      const contentIdForActivity = activity.meta.contentId
      if (acc[contentIdForActivity]) {
        acc[contentIdForActivity].push(activity.userId)
      } else {
        acc[contentIdForActivity] = [activity.userId]
      }
      return acc
    }, {} as { [contentId: string]: string[] })


    const allowedFormatsSet = squadPageReportAllowedFormats.reduce((acc, format) => {
      acc[format] = true
      return acc
    }, {} as {[format: string]: true})


    const filteredContentIdVsCatalogue = (await Promise.all(sortedFilteredContentIds
      .map(async id => {
        try {
          return await this.diyFulfilmentService.getDigitalCatalogueEntry(id)
        }
        catch (e) {
          this.logger.info(`Error in getReports: ${e.message}. ${e.stack}`)
          return undefined
        }
      })))
      .filter(item => item)
      .reduce((acc, catalogueEntry) => {
      if (allowedFormatsSet[catalogueEntry.format]) {
        acc[(<any>catalogueEntry)._id] = catalogueEntry
      }
      return acc
    }, {} as ContentIdVsCatalogue)

    const filteredContentIds = _.keys(filteredContentIdVsCatalogue)


    const contentIdVsUserScoreMetric = (await Promise.all(filteredContentIds.map(id => this.diyFulfilmentService.getUserScoreMetricsBulkV2(contentIdVsUserIds[id], id, "LIVE")))).reduce((acc, userScoreMetricsResponses, index) => {
      acc[filteredContentIds[index]] = userScoreMetricsResponses
      return acc
    }, {} as ContentIdVsUserScoreMetrics)


    /* Sort based on class end time */
    const filteredSortedContentIds = _.keys(filteredContentIdVsCatalogue).sort((id1, id2) => {
      const catalogueEntry1 = filteredContentIdVsCatalogue[id1]
      const catalogueEntry2 = filteredContentIdVsCatalogue[id2]
      return (catalogueEntry2.scheduledTimeEpoch + catalogueEntry2.duration) - (catalogueEntry1.scheduledTimeEpoch + catalogueEntry1.duration)
    }).slice(0, CLASS_WALL_TOTAL_REPORTS_LIMIT)


    /* Sort each UserScoreMetricsResponse[] based on playback millis */
    _.keys(contentIdVsUserScoreMetric).forEach(id => {
      contentIdVsUserScoreMetric[id].sort((metric1, metric2) => {
        /* Descending order */
        return metric2.playbackMillis - metric1.playbackMillis
      })
    })


    const reports = await Promise.all(filteredSortedContentIds.map(async (id) => {
      const userScoreMetricsForContentId = contentIdVsUserScoreMetric[id]
      const liveVideoResponse = filteredContentIdVsCatalogue[id]

      return (await Promise.all(userScoreMetricsForContentId.map(async (scoreMetric) => {

          try {
            /* only show completed reports */
            if (scoreMetric.reportStatus === SessionReportStatus.INCOMPLETE) {
              return undefined
            }
            let momentsImage
            if (!scoreMetric?.imageDetails?.imageUrl) {
              // currently only LIVE_SESSION are being used, if DIY is needed change the tenant accordingly
              const postResponse = (await eternalPromise(this.socialService.getUserPostsForCommunity(Number(scoreMetric.userId), id, Tenant.LIVE, 1, 0))).obj
              const media = postResponse?.contentList.elements?.[0]?.medias?.[0]
              if (media && media.fileCDNPrefix && media.fileName && media.fileExtension) {
                momentsImage = media.fileCDNPrefix + media.fileName + "." + media.fileExtension
              }
            }

            const {obj: reportWithoutPostData, err} = await eternalPromise(DigitalReportViewBuilder.getReportSelfieShareCardWidget({session: liveVideoResponse, userContext: {...userContext, userProfile: {...userContext.userProfile, userId: scoreMetric.userId}}, userScoreMetrics: scoreMetric, source: "class_moments_page", momentsImage, diyFulfilmentService: this.diyFulfilmentService, socialService: this.socialService, logger: this.logger, userCache: this.userCache, contentType: "LIVE"}))

            if (err) {
              this.rollbarService.sendError(err)
            }
            if (!reportWithoutPostData) {
              return undefined
            }

            const userForReport = userIdVsUserData[scoreMetric.userId]
            const postData = {
              userName: userForReport.firstName + (userForReport.lastName ? ` ${userForReport.lastName}` : ""),
              profilePictureUrl: userForReport.profilePictureUrl,
              shareAction: currentUserId === scoreMetric.userId ? {
                actionType: "SHARE_SCREENSHOT",
              } : undefined,
            }

            const extraData = {
              containerStyle: {
                marginBottom: 0,
              },
              cardV2SceneStyle: {
                marginTop: 0,
              },
              hasShadow: true,
              hideInteractiveLayer: true,
              postData,
              button: undefined as undefined,
              overlayAction: currentUserId === scoreMetric.userId ? {
                actionType: "NAVIGATION",
                url: `curefit://livesessionreport?contentId=${scoreMetric.contentId}`
              } : undefined
            }

            Object.assign(reportWithoutPostData, extraData)

            reportWithoutPostData.maxTime = liveVideoResponse.scheduledTimeEpoch + liveVideoResponse.duration

            return reportWithoutPostData as TimeStampedWidget<any>
          } catch (e) {
            this.logger.info(`Error building reports in getReports ${e.message} ${e.stack}`)
            return undefined
          }

        }))
      ).filter(item => item)

    }))


    return _.flatten(reports)
  }


  createSections(inputs: TimeStampedWidget<any>[], userContext: UserContext): LeagueClassStreamWidget {
    const tz = userContext.userProfile.timezone
    const todaysStartTime = momentTz.tz(tz).startOf("day")

    /* sort descending */
    inputs.sort((i1, i2) => i2.maxTime - i1.maxTime)

    const sections = []
    for (let i = 0, nextSectionStart = 0; i < inputs.length; i = nextSectionStart) {
      const item = inputs[i]

      const sectionBehindTodayByDays = this.behindMomentInDays(todaysStartTime, item.maxTime, tz)

      const nextIndexOfDifferentDiff = _.findIndex(inputs, (searchItem) => this.behindMomentInDays(todaysStartTime, searchItem.maxTime, tz) !== sectionBehindTodayByDays, i + 1)

      nextSectionStart = nextIndexOfDifferentDiff > -1 ? nextIndexOfDifferentDiff : inputs.length

      const header = {
        title: this.getClassStreamSectionHeaderText(sectionBehindTodayByDays, tz, todaysStartTime)
      }
      const data = inputs.slice(i, nextSectionStart)
      const newSection = {
        data,
        header
      }

      sections.push(newSection)
    }
    return !_.isEmpty(sections) ? {
      widgetType: "LEAGUE_CLASS_STREAM_WIDGET",
      sections,
    } : undefined
  }

  async getSquadChallengeBadgesList(userContext: UserContext, communityIdVsUserList: CommunityIdVsUserList) {
    const userId = userContext.userProfile.userId
    const {obj: communitiesResponse} = await eternalPromise(this.socialService.getUserCommunities(userId, CommunityType.LEAGUE, false, 10, 0))
    const activitySummaries: {val: GroupActivitySummary, communityId: number}[] = (await Promise.all(communitiesResponse.communities.map(async (community) => {
      try {
        return {
          communityId: community.id,
          val: await this.questService.getGroupActivitySummary(String(community.id), "SOCIAL")}
      }
      catch (e) {
        this.logger.error(e)
        return Promise.resolve(undefined)
      }
    }))).filter(item => item)
    const sortedTimeStampedBadgeIds = _.flatten(activitySummaries.map(summary => summary.val.badges.map(badge => {
      return {
        badgeId: badge.badgeId,
        maxTime: this.getUTCMillsFromISO(String(badge.awardedAt)),
        communityId: summary.communityId
      }
    })))
    return await Promise.all(sortedTimeStampedBadgeIds.map((badgeIdObj) => this.getSquadChallengeBadgeWidget(badgeIdObj.badgeId, communityIdVsUserList[badgeIdObj.communityId], badgeIdObj.maxTime)))
  }

  async getSquadChallengeBadgeWidget(badgeId: string, userList: LeagueLeaderBoardUser[], maxTime: number): Promise<TSSquadChallengeBadgeWidget> {
    try {
      const badge: Badge = await this.questService.getBadgeById(badgeId)
      if (!badge) {
        return undefined
      }
      // todo incorrect implementation since no way to get userId-communityId vs badgeId, may show people who didnt actually won the badge
      if (_.isEmpty(userList)) {
        return undefined
      }
      return {
        widgetType: "SQUAD_CHALLENGE_BADGE_WIDGET",
        badgeImage: UrlPathBuilder.getBadgeImagePath(badge.badgeId, true),
        title: "WOOHOO!",
        description: `Our squad completed the challenge and has earned the ${badge.name.toUpperCase()} badge.`,
        action: {
          actionType: "SHARE_SCREENSHOT",
          title: "SHARE"
        },
        userList: userList.map(listItem => {
          return {
            ...listItem,
            ...NO_RANK_METRIC_OBJ
          }
        }),
        cfIcon: "image/cf_logo_white_colored.png",
        maxTime
      }
    }
    catch (e) {
      this.logger.info(`Error building getSquadChallengeBadgeWidget: ${e.message} ${e.stack}`)
      return undefined
    }
  }

  /*
  * Gets challenges which have the squad tag and are either ACTIVE, UPCOMING or ended within MAX_DIFF_DAYS_FOR_SHOWING_CHALLENGE, for non SQUAD_CHALLENGE_PAGE source, filter based on productType
  * */
  getChallengesNeeded(userContext: UserContext, source = <LEAGUE_LEADERBOARD_SOURCE | SQUAD_CHALLENGES_SOURCE>LEAGUE_LEADERBOARD_SOURCE.WALL, selectedChallengeId?: string, productType?: ProductType) {
    if (!AppUtil.isSquadChallengesSupported(productType)) return []
    const challengeIdVsStatus: IdVsValueMap<CHALLENGE_STATUS> = {}
    const tz = userContext.userProfile.timezone
    const challenges =  this.challengeCache.getAllChallenges().filter((challenge) => {
      const {hasTestTag, hasSquadTag, hasLiveTag} = DigitalSocialLeagueTabPageViewBuilder.challengeHasCorrectTagsForSquads(challenge)
      // disabling live squads challenges
      if (hasTestTag) {
        return false
      }

      if (hasSquadTag) {
        if (source === LEAGUE_LEADERBOARD_SOURCE.SQUAD_CHALLENGE_PAGE) {
          return challenge.challengeId === selectedChallengeId
        }
        if ((source === LEAGUE_LEADERBOARD_SOURCE.WALL || source === LEAGUE_LEADERBOARD_SOURCE.CLP || source === SQUAD_CHALLENGES_SOURCE.WALL_CHALLENGES_CAROUSEL) && !(productType === "FITNESS" && hasLiveTag)) {
          const todaysStartTime = momentTz.tz(tz).startOf("day")
          const challengeEndTime =  momentTz.tz(challenge.endDate, tz).startOf("day")
          const diffInDays = todaysStartTime.diff(challengeEndTime, "days")
          const challengeStatus = challengeIdVsStatus[challenge.challengeId] || (challengeIdVsStatus[challenge.challengeId] = DigitalLeagueChallengeViewBuilder.getChallengeStatusAccordingToTimezone(userContext.userProfile.timezone, challenge))
          /* showing all past challenges for challenges carousel whether the user was ever enrolled in it or not, call enrolments api if needed stricter checks */
          return challengeStatus === CHALLENGE_STATUS.UPCOMING || challengeStatus === CHALLENGE_STATUS.ONGOING || (source !== SQUAD_CHALLENGES_SOURCE.WALL_CHALLENGES_CAROUSEL && diffInDays <= MAX_DIFF_DAYS_FOR_SHOWING_CHALLENGE)
        }
      }

      return false
    })

    if (source === LEAGUE_LEADERBOARD_SOURCE.WALL || source === SQUAD_CHALLENGES_SOURCE.WALL_CHALLENGES_CAROUSEL) {
      challenges.sort((c1, c2) => {
        const c1Status = challengeIdVsStatus[c1.challengeId]
        const c2Status = challengeIdVsStatus[c2.challengeId]
        if (this.putC1BeforeC2(c1Status, c2Status)) {
          return -1
        }
        if (this.putC1BeforeC2(c2Status, c1Status)) {
          return 1
        }
        return this.doesC1StartBeforeC2({challengeId: c1.challengeId}, {challengeId: c2.challengeId}, _.keyBy([c1, c2], "challengeId")) ? -1 : 1
      })
    }

    return challenges
  }

  putC1BeforeC2(c1Status: CHALLENGE_STATUS, c2Status: CHALLENGE_STATUS) {
    return (c1Status === CHALLENGE_STATUS.ONGOING && c2Status !== CHALLENGE_STATUS.ONGOING) || (c1Status === CHALLENGE_STATUS.UPCOMING && c2Status === CHALLENGE_STATUS.ENDED)
  }

  getLeaderBoardPropsForEnrolment(recentCommunityEnrolment: EnrolmentResponseObj, source: LEAGUE_LEADERBOARD_SOURCE, challenge?: Challenge): Partial<Pick<LeaderboardWidgetWithSortFields, "rank" | "subTitle" | "tag">> {
    if (!recentCommunityEnrolment) return {}

    let tag
    let subTitle
    let rank
    const {showRank, showDescription} = this.shouldShowLeaderboardSubtitle(source)
    if (showRank && challenge.challengeType === TEAM_CHALLENGE_TYPE) {
      const leaderboardId = DigitalLeagueChallengeViewBuilder.getTeamLeaderboardId(challenge)
      const globalRank = DigitalLeagueChallengeViewBuilder.getTeamRank(recentCommunityEnrolment.enrolment, leaderboardId)
      if (globalRank) {
        rank = globalRank
        subTitle = `Global rank of squad : ${globalRank}. `
      }
    }

    if (showDescription) {
      subTitle =  (subTitle || "") + (challenge?.constants?.find((val) => val.name === CHALLENGE_DESCRIPTION_NAME)?.value || "")
    }
    if (this.shouldShowLeaderboardTag(source)) {
      tag = this.getTagForLeaderBoard(recentCommunityEnrolment.enrolment, recentCommunityEnrolment.groupEnrolment, challenge)
    }

    return {rank, subTitle, tag}
  }

  behindMomentInDays(day: moment.Moment, dateToTest: number, tz: Timezone) {
    const beginOfDate = momentTz.tz(day, tz).startOf("day")
    const beginOfDateToTest = momentTz.tz(dateToTest, tz).startOf("day")
    return beginOfDate.diff(beginOfDateToTest, "days")
  }

  static challengeHasCorrectTagsForSquads(challenge: Challenge) {
    const ret = {hasTestTag: false, hasSquadTag: false, hasLiveTag: false}
    if (challenge?.tags) {
      for (const tag of challenge.tags) {
        if (tag === SQUAD_CHALLENGE_TAG) {
          ret.hasSquadTag = true
        }
        if (tag === LIVE_CHALLENGE_TAG) {
          ret.hasLiveTag = true
        }
        if (tag === TEST_TAG) {
          ret.hasTestTag = true

          /* returning early since this challenge is not needed now, even when it might have the Squad tag */
          return ret
        }
      }
    }
    return ret
  }

  async getClassAction(source: LIVE_SESSION_ACTION_SOURCE, userContext: UserContext, videoResponse: DigitalCatalogueEntryV1) {
    let action: Action

    const nowEpoch = TimeUtil.getCurrentEpoch()
    const startTime = videoResponse.playerStartTimeEpoch
    const endTime = videoResponse.scheduledTimeEpoch + videoResponse.duration
    let title
    if (startTime > nowEpoch) {
      title = "JOIN"
    } else if (endTime < nowEpoch) {
      title = "KNOW MORE"
    } else {
      title = "JOIN NOW"
    }
    if (AppUtil.isNewLiveClassProductPageSupported(userContext)) {
      const url = `${this.diyFulfilmentService.getClassDetailDeepLinkUrl(videoResponse)}&pageFrom=${source}`
      action = {
        actionType: "NAVIGATION",
        url,
        title:  _.isEmpty(title) ? "KNOW MORE" : title
      }
    } else {
      action = LiveUtil.getLiveSessionDetailActionFromLiveClassId((<any>videoResponse)._id, (<any>videoResponse)._id, source, videoResponse.contentCategory, userContext, title)
    }
    return action
  }

  getChallengeAndCommunityVsEnrolments(challengesVsEnrolments: { enrolments: EnrolmentResponse[]}[]) {
    const communityIdVsSelectedChallengeEnrolments: CommunityIdVsEnrolmentResponse = {}
    const challengeIdVsEnrolmentResponses: ChallengeIdVsEnrolmentResponse = {}
    for (const enrolmentResponse of challengesVsEnrolments) {
      const {enrolments} = enrolmentResponse
      for (const enrolment of enrolments) {
        if (this.shouldFilterOutEnrolmentBasedOnStatus(enrolment)) {
          continue
        }

        const existingEnrolmentsForChallenge = challengeIdVsEnrolmentResponses[enrolment.challengeId]
        if (existingEnrolmentsForChallenge) {
          existingEnrolmentsForChallenge.push(enrolment)
        } else {
          challengeIdVsEnrolmentResponses[enrolment.challengeId] = [enrolment]
        }

        if (enrolment.groupEnrolments) {
          for (const groupEnrolment of enrolment.groupEnrolments) {
            const squadId = groupEnrolment?.group?.meta?.squadId
            const existingEnrolmentForCommunityId = squadId ? communityIdVsSelectedChallengeEnrolments[squadId] : undefined
            if (existingEnrolmentForCommunityId) {
              communityIdVsSelectedChallengeEnrolments[squadId].push({enrolment, groupEnrolment})
            } else {
              communityIdVsSelectedChallengeEnrolments[squadId] = [{enrolment, groupEnrolment}]
            }
          }
        }
      }
    }
    return {communityIdVsSelectedChallengeEnrolments, challengeIdVsEnrolmentResponses}
  }

  getCommunitiesToShow(communitiesResponse: CommunitiesResponse, source: LEAGUE_LEADERBOARD_SOURCE, challengeIdVsEnrolmentResponses: ChallengeIdVsEnrolmentResponse, communityIdVsSelectedChallengeEnrolments: CommunityIdVsEnrolmentResponse, selectedChallengeId?: string) {
    let communities
    if (source === LEAGUE_LEADERBOARD_SOURCE.WALL) { // get all communities
      communities = communitiesResponse.communities
    } else if (source === LEAGUE_LEADERBOARD_SOURCE.JOIN_CHALLENGE_MODAL) {// not used in current implementation
      communities = communitiesResponse.communities.filter(community => !communityIdVsSelectedChallengeEnrolments[community.id])
    } else if (source === LEAGUE_LEADERBOARD_SOURCE.SQUAD_CHALLENGE_BADGE_WIDGET) {// not used in current implementation
      communities = [communitiesResponse.communities.find(community => communityIdVsSelectedChallengeEnrolments[community.id])]
    } else if (source === LEAGUE_LEADERBOARD_SOURCE.SQUAD_CHALLENGE_PAGE) {
      /* If an enrolment exists for the challenge then show the communities enrolled in that challenge */

      const enrolmentsForSelectedChallenge = challengeIdVsEnrolmentResponses[selectedChallengeId]

      const communityIdsEnrolledToChallenge = enrolmentsForSelectedChallenge?.reduce((acc, enrolment) => {
        enrolment?.groupEnrolments.forEach((groupEnrolment) => {
          acc[DigitalSocialLeagueTabPageViewBuilder.communityIdFromGroupEnrolment(groupEnrolment)] = true
        })
        return acc
      }, {} as CommunityIdVsEnrollmentFlag)

      /* Any enrolment is valid except FORCE_EXIT but it has been filtered earlier */
      if (!_.isEmpty(enrolmentsForSelectedChallenge)) {
        communities = communitiesResponse.communities.filter((community) => communityIdsEnrolledToChallenge?.[community.id])
      } else {
        /* Show the communities never enrolled */
        communities = communitiesResponse.communities.filter((community) => !communityIdsEnrolledToChallenge?.[community.id])
      }
    }
    return communities
  }

  getEnrolmentToShow(enrolmentResponses: GetEnrolmentToShowEnrolmentResponses, source: LEAGUE_LEADERBOARD_SOURCE, challengeIdVsChallenge?: ChallengeIdVsChallenge, selectedChallengeId?: string): GetEnrolmentToShowEnrolmentResponses[0] | undefined {
    if (_.isEmpty(enrolmentResponses)) {
      return undefined
    }
    /* Showing Active, Expired or Upcoming enrolment for squad*/
    if (source === LEAGUE_LEADERBOARD_SOURCE.WALL || source === LEAGUE_LEADERBOARD_SOURCE.CLP) {
      let selectedEnrolmentResponse: GetEnrolmentToShowEnrolmentResponses[0]
      for (const enrolmentResponse of enrolmentResponses) {
        const enrolment = enrolmentResponse.enrolment

        const selectedEnrolment = selectedEnrolmentResponse?.enrolment
        if (!selectedEnrolmentResponse ||
          (enrolment.status === "EXPIRED" && selectedEnrolment.status === "UPCOMING") ||
          (enrolment.status === "EXPIRED" && selectedEnrolment.status === "EXPIRED" && (enrolment.endDate > selectedEnrolment.endDate)) ||
          (enrolment.status === "UPCOMING" && selectedEnrolment.status === "UPCOMING" && this.doesC1StartBeforeC2(enrolment, selectedEnrolment, challengeIdVsChallenge)) ||
          (enrolment.status === "ACTIVE" && selectedEnrolment.status !== "ACTIVE") ||
          (enrolment.status === "ACTIVE" && selectedEnrolment.status === "ACTIVE" && this.doesC1StartBeforeC2(enrolment, selectedEnrolment, challengeIdVsChallenge))
        ) {
          selectedEnrolmentResponse = enrolmentResponse
        }
      }
      return selectedEnrolmentResponse
    }
    if (source === LEAGUE_LEADERBOARD_SOURCE.SQUAD_CHALLENGE_PAGE) {
       /* There will be only one enrolment for a community to a challenge (FORCE_EXIT is ignored)*/
      return enrolmentResponses.find(enrolmentResponse => enrolmentResponse?.enrolment?.challengeId === selectedChallengeId)
    }
  }

  /*
  * Needed in case the challengesNeeded has no challenge to which this community was enrolled to
  * */
  async getGroupEnrolmentForCommunity(userId: string, communityId: string): Promise<EnrolmentResponseObj> {
    const groupEnrolment = (await eternalPromise(this.riddlerService.getLatestGroupEnrolment(this.groupIdFromCommunityId(communityId), false)))?.obj?.groupEnrolment
    const challengeId = groupEnrolment?.challengeId
    if (!challengeId) return
    const enrolmentsForChallenge = (await this.riddlerService.getEnrolmentsForChallenge({userId, challengeId, withChallenge: true, withStandings: true, entries: 10, withGroupEnrolments: true}))?.enrolments
    const enrolment =  _.find(enrolmentsForChallenge, (enrolment) => !this.shouldFilterOutEnrolmentBasedOnStatus(enrolment) && _.some(enrolment?.groupEnrolments, (groupEnrolment) => groupEnrolment?.groupId === this.groupIdFromCommunityId(communityId)))
    if (enrolment) {
      return {enrolment, groupEnrolment}
    }
  }

  doesC1StartBeforeC2(enrolment1: EnrolmentResponse | {challengeId: string}, enrolment2: EnrolmentResponse | {challengeId: string}, challengeIdVsChallenge?: ChallengeIdVsChallenge) {
    if (challengeIdVsChallenge) {
      return challengeIdVsChallenge[enrolment1.challengeId]?.startDate > challengeIdVsChallenge[enrolment2.challengeId]?.startDate
    }
    if ("challenge" in enrolment1 && "challenge" in enrolment2) {
      return enrolment1?.challenge?.startDate > enrolment2.challenge?.startDate
    }
  }

  async getUserData(userIds: string[]) {
    const [userProfiles, users] = await Promise.all([this.socialService.getUserProfileByUserIdBulk(userIds), this.userCache.getUsers(userIds)])
    const userIdVsProfile = _.mapKeys(userProfiles, profile => profile.userId)
    return {userIdVsProfile, users}
  }

  sortCommunityMemberByScoreFunc(userIdsVsScore: UserIdVsChallengeScore) {
    return (uId1: string, uId2: string) => {
      const rank1 = userIdsVsScore[uId1]?.rank
      const rank2 = userIdsVsScore[uId2]?.rank
      if (rank1 && rank2) {
        return rank1 - rank2
      }
      if (rank1 && !rank2) {
        return -1
      }
      if (rank2 && !rank1) {
        return 1
      }
      return 0
    }
  }

  static sortLeaderboard(d1: LeaderboardWidgetWithSortFields, d2: LeaderboardWidgetWithSortFields) {
    if (DigitalSocialLeagueTabPageViewBuilder.isTeamChallengeAndActive(d1.challengeDetails) && DigitalSocialLeagueTabPageViewBuilder.isTeamChallengeAndActive(d2.challengeDetails)) {
      return d1.rank < d2.rank ? -1 : 1
    }
    return d2.userList.length - d1.userList.length
  }

  static isTeamChallengeAndActive(challengeDetails: LeaderboardSortFields["challengeDetails"]) {
    return challengeDetails?.type === "TEAM" && challengeDetails?.isActive
  }

  getUserIdsVsScore(communityId: number, userId: string, recentCommunityEnrolment: CommunityIdVsEnrolmentResponse[""][0], profileAttributes?: UserAttributeMappingEntry[]) {
    const userIdsVsScore: IdVsValueMap<{score?: number, rank: number}> = {}
    if (this.hasNearbyStandings(recentCommunityEnrolment?.enrolment)) {
      const challengeStandings = recentCommunityEnrolment.enrolment.challengeStandings[0].nearbyStandings
      challengeStandings.forEach(standing => {
        userIdsVsScore[standing.userId] = {
          score: standing.score,
          rank: standing.rank
        }
      })
    } else {
      profileAttributes
        ?.forEach((attribute) => {
          userIdsVsScore[attribute.userId] = {
            rank: Number(attribute.attributeValue)
          }
        })
    }
    return userIdsVsScore
  }

  getLeagueIntroData() {
    return {
      widgetType: "LEAGUE_INTRO_WIDGET",
      title: "Here you’ll find following updates",
      infoArray: [{ text: "Class Bookings of friends from All Squads", icon: "/image/diy/Leagues/introIcon1.png" },
        { text: "Reports of friends from All Squads ", icon: "/image/diy/Leagues/introIcon2.png"},
        { text: "All Squads Leaderboard ", icon: "/image/diy/Leagues/introIcon3.png"},
        { text: "Moment of the Day and Class Memories ", icon: "/image/diy/Leagues/introIcon4.png" }
      ]
    }
  }

  getClassStreamSectionHeaderText(diffInDays: number, tz: Timezone, fromDate: moment.Moment) {
    const daysAdded = moment(TimeUtil.subtractDays(tz, fromDate.format(TimeUtil.DEFAULT_DATE_FORMAT), diffInDays)).format("MMM DD, YYYY")
    return behindTodayDiffToWord[diffInDays] || daysAdded
  }


  getClassStreamSectionFooterText(userForSectionLength: number, userName = "Your Squad member", className: string, classTimeEpoch: number, userContext: UserContext, centerName?: string) {
    const tz = userContext.userProfile.timezone
    const todaysStartTime =  momentTz.tz(tz).startOf("day")
    const description: {text: string, style?: any}[] = [{text: userName, style: BOLD_FONT_STYLE}]

    if (userForSectionLength > 1) {
      description.push({text: " and"})
      description.push({text: ` ${userForSectionLength - 1}`, style: BOLD_FONT_STYLE})
      description.push({text: ` other${userForSectionLength > 2 ? "s" : ""}`, style: BOLD_FONT_STYLE})
      description.push({text: " are"})
    } else {
      description.push({text: " is"})
    }
    description.push({text: " going for"})
    description.push({text: ` ${className}`, style: BOLD_FONT_STYLE})

    const dayBeginTimeForEntry = momentTz(classTimeEpoch).tz(tz).startOf("day")
    const scheduledTimeBehindTodayByDays = todaysStartTime.diff(dayBeginTimeForEntry, "days")

    if (scheduledTimeBehindTodayByDays <= 0 && behindTodayDiffToWord[scheduledTimeBehindTodayByDays]) {

      const scheduledTimeDiffDaysStr = behindTodayDiffToWord[scheduledTimeBehindTodayByDays]

      description.push({text: ` - ${scheduledTimeDiffDaysStr}`, style: BOLD_FONT_STYLE})
      description.push({text: " at "})
      description.push({
        text: TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, classTimeEpoch, "h:mm A"),
        style: BOLD_FONT_STYLE
      })
    } else {
      description.push({text: " on" })

      const day = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, classTimeEpoch, "MMM DD, YYYY")
      const time = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, classTimeEpoch, "hh:mm A")

      description.push({text: ` ${day}`, style: BOLD_FONT_STYLE})
      description.push({text: " at "})
      description.push({text: time, style: BOLD_FONT_STYLE})
    }
    if (!_.isEmpty(centerName)) {
      description.push({text: " at "})
      description.push({text: centerName, style: BOLD_FONT_STYLE})
    }
    return description
  }


  async userSquadDetails(userId: string) {
    /* this definition is used to show the banners on the class wall, is different from userLeagueHasBeenCreatedFE logic */
    const {obj: communitiesResponse} = await eternalPromise(this.socialService.getUserCommunities(userId, CommunityType.LEAGUE, false, LEAGUE_LIST_MEMBER_LIMIT, 0))
    const currentUserCommunity = communitiesResponse?.communities?.find((community) => userId === community.creatorNode.entityId)
    const numOtherCommunitiesJoined = communitiesResponse?.communities?.length - (currentUserCommunity ? 1 : 0)
    const isMemberOfAnotherSquad = numOtherCommunitiesJoined > 0
    let usersInCurrentUserCommunity
    if (!isMemberOfAnotherSquad && currentUserCommunity?.id) {
      ({obj: usersInCurrentUserCommunity} = await eternalPromise(this.socialService.getCommunityMembers(currentUserCommunity?.id, userId, 2, 0)))
    }
    return {
      hasOwnCommunity: Boolean(currentUserCommunity),
      numOtherCommunitiesJoined,
      usersInOwnCommunity: usersInCurrentUserCommunity?.length
    }
  }


  getLeagueClassStreamCell(userList: LeagueLeaderBoardUser[], userContext: UserContext): LeagueClassStreamSmallCellWidget {
    return {
      imageUrl: "/image/diy/Leagues/YourSquadBanner.png",
      title: "Congratulations!",
      subTitle: "Your Squad is now active!",
      widgetType: "LEAGUE_CLASS_STREAM_SMALL_CELL_WIDGET",
      userList: userList.map(listItem => ({...listItem, ...NO_RANK_METRIC_OBJ})),
      action: {
        actionType: "SHARE_SCREENSHOT",
        title: "SHARE",
        meta: {
          shareTitle: "My Squad",
          shareMessage: "",
        }
      }
    }
  }

  async getFilteredCommunities(userId: string, source: LEAGUE_LEADERBOARD_SOURCE, communityIdVsChallenge?: CommunityIdVsChallenge) {
    const communitiesResponse  = await this.socialService.getUserCommunities(userId, CommunityType.LEAGUE, false, LEAGUE_LEADERBOARD_COMMUNITIES_LIMIT, 0)
    switch (source) {
      case LEAGUE_LEADERBOARD_SOURCE.JOIN_CHALLENGE_MODAL: {
        return communitiesResponse.communities.filter(community => !communityIdVsChallenge[community.id])
      }
      case LEAGUE_LEADERBOARD_SOURCE.WALL: {
        return communitiesResponse.communities
      }
      default: return communitiesResponse.communities
    }
  }

  getJoinChallengePopUpAction(challengeId: string, title: string) {
    return {
      title,
      actionType: "SHOW_ALERT_MODAL",
        meta: {
      title: "Challenge Accepted!",
        subTitle: "You have joined the challenge from your Squads behalf. We have notified your friends. A shiny badge is waiting for you and your Squad.",
        meta: {
        modalHeight: 260,
      },
      actions: [
        this.getJoinChallengeCtaAction(challengeId, "Let's start", JOIN_CHALLENGE_ACTION_SOURCE.SQUAD_CHALLENGE_PAGE)
      ],
    },
    } as Action
  }

  getTagForLeaderBoard(enrolmentResponse: EnrolmentResponse, groupEnrolment: GroupEnrolment, challenge?: Challenge) {
    const challengeLeaderboardProgressStatus = this.getChallengeLeaderboardStatus(enrolmentResponse, groupEnrolment, challenge)
    return CHALLENGE_LEADERBOARD_STATUS_TEXT[challengeLeaderboardProgressStatus]
  }

  getChallengeProgressStatusViewProps(recentCommunityEnrolment: CommunityIdVsEnrolmentResponse[""][0], userStanding: UserIdVsChallengeScore[""], challenge: Challenge) {
    let checkColor
    let isSuccess, targetValue, currentScore
    if (challenge && _.isEmpty((challenge as RankedChallenge).leaderboardConfigs)) {
      // for the challenges where user success depends on group sucess
      isSuccess = challenge.groupMilestones.length === recentCommunityEnrolment.groupEnrolment.milestones.length
    } else {
      targetValue = Number(challenge?.constants?.find((val) => val.name === TARGET_METRIC_NAME)?.value || 0)

      if (challenge?.constants?.find((val) => val.name === SQUAD_MEMBER_SCORE_TYPE)?.value === "GROUP") {
        currentScore = recentCommunityEnrolment.groupEnrolment.metrics[0]?.value || 0
      } else {
        currentScore = userStanding?.score || 0
      }

      isSuccess = targetValue ? (currentScore >= targetValue) : currentScore > 0
    }

    let userProgressStatus: USER_PROGRESS_STATUS
    if (isSuccess) {
      userProgressStatus = USER_PROGRESS_STATUS.COMPLETED
    } else if (recentCommunityEnrolment.enrolment.status === "ACTIVE" || recentCommunityEnrolment.enrolment.status === "UPCOMING") {
      userProgressStatus = USER_PROGRESS_STATUS.ONGOING
    } else {
      userProgressStatus = USER_PROGRESS_STATUS.FAILED
    }

    const borderColor = USER_PROGRESS_STATUS_COLORS[userProgressStatus]
    const metricColor = USER_PROGRESS_STATUS_COLORS[userProgressStatus]
    if (userProgressStatus === USER_PROGRESS_STATUS.COMPLETED) {
      checkColor = USER_PROGRESS_STATUS_COLORS[userProgressStatus]
    }
    const metric = {
      color: metricColor,
      text: targetValue ? `${currentScore}/${targetValue}` : currentScore ? String(currentScore) : undefined
    }
    return {
      borderColor,
      checkColor,
      metric,
      borderWidth: 2,
      padding: 4
    }
  }

  getChallengeLeaderboardStatus(enrolmentResponse: EnrolmentResponse, groupEnrolment: GroupEnrolment, challenge?: Challenge) {
    let challengeLeaderboardProgressStatus: CHALLENGE_PROGRESS_STATUS
    if (challenge?.groupMilestones.length === groupEnrolment.milestones.length) {
      challengeLeaderboardProgressStatus = CHALLENGE_PROGRESS_STATUS.COMPLETED
    } else if (enrolmentResponse.status === "ACTIVE") {
      challengeLeaderboardProgressStatus = CHALLENGE_PROGRESS_STATUS.ONGOING
    }  else if (enrolmentResponse.status === "UPCOMING") {
      challengeLeaderboardProgressStatus = CHALLENGE_PROGRESS_STATUS.UPCOMING
    } else {
      challengeLeaderboardProgressStatus = CHALLENGE_PROGRESS_STATUS.FAILED
    }
    return challengeLeaderboardProgressStatus
  }

  getJoinChallengeCtaAction(challengeId: string,  title: string, source?: JOIN_CHALLENGE_ACTION_SOURCE): Action {
    return challengeId ? {
      actionType: "REST_API",
      title,
      shouldRefreshAllTabPages: true,
      shouldRefreshSourcePage: true,
      meta: {
        method: "post",
        url: "/digital/leagues/joinSquadChallenge",
        body: {
          challengeId,
          source
        }
      }
    } : undefined
  }

  getSquadChallengePageNavigationAction(challengeId: string, prevPage?: string): Action {
    return {
      actionType: "NAVIGATION",
      url: "curefit://squadchallengepage",
      title: "JOIN",
      meta: {
        queryParams: {
          challengeId,
          prevPage
        }
      }
    }
  }


  getBookClassBanner(productType: ProductType): BannerCarouselWidget {
    let action: Action
    if (productType === "FITNESS") {
      action = {
        url: "curefit://classbookingv2?pageFrom=league_wall_banner&productType=FITNESS",
        title: "Book",
        actionType: "NAVIGATION"
      }
    } else {
      action = {
        url: "curefit://liveclassbooking?pageFrom=league_wall_banner&productType=FITNESS&isLiveBookingPage=true",
        title: "Book",
        actionType: "NAVIGATION"
      }
    }

    return new BannerCarouselWidget("375:115", [
      {
        id: "league_info_banner",
        image: "/image/diy/Leagues/BookClassWallBanner.png",
        action: action as any,
      },
    ], {
      showPagination: false,
      v2: true,
      alignment: "center",
      backgroundColor: "",
      autoScroll: false,
      enableSnap: false,
      useShadow: false,
      roundedCorners: true,
      noVerticalPadding: true,
      edgeToEdge: false,
      interContentSpacing: 0,
      bannerOriginalWidth: 748,
      bannerOriginalHeight: 360,
      bannerWidth: 374,
      bannerHeight: 180,
      containerStyle: {
        marginVertical: 15,
      },
    }, 1, false, false, false)
  }

  shouldFilterOutEnrolmentBasedOnStatus(enrolment: Enrolment) {
    return enrolment.status === "FORCE_EXIT"
  }

  static communityIdFromGroupEnrolment(groupEnrolment: GroupEnrolmentResponse) {
    return groupEnrolment?.group?.meta?.squadId
  }

  groupIdFromCommunityId(communityId: string) {
    return `SOCIAL_${communityId}`
  }

  getUTCMillsFromISO(isoDate: string) {
    return moment(isoDate).valueOf()
  }

  hasNearbyStandings(enrolment?: EnrolmentResponse) {
    return !_.isEmpty(enrolment?.challengeStandings?.[0]?.nearbyStandings)
  }

  shouldShowLeaderboardTag(source: LEAGUE_LEADERBOARD_SOURCE) {
    return source === LEAGUE_LEADERBOARD_SOURCE.SQUAD_CHALLENGE_PAGE || source === LEAGUE_LEADERBOARD_SOURCE.WALL || source === LEAGUE_LEADERBOARD_SOURCE.CLP
  }

  shouldShowLeaderboardSubtitle(source: LEAGUE_LEADERBOARD_SOURCE) {
    return {
      showDescription: source === LEAGUE_LEADERBOARD_SOURCE.WALL || source === LEAGUE_LEADERBOARD_SOURCE.CLP,
      showRank: source === LEAGUE_LEADERBOARD_SOURCE.WALL || source === LEAGUE_LEADERBOARD_SOURCE.CLP || source === LEAGUE_LEADERBOARD_SOURCE.SQUAD_CHALLENGE_PAGE
    }
  }

  shouldShowLeaderboardBadgeList(source: LEAGUE_LEADERBOARD_SOURCE) {
    return source === LEAGUE_LEADERBOARD_SOURCE.WALL || source === LEAGUE_LEADERBOARD_SOURCE.CLP
  }
}



export default DigitalSocialLeagueTabPageViewBuilder
