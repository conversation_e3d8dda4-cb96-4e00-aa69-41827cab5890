import { inject, injectable } from "inversify"
import { SURYA_NAMASKARS } from "../util/LiveUtil"
import DigitalReportViewBuilder from "./DigitalReportViewBuilder"
import { DigitalCatalogueEntryV1, UserScoreMetricsResponse } from "@curefit/diy-common"
import { UserContext } from "@curefit/vm-models"
import * as _ from "lodash"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CacheHelper } from "../util/CacheHelper"
import { BASE_TYPES, Logger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

@injectable()
class YogaReportCardWidgetViewBuilder {

    constructor(@inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper, @inject(BASE_TYPES.ILogger) private logger: Logger, @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService) {}

    async buildView(userContext: UserContext, session: DigitalCatalogueEntryV1, userScoreMetrics: UserScoreMetricsResponse, shareMessageObj: {shareTitle: string, shareMessage: string}) {
        try {
            const userId = userContext.userProfile.userId
            return {
                widgetType: "YOGA_REPORT_CARD_WIDGET",
                action: this.getShareAction(shareMessageObj),
                title: await this.getReportTitle(userId),
                mainMetric: this.getMainMetric(userScoreMetrics),
                metrics: this.getMetrics(session, userScoreMetrics),
                ...this.getCustomStylingFields()
            }
        }
        catch (e) {
            this.rollbarService.sendError(e)
            return undefined
        }
    }

    private getShareAction(shareMessageObj: {shareTitle: string, shareMessage: string}) {
        return {
            actionType: "SHARE_SCREENSHOT",
            title: "SHARE",
            meta: {...shareMessageObj, useParent: true},
            analyticsData: {
                type: "cult.live report",
            },
        }
    }

    private async getReportTitle(userId: string) {
        const user = await this.userCache.getUser(userId)
        return `${_.startCase(user.firstName)}, You did`
    }

    private getCustomStylingFields() {
        return {
            noMargin: true,
            noShadow: true,
            numColumns: 2,
        }
    }

    getMainMetric(userScoreMetrics: UserScoreMetricsResponse) {
        return {
            data: `${userScoreMetrics.repCount}/${userScoreMetrics.totalRepetitions}`,
            units: SURYA_NAMASKARS
        }
    }

    getMetrics(session: DigitalCatalogueEntryV1, userScoreMetrics: UserScoreMetricsResponse) {
        const {minutes} = DigitalReportViewBuilder.getMinutesAndSeconds(userScoreMetrics?.playbackMillis || 0)
        const {minutes: totalDuration} = DigitalReportViewBuilder.getMinutesAndSeconds(session.duration)
        const metrics = []
        if (_.isNumber(minutes)) {
            metrics.push({
                title: "WORKOUT\nDURATION",
                data: `${minutes}/${totalDuration}`,
                units: "min"
            })
        }
        if (_.isFinite(userScoreMetrics?.caloriesBurnt)) {
            metrics.push({
                title: "CALORIES\nBURNT",
                data: userScoreMetrics.caloriesBurnt,
                units: "Cal"
            })
        }
    }
}

export default YogaReportCardWidgetViewBuilder
