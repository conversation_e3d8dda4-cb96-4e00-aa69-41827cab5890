import { Container, inject } from "inversify"
import AuthMiddleware from "../auth/AuthMiddleware"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService } from "@curefit/diy-client"
import * as express from "express"
import { UserContext } from "@curefit/vm-models"
import DigitalCalendarViewBuilder from "./DigitalCalendarViewBuilder"
import DigitalReportViewBuilder from "./DigitalReportViewBuilder"
import DigitalMomentsViewBuilder from "./DigitalMomentsViewBuilder"
import { SOCIAL_CLIENT_TYPES, SocialService } from "@curefit/social-client"
import {
    CommunityChallengeEnrollmentRequest,
    CommunityEntry,
    CommunityInviteType,
    CommunityRecommendationSource,
    CommunityType,
    CommunityUserInviteRequest,
    ContentEntry,
    ContentType,
    EntityType,
    MediaType,
    ProfileVisibility,
    Tenant
} from "@curefit/social-common"
import { CacheHelper } from "../util/CacheHelper"
import DigitalWorkoutMemberHistoryBuilder, {
    ITEMS_PER_ROW,
    NUM_ROWS_PUBLIC_INITIAL,
    NUM_ROWS_RANKS,
} from "./DigitalWorkoutMemberHistoryBuilder"
import SocialKnowMoreViewBuilder from "./SocialKnowMoreViewBuilder"
import BaseOrderConfirmationViewBuilder, { ConfirmationRequestParams } from "../order/BaseOrderConfirmationViewBuilder"
import {
    Action,
    CultSocialProfileListPageResponse,
    InviteInfo,
    LiveMomentOfTheDayPageResponse,
    Moments,
    PageTypes,
    RecommendedContactsResponse,
    WidgetView,
    WorkoutMemberSection,
    WorkoutMemberViewAllPageResponse
} from "@curefit/apps-common"
import * as _ from "lodash"
import * as momentTz from "moment-timezone"
import { BASE_TYPES, CLSUtil, Logger } from "@curefit/base"
import { BASE_UTILS_TYPES, S3Helper } from "@curefit/base-utils"
import LiveUtil, {
    CULT_LIVE_CAMPAIGN_ID, FEEDBACK_USER_FORM_AB_TEST_USER_FORM_ID,
    getLiveReportFilters,
    LIVE_MOMENT_ASPECT_RATIO_1,
    LIVE_REPORT_V2_ASPECT_RATIO,
    POSE_OF_DAY_MOTHERS_DAY_START_TEXT,
    poseOfDaySupportedFormats,
    RECOMMENDED_CONTACTS_SECTION_ID
} from "../util/LiveUtil"
import { ClassInviteLinkCreator } from "../cult/invitebuddy/ClassInviteLinkCreator"
import { eternalPromise, TimeUtil } from "@curefit/util-common"
import AppUtil, { MEDIA_GATEWAY_REPORT_SUPPORT, USER_JOURNEY_SUPPORTED_VERSION } from "../util/AppUtil"
import { ICultBusiness } from "../cult/CultBusiness"
import {
    CFLiveProduct,
    DIYFitnessProductExtended,
    DIYSeries,
    DIYSocialNodeType,
    DIYUserAttributeState,
    DIYUserFitnessPack,
    SessionContentType,
    SessionReportStatus, SubscriptionStatus
} from "@curefit/diy-common"
import {
    ClassMetric,
    CultBuddiesJoiningListSmallView,
    DiyShareCard,
    PostWorkoutDIY,
    ProductDetailPage
} from "../common/views/WidgetView"
import LiveClassDetailViewBuilder from "../cult/LiveClassDetailViewBuilder"
import LiveClassDetailViewBuilderV2 from "../cult/LiveClassDetailViewBuilderV2"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import LiveMembershipExpiredModalViewBuilder from "./LiveMembershipExpiredModalViewBuilder"
import { Session } from "@curefit/userinfo-common"
import { Order, OrderProduct, OrderSource } from "@curefit/order-common"
import { OrderCreate, OMS_API_CLIENT_TYPES, IOrderService, OrderCheckoutRequest, OrderCheckoutResponse } from "@curefit/oms-api-client"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { IUserService, SignedUrlResponse, USER_CLIENT_TYPES } from "@curefit/user-client"
import IUserBusiness from "../user/IUserBusiness"
import { ProductType, UrlPathBuilder } from "@curefit/product-common"
import LiveMembershipViewBuilder from "./LiveMembershipViewBuilder"
import { ErrorFactory, HTTP_CODE } from "@curefit/error-client"
import { ErrorCodes } from "../error/ErrorCodes"
import { postWorkoutMeditations } from "./PostWorkoutMeditationDetails"
import DigitalSocialLeagueTabPageViewBuilder from "./DigitalSocialLeagueTabPageViewBuilder"
import DigitalLeagueSectionViewBuilder, {
    Colors,
    DUMMY_USER_IMAGE,
    LEAGUE_BORWSE_USER_PROFILE_TITLE,
    LEAGUE_LIST_MEMBER_LIMIT,
    LeagueSectionSource,
    MEMBERS_INITIAL_LIMIT,
    PENDING_INVITES_LIMIT,
    RECOMMENDED_USERS_LIMIT
} from "./DigitalLeagueSectionViewBuilder"
import { OFFER_SERVICE_CLIENT_TYPES } from "@curefit/offer-service-client"
import { OfferServiceV3 } from "@curefit/offer-service-client/dist/src/client/OfferServiceV3"
import { Tenant as AppTenant, UserInfo } from "@curefit/user-common"
import { LivePricesResponse, OfferV2 } from "@curefit/offer-common"
import { LivePackUtil } from "../util/LivePackUtil"
import { DiscountBreakup } from "@curefit/offer-common/src/OfferV3"
import VMPageBuilder, { ListPage } from "../page/vm/VMPageBuilder"
import { LiveTrialInterstitialPage } from "./LiveTrialInterstitialPage"
import DigitalLeagueChallengeViewBuilder, { SQUAD_CHALLENGE_ENROLMENT_TOAST_MESSAGES } from "./DigitalLeagueChallengeViewBuilder"
import { CustomEventEmitter, eventName } from "../externalSource/CustomEventEmitter"
import { ChallengeCache, RIDDLER_CACHE_TYPES } from "@curefit/riddler-cache"
import { IRiddlerService, RIDDLER_CLIENT_TYPES, RiddlerCacheService } from "@curefit/riddler-client"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { RecommendedFirstClassesViewBuilder } from "./find/RecommendedFirstClassesViewBuilder"
import LiveClassDetailViewBuilderTV from "../cult/LiveClassDetailViewBuilderTV"
import SeriesViewBuilder from "./diy/SeriesViewBuilder"
import { IFeedbackRedisCache } from "../ugc/FeedbackRedisCache"
import { LiveJourneyInterstitialPage } from "./LiveJourneyInterstitialPage"
import TLLivePacksModalViewBuilder from "./TLLivePacksModalViewBuilder"
import {
    IMediaGatewayService,
    MEDIA_GATEWAY_CLIENT_TYPES,
    MediaType as MediaTypeMediaGateway,
    ObjectAcl
} from "@curefit/media-gateway-js-client"
import DigitalSessionListViewBuilder from "./DigitalSessionListViewBuilder"
import { DaysRemainingWidgetView } from "./DaysRemainingWidgetView"
import DIYClassDetailViewBuilder from "./DIYClassDetailViewBuilder"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { createHmac } from "crypto"
import CFAPIJavaService from "../CFAPIJavaService"
import OrderViewBuilder from "../order/OrderViewBuilder"
import DIYPackService from "./diy/DIYPackService"
import { IMaxmindService, MAXMIND_CLIENT_TYPES } from "@curefit/maxmind-client"

const crypto = require("crypto")

export function controllerFactory(kernel: Container) {

    @controller("/digital",
        kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession
    )
    class DigitalController {

        constructor(
            @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) private diyFulfilmentService: IDIYFulfilmentService,
            @inject(CUREFIT_API_TYPES.DigitalCalendarViewBuilder) private digitalCalendarViewBuilder: DigitalCalendarViewBuilder,
            @inject(CUREFIT_API_TYPES.DigitalReportViewBuilder) private digitalReportViewBuilder: DigitalReportViewBuilder,
            @inject(CUREFIT_API_TYPES.SocialKnowMoreViewBuilder) private socialKnowMoreViewBuilder: SocialKnowMoreViewBuilder,
            @inject(CUREFIT_API_TYPES.OrderConfirmationViewBuilderV1) private orderConfirmationViewBuilderV1: BaseOrderConfirmationViewBuilder,
            @inject(CUREFIT_API_TYPES.DigitalMomentsViewBuilder) private digitalMomentsViewBuilder: DigitalMomentsViewBuilder,
            @inject(SOCIAL_CLIENT_TYPES.SocialService) private socialService: SocialService,
            @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
            @inject(CUREFIT_API_TYPES.DigitalWorkoutMemberHistoryBuilder) private digitalWorkoutMemberHistoryBuilder: DigitalWorkoutMemberHistoryBuilder,
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(CUREFIT_API_TYPES.ClassInviteLinkCreator) private classInviteLinkCreator: ClassInviteLinkCreator,
            @inject(CUREFIT_API_TYPES.CultBusiness) private cultBusiness: ICultBusiness,
            @inject(CUREFIT_API_TYPES.LiveClassDetailViewBuilder) private liveClassDetailViewBuilder: LiveClassDetailViewBuilder,
            @inject(CUREFIT_API_TYPES.LiveClassDetailViewBuilderV2) private liveClassDetailViewBuilderV2: LiveClassDetailViewBuilderV2,
            @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
            @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
            @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
            @inject(OMS_API_CLIENT_TYPES.OrderService) private omsApiClient: IOrderService,
            @inject(CUREFIT_API_TYPES.LiveMembershipExpiredModalViewBuilder) private LiveMembershipExpiredModalViewBuilder: LiveMembershipExpiredModalViewBuilder,
            @inject(CUREFIT_API_TYPES.TLLivePacksModalViewBuilder) private TLLivePacksModalViewBuilder: TLLivePacksModalViewBuilder,
            @inject(CUREFIT_API_TYPES.UserBusiness) private userBusiness: IUserBusiness,
            @inject(CUREFIT_API_TYPES.LiveMembershipViewBuilder) private liveMembershipViewBuilder: LiveMembershipViewBuilder,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
            @inject(CUREFIT_API_TYPES.DIYPackService) private packService: DIYPackService,
            @inject(CUREFIT_API_TYPES.DigitalSocialLeagueTabPageViewBuilder) private digitalSocialLeagueTabPageViewBuilder: DigitalSocialLeagueTabPageViewBuilder,
            @inject(CUREFIT_API_TYPES.DigitalSessionListViewBuilder) private digitalSessionListViewBuilder: DigitalSessionListViewBuilder,
            @inject(CUREFIT_API_TYPES.DigitalLeagueSectionViewBuilder) private digitalLeagueSectionViewBuilder: DigitalLeagueSectionViewBuilder,
            @inject(BASE_TYPES.ClsUtil) private clsUtil: CLSUtil,
            @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) private offerServiceV3: OfferServiceV3,
            @inject(CUREFIT_API_TYPES.DigitalLeagueChallengeViewBuilder) private digitalLeagueChallengeViewBuilder: DigitalLeagueChallengeViewBuilder,
            @inject(CUREFIT_API_TYPES.VMPageBuilder) private vmPageBuilder: VMPageBuilder,
            @inject(CUREFIT_API_TYPES.CustomEventEmitter) private customEventEmitter: CustomEventEmitter,
            @inject(RIDDLER_CACHE_TYPES.ChallengeCache) private challengeCache: ChallengeCache,
            @inject(RIDDLER_CLIENT_TYPES.RiddlerService) private riddlerService: IRiddlerService,
            @inject(RIDDLER_CLIENT_TYPES.RiddlerCacheService) private riddlerCacheService: RiddlerCacheService,
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
            @inject(CUREFIT_API_TYPES.RecommendedFirstClassesViewBuilder) private recommendedFirstClassesViewBuilder: RecommendedFirstClassesViewBuilder,
            @inject(CUREFIT_API_TYPES.SeriesViewBuilder) private seriesViewBuilder: SeriesViewBuilder,
            @inject(CUREFIT_API_TYPES.FeedbackRedisCache) private feedbackRedisCache: IFeedbackRedisCache,
            @inject(CUREFIT_API_TYPES.LiveClassDetailViewBuilderTV) private liveClassDetailViewBuilderTV: LiveClassDetailViewBuilderTV,
            @inject(BASE_UTILS_TYPES.S3Helper) private s3Helper: S3Helper,
            @inject(MEDIA_GATEWAY_CLIENT_TYPES.IMediaGatewayService) private mediaGatewayClient: IMediaGatewayService,
            @inject(CUREFIT_API_TYPES.DIYClassDetailViewBuilder) private diyClassDetailViewBuilder: DIYClassDetailViewBuilder,
            @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
            @inject(CUREFIT_API_TYPES.CFAPIJavaService) public cfAPIJavaService: CFAPIJavaService,
            @inject(CUREFIT_API_TYPES.OrderViewBuilder) private orderViewBuilder: OrderViewBuilder,
            @inject(MAXMIND_CLIENT_TYPES.IMaxmindService) private maxmindService: IMaxmindService,
        ) {
        }

        @httpGet("/detailpage/:bookingNumber")
        async getClassDetailPage(req: express.Request): Promise<ProductDetailPage> {
            const userContext: UserContext = req.userContext as UserContext
            const apiKey: string = req.headers["api-key"] as string || req.headers["apikey"] as string
            const ip: string = req.ip ?? req.connection.remoteAddress
            const bookingNumber: string = AppUtil.isTVAppWithApiKey(apiKey) || AppUtil.isWeb(userContext) ? req.params.bookingNumber : req.query.bookingNumber
            const liveClassId: string = !_.isEmpty(req.query.liveClassId) && req.query.liveClassId !== "undefined" ? req.query.liveClassId : bookingNumber
            const category: string = req.query.category
            const nodeRelationID = req.query.nodeRelationID
            const isSuccessiveClassCall: boolean = req.query.isSuccessiveClassCall === "true" ? true : false
            const isNoSlotSelected: boolean = req.query.isNoSlotSelected === "true" ? true : false
            const isDIY: boolean = req.query.isDIY === "true" ? true : false
            const blockIfInternationalUser: boolean = await AppUtil.blockFitnessContentForInternationalUser(userContext, this.maxmindService, this.logger)
            if (isDIY) {
                const diyProductId = req.params.bookingNumber
                const packId = req.query.packId
                const isSwap = req.query.isSwap === "true"
                const viewOnly = req.query.viewOnly === "true"
                return this.diyClassDetailViewBuilder.buildView(userContext, diyProductId, packId, blockIfInternationalUser, isSwap, viewOnly)
            }
            if (AppUtil.isTVAppWithApiKey(apiKey)) {
                return this.liveClassDetailViewBuilderTV.buildView(userContext, liveClassId, bookingNumber, true, false, nodeRelationID, isSuccessiveClassCall, isNoSlotSelected)
            }
            return this.liveClassDetailViewBuilderV2.buildView(userContext, category, liveClassId, bookingNumber, true, blockIfInternationalUser, nodeRelationID, isSuccessiveClassCall, isNoSlotSelected)
        }

        @httpGet("/calendar")
        async fetchCalendar(request: express.Request) {
            const userContext: UserContext = request.userContext as UserContext
            const productType = request.query.productType
            return this.digitalCalendarViewBuilder.buildView(userContext, productType)
        }

        @httpGet("/:contentId/report")
        async getUserReport(request: express.Request) {
            const userContext: UserContext = request.userContext as UserContext
            const apiKey: string = request.headers["api-key"] as string || request.headers["apikey"] as string
            const orderSource = AppUtil.callSource(apiKey)
            const contentId = request.query.contentId
            const userSessionId = request.query.userSessionId
            const prevPage = request.query.prevPage
            const session: Session = request.session
            const isDIY = request.query.contentType === "DIY_FITNESS" || request.query.contentType === "DIY_MEDITATION"
            return this.digitalReportViewBuilder.buildView(userContext, contentId, prevPage, orderSource, session, isDIY, userSessionId, request.query.contentType)
        }

        @httpGet("/know-more")
        async getSocialKnowMorePage(request: express.Request) {
            const userContext: UserContext = request.userContext as UserContext
            return this.socialKnowMoreViewBuilder.buildView(userContext)
        }

        @httpPost("/updateSessionReportById")
        async updateSessionReportById(request: express.Request) {
            const userContext: UserContext = request.userContext as UserContext
            const { productType, fulfilmentId, imageFilter, contentId, userSessionId } = request.body
            const appVersion: number = Number(request.headers["appversion"])
            let imageUrl = request.body.imageUrl
            if (imageUrl && appVersion >= MEDIA_GATEWAY_REPORT_SUPPORT) {
                imageUrl = await this.mediaGatewayClient.validateAndGetDestinationUrl(imageUrl)
            }
            if (!_.isEmpty(userSessionId)) {
                return await this.diyFulfilmentService.updateUserScoreMetricsImageDetailsV2(userSessionId, userContext.userProfile.userId, {
                    imageUrl, imageFilter
                })
            }
            if (productType === "DIY_FITNESS" || productType === "DIY_MEDITATION") {
                return await this.diyFulfilmentService.updateDIYProductFulfilmentImageDetails(fulfilmentId, {
                    imageUrl: imageUrl,
                    imageFilter: imageFilter
                })
            }
            return this.diyFulfilmentService.updateUserScoreMetricsImageDetails(
                contentId,
                userContext.userProfile.userId,
                {
                    imageUrl,
                    imageFilter
                }
            )
        }

        @httpPost("/book/:classId")
        async bookClass(request: express.Request) {
            const classId = request.params.classId
            const userContext: UserContext = request.userContext
            const apiKey: string = request.headers["api-key"] as string || request.headers["apikey"] as string

            const params: ConfirmationRequestParams = {
                orderSource: AppUtil.callSource(apiKey),
                userContext: userContext
            }

            return await this.orderConfirmationViewBuilderV1.buildLiveClassConfirmationView(userContext, classId, params, request)
        }

        @httpGet("/moments/:contentId")
        async getLiveMomentsByClass(request: express.Request) {
            const userContext: UserContext = request.userContext as UserContext
            const contentId = request.params.contentId
            return await this.digitalMomentsViewBuilder.buildView(contentId, userContext)
        }

        @httpGet("/diySessionSummary")
        async getDiySummaryReport(request: express.Request): Promise<{ pageLoadAction: Action, widgets: WidgetView[] }> {
            const fulfilmentId = request.query.fulfilmentId
            const userContext: UserContext = request.userContext as UserContext
            const tz = userContext.userProfile.timezone
            const diyProductFitnessReport = await this.diyFulfilmentService.getDIYProductFitnessReport(fulfilmentId)
            const tenant = AppUtil.getTenantFromUserContext(userContext)
            let sessionDetail
            const metrics: ClassMetric[] = []
            let diyPack
            if (diyProductFitnessReport.productType === "DIY_FITNESS") {
                sessionDetail = (await this.diyFulfilmentService.getDIYFitnessProductsByProductIds(userContext.userProfile.userId, [diyProductFitnessReport.productId], tenant))[0]
                diyPack = await this.packService.getFitnessDIYPackV2(diyProductFitnessReport.packId, userContext.userProfile.userId)
                metrics.push({ title: "WORKOUT DURATION", data: Math.round(diyProductFitnessReport.duration / 60000).toString(), units: "Mins" })
                // metrics.push({ title: "CALORIES BURNT", data: diyProductFitnessReport.caloriesBurnt ? diyProductFitnessReport.caloriesBurnt.toString() : "", units: "Calories", headerColor: "#f57575" })
            } else {
                diyPack = await this.packService.getMindDIYPackV2(diyProductFitnessReport.packId, userContext.userProfile.userId)
                metrics.push({ title: "MEDITATION DURATION", data: Math.round(diyProductFitnessReport.duration / 60000).toString(), units: "Mins" })
                sessionDetail = (await this.diyFulfilmentService.getDIYMeditationProductsByProductIds(userContext.userProfile.userId, [diyProductFitnessReport.productId], tenant))[0]
            }
            const user = await this.userCache.getUser(userContext.userProfile.userId)
            const userName = _.isNil(user) ? "" : user.firstName
            const userNameCapitals = userName ? userName.toUpperCase() : ""
            const widgets: WidgetView[] = []

            const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, this.hamletBusiness)

            let pageLoadAction: Action = undefined
            if (!AppUtil.isInternationalApp(userContext)) {
                const isUserEligibleForTrial = await LiveUtil.isUserEligibleForLivePackTrial(userContext, this.diyFulfilmentService)
                if (isUserEligibleForTrial) {
                    const isUserPartOfExperiment1 = await this.cultBusiness.checkIfUserPartOfLiveTrialExperiment1(userContext)
                    if (isUserPartOfExperiment1) {
                        const apiKey: string = request.headers["api-key"] as string || request.headers["apikey"] as string
                        const orderSource = AppUtil.callSource(apiKey)
                        const session: Session = request.session
                        const trialStarted = await LivePackUtil.startCurefitLiveTrial(this.serviceInterfaces, userContext, orderSource, session)
                        pageLoadAction = LiveUtil.getLivePackTrialStartAction(undefined, userContext, "digital_report", bucketId)
                        widgets.push(LivePackUtil.getSingleBannerCarouselWidget("image/livefit/app/trial.png", 334, 57, undefined, "transparent"))
                    }
                    else {
                        widgets.push(LivePackUtil.getSingleBannerCarouselWidget("image/livefit/app/trial_exp_2.png", 334, 148, LiveUtil.getLivePackTrialStartAction(undefined, userContext, "digital_report", bucketId), "transparent"))
                    }
                }
            }
            const classLink = await this.classInviteLinkCreator.getDiyClassInviteLink(userContext, diyPack.pack, diyProductFitnessReport.productType, diyProductFitnessReport.packName, DIYSocialNodeType.SESSION, "", sessionDetail.productId, sessionDetail.title)
            const digitalShareCard: DiyShareCard = {
                widgetType: "DIGITAL_SHARE_CARD_WIDGET",
                header: {
                    title: diyProductFitnessReport.packName + " | " + sessionDetail.title,
                    subTitle: TimeUtil.formatDateInTimeZone(tz, diyProductFitnessReport.startTime, "ddd MMM DD") + " | " +
                        TimeUtil.formatDateInTimeZone(tz, diyProductFitnessReport.startTime, "hh:mm a") + " | " + `${Math.round(sessionDetail.duration / 60000)} mins`,
                    noPaddingTop: true,
                    backgroundImageUrl: "/image/livefit/app/report-header-bg-all.png",
                },
                metrics,
                user: {
                    userId: user.id,
                    firstName: user.firstName,
                    profilePictureUrl: user.profilePictureUrl
                },
                isReportV2: true,
                isMoment: false,
                achievement: "You did great today",
                isReportIncomplete: false,
                imageUrl: diyProductFitnessReport.imageDetails?.imageUrl ? diyProductFitnessReport.imageDetails.imageUrl : diyPack.pack.imageDetails?.heroImage,
                fitnessReportFilters: getLiveReportFilters(userNameCapitals, null, tenant),
                imageFilter: diyProductFitnessReport.imageDetails?.imageFilter,
                productType: diyProductFitnessReport.productType,
                containerStyle: { marginBottom: 0, marginTop: 4 },
                shareAction: {
                    actionType: "SHARE_SCREENSHOT",
                    meta: {
                        shareTitle: diyProductFitnessReport.productType ? "Fitness Report" : "Meditation Report",
                        shareMessage: `I just finished a ${diyProductFitnessReport.packName} workout and I feel awesome! You too can workout from home on the cult.fit app ${classLink}`,
                    },
                    analyticsData: {
                        type: "diyreport",
                    },
                    title: "SHARE YOUR ACHIEVEMENT"
                },
                fulfilmentId
            }
            widgets.push(digitalShareCard)
            return { pageLoadAction, widgets }
        }

        @httpGet("/diyRecommendation")
        async getDiyRecommendation(req: express.Request): Promise<{ widgets: WidgetView[], backAction?: Action }> {
            const fulfilmentId = req.query.fulfilmentId
            const widgets: WidgetView[] = []
            const randomPostMeditation = postWorkoutMeditations[Math.floor(Math.random() * 3)]
            const postWorkoutDiyWidget: PostWorkoutDIY = {
                widgetType: "POST_WORKOUT_DIY_WIDGET",
                title: "Complete your workout with a 5-min meditation",
                description: "Taking care of your mind is #justasimportant",
                image: randomPostMeditation.imageUrl,
                // Commented till reports get fixed
                // skipAction: {
                //     title: "SKIP",
                //     actionType: "NAVIGATION",
                //     navigationType: "NAVIGATE_REPLACE",
                //     url: `curefit://diyreportspage?url=/digital/diySessionSummary?fulfilmentId=${fulfilmentId}`,
                //     analyticsData: {
                //         packId: randomPostMeditation.packId,
                //         activityId: randomPostMeditation.activityId
                //     }
                // },
                skipAction: {
                    title: "SKIP",
                    actionType: "POP_ACTION",
                    analyticsData: {
                        packId: randomPostMeditation.packId,
                        activityId: randomPostMeditation.activityId
                    }
                },
                containerStyles: {
                    marginTop: 64
                },
                action: {
                    title: "BEGIN",
                    actionType: "PLAY_VIDEO",
                    meta: {
                        content: {
                            id: randomPostMeditation.contentId,
                            type: randomPostMeditation.contentType,
                            format: randomPostMeditation.contentFormat,
                            URL: randomPostMeditation.contentType === "audio" ? UrlPathBuilder.getAudioPath(randomPostMeditation.contentId, randomPostMeditation.contentFormat) : UrlPathBuilder.getVideoPath(randomPostMeditation.contentId, randomPostMeditation.contentType),
                            absoluteUrl: randomPostMeditation.contentType === "audio" ? UrlPathBuilder.getAudioAbsolutePath(randomPostMeditation.contentId, randomPostMeditation.contentFormat) : UrlPathBuilder.getVideoPath(randomPostMeditation.contentId, randomPostMeditation.contentFormat)
                        },
                        queryParams: {
                            activityId: randomPostMeditation.activityId,
                            packId: randomPostMeditation.packId,
                            contentId: randomPostMeditation.contentId,
                            consumptionRequired: true,
                            activityType: "DIY_MEDITATION",
                            title: randomPostMeditation.title,
                            image: randomPostMeditation.imageUrl,
                            skipReport: true
                        },
                        title: randomPostMeditation.title,
                        checkDownloadStatus: true
                    },
                    analyticsData: {
                        packId: randomPostMeditation.packId,
                        activityId: randomPostMeditation.activityId
                    }
                }
            }
            widgets.push(postWorkoutDiyWidget)
            return {
                widgets,
                // backAction: {
                //     actionType: "NAVIGATION",
                //     navigationType: "NAVIGATE_REPLACE",
                //     url: `curefit://diyreportspage?url=/digital/diySessionSummary?fulfilmentId=${fulfilmentId}`,
                // }
            }
        }

        @httpGet("/classMembers/all")
        async getClassMembers(req: express.Request): Promise<WorkoutMemberViewAllPageResponse> {
            const userContext: UserContext = req.userContext as UserContext
            const userId = userContext.userProfile.userId
            const contentId = req.query.classId
            const [currentUser, session] = await Promise.all([this.socialService.getUserProfileByUserId(userId), this.diyFulfilmentService.getDigitalCatalogueEntry(contentId)])
            const isCurrentUserProfilePublic = currentUser.visibility === ProfileVisibility.PUBLIC
            const topPerformerFormatBlackList = ["YOGA", "MEDITATION", "YOGA_VINYASA", "YOGA_HATHA", "YOGA_GENTLE"]
            const userFilters = (await this.socialService.getCommunityUserFilters()).filter(userFilter => !(topPerformerFormatBlackList.includes(session.format) && userFilter === "RANKS")).map((userFilter = "NONE") => userFilter.toString())

            if (!isCurrentUserProfilePublic) {
                return await this.digitalWorkoutMemberHistoryBuilder.getViewAllPagePrivate(userContext, contentId, userFilters)
            }
            const payloadForFilter: { [key: string]: number } = {}
            const userProfileEntriesNonEmpty = (await Promise.all(userFilters.map((userFilter = "NONE") => {
                const filter = userFilter
                let payloadSize
                if (filter === "RANKS") {
                    payloadSize = NUM_ROWS_RANKS * ITEMS_PER_ROW
                }
                else {
                    payloadSize = NUM_ROWS_PUBLIC_INITIAL * ITEMS_PER_ROW
                }
                payloadForFilter[filter] = payloadSize
                return eternalPromise(this.socialService.getCommunityUsers(userId, undefined, contentId, Tenant.LIVE, filter, 0, payloadSize), `getCommunity Users failed for userId: ${userId}, contentId: ${contentId}, filter: ${filter}, payloadSize: ${payloadSize} `)
            }))).map((response, index) => {
                if (response.err) {
                    this.logger.info(response.err)
                }
                return { value: response.obj, filter: userFilters[index] }
            }
            )
                .filter(response => response.value && !_.isEmpty(response.value.elements))

            if (!_.isEmpty(userProfileEntriesNonEmpty)) {
                return this.digitalWorkoutMemberHistoryBuilder.getViewAllPagePublic(userContext, userProfileEntriesNonEmpty, userFilters, payloadForFilter, contentId)
            }
            else {
                return undefined
            }
        }

        @httpPost("/classMembers/section")
        async getClassMemberSection(req: express.Request): Promise<WorkoutMemberSection> {
            const userContext: UserContext = req.userContext as UserContext
            const userId = userContext.userProfile.userId
            const currentUser = await this.socialService.getUserProfileByUserId(userId)
            const { userFilter = "NONE", classId } = req.body
            const payloadSize = Number(req.body.nextQuery.payloadSize)
            const offset = Number(req.body.nextQuery.offset)
            if (currentUser.visibility === ProfileVisibility.PRIVATE) {
                const totalElements = (await eternalPromise(this.socialService.getCountOfCommunityUsers(userId, undefined, classId, Tenant.LIVE, userFilter))).obj
                if (totalElements > 0) {
                    return this.digitalWorkoutMemberHistoryBuilder.getSectionPrivate(userFilter, totalElements)
                }
                return undefined
            }
            const userProfileEntries = (await eternalPromise(this.socialService.getCommunityUsers(userId, undefined, classId, Tenant.LIVE, userFilter, offset, payloadSize))).obj
            const sectionLoggingInfo = !userProfileEntries || _.isEmpty(userProfileEntries.elements) ? {
                filter: userFilter,
                length: userProfileEntries.elements.length,
                totalElements: userProfileEntries.totalElements
            } : undefined
            this.logger.info(`getClassMemberSection_userProfileEntries  userID: ${userId}, contentId: ${classId}, filter: ${userFilter} `, sectionLoggingInfo)
            if (!userProfileEntries) {
                return undefined
            }
            const userIds = userProfileEntries.elements.map(profileEntry => {
                return profileEntry.userId
            })
            const users = await this.userCache.getUsers(userIds)
            const sectionResponse = this.digitalWorkoutMemberHistoryBuilder.getSectionPublic(userProfileEntries, users, userFilter, payloadSize, classId)
            const sectionResponseLoggingInfo = {
                filter: userFilter,
                length: sectionResponse.data.length,
                nextQuery: sectionResponse.nextQuery
            }
            this.logger.info(`getClassMemberSection_sectionResponse  userID: ${userId}, contentId: ${classId}, filter: ${userFilter} `, sectionResponseLoggingInfo)
            return sectionResponse
        }

        @httpGet("/usersProfileByClass")
        async getUsersProfileByClass(req: express.Request): Promise<CultSocialProfileListPageResponse> {
            const classId: number = req.query.classId
            const userContext: UserContext = req.userContext as UserContext
            const selectedProfileIndex = Number(req.query.profileIndex)
            const userId: string = userContext.userProfile.userId
            const { userFilter = "NONE" } = req.query
            const offset = Number(req.query.offset)
            const payloadSize = Number(req.query.payloadSize)
            const usersProfileEntry = await this.socialService.getCommunityUsers(userId, undefined, classId.toString(), Tenant.LIVE, userFilter, offset, payloadSize)
            return await this.digitalMomentsViewBuilder.getUsersProfileByClass(usersProfileEntry, selectedProfileIndex, userContext, offset, payloadSize, userFilter)
        }

        @httpPost("/userProfilesByUserIds")
        async getUserProfilesByUserIds(req: express.Request) {
            const targetUserIds = req.body.targetUserIds
            const userContext: UserContext = req.userContext as UserContext
            const userProfiles = await this.socialService.getUserProfileByUserIdBulk(targetUserIds)
            return await this.digitalMomentsViewBuilder.getUsersProfileByUserIds(userContext, userProfiles, LEAGUE_BORWSE_USER_PROFILE_TITLE)
        }

        @httpPost("/inviteLink")
        async getInviteAction(req: express.Request): Promise<{ action: Action }> {
            const userContext: UserContext = req.userContext as UserContext
            const contentId = req.body.contentId
            const liveSession = await this.diyFulfilmentService.getDigitalCatalogueEntry(contentId)
            const referralCard = req.body.card
            let cardWithSource
            const userEligibilityForReferral = await this.cultBusiness.isUserEligibleForInviteOffer(userContext, CULT_LIVE_CAMPAIGN_ID)

            if (userEligibilityForReferral.supported && referralCard) {
                cardWithSource = { ...userEligibilityForReferral.card, ...referralCard, source: referralCard && referralCard.source || "live-upcoming-widget" }
            }

            let url
            if (cardWithSource) {
                const newLiveSessionId = liveSession.originalContentId ? liveSession.originalContentId : (<any>liveSession)._id
                url = (await eternalPromise(this.classInviteLinkCreator.getLiveClassReferralLink(userContext, (<any>liveSession)._id, liveSession.title, liveSession.description, liveSession.format, cardWithSource, newLiveSessionId))).obj
            }
            if (!url && contentId) {
                const socialDataResponse = (await eternalPromise(this.diyFulfilmentService.getSocialDataForSession(userContext.userProfile.userId, contentId, true, AppUtil.getTenantFromUserContext(userContext)))).obj
                if (socialDataResponse && socialDataResponse.classLink) {
                    url = socialDataResponse.classLink
                }
            }
            if (!url) {
                url = await this.classInviteLinkCreator.getLiveClassInviteLink(userContext, (<any>liveSession)._id, liveSession.title, liveSession.originalContentId)
            }
            const shareAction = url ? await LiveUtil.buildShareActionFromShareLink(userContext, liveSession, url, cardWithSource) : {
                actionType: "SHOW_ALERT_MODAL",
                meta: {
                    title: "Error",
                    subTitle: `Invite link not created. Please try again`,
                    meta: {
                        modalHeight: 260,
                    },
                    actions: [
                        LiveUtil.getInviteBuddyLazyLoadAction(contentId, referralCard, "Retry"),
                        {
                            title: "Cancel",
                            actionType: "HIDE_ALERT_MODAL"
                        },
                    ],
                },
            } as Action
            return {
                action: shareAction
            }
        }

        @httpPost("/videoCallJoin")
        async getVideoCallJoinAction(req: express.Request): Promise<{ action: Action }> {
            const userContext: UserContext = req.userContext as UserContext
            const contentId = req.body.contentId
            const liveSession = await this.diyFulfilmentService.getDigitalCatalogueEntry(contentId)
            const user = await userContext.userPromise
            const userId = userContext.userProfile.userId
            const action = LiveUtil.getLiveInteractionSessionJoinActionFromUrl(userContext, contentId, user, liveSession)
            await LiveUtil.updatePlaybackMetricsSynthetically(userContext, contentId, liveSession?.preferredStreamType, req, this.diyFulfilmentService, this.userCache, action.meta.displayName)
            if (AppUtil.isSugarFitOrUltraFitApp(userContext) && liveSession && !_.isEmpty(liveSession.tags)) {
                if (liveSession.tags.includes("WEBINAR")) {
                    this.userBusiness.publishUserActivityEventToRashi(userId, "SF_WEBINAR_JOINED", {
                        start_time: liveSession?.scheduledTimeEpoch,
                        content_id: contentId,
                        user_id: userId,
                    }, AppUtil.getAppTenantFromUserContext(userContext))
                } else if (_.findIndex(liveSession.tags, tag => tag.includes("SF-MASTER-CLASS")) > -1) {
                    this.userBusiness.publishUserActivityEventToRashi(userId, "SF_MASTER_CLASS_JOINED", {
                        start_time: liveSession?.scheduledTimeEpoch,
                        event_time: momentTz.tz(userContext.userProfile.timezone).toDate().getTime(),
                        content_id: contentId,
                        user_id: userId,
                    }, AppUtil.getAppTenantFromUserContext(userContext))
                } else if (_.findIndex(liveSession.tags, tag => ["INJURY", "FEMALE", "COMPLICATIONS", "HIGH_BMI", "BEGINNER", "INTERMEDIATE", "ADVANCED"].includes(tag)) > -1) {
                    this.userBusiness.publishUserActivityEventToRashi(userId, "SF_LIVE_CLASS_JOINED", {
                        start_time: liveSession?.scheduledTimeEpoch,
                        event_time: momentTz.tz(userContext.userProfile.timezone).toDate().getTime(),
                        content_id: contentId,
                        user_id: userId,
                    }, AppUtil.getAppTenantFromUserContext(userContext))
                }
            }
            return {
                action
            }
        }

        async isImmediateFeedbackAvailable(userContext: UserContext, feedbackId: string): Promise<boolean> {
            if (!AppUtil.isLiveImmediateFeedbackSupported(userContext)) {
                return false
            }

            try {
                const feedback = await this.feedbackRedisCache.getFeedbackById(feedbackId)
                if (!feedback) {
                    return false
                }

            } catch (e) {
                this.logger.error("Error while checking for immediate feedback", e)
                return false
            }

            return true
        }

        private async getEndActionUrlForMomentOfDay(userContext: UserContext, contentId: string): Promise<string> {
            return (await this.isImmediateFeedbackAvailable(userContext, AppUtil.getLiveFeedbackId(userContext, contentId))) ?
                `curefit://feedback?feedbackId=${AppUtil.getLiveFeedbackId(userContext, contentId)}&endRoute=${PageTypes.LiveSessionReport}` :
                `curefit://livesessionreport?contentId=${contentId}&prevPage=videoplayer`
        }

        /**
         * @deprecated
         * use /live/exit route instead
         */
        @httpGet("/momentOfDayEnabled/:contentId")
        async getReportStatus(req: express.Request) {
            const contentId = req.params.contentId
            const userContext: UserContext = req.userContext as UserContext
            const userId = userContext.userProfile.userId
            const session = userContext.sessionInfo
            const { obj: videoResponse, err: videoResponseError } = (await eternalPromise(this.diyFulfilmentService.getDigitalCatalogueEntry(contentId), `getDigitalCatalogueEntry failed in getReportStatus, contentId: ${contentId}, userId: ${userId}`))
            if (videoResponseError) {
                this.logger.info(videoResponseError)
            }
            const isAllowedFormat = videoResponse?.format && poseOfDaySupportedFormats.includes(videoResponse.format)
            const { obj: userMetrics, err: userMetricsError } = (await eternalPromise(this.diyFulfilmentService.getUserScoreMetrics(contentId, userId), `getUserScoreMetrics failed in getReportStatus, contentId: ${contentId}, userId: ${userId}`))
            if (userMetricsError) {
                this.logger.info(userMetricsError)
            }
            const hasCompletedClass = AppUtil.isRelaxingMomentAndReportSupported(userContext) ? true : userMetrics?.reportStatus === SessionReportStatus.COMPLETE
            const isPostCreationAllowed = (await eternalPromise(this.socialService.isPostCreationAllowed(contentId, Tenant.LIVE, Number(userId)))).obj
            const isEnabled = hasCompletedClass && isPostCreationAllowed && isAllowedFormat && AppUtil.isLiveMomentOfDaySupported(userContext)
            // if (hasCompletedClass) {
            //     this.customEventEmitter.emitEvent(eventName.VIDEO_COMPLETION, userId, session.attributionSource)
            // }
            if (!isPostCreationAllowed) {
                this.logger.info(`momentOfDay isPostCreationAllowed false for contentId: ${contentId}, userId: ${userId}`)
            }
            if (!hasCompletedClass) {
                this.logger.info(`momentOfDay hasCompletedClass false for contentId: ${contentId}, userId: ${userId}, reportStatus: ${userMetrics?.reportStatus}`)
            }
            if (!isAllowedFormat) {
                this.logger.info(`momentOfDay isAllowedFormat false for contentId: ${contentId}, userId: ${userId}, format: ${videoResponse?.format}`)
            }
            if (!isEnabled) {
                this.logger.info(`momentOfDay disabled for contentId: ${contentId}, userId: ${userId}`)
            }
            return {
                isEnabled,
                targetRoute: PageTypes.LiveMomentOfTheDayPage,
                params: {
                    queryParams: {
                        contentId
                    }
                }
            }
        }

        @httpGet("/momentOfDay")
        async getMomentOfTheDayPage(req: express.Request): Promise<LiveMomentOfTheDayPageResponse> {
            const contentId = req.query.contentId
            const userContext: UserContext = req.userContext as UserContext
            const userId = userContext.userProfile.userId
            const user = await this.userCache.getUser(userId)
            const firstName = user.firstName ? " " + user.firstName[0].toUpperCase() + user.firstName.slice(1) : ""
            const poseOfDayResponse = (await eternalPromise(this.socialService.getPodForCommunity(contentId, Tenant.LIVE))).obj
            const podMedia = poseOfDayResponse?.content?.medias && poseOfDayResponse.content.medias[0]
            let podImage = "https://dev-curefit-content.s3.ap-south-1.amazonaws.com/social-media/image/5a8a0065-4e5a-43ca-8501-c5238c040f32.png"
            if (podMedia && podMedia.fileCDNPrefix && podMedia.fileName && podMedia.fileExtension) {
                podImage = podMedia.fileCDNPrefix + podMedia.fileName + "." + podMedia.fileExtension
            } else {
                this.logger.info(`poseOfDay not received for contentId: ${contentId}, uerId: ${userId}, podMedia: ${JSON.stringify(podMedia)}`)
            }
            const startText = AppUtil.isRelaxingMomentAndReportSupported(userContext) ? POSE_OF_DAY_MOTHERS_DAY_START_TEXT : [{ text: `Well done${firstName}!\n` },
            { text: "Let’s click a " },
            {
                text: "picture\n",
                color: "#1ee0ff"
            },
            { text: "before you go?\n\nKeep your phone away and follow the pose of the day" }]

            const endNavigation = [
                {
                    title: "Cancel",
                    actionType: "HIDE_ALERT_MODAL"
                },
                {
                    actionType: "NAVIGATION",
                    url: await this.getEndActionUrlForMomentOfDay(userContext, contentId),
                    title: "SKIP"
                }
            ]

            return {
                proceedAction: {
                    title: "PROCEED",
                    actionType: "NAVIGATION",
                    url: `curefit://editmomentofdaypage?contentId=${contentId}`
                },
                overlayImage: podImage,
                startText,
                startActionText: "BEGIN",
                skipAction: {
                    title: "SKIP",
                    textColor: "#000000",
                    actionType: "SHOW_ALERT_MODAL",
                    meta: {
                        title: "Are you sure?",
                        subTitle: `This will skip your chance to capture and share your moment of the day`,
                        meta: {
                            modalHeight: 260,
                        },
                        actions: endNavigation,
                    },
                },
                imageText: "POSE OF\nTHE DAY",
                poseOfDayImage: podImage,
                retakeActionText: "RETAKE",
                countDownTimeInSecs: 5,
                toastMessage: "Enable auto-rotate from phone settings to click pictures in landscape"
            }
        }

        @httpGet("/momentOfDay/edit")
        async getEditMomentOfDayPage(req: express.Request) {
            const userContext: UserContext = req.userContext as UserContext
            const userId = userContext.userProfile.userId
            const contentId = req.query.contentId
            const isMomentOfDayV2Supported = AppUtil.isLiveMomentOfDayV2Supported(userContext)
            const user = await this.userCache.getUser(userId)
            const userNameCapitals = user.firstName?.toUpperCase() || ""
            const tenant = AppUtil.getTenantFromUserContext(userContext)
            const filters = !isMomentOfDayV2Supported ? getLiveReportFilters(userNameCapitals, null, tenant) : undefined

            const [{ obj: sessionResponse, err: sessionError }, { obj: userScoreMetricsResponse }] = await Promise.all([eternalPromise(this.diyFulfilmentService.getDigitalCatalogueEntry(contentId), `getDigitalCatalogueEntry failed for contentId: ${contentId}, userId: ${userId}`),
            eternalPromise(this.diyFulfilmentService.getUserScoreMetrics(contentId, userId))])

            let caption: Moments["caption"]
            if (sessionResponse?.title && !isMomentOfDayV2Supported) {
                const title = tenant === AppTenant.CUREFIT_APP ? "#WEARECULT" : ""
                const subTitle = `${sessionResponse.title} ${TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, sessionResponse.scheduledTimeEpoch, "MMM DD, YYYY hh:mm a")}`
                caption = {
                    title,
                    subTitle
                }
            }

            if (sessionError) {
                this.logger.info(sessionError)
            }

            const widgetId = process.env.ENVIRONMENT === "PRODUCTION" ? "4bfaee5b-9514-4247-a93c-3265fc32f070Live" : "18f386f2-0e57-4095-935e-0551e49a36de"



            const retakeAction: Action = {
                actionType: "POP_ACTION",
                title: "RETAKE",
                titleColor: "#000000",
                colors: ["#ffffff", "#ffffff"]
            }

            const proceedAction: Action = {
                actionType: "NAVIGATION",
                url: await this.getEndActionUrlForMomentOfDay(userContext, contentId),
                uploadImage: true,
                meta: {
                    backAction: {
                        actionType: "NAVIGATION",
                        url: `curefit://listpage?pageId=CultAtHome&widgetId=${widgetId}`
                    },
                },
                title: isMomentOfDayV2Supported ? "PROCEED" : "ADD TO MY REPORT",
                colors: ["#ff3278", "#ff3278"],
                titleColor: "#ffffff",
            }

            const pageActions: Action[] = []

            if (isMomentOfDayV2Supported) {
                pageActions.push(retakeAction)
            }

            pageActions.push(proceedAction)

            return {
                filters,
                pageActions,
                overlay: {
                    caption,
                    linearGradientStyle: {
                        height: 80
                    },
                    hideLogo: DigitalReportViewBuilder.isTopPerformer(userScoreMetricsResponse),
                },
                aspectRatio: isMomentOfDayV2Supported ? LIVE_MOMENT_ASPECT_RATIO_1 : LIVE_REPORT_V2_ASPECT_RATIO,
                uploadToReport: false,
                uploadToMoment: true,
                allowRetry: true,
                skipAction: {
                    actionType: "NAVIGATION",
                    url: await this.getEndActionUrlForMomentOfDay(userContext, contentId),
                    title: "SKIP",
                },
            }
        }

        @httpGet("/momentOfDay/signedUrl")
        async getMomentOfTheDaySignedUrl(req: express.Request) {
            const extension = req.query.extension
            const contentId = req.query.contentId
            const userContext: UserContext = req.userContext as UserContext
            const userId = userContext.userProfile.userId
            const { obj: isPostCreationAllowed, err: postCreationAllowedError } = (await eternalPromise(this.socialService.isPostCreationAllowed(contentId, Tenant.CULT, Number(userId)), `isPostCreationAllowedFailed for contentId: ${contentId}, userId: ${userId}`))
            if (isPostCreationAllowed.allowed) {
                const bucketName = (process.env.ENVIRONMENT === "PRODUCTION") ? "curefit-content" : "dev-curefit-content"
                const { obj: signedUrlResponse, err: mediaEntryResponseError } = (await eternalPromise(
                    this.mediaGatewayClient.getPresignedPutUrl({
                        bucketName,
                        path: "social-media/image",
                        fileName: `moment.${extension}`,
                        contentType: MediaTypeMediaGateway.IMAGE,
                        maxUploadSize: 8097152, // need to be decided
                        objectAcl: ObjectAcl.PUBLIC_READ
                    }), `getImagePreSignedUrl failed for extension: ${extension}, userId: ${userId}, contentId: ${contentId}`))
                if (mediaEntryResponseError) {
                    this.logger.info(`Moment of Day MediaGateWayClient.getPresignedPutUrl failed forextension: ${extension}, userId: ${userId}, contentId: ${contentId}. Error: ${mediaEntryResponseError}`)
                }
                return {
                    url: signedUrlResponse.url,
                    fileName: signedUrlResponse.fileName
                }
            }
            if (postCreationAllowedError) {
                this.logger.info(postCreationAllowedError)
            }
            return {
                url: ""
            }
        }

        @httpPost("/momentOfDay/createPost")
        async createMomentOfDayPost(req: express.Request) {
            const finalImageUrl = await this.mediaGatewayClient.validateAndGetDestinationUrl(req.body.fileName)
            const fileName = finalImageUrl.slice(finalImageUrl.lastIndexOf("/") + 1, finalImageUrl.lastIndexOf("."))
            const contentId = req.body.contentId
            const userContext: UserContext = req.userContext as UserContext
            const userId = userContext.userProfile.userId

            const contentEntry: ContentEntry = {
                contentType: ContentType.POST,
                creatorNode: {
                    entityId: userId,
                    entityType: EntityType.USER
                },
                medias: [
                    {
                        mediaType: MediaType.IMAGE,
                        fileName: fileName,
                        fileExtension: "jpg"
                    }
                ],
                tenant: Tenant.LIVE
            }

            const communityEntry: CommunityEntry = {
                description: "Pose of the day",
                externalId: contentId,
                name: "Class Memory",
                tenant: Tenant.LIVE,
                type: CommunityType.OPEN
            }
            try {
                const res = await this.socialService.createPost(contentEntry, communityEntry)
                return true
            }
            catch (error) {
                this.logger.info(`createPost failed for contentId: ${contentId}, userId: ${userId}, fileName: ${fileName}. ${JSON.stringify({contentEntry, communityEntry })}. error : ${error.message}`)
                throw this.errorFactory.withCode(ErrorCodes.MOMENT_OF_DAY_CREATION_ERR, 400).withDebugMessage("Post creation failed in digital moment of the day").build()
            }
        }

        @httpGet("/interstitial/postTrial")
        async getLiveMembershipExpiredModalModal(req: express.Request) {
            const userContext: UserContext = req.userContext as UserContext
            const requestSource = AppUtil.getRequestSource(userContext)
            const userId = userContext.userProfile.userId
            const deviceId = userContext.sessionInfo.deviceId
            const user = await this.userCache.getUser(userId)
            const livePacks: CFLiveProduct[] = await this.diyFulfilmentService.getCFLiveProducts(userId, requestSource, AppUtil.getTenantFromUserContext(userContext), deviceId)
            const userInfo: UserInfo = {
                userId: userId,
                deviceId: userContext.sessionInfo.deviceId,
                email: user.email,
                phone: user.phone,
                workEmail: user.workEmail
            }

            const productIds = LivePackUtil.getProductIds(livePacks)
            const livePricesResponse: LivePricesResponse = await this.offerServiceV3.getLivePackPrices(LivePackUtil.getProductIds(livePacks), userInfo)
            let offerIdOfferMap: { [offerId: string]: OfferV2 } = {}
            const offerIds: string[] = []
            productIds.forEach(productId => {
                const prices = livePricesResponse.priceMap[productId]
                if (prices && !_.isEmpty(prices.breakup)) {
                    prices.breakup.forEach((discountBreakup: DiscountBreakup) => {
                        offerIds.push(discountBreakup.offerId)
                    })
                }
            })

            if (AppUtil.isInternationalTLApp(userContext)) {
                return this.TLLivePacksModalViewBuilder.buildView(userContext, livePacks, livePricesResponse)
            }

            if (!_.isEmpty(offerIds)) {
                const offerResposne = await this.offerServiceV3.getOffersByIds(offerIds)
                offerIdOfferMap = offerResposne.data
            }
            const segments: string[] = await this.clsUtil.getPlatformSegments()
            let showTrialBanner = true
            if (!_.isEmpty(segments) && (segments.includes("cflive-free-trial-expired") || segments.includes("cflive-subscription-expired") || segments.includes("cflive-subscription-active"))) {
                showTrialBanner = false
            }
            const isUserEligibleForMonetisation = AppUtil.isLivePackSupported(userContext)
            let membership = null
            if (!AppUtil.isInternationalApp(userContext)) {
                membership = await this.diyFulfilmentService.getMembershipDetails(userId, AppUtil.getTenantFromUserContext(userContext))
            }
            return this.LiveMembershipExpiredModalViewBuilder.buildView(userContext, livePacks, membership, livePricesResponse, offerIdOfferMap, isUserEligibleForMonetisation, showTrialBanner)
        }

        @httpPost("/live/start/trial")
        async startTrialLivePack(req: express.Request): Promise<Order> {
            const session: Session = req.session
            const userContext: UserContext = req.userContext as UserContext
            const trialPack: CFLiveProduct = await this.diyFulfilmentService.getTrialCFLiveProduct(userContext.userProfile.userId, AppUtil.getRequestSource(userContext), AppUtil.getTenantFromUserContext(userContext))
            const orderProduct: OrderProduct = {
                productId: trialPack.productId,
                productType: "CF_LIVE",
                quantity: 1,
                option: {
                    isPack: true
                }
            }
            const dontCreateRazorpayOrder: boolean = req.body.dontCreateRazorpayOrder
            const userId: string = session.userId
            const apiKey: string = req.headers["api-key"] as string || req.headers["apikey"] as string
            const orderSource: OrderSource = AppUtil.callSource(apiKey)
            const useFitCash: boolean = false
            const advertiserId: string = req.body.advertiserId
            // Temp hack as website is passing offer id as empty
            orderProduct.option.offerId = !_.isEmpty(orderProduct.option.offerId) ? orderProduct.option.offerId : undefined

            const orderCreate: OrderCreate = {
                userId: session.userId,
                deviceId: session.deviceId,
                products: [orderProduct],
                source: orderSource,
                dontCreateRazorpayOrder: dontCreateRazorpayOrder,
                useOffersV2: true,
                cityId: session.sessionData.cityId,
                useFitCash: useFitCash,
                tenant: AppUtil.getTenantFromUserContext(userContext),
                osName: userContext.sessionInfo.osName,
                appVersion: (userContext.sessionInfo.appVersion ?? 0).toString()
            }

            const checkoutRequest: OrderCheckoutRequest = {
                userId: session.userId,
                deviceId: session.deviceId,
                orderProducts: [orderProduct],
                source: orderSource,
                cityId: session.sessionData.cityId,
                useFitCash: useFitCash,
                tenant: AppUtil.getTenantFromUserContext(userContext),
                osName: userContext.sessionInfo.osName,
                userAddress: undefined,
                advertiserId: advertiserId,
                appVersion: (userContext.sessionInfo.appVersion ?? 0).toString()
            }

            if (!_.isNil(advertiserId)) orderCreate.advertiserId = advertiserId

            const notification: any = undefined
            // const expId = process.env.ENVIRONMENT === "STAGE" ? "415" : "780"
            // const hamletExperimentMap = await this.hamletBusiness.getUserAllocations(AppUtil.getHamletContext(userContext, [expId]))
            // const userAssignment = hamletExperimentMap ? hamletExperimentMap.assignmentsMap[expId] : undefined
            // const doesUserBelongToWhatsappTrialExperiment = userAssignment ? userAssignment.bucket.bucketId === "1" : process.env.ENVIRONMENT === "STAGE"

            // const isWhatsappCommunicationForNudgesSupported = AppUtil.isWhatsappCommunicationForNudgesSupported(userContext)
            // if (isWhatsappCommunicationForNudgesSupported && doesUserBelongToWhatsappTrialExperiment) {
            //     notification = await LiveUtil.getLiveNotificationObject(userContext, this.hamletBusiness, this.cfAPIJavaService, this.logger, false)
            // }
            if (AppUtil.isWeb(userContext) || userContext.sessionInfo.clientVersion < 9.14) {
                // disable the whatsapp flag
                this.logger.info("calling API to disable nudges flag for userId: ", userContext.userProfile.userId)
                try {
                    const res = await this.cfAPIJavaService.disableNudgesFlagForFitness(userContext)
                } catch (error) {
                    this.logger.error("disabling nudges for fitness failed for userId  " + userContext.userProfile.userId + " with error: ", error)
                }
            }
            return this.catalogueService.getProduct(orderProduct.productId).then(async product => {
                const user = await this.userService.getUser(userId)
                const result = AppUtil.getUserAlertInfo(user, userContext)
                const tz = userContext.userProfile.timezone
                const code: string = result.code
                if (!_.isEmpty(code)) {
                    throw this.errorFactory.withCode(result.code, result.alertInfo.statusCode).build()
                }
                if (product.productType === "CF_LIVE" && product.isPack) {
                    // Temp fix to handle the case where device is passing past date  as start date
                    if (orderCreate.products[0].option.startDate < TimeUtil.todaysDateWithTimezone(tz)) {
                        orderCreate.products[0].option.startDate = TimeUtil.todaysDateWithTimezone(tz)
                        checkoutRequest.orderProducts[0].option.startDate = TimeUtil.todaysDateWithTimezone(tz)
                    }
                }
                const checkoutResponse: OrderCheckoutResponse = await this.omsApiClient.checkoutOrder(checkoutRequest)
                const order = checkoutResponse.order
                this.logger.info("Fulfilment started ", {orderId: order.orderId})
                const source = order.source === "CUREFIT_APP" || order.source === "CUREFIT_WEBSITE" ? "APP" : "WEBHOOK"
                const res = await this.omsApiClient.createFulfillment({
                    orderId: order.orderId,
                    initiationSource: source
                })
                this.logger.info("Fulfilment results ", {res, orderId: order.orderId})
                if (res.result !== "success") {
                    throw this.errorFactory.withCode("ERR_FULFILMENT_FAILURE", HTTP_CODE.INTERNAL_SERVER_ERROR).build()
                }
                const successOrder = await this.omsApiClient.getOrder(order.orderId)
                this.logger.info("Fulfilment successOrder ", {successOrder, orderId: order.orderId})
                return { ...successOrder, notification: notification }
            })
        }

        @httpGet("/livemembershippage/:membershipId")
        async getMemberShipDetail(req: express.Request): Promise<ProductDetailPage> {
            const membershipId: number = req.params.membershipId
            const userContext: UserContext = req.userContext as UserContext
            // const liveClassId: string = req.query.liveClassId
            return this.liveMembershipViewBuilder.buildView(userContext, membershipId)
        }

        @httpGet("/report/signedUrl")
        async getReportPictureUploadSignedUrl(req: express.Request): Promise<SignedUrlResponse> {
            const fileName: string = req.query.fileName as string
            const bucketName = (process.env.ENVIRONMENT === "STAGE" || process.env.ENVIRONMENT === "LOCAL") ? "cult-api-fitness-report-pictures-stage" : "cult-api-fitness-report-pictures-prod"
            const appVersion: number = Number(req.headers["appversion"])
            if (appVersion >= MEDIA_GATEWAY_REPORT_SUPPORT) return this.mediaGatewayClient.getPresignedPutUrl({
                bucketName,
                path: "",
                fileName,
                contentType: MediaTypeMediaGateway.IMAGE,
                maxUploadSize: 8097152, // need to be decided
                objectAcl: ObjectAcl.PUBLIC_READ
            })
            else return this.userBusiness.getImageSignedUrl(fileName, bucketName)
        }

        @httpGet("/debugger/url")
        async getDebugFileUploadUrl(req: express.Request) {
            const sessionId = req.query.sessionId
            const extension = req.query.extension
            const userContext = req.userContext as UserContext
            const userId = userContext.userProfile.userId

            if (!sessionId) throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("parameter missing: sessionId").build()
            if (!extension) throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("parameter missing: extension").build()

            const date = new Date().toISOString()
            const path = `DEBUGGER/${userId}_${date.substring(0, 10)}_${sessionId}_${date.substring(11, 19)}.${extension}`
            const url = await this.s3Helper.getSignedUrlWithPutObject({ Bucket: "cf-app-upload", Key: path })
            return { url, path }
        }

        @httpGet("/leagues/tabbedPage")
        async getSocialLeagueTabbedPage(req: express.Request) {
            const userContext: UserContext = req.userContext as UserContext
            if (!userContext?.sessionInfo?.isUserLoggedIn) {
                const user = await this.userCache.getUser(userContext.userProfile.userId)
                const result = AppUtil.getUserAlertInfo(user, userContext)
                throw this.errorFactory.withCode(ErrorCodes.NOT_LOGGED_IN_ERR, result.alertInfo.statusCode).build()
            }

            if (!AppUtil.isSocialLeaguesSupported(userContext, req.query.productType)) {
                const osName = userContext.sessionInfo.osName.toLowerCase()
                let errorCode
                if (osName === "android") {
                    errorCode = ErrorCodes.APP_VERSION_NOT_SUPPORTED_ANDROID_ERR
                } else if (osName === "ios") {
                    errorCode = ErrorCodes.APP_VERSION_NOT_SUPPORTED_IOS_ERR
                }
                throw this.errorFactory.withCode(errorCode, HTTP_CODE.FORBIDDEN).build()
            }
            const selectedTab = req.query.selectedTab
            const tabs = [{ key: "WALL", title: "Wall" }, { key: "ALL_LEAGUES", title: "All Squads" }]
            let initialIndex = 0
            if (selectedTab) {
                const selectedTabIndex = tabs.findIndex((tab) => tab.key === selectedTab)
                if (selectedTabIndex > -1) {
                    initialIndex = selectedTabIndex
                }
            }
            return {
                tabs: [{ key: "WALL", title: "Wall" }, { key: "ALL_LEAGUES", title: "All Squads" }],
                initialIndex
            }
        }

        @httpGet("/leagues/tabPage")
        async getSocialLeagueTabPage(req: express.Request) {
            const userContext: UserContext = req.userContext as UserContext
            const id = req.query.id
            const productType: ProductType = req.query.productType
            return await this.digitalSocialLeagueTabPageViewBuilder.buildView(userContext, id, productType)
        }

        @httpPost("/leagues/allLeaguesSections")
        async getAllLeaguesSections(req: express.Request) {
            const userContext: UserContext = req.userContext as UserContext
            const source = req.body.callParams?.[0]?.source
            const productType = req.body.callParams?.[0]?.productType
            const { userCache, socialService, digitalLeagueSectionViewBuilder, digitalSocialLeagueTabPageViewBuilder, logger, challengeCache, riddlerCacheService, riddlerService, hamletBusiness } = this
            if (source === LeagueSectionSource.CLP) {// dont return any call params
                return await LiveUtil.getClpRequestStatusSectionListWidgetView(userContext, { userCache, socialService, digitalLeagueSectionViewBuilder, digitalSocialLeagueTabPageViewBuilder, logger, challengeCache, riddlerCacheService, riddlerService }, false)
            } else if (source === LeagueSectionSource.CREATE_SQUAD_MODAL) {
                return await LiveUtil.getCreateSquadView(userContext, userCache, digitalLeagueSectionViewBuilder, socialService, logger, LeagueSectionSource.CREATE_SQUAD_MODAL) || {}
            }
            return await this.digitalLeagueSectionViewBuilder.buildView(userContext, req.body)
        }

        @httpPost("/leagues/inviteByContactModal")
        async getInviteByContactModal(req: express.Request) {
            const userContext: UserContext = req.userContext as UserContext
            const userId = userContext.userProfile.userId
            const communityId = Number(req.body.communityId)
            const hasContactsAccess = Boolean(req.body.hasContactsAccess)
            const currentUserIsCreator = Boolean(req.body.currentUserIsCreator)
            const modalCallParams = req.body // returning the same callParams for now
            const [{ obj: membersMappingEntryResponse }, { obj: pendingInvitesResponse }] = await Promise.all([
                eternalPromise(this.socialService.getCommunityMembers(communityId, userContext.userProfile.userId, MEMBERS_INITIAL_LIMIT, 0)),
                eternalPromise(this.socialService.getCommunityPendingInvites(communityId, userId, PENDING_INVITES_LIMIT, 0))
            ])
            const membersLimitReached = LiveUtil.getLeagueInviteLimit(membersMappingEntryResponse.length, pendingInvitesResponse.length) <= 0
            if (!membersMappingEntryResponse || !pendingInvitesResponse) {
                this.logger.info("InviteByContactModal null response", { userId, communityId, membersMappingEntryResponse, pendingInvitesResponse })
            } else {
                this.logger.info("InviteByContactModal count", { count: membersMappingEntryResponse.length + pendingInvitesResponse.length, communityId, membersLimitReached, maxMembersAlLowed: LEAGUE_LIST_MEMBER_LIMIT })
            }

            const isContactsSyncSupported = await LiveUtil.isContactsSyncSupportedAndSquadExists(userContext, this.socialService, currentUserIsCreator && communityId) && currentUserIsCreator

            let subtitle
            if (membersLimitReached) {
                subtitle = "Unable to add more members to squad"
            } else if (isContactsSyncSupported && !hasContactsAccess) {
                subtitle = "Enable access to connect with friends instantly"
            }

            let action: Action
            if (isContactsSyncSupported) {
                action = {
                    actionType: "NAVIGATION",
                    url: "curefit://leagueinvitepage?title=Invite"
                }
            } else {
                action = {
                    actionType: "REST_API",
                    meta: {
                        method: "post",
                        url: "/digital/leagues/inviteUser",
                        body: {
                            communityId,
                        }
                    },
                    openContactsPicker: true
                }
            }
            /* let fallbackAction: Action
            if (isContactsSyncSupported) {
                fallbackAction = {
                    actionType: "REST_API",
                    openContactsPicker: true,
                    meta: {
                        method: "post",
                        url: "/digital/leagues/inviteUser",
                        body: {
                            communityId,
                        },
                    }
                }
            } */
            return {
                ctaAction: currentUserIsCreator ? {
                    disabled: membersLimitReached,
                    shouldCloseModal: isContactsSyncSupported,
                    title: `ADD FROM CONTACTS`,
                    subtitle,
                    shouldRefreshTabPage: true,
                    shouldRefreshModal: true,
                    checkContactsPermission: true,
                    // fallbackAction,
                    ...action
                } : undefined,
                modalCallParams
            }
        }


        @httpPost("/leagues/create")
        async createLeague(req: express.Request) {
            const userContext: UserContext = req.userContext as UserContext
            const userId = userContext.userProfile.userId
            const user = await this.userCache.getUser(userId)
            const communityName = `${user.firstName}'s Squad`
            const communityEntry: CommunityEntry = {
                name: `${user.firstName}'s Squad`,
                description: `${user.firstName}'s Squad`,
                type: CommunityType.LEAGUE,
                tenant: Tenant.LIVE,
                creatorNode: {
                    entityId: userId,
                    entityType: EntityType.USER
                }
            }
            try {
                const communityResponse = await this.socialService.createCommunity(communityEntry)
                return {
                    performPostCallActions: true,
                    action: DigitalLeagueSectionViewBuilder.getShowLeagueAddContactModalAction("MANAGE", "Your Squad", true, LeagueSectionSource.MODAL, communityResponse.id, Colors.reddishPinkFour)
                }
            }
            catch (e) {
                this.rollbarService.sendError(e)
                throw this.errorFactory.withCode(ErrorCodes.SQUAD_NOT_CREATED_ERR, 400).build()
            }
        }

        @httpPost("/leagues/setInviteState")
        async setInviteState(req: express.Request) {
            const userContext: UserContext = req.userContext
            const userId = userContext.userProfile.userId
            const { inviteState, mappingEntryId, targetUserId, inviteType = CommunityInviteType.CF_USER } = req.body
            const mappingResponse = await eternalPromise(this.socialService.updateCommunityUserMappingStatusV2(Number(mappingEntryId), inviteState, userId, inviteType))
            if (mappingResponse?.err) {
                this.logger.info(`Invite status change failed for ${JSON.stringify({ userId, mappingEntryId, inviteState, inviteType })}, err: ${mappingResponse.err}`)
            }
            const toastMessage = LiveUtil.getMappingStateToastMessages(inviteState, mappingResponse.obj?.success, userId, targetUserId)
            return {
                isSuccess: mappingResponse.obj?.success,
                performPostCallActions: true,
                action: {
                    actionType: "SHOW_TOAST_MESSAGE",
                    meta: {
                        message: toastMessage,
                        duration: "SHORT",
                        position: "TOP"
                    }
                }
            }
        }

        @httpPost("/leagues/inviteUser")
        async inviteUser(req: express.Request) {
            const userContext: UserContext = req.userContext
            const userId = userContext.userProfile.userId
            const communityId = Number(req.body.communityId)
            const { countryCallingCode, phoneNumber, name, inviteeUserId }: { countryCallingCode: string, phoneNumber: string, name: string, inviteeUserId: string } = req.body
            let toastMessage
            if (countryCallingCode && countryCallingCode !== "91") {
                toastMessage = LiveUtil.getMappingStateToastMessages("INTERNATIONAL_PENDING")
            } else {
                const communityUserRequest: CommunityUserInviteRequest = {
                    communityId,
                    countryCallingCode: countryCallingCode ? `+${countryCallingCode}` : undefined,
                    phoneNumber,
                    name,
                    userId: inviteeUserId,
                }
                const inviteResponse = await eternalPromise(this.socialService.inviteUserToCommunity(communityUserRequest, userId))
                toastMessage = LiveUtil.getMappingStateToastMessages("PENDING", inviteResponse?.obj?.success, userId, inviteeUserId)
            }
            return {
                performPostCallActions: true,
                action: {
                    actionType: "SHOW_TOAST_MESSAGE",
                    meta: {
                        message: toastMessage,
                        duration: "SHORT",
                        position: "TOP"
                    }
                }
            }
        }

        @httpPost("/leagues/inviteUsersBulk")
        async inviteUsersBulk(req: express.Request) {
            const userContext: UserContext = req.userContext
            const userId = userContext.userProfile.userId
            const communityId = Number(req.body.communityId)
            const { inviteeInfo }: { inviteeInfo: InviteInfo[] } = req.body
            let hasInternationalContact = false
            const inviteRequests: CommunityUserInviteRequest[] = _.compact(inviteeInfo.map(info => {
                if (!info.userId && info.countryCallingCode !== "91") {
                    hasInternationalContact = true
                }
                if ((!info.userId && !info.phoneNumber) || info.countryCallingCode && info.countryCallingCode !== "91") {
                    return undefined
                }
                return LiveUtil.createCommunityUserInviteRequest(communityId, info.userId, info.countryCallingCode, info.phoneNumber, info.name)
            }))
            let someInvitesFailed
            for (const request of inviteRequests) {
                try {
                    const inviteResponse = await (this.socialService.inviteUserToCommunity(request, userId))
                    if (!inviteResponse?.success) {
                        someInvitesFailed = true
                    }
                }
                catch (e) {
                    someInvitesFailed = true
                    this.logger.info(`Leagues bulk invite failed for userId:${userId}, communityId: ${communityId}`)
                }
            }
            let toastMessage: string
            if (hasInternationalContact) {
                toastMessage = LiveUtil.getMappingStateToastMessages("INTERNATIONAL_PENDING")
            }
            else if (someInvitesFailed) {
                toastMessage = "One or more invites failed"
            } else {
                toastMessage = "Successfully invited"
            }
            return {
                performPostCallActions: true,
                action: {
                    actionType: "SHOW_TOAST_MESSAGE",
                    meta: {
                        message: toastMessage,
                        duration: "SHORT",
                        position: "TOP"
                    }
                },
                closePage: true,
                customAction: DigitalLeagueSectionViewBuilder.getShowLeagueAddContactModalAction("MANAGE", "Your Squad", true, LeagueSectionSource.MODAL, communityId)
            }
        }

        @httpPost("/live/exit")
        async fetchExitAction(req: express.Request) {
            const userContext: UserContext = req.userContext
            const userId = userContext.userProfile.userId
            const sessionData = req.session.sessionData
            const { contentId, isManualExit, isDeviceSupported, isCameraDisabled, userSessionId, contentType, liveClassId, lastPlayBackOffset }: { contentId: string, isManualExit: boolean, isDeviceSupported: boolean, isCameraDisabled: boolean, userSessionId: string, contentType: string, liveClassId: string, lastPlayBackOffset: number } = req.body || {}

            const isDIYContent: boolean = contentType === "DIY_MEDITATION" || contentType === "DIY_FITNESS"

            if (AppUtil.isSugarFitOrUltraFitApp(userContext)) return {}
            const [segmentsObj, membershipObj, userScoreMetricObj, videoResponseObj, diyVideoResponseObj] = await Promise.all([
                eternalPromise(this.clsUtil.getPlatformSegments()),
                eternalPromise(this.diyFulfilmentService.getMembershipDetails(userId, AppUtil.getTenantFromUserContext(userContext))),
                (isDIYContent && !_.isEmpty(userSessionId)) ? eternalPromise(this.diyFulfilmentService.getUserScoreMetricsV2(userSessionId, <SessionContentType>contentType)) : eternalPromise(this.diyFulfilmentService.getUserScoreMetrics(contentId, userContext.userProfile.userId)),
                isDIYContent ? undefined : eternalPromise(this.diyFulfilmentService.getDigitalCatalogueEntry(contentId), `getDigitalCatalogueEntry failed in getReportStatus, contentId: ${contentId}, userId: ${userId}`),
                isDIYContent ? eternalPromise(this.diyFulfilmentService.getDIYFitnessProductsByProductIds(userId, [contentId], AppUtil.getTenantFromUserContext(userContext))) : undefined,
                AppUtil.isDiyEnergyMeterSupported(userContext, this.hamletBusiness) ? eternalPromise(this.consumeOnExit(req, userContext)) : undefined
            ])
            const userScoreMetrics = userScoreMetricObj?.obj
            const videoResponse = videoResponseObj?.obj
            const diyVideoResponse = diyVideoResponseObj?.obj?.[0]

            const isSessionIncomplete = userScoreMetrics?.reportStatus === SessionReportStatus.INCOMPLETE
            if (!isSessionIncomplete) {
                this.customEventEmitter.emitEvent(eventName.VIDEO_COMPLETION, userId, sessionData?.attributionSource)
            }
            const isSessionComplete = userScoreMetrics?.reportStatus === SessionReportStatus.COMPLETE

            const weeklyScreenUrl: any = null

            if (LiveUtil.isInteractiveSession(videoResponse?.preferredStreamType)) {
                // marking the class exit by the user
                await LiveUtil.updatePlaybackMetricsSynthetically(userContext, contentId, videoResponse?.preferredStreamType, req, this.diyFulfilmentService, this.userCache)
                const feedbackId = LiveUtil.getLiveVideoCallFeedbackId(contentId, userId)
                const nextUrl = `curefit://feedback?feedbackId=${feedbackId}&itemId=${contentId}&endRoute=POP_ACTION`
                if (weeklyScreenUrl != null && isSessionComplete) {
                    return LiveUtil.getExitActionForWeeklyScreen(weeklyScreenUrl, nextUrl)
                }
                return {
                    exitRoute: {
                        targetRoute: PageTypes.FeedBack,
                        params: {
                            queryParams: {
                                feedbackId: feedbackId,
                                itemId: contentId,
                                endRoute: "POP_ACTION"
                            }
                        }
                    }
                }
            }
            if (AppUtil.isTVApp(userContext)) {
                if (contentType === "DIY_MEDITATION") {
                    return {}
                }
                return {
                    exitAction: {
                        actionType: "NAVIGATION",
                        url: `curefit://livesessionreport?contentId=${contentId}&prevPage=videoplayer&userSessionId=${userSessionId}&contentType=${contentType}`
                    }
                }
            }

            const segments = segmentsObj?.obj
            const isFreeTrial = (!_.isEmpty(segments) && (segments.includes("cflive-free-trial-started")))
            const isIntentionalPlayback = userScoreMetrics?.playbackMillis > 2 * 60 * 1000
            const membership = membershipObj?.obj
            const isComplimentaryMembership = (!_.isEmpty(membership) && (membership.productId === "CFLIVE_COMPLIMENTARY_001"))

            const isUserPartOfFeedbackUserFormExperiment = await AppUtil.doesUserBelongsToUserFormFeedbackABExperiment(userContext, this.hamletBusiness, this.logger, this.rollbarService)
            this.logger.info(`fetchExitAction, userId:${userId}, isSessionIncomplete ${isSessionIncomplete}, isUserPartOfFeedbackUserFormExperiment;${isUserPartOfFeedbackUserFormExperiment}`)

            if (isSessionComplete && isUserPartOfFeedbackUserFormExperiment) {
                // TODO samarth: flutter form not supported currently from small wins flow
                // if (weeklyScreenUrl != null && isUserPartOfSmallWinsExp) {
                //     const nextUrl = `curefit://fl_form?formId=${FEEDBACK_USER_FORM_AB_TEST_USER_FORM_ID}&contentId=${contentId}&isDeviceSupported=${!!isDeviceSupported}&isCameraDisabled=${!!isCameraDisabled}`
                //     return LiveUtil.getExitActionForWeeklyScreen(weeklyScreenUrl, nextUrl, true)
                // }
                return {
                    exitAction: {
                        actionType: "NAVIGATION",
                        url: `curefit://fl_form?formId=${FEEDBACK_USER_FORM_AB_TEST_USER_FORM_ID}&contentId=${contentId}&isDeviceSupported=${!!isDeviceSupported}&isCameraDisabled=${!!isCameraDisabled}`
                    }
                }
            }

            const isUserFormEnabled = isManualExit && (isFreeTrial || isComplimentaryMembership) && isIntentionalPlayback && isSessionIncomplete
            if (false) { // disabled: isUserFormEnabled
                return {
                    exitAction: {
                        actionType: "NAVIGATION",
                        url: `curefit://userform?formId=NPS_LIVEX&contentId=${contentId}&isDeviceSupported=${!!isDeviceSupported}&isCameraDisabled=${!!isCameraDisabled}`
                    }
                }
            }

            const isAllowedFormat = videoResponse?.format && poseOfDaySupportedFormats.includes(videoResponse.format)
            const hasCompletedClass = AppUtil.isRelaxingMomentAndReportSupported(userContext) || userScoreMetrics?.reportStatus === SessionReportStatus.COMPLETE
            const isPostCreationAllowed = (await eternalPromise(this.socialService.isPostCreationAllowed(contentId, Tenant.LIVE, Number(userId)))).obj
            const isMomentOfTheDayEnabled = hasCompletedClass && isPostCreationAllowed && isAllowedFormat
            if (false) { // disabled: isMomentOfTheDayEnabled
                this.logger.info(`fetchExitAction contentId: ${contentId}, userId: ${userId}, isPostCreationAllowed: ${isPostCreationAllowed}, hasCompletedClass: ${hasCompletedClass}, isAllowedFormat: ${isAllowedFormat}`)
                return {
                    exitRoute: {
                        isLandscape: true,
                        targetRoute: PageTypes.LiveMomentOfTheDayPage,
                        params: {
                            queryParams: {
                                contentId
                            }
                        }
                    }
                }
            }

            const feedbackId = contentType === "DIY_FITNESS" ? userSessionId : AppUtil.getLiveFeedbackId(userContext, contentId)
            if (await this.isImmediateFeedbackAvailable(userContext, feedbackId)) {
                if (weeklyScreenUrl != null && isSessionComplete) {
                    const nextUrl = `curefit://feedback?feedbackId=${feedbackId}&endRoute=livesessionreport`
                    return LiveUtil.getExitActionForWeeklyScreen(weeklyScreenUrl, nextUrl)
                }
                return {
                    exitRoute: {
                        targetRoute: PageTypes.FeedBack,
                        params: {
                            queryParams: {
                                feedbackId,
                                endRoute: PageTypes.LiveSessionReport,
                            }
                        }
                    }
                }
            }


            const finalPlaybackOffset = _.isFinite(lastPlayBackOffset) ? lastPlayBackOffset : (_.isFinite(userScoreMetrics?.lastPlayBackOffset) ? userScoreMetrics?.lastPlayBackOffset : undefined)

            if (videoResponse?.format !== "EAT") {
                let sessionEnded = false
                if (!isDIYContent) {
                    sessionEnded = videoResponse?.scheduledTimeEpoch + videoResponse?.duration < Date.now()
                } else if (diyVideoResponse?.duration && finalPlaybackOffset) {
                    sessionEnded = Math.abs(diyVideoResponse.duration - finalPlaybackOffset) < 2 * 60000
                }
                if (isSessionIncomplete && !sessionEnded) {
                    // const isInternalUser = (await userContext.userPromise).isInternalUser
                    const { sessionInfo } = userContext
                    // Not enabling journey interstitial for international app
                    if (sessionInfo.userAgent === "APP" && !AppUtil.isAppVersionBelow(userContext, 100, USER_JOURNEY_SUPPORTED_VERSION)) {
                        return {
                            exitAction: {
                                actionType: "SHOW_DIGITAL_JOURNEY_RETENTION_INTERSTITIAL",
                                payload: {
                                    classId: contentId,
                                    liveClassId: videoResponse?.originalContentId ? videoResponse?.originalContentId : contentId,
                                    contentType,
                                    userSessionId,
                                    duration: userScoreMetrics?.playbackMillis
                                }
                            }
                        }
                    }
                }
                if (weeklyScreenUrl != null && isSessionComplete) {
                    const nextUrl = `curefit://livesessionreport?contentId=${contentId}&prevPage=videoplayer&userSessionId=${userSessionId}&contentType=${contentType}`
                    return LiveUtil.getExitActionForWeeklyScreen(weeklyScreenUrl, nextUrl)
                }
                return {
                    exitAction: {
                        actionType: "NAVIGATION",
                        url: `curefit://livesessionreport?contentId=${contentId}&prevPage=videoplayer&userSessionId=${userSessionId}&contentType=${contentType}`
                    }
                }
            }

            return {} // App will use local fallback route
        }

        private consumeOnExit(req: express.Request, userContext: UserContext) {
            const payload = req.body
            const userId = userContext.userProfile.userId
            const { userSessionId, contentId, contentType } = payload
            const tz = userContext.userProfile.timezone
            return this.diyFulfilmentService.endDIYSession(userSessionId, contentId, userId, contentType, tz)
        }

        @httpGet("/trial/interstitial")
        async fetchTrialInterstial(req: express.Request): Promise<any> {
            const userContext: UserContext = req.userContext
            const trialPage = (await this.vmPageBuilder.getPage("CurefitLiveTrialInterstitial", userContext, {})) as ListPage
            // let notification: any = undefined
            // const whatsappTrialExperimentId = process.env.ENVIRONMENT === "STAGE" ? "415" : "780"
            // const unifiedCLPExperimentId = process.env.ENVIRONMENT === "STAGE" ? "415" : "772"
            // const hamletExperimentMap = await this.hamletBusiness.getUserAllocations(AppUtil.getHamletContext(userContext, [whatsappTrialExperimentId, unifiedCLPExperimentId]))
            // const userAssignment = hamletExperimentMap ? hamletExperimentMap.assignmentsMap[whatsappTrialExperimentId] : undefined
            // const doesUserBelongToWhatsappTrialExperiment = userAssignment ? userAssignment.bucket.bucketId === "1" : process.env.ENVIRONMENT === "STAGE"

            // const userAssignmentUnifiedCLP = hamletExperimentMap ? hamletExperimentMap.assignmentsMap[unifiedCLPExperimentId] : undefined
            // const doesUserBelongToUnifiedCLPExperiment = userAssignmentUnifiedCLP ? userAssignmentUnifiedCLP.bucket.bucketId === "2" : process.env.ENVIRONMENT === "STAGE"
            // if (AppUtil.isWhatsappCommunicationForNudgesSupported(userContext) && doesUserBelongToWhatsappTrialExperiment && !doesUserBelongToUnifiedCLPExperiment) {
            //     notification = await LiveUtil.getLiveNotificationObject(userContext, this.hamletBusiness, this.cfAPIJavaService, this.logger, false)
            // }
            const liveTrialInterstitialPage: LiveTrialInterstitialPage = {
                widgets: trialPage.body,
                pageAction: trialPage.actions[0],
                titleUrl: LiveUtil.getCultLiveTitleImage(req.userContext),
                notification: undefined
            }
            return liveTrialInterstitialPage
        }

        @httpGet("/journey/interstitial")
        async fetchJourneyInterstial(req: express.Request): Promise<any> {
            const userContext: UserContext = req.userContext
            const journeyPage = (await this.vmPageBuilder.getPage("CurefitLiveJourneyInterstitial", userContext, req.query, req)) as ListPage
            const liveJourneyInterstitialPage: LiveJourneyInterstitialPage = {
                widgets: journeyPage.body,
                // pageAction: journeyPage.actions[0],
                titleUrl: LiveUtil.getCultLiveTitleImage(userContext)
            }
            return liveJourneyInterstitialPage
        }

        @httpPost("/pack/diyfitness")
        @httpPost("/sessions")
        async getSessionListing(req: express.Request) {
            const session: Session = req.session
            const userContext = req.userContext as UserContext
            const { packId, sessionFilters, trainerId, formatId } = req.body
            const appVersion: number = Number(req.headers["appversion"])
            const nodeRelationId = req.query.nodeRelationID
            const userId = session.userId
            const tenant = AppUtil.getTenantFromUserContext(userContext)

            const userPromise = this.userCache.getUser(session.userId)
            const daysRemainingWidgetPromise = new DaysRemainingWidgetView().buildView(this.serviceInterfaces, req.userContext, { source: "sessionlistingpage" })
            const ip = req.ip || req.socket.remoteAddress

            const userHasActiveMembershiopPromise = LiveUtil.getActiveMembershipDates(userContext, this.serviceInterfaces.diyService, this.serviceInterfaces.catalogueService)
            const checkIfMonetisationEnabledAndEligibleForTrialPromise = LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(this.cultBusiness, this.diyFulfilmentService, userContext)
            const isDiyEnergyMeterSupportedPromise = AppUtil.isDiyEnergyMeterSupported(userContext, this.hamletBusiness)
            const blockInternationalUser = await AppUtil.blockFitnessContentForInternationalUser(userContext, this.maxmindService, this.logger)

            if (!_.isNil(trainerId) || !_.isNil(formatId)) {
                const products = await this.diyFulfilmentService.searchDIYProducts(userId, AppUtil.getTenantFromUserContext(userContext), AppUtil.getCountryId(userContext), {
                    trainerId,
                    format: formatId
                })

                const productMap = LivePackUtil.getProductMap(products)

                const [daysRemainingWidget, user, userHasActiveMembership, { isUserEligibleForMonetisation, isUserEligibleForTrial },
                    isDiyEnergyMeterSupported
                ] = await Promise.all([
                    daysRemainingWidgetPromise, userPromise, userHasActiveMembershiopPromise, checkIfMonetisationEnabledAndEligibleForTrialPromise,
                    isDiyEnergyMeterSupportedPromise
                ])

                const diyEnergyMeterSupportedMap = LivePackUtil.getDiyEnergyMeterSupportedMap(<DIYFitnessProductExtended[]>products, ["ENERGY", "SCORE"], isDiyEnergyMeterSupported, false)

                return this.digitalSessionListViewBuilder.buildView(appVersion, null, productMap, null, null, blockInternationalUser, daysRemainingWidget, userContext, user.isInternalUser, isUserEligibleForTrial, isUserEligibleForMonetisation, userHasActiveMembership, diyEnergyMeterSupportedMap, sessionFilters, req.body)
            }

            let diyPackPromise: Promise<DIYUserFitnessPack>
            diyPackPromise = this.packService.getFitnessDIYPackV2(packId, userId)

            let addDIYSocialNodeRelationPromise
            if (nodeRelationId) {
                const productType = "DIY_FITNESS_PACK"
                const nodeType = DIYSocialNodeType.PACK
                addDIYSocialNodeRelationPromise = this.diyFulfilmentService.addDIYSocialNodeRelation(userId, productType, nodeType, packId, nodeRelationId)
            }
            const isLegacyVideoBackgroundSupportedPromise = AppUtil.isLiveVideoPlayerBackgroundSupported(userContext, this.hamletBusiness)
            const [diyPack] = await Promise.all([diyPackPromise, addDIYSocialNodeRelationPromise])

            this.logger.debug(`Cult DIY Pack details called for user id: ${session.userId}; nodeRelationId: ${nodeRelationId}; packId: ${packId}`)

            const productsV2 = await this.diyFulfilmentService.getProductsForPack(packId, userId, tenant)
            const isOldDIYConstruct = (diyPack.pack.sessionIds?.length > 0)

            if ( !isOldDIYConstruct && productsV2.products ) {
                diyPack.pack.sessionIds = LivePackUtil.getProductV2DIYSessionIds(productsV2.products)
            }

            let productsPromise = Promise.resolve(<DIYFitnessProductExtended[]>[])
            if (diyPack.pack.sessionIds?.length > 0) {
                productsPromise = this.diyFulfilmentService.getDIYFitnessProductsByProductIds(session.userId, diyPack.pack.sessionIds, AppUtil.getTenantFromUserContext(userContext))
            }
            const socialDataForPackPromise = this.diyFulfilmentService.getDIYSocialAssociatedParentNodes(session.userId, "DIY_FITNESS_PACK", DIYSocialNodeType.PACK, [packId])
            let socialDataForSessionsPromise
            if (isOldDIYConstruct) {
                socialDataForSessionsPromise = this.diyFulfilmentService.getDIYSocialAssociatedParentNodes(session.userId, "DIY_FITNESS", DIYSocialNodeType.SESSION, diyPack.pack.sessionIds)
            }
            const [socialDataForPack, socialDataForSessions] = await Promise.all([socialDataForPackPromise, socialDataForSessionsPromise])

            let packRecommendedByBuddiesPromise: Promise<CultBuddiesJoiningListSmallView>
            if (socialDataForPack && socialDataForPack[packId] && !_.isEmpty(socialDataForPack[packId].userIds)) {
                const attendingUsers: { userId: string }[] = socialDataForPack[packId].userIds.map(userId => { return { userId } })
                packRecommendedByBuddiesPromise = LiveUtil.getBuddiesJoiningListSmallView(attendingUsers, this.userCache, PageTypes.CultDIYPack, true, "shared this")
            }
            const packDetail = diyPack.pack.productType === "DIY_FITNESS_PACK" ? (await this.diyFulfilmentService.getDIYFitnessPacksForIds(session.userId, [diyPack.pack.productId]))[0] :
                (await this.diyFulfilmentService.getDIYMeditationPacksForIds(session.userId, [diyPack.pack.productId]))[0]
            const sessionRecommendedByBuddiesMap: { [sessionId: string]: CultBuddiesJoiningListSmallView } = {}
            const settingSessionRecommendedByBuddiesMapPromise = Promise.all(diyPack.pack.sessionIds.map(async (sessionId) => {
                if (!socialDataForSessions || !socialDataForSessions[sessionId] || _.isEmpty(socialDataForSessions[sessionId].userIds)) return undefined
                const attendingUsers: { userId: string }[] = socialDataForSessions[sessionId].userIds.map(userId => { return { userId } })
                sessionRecommendedByBuddiesMap[sessionId] = await LiveUtil.getBuddiesJoiningListSmallView(attendingUsers, this.userCache, PageTypes.CultDIYPack, true, "shared this", 2)
            }))
            const [daysRemainingWidget, products, user, packRecommendedByBuddies, userHasActiveMembership, { isUserEligibleForMonetisation, isUserEligibleForTrial },
                isDiyEnergyMeterSupported, isLegacyVideoBackgroundSupported
            ] = await Promise.all([
                daysRemainingWidgetPromise, productsPromise, userPromise, packRecommendedByBuddiesPromise, userHasActiveMembershiopPromise, checkIfMonetisationEnabledAndEligibleForTrialPromise,
                isDiyEnergyMeterSupportedPromise, isLegacyVideoBackgroundSupportedPromise, settingSessionRecommendedByBuddiesMapPromise
            ])
            const diyEnergyMeterSupportedMap = LivePackUtil.getDiyEnergyMeterSupportedMap(products, ["ENERGY", "SCORE"], isDiyEnergyMeterSupported, isLegacyVideoBackgroundSupported)

            const productMap = LivePackUtil.getProductMap(products)

            return this.digitalSessionListViewBuilder.buildView(appVersion, diyPack, productMap, packRecommendedByBuddies, sessionRecommendedByBuddiesMap, blockInternationalUser, daysRemainingWidget, userContext, user.isInternalUser, isUserEligibleForTrial, isUserEligibleForMonetisation, userHasActiveMembership, diyEnergyMeterSupportedMap, sessionFilters)
        }

        @httpPost("/leagues/challenge")
        async getLeaguesChallengePage(req: express.Request) {
            const userContext: UserContext = req.userContext
            const { challengeId } = req.body
            return await this.digitalLeagueChallengeViewBuilder.buildView(userContext, challengeId)
        }

        @httpPost("/leagues/joinSquadChallenge")
        async joinSquadChallenge(req: express.Request) {
            const userContext: UserContext = req.userContext
            const userId = userContext.userProfile.userId
            const { challengeId } = req.body
            const enrollmentRequest: CommunityChallengeEnrollmentRequest = {
                challengeId,
                communityType: CommunityType.LEAGUE,
                userId
            }
            const { obj: enrollmentResponse } = await eternalPromise(this.socialService.enrollUserToChallenge(enrollmentRequest))
            const enrolmentStatus = enrollmentResponse?.success ? "SUCCESS" : "FAILED"
            const customAction = enrollmentResponse?.success ? {
                actionType: "NAVIGATION",
                url: "curefit://leaguewalltabbedpage",
                popPage: true
            } : undefined
            return {
                performPostCallActions: true,
                toastMessage: SQUAD_CHALLENGE_ENROLMENT_TOAST_MESSAGES[enrolmentStatus],
                shouldCloseModal: enrolmentStatus === "SUCCESS",
                shouldRefreshSourcePage: true,
                customAction
            }
        }

        @httpGet("/leagues/leagueInvitePage")
        async getLeagueInvitePage(req: express.Request) {
            const userContext: UserContext = req.userContext
            const userId = userContext.userProfile.userId
            const communityId = await LiveUtil.getSelfCommunityId(userId, this.socialService)

            let inviteLimit = 0
            if (communityId) {
                const [invitesResponse, communityMembers] = await Promise.all([
                    this.socialService.getCommunityPendingInvites(communityId, userId, PENDING_INVITES_LIMIT, 0),
                    this.socialService.getCommunityMembers(communityId, userId, LEAGUE_LIST_MEMBER_LIMIT, 0)])

                inviteLimit = LiveUtil.getLeagueInviteLimit(communityMembers.length, invitesResponse.length)
            }

            return {
                contactsToFetchBatchSize: 10,
                inviteLimit,
                action: {
                    title: communityId ? "INVITE" : "SQUAD DOESN'T EXISTS",
                    subtitle: !communityId ? "Can't invite without a squad" : undefined,
                    selectionNotAllowed: "LIMIT REACHED",
                    disableOnZeroInvites: true,
                    shouldRefreshTabPage: true,
                    shouldRefreshModal: true,
                    shouldRefreshAllTabPages: true,
                    actionType: "REST_API",
                    meta: {
                        method: "post",
                        url: `/digital/leagues/inviteUsersBulk`,
                        body: {
                            communityId
                        },
                    }
                },
                localContactsBatchSize: 800
            }
        }

        @httpGet("/leagues/recommendedUsers")
        async getLeagueRecommendedUsers(req: express.Request): Promise<RecommendedContactsResponse> {
            const userContext: UserContext = req.userContext
            const userId = userContext.userProfile.userId
            const { offset = 0, limit = RECOMMENDED_USERS_LIMIT, communityId } = req.query
            const selfCommunityId = Number(communityId) ? Number(communityId) : await LiveUtil.getSelfCommunityId(userId, this.socialService)

            let data
            const nextQuery: { offset: number, batchSize: number, done: boolean, communityId: string } = {
                offset: Number(offset) + Number(limit),
                batchSize: Number(limit),
                done: true, // don't allow pagination(when enabled) if selfCommunityId is undefined
                communityId: String(selfCommunityId) // maybe "undefined" but since we are not supporting pagination it isn't used
            }
            if (selfCommunityId) {
                const [recommendedUserEntries, invitesResponse, communityMembers] = await Promise.all([
                    this.socialService.getCommunityRecommendationsV2(userId),
                    this.socialService.getCommunityPendingInvites(selfCommunityId, userId, PENDING_INVITES_LIMIT, 0),
                    this.socialService.getCommunityMembers(selfCommunityId, userId, LEAGUE_LIST_MEMBER_LIMIT, 0)])

                const inviteLimit = LiveUtil.getLeagueInviteLimit(communityMembers.length, invitesResponse.length)

                const invitedUserIdsMap = LiveUtil.createBooleanMapping(invitesResponse, "userId")
                const communityMembersMap = LiveUtil.createBooleanMapping(communityMembers, "userId")

                const userIdVsRecommendedUserEntries = _.keyBy(recommendedUserEntries, "userId")
                const filteredRecommendedUserIds = recommendedUserEntries.filter(entry => entry && !invitedUserIdsMap[entry.userId] && !communityMembersMap[entry.userId]).map(({ userId: id }) => id)

                const users = await this.userCache.getUsers(filteredRecommendedUserIds)
                data = Number(offset) === 0 && inviteLimit > 0 ? filteredRecommendedUserIds.map(id => {
                    const user = users[id]
                    const recommendationSource = userIdVsRecommendedUserEntries[id]?.recommendationSource
                    return {
                        imageUrl: user.profilePictureUrl || DUMMY_USER_IMAGE,
                        name: LiveUtil.getLeagueRecommendedUserName(user, recommendationSource === CommunityRecommendationSource.CONTACT_SYNC),
                        id: crypto.randomUUID(),
                        userId: id,
                    }
                }).filter(item => !_.isEmpty(item.name)) : []
            }

            return {
                sectionId: RECOMMENDED_CONTACTS_SECTION_ID,
                header: {
                    title: "Recommended: Active in cult.fit"
                },
                nextQuery,
                data
            }
        }

        @httpGet("/find/firstclass")
        async findYourFirstClass(req: express.Request): Promise<RecommendedFirstClassesViewBuilder> {
            const userContext = req.userContext as UserContext
            const userId = userContext.userProfile.userId
            await AppUtil.timeout(2000)
            const countryId = AppUtil.getCountryId(userContext)
            const expId = process.env.ENVIRONMENT === "STAGE" ? "415" : "523"
            const hamletExperimentMap = await this.hamletBusiness.getUserAllocations(AppUtil.getHamletContext(userContext, [expId]))
            const userAssignment = hamletExperimentMap ? hamletExperimentMap.assignmentsMap[expId] : undefined
            const doesUserBelongToWhatsappTrialExperiment = userAssignment ? userAssignment.bucket.bucketId === "1" : false
            let formId = req.query.formId || "LIVE_New_User_Onboarding"
            if (userContext.sessionInfo.clientVersion >= 8.94 && doesUserBelongToWhatsappTrialExperiment) {
                formId = "LIVE_New_User_Onboarding_Goal"
            }
            const userForm = await this.serviceInterfaces.formService.getLastCompletedUserForm(formId, userId)
            const form = await this.serviceInterfaces.formService.getFormPageResponse(formId)
            const qnaMap = form.screens.map(screen => {
                const map = new Map<string, string[]>()
                const answerDisplayTextMap = new Map<string, string>()
                screen.data[0].possibleAnswers.forEach((possibleAnswer) => {
                    // @ts-ignore
                    map.set(possibleAnswer.possibleAnswerId, possibleAnswer.meta.value)
                    answerDisplayTextMap.set(possibleAnswer.possibleAnswerId, possibleAnswer.displayText)
                })
                return {
                    screenDataId: screen.dataIds[0],
                    // @ts-ignore
                    questionType: screen.data[0].meta?.field,
                    answers: map,
                    answerDisplayTextMap: answerDisplayTextMap
                }
            })
            const filters: string[] = []
            const map: any = {}
            qnaMap.forEach((qna) => {
                    const responseField = qna["questionType"]
                    const answerId = userForm.screenDataResponse[qna["screenDataId"]].response[0].possibleAnswerId
                    const answer = qna.answers.get(answerId)
                    map[responseField] = answer
                    filters.push(qna.answerDisplayTextMap.get(answerId))
                }
            )
            this.logger.info("/find/firstclass formats: " + map["format"])
            this.logger.info("/find/firstclass intensity: " + map["intensity"])
            // const formats: LiveFitWorkoutFormat[] = ["DANCE", "STRENGTH", "CARDIO"]
            const allLiveClasses = await this.diyFulfilmentService.getUpcomingLiveClasses(userId, AppUtil.getTenantFromUserContext(userContext), map["format"], countryId)
            const liveClasses = LiveUtil.filterInteractiveSessionAndRealLive(allLiveClasses)
            const filteredClasses = liveClasses.filter((liveClass) => {
                if (map["intensity"].includes(liveClass.intensityLevel)) {
                    return true
                }
            })
            // const time = map["time"][0]
            return await this.recommendedFirstClassesViewBuilder.buildView(this.serviceInterfaces, filteredClasses, userContext, filters)
        }

        @httpGet("/series/browse")
        async getDIYSeries(req: express.Request) {
            const userContext: UserContext = req.userContext as UserContext
            const productType = req.query.productType
            const countryId = AppUtil.getCountryId(userContext)
            const diySeries: DIYSeries[] = await this.diyFulfilmentService.getDIYSeries(userContext.userProfile.userId, productType, countryId)
            return this.seriesViewBuilder.buildView(userContext, diySeries, productType)
        }

        @httpPost("/zoom/signature")
        async getZoomSignature(req: express.Request): Promise<object> {
            const timestamp = new Date().getTime() - 30000
            const ZOOM_API_KEY = "9mOo9UCnQBKVDLWvS5SE8g"
            const ZOOM_API_SECRET = "MNDB26hdTS74DQjjrmqnnX2McmrJM7HdvzMT"
            const { body } = req
            const msg = Buffer.from(
                ZOOM_API_KEY + body.meetingNumber + timestamp + body.role
            ).toString("base64")
            const hash = createHmac("sha256", ZOOM_API_SECRET)
                .update(msg)
                .digest("base64")
            const signature = Buffer.from(
                `${ZOOM_API_KEY}.${body.meetingNumber}.${timestamp}.${body.role}.${hash}`
            ).toString("base64")
            return {
                signature: signature
            }
        }

        @httpGet("/userOnboarding/recommendation")
        async userOnboarding(req: express.Request): Promise<object> {
            const userContext: UserContext = req.userContext as UserContext
            const userId: string = userContext.userProfile.userId
            const isRecomCreated = await this.diyFulfilmentService.createNewUserOnBoardingRecommendations(userId)
            this.logger.info("/digital/userOnboarding called for userId: " + userId)

            const response: {
                action: Action;
            } = {
                action : null,
            }

            if (isRecomCreated) {
                const recommendationResponse = await this.diyFulfilmentService.getNewUserOnBoardingRecommendations(userId)
                this.logger.info("Recommendation Flow: UserId: " + userId + ", createNewUserOnBoardingRecommendations: " + isRecomCreated + ", getNewUserOnBoardingRecommendations: " + recommendationResponse)

                if (recommendationResponse.fitnessPackId) {
                    response.action = {
                        actionType: "NAVIGATION",
                        url: "curefit://cultdiypack?packId=" + recommendationResponse.fitnessPackId,
                        title: "DiyPackPage"
                    }
                }
                else if (recommendationResponse.cyclopsPageId) {
                    response.action = {
                        actionType: "NAVIGATION",
                        url: "curefit://listpage?pageId=" + recommendationResponse.cyclopsPageId + "&pageHook=programpage",
                        title: "CyclopsPage"
                    }
                }
            }

            if (response.action === null) {
                this.logger.error("Null recommendation action")
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("Null recommendation action for userId " + userId).build()
            }

            return response
        }

        @httpGet("/userOnboarding/formData")
        async getUserOnboardingFormData(req: express.Request): Promise<object> {

            const userContext: UserContext = req.userContext as UserContext
            const userId: string = userContext.userProfile.userId
            this.logger.info("User Onboarding form opening for userId ", userId)

            const response: {
                items: {
                    duration: number;
                    text: string;
                }[];
            } = {
                items: [
                    {
                        duration: 2,
                        text: "Analysing Your Inputs",
                    },
                    {
                        duration: 2,
                        text: "Creating Your Plan",
                    },
                    {
                        duration: 1,
                        text: "Plan Creation Complete",
                    },
                ],
            }

            return response
        }

        @httpGet("/userOnboarding/enroll")
        async userOnboardingProgramEnroll(req: express.Request): Promise<object> {

            const userContext: UserContext = req.userContext as UserContext
            const userId: string = userContext.userProfile.userId
            const packId: string = req.query.packId
            const isEnroll: boolean = req.query.isEnroll === "1"
            const tenant = AppUtil.getTenantFromUserContext(userContext)
            this.logger.info("UserId: " + userId + ", packId: " + packId + ", isEnroll: " + isEnroll )

            const enrollState: SubscriptionStatus = isEnroll ? "SUBSCRIBED" : "UNSUBSCRIBED"

            const enrollStatus =  await this.diyFulfilmentService.changeNewUserEnrolledProgramState(userId, packId, enrollState, tenant)
            if (isEnroll) {
                await new Promise(resolve => setTimeout(resolve, 3000))
            }

            // Generic Comment
            return {
                message: "Success",
                enrollStatus: enrollStatus,
            }
        }

    }
    return DigitalController
}

export default controllerFactory
