import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { ProfileVisibility, Tenant, UserProfileEntry, UserProfileEntryPaged } from "@curefit/social-common"
import * as _ from "lodash"
import CultUtil from "../util/CultUtil"
import { UserContext } from "@curefit/vm-models"
import { SOCIAL_CLIENT_TYPES, SocialService } from "@curefit/social-client"
import { CacheHelper } from "../util/CacheHelper"
import { User } from "@curefit/user-common"
import { WorkoutMemberSection, WorkoutMemberViewAllPageResponse } from "@curefit/apps-common"
import LiveUtil from "../util/LiveUtil"

export const ITEMS_PER_ROW = 5
export const NUM_ROWS_PRIVATE = 1
export const NUM_ROWS_RANKS = 1
export const PAYLOAD_SIZE_FOR_PROFILES_PAGE = 8
export const NUM_ROWS_PUBLIC = 2
export const NUM_ROWS_PUBLIC_INITIAL = 1

@injectable()
class DigitalWorkoutMemberHistoryBuilder {
    constructor(
        @inject(SOCIAL_CLIENT_TYPES.SocialService) private socialService: SocialService,
        @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
    ) {}

    async getViewAllPagePrivate(userContext: UserContext, contentId: string, userFilters: string[]): Promise<WorkoutMemberViewAllPageResponse> {
        const sections: WorkoutMemberSection[] = []
        const totalElementsArray = await Promise.all(userFilters.map(async userFilter => this.socialService.getCountOfCommunityUsers(userContext.userProfile.userId, undefined, contentId, Tenant.LIVE, userFilter)))
        // const totalElementsArray = await Promise.all(userFilters.map(async (userFilter = "NONE") => (await this.socialService.getCommunityUsers(userContext.userProfile.userId, undefined, contentId, Tenant.LIVE, userFilter, 0, 1)).totalElements))
        userFilters.forEach((userFilter = "NONE", index) => {
            const totalElements = totalElementsArray[index]
            if (totalElements > 0) {
                sections.push(this.getSectionPrivate(userFilter, totalElements))
            }
        })
        return {
            sections,
            isCurrentUserProfilePublic: false,
            header: {
                title: "You worked out with:"
            }
        }
    }

    async getViewAllPagePublic(userContext: UserContext, filteredUserProfiles: {value: UserProfileEntryPaged, filter: string}[], userFilters: string[], payloadForFilter: {[filter: string]: number}, contentId: string): Promise<WorkoutMemberViewAllPageResponse> {
        const filteredUserProfilesLoggingInfo = filteredUserProfiles.map(filteredUserProfile => {
          return {
            length: filteredUserProfile.value.elements.length,
            limit: filteredUserProfile.value.limit,
            totalElements: filteredUserProfile.value.totalElements,
            filter: filteredUserProfile.filter
          }
        })
        const userIds = []
        for (const filterUserProfileEntry of filteredUserProfiles) {
            for (const element of filterUserProfileEntry.value.elements) {
                userIds.push(element.userId)
            }
        }
        const users = await this.userCache.getUsers(userIds)

        const sections = filteredUserProfiles.map((filteredUserProfileEntry, sectionIndex) => {
            const filter: string | undefined = filteredUserProfileEntry.filter || "NONE"
            return this.getSectionPublic(filteredUserProfileEntry.value, users, filteredUserProfileEntry.filter, payloadForFilter[filter], contentId, )
        })
        return {
            sections,
            isCurrentUserProfilePublic: true,
            header: {
                title: "You worked out with:"
            }
        }
    }

    getSectionPrivate(userFilter = "NONE", totalElements: number): WorkoutMemberSection {
        const userFilterIsRank = userFilter === "RANKS"
        const payloadSizeForRanks = NUM_ROWS_RANKS * ITEMS_PER_ROW
        const title = this.getSectionTitleFromFilter(userFilter)
        const displayText = this.getDisplayText(userFilterIsRank ? payloadSizeForRanks : totalElements)
        /* Assuming private users will have only one API call and that will send only the initial numRows */
        let lengthOfSentData
        if (userFilter === "RANKS") {
            lengthOfSentData = payloadSizeForRanks
        } else if (totalElements > NUM_ROWS_PRIVATE * ITEMS_PER_ROW) {
            lengthOfSentData = NUM_ROWS_PRIVATE * ITEMS_PER_ROW
        } else {
            lengthOfSentData = totalElements
        }
        const data = LiveUtil.getRandomWorkoutMembers(lengthOfSentData, CultUtil.getMakeProfilePublicAction(false))
        return {
            data,
            title,
            displayCount: displayText,
            footerText: "SEE MORE",
            footerAction: CultUtil.getMakeProfilePublicAction(false),
            userFilter,
        }
    }

    getSectionPublic(filteredUserProfileEntry: UserProfileEntryPaged, users: {[id: string]: User}, userFilter = "NONE", payloadSizeQueried: number, contentId: string): WorkoutMemberSection {
        const title = this.getSectionTitleFromFilter(userFilter)
        const totalElements = userFilter === "RANKS" ? Math.min(NUM_ROWS_RANKS * ITEMS_PER_ROW, filteredUserProfileEntry.totalElements) : filteredUserProfileEntry.totalElements
        const displayText = this.getDisplayText(totalElements)
        const elementsRemainingToBeQueried = this.getElementsRemainingToBeQueried(filteredUserProfileEntry)
        const nextQuery = this.getNextQuery(filteredUserProfileEntry, payloadSizeQueried, userFilter)
        const footerText = !_.isEmpty(nextQuery) ? "SEE MORE" : undefined
        const teaserText = _.isEmpty(nextQuery) && payloadSizeQueried !== filteredUserProfileEntry.elements.length && (elementsRemainingToBeQueried > 0) ? `+${elementsRemainingToBeQueried} more` : undefined
        const elements = filteredUserProfileEntry.elements
        return {
            data: elements.map((element, index) => {
                const profileIndex = filteredUserProfileEntry.offset + index
                const otherUser = users[element.userId]
                const isOtherUserProfilePublic = element.visibility === ProfileVisibility.PUBLIC
                const action = CultUtil.getProfileClickAction(true, isOtherUserProfilePublic, profileIndex, contentId, {
                    productType: "LIVE_FITNESS",
                    paginationEnabled: true,
                    userFilter,
                    offset: DigitalWorkoutMemberHistoryBuilder.getOffsetForUserProfile(profileIndex, userFilter),
                    payloadSize: userFilter !== "RANKS" ? PAYLOAD_SIZE_FOR_PROFILES_PAGE : ITEMS_PER_ROW * NUM_ROWS_RANKS,
                    limit: filteredUserProfileEntry.limit,
                })
                return CultUtil.createWorkoutMember(true, isOtherUserProfilePublic, otherUser, action)
            }),
            title,
            displayCount: displayText,
            nextQuery,
            footerText,
            userFilter,
            teaserText
        }
    }

    private getElementsRemainingToBeQueried(filteredUserProfileEntry: UserProfileEntryPaged) {
        const nextLastProfileIndex = filteredUserProfileEntry.offset + filteredUserProfileEntry.elements.length - 1
        return filteredUserProfileEntry.totalElements - 1 - nextLastProfileIndex
    }

    private getNextQuery(filteredUserProfileEntry: UserProfileEntryPaged, payloadSizeQueried: number, userFilter: string) {
        const offset = filteredUserProfileEntry.offset
        const totalElements = userFilter === "RANKS" ? Math.min(NUM_ROWS_RANKS * ITEMS_PER_ROW, filteredUserProfileEntry.totalElements) : filteredUserProfileEntry.totalElements
        const numMembersToFetch = Math.floor(filteredUserProfileEntry.limit / ITEMS_PER_ROW) * ITEMS_PER_ROW || filteredUserProfileEntry.limit
        const nextLastProfileIndex = offset + filteredUserProfileEntry.elements.length - 1
        const shouldAllowFurtherQueries = (nextLastProfileIndex < totalElements - 1) && (payloadSizeQueried > 0) && (payloadSizeQueried === filteredUserProfileEntry.elements.length) && userFilter !== "RANKS"
        return  shouldAllowFurtherQueries ? {
            offset: offset + filteredUserProfileEntry.elements.length,
            payloadSize: numMembersToFetch
        } : undefined
    }

    static getOffsetForUserProfile(profileIndex: number, userFilter?: string) {
        if (userFilter === "RANKS") {
            return 0
        }
        return Math.max(0, profileIndex - Math.ceil(PAYLOAD_SIZE_FOR_PROFILES_PAGE / 2))
    }

    getDisplayText(count: number) {
        return count >= 1000 ? `${(count / 1000).toFixed(1)} K` : `${count}`
    }

    getSectionTitleFromFilter(filter: string | undefined) {
        switch (filter) {
            case "RANKS": return "TOP PERFORMERS"
            case "TAGS": return "WITH COMMON INTERESTS"
            case "NONE": return "ALL ATHLETES"
            default : return "ALL ATHLETES"
        }
    }
}

export default DigitalWorkoutMemberHistoryBuilder
