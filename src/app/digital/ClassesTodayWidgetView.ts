import { IBaseWidget, UserContext } from "@curefit/vm-models"
import { ClassesTodayWidget } from "@curefit/vm-models/dist/src/models/widgets/digital/ClassesTodayWidget"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { LiveClass, LiveFitWorkoutFormat, VideoStatus } from "@curefit/diy-common"
import AppUtil from "../util/AppUtil"
import * as _ from "lodash"
import { TodaysClassWidget } from "./TodaysClassWidget"
import LiveUtil, { LIVE_SESSION_ACTION_SOURCE } from "../util/LiveUtil"
import { HourMin } from "@curefit/base-common"
import { TimeUtil } from "@curefit/util-common"
import { Action } from "../common/views/WidgetView"
import * as momentTz from "moment-timezone"
import { User } from "@curefit/user-common"
import { WidgetHeader } from "@curefit/apps-common"
import { SubscriptionStatus } from "@curefit/diy-common"
import { LivePackUtil } from "../util/LivePackUtil"
import { HamletBusiness } from "@curefit/hamlet-node-sdk"

export class ClassesTodayWidgetView extends ClassesTodayWidget {
    header: WidgetHeader
    widgets: TodaysClassWidget[]

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams?: { [p: string]: string }): Promise<IBaseWidget> {
        const countryId = AppUtil.getCountryId(userContext)
        const userId = userContext.userProfile.userId
        const user = await interfaces.userCache.getUser(userId)
        const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, interfaces.hamletBusiness)
        let formats: LiveFitWorkoutFormat[] = ["SNC", "STRENGTH", "CARDIO", "HRX", "BOXING", "DANCE", "YOGA", "MEDITATION", "MIND_PODCAST", "DANCE_FIT_JUNIOR", "BARRE", "TABATA", "PILATES", "HIIT", "RECOVERY", "AMA", "WALK_FITNESS"]

        if (!_.isEmpty(this.productType)) {
            if (this.productType === "MIND") {
                formats = ["YOGA", "MEDITATION"]
            } else if (this.productType === "DIY_MEDITATION") {
                formats = ["MEDITATION"]
            } else if (this.productType === "FITNESS") {
                formats = ["SNC", "STRENGTH", "CARDIO", "HRX", "BOXING", "DANCE", "YOGA", "DANCE_FIT_JUNIOR", "BARRE", "TABATA", "PILATES", "HIIT", "RECOVERY", "AMA", "WALK_FITNESS"]
            } else if (this.productType === "FOOD") {
                formats = ["EAT"]
            } else if (this.productType === "MIND_PODCAST") {
                formats = ["MIND_PODCAST"]
            } else if (this.productType === "HOBBY") {
                formats = ["HOBBY"]
            }
        }
        const allLiveClasses: LiveClass[] = await interfaces.diyService.getUpcomingLiveClasses(userId, AppUtil.getTenantFromUserContext(userContext), formats, countryId)
        if (_.isEmpty(allLiveClasses)) {
            return undefined
        }
        const liveClasses = LiveUtil.filterInteractiveSessionAndRealLive(allLiveClasses)
        const title = "Classes Today"
        const bookingPageAction = LiveUtil.getLiveClassBookingPageAction(userContext, this.productType)
        const scheduleAction: Action = {
            actionType: "NAVIGATION",
            url: bookingPageAction.url,
            title: "SCHEDULE",
            meta: {
                showArrowImage: true
            }
        }
        this.header = {
            title: title,
            seemore: scheduleAction
        }
        this.widgets = await this.getTodaysClassWidgets(interfaces, userContext, user, liveClasses, bucketId)
        return this
    }

    async getTodaysClassWidgets(interfaces: CFServiceInterfaces, userContext: UserContext, user: User, liveClasses: LiveClass[], bucketId: string): Promise<TodaysClassWidget[]> {
        const { sessionInfo } = userContext
        liveClasses = liveClasses.slice(0, 10)
        const sessionCount = LiveUtil.getNumberOfSessions(user, liveClasses)
        const timezone = userContext.userProfile.timezone
        const blockInternationalUser = await AppUtil.blockFitnessContentForInternationalUser(userContext, interfaces.maxmindService, interfaces.logger)
        const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(interfaces.cultBusiness, interfaces.diyService, userContext)
        const todaysLiveClasses: TodaysClassWidget[] = await Promise.all(liveClasses.map(async session => {
            if (_.includes(session.tags, "TEST_SESSION") && !user.isInternalUser) {
                return
            }
            const durationHourMin: HourMin = TimeUtil.convertDurationSecondsToHourMin(session.duration / 1000)
            let formattedTimeString: string = durationHourMin.min > 0 ? durationHourMin.min + " Min" : ""
            if (durationHourMin.hour > 0) {
                formattedTimeString = durationHourMin.hour + " Hr " + formattedTimeString
            }
            const intensity = session.intensityLevel && !(session.format === "MIND_PODCAST") && !LiveUtil.isVanillaLiveFitFormat(session.format) ? session.intensityLevel : ""
            const isInternationalApp = AppUtil.isInternationalApp(userContext)
            const todayLiveClass: TodaysClassWidget = {
                widgetType: "CLASS_TODAY_WIDGET",
                image: LiveUtil.getImage(session.bannerImages, sessionInfo, sessionCount),
                title: session.title,
                intensity: intensity,
                duration: formattedTimeString,
                timeslots: [],
                isMasterClass: session.isMasterClass,
                isLocked: isInternationalApp && AppUtil.isLiveContentLocked(userContext, session.slots[0].locked, isUserEligibleForMonetisation, isUserEligibleForTrial),
                refreshCardEpoch: session.slots[0].scheduledTimeEpoch,
                cardAction: await LiveUtil.getLiveWODTimeslotActionV2(session.slots[0].classId, "PREPARED", sessionInfo, "clp_banner_cta", timezone, interfaces, userContext, session.slots[0].locked, bucketId, session.liveClassId, isUserEligibleForMonetisation, isUserEligibleForTrial)
            }
            const NUM_SLOTS = 3
            const slots = session.slots.slice(0, NUM_SLOTS)
            todayLiveClass.timeslots = await Promise.all(slots.map(async (slot, index) => {
                const action: Action = await this.getSlotAction(slot.classId, slot.subscriptionStatus, slot.status, session.liveClassId, session.format, session.contentCategory, interfaces, userContext, user, slot.locked, isUserEligibleForMonetisation, isUserEligibleForTrial, "todays_widget", bucketId, blockInternationalUser)
                return {
                    title: momentTz(slot.scheduledTimeEpoch).tz(timezone).format(index == 0 ? "hh:mm A" : "hh:mm"),
                    action: action,
                    isLocked: isInternationalApp && AppUtil.isLiveContentLocked(userContext, slot.locked, isUserEligibleForMonetisation, isUserEligibleForTrial),
                    isScheduled: slot.subscriptionStatus === "SUBSCRIBED",
                    isLive: slot.status === "LIVE"
                }
            }))
            if (session.slots.length > NUM_SLOTS) {
                todayLiveClass.moreAction = todayLiveClass.cardAction
            }
            return todayLiveClass.timeslots.length > 0 ? todayLiveClass : undefined
        }))
        return todaysLiveClasses.filter(card => card)
    }

    private async getSlotAction(classId: string, subscriptionStatus: SubscriptionStatus, status: VideoStatus, liveClassId: string, format: LiveFitWorkoutFormat,
        contentCategory: string, interfaces: CFServiceInterfaces, userContext: UserContext, user: User, isLocked: boolean, isUserEligibleForLivePackMonetisation: boolean,
        isUserEligibleForLivePackTrial: boolean, source: LIVE_SESSION_ACTION_SOURCE, bucketId: string, blockInternationalUser: boolean): Promise<Action> {
        let action: Action
        if (status === "LIVE") {
            const liveSession = await interfaces.diyService.getDigitalCatalogueEntry(classId)
            action = LiveUtil.getLiveVideoPlayerAction(userContext, user, liveSession, userContext.sessionInfo, source)
        } else if (subscriptionStatus === "SUBSCRIBED") {
            return LiveUtil.getLiveSessionDetailActionFromLiveClassId(liveClassId, classId, source, contentCategory, userContext)
        } else {
            const liveSession = await interfaces.diyService.getDigitalCatalogueEntry(classId)
            action = await LiveUtil.getVideoSubscribeAction(user, userContext, liveSession, [], userContext.sessionInfo, source, false, interfaces.classInviteLinkCreator, userContext.userProfile.timezone)
        }
        return LiveUtil.getLiveSessionAction(isUserEligibleForLivePackMonetisation, isLocked, action, isUserEligibleForLivePackTrial, userContext, format, source, bucketId, blockInternationalUser)
    }
}
