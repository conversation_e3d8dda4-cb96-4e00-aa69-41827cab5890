import { injectable, inject } from "inversify"
import { ProductDetailPage, Action, ProductListWidget, IconDescriptionWidget } from "../common/views/WidgetView"
import { PageWidget } from "../page/Page"
import { ImageData } from "@curefit/diy-common"
import { UserContext } from "@curefit/vm-models"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import * as _ from "lodash"
import { ImageOverlayCardContainerWidget, HeaderWidget, User } from "@curefit/apps-common"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { WODLiveCarouselWidgetView } from "./WODLiveCarouselWidgetView"
import ActionUtil from "../util/ActionUtil"

class SocialKnowMoreView extends ProductDetailPage {

    bannerImages: ImageData

    constructor(widgets: PageWidget[], actions: Action[]) {
        super()
        this.widgets = widgets
        this.actions = actions
        this.bannerImages = {
          mobileImage: "/image/livefit/app/live-know-more-thumbnail.png",
          mwebImage: "/image/livefit/app/live-know-more-thumbnail.png",
          webImage: "/image/livefit/app/live-know-more-thumbnail.png",
          webImageLarge: "/image/livefit/app/live-know-more-thumbnail.png",
          webProductImage: "/image/livefit/app/live-know-more-thumbnail.png",
        }
    }
}

@injectable()
class SocialKnowMoreViewBuilder {

    constructor(
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
    ) {
    }

    async buildView(userContext: UserContext): Promise<SocialKnowMoreView> {
        const widgets: PageWidget[] = []
        const actions: Action[] = []

        const user: User = await userContext.userPromise

        const productType = "FITNESS"
        const bookingPageAction = ActionUtil.getClassBookingAction(userContext, productType, user)

        const showCalendarAction: Action = {
          title: "EXPLORE UPCOMING CLASSES",
          actionType: "NAVIGATION",
          url: bookingPageAction.url,
        }

        actions.push(showCalendarAction)
        widgets.push(this.getImageOverlayCardContainerWidget())
        widgets.push(this.getFullScreenIconDescriptionWidget())
        widgets.push(this.getHowItWorksWidget())
        widgets.push(await this.getWODLiveCarouselWidget(userContext))
        return new SocialKnowMoreView(widgets, actions)
    }

    getImageOverlayCardContainerWidget(): ImageOverlayCardContainerWidget {
      const imageOverlayContainerWidget = new ImageOverlayCardContainerWidget()
      imageOverlayContainerWidget.assets.push({
          assetType: "IMAGE",
          assetUrl: "image/livefit/app/live-know-more-thumbnail.png"
      })
      const headerWidget: HeaderWidget = {
        widgetType: "HEADER_WIDGET",
        widgetTitle: {
            title: "Friends who sweat together, stay together",
            subTitle: "Attend LIVE classes with your friends, enjoy friendly competition in real-time, and stay motivated!"
        },
        headerStyle: {
            marginLeft: 0,
            fontSize: 22,
            color: "#000000"
        },
        subTitleStyle: {
            marginLeft: 0,
            fontSize: 14,
            color: "rgb(102, 102, 102)"
        }
      }
      imageOverlayContainerWidget.widgets.push(headerWidget)
      return imageOverlayContainerWidget
    }

    getHowItWorksWidget(): ProductListWidget {
      const howItWorksWidget: ProductListWidget = {
          widgetType: "PRODUCT_LIST_WIDGET",
          type: "SMALL",
          header: {
              title: "How it works"
          },
          hideSepratorLines: false,
          items: [
            {
              subTitle: "Subscribe to a LIVE Class of your choice",
              icon: "/image/livefit/app/select.png",
            },
            {
              subTitle: "Share the class invite link with your friends",
              icon: "/image/livefit/app/share.png",
            },
            {
              subTitle: "Get notified when your friend accepts your invite",
              icon: "/image/livefit/app/notification.png",
            },
            {
              subTitle: "Compete in-class with your friends",
              icon: "/image/livefit/app/compete.png",
            },
            {
              subTitle: "See your performance vs your friends in your report",
              icon: "/image/livefit/app/trophy.png",
            },
          ]
      }
      return howItWorksWidget
    }

    private async getWODLiveCarouselWidget(userContext: UserContext) {
      const wodLiveCarouselWidget = await new WODLiveCarouselWidgetView().buildView(this.serviceInterfaces, userContext)
      if (!_.isNil(wodLiveCarouselWidget) && !_.isNil(wodLiveCarouselWidget.layoutProps)) {
        wodLiveCarouselWidget.layoutProps.backgroundColor = "rgb(244, 244, 244)"
      }
      return wodLiveCarouselWidget
    }

    private getFullScreenIconDescriptionWidget(): IconDescriptionWidget {
      const iconDescriptionWidget: IconDescriptionWidget = {
          widgetType: "ICON_DESCRIPTION_WIDGET",
          actions: [],
          data: [
            {
              description: "Workout together",
              iconId: "GROUP",
            },
            {
              description: "Compete in-class",
              iconId: "COMPETE",
            },
            {
              description: "Stay motivated",
              iconId: "MOTIVATED",
            },
          ],
          showDivider: false
      }
      return iconDescriptionWidget
    }

}

export default SocialKnowMoreViewBuilder