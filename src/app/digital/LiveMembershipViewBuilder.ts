import { injectable, inject } from "inversify"
import {
  ProductDetailPage,
  Action,
  ProductListWidget,
  ManageOptionPayload,
  NavigationCardWidget
} from "../common/views/WidgetView"
import { PageWidget } from "../page/Page"
import { ImageData } from "@curefit/diy-common"
import { UserContext } from "@curefit/vm-models"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import * as _ from "lodash"
import { User, WidgetView, CultActivePackInfoWidget } from "@curefit/apps-common"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { UrlPathBuilder } from "@curefit/product-common"
import { FoodProduct as Product } from "@curefit/eat-common"
import { Membership } from "@curefit/membership-commons"
import CultUtil from "../util/CultUtil"
import AppUtil, { SUPPORT_DEEP_LINK } from "../util/AppUtil"
import { LivePackUtil } from "../util/LivePackUtil"
import LiveUtil from "../util/LiveUtil"
import { IThirdPartyService, NeuPassClickAction, THIRD_PARTY_CLIENT_TYPES } from "@curefit/third-party-integrations-client"

const CANCEL_SUBSCRIPTION_IOS = "https://apps.apple.com/account/subscriptions"
const CANCEL_SUBSCRIPTION_GOOGLE_PLAY = "https://play.google.com/store/account/subscriptions"

class LiveMembershipView extends ProductDetailPage {

  constructor(widgets: PageWidget[], actions: Action[]) {
    super()
    this.widgets = widgets
    this.actions = actions
  }
}

@injectable()
class LiveMembershipViewBuilder {

  constructor(
    @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
    @inject(THIRD_PARTY_CLIENT_TYPES.ThirdPartyService) private thirdPartyService: IThirdPartyService,
  ) {
  }

  async buildView(userContext: UserContext, membershipId: number): Promise<LiveMembershipView> {
    const widgets: (WidgetView | PageWidget)[] = []
    const actions: Action[] = []
    const membership: Membership = await this.serviceInterfaces.diyService.getMembershipById(membershipId)

    widgets.push(await this.getMembershipViewWidget(userContext, membership))

    widgets.push(this.getHowItWorksWidget())
    widgets.push(LivePackUtil.getFAQWidget(userContext))

    return new LiveMembershipView(widgets, actions)
  }

  getRightInfo(membership: Membership, userContext: UserContext, tz: Timezone, membershipState: any) {
    if (membershipState === "PAUSED") {
      const pauseDate: any = undefined // membership.ActivePause ? membership.ActivePause.maxEndDate ? membership.ActivePause.maxEndDate : undefined : undefined
      if (pauseDate) {
        const formatedPauseDate = TimeUtil.formatDateInTimeZone(tz, pauseDate)
        const todaysDate = TimeUtil.todaysDate(tz)
        const diffDays = TimeUtil.diffInDays(userContext.userProfile.timezone, todaysDate, formatedPauseDate)
        return diffDays === 0 ? `Resumes Tonight` : `Resumes in ${diffDays} days`
      }
    }
    return null
  }

  getLeftText(packTagAndColor: any, startDateFormatted: string, duration: number, isTrial: boolean, userContext: UserContext) {
    if (packTagAndColor.tag === "EXPIRED" && isTrial) {
      return `${duration} /${duration} Completed`
    }
    if (AppUtil.isWeb(userContext)) {
      return startDateFormatted
    }
    return `${packTagAndColor.tag === "UPCOMING" ? "Starts:" : "Started:"} ${startDateFormatted}`
  }

  async getMyNeuPassWidget(userContext: UserContext, url: string): Promise<any> {
    const isWeb = AppUtil.isWeb(userContext)
    const action: Action = {
      actionType: isWeb ? "EXTERNAL_DEEP_LINK" : "NAVIGATION",
      title: "",
      url: isWeb ? url : `curefit://webview?uri=${encodeURIComponent(url)}&theme=dark&title=${encodeURIComponent("My NeuPass")}`,
    }

    if (isWeb) {
      return new NavigationCardWidget("My NeuPass", "", action, true, undefined, undefined, undefined, undefined, undefined, "TATA_NEU_LOGO")
    }

    return {
      widgetType: "LIST_INFO_CARD_WIDGET",
      title: "My NeuPass",
      imageUrl:
        "image/tata/tata-neu.png",
      imageSize: 25,
      layoutProps: {
        spacing: {
          top: 15,
          force: true,
        },
      },
      action
    }
  }

  async getMembershipViewWidget(userContext: UserContext, membership: Membership): Promise<CultActivePackInfoWidget> {
    // check membership widget is shown to current user only
    if (membership.userId !== userContext.userProfile.userId) {
      return {
        widgetType: "CULT_ACTIVE_PACK_INFO_WIDGET",
        title: "Unable to fetch membership",
        image: "image/livefit/app/packs/membership_detail.png",
        tag: null,
        actions: null,
      }
    }

    const tz = userContext.userProfile.timezone
    const isTrialMembership = !_.isEmpty(membership.metadata) && membership.metadata.isTrial
    const productPromise = this.serviceInterfaces.catalogueService.getProduct(membership.productId)
    const product = await productPromise
    const startDateFormatted = TimeUtil.formatEpochInTimeZone(tz, membership.start, "D MMM YYYY")
    const endDateFormatted = TimeUtil.formatEpochInTimeZone(tz, membership.end, "D MMM YYYY")

    const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDate(tz), tz)
    const endDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.end)), tz)
    const startDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.start)), tz)
    const numDaysToEndFromToday = endDate.diff(today, "days")
    const totalDuration = endDate.diff(startDate, "days")

    const membershipState = LiveUtil.getLiveFitMembershipState(userContext, membership, product, isTrialMembership)
    const packTagAndColor = CultUtil.getCultPackTagAndColor(membershipState, numDaysToEndFromToday)

    const rightInfoText = this.getRightInfo(membership, userContext, tz, membershipState)
    let rightInfo = undefined
    if (rightInfoText) {
      rightInfo = {
        title: rightInfoText
      }
    }

    const actions = await this.getActions(userContext, membership, product, "REPORT")
    const moreActions = await this.getActions(userContext, membership, product, "Need Help")

    if (!isTrialMembership && (membershipState === "ACTIVE" || membershipState === "EXPIRING")) {
      if (userContext.sessionInfo.osName === "ios" && product?.appleIAPProductDetails?.productId) {
        moreActions.push(this.getCancelSubscriptionAction(membership, CANCEL_SUBSCRIPTION_IOS))
      } else if (userContext.sessionInfo.osName === "android" && product?.androidIAPProductDetails?.productId) {
        moreActions.push(this.getCancelSubscriptionAction(membership, CANCEL_SUBSCRIPTION_GOOGLE_PLAY))
      }
    }

    return {
      widgetType: "CULT_ACTIVE_PACK_INFO_WIDGET",
      progressBar: {
        leftText: this.getLeftText(packTagAndColor, startDateFormatted, totalDuration, isTrialMembership, userContext),
        rightText: AppUtil.isWeb(userContext) ? endDateFormatted : `Ends: ${endDateFormatted}`,
        total: totalDuration,
        completed: numDaysToEndFromToday > 0 ? today.diff(startDate, "days") : endDate.diff(startDate, "days"),
        type: "FITNESS",
        noPadding: true,
        progressBarColor: packTagAndColor.color,
        progressBarTextStyle: membershipState === "EXPIRED" ? { color: "#97a4c9" } : {}
      },
      tag: { title: packTagAndColor.tag, color: packTagAndColor.color },
      title: product.title + " " + LiveUtil.getLiveBranding(userContext),
      moreAction: (!_.isEmpty(actions) && actions.length === 1) ? {
        actionType: "ACTION_LIST",
        actions: moreActions
      } : undefined,
      actions: (AppUtil.isWeb(userContext) || (!_.isEmpty(actions) && actions.length > 1)) ? await this.getActions(userContext, membership, product, "REPORT") : undefined,
      image: "image/livefit/app/packs/membership_detail.png",
      centerInfo: undefined,
      rightInfo: rightInfo,
      footer: product?.option?.isNeuPassSubscription ? {
        text: "Complimentary with",
        image: "image/tata/tata-neu-with-neupass-text.png",
        aspectRatio: 4.69
      } : undefined,
    }
  }

  async getActions(userContext: UserContext, membership: Membership, product: Product, title: string): Promise<Action[]> {
    const actions: Action[] = []
    const issues = await this.serviceInterfaces.issueBusiness.getDigitalMembershipIssues(userContext, membership, product)
    const reportIssue = this.serviceInterfaces.issueBusiness.toManageOptionPayload(issues, true)
    actions.push(this.getReportIssueAction(reportIssue, membership, title))

    const isTrialMembership = !_.isEmpty(membership.metadata) && membership.metadata.isTrial
    const membershipState = LiveUtil.getLiveFitMembershipState(userContext, membership, product, isTrialMembership)
    /*if (_.get(userContext, "sessionInfo.osName", "").toLowerCase() === "ios") {
      const subscriptionOptions = product.subscriptionOptions
      const planChangeSources = subscriptionOptions.planChangeSources
      const requestSource = AppUtil.getRequestSource(userContext)
      if (!_.isEmpty(planChangeSources)) {
        let upgradeSupported = false
        planChangeSources.map(planChangeSource => {
          if (planChangeSource.osName === requestSource.osName && planChangeSource.source === requestSource.source) {
            upgradeSupported = true
          }
        })
        if (upgradeSupported) {
          actions.push({
            actionType: "EXTERNAL_DEEP_LINK",
            iconUrl: "/image/icons/cult/add.png",
            title: "UPGRADE",
            url: "itms-apps://apps.apple.com/account/subscriptions"
          })
        }
      }
    }*/
    return actions
  }

  private getCancelSubscriptionAction(membership: Membership, subscriptionUrl: string): Action {
    return {
      title: "Cancel Subscription",
      actionType: "EXTERNAL_DEEP_LINK",
      iconUrl: "/image/icons/cult/cancel.png",
      meta: {},
      url: subscriptionUrl,
      analyticsData: {
        eventKey: "cancel_membership_click",
        eventData: {
          pageFrom: "livemembershippage"
        }
      }
    }
  }

  private getReportIssueAction(reportIssue: ManageOptionPayload, membership: Membership, title: string): Action {
    return {
      title,
      actionType: "NAVIGATION",
      iconUrl: "/image/icons/cult/info.png",
      meta: reportIssue.meta,
      url: SUPPORT_DEEP_LINK,
      analyticsData: {
        eventKey: "report_issue_click",
        eventData: {
          pageFrom: "livemembershippage"

        }
      }
    }
  }

  getHowItWorksWidget(): ProductListWidget {
    const howItWorksWidget: ProductListWidget = {
      widgetType: "PRODUCT_LIST_WIDGET",
      type: "SMALL",
      header: {
        title: "Unlimited access to"
      },
      hideSepratorLines: true,
      items: [
        {
          subTitle: "Fitness, Dance & Meditation classes",
          icon: "/image/icons/howItWorks/live-icon.png", // "/image/icons/howItWorks/live_icon.png",
          cellStyle: {
            paddingBottom: 0
          },
          subtitleStyle: {
            fontFamily: "BrandonText-Medium",
            fontSize: 16,
            color: "#000000"
          }
        },
        {
          subTitle: "Celebrity masterclasses",
          icon: "/image/icons/howItWorks/star-icon.png",
          cellStyle: {
            paddingBottom: 0
          },
          subtitleStyle: {
            fontFamily: "BrandonText-Medium",
            fontSize: 16,
            color: "#000000"
          }
        },
        {
          subTitle: "Live Energy meter & leaderboard",
          icon: "/image/icons/howItWorks/bolt-icon.png",
          cellStyle: {
            paddingBottom: 0
          },
          subtitleStyle: {
            fontFamily: "BrandonText-Medium",
            fontSize: 16,
            color: "#000000"
          }
        },
        {
          subTitle: "Workout with friends & family",
          icon: "/image/icons/howItWorks/group-icon.png",
          cellStyle: {
            paddingBottom: 0
          },
          subtitleStyle: {
            fontFamily: "BrandonText-Medium",
            fontSize: 16,
            color: "#000000"
          }
        },
        {
          subTitle: "Chromecast support",
          icon: "/image/icons/howItWorks/cast-black-icon.png",
          cellStyle: {
            paddingBottom: 0
          },
          subtitleStyle: {
            fontFamily: "BrandonText-Medium",
            fontSize: 16,
            color: "#000000"
          }
        }
      ]
    }
    return howItWorksWidget
  }
}

export default LiveMembershipViewBuilder
