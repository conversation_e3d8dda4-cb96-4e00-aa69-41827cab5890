import { inject, injectable } from "inversify"
import { UserContext } from "@curefit/vm-models"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import DigitalReportViewBuilder from "./DigitalReportViewBuilder"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService } from "@curefit/diy-client"
import { <PERSON>acheHelper } from "../util/CacheHelper"
import { ISocialService, SOCIAL_CLIENT_TYPES, SocialService } from "@curefit/social-client"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { Challenge, EnrolmentResponse, Group, RankedChallenge } from "@curefit/riddler-common"
import {
    Action,
    HeaderWidget,
    ImageOverlayCardContainerWidget,
    LeagueLeaderBoardListWidget,
    LeagueRequestStatusSectionListWidget,
    MediaAsset
} from "@curefit/apps-common"
import { DescriptionWidget, ManageOptionsWidgetV2, ProductListWidget } from "../common/views/WidgetView"
import DigitalSocialLeagueTabPageViewBuilder, {
    CHALLENGE_DESCRIPTION_NAME,
    CHALLENGE_STATUS,
    CHALLENGE_STATUS_TEXT,
    ExtraData,
    LEAGUE_LEADERBOARD_SOURCE
} from "./DigitalSocialLeagueTabPageViewBuilder"
import AppUtil from "../util/AppUtil"
import { IRiddlerService, RIDDLER_CLIENT_TYPES } from "@curefit/riddler-client"
import { eternalPromise, TimeUtil, Timezone } from "@curefit/util-common"
import * as _ from "lodash"
import LiveUtil from "../util/LiveUtil"
import { BASE_TYPES, Logger } from "@curefit/base"
import DigitalLeagueSectionViewBuilder, { LeagueSectionSource } from "./DigitalLeagueSectionViewBuilder"
import * as momentTz from "moment-timezone"
import ChallengesViewBuilder from "../challenges/ChallengesViewBuilder"
import ChallengeDetailsViewBuilder, { ParticipantDetails } from "../challenges/ChallengeDetailsViewBuilder"
import { IUserService, USER_CLIENT_TYPES } from "@curefit/user-client"

export interface TeamParticipantDetails extends ParticipantDetails {
    groupMap?: {[groupId: string]: Group & {profilePictureUrl?: string}},
    communityIdVsUserId: {[groupId: string]: string}
}

const HEADER_WIDGET_HEADER_STYLE = {
    marginLeft: 0,
    fontSize: 22,
    color: "#000000",
    width: "100%",
}
const HEADER_WIDGET_SUBTITLE_STYLE = {
    marginLeft: 0,
    fontSize: 14,
    color: "#000000",
}
const HEADER_WIDGET_SUBHEADER_STYLE = {
    fontSize: 12,
    color: "#55565b",
    fontFamily: "BrandonText-Regular",
    marginBottom: 9,
}

/* Basics:
1. Only one challenge per community
2. Can have multiple communities to a single challenge
3. Can't join a challenge if another community that we are part of is already enrolled to the challenge
4. A community with a single member(the creator) can't the enrolled
5. (follows from 4) user can't enrol their community when it is the only one of all their joined or created communities that is not enrolled to the challenge,
        if it doesn't have any member other than them.
    a. if they haven't created any other community(only community is the one they created): then we still show them join option but then show a
        prompt on challenge page and doesn't allow them to join unless they add members to that community
    b. if they have joined other communities(>=1) but all of them are already enrolled to this challenge, then we show them the join option only
        if they have members in their own community, and on challenge page they will be able to join
*/


export const SQUAD_CHALLENGE_ENROLMENT_TOAST_MESSAGES = {
    "SUCCESS": "Challenge joined",
    "FAILED": "Failed to join challenge"
}
const TOO_FEW_MEMBER_TITLE = "You haven't added a friend yet"
const TOO_FEW_MEMBER_FOR_CHALLENGE_PROMPT = "You need to have at least one friend in your Squad to join the challenge"

export const SQUAD_CHALLENGE_TAG = "squad"

export const  LIVE_CHALLENGE_TAG = "live"

export const TEAM_CHALLENGE_TYPE = "TEAM"

export const TEST_TAG = "test"

export const MAX_NEARBY_SQUADS_TO_SHOW = 3

@injectable()
class DigitalLeagueChallengeViewBuilder {
    constructor(
      @inject(CUREFIT_API_TYPES.DigitalReportViewBuilder) private digitalReportsViewBuilder: DigitalReportViewBuilder,
      @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) private diyFulfilmentService: IDIYFulfilmentService,
      @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
      @inject(SOCIAL_CLIENT_TYPES.SocialService) private socialService: SocialService,
      @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
      @inject(CUREFIT_API_TYPES.DigitalSocialLeagueTabPageViewBuilder) private digitalSocialLeagueTabPageViewBuilder: DigitalSocialLeagueTabPageViewBuilder,
      @inject(RIDDLER_CLIENT_TYPES.RiddlerService) private riddlerService: IRiddlerService,
      @inject(BASE_TYPES.ILogger) private logger: Logger,
      @inject(CUREFIT_API_TYPES.DigitalLeagueSectionViewBuilder) private digitalLeagueSectionViewBuilder: DigitalLeagueSectionViewBuilder,
      @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
      @inject(CUREFIT_API_TYPES.ChallengeDetailsViewBuilder) private challengeDetailsViewBuilder: ChallengeDetailsViewBuilder,
      ) {}

    async buildView(userContext: UserContext, challengeId: string): Promise<any> {
        const userId = userContext.userProfile.userId
        const challengePromise = eternalPromise(this.riddlerService.getChallenge(challengeId))
        const userHasAtLeastOneSquadMemberPromise = eternalPromise(this.isUserEligibleToJoinChallenge(userId))
        const extraData: ExtraData = {}
        const leagueLeaderBoardListWidgetPromise = this.digitalSocialLeagueTabPageViewBuilder.getLeagueLeaderBoardListWidget({
            userContext,
            source: LEAGUE_LEADERBOARD_SOURCE.SQUAD_CHALLENGE_PAGE,
            selectedChallengeId: challengeId,
            extraData
        })

        const [leagueLeaderBoardListWidget, {obj: {challenge}}, {obj: userHasAtLeastOneSquadMember}] = await Promise.all([leagueLeaderBoardListWidgetPromise, challengePromise, userHasAtLeastOneSquadMemberPromise])

        const rewardsWidget = DigitalLeagueChallengeViewBuilder.getRewardsWidget(challenge)

        const widgets = []

        const pageAction = await this.getPageAction(leagueLeaderBoardListWidget, extraData, challenge, userHasAtLeastOneSquadMember, userContext)
        widgets.push(this.getImageOverlayCardContainerWidget(userContext, challenge))

        let createSquadWidgetAdded = false
        if (!(userHasAtLeastOneSquadMember)) {
            const createSquadWidget = await this.getCreateSquadWidget(userContext)
            if (createSquadWidget) {
                createSquadWidgetAdded = true
                widgets.push(createSquadWidget)
            }
        }
        if (!createSquadWidgetAdded) {
            const leaderboardWidget = await this.getLeaderboardWidget(challenge, extraData)
            if (leaderboardWidget) {
                widgets.push(leaderboardWidget)
            }
            if (leagueLeaderBoardListWidget?.data?.length === 1) {
                widgets.push({...leagueLeaderBoardListWidget.data[0], contentContainerStyle: {marginHorizontal: 20, alignSelf: "center"}, widgetType: "LEAGUE_LEADER_BOARD_WIDGET", header: undefined})
            } else if (!_.isEmpty(leagueLeaderBoardListWidget?.data)) {
                widgets.push({...leagueLeaderBoardListWidget, header: undefined})
            }
        }
        widgets.push(rewardsWidget)
        widgets.push(this.getDescriptionWidget(challenge))
        widgets.push(this.getHowItWorksWidget(challenge, Boolean(pageAction)))
        return {
            widgets: widgets.filter(item => item),
            pageAction
        }
    }

    getImageOverlayCardContainerWidget(userContext: UserContext, challenge: Challenge): ImageOverlayCardContainerWidget {
        if (!challenge?.uiConfig?.description) {
            return undefined
        }
        const {bannerUrl} = challenge.uiConfig
        const headerWidget = this.getHeaderWidget(userContext, challenge)

        const manageOptionsWidgetV2 = this.getManageOptionsWidget(userContext, challenge)

        const assets: MediaAsset[] = []
        if (bannerUrl) {
            assets.push({assetType: "IMAGE", assetUrl: bannerUrl})
        }

        const widgets = [manageOptionsWidgetV2, headerWidget]
        return {
            widgetType: "IMAGE_OVERLAY_CARD_CONTAINER_WIDGET",
            widgets,
            assets
        }
    }


    getManageOptionsWidget(userContext: UserContext, challenge: Challenge): ManageOptionsWidgetV2 {
        const challengeStatus = DigitalLeagueChallengeViewBuilder.getChallengeStatusAccordingToTimezone(userContext.userProfile.timezone, challenge)
        const {title, backgroundColor} = CHALLENGE_STATUS_TEXT[challengeStatus]
        return {
            widgetType: "MANAGE_OPTIONS_WIDGET_V2",
            title,
            headerActionIcon: "MANAGE",
            headerActionIconStyle: {
                tintColor: "#000000",
            },
            isLocked: false,
            style: {
                backgroundColor
            },
        }
    }

    getHeaderWidget(userContext: UserContext, challenge: Challenge): HeaderWidget {
        return  {
            widgetType: "HEADER_WIDGET",
            widgetTitle: {
                title: challenge.title,
                subTitle: challenge.constants?.find((val) => val.name === CHALLENGE_DESCRIPTION_NAME)?.value
            },
            headerStyle: HEADER_WIDGET_HEADER_STYLE,
            subTitleStyle: HEADER_WIDGET_SUBTITLE_STYLE,
            subHeader: {
                text: ChallengesViewBuilder._getChallengesSubTitle(userContext.userProfile.timezone, challenge.startDate as any, challenge.endDate as any),
                style: HEADER_WIDGET_SUBHEADER_STYLE,
            },
        }
    }

    static getRewardsWidget(challenge: Challenge) {
        const rewardsInfo = challenge.uiConfig.rewards
        if (_.isEmpty(rewardsInfo.items)) {
            return undefined
        }
        return {
            widgetType: "REWARDS_WIDGET",
            title: challenge.uiConfig.rewards.title || "REWARDS",
            rewards: challenge.uiConfig.rewards.items.map(item => {
                return {
                    badge: {
                        name: "",
                        imageUrl: item.icon
                    },
                    title: item.title,
                    subTitle: "",
                    description: item.description,
                    bgColor: item.bgColor
                }
            })
        }
    }

    async getLeaderboardWidget(challenge: Challenge, extraData: ExtraData) {
        const leaderboardId = DigitalLeagueChallengeViewBuilder.getTeamLeaderboardId(challenge)
        if (challenge.challengeType === TEAM_CHALLENGE_TYPE && leaderboardId) {
            let leaderboardWidget
            try {
                let enrolmentToShow: EnrolmentResponse
                const enrolmentsForChallenge = extraData.challengeIdVsEnrolmentResponses?.[challenge.challengeId]
                if (!_.isEmpty(enrolmentsForChallenge)) {
                    for (const enrolment of enrolmentsForChallenge) {
                        if (!enrolmentToShow || DigitalLeagueChallengeViewBuilder.getTeamRank(enrolment, leaderboardId) < DigitalLeagueChallengeViewBuilder.getTeamRank(enrolmentToShow, leaderboardId)) {
                            enrolmentToShow = enrolment
                        }
                    }
                }
                enrolmentToShow.challenge = challenge
                const participantDetails = await this.challengeDetailsViewBuilder._getParticipantDetails("ENROLMENT", enrolmentToShow as EnrolmentResponse) as TeamParticipantDetails
                leaderboardWidget = this.challengeDetailsViewBuilder._getLeaderboardWidget("ENROLMENT", enrolmentToShow, participantDetails, undefined, true)
                if (!_.isEmpty(leaderboardWidget?.data?.[0])) {
                    return leaderboardWidget.data[0]
                }
            }
            catch (e) {
                this.logger.info(`Leaderboard widget error: ${e.message}`)
                return undefined
            }
        }
    }

    getHowItWorksWidget(challenge: Challenge, hasPageAction: boolean): ProductListWidget {
        const hiws = challenge?.uiConfig?.hiws
        if (!hiws?.items) {
            return undefined
        }
        const hiwWidget: ProductListWidget = {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "SMALL",
            header: {
                title: hiws.title
            },
            hideSepratorLines: false,
            items: hiws.items.map((item) => {
                return {
                    subTitle: item.description,
                    icon: item.icon
                }
            }),
            style: hasPageAction ? {paddingBottom: 70} : undefined
        }
        return hiwWidget
    }

    /* The Page Action when the user arrives on the Challenge Page */
   async getPageAction(leagueLeaderBoardListWidget: LeagueLeaderBoardListWidget, extraData: ExtraData, challenge: Challenge, userHasAtLeastOneSquadMember: boolean, userContext: UserContext): Promise<Action | undefined> {
       const userId = userContext.userProfile.userId
       const challengeId = challenge.challengeId

       const {canShowJoinButton} = await DigitalLeagueChallengeViewBuilder.canShowChallengeJoinButton(extraData, userContext, this.socialService, challenge)

       const userIsAlreadyEnrolledToTheChallenge = extraData.challengeIdVsEnrolmentResponses?.[challengeId]?.some(enrolment => {
           return enrolment.status === "ACTIVE" || enrolment.status === "UPCOMING"
       })

       const challengeStatus = DigitalLeagueChallengeViewBuilder.getChallengeStatusAccordingToTimezone(userContext.userProfile.timezone, challenge)

       let pageAction: Action

       const challengeHasntEnded = challengeStatus !== CHALLENGE_STATUS.ENDED
       if (canShowJoinButton && challengeHasntEnded) {
           if (userHasAtLeastOneSquadMember) {
               /* the user will be actually able to join, follows from Basics[4] */
               pageAction = this.digitalSocialLeagueTabPageViewBuilder.getJoinChallengePopUpAction(challengeId, "JOIN")
           } else {
               /* show a prompt to add more members */
               const ownCommunity = extraData.communitiesResponse.communities.find(community => community.creatorNode.entityId === userId)
               pageAction = this.getAlertModalAction("JOIN", ownCommunity.id, TOO_FEW_MEMBER_TITLE, TOO_FEW_MEMBER_FOR_CHALLENGE_PROMPT)
           }
       } else if (userIsAlreadyEnrolledToTheChallenge) {
           pageAction = {
               actionType: "NAVIGATION",
               url: "curefit://liveclassbooking?pageFrom=squadchallengepage&productType=FITNESS&isLiveBookingPage=true",
               title: "BOOK NEXT CLASS"
           }
       } /* else user erroneously reached this page, they can't do anything, no pageAction */
       return pageAction
   }

    async getCreateSquadWidget(userContext: UserContext): Promise<LeagueRequestStatusSectionListWidget> {
        return await LiveUtil.getCreateSquadView(userContext, this.userCache, this.digitalLeagueSectionViewBuilder, this.socialService, this.logger, LeagueSectionSource.SQUAD_CHALLENGE_PAGE)
    }

    getDescriptionWidget(challenge: Challenge) {
        return {
            ...new DescriptionWidget(
              [{
                  title:  challenge.uiConfig.description.title,
                  subTitle: challenge.uiConfig.description.text
              }],
              {
                  tncLinkTitle: challenge.uiConfig.tncs.tncLinkTitle,
                  tncModalTitle: challenge.uiConfig.tncs.tncModalTitle
              },
              challenge.uiConfig.tncs.tncs
            ),
            containerStyle: {
                paddingTop: 0,
            }
        }
    }

    /* The stage at which the challenge is in according to the currentTime and timezone */
    static getChallengeStatusAccordingToTimezone(tz: Timezone, challenge: Challenge): CHALLENGE_STATUS {
        const dateNow = TimeUtil.getDateNow(tz)
        const challengeStartDate = momentTz(challenge.startDate).tz(tz).startOf("day").toDate()
        const challengeEndDate = momentTz(challenge.endDate).tz(tz).endOf("day").toDate()
        let challengeStatus
        if (challengeStartDate > dateNow) {
            challengeStatus = CHALLENGE_STATUS.UPCOMING
        } else if (dateNow >= challengeStartDate && dateNow <= challengeEndDate) {
            challengeStatus = CHALLENGE_STATUS.ONGOING
        } else {
            challengeStatus = CHALLENGE_STATUS.ENDED
        }
        return challengeStatus
    }

    getAlertModalAction(title: string, communityId: number, metaTitle?: string, metaSubtitle?: string): Action {
        return {
            title,
            actionType: "SHOW_ALERT_MODAL",
            meta: {
                title: metaTitle,
                subTitle: metaSubtitle,
                actions: [DigitalLeagueSectionViewBuilder.getShowLeagueAddContactModalAction("OK",  "Your Squad", true, LeagueSectionSource.MODAL, communityId)]
            }
        }
    }


    static async canShowChallengeJoinButton(extraData: ExtraData, userContext: UserContext, socialService: ISocialService, challenge: Challenge): Promise<{ canShowJoinButton: boolean }> {
        const currentUserId = userContext.userProfile.userId

        const communitiesNotEnrolled = extraData?.communitiesResponse?.communities?.filter((community) => {
            /* Do not include communities which are enrolled to this challenge */
            return !extraData.communityIdVsSelectedChallengeEnrolment[community.id]?.some((enrolmentResponse => {
                /* this community has an unexpired enrolment in the challenge */
                return challenge.challengeId === enrolmentResponse?.enrolment?.challengeId && enrolmentResponse?.enrolment?.status !== "EXPIRED"
            }))
        }) || []

        let satisfies
        const noEligibleCommunities = _.isEmpty(communitiesNotEnrolled)
        const moreThanOneEligibleCommunities = communitiesNotEnrolled.length > 1
        const onlyEligibleCommunityIsOfSomeoneElse = communitiesNotEnrolled.length === 1 && communitiesNotEnrolled[0]?.creatorNode?.entityId !== currentUserId
        /* Used when this is also the only community eligible, then we allow it for enrolment even if it has only currentUser as the only member since it is the case where the user first creates their community before joining any other and hasn't added any members either,
        * this is a special case to allow user to see join challenge option when they first creates a community and then we prompt them to add users to their community, they still can't enrol their community without adding users to it
        * The other case is when the only eligible community is the user's community but they are also part of some other community, in that case we only allow user to join if their community has some member other than them (happens when the user joins some other community, whose owner
        * */
        const currentUserIsPartOfASingleCommunity = extraData?.communitiesResponse?.communities?.length === 1
        if (noEligibleCommunities) {
             /* if there are 0 communities available to join the challenge (since we only support one challenge per community) */
            satisfies = false
        } else if (moreThanOneEligibleCommunities || onlyEligibleCommunityIsOfSomeoneElse || (currentUserIsPartOfASingleCommunity /* when the user first creates the community before joining any */) ) {
           satisfies = true
        } else {
            /* if only community eligible(not-yet enrolled) is currentUser created community and the user is also part of some other community(which is not eligible to join the challenge) then check if currentUser community has members */
            const membersInOwnCommunity = await socialService.getCommunityMembers(communitiesNotEnrolled[0]?.id, currentUserId, 2, 0)
            if (membersInOwnCommunity.length > 1) {
                /*  then check if it has a member other than us */
                satisfies = true
            }
        }

        return {
            canShowJoinButton: satisfies,
        }
    }

    static isSquadTeamChallenge(challenge: Challenge) {
        return challenge.challengeType === "TEAM" && challenge.tags?.some(tag => tag === SQUAD_CHALLENGE_TAG)
    }

    async isUserEligibleToJoinChallenge(userId: string) {
        const userAcrossCommunities = await this.socialService.getAllUsersAcrossCommunities(userId, 0, 50)
        return  userAcrossCommunities?.length > 0
    }

    static communityIdFromGroupId(groupId: string) {
        return groupId.replace("SOCIAL_", "")
    }

    static getTeamRank(enrolment: EnrolmentResponse, leaderboardId: string) {
        return enrolment.challengeStandings?.find(cs => leaderboardId && cs.leaderboardId === leaderboardId)?.standings?.rank
    }

    static getTeamLeaderboardId(challenge: Challenge) {
        return (challenge as RankedChallenge)?.leaderboardConfigs?.find(lc => lc.leaderBoardType === TEAM_CHALLENGE_TYPE)?.leaderBoardConfigId
    }
}

export default DigitalLeagueChallengeViewBuilder
