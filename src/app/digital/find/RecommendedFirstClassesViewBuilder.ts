import { CFServiceInterfaces } from "../../page/vm/ServiceInterfaces"
import { UserContext } from "@curefit/vm-models"
import { RecommendedClassWidget } from "../TodaysClassWidget"
import { Action } from "@curefit/apps-common"
import { LiveClass, LiveClassSlot, LiveFitWorkoutFormat, VideoStatus } from "@curefit/diy-common"
import * as _ from "lodash"
import { HourMin } from "@curefit/base-common"
import { TimeUtil } from "@curefit/util-common"
import LiveUtil, {
    FIND_YOUR_FIRST_CLASS_LAST_CLASS_LATER, FIND_YOUR_FIRST_CLASS_LAST_CLASS_NOW,
    FIND_YOUR_FIRST_CLASS_MILLISECONDS,
    LIVE_SESSION_ACTION_SOURCE
} from "../../util/LiveUtil"
import { LivePackUtil } from "../../util/LivePackUtil"
import { inject, injectable } from "inversify"
import { SubscriptionStatus } from "@curefit/diy-common"
import { User } from "@curefit/user-common"
import AppUtil from "../../util/AppUtil"
import * as momentTz from "moment-timezone"
import { HamletBusiness } from "@curefit/hamlet-node-sdk"

@injectable()
export class RecommendedFirstClassesViewBuilder {
    title: string
    filters: string[]
    sessions: RecommendedClassWidget[]
    pageAction: Action
    skipAction: Action

    async buildView(serviceInterfaces: CFServiceInterfaces, liveClasses: LiveClass[], userContext: UserContext, filters: string[]) {
        const user = await serviceInterfaces.userService.getUser(userContext.userProfile.userId)
        this.title = user.firstName + ", here are some options for you"
        this.pageAction = {
            title: "TRY AGAIN",
            actionType: "NAVIGATION",
            url: "curefit://userform?formId=LIVE_Find_Your_First_Class",
            analyticsData: {
                eventKey: "button_click_event",
                eventData: {
                    source: "find_your_first_class",
                    productType: "LIVE_FITNESS",
                    title: "try again",
                    actionType: "try again",
                }
            },
        }
        this.skipAction = {
            title: "SKIP",
            actionType: "NAVIGATION",
            url: "curefit://livefitclp?pageId=live",
            analyticsData: {
                eventKey: "button_click_event",
                eventData: {
                    source: "findyourfirstclass",
                    productType: "LIVE_FITNESS",
                    title: "Close",
                    actionType: "Close",
                }
            }
        }
        const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, serviceInterfaces.hamletBusiness)
        const blockInternationalUser = await AppUtil.blockFitnessContentForInternationalUser(userContext, serviceInterfaces.maxmindService, serviceInterfaces.logger)
        this.filters = _.isEmpty(filters) ? [] : filters
        const currentTimeInMillis = Date.now()
        const today = momentTz(Date.now()).tz(userContext.userProfile.timezone).day()
        const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(serviceInterfaces.cultBusiness, serviceInterfaces.diyService, userContext)
        this.sessions = await Promise.all(liveClasses.map(async (liveClass, index) => {
            const filteredSlots = this.getSlotsBasedOnWorkoutTime(liveClass.slots, userContext.userProfile.timezone, currentTimeInMillis)
            if (_.isEmpty(filteredSlots)) {
                return undefined
            }
            const durationHourMin: HourMin = TimeUtil.convertDurationSecondsToHourMin(liveClass.duration / 1000)
            let formattedTimeString: string = durationHourMin.min > 0 ? durationHourMin.min + " Min" : ""
            if (durationHourMin.hour > 0) {
                formattedTimeString = durationHourMin.hour + " Hr " + formattedTimeString
            }
            // const description = this.getDescription(slot, workoutTime, timeInMillis)
            const intensity = liveClass.intensityLevel && !(liveClass.format === "MIND_PODCAST") && !LiveUtil.isVanillaLiveFitFormat(liveClass.format) ? liveClass.intensityLevel : ""
            const isInternationalApp = AppUtil.isInternationalApp(userContext)
            let timeSlots = await Promise.all(filteredSlots.map(async slot => {
                const action: Action = await this.getSlotAction(slot.classId, slot.subscriptionStatus, slot.status, liveClass.liveClassId, liveClass.format, liveClass.contentCategory, serviceInterfaces, userContext, user, slot.locked, isUserEligibleForMonetisation, isUserEligibleForTrial, "find_your_first_class", bucketId, blockInternationalUser)
                action.analyticsData = {
                    ...action.analyticsData,
                    eventData: {
                        ...action.analyticsData?.eventData,
                        position: index
                    }
                }
                const momentTzSlot = momentTz(slot.scheduledTimeEpoch).tz(userContext.userProfile.timezone)
                return {
                    title: momentTzSlot.format("hh:mm A"),
                    action: action,
                    isLocked: isInternationalApp && AppUtil.isLiveContentLocked(userContext, slot.locked, isUserEligibleForMonetisation, isUserEligibleForTrial),
                    isScheduled: slot.subscriptionStatus === "SUBSCRIBED",
                    isLive: slot.status === "LIVE",
                    actionSlotText: momentTzSlot.day() === today ? "TODAY" : (momentTzSlot.day() - 1) === today ? "TOMORROW" : momentTzSlot.format("MMM DD")
                }
            }))
            const moreSlot = {
                title: "MORE",
                action: LiveUtil.getLiveSessionDetailActionFromLiveClassId(liveClass.liveClassId, liveClass.slots[0].classId, "find_your_first_class", "LIVE_FITNESS", userContext),
                isLocked: false,
                isLive: false,
                isScheduled: false,
                actionSlotText: ""
            }
            const mainTimeSlot = timeSlots[0]
            const classAction = timeSlots[0].action
            timeSlots = timeSlots.slice(1, 6)
            if (!_.isEmpty(timeSlots)) {
                timeSlots.push(moreSlot)
            }
            const recommendedClassWidget: RecommendedClassWidget = {
                widgetType: "SESSION_SUMMARY_WIDGET",
                image: LiveUtil.getImage(liveClass.bannerImages, userContext.sessionInfo, 1),
                title: liveClass.title,
                intensity: intensity,
                durationInMillis: liveClass.duration,
                format: liveClass.format,
                duration: formattedTimeString,
                timeslots: timeSlots,
                description: undefined,
                scheduleTime: liveClass.slots[0].scheduledTimeEpoch,
                action: {
                    ...classAction,
                    title: "BOOK NOW FOR " + mainTimeSlot.title.replace(" ", "") + " " + mainTimeSlot.actionSlotText
                },
                isMasterClass: liveClass.isMasterClass,
                trainers: liveClass.trainerName,
                isLocked: isInternationalApp && AppUtil.isLiveContentLocked(userContext, mainTimeSlot.isLocked, isUserEligibleForMonetisation, isUserEligibleForTrial),
                cardAction: undefined,
                videoUrl: undefined
            }
            return recommendedClassWidget
        }))
        this.sessions = this.sessions.filter(session => session)
        this.sessions = this.sessions.sort((a: RecommendedClassWidget, b: RecommendedClassWidget) => {
            if (a.intensity === b.intensity) {
                if (a.scheduleTime !== b.scheduleTime) {
                    return a.scheduleTime - b.scheduleTime
                }
                return 0
            }
            switch (a.intensity) {
                case "BEGINNER":
                    return -1
                    break
                case "MODERATE":
                    if (b.intensity === "BEGINNER") {
                        return 1
                    }
                    return -1
                    break
                case "INTERMEDIATE":
                    if (b.intensity === "BEGINNER" || b.intensity === "MODERATE") {
                        return 1
                    }
                    return -1
                    break
                case "ADVANCED":
                default:
                    return 1
            }
        })
        this.sessions = this.sessions.slice(0, 6)
        return this
    }

    /*private getDescription(slot: LiveClassSlot, workoutTime: string, currentMillis: number) {
        if (workoutTime !== "now") {
            return ""
        }
        if (slot.scheduledTimeEpoch < currentMillis) {
            return "Just Started"
        }
        const descriptionHourMin: HourMin = TimeUtil.convertDurationSecondsToHourMin(Math.abs(slot.scheduledTimeEpoch - currentMillis) / 1000)
        let description: string = (descriptionHourMin.min > 0 ? descriptionHourMin.min + " Min" : "")
        if (descriptionHourMin.hour > 0) {
            description = descriptionHourMin.hour + " Hr " + description
        }
        return "Starts in " + description
    }*/

    /*private async getSlotAction(classId: string, subscriptionStatus: SubscriptionStatus, status: VideoStatus, liveClassId: string, format: LiveFitWorkoutFormat,
        contentCategory: string, interfaces: CFServiceInterfaces, userContext: UserContext, user: User, isLocked: boolean, isUserEligibleForLivePackMonetisation: boolean,
        isUserEligibleForLivePackTrial: boolean, source: LIVE_SESSION_ACTION_SOURCE, workoutTime: string): Promise<Action> {
        if (workoutTime === "now") {
            let action
            if (status === "LIVE") {
                const liveSession = await interfaces.diyService.getDigitalCatalogueEntry(classId)
                action = LiveUtil.getLiveVideoPlayerAction(userContext, liveSession, userContext.sessionInfo, source)
            } else if (subscriptionStatus === "SUBSCRIBED") {
                return LiveUtil.getLiveSessionDetailActionFromLiveClassId(liveClassId, classId, source, contentCategory, userContext)
            } else {
                const liveSession = await interfaces.diyService.getDigitalCatalogueEntry(classId)
                action = await LiveUtil.getVideoSubscribeAction(user, userContext, liveSession, [], userContext.sessionInfo, source, false, interfaces.classInviteLinkCreator, userContext.userProfile.timezone)
            }
            return LiveUtil.getLiveSessionAction(isUserEligibleForLivePackMonetisation, isLocked, action, isUserEligibleForLivePackTrial, userContext, format, source)
        }
        const action = LiveUtil.getLiveSessionDetailActionFromLiveClassId(liveClassId, classId, source, contentCategory, userContext)
        action.title = "PICK A SLOT"
        return action
    }*/

    private async getSlotAction(classId: string, subscriptionStatus: SubscriptionStatus, status: VideoStatus, liveClassId: string, format: LiveFitWorkoutFormat,
                                contentCategory: string, interfaces: CFServiceInterfaces, userContext: UserContext, user: User, isLocked: boolean, isUserEligibleForLivePackMonetisation: boolean,
                                isUserEligibleForLivePackTrial: boolean, source: LIVE_SESSION_ACTION_SOURCE, bucketId: string, blockInternationalUser: boolean): Promise<Action> {
        let action: Action
        if (status === "LIVE") {
            const liveSession = await interfaces.diyService.getDigitalCatalogueEntry(classId)
            action = LiveUtil.getLiveVideoPlayerAction(userContext, user, liveSession, userContext.sessionInfo, source)
            action.title = "\u2022 JOIN LIVE WORKOUT"
        } else if (subscriptionStatus === "SUBSCRIBED") {
            return LiveUtil.getLiveSessionDetailActionFromLiveClassId(liveClassId, classId, source, contentCategory, userContext, "BOOKED")
        } else {
            const liveSession = await interfaces.diyService.getDigitalCatalogueEntry(classId)
            action = await LiveUtil.getVideoSubscribeAction(user, userContext, liveSession, [], userContext.sessionInfo, source, false, interfaces.classInviteLinkCreator, userContext.userProfile.timezone)
            action.title = "BOOK NOW"
        }
        return LiveUtil.getLiveSessionAction(isUserEligibleForLivePackMonetisation, isLocked, action, isUserEligibleForLivePackTrial, userContext, format, source, bucketId, blockInternationalUser)
    }

    private getSlotsBasedOnWorkoutTime(slots: LiveClassSlot[], timezone: string, currentTimeInMills: number) {
        const momentTodayTz = momentTz(currentTimeInMills).tz(timezone)
        const hour = momentTodayTz.hour()
        const todayDate = new Date(currentTimeInMills)
        const dayOfMonth = todayDate.getDate()
        let workoutTime = "today"
        if (hour > 20) {
            workoutTime = "tomorrow"
        }
        const date = new Date(currentTimeInMills)
        date.setDate(date.getDate() + 1)
        date.setHours(6)
        date.setMinutes(31)
        const tomorrowInMillis = date.getTime()
        if (workoutTime === "tomorrow") {
            return slots.filter(slot => {
                return slot.scheduledTimeEpoch > tomorrowInMillis
            })
        }
        /*const todayDate = new Date(currentTimeInMills)
        todayDate.setHours(23, 59, 59)
        const todayEndInMillis = todayDate.getTime()*/
        return slots.filter((slot, index) => {
            const date = new Date(slot.scheduledTimeEpoch)
            if (date.getDate() !== dayOfMonth) {
                return slot.status != "LIVE" && slot.scheduledTimeEpoch > tomorrowInMillis
            }
            return slot.status != "LIVE"
        })
    }
}
