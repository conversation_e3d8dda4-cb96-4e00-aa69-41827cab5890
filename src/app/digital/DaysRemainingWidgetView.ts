import { UserContext } from "@curefit/userinfo-common"
import * as _ from "lodash"
import { TimeUtil } from "@curefit/util-common"
import { IBaseWidget, DaysRemainingWidget } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import LiveUtil from "../util/LiveUtil"
import AppUtil from "../util/AppUtil"

export class DaysRemainingWidgetView extends DaysRemainingWidget {

  async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams?: {
    [filterName: string]: string;
  }): Promise<DaysRemainingWidget> {
    if (AppUtil.isInternationalApp(userContext)) {
      return undefined
    }

    if (userContext.sessionInfo.osName === "ios") {
      return undefined
    }

    if (!AppUtil.isLivePackSupported(userContext)) {
      return undefined
    }
    const { userProfile } = userContext
    const { timezone } = userProfile

    if (userProfile.userId === "0") {
      return undefined
    }

    let membership
    try {
      membership = await interfaces.diyService.getMembershipDetails(userProfile.userId, AppUtil.getTenantFromUserContext(userContext))
      const product = await interfaces.catalogueService.getProduct(membership.productId)
      if (product.appleIAPProductDetails?.productId || product.androidIAPProductDetails?.productId) {
        return undefined
      }
    } catch (e) {
      return undefined
    }

    const widgetSource = !_.isEmpty(queryParams) && !_.isEmpty(queryParams.source) ? queryParams.source : "live_clp"

    const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDate(timezone), timezone)
    const endDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(timezone, TimeUtil.parseDateFromEpoch(membership.end)), timezone)
    const startDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(timezone, TimeUtil.parseDateFromEpoch(membership.start)), timezone)
    const daysRemaining = endDate.diff(today, "days")
    const daysTotal = endDate.diff(startDate, "days")

    if (daysRemaining > 14) {
      return undefined
    }
    // We're showing DaysRemainingWidget on iOS only for trial period. Post purchase, reccrring flow is there.
    if (daysTotal > 14 && userContext.sessionInfo.osName === "ios") {
      return undefined
    }

    const { daysRemainingStyles, daysTotalStyles, titleStyles, rootStyles } = this.getStyles(daysRemaining)

    this.trialDays = {
      daysRemaining: daysRemaining > 0 ? daysRemaining : 0,
      daysTotal,
      daysRemainingStyles,
      daysTotalStyles
    }
    const isTrial = !_.isEmpty(membership.metadata) && !_.isEmpty(membership.metadata.isTrial) ? membership.metadata.isTrial : false
    this.title = isTrial ? "trial days left" : "membership days left"
    const actionTitle = isTrial ? "RENEW MEMBERSHIP" : "BECOME A MEMBER"
    this.rootStyles = rootStyles
    this.titleStyles = titleStyles
    this.action = {
      actionType: "NAVIGATION",
      title: actionTitle,
      url: "curefit://livefitnessbrowsepage"
    }
    this.action.meta = {
      source: widgetSource
    }
    return this
  }

  getStyles = (daysRemaining: number) => {
    const daysTotalStyles = {
      color: "#00000050"
    }
    const daysRemainingStyles = {
      color: "#000000"
    }
    const rootStyles = {
      backgroundColor: "#ffdaac"
    }
    const titleStyles = {
      color: "#00000050"
    }

    if (daysRemaining > 3) {
      // no style changes
    } else if (daysRemaining > 0 && daysRemaining <= 3) {
      daysRemainingStyles.color = "#b00020"
    } else {
      daysRemainingStyles.color = "#b00020"
      rootStyles.backgroundColor = "#f9d0d0"
    }

    return {
      daysTotalStyles,
      daysRemainingStyles,
      rootStyles,
      titleStyles
    }
  }
}
