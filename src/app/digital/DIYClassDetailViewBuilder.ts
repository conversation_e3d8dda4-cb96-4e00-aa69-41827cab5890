import {
    Action,
    DescriptionWidget,
    ManageOptionsWidgetV2,
    ProductDetailPage, ProductListWidget,
    WidgetView
} from "../common/views/WidgetView"
import { inject, injectable } from "inversify"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService } from "@curefit/diy-client"
import {
    DIYFitnessPackExtended,
    DIYFitnessProductExtended, DIYPack, DIYPackFulfilment, DIYProduct, DIYProductImage,
    ImageData,
} from "@curefit/diy-common"
import { IFeedback } from "@curefit/vm-common"
import { BaseWidget, TemplateWidget, UserContext } from "@curefit/vm-models"
import { PageWidget } from "../page/Page"
import AppUtil from "../util/AppUtil"
import { TimeUtil } from "@curefit/util-common"
import LiveUtil from "../util/LiveUtil"
import { ImageOverlayCardContainerWidget, Tenant, } from "@curefit/apps-common"
import * as _ from "lodash"
import { HeaderWidget } from "@curefit/apps-common/dist/src/widgets/page/interfaces"
import { UrlPathBuilder } from "@curefit/product-common"
import { HourMin } from "@curefit/base-common"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { LivePackUtil } from "../util/LivePackUtil"
import { ICultBusiness } from "../cult/CultBusiness"
import AtlasUtil from "../util/AtlasUtil"
import { BASE_TYPES, Logger } from "@curefit/base"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"

export class DIYClassDetailView extends ProductDetailPage {

    pageType: string
    isFullScreen: boolean
    changeBackgroundColorIndex: number
    bannerImages: ImageData | DIYProductImage
    isRetelecast?: boolean
    refreshPageEpoch: number
    classDuration: number
    isLocked: boolean
    feedback: IFeedback[]
    liveClassId: string

    constructor(widgets: (BaseWidget | WidgetView | PageWidget | TemplateWidget)[], actions: Action[], isFullScreen: boolean, bannerImages: (ImageData | DIYProductImage), isRetelecast: boolean, refreshPageEpoch: number, classDuration: number, isLocked: boolean, liveClassId: string, feedback?: IFeedback[]) {
        super()
        this.widgets = widgets
        this.actions = actions
        this.bannerImages = bannerImages
        this.pageType = "DIYSessionDetail"
        this.isFullScreen = isFullScreen
        this.isRetelecast = isRetelecast
        this.refreshPageEpoch = refreshPageEpoch
        this.classDuration = classDuration
        if (!this.isFullScreen) {
            this.changeBackgroundColorIndex = 1
        }
        this.isLocked = isLocked
        this.feedback = feedback
        this.liveClassId = liveClassId
    }
}

@injectable()
class DIYClassDetailViewBuilder {

    constructor(
        @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) private diyFulfilmentService: IDIYFulfilmentService,
        @inject(CUREFIT_API_TYPES.CultBusiness) private cultBusiness: ICultBusiness,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness) {
    }

    async buildView(userContext: UserContext, productId: string, packId: string, blockIfInternationalUser: boolean , isSwap?: boolean, viewOnly?: boolean): Promise<DIYClassDetailView> {
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        const sessions: DIYFitnessProductExtended[] = await this.diyFulfilmentService.getDIYFitnessProductsByProductIds(userContext.userProfile.userId, [productId], tenant)
        let pack: DIYFitnessPackExtended
        if (!_.isEmpty(packId)) {
            pack = (await this.diyFulfilmentService.getDIYFitnessPacksForIds(userContext.userProfile.userId, [packId]))[0]
        }
        const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(this.cultBusiness, this.diyFulfilmentService, userContext)
        const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, this.hamletBusiness)
        const pageAction: Action = this.getSessionAction(sessions[0], pack, undefined, isUserEligibleForTrial, sessions[0].locked, userContext, isUserEligibleForMonetisation, bucketId, blockIfInternationalUser, isSwap, viewOnly)
        const widgets = []
        widgets.push(...await this.getDetailWidgets(userContext, sessions[0], pack, isUserEligibleForMonetisation))
        const bannerImages = {
            mobileImage: sessions[0].imageDetails.heroImage,
            mobileImageV2: sessions[0].imageDetails.heroImage,
            webImage: sessions[0].imageDetails.thumbnailImage,
            mwebImage: sessions[0].imageDetails.heroImage,
            webImageLarge: sessions[0].imageDetails.thumbnailImage,
            webProductImage: sessions[0].imageDetails.thumbnailImage
        }
        return new DIYClassDetailView(widgets, [pageAction], true, bannerImages, false, -1, sessions[0].duration, sessions[0].locked, productId)
    }

    getSessionAction(session: DIYProduct, pack: DIYPack, packFulfilment: DIYPackFulfilment, isUserEligibleForTrial: boolean, isLocked: boolean, userContext: UserContext, isUserEligibleForMonetisaiton: boolean, bucketId: string, blockIfInternationalUser: boolean, isSwap?: boolean, viewOnly?: boolean): Action {
        if (viewOnly) {
            return null
        }
        let action: Action = {
            actionType: "PLAY_VIDEO",
            content: AtlasUtil.getContentDetailV2(session),
            title: "PLAY",
            meta: {
                content: AtlasUtil.getContentDetailV2(session),
                queryParams: AtlasUtil.getContentMetaV2(session, pack),
                title: session.title,
                packId: pack?.productId,
                productType: session.productType,
                checkDownloadStatus: true
            }
        }
        // if (blockIfInternationalUser) {
        //     action = LiveUtil.getSessionActionForBlockedInternationalUser("PLAY")
        // }
        if (isSwap) {
            action = {
                actionType: "SHOW_SUCCESS_MODAL",
                title: "SWAP WITH TODAY",
                payload: {
                    text: "Class is swapped with today's class",
                    programSessionSwapMeta: {
                      sessionId: session.productId,
                    },
                    eventType: "CLASS_SWAP_" + session.productId,
                },
                completionAction: {
                    actionType: "NAVIGATION",
                    url: "curefit://tabpage?pageId=fitnesshub&selectedTab=cultpassLIVE-Members_NEW"
                }
            }
        }
        return LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisaiton, isLocked, action, isUserEligibleForTrial, session.productType, "diy_page_session_item", bucketId, blockIfInternationalUser, session.format)
    }

    async getDetailWidgets(userContext: UserContext, session: DIYFitnessProductExtended, pack: DIYPack, isUserEligibleForMonetisation: boolean): Promise<(BaseWidget | WidgetView | PageWidget | TemplateWidget)[]> {
        const widgets: (BaseWidget | WidgetView | PageWidget | TemplateWidget)[] = []
        const isHeaderEnabled = !AppUtil.isSugarFitOrUltraFitApp(userContext)
        widgets.push(await this.getImageOverlayCardContainerWidget(session, pack, isHeaderEnabled))
        widgets.push(...this.getDescriptionWidgets(userContext, session, isUserEligibleForMonetisation))
        return widgets
    }

    getImageOverlayCardContainerWidget(session: DIYFitnessProductExtended, pack: DIYPack, isHeaderEnabled?: boolean): ImageOverlayCardContainerWidget {
        const imageOverlayContainerWidget = new ImageOverlayCardContainerWidget()
        imageOverlayContainerWidget.assets.push({
            assetType: "IMAGE",
            assetUrl: UrlPathBuilder.prefixSlash(session.imageDetails.todayImage)
        })
        imageOverlayContainerWidget.widgets.push(this.getManageOptionsWidget(session, pack, isHeaderEnabled))
        let intensity = ``
        if (!_.isEmpty(session.intensityLevel)) {
            intensity = session.intensityLevel.charAt(0).toUpperCase() + session.intensityLevel.slice(1).toLowerCase()
        }
        const headerWidget: HeaderWidget = {
            widgetType: "HEADER_WIDGET",
            widgetTitle: {
                title: session.title,
                subTitle: undefined
            },
            headerStyle: {
                marginLeft: 0,
                fontSize: 22,
                color: "#000000",
                width: "100%"
            },
            subTitleStyle: {
                marginLeft: 0,
                fontSize: 14
            },
            subHeader: {
                text: (_.isEmpty(intensity)
                        ? `${session.trainerName || ""}`
                        : (_.isEmpty(session.trainerName)
                        ? `${intensity || ""}`
                        : `${session.trainerName} · ${intensity}`))
            },
            tagIcon: session.isMasterClass ? "MASTERCLASS_TAG" : undefined
        }
        imageOverlayContainerWidget.widgets.push(headerWidget)
        return imageOverlayContainerWidget
    }

    getManageOptionsWidget(selectedClassResponse: DIYFitnessProductExtended, diyPack: DIYPack, isHeaderEnabled: boolean = true): ManageOptionsWidgetV2 {
        const durationHourMin: HourMin = TimeUtil.convertDurationSecondsToHourMin(selectedClassResponse.duration / 1000)
        let formattedTimeString: string = durationHourMin.min > 0 ? durationHourMin.min + " Min" : ""
        if (durationHourMin.hour > 0) {
            formattedTimeString = durationHourMin.hour + " Hr " + formattedTimeString
        }
        const isPremiere = false
        const title = `${formattedTimeString}`
        return {
            widgetType: "MANAGE_OPTIONS_WIDGET_V2",
            title,
            moreAction: undefined,
            style: isPremiere ? { backgroundColor: "#3888ff" } : undefined,
            icon: isPremiere ? "/image/livefit/app/star_white.png" : undefined,
            headerAction: diyPack && isHeaderEnabled ? LiveUtil.getDIYShareAction(diyPack?.productId, selectedClassResponse.productId, selectedClassResponse.productType, diyPack?.title, selectedClassResponse.title) : undefined,
            headerActionIcon: diyPack && isHeaderEnabled ? "SHARE_ICON" : undefined,
            isLocked: selectedClassResponse.locked
        }
    }

    getDescriptionWidgets(userContext: UserContext, session: DIYFitnessProductExtended, isUserEligibleForMonetisation: boolean): (BaseWidget | WidgetView | PageWidget | TemplateWidget)[] {
        const isWeb = AppUtil.isWeb(userContext)
        const widgets: (BaseWidget | WidgetView | PageWidget | TemplateWidget)[] = []
        // Show become a member widget if the session is locked
        if (isUserEligibleForMonetisation) {
            if (session.locked && !AppUtil.isInternationalApp(userContext)) {
                const packBenefitsWidget = LivePackUtil.getLivePackBenefitsWidget(isWeb)
                widgets.push(packBenefitsWidget)
            }
        }
        widgets.push(this.getDescriptionWidget(session))
        return widgets
    }

    getDescriptionWidget(session: DIYFitnessProductExtended): DescriptionWidget {
        const descriptionWidget: DescriptionWidget = {
            widgetType: "DESCRIPTION_WIDGET",
            showDivider: false,
            hasDividerBelow: false,
            descriptions: [{
                title: "About",
                subTitle: session.description
                // moreIndex: 168
            }],
            containerStyle: {
                paddingTop: 0,
            },
        }
        return descriptionWidget
    }
}

export default DIYClassDetailViewBuilder
