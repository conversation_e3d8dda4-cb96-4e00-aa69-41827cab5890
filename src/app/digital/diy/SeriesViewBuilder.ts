import { injectable } from "inversify"
import { DIYSeries } from "@curefit/diy-common"
import { Action } from "@curefit/apps-common"
import { UserContext } from "@curefit/userinfo-common"
import { ActionUtil, ActionUtilV1 } from "@curefit/base-utils"
import AppUtil from "../../util/AppUtil"
import { ProductType } from "@curefit/product-common"
import * as _ from "lodash"
import { SeoUrlParams } from "@curefit/base-utils/dist/src/ActionUtil"

@injectable()
class SeriesViewBuilder {

    public buildView(userContext: UserContext, seriesList: DIYSeries[], productType: ProductType) {
        const widgets: (LiveHeaderWidget | LiveTilesView)[] = []
        const isMasterclass: boolean = false
        const titleLiveHeaderWidget: LiveHeaderWidget = new LiveHeaderWidget()
        titleLiveHeaderWidget.title = "Fitness Programs"
        widgets.push(titleLiveHeaderWidget)

        const subtitleLiveHeaderWidget: LiveHeaderWidget = new LiveHeaderWidget()
        titleLiveHeaderWidget.subtitle = "Reach your fitness goals with guided programs created by our world-class coaches"
        widgets.push(subtitleLiveHeaderWidget)

        const seriesItems = seriesList.map(series => {
            const livePackSeriesTile: LivePackSeriesTile = new LivePackSeriesTile()
            livePackSeriesTile.title = series.name
            livePackSeriesTile.subtitle = "" + series.packIds.length + (series.packIds.length > 1 ? " Weeks" : " Week")
            livePackSeriesTile.image = {
                url: _.isEmpty(series.clpImage) ? series.image : series.clpImage,
                cloudinaryParams: _.isEmpty(series.clpImage) ? ["c_fill", "g_face"] : [],
            }
            livePackSeriesTile.cardAction = {
                actionType: "NAVIGATION",
                url: this.getDIYSeriesActionUrl(userContext, series, productType)
            }
            return livePackSeriesTile
        })
        const liveTileView = new LiveTilesView()
        liveTileView.items = seriesItems

        widgets.push(liveTileView)
        return { widgets: widgets, isMasterclass: isMasterclass}
    }

    private getDIYSeriesActionUrl(userContext: UserContext,  series: DIYSeries, productType: ProductType): string {
        if (!AppUtil.isNewDIYSeriesViewSupported(userContext)) {
            if (!AppUtil.isWeb(userContext)) {
                return ActionUtil.diyPackBrowseV2(series.seriesId, series.name, productType)
            }

            return ActionUtilV1.diySeriesPage(series, userContext.sessionInfo.userAgent)
        }
        return this.diyPackBrowseV3(series, productType, userContext)
    }

    private diyPackBrowseV3(series: DIYSeries, productType: ProductType, userContext: UserContext): string {
        if (!AppUtil.isWeb(userContext)) {
            let url = "curefit://diybrowsepagev3"
            const obj = { seriesId: series.seriesId, name: series.name, productType: productType }
            url += ActionUtil.serializeAsQueryParams(obj)
            return url
        }
        const obj = { seriesId: series.seriesId, name: series.name, productType: productType, pageType: "diyseriesbrowsepageV3" }
        let url = productType === "DIY_FITNESS" ? "curefit://fitness-programs" : "curefit://mindfulness-programs"
        url += ActionUtil.serializeAsQueryParams(obj)
        return url
    }
}

export class LiveHeaderWidget {
    widgetType: string = "LIVE_HEADER_WIDGET"
    title: string
    subtitle: string
}

export class LiveTilesView {
    widgetType: string = "LIVE_TILES_VIEW"
    items: LivePackSeriesTile[]
}

export class LivePackSeriesTile {
    widgetType: string = "LIVE_PACK_SERIES_TILE"
    title: string
    subtitle: string
    image: {
        url: string,
        cloudinaryParams: string[]
    }
    cardAction: Action
}

export default SeriesViewBuilder
