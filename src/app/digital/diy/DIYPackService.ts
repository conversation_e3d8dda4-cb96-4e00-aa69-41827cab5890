import { inject, injectable } from "inversify"
import { Di<PERSON><PERSON><PERSON>tatus, DIYUserFitnessPack, DIYUserMeditationPack } from "@curefit/diy-common"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService } from "@curefit/diy-client"
import { DIYFitnessPack, DIYMeditationPack } from "@curefit/diy-common"

@injectable()
class DIYPackService {

    constructor(
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) private DIYFulfilmentService: IDIYFulfilmentService,
    ) {
    }

    async getMindDIYPackV2(packId: string, userId: string): Promise<DIYUserMeditationPack> {
        return {
            pack: await this.catalogueService.getDIYMeditationPack(packId),
            fulfilment: (await this.DIYFulfilmentService.getDIYPackFulfilmentsForUser(Array.of(packId), userId))[0]
        }
    }

    async getFitnessDIYPackV2(packId: string, userId: string): Promise<DIYUserFitnessPack> {
        return {
            pack: await this.catalogueService.getDIYFitnessPack(packId),
            fulfilment: (await this.DIYFulfilmentService.getDIYPackFulfilmentsForUser(Array.of(packId), userId))[0]
        }
    }


    browseMindDIYPacksV2(userId: string, displayStatus: DisplayStatus): Promise<(DIYMeditationPack)[]> {
        return this.catalogueService.getDIYMeditationPacks(displayStatus).then(result => {
            return result
        })
    }

    browseFitnessDIYPacksV2(userId: string, displayStatus: DisplayStatus): Promise<(DIYFitnessPack)[]> {
        return this.catalogueService.getDIYFitnessPacks(displayStatus).then(result => {
            return result
        })
    }

}

export default DIYPackService
