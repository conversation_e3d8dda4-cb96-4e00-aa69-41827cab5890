import { injectable, inject } from "inversify"
import * as momentTz from "moment-timezone"
import { ProductDetailPage, Action, ActionType } from "../common/views/WidgetView"
import { PageWidget } from "../page/Page"
import { DatesAvailableWidget, DateWiseSlots, DigitalTimeSlotCategory } from "../page/PageWidgets"
import { DigitalCatalogueEntryV1, DigitalCatalogueSearchRequest } from "@curefit/diy-common"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService } from "@curefit/diy-client"
import { UserContext, VideoCardItem } from "@curefit/vm-models"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { User } from "@curefit/user-common"
import LiveUtil from "../util/LiveUtil"
import { ICultBusiness } from "../cult/CultBusiness"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CacheHelper } from "../util/CacheHelper"
import { ClassInviteLinkCreator } from "../cult/invitebuddy/ClassInviteLinkCreator"
import * as _ from "lodash"
import { ProductType } from "@curefit/product-common"
import AppUtil from "../util/AppUtil"

class CalendarView extends ProductDetailPage {

    constructor(widgets: PageWidget[], actions: Action[]) {
        super()
        this.widgets = widgets
        this.actions = actions
    }
}

@injectable()
class DigitalCalendarViewBuilder {

    constructor(
        @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) private diyFulfilmentService: IDIYFulfilmentService,
        @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
        @inject(CUREFIT_API_TYPES.CultBusiness) private cultBusiness: ICultBusiness,
        @inject(CUREFIT_API_TYPES.ClassInviteLinkCreator) private classInviteLinkCreator: ClassInviteLinkCreator,
    ) {
    }

    async buildView(userContext: UserContext, productType?: ProductType): Promise<CalendarView> {
        const widgets: PageWidget[] = []
        const actions: Action[] = []

        // const { userProfile } = userContext
        // const timezone = userContext.userProfile.timezone
        // const user: User = await userContext.userPromise
        // const searchRequest: DigitalCatalogueSearchRequest = {
        //     statuses: ["UPCOMING", "PREPARED", "LIVE"],
        //     categories: ["LIVE"],
        //     scheduledTimeEpochOptions: {
        //         endEpoch: momentTz.tz(timezone).valueOf() + TimeUtil.TIME_IN_MILLISECONDS.DAY * 7
        //     }
        // }
        // if (!_.isEmpty(productType)) {
        //     if (productType === "MIND") {
        //         searchRequest.formats = ["YOGA"]
        //     } else if (productType === "DIY_MEDITATION") {
        //         searchRequest.formats = ["YOGA", "MEDITATION"]
        //     }
        // }
        // const liveVideosResponse: DigitalCatalogueEntryV1[] = await this.diyFulfilmentService.searchDigitalCatalogue(searchRequest)
        // const upcomingSessionIds = liveVideosResponse.map(liveVideo => (<any>liveVideo)._id)
        // const cultPreference: PreferenceDetail = (await eternalPromise(this.cultBusiness.getClassRemindersPreference(userContext, userContext.sessionInfo.sessionData.cityId, userContext.userProfile.userId, "FITNESS"))).obj
        // const subscribedVideos: string[] = await this.diyFulfilmentService.getSubscribedVideos(userProfile.userId)
        // const upcomingSubscribedVideos = _.intersection(upcomingSessionIds, subscribedVideos)
        // const socialData = await this.diyFulfilmentService.getSocialDataForSessionsAsMap(userProfile.userId, upcomingSubscribedVideos, !AppUtil.isLiveLazyInviteLinkActionSupported(userContext))
        // const bookingPref = false   // (_.isEmpty(cultPreference)) ? false : cultPreference.bookingEmailPreference
        // const videoCards: VideoCardItem[] = await LiveUtil.getLiveVideoCards(
        //     user,
        //     userContext,
        //     liveVideosResponse,
        //     subscribedVideos,
        //     true,
        //     bookingPref,
        //     this.classInviteLinkCreator,
        //     this.userCache,
        //      socialData
        // )

        // const dates: string[] = TimeUtil.getDays(timezone, 7)
        // const datesMap: { [key: string]: DigitalTimeSlotCategory[] } = this.getDatesMapForLiveVideo(videoCards, timezone)

        // const datesAvailable: DateWiseSlots[] = dates.map(date => {
        //     const dateWiseSlot: DateWiseSlots = {
        //         date,
        //         timeZones: [],
        //         dateType: TimeUtil.getMomentForDateString(date, timezone).isoWeekday() >= 6 ? "WEEKEND" : "WEEKDAY"
        //     }
        //     if (datesMap[date]) {
        //         dateWiseSlot.timeZones = datesMap[date]
        //     } else {
        //         dateWiseSlot.noSlotsAvailable = true
        //         dateWiseSlot.noSlotText = "No streams available"
        //     }
        //     return dateWiseSlot
        // })
        // const actionType: ActionType = "NAVIGATION"
        // const action: Action = {
        //     actionType: actionType,
        //     url: ""
        // }
        // widgets.push(new DatesAvailableWidget(datesAvailable, action.url, action, undefined, undefined, undefined, undefined, undefined, true))
        return new CalendarView(widgets, actions)
    }

    getDatesMapForLiveVideo(videoCards: VideoCardItem[], timezone: Timezone): {[key: string]: DigitalTimeSlotCategory[]} {
        const datesMap: {[key: string]: DigitalTimeSlotCategory[]} = {}
        for (let i = 0 ; i < videoCards.length ; i++) {
            const videoResponse = videoCards[i]
            const date = TimeUtil.formatEpochInTimeZone(timezone, videoResponse.footer.startTimeEpoch, "YYYY-MM-DD")
            if (!datesMap[date]) {
                datesMap[date] = []
            }
            datesMap[date].push({
                videoCard: videoResponse,
                title: TimeUtil.formatEpochInTimeZone(timezone, videoResponse.footer.startTimeEpoch, "hh:mm A")
            })
        }
        return datesMap
    }
}

export default DigitalCalendarViewBuilder
