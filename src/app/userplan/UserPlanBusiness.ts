import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import * as _ from "lodash"
import { Logger, BASE_TYPES } from "@curefit/base"
import {
    IPlanActionWidget,
    IPlanSuggestionWidget,
    PlanActionWidget,
    PlanSuggestionWidget
} from "../common/views/PlanSuggestionWidget"
import { ICatalogueService, CATALOG_CLIENT_TYPES, ICatalogueServicePMS } from "@curefit/catalog-client"
import { IMetricServiceClient as IMetricService, METRIC_TYPES } from "@curefit/metrics"
import { UserContext } from "@curefit/userinfo-common"
import { IMetricsCardsWidget, IMetricWidgetBuildParams, MetricsWidget } from "../common/views/MetricsWidget"
import { GMF_CLIENT_TYPES, IGMFClient } from "@curefit/gmf-client"
import { UserGuidelineView } from "@curefit/gmf-common"
import { IHealthfaceService, ALBUS_CLIENT_TYPES } from "@curefit/albus-client"
import IssueBusiness from "../crm/IssueBusiness"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService } from "@curefit/diy-client"

export interface IUserPlanBusiness {
    getUserPlanView(userContext: UserContext): Promise<UserPlanView>
}

type IPlanPageWidgetTypes = IPlanSuggestionWidget | IMetricsCardsWidget | IPlanActionWidget
export interface UserPlanView {
    widgets: IPlanPageWidgetTypes[]
}

@injectable()
export class UserPlanBusiness implements IUserPlanBusiness {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(GMF_CLIENT_TYPES.IGMFClient) private gmfClient: IGMFClient,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReaderPMS) private catalogueServicePMS: ICatalogueServicePMS,
        @inject(METRIC_TYPES.MetricServiceClient) private metricService: IMetricService,
        @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
        @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness,
        @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) private diyService: IDIYFulfilmentService
    ) {
    }

    public async getUserPlanView(userContext: UserContext): Promise<UserPlanView> {
        const userPlanView: UserPlanView = {
            widgets: []
        }
        const userPlanDetail = await this.gmfClient.fetchPlanDetail(userContext.userProfile.userId)
        const guidelines: UserGuidelineView[] = userPlanDetail.guidelines as UserGuidelineView[]
        const filteredGuidelines: {
            careGuidelines: UserGuidelineView[],
            otherGuidelines: UserGuidelineView[]
        } = this.filterCareGuidelines(guidelines)

        const planWidgetPromises: Promise<void>[] = []

        // Add metrics widget
        const metricsWidget: MetricsWidget = new MetricsWidget()
        userPlanView.widgets.push(metricsWidget)
        const metricWidgetParams: IMetricWidgetBuildParams = {
            userContext: userContext,
            gmfClient: this.gmfClient,
            metricService: this.metricService,
            logger: this.logger
        }
        planWidgetPromises.push(metricsWidget.buildGoalTickersView(metricWidgetParams))

        // Add plan action widgets
        const planActionWidget: PlanActionWidget = new PlanActionWidget()
        userPlanView.widgets.push(planActionWidget)
        planWidgetPromises.push(planActionWidget.buildView(userContext, userPlanDetail.planActions, this.healthfaceService, this.issueBusiness, this.catalogueService))

        const userId = userContext.userProfile.userId
        // Build all widgets which are NOT related to care.fit
        filteredGuidelines.otherGuidelines.forEach((userGuideline: UserGuidelineView) => {
            const userPlanWidget: PlanSuggestionWidget = new PlanSuggestionWidget()
            const widgetBuildPromise: Promise<void> = userPlanWidget.buildView(userId, userGuideline, this.catalogueServicePMS, this.diyService, userContext)
            userPlanView.widgets.push(userPlanWidget)
            planWidgetPromises.push(widgetBuildPromise)
        })

        // Build care.fit widget
        if (!_.isEmpty(filteredGuidelines.careGuidelines)) {
            const carePlanWidget: PlanSuggestionWidget = new PlanSuggestionWidget()
            const widgetBuildPromise: Promise<void> = carePlanWidget.buildCareView(userContext, filteredGuidelines.careGuidelines, this.catalogueService)
            userPlanView.widgets.push(carePlanWidget)
            planWidgetPromises.push(widgetBuildPromise)
        }
        await Promise.all(planWidgetPromises)
        this.removeEmptySections(userPlanView)
        return userPlanView
    }

    private filterCareGuidelines(guidelines: UserGuidelineView[]): {
        careGuidelines: UserGuidelineView[],
        otherGuidelines: UserGuidelineView[]
    } {
        const result: {
            careGuidelines: UserGuidelineView[],
            otherGuidelines: UserGuidelineView[]
        } = {
            careGuidelines: [],
            otherGuidelines: []
        }

        guidelines.forEach((guideline: UserGuidelineView) => {
            if (guideline.category === "FITNESS") {
                result.otherGuidelines.push(guideline)
            } else if (guideline.category === "MIND") {
                result.otherGuidelines.push(guideline)
            } else if (guideline.category === "FOOD") {
                result.otherGuidelines.push(guideline)
            } else if (guideline.category === "CONSULTATION") {
                result.careGuidelines.push(guideline)
            } else if (guideline.category === "DIAGNOSTICS") {
                result.careGuidelines.push(guideline)
            } else if (guideline.category === "HABIT") {
                result.careGuidelines.push(guideline)
            } else if (guideline.category === "MEDICINE") {
                result.careGuidelines.push(guideline)
            }
        })

        return result
    }

    private removeEmptySections(userPlanView: UserPlanView): void {
        const filteredWidgets: IPlanPageWidgetTypes[] = []
        userPlanView.widgets.forEach((planWidget) => {
            if (planWidget instanceof PlanSuggestionWidget) {
                if (!_.isEmpty(planWidget.planItems) || !_.isEmpty(planWidget.packDetails)) {
                    filteredWidgets.push(planWidget)
                }
            } else {
                filteredWidgets.push(planWidget)
            }
        })
        userPlanView.widgets = filteredWidgets
    }

}
