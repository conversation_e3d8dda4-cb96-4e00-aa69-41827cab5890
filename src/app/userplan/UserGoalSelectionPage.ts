import * as _ from "lodash"
import { GoalSelectionDetailWidget, Header, ProductDetailPage } from "../common/views/WidgetView"
import { DiagnosticProductResponse } from "@curefit/care-common"
import { FoodProduct as Product } from "@curefit/eat-common"
import { ActionUtil } from "@curefit/base-utils"
import { RUPEE_SYMBOL } from "@curefit/finance-common"
import { PackOffersResponse } from "@curefit/offer-common"
import { OfferUtil } from "@curefit/base-utils"

const GRADIENT_COLORS: string[][] = [["#5ac3ff", "#8099da"], ["#83f1bd", "#5dc6cb"], ["#5ac3ff", "#8099da"]]

class UserGoalSelectionPage extends ProductDetailPage {
    public header: Header
    private constructor(products: Product[], diagnosticsProducts: DiagnosticProductResponse[], offers: PackOffersResponse, deviceOffers: PackOffersResponse) {
        super()
        this.widgets = products.map((product: Product, index: number) => {
            if (product.productType === "MANAGED_GOAL") {
                const widget: GoalSelectionDetailWidget = {
                    title: "Standard Plan",
                    widgetType: "GOAL_SELECTION_DETAIL_WIDGET",
                    bullets: [
                        "Ideal for generally healthy individuals (no major injuries or medical conditions such as Thyroid etc.)",
                        "All-in-one Personalised plan covering nutrition, fitness and mindfulness",
                        "Includes 2 free consultations with a Lifestyle Coach per month. "
                    ],
                    gradientColors: GRADIENT_COLORS[index],
                    action: {
                        actionType: "NAVIGATION",
                        url: ActionUtil.userFormProductPage(product.productId)
                    },
                    price: {
                        priceCultValue: `${RUPEE_SYMBOL}${product.price.mrp}/mo*`,
                        value: product.price.listingPrice === 0 ? "FREE" : `${RUPEE_SYMBOL}${product.price.listingPrice}/mo*`
                    },
                    showArrow: true
                }
                return widget
            } else if (product.productType === "BUNDLE") {
                const diagnosticsProduct = diagnosticsProducts.find(diagnosticProduct => diagnosticProduct.productCode === product.productId)
                const mandatoryProducts = diagnosticsProduct.childProducts.filter(product => product.childProductType === "CHILD_MANDATORY"
                    && product.baseSellableProduct.subCategoryCode !== "MP_SUBS" && product.baseSellableProduct.categoryCode !== "DEVICE")
                const deviceProducts = diagnosticsProduct.childProducts.filter(product => product.childProductType === "CHILD_MANDATORY" && product.baseSellableProduct.categoryCode === "DEVICE" && product.baseSellableProduct.subCategoryCode !== "INT_WELCOME_KIT")
                const subscriptionProducts = diagnosticsProduct.childProducts.filter(product => product.baseSellableProduct.subCategoryCode === "MP_SUBS")

                let oneTimePrice = mandatoryProducts.map(product => OfferUtil.getPackOfferAndPriceForCare(product.baseSellableProduct, offers).price.listingPrice).reduce((a, b) => a + b)
                let recurringMonthlyPrice, recurringMonthlyPriceWithOffer
                if (!_.isEmpty(deviceProducts)) {
                    oneTimePrice += deviceProducts.map(product => OfferUtil.getPackOfferAndPriceForCare(product.baseSellableProduct, offers).price.listingPrice).reduce((a, b) => a + b)
                }

                if (!_.isEmpty(subscriptionProducts)) {
                    const minSubscriptionProduct = _.minBy(subscriptionProducts, product => product.baseSellableProduct.duration)
                    const durationInMonth = Math.floor(minSubscriptionProduct.baseSellableProduct.duration / 30)
                    const offerPrice = OfferUtil.getPackOfferAndPriceForCare(minSubscriptionProduct.baseSellableProduct, offers)
                    recurringMonthlyPrice = offerPrice.price.mrp / durationInMonth
                    recurringMonthlyPriceWithOffer = (offerPrice.price.listingPrice) / durationInMonth
                }
                const advancedPlanWidget: GoalSelectionDetailWidget = {
                    title: "Advanced Plan",
                    widgetType: "GOAL_SELECTION_DETAIL_WIDGET",
                    gradientColors: GRADIENT_COLORS[index],
                    bullets: [
                        "Comprehensive plan custom designed by a Doctor and a Lifestyle Coach to cover all medical conditions or injuries",
                        "Includes comprehensive diagnostic tests",
                        "Unlimited consultations with the Doctor and Lifestyle Coach"
                    ],
                    action: {
                        actionType: "NAVIGATION",
                        url: ActionUtil.carefitbundle(product.productId, "MP")
                    },
                    price: {
                        priceCultValue: `${RUPEE_SYMBOL}${recurringMonthlyPrice}/mo*`,
                        value: `${RUPEE_SYMBOL}${recurringMonthlyPriceWithOffer}/mo*`,
                        subTitle: `+ ${RUPEE_SYMBOL}${oneTimePrice} (one time)`
                    },
                    showArrow: true
                }
                return advancedPlanWidget
            }
        })

        this.header = {
            title: "Select a Weight Loss Plan"
        }
    }


    public static async getView(products: Product[], diagnosticsProducts: DiagnosticProductResponse[], offers: PackOffersResponse, deviceOffers: PackOffersResponse) {
        return new UserGoalSelectionPage(products, diagnosticsProducts, offers, deviceOffers)
    }
}

export default UserGoalSelectionPage
