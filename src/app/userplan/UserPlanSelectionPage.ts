import { Header, ProductDetailPage } from "../common/views/WidgetView"
import { UserPlanDescriptionWidget } from "../page/PageWidgets"

const GRADIENT_COLORS: string[][] = [["#e051bd", "#f56d6f"], ["#4dc0eb", "#509eeb"]]
const ACTION_COLORS: string[] = ["#BD4397", "#4093B4"]

class UserPlanSelectionPage extends ProductDetailPage {
    public header: Header
    private constructor(productId: string, plans: any) {
        super()
        plans.map((plan: any, index: number) => {
            this.widgets.push(this.userPlanDescriptionWidget(productId, plan, index))
        })
        this.header = {
            title: "My Plan"
        }
    }

    private userPlanDescriptionWidget(productId: string, plan: any, index: number): UserPlanDescriptionWidget {
        return {
            widgetType: "USER_PLAN_DESCRIPTION_WIDGET",
            header: {
                title: plan.sectionHeader,
                subtitle: plan.sectionSubHeader
            },
            optionId: plan.optionId,
            title: plan.title,
            details: plan.details,
            action: {
                actionType: "SELECT_USER_PLAN",
                title: "Select Plan",
                meta: {
                    productId: productId,
                    optionId: plan.optionId
                }
            },
            gradientColor: GRADIENT_COLORS[index],
            actionColor: ACTION_COLORS[index]
        }
    }

    public static async getView(productId: string, plans: any) {
        return new UserPlanSelectionPage(productId, plans)
    }
}

export default UserPlanSelectionPage
