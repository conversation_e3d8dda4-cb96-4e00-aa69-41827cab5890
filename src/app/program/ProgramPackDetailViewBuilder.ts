import { injectable } from "inversify"
import * as _ from "lodash"
import { ActionUtil } from "@curefit/base-utils"
import {
    EvaluationScoreBase,
    InfoListSection,
    InfoSection,
    ProgramPack,
    Schedule
} from "@curefit/program-client"
import {
    Action,
    ChainedAction,
    DescriptionWidget,
    InfoCard,
    ManageOptions,
    ManageOptionsWidget,
    ProductDetailPage,
    ProductListWidget,
    ProductSummaryWidget,
    TabWidget,
    WidgetView
} from "../common/views/WidgetView"
import { UrlPathBuilder } from "@curefit/product-common"
import { ProductPrice } from "@curefit/product-common"
import { ProductType } from "@curefit/product-common"
import { ProgramPackProduct } from "@curefit/product-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { CatalogueServiceUtilities } from "../util/CatalogueServiceUtilities"
import { CenterProgramDetail } from "./IProgramBusiness"
import { CultCenter } from "@curefit/cult-common"
import { OfferUtil } from "@curefit/base-utils"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { PackOffersResponse } from "@curefit/offer-common"
import { CdnUtil } from "@curefit/util-common"

export interface ProgramView {
    date: string
    programId: string
    packId: string
    productId: string
}
export interface CenterProgramView {
    center: CultCenter
    programs: ProgramView[]
}
export interface ProgramPackSummaryWidget extends WidgetView {
    title: string
    offerId?: string,
    offerIds?: string[],
    subTitle: string
    image: string
    price: ProductPrice
    centerProgramDetails: { [centerId: string]: CenterProgramView }
    selectedCenterId: string
}

export interface EvaluationWidget extends WidgetView {
    title: string
    subTitle?: string
    icon: string
    backgroundColor: string
    action: Action
}

@injectable()
export class ProgramPackDetailViewBuilder {

    constructor(
    ) {

    }

    async getView(
        userAgent: UserAgent,
        programPack: ProgramPack,
        membershipId: string,
        programOffers: PackOffersResponse,
        centerProgramDetails?: { [centerId: string]: CenterProgramDetail }, timezone: Timezone = TimeUtil.IST_TIMEZONE): Promise<ProductDetailPage> {
        const productDetailPage: ProductDetailPage = new ProductDetailPage()
        const programPackProduct = CatalogueServiceUtilities.toProgramPack(programPack, "PROGRAM")
        if (userAgent === "APP")
            productDetailPage.widgets.push(this.getProductSummaryWidget(programPackProduct, userAgent, programPack, membershipId, programOffers))
        else
            productDetailPage.widgets.push(this.getWebProductSummaryWidget(programPackProduct, userAgent, centerProgramDetails, membershipId, programOffers, timezone))

        if (!membershipId) {
            productDetailPage.widgets.push(this.getIntroductionVideoWidget(programPackProduct))
        }

        productDetailPage.widgets.push(this.getDescriptionWidget([programPack.infoSections.goal]))

        if (programPack.evaluationId) {
            productDetailPage.widgets.push(this.getEvaluationWidget(programPack, programPackProduct.productType))
        }

        if (membershipId) {
            productDetailPage.actions = [{
                title: "Done",
                actionType: "POP_ACTION"
            }]
            productDetailPage.widgets.push(this.getPlanScheduleWidget(programPack.infoSections.planInfo, programPack.infoSections.schedule))
            productDetailPage.widgets.push(this.getWhatsInPackWidget(programPack.infoSections.whatIsInThePack))

        } else {
            productDetailPage.widgets.push(this.getDescriptionWidget([programPack.infoSections.for]))
            productDetailPage.widgets.push(this.getDescriptionWidget([programPack.infoSections.howItHelps]))
            productDetailPage.widgets.push(this.getWhatsInPackWidget(programPack.infoSections.whatIsInThePack))
            productDetailPage.widgets.push(this.getPlanScheduleWidget(programPack.infoSections.planInfo, programPack.infoSections.schedule))
            if (userAgent === "APP") {
                productDetailPage.actions = [{
                    title: "Get pack",
                    actionType: "NAVIGATION",
                    url: ActionUtil.programOptions(programPack.id, programPackProduct.productId, programPackProduct.productType)
                }]
            } else {
                if (this.hasProgram(centerProgramDetails)) {
                    productDetailPage.actions = [{
                        title: "Get pack",
                        actionType: "GET_PROGRAM_PACK",
                    }]
                }
            }
        }
        return productDetailPage
    }


    hasProgram(centerProgramDetails: { [centerId: string]: CenterProgramDetail }) {
        return Object.keys(centerProgramDetails).length > 0 ? true : false
    }
    getEvaluationWidget(programPack: ProgramPack, productType: ProductType): WidgetView {
        let action: ChainedAction | Action
        const userEvaluation = programPack.meta.userEvaluation
        const evaluation = programPack.meta.evaluation
        let title: string = evaluation.name
        let subTitle: string = undefined
        if (userEvaluation) {
            if (userEvaluation.completed) {
                title = `Score ${Math.round(userEvaluation.score)}/${EvaluationScoreBase}`
                if (userEvaluation.metricRangeBucket) {
                    subTitle = userEvaluation.metricRangeBucket.displayLabel
                }
                action = {
                    title: "Take the quiz again",
                    actionType: "REST_API",
                    meta: {
                        method: "POST",
                        url: "/program/createEvaluation/",
                        body: {
                            evaluationId: evaluation.id,
                            sourceType: "PACK",
                            sourceId: programPack.id
                        }
                    }
                }
            } else {
                action = {
                    title: "Complete quiz",
                    actionType: "OPEN_TYPEFORM",
                    url: userEvaluation.meta.typeFormUrl,
                    nextAction: {
                        actionType: "REST_API",
                        meta: {
                            method: "POST",
                            url: "/program/syncEvaluation/",
                            body: {
                                evaluationId: userEvaluation.evaluationId,
                                userEvaluationId: userEvaluation.id
                            }
                        }
                    }
                }
            }
        } else {
            action = {
                title: "Take the quiz",
                actionType: "REST_API",
                meta: {
                    method: "POST",
                    url: "/program/createEvaluation/",
                    body: {
                        evaluationId: evaluation.id,
                        sourceType: "PACK",
                        sourceId: programPack.id
                    }
                }
            }
        }

        const evaluationWidget: EvaluationWidget = {
            title: title,
            subTitle: subTitle,
            widgetType: "EVALUATION_WIDGET",
            icon: "/image/icons/program/evaluation.png",
            backgroundColor: "#ac9dff",
            action: action
        }
        return evaluationWidget
    }

    getProductSummaryWidget(programPackProduct: ProgramPackProduct, userAgent: UserAgent,
        programPack: ProgramPack,
        membershipId: string,
        programOffers: PackOffersResponse): WidgetView {

        const productSummaryWidget: ProductSummaryWidget = {
            title: programPackProduct.title,
            subTitle: "1 month", // Get from cult
            image: UrlPathBuilder.getPackImagePath(programPackProduct.productId, "PROGRAM", "HERO", Number(programPackProduct.imageVersion), userAgent),
            widgetType: "PRODUCT_SUMMARY_WIDGET"
        }
        if (!membershipId) {
            const offerDetails = OfferUtil.getPackOfferAndPrice(programPackProduct, programOffers)
            productSummaryWidget.price = offerDetails.price
        }

        return productSummaryWidget
    }

    getWebProductSummaryWidget(programPackProduct: ProgramPackProduct, userAgent: UserAgent, centerProgramDetails: { [centerId: string]: CenterProgramDetail },
        membershipId: string,
        programOffers: PackOffersResponse, timezone: Timezone = TimeUtil.IST_TIMEZONE): WidgetView {
        const data: { [centerId: string]: CenterProgramView } = {}
        Object.keys(centerProgramDetails).forEach(centerId => {
            const centerProgramView: CenterProgramView = {
                center: centerProgramDetails[centerId].center,
                programs: _.map(centerProgramDetails[centerId].programs, program => {
                    const programView: ProgramView = {
                        date: TimeUtil.formatDateInTimeZone(timezone, TimeUtil.parseDateFromEpoch(program.startAt)),
                        programId: program.id,
                        packId: programPackProduct.packId,
                        productId: programPackProduct.productId,
                    }
                    return programView
                })
            }
            centerProgramView.programs = centerProgramView.programs.sort((a, b) => {
                return a.date < b.date ? -1 : 1
            })
            data[centerId] = centerProgramView
        })
        const productSummaryWidget: ProgramPackSummaryWidget = {
            title: programPackProduct.title,
            subTitle: "1 month", // Get from cult
            image: UrlPathBuilder.getPackImagePath(programPackProduct.productId, "PROGRAM", "HERO", Number(programPackProduct.imageVersion), userAgent),
            price: programPackProduct.price,
            centerProgramDetails: data,
            selectedCenterId: Object.keys(centerProgramDetails)[0],
            widgetType: "PROGRAM_PACK_SUMMARY_WIDGET"
        }
        if (!membershipId) {
            const offerDetails = OfferUtil.getPackOfferAndPrice(programPackProduct, programOffers)
            productSummaryWidget.price = offerDetails.price
            productSummaryWidget.offerIds = !_.isEmpty(offerDetails.offers) ? _.map(offerDetails.offers, offer => { return offer.offerId }) : undefined
            productSummaryWidget.offerId = !_.isEmpty(productSummaryWidget.offerIds) ? productSummaryWidget.offerIds[0] : undefined

        }
        return productSummaryWidget
    }

    getDescriptionWidget(infoSections: InfoSection[], showDivider: boolean = true): DescriptionWidget {
        const descriptionWidget: DescriptionWidget = {
            widgetType: "DESCRIPTION_WIDGET",
            showDivider: showDivider,
            descriptions: []
        }
        descriptionWidget.descriptions = _.map(infoSections, infoSection => {
            return {
                title: infoSection.title,
                subTitle: infoSection.value.value
            }
        })
        return descriptionWidget
    }

    private getIntroductionVideoWidget(packProduct: ProgramPackProduct) {
        const relativeVideoUrl = "curefit-content/video/" + packProduct.productId + "_INTRO.mp4"
        let videoPlayerUrl = "curefit://videoplayer?videoUrl=" + encodeURIComponent(relativeVideoUrl)
        videoPlayerUrl += "&absoluteVideoUrl=" + encodeURIComponent(CdnUtil.getCdnUrl(relativeVideoUrl))

        const manageOptions: ManageOptions = {
            displayText: "Watch pack introduction",
            icon: "PLAY",
            options: [{
                isEnabled: true,
                displayText: "Watch pack introduction",
                action: videoPlayerUrl,
                type: "PACK_INTRO"
            }]
        }
        return new ManageOptionsWidget(manageOptions, {})
    }
    getWhatsInPackWidget(infoListSection: InfoListSection): ProductListWidget {
        const productListWidget: ProductListWidget = {
            header: {
                title: infoListSection.title
            },
            hideSepratorLines: true,
            items: [],
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "SMALL"
        }
        productListWidget.items = _.map(infoListSection.value, item => {
            const infocard: InfoCard = {
                subTitle: item.line,
                icon: "/image/icons/program/" + item.tag + "/2.png"
            }
            return infocard
        })
        return productListWidget
    }

    getPlanScheduleWidget(planInfo: InfoSection, schedule: Schedule): TabWidget {
        const tabWidget: TabWidget = {
            tabs: [],
            header: {
                title: planInfo.title,
                subTitle: planInfo.value.value
            },
            widgetType: "TAB_WIDGET"
        }
        tabWidget.tabs = _.map(schedule.value, item => {
            const prescription: string = _.reduce(item.value.value, (prescription, item) => {
                return prescription = prescription + "\n" + item.line
            }, "")
            const tab = {
                tabLabel: item.title,
                title: item.value.title,
                subTitle: prescription
            }
            return tab
        })
        return tabWidget
    }
}

export default ProgramPackDetailViewBuilder
