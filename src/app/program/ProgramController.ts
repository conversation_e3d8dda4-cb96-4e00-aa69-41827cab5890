import * as express from "express"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import { Container, inject } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { ICatalogueService, CATALOG_CLIENT_TYPES, CatalogueServiceV2Utilities } from "@curefit/catalog-client"
import {
    CreateUserEvaluationRequest,
    EvaluationSource,
    IProgramService,
    Program,
    SyncUserEvaluationRequest, PROGRAM_CLIENT_TYPES
} from "@curefit/program-client"
import AuthMiddleware from "../auth/AuthMiddleware"
import { Action, ChainedAction, ProductDetailPage } from "../common/views/WidgetView"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { ProductPrice } from "@curefit/product-common"
import { ProductType } from "@curefit/product-common"
import { Session } from "@curefit/userinfo-common"
import ProgramPackDetailViewBuilder from "./ProgramPackDetailViewBuilder"
import { IProgramBusiness } from "./IProgramBusiness"
import * as _ from "lodash"
import { TimeUtil } from "@curefit/util-common"
import { OrderProduct } from "@curefit/order-common"
import { OfferUtil } from "@curefit/base-utils"
import { IOfferServiceV2 } from "@curefit/offer-service-client"
import { UserContext } from "@curefit/userinfo-common"
import { OFFER_SERVICE_CLIENT_TYPES } from "@curefit/offer-service-client"
import AppUtil from "../util/AppUtil"

interface MandatoryClassInfo {
    dates: string[]
    title: string
    subTitle: string
    metaText: string
}
interface ProgramDetail {
    programId: string
    packId: string
    productId: string
    date: string
    offerId?: string
    offerIds?: string[]
    mandatoryClassInfos: MandatoryClassInfo[]
}

interface BestProgramPackDetail {
    checkoutAction: Action
    directCheckout: boolean
    title?: string
    image?: string
    subTitle?: string
    priceHeading?: string
    price?: ProductPrice
}

interface SyncEvaluationRequest {
    productType: ProductType
    userEvaluationId: string
    evaluationId: string
    isEvaluationMandatory?: boolean
}

interface CreateEvaluationRequest {
    evaluationId: string
    sourceType: EvaluationSource
    sourceId: string
}
function controllerFactory(kernel: Container) {

    @controller("/program", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class ProgramController {
        constructor(
            @inject(CUREFIT_API_TYPES.ProgramBusiness) private programBusiness: IProgramBusiness,
            @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) private offerService: IOfferServiceV2,
            @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
            @inject(PROGRAM_CLIENT_TYPES.ProgramService) private programService: IProgramService,
            @inject(CUREFIT_API_TYPES.ProgramPackDetailViewBuilder) private programPackDetailViewBuilder: ProgramPackDetailViewBuilder
        ) {
        }

        @httpGet("/pack/:packId")
        async getMindProgramPack(req: express.Request): Promise<ProductDetailPage> {
            const session: Session = req.session
            const packId: string = req.params.packId
            const membershipId: string = req.query.membershipId
            const userId: string = session.userId
            const userAgent: UserAgent = session.userAgent
            const programPackPromise = this.programBusiness.getProgramPack(userId, packId)
            const osName: string = req.headers["osname"] as string
            const appVersion: string = req.headers["appversion"] as string
            const deviceId = session.deviceId
            const userContext: UserContext = req.userContext as UserContext
            let offersPromise
            if (!membershipId) {
                offersPromise = this.offerService.getProgramPackOffers({
                    userId: userId,
                    cityId: userContext.userProfile.cityId,
                    deviceId: deviceId,
                    source: AppUtil.callSourceFromContext(userContext)
                })
            }
            let centerProgramPromise = undefined
            if (userAgent !== "APP") {
                centerProgramPromise = this.programBusiness.getCenterProgramDetails(userId, packId)
            }
            const programPack = await programPackPromise
            const centerProgramDetails = await centerProgramPromise
            const offerDetails = await offersPromise

            return this.programPackDetailViewBuilder.getView(userAgent, programPack, membershipId, offerDetails, centerProgramDetails, userContext.userProfile.timezone)
        }

        @httpPost("/createEvaluation")
        async createEvaluation(req: express.Request): Promise<{ action: Action }> {
            const session: Session = req.session
            const createRequest: CreateEvaluationRequest = req.body
            const userId: string = session.userId
            const userAgent: UserAgent = session.userAgent
            const createUserEvaluationRequest: CreateUserEvaluationRequest = {
                evaluationId: createRequest.evaluationId,
                sourceId: createRequest.sourceId,
                sourceType: createRequest.sourceType,
                userId: userId
            }
            const response = await this.programService.createUserEvaluation(createUserEvaluationRequest)
            const action: ChainedAction = {
                actionType: "OPEN_TYPEFORM",
                url: response.meta.typeFormUrl,
                nextAction: {
                    actionType: "REST_API",
                    meta: {
                        method: "POST",
                        url: "/program/syncEvaluation/",
                        body: {
                            evaluationId: response.evaluationId,
                            userEvaluationId: response.id
                        }
                    }
                }
            }
            return { action: action }
        }

        @httpGet("/bestpack")
        async bestMindProgramPack(req: express.Request): Promise<BestProgramPackDetail> {
            const session: Session = req.session
            const programId: string = req.query.programId
            const packId: string = req.query.packId
            const userId: string = session.userId
            const userAgent: UserAgent = session.userAgent
            const productId = CatalogueServiceV2Utilities.getProgramPackProductId(packId)
            return {
                directCheckout: true,
                checkoutAction: {
                    actionType: "NAVIGATION",
                    url: `curefit://checkoutv1?payload=${this.checkoutPayload(productId, programId)}`
                }
            }
            // const bestprogramPack = await this.programBusiness.getBestPackForProgram(userId, programId)
            // if (bestprogramPack.id === packId) {
            //     const productId = CatalogueServiceV2Utilities.getProgramPackProductId(packId)
            //     return {
            //         directCheckout: true,
            //         checkoutAction: {
            //             actionType: "NAVIGATION",
            //             url: `curefit://checkoutv1?payload=${this.checkoutPayload(productId, programId)}`
            //         }
            //     }
            // } else {
            //     const productId = CatalogueServiceV2Utilities.getProgramPackProductId(bestprogramPack.id)
            //     return {
            //         directCheckout: false,
            //         checkoutAction: {
            //             title: "Proceed to payment",
            //             actionType: "NAVIGATION",
            //             url: `curefit://checkoutv1?payload=${this.checkoutPayload(productId, programId)}`
            //         },
            //         price: {
            //             mrp: bestprogramPack.price,
            //             listingPrice: bestprogramPack.price
            //         },
            //         priceHeading: "Updated price",
            //         image: UrlPathBuilder.getPackImagePath(productId, "PROGRAM", "MAGAZINE", bestprogramPack.meta.mediaVersion, userAgent),
            //         title: "Your membership will expire during this programme",
            //         subTitle: "22 days will be added to your existing membership"
            //     }
            // }
        }

        private checkoutPayload(productId: string, programId: string): string {
            const orderProduct: OrderProduct = {
                productId: productId,
                quantity: 1,
                option: {
                    programId: programId
                }
            }
            const createOrderPayload: {
                orderProduct: OrderProduct
            } = {
                orderProduct: orderProduct
            }
            return JSON.stringify(createOrderPayload)
        }

        @httpGet("/options/:packId")
        async getMindProgramsByCenterId(req: express.Request): Promise<{ programs: ProgramDetail[] }> {
            const session: Session = req.session
            const packId: string = req.params.packId
            const centerId: string = req.query.centerId
            const userId: string = session.userId
            const userContext: UserContext = req.userContext as UserContext
            const userAgent: UserAgent = session.userAgent
            const productId = CatalogueServiceV2Utilities.getProgramPackProductId(packId)
            const packProductPromise = this.catalogueService.getProduct(productId)
            const programsPromise = this.programBusiness.getProgramsForCenter(packId, centerId, userId)
            const appVersion: string = req.headers["appversion"] as string
            const codePushVersion: number = req.headers["codepushversion"] && req.headers["codepushversion"] !== "undefined" ? Number(req.headers["codepushversion"]) : 0
            const osName: string = req.headers["osname"] as string
            const deviceId = session.deviceId
            const offersPromise = this.offerService.getProgramPackOffers({
                userId: userId,
                cityId: userContext.userProfile.cityId,
                deviceId: deviceId,
                source: AppUtil.callSourceFromContext(userContext)
            })
            const programs = await programsPromise
            const offers = await offersPromise
            const packProduct = await packProductPromise
            const offerDetails = OfferUtil.getPackOfferAndPrice(packProduct, offers)
            const offerIds = !_.isEmpty(offerDetails.offers) ? _.map(offerDetails.offers, offer => { return offer.offerId }) : undefined
            const programDetails = await this.getProgramDetails(programs, productId, packId, offerIds, userContext)
            const sortedPrograms = programDetails.sort((a, b) => {
                if (a.date < b.date)
                    return -1
                else if (a.date > b.date) {
                    return 1
                } else {
                    return 0
                }
            })
            return { programs: sortedPrograms }
        }

        private getProgramDetails(programs: Program[], productId: string, packId: string, offerIds: string[], userContext: UserContext): ProgramDetail[] {
            const programDetails: ProgramDetail[] = _.map(programs, program => {
                const mandatoryClassInfos: MandatoryClassInfo[] = []
                const tz = userContext.userProfile.timezone
                if (program.mandatoryActivityDates) {
                    const mandatoryClasses = Object.keys(program.mandatoryActivityDates)
                    mandatoryClasses.forEach(mandatoryClass => {
                        const classDates = program.mandatoryActivityDates[mandatoryClass]
                        const dateStrings = _.map(classDates, classDate => {
                            const date = TimeUtil.parseDateFromEpoch(classDate)
                            return TimeUtil.formatDateInTimeZone(tz, date)
                        })

                        const startDate = TimeUtil.parseDateFromEpoch(program.startAt)
                        const endDate = TimeUtil.parseDateFromEpoch(program.endAt)
                        const groupSessionTime = program.meta[mandatoryClass + "_time"]
                        // TODO: Remove this once Nitin fixes end time issue
                        const programFirstClassDate = dateStrings[0]
                        const programDateTime = TimeUtil.parseDateTime(programFirstClassDate + " " + groupSessionTime, tz)
                        mandatoryClassInfos.push({
                            dates: dateStrings,
                            title: `${mandatoryClass}`,
                            subTitle: `${mandatoryClass} will be held every ${TimeUtil.formatDateStringInTimeZone(programFirstClassDate, tz, "dddd")}
                                 at ${TimeUtil.formatDateInTimeZone(tz, programDateTime, "h A")}, starting ${TimeUtil.formatDateInTimeZone(tz, startDate, "D MMM")}
                                     till ${TimeUtil.formatDateInTimeZone(tz, endDate, "D MMM")}`,
                            metaText: `${mandatoryClass} cannot be recheduled or cancelled.`
                        })
                    })
                }
                const programDetail: ProgramDetail = {
                    date: TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(program.startAt)),
                    programId: program.id,
                    packId: packId,
                    productId: productId,
                    offerId: !_.isEmpty(offerIds) ? offerIds[0] : undefined,
                    offerIds: offerIds,
                    mandatoryClassInfos: mandatoryClassInfos
                }
                return programDetail
            })
            return programDetails
        }
    }

    return ProgramController
}

export default controllerFactory
