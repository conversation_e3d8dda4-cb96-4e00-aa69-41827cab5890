import { Cult<PERSON><PERSON> } from "@curefit/cult-common"
import { Program, ProgramPack } from "@curefit/program-client"
import { ProgramPackProduct } from "@curefit/product-common"

export interface CenterProgramDetail {
    center: CultCenter
    programs: Program[]
}

export interface UserGoalMetric {
    name: string,
    currentValue: number,
    targetValue: number,
    initialValue: number,
    unit: string
}

export interface IProgramBusiness {
    getCentersForProgramPack: (userId: string, programPack: ProgramPackProduct) => Promise<CultCenter[]>
    getProgramPack: (userId: string, packId: string) => Promise<ProgramPack>
    getCenterProgramDetails: (userId: string, packId: string) => Promise<{ [centerId: string]: CenterProgramDetail }>
    getProgramsForCenter: (packId: string, centerId: string, userId: string) => Promise<Program[]>
    getBestPackForProgram: (userId: string, programId: string) => Promise<ProgramPack>
}
