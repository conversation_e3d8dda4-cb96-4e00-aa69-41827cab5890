import { <PERSON>ult<PERSON><PERSON> } from "@curefit/cult-common"
import { IProgramService, Program, ProgramPack, PROGRAM_CLIENT_TYPES } from "@curefit/program-client"
import { inject, injectable } from "inversify"
import { ICatalogueService, CATALOG_CLIENT_TYPES, CatalogueServiceV2Utilities } from "@curefit/catalog-client"
import { ProgramPackProduct } from "@curefit/product-common"
import * as _ from "lodash"
import { CenterProgramDetail, IProgramBusiness } from "./IProgramBusiness"

@injectable()
export class ProgramBusiness implements IProgramBusiness {

    constructor(
        @inject(PROGRAM_CLIENT_TYPES.ProgramService) private programService: IProgramService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
    ) {
    }

    async getCentersForProgramPack(userId: string, programPack: ProgramPackProduct): Promise<CultCenter[]> {
        const programs: Program[] = await this.programService.browseProgramsByPackId(programPack.packId, userId)
        const groupedProgram = _.groupBy(programs, (program: Program) => { return program.centerId })
        const centerIds = Object.keys(groupedProgram)
        const centerPromises = _.map(centerIds, centerId => {
            return programPack.tenantId === "cult.fit" ? this.catalogueService.getCultCenter(centerId) : this.catalogueService.getCultMindCenter(centerId)
        })
        const centers = await Promise.all(centerPromises)
        return centers
    }

    async getCenterProgramDetails(userId: string, packId: string): Promise<{ [centerId: string]: CenterProgramDetail }> {
        const productId = CatalogueServiceV2Utilities.getProgramPackProductId(packId)
        const productPromise = this.catalogueService.getProduct(productId)
        const programsPromise = this.programService.browseProgramsByPackId(packId, userId)
        const product = <ProgramPackProduct>await productPromise
        const programs: Program[] = await programsPromise
        const programsByCenterId = _.groupBy(programs, (program: Program) => { return program.centerId })
        const centerIds = Object.keys(programsByCenterId)
        const centerPromises = _.map(centerIds, centerId => {
            return product.tenantId === "cult.fit" ? this.catalogueService.getCultCenter(centerId) : this.catalogueService.getCultMindCenter(centerId)
        })
        const centers = await Promise.all(centerPromises)
        const centerMap = _.keyBy(centers, center => { center.id })
        const result: { [centerId: string]: CenterProgramDetail } = {}
        centers.forEach(center => {
            result[center.id] = {
                center: center,
                programs: programsByCenterId[center.id]
            }
        })
        return result
    }

    async getProgramPack(userId: string, packId: string): Promise<ProgramPack> {
        return this.programService.packById(userId, packId)
    }

    async getProgramsForCenter(packId: string, centerId: string, userId: string): Promise<Program[]> {
        const productId = CatalogueServiceV2Utilities.getProgramPackProductId(packId)
        const programPackProduct = <ProgramPackProduct>await this.catalogueService.getProduct(productId)
        const programs: Program[] = programPackProduct.tenantId === "cult.fit" ? await this.programService.browseProgramsByPackId(packId, userId) : await this.programService.browseProgramsByPackId(packId, userId)
        const centerPrograms = _.filter(programs, program => {
            return program.centerId.toString() === centerId
        })
        return centerPrograms
    }

    async getBestPackForProgram(userId: string, programId: string): Promise<ProgramPack> {
        const programPack: ProgramPack = await this.programService.bestProgramPack(userId, programId)
        return programPack
    }

}

export default ProgramBusiness
