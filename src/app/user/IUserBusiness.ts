import { PreferredLocation, SessionInfo } from "@curefit/userinfo-common"
import {
    CityResponse, LatLong, VerticalType
} from "@curefit/location-common"
import {
    DeliverySubArea, ListingBrandIdType,
} from "@curefit/eat-common"
import {
    Session,
} from "@curefit/userinfo-common"
import {
    SessionData,
} from "@curefit/userinfo-common"
import {
    UserDeliveryAddress,
} from "@curefit/eat-common"
import {
    UserStructuredAddress,
    UserPreference
} from "@curefit/user-common"
import { PlaceData } from "@curefit/location-common"
import { ActivePackViewV1 } from "./ActivePackViewBuilderV1"
import { UserContext } from "@curefit/userinfo-common"
import { SignedUrlResponse } from "@curefit/user-client"
import { City } from "@curefit/location-common"
import { AppTenant, OrderSource, UserAgentType as UserAgent } from "@curefit/base-common"
import AppLayoutBuilder from "./AppLayoutBuilder"
import { Tenant } from "@curefit/user-common"
import { AttributeAction } from "@curefit/rashi-client"
import { MembershipItem } from "../common/views/ProfileWidgetView"
import { LocationPreferenceRequestEntity, LocationPreferenceResponseEntity, UpdateStatusResponseEntity } from "../util/UserUtil"
import { MembershipDetails } from "@curefit/cult-common"
import { Membership } from "@curefit/membership-commons"
import { PromUtil } from "../../util/PromUtil"

export interface UpdateBrowseLocationPayload {
    areaId?: string
    subArea?: string
    addressId?: string
    lat?: number
    lon?: number
    shouldUpdateSession?: boolean
    placeName?: string
    listingBrand?: string
}

export interface UpdateBrowseLocationResponse {
    isWithinServicableCity?: boolean
    isInServicableArea: boolean
    placeData?: PlaceData,
    city?: City
    addressId?: string
}

export interface FindPreferredCityResponse {
    isCityChangeToBeNotified: boolean
    showCitySelection: boolean
    city: City
    detectedCityName?: string
    detectedCountryCode?: string
}

export interface UserCity {
    city: City
    isCityManuallySelected: boolean
    reason: string
}

export interface LocationInfoBody {
    addressId?: string
    pincode?: string
    latLong?: LatLong
}

interface IUserBusiness {
    addAddress(userId: string, userAddress: UserDeliveryAddress, pincode?: string, appTenant?: AppTenant): Promise<UserDeliveryAddress>
    addStructuredAddress(userId: string, address: UserStructuredAddress): Promise<UserStructuredAddress>
    addKiosk(userId: string, kisokScanCode: string): Promise<UserDeliveryAddress>
    addKioskById(userId: string, kioskId: string): Promise<UserDeliveryAddress>
    deleteAddress(session: Session, addressId: string, tenant: Tenant): Promise<UserDeliveryAddress[]>
    deleteStructuredAddress(userId: string, addressId: string): Promise<boolean>
    getAddress(userId: string, addressId: string): Promise<UserDeliveryAddress>
    getStructuredAddress(userId: string, addressId: string): Promise<UserStructuredAddress>
    updateAddress(userId: string, userAddress: UserDeliveryAddress): Promise<UserDeliveryAddress[]>
    updateStructuredAddress(userId: string, addressId: string, newAddress: UserStructuredAddress): Promise<UserStructuredAddress>
    getAddresses(userId: string, verticalQuery: string, listingBrand?: string): Promise<UserDeliveryAddress[]>
    getActiveAddressCount(userId: string): Promise<number>
    getStructuredAddresses(userId: string): Promise<UserStructuredAddress[]>
    getDIYActivePacks(userContext: UserContext): Promise<ActivePackViewV1[]>
    getActivePacksV1(userContext: UserContext, limit?: number): Promise<ActivePackViewV1[]>
    getCultAndLiveAssociatedActivePacks(userContext: UserContext): Promise<ActivePackViewV1[]>
    getCultSummaryActivePacks(userContext: UserContext): Promise<ActivePackViewV1[]>
    getLiveFitMembershipActivePacks(userContext: UserContext): Promise<ActivePackViewV1[]>
    getGymFitMembershipActivePacks(userContext: UserContext): Promise<ActivePackViewV1[]>
    getCultPTActivePacks(userContext: UserContext): Promise<ActivePackViewV1[]>
    getLiveSGTActivePacks(userContext: UserContext): Promise<ActivePackViewV1[]>
    getCultSummaryAndLivePack(userContext: UserContext): Promise<MembershipItem[]>
    getOnePassMembershipItem(userContext: UserContext): Promise<MembershipItem[]>
    updateBrowseLocation(session: Session, updateBrowseLocationPayload: UpdateBrowseLocationPayload, tenant: Tenant): Promise<UpdateBrowseLocationResponse>
    getPreferredLocation(userContext: UserContext, userId: string, sessionData: SessionData, long?: number, lat?: number, mealSlot?: string, ignoreServiceableTimings?: boolean, listingBrand?: ListingBrandIdType, userAgent?: UserAgent, ignoreKiosk?: boolean): Promise<PreferredLocation>
    getAreaAndAddressByAddressId(addressId: string, userId: string, mealSlot: string, isPack: boolean, listingBrand?: ListingBrandIdType): Promise<{ area: DeliverySubArea, defaultArea: DeliverySubArea, address: UserDeliveryAddress }>
    findPreferredCity(tenenat: Tenant, latitude: number, longitude: number, ip: string, session: Session, passedCityId?: string): Promise<FindPreferredCityResponse>
    updateCity(userContext: UserContext, session: Session, selectedCityId: string): Promise<CityResponse>
    publishUserEventToRashi(userId: string | Number, body: any, action: AttributeAction, appTenant: AppTenant): Promise<boolean>
    publishUserActivityEventToRashi(userId: string | Number, eventName: string, body: any, appTenant: AppTenant): Promise<boolean>
    augmentStructuredAddress(userId: string, addressId: string, address?: UserDeliveryAddress, pincode?: string, verticalType?: VerticalType): Promise<UserDeliveryAddress>
    getImageSignedUrl(fileName: string, s3BucketName?: string): Promise<SignedUrlResponse>
    updatePincodeLocation(session: Session, locationInfo: LocationInfoBody, verticalType: VerticalType): Promise<any>
    getYearEndReport(userContext: UserContext): Promise<any>
    setLocationPreference(userContext: UserContext, userId: string, cityId: string, preference: LocationPreferenceRequestEntity, pageFrom?: string): Promise<UpdateStatusResponseEntity>
    getLocationPreference(userContext: UserContext, userId: string, cityId: string): Promise<LocationPreferenceResponseEntity>
    isEligibleForJuspayRP(userId: string, deviceId: string, orderSource: OrderSource): Promise<boolean>
}

export default IUserBusiness
