import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { IPageService } from "@curefit/vm-models"
import { TabTypes } from "./UserController"
import { City, VerticalType } from "@curefit/location-common"
import { Page as VMPage } from "@curefit/vm-common"
import VMPageBuilder, { Section } from "../page/vm/VMPageBuilder"
import { UserContext, UserProfile, SessionInfo } from "@curefit/userinfo-common"
import IUserBusiness from "./IUserBusiness"
import INavBarBusiness from "./INavBarBusiness"
import EatUtil from "../util/EatUtil"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import * as _ from "lodash"
import {
    AppLayout,
    PageFrame,
    AreaPageSelector,
    CityPageSelector,
    PageSelector,
    getTabToPageIdMap
} from "@curefit/apps-common"
import AppUtil from "../util/AppUtil"
import { Tenant } from "@curefit/user-common"
import { PromiseCache } from "../util/VMUtil"

@injectable()
class AppLayoutBuilder {
    constructor(
        @inject(CUREFIT_API_TYPES.PageService) protected pageService: IPageService,
        @inject(CUREFIT_API_TYPES.UserBusiness) private userBusiness: IUserBusiness,
        @inject(CUREFIT_API_TYPES.NavBarBusiness) private navBarBusiness: INavBarBusiness,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(CUREFIT_API_TYPES.VMPageBuilder) private pageBuilder: VMPageBuilder
    ) { }

    async fetchPageLayout(userContext: UserContext, supportedVerticals: VerticalType[]): Promise<AppLayout> {
        const isWeb = AppUtil.isWeb(userContext)

        if (!userContext.userProfile.promiseMapCache) {
            userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
        }

        const tenant = AppUtil.getTenantFromUserContext(userContext)
        const TAB_TO_PAGE_ID_MAP: any = getTabToPageIdMap(tenant)
        /* filtering page id which are not supported for the vertical */
        /* removing the eatclp from layout */
        const pageIds = _.pull(_.values(_.pick(TAB_TO_PAGE_ID_MAP, supportedVerticals)), isWeb ? undefined : "eatclp")
        const pages = await Promise.all(_.map(pageIds, async pageId => await this.pageService.getPage(pageId, AppUtil.getTenantFromUserContext(userContext))))
        const pageFramesArray: PageFrame[] = await Promise.all(_.map(pages, async page => {
            const [selector, sections, widgets] = await Promise.all([
                this.buildPageSelector(userContext, page, userContext.userProfile, userContext.sessionInfo, {}),
                this.pageBuilder.getTabPageSection(userContext, page, {}),
                this.pageBuilder.getWidgetLayout(userContext, page, {})
            ])

            let selectedTabIndex
            if (page.pageType === "TAB_PAGE") {
                const selectedTabId = _.get(_.minBy(sections, "priority"), "id")
                selectedTabIndex = sections.findIndex(a => { return a.id === selectedTabId })
                selectedTabIndex = selectedTabIndex === -1 ? 0 : selectedTabIndex
            }

            return {
                pageType: page.pageType,
                pageName: page.pageId,
                icon: page.icon,
                selectedTabIndex,
                pulsatingDotIndex: sections.findIndex(a => { return a.id === page.pulsatingDotTab }),
                pulsatingDotVersion: sections.findIndex(a => { return a.id === page.pulsatingDotTab }),
                selector,
                sections,
                widgets
            }
        }))

        const pageFrames: { [key in TabTypes]?: PageFrame } = _.keyBy(pageFramesArray, p => {
            return p.pageName
        })

        const appSupportedVerticals = ["PLAN", ...supportedVerticals]
        const tabs = _.map(appSupportedVerticals, (tab: TabTypes) => ({
            tabId: tab,
            pageId: TAB_TO_PAGE_ID_MAP[tab]
        }))

        return {
            appTabs: tabs,
            pageFrames
        }
    }

    private async buildPageSelector(userContext: UserContext, page: VMPage, userProfile: UserProfile, sessionInfo: SessionInfo, queryParams: { [filterName: string]: string }): Promise<PageSelector> {
        if (AppUtil.isInternationalApp(userContext)) {
            return undefined
        }
        if (page.selector === "AREA_SELECTOR") {
            if (userProfile.preferredLocationPromise === undefined)
                userProfile.preferredLocationPromise = this.userBusiness.getPreferredLocation(userContext, userProfile.userId, sessionInfo.sessionData, sessionInfo.lon, sessionInfo.lat, undefined, undefined, EatUtil.getListingBrandForPageId(page.pageId, { query: queryParams }))
            const preferredLocation = await userProfile.preferredLocationPromise
            const pageSelector: AreaPageSelector = {
                selector: "AREA_SELECTOR",
                areaName: EatUtil.formatPreferredLocation(preferredLocation)
            }
            return pageSelector
        } else if (page.selector === "CITY_SELECTOR") {
            const city = await this.serviceInterfaces.cityService.getCityById(userProfile.cityId)
            const pageSelector: CityPageSelector = {
                selector: "CITY_SELECTOR",
                cityName: city.name
            }
            return pageSelector
        }
        return undefined
    }
}

export default AppLayoutBuilder
