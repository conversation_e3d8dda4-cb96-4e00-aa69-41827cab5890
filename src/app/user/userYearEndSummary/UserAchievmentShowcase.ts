export interface UserAchievmentShowcase {
    user_id: number
    classes_attended: number
    calories: number
    weekend_at_cult?: number
    streak?: number
    first_class?: string
    latest_class?: string
    total_minutes_worked?: number
    days_worked_out_best_month?: number
    weeks_active?: number
    months_active?: number
    country_percentile?: number
    favorate_format_1?: string
    favorate_format_1_classes?: number
    favorate_format_2?: string
    favorate_format_2_classes?: number
    favorate_format_3?: string
    favorate_format_3_classes?: number
    Better_than_users?: string
    best_month?: string
    wod_song?: string
    wod_class_song_time?: number
    Ranking_type?: string
    start_off_year?: string
    days_streak?: number
    total_pack_length?: number
    total_memberships?: number
    total_workout_days?: number
    classes_best_month?: number
    song_classes?: number
    wod?: string
    wod_classes?: number
    best_month_date?: string
    comebacks?: number
    trainer_name?: string
    trainer_classes?: number
    formats?: number
    GX_Classes?: number
    GYM_Classes?: number
    Live_Classes?: number
    PLAY_Classes?: number
    morning_classes?: number
    evening_classes?: number
    karma_points?: number
    squadclasses?: number
    squaduser?: string
    notinsquadclasses?: number
    notinsquaduser?: string
    fav_format_attribute?: string
    fav_format_attribute_value?: number
    final_weight?: number
    start_weight?: number
    pr_date?: string
    squad_friends_count?: number
    pack_utilisation_count?: number
    cities_count?: number
    centers_visited?: number
    favourite_city_1?: string
    favourite_city_2?: string
    favourite_city_3?: string
}