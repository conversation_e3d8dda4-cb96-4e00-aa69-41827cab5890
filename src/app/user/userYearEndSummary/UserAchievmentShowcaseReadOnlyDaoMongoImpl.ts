import { inject, injectable } from "inversify"
import { MongoReadonlyDao } from "@curefit/mongo-utils"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { UserAchievmentShowcaseModel } from "./UserAchievmentShowcaseModel"
import { IUserAchievmentShowcaseReadOnlyDao } from "./UserAchievmentShowcaseDao"
import { UserAchievmentShowcase } from "./UserAchievmentShowcase"
import { UserAchievmentShowcaseSchema } from "./UserAchievmentShowcaseSchema"

@injectable()
export class UserAchievmentShowcaseReadOnlyDaoMongoImpl extends MongoReadonlyDao<UserAchievmentShowcaseModel, UserAchievmentShowcase> implements IUserAchievmentShowcaseReadOnlyDao {
    constructor(
        @inject(CUREFIT_API_TYPES.UserAchievmentShowcaseSchema) userYearEndSummarySchema: UserAchievmentShowcaseSchema,
        @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
        super(userYearEndSummarySchema.mongooseModel, logger, userYearEndSummarySchema.isLeanQueryEnabled)
    }
}