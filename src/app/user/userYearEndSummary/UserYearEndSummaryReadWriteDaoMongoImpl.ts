import { inject, injectable } from "inversify"
import { MongoReadWriteDao } from "@curefit/mongo-utils"
import { BASE_TYPES, ILogger } from "@curefit/base"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { UserYearEndSummaryReadOnlyDaoMongoImpl } from "./UserYearEndSummaryReadOnlyDaoMongoImpl"
import { UserYearEndSummaryModel } from "./UserYearEndSummaryModel"
import { UserYearEndSummary } from "./UserYearEndSummary"
import { IUserYearEndSummaryReadWriteDao } from "./UserYearEndSummaryDao"
import { UserYearEndSummarySchema } from "./UserYearEndSummarySchema"

@injectable()
export class UserYearEndSummaryReadWriteDaoMongoImpl extends MongoReadWriteDao<UserYearEndSummaryModel, UserYearEndSummary> implements IUserYearEndSummaryReadWriteDao {
    constructor(
        @inject(CUREFIT_API_TYPES.UserYearEndSummarySchema) userYearEndSummarySchema: UserYearEndSummarySchema,
        @inject(CUREFIT_API_TYPES.UserYearEndSummaryReadOnlyDaoMongoImpl) readonlyDao: UserYearEndSummaryReadOnlyDaoMongoImpl,
        @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
        super(userYearEndSummarySchema.mongooseModel, readonlyDao, logger)
    }
}
