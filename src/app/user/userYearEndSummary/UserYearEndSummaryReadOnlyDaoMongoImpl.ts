import { inject, injectable } from "inversify"
import { MongoReadonlyDao } from "@curefit/mongo-utils"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { UserYearEndSummaryModel } from "./UserYearEndSummaryModel"
import { UserYearEndSummary } from "./UserYearEndSummary"
import { IUserYearEndSummaryReadOnlyDao } from "./UserYearEndSummaryDao"
import { UserYearEndSummarySchema } from "./UserYearEndSummarySchema"

@injectable()
export class UserYearEndSummaryReadOnlyDaoMongoImpl extends MongoReadonlyDao<UserYearEndSummaryModel, UserYearEndSummary> implements IUserYearEndSummaryReadOnlyDao {
    constructor(
        @inject(CUREFIT_API_TYPES.UserYearEndSummarySchema) userYearEndSummarySchema: UserYearEndSummarySchema,
        @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
        super(userYearEndSummarySchema.mongooseModel, logger, userYearEndSummarySchema.isLeanQueryEnabled)
    }
}
