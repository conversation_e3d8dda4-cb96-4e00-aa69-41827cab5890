import { inject, injectable } from "inversify"
import { MongoReadWriteDao } from "@curefit/mongo-utils"
import { BASE_TYPES, ILogger } from "@curefit/base"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { UserAchievmentShowcaseModel } from "./UserAchievmentShowcaseModel"
import { UserAchievmentShowcase } from "./UserAchievmentShowcase"
import { UserAchievmentShowcaseSchema } from "./UserAchievmentShowcaseSchema"
import { UserAchievmentShowcaseReadOnlyDaoMongoImpl } from "./UserAchievmentShowcaseReadOnlyDaoMongoImpl"
import { IAchievmentShowcaseReadWriteDao } from "./UserAchievmentShowcaseDao"

@injectable()
export class UserAchievmentShowcaseReadWriteDaoMongoImpl extends MongoReadWriteDao<UserAchievmentShowcaseModel, UserAchievmentShowcase> implements IAchievmentShowcaseReadWriteDao {
    constructor(
        @inject(CUREFIT_API_TYPES.UserAchievmentShowcaseSchema) userYearEndSummarySchema: UserAchievmentShowcaseSchema,
        @inject(CUREFIT_API_TYPES.UserAchievmentShowcaseReadOnlyDaoMongoImpl) readonlyDao: UserAchievmentShowcaseReadOnlyDaoMongoImpl,
        @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
        super(userYearEndSummarySchema.mongooseModel, readonlyDao, logger)
    }
}