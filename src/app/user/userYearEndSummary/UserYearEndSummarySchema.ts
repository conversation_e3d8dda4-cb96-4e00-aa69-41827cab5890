import { MONGO_TYPES, MultiMongooseAccess, MultiMongooseSchema } from "@curefit/mongo-utils"
import { inject } from "inversify"
import { UserYearEndSummaryModel } from "./UserYearEndSummaryModel"
import { ReadPreference } from "mongodb"

export class UserYearEndSummarySchema extends MultiMongooseSchema<UserYearEndSummaryModel> {

    constructor(
        @inject(MONGO_TYPES.MultiMongooseAccess) mongooseAccess: MultiMongooseAccess
    ) {
        super(mongooseAccess, "useryearendstats", "DEFAULT", ReadPreference.SECONDARY_PREFERRED)
    }

    protected schema() {
        return {
            user_id: {type: Number, unique: true},
            class_attend: {type: Number, required: true},
            total_minutes_worked: {type: Number, required: false},
            calories: {type: Number, required: true},
            weekend_at_cult: {type: Number, required: false},
            streak: {type: Number, required: true},
            favorate_format_1: {type: String, required: false},
            favorate_format_1_classes: {type: Number, required: false},
            favorate_format_2: {type: String, required: false},
            favorate_format_2_classes: {type: Number, required: false},
            country_percentile: {type: Number, required: false},
            best_month: {type: String, required: false},
            pr_date: {type: String, required: false},
            days_worked_out_best_month: {type: Number, required: false},
            comebacks: {type: Number, required: false},
            trainer_classes: {type: Number, required: false},
            formats: {type: Number, required: false},
            trainername: {type: String, required: false},
            squaduser: {type: String, required: false},
            fav_format_attribute: {type: String, required: false},
            karma_points: {type: Number, required: false},
            squadclasses: {type: Number, required: false},
            fav_format_attribute_value: {type: Number, required: false},
            final_weight: {type: Number, required: false},
            start_weight: {type: Number, required: false}
        }
    }

}
