import { inject, injectable } from "inversify"
import { UserContext } from "@curefit/userinfo-common"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { IUserYearEndSummaryReadOnlyDao } from "./userYearEndSummary/UserYearEndSummaryDao"
import { UserYearEndSummary } from "./userYearEndSummary/UserYearEndSummary"
import { CULT_CLIENT_TYPES, ICultServiceOld as ICultService } from "@curefit/cult-client"
import { ISocialService, SOCIAL_CLIENT_TYPES } from "@curefit/social-client"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { IPageConfigReadOnlyDao, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import CultUtil from "../util/CultUtil"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { ISegmentService } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import * as _ from "lodash"
import { IUserAchievmentShowcaseReadOnlyDao } from "./userYearEndSummary/UserAchievmentShowcaseDao"
import { UserAchievmentShowcase } from "./userYearEndSummary/UserAchievmentShowcase"

@injectable()
export class UserYearReport2022Business {

    constructor(
        @inject(PAGE_CONFIG_TYPES.PageConfigReadOnlySecondaryDao) protected pageConfigDao: IPageConfigReadOnlyDao,
        @inject(CUREFIT_API_TYPES.UserYearEndSummaryReadOnlyDaoMongoImpl) private userYearEndSummaryReadOnlyDao: IUserYearEndSummaryReadOnlyDao,
        @inject(CUREFIT_API_TYPES.UserAchievmentShowcaseReadOnlyDaoMongoImpl) private userYearEndSummaryReadOnlyDaoV2: IUserAchievmentShowcaseReadOnlyDao,
        @inject(CULT_CLIENT_TYPES.CultFitService) public cultFitService: ICultService,
        @inject(SOCIAL_CLIENT_TYPES.SocialService) public socialService: ISocialService,
        @inject(BASE_TYPES.ILogger) public logger: ILogger,
        @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness,
        @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
    ) {
    }

    public async  getMotivatorReport(userContext: UserContext): Promise<any>  {
        const properCase = (text: string) => {
            return text.replace("_", " ").replace(/\w\S*/g, function (txt) {
                return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
            })
        }
        const pageData: any = await this.pageConfigDao.findOne({ pageId: { $eq: "UserReportPageConfig" } }).then((pageConfig: any) => {
            return pageConfig.data
        })
        const user = await userContext.userPromise
        const contentList = pageData?.mainScreen?.contentList ?? []
        if (!pageData && contentList?.length > 0) {
            this.logger.error("Error in Mothers Day report, userId: " + user.id)
            return null
        }
        const pages: any = []
        for (let i = 0; i < contentList.length; i++) {
            pages.push({
                "id": `motivator_in_chief_${i}`,
                "backgroundColorStart": "#3D3895",
                "backgroundColorEnd": "#5E7F9C",
                "backgroundImageURL": pageData?.mainScreen?.backgroundImageURL ? `${pageData?.mainScreen?.backgroundImageURL}` : null,
                "backgroundLottieURL": pageData?.mainScreen?.backgroundLottieURL ? `${pageData?.mainScreen?.backgroundLottieURL}` : null,
                "swipeLottieURL": i === 0 ? "image/year-end-report/final/swipe.json" : null,
                "shareButtonIcon": i == contentList.length - 1 ? null : "share-2",
                "shareButtonTitle": i == contentList.length - 1 ? "BOOK A CLASS NOW" : "SHARE",
                "shareButtonAction": i == contentList.length - 1 ? {
                    actionType: "NAVIGATION",
                    url: "curefit://classbookingv2"
                } : null,
                "widgets":
                    [
                        contentList[i].image ? {
                            "type": "MEDIA",
                            "data": {
                                "type": "image",
                                "url": `${contentList[i].image?.url}`,
                                "height": contentList[i].image?.height,
                                "width": contentList[i].image?.width,
                                "topPadding": 20,
                                "bottomPadding": 0
                            }
                        } : {},
                        contentList[i].lottie ? {
                            "type": "MEDIA",
                            "data": {
                                "type": "lottie",
                                "url": `${contentList[i].lottie?.url}`,
                                "height": contentList[i].lottie?.height,
                                "width": contentList[i].lottie?.width,
                                "topPadding": 20,
                                "bottomPadding": 0
                            }
                        } : {},
                        contentList[i].text ? {
                            "type": "HTML",
                            "data":
                                {
                                    "topPadding": 20,
                                    "bottomPadding": 0,
                                    "horizontalPadding": 80,
                                    "value": `<div style="font-size:22px; color:#FFFFFF; font-weight: 500; text-align: center; font-family: Inter;">${contentList[i].text}</div>`
                                }
                        } : {},
                    ],
            })
        }
        return {
            "welcomeScreen": {
                "id": "motivator_in_chief_intro",
                "backgroundLottieURL": `${pageData?.introScreen?.backgroundLottieURL}`,
                "backgroundColorStart": "#3D3895",
                "backgroundColorEnd": "#5E7F9C",
                "animationDuration": pageData?.introScreen?.animationDuration !== undefined ? pageData?.introScreen?.animationDuration : 1,
                "widgets":
                    [
                        {
                            "type": "MEDIA",
                            "data": {
                                "type": "image",
                                "url": "image/year-end-report/v_man_color.png",
                                "height": 45,
                                "width": 40,
                                "topPadding": 100,
                                "bottomPadding": 4
                            }
                        },
                        {
                            "type": "TEXT",
                            "data": {
                                "topPadding": 70,
                                "bottomPadding": 2,
                                "horizontalPadding": 40,
                                "textStyle": "P2",
                                "value": "Get motivated by our",
                                "color": "#FFFFFF",
                                "centerAlign": true
                            }
                        },
                        pageData?.introScreen?.image ? {
                            "type": "MEDIA",
                            "data": {
                                "type": "image",
                                "url": `${pageData?.introScreen?.image?.url}`,
                                "height": pageData?.introScreen?.image?.height,
                                "width": pageData?.introScreen?.image?.width,
                                "topPadding": 0,
                                "bottomPadding": 0
                            }
                        } : {},
                        pageData?.introScreen?.lottie ? {
                            "type": "MEDIA",
                            "data": {
                                "type": "lottie",
                                "url": `${pageData?.introScreen?.lottie?.url}`,
                                "height": pageData?.introScreen?.lottie?.height,
                                "width": pageData?.introScreen?.lottie?.width,
                                "topPadding": 0,
                                "bottomPadding": 0
                            }
                        } : {},
                        {
                            "type": "TEXT",
                            "data": {
                                "topPadding": 80,
                                "bottomPadding": 0,
                                "horizontalPadding": 100,
                                "textStyle": "P2",
                                "value": "You’ll never want to miss another workout!",
                                "color": "#FFFFFF",
                                "centerAlign": true
                            }
                        },
                        {
                            "type": "TEXT",
                            "data": {
                                "topPadding": 30,
                                "bottomPadding": 0,
                                "textStyle": "H2",
                                "value": "TAP FOR MOTIVATION",
                                "color": "#FFFFFF",
                                "centerAlign": true
                            }
                        },
                    ],
            },
            "pages": pages,
        }
    }

    public async getUserYearReport2024(userContext: UserContext, userYearlyFitnessAttributeMap?: any): Promise<any> {
        const properCase = (text: string) => {
            return text.replace("_", " ").replace(/\w\S*/g, function (txt) {
                return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
            })
        }
        const user = await userContext.userPromise
        const firstName = properCase(user.firstName ?? user.lastName)
        const userYearEndData: UserYearEndSummary = await this.userYearEndSummaryReadOnlyDao.findOne({user_id: user.id})
        if (!userYearEndData) {
            this.logger.error("Error in year end report 2024, user data dont exist, userId: " + user.id)
            return null
        }

        const isDeadBaseAlumniPassPlayUser = await CultUtil.doesUserBelongToDeadBaseAlumniPassElitePlaySegment(userContext, this.serviceInterfaces)
        const isDeadBaseAlumniPassNonPlayUser = await CultUtil.doesUserBelongToDeadBaseAlumniPassEliteNonPlaySegment(userContext, this.serviceInterfaces)

        const start_year = userYearEndData.first_class != null && userYearEndData.first_class.trim().split("/").length >= 3 ?  userYearEndData.first_class.trim().split("/")[2] : "2023"

        const pages: any = []

        const items: any = []

        const getFormatName = (formatName: string) => {
            if (formatName === "DANCE") {
                return "DANCE\nFITNESS"
            } else if (formatName === "SNC" || formatName === "STRENGTH" || formatName === "EQUIPMENT") {
                return "\nSTRENGTH+"
            } else if (formatName === "BURN") {
                return "\nBURN"
            } else if (formatName === "HRX") {
                return "\nHRX"
            } else if (formatName === "YOGA") {
                return "\nYOGA"
            } else if (formatName === "BOXING") {
                return "\nBOXING"
            } else if (formatName === "GYM") {
                return "\nGYM"
            } else if (formatName === "Swimming") {
                return "\nSWIMMING"
            } else if (formatName === "Badminton") {
                return "\nBADMINTON"
            } else if (formatName === "CARDIO") {
                return "\nCARDIO"
            } else {
                return 0
            }
        }

        items.push({
            "title": `${userYearEndData.class_attend.toLocaleString("en-IN")}`,
            "subtitle": "Classes done"
        })
        items.push({
            "title": `${userYearEndData.calories.toLocaleString("en-IN")}`,
            "subtitle": "Calories burnt"
        })
        items.push({
            "title": `${userYearEndData.streak.toLocaleString("en-IN")}\nWeek${userYearEndData.streak === 1 ? "" : "s"}`,
            "subtitle": "Longest streak"
        })

        if (userYearEndData.favorate_format_1 != null) {
            const finalFormatName = getFormatName(userYearEndData.favorate_format_1)
            items.push( {
                "title": `${finalFormatName}`,
                "subtitle": "Most done format"
            })
        }

        pages.push({
            "id": "summary",
            "backgroundColorStart": "#2E80C5",
            "backgroundColorEnd": "#712C72",
            "backgroundImageURL": "image/year-end-report/2024/medal.jpg",
            "swipeLottieURL": "image/year-end-report/final/swipe.json",
            "shareButtonIcon": "share-2",
            "shareButtonTitle": "SHARE",
            "nextButtonTitle": "NEXT",
            "disableBackgroundAnimation": true,
            "widgets":
                [
                    {
                        "type": "TEXT",
                        "data":
                            {
                                "topPadding": 30,
                                "bottomPadding": 0,
                                "textStyle": "H6",
                                "value": `${user.firstName}'s\n 2024 Journey`,
                                "color": "#FFFFFF",
                                "centerAlign": true
                            }
                    },
                    {
                        "type": "GRID_TEXT",
                        "data":
                            {
                                "items": items,
                                "topPadding": 0.40,
                                "bottomPadding": 30,
                                "horizontalPadding": 10,
                                "titleColor": "#FFFFFF",
                                "titleStyle": "KeplerStd30",
                                "subtitleColor": "#FFFFFF",
                                "subtitleStyle": "P5",
                                "yOffset": 0
                            }
                    },
                ],
        })

        pages.push({
            "id": "transitory",
            "backgroundColorStart": "#053C7A",
            "backgroundColorEnd": "#D1C443",
            "backgroundImageURL": "image/year-end-report/2024/transitory.jpg",
            "swipeLottieURL": "image/year-end-report/final/swipe.json",
            "nextButtonTitle": "NEXT",
            "disableBackgroundAnimation": true,
            "widgets": [
               {
                  "type": "TEXT",
                  "data": {
                     "topPadding": 30,
                     "bottomPadding": 0,
                     "textStyle": "H13",
                     "value": "This is just\n the beginning",
                     "color": "#FFFFFF",
                     "centerAlign": true
                  }
               },
               {
                  "type": "HTML",
                  "data": {
                     "topPadding": 10,
                     "bottomPadding": 10,
                     "horizontalPadding": 50,
                     "value": "<div style=\"font-size:17px; color:#FFFFFF; font-weight: normal; text-align: center; font-family: Inter;\">Presenting your world of wins, milestones & unexpected victories...</div>"
                  }
               }
            ]
        })

        if (userYearEndData.class_attend != null && userYearEndData.weekend_at_cult != null) {
            pages.push({
                "id": "sessions_done",
                "backgroundColorStart": "#053C7A",
                "backgroundColorEnd": "#D1C443",
                "backgroundImageURL": "image/year-end-report/2024/no_sessions.jpg",
                "swipeLottieURL": "image/year-end-report/final/swipe.json",
                "shareButtonIcon": "share-2",
                "shareButtonTitle": "SHARE",
                "disableBackgroundAnimation": true,
                "widgets": [
                   {
                      "type": "TEXT",
                      "data": {
                         "topPadding": 30,
                         "bottomPadding": 0,
                         "textStyle": "H13",
                         "value": `${userYearEndData.class_attend} classes\n smashed with ${userYearEndData.weekend_at_cult} weekend class${userYearEndData.weekend_at_cult === 1 ? "" : "es"}!`,
                         "color": "#FFFFFF",
                         "centerAlign": true
                      }
                   },
                   {
                      "type": "HTML",
                      "data": {
                         "topPadding": 10,
                         "bottomPadding": 10,
                         "horizontalPadding": 50,
                         "value": "<div style=\"font-size:17px; color:#FFFFFF; font-weight: normal; text-align: center; font-family: Inter;\">You came. You saw.\n You owned every session.</div>"
                      }
                   }
                ]
            })
        }

        if ((userYearEndData.streak != null && userYearEndData.streak >= 4)) {
            let subTitle = null

            if (userYearEndData.streak === 52) {
                subTitle = "One year, zero breaks. You're the gold standard of consistency!"
            } else {
                subTitle = "You walked the path of consistency. Onward and upward!"
            }

            pages.push(
                {
                    "id": "streak",
                    "backgroundColorStart": "#2A3684",
                    "backgroundColorEnd": "#D9B7CF",
                    "backgroundImageURL": "image/year-end-report/2024/streak.jpg",
                    "shareButtonIcon": "share-2",
                    "shareButtonTitle": "SHARE",
                    "disableBackgroundAnimation": true,
                    "widgets": [
                       {
                          "type": "TEXT",
                          "data": {
                             "topPadding": 30,
                             "bottomPadding": 5,
                             "textStyle": "H13",
                             "value": `${userYearEndData.streak.toLocaleString("en-IN")}-week streak\n this year!`,
                             "color": "#FFFFFF",
                             "centerAlign": true
                          }
                       },
                       {
                          "type": "HTML",
                          "data": {
                             "topPadding": 10,
                             "bottomPadding": 15,
                             "horizontalPadding": 50,
                             "value": `<div style=\"font-size:17px; color:#FFFFFF; font-weight: normal; text-align: center; font-family: Inter;\">${subTitle}</div>`
                          }
                       }
                    ]
                }
            )
        }

        if (userYearEndData.trainername != null && userYearEndData.trainer_classes != null) {
            pages.push(
                {
                    "id": "trainer",
                    "backgroundColorStart": "#075099",
                    "backgroundColorEnd": "#A26FB3",
                    "backgroundImageURL": "image/year-end-report/2024/trainer_without_comp.png",
                    "shareButtonIcon": "share-2",
                    "shareButtonTitle": "SHARE",
                    "disableBackgroundAnimation": true,
                    "widgets": [
                        {
                            "type": "TEXT",
                            "data":
                                {
                                    "topPadding": 30,
                                    "bottomPadding": 5,
                                    "horizontalPadding": 40,
                                    "textStyle": "H13",
                                    "value": `${userYearEndData.trainername}\n was your go-to trainer`,
                                    "color": "#FFFFFF",
                                    "centerAlign": true
                                }
                        },
                        {
                            "type": "HTML",
                            "data":
                                {
                                    "topPadding": 10,
                                    "bottomPadding": 10,
                                    "horizontalPadding": 50,
                                    "value": `<div style="font-size:17px; color:#FFFFFF; font-weight: normal; text-align: center; font-family: Inter;">Guiding you through ${userYearEndData.trainer_classes} workout${userYearEndData.trainer_classes === 1 ? "" : "s"}.</div>`
                                }
                        },
                    ]
                }
            )
        }

        pages.push({
            "id": "calories_burnt",
            "backgroundColorStart": "#042B4D",
            "backgroundColorEnd": "#D9B7CF",
            "backgroundImageURL": "image/year-end-report/2024/calorie_burnt.jpg",
            "shareButtonIcon": "share-2",
            "shareButtonTitle": "SHARE",
            "disableBackgroundAnimation": true,
            "widgets": [
               {
                  "type": "TEXT",
                  "data": {
                     "topPadding": 30,
                     "bottomPadding": 0,
                     "textStyle": "H13",
                     "value": `${userYearEndData.calories.toLocaleString("en-IN")}\ncalories burnt`,
                     "color": "#FFFFFF",
                     "centerAlign": true
                  }
               },
               {
                  "type": "HTML",
                  "data": {
                     "topPadding": 10,
                     "bottomPadding": 10,
                     "horizontalPadding": 50,
                     "value": "<div style=\"font-size:17px; color:#FFFFFF; font-weight: normal; text-align: center; font-family: Inter;\">Safe to say, they're almost\n scared of you now!</div>"
                  }
               }
            ]
        })

        const getFormatId = (formatName: string) => {
            if (formatName === "DANCE") {
                return 1
            } else if (formatName === "SNC" || formatName === "STRENGTH" || formatName === "EQUIPMENT") {
                return 2
            } else if (formatName === "BURN") {
                return 3
            } else if (formatName === "HRX") {
                return 4
            } else if (formatName === "YOGA") {
                return 5
            } else if (formatName === "BOXING") {
                return 6
            } else if (formatName === "GYM") {
                return 7
            } else if (formatName === "Swimming") {
                return 8
            } else if (formatName === "Badminton") {
                return 9
            } else if (formatName === "CARDIO") {
                return 10
            } else {
                return 0
            }
        }

        const addFormatPage = (fav_format: string, fav_format_classes: number, id: string, updateFinalFormat: boolean = false, formats: number) => {
            let title = null
            let imageUrl = null
            let backgroundColorStart = null
            let backgroundColorEnd = null
            let textStyle = "H13"
            let subtitle = ""
            const formatName = fav_format
            const formatClasses = fav_format_classes.toLocaleString("en-IN")
            if (formatName === "DANCE") {
                if (formats < 3) {
                    title = `Your moves made\n it to ${formatClasses} Dance\n Fitness sessions`
                    subtitle = "They are now officially cardio-certified!"
                }
                else {
                    title = `You’re a jack of ${formats} formats. But Dance Fitness? Your sweet spot. ${formatClasses} sessions in the bag!`
                    textStyle = "H1"
                }
                imageUrl = "image/year-end-report/2024/no_dance.jpg"
                backgroundColorStart = "#142F71"
                backgroundColorEnd = "#A13691"
            } else if (formatName === "STRENGTH") {
                if (formats < 3) {
                    title = `Thanks to ${formatClasses}\n sessions of Strength+`
                    subtitle = "You've unlocked: More power. More energy. More footwork."
                }
                else {
                    title = `You’re a jack of ${formats} formats. But Strength+? Your sweet spot. ${formatClasses} sessions in the bag!`
                    textStyle = "H1"
                }
                imageUrl = "image/year-end-report/2024/no_strength_new.jpg"
                backgroundColorStart = "#007CC7"
                backgroundColorEnd = "#99676E"
            } else if (formatName === "BURN") {
                if (formats < 3) {
                    title = `You aced ${formatClasses}\n Burn sessions!`
                    subtitle = "Every rep you crush is another calorie running for its life!"
                }
                else {
                    title = `You’re a jack of ${formats} formats. But Burn? Your sweet spot. ${formatClasses} sessions in the bag!`
                    textStyle = "H1"
                }
                imageUrl = "image/year-end-report/2024/no_burn.jpg"
                backgroundColorStart = "#005FCD"
                backgroundColorEnd = "#EE9EE3"
            } else if (formatName === "HRX") {
                if (formats < 3) {
                    title = `${formatClasses} HRX\n sessions in!`
                    subtitle = "We can almost see your gains through the screen!"
                }
                else {
                    title = `You’re a jack of ${formats} formats. But HRX? Your sweet spot. ${formatClasses} sessions in the bag!`
                    textStyle = "H1"
                }
                imageUrl = "image/year-end-report/2024/hrx_new.jpg"
                backgroundColorStart = "#2379C5"
                backgroundColorEnd = "#A58BD0"
            } else if (formatName === "YOGA") {
                if (formats < 3) {
                    title = `You made it to the\n mat ${formatClasses} times!`
                    subtitle = "Zen-vironment: Your new address."
                }
                else {
                    title = `You’re a jack of ${formats} formats. But Yoga? Your sweet spot. ${formatClasses} sessions in the bag!`
                    textStyle = "H1"
                }
                imageUrl = "image/year-end-report/2024/no_yoga.jpg"
                backgroundColorStart = "#007ECE"
                backgroundColorEnd = "#CDB78F"
            } else if (formatName === "BOXING") {
                if (formats < 3) {
                    title = `${formatClasses} Boxing sessions\n punched in`
                    subtitle = " You’re probably throwing jabs and hooks even in your sleep!"
                }
                else {
                    title = `You’re a jack of ${formats} formats. But Boxing? Your sweet spot. ${formatClasses} sessions in the bag!`
                    textStyle = "H1"
                }
                imageUrl = "image/year-end-report/2024/no_boxing.jpg"
                backgroundColorStart = "#0F1E52"
                backgroundColorEnd = "#A86190"
            } else if (formatName === "CARDIO") {
                if (formats < 3) {
                    title = `Your moves made\n it to ${formatClasses} Cardio sessions`
                    subtitle = "Breaking sweats, and limits!"
                }
                else {
                    title = `You’re a jack of ${formats} formats. But Cardio? Your sweet spot. ${formatClasses} sessions in the bag!`
                    textStyle = "H1"
                }
                imageUrl = "image/year-end-report/2024/no_dance.jpg"
                backgroundColorStart = "#142F71"
                backgroundColorEnd = "#A13691"
            } else if (formatName === "GYM") {
                if (formats < 3) {
                    title = `${formatClasses} Gym sessions\n done and dusted!`
                    subtitle = "TAKE.A.BOW. (and maybe a protein shake)"
                }
                else {
                    title = `You’re a jack of ${formats} formats. But Gym? Your sweet spot. ${formatClasses} sessions in the bag!`
                    textStyle = "H1"
                }
                imageUrl = "image/year-end-report/2024/no_gym.jpg"
                backgroundColorStart = "#428ED7"
                backgroundColorEnd = "#342D6B"
            } else if (formatName === "Badminton") {
                if (formats < 3) {
                    title = `${formatClasses} Badminton\n sessions in!`
                    subtitle = "Serve. Smash. Soar. Repeat. You're owning that court! "
                }
                else {
                    title = `You’re a jack of ${formats} formats. But Badminton? Your sweet spot. ${formatClasses} sessions in the bag!`
                    textStyle = "H1"
                }
                imageUrl = "image/year-end-report/2024/no_badminton.jpg"
                backgroundColorStart = "#006FAE"
                backgroundColorEnd = "#CEBA9E"
            } else if (formatName === "Swimming") {
                if (formats < 3) {
                    title = `${formatClasses} Swim\n sessions in`
                    subtitle = "The pool was your stage, and you performed like a pro."
                }
                else {
                    title = `You’re a jack of ${formats} formats. But Swimming? Your sweet spot. ${formatClasses} sessions in the bag!`
                    textStyle = "H1"
                }
                imageUrl = "image/year-end-report/2024/swim.jpg"
                backgroundColorStart = "#1A4993"
                backgroundColorEnd = "#4493B8"
            } else {
                title = null
            }
            if (title != null) {
                pages.push(
                    {
                        "id": id,
                        "backgroundColorStart": backgroundColorStart,
                        "backgroundColorEnd": backgroundColorEnd,
                        "backgroundImageURL": imageUrl,
                        "shareButtonIcon": "share-2",
                        "shareButtonTitle": "SHARE",
                        "disableBackgroundAnimation": true,
                        "widgets": [
                            {
                                "type": "TEXT",
                                "data":
                                    {
                                        "topPadding": 30,
                                        "bottomPadding": 0,
                                        "horizontalPadding": 30,
                                        "textStyle": textStyle,
                                        "value": `${title}`,
                                        "color": "#FFFFFF",
                                        "centerAlign": true
                                    }
                            },
                            {
                                "type": "HTML",
                                "data":
                                    {
                                        "topPadding": 10,
                                        "bottomPadding": 10,
                                        "horizontalPadding": 50,
                                        "value": `<div style="font-size:17px; color:#FFFFFF; font-weight: normal; text-align: center; font-family: Inter;">${subtitle}</div>`
                                    }
                            },
                        ]
                    }
                )
            }
        }

        function formatNumber(num: number): string {
            if (num % 1 === 0) {
                return num.toString().split(".")[0] // Remove the decimal part
            }
            return num.toString()
        }

        const addFollowUpFormatPage = (fav_format: string, fav_format_classes: number, id: string, fav_format_attribute: string, fav_format_attribute_value: number) => {
            let title = null
            let imageUrl = null
            let backgroundColorStart = null
            let backgroundColorEnd = null
            let textStyle = "H13"
            let subtitle = ""
            const formatName = fav_format
            const formatClasses = fav_format_classes.toLocaleString("en-IN")
            if (formatName === "DANCE" && fav_format_attribute != null) {
                title = `Your 2024 groove:`
                subtitle = `${Math.round(fav_format_attribute_value)} minutes of\n ${fav_format_attribute}`
                imageUrl = "image/year-end-report/2024/wod_dance.jpg"
                backgroundColorStart = "#235B96"
                backgroundColorEnd = "#4AA2BE"
            } else if (formatName === "STRENGTH" && fav_format_attribute != null) {
                title = `After ${formatNumber(fav_format_attribute_value)}\n ${fav_format_attribute} workout${fav_format_attribute_value === 1 ? "" : "s"}`
                if (fav_format_attribute === "Lower Body") {
                    subtitle = `leg day pain? It dreads you!`
                } else if (fav_format_attribute === "Upper Body") {
                    subtitle = `arms, chest, shoulders... it's probably all a piece of cake for you.`
                } else if (fav_format_attribute === "Full Body") {
                    subtitle = `every muscle of your body is screaming, "Bring it on!"`
                }
                imageUrl = "image/year-end-report/2024/wod_strength.jpg"
                backgroundColorStart = "#0C619F"
                backgroundColorEnd = "#A04A9E"
            } else if (formatName === "BURN") {
                title = `${Math.round(fav_format_attribute_value * 6)} circuit\n exercises!`
                subtitle = `Hope you packed an extinguisher. This is LIT!`
                imageUrl = "image/year-end-report/2024/wod_burn.jpg"
                backgroundColorStart = "#0057B6"
                backgroundColorEnd = "#039CCF"
            } else if (formatName === "HRX" && fav_format_attribute != null) {
                if (userYearEndData.start_weight != null && userYearEndData.final_weight != null) {
                    title = `From ${formatNumber(userYearEndData.start_weight)} kgs to\n ${formatNumber(userYearEndData.final_weight)} kgs in ${fav_format_attribute}`
                    subtitle = "You've come far, one lift at a time."
                } else if (userYearEndData.pr_date != null) {
                    const date = new Date(userYearEndData.pr_date)
                    const formattedDate = new Intl.DateTimeFormat("en-US", {
                        day: "numeric",
                        month: "short",
                    }).format(date)
                    title = `You lifted your heaviest,\n ${formatNumber(fav_format_attribute_value)} kgs, during the\n ${fav_format_attribute} on ${formattedDate}.`
                }
                imageUrl = "image/year-end-report/2024/wod_hrx.jpg"
                textStyle = "H1"
                backgroundColorStart = "#1C79CD"
                backgroundColorEnd = "#04AAB6"
            } else if (formatName === "YOGA") {
                title = `You practiced ${Math.round(fav_format_attribute_value * 3)}\n minutes of Pranayama`
                subtitle = `Leaving chaos farrrr behind.`
                imageUrl = "image/year-end-report/2024/wod_yoga.jpg"
                backgroundColorStart = "#007ECE"
                backgroundColorEnd = "#CDB78F"
            } else if (formatName === "BOXING") {
                title = `${Math.round(fav_format_attribute_value * 100)} power punches.`
                subtitle = `No punching bags were harmed. Just thoroughly impressed!`
                imageUrl = "image/year-end-report/2024/wod_boxing.jpg"
                backgroundColorStart = "#1F367E"
                backgroundColorEnd = "#7C6AA9"
            } else {
                title = null
            }
            if (title != null) {
                pages.push(
                    {
                        "id": id,
                        "backgroundColorStart": backgroundColorStart,
                        "backgroundColorEnd": backgroundColorEnd,
                        "backgroundImageURL": imageUrl,
                        "shareButtonIcon": "share-2",
                        "shareButtonTitle": "SHARE",
                        "disableBackgroundAnimation": true,
                        "widgets": [
                            {
                                "type": "TEXT",
                                "data":
                                    {
                                        "topPadding": 30,
                                        "bottomPadding": 0,
                                        "horizontalPadding": 30,
                                        "textStyle": textStyle,
                                        "value": `${title}`,
                                        "color": "#FFFFFF",
                                        "centerAlign": true
                                    }
                            },
                            {
                                "type": "HTML",
                                "data":
                                    {
                                        "topPadding": 10,
                                        "bottomPadding": 10,
                                        "horizontalPadding": 50,
                                        "value": `<div style="font-size:17px; color:#FFFFFF; font-weight: normal; text-align: center; font-family: Inter;">${subtitle}</div>`
                                    }
                            },
                        ]
                    }
                )
            }
        }

        if (userYearEndData.favorate_format_1 != null && userYearEndData.favorate_format_1_classes != null && userYearEndData.formats != null) {
            const fav_1_id = getFormatId(userYearEndData.favorate_format_1)
            if (fav_1_id > 0) {
                addFormatPage(userYearEndData.favorate_format_1, userYearEndData.favorate_format_1_classes, "fav_format_1", true, userYearEndData.formats)
                if (userYearEndData.fav_format_attribute_value != null) {
                    addFollowUpFormatPage(userYearEndData.favorate_format_1, userYearEndData.favorate_format_1_classes, "fav_format_follow_up", userYearEndData.fav_format_attribute, userYearEndData.fav_format_attribute_value)
                }
            }
        }


        if (userYearEndData.country_percentile != null) {
            pages.push(
                {
                    "id": "india_top",
                    "backgroundColorStart": "#1D439B",
                    "backgroundColorEnd": "#E49951",
                    "backgroundImageURL": "image/year-end-report/2024/percentile.jpg",
                    "shareButtonIcon": "share-2",
                    "shareButtonTitle": "SHARE",
                    "disableBackgroundAnimation": true,
                    "widgets": [
                        {
                            "type": "TEXT",
                            "data":
                                {
                                    "topPadding": 30,
                                    "bottomPadding": 5,
                                    "horizontalPadding": 40,
                                    "textStyle": "H13",
                                    "value": `${firstName},\n you're better than ${Math.round(userYearEndData.country_percentile)}% of cult athletes!`,
                                    "color": "#FFFFFF",
                                    "centerAlign": true
                                }
                        }
                    ]
                }
            )
        }

        if (userYearEndData.comebacks != null) {
            pages.push(
                {
                    "id": "comebacks",
                    "backgroundColorStart": "#1B4F9A",
                    "backgroundColorEnd": "#7E367C",
                    "backgroundImageURL": "image/year-end-report/2024/comeback.png",
                    "shareButtonIcon": "share-2",
                    "shareButtonTitle": "SHARE",
                    "disableBackgroundAnimation": true,
                    "widgets": [
                        {
                            "type": "TEXT",
                            "data":
                                {
                                    "topPadding": 30,
                                    "bottomPadding": 5,
                                    "horizontalPadding": 40,
                                    "textStyle": "H13",
                                    "value": `You fought excuses\n and made ${userYearEndData.comebacks}\n comeback${userYearEndData.comebacks === 1 ? "" : "s"} this year.`,
                                    "color": "#FFFFFF",
                                    "centerAlign": true
                                }
                        },
                    ]
                }
            )
        }

        if (userYearEndData.karma_points != null) {
            let title = null
            let subTitle = null

            if (userYearEndData.karma_points <= 2) {
                title = `You earned ${userYearEndData.karma_points}\n Karma Point${userYearEndData.karma_points === 1 ? "" : "s"}`
                subTitle = `Small start, big impact. Here’s to inspiring many more!`
            } else if (userYearEndData.karma_points <= 5) {
                title = `${userYearEndData.karma_points} Karma Points,\n well-earned!`
                subTitle = `Your energy fueled not only your journey, but someone else's too.`
            } else {
                title = `Your ${userYearEndData.karma_points} Karma Points\n show big gains`
                subTitle = `Built on extra reps... of kindness. `
            }

            pages.push(
                {
                    "id": "karma_points",
                    "backgroundColorStart": "#0068BF",
                    "backgroundColorEnd": "#D0C1B4",
                    "backgroundImageURL": "image/year-end-report/2024/karma_2.jpg",
                    "shareButtonIcon": "share-2",
                    "shareButtonTitle": "SHARE",
                    "disableBackgroundAnimation": true,
                    "widgets": [
                        {
                            "type": "TEXT",
                            "data":
                                {
                                    "topPadding": 30,
                                    "bottomPadding": 5,
                                    "horizontalPadding": 40,
                                    "textStyle": "H13",
                                    "value": title,
                                    "color": "#FFFFFF",
                                    "centerAlign": true
                                }
                        },
                        {
                            "type": "HTML",
                            "data":
                                {
                                    "topPadding": 10,
                                    "bottomPadding": 10,
                                    "horizontalPadding": 50,
                                    "value": `<div style="font-size:17px; color:#FFFFFF; font-weight: normal; text-align: center; font-family: Inter;">${subTitle}</div>`
                                }
                        },
                    ]
                }
            )
        }


        if (userYearEndData.squadclasses != null && userYearEndData.squaduser != null) {
            pages.push(
                {
                    "id": "squads",
                    "backgroundColorStart": "#1A509B",
                    "backgroundColorEnd": "#204E7E",
                    "backgroundImageURL": "image/year-end-report/2024/squad_comp.jpg",
                    "shareButtonIcon": "share-2",
                    "shareButtonTitle": "SHARE",
                    "disableBackgroundAnimation": true,
                    "widgets": [
                        {
                            "type": "TEXT",
                            "data":
                                {
                                    "topPadding": 30,
                                    "bottomPadding": 5,
                                    "horizontalPadding": 40,
                                    "textStyle": "H13",
                                    "value": `${userYearEndData.squadclasses} class${userYearEndData.squadclasses === 1 ? "" : "es"} with\n ${userYearEndData.squaduser}`,
                                    "color": "#FFFFFF",
                                    "centerAlign": true
                                }
                        },
                        {
                            "type": "HTML",
                            "data":
                                {
                                    "topPadding": 10,
                                    "bottomPadding": 10,
                                    "horizontalPadding": 50,
                                    "value": `<div style="font-size:17px; color:#FFFFFF; font-weight: normal; text-align: center; font-family: Inter;">You seem like the ultimate duo. Always lifting each other up!</div>`
                                }
                        },
                    ]
                }
            )
        }

        if (userYearEndData.best_month != null && userYearEndData.days_worked_out_best_month != null) {
            let monthImageURL = "image/year-end-report/2024/dec_jan.jpg"
            let subTitle = "When the temperature dropped, your energy skyrocketed!"
            let backgroundColorStart = "#244A91"
            let backgroundColorEnd = "#CD7B2E"
            const month = userYearEndData.best_month.trim().toLowerCase()
            if (month === "january") {
                monthImageURL = "image/year-end-report/2024/dec_jan.jpg"
                subTitle = "When the temperature dropped, your energy skyrocketed!"
                backgroundColorStart = "#4271BC"
                backgroundColorEnd = "#74C0DC"
            } else if (month === "february") {
                monthImageURL = "image/year-end-report/2024/feb_mar.jpg"
                subTitle = "Making 'New Year, New Me' look so easy!"
                backgroundColorStart = "#2B5D87"
                backgroundColorEnd = "#CD962C"
            } else if (month === "march") {
                monthImageURL = "image/year-end-report/2024/feb_mar.jpg"
                subTitle = "Making 'New Year, New Me' look so easy!"
                backgroundColorStart = "#2B5D87"
                backgroundColorEnd = "#CD962C"
            } else if (month === "april") {
                monthImageURL = "image/year-end-report/2024/apr_may.jpg"
                subTitle = "You didn’t just beat the heat, you sizzled through it!"
                backgroundColorStart = "#244A91"
                backgroundColorEnd = "#CD7B2E"
            } else if (month === "may") {
                monthImageURL = "image/year-end-report/2024/apr_may.jpg"
                subTitle = "You didn’t just beat the heat, you sizzled through it!"
                backgroundColorStart = "#244A91"
                backgroundColorEnd = "#CD7B2E"
            } else if (month === "june") {
                monthImageURL = "image/year-end-report/2024/jun_july_aug.jpg"
                subTitle = "Literally nothing could rain over your goals!"
                backgroundColorStart = "#42728B"
                backgroundColorEnd = "#163649"
            } else if (month === "july") {
                monthImageURL = "image/year-end-report/2024/jun_july_aug.jpg"
                subTitle = "Literally nothing could rain over your goals!"
                backgroundColorStart = "#42728B"
                backgroundColorEnd = "#163649"
            } else if (month === "august") {
                monthImageURL = "image/year-end-report/2024/jun_july_aug.jpg"
                subTitle = "Literally nothing could rain over your goals!"
                backgroundColorStart = "#42728B"
                backgroundColorEnd = "#163649"
            } else if (month === "september") {
                monthImageURL = "image/year-end-report/2024/sep_oct_nov.jpg"
                subTitle = "Snacks and sweets stood no chance against your dedication!"
                backgroundColorStart = "#0E608E"
                backgroundColorEnd = "#B46C3C"
            } else if (month === "october") {
                monthImageURL = "image/year-end-report/2024/sep_oct_nov.jpg"
                subTitle = "Snacks and sweets stood no chance against your dedication!"
                backgroundColorStart = "#0E608E"
                backgroundColorEnd = "#B46C3C"
            } else if (month === "november") {
                monthImageURL = "image/year-end-report/2024/sep_oct_nov.jpg"
                subTitle = "Snacks and sweets stood no chance against your dedication!"
                backgroundColorStart = "#0E608E"
                backgroundColorEnd = "#B46C3C"
            } else if (month === "december") {
                monthImageURL = "image/year-end-report/2024/dec_jan.jpg"
                subTitle = "When the temperature dropped, your energy skyrocketed!"
                backgroundColorStart = "#4271BC"
                backgroundColorEnd = "#74C0DC"
            }

            pages.push(
                {
                    "id": "best_month",
                    "backgroundColorStart": backgroundColorStart,
                    "backgroundColorEnd": backgroundColorEnd,
                    "backgroundImageURL": monthImageURL,
                    "shareButtonIcon": "share-2",
                    "shareButtonTitle": "SHARE",
                    "disableBackgroundAnimation": true,
                    "widgets": [
                        {
                            "type": "TEXT",
                            "data":
                                {
                                    "topPadding": 30,
                                    "bottomPadding": 5,
                                    "horizontalPadding": 50,
                                    "textStyle": "H13",
                                    "value": `You crushed ${userYearEndData.days_worked_out_best_month}\n sessions in ${userYearEndData.best_month}`,
                                    "color": "#FFFFFF",
                                    "centerAlign": true
                                }
                        },
                        {
                            "type": "HTML",
                            "data":
                                {
                                    "topPadding": 10,
                                    "bottomPadding": 10,
                                    "horizontalPadding": 50,
                                    "value": `<div style="font-size:17px; color:#FFFFFF; font-weight: normal; text-align: center; font-family: Inter;">${subTitle}</div>`
                                }
                        },
                    ]
                }
            )
        }

        pages.push(
            {
                "id": "referral",
                "backgroundColorStart": "#C75F52",
                "backgroundColorEnd": "#003278",
                "backgroundImageURL": "image/year-end-report/2024/referral.jpg",
                "shareButtonIcon": "share-2",
                "shareButtonTitle": "Tell Your Friends",
                "shareButtonAction": {
                    "actionType": "NAVIGATION",
                    "url": "curefit://referralpagev2"
                },
                "disableBackgroundAnimation": true,
                "widgets": [
                    {
                        "type": "TEXT",
                        "data":
                            {
                                "topPadding": 30,
                                "bottomPadding": 5,
                                "horizontalPadding": 40,
                                "textStyle": "H13",
                                "value": `Rope a buddy in!`,
                                "color": "#FFFFFF",
                                "centerAlign": true
                            }
                    },
                    {
                        "type": "HTML",
                        "data":
                            {
                                "topPadding": 10,
                                "bottomPadding": 10,
                                "horizontalPadding": 50,
                                "value": `<div style="font-size:17px; color:#FFFFFF; font-weight: normal; text-align: center; font-family: Inter;">Each referral extends your membership by a month.</div>`
                            }
                    },
                ]
            }
        )

        pages.push({
            "id": "summary_2",
            "backgroundColorStart": "#2E80C5",
            "backgroundColorEnd": "#712C72",
            "backgroundImageURL": "image/year-end-report/2024/medal.jpg",
            "shareButtonIcon": "share-2",
            "shareButtonTitle": "SHARE",
            "nextButtonTitle": "NEXT",
            "disableBackgroundAnimation": true,
            "widgets":
                [
                    {
                        "type": "TEXT",
                        "data":
                            {
                                "topPadding": 30,
                                "bottomPadding": 0,
                                "textStyle": "H6",
                                "value": `${user.firstName}'s\n 2024 Journey`,
                                "color": "#FFFFFF",
                                "centerAlign": true
                            }
                    },
                    {
                        "type": "GRID_TEXT",
                        "data":
                            {
                                "items": items,
                                "topPadding": 0.40,
                                "bottomPadding": 30,
                                "horizontalPadding": 10,
                                "titleColor": "#FFFFFF",
                                "titleStyle": "KeplerStd30",
                                "subtitleColor": "#FFFFFF",
                                "subtitleStyle": "P5",
                                "yOffset": 0
                            }
                    },
                ],
        })

        pages.push(
            {
                "id": "closing_screen",
                "backgroundColorStart": "#2E3471",
                "backgroundColorEnd": "#844C77",
                "backgroundImageURL": "image/year-end-report/2024/closing_screen.jpg",
                "shareButtonTitle": "Replay report",
                "shareButtonAction": {
                    "actionType": "RESET_NAVIGATION",
                    "url": "curefit://yearendreport"
                },
                "disableBackgroundAnimation": true,
                "widgets": [
                    {
                        "type": "TEXT",
                        "data":
                            {
                                "topPadding": 30,
                                "bottomPadding": 5,
                                "horizontalPadding": 40,
                                "textStyle": "H13",
                                "value": `${firstName},\n you turned small steps into BIG WINS`,
                                "color": "#FFFFFF",
                                "centerAlign": true
                            }
                    },
                    {
                        "type": "HTML",
                        "data":
                            {
                                "topPadding": 10,
                                "bottomPadding": 10,
                                "horizontalPadding": 50,
                                "value": `<div style="font-size:17px; color:#FFFFFF; font-weight: normal; text-align: center; font-family: Inter;">See you in 2025, crushing it harder!</div>`
                            }
                    },
                ]
            }
        )

        const pledgeScreen = {
            "id": "pledge",
            "backgroundColorStart": "#2B432F",
            "backgroundColorEnd": "#213828",
        }

        const inactive_and_active_expiring_offer = {
            "id": "inactive_and_active_expiring",
            "backgroundColorStart": "#FFDB69",
            "backgroundColorEnd": "#000000",
            "backgroundImageURL":
                "image/year-end-report/inactive_and_active_expiring.png",
            "widgets": [
              {
                "type": "TEXT",
                "data": {
                  "topPadding": 100,
                  "bottomPadding": 0,
                  "textStyle": "H13",
                  "value": "Kudos! You\nhave been\ntruely awesome",
                  "color": "#FFDB69",
                  "centerAlign": true
                }
              },
              {
                "type": "HTML",
                "data": {
                  "topPadding": 10,
                  "bottomPadding": 10,
                  "horizontalPadding": 50,
                  "value":
                      "<div style=\"font-size:17px; color:#FFFFFF; font-weight: normal; text-align: center; font-family: Inter;\">You have been truely remarkable in your fitness journey so far, and we have a <b>special offer</b> to keep you going!</div>"
                }
              }
            ]
          }

          const dead_base_offer = {
            "id": "dead_base",
            "backgroundColorStart": "#FFDB69",
            "backgroundColorEnd": "#000000",
            "backgroundImageURL": "image/year-end-report/dead_base.png",
            "widgets": [
              {
                "type": "TEXT",
                "data": {
                  "topPadding": 100,
                  "bottomPadding": 0,
                  "textStyle": "H13",
                  "value": "Enjoy\nexclusive offer\njust for you!",
                  "color": "#FFDB69",
                  "centerAlign": true
                }
              },
              {
                "type": "HTML",
                "data": {
                  "topPadding": 10,
                  "bottomPadding": 10,
                  "horizontalPadding": 50,
                  "value":
                      "<div style=\"font-size:17px; color:#FFFFFF; font-weight: normal; text-align: center; font-family: Inter;\">Get access to all\ncult centres, gym & play centres\nto regain your fitness routine.</div>"
                }
              }
            ]
        }

        const dead_base_with_play = {
            "id": "dead_base",
            "backgroundColorStart": "#FFDB69",
            "backgroundColorEnd": "#000000",
            "backgroundImageURL": "image/year-end-report/DeadBaseWithPlayV3.png",
        }

        const dead_base_without_play = {
            "id": "dead_base",
            "backgroundColorStart": "#FFDB69",
            "backgroundColorEnd": "#000000",
            "backgroundImageURL": "image/year-end-report/DeadBaseWithoutPlayV3.png",
        }


        if (isDeadBaseAlumniPassPlayUser) {
            pages.push(dead_base_with_play)
        }
        else if (isDeadBaseAlumniPassNonPlayUser) {
            pages.push(dead_base_without_play)
        }

        let welcomeScreenCta = null

        if (userContext.sessionInfo.appVersion < 10.79) {
            welcomeScreenCta = "UPDATE YOUR APP"
        } else {
            welcomeScreenCta = "VIEW MY REPORT"
        }

        return {
            "welcomeScreen": {
                "id": "intro",
                "backgroundLottieURL": "image/year-end-report/2024/welcome_lottie.json",
                "backgroundColorStart": "#000000",
                "backgroundColorEnd": "#000000",
                "animationDuration": 4,
                "bottomTextWidget": {
                    "type": "TEXT",
                    "data":
                        {
                            "topPadding": 10,
                            "bottomPadding": 80,
                            "textStyle": "H2",
                            "value": welcomeScreenCta,
                            "color": "#FFFFFF",
                            "centerAlign": true
                        }
                },
                "widgets": [
                    {
                       "type": "TEXT",
                       "data": {
                          "topPadding": 10,
                          "bottomPadding": 4,
                          "textStyle": "P1",
                          "value": "cult presents",
                          "color": "#FFFFFF",
                          "centerAlign": true
                       }
                    },
                    {
                       "type": "MEDIA",
                       "data": {
                          "type": "image",
                          "url": "image/year-end-report/v_man_color.png",
                          "height": 45,
                          "width": 40,
                          "topPadding": 10,
                          "bottomPadding": 0
                       }
                    },
                    {
                       "type": "TEXT",
                       "data": {
                          "topPadding": 40,
                          "bottomPadding": 0,
                          "textStyle": "H14",
                          "value": "YOUR 2024",
                          "color": "#FFFFFF",
                          "centerAlign": true
                       }
                    },
                    {
                       "type": "TEXT",
                       "data": {
                          "topPadding": 0,
                          "bottomPadding": 20,
                          "textStyle": "H14",
                          "value": "BIG MOMENTS",
                          "color": "#FFFFFF",
                          "centerAlign": true
                       }
                    },
                    {
                       "type": "TEXT",
                       "data": {
                          "topPadding": 10,
                          "bottomPadding": 0,
                          "textStyle": "H1",
                          "value": `${firstName},`,
                          "color": "#FFFFFF",
                          "centerAlign": true
                       }
                    },
                    {
                       "type": "TEXT",
                       "data": {
                          "topPadding": 0,
                          "bottomPadding": 30,
                          "horizontalPadding": 50,
                          "textStyle": "P2",
                          "value": "it's been an incredible year!\n Time to hit rewind and flex on\n your fitness highlights.",
                          "color": "#FFFFFF",
                          "centerAlign": true
                       }
                    }
                 ]
            },
            "pledgeScreen": null,
            "pages": pages,
        }
    }

    public async getUserAchievemtReport(userContext: UserContext): Promise<any> {
        const properCase = (text: string) => {
            return text.replace("_", " ").replace(/\w\S*/g, function (txt) {
                return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
            })
        }
        const user = await userContext.userPromise
        const firstName = properCase(user.firstName ?? user.lastName)
        const userYearEndData: UserAchievmentShowcase = await this.userYearEndSummaryReadOnlyDaoV2.findOne({user_id: user.id})
        if (!userYearEndData) {
            this.logger.error("Error in achievemt report, user data dont exist, userId: " + user.id)
            return null
        }

        const isDeadBaseAlumniPassPlayUser = await CultUtil.doesUserBelongToDeadBaseAlumniPassElitePlaySegment(userContext, this.serviceInterfaces)
        const isDeadBaseAlumniPassNonPlayUser = await CultUtil.doesUserBelongToDeadBaseAlumniPassEliteNonPlaySegment(userContext, this.serviceInterfaces)

        const pages: any = []

        const items: any = []

        const getFormatName = (formatName: string) => {
            if (formatName === "DANCE") {
                return "DANCE\nFITNESS"
            } else if (formatName === "SNC" || formatName === "STRENGTH" || formatName === "EQUIPMENT") {
                return "\nSTRENGTH+"
            } else if (formatName === "BURN") {
                return "\nBURN"
            } else if (formatName === "HRX") {
                return "\nHRX"
            } else if (formatName === "YOGA") {
                return "\nYOGA"
            } else if (formatName === "BOXING") {
                return "\nBOXING"
            } else if (formatName === "GYM") {
                return "\nGYM"
            } else if (formatName === "Swimming") {
                return "\nSWIMMING"
            } else if (formatName === "Badminton") {
                return "\nBADMINTON"
            } else if (formatName === "CARDIO") {
                return "\nCARDIO"
            } else {
                return 0
            }
        }

        items.push({
            "title": `${userYearEndData.classes_attended.toLocaleString("en-IN")}`,
            "subtitle": "Sessions done"
        })
        items.push({
            "title": `${userYearEndData.calories.toLocaleString("en-IN")}`,
            "subtitle": "Calories burnt"
        })
        items.push({
            "title": `${userYearEndData.weeks_active.toLocaleString("en-IN")}\nWeek${userYearEndData.weeks_active === 1 ? "" : "s"}`,
            "subtitle": "Longest streak"
        })

        if (userYearEndData.favorate_format_1 != null) {
            const finalFormatName = getFormatName(userYearEndData.favorate_format_1)
            items.push( {
                "title": `${finalFormatName}`,
                "subtitle": "Most done format"
            })
        }

        pages.push({
            "id": "achievAchievement_summary",
            "backgroundColorStart": "#2E80C5",
            "backgroundColorEnd": "#712C72",
            "backgroundImageURL": "image/achievement-report/summary-update.png",
            "backgroundAudioURL": "https://curefit-content.s3.ap-south-1.amazonaws.com/image/achievement-report/music/Audio_01.mp3",
            "backgroundAudioName": "cult.fit · Original audio",
            "swipeLottieURL": "image/year-end-report/final/swipe.json",
            "shareButtonIcon": "share-2",
            "shareButtonTitle": "SHARE",
            "nextButtonTitle": "NEXT",
            "disableBackgroundAnimation": true,
            "widgets":
                [
                    {
                        "type": "TEXT",
                        "data":
                            {
                                "topPadding": 30,
                                "bottomPadding": 0,
                                "textStyle": "H9",
                                "value": "You stood out.\nAs did your score.",
                                "color": "#FFFFFF",
                                "centerAlign": true
                            }
                    },
                    {
                        "type": "GRID_TEXT",
                        "data":
                            {
                                "items": items,
                                "topPadding": 0.40,
                                "bottomPadding": 30,
                                "horizontalPadding": 10,
                                "titleColor": "#FFFFFF",
                                "titleStyle": "KeplerStd30",
                                "subtitleColor": "#FFFFFF",
                                "subtitleStyle": "P5",
                                "yOffset": 0
                            }
                    },
                ],
        })

        if (userYearEndData.total_minutes_worked != null) {
            const totalMinutes = userYearEndData.total_minutes_worked
            const totalReels = Math.floor((totalMinutes * 60) / 15)
            pages.push(
                {
                    "id": "totalMinutes",
                    "backgroundColorStart": "#075099",
                    "backgroundColorEnd": "#A26FB3",
                    "backgroundImageURL": "image/achievement-report/journey_new.png",
                    "backgroundAudioURL": "https://curefit-content.s3.ap-south-1.amazonaws.com/image/achievement-report/music/Audio_02.mp3",
                    "backgroundAudioName": "cult.fit · Original audio",
                    "swipeLottieURL": "image/year-end-report/final/swipe.json",
                    "shareButtonIcon": "share-2",
                    "shareButtonTitle": "SHARE",
                    "disableBackgroundAnimation": true,
                    "widgets": [
                        {
                            "type": "TEXT",
                            "data":
                                {
                                    "topPadding": 30,
                                    "bottomPadding": 5,
                                    "horizontalPadding": 40,
                                    "textStyle": "H9",
                                    "value": `You worked out for ${userYearEndData.total_minutes_worked} minute${userYearEndData.total_minutes_worked === 1 ? "" : "s"}.`,
                                    "color": "#FFFFFF",
                                    "centerAlign": true
                                }
                        },
                        {
                            "type": "HTML",
                            "data":
                                {
                                    "topPadding": 5,
                                    "bottomPadding": 10,
                                    "horizontalPadding": 50,
                                    "value": `<div style=\"font-size:16px; color:#FFFFFF; font-weight: 400; text-align: center; font-family: Inter; letter-spacing: -0.32px;\">This has saved you from watching <b>${totalReels}</b> reels!</div>`
                                }
                        },
                    ]
                }
            )
        }

        if (userYearEndData.classes_attended != null) {
            pages.push({
                "id": "sessions_done",
                "backgroundColorStart": "#053C7A",
                "backgroundColorEnd": "#D1C443",
                "backgroundImageURL": "image/achievement-report/milestone.png",
                "swipeLottieURL": "image/year-end-report/final/swipe.json",
                "shareButtonIcon": "share-2",
                "shareButtonTitle": "SHARE",
                "disableBackgroundAnimation": true,
                "widgets": [
                   {
                      "type": "TEXT",
                      "data": {
                         "topPadding": 30,
                         "bottomPadding": 0,
                         "textStyle": "H9",
                         "value": `For ${userYearEndData.classes_attended} workouts,\n you chose 'Now' over 'Maybe later.'`,
                         "color": "#FFFFFF",
                         "centerAlign": true
                      }
                   }
                ]
            })
        }

        if (userYearEndData.country_percentile != null) {
            pages.push(
                {
                    "id": "india_top",
                    "backgroundColorStart": "#1D439B",
                    "backgroundColorEnd": "#E49951",
                    "backgroundImageURL": "image/year-end-report/2024/percentile.jpg",
                    "swipeLottieURL": "image/year-end-report/final/swipe.json",
                    "shareButtonIcon": "share-2",
                    "shareButtonTitle": "SHARE",
                    "disableBackgroundAnimation": true,
                    "widgets": [
                        {
                            "type": "TEXT",
                            "data":
                                {
                                    "topPadding": 30,
                                    "bottomPadding": 5,
                                    "horizontalPadding": 40,
                                    "textStyle": "H9",
                                    "value": `You're in Top ${Math.round((1 - userYearEndData.country_percentile) * 100)}%`,
                                    "color": "#FFFFFF",
                                    "centerAlign": true
                                }
                        },
                        {
                            "type": "HTML",
                            "data":
                                {
                                    "topPadding": 5,
                                    "bottomPadding": 10,
                                    "horizontalPadding": 50,
                                    "value": `<div style=\"font-size:16px; color:#FFFFFF; font-weight: 400; text-align: center; font-family: Inter; letter-spacing: -0.32px;\">One of the most consistent cult athletes in the country.</div>`
                                }
                        },
                    ]
                }
            )
        }

        if (userYearEndData.streak != null) {
            if (userYearEndData.streak === 52) {
                const subTitle = "Rain or shine, you showed up, week after week."
                pages.push({
                    "id": "streak_active",
                    "backgroundColorStart": "#2A3684",
                    "backgroundColorEnd": "#D9B7CF",
                    "backgroundImageURL": "image/achievement-report/weeks-streaks.png",
                    "swipeLottieURL": "image/year-end-report/final/swipe.json",
                    "shareButtonIcon": "share-2",
                    "shareButtonTitle": "SHARE",
                    "disableBackgroundAnimation": true,
                    "widgets": [
                        {
                            "type": "TEXT",
                            "data": {
                                "topPadding": 30,
                                "bottomPadding": 5,
                                "textStyle": "H13",
                                "value": `Longest streak \n ${userYearEndData.streak.toLocaleString("en-IN")} weeks`,
                                "color": "#FFFFFF",
                                "centerAlign": true
                            }
                        },
                        {
                            "type": "HTML",
                            "data": {
                                "topPadding": 5,
                                "bottomPadding": 15,
                                "horizontalPadding": 50,
                                "value": `<div style=\"font-size:17px; color:#FFFFFF; font-weight: normal; text-align: center; font-family: Inter;\">${subTitle}</div>`
                            }
                        }
                    ]
                })
            } else {
                const subTitle = "Your calendar called. It looks super fit with so many weeks of workout."
                pages.push({
                    "id": "weeks_active",
                    "backgroundColorStart": "#2A3684",
                    "backgroundColorEnd": "#D9B7CF",
                    "backgroundImageURL": "image/achievement-report/weeks-active.png",
                    "swipeLottieURL": "image/year-end-report/final/swipe.json",
                    "shareButtonIcon": "share-2",
                    "shareButtonTitle": "SHARE",
                    "disableBackgroundAnimation": true,
                    "widgets": [
                        {
                            "type": "TEXT",
                            "data": {
                                "topPadding": 30,
                                "bottomPadding": 5,
                                "textStyle": "H13",
                                "value": `Weeks \n active ${userYearEndData.streak.toLocaleString("en-IN")}`,
                                "color": "#FFFFFF",
                                "centerAlign": true
                            }
                        },
                        {
                            "type": "HTML",
                            "data": {
                                "topPadding": 5,
                                "bottomPadding": 15,
                                "horizontalPadding": 50,
                                "value": `<div style=\"font-size:17px; color:#FFFFFF; font-weight: normal; text-align: center; font-family: Inter;\">${subTitle}</div>`
                            }
                        }
                    ]
                })
            }
        }

        const getFormatId = (formatName: string) => {
            if (formatName === "DANCE" || formatName === "CARDIO") {
                return 1
            } else if (formatName === "SNC" || formatName === "STRENGTH" || formatName === "EQUIPMENT") {
                return 2
            } else if (formatName === "BURN") {
                return 3
            } else if (formatName === "HRX") {
                return 4
            } else if (formatName === "YOGA") {
                return 5
            } else if (formatName === "BOXING") {
                return 6
            } else if (formatName === "GYM") {
                return 7
            } else if (formatName === "Swimming") {
                return 8
            } else if (formatName === "Badminton") {
                return 9
            } else {
                return 0
            }
        }

        const addFormatPage = (fav_format: string, fav_format_classes: number, id: string, updateFinalFormat: boolean = false, formats: number) => {
            let title = null
            let imageUrl = null
            let backgroundColorStart = null
            let backgroundColorEnd = null
            let textStyle = "H9"
            let subtitle = null
            const formatName = fav_format
            const formatClasses = fav_format_classes.toLocaleString("en-IN")
            if (formatName === "DANCE" || formatName === "CARDIO") {
                    title = `${formatClasses}\n Dance Sessions`
                    subtitle = "You danced your way to less stress, more calorie-burn, and the grooviest of workouts."
                    textStyle = "H9"
                    imageUrl = "image/achievement-report/format_dance_new.png"
                    backgroundColorStart = "#142F71"
                    backgroundColorEnd = "#A13691"
            } else if (formatName === "SNC" || formatName === "STRENGTH" || formatName === "EQUIPMENT") {
                    title = `${formatClasses}\n Strength+ Sessions`
                    subtitle = "You built more strength, topped up on stamina, and pushed your endurance. That’s like Strength+++++"
                    textStyle = "H9"
                    imageUrl = "image/achievement-report/format_strength_new.png"
                    backgroundColorStart = "#007CC7"
                    backgroundColorEnd = "#99676E"
            } else if (formatName === "BURN") {
                    title = `${formatClasses}\n burn Sessions`
                    subtitle = "You climbed the height of strength, calorie-burn, and ultimate fitness like a champ."
                    textStyle = "H9"
                    imageUrl = "image/achievement-report/format_burn.png"
                    backgroundColorStart = "#005FCD"
                    backgroundColorEnd = "#EE9EE3"
            } else if (formatName === "HRX") {
                    title = `${formatClasses}\n HRX Sessions`
                    subtitle = "You trained like Hrithik. Built strength, muscle, joint-health, and a great mood!"
                    textStyle = "H9"
                    imageUrl = "image/achievement-report/format_hrx.png"
                    backgroundColorStart = "#2379C5"
                    backgroundColorEnd = "#A58BD0"
            } else if (formatName === "YOGA") {
                    title = `${formatClasses}\n Yoga Sessions`
                    subtitle = "You stretched your limits, imrpoved breathing & corrected balance. That’s peace in a package!"
                    textStyle = "H9"
                    imageUrl = "image/achievement-report/format_yoga_new.png"
                    backgroundColorStart = "#007ECE"
                    backgroundColorEnd = "#CDB78F"
            } else if (formatName === "BOXING") {
                    title = `${formatClasses} Boxing Sessions \n punched in`
                    subtitle = "You punched in more stamina, built that core, and gained all-time boxer-like agility."
                    textStyle = "H9"
                    imageUrl = "image/achievement-report/format_boxing_new.png"
                    backgroundColorStart = "#0F1E52"
                    backgroundColorEnd = "#A86190"
            } else if (formatName === "GYM") {
                    title = `${formatClasses} Gym Sessions \n done and dusted!`
                    subtitle = "You’re the gym-freak who conquered it all. Leg-day or shoulders, you slayed!"
                    textStyle = "H9"
                    imageUrl = "image/achievement-report/format_gym.png"
                    backgroundColorStart = "#428ED7"
                    backgroundColorEnd = "#342D6B"
            } else if (formatName === "Badminton") {
                    title = `${formatClasses}\n badminton hours`
                    subtitle = "You smashed it, served it, and fully deserved it."
                    textStyle = "H9"
                    imageUrl = "image/achievement-report/format_batminton.png"
                    backgroundColorStart = "#006FAE"
                    backgroundColorEnd = "#CEBA9E"
            } else if (formatName === "Swimming") {
                title = `${formatClasses}\n swims`
                subtitle = "You lapped up your stamina, toned up that muscle, and swam to glory."
                textStyle = "H9"
                imageUrl = "image/achievement-report/format_swimming.png"
                backgroundColorStart = "#1A4993"
                backgroundColorEnd = "#4493B8"
            } else {
                title = null
            }
            if (title != null) {
                pages.push(
                    {
                        "id": id,
                        "backgroundColorStart": backgroundColorStart,
                        "backgroundColorEnd": backgroundColorEnd,
                        "backgroundImageURL": imageUrl,
                        "swipeLottieURL": "image/year-end-report/final/swipe.json",
                        "shareButtonIcon": "share-2",
                        "shareButtonTitle": "SHARE",
                        "disableBackgroundAnimation": true,
                        "widgets": [
                            {
                                "type": "TEXT",
                                "data":
                                    {
                                        "topPadding": 30,
                                        "bottomPadding": 0,
                                        "horizontalPadding": 30,
                                        "textStyle": textStyle,
                                        "value": `${title}`,
                                        "color": "#FFFFFF",
                                        "centerAlign": true
                                    }
                            },
                            {
                                "type": "HTML",
                                "data":
                                    {
                                        "topPadding": 10,
                                        "bottomPadding": 10,
                                        "horizontalPadding": 50,
                                        "value": `<div style=\"font-size:16px; color:#FFFFFF; font-weight: 400; text-align: center; font-family: Inter; letter-spacing: -0.32px;\">${subtitle}</div>`
                                    }
                            },
                        ]
                    }
                )
            }
        }

        if (userYearEndData.favorate_format_1 != null && userYearEndData.favorate_format_1_classes != null && userYearEndData.formats != null) {
            const fav_1_id = getFormatId(userYearEndData.favorate_format_1)
            if (fav_1_id > 0) {
                addFormatPage(userYearEndData.favorate_format_1, userYearEndData.favorate_format_1_classes, "fav_format_1", true, userYearEndData.formats)
            }
        }

    const formatClasses = [
        userYearEndData.favorate_format_1,
        userYearEndData.favorate_format_2,
        userYearEndData.favorate_format_3
    ]
    const keepUppercase = new Set(["HRX"])
    const nonNullFormats = formatClasses
    .filter(Boolean)
    .map(format => {
        if (keepUppercase.has(format.toUpperCase())) {
            return format.toUpperCase()
        }
        return format.charAt(0).toUpperCase() + format.slice(1).toLowerCase()
    })
    if (userYearEndData.formats >= 2 && nonNullFormats.length >= 2) {
        pages.push({
            "id": "format_list",
            "backgroundColorStart": "#1D439B",
            "backgroundColorEnd": "#E49951",
            "backgroundImageURL": "image/achievement-report/fav_format.png",
            "swipeLottieURL": "image/year-end-report/final/swipe.json",
            "shareButtonIcon": "share-2",
            "shareButtonTitle": "SHARE",
            "disableBackgroundAnimation": true,
            "widgets": [
                {
                    "type": "TEXT",
                    "data": {
                        "topPadding": 30,
                        "bottomPadding": 5,
                        "horizontalPadding": 40,
                        "textStyle": "H9",
                        "value": nonNullFormats.join(", "),
                        "color": "#FFFFFF",
                        "centerAlign": true
                    }
                },
                {
                    "type": "HTML",
                    "data": {
                        "topPadding": 5,
                        "bottomPadding": 10,
                        "horizontalPadding": 50,
                        "value": `<div style="font-size:16px; color:#FFFFFF; font-weight: 400; text-align: center; font-family: Inter; letter-spacing: -0.32px;">If you were a kid, and cult a toy store, you'd have played with everything.</div>`
                    }
                }
            ]
        })
    }

    if (userYearEndData.cities_count != null) {
        const favouriteCities = [
            userYearEndData.favourite_city_1,
            userYearEndData.favourite_city_2,
            userYearEndData.favourite_city_3
        ].filter(city => city != null)

        if (favouriteCities.length >= 2) {
            const textValue = `Cities worked out in:\n${favouriteCities.join(", ")}`
            pages.push({
                "id": "cities_worked_out",
                "backgroundColorStart": "#1D439B",
                "backgroundColorEnd": "#E49951",
                "backgroundImageURL": "image/achievement-report/cities_visited.png",
                "swipeLottieURL": "image/year-end-report/final/swipe.json",
                "shareButtonIcon": "share-2",
                "shareButtonTitle": "SHARE",
                "disableBackgroundAnimation": true,
                "widgets": [
                    {
                        "type": "TEXT",
                        "data": {
                            "topPadding": 30,
                            "bottomPadding": 5,
                            "horizontalPadding": 40,
                            "textStyle": "H9",
                            "value": textValue,
                            "color": "#FFFFFF",
                            "centerAlign": true
                        }
                    },
                    {
                        "type": "HTML",
                        "data": {
                            "topPadding": 5,
                            "bottomPadding": 10,
                            "horizontalPadding": 50,
                            "value": `<div style=\"font-size:16px; color:#FFFFFF; font-weight: 400; text-align: center; font-family: Inter; letter-spacing: -0.32px;\">Thanks to your cultpass, you could have worked out in 27 cities across India!</div>`
                        }
                    },
                ]
            })
        }
    }

    if (userYearEndData.centers_visited != null) {
        pages.push(
                {
                    "id": "center_visited",
                    "backgroundColorStart": "#1D439B",
                    "backgroundColorEnd": "#E49951",
                    "backgroundImageURL": "image/achievement-report/center_visited_new.png",
                    "swipeLottieURL": "image/year-end-report/final/swipe.json",
                    "shareButtonIcon": "share-2",
                    "shareButtonTitle": "SHARE",
                    "disableBackgroundAnimation": true,
                    "widgets": [
                        {
                            "type": "TEXT",
                            "data":
                                {
                                    "topPadding": 30,
                                    "bottomPadding": 5,
                                    "horizontalPadding": 40,
                                    "textStyle": "H9",
                                    "value": `Centres visited:\n ${userYearEndData.centers_visited}`,
                                    "color": "#FFFFFF",
                                    "centerAlign": true
                                }
                        },
                        {
                            "type": "HTML",
                            "data":
                                {
                                    "topPadding": 5,
                                    "bottomPadding": 10,
                                    "horizontalPadding": 50,
                                    "value": `<div style=\"font-size:16px; color:#FFFFFF; font-weight: 400; text-align: center; font-family: Inter; letter-spacing: -0.32px;\">You invented cult-hopping and made it cool.</div>`
                                }
                        },
                    ]
                }
        )
    }
        if (userYearEndData.squad_friends_count != null) {
            pages.push(
                {
                    "id": "squads",
                    "backgroundColorStart": "#1A509B",
                    "backgroundColorEnd": "#204E7E",
                    "backgroundImageURL": "image/achievement-report/squad_comp.png",
                    "swipeLottieURL": "image/year-end-report/final/swipe.json",
                    "shareButtonIcon": "share-2",
                    "shareButtonTitle": "Share Your Friends",
                    "disableBackgroundAnimation": true,
                    "widgets": [
                        {
                            "type": "TEXT",
                            "data":
                                {
                                    "topPadding": 30,
                                    "bottomPadding": 5,
                                    "horizontalPadding": 40,
                                    "textStyle": "H9",
                                    "value": `You have \n ${userYearEndData.squad_friends_count} BFFs Best fitness friends who always say "Workouts before hangouts."`,
                                    "color": "#FFFFFF",
                                    "centerAlign": true
                                }
                        },
                    ]
                }
            )
        }

        if (userYearEndData.trainer_name != null) {
            pages.push(
                {
                    "id": "trainer",
                    "backgroundColorStart": "#075099",
                    "backgroundColorEnd": "#A26FB3",
                    "backgroundImageURL": "image/year-end-report/2024/trainer_without_comp.png",
                    "swipeLottieURL": "image/year-end-report/final/swipe.json",
                    "shareButtonIcon": "share-2",
                    "shareButtonTitle": "SHARE",
                    "disableBackgroundAnimation": true,
                    "widgets": [
                        {
                            "type": "TEXT",
                            "data":
                                {
                                    "topPadding": 30,
                                    "bottomPadding": 5,
                                    "horizontalPadding": 40,
                                    "textStyle": "H9",
                                    "value": `Your ultimate trainer: \n${userYearEndData.trainer_name}`,
                                    "color": "#FFFFFF",
                                    "centerAlign": true
                                }
                        },
                        {
                            "type": "HTML",
                            "data":
                                {
                                    "topPadding": 5,
                                    "bottomPadding": 10,
                                    "horizontalPadding": 50,
                                    "value": `<div style=\"font-size:16px; color:#FFFFFF; font-weight: 400; text-align: center; font-family: Inter; letter-spacing: -0.32px;\">You trained like a beast with the best (or vice versa)</div>`
                                }
                        },
                    ]
                }
            )
        }

        if (userYearEndData.calories != null) {
            const biryaniCal = 500
            const biryaniCount = Math.floor(userYearEndData.calories / biryaniCal)

            pages.push({
                "id": "calories_burnt",
                "backgroundColorStart": "#042B4D",
                "backgroundColorEnd": "#D9B7CF",
                "backgroundImageURL": "image/achievement-report/calorie_burnt_new.png",
                "swipeLottieURL": "image/year-end-report/final/swipe.json",
                "shareButtonIcon": "share-2",
                "shareButtonTitle": "SHARE",
                "disableBackgroundAnimation": true,
                "widgets": [
                    {
                        "type": "TEXT",
                        "data": {
                            "topPadding": 30,
                            "bottomPadding": 0,
                            "textStyle": "H9",
                            "value": `Calories burned: \n ${userYearEndData.calories.toLocaleString("en-IN")}`,
                            "color": "#FFFFFF",
                            "centerAlign": true
                        }
                    },
                    {
                        "type": "HTML",
                        "data": {
                            "topPadding": 10,
                            "bottomPadding": 10,
                            "horizontalPadding": 50,
                            "value": `<div style=\"font-size:16px; color:#FFFFFF; font-weight: 400; text-align: center; font-family: Inter; letter-spacing: -0.32px;\">
                                Hyderabadi, Lucknowi, Ambur, and Dindigul. <br/> You've burnt ${biryaniCount} biryanis.
                            </div>`
                        }
                    }
                ]
            })
        }
    if (userYearEndData.pack_utilisation_count != null) {
        const roiPercentage = Math.round(userYearEndData.pack_utilisation_count * 100)
        let backgroundImageURL = "image/achievement-report/ROI_chart.png"
        let valueText = `Congratulations, \nYou’ve got ${roiPercentage}% ROI on your membership!`
        let subText = `Return on Insane Determination that's higher than what most cult users got.`

        if (roiPercentage > 100 && roiPercentage <= 120) {
            backgroundImageURL = "image/achievement-report/ROI_chart_one.png"
            valueText = `Woaaaah!\nYou got a whooping ${roiPercentage}% ROI on cult.`
            subText = `Return on Insane Determination that's way better than what most cult users got.`
        } else if (roiPercentage > 120) {
            backgroundImageURL = "image/achievement-report/ROI_chart_two.png"
            valueText = `It's unbelievable!\nYou got ${roiPercentage}% ROI.`
            subText = `Return on Insane Determination that's more than double of what most cult users got.`
        }

        pages.push({
            "id": "roi_membership",
            "backgroundColorStart": "#042B4D",
            "backgroundColorEnd": "#D9B7CF",
            "backgroundImageURL": backgroundImageURL,
            "swipeLottieURL": "image/year-end-report/final/swipe.json",
            "shareButtonIcon": "share-2",
            "shareButtonTitle": "SHARE",
            "disableBackgroundAnimation": true,
            "widgets": [
                {
                    "type": "TEXT",
                    "data": {
                        "topPadding": 30,
                        "bottomPadding": 0,
                        "textStyle": "H9",
                        "value": valueText,
                        "color": "#FFFFFF",
                        "centerAlign": true
                    }
                },
                {
                    "type": "HTML",
                    "data": {
                        "topPadding": 10,
                        "bottomPadding": 10,
                        "horizontalPadding": 50,
                        "value": `<div style="font-size:16px; color:#FFFFFF; font-weight: 400; text-align: center; font-family: Inter; letter-spacing: -0.32px;">
                            ${subText}
                        </div>`
                    }
                }
            ]
        })
    }
        pages.push({
            "id": "achievAchievement_summary_last",
            "backgroundColorStart": "#2E80C5",
            "backgroundColorEnd": "#712C72",
            "backgroundImageURL": "image/achievement-report/closing_screen.png",
            "shareButtonTitle": "Play again",
            "shareButtonAction": {
                "actionType": "RESET_NAVIGATION",
                "url": "curefit://yearendreport?achievement=true"
            },
            "disableBackgroundAnimation": true,
            "widgets":
                [
                    {
                        "type": "TEXT",
                        "data":
                            {
                                "topPadding": 30,
                                "bottomPadding": 0,
                                "textStyle": "H9",
                                "value": "The next cult chronicle is waiting to be written.",
                                "color": "#FFFFFF",
                                "centerAlign": true
                            }
                    },
                    {
                        "type": "HTML",
                        "data": {
                            "topPadding": 10,
                            "bottomPadding": 10,
                            "horizontalPadding": 50,
                            "value": `<div style=\"font-size:16px; color:#FFFFFF; font-weight: 400; text-align: center; font-family: Inter; letter-spacing: -0.32px;\">
                                With exciting new formats Many more centres Great facilities & you.
                            </div>`
                        }
                    }
                ],
        })

        const dead_base_with_play = {
            "id": "dead_base",
            "backgroundColorStart": "#FFDB69",
            "backgroundColorEnd": "#000000",
            "backgroundImageURL": "image/year-end-report/DeadBaseWithPlayV3.png",
        }

        const dead_base_without_play = {
            "id": "dead_base",
            "backgroundColorStart": "#FFDB69",
            "backgroundColorEnd": "#000000",
            "backgroundImageURL": "image/year-end-report/DeadBaseWithoutPlayV3.png",
        }


        if (isDeadBaseAlumniPassPlayUser) {
            pages.push(dead_base_with_play)
        }
        else if (isDeadBaseAlumniPassNonPlayUser) {
            pages.push(dead_base_without_play)
        }

        let welcomeScreenCta = null

        if (userContext.sessionInfo.appVersion < 10.79) {
            welcomeScreenCta = "UPDATE YOUR APP"
        } else {
            welcomeScreenCta = "TAKE ME THERE"
        }

        return {
            "welcomeScreen": {
                "id": "intro",
                "backgroundLottieURL": "image/achievement-report/achievment_opt.json",
                "backgroundColorStart": "#000000",
                "backgroundColorEnd": "#000000",
                "animationDuration": 4,
                "bottomTextWidget": {
                    "type": "TEXT",
                    "data":
                        {
                            "topPadding": 10,
                            "bottomPadding": 10,
                            "textStyle": "P13",
                            "value": welcomeScreenCta,
                            "color": "#F44336",
                            "centerAlign": true
                        }
                },
                "widgets": [
                    {
                       "type": "MEDIA",
                       "data": {
                          "type": "image",
                          "url": "image/year-end-report/v_man_color.png",
                          "height": 45,
                          "width": 40,
                          "topPadding": 0,
                          "bottomPadding": 0
                       }
                    },
                    {
                       "type": "TEXT",
                       "data": {
                          "topPadding": 5,
                          "bottomPadding": 0,
                          "textStyle": "P2",
                          "value": "cult.fit presents",
                          "color": "#f6f6f6",
                          "centerAlign": true
                       }
                    },
                    {
                       "type": "TEXT",
                       "data": {
                          "topPadding": 0.34,
                          "bottomPadding": 20,
                          "textStyle": "H15",
                          "value": "YOUR BOOK OF\nACHIEVEMENTS\n& FUN FACTS",
                          "color": "#FFFFFF",
                          "centerAlign": true
                       }
                    },
                    {
                       "type": "TEXT",
                       "data": {
                          "topPadding": 0,
                          "bottomPadding": 0,
                          "textStyle": "H1",
                          "value": `${firstName},`,
                          "color": "#FFFFFF",
                          "centerAlign": true
                       }
                    },
                    {
                       "type": "TEXT",
                       "data": {
                          "topPadding": 0,
                          "bottomPadding": 30,
                          "horizontalPadding": 50,
                          "textStyle": "P2",
                          "value": "What you achieved, and more...\nFacts so good, you'll feel they're made up",
                          "color": "#f9f2f2",
                          "centerAlign": true
                       }
                    }
                 ]
            },
            "pledgeScreen": null,
            "pages": pages,
        }
    }
}