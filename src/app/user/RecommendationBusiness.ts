import {
    ConsultationProduct,
} from "@curefit/care-common"
import {
    DeliveryChannel,
} from "@curefit/eat-common"
import {
    DiagnosticProduct,
} from "@curefit/care-common"
import {
    FoodPack,
} from "@curefit/eat-common"
import {
    MealSlot,
} from "@curefit/eat-common"
import {
    ProductType,
} from "@curefit/product-common"
import {
    UserAgentType as UserAgent
} from "@curefit/base-common"
import IRecommendationBusiness from "./IRecommendationBusiness"
import IUserBusiness from "./IUserBusiness"
import { PreferredLocation } from "@curefit/userinfo-common"
import IProductBusiness from "../product/IProductBusiness"
import { inject, injectable, interfaces } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { ICultServiceOld as ICultService, CULT_CLIENT_TYPES } from "@curefit/cult-client"
import { ActivePackViewV1 } from "./ActivePackViewBuilderV1"
import RecommendationViewBuilder, { RecommendationView } from "./RecommendationViewBuilder"
import * as _ from "lodash"
import Util from "../../util/Util"
import { SlotUtil } from "@curefit/eat-util"
import { IActivityStoreReadWriteDao, LOGGING_MODELS_TYPES } from "@curefit/logging-models"
import { BaseOfferRequestParams, CultProductPricesResponse, EatOfferRequestParamsV3, FoodSinglePriceOfferResponse, PackOffersResponse } from "@curefit/offer-common"
import { IOfferServiceV2, OfferServiceV3 } from "@curefit/offer-service-client"
import EatUtil from "../util/EatUtil"
import { FoodBooking } from "@curefit/shipment-common"
import { IShipmentService, ALFRED_CLIENT_TYPES } from "@curefit/alfred-client"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { CareWidgetUtil, ICerberusService } from "@curefit/vm-models"
import { eternalPromise } from "@curefit/util-common"
import { IDIYFulfilmentService, DIY_CLIENT_TYPES } from "@curefit/diy-client"
import { DIYPack, DIYSeries } from "@curefit/diy-common"
import { ICatalogueService, CATALOG_CLIENT_TYPES, ICatalogueServicePMS } from "@curefit/catalog-client"
import { IMenuService, CAESAR_CLIENT_TYPES } from "@curefit/caesar-client"
import { UserContext } from "@curefit/userinfo-common"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { OFFER_SERVICE_CLIENT_TYPES } from "@curefit/offer-service-client"
import AppUtil from "../util/AppUtil"
import { Tenant } from "@curefit/user-common"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import DIYPackService from "../digital/diy/DIYPackService"
import CatalogueServiceUtilities from "../util/CatalogueServiceUtilities"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"
import { IOfflineFitnessPackService, PACK_CLIENT_TYPES } from "@curefit/pack-management-service-client"

const EligibleProductTypes: ProductType[] = ["FOOD", "FITNESS", "MIND", "DIY_MEDITATION", "DIY_FITNESS"]
const MealSlots: MealSlot[] = ["BREAKFAST", "LUNCH", "SNACKS", "DINNER"]

@injectable()
class RecommendationBusiness implements IRecommendationBusiness {

    constructor(
        @inject(LOCATION_TYPES.CityService) private cityService: ICityService,
        @inject(CUREFIT_API_TYPES.RecommendationViewBuilder) private recommendationViewBuilder: RecommendationViewBuilder,
        @inject(CUREFIT_API_TYPES.UserBusiness) private userBusiness: IUserBusiness,
        @inject(CUREFIT_API_TYPES.ProductBusiness) protected productBusiness: IProductBusiness,
        @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
        @inject(CULT_CLIENT_TYPES.MindFitService) private mindFitService: ICultService,
        @inject(CAESAR_CLIENT_TYPES.MenuService) private menuService: IMenuService,
        @inject(CUREFIT_API_TYPES.DIYPackService) private packService: DIYPackService,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) protected offerServiceV2: IOfferServiceV2,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) protected offerServiceV3: OfferServiceV3,
        @inject(ALFRED_CLIENT_TYPES.ShipmentService) private shipmentService: IShipmentService,
        @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) protected DIYFulfilmentService: IDIYFulfilmentService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) protected catalogueService: ICatalogueService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReaderPMS) protected catalogueServicePMS: ICatalogueServicePMS,
        @inject(PACK_CLIENT_TYPES.OfflineFitnessPackService) protected offlineFitnessPackService: IOfflineFitnessPackService,
        @inject(CUREFIT_API_TYPES.CerberusServiceV2) protected cerberusService: ICerberusService,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces) {
    }

    async getRecommendations(userContext: UserContext, forceCareReco?: boolean): Promise<RecommendationView> {
        if (forceCareReco === true) {
            return this.getPackRecommendationView("BUNDLE", userContext)
            // return this.getPackRecommendationView("CONSULTATION", userContext)
        } else {
            const activeCategories: ProductType[] = []
            const activeMealSlotsMap: Map<MealSlot, boolean> = new Map<MealSlot, boolean>()
            const activeDIYPacksMap: Map<string, boolean> = new Map<string, boolean>()
            const activePacksPromise = this.userBusiness.getActivePacksV1(userContext)
            const preferredLocationPromise = this.userBusiness.getPreferredLocation(userContext, userContext.userProfile.userId, userContext.sessionInfo.sessionData, undefined, undefined, undefined)
            const activePacks: ActivePackViewV1[] = await activePacksPromise
            if (!_.isEmpty(activePacks)) {
                activePacks.forEach(activePack => {
                    const type = activePack.productType
                    if (type === "FOOD") {
                        activeMealSlotsMap.set(activePack.mealSlot, true)
                    }
                    else if (["DIY_FITNESS", "DIY_MEDITATION"].indexOf(type) >= 0) {
                        activeDIYPacksMap.set(activePack.packId, true)
                    }
                    else {
                        activeCategories.push(type)
                    }
                })
            }
            // eat pack offer is active only if all of the meal slots are active
            if (activeMealSlotsMap.size === MealSlots.length) {
                activeCategories.push("FOOD")
            }

            // if user has already subscribed to food pack, then only show DIY recommendation(conversion for meal purchase is higher)
            if (_.isEmpty(activeMealSlotsMap)) {
                activeCategories.push("DIY_FITNESS")
                activeCategories.push("DIY_MEDITATION")
            }

            const preferredLocation = await preferredLocationPromise
            const isEatFitEligibleForLocation = EatUtil.isEatFitAvailable(userContext.sessionInfo.sessionData.cityId) && this.checkEatFitEligibilityForLocation(preferredLocation)
            // set food as active category to remove that from eligible categories
            if (!isEatFitEligibleForLocation) {
                activeCategories.push("FOOD")
            }

            // Temp change to show food recommendation always
            let eligibleCategories: ProductType[]
            if (!_.includes(activeCategories, "FOOD")) {
                eligibleCategories = ["FOOD"]
            }
            else {
                eligibleCategories = EligibleProductTypes.filter(productType => { return !_.includes(activeCategories, productType) })
            }
            return this.getRecommendationView(userContext, preferredLocation, activeDIYPacksMap, activeMealSlotsMap, eligibleCategories)
        }
    }

    private async getRecommendationView(
        userContext: UserContext,
        preferredLocation: PreferredLocation,
        activeDIYPacksMap: Map<string, boolean>,
        activeMealSlotsMap: Map<MealSlot, boolean>,
        eligibleCategories: ProductType[]): Promise<RecommendationView> {
        // if no eligible category present, return diy packs
        if (_.isEmpty(eligibleCategories)) {
            // TODO - health tip widget
            const recommendationView = this.getDIYPackRecommendationView(userContext.userProfile.userId, userContext.sessionInfo.userAgent, activeDIYPacksMap)
            return recommendationView
        }
        else {
            // get eligible category recommendation view
            const randomIndex = Util.getRandomInt(0, eligibleCategories.length)
            const productType = eligibleCategories[randomIndex]
            if (productType === "FOOD") {
                const foodRecoView = await this.getFoodRecommendationView(userContext, preferredLocation, activeMealSlotsMap)
                if (foodRecoView) {
                    return foodRecoView
                }
                // if food recommendations are empty, show other random recommendation
                else {
                    eligibleCategories.splice(eligibleCategories.indexOf(productType), 1)
                    // Again call same function with updated eligible categories
                    return this.getRecommendationView(userContext, preferredLocation, activeDIYPacksMap, activeMealSlotsMap, eligibleCategories)
                }
            }
            else {
                return this.getPackRecommendationView(productType, userContext, activeMealSlotsMap, preferredLocation, activeDIYPacksMap)
            }
        }
    }

    private async getFoodRecommendationView(
        userContext: UserContext,
        preferredLocation: PreferredLocation,
        activeMealSlotsMap: Map<MealSlot, boolean>
    ): Promise<RecommendationView> {
        const tz = userContext.userProfile.timezone
        const todaysDate = TimeUtil.todaysDateWithTimezone(tz)
        const futureFoodBookingsPromise = this.shipmentService.getFutureFoodBookings(tz, userContext.userProfile.userId, todaysDate, 7)
        // calculate meal slot for pack before modifying the map
        const foodPackMealSlot = this.getEligibleMealSlot(activeMealSlotsMap, preferredLocation.deliveryChannel, tz)
        const futureFoodBookings = await futureFoodBookingsPromise
        const filteredBookings: FoodBooking[] = futureFoodBookings.filter(booking => {
            return booking.state !== "CANCELLED" && booking.state !== "REJECTED"
        })
        filteredBookings.forEach(booking => {
            const mealSlot = SlotUtil.getMealSlotForSlotId(booking.deliverySlot.slotId, tz)
            activeMealSlotsMap.set(mealSlot, true)
        })
        // const foodActivitiesInLast7Days = await foodActivityPromise
        // foodActivitiesInLast7Days.forEach(foodActivity => {
        //     const clientActivityId = foodActivity.clientActivityId
        //     //check if the activity is of which meal slot and remove that meal slot from eligible meal slots
        //     MealSlots.forEach(mealSlot => {
        //         //check meal slot in food activity with clientActivityId
        //         //TODO add meal slot in activity DS
        //         if (_.includes(clientActivityId, mealSlot)) {
        //             activeMealSlotsMap.set(mealSlot, true)
        //         }
        //     })
        // })
        const foodSingleMealSlot = this.getEligibleMealSlot(activeMealSlotsMap, preferredLocation.deliveryChannel, tz)
        if (foodSingleMealSlot) {
            const areaId = preferredLocation.area ? preferredLocation.area.areaId : preferredLocation.defaultArea.areaId
            const mealAvailabilityPromise = this.productBusiness.getNextAvailableMenu(userContext, foodSingleMealSlot, preferredLocation, true)
            const eatOfferRequestParams: EatOfferRequestParamsV3 = {
                areaId: areaId,
                userId: userContext.userProfile.userId,
                cityId: userContext.userProfile.cityId,
                deviceId: userContext.sessionInfo.deviceId,
                dateMealSlotMap: await this.menuService.getDateMealSlotsMap(areaId),
                source: AppUtil.callSourceFromContext(userContext)
            }
            const mealMenuAvailablity = await mealAvailabilityPromise
            if (mealMenuAvailablity.menus && !_.isEmpty(mealMenuAvailablity.menus)) {
                const recommendedMealsPromise = eternalPromise(this.cerberusService.getRecommendedMeals(userContext.userProfile.userId, mealMenuAvailablity.day))
                const recommendedMealsResult = await recommendedMealsPromise
                const recommendedMealMap = EatUtil.getRecommendedMealMap(recommendedMealsResult)
                return this.recommendationViewBuilder.buildFoodSingleView(userContext, foodSingleMealSlot, mealMenuAvailablity, recommendedMealMap, {} as FoodSinglePriceOfferResponse)
            }
        }
        return this.getFoodPackRecommendationView(userContext, preferredLocation, foodPackMealSlot)
    }

    private checkEatFitEligibilityForLocation(preferredLocation: PreferredLocation): boolean {
        if (preferredLocation.isInServicableArea) {
            return true
        }
        return false
    }

    private async getFoodPackRecommendationView(
        userContext: UserContext,
        preferredLocation: PreferredLocation,
        mealSlot: MealSlot
    ): Promise<RecommendationView> {
        const areaId = (preferredLocation && preferredLocation.area) ? preferredLocation.area.areaId : "1"
        let packs: FoodPack[] = []
        packs = packs.filter(foodPack => { return foodPack.mealSlot === mealSlot })
        // if no food packs available return DIY packs recommendation
        if (!_.isEmpty(packs)) {
            return this.recommendationViewBuilder.buildFoodPackView(userContext.sessionInfo.userAgent, packs, preferredLocation, mealSlot)
        }
    }

    private async getDIYPackRecommendationView(userId: string, userAgent: UserAgent, activeDIYPacksMap: Map<string, boolean>, productType?: ProductType, countryId?: string): Promise<RecommendationView> {
        if (!productType) {
            const randomIndex = Util.getRandomInt(0, 2) // 0 for mind, 1 for cult
            productType = randomIndex === 0 ? "DIY_MEDITATION" : "DIY_FITNESS"
        }
        let recommendedPacks: DIYPack[]
        let recommendedSeries: DIYSeries[]
        if (productType === "DIY_MEDITATION") {
            recommendedPacks = await this.packService.browseMindDIYPacksV2(userId, "VISIBLE")
            recommendedSeries = await this.DIYFulfilmentService.getDIYSeries(userId, productType, countryId)
        }
        else {
            recommendedPacks = await this.packService.browseFitnessDIYPacksV2(userId, "VISIBLE")
            recommendedSeries = await this.DIYFulfilmentService.getDIYSeries(userId, productType, countryId)
        }
        return this.recommendationViewBuilder.buildDIYPackView(userId, recommendedPacks, recommendedSeries, userAgent, productType, activeDIYPacksMap)
    }

    private getEligibleMealSlot(activeMealSlotsMap: Map<MealSlot, boolean>, deliveryChannel: DeliveryChannel, timezone: Timezone): MealSlot {
        const eligibleMealSlots = MealSlots.filter(mealSlot => { return !activeMealSlotsMap.has(mealSlot) })
        const selectedMealSlotIndex = EatUtil.getSelectedMealSlotIndex(eligibleMealSlots, deliveryChannel, timezone)
        return eligibleMealSlots[selectedMealSlotIndex]
    }

    private async getPackRecommendationView(
        productType: ProductType,
        userContext: UserContext,
        activeMealSlotsMap?: Map<MealSlot, boolean>,
        preferredLocation?: PreferredLocation,
        activeDIYPacksMap?: Map<string, boolean>): Promise<RecommendationView> {
        const countryId = AppUtil.getCountryId(userContext)
        const tenant: Tenant = AppUtil.getTenantFromUserContext(userContext)
        const city = userContext.sessionInfo.sessionData.cityId ? await this.cityService.getCityById(userContext.sessionInfo.sessionData.cityId) : await this.cityService.getDefaultCity(tenant)
        const userId = userContext.userProfile.userId
        const userAgent = userContext.sessionInfo.userAgent
        const deviceId = userContext.sessionInfo.deviceId
        const appVersion = userContext.sessionInfo.appVersion
        const source = AppUtil.callSourceFromContext(userContext)
        switch (productType) {
            case "FITNESS":
                const fitnessPacks: OfflineFitnessPack[] = await CatalogueServiceUtilities.getCultPMSPacks(this.offlineFitnessPackService, userId, city.cityId)
                if (_.isEmpty(fitnessPacks)) {
                    return this.getDIYPackRecommendationView(userId, userAgent, activeDIYPacksMap, countryId)
                }
                const cultPackV3Offers = await this.offerServiceV3.getCultPackPrices({
                    userInfo: {
                        userId,
                        deviceId
                    },
                    source,
                    cityId: userContext.userProfile.cityId,
                    cultCityId: city.cultCityId,
                    productIds: fitnessPacks.map(p => p.productId)
                })
                return this.recommendationViewBuilder.buildCultPackView(fitnessPacks, productType, userAgent, cultPackV3Offers)
            case "MIND":
                return this.recommendationViewBuilder.buildCultPackView([], productType, userAgent, {} as CultProductPricesResponse)
            case "DIY_FITNESS":
            case "DIY_MEDITATION":
                return this.getDIYPackRecommendationView(userId, userAgent, activeDIYPacksMap, productType, countryId)
            case "BUNDLE":
                const products: DiagnosticProduct[] = await this.catalogueService.getCareBundleProducts()
                if (_.isEmpty(products)) {
                    return this.getDIYPackRecommendationView(userId, userAgent, activeDIYPacksMap, countryId)
                }
                const hcuPacks: DiagnosticProduct[] = products.filter(product => product.userFacing === true && product.subCategoryCode === "HCU_PACK")
                if (_.isEmpty(hcuPacks)) {
                    return this.getDIYPackRecommendationView(userId, userAgent, activeDIYPacksMap, countryId)
                }
                const bundleoffers = await CareWidgetUtil.getCareProductOffersFromUserContext(
                    userContext,
                    "BUNDLE",
                    hcuPacks.map(pack => pack.productId),
                    AppUtil.callSourceFromContext(userContext),
                    this.serviceInterfaces,
                    true
                )
                return this.recommendationViewBuilder.buildCareBundlePackView(hcuPacks, productType, bundleoffers, appVersion)
            case "CONSULTATION":
                const consultations: ConsultationProduct[] = await this.catalogueService.getCareConsultationProducts()
                if (_.isEmpty(consultations)) {
                    return this.getDIYPackRecommendationView(userId, userAgent, activeDIYPacksMap, countryId)
                }

                const careBundleoffers = await CareWidgetUtil.getCareProductOffersFromUserContext(
                    userContext,
                    "CONSULTATION",
                    consultations.map(pack => pack.productId),
                    AppUtil.callSourceFromContext(userContext),
                    this.serviceInterfaces,
                    true
                )
                return this.recommendationViewBuilder.buildCareConsultationView(userContext, consultations, productType, careBundleoffers, appVersion)
        }
    }
}

export default RecommendationBusiness
