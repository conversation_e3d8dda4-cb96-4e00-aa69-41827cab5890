import { inject, injectable } from "inversify"
import { MongoReadonlyDao } from "@curefit/mongo-utils"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { UserYearEndReportModel } from "./UserYearEndReportModel"
import { UserYearEndReport } from "./UserYearEndReport"
import { IUserYearEndReportReadOnlyDao } from "./UserYearEndReportDao"
import { UserYearEndReportSchema } from "./UserYearEndReportSchema"

@injectable()
export class UserYearEndReportReadOnlyDaoMongoImpl extends MongoReadonlyDao<UserYearEndReportModel, UserYearEndReport> implements IUserYearEndReportReadOnlyDao {
    constructor(
        @inject(CUREFIT_API_TYPES.UserYearEndReportSchema) UserYearEndReportSchema: UserYearEndReportSchema,
        @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
        super(UserYearEndReportSchema.mongooseModel, logger, UserYearEndReportSchema.isLeanQueryEnabled)
    }
}
