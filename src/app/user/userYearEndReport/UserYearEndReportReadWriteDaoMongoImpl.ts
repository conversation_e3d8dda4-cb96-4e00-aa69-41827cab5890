import { inject, injectable } from "inversify"
import { MongoReadWriteDao } from "@curefit/mongo-utils"
import { BASE_TYPES, ILogger } from "@curefit/base"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { UserYearEndReportReadOnlyDaoMongoImpl } from "./UserYearEndReportReadOnlyDaoMongoImpl"
import { UserYearEndReportModel } from "./UserYearEndReportModel"
import { UserYearEndReport } from "./UserYearEndReport"
import { IUserYearEndReportReadWriteDao } from "./UserYearEndReportDao"
import { UserYearEndReportSchema } from "./UserYearEndReportSchema"

@injectable()
export class UserYearEndReportReadWriteDaoMongoImpl extends MongoReadWriteDao<UserYearEndReportModel, UserYearEndReport> implements IUserYearEndReportReadWriteDao {
    constructor(
        @inject(CUREFIT_API_TYPES.UserYearEndReportSchema) UserYearEndReportSchema: UserYearEndReportSchema,
        @inject(CUREFIT_API_TYPES.UserYearEndReportReadOnlyDaoMongoImpl) readonlyDao: UserYearEndReportReadOnlyDaoMongoImpl,
        @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
        super(UserYearEndReportSchema.mongooseModel, readonlyDao, logger)
    }
}
