import { MONGO_TYPES, MultiMongooseAccess, MultiMongooseSchema } from "@curefit/mongo-utils"
import { inject } from "inversify"
import { UserYearEndReportModel } from "./UserYearEndReportModel"
import { ReadPreference } from "mongodb"

export class UserYearEndReportSchema extends MultiMongooseSchema<UserYearEndReportModel> {

    constructor(
        @inject(MONGO_TYPES.MultiMongooseAccess) mongooseAccess: MultiMongooseAccess
    ) {
        super(mongooseAccess, "UserYearEndReport", "DEFAULT", ReadPreference.SECONDARY_PREFERRED)
    }

    protected schema() {
        return {
            userId: {type: String, unique: true},
            classes: {type: Number, required: false},
            favWorkout: {type: String, required: false},
            minsWorked: {type: Number, required: false},
            favWorkoutClasses: {type: Number, required: false},
            caloriesBurnt: {type: Number, required: false},
            buddies: {type: Number, required: false},
            favWorkoutMins: {type: Number, required: false},
            bestStreak: {type: Number, required: false},
            bestWeeklyStreak: {type: Number, required: false},
            bestRank: {type: Number, required: false},
            formatsTried: {type: Number, required: false},
            bestScore: {type: Number, required: false},
            percentile: {type: Number, required: false},
            workoutList: {type: String, required: false},
        }
    }

}
