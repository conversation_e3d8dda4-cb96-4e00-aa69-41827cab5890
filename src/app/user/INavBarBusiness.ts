

import { City, VerticalType, Verticals } from "@curefit/location-common"
import { UserContext } from "@curefit/userinfo-common"
import { HamletBusiness } from "@curefit/hamlet-node-sdk"
import { AppLayout, UserPreference } from "@curefit/apps-common"
import { DeviceInfo } from "@curefit/device-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import {
    Session
} from "@curefit/userinfo-common"
import { UserAllocation, UserAssignment } from "@curefit/hamlet-common"
export interface VerticalInfo {
    verticalType: VerticalType
    displayText: string
    pageId: string
    isEditable: boolean
    tag?: {
        tagText: string
    }
    isHighlighted: Boolean
}
export interface VerticalsWithHighlightInformation {
    verticals: VerticalInfo[]
    isMoreToBeHighlighted?: Boolean
    appLayout?: AppLayout
}

export interface NavBarInteraction {
    isMoreInteracted: Boolean,
    verticalInteracted: VerticalType
}


interface INavBarBusiness {
    isMoreVerticalsSupported(userContext: UserContext): boolean
    getUserAndSupportedVerticalsInfo(userContext: UserContext, city: City, userId: string, userExperimentsPromise?: Promise<UserAllocation>): Promise<VerticalsWithHighlightInformation>
    getSupportedVerticals(userContext: UserContext, city: City): Promise<VerticalType[]>
    getSupportedVerticalsForNavBar(userContext: UserContext, city: City): Promise<VerticalType[]>
    updateNavBarInteraction(userId: string, session: Session, barInteraction: NavBarInteraction, userContext: UserContext): Promise<UserPreference>
}

export default INavBarBusiness
