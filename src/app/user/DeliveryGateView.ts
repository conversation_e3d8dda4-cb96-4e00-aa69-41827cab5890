import { DeliveryGate } from "@curefit/eat-common"
import { Status } from "@curefit/base-common"

class DeliveryGateView {
    constructor(gate: DeliveryGate) {
        this.gateId = gate.gateId
        this.key = gate.gateId
        this.geteId = gate.gateId
        this.name = gate.name
        this.address = gate.locality
        this.addressString =  gate.name + ", " + gate.addressString + ", " + gate.locality + ", " + gate.city
        this.status = gate.status ? gate.status : "LIVE"
    }
    public key: string // Used in client side for recylcing view
    public gateId: string
    public geteId: string
    public name: string
    public address: string
    public status: Status
    public addressString: string
}

export default DeliveryGateView
