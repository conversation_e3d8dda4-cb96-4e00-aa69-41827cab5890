
import { inject, injectable } from "inversify"
import * as _ from "lodash"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { City, VerticalType, Verticals } from "@curefit/location-common"
import IUserPreferenceBussiness from "./IUserPreferenceBussiness"
import { UserContext } from "@curefit/userinfo-common"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { UserPreference } from "@curefit/apps-common"
import { TimeUtil } from "@curefit/util-common"
import AppUtil from "../util/AppUtil"
import INavBarBusiness, { VerticalInfo, VerticalsWithHighlightInformation, NavBarInteraction } from "./INavBarBusiness"
import { PageTypes } from "@curefit/apps-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
const THRESHOLD_FOR_NEW_VERTICAL_TREATMENT_IN_DAYS = 30
import {
    Session
} from "@curefit/userinfo-common"
import { Payload, UserAllocation, UserAssignment } from "@curefit/hamlet-common"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { ISegmentService } from "@curefit/vm-models"


export const MORE_VERTICALS_LAUNCH_EXPERIMENT_STAGE = "108"
export const MORE_VERTICALS_LAUNCH_EXPERIMENT_PROD = "64"
const MORE_VERTICALS_VERSION = 8.0
const LIVE_FIT_VERSION = 8.15
const INTERNATION_LIVE_FIT_TAB_VERSION = 1.0


const VERTICAL_TO_DISPLAY_MAP: { [key in VerticalType]?: string } = {
    MIND: "MIND",
    CULT: "CULT",
    GYM: "GYM",
    GEAR: "GEAR",
    EAT: "EAT",
    CARE: "CARE",
    WHOLE: "WHOLE",
    LIVE: "LIVE",
    COACH: "COACH",
    STORE: "STORE",
    WELLNESS: "WELLNESS",
    FITNESSHUB: "FITNESSHUB"
}

const APP_TABS_SUPPORTED_BY_APP = 4

interface HighLightInformation {
    verticalToBeHighlighted: Map<VerticalType, boolean>,
    isUpdateRequired: boolean,
    isMoreToBeHighlighted: boolean,
    userPreference: UserPreference,
    supportedVerticals: Verticals[]
}
interface UserInteractedVeticals {
    verticalType: String
    isVerticalInteracted: Boolean
    isMoreInteracted: Boolean
}
@injectable()
class NavBarBusiness implements INavBarBusiness {
    constructor(
        @inject(CUREFIT_API_TYPES.UserPreferenceBusiness) private userPreferenceBussiness: IUserPreferenceBussiness,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
        @inject(ERROR_COMMON_TYPES.RollbarService) public rollbarService: RollbarService
    ) {
    }
    async updateNavBarInteraction(userId: string, session: Session, barInteraction: NavBarInteraction, userContext: UserContext): Promise<UserPreference> {
        const userPreference: UserPreference = await this.userPreferenceBussiness.getUserPreference(userContext)
        if (barInteraction.isMoreInteracted) {
            userPreference.cfAPI.isMoreInteracted = barInteraction.isMoreInteracted
            _.each(userPreference.cfAPI.userInteractedVerticals, (vertical: UserInteractedVeticals) => {
                vertical.isMoreInteracted = true
            })
        }
        if (barInteraction.verticalInteracted) {
            _.each(userPreference.cfAPI.userInteractedVerticals, (vertical: UserInteractedVeticals) => {
                if (vertical.verticalType === barInteraction.verticalInteracted) {
                    vertical.isVerticalInteracted = true
                    vertical.isMoreInteracted = true
                }
            })
        }
        await this.userPreferenceBussiness.createOrUpdateUserPreference(userContext, userPreference)
        return userPreference
    }

    async getSupportedVerticals(userContext: UserContext, city: City): Promise<VerticalType[]> {
        const supportedVerticalsWithDate: Verticals[] = await this.filterSupportedVerticals(userContext, city)
        const supportedVerticals: VerticalType[] = []
        _.each(supportedVerticalsWithDate, (vertical: Verticals) => {
            supportedVerticals.push(vertical.verticalType)
        })
        return supportedVerticals
    }

    private async filterSupportedVerticals(userContext: UserContext, city: City): Promise<Verticals[]> {
        let supportedVerticals = userContext.sessionInfo.userAgent != "APP" && city.webSupportedVerticals && city.webSupportedVerticals.length ? city.webSupportedVerticals : city.appSupportedVerticals
        if (!AppUtil.isWholeFitV2ReviewSupported(userContext)) {
            supportedVerticals = supportedVerticals.filter((vertical: Verticals) => vertical.verticalType !== "WHOLE")
        }
        const isInternalUser = userContext.userPromise ? (await userContext.userPromise).isInternalUser : false
        if (city.cityId === "Dubai" && !AppUtil.isMarketplaceSupported(userContext, isInternalUser)) {
            supportedVerticals = supportedVerticals.filter((vertical: Verticals) => vertical.verticalType !== "EAT")
        }
        const isGymFitEnabled: boolean = false
        if (!isGymFitEnabled) {
            supportedVerticals = supportedVerticals.filter((vertical: Verticals) => vertical.verticalType !== "GYM")
        }
        if (!this.isLiveFitTabSupported(userContext)) {
            supportedVerticals = supportedVerticals.filter((vertical: Verticals) => vertical.verticalType !== "LIVE")
        }
        if (!AppUtil.isCoachFitVerticalSupported(userContext)) {
            supportedVerticals = supportedVerticals.filter((vertical: Verticals) => vertical.verticalType !== "COACH")
        }

        const wellnessTabPresent = _.some(supportedVerticals, v => v.verticalType === "WELLNESS")
        if (wellnessTabPresent && AppUtil.isWellnessTabSupported(userContext)) {
                // Enabling store tab for all users
                supportedVerticals = this.filterWellnessComplementaryTabsExcludeStore(supportedVerticals)
                // supportedVerticals = this.filterWellnessComplementaryTabs(supportedVerticals)
        } else {
            supportedVerticals = supportedVerticals.filter(v => v.verticalType !== "WELLNESS")
        }

        const storeTabPresent = _.some(supportedVerticals, v => v.verticalType === "STORE")
        if (storeTabPresent) {
            if (!AppUtil.isStoreTabSupported(userContext)) {
                // creating a unified vertical support for gear and store, STORE vertical to control both store and gear tab for a city
                supportedVerticals = supportedVerticals.filter((vertical: Verticals) => vertical.verticalType !== "STORE")
                if (AppUtil.getCountryId(userContext) === "IN") {
                    const gearTabPresent = _.some(supportedVerticals, vertical => vertical.verticalType === "GEAR")
                    if (!gearTabPresent) {
                        const priorDate = new Date().setDate(new Date().getDate() - 31)
                        supportedVerticals.push({
                            verticalType: "GEAR",
                            startAt: {
                                timezone: "Asia/Kolkata",
                                date: new Date(priorDate)
                            }
                        })
                    }
                }
            } else {
                // keeping either store tab or whole-gear combo
                supportedVerticals = supportedVerticals.filter((vertical: Verticals) => vertical.verticalType !== "WHOLE" && vertical.verticalType !== "GEAR")
            }
        }
        return supportedVerticals
    }

    private async getHighLightInformation(userId: string, userContext: UserContext, city: City): Promise<HighLightInformation> {
        let isUpdateRequired: boolean = false
        let isMoreToBeHighlighted: boolean = false
        const supportedVerticals: Verticals[] = await this.getSupportedVerticalsForNavBarWithStartDate(userContext, city)
        const verticalToBeHighlighted: Map<VerticalType, boolean> = new Map()
        let userPreference: UserPreference
        /* disable user preference for the international app */
        if (!AppUtil.isInternationalApp(userContext)) {
            userPreference = await this.userPreferenceBussiness.getUserPreference(userContext)
            isMoreToBeHighlighted = !userPreference?.cfAPI?.isMoreInteracted
            _.each(supportedVerticals, (supportedVertical: Verticals) => {
                const diffDays = TimeUtil.diffInDays(userContext.userProfile.timezone, supportedVertical.startAt.date.toString(), new Date().toDateString())
                let isVerticalToBeHighlighted = diffDays < THRESHOLD_FOR_NEW_VERTICAL_TREATMENT_IN_DAYS
                const userInteractedVertical = userPreference?.cfAPI?.userInteractedVerticals?.find(userInteractedVertical => userInteractedVertical.verticalType === supportedVertical.verticalType)
                if (userInteractedVertical) {
                    isVerticalToBeHighlighted = isVerticalToBeHighlighted && !userInteractedVertical.isVerticalInteracted
                } else {
                    if (isVerticalToBeHighlighted) {
                        isUpdateRequired = true
                        if (!userPreference.cfAPI.userInteractedVerticals) {
                            userPreference.cfAPI.userInteractedVerticals = []
                        }
                        userPreference.cfAPI.userInteractedVerticals.push({
                            verticalType: supportedVertical.verticalType,
                            isVerticalInteracted: false,
                            isMoreInteracted: false
                        })
                    }
                }
                isMoreToBeHighlighted = isMoreToBeHighlighted || !!(userInteractedVertical && !userInteractedVertical.isMoreInteracted)
                verticalToBeHighlighted.set(supportedVertical.verticalType, isVerticalToBeHighlighted)
            })
        }

        return {
            verticalToBeHighlighted: verticalToBeHighlighted,
            isUpdateRequired: isUpdateRequired,
            isMoreToBeHighlighted: isMoreToBeHighlighted,
            userPreference: userPreference,
            supportedVerticals: supportedVerticals
        }
    }

    public async getUserAndSupportedVerticalsInfo(userContext: UserContext, city: City, userId: string): Promise<VerticalsWithHighlightInformation> {
        const highLightInformation: HighLightInformation = await this.getHighLightInformation(userId, userContext, city)
        let supportedVerticals: Verticals[] = highLightInformation.supportedVerticals
        const userPreference = highLightInformation.userPreference
        const isUpdateRequired = highLightInformation.isUpdateRequired
        const verticalToBeHighlighted = highLightInformation.verticalToBeHighlighted
        const isMoreToBeHighlighted = highLightInformation.isMoreToBeHighlighted
        const verticalInfos: VerticalInfo[] = []
        const isInternalUser = userContext.userPromise ? (await userContext.userPromise).isInternalUser : false
        const isFitnessCLPSupported = AppUtil.isFitnessCLPSupported(userContext, isInternalUser)
        // const doesUserBelongToMemberSegment = await this.segmentService.doesUserBelongToSegment("5feb3a1-3b34-4987-b1b0-b0cbda617a46", userContext)
        // isFitnessCLPSupported = isFitnessCLPSupported && !_.isEmpty(doesUserBelongToMemberSegment)
        if (isFitnessCLPSupported) {
            supportedVerticals = supportedVerticals.filter((vertical: Verticals) => vertical.verticalType !== "CULT" && vertical.verticalType !== "LIVE")
            const vertical = "FITNESSHUB"
            verticalInfos.push({
                verticalType: vertical,
                displayText: VERTICAL_TO_DISPLAY_MAP[vertical],
                pageId: this.getVerticalPageId(vertical),
                isEditable: true,
                isHighlighted: verticalToBeHighlighted.get(vertical)
            })
        }
        let remainingVerticalsForCity = []
        const isWeb = AppUtil.isWeb(userContext)
        if (!_.isEmpty(userPreference?.cfAPI?.verticals) && !isWeb) {
            _.each(userPreference.cfAPI.verticals, vertical => {
                const isVerticalSupported = _.some(supportedVerticals, v => v.verticalType === vertical)
                if (isVerticalSupported) {
                    verticalInfos.push({
                        verticalType: vertical,
                        displayText: VERTICAL_TO_DISPLAY_MAP[vertical],
                        pageId: this.getVerticalPageId(vertical),
                        isEditable: true,
                        isHighlighted: verticalToBeHighlighted.get(vertical)
                    })
                }
            })
            remainingVerticalsForCity = _.filter(supportedVerticals, (vertical: Verticals) => {
                const index = _.findIndex(userPreference.cfAPI.verticals, (VerticalType: VerticalType) => {
                    return vertical && vertical.verticalType === VerticalType
                })
                return index === -1
            })
        } else {
            remainingVerticalsForCity = [...supportedVerticals]
        }
        _.each(remainingVerticalsForCity, (vertical: Verticals) => {
            verticalInfos.push({
                verticalType: vertical.verticalType,
                displayText: VERTICAL_TO_DISPLAY_MAP[vertical.verticalType],
                pageId: this.getVerticalPageId(vertical.verticalType),
                isEditable: true,
                isHighlighted: verticalToBeHighlighted.get(vertical.verticalType)
            })
        })
        if (isUpdateRequired)
            await this.userPreferenceBussiness.createOrUpdateUserPreference(userContext, userPreference)

        return { isMoreToBeHighlighted, verticals: verticalInfos }
    }

    public async getSupportedVerticalsForNavBar(userContext: UserContext, city: City): Promise<VerticalType[]> {
        const supportedVerticalsWithDate: Verticals[] = await this.getSupportedVerticalsForNavBarWithStartDate(userContext, city)
        const supportedVerticals: VerticalType[] = []
        _.each(supportedVerticalsWithDate, (vertical: Verticals) => {
            supportedVerticals.push(vertical.verticalType)
        })
        return supportedVerticals
    }

    private async getSupportedVerticalsForNavBarWithStartDate(userContext: UserContext, city: City): Promise<Verticals[]> {
        let supportedVerticals = await this.filterSupportedVerticals(userContext, city)
        if (!this.isMoreVerticalsSupported(userContext) && userContext.sessionInfo.userAgent === "APP" && supportedVerticals.length > APP_TABS_SUPPORTED_BY_APP) {
            supportedVerticals = supportedVerticals.slice(0, APP_TABS_SUPPORTED_BY_APP)
        }
        return supportedVerticals
    }

    public isMoreVerticalsSupported(userContext: UserContext): boolean {
        const { appVersion, osName } = userContext.sessionInfo
        if ((osName.toLowerCase() === "android" || osName.toLowerCase() === "ios") && appVersion >= MORE_VERTICALS_VERSION) {
            return true
        }
        return false
    }

    private isLiveFitTabSupported(userContext: UserContext) {
        const { clientVersion } = userContext.sessionInfo

        if (AppUtil.isInternationalApp(userContext)) {
            /** for international app & web disable the live tab */
            return clientVersion <= INTERNATION_LIVE_FIT_TAB_VERSION || AppUtil.isWeb(userContext)
        }

        if (AppUtil.isWeb(userContext) || clientVersion >= LIVE_FIT_VERSION) {
            return true
        }

        return false
    }

    private filterWellnessComplementaryTabs(supportedVerticals: Verticals[]) {
        return supportedVerticals.filter( v =>
            v.verticalType !== "MIND" &&
            v.verticalType !== "CARE" &&
            v.verticalType !== "STORE" &&
            v.verticalType !== "EAT" &&
            v.verticalType !== "GEAR" &&
            v.verticalType !== "WHOLE"
        )
    }

    private filterWellnessComplementaryTabsExcludeStore(supportedVerticals: Verticals[]) {
        return supportedVerticals.filter( v =>
            v.verticalType !== "MIND" &&
            v.verticalType !== "CARE" &&
            v.verticalType !== "EAT" &&
            v.verticalType !== "GEAR" &&
            v.verticalType !== "WHOLE"
        )
    }

    private getVerticalPageId(verticalType: VerticalType): string {
        switch (verticalType) {
            case "MIND":
                return PageTypes.MindFitCLP
            case "GYM":
                return PageTypes.GymFitCLP
            case "GEAR":
                return PageTypes.CultGearCLP
            case "EAT":
                return PageTypes.EatFitCLPV2
            case "CULT":
                return PageTypes.CultFitCLP
            case "CARE":
                return PageTypes.CareFitCLP
            case "WHOLE":
                return PageTypes.WholeFitCLP
            case "LIVE":
                return PageTypes.LiveFitCLP
            case "COACH":
                return PageTypes.CoachFitCLP
            case "STORE":
                return PageTypes.StoreCLP
            case "WELLNESS":
                return PageTypes.WellnessCLP
            case "FITNESSHUB":
                return PageTypes.FitnessHubCLP
            default:
                return ""
        }
    }


}

export default NavBarBusiness
