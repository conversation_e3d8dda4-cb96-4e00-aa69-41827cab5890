import { Action } from "../common/views/WidgetView"
import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CatalogueServiceUtilities } from "../util/CatalogueServiceUtilities"
import { ICatalogueService, CATALOG_CLIENT_TYPES, ICatalogueServicePMS } from "@curefit/catalog-client"
import {
    ConsultationProduct,
} from "@curefit/care-common"
import {
    DiagnosticProduct,
} from "@curefit/care-common"
import {
    FoodPack,
} from "@curefit/eat-common"
import {
    MealSlot,
} from "@curefit/eat-common"
import {
    MindPack,
} from "@curefit/mind-common"
import {
    FoodProduct as Product,
} from "@curefit/eat-common"
import {
    ProductType,
} from "@curefit/product-common"
import {
    UserAgentType as UserAgent
} from "@curefit/base-common"
import { CultPack } from "@curefit/cult-common"
import * as _ from "lodash"
import { UrlPathBuilder } from "@curefit/product-common"
import { ActionUtil, ActionUtilV1, SeoUrlParams } from "@curefit/base-utils"
import { OfferUtil } from "@curefit/base-utils"
import BaseCultLandingPageConfig from "../page/BaseCultLandingPageConfig"
import { PageWidget, PageWidgetFooter, PageWidgetTitle } from "../page/Page"
import { ActionCard, FoodSinglesRecommendationWidget, MealItem, PackRecommendationWidget } from "../page/PageWidgets"
import { PreferredLocation } from "@curefit/userinfo-common"
import EatUtil, { DEFAULT_FOOD_CATEGORY_ID, DEFAULT_TICKET_SIZE } from "../util/EatUtil"
import { ALL_MEAL_SLOTS } from "@curefit/eat"
import RecommendationPageConfig from "./RecommendationPageConfig"
import { MenuAvailabilityResult } from "../product/IProductBusiness"
import EatLandingPageConfig from "../page/EatLandingPageConfig"
import { MAX_CART_SIZE, MealUtil } from "@curefit/base-utils"
import { RecommendedMeals } from "@curefit/vm-models"
import { CultProductPricesResponse, FoodSinglePriceOfferResponse, PackOffersResponse } from "@curefit/offer-common"
import { DIYPack, DIYSeries } from "@curefit/diy-common"
import Util from "../../util/Util"
import { IBaseWidget, UserContext } from "@curefit/vm-models"
import { FoodCategory } from "@curefit/eat-common"
import { EAT_TYPES, IFoodCategoryService } from "@curefit/eat"
import { TimeUtil } from "@curefit/util-common"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService } from "@curefit/diy-client"
import CareUtil from "../util/CareUtil"
import CultUtil from "../util/CultUtil"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"

export interface RecommendationView {
    widgets: (PageWidget | IBaseWidget)[]
    pageActions?: Action[]
}

const NO_OF_ACTION_CARDS = 3

@injectable()
class RecommendationViewBuilder {

    constructor(
        @inject(CUREFIT_API_TYPES.EatLandingPageConfig) protected eatLandingPageConfig: EatLandingPageConfig,
        @inject(CUREFIT_API_TYPES.RecommendationPageConfig) private recommendationPageConfig: RecommendationPageConfig,
        @inject(CUREFIT_API_TYPES.CultLandingPageConfigV2) private cultLandingPageConfig: BaseCultLandingPageConfig,
        @inject(CUREFIT_API_TYPES.MindLandingPageConfigV2) private mindLandingPageConfig: BaseCultLandingPageConfig,
        @inject(EAT_TYPES.FoodCategoryService) protected foodCategoryService: IFoodCategoryService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) protected catalogueService: ICatalogueService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReaderPMS) protected catalogueServicePMS: ICatalogueServicePMS,
        @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) protected diyService: IDIYFulfilmentService) {
    }

    async buildDIYPackView(
        userId: string,
        packs: DIYPack[],
        diySeries: DIYSeries[],
        userAgent: UserAgent,
        productType: ProductType,
        activeDIYPacksMap: Map<string, boolean>): Promise<RecommendationView> {
        const unSubscribedSeries = diySeries.filter(series => {
            const activePackInSeries = _.find(series.packIds, packId => { return activeDIYPacksMap.has(packId) })
            return activePackInSeries ? false : true
        })

        if (_.isEmpty(unSubscribedSeries)) {
            return undefined
        } else {
            const seriesToRecommend = unSubscribedSeries[Util.getRandomInt(0, unSubscribedSeries.length)]
            const diyPackActionCards: ActionCard[] = []
            // show only NO_OF_ACTION_CARDS packs of selected series
            for (let i = 0; i < seriesToRecommend.packIds.length; i++) {
                const packId = seriesToRecommend.packIds[i]
                const packs = productType === "DIY_FITNESS" ? await this.diyService.getDIYFitnessPacksForIds(userId, [packId]) : await this.diyService.getDIYMeditationPacksForIds(userId, [packId])
                const diyPack = packs[0]
                const seoParams: SeoUrlParams = {
                  productName: diyPack.title
                }
                diyPackActionCards.push({
                    title: diyPack.title,
                    image: UrlPathBuilder.prefixSlash((userAgent === "APP" ? diyPack.imageDetails.magazineImage : diyPack.imageDetails.magazineWebImage)),
                    action: ActionUtilV1.diyPackProductPage(diyPack, userAgent)
                })
                if (diyPackActionCards.length >= NO_OF_ACTION_CARDS) {
                    break
                }
            }
            let pageTitle = this.recommendationPageConfig.getDIYPacksTitle(seriesToRecommend.name)
            if (_.isEmpty(pageTitle)) {
                pageTitle = {
                    title: seriesToRecommend.name,
                    subTitle: "Try these guided sessions"
                }
            }
            const seeMoreAction = productType === "DIY_FITNESS" ? ActionUtil.cultCLPVMPage("CultAtHome") : ActionUtil.mindCLPVMPage("MindAtHome")
            const footer: PageWidgetFooter = {
                action: seeMoreAction,
                title: this.recommendationPageConfig.packSeeMoreText,
            }
            const widget: PackRecommendationWidget = new PackRecommendationWidget(
                pageTitle,
                diyPackActionCards,
                footer,
            )
            widget.analyticsData = {
                recommendationCategory: productType
            }
            return this.getRecommendationView(widget)
        }

    }
    async buildFoodPackView(
        userAgent: UserAgent,
        mealPacks: FoodPack[],
        preferredLocation: PreferredLocation,
        mealSlot: MealSlot): Promise<RecommendationView> {
        const widgetTitle: PageWidgetTitle = this.recommendationPageConfig.foodPackTitle
        const topPickActionCards: ActionCard[] = []
        for (let i = 0; i < mealPacks.length; i++) {
            const mealPack = mealPacks[i]
            const seoParams: SeoUrlParams = {
                productName: mealPack.title
            }
            if (mealPack && mealPack.status === "LIVE") {
                topPickActionCards.push({
                    title: mealPack.title,
                    image: UrlPathBuilder.getPackImagePath(mealPack.productId, "FOOD", "MAGAZINE", mealPack.imageVersion, userAgent),
                    action: ActionUtil.foodPack(mealPack.productId, undefined, DEFAULT_TICKET_SIZE, true, userAgent, seoParams)
                })
            }
            // To show only NO_OF_ACTION_CARDS pack action cards
            if (topPickActionCards.length >= NO_OF_ACTION_CARDS) {
                break
            }
        }
        const seeMoreAction = ActionUtil.packBrowsePage()
        const footer: PageWidgetFooter = {
            action: seeMoreAction,
            title: this.getFoodPackSeeMoreText(mealSlot),
        }
        const widget = new PackRecommendationWidget(widgetTitle, topPickActionCards, footer)
        widget.analyticsData = {
            recommendationCategory: "FOOD_PACK"
        }
        return this.getRecommendationView(widget)
    }

    async buildCareBundlePackView(bundles: DiagnosticProduct[], productType: ProductType, bundleoffers: PackOffersResponse, appVersion: number): Promise<RecommendationView> {
        const packActionCards: ActionCard[] = []
        let subTitle = "Get a personalised Medical & Lifestyle plan."
        for (let i = 0; i < bundles.length; i++) {
            const pack = bundles[i]
            const offerPrice = OfferUtil.getPackOfferAndPrice(pack, bundleoffers)
            packActionCards.push({
                title: pack.title,
                price: offerPrice.price,
                image: pack.imageUrl,
                action: ActionUtil.carefitbundle(pack.productId, pack.subCategoryCode)
            })
            if (!_.isEmpty(offerPrice.offers)) {
                subTitle = offerPrice.offers[0].description
            }
        }
        const widgetTitle: PageWidgetTitle = this.recommendationPageConfig.careHCUPackTitle
        widgetTitle.subTitle = subTitle
        const seeMoreAction = ActionUtil.careFitClp()
        const footer: PageWidgetFooter = {
            action: seeMoreAction,
            title: "Explore care.fit",
        }
        const widget: PackRecommendationWidget = new PackRecommendationWidget(widgetTitle, packActionCards, footer)
        widget.analyticsData = {
            recommendationCategory: productType + "_PACK"
        }
        return this.getRecommendationView(widget)
    }

    async buildCareConsultationView(userContext: UserContext, consultations: ConsultationProduct[], productType: ProductType, bundleoffers: PackOffersResponse, appVersion: number): Promise<RecommendationView> {
        const packActionCards: ActionCard[] = []
        const lcConsultation = consultations.filter(consultation => consultation.productId === "CONS017")[0]
        const doctorConsultation = consultations.filter(consultation => consultation.productId === "CONS003")[0]
        let offerPrice = OfferUtil.getPackOfferAndPrice(lcConsultation, bundleoffers)
        packActionCards.push({
            title: lcConsultation.title,
            price: offerPrice.price,
            image: "/image/carefit/consultation/lifestyleCoach_thumb.png",
            action: ActionUtil.teleconsultationSingle(userContext, lcConsultation.productId, lcConsultation.urlPath, undefined, undefined, CareUtil.getVerticalForConsultation(lcConsultation.doctorType))
        })

        offerPrice = OfferUtil.getPackOfferAndPrice(doctorConsultation, bundleoffers)
        packActionCards.push({
            title: doctorConsultation.title,
            price: offerPrice.price,
            image: "/image/carefit/consultation/doctorConsultation_thump.png",
            action: ActionUtil.teleconsultationSingle(userContext, doctorConsultation.productId, lcConsultation.urlPath, undefined, undefined, CareUtil.getVerticalForConsultation(doctorConsultation.doctorType))
        })

        const widgetTitle: PageWidgetTitle = {
            title: "Consult a Nutritionist & Doctor",
            subTitle: "On-time consultation, no queues & waiting!"
        }
        const seeMoreAction = ActionUtil.careFitClp()
        const footer: PageWidgetFooter = {
            action: seeMoreAction,
            title: "Explore care.fit",
        }
        const widget: PackRecommendationWidget = new PackRecommendationWidget(widgetTitle, packActionCards, footer)
        widget.analyticsData = {
            recommendationCategory: productType + "_PACK"
        }
        return this.getRecommendationView(widget)
    }

    async buildCultPackView(fitnessPacks: OfflineFitnessPack[], productType: ProductType, userAgent: UserAgent, packOffersV3Response?: CultProductPricesResponse): Promise<RecommendationView> {
        const packActionCards: ActionCard[] = []
        for (let i = 0; i < fitnessPacks.length; i++) {
            const fitnessPack = fitnessPacks[i]
            if (fitnessPack.status === "ACTIVE") {
                const offerDetails = CultUtil.getOfferDetailsPMS(fitnessPack, packOffersV3Response)
                packActionCards.push({
                    title: fitnessPack.title,
                    subTitle: CatalogueServiceUtilities.getCultSubtitle(fitnessPack),
                    price: offerDetails.price,
                    image: CatalogueServiceUtilities.getFitnessProductImage(fitnessPack, "MAGAZINE", userAgent),
                    action: CatalogueServiceUtilities.getPackPageAction(fitnessPack, userAgent)
                })
            }
            // To show only NO_OF_ACTION_CARDS pack action cards
            if (packActionCards.length >= NO_OF_ACTION_CARDS) {
                break
            }
        }
        const widgetTitle: PageWidgetTitle = productType === "FITNESS" ? this.recommendationPageConfig.cultPackTitle : this.recommendationPageConfig.mindPackTitle
        const seeMoreAction = productType === "FITNESS" ? ActionUtil.cultPackBrowse() : ActionUtil.mindPackBrowse()
        const footer: PageWidgetFooter = {
            action: seeMoreAction,
            title: this.recommendationPageConfig.packSeeMoreText,
        }
        const widget: PackRecommendationWidget = new PackRecommendationWidget(widgetTitle, packActionCards, footer)
        widget.analyticsData = {
            recommendationCategory: productType + "_PACK"
        }
        return this.getRecommendationView(widget)
    }
    async buildFoodSingleView(userContext: UserContext, mealSlot: MealSlot,
        mealMenuAvailability: MenuAvailabilityResult,
        recommendedMealMap: _.Dictionary<RecommendedMeals>,
        singleOfferResponse: FoodSinglePriceOfferResponse): Promise<RecommendationView> {
        const productIds = _.map(mealMenuAvailability.menus, menu => menu.productId)
        const productMap = await this.catalogueService.getProductMap(productIds)
        const mealItemPromises = _.map(mealMenuAvailability.menus, async (menu) => {
            const product = productMap[menu.productId]
            let parentProduct
            if (!_.isNil(product.parentProductId)) {
                parentProduct = await this.catalogueServicePMS.getCatalogProduct(product.parentProductId)
            }
            const nutritionInfo = EatUtil.computeNutritionalInfo(product, parentProduct, "EAT_FIT")
            const offerDetails = OfferUtil.getSingleOfferAndPrice(product, mealMenuAvailability.day, singleOfferResponse, mealSlot)
            const availability = OfferUtil.getAvailabilityV1(product, mealMenuAvailability.inventoryResult)
            const actions: Action[] = []
            const seoParams: SeoUrlParams = {
                productName: product.title
            }
            actions.push({
                url: ActionUtil.foodSingle(product.productId, mealMenuAvailability.day, mealSlot, true, undefined, userContext.sessionInfo.userAgent, seoParams),
                actionType: "WIDGET_NAVIGATION"
            })
            let image = UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, "FOOD", "THUMBNAIL", product.imageVersion)
            image = (menu.salesTag ? "/l_" + this.eatLandingPageConfig.salesTagsImages[menu.salesTag] : "") + image
            const mealItem: MealItem = {
                title: product.title,
                calories: `${Math.ceil(nutritionInfo.Calories["Total Calories"])} kal`,
                price: offerDetails.price,
                image: image,
                shipmentWeight: product.shipmentWeight,
                stock: Math.min(MAX_CART_SIZE, availability.left),
                date: mealMenuAvailability.day,
                isInventorySet: availability.total > 0 ? true : false,
                actions: actions,
                productId: product.productId,
                isVeg: product.attributes["isVeg"] === "TRUE" ? true : false,
                foodCategoryId: product.foodCategoryId ? product.foodCategoryId : DEFAULT_FOOD_CATEGORY_ID
            }
            return mealItem
        })
        let mealItems = await Promise.all(mealItemPromises)
        const categoryIdArray = _.uniq(mealItems.map((mealItem) => {
            return mealItem.foodCategoryId
        }))
        const clpCategories: { [id: string]: FoodCategory } = this.foodCategoryService.getCLPCategories(categoryIdArray)
        mealItems = EatUtil.sortMealItems(mealItems, recommendedMealMap, mealSlot, productMap, clpCategories, "ONLINE")
        // filter sold out items
        mealItems = mealItems.filter(mealItem => {
            return mealItem.stock > 0
        })
        if (!_.isEmpty(mealItems)) {
            mealItems = mealItems.length > NO_OF_ACTION_CARDS ? mealItems.slice(0, NO_OF_ACTION_CARDS) : mealItems
            const widgetTitle: PageWidgetTitle = this.getFoodSingleTitle(mealSlot)
            const seeMoreText: string = "See " + this.getFoodSingleSeeMoreText(mealSlot, mealMenuAvailability.day, userContext)
            const seeMoreAction = ActionUtil.eatFitClp(ALL_MEAL_SLOTS.indexOf(mealSlot))
            const footer: PageWidgetFooter = {
                title: seeMoreText,
                action: seeMoreAction,
            }
            const widget: FoodSinglesRecommendationWidget = new FoodSinglesRecommendationWidget(widgetTitle, mealItems, footer)
            widget.analyticsData = {
                recommendationCategory: "FOOD_SINGLES"
            }
            return this.getRecommendationView(widget)
        }
    }

    getFoodSingleTitle(mealSlot: MealSlot): PageWidgetTitle {
        const meal = MealUtil.getMealName(mealSlot).toLowerCase()
        const widgetTitle: PageWidgetTitle = {
            title: "Try these delicious eat.fit meals for " + meal
        }
        return widgetTitle
    }

    getFoodSingleSeeMoreText(mealSlot: MealSlot, mealDate: string, userContext: UserContext): string {
        const meal = MealUtil.getMealName(mealSlot).toLowerCase()
        const seeMoreText = TimeUtil.getDayText(mealDate, userContext.userProfile.timezone, {
            sameDay: `[today's ${meal}]`,
            nextDay: `[tomorrow's ${meal}]`,
            nextWeek: `dddd ['s ${meal}]`
        })
        return seeMoreText
    }

    getFoodPackSeeMoreText(mealSlot: MealSlot): string {
        const mealSlotName = MealUtil.getMealName(mealSlot).toLowerCase()
        const seeMoreText = `See all ${mealSlotName} packs`
        return seeMoreText
    }

    getRecommendationView(...widgets: PageWidget[]) {
        return {
            widgets: widgets
        }
    }
}

export default RecommendationViewBuilder
