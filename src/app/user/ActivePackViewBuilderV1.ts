import {
    DiagnosticProduct,
} from "@curefit/care-common"
import {
    AccessLevel,
    CultPackType,
    MembershipStates,
} from "@curefit/cult-common"
import {
    FoodPack,
} from "@curefit/eat-common"
import {
    HourMin,
} from "@curefit/base-common"
import {
    MealSlot,
} from "@curefit/eat-common"
import {
    MindPack,
} from "@curefit/mind-common"
import {
    ProductType,
} from "@curefit/product-common"
import {
    UserAgentType as UserAgent
} from "@curefit/base-common"
import {
    ProgramPackProduct,
} from "@curefit/product-common"
import { inject, injectable } from "inversify"
import { FoodBooking, FoodPackBooking } from "@curefit/shipment-common"
import {
    ICatalogueService,
    CATALOG_CLIENT_TYPES,
    CatalogueServiceV2Utilities,
    ICatalogueServicePMS
} from "@curefit/catalog-client"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import * as _ from "lodash"
import { UrlPathBuilder } from "@curefit/product-common"
import { Action, ActionType, PauseInfo } from "../common/views/WidgetView"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { ActionUtil, ActionUtilV1, SeoUrlParams } from "@curefit/base-utils"
import { SlotUtil } from "@curefit/eat-util"
import * as momentTz from "moment-timezone"
import { max } from "moment-timezone"
import IProductBusiness from "../product/IProductBusiness"
import { ProgramMembership } from "@curefit/program-client"
import { DIYPackFulfilment } from "@curefit/diy-common"
import { MealUtil } from "@curefit/base-utils"
import { ActiveBundleOrderDetail, BookingDetail } from "@curefit/albus-client"
import { MembershipDetails } from "@curefit/cult-common"
import AppUtil from "../util/AppUtil"
import { UserContext } from "@curefit/userinfo-common"
import { CacheHelper } from "../util/CacheHelper"
import { User } from "@curefit/user-common"
import { ActionUtil as AppActionUtil } from "../util/ActionUtil"
import { MembershipItemUtil } from "../util/MembershipItemUtil"
import { FitClubMembership } from "@curefit/fitclub-models"
import { ICultServiceOld as ICultService, CULT_CLIENT_TYPES } from "@curefit/cult-client"
import { FitclubBusiness } from "../fitclub/FitclubBusiness"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService } from "@curefit/diy-client"
import { Membership, MembershipType, Status } from "@curefit/membership-commons"
import LiveUtil from "../util/LiveUtil"
import {
    GymfitMembershipState,
} from "@curefit/gymfit-common"
import GymfitUtil from "../util/GymfitUtil"
import { MembershipItem } from "../common/views/ProfileWidgetView"
import { BASE_TYPES, Logger } from "@curefit/base"
import CultUtil, { SELECT_IMAGE_URL } from "../util/CultUtil"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import PlayUtil from "../util/PlayUtil"
import { Benefit } from "@curefit/membership-commons/src/common"
import { FoodProduct as Product } from "@curefit/eat-common/dist/src/models/Models"
import { TransformUtil } from "../util/TransformUtil"
import { CENTER_SERVICE_CLIENT_TYPES, ICenterService } from "@curefit/center-service-client"
import { DEFAULT_IMAGE_VERSION, PT_MEMBERSHIP_BENEFIT_NAME } from "../common/Constants"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"
import { PACK_CLIENT_TYPES, IOfflineFitnessPackService } from "@curefit/pack-management-service-client"
import CatalogueServiceUtilities from "../util/CatalogueServiceUtilities"

export interface ActivePackViewV1 {
    productType: ProductType
    mealSlot?: MealSlot
    series?: string
    packId?: string
    image: string
    title: string
    description?: string
    action: Action
    quickAction: Action
    progress?: number
    startDate?: string
    endDate?: string
    packDesc?: string
    footer?: {
        text: string
        image: string
        aspectRatio: number
    }
}


export interface ActiveDIYPackViewV1 extends ActivePackViewV1 {
    preferredDays?: number[]
    preferredTime?: HourMin
    reminder?: boolean
}

const clone = require("clone")

@injectable()
class ActivePackViewBuilderV1 {

    constructor(
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReaderPMS) private catalogueServicePMS: ICatalogueServicePMS,
        @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
        @inject(CULT_CLIENT_TYPES.MindFitService) private mindFitService: ICultService,
        @inject(CUREFIT_API_TYPES.ProductBusiness) private productBusiness: IProductBusiness,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected cacheHelper: CacheHelper,
        @inject(CUREFIT_API_TYPES.FitclubBusiness) protected fitclubBusiness: FitclubBusiness,
        @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) protected diyService: IDIYFulfilmentService,
        @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) protected membershipService: IMembershipService,
        @inject(CENTER_SERVICE_CLIENT_TYPES.CenterService) private centerService: ICenterService,
        @inject(PACK_CLIENT_TYPES.OfflineFitnessPackService) private offlineFitnessPackService: IOfflineFitnessPackService,
    ) {
    }

    private TOP_RIGHT_ICON: string = "image/tata/cult-logo.png"
    private FOOTER_IMAGE: string = "image/tata/tata-neu-with-neupass-text.png"

    async buildFoodPackView(userContext: UserContext, foodPackBooking: FoodPackBooking): Promise<ActivePackViewV1> {
        const tz = userContext.userProfile.timezone
        const userAgent = userContext.sessionInfo.userAgent
        const nextAvailableDate = !_.isEmpty(foodPackBooking.futureShipment) ? foodPackBooking.futureShipment[0].deliveryDate : TimeUtil.todaysDateWithTimezone(tz)
        const pack: FoodPack = await this.catalogueService.getFoodPack(foodPackBooking.packId)
        const mealSlot: MealSlot = SlotUtil.getMealSlot(foodPackBooking.packSlot)
        const foodBooking: FoodBooking = this.productBusiness.getUpcomingShipment(foodPackBooking, tz)
        let difference = 0
        if (foodBooking)
            difference = await this.productBusiness.getCancelDifference(foodBooking)
        const minDate = TimeUtil.getMomentForDateString(nextAvailableDate, tz)
        const maxDate = foodPackBooking.subscriptionType === undefined ? max(momentTz.tz(foodPackBooking.packEndDate, tz).subtract(foodPackBooking.schedule.ticketsLeft, "days"), minDate) : momentTz.tz(new Date(), tz).add(foodPackBooking.schedule.ticketsTotal, "days")
        let quickActionTitle: string
        let quickActionType: ActionType
        if (foodPackBooking.subscriptionType === undefined && MealUtil.isSkipMealsAllowed(pack)) {
            quickActionTitle = foodPackBooking.packState === "ACTIVE" ? "PAUSE PACK" : "RESUME PACK"
            quickActionType = foodPackBooking.packState === "ACTIVE" ? "PAUSE_MEAL_PACK" : "RESUME_MEAL_PACK"
        } else {
            quickActionTitle = foodPackBooking.packState === "PAUSED" || (foodPackBooking.pauseWindow && TimeUtil.getMomentNow(tz).isBefore(foodPackBooking.pauseWindow.start)) ? "UNDO CANCEL" : "CANCEL MEALS"
            quickActionType = foodPackBooking.packState === "PAUSED" || (foodPackBooking.pauseWindow && TimeUtil.getMomentNow(tz).isBefore(foodPackBooking.pauseWindow.start)) ? "RESUME_MEAL_PACK" : "SKIP_MEALS"
            if (foodPackBooking.packState === "PENDING") {
                quickActionTitle = "RENEW"
                quickActionType = "RENEW_MEAL_PACK_V2"
            } else if (foodPackBooking.packState === "HALTED") {
                quickActionTitle = "RESTART"
                quickActionType = "NAVIGATION"
            }
        }
        let progressComplete: number = foodPackBooking.schedule.ticketsTotal - foodPackBooking.schedule.ticketsLeft
        const progressTotal: number = foodPackBooking.schedule.ticketsTotal
        if (foodPackBooking.subscriptionType !== undefined && momentTz.tz(foodPackBooking.packStartDate, tz).isSameOrBefore(TimeUtil.todaysDateWithTimezone(tz))) {
            progressComplete = foodPackBooking.weekendsEnabled ? TimeUtil.getMomentNow(tz).diff(momentTz.tz(foodPackBooking.packStartDate, tz), "days") :
                TimeUtil.weekDayDiffInDays(tz, foodPackBooking.packStartDate, TimeUtil.todaysDateWithTimezone(tz))
            progressComplete += SlotUtil.isCancelCutOffPassed(TimeUtil.todaysDateWithTimezone(tz), tz, foodPackBooking.packSlot.slotId) ? 1 : 0
            // progressTotal = momentTz(foodPackBooking.packEndDate).diff(momentTz(foodPackBooking.packStartDate), "days")
        }
        const seoParams: SeoUrlParams = {
            productName: pack.title
        }
        const foodPackView: ActivePackViewV1 = {
            productType: pack.productType,
            mealSlot: mealSlot,
            startDate: foodPackBooking.packStartDate,
            endDate: foodPackBooking.packEndDate,
            image: UrlPathBuilder.getPackImagePath(pack.productId, "FOOD", "MAGAZINE", pack.imageVersion, userAgent),
            title: foodPackBooking.subscriptionType === undefined ? `${foodPackBooking.schedule.ticketsTotal} meals ${mealSlot.toLowerCase()} pack`
                : foodPackBooking.subscriptionType === "WEEKLY" ? ("Weekly " + mealSlot.toLowerCase() + " plan") : ("Monthly " + mealSlot.toLowerCase() + " plan"),
            action: {
                actionType: "NAVIGATION",
                url: ActionUtil.foodPack(foodPackBooking.packId, foodPackBooking.fulfilmentId, undefined, undefined, userContext.sessionInfo.userAgent, seoParams)
            },
            progress: parseFloat((progressComplete / progressTotal).toFixed(2)),
            quickAction: {
                actionType: quickActionType,
                title: quickActionTitle,
                url: foodPackBooking.packState === "PENDING" ? "curefit://payment" : ActionUtil.foodPack(foodPackBooking.packId, foodPackBooking.fulfilmentId, undefined, undefined, userAgent, seoParams),
                meta: {
                    productId: foodPackBooking.packId,
                    packId: foodPackBooking.packId,
                    orderId: foodPackBooking.orderId,
                    fulfilmentId: foodPackBooking.fulfilmentId,
                    difference: difference,
                    defaultStartDate: minDate.format("YYYY-MM-DD"),
                    defaultDate: minDate.format("YYYY-MM-DD"),
                    deliveryDate: foodBooking ? foodBooking.deliveryDate : undefined,
                    maximumDate: maxDate.format("YYYY-MM-DD"),
                    minimumDate: minDate.format("YYYY-MM-DD")
                }
            }
        }
        if (foodPackBooking.packType === "KIOSKPACK") {
            foodPackView.quickAction = undefined
        }
        return foodPackView
    }


    async buildCultPackView(userContext: UserContext, membershipDetail: MembershipDetails, subUserId: string): Promise<ActivePackViewV1> {
        let cultPackProductId: string = membershipDetail.productId
        if (!cultPackProductId) {
            this.logger.error("PMS::DEPR: CultPackProductId not found for membershipDetail", membershipDetail)
            cultPackProductId = CatalogueServiceV2Utilities.getCultPackProductId(membershipDetail.packId)
        }
        const pack = await this.catalogueServicePMS.getProduct(cultPackProductId)
        const primaryUser: User = await userContext.userPromise
        const userDetail = await this.cacheHelper.getUser(subUserId)
        const subUsersPromises = primaryUser && Array.isArray(primaryUser.subUserRelations) && primaryUser.subUserRelations.map(item => this.cacheHelper.getUser(item.subUserId))
        const subUsers = subUsersPromises && await Promise.all(subUsersPromises)
        const membershipId = await CultUtil.getMembershipIdByCultMembershipId(userContext, membershipDetail.id.toString(), this.membershipService)
        const cultPackView: ActivePackViewV1 = {
            productType: pack.productType,
            startDate: membershipDetail.startDate,
            endDate: membershipDetail.endDate,
            image: CatalogueServiceUtilities.getFitnessProductImage(pack, "MAGAZINE", userContext.sessionInfo.userAgent),
            title: pack.title,
            action: {
                actionType: "NAVIGATION",
                url: await CatalogueServiceUtilities.getCultMembershipDetailsPageAction(membershipId, userContext)
            },
            progress: this.cultPackProgressPercentage(userContext, membershipDetail),
            quickAction: await this.cultPackQuickAction(membershipDetail, pack, AppUtil.isNewClassBookingSuppoted(userContext, (await userContext.userPromise).isInternalUser), userContext, userDetail)
        }
        if (membershipDetail.isPreReg) {
            cultPackView.image = CatalogueServiceUtilities.getFitnessProductImage(pack, "CUSTOM_SUFFIX", userContext.sessionInfo.userAgent, "_prereg_web.jpg")
        }
        return Promise.resolve(cultPackView)
    }

    async buildCultSummaryMembership(userContext: UserContext, membershipDetail: MembershipDetails): Promise<MembershipItem> {
        const membership = await CultUtil.getMembershipByCultMembershipId(userContext, membershipDetail.id.toString(), this.membershipService)
        const cultPackProductId: string = membership.productId
        const pack: OfflineFitnessPack = await this.catalogueServicePMS.getProduct(cultPackProductId)
        if (_.isEmpty(pack)) {
            return null
        }
        const tz = userContext.userProfile.timezone

        let isSelectMembership = MembershipItemUtil.isSelectMembership(membership)
        if (membership?.metadata?.jpmcMembership) {
            isSelectMembership = false
        }
        let selectCenterName: string
        if (isSelectMembership) {
            selectCenterName = await CultUtil.getSelectCenterNameForMembership(userContext, membership, this.centerService)
        }
        const islimitedEliteMembership = membership != null && membership.metadata?.limitedSessions === true
        const isCultpassXMembership = membership != null && membership.metadata?.isCultpassX === true
        const isLimitedElitePack = islimitedEliteMembership || isCultpassXMembership
        if (isLimitedElitePack) {
            const cultBenefit = membership.benefits.find(a => a.name === "CULT")
            selectCenterName = `${cultBenefit.maxTickets} session${cultBenefit.maxTickets > 1 ? "s" : ""}/month`
        }
        let cardTitle
        if (isSelectMembership) cardTitle = "cultpass SELECT"
        else if (islimitedEliteMembership) cardTitle = "cultpass ELITE"
        else if (isCultpassXMembership) cardTitle = "ELITE + ONEPASS"
        else cardTitle = "cultpass ELITE"

        const membershipState = this.getMembershipState(membershipDetail.state === "PAUSED" ? "PAUSED" :
            membershipDetail.state === "CANCELLED" ? "CANCELLED" :
                "PURCHASED", membershipDetail.startDate, membershipDetail.endDate, tz)
        const action: Action = {
            actionType: "NAVIGATION",
            url: await CatalogueServiceUtilities.getCultMembershipDetailsPageAction(membership.id.toString(), userContext)
        }
        const membershipItem: MembershipItem = {
            productType: pack.productType,
            packId: membershipDetail.packId.toString(),
            image: CatalogueServiceUtilities.getFitnessProductImage(pack, "MAGAZINE", userContext.sessionInfo.userAgent),
            title: cardTitle,
            subtitle: selectCenterName,
            descriptionText: `Expires on ${TimeUtil.formatDateStringInTimeZone(membershipDetail.endDate, tz, "DD MMM YYYY")} ${isCultpassXMembership ? "\u2022 Limited Pack" : ""}`,
            action: action,
            progress: this.cultPackProgressPercentage(userContext, membershipDetail) * 100,
            topRightIcon: this.TOP_RIGHT_ICON,
            status: "ACTIVE",
            membershipState: membershipState,
            membershipStateTextColor: MembershipItemUtil.getPrimaryColorForMembershipState(membershipState),
            progressBar: MembershipItemUtil.getProgressBarDateForMembership(
                membershipState,
                userContext,
                `Expires on ${TimeUtil.formatDateStringInTimeZone(membershipDetail.endDate, tz, "DD MMM YYYY")} ${isCultpassXMembership ? "\u2022 Limited Pack" : ""}`,
                this.cultPackProgressPercentage(userContext, membershipDetail),
            ),
            cardAction: action,
            iconDimensions: {
                iconTopSpacing: 40,
                iconHeight: 74,
                iconWidth: 67
            }
        }
        if (membershipDetail.isPreReg) {
            membershipItem.image = CatalogueServiceUtilities.getFitnessProductImage(pack, "CUSTOM_SUFFIX", userContext.sessionInfo.userAgent, "_prereg_web.jpg")
        }
        return Promise.resolve(membershipItem)
    }

    buildCultPackIssueView(userContext: UserContext, membership: Membership): { title: string, description: string, startDate: string, endDate: string } {
        const tz = userContext.userProfile.timezone
        const startDate = TimeUtil.formatEpochInTimeZone(tz, membership.start)
        const endDate = TimeUtil.formatEpochInTimeZone(tz, membership.end)
        const today = TimeUtil.todaysDateWithTimezone(tz)
        const endMoment = TimeUtil.getMomentForDateString(endDate, tz)
        const startMoment = TimeUtil.getMomentForDateString(startDate, tz)
        const numDaysToEndFromToday = endMoment.diff(today, "days")
        let description = ""
        if (startMoment.diff(today, "days") > 0) {
            description = `Starts on ${TimeUtil.formatDateStringInTimeZone(startDate, tz, "MMMM DD, YYYY")}`
        } else if (numDaysToEndFromToday > 0) {
            description = `Ends on ${TimeUtil.formatDateStringInTimeZone(endDate, tz, "MMMM DD, YYYY")}`
        } else if (endDate < today) {
            description = "Expired"
        }
        const cultPackView = {
            description: description,
            startDate: startDate,
            endDate: endDate,
            title: membership.name,
        }
        return cultPackView
    }

    async buildCultPackViewV2(userContext: UserContext, membership: Membership): Promise<ActivePackViewV1> {
        if (membership.status === "PURCHASED" || membership.status === "PAUSED") {
            const pack = await this.catalogueServicePMS.getProduct(membership.productId)
            const tz = userContext.userProfile.timezone
            const startDate = TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.start, "yyyy-MM-dd")
            const endDate = TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.end, "yyyy-MM-dd")
            const today = TimeUtil.todaysDateWithTimezone(tz)
            if (endDate < today) {
                return Promise.reject(`Skipped building EXPIRED membership`)
            }
            const cultPackView: ActivePackViewV1 = {
                productType: pack.productType,
                startDate: startDate,
                endDate: endDate,
                image: CatalogueServiceUtilities.getAccessLevel(pack) === AccessLevel.CENTER ? SELECT_IMAGE_URL : CatalogueServiceUtilities.getFitnessProductImage(pack, "MAGAZINE", userContext.sessionInfo.userAgent),
                title: pack.title,
                action: {
                    actionType: "NAVIGATION",
                    url: await CatalogueServiceUtilities.getCultMembershipDetailsPageAction(membership.id?.toString(), userContext)
                },
                progress: this.cultPackProgressPercentageV2(userContext, membership),
                quickAction: await this.cultPackQuickActionV2(membership, pack, AppUtil.isNewClassBookingSuppoted(userContext, (await userContext.userPromise).isInternalUser), userContext)
            }
            // if (pack.type && pack.type == CultPackType.PRE_REG) {
            //     cultPackView.image = UrlPathBuilder.getPackImagePath(pack.productId, pack.productType, "CUSTOM_SUFFIX", pack.imageVersion, userContext.sessionInfo.userAgent, "_prereg_web.jpg")
            // }
            if (membership.type == MembershipType.COMPLIMENTARY) {
                cultPackView.image = "/image/packs/cult/CULTPACK37/80_mag.jpg"
            }
            return Promise.resolve(cultPackView)
        }
        return Promise.reject(`Skipped building CANCELLED membership`)
    }

    // async buildMindPackView(userContext: UserContext, membershipDetail: MembershipDetails, subUserId: string): Promise<ActivePackViewV1> {
    //     const mindPackProductId: string = CatalogueServiceV2Utilities.getMindPackProductId(membershipDetail.packId)
    //     const pack = await this.catalogueService.getMindPack(mindPackProductId)
    //     const primaryUser: User = await userContext.userPromise
    //     const userDetail = await this.cacheHelper.getUser(subUserId)
    //     const subUsersPromises = primaryUser && Array.isArray(primaryUser.subUserRelations) && primaryUser.subUserRelations.map(item => this.cacheHelper.getUser(item.subUserId))
    //     const subUsers = subUsersPromises && await Promise.all(subUsersPromises)
    //     const mindPackView: ActivePackViewV1 = {
    //         productType: pack.productType,
    //         startDate: membershipDetail.startDate,
    //         endDate: membershipDetail.endDate,
    //         image: UrlPathBuilder.getPackImagePath(mindPackProductId, "MIND", "MAGAZINE", pack.imageVersion, userContext.sessionInfo.userAgent),
    //         title: pack.ageCategory === "JUNIOR" ? `For ${userDetail.firstName} | Cult Junior` : pack.title,
    //         action: {
    //             actionType: "NAVIGATION",
    //             url: ActionUtil.mindFitPack(membershipDetail.packId.toString(), membershipDetail.id.toString())
    //         },
    //         progress: this.cultPackProgressPercentage(userContext, membershipDetail),
    //         quickAction: await this.cultPackQuickAction(membershipDetail, pack, AppUtil.isNewClassBookingSuppoted(userContext, (await userContext.userPromise).isInternalUser), userContext, userDetail)
    //     }
    //     if (membershipDetail.isPreReg) {
    //         mindPackView.image = UrlPathBuilder.getPackImagePath(pack.productId, pack.productType, "CUSTOM_SUFFIX", pack.imageVersion, userContext.sessionInfo.userAgent, "_prereg_web.jpg")
    //     }
    //     return Promise.resolve(mindPackView)
    // }

    async buildCFLivePackView(userContext: UserContext, membership: Membership) {
        const livePackProductId: string = membership.productId
        const pack = await this.catalogueService.getCFLiveProduct(livePackProductId)
        const tz = userContext.userProfile.timezone
        const livePackView: ActivePackViewV1 = {
            packId: livePackProductId,
            quickAction: LiveUtil.getLiveClassBookingPageAction(userContext, "FITNESS", "activePacks"),
            productType: pack.productType,
            startDate: TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.start)),
            endDate: TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.end)),
            image: !_.isEmpty(pack.imageUrl) ? pack.imageUrl : LiveUtil.getDefaultLivePackUrl(userContext),
            title: pack.title,
            action: {
                title: "VIEW MEMBERSHIP",
                actionType: "NAVIGATION",
                url: `curefit://livemembershippage?membershipId=${membership.id}`
            },
            progress: this.livePackProgressPercentage(tz, membership),
            footer: pack?.option?.isNeuPassSubscription ? {
                text: "Complimentary with",
                image: "/image/test/brand-logo/tata-neu-text-and-logo-horizontal.png",
                aspectRatio: 4.68
            } : undefined
        }

        livePackView.quickAction.title = "BOOK LIVE CLASS"
        return Promise.resolve(livePackView)
    }

    async buildCFLiveMembership(userContext: UserContext, membership: Membership): Promise<MembershipItem> {
        const livePackProductId: string = membership.productId
        const pack = await this.catalogueService.getCFLiveProduct(livePackProductId)
        if (_.isEmpty(pack)) {
            return null
        }
        const tz = userContext.userProfile.timezone
        const status: "PURCHASED" | "CANCELLED" | "PAUSED" | "SUSPENDED" = membership.status
        // const membershipState = MembershipItemUtil.getMembershipState(membership, userContext)
        const membershipState = this.getMembershipState(status, TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.start, "yyyy-MM-dd"), TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.end, "yyyy-MM-dd"), tz)
        const action: Action = {
            title: "VIEW MEMBERSHIP",
            actionType: "NAVIGATION",
            url: `curefit://livemembershippage?membershipId=${membership.id}`
        }
        if (status === "PURCHASED") {
            const membershipItem: MembershipItem = {
                productType: pack.productType,
                packId: livePackProductId,
                image: !_.isEmpty(pack.imageUrl) ? pack.imageUrl : LiveUtil.getDefaultLivePackUrl(userContext),
                title: "cultpass HOME",
                descriptionText: `Expires on ${TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.end), "DD MMM YYYY")}`,
                action: action,
                progress: this.livePackProgressPercentage(tz, membership) * 100,
                topRightIcon: this.TOP_RIGHT_ICON,
                status: "ACTIVE",
                membershipState: membershipState,
                membershipStateTextColor: MembershipItemUtil.getPrimaryColorForMembershipState(membershipState),
                progressBar: MembershipItemUtil.getProgressBarDateForMembership(
                    membershipState,
                    userContext,
                    `Expires on ${TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.end), "DD MMM YYYY")}`,
                    this.livePackProgressPercentage(tz, membership)
                ),
                cardAction: action,
                iconDimensions: {
                    iconTopSpacing: 40,
                    iconHeight: 74,
                    iconWidth: 67
                }
            }
            // if (pack.option?.isNeuPassSubscription) {
            //     membershipItem.footer = {
            //         text: "Complimentary with",
            //         image: this.FOOTER_IMAGE
            //     }
            // }
            return Promise.resolve(membershipItem)
        }
        return null
    }

    async buildProgramPackView(userContext: UserContext, membership: ProgramMembership): Promise<ActivePackViewV1> {
        const userAgent = userContext.sessionInfo.userAgent
        const tz = userContext.userProfile.timezone
        const productId = CatalogueServiceV2Utilities.getProgramPackProductId(membership.packId)
        const programPack: ProgramPackProduct = <ProgramPackProduct>await this.catalogueService.getProduct(productId)
        const centerProductType = programPack.tenantId === "mind.fit" ? "MIND" : "FITNESS"
        const mindPackView: ActivePackViewV1 = {
            productType: programPack.productType,
            startDate: TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.startAt)),
            endDate: TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.endAt)),
            image: UrlPathBuilder.getPackImagePath(productId, "PROGRAM", "MAGAZINE", programPack.imageVersion, userAgent),
            title: programPack.title,
            action: {
                actionType: "NAVIGATION",
                url: ActionUtil.programPack(programPack.packId, centerProductType, membership.id)
            },
            progress: this.programPackProgressPercentage(userContext, membership),
            quickAction: {
                actionType: "NAVIGATION",
                url: ActionUtil.programPack(programPack.packId, centerProductType, membership.id),
                title: "GO TO PACK"
            }
        }
        return Promise.resolve(mindPackView)
    }

    async buildMealPlannerPackView(userContext: UserContext): Promise<ActivePackViewV1> {
        const title = "1 Month Meal Plan"
        const action: Action = {
            actionType: "NAVIGATION",
            url: "curefit://mealplannermealoptions"
        }
        return {
            title,
            action,
            image:
                "/image/icons/mealPanner/mealplannerpackcard.png",
            quickAction: action,
            productType: "MEAL_PLAN" as ProductType
        }
    }

    public programPackProgressPercentage(userContext: UserContext, membership: ProgramMembership): number {
        const tz = userContext.userProfile.timezone
        const startDate = TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.startAt))
        const endDate = TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.endAt))
        const today = TimeUtil.todaysDateWithTimezone(tz)
        const packDuration = TimeUtil.diffInDays(tz, startDate, endDate)
        const daysCompleted = today > startDate ? TimeUtil.diffInDays(tz, startDate, today) : 0
        const progress = (daysCompleted / packDuration).toFixed(2)
        return parseFloat(progress)
    }

    private cultPackProgressPercentage(userContext: UserContext, membershipDetail: MembershipDetails): number {
        const tz = userContext.userProfile.timezone
        const today = TimeUtil.todaysDateWithTimezone(tz)
        const daysCompleted = today > membershipDetail.startDate ? TimeUtil.diffInDays(tz, membershipDetail.startDate, today) : 0
        const packDuration = TimeUtil.diffInDays(tz, membershipDetail.startDate, membershipDetail.endDate)
        const progress = (daysCompleted / packDuration).toFixed(2)
        return parseFloat(progress)
    }

    private getPTMembershipProgressPercentage(membership: Membership): number {
        const ptBenefit: Benefit = membership.benefits.find(benefit => PT_MEMBERSHIP_BENEFIT_NAME === benefit.name)
        const progress = (ptBenefit.ticketsUsed / ptBenefit.maxTickets).toFixed(2)
        return parseFloat(progress)
    }

    private cultPackProgressPercentageV2(userContext: UserContext, membership: Membership) {
        const tz = userContext.userProfile.timezone
        const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDateWithTimezone(tz), tz)
        const endDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.end)), tz)
        const startDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.start)), tz)
        const daysCompleted = today > startDate ? today.diff(startDate, "days") : 0
        const totalDays = endDate.diff(startDate, "days")
        const progress = (daysCompleted / totalDays).toFixed(2)
        return parseFloat(progress)
    }

    private livePackProgressPercentage(tz: Timezone, membership: Membership) {
        const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDateWithTimezone(tz), tz)
        const endDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.end)), tz)
        const startDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.start)), tz)
        const daysCompleted = today > startDate ? today.diff(startDate, "days") : 0
        const totalDays = endDate.diff(startDate, "days")
        const progress = (daysCompleted / totalDays).toFixed(2)
        return parseFloat(progress)
    }

    private async cultPackQuickAction(membershipDetail: MembershipDetails, cultPack: OfflineFitnessPack, isNewClassBookingSupported: boolean, userContext: UserContext, subUserDetail: User): Promise<Action> {
        if (membershipDetail.state === "PAUSED" && membershipDetail.startDate <= TimeUtil.todaysDateWithTimezone(userContext.userProfile.timezone)) {
            const membership = cultPack.productType === "FITNESS" ? await this.cultFitService.getMembershipById(membershipDetail.id, userContext.userProfile.userId) : await this.mindFitService.getMembershipById(membershipDetail.id, userContext.userProfile.userId)
            const tz = userContext.userProfile.timezone
            const pauseStartDate = membership.ActivePause ? membership.ActivePause.startTime : undefined
            const pauseEndDate = membership.ActivePause ? membership.ActivePause.maxEndDate ? membership.ActivePause.maxEndDate : undefined : undefined
            let limit = TimeUtil.addDays(tz, membership.endDate, membership.pauseMaxDays - membership.remainingPauseDays)
            let remainingDaysInCurrentDuration
            if (pauseEndDate && pauseStartDate) {
                const pauseStartDateFormatted = TimeUtil.formatDateInTimeZone(tz, pauseStartDate)
                remainingDaysInCurrentDuration = TimeUtil.diffInDays(tz, pauseStartDateFormatted, TimeUtil.todaysDate(tz))
                limit = membership.isSubscription ? TimeUtil.addDays(tz, TimeUtil.todaysDate(tz), remainingDaysInCurrentDuration + 2) : TimeUtil.addDays(tz, membership.endDate, remainingDaysInCurrentDuration)
            }
            const isEditPauseDateSupported = AppUtil.isPauseEditDateSupported(userContext, (await userContext.userPromise).isInternalUser)
            const action: Action = {
                actionType: membershipDetail.state === "PAUSED" ? "RESUME_CULT_MEMEBERSHIP" : "PAUSE_CULT_MEMEBERSHIP",
                title: membershipDetail.state === "PAUSED" ? "RESUME PACK" : "PAUSE PACK",
                meta: {
                    membershipId: membershipDetail.id,
                    packId: membershipDetail.packId,
                    productType: cultPack.productType,
                    title: isEditPauseDateSupported ? "Modify Pause" : "Resume Pack",
                    pauseMaxDays: membership.pauseMaxDays,
                    remainingPauseDays: membership.remainingPauseDays,
                    remainingDaysInCurrentDuration,
                    action: {
                        primaryText: "RESUME",
                        secondaryText: isEditPauseDateSupported ? "EDIT" : "CANCEL"
                    },
                    pauseInfoTitles: {
                        pauseUsed: "Pause days used",
                        membershipExtended: membership.isSubscription && !_.isEmpty(membership.userSubscription) ? `Rs.${membership.userSubscription.metaData.pauseRefundAmount} per pause day to be reduced from next month's bill. Pause days used` : "Membership will be extended by",
                        membershipEndsOn: membership.isSubscription ? "Membership will automatically resume on" : "Membership will now end on",
                        pauseLeft: "Pause days left"
                    },
                    pauseInfo: this.getPauseInfo(remainingDaysInCurrentDuration, tz, limit, (membership.remainingPauseDays - remainingDaysInCurrentDuration), !membership.isSubscription),
                    dateParam: {
                        date: TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, pauseStartDate, "YYYY-MM-DD hh:mm A"),
                        limit,
                        pauseEndDate: pauseEndDate ? TimeUtil.formatDateInTimeZone(tz, pauseEndDate) : undefined,
                        pauseEndText: "Your pack is paused till"
                    },
                    editPauseAction: {
                        meta: {isEdit: true, membershipId: membership.id, productType: cultPack.productType},
                        actionType: "PAUSE_CULT_MEMEBERSHIP"
                    }
                }
            }
            return action
        } else {
            return {
                actionType: "NAVIGATION",
                title: "BOOK A CLASS",
                url: ActionUtil.getBookCultClassUrl(cultPack.productType, isNewClassBookingSupported, "mypackspage")
            }
        }
    }

    private async cultPackQuickActionV2(membership: Membership, cultPack: OfflineFitnessPack, isNewClassBookingSupported: boolean, userContext: UserContext): Promise<Action> {
        const tz = userContext.userProfile.timezone
        const startDate = TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.start, "yyyy-MM-dd")
        const endDate = TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.end, "yyyy-MM-dd")
        if (membership.status === "PAUSED" && startDate <= TimeUtil.todaysDateWithTimezone(tz)) {
            const cultMembershipId = membership.metadata["membershipId"]
            const pauseMaxDays = Math.floor(membership.maxPauseDuration / (24 * 60 * 60 * 1000))
            const remainingPauseDays = Math.floor(membership.remainingPauseDuration / (24 * 60 * 60 * 1000))
            const tz = userContext.userProfile.timezone
            const pauseStartDate = membership.activePause ? TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.activePause.start, "yyyy-MM-dd") : undefined
            const pauseEndDate = membership.activePause ? TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.activePause.end, "yyyy-MM-dd") : undefined
            let limit = TimeUtil.addDays(tz, endDate, pauseMaxDays - remainingPauseDays)
            let remainingDaysInCurrentDuration
            if (pauseEndDate && pauseStartDate) {
                remainingDaysInCurrentDuration = TimeUtil.diffInDays(tz, pauseStartDate, TimeUtil.todaysDate(tz))
                limit = TimeUtil.addDays(tz, endDate, remainingDaysInCurrentDuration)
            }
            const isEditPauseDateSupported = AppUtil.isPauseEditDateSupported(userContext, (await userContext.userPromise).isInternalUser)
            const action: Action = {
                actionType: membership.status === "PAUSED" ? "RESUME_CULT_MEMEBERSHIP" : "PAUSE_CULT_MEMEBERSHIP",
                title: membership.status === "PAUSED" ? "RESUME PACK" : "PAUSE PACK",
                meta: {
                    membershipId: cultMembershipId,
                    // packId: CatalogueServiceUtilities.extractPackId(cultPack.productId),
                    productId: cultPack.productId,
                    productType: cultPack.productType,
                    title: isEditPauseDateSupported ? "Modify Pause" : "Resume Pack",
                    pauseMaxDays: pauseMaxDays,
                    remainingPauseDays: remainingPauseDays,
                    remainingDaysInCurrentDuration,
                    action: {
                        primaryText: "RESUME",
                        secondaryText: isEditPauseDateSupported ? "EDIT" : "CANCEL"
                    },
                    pauseInfoTitles: {
                        pauseUsed: "Pause days used",
                        membershipExtended: "Membership will be extended by",
                        membershipEndsOn: "Membership will now end on",
                        pauseLeft: "Pause days left"
                    },
                    pauseInfo: this.getPauseInfo(remainingDaysInCurrentDuration, tz, limit, (remainingPauseDays - remainingDaysInCurrentDuration), true),
                    dateParam: {
                        date: TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, new Date(pauseStartDate), "YYYY-MM-DD hh:mm A"),
                        limit,
                        pauseEndDate: pauseEndDate,
                        pauseEndText: "Your pack is paused till"
                    },
                    editPauseAction: {
                        meta: {isEdit: true, membershipId: membership.id, productType: cultPack.productType},
                        actionType: "PAUSE_CULT_MEMEBERSHIP"
                    }
                }
            }
            return action
        } else {
            return {
                actionType: "NAVIGATION",
                title: "BOOK A CLASS",
                url: ActionUtil.getBookCultClassUrl(cultPack.productType, isNewClassBookingSupported, "mypackspage")
            }
        }
    }

    private getPauseInfo(pausedUsed: number, tz: Timezone, packEndDate: string, pauseDaysLeft: number, shouldAddExtendsOn: boolean): PauseInfo[] {
        const endsOn = TimeUtil.formatDateInTimeZone(tz, new Date(packEndDate), "DD MMM YYYY")
        const pauseInfo = [
            {title: "Pause days used", value: AppUtil.appendDays(pausedUsed)}
        ]
        if (shouldAddExtendsOn) {
            pauseInfo.push({title: "Membership will be extended by", value: AppUtil.appendDays(pausedUsed)})
            pauseInfo.push({title: "Membership will now end on", value: endsOn})
        }
        pauseInfo.push({title: "Pause days left", value: AppUtil.appendDays(pauseDaysLeft)})
        return pauseInfo
    }

    async buildDIYPackView(userContext: UserContext, fulfilment: DIYPackFulfilment): Promise<ActiveDIYPackViewV1> {
        const packId = fulfilment.productId
        const tz = userContext.userProfile.timezone
        const userAgent = userContext.sessionInfo.userAgent
        const userId = userContext.userProfile.userId
        const packs = fulfilment.productType === "DIY_FITNESS" ? await this.diyService.getDIYFitnessPacksForIds(userId, [packId]) : await this.diyService.getDIYMeditationPacksForIds(userId, [packId])
        const pack = packs[0]
        const nextSessionId = pack.sessionIds[LiveUtil.getNextDIYSessionIdx(pack, fulfilment)]
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        const nextSessions = fulfilment.productType === "DIY_FITNESS_PACK" ? await this.diyService.getDIYFitnessProductsByProductIds(userId, [nextSessionId], tenant) : await this.diyService.getDIYMeditationProductsByProductIds(userId, [nextSessionId], tenant)
        const nextSession = nextSessions[0]
        const actionUrl = ActionUtilV1.diyPackProductPage(pack, userAgent)
        const DIYPackView: ActiveDIYPackViewV1 = {
            packId: packId,
            productType: nextSession.productType,
            startDate: TimeUtil.todaysDate(tz),
            endDate: "",
            title: `${pack.sessionIds.length} ${pack.sessionIds.length > 1 ? "sessions" : "session"}`,
            image: UrlPathBuilder.prefixSlash((userAgent === "APP" ? pack.imageDetails.magazineImage : pack.imageDetails.magazineWebImage)),
            action: {
                actionType: "NAVIGATION",
                url: actionUrl
            },
            progress: parseFloat(((fulfilment.completedProductIds ? fulfilment.completedProductIds.length : 0) / pack.sessionIds.length).toFixed(2)),
            quickAction: {
                title: `START SESSION ${LiveUtil.getNextDIYSessionIdx(pack, fulfilment) + 1}`,
                actionType: "NAVIGATION",
                url: actionUrl
            },
            packDesc: pack.title,
            preferredDays: fulfilment.preferredDays,
            preferredTime: fulfilment.preferredTime,
            reminder: fulfilment.reminder
        }
        return Promise.resolve(DIYPackView)
    }

    async buildManagedPlanPackView(userContext: UserContext, booking: BookingDetail): Promise<ActivePackViewV1> {
        const tz = userContext.userProfile.timezone
        const userAgent = userContext.sessionInfo.userAgent
        const product = <DiagnosticProduct>await this.catalogueService.getProduct(booking.booking.productCode)
        const subscriptionBooking = booking.childBookingInfos.find(bookingInfo => bookingInfo.booking.subCategoryCode === "MP_SUBS")
        const endDate = TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(subscriptionBooking.bundleOrderResponse.expiryTimeEpoch))
        const today = TimeUtil.todaysDateWithTimezone(tz)
        const remainingDays = TimeUtil.diffInDays(tz, endDate, today)
        const total = TimeUtil.diffInDays(tz, endDate, TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(subscriptionBooking.bundleOrderResponse.startTimeEpoch)))
        const progress = parseFloat(((total - remainingDays) / total).toFixed(2))
        const managedPlanPackView: ActivePackViewV1 = {
            productType: booking.booking.categoryCode as ProductType,
            startDate: TimeUtil.formatDateInTimeZone(tz,
                TimeUtil.parseDateFromEpoch(subscriptionBooking.bundleOrderResponse.startTimeEpoch), "DD-MM-YYYY"),
            endDate: TimeUtil.formatDateInTimeZone(tz,
                TimeUtil.parseDateFromEpoch(subscriptionBooking.bundleOrderResponse.expiryTimeEpoch), "DD-MM-YYYY"),
            image: product.imageUrl,
            title: "Managed Care Plan",
            action: {
                actionType: "NAVIGATION",
                url: ActionUtil.carefitbundle(booking.booking.productCode, booking.booking.subCategoryCode, booking.booking.id.toString())
            },
            progress: progress,
            quickAction: {
                title: "MANAGE",
                actionType: "NAVIGATION",
                url: ActionUtil.carefitbundle(booking.booking.productCode, booking.booking.subCategoryCode, booking.booking.id.toString())
            }
        }
        return Promise.resolve(managedPlanPackView)
    }

    async buildTherapyPackView(userContext: UserContext, booking: BookingDetail): Promise<ActivePackViewV1> {
        const tz = userContext.userProfile.timezone
        const userAgent = userContext.sessionInfo.userAgent
        const product = <DiagnosticProduct>await this.catalogueService.getProduct(booking.booking.productCode)
        const endDate = TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(booking.bundleOrderResponse.expiryTimeEpoch))
        const today = TimeUtil.todaysDateWithTimezone(tz)
        const remainingDays = TimeUtil.diffInDays(tz, endDate, today)
        const total = TimeUtil.diffInDays(tz, endDate, TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(booking.bundleOrderResponse.startTimeEpoch)))
        const progress = parseFloat(((total - remainingDays) / total).toFixed(2))
        const isFlutter = AppUtil.checkIfUserPartOfMindTherapyAuroraExperiment(userContext, this.hamletBusiness)
        const managedPlanPackView: ActivePackViewV1 = {
            productType: booking.booking.categoryCode as ProductType,
            startDate: TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(booking.bundleOrderResponse.startTimeEpoch), "DD-MM-YYYY"),
            endDate: TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(booking.bundleOrderResponse.expiryTimeEpoch), "DD-MM-YYYY"),
            image: product.imageUrl,
            title: product.title,
            action: {
                actionType: "NAVIGATION",
                url: ActionUtil.carefitbundle(booking.booking.productCode, booking.booking.subCategoryCode, booking.booking.id.toString(), undefined, undefined, undefined, undefined, undefined, undefined, undefined, isFlutter ? "flutter" : undefined)
            },
            progress: progress,
            quickAction: {
                title: "MANAGE",
                actionType: "NAVIGATION",
                url: ActionUtil.carefitbundle(booking.booking.productCode, booking.booking.subCategoryCode, booking.booking.id.toString())
            }
        }
        return Promise.resolve(managedPlanPackView)
    }

    async buildBundleSessionPackView(userContext: UserContext, bundleBooking: ActiveBundleOrderDetail): Promise<ActivePackViewV1> {
        const tz = userContext.userProfile.timezone
        const userAgent = userContext.sessionInfo.userAgent
        const product = <DiagnosticProduct>await this.catalogueService.getProduct(bundleBooking.productCode)
        const endDate = TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(bundleBooking.endDate))
        const today = TimeUtil.todaysDateWithTimezone(tz)
        const remainingDays = TimeUtil.diffInDays(tz, endDate, today)
        const total = TimeUtil.diffInDays(tz, endDate, TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(bundleBooking.startDate)))
        const progress = parseFloat(((total - remainingDays) / total).toFixed(2))
        const activePackView: ActivePackViewV1 = {
            productType: bundleBooking.categoryCode as ProductType,
            startDate: TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(bundleBooking.startDate), "DD-MM-YYYY"),
            endDate: TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(bundleBooking.endDate), "DD-MM-YYYY"),
            image: product.imageUrl,
            title: product.title,
            action: {
                actionType: "NAVIGATION",
                url: ActionUtil.carefitbundle(bundleBooking.productCode, bundleBooking.subCategoryCode, bundleBooking.bookingId.toString(), undefined, undefined, undefined, undefined, undefined, userContext.sessionInfo.userAgent)
            },
            progress: progress,
            quickAction: {
                title: "MANAGE",
                actionType: "NAVIGATION",
                url: ActionUtil.carefitbundle(bundleBooking.productCode, bundleBooking.subCategoryCode, bundleBooking.bookingId.toString(), undefined, undefined, undefined, undefined, undefined, userContext.sessionInfo.userAgent)
            }
        }
        return Promise.resolve(activePackView)
    }

    async buildHCUActivePackView(userContext: UserContext, bundleBooking: ActiveBundleOrderDetail): Promise<ActivePackViewV1> {
        const product = <DiagnosticProduct>await this.catalogueService.getProduct(bundleBooking.productCode)
        const hcuActivePackView: ActivePackViewV1 = {
            productType: bundleBooking.categoryCode as ProductType,
            image: product.imageUrl,
            title: product.title,
            action: {
                actionType: "NAVIGATION",
                url: ActionUtil.carefitbundle(bundleBooking.productCode, bundleBooking.subCategoryCode, bundleBooking.bookingId.toString())
            },
            quickAction: {
                title: "MANAGE",
                actionType: "NAVIGATION",
                url: ActionUtil.carefitbundle(bundleBooking.productCode, bundleBooking.subCategoryCode, bundleBooking.bookingId.toString())
            }
        }
        return Promise.resolve(hcuActivePackView)
    }

    async buildFitClubPackView(userAgent: UserAgent, fitClubMembership: FitClubMembership, userContext: UserContext): Promise<ActivePackViewV1> {
        const isFitCulbV2Supported = await this.fitclubBusiness.checkIfUserIsEligibleForFitclubV2(userContext)
        const fitClubPackView: ActivePackViewV1 = {
            productType: "FIT_CLUB_MEMBERSHIP",
            image: UrlPathBuilder.getPackImagePath(fitClubMembership.productId, "FIT_CLUB_MEMBERSHIP", "MAGAZINE", fitClubMembership.imageVersion, userAgent),
            title: fitClubMembership.title,
            action: isFitCulbV2Supported ? {
                actionType: "NAVIGATION",
                url: "curefit://fitclubsummarypage"
            } : undefined,
            quickAction: isFitCulbV2Supported ? {
                title: "DETAILS",
                actionType: "NAVIGATION",
                url: "curefit://fitclubsummarypage"
            } : undefined
        }
        return Promise.resolve(fitClubPackView)
    }

    async buildConsultationPackView(userContext: UserContext, bundleBooking: ActiveBundleOrderDetail): Promise<ActivePackViewV1> {
        const tz = userContext.userProfile.timezone
        const userAgent = userContext.sessionInfo.userAgent
        const product = <DiagnosticProduct>await this.catalogueService.getProduct(bundleBooking.productCode)
        const endDate = TimeUtil.formatDateInTimeZoneDateFns(tz, TimeUtil.parseDateFromEpoch(bundleBooking.endDate), "yyyy-MM-dd")
        const today = TimeUtil.todaysDateWithTimezone(tz)
        const remainingDays = TimeUtil.diffInDays(tz, endDate, today)
        const total = TimeUtil.diffInDays(tz, endDate, TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(bundleBooking.startDate)))
        const progress = parseFloat(((total - remainingDays) / total).toFixed(2))
        const activePackView: ActivePackViewV1 = {
            productType: bundleBooking.categoryCode as ProductType,
            startDate: TimeUtil.formatDateInTimeZoneDateFns(tz, TimeUtil.parseDateFromEpoch(bundleBooking.startDate), "dd-MM-yyyy"),
            endDate: TimeUtil.formatDateInTimeZoneDateFns(tz, TimeUtil.parseDateFromEpoch(bundleBooking.endDate), "dd-MM-yyyy"),
            image: product.imageUrl,
            title: product.title,
            action: {
                actionType: "NAVIGATION",
                url: ActionUtil.carefitbundle(bundleBooking.productCode, bundleBooking.subCategoryCode, bundleBooking.bookingId.toString())
            },
            progress: progress,
            quickAction: {
                title: "MANAGE",
                actionType: "NAVIGATION",
                url: ActionUtil.carefitbundle(bundleBooking.productCode, bundleBooking.subCategoryCode, bundleBooking.bookingId.toString())
            }
        }
        return Promise.resolve(activePackView)
    }

    async buildGymFitPackView(userContext: UserContext, membership: Membership): Promise<ActivePackViewV1> {
        if (membership.status === "PURCHASED" || membership.status === "PAUSED") {
            const tz = userContext.userProfile.timezone
            // const product = membership.listing.product
            const startDate = TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.start, "yyyy-MM-dd")
            const endDate = TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.end, "yyyy-MM-dd")
            const today = TimeUtil.todaysDateWithTimezone(tz)
            if (endDate < today) {
                return Promise.reject(`Skipped building ${GymfitMembershipState.EXPIRED} membership`)
            }
            const remainingDays = TimeUtil.diffInDays(tz, today, endDate)
            const total = TimeUtil.diffInDays(tz, startDate, endDate)
            const progress = today < startDate ? 0 : parseFloat(((total - remainingDays) / total).toFixed(2))
            const isSelectMembership = MembershipItemUtil.isSelectMembership(membership)
            let selectCenterName: string
            if (isSelectMembership) {
                selectCenterName = await CultUtil.getSelectCenterNameForMembership(userContext, membership, this.centerService)
            }
            // const durationInMonths = Math.floor(product.durationInDays / 30)
            // const title = product.durationInDays < 30 ? pluralizeStringIfRequired(`${product.durationInDays} day`, product.durationInDays) : pluralizeStringIfRequired(`${durationInMonths} month`, durationInMonths)
            const activePackView: ActivePackViewV1 = {
                productType: "GYMFIT_FITNESS_PRODUCT",
                startDate: startDate,
                endDate: endDate,
                image: GymfitUtil.getDefaultMagazineImage(),
                title: isSelectMembership ? `cultpass SELECT ${selectCenterName}` : membership.name, // check this
                action: {
                    actionType: "NAVIGATION",
                    url: await GymfitUtil.getGoldMembershipDetailsUrl(membership, userContext)
                },
                progress: progress,
                quickAction: GymfitUtil.getExploreGymsAction()
            }
            return Promise.resolve(activePackView)
        }
        return Promise.reject(`Skipped building ${GymfitMembershipState.CANCELLED} membership`)
    }

    async buildOnePassMembership(userContext: UserContext, membership: Membership): Promise<MembershipItem> {
        if (membership.status === "PURCHASED" || membership.status === "PAUSED") {
            const tz = userContext.userProfile.timezone
            // const product = membership.listing.product
            const startDate = TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.start, "yyyy-MM-dd")
            const endDate = TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.end, "yyyy-MM-dd")
            const today = TimeUtil.todaysDateWithTimezone(tz)
            if (endDate < today) {
                return Promise.reject(`Skipped building ${GymfitMembershipState.EXPIRED} membership`)
            }
            const remainingDays = TimeUtil.diffInDays(tz, today, endDate)
            const total = TimeUtil.diffInDays(tz, startDate, endDate)
            const progress = today < startDate ? 0 : parseFloat(((total - remainingDays) / total).toFixed(2))
            // const durationInMonths = Math.floor(product.durationInDays / 30)
            // const title = product.durationInDays < 30 ? pluralizeStringIfRequired(`${product.durationInDays} day`, product.durationInDays) : pluralizeStringIfRequired(`${durationInMonths} month`, durationInMonths)
            // const membershipState = MembershipItemUtil.getMembershipState(membership, userContext)
            const action: Action = {
                actionType: "NAVIGATION",
                url: await GymfitUtil.getGoldMembershipDetailsUrl(membership, userContext)
            }
            const membershipState = "ACTIVE"
            const membershipItem: MembershipItem = {
                productType: "ONEPASS_PRODUCT",
                packId: membership.productId,
                image: GymfitUtil.getDefaultMagazineImage(),
                title: "OnePass",
                descriptionText: `Expires on ${TimeUtil.formatDateInTimeZoneDateFns(tz, TimeUtil.parseDateFromEpoch(membership.end), "dd MMM yyyy")}`,
                action: action,
                progress: progress * 100,
                topRightIcon: this.TOP_RIGHT_ICON,
                status: "ACTIVE",
                membershipState: membershipState,
                membershipStateTextColor: MembershipItemUtil.getPrimaryColorForMembershipState(membershipState),
                progressBar: MembershipItemUtil.getProgressBarDateForMembership(
                    membershipState,
                    userContext,
                    `Expires on ${TimeUtil.formatDateInTimeZoneDateFns(tz, TimeUtil.parseDateFromEpoch(membership.end), "dd MMM yyyy")}`,
                    progress,
                ),
                cardAction: action,
                iconDimensions: {
                    iconTopSpacing: 40,
                    iconHeight: 74,
                    iconWidth: 67
                }
            }
            return Promise.resolve(membershipItem)
        }
        return Promise.reject(`Skipped building ${GymfitMembershipState.CANCELLED} membership`)
    }

    async buildGymFitMembership(userContext: UserContext, membership: Membership): Promise<MembershipItem> {
        if (membership.status === "PURCHASED" || membership.status === "PAUSED") {
            const tz = userContext.userProfile.timezone
            // const product = membership.listing.product
            const startDate = TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.start, "yyyy-MM-dd")
            const endDate = TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.end, "yyyy-MM-dd")
            const today = TimeUtil.todaysDateWithTimezone(tz)
            if (endDate < today) {
                return Promise.reject(`Skipped building ${GymfitMembershipState.EXPIRED} membership`)
            }
            const remainingDays = TimeUtil.diffInDays(tz, today, endDate)
            const total = TimeUtil.diffInDays(tz, startDate, endDate)
            const progress = today < startDate ? 0 : parseFloat(((total - remainingDays) / total).toFixed(2))
            // const durationInMonths = Math.floor(product.durationInDays / 30)
            // const title = product.durationInDays < 30 ? pluralizeStringIfRequired(`${product.durationInDays} day`, product.durationInDays) : pluralizeStringIfRequired(`${durationInMonths} month`, durationInMonths)
            // const membershipState = MembershipItemUtil.getMembershipState(membership, userContext)
            const action: Action = {
                actionType: "NAVIGATION",
                url: await GymfitUtil.getGoldMembershipDetailsUrl(membership, userContext)
            }
            const isSelectMembership = MembershipItemUtil.isSelectMembership(membership)
            let selectCenterName: string
            if (isSelectMembership) {
                selectCenterName = await CultUtil.getSelectCenterNameForMembership(userContext, membership, this.centerService)
            }

            const membershipState = this.getMembershipState(membership.status, startDate, endDate, tz)
            const membershipItem: MembershipItem = {
                productType: "GYMFIT_FITNESS_PRODUCT",
                packId: membership.productId,
                image: GymfitUtil.getDefaultMagazineImage(),
                title: isSelectMembership ? "cultpass SELECT" : "cultpass PRO",
                subtitle: selectCenterName,
                descriptionText: `Expires on ${TimeUtil.formatDateInTimeZoneDateFns(tz, TimeUtil.parseDateFromEpoch(membership.end), "dd MMM yyyy")}`,
                action: action,
                progress: progress * 100,
                topRightIcon: this.TOP_RIGHT_ICON,
                status: "ACTIVE",
                membershipState: membershipState,
                membershipStateTextColor: MembershipItemUtil.getPrimaryColorForMembershipState(membershipState),
                progressBar: MembershipItemUtil.getProgressBarDateForMembership(
                    membershipState,
                    userContext,
                    `Expires on ${TimeUtil.formatDateInTimeZoneDateFns(tz, TimeUtil.parseDateFromEpoch(membership.end), "dd MMM yyyy")}`,
                    progress,
                ),
                cardAction: action,
                iconDimensions: {
                    iconTopSpacing: 40,
                    iconHeight: 74,
                    iconWidth: 67
                }
            }
            return Promise.resolve(membershipItem)
        }
        return Promise.reject(`Skipped building ${GymfitMembershipState.CANCELLED} membership`)
    }

    async buildPlayMembership(userContext: UserContext, membership: Membership) {
        if (membership.status === "PURCHASED" || membership.status === "PAUSED") {
            const tz = userContext.userProfile.timezone
            const startDate = TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.start, "yyyy-MM-dd")
            const endDate = TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.end, "yyyy-MM-dd")
            const today = TimeUtil.todaysDateWithTimezone(tz)
            if (endDate < today) {
                return Promise.reject(`Skipped building Play Expired membership`)
            }
            const remainingDays = TimeUtil.diffInDays(tz, today, endDate)
            const total = TimeUtil.diffInDays(tz, startDate, endDate)
            const progress = today < startDate ? 0 : parseFloat(((total - remainingDays) / total).toFixed(2))
            // const durationInMonths = Math.floor(product.durationInDays / 30)
            // const title = product.durationInDays < 30 ? pluralizeStringIfRequired(`${product.durationInDays} day`, product.durationInDays) : pluralizeStringIfRequired(`${durationInMonths} month`, durationInMonths)
            const action: Action = {
                actionType: "NAVIGATION",
                url: await PlayUtil.getPlayMembershipDetailsUrl(userContext, membership)
            }
            const membershipState = this.getMembershipState(membership.status, startDate, endDate, tz)
            let isSelectPLay = false
            membership.benefits.map((benefit) => {
                if (benefit.name == "PLAY" && benefit.meta["allowedCenterIDs"] != null) {
                    isSelectPLay = true
                }
            })
            if (membership.attributes != null) {
                membership.attributes.map((attribute) => {
                    if (attribute.attrKey === "ACCESS_CENTER") {
                        isSelectPLay = true
                    }
                })
            }
            const membershipItem: MembershipItem = {
                productType: "PLAY",
                packId: membership.productId,
                image: UrlPathBuilder.getPackImagePath(membership.productId, "PLAY", "MAGAZINE", DEFAULT_IMAGE_VERSION, userContext.sessionInfo.userAgent),
                title: "cultpass PLAY",
                descriptionText: `Expires on ${TimeUtil.formatDateInTimeZoneDateFns(tz, TimeUtil.parseDateFromEpoch(membership.end), "dd MMM yyyy")}`,
                action: action,
                progress: progress * 100,
                topRightIcon: this.TOP_RIGHT_ICON,
                status: "ACTIVE",
                membershipState: membershipState,
                membershipStateTextColor: MembershipItemUtil.getPrimaryColorForMembershipState(membershipState),
                progressBar: MembershipItemUtil.getProgressBarDateForMembership(
                    membershipState,
                    userContext,
                    `Expires on ${TimeUtil.formatDateInTimeZoneDateFns(tz, TimeUtil.parseDateFromEpoch(membership.end), "dd MMM yyyy")}`,
                    progress,
                ),
                cardAction: action,
                iconDimensions: {
                    iconTopSpacing: 40,
                    iconHeight: 74,
                    iconWidth: 67
                }
            }
            const slpMembership: { accessWorkout: any; accessCenter: any; isSLPPack: boolean } = MembershipItemUtil.isPlaySportLevelMembership(membership)
            if (slpMembership.isSLPPack) {
                const center = await this.centerService.getCenterById(Number(slpMembership.accessCenter))
                membershipItem.title = "cultpass " + PlayUtil.getSportNameById(slpMembership.accessWorkout)
                membershipItem.subtitle = center.name
            }
            if (isSelectPLay) membershipItem.subtitle = "L  I  T  E"
            return Promise.resolve(membershipItem)
        }
        return Promise.reject(`Skipped building Play Cancelled membership`)
    }

    async buildLuxMembership(userContext: UserContext, membership: Membership) {
        if (membership.status === "PURCHASED" || membership.status === "PAUSED") {
            const tz = userContext.userProfile.timezone
            const pack: OfflineFitnessPack = await this.offlineFitnessPackService.getCachedPackById(membership.productId)
            const startDate = TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.start, "yyyy-MM-dd")
            const endDate = TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.end, "yyyy-MM-dd")
            const today = TimeUtil.todaysDateWithTimezone(tz)
            if (endDate < today) {
                return Promise.reject(`Skipped building Lux Expired membership`)
            }
            const remainingDays = TimeUtil.diffInDays(tz, today, endDate)
            const total = TimeUtil.diffInDays(tz, startDate, endDate)
            const progress = today < startDate ? 0 : parseFloat(((total - remainingDays) / total).toFixed(2))
            const action: Action = {
                actionType: "NAVIGATION",
                url: await GymfitUtil.getLuxMembershipDetailsUrl(membership, userContext)
            }
            const membershipState = this.getMembershipState(membership.status, startDate, endDate, tz)
            const membershipItem: MembershipItem = {
                productType: "LUX_FITNESS_PRODUCT",
                packId: membership.productId,
                image: !_.isEmpty(pack?.product?.images) ? pack.product.images[0].imageURL : GymfitUtil.getDefaultLuxMagazineImage(),
                title: pack.clientMetadata?.centerName ?? pack.product.title,
                descriptionText: `Expires on ${TimeUtil.formatDateInTimeZoneDateFns(tz, TimeUtil.parseDateFromEpoch(membership.end), "dd MMM yyyy")}`,
                action: action,
                progress: progress * 100,
                topRightIcon: this.TOP_RIGHT_ICON,
                status: "ACTIVE",
                membershipState: membershipState,
                membershipStateTextColor: MembershipItemUtil.getPrimaryColorForMembershipState(membershipState),
                progressBar: MembershipItemUtil.getProgressBarDateForMembership(
                    membershipState,
                    userContext,
                    `Expires on ${TimeUtil.formatDateInTimeZoneDateFns(tz, TimeUtil.parseDateFromEpoch(membership.end), "dd MMM yyyy")}`,
                    progress,
                ),
                cardAction: action,
                iconDimensions: {
                    iconTopSpacing: 40,
                    iconHeight: 74,
                    iconWidth: 67
                }
            }
            return Promise.resolve(membershipItem)
        }
        return Promise.reject(`Skipped building lux Cancelled membership`)
    }

    async buildPlayPackView(userContext: UserContext, membership: Membership): Promise<ActivePackViewV1> {
        if (membership.status === "PURCHASED" || membership.status === "PAUSED") {
            const tz = userContext.userProfile.timezone
            const startDate = TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.start, "yyyy-MM-dd")
            const endDate = TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.end, "yyyy-MM-dd")
            const today = TimeUtil.todaysDateWithTimezone(tz)
            if (endDate < today) {
                return Promise.reject(`Skipped building EXPIRED membership`)
            }
            const pack = await this.catalogueServicePMS.getProduct(membership.productId)
            const playPackView: ActivePackViewV1 = {
                productType: pack.productType,
                startDate: startDate,
                endDate: endDate,
                image: AppUtil.isWeb(userContext) ? PlayUtil.PLAY_PACK_MAGAZINE_WEB : UrlPathBuilder.getPackImagePath(membership.productId, "PLAY", "MAGAZINE", DEFAULT_IMAGE_VERSION, userContext.sessionInfo.userAgent),
                title: pack.title,
                action: {
                    actionType: "NAVIGATION",
                    url: await PlayUtil.getPlayMembershipDetailsUrl(userContext, membership)
                },
                progress: this.cultPackProgressPercentageV2(userContext, membership),
                quickAction: AppUtil.isWeb(userContext) ? PlayUtil.getPlayWebBookSlotAction("packPage", membership?.metadata?.centerId ?? membership?.metadata?.centerServiceCenterId, membership?.metadata?.workoutId) : await this.buildPlayQuickAction()
            }
            const slpMembership = MembershipItemUtil.isPlaySportLevelMembership(membership)
            if (slpMembership?.isSLPPack) {
                playPackView.image = PlayUtil.getPackImageByWorkoutId(slpMembership.accessWorkout)
            }
            return Promise.resolve(playPackView)
        }
        return Promise.reject(`Skipped building CANCELLED membership`)
    }

    private async buildPlayQuickAction(): Promise<Action> {
        return {
            actionType: "NAVIGATION",
            title: "BOOK A CLASS",
            url: PlayUtil.getPlayClassBookingPageUrl(),
        }
    }

    async buildTransformMembership(userContext: UserContext, membership: Membership) {
        if (membership.status === "PURCHASED") {
            const tz = userContext.userProfile.timezone
            const productPromise: Promise<Product> = this.catalogueService.getProduct(membership.productId)
            const product = await productPromise
            const baseProduct: DiagnosticProduct = clone(<DiagnosticProduct>product)

            const startDate = TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.start, "yyyy-MM-dd")
            const endDate = TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.end, "yyyy-MM-dd")
            const today = TimeUtil.todaysDateWithTimezone(tz)
            if (endDate < today) {
                return Promise.reject(`Skipped building Transform Expired membership`)
            }
            const remainingDays = TimeUtil.diffInDays(tz, today, endDate)
            const total = TimeUtil.diffInDays(tz, startDate, endDate)
            const progress = today < startDate ? 0 : parseFloat(((total - remainingDays) / total).toFixed(2))

            const action: Action = {
                actionType: "NAVIGATION",
                url: TransformUtil.getTransformMembershipUrl(baseProduct.subCategoryCode),
            }
            const membershipState = this.getMembershipState(membership.status, startDate, endDate, tz)
            const membershipItem: MembershipItem = {
                productType: "TRANSFORM",
                packId: membership.productId,
                image: GymfitUtil.getDefaultMagazineImage(), // Need to correct this
                title: TransformUtil.getTransformMembershipName(baseProduct.subCategoryCode),
                descriptionText: `Expires on ${TimeUtil.formatDateInTimeZoneDateFns(tz, TimeUtil.parseDateFromEpoch(membership.end), "dd MMM yyyy")}`,
                action: action,
                progress: progress * 100,
                topRightIcon: this.TOP_RIGHT_ICON,
                status: "ACTIVE",
                membershipState: membershipState,
                membershipStateTextColor: MembershipItemUtil.getPrimaryColorForMembershipState(membershipState),
                progressBar: MembershipItemUtil.getProgressBarDateForMembership(
                    membershipState,
                    userContext,
                    `Expires on ${TimeUtil.formatDateInTimeZoneDateFns(tz, TimeUtil.parseDateFromEpoch(membership.end), "dd MMM yyyy")}`,
                    progress,
                ),
                cardAction: action,
                iconDimensions: {
                    iconTopSpacing: 40,
                    iconHeight: 74,
                    iconWidth: 67
                }
            }
            return membershipItem
        }
        return Promise.reject(`Skipped building Transform Cancelled membership`)
    }

    async buildComplimentaryCultMembership(userContext: UserContext, membershipDetail: MembershipDetails) {
        const pack: OfflineFitnessPack = await this.catalogueServicePMS.getProduct(membershipDetail.productId)
        this.logger.info(`CULT COMPLIMENTARY PACK DETAILS :: ${JSON.stringify(pack)} cultpackProductId::  ${JSON.stringify(membershipDetail.productId)}`)
        if (_.isEmpty(pack)) {
            return null
        }
        const tz = userContext.userProfile.timezone
        const membershipId = membershipDetail.id.toString()
        const membershipState = this.getMembershipState(membershipDetail.state === "PAUSED" ? "PAUSED" :
            membershipDetail.state === "CANCELLED" ? "CANCELLED" :
                "PURCHASED", membershipDetail.startDate, membershipDetail.endDate, tz)
        const action: Action = {
            actionType: "NAVIGATION",
            url: await CatalogueServiceUtilities.getCultMembershipDetailsPageAction(membershipId, userContext)
        }
        if (pack.status === "ACTIVE") {
            const membershipItem: MembershipItem = {
                productType: pack.productType,
                productId: membershipDetail.productId,
                image: CatalogueServiceUtilities.getFitnessProductImage(pack, "MAGAZINE", userContext.sessionInfo.userAgent),
                title: "cultpass ELITE",
                descriptionText: `Expires on ${TimeUtil.formatDateStringInTimeZone(membershipDetail.endDate, tz, "DD MMM YYYY")}`,
                action: action,
                progress: this.cultPackProgressPercentage(userContext, membershipDetail) * 100,
                topRightIcon: this.TOP_RIGHT_ICON,
                status: "ACTIVE",
                membershipState: membershipState,
                membershipStateTextColor: MembershipItemUtil.getPrimaryColorForProgressBar(membershipState),
                progressBar: MembershipItemUtil.getProgressBarDateForMembership(
                    membershipState,
                    userContext,
                    `Expires on ${TimeUtil.formatDateStringInTimeZone(membershipDetail.endDate, tz, "DD MMM YYYY")}`,
                    this.cultPackProgressPercentage(userContext, membershipDetail),
                ),
                cardAction: action,
                iconDimensions: {
                    iconTopSpacing: 40,
                    iconHeight: 74,
                    iconWidth: 67
                }
            }
            if (membershipDetail.isPreReg) {
                membershipItem.image = CatalogueServiceUtilities.getFitnessProductImage(pack, "CUSTOM_SUFFIX", userContext.sessionInfo.userAgent, "_prereg_web.jpg")
            }
            return Promise.resolve(membershipItem)
        }
        return null
    }

    private hasActivePTBenefits(membership: Membership): boolean {
        return membership.benefits.filter(benefit => benefit.name === PT_MEMBERSHIP_BENEFIT_NAME && benefit.maxTickets !== benefit.ticketsUsed).length > 0
    }

    private getPTCTAAction(userContext: UserContext, membership: Membership, membershipState: string): Action {
        switch (membership.status) {
            case "PURCHASED":
                if (!AppUtil.allowPtSessionBooking(userContext)) {
                    return null
                }
                if (membershipState !== "ACTIVE" && membershipState !== "EXPIRING") {
                    return null
                }
                const membershipMeta = membership.metadata
                if (!membershipMeta["preferredTrainerId"] || !membershipMeta["preferredCenterId"]) {
                    return null
                }
                const trainerId: string = membershipMeta["preferredTrainerId"] + ""
                const centerId: string = membershipMeta["preferredCenterId"] + ""
                const userId: string = userContext.userProfile.userId
                return {
                    title: "BOOK",
                    actionType: "NAVIGATION",
                    url: "curefit://pt_slot_selection?productId=" + membership.productId + "&doctorId=" + trainerId + "&centerId=" + centerId + "&patientId=" + userId + "&parentBookingId=" + membership.id + "&isRescheduled=" + "false"
                }
            default:
                return null
        }
    }

    async buildPTMembership(userContext: UserContext, membership: Membership): Promise<MembershipItem> {
        this.logger.info(`Building PT Membership :: ${JSON.stringify(membership)}`)
        if (_.isEmpty(membership) || membership.status !== "PURCHASED") {
            return null
        }

        const tz = userContext.userProfile.timezone

        const startDate = TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.start, "yyyy-MM-dd")
        const endDate = TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.end, "yyyy-MM-dd")
        const today = TimeUtil.todaysDateWithTimezone(tz)
        if (endDate < today) {
            return Promise.reject(`Skipped building expired PT Membership`)
        }

        const action: Action = {
            actionType: "NAVIGATION",
            url: "curefit://pt_membership_details?membershipId=" + membership.id
        }
        const membershipState = this.getMembershipState(membership.status, startDate, endDate, tz)
        const progress = this.getPTMembershipProgressPercentage(membership)

        const ctaAction: Action = this.hasActivePTBenefits(membership) ? this.getPTCTAAction(userContext, membership, membershipState) : null

        return {
            title: "Gym Personal Training",
            productType: "GYM_PT_PRODUCT",
            packId: membership.productId,
            image: GymfitUtil.getDefaultMagazineImage(), // Need to correct this
            descriptionText: `Expires on ${TimeUtil.formatDateInTimeZoneDateFns(tz, TimeUtil.parseDateFromEpoch(membership.end), "dd MMM yyyy")}`,
            action: action,
            ctaAction,
            progress: progress * 100,
            topRightIcon: this.TOP_RIGHT_ICON,
            status: "ACTIVE",
            membershipState: membershipState,
            membershipStateTextColor: MembershipItemUtil.getPrimaryColorForMembershipState(membershipState),
            progressBar: MembershipItemUtil.getProgressBarDateForMembership(
                membershipState,
                userContext,
                `Expires on ${TimeUtil.formatDateInTimeZoneDateFns(tz, TimeUtil.parseDateFromEpoch(membership.end), "dd MMM yyyy")}`,
                progress,
            ),
            cardAction: action,
            iconDimensions: {
                iconTopSpacing: 40,
                    iconHeight: 74,
                    iconWidth: 67
            }
        }
    }

    getMembershipState(status: Status, startDate: string, endDate: string, tz: Timezone) {
        const today = TimeUtil.todaysDateWithTimezone(tz)
        const numDaysToEndFromToday = TimeUtil.diffInDays(tz, today, endDate)
        switch (status) {
            case "PURCHASED": {
                if (startDate > today) {
                    return "UPCOMING"
                }
                if (endDate < today) {
                    return "EXPIRED"
                }
                if (numDaysToEndFromToday <= 15) {
                    return "EXPIRING"
                }
                return "ACTIVE"
            }
            case "SUSPENDED":
            case "CANCELLED": {
                return "CANCELLED"
            }
            case "PAUSED": {
                return "PAUSED"
            }
            default:
                return "ACTIVE"
        }
    }
}

export default ActivePackViewBuilderV1
