import { User } from "@curefit/user-common"

class UserStatusView {

    constructor(user: User, isNotLoggedIn: boolean) {
        this.userId = user.id.toString()
        this.profilePictureUrl = (!user.profilePictureUrl) ? undefined : user.profilePictureUrl
        this.firstName = (!user.firstName) ? undefined : user.firstName
        this.lastName = (!user.lastName) ? undefined : user.lastName
        this.isNotLoggedIn = isNotLoggedIn ? true : false
        this.email = (!user.email) ? undefined : user.email
        this.phone = (!user.phone) ? undefined : user.phone
        this.countryCallingCode = user.countryCallingCode
        this.isInternalUser = user.isInternalUser
    }

    public userId: string
    public profilePictureUrl: string
    public firstName: string
    public lastName: string
    public isNotLoggedIn: boolean
    public email: string
    public phone: string
    public countryCallingCode: string
    public isInternalUser: boolean
}

Object.seal(UserStatusView)
export default UserStatusView
