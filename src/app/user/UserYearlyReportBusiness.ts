import { inject, injectable } from "inversify"
import { UserContext } from "@curefit/userinfo-common"
import * as _ from "lodash"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { IUserYearEndReportReadOnlyDao } from "./userYearEndReport/UserYearEndReportDao"
import { UserYearEndReport } from "./userYearEndReport/UserYearEndReport"
import { CULT_CLIENT_TYPES, ICultServiceOld as ICultService } from "@curefit/cult-client"
import { ISocialService, SOCIAL_CLIENT_TYPES } from "@curefit/social-client"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { Action } from "@curefit/vm-models"
import CultUtil from "../util/CultUtil"


export interface UserYearlyReport {
    shareableImageUrl: string
    imageUrl: string
    preAnimationLotteType?: string
    postAnimationLotteType: string
    theme: "DARK"| "LIGHT"
    color: string
    action?: Action
    leftCTAText?: string
    rightCTAText?: string
    shareIconUrl?: string
}

export const FAV_WORKOUT_LIST = ["YOGA", "HRX", "PROWL", "SNC", "BOXING", "DANCE", "HIIT"]

@injectable()
export class UserYearlyReportBusiness {

    constructor(
        @inject(CUREFIT_API_TYPES.UserYearEndReportReadOnlyDaoMongoImpl) private userYearEndReportReadOnlyDao: IUserYearEndReportReadOnlyDao,
        @inject(CULT_CLIENT_TYPES.CultFitService) public cultFitService: ICultService,
        @inject(SOCIAL_CLIENT_TYPES.SocialService) public socialService: ISocialService,
        @inject(BASE_TYPES.ILogger) public logger: ILogger,
    ) {
    }

    public async getYearEndReportV2(userContext: UserContext): Promise<UserYearlyReport[]> {
        const userId = userContext.userProfile.userId
        const user = await userContext.userPromise
        const firstName = !_.isEmpty(user.firstName) ? user.firstName : user.lastName
        let userYearEndData: UserYearEndReport = await this.userYearEndReportReadOnlyDao.findOne({ userId: user.id })
        userYearEndData ??= await this.userYearEndReportReadOnlyDao.findOne({ userId: "0" })

        const ret: UserYearlyReport[] = []
        ret.push({
            imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_200:${firstName}!,g_north_west,y_380,x_160,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/2021/1.png`,
            shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_200:${firstName}!,g_north_west,y_380,x_160,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/2021/share/share-1.jpeg`,
            preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/blackEmpty.json`,
            postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/01.json`, // intro - 1
            theme: "LIGHT",
            color: "#000000",
            leftCTAText: "SHARE",
            rightCTAText: "NEXT",
            shareIconUrl: "/image/icons/cult/share_white.png"
        })

        if (userYearEndData.classes && userYearEndData.minsWorked) {
            ret.push({
                imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_130:${userYearEndData.classes},g_north_west,y_270,x_130,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_130:${userYearEndData.minsWorked},g_north_west,y_550,x_130,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/2021/2.png`,
                shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_130:${userYearEndData.classes},g_north_west,y_270,x_130,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_130:${userYearEndData.minsWorked},g_north_west,y_550,x_130,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/2021/share/share-2.jpeg`,
                preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/blackEmpty.json`,
                postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/02.json`, // num classes - 2
                theme: "LIGHT",
                color: "#000000",
                leftCTAText: "SHARE",
                rightCTAText: "NEXT",
                shareIconUrl: "/image/icons/cult/share_white.png"
            })
        }

        if (userYearEndData.bestStreak && userYearEndData.bestStreak > 3) {
            const streakText = userYearEndData.bestStreak <= 5 ? (userYearEndData.bestStreak + "%252F7") : userYearEndData.bestStreak
            ret.push({
                imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_130:${streakText},g_north_west,y_280,x_600,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/2021/3.png`,
                shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_130:${streakText},g_north_west,y_280,x_600,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/2021/share/share-3.jpeg`,
                preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/blackEmpty.json`,
                postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/03.json`, // streak - 3
                theme: "LIGHT",
                color: "#000000",
                leftCTAText: "SHARE",
                rightCTAText: "NEXT",
                shareIconUrl: "/image/icons/cult/share_white.png"
            })
        }

        if (userYearEndData.workoutList && userYearEndData.formatsTried > 0) {
            if (userYearEndData.formatsTried > 0) {
                const formatsLength = userYearEndData.formatsTried < 10 ? "0" + userYearEndData.formatsTried : userYearEndData.formatsTried
                ret.push({
                    imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_130:${formatsLength},g_north_west,y_280,x_130,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/2021/4.png`,
                    shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_130:${formatsLength},g_north_west,y_280,x_130,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/2021/share/share-4.jpeg`,
                    preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/blackEmpty.json`,
                    postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/04.json`, // num formats - 4
                    theme: "LIGHT",
                    color: "#000000",
                    leftCTAText: "SHARE",
                    rightCTAText: "NEXT",
                    shareIconUrl: "/image/icons/cult/share_white.png"
                })
            }
        }

        if (userYearEndData.favWorkout && userYearEndData.favWorkoutMins) {
            ret.push({
                imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_130:${userYearEndData.favWorkout},g_north_west,y_280,x_130,co_rgb:fdfdfd` +
                    `/l_text:fonts:BrandonText-Black.otf_130:${parseFloat(userYearEndData.favWorkoutMins.toString()).toFixed()},g_north_west,y_600,x_135,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/2021/5.png`,
                shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_130:${userYearEndData.favWorkout},g_north_west,y_280,x_130,co_rgb:fdfdfd` +
                    `/l_text:fonts:BrandonText-Black.otf_130:${parseFloat(userYearEndData.favWorkoutMins.toString()).toFixed()},g_north_west,y_600,x_135,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/2021/share/share-5.jpeg`,
                preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/blackEmpty.json`,
                postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/05.json`, // fav workout - 5
                theme: "LIGHT",
                color: "#000000",
                leftCTAText: "SHARE",
                rightCTAText: "NEXT",
                shareIconUrl: "/image/icons/cult/share_white.png"
            })
        }


        if (userYearEndData.buddies && userYearEndData.percentile) {
            ret.push({
                imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_130:${userYearEndData.buddies},g_north_west,y_335,x_130,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_130:${parseFloat(userYearEndData.percentile.toString()).toFixed()}%25,g_north_west,y_680,x_130,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/2021/6.png`,
                shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_130:${userYearEndData.buddies},g_north_west,y_335,x_130,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_130:${parseFloat(userYearEndData.percentile.toString()).toFixed()}%25,g_north_west,y_680,x_130,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/2021/share/share-6.jpeg`,
                preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/blackEmpty.json`,
                postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/06.json`, // num buddies - 6
                theme: "LIGHT",
                color: "#000000",
                leftCTAText: "SHARE",
                rightCTAText: "NEXT",
                shareIconUrl: "/image/icons/cult/share_white.png"
            })
        }

        if (userYearEndData.bestRank > 0 && userYearEndData.bestScore > 100) {
            ret.push({
                imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_130:${userYearEndData.bestScore},g_north_west,y_350,x_130,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_130:${userYearEndData.bestRank},g_north_west,y_590,x_140,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/2021/7.png`,
                shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_130:${userYearEndData.bestScore},g_north_west,y_350,x_130,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_130:${userYearEndData.bestRank},g_north_west,y_590,x_140,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/2021/share/share-7.jpeg`,
                preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/blackEmpty.json`,
                postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/07.json`, // best score - 7
                theme: "LIGHT",
                color: "#000000",
                leftCTAText: "SHARE",
                rightCTAText: "NEXT",
                shareIconUrl: "/image/icons/cult/share_white.png"
            })
        }

        if (userYearEndData.caloriesBurnt) {
            ret.push({
                imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_130:${userYearEndData.caloriesBurnt},g_north_west,y_320,x_130,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/2021/8.png`,
                shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_130:${userYearEndData.caloriesBurnt},g_north_west,y_320,x_130,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/2021/share/share-8.jpeg`,
                preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/blackEmpty.json`,
                postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/09.json`, // num calories - 9
                theme: "LIGHT",
                color: "#000000",
                leftCTAText: "SHARE",
                rightCTAText: "NEXT",
                shareIconUrl: "/image/icons/cult/share_white.png"
            })
        }

        if (userYearEndData.caloriesBurnt && userYearEndData.minsWorked && userYearEndData.buddies && userYearEndData.favWorkout && userYearEndData.bestStreak) {
            ret.push({
                imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_100:${userYearEndData.minsWorked},g_north_west,y_640,x_180,co_rgb:fdfdfd` +
                    `/l_text:fonts:BrandonText-Black.otf_100:${firstName},g_north_west,y_160,x_110,co_rgb:fdfdfd` +
                    `/l_text:fonts:BrandonText-Black.otf_100:${userYearEndData.buddies},g_north_east,y_640,x_200,co_rgb:fdfdfd` +
                    `/l_text:fonts:BrandonText-Black.otf_100:${userYearEndData.bestStreak},g_south_east,y_800,x_250,co_rgb:fdfdfd` +
                    `/l_text:fonts:BrandonText-Black.otf_100:${userYearEndData.favWorkout},g_south_west,y_800,x_120,co_rgb:fdfdfd` +
                    `/l_text:fonts:BrandonText-Black.otf_100:${userYearEndData.caloriesBurnt},g_south_east,y_450,x_360,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/2021/9.png`,
                shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_100:${userYearEndData.minsWorked},g_north_west,y_640,x_180,co_rgb:fdfdfd` +
                    `/l_text:fonts:BrandonText-Black.otf_100:${firstName},g_north_west,y_160,x_110,co_rgb:fdfdfd` +
                    `/l_text:fonts:BrandonText-Black.otf_100:${userYearEndData.buddies},g_north_east,y_640,x_200,co_rgb:fdfdfd` +
                    `/l_text:fonts:BrandonText-Black.otf_100:${userYearEndData.bestStreak},g_south_east,y_800,x_250,co_rgb:fdfdfd` +
                    `/l_text:fonts:BrandonText-Black.otf_100:${userYearEndData.favWorkout},g_south_west,y_800,x_120,co_rgb:fdfdfd` +
                    `/l_text:fonts:BrandonText-Black.otf_100:${userYearEndData.caloriesBurnt},g_south_east,y_450,x_360,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/2021/share/share-9.jpeg`,
                preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/blackEmpty.json`,
                postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/10-1.json`, // overall - 10
                theme: "LIGHT",
                color: "#000000",
                leftCTAText: "SHARE",
                rightCTAText: "NEXT",
                shareIconUrl: "/image/icons/cult/share_white.png"
            })
        }

        ret.push({
            imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/image/userReport/2021/thank.jpeg`,
            shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/image/userReport/2021/thank.jpeg`,
            preAnimationLotteType: undefined,
            postAnimationLotteType: undefined,
            theme: "LIGHT",
            color: "#000000",
            leftCTAText: "HOME",
            rightCTAText: "BACK",
            action: {
                actionType: "NAVIGATION",
                url: "curefit://listpage?pageId=CI_SU_LP",
            },
        })

        return ret
    }

    private getGenericVersionResponseV2(): UserYearlyReport[] {
        const ret: UserYearlyReport[] = []
        const userYearEndData: UserYearEndReport = {
            userId: "0",
            favWorkout: "DANCE",
            classes: 26669652,
            minsWorked: 795964050,
            bestStreak: 350,
            formatsTried: 22,
            favWorkoutMins: 205280711,
            buddies: 1473184,
            caloriesBurnt: 4295511891,
            workoutList: "BOXING,SNC,HRX,DANCE,YOGA,PILATES,HIIT"
        }
        const userYearEndDataText = {
            favWorkout: "DANCE",
            classes: "26 Million",
            minsWorked: "800 Million",
            bestStreak: "350",
            formatsTried: "22",
            favWorkoutMins: "200 Million",
            buddies: "1.5 Million",
            caloriesBurnt: "4 Billion",
        }

        ret.push({
            imageUrl: "https://cdn-images.cure.fit/www-curefit-com/image/upload/w_750,ar_0.5625/image/userReport/generic/name.png",
            shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/w_750,ar_0.5625/image/userReport/generic/name_background.png`,
            preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/blackEmpty.json`,
            postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/01.json`,
            theme: "LIGHT",
            color: "#000000",
            leftCTAText: "SHARE",
            rightCTAText: "NEXT",
            shareIconUrl: "/image/icons/cult/share_white.png"
        })

        ret.push({
            imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_750:26,g_north_west,y_1050,x_600/l_text:fonts:BrandonText-Black.otf_500:million,g_north_west,y_1250,x_1500/l_text:fonts:BrandonText-Black.otf_750:800,g_north_west,y_2550,x_600/l_text:fonts:BrandonText-Black.otf_500:million,g_north_west,y_2725,x_2050/w_750,ar_0.5625/image/userReport/generic/class.png`,
            shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_750:26,g_north_west,y_1050,x_600/l_text:fonts:BrandonText-Black.otf_500:million,g_north_west,y_1250,x_1500/l_text:fonts:BrandonText-Black.otf_750:800,g_north_west,y_2550,x_600/l_text:fonts:BrandonText-Black.otf_500:million,g_north_west,y_2725,x_2050/w_750,ar_0.5625/image/userReport/generic/class_background.png`,
            preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/blackEmpty.json`,
            postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/02.json`,
            theme: "DARK",
            color: "#FFD400",
            leftCTAText: "SHARE",
            rightCTAText: "NEXT",
            shareIconUrl: "/image/icons/cult/share_black.png"
        })

        if (userYearEndDataText.bestStreak) {
            ret.push({
                imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_999:${userYearEndDataText.bestStreak},g_north_west,y_1400,x_2600,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/generic/streak.png`,
                shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_999:${userYearEndDataText.bestStreak},g_north_west,y_1400,x_2600,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/generic/streak_background.png`,
                preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/blackEmpty.json`,
                postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/03.json`,
                theme: "LIGHT",
                color: "#E51C6D",
                leftCTAText: "SHARE",
                rightCTAText: "NEXT",
                shareIconUrl: "/image/icons/cult/share_white.png"
            })
        }

        if (userYearEndData.workoutList && userYearEndData.formatsTried > 0) {
            const formatsTried: string[] = userYearEndData.workoutList.split(",")
            if (userYearEndData.formatsTried > 0) {
                const formatsLength = userYearEndData.formatsTried < 10 ? "0" + userYearEndData.formatsTried : userYearEndData.formatsTried
                let url = `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_9999:${formatsLength},g_north_west,y_1600,x_850,co_rgb:fdfdfd`
                for (let i = 0; i < formatsTried.length; i++) {
                    url += `/l_text:fonts:BrandonText-Black.otf_2250:${formatsTried[i].replace(" ", "")},g_north_west,y_${1500 + 500 * i},x_3000,co_rgb:fdfdfd`
                }
                url += `/l_text:fonts:BrandonText-Black.otf_2250:${"and more"},g_north_west,y_5000,x_3000,co_rgb:fdfdfd`
                ret.push({
                    imageUrl: url + `/w_750,ar_0.5625/image/userReport/generic/formats.png`,
                    shareableImageUrl: url + `/w_750,ar_0.5625/image/userReport/generic/formats_background.png`,
                    preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/blackEmpty.json`,
                    postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/04.json`,
                    theme: "LIGHT",
                    color: "#000000",
                    leftCTAText: "SHARE",
                    rightCTAText: "NEXT",
                    shareIconUrl: "/image/icons/cult/share_white.png"
                })
            }
        }

        ret.push({
            imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_650:DANCE,g_north_west,y_1050,x_650,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_600:200,g_north_west,y_2250,x_650,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_300:million,g_north_west,y_2450,x_1750,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/generic/fav_format.png`,
            shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_650:DANCE,g_north_west,y_1050,x_650,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_600:200,g_north_west,y_2250,x_650,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_300:million,g_north_west,y_2450,x_1750,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/generic/fav_format_background.png`,
            preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/blackEmpty.json`,
            postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/05.json`,
            theme: "LIGHT",
            color: "#000000",
            leftCTAText: "SHARE",
            rightCTAText: "NEXT",
            shareIconUrl: "/image/icons/cult/share_white.png"
        })


        ret.push({
            imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_750:1.5,g_north_west,y_1750,x_650,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_550:million,g_north_west,y_1900,x_1650,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/generic/friends.png`,
            shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_750:1.5,g_north_west,y_1750,x_650,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_550:million,g_north_west,y_1900,x_1650,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/generic/friends_background.png`,
            preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/blackEmpty.json`,
            postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/06.json`,
            theme: "LIGHT",
            color: "#000000",
            leftCTAText: "SHARE",
            rightCTAText: "NEXT",
            shareIconUrl: "/image/icons/cult/share_white.png"
        })

        ret.push({
            imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_850:4,g_north_west,y_1650,x_600,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_600:billion,g_north_west,y_1800,x_1200,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/generic/calorie.png`,
            shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_850:4,g_north_west,y_1650,x_600,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_600:billion,g_north_west,y_1800,x_1200,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/generic/calorie_background.png`,
            preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/blackEmpty.json`,
            postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/09.json`,
            theme: "LIGHT",
            color: "#E51C6D",
            leftCTAText: "SHARE",
            rightCTAText: "NEXT",
            shareIconUrl: "/image/icons/cult/share_white.png"
        })

        ret.push({
            imageUrl: undefined,
            shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_350:800,g_north_west,y_2450,x_650` +
                `/l_text:fonts:BrandonText-Black.otf_200:million,g_north_west,y_2550,x_1325/l_text:fonts:BrandonText-Black.otf_350:1.4,g_north_east,y_2450,x_1300,co_rgb:fdfdfd` +
                `/l_text:fonts:BrandonText-Black.otf_200:million,g_north_east,y_2550,x_650,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_350:22,g_south_east,y_3620,x_1100,co_rgb:fdfdfd` +
                `/l_text:fonts:BrandonText-Black.otf_350:DANCE,g_south_west,y_3600,x_700,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_350:4,g_south_east,y_2180,x_1790,co_rgb:fdfdfd` +
                `/l_text:fonts:BrandonText-Black.otf_200:billion,g_south_east,y_2200,x_1200,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/generic/reportcard_background.png`,
            preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/blackEmpty.json`,
            postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/10.json`,
            theme: "LIGHT",
            color: "#000000",
            leftCTAText: "SHARE",
            rightCTAText: "NEXT",
            shareIconUrl: "/image/icons/cult/share_white.png"
        })

        ret.push({
            imageUrl: undefined,
            shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/w_750,ar_0.5625/image/userReport/generic/wrap_background.png`,
            preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/2021/blackEmpty.json`,
            postAnimationLotteType: undefined,
            theme: "DARK",
            color: "#00B4EC",
            leftCTAText: "SHARE",
            rightCTAText: "CLOSE",
            shareIconUrl: "/image/icons/cult/share_black.png"
        })


        return ret
    }

    public async getYearEndReport(userContext: UserContext): Promise<UserYearlyReport[]> {
        const userId = userContext.userProfile.userId
        const user = await userContext.userPromise
        const firstName = !_.isEmpty(user.firstName) ? user.firstName : user.lastName
        const userYearEndData: UserYearEndReport = await this.userYearEndReportReadOnlyDao.findOne({ userId: user.id })

        if (_.isEmpty(userYearEndData)) {
            return this.getGenericVersionResponse()
        }


        let images: string[] = []
        const cultMomentsResponse = await this.cultFitService.getCultMoments({ userId, appName: "CUREFIT_API", pageNumber: 1, pageSize: 10 })
        if (cultMomentsResponse && !_.isEmpty(cultMomentsResponse.cultMoments)) {
            cultMomentsResponse.cultMoments.forEach(moment => {
                if (CultUtil.isBoomerang(moment.CultMoment)) {
                    if (moment.CultMoment?.thumbnailURL && !_.isEmpty(moment.CultMoment.thumbnailURL)) {
                        images.push(moment.CultMoment.thumbnailURL)
                    }
                } else if (moment?.CultMoment?.imageURL && !_.isEmpty(moment.CultMoment.imageURL)) {
                    images.push(moment.CultMoment.imageURL)
                }
            })
        }
        // if (cultMomentsResponse.cultMoments.length < 2) {
        //     const pagedPostsResponseEntry = (await eternalPromise(this.socialService.getUserPosts(Number(userId), 0, 2))).obj
        //     this.logger.error("live moments for userId: 4051269 " + JSON.stringify(pagedPostsResponseEntry))
        //     pagedPostsResponseEntry.elements.forEach(element => {
        //         const media = element?.contentList?.elements?.[0]?.medias?.[0]
        //         if (media && media.fileCDNPrefix + media.fileName + media.fileExtension) {
        //             const imageUrl =  media.fileCDNPrefix + media.fileName + "." + media.fileExtension
        //             images.push(imageUrl)
        //         }
        //     })
        // }


        images = images.slice(0, 2)

        const ret: UserYearlyReport[] = []
        ret.push({
            imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_750:${firstName}!,g_north_west,y_1650,x_600,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/name.png`,
            shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_750:${firstName}!,g_north_west,y_1650,x_600,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/name_background.png`,
            preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_1_non_looping.json`,
            postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_1_looping.json`,
            theme: "LIGHT",
            color: "#000000",
            leftCTAText: "SHARE",
            rightCTAText: "NEXT",
            shareIconUrl: "/image/icons/cult/share_white.png"
        })

        if (userYearEndData.classes && userYearEndData.minsWorked) {
            const tripCount = Math.floor(userYearEndData.minsWorked / 600)
            ret.push({
                imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_750:${userYearEndData.classes},g_north_west,y_1050,x_600/l_text:fonts:BrandonText-Black.otf_750:${userYearEndData.minsWorked},g_north_west,y_2650,x_600/l_text:fonts:BrandonText-Black.otf_250:${tripCount},g_north_west,y_4275,x_1000/w_750,ar_0.5625/image/userReport/class.png`,
                shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_750:${userYearEndData.classes},g_north_west,y_1050,x_600/l_text:fonts:BrandonText-Black.otf_750:${userYearEndData.minsWorked},g_north_west,y_2650,x_600/l_text:fonts:BrandonText-Black.otf_250:${tripCount},g_north_west,y_4275,x_1000/w_750,ar_0.5625/image/userReport/class_background.png`,
                preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_2_non_looping.json`,
                postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_2_looping.json`,
                theme: "DARK",
                color: "#FFD400",
                leftCTAText: "SHARE",
                rightCTAText: "NEXT",
                shareIconUrl: "/image/icons/cult/share_black.png"
            })
        }

        if (userYearEndData.bestStreak && userYearEndData.bestStreak > 3) {
            const streakText = userYearEndData.bestStreak <= 5 ? (userYearEndData.bestStreak + "%252F7") : userYearEndData.bestStreak
            ret.push({
                imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_999:${streakText},g_north_west,y_1400,x_2600,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/streak.png`,
                shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_999:${streakText},g_north_west,y_1400,x_2600,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/streak_background.png`,
                preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_3_non_looping.json`,
                postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_3_looping.json`,
                theme: "LIGHT",
                color: "#E51C6D",
                leftCTAText: "SHARE",
                rightCTAText: "NEXT",
                shareIconUrl: "/image/icons/cult/share_white.png"
            })
        }

        if (userYearEndData.workoutList && userYearEndData.formatsTried > 0) {
            const formatsTried: string[] = userYearEndData.workoutList.split(",").slice(0, 5)
            if (userYearEndData.formatsTried > 0) {
                const formatsLength = userYearEndData.formatsTried < 10 ? "0" + userYearEndData.formatsTried : userYearEndData.formatsTried
                let url = `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_9999:${formatsLength},g_north_west,y_1600,x_850,co_rgb:fdfdfd`
                for (let i = 0; i < formatsTried.length; i++) {
                    url += `/l_text:fonts:BrandonText-Black.otf_2250:${formatsTried[i].replace(" ", "")},g_north_west,y_${1500 + 500 * i},x_3000,co_rgb:fdfdfd`
                }
                if (userYearEndData.formatsTried > 5) {
                    url += `/l_text:fonts:BrandonText-Black.otf_2250:${"and more"},g_north_west,y_4000,x_3000,co_rgb:fdfdfd`
                }
                ret.push({
                    imageUrl: url + `/w_750,ar_0.5625/image/userReport/formats.png`,
                    shareableImageUrl: url + `/w_750,ar_0.5625/image/userReport/formats_background.png`,
                    preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_4_1_non_looping.json`,
                    postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_4_1_looping.json`,
                    theme: "LIGHT",
                    color: "#000000",
                    leftCTAText: "SHARE",
                    rightCTAText: "NEXT",
                    shareIconUrl: "/image/icons/cult/share_white.png"
                })
            }
        }

        if (userYearEndData.favWorkout && userYearEndData.favWorkoutMins) {
            const formatImage = FAV_WORKOUT_LIST.includes(userYearEndData.favWorkout.toUpperCase()) ? userYearEndData.favWorkout : "HRX"
            const episodeCount = Math.floor(userYearEndData.favWorkoutMins / 25)
            ret.push({
                imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_650:${userYearEndData.favWorkout},g_north_west,y_1050,x_650,co_rgb:fdfdfd` +
                                                                                    `/l_text:fonts:BrandonText-Black.otf_650:${parseFloat(userYearEndData.favWorkoutMins.toString()).toFixed()},g_north_west,y_2050,x_650,co_rgb:fdfdfd` +
                                                                                    `/l_text:fonts:BrandonText-Black.otf_200:${episodeCount},g_north_west,y_3750,x_1225,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/fav_format_${formatImage.toLowerCase()}.png`,
                shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_650:${userYearEndData.favWorkout},g_north_west,y_1050,x_650,co_rgb:fdfdfd` +
                                                                                    `/l_text:fonts:BrandonText-Black.otf_650:${parseFloat(userYearEndData.favWorkoutMins.toString()).toFixed()},g_north_west,y_2050,x_650,co_rgb:fdfdfd` +
                                                                                    `/l_text:fonts:BrandonText-Black.otf_200:${episodeCount},g_north_west,y_3750,x_1225,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/fav_format_${formatImage.toLowerCase()}_background.png`,
                preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_4_2_non_looping.json`,
                postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_4_2_looping.json`,
                theme: "LIGHT",
                color: "#000000",
                leftCTAText: "SHARE",
                rightCTAText: "NEXT",
                shareIconUrl: "/image/icons/cult/share_white.png"
            })
        }


        if (userYearEndData.buddies) {
            const numFlights = 1 + Math.floor(userYearEndData.buddies / 660)
            ret.push({
                imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_850:${userYearEndData.buddies},g_north_west,y_1650,x_650,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_210:${numFlights},g_north_west,y_3275,x_2300,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/friends.png`,
                shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_850:${userYearEndData.buddies},g_north_west,y_1650,x_650,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_210:${numFlights},g_north_west,y_3275,x_2300,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/friends_background.png`,
                preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_5_non_looping.json`,
                postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_5_looping.json`,
                theme: "LIGHT",
                color: "#000000",
                leftCTAText: "SHARE",
                rightCTAText: "NEXT",
                shareIconUrl: "/image/icons/cult/share_white.png"
            })
        }

        if (userYearEndData.bestRank > 0 && userYearEndData.bestScore > 0) {
            ret.push({
                imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_225:${firstName},g_north_west,y_725,x_1175,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_550:${userYearEndData.bestScore},g_north_west,y_1750,x_650,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_900:${userYearEndData.bestRank},g_north_west,y_2700,x_700,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/rank.png`,
                shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_225:${firstName},g_north_west,y_725,x_1175,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_550:${userYearEndData.bestScore},g_north_west,y_1750,x_650,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_900:${userYearEndData.bestRank},g_north_west,y_2700,x_700,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/rank_background.png`,
                preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_6_non_looping.json`,
                postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_6_looping.json`,
                theme: "DARK",
                color: "#00B4EC",
                leftCTAText: "SHARE",
                rightCTAText: "NEXT",
                shareIconUrl: "/image/icons/cult/share_black.png"
            })
        }

        if (userYearEndData.percentile) {
            ret.push({
                imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_975:${parseFloat(userYearEndData.percentile.toString()).toFixed()}%25,g_north_west,y_1400,x_650,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/percentile.png`,
                shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_975:${parseFloat(userYearEndData.percentile.toString()).toFixed()}%25,g_north_west,y_1400,x_650,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/percentile_background.png`,
                preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_7_non_looping.json`,
                postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_7_looping.json`,
                theme: "LIGHT",
                color: "#000000",
                leftCTAText: "SHARE",
                rightCTAText: "NEXT",
                shareIconUrl: "/image/icons/cult/share_white.png"
            })
        }

        if (images.length > 1) {
            ret.push({
                imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/${this.getFileNameForPicture(images[0])}/fl_layer_apply,q_auto:eco,c_fit,f_auto,w_3000,g_north_west,y_1500,x_650/${this.getFileNameForPicture(images[1])}/fl_layer_apply,w_3000,q_auto:eco,c_fit,f_auto,g_south_west,y_2750,x_650/w_750,ar_0.5625/image/userReport/memories.png`,
                shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/${this.getFileNameForPicture(images[0])}/fl_layer_apply,q_auto:eco,c_fit,f_auto,w_3000,g_north_west,y_1500,x_650/${this.getFileNameForPicture(images[1])}/fl_layer_apply,w_3000,q_auto:eco,c_fit,f_auto,g_south_west,y_2750,x_650/w_750,ar_0.5625/image/userReport/memories_background.png`,
                preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_8_non_looping.json`,
                postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_8_looping.json`,
                theme: "DARK",
                color: "#FFD400",
                leftCTAText: "SHARE",
                rightCTAText: "NEXT",
                shareIconUrl: "/image/icons/cult/share_black.png"
            })
        }

        if (userYearEndData.caloriesBurnt) {
            ret.push({
                imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_850:${userYearEndData.caloriesBurnt},g_north_west,y_1950,x_600,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/calorie.png`,
                shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_850:${userYearEndData.caloriesBurnt},g_north_west,y_1950,x_600,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/calorie_background.png`,
                preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_9_1_non_looping.json`,
                postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_9_1_looping.json`,
                theme: "LIGHT",
                color: "#E51C6D",
                leftCTAText: "SHARE",
                rightCTAText: "NEXT",
                shareIconUrl: "/image/icons/cult/share_white.png"
            })
        }

        if (userYearEndData.caloriesBurnt && userYearEndData.minsWorked && userYearEndData.buddies && userYearEndData.favWorkout && userYearEndData.bestStreak) {
            ret.push({
                imageUrl: undefined,
                shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_350:${userYearEndData.minsWorked},g_north_west,y_2450,x_900` +
                                                                                    `/l_text:fonts:BrandonText-Black.otf_350:${userYearEndData.buddies},g_north_east,y_2450,x_1000,co_rgb:fdfdfd` +
                                                                                    `/l_text:fonts:BrandonText-Black.otf_350:${userYearEndData.bestStreak},g_south_east,y_3600,x_1200,co_rgb:fdfdfd` +
                                                                                    `/l_text:fonts:BrandonText-Black.otf_350:${userYearEndData.favWorkout},g_south_west,y_3600,x_700,co_rgb:fdfdfd` +
                                                                                    `/l_text:fonts:BrandonText-Black.otf_350:${userYearEndData.caloriesBurnt},g_south_east,y_2250,x_1200,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/reportcard_background.png`,
                preAnimationLotteType: undefined,
                postAnimationLotteType: undefined,
                theme: "LIGHT",
                color: "#000000",
                leftCTAText: "SHARE",
                rightCTAText: "NEXT",
                shareIconUrl: "/image/icons/cult/share_white.png"
            })
        }

        ret.push({
            imageUrl: undefined,
            shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_425:${firstName}:,g_north_west,y_1325,x_875,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/wrap_background.png`,
            preAnimationLotteType: undefined,
            postAnimationLotteType: undefined,
            theme: "DARK",
            color: "#00B4EC",
            leftCTAText: "SHARE",
            rightCTAText: "NEXT",
            shareIconUrl: "/image/icons/cult/share_black.png"
        })

        ret.push({
            imageUrl: undefined,
            shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/w_750,ar_0.5625/image/userReport/challenge_background.png`,
            preAnimationLotteType: undefined,
            postAnimationLotteType: undefined,
            theme: "LIGHT",
            color: "#E51C6D",
            action: {actionType: "NAVIGATION", url: process.env.K8S_ENV === "PRODUCTION" ? `curefit://hometab?pageId=hometab&widgetId=0cdb4726-8c8c-4e1e-b152-6c2e70bf0c95` : `curefit://hometab?pageId=hometab&widgetId=0cdb4726-8c8c-4e1e-b152-6c2e70bf0c95`},
            leftCTAText: "EXIT",
            rightCTAText: "PICK A GOAL"
        })


        return ret
    }

    private getGenericVersionResponse(): UserYearlyReport[] {
        const ret: UserYearlyReport[] = []
        const userYearEndData: UserYearEndReport = {
            userId: "0",
            favWorkout: "DANCE",
            classes: 26669652,
            minsWorked: 795964050,
            bestStreak: 350,
            formatsTried: 22,
            favWorkoutMins: 205280711,
            buddies: 1473184,
            caloriesBurnt: 4295511891,
            workoutList: "BOXING,SNC,HRX,DANCE,YOGA,PILATES,HIIT"
        }
        const userYearEndDataText = {
            favWorkout: "DANCE",
            classes: "26 Million",
            minsWorked: "800 Million",
            bestStreak: "350",
            formatsTried: "22",
            favWorkoutMins: "200 Million",
            buddies: "1.5 Million",
            caloriesBurnt: "4 Billion",
        }

        ret.push({
            imageUrl: "https://cdn-images.cure.fit/www-curefit-com/image/upload/w_750,ar_0.5625/image/userReport/generic/name.png",
            shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/w_750,ar_0.5625/image/userReport/generic/name_background.png`,
            preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_1_non_looping.json`,
            postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_1_looping.json`,
            theme: "LIGHT",
            color: "#000000",
            leftCTAText: "SHARE",
            rightCTAText: "NEXT",
            shareIconUrl: "/image/icons/cult/share_white.png"
        })

        ret.push({
            imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_750:26,g_north_west,y_1050,x_600/l_text:fonts:BrandonText-Black.otf_500:million,g_north_west,y_1250,x_1500/l_text:fonts:BrandonText-Black.otf_750:800,g_north_west,y_2550,x_600/l_text:fonts:BrandonText-Black.otf_500:million,g_north_west,y_2725,x_2050/w_750,ar_0.5625/image/userReport/generic/class.png`,
            shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_750:26,g_north_west,y_1050,x_600/l_text:fonts:BrandonText-Black.otf_500:million,g_north_west,y_1250,x_1500/l_text:fonts:BrandonText-Black.otf_750:800,g_north_west,y_2550,x_600/l_text:fonts:BrandonText-Black.otf_500:million,g_north_west,y_2725,x_2050/w_750,ar_0.5625/image/userReport/generic/class_background.png`,
            preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_2_non_looping.json`,
            postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_2_looping.json`,
            theme: "DARK",
            color: "#FFD400",
            leftCTAText: "SHARE",
            rightCTAText: "NEXT",
            shareIconUrl: "/image/icons/cult/share_black.png"
        })

        if (userYearEndDataText.bestStreak) {
            ret.push({
                imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_999:${userYearEndDataText.bestStreak},g_north_west,y_1400,x_2600,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/generic/streak.png`,
                shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_999:${userYearEndDataText.bestStreak},g_north_west,y_1400,x_2600,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/generic/streak_background.png`,
                preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_3_non_looping.json`,
                postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_3_looping.json`,
                theme: "LIGHT",
                color: "#E51C6D",
                leftCTAText: "SHARE",
                rightCTAText: "NEXT",
                shareIconUrl: "/image/icons/cult/share_white.png"
            })
        }

        if (userYearEndData.workoutList && userYearEndData.formatsTried > 0) {
            const formatsTried: string[] = userYearEndData.workoutList.split(",")
            if (userYearEndData.formatsTried > 0) {
                const formatsLength = userYearEndData.formatsTried < 10 ? "0" + userYearEndData.formatsTried : userYearEndData.formatsTried
                let url = `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_9999:${formatsLength},g_north_west,y_1600,x_850,co_rgb:fdfdfd`
                for (let i = 0; i < formatsTried.length; i++) {
                    url += `/l_text:fonts:BrandonText-Black.otf_2250:${formatsTried[i].replace(" ", "")},g_north_west,y_${1500 + 500 * i},x_3000,co_rgb:fdfdfd`
                }
                url += `/l_text:fonts:BrandonText-Black.otf_2250:${"and more"},g_north_west,y_5000,x_3000,co_rgb:fdfdfd`
                ret.push({
                    imageUrl: url + `/w_750,ar_0.5625/image/userReport/generic/formats.png`,
                    shareableImageUrl: url + `/w_750,ar_0.5625/image/userReport/generic/formats_background.png`,
                    preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_4_1_non_looping.json`,
                    postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_4_1_looping.json`,
                    theme: "LIGHT",
                    color: "#000000",
                    leftCTAText: "SHARE",
                    rightCTAText: "NEXT",
                    shareIconUrl: "/image/icons/cult/share_white.png"
                })
            }
        }

        ret.push({
            imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_650:DANCE,g_north_west,y_1050,x_650,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_600:200,g_north_west,y_2250,x_650,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_300:million,g_north_west,y_2450,x_1750,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/generic/fav_format.png`,
            shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_650:DANCE,g_north_west,y_1050,x_650,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_600:200,g_north_west,y_2250,x_650,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_300:million,g_north_west,y_2450,x_1750,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/generic/fav_format_background.png`,
            preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_4_2_non_looping.json`,
            postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_4_2_looping.json`,
            theme: "LIGHT",
            color: "#000000",
            leftCTAText: "SHARE",
            rightCTAText: "NEXT",
            shareIconUrl: "/image/icons/cult/share_white.png"
        })


        ret.push({
            imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_750:1.5,g_north_west,y_1750,x_650,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_550:million,g_north_west,y_1900,x_1650,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/generic/friends.png`,
            shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_750:1.5,g_north_west,y_1750,x_650,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_550:million,g_north_west,y_1900,x_1650,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/generic/friends_background.png`,
            preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_5_non_looping.json`,
            postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_5_looping.json`,
            theme: "LIGHT",
            color: "#000000",
            leftCTAText: "SHARE",
            rightCTAText: "NEXT",
            shareIconUrl: "/image/icons/cult/share_white.png"
        })

        ret.push({
            imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_850:4,g_north_west,y_1650,x_600,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_600:billion,g_north_west,y_1800,x_1200,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/generic/calorie.png`,
            shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_850:4,g_north_west,y_1650,x_600,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_600:billion,g_north_west,y_1800,x_1200,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/generic/calorie_background.png`,
            preAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_9_1_non_looping.json`,
            postAnimationLotteType: `https://cdn-media.cure.fit/image/userReport/animation_9_1_looping.json`,
            theme: "LIGHT",
            color: "#E51C6D",
            leftCTAText: "SHARE",
            rightCTAText: "NEXT",
            shareIconUrl: "/image/icons/cult/share_white.png"
        })

        ret.push({
            imageUrl: undefined,
            shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_350:800,g_north_west,y_2450,x_650` +
                `/l_text:fonts:BrandonText-Black.otf_200:million,g_north_west,y_2550,x_1325/l_text:fonts:BrandonText-Black.otf_350:1.4,g_north_east,y_2450,x_1300,co_rgb:fdfdfd` +
                `/l_text:fonts:BrandonText-Black.otf_200:million,g_north_east,y_2550,x_650,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_350:22,g_south_east,y_3620,x_1100,co_rgb:fdfdfd` +
                `/l_text:fonts:BrandonText-Black.otf_350:DANCE,g_south_west,y_3600,x_700,co_rgb:fdfdfd/l_text:fonts:BrandonText-Black.otf_350:4,g_south_east,y_2180,x_1790,co_rgb:fdfdfd` +
                `/l_text:fonts:BrandonText-Black.otf_200:billion,g_south_east,y_2200,x_1200,co_rgb:fdfdfd/w_750,ar_0.5625/image/userReport/generic/reportcard_background.png`,
            preAnimationLotteType: undefined,
            postAnimationLotteType: undefined,
            theme: "LIGHT",
            color: "#000000",
            leftCTAText: "SHARE",
            rightCTAText: "NEXT",
            shareIconUrl: "/image/icons/cult/share_white.png"
        })

        ret.push({
            imageUrl: undefined,
            shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/w_750,ar_0.5625/image/userReport/generic/wrap_background.png`,
            preAnimationLotteType: undefined,
            postAnimationLotteType: undefined,
            theme: "DARK",
            color: "#00B4EC",
            leftCTAText: "SHARE",
            rightCTAText: "NEXT",
            shareIconUrl: "/image/icons/cult/share_black.png"
        })

        ret.push({
            imageUrl: undefined,
            shareableImageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/w_750,ar_0.5625/image/userReport/generic/challenge_background.png`,
            preAnimationLotteType: undefined,
            postAnimationLotteType: undefined,
            theme: "LIGHT",
            color: "#E51C6D",
            action: {actionType: "NAVIGATION", url: process.env.K8S_ENV === "PRODUCTION" ? `curefit://hometab?pageId=hometab&widgetId=0cdb4726-8c8c-4e1e-b152-6c2e70bf0c95` : `curefit://hometab?pageId=hometab&widgetId=0cdb4726-8c8c-4e1e-b152-6c2e70bf0c95`},
            leftCTAText: "EXIT",
            rightCTAText: "PICK A GOAL"
        })


        return ret
    }

    private getFileNameForPicture(imageUrl: string): string {
        const parts: string[] = imageUrl.split("/")
        return `l_` + parts.join(":")
    }

}
