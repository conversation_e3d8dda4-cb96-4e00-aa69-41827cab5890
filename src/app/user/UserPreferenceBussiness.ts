
import {
    UserPreference
} from "@curefit/user-common"
import
IUserPreferenceBussiness
    from "./IUserPreferenceBussiness"
import { IUserPreferenceReadWriteDao } from "@curefit/user-models"
import { USER_MODELS_TYPES } from "@curefit/user-models"
import { inject, injectable } from "inversify"
import {
    VerticalType
} from "@curefit/location-common"
import {
    Session, SessionData,
} from "@curefit/userinfo-common"
import { UserContext } from "@curefit/userinfo-common"
import _ = require("lodash")
import { ISessionBusiness } from "@curefit/base-utils"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { ICrudKeyValue, IMultiCrudKeyValue, REDIS_TYPES } from "@curefit/redis-utils"
import { TimeUtil } from "@curefit/util-common"
import { BASE_TYPES, Logger } from "@curefit/base"
import AuthUtil from "../util/AuthUtil"

@injectable()
class UserPreferenceBussiness implements IUserPreferenceBussiness {
    private crudDao: ICrudKeyValue

    constructor(
        @inject(USER_MODELS_TYPES.UserPreferenceReadWriteDao) private userPreferenceDao: IUserPreferenceReadWriteDao,
        @inject(CUREFIT_API_TYPES.SessionService) private sessionBusiness: ISessionBusiness,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(REDIS_TYPES.MultiCrudKeyValueDao) private multiCrudKeyValueDao: IMultiCrudKeyValue,
    ) {
        this.crudDao = multiCrudKeyValueDao.getICrudKeyValue("CFAPI-CACHE")
    }

    async getUserPreference(userContext: UserContext): Promise<UserPreference> {
        const userId = userContext.userProfile.userId
        let userPreference
        if (AuthUtil.isFixedGuestUser(userContext)) {
            userPreference =  this.getUserPreferenceGuestUser(userContext.sessionInfo.sessionData)
        } else {
            userPreference =  await  this.getUserPreferenceForLoggedInUser(userId)
        }
        if (!userPreference) {
            userPreference = {
                userId: userId,
                cfAPI: {
                    verticals: [],
                    userInteractedVerticals: [],
                    isMoreInteracted: false
                }
            }
        } else if (!userPreference.cfAPI) {
            userPreference.cfAPI = {
                verticals: [],
                userInteractedVerticals: [],
                isMoreInteracted: false
            }
        }
        return userPreference
    }

    private async getUserPreferenceForLoggedInUser(userId: string): Promise<UserPreference> {
        const userPreference = await this.userPreferenceDao.findOne({ userId: userId })
        return userPreference
    }

     getUserPreferenceGuestUser(sessionData: SessionData): UserPreference {
        const userPreference = _.get(sessionData, "userPreference")
        return userPreference
    }

    async createOrUpdateUserPreference(userContext: UserContext, userPreference: UserPreference) {
        if (AuthUtil.isFixedGuestUser(userContext)) {
            return this.createOrUpdateUserPreferenceGuestUser(userContext, userPreference)
        }  else {
            return this.createOrUpdateUserPreferenceForLoggedInUser(userPreference)
        }
    }

    private async createOrUpdateUserPreferenceGuestUser(userContext: UserContext, userPreference: UserPreference): Promise<UserPreference> {
        let sessionData = userContext.sessionInfo.sessionData
        sessionData = {
            ...sessionData,
            userPreference
        }
        await this.sessionBusiness.updateSessionData(userContext.sessionInfo.at, sessionData, true)
        return userPreference
    }

    private async createOrUpdateUserPreferenceForLoggedInUser(userPreference: UserPreference): Promise<UserPreference> {
        const userId = userPreference.userId
        this.logger.info(`createOrUpdateUserPreferenceForLoggedInUser: userId ${userId} and userPreference.userId ${userPreference.userId}`)
        await this.crudDao.upsertWithExpiry(this._getUserPreferenceRedisKey(userPreference.userId), JSON.stringify(userPreference), TimeUtil.TIME_IN_SECONDS.DAY * 30)
        const existingPref = await this.userPreferenceDao.findOne({ userId: userId })
        if (existingPref) {
            this.logger.info(`createOrUpdateUserPreferenceForLoggedInUser: existingPref ${userId}`)
        }
        return await this.userPreferenceDao.findOneAndUpdate({ userId: userPreference.userId }, userPreference, { upsert: true })
    }

    async updateSelectedVerticals(selectedVerticals: VerticalType[], userContext: UserContext): Promise<UserPreference> {
        const userPreference: UserPreference = await this.getUserPreference(userContext)
        userPreference.cfAPI.verticals = selectedVerticals
        await this.createOrUpdateUserPreference(userContext, userPreference)
        return userPreference
    }

    private _getUserPreferenceRedisKey(userId: string) {
        return `cfapi:userPreference:${userId}`
    }

}

export default UserPreferenceBussiness
