import { inject, injectable } from "inversify"
import { DEFAULT_CACHE_REFRESH_TIME_IN_SECONDS, InMemoryCacheService } from "@curefit/memory-cache"
import { IPageConfigReadOnlyDao, IPageConfigReadWriteDao, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import { MePageSideBarSection } from "@curefit/vm-common"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

@injectable()
class MePageSideBarPageConfig extends InMemoryCacheService<any> {

    constructor(
        @inject(PAGE_CONFIG_TYPES.PageConfigReadOnlySecondaryDao) protected pageConfigDao: IPageConfigReadOnlyDao,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger, 34 * 60)
        this.load("MePageSideBarPageConfig")
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    loadData(): Promise<any> {
        return this.pageConfigDao.findOne({ pageId: { $eq: "MePageSideBarConfig" } }).then(pageConfig => {
            const data = pageConfig.data
            this.profile = data.profile
            this.orders = data.orders
            this.medical = data.medical
            this.packs = data.packs
            this.coupons = data.coupons
            this.account = data.account
            this.support = data.support
            this.logout = data.logOut
            return data
        })
    }

    public profile: MePageSideBarSection
    public orders: MePageSideBarSection
    public medical: MePageSideBarSection
    public packs: MePageSideBarSection
    public account: MePageSideBarSection
    public coupons: MePageSideBarSection
    public support: MePageSideBarSection
    public logout: MePageSideBarSection


    public getMePageResponse(careMappedActions: any []): MePageSideBarSection []  {
        const response =  [
            this.profile,
            this.orders,
        ]
        if (careMappedActions.length) {
            response.push(this.medical)
        }
        response.push(this.packs)
        if (this.coupons) {
            response.push(this.coupons)
        }
        response.push(this.account)
        response.push(this.support)
        response.push(this.logout)
        return response
    }
}

export default MePageSideBarPageConfig
