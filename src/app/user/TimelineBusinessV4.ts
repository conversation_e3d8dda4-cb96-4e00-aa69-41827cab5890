import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import * as _ from "lodash"
import { Logger, BASE_TYPES } from "@curefit/base"
import ITimelineBusiness, { TimelineRequestParams } from "./ITimelineBusiness"
import { ILoggingService, LOGGING_CLIENT_TYPES } from "@curefit/logging-client"
import { UserContext } from "@curefit/userinfo-common"
import { TimelineViewV1 } from "./TimelineView"
import { STEPS_GOAL } from "./TimelineBusinessV1"
import { ActivityDS } from "@curefit/logging-common"
import { UserActivityDetails } from "@curefit/quest-common"
import { EmptyStateWidget, WidgetType, WidgetView } from "../common/views/WidgetView"
import {
    ILoggedActivityWidget,
    ILoggedActivityWidgetItem,
    sortLoggedActivityWidgets
} from "../common/views/LoggedActivityWidget"
import { ICatalogueService, CATALOG_CLIENT_TYPES } from "@curefit/catalog-client"
import { FoodBooking } from "@curefit/shipment-common"
import IProductBusiness from "../product/IProductBusiness"
import { ActivityState, } from "@curefit/logging-common"
import { MealSlot } from "@curefit/eat-common"
import { FoodProduct as Product } from "@curefit/eat-common"
import { ProductType } from "@curefit/product-common"
import ICRMIssueService from "../crm/ICRMIssueService"
import { NotificationWidget } from "../common/views/NotificationWidget"
import { IQuestService, QUEST_CLIENT_TYPES } from "@curefit/quest-client"
import { TimeUtil, Timezone } from "@curefit/util-common"
import {
    GoalCardView,
    IMetricWidgetBuildParams,
    MetricsWidget,
    ScoreMetricsWidget
} from "../common/views/MetricsWidget"
import { BulkBookingResponse, CultBooking, CultWorkout } from "@curefit/cult-common"
import { ICultServiceOld as ICultService, CULT_CLIENT_TYPES } from "@curefit/cult-client"
import { CultMindCarouselWidget } from "../common/views/CultMindCarouselWidget"
import TimelineUtil from "../util/TimelineUtil"
import { GMSCarouselWidgetBuilder } from "../common/views/GMSCarouselWidgetBuilder"
import { ICultBusiness } from "../cult/CultBusiness"
import { CatalogueProductType } from "@curefit/maverick-common"
import { IDIYFulfilmentService, DIY_CLIENT_TYPES } from "@curefit/diy-client"
import { DIYUserDay } from "@curefit/diy-common"
import { DiyCarouselWidget } from "../common/views/DiyCarouselWidget"
import {
    IHealthfaceService,
    RecommendedTimelineActivity,
    TimelineActivity,
    TimelineResponse,
    ALBUS_CLIENT_TYPES
} from "@curefit/albus-client"
import { CareFitCarouselWidgetBuilder } from "../common/views/CareFitCarouselWidgetBuilder"
import { ITodoActivityWidget, sortTodoWidgets } from "../common/views/TodoActivityWidget"
import { FoodLoggedActivityWidget } from "../common/views/FoodLoggedActivityWidget"
import { ActivityLoggingUtil } from "../util/ActivityLoggingUtil"
import { StepsCarouselWidget } from "../common/views/StepsCarouselWidget"
import { FitnessLoggedActivityWidget } from "../common/views/FitnessLoggedActivityWidget"
import { MeditationLoggedActivityWidget } from "../common/views/MeditationLoggedActivityWidget"
import { CareLoggedActivityWidget } from "../common/views/CareLoggedActivityWidget"
import { IActivityLoggingBusiness } from "../logging/ActivityLoggingBusiness"
import AppUtil from "../util/AppUtil"
import { IUserService } from "@curefit/user-client"
import { SlotUtil } from "@curefit/eat-util"
import { CacheHelper } from "../util/CacheHelper"
import { IMetricServiceClient as IMetricService, METRIC_TYPES } from "@curefit/metrics"
import CultUtil from "../util/CultUtil"
import { GMF_CLIENT_TYPES, IGMFClient } from "@curefit/gmf-client"
import { ProductRecommendation, TodoPlanActivityView } from "@curefit/gmf-common"
import { IRecommendationService, RECOMMENDATION_CLIENT_TYPES } from "@curefit/recommendation-client"
import { AddActivitiesWidget, IAddActivityParams } from "../common/views/AddActivitiesWidget"
import { RollbarService, ERROR_COMMON_TYPES } from "@curefit/error-common"
import { Meal } from "@curefit/food-common"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { GenericError } from "@curefit/error-client"
import { AnnouncementBusiness } from "../announcement/AnnouncementBusiness"
import { User } from "@curefit/user-common"
import { KiosksDemandService, MASTERCHEF_CLIENT_TYPES } from "@curefit/masterchef-client"
import { Kiosk } from "@curefit/eat-common"
import { IFulfilmentService, ALFRED_CLIENT_TYPES } from "@curefit/alfred-client"
import { StartEndTime } from "@curefit/base-common"
import { USER_CLIENT_TYPES } from "@curefit/user-client"
import { AtlasActivityService } from "../atlas/AtlasActivityService"
import { InAppNotificationsService, IRIS_CLIENT_TYPES } from "@curefit/iris-client"


const TODO_CONTAINER_META = {
    title: "Todo",
    sectionViewHide: true
}

const PENDING_CONTAINER_META = {
    title: "Pending",
    sectionViewHide: true
}

const LOGGED_CONTAINER_META = {
    title: "Logged",
    sectionViewHide: true
}

@injectable()
export class TimelineBusinessV4 implements ITimelineBusiness {

    constructor(
        @inject(IRIS_CLIENT_TYPES.InAppNotificationsService) protected inAppNotificationService: InAppNotificationsService,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(LOGGING_CLIENT_TYPES.LoggingService) private loggingService: ILoggingService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(CUREFIT_API_TYPES.ProductBusiness) private productBusiness: IProductBusiness,
        @inject(CUREFIT_API_TYPES.ActivityLoggingBusiness) private activityLoggingBusiness: IActivityLoggingBusiness,
        @inject(CUREFIT_API_TYPES.CRMIssueService) private CRMIssueService: ICRMIssueService,
        @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
        @inject(LOCATION_TYPES.CityService) private cityService: ICityService,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
        @inject(QUEST_CLIENT_TYPES.IQuestService) private questService: IQuestService,
        @inject(GMF_CLIENT_TYPES.IGMFClient) private gmfClient: IGMFClient,
        @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
        @inject(CULT_CLIENT_TYPES.MindFitService) private mindFitService: ICultService,
        @inject(RECOMMENDATION_CLIENT_TYPES.IRecommendationService) private recommendationService: IRecommendationService,
        @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
        @inject(ALBUS_CLIENT_TYPES.CultPersonalTrainingService) private cultPTService: IHealthfaceService,
        @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) protected DIYFulfilmentService: IDIYFulfilmentService,
        @inject(CUREFIT_API_TYPES.CultBusiness) private cultBusiness: ICultBusiness,
        @inject(METRIC_TYPES.MetricServiceClient) private metricService: IMetricService,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(CUREFIT_API_TYPES.AnnouncementBusiness) private announcementBusiness: AnnouncementBusiness,
        @inject(ALFRED_CLIENT_TYPES.FulfilmentService) private fulfilmentService: IFulfilmentService,
        @inject(MASTERCHEF_CLIENT_TYPES.KiosksDemandService) private kiosksDemandService: KiosksDemandService,
        @inject(CUREFIT_API_TYPES.AtlasActivityService) private atlasActivityService: AtlasActivityService,
    ) {
    }

    public async getTimeLine(timelineRequestParams: TimelineRequestParams, userContext: UserContext): Promise<TimelineViewV1> {
        const timelineView: TimelineViewV1 = {
            stepsGoal: STEPS_GOAL,
            dateMap: {}
        }
        this.constructDateMapSkeleton(timelineView, timelineRequestParams)
        const userPromise = this.userCache.getUser(userContext.userProfile.userId)
        const widgetPromiseList: Promise<void>[] = []
        const timezone = userContext.userProfile.timezone
        widgetPromiseList.push(this.addLoggedActivityWidgets(timelineView, timelineRequestParams, userContext, userPromise))
        widgetPromiseList.push(this.addNotificationWidget(timelineView, timelineRequestParams))
        widgetPromiseList.push(this.addCultMindFutureClassWidgets(timelineView, timelineRequestParams, userContext))
        widgetPromiseList.push(this.addCultPersonalTrainingWidgets(timelineView, timelineRequestParams, userContext, userPromise))
        widgetPromiseList.push(this.addGMSWidgets(timelineView, timelineRequestParams, userContext, userPromise))
        widgetPromiseList.push(this.addDIYWidgets(timelineView, timelineRequestParams, userContext))
        widgetPromiseList.push(this.addCareFitWidgets(timelineView, timelineRequestParams, userContext, userPromise))
        if (timelineRequestParams.metrics) widgetPromiseList.push(this.addMetricsWidget(timelineView, timelineRequestParams, userContext, userPromise))
        if (timelineRequestParams.showaddoption) widgetPromiseList.push(this.getAddOptionsWidget(timelineView, timelineRequestParams, userContext, userPromise))
        await Promise.all(widgetPromiseList)
        this.cafeTimeline(timelineView)
        await this.noActivityWidget(timelineView, timelineRequestParams)
        this.dedupeFoodWidgets(timelineView, timezone)
        this.hideEmptyContainers(timelineView)
        this.sortWidgets(userContext, timelineView)
        return timelineView
    }

    private constructDateMapSkeleton(timelineView: TimelineViewV1, timelineRequestParams: TimelineRequestParams): void {
        const tz = timelineRequestParams.userContext.userProfile.timezone
        const allDates: string[] = TimeUtil.datesBetween(tz, timelineRequestParams.startDate, timelineRequestParams.endDate)
        allDates.forEach((dateStr: string) => {
            this.putWidgetIntoView(undefined, timelineView, dateStr, "TODO_ACTIVITIES_CONTAINER_WIDGET", TODO_CONTAINER_META)
            this.putWidgetIntoView(undefined, timelineView, dateStr, "PENDING_ACTIVITIES_CONTAINER_WIDGET", PENDING_CONTAINER_META)
            this.putWidgetIntoView(undefined, timelineView, dateStr, "LOGGED_ACTIVITIES_CONTAINER_WIDGET", LOGGED_CONTAINER_META)
        })
    }

    private async addDIYWidgets(timelineView: TimelineViewV1, timelineRequestParams: TimelineRequestParams, userContext: UserContext): Promise<void> {
        const tz = timelineRequestParams.userContext.userProfile.timezone
        const futureDatesArray: string[] = TimeUtil.futureDatesWithinRange(tz, timelineRequestParams.startDate, timelineRequestParams.endDate)
        // Check if dates are in past
        if (futureDatesArray.length === 0) return
        let diyUserDays: DIYUserDay[]
        try {
            diyUserDays = await this.DIYFulfilmentService.getDIYCalendarForUser(userContext.userProfile.userId, AppUtil.getTenantFromUserContext(userContext), _.head(futureDatesArray), _.last(futureDatesArray), false)
        } catch (e) {
            this.logger.error("Error contacting DIY Service", e)
            this.rollbarService.handleError(e)
            return
        }

        const widgetBuildPromises: {
            widgetPromise: Promise<WidgetView>,
            date: string
        }[] = []
        diyUserDays.forEach((diyUserDay: DIYUserDay) => {
            const date = TimeUtil.formatDateInTimeZone(tz, diyUserDay.date)
            const preferredTime = diyUserDay.preferredTime
            const widget: DiyCarouselWidget = new DiyCarouselWidget()
            widgetBuildPromises.push({
                widgetPromise: widget.buildView({
                    date: date,
                    packId: diyUserDay.packId,
                    productId: diyUserDay.productId,
                    status: diyUserDay.status,
                    catalogueService: this.catalogueService,
                    preferredTime: preferredTime,
                    userContext: userContext,
                    logger: this.logger,
                    diyService: this.DIYFulfilmentService
                }),
                date: date
            })
        })
        for (const widgetBuildData of widgetBuildPromises) {
            const widget: ITodoActivityWidget = <ITodoActivityWidget>await widgetBuildData.widgetPromise
            if (!_.isNil(widget) && widget.status === "TODO") {
                this.putWidgetIntoView(widget, timelineView, widgetBuildData.date, "TODO_ACTIVITIES_CONTAINER_WIDGET")
            }
        }
    }

    private async addCultPersonalTrainingWidgets(timelineView: TimelineViewV1, timelineRequestParams: TimelineRequestParams, userContext: UserContext, userPromise: Promise<User>): Promise<void> {
        const tz = timelineRequestParams.userContext.userProfile.timezone
        const futureDatesArray: string[] = TimeUtil.futureDatesWithinRange(tz, timelineRequestParams.startDate, timelineRequestParams.endDate)
        // Check if dates are in past
        if (futureDatesArray.length === 0) return
        const today: string = TimeUtil.todaysDate(tz)
        const isTodayPresent: string = _.find(futureDatesArray, (date: string) => {
            return date === today
        })
        let cultPTTimelineActivities: TimelineResponse
        try {
            cultPTTimelineActivities = await this.cultPTService.getTimelineActivities(userContext, userContext.userProfile.userId,
                TimeUtil.getDate(_.head(futureDatesArray), 0, 0, tz).getTime(), futureDatesArray.length)
        } catch (e) {
            this.logger.error("Error contacting cultPTService", e)
            this.rollbarService.handleError(e)
            return
        }
        if (_.isNil(cultPTTimelineActivities) || _.isEmpty(cultPTTimelineActivities.timelineActivities)) {
            return
        }
        const user: User = await userPromise

        const widgetBuildPromises: {
            widgetPromise: Promise<WidgetView>,
            date: string
        }[] = []
        const cultPTWidgetBuilder: CareFitCarouselWidgetBuilder = new CareFitCarouselWidgetBuilder()
        cultPTTimelineActivities.timelineActivities.forEach((careFitTimelineActivity: TimelineActivity) => {
            const date = TimeUtil.formatDateInTimeZone(tz, new Date(careFitTimelineActivity.timestamp))
            widgetBuildPromises.push({
                widgetPromise: cultPTWidgetBuilder.buildCareFitWidget({
                    timelineActivity: careFitTimelineActivity,
                    issuesMap: undefined,
                    healthfaceService: this.cultPTService,
                    productBusiness: this.productBusiness,
                    user: user,
                    userContext: userContext,
                    logger: this.logger,
                    isRecommendedActivity: false
                }),
                date: date
            })
        })
        for (const widgetBuildData of widgetBuildPromises) {
            const widget: ITodoActivityWidget = <ITodoActivityWidget>await widgetBuildData.widgetPromise
            if (!_.isNil(widget) && widget.status === "TODO") {
                this.putWidgetIntoView(widget, timelineView, widgetBuildData.date, "TODO_ACTIVITIES_CONTAINER_WIDGET")
            }
        }
    }

    private async addCareFitWidgets(timelineView: TimelineViewV1, timelineRequestParams: TimelineRequestParams, userContext: UserContext, userPromise: Promise<User>): Promise<void> {
        const tz = timelineRequestParams.userContext.userProfile.timezone
        const futureDatesArray: string[] = TimeUtil.futureDatesWithinRange(tz, timelineRequestParams.startDate, timelineRequestParams.endDate)
        // Check if dates are in past
        if (futureDatesArray.length === 0) return
        const today: string = TimeUtil.todaysDate(tz)
        const isTodayPresent: string = _.find(futureDatesArray, (date: string) => {
            return date === today
        })
        let careTimelineActivities: TimelineResponse
        try {
            careTimelineActivities = await this.healthfaceService.getTimelineActivities(userContext, userContext.userProfile.userId,
                TimeUtil.getDate(_.head(futureDatesArray), 0, 0, tz).getTime(), futureDatesArray.length)
        } catch (e) {
            this.logger.error("Error contacting healthface service", e)
            this.rollbarService.handleError(e)
            return
        }
        if (_.isNil(careTimelineActivities) || _.isEmpty(careTimelineActivities.timelineActivities)) {
            return
        }
        const user: User = await userPromise

        const widgetBuildPromises: {
            widgetPromise: Promise<WidgetView>,
            date: string
        }[] = []
        const carefitWidgetBuilder: CareFitCarouselWidgetBuilder = new CareFitCarouselWidgetBuilder()
        careTimelineActivities.timelineActivities.forEach((careFitTimelineActivity: TimelineActivity) => {
            const date = TimeUtil.formatDateInTimeZone(tz, new Date(careFitTimelineActivity.timestamp))
            widgetBuildPromises.push({
                widgetPromise: carefitWidgetBuilder.buildCareFitWidget({
                    timelineActivity: careFitTimelineActivity,
                    issuesMap: undefined,
                    healthfaceService: this.healthfaceService,
                    productBusiness: this.productBusiness,
                    user: user,
                    userContext: userContext,
                    logger: this.logger,
                    isRecommendedActivity: false
                }),
                date: date
            })
        })
        for (const widgetBuildData of widgetBuildPromises) {
            const widget: ITodoActivityWidget = <ITodoActivityWidget>await widgetBuildData.widgetPromise
            if (!_.isNil(widget) && widget.status === "TODO") {
                this.putWidgetIntoView(widget, timelineView, widgetBuildData.date, "TODO_ACTIVITIES_CONTAINER_WIDGET")
            }
        }
    }

    private async addMetricsWidget(timelineView: TimelineViewV1, timelineRequestParams: TimelineRequestParams,
        userContext: UserContext, userPromise: Promise<User>): Promise<void> {
        const scoreMetricWidget: ScoreMetricsWidget = await this.getScoreMetricsWidget(timelineRequestParams, userContext)
        if (AppUtil.isCombinedGoalsAndScoreMetricsSupported(userContext)) {
            return this.addGoalsAndScoreMetricsWidget(timelineView, timelineRequestParams, userContext, userPromise, scoreMetricWidget)
        } else {
            timelineView.metrics = scoreMetricWidget
        }
    }


    private async getAddOptionsWidget(timelineView: TimelineViewV1, timelineRequestParams: TimelineRequestParams, userContext: UserContext, userPromise: Promise<User>): Promise<void> {
        const user: User = await userPromise
        const addAcivitiesWidget: AddActivitiesWidget = new AddActivitiesWidget()
        const buildParams: IAddActivityParams = {
            gmfClient: this.gmfClient,
            user: user,
            userContext: userContext,
            cityService: this.cityService,
            cacheHelper: this.userCache
        }
        await addAcivitiesWidget.buildView(buildParams)
        timelineView.addOptionWidget = addAcivitiesWidget
    }

    private async addGoalsAndScoreMetricsWidget(timelineView: TimelineViewV1, timelineRequestParams: TimelineRequestParams, userContext: UserContext, userPromise: Promise<User>, scoreMetricWidget: ScoreMetricsWidget): Promise<void> {
        try {
            const goalMetrics: MetricsWidget = new MetricsWidget()
            const metricBuildParams: IMetricWidgetBuildParams = {
                userContext: userContext,
                scoreMetric: scoreMetricWidget,
                gmfClient: this.gmfClient,
                metricService: this.metricService,
                logger: this.logger
            }
            await goalMetrics.buildView(metricBuildParams)
            const scorecard: GoalCardView = !_.isEmpty(goalMetrics.cards) && goalMetrics.cards.length > 1 && goalMetrics.cards[0]
            scorecard && (scorecard.viewType = "SMALL") && (scorecard.header = "LEVEL")
            timelineView.metrics = goalMetrics
        } catch (e) {
            this.logger.error("Error while trying to create metrics widget", e)
            this.rollbarService.handleError(e)
        }
    }

    private async getScoreMetricsWidget(timelineRequestParams: TimelineRequestParams, userContext: UserContext): Promise<ScoreMetricsWidget> {
        const userActivityDetailsPromise = this.getUserDetailedStatsPromise(timelineRequestParams, userContext)
        const scoreMetricWidget: ScoreMetricsWidget = new ScoreMetricsWidget()
        const userActivityDetails: UserActivityDetails = await userActivityDetailsPromise
        if (!_.isNil(userActivityDetails)) {
            scoreMetricWidget.buildScoreView(userActivityDetails)
            return scoreMetricWidget
        }
    }

    private async getUserDetailedStatsPromise(timelineRequestParams: TimelineRequestParams, userContext: UserContext): Promise<UserActivityDetails> {
        try {
            const tz = userContext.userProfile.timezone
            const today = TimeUtil.todaysDate(tz)
            const todayMinus30Days = TimeUtil.getMomentNow(tz).subtract(29, "day").format("YYYY-MM-DD")
            return await this.questService.getUserDetailedStats(timelineRequestParams.userId, todayMinus30Days, today)
        } catch (e) {
            this.logger.error("Error while trying to contact quest Service", e)
            this.rollbarService.handleError(e)
            return
        }
    }

    private async addLoggedActivityWidgets(timelineView: TimelineViewV1, timelineRequestParams: TimelineRequestParams, userContext: UserContext, userPromise: Promise<User>): Promise<void> {
        const user: User = await userPromise
        const tz = userContext.userProfile.timezone
        const subUserIds = user.subUserRelations && user.subUserRelations.map(subUserRelation => {
            return subUserRelation.subUserId
        }) || []
        const allUserIds = [user.id, ...subUserIds]
        let date = [timelineRequestParams.startDate]
        if (timelineRequestParams.endDate) {
            date = TimeUtil.datesBetween(tz, timelineRequestParams.startDate, timelineRequestParams.endDate)
        }
        const loggedActivitiesPromise: Promise<{ activities: ActivityDS[] }> = this.loggingService.getActivitiesFor({
            userId: allUserIds,
            date: date,
            show: [true]
        })
        let loggedActivities: { activities: ActivityDS[] }
        try {
            loggedActivities = await loggedActivitiesPromise
        } catch (e) {
            this.logger.error("Error while trying to contact logging service", e)
            this.rollbarService.handleError(e)
            return
        }

        // Group all activities by category
        const loggedActivitiesMap: Map<string, ActivityDS[]> = new Map<string, ActivityDS[]>()
        loggedActivitiesMap.set("FOOD", [])
        loggedActivitiesMap.set("FITNESS", [])
        loggedActivitiesMap.set("MEDITATION", [])
        loggedActivitiesMap.set("CARE", [])

        loggedActivities.activities.forEach((loggedActivity: ActivityDS) => {
            if (ActivityLoggingUtil.isFoodActivity(loggedActivity)) {
                loggedActivitiesMap.get("FOOD").push(loggedActivity)
            } else if (ActivityLoggingUtil.isFitnessActivity(loggedActivity)) {
                loggedActivitiesMap.get("FITNESS").push(loggedActivity)
            } else if (ActivityLoggingUtil.isMeditationActivity(loggedActivity)) {
                loggedActivitiesMap.get("MEDITATION").push(loggedActivity)
            } else if (ActivityLoggingUtil.isCareActivity(loggedActivity)) {
                loggedActivitiesMap.get("CARE").push(loggedActivity)
            }
        })

        // Create widgets for each of these logged activities
        const widgetPromiseList: Promise<void>[] = []
        widgetPromiseList.push(this.addFoodLoggedActivitiesWidgets(timelineView, loggedActivitiesMap.get("FOOD"), userContext))
        widgetPromiseList.push(this.addFitnessLoggedActivitiesWidget(timelineView, loggedActivitiesMap.get("FITNESS"), userContext))
        widgetPromiseList.push(this.addMeditationLoggedActivitiesWidget(timelineView, loggedActivitiesMap.get("MEDITATION"), userContext, timelineRequestParams))
        widgetPromiseList.push(this.addCareLoggedActivitiesWidget(timelineView, loggedActivitiesMap.get("CARE"), userContext))

        await Promise.all(widgetPromiseList)
    }

    private async addNotificationWidget(timelineView: TimelineViewV1, timelineRequestParams: TimelineRequestParams): Promise<void> {
        const tz = timelineRequestParams.userContext.userProfile.timezone
        const today: string = TimeUtil.todaysDate(tz)
        const dateMap: { [date: string]: WidgetView[] } = timelineView.dateMap
        if (!_.isNil(dateMap[today])) {
            let notificationWidget: NotificationWidget = new NotificationWidget()
            notificationWidget = await notificationWidget.buildView({
                canAskFitbit: timelineRequestParams.canAskFitBit,
                canAskHealthKit: timelineRequestParams.canAskHealthKit,
                userId: timelineRequestParams.userId,
                userService: this.userService,
                inAppNotificationService: this.inAppNotificationService,
                announcementBusiness: this.announcementBusiness,
                userContext: timelineRequestParams.userContext,
                atlasActivityService: this.atlasActivityService
            })
            if (!_.isNil(notificationWidget) && !_.isEmpty(notificationWidget.items)) {
                this.putWidgetIntoView(notificationWidget, timelineView, today, "TODO_ACTIVITIES_CONTAINER_WIDGET")
            }
        }
    }

    private async addCultMindFutureClassWidgets(timelineView: TimelineViewV1, timelineRequestParams: TimelineRequestParams, userContext: UserContext, bookingsPromise?: Promise<FoodBooking[]>): Promise<void> {
        const user: User = await timelineRequestParams.userContext.userPromise
        const subUserIds = _.map(user.subUserRelations, relation => relation.subUserId)
        const allUserIds = [user.id, ...subUserIds]
        const cultBookingsPromise = this.cultFitService.bulkBookings(allUserIds, timelineRequestParams.endDate, timelineRequestParams.startDate)
        const mindBookingsPromise = this.mindFitService.bulkBookings(allUserIds, timelineRequestParams.endDate, timelineRequestParams.startDate)
        const cultBookingsResponse = await cultBookingsPromise
        const mindBookingsResponse = await mindBookingsPromise

        const foodBookings: FoodBooking[] = []
        const cultBookingWidgetPromise: Promise<void> = this.addCultMindFutureClassWidgetsHelper(timelineView, cultBookingsResponse, "FITNESS", userContext, foodBookings)
        const mindBookingWidgetPromise: Promise<void> = this.addCultMindFutureClassWidgetsHelper(timelineView, mindBookingsResponse, "MIND", userContext)

        await cultBookingWidgetPromise
        await mindBookingWidgetPromise
    }

    private async addCultMindFutureClassWidgetsHelper(timelineView: TimelineViewV1, bookingsMap: BulkBookingResponse, productType: ProductType, userContext: UserContext, foodBookings?: FoodBooking[]): Promise<void> {
        const widgetBuildPromises: {
            widgetPromise: Promise<WidgetView>,
            date: string
        }[] = []
        const bookingUserIds = Object.keys(bookingsMap)
        let userMap: { [userId: string]: User }
        if (!_.isEmpty(bookingUserIds)) {
            userMap = await this.userCache.getUsers(bookingUserIds)
        }
        const foodProductMap: { [bookingNumber: string]: FoodBooking } = {}
        for (const i in foodBookings) {
            if (foodBookings[i].meta && foodBookings[i].meta.referrerSource === "CULT_CLASS" && foodBookings[i].meta.referrerId) {
                const cultBookingNumber: string = foodBookings[i].meta.referrerId
                foodProductMap[cultBookingNumber] = foodBookings[i]
            }
        }
        const promiseArray = _.map(bookingUserIds, async (userId) => {
            const bookings = bookingsMap[userId].bookings
            const waitlistBookings = bookingsMap[userId].waitlists
            if (bookings && bookings.length > 0) {
                for (let i = 0; i < bookings.length; i++) {
                    const booking: CultBooking = bookings[i]
                    const status: ActivityState = TimelineUtil.getCultStatus(booking)
                    const workoutId: string = _.get(booking, "Class.Workout.id", "0")
                    const isCultScoreWorkout: boolean = CultUtil.isCultScoreWorkout(parseInt(workoutId))
                    const cultClassID: string = booking.Center.id.toString()
                    const kioskId: string = this.kiosksDemandService.getKioskIdGivenCenterId(cultClassID)
                    const kiosk: Kiosk = _.isNil(kioskId) ? undefined : this.kiosksDemandService.getKiosk(kioskId)
                    const cultClass = booking.Class
                    const startTime: string = cultClass.startTime
                    const endTime: string = cultClass.endTime
                    const start: string[] = startTime.split(":")
                    const end: string[] = endTime.split(":")
                    const startEndTime: StartEndTime = {
                        startingHours: { hour: parseInt(start[0]), min: parseInt(start[1]) },
                        closingHours: { hour: parseInt(end[0]), min: parseInt(end[1]) }
                    }
                    let productMap: { [productId: string]: Product }
                    const showAddSnackWidget: boolean = await this.kiosksDemandService.shouldShowAddSnackWidget(cultClassID, booking.Class.date, startEndTime)
                    const isCafe: boolean = kiosk && kiosk.cafeConfig && kiosk.cafeConfig.enabled && showAddSnackWidget && _.isNil(booking.wlBookingNumber)
                    if (isCafe && !_.isNil(foodProductMap[booking.bookingNumber])) {
                        const productIds: string[] = []
                        for (const j in foodProductMap[booking.bookingNumber].products) {
                            const productId: string = foodProductMap[booking.bookingNumber].products[j].productId
                            productIds.push(productId)
                        }
                        productMap = await this.catalogueService.getProductMap(productIds)
                    }
                    if (status === "TODO" || isCultScoreWorkout) {
                        const cultWidget: CultMindCarouselWidget = new CultMindCarouselWidget()
                        widgetBuildPromises.push({
                            widgetPromise: cultWidget.buildView({
                                booking: booking,
                                productBusiness: this.productBusiness,
                                issuesMap: undefined,
                                userContext: userContext,
                                productType: productType,
                                logger: this.logger,
                                subUserDetail: userId != userContext.userProfile.userId ? userMap[userId] : undefined,
                                cafeFoodDetails: foodProductMap[booking.bookingNumber],
                                productMap: productMap,
                                isCafe: isCafe,
                                kioskId: kioskId,
                            }),
                            date: booking.Class.date
                        })
                    }
                }
            }
            if (!_.isEmpty(waitlistBookings)) {
                for (let i = 0; i < waitlistBookings.length; i++) {
                    const booking: CultBooking = waitlistBookings[i]
                    if (booking.state === "PENDING" || booking.state === "REJECTED") {
                        const cultWidget: CultMindCarouselWidget = new CultMindCarouselWidget()
                        widgetBuildPromises.push({
                            widgetPromise: cultWidget.buildView({
                                booking: booking,
                                productBusiness: this.productBusiness,
                                issuesMap: undefined,
                                userContext: userContext,
                                productType: productType,
                                logger: this.logger
                            }),
                            date: booking.Class.date
                        })
                    }
                }
            }
            return Promise.resolve()
        })
        await Promise.all(promiseArray)

        for (const widgetBuildData of widgetBuildPromises) {
            const widget: ITodoActivityWidget = <ITodoActivityWidget>await widgetBuildData.widgetPromise
            if (!_.isNil(widget) && widget.status === "TODO") {
                this.putWidgetIntoView(widget, timelineView, widgetBuildData.date, "TODO_ACTIVITIES_CONTAINER_WIDGET")
            }
        }
    }

    private async addGMSWidgets(timelineView: TimelineViewV1, timelineRequestParams: TimelineRequestParams, userContext: UserContext, userPromise: Promise<User>): Promise<void> {
        const tz = timelineRequestParams.userContext.userProfile.timezone
        const isClientSupported = AppUtil.isGoalManagementSupported(userContext)
        const user: User = await userPromise
        if (!isClientSupported) {
            return
        }
        const dateMap: { [date: string]: WidgetView[] } = timelineView.dateMap
        const allDates: string[] = TimeUtil.datesBetween(tz, timelineRequestParams.startDate, timelineRequestParams.endDate)
        const startEpoch: number = TimeUtil.parseDateUTC(allDates[0], tz).getTime()
        let todoUserGoals: TodoPlanActivityView[]
        try {
            // TODO: Delete code as todoActivities is always empty array from gmf-client
            todoUserGoals = []
            // await this.gmfClient.getTodoActivities({
            //     userId: timelineRequestParams.userId,
            //     isSelf: true,
            //     dateSearchEpochs: {
            //         startEpoch: startEpoch,
            //         numberOfDays: allDates.length
            //     },
            //     statuses: ["RECOMMENDED"]
            // })
        } catch (e) {
            this.logger.error("Error contacting GMS Service", e)
            this.rollbarService.handleError(e)
            return
        }
        const widgetBuildPromises: {
            widgetPromise: Promise<ITodoActivityWidget>,
            date: string
        }[] = []
        const cultCityId: number = await this.cultBusiness.getCultCityId(userContext, userContext.userProfile.cityId, userContext.userProfile.userId)
        const cultWorkoutPromiseMap: Promise<Map<string, CultWorkout>> = this.getAllRecommendedWorkouts(todoUserGoals, cultCityId, "FITNESS")
        const mindWorkoutPromiseMap: Promise<Map<string, CultWorkout>> = this.getAllRecommendedWorkouts(todoUserGoals, cultCityId, "MIND")
        const mealsPromise: Promise<Meal[]> = this.getAllRecommendedMeals(todoUserGoals)
        todoUserGoals.forEach((todoGoal: TodoPlanActivityView) => {
            const date: string = TimeUtil.formatDateInTimeZone(tz, new Date(todoGoal.todoDate))
            if (_.isNil(dateMap[date])) return
            widgetBuildPromises.push({
                widgetPromise: GMSCarouselWidgetBuilder.buildGMSWidget({
                    recommendedItem: todoGoal,
                    userContext: userContext,
                    catalogueService: this.catalogueService,
                    activityLoggingBusiness: this.activityLoggingBusiness,
                    logger: this.logger,
                    cultWorkoutPromiseMap: cultWorkoutPromiseMap,
                    mindWorkoutPromiseMap: mindWorkoutPromiseMap,
                    mealsPromiseList: mealsPromise,
                    diyService: this.DIYFulfilmentService
                }),
                date: date
            })
        })

        const currentEpoch: number = TimeUtil.getCurrentEpoch()
        const todoCutoff: number = currentEpoch - (2 * TimeUtil.TIME_IN_MILLISECONDS.MINUTE) // showing widgets in Todo for a 2 minute buffer window as well
        for (const widgetBuildData of widgetBuildPromises) {
            const widget: ITodoActivityWidget = await widgetBuildData.widgetPromise
            if (!_.isNil(widget)) {
                let container: WidgetType = "PENDING_ACTIVITIES_CONTAINER_WIDGET"
                if (!_.isNil(widget.timestamp) && widget.timestamp >= todoCutoff) {
                    container = "TODO_ACTIVITIES_CONTAINER_WIDGET"
                }
                this.putWidgetIntoView(widget, timelineView, widgetBuildData.date, container)
            }
        }
    }

    private async getAllRecommendedWorkouts(todoUserGoals: TodoPlanActivityView[], cultCityId: number, productType: CatalogueProductType): Promise<Map<string, CultWorkout>> {
        const workoutPromiseMap: Map<string, Promise<CultWorkout>> = new Map<string, Promise<CultWorkout>>()
        todoUserGoals.forEach((todoGoal: TodoPlanActivityView) => {
            todoGoal.recommendations.forEach((recommendation: ProductRecommendation) => {
                if (recommendation.catalogueProductType === "FITNESS" && productType === "FITNESS") {
                    recommendation.productIds.forEach((workoutId: string) => {
                        if (!workoutPromiseMap.has(workoutId)) {
                            workoutPromiseMap.set(workoutId, this.cultFitService.getWorkout(workoutId, "CUREFIT_API", cultCityId))
                        }
                    })
                } else if (recommendation.catalogueProductType === "MIND" && productType === "MIND") {
                    recommendation.productIds.forEach((workoutId: string) => {
                        if (!workoutPromiseMap.has(workoutId)) {
                            workoutPromiseMap.set(workoutId, this.mindFitService.getWorkout(workoutId, "CUREFIT_API", cultCityId))
                        }
                    })
                }
            })
        })

        const workoutMap: Map<string, CultWorkout> = new Map<string, CultWorkout>()
        for (const workoutId of Array.from(workoutPromiseMap.keys())) {
            const cultWorkout: CultWorkout = await workoutPromiseMap.get(workoutId)
            workoutMap.set(workoutId, cultWorkout)
        }
        return workoutMap
    }

    private async getAllRecommendedMeals(todoUserGoals: TodoPlanActivityView[]): Promise<Meal[]> {
        const uniqueMealIds: Set<string> = new Set<string>()
        todoUserGoals.forEach((todoGoal: TodoPlanActivityView) => {
            todoGoal.recommendations.forEach((recommendation: ProductRecommendation) => {
                if (recommendation.catalogueProductType === "FOOD_RECOMMENDATION") {
                    recommendation.productIds.forEach((mealId: string) => {
                        uniqueMealIds.add(mealId)
                    })
                }
            })
        })
        if (!_.isEmpty(uniqueMealIds)) {
            const mealsPromise: Promise<Meal[]> = this.recommendationService.getMealsByIds(Array.from(uniqueMealIds.keys()))
            return mealsPromise
        } else {
            return []
        }

    }

    private async addFoodLoggedActivitiesWidgets(timelineView: TimelineViewV1, loggedActivities: ActivityDS[], userContext: UserContext): Promise<void> {
        const loggedDietDateMap: Map<string, ActivityDS[]> = new Map<string, ActivityDS[]>()
        const dietWidgetDateMap: Map<string, ILoggedActivityWidget> = new Map<string, ILoggedActivityWidget>()

        // Construct Map of date to food activities
        _.each(loggedActivities, (activity: ActivityDS) => {
            if (!ActivityLoggingUtil.isFoodActivity(activity)) return
            if (!loggedDietDateMap.has(activity.date)) {
                loggedDietDateMap.set(activity.date, [])
            }
            loggedDietDateMap.get(activity.date).push(activity)
        })

        // Construct widgets from food activity data
        const dietWidgetBuildPromises: Promise<void>[] = []
        loggedDietDateMap.forEach((foodActivities: ActivityDS[], date: string) => {
            const dietWidget: FoodLoggedActivityWidget = new FoodLoggedActivityWidget()
            dietWidgetBuildPromises.push(dietWidget.buildView(foodActivities, {
                catalogueService: this.catalogueService,
                productBusiness: this.productBusiness,
                activityLoggingBusiness: this.activityLoggingBusiness,
                activities: foodActivities,
                date: date,
                logger: this.logger,
                userContext: userContext
            }))
            dietWidgetDateMap.set(date, dietWidget)
        })
        await Promise.all(dietWidgetBuildPromises)

        dietWidgetDateMap.forEach((dietWidget: ILoggedActivityWidget, date: string) => {
            if (!_.isEmpty(dietWidget.items)) {
                this.putWidgetIntoView(dietWidget, timelineView, date, "LOGGED_ACTIVITIES_CONTAINER_WIDGET")
            }
        })
    }

    private async addFitnessLoggedActivitiesWidget(timelineView: TimelineViewV1, loggedActivities: ActivityDS[], userContext: UserContext): Promise<void> {
        const loggedFitnessDateMap: Map<string, ActivityDS[]> = new Map<string, ActivityDS[]>()
        const fitnessWidgetDateMap: Map<string, ILoggedActivityWidget> = new Map<string, ILoggedActivityWidget>()
        const fitnessWidgetBuildPromises: Promise<void>[] = []

        // Construct Map of date to fitness activities
        _.each(loggedActivities, (activity: ActivityDS) => {
            if (!ActivityLoggingUtil.isFitnessActivity(activity)) return
            if (ActivityLoggingUtil.isTodayWalkActivity(userContext, activity) && activity.score === 0) {
                // Special case: Today's walk activity which is still not completed (should show up in TODO section)
                const todayStepsWidget: StepsCarouselWidget = new StepsCarouselWidget()
                fitnessWidgetBuildPromises.push(todayStepsWidget.buildView({
                    walkActivity: TimelineUtil.createStepsAtlasActivity(activity),
                    userContext: userContext,
                    logger: this.logger
                }))
                this.putWidgetIntoView(todayStepsWidget, timelineView, activity.date, "TODO_ACTIVITIES_CONTAINER_WIDGET")
                return
            }
            if (!loggedFitnessDateMap.has(activity.date)) {
                loggedFitnessDateMap.set(activity.date, [])
            }
            loggedFitnessDateMap.get(activity.date).push(activity)
        })

        // Construct widgets from fitness activity data
        loggedFitnessDateMap.forEach((fitnessActivities: ActivityDS[], date: string) => {
            const fitnessWidget: FitnessLoggedActivityWidget = new FitnessLoggedActivityWidget()
            fitnessWidgetBuildPromises.push(fitnessWidget.buildView({
                activities: fitnessActivities,
                catalogueService: this.catalogueService,
                activityLoggingBusiness: this.activityLoggingBusiness,
                cultFitService: this.cultFitService,
                cultPTService: this.cultPTService,
                logger: this.logger,
                userContext: userContext,
                userCache: this.userCache
            }))
            fitnessWidgetDateMap.set(date, fitnessWidget)
        })
        await Promise.all(fitnessWidgetBuildPromises)

        fitnessWidgetDateMap.forEach((fitnessWidget: ILoggedActivityWidget, date: string) => {
            if (!_.isEmpty(fitnessWidget.items)) {
                this.putWidgetIntoView(fitnessWidget, timelineView, date, "LOGGED_ACTIVITIES_CONTAINER_WIDGET")
            }
        })
    }

    private async addMeditationLoggedActivitiesWidget(timelineView: TimelineViewV1, loggedActivities: ActivityDS[], userContext: UserContext, timelineRequestParams: TimelineRequestParams): Promise<void> {
        const tz = timelineRequestParams.userContext.userProfile.timezone
        const loggedMeditationDateMap: Map<string, ActivityDS[]> = new Map<string, ActivityDS[]>()
        const meditationWidgetDateMap: Map<string, ILoggedActivityWidget> = new Map<string, ILoggedActivityWidget>()
        const meditationWidgetBuildPromises: Promise<void>[] = []
        const dates: string[] = TimeUtil.datesBetween(tz, timelineRequestParams.startDate, timelineRequestParams.endDate)
        dates.forEach((date: string) => {
            loggedMeditationDateMap.set(date, [])
        })

        // Construct Map of date to meditation activities
        _.each(loggedActivities, (activity: ActivityDS) => {
            if (!ActivityLoggingUtil.isMeditationActivity(activity)) return
            loggedMeditationDateMap.get(activity.date).push(activity)
        })

        // Construct widgets from meditation activity data
        loggedMeditationDateMap.forEach((meditationActivities: ActivityDS[], date: string) => {
            const meditationWidget: MeditationLoggedActivityWidget = new MeditationLoggedActivityWidget()
            meditationWidgetBuildPromises.push(meditationWidget.buildView({
                activities: meditationActivities,
                date: date,
                catalogueService: this.catalogueService,
                mindFitService: this.mindFitService,
                activityLoggingBusiness: this.activityLoggingBusiness,
                userContext: userContext
            }))
            meditationWidgetDateMap.set(date, meditationWidget)
        })
        await Promise.all(meditationWidgetBuildPromises)

        meditationWidgetDateMap.forEach((meditationWidget: ILoggedActivityWidget, date: string) => {
            if (!_.isEmpty(meditationWidget.items)) {
                this.putWidgetIntoView(meditationWidget, timelineView, date, "LOGGED_ACTIVITIES_CONTAINER_WIDGET")
            }
        })
    }

    private async addCareLoggedActivitiesWidget(timelineView: TimelineViewV1, loggedActivities: ActivityDS[], userContext: UserContext): Promise<void> {
        const loggedCareDateMap: Map<string, ActivityDS[]> = new Map<string, ActivityDS[]>()
        const careWidgetDateMap: Map<string, ILoggedActivityWidget> = new Map<string, ILoggedActivityWidget>()
        const careWidgetBuildPromises: Promise<void>[] = []

        // Construct Map of date to care activities
        _.each(loggedActivities, (activity: ActivityDS) => {
            if (!ActivityLoggingUtil.isCareActivity(activity)) return
            if (!loggedCareDateMap.has(activity.date)) {
                loggedCareDateMap.set(activity.date, [])
            }
            loggedCareDateMap.get(activity.date).push(activity)
        })

        // Construct widgets from care activity data
        loggedCareDateMap.forEach((careActivities: ActivityDS[], date: string) => {
            const careWidget: CareLoggedActivityWidget = new CareLoggedActivityWidget()
            careWidgetBuildPromises.push(careWidget.buildView({
                healthfaceService: this.healthfaceService,
                catalogueService: this.catalogueService,
                activities: careActivities,
                date: date,
                logger: this.logger,
                userContext: userContext
            }))
            careWidgetDateMap.set(date, careWidget)
        })
        await Promise.all(careWidgetBuildPromises)

        careWidgetDateMap.forEach((careWidget: ILoggedActivityWidget, date: string) => {
            if (!_.isEmpty(careWidget.items)) {
                this.putWidgetIntoView(careWidget, timelineView, date, "LOGGED_ACTIVITIES_CONTAINER_WIDGET")
            }
        })
    }

    // ******************************************* //
    // ******************************************* //

    private putWidgetIntoView(widget: WidgetView, timelineView: TimelineViewV1, date: string, container: WidgetType, containerMeta?: any): void {
        const dateMap: { [date: string]: WidgetView[] } = timelineView.dateMap
        if (_.isNil(dateMap[date])) {
            dateMap[date] = []
        }
        let index: number = _.findIndex(dateMap[date], (widgetContainer: WidgetView) => {
            return widgetContainer.widgetType === container
        })
        if (index === -1) {
            index = dateMap[date].length
            const newContainerWidget: WidgetView = {
                widgetType: container
            }
            _.assign(newContainerWidget, containerMeta)
            dateMap[date].push(newContainerWidget)
        }
        if (!_.isArray(dateMap[date][index].data)) {
            dateMap[date][index].data = []
        }
        if (!_.isNil(widget)) {
            const section = <any>dateMap[date][index]
            section["sectionViewHide"] = (section.title === "Todo")
            section.data.push(widget)
        }
    }

    private sortWidgets(userContext: UserContext, timelineView: TimelineViewV1): void {
        _.each(timelineView.dateMap, (widgetContainerList) => {
            widgetContainerList.forEach((widgetContainer) => {
                if (widgetContainer.widgetType === "TODO_ACTIVITIES_CONTAINER_WIDGET" || widgetContainer.widgetType === "PENDING_ACTIVITIES_CONTAINER_WIDGET") {
                    sortTodoWidgets(userContext, <Array<ITodoActivityWidget>>(widgetContainer.data))
                } else if (widgetContainer.widgetType === "LOGGED_ACTIVITIES_CONTAINER_WIDGET") {
                    sortLoggedActivityWidgets(<Array<ILoggedActivityWidget>>(widgetContainer.data))
                }
            })
        })
    }

    private hideEmptyContainers(timelineView: TimelineViewV1): void {
        _.each(timelineView.dateMap, (widgetContainerList) => {
            widgetContainerList.forEach((widgetContainer) => {
                if (widgetContainer.widgetType === "TODO_ACTIVITIES_CONTAINER_WIDGET" || widgetContainer.widgetType === "PENDING_ACTIVITIES_CONTAINER_WIDGET") {
                    if (_.isEmpty(<Array<ITodoActivityWidget>>(widgetContainer.data))) {
                        (<any>widgetContainer).sectionViewHide = true
                    }
                } else if (widgetContainer.widgetType === "LOGGED_ACTIVITIES_CONTAINER_WIDGET") {
                    if (_.isEmpty(<Array<ILoggedActivityWidget>>(widgetContainer.data))) {
                        (<any>widgetContainer).sectionViewHide = true
                    }
                }
            })
        })
    }

    private dedupeFoodWidgets(timelineView: TimelineViewV1, timezone: Timezone): void {
        // Create a map of date to set of meal slots for which eatfit meal has been ordered on that date
        const eatfitMealMap: Map<string, Set<string>> = new Map<string, Set<string>>()
        _.each(timelineView.dateMap, (widgetContainerList, date) => {
            widgetContainerList.forEach((widgetContainer) => {
                if (widgetContainer.widgetType === "TODO_ACTIVITIES_CONTAINER_WIDGET") {
                    (<Array<ITodoActivityWidget>>(widgetContainer.data)).forEach(widget => {
                        if (widget.activityType === "EATFIT_MEAL") {
                            if (!eatfitMealMap.has(date)) {
                                eatfitMealMap.set(date, new Set<string>())
                            }
                            try {
                                const mealSlot: MealSlot = SlotUtil.getMealSlotFromDate(new Date(widget.timestamp), timezone)
                                eatfitMealMap.get(date).add(mealSlot)
                            } catch (e) {
                                // Do nothing -- TODO - fix later
                            }
                        }
                    })
                } else if (widgetContainer.widgetType === "LOGGED_ACTIVITIES_CONTAINER_WIDGET") {
                    (<Array<ILoggedActivityWidget>>(widgetContainer.data)).forEach(widget => {
                        widget.items.forEach((widgetItem: ILoggedActivityWidgetItem) => {
                            if (widgetItem.activityType === "EATFIT_MEAL" && widgetItem.status === "DONE") {
                                if (!eatfitMealMap.has(date)) {
                                    eatfitMealMap.set(date, new Set<string>())
                                }
                                try {
                                    const mealSlot: MealSlot = SlotUtil.getMealSlotFromDate(new Date(widgetItem.timestamp), timezone)
                                    eatfitMealMap.get(date).add(mealSlot)
                                } catch (e) {
                                    // Do nothing -- TODO - fix later
                                }
                            }
                        })
                    })
                }
            })
        })

        // Iterate through all widgets and remove any meal recommendations which exist on the same slot and date when a eatfit meal is scheduled
        _.each(timelineView.dateMap, (widgetContaierList, date) => {
            widgetContaierList.forEach((widgetContainer) => {
                if (widgetContainer.widgetType === "TODO_ACTIVITIES_CONTAINER_WIDGET" || widgetContainer.widgetType === "PENDING_ACTIVITIES_CONTAINER_WIDGET") {
                    let i: number = (<Array<ITodoActivityWidget>>(widgetContainer.data)).length
                    while (i--) { // Iterating backwards coz deleting an element in array reindexes the array
                        const widget: ITodoActivityWidget = widgetContainer.data[i]
                        if (widget.activityType === "MEAL_RECOMMENDATION") {
                            const mealSlot: MealSlot = SlotUtil.getMealSlotFromDate(new Date(widget.timestamp), timezone)
                            if (eatfitMealMap.has(date) && eatfitMealMap.get(date).has(mealSlot)) {
                                (<Array<ITodoActivityWidget>>(widgetContainer.data)).splice(i, 1)
                            }
                        }
                    }
                }
            })
        })
    }

    private async noActivityWidget(timelineView: TimelineViewV1, timelineRequestParams: TimelineRequestParams): Promise<void> {
        const tz = timelineRequestParams.userContext.userProfile.timezone
        const today: string = TimeUtil.todaysDate(tz)
        _.each(timelineView.dateMap, (widgetContainerList, date) => {
            widgetContainerList.forEach((widgetContainer) => {
                if (widgetContainer.widgetType === "TODO_ACTIVITIES_CONTAINER_WIDGET") {
                    if (_.isEmpty(<Array<ITodoActivityWidget>>(widgetContainer.data)) || _.filter(widgetContainer.data, { "widgetType": "TODO_ACTIVITY_WIDGET" }).length === 0) {
                        if (date === today) {
                            const widget: EmptyStateWidget = {
                                widgetType: "EMPTY_STATE_WIDGET",
                                image: "/image/today/placeholders/today_no_activity.png",
                                title: "This is your To-Do list!",
                                subTitle: "When you book a class, order a meal, or book a doctor consultation, it will appear in this list. Simply complete your To-Do list everyday to stay healthy!",
                                action: {
                                    title: "Get started",
                                    actionType: "NAVIGATION",
                                    url: "curefit://browse"
                                }
                            }
                            this.putWidgetIntoView(widget, timelineView, date, "TODO_ACTIVITIES_CONTAINER_WIDGET")
                        } else if (TimeUtil.diffInDaysReal(tz, date, today) > 0) {
                            const widget: EmptyStateWidget = {
                                widgetType: "EMPTY_STATE_WIDGET",
                                image: "/image/today/placeholders/today_no_activity.png",
                                title: "",
                                subTitle: "No activites scheduled for the day",
                            }
                            this.putWidgetIntoView(widget, timelineView, date, "TODO_ACTIVITIES_CONTAINER_WIDGET")
                        }

                    }
                }
            })
        })
    }

    private cafeTimeline(timeline: TimelineViewV1) {
        for (const date in timeline.dateMap) {
            const timelineDataData = timeline.dateMap[date]
            const todoIndex = timelineDataData.findIndex(index => {
                if (index.widgetType === "TODO_ACTIVITIES_CONTAINER_WIDGET")
                    return true
            })
            const todoContainer = timelineDataData[todoIndex]
            const cultActivityTypeList = todoContainer.data.filter((data: any) => {
                if (data.activityType === "CULT_CLASS") {
                    return true
                }
            })
            todoContainer.data = todoContainer.data.filter((activity: any) => {
                if (activity.activityType === "EATFIT_MEAL") {
                    const bookingNumber: string = activity.bookingNumber
                    if (bookingNumber) {
                        const cultCafeClass = _.find(cultActivityTypeList, (cultActivity) => { return cultActivity.bookingNumber === bookingNumber })
                        if (cultCafeClass) {
                            return false
                        }
                    }
                }
                return true
            })
        }
    }
}
