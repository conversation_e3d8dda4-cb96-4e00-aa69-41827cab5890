import { inject, injectable } from "inversify"
import { DEFAULT_CACHE_REFRESH_TIME_IN_SECONDS, InMemoryCacheService } from "@curefit/memory-cache"
import { IPageConfigReadOnlyDao, IPageConfigReadWriteDao, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import { PageWidgetTitle } from "../page/Page"
import { MealSlot } from "@curefit/eat-common"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

@injectable()
class RecommendationPageConfig extends InMemoryCacheService<any> {

    constructor(
        @inject(PAGE_CONFIG_TYPES.PageConfigReadOnlySecondaryDao) protected pageConfigDao: IPageConfigReadOnlyDao,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger, 37 * 60)
        this.load("RecommendationPageConfig")
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    loadData(): Promise<any> {
        return this.pageConfigDao.findOne({ pageId: { $eq: "RecommendationPageConfig" } }).then(pageConfig => {
            this.data = pageConfig.data
            this.packSeeMoreText = this.data.packSeeMoreText
            this.cultPackTitle = this.data.cultPackTitle
            this.mindPackTitle = this.data.mindPackTitle
            this.foodPackTitle = this.data.foodPackTitle
            this.careHCUPackTitle = this.data.careHCUPackTitle
            return this.data
        })
    }
    public data: any
    public packSeeMoreText: string
    public cultPackTitle: PageWidgetTitle
    public mindPackTitle: PageWidgetTitle
    public foodPackTitle: PageWidgetTitle
    public careHCUPackTitle: PageWidgetTitle

    public getDIYPacksTitle(series: string): PageWidgetTitle {
        return this.data.diyPacksTitle[series]
    }

    public getFoodPackTitle(mealSlot: MealSlot): PageWidgetTitle {
        return this.data.foodPackTitle[mealSlot]
    }
}

export default RecommendationPageConfig
