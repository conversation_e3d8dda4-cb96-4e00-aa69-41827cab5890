import { TimelineViewV1 } from "./TimelineView"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { ActivityType } from "@curefit/product-common"
import { UserContext } from "@curefit/userinfo-common"

export const ACTIVITY_PRIORITY_MAP = new Map<ActivityType, number>()
ACTIVITY_PRIORITY_MAP.set("METRICS", 0)
ACTIVITY_PRIORITY_MAP.set("NOTIFICATION", 1)
ACTIVITY_PRIORITY_MAP.set("GET_A_PACK", 10)
ACTIVITY_PRIORITY_MAP.set("CULT_CLASS_BOOK", 20)
ACTIVITY_PRIORITY_MAP.set("CONSULTATION", 40)
ACTIVITY_PRIORITY_MAP.set("DIAGNOSTICS_SAMPLE_COLLECTION", 40)
ACTIVITY_PRIORITY_MAP.set("BOOK_CONSULTATION", 40)
ACTIVITY_PRIORITY_MAP.set("BOOK_TESTS", 40)
ACTIVITY_PRIORITY_MAP.set("VIEW_REPORTS", 40)
ACTIVITY_PRIORITY_MAP.set("VIEW_PRESCRIPTION", 40)
ACTIVITY_PRIORITY_MAP.set("HEALTH_ASSESSMENTS", 40)
ACTIVITY_PRIORITY_MAP.set("DIAGNOSTICS", 40)
ACTIVITY_PRIORITY_MAP.set("PRESCRIPTION", 40)
ACTIVITY_PRIORITY_MAP.set("LABREPORT", 40)
ACTIVITY_PRIORITY_MAP.set("CONSULTATION_OFFERS", 40)
ACTIVITY_PRIORITY_MAP.set("CULT_CLASS", 40)
ACTIVITY_PRIORITY_MAP.set("EATFIT_MEAL", 40)
ACTIVITY_PRIORITY_MAP.set("DIY_MEDITATION", 50)
ACTIVITY_PRIORITY_MAP.set("DIY_FITNESS", 60)
ACTIVITY_PRIORITY_MAP.set("MEAL_RECOMMENDATION", 70)
ACTIVITY_PRIORITY_MAP.set("CULT_RECOMMENDATION", 71)
ACTIVITY_PRIORITY_MAP.set("MIND_RECOMMENDATION", 72)
ACTIVITY_PRIORITY_MAP.set("CARE_RECOMMENDATION", 73)
ACTIVITY_PRIORITY_MAP.set("WALK", 80)
ACTIVITY_PRIORITY_MAP.set("SLEEP", 90)
ACTIVITY_PRIORITY_MAP.set("META", 100)

export interface TimelineRequestParams {
    userAgent: UserAgent
    cityId: string
    userId: string
    cultCenterId: string
    mindCenterId: string
    deviceId: string
    startDate: string
    endDate?: string
    score: boolean
    metrics: boolean
    canAskHealthKit: boolean
    canAskFitBit: boolean
    isTodayStepFromServer: boolean
    showaddoption: boolean
    userContext: UserContext
}
interface ITimelineBusiness {
    getTimeLine: (timelineRequestParams: TimelineRequestParams, userContext: UserContext) => Promise<TimelineViewV1>

}

export default ITimelineBusiness
