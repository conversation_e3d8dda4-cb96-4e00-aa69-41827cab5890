import { Hub } from "@curefit/eat-common"
import { UserAddress } from "@curefit/user-common"

export class HubView {
    constructor(hub: Hub, isSelected: boolean) {
        this.hubId = hub.hubId
        this.hubName = hub.name
        this.city = hub.city
        this.selected = isSelected
    }

    public hubId: string
    public hubName: string
    public city: string
    public selected: boolean
}

export class LocationView {
    constructor(public addresses: UserAddress[], hubs: Hub[], selectedHub?: Hub) {
        let selectedHubId: string
        if (selectedHub) {
            selectedHubId = selectedHub.hubId
            this.selectedHub = new HubView(selectedHub, true)
        }
        this.hubs = hubs.map(hub => new HubView(hub, (hub.hubId === selectedHubId)))
    }

    public selectedHub: HubView
    public hubs: HubView[]
}
