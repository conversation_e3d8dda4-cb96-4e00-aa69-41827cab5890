import { UserStructuredAddress } from "@curefit/user-common"

class UserStructuredAddressView {
  constructor(address: UserStructuredAddress) {
    this.key = address.addressId
    this.addressId = address.addressId
    this.name = address.name
    this.addressLine1 = address.addressLine1
    this.addressLine2 = address.addressLine2
    this.locality = address.locality
    this.landmark = address.landmark
    this.city = address.city
    this.state = address.state
    this.country = address.country
    this.pincode = address.pincode
    this.phoneNumber = address.phoneNumber
  }

  key: string
  addressId: string
  name: string // name of the recipient.
  addressLine1: string
  addressLine2?: string
  locality: string
  landmark?: string
  city: string
  state: string
  country: string
  pincode: string
  phoneNumber: string

}
export default UserStructuredAddressView
