import * as express from "express"
import { DiagnosticProductResponse } from "@curefit/care-common"
import { FoodProduct as Product } from "@curefit/eat-common"
import { Session } from "@curefit/userinfo-common"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { controller, httpGet } from "inversify-express-utils"
import { Container, inject } from "inversify"
import AuthMiddleware from "../auth/AuthMiddleware"
import * as _ from "lodash"
import { Logger, BASE_TYPES } from "@curefit/base"
import { IUserGoalBusiness, UserGoalDetailView } from "../goals/UserGoalBusiness"
import { IMetricsGraphWidget } from "../common/views/IMetricsGraphWidget"
import { ManagedGoalProductViewModel } from "@curefit/gmf-common"
import { IGMFClient, GMF_CLIENT_TYPES } from "@curefit/gmf-client"
import { UserPlanStatus } from "@curefit/gmf-client"
import PlanProductPage from "./PlanProductPage"
import UserPlanSelectionPage from "../userplan/UserPlanSelectionPage"
import { UserContext } from "@curefit/userinfo-common"
import { ICatalogueService, CATALOG_CLIENT_TYPES } from "@curefit/catalog-client"
import { IHealthfaceService, ALBUS_CLIENT_TYPES } from "@curefit/albus-client"
import { IOfferServiceV2 } from "@curefit/offer-service-client"
import UserGoalSelectionPage from "../userplan/UserGoalSelectionPage"
import { OFFER_SERVICE_CLIENT_TYPES } from "@curefit/offer-service-client"
import AppUtil from "../util/AppUtil"
import { CareWidgetUtil } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"

export function controllerFactory(kernel: Container) {
  @controller(
    "/goals",
    kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
  class UserGoalController {
    constructor(
      @inject(CUREFIT_API_TYPES.UserGoalBusiness)
      private userGoalBusiness: IUserGoalBusiness,
      @inject(GMF_CLIENT_TYPES.IGMFClient) private gmsService: IGMFClient,
      @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
      @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) private offerService: IOfferServiceV2,
      @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
      @inject(BASE_TYPES.ILogger) private logger: Logger,
      @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces
    ) { }

    @httpGet("/detail/:id")
    async getUserGoalDetail(req: express.Request): Promise<UserGoalDetailView> {
      const session: Session = req.session
      const userId: string = session.userId
      const userContext: UserContext = req.userContext as UserContext
      return this.userGoalBusiness.getUserGoalDetailView(userId, req.params.id, userContext)
    }

    @httpGet("/metricGraphData")
    async getGoalMetricsGraphData(
      req: express.Request
    ): Promise<IMetricsGraphWidget> {
      const session: Session = req.session
      const userId: string = session.userId
      const userContext: UserContext = req.userContext as UserContext
      return this.userGoalBusiness.getUserGoalMetricsGraphView(userId, req.query, userContext)
    }

    @httpGet("/adherenceGraphData")
    async getGoalAdherenceGraphData(
      req: express.Request
    ): Promise<IMetricsGraphWidget> {
      const session: Session = req.session
      const userId: string = session.userId
      return this.userGoalBusiness.getUserGoalAdherenceGraphView(
        req.userContext,
        userId,
        req.query
      )
    }

    @httpGet("/productpage/:productId")
    async getProductPage(req: express.Request): Promise<PlanProductPage> {
      const productId: string = req.params.productId
      const product: ManagedGoalProductViewModel = await this.gmsService.getProductInfo(productId)
      return PlanProductPage.getView(product)
    }

    @httpGet("/fetchRecommendedPlans/:productId")
    async fetchPlans(req: express.Request): Promise<UserPlanSelectionPage> {
      const productId: string = req.params.productId
      const plans = (await this.gmsService.fetchRecommendedPlans(productId))
      return UserPlanSelectionPage.getView(productId, plans.options)
    }

    @httpGet("/fetchGoalProducts")
    async fetchGoalProducts(req: express.Request): Promise<UserGoalSelectionPage> {
      const userContext: UserContext = req.userContext as UserContext
      const productIds: string[] = req.query.productIds.split(",")
      const session: Session = req.session
      const productPromises = _.map(productIds, async productId => await this.catalogueService.getProduct(productId))
      const products: Product[] = await Promise.all(productPromises)
      const diagnosticsProducts: DiagnosticProductResponse[] = []
      const offerProductIds: string[] = []
      const deviceProductIds: string[] = []
      await Promise.all(_.map(products, async product => {
        if (product.productType === "BUNDLE") {
          // get all child products
          const diagnosticsProduct = <DiagnosticProductResponse>((await this.healthfaceService.getProductInfoDetails("BUNDLE", { subCategoryCode: "MP", productCodeCsv: product.productId }))[0]).baseSellableProduct
          diagnosticsProducts.push(diagnosticsProduct)
          const mandatoryProducts = diagnosticsProduct.childProducts.filter(product => product.childProductType === "CHILD_MANDATORY"
            && product.baseSellableProduct.categoryCode !== "DEVICE")
          const deviceProducts = diagnosticsProduct.childProducts.filter(product => product.childProductType === "CHILD_MANDATORY" && product.baseSellableProduct.categoryCode === "DEVICE")
          _.map(mandatoryProducts, product => offerProductIds.push(product.baseSellableProduct.productCode))
          _.map(deviceProducts, product => deviceProductIds.push(product.baseSellableProduct.productCode))
        }
      }))
      const offers = await CareWidgetUtil.getCareProductOffersFromUserContext(
          userContext,
          "BUNDLE",
          offerProductIds,
          AppUtil.callSourceFromContext(userContext),
          this.serviceInterfaces,
          true
      )
      const deviceOffers = await CareWidgetUtil.getCareProductOffersFromUserContext(
        userContext,
        "DEVICE",
        deviceProductIds,
        AppUtil.callSourceFromContext(userContext),
        this.serviceInterfaces,
        true
      )
      return UserGoalSelectionPage.getView(products, diagnosticsProducts, offers, deviceOffers)
    }

    @httpGet("/selectPlan/:productId")
    async selectPlan(req: express.Request): Promise<boolean> {
      const productId: string = req.params.productId
      const optionId: string = req.query.optionId
      const session: Session = req.session
      const userId: string = session.userId
      const planInfo = await this.gmsService.generatePlan(userId)
      return await this.gmsService.selectPlanOption(userId, productId, optionId)
    }

    @httpGet("/generatePlan")
    async generatePlan(req: express.Request): Promise<string> {
      const session: Session = req.session
      const userId: string = session.userId
      const planInfo = await this.gmsService.generatePlan(userId)
      return planInfo.userPlanGenInfo.genId
    }

    @httpGet("/planGenerated")
    async planGenerated(req: express.Request): Promise<UserPlanStatus> {
      const session: Session = req.session
      const userId: string = session.userId
      const genId: string = req.query.genId
      const planInfo = await this.gmsService.planGenerated(userId, genId)
      return planInfo.status
    }

    @httpGet("/fetchPlanGenerationSteps")
    async getPlanGenerationSteps(req: express.Request): Promise<any> {
      return {
        title: "Brining it together",
        steps: [
          {
            title: "Creating a personalized plan",
            time: 3000
          },
          {
            title: "Creating an activity list",
            time: 3000
          },
          {
            title: "Taking you to the plan",
            time: 3000
          }
        ],
        apiUrl: "/goals/planGenerated",
        action: {
          actionType: "NAVIGATION",
          url: "curefit://activeplans"
        }
      }

    }
  }
  return UserGoalController
}

export default controllerFactory
