import { IFindQuery } from "@curefit/mongo-utils"
import * as express from "express"
import { ActivityState } from "@curefit/logging-common"
import { City } from "@curefit/location-common"
import { DeliveryGate } from "@curefit/eat-common"
import { FitbitUserInfo } from "@curefit/userinfo-common"
import { Location } from "@curefit/location-common"
import { ProductType } from "@curefit/product-common"
import { Session } from "@curefit/userinfo-common"
import { User, UserPreference, WhatsappEnum, Tenant, Genders } from "@curefit/user-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { UserDeliveryAddress } from "@curefit/eat-common"
import { VerticalType } from "@curefit/location-common"
import { UserStructuredAddress } from "@curefit/user-common"

import { MePageSideBarSection, PageType, IAnnouncement } from "@curefit/vm-common"
import { Feedback } from "@curefit/feedback-common"
import UserView from "./UserView"
import UserStatusView from "./UserStatusView"
import SessionView from "./../auth/SessionView"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { SortOrder } from "@curefit/mongo-utils"
import { ErrorFactory, GenericError } from "@curefit/error-client"
import EmailValidator from "./../common/validator/EmailValidator"
import UserNameValidator from "./../common/validator/UserNameValidator"
import { SignedUrlResponse } from "@curefit/user-client"
import { IUserService } from "@curefit/user-client"
import { controller, httpDelete, httpGet, httpPost, httpPut } from "inversify-express-utils"
import { Container, inject } from "inversify"
import { Constants } from "@curefit/base-utils"
import { IQueueService, SQS_CLIENT_TYPES } from "@curefit/sqs-client"
import IUserBusiness, { UpdateBrowseLocationPayload, UpdateBrowseLocationResponse, UserCity } from "./IUserBusiness"
import IRecommendationBusiness from "./IRecommendationBusiness"
import { IGateService } from "@curefit/delivery-client"
import UserAddressView from "./UserAddressView"
import UserStructuredAddressView from "./UserStructuredAddressView"
import AddressResponse from "./AddressResponse"
import { TimelineViewV1 } from "./TimelineView"
import DeliveryGateView from "./DeliveryGateView"
import DeliveryAreaView from "./DeliveryAreaView"
import ITimelineBusiness, { TimelineRequestParams } from "./ITimelineBusiness"
import { ICultServiceOld as ICultService, CULT_CLIENT_TYPES, IPulseService } from "@curefit/cult-client"
import { CultMembership, SubUserDetails, SubUserRequestPayload } from "@curefit/cult-common"
import AuthMiddleware from "../auth/AuthMiddleware"
import { TimeUtil, Timezone } from "@curefit/util-common"
import * as _ from "lodash"
import { eternalPromise } from "@curefit/util-common"
import { Logger, BASE_TYPES } from "@curefit/base"
import { CLSUtil } from "@curefit/base"
import AppUtil, {
    PlanTabsResponse,
    SleepConfig,
    DIY_REPORT_SUPPORTED,
    POST_RATING_INTERVENTION_ID,
    PARTIAL_DIY_WATCHED_INTERVENTION_ID,
    PARTIAL_LIVE_WATCHED_INTERVENTION_ID,
    CITY_SPLIT_LAUNCH_DATE, REST_OF_MUMBAI_CITY_ID, ONBOARDING_FORM_ID
} from "../util/AppUtil"
import { IFeedbackReadOnlyDao, FEEDBACK_MONGO_TYPES } from "@curefit/feedback-mongo"
import { Action, EmptyStateWidget, MyPaymentInfoView, SavedCardView, WidgetView } from "../common/views/WidgetView"
import { ICatalogueService, CATALOG_CLIENT_TYPES } from "@curefit/catalog-client"
import { IDeliveryAreaService, DELIVERY_CLIENT_TYPES } from "@curefit/delivery-client"
import IDeviceBusiness from "../device/IDeviceBusiness"
import CultUtil from "../util/CultUtil"
import TimelineUtil from "../util/TimelineUtil"
import { IActivityStoreReadonlyDao } from "@curefit/logging-models"
import { LOGGING_MODELS_TYPES } from "@curefit/logging-models"
import { IFoodShipmentReadonlyDao, SHIPMENT_MODELS_TYPES } from "@curefit/shipment-models"
import { PreferredLocation } from "@curefit/userinfo-common"
import { PlaceData } from "@curefit/location-common"
import { ActivityDS, ActivityInterval } from "@curefit/logging-common"
import AuthUtil from "../util/AuthUtil"
import { IQuestService, NewLevelBadges, QUEST_CLIENT_TYPES } from "@curefit/quest-client"
import { ActionUtil as EtherActionUtil } from "@curefit/base-utils"
import EatUtil from "../util/EatUtil"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { NotificationState } from "@curefit/iris-common"
import {
    UserTags, SendCampaignNotificationsRequest,
    ICampaignService
} from "@curefit/iris-client"
import { ActivePackViewV1 } from "./ActivePackViewBuilderV1"
import { SleepActivityV2 } from "@curefit/base-utils"
import { RecommendationView } from "./RecommendationViewBuilder"
import IInterventionBusiness from "../intervention/IInterventionBusiness"
import { Intervention, InterventionType } from "@curefit/intervention-common"
import { AtlasUtil, DiyContentDetail } from "../util/AtlasUtil"
import IFeedbackBusiness from "../ugc/IFeedbackBusiness"
import { CryptoUtil } from "@curefit/base-utils"
import { IDIYFulfilmentService, DIY_CLIENT_TYPES } from "@curefit/diy-client"
import { ISMSService, SMS_CLIENT_TYPES } from "@curefit/sms-client"
import { IAtlasService, SleepUpdatePayload, StepsData, ATLAS_CLIENT_TYPES } from "@curefit/atlas-client"
import { PaymentChannel, UserWalletBalanceResponse } from "@curefit/payment-common"
import { SavedCardEnabledChannels } from "@curefit/payment-common"
import { SavedCardList } from "@curefit/payment-common"
import { DIYContentConsumeResponse, DIYPack, DIYProduct } from "@curefit/diy-common"
import IAuthBusiness from "../auth/IAuthBusiness"
import { CareUtil, LAB_TEST_CYCLOP_SEGMENT } from "../util/CareUtil"
import HomePageConfig from "../page/HomePageConfig"
import { ICFAPICityService } from "../city/ICFAPICityService"
import { UserContext } from "@curefit/userinfo-common"
import { IActivityLoggingBusiness } from "../logging/ActivityLoggingBusiness"
import { IUserPlanBusiness, UserPlanView } from "../userplan/UserPlanBusiness"
import { ActionUtil } from "../util/ActionUtil"
import { FITCASH_CLIENT_TYPES } from "@curefit/fitcash-client/dist/src/ioc/FitcashClientTypes"
import { IFitcashService } from "@curefit/fitcash-client"
import { CacheHelper } from "../util/CacheHelper"
import ProfileUtil from "../../app/util/ProfileUtil"
import { WalletBalance } from "@curefit/fitcash-common"
import { ICultBusiness, PreferenceDetail } from "../cult/CultBusiness"
import { CULT_AREA_TOOL_TIP } from "../cult/ClassListViewBuilderV2"
import { GMF_CLIENT_TYPES, IGMFClient } from "@curefit/gmf-client"
import SupportPageConfig from "./SupportPageConfig"
import { Gender } from "@curefit/user-common"
import GenderValidator from "../common/validator/GenderValidator"
import DateValidator from "../common/validator/DateValidator"
import MePageSideBarPageConfig from "./MePageSideBarPageConfig"
import { QuickAction } from "./UserControllerModels"
import { UserActivityDetails } from "@curefit/quest-common"
import { AccordionSectionItem, MembershipItem, ScoreCardWidget } from "../common/views/ProfileWidgetView"
import * as util from "util"
import { RollbarService, ERROR_COMMON_TYPES } from "@curefit/error-common"
import { ICountryService, ICityService, LOCATION_TYPES, ICityReadWriteDao } from "@curefit/location-mongo"
import { AnnouncementBusiness, INTERVENTION_EVENTS } from "../announcement/AnnouncementBusiness"
import { AnnouncementId, AnnouncementState } from "../announcement/Announcement"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { capitalizeFirstLetter } from "@curefit/util-common"
import { Country, LocationUtil } from "@curefit/location-common"
import { FITCLUB_CLIENT_TYPES, IFitClubService } from "@curefit/fitclub-client"
import { SSOMiddleware } from "../sso/SSOMiddleware"
import { USER_CLIENT_TYPES } from "@curefit/user-client"
import { BaseWidget, IPageService, ISegmentService } from "@curefit/vm-models"
import { TrackYourHabitWidgetView } from "../page/vm/widgets/timeline/TrackYourHabitWidgetView"
import TransferMembershipViewBuilder from "../cult/TransferMembershipViewBuilder"
import { METRIC_TYPES, IMetricServiceClient as IMetricService } from "@curefit/metrics"
import { FitclubBusiness } from "../fitclub/FitclubBusiness"
import { GearService, GEARVAULT_CLIENT_TYPES } from "@curefit/gearvault-client"
import { UserAllocation, UserAssignment } from "@curefit/hamlet-common"
import { AtlasActivityService } from "../atlas/AtlasActivityService"
import { ParsedPhoneNumber, PhoneUtil } from "@curefit/base-utils/dist/src/PhoneUtil"

import AppLayoutBuilder from "./AppLayoutBuilder"
import { PAYMENT_TYPES, IPaymentClient, IUserGiftCardWalletService } from "@curefit/payment-client"
export type EMAIL_TYPE = "PRIMARY" | "WORK"
import { ActionType, AppLayout } from "@curefit/apps-common"
import { ICFApiSessionBusiness } from "../auth/SessionBusiness"
import { AnnouncementView } from "../announcement/AnnouncementViewBuilder"
import IUserPreferenceBussiness from "./IUserPreferenceBussiness"
import INavBarBusiness, { VerticalsWithHighlightInformation, VerticalInfo } from "./INavBarBusiness"
import { Verticals } from "@curefit/location-common"
import { ILoggingService, LOGGING_CLIENT_TYPES } from "@curefit/logging-client"
import { CFUserProfile } from "../page/vm/CFUserProfile"
import { InAppNotificationsService, IRIS_CLIENT_TYPES } from "@curefit/iris-client"
import CFAPIJavaService from "../CFAPIJavaService"
import { GearState, ZipcodeServiceabilityInfo } from "@curefit/gear-common"
import { ErrorCodes } from "../error/ErrorCodes"
import { IRewardService, REWARD_CLIENT_TYPES } from "@curefit/reward-client"

import { FetchUtilV2 } from "@curefit/base"
import { Sources, ExternalSourceUtil } from "../externalSource/ExternalSourceUtil"
import { CustomEventEmitter, eventName } from "../externalSource/CustomEventEmitter"
import { PromiseCache } from "../util/VMUtil"
import { ICaptchaBusiness } from "../referral/CaptchaBusiness"
import { BlockingType } from "../metrics/models/BlockingType"
import MetricsUtil from "../metrics/MetricsUtil"
import { EventInterventionMappingBusiness } from "../intervention/EventInterventionMappingBusiness"
import {
    IMediaGatewayService,
    MEDIA_GATEWAY_CLIENT_TYPES,
    MediaType,
    ObjectAcl
} from "@curefit/media-gateway-js-client"
import { UserYearlyReport, UserYearlyReportBusiness } from "./UserYearlyReportBusiness"
import IAuthMiddleware from "../auth/IAuthMiddleware"
import { UserYearReport2022Business } from "./UserYearReport2022Business"
import { UserYearReport2023Business } from "./UserYearReport2023Business"
import { YearEndQuizViewProps } from "@curefit/apps-common/dist/src/page/yearReportQuiz/types"
import EnterpriseUtil from "../util/EnterpriseUtil"
import { TataNeuUtil } from "../util/TataNeuUtil"
import { IThirdPartyService, TataLoyaltyPointsResponse, THIRD_PARTY_CLIENT_TYPES } from "@curefit/third-party-integrations-client"
import { SEGMENTATION_CLIENT_TYPES, IUserSegmentClient, SegmentationCacheClient, ISegmentationCacheClient } from "@curefit/segmentation-service-client"
import OrderViewBuilder from "../order/OrderViewBuilder"
import { ICfsFormCache } from "../cfs/CfFormCache"
import { LocationPreferenceRequestEntity, LocationPreferenceResponseEntity, UpdateStatusResponseEntity } from "../util/UserUtil"
import { SubmitFDMetricsPayload } from "@curefit/logging-common/dist/src/models/ActivityStore"
import { CACHE_CLIENT_TYPES, CacheService, wrapWithMethodCache } from "@curefit/cache-client"
import { LocationUtil as lu } from "../util/LocationUtil"
import DIYPackService from "../digital/diy/DIYPackService"
import CultsportUtil from "../util/CultsportUtil"
import { AttributeAction } from "@curefit/rashi-client"
import { PlatformSegmentWrapper } from "../common/PlatformSegmentWrapper"
import { UserSegment } from "@curefit/gmf-common"
import { ICultstoreShopifyService } from "../cultstoreShopify/CultstoreShopifyInterfaces"
import { RedlockAccess } from "@curefit/lock-utils"
import { MultiRedisAccess, REDIS_TYPES } from "@curefit/redis-utils"

const crypto = require("crypto")

interface ContentConsumedPayload {
    packId: string
    uid: string
    activityId: string
    activityType: string
    contentId: string
    startTime: number
    endTime: number
    videoStartTime: number
    videoEndTime: number
    state: string
    duration?: number
    skipReport?: boolean
    platform: string
    subType?: string
}

interface CoreMotionData {
    s: number,
    c: number,
    t: any
}

export interface FitbitAuth {
    authenticated: boolean,
    url: string
}

interface ContentConsumedResult {
    consumptionId?: string,
    consumptionStatus: boolean
    nextSessiondetail: DiyContentDetail
    action?: Action
    interventionAction?: Action
}

interface AppSleepData {
    date: string
    sleepIntervals: ActivityInterval[]
    sleepMeta: any
    sourceType?: string,
    sourceAlgo?: string
}

interface AppSleepRawData {
    date: string
    data: any
}

interface SleepDetailResponse {
    sleepIntervals: ActivityInterval[]
    hasReviewed: boolean
    status: ActivityState
    source: {
        sourceId: string
        sourceName: string
    }
}
interface UserResponse {
    area?: DeliveryAreaView
    address?: UserAddressView
    placeData?: PlaceData
    cityName?: string
    user: UserView
    session: SessionView
    fitbit?: FitbitAuth
    sleepConfig: SleepConfig
    careEnabled: boolean
    careMappedActions: any[]
    widgets?: WidgetView[]
    genders?: {
        title: string
        id: string
    }[]
}

export interface CountryData {
    countryId: string
    countryCallingCode: string
    name: string,
    validationCountryCode?: string
    phoneLoginSupported?: boolean
    flagImage?: string
    phoneNumberMaxLength?: number
}

interface CoreMotionPayload {
    data: CoreMotionData[],
    prevSyncTime: number,
    currentSyncTime: number,
    deviceId: string
}

interface GetCitiesResult {
    countries: CountryView[],
    detectedInfo?: { cityId: string, countryId: string },
    selectedCity?: CityView
    cityTextInfo?: {
        popularCityText: string
        otherCityText: string
    }
}

type AreasResult = {
    title: string;
    subtitle: string;
    cityName: string;
    backgroundImage: string;
    whiteBackgroundImage: string;
    areaList: AreaView[]
}

type AreaView = {
    title: string;
    areaId: string;
    cityId: string;
    areaData: CityView;
}

interface CountryView {
    title: string
    countryId: string
    cities: CityView[]
}
interface CityView {
    isSelected: boolean
    cityId: string
    countryId: string
    timezone: Timezone
    name: string
    image: string
    areaName?: string
    isPopular?: boolean
    action?: Action
    section?: {
        title: string
        subTitle: string
        items: {
            icon: string
            message: string
        }[]
    }
    lat?: number
    lon?: number
}

export type Area = {
    cityId: string
    areaId: string
    cultAreaId: number
    name: string
    lat?: number
    lon?: number
}

export type TabTypes = "PLAN" | VerticalType

export interface CityResponseV2 {
    appTabs: TabTypes[],
    verticals?: VerticalInfo[],
    isMoreToBeHighlighted?: Boolean
    supportedVerticals?: VerticalType[]
    appLayout?: AppLayout
}

interface MicroAppConfig {
    [key: string]: { enabled: boolean }
}

export interface UserStatusResponse extends NewLevelBadges {
    intervention?: {
        interventionId: string
        interventionType: InterventionType
    }
    user: UserStatusView
    sideBar: MePageSideBarSection[]
    feedbackId?: string
    activitySyncTime?: string
    showCitySelection?: boolean
    levelShareText?: string
    activePackSubscribed?: boolean
    cityId: string
    countryId: string
    timezone: Timezone
    cityName: string
    detectedCity: DetectedCity
    session: SessionView
    tlaSemusnoc?: string,
    redirectUri?: string,
    isMoreToBeHighlighted?: Boolean,
    planTabItem?: PlanTabsResponse,
    goalPlanExists: boolean,
    appTabs: TabTypes[],
    quickActions?: QuickAction[],
    analyticsData?: { [experiment: string]: string }
    countriesData: CountryData[],
    sleepConfig: SleepConfig,
    defaultPageId: string,
    cityRepresentativeLat: number
    cityRepresentativeLon: number
    verticals: VerticalInfo[]
    appLayout: AppLayout
    announcementData?: AnnouncementView
    diySubscriptions?: ActivePackViewV1[]
    microapps?: MicroAppConfig
    showcfsform?: boolean
    formAction?: Action
}

interface DetectedCity {
    city: string
    country: string
}

interface AddActivity {
    title: string
    image?: string
    action: Action
    gradientColors?: string[]
}

export type OTP_MEDIUM = "sms" | "call"
export const ANDROID_SLEEP_VERSION = 6.5
const SLICE_LIMIT = 4000
const AUTO_SUBSCRIBE_PACKS: any = {
    "DIYPACK001": true,
    "MEDPACK001": true
}

const userFormSegmentNameMap: {[formId: string]: string} = {
    "NPS_V3_GX_GYM": "nps journey elite pro d30",
    "NPS_V3_PLAY": "CSI Auto trigger Play",
    "NPS_V3_GYM_PT": "CSI Auto trigger PT",
    "NPS_V3_LIVE": "CSI Auto trigger Live",
    "NPS_V3_BOOTCAMP": "CSI Auto trigger Bootcamp",
    "NPS_V3_TransformPlus": "CSI Auto trigger TRANSFORM Plus",
    "NPS_V3_TRANSFORM": "CSI Auto trigger TRANSFORM",
    "d15-gx-survey": "CSI Auto trigger GX",
    "d15-gym-survey": "CSI Auto trigger gym/hybrid",
    "gx_1st_class_survey": "GX 1st class survey for new users",
    "gym_onboarding_cfs_feedback_form": "Gym Onboarding Both Session Feedback Through CFS Dec 2024",
    "au2x4kz1t16r": "Streak understanding segment for in-app survey"
}

export function controllerFactory(kernel: Container) {
    @controller("/user",
        kernel.get<SSOMiddleware>(CUREFIT_API_TYPES.SSOMiddleware).validateSsoSession,
        kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class UserController {
        private lock: RedlockAccess
        constructor(
            @inject(IRIS_CLIENT_TYPES.InAppNotificationsService) protected inAppNotificationService: InAppNotificationsService,
            @inject(BASE_TYPES.FetchUtilV2) private fetchHelper: FetchUtilV2,
            @inject(LOCATION_TYPES.CityReadWriteDao) private cityReadWriteDao: ICityReadWriteDao,
            @inject(CUREFIT_API_TYPES.UserPreferenceBusiness) private userPreferenceBussiness: IUserPreferenceBussiness,
            @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
            @inject(LOCATION_TYPES.CityService) private cityService: ICityService,
            @inject(LOCATION_TYPES.CountryService) private countryService: ICountryService,
            @inject(CUREFIT_API_TYPES.CFAPICityService) private CFAPICityService: ICFAPICityService,
            @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
            @inject(CULT_CLIENT_TYPES.MindFitService) private mindFitService: ICultService,
            @inject(CULT_CLIENT_TYPES.PulseService) private pulseService: IPulseService,
            @inject(ATLAS_CLIENT_TYPES.AtlasService) private atlasService: IAtlasService,
            @inject(DELIVERY_CLIENT_TYPES.DeliveryAreaService) private deliveryAreaService: IDeliveryAreaService,
            @inject(FEEDBACK_MONGO_TYPES.FeedbackReadOnlyDao) private feedbackDao: IFeedbackReadOnlyDao,
            @inject(CUREFIT_API_TYPES.TimelineBusinessV1) private timelineBusinessV1: ITimelineBusiness,
            @inject(CUREFIT_API_TYPES.TimelineBusinessV4) private timelineBusinessV4: ITimelineBusiness,
            @inject(CUREFIT_API_TYPES.UserBusiness) private userBusiness: IUserBusiness,
            @inject(CUREFIT_API_TYPES.NavBarBusiness) private navBarBusiness: INavBarBusiness,
            @inject(CUREFIT_API_TYPES.UserPlanBusiness) private userPlanBusiness: IUserPlanBusiness,
            @inject(CUREFIT_API_TYPES.AuthBusiness) private authBusiness: IAuthBusiness,
            @inject(CUREFIT_API_TYPES.SessionService) private sessionBusiness: ICFApiSessionBusiness,
            @inject(CUREFIT_API_TYPES.RecommendationBusiness) private recommendationBusiness: IRecommendationBusiness,
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
            @inject(SQS_CLIENT_TYPES.QueueService) private queueService: IQueueService,
            @inject(DELIVERY_CLIENT_TYPES.GateService) private gateService: IGateService,
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(CUREFIT_API_TYPES.DeviceBusiness) private deviceBusiness: IDeviceBusiness,
            @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalougeService: ICatalogueService,
            @inject(LOGGING_MODELS_TYPES.ActivityStoreReadonlyDao) private activityDao: IActivityStoreReadonlyDao,
            @inject(SHIPMENT_MODELS_TYPES.FoodShipmentReadonlyDao) private foodShipmentReadonlyDao: IFoodShipmentReadonlyDao,
            @inject(QUEST_CLIENT_TYPES.IQuestService) private questService: IQuestService,
            @inject(CUREFIT_API_TYPES.InterventionBusiness) private interventionBusiness: IInterventionBusiness,
            @inject(CUREFIT_API_TYPES.FeedbackBusiness) private feedbackBusiness: IFeedbackBusiness,
            @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) protected DIYFulfilmentService: IDIYFulfilmentService,
            @inject(CUREFIT_API_TYPES.HomePageConfig) private homePageConfig: HomePageConfig,
            @inject(BASE_TYPES.ClsUtil) private clsUtil: CLSUtil,
            @inject(CUREFIT_API_TYPES.ActivityLoggingBusiness) private activityLoggingBusiness: IActivityLoggingBusiness,
            @inject(GMF_CLIENT_TYPES.IGMFClient) private gmfClient: IGMFClient,
            @inject(SMS_CLIENT_TYPES.SMSService) private smsService: ISMSService,
            @inject(FITCASH_CLIENT_TYPES.FitcashService) private fitcashService: IFitcashService,
            @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
            @inject(CUREFIT_API_TYPES.CultBusiness) private cultBusiness: ICultBusiness,
            @inject(CUREFIT_API_TYPES.SupportPageConfig) private supportPageConfig: SupportPageConfig,
            @inject(CUREFIT_API_TYPES.MePageSideBarPageConfig) private mePageSideBarPageConfig: MePageSideBarPageConfig,
            @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
            @inject(CUREFIT_API_TYPES.AnnouncementBusiness) private announcementBusiness: AnnouncementBusiness,
            @inject(FITCLUB_CLIENT_TYPES.IFitClubService) private fitclubService: IFitClubService,
            @inject(REWARD_CLIENT_TYPES.IRewardService) private rewardService: IRewardService,
            @inject(CUREFIT_API_TYPES.TransferMembershipViewBuilder) private transferMembershipViewBuilder: TransferMembershipViewBuilder,
            @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
            @inject(METRIC_TYPES.MetricServiceClient) private metricService: IMetricService,
            @inject(CUREFIT_API_TYPES.AuthMiddleware) private authMiddleWare: IAuthMiddleware,
            @inject(CUREFIT_API_TYPES.FitclubBusiness) private fitclubBusiness: FitclubBusiness,
            @inject(GEARVAULT_CLIENT_TYPES.GearService) private gearService: GearService,
            @inject(CUREFIT_API_TYPES.AtlasActivityService) private atlasActivityService: AtlasActivityService,
            @inject(CUREFIT_API_TYPES.AppLayoutBuilder) private appLayoutBuilder: AppLayoutBuilder,
            @inject(PAYMENT_TYPES.IPaymentClient) protected paymentClient: IPaymentClient,
            @inject(LOGGING_CLIENT_TYPES.LoggingService) private loggingService: ILoggingService,
            @inject(IRIS_CLIENT_TYPES.IrisCampaignService) private campaignService: ICampaignService,
            @inject(CUREFIT_API_TYPES.CFAPIJavaService) private cFAPIJavaService: CFAPIJavaService,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
            @inject(CUREFIT_API_TYPES.PageService) protected pageService: IPageService,
            @inject(CUREFIT_API_TYPES.SegmentService) protected segmentService: ISegmentService,
            @inject(CUREFIT_API_TYPES.ExternalSourceUtil) private externalSourceUtil: ExternalSourceUtil,
            @inject(CUREFIT_API_TYPES.CustomEventEmitter) private customEventEmitter: CustomEventEmitter,
            @inject(CUREFIT_API_TYPES.CaptchaBusiness) private captchaBusiness: ICaptchaBusiness,
            @inject(CUREFIT_API_TYPES.MetricsUtil) private metricsUtil: MetricsUtil,
            @inject(CUREFIT_API_TYPES.EventInterventionMappingBusiness) private eventInterventionMappingBusiness: EventInterventionMappingBusiness,
            @inject(MEDIA_GATEWAY_CLIENT_TYPES.IMediaGatewayService) private mediaGatewayClient: IMediaGatewayService,
            @inject(CUREFIT_API_TYPES.UserYearlyReportBusiness) private userYearlyReportBusiness: UserYearlyReportBusiness,
            @inject(CUREFIT_API_TYPES.UserQuiz2021Business) private userYearReport2022Business: UserYearReport2022Business,
            @inject(CUREFIT_API_TYPES.UserYearReport2023Business) private userYearReport2023Business: UserYearReport2023Business,
            @inject(CUREFIT_API_TYPES.EnterpriseUtil) private enterpriseUtil: EnterpriseUtil,
            @inject(THIRD_PARTY_CLIENT_TYPES.ThirdPartyService) private thirdPartyService: IThirdPartyService,
            @inject(SEGMENTATION_CLIENT_TYPES.UserSegmentClient) public segmentationClient: IUserSegmentClient,
            @inject(CUREFIT_API_TYPES.OrderViewBuilder) private orderViewBuilder: OrderViewBuilder,
            @inject(CUREFIT_API_TYPES.CfsFormCache) private cfsFormCache: ICfsFormCache,
            @inject(CUREFIT_API_TYPES.DIYPackService) private diyPackService: DIYPackService,
            @inject(CULT_CLIENT_TYPES.CultService) private cultService: ICultService,
            @inject(PAYMENT_TYPES.IUserGiftCardWalletService) private giftCardWalletService: IUserGiftCardWalletService,
            @inject(SEGMENTATION_CLIENT_TYPES.SegmentationCacheClient) public segmentationCacheClient: ISegmentationCacheClient,
            @inject(CUREFIT_API_TYPES.CultstoreShopifyService) private cultstoreShopifyService: ICultstoreShopifyService,
            @inject(REDIS_TYPES.MultiRedisAccess) private multiRedisAccess: MultiRedisAccess,
        ) {
            this.lock = new RedlockAccess(this.multiRedisAccess, logger, "CFAPI-CACHE")
        }

        @httpGet("/getUser")
        async getUser(req: express.Request, res: express.Response): Promise<any> {
            const session: Session = await this.createGuestUserSession(req, res)
            const userId: string = session.userId
            const userPromise = this.userService.getUser(userId)

            const user = await userPromise
            return { user: user }
        }

        @httpGet("/me")
        async findMe(req: express.Request, res: express.Response): Promise<UserResponse> {

            let session: Session = await this.createGuestUserSession(req, res)
            const userId: string = session.userId
            const userAgent: UserAgent = session.userAgent
            const deviceId: string = session.deviceId
            const osName: string = req.headers["osname"] as string
            const appVersion: string = req.headers["appversion"] as string
            const getFeedbackId: string = req.query.feedback
            const userContext: UserContext = req.userContext as UserContext
            const tz = userContext.userProfile.timezone
            const expanded = req.query.expanded
            const tenant: Tenant = AppUtil.getTenantFromReq(req)

            this.logger.info("USER_ME_USERAGENT: " + userAgent + ", osName: " + osName + ", appVersion: " + appVersion)
            if (!userContext.userProfile.promiseMapCache) {
                userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
            }

            // update cookie if not present
            if (userAgent !== "APP" && !AuthUtil.isAuthTokenCookiePresent(req)) {
                this.logger.info("Cookie not found for user accessing through browser")
                AuthUtil.setCookies(session.st, session.at, deviceId, req, res)
            }

            // update consumeSalt in session if not present
            let sessionUpdatePromise: Promise<Session>
            if (_.isNil(session.sessionData.tlaSemusnoc)) {
                session.sessionData.tlaSemusnoc = crypto.randomUUID()
                sessionUpdatePromise = this.sessionBusiness.updateSessionData(session.at, session.sessionData, session.isNotLoggedIn)
            } else {
                this.sessionBusiness.updateSessionData(session.at, session.sessionData, session.isNotLoggedIn)
            }

            const lat: number = req.query.lat
            const lon: number = req.query.lon
            const codePushVersion: string = req.headers["codepushversion"] && req.headers["codepushversion"] !== "undefined" ? req.headers["codepushversion"] as string : undefined


            // Fire and Forget
            this.deviceBusiness.updateSessionInformation(session.deviceId, session.sessionData, tenant, Number(appVersion), codePushVersion, lat, lon)

            const userPromise = this.userService.getUser(userId)
            const userCity: UserCity = await lu.getUserCityFromReq(req, this.cityService, this.logger, this.CFAPICityService, this.deviceBusiness)
            const addressPromise = this.getUserAddresses(userId)
            const preferredLocationPromise = this.userBusiness.getPreferredLocation(userContext, session.userId, session.sessionData, lon, lat, undefined, true)
            const fitcashPromise = this.fitcashService.balance(userId, userContext.userProfile.city.country.currencyCode)
            let feedbackPromise = undefined
            const oneDayBefore = TimeUtil.getMomentNow(tz).subtract(1, "days").toDate()
            if (getFeedbackId === "true") {
                feedbackPromise = this.feedbackDao.find({
                    condition: { userId: { $in: session.userId }, createdDate: { $gte: oneDayBefore } },
                    sortField: "dateTime",
                    sortOrder: SortOrder.ASC,
                })
            }

            const user = await userPromise
            const city = userCity.city

            const isfitbitAuthenticated = await this.atlasActivityService.isFitbitAuthenticated(userId)
            const fitbitInfo = {
                authenticated: isfitbitAuthenticated,
                url: isfitbitAuthenticated ? "curefit://fitbit/logout" : this.fitbitLoginParams(req).url
            }
            const feedbackResult = await feedbackPromise
            const feedback: Feedback = feedbackResult ? feedbackResult.find(feedback => (feedback.rating === "NOT_RATED")) : undefined

            const addressViews = await addressPromise
            const preferredLocation = await preferredLocationPromise
            const sleepConfig = AppUtil.getSleepConfig(appVersion, osName)

            if (!_.isNil(sessionUpdatePromise)) {
                session = await sessionUpdatePromise
            }

            let fitcashBalance: number = 0
            try {
                const fitcashBalanceResponse: WalletBalance = await fitcashPromise
                fitcashBalance = fitcashBalanceResponse.balance / 100
            } catch (error) {
                const genericErr: GenericError = new GenericError(({ message: error.message }))
                genericErr.statusCode = 500
                this.rollbarService.handleError(genericErr)
            }

            const response: UserResponse = {
                "user": new UserView(user, session.isNotLoggedIn, addressViews, false, feedback?.feedbackId, fitcashBalance),
                "session": new SessionView(session),
                cityName: city ? city.name : undefined,
                fitbit: fitbitInfo,
                sleepConfig: sleepConfig,
                careEnabled: CareUtil.isCareSupported(osName, Number(appVersion), city.cityId),
                careMappedActions: CareUtil.addCareMappedActions(Number(appVersion), _.get(city, "cityId", ""), user.isInternalUser),
                genders: Genders,
            }
            if (osName.toLowerCase() === "android" || osName.toLowerCase() === "ios") {
                const userScoreCardWidgetPromise = this.getScoreMetricsWidget(userId, userContext)
                const cultSummaryAndLivePackPromise = this.userBusiness.getCultSummaryAndLivePack(userContext)
                const classRemindersPreferencePromise = eternalPromise(this.cultBusiness.getClassRemindersPreference(userContext, city.cityId, userContext.userProfile.userId, "FITNESS"))
                const isFlutterAccountSegmentEnabled = await AppUtil.doesUserBelongToFlutterAccountViewSegment(this.segmentService, userContext)
                const isEnterpriseUser = (isFlutterAccountSegmentEnabled && AppUtil.isEnterpriseCultPassCorpCardSupported(userContext)) ? await AppUtil.doesUserBelongToEnterpriseUserSegment(this.segmentService, userContext) : false
                const membershipItems: MembershipItem[] = await cultSummaryAndLivePackPromise
                if (isEnterpriseUser) {
                    const corpCards = await this.enterpriseUtil.getCorpMembershipCard(userId)
                    if (corpCards != null) {
                        const isOptumUser = await AppUtil.doesUserBelongToOptumCorpSegment(this.segmentService, userContext)
                        if (isOptumUser) {
                            membershipItems.unshift(corpCards)
                        } else {
                            membershipItems.push(corpCards)
                        }
                    }
                    const onePassMemberships = await this.userBusiness.getOnePassMembershipItem(userContext)
                    if (!_.isEmpty(onePassMemberships)) {
                        membershipItems.push(...onePassMemberships)
                    }

                }
                let userScoreCardWidget: ScoreCardWidget = undefined
                if (session.isNotLoggedIn !== true && tenant !== Tenant.MINDFIT) {
                    userScoreCardWidget = await userScoreCardWidgetPromise
                }
                let cultPreference: PreferenceDetail = undefined
                if (tenant !== Tenant.MINDFIT) {
                    cultPreference = (await classRemindersPreferencePromise).obj
                }
                const isMembershipListWidgetSupported: boolean = userContext.sessionInfo.appVersion >= 9.39
                response.widgets = await ProfileUtil.addMePageWidgets(req,
                    fitcashBalance, city, fitbitInfo, session.isNotLoggedIn, userScoreCardWidget,
                    this.cultService, cultPreference, expanded, this.metricService, this.fitclubBusiness, this.cityService, this.enterpriseUtil, membershipItems, isMembershipListWidgetSupported, undefined, this.segmentService)
            }

            this.addPreferredLocation(preferredLocation, response)
            return response
        }

        private async getScoreMetricsWidget(userId: string, userContext: UserContext): Promise<ScoreCardWidget> {
            const userActivityDetails: UserActivityDetails = await this.getUserDetailedStatsPromise(userId, userContext)
            if (!_.isNil(userActivityDetails)) {
                return ProfileUtil.getScoreView(userActivityDetails)
            }
        }

        private async getUserDetailedStatsPromise(userId: string, userContext: UserContext): Promise<UserActivityDetails> {
            try {
                const tz = userContext.userProfile.timezone
                const today = TimeUtil.todaysDate(tz)
                const todayMinus30Days = TimeUtil.getMomentNow(tz).subtract(29, "day").format("YYYY-MM-DD")
                return await this.questService.getUserDetailedStats(userId, todayMinus30Days, today)
            } catch (e) {
                this.logger.error("Error while trying to contact quest Service !! ", e)
                const genericErr: GenericError = new GenericError({ message: "Error while trying to contact quest Service!" })
                genericErr.statusCode = 500
                this.rollbarService.handleError(genericErr)
                return
            }
        }

        private addPreferredLocation(preferredLocation: PreferredLocation, response: UserResponse) {
            if (preferredLocation.area) {
                // page.area = new DeliveryAreaView(preferredLocation.area)
                response.placeData = {
                    placeId: "DUMMY",
                    name: preferredLocation.area.subArea,
                    address: ""
                }
            }
            if (preferredLocation.address) {
                response.address = new UserAddressView(preferredLocation.address)
            }
            if (preferredLocation.placeData) {
                response.placeData = preferredLocation.placeData
            }
            if (preferredLocation.city) {
                response.placeData = {
                    placeId: "DUMMY",
                    name: preferredLocation.city.name,
                    address: ""
                }
            }
        }

        // Takes a new phoneNumber and assoiates with the user account.
        @httpPost("/setPhoneNumber")
        async setPhoneNumber(req: express.Request, res: express.Response): Promise<{ user: UserView, session: SessionView }> {
            const session: Session = req.session
            const newPhoneNumber: number = req.body.phoneNumber
            const medium: OTP_MEDIUM = req.body.medium || "sms"
            const appVersion: number = req.headers["appversion"] as string ? Number(req.headers["appversion"] as string) : undefined
            const osName: string = req.headers["osname"] as string
            const userAgent: UserAgent = AuthUtil.getUserAgent(req)
            const captchaResponse = req.body.captchaResponse
            const countryCallingCode: string = req.body.countryCallingCode || "+91"
            const userContext = req?.userContext
            const apiKey = AuthUtil.getApiKeyFromReq(req)
            return this.authBusiness.setPhoneNumber(res, session.userId, newPhoneNumber, medium, appVersion,
                osName, userAgent, countryCallingCode, session, captchaResponse, userContext, apiKey).then(user => {
                return {
                    "user": new UserView(user, session.isNotLoggedIn),
                    "session": new SessionView(session)
                }
            })
        }

        @httpPost("/validatePhoneNumber")
        validatePhoneNumber(req: express.Request): Promise<ParsedPhoneNumber> {
            const phoneNumber = req.body.phoneNumber
            const countryCode = req.body.countryCode
            const validationResponse = PhoneUtil.parsePhoneNumber(phoneNumber, countryCode)
            return new Promise((resolve) => resolve(validationResponse))
        }

        @httpPost("/resendOTP")
        resendOTP(req: express.Request): Promise<{ user: UserView, session: SessionView }> {
            const session: Session = req.session
            const medium: OTP_MEDIUM = req.body.medium || "sms"
            if (session.sessionData.failedOtpTry) {
                if (session.sessionData.failedOtpTry > 3 &&
                    (new Date().getTime() - session.sessionData.lastFailedOtpTime.getTime()) / (60 * 1000) < 5) {
                    throw this.errorFactory.withCode(ErrorCodes.OTP_LIMIT_REACHED_ERR, 400).withDebugMessage("OTP limit reached").build()
                }
            }
            return this.userService.resendPhoneOtp(session.userId, medium).then(user => {
                return {
                    "user": new UserView(user, session.isNotLoggedIn),
                    "session": new SessionView(session)
                }
            })
        }

        @httpPost("/resendEmailOtp")
        resendEmailOTP(req: express.Request): Promise<{ user: UserView, session: SessionView }> {
            const session: Session = req.session
            return this.userService.resendEmailOtp(session.userId).then(user => {
                return {
                    "user": new UserView(user, session.isNotLoggedIn),
                    "session": new SessionView(session)
                }
            })
        }

        @httpPost("/resendWorkEmailOtp")
        resendWorkEmailOtp(req: express.Request): Promise<{ user: UserView, session: SessionView }> {
            const session: Session = req.session
            return this.userService.resendWorkEmailOtp(session.userId).then(user => {
                return {
                    "user": new UserView(user, session.isNotLoggedIn),
                    "session": new SessionView(session)
                }
            })
        }

        @httpPost("/deleteAccountSendOtp")
        async deleteAccountSendOtp(req: express.Request, res: express.Response): Promise<boolean> {
            const userContext = req.userContext as UserContext
            if (userContext.sessionInfo.osName === "android" || userContext.sessionInfo.osName === "Android") {
                throw this.errorFactory.withCode(ErrorCodes.CANNOT_DELETE_ACCOUNT, 404).withDebugMessage("cannot delete account. Please contact support").build()
            }
            const session: Session = req.session
            const tenant: Tenant = AppUtil.getTenantFromReq(req)
            const userAgent: UserAgent = AuthUtil.getUserAgent(req)
            const medium: OTP_MEDIUM = req.body.medium ?? "sms"
            const appVersion: string = req.headers["appversion"] as string
            const { captchaResponse, countryCallingCode, phone } = req.body

            // this.logger.info(`deleteAccountSendOtp reqHeaders: ${JSON.stringify(req.headers)} requestBody: ${JSON.stringify(req.body)}`)

            if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
                const verifyCaptchaResp = await this.captchaBusiness.verifyCaptchaLogin(captchaResponse, AppUtil.callSource(AuthUtil.getApiKeyFromReq(req)))
                // this.authBusiness.temporaryLogsForDetectingCaptchaError(req, verifyCaptchaResp)
                this.logger.info(`/deleteAccountSendOtp  verifyCaptchaResp`, verifyCaptchaResp)
                if (verifyCaptchaResp.success && verifyCaptchaResp.score > 0.6) {
                    return await this.userService.deleteAccountSendOtp(session.userId, tenant, medium)
                } else {
                    this.metricsUtil.reportBlockedRequest(BlockingType.CAPTCHA_FAILURE)
                    this.logger.error(`AuthController:Failed to verify captcha on /deleteAccountSendOtp Error: Failed to verify. Please try again`)
                    this.metricsUtil.reportCaptchaFailure(userAgent, req.originalUrl.split("?")[0])
                    throw this.errorFactory.withCode(ErrorCodes.CAPTCHA_INVALID, 400).withDebugMessage("captcha not valid").build()
                }
            }
            return await this.userService.deleteAccountSendOtp(session.userId, tenant, medium)
        }

        @httpPost("/deleteAccountVerifyOtp")
        async deleteAccountVerifyOtp(req: express.Request, res: express.Response): Promise<boolean> {
            const session: Session = req.session
            const tenant: Tenant = AppUtil.getTenantFromReq(req)
            const userAgent: UserAgent = AuthUtil.getUserAgent(req)
            const appVersion: string = req.headers["appversion"] as string
            const { phone, countryCallingCode, otp, captchaResponse } = req.body
            const deviceId: string = this.authBusiness.validateAndGetDeviceId(req)

            // this.logger.info("deleteAccountVerifyOtp Request Body: " + JSON.stringify(req.body))

            if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
                const verifyCaptchaResp = await this.captchaBusiness.verifyCaptchaLogin(captchaResponse, AppUtil.callSource(AuthUtil.getApiKeyFromReq(req)))
                // this.authBusiness.temporaryLogsForDetectingCaptchaError(req, verifyCaptchaResp)

                this.logger.info(`/deleteAccountVerifyOtp  verifyCaptchaResp`, verifyCaptchaResp)

                if (verifyCaptchaResp.success && verifyCaptchaResp.score > 0.6) {
                    return await this.userService.deleteAccountVerifyOtp(session.userId, otp, tenant, deviceId)
                } else {
                    this.metricsUtil.reportBlockedRequest(BlockingType.CAPTCHA_FAILURE)
                    this.logger.error(`AuthController:Failed to verify captcha on /deleteAccountVerifyOtp Error: Failed to verify. Please try again`)
                    this.metricsUtil.reportCaptchaFailure(userAgent, req.originalUrl.split("?")[0])
                    throw this.errorFactory.withCode(ErrorCodes.CAPTCHA_INVALID, 400).withDebugMessage("captcha not valid").build()
                }
            } else {
                return await this.userService.deleteAccountVerifyOtp(session.userId, otp, tenant, deviceId)
            }
        }

        @httpPost("/resendDeleteAccountOtp")
        resendDeleteAccountOtp(req: express.Request): Promise<{ user: UserView, session: SessionView }> {
            const session: Session = req.session
            return this.userService.resendDeleteAccountOtp(session.userId).then(user => {
                return {
                    "user": new UserView(user, session.isNotLoggedIn),
                    "session": new SessionView(session)
                }
            })
        }

        @httpPost("/verifyPhoneNumber")
        verifyPhoneNumber(req: express.Request): Promise<{ user: UserView, session: SessionView }> {
            const session: Session = req.session
            const otp: string = req.body.otp

            return this.userService.verifyPhoneOtp(session.userId, otp).then(cultUserResponse => {
                if (!_.isEmpty(cultUserResponse.mergeData) && cultUserResponse.mergeData.mergeStatus) {
                    // change userid as new userId
                    this.logger.info(`merging user with userId ${cultUserResponse.mergeData.oldUserId} to ${cultUserResponse.mergeData.newUserId}`)
                    session.userId = cultUserResponse.mergeData.newUserId
                }
                session.sessionData.failedOtpTry = 0
                return this.sessionBusiness.updateSessionDataWithUserId(session.at, session.sessionData, session.userId).then(updatedSession => {
                    return {
                        "user": new UserView(cultUserResponse.user, session.isNotLoggedIn),
                        "session": new SessionView(updatedSession)
                    }
                })
            }).catch(reason => {
                session.sessionData.failedOtpTry = (session.sessionData.failedOtpTry) ? session.sessionData.failedOtpTry + 1 : 1
                session.sessionData.lastFailedOtpTime = new Date()
                return this.sessionBusiness.updateSessionData(session.at, session.sessionData).then(updatedSession => {
                    return Promise.reject<{ user: UserView, session: SessionView }>(reason)
                })
            })
        }

        @httpPost("/verifyemail")
        async verifyEmail(req: express.Request, res: express.Response): Promise<User> {
            const appVersion: string = req.headers["appversion"] as string
            const deviceId: string = req.headers["deviceid"] as string
            const token: string = req.body["token"] as string

            const response = await this.userService.verifyEmail(token)
            return response
        }

        @httpPost("/verifyworkemail")
        async verifyWorkEmail(req: express.Request, res: express.Response): Promise<User> {
            const appVersion: string = req.headers["appversion"] as string
            const deviceId: string = req.headers["deviceid"] as string
            const token: string = req.body["token"] as string

            const response = await this.userService.verifyWorkEmail(token)
            return response
        }

        @httpPost("/setName")
        setName(req: express.Request): Promise<{ user: UserView, session: SessionView, action?: Action }> {
            const session: Session = req.session
            const userContext = req.userContext as UserContext
            const nameArr: string[] = req.body.name.split(" ")
            let firstName: string = nameArr[0]
            let lastName: string = ""
            if (nameArr.length > 1) {
                for (let i = 1; i < nameArr.length - 1; i++) {
                    firstName += " "
                    firstName += nameArr[i]
                }
                lastName = nameArr[nameArr.length - 1]
            }
            const action: Action = {
                actionType: "NAVIGATION",
                url: `curefit://userform?formId=${ONBOARDING_FORM_ID}`,
                formId: ONBOARDING_FORM_ID
            }
            return new UserNameValidator().validate(firstName, "First Name").then(() => {
                return this.userService.setName(session.userId, firstName, lastName).then(user => {
                    return AppUtil.isUserOnboardingFormSupported(this.segmentService, userContext, false).then((isUserOnboardingFormSupported) => {
                        return {
                            "user": new UserView(user, session.isNotLoggedIn),
                            "session": new SessionView(session),
                            "action": isUserOnboardingFormSupported ? action : undefined
                        }
                    })
                })
            })
        }

        @httpPost("/updateUserPreference")
        async updateUserPreference(req: express.Request): Promise<VerticalInfo[] | VerticalsWithHighlightInformation | UserPreference> {
            const userContext = req.userContext as UserContext
            const selectedVerticals: VerticalType[] = req.body.selectedVerticals ? req.body.selectedVerticals : []
            const session: Session = req.session
            const userPreference: UserPreference = await this.userPreferenceBussiness.updateSelectedVerticals(selectedVerticals, userContext)
            if (selectedVerticals.length) {
                const verticalInfo: VerticalsWithHighlightInformation = await this.navBarBusiness.getUserAndSupportedVerticalsInfo(userContext, userContext.userProfile.city, session.userId)
                if (!this.navBarBusiness.isMoreVerticalsSupported(userContext)) {
                    return verticalInfo.verticals
                }
                const supportedVerticals: VerticalType[] = verticalInfo.verticals.map(vertical => vertical.verticalType)
                let appLayout: AppLayout
                try {
                    appLayout = await this.appLayoutBuilder.fetchPageLayout(userContext, supportedVerticals)
                } catch (err) {
                    this.logger.error("Failed to fetch app layout " + err)
                }
                return {
                    ...verticalInfo,
                    appLayout
                }
            }
            return userPreference
        }

        @httpPost("/bottomBarInteraction")
        async bottomBarInteraction(req: express.Request): Promise<VerticalsWithHighlightInformation> {
            const barInteraction = req.body.barInteraction
            const session: Session = req.session
            const userContext = req.userContext as UserContext
            await this.navBarBusiness.updateNavBarInteraction(session.userId, session, barInteraction, userContext)
            return this.navBarBusiness.getUserAndSupportedVerticalsInfo(userContext, userContext.userProfile.city, session.userId)

        }

        @httpPost("/updateProfile")
        async updateProfile(req: express.Request, res: express.Response): Promise<{ "user": UserView; "session": SessionView; }> {
            const session: Session = req.session
            const name: string = req.body.name
            const gender: Gender = req.body.gender
            const birthday: string = req.body.birthday
            let firstName: string, lastName: string
            let userNameValidatorPromise
            let genderValidatorPromise
            let dobValidator
            const promises = []
            if (name) {
                const nameArr: string[] = name.trim().split(" ")
                firstName = nameArr[0]
                if (nameArr.length > 1) {
                    for (let i = 1; i < nameArr.length; i++) {
                        lastName = lastName ? lastName + nameArr[i] : nameArr[i]
                    }
                    lastName = lastName.trimRight()
                    userNameValidatorPromise = new UserNameValidator().validate(firstName, "First Name")
                    promises.push(userNameValidatorPromise)
                }
            }

            if (gender) {
                genderValidatorPromise = new GenderValidator().validate(gender)
                promises.push(genderValidatorPromise)
            }
            if (birthday) {
                dobValidator = new DateValidator().validate(birthday)
                promises.push(dobValidator)
            }

            return Promise.all(promises).then(() => {
                return this.userService.updateProfile(session.userId, firstName, lastName, gender, birthday).then(user => {
                    return {
                        "user": new UserView(user, session.isNotLoggedIn),
                        "session": new SessionView(session)
                    }
                })
            })
        }



        @httpPost("/setEmail")
        setEmail(req: express.Request): Promise<{ user: UserView, session: SessionView }> {
            const session: Session = req.session
            const newEmail = req.body.email
            return this.authBusiness.setWorkEmail(session.userId, newEmail).then(user => {
                return {
                    "user": new UserView(user, session.isNotLoggedIn),
                    "session": new SessionView(session)
                }
            })
        }

        @httpPost("/setWorkEmail")
        setWorkEmail(req: express.Request): Promise<{ user: UserView, session: SessionView }> {
            const session: Session = req.session
            const newEmail = req.body.email
            return this.authBusiness.setWorkEmail(session.userId, newEmail).then(user => {
                return {
                    "user": new UserView(user, session.isNotLoggedIn),
                    "session": new SessionView(session)
                }
            })
        }

        @httpGet("/address")
        getAddresses(req: express.Request): Promise<UserAddressView[]> {
            const session: Session = req.session
            return this.getUserAddresses(session.userId)
        }

        private getUserAddresses(userId: string, verticalQuery?: string, listingBrand?: string): Promise<UserAddressView[]> {
            return this.userBusiness.getAddresses(userId, verticalQuery, listingBrand).then(async addresses => {
                if (listingBrand === "GEAR_FIT") {
                    for (let i = 0; i < addresses.length; i += 1) {
                        let address = addresses[i]
                        address = await this.correctGearAddress(address, userId)
                    }
                }
                const addressViews: UserAddressView[] = []
                addresses.forEach(address => {
                    addressViews.push(new UserAddressView(address))
                })
                return addressViews
            })
        }

        private updateGearAddressContext(user: User, addresses: UserAddressView[], gearPreferredAddressId: string): UserAddressView[] {
            addresses.forEach(a => {
                if (_.isEmpty(a.name)) a.name = user.firstName + " " + user.lastName
                if (_.isEmpty(a.phoneNumber)) a.phoneNumber = user.phone
                if (_.isEmpty(a.addressType)) a.addressType = "HOME"
                a.isDefaultGearAddress = a.addressId === gearPreferredAddressId
            })
            return addresses
        }

        private async updateGearBrowseAddress(session: Session, addressViews: UserAddressView[]): Promise<string> {
            /*
             * can give undefined if the user doesn't have any addresses
             */
            let gearPreferredAddressId = session?.sessionData?.locationPreferenceData?.addressId ?? undefined
            if (_.isEmpty(gearPreferredAddressId) && !_.isEmpty(addressViews)) {
                gearPreferredAddressId = addressViews[0].addressId
                await this.userBusiness.updateBrowseLocation(session, {addressId: gearPreferredAddressId, listingBrand: "GEAR_FIT"}, Tenant.CULTSPORT_APP)
            }
            return gearPreferredAddressId
        }

        private correctGearAddressOrdering(userAddresses: UserAddressView[], gearPreferredAddressId: string): UserAddressView[] {
            let indexOfPreferredAddress: number = -1, preferredAddress: UserAddressView = undefined
            const finalAddressOrdering = []
            for (let i = 0; i < userAddresses.length; i = i + 1) {
                const address = userAddresses[i]
                if (address.addressId === gearPreferredAddressId) {
                    /*
                     * considering addressId is unique, if should run at max once
                     */
                    indexOfPreferredAddress = i
                    preferredAddress = address
                } else {
                    finalAddressOrdering.push(address)
                }
            }
            if (indexOfPreferredAddress !== -1) {
                finalAddressOrdering.unshift(preferredAddress)
            }
            return finalAddressOrdering

        }

        @httpGet("/address/v1")
        async getAddressesV1(req: express.Request): Promise<AddressResponse> {
            const session: Session = req.session
            const userContext = req.userContext as UserContext
            const vertical = req.query.vertical ?? undefined
            let addressViews = await this.getUserAddresses(session.userId, "ALL", vertical)
            if (vertical === "GEAR_FIT" && addressViews?.length > 0) {
                const session: Session = req.session
                const gearPreferredAddressId: string = await this.updateGearBrowseAddress(session, addressViews)
                addressViews = this.correctGearAddressOrdering(addressViews, gearPreferredAddressId)
                addressViews = this.updateGearAddressContext(await userContext.userPromise, addressViews, gearPreferredAddressId)
            }
            return new AddressResponse(addressViews, [])
        }

        @httpPost("/updateUserPreferenceCity")
        async updateUserPreferenceCity(req: express.Request): Promise<any> {
            const tenant: Tenant = AppUtil.getTenantFromReq(req)
            const cities: any = this.cityService.listCities(tenant)
            const today = new Date()
            const priorDate = new Date().setDate(today.getDate() - 31)
            cities.forEach((city: any) => {
                const appSupportedVerticals: Verticals[] = []
                const webSupportedVerticals: Verticals[] = []
                city.supportedVerticals.forEach((vertical: VerticalType) => {
                    appSupportedVerticals.push({
                        "verticalType": vertical,
                        startAt: {
                            timezone: "Asia/Kolkata",
                            date: new Date(priorDate)
                        }
                    })
                })
                city.supportedVerticalsWeb ? city.supportedVerticalsWeb.forEach((vertical: VerticalType) => {
                    webSupportedVerticals.push({
                        "verticalType": vertical,
                        startAt: {
                            timezone: "Asia/Kolkata",
                            date: new Date(priorDate)
                        }
                    })
                }) : null
                city.appSupportedVerticals = appSupportedVerticals
                city.webSupportedVerticals = webSupportedVerticals
                this.cityReadWriteDao.findOneAndUpdate({ cityId: city.cityId }, city, { upsert: true })
            })
        }

        @httpGet("/areaAndAddress")
        async getAreaAndAddresses(req: express.Request): Promise<{
            addresses: UserAddressView[],
            kiosks: UserAddressView[],
            areas: DeliveryAreaView[],
            city: Action
        }> {
            const session: Session = req.session
            const userContext: UserContext = req.userContext
            const lat: number = req.query.lat
            const lon: number = req.query.lon
            const listingBrand = req.query.listingBrand
            const tenant: Tenant = AppUtil.getTenantFromReq(req)

            const addressesPromise = this.userBusiness.getAddresses(session.userId, undefined)
            const subDeliveryAreasPromise = listingBrand === "WELLNESS" ? Promise.resolve([]) : this.deliveryAreaService.listDeliverySubAreas(session.sessionData.cityId)
            await Promise.all([addressesPromise, subDeliveryAreasPromise])

            const subDeliveryAreas = await subDeliveryAreasPromise
            const deliveryAreaViews: DeliveryAreaView[] = []
            subDeliveryAreas.sort((areaA, areaB) => {
                return areaA.subArea.localeCompare(areaB.subArea)
            })
            subDeliveryAreas.forEach(subDeliveryArea => {
                deliveryAreaViews.push(new DeliveryAreaView(subDeliveryArea))
            })
            const addresses = await addressesPromise
            const addressViews: UserAddressView[] = []
            addresses.forEach(address => {
                addressViews.push(new UserAddressView(address))
            })

            const userAddress = addressViews.filter(addressView => { return addressView.addressType !== "KIOSK" })
            // check to not show kiosk on web
            const userAgentType: UserAgent = req.session.userAgent
            let kioskAddress
            if (userAgentType === "APP" && !["WHOLE_FIT", "STORE", "WELLNESS"].includes(listingBrand)) {
                kioskAddress = addressViews.filter(addressView => { return addressView.addressType === "KIOSK" })
            }
            const citySelection = ActionUtil.showCityAction(true)
            const userCity: UserCity = await lu.getUserCityFromReq(req, this.cityService, this.logger, this.CFAPICityService, this.deviceBusiness)
            const city: City = userCity.city
            citySelection.title = city.name
            return { addresses: userAddress, kiosks: kioskAddress, areas: deliveryAreaViews, city: citySelection }
        }

        @httpGet("/gateAndAddress")
        async getGateAndAddresses(req: express.Request): Promise<AddressResponse> {
            const session: Session = req.session
            const lat: number = req.query.lat
            const lon: number = req.query.lon
            const listingBrand = req.query.listingBrand
            const verticalQuery = req.query.verticalQuery
            const userContext = req.userContext as UserContext
            let addresses = await this.userBusiness.getAddresses(session.userId, verticalQuery, listingBrand)

            // let nearbyGatesPromise
            // if (lat && lon)
            //     nearbyGatesPromise = this.gateService.getNearbyGates(lat, lon, 200, 50
            // const gates = await nearbyGatesPromise
            let gearZipCodeServiceabilityInfo: ZipcodeServiceabilityInfo = null
            if (listingBrand === "GEAR_FIT") {
                const fixedAddresses: UserDeliveryAddress[] = []
                for (let address of addresses) {
                    address = await this.correctGearAddress(address, userContext.userProfile.userId)
                    fixedAddresses.push(address)
                }
                addresses = fixedAddresses
                gearZipCodeServiceabilityInfo = await this.gearZipCodeServiceabilityInfo(addresses)
            }


            // Added backfilling of augumented structed
            if (AppUtil.isCultSportWebApp(userContext)) {
                const gearStates: GearState[] = await this.getCultsportServicableStates()
                const states = gearStates.map(state => state.name.toLowerCase())
                addresses = (await Promise.all(addresses.filter(address => address?.addressType !== "KIOSK").map(async (address) => {
                    if (!_.isEmpty(address.structuredAddress)) {
                        return address
                    }
                    try {
                        address = await this.userBusiness.augmentStructuredAddress(
                            address.userId,
                            address.addressId,
                            address,
                            undefined,
                            "GEAR"
                        )
                        return address
                    } catch {
                        // ignore error
                        return undefined
                    }
                }))).filter(address => address && !_.isEmpty(address?.structuredAddress) && states.includes(address?.structuredAddress?.state?.toLowerCase()) )
            }

            let addressViews: UserAddressView[] = []
            addresses.forEach(address => {
                addressViews.push(new UserAddressView(address, gearZipCodeServiceabilityInfo))
            })

            if (listingBrand === "GEAR_FIT") {
                const gearPreferredAddressId: string = await this.updateGearBrowseAddress(session, addressViews)
                addressViews = this.correctGearAddressOrdering(addressViews, gearPreferredAddressId)
                addressViews = this.updateGearAddressContext(await userContext.userPromise, addressViews, gearPreferredAddressId)
            }

            const gateViews: DeliveryGateView[] = []

            // if (gates) {
            //     const activeGates = _.filter(gates, gate => {
            //         return gate.status === "LIVE"
            //     })

            //     activeGates.forEach(gate => {
            //         gateViews.push(new DeliveryGateView(gate))
            //     })
            // }

            return new AddressResponse(addressViews, gateViews)
        }

        private async correctGearAddress(address: UserDeliveryAddress, userId: string): Promise<UserDeliveryAddress> {
            try {
                let correctedAddress = _.cloneDeep(address)
                if (CultsportUtil.doesGearAddressContainFloorString(address.addressLine1)) {
                    correctedAddress = CultsportUtil.correctGearDeliveryAddressLine1(address, this.logger)
                }
                if (_.isEmpty(address.locality)) {
                    correctedAddress.locality = CultsportUtil.extraLocalityFromAddressLine2(correctedAddress)
                }
                this.logger.info("for addressId: " + correctedAddress.addressId + " " + "corrected address: " + JSON.stringify(correctedAddress))
                if (!_.isEqual(address, correctedAddress)) {
                    this.logger.info("for addressId: " + correctedAddress.addressId + ". persisting corrected address.")
                    address = correctedAddress
                    await this.userBusiness.updateAddress(userId, correctedAddress)
                }
            } catch (e) {
                this.logger.error("Gear address correction failed for addressId: " + address.addressId + " with error: " + JSON.stringify(e))
            }
            return address
        }

        @httpPost("/address")
        async addAddress(req: express.Request): Promise<UserAddressView> {
            const session: Session = req.session
            let address: UserDeliveryAddress
            if (await this.userBusiness.getActiveAddressCount(session.userId) >= 10) {
                throw this.errorFactory.withCode(ErrorCodes.MAX_ADDRESS_ADDITION_LIMIT_ERR, 400).withDebugMessage("Max limit of addresses added limit reached").build()
            }
            if (req.body.kioskScanCode) {
                address = await this.userBusiness.addKiosk(session.userId, req.body.kioskScanCode)
            } else {
                const newAddress: UserDeliveryAddress = req.body
                newAddress.userId = session.userId

                if (_.isEmpty(newAddress.addressLine1)) {
                    throw this.errorFactory.withCode(ErrorCodes.ADDRESS_LINE1_INCOMPLETE_ERR, 400).withDebugMessage("empty address line 1").build()
                }

                // If the structured address is sent via the API, we should append it to addressLine2 because
                // that is what is displayed across the board.
                if (_.isEmpty(newAddress.locality)) {
                    /*
                     * locality holds the older content of addressLine2
                     * Useful in edit cases where we want to edit just the content of addressLine2
                     */
                    newAddress.locality = `${newAddress.addressLine2}`
                }
                if (!_.isEmpty(newAddress?.structuredAddress)) {
                    newAddress.addressLine2 = `${newAddress.addressLine2}, ${newAddress?.structuredAddress?.locality}, ${newAddress?.structuredAddress?.city}, ${newAddress?.structuredAddress?.state} - ${newAddress?.structuredAddress?.pincode} `
                }
                address = await this.userBusiness.addAddress(session.userId, newAddress, newAddress?.pincode, AppUtil.getAppTenantFromReq(req))
            }
            return new UserAddressView(address)
        }

        @httpPut("/address/:id")
        updateAddress(req: express.Request): Promise<UserAddressView[]> {
            const session: Session = req.session
            const addressId: string = req.params.id
            const newAddress: UserDeliveryAddress = req.body
            newAddress.userId = session.userId
            if (_.isEmpty(newAddress.locality)) {
                newAddress.locality = `${newAddress.addressLine2}`
            }
            if (!_.isEmpty(newAddress?.structuredAddress)) {
                newAddress.addressLine2 = `${newAddress.addressLine2}, ${newAddress?.structuredAddress?.locality}, ${newAddress?.structuredAddress?.city}, ${newAddress?.structuredAddress?.state} - ${newAddress?.structuredAddress?.pincode} `
            }

            return this.userBusiness.updateAddress(session.userId, newAddress).then(addresses => {
                const addressViews: UserAddressView[] = []
                addresses.forEach(address => {
                    addressViews.push(new UserAddressView(address))
                })
                return addressViews
            })
        }

        @httpDelete("/address/:id")
        deleteAddress(req: express.Request): Promise<UserAddressView[]> {
            const session: Session = req.session
            const addressId: string = req.params.id
            const tenant: Tenant = AppUtil.getTenantFromReq(req)
            return this.userBusiness.deleteAddress(session, addressId, tenant).then(addresses => {
                const addressViews: UserAddressView[] = []
                addresses.forEach(address => {
                    addressViews.push(new UserAddressView(address))
                })
                return addressViews
            })
        }

        @httpGet("/structured-address/list")
        async getStructuredAddresses(req: express.Request): Promise<UserStructuredAddressView[]> {
            const userId: string = req.session.userId
            const addresses: UserStructuredAddress[] = await this.userBusiness.getStructuredAddresses(userId)
            const view: UserStructuredAddressView[] = []
            addresses.forEach((address: UserStructuredAddress) => {
                view.push(new UserStructuredAddressView(address))
            })
            return view
        }

        @httpGet("/structured-address/:id")
        async getStructuredAddress(req: express.Request): Promise<UserStructuredAddressView> {
            const userId: string = req.session.userId
            const addressId: string = req.params.id
            return new UserStructuredAddressView(await this.userBusiness.getStructuredAddress(userId, addressId))
        }

        @httpPost("/structured-address")
        async addStructuredAddress(req: express.Request): Promise<UserStructuredAddressView> {
            const userId: string = req.session.userId
            return new UserStructuredAddressView(await this.userBusiness.addStructuredAddress(userId, req.body))
        }

        @httpPut("/structured-address/:id")
        async updateStructuredAddress(req: express.Request): Promise<UserStructuredAddressView> {
            const userId: string = req.session.userId
            const addressId: string = req.params.id
            return new UserStructuredAddressView(await this.userBusiness.updateStructuredAddress(userId, addressId, req.body))
        }

        @httpDelete("/structured-address/:id")
        async deleteStructuredAddress(req: express.Request, res: express.Response): Promise<void> {
            const userId: string = req.session.userId
            const addressId: string = req.params.id
            if (await this.userBusiness.deleteStructuredAddress(userId, addressId)) {
                res.status(204).send()
            } else {
                res.status(404).send()
            }
        }

        @httpGet("/fitbitLoginParams")
        fitbitLoginParams(req: express.Request): any {
            const url = AppUtil.fitbitLoginParams(req.session.userId)
            return { url: url }
        }

        @httpGet("/fitbit/logout")
        async fitbitLogout(req: express.Request): Promise<FitbitUserInfo> {
            const userId = req.session.userId
            return this.atlasActivityService.fitbitLogout(userId)
        }

        @httpGet("/fitbit/isAuthenticated")
        async isFitbitAuthenticated(req: express.Request): Promise<any> {
            const userId = req.session.userId
            const isAuthenticated = await this.atlasActivityService.isFitbitAuthenticated(userId)
            return {
                isAuthenticated: isAuthenticated
            }
        }

        @httpPost("/update/fd/activities")
        public async triggerUserActivitiesUpdate(req: express.Request): Promise<boolean> {
            const userId = req.session.userId
            const metricsPayload: SubmitFDMetricsPayload = req.body
            return this.atlasActivityService.updateActivitiesForUser(userId, metricsPayload)
        }

        @httpPost("/inAppNotification/:id")
        async deleteInAppNotification(req: express.Request): Promise<boolean> {
            const notificationId: string = req.params.id
            const userId = req.session.userId
            const cityId: string = req.session.sessionData.cityId
            const payload: { state: NotificationState, productType?: ProductType } = req.body
            const userContext = req.userContext as UserContext
            // TODO: have to fix this after modelling various in app notification properly
            if (notificationId === CULT_AREA_TOOL_TIP) {
                const result = await this.cultBusiness.updateUserAcknowledged(userContext, cityId, userId, payload.productType)
                return result.status
            }
            const genericResponse = await this.inAppNotificationService.updatePropsByNotificationId(notificationId, payload)
            if (genericResponse.status === "SUCCESS") {
                return true
            } else {
                return false
            }
        }

        @httpPost("/fitnessData/android")
        fitnessDataAndroid(req: express.Request, res: express.Response): Promise<{ success: boolean }> {
            const fitnessData: any = req.body
            const session: Session = req.session
            const attributes: Map<string, any> = new Map<string, any>()

            attributes.set("userId", session.userId)
            attributes.set("type", "FITNESS_ANDROID")
            attributes.set("X-Request-Id", res.get(CLSUtil.REQUEST_ID_FIELD))
            const queueName = Constants.getSQSQueue("USER_COACH_ACTIVITY")
            return this.queueService.sendMessage(queueName, fitnessData, attributes).then(done => {
                return { success: true }
            }).catch(err => {
                return { success: false }
            })
        }

        @httpPost("/fitnessData/ios")
        fitnessDataiOS(req: express.Request, res: express.Response): Promise<{ success: boolean }> {
            const fitnessData: any = req.body
            const session: Session = req.session
            const attributes: Map<string, any> = new Map<string, any>()

            attributes.set("userId", session.userId)
            attributes.set("type", "FITNESS_IOS")
            attributes.set("X-Request-Id", res.get(CLSUtil.REQUEST_ID_FIELD))
            const queueName = Constants.getSQSQueue("USER_COACH_ACTIVITY")
            return this.queueService.sendMessage(queueName, fitnessData, attributes).then(done => {
                return { success: true }
            }).catch(err => {
                return { success: false }
            })
        }

        @httpGet("/sleep/:date")
        async getSleepDetail(req: express.Request, res: express.Response): Promise<SleepDetailResponse> {
            const date = req.params.date
            const session: Session = req.session
            const userContext: UserContext = req.userContext as UserContext
            const userId = session.userId
            const findQuery: IFindQuery = {
                condition: { "idempotenceKey": "SLEEP-" + date + "-" + userId }
            }
            const tz = userContext.userProfile.timezone
            const sleepForToday: ActivityDS = await (this.activityDao.findOne(findQuery.condition))
            const diffInDays = TimeUtil.diffInDays(tz, date, TimeUtil.todaysDateWithTimezone(tz))
            const hasReviewed = diffInDays > 15 || sleepForToday.meta.sleep.sourceType === "USER"
            const sleepIntervals = !_.isEmpty(sleepForToday.meta.sleep.sleepIntervals) ? sleepForToday.meta.sleep.sleepIntervals : [{
                startTime: sleepForToday.meta.sleep.startTime,
                endTime: sleepForToday.meta.sleep.endTime
            }]

            const sourceTypeSleep = sleepForToday.meta.deviceId === "USER-UPDATE-YES" && !_.isNil(sleepForToday.meta.sleep.referenceSourceType) ? sleepForToday.meta.sleep.referenceSourceType : sleepForToday.meta.sleep.sourceType
            return {
                hasReviewed,
                status: sleepForToday.score > 0 ? "DONE" : "SKIPPED",
                sleepIntervals,
                source: (_.isNil(sleepForToday.meta.sleep.sourceType)) ? null : {
                    sourceId: sourceTypeSleep,
                    sourceName: TimelineUtil.camelcaseSource(sourceTypeSleep)
                }
            } as SleepDetailResponse
        }

        @httpPost("/sleep")
        async appSleepUpdate(req: express.Request, res: express.Response): Promise<{ success: boolean, widget: BaseWidget }> {
            const session: Session = req.session
            const osName: string = req.headers["osname"] as string
            const deviceId: string = req.headers["deviceid"] as string
            const userId = session.userId
            const appVersion: string = req.headers["appversion"] as string
            const appSleepDataPayload: AppSleepData[] = req.body
            // this.logger.info("Sleep update req " + JSON.stringify(req.body))
            const userContext: UserContext = req.userContext as UserContext
            const tz = userContext.userProfile.timezone

            const user = await this.userCache.getUser(userId)
            if (!user.isInternalUser && osName && osName.toLowerCase() === "android" && Number(appVersion) < ANDROID_SLEEP_VERSION) {
                return
            }
            const sleepUpdatePromises: Promise<boolean>[] = []
            const sleepActivityStorePromises: Promise<boolean>[] = []

            for (let i = 0; i < appSleepDataPayload.length; i++) {
                const appSleepData = appSleepDataPayload[i]
                if (appSleepData) {
                    appSleepData.sleepIntervals = appSleepData.sleepIntervals.sort((a, b) => {
                        const diff = a.startTime - b.startTime
                        return diff > 0 ? 1 : diff < 0 ? -1 : diff
                    })

                    if (!this.isDateValidForSleepUpdate(appSleepData.date, userContext)) {
                        continue
                    }

                    if (_.isEmpty(appSleepData.sleepIntervals))
                        continue

                    const numSleepIntervals = appSleepData.sleepIntervals.length
                    const sleepUpdatePayload: SleepUpdatePayload = {
                        platform: osName,
                        deviceId: deviceId,
                        startTime: appSleepData.sleepIntervals[0].startTime,
                        endTime: appSleepData.sleepIntervals[numSleepIntervals - 1].endTime,
                        sourceType: _.isNil(appSleepData.sourceType) ? "CUREFIT-APP" : appSleepData.sourceType,
                        meta: JSON.stringify(appSleepData),
                        sourceAlgo: appSleepData.sourceAlgo
                    }

                    const message: SleepActivityV2 = {
                        platform: sleepUpdatePayload.platform,
                        device_id: sleepUpdatePayload.deviceId,
                        start_time: sleepUpdatePayload.startTime,
                        end_time: sleepUpdatePayload.endTime,
                        sourceType: sleepUpdatePayload.sourceType,
                        meta: sleepUpdatePayload.meta,
                        duration_ms: sleepUpdatePayload.endTime - sleepUpdatePayload.startTime,
                        sleep_date: appSleepData.date,
                        uuid: userId,
                        id: null,
                        referenceSourceType: null,
                        sourceAlgo: appSleepData.sourceAlgo,
                        source: sleepUpdatePayload.sourceType,
                        timezone: tz
                    }
                    // await this.loggingService.putActivityByActivityType(userId, "ACTIVITY_SLEEP_V2", message)
                    const attributes = new Map()
                    attributes.set("userId", userId)
                    attributes.set("type", "ACTIVITY_SLEEP_V2")
                    this.queueService.sendMessageAsync(Constants.getSQSQueue("ACTIVITYSTORE"), message, attributes)
                    // sleepUpdatePromises.push(this.atlasActivityService.updateSleepData(tz, userId, appSleepData.date, sleepUpdatePayload))
                }
            }
            await Promise.all(sleepUpdatePromises)
            await Promise.all(sleepActivityStorePromises)
            const trackHabitWidget = new TrackYourHabitWidgetView()
            const widget = await trackHabitWidget.buildView(this.serviceInterfaces, req.userContext, req.query)
            return { "success": true, "widget": widget }
        }

        @httpPost("/sleep/rawData")
        async appSleepRawData(req: express.Request, res: express.Response): Promise<void> { }

        isDateValidForSleepUpdate(date: string, userContext: UserContext) {
            const tz = userContext.userProfile.timezone
            const now = TimeUtil.getMomentNow(tz)
            const sleepDate = TimeUtil.getDefaultMomentForDateString(date, tz)
            const twoMonthsBack = now.clone().subtract(2, "months")
            // If sleep date is not within the last 2 month, fail the request.
            if (!(now.isAfter(sleepDate) && sleepDate.isAfter(twoMonthsBack))) {
                this.logger.error(`Invalid sleep date ${date}`)
                return false
            }
            return true
        }

        @httpPost("/sleep/:date")
        async updateSleepData(req: express.Request, res: express.Response): Promise<boolean> {
            const date = req.params.date
            const userContext: UserContext = req.userContext as UserContext
            if (!this.isDateValidForSleepUpdate(date, userContext)) {
                return
            }
            const session: Session = req.session
            const osName: string = req.headers["osname"] as string
            const deviceId: string = req.headers["deviceid"] as string
            const userId = session.userId
            const payload = req.body
            const tz = userContext.userProfile.timezone
            const sleepUpdatePayload: SleepUpdatePayload = {
                platform: osName,
                deviceId: deviceId,
            }
            // this.logger.info("Sleep payload " + (_.isEmpty(payload) ? "Empty payload" : JSON.stringify(payload)))
            if (!_.isEmpty(payload) && payload.isCorrect !== true) {
                sleepUpdatePayload.startTime = parseInt(payload.startTime)
                sleepUpdatePayload.endTime = parseInt(payload.endTime)
                sleepUpdatePayload.sourceType = "USER"
                sleepUpdatePayload.isCorrect = false
                sleepUpdatePayload.referenceSourceType = payload.referenceSourceType
            }
            else {

                const findQuery: IFindQuery = {
                    condition: { "idempotenceKey": "SLEEP-" + date + "-" + userId }
                }
                const sleepForToday: ActivityDS = await (this.activityDao.findOne(findQuery.condition))
                // this.logger.info("sleep going for updation : " + JSON.stringify(sleepForToday))
                if (!_.isEmpty(sleepForToday)) {
                    sleepUpdatePayload.startTime = sleepForToday.meta.sleep.startTime
                    sleepUpdatePayload.endTime = sleepForToday.meta.sleep.endTime
                    sleepUpdatePayload.isCorrect = true
                    sleepUpdatePayload.sourceType = "USER"
                    sleepUpdatePayload.referenceSourceType = sleepForToday.meta.sleep.sourceType
                    if (!_.isEmpty(sleepForToday.meta.sleep.sleepIntervals)) {
                        const appSleepData: AppSleepData = {
                            date: date,
                            sleepIntervals: sleepForToday.meta.sleep.sleepIntervals,
                            sleepMeta: undefined
                        }
                        sleepUpdatePayload.meta = JSON.stringify(appSleepData)
                    }
                }
            }
            this.logger.info("payload : " + JSON.stringify(sleepUpdatePayload))

            const message: SleepActivityV2 = {
                platform: sleepUpdatePayload.platform,
                device_id: sleepUpdatePayload.deviceId,
                start_time: sleepUpdatePayload.startTime,
                end_time: sleepUpdatePayload.endTime,
                sourceType: sleepUpdatePayload.sourceType,
                meta: sleepUpdatePayload.meta,
                duration_ms: sleepUpdatePayload.endTime - sleepUpdatePayload.startTime,
                sleep_date: date,
                uuid: userId,
                id: null,
                referenceSourceType: null,
                sourceAlgo: sleepUpdatePayload.sourceAlgo,
                source: sleepUpdatePayload.sourceType,
                timezone: tz
            }
            // await this.atlasActivityService.updateSleepData(tz, userId, date, sleepUpdatePayload)
            await this.loggingService.putActivityByActivityType(userId, "ACTIVITY_SLEEP_V2", message)
        }


        @httpPost("/fitnessData/ios/coreMotion")
        async coreMotionData(req: express.Request, res: express.Response): Promise<{ success: boolean }> {
            const payload: CoreMotionPayload = req.body
            let data: CoreMotionData[] = payload.data
            let prevSyncTime: number = payload.prevSyncTime
            const currentSyncTime: number = undefined
            const session: Session = req.session
            const attributes: Map<string, any> = new Map<string, any>()
            attributes.set("userId", session.userId)
            attributes.set("type", "FITNESS_IOS_CORE_MOTION")
            attributes.set("X-Request-Id", res.get(CLSUtil.REQUEST_ID_FIELD))
            const queueName = Constants.getSQSQueue("USER_COACH_ACTIVITY")
            while (data.length > 0) {
                const sliceLength = Math.min(data.length, SLICE_LIMIT)
                const isLastFrame = sliceLength === data.length ? true : false
                const dataToSend = data.slice(0, sliceLength)
                const splittedPayload: CoreMotionPayload = {
                    data: dataToSend,
                    prevSyncTime: prevSyncTime,
                    currentSyncTime: isLastFrame ? payload.currentSyncTime : dataToSend[dataToSend.length - 1].s,
                    deviceId: payload.deviceId
                }
                await this.queueService.sendMessage(queueName, splittedPayload, attributes)
                data = isLastFrame ? [] : data.slice(sliceLength)
                prevSyncTime = dataToSend[dataToSend.length - 1].s
            }
            return { "success": true }
        }

        @httpPost("/stepsData/")
        stepsData(req: express.Request, res: express.Response): Promise<{ success: boolean, widget: BaseWidget }> {
            const stepsData: StepsData[] = req.body
            const session: Session = req.session
            const userId: string = session.userId
            const osName: string = req.header("osname") as string
            // const appVersion: string = req.headers["appversion"] as string
            const deviceId: string = req.header("deviceid") as string
            this.logger.debug(`osName: ${osName} deviceId: ${deviceId}`)
            return this.atlasActivityService.updateStepsData(userId, osName, deviceId, stepsData).then(async result => {
                const trackHabitWidget = new TrackYourHabitWidgetView()
                const widget = await trackHabitWidget.buildView(this.serviceInterfaces, req.userContext, req.query)
                return { "success": result, "widget": widget }
            })
        }

        @httpPost("/content/consumed")
        async contentConsumed(req: express.Request): Promise<ContentConsumedResult> {
            const session: Session = req.session
            const payload: ContentConsumedPayload = req.body
            const checksum: string = req.headers["checksum"] as string
            const userContext = req.userContext as UserContext
            const selectedCity = {
                city: userContext.userProfile.city.cityId,
                country: userContext.userProfile.city.countryId
            }
            const detectedCity = userContext.sessionInfo.sessionData.detectedCity
            const source = AppUtil.callSourceFromContext(userContext)

            if (!payload.activityType || !payload.activityId) {
                this.logger.error("Invalid data")
                return { consumptionStatus: false, consumptionId: undefined, nextSessiondetail: undefined }
            }

            if (payload.activityType === "poplive") {
                const attributes = new Map()
                attributes.set("userId", userContext.userProfile.userId)
                attributes.set("type", "POP_LIVE")
                this.queueService.sendMessageAsync(Constants.getSQSQueue("ACTIVITYSTORE"), payload, attributes)
                return { consumptionStatus: true, consumptionId: undefined, nextSessiondetail: undefined }
            }
            const tz = userContext.userProfile.timezone
            const endDateTime = payload.endTime ? new Date(payload.endTime) : undefined
            const result: DIYContentConsumeResponse = <DIYContentConsumeResponse>await this.DIYFulfilmentService.consumeDIYContentForUser(payload.activityId, session.userId,
                new Date(payload.startTime), endDateTime, payload.duration, payload.uid, tz, selectedCity, detectedCity, source)
            let nextSessiondetail
            let responseStatus = false
            if (result.status === "CONSUMED") {
                nextSessiondetail = await this.getNextSessionDetail(userContext, payload)
                responseStatus = true
            }

            const contentConsumedResult: ContentConsumedResult = {
                consumptionStatus: responseStatus,
                nextSessiondetail: nextSessiondetail
            }
            return contentConsumedResult
        }

        @httpPost("/content/consumed/v2")
        async contentConsumedV2(req: express.Request): Promise<ContentConsumedResult> {
            const session: Session = req.session
            const payload: ContentConsumedPayload = req.body
            const checksum: string = req.headers["checksum"] as string
            const userContext = req.userContext as UserContext
            const selectedCity = {
                city: userContext.userProfile.city.cityId,
                country: userContext.userProfile.city.countryId
            }
            const tz = userContext.userProfile.timezone
            const sessionData = userContext.sessionInfo.sessionData
            const detectedCity = sessionData.detectedCity
            const source = AppUtil.callSourceFromContext(userContext)

            if (!userContext.userProfile.promiseMapCache) {
                userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
            }

            try {
                if (AppUtil.isInternationalApp(userContext) && selectedCity.country == "WORLDWIDE" && detectedCity.country == "US") {
                    this.logger.info("selectedCountry: " + selectedCity.country + " detectedCountry: " + detectedCity.country + "ip: " + req.ip)
                }
            } catch (e) {
                this.logger.error("Error in Ip Logging")
            }
            if (!payload.activityType || !payload.activityId) {
                this.logger.error("Invalid data. Missing activity Id or type.")
                return { consumptionStatus: false, consumptionId: undefined, nextSessiondetail: undefined }
            }

            // verify checksum of payload
            /*
            @nitesh.vijay: Temp disabling checksum check, this is breaking on web for some flows.
            if (_.isNil(checksum)) {
                this.logger.error("Invalid data")
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid content consumption data").build()
            }
            */
            if (payload.activityType === "poplive") {
                const attributes = new Map()
                attributes.set("userId", userContext.userProfile.userId)
                attributes.set("type", "POP_LIVE")
                this.queueService.sendMessageAsync(Constants.getSQSQueue("ACTIVITYSTORE"), payload, attributes)
                this.customEventEmitter.emitEvent(eventName.VIDEO_COMPLETION, userContext.userProfile.userId, sessionData.attributionSource)
                return { consumptionStatus: true, consumptionId: undefined, nextSessiondetail: undefined }
            }
            if (payload.activityType === "DIY_RECIPE" || payload.activityType === "ON_DEMAND_VIDEO") {
                try {
                    if (!AppUtil.isWeb(userContext) && userContext.sessionInfo.appVersion < DIY_REPORT_SUPPORTED && !_.isNil(payload.duration) && payload.duration < 1000) {
                        const oldDuration = payload.duration
                        payload.duration = oldDuration * 1000
                    }
                    const endDateTime = payload.endTime ? new Date(payload.endTime) : undefined
                    const callSource = AppUtil.callSourceFromContext(userContext)
                    const response = await this.DIYFulfilmentService.consumeDIYContentForUserV2(payload.activityType, payload.activityId, userContext.userProfile.userId, new Date(payload.startTime), endDateTime, payload.duration, payload.uid, callSource, selectedCity, detectedCity, tz, payload.subType)
                    response.status === "CONSUMED" && this.customEventEmitter.emitEvent(eventName.VIDEO_COMPLETION, userContext.userProfile.userId, sessionData.attributionSource)
                    return { consumptionStatus: response.status === "CONSUMED", consumptionId: undefined, nextSessiondetail: undefined }
                } catch (e) {
                    this.serviceInterfaces.rollbarService.sendError(e, {extra: { userId: userContext.userProfile.userId, activityType: payload.activityType }})
                    this.serviceInterfaces.logger.error(`Error pushing recipe activity to activity log, payload: ${payload}`)
                }
            }
            /*
            @nitesh.vijay: Temp disabling checksum check, this is breaking on web for some flows.
            const expectedChecksum: string = new CryptoUtil().generateChecksumWithSalt(payload, session.sessionData.tlaSemusnoc)
            if (checksum !== expectedChecksum) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid content consumption data").build()
            }
            */

            const endDateTime = payload.endTime ? new Date(payload.endTime) : undefined
            const result: DIYContentConsumeResponse = <DIYContentConsumeResponse>await this.DIYFulfilmentService.consumeDIYContentForUser(payload.activityId, session.userId,
                new Date(payload.startTime), endDateTime, payload.duration, payload.uid, tz, selectedCity, detectedCity, source)

            let nextSessiondetail
            let responseStatus = false
            let sessionNo
            let isSubscribed
            let interventionAction: Action
            if (result.status === "CONSUMED") {
                this.customEventEmitter.emitEvent(eventName.VIDEO_COMPLETION, userContext.userProfile.userId, sessionData.attributionSource)
                responseStatus = true
                if (payload.activityType === "DIY_RECIPE") {
                    return {
                        consumptionStatus: responseStatus,
                        nextSessiondetail: undefined,
                        action: undefined
                    }
                }
                if ((payload.activityType === "DIY_FITNESS" || payload.activityType === "DIY_MEDITATION") && AppUtil.isDiyReportSupported(userContext) && result.DIYProductFulfilment && !payload.skipReport) {
                    const diyProductFitnessReport = await this.DIYFulfilmentService.getDIYProductFitnessReport(result.DIYProductFulfilment.fulfilmentId)
                    // Check if user has satisfied the criteria for a class being marked as complete by checking if a report is generated
                    if (!_.isEmpty(diyProductFitnessReport) && diyProductFitnessReport.productId) {
                        const action: Action = {
                            actionType: "NAVIGATION",
                            url: `curefit://diyreportspage?url=/digital/diySessionSummary?fulfilmentId=${result.DIYProductFulfilment.fulfilmentId}`
                        }
                        return {
                            consumptionStatus: responseStatus,
                            nextSessiondetail: undefined,
                            action
                        }
                    }
                }
                nextSessiondetail = await this.getNextSessionDetail(userContext, payload)

                let packDetail
                if (payload.activityType === "DIY_FITNESS") {
                    packDetail = await this.diyPackService.getFitnessDIYPackV2(payload.packId, session.userId)
                } else {
                    packDetail = await this.diyPackService.getMindDIYPackV2(payload.packId, session.userId)
                }
                const pack = packDetail.pack
                sessionNo = _.findIndex(pack.sessionIds, function (sessionId) { return sessionId === payload.activityId })
                isSubscribed = (packDetail.fulfilment !== undefined && packDetail.fulfilment.status === "SUBSCRIBED")
            } else if (result.status === "THRESHOLD_NOT_MET" && payload.activityType === "DIY_FITNESS" && AppUtil.isPartialVideoWatchedInterventionSupported(userContext) && payload?.duration > 15000) {
                interventionAction = await this.eventInterventionMappingBusiness.buildInterventionAction(INTERVENTION_EVENTS.partialDiyWatchedEvent, this.pageService, this.segmentService, userContext)
            }

            const action: Action = {
                actionType: "SHOW_SCHEDULE_DIY_INFO_MODAL",
                meta: {
                    action: {
                        url: EtherActionUtil.diySchedule(payload.packId, payload.activityType),
                        actionType: "NAVIGATION"
                    }
                }
            }
            const contentConsumedResult: ContentConsumedResult = {
                consumptionStatus: responseStatus,
                nextSessiondetail: nextSessiondetail,
                action: (sessionNo === 0 && !isSubscribed) ? action : undefined,
                interventionAction
            }
            return contentConsumedResult
        }

        private async getNextSessionDetail(userContext: UserContext, payload: ContentConsumedPayload): Promise<DiyContentDetail> {
            const userId = userContext.userProfile.userId
            const pack: DIYPack = payload.activityType === "DIY_FITNESS" ? await this.catalougeService.getDIYFitnessPack(payload.packId) : await this.catalougeService.getDIYMeditationPack(payload.packId)
            const sessionNo = _.findIndex(pack.sessionIds, function (sessionId) { return sessionId === payload.activityId })
            const tenant = AppUtil.getTenantFromUserContext(userContext)
            if (sessionNo + 1 < pack.sessionIds.length) {
                const sessionId = pack.sessionIds[sessionNo + 1]
                const diyProducts: DIYProduct[] = payload.activityType === "DIY_FITNESS" ? await this.DIYFulfilmentService.getDIYFitnessProductsByProductIds(userId, [sessionId], tenant) : await this.DIYFulfilmentService.getDIYMeditationProductsByProductIds(userId, [sessionId], tenant)
                const diyProduct = diyProducts[0]
                const nextSessiondetail = AtlasUtil.getContentDetailV2(diyProduct)
                return nextSessiondetail
            }
        }

        @httpPost("/content/consumed/batch")
        contentConsumedBatch(req: express.Request): Promise<{ success: boolean }> {
            const session: Session = req.session
            const payload: ContentConsumedPayload[] = req.body
            const promises: Promise<any>[] = []

            const userContext = req.userContext as UserContext
            const tz = userContext.userProfile.timezone
            const selectedCity = {
                city: userContext.userProfile.city.cityId,
                country: userContext.userProfile.city.countryId
            }
            const detectedCity = userContext.sessionInfo.sessionData.detectedCity
            const source = AppUtil.callSourceFromContext(userContext)
            payload.forEach(contentConsumedPayload => {
                const endDateTime = contentConsumedPayload.endTime ? new Date(contentConsumedPayload.endTime) : undefined
                promises.push(this.DIYFulfilmentService.consumeDIYContentForUser(contentConsumedPayload.activityId, session.userId,
                    new Date(contentConsumedPayload.startTime), endDateTime, contentConsumedPayload.duration, contentConsumedPayload.uid, tz,
                    selectedCity, detectedCity, source))
            })


            return Promise.all(promises).then(() => {
                return { success: true }
            })
        }

        @httpPost("/content/consumed/batch/v2")
        contentConsumedBatchV2(req: express.Request): Promise<{ success: boolean }> {
            const session: Session = req.session
            const payload: ContentConsumedPayload[] = req.body
            const appVersion: string = req.headers["appversion"] as string
            const promises: Promise<any>[] = []
            const checksum: string = req.headers["checksum"] as string

            const userContext = req.userContext as UserContext
            const tz = userContext.userProfile.timezone
            const selectedCity = {
                city: userContext.userProfile.city.cityId,
                country: userContext.userProfile.city.countryId
            }
            const detectedCity = userContext.sessionInfo.sessionData.detectedCity
            const source = AppUtil.callSourceFromContext(userContext)
            // verify checksum of payload
            if (_.isNil(checksum)) {
                this.logger.error("Invalid data. Missing checksum")
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid content consumption data").build()
            }
            if (AppUtil.isSugarFitApp(userContext)) {
                try {
                    const expectedChecksum: string = new CryptoUtil().generateChecksumWithSalt(payload, session.sessionData.tlaSemusnoc)
                    if (checksum !== expectedChecksum) {
                        throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid content consumption data").build()
                    }
                } catch (e) {
                    // Ignore
                }
            } else {
                const expectedChecksum: string = new CryptoUtil().generateChecksumWithSalt(payload, session.sessionData.tlaSemusnoc)
                if (checksum !== expectedChecksum) {
                    throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid content consumption data").build()
                }
            }

            payload.forEach(contentConsumedPayload => {
                const endDateTime = contentConsumedPayload.endTime ? new Date(contentConsumedPayload.endTime) : undefined
                promises.push(this.DIYFulfilmentService.consumeDIYContentForUser(contentConsumedPayload.activityId, session.userId,
                    new Date(contentConsumedPayload.startTime), endDateTime, contentConsumedPayload.duration, contentConsumedPayload.uid, tz,
                    selectedCity, detectedCity, source))

            })

            return Promise.all(promises).then(() => {
                return { success: true }
            })
        }


        @httpGet("/timelineV3")
        getTimelineV3(req: express.Request): Promise<TimelineViewV1> {
            const session: Session = req.session
            const userAgent: UserAgent = req.session.userAgent
            const startDate: string = req.query.startDate
            const endDate: string = req.query.endDate
            const score: boolean = req.query.score && req.query.score === "true"
            const metrics: boolean = req.query.metrics && req.query.metrics === "true"
            const showaddoption: boolean = req.query.showaddoption && req.query.showaddoption === "true"
            const canAskHealthKit: boolean = req.query.canAskHealthKit && req.query.canAskHealthKit === "true"
            const canAskFitBit: boolean = req.query.canAskFitBit && req.query.canAskFitBit === "true"
            const appVersion: string = req.headers["appversion"] as string
            const osName: string = req.headers["osname"] as string
            const codePushVersion = req.headers["codepushversion"] as string
            const isTodayStepFromServer = TimelineUtil.isTodayStepsFromServerSupported(userAgent, Number(appVersion), Number(codePushVersion), osName)
            const timelineRequestParams: TimelineRequestParams = {
                userContext: req.userContext,
                canAskFitBit: canAskFitBit,
                canAskHealthKit: canAskHealthKit,
                userId: session.userId,
                deviceId: session.deviceId,
                cityId: session.sessionData.cityId,
                userAgent: userAgent,
                mindCenterId: session.sessionData.mindCenterId,
                cultCenterId: session.sessionData.cultCenterId,
                startDate: startDate,
                endDate: endDate,
                score: score,
                metrics: metrics,
                isTodayStepFromServer: isTodayStepFromServer,
                showaddoption: showaddoption,
            }
            return this.timelineBusinessV1.getTimeLine(timelineRequestParams, req.userContext as UserContext)
        }

        @httpGet("/timelineV4")
        getTimelineV4(req: express.Request): Promise<TimelineViewV1> {
            const session: Session = req.session
            const userAgent: UserAgent = req.session.userAgent
            const startDate: string = req.query.startDate
            const endDate: string = req.query.endDate
            const score: boolean = req.query.score && req.query.score === "true"
            const metrics: boolean = req.query.metrics && req.query.metrics === "true"
            const showaddoption: boolean = req.query.showaddoption && req.query.showaddoption === "true"
            const canAskHealthKit: boolean = req.query.canAskHealthKit && req.query.canAskHealthKit === "true"
            const canAskFitBit: boolean = req.query.canAskFitBit && req.query.canAskFitBit === "true"
            const appVersion: string = req.headers["appversion"] as string
            const osName: string = req.headers["osname"] as string
            const codePushVersion = req.headers["codepushversion"] as string
            const isTodayStepFromServer = TimelineUtil.isTodayStepsFromServerSupported(userAgent, Number(appVersion), Number(codePushVersion), osName)

            if (_.isNil(endDate) || _.isNil(startDate)) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("missing data params").build()
            }
            const timelineRequestParams: TimelineRequestParams = {
                userContext: req.userContext,
                canAskFitBit: canAskFitBit,
                canAskHealthKit: canAskHealthKit,
                userId: session.userId,
                deviceId: session.deviceId,
                cityId: session.sessionData.cityId,
                userAgent: userAgent,
                mindCenterId: session.sessionData.mindCenterId,
                cultCenterId: session.sessionData.cultCenterId,
                startDate: startDate,
                endDate: endDate,
                score: score,
                metrics: metrics,
                isTodayStepFromServer: isTodayStepFromServer,
                showaddoption: showaddoption
            }
            return this.timelineBusinessV4.getTimeLine(timelineRequestParams, req.userContext as UserContext)
        }

        @httpGet("/addActivities")
        async getActivities(req: express.Request): Promise<{ activities: AddActivity[] }> {
            const session: Session = req.session
            const appVersion: number = Number(req.headers["appversion"])
            const codePushVersion = req.headers["codepushversion"] as string
            const osName: string = req.headers["osname"] as string
            const cityId = session.sessionData.cityId
            const activities: AddActivity[] = []
            const cultCLPAction = EtherActionUtil.cultCLPVMPage("CultAtHome")
            const mindCLPAction = EtherActionUtil.mindCLPVMPage("MindAtHome")

            activities.push({
                title: "Book a cult class",
                image: "/image/addActivity/cult3.png",
                action: {
                    actionType: "NAVIGATION",
                    url: EtherActionUtil.bookCultClass("addActivity", session.sessionData.cultCenterId)
                }
            })
            if (CultUtil.isMindFitAvailable(cityId)) {
                activities.push({
                    title: "Book a mind class",
                    image: "/image/addActivity/mind3.png",
                    action: {
                        actionType: "NAVIGATION",
                        url: EtherActionUtil.bookMindClass("addActivity", session.sessionData.mindCenterId)
                    }
                })
            }

            if (EatUtil.isEatFitAvailable(cityId)) {
                activities.push({
                    title: "Book a meal",
                    image: "/image/addActivity/meal3.png",
                    action: {
                        actionType: "NAVIGATION",
                        url: EtherActionUtil.eatFitClp(),
                    }
                })
            }

            activities.push({
                title: "Add a home workout",
                image: "/image/addActivity/culthome3.png",
                action: {
                    actionType: "NAVIGATION",
                    url: cultCLPAction,
                }
            })
            activities.push({
                title: "Add meditation",
                image: "/image/addActivity/mindhome3.png",
                action: {
                    actionType: "NAVIGATION",
                    url: mindCLPAction,
                }
            })
            if (CareUtil.isCareSupported(osName, appVersion, cityId)) {
                activities.push({
                    title: "Book a consultation",
                    image: "/image/addActivity/care1.png",
                    action: {
                        actionType: "NAVIGATION",
                        url: EtherActionUtil.careFitClp(),
                    }
                })
            }
            return { activities: activities }
        }

        @httpGet("/addActivities/v2")
        async getActivitiesV2(req: express.Request): Promise<{ bookActivities: AddActivity[], logActivities?: AddActivity[] }> {
            const session: Session = req.session
            const appVersion: number = Number(req.headers["appversion"])
            const osName: string = req.headers["osname"] as string
            const userId: string = session.userId

            const user: User = await this.userCache.getUser(userId)
            const cityId = session.sessionData.cityId
            const bookActivities: AddActivity[] = []
            const logActivities: AddActivity[] = []

            const result: {
                bookActivities: AddActivity[],
                logActivities?: AddActivity[]
            } = {
                bookActivities: []
            }
            // const userPlanExistsResult = await eternalPromise(this.gmfClient.checkExistsUserPlan(user.id))
            const userPlanExists: boolean = false // _.get(userPlanExistsResult.obj, "exists", false)
            const isNewClassBookingSuppoted = AppUtil.isNewClassBookingSuppoted(req.userContext as UserContext, user.isInternalUser)
            bookActivities.push({
                title: "cult.fit\nclass",
                action: {
                    actionType: "NAVIGATION",
                    url: EtherActionUtil.getBookCultClassUrl("FITNESS", isNewClassBookingSuppoted, "addActivity", undefined)
                },
                gradientColors: ["#FCAC8C", "#EF6D65"]
            })
            bookActivities.push({
                title: "mind.fit\nclass",
                action: {
                    actionType: "NAVIGATION",
                    url: EtherActionUtil.getBookCultClassUrl("MIND", isNewClassBookingSuppoted, "addActivity", undefined)
                },
                gradientColors: ["#DEA9FC", "#F36EA2"]
            })
            bookActivities.push({
                title: "eat.fit\nmeal",
                action: {
                    actionType: "NAVIGATION",
                    url: EtherActionUtil.eatFitClp(),
                },
                gradientColors: ["#84ECFE", "#6BB0FF"]
            })
            bookActivities.push({
                title: "care.fit\nconsultation",
                action: {
                    actionType: "NAVIGATION",
                    url: EtherActionUtil.careFitClp(),
                },
                gradientColors: ["#82E4B6", "#6FC5C9"]
            })
            // TODO: Delete code as gmf client always returns false for userPlanExists
            if (userPlanExists) {
                logActivities.push(
                    {
                        title: "Diet",
                        image: "/image/icons/addActivity/diet2.png",
                        action: {
                            actionType: "NAVIGATION",
                            url: ActionUtil.addLoggableActivityUrl("DIET")
                        }
                    },
                    {
                        title: "Fitness",
                        image: "/image/icons/addActivity/workout2.png",
                        action: {
                            actionType: "NAVIGATION",
                            url: ActionUtil.addLoggableActivityUrl("FITNESS", {
                                slotId: "ALLDAY"
                            })
                        }
                    },
                    {
                        title: "Mindfulness",
                        image: "/image/icons/addActivity/meditation2.png",
                        action: {
                            actionType: "NAVIGATION",
                            url: ActionUtil.addLoggableActivityUrl("MEDITATE", {
                                slotId: "ALLDAY"
                            })
                        }
                    },
                    {
                        title: "Metrics",
                        image: "/image/icons/addActivity/metrics3.png",
                        action: {
                            actionType: "NAVIGATION",
                            url: ActionUtil.addLoggableActivityUrl("METRICS", {
                                slotId: "ALLDAY"
                            })
                        }
                    }
                    // {
                    //     title: "Symptoms",
                    //     image: "/image/icons/addActivity/symptoms2.svg",
                    //     action: {
                    //         actionType: "NAVIGATION",
                    //         url: ActionUtil.addLoggableActivityUrl("SYMPTOMS", {
                    //             slotId: "ALLDAY"
                    //         })
                    //     }
                    // }
                )
                result.logActivities = logActivities
            }
            result.bookActivities = bookActivities
            return result
        }

        @httpGet("/cities")
        async getCities(req: express.Request): Promise<{ cities: CityView[] }> {
            const session: Session = req.session
            const tenant: Tenant = AppUtil.getTenantFromReq(req)
            const citiesPromise = this.cityService.listCities(tenant, req.query.countryId || "IN")
            const latitude: number = req.headers["lat"] ? Number(req.headers["lat"]) : undefined
            const longitude: number = req.headers["lon"] ? Number(req.headers["lon"]) : undefined
            let detectedCityAndCountryPromise
            const activeMembershipPromise = this.cultFitService.activeMembership(session.userId)
            const membershipData = await activeMembershipPromise
            if (latitude && longitude) {
                detectedCityAndCountryPromise = (latitude && longitude) ? this.cityService.getCityAndCountry(tenant, latitude, longitude) : undefined
            }
            const detectedCityAndCountry = await detectedCityAndCountryPromise
            const cities = await citiesPromise
            LocationUtil.sortCityByPriority(cities)
            let selectedCityId = session.sessionData.cityId
            if (detectedCityAndCountry && detectedCityAndCountry.city && selectedCityId !== detectedCityAndCountry.city.cityId)
                selectedCityId = detectedCityAndCountry.city.cityId

            const cityViews = cities.map(city => {
                const cityView: CityView = {
                    isSelected: city.cityId === selectedCityId ? true : false,
                    cityId: city.cityId,
                    timezone: city.timezone,
                    name: city.name,
                    image: city.image,
                    countryId: city.countryId,
                    lat: city.representativeLatLong.lat,
                    lon: city.representativeLatLong.long
                }

                const activeMembership = CultUtil.getCurrentMembership(membershipData.memberships)
                if (activeMembership && activeMembership.cityID !== city.cultCityId && !this.cityService.checkIfCityIsOtherCity(city.cityId)) {
                    cityView.section = {
                        title: `Welcome to ${city.name}`,
                        subTitle: "Find custom packs for every location",
                        items: [{
                            icon: "/image/icons/howItWorks/bookClass_1.png",
                            message: "Your Cult and Mind membership includes 5 class/month outside your home city. Stay fit even when you travel!"
                        }]
                    }
                }
                return cityView
            })
            return { cities: cityViews }
        }


        @httpGet("/cities/v2")
        async getCitiesV2(req: express.Request, res: express.Response): Promise<GetCitiesResult> {
            const session: Session = req.session
            const latitude: number = req.headers["lat"] ? Number(req.headers["lat"]) : undefined
            const longitude: number = req.headers["lon"] ? Number(req.headers["lon"]) : undefined
            const ip: string = res.locals.ip
            const userContext: UserContext = req.userContext as UserContext
            let tenant: Tenant = AppUtil.getTenantFromReq(req)
            // Putting this hack temporarily as city service doesn't return anything for CultSport app currently
            if (tenant === Tenant.CULTSPORT_APP) {
                tenant = Tenant.CUREFIT_APP
            }
            const currentCultMembershipPromise = this.getUserCurrentCultMembership(session.userId)
            const detectedCityAndCountryPromise = this.detectCityAndCountryAndIp(tenant, ip, latitude, longitude)

            let cities = this.cityService.listCities(tenant), selectedCity: CityView
            LocationUtil.sortCityByPriority(cities)
            if (!AppUtil.isDubaiEnabled(userContext)) {
                cities = _.filter(cities, city => city.countryId === "IN")
            }
            const countryMap = this.countryService.getCountryMap(tenant)
            const currentCultMembership = await currentCultMembershipPromise
            const cultCityId = currentCultMembership && currentCultMembership.cityID
            const detectedCityAndCountry = await detectedCityAndCountryPromise
            const detectedCountryId = detectedCityAndCountry.country ? detectedCityAndCountry.country.countryId : undefined

            const selectedCityId = session.sessionData ? session.sessionData.cityId : undefined
            const cityMap: Map<string, City> = new Map<string, City>()
            const areaMap: Map<string, Area[]> = new Map<string, Area[]>()

            cities.forEach(city => {
                if (city.parentCityId) {
                    if (city.cityId === city.parentCityId) {
                        areaMap.set(city.parentCityId, [ {
                            cityId: city.parentCityId,
                            areaId: city.cityId,
                            name: city.name,
                            cultAreaId: city.cultCityId,
                            lat: city.representativeLatLong.lat,
                            lon: city.representativeLatLong.long,
                        } as Area, ...(areaMap.get(city.cityId) || [])])
                    } else {
                        areaMap.set(city.parentCityId, [...(areaMap.get(city.cityId) || []), ({
                            cityId: city.parentCityId,
                            areaId: city.cityId,
                            name: city.name,
                            cultAreaId: city.cultCityId,
                            lat: city.representativeLatLong.lat,
                            lon: city.representativeLatLong.long,
                        } as Area)])
                    }

                    if (!cityMap.get(city.parentCityId) && city.cityId === city.parentCityId) {
                        cityMap.set(city.parentCityId, {...city, name: city.parentCityName})
                    }
                } else {
                    cityMap.set(city.cityId, city)
                }
            })
            const cityViews: CityView[] = []
            const isCitySplitFeatureSupported = (await AppUtil.isCitySplitFeatureSupported(userContext))
            cityMap.forEach((city, cityId) => {
                const cityView: CityView = {
                    isSelected: city.cityId === selectedCityId ? true : false,
                    cityId: city.cityId,
                    countryId: city.countryId,
                    name: city.name,
                    image: city.image,
                    timezone: city.timezone,
                    lat: city.representativeLatLong.lat,
                    lon: city.representativeLatLong.long,
                    isPopular: city.isPopular || false
                }

                if (cityView.isSelected) {
                    selectedCity = cityView
                }

                if (cultCityId && cultCityId !== city.cultCityId && !this.cityService.checkIfCityIsOtherCity(city.cityId)) {
                    cityView.section = {
                        title: `Welcome to ${city.name}`,
                        subTitle: "Find custom packs for every location",
                        items: [{
                            icon: "/image/icons/howItWorks/bookClass_1.png",
                            message: "Your Cult and Mind membership includes 5 class/month outside your home city. Stay fit even when you travel!"
                        }]
                    }
                }
                const areaData = {...cityView}

                if (areaMap.get(cityId) && areaMap.get(cityId).length > 1 && isCitySplitFeatureSupported) {
                    const cultAreaIds = areaMap.get(cityId).map(area => area.cultAreaId)
                    cityView.action = {
                        actionType: "SHOW_AREA_SELECTOR_MODAL",
                        meta: {
                            title: `Select location preference`,
                            subtitle: currentCultMembership && cultCityId && cultAreaIds.includes(cultCityId) && (new Date(currentCultMembership.createdAt) <= CITY_SPLIT_LAUNCH_DATE)
                                ? "Access both areas with existing membership"
                                : "Membership prices vary across these areas",
                            cityName: city.name,
                            backgroundImage: "/image/icons/cult/area_selector_header.png",
                            whiteBackgroundImage: "/image/icons/cult/area_selector_header_white_v2.png",
                            areaList: areaMap.get(cityId).map(area => {
                                if (area.areaId === selectedCityId) {
                                    cityView.isSelected = true
                                    if (area.areaId !== REST_OF_MUMBAI_CITY_ID) {
                                        cityView.areaName = area.name
                                    }
                                    selectedCity = cityView
                                }
                                return {
                                    title: area.name,
                                    areaId: area.areaId,
                                    cityId: area.cityId,
                                    areaData: {
                                        ...areaData,
                                        cityId: area.areaId,
                                        name: area.name
                                    },
                                }
                            })
                        },
                    }
                }

                cityViews.push(cityView)
            })

            // group by country
            const countryToCitiesMap: { [countryId: string]: CityView[] } = {}
            cityViews.forEach(cityView => {
                countryToCitiesMap[cityView.countryId] = countryToCitiesMap[cityView.countryId] || []
                countryToCitiesMap[cityView.countryId].push(cityView)
            })

            const result: GetCitiesResult = {
                countries: [],
                selectedCity: !_.isEmpty(selectedCity) ? selectedCity : undefined,
                cityTextInfo: {
                    popularCityText: "POPULAR CITY",
                    otherCityText: "OTHERS"
                }
            }
            Object.keys(countryToCitiesMap).forEach(countryId => {
                const countryView: CountryView = {
                    title: countryMap.get(countryId).name,
                    countryId: countryId,
                    cities: countryToCitiesMap[countryId]
                }
                result.countries.push(countryView)
            })
            result.countries.sort((a, b) => {
                // Moving up the detected country to top
                if (detectedCountryId) {
                    if (a.countryId === detectedCountryId) {
                        return -1
                    } else if (b.countryId === detectedCountryId) {
                        return 1
                    }
                }
                // Moving down the OTHER country to end
                if (a.countryId === "OTHER") {
                    return 1
                } else if (b.countryId === "OTHER") {
                    -1
                }
                // remaining sorted alphabetically among remaining countries
                return a.title < b.title ? -1 : 1
            })

            // Adding Detected Info, when city and country of user is detected
            if (detectedCityAndCountry && detectedCountryId && detectedCityAndCountry.city) {
                result.detectedInfo = {
                    cityId: detectedCityAndCountry.city.cityId || undefined,
                    countryId: detectedCountryId
                }
            }

            return result
        }

        @httpGet("/areas")
        async getAreas(req: express.Request, res: express.Response): Promise<AreasResult> {
            const session: Session = req.session
            const cityId: string = req.query.cityId
            const tenant: Tenant = AppUtil.getTenantFromReq(req)
            const currentCultMembershipPromise = this.getUserCurrentCultMembership(session.userId)
            const currentCultMembership = await currentCultMembershipPromise
            const cultCityId = currentCultMembership && currentCultMembership.cityID

            const cities = this.cityService.listCities(tenant)

            const areas: Area[] = []
            let parentCityName = ""
            let cityData: City = undefined

            cities.forEach(city => {
                if (city.parentCityId && city.parentCityId === cityId) {
                    cityData = city
                    parentCityName = city.parentCityName
                    if (city.cityId === city.parentCityId) {
                        areas.unshift({
                            cityId: city.parentCityId,
                            areaId: city.cityId,
                            name: city.name,
                            cultAreaId: city.cultCityId,
                            lat: city.representativeLatLong.lat,
                            lon: city.representativeLatLong.long,
                        } as Area)
                    } else {
                        areas.push({
                            cityId: city.parentCityId,
                            areaId: city.cityId,
                            cultAreaId: city.cultCityId,
                            name: city.name,
                            lat: city.representativeLatLong.lat,
                            lon: city.representativeLatLong.long,
                        } as Area)
                    }
                }
            })

            const areaData: CityView = cityData ? {
                isSelected: true,
                cityId: cityData.cityId,
                countryId: cityData.countryId,
                name: cityData.name,
                image: cityData.image,
                timezone: cityData.timezone,
                lat: cityData.representativeLatLong.lat,
                lon: cityData.representativeLatLong.long,
                isPopular: cityData.isPopular || false
            } : undefined

            const cultAreaIds = areas.map(area => area.cultAreaId)

            return areas && areas.length > 1 && areaData ? {
                title: `Select location preference`,
                subtitle: currentCultMembership && cultCityId && cultAreaIds.includes(cityData.cultCityId) && (new Date(currentCultMembership.createdAt) <= CITY_SPLIT_LAUNCH_DATE)
                    ? "Access both areas with existing membership"
                    : "Membership prices vary across these areas",
                cityName: parentCityName,
                backgroundImage: "/image/icons/cult/area_selector_header.png",
                whiteBackgroundImage: "/image/icons/cult/area_selector_header_white_v2.png",
                areaList: areas.map(area => {
                    return {
                        title: area.name,
                        areaId: area.areaId,
                        cityId: area.cityId,
                        areaData: {
                            ...areaData,
                            cityId: area.areaId,
                            name: area.name
                        },
                    }
                })
            } : null
        }

        async detectCityAndCountryAndIp(tenant: Tenant, ip: string, latitude: number, longitude: number): Promise<{ city?: City, country?: Country }> {
            const cityAndCountryByIPPromise = this.CFAPICityService.getCityAndCountryByIp(tenant, ip)
            const cityAndCountryByLatLongPromise = (longitude && latitude) ? this.cityService.getCityAndCountry(tenant, latitude, longitude) : undefined
            const cityAndCountryByIP = await cityAndCountryByIPPromise
            const cityAndCountryByLatLong = await cityAndCountryByLatLongPromise
            if (cityAndCountryByLatLong && cityAndCountryByLatLong.city) {
                return cityAndCountryByLatLong
            }
            return cityAndCountryByIP
        }

        async getUserCurrentCultMembership(userId: string): Promise<CultMembership> {
            if (userId) {
                const activeMembershipPromise = this.cultFitService.activeMembership(userId)
                const membershipData = await activeMembershipPromise
                return CultUtil.getCurrentMembership(membershipData.memberships)
            }
            return undefined
        }

        @httpPost("/updateCityPreference")
        async updateCityPreference(req: express.Request): Promise<any> {
            const session: Session = req.session
            const userContext = req.userContext as UserContext
            const selectedCityId = req.body.cityId
            const cityResponse: CityResponseV2 = await this.userBusiness.updateCity(userContext, session, selectedCityId)
            const updatedUserContext = await this.authMiddleWare.getUserContextFromReq(req)
            let appLayout: AppLayout

            try {
                appLayout = await this.appLayoutBuilder.fetchPageLayout(updatedUserContext, cityResponse.supportedVerticals)
            } catch (err) {
                this.logger.error("Failed to fetch app layout " + err)
            }
            return {
                ...cityResponse,
                appLayout
            }
        }

        @httpGet("/gates")
        getDeliveryGates(req: express.Request): Promise<DeliveryGateView[]> {
            const lat: number = req.query.lat
            const lon: number = req.query.lon
            let gatePromise: Promise<DeliveryGate[]>

            if (lat && lon) {
                gatePromise = this.gateService.getNearbyGates(lat, lon)
            } else {
                gatePromise = this.gateService.getAllGates()
            }

            return gatePromise.then(gates => {

                const activeGates = _.filter(gates, gate => {
                    return gate.status === "LIVE"
                })

                const inactiveGates = _.filter(gates, gate => {
                    return gate.status === "DRAFT"
                })

                const sortedGates = [...activeGates, ...inactiveGates]

                return sortedGates.map(gate => { return new DeliveryGateView(gate) })
            })
        }

        @httpGet("/fetchOnDemandAnnouncement")
        async getCustomAnnouncement(req: express.Request, res: express.Response): Promise<{}> {
            const userContext: UserContext = req.userContext as UserContext
            const announcementId: string = req.query.announcementId
            const announcementActionType: string = "SHOW_ANNOUNCEMENT"
            if (!userContext.userProfile.promiseMapCache) {
                userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
            }
            if (!announcementId || _.isEmpty(announcementId)) return

            let announcementPromise: Promise<Action> = null

            if (announcementId === "referral_invite_announcement") {
                announcementPromise = this.announcementBusiness.getReferralAnnouncement(userContext, announcementId, announcementActionType as ActionType, req.query)
            } else {
                announcementPromise = this.announcementBusiness.getAnnouncement(userContext, announcementId, announcementActionType as ActionType, this.pageService, this.segmentService)
            }

            return {
                action: await announcementPromise
            }
        }

        @httpGet("/experiments")
        async getExperiments(req: express.Request) {
            const userContext: UserContext = req.userContext as UserContext
            return this.hamletBusiness.getUserAllocations(AppUtil.getHamletContext(userContext, []))
        }

        @httpPost("/status")
        async getStatus(req: express.Request, res: express.Response): Promise<UserStatusResponse> {
            if (req.session?.userId && await AppUtil.isJavaServiceEnabled(req.userContext, this.hamletBusiness)) {
                return await this.cFAPIJavaService.statusPage(req)
            }
            const session: Session = await this.createGuestUserSession(req, res)
            const userId: string = session.userId
            const userAgent: UserAgent = session.userAgent
            const latitude: number = req.headers["lat"] ? Number(req.headers["lat"]) : undefined
            const longitude: number = req.headers["lon"] ? Number(req.headers["lon"]) : undefined
            const ip: string = res.locals.ip
            const appVersion: string = req.headers["appversion"] as string
            const osName: string = req.headers["osname"] as string
            const codePushVersion = req.headers["codepushversion"] as string
            const apiKey: string = req.headers["api-key"] as string || req.headers["apikey"] as string
            const istest: boolean = (req.headers["test"] as string === "true") ? true : false
            const userPromise = this.userCache.getUser(userId)
            const userContext = req.userContext as UserContext
            const passedCityId = req.body.cityId
            const advertiserId = req.body.advertiserId
            const tenant: Tenant = AppUtil.getTenantFromReq(req)
            const isInternationalApp = AppUtil.isInternationalAppFromReq(req)

            // update consumeSalt in session if not present
            if (_.isNil(session.sessionData.tlaSemusnoc)) {
                session.sessionData.tlaSemusnoc = crypto.randomUUID()
            }

            const countriesPromise = this.countryService.listCountries(tenant)
            const updateCityResponse = await this.userBusiness.findPreferredCity(tenant, latitude, longitude, ip, session, passedCityId)

            const diySubscriptionsPromise: Promise<ActivePackViewV1[]> = this.userBusiness.getDIYActivePacks(userContext)
            const hamletUserExperimentPromise: Promise<UserAllocation> = this.hamletBusiness.getUserAllocations(AppUtil.getHamletContext(userContext, []))

            const announcementPromise: Promise<IAnnouncement> = this.announcementBusiness.getSingleActiveAnnouncement(this.pageService, this.segmentService, userContext, true)
            const interventionPromise: Promise<Intervention> = this.interventionBusiness.getInterventionToShow(session, userContext, userId)

            const city = updateCityResponse.city

            const isCityChangeToBeNotified = updateCityResponse.isCityChangeToBeNotified
            // For not logged in user, to pass on the timezone and city information from detected city
            if (!userContext.userProfile) {
                userContext.userProfile = {
                    userId: session.userId,
                    timezone: city.timezone,
                    cityId: city.cityId,
                    areaId: undefined,
                    cultCenterId: undefined,
                    mindCenterId: undefined,
                    city: city
                }
            }

            const detectedCity = {
                city: updateCityResponse.detectedCityName,
                country: updateCityResponse.detectedCountryCode,
            }
            session.sessionData.cityId = city.cityId
            session.sessionData.detectedCity = detectedCity

            if (AppUtil.isSugarFitOrUltraFitApp(userContext) && detectedCity?.city) {
                try {
                    this.userBusiness.publishUserEventToRashi(userId, {
                        sf_ip_detected_city: detectedCity?.city,
                    }, AttributeAction.REPLACE, AppUtil.getAppTenantFromReq(req))
                } catch (error) {
                    this.logger.info(`Error is updating detected city to rashi for user:: ${userId}`)
                }
            }
            // Updates city & slat if needed and also used to update session update time to extend the session ttl
            const updateSessionPromise: Promise<Session> = istest ? null : this.sessionBusiness.updateSessionData(session.at, session.sessionData, session.isNotLoggedIn, isCityChangeToBeNotified)
            const verticalInfoPromise: Promise<VerticalsWithHighlightInformation> = this.navBarBusiness.getUserAndSupportedVerticalsInfo(userContext, city, session.userId)

            const cfUserProfile = <CFUserProfile>userContext.userProfile
            cfUserProfile.hamletUserExperimentPromise = hamletUserExperimentPromise

            const user = await userPromise
            if (!user.id) {
                this.logger.error("UserIdNotReturned for req userid", userId || "falsy userid")
            }
            const subUserIds = user.subUserRelations && user.subUserRelations.map(subUserRelation => {
                return subUserRelation.subUserId
            }) || []
            const allUserIds = [user.id, ...subUserIds]

            const feedbackPromise: Promise<Feedback> = this.feedbackBusiness.getFeedbackToShow(allUserIds, req.userContext)


            const userPlanExists: boolean = false
            // update sidebar config
            const careMappedActions = CareUtil.addCareMappedActions(Number(appVersion), city.cityId, user.isInternalUser)
            const sideBar = this.mePageSideBarPageConfig.getMePageResponse(careMappedActions)

            // fill countries data
            const countries = await countriesPromise
            let countriesData: CountryData[] = _.map(countries, country => {
                return {
                    countryId: country.countryId,
                    name: country.name,
                    countryCallingCode: country.countryCallingCode,
                    phoneLoginSupported: country.phoneLoginSupported,
                    flagImage: country.flagImage,
                    phoneNumberMaxLength: country.phoneNumberMaxLength
                }
            })
            if (!AppUtil.isDubaiEnabled(userContext)) {
                countriesData = _.filter(countriesData, country => country.countryId === "IN")
            }
            const sleepConfig = AppUtil.getSleepConfig(appVersion, osName)
            const verticalInfo = await verticalInfoPromise
            const supportedVerticals: VerticalType[] = verticalInfo.verticals.map(vertical => vertical.verticalType)
            let appLayout: AppLayout
            try {
                appLayout = await this.appLayoutBuilder.fetchPageLayout(userContext, supportedVerticals)
            } catch (err) {
                this.logger.error("Failed to fetch app layout " + err)
            }

            // Get active CFS Form action for user if present
            const cfsFormAction: Action = await this.cfsFormCache.getActiveCfsFormActionForUser(userId)
            const showcfsform = !_.isEmpty(cfsFormAction)

            const response: UserStatusResponse = {
                "user": new UserStatusView(user, session.isNotLoggedIn),
                "session": new SessionView(session),
                planTabItem: AppUtil.getPlanTabs(osName, appVersion, userPlanExists, user.isInternalUser),
                goalPlanExists: userPlanExists,
                sideBar,
                verticals: verticalInfo.verticals,
                isMoreToBeHighlighted: verticalInfo.isMoreToBeHighlighted,
                appTabs: ["PLAN", ...supportedVerticals], // appTabs can only handle 5 tabs
                cityId: city.cityId,
                cityName: city.name,
                detectedCity: detectedCity,
                timezone: city.timezone,
                countryId: city.countryId,
                showCitySelection: updateCityResponse.showCitySelection,
                countriesData,
                sleepConfig,
                defaultPageId: city.defaultPageId,
                cityRepresentativeLat: city.representativeLatLong.lat,
                cityRepresentativeLon: city.representativeLatLong.long,
                appLayout,
                microapps: {
                    GEAR: { enabled: true } // Gear microapp enabled for all
                },
                showcfsform: showcfsform,
                formAction: cfsFormAction,
            }

            const announcement: IAnnouncement = await announcementPromise
            if (announcement) {
                response.announcementData = announcement.announcementData
            }

            const intervention = await interventionPromise
            if (intervention) {
                response.intervention = {
                    interventionId: intervention.interventionId,
                    interventionType: intervention.type
                }
            } else {
                const feedback = await feedbackPromise
                if (feedback) {
                    response.feedbackId = feedback.feedbackId
                }
            }

            const diySubscriptions = await diySubscriptionsPromise

            response.diySubscriptions = diySubscriptions
            response.activePackSubscribed = true

            await updateSessionPromise
            response.tlaSemusnoc = session.sessionData.tlaSemusnoc
            ActionUtil.getQuickActions(session, user, req.userContext as UserContext, city)

            // Awaiting for User Experiment (Hamlet)
            const userExperimentDataMapObject = !isInternationalApp && (await eternalPromise(hamletUserExperimentPromise, "User Experiment Mapping"))
            if (!_.isEmpty(userExperimentDataMapObject.obj?.assignmentsMap)) {
                const userExperimentList = _.map(_.keys(userExperimentDataMapObject.obj.assignmentsMap), experimentId => {
                    const userAssignment = userExperimentDataMapObject.obj.assignmentsMap[experimentId]
                    return `E:${userAssignment.experimentId}_${userAssignment.bucket && userAssignment.bucket.bucketId}_${userAssignment.versionIdentifier}`
                })
                response.analyticsData = { experiment: userExperimentList.toString() }
                response.defaultPageId = response.activePackSubscribed === true ? response.defaultPageId : "CULT"
            } else if (userExperimentDataMapObject.err) {
                this.logger.error("error at User Hamlet Experiment Mapping api : " + userExperimentDataMapObject.err)
            }

            // Fire and Forget to capture location in mongo
            this.deviceBusiness.updateSessionInformation(session.deviceId, session.sessionData, tenant, Number(appVersion), codePushVersion, latitude, longitude, advertiserId)

            this.logger.info(`user/status response ${util.inspect(response, { depth: 1 })}`)
            return response
        }

        @httpDelete("/status/cfsFormAction")
        async dismissCfsAutoPopup(req: express.Request, res: express.Response): Promise<any> {
            try {
                const session: Session = await this.createGuestUserSession(req, res)
                const userId: string = session.userId
                const formId: string = req.body.formId
                this.logger.info("cfs_autolaunch_testing: Fetched userId and formId")
                const segmentName = userFormSegmentNameMap[formId]
                if (_.isNil(segmentName)) {
                    return
                }
                this.logger.info("cfs_autolaunch_testing: Get the segment of the user")
                const lock = await this.lock.lockResource(`DELETE-${userId}-${segmentName}`, 30000)
                try {
                    await this.serviceInterfaces.segmentationClient.removeUsersFromSegment(segmentName, [userId])
                } catch (error) {
                    const genericErr: GenericError = new GenericError(({ message: error.message }))
                    genericErr.statusCode = 500
                    await this.rollbarService.handleError(genericErr)
                } finally {
                    await this.lock.unlockResource(lock)
                }
            } catch (error) {
                this.rollbarService.sendError(error)
                return res.sendStatus(200)
            }
        }

        @httpPost("/status/v2")
        async getStatusV2(req: express.Request, res: express.Response): Promise<UserStatusResponse> {
            return this.getStatus(req, res)
        }

        async createGuestUserSession(req: express.Request, res: express.Response): Promise<Session> {
            // This is not needed post we migrate to Janus
            // This is needed to handle the case for the first time web load on a browser where cookie won;t be present and web
            // won't be passing st / at as well
            const session: Session = req.session
            if (session.userId) {
                return req.session
            }
            const deviceId = req.headers["deviceid"] || req.signedCookies["deviceId"] || req.cookies["deviceId"] || crypto.randomUUID()
            const appVersion: string = req.headers["appversion"] as string
            const user = this.getGuestUser()
            const loginResponse = await this.authBusiness.registerDeviceAndCreateSession(user, deviceId, appVersion, req, res, true)
            return loginResponse.session
        }

        private getGuestUser(): User {
            const user: User = {
                id: "0",
                firstName: null,
                lastName: null,
                email: null,
                isEmailVerified: false,
                unverifiedEmail: null,
                phone: null,
                unverifiedPhone: null,
                profilePictureUrl: null,
                birthday: null,
                gender: null,
                googleUserId: null,
                facebookUserID: null,
                firebasePassword: null,
                workEmail: null,
                isWorkEmailVerified: false,
                unverifiedWorkEmail: null,
                isNewUser: true,
                isPhoneVerified: false,
                isInternalUser: false,
                isSubscribedForCommunications: false,
                isSubscribedForWhatsapp: WhatsappEnum.PENDING
            }
            return user
        }


        @httpGet("/countries")
        async fetchSupportedCountriesData(req: express.Request, res: express.Response): Promise<{ countriesData: CountryData[] }> {
            const userContext: UserContext = req.userContext as UserContext
            // fill countries data
            const tenant: Tenant = AppUtil.getTenantFromReq(req)

            const countries = await this.countryService.listCountries(tenant)
            let countriesData: CountryData[] = _.map(countries, country => {
                return {
                    countryId: country.countryId,
                    name: country.name,
                    countryCallingCode: country.countryCallingCode,
                    phoneLoginSupported: country.phoneLoginSupported,
                    flagImage: country.flagImage,
                    phoneNumberMaxLength: country.phoneNumberMaxLength
                }
            })
            if (!AppUtil.isDubaiEnabled(userContext)) {
                countriesData = _.filter(countriesData, country => country.countryId === "IN")
            }
            return {
                countriesData
            }
        }

        @httpPost("/updateLocation")
        updateLocation(req: express.Request): Promise<{ success: boolean }> {
            const session: Session = req.session
            const location: Location = req.body
            const promises: Promise<any>[] = []
            if (location.type === "GATE") {
                this.gateService.getGate(location.locationId).then(deliveryGate => {
                    // Storing the locality instead of address
                    location.address = deliveryGate.locality
                    if (deliveryGate.status === "DRAFT")
                        promises.push(this.gateService.updateLocationInterest(location, session.userId))
                })
            } else {
                promises.push(this.gateService.updateLocationInterest(location, session.userId))
            }
            session.sessionData.gateId = location.locationId
            session.sessionData.locationName = location.name
            session.sessionData.locationAddress = location.address
            promises.push(this.sessionBusiness.updateSessionData(session.at, session.sessionData))

            return Promise.all(promises).then(() => {
                return { "success": true }
            })
        }

        @httpPost("/updateBrowseLocation")
        async updateBrowseLocation(req: express.Request): Promise<UpdateBrowseLocationResponse> {
            const session: Session = req.session
            const payload: UpdateBrowseLocationPayload = req.body
            const tenant: Tenant = AppUtil.getTenantFromReq(req)
            return this.userBusiness.updateBrowseLocation(session, payload, tenant)
        }

        @httpPost("/updateCultCenter")
        updateCultCenter(req: express.Request): Promise<{ success: boolean }> {
            const session: Session = req.session
            const data: { centerId: string, centerServiceCultCenterId: string } = req.body
            session.sessionData.cultCenterId = data.centerId
            session.sessionData.centerServiceCultCenterId = data.centerServiceCultCenterId
            return this.sessionBusiness.updateSessionData(session.at, session.sessionData).then(session => {
                return { success: true }
            })
        }

        @httpPost("/updateMindCenter")
        updateMindCenter(req: express.Request): Promise<{ success: boolean }> {
            const session: Session = req.session
            const data: { centerId: string } = req.body
            session.sessionData.mindCenterId = data.centerId
            return this.sessionBusiness.updateSessionData(session.at, session.sessionData).then(session => {
                return { success: true }
            })
        }

        @httpPost("/updateGymPTCenter")
        updateGymPTCenter(req: express.Request): Promise<{ success: boolean }> {
            const session: Session = req.session
            const data: { centerId: string } = req.body
            session.sessionData.gymPTCenterId = data.centerId
            return this.sessionBusiness.updateSessionData(session.at, session.sessionData).then(session => {
                return { success: true }
            })
        }

        @httpPost("/sendAppLink")
        async sendAppLink(req: express.Request): Promise<void> {
            const session: Session = req.session
            const payload: { phone: string } = req.body
            const phone = payload.phone
            if (_.isNil(phone) || phone.length === 0) {
                throw this.errorFactory.withCode(ErrorCodes.SEND_LINK_INVALID_PHONE_NUMBER_ERR, 400).withDebugMessage("Invalid phone number to send app link").build()
            }
            const sanitizedPhone = this.smsService.sanitizeNumber(phone)
            await this.smsService.sendSMS(sanitizedPhone,
                encodeURIComponent("Planning daily fitness tasks, tracking progress and sticking to healthy habits made easy with the cult.fit app. Download now! https://cure.app.link/rVqKWKRlQC"))
        }

        @httpGet("/pack/browse/tata-neu-pwa")
        async getActivePacksForTataNeuAppInApp(req: express.Request): Promise<{
            activePacks?: ActivePackViewV1[],
            emptyStatewidget?: EmptyStateWidget
        }> {
            const userContext: UserContext = req.userContext
            const activePackViewList: ActivePackViewV1[] = await this.userBusiness.getCultAndLiveAssociatedActivePacks(userContext)

            if (_.isEmpty(activePackViewList)) {
                const emptyStatewidget: EmptyStateWidget = {
                    title: "Your packs go here.",
                    subTitle: "You can manage your active memberships and packs from here.",
                    action: {
                        title: "Explore",
                        actionType: "NAVIGATION",
                        url: "curefit://hometab"
                    },
                    widgetType: "EMPTY_STATE_WIDGET"
                }
                return { emptyStatewidget: emptyStatewidget }
            }

            return { activePacks: activePackViewList }
        }

        @httpGet("/pack/browse/v1")
        async getActivePacksV1(req: express.Request): Promise<{
            activePacks?: ActivePackViewV1[],
            emptyStatewidget?: EmptyStateWidget
        }> {
            const session: Session = req.session
            const userId: string = session.userId
            const osName: string = req.headers["osname"] as string
            const appVersion: string = req.headers["appversion"] as string
            const userAgent: UserAgent = session.userAgent
            const limit: number = req.query.limit
            const activePacks = await this.userBusiness.getActivePacksV1(req.userContext as UserContext, limit)
            if (_.isEmpty(activePacks)) {
                const emptyStatewidget: EmptyStateWidget = {
                    title: "Your packs go here.",
                    subTitle: "You can manage your active memberships and packs from here.",
                    action: {
                        title: "Explore",
                        actionType: "NAVIGATION",
                        url: "curefit://hometab"
                    },
                    widgetType: "EMPTY_STATE_WIDGET"
                }
                return { emptyStatewidget: emptyStatewidget }
            } else {
                return { activePacks: activePacks }
            }
        }

        @httpGet("/recommendation")
        async getRecommendations(req: express.Request): Promise<RecommendationView> {
            return this.recommendationBusiness.getRecommendations(req.userContext)
        }

        @httpGet("/deleteCard")
        async deleteSavedCard(req: express.Request): Promise<{ status: boolean }> {

            const session: Session = req.session
            const userId: string = session.userId
            const paymentChannel: PaymentChannel = req.query.paymentChannel
            const tokenId: string = req.query.tokenId
            const cardsAfterDelete = await this.paymentClient.deleteSavedCard(paymentChannel, userId, tokenId)
            let isCardDeleted = false
            if (cardsAfterDelete) {
                const cardMatch = cardsAfterDelete.cards.find((card) => {
                    if (card.tokenId === tokenId)
                        return true
                    return false
                })
                if (!cardMatch)
                    isCardDeleted = true
            }
            return { status: isCardDeleted }
        }

        @httpGet("/paymentInfo")
        async getPaymentInformation(req: express.Request): Promise<MyPaymentInfoView> {
            const session: Session = req.session
            const userId: string = session.userId
            const userContext = req.userContext as UserContext
            const paymentView: MyPaymentInfoView = {
                cards: [],
                wallets: []
            }
            const savedCardPromises: Promise<{ obj: SavedCardList; err?: any }>[] = []
            SavedCardEnabledChannels.forEach((channel) => {
                savedCardPromises.push(eternalPromise(this.paymentClient.getSavedCards(<PaymentChannel>channel, userId)))
            })
            await Promise.all(savedCardPromises)
            savedCardPromises.forEach(async (savedCardPromise) => {
                const resultsc = await savedCardPromise
                if (!resultsc.err) {
                    if (resultsc.obj) {
                        const sc: SavedCardList = resultsc.obj
                        if (!_.isEmpty(sc)) {
                            sc.cards.forEach((cardsc) => {
                                const cardview: SavedCardView = Object.assign({}, cardsc, { paymentChannel: sc.channel })
                                paymentView.cards.push(cardview)
                            })

                        }

                    }
                }
            })

            // const isPaypalLinkedPromise = eternalPromise(this.paymentClient.paypalVerifyAccountLink(userId), "isPaypalLinked")
            // const isPaytmLinkedPromise = eternalPromise(this.paymentClient.isPaytmLinked(userId, true), "isPaytmLinked")

            // const isPaytmLinkedResult = await isPaytmLinkedPromise
            // let isPaytmLinked: boolean = isPaytmLinkedResult && isPaytmLinkedResult.obj ? isPaytmLinkedResult.obj.isLinked : false
            // if (isPaytmLinkedResult.err) {
            //     this.logger.error(isPaytmLinkedResult.err)
            // }
            // const isPaypalLinkedResult = await isPaypalLinkedPromise
            // const isPaypalLinked: boolean = isPaypalLinkedResult && isPaypalLinkedResult.obj ? isPaypalLinkedResult.obj.isLinked : false
            // const paypalLinkedEmail: string = _.get(isPaypalLinkedResult, "obj.meta.paypalEmail", undefined)
            // if (isPaypalLinkedResult.err) {
            //     this.logger.error(isPaypalLinkedResult.err)
            // }
            if (await this.userBusiness.isEligibleForJuspayRP(userId, userContext.sessionInfo.deviceId, userContext.sessionInfo.orderSource)) {
                paymentView.paymentModes = [{
                    title: "Saved Payment Modes",
                    paymentChannel: "JUSPAY",
                    buttonTitle: ">",
                    action: {
                        actionType: "NAVIGATION",
                        url: "curefit://juspaymanagement"
                    }
                }]
            }

            // let paytmbalance = null
            // if (isPaytmLinked) {
            //     try {
            //         const checkBalanceResult = await this.paymentClient.paytmCheckBalance(userId)
            //         paytmbalance = checkBalanceResult.amount
            //     } catch (error) {
            //         this.logger.error("error at paytm checkbalance api : ", error)
            //         if (error.responseCode && error.responseCode === 411) {
            //             isPaytmLinked = false
            //         } else if (error.responseCode) {
            //             try {
            //                 isPaytmLinked = (await this.paymentClient.isPaytmLinked(userId)).isLinked
            //             } catch {
            //                 this.logger.error("error at paytm ispaytm linked api : ", error)
            //                 throw error
            //             }
            //         }
            //         else
            //             throw error
            //     }
            // }
            // paymentView.wallets.push({
            //     isLinked: isPaytmLinked,
            //     paymentChannel: "PAYTM",
            //     balance: paytmbalance,
            //     title: "Paytm",
            //     subTitle: isPaytmLinked ? `Balance Rs.${paytmbalance}` : ""
            // })
            // if (AppUtil.isPaypalProfileLinkingSupported(userContext)) {
            //     paymentView.wallets.push({
            //         isLinked: isPaypalLinked,
            //         paymentChannel: "PAYPAL",
            //         title: "PayPal",
            //         subTitle: _.isNil(paypalLinkedEmail) ? "" : paypalLinkedEmail
            //     })
            // } else if (isPaypalLinked) {
            //     paymentView.wallets.push({
            //         isLinked: isPaypalLinked,
            //         paymentChannel: "PAYPAL",
            //         title: "PayPal",
            //         subTitle: _.isNil(paypalLinkedEmail) ? "" : paypalLinkedEmail
            //     })
            // }

            const isInternationalApp = AppUtil.isInternationalApp(userContext)
            const countryId = userContext.userProfile.city.countryId

            const isCultSportWebApp = AppUtil.isCultSportWebApp(userContext)
            const isGiftCardSupported = await AppUtil.isGiftCardSupported(userContext) || isCultSportWebApp
            const isUnifiedGiftCardSupported = await AppUtil.isUnifiedGiftCardPageSupported(userContext) || isCultSportWebApp
            if (!isUnifiedGiftCardSupported && !isInternationalApp && countryId === "IN" && AppUtil.isFitcashInsidePaymentsSupported(userContext)) {
                let fitcashBalance: number = 0
                try {
                    const fitcashBalanceResponse: WalletBalance = await this.fitcashService.balance(userId, userContext.userProfile.city.country.currencyCode)
                    fitcashBalance = fitcashBalanceResponse.balance / 100
                } catch (error) {
                    const genericErr: GenericError = new GenericError(({ message: error.message }))
                    genericErr.statusCode = 500
                    this.rollbarService.handleError(genericErr)
                }

                paymentView.wallets.push({
                    isLinked: false,
                    paymentChannel: "FITCASH",
                    title: "Fitcash",
                    subTitle: "",
                    fitcash: fitcashBalance,
                    action: {
                        actionType: "NAVIGATION",
                        url: ActionUtil.getFitcashPageUrl()
                    }
                })
            }
            try {
                const segmentsForTataNeu = TataNeuUtil.getSegmentsForTataNeu()
                const hasUserGivenConsent: boolean = await TataNeuUtil.doesUserBelongToTataSegment(this.segmentationClient, segmentsForTataNeu.NEUPASS_USER, userId)
                if (hasUserGivenConsent) {
                    const neuCoinsBalanceResponse: TataLoyaltyPointsResponse = await this.thirdPartyService.getCurrentTataLoyaltyPoints(userId)
                    paymentView.tataNeuLoyaltyPoints = {
                        title: "NeuCoins",
                        subTitle: `1 NeuCoin = ₹${neuCoinsBalanceResponse.pointsToCurrencyRatio}`,
                        buttonSubTitle: `${neuCoinsBalanceResponse.promisedPoints} NeuCoins pending`,
                        buttonTitle: `${neuCoinsBalanceResponse.loyaltyPoints}`
                    }
                }
            } catch (err) {
                this.logger.error(`Failed to fetch Tata loyalty points.`, { err })
            }

            if (isGiftCardSupported) {
                let giftCardBalance: number = 0
                let fitcashBalance: number = 0
                try {
                    const userWalletResponse: UserWalletBalanceResponse = await this.giftCardWalletService.getWalletBalance({userId})
                    giftCardBalance = userWalletResponse.balance
                } catch (error) {}
                if (isUnifiedGiftCardSupported) {
                    try {
                        const fitcashBalanceResponse: WalletBalance = await this.fitcashService.balance(userId, userContext.userProfile.city.country.currencyCode)
                        fitcashBalance = fitcashBalanceResponse.balance / 100
                    } catch (error) {}
                }
                paymentView.wallets.push({
                    isLinked: false,
                    paymentChannel: "FITCASH",
                    title: isUnifiedGiftCardSupported ? "Gift Card + Fitcash" : "Gift Card",
                    subTitle: "",
                    fitcash: giftCardBalance + fitcashBalance,
                    action: {
                        actionType: "NAVIGATION",
                        url: ActionUtil.getGiftCardTransactionsPageUrl()
                    }
                })
            }
            return paymentView

        }

        @httpGet("/medicalrecords")
        async getMedicalRecordsSection(req: express.Request): Promise<any> {
            const userContext: UserContext = req.userContext
            const cityId = userContext.userProfile.city.cityId
            const clientVersion = userContext.sessionInfo.clientVersion
            let isLabTestSegmentSupported = false
            if (AppUtil.isProdLike) {
                const segment = await this.serviceInterfaces.segmentService.doesUserBelongToSegment(LAB_TEST_CYCLOP_SEGMENT, userContext)
                if (!_.isEmpty(segment)) {
                    isLabTestSegmentSupported = true
                }
            }
            return { records: CareUtil.addCareMappedActionsV2(cityId, clientVersion, isLabTestSegmentSupported) }
        }

        @httpGet("/support")
        getSupportJSON() {
            return this.supportPageConfig.getSupportResponse()
        }

        @httpGet("/profilePicture/signedUrl")
        async getProfilePictureUploadSignedUrl(req: express.Request): Promise<SignedUrlResponse> {
            const fileName: string = req.query.fileName as string
            const bucketName = (process.env.ENVIRONMENT === "PRODUCTION") ? "profile-pictures-prod" : "profile-pictures-staging"
            return this.mediaGatewayClient.getPresignedPutUrl({
                bucketName,
                path: "",
                fileName,
                contentType: MediaType.IMAGE,
                maxUploadSize: 10485760,
                objectAcl: ObjectAcl.PUBLIC_READ
            })
        }

        @httpPost("/uploadProfilePicture")
        async uploadProfilePicture(req: express.Request): Promise<UserView> {
            const fileName = req.body.fileName as string
            const fileNameAfterValidation = await this.mediaGatewayClient.validateAndGetDestinationUrl(fileName)
            const userId: string = req.session.userId
            const user = await this.userService.uploadProfilePicture(userId, fileNameAfterValidation)
            return new UserView(user, req.session.isNotLoggedIn)
        }

        @httpPost("/announcement/:id")
        async updateAnnouncementState(req: express.Request): Promise<boolean> {
            const announcementId: AnnouncementId = req.params.id
            const userId = req.session.userId
            const payload: { state: AnnouncementState } = req.body
            this.logger.info(`updateAnnouncementState request`, { reqBody: req.body, userId: userId, payload: payload })
            return await this.announcementBusiness.updateAnnouncementState(userId, announcementId, payload.state)
        }
        @httpPost("/updateBulkAnnouncement")
        async updateBulkAnnouncementState(req: express.Request): Promise<boolean[]> {
            const userId = req.session.userId
            const payload: { announcementId: AnnouncementId, state: AnnouncementState }[] = req.body
            this.logger.info(`updateBulkAnnouncement request`, { reqBody: req.body, userId: userId, payload: payload })
            const announcementPromiseArray = payload.map(announcement => {
                return this.announcementBusiness.updateAnnouncementState(userId, announcement.announcementId, announcement.state)
            })
            return await Promise.all(announcementPromiseArray)
        }

        @httpPost("/addSubUser")
        async addSubUser(req: express.Request): Promise<SubUserDetails> {
            const session: Session = req.session
            const userId: string = session.userId
            const cityId: string = session.sessionData.cityId
            const userContext = req.userContext
            const cultCityId: number = await this.cultBusiness.getCultCityId(userContext, cityId, userId)
            const subUser: SubUserRequestPayload = req.body
            if (subUser.user.firstName !== undefined) {
                subUser.user.firstName = capitalizeFirstLetter(subUser.user.firstName)
            }
            if (subUser.user.lastName !== undefined) {
                subUser.user.lastName = capitalizeFirstLetter(subUser.user.lastName)
            }
            if (subUser.user.gender !== undefined) {
                subUser.user.gender = subUser.user.gender.toLowerCase()
            }
            if (subUser.userAttributes.emergencyContactName !== undefined) {
                subUser.userAttributes.emergencyContactName = capitalizeFirstLetter(subUser.userAttributes.emergencyContactName)
            }
            subUser["relation"] = "CHILD"
            return this.cultFitService.addSubUserDetails(cultCityId, userId, subUser, process.env.APP_NAME)
        }

        @httpPost("/invite/")
        async inviteUser(req: express.Request): Promise<boolean> {
            const phoneNumber: string = req.body.phoneNumber
            const userId: string = req.session.userId
            const countryCallingCode = req.body.countryCallingCode
            return await this.cultFitService.inviteNewUser(phoneNumber, countryCallingCode, userId, "CUREFIT_API")
        }

        @httpPost("/location")
        async updatePincodeLocation(req: express.Request): Promise<any> {
            const session: Session = req.session
            const userContext: UserContext = req.userContext as UserContext
            return this.userBusiness.updatePincodeLocation(session, req.body, AppUtil.isCultSportWebApp(userContext) ? "GEAR" : undefined)
        }


        @httpPost("/fbLinkUser")
        async fbLinkUser(req: express.Request): Promise<{ success: boolean, user: UserView }> {
            const session: Session = req.session
            const deviceId: string = this.authBusiness.validateAndGetDeviceId(req)
            const user = await this.userService.fbLinkUser(session.userId, req.body.id, req.body.accessToken, req.body.expiresIn, deviceId)
            return { success: true, user: new UserView(user, true) }
        }

        @httpPost("/googleLinkUser")
        async googleLinkUser(req: express.Request): Promise<{ success: boolean, user: UserView }> {
            const session: Session = req.session
            const deviceId: string = this.authBusiness.validateAndGetDeviceId(req)
            const user = await this.userService.googleLinkUser(session.userId, req.body.idToken, req.body.accessToken, deviceId)
            return { success: true, user: new UserView(user, true) }
        }

        @httpPost("/unlinkEmail")
        async unlinkEmail(req: express.Request, res: express.Response): Promise<{ unlinked: boolean, user: UserView }> {
            throw this.errorFactory.withCode(ErrorCodes.EMAIL_UNLINK_NOT_ALLOWED_ERR, 400).withDebugMessage("Email unlink not supported now").build()
            // const session: Session = req.session
            // const forceUnlink = req.body.forceUnlink
            // const response = await this.userService.unlinkEmail(session.userId, forceUnlink)
            // if (forceUnlink) {
            //     await this.logout(req, res)
            // }
            // return { unlinked: response.unlinked, user: new UserView(response.user, true) }

        }

        @httpPost("/unlinkPhone")
        async unlinkPhone(req: express.Request, res: express.Response): Promise<{ unlinked: boolean, user: UserView }> {
            throw this.errorFactory.withCode(ErrorCodes.UNLINK_NOT_ALLOWED_ERR, 400).withDebugMessage("Phone unlink not supported now").build()
            // const session: Session = req.session
            // const forceUnlink = req.body.forceUnlink
            // const response = await this.userService.unlinkPhone(session.userId, forceUnlink)
            // if (forceUnlink) {
            //     await this.logout(req, res)
            // }
            // return { unlinked: response.unlinked, user: new UserView(response.user, true) }
        }

        private async logout(req: express.Request, res: express.Response) {
            const session: Session = req.session
            const deviceId: string = req.headers["deviceid"] as string
            const tenant: Tenant = AppUtil.getTenantFromReq(req)
            await this.sessionBusiness.expireSession(session)
            AuthUtil.clearCookies(res, req)
            await this.deviceBusiness.logoutDevice(deviceId, tenant)

        }
        @httpPost("/unlinkFacebook")
        async unlinkFacebook(req: express.Request, res: express.Response): Promise<{ unlinked: boolean, user: UserView }> {
            const session: Session = req.session
            const forceUnlink = req.body.forceUnlink
            const response = await this.userService.unlinkFacebook(session.userId, forceUnlink)
            if (forceUnlink) {
                await this.logout(req, res)
            }
            return { unlinked: response.unlinked, user: new UserView(response.user, true) }
        }

        @httpPost("/setEmailSendOtp")
        async setEmailSendOtp(req: express.Request, res: express.Response): Promise<{ success: boolean }> {
            const session: Session = req.session
            const userAgent: UserAgent = AuthUtil.getUserAgent(req)
            if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
                const captchaResponse = req.body.captchaResponse
                const verifyCaptchaResp = await this.captchaBusiness.verifyCaptchaLogin(captchaResponse, AppUtil.callSource(AuthUtil.getApiKeyFromReq(req)))
                this.logger.info(` /setEmailSendOtp  verifyCaptchaResp`, verifyCaptchaResp)
                if (verifyCaptchaResp.success && verifyCaptchaResp.score > 0.6) {
                    return this.checkEmailSendOtp(req)
                } else {
                    this.metricsUtil.reportBlockedRequest(BlockingType.CAPTCHA_FAILURE)
                    this.logger.error(`AuthController:Failed to verify captcha Error: Failed to verify. Please try again`)
                    res.status(400).send({ statusCode: 400, error: "Bad Request", message: "captcha not valid" })
                }
                return
            }
            return this.checkEmailSendOtp(req)

        }

        async checkEmailSendOtp(req: express.Request) {
            const { session, body } = req
            const emailType: EMAIL_TYPE = body.emailType
            if (emailType === "PRIMARY") {
                await this.userService.setEmailSendOtp(session.userId, body.email)
            } else if (emailType === "WORK") {
                await this.userService.setWorkEmailSendOtp(session.userId, body.email)
            } else {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Not supported email type").build()
            }
            return { success: true }
        }

        @httpPost("/whatsappOptIn")
        async optInForWhatsAppUpdates(req: express.Request): Promise<any> {
            const userContext: UserContext = req.userContext as UserContext
            const newState = await this.userService.updateUserWhatsappState(userContext.userProfile.userId, WhatsappEnum.APPROVED)
            if (parseInt(newState.state) === WhatsappEnum.APPROVED) {
                const contextsWhatsapp = [{
                    userId: userContext.userProfile.userId,
                    tags: {}
                }]
                const request: SendCampaignNotificationsRequest = {
                    campaignId: "CUREFIT_WHATSAPP_MESSAGE",
                    creativeIds: ["WHATSAPP_FIRST_MESSAGE"],
                    userContexts: contextsWhatsapp
                }
                await this.campaignService.sendCampaignMessagesThroughOneCreative(request)
                return { success: true, data: this.getOptedInResponse() }
            }
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Error in whatsappOptIn").build()
        }

        private getOptedInResponse() {
            return {
                title: "Thanks for opting in",
                message: "You will now receive updates on WhatsApp",
                iconUrl: "https://cdn-images.cure.fit/www-curefit-com/image/upload/image/optin/opted_in.png",
            }
        }

        @httpPost("/whatsappOptOut")
        async optOutForWhatsAppUpdates(req: express.Request): Promise<any> {
            const userContext: UserContext = req.userContext as UserContext
            const newState = await this.userService.updateUserWhatsappState(userContext.userProfile.userId, WhatsappEnum.DENIED)
            if (parseInt(newState.state) === WhatsappEnum.DENIED) {
                return { success: true, data: this.getOptedOutResponse() }
            }
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Error in whatsappOptOut").build()
        }

        private getOptedOutResponse() {
            return {
                title: "Thanks for your response.",
                message: "We will not be sending you updates on WhatsApp.",
                iconUrl: ""
            }
        }

        @httpPost("/whatsappOptinReset")
        async resetWhatsappOptin(req: express.Request): Promise<any> {
            const userContext: UserContext = req.userContext as UserContext
            const newState = await this.userService.updateUserWhatsappState(userContext.userProfile.userId, WhatsappEnum.PENDING)
            if (parseInt(newState.state) === WhatsappEnum.PENDING) {
                return { success: true }
            }
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Error in whatsappOptinReset").build()
        }

        @httpPost("/verifyEmailThroughOtp")
        async verifyEmailThroughOtp(req: express.Request): Promise<{ success: boolean, user: UserView }> {
            const session: Session = req.session
            const emailType: EMAIL_TYPE = req.body.emailType
            let user: User
            if (isNaN(req.body.otp)) {
                this.logger.error("received non number otp: " + req.body.otp)
                this.rollbarService.handleError(new Error("received non number otp: " + req.body.otp))
            }
            if (emailType === "PRIMARY") {
                user = await this.userService.verifyEmailThroughOtp(session.userId, req.body.email, req.body.otp + "")
            } else if (emailType === "WORK") {
                user = await this.userService.verifyWorkEmailThroughOtp(session.userId, req.body.email, req.body.otp + "", req.body.cityId)
            } else {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Not supported email type").build()
            }
            return { success: true, user: new UserView(user, true) }
        }

        @httpGet("/yearEndReport")
        async getUserYearEndReport(req: express.Request): Promise<UserYearlyReport[]> {
            const userContext: UserContext = req.userContext as UserContext
            return this.userYearlyReportBusiness.getYearEndReport(userContext)
        }

        @httpGet("/yearEndReport/v2")
        async getUserYearEndReportV2(req: express.Request): Promise<UserYearlyReport[]> {
            const userContext: UserContext = req.userContext as UserContext
            return this.userYearlyReportBusiness.getYearEndReportV2(userContext)
        }

        @httpGet("/yearEndReport/2022")
        async getUserYearReport2024(req: express.Request): Promise<any> {
            const userContext: UserContext = req.userContext as UserContext

            try {
                // return this.userYearReport2022Business.getUserYearReport2024(userContext)
                return this.userYearReport2022Business.getUserAchievemtReport(userContext)
            } catch (e) {
                await this.logger.error("Error in year end report 2024- " + e)
                await this.rollbarService.sendError(e)
            }
            return null
        }


        @httpGet("/yearEndReport/2023")
        async getUserYearReport(req: express.Request): Promise<any> {
            const questionId: string = req.query && req.query.questionId ? req.query.questionId.toString() : null
            const answerId: string = req.query && req.query.answerId ? req.query.answerId.toString() : null
            const userContext: UserContext = req.userContext as UserContext
            return this.userYearReport2023Business.getUserEndYearReport2023(userContext, questionId, answerId)
        }

        private async gearZipCodeServiceabilityInfo(addresses: UserDeliveryAddress[]): Promise<ZipcodeServiceabilityInfo> {
            if (_.isEmpty(addresses)) {
                return null
            }

            const zipCodes: string[] = []

            // using for loop to await async call inside it
            for (let address of addresses) {
                if (_.isEmpty(address.structuredAddress)) {
                    try {
                        address = await this.userBusiness.augmentStructuredAddress(
                            address.userId,
                            address.addressId,
                            address
                        )
                    } catch {
                        // ignore error
                        continue
                    }
                }

                if (address && address.structuredAddress && address.structuredAddress.pincode) {
                    zipCodes.push(address.structuredAddress.pincode)
                }
            }

            if (_.isEmpty(zipCodes)) {
                return null
            }

            return await this.gearService.getAddressChangeServiceability(zipCodes)
        }

        @httpGet("/fitstore/serviceablestates")
        async getServiceableStates(req: express.Request): Promise<any> {
            return { states: await this.getCultsportServicableStates() }
        }

        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), 1 * TimeUtil.TIME_IN_SECONDS.DAY)
        async getCultsportServicableStates(): Promise<GearState[]> {
            return (await this.gearService.getListOfStates()).states
        }

        @httpGet("/locationPreference")
        async getLocationPreference(req: express.Request): Promise<LocationPreferenceResponseEntity> {
            const session: Session = req.session
            const cityId: string = session.sessionData.cityId
            const userId: string = session.userId
            return this.userBusiness.getLocationPreference(req.userContext as UserContext, userId, cityId)
        }

        @httpPost("/locationPreference")
        async setLocationPreference(req: express.Request): Promise<UpdateStatusResponseEntity> {
            const session: Session = req.session
            const cityId: string = session.sessionData.cityId
            const userId: string = session.userId
            const pageFrom: string = req.query.pageFrom
            const preference = <LocationPreferenceRequestEntity>req.body
            return this.userBusiness.setLocationPreference(req.userContext as UserContext, userId, cityId, preference, pageFrom)
        }

        @httpGet("/fitCashButton")
        async getFitCashDetails(req: express.Request): Promise<any> {
            const session: Session = req.session
            const userId: string = session.userId
            const userContext: UserContext = req.userContext as UserContext
            const fitcashPromise = this.fitcashService.balance(userId, userContext.userProfile.city.country.currencyCode)

            let fitcashBalance: number
            const fitcashBalanceResponse: WalletBalance = await fitcashPromise
            fitcashBalance = fitcashBalanceResponse.balance / 100
            let giftCardBalance: number = 0
            let totalBalance: number = 0
            const isUnifiedGiftCardSupported = await AppUtil.isUnifiedGiftCardPageSupported(userContext) || AppUtil.isCultSportWebApp(userContext)
            if (isUnifiedGiftCardSupported) {
                try {
                    const userWalletResponse: UserWalletBalanceResponse = await this.giftCardWalletService.getWalletBalance({userId})
                    giftCardBalance = userWalletResponse.balance
                } catch (error) {}
            }
            totalBalance = giftCardBalance + fitcashBalance
            if (totalBalance > 0) {
                return {
                    balance: totalBalance,
                    fitCashAction: {
                        actionType: "NAVIGATION",
                        url: isUnifiedGiftCardSupported ? ActionUtil.getGiftCardTransactionsPageUrl() : ActionUtil.getFitcashPageUrl()
                    },
                }
            }
            return null
        }

        @httpPost("/getShopifyUrlWithMultipassToken/:cfUserId")
        async getShopifyUrlWithMultipassToken(req: express.Request, res: express.Response): Promise<string> {
            const sessionUserId: string = req.session.userId
            const cfUserId = req.params.cfUserId
            if ((sessionUserId != null || cfUserId != null) && sessionUserId != cfUserId) {
                res.status(401).send({ message: "Authorization header missing" })
            }
            return this.cultstoreShopifyService.getShopifyUrlWithMultipassToken(sessionUserId, req.body.shopifyRedirectUrl)
        }
        @httpPost("/getShopifyMultipassAction/:cfUserId")
        async getShopifyMultipassAction(req: express.Request, res: express.Response): Promise<{action: Action}> {
            const sessionUserId: string = req.session.userId
            const cfUserId = req.params.cfUserId
            if ((sessionUserId != null || cfUserId != null) && sessionUserId != cfUserId) {
                res.status(401).send({ message: "Authorization header missing" })
            }
            const isAppWebview: boolean = req.body.isAppWebview
            const shopifyRedirectUrl: string = req.body.shopifyRedirectUrl
            const multipassUrl = await this.cultstoreShopifyService.getShopifyUrlWithMultipassToken(sessionUserId, shopifyRedirectUrl)
            const action: Action = {
                actionType: "NAVIGATION",
                url: multipassUrl,
                trigger: true,
                payload: {
                    "target": isAppWebview ? "_self" : "_blank"
                }
            }
            return {action}
        }
    }

    return UserController
}

export default controllerFactory