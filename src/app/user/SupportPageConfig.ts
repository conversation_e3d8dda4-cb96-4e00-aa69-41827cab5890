import { inject, injectable } from "inversify"
import { DEFAULT_CACHE_REFRESH_TIME_IN_SECONDS, InMemoryCacheService } from "@curefit/memory-cache"
import { IPageConfigReadOnlyDao, IPageConfigReadWriteDao, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import { SupportContact, SupportFAQ, SupportPolicy } from "@curefit/issue-common"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

@injectable()
class SupportPageConfig extends InMemoryCacheService<any> {

    constructor(
        @inject(PAGE_CONFIG_TYPES.PageConfigReadOnlySecondaryDao) protected pageConfigDao: IPageConfigReadOnlyDao,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger, 36 * 60)
        this.load("SupportPageConfig")
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    loadData(): Promise<any> {
        return this.pageConfigDao.findOne({ pageId: { $eq: "SupportPageConfig" } }).then(pageConfig => {
            const data = pageConfig.data
            this.faq = data.faq
            this.policy = data.policy
            this.contact = data.contact
            return data
        })
    }

    public faq: SupportFAQ
    public policy: SupportPolicy
    public contact: SupportContact

    public getSupportResponse() {
        return {
            "sections": [
                {
                    "title": this.faq.title,
                    "value": this.faq.value
                },
                {
                    "title": this.policy.title,
                    "value": this.policy.value
                },
                {
                    "title": this.contact.title,
                    "value": this.contact.value
                }
            ]
        }
    }
}

export default SupportPageConfig
