import { ActivityType } from "@curefit/product-common"
import { ActivityState } from "@curefit/logging-common"
import {
    Action,
    CallReminderWidget,
    CultCafeWidget, ShareActionWidget,
    ManageOptions,
    SlotActionType,
    ViewType,
    WidgetView, CultBuddiesJoiningListSmallView
} from "../common/views/WidgetView"
import OrderTrackingStatusWidget from "../common/views/OrderTrackingStatusWidget"

export interface ActivityHeaderWidget extends WidgetView {
    title: string
    subTitle?: string
    calloutText?: string
}

export interface ViewTypeCard {
    viewType: ViewType,
    list: CarouselCardMeta[] | string[]
    action?: Action,
    title?: string,
    subTitle?: string
}

export interface CarouselCardMeta {
    header: string,
    title: string,
    image: string,
    subTitle: string,
    action: Action,
    quickActions?: Action[]
}

export interface ActivityWidget extends WidgetView {
    userActivityId?: string
    activityType: ActivityType
    title: string
    date: string
    subTitle: string
    status?: ActivityState
    metaText?: string
    calloutText?: string
    nudgeText?: string
    startTime?: string
    closedSection: {
        title: string
        subTitle?: string
    }
    meta?: { progress?: number, chatActive?: boolean }
    action: Action
    images?: string[] // To be deprecated
    subWidget?: OrderTrackingStatusWidget
    quickActions?: Action[]
    bullets?: string[] // To be deprecated
    subActivities?: ViewTypeCard[]
}
export interface Score {
    todoCount: number
    level: number
    streak: number
    progress: number
    activities: number
    points: number
    target: number
    numDays: number
    isLevelDirty: boolean
}

export interface TimelineViewV1 {
    dateMap?: { [date: string]: WidgetView[] }
    score?: Score
    metrics?: any
    addOptionWidget?: any
    stepsGoal: number
}
export interface UserActivity {
    date: string,
    activityType: ActivityType
    subActivityType?: string
    status?: ActivityState
    title: string
    subTitle?: string
    activityName: string
    activityAction?: Action
    action?: string
    duration?: number
    deliveryStatus?: string
    schedule?: {
        startTime: string
        endTime: string
    },
    isPulseClass?: boolean,
    pulseDeviceName?: string,
    steps?: {
        done: number, goal: number
    }
    ivrConfig?: CallReminderWidget
    clickActions?: {
        title: string
        action: string
    }[]
    manageOptions?: ManageOptions
    deliveryDetail?: {
        date: string
        slotStart: string
        slotEnd: string
        state: string
        cutOffMessage?: string
    }
    messageText?: string
    meta?: any
    content?: { id: string, type: string, format: string, URL: string }
    image?: string
    images?: string[]
    userActivityId: String
    packStatus?: PackStatus,
    subtitle?: string
    workoutId?: number,
    workoutIcon?: string,
    source?: {
        sourceId: string,
        sourceName: string
    }
    infoBlock?: {
        title?: string
        message?: string
        calloutView?: {
            bgColor?: string
            tag?: string
            title: string
            subTitle: string
        }
    }
    savings?: string
    totalFitCashEarned?: string
    cultCafeBlock?: CultCafeWidget
    inviteBuddy?: ShareActionWidget
    buddiesJoining?: CultBuddiesJoiningListSmallView
    notification?: any
    creditCost?: number
}

export interface PackStatus {
    packName?: string
    packId: string
    total: number
    current: number
    expiryMessage?: string
    lastActive?: string
}


export type TimelineView = UserActivity[]
