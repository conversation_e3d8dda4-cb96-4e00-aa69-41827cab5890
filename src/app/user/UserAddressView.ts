import {
  AddressTag,
  DeliveryInstruction,
  UserDeliveryAddress
} from "@curefit/eat-common"
import { capitalizeFirstLetter } from "@curefit/util-common"
import EatUtil from "../util/EatUtil"
import * as _ from "lodash"
import { ZipcodeServiceabilityInfo } from "@curefit/gear-common"
import { GearDeliveryInfo } from "@curefit/location-common"
import { UserStructuredAddressAugmentation } from "@curefit/location-common"

class UserAddressView {
  constructor(
    address: UserDeliveryAddress,
    gearZipCodeServiceabilityInfo?: ZipcodeServiceabilityInfo,
    isGearDefaultAddress?: boolean
  ) {
    this.addressId = address.addressId
    this.key = address.addressId
    this.name = address.name
    this.phoneNumber = address.phoneNumber
    this.addressType = address.addressType
    this.otherAddressName = address.otherAddressName
    this.addressTypeDisplayText = capitalizeFirstLetter(address.addressType?.toLowerCase() ?? "OTHER")
    this.addressLine1 = address.addressLine1
    this.addressLine2 = address.addressLine2
    this.latLong = address.latLong
    this.gateId = address.gateId
    this.googlePlacesId = address.googlePlacesId
    this.eatDeliveryInstruction = address.eatDeliveryInstruction
    if (address.eatDeliveryInstruction) {
      this.standingInstruction = EatUtil.getStandingInstruction(address.eatDeliveryInstruction)
    }
    if (gearZipCodeServiceabilityInfo) {
      this.gearServiceable =  (address.structuredAddress &&
        _.get(gearZipCodeServiceabilityInfo, address.structuredAddress.pincode)) || false
    }
    this.gearDeliveryInfo = address.gearDeliveryInfo
    this.isDefaultGearAddress = !!isGearDefaultAddress
    this.structuredAddress = address.structuredAddress
    this.locality = address.locality
  }

  key: string
  addressId: string
  addressType?: AddressTag
  addressTypeDisplayText: string
  otherAddressName?: string
  addressLine1: string
  addressLine2: string
  latLong: number[]
  gateId?: string
  googlePlacesId?: string
  serviceable?: boolean
  gearServiceable?: boolean
  eatDeliveryInstruction: DeliveryInstruction
  standingInstruction: string
  name?: string
  phoneNumber?: string
  isDefaultGearAddress?: boolean
  gearDeliveryInfo?: GearDeliveryInfo
  structuredAddress?: UserStructuredAddressAugmentation
  locality?: string
}
export default UserAddressView
