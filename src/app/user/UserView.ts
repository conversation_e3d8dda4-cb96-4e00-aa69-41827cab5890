import * as _ from "lodash"
import { User } from "@curefit/user-common"
import UserAddressView from "./UserAddressView"
import { Gender } from "@curefit/user-common"

class UserView {

    constructor(user: User, isNotLoggedIn: boolean, userAddresses?: UserAddressView[], isPaytmLinked?: boolean, feedbackId?: string, fitcashBalance?: number) {
        this.userId = user.id.toString()
        // this.firebasePassword = user.firebasePassword
        this.email = (!user.email) ? undefined : user.email
        this.phoneNumber = (!user.phone) ? undefined : user.phone
        this.phone = (!user.phone) ? undefined : user.phone
        this.countryCallingCode = user.countryCallingCode
        this.profilePictureUrl = (!user.profilePictureUrl) ? undefined : UserView.getCdnUrl(user.profilePictureUrl)
        this.unverifiedEmail = (!user.unverifiedEmail) ? undefined : user.unverifiedEmail
        this.unverifiedWorkEmail = (!user.unverifiedWorkEmail) ? undefined : user.unverifiedWorkEmail
        this.unverifiedPhone = (!user.unverifiedPhone) ? undefined : user.unverifiedPhone
        this.firstName = (!user.firstName) ? undefined : user.firstName
        this.lastName = (!user.lastName) ? undefined : user.lastName
        this.workEmail = user.workEmail
        this.feedbackId = feedbackId
        this.isPaytmLinked = isPaytmLinked
        this.userAddresses = userAddresses
        this.isInternalUser = UserView.isInternalUser(user)
        this.dotVersion = 13
        this.isNotLoggedIn = isNotLoggedIn ? true : false
        this.isNewUser = user.isNewUser
        this.fitcashBalance = fitcashBalance
        this.birthday = (!user.birthday) ? undefined : user.birthday
        this.gender = (!user.gender) ? undefined : user.gender
        this.profileCompletion = UserView.getProfileCompletionPercentage(user)
        this.isFBLinked = user.facebookUserID ? true : false
        this.isGoogleLinked = user.googleUserId ? true : false
        this.isPhoneChurned = user.isPhoneChurned
    }

    private static getCdnUrl(profilePictureUrl: string): string {
        if (!profilePictureUrl) {
            return profilePictureUrl
        }
        return profilePictureUrl.replace(`profile-pictures-prod.s3.amazonaws.com`, `cdn-profile-pictures.cure.fit`)
    }

    public static getProfileCompletionPercentage(user: User): number {
        const totalFields = 6
        let availableFields = 0
        !_.isEmpty(user.profilePictureUrl) ? availableFields += 1 : availableFields += 0
        !_.isEmpty(UserView.getUserName(user)) ? availableFields += 1 : availableFields += 0
        !_.isEmpty(user.gender) ? availableFields += 1 : availableFields += 0
        !_.isEmpty(user.birthday) ? availableFields += 1 : availableFields += 0
        !_.isEmpty(user.phone) ? availableFields += 1 : availableFields += 0
        !_.isEmpty(user.email) ? availableFields += 1 : availableFields += 0
        const percentage = (availableFields * 100) / totalFields
        return Math.round(percentage)
    }

    public static getUserName(user: User): string {
        const firstName = (!user.firstName) ? "" : user.firstName
        const lastName = (!user.lastName) ? "" : user.lastName
        return firstName + " " + lastName
    }

    public static isAmazonUser(user: User): boolean {
        if (user.email && this.isAmazonEmail(user.email))
            return true
        if (user.workEmail && this.isAmazonEmail(user.workEmail))
            return true
        return false
    }


    public static isAmazonEmail(email: string): boolean {
        const suffix = email.substring(email.indexOf("@") + 1)
        if (suffix === "amazon.com")
            return true
        else
            return false
    }

    public static isInternalUser(user: User): boolean {
        if (user.email) {
            if (user.email.includes("@v.curefit.com") || user.email.includes("@i.curefit.com") || user.email.includes("@cultfit.in") || user.email.includes("@eatfit.in")) {
                return true
            }
        }
        return user.isInternalUser
    }

    public userAddresses: UserAddressView[]
    public userId: string
    public email: string
    public workEmail: string
    public phoneNumber: string
    public phone: string
    public countryCallingCode: string
    public unverifiedEmail?: string
    public unverifiedWorkEmail?: string
    public unverifiedPhone?: string
    public firebasePassword?: string
    public profilePictureUrl: string
    public firstName: string
    public lastName: string
    public feedbackId?: string
    public isPaytmLinked?: boolean
    public isInternalUser: boolean
    // Temp fix for showing dot
    public dotVersion: number
    public isNotLoggedIn: boolean
    public isNewUser: boolean
    public fitcashBalance: number
    public gender: Gender
    public birthday: string
    public profileCompletion?: number
    public isFBLinked?: boolean
    public isGoogleLinked?: boolean
    public isPhoneChurned: boolean

}

Object.seal(UserView)
export default UserView
