import DeliveryGateView from "./DeliveryGateView"
import UserAddressView from "../user/UserAddressView"
import { EatInstructionList, Instruction } from "../util/EatUtil"

class AddressResponse {
  constructor(userAddressList: UserAddressView[], gates: DeliveryGateView[]) {
    this.addresses = userAddressList.filter(address => {
      return address.addressType !== "KIOSK"
    })
    this.kiosks = userAddressList.filter(address => {
      return address.addressType === "KIOSK"
    })
    this.gates = gates
    this.eatInstructionList = EatInstructionList
  }

  addresses: UserAddressView[]
  kiosks: UserAddressView[]
  gates: DeliveryGateView[]
  eatInstructionList: Instruction[]
}
export default AddressResponse
