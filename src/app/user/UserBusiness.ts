import { City, Country, PlaceData, VerticalType } from "@curefit/location-common"
import {
    DeliveryArea,
    DeliveryGate,
    DeliverySubArea,
    ListingBrandIdType,
    UserDeliveryAddress,
} from "@curefit/eat-common"
import { GMF_CLIENT_TYPES, IGMFClient, } from "@curefit/gmf-client"
import { AppTenant, LatLong, OrderSource, UserAgentType as UserAgent } from "@curefit/base-common"
import {
    LocationPreferenceData,
    PreferredLocation,
    Session,
    SessionData,
    SessionInfo,
    UserContext,
} from "@curefit/userinfo-common"
import { ProductType, } from "@curefit/product-common"
import { Tenant, UserStructuredAddress } from "@curefit/user-common"
import IUserBusiness, {
    FindPreferredCityResponse,
    LocationInfoBody,
    UpdateBrowseLocationPayload,
    UpdateBrowseLocationResponse
} from "./IUserBusiness"
import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import {
    DELIVERY_MODELS_TYPES,
    IUserDeliveryAddressHistoryReadWriteDao,
    IUserDeliveryAddressReadWriteDao
} from "@curefit/delivery-models"
import {
    IUserPreferenceReadWriteDao,
    IUserStructuredAddressReadWriteDao,
    USER_MODELS_TYPES
} from "@curefit/user-models"
import { ErrorFactory, GenericError, } from "@curefit/error-client"
import { CULT_CLIENT_TYPES, ICultServiceOld as ICultService } from "@curefit/cult-client"
import { FoodPackBooking } from "@curefit/shipment-common"
import ActivePackViewBuilderV1, { ActivePackViewV1 } from "./ActivePackViewBuilderV1"
import * as _ from "lodash"
import { eternalPromise, TimeUtil } from "@curefit/util-common"
import { BASE_TYPES, Logger } from "@curefit/base"
import { DELIVERY_CLIENT_TYPES, IDeliveryAreaService, IGateService } from "@curefit/delivery-client"
import { ILocationService, LOCATION_SERVICE_TYPES } from "@curefit/location-service"
import { BASE_UTILS_TYPES, ISessionBusiness as ISessionService, S3Helper } from "@curefit/base-utils"
import LocationUtil from "../../app/util/LocationUtil"
import IDeviceBusiness from "../../app/device/IDeviceBusiness"
import { SortOrder } from "@curefit/mongo-utils"
import { IProgramService, PROGRAM_CLIENT_TYPES } from "@curefit/program-client"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService } from "@curefit/diy-client"
import { DIYPackFulfilment } from "@curefit/diy-common"
import { IKioskService, MASTERCHEF_CLIENT_TYPES } from "@curefit/masterchef-client"
import { DetectedCityResponseByIp, ICFAPICityService } from "../city/ICFAPICityService"
import EatUtil from "../util/EatUtil"
import { ActiveBundleOrderDetail, ALBUS_CLIENT_TYPES, BookingDetail, IHealthfaceService } from "@curefit/albus-client"
import { CacheHelper } from "../util/CacheHelper"
import CultUtil, { CultOrMindSummary, transformCultSummaryMap } from "../util/CultUtil"
import * as mime from "mime-types"
import * as path from "path"
import { SignedUrlResponse } from "@curefit/user-client"
import { ICityService, ICountryService, LOCATION_TYPES } from "@curefit/location-mongo"
import { CityResponseV2, TabTypes } from "./UserController"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import AppUtil from "../util/AppUtil"
import { EAT_API_CLIENT_TYPES, IEatApiService } from "@curefit/eat-api-client"
import { IUserYearEndReportReadOnlyDao } from "./userYearEndReport/UserYearEndReportDao"
import INavBarBusiness from "./INavBarBusiness"
import { ErrorCodes } from "../error/ErrorCodes"
import { Membership, Status } from "@curefit/membership-commons"
import LiveUtil from "../util/LiveUtil"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { GYMFIT_CLIENT_TYPES, IGymfitService } from "@curefit/gymfit-client"
import {
    AttributeAction,
    EventType,
    IRashiSnsClient,
    IUserAttributeClient,
    RASHI_CLIENT_TYPES,
    UserEvent
} from "@curefit/rashi-client"
import { IMediaGatewayService, MEDIA_GATEWAY_CLIENT_TYPES } from "@curefit/media-gateway-js-client"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import WellnessUtil from "../util/WellnessUtil"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { FOODWAY_CLIENT_TYPES, IFoodwayApiService } from "@curefit/foodway-client"
import { MembershipItem } from "../common/views/ProfileWidgetView"
import { CultSummary, CultUserPreference, MembershipDetails, MembershipStates } from "@curefit/cult-common"
import UserUtil, {
    LocationDataKey,
    LocationPreferenceRequestEntity,
    LocationPreferenceResponseEntity,
    UpdateStatusResponseEntity,
    UserPreferenceV2
} from "../util/UserUtil"
import AuthUtil from "../util/AuthUtil"
import { HamletConfigRequest } from "@curefit/hamlet-common"
import { HAMLET_CLIENT_TYPES, IHamletService } from "@curefit/hamlet-client"
import { PT_MEMBERSHIP_BENEFIT_NAME } from "../common/Constants"

const crypto = require("crypto")

@injectable()
class UserBusiness implements IUserBusiness {

    LOCATION_SELECTION_THRESHOLD_MIN: number = 10

    constructor(
        @inject(CUREFIT_API_TYPES.NavBarBusiness) private navBarBusiness: INavBarBusiness,
        @inject(GMF_CLIENT_TYPES.IGMFClient) private gmfService: IGMFClient,
        @inject(DELIVERY_MODELS_TYPES.UserDeliveryAddressReadwriteDao) private userAddressDao: IUserDeliveryAddressReadWriteDao,
        @inject(DELIVERY_MODELS_TYPES.UserDeliveryAddressHistoryReadwriteDao) private userAddressHistoryDao: IUserDeliveryAddressHistoryReadWriteDao,
        @inject(USER_MODELS_TYPES.UserStructuredAddressReadwriteDao) private userStructuredAddressDao: IUserStructuredAddressReadWriteDao,
        @inject(LOCATION_TYPES.CityService) private cityService: ICityService,
        @inject(CUREFIT_API_TYPES.CFAPICityService) private CFAPICityService: ICFAPICityService,
        @inject(MASTERCHEF_CLIENT_TYPES.KiosksDemandService) private kioskService: IKioskService,
        @inject(CUREFIT_API_TYPES.ActivePackViewBuilderV1) private activePackViewBuilderV1: ActivePackViewBuilderV1,
        @inject(CUREFIT_API_TYPES.DeviceBusiness) private deviceBusiness: IDeviceBusiness,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(PROGRAM_CLIENT_TYPES.ProgramService) private programService: IProgramService,
        @inject(DELIVERY_CLIENT_TYPES.GateService) private gateService: IGateService,
        @inject(CUREFIT_API_TYPES.SessionService) private sessionBusiness: ISessionService,
        @inject(LOCATION_SERVICE_TYPES.LocationService) private locationService: ILocationService,
        @inject(DELIVERY_CLIENT_TYPES.DeliveryAreaService) private deliveryAreaService: IDeliveryAreaService,
        @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
        @inject(CULT_CLIENT_TYPES.MindFitService) private mindFitService: ICultService,
        @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) protected DIYFulfilmentService: IDIYFulfilmentService,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected cacheHelper: CacheHelper,
        @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
        @inject(ALBUS_CLIENT_TYPES.CultPersonalTrainingService) private cultPTService: IHealthfaceService,
        @inject(BASE_UTILS_TYPES.S3Helper) private s3Helper: S3Helper,
        @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(EAT_API_CLIENT_TYPES.IEatApiService) private eatApiClientService: IEatApiService,
        @inject(CUREFIT_API_TYPES.UserYearEndReportReadOnlyDaoMongoImpl) private userYearEndReportReadOnlyDao: IUserYearEndReportReadOnlyDao,
        @inject(USER_MODELS_TYPES.UserPreferenceReadWriteDao) private userPreferenceDao: IUserPreferenceReadWriteDao,
        @inject(LOCATION_TYPES.CountryService) private countryService: ICountryService,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        @inject(GYMFIT_CLIENT_TYPES.GymfitService) private gymfitService: IGymfitService,
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) public membershipService: IMembershipService,
        @inject(RASHI_CLIENT_TYPES.RashiSnsClient) private rashiSnsClient: IRashiSnsClient,
        @inject(MEDIA_GATEWAY_CLIENT_TYPES.IMediaGatewayService) private mediaGatewayClient: IMediaGatewayService,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(FOODWAY_CLIENT_TYPES.IFoodwayApiService) private foodwayService: IFoodwayApiService,
        @inject(RASHI_CLIENT_TYPES.UserAttributeClient) public userAttributeClient: IUserAttributeClient,
        @inject(HAMLET_CLIENT_TYPES.HamletService) private hamletService: IHamletService

    ) {
    }

    async addAddress(userId: string, userAddress: UserDeliveryAddress, pincode?: string, appTenant?: AppTenant): Promise<UserDeliveryAddress> {
        if (userAddress.gateId) {
            const gateInfo = await this.gateService.getActiveGate(userAddress.gateId)
            this.addGateInfo(userAddress, gateInfo)
        }
        this.logger.info("UserBusiness new address entry: " + JSON.stringify(userAddress))
        const address = await this.userAddressDao.create(userAddress)
        return this.augmentStructuredAddress(userId, address.addressId, address, pincode, appTenant === AppTenant.CUREFIT ? "ADDRESS" : null, appTenant)
    }

    async addStructuredAddress(userId: string, address: UserStructuredAddress): Promise<UserStructuredAddress> {
        // address.userId = userId // in case the userId wasn't a part of the req.body send, we should still store the address, since the userId is available in the session.
        // before saving we assign the userId from the session to the address being stored. This ensures that address is stored even if the userId
        // wasn't passed as a part of req.body.
        return this.userStructuredAddressDao.create(_.assign(address, { userId: userId }))
    }

    async addKioskById(userId: string, kioskId: string): Promise<UserDeliveryAddress> {
        const address = await this.getAddressByKioskId(userId, kioskId)
        // Already kiosk is in user save address and no need to create it
        if (address) {
            if (address.state === "DELETED") {
                address.state = "CREATED"
                await this.userAddressDao.findOneAndUpdate({ addressId: address.addressId, userId: userId }, address)
            }
            return address
        } else {
            const userDeliveryAddressAndArea = await this.getUserAddressAndArea(kioskId, userId)
            return this.userAddressDao.create(userDeliveryAddressAndArea.address)
        }
    }

    async addKiosk(userId: string, kioskScanCode: string): Promise<UserDeliveryAddress> {
        const kiosk = this.kioskService.getKioskByScanCode(kioskScanCode)
        return this.addKioskById(userId, kiosk.kioskId)
    }

    private async getUserAddressAndArea(kioskId: string, userId: string, addressId?: string): Promise<{ address: UserDeliveryAddress, area: DeliveryArea }> {
        const kioskPromise = await this.kioskService.getKiosk(kioskId)
        const areaPromise = await this.deliveryAreaService.findAreaForKiosk(kioskId)
        const kiosk = await kioskPromise
        const area = await areaPromise
        const kioskAddress = kiosk.address
        const userDeliveryAddress: UserDeliveryAddress = {
            kioskId: kioskId,
            kioskType: kiosk.kioskType,
            latLong: UserBusiness.toMongoLatLng(kioskAddress.latLong),
            addressLine1: kiosk.name,
            addressLine2: kiosk.address.addressString,
            addressId: addressId ?? crypto.randomUUID(),
            userId: userId,
            eatDeliveryInstruction: {
                dropInstruction: "KIOSK",
                contactInstruction: "DONT_CALL",
                cutleryInstruction: "GIVE_CUTLERY"
            },
            addressType: "KIOSK"
        }
        return { address: userDeliveryAddress, area: area }
    }

    private addGateInfo(userAddress: UserDeliveryAddress, gate: DeliveryGate) {
        userAddress.latLong = gate.latLong
    }

    public static toLatLng(latLng: number[]): LatLong {
        return {
            long: latLng[0],
            lat: latLng[1]
        }
    }

    public static toMongoLatLng(latLng: LatLong): number[] {
        return [latLng.long, latLng.lat]
    }
    async updateAddress(userId: string, userAddress: UserDeliveryAddress): Promise<UserDeliveryAddress[]> {
        if (userAddress.gateId) {
            const gateInfo = await this.gateService.getActiveGate(userAddress.gateId)
            this.addGateInfo(userAddress, gateInfo)
        }
        const address = await this.userAddressDao.findOne({ addressId: userAddress.addressId })
        if (!address) {
            const genericErr: GenericError = new GenericError({ message: "Address does not exist" })
            genericErr.statusCode = 400
            return Promise.reject<UserDeliveryAddress[]>(genericErr)
        }
        if (!_.isEqual(address, userAddress)) {
            await this.logger.info("Address changed for addressId: " + address.addressId + ". Persisting in history.")
            try {
                delete (address as any)["_id"]
                await this.userAddressHistoryDao.create(address)
            } catch (e) {
                this.logger.error("ERR:updateAddress()1:userAddressHistoryDao.create()::" + JSON.stringify(e))
                this.rollbarService.sendError(e, {user: {id: userId}})
            }
        }

        address.gateId = userAddress.gateId
        address.addressLine1 = userAddress.addressLine1
        address.addressLine2 = userAddress.addressLine2
        address.googlePlacesId = userAddress.googlePlacesId
        address.latLong = userAddress.latLong
        address.addressType = userAddress.addressType
        address.otherAddressName = userAddress.otherAddressName
        address.eatDeliveryInstruction = userAddress.eatDeliveryInstruction
        address.name = userAddress.name
        address.vertical = userAddress.vertical
        address.phoneNumber = userAddress.phoneNumber
        address.pincode = userAddress.pincode
        address.structuredAddress = userAddress.structuredAddress
        address.locality = userAddress.locality
        if (!_.isEmpty(userAddress.gearDeliveryInfo)) {
            address.gearDeliveryInfo = userAddress.gearDeliveryInfo
        }
        try {
            const updatedEntry = await this.userAddressDao.findOneAndUpdate({addressId: address.addressId}, address, {
                runValidators: true,
                upsert: true
            })
        } catch (e) {
            this.logger.error("ERR::updateAddress(): " + JSON.stringify(e))
        }
        return this.getAddresses(userId, undefined)
    }

    async updateStructuredAddress(userId: string, addressId: string, newAddress: UserStructuredAddress): Promise<UserStructuredAddress> {
        const oldAddress = await this.getStructuredAddress(userId, addressId) // in case the address doesn't exist, this call would return a 404.
        // Only pick the updateable fields and assign to the oldAddress so that the userId, addressId, createdDate and version values are retained.
        return this.userStructuredAddressDao.findOneAndReplace({ addressId: addressId, userId: userId }, _.assign(oldAddress,
            _.pick(newAddress, ["name", "addressLine1", "addressLine2", "locality", "landmark", "city", "state", "country", "pincode", "phoneNumber"])))
    }

    async deleteAddress(session: Session, addressId: string, tenant: Tenant): Promise<UserDeliveryAddress[]> {
        const userId = session.userId
        const address = await this.userAddressDao.findOne({ addressId: addressId, userId: userId })
        address.state = "DELETED"
        const deleteAddressPromise = this.userAddressDao.findOneAndUpdate({ addressId: addressId, userId: userId }, address)
        let sessionUpdatePromise
        let deviceUpdatePromise
        if (session.sessionData.locationPreferenceData && session.sessionData.locationPreferenceData.addressId === addressId) {
            session.sessionData.locationPreferenceData = {}
            sessionUpdatePromise = this.sessionBusiness.updateSessionData(session.at, session.sessionData)
            deviceUpdatePromise = this.deviceBusiness.updateSessionInformation(session.deviceId, session.sessionData, tenant)
        }
        await Promise.all([deleteAddressPromise, sessionUpdatePromise, deviceUpdatePromise])
        return this.getAddresses(userId, undefined)
    }

    async deleteStructuredAddress(userId: string, addressId: string): Promise<boolean> {
        return this.userStructuredAddressDao.findOneAndDelete({ addressId: addressId, userId: userId })
    }

    getAddress(userId: string, addressId: string): Promise<UserDeliveryAddress> {
        return this.userAddressDao.findOne({ addressId: addressId, userId: userId })
    }

    async getStructuredAddress(userId: string, addressId: string): Promise<UserStructuredAddress> {
        try {
            return await this.userStructuredAddressDao.findOne({ addressId: addressId, userId: userId })
        } catch (err) {
            throw this.errorFactory.withCode(ErrorCodes.ADDRESS_DOES_NOT_EXIST_ERR, 400).withDebugMessage("Address does not exist").build()
        }
    }

    async augmentStructuredAddress(userId: string, addressId: string, address?: UserDeliveryAddress, pincode?: string, verticalType?: VerticalType, appTenant?: AppTenant): Promise<UserDeliveryAddress> {
        const userDeliveryAddress: UserDeliveryAddress = !_.isEmpty(address) ? address : await this.getAddress(userId, addressId)

        if (!userDeliveryAddress) {
            throw this.errorFactory.withCode(ErrorCodes.ADDRESS_DOES_NOT_EXIST_ERR, 400).withDebugMessage("Address not found").build()
        }

        if (userDeliveryAddress.structuredAddress) {
            return userDeliveryAddress
        }

        // lat long is stored reversed in mongo as [long, lat]
        const placeData = await this.locationService.getPlace(userDeliveryAddress.latLong[1], userDeliveryAddress.latLong[0], verticalType, appTenant)

        // can't take gear orders without pincode
        if (placeData.placeId === "NOT_FOUND" || (_.isEmpty(placeData.postalcode) && placeData.country !== "United Arab Emirates" && _.isEmpty(pincode))) {
            throw this.errorFactory.withCode(ErrorCodes.ADDRESS_DOES_NOT_EXIST_ERR, 400).withDebugMessage("error in getting address from location service").build()
        }

        return this.userAddressDao.findOneAndUpdatePartial({
            addressId: userDeliveryAddress?.addressId
        }, {
            structuredAddress: {
                locality: placeData.name,
                city: placeData.city,
                state: placeData.state,
                country: placeData.country,
                pincode: pincode || placeData.postalcode
            }
        })
    }

    getAddressByKioskId(userId: string, kioskId: string): Promise<UserDeliveryAddress> {
        return this.userAddressDao.findOne({ kioskId: kioskId, userId: userId })
    }

    async updateBrowseLocation(session: Session, updateBrowseLocationPayload: UpdateBrowseLocationPayload, tenant: Tenant): Promise<UpdateBrowseLocationResponse> {
        const locationPreferenceData: LocationPreferenceData = {}
        let shouldUpdateSession: boolean = updateBrowseLocationPayload.shouldUpdateSession
        let updateBrowseLocationResponse: UpdateBrowseLocationResponse
        if (updateBrowseLocationPayload.addressId) {
            locationPreferenceData.addressId = updateBrowseLocationPayload.addressId
            const address = await this.getAddress(session.userId, updateBrowseLocationPayload.addressId)
            if (!address) {
                throw this.errorFactory.withCode(ErrorCodes.ADDRESS_DOES_NOT_EXIST_ERR, 400).withDebugMessage("Address does not exist for the id passed").build()
            }
            const latLong: LatLong = UserBusiness.toLatLng(address.latLong)
            locationPreferenceData.latLong = latLong
            if (address.kioskId) {
                updateBrowseLocationResponse = {
                    isInServicableArea: true
                }
            } else {
                try {
                    if (updateBrowseLocationPayload?.listingBrand === "CULT_BIKE") {
                        updateBrowseLocationResponse = {
                            isInServicableArea: true // maybe put a city offerings check
                        }
                        shouldUpdateSession = shouldUpdateSession || (!!updateBrowseLocationResponse.isInServicableArea)
                    } else if (updateBrowseLocationPayload?.listingBrand === "GEAR_FIT") {
                        updateBrowseLocationResponse = {
                            isInServicableArea: true, // considering gear is serviceable PAN India
                            addressId: updateBrowseLocationPayload.addressId
                        }
                        session.sessionData.gearLocationPreference = {
                            addressId: address.addressId,
                            latLong: latLong
                        }
                        shouldUpdateSession = true
                    } else {
                        const area = await this.deliveryAreaService.findDeliveryArea(latLong)
                        updateBrowseLocationResponse = {
                            isInServicableArea: true
                        }
                    }
                } catch (err) {
                    if (updateBrowseLocationPayload?.listingBrand === "FOOD_MARKETPLACE") {
                        updateBrowseLocationResponse = {
                            isInServicableArea: await this.checkFoodMarkerplaceServiceability(latLong.lat , latLong.long)
                        }
                        shouldUpdateSession = shouldUpdateSession || (!!updateBrowseLocationResponse.isInServicableArea)
                    } else if (updateBrowseLocationPayload?.listingBrand === "WELLNESS" ||
                        updateBrowseLocationPayload?.listingBrand === "CULT_BIKE"
                    ) {
                        updateBrowseLocationResponse = {
                            isInServicableArea: true // maybe put a city offerings check
                        }
                        shouldUpdateSession = shouldUpdateSession || (!!updateBrowseLocationResponse.isInServicableArea)
                    } else {
                        updateBrowseLocationResponse = {
                            isInServicableArea: false
                        }
                        shouldUpdateSession = false
                    }
                }
            }
        } else if (updateBrowseLocationPayload.areaId) {
            // wpuld never happen for foodmp and wellnessclp
            const deliverySubArea = await this.deliveryAreaService.getDeliverySubArea(updateBrowseLocationPayload.areaId, updateBrowseLocationPayload.subArea)
            locationPreferenceData.areaId = updateBrowseLocationPayload.areaId
            locationPreferenceData.subArea = updateBrowseLocationPayload.subArea
            locationPreferenceData.latLong = deliverySubArea.representativeLatLong
            updateBrowseLocationResponse = {
                isInServicableArea: true
            }
        } else if (updateBrowseLocationPayload.lat && updateBrowseLocationPayload.lon) {
            const placeName = updateBrowseLocationPayload.placeName
            const placeData = await this.locationService.getPlace(updateBrowseLocationPayload.lat, updateBrowseLocationPayload.lon, tenant === Tenant.CULTSPORT_APP ? "GEAR" : undefined)
            if (updateBrowseLocationPayload?.listingBrand === "WELLNESS") {
                // WELLNESS is avalable PAN India
                updateBrowseLocationResponse = {
                    isInServicableArea: true,
                    placeData: placeData
                }
                shouldUpdateSession = shouldUpdateSession || (!!updateBrowseLocationResponse.isInServicableArea)
            } else if (updateBrowseLocationPayload?.listingBrand === "FOOD_MARKETPLACE") {
                updateBrowseLocationResponse = {
                    isInServicableArea: await this.checkFoodMarkerplaceServiceability(updateBrowseLocationPayload.lat, updateBrowseLocationPayload.lon),
                    placeData: placeData
                }
                shouldUpdateSession = shouldUpdateSession || (!!updateBrowseLocationResponse.isInServicableArea)
            } else {
                try {
                    const area = await this.deliveryAreaService.findDeliveryArea(UserBusiness.toLatLng([updateBrowseLocationPayload.lon, updateBrowseLocationPayload.lat]))
                    updateBrowseLocationResponse = {
                        isInServicableArea: true,
                        placeData: placeData
                    }
                } catch (err) {
                    const marketPlaceServiceableResponse = await this.eatApiClientService.isMarketPlaceServiceable(UserBusiness.toLatLng([updateBrowseLocationPayload.lon, updateBrowseLocationPayload.lat]), "ONLINE", new Date())
                    if (marketPlaceServiceableResponse && marketPlaceServiceableResponse.isServiceable) {
                        updateBrowseLocationResponse = {
                            isInServicableArea: true,
                            placeData: placeData
                        }
                    } else {
                        updateBrowseLocationResponse = {
                            isInServicableArea: false,
                            placeData: placeData
                        }
                    }
                }

            }

            locationPreferenceData.placeId = placeData.placeId
            locationPreferenceData.placeName = !_.isNil(placeName) ? placeName : placeData.name
            locationPreferenceData.placeAddress = placeData.address
            locationPreferenceData.latLong = {
                lat: updateBrowseLocationPayload.lat,
                long: updateBrowseLocationPayload.lon
            }
        } else {
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Atleast one of address or area or lat-lng needed").build()
        }
        locationPreferenceData.lastUserUpdatedTimestamp = new Date().getTime()
        session.sessionData.locationPreferenceData = locationPreferenceData
        let cityAndCountry, cityId
        if (updateBrowseLocationPayload?.listingBrand === "CULT_BIKE") {
            cityAndCountry = await this.getCityAndCountryForCultBike(tenant, locationPreferenceData.latLong.lat, locationPreferenceData.latLong.long)
            cityId = cityAndCountry.city ? cityAndCountry.city.cityId : undefined
        } else {
            cityAndCountry = await this.cityService.getCityAndCountry(tenant, locationPreferenceData.latLong.lat, locationPreferenceData.latLong.long)
            cityId = cityAndCountry.city ? cityAndCountry.city.cityId : undefined
        }
        session.sessionData.cityId = cityId
        updateBrowseLocationResponse.city = cityAndCountry.city
        updateBrowseLocationResponse.isWithinServicableCity = await this.checkListingBrandLevelServiceability(updateBrowseLocationPayload, cityAndCountry)
        if ((shouldUpdateSession === undefined || shouldUpdateSession === true) || (updateBrowseLocationResponse.isInServicableArea)) {
            const sessionUpdatePromise = this.sessionBusiness.updateSessionData(session.at, session.sessionData)
            const deviceUpdatePromise = this.deviceBusiness.updateSessionInformation(session.deviceId, session.sessionData, tenant)
            await Promise.all([sessionUpdatePromise, deviceUpdatePromise])
        }
        return updateBrowseLocationResponse
    }


    // Added logic to override city for cult bike flow
    private async getCityAndCountryForCultBike(tenant: Tenant, lat: number, long: number) {
        const cities = this.cityService.listCities(tenant)
        for (const city of cities) {
            if (this.cityService.checkIfCityIsOtherCity(city.cityId))
                continue
            const distance = LocationUtil.getDistanceFromLatLonInKm(lat, long, city.representativeLatLong.lat, city.representativeLatLong.long)
            if (distance <= 50) {
                return { city: city, country: this.countryService.getCountryById(city.countryId) }
            }
        }

        const otherCity = cities.find(city => city.cityId === "Other")
        return {
            city: otherCity,
            country: this.countryService.getCountryById(otherCity.countryId)
        }
    }

    private async checkFoodMarkerplaceServiceability(lat: number, lon: number): Promise<boolean> {
        const { outletsList } = await this.foodwayService.getOutletsForLocationAndCuisine({
            latitude: lat,
            longitude: lon
        })
        return !_.isEmpty(outletsList)
    }

    private  async checkListingBrandLevelServiceability(updateBrowseLocationPayload: UpdateBrowseLocationPayload, cityAndCountry: {
        city: City;
        country: Country;
    } ): Promise<boolean> {
        const { listingBrand } = updateBrowseLocationPayload
        switch (listingBrand) {
            case "WELLNESS":
                return (await this.cityService.getCityById(cityAndCountry?.city?.cityId))?.supportedVerticals?.includes("WELLNESS")
            case "FOOD_MARKETPLACE":
                return (await this.cityService.getCityById(cityAndCountry?.city?.cityId))?.availableOfferings?.includes("FOOD_MARKETPLACE")
            default:
                return (await this.cityService.getCityById(cityAndCountry?.city?.cityId))?.availableOfferings?.includes("FOOD")
        }
    }

    async getAreaAndAddressByAddressId(addressId: string, userId: string, mealSlot: string, ignoreServiceableTimings?: boolean, listingBrand: ListingBrandIdType | "CULT_BIKE" = "EAT_FIT" ,
    ): Promise<{ area: DeliverySubArea, defaultArea: DeliverySubArea, address: UserDeliveryAddress }> {
        const userAddress = await this.augmentStructuredAddress(userId, addressId)
        let area: DeliverySubArea
        let defaultArea: DeliverySubArea

        try {
            if (listingBrand !== "CULT_BIKE") {
                if (userAddress.kioskId) {
                    if (listingBrand === "WHOLE_FIT") {
                        defaultArea = await this.deliveryAreaService.getDefaultSubArea(undefined, undefined, listingBrand)
                    } else {
                        const kioskArea = await this.deliveryAreaService.findAreaForKiosk(userAddress.kioskId)
                        area = {
                            representativeLatLong: UserBusiness.toLatLng(userAddress.latLong),
                            subArea: kioskArea.name,
                            areaId: kioskArea.areaId
                        }
                    }
                } else {
                    area = await this.deliveryAreaService.findDeliverySubArea(UserBusiness.toLatLng(userAddress.latLong), undefined, mealSlot, ignoreServiceableTimings, listingBrand)
                }
            }
        } catch (err) {
            defaultArea = await this.deliveryAreaService.getDefaultSubArea(undefined, undefined, listingBrand as any)
        }
        return { address: userAddress, area: area, defaultArea: defaultArea }

    }

    private async isInServiceableCity(userContext: UserContext, lat: number, long: number): Promise<boolean> {
        const tenant: Tenant = AppUtil.getTenantFromUserContext(userContext)
        const cityAndCountry = await this.cityService.getCityAndCountry(tenant, lat, long)
        if (cityAndCountry.city) {
            return EatUtil.isEatFitAvailable(cityAndCountry.city.cityId)
        }
        return false
    }

    async findClosestAddress(userContext: UserContext, userAgentType: UserAgent, listingBrand: string, lat: number, long: number, ignoreKiosk: boolean = false): Promise<UserDeliveryAddress> {
        let userAddresses: UserDeliveryAddress[] = await this.getAddresses(userContext.userProfile.userId, undefined)
        const KIOSK_DISTANCE_THRESHOLD_IN_KM = 0.1
        if (ignoreKiosk || userAgentType !== "APP" || (AppUtil.isWholeFitV2ReviewSupported(userContext) && listingBrand === "WHOLE_FIT")) {
            userAddresses = userAddresses.filter((address: UserDeliveryAddress) => {
                return address.addressType !== "KIOSK"
            })
        }
        const closestAddress: UserDeliveryAddress = userAddresses.reduce((closestAddress: UserDeliveryAddress, userAddress: UserDeliveryAddress) => {
            if (closestAddress) {
                const distance1 = LocationUtil.getDistanceFromLatLonInKm(lat, long,
                    userAddress.latLong[1], userAddress.latLong[0])
                const distance2 = LocationUtil.getDistanceFromLatLonInKm(lat, long,
                    closestAddress.latLong[1], closestAddress.latLong[0])
                // If kiosk, is there is anything within the threshold and also listingBrand shouldn't be whole_fit
                if (userAddress.kioskId && !closestAddress.kioskId && distance1 < KIOSK_DISTANCE_THRESHOLD_IN_KM) {
                    return userAddress
                }
                if (closestAddress.kioskId && !userAddress.kioskId && distance2 < KIOSK_DISTANCE_THRESHOLD_IN_KM) {
                    return closestAddress
                }
                return distance1 < distance2 ? userAddress : closestAddress
            }
            return userAddress
        }, undefined)
        return closestAddress
    }

    async getPreferredLocation(userContext: UserContext, userId: string, sessionData: SessionData, long?: number, lat?: number, mealSlot?: string, ignoreServiceableTimings?: boolean, listingBrand: ListingBrandIdType = "EAT_FIT", userAgentType: UserAgent = "APP", ignoreKiosk: boolean = false): Promise<PreferredLocation> {
        // this.logger.info(`getPreferredLocation: mealSlot: ${mealSlot} ignoreServiceableTimings: ${ignoreServiceableTimings}`)
        const locationPreferenceData: LocationPreferenceData = sessionData.locationPreferenceData
        let isWithinServicableCity = EatUtil.isEatFitAvailable(sessionData.cityId)
        const tz = userContext.userProfile.timezone
        const cityId = sessionData.cityId
        let address: UserDeliveryAddress
        let area: DeliverySubArea
        let defaultArea: DeliverySubArea
        let isInServicableArea: boolean
        let isUserPreferenceSet: boolean
        let latLong: LatLong
        const tenant: Tenant = AppUtil.getTenantFromUserContext(userContext)

        /**
         * To check the whether the currently browsing city is same as the
         * city corresponding to eat area id.
         * If city matches proceed with the area id
         * or else go with browing city's default area id.
         */
        if (locationPreferenceData && locationPreferenceData.latLong) {
            latLong = locationPreferenceData.latLong
            if (sessionData.cityId) {
                const cityAndCountryBasedOnAreaPromise = this.cityService.getCityAndCountry(tenant, latLong.lat, latLong.long)
                const selectedCityPromise = this.cityService.getCityById(sessionData.cityId)
                const cityAndCountryBasedOnArea = await cityAndCountryBasedOnAreaPromise
                const selectedCity = await selectedCityPromise
                if (cityAndCountryBasedOnArea.city && cityAndCountryBasedOnArea.city.cityId !== selectedCity.cityId) {
                    isWithinServicableCity = EatUtil.isEatFitAvailable(selectedCity.cityId)
                    const defaultAreaPromise = this.getDefaultAreaPromise(listingBrand, selectedCity.cityId)
                    return {
                        city: selectedCity,
                        defaultArea: await defaultAreaPromise,
                        isWithinServicableCity: isWithinServicableCity,
                        isInServicableArea: isWithinServicableCity,
                        deliveryChannel: "ONLINE"
                    }
                }
            }
        } else if (lat && long) {
            latLong = {
                lat: lat,
                long: long
            }
        }

        /**
         * To honour the user preferred location for LOCATION_SELECTION_THRESHOLD_MIN.
         * If past LOCATION_SELECTION_THRESHOLD_MIN, this will try to find the saved address
         * closeset to the current lat & long  received from the app and try to give the
         * experience for the closest address.
         */


        if (lat && long && (locationPreferenceData === undefined || locationPreferenceData.lastUserUpdatedTimestamp === undefined ||
            (new Date().getTime() - locationPreferenceData.lastUserUpdatedTimestamp) / 60000 > this.LOCATION_SELECTION_THRESHOLD_MIN)) {
            address = await this.findClosestAddress(userContext, userAgentType, listingBrand, lat, long, ignoreKiosk)
            if (address) {
                isUserPreferenceSet = true
                // kiosk not applicable for whole_fit
                if (address.kioskId && userAgentType === "APP") {
                    if (listingBrand === "WHOLE_FIT" || ignoreKiosk) {
                        if (AppUtil.isWholeFitV2ReviewSupported(userContext)) {
                            // address = await this.findClosestAddress(userContext, userAgentType, listingBrand, lat, long)
                            const response = await this.findServicableArea(address, mealSlot, ignoreServiceableTimings, listingBrand, cityId, userContext, sessionData)
                            area = response.area
                            defaultArea = response.defaultArea
                            isInServicableArea = response.isInServicableArea
                            isWithinServicableCity = response.isWithinServicableCity
                        } else {
                            defaultArea = await this.getDefaultAreaPromise(listingBrand, sessionData.cityId)
                            isInServicableArea = false
                        }
                    } else {
                        const areaForKiosk = await this.deliveryAreaService.findAreaForKiosk(address.kioskId)
                        area = {
                            areaId: areaForKiosk.areaId,
                            subArea: areaForKiosk.name,
                            representativeLatLong: UserBusiness.toLatLng(address.latLong)
                        }
                        isInServicableArea = true
                        isWithinServicableCity = true
                    }
                } else {
                    isWithinServicableCity = await this.isInServiceableCity(userContext, address.latLong[1], address.latLong[0])
                    try {
                        area = await this.deliveryAreaService.findDeliverySubArea(UserBusiness.toLatLng(address.latLong), undefined, mealSlot, ignoreServiceableTimings, listingBrand === "FOOD_MARKETPLACE" ? undefined : listingBrand, cityId)
                        isInServicableArea = true
                    } catch (err) {
                        this.logger.debug("Error getting deliverySubArea" + userId + " : " + err)
                        defaultArea = await this.getDefaultAreaPromise(listingBrand, sessionData.cityId)
                        isInServicableArea = false
                    }
                }

                return {
                    isInServicableArea: isInServicableArea,
                    isWithinServicableCity: isWithinServicableCity,
                    address: address,
                    area: area,
                    defaultArea: defaultArea,
                    latLong: {
                        lat: address.latLong[1],
                        long: address.latLong[0]
                    },
                    deliveryChannel: EatUtil.getDeliveryChannel(address)
                }
            }
        }

        /**
         * Honor the preference selected by the user on the app which can be
         * either addressId or delivery sub area or google place and compute the
         * servicability accordingly based on lat-long associated with the preference.
         */
        let placeData: PlaceData
        if (locationPreferenceData && locationPreferenceData.addressId) {
            const userAddress = await this.userAddressDao.findOne({ addressId: locationPreferenceData.addressId })
            // This condition can happen when the user has deleted the address from a differen session on different // device
            if (userAddress) {
                isUserPreferenceSet = true
                address = userAddress
                // kiosk not for whole fit
                if (userAddress.kioskId && userAgentType === "APP") {
                    if (listingBrand === "WHOLE_FIT") {
                        if (AppUtil.isWholeFitV2ReviewSupported(userContext)) {
                            address = await this.findClosestAddress(userContext, userAgentType, listingBrand, lat, long, ignoreKiosk)
                            const response = await this.findServicableArea(address, mealSlot, ignoreServiceableTimings, listingBrand, cityId, userContext, sessionData)
                            area = response.area
                            defaultArea = response.defaultArea
                            isInServicableArea = response.isInServicableArea
                            isWithinServicableCity = response.isWithinServicableCity
                        } else {
                            defaultArea = await this.deliveryAreaService.getDefaultSubArea("FOOD", sessionData.cityId, listingBrand)
                            isInServicableArea = false
                        }
                    } else {
                        // Constructing the user address from kiosk id again as kiosk property might have been changed
                        const userDeliveryAddressAndArea = await this.getUserAddressAndArea(userAddress.kioskId, userId, userAddress.addressId)
                        address = userDeliveryAddressAndArea.address
                        const deliveryArea = userDeliveryAddressAndArea.area
                        area = {
                            areaId: deliveryArea.areaId,
                            subArea: deliveryArea.name,
                            representativeLatLong: UserBusiness.toLatLng(userAddress.latLong)
                        }
                        isInServicableArea = true
                        isWithinServicableCity = true
                    }
                } else {
                    const response = await this.findServicableArea(address, mealSlot, ignoreServiceableTimings, listingBrand, cityId, userContext, sessionData)
                    area = response.area
                    defaultArea = response.defaultArea
                    isInServicableArea = response.isInServicableArea
                    isWithinServicableCity = response.isWithinServicableCity
                }
            }
        } else if (locationPreferenceData && locationPreferenceData.areaId) {
            isUserPreferenceSet = true
            isWithinServicableCity = true
            try {
                const delArea = await this.deliveryAreaService.findDeliverySubArea(UserBusiness.toLatLng([locationPreferenceData.latLong.long,
                locationPreferenceData.latLong.lat]), undefined, mealSlot, ignoreServiceableTimings, listingBrand === "FOOD_MARKETPLACE" ? undefined : listingBrand, cityId)
                area = {
                    areaId: delArea.areaId,
                    subArea: delArea.subArea,
                    representativeLatLong: locationPreferenceData.latLong
                }
                isInServicableArea = true
            } catch (err) {
                this.logger.debug("Error getting deliverySubArea" + userId + " : " + err)
                defaultArea = await this.getDefaultAreaPromise(listingBrand, sessionData.cityId)
                isInServicableArea = false
            }

        } else if (locationPreferenceData && locationPreferenceData.placeId) {
            isUserPreferenceSet = true
            placeData = {
                placeId: locationPreferenceData.placeId,
                name: locationPreferenceData.placeName,
                address: locationPreferenceData.placeAddress,
            }
            isWithinServicableCity = await this.isInServiceableCity(userContext, locationPreferenceData.latLong.lat, locationPreferenceData.latLong.long)
            try {
                area = await this.deliveryAreaService.findDeliverySubArea(UserBusiness.toLatLng([locationPreferenceData.latLong.long,
                locationPreferenceData.latLong.lat]), undefined, mealSlot, ignoreServiceableTimings, listingBrand === "FOOD_MARKETPLACE" ? undefined : listingBrand, cityId)
                isInServicableArea = true
            } catch (err) {
                this.logger.debug("Error getting deliverySubArea" + userId + " : " + err)
                defaultArea = await this.getDefaultAreaPromise(listingBrand, sessionData.cityId)
                isInServicableArea = false
            }
        }

        /**
         * If user preference is not set expplicitly, then will try to find the closest sub area
         * if lat-long is passed or city default area if lat-long is not passed.
         */
        if (!isUserPreferenceSet) {
            if (long && lat) {
                // placeData = await this.locationService.getPlace(lat, long)
                isWithinServicableCity = await this.isInServiceableCity(userContext, lat, long)
                try {
                    area = await this.deliveryAreaService.findDeliverySubArea(UserBusiness.toLatLng([long, lat]), undefined, mealSlot, ignoreServiceableTimings, listingBrand === "FOOD_MARKETPLACE" ? undefined : listingBrand, cityId)
                    isInServicableArea = true
                } catch (err) {
                    defaultArea = await this.getDefaultAreaPromise(listingBrand, sessionData.cityId)
                    this.logger.debug("Error getting deliverySubArea" + userId + " : " + err)
                    isInServicableArea = false
                }

            } else {
                defaultArea = await this.getDefaultAreaPromise(listingBrand, sessionData.cityId)
                isInServicableArea = true
                isWithinServicableCity = true
            }
        }
        return {
            isInServicableArea: isInServicableArea,
            isWithinServicableCity: isWithinServicableCity,
            address: address,
            area: area,
            defaultArea: defaultArea,
            placeData: placeData,
            latLong: latLong,
            deliveryChannel: EatUtil.getDeliveryChannel(address)
        }
    }

    async findServicableArea(userAddress: UserDeliveryAddress, mealSlot: any, ignoreServiceableTimings: boolean, listingBrand: ListingBrandIdType, cityId: string, userContext: UserContext, sessionData: SessionData): Promise<{
        isWithinServicableCity: boolean,
        area: DeliverySubArea,
        isInServicableArea: boolean,
        defaultArea: DeliverySubArea
    }> {
        let isWithinServicableCity
        let area
        let isInServicableArea
        let defaultArea
        try {
            isWithinServicableCity = await this.isInServiceableCity(userContext, userAddress.latLong[1], userAddress.latLong[0])
            area = await this.deliveryAreaService.findDeliverySubArea(UserBusiness.toLatLng(userAddress.latLong), undefined, mealSlot, ignoreServiceableTimings, listingBrand === "FOOD_MARKETPLACE" ? undefined : listingBrand, cityId)
            isInServicableArea = true
        } catch (err) {
            this.logger.debug("Error getting deliverySubArea" + userContext.userProfile.userId + " : " + err)
            defaultArea = await this.getDefaultAreaPromise(listingBrand, cityId)
            isInServicableArea = false
        }
        return {
            isWithinServicableCity,
            isInServicableArea,
            area,
            defaultArea
        }
    }

    async getAddresses(userId: string, verticalQuery: string = "ALL", listingBrand?: string): Promise<UserDeliveryAddress[]> {
        const condition: any = { userId }

        // If the verticalquery as ALL is provided in the request, honour it and fetch all address. Else, return all results that don't belong to CultGear.
        // This is because CultGear (being e-commerce) will add addresses that may not have geo-coded lat,long values.
        // Such addresses will not work for the CF App. Hence, removing any CultGear addresses by default for backwards compatibility
        if (verticalQuery !== "ALL") {
            condition["vertical"] = { $ne: "CULT_GEAR" }
        }
        const addressess = await this.userAddressDao.find({ condition, sortField: "updatedDate", sortOrder: SortOrder.DESC })
        let activeAddresses = _.filter(addressess, address => address.state !== "DELETED")
        if (listingBrand === "GEAR_FIT" && !_.isEmpty(activeAddresses)) {
            // appending structured address to all the addresses but KIOSK as thry are used in gear
            activeAddresses = await Promise.all(_.map(_.filter(activeAddresses, a => a.addressType !== "KIOSK"), address => this.augmentStructuredAddress(userId, address.addressId, address, undefined, "GEAR")))
        }
        const transformedAddressPromises = _.map(activeAddresses, async address => {
            if (address.addressType === "KIOSK") {
                const userAddressAndArea = await this.getUserAddressAndArea(address.kioskId, userId, address.addressId)
                return userAddressAndArea.address
            }
            return address
        })
        return await Promise.all(transformedAddressPromises)
    }

    getActiveAddressCount(userId: string): Promise<number> {
        return this.userAddressDao.count({ userId: userId, vertical: { $ne: "CULT_GEAR" }, state: { $ne: "DELETED" }, addressType: { $ne: "KIOSK"} })
    }

    async getStructuredAddresses(userId: string): Promise<UserStructuredAddress[]> {
        return this.userStructuredAddressDao.find({ condition: { userId: userId }, sortField: "updatedDate", sortOrder: SortOrder.DESC })
    }

    async getCultAndLiveAssociatedActivePacks(userContext: UserContext): Promise<ActivePackViewV1[]> {
        const activePackViewList: ActivePackViewV1[][] = await Promise.all([
            this.getCultSummaryActivePacks(userContext),
            this.getGymFitMembershipActivePacks(userContext),
            this.getCultPTActivePacks(userContext),
            this.getLiveSGTActivePacks(userContext)
        ])
        const flattenedList: ActivePackViewV1[] = []

        activePackViewList.forEach((currentList) => {
            flattenedList.push(...currentList)
        })

        const sortedViews = flattenedList.sort((a, b) => {
            if (!this.isDiy(a.productType) && this.isDiy(b.productType)) {
                return -1
            } else if (this.isDiy(a.productType) && !this.isDiy(b.productType)) {
                return 1
            } else {
                if (a.startDate > b.startDate) {
                    return -1
                } else if (a.startDate < b.startDate) {
                    return 1
                } else {
                    return 0
                }
            }
        })

        return sortedViews
    }

    async getDIYActivePacks(userContext: UserContext): Promise<ActivePackViewV1[]> {
        const diyFulfilmentsPromise = eternalPromise(this.DIYFulfilmentService.getDIYPackFulfilmentsForUserForProductType(userContext.userProfile.userId), "diy service get active packs")
        const diyFulfilmentsResult = await diyFulfilmentsPromise
        const diyServicefulfilments: DIYPackFulfilment[] = (diyFulfilmentsResult.obj) ? diyFulfilmentsResult.obj : []
        const activePacksPromise: Promise<{ obj: ActivePackViewV1, err?: any }>[] = []
        if (!_.isEmpty(diyServicefulfilments)) {
            diyServicefulfilments.forEach(diyServicefulfilment => {
                activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildDIYPackView(userContext, diyServicefulfilment), "Build atlas pack view"))
            })
        }
        const activePacksResult = await Promise.all(activePacksPromise)
        const activePackView: ActivePackViewV1[] = []
        activePacksResult.forEach(activePack => {
            if (activePack.obj) {
                activePackView.push(activePack.obj)
            } else {
                this.logger.error(activePack.err)
            }
        })
        return activePackView
    }

    async getCultSummaryActivePacks(userContext: UserContext): Promise<ActivePackViewV1[]> {
        const userId = userContext.userProfile.userId
        const cultSummaryMap = await eternalPromise(this.cacheHelper.getCultSummaryForAllSubUsers(userId))
        const cultMembershipDataMap: { [userId: string]: CultOrMindSummary } = (cultSummaryMap.obj) ? transformCultSummaryMap(cultSummaryMap.obj, "CULT_FIT") : undefined
        const activePacksPromise: Promise<{ obj: ActivePackViewV1, err?: any }>[] = []

        if (!_.isEmpty(cultMembershipDataMap)) {
            _.forEach(Object.keys(cultMembershipDataMap), userId => {
                const cultMembershipData = cultMembershipDataMap[userId]
                if (!_.isNil(cultMembershipData.currentMembership)) {
                    activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildCultPackView(userContext, cultMembershipData.currentMembership, userId), "Build cult pack view"))
                }
                if (!_.isNil(cultMembershipData.upcomingMembership)) {
                    activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildCultPackView(userContext, cultMembershipData.upcomingMembership, userId), "Build cult pack view"))
                }
            })
        }

        const activePacksResult = await Promise.all(activePacksPromise)
        const activePackView: ActivePackViewV1[] = []
        activePacksResult.forEach(activePack => {
            activePack.obj ? activePackView.push(activePack.obj) : this.logger.error(activePack.err)
        })

        return activePackView
    }

    async getLiveFitMembershipActivePacks(userContext: UserContext): Promise<ActivePackViewV1[]> {
        const userId = userContext.userProfile.userId
        const livefitData = await eternalPromise(this.DIYFulfilmentService.getAllMembershipDetails(userId, AppUtil.getTenantFromUserContext(userContext)))
        const cfLiveMemberships: Membership[] = !_.isEmpty(livefitData) && !_.isEmpty(livefitData.obj) ? livefitData.obj : []
        const activePacksPromise: Promise<{ obj: ActivePackViewV1, err?: any }>[] = []

        if (!_.isEmpty(cfLiveMemberships)) {
            for (const cfLiveMembership of cfLiveMemberships) {
                const product = await this.catalogueService.getProduct(cfLiveMembership.productId)
                if (LiveUtil.getLiveFitMembershipState(userContext, cfLiveMembership, product) !== "EXPIRED") {
                    if (_.isNil(product) || product.vendorId !== "TRANSFORM") {
                        activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildCFLivePackView(userContext, cfLiveMembership)))
                    }
                }
            }
        }

        const activePacksResult = await Promise.all(activePacksPromise)
        const activePackView: ActivePackViewV1[] = []

        activePacksResult.forEach(activePack => {
            activePack.obj ? activePackView.push(activePack.obj) : this.logger.error(activePack.err)
        })

        return activePackView
    }

    async getGymFitMembershipActivePacks(userContext: UserContext): Promise<ActivePackViewV1[]> {
        const userId = userContext.userProfile.userId
        const gymfitData = await eternalPromise(this.membershipService.getMembershipsForUser(userId, AppUtil.getTenantFromUserContext(userContext), ["GYMFIT_GA", "GYMFIT_GX"]))
        const gymfitPacks: Membership[] = !_.isEmpty(gymfitData) && !_.isEmpty(gymfitData.obj) ? gymfitData.obj : []
        const activePacksPromise: Promise<{ obj: ActivePackViewV1, err?: any }>[] = []

        if (!_.isEmpty(gymfitPacks)) {
            gymfitPacks.forEach(gymfitPack => {
                activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildGymFitPackView(userContext, gymfitPack), "Build GymFit pack view"))
            })
        }

        const activePacksResult = await Promise.all(activePacksPromise)
        const activePackView: ActivePackViewV1[] = []

        activePacksResult.forEach(activePack => {
            activePack.obj ? activePackView.push(activePack.obj) : this.logger.error(activePack.err)
        })

        return activePackView
    }

    async getCultPTActivePacks(userContext: UserContext): Promise<ActivePackViewV1[]> {
        const userId = userContext.userProfile.userId
        const cultPtLivePackData = await eternalPromise(this.cultPTService.getActiveBundleOrders(userId, "BUNDLE", "LIVE_PERSONAL_TRAINING"))
        const cultLivePacks: ActiveBundleOrderDetail[] = !_.isEmpty(cultPtLivePackData) && !_.isEmpty(cultPtLivePackData.obj) ? cultPtLivePackData.obj : []
        const activePacksPromise: Promise<{ obj: ActivePackViewV1, err?: any }>[] = []

        if (!_.isEmpty(cultLivePacks)) {
            cultLivePacks.forEach(cultLivePTPack => {
                activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildBundleSessionPackView(userContext, cultLivePTPack), "Build Bundle session pack view"))
            })
        }

        const activePacksResult = await Promise.all(activePacksPromise)
        const activePackView: ActivePackViewV1[] = []

        activePacksResult.forEach(activePack => {
            activePack.obj ? activePackView.push(activePack.obj) : this.logger.error(activePack.err)
        })

        return activePackView
    }

    async getLiveSGTActivePacks(userContext: UserContext): Promise<ActivePackViewV1[]> {
        const userId = userContext.userProfile.userId
        const liveSGTPackData = await eternalPromise(this.cultPTService.getActiveBundleOrders(userId, "BUNDLE", "LIVE_SGT"))
        const liveSGTPacks: ActiveBundleOrderDetail[] = !_.isEmpty(liveSGTPackData) && !_.isEmpty(liveSGTPackData.obj) ? liveSGTPackData.obj : []
        const activePacksPromise: Promise<{ obj: ActivePackViewV1, err?: any }>[] = []

        if (!_.isEmpty(liveSGTPacks)) {
            liveSGTPacks.forEach(liveSGTPack => {
                activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildBundleSessionPackView(userContext, liveSGTPack), "Build Bundle session pack view"))
            })
        }

        const activePacksResult = await Promise.all(activePacksPromise)
        const activePackView: ActivePackViewV1[] = []

        activePacksResult.forEach(activePack => {
            activePack.obj ? activePackView.push(activePack.obj) : this.logger.error(activePack.err)
        })

        return activePackView
    }

    async getActivePacksV1(userContext: UserContext, limit?: number): Promise<ActivePackViewV1[]> {
        const promises: Promise<any>[] = []
        const userId = userContext.userProfile.userId
        const userAgent = userContext.sessionInfo.userAgent
        const diyServicePromise = eternalPromise(this.DIYFulfilmentService.getDIYPackFulfilmentsForUserForProductType(userId), "diy service get active packs")
        const shipmentServicePromise = eternalPromise(Promise.resolve([]), "meal get active packs")
        const cultPromise = eternalPromise(CultUtil.getEliteMembershipsFromMembershipService(userContext, this.membershipService))
        const livefitPromise = eternalPromise(this.DIYFulfilmentService.getAllMembershipDetails(userId, AppUtil.getTenantFromUserContext(userContext)))
        const gymfitPromise = eternalPromise(this.membershipService.getCachedMembershipsForUser(userId, AppUtil.getTenantFromUserContext(userContext), ["GYMFIT_GA", "GYMFIT_GX"]))
        const playPackPromise = eternalPromise(this.membershipService.getCachedMembershipsForUser(userId, AppUtil.getTenantFromUserContext(userContext), ["PLAY"]))

        // get self patient and get all care packs for it
        let managedPlanPromise
        let therapyPackPromise
        let cultPTPacksPromise
        const cultPtLivePackPromise = eternalPromise(this.cultPTService.getActiveBundleOrders(userId, "BUNDLE", "LIVE_PERSONAL_TRAINING"))
        const liveSGTPackPromise = eternalPromise(this.cultPTService.getActiveBundleOrders(userId, "BUNDLE", "LIVE_SGT"))
        let hcuPacksPromise
        let aiMgPacksPromise
        let consultationPacksPromise
        const mealPlannerPackPromise = eternalPromise(this.gmfService.checkExistsUserPlan(userId))
        let physiotherapyPromise, skinPacksPromise, beautyPacksPromise, hairPacksPromise, lcPacksPromise, transformPacksPromise
        const patientsListPromise = eternalPromise(this.healthfaceService.getAllPatients(userId))
        const patientsListResult = await patientsListPromise
        if (patientsListResult && patientsListResult.obj) {
            const patientsList = patientsListResult.obj
            const selfPatient = !_.isEmpty(patientsList) ? _.find(patientsList, patient => patient.relationship === "Self") : undefined
            if (selfPatient && userAgent === "APP") {
                managedPlanPromise = eternalPromise(this.healthfaceService.getActivePacksByBookingType(userId, selfPatient.id, "CARE", "BUNDLE", "MP"))
                therapyPackPromise = eternalPromise(this.healthfaceService.getActivePacksByBookingType(userId, selfPatient.id, "MIND", "BUNDLE", "MIND_THERAPY"))
                cultPTPacksPromise = eternalPromise(this.cultPTService.getActiveBundleOrders(userId, "BUNDLE", "PERSONAL_TRAINING"))
                hcuPacksPromise = eternalPromise(this.healthfaceService.getActiveBundleOrders(userId, "BUNDLE", "HCU_PACK"))
                skinPacksPromise = eternalPromise(this.healthfaceService.getActiveBundleOrders(userId, "BUNDLE", "SKIN_PACK"))
                beautyPacksPromise = eternalPromise(this.healthfaceService.getActiveBundleOrders(userId, "BUNDLE", "BEAUTY_PACK"))
                hairPacksPromise = eternalPromise(this.healthfaceService.getActiveBundleOrders(userId, "BUNDLE", "HAIR_PACK"))
                physiotherapyPromise = eternalPromise(this.healthfaceService.getActiveBundleOrders(userId, "BUNDLE", "PHYSIOTHERAPY"))
                lcPacksPromise = eternalPromise(this.healthfaceService.getActiveBundleOrders(userId, "BUNDLE", "NUTRITIONIST"))
                aiMgPacksPromise = eternalPromise(this.healthfaceService.getActivePacksByBookingType(userId, selfPatient.id, "CARE", "BUNDLE", "AI_MG"))
                consultationPacksPromise = eternalPromise(this.healthfaceService.getActiveBundleOrders(userId, "BUNDLE", "CONSULTATION_PACK"))
            }
        } else {
            this.logger.error("Error fetching patent list for user  " + userId + " : " + patientsListResult.err)
        }

        transformPacksPromise = eternalPromise(this.healthfaceService.getActiveBundleOrders(userId, "BUNDLE", null, false, "TRANSFORM"))

        promises.push(diyServicePromise)
        promises.push(cultPromise)
        promises.push(shipmentServicePromise)
        promises.push(managedPlanPromise)
        promises.push(therapyPackPromise)
        promises.push(cultPTPacksPromise)
        promises.push(hcuPacksPromise)
        promises.push(aiMgPacksPromise)
        promises.push(physiotherapyPromise)
        promises.push(skinPacksPromise)
        promises.push(hairPacksPromise)
        promises.push(beautyPacksPromise)
        promises.push(lcPacksPromise)
        promises.push(cultPtLivePackPromise)
        promises.push(mealPlannerPackPromise)
        promises.push(livefitPromise)
        promises.push(consultationPacksPromise)
        promises.push(liveSGTPackPromise)
        promises.push(gymfitPromise)
        promises.push(transformPacksPromise)
        promises.push(playPackPromise)

        const result = await Promise.all(promises)
        let diyServicefulfilments: DIYPackFulfilment[]
        diyServicefulfilments = (result[0].obj) ? result[0].obj : []

        const activePacksPromise: Promise<{ obj: ActivePackViewV1, err?: any }>[] = []
        const cultPacks: Membership[] = (result[1] && result[1].obj) ? result[1].obj : []
        const foodPackBookings: FoodPackBooking[] = (result[2].obj) ? result[2].obj : []
        const managedPlanPacks: BookingDetail[] = (result[3] && result[3].obj) ? result[3].obj : []
        const therapyPacks: BookingDetail[] = (result[4] && result[4].obj) ? result[4].obj : []
        const cultPTPacks: ActiveBundleOrderDetail[] = (result[5] && result[5].obj) ? result[5].obj : []
        const hcuPacks: ActiveBundleOrderDetail[] = (result[6] && result[6].obj) ? result[6].obj : []
        const aimgPacks: BookingDetail[] = (result[7] && result[7].obj) ? result[7].obj : []
        const physioPacks: ActiveBundleOrderDetail[] = (result[8] && result[8].obj) ? result[8].obj : []
        const skinPacks: ActiveBundleOrderDetail[] = (result[9] && result[9].obj) ? result[9].obj : []
        const hairPacks: ActiveBundleOrderDetail[] = (result[10] && result[10].obj) ? result[10].obj : []
        const beautyPacks: ActiveBundleOrderDetail[] = (result[11] && result[11].obj) ? result[11].obj : []
        const lcPacks: ActiveBundleOrderDetail[] = (result[12] && result[12].obj) ? result[12].obj : []
        const cultLivePacks: ActiveBundleOrderDetail[] = (result[13] && result[13].obj) ? result[13].obj : []
        const mealPlannerPack: boolean =
            result[14] && result[14].obj ? result[14].obj : false
        const cfLiveMemberships: Membership[] = !_.isEmpty(result[15]) && !_.isEmpty(result[15].obj) ? result[15].obj : []
        const consultationPacks: ActiveBundleOrderDetail[] = !_.isEmpty(result[16]) && !_.isEmpty(result[16].obj) ? result[16].obj : []
        const liveSGTPacks: ActiveBundleOrderDetail[] = (result[17] && result[17].obj) ? result[17].obj : []
        const gymfitPacks: Membership[] = (result[18] && result[18].obj) ? result[18].obj : []
        const transformPacks: ActiveBundleOrderDetail[] = (result[19] && result[19].obj) ? result[19].obj : []
        const playPacks: Membership[] = (result[20] && result[20].obj) ? result[20].obj : []

        for (let i = 0; i < 3; i++) {
            if (result[i].err) {
                this.logger.error("Error for user " + userId + " : " + result[i].err)
            }
        }

        if (!_.isEmpty(cultPacks)) {
            cultPacks.forEach(cultPack => {
                activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildCultPackViewV2(userContext, cultPack), "Build cult pack view"))
            })
        }

        if (!_.isEmpty(foodPackBookings)) {
            foodPackBookings.forEach(foodPackBooking => {
                activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildFoodPackView(userContext, foodPackBooking), "Build meal pack view"))
            })
        }

        if (!_.isEmpty(diyServicefulfilments)) {
            diyServicefulfilments.forEach(diyServicefulfilment => {
                activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildDIYPackView(userContext, diyServicefulfilment), "Build atlas pack view"))
            })
        }

        if (mealPlannerPack) {
            activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildMealPlannerPackView(userContext)))
        }

        if (!_.isEmpty(managedPlanPacks)) {
            managedPlanPacks.forEach(mpbooking => {
                activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildManagedPlanPackView(userContext, mpbooking), "Build managed plan pack view"))
            })
        }

        if (!_.isEmpty(therapyPacks)) {
            therapyPacks.forEach(therapyPack => {
                activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildTherapyPackView(userContext, therapyPack), "Build therapy plan pack view"))
            })
        }

        if (!_.isEmpty(cultPTPacks)) {
            cultPTPacks.forEach(cultPTPack => {
                activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildBundleSessionPackView(userContext, cultPTPack), "Build Bundle session pack view"))
            })
        }
        if (!_.isEmpty(cultLivePacks)) {
            cultLivePacks.forEach(cultLivePTPack => {
                activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildBundleSessionPackView(userContext, cultLivePTPack), "Build Bundle session pack view"))
            })
        }

        if (!_.isEmpty(liveSGTPacks)) {
            liveSGTPacks.forEach(liveSGTPack => {
                activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildBundleSessionPackView(userContext, liveSGTPack), "Build Bundle session pack view"))
            })
        }

        if (!_.isEmpty(hcuPacks)) {
            hcuPacks.forEach(hcuPack => {
                activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildHCUActivePackView(userContext, hcuPack), "Build hcu active pack view"))
            })
        }

        if (!_.isEmpty(aimgPacks)) {
            aimgPacks.forEach(aimgbooking => {
                activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildManagedPlanPackView(userContext, aimgbooking), "Build ai mg active pack view"))
            })
        }

        if (!_.isEmpty(physioPacks)) {
            physioPacks.forEach(physiobooking => {
                activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildBundleSessionPackView(userContext, physiobooking), "Build physio pack view"))
            })
        }

        if (!_.isEmpty(skinPacks)) {
            skinPacks.forEach(skinBooking => {
                activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildHCUActivePackView(userContext, skinBooking), "Build skin pack view"))
            })
        }

        if (!_.isEmpty(transformPacks)) {
            transformPacks.forEach(tfBooking => {
                activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildBundleSessionPackView(userContext, tfBooking), "Build transform pack view"))
            })
        }
        if (!_.isEmpty(hairPacks)) {
            hairPacks.forEach(hairBooking => {
                activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildHCUActivePackView(userContext, hairBooking), "Build hair pack view"))
            })
        }

        if (!_.isEmpty(beautyPacks)) {
            beautyPacks.forEach(beautyBooking => {
                activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildHCUActivePackView(userContext, beautyBooking), "Build beauty pack view"))
            })
        }

        if (!_.isEmpty(lcPacks)) {
            lcPacks.forEach(lcPacksbooking => {
                activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildBundleSessionPackView(userContext, lcPacksbooking), "Build LC pack view"))
            })
        }

        if (!_.isEmpty(cfLiveMemberships)) {
            for (const cfLiveMembership of cfLiveMemberships) {
                const product = await this.catalogueService.getProduct(cfLiveMembership.productId)
                if (LiveUtil.getLiveFitMembershipState(userContext, cfLiveMembership, product) !== "EXPIRED") {
                    if (_.isNil(product) || product.vendorId !== "TRANSFORM") {
                    activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildCFLivePackView(userContext, cfLiveMembership)))
                    }
                }
            }
        }

        if (!_.isEmpty(consultationPacks)) {
            consultationPacks.forEach(consultationPack => {
                activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildConsultationPackView(userContext, consultationPack), "Build consultation pack view"))
            })
        }

        if (!_.isEmpty(gymfitPacks)) {
            gymfitPacks.forEach(gymfitPack => {
                activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildGymFitPackView(userContext, gymfitPack), "Build GymFit pack view"))
            })
        }

        if (!_.isEmpty(playPacks)) {
            playPacks.forEach(playPack => {
                activePacksPromise.push(eternalPromise(this.activePackViewBuilderV1.buildPlayPackView(userContext, playPack), "Build play pack view"))
            })
        }

        const activePacksResult = await Promise.all(activePacksPromise)
        const activePackView: ActivePackViewV1[] = []
        activePacksResult.forEach(activePack => {
            if (activePack.obj) {
                activePackView.push(activePack.obj)
            } else {
                this.logger.error(activePack.err)
            }
        })
        const sortedViews = activePackView.sort((a, b) => {
            if (!this.isDiy(a.productType) && this.isDiy(b.productType)) {
                return -1
            } else if (this.isDiy(a.productType) && !this.isDiy(b.productType)) {
                return 1
            } else {
                if (a.startDate > b.startDate) {
                    return -1
                } else if (a.startDate < b.startDate) {
                    return 1
                } else {
                    return 0
                }
            }
        })

        if (limit !== undefined) {
            return sortedViews.slice(0, limit)
        }
        return sortedViews
    }

    async getOnePassMembershipItem(userContext: UserContext): Promise<MembershipItem[]> {
        const userId = userContext.userProfile.userId
        const result = await eternalPromise(this.membershipService.getCachedMembershipsForUser(userId, AppUtil.getTenantFromUserContext(userContext), ["ONEPASS"]))
        const onePassMemberships = result?.obj
        const filteredOnePassMemberships = onePassMemberships.filter(membership => !_.isNil(membership.benefits.find(benefit => benefit.name === "ONEPASS" && !benefit.allowOverlap)))
        const membershipListPromise: Promise<{ obj: MembershipItem, err?: any }>[] = []
        if (!_.isEmpty(filteredOnePassMemberships)) {
            for (const onePassMembership of onePassMemberships) {
                const membershipItemPromise: Promise<MembershipItem> = this.activePackViewBuilderV1.buildOnePassMembership(userContext, onePassMembership)
                if (membershipItemPromise !== null) {
                    membershipListPromise.push(eternalPromise(membershipItemPromise))
                }
            }
        }
        const membershipResult: { obj: MembershipItem; err?: any }[] = await Promise.all(membershipListPromise)
        const membershipItems: MembershipItem[] = _.map(
            _.filter(membershipResult, membership => !_.isEmpty(membership.obj)), membership => membership.obj)

        return membershipItems
    }

    private async retnum(str: string) {
        const num = str.replace(/[^0-9]/g, "")
        return parseInt(num, 10)
    }

    private async convertMemStatusToMemDetailState(status: Status) {
        let state
        switch (status) {
            case "PURCHASED":
                state = MembershipStates.ACTIVE
                break
            case "CANCELLED":
                state = MembershipStates.CANCELLED
                break
            case "PAUSED":
                state = MembershipStates.PAUSED
                break
            case "SUSPENDED":
                state = MembershipStates.CANCELLED
                break

        }
        return state
    }


     async getCultComplimentaryMembership(userContext: UserContext, memDetail: Membership): Promise<MembershipDetails> {
        const tz = userContext.userProfile.timezone
        const productId = memDetail.productId
        const packId = await this.retnum(productId)
        const membershipDetails: MembershipDetails = {
            pauseDetails: null,
            cityId: 1, // the city-id is not being used anywhere
            endDate: TimeUtil.formatDateInTimeZoneDateFns(tz, TimeUtil.parseDateFromEpoch(memDetail.end), "yyyy-MM-dd"),
            id: memDetail.id,
            isPreReg: false,
            isSubscription: false,
            packId: packId, // TODO: Deprecate cultPackId
            productId,
            startDate: TimeUtil.formatDateInTimeZoneDateFns(tz, TimeUtil.parseDateFromEpoch(memDetail.start), "yyyy-MM-dd"),
            state: await this.convertMemStatusToMemDetailState(memDetail.status)
        }
        return membershipDetails
    }

    async getCultSummaryAndLivePack(userContext: UserContext): Promise<MembershipItem[]> {
        const userId = userContext.userProfile.userId
        const promises: Promise<any>[] = []
        const cultSummaryMapPromise = eternalPromise(this.cacheHelper.getCultSummaryForAllSubUsers(userId))
        const livefitPromise = eternalPromise(this.DIYFulfilmentService.getAllMembershipDetails(userId, AppUtil.getTenantFromUserContext(userContext)))
        const gymfitPromise = eternalPromise(this.membershipService.getMembershipsForUser(userId, AppUtil.getTenantFromUserContext(userContext), ["GYMFIT_GA", "GYMFIT_GX"]))
        const playPackPromise = eternalPromise(this.membershipService.getMembershipsForUser(userId, AppUtil.getTenantFromUserContext(userContext), ["PLAY"],  ["PURCHASED", "PAUSED"]))
        const luxPackPromise = eternalPromise(this.membershipService.getMembershipsForUser(userId, AppUtil.getTenantFromUserContext(userContext), ["LUX"],  ["PURCHASED", "PAUSED"]))
        const transformPromise = eternalPromise(this.membershipService.getMembershipsForUser(userId, AppUtil.getTenantFromUserContext(userContext), ["COACH_PLUS", "COACH_TRANSFORM", "COACH_BOOTCAMP_NUTRITION", "LIFT"],  ["PURCHASED"]))
        const cultComplimentaryAccessPromise = eternalPromise(this.membershipService.getMembershipsForUser(userId, AppUtil.getTenantFromUserContext(userContext), ["CULT_COMPLIMENTARY_ACCESS"],  ["PURCHASED"]))
        const ptMembershipPromise = eternalPromise(this.membershipService.getMembershipsForUser(userId, AppUtil.getTenantFromUserContext(userContext), [PT_MEMBERSHIP_BENEFIT_NAME]))
        promises.push(cultSummaryMapPromise)
        promises.push(livefitPromise)
        promises.push(gymfitPromise)
        promises.push(playPackPromise)
        promises.push(luxPackPromise)
        promises.push(transformPromise)
        promises.push(cultComplimentaryAccessPromise)
        promises.push(ptMembershipPromise)
        const result = await Promise.all(promises)
        const membershipListPromise: Promise<{ obj: MembershipItem, err?: any }>[] = []
        const cultMembershipMap = result[0]?.obj
        const cfLiveMemberships = result[1]?.obj
        const gymfitMemberships = result[2]?.obj
        const playMemberships = result[3]?.obj
        const luxMemberships = result[4]?.obj
        const transformMemberships = result[5]?.obj
        const cultComplimentaryAccessMemberships = result[6]?.obj
        const ptMemberships = result[7]?.obj

        if (!_.isEmpty(ptMemberships)) {
            for (const ptMembership of ptMemberships) {
                const membershipItemPromise: Promise<MembershipItem> = this.activePackViewBuilderV1.buildPTMembership(userContext, ptMembership)
                if (membershipItemPromise !== null) {
                    membershipListPromise.push(eternalPromise(membershipItemPromise))
                }
            }
        }

        if (!_.isEmpty(cultMembershipMap)) {
            _.forEach(Object.keys(cultMembershipMap), userId => {
                const cultSummary: CultSummary = cultMembershipMap[userId]
                if (!_.isNil(cultSummary.membershipSummary.current.cult)) {
                    const currentCultMembershipItemPromise: Promise<MembershipItem> = this.activePackViewBuilderV1.buildCultSummaryMembership(userContext, cultSummary.membershipSummary.current.cult)
                    if (currentCultMembershipItemPromise !== null) {
                        membershipListPromise.push(eternalPromise(currentCultMembershipItemPromise, "Build cult pack view"))
                    }
                }
                if (!_.isNil(cultSummary.membershipSummary.upcoming.cult)) {
                    const upcomingCultMembershipItemPromise: Promise<MembershipItem> = this.activePackViewBuilderV1.buildCultSummaryMembership(userContext, cultSummary.membershipSummary.upcoming.cult)
                    if (upcomingCultMembershipItemPromise !== null) {
                        membershipListPromise.push(eternalPromise(upcomingCultMembershipItemPromise, "Build cult pack view"))
                    }
                }
            })
        }
        if (!_.isEmpty(cfLiveMemberships)) {
            for (const cfLiveMembership of cfLiveMemberships) {
                const product = await this.catalogueService.getProduct(cfLiveMembership.productId)
                if (LiveUtil.getLiveFitMembershipState(userContext, cfLiveMembership, product) !== "EXPIRED") {
                    if (_.isNil(product) || product.vendorId !== "TRANSFORM") {
                        const membershipItemPromise: Promise<MembershipItem> = this.activePackViewBuilderV1.buildCFLiveMembership(userContext, cfLiveMembership)
                        if (membershipItemPromise !== null) {
                            membershipListPromise.push(eternalPromise(membershipItemPromise))
                        }
                    }
                }
            }
        }
        if (!_.isEmpty(gymfitMemberships)) {
            for (const gymfitMembership of gymfitMemberships) {
                const membershipItemPromise: Promise<MembershipItem> = this.activePackViewBuilderV1.buildGymFitMembership(userContext, gymfitMembership)
                if (membershipItemPromise !== null) {
                    membershipListPromise.push(eternalPromise(membershipItemPromise))
                }
            }
        }
        if (!_.isEmpty(playMemberships)) {
            for (const playMembership of playMemberships) {
                const membershipItemPromise: Promise<MembershipItem> = this.activePackViewBuilderV1.buildPlayMembership(userContext, playMembership)
                if (membershipItemPromise !== null) {
                    membershipListPromise.push(eternalPromise(membershipItemPromise))
                }
            }
        }
        if (!_.isEmpty(luxMemberships)) {
            for (const luxMembership of luxMemberships) {
                const membershipItemPromise: Promise<MembershipItem> = this.activePackViewBuilderV1.buildLuxMembership(userContext, luxMembership)
                if (membershipItemPromise !== null) {
                    membershipListPromise.push(eternalPromise(membershipItemPromise))
                }
            }
        }
        if (!_.isEmpty(transformMemberships)) {
            for (const transformMembership of transformMemberships) {
                const membershipItemPromise: Promise<MembershipItem> = this.activePackViewBuilderV1.buildTransformMembership(userContext, transformMembership)
                if (membershipItemPromise !== null) {
                    membershipListPromise.push(eternalPromise(membershipItemPromise))
                }
            }
        }

        if (!_.isEmpty(cultComplimentaryAccessMemberships)) {
            for (const cultComplimentaryAccessMembership of cultComplimentaryAccessMemberships) {
                const membershipDetail = await this.getCultComplimentaryMembership(userContext, cultComplimentaryAccessMembership)
                const membershipItemPromise: Promise<MembershipItem> = this.activePackViewBuilderV1.buildComplimentaryCultMembership(userContext, membershipDetail)
                if (membershipItemPromise !== null) {
                    membershipListPromise.push(eternalPromise(membershipItemPromise))
                }
            }
        }

        const membershipResult: { obj: MembershipItem; err?: any }[] = await Promise.all(membershipListPromise)
        const membershipItems: MembershipItem[] = _.map(
            _.filter(membershipResult, membership => !_.isEmpty(membership.obj)), membership => membership.obj)

        return membershipItems
    }

    private isDiy(productType: ProductType) {
        if (productType === "DIY_FITNESS" || productType === "DIY_MEDITATION") {
            return true
        }
        return false
    }


    private async detectPreferredCity(tenant: Tenant, latitude: number, longitude: number, ip: string, session: Session, detectedCityResponseByIp: DetectedCityResponseByIp): Promise<FindPreferredCityResponse> {
        const isInternationalApp = tenant === Tenant.LIVEFIT_APP
        let showCitySelection = false
        const cityAndCountry = (latitude && longitude) ? await this.cityService.getCityAndCountry(tenant, latitude, longitude) : { city: detectedCityResponseByIp.city, country: detectedCityResponseByIp.country }

        const sessionCityId = session.sessionData.cityId
        this.logger.info(`latitude ${latitude} longitude ${longitude}  ip ${ip} selectedCityId ${sessionCityId}`)

        // Notify if there is any city change marked in less than a min
        let isCityChangeToBeNotified = (new Date().getTime() - session.sessionData.citySelectedTimeInMillis) < 60000

        const cityBasedOnSession = sessionCityId && (await this.cityService.getCityById(sessionCityId)) || undefined
        const cityBasedOnIp = detectedCityResponseByIp.city

        // If  city stored in session different from detected city and city selection has been made 2 days before,
        // show the city selection popup

        if (sessionCityId && !isInternationalApp) {
            if (cityAndCountry.city) {
                if (cityAndCountry.city.cityId === sessionCityId)
                    showCitySelection = false
                else {
                    const timeDiff = new Date().getTime() - session.sessionData.citySelectedTimeInMillis
                    if (timeDiff > 2 * 24 * 60 * 60 * 1000) {
                        showCitySelection = true
                    }
                }
            }
        } else {
            // If we are able to detect the city then don't show the pop up,
            // else show the city / country selection popup
            isCityChangeToBeNotified = true
            if (cityAndCountry.city) {
                session.sessionData.cityId = cityAndCountry.city.cityId
                session.sessionData.isCityManuallySelected = true
                session.sessionData.citySelectedTimeInMillis = new Date().getTime()
                showCitySelection = false
            } else {
                cityAndCountry.city = !_.isEmpty(cityBasedOnIp) ? cityBasedOnIp : this.cityService.getDefaultCity(tenant)
                showCitySelection = true
            }
        }

        /*
         * international app :: cityAndCountry -> IP based city or Default
         * india app :: cityAndCountry -> session based, IP based or Default
         * city modal -> disabled on international app
         */
        let finalCity = cityBasedOnSession

        if (_.isEmpty(finalCity) || isInternationalApp) {
            finalCity = cityAndCountry.city
        }

        if (isInternationalApp) {
            showCitySelection = false
        }

        const isDetectedCityServiceable: boolean = AppUtil.isDigitalServiceableAtLocation(tenant, finalCity.cityId, this.cityService)
        session.sessionData.isLocationServiceable = isDetectedCityServiceable

        const { detectedCityName, detectedCountryCode } = detectedCityResponseByIp
        return { showCitySelection: showCitySelection, city: finalCity, detectedCityName, detectedCountryCode, isCityChangeToBeNotified }
    }

    async findPreferredCity(tenant: Tenant, latitude: number, longitude: number, ip: string, session: Session, passedCityId?: string): Promise<FindPreferredCityResponse> {
        const detectedCityResponseByIp = await this.CFAPICityService.getCityAndCountryByIp(tenant, ip)
        const showCitySelection = false
        if (passedCityId) {
            const selectedCity = await this.cityService.getCityById(passedCityId)
            if (selectedCity) {
                session.sessionData.cityId = selectedCity.cityId
                const { detectedCityName, detectedCountryCode } = detectedCityResponseByIp
                return { showCitySelection, city: selectedCity, detectedCityName, detectedCountryCode, isCityChangeToBeNotified: true }
            }
        }
        return this.detectPreferredCity(tenant, latitude, longitude, ip, session, detectedCityResponseByIp)
    }


    async updateCity(userContext: UserContext, session: Session, selectedCityId: string): Promise<CityResponseV2> {
        session.sessionData.cityId = selectedCityId
        session.sessionData.isCityManuallySelected = true
        session.sessionData.citySelectedTimeInMillis = new Date().getTime()

        const selectedCityPromise = this.cityService.getCityById(selectedCityId)
        // Removing center selection on city update
        session.sessionData.cultCenterId = undefined
        session.sessionData.mindCenterId = undefined
        const city = await selectedCityPromise
        // fire and forget city event to Rashi
        const appTenant: AppTenant = AppUtil.getAppTenantFromUserContext(userContext)
        this.publishUserEventToRashi(userContext.userProfile.userId, { timezone: city.timezone, cityId: city.cityId }, AttributeAction.REPLACE, appTenant)
        const updateSessionPromise = this.sessionBusiness.updateSessionData(session.at, session.sessionData)
        const updateDevicePromsise = this.deviceBusiness.updateSessionInformation(session.deviceId, session.sessionData, AppUtil.getTenantFromUserContext(userContext))
        const user = await userContext.userPromise
        await Promise.all([updateDevicePromsise, updateSessionPromise])
        const verticalInfo = await this.navBarBusiness.getUserAndSupportedVerticalsInfo(userContext, city, session.userId)
        const supportedVerticals: VerticalType[] = verticalInfo.verticals.map(vertical => vertical.verticalType)
        const appTabs: TabTypes[] = ["PLAN", ...supportedVerticals]
        return {
            appTabs: appTabs,
            verticals: verticalInfo.verticals,
            isMoreToBeHighlighted: verticalInfo.isMoreToBeHighlighted,
            supportedVerticals
        }
    }

    async publishUserEventToRashi(userId: string | Number, body: any, action: AttributeAction, appTenant: AppTenant): Promise<boolean> {
        if (!Number(userId)) {
            this.logger.error(`Rashi user Event failed for userId : ${userId} and body ${JSON.stringify(body)} and action: ${action}`)
            return false
        }
        const rashiEvent: UserEvent = {
            userId: Number(userId),
            type: EventType.USER_PROFILE_EVENT,
            eventTime: new Date().getTime(),
            action,
            body
        }
        const isEventPublished: boolean = await this.rashiSnsClient.publishUserEvent(rashiEvent, appTenant)
        if (isEventPublished) {
            // this.logger.info("successfully published user rashi Event : " + JSON.stringify(rashiEvent))
            return true
        }
        else {
            this.logger.error("Unable to publish user Event To rashi :" + JSON.stringify(rashiEvent))
            return false
        }
    }

    async publishUserActivityEventToRashi(userId: string | Number, eventName: string, body: any, appTenant: AppTenant): Promise<boolean> {
        if (!Number(userId)) {
            this.logger.error(`Rashi user activity Event failed for userId : ${userId} and body ${JSON.stringify(body)}`)
            return false
        }
        const rashiEvent: UserEvent = {
            userId: Number(userId),
            eventName,
            type: EventType.USER_ACTIVITY_EVENT,
            eventTime: new Date().getTime(),
            body
        }
        const isEventPublished: boolean = await this.rashiSnsClient.publishUserEvent(rashiEvent, appTenant)
        if (isEventPublished) {
            return true
        }
        else {
            this.logger.error("Unable to publish user Event To rashi :" + JSON.stringify(rashiEvent))
            return false
        }
    }

    async getImageSignedUrl(fileName: string, s3BucketName?: string): Promise<SignedUrlResponse> {
        let url = ""
        let s3FileName = ""
        let mimeType = ""
        let fileBaseName = ""
        const bucketName = s3BucketName ? s3BucketName : (process.env.ENVIRONMENT === "PRODUCTION") ? "profile-pictures-prod" : "profile-pictures-staging"
        const s3Url = `https://${bucketName}.s3.amazonaws.com`
        let completeFilePath = ""
        if (fileName) {
            try {
                const extName = path.extname(fileName)
                fileBaseName = extName ? fileName.slice(0, fileName.indexOf(extName)) : fileName
                const timeStamp = TimeUtil.getCurrentEpoch()
                mimeType = mime.lookup(fileName) as string
                s3FileName = fileBaseName ? `${fileBaseName}-${timeStamp}${extName}` : ""
                if (s3FileName) {
                    const params = {
                        Bucket: bucketName,
                        Key: s3FileName
                    }
                    url = await this.s3Helper.getSignedUrlWithPutObject(params)
                    completeFilePath = `${s3Url}/${s3FileName}`
                }
                this.logger.info("s3fileName is ", s3FileName)
                return {
                    url,
                    fileName: completeFilePath,
                    mimeType
                }
            } catch (error) {
                this.logger.error("error in getting signed url is : ", error)
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("Error while creating a signed url").build()
            }
        } else {
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid request params in getting image signed url").build()
        }
    }

    async updatePincodeLocation(session: Session, locationInfo: LocationInfoBody, verticalType?: VerticalType): Promise<any> {
        let sessionData: SessionData

        if (locationInfo.pincode) {
            const place = await this.locationService.getPlaceFromPincode(locationInfo.pincode, verticalType)
            sessionData = {
                ...session.sessionData,
                gearLocationPreference: {
                    pincode: locationInfo.pincode,
                    placeName: place.name
                }
            }
        } else if (locationInfo.addressId) {
            const userDetailAddress: UserDeliveryAddress = await this.getAddress(session.userId, locationInfo.addressId)
            // Added check for cultgear vertical and with lat long zero not calling location service or pincode is present in address
            const ignoreLocationService = !_.isEmpty(userDetailAddress?.structuredAddress?.pincode) || (userDetailAddress?.vertical === "CULT_GEAR" && userDetailAddress.latLong[1] === 0 && userDetailAddress.latLong[0] === 0)

            const place = ignoreLocationService ? undefined : await this.locationService.getPlace(userDetailAddress.latLong[1], userDetailAddress.latLong[0], verticalType)
            const locationData: LocationPreferenceData = {
                addressId: locationInfo.addressId,
                placeName: userDetailAddress.addressType,
                placeAddress: `${userDetailAddress.addressLine1} ${userDetailAddress.addressLine2}`,
                latLong: {
                    lat: userDetailAddress.latLong[1],
                    long: userDetailAddress.latLong[0]
                },
                pincode: userDetailAddress?.structuredAddress?.pincode || place?.postalcode
            }

            sessionData = {
                ...session.sessionData,
                gearLocationPreference: locationData
            }
        } else if (locationInfo.latLong) {
            const place = await this.locationService.getPlace(locationInfo.latLong.lat, locationInfo.latLong.long, verticalType)
            sessionData = {
                ...session.sessionData,
                gearLocationPreference: {
                    placeName: place.name,
                    pincode: place.postalcode,
                    latLong: locationInfo.latLong
                }
            }
        } else {
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("Invalid request params in updatePincodeLocation").build()
        }

        return await this.sessionBusiness.updateSessionData(session.at, sessionData)
    }

    public async getYearEndReport(userContext: UserContext): Promise<any> {
        const user = await userContext.userPromise
        const ret = []
        const userYearEndData = await this.userYearEndReportReadOnlyDao.findOne({ userId: user.id })
        if (_.isEmpty(userYearEndData)) {
            return undefined
        }

        if (user.profilePictureUrl) {
            if (user.profilePictureUrl.includes("profile-pictures-staging") || user.profilePictureUrl.includes("profile-pictures-prod")) {
                ret.push({
                    imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_${this.getFileNameForUserProfilePicture(user.profilePictureUrl)},w_200,h_200,g_faces,c_thumb,fl_region_relative/e_saturation:50/fl_layer_apply,w_550,r_max,g_center,y_-205,x_-0/image/userReport/Photo3x.png`,
                    action: { actionType: "SHARE_ACTION" }
                })
            } else {
                ret.push({
                    imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/image/userReport/Photo3x.png`,
                    action: { actionType: "SHARE_ACTION" }
                })
            }
        }

        if (userYearEndData.classes) {
            ret.push({
                imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_200:${userYearEndData.classes},y_-30,co_rgb:fffbeb/image/userReport/Count3X.png`,
                action: { actionType: "SHARE_ACTION" }
            })
        }

        if (userYearEndData.minsWorked) {
            ret.push({
                imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_200:${userYearEndData.minsWorked},y_-30,co_rgb:fffbeb/image/userReport/Minutes3x.png`,
                action: { actionType: "SHARE_ACTION" }
            })
        }
        if (userYearEndData.buddies) {
            ret.push({
                imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_200:${userYearEndData.buddies},y_-30,co_rgb:fffbeb/image/userReport/Buddy3x.png`,
                action: { actionType: "SHARE_ACTION" }
            })
        }
        // if (userYearEndData.favWorkoutClasses && userYearEndData.favWorkout) {
        //     ret.push({
        //         imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_200:${userYearEndData.favWorkoutClasses},y_-30,co_rgb:fffbeb/l_text:fonts:BrandonText-Black.otf_90:${encodeURIComponent(userYearEndData.favWorkout)},y_-290,co_rgb:22cbff/image/userReport/FavWorkout3x.png`,
        //         action: { actionType: "SHARE_ACTION" }
        //     })
        // }
        if (userYearEndData.classes && userYearEndData.buddies && userYearEndData.favWorkout) {
            if (userYearEndData.caloriesBurnt) {
                ret.push({
                    imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_75:${userYearEndData.classes},y_-415,x_-10,co_rgb:ffd62c/l_text:fonts:BrandonText-Black.otf_75:${userYearEndData.buddies},y_-170,x_10,co_rgb:ffd62c/l_text:fonts:BrandonText-Black.otf_75:${encodeURIComponent(userYearEndData.favWorkout)},y_100,x_10,co_rgb:ffd62c/l_text:fonts:BrandonText-Black.otf_75:${userYearEndData.caloriesBurnt},y_400,x_10,co_rgb:ffd62c/image/userReport/Summarize3x.png`,
                    action: { actionType: "SHARE_ACTION" }
                })
            } else {
                ret.push({
                    imageUrl: `https://cdn-images.cure.fit/www-curefit-com/image/upload/l_text:fonts:BrandonText-Black.otf_75:${userYearEndData.classes},y_-415,x_-10,co_rgb:ffd62c/l_text:fonts:BrandonText-Black.otf_75:${userYearEndData.buddies},y_-170,x_10,co_rgb:ffd62c/l_text:fonts:BrandonText-Black.otf_75:${encodeURIComponent(userYearEndData.favWorkout)},y_100,x_10,co_rgb:ffd62c/image/userReport/SummarizeNoCalorie3x.png`,
                    action: { actionType: "SHARE_ACTION" }
                })
            }
        }
        return ret
    }

    private getFileNameForUserProfilePicture(profilePic: string): string {
        const bucketName = profilePic.includes("staging") ? "profile-pictures-staging" : "profile-pictures-prod"
        const userImageArray = profilePic.split("/")
        const fileName = userImageArray[userImageArray.length - 1]
        return bucketName + ":" + fileName
    }

    private async getDefaultAreaPromise(listingBrand: ListingBrandIdType = "EAT_FIT", cityId: string): Promise<DeliverySubArea> {
        switch (listingBrand) {
            case "WHOLE_FIT":
                return this.deliveryAreaService.getDefaultSubArea("FOOD", cityId, listingBrand)
            case "FOOD_MARKETPLACE":
                let city: City
                try {
                    city = this.cityService.getCityById(cityId)
                } catch (e) {
                    this.logger.error("SUPPRESS: City fetch failed for FOOD_MARKTRPLACE and cityId: " + cityId, e.message)
                    city = undefined
                }
                if (city?.defaultWellnessAreaId) {
                    return WellnessUtil.getWellnessDefaultSubAreaForCity(this.deliveryAreaService, city, this.rollbarService)
                }
                listingBrand = "EAT_FIT"
            default:
                return this.deliveryAreaService.getDefaultSubArea("FOOD", cityId, listingBrand)
        }
    }

    async setLocationPreference(userContext: UserContext, userId: string, cityId: string, preference: LocationPreferenceRequestEntity, pageFrom?: string): Promise<UpdateStatusResponseEntity> {
        if (preference.prefLocationType == LocationDataKey.CURRENT_LOC) {
            if (!userContext.sessionInfo.lat || !userContext.sessionInfo.lon || userContext.sessionInfo.lat == 0 || userContext.sessionInfo.lon == 0) {
                return { success: false, message: `Please provide location permission`}
            }
            const checkValue = await UserUtil.isCoordinateInCity(userContext, cityId, userContext.sessionInfo.lat, userContext.sessionInfo.lon, this.cityService)
            if (!checkValue.ok) {
                return { success: false, message: `Selected City is ${checkValue.currentCityName} but detected city is ${checkValue.detectedCity.name}`, currentCityName: checkValue.currentCityName, detectedCity: checkValue.detectedCity }
            }
        }

        if (preference.prefLocationType == LocationDataKey.COORDINATES) {
            if (_.isNil(preference.coordinates) || _.isNil(preference.coordinatesName)) {
                return { success: false, message: `Map Location not found`}
            }
            const checkValue = await UserUtil.isCoordinateInCity(userContext, cityId, preference.coordinates.lat, preference.coordinates.long, this.cityService)
            if (!checkValue.ok) {
                return { success: false, message: `Selected City is ${checkValue.currentCityName} but detected city is ${checkValue.detectedCity.name}`, currentCityName: checkValue.currentCityName, detectedCity: checkValue.detectedCity }
            }
        }

        const userPreference: UserPreferenceV2 = {
            settings: []
        }

        if (!_.isNil(preference.prefLocationType)) {
            if (pageFrom == "CLASS_BOOKING" || preference.pageFrom == "CLASS_BOOKING") {
                const cultUserPreference: CultUserPreference = {
                    settings: [],
                    shutdownSchedule: []
                }
                cultUserPreference.settings.push({
                    "key": "USER_BOOKING_V2_ACTIVE_SETTING",
                    "value": null
                })
                if (AuthUtil.isGuestUser(userContext)) {
                    const sessionInfo: SessionInfo = _.get(userContext, "sessionInfo")
                    const {at} = sessionInfo
                    const {sessionData} = sessionInfo
                    switch (preference.prefLocationType) {
                        case LocationDataKey.LOCALITY:
                            cultUserPreference.settings.at(0).value = "USER_BOOKING_V2_LOCALITY_V3"
                            cultUserPreference.settings.push({
                                "key": "USER_BOOKING_V2_LOCALITY_V3",
                                "value": preference.locality
                            })
                            break
                        case LocationDataKey.CURRENT_LOC:
                            cultUserPreference.settings.at(0).value = "USER_BOOKING_V2_LAT_LONG"
                            cultUserPreference.settings.push({
                                "key": "USER_BOOKING_V2_LAT_LONG",
                                "value": {
                                    "latitude": userContext.sessionInfo.lat,
                                    "longitude": userContext.sessionInfo.lon
                                }
                            })
                            break
                        case LocationDataKey.COORDINATES:
                            cultUserPreference.settings.at(0).value = "USER_BOOKING_V2_LAT_LONG"
                            cultUserPreference.settings.push({
                                "key": "USER_BOOKING_V2_LAT_LONG",
                                "value": {
                                    "latitude": preference.coordinates.lat,
                                    "longitude": preference.coordinates.long
                                }
                            })
                            break
                    }
                    sessionData.cultPreference = cultUserPreference
                    await this.sessionBusiness.updateSessionData(at, sessionData)
                } else {
                    const currentCity = this.cityService.getCityById(cityId)
                    const cultPreference = await this.cultFitService.getPreferenceByKey(currentCity.cultCityId, userId, "CUREFIT_API", userContext.userProfile.subUserId, "USER_BOOKING_V2_ACTIVE_SETTING")
                    if (cultPreference && !_.isEmpty(cultPreference.settings) && cultPreference.settings[0].value == "USER_BOOKING_V2_FAVOURITE_CENTER") {
                        this.cultFitService.savePreference(currentCity.cultCityId, userId, cultUserPreference, "CUREFIT_API", userContext.userProfile.subUserId)
                    }
                }
            }
            await UserUtil.updateLocationData(userContext, userId, cityId, LocationDataKey.LOC_PREF_TYPE, preference.prefLocationType, this.userAttributeClient, userPreference)
            let value: string
            switch (preference.prefLocationType) {
                case LocationDataKey.COORDINATES:
                    if (!_.isNil(preference.coordinates)) {
                        value = `${preference.coordinates.lat},${preference.coordinates.long}`
                        await UserUtil.updateLocationData(userContext, userId, cityId, LocationDataKey.COORDINATES_NAME, preference.coordinatesName, this.userAttributeClient, userPreference)
                    }
                    break
                case LocationDataKey.LOCALITY:
                    if (!_.isNil(preference.locality)) {
                        value = preference.locality
                    }
                    break
                case LocationDataKey.CURRENT_LOC:
                    value = `${userContext.sessionInfo.lat},${userContext.sessionInfo.lon}`
                    break
            }

            if (!_.isNil(value)) {
                await UserUtil.updateLocationData(userContext, userId, cityId, preference.prefLocationType, value, this.userAttributeClient, userPreference)
                if (!userContext.sessionInfo.isUserLoggedIn) {
                    const sessionData = userContext.sessionInfo.sessionData
                    sessionData.fitnessLocationPreference = userPreference
                    await this.sessionBusiness.updateSessionData(userContext.sessionInfo.at, sessionData)
                }

                return { success: true }
            }

            // Can change error message accordingly
            return { success: false, message: `Value for prefered location type ${preference.prefLocationType} is not provided` }
        }
        return { success: false, message: "Value for prefLocationType is not provided" }
    }

    async isEligibleForJuspayRP(userId: string, deviceId: string, orderSource: OrderSource): Promise<boolean> {
        if (_.isEmpty(userId) || userId === "0") {
            return false
        }
        let tenant, experimentId
        const orderSourcesToSkip: OrderSource[] = ["CUREFIT_APP"]
        if (orderSourcesToSkip.includes(orderSource)) {
            return true
        }
        else if (orderSource === "CULTSPORT_EMBED_APP" || orderSource === "CULTSPORT_WEBSITE") {
            tenant = Tenant.CULTSPORT_APP
            experimentId = ["production", "alpha"].includes(process.env.ENVIRONMENT.toLowerCase()) ? "1432" : "600"
        }
        else if (orderSource === "CUREFIT_NEW_WEBSITE") {
            tenant = Tenant.CUREFIT_APP
            experimentId = ["production", "alpha"].includes(process.env.ENVIRONMENT.toLowerCase()) ? "1433" : "604"
        }
        else {
            return false
        }

        let isJuspayRpFlow: boolean = false
        try {
            const hamletRequest: HamletConfigRequest<boolean> = {
                query: {
                    experimentId,
                    configKey: "isJuspayRpFlow",
                    defaultValue: false
                },
                context: {
                    userId,
                    deviceId: deviceId,
                    tenant: tenant
                }
            }
            isJuspayRpFlow = await this.hamletService.getConfig(hamletRequest)
            this.logger.info("Experiment check Debug - ", {hamletRequest, isJuspayRpFlow})
        } catch (err) {
            this.logger.error(`experiment check isJuspayRpFlow error for userId: ${userId}: `, { err })
        }
        this.logger.info(`experiment check isJuspayRpFlow for userId: ${userId} = ${isJuspayRpFlow}`, {orderSource, tenant, experimentId, deviceId, userId})
        return isJuspayRpFlow
    }

    async getLocationPreference(userContext: UserContext, userId: string, cityId: string): Promise<LocationPreferenceResponseEntity> {
        const response: LocationPreferenceResponseEntity = {
            prefLocationType: undefined
        }
        if (!userContext.sessionInfo.isUserLoggedIn) {
            UserUtil.getLocationPreferenceResponseEntityFromSessionData(userContext.sessionInfo.sessionData, cityId, response)
        } else {
            await UserUtil.getLocationPreference(userId, cityId, this.userAttributeClient, response)
        }
        return response
    }
}

export default UserBusiness
