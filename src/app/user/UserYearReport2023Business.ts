import { inject, injectable } from "inversify"
import { UserContext } from "@curefit/userinfo-common"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { IUserYearEndSummaryReadOnlyDao } from "./userYearEndSummary/UserYearEndSummaryDao"
import { UserYearEndSummary } from "./userYearEndSummary/UserYearEndSummary"
import { CULT_CLIENT_TYPES, ICultServiceOld, ICultService } from "@curefit/cult-client"
import { ISocialService, SOCIAL_CLIENT_TYPES } from "@curefit/social-client"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { IPageConfigReadOnlyDao, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import CultUtil from "../util/CultUtil"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { ISegmentService } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import * as _ from "lodash"
import { IMultiCrudKeyValue, REDIS_TYPES, ICrudKeyValue } from "@curefit/redis-utils"
import { IUserAttributeClient, RASHI_CLIENT_TYPES, UserEvent } from "@curefit/rashi-client"
import { TimeUtil } from "@curefit/util-common"
import { UserYearReport2022Business } from "./UserYearReport2022Business"
import { IUserYERCharacterRevealReportReadOnlyDao } from "./userYERCharacterRevealReport/userYERCharacterRevealReportDao"

@injectable()
export class UserYearReport2023Business {
    private crudDao: ICrudKeyValue
    constructor(
        @inject(PAGE_CONFIG_TYPES.PageConfigReadOnlySecondaryDao) protected pageConfigDao: IPageConfigReadOnlyDao,
        @inject(CUREFIT_API_TYPES.UserYearEndSummaryReadOnlyDaoMongoImpl) private userYearEndSummaryReadOnlyDao: IUserYearEndSummaryReadOnlyDao,
        @inject(CUREFIT_API_TYPES.UserYERCharacterRevealReportReadOnlyDaoMongoImpl) private userYERCharacterRevealReportReadOnlyDao: IUserYERCharacterRevealReportReadOnlyDao,
        @inject(CULT_CLIENT_TYPES.CultFitService) public cultFitServiceOld: ICultServiceOld,
        @inject(CULT_CLIENT_TYPES.CultFitService) public cultFitService: ICultService,
        @inject(SOCIAL_CLIENT_TYPES.SocialService) public socialService: ISocialService,
        @inject(BASE_TYPES.ILogger) public logger: ILogger,
        @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness,
        @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(REDIS_TYPES.MultiCrudKeyValueDao) private multiCrudKeyValueDao: IMultiCrudKeyValue,
        @inject(RASHI_CLIENT_TYPES.UserAttributeClient) public userAttributeClient: IUserAttributeClient,
        @inject(CUREFIT_API_TYPES.UserQuiz2021Business) private userYearReport2022Business: UserYearReport2022Business,
    ) {
        this.crudDao = this.multiCrudKeyValueDao.getICrudKeyValue("CFAPI-CACHE")
    }

    public async getUserEndYearReport2023(userContext: UserContext, questionId?: string, answerId?: string): Promise<any> {
        const userData = await this.serviceInterfaces.userCache.getUser(userContext.userProfile.userId)
        const currentMoment = TimeUtil.getMomentNow(TimeUtil.IST_TIMEZONE)
        const currentYear = "2023" // currentMoment.format("YYYY")

        const userName = _.isNil(userData) ? "" : userData.firstName
        const characterRevealPageConfig: any = await this.pageConfigDao.findOne({ pageId: { $eq: "CharacterRevealPageConfig" } }).then((pageConfig: any) => {
            return pageConfig.data
        })

        const configKey = "USER_YEARLY_FITNESS_ATTRIBUTE_CONFIG"
        const attributeCacheKey = "cf-app:user_yearly_fitness_attribute_config"
        // const userYearlyFitnessAttributeCacheConfig  = await this.crudDao.read(attributeCacheKey)
        // this.logger.info("userYearlyFitnessAttributeCacheConfig - ", JSON.stringify(userYearlyFitnessAttributeCacheConfig))
        // let userYearlyFitnessAttributeConfig = null
        // if (userYearlyFitnessAttributeCacheConfig) {
        //     userYearlyFitnessAttributeConfig = JSON.parse(userYearlyFitnessAttributeCacheConfig)
        // }

        // characterRevealPageConfig["responseConfig"]["question"]["options"] = characterRevealPageConfig["questionOptionsConfig"]

        const userYearlyFitnessAttributeConfig = characterRevealPageConfig["userYearlyFitnessAttributeConfig"]

        const formatNumberWithSuffix = (val: any) => {
          let value = val
          if (typeof value === "string") {
            value = Number(value)
          }
          const absValue = Math.abs(value)

          const numberWithCommas = (x: any) => {
            return x.toString().split(".")[0].length > 3 ? x.toString().substring(0, x.toString().split(".")[0].length - 3).replace(/\B(?=(\d{2})+(?!\d))/g, ",") + "," + x.toString().substring(x.toString().split(".")[0].length - 3) : x.toString()
          }

          if (absValue >= 1e9) {
            return numberWithCommas((value / 1e9).toFixed(1)) + " BILLION"
          } else if (absValue >= 1e6) {
            return numberWithCommas((value / 1e6).toFixed(1)) + " MILLION"
          } else if (absValue >= 1e3  &&  absValue < 1e6) {
            return numberWithCommas(value.toFixed(0))
          }
          else {
            return numberWithCommas(value)
          }
        }

        const removePagesByIds = (json: any, idsToRemove: any) => {
          const responseConfig = json
          const updatedPages = responseConfig["pages"].filter((page: any) => !idsToRemove.includes(page["id"]))

          const updatedJson =  {
              ...responseConfig,
              "pages": updatedPages,
          }
          return updatedJson
        }

        const userYearlyFitnessAttributeMap: any = {}

        if (userYearlyFitnessAttributeConfig && userYearlyFitnessAttributeConfig["ATTRIBUTES"]) {
            const ACTIVE_DAYS = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["ACTIVE_DAYS"] + "_" + currentYear
            const ACTIVITIES_DONE = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["ACTIVITIES_DONE"] + "_" + currentYear
            const WORKOUT_TIME_SPENT = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["WORKOUT_TIME_SPENT"] + "_" + currentYear
            const CALORIE_COUNT = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["CALORIE_COUNT"] + "_" + currentYear
            const UNIQUE_WORKOUT_FORMATS = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["UNIQUE_WORKOUT_FORMATS"] + "_" + currentYear
            const UNIQUE_WORKOUT_FORMATS_COUNT = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["UNIQUE_WORKOUT_FORMATS_COUNT"] + "_" + currentYear
            const LONGEST_WEEKLY_STREAK = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["LONGEST_WEEKLY_STREAK"] + "_" + currentYear
            const MORNING_SLOT_COUNT = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["MORNING_SLOT_COUNT"] + "_" + currentYear
            const EVENING_SLOT_COUNT = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["EVENING_SLOT_COUNT"] + "_" + currentYear
            const FAVOURITE_WORKOUT_SLOT = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["FAVOURITE_WORKOUT_SLOT"] + "_" + currentYear
            const YEARLY_MEMBERSHIP_DAYS = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["YEARLY_MEMBERSHIP_DAYS"] + "_" + currentYear
            const WORKOUTS_PER_MEMBERSHIP_MONTH = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["WORKOUTS_PER_MEMBERSHIP_MONTH"] + "_" + currentYear
            const FITNESS_LEVEL = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["FITNESS_LEVEL"] + "_" + currentYear
            const WEEKEND_ACTIVITY_COUNT = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["WEEKEND_ACTIVITY_COUNT"] + "_" + currentYear
            const MOST_ACTIVE_MONTH = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["MOST_ACTIVE_MONTH"] + "_" + currentYear
            const MOST_ACTIVE_MONTH_ACTIVITY_COUNT = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["MOST_ACTIVE_MONTH_ACTIVITY_COUNT"] + "_" + currentYear
            const FITNESS_COMEBACKS = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["FITNESS_COMEBACKS"] + "_" + currentYear
            const LAST_WORKOUT_DONE_AT = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["LAST_WORKOUT_DONE_AT"] + "_" + currentYear
            const FAVOURITE_FORMAT = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["FAVOURITE_FORMAT"] + "_" + currentYear
            const FAVOURITE_FORMAT_CLASSES = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["FAVOURITE_FORMAT_CLASSES"] + "_" + currentYear
            const GX_CLASSES =  userYearlyFitnessAttributeConfig["ATTRIBUTES"]["GX_CLASSES"] + "_" + currentYear
            const GYM_CLASSES = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["GYM_CLASSES"] + "_" + currentYear
            const LIVE_CLASSES = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["LIVE_CLASSES"] + "_" + currentYear
            const PLAY_CLASSES = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["PLAY_CLASSES"] + "_" + currentYear
            const DIY_CLASSES = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["DIY_CLASSES"] + "_" + currentYear
            const DANCE_FORMAT_CLASSES =  "yearly_format_count_dance" + "_" + currentYear
            const SNC_FORMAT_CLASSES = "yearly_format_count_snc" + "_" + currentYear
            const HRX_FORMAT_CLASSES = "yearly_format_count_hrx" + "_" + currentYear
            const BURN_FORMAT_CLASSES = "yearly_format_count_burn" + "_" + currentYear
            const YOGA_FORMAT_CLASSES = "yearly_format_count_yoga" + "_" + currentYear
            const BOXING_FORMAT_CLASSES = "yearly_format_count_boxing" + "_" + currentYear
            const BADMINTON_FORMAT_CLASSES = "yearly_format_count_badminton" + "_" + currentYear
            const SWIMMING_FORMAT_CLASSES = "yearly_format_count_swimming" + "_" + currentYear
            const GYM_FORMAT_CLASSES = "yearly_format_count_gym" + "_" + currentYear
            const COUNTRY_PERCENTILE = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["COUNTRY_PERCENTILE"] + "_" + currentYear
            const FAVOURITE_TRAINER = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["FAVOURITE_TRAINER"] + "_" + currentYear
            const FAVOURITE_TRAINER_CLASSES = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["FAVOURITE_TRAINER_CLASSES"] + "_" + currentYear

            const [ userFitnessAttributes ] = await Promise.all([
                await this.userAttributeClient.getBulkCachedUserAttributes(parseInt(userContext.userProfile.userId), [ ACTIVE_DAYS, ACTIVITIES_DONE,
                    WORKOUT_TIME_SPENT, CALORIE_COUNT, UNIQUE_WORKOUT_FORMATS, UNIQUE_WORKOUT_FORMATS_COUNT,
                    LONGEST_WEEKLY_STREAK, MORNING_SLOT_COUNT, EVENING_SLOT_COUNT, FAVOURITE_WORKOUT_SLOT,
                    YEARLY_MEMBERSHIP_DAYS, WORKOUTS_PER_MEMBERSHIP_MONTH, FITNESS_LEVEL, WEEKEND_ACTIVITY_COUNT,
                    MOST_ACTIVE_MONTH, MOST_ACTIVE_MONTH_ACTIVITY_COUNT, FITNESS_COMEBACKS, LAST_WORKOUT_DONE_AT,
                    FAVOURITE_FORMAT, FAVOURITE_FORMAT_CLASSES, GX_CLASSES, GYM_CLASSES, LIVE_CLASSES,
                    PLAY_CLASSES, DANCE_FORMAT_CLASSES, SNC_FORMAT_CLASSES, HRX_FORMAT_CLASSES, BURN_FORMAT_CLASSES, YOGA_FORMAT_CLASSES, BOXING_FORMAT_CLASSES, BADMINTON_FORMAT_CLASSES,
                    SWIMMING_FORMAT_CLASSES, GYM_FORMAT_CLASSES, COUNTRY_PERCENTILE, FAVOURITE_TRAINER, FAVOURITE_TRAINER_CLASSES ])
            ])

            if (userFitnessAttributes && userFitnessAttributes.attributes) {
                userYearlyFitnessAttributeMap["ACTIVE_DAYS"] = (Object)(userFitnessAttributes.attributes)[ACTIVE_DAYS]
                userYearlyFitnessAttributeMap["ACTIVITIES_DONE"] = (Object)(userFitnessAttributes.attributes)[ACTIVITIES_DONE]
                userYearlyFitnessAttributeMap["WORKOUT_TIME_SPENT"] = (Object)(userFitnessAttributes.attributes)[WORKOUT_TIME_SPENT]
                userYearlyFitnessAttributeMap["CALORIE_COUNT"] = (Object)(userFitnessAttributes.attributes)[CALORIE_COUNT]
                userYearlyFitnessAttributeMap["UNIQUE_WORKOUT_FORMATS"] = (Object)(userFitnessAttributes.attributes)[UNIQUE_WORKOUT_FORMATS]
                userYearlyFitnessAttributeMap["UNIQUE_WORKOUT_FORMATS_COUNT"] = (Object)(userFitnessAttributes.attributes)[UNIQUE_WORKOUT_FORMATS_COUNT]
                userYearlyFitnessAttributeMap["LONGEST_WEEKLY_STREAK"] = (Object)(userFitnessAttributes.attributes)[LONGEST_WEEKLY_STREAK]
                userYearlyFitnessAttributeMap["MORNING_SLOT_COUNT"] = (Object)(userFitnessAttributes.attributes)[MORNING_SLOT_COUNT]
                userYearlyFitnessAttributeMap["EVENING_SLOT_COUNT"] = (Object)(userFitnessAttributes.attributes)[EVENING_SLOT_COUNT]
                userYearlyFitnessAttributeMap["FAVOURITE_WORKOUT_SLOT"] = (Object)(userFitnessAttributes.attributes)[FAVOURITE_WORKOUT_SLOT]
                userYearlyFitnessAttributeMap["YEARLY_MEMBERSHIP_DAYS"] = (Object)(userFitnessAttributes.attributes)[YEARLY_MEMBERSHIP_DAYS]
                userYearlyFitnessAttributeMap["WORKOUTS_PER_MEMBERSHIP_MONTH"] = (Object)(userFitnessAttributes.attributes)[WORKOUTS_PER_MEMBERSHIP_MONTH]
                userYearlyFitnessAttributeMap["FITNESS_LEVEL"] = (Object)(userFitnessAttributes.attributes)[FITNESS_LEVEL]
                userYearlyFitnessAttributeMap["WEEKEND_ACTIVITY_COUNT"] = (Object)(userFitnessAttributes.attributes)[WEEKEND_ACTIVITY_COUNT]
                userYearlyFitnessAttributeMap["MOST_ACTIVE_MONTH"] = (Object)(userFitnessAttributes.attributes)[MOST_ACTIVE_MONTH]
                userYearlyFitnessAttributeMap["MOST_ACTIVE_MONTH_ACTIVITY_COUNT"] = (Object)(userFitnessAttributes.attributes)[MOST_ACTIVE_MONTH_ACTIVITY_COUNT]
                userYearlyFitnessAttributeMap["FITNESS_COMEBACKS"] = (Object)(userFitnessAttributes.attributes)[FITNESS_COMEBACKS]
                userYearlyFitnessAttributeMap["LAST_WORKOUT_DONE_AT"] = (Object)(userFitnessAttributes.attributes)[LAST_WORKOUT_DONE_AT]
                userYearlyFitnessAttributeMap["FAVOURITE_FORMAT"] = (Object)(userFitnessAttributes.attributes)[FAVOURITE_FORMAT]
                userYearlyFitnessAttributeMap["FAVOURITE_FORMAT_CLASSES"] = (Object)(userFitnessAttributes.attributes)[FAVOURITE_FORMAT_CLASSES]
                userYearlyFitnessAttributeMap["GX_CLASSES"] =  (Object)(userFitnessAttributes.attributes)[GX_CLASSES]
                userYearlyFitnessAttributeMap["GYM_CLASSES"] = (Object)(userFitnessAttributes.attributes)[GYM_CLASSES]
                userYearlyFitnessAttributeMap["LIVE_CLASSES"] = (Object)(userFitnessAttributes.attributes)[LIVE_CLASSES]
                userYearlyFitnessAttributeMap["PLAY_CLASSES"] = (Object)(userFitnessAttributes.attributes)[PLAY_CLASSES]
                userYearlyFitnessAttributeMap["DIY_CLASSES"] = (Object)(userFitnessAttributes.attributes)[DIY_CLASSES]
                userYearlyFitnessAttributeMap["DANCE_FORMAT_CLASSES"] = (Object)(userFitnessAttributes.attributes)[DANCE_FORMAT_CLASSES]
                userYearlyFitnessAttributeMap["SNC_FORMAT_CLASSES"] = (Object)(userFitnessAttributes.attributes)[SNC_FORMAT_CLASSES]
                userYearlyFitnessAttributeMap["HRX_FORMAT_CLASSES"] = (Object)(userFitnessAttributes.attributes)[HRX_FORMAT_CLASSES]
                userYearlyFitnessAttributeMap["BURN_FORMAT_CLASSES"] = (Object)(userFitnessAttributes.attributes)[BURN_FORMAT_CLASSES]
                userYearlyFitnessAttributeMap["YOGA_FORMAT_CLASSES"] = (Object)(userFitnessAttributes.attributes)[YOGA_FORMAT_CLASSES]
                userYearlyFitnessAttributeMap["BOXING_FORMAT_CLASSES"] = (Object)(userFitnessAttributes.attributes)[BOXING_FORMAT_CLASSES]
                userYearlyFitnessAttributeMap["BADMINTON_FORMAT_CLASSES"] = (Object)(userFitnessAttributes.attributes)[BADMINTON_FORMAT_CLASSES]
                userYearlyFitnessAttributeMap["SWIMMING_FORMAT_CLASSES"] = (Object)(userFitnessAttributes.attributes)[SWIMMING_FORMAT_CLASSES]
                userYearlyFitnessAttributeMap["GYM_FORMAT_CLASSES"] = (Object)(userFitnessAttributes.attributes)[GYM_FORMAT_CLASSES]
                userYearlyFitnessAttributeMap["COUNTRY_PERCENTILE"] = (Object)(userFitnessAttributes.attributes)[COUNTRY_PERCENTILE]
                userYearlyFitnessAttributeMap["FAVOURITE_TRAINER"] = (Object)(userFitnessAttributes.attributes)[FAVOURITE_TRAINER]
                userYearlyFitnessAttributeMap["FAVOURITE_TRAINER_CLASSES"] = (Object)(userFitnessAttributes.attributes)[FAVOURITE_TRAINER_CLASSES]
            }
        }

        this.logger.info("userYearlyFitnessAttributeMap - ", JSON.stringify(userYearlyFitnessAttributeMap))

      const properCase = (text: string) => {
        return text.replace("_", " ").replace(/\w\S*/g, function (txt) {
            return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
        })
      }
      const user = await userContext.userPromise
      const firstName = properCase(user.firstName ?? user.lastName)
      // const userYearEndData: UserYearEndSummary = await this.userYearEndSummaryReadOnlyDao.findOne({user_id: user.id})
      // const userYearEndData: UserYearEndSummary = await this.userYERCharacterRevealReportReadOnlyDao.findOne({user_id: user.id})
      // if (!userYearEndData) {
      //     this.logger.error("Error in year end report 2023, user data dont exist, userId: " + user.id)
      //     return null
      // }

      this.logger.info("userYearlyFitnessAttribute fav format 1 - ", userYearlyFitnessAttributeMap["FAVOURITE_FORMAT"])
      // this.logger.info("userYearlyFitnessAttribute fav format 2 - ", userYearEndData.favorate_format_1)
      let fav_format = userYearlyFitnessAttributeMap["FAVOURITE_FORMAT"]
      this.logger.info("userYearlyFitnessAttribute fav format 3 - ", fav_format)
      fav_format = ((_.isEmpty(fav_format) || _.isNil(fav_format)) ? "HYBRID"
        : (!_.isNil(characterRevealPageConfig["favFormatMap"][fav_format.toUpperCase()]) ? characterRevealPageConfig["favFormatMap"][fav_format.toUpperCase()] : "HYBRID"))
      this.logger.info("userYearlyFitnessAttribute fav format 4 - ", fav_format)
      // fav_format = fav_format.charAt(0).toUpperCase() + fav_format.slice(1).toLowerCase()

      const isGymUser: boolean =  (userYearlyFitnessAttributeMap["FAVOURITE_FORMAT"] && userYearlyFitnessAttributeMap["FAVOURITE_FORMAT"] === "Gym Access") || fav_format === "GYM"

      const questionOptions = characterRevealPageConfig["responseConfig"]["question"]["options"]
      const questionConfig = questionOptions.find((obj: { id: any }) => isGymUser && obj.id == answerId)

      // Order matters here. eg: FAVOURITE_TRAINER_CLASSES should precede FAVOURITE_TRAINER
      const configVariables: Record<string, any> = {
        FAV_FORMAT: fav_format,
        FAV_FORMAT_DISPLAY_NAME: characterRevealPageConfig["characterConfig"][fav_format.toUpperCase()]["formatDisplayName"] ?? "",
        PROFILE_PIC: user.profilePictureUrl ?? "",
        USER_NAME: firstName ?? "",
        ACTIVE_DAYS: parseInt(userYearlyFitnessAttributeMap["ACTIVE_DAYS"] ?? 0), // ?? userYearEndData.days_worked_out
        WORKOUT_TIME_SPENT_TEXT: formatNumberWithSuffix(parseInt(userYearlyFitnessAttributeMap["WORKOUT_TIME_SPENT"] ?? 0)),
        WORKOUT_TIME_SPENT: parseInt(userYearlyFitnessAttributeMap["WORKOUT_TIME_SPENT"] ?? 0), // userYearEndData.time_spent
        CALORIE_COUNT_TEXT: formatNumberWithSuffix(userYearlyFitnessAttributeMap["CALORIE_COUNT"] ?? 0), // userYearEndData.calories
        CALORIE_COUNT: parseInt(userYearlyFitnessAttributeMap["CALORIE_COUNT"] ?? 0), // userYearEndData.calories
        CHARACTER_INFO: (!isGymUser && characterRevealPageConfig["characterConfig"][fav_format.toUpperCase()]) ? characterRevealPageConfig["characterConfig"][fav_format.toUpperCase()]["characterInfo"]
          : ((answerId && questionConfig && characterRevealPageConfig["characterConfig"][questionConfig.mapToFormat.toUpperCase()]) ? characterRevealPageConfig["characterConfig"][questionConfig.mapToFormat.toUpperCase()]["characterInfo"] : ""),
        LONGEST_WEEKLY_STREAK: parseInt(userYearlyFitnessAttributeMap["LONGEST_WEEKLY_STREAK"] ?? 0), // userYearEndData.streak
        ACTIVITIES_DONE: parseInt(userYearlyFitnessAttributeMap["ACTIVITIES_DONE"] ?? 0), // userYearEndData.class_attend
        MOST_ACTIVE_MONTH_ACTIVITY_COUNT: parseInt(userYearlyFitnessAttributeMap["MOST_ACTIVE_MONTH_ACTIVITY_COUNT"] ?? 0),
        MOST_ACTIVE_MONTH: (userYearlyFitnessAttributeMap["MOST_ACTIVE_MONTH"] ?? "").trim().toString(), // userYearEndData.best_month
        SEASON: null,
        FITNESS_COMEBACKS_TEXT: "",
        FITNESS_COMEBACKS: parseInt(userYearlyFitnessAttributeMap["FITNESS_COMEBACKS"] ?? 0), // userYearEndData.comebacks
        FAVOURITE_TRAINER_CLASSES_TEXT: "",
        FAVOURITE_TRAINER_CLASSES: parseInt(userYearlyFitnessAttributeMap["FAVOURITE_TRAINER_CLASSES"] ?? 0), // userYearEndData.trainer_classes
        FAVOURITE_TRAINER: userYearlyFitnessAttributeMap["FAVOURITE_TRAINER"] ?? "", // userYearEndData.trainername
        UNIQUE_WORKOUT_FORMATS_COUNT: parseInt(userYearlyFitnessAttributeMap["UNIQUE_WORKOUT_FORMATS_COUNT"] ?? 0), // userYearEndData.formats
        FORMAT_LEVEL_COUNT_TEXT: "",
        WEEKEND_ACTIVITY_COUNT: parseInt(userYearlyFitnessAttributeMap["WEEKEND_ACTIVITY_COUNT"] ?? 0), //  userYearEndData.Weekend_at_cult
        TOP_PERCENTILE: parseInt(userYearlyFitnessAttributeMap["COUNTRY_PERCENTILE"] ?? 0), // userYearEndData.Better_than_users
        YER_CHARACTER_ASSIGNED: (!isGymUser && characterRevealPageConfig["characterConfig"][fav_format.toUpperCase()]) ? characterRevealPageConfig["characterConfig"][fav_format.toUpperCase()]["characterName"]
          : ((answerId && questionConfig && characterRevealPageConfig["characterConfig"][questionConfig.mapToFormat.toUpperCase()]) ? characterRevealPageConfig["characterConfig"][questionConfig.mapToFormat.toUpperCase()]["characterName"] : "NA")
      }

      // configVariables.CHARACTER_CARD_TEXT_Z = "LORD OF "
      // configVariables.CHARACTER_CARD_VALUE_Z = (!isGymUser && characterRevealPageConfig["characterConfig"][fav_format.toUpperCase()]) ? characterRevealPageConfig["characterConfig"][fav_format.toUpperCase()]["benefit"]
      // : ((answerId && questionConfig && characterRevealPageConfig["characterConfig"][fav_format.toUpperCase()]) ? characterRevealPageConfig["characterConfig"][questionConfig.mapToFormat.toUpperCase()]["benefit"] : null)
      configVariables.CHARACTER_CARD_TEXT_A = "Best Weekly\nStreak"
      configVariables.CHARACTER_CARD_VALUE_A = configVariables.LONGEST_WEEKLY_STREAK.toString()
      configVariables.CHARACTER_CARD_TEXT_B = "Sessions\ndone"
      configVariables.CHARACTER_CARD_VALUE_B = configVariables.ACTIVITIES_DONE.toString()
      configVariables.CHARACTER_CARD_TEXT_C = "Calories\nBurnt"
      configVariables.CHARACTER_CARD_VALUE_C = configVariables.CALORIE_COUNT
      configVariables.CHARACTER_CARD_TEXT_D = "Superpower\nEarned"
      configVariables.CHARACTER_CARD_VALUE_D = (!isGymUser && !_.isNil(characterRevealPageConfig["characterConfig"][fav_format.toUpperCase()])) ? characterRevealPageConfig["characterConfig"][fav_format.toUpperCase()]["focusedOn"] : "ENDURANCE"

      configVariables.FITNESS_COMEBACKS_TEXT = (configVariables.FITNESS_COMEBACKS == 1 ? "just " : "") + configVariables.FITNESS_COMEBACKS.toString() + (configVariables.FITNESS_COMEBACKS == 1 ? " time" : " times")
      configVariables.FAVOURITE_TRAINER_CLASSES_TEXT = configVariables.FAVOURITE_TRAINER_CLASSES.toString() + (configVariables.FAVOURITE_TRAINER_CLASSES == 1 ? " session" : " sessions")

      this.logger.info("userYearlyFitnessAttribute fav format 6 - ", configVariables.YER_CHARACTER_ASSIGNED, "-", characterRevealPageConfig["characterConfig"][fav_format.toUpperCase()], "-", characterRevealPageConfig["characterConfig"][fav_format.toUpperCase()]["characterName"])

        const month = configVariables.MOST_ACTIVE_MONTH.toLowerCase()
        if (month === "december" || month === "january" || month === "february") {
          configVariables.SEASON = "Winter"
        }
        else if (month === "march" || month === "april" || month === "may") {
          configVariables.SEASON = "Summer"
        }
        else if (month === "june" || month === "july" || month === "august" || month === "september") {
          configVariables.SEASON = "Monsoon"
        }
        else if (month === "october" || month === "november") {
          configVariables.SEASON = "Autumn"
        }

        // GYM Answer
        const userGymFitnessGoalKey = userYearlyFitnessAttributeConfig["ATTRIBUTES"]["GYM_GOAL_ANSWER"] + "_" + currentYear
        let isGymFitnessGoalQuestionAnswered = false
        if (isGymUser) {
            this.logger.info("userYearlyFitnessAttribute GYM ANSWER isGymUser - ", isGymUser)
            // configVariables.CHARACTER_CARD_TEXT_B = "Sessions\ndone"
            // configVariables.CHARACTER_CARD_VALUE_B = configVariables.ACTIVITIES_DONE.toString()
            // configVariables.CHARACTER_CARD_TEXT_D = "You focused\non"
            // configVariables.CHARACTER_CARD_VALUE_D = "ENDURANCE"
            // configVariables.CHARACTER_CARD_TEXT_Z = "AKA"
            // configVariables.CHARACTER_CARD_VALUE_Z = ` "THE NAME"`

            const user_attributes = await this.userAttributeClient.getCachedUserAttributes(parseInt(userContext.userProfile.userId), userGymFitnessGoalKey)

            this.logger.info("userYearlyFitnessAttribute GYM ANSWER user_attributes - ", user_attributes)

            isGymFitnessGoalQuestionAnswered = user_attributes && user_attributes.attributes && ((Object)(user_attributes.attributes)[userGymFitnessGoalKey])
              && (typeof ((Object)(user_attributes.attributes)[userGymFitnessGoalKey]) === "string") && ((Object)(user_attributes.attributes)[userGymFitnessGoalKey]).length

            this.logger.info("userYearlyFitnessAttribute GYM ANSWER isGymFitnessGoalQuestionAnswered - ", isGymFitnessGoalQuestionAnswered)

            if (isGymFitnessGoalQuestionAnswered) {
              const preSubmitQuestionConfig = questionOptions.find((obj: { id: any }) => isGymUser && obj.id == ((Object)(user_attributes.attributes)[userGymFitnessGoalKey]))
              this.logger.info("userYearlyFitnessAttribute GYM ANSWER isGymFitnessGoalQuestionAnswered in side if - ", preSubmitQuestionConfig, " - ", ((Object)(user_attributes.attributes)[userGymFitnessGoalKey]))
              if (!_.isNil(preSubmitQuestionConfig)) {
                configVariables.CHARACTER_CARD_VALUE_D = preSubmitQuestionConfig.focusedOn ?? configVariables.CHARACTER_CARD_VALUE_D
                configVariables.FAV_FORMAT = preSubmitQuestionConfig.mapToFormat
                configVariables.YER_CHARACTER_ASSIGNED = characterRevealPageConfig["characterConfig"][preSubmitQuestionConfig.mapToFormat.toUpperCase()] ?
                  characterRevealPageConfig["characterConfig"][preSubmitQuestionConfig.mapToFormat.toUpperCase()]["characterName"] : configVariables.YER_CHARACTER_ASSIGNED
                configVariables.CHARACTER_INFO = characterRevealPageConfig["characterConfig"][preSubmitQuestionConfig.mapToFormat.toUpperCase()] ?
                  characterRevealPageConfig["characterConfig"][preSubmitQuestionConfig.mapToFormat.toUpperCase()]["characterInfo"] : configVariables.CHARACTER_INFO
                this.logger.info("userYearlyFitnessAttribute GYM ANSWER isGymFitnessGoalQuestionAnswered in side if 2 - ", configVariables.YER_CHARACTER_ASSIGNED)
              }
            }
            else if (answerId && questionConfig) {
              configVariables.FAV_FORMAT = questionConfig.mapToFormat
              configVariables.CHARACTER_CARD_VALUE_D = questionConfig.focusedOn ?? configVariables.CHARACTER_CARD_VALUE_D
            }

            if (answerId && questionConfig) {
                await this.userAttributeClient.setUserAttributes({
                    userId: Number(userContext.userProfile.userId),
                    attribute: userGymFitnessGoalKey,
                    attrValue: questionConfig.id,
                    namespace: "GLOBAL",
                    description: "user gym fitness goal",
                    dataType: "STRING",
                    occuredAt: new Date().getTime()
                })
                isGymFitnessGoalQuestionAnswered = true
                configVariables.CHARACTER_CARD_VALUE_D = questionConfig.focusedOn ?? configVariables.CHARACTER_CARD_VALUE_D
            }
        }

      // format count
      const keysToConsider = [
        "DANCE_FORMAT_CLASSES",
        "SNC_FORMAT_CLASSES",
        "HRX_FORMAT_CLASSES",
        "BURN_FORMAT_CLASSES",
        "YOGA_FORMAT_CLASSES",
        "BOXING_FORMAT_CLASSES",
        "BADMINTON_FORMAT_CLASSES",
        "SWIMMING_FORMAT_CLASSES",
        "GYM_FORMAT_CLASSES"
      ]

      let filteredFormatCounts = keysToConsider
        .map((key) => userYearlyFitnessAttributeMap[key])
        .filter((value) => value !== null && value !== undefined && value != "0")

      filteredFormatCounts = filteredFormatCounts.map(str => parseInt(str))

      this.logger.info("userYearlyFitnessAttribute format filteredFormatCounts - ", filteredFormatCounts)

      const maxFormatValue = parseInt(userYearlyFitnessAttributeMap["FAVOURITE_FORMAT_CLASSES"] ?? 0) // Math.max(...filteredFormatCounts)
      const sumFormat = filteredFormatCounts.reduce((acc, value) => acc + value, 0)

      const percentageMaxFormat = (maxFormatValue / sumFormat) * 100

      this.logger.info("userYearlyFitnessAttribute format percentage - ", maxFormatValue, sumFormat, percentageMaxFormat)

      if (percentageMaxFormat < 35) {
        configVariables.FAV_FORMAT = "HYBRID"
        configVariables.YER_CHARACTER_ASSIGNED = characterRevealPageConfig["characterConfig"]["HYBRID"]["characterName"]
        configVariables.CHARACTER_CARD_VALUE_D = characterRevealPageConfig["characterConfig"]["HYBRID"]["focusedOn"]
        configVariables.CHARACTER_INFO = characterRevealPageConfig["characterConfig"]["HYBRID"]["characterInfo"]
      }

      configVariables.CHARACTER_INFO = configVariables.CHARACTER_INFO.replace("<NAME>", configVariables.USER_NAME)

      const filteredMap: any = {}
      const userYearlyFitnessAttributeMapKeys = Object.keys(userYearlyFitnessAttributeMap)

      for (let i = 0; i < userYearlyFitnessAttributeMapKeys.length; i++) {
        const attrKey = userYearlyFitnessAttributeMapKeys[i]
        if (
          userYearlyFitnessAttributeMap[attrKey] != null &&
          userYearlyFitnessAttributeMap[attrKey] != undefined &&
          userYearlyFitnessAttributeMap[attrKey] != 0 &&
          userYearlyFitnessAttributeMap[attrKey] != "0" &&
          keysToConsider.includes(attrKey) &&
          !isNaN(parseInt(userYearlyFitnessAttributeMap[attrKey]))
        ) {
          const attrValue = parseInt(userYearlyFitnessAttributeMap[attrKey])
          filteredMap[attrKey] = attrValue
        }
      }
      this.logger.info("userYearlyFitnessAttribute multi format -1 - ", filteredMap)

      // Top 3 formats of user
      try {
        if (filteredMap && Object.keys(filteredMap).length) {
          const sortedPairs = Object.entries(filteredMap)
            .sort((a: any, b: any) => b[1] - a[1])
            .slice(0, 2)

          const topFormats = Object.fromEntries(sortedPairs)
          this.logger.info("userYearlyFitnessAttribute multi format -1 - ", topFormats)
          const topFormatKeys = Object.keys(topFormats)
          this.logger.info("userYearlyFitnessAttribute multi format 0 - ", topFormatKeys)
          if (topFormats && topFormatKeys.length) {
            for (const [index, key] of topFormatKeys.entries()) {
                let formatName = key.split("_")[0].trim().toUpperCase()
                this.logger.info("userYearlyFitnessAttribute multi format 1 - ", formatName)
                formatName = characterRevealPageConfig["favFormatMap"][formatName.toUpperCase()]
                this.logger.info("userYearlyFitnessAttribute multi format 2 - ", formatName)
                const displayName = characterRevealPageConfig["characterConfig"][formatName.toUpperCase()]["formatDisplayName"]
                this.logger.info("userYearlyFitnessAttribute multi format 3 - ", displayName)

                configVariables.FORMAT_LEVEL_COUNT_TEXT +=  `${topFormats[key]} ` + displayName

                if (index === topFormatKeys.length - 2) {
                    configVariables.FORMAT_LEVEL_COUNT_TEXT += " & "
                } else if (index !== topFormatKeys.length - 1) {
                    configVariables.FORMAT_LEVEL_COUNT_TEXT += ", "
                }
                else if (index === topFormatKeys.length - 1) {
                  // configVariables.FORMAT_LEVEL_COUNT_TEXT += (topFormatKeys.length === 1 && !_.isNil(filteredMap[key]) && filteredMap[key] === 1) ? " time" : "  times"
                }
                this.logger.info("userYearlyFitnessAttribute multi format 4 - ", configVariables.FORMAT_LEVEL_COUNT_TEXT)
            }
          }
        }
      }
      catch (err) {
        configVariables.FORMAT_LEVEL_COUNT_TEXT = ""
        this.logger.error(err)
      }
      this.logger.info("userYearlyFitnessAttribute multi format 5 - ", configVariables.FORMAT_LEVEL_COUNT_TEXT)

        // if (isGymUser) {
        //   // for testing
        //   configVariables.FAV_FORMAT = "HYBRID"
        // }

        let response = UserYearReport2023Business.processConfigJSON(characterRevealPageConfig["responseConfig"], configVariables)

        this.logger.info("userYearlyFitnessAttribute filter attributes - ", configVariables.FAVOURITE_TRAINER, "-", configVariables.FAVOURITE_TRAINER_CLASSES, "-", configVariables.TOP_PERCENTILE, "-", configVariables.FAVOURITE_TRAINER_CLASSES == 0)

        const rmPages = []
        if (_.isNil(configVariables.FITNESS_COMEBACKS) || configVariables.FITNESS_COMEBACKS < 3) {
          rmPages.push("comebacks")
        }
        if (_.isNil(configVariables.FAVOURITE_TRAINER) || _.isNil(configVariables.FAVOURITE_TRAINER_CLASSES) || !configVariables.FAVOURITE_TRAINER.length || configVariables.FAVOURITE_TRAINER_CLASSES < 10) {
          rmPages.push("fav_trainer")
        }
        if (_.isNil(configVariables.WEEKEND_ACTIVITY_COUNT) || configVariables.WEEKEND_ACTIVITY_COUNT == 0) {
          rmPages.push("weekend_classes")
        }
        if (_.isNil(configVariables.TOP_PERCENTILE) || configVariables.TOP_PERCENTILE == 0 || configVariables.TOP_PERCENTILE > 30) {
          rmPages.push("top_athlete_india")
        }
        if (configVariables.UNIQUE_WORKOUT_FORMATS_COUNT < 2 || !configVariables.FORMAT_LEVEL_COUNT_TEXT.length) {
          rmPages.push("fav_formats")
        }
        this.logger.info("userYearlyFitnessAttribute filter attributes rmPages - ", rmPages)
        response = removePagesByIds(response, rmPages)

        this.logger.info("check filter cond 1 - ", isGymUser, "-", answerId, "-", _.isNil(answerId), "-", isGymFitnessGoalQuestionAnswered)
        if (isGymUser) {
          if (answerId) {
            this.logger.info("check filter cond 2 - ", isGymUser, "-", answerId)
            delete response["welcomeScreen"]
            delete response["question"]
          }
          else if (_.isNil(answerId) && !isGymFitnessGoalQuestionAnswered) {
            this.logger.info("check filter cond 3 - ", isGymUser, "-", answerId)
            // delete response["revealLotties"]
            delete response["pages"]
          }
          else if (isGymFitnessGoalQuestionAnswered) {
            this.logger.info("check filter cond 4 - ", isGymUser, "-", answerId)
            delete response["question"]
          }
        }
        else {
          this.logger.info("check filter cond 5 - ", isGymUser, "-", answerId)
          delete response["question"]
        }

        return response
    }

    private static processConfigJSON(json: any, configVariables: Record<string, any>): any {
      if (typeof json === "string") {
        let replacedString = json
        for (const variable in configVariables) {
          if (configVariables.hasOwnProperty(variable)) {
            if (replacedString.includes(variable)) {
                // if(configVariables[variable])
                    replacedString = replacedString.replace(new RegExp(variable, "g"), configVariables[variable])
                // else
                // return null
            }
          }
        }
        return replacedString
      } else if (Array.isArray(json)) {
        return json.map((item) => this.processConfigJSON(item, configVariables))
      } else if (typeof json === "object" && json !== null) {
        const result: Record<string, any> = {}
        for (const key in json) {
          if (json.hasOwnProperty(key)) {
            const processedKey = this.processConfigJSON(key, configVariables)
            const processedValue = this.processConfigJSON(json[key], configVariables)
            if (processedValue !== null && processedValue !== undefined) {
              result[processedKey] = processedValue
            }
          }
        }
        return result
      } else {
        return json
    }
  }
}