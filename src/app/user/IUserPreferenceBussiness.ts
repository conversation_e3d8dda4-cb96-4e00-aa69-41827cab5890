
import {
    UserPreference
} from "@curefit/user-common"
import {
    VerticalType
} from "@curefit/location-common"
import {
    Session, UserContext,
} from "@curefit/userinfo-common"

interface IUserPreferenceBussiness {
    getUserPreference(userContext: UserContext): Promise<UserPreference>
    createOrUpdateUserPreference(userContext: UserContext, userPreference: UserPreference): Promise<UserPreference>
    updateSelectedVerticals(selectedVerticals: VerticalType[], userContext: UserContext): Promise<UserPreference>
}

export default IUserPreferenceBussiness
