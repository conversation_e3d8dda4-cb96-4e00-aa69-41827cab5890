export interface UserYERCharacterRevealReport {
    user_id: string
    class_attend: number
    time_spent: number
    calories: number
    Weekend_at_cult?: number
    streak?: number
    first_class?: string
    favorate_format_1?: string
    favorate_format_1_classes?: number
    favorate_format_2?: string
    favorate_format_2_classes?: number
    favorate_format_3?: string
    favorate_format_3_classes?: number
    Better_than_users?: string
    best_month?: string
    days_worked_out?: number
    wod_song?: string
    wod_class_song_time?: number
    Ranking_type?: string
    start_off_year?: string
    days_streak?: number
    total_pack_length?: number
    total_memberships?: number
    total_workout_days?: number
    classes_best_month?: number
    song_classes?: number
    wod?: string
    wod_classes?: number
    best_month_date?: string
    comebacks?: number
    trainername?: string
    trainer_classes?: number
    formats?: number
    GX_Classes?: number
    GYM_Classes?: number
    Live_Classes?: number
    PLAY_Classes?: number
}