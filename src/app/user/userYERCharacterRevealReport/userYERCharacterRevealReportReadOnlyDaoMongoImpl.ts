import { inject, injectable } from "inversify"
import { MongoReadonlyDao } from "@curefit/mongo-utils"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { UserYERCharacterRevealReportModel } from "./userYERCharacterRevealReportModel"
import { UserYERCharacterRevealReport } from "./userYERCharacterRevealReport"
import { IUserYERCharacterRevealReportReadOnlyDao } from "./userYERCharacterRevealReportDao"
import { UserYERCharacterRevealReportSchema } from "./userYERCharacterRevealReportSchema"

@injectable()
export class UserYERCharacterRevealReportReadOnlyDaoMongoImpl extends MongoReadonlyDao<UserYERCharacterRevealReportModel, UserYER<PERSON>haracterRevealReport> implements IUserYERCharacterRevealReportReadOnlyDao {
    constructor(
        @inject(CUREFIT_API_TYPES.UserYERCharacterRevealReportSchema) userYERCharacterRevealReportSchema: UserYERCharacterRevealReportSchema,
        @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
        super(userYERCharacterRevealReportSchema.mongooseModel, logger, userYERCharacterRevealReportSchema.isLeanQueryEnabled)
    }
}
