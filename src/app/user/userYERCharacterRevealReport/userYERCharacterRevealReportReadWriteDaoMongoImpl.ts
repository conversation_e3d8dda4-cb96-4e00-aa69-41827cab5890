import { inject, injectable } from "inversify"
import { MongoReadWriteDao } from "@curefit/mongo-utils"
import { BASE_TYPES, ILogger } from "@curefit/base"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { UserYERCharacterRevealReportReadOnlyDaoMongoImpl } from "./userYERCharacterRevealReportReadOnlyDaoMongoImpl"
import { UserYERCharacterRevealReportModel } from "./userYERCharacterRevealReportModel"
import { UserYERCharacterRevealReport } from "./userYERCharacterRevealReport"
import { IUserYERCharacterRevealReportReadWriteDao } from "./userYERCharacterRevealReportDao"
import { UserYERCharacterRevealReportSchema } from "./userYERCharacterRevealReportSchema"

@injectable()
export class UserYERCharacterRevealReportReadWriteDaoMongoImpl extends MongoReadWriteDao<UserYERCharacterRevealReportModel, UserYERCharacterRevealReport> implements IUserYERCharacterRevealReportReadWriteDao {
    constructor(
        @inject(CUREFIT_API_TYPES.UserYERCharacterRevealReportSchema) userYERCharacterRevealReportSchema: UserYERCharacterRevealReportSchema,
        @inject(CUREFIT_API_TYPES.UserYERCharacterRevealReportReadOnlyDaoMongoImpl) readonlyDao: UserYERCharacterRevealReportReadOnlyDaoMongoImpl,
        @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
        super(userYERCharacterRevealReportSchema.mongooseModel, readonlyDao, logger)
    }
}
