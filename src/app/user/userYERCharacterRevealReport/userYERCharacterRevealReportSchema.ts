import { MONG<PERSON>_TYPES, MultiMongooseAccess, MultiMongooseSchema } from "@curefit/mongo-utils"
import { inject } from "inversify"
import { UserYERCharacterRevealReportModel } from "./userYERCharacterRevealReportModel"
import { ReadPreference } from "mongodb"

export class User<PERSON>ERCharacterRevealReportSchema extends MultiMongooseSchema<UserYERCharacterRevealReportModel> {

    constructor(
        @inject(MONGO_TYPES.MultiMongooseAccess) mongooseAccess: MultiMongooseAccess
    ) {
        super(mongooseAccess, "userYERCharacterRevealReport", "DEFAULT", ReadPreference.SECONDARY_PREFERRED)
    }

    protected schema() {
        return {
            user_id: {type: String, unique: true},
            class_attend: {type: Number, required: true},
            time_spent: {type: Number, required: true},
            calories: {type: Number, required: true},
            Weekend_at_cult: {type: Number, required: false},
            streak: {type: Number, required: false},
            first_class: {type: String, required: false},
            favorate_format_1: {type: String, required: false},
            favorate_format_1_classes: {type: Number, required: false},
            favorate_format_2: {type: String, required: false},
            favorate_format_2_classes: {type: Number, required: false},
            favorate_format_3: {type: String, required: false},
            favorate_format_3_classes: {type: Number, required: false},
            Better_than_users: {type: String, required: false},
            best_month: {type: String, required: false},
            days_worked_out: {type: Number, required: false},
            wod_song: {type: String, required: false},
            wod_class_song_time: {type: Number, required: false},
            Ranking_type: {type: String, required: false},
        }
    }

}
