import { Container, inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CultBooking, CultMembership } from "@curefit/cult-common"
import { ICultServiceOld as ICultService, CULT_CLIENT_TYPES } from "@curefit/cult-client"
import { ICatalogueService, CATALOG_CLIENT_TYPES } from "@curefit/catalog-client"
import * as _ from "lodash"
import ICRMIssueService from "../crm/ICRMIssueService"
import { SleepActivity, WalkActivity } from "@curefit/atlas-client"
import { IProgramBusiness } from "../program/IProgramBusiness"
import ITimelineBusiness, { ACTIVITY_PRIORITY_MAP, TimelineRequestParams } from "./ITimelineBusiness"
import { ActivityHeaderWidget, ActivityWidget, Score, TimelineView, TimelineViewV1, UserActivity } from "./TimelineView"
import {
    Activity,
} from "@curefit/logging-common"

import {
    ActivityState,
} from "@curefit/logging-common"
import {
    CustomerIssueType,
} from "@curefit/issue-common"
import {
    FitbitUserInfo,
} from "@curefit/userinfo-common"
import {
    HourMin,
} from "@curefit/base-common"
import {
    ProductType,
} from "@curefit/product-common"
import {
    FoodPack,
    FoodProduct as Product,
} from "@curefit/eat-common"
import {
    UserAgentType as UserAgent
} from "@curefit/base-common"
import {
    User,
} from "@curefit/user-common"
import { eternalPromise } from "@curefit/util-common"
import { Logger, BASE_TYPES } from "@curefit/base"
import CultUtil, { RUNNING_EVENT_WORKOUT_ID } from "../util/CultUtil"
import { ActionUtilV1, RENEWAL_WINDOW_DAYS, SeoUrlParams } from "@curefit/base-utils"
import { capitalizeFirstLetter } from "@curefit/util-common"
import { UrlPathBuilder } from "@curefit/product-common"
import { SlotUtil } from "@curefit/eat-util"
import IProductBusiness from "../product/IProductBusiness"
import {
    BookingDetail,
    IHealthfaceService,
    RecommendedTimelineActivity,
    TimelineActivity,
    TimelineResponse, ALBUS_CLIENT_TYPES
} from "@curefit/albus-client"
import { IOfferServiceEther as IOfferService } from "@curefit/offer-service-client"
import {
    Action,
    ActionList,
    EmptyStateWidget,
    ManageOptionPayload,
    ManageOptions,
    WidgetView
} from "../common/views/WidgetView"
import * as momentTz from "moment-timezone"
import { IActivityStoreReadonlyDao, LOGGING_MODELS_TYPES } from "@curefit/logging-models"
import { IFindQuery } from "@curefit/mongo-utils"
import { ActivityDS } from "@curefit/logging-common"
import TimelineUtil from "../util/TimelineUtil"
import OrderTrackingStatusWidget from "../common/views/OrderTrackingStatusWidget"
import { ActionUtil as EtherActionUtil } from "@curefit/base-utils"
import { ActionUtil } from "../util/ActionUtil"
import { IQuestService, QUEST_CLIENT_TYPES } from "@curefit/quest-client"
import { UserActivityDetails } from "@curefit/quest-common"
import { IUserService } from "@curefit/user-client"
import AppUtil, { INAPP_NOTIFICATION_APPID } from "../util/AppUtil"
import { InAppNotificationData } from "@curefit/iris-common"
import { IDIYFulfilmentService, DIY_CLIENT_TYPES } from "@curefit/diy-client"
import { DIYPack } from "@curefit/diy-common"
import { CareUtil } from "../util/CareUtil"
import { SessionInfo, UserContext } from "@curefit/userinfo-common"
import { INotification, INotificationWidget } from "../common/views/NotificationWidget"
import { CacheHelper } from "../util/CacheHelper"
import { TimeUtil } from "@curefit/util-common"
import { OFFER_SERVICE_CLIENT_TYPES } from "@curefit/offer-service-client"
import { USER_CLIENT_TYPES } from "@curefit/user-client"
import { AtlasActivityService } from "../atlas/AtlasActivityService"
import { InAppNotificationsService, IRIS_CLIENT_TYPES } from "@curefit/iris-client"
import { EatSubscriptionUtil } from "../util/EatSubscriptionUtil"

const clone = require("clone")

export const STEPS_GOAL = 10000
export function TimelineBusinessFactoryV1(kernel: Container) {
    @injectable()
    class TimelineBusinessV1 implements ITimelineBusiness {

        constructor(
            @inject(IRIS_CLIENT_TYPES.InAppNotificationsService) protected inAppNotificationService: InAppNotificationsService,
            @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
            @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
            @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
            @inject(CULT_CLIENT_TYPES.MindFitService) private mindFitService: ICultService,
            @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(CUREFIT_API_TYPES.CRMIssueService) private CRMIssueService: ICRMIssueService,
            @inject(CUREFIT_API_TYPES.ProductBusiness) private productBusiness: IProductBusiness,
            @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
            @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceEther) private offerService: IOfferService,
            @inject(QUEST_CLIENT_TYPES.IQuestService) private questService: IQuestService,
            @inject(LOGGING_MODELS_TYPES.ActivityStoreReadonlyDao) private activityDao: IActivityStoreReadonlyDao,
            @inject(CUREFIT_API_TYPES.ProgramBusiness) private programBusiness: IProgramBusiness,
            @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) protected DIYFulfilmentService: IDIYFulfilmentService,
            @inject(CUREFIT_API_TYPES.AtlasActivityService) private atlasActivityService: AtlasActivityService,
        ) {
        }

        async getTimeLine(timelineRequestParams: TimelineRequestParams, userContext: UserContext): Promise<TimelineViewV1> {
            const widgetViews: WidgetView[] = []
            const tz = userContext.userProfile.timezone
            const issuesMap: Map<string, CustomerIssueType[]> = await this.CRMIssueService.getIssuesMap()
            const dateMap: { [date: string]: WidgetView[] } = {}
            const todayDate = TimeUtil.todaysDateWithTimezone(tz)
            const resultsPromise: Promise<{ [date: string]: ActivityWidget[] }>[] = []
            // Fetch the past and / or future activites based on start and end date
            if (timelineRequestParams.endDate) {
                const datesBetween = TimeUtil.datesBetween(tz, timelineRequestParams.startDate, timelineRequestParams.endDate)
                const datesOnOrAfterToday = datesBetween.filter(date => { return date >= todayDate })
                const datesBeforeToday = datesBetween.filter(date => { return date < todayDate })

                if (!_.isEmpty(datesOnOrAfterToday)) {
                    resultsPromise.push(this.todayAndFutureActivites(timelineRequestParams.userAgent, timelineRequestParams.cityId,
                        timelineRequestParams.userId, timelineRequestParams.cultCenterId, timelineRequestParams.mindCenterId, timelineRequestParams.deviceId, issuesMap, datesOnOrAfterToday[0], datesOnOrAfterToday[datesOnOrAfterToday.length - 1], userContext))
                }
                if (!_.isEmpty(datesBeforeToday)) {
                    resultsPromise.push(this.pastActivites(timelineRequestParams.userAgent, timelineRequestParams.cityId,
                        timelineRequestParams.userId, timelineRequestParams.cultCenterId, timelineRequestParams.mindCenterId, timelineRequestParams.deviceId, issuesMap, datesBeforeToday[0], datesBeforeToday[datesBeforeToday.length - 1], userContext))
                }
            } else {
                if (timelineRequestParams.startDate >= todayDate) {
                    resultsPromise.push(this.todayAndFutureActivites(timelineRequestParams.userAgent, timelineRequestParams.cityId,
                        timelineRequestParams.userId, timelineRequestParams.cultCenterId, timelineRequestParams.mindCenterId,
                        timelineRequestParams.deviceId, issuesMap, timelineRequestParams.startDate, timelineRequestParams.startDate, userContext))
                } else {
                    resultsPromise.push(this.pastActivites(timelineRequestParams.userAgent, timelineRequestParams.cityId,
                        timelineRequestParams.userId, timelineRequestParams.cultCenterId, timelineRequestParams.mindCenterId,
                        timelineRequestParams.deviceId, issuesMap, timelineRequestParams.startDate, timelineRequestParams.startDate, userContext))
                }
            }

            // Transform the widgets by sorting , splitting todo and completed activities
            const results: { [date: string]: ActivityWidget[] }[] = await Promise.all(resultsPromise)
            const transformedWidgetsPromise: Promise<{ date: string, widgets: WidgetView[], todoCount?: number }>[] = []
            for (let i = 0; i < results.length; i++) {
                const result = results[i]
                const dates = Object.keys(result)
                for (let j = 0; j < dates.length; j++) {
                    const date = dates[j]
                    const activityWidgets = result[date]
                    transformedWidgetsPromise.push(this.transformWidgets(userContext, activityWidgets, date, todayDate, timelineRequestParams.userId,
                        timelineRequestParams.canAskHealthKit, timelineRequestParams.canAskFitBit, timelineRequestParams.isTodayStepFromServer))
                }
            }
            // Queries the quest service to show the dasboard summary
            const today = TimeUtil.todaysDate(tz)
            const todayMinus30Days = TimeUtil.getMomentNow(tz).subtract(29, "day").format("YYYY-MM-DD")
            const userActivityDetails: UserActivityDetails = (!timelineRequestParams.metrics && !timelineRequestParams.score) ? undefined : await this.questService.getUserDetailedStats(timelineRequestParams.userId, todayMinus30Days, today)

            // Build the date map to widgets from the transformed widget result
            const transformedWidgetsResults = await Promise.all(transformedWidgetsPromise)
            let todoCount = 0
            for (let i = 0; i < transformedWidgetsResults.length; i++) {
                const transformedWidgetsResult = transformedWidgetsResults[i]
                dateMap[transformedWidgetsResult.date] = transformedWidgetsResult.widgets
                if (transformedWidgetsResult.date === todayDate)
                    todoCount = transformedWidgetsResult.todoCount
            }

            let score: Score

            if (userActivityDetails) {
                const isLevelDirty = userActivityDetails.pointsLeftForNextLevel ? false : userActivityDetails.pointsToMaintainCurrentLevel && userActivityDetails.levelValidUntil ? true : false
                // Build the dashboard summary from the user activity detail
                const globalActivity = _.find(userActivityDetails.activitiesBreakUp, activity => {
                    return activity.vertical === "GLOBAL"
                })
                const targetForNextLevel = userActivityDetails.pointsLeftForNextLevel + userActivityDetails.activityPoints
                const currentLevelPoints = userActivityDetails.level.levelId === 0 ? 0 : userActivityDetails.level.activityCount
                const progress = (userActivityDetails.activityPoints - currentLevelPoints) / (targetForNextLevel - currentLevelPoints)

                score = {
                    level: userActivityDetails.level.levelId,
                    activities: userActivityDetails.numActivities,
                    points: userActivityDetails.activityPoints,
                    numDays: 30,
                    progress: parseFloat(progress.toFixed(2)),
                    target: targetForNextLevel,
                    streak: globalActivity.currentStreak,
                    todoCount: todoCount,
                    isLevelDirty: isLevelDirty
                }
            }
            if (!timelineRequestParams.score) score = undefined
            return { dateMap: dateMap, score: score, stepsGoal: STEPS_GOAL, metrics: undefined }
        }

        private async transformWidgets(userContext: UserContext, activityWidgets: ActivityWidget[], activityDate: string, today: string, userId: string,
            canAskHealthKit: boolean, canAskFitbit: boolean, isTodayStepFromServer: boolean): Promise<{ date: string, widgets: WidgetView[], todoCount?: number }> {
            // Sort the activities
            const sortedActivities = activityWidgets.sort(this.compare.bind(this))
            const isSkipWalk = !isTodayStepFromServer && (activityDate === today)
            const hasCompletedStepForToday = _.find(sortedActivities, activity => {
                return activity.activityType === "WALK" && activity.status === "DONE" && activityDate === today
            })
            // Skipping walk from today activity since app adds it already on itself
            const completedActivities = _.filter(sortedActivities, activity => {
                return activity.status === "DONE" && (activity.activityType !== "WALK" || !isSkipWalk)
            })
            const skippedActivities = _.filter(sortedActivities, activity => {
                return activity.status === "SKIPPED" && (activity.activityType !== "WALK" || !isSkipWalk)
            })
            const todoActivities = _.filter(sortedActivities, activity => {
                return activity.status === "TODO" && (activity.activityType !== "WALK" || !isSkipWalk)
            })
            const widgets: WidgetView[] = []

            if (activityDate === today) {
                const notificationWidget = await this.getNotificationWidget(userId, canAskHealthKit, canAskFitbit)
                if (notificationWidget)
                    widgets.push(notificationWidget)
            }
            const isNoActivitiesForDate = _.isEmpty(sortedActivities)
            if (isNoActivitiesForDate) {
                widgets.push(await this.noActivityWidget(userContext, activityDate, today, userId))
            }
            if (!_.isEmpty(todoActivities)) {
                widgets.push(...todoActivities)
            }

            if (!_.isEmpty(completedActivities) || !_.isEmpty(skippedActivities) || hasCompletedStepForToday) {
                const score = await this.scoreOnDate(activityDate, userId)
                let completedCount = completedActivities.length
                if (hasCompletedStepForToday && isSkipWalk)
                    completedCount = completedCount + 1
                const activityHeaderWidget: ActivityHeaderWidget = {
                    title: `${completedCount} completed`,
                    subTitle: activityDate === today ? await this.scoreDropNudgeText(userContext, activityDate, today, userId) : undefined,
                    widgetType: "ACTIVITY_HEADER_WIDGET"
                }
                if (score && score > 0) {
                    activityHeaderWidget.calloutText = `${score} pt`
                }
                if (activityDate <= today)
                    widgets.push(activityHeaderWidget)
                widgets.push(...completedActivities)
                widgets.push(...skippedActivities)
            }
            return { date: activityDate, widgets: widgets, todoCount: todoActivities.length }
        }

        private async noActivityWidget(userContext: UserContext, date: string, today: string, userId: string): Promise<EmptyStateWidget> {
            if (date === today) {
                const widget: EmptyStateWidget = {
                    widgetType: "EMPTY_STATE_WIDGET",
                    image: "/image/today/today_no_activity.png",
                    title: "This is your to-do list!",
                    subTitle: "The healthy activities you schedule show up here as to-dos. Just complete your to-do list everyday to stay healthy and up your score.\n\n Get started, add healthy activities:",
                    action: {
                        title: "EXPLORE CURE.FIT",
                        actionType: "NAVIGATION",
                        url: "curefit://browse"
                    }
                }
                return widget
            } else {
                const widget: EmptyStateWidget = {
                    widgetType: "EMPTY_STATE_WIDGET",
                    title: "Nothing to show",
                    subTitle: await this.scoreDropNudgeText(userContext, date, today, userId),
                }
                return widget
            }
        }

        private async scoreDropNudgeText(userContext: UserContext, date: string, today: string, userId: string): Promise<string> {
            const rolling30DayBefore = TimeUtil.subtractDays(userContext.userProfile.timezone, date, 30)
            const scoreOnRolling30DayBefore = await this.scoreOnDate(rolling30DayBefore, userId)

            let text
            if (scoreOnRolling30DayBefore && scoreOnRolling30DayBefore > 0) {
                if (date === today) {
                    text = `To maintain your score, complete ${scoreOnRolling30DayBefore} activities`
                }
                else if (date < today) {
                    text = `No health activities were scheduled or completed. Your score dropped by ${scoreOnRolling30DayBefore} points on this day.`
                } else {
                    text = `No health activities were scheduled or completed. Your score will drop by ${scoreOnRolling30DayBefore} points on this day.`
                }
            }
            return text
        }

        private healthKitPermissionNotification(): INotification {
            const notification: INotification = {
                notificationId: "HEALTH_KIT_PERMISSION",
                notificationType: "PERMISSION",
                title: "Track steps, sleep and get the full health picture by allowing access to health kit",
                action: { actionType: "HEALTH_KIT_PERMISSION", title: "TAP TO GIVE ACCESS" },
                closeAction: { actionType: "DENIED_HEALTH_KIT_PERMISSION" }
            }
            return notification
        }

        private async fitbitPermissionNotification(userId: string): Promise<INotification> {
            const fitbitPromise = eternalPromise(this.atlasActivityService.getFitbitDataForUser(userId), "fitbit login details")
            const fitbitData: FitbitUserInfo = (await fitbitPromise).obj

            const isfitbitAuthenticated: boolean = !_.isEmpty(fitbitData) && !_.isNil(fitbitData.fitbitToken) && fitbitData.fitbitToken !== "" ? true : false
            const fitbitInfo = {
                authenticated: isfitbitAuthenticated,
                url: isfitbitAuthenticated ? "curefit://fitbit/logout" : AppUtil.fitbitLoginParams(userId)
            }
            if (!fitbitInfo.authenticated) {
                const notification: INotification = {
                    notificationId: "FITBIT_PERMISSION",
                    notificationType: "PERMISSION",
                    title: "Track your steps & sleep by allowing access to fitbit",
                    action: { actionType: "EXTERNAL_DEEP_LINK", title: "LINK YOUR FITBIT ACCOUNT", url: fitbitInfo.url, actionId: "FITBIT_OAUTH" },
                    closeAction: { actionType: "DENIED_FITBIT_PERMISSION" }
                }
                return notification
            }
            return undefined

        }
        private async getNotificationWidget(userId: string, canAskHealthKit: boolean, canAskFitbit: boolean): Promise<INotificationWidget> {
            const notifications: INotification[] = []
            if (canAskHealthKit) {
                notifications.push(this.healthKitPermissionNotification())
            }
            if (canAskFitbit) {
                const notification = await this.fitbitPermissionNotification(userId)
                if (notification)
                    notifications.push(notification)
            }
            const inAppNotifications = await this.inAppNotificationService.getActiveInAppNotificationsForUser(userId, INAPP_NOTIFICATION_APPID)
            const serverNotifications = _.map(inAppNotifications, (inAppNotification) => {
                const inAppNotificationData: InAppNotificationData = JSON.parse(inAppNotification.dataBlob)
                const notification: INotification = {
                    title: inAppNotificationData.title,
                    action: {
                        actionType: "NAVIGATION",
                        url: inAppNotificationData.action.url,
                        title: inAppNotificationData.action.title
                    },
                    closeAction: {
                        actionType: "REST_API",
                        meta: {
                            method: "POST",
                            url: "/user/inAppNotification/" + inAppNotification.notificationId,
                            body: { "state": "CLOSED" }
                        }
                    },
                    notificationId: inAppNotification.notificationId,
                    notificationType: inAppNotification.type
                }
                return notification
            })
            notifications.push(...serverNotifications)
            if (!_.isEmpty(notifications)) {
                const notificationWidget: INotificationWidget = {
                    items: notifications,
                    widgetType: "NOTIFICATION_WIDGET"
                }
                return notificationWidget
            }
            return undefined

        }

        async scoreOnDate(date: string, userId: string): Promise<number> {
            const findQuery: IFindQuery = {
                condition: { "date": { "$gte": date, "$lte": date }, "userId": userId, "show": true }
            }
            const activityStorePromise = eternalPromise(this.activityDao.find(findQuery), "activitystore orders")
            const result = await activityStorePromise
            if (result.obj) {
                const activities = result.obj
                const score = _.sumBy(activities, activity => { return activity.score })
                return score
            } else {
                this.logger.error("Error getting score")
            }
            return undefined
        }

        private compare(a: ActivityWidget, b: ActivityWidget): number {

            if (a.status === "TODO" && b.status !== "TODO") {
                return -1
            }

            if (a.status !== "TODO" && b.status === "TODO") {
                return 1
            }

            if (a.status === "SKIPPED" && b.status !== "SKIPPED") {
                return -1
            }

            if (a.status !== "SKIPPED" && b.status === "SKIPPED") {
                return 1
            }

            const priorityA = ACTIVITY_PRIORITY_MAP.get(a.activityType)
            const priorityB = ACTIVITY_PRIORITY_MAP.get(b.activityType)

            if (priorityA && priorityB) {
                if (priorityA < priorityB)
                    return -1
                else if (priorityA > priorityB) {
                    return 1
                } else {
                    return this.scheduleBasedSortPosition(a, b)
                }
            } else if (priorityA) {
                return -1
            } else if (priorityB) {
                return 1
            } else {
                return this.scheduleBasedSortPosition(a, b)
            }
        }

        private scheduleBasedSortPosition(a: ActivityWidget, b: ActivityWidget) {
            if (a.startTime && b.startTime) {
                return (a.startTime > b.startTime) ? 1 : -1
            }
            if (a.startTime && !b.startTime) {
                return -1
            }
            if (!a.startTime && b.startTime) {
                return 1
            }
            return 0
        }
        async todayAndFutureActivites(userAgent: UserAgent, cityId: string, userId: string, cultCenterId: string,
            mindCenterId: string, deviceId: string, issuesMap: Map<string, CustomerIssueType[]>, startDate: string,
            endDate: string, userContext: UserContext): Promise<{ [date: string]: ActivityWidget[] }> {
            const activityWidgets: ActivityWidget[] = []
            const tz = userContext.userProfile.timezone
            const todayAndFutureActivites: { [date: string]: ActivityWidget[] } = {}
            const dates = TimeUtil.getDaysFrom(tz, startDate, TimeUtil.diffInDays(tz, startDate, endDate) + 1, false)
            dates.forEach(date => { todayAndFutureActivites[date] = [] })

            const activityPromises: Promise<{ obj: ActivityWidget[], err?: any }>[] = []

            activityPromises.push(eternalPromise(this.getCultFitTodayAndFutureActivities(cityId, userContext, cultCenterId, mindCenterId, issuesMap, startDate, endDate), "cult get today"))
            activityPromises.push(eternalPromise(this.getCareFitTodayAndFutureActivities(userContext, userId, issuesMap, startDate, endDate), "care get today"))
            activityPromises.push(eternalPromise(this.getDiyServiceTodayAndFutureActivities(userAgent, userId, deviceId, startDate, endDate, userContext), "diy service get today"))
            activityPromises.push(eternalPromise(this.getCultScoreActivities(userAgent, userId, deviceId, dates), "cult score get today"))

            const today = TimeUtil.todaysDateWithTimezone(tz)
            if (startDate <= today && TimeUtil.todaysDateWithTimezone(tz) <= today) {
                activityPromises.push(eternalPromise(this.getSleepAndStepsActivityForToday(userContext, userAgent, userId, deviceId, today), "get today sleep"))
            }

            const todayAndFutureActivitiesResult = await Promise.all(activityPromises)

            todayAndFutureActivitiesResult.forEach(todayAndFutureActivities => {
                if (todayAndFutureActivities.obj) {
                    todayAndFutureActivities.obj.forEach(todayAndFutureActivity => {
                        if (todayAndFutureActivity.date >= startDate && todayAndFutureActivity.date <= endDate)
                            todayAndFutureActivites[todayAndFutureActivity.date].push(todayAndFutureActivity)
                    })
                }
                else {
                    this.logger.error("todayAndFutureActivites call failed for user " + userId + " " + JSON.stringify(todayAndFutureActivities.err))
                }
            })
            return todayAndFutureActivites
        }

        async pastActivites(userAgent: UserAgent, cityId: string, userId: string, cultCenterId: string,
            mindCenterId: string, deviceId: string, issuesMap: Map<string, CustomerIssueType[]>, startDate: string,
            endDate: string, userContext: UserContext): Promise<{ [date: string]: ActivityWidget[] }> {
            const tz = userContext.userProfile.timezone
            const activityWidgets: ActivityWidget[] = []
            const dates = TimeUtil.getDaysFrom(tz, startDate, TimeUtil.diffInDays(tz, startDate, endDate) + 1, false)
            const sleepLoggedDatesMap: Map<string, boolean> = new Map<string, boolean>()
            const pastActivites: { [date: string]: ActivityWidget[] } = {}
            dates.forEach(date => { pastActivites[date] = [] })
            this.logger.info("Into pastActivites for userId  :" + userId + " startdate : " + startDate + " and enddate : " + endDate)
            const findQuery: IFindQuery = {
                condition: { "date": { "$gte": startDate, "$lte": endDate }, "userId": userId, "show": true }
            }
            const activityStorePromise = eternalPromise(this.activityDao.find(findQuery), "activitystore orders")
            const done = await Promise.all([activityStorePromise])
            const activityStoreObjs = (done[0].obj) ? done[0].obj : []

            for (let i = 0; i < done.length; i++) {
                if (done[i].err) {
                    this.logger.error("Error for user " + userId + " : " + done[i].err)
                }
            }

            for (let i = 0; i < activityStoreObjs.length; i++) {
                const activityS = <ActivityDS>clone(activityStoreObjs[i])
                // this.logger.info(JSON.stringify(activityS))
                let cultbooking: CultBooking
                if (activityS.activityType === "CULT_CLASS" || activityS.activityType === "MIND_CLASS") {
                    cultbooking = await this.cultFitService.getBookingById(activityS.meta.fulfilmentId, activityS.userId)
                    // this.logger.info("booking details : " + JSON.stringify(cultbooking))
                    if (cultbooking.label !== "Cancelled") {
                        pastActivites[activityS.date].push(await this.cultActivityWidget(userContext, activityS.activityType === "CULT_CLASS" ? "FITNESS" : "MIND", cultbooking, issuesMap))
                    }
                } else if (activityS.activityType === "SLEEP") {
                    if (activityS.meta.sleep.duration === 0) {
                        // skipping since duration is 0
                        continue
                    }
                    sleepLoggedDatesMap.set(activityS.date, true)
                    pastActivites[activityS.date].push(this.atlasActivityWidget(userContext, userAgent, TimelineUtil.createSleepAtlasActivity(activityS, true)))
                } else if (activityS.activityType === "WALK") {
                    pastActivites[activityS.date].push(this.atlasActivityWidget(userContext, userAgent, TimelineUtil.createStepsAtlasActivity(activityS)))
                } else if (activityS.activityType === "DIY_MEDITATION" || activityS.activityType === "DIY_FITNESS") {
                    const activityWidget = await this.getDiyActivityWidget(userContext, activityS.date, activityS.meta.packId, activityS.productId, "DONE", userId, undefined, userContext.sessionInfo)
                    pastActivites[activityS.date].push(activityWidget)
                } else if (activityS.activityType === "CONSULTATION") {
                    const bookingId: number = Number(activityS.clientActivityId.split("-")[0])
                    const appointmentId: number = Number(activityS.clientActivityId.split("-")[1])
                    const bookingDetail: BookingDetail = await this.healthfaceService.getBookingDetail(bookingId, appointmentId)
                    const title: string = bookingDetail.booking.subCategoryCode === "CF_INCENTRE_CONSULTATION" ? "In-clinic consultation" : "Video Consultation"
                    const timelinActivity: TimelineActivity = {
                        carefitActivity: "CONSULTATION",
                        subCategory: bookingDetail.booking.subCategoryCode,
                        consultationStatus: bookingDetail.consultationOrderResponse.consultationUserState,
                        title: title,
                        description: `With ${bookingDetail.consultationOrderResponse.doctor.name}`,
                        timestamp: bookingDetail.consultationOrderResponse.startTime,
                        bookingInfo: bookingDetail
                    }
                    const activityWidget = await this.careActivityWidget(userContext, userId, timelinActivity, issuesMap)
                    pastActivites[activityS.date].push(activityWidget)
                } else if (activityS.activityType === "AT_HOME_SAMPLE_COLLECTION") {
                    const bookingId: number = Number(activityS.clientActivityId.split("-")[0])
                    const bookingDetail: BookingDetail = await this.healthfaceService.getBookingDetail(bookingId)
                    const timelinActivity: TimelineActivity = {
                        carefitActivity: "DIAGNOSTICS",
                        subCategory: bookingDetail.booking.subCategoryCode,
                        consultationStatus: "COMPLETED",
                        title: "At Home Test",
                        description: `For ${bookingDetail.diagnosticsTestOrderResponse[0].patient.name}`,
                        timestamp: bookingDetail.diagnosticsTestOrderResponse[0].atHomeDiagnosticOrder.startTime,
                        bookingInfo: bookingDetail
                    }
                    const activityWidget = await this.careActivityWidget(userContext, userId, timelinActivity, issuesMap)
                    pastActivites[activityS.date].push(activityWidget)
                } else if (activityS.activityType === "IN_CENTRE_VISIT_FOR_TEST") {
                    const bookingId: number = Number(activityS.clientActivityId.split("-")[0])
                    const bookingDetail: BookingDetail = await this.healthfaceService.getBookingDetail(bookingId)
                    const timelinActivity: TimelineActivity = {
                        carefitActivity: "DIAGNOSTICS",
                        subCategory: bookingDetail.booking.subCategoryCode,
                        consultationStatus: "COMPLETED",
                        title: "At Center Test",
                        description: `For ${bookingDetail.diagnosticsTestOrderResponse[0].patient.name}`,
                        timestamp: bookingDetail.diagnosticsTestOrderResponse[0].inCentreDiagnosticOrder.slot.workingStartTime,
                        bookingInfo: bookingDetail
                    }
                    const activityWidget = await this.careActivityWidget(userContext, userId, timelinActivity, issuesMap)
                    pastActivites[activityS.date].push(activityWidget)
                } else if (activityS.activityType === "CULT_SCORE_DIY") {
                    const activityWidget = await this.cultScoreActivityWidget(activityS)
                    pastActivites[activityS.date].push(activityWidget)
                }
            }

            const today = TimeUtil.todaysDateWithTimezone(tz)
            // Asking sleep log for last 15 days
            await Promise.all(_.map(dates, async date => {
                if (!sleepLoggedDatesMap.get(date) && TimeUtil.diffInDays(tz, date, today) <= 15) {
                    pastActivites[date].push(await this.sleepActivityWidget(userContext, undefined, userAgent, date))
                }
            }))

            return pastActivites
        }

        // Returns both cult and mind classes
        private async getCultFitTodayAndFutureActivities(cityId: string, userContext: UserContext, cultCenterId: string,
            mindCenterId: string, issuesMap: Map<string, CustomerIssueType[]>, startDate: string, endDate: string):
            Promise<ActivityWidget[]> {
            const userId = userContext.userProfile.userId
            const tz = userContext.userProfile.timezone
            const todayAndFutureActivities: ActivityWidget[] = []
            const today: string = TimeUtil.todaysDateWithTimezone(tz)
            const cultBookingsPromise = this.cultFitService.bookingsV2(userId, endDate, startDate)
            const mindBookingsPromise = this.mindFitService.bookingsV2(userId, endDate, startDate)
            const cultBulkBookingsResponse = await cultBookingsPromise
            const mindBulkBookingsResponse = await mindBookingsPromise
            const cultBookingsResponse = cultBulkBookingsResponse[userId]
            const mindBookingsResponse = mindBulkBookingsResponse[userId]
            const cultBookings = cultBookingsResponse.bookings
            const mindBookings = mindBookingsResponse.bookings
            if (cultBookings && cultBookings.length > 0) {
                for (let i = 0; i < cultBookings.length; i++) {
                    const booking = cultBookings[i]
                    if (booking.label !== "Cancelled") {
                        const activityWidget: ActivityWidget = await this.cultActivityWidget(userContext, "FITNESS", booking, issuesMap)
                        todayAndFutureActivities.push(activityWidget)
                    }
                }
            }
            if (mindBookings && mindBookings.length > 0) {
                for (let i = 0; i < mindBookings.length; i++) {
                    const booking = mindBookings[i]
                    if (booking.label !== "Cancelled") {
                        const activityWidget: ActivityWidget = await this.cultActivityWidget(userContext, "MIND", booking, issuesMap)
                        todayAndFutureActivities.push(activityWidget)
                    }
                }
            }

            return todayAndFutureActivities
        }

        private async getCareFitTodayAndFutureActivities(userContext: UserContext, userId: string, issuesMap: Map<string,
            CustomerIssueType[]>, startDate: string, endDate: string):
            Promise<ActivityWidget[]> {
            const tz = userContext.userProfile.timezone
            const todayAndFutureActivities: ActivityWidget[] = []
            const careTimelineActivities: TimelineResponse = await this.healthfaceService.getTimelineActivities(userContext, userId,
                TimeUtil.getDate(startDate, 0, 0, tz).getTime(), TimeUtil.diffInDays(tz, startDate, endDate) + 1)
            if (careTimelineActivities && careTimelineActivities.timelineActivities && careTimelineActivities.timelineActivities.length > 0) {
                for (let i = 0; i < careTimelineActivities.timelineActivities.length; i++) {
                    const activityWidget: ActivityWidget = await this.careActivityWidget(userContext, userId, careTimelineActivities.timelineActivities[i], issuesMap)
                    todayAndFutureActivities.push(activityWidget)
                }
            }
            return todayAndFutureActivities
        }

        private async careActivityRecommendedActivityWidget(userContext: UserContext, activity: RecommendedTimelineActivity, issuesMap: Map<string, CustomerIssueType[]>): Promise<ActivityWidget> {
            const status: ActivityState = "TODO"
            const tz = userContext.userProfile.timezone
            const momentTime = TimeUtil.getMomentNow(tz)
            const userActivityWidget: ActivityWidget = {
                userActivityId: activity.bookingInfo.booking.id.toString(),
                title: activity.title,
                bullets: activity.subTaskList ? activity.subTaskList.map(val => val.title) : undefined,
                activityType: activity.careFitRecommendedActivityType,
                subTitle: activity.description,
                action: ActionUtil.getCareRecommendedActivityAction(userContext, activity, this.logger),
                meta: {
                    chatActive: CareUtil.getChatEnabled(activity.bookingInfo)
                },
                calloutText: "", // momentTime.format("LT"),
                startTime: momentTime.format("HH:mm:ss"),
                closedSection: {
                    title: activity.title,
                    subTitle: activity.description,
                },
                date: TimeUtil.formatDateInTimeZone(tz, new Date()),
                images: activity.careFitRecommendedActivityType !== "VIEW_PRESCRIPTION" ? [activity.timelineImageUrl] : undefined,
                status: status,
                quickActions: [],
                widgetType: "ACTIVITY_WIDGET",
                subActivities: [
                    {
                        viewType: "BULLET_ITEMS",
                        list: activity.subTaskList.map(val => val.title)
                    }
                ]
            }
            const actions: Action[] = ActionUtil.getCareRecommendedActivityQuickActions(userContext, activity, issuesMap, this.productBusiness, this.logger)
            if (actions) {
                actions.forEach(action => {
                    userActivityWidget.quickActions.push(action)
                })
            }
            return userActivityWidget
        }

        private async careActivityWidget(userContext: UserContext, userId: string, activity: TimelineActivity,
            issuesMap: Map<string, CustomerIssueType[]>): Promise<ActivityWidget> {
            const tz = userContext.userProfile.timezone
            const isMissed: boolean = !_.isEmpty(activity.bookingInfo.consultationOrderResponse) && activity.bookingInfo.consultationOrderResponse.status === "MISSED"
            const status: ActivityState = TimelineUtil.getCareStatus(activity, isMissed)
            const momentTime = momentTz.tz(activity.timestamp, tz)
            const parsedDate = TimeUtil.parseDateFromEpoch(activity.timestamp)
            const parsedDateMoment = TimeUtil.getMomentForDate(parsedDate, tz)
            const startTime = TimeUtil.getTimeIn12HRFormat(TimeUtil.formatDateInTimeZone(tz, parsedDate), parsedDateMoment.hours(), parsedDateMoment.minutes(), true, tz)
            const user: User = await this.userCache.getUser(userId)
            const userActivityWidget: ActivityWidget = {
                userActivityId: activity.bookingInfo.booking.id.toString(),
                title: activity.title,
                activityType: activity.carefitActivity,
                subTitle: activity.description,
                action: await ActionUtil.getCareActivityAction(userContext, activity, this.healthfaceService, this.logger),
                meta: {
                    chatActive: CareUtil.getChatEnabled(activity.bookingInfo)
                },
                calloutText: startTime, // momentTime.format("LT"),
                startTime: momentTime.format("HH:mm:ss"),
                closedSection: {
                    title: activity.title,
                    subTitle: activity.description,
                },
                date: TimeUtil.formatDateInTimeZone(tz, new Date(activity.timestamp)),
                images: [activity.timelineImageUrl], // ["/image/banners/care/timeline_banner.jpg"],
                status: status,
                quickActions: activity.bookingInfo.booking.subCategoryCode === "CF_INCENTRE_CONSULTATION" ? [ActionUtil.centerNavigationAction(activity.bookingInfo.consultationOrderResponse.center.placeUrl)] : [],
                widgetType: "ACTIVITY_WIDGET",
                subActivities: [
                    {
                        viewType: "IMAGE_CARD",
                        list: ["/image/banners/care/timeline_banner.jpg"]
                    }
                ]
            }
            const actions: Action[] = ActionUtil.getCareQuickActions(userContext, activity, issuesMap, user, this.productBusiness, this.logger)
            if (actions) {
                const smartActions: Action[] = TimelineUtil.createSmartManageOptionActions(actions)
                smartActions.forEach(action => {
                    userActivityWidget.quickActions.push(action)
                })
            }
            return userActivityWidget
        }

        private async cultActivityWidget(userContext: UserContext, productType: ProductType, booking: CultBooking,
            issuesMap: Map<string, CustomerIssueType[]>): Promise<ActivityWidget> {
            const tz = userContext.userProfile.timezone
            const status: ActivityState = TimelineUtil.getCultStatus(booking)
            const document = _.find(booking.Class.Workout.documents, document => {
                return document.tagID === 11
            })
            const isRunningEvent = booking.Class.Workout.id === RUNNING_EVENT_WORKOUT_ID
            const isCompletedClass = booking.label === "Completed"
            const showNavigationAction = !(isRunningEvent || isCompletedClass)
            const hour = TimeUtil.getMomentForDateString(booking.Class.startTime, tz, "HH:mm:ss").format("HH")
            const minute = TimeUtil.getMomentForDateString(booking.Class.startTime, tz, "HH:mm:ss").format("mm")
            const startTime = TimeUtil.getTimeIn12HRFormat(TimeUtil.formatDateInTimeZone(tz, new Date()), +hour, +minute, true, tz)
            const userActivityWidget: ActivityWidget = {
                userActivityId: booking.Class.id.toString(),
                title: booking.Class.Workout.name,
                activityType: "CULT_CLASS",
                subTitle: booking.Center.name,
                action: {
                    actionType: "NAVIGATION",
                    url: productType === "FITNESS" ? `curefit://cultclass?bookingNumber=${booking.bookingNumber}` : `curefit://mindclass?bookingNumber=${booking.bookingNumber}`
                },
                calloutText: startTime,
                startTime: booking.Class.startTime,
                closedSection: {
                    title: booking.Class.Workout.name + " at " + booking.Center.name
                },
                date: booking.Class.date,
                images: document ? ["/" + document.URL] : undefined,
                status: status,
                quickActions: showNavigationAction ? [ActionUtil.centerNavigationAction(booking.Center.placeUrl)] : [],
                widgetType: "ACTIVITY_WIDGET",
                subActivities: [
                    {
                        viewType: "IMAGE_CARD",
                        list: document ? ["/" + document.URL] : undefined
                    }
                ]
            }

            const cultManageOptions = await this.productBusiness.getCultManageOptions(userContext, productType, booking, issuesMap, true, true)
            const enabledOptions = cultManageOptions.manageOptions.options.filter((option: ManageOptionPayload) => {
                return option.isEnabled
            })
            if (enabledOptions.length > 0) {
                const cancelClassAction = await ActionUtil.cancelClassAction(enabledOptions, userContext)
                if (cancelClassAction)
                    userActivityWidget.quickActions.push(cancelClassAction)
                const cultScoreLogAction = ActionUtil.cultScoreLogAction(enabledOptions)
                if (cultScoreLogAction) {
                    // Setting status to TODO until user has logged the score for cult score
                    userActivityWidget.status = "TODO"
                    userActivityWidget.quickActions.push(cultScoreLogAction)
                }
            }

            return userActivityWidget
        }

        private reportIssueManageOptions(options: ManageOptionPayload[]): ManageOptions {
            const reportIssueOption = _.find(options, manageOption => {
                return manageOption.type === "REPORT_ISSUE"
            })
            if (reportIssueOption) {
                const reportIssueOptionManageOption: ManageOptions = {
                    displayText: "Need Help",
                    icon: "REPORT_ISSUE",
                    options: [reportIssueOption]
                }
                return reportIssueOptionManageOption
            } else {
                return undefined
            }
        }

        private modifiedStartTime(startTime: HourMin): HourMin {
            if (startTime.min + 25 >= 60) {
                return {
                    hour: startTime.hour + 1,
                    min: startTime.min + 25 - 60
                }
            } else {
                return {
                    hour: startTime.hour,
                    min: startTime.min + 25
                }
            }
        }

        private async getDiyServiceTodayAndFutureActivities(userAgent: UserAgent, userId: string, deviceId: string, startDate: string,
            endDate: string, userContext: UserContext): Promise<ActivityWidget[]> {
            const promises: Promise<ActivityWidget>[] = []
            const tenant = AppUtil.getTenantFromUserContext(userContext)
            const diyUserDays = await this.DIYFulfilmentService.getDIYCalendarForUser(userId, tenant, startDate, endDate, false)
            for (let i = 0; i < diyUserDays.length; i++) {
                const diyUserDay = diyUserDays[i]
                const date = TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, diyUserDay.date)
                const parsedDate = new Date(diyUserDay.date)
                const preferredTime = diyUserDay.preferredTime
                promises.push(this.getDiyActivityWidget(userContext, date, diyUserDay.packId, diyUserDay.productId, diyUserDay.status, userId, preferredTime, userContext.sessionInfo))
            }
            const diyActivities: ActivityWidget[] = await Promise.all(promises)
            return diyActivities
        }

        private async getDiyActivityWidget(userContext: UserContext, date: string, packId: string, productId: string, status: ActivityState,
            userId: string, preferredTime: HourMin, sessionInfo: SessionInfo): Promise<ActivityWidget> {
            const pack = <DIYPack>await this.catalogueService.getProduct(packId)
            const tz = userContext.userProfile.timezone
            const packType = pack.productType
            const tenant = AppUtil.getTenantFromUserContext(userContext)
            const sessions = packType === "DIY_MEDITATION_PACK" ? await this.DIYFulfilmentService.getDIYMeditationProductsByProductIds(userId, [productId], tenant) : await this.DIYFulfilmentService.getDIYFitnessProductsByProductIds(userId, [productId], tenant)
            const session = sessions[0]
            const seoParams: SeoUrlParams = {
                productName: pack.title
            }
            const image = packType === "DIY_MEDITATION_PACK" ? UrlPathBuilder.prefixSlash(pack.imageDetails.todayImage) : UrlPathBuilder.prefixSlash(session.imageDetails.todayImage)
            const actionUrl = ActionUtilV1.diyPackProductPage(pack, userContext.sessionInfo.userAgent)
            const sessionIndex = pack.sessionIds.indexOf(productId) + 1
            const activityWidget: ActivityWidget = {
                activityType: packType === "DIY_MEDITATION_PACK" ? "DIY_MEDITATION" : "DIY_FITNESS",
                userActivityId: packId,
                title: packType === "DIY_MEDITATION_PACK" ? "Meditation" : "Workout",
                date: date,
                subTitle: `${pack.title} (${sessionIndex} of ${pack.sessionIds.length})`,
                calloutText: (preferredTime != undefined) ? TimeUtil.getTimeIn12HRFormat(TimeUtil.formatDateInTimeZone(tz, new Date()), preferredTime.hour, preferredTime.min, true, tz) : TimeUtil.durationFromSecondsToMins(session.duration / 1000),
                startTime: (preferredTime != undefined) ? this.getStartTime(userContext, date, preferredTime) : null,
                closedSection: {
                    title: packType === "DIY_MEDITATION_PACK" ? `Meditation - ${pack.title}` : `Workout - ${pack.title}`,
                },
                status: status,
                widgetType: "ACTIVITY_WIDGET",
                images: [image],
                action: {
                    actionType: "NAVIGATION",
                    url: actionUrl
                },
                quickActions: ActionUtil.diyActionsFromSessionDetailV2(session, pack, sessionInfo),
                subActivities: [
                    {
                        viewType: "IMAGE_CARD",
                        list: [image]
                    }
                ]
            }
            return activityWidget
        }

        private getStartTime(userContext: UserContext, date: string, preferredTime: HourMin) {
            const tz = userContext.userProfile.timezone
            const parsedDate = TimeUtil.parseDate(date, tz)
            parsedDate.setHours(preferredTime.hour, preferredTime.min)
            return TimeUtil.formatEpochInTimeZone(tz, parsedDate.getTime(), "HH:mm:ss")
        }

        private async getCultScoreActivities(userAgent: UserAgent, userId: string, deviceId: string, dates: string[]): Promise<ActivityWidget[]> {
            const findQuery: IFindQuery = {
                condition: {
                    "date": { "$in": dates },
                    "activityType": "CULT_SCORE_DIY",
                    "userId": userId
                }
            }
            const cultScoreActivities: ActivityDS[] = await (this.activityDao.find(findQuery))

            const cultScorePromises = _.map(cultScoreActivities, cultScoreActivity => this.cultScoreActivityWidget(cultScoreActivity))
            return await Promise.all(cultScorePromises)
        }

        private async getSleepAndStepsActivityForToday(userContext: UserContext, userAgent: UserAgent, userId: string,
            deviceId: string, today: string): Promise<ActivityWidget[]> {
            const tz = userContext.userProfile.timezone
            const sleepKey = "SLEEP-" + TimeUtil.todaysDate(tz) + "-" + userId
            const stepsKey = "STEPS-" + TimeUtil.todaysDate(tz) + "-" + userId
            const findQuery: IFindQuery = {
                condition: {
                    "idempotenceKey": { "$in": [sleepKey, stepsKey] }
                }
            }
            const sleepAndStepForToday: ActivityDS[] = await (this.activityDao.find(findQuery))
            const sleepForToday = _.find(sleepAndStepForToday, sleepAndStepForToday => { return sleepAndStepForToday.activityType === "SLEEP" })
            const stepsForToday = _.find(sleepAndStepForToday, sleepAndStepForToday => { return sleepAndStepForToday.activityType === "WALK" })
            const stepsActivityWidget = await this.stepsActivityWidget(userContext, stepsForToday, userAgent)
            const sleepActivityWidget = await this.sleepActivityWidget(userContext, sleepForToday, userAgent, today)
            const activityWidgets: ActivityWidget[] = []
            if (stepsActivityWidget)
                activityWidgets.push(stepsActivityWidget)
            if (sleepActivityWidget)
                activityWidgets.push(sleepActivityWidget)
            return activityWidgets
        }


        private async stepsActivityWidget(userContext: UserContext, stepsForToday: ActivityDS, userAgent: UserAgent): Promise<ActivityWidget> {
            let stepsActivityWidget
            if (!_.isNil(stepsForToday)) {
                stepsActivityWidget = this.atlasActivityWidget(userContext, userAgent, TimelineUtil.createStepsAtlasActivity(stepsForToday))
                // Hack to show in todo section for today steps alone
                stepsActivityWidget.status = stepsActivityWidget.status === "SKIPPED" ? "TODO" : stepsActivityWidget.status
            }
            return stepsActivityWidget
        }

        private async sleepActivityWidget(userContext: UserContext, sleepForDate: ActivityDS, userAgent: UserAgent,
            date: string): Promise<ActivityWidget> {
            const tz = userContext.userProfile.timezone
            let sleepActivityWidget
            if (!_.isNil(sleepForDate)) {
                const isReviewed = sleepForDate.meta.sleep.sourceType === "USER"
                sleepActivityWidget = this.atlasActivityWidget(userContext, userAgent, TimelineUtil.createSleepAtlasActivity(sleepForDate, isReviewed))
            } else {
                const yesterday = TimeUtil.getDaysFrom(tz, date, 2, true)[1]
                const sleepStartTime = TimeUtil.getDate(yesterday, 23, 0, tz).getTime()
                const sleepEndTime = TimeUtil.getDate(date, 7, 0, tz).getTime()
                if (new Date().getTime() >= sleepEndTime) {
                    const action = `curefit://sleepdetails?addEntry=true&date=${date}&startTime=${sleepStartTime}&endTime=${sleepEndTime}`
                    const activityWidget: ActivityWidget = {
                        activityType: "SLEEP",
                        title: "Log your last night sleep time",
                        date: date,
                        subTitle: "Target: Sleep 7 to 9 hrs",
                        closedSection: {
                            title: "Log your last night sleep time"
                        },
                        status: "SKIPPED",
                        widgetType: "ACTIVITY_WIDGET",
                        action: {
                            actionType: "NAVIGATION",
                            url: action
                        }
                    }
                    sleepActivityWidget = activityWidget
                }
            }
            return sleepActivityWidget
        }

        private async cultScoreActivityWidget(activityDS: ActivityDS): Promise<ActivityWidget> {
            const activityWidget: ActivityWidget = {
                activityType: "CULT_SCORE_DIY",
                title: "Cult Score",
                date: activityDS.date,
                subTitle: "Cult Score",
                closedSection: {
                    title: "Cult Score"
                },
                status: "DONE",
                widgetType: "ACTIVITY_WIDGET",
                action: undefined
            }
            return activityWidget
        }

        private atlasActivityWidget(userContext: UserContext, userAgent: UserAgent, activity: Activity): ActivityWidget {
            // this.logger.info("in atlas activity for : " + JSON.stringify(activity))
            switch (activity.type) {
                case "WALK": {
                    const walk = <WalkActivity>activity
                    const activityWidget: ActivityWidget = {
                        activityType: "WALK",
                        title: walk.steps.done + " Steps",
                        date: activity.date,
                        subTitle: `Target ${walk.steps.goal} steps`,
                        calloutText: "",
                        closedSection: {
                            title: walk.steps.done + " Steps"
                        },
                        images: ["/image/today/steps.png"],
                        status: activity.status,
                        widgetType: "ACTIVITY_WIDGET",
                        meta: { progress: Math.min(walk.steps.done / walk.steps.goal, 1.0) },
                        action: {
                            actionType: "NAVIGATION",
                            url: `curefit://stepdetails?done=${walk.steps.done}&goal=${walk.steps.goal}&date=${activity.date}`
                        },
                        subActivities: [
                            {
                                viewType: "IMAGE_CARD",
                                list: ["/image/today/steps.png"]
                            }
                        ]
                    }
                    return activityWidget
                }
                case "SLEEP": {
                    const sleep = <SleepActivity>activity
                    const startTime = sleep.timeRange.start
                    const endTime = sleep.timeRange.end
                    const today = TimeUtil.todaysDateWithTimezone(userContext.userProfile.timezone)
                    let action
                    if (today === activity.date) {
                        action = `curefit://sleepdetails?reviewed=${sleep.hasReviewed}&date=${activity.date}&startTime=${startTime}&endTime=${endTime}`
                    } else {
                        action = `curefit://sleepdetails?reviewed=true&date=${activity.date}&startTime=${startTime}&endTime=${endTime}`
                    }
                    const activityWidget: ActivityWidget = {
                        activityType: "SLEEP",
                        title: this.sleepDisplayText(sleep.duration),
                        date: activity.date,
                        subTitle: "Target: Sleep 7 to 9 hrs",
                        closedSection: {
                            title: this.sleepDisplayText(sleep.duration),
                        },
                        status: activity.status,
                        widgetType: "ACTIVITY_WIDGET",
                        action: {
                            actionType: "NAVIGATION",
                            url: action
                        }
                    }
                    return activityWidget
                }
            }
        }

        private sleepDisplayText(durationInSeconds: number) {
            const minutes = durationInSeconds / 60
            const displayHours = Math.floor(minutes / 60)
            const displayMins = minutes % 60
            if (displayMins === 0) {
                return `${displayHours} hrs Sleep`
            } else {
                return `${displayHours} hrs ${Math.round(displayMins)} min Sleep`
            }
        }



        getPackActivity(): UserActivity {
            return {
                userActivityId: "GET_A_PACK",
                // Not used by any function, Change to user timezone, when this function is used
                date: TimeUtil.todaysDateWithTimezone(TimeUtil.IST_TIMEZONE),
                title: "Form healthy habits with Cure.fit. Get the most out of it by starting a pack.",
                activityType: "GET_A_PACK",
                activityName: "Get a Pack!",
                status: "TODO",
                action: "curefit://browse"
            }
        }
    }
    return TimelineBusinessV1
}
