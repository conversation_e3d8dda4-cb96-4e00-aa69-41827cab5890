import {
    DescriptionWidget,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    InfoCard,
    ProductDetailPage,
    ProductListWidget,
    WidgetView
} from "../common/views/WidgetView"
import { ManagedGoalProductViewModel, ProductSectionInfo } from "@curefit/gmf-common"

const GRADIENT_COLORS: string[][] = [["#76e997", "#2cc2d3"], ["#17d8e5", "#ac9aff"], ["#fb8a72", "#f64cac"], ["#f29458", "#fd796d"]]
const SHADOW_COLOR: string[] = ["#76e997", "#17d8e5", "#fb8a72", "#f29458"]
class PlanProductPage extends ProductDetailPage {
    private constructor(product: ManagedGoalProductViewModel) {
        super()
        this.widgets.push(this.getSummaryWidget(product.imgDetails["hero"].url, product.title))
        const descriptions = [{
            subTitle: product.productDescription,
        }]
        this.widgets.push(new DescriptionWidget(descriptions))
        this.widgets.push(this.getWhatsInPackWidget(product.sectionInfos.find(info => info.type === "PACK_CONTENTS_DETAILED")))
        this.widgets.push(this.getHowItWorksWidget(product.sectionInfos.find(info => info.type === "PACK_STEPS")))
        this.actions.push({
            title: "Get Started",
            actionType: "NAVIGATION",
            // url: "curefit://userform?formId=assessment_asthma",
            url: "curefit://userform?formId=Onboarding"
        })
    }

    getSummaryWidget(imageUrl: string, title: string) {
        const widgetView: WidgetView & {
            title: string
            image: string
        } = {
            widgetType: "TELECONSULTATION_SUMMARY_WIDGET",
            title: title,
            image: imageUrl
        }
        return widgetView
    }

    getWhatsInPackWidget(sectionInfo: ProductSectionInfo): ProductListWidget {
        const header: Header = {
            title: sectionInfo.title,
            color: "#000000"
        }
        const cards: GradientCard[] = []
        sectionInfo.details.forEach((item, index) => {
            cards.push({
                title: item.title,
                action: {
                    actionType: "SHOW_INFO_MODAL",
                    meta: {
                        icon: item.imageUrl,
                        title: item.title,
                        subTitle: item.desc
                    }
                },
                shadowColor: SHADOW_COLOR[index],
                gradientColors: GRADIENT_COLORS[index],
                icon: item.type
            })
        })
        return new ProductListWidget("GARDIENT_CARD", header, cards)
    }

    getHowItWorksWidget(sectionInfo: ProductSectionInfo): ProductListWidget {
        const header: Header = {
            title: sectionInfo.title,
            color: "#000000"
        }
        const infoCards: InfoCard[] = []
        sectionInfo.details.forEach(item => {
            infoCards.push({
                subTitle: item.desc,
                icon: item.imageUrl
            })
        })
        return new ProductListWidget("SMALL", header, infoCards)
    }

    public static async getView(product: ManagedGoalProductViewModel) {
        return new PlanProductPage(product)
    }
}

export default PlanProductPage
