import * as express from "express"
import { Container, inject } from "inversify"
import { controller, httpGet } from "inversify-express-utils"
import {
  LiveClass,
  LiveFitWorkoutFormat,
  LiveFitWorkoutFormats,
} from "@curefit/diy-common"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import AuthMiddleware from "../auth/AuthMiddleware"
import { DIY_CLIENT_TYPES, IIntlClientService } from "@curefit/diy-client"
import AppUtil from "../util/AppUtil"
import { UserContext } from "@curefit/userinfo-common"
import { TLCardListView } from "../pack/ContentPackDetailViewV2"
import ClassListViewBuilderV2 from "../cult/ClassListViewBuilderV2"
import LiveUtil from "../util/LiveUtil"

type TrainerWorkouts = {
  widgets: any[]
}

type Trainer = {
  name: string
  image: string
}

type Workout = {
  title: string
  time: string
  duration: string
  trainers: Trainer[]
  isLive: boolean
  locked: boolean
  actionText: string
}

type WorkoutSchedule = {
  date: string
  workouts: Workout[]
}

type WorkoutScheduleWidget = {
  widgetType: "TL_WORKOUT_SCHEDULE_WIDGET"
  schedule: any,
  title?: string
  showMoreCount?: number
}

export default function controllerFactory(kernel: Container) {
  @controller(
    "/trainer",
    kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession
  )
  class TrainerController {
    constructor(
      @inject(DIY_CLIENT_TYPES.IntlClientService) protected diyFulfilmentService: IIntlClientService,
      @inject(CUREFIT_API_TYPES.ClassListViewBuilderV2) private classListViewBuilderV2: ClassListViewBuilderV2
    ) { }

    @httpGet("/workouts")
    async getTrainerWorkouts(req: express.Request): Promise<TrainerWorkouts> {
      const userContext = req.userContext as UserContext
      const userId = req.session.userId
      const tenant = AppUtil.getTenantFromUserContext(userContext)
      const location = AppUtil.getCountryId(userContext)
      const { id } = req.query
      const response: TrainerWorkouts = {
        widgets: [],
      }

      const [allLiveClasses, diyPacks] = await Promise.all([
        this.diyFulfilmentService.getUpcomingTrainersLiveClasses(
          userId,
          [id],
          tenant,
          LiveFitWorkoutFormats as LiveFitWorkoutFormat[],
          location
        ),
        this.diyFulfilmentService.getDiyPacksForTrainers(
          [id],
          location,
          tenant,
          null
        ),
      ])


      // not sending real-live classes in either case
      const videoCallClasses: LiveClass[] = allLiveClasses.filter(liveClass => LiveUtil.isInteractiveSession(liveClass.preferredStreamType))
      const liveClasses: LiveClass[] = allLiveClasses.filter(liveClass => !LiveUtil.isConsideredInteractiveSessionForPresentation(liveClass.preferredStreamType))
      const isLiveQnASupported = await AppUtil.isLiveQnASupported(userContext)
      if (isLiveQnASupported && videoCallClasses.length) {
        const videoCallWidget: WorkoutScheduleWidget = {
          widgetType: "TL_WORKOUT_SCHEDULE_WIDGET",
          schedule: await this.classListViewBuilderV2.buildIntlView(userContext, "FITNESS", videoCallClasses),
          title: "Live Streaming",
          showMoreCount: 1
        }
        response.widgets.push(videoCallWidget)
      }

      const workoutScheduleWidget: WorkoutScheduleWidget = {
        widgetType: "TL_WORKOUT_SCHEDULE_WIDGET",
        schedule: [],
      }

      if (liveClasses.length) {
        workoutScheduleWidget.schedule = await this.classListViewBuilderV2.buildIntlView(userContext, "FITNESS", liveClasses)
        response.widgets.push(workoutScheduleWidget)
      }

      if (diyPacks.length) {
        response.widgets.push(...new TLCardListView({
          userContext,
          header: "Collections",
          packs: diyPacks,
        }).widgets)
      }

      return response
    }
  }
}
