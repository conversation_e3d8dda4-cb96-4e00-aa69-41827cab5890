import { Action, BaseWidget, IBaseWidget, UserContext } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../page/vm/ServiceInterfaces"
import { SortOrder } from "@curefit/mongo-utils"
import {
    CFLiveProduct,
    DIYRecipeCustomizedResponse,
    DIYRecipeIngredient,
    DIYRecipeProduct,
    DIYRecipeStep,
    DIYRecipeView,
    DIYCategory
} from "@curefit/diy-common"
import * as _ from "lodash"
import { RecipePackWidget } from "../../page/vm/widgets/RecipePackWidget"
import { ActionUtil, SeoUrlParams } from "@curefit/base-utils"
import { UrlPathBuilder } from "@curefit/product-common"
import { ProductDetailPage, WidgetView, WidgetType } from "../../common/views/WidgetView"
import { difficultyDisplayMap, IRecipeDetails, RecipeVideoWidget } from "../../recipe/RecipeVideoWidget"
import { IIngredientDetails } from "../../recipe/RecipeIngredientsListWidget"
import {
    createRecipeCards,
    getBreadCrumbsData,
    getIngredientGridWidgetList,
    IStructuredDataRecipe
} from "../../recipe/RecipeCommon"
import { NutritionWidget } from "../../common/views/NutritionWidget"
import AppUtil from "../../util/AppUtil"
import { DateUtil, TimeUtil, titleCase, CONTENT_CDN_BASE_PATH } from "@curefit/util-common"
import EatUtil from "../../util/EatUtil"
import { IEatLiveWidgetDataRequest } from "../../util/VMUtil"
import moment = require("moment-timezone/moment-timezone")
import { HourMin } from "@curefit/base-common"
import LiveUtil from "../../util/LiveUtil"
import { TitleDescriptionListWidget } from "../../page/vm/widgets/TitleDescriptionListWidget"
import { LivePackUtil } from "../../util/LivePackUtil"

export class NewRecipesWidget extends BaseWidget  {
    title: string
    items: any[]
    layoutProps: any
    constructor() {
        super("NEW_RECIPES_WIDGET")
        this.title = "New Recipes"
        this.items = []
    }
    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: {[filter: string]: any}): Promise<IBaseWidget> {

        let bookmarkedOnly = String(_.get(queryParams, "filters.bookmarkedOnly")) === "true"
        let vegOnlyFilter = String(_.get(queryParams, "filters.vegOnly", false)) === "true"

        if (AppUtil.isWeb(userContext)) {
            bookmarkedOnly = String(_.get(queryParams, "bookmarkedOnly", false)) === "true"
            vegOnlyFilter = String(_.get(queryParams, "vegOnly", false)) === "true"
        }
        if (bookmarkedOnly) {
            // not showing this widget in case bookmark is enabled
            return undefined
        }
        const req: IEatLiveWidgetDataRequest = {
            newRecipesRequired: true,
            recipeoftheDayRequired: true,
            trendingRecipesRequired: true,
            date: DateUtil.todaysDate("YYYY-MM-DD", userContext.userProfile.timezone),
            userId: userContext.userProfile.userId,
            vegOnly: vegOnlyFilter,
            location: AppUtil.getCountryId(userContext),
            tenant: AppUtil.getTenantFromUserContext(userContext)
        }
        const clpDataResponse: DIYRecipeCustomizedResponse = await userContext.userProfile.promiseMapCache.getPromise("eatlive-clp-data", req)
        const recipes: DIYRecipeView[] = clpDataResponse.whatsNewRecipes
        if (_.isNil(recipes) || _.isEmpty(recipes)) {
            return undefined
        }
        const recipeCardPromise: Promise<IBaseWidget>[] = []
        const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(interfaces.cultBusiness, interfaces.diyService, userContext)
        recipes.forEach(item => {
            recipeCardPromise.push(createRecipeCards(item, userContext, isUserEligibleForTrial, isUserEligibleForMonetisation))
        })
        if (! AppUtil.isWeb(userContext)) {
            this.layoutProps = {
                height: 187,
                width: 150
            }
        }

        this.items = (await Promise.all(recipeCardPromise)).filter( v => !_.isNil(v) && !_.isNull(v))
        return this
    }
}

export class TrendingRecipesWidget extends BaseWidget {
    title: string
    items: any[]
    layoutProps: any
    constructor() {
        super("TRENDING_RECIPES_WIDGET")
        this.title = "Trending Recipes"
        this.items =  []
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: {[filter: string]: any}): Promise<IBaseWidget> {
        let bookmarkedOnly = String(_.get(queryParams, "filters.bookmarkedOnly")) === "true"
        let vegOnlyFilter = String(_.get(queryParams, "filters.vegOnly", false)) === "true"

        if (AppUtil.isWeb(userContext)) {
            bookmarkedOnly = String(_.get(queryParams, "bookmarkedOnly", false)) === "true"
            vegOnlyFilter = String(_.get(queryParams, "vegOnly", false)) === "true"
        }
        if (bookmarkedOnly) {
            // not showing this widget in case bookmark is enabled
            return undefined
        }
        const req: IEatLiveWidgetDataRequest = {
            newRecipesRequired: true,
            recipeoftheDayRequired: true,
            trendingRecipesRequired: true,
            date: DateUtil.todaysDate("YYYY-MM-DD", userContext.userProfile.timezone),
            userId: userContext.userProfile.userId,
            vegOnly: vegOnlyFilter,
            location: AppUtil.getCountryId(userContext),
            tenant: AppUtil.getTenantFromUserContext(userContext)
        }

        const clpDataResponse: DIYRecipeCustomizedResponse = await userContext.userProfile.promiseMapCache.getPromise("eatlive-clp-data", req)
        const recipes: DIYRecipeView[] = clpDataResponse.trendingRecipes
        if (_.isNil(recipes) || _.isEmpty(recipes)) {
            return undefined
        }

        const recipeCardPromise: Promise<IBaseWidget>[] = []
        const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(interfaces.cultBusiness, interfaces.diyService, userContext)
        recipes.forEach(item => {
            recipeCardPromise.push(createRecipeCards(item, userContext, isUserEligibleForTrial, isUserEligibleForMonetisation))
        })

        if (! AppUtil.isWeb(userContext)) {
            this.layoutProps = {
                height: 187,
                width: 150
            }
        }

        this.items = (await Promise.all(recipeCardPromise)).filter( v => !_.isNil(v) && !_.isNull(v))
        return this
    }
}

export class RecipeofthedayWidget extends BaseWidget {
    items: {
        title: string
        tag: string
        recipeTitle: string
        preparationTime: number
        difficulty: string
        servings: number
        videoUrl: string
        action: Action,
        image: string,
        recipeDetails?: IRecipeDetails[] // using it for app
        shareAction?: Action
        bookmarkMeta?: {
            status: boolean,
            action: Action
        }
    }[]
    constructor() {
        super("RECIPE_OF_THE_DAY_WIDGET")
        this.items = []
    }
    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: {[filter: string]: any}): Promise<IBaseWidget> {
        let bookmarkedOnly = String(_.get(queryParams, "filters.bookmarkedOnly")) === "true"
        let vegOnlyFilter = String(_.get(queryParams, "filters.vegOnly", false)) === "true"

        if (AppUtil.isWeb(userContext)) {
            bookmarkedOnly = String(_.get(queryParams, "bookmarkedOnly", false)) === "true"
            vegOnlyFilter = String(_.get(queryParams, "vegOnly", false)) === "true"
        }
        if (bookmarkedOnly) {
            // not showing this widget in case bookmark is enabled
            return undefined
        }

        const userId = userContext.userProfile.userId
        const countryId = AppUtil.getCountryId(userContext)
        const req: IEatLiveWidgetDataRequest = {
            newRecipesRequired: true,
            recipeoftheDayRequired: true,
            trendingRecipesRequired: true,
            date: DateUtil.todaysDate("YYYY-MM-DD", userContext.userProfile.timezone),
            userId: userId,
            vegOnly: vegOnlyFilter,
            location: AppUtil.getCountryId(userContext),
            tenant: AppUtil.getTenantFromUserContext(userContext)
        }
        const clpDataResponse: DIYRecipeCustomizedResponse = await userContext.userProfile.promiseMapCache.getPromise("eatlive-clp-data", req)
        const recipeResponse: DIYRecipeView[] = clpDataResponse.recipesOfTheDay
        if (_.isNil(recipeResponse) || _.isEmpty(recipeResponse)) {
            return undefined
        }

        const recipeProductsPromise: Promise<DIYRecipeProduct[]> = interfaces.diyService.getDIYRecipeProducts(userId, _.map(recipeResponse, recipe => recipe.id), countryId)
        const recipes = await recipeProductsPromise
        const cardsPromise: Promise<any>[] = []
        const user = await interfaces.userCache.getUser(userContext.userProfile.userId)
        const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(interfaces.cultBusiness, interfaces.diyService, userContext)
        recipes.forEach(item => {
            cardsPromise.push(this.createCard(item as DIYRecipeProduct, userContext, isUserEligibleForMonetisation, isUserEligibleForTrial, interfaces))
        })

        this.items = (await Promise.all(cardsPromise)).filter( v => !_.isNull(v) && !_.isNil(v))
        return this

    }

    async createCard(recipe: DIYRecipeProduct, userContext: UserContext, isUserEligibleForMonetisation: boolean, isUserEligibleForLiveTrialPack: boolean, interfaces: CFServiceInterfaces ): Promise<any> {
        const recipeDetails: IRecipeDetails[] = []
        const seoParams: SeoUrlParams = {
            productName: recipe.title
        }
        const navigationAction: Action = {
            title: "VIEW STEPS",
            actionType: "NAVIGATION",
            url: ActionUtil.recipeHomePage(recipe.productId, seoParams, userContext.sessionInfo.userAgent)
        }
        let diyAction = navigationAction
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        const deeplink = await interfaces.deeplinkService.getRecipeDeeplink(tenant, recipe)
        if (! AppUtil.isWeb(userContext)) {
            // For web, no monetisation for recipes as it will hamper SEO
            const isLocked = recipe.locked
            diyAction = LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, isLocked, navigationAction, isUserEligibleForLiveTrialPack, recipe.productType, "eat_live_clp", "", false, null)
            const preparationTime: HourMin = TimeUtil.convertDurationSecondsToHourMin(recipe.preparationTime)
            let preparationTimeString: string = preparationTime.min > 0 ? preparationTime.min + " Min" : ""
            if (preparationTime.hour > 0) {
                preparationTimeString = preparationTime.hour + " Hr " + preparationTimeString
            }
            recipeDetails.push(
                {
                    key: "Time",
                    value: preparationTimeString
                },
                {
                    key: "Difficulty",
                    value: difficultyDisplayMap.get(recipe.difficultyLevel)
                },
                {
                    key: "Serves",
                    value: recipe.serving + ""
                }
            )
        }
        const card: {
            title: string
            tag: string
            recipeTitle: string
            preparationTime: number
            difficulty: string
            servings: number
            videoUrl: string
            action: Action
            image: string,
            recipeDetails?: IRecipeDetails[]
            shareAction?: Action
            bookmarkMeta?: {
                status: boolean,
                action: Action
            }
        } = {
            title: "Recipe Of The Day",
            tag: undefined, // todo: TAG not supported in v1
            recipeTitle: recipe.title,
            difficulty: recipe.difficultyLevel,
            preparationTime: recipe.preparationTime,
            servings: recipe.serving,
            videoUrl: `/video/${recipe.contentId}.` + (recipe.contentFormat || "mp4"),
            action: diyAction,
            image: recipe.imageDetails.heroImage,
            recipeDetails: !_.isEmpty(recipeDetails) ? recipeDetails : undefined,
            shareAction: {
                actionType: "SHARE_ACTION",
                title: "Share",
                meta: {
                    videoUrl: UrlPathBuilder.getVideoAbsolutePath(recipe.contentId, recipe.contentFormat),
                    isShareSingle: true,
                    shareOptions: {
                        message: `Hey! Check out this recipe for ${recipe.title}! Explore 100s of healthy recipes with step-by-step guide on the cult.fit app. * ${deeplink} *`
                    }
                }
            },
            bookmarkMeta: {
                status: _.get(recipe, "userMeta.isSubscribed",  false), // current recipe bookmark state
                action: {
                    actionType: _.get(recipe, "userMeta.isSubscribed", false) ? "UNBOOKMARK_RECIPE" : "BOOKMARK_RECIPE",
                    meta: {
                        recipeId: recipe.productId,
                        isBookMarked: !_.get(recipe, "userMeta.isSubscribed", false) // what the user wants to do with recipe
                    }
                }
            }
        }

        return card
    }

}

export class RecipeDetailWebView extends ProductDetailPage {
    constructor() {
        super()
    }
    async buildView(recipe: DIYRecipeProduct, deeplink: string, userContext: UserContext, categories: DIYCategory[], isInternationalUser: boolean): Promise<ProductDetailPage> {

        const videoWidgetPromise = new RecipeVideoWidget(recipe, deeplink, false).buildView()
        const nutritionInfoWebWidgetPromise = new NutritionWidget(recipe).buildView()
        const ingredientWidgetPromise = new IngredientInfoWebWidget().buildView(recipe.ingredients)
        const descriptionWidgetPromise = new RecipeDetailDescriptionWidget().buildView(recipe.steps)
        const recipeSuccessWidgetPromise = new RecipeSuccessWebWidget(recipe).buildView()
        const recipeTitleDescriptionInfoWidgetPromise = !_.isNil(recipe.multiLevelDescription) && !_.isEmpty(recipe.multiLevelDescription) ?
            new TitleDescriptionListWidget(recipe.multiLevelDescription, 24, 16, false).buildView() : undefined
        const recipeStructuredDataPromise = new StructuredDataRecipeWidget().buildView(undefined, undefined, {
            "recipe": recipe,
            "categories": categories
        })

        const [
            videoWidget,
            nutritionWidget,
            ingredientInfoWidget,
            descriptionWidget,
            recipeSuccessWidget,
            recipeTitleDescriptionInfoWidget,
            recipeStructuredData
        ] = await Promise.all([
            videoWidgetPromise,
            nutritionInfoWebWidgetPromise,
            ingredientWidgetPromise,
            descriptionWidgetPromise,
            recipeSuccessWidgetPromise,
            recipeTitleDescriptionInfoWidgetPromise,
            recipeStructuredDataPromise
        ])

        nutritionWidget.layoutProps = {
            showDivider: false
        }
        if (AppUtil.isWeb(userContext)) {
            videoWidget.videoUrl = `/video/${recipe.contentId}.` + (recipe.contentFormat || "mp4")
            videoWidget.breadCrumbs  = getBreadCrumbsData(recipe.title, "EAT", isInternationalUser)
            videoWidget.widgets.push(nutritionWidget)
            // todo: add the seo widget once client needs it
            this.widgets.push(videoWidget, ingredientInfoWidget, descriptionWidget, recipeTitleDescriptionInfoWidget, recipeSuccessWidget, recipeStructuredData)
        } else {
            // todo: add the seo widget once client needs it
            this.widgets.push(videoWidget, nutritionWidget, ingredientInfoWidget, descriptionWidget, recipeTitleDescriptionInfoWidget, recipeSuccessWidget)
        }
        this.widgets = _.compact(this.widgets)
        return this
    }
}

export class IngredientInfoWebWidget implements WidgetView {
    title: string
    list: {
        title: string,
        items: {
            name: string,
            quantity: number,
            unit: string,
            image: string
        }[]
    }[]
    widgetType: WidgetType
    constructor() {
        this.widgetType = "DESCRIPTION_LIST_WIDGET"
        this.title = "Ingredients"
        this.list = []
    }

    async buildView(ingredients: DIYRecipeIngredient[]): Promise<any> {
        if (_.isNil(ingredients)) {
            return undefined
        }
        const categoryMap: Map<string, IIngredientDetails[]> = getIngredientGridWidgetList(ingredients)
        categoryMap.forEach((val, key: string) => {
            const items: {
                name: string,
                quantity: number,
                unit: string,
                image: string
            }[] = []
            val.forEach(v => {
                items.push({
                    name: v.title,
                    unit: v.unit,
                    quantity: v.quantity,
                    image: v.image
                })
            })
            this.list.push({
                title: key,
                items: items
            })
        })
        return this
    }
}


export class RecipeDetailDescriptionWidget implements WidgetView {
    list: string[]
    title: string
    widgetType: WidgetType
    constructor() {
        this.widgetType = "ORDERED_LIST_WIDGET"
        this.list = []
        this.title = "Directions"
    }

    async buildView(steps: DIYRecipeStep[]): Promise<WidgetView> {
        if (_.isNil(steps)) {
            return undefined
        }

        steps.forEach( step => {
            this.list.push(step.text)
        })

        return this
    }
}

export class RecipeSuccessWebWidget implements WidgetView {
    title: string
    subtitle: string
    imageUrl: string
    widgetType: WidgetType
    videoUrl: string
    constructor(recipe: DIYRecipeProduct) {
        this.widgetType = "RECIPE_SUCCESS_WIDGET"
        this.title = "Success!"
        this.subtitle = "We hope you had fun making it! Enjoy the meal."
        this.imageUrl = recipe.imageDetails.thumbnailImage
        this.videoUrl = `/video/${recipe.completionVideo}.` + (recipe.contentFormat || "mp4")
    }
    async buildView(): Promise<WidgetView> {
        return this
    }
}

export class StructuredDataRecipeWidget implements WidgetView {
    data: IStructuredDataRecipe
    widgetType: WidgetType
    constructor() {
        this.widgetType = "SEO_STRUCTURED_DATA_RECIPE"
    }

    async buildView(interfaces?: CFServiceInterfaces, userContext?: UserContext, queryParams?: {[filter: string]: any}): Promise<WidgetView> {
        const recipe: DIYRecipeProduct = queryParams["recipe"]
        const categories: DIYCategory[] = queryParams["categories"]
        const [images, instrcutions, ingredients] = await Promise.all([
            this.getImages(recipe),
            this.getInstructions(recipe),
            this.getIngredients(recipe)
        ])
        const date = _.get(recipe, "createdDate", undefined) as Date
        const thumbnailImage = _.get(recipe, "imageDetails.thumbnailImage", "")
        const category = categories.find((v) => (<any>v)._id === recipe.categoryIds[0])
        this.data = {
            name: recipe.title,
            description: recipe.subTitle,
            images: images,
            recipeCuisine: "",
            datePublished: moment(date).format("YYYY-MM-DD"),
            instructions: instrcutions,
            video: {
                url: UrlPathBuilder.getVideoAbsolutePath(recipe.completionVideo, recipe.contentFormat),
                name: recipe.title,
                description: recipe.subTitle,
                thumbnailImages:  [thumbnailImage ? CONTENT_CDN_BASE_PATH + thumbnailImage : ""],
                uploaded: moment(date).format("YYYY-MM-DD"),
            },
            serving: recipe.serving,
            category: category ? category.name : "",
            prepTime: "",
            cookTime: "",
            totalTime: moment.duration(recipe.duration, "seconds").toISOString(),
            difficulty: recipe.difficultyLevel,
            ingredients: ingredients,
            calories: Math.ceil(_.get(recipe, "nutritionInfo.energy", 0)),
            keywords: ""
        }
        return this
    }

    async getImages(recipe: DIYRecipeProduct) {
        const images: string[] = []
        if (recipe && recipe.imageDetails) {
            recipe.imageDetails.heroImage ? images.push(recipe.imageDetails.heroImage) : null
            recipe.imageDetails.thumbnailImage ? images.push(recipe.imageDetails.thumbnailImage) : null
            recipe.imageDetails.todayImage ? images.push(recipe.imageDetails.todayImage) : null
        }
        !_.isEmpty(recipe.imageUrls) ? images.push(... recipe.imageUrls) : null
        !_.isNil(recipe.imageUrl) ? images.push(recipe.imageUrl) : null
        return images.map((image) => CONTENT_CDN_BASE_PATH + image)
    }

    async getInstructions(recipe: DIYRecipeProduct) {
        const steps: DIYRecipeStep[] = []
        if (recipe && !_.isEmpty(recipe.steps) ) {
            recipe.steps.forEach( step => {
                const imageUrl = step.imageUrl ? CONTENT_CDN_BASE_PATH + step.imageUrl : ""
                steps.push({text: step.text, imageUrl})
            })
        }
        return steps
    }

    async getIngredients(recipe: DIYRecipeProduct) {
        const ingredientArray: string[] = []
        if (!_.isEmpty(recipe.ingredients)) {
            recipe.ingredients.forEach( ingredient => {
                const quantity = ingredient.quantity
                const unit = EatUtil.getUnit(ingredient.quantity, ingredient.unit)
                const name = titleCase(ingredient.ingredientDetails.name)
                ingredientArray.push(`${quantity} ${unit} ${name}`)
            })
        }
        return ingredientArray
    }

}
