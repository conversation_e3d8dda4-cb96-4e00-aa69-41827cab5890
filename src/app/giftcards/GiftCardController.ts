import { Container, inject } from "inversify"
import { controller, httpPost } from "inversify-express-utils"
import AuthMiddleware from "../auth/AuthMiddleware"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { BASE_TYPES, Logger } from "@curefit/base"
import { CONSTELLO_CLIENT_TYPES, IGiftCardService } from "@curefit/constello-client"
import * as express from "express"
import { Session, UserContext } from "@curefit/userinfo-common"
import GiftCardViewBuilder from "./GiftCardViewBuilder"
import { GiftCardUtil } from "../referral/GiftCardUtil"
import * as _ from "lodash"
import ReferPageConfig, { ReferralPage } from "../referral/ReferPageConfig"
import { GiftCardPolicy } from "@curefit/constello-common"
import { IFeedback } from "@curefit/vm-common"
import { PAGE_ID } from "../page/Page"
import { IAppFeedback } from "../page/vm/services/AppFeedbackService"
import AuthUtil from "../util/AuthUtil"
import AppUtil from "../util/AppUtil"


export function GiftCardControllerFactory(kernel: Container) {
    @controller("/giftcards",
        kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class GiftCardController {
        constructor(
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(CONSTELLO_CLIENT_TYPES.GiftCardService) private giftCardService: IGiftCardService,
            @inject(CUREFIT_API_TYPES.GiftCardViewBuilder) private giftCardViewBuilder: GiftCardViewBuilder,
            @inject(CUREFIT_API_TYPES.ReferPageConfig) protected referPageConfig: ReferPageConfig,
            @inject(CUREFIT_API_TYPES.AppFeedbackService) private userResearchAppFeedbackService: IAppFeedback,
        ) {
        }


        @httpPost("/user")
        async fetchGiftCards(request: express.Request) {
            const session: Session = request.session
            const userId: string = session.userId
            const userContext: UserContext = request.userContext as UserContext
            const {countryCallingCode} = await userContext.userPromise
            const countryId = GiftCardUtil.getCountryFromPhone(countryCallingCode)
            let campaignId = request.query.campaignId
            if (_.isNil(campaignId) || campaignId === "undefined") {
                campaignId = await this.getCampaignIds(userId, countryId)
            }
            if (_.isNil(campaignId)) {
                return await this.getUnavailableView()
            }
            const {data, success} = await this.giftCardService.getOrCreateGiftCardsWithPolicy(
                userId,
                campaignId,
                countryId
            )
            if (!success) {
                const {policy} = await this.giftCardService.getLatestPolicy(campaignId, countryId)
                return await this.getUnavailableView(policy)
            }
            const pageId = PAGE_ID.GIFTCARD_USER_REFERRAL
            const feedback: IFeedback[] = await this.userResearchAppFeedbackService.getAppFeedbackForPage(userContext, pageId)
            const giftCardView = await this.giftCardViewBuilder.buildGiftCardView(userContext, data.summary, data.policyEntity.policy)
            return {
                ...giftCardView,
                feedback,
                pageId,
            }
        }

        @httpPost("/attribution")
        public async consumeCouponAndCreateCard(request: express.Request) {
            const session: Session = request.session
            const {userId, deviceId} = session
            const {referrerCode} = request.body
            const consumeGiftCardResponse = await this.giftCardService.consumeGiftCard(
                referrerCode,
                userId,
                deviceId,
                AppUtil.callSource(AuthUtil.getApiKeyFromReq(request))
            )
            return this.giftCardViewBuilder.buildAcceptInviteView(consumeGiftCardResponse, request.body, userId)
        }
        private async getCampaignIds(userId: string, countryId: string): Promise<string> {
            const campaigns = await this.giftCardService.getAllApplicablePolicies(userId, countryId)
            const policies = campaigns.policies
                .map(policy => policy.policyIdentifier.campaignId)
            if (policies.length == 1) {
                return policies[0]
            } else {
                return policies
                    .filter(campaignId => !["eat_200_fitcash"].includes(campaignId))
                    .find(_ => _)
            }
        }

        private async getUnavailableView(policy?: GiftCardPolicy) {
            const referralPage: ReferralPage = _.find(this.referPageConfig.referralPages, (referralPage) => {
                return referralPage.pageId === "ReferPageConfig"
            })

            return this.giftCardViewBuilder.getUnavailableView(policy, (referralPage && referralPage.data && referralPage.data.unavailableCopy) || {})
        }
    }

    return GiftCardController
}

export default GiftCardControllerFactory
