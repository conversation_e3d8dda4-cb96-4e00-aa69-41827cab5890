import { inject, injectable } from "inversify"
import { CardConfig, DynamicLink, GiftCard, GiftCardPolicy, GiftCardSummary, MultiCardConfig } from "@curefit/constello-common"
import {
    Action,
    CodeState,
    CouponBenefitsViewModel,
    CouponsViewModel,
    GiftCardPageResponse
} from "@curefit/apps-common"
import { GiftCardUtil, UnavailableCopy } from "../referral/GiftCardUtil"
import { UserContext } from "@curefit/userinfo-common"
import { TimeUtil } from "@curefit/util-common"
import { ActionUtil } from "@curefit/base-utils"
import * as _ from "lodash"
import { IUserService, USER_CLIENT_TYPES } from "@curefit/user-client"

@injectable()
class GiftCardViewBuilder {
    constructor(
        @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
    ) {
    }

    async buildGiftCardView(userContext: User<PERSON>ontext, summary: GiftCardSummary, policy: GiftCardPolicy): Promise<GiftCardPageResponse> {

        const tz = userContext.userProfile.timezone
        const expiryDate = policy.endDate
        const expirationTime = TimeUtil.getEpochFromDate(expiryDate) - TimeUtil.getEpochFromDate(TimeUtil.getDateNow(tz))
        const couponsHaveExpired = expirationTime <= 0
        const validTill = TimeUtil.formatDateInTimeZone(tz, expiryDate, "MMM DD, YYYY")

        const uiConfig = policy.uiConfig
        const isAndroid: boolean = userContext.sessionInfo.osName === "android"
        const coupons: CouponsViewModel = {
            icon: uiConfig.bannerUrl,
            title: uiConfig.giftCallout.title,
            subTitle: uiConfig.giftCallout.subtitle,
            footerText: uiConfig.showExpiry ? (couponsHaveExpired ? `Expired on ${validTill}.` : `Expires on ${validTill}. So hurry!`) : undefined,
            backgroundGradient: {left: "#ffe39a", right: "#ffd388"},
            codes: summary.cards.map((card: { code: string, consumedCount: number, dynamicLink?: DynamicLink }) => {
                const consumed = this.isUsedCompletely(card.consumedCount, policy.cardConfig)
                const state = consumed ? CodeState.CONSUMED : couponsHaveExpired ? CodeState.EXPIRED : CodeState.ACTIVE
                const whatsAppMessageTags: any = {
                    code: card.code
                }

                if (policy.cardConfig.generateLink) {
                    if (card && card.dynamicLink) {
                        whatsAppMessageTags.dll = card.dynamicLink.url
                    }
                }
                return {
                    name: card.code,
                    consumed,
                    state,
                    action: {
                        actionType: "SHARE_ACTION",
                        title: GiftCardUtil.getCouponTitle(state, policy.cardConfig),
                        meta: {
                            shareOptions: {
                                url: uiConfig.whatsApp.image,
                                type: "image/png",
                                message: GiftCardUtil.getWhatsappMessage(uiConfig.whatsApp.message, whatsAppMessageTags),
                                title: "Curefit Referrals",
                                whatsAppNumber: isAndroid ?  "" : undefined
                            },
                            shareChannel: isAndroid ? "WHATSAPP" : undefined
                        }
                    },
                }
            }),
            params: {}
        }

        let conversion: CouponBenefitsViewModel
        if (uiConfig && uiConfig.conversionBenefits && !_.isEmpty(uiConfig.conversionBenefits)) {
            conversion = {
                title: "You get",
                benefits: uiConfig.conversionBenefits.map(benefit => ({
                    title: benefit.title,
                    subTitle: benefit.subtitle,
                    icon: benefit.iconUrl
                })),
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtil.webview(uiConfig.howItWorksUrl),
                    title: "HOW IT WORKS?",
                    icon: "INFO_PINK"
                }
            }
        }

        const activation: CouponBenefitsViewModel = {
            title: "Your friend gets",
            benefits: uiConfig.activationBenefits.map(benefit => ({
                title: benefit.title,
                subTitle: benefit.subtitle,
                icon: benefit.iconUrl
            })),
            action: {
                actionType: "NAVIGATION",
                url: ActionUtil.webview(uiConfig.eligibilityUrl),
                title: "WHO IS ELIGIBLE?",
                icon: "INFO_PINK"
            }
        }

        return {coupons, conversion, activation}
    }

    private isUsedCompletely(consumptionCount: number, cardConfig: CardConfig): boolean {
        switch (cardConfig.cardType) {
            case "MULTI":
                return consumptionCount == (cardConfig as MultiCardConfig).maxConsumptions
            case "SINGLE":
                return consumptionCount == 1
        }
    }

    public getUnavailableView(policy: GiftCardPolicy, copy: UnavailableCopy | {}) {
        const uiConfig = policy && policy.uiConfig
        return {
            coupons: {
                icon: _.get(copy, "icon", "/image/icons/referral/vertical/eat-head.png"),
                title: _.get(copy, "title", "Not Available"),
                subTitle: _.get(copy, "subTitle", "This is now only available to a select set of users."),
                backgroundGradient: {left: "#ffe39a", right: "#ffd388"},
                // @ts-ignore
                codes: [{
                    name: "xxxxxxxxxx",
                    consumed: true,
                    state: CodeState.CONSUMED,
                    action: {
                        actionType: "SHARE_ACTION",
                        title: "INACTIVE",
                        meta: {}
                    }
                }]
            },
            conversion: uiConfig && uiConfig.conversionBenefits && !_.isEmpty(uiConfig.conversionBenefits) ? {
                title: "You get",
                benefits: uiConfig.conversionBenefits.map(benefit => ({
                    title: benefit.title,
                    subTitle: benefit.subtitle,
                    icon: benefit.iconUrl
                })),
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtil.webview(uiConfig.howItWorksUrl),
                    title: "HOW IT WORKS?",
                    icon: "INFO_PINK"
                }
            } : undefined,
            activation: uiConfig && uiConfig.activationBenefits && !_.isEmpty(uiConfig.activationBenefits) ? {
                title: "Your friend gets",
                benefits: uiConfig.activationBenefits.map(benefit => ({
                    title: benefit.title,
                    subTitle: benefit.subtitle,
                    icon: benefit.iconUrl
                })),
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtil.webview(uiConfig.eligibilityUrl),
                    title: "WHO IS ELIGIBLE?",
                    icon: "INFO_PINK"
                }
            } : undefined
        }
    }

    public async buildAcceptInviteView(consumeGiftCardResponse: any, reqBody: any, refereeUserId: string) {
        const { success, data } = consumeGiftCardResponse
        if (success) {
            return this.buildInviteAcceptedView(data.card, data.policy, reqBody)
        } else {
            return this.buildFailedViewByError(consumeGiftCardResponse, reqBody, refereeUserId)
        }
    }
    public async buildInviteAcceptedView(giftCard: GiftCard, policy: GiftCardUtil, reqBody: any) {
        const referrer = await this.userService.getUser(giftCard.userId)
        const referee = await this.userService.getUser(giftCard.consumedUser.userId)
        return {
            imageUrl: "https://cdn-images.cure.fit/www-curefit-com/image/upload/image/icons/referral/vertical/gc_invite_accepted.png",
            title: `Welcome ${referee.firstName}!`,
            info: `Your friend ${referrer.firstName} has invited you to your 1st live class`,
            rewards: [
                {
                    url: "https://cdn-images.cure.fit/www-curefit-com/image/upload/image/icons/referral/vertical/gc_reward_icon.png",
                    text: "With live classes, you can track your performance and compete with friends in real-time for FREE!"
                }
            ],
            action: {
                title: `ACCEPT INVITE`,
                actionType: "NAVIGATION",
                pageFrom: "referralattributionpage",
                url: this.getActionUrl(reqBody)
            }
        }
    }
    private async buildFailedViewByError(consumeGiftCardResponse: any, reqBody: any, refereeUserId: string) {
        const {errorResponse, summary} = consumeGiftCardResponse.data
        const referee = await this.userService.getUser(refereeUserId)
        const referrer = await this.userService.getUser(summary.userId)
        const imageUrl = "https://cdn-images.cure.fit/www-curefit-com/image/upload/image/icons/referral/vertical/gc_invite_accepted.png"
        const iconUrl = "https://cdn-images.cure.fit/www-curefit-com/image/upload/image/icons/referral/vertical/gc_reward_icon.png"
        const classBenefitsSting = "You can still join the live classes, track your performance and compete with friends in real-time for FREE!"
        const actionUrl =  this.getActionUrl(reqBody)
        switch (errorResponse.code) {
            case "COUPON_ALREADY_MAX_CONSUMED":
                return {
                    imageUrl: imageUrl,
                    title: `Welcome, ${referee.firstName}!`,
                    info: `Your friend ${referrer.firstName} has invited you to your 1st live class. Please note that they have already reached maximum reward limit for inviting. But the fun of working out together will continue!`,
                    rewards: [
                        {
                            url: iconUrl,
                            text: classBenefitsSting
                        }
                    ],
                    action: {
                        title: `PROCEED TO JOIN`,
                        actionType: "NAVIGATION",
                        pageFrom: "referralattributionpage",
                        url: actionUrl
                    },
                    isAutoRedirect: false
                }
           case "NO_GIFT_CARD_APPLICABLE":
                return {
                    imageUrl: imageUrl,
                    title: `Welcome, ${referee.firstName}!`,
                    info: `Your friend ${referrer.firstName} has invited you to join a live class. Please note that Fitcash for inviting friends to their 1st live class has been stopped.`,
                    rewards: [
                        {
                            url: iconUrl,
                            text: classBenefitsSting
                        }
                    ],
                    action: {
                        title: `PROCEED TO JOIN`,
                        actionType: "NAVIGATION",
                        pageFrom: "referralattributionpage",
                        url: actionUrl
                    },
                    isAutoRedirect: false
                }
            case "USER_USING_GIFT_CARD_FOR_SELF":
                return {
                    imageUrl: imageUrl,
                    title: `Hey ${referee.firstName}`,
                    info: errorResponse.message,
                    rewards: [
                        {
                            url: iconUrl,
                            text: classBenefitsSting
                        }
                    ],
                    action: {
                        title: `OKAY, GOT IT`,
                        actionType: "NAVIGATION",
                        pageFrom: "referralattributionpage",
                        url: this.getActionUrl(reqBody)
                    },
                    isAutoRedirect: false
                }

            case "USER_ALREADY_CONSUMED_SOME_GIFT_CARD":
            case "USER_HAS_ACTIVE_REFERRAL":
                return {
                    imageUrl: imageUrl,
                    title: `Welcome back, ${referee.firstName}`,
                    info: errorResponse.message,
                    rewards: [
                        {
                            url: iconUrl,
                            text: classBenefitsSting
                        }
                    ],
                    action: {
                        title: `PROCEED TO JOIN`,
                        actionType: "NAVIGATION",
                        pageFrom: "referralattributionpage",
                        url: actionUrl
                    },
                    isAutoRedirect: false
                }
            case "USER_REFERRING_TO_SAME_DEVICE":
                return {
                    imageUrl: imageUrl,
                    title: `Welcome back, ${referee.firstName}`,
                    info: errorResponse.message,
                    rewards: [
                        {
                            url: iconUrl,
                            text: classBenefitsSting
                        }
                    ],
                    action: {
                        title: `PROCEED TO JOIN`,
                        actionType: "NAVIGATION",
                        pageFrom: "referralattributionpage",
                        url: actionUrl
                    },
                    isAutoRedirect: false
                }
            case "USER_ALREADY_CONSUMED_TRIAL_CLASS":
                return {
                    imageUrl: imageUrl,
                    title: `Welcome back, ${referee.firstName}`,
                    info: errorResponse.message,
                    rewards: [
                        {
                            url: iconUrl,
                            text: classBenefitsSting
                        }
                    ],
                    action: {
                        title: `PROCEED TO JOIN`,
                        actionType: "NAVIGATION",
                        pageFrom: "referralattributionpage",
                        url: actionUrl
                    },
                    isAutoRedirect: false
                }
            case "USER_HAS_NO_PHONE":
                return {
                    imageUrl: imageUrl,
                    title: `Welcome back, ${referee.firstName}`,
                    info: `Your friend ${referrer.firstName} has invited you to your 1st live class. Please update your phone number first if you want them to get rewarded.`,
                    rewards: [
                        {
                            url: iconUrl,
                            text: classBenefitsSting
                        }
                    ],
                    action: {
                        title: `UPDATE PHONE`,
                        actionType: "NAVIGATION",
                        pageFrom: "referralattributionpage",
                        url: "curefit://updatephone?pageFrom=referralattributionpage"
                    },
                    isAutoRedirect: false
                }
            case "ALREADY_CULT_LIVE_USER":
                return {
                    imageUrl: imageUrl,
                    title: `Welcome back, ${referee.firstName}`,
                    info: `Your friend ${referrer.firstName} has invited you to a live class`,
                    rewards: [
                        {
                            url: iconUrl,
                            text: classBenefitsSting
                        }
                    ],
                    action: {
                        title: `PROCEED TO JOIN`,
                        actionType: "NAVIGATION",
                        pageFrom: "referralattributionpage",
                        url: actionUrl
                    },
                    isAutoRedirect: this.shouldAutoRedirect(reqBody)
                }
            default:
                return  {
                    imageUrl: imageUrl,
                    title: `Uh-Oh!`,
                    info: errorResponse.message,
                    rewards: [
                        {
                            url: iconUrl,
                            text: classBenefitsSting
                        }
                    ],
                    action: {
                        title: `PROCEED TO JOIN`,
                        actionType: "NAVIGATION",
                        pageFrom: "referralattributionpage",
                        url: actionUrl
                    },
                    isAutoRedirect: false
                }

        }
    }

    private getActionUrl(reqBody: any): string {
        const {redirectUrl, nodeRelationID, liveClassId } = reqBody
        if (redirectUrl) {
            const url = new URL(redirectUrl)
            const base = url.href.substring(0, url.href.indexOf("?"))
            const params = new URLSearchParams(url.search.slice(1))
            if (nodeRelationID) {
                params.append("nodeRelationID", nodeRelationID)
            }
            if (liveClassId) {
                params.append("liveClassId", liveClassId)
            }
            params.append("productType", "LIVE_FITNESS" )
            return base + "?" + params
        }
        return "curefit://classbookingv2?productType=LIVE_FITNESS"

    }

    private shouldAutoRedirect(reqBody: any): boolean {
        const { source } = reqBody || {}
        if (source === "live-upcoming-widget" || source === "class-details-invite" || source === "order-confirmation"  ) {
            return true
        }
        return false
    }
}

export default GiftCardViewBuilder
