import { injectable } from "inversify"
import * as _ from "lodash"
import { OfferUtil } from "@curefit/base-utils"
import { Action } from "@curefit/apps-common"
import { MealAction } from "../common/views/WidgetView"
import {
    ImagePathBuilder,
    ProductPrice,
    UrlPathBuilder,
    SalesCategoryImageCategory,
    Brand
} from "@curefit/product-common"
import { IWholefitCategoryResponse, WholefitMealItem } from "./WholefitCommon"
import { WHOLE_FIT_V2_CART_LIMIT } from "../util/MealUtil"
import EatUtil, { DEFAULT_FOOD_CATEGORY_ID } from "../util/EatUtil"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { PreferredLocation, SessionInfo, UserContext } from "@curefit/userinfo-common"
import { FoodSinglePriceOfferResponse, OfferV2Lite } from "@curefit/offer-common"
import {
    CategoriesResponse, DelayMode, DelayModeReason,
    DeliveryArea,
    EatCLPTabs,
    ListingBrandIdType,
    MealSlot,
    MenuType,
    WholeFitCLPResponse
} from "@curefit/eat-common"
import { capitalizeFirstLetter, ILogger } from "@curefit/util-common"
import AppUtil from "../util/AppUtil"
import { FoodProduct as Product } from "@curefit/eat-common"

@injectable()
export class WholefitBusiness {
    buildMealPromise(products: Product[], interfaces: CFServiceInterfaces, day: string, eatSingleOffersResult: FoodSinglePriceOfferResponse, tabSlot: MenuType, availabilityProductMap: Map<string, number>, sessionInfo: SessionInfo, preferredLocation: PreferredLocation, stopOrders: boolean, userContext: UserContext, productVariantMap: {[productId: string]: Promise<Product[]>}, brandDataMap: Map<string, string>): Promise<WholefitMealItem>[] {
        const mealPromise = _.map(products, async (menu) => {
            const product =  menu
            if (_.isNil(product)) {
                interfaces.logger.error(`WholeFitMealsWidgetView error -- for productId ${menu.productId}`)
                return
            }
            const offerDetails = OfferUtil.getSingleOfferAndPrice(product, day, eatSingleOffersResult, tabSlot)
            const availability = availabilityProductMap.get(product.productId)
            const actions: (Action | MealAction)[] = this.getMealActions(product, offerDetails, day, tabSlot, preferredLocation, sessionInfo, undefined, stopOrders, {left: availability, total: undefined}, interfaces.logger, undefined, userContext, undefined)
            let image
            const imageProductId = EatUtil.getProductId(product)
            if (sessionInfo.userAgent === "DESKTOP") {
                image = UrlPathBuilder.getSingleImagePath(imageProductId, "FOOD", "HERO", product.imageVersion)
            } else {
                image = UrlPathBuilder.getSingleImagePath(imageProductId, "FOOD", product.landscapeImageExists ? "LANDSCAPE" : "HERO", product.imageVersion)
            }
            const brandName = brandDataMap ? brandDataMap.get(product.brandId) : "WHOLE.FIT"
            const imageThumbnail = UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, "FOOD", "THUMBNAIL", product.imageVersion)
            const mealItem: WholefitMealItem = {
                title: product.title,
                titleWithoutUnits: product.titleWithoutUnits,
                price: offerDetails.price,
                date: day,
                image: image,
                imageThumbnail: imageThumbnail,
                isInventorySet: availability > 0,
                stock: availability,
                actions: actions,
                offerIds: _.map(offerDetails.offers, (offer) => { return offer.offerId }),
                categoryId: EatUtil.getCategoryIdforWholefit(product),
                productId: product.productId,
                isVeg: product.attributes["isVeg"] === "TRUE",
                shipmentWeight: product.shipmentWeight,
                foodCategoryId: product.foodCategoryId ? product.foodCategoryId : DEFAULT_FOOD_CATEGORY_ID,
                qty: product.servingQty,
                unit: product.servingUnit,
                displayUnitQty: EatUtil.getQuantityTitle(product),
                variantTitle: EatUtil.getVariantTitle(product, "WHOLE_FIT"),
                listingBrand: "WHOLE_FIT",
                mealSlot: "ALL",
                brandName: brandName ? brandName : "WHOLE.FIT"
            }
            mealItem.variants = []
            mealItem.variants.push( this.getVariantData(product, availability, offerDetails, "WHOLE_FIT"))
            if (productVariantMap[product.productId]) {
                const variants: Product[] = await productVariantMap[product.productId]
                const variantData: {
                    title: string,
                    price: ProductPrice,
                    productId: string,
                    displayUnitQty: string,
                    variantTitle: string,
                    stock: number,
                    offerIds: string[]
                }[] = []
                variants.forEach( v => {
                    if (v.status === "LIVE") {
                        variantData.push(this.getVariantData(v, availabilityProductMap.get(v.productId), OfferUtil.getSingleOfferAndPrice(v, day, eatSingleOffersResult, tabSlot), "WHOLE_FIT"))
                    }
                })
                mealItem.variants.push(...variantData)
            }
            return mealItem
        })
        return mealPromise
    }

    async getWholefitCategoryData(clpDataPromise: Promise<WholeFitCLPResponse>, selectedC1: string): Promise<IWholefitCategoryResponse> {
        const clpData: WholeFitCLPResponse = await clpDataPromise
        const categories: CategoriesResponse[] = clpData.categories
        const wholefitCategories: {
            categoryId: string
            title: string
            imageUrl: string
            offerText: string
            childCategories: any
        }[] = []
        categories.forEach(category => {
            const c2s: {
                categoryId: string,
                offerText?: string,
                imageUrl: string,
                action: Action,
                title: string,
                description: string
            }[] = []
            if (category.childCategories.length) {
                const childCategory = category.childCategories
                childCategory.forEach(child => {
                    let description = ``
                    if (child.childCategories && child.childCategories.length) {
                        child.childCategories.forEach( (childCategory, index) => {
                            const isLastIndex = index === (category.childCategories.length - 1)
                            const part = isLastIndex ? " " : ", "
                            description = description + childCategory.categoryName
                            description += part
                        })
                    }

                    c2s.push({
                        categoryId: child.categoryId,
                        title: child.categoryName,
                        offerText: child.calloutText,
                        description: description,
                        imageUrl: ImagePathBuilder.getSalesCategoryImagePath(child.categoryId, "CUREFIT_WHOLEFIT", (child.imageVersionMap) ? child.imageVersionMap["HERO"] : 0, SalesCategoryImageCategory.HERO),
                        action: {
                            actionType: "NAVIGATION",
                            url: `curefit://wholefitproductpage?categoryId=${child.categoryId}&title=${child.categoryName}`
                        }
                    })
                })
            }
            wholefitCategories.push({
                categoryId: category.categoryId,
                title: category.categoryName,
                imageUrl: ImagePathBuilder.getSalesCategoryImagePath(category.categoryId, "CUREFIT_WHOLEFIT" , (category.imageVersionMap) ? category.imageVersionMap["HERO"] : 0, SalesCategoryImageCategory.HERO),
                offerText: category.calloutText,
                childCategories: c2s.length ? c2s : undefined,

            })
        })
        return { categories: wholefitCategories, selectedC1: selectedC1}
    }

    getMealActions(product: Product, offerDetails: { price: ProductPrice, offers: OfferV2Lite[], offerProduct: Product }, day: string, menuType: MenuType, preferredLocation: PreferredLocation,
                   sessionInfo: SessionInfo, clpTab: EatCLPTabs, stopOrders: boolean, availability: { left: number, total: number },
                   logger: ILogger, parentProductIdMap: { [productId: string]: string }, userContext: UserContext, cafeDeliverySlot?: string): (Action | MealAction)[] {
        const actions: (MealAction | Action)[] = []
        actions.push({
            url: `curefit://wholefitsingles?productId=${product.productId}&enableBuy=true&slot=${menuType}&date=${day}&listingBrand=WHOLE_FIT&categoryId=${product.categoryId}&launchNewInstance=true`,
            actionType: "WIDGET_NAVIGATION"
        })
        if (!AppUtil.isWeb(userContext) && !sessionInfo.isUserLoggedIn) {
            const action: Action = {
                actionType: "SHOW_ALERT_MODAL",
                title: "BUY",
                meta: {
                    title: "Login Required!",
                    subTitle: "Please login to continue",
                    actions: [{ actionType: "LOGOUT", title: "Login" }]
                }
            }
            actions.push({ ...action, title: "ADD" })
            return actions
        }
        if ((sessionInfo.isUserLoggedIn || sessionInfo.userAgent === "DESKTOP" || sessionInfo.userAgent === "MBROWSER") && preferredLocation.isInServicableArea) {
            const offerIds = _.isEmpty(offerDetails) ? [] : _.map(offerDetails.offers, offer => { return offer.offerId })

            const image = UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, product.productType, "HERO", product.imageVersion)
            const action: MealAction = {
                actionType: "BUY_MEAL",
                url: "curefit://cartcheckout",
                title: "BUY",
                date: day,
                image: image,
                productId: product.productId,
                productName: product.title,
                offerId: !_.isEmpty(offerIds) ? offerIds[0] : undefined,
                offerIds: offerIds,
                price: offerDetails.price,
                maxCartSize: WHOLE_FIT_V2_CART_LIMIT,
                stock: availability.left,
                clpTab: clpTab,
                mealSlot: {
                    id: menuType,
                    name: capitalizeFirstLetter(menuType.toLowerCase())
                },
                listingBrand: "WHOLE_FIT"
            }
            actions.push({ ...action, actionType: AppUtil.isWeb(userContext) ? "SET_WHOLEFIT_CART" : "ADD_TO_CART", title: "ADD" })
        } else {
            logger.info("Unserviceable location " + JSON.stringify(preferredLocation.latLong))
            actions.push({
                actionType: "SHOW_ALERT_MODAL",
                title: "ADD",
                meta: {
                    title: "Unserviceable Location",
                    subTitle: "We currently do not deliver to this area. Please update your location at the top of the page",
                    actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
                }
            })
        }
        if (stopOrders) {
            actions.splice(1, 2)
            actions.push({
                actionType: "SHOW_ALERT_MODAL",
                title: "CLOSED",
                meta: {
                    title: "Ordering closed",
                    subTitle: "We're currently closed for orders. Please check back with us in sometime",
                    actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
                }
            })
        }
        return actions
    }

    private getVariantData(product: Product, stock: number, offer: { price: ProductPrice, offers: OfferV2Lite[], offerProduct: Product }, listingBrand: ListingBrandIdType) {
        return {
            title: product.title,
            price: offer.price,
            offerIds: _.map(offer.offers, (offer) => { return offer.offerId }),
            productId: product.productId,
            displayUnitQty: EatUtil.getQuantityTitle(product),
            variantTitle: EatUtil.getVariantTitle(product, listingBrand),
            stock: stock
        }
    }

    public createBrandMap(brandData: Brand[], brandDataMap: Map<string, string>) {
        for (const brand of brandData) {
            if (!brandDataMap.get(brand.brandId)) {
                brandDataMap.set(brand.brandId, brand.name)
            }
        }
    }

}