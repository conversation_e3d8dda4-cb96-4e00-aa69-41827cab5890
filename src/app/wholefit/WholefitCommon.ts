import { Product, ProductPrice } from "@curefit/product-common"
import { Action } from "@curefit/apps-common"
import { MealSlot, MenuType, WholeFitAutocomplete } from "@curefit/eat-common"
import { IBaseWidget } from "@curefit/vm-models"
import { ICategoryInfo } from "./FilterProcessor"
import { MeasurementUnit } from "@curefit/food-common"

export interface WholefitMealItem {
    title: string
    titleWithoutUnits: string
    calories?: number
    price: ProductPrice
    date: string,
    image: string,
    imageThumbnail: string,
    isInventorySet: boolean
    stock: number
    actions: Action[],
    offerIds: string[]
    categoryId: string
    productId: string
    isVeg: boolean
    shipmentWeight: number
    foodCategoryId: string
    qty: number
    unit: MeasurementUnit
    displayUnitQty: string
    variantTitle?: string
    listingBrand: "WHOLE_FIT"
    mealSlot: MenuType
    variants?: {
        title: string,
        price: ProductPrice,
        productId: string,
        displayUnitQty: string,
        variantTitle?: string,
        stock: number,
        offerIds: string[]
    }[]
    brandName?: string
}


export interface IWholefitPageResponse {
    pages?: number
    widgets: IBaseWidget[]
    categoryInfo?: {
        c2CategoryInfo: ICategoryInfo,
        c1CategoryInfo: ICategoryInfo
    }
}

export interface IWholefitCategoryResponse {
    categories: {
        categoryId: string
        title: string
        imageUrl: string
        offerText: string
        childCategories: any
    }[]
    selectedC1: string
}

export interface IWholefitAutoFill extends Omit<WholeFitAutocomplete, "searchText" | "filters"> {
    action: Action
}