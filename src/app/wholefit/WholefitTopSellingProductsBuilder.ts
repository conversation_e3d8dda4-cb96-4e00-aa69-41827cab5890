import { UserContext } from "@curefit/userinfo-common"
import {
    FoodProduct as Product,
    MenuType,
    ProductsResponse,
    WholeFitCLPResponse
} from "@curefit/eat-common"
import { Action, BaseWidget } from "@curefit/vm-models"
import { WholefitMealCardWidget } from "../page/vm/widgets/WholefitMealCardWidget"
import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import kernel from "../../config/ioc/ioc"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { CFUserProfile } from "../page/vm/CFUserProfile"
import { WholefitBusiness } from "./WholefitBusiness"
import UserBusiness from "../user/UserBusiness"
import { TimeUtil } from "@curefit/util-common"
import EatUtil  from "../util/EatUtil"
import * as _ from "lodash"
import { WholefitTopSellingProductsWidget } from "@curefit/vm-models"

@injectable()
export class WholefitTopSellingProductsViewBuilder extends WholefitTopSellingProductsWidget {
    title: string
    widgets: WholefitMealCardWidget[]
    constructor() {
        super()
        this.title = "Top Selling Products"
        this.widgets = []
    }
    async buildView(interfaces?: CFServiceInterfaces, userContext?: UserContext, queryParams?: {[filter: string]: any}): Promise<BaseWidget> {
        const brandDataPromise = interfaces.brandService.getAllBrands()
        const userProfile = userContext.userProfile as CFUserProfile
        const sessionInfo = userContext.sessionInfo
        const tz = userContext.userProfile.timezone
        const day = TimeUtil.todaysDate(tz)
        const tabSlot: MenuType = "ALL"
        const productIds = []
        const brandDataMap = new Map<string, string>()
        const preferredLocation = await userProfile.wholeFitPreferredLocationPromise
        const areaId = preferredLocation.area ? preferredLocation.area.areaId : preferredLocation.defaultArea.areaId
        const parentProductIds: string[] = []
        const availabilityProductMap = new Map<string, number>()
        const productVariantMap: {[productId: string]: Promise<Product[]>} = {}
        const date = (await interfaces.wholefitService.getNextAvailableSlot(areaId)).date
        const clpResponse: WholeFitCLPResponse = await userContext.userProfile.promiseMapCache.getPromise("wholefit-v2-clp-data", { areaId: userContext.userProfile.areaId, userId: userContext.userProfile.userId, deviceId: userContext.sessionInfo.deviceId, source: userContext.sessionInfo.orderSource })
        const topSellingProducts: ProductsResponse[] = clpResponse.topSellingProducts
        if (_.isNil(topSellingProducts) || _.isEmpty(topSellingProducts)) {
            return undefined
        }
        topSellingProducts.forEach(product => {
            // if (true) { // todo Nisheet: add stock check
            parentProductIds.push(product.productId)
            availabilityProductMap.set(product.productId, product.available)
            if (product.variants) {
                const variantIds: string[] = []
                product.variants.forEach(variant => {
                    // if (true) { // todo Nisheet: add stock check
                    variantIds.push(variant.productId)
                    // }
                    availabilityProductMap.set(variant.productId, variant.available)
                })
                productVariantMap[product.productId] = interfaces.catalogueService.getProducts(variantIds)
                productIds.push(...variantIds)
            }
            // }
        })
        productIds.push(...parentProductIds)
        const eatSingleOffersResultPromise = EatUtil.getSingleOffers(interfaces, userContext, sessionInfo, "WHOLE_FIT", productIds, date )
        const brandData = await brandDataPromise

        const wholefitBusinessObject = new WholefitBusiness()
        wholefitBusinessObject.createBrandMap(brandData, brandDataMap)

        const parentProductsPromise = interfaces.catalogueService.getProducts(parentProductIds)
        const products = await parentProductsPromise
        products.filter( p => p.status === "LIVE")
        const eatSingleOffersResult = await eatSingleOffersResultPromise
        const mealItemPromises = wholefitBusinessObject.buildMealPromise(products, interfaces, day, eatSingleOffersResult, tabSlot, availabilityProductMap, sessionInfo, preferredLocation, false, userContext, productVariantMap, brandDataMap)
        const mealItems = await Promise.all(mealItemPromises)
        const mealcardWidgets = _.map(mealItems, (mealItem) => {
            return new WholefitMealCardWidget(mealItem)
        })
        this.widgets = mealcardWidgets
        return this
    }
}