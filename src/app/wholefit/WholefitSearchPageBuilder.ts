import { inject, injectable } from "inversify"
import { UserContext } from "@curefit/userinfo-common"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { CFUserProfile } from "../page/vm/CFUserProfile"
import {
    DelayMode, DelayModeReason, DeliveryArea,
    Filter, FoodProduct as Product,
    ListingBrandIdType, MealSlot,
    MenuType, SelectValue, WholeFitGetProductsResponse
} from "@curefit/eat-common"
import * as _ from "lodash"
import { Action } from "@curefit/apps-common"
import { Brand } from "@curefit/product-common"
import { BaseWidget, IBaseWidget, IServiceInterfaces } from "@curefit/vm-models"
import { IWholefitPageResponse } from "./WholefitCommon"
import { WholefitMealCardWidget } from "../page/vm/widgets/WholefitMealCardWidget"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { WholefitBusiness } from "./WholefitBusiness"
import { ICategoryInfo, WholefitFilterProcessor } from "./FilterProcessor"
import EatUtil from "../util/EatUtil"

@injectable()
export class WholefitSearchPageBuilder {
    constructor(
        @inject(CUREFIT_API_TYPES.WholefitBusiness) private wholefitBusiness: WholefitBusiness,
    ) {
    }
    async build(interfaces: CFServiceInterfaces, userContext: UserContext, searchPromise: Promise<WholeFitGetProductsResponse>, pageSize?: number): Promise<IWholefitPageResponse> {
        const brandDataPromise = interfaces.brandService.getAllBrands()
        const userProfile = userContext.userProfile as CFUserProfile
        const sessionInfo = userContext.sessionInfo
        const tz = userContext.userProfile.timezone
        const tabSlot: MenuType = "ALL"
        const listingBrand: ListingBrandIdType = "WHOLE_FIT"
        const isWeb: boolean = userContext.sessionInfo.userAgent !== "APP"
        const preferredLocation = await userProfile.preferredLocationPromise
        const searchResult: WholeFitGetProductsResponse = await searchPromise
        const selectedFilter = searchResult.appliedFilters
        const availableFilters = searchResult.availableFilters
        const numProducts = _.get(searchResult, "meta.totalAvailable")
        const numPages = Math.ceil(numProducts / pageSize)
        const productIds = []
        const parentProductIds: string[] = []
        const availabilityProductMap = new Map<string, number>()
        const productVariantMap: {[productId: string]: Promise<Product[]>} = {}
        searchResult.products.forEach(product => {
            // if (true) { // todo Nisheet: add stock check
                parentProductIds.push(product.productId)
                availabilityProductMap.set(product.productId, product.available)
                if (product.variants) {
                    const variantIds: string[] = []
                    product.variants.forEach(variant => {
                        // if (true) { // todo Nisheet: add stock check
                            variantIds.push(variant.productId)
                        // }
                        availabilityProductMap.set(variant.productId, variant.available)
                    })
                    productVariantMap[product.productId] = interfaces.catalogueService.getProducts(variantIds)
                    productIds.push(...variantIds)
                }
            // }
        })
        productIds.push(...parentProductIds)
        const parentProductsPromise = interfaces.catalogueService.getProducts(parentProductIds)
        const products = await parentProductsPromise
        products.filter( p => p.status === "LIVE")
        const areaId = preferredLocation && preferredLocation.area ? preferredLocation.area.areaId : preferredLocation.defaultArea.areaId
        const nextAvailableSlot = await interfaces.wholefitService.getNextAvailableSlot(areaId)
        const day = nextAvailableSlot.date
        const eatSingleOffersResult = await EatUtil.getSingleOffers(interfaces, userContext, sessionInfo, listingBrand, productIds, day)
        const brandData = await brandDataPromise
        const brandDataMap = new Map<string, string>()
        this.wholefitBusiness.createBrandMap(brandData, brandDataMap)
        const mealItemPromises = this.wholefitBusiness.buildMealPromise(products, interfaces, day, eatSingleOffersResult, tabSlot, availabilityProductMap, sessionInfo, preferredLocation, false,  userContext, productVariantMap, brandDataMap)
        const mealItems = await Promise.all(mealItemPromises)
        let categoryInfo
        const finalWidgets = []
        const mealWidgetPromises = _.map(mealItems, (mealItem) => {
            return new WholefitMealCardWidget(mealItem)
        })
        const isFilterWidgetRequired = this.isFilterWidgetRequired(isWeb, searchResult.pageInfo.page, _.get(searchResult, "searchInfo.searchText"))
        if (isFilterWidgetRequired && searchResult.products.length > 0) {
            let filterWidget = await new ProductFilterWidget().buildView(interfaces, userContext, undefined) as ProductFilterWidget
            if (!isWeb) {
                const categoryfilters = this.getCategoryFilters(selectedFilter, availableFilters, searchResult.quickFilters)
                categoryInfo = WholefitFilterProcessor.getCategoryInfo(interfaces, selectedFilter)
                filterWidget.appliedFilters = selectedFilter
                filterWidget.quickFilters = categoryfilters.quickFilters
                filterWidget.availableFilters = availableFilters
                finalWidgets.push(filterWidget)
            } else {
                /**
                 * Filter changes for web and mweb
                 */
                try {
                    const filterData: {
                        quickFilters: Filter[],
                        availableFilters: Filter[],
                        categoryInfo: {
                            c1CategoryInfo: ICategoryInfo,
                            c2CategoryInfo: ICategoryInfo
                        }
                    } = await this.getFilterData(interfaces, selectedFilter, availableFilters, searchResult.quickFilters)
                    filterWidget.quickFilters = filterData.quickFilters
                    filterWidget.availableFilters = filterData.availableFilters
                    filterWidget.appliedFilters = selectedFilter
                    categoryInfo = filterData.categoryInfo
                } catch (e) {
                    filterWidget = undefined
                    interfaces.rollbarService.sendError(e, {extra: { userId: userContext.userProfile.userId, widget: "Wholefit filter widget" }})
                    interfaces.logger.info(`wholefit web filter widget broke`)
                }
                if (filterWidget ) {
                    finalWidgets.push(filterWidget)
                }
            }
        }
        const mealCardWidgets = await Promise.all(mealWidgetPromises)
        finalWidgets.push(...mealCardWidgets)
        finalWidgets.filter(v => !_.isNil(v) && !_.isNull(v))
        return { pages: numPages, widgets: finalWidgets, categoryInfo: categoryInfo }
    }

    async getFilterData(interfaces: CFServiceInterfaces, appliedFilters: Filter[], availableFilters: Filter[], quickFilters: Filter[]): Promise<{
        quickFilters: Filter[],
        availableFilters: Filter[],
        categoryInfo: {
            c1CategoryInfo: ICategoryInfo,
            c2CategoryInfo: ICategoryInfo
        }
    }> {
        let categoryInfo: {
            c1CategoryInfo: ICategoryInfo,
            c2CategoryInfo: ICategoryInfo
        }

        // populates c1 and c2 based on selection
        categoryInfo = WholefitFilterProcessor.getCategoryInfo(interfaces, appliedFilters)

        let appliedFoodCategory: ICategoryInfo

        const c1Info = categoryInfo.c1CategoryInfo
        const c2Info = categoryInfo.c2CategoryInfo

        if (c2Info) {
            appliedFoodCategory = c2Info
        } else {
            appliedFoodCategory = c1Info
        }

        // adds all filter considering the correct categoryId
        if (appliedFoodCategory && appliedFoodCategory.categoryId) {
            quickFilters = this.processAllFilter(quickFilters, appliedFoodCategory.categoryId)
        } else {
            interfaces.logger.error(`wholefit web filter categoryInfo: ${JSON.stringify(categoryInfo)}`)
        }
        // only keeps the c1 tree for the selected c1/c2/c3 selected
        availableFilters = this.processAvailableFilters(availableFilters, c1Info.categoryId )

        return {
            quickFilters: quickFilters,
            availableFilters: availableFilters,
            categoryInfo: categoryInfo
        }


    }

    processAvailableFilters(availableFilters: Filter[], categoryId: string): Filter[] {
        let result: Filter[]
        for (const filter of availableFilters) {
            if (filter.key === "FOOD_CATEGORY") {
                const val = filter.values as SelectValue[]
                for (const c1 of val) {
                    if (c1.key === categoryId) {
                        filter.values = [c1]
                        result = [filter]
                        break
                    }
                }
            }
            if (result) {
                break
            }
        }
        return result
    }

    processAllFilter(quickFilters: Filter[], appliedFoodCategoryId: string) {
        quickFilters.forEach( filter => {
            // todo HACK: considering food_category as the only quick filter for now
            if (filter.key === "FOOD_CATEGORY") {
                const val = filter.values as SelectValue[]
                val.unshift({
                    title: "All",
                    key: appliedFoodCategoryId
                })
            }
        })
        return quickFilters
    }

    getCategoryFilters(appliedFilters: Filter[], availableFilters: Filter[], quickFilters: Filter[]): {
        quickFilters: Filter[],
        parentFilters: Filter[]
    } {
        const categoryFilters = availableFilters.filter((filter => filter.key === "FOOD_CATEGORY"))
        const appliedCategoryFilter = appliedFilters.filter((filter => filter.key === "FOOD_CATEGORY"))
        const filterHierarchy: Filter[] = []
        const leafFilters: Filter [] = []
        WholefitFilterProcessor.findLeafFilter(appliedCategoryFilter, leafFilters)
        leafFilters.forEach((appliedLeafFilter) => {
            const values = appliedLeafFilter.values as SelectValue[]
            const keys = values.map((value) => value.key)
            keys.forEach((key) => {
                WholefitFilterProcessor.findNestedFilters(categoryFilters, key, filterHierarchy)
            })
        })

        const allFilter =  WholefitFilterProcessor.createAllCategoryFilter(appliedCategoryFilter, filterHierarchy)
        if (quickFilters && quickFilters.length > 0 && allFilter) {
            const allFilterValues = allFilter.values as SelectValue[]
            const filter = quickFilters[0]
            const values = filter.values as SelectValue[]
            if (allFilterValues.length > 0) {
                values.unshift(allFilterValues[0])
            }
        }
        return {quickFilters: quickFilters, parentFilters: filterHierarchy}
    }

    getDelayMode(menuType: MenuType, deliveryArea: DeliveryArea): { mealSlot: MealSlot, mode: DelayMode, reason: DelayModeReason, revertDrivingScaleFactor?: number } {
        if (deliveryArea.delayMode) {
            return deliveryArea.delayMode.find((delayMode) => {
                return delayMode.mealSlot === menuType
            })
        }
    }

    isFilterWidgetRequired(isWeb: boolean, page: number, searchText: string): boolean {
        if (isWeb) {
            if (!_.isNil(searchText)) {
                return false
            }
            return true
        } else {
            if (page === 0 ) {
                return true
            } else return false
        }
    }
}

 class ProductFilterWidget extends BaseWidget {
    title?: string
    action?: Action
     quickFilters: Filter[]
     appliedFilters: Filter[]
     availableFilters: Filter[]

     constructor() {
        super("WHOLE_FIT_FILTER_WIDGET")
     }
     async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: {
         [filterName: string]: string;
     }): Promise<IBaseWidget | IBaseWidget[]> {
        return this
     }
}
