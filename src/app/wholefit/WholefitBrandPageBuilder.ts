import { inject, injectable } from "inversify"
import { WholefitBrandWidget } from "@curefit/vm-models/dist/src/models/widgets/WholefitBrandWidget"
import { PreferredLocation, SessionInfo, UserContext } from "@curefit/userinfo-common"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { BaseWidget, IBaseWidget } from "@curefit/vm-models"
import {
    BrandListingResponse, EatCLPTabs,
    FoodProduct as Product, ListingBrandIdType,
    MenuType, WholeFitGetProductsResponse
} from "@curefit/eat-common"
import { CFUserProfile } from "../page/vm/CFUserProfile"
import { TimeUtil } from "@curefit/util-common"
import * as _ from "lodash"
import AppUtil from "../util/AppUtil"
import EatUtil from "../util/EatUtil"
import { IWholefitPageResponse } from "./WholefitCommon"
import { WholefitMealCardWidget } from "../page/vm/widgets/WholefitMealCardWidget"
import { WholefitBusiness } from "./WholefitBusiness"
import { MAX_CART_SIZE } from "@curefit/base-utils"
import { capitalizeFirstLetter, ILogger } from "@curefit/base"
import { ProductPrice, UrlPathBuilder } from "@curefit/product-common"
import { MealAction } from "../common/views/WidgetView"
import { Action } from "@curefit/apps-common"
import { OfferV2Lite } from "@curefit/offer-common"

@injectable()
export class WholefitBrandPageBuilder {
    constructor(
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(CUREFIT_API_TYPES.WholefitBusiness) private wholefitBusiness: WholefitBusiness
    ) {  }

    async buildBrandPage(userContext: UserContext, resultPromise: Promise<BrandListingResponse>, interfaces: CFServiceInterfaces, pageSize: number ): Promise<IWholefitPageResponse> {
        const sessionInfo = userContext.sessionInfo
        const listingBrand: ListingBrandIdType = "WHOLE_FIT"
        const tz = userContext.userProfile.timezone
        const userProfile = userContext.userProfile as CFUserProfile
        const tabSlot: MenuType = "ALL"
        const result: BrandListingResponse = await resultPromise
        const widgetPromises = []
        const isDesktop = AppUtil.isDesktop(userContext)
        const brandDetails = result.brandDetails
        const numProducts = _.get(result, "meta.totalAvailable")
        const numPages = Math.ceil(numProducts / pageSize)
        const pageNo = result.pageInfo.page
        let brandWidgetPromise, titlePromise
        const isBrandAndTitleRequired = this.isBrandAndTitleRequired(pageNo)
        if (isBrandAndTitleRequired) {
            brandWidgetPromise = new WholefitBrandWidget().buildView(this.serviceInterfaces, userContext, {
                "brandDetails": brandDetails,
                "isDesktop": isDesktop
            })
            titlePromise = Promise.resolve({title: `${numProducts} products`, widgetType: "WHOLE_FIT_TITLE_WIDGET"} as unknown as BaseWidget)
        }
        const searchResult: WholeFitGetProductsResponse = result
        const productIds = []
        const parentProductIds: string[] = []
        const availabilityProductMap = new Map<string, number>()
        const productVariantMap: {[productId: string]: Promise<Product[]>} = {}
        searchResult.products.forEach(product => {
            // todo: have a stock check if (product.available > 0) {
                parentProductIds.push(product.productId)
                availabilityProductMap.set(product.productId, product.available)
                if (product.variants) {
                    const variantIds: string[] = []
                    product.variants.forEach(variant => {
                        // todo: have a stock check if (variant.available > 0) {
                            variantIds.push(variant.productId)
                        availabilityProductMap.set(variant.productId, variant.available)
                    })
                    productVariantMap[product.productId] = interfaces.catalogueService.getProducts(variantIds)
                    productIds.push(...variantIds)
                }
        })
        productIds.push(...parentProductIds)
        const preferredLocation = await userProfile.preferredLocationPromise
        const areaId = preferredLocation && preferredLocation.area ? preferredLocation.area.areaId : preferredLocation.defaultArea.areaId
        const nextAvailableSlot = await this.serviceInterfaces.wholefitService.getNextAvailableSlot(areaId)
        const day = nextAvailableSlot.date
        const eatSinglesOfferPromise = EatUtil.getSingleOffers(interfaces, userContext, sessionInfo, listingBrand, productIds, day)
        const parentProductsPromise = interfaces.catalogueService.getProducts(parentProductIds)
        const products = await parentProductsPromise
        const brandDataMap = new Map<string, string>()
        brandDataMap.set(brandDetails.brandId, brandDetails.name) // setting brand details for brandMap
        const eatSingleOffersResult = await eatSinglesOfferPromise
        const mealItemPromises = this.wholefitBusiness.buildMealPromise(products, interfaces, day, eatSingleOffersResult, tabSlot, availabilityProductMap, sessionInfo, preferredLocation, false, userContext, productVariantMap, brandDataMap)
        const mealItems = await Promise.all(mealItemPromises)
        const mealWidgetPromises = _.map(mealItems, (mealItem) => {
            return new WholefitMealCardWidget(mealItem).buildView()
        })
        if (brandWidgetPromise) {
            widgetPromises.push(brandWidgetPromise)
        }
        if (titlePromise) {
            widgetPromises.push(titlePromise)
        }
        widgetPromises.push(...await Promise.all(mealWidgetPromises))
        const finalWidgets: IBaseWidget[] = await Promise.all(widgetPromises)
        finalWidgets.filter( v => !_.isNil(v) && !_.isNull(v))
        return {pages: numPages, widgets: finalWidgets}
    }
    getMealActions(product: Product, offerDetails: { price: ProductPrice, offers: OfferV2Lite[], offerProduct: Product }, day: string, menuType: MenuType, preferredLocation: PreferredLocation,
                   sessionInfo: SessionInfo, clpTab: EatCLPTabs, stopOrders: boolean, availability: { left: number, total: number },
                   logger: ILogger, parentProductIdMap: { [productId: string]: string }, isOnboardingCheckoutSupported: boolean, userContext: UserContext, cafeDeliverySlot?: string): (Action | MealAction)[] {
        const actions: (MealAction | Action)[] = []
        actions.push({
            url: `curefit://wholefitsingles/${product.productId}`,
            actionType: "WIDGET_NAVIGATION"
        })
        if (!AppUtil.isWeb(userContext) && !isOnboardingCheckoutSupported && !sessionInfo.isUserLoggedIn) {
            const action: Action = {
                actionType: "SHOW_ALERT_MODAL",
                title: "BUY",
                meta: {
                    title: "Login Required!",
                    subTitle: "Please login to continue",
                    actions: [{ actionType: "LOGOUT", title: "Login" }]
                }
            }
            actions.push({ ...action, title: "ADD" })
            return actions
        }
        if ((sessionInfo.isUserLoggedIn || sessionInfo.userAgent === "DESKTOP" || sessionInfo.userAgent === "MBROWSER") && preferredLocation.isInServicableArea) {
            const offerIds = _.isEmpty(offerDetails) ? [] : _.map(offerDetails.offers, offer => { return offer.offerId })
            const image = UrlPathBuilder.getSingleImagePath(product.productId, product.productType, "THUMBNAIL", product.imageVersion)
            const action: MealAction = {
                actionType: "BUY_MEAL",
                url: "curefit://cartcheckout",
                title: "BUY",
                date: day,
                image: image,
                productId: product.productId,
                productName: product.title,
                offerId: !_.isEmpty(offerIds) ? offerIds[0] : undefined,
                offerIds: offerIds,
                price: offerDetails.price,
                maxCartSize: MAX_CART_SIZE,
                stock: Math.min(MAX_CART_SIZE, availability.left),
                clpTab: clpTab,
                mealSlot: {
                    id: menuType,
                    name: capitalizeFirstLetter(menuType.toLowerCase())
                },
                listingBrand: "WHOLE_FIT"
            }
            actions.push({ ...action, actionType: "ADD_TO_CART", title: "ADD" })
        } else {
            logger.info("Unserviceable location " + JSON.stringify(preferredLocation.latLong))
            actions.push({
                actionType: "SHOW_ALERT_MODAL",
                title: "ADD",
                meta: {
                    title: "Unserviceable Location",
                    subTitle: "We currently do not deliver to this area. Please update your location at the top of the page",
                    actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
                }
            })
        }
        if (stopOrders) {
            actions.splice(1, 2)
            actions.push({
                actionType: "SHOW_ALERT_MODAL",
                title: "CLOSED",
                meta: {
                    title: "Ordering closed",
                    subTitle: "We're currently closed for orders. Please check back with us in sometime",
                    actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
                }
            })
        }
        return actions
    }

    isBrandAndTitleRequired(pageNo: number): boolean {
        return pageNo === 0
    }
}