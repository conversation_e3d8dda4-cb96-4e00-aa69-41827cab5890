import { inject, injectable } from "inversify"
import { UserContext } from "@curefit/userinfo-common"
import { FoodProduct, ProductDetailsResponse } from "@curefit/eat-common"
import { FoodSinglePriceOfferResponse } from "@curefit/offer-common"
import { ProductPrice } from "@curefit/product-common"
import {
    createWholefitCategoryUrl,
    FssaiLicenseWidget,
    WholefitAboutBrandWidget,
    WholefitProductDescriptionWidget,
    WholefitProductWidget,
} from "@curefit/vm-models"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { TimeUtil } from "@curefit/util-common"
import * as _ from "lodash"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { ActionType } from "@curefit/apps-common"
import AppUtil from "../util/AppUtil"
import { ProductDetailPage } from "../common/views/WidgetView"
import { OfferUtil } from "@curefit/base-utils"
import EatUtil from "../util/EatUtil"
import { FINANCE_MODELS_TYPES, SellerService } from "@curefit/finance-models"
@injectable()
export class WholefitProductDetailViewBuilder {
    constructor(
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(FINANCE_MODELS_TYPES.SellerService) private sellerService: SellerService,
    ) { }

    async build(userContext: UserContext, productDetail: ProductDetailsResponse,
        singleOffersPromise: Promise<FoodSinglePriceOfferResponse>, variantPromise: Promise<FoodProduct[]>, productIds: string[]):
        Promise<ProductDetailPage> {
        const preferredLocation = await userContext.userProfile.preferredLocationPromise
        const areaId = preferredLocation && preferredLocation.area ? preferredLocation.area.areaId : preferredLocation.defaultArea.areaId
        const nextAvailableSlot = await this.serviceInterfaces.wholefitService.getNextAvailableSlot(areaId)
        const date = nextAvailableSlot.date
        const mealSlot = "ALL"
        const variants = await variantPromise
        variants.filter(v => v.status === "LIVE")
        // populating the display quantity title
        variants.forEach(product => {
            product.variantTitle = EatUtil.getQuantityTitle(product)
        })
        const productMap = _.keyBy(variants, v => v.productId)
        const productOfferMap = new Map<string, ProductPrice>()
        const availabilityMap = new Map<string, number>()
        const parentProduct = productDetail.productResponse
        const catalogProduct = productMap[parentProduct.productId]
        const mainProductId = catalogProduct.parentProductId ? catalogProduct.parentProductId : catalogProduct.productId
        let allVariantsUnavailable = true
        const eatSingleOffersResult = await singleOffersPromise
        const parentProductPrice = OfferUtil.getSingleOfferAndPrice(catalogProduct, date, eatSingleOffersResult, mealSlot)
        productOfferMap.set(catalogProduct.productId, parentProductPrice.price)
        allVariantsUnavailable = parentProduct.available === 0
        availabilityMap.set(mainProductId, parentProduct.available)
        const widgetPromises = []
        const variantProducts = parentProduct.variants
        variantProducts.forEach(product => {
            const variantProductPrice = OfferUtil.getSingleOfferAndPrice(productMap[product.productId], date, eatSingleOffersResult, mealSlot)
            availabilityMap.set(product.productId, product.available)
            productOfferMap.set(product.productId, variantProductPrice.price)
            allVariantsUnavailable = product.available === 0
        })
        const brandDetails = productDetail.brandDetails
        const productWidget = new WholefitProductWidget()
        const actionType: ActionType = AppUtil.isWeb(userContext) ? "SET_WHOLEFIT_CART" : "ADD_TO_CART"
        productWidget.buildView(this.serviceInterfaces, userContext, {
            "variants": productMap,
            "productOfferMap": productOfferMap,
            "availabilityMap": availabilityMap,
            "title": catalogProduct.titleWithoutUnits,
            "isVeg": (catalogProduct.attributes.isVeg) === "TRUE",
            "productIds": productIds,
            "foodProduct": catalogProduct,
            "actionType": actionType,
            "brandName": brandDetails && brandDetails.name ? brandDetails.name : "",
            "categoryId": EatUtil.getCategoryIdforWholefit(catalogProduct) // assuming categoryId is same for all variants
        })
        productWidget.allVariantsUnavailable = allVariantsUnavailable
        const productBenefits = _.get(catalogProduct, "callouts[0].value")
        widgetPromises.push(productWidget)
        const productDescriptionWidget = new WholefitProductDescriptionWidget().buildView(this.serviceInterfaces, userContext, {
            "benefits": productBenefits,
            "about": _.get(catalogProduct, "subTitle")
        })
        widgetPromises.push(productDescriptionWidget)

        if (brandDetails) {
            const aboutBrandWidget = new WholefitAboutBrandWidget().buildView(this.serviceInterfaces, userContext, {
                "brandId": brandDetails.brandId,
                "description": brandDetails.description,
                "title": brandDetails.name
            })
            widgetPromises.push(aboutBrandWidget)
        }

        const fssaiLicenseNumber = await EatUtil.getFssaiNumber(catalogProduct.sellerId, this.sellerService, this.serviceInterfaces.deliveryAreaService, this.serviceInterfaces.cityService, await userContext.userProfile.preferredLocationPromise)
        if (!_.isNil(fssaiLicenseNumber)) {
            const licenseWidget = new FssaiLicenseWidget(fssaiLicenseNumber).buildView(undefined, userContext, undefined)
            widgetPromises.push(licenseWidget)
        }

        const finalWidgets = [...await Promise.all(widgetPromises)]
        finalWidgets.filter( w => !_.isNil(w) && !_.isNull(w))
        return {
            widgets: finalWidgets,
            pageActions: [],
            actions: []
        }
    }
}