import { controller, httpGet, httpPost } from "inversify-express-utils"
import * as express from "express"
import * as Inversify from "inversify"
import { BASE_TYPES, capitalizeFirstLetter, ILogger } from "@curefit/base"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { inject } from "inversify"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { UserContext } from "@curefit/userinfo-common"
import { Filter, PageInfo, WholeFitAutocomplete } from "@curefit/eat-common"
import AuthMiddleware from "../auth/AuthMiddleware"
import * as _ from "lodash"
import { CacheHelper } from "../util/CacheHelper"
import { IUserService, USER_CLIENT_TYPES } from "@curefit/user-client"
import IAuthBusiness from "../auth/IAuthBusiness"
import { WHOLE_FIT_DEFAULT_PAGE_NO, WHOLE_FIT_V2_PAGE_SIZE } from "../util/AppUtil"
import { CFUserProfile } from "../page/vm/CFUserProfile"
import IUserBusiness from "../user/IUserBusiness"
import { WholefitBusiness } from "./WholefitBusiness"
import { WholefitBrandPageBuilder } from "./WholefitBrandPageBuilder"
import { WholefitSearchPageBuilder } from "./WholefitSearchPageBuilder"
import { PromiseCache } from "../util/VMUtil"
import { IWholefitAutoFill, IWholefitCategoryResponse, IWholefitPageResponse } from "./WholefitCommon"
import { PreferredLocation } from "@curefit/vm-models"
import { WHOLE_FIT_API_CLIENT_TYPES, IWholeFitService } from "@curefit/wholefit-api-client"
import { ErrorFactory } from "@curefit/error-client"
import { ErrorCodes } from "../error/ErrorCodes"

export function WholefitControllerFactory(kernel: Inversify.Container) {
    @controller("/wholefit", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class WholefitController {
        constructor(
            @Inversify.inject(BASE_TYPES.ILogger) private logger: ILogger,
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
            @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
            @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
            @inject(CUREFIT_API_TYPES.AuthBusiness) private authBusiness: IAuthBusiness,
            @inject(CUREFIT_API_TYPES.UserBusiness) private userBusiness: IUserBusiness,
            @inject(CUREFIT_API_TYPES.WholefitBusiness) private wholefitBusiness: WholefitBusiness,
            @inject(CUREFIT_API_TYPES.WholefitBrandPageBuilder) private wholefitPageBuilder: WholefitBrandPageBuilder,
            @inject(CUREFIT_API_TYPES.WholefitSearchPageBuilder) private wholefitSearchPageBuilder: WholefitSearchPageBuilder,
            @inject(WHOLE_FIT_API_CLIENT_TYPES.IWholeFitService) public wholefitService: IWholeFitService,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        ) {
        }

        /**
         * Please populate userProfile.preferredLocationPromise only as some of the utils are using preferredLocationPromise only by default
         */
        @httpGet("/brand/:brandId")
        public async getBrandPageData(req: express.Request, res: express.Response): Promise<IWholefitPageResponse> {
            const brandId = req.params.brandId as string
            const size = Number(req.query.size)
            const pageNo = Number(req.query.page)
            const pageInfo: PageInfo = {
                page: pageNo ? pageNo : WHOLE_FIT_DEFAULT_PAGE_NO,
                size: size ?  size : WHOLE_FIT_V2_PAGE_SIZE
            }
            const userContext = req.userContext as UserContext
            const userProfile = userContext.userProfile as CFUserProfile
            const preferredLocationPromise = this.getPreferredLocation(userContext)
            userProfile.preferredLocationPromise = preferredLocationPromise
            const preferredLocation = await userProfile.preferredLocationPromise
            const areaId = preferredLocation.area ? preferredLocation.area.areaId : preferredLocation.defaultArea.areaId
            this.serviceInterfaces.logger.info(`wholefit/brand = ${JSON.stringify(preferredLocation)}`)
            const resultPromise = this.serviceInterfaces.wholefitService.getBrandListingDetails(brandId, areaId, undefined, undefined, undefined, pageInfo)
            return await this.wholefitPageBuilder.buildBrandPage(userContext, resultPromise, this.serviceInterfaces, size ? size : WHOLE_FIT_V2_PAGE_SIZE)
        }

        @httpPost("/search")
        private async getSearchData(req: express.Request, res: express.Response): Promise<IWholefitPageResponse> {
            const userContext = req.userContext as UserContext
            const pageNo = Number(req.body.page)
            const size = Number(req.body.size)
            const searchTerm = _.get(req, "body.searchText", undefined)
            const preferredLocationPromise = this.getPreferredLocation(userContext)
            const userProfile = userContext.userProfile as CFUserProfile
            userProfile.preferredLocationPromise = preferredLocationPromise
            const preferredLocation = await userProfile.preferredLocationPromise
            userContext.userProfile = userProfile
            const areaId = preferredLocation.area ? preferredLocation.area.areaId : preferredLocation.defaultArea.areaId
            const pageInfo: PageInfo = {
                page: pageNo ? pageNo : WHOLE_FIT_DEFAULT_PAGE_NO,
                size: size ? size : WHOLE_FIT_V2_PAGE_SIZE
            }
            const appliedFilters: Filter[] = _.get(req, "body.appliedFilters") ? req.body.appliedFilters : []
            const categoryId = req.body.categoryId
            const categoryFilter = appliedFilters.find((filter) => filter.key === "FOOD_CATEGORY")
            if (categoryId && !categoryFilter) {
                appliedFilters.push({
                    type: "MULTISELECT",
                    key: "FOOD_CATEGORY",
                    title: undefined,
                    values: [{
                        key: categoryId,
                        title: capitalizeFirstLetter(categoryId.toLowerCase())
                    }]
                })
            }
            this.serviceInterfaces.logger.info(`wholefit/search = ${JSON.stringify(preferredLocation)}`)
            const resultPromise = this.serviceInterfaces.wholefitService.getProducts(areaId, { searchText: searchTerm }, appliedFilters, undefined, pageInfo, searchTerm && searchTerm.length > 0 ? false : true)
            return this.wholefitSearchPageBuilder.build(this.serviceInterfaces, userContext, resultPromise, size ? size : WHOLE_FIT_V2_PAGE_SIZE)
        }

        @httpGet("/categories/:c1")
        async getWholefitCategoryData(req: express.Request): Promise<IWholefitCategoryResponse> {
            const selectedC1 = req.params.c1
            const userContext = req.userContext as UserContext
            if (!userContext.userProfile.promiseMapCache) {
                userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
            }
            const preferredLocation = await this.getPreferredLocation(userContext)
            const areaId = preferredLocation.area ? preferredLocation.area.areaId : preferredLocation.defaultArea.areaId
            const clpDataPromise = userContext.userProfile.promiseMapCache.getPromise("wholefit-v2-clp-data", { areaId: areaId })
            return this.wholefitBusiness.getWholefitCategoryData(clpDataPromise, selectedC1)
        }

        @httpGet("/autofill")
        @httpGet("/autofill/:term")
        async getWholefitAutofill(req: express.Request): Promise<IWholefitAutoFill[]> {
            const term = req.query.searchText
            const term2 = req.params.term
            const finalTerm = term ? term : term2
            const answers: any[] = []

            if (_.isNil(finalTerm)) {
                return answers
            }
            const autofillResultPromise = this.wholefitService.getAutocompleteResponse(finalTerm)
            const autofillResult: WholeFitAutocomplete[] = await autofillResultPromise
            if (autofillResult && autofillResult.length) {
                autofillResult.forEach(result => {
                    if (result.type === "PRODUCT") {
                        const productId = _.get(result, "filters[0].values[0].key")
                        answers.push({
                            displayText: result.displayText,
                            type: "PRODUCT",
                            action: {
                                actionType: "NAVIGATION",
                                url: `curefit://wholefitsingles?productId=${productId}&listingBrand=WHOLE_FIT`
                            }
                        })
                    } else {
                        answers.push(result)
                    }
                })
            }
            return answers
        }

        private async getPreferredLocation(userContext: UserContext): Promise<PreferredLocation> {
            const preferredLocation = await this.userBusiness.getPreferredLocation(userContext, userContext.userProfile.userId,
                userContext.sessionInfo.sessionData, userContext.sessionInfo.lon, userContext.sessionInfo.lat,
                undefined, true, "WHOLE_FIT", userContext.sessionInfo.userAgent)
            if (preferredLocation) {
                return preferredLocation
            } else {
                throw this.errorFactory.withCode(ErrorCodes.AREA_NOT_SERVICEABLE_ERR, 400).withDebugMessage("Area not serviceable").build()
            }
        }
    }
}
