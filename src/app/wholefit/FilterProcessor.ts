import { injectable } from "inversify"
import { Filter, SalesCategory, SelectValue } from "@curefit/eat-common"
import * as _ from "lodash"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"

const clone = require("clone")

export class FilterProcessor {

    static findNestedFilters(filters: Filter[], key: string, filterHierarchy: Filter[]): {nestedFilter: Filter[], hasKey: boolean} {
        let nestedFilter: Filter[] = []
        let hasKey = false
        filters.forEach((appliedFilter) => {
            const values = appliedFilter.values as SelectValue[]
            values.forEach((value) => {
                if (value.key === key) {
                    hasKey = true
                    filterHierarchy.push(appliedFilter)
                    if (value.nestedFilters) {
                        nestedFilter = value.nestedFilters
                    } else {
                        nestedFilter = [appliedFilter]
                    }
                } else if (value.nestedFilters) {
                   const nestedFilterResponse = this.findNestedFilters(value.nestedFilters, key, filterHierarchy)
                    if (nestedFilterResponse.hasKey) {
                        const filter: Filter = {
                            type: appliedFilter.type,
                            title: appliedFilter.title,
                            key: appliedFilter.key,
                            values: [value]
                        }
                        filterHierarchy.push(filter)
                        hasKey = true
                    }
                }
            })
        })
        return {nestedFilter, hasKey}
    }

    static findLeafFilter(filters: Filter[], leafFilters: Filter[]) {
        filters.forEach((appliedFilter) => {
            const values = appliedFilter.values as SelectValue[]
            values.forEach((value) => {
                if (value.nestedFilters) {
                    this.findLeafFilter(value.nestedFilters, leafFilters)
                } else {
                    leafFilters.push(appliedFilter)
                }
            })
        })
    }
}

export interface ICategoryInfo {
    title: string,
    categoryId: string
}

export class WholefitFilterProcessor extends FilterProcessor {

    static getCategoryInfo(interfaces: CFServiceInterfaces, appliedFilters: Filter[]): {c1CategoryInfo: ICategoryInfo, c2CategoryInfo: ICategoryInfo } {
        let c2CategoryInfo: SalesCategory
        let c1CategoryInfo: SalesCategory

        let appliedCategoryId
        appliedFilters.forEach((filter) => {
            const values = filter.values as SelectValue[]
            appliedCategoryId = values[0].key
        })

        const parentCategories = interfaces.salesCategoryService.getAllParentCategoriesInOrder(appliedCategoryId, "CUREFIT_WHOLEFIT")

        if (parentCategories.length > 1) {
            c2CategoryInfo = parentCategories[0]
            c1CategoryInfo = parentCategories[1]
        } else if (parentCategories.length > 0) {
            c2CategoryInfo = interfaces.salesCategoryService.getSalesCategoryById(appliedCategoryId, "CUREFIT_WHOLEFIT")
            c1CategoryInfo = parentCategories[0]
        } else {
            c1CategoryInfo = interfaces.salesCategoryService.getSalesCategoryById(appliedCategoryId, "CUREFIT_WHOLEFIT")
        }
        return { c1CategoryInfo: c1CategoryInfo ? { title: c1CategoryInfo.name, categoryId: c1CategoryInfo.categoryId} : undefined, c2CategoryInfo: c2CategoryInfo ? { title: c2CategoryInfo.name, categoryId: c2CategoryInfo.categoryId} : undefined }
    }

    static createAllCategoryFilter(appliedFilters: Filter[], parentFilters: Filter[]): Filter {
        if (appliedFilters.length > 0) {
            const categoryFilter: Filter = parentFilters.length > 2 ?  clone(parentFilters[1]) : clone(appliedFilters[0])
            const selectValues = clone(categoryFilter.values) as SelectValue[]
            categoryFilter.values = selectValues.map((value) => {
                value.title = "All"
                value.nestedFilters = undefined
                return value
            })
            return categoryFilter
        }
    }
}
