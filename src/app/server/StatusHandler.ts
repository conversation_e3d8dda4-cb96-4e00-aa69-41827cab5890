import { inject, injectable } from "inversify"
import { IInMemoryCacheService } from "@curefit/memory-cache"
import { DELIVERY_CLIENT_TYPES } from "@curefit/delivery-client"
import { PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { RIDDLER_CACHE_TYPES, ChallengeCache } from "@curefit/riddler-cache"
import { ErrorFactory } from "@curefit/error-client"
import { ErrorCodes } from "../error/ErrorCodes"
import IssueService from "../crm/IssueService"

const os = require("os")

export interface StatusResponse {
    status: string
    pid: number
}

@injectable()
export class StatusHandler {

    readonly #instanceId: string

    constructor(
        @inject(DELIVERY_CLIENT_TYPES.DeliverySlotService) private deliverySlotService: IInMemoryCacheService,
        @inject(PAGE_CONFIG_TYPES.ConfigService) private configService: IInMemoryCacheService,
        @inject(CUREFIT_API_TYPES.CFAPICityService) private cfapiCityService: IInMemoryCacheService,
        @inject(RIDDLER_CACHE_TYPES.ChallengeCache) private challengeCache: ChallengeCache,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        @inject(CUREFIT_API_TYPES.IssueService) private issueService: IssueService
    ) {
        this.#instanceId = os.hostname()
    }

    public async getStatus(): Promise<StatusResponse> {
        //     if (!this.segmentationService.isSDKReady()) {
        //         throw new DataErrorV2({ message: "SDK is not ready yet", context: {service: "Hansel SDK"} })
        //     }
        if (!this.cfapiCityService.isCacheReady()) {
            throw this.errorFactory.withCode(ErrorCodes.CACHE_NOT_LOADED_ERR, 400).withDebugMessage("CfapiCityService cache not loaded yet").withContext({ service: "CfapiCityService" }).build()
        }
        if (!this.deliverySlotService.isCacheReady()) {
            throw this.errorFactory.withCode(ErrorCodes.CACHE_NOT_LOADED_ERR, 400).withDebugMessage("DeliverySlotService cache not loaded yet").withContext({ service: "DeliverySlotService" }).build()
        }
        if (!this.configService.isCacheReady()) {
            throw this.errorFactory.withCode(ErrorCodes.CACHE_NOT_LOADED_ERR, 400).withDebugMessage("ConfigService cache not loaded yet").withContext({ service: "ConfigService" }).build()
        }
        if (!this.challengeCache.isCacheReady()) {
            throw this.errorFactory.withCode(ErrorCodes.CACHE_NOT_LOADED_ERR, 400).withDebugMessage("RiddlerChallengeCache cache not loaded yet").withContext({ service: "RiddlerChallengeCache" }).build()
        }
        return Promise.resolve({
            status: "ok",
            pid: process.pid,
            iId: this.#instanceId

        })
    }
}
