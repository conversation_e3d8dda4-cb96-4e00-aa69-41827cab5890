import { inject, injectable } from "inversify"
import { BASE_TYPES, FetchUtil } from "@curefit/base"
const fetch = require("node-fetch")

export interface SpotInstanceActionResponse {
    action: "stop" | "terminate"
    time: string
}

@injectable()
export class SpotHandler {

    constructor(
        @inject(BASE_TYPES.FetchUtil) protected fetchUtil: FetchUtil,
    ) { }

    public async isMarkedForTermination(): Promise<boolean> {
        try {
            const actionResponse = await fetch("http://169.254.169.254/latest/meta-data/spot/instance-action")
            const actionResponseJson = await this.fetchUtil.parseResponse<SpotInstanceActionResponse>(actionResponse)
            if (!actionResponseJson) {
                return false
            }
            return actionResponseJson.action === "stop" || actionResponseJson.action === "terminate" || false
        } catch (e) {
            // ignore
            return false
        }
    }
}
