import { isIPv4 } from "net"
import { Container, inject } from "inversify"
import { controller, httpGet } from "inversify-express-utils"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { StatusHandler, StatusResponse } from "./StatusHandler"
import { Logger } from "@curefit/base"
import { BASE_TYPES, FetchUtil } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { FitbitCronService } from "../atlas/FitbitCronService"
// import { monitorEventLoopDelay } from "perf_hooks"
import * as express from "express"
import { ILoggingService, LOGGING_CLIENT_TYPES } from "@curefit/logging-client"
import { CacheHelper } from "../util/CacheHelper"
import * as murmurhash from "murmurhash"
import { ICrudKeyValue, IMultiCrudKeyValue, REDIS_TYPES } from "@curefit/redis-utils"
import { ICountryReadOnlyDao, ICountryService, LOCATION_TYPES } from "@curefit/location-mongo"
import { BASE_MONGO_TYPES } from "@curefit/base-mongo"
import { Constants } from "@curefit/base-utils"
import { IQueueService, SQS_CLIENT_TYPES } from "@curefit/sqs-client"

const { writeHeapSnapshot } = require("v8")
// const histogram = monitorEventLoopDelay({ resolution: 10 })

const fetch = require("node-fetch")

export default function statusControllerFactory(kernel: Container) {
    @controller("/status")
    class StatusController {
        private redisCrudDao: ICrudKeyValue
        private jsonData = [{"_id": "6169832b1bc682900918127c", "index": 0, "guid": "64b44504-d34d-414a-b60f-0ba5879ecb1a", "isActive": true, "balance": "$2,377.16", "picture": "http://placehold.it/32x32", "age": 37, "eyeColor": "green", "name": "Oneal Pate", "gender": "male", "company": "RODEOMAD", "email": "<EMAIL>", "phone": "+****************", "address": "897 Polhemus Place, Beaverdale, Minnesota, 2454", "about": "Incididunt aliqua reprehenderit consequat laborum Lorem consequat deserunt nulla culpa proident in qui consequat in. Cupidatat pariatur esse esse enim cupidatat reprehenderit minim cillum in et incididunt consectetur aliquip. Nulla eu consequat nulla est et pariatur occaecat sunt ipsum.\r\n", "registered": "2017-03-20T06:49:34 -06:-30", "latitude": 3.861874, "longitude": -13.220347, "tags": ["et", "nisi", "qui", "sunt", "magna", "quis", "sint"], "friends": [{"id": 0, "name": "Denise Reese"}, {"id": 1, "name": "William Herring"}, {"id": 2, "name": "Bessie Finch"}], "greeting": "Hello, Oneal Pate! You have 9 unread messages.", "favoriteFruit": "strawberry"}, {"_id": "6169832b81c68f1f47b05ca4", "index": 1, "guid": "d6774daf-70cf-455f-8a1f-eb0287766fb2", "isActive": false, "balance": "$1,803.77", "picture": "http://placehold.it/32x32", "age": 29, "eyeColor": "green", "name": "Rosanna Estes", "gender": "female", "company": "GEOFORMA", "email": "<EMAIL>", "phone": "+****************", "address": "375 Glen Street, Mooresburg, Maine, 5833", "about": "Quis cupidatat proident occaecat consequat ad cillum quis aute consectetur tempor ad. Ad magna velit irure nulla velit amet dolor irure in mollit aliqua dolor. Commodo nulla sit commodo ad deserunt pariatur consectetur ea. Ad id voluptate eu cillum dolor consequat officia exercitation aute qui. Laboris enim velit esse nostrud excepteur aliquip laborum esse sit deserunt pariatur aliquip esse proident.\r\n", "registered": "2018-08-12T09:33:00 -06:-30", "latitude": -38.110842, "longitude": 69.321646, "tags": ["eu", "cupidatat", "adipisicing", "cupidatat", "reprehenderit", "proident", "nulla"], "friends": [{"id": 0, "name": "Castaneda Hammond"}, {"id": 1, "name": "Forbes Golden"}, {"id": 2, "name": "Louella Lara"}], "greeting": "Hello, Rosanna Estes! You have 9 unread messages.", "favoriteFruit": "banana"}, {"_id": "6169832bfb3ad6fe4fec08e1", "index": 2, "guid": "51d59732-eadc-4d4b-9471-b2266a38259e", "isActive": true, "balance": "$1,312.35", "picture": "http://placehold.it/32x32", "age": 40, "eyeColor": "blue", "name": "Spears Reynolds", "gender": "male", "company": "LEXICONDO", "email": "<EMAIL>", "phone": "+****************", "address": "641 Thatford Avenue, Madaket, Pennsylvania, 6334", "about": "Lorem do mollit aliquip ad duis in. Amet culpa et dolor aute dolore reprehenderit consequat velit in minim. Cillum duis sint dolor consequat quis adipisicing laborum nulla ex et adipisicing fugiat.\r\n", "registered": "2019-06-20T05:57:22 -06:-30", "latitude": 44.221354, "longitude": -70.346264, "tags": ["in", "duis", "deserunt", "nulla", "ipsum", "sint", "officia"], "friends": [{"id": 0, "name": "Georgia Townsend"}, {"id": 1, "name": "Lenora Ayala"}, {"id": 2, "name": "Rosario Lloyd"}], "greeting": "Hello, Spears Reynolds! You have 2 unread messages.", "favoriteFruit": "strawberry"}, {"_id": "6169832b6ba0aef1f7f4b84c", "index": 3, "guid": "270ddf0c-8664-49f1-81fa-49bfd887977f", "isActive": true, "balance": "$1,141.51", "picture": "http://placehold.it/32x32", "age": 36, "eyeColor": "green", "name": "Deanna Francis", "gender": "female", "company": "VURBO", "email": "<EMAIL>", "phone": "+****************", "address": "188 Montieth Street, Caberfae, South Dakota, 5432", "about": "Sunt exercitation aliquip occaecat amet aute proident tempor irure est duis reprehenderit pariatur voluptate. Dolor veniam pariatur do enim anim reprehenderit anim amet dolore aliqua. Aliquip non deserunt quis dolor aute proident culpa labore occaecat dolore. Ex magna amet sit do anim. Ea qui qui cillum veniam amet consectetur laborum officia deserunt nulla excepteur eiusmod. Elit incididunt dolor velit voluptate aliquip. Proident duis nisi tempor ipsum.\r\n", "registered": "2019-12-09T06:52:38 -06:-30", "latitude": -87.399755, "longitude": -9.078153, "tags": ["eiusmod", "reprehenderit", "consequat", "velit", "consectetur", "do", "dolor"], "friends": [{"id": 0, "name": "Suzanne Irwin"}, {"id": 1, "name": "Nieves Rutledge"}, {"id": 2, "name": "Socorro Carr"}], "greeting": "Hello, Deanna Francis! You have 9 unread messages.", "favoriteFruit": "banana"}, {"_id": "6169832ba02264e7c74cb45b", "index": 4, "guid": "0aae338b-3d25-4224-8893-cc6d8c35c69e", "isActive": false, "balance": "$3,900.54", "picture": "http://placehold.it/32x32", "age": 25, "eyeColor": "green", "name": "Eliza Griffith", "gender": "female", "company": "VOLAX", "email": "<EMAIL>", "phone": "+****************", "address": "754 Bouck Court, Yukon, Florida, 1283", "about": "Aliqua tempor tempor cupidatat laborum quis exercitation adipisicing velit. Voluptate mollit consectetur esse excepteur ex cillum culpa enim ea. Ad Lorem laborum et laborum. Cupidatat laboris do exercitation ullamco ea Lorem reprehenderit et reprehenderit sint excepteur dolor. Aute tempor ea dolore est nisi non culpa. Anim aute et mollit pariatur minim non enim. Dolore ullamco deserunt tempor qui eu culpa sunt occaecat ut do non dolor.\r\n", "registered": "2015-10-11T02:30:49 -06:-30", "latitude": 75.633787, "longitude": 112.862764, "tags": ["excepteur", "adipisicing", "ipsum", "dolore", "dolor", "qui", "ut"], "friends": [{"id": 0, "name": "Dollie Sawyer"}, {"id": 1, "name": "Christie Stafford"}, {"id": 2, "name": "Esperanza England"}], "greeting": "Hello, Eliza Griffith! You have 9 unread messages.", "favoriteFruit": "banana"}, {"_id": "6169832bdf50daa197b6cfad", "index": 5, "guid": "40d7570f-4aab-414b-94ec-71ce4778df72", "isActive": true, "balance": "$2,927.72", "picture": "http://placehold.it/32x32", "age": 29, "eyeColor": "brown", "name": "Hurley Kidd", "gender": "male", "company": "BEADZZA", "email": "<EMAIL>", "phone": "+****************", "address": "703 Bank Street, Greenwich, Georgia, 4024", "about": "Labore reprehenderit adipisicing ullamco sunt cillum occaecat et mollit anim anim veniam excepteur mollit. Tempor tempor proident eu cupidatat est labore ad elit cupidatat. Nulla dolor deserunt ut sint quis esse nulla nostrud do id. Laboris voluptate ullamco incididunt anim. Culpa dolor dolor esse enim qui cupidatat pariatur ex. Adipisicing consectetur commodo fugiat velit ea.\r\n", "registered": "2017-01-27T04:41:32 -06:-30", "latitude": -41.085404, "longitude": -104.390585, "tags": ["duis", "mollit", "reprehenderit", "laborum", "sint", "proident", "irure"], "friends": [{"id": 0, "name": "Cherry Farmer"}, {"id": 1, "name": "Kemp Coleman"}, {"id": 2, "name": "Christina Harrison"}], "greeting": "Hello, Hurley Kidd! You have 2 unread messages.", "favoriteFruit": "banana"}]

        constructor(
            @inject(CUREFIT_API_TYPES.StatusHandler) protected statusHandler: StatusHandler,
            @inject(BASE_TYPES.FetchUtil) protected fetchUtil: FetchUtil,
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
            @inject(LOGGING_CLIENT_TYPES.LoggingService) private loggingService: ILoggingService,
            @inject(CUREFIT_API_TYPES.CacheHelper) protected cacheHelper: CacheHelper,
            @inject(REDIS_TYPES.MultiCrudKeyValueDao) private multiCrudKeyValueDao: IMultiCrudKeyValue,
            @inject(LOCATION_TYPES.CountryReadOnlyDao) private countryDao: ICountryReadOnlyDao,
            @inject(SQS_CLIENT_TYPES.QueueService) private queueService: IQueueService
        ) {
            this.redisCrudDao = this.multiCrudKeyValueDao.getICrudKeyValue("DEFAULT")
        }

        @httpGet("/")
        public async getStatus(request: any): Promise<StatusResponse> {
            const localStatus = await this.statusHandler.getStatus()
            return localStatus
            /*
                        for (let i = 0; i < 10; i++) {
                            await this.siblingStatus()
                        }
                        return localStatus */
        }

        @httpGet("/live")
        public async getLivenessProbe(request: any): Promise<any> {
            return Promise.resolve({status: "ok"})
        }

        @httpGet("/abtest/:userId")
        async abTest(req: express.Request): Promise<any> {
            const userId = req.params.userId
            const hashValue = murmurhash.v3(userId.toString(), 1)
            const userIndex = (Math.round(hashValue) % 10000) + 1
            return { index: userIndex }
        }

        @httpGet("/local")
        public async getLocalStatus(request: any): Promise<StatusResponse> {
            return this.statusHandler.getStatus()
        }

        @httpGet("/perf/mongo")
        public async perfMongo(request: any): Promise<boolean> {
            const x = await this.countryDao.retrieve()
            return true
        }

        @httpGet("/perf/redis")
        public async perfRedis(request: any): Promise<boolean> {
            const x = await this.redisCrudDao.read("CFAPP:8f52b856-be1f-42e3-a59c-26c5c1aef4c2")
            return true
        }

        @httpGet("/perf/sqs")
        public async perfSqs(request: any): Promise<boolean> {
            const attributes: Map<string, any> = new Map<string, any>()
            attributes.set("eventType", "PROCESSED")
            return this.queueService.sendMessage("temp-perf", this.jsonData, attributes)
        }

        private async siblingStatus(): Promise<any> {
            const statusResponse = await fetch("http://localhost:3000/status/local")
            return await this.fetchUtil.parseResponse<StatusResponse>(statusResponse)
        }

        @httpGet("/heapDump")
        public heapDump(request: any): any {
            writeHeapSnapshot()
            this.logger.info("heapDump initiated")
        }

        // @httpGet("/enableLoopLag")
        // public enableLoopLag() {
        //     histogram.enable()
        // }

        // @httpGet("/disableLoopLag")
        // public disableLoopLag() {
        //     histogram.disable()
        // }

        // @httpGet("/loopLag")
        // public getLoopLagDetails(request: any): any {
        //     return {
        //         min: histogram.min,
        //         max: histogram.max,
        //         mean: histogram.mean,
        //         stddev: histogram.stddev,
        //         p50: histogram.percentile(50),
        //         p90: histogram.percentile(90),
        //         p99: histogram.percentile(99)
        //     }
        // }

        @httpGet("/respondWithDelay")
        public async respondWithDelay(request: express.Request): Promise<any> {
            const { waitTimeMillis } = request.query
            return this.loggingService.respondWithDelay(waitTimeMillis)
        }

        @httpGet("/perftest/init")
        public async initPerfTest(request: express.Request): Promise<any> {
            const rateLimitConfig = await this.cacheHelper.getCfApiRateLimitConfig()
            if (isIPv4(request.ip)) {
                rateLimitConfig.configs.whitelistedCIDRs.push(`${request.ip}/32`)
                this.logger.info(`${request.ip} IP whitelisted`)
                return this.cacheHelper.setCfApiRateLimitConfig(rateLimitConfig)
            }
        }
    }
    return StatusController
}
