import * as express from "express"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import { Container, inject } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import AuthMiddleware from "../auth/AuthMiddleware"
import { BASE_TYPES, Logger } from "@curefit/base"
import { Session, UserContext } from "@curefit/userinfo-common"
import { IQuestService, NewLevelBadges, QUEST_CLIENT_TYPES } from "@curefit/quest-client"
import { ILevelReadOnlyDao, QUEST_MODELS_TYPES } from "@curefit/quest-models"
import * as momentTz from "moment-timezone"
import { TimeUtil } from "@curefit/util-common"
import * as _ from "lodash"
import { Badge, UserActivityDetails, Vertical } from "@curefit/quest-common"
import { UrlPathBuilder } from "@curefit/product-common"
import { CULT_LAUNCH_TERMS, getShareText } from "../util/QuestUtils"
import { IMetricServiceClient as IMetricService, METRIC_TYPES } from "@curefit/metrics"
import { IMetricData, METRICS_CATEGORY_NAME } from "@curefit/metrics-common"
import { Action } from "../common/views/WidgetView"
import { ErrorFactory } from "@curefit/error-client"
import { ErrorCodes } from "../error/ErrorCodes"
import LiveUtil from "../util/LiveUtil"


export function QuestControllerFactory(kernel: Container) {

    @controller("/quest", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class QuestController {
        constructor(
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(QUEST_CLIENT_TYPES.IQuestService) private questService: IQuestService,
            @inject(QUEST_MODELS_TYPES.LevelReadOnlyDao) private levelDao: ILevelReadOnlyDao,
            @inject(METRIC_TYPES.MetricServiceClient) private metricService: IMetricService,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        ) {
        }

        @httpGet("/newLevelsBadges")
        async getNewLevels(req: express.Request): Promise<NewLevelBadges> {
            const session: Session = req.session
            const userId: string = session.userId
            return this.questService.getNewLevelsAndBadges(userId)
        }

        private getTrend(thisWeekData: UserActivityDetails, lastWeekData: UserActivityDetails, vertical: Vertical): number {
            const thisWeekActivityCount = thisWeekData && _.find(thisWeekData.activitiesBreakUp, data => { return data.vertical === vertical }) ? _.find(thisWeekData.activitiesBreakUp, data => { return data.vertical === vertical }).numActivities : 0
            const lastWeekActivityCount = lastWeekData && _.find(lastWeekData.activitiesBreakUp, data => { return data.vertical === vertical }) ? _.find(lastWeekData.activitiesBreakUp, data => { return data.vertical === vertical }).numActivities : 0
            return thisWeekActivityCount - lastWeekActivityCount
        }

        @httpGet("/detailedStats")
        async getDetailedStats(req: express.Request): Promise<any> {
            const session: Session = req.session
            const userId: string = session.userId
            const userContext: UserContext = req.userContext as UserContext
            const tz = userContext.userProfile.timezone
            const today = TimeUtil.todaysDate(tz)
            const todayMinus30Days = TimeUtil.getMomentNow(tz).subtract(29, "day").format("YYYY-MM-DD")
            const todayMinus7Days = TimeUtil.getMomentNow(tz).subtract(6, "day").format("YYYY-MM-DD")
            const todayMinus14Days = TimeUtil.getMomentNow(tz).subtract(13, "day").format("YYYY-MM-DD")
            const [monthlyStats, currentWeekStats, lastWeekStats, levels, latestMetrics] = await Promise.all([
                this.questService.getUserDetailedStats(userId, todayMinus30Days, today),
                this.questService.getUserDetailedStats(userId, todayMinus7Days, today),
                this.questService.getUserDetailedStats(userId, todayMinus14Days, TimeUtil.getMomentNow(tz).subtract(7, "day").format("YYYY-MM-DD")),
                this.levelDao.retrieve(),
                this.metricService.getMetricsForCategory(userId, METRICS_CATEGORY_NAME.VITAL),
            ])

            const allBadges = _.flatten(_.map(monthlyStats.activitiesBreakUp, a => { return a.badges }))
            allBadges.sort((b1, b2) => {
                return new Date(b2.awardedAt).getTime() - new Date(b1.awardedAt).getTime()
            })

            let badgeList = []
            const awardedBadges = _.filter(allBadges, badge => {
                if (badge.awardedAt !== undefined) {
                    return true
                }
                return false
            })

            const unawarderBadges = _.filter(allBadges, badge => {
                if (badge.awardedAt !== undefined) {
                    return false
                }
                return true
            })

            badgeList = awardedBadges.length > 0 ? awardedBadges : unawarderBadges.slice(0, 3)
            const isLevelDirty = monthlyStats.pointsLeftForNextLevel ? false : monthlyStats.pointsToMaintainCurrentLevel && monthlyStats.levelValidUntil ? true : false

            const data: any = {
                "widgets": [
                    {
                        "widgetType": "LEVEL_WIDGET",
                        "level": monthlyStats.level,
                        "title": monthlyStats.level.name,
                        "total": monthlyStats.numActivities,
                        "duration": 30,
                        "subTitle": monthlyStats.pointsLeftForNextLevel ? `${monthlyStats.pointsLeftForNextLevel} AWAY FROM LEVEL ${(monthlyStats.level.levelId + 1)}` :
                            (monthlyStats.pointsToMaintainCurrentLevel && monthlyStats.levelValidUntil ? "SCORE NOT ENOUGH" : "SEE ALL LEVELS "),
                        "isLevelDirty": isLevelDirty,
                        "action": {
                            "actionType": "NAVIGATION",
                            "title": "SEE ALL LEVELS",
                            "url": "curefit://viewlevels"
                        }
                    },
                    {
                        "widgetType": "ACTIVITIES_GRAPH_WIDGET",
                        "activities": [
                            {
                                "type": "CULT",
                                "score": _.find(monthlyStats.activitiesBreakUp, vertical => { return vertical.vertical === "CULT" }) ? _.find(monthlyStats.activitiesBreakUp, vertical => { return vertical.vertical === "CULT" }).activityPoints : 0,
                                "title": "Workout\npoints",
                                "status": this.getTrend(currentWeekStats, lastWeekStats, "CULT")
                            },
                            {
                                "type": "EAT",
                                "score": _.find(monthlyStats.activitiesBreakUp, vertical => { return vertical.vertical === "EAT" }) ? _.find(monthlyStats.activitiesBreakUp, vertical => { return vertical.vertical === "EAT" }).activityPoints : 0,
                                "title": "Meals\npoints",
                                "status": this.getTrend(currentWeekStats, lastWeekStats, "EAT")
                            },
                            {
                                "type": "MEDITATION",
                                "score": _.find(monthlyStats.activitiesBreakUp, vertical => { return vertical.vertical === "MIND" }) ? _.find(monthlyStats.activitiesBreakUp, vertical => { return vertical.vertical === "MIND" }).activityPoints : 0,
                                "title": "Meditation\npoints",
                                "status": this.getTrend(currentWeekStats, lastWeekStats, "MIND")
                            },
                            {
                                "type": "SLEEP",
                                "score": _.find(monthlyStats.activitiesBreakUp, vertical => { return vertical.vertical === "SLEEP" }) ? _.find(monthlyStats.activitiesBreakUp, vertical => { return vertical.vertical === "SLEEP" }).activityPoints : 0,
                                "title": "Sleep\npoints",
                                "status": this.getTrend(currentWeekStats, lastWeekStats, "SLEEP")
                            },
                            {
                                "type": "STEPS",
                                "score": _.find(monthlyStats.activitiesBreakUp, vertical => { return vertical.vertical === "STEPS" }) ? _.find(monthlyStats.activitiesBreakUp, vertical => { return vertical.vertical === "STEPS" }).activityPoints : 0,
                                "title": "Steps\npoints",
                                "status": this.getTrend(currentWeekStats, lastWeekStats, "STEPS")
                            },
                            {
                                "type": "CARE",
                                "score": _.find(monthlyStats.activitiesBreakUp, vertical => { return vertical.vertical === "CARE" }) ? _.find(monthlyStats.activitiesBreakUp, vertical => { return vertical.vertical === "CARE" }).activityPoints : 0,
                                "title": "Care\npoints",
                                "status": this.getTrend(currentWeekStats, lastWeekStats, "CARE")
                            }
                        ],
                        "isSyncAllowed": true,
                        "userId": userId
                    },
                    {
                        "widgetType": "LATEST_METRIC_WIDGET",
                        "title": "Scores & Metrics",
                        "action": {
                            "actionType": "NAVIGATION",
                            "url": "curefit://metrics",
                            "title": "SEE ALL"
                        },
                        "data": this.getStatsDataArray(latestMetrics)
                    },
                    {
                        "widgetType": "STREAK_WIDGET",
                        "best": _.find(monthlyStats.activitiesBreakUp, vertical => { return vertical.vertical === "GLOBAL" }) ? _.find(monthlyStats.activitiesBreakUp, vertical => { return vertical.vertical === "GLOBAL" }).bestStreak : 0,
                        "current": _.find(monthlyStats.activitiesBreakUp, vertical => { return vertical.vertical === "GLOBAL" }) ? _.find(monthlyStats.activitiesBreakUp, vertical => { return vertical.vertical === "GLOBAL" }).currentStreak : 0,
                        "title": "STREAK"
                    },
                    {
                        "widgetType": "BADGE_LIST_WIDGET",
                        "action": {
                            "actionType": "NAVIGATION",
                            "title": "SEE ALL",
                            "url": "curefit://allbadgespage"
                        },
                        "badges": _.map(badgeList, b => {
                            return {
                                ...b.badge,
                                url: UrlPathBuilder.getBadgeImagePath(b.badge.badgeId),
                                largeImgUrl: UrlPathBuilder.getBadgeImagePath(b.badge.badgeId, true),
                                shareImgUrl: UrlPathBuilder.getShareImageUrl(b.badge.badgeId, b.badge.name),
                                shareText: getShareText(b.badge.vertical),
                                isAchieved: b.awardedAt === undefined ? false : true
                            }
                        })
                    }
                ],
                actions: []
            }

            if (userContext.sessionInfo.osName != "ios") {
                data.widgets.push({
                    "widgetType": "ACTIVITY_SUMMARY_WIDGET",
                    "title": "DETAILED STATS",
                    "list": [
                        {
                            "type": "WORKOUT",
                            "average": monthlyStats.additionalDetails.averageWorkoutMins ? monthlyStats.additionalDetails.averageWorkoutMins.toFixed(0) : 0,
                            "unit": "Min / day",
                            "title": monthlyStats.additionalDetails.numCultClasses + " at centre, " + monthlyStats.additionalDetails.numWorkouts + " at home, " +
                                monthlyStats.additionalDetails.numCultLiveClasses + " live classes, " + monthlyStats.additionalDetails.numCultOnlineClasses + " online classes",
                            "action": {
                                "actionType": "NAVIGATION",
                                "url": "curefit://detailedstats?activityType=CULT"
                            }
                        },
                        {
                            "type": "STEPS",
                            "average": monthlyStats.additionalDetails.numStepsPerDay ? monthlyStats.additionalDetails.numStepsPerDay.toFixed(0) : 0,
                            "unit": "Steps / day",
                            "title": "Target:",
                            "subTitle": "10000 steps",
                            "action": {
                                "actionType": "NAVIGATION",
                                "url": "curefit://detailedstats?activityType=STEPS"
                            }
                        },
                        {
                            "type": "MEDITATION",
                            "average": monthlyStats.additionalDetails.averageMeditationTime ? monthlyStats.additionalDetails.averageMeditationTime.toFixed(0) : 0,
                            "unit": "Min / day",
                            "title": monthlyStats.additionalDetails.numMindFitClasses + " at centre, " + monthlyStats.additionalDetails.numDIYMeditations + " at home, " + monthlyStats.additionalDetails.numMindLiveClasses ? monthlyStats.additionalDetails.numMindLiveClasses : 0 + " live classes",
                            "action": {
                                "actionType": "NAVIGATION",
                                "url": "curefit://detailedstats?activityType=MIND"
                            }
                        },
                        {
                            "type": "SLEEP",
                            "average": monthlyStats.additionalDetails.averageSleep ? (monthlyStats.additionalDetails.averageSleep / 60).toFixed(1) : 0,
                            "unit": "Hours / day",
                            "title": "Target:",
                            "subTitle": "8 hours",
                            "action": {
                                "actionType": "NAVIGATION",
                                "url": "curefit://detailedstats?activityType=SLEEP"
                            }
                        },
                        {
                            "type": "MEALS",
                            "average": monthlyStats.additionalDetails.numMeals ? (monthlyStats.additionalDetails.numMeals).toFixed(0) : 0,
                            "unit": "Meals",
                            "title": "",
                            "action": {
                                "actionType": "NAVIGATION",
                                "url": "curefit://detailedstats?activityType=EAT"
                            }
                        }
                    ]
                })
            }
            return data
        }

        getStatsDataArray(stats: { [id: string]: IMetricData }): Array<{ type: string, value: string, action: Action }> {
            const filtered = _.pickBy(stats)
            return _.map(filtered, (item) => {
                const action: Action = {
                    actionType: "NAVIGATION",
                    url: `curefit://metricDetail?metricId=${item.metric.id}`
                }
                const unit = item.metric.unit !== "value" ? item.metric.unit : ""
                return {
                    type: item.metric.name,
                    value: `${item.value} ${unit}`,
                    action
                }
            })
        }

        @httpGet("/historicGraphData")
        async getHistoricGraphData(req: express.Request): Promise<any> {
            const session: Session = req.session
            const userId: string = session.userId
            if (req.query.vertical === undefined) {
                this.logger.error("Please select a vertical while querying historicGraphData from quest")
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Please select a vertical").build()
            }
            if (req.query.startDate === undefined) {
                this.logger.error("Please select a startDate while querying historicGraphData from quest")
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Please send a startDate").build()
            }
            if (req.query.endDate === undefined) {
                this.logger.error("Please select a endDate while querying historicGraphData from quest")
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Please send a endDate").build()
            }
            if (req.query.graphType === undefined) {
                this.logger.error("Please select a graphType while querying historicGraphData from quest")
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Please send the graph Type - WEEK, MONTH or YEAR").build()
            }
            const vertical: Vertical = req.query.vertical
            const verticalDetailedStats = await this.questService.getVerticalSpecificStats(userId, req.query.startDate, req.query.endDate, vertical, req.query.graphType)
            let overallValueText = ""
            switch (vertical) {
                case "CULT": {
                    overallValueText = " min / day average"
                    break
                }
                case "EAT": {
                    overallValueText = " meals ordered"
                    break
                }
                case "MIND": {
                    overallValueText = " min / day average"
                    break
                }
                case "SLEEP": {
                    overallValueText = " hours / day average"
                    break
                }
                case "STEPS": {
                    overallValueText = " steps / day average"
                    break
                }
                default: {
                    break
                }
            }
            return {
                "widgetType": "ACTIVITY_GRAPH_WIDGET",
                "startDate": verticalDetailedStats.graphData.startDate,
                "endDate": verticalDetailedStats.graphData.endDate,
                "graphType": verticalDetailedStats.graphData.type,
                "vertical": vertical,
                "graphData": verticalDetailedStats.graphData.data,
                "averageValue": verticalDetailedStats.graphData.overallValue,
                "averageValueText": overallValueText
            }
        }

        @httpGet("/verticalStats")
        async getVerticalStats(req: express.Request): Promise<any> {
            const session: Session = req.session
            const userId: string = session.userId
            const userContext: UserContext = req.userContext as UserContext
            const tz = userContext.userProfile.timezone

            // start date of the week
            let startDate = ""
            let startPlus7Days = ""

            if (TimeUtil.getMomentNow(tz).diff(TimeUtil.getMomentNow(tz).startOf("week"), "days") === 0) {
                startDate = TimeUtil.getMomentNow(tz).subtract(6, "days").format("YYYY-MM-DD")
                startPlus7Days = TimeUtil.todaysDate(tz)
            } else {
                startDate = TimeUtil.getMomentNow(tz).startOf("week").add(1, "day").format("YYYY-MM-DD")
                startPlus7Days = TimeUtil.getMomentNow(tz).startOf("week").add(7, "day").format("YYYY-MM-DD")
            }

            if (req.query.vertical === undefined) {
                this.logger.error("Please select a vertical while querying historicGraphData from quest")
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Please select a vertical").build()
            }
            const vertical: Vertical = req.query.vertical
            const verticalDetailedStats = await this.questService.getVerticalSpecificStats(userId, startDate, startPlus7Days, vertical, "WEEK")
            let verticalName = ""
            let value = "0"
            let subString = ""
            let allTimeStatsData: { value: string, text: string, action?: string }[] = []
            let overallValueText = ""
            switch (vertical) {
                case "CULT": {
                    verticalName = "Workout"
                    value = verticalDetailedStats.last30DayDetails.averageWorkoutMins.toFixed(0)
                    subString = "min / day"
                    allTimeStatsData = [
                        {
                            value: ((verticalDetailedStats.allTimeStats.cultClassDuration + verticalDetailedStats.allTimeStats.cultDIYClassDuration +
                                (verticalDetailedStats.allTimeStats.cultLiveClassDuration ? verticalDetailedStats.allTimeStats.cultLiveClassDuration : 0) +
                                (verticalDetailedStats.allTimeStats.cultOnlineClassDuration ? verticalDetailedStats.allTimeStats.cultOnlineClassDuration : 0)) / (1000 * 60)).toFixed(0),
                            text: " minutes worked out"
                        },
                        {
                            value: verticalDetailedStats.allTimeStats.cultLiveClassCount ? verticalDetailedStats.allTimeStats.cultLiveClassCount.toFixed(0) : "0",
                            text: `${LiveUtil.getCultLiveBranding(userContext)} classes`,
                            action: ""
                        },
                        {
                            value: verticalDetailedStats.allTimeStats.cultClassCount.toFixed(0),
                            text: " cult.fit classes",
                            action: ""
                        },
                        {
                            value: verticalDetailedStats.allTimeStats.cultOnlineClassCount.toFixed(0),
                            text: " online classes",
                            action: ""
                        },
                        {
                            value: verticalDetailedStats.allTimeStats.cultDIYClassCount.toFixed(0),
                            text: " workouts at home",
                            action: ""
                        }
                    ]
                    overallValueText = " min / day average"
                    break
                }
                case "EAT": {
                    verticalName = "Meals"
                    value = verticalDetailedStats.last30DayDetails.numMeals.toFixed(0)
                    subString = "Meals ordered"
                    allTimeStatsData = [
                        {
                            value: (verticalDetailedStats.allTimeStats.eatBreakfastCount + verticalDetailedStats.allTimeStats.eatLunchCount +
                                verticalDetailedStats.allTimeStats.eatSnacksCount + verticalDetailedStats.allTimeStats.eatDinnerCount).toFixed(0),
                            text: " meals ordered",
                            action: ""
                        }
                    ]
                    overallValueText = " meals ordered"
                    break
                }
                case "MIND": {
                    verticalName = "Meditation"
                    value = verticalDetailedStats.last30DayDetails.averageMeditationTime.toFixed(0)
                    subString = "min / day"
                    allTimeStatsData = [
                        {
                            value: ((verticalDetailedStats.allTimeStats.mindClassDuration + verticalDetailedStats.allTimeStats.mindDIYClassDuration + (verticalDetailedStats.allTimeStats.mindLiveClassDuration ? verticalDetailedStats.allTimeStats.mindLiveClassDuration : 0)) / (1000 * 60)).toFixed(0),
                            text: " minutes meditated"
                        },
                        {
                            value: verticalDetailedStats.allTimeStats.mindLiveClassCount ? verticalDetailedStats.allTimeStats.mindLiveClassCount.toFixed(0) : "0"  ,
                            text: " mind.live classes",
                            action: ""
                        },
                        {
                            value: verticalDetailedStats.allTimeStats.mindClassCount.toFixed(0),
                            text: " mind.fit classes",
                            action: ""
                        },
                        {
                            value: verticalDetailedStats.allTimeStats.mindDIYClassCount.toFixed(0),
                            text: " audio meditation sessions",
                            action: ""
                        }
                    ]
                    overallValueText = " min / day average"
                    break
                }
                case "SLEEP": {
                    verticalName = "Sleep"
                    value = (verticalDetailedStats.last30DayDetails.averageSleep / 60).toFixed(1)
                    subString = "hours / day"
                    overallValueText = " hours / day average"
                    break
                }
                case "STEPS": {
                    verticalName = "Steps"
                    value = verticalDetailedStats.last30DayDetails.numStepsPerDay.toFixed(0)
                    subString = "steps / day"
                    allTimeStatsData = [
                        {
                            value: verticalDetailedStats.allTimeStats.stepsCount.toFixed(0),
                            text: " steps taken"
                        },
                        {
                            value: verticalDetailedStats.allTimeStats.stepsKMDitance.toFixed(0),
                            text: " kms walked"
                        }
                    ]
                    overallValueText = " steps / day average"
                    break
                }
                default: {
                    break
                }
            }
            const widgetData = {
                "widgets": [
                    {
                        "widgetType": "OVERVIEW_WIDGET",
                        "vertical": verticalName,
                        "value": value,
                        "subString": subString
                    },
                    {
                        "widgetType": "STREAK_WIDGET",
                        "best": verticalDetailedStats.bestStreak,
                        "current": verticalDetailedStats.currentStreak,
                        "title": "STREAK"
                    },
                    {
                        "widgetType": "BADGE_LIST_WIDGET",
                        "action": {
                            "actionType": "NAVIGATION",
                            "title": "SEE ALL",
                            "url": `curefit://allbadgespage?vertical=${vertical}`
                        },
                        "badges": _.map(verticalDetailedStats.badges, b => {
                            return {
                                ...b.badge,
                                url: UrlPathBuilder.getBadgeImagePath(b.badge.badgeId),
                                largeImgUrl: UrlPathBuilder.getBadgeImagePath(b.badge.badgeId, true),
                                shareImgUrl: UrlPathBuilder.getShareImageUrl(b.badge.badgeId, b.badge.name),
                                shareText: getShareText(b.badge.vertical),
                                isAchieved: b.awardedAt === undefined ? false : true
                            }
                        })
                    },
                    {
                        "widgetType": "ACTIVITY_GRAPH_WIDGET",
                        "startDate": verticalDetailedStats.graphData.startDate,
                        "endDate": verticalDetailedStats.graphData.endDate,
                        "graphType": verticalDetailedStats.graphData.type,
                        "graphData": verticalDetailedStats.graphData.data,
                        "vertical": vertical,
                        "averageValue": verticalDetailedStats.graphData.overallValue,
                        "averageValueText": overallValueText
                    },

                    {
                        "widgetType": "ALL_TIME_STATS_WIDGET",
                        "firstActivityDate": verticalDetailedStats.firtActivityDate,
                        "data": allTimeStatsData
                    }
                ]
            }
            return widgetData
        }

        @httpGet("/levels")
        async getAllLevels(req: express.Request): Promise<any> {
            const session: Session = req.session
            const userId: string = session.userId
            const userContext: UserContext = req.userContext as UserContext
            const tz = userContext.userProfile.timezone

            const today = TimeUtil.todaysDate(tz)
            const todayMinus30Days = TimeUtil.getMomentNow(tz).subtract(29, "day").format("YYYY-MM-DD")

            const [levels, userSummary] = await Promise.all([this.levelDao.retrieve(), this.questService.getUserDetailedStats(userId, todayMinus30Days, today)])
            const isLevelDirty = userSummary.pointsLeftForNextLevel ? false : userSummary.pointsToMaintainCurrentLevel && userSummary.levelValidUntil ? true : false

            let subTitle = ""
            const learnMore: Array<String> = []
            if (isLevelDirty) {
                const diffDays = momentTz.tz(userSummary.levelValidUntil, tz).diff(TimeUtil.getMomentNow(tz), "days")
                const pointsForLevel = userSummary.level.activityCount
                // const pointsForLevel = userSummary.level.activityPoints;
                subTitle = `Your score is ${userSummary.activityPoints}. You need ${pointsForLevel} or above to stay at Level ${userSummary.level.levelId}`
                const daysRemaining = TimeUtil.getMomentForDateString(userSummary.levelValidUntil, tz).diff(TimeUtil.getMomentNow(tz), "days")
                if (daysRemaining > 0) {
                    learnMore.push(`After ${daysRemaining} day(s), if you are still below ${pointsForLevel}, your level will drop`)
                    learnMore.push(`Time to saddle up and salvage your level! Get atleast ${userSummary.pointsToMaintainCurrentLevel} points in the next ${daysRemaining} days to maintain your level.`)
                } else if (daysRemaining === 0) {
                    learnMore.push(`After today, if you are still below ${pointsForLevel}, your level will drop`)
                    learnMore.push(`Time to saddle up and salvage your level! Get atleast ${userSummary.pointsToMaintainCurrentLevel} points today to maintain your level.`)
                }
            } else {
                subTitle = "Grow your score to up your levels. Your score is points gained in last 30 days."
            }

            levels.sort((l1, l2) => { return l1.levelId - l2.levelId })
            return {
                "title": "Fitness Levels",
                "subTitle": subTitle,
                "action":
                    isLevelDirty ? {
                        "actionType": "NAVIGATION",
                        "title": "LEARN MORE",
                        "url": "curefit://leveldropwarning"
                    } : undefined,
                "learnMore": isLevelDirty ? learnMore : undefined,
                "learnMoreTitle": isLevelDirty ? "Score not enough" : undefined,
                "userLevel": userSummary ? userSummary.level.levelId : 0,
                "acvitityScore": _.find(userSummary.activitiesBreakUp, a => { return a.vertical === "GLOBAL" }) ? _.find(userSummary.activitiesBreakUp, a => { return a.vertical === "GLOBAL" }).activityPoints : 0,
                "tnc": [
                    { symbolText: "*", text: CULT_LAUNCH_TERMS }
                ],
                "levels": _.map(levels, level => {
                    let benefits: Array<{ name: string, description: string, imageUrl: string }> = []
                     if (!_.isEmpty(level.rewards)) {
                         benefits = level.rewards.map((reward) => {
                             return {
                                 name: reward.name,
                                 description: reward.description,
                                 imageUrl: reward.imageUrl
                             }
                         })
                     }
                    // if (level.rewards && level.levelId > 3) {
                    //     benefits.push({
                    //         name: "Benefits of the previous levels.",
                    //         description: "Benefits of the previous levels.",
                    //         imageUrl: `/image/rewards/plus${level.levelId}.png`
                    //     })
                    // }

                    return {
                        level: level.levelId,
                        description: level.description,
                        benefits: benefits ? benefits : []
                    }
                })
            }
        }


        @httpPost("/updateCommunication")
        async updateLevelBadgesCommunication(request: any): Promise<boolean> {
            const session: Session = request.session
            const userId: string = session.userId
            return this.questService.updateLevelBadgesCommunication(userId, request.body.badges, request.body.level)
        }

        @httpGet("/badges")
        public async getBadges(req: express.Request): Promise<any> {
            const session: Session = req.session
            const userId: string = session.userId
            const vertical: Vertical = req.query.vertical

            const response = await this.questService.getAllBadges(userId)

            let badgeVericalList = []
            if (vertical !== undefined) {
                badgeVericalList = _.filter(response, v => {
                    if (v.vertical === vertical) {
                        return true
                    }
                })
            } else {
                badgeVericalList = response
            }

            const res = _.map(badgeVericalList, data => {
                return {
                    ...data,
                    title: this.getTitleForVertical(data.vertical),
                    badges: _.map(data.badges, b => {
                        return {
                            ...b.badge,
                            imageUrl: UrlPathBuilder.getBadgeImagePath(b.badge.badgeId),
                            shareImgUrl: UrlPathBuilder.getShareImageUrl(b.badge.badgeId, b.badge.name),
                            largeImgUrl: UrlPathBuilder.getBadgeImagePath(b.badge.badgeId, true),
                            isAchieved: b.isAchieved,
                            shareText: getShareText(b.badge.vertical)
                        }
                    })
                }
            })

            if (vertical === undefined) {
                // adding sort order for badges
                const sortOrder = ["GLOBAL", "CULT", "STEPS", "MIND", "SLEEP", "EAT"]

                res.sort((a, b) => {
                    if (_.indexOf(sortOrder, a.vertical) > _.indexOf(sortOrder, b.vertical)) {
                        return 1
                    }
                    return -1
                })
            }

            return res
        }

        @httpGet("/getbadges")
        public async getFitBadges(req: express.Request): Promise<any> {
            const verticalExcludeList: Vertical[] = ["EAT"]
            const session: Session = req.session
            const userId: string = session.userId
            const vertical: Vertical = req.query.vertical

            const response = await this.questService.getAllBadges(userId)

            let badgeVericalList: typeof response = []
            if (vertical !== undefined) {
                badgeVericalList = _.filter(response, v => {
                    if (v.vertical === vertical) {
                        return true
                    }
                })
            } else {
                badgeVericalList = _.filter(response, (data) => {
                    return !verticalExcludeList.includes(data.vertical)
                })
            }


            type BadgesMapInnerElement = Badge & {imageUrl: string, largeImgUrl: string, shareImgUrl: string, isAchieved: boolean, shareText: string}

            const res = _.map(badgeVericalList, data => {
                const badgesMap = _.groupBy<BadgesMapInnerElement>(_.map<(typeof response)[0]["badges"][0], BadgesMapInnerElement>(data.badges, b => {
                    return {
                        ...b.badge,
                        imageUrl: UrlPathBuilder.getBadgeImagePath(b.badge.badgeId),
                        largeImgUrl: UrlPathBuilder.getBadgeImagePath(b.badge.badgeId, true),
                        shareImgUrl: UrlPathBuilder.getShareImageUrl(b.badge.badgeId, b.badge.name),
                        isAchieved: b.isAchieved,
                        shareText: getShareText(b.badge.vertical)
                    }
                }), "badgeCategory")

                const badgesByCategory = []

                for (const [key, value] of Object.entries(badgesMap)) {
                    badgesByCategory.push({
                        title: value[0].badgeCategory,
                        data: value.sort((b1, b2) => b1.activityCount - b2.activityCount)
                    })
                }

                return {
                    vertical: data.vertical,
                    title: this.getTitleForVertical(data.vertical),
                    badgesByCategory: badgesByCategory
                }
            })

            if (vertical === undefined) {
                // adding sort order for badges
                const sortOrder = ["GLOBAL", "CULT", "STEPS", "MIND", "SLEEP", "EAT"]

                res.sort((a, b) => {
                    if (_.indexOf(sortOrder, a.vertical) > _.indexOf(sortOrder, b.vertical)) {
                        return 1
                    }
                    return -1
                })
            }

            return res
        }

        getTitleForVertical(vertical: Vertical) {
            let title = ""
            switch (vertical) {
                case "EAT":
                    title = "EAT.FIT BADGES"
                    break
                case "CULT":
                    title = "CULT.FIT BADGES"
                    break
                case "MIND":
                    title = "MIND.FIT BADGES"
                    break
                case "GLOBAL":
                    title = ""
                    break
                case "STEPS":
                    title = "STEPS BADGES"
                    break
                case "SLEEP":
                    title = "SLEEP BADGES"
                    break
            }
            return title
        }

    }
    return QuestController
}
export default QuestControllerFactory
