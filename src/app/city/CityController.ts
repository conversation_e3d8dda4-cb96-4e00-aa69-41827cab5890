import * as express from "express"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { controller, httpGet } from "inversify-express-utils"
import { Container, inject } from "inversify"
import AuthMiddleware from "../auth/AuthMiddleware"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { City, LocationUtil } from "@curefit/location-common"
import { Tenant } from "@curefit/base-common"

interface CityView {
    isSelected: boolean
    cityId: string
    name: string
    image: string
}

export function controllerFactory(kernel: Container) {
    @controller("/city")
    class CityController {
        constructor(
            @inject(LOCATION_TYPES.CityService) private cityService: ICityService
        ) { }

        @httpGet("/")
        async getCities(req: express.Request): Promise<{ cities: CityView[] }> {
            const cities = await this.cityService.listCities(undefined)
            LocationUtil.sortCityByPriority(cities)

            const cityViews = cities.map(city => ({
                isSelected: false,
                cityId: city.cityId,
                name: city.name,
                image: city.image
            })
            )
            return { cities: cityViews }
        }

        @httpGet("/v2")
        async getCitiesV2(req: express.Request): Promise<{ cities: City[] }> {
            const cities = this.cityService.listCities(Tenant.CUREFIT_APP)
            return { cities: cities }
        }

        @httpGet("/v2/:id")
        async getCityById(req: express.Request): Promise<{ city: City }> {
            const cityId: string = req.params.id
            const city = this.cityService.getCityById(cityId)
            return { city: city }
        }
    }

    return CityController
}

export default controllerFactory
