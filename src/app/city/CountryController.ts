import * as express from "express"
import { controller, httpGet } from "inversify-express-utils"
import { Container, inject } from "inversify"
import { LOCATION_TYPES, ICountryService } from "@curefit/location-mongo"
import * as _ from "lodash"
import { CountryData } from "../user/UserController"
import { Tenant } from "@curefit/user-common"
import AppUtil from "../util/AppUtil"


export function controllerFactory(kernel: Container) {
    @controller("/country")
    class CountryController {
        constructor(
            @inject(LOCATION_TYPES.CountryService) private countryService: ICountryService
        ) { }

        @httpGet("/")
        async getCountries(req: express.Request): Promise<{ countriesData: CountryData[] }> {
            const tenant: Tenant = AppUtil.getTenantFromReq(req)
            const countries = await this.countryService.listCountries(tenant)
            const countriesData: CountryData[] = _.map(
                countries,
                ({ countryId, name, countryCallingCode, flagImage, phoneLoginSupported, phoneNumberMaxLength }) => ({
                    countryId,
                    name,
                    countryCallingCode,
                    flagImage,
                    phoneLoginSupported,
                    phoneNumberMaxLength
                })
            )

            return {
                countriesData
            }
        }
    }

    return CountryController
}

export default controllerFactory
