import { inject, injectable } from "inversify"
import { InMemoryCacheService } from "@curefit/memory-cache"
import { CityResponse } from "maxmind"
import * as _ from "lodash"
import { Logger, BASE_TYPES, ILogger, FetchUtilV2 } from "@curefit/base"
import { DetectedCityResponseByIp, ICFAPICityService } from "./ICFAPICityService"
import { City, Country, VerticalType, Verticals } from "@curefit/location-common"
import { ICityReadOnlyDao, ICountryReadOnlyDao, LOCATION_TYPES, ICityService, ICountryService } from "@curefit/location-mongo"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { IMaxmindService, MAXMIND_CLIENT_TYPES } from "@curefit/maxmind-client"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { Tenant } from "@curefit/user-common"
const BY_GEO_CITY_NAME_CACHE = "BY_GEO_CITY_NAME_CACHE"
const BY_GEO_COUNTRY_NAME_CACHE = "BY_GEO_COUNTRY_NAME_CACHE"
const BY_COUNTRY_ID_CACHE = "BY_COUNTRY_ID_CACHE"


@injectable()
export class CFAPICityService extends InMemoryCacheService<Map<string, Map<string, City | Country>>> implements ICFAPICityService {
    constructor(
        @inject(LOCATION_TYPES.CityReadOnlyDao) private cityDao: ICityReadOnlyDao,
        @inject(LOCATION_TYPES.CountryReadOnlyDao) private countryDao: ICountryReadOnlyDao,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(MAXMIND_CLIENT_TYPES.IMaxmindService) private maxmindService: IMaxmindService,
        @inject(BASE_TYPES.FetchUtilV2) private fetchHelper: FetchUtilV2,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
        @inject(LOCATION_TYPES.CityService) private cityService: ICityService,
        @inject(LOCATION_TYPES.CountryService) private countryService: ICountryService
    ) {
        super(logger, 100 * 60)
        this.load("CFAPICityService")
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    protected async loadData(): Promise<Map<string, Map<string, City | Country>>> {
        const citiesPromise = this.cityDao.retrieve()
        const countriesPromise = this.countryDao.retrieve()

        const cities = await citiesPromise
        const countries = await countriesPromise

        const geoCityNameMap = new Map<string, City>()
        const geoCountryNameMap = new Map<string, Country>()
        const countryIdMap = new Map<string, Country>()

        const map = new Map<string, Map<string, City | Country>>()
        cities.filter(city => !_.isEmpty(city.geoCityNames)).forEach(city => {
            city.geoCityNames.forEach(geoCityName => {
                geoCityNameMap.set(geoCityName, city)
            })
        })
        map.set(BY_GEO_CITY_NAME_CACHE, geoCityNameMap)

        countries.filter(country => !_.isEmpty(country.geoCountryNames)).forEach(country => {
            country.geoCountryNames.forEach(geoCountryName => {
                geoCountryNameMap.set(geoCountryName, country)
                countryIdMap.set(country.countryId, country)
            })
        })
        map.set(BY_GEO_COUNTRY_NAME_CACHE, geoCountryNameMap)
        map.set(BY_COUNTRY_ID_CACHE, countryIdMap)
        return map
    }

    public async getCityAndCountryByIp(tenant: Tenant, ip: string): Promise<DetectedCityResponseByIp> {
        try {
            const response = await this.maxmindService.getLocationDetailsByIp(ip) as CityResponse
            const detectedCityName = _.get(response, "city.names.en")
            const detectedCountryCode = _.get(response, "country.iso_code")

            let city: City, country: Country
            // Check if ip is from serviceable city
            if (!_.isNil(response) && !_.isNil(response.city) && !_.isEmpty(response.city.names)) {
                const geoCityName = response.city.names["en"]
                city = this.cache.get(BY_GEO_CITY_NAME_CACHE).get(geoCityName) as City

                // reset city if not part of tenant
                if (!_.isEmpty(city) && !_.includes(city.tenant, tenant)) {
                    city = undefined
                }
                if (city) {
                    country = this.cache.get(BY_COUNTRY_ID_CACHE).get(city.countryId) as Country
                }
            }

            // Check if ip is from serviceable country
            if (!_.isNil(response) && !_.isNil(response.country) && !_.isNil(response.country.iso_code)) {
                const geoCountryCode = response.country.iso_code
                country = this.cache.get(BY_GEO_COUNTRY_NAME_CACHE).get(geoCountryCode) as Country

                // set to default country if empty or nor part of tenant
                if (_.isEmpty(country) || (!_.isEmpty(country) && !_.includes(country.tenant, tenant))) {
                    country = this.countryService.getDefaultCountry(tenant)
                }
                if (tenant === Tenant.LIVEFIT_APP) {
                    city = this.cityService.getDefaultCityForCountry(country.countryId)
                }
            }
            return {
                city,
                country,
                detectedCityName,
                detectedCountryCode
            }
        } catch (err) {
            this.logger.error("IP detection failed:: ", err)
            return { city: undefined, country: undefined, detectedCityName: undefined, detectedCountryCode: undefined }
        }

    }
}
