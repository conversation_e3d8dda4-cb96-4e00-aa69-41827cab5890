import { inject, injectable } from "inversify"
import { InMemoryCacheService } from "@curefit/memory-cache"
import { IPageConfigReadOnlyDao, IPageConfigReadWriteDao, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import { PaymentChannel, PaymentMode } from "@curefit/payment-common"
import { Logger } from "@curefit/base"
import * as _ from "lodash"
import { PromUtil } from "@curefit/base"
import { CachingServiceType } from "@curefit/base"
import { BASE_TYPES } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

export type PaymentOptionState = "ENABLED" | "DISABLED" | "HIDDEN"

@injectable()
class PaymentPageConfig extends InMemoryCacheService<any> {

    private paymentOptionStateMap: Map<String, PaymentOptionState>
    private paymentTestUsersMap: Map<String, boolean>

    constructor(
        @inject(PAGE_CONFIG_TYPES.PageConfigReadOnlySecondaryDao) protected pageConfigDao: IPageConfigReadOnlyDao,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) protected logger: Logger,
        @inject(BASE_TYPES.PromUtil) private promUtil: PromUtil,

    ) {
        super(logger, 7 * 60)
        this.paymentOptionStateMap = new Map<String, PaymentOptionState>()
        this.paymentTestUsersMap = new Map<String, boolean>()
        this.load("PaymentPageConfig")
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    loadData(): Promise<any> {
        return this.pageConfigDao.findOne({ pageId: { $eq: "PaymentPageConfig" } }).then(pageConfig => {
            this.data = pageConfig.data
            if (_.isArray(this.data.paymentModesConfig)) {
                this.data.paymentModesConfig.forEach(config => {
                    this.paymentOptionStateMap.set(config.paymentMethod, config.option)
                })
            }
            if (_.isArray(this.data.paymentTestUserIdList)) {
                this.paymentTestUsersMap.clear()
                this.data.paymentTestUserIdList.forEach(userId => {
                    this.paymentTestUsersMap.set(userId, true)
                })
            }
            return this.data
        })
    }
    public data: {
        gatewaysPercentage: [
            {
                channel: PaymentChannel,
                start: number,
                end: number,
                paymentMethod: PaymentMode
            }
        ],
        paymentModesConfig: [
            {
                paymentMethod: String,
                option: PaymentOptionState
            }
        ],
        paymentTestUserIdList: string[]
    }

    public getSelectedChannel(orderId: number, paymentMethod: PaymentMode | "DEFAULT" = "DEFAULT"): PaymentChannel {
        const number = orderId % 100
        this.promUtil.reportCacheAccess(CachingServiceType.PAYMENT_PAGE_CONFIG)
        return this.data.gatewaysPercentage.filter(gateway => gateway.paymentMethod === paymentMethod && number >= gateway.start && number <= gateway.end)[0].channel
    }

    public getPaymentMethodState(paymentMethod: PaymentMode): PaymentOptionState {
        const state: PaymentOptionState = this.paymentOptionStateMap.get(paymentMethod)
        if (_.isNil(state)) {
            this.logger.error("PaymentPageConfig error: No entry found for " + paymentMethod)
            return "ENABLED"
        } else {
            return state
        }
    }

    public isUserPaymentTester(userId: string): boolean {
        const res = this.paymentTestUsersMap.get(userId)
        if (_.isNil(res)) return false
        else return res
    }
}


export { PaymentPageConfig }
