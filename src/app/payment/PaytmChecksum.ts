import * as crypto from "crypto"
import * as util from "util"

class PaytmChecksum {

    constructor() {
    }

    public CONFIG = {
        MID: "CureFi98058915151779",
        WEBSITE: "APP_STAGING",
        CHANNEL_ID: "WAP",
        INDUSTRY_TYPE_ID: "Retail",
        MERCHANT_KEY: "0lgfg!t!F4zSxc_9"
    }

    public paramsToString(params: any, mandatoryflag: boolean = false) {
        let data: string = ""
        const flag = params.refund ? true : false
        delete params.refund
        const tempKeys = Object.keys(params)
        if (!flag) tempKeys.sort()
        tempKeys.forEach(function (key) {
            if (key !== "CHECKSUMHASH") {
                if (params[key] === "null") params[key] = ""
                if (!mandatoryflag) {
                    data += (params[key] + "|")
                }
            }
        })
        return data
    }


    public genchecksum(params: any, cb: (err: Error, params: any) => any) {
        const key = this.CONFIG.MERCHANT_KEY
        const flag = params.refund ? true : false
        const data = this.paramsToString(params)
        this.gen_salt(4, (err, salt) => {
            const sha256 = crypto.createHash("sha256").update(data + salt).digest("hex")
            const check_sum = sha256 + salt
            const encrypted = this.encrypt(check_sum, key)
            if (flag) {
                params.CHECKSUM = (encrypted)
                params.CHECKSUM = encrypted
            } else {
                params.CHECKSUMHASH = (encrypted)
                params.payt_STATUS = "1"
            }
            cb(undefined, params)
        })
    }

    public genchecksumbystring(params: any, key: string, cb: (err: Error, hash: string) => any) {
        this.gen_salt(4, (err, salt) => {
            const sha256 = crypto.createHash("sha256").update(params + "|" + salt).digest("hex")
            const check_sum = sha256 + salt
            const encrypted = this.encrypt(check_sum, key)

            let CHECKSUMHASH = encodeURIComponent(encrypted)
            CHECKSUMHASH = encrypted
            cb(undefined, CHECKSUMHASH)
        })
    }


    public verifychecksum(params: any): boolean {
        const key = this.CONFIG.MERCHANT_KEY
        if (!params) {
            console.log("paytm params are null")
            return false
        }

        const data = this.paramsToString(params, false)
        if (params.CHECKSUMHASH) {
            params.CHECKSUMHASH = params.CHECKSUMHASH.replace("\n", "")
            params.CHECKSUMHASH = params.CHECKSUMHASH.replace("\r", "")

            const temp = decodeURIComponent(params.CHECKSUMHASH)
            const checksum = this.decrypt(temp, key)
            const salt = checksum.substr(checksum.length - 4)
            const sha256 = checksum.substr(0, checksum.length - 4)
            const hash = crypto.createHash("sha256").update(data + salt).digest("hex")
            if (hash === sha256) {
                return true
            } else {
                util.log("paytm checksum is wrong")
                return false
            }
        } else {
            util.log("paytm checksum not found")
            return false
        }
    }

    public verifychecksumbystring(params: any, key: string, checksumhash: string): boolean {

        const checksum = this.decrypt(checksumhash, key)
        const salt = checksum.substr(checksum.length - 4)
        const sha256 = checksum.substr(0, checksum.length - 4)
        const hash = crypto.createHash("sha256").update(params + "|" + salt).digest("hex")
        if (hash === sha256) {
            return true
        } else {
            util.log("paytm checksum is wrong")
            return false
        }
    }

    private iv: string = "@@@@&&&&####$$$$"

    private encrypt(data: string, custom_key: string): string {
        const iv = this.iv
        const key = custom_key
        let algo = "256"
        switch (key.length) {
            case 16:
                algo = "128"
                break
            case 24:
                algo = "192"
                break
            case 32:
                algo = "256"
                break

        }
        const cipher = crypto.createCipheriv("AES-" + algo + "-CBC", key, iv)
        let encrypted = cipher.update(data, "binary", "base64")
        encrypted += cipher.final("base64")
        return encrypted
    }

    private decrypt(data: string, custom_key: string): string {
        const iv = this.iv
        const key = custom_key
        let algo = "256"
        switch (key.length) {
            case 16:
                algo = "128"
                break
            case 24:
                algo = "192"
                break
            case 32:
                algo = "256"
                break
        }
        const decipher = crypto.createDecipheriv("AES-" + algo + "-CBC", key, iv)
        let decrypted = decipher.update(data, "base64", "binary")
        try {
            decrypted += decipher.final("binary")
        } catch (e) {
            util.log(util.inspect(e))
        }
        return decrypted
    }

    private gen_salt(length: number, cb: (err: Error, salt: string) => any) {
        crypto.randomBytes((length * 3.0) / 4.0, function (err, buf) {
            let salt: string
            if (!err) {
                salt = buf.toString("base64")
            }
            cb(err, salt)
        })
    }

    /* one way md5 hash with salt */
    private md5sum(salt: string, data: string) {
        return crypto.createHash("md5").update(salt + data).digest("hex")
    }

    private sha256sum(salt: string, data: string) {
        return crypto.createHash("sha256").update(data + salt).digest("hex")
    }
}

export default PaytmChecksum