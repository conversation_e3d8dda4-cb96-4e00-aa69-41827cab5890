import { inject, injectable } from "inversify"
import * as _ from "lodash"
import { <PERSON><PERSON><PERSON>, BASE_TYPES } from "@curefit/base"
import { PaymentMode, PaymentModeConfig, PaymentOption, PaymentChannel } from "@curefit/payment-common"
import { BaseOrder } from "@curefit/order-common"
import { OfferV2, GetBestPaymentOffersResponse, OfferAddon } from "@curefit/offer-common"
import AppUtil from "../util/AppUtil"
import { GearService, GearUtil, IGearService } from "@curefit/gearvault-client"
import { OrderProductSnapshots } from "@curefit/fulfilment-common"
import { SessionData, UserContext } from "@curefit/userinfo-common"
import { OrderUtil } from "@curefit/base-utils"
import { ListingBrandIdType } from "@curefit/eat-common"
import { PaymentOptionsBusinessV2 } from "./PaymentOptionsBusinessV2"
import { GearPaymentOptionsResponse, ServiceabilityType } from "@curefit/gear-common"
import { ProductType } from "@curefit/product-common"
import { PaymentClientUtil } from "@curefit/payment-client"
import { OfferHelper, OMS_API_CLIENT_TYPES } from "@curefit/oms-api-client"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { ISegmentService } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { PromiseCache } from "../util/VMUtil"
import { HamletConfigRequest } from "@curefit/hamlet-common"
import { Tenant } from "@curefit/base-common"
import { HAMLET_CLIENT_TYPES, IHamletService } from "@curefit/hamlet-client"
import { User } from "@curefit/user-common"

@injectable()
export class PaymentOptionViewFactory {

    PAYMENT_MODE_MAP: Map<PaymentMode, BasePaymentOptionView> = new Map<PaymentMode, BasePaymentOptionView>()

    constructor(
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private interfaces: CFServiceInterfaces,
        @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(HAMLET_CLIENT_TYPES.HamletService) protected hamletService: IHamletService,
    ) {
        this.PAYMENT_MODE_MAP.set("SODEXO", new SodexoPaymentOptionView(logger))
        this.PAYMENT_MODE_MAP.set("DEBIT_OR_CREDIT_CARD", new CreditCardDebitCardPaymentOptionView(logger))
        this.PAYMENT_MODE_MAP.set("EMI", new EmiPaymentOptionView(logger))
        this.PAYMENT_MODE_MAP.set("PAYTM", new PaytmPaymentOptionView(logger))
        this.PAYMENT_MODE_MAP.set("NET_BANKING", new NetBankingPaymentOptionView(logger))
        this.PAYMENT_MODE_MAP.set("PHONEPE", new PhonePePaymentOptionView(logger))
        this.PAYMENT_MODE_MAP.set("AMAZONPAY", new AmazonPayPaymentOptionView(logger))
        this.PAYMENT_MODE_MAP.set("PAYPAL", new PaypalPaymentOptionView(logger))
        this.PAYMENT_MODE_MAP.set("UPI", new UPIPaymentOptionView(logger))
        this.PAYMENT_MODE_MAP.set("WALLET", new WalletPaymentOptionView(logger))
        this.PAYMENT_MODE_MAP.set("COD", new CODPaymentOptionView(interfaces, segmentService, logger, hamletService))
        this.PAYMENT_MODE_MAP.set("PAY_LATER", new PayLaterOptionView(logger))
        this.PAYMENT_MODE_MAP.set("CARDLESS_EMI", new CardlessEMIPaymentOptionView(logger))
        this.PAYMENT_MODE_MAP.set("BAJAJ_FINSERV", new BajajFinservPaymentOptionView(logger))
        this.PAYMENT_MODE_MAP.set("ZESTMONEY", new ZestMoneyPaymentOptionView(logger))
        this.PAYMENT_MODE_MAP.set("CRED", new CredPaymentOptionView(logger))
        this.PAYMENT_MODE_MAP.set("TATA_PAY", new TataPayPaymentOptionView(logger))
        this.PAYMENT_MODE_MAP.set("COD_V2", new CODV2PaymentOptionView(logger, hamletService, interfaces))
    }

    public getPaymentOptionView(paymentMode: PaymentMode): BasePaymentOptionView {
        return this.PAYMENT_MODE_MAP.get(paymentMode)
    }
}

export interface IPaymentOptionViewParams {
    modeConfig: PaymentModeConfig
    order: BaseOrder
    offers: GetBestPaymentOffersResponse
    userContext: UserContext
    gearService: IGearService
    offerHelper: OfferHelper
}

export abstract class BasePaymentOptionView {

    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(HAMLET_CLIENT_TYPES.HamletService) protected hamletService?: IHamletService,
    ) { }

    public async isAvailable(params: IPaymentOptionViewParams): Promise<boolean> {
        return true
    }

    public abstract getOptions(params: IPaymentOptionViewParams): Promise<PaymentOption[]>

    protected getPaymentOfferAddon(bestPaymentOffers: GetBestPaymentOffersResponse, paymentMode: string): OfferAddon {
        if (bestPaymentOffers?.paymentOffers) {
            const offer = bestPaymentOffers.paymentOffers[paymentMode as PaymentMode]
            if (offer && offer.offer) {
                const offerAddons = offer.offer.addons
                const paymentOfferAddon: OfferAddon = _.find(offerAddons, offerAddon => {
                    return offerAddon.addonType === "PAYMENT" || offerAddon.addonType === "CASHBACK" || offerAddon.addonType === "NO_COST_EMI"
                })
                return paymentOfferAddon
            }
        }
    }

    protected getCFInstantDiscountAddon(order: BaseOrder, bestPaymentOffers: GetBestPaymentOffersResponse, paymentMode: string): { addon: OfferAddon, discount: number } {
        if (bestPaymentOffers.paymentOffers) {
            const offer = bestPaymentOffers.paymentOffers[paymentMode as PaymentMode]
            if (offer && offer.offer) {
                const offerAddons = offer.offer.addons
                const instantDiscountAddon: OfferAddon = _.find(offerAddons, (offerAddon: OfferAddon) => {
                    return offerAddon.addonType === "CF_INSTANT_DISCOUNT"
                })
                if (!_.isNil(instantDiscountAddon)) {
                    const cfInstantDiscount: number = PaymentClientUtil.calculateCFPoweredInstantDiscount(order, instantDiscountAddon)
                    if (cfInstantDiscount > 0) {
                        return {
                            addon: instantDiscountAddon,
                            discount: cfInstantDiscount
                        }
                    }
                }
            }
        }
    }

    protected getPaymentOffer(bestPaymentOffers: GetBestPaymentOffersResponse, paymentMode: string): OfferV2 {
        if (bestPaymentOffers?.paymentOffers) {
            const offer = bestPaymentOffers.paymentOffers[paymentMode as PaymentMode]
            if (offer) {
                return offer.offer
            }
        }
        return undefined
    }

    protected getPaymentIconURL(paymentOffer: OfferV2): string {
        const iconURL: string = _.get(paymentOffer, "paymentIconURL")
        return !_.isEmpty(iconURL) ? iconURL : undefined
    }

    protected async isCodV2ExperimentAvailable(productIds: string[], userContext: UserContext, interfaces: CFServiceInterfaces): Promise<boolean> {
        return AppUtil.doesUserBelongToCyclesCODV2EnabledSegment(productIds, userContext, interfaces)
    }

}

export class CreditCardDebitCardPaymentOptionView extends BasePaymentOptionView {
    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger)
    }

    public async getOptions(params: IPaymentOptionViewParams): Promise<PaymentOption[]> {
        const isSubscriptionAutoRenew: boolean = AppUtil.isSubscriptionTypePresent(params.order.products) && AppUtil.isAutoRenewEnabled(params.order.products)

        const options: PaymentOption[] = []

        const paymentOffer: OfferV2 = this.getPaymentOffer(params.offers, "DEBIT_OR_CREDIT_CARD")
        const instantDiscountResponse: { addon: OfferAddon, discount: number } = this.getCFInstantDiscountAddon(params.order, params.offers, "DEBIT_OR_CREDIT_CARD")
        const cardOfferOptions: PaymentOption[] = await this.getCreditCardDebitCardOfferOptions(params, instantDiscountResponse, paymentOffer)
        if (!_.isEmpty(cardOfferOptions)) {
            options.push(...cardOfferOptions)
        }

        // Default CC/DC option without offers
        if (!this.isCFInstantDiscountApplicable(params, instantDiscountResponse, paymentOffer) && !(isSubscriptionAutoRenew && params.modeConfig.availablePaymentChannels.indexOf("RAZORPAY") < 0)) {
            options.push({
                displayName: !_.isEmpty(params.modeConfig.displayName) ? params.modeConfig.displayName : "Credit / Debit Card",
                type: "DEBIT_OR_CREDIT_CARD",
                iconType: "DEBIT_OR_CREDIT_CARD",
                gatewayType: isSubscriptionAutoRenew ? "RAZORPAY" : params.modeConfig.preferredPaymentChannel,
                createGatewayOrderPayload: {
                    gatewayType: isSubscriptionAutoRenew ? "RAZORPAY" : params.modeConfig.preferredPaymentChannel,
                    meta: {
                        selectedPaymentMode: "DEBIT_OR_CREDIT_CARD"
                    },
                    paymentMode: "DEBIT_OR_CREDIT_CARD"
                },
                supportedModes: isSubscriptionAutoRenew ? ["ICICI", "CITI", "CANARA", "KOTAK"] : undefined,
                supportsAutoRenew: isSubscriptionAutoRenew,
                isServiceUnreachable: false,
                disableText: undefined
            })
        }
        return options
    }

    private async getCreditCardDebitCardOfferOptions(params: IPaymentOptionViewParams, instantDiscountResponse: { addon: OfferAddon, discount: number }, paymentOffer: OfferV2): Promise<PaymentOption[]> {
        try {
            const options: PaymentOption[] = []

            const isSubscription: boolean = AppUtil.isSubscriptionTypePresent(params.order.products)
            const isSubscriptionAutoRenew: boolean = isSubscription && AppUtil.isAutoRenewEnabled(params.order.products)

            const paymentOfferAddon: OfferAddon = this.getPaymentOfferAddon(params.offers, "DEBIT_OR_CREDIT_CARD")
            const isRazorpayCCDCAddon: boolean = paymentOfferAddon?.config?.paymentBank === "RAZORPAY"
            const creditCardPaymentOfferAddon: OfferAddon = this.getPaymentOfferAddon(params.offers, "CREDIT_CARD")
            const isRazorpayCCAddon: boolean = creditCardPaymentOfferAddon?.config?.paymentBank === "RAZORPAY"
            const creditCardPaymentOffer: OfferV2 = this.getPaymentOffer(params.offers, "CREDIT_CARD")

            // curefit powered instant discount
            if (this.isCFInstantDiscountApplicable(params, instantDiscountResponse, paymentOffer)) {
                let defaultOptionDisplayName: string = "Credit / Debit Card"
                if (!_.isEmpty(params.modeConfig.displayName)) {
                    defaultOptionDisplayName = params.modeConfig.displayName
                }
                const paymentDisplayName: string = _.get(paymentOffer, "paymentDisplayName", defaultOptionDisplayName)
                const paymentIconType: string = _.get(paymentOffer, "paymentIconType", "DEBIT_OR_CREDIT_CARD")
                const paymentIconURL: string = this.getPaymentIconURL(paymentOffer)
                options.push({
                    displayName: paymentDisplayName,
                    type: "DEBIT_OR_CREDIT_CARD",
                    iconType: paymentIconType,
                    iconURL: paymentIconURL,
                    gatewayType: params.modeConfig.preferredPaymentChannel,
                    createGatewayOrderPayload: {
                        gatewayType: params.modeConfig.preferredPaymentChannel,
                        paymentMode: "DEBIT_OR_CREDIT_CARD",
                        meta: {
                            selectedPaymentMode: "DEBIT_OR_CREDIT_CARD"
                        }
                    },
                    isServiceUnreachable: false,
                    disableText: undefined,
                    offerText: instantDiscountResponse.addon.description,
                    originalAmount: params.order.totalAmountPayable,
                    discountedAmount: _.round(params.order.totalAmountPayable - instantDiscountResponse.discount, 2)
                })
            }

            // Option for offers on only CC in India
            if (
                OrderUtil.getCurrency(params.order) === "INR" &&
                !_.isNil(creditCardPaymentOfferAddon) &&
                this.isOfferValidForProduct(params.order, creditCardPaymentOffer)
            ) {
                let defaultOptionDisplayName: string = "Credit Card"
                if (!_.isEmpty(params.modeConfig.displayName)) {
                    defaultOptionDisplayName = params.modeConfig.displayName
                }
                const paymentDisplayName: string = _.get(creditCardPaymentOffer, "paymentDisplayName", defaultOptionDisplayName)
                const paymentIconType: string = _.get(creditCardPaymentOffer, "paymentIconType", "DEBIT_OR_CREDIT_CARD")
                const paymentIconURL: string = this.getPaymentIconURL(creditCardPaymentOffer)
                options.push({
                    displayName: paymentDisplayName,
                    type: "DEBIT_OR_CREDIT_CARD",
                    iconType: paymentIconType,
                    iconURL: paymentIconURL,
                    gatewayType: isRazorpayCCAddon ? "RAZORPAY" : params.modeConfig.preferredPaymentChannel,
                    createGatewayOrderPayload: {
                        gatewayType: isRazorpayCCAddon ? "RAZORPAY" : params.modeConfig.preferredPaymentChannel,
                        paymentMode: "CREDIT_CARD",
                        meta: {
                            selectedPaymentMode: "DEBIT_OR_CREDIT_CARD"
                        }
                    },
                    isServiceUnreachable: false,
                    disableText: undefined,
                    offerText: creditCardPaymentOfferAddon.description
                })

            }

            // Option for offers on CC or DC in India
            if (
                OrderUtil.getCurrency(params.order) === "INR" &&
                !_.isNil(paymentOfferAddon) &&
                this.isOfferValidForProduct(params.order, paymentOffer)
            ) {
                let defaultOptionDisplayName: string = "Credit / Debit Card"
                if (!_.isEmpty(params.modeConfig.displayName)) {
                    defaultOptionDisplayName = params.modeConfig.displayName
                }
                const paymentDisplayName: string = _.get(paymentOffer, "paymentDisplayName", defaultOptionDisplayName)
                const paymentIconType: string = _.get(paymentOffer, "paymentIconType", "HDFC")
                const paymentIconURL: string = this.getPaymentIconURL(paymentOffer)
                options.push({
                    displayName: paymentDisplayName,
                    type: "DEBIT_OR_CREDIT_CARD",
                    iconType: paymentIconType,
                    iconURL: paymentIconURL,
                    gatewayType: isRazorpayCCDCAddon ? "RAZORPAY" : params.modeConfig.preferredPaymentChannel,
                    createGatewayOrderPayload: {
                        gatewayType: isRazorpayCCDCAddon ? "RAZORPAY" : params.modeConfig.preferredPaymentChannel,
                        paymentMode: "DEBIT_OR_CREDIT_CARD",
                        meta: {
                            selectedPaymentMode: "DEBIT_OR_CREDIT_CARD"
                        }
                    },
                    isServiceUnreachable: false,
                    disableText: undefined,
                    offerText: paymentOfferAddon.description
                })

            }

            // Credit card for food subscription, for autoRenew check if Razorpay is available
            if (isSubscription && !(isSubscriptionAutoRenew && params.modeConfig.availablePaymentChannels.indexOf("RAZORPAY") < 0)) {
                let defaultOptionDisplayName: string = "Credit Card"
                if (!_.isEmpty(params.modeConfig.displayName)) {
                    defaultOptionDisplayName = params.modeConfig.displayName
                }
                const paymentDisplayName: string = _.get(paymentOffer, "paymentDisplayName", defaultOptionDisplayName)
                const paymentIconType: string = _.get(paymentOffer, "paymentIconType", "DEBIT_OR_CREDIT_CARD")
                const paymentIconURL: string = this.getPaymentIconURL(paymentOffer)
                options.push({
                    displayName: paymentDisplayName,
                    type: "DEBIT_OR_CREDIT_CARD",
                    iconType: paymentIconType,
                    iconURL: paymentIconURL,
                    gatewayType: isSubscriptionAutoRenew ? "RAZORPAY" : params.modeConfig.preferredPaymentChannel,
                    createGatewayOrderPayload: {
                        gatewayType: isSubscriptionAutoRenew ? "RAZORPAY" : params.modeConfig.preferredPaymentChannel,
                        meta: {
                            selectedPaymentMode: "DEBIT_OR_CREDIT_CARD"
                        },
                        paymentMode: "DEBIT_OR_CREDIT_CARD"
                    },
                    supportsAutoRenew: isSubscriptionAutoRenew,
                    isServiceUnreachable: false,
                    offerText: !_.isNil(paymentOfferAddon) ? paymentOfferAddon.description : undefined
                })
            }

            return options
        } catch (err) {
            this.logger.error(`Error while building offer payment options for CC/DC.`, { err })
        }

    }

    private isCFInstantDiscountApplicable(
        params: IPaymentOptionViewParams,
        instantDiscountResponse: { addon: OfferAddon, discount: number },
        paymentOffer: OfferV2
    ): boolean {
        return OrderUtil.getCurrency(params.order) === "INR" &&
            !_.isNil(instantDiscountResponse) &&
            params.order.totalAmountPayable > instantDiscountResponse.discount &&
            this.isOfferValidForProduct(params.order, paymentOffer)
    }

    private isOfferValidForProduct(order: BaseOrder, offer: OfferV2): boolean {
        const product: OrderProductSnapshots = order.productSnapshots[0]
        const isCultSubscriptionOrder: boolean = product.isPack && AppUtil.isSubscriptionTypePresent(order.products) && (product.productType === "FITNESS" || product.productType === "MIND")
        const isValid: boolean = (
            ((product.productType === "FITNESS" || product.productType === "MIND") && !isCultSubscriptionOrder)
            || product.productType === "PLAY"
            || product.productType === "FITNESS_FIRST_PACK"
            || product.productType === "BUNDLE"
            || product.productType === "GYMFIT_FITNESS_PACK"
            || product.productType === "THIRD_PARTY_FITNESS_PRODUCT"
            || product.productType === "GYMFIT_FITNESS_PRODUCT"
            || product.productType === "CF_LIVE"
            || product.productType === "CONSULTATION"
            || product.productType === "DIAGNOSTICS"
            || product.productType === "GEAR"
            || product.productType === "CULT_BIKE"
        )
        const isOfferValidForProductType: boolean = offer.productType === product.productType
        if (isValid ? !isOfferValidForProductType : isOfferValidForProductType) {
            this.logger.info(`CC/DC offer config mismatch.`, {
                orderId: order.orderId,
                orderProductType: product.productType,
                offerProductType: offer.productType
            })
        }
        return isValid
    }
}

export class EmiPaymentOptionView extends BasePaymentOptionView {
    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger)
    }

    public async getOptions(params: IPaymentOptionViewParams): Promise<PaymentOption[]> {
        const options: PaymentOption[] = []
        if (this.isEMIApplicableForOrder(params) && params.modeConfig.availablePaymentChannels.indexOf(params.modeConfig.preferredPaymentChannel) > -1) {
            const emiOfferAddon: OfferAddon = this.getPaymentOfferAddon(params.offers, "EMI")
            const paymentOffer: OfferV2 = this.getPaymentOffer(params.offers, "EMI")
            let defaultDisplayName: string = params.modeConfig.preferredPaymentChannel === "JUSPAY" ? "Credit/Debit Card EMI" : "Credit Card EMI"
            if (!_.isNil(emiOfferAddon)) {
                defaultDisplayName = "No Cost " + defaultDisplayName
            }
            if (!_.isEmpty(params.modeConfig.displayName)) {
                defaultDisplayName = params.modeConfig.displayName
            }
            const paymentDisplayName: string = _.get(paymentOffer, "paymentDisplayName", undefined)
            const paymentIconType: string = _.get(paymentOffer, "paymentIconType", undefined)
            const paymentIconURL: string = this.getPaymentIconURL(paymentOffer)
            const ncEMIChannel: PaymentChannel = this.getPaymentChannelForNoCostEMI(params, emiOfferAddon)
            const paymentOption: PaymentOption = {
                displayName: !_.isNil(paymentDisplayName) ? paymentDisplayName : defaultDisplayName,
                type: "DEBIT_OR_CREDIT_CARD",
                iconType: !_.isNil(paymentIconType) ? paymentIconType : "DEBIT_OR_CREDIT_CARD",
                iconURL: !_.isNil(paymentIconURL) ? paymentIconURL : undefined,
                gatewayType: ncEMIChannel,
                createGatewayOrderPayload: {
                    gatewayType: ncEMIChannel,
                    paymentMode: "EMI",
                    meta: {
                        selectedPaymentMode: "EMI"
                    }
                },
                isServiceUnreachable: false,
                disableText: undefined,
                offerText: emiOfferAddon?.description
            }
            options.push(paymentOption)
        }
        return options
    }

    private isWholeFitOrder(order: BaseOrder): boolean {
        const listingBrand: ListingBrandIdType = PaymentOptionsBusinessV2.getListingBrandFromOrder(order)
        if (listingBrand === "WHOLE_FIT") {
            return true
        } else {
            // Check if any of the product is WHOLE_FIT
            const productSnapshots: OrderProductSnapshots[] = order.productSnapshots
            if (!_.isNil(productSnapshots)) {
                for (const productSnapshot of productSnapshots) {
                    const listingBrands = _.get(productSnapshot, "listingBrands", [])
                    if (listingBrands.indexOf("WHOLE_FIT") > -1) {
                        return true
                    }
                }
            }
        }
        return false
    }

    private isEMIApplicableForOrder(params: IPaymentOptionViewParams): boolean {
        const product: OrderProductSnapshots = params.order.productSnapshots[0]
        const isSubscription: boolean = AppUtil.isSubscriptionTypePresent(params.order.products)
        const isWholeFitOrder: boolean = this.isWholeFitOrder(params.order)
        const billingInfo = params.offerHelper.getOrderBilling(params.order)
        return (
            ((product.productType === "FITNESS" || product.productType === "MIND" || isWholeFitOrder) && !isSubscription)
            || product.productType === "PLAY"
            || product.productType === "FITNESS_FIRST_PACK"
            || product.productType === "BUNDLE"
            || product.productType === "GYMFIT_FITNESS_PACK"
            || product.productType === "THIRD_PARTY_FITNESS_PRODUCT"
            || product.productType === "GYMFIT_FITNESS_PRODUCT"
            || product.productType === "CULT_BIKE"
            || product.productType === "GEAR"
            || product.productType === "GYM_PT_PRODUCT"
        ) && billingInfo && billingInfo.amountPayable >= 2000
    }

    private getPaymentChannelForNoCostEMI(params: IPaymentOptionViewParams, emiOfferAddon: OfferAddon): PaymentChannel {
        let selectedPaymentChannel: PaymentChannel
        if (!_.isEmpty(params?.modeConfig?.preferredOfferModePaymentChannel) && !_.isEmpty(emiOfferAddon)) {
            if (!_.isEmpty(params?.modeConfig?.preferredOfferModePaymentChannel["NO_COST_EMI"])) {
                selectedPaymentChannel = params?.modeConfig?.preferredOfferModePaymentChannel["NO_COST_EMI"]
            } else if (!_.isEmpty(params?.modeConfig?.preferredOfferModePaymentChannel["default"])) {
                selectedPaymentChannel = params?.modeConfig?.preferredOfferModePaymentChannel["default"]
            } else {
                selectedPaymentChannel = "RAZORPAY"
            }
        }
        if (_.isEmpty(selectedPaymentChannel)) {
            selectedPaymentChannel = "RAZORPAY"
        }
        return selectedPaymentChannel
    }

}

export class PaytmPaymentOptionView extends BasePaymentOptionView {
    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger)
    }

    public async getOptions(params: IPaymentOptionViewParams): Promise<PaymentOption[]> {
        const paymentOffer = this.getPaymentOfferAddon(params.offers, params.modeConfig.preferredPaymentChannel)
        const options: PaymentOption[] = [{
            displayName: !_.isEmpty(params.modeConfig.displayName) ? params.modeConfig.displayName : "Paytm",
            type: "PAYTM",
            iconType: "PAYTM",
            gatewayType: params.modeConfig.preferredPaymentChannel,
            createGatewayOrderPayload: {
                gatewayType: params.modeConfig.preferredPaymentChannel,
                paymentMode: "PAYTM"
            },
            isLinked: params.modeConfig.isUserAccountLinked,
            isServiceUnreachable: params.modeConfig.isServiceUnreachable,
            supportsAutoRenew: false,
            disableText: undefined,
            offerText: !_.isNil(paymentOffer) ? paymentOffer.description : undefined
        }]
        return options
    }
}

export class NetBankingPaymentOptionView extends BasePaymentOptionView {
    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger)
    }

    public async getOptions(params: IPaymentOptionViewParams): Promise<PaymentOption[]> {
        const paymentOffer = this.getPaymentOfferAddon(params.offers, "NET_BANKING")
        return [{
            displayName: !_.isEmpty(params.modeConfig.displayName) ? params.modeConfig.displayName : "Net Banking",
            type: "NET_BANKING",
            iconType: "NET_BANKING",
            gatewayType: params.modeConfig.preferredPaymentChannel, // moving netbanking to razorpay completely
            createGatewayOrderPayload: {
                gatewayType: params.modeConfig.preferredPaymentChannel,
                paymentMode: "NET_BANKING",
                meta: {
                    selectedPaymentMode: "NET_BANKING"
                }
            },
            isServiceUnreachable: false,
            disableText: undefined,
            offerText: !_.isNil(paymentOffer) ? paymentOffer.description : undefined
        }]
    }
}

export class PhonePePaymentOptionView extends BasePaymentOptionView {
    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger)
    }

    public async getOptions(params: IPaymentOptionViewParams): Promise<PaymentOption[]> {
        const paymentOffer = this.getPaymentOfferAddon(params.offers, "PHONEPE")
        return [{
            displayName: !_.isEmpty(params.modeConfig.displayName) ? params.modeConfig.displayName : "PhonePe/BHIM UPI",
            type: "PHONEPE",
            gatewayType: params.modeConfig.preferredPaymentChannel,
            iconType: "PHONEPE",
            createGatewayOrderPayload: {
                gatewayType: params.modeConfig.preferredPaymentChannel,
                paymentMode: "PHONEPE"
            },
            isServiceUnreachable: false,
            disableText: undefined,
            offerText: !_.isNil(paymentOffer) ? paymentOffer.description : undefined
        }]
    }
}

export class AmazonPayPaymentOptionView extends BasePaymentOptionView {
    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger)
    }

    public async getOptions(params: IPaymentOptionViewParams): Promise<PaymentOption[]> {
        const paymentOffer = this.getPaymentOfferAddon(params.offers, "AMAZONPAY")
        return [{
            displayName: !_.isEmpty(params.modeConfig.displayName) ? params.modeConfig.displayName : "Amazon Pay",
            type: "AMAZONPAY",
            iconType: "AMAZONPAY",
            gatewayType: params.modeConfig.preferredPaymentChannel,
            createGatewayOrderPayload: {
                gatewayType: params.modeConfig.preferredPaymentChannel,
                paymentMode: "AMAZONPAY"
            },
            isServiceUnreachable: false,
            disableText: undefined,
            offerText: !_.isNil(paymentOffer) ? paymentOffer.description : undefined
        }]
    }
}

export class PaypalPaymentOptionView extends BasePaymentOptionView {
    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger)
    }

    public async getOptions(params: IPaymentOptionViewParams): Promise<PaymentOption[]> {
        const options: PaymentOption[] = []
        const paypalOffer = this.getPaymentOfferAddon(params.offers, "PAYPAL")
        const paypalRTOffer = this.getPaymentOfferAddon(params.offers, "PAYPAL_RT")
        const isPaypalVerifyLinkServiceDown: boolean = params.modeConfig.isServiceUnreachable
        const isPaypalAccountLinked: boolean = params.modeConfig.isUserAccountLinked
        const orderAmount: number = _.get(params.order, "totalAmountPayable", 0)
        const currency: string = OrderUtil.getCurrency(params.order)
        const selectedPaymentChannel: PaymentChannel = params.modeConfig.preferredPaymentChannel
        const regularCheckoutPaypal: PaymentOption = {
            displayName: "PayPal",
            type: "PAYPAL",
            gatewayType: selectedPaymentChannel,
            iconType: "PAYPAL",
            createGatewayOrderPayload: {
                gatewayType: selectedPaymentChannel,
                paymentMode: "PAYPAL"
            },
            description: "Credit & Debit Cards Only",
            isServiceUnreachable: false,
            disableText: undefined,
            offerText: _.get(paypalOffer, "description", undefined)
        }
        if (orderAmount >= 2000 || isPaypalVerifyLinkServiceDown || currency !== "INR") {
            options.push(regularCheckoutPaypal)
        } else if (!isPaypalAccountLinked) {
            let multimodeOfferDescription: string = _.get(params.offers, "paymentOffers.PAYPAL.offer.multimodeOfferDescription", undefined)
            if (_.isNil(multimodeOfferDescription)) {
                multimodeOfferDescription = _.get(params.offers, "paymentOffers.PAYPAL_RT.offer.multimodeOfferDescription", undefined)
            }
            const paypalPopUpOption: PaymentOption = {
                displayName: "PayPal",
                displayType: "POPUP",
                type: "PAYPAL",
                gatewayType: selectedPaymentChannel,
                iconType: "PAYPAL",
                createGatewayOrderPayload: {
                    gatewayType: selectedPaymentChannel,
                    paymentMode: "PAYPAL"
                },
                isServiceUnreachable: false,
                disableText: undefined,
                offerText: multimodeOfferDescription,
                subOptions: []
            }
            paypalPopUpOption.subOptions.push({
                displayName: "Pay in One Click",
                type: "PAYPAL_RT",
                gatewayType: selectedPaymentChannel,
                iconType: "PAYPAL",
                createGatewayOrderPayload: {
                    gatewayType: selectedPaymentChannel,
                    paymentMode: "PAYPAL_RT"
                },
                description: "Credit Cards Only. Skip OTP. Valid for orders below Rs.2000",
                isServiceUnreachable: false,
                isLinked: isPaypalAccountLinked,
                disableText: undefined,
                offerText: _.get(paypalRTOffer, "description", undefined)
            })
            paypalPopUpOption.subOptions.push(regularCheckoutPaypal)
            options.push(paypalPopUpOption)
        } else {
            options.push({
                displayName: "Faster Way to Pay",
                type: "PAYPAL_RT",
                gatewayType: selectedPaymentChannel,
                iconType: "PAYPAL",
                createGatewayOrderPayload: {
                    gatewayType: selectedPaymentChannel,
                    paymentMode: "PAYPAL_RT"
                },
                isServiceUnreachable: false,
                isLinked: isPaypalAccountLinked,
                disableText: undefined,
                offerText: _.get(paypalRTOffer, "description", undefined)
            })
        }
        return options
    }
}

export class UPIPaymentOptionView extends BasePaymentOptionView {
    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger)
    }

    public async getOptions(params: IPaymentOptionViewParams): Promise<PaymentOption[]> {
        const paymentOffer = this.getPaymentOfferAddon(params.offers, "UPI")
        const selectedPaymentChannel: PaymentChannel = params.modeConfig.preferredPaymentChannel
        return [{
            displayName: !_.isEmpty(params.modeConfig.displayName) ? params.modeConfig.displayName : "UPI",
            type: "UPI",
            iconType: "UPI",
            gatewayType: selectedPaymentChannel,
            createGatewayOrderPayload: {
                gatewayType: selectedPaymentChannel,
                paymentMode: "UPI",
                meta: {
                    selectedPaymentMode: "UPI"
                }
            },
            isServiceUnreachable: false,
            disableText: undefined,
            offerText: !_.isNil(paymentOffer) ? paymentOffer.description : undefined
        }]
    }
}

export class WalletPaymentOptionView extends BasePaymentOptionView {
    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger)
    }

    public async getOptions(params: IPaymentOptionViewParams): Promise<PaymentOption[]> {
        const paymentOffer = this.getPaymentOfferAddon(params.offers, "WALLET")
        const selectedPaymentChannel: PaymentChannel = params.modeConfig.preferredPaymentChannel
        return [{
            displayName: !_.isEmpty(params.modeConfig.displayName) ? params.modeConfig.displayName : "Wallet",
            type: "WALLET",
            iconType: "WALLET",
            gatewayType: selectedPaymentChannel, // Since juspay supports only paytm
            createGatewayOrderPayload: {
                gatewayType: selectedPaymentChannel,
                paymentMode: "WALLET",
                meta: {
                    selectedPaymentMode: "WALLET"
                }
            },
            isServiceUnreachable: false,
            disableText: undefined,
            offerText: !_.isNil(paymentOffer) ? paymentOffer.description : undefined
        }]
    }
}

export class SodexoPaymentOptionView extends BasePaymentOptionView {
    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger)
    }

    public async isAvailable(params: IPaymentOptionViewParams): Promise<boolean> {
        const _isAvailable: boolean = await super.isAvailable(params)
        return _isAvailable && this.isEatOrder(params.order)
    }

    public async getOptions(params: IPaymentOptionViewParams): Promise<PaymentOption[]> {
        const paymentOffer = this.getPaymentOfferAddon(params.offers, "SODEXO")
        const selectedPaymentChannel: PaymentChannel = params.modeConfig.preferredPaymentChannel
        return [{
            displayName: !_.isEmpty(params.modeConfig.displayName) ? params.modeConfig.displayName : "Sodexo",
            type: "SODEXO",
            iconType: "SODEXO",
            gatewayType: selectedPaymentChannel,
            createGatewayOrderPayload: {
                gatewayType: selectedPaymentChannel,
                paymentMode: "SODEXO",
                meta: {
                    selectedPaymentMode: "SODEXO"
                }
            },
            isServiceUnreachable: false,
            disableText: undefined,
            offerText: !_.isNil(paymentOffer) ? paymentOffer.description : undefined
        }]
    }

    private isEatOrder(order: BaseOrder): boolean {
        if (!_.isNil(order)) {
            let isEatOrder: boolean = true
            order.productSnapshots.forEach((prodSnapshot) => {
                if (prodSnapshot.productType !== "FOOD") {
                    isEatOrder = false
                }
            })
            return isEatOrder
        }
        return false
    }

}

export class CODPaymentOptionView extends BasePaymentOptionView {
    constructor(
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private interfaces: CFServiceInterfaces,
        @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(HAMLET_CLIENT_TYPES.HamletService) protected hamletService: IHamletService,
    ) {
        super(logger, hamletService)
    }

    private MAX_COD_ORDER_AMOUNT = 10000
    private STORE_MAX_COD_ORDER_AMOUNT = 45000
    private STORE_MAX_COD_ORDER_AMOUNT_V2 = 65000

    public async isAvailable(params: IPaymentOptionViewParams): Promise<boolean> {
        const isCodAvailable = await this.isCodAvailable(params)
        const _isAvailable: boolean = await super.isAvailable(params) && isCodAvailable
        const productIds: string[] = _.compact(_.map(params.order?.productSnapshots, p => p.masterProductId))
        const isCodV2ExperimentAvailable: boolean =  await this.isCodV2ExperimentAvailable(productIds, params.userContext, this.interfaces)
        return _isAvailable && this.isGearOrder(params.order) && !isCodV2ExperimentAvailable
    }

    public async getOptions(params: IPaymentOptionViewParams): Promise<PaymentOption[]> {
        const paymentOffer = this.getPaymentOffer(params.offers, "COD")
        const selectedPaymentChannel: PaymentChannel = params.modeConfig.preferredPaymentChannel
        this.logger.info(`COD_V1 option will be displayed for orderId: ${params.order?.orderId}`)
        return [{
            displayName: !_.isEmpty(params.modeConfig.displayName) ? params.modeConfig.displayName : "Pay on Delivery",
            type: "COD",
            iconType: "COD",
            gatewayType: selectedPaymentChannel,
            createGatewayOrderPayload: {
                gatewayType: selectedPaymentChannel,
                paymentMode: "COD",
                meta: {
                    selectedPaymentMode: "COD"
                }
            },
            isServiceUnreachable: false,
            disableText: undefined,
            offerText: !_.isNil(paymentOffer) ? paymentOffer.description : undefined
        }]
    }

    private isGearOrder(order: BaseOrder): boolean {
        if (!_.isNil(order)) {
            let isGearOrder: boolean = true
            order.productSnapshots.forEach((prodSnapshot) => {
                if (prodSnapshot.productType !== "GEAR") {
                    isGearOrder = false
                }
            })
            return isGearOrder
        }
        return false
    }

    private async isCodAvailable(params: IPaymentOptionViewParams): Promise<boolean> {
        const sessionData: SessionData = params.userContext.sessionInfo.sessionData
        const pincode = _.get(params.order, "userAddress.structuredAddress.pincode")
        const orderAmount: number = _.get(params.order, "totalAmountPayable", 0)
        const productIds: string[] = _.compact(_.map(params.order?.productSnapshots, p => p.masterProductId))
        const variantSkus: string[] = _.compact(_.map(params.order?.productSnapshots, p => GearService.cfProductIdToGearSkuName(p.productId)))
        const gearArticleTypes: string[] = _.compact(_.map(params.order?.productSnapshots, p => p.gearArticleType))
        const isGearOrder = this.isGearOrder(params.order)
        // Incrementing cod amount for gear order
        let MAX_COD_ORDER_AMOUNT = isGearOrder
            ? this.STORE_MAX_COD_ORDER_AMOUNT
            : this.MAX_COD_ORDER_AMOUNT

        // Increase cod to 65k from 45k incase of user is part of this segment
        if (AppUtil.isProdLike && !_.isEmpty(await this.segmentService.doesUserBelongToSegment(AppUtil.cultGear65KCODEnabledSegment(), params.userContext)) ) {
            MAX_COD_ORDER_AMOUNT = this.STORE_MAX_COD_ORDER_AMOUNT_V2
        }
        if (orderAmount > MAX_COD_ORDER_AMOUNT) {
            return false
        }

        if (!params.userContext.userProfile.promiseMapCache) {
            params.userContext.userProfile.promiseMapCache ??= new PromiseCache(this.interfaces)
        }

        if (isGearOrder) {
            // For trainer kit products, disabling cod
            if (AppUtil.doesUserHasTrainerKitProductIdInCart(productIds)) {
                return false
            }
            if (!_.isNil(pincode)) {
                for (const variantSku of variantSkus) {
                    const serviceability = await params.gearService.getOrderServiceability(pincode, variantSku, [ServiceabilityType.COD], undefined, params.userContext.userProfile.userId)
                    if (!serviceability.cod) return false
                }
                return true
            }
        }
        return false
    }

}

export class CODV2PaymentOptionView extends BasePaymentOptionView {

    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(HAMLET_CLIENT_TYPES.HamletService) protected hamletService: IHamletService,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private interfaces: CFServiceInterfaces,
    ) {
        super(logger, hamletService)
    }

    public async isAvailable(params: IPaymentOptionViewParams): Promise<boolean> {
        const productIds: string[] = _.compact(_.map(params.order?.productSnapshots, p => p.masterProductId))
        return await this.isCodV2ExperimentAvailable(productIds, params.userContext, this.interfaces)
    }

    public async getOptions(params: IPaymentOptionViewParams): Promise<PaymentOption[]> {
        const cultSportRiskProfile: GearPaymentOptionsResponse = await this.getCultSportRiskProfile(params)
        this.logger.info(`COD_V2 option risk profile for orderId: ${params.order?.orderId}`, { cultSportRiskProfile })
        if (!cultSportRiskProfile || !cultSportRiskProfile.is_cod_available) {
            this.logger.info(`COD_V2 option will NOT be displayed for orderId: ${params.order?.orderId} due to risk profile.`)
            return
        }
        let codSubtitle: string
        if (cultSportRiskProfile.pre_payment_amount_in_lowest_denomination && cultSportRiskProfile.pre_payment_amount_in_lowest_denomination > 0) {
            codSubtitle = `Online payment of ₹${Math.round(cultSportRiskProfile.pre_payment_amount_in_lowest_denomination / 100)} is required to place the COD order`
        }
        let isServiceUnreachable: boolean = false
        let disableText: string = undefined
        if (_.defaultTo(params.order.totalFitCashPayable, 0) > 0) {
            isServiceUnreachable = true
            disableText = "Fitcash is not allowed. Please un-select Fitcash if you want to Pay on Delivery."
        }
        this.logger.info(`COD_V2 option will be displayed for orderId: ${params.order?.orderId}`)
        return [{
            displayName: !_.isEmpty(params.modeConfig.displayName) ? params.modeConfig.displayName : "Pay on Delivery",
            type: "COD_V2",
            iconType: "COD",
            gatewayType: params.modeConfig.preferredPaymentChannel,
            createGatewayOrderPayload: {
                gatewayType: params.modeConfig.preferredPaymentChannel,
                paymentMode: "COD_V2",
                meta: {
                    selectedPaymentMode: "COD_V2"
                }
            },
            isServiceUnreachable: isServiceUnreachable,
            disableText: disableText,
            offerText: codSubtitle
        }]
    }

    public async getCultSportRiskProfile(params: IPaymentOptionViewParams): Promise<GearPaymentOptionsResponse> {
        try {
            return await params.gearService.fetchPaymentOptions(params.order.orderId)
        } catch (err) {
            this.logger.error(`Failed to fetch gear COD risk profile`, { err })
        }
    }
}

export class PayLaterOptionView extends BasePaymentOptionView {
    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger)
    }

    public async getOptions(params: IPaymentOptionViewParams): Promise<PaymentOption[]> {
        const paymentOffer = this.getPaymentOfferAddon(params.offers, "PAY_LATER")
        const selectedPaymentChannel: PaymentChannel = params.modeConfig.preferredPaymentChannel
        return [{
            displayName: !_.isEmpty(params.modeConfig.displayName) ? params.modeConfig.displayName : "Pay Later",
            type: "PAY_LATER",
            iconType: "PAY_LATER",
            gatewayType: selectedPaymentChannel,
            createGatewayOrderPayload: {
                gatewayType: selectedPaymentChannel,
                meta: {
                    selectedPaymentMode: "PAY_LATER"
                },
                paymentMode: "PAY_LATER"
            },
            isServiceUnreachable: false,
            disableText: undefined,
            offerText: !_.isNil(paymentOffer) ? paymentOffer.description : undefined
        }]
    }
}

export class CardlessEMIPaymentOptionView extends BasePaymentOptionView {
    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger)
    }

    public async getOptions(params: IPaymentOptionViewParams): Promise<PaymentOption[]> {
        const billingInfo = params.offerHelper.getOrderBilling(params.order)
        const options: PaymentOption[] = []
        const selectedPaymentChannel: PaymentChannel = params.modeConfig.preferredPaymentChannel

        if (billingInfo && billingInfo.amountPayable >= 1000) {
            const cardlessEMIOption: PaymentOption = {
                displayName: !_.isEmpty(params.modeConfig.displayName) ? params.modeConfig.displayName : "Bank EMI, ZestMoney & EarlySalary",
                type: "CARDLESS_EMI",
                iconType: "DEBIT_OR_CREDIT_CARD",
                gatewayType: selectedPaymentChannel,
                createGatewayOrderPayload: {
                    gatewayType: selectedPaymentChannel,
                    meta: {
                        selectedPaymentMode: "CARDLESS_EMI"
                    },
                    paymentMode: "CARDLESS_EMI"
                },
                isServiceUnreachable: false,
                disableText: undefined,
                offerText: undefined
            }
            options.push(cardlessEMIOption)
        }
        return options
    }
}

export class BajajFinservPaymentOptionView extends BasePaymentOptionView {
    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger)
    }

    public async getOptions(params: IPaymentOptionViewParams): Promise<PaymentOption[]> {
        const product: OrderProductSnapshots = params.order.productSnapshots[0]
        const isSubscription: boolean = AppUtil.isSubscriptionTypePresent(params.order.products)
        const billingInfo = params.offerHelper.getOrderBilling(params.order)
        const options: PaymentOption[] = []
        const selectedPaymentChannel: PaymentChannel = params.modeConfig.preferredPaymentChannel

        if ((((product.productType === "FITNESS" || product.productType === "MIND") && !isSubscription)
            || product.productType === "PLAY" || product.productType === "FITNESS_FIRST_PACK" || product.productType === "BUNDLE" || product.productType === "GYMFIT_FITNESS_PACK"
            || product.productType === "THIRD_PARTY_FITNESS_PRODUCT" || product.productType === "GYMFIT_FITNESS_PRODUCT" || product.productType === "CULT_BIKE")
            && billingInfo
            && billingInfo.amountPayable >= 3000) {
            const bajajFinservOption: PaymentOption = {
                displayName: !_.isEmpty(params.modeConfig.displayName) ? params.modeConfig.displayName : "Bajaj Finserv",
                type: "BAJAJ_FINSERV",
                iconType: "BAJAJ_FINSERV",
                gatewayType: selectedPaymentChannel,
                createGatewayOrderPayload: {
                    gatewayType: selectedPaymentChannel,
                    meta: {
                        selectedPaymentMode: "BAJAJ_FINSERV"
                    },
                    paymentMode: "BAJAJ_FINSERV"
                },
                isServiceUnreachable: false,
                disableText: undefined,
                offerText: "No Cost EMI with Bajaj Finserv"
            }
            options.push(bajajFinservOption)
        }
        return options
    }
}

export class ZestMoneyPaymentOptionView extends BasePaymentOptionView {
    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger)
    }

    public async getOptions(params: IPaymentOptionViewParams): Promise<PaymentOption[]> {
        const product: OrderProductSnapshots = params.order.productSnapshots[0]
        const isSubscription: boolean = AppUtil.isSubscriptionTypePresent(params.order.products)
        const billingInfo = params.offerHelper.getOrderBilling(params.order)
        const options: PaymentOption[] = []
        const selectedPaymentChannel: PaymentChannel = params.modeConfig.preferredPaymentChannel

        if ((((product.productType === "FITNESS" || product.productType === "MIND") && !isSubscription)
            || product.productType === "FITNESS_FIRST_PACK" || product.productType === "BUNDLE" || product.productType === "GYMFIT_FITNESS_PACK"
            || product.productType === "THIRD_PARTY_FITNESS_PRODUCT" || product.productType === "GYMFIT_FITNESS_PRODUCT")
            && billingInfo
            && billingInfo.amountPayable >= 3000) {
            const zestMoneyOption: PaymentOption = {
                displayName: !_.isEmpty(params.modeConfig.displayName) ? params.modeConfig.displayName : "ZestMoney",
                type: "ZESTMONEY",
                iconType: "ZESTMONEY",
                gatewayType: selectedPaymentChannel,
                createGatewayOrderPayload: {
                    gatewayType: selectedPaymentChannel,
                    meta: {
                        selectedPaymentMode: "ZESTMONEY"
                    },
                    paymentMode: "ZESTMONEY"
                },
                isServiceUnreachable: false,
                disableText: undefined,
                offerText: undefined
            }
            options.push(zestMoneyOption)
        }
        return options
    }
}

export class CredPaymentOptionView extends BasePaymentOptionView {
    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger)
    }

    public async getOptions(params: IPaymentOptionViewParams): Promise<PaymentOption[]> {
        try {
            const credPaymentOptions: PaymentOption[] = []
            const selectedPaymentChannel: PaymentChannel = params.modeConfig.preferredPaymentChannel
            const paymentOffer: OfferV2 = this.getPaymentOffer(params.offers, "CRED")
            const paymentIconURL: string = this.getPaymentIconURL(paymentOffer)
            const paymentOfferAddon: OfferAddon = this.getPaymentOfferAddon(params.offers, "CRED")
            const paymentDisplayName: string = _.get(paymentOffer, "paymentDisplayName", "CRED Pay")

            credPaymentOptions.push({
                displayName: paymentDisplayName,
                type: "CRED",
                iconType: "CRED",
                iconURL: _.defaultTo(paymentIconURL, "https://curefit-content.s3.ap-south-1.amazonaws.com/image/payment/CREDLogoSmall.png"),
                gatewayType: selectedPaymentChannel,
                createGatewayOrderPayload: {
                    gatewayType: selectedPaymentChannel,
                    meta: {
                        selectedPaymentMode: "CRED"
                    },
                    paymentMode: "CRED"
                },
                isServiceUnreachable: false,
                disableText: undefined,
                offerText: paymentOfferAddon?.description,
                isLinked: params.modeConfig.isUserAccountLinked,
                errorMessage: "You're currently not a CRED member. Become a CRED member to pay via CRED Pay and earn special perks. To proceed for now, try a different payment method."
            })
            return credPaymentOptions
        } catch (err) {
            this.logger.error(`Failed to load cred payment mode`, { err })
        }
    }
}

export class TataPayPaymentOptionView extends BasePaymentOptionView {
    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger)
    }

    public async getOptions(params: IPaymentOptionViewParams): Promise<PaymentOption[]> {
        try {
            const tataPaymentOptions: PaymentOption[] = []
            const selectedPaymentChannel: PaymentChannel = params.modeConfig.preferredPaymentChannel
            const paymentOffer: OfferV2 = this.getPaymentOffer(params.offers, "TATA_PAY")
            const paymentIconURL: string = this.getPaymentIconURL(paymentOffer)
            const paymentOfferAddon: OfferAddon = this.getPaymentOfferAddon(params.offers, "TATA_PAY")
            const paymentDisplayName: string = _.get(paymentOffer, "paymentDisplayName", "Tata Pay")
            const defaultSubtext: string = ["CUREFIT_APP", "CUREFIT_NEW_WEBSITE"].includes(params.order.source) ? "Redeem earned NeuCoins" : ""
            tataPaymentOptions.push({
                displayName: paymentDisplayName,
                type: "TATA_PAY",
                iconType: "TATA_PAY",
                iconURL: paymentIconURL,
                gatewayType: selectedPaymentChannel,
                createGatewayOrderPayload: {
                    gatewayType: selectedPaymentChannel,
                    meta: {
                        selectedPaymentMode: "TATA_PAY"
                    },
                    paymentMode: "TATA_PAY"
                },
                isServiceUnreachable: false,
                disableText: undefined,
                offerText: _.defaultTo(paymentOfferAddon?.description, defaultSubtext),
                isLinked: params.modeConfig.isUserAccountLinked
            })
            return tataPaymentOptions
        } catch (err) {
            this.logger.error(`Failed to load TATA_PAY payment mode`, { err })
        }
    }
}
