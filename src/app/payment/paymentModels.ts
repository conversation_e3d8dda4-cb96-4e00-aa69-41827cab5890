import { PaymentChannel, PaymentOptionView, PaymentOption, IPaymentChannelMerchantIdMap } from "@curefit/payment-common"
import { AutoRenewWidget } from "../common/views/WidgetView"
import { OrderMeta } from "@curefit/apps-common"
import { BaseOrder } from "@curefit/fulfilment-common"
import { OfferV2, GetBestPaymentOffersResponse } from "@curefit/offer-common"
import { IAlfredServiceEther as IAlfredService } from "@curefit/alfred-client"
import { IOrderService } from "@curefit/alfred-client"
import { UserContext } from "@curefit/userinfo-common"
import { Logger } from "@curefit/base"
import { FitClubSavings } from "@curefit/fitclub-models"
import { IGearService } from "@curefit/gearvault-client"
import { ICatalogueService } from "@curefit/catalog-client"
import { IPaymentClient } from "@curefit/payment-client"
import { ConfirmationView } from "../order/BaseOrderConfirmationViewBuilder"
import { OrderInfo } from "../order/OrderViewBuilder"

export interface PaymentSummary {
    fitCashBalance: number,
    isFitCashSelected: boolean,
    displayFitCash: boolean,
    fitCashUsed: number,
    totalAmountPayable: number,
    taxDescription: string,
    fitCashOfferText?: string,
    currency: string
}

export interface PaymentOptionExtendedView {
    paymentOptions: PaymentOptionView[],
    orderMeta: OrderMeta,
    showOnlyAutoRenewEnabledModes?: boolean
    paymentSummary: PaymentSummary
}

export interface PaymentOptionLegacyView {
    paymentOptions: PaymentOption[],
    orderMeta: OrderMeta,
    showOnlyAutoRenewEnabledModes?: boolean
    paymentSummary: PaymentSummary
}

export interface IPaymentOptionHandlerParams {
    order: BaseOrder
    offerPromise: Promise<GetBestPaymentOffersResponse>
    fitclubSavingsPromise?: Promise<FitClubSavings>
    offersAppliedPromise?: Promise<{ [key: string]: OfferV2 }>
    catalogService: ICatalogueService
    defaultSelectedChannels: Map<String, Map<string, PaymentChannel>>
    availablePaymentChannelPromise: Promise<IPaymentChannelMerchantIdMap>
    alfredService: IAlfredService
    orderService: IOrderService
    userContext: UserContext
    logger: Logger
    whitelistUsers: string[]
    gearService: IGearService
    paymentClient: IPaymentClient
}

export interface IGetOrderMetaParams {
    userContext: UserContext
    order: BaseOrder
}
export interface IGetPaymentSummaryParams {
    order: BaseOrder
    userContext: UserContext
    isFitcashEnabled: boolean
}

export interface IPaymentOptionRequestParams {
    order: BaseOrder
    userContext: UserContext
}

export interface AppleInAppUpdateReceiptViewResponse {
    orderInfo: OrderInfo
    orderConfirmationView?: ConfirmationView
    meta: AnalyticsData
}

export interface PlayStoreVerifyPurchaseViewResponse {
    orderInfo: OrderInfo
    orderConfirmationView?: ConfirmationView
    meta: AnalyticsData
}

export interface AnalyticsData {
    cleverTap: CleverTapData
}

export interface CleverTapData {
    af_price: number,
    af_content_type: string[],
    af_content_id: string[],
    af_content: string[],
    af_quantity: number
}

export interface AppleInAppRefreshReceiptViewResponse {
    orderConfirmationView?: ConfirmationView
}