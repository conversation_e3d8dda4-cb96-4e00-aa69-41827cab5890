import * as express from "express"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import { Container, inject } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import {
    ALFRED_CLIENT_TYPES,
    IFulfilmentService,
    IPaypalWebExperienceProfile
} from "@curefit/alfred-client"
import { CreateGatewayOrderResponse, OMS_API_CLIENT_TYPES, IOrderService } from "@curefit/oms-api-client"
import PaytmChecksum from "./PaytmChecksum"
import { htmlEscape } from "../util/HtmlUtil"
import { Session, UserContext } from "@curefit/userinfo-common"
import {
    AmazonPaySignRequest,
    AmazonPayVerifyRequest,
    ICheckoutDotComRequestPaymentSource,
    CheckoutDotComRequestCardPaymentResponseMin,
    AppleIAPUpdateReceiptRequest,
    AppleIAPUpdateReceiptResponse,
    WebhookResponse,
    AppleIAPRefreshReceiptResponse,
    PaytmAddMoneyPayload,
    PaymentMode,
    RazorpayXContactRequest,
    SavedCardList,
    PaymentOption,
    PaymentOptionView,
    PSBVerifyPurchaseRequest,
    PSBVerifyPurchaseResponse,
    PaymentChannel,
    DeviceType,
    InitiatePayloadRequest,
    ProcessPayloadResponse,
    InitiatePayloadResponse,
    CreatePaymentManagementPayload
} from "@curefit/payment-common"
import { Order, BaseOrder, OrderProductSnapshots, OrderSource } from "@curefit/order-common"
import { BASE_TYPES, FetchUtil, Logger } from "@curefit/base"
import { DataError, DataErrorV2, RuntimeError, RuntimeErrorV2 } from "@curefit/error-client"
import AuthMiddleware from "../auth/AuthMiddleware"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import AppUtil from "../util/AppUtil"
import { PaymentPageConfig } from "./PaymentPageConfig"
import IUserBusiness from "../user/IUserBusiness"
import * as _ from "lodash"
import { GetBestPaymentOffersResponse } from "@curefit/offer-common"
import { FITCASH_CLIENT_TYPES, IFitcashService } from "@curefit/fitcash-client"
import { CacheHelper } from "../util/CacheHelper"
import { ICerberusServiceV2 } from "@curefit/vm-models"
import { PaypalConfig } from "@curefit/config-mongo"
import { PaymentOptionExtendedView, PaymentOptionLegacyView, AnalyticsData, CleverTapData } from "./paymentModels"
import { IPaymentOptionsBusinessV2 } from "./PaymentOptionsBusinessV2"
import { PAYMENT_TYPES, IPaymentClient, PaymentClientUtil, PaymentPromUtil } from "@curefit/payment-client"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { TimeUtil } from "@curefit/util-common"
import { AppleInAppUpdateReceiptViewResponse, AppleInAppRefreshReceiptViewResponse, PlayStoreVerifyPurchaseViewResponse } from "./paymentModels"
import { ConfirmationView, ConfirmationRequestParams } from "../order/BaseOrderConfirmationViewBuilder"
import OrderConfirmationViewBuilderV1 from "../order/OrderConfirmationViewBuilderV1"
import { ErrorFactory } from "@curefit/error-client"
import { ErrorCodes } from "../error/ErrorCodes"
import { OrderUtil } from "@curefit/base-utils"

const fetch = require("node-fetch")

export function controllerFactory(kernel: Container) {

    @controller("/payment")
    class PaymentController {
        constructor(@inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
            @inject(OMS_API_CLIENT_TYPES.OrderService) protected omsApiClient: IOrderService,
            @inject(CUREFIT_API_TYPES.PaymentPageConfig) private paymentPageConfig: PaymentPageConfig,
            @inject(CUREFIT_API_TYPES.UserBusiness) private userBusiness: IUserBusiness,
            @inject(ALFRED_CLIENT_TYPES.FulfilmentService) private fulfilmentService: IFulfilmentService,
            @inject(FITCASH_CLIENT_TYPES.FitcashService) private fitcashService: IFitcashService,
            @inject(CUREFIT_API_TYPES.PaymentOptionsBusinessV2) protected paymentOptionsBusinessV2: IPaymentOptionsBusinessV2,
            @inject(CUREFIT_API_TYPES.CerberusServiceV2) private cerberusServiceV2: ICerberusServiceV2,
            @inject(CUREFIT_API_TYPES.CacheHelper) protected cacheHelper: CacheHelper,
            @inject(BASE_TYPES.FetchUtil) private fetchHelper: FetchUtil,
            @inject(PAYMENT_TYPES.IPaymentClient) protected paymentClient: IPaymentClient,
            @inject(HAMLET_TYPES.HamletBusiness) protected hamletBusiness: HamletBusiness,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
            @inject(CUREFIT_API_TYPES.OrderConfirmationViewBuilderV1) private orderConfirmationViewBuilderV1: OrderConfirmationViewBuilderV1,
            @inject(PAYMENT_TYPES.PaymentPromUtil) protected paymentPromUtil: PaymentPromUtil,
        ) {

        }


        @httpPost("/paytmSigninOtp", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        async paytmSigninOtp(req: express.Request, res: express.Response): Promise<{ state: string }> {
            const phone = req.body.phone
            const email = req.body.email
            this.logger.info(`Going with V2 PAYMENT_SERVICE Implementation- paytmSigninOtp`)
            return this.paymentClient.paytmSigninOtp(phone, email)
        }

        @httpPost("/paytmUnlink", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        async paytmUnlink(req: express.Request, res: express.Response): Promise<{}> {
            const session: Session = req.session
            const userId = session.userId
            this.logger.info(`Going with PAYMENT_SERVICE Implementation- paytmUnlink`)
            return this.paymentClient.paytmUnlink(userId)
        }

        @httpPost("/paytmVerifyOtp", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        async paytmVerifyOtp(req: express.Request, res: express.Response): Promise<{ success: boolean }> {
            const session: Session = req.session
            const userId = session.userId
            const otp = req.body.otp
            const phone = req.body.phone
            const state = req.body.state
            this.logger.info(`Going with PAYMENT_SERVICE Implementation- paytmVerifyOtp`)
            return this.paymentClient.paytmVerifyOtp(userId, otp, phone, state)
        }

        private async checkOrderBelongsToSameUser(orderId: any, userId: string, order?: BaseOrder) {
            if (_.isEmpty(order)) {
                order = await this.omsApiClient.getOrder(orderId)
            }
            if (order.userId !== userId) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_AUTHORIZED_TO_VIEW_ORDER, 401).withDebugMessage("Not authorized to view order").build()
            }
        }

        @httpPost("/paytmCheckBalance", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        async paytmCheckBalance(req: express.Request, res: express.Response): Promise<{ amount: number, url?: string, payload?: PaytmAddMoneyPayload }> {
            const session: Session = req.session
            const userId = session.userId
            const orderId = req.body.orderId
            if (!_.isEmpty(orderId)) {
                await this.checkOrderBelongsToSameUser(orderId, userId)
            }
            return this.paymentClient.paytmCheckBalance(userId, orderId)
        }

        @httpGet("/options", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        async paymentOptions(req: express.Request, res: express.Response): Promise<PaymentOptionLegacyView> {
            const userContext: UserContext = req.userContext as UserContext
            const userId: string = _.get(userContext, "userProfile.userId")
            const orderId: string = req.query.orderId
            const order: BaseOrder = await this.omsApiClient.getOrder(orderId)
            if (order.userId !== userId) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid user").withContext({
                    orderId: orderId,
                    userId: userId
                }).build()
            }
            this.logger.info(`/options getting NEW payment config, userID: ${userId}, orderID: ${orderId}`)
            this.paymentPromUtil.reportPaymentOptionsFetched(order.source, OrderUtil.getVerticalName(_.get(order, "productSnapshots[0]")))
            return this.paymentOptionsBusinessV2.getPaymentOptionsForWeb({
                order: order,
                userContext: userContext
            })
        }

        @httpGet("/options/v2", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        async paymentOptionsV2(req: express.Request, res: express.Response): Promise<PaymentOptionExtendedView> {
            const userContext: UserContext = req.userContext as UserContext
            const userId: string = _.get(userContext, "userProfile.userId")
            const orderId: string = req.query.orderId
            const order: BaseOrder = await this.omsApiClient.getOrder(orderId)
            if (order.userId !== userId) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid user").withContext({
                    orderId: orderId,
                    userId: userId
                }).build()
            }
            this.logger.info(`/options/v2 getting NEW payment config, userID: ${userId}, orderID: ${orderId}`)
            this.paymentPromUtil.reportPaymentOptionsFetched(order.source, OrderUtil.getVerticalName(_.get(order, "productSnapshots[0]")))
            return this.paymentOptionsBusinessV2.getPaymentOptionsForApp({
                order: order,
                userContext: userContext
            })
        }

        @httpPost("/juspay/initiatePayload", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        async initateJuspayPayload(req: express.Request, res: express.Response): Promise<InitiatePayloadResponse> {
            const userContext: UserContext = req.userContext as UserContext
            const userId: string = _.get(userContext, "userProfile.userId")
            const deviceId: string = _.get(userContext, "sessionInfo.deviceId")
            const appVersion: number = _.get(userContext, "sessionInfo.appVersion", 0)
            const osName: string = _.get(userContext, "sessionInfo.osName")
            const cityId = _.get(req, "session.sessionData.cityId")
            const apiKey: string = req.headers["api-key"] as string || req.headers["apikey"] as string
            const orderSource: OrderSource = AppUtil.callSource(apiKey)
            if (_.isNil(userId) || _.isNil(orderSource) || _.isNil(appVersion)) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Provide the userId, orderSource and appVersion").withContext({
                    userId, orderSource, appVersion
                }).build()
            }
            const {hyperSDKDiv, integrationType} = req.body
            const initateJuspayRequestPayload: InitiatePayloadRequest = {
                userId,
                deviceId,
                orderSource,
                appVersion,
                hyperSDKDiv,
                integrationType,
                osName: osName as DeviceType,
                meta: {
                    cityId
                }
            }
            return this.paymentClient.createInitiateJuspayPayload(initateJuspayRequestPayload)
        }


        @httpPost("/juspay/paymentManagement", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        async paymentManagementPayload(req: express.Request, res: express.Response): Promise<ProcessPayloadResponse> {
            const userContext: UserContext = req.userContext as UserContext
            const userId: string = _.get(userContext, "userProfile.userId")
            const osName: string = _.get(userContext, "sessionInfo.osName")
            const apiKey: string = req.headers["api-key"] as string || req.headers["apikey"] as string
            const orderSource: OrderSource = AppUtil.callSource(apiKey)
            if (_.isNil(userId) || _.isNil(orderSource)) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Provide the userId, orderSource").withContext({
                    userId, orderSource
                }).build()
            }
            const paymentManagementRequestPayload: CreatePaymentManagementPayload = {
                userId,
                orderSource
            }
            return this.paymentClient.createJuspayPaymentManagementPayload(paymentManagementRequestPayload)
        }

        @httpPost("/juspay/processPayload/:orderId", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        async processJuspayPayload(req: express.Request): Promise<any> {
            const orderId: string = req.params.orderId
            const redirectUrl: string = req.body.redirectUrl
            const meta: any = req.body.meta || {}
            const userContext: UserContext = req.userContext as UserContext
            const userId: string = _.get(userContext, "userProfile.userId")
            const orderSource: OrderSource = _.get(userContext, "sessionInfo.orderSource")
            const appVersion: number = _.get(userContext, "sessionInfo.appVersion", 0)
            const osName: string = _.get(userContext, "sessionInfo.osName")
            const isMweb: boolean = AppUtil.isMWeb(userContext)
            _.set(meta, "appVersion", appVersion)
            _.set(meta, "osName", osName)
            _.set(meta, "isMweb", isMweb)
            this.logger.debug(`Trying to call OMS process payload ${orderId}`)
            return this.omsApiClient.processPayload({
                orderId,
                userId,
                orderSource,
                redirectUrl,
                meta,
            })
        }

        @httpPost("/juspay/paymentEvents/:orderId", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        async publishJuspayPaymentEvents(req: express.Request): Promise<any> {
            const orderId: string = req.params.orderId
            const event: any = req.body.paymentEvent
            this.logger.info("DEBUG:: Juspay payment event", {body: req.body, orderId})
            try {
                this.omsApiClient.publishJuspayPaymentEvent(orderId, event.event, event.value)
            }
            catch (err) {
                this.logger.info(`Juspay payment event failed for orderId ${orderId}`, {body: req.body})
            }
            return true
        }

        getPaymentOffer(bestPaymentOffers: GetBestPaymentOffersResponse, paymentMode: PaymentMode) {
            if (bestPaymentOffers.paymentOffers) {
                const offer = bestPaymentOffers.paymentOffers[paymentMode]
                if (offer && offer.offer) {
                    const offerAddons = offer.offer.addons
                    const paymentOffer = _.find(offerAddons, offerAddon => {
                        return (offerAddon.addonType === "PAYMENT" || offerAddon.addonType === "NO_COST_EMI")
                    })
                    return paymentOffer
                }
            }
        }

        @httpPost("/amazonpay/signrequest")
        async amazonPaySignRequest(req: express.Request, res: express.Response): Promise<any> {
            let response
            this.logger.info(`Going with V2 PAYMENT_SERVICE Implementation- amazonPaySignRequest`)
            const signRequest: AmazonPaySignRequest = {
                isURLRequested: _.get(req, "body.isURLRequested"),
                orderTotalAmount: _.get(req, "body.orderTotalAmount"),
                orderTotalCurrencyCode: _.get(req, "body.orderTotalCurrencyCode"),
                sellerOrderId: _.get(req, "body.sellerOrderId"),
                thankYouPageUrl: _.get(req, "body.thankYouPageUrl"),
                transactionTimeout: _.get(req, "body.transactionTimeout")
            }
            response = await this.paymentClient.amazonPaySignRequest(signRequest)
            return response.signRequest
        }

        @httpPost("/amazonpay/verifyrequest")
        async amazonPayValidateRequest(req: express.Request, res: express.Response): Promise<any> {
            let response
            this.logger.info(`Going with V2 PAYMENT_SERVICE Implementation- amazonPayVerifyRequest`)
            const verifyRequest: AmazonPayVerifyRequest = {
                amazonOrderId: _.get(req, "body.amazonOrderId"),
                description: _.get(req, "body.description"),
                orderTotalAmount: _.get(req, "body.orderTotalAmount"),
                orderTotalCurrencyCode: _.get(req, "body.orderTotalCurrencyCode"),
                reasonCode: _.get(req, "body.reasonCode"),
                sellerOrderId: _.get(req, "body.sellerOrderId"),
                signature: _.get(req, "body.signature"),
                status: _.get(req, "body.status"),
                transactionDate: _.get(req, "body.transactionDate")
            }
            response = await this.paymentClient.amazonPayVerifyRequest(verifyRequest)
            return response.response
        }

        @httpPost("/webhook")
        async webhook(req: express.Request, res: express.Response): Promise<Boolean | Order> {
            await AppUtil.timeout(3000)
            const params: { webhookBody: any, webhookHeaders: any } = {
                webhookBody: req.body,
                webhookHeaders: req.headers
            }
            return this.omsApiClient.handleWebhook("RAZORPAY", params)
        }

        @httpPost("/razorpayeat/webhook")
        async razorpayEatwebhook(req: express.Request, res: express.Response): Promise<Boolean | Order> {
            await AppUtil.timeout(3000)
            const params: { webhookBody: any, webhookHeaders: any } = {
                webhookBody: req.body,
                webhookHeaders: req.headers
            }
            return this.omsApiClient.handleWebhook("RAZORPAY_EAT", params)
        }

        @httpPost("/checkoutDotCom/webhook")
        async checkoutDotComWebhook(req: express.Request, res: express.Response): Promise<Boolean | Order> {
            await AppUtil.timeout(3000)
            const params: { webhookBody: any, webhookHeaders: any } = {
                webhookBody: req.body,
                webhookHeaders: req.headers
            }
            this.logger.info(`got webhook from checkoutDotCom:: webhookBody:: ${JSON.stringify(req.body)}, webhookHeaders:: ${JSON.stringify(req.headers)}`) // todo:: remove after testing.
            return this.omsApiClient.handleWebhook("CHECKOUT_DOT_COM", params)
        }

        @httpPost("/paytm/webhook/payment")
        async paytmPaymentWebhook(req: express.Request, res: express.Response): Promise<Boolean | Order> {
            await AppUtil.timeout(3000)
            const params: { webhookBody: any, webhookHeaders: any } = {
                webhookBody: {
                    type: "payment",
                    request: req.body
                },
                webhookHeaders: req.headers
            }
            this.logger.info("got paytm payment webhook", params)
            return this.omsApiClient.handleWebhook("PAYTM_V2", params)
        }

        @httpPost("/paytm/webhook/refund")
        async paytmRefundWebhook(req: express.Request, res: express.Response): Promise<Boolean | Order> {
            await AppUtil.timeout(3000)
            const params: { webhookBody: any, webhookHeaders: any } = {
                webhookBody: {
                    type: "refund",
                    request: req.body
                },
                webhookHeaders: req.headers
            }
            this.logger.info("got paytm refund webhook", params)
            return this.omsApiClient.handleWebhook("PAYTM_V2", params)
        }


        @httpPost("/juspay/webhook")
        public async juspayWebhook(req: express.Request, res: express.Response): Promise<boolean | Order> {
            await AppUtil.timeout(3000)
            const params: { "webhookBody": any, "webhookHeaders": any } = {
                "webhookBody": req.body,
                "webhookHeaders": req.headers
            }
            this.logger.info("JUSPAY webhook", params)
            return this.omsApiClient.handleWebhook("JUSPAY", params)
        }

        @httpGet("/simpl/webhook")
        public async simplWebhook(req: express.Request, res: express.Response): Promise<boolean | Order> {
            await AppUtil.timeout(3000)
            const params: { "webhookBody": any, "webhookHeaders": any, "webhookParams": any } = {
                "webhookBody": req.body,
                "webhookHeaders": req.headers,
                "webhookParams": req.query
            }
            this.logger.info("SIMPL webhook", params)
            return this.omsApiClient.handleWebhook("SIMPL", params)
        }

        @httpPost("/amazonpay/webhook")
        public async amazonpayWebhook(req: express.Request, res: express.Response): Promise<boolean | Order> {
            const params: { "webhookBody": any, "webhookHeaders": { [key: string]: string; } } = {
                "webhookBody": req.body,
                "webhookHeaders": <{ [key: string]: string; }>req.headers
            }
            return this.omsApiClient.handleWebhook("AMAZONPAY", params)
        }

        @httpPost("/phonepe/webhook")
        public async phonepeWebhook(req: express.Request, res: express.Response): Promise<boolean | Order> {
            const params: { "webhookBody": any, "webhookHeaders": { [key: string]: string; } } = {
                "webhookBody": req.body,
                "webhookHeaders": <{ [key: string]: string; }>req.headers
            }
            return this.omsApiClient.handleWebhook("PHONEPE", params)
        }

        @httpPost("/razorpay/prepayment/webhook")
        public async handlePrePaymentWebhook(req: express.Request, res: express.Response): Promise<void> {
            const params: { "webhookBody": any, "webhookHeaders": { [key: string]: string; } } = {
                "webhookBody": req.body,
                "webhookHeaders": <{ [key: string]: string; }>req.headers
            }
            await this.paymentClient.handlePrePaymentWebhook(params)
        }

        @httpPost("/razorpay/paymentLink/webhook")
        public async handleRazorpayPaymentLinkWebhook(req: express.Request, res: express.Response): Promise<void> {
            this.logger.info("Razorpay Payment Link webhook req.body: ", JSON.stringify(req.body))
            const params: { "webhookBody": any, "webhookHeaders": { [key: string]: string; } } = {
                "webhookBody": req.body,
                "webhookHeaders": <{ [key: string]: string; }>req.headers
            }
            await this.paymentClient.handlePaymentLinkWebhook(params)
        }

        @httpPost("/razorpay/recurring/webhook")
        public async handleRazorpayRecurringPaymentLinkWebhook(req: express.Request, res: express.Response): Promise<boolean> {
            this.logger.info("Razorpay Recurring payment webhook req.body: ", JSON.stringify(req.body))
            const webhook: { webhookBody: any, webhookHeaders: {[key: string]: string}} = {
                webhookBody: req.body,
                webhookHeaders: <{[key: string]: string}> req.headers
            }
            return this.paymentClient.handleRecurringPaymentWebhook(webhook, "RAZORPAY")
        }


        @httpPost("/ccavenue/risk/webhook")
        public async ccAvenueRiskWebhook(req: express.Request, res: express.Response): Promise<{ success: boolean }> {
            this.logger.info("ccavenue risk status webhook req.body: ", JSON.stringify(req.body))
            const params: { "webhookBody": any, "webhookHeaders": { [key: string]: string; } } = {
                "webhookBody": req.body,
                "webhookHeaders": <{ [key: string]: string; }>req.headers
            }
            this.logger.info("ccavenue risk status webhook, params: ", JSON.stringify(params))
            return this.paymentClient.handleCCAvenueRiskStatusWebhook(params)
        }

        @httpPost("/apple/updateReceipt", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        public async updateAppleInAppReceipt(req: express.Request, res: express.Response): Promise<AppleInAppUpdateReceiptViewResponse> {
            const updateReceiptRequest: AppleIAPUpdateReceiptRequest = req.body
            if (_.isNil(updateReceiptRequest)) {
                this.logger.error(`Invalid Apple In-App Purchase receipt update request.`)
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid Apple In-App Purchase receipt update request.").build()
            }
            // App not sending correct userId always
            updateReceiptRequest.userId = req.session.userId
            this.logger.info(`/apple/updateReceipt:: userId: ${updateReceiptRequest.userId}, transactionId: ${updateReceiptRequest.transactionId}, originalTransactionId: ${updateReceiptRequest.originalTransactionId}`)

            updateReceiptRequest.tenant = AppUtil.getTenantFromUserContext(req.userContext)
            const updateReceiptResponse: AppleIAPUpdateReceiptResponse = await this.paymentClient.updateAppleInAppPurchaseReceipt(updateReceiptRequest)
            this.logger.info(`updateReceiptResponse: ${JSON.stringify(updateReceiptResponse)}`)

            const order: BaseOrder = await this.omsApiClient.getOrder(updateReceiptResponse.orderId)
            await this.checkOrderBelongsToSameUser(updateReceiptResponse.orderId, req.session.userId, order)
            const apiKey: string = req.headers["api-key"] as string || req.headers["apikey"] as string
            const userContext = req.userContext as UserContext
            const params: ConfirmationRequestParams = {
                orderSource: AppUtil.callSource(apiKey),
                userContext: userContext,
                freeTrialExpiryDateMs: updateReceiptResponse.freeTrialExpiryDateMs
            }

            const productSnapshot: OrderProductSnapshots = order.productSnapshots[0]
            const meta: AnalyticsData = {
                cleverTap: {
                    af_price: productSnapshot.price.listingPrice,
                    af_content_type: [productSnapshot.productType],
                    af_content_id: [productSnapshot.productId],
                    af_content: [productSnapshot.title],
                    af_quantity: productSnapshot.quantity
                }
            }

            let orderConfirmationView: ConfirmationView
            if (updateReceiptRequest.sendOrderConfirmationView) {
                try {
                    orderConfirmationView = await this.orderConfirmationViewBuilderV1.buildOrderConfirmationView(userContext, order, params)
                } catch (err) {
                    this.logger.error(`/apple/updateReceipt:: Error while generating order confirmation view.`, { err })
                }
            }

            const response: AppleInAppUpdateReceiptViewResponse = {
                orderConfirmationView: orderConfirmationView,
                orderInfo: orderConfirmationView?.orderInfo,
                meta: meta
            }
            this.logger.info(`/apple/updateReceipt response for ${updateReceiptResponse.orderId}.`, { response })
            return response
        }

        @httpPost("/playStore/verifyPurchase", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        public async verifyPlayStorePurchase(req: express.Request, res: express.Response): Promise<PlayStoreVerifyPurchaseViewResponse> {
            const verifyPurchaseRequest: PSBVerifyPurchaseRequest = req.body
            if (_.isNil(verifyPurchaseRequest)) {
                this.logger.error(`Invalid playStore verifyPurchaseRequest.`)
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid playStore verifyPurchaseRequest.").build()
            }
            this.logger.info(`/playStore/verifyPurchase:: `, { verifyPurchaseRequest })

            const verifyPurchaseResponse: PSBVerifyPurchaseResponse = await this.paymentClient.verifyPlayStoreBillingPurchase(verifyPurchaseRequest)
            this.logger.info(`verifyPurchaseResponse: `, { verifyPurchaseResponse })

            const order: BaseOrder = await this.omsApiClient.getOrder(verifyPurchaseResponse.orderId)
            await this.checkOrderBelongsToSameUser(verifyPurchaseResponse.orderId, req.session.userId, order)
            const apiKey: string = req.headers["api-key"] as string || req.headers["apikey"] as string
            const userContext = req.userContext as UserContext
            const params: ConfirmationRequestParams = {
                orderSource: AppUtil.callSource(apiKey),
                userContext: userContext,
                freeTrialExpiryDateMs: verifyPurchaseResponse.freeTrialExpiryDateMs
            }

            const productSnapshot: OrderProductSnapshots = order.productSnapshots[0]
            const meta: AnalyticsData = {
                cleverTap: {
                    af_price: productSnapshot.price.listingPrice,
                    af_content_type: [productSnapshot.productType],
                    af_content_id: [productSnapshot.productId],
                    af_content: [productSnapshot.title],
                    af_quantity: productSnapshot.quantity
                }
            }

            let orderConfirmationView: ConfirmationView
            if (verifyPurchaseRequest.sendOrderConfirmationView) {
                try {
                    orderConfirmationView = await this.orderConfirmationViewBuilderV1.buildOrderConfirmationView(userContext, order, params)
                } catch (err) {
                    this.logger.error(`/playStore/verifyPurchase:: Error while generating order confirmation view.`, { err })
                }
            }

            const response: PlayStoreVerifyPurchaseViewResponse = {
                orderConfirmationView: orderConfirmationView,
                orderInfo: orderConfirmationView?.orderInfo,
                meta: meta
            }
            this.logger.info(`/playStore/verifyPurchase response for ${verifyPurchaseResponse.orderId}.`, { response })
            return response
        }

        @httpPost("/apple/refreshReceipt", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        public async refreshAppleInAppReceipt(req: express.Request, res: express.Response): Promise<AppleInAppRefreshReceiptViewResponse> {
            const userContext = req.userContext as UserContext
            let userId: string = req.body.userId
            if (_.isNil(userId)) {
                userId = userContext?.userProfile?.userId
            }
            if (_.isNil(userId)) {
                this.logger.error(`Invalid Apple In-App Purchase receipt refresh request. userId missing.`)
                throw this.errorFactory.withCode(ErrorCodes.APPLE_IN_APP_REFRESH_RECEIPT_INVALID_REQUEST, 400).withDebugMessage("Invalid refresh receipt request").withContext({ userId }).build()
            }
            const receiptData: string = req.body.receiptData
            const refreshReceiptResponse: AppleIAPRefreshReceiptResponse = await this.paymentClient.refreshReceipt({
                userId: req.session.userId,
                receiptData: receiptData,
                tenant: AppUtil.getTenantFromUserContext(req.userContext)
            })
            if (_.isNil(refreshReceiptResponse)) {
                throw this.errorFactory.withCode(ErrorCodes.APPLE_IN_APP_REFRESH_RECEIPT_NO_ACTIVE_PURCHASE, 400).withDebugMessage("Invalid refresh receipt request").withContext({ userId }).build()
            }
            const order: BaseOrder = await this.omsApiClient.getOrder(refreshReceiptResponse.orderId)
            await this.checkOrderBelongsToSameUser(refreshReceiptResponse.orderId, userId, order)
            const apiKey: string = req.headers["api-key"] as string || req.headers["apikey"] as string
            const params: ConfirmationRequestParams = {
                orderSource: AppUtil.callSource(apiKey),
                userContext: userContext
            }
            const orderConfirmationView: ConfirmationView = await this.orderConfirmationViewBuilderV1.buildOrderConfirmationView(userContext, order, params)
            return {
                orderConfirmationView: orderConfirmationView
            }
        }

        @httpPost("/apple/inapp/webhook")
        public async appleInAppWebhook(req: express.Request, res: express.Response): Promise<WebhookResponse> {
            this.logger.info(`Apple In-App Purchase webhook. req.body: `, { webhookBody: req.body })
            const params: { "webhookBody": any, "webhookHeaders": { [key: string]: string; } } = {
                "webhookBody": req.body,
                "webhookHeaders": <{ [key: string]: string; }>req.headers
            }
            this.logger.info(`Apple In-App Purchase webhook. params: `, { params: params })
            return this.paymentClient.handleWebhook("APPLE_IN_APP", params)
        }

        @httpPost("/playStore/inapp/webhook")
        public async playStoreInAppWebhook(req: express.Request, res: express.Response): Promise<WebhookResponse> {
            this.logger.info(`PlayStore In-App Purchase webhook. req.body: `, { webhookBody: req.body })
            const params: { "webhookBody": any, "webhookHeaders": { [key: string]: string; } } = {
                "webhookBody": req.body,
                "webhookHeaders": <{ [key: string]: string; }>req.headers
            }
            this.logger.info(`PlayStore In-App Purchase webhook. params: `, { params: params })
            return this.paymentClient.handleWebhook("PLAY_STORE_BILLING", params)
        }

        @httpPost("/paytm/checksumGenerator")
        public paytmGenerateChecksum(req: express.Request, res: express.Response) {
            const paramarray: any = {}
            paramarray["MID"] = ""
            paramarray["ORDER_ID"] = ""
            paramarray["CUST_ID"] = ""
            paramarray["INDUSTRY_TYPE_ID"] = ""
            paramarray["CHANNEL_ID"] = ""
            paramarray["TXN_AMOUNT"] = ""
            paramarray["WEBSITE"] = ""

            for (const name in req.body) {
                paramarray[name] = req.body[name]
                if (req.body[name].includes("REFUND")) {
                    throw new Error("SECURITY ERROR")
                }
            }

            const paytmChecksum = new PaytmChecksum()
            paytmChecksum.genchecksum(paramarray, (err, params) => {
                if (err) {
                    res.writeHead(200, {"Content-type": "text/json"})
                    res.end()
                } else {
                    res.writeHead(200, {"Content-type": "text/json", "Cache-Control": "no-cache"})
                    res.write(JSON.stringify(paramarray))
                    res.end()
                }
            })
        }

        @httpPost("/paytm/checksumValidator")
        public paytmValidateChecksum(req: express.Request, res: express.Response) {

            res.writeHead(200, {"Content-type": "text/html", "Cache-Control": "no-cache"})

            const paytmChecksum = new PaytmChecksum()

            if (paytmChecksum.verifychecksum(req.body)) {
                req.body.IS_CHECKSUM_VALID = "Y"
            } else {
                req.body.IS_CHECKSUM_VALID = "N"
            }
            if (req.body.CHECKSUMHASH) {
                delete req.body.CHECKSUMHASH
            }

            res.write("<html>")
            res.write("<head>")
            res.write("<meta http-equiv=\"Content-Type\" content=\"text/html;charset=ISO-8859-I\">")
            res.write("<title>Paytm</title>")
            res.write("<script type=\"text/javascript\">")
            res.write("function response(){ return document.getElementById(\"response\").value; }")
            res.write("</script>")
            res.write("</head>")
            res.write("<body>")
            res.write("Redirect back to the app<br>")
            res.write("<form name=\"frm\" method=\"post\">")
            res.write("<input type=\"hidden\" id=\"response\" name=\"responseField\" value='" + htmlEscape(JSON.stringify(req.body)) + "'>")
            res.write("</form>")
            res.write("</body>")
            res.write("</html>")
            res.end()
        }

        @httpPost("/paytm/response")
        public paytmWebhook(req: express.Request, res: express.Response) {
            this.logger.info(req.body)
            res.writeHead(200, {"Content-type": "text/json"})
            res.end()
        }

        private isSodexoSupported(order: Order): boolean {
            if (!_.isNil(order)) {
                let isEatOrder: boolean = true
                order.productSnapshots.forEach((prodSnapshot) => {
                    if (prodSnapshot.productType !== "FOOD") {
                        isEatOrder = false
                    }
                })
                return isEatOrder

            }
            return false

        }

        // -------- PhonePe related --------- //
        @httpPost("/phonepe/prepareOrder", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        public async phonepePrepareOrder(req: express.Request, res: express.Response) {
            const session: Session = req.session
            const userId = session.userId
            const orderId = req.body.orderId
            await this.checkOrderBelongsToSameUser(orderId, userId)
            const userAuthToken = req.body.userAuthToken
            const phonePeMerchantUserId = _.isNil(userAuthToken) ? userId : undefined

            this.logger.info(`Going with PAYMENT_SERVICE Implementation- phonepePrepareOrder`)
            return this.paymentClient.phonepePrepareOrder(orderId, phonePeMerchantUserId, userAuthToken)
        }

        @httpPost("/phonepe/initiateServiceRequest", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        public async phonepeInitiateOrder(req: express.Request, res: express.Response) {
            const session: Session = req.session
            const userId = session.userId
            const orderId = req.body.orderId
            await this.checkOrderBelongsToSameUser(orderId, userId)
            const cityId = session.sessionData.cityId

            this.logger.info(`Going with PAYMENT_SERVICE Implementation- phonepeInitiateServiceRequest`)
            return this.paymentClient.phonepeInitiateServiceRequest(userId, orderId, cityId)
        }

        // -------- checkout.com related --------- //
        @httpPost("/checkoutDotCom/requestPayment", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        // todo:: define response interface.
        public async checkoutDotComRequestPayment(req: express.Request, res: express.Response): Promise<CheckoutDotComRequestCardPaymentResponseMin> {
            const session: Session = req.session
            const userId: string = session.userId
            const orderId: string = req.body.orderId
            const paymentDetails: ICheckoutDotComRequestPaymentSource = req.body.paymentDetails

            if (_.isNil(paymentDetails)) {
                this.logger.error("missing payment details:: ", paymentDetails)
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("missing payment details").withContext({userId, orderId, paymentDetails}).build()
            }

            if (_.isNil(orderId)) {
                this.logger.error("missing orderId:: ", orderId)
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("missing orderId").withContext({userId, orderId, paymentDetails}).build()
            }
            await this.checkOrderBelongsToSameUser(orderId, userId)
            const token = _.get(paymentDetails, "data.token", undefined)
            if (paymentDetails.type === "token" && _.isNil(token)) {
                this.logger.error("missing `token value` for type `token`:: ", JSON.stringify(paymentDetails.data))
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("card token not present for payment").withContext({userId, orderId, paymentDetails}).build()
            }

            const cardId = _.get(paymentDetails, "data.id", undefined)
            if (paymentDetails.type === "id" && _.isNil(cardId)) {
                this.logger.error("missing `id value` for type `id`:: ", JSON.stringify(paymentDetails.data))
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("card id not present for payment").withContext({userId, orderId, paymentDetails}).build()
            }
            try {
                return await this.omsApiClient.requestCheckoutDotComPayment(userId, orderId, paymentDetails)
            } catch (err) {
                this.logger.error("Error contacting payment Service!")
                this.logger.error(JSON.stringify(err))
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("Error contacting payment Service!").withContext({userId, orderId, paymentDetails}).build()
            }
        }

        @httpGet("checkoutDotCom/savedCards", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        public async checkoutDotComGetSavedCards(req: express.Request, res: express.Response): Promise<SavedCardList> {
            const session: Session = req.session
            const userId: string = session.userId
            return this.paymentClient.getSavedCards("CHECKOUT_DOT_COM", userId)
        }

        // -------- Paypal related --------- //
        @httpPost("/paypal/linkAccount", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        public async paypalLinkAccount(req: express.Request, res: express.Response): Promise<CreateGatewayOrderResponse> {
            const session: Session = req.session
            const userId: string = session.userId
            const orderId: string = req.body.orderId
            const return_url: string = req.body.redirectUrl
            const cancel_url: string = req.body.failureUrl

            if (_.isNil(userId) || _.isNil(return_url) || _.isNil(cancel_url)) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid params for Paypal link Account request").build()
            }
            await this.checkOrderBelongsToSameUser(orderId, userId)
            this.logger.info(`Going with PAYMENT_SERVICE Implementation- paypalLinkAccount`)
            return this.paymentClient.paypalLinkAccount(userId, orderId, return_url, cancel_url)
        }

        @httpPost("/paypal/unlinkAccount", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        public async paypalUnlinkAccount(req: express.Request, res: express.Response): Promise<{ success: boolean }> {
            const session: Session = req.session
            const userId: string = session.userId

            if (_.isNil(userId)) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid params for Paypal unlinkAccount request").build()
            }

            this.logger.info(`Going with PAYMENT_SERVICE Implementation- paypalUnlinkAccount`)
            await this.paymentClient.paypalUnlinkAccount(userId)
            return {"success": true}
        }

        @httpPost("/paypal/confirmAccountLink", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        public async paypalConfirmAccountLink(req: express.Request, res: express.Response): Promise<{ success: boolean }> {
            const session: Session = req.session
            const userId: string = session.userId

            if (_.isNil(userId)) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid params for Paypal confirmAccountLink request").build()
            }

            this.logger.info(`Going with PAYMENT_SERVICE Implementation- paypalConfirmAccountLink`)
            await this.paymentClient.paypalConfirmAccountLink(userId)
            return {"success": true}
        }

        @httpPost("/paypal/createWebExperienceProfile", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        public async paypalCreateWebExperienceProfile(req: express.Request, res: express.Response): Promise<IPaypalWebExperienceProfile> {
            const paypalConfig: PaypalConfig = await this.cacheHelper.getPaypalConfig()
            const paypalWebExperienceConfig: IPaypalWebExperienceProfile = _.get(paypalConfig, "configs.web_experience_profile", undefined)
            if (_.isNil(paypalWebExperienceConfig)) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("No web experience config found for Paypal").build()
            }
            this.logger.info(`Going with V2 PAYMENT_SERVICE Implementation- paypalCreateWebExperienceProfile`)
            return this.paymentClient.paypalCreateWebExperienceProfile(paypalWebExperienceConfig)
        }

        @httpPost("/cod/fetchContactId", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        public async codFetchContactId(req: express.Request, res: express.Response): Promise<{ contactId: string }> {
            const session: Session = req.session
            const userId: string = session.userId

            if (_.isNil(userId)) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid Userid").build()
            }

            const user = await this.cacheHelper.getUser(userId)

            const contact: RazorpayXContactRequest = {
                name: `${user.firstName} ${user.lastName}`.trim(),
                type: "customer",
                email: user.email,
                reference_id: userId
            }

            return this.paymentClient.codFetchContactId(userId, contact)
        }
    }

    return PaymentController
}

export default controllerFactory

