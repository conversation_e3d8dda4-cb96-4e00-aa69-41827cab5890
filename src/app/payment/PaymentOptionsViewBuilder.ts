import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import * as _ from "lodash"
import { Logger, BASE_TYPES } from "@curefit/base"
import { PaymentOptionView, PaymentModeConfig, PaymentOption, PaymentMode } from "@curefit/payment-common"
import { ALFRED_CLIENT_TYPES } from "@curefit/alfred-client"
import { BaseOrder } from "@curefit/order-common"
import { GetBestPaymentOffersResponse } from "@curefit/offer-common"
import { IAlfredServiceEther as IAlfredService } from "@curefit/alfred-client"
import { recommendedPaymentChannel, UserContext } from "@curefit/vm-models"
import { IGearService, GEARVAULT_CLIENT_TYPES } from "@curefit/gearvault-client"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { PAYMENT_TYPES, IPaymentClient } from "@curefit/payment-client"
import { PaymentOptionViewFactory } from "./PaymentOptionViewFactory"
import { BasePaymentOptionView, IPaymentOptionViewParams } from "./PaymentOptionViewFactory"
import { OfferHelper, OMS_API_CLIENT_TYPES } from "@curefit/oms-api-client"

const modesExcludedFromOffersSection: PaymentMode[] = ["BAJAJ_FINSERV", "PAY_LATER", "TATA_PAY", "COD_V2"]

@injectable()
export class PaymentOptionsViewBuilder {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(ALFRED_CLIENT_TYPES.AlfredService) private alfredService: IAlfredService,
        @inject(GEARVAULT_CLIENT_TYPES.GearService) private gearService: IGearService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) protected catalogueService: ICatalogueService,
        @inject(PAYMENT_TYPES.IPaymentClient) protected paymentClient: IPaymentClient,
        @inject(CUREFIT_API_TYPES.PaymentOptionViewFactory) protected paymentOptionViewFactory: PaymentOptionViewFactory,
        @inject(OMS_API_CLIENT_TYPES.OfferHelper) private offerHelper: OfferHelper,
    ) {}

    public async buildMultiSectionList(
        paymentModeConfigs: PaymentModeConfig[],
        recommendedMethods: recommendedPaymentChannel,
        order: BaseOrder,
        offers: GetBestPaymentOffersResponse,
        userContext: UserContext
    ): Promise<PaymentOptionView[]> {
        if (!_.isNil(recommendedMethods)) {
            paymentModeConfigs.sort((a: PaymentModeConfig, b: PaymentModeConfig) => {
                const recommendationRatingA: number = _.get(recommendedMethods, a.paymentMode, undefined)
                const recommendationRatingB: number = _.get(recommendedMethods, b.paymentMode, undefined)
                if (_.isNil(recommendationRatingA) && _.isNil(recommendationRatingB)) {
                    return a.priority <= b.priority ? -1 : 1
                } else if (_.isNil(recommendationRatingA)) {
                    return 1
                } else if (_.isNil(recommendationRatingB)) {
                    return -1
                } else {
                    return recommendationRatingA >= recommendationRatingB ? -1 : 1
                }
            })
        }

        const paymentOptions: PaymentOption[] = await this.getPaymentOptionViews(paymentModeConfigs, order, offers, userContext)
        return this.buildPaymentOptionSections(paymentOptions, recommendedMethods)
    }

    public async buildLegacyList(paymentModeConfigs: PaymentModeConfig[], recommendedMethods: recommendedPaymentChannel, order: BaseOrder, offers: GetBestPaymentOffersResponse, userContext: UserContext): Promise<PaymentOption[]> {
        paymentModeConfigs.sort((a: PaymentModeConfig, b: PaymentModeConfig) => {
            const recommendationRatingA: number = _.get(recommendedMethods, a.paymentMode, undefined)
            const recommendationRatingB: number = _.get(recommendedMethods, b.paymentMode, undefined)
            if (_.isNil(recommendationRatingA) && _.isNil(recommendationRatingB)) {
                return a.priority <= b.priority ? -1 : 1
            } else if (_.isNil(recommendationRatingA)) {
                return 1
            } else if (_.isNil(recommendationRatingB)) {
                return -1
            } else {
                return recommendationRatingA >= recommendationRatingB ? -1 : 1
            }
        })
        const optionViews: PaymentOption[] = await this.getPaymentOptionViews(paymentModeConfigs, order, offers, userContext)
        const offerOptions: PaymentOption[] = []
        const nonOfferOptions: PaymentOption[] = []
        for (const option of optionViews) {
            if (!_.isNil(option.offerText) && !modesExcludedFromOffersSection.includes(option.type)) {
                offerOptions.push(option)
            } else {
                nonOfferOptions.push(option)
            }
        }
        return [...offerOptions, ...nonOfferOptions]
    }

    private async getPaymentOptionViews(
        paymentModeConfigs: PaymentModeConfig[],
        order: BaseOrder,
        offers: GetBestPaymentOffersResponse,
        userContext: UserContext
    ): Promise<PaymentOption[]> {
        const paymentOptionViewPromises: Promise<PaymentOption[]>[] = []
        for (let idx = 0; idx < paymentModeConfigs.length; idx++) {
            const modeConfig: PaymentModeConfig = paymentModeConfigs[idx]
            const optionsViewForMode: BasePaymentOptionView = this.paymentOptionViewFactory.getPaymentOptionView(modeConfig.paymentMode)
            if (!_.isNil(optionsViewForMode)) {
                const paymentOptionViewParams: IPaymentOptionViewParams = {
                    modeConfig: modeConfig,
                    order: order,
                    offers: offers,
                    gearService: this.gearService,
                    userContext: userContext,
                    offerHelper: this.offerHelper
                }
                const isAvailable: boolean = await optionsViewForMode.isAvailable(paymentOptionViewParams)
                if (isAvailable) {
                    const optionsPromiseForMode: Promise<PaymentOption[]> = optionsViewForMode.getOptions(paymentOptionViewParams)
                    paymentOptionViewPromises.push(optionsPromiseForMode)
                }
            }
        }
        const modeWisePaymentOptions: PaymentOption[][] = await Promise.all(paymentOptionViewPromises)
        const paymentOptions: PaymentOption[] = []
        for (const optionsForMode of modeWisePaymentOptions) {
            if (!_.isEmpty(optionsForMode)) {
                for (const option of optionsForMode) {
                    paymentOptions.push(option)
                }
            }
        }
        return paymentOptions
    }

    private buildPaymentOptionSections(paymentOptions: PaymentOption[], recommendedMethods: recommendedPaymentChannel): PaymentOptionView[] {
        const multisectionOptionViews: PaymentOptionView[] = []
        // 3 sections
        const preferredSection: PaymentOptionView = {
            sectionHeaderText: "PREFERRED PAYMENT",
            sectionStyle: "PLAIN",
            options: []
        }
        const offersSection: PaymentOptionView = {
            sectionHeaderText: "OFFER",
            sectionStyle: "PLAIN",
            options: []
        }
        const othersSection: PaymentOptionView = {
            sectionHeaderText: "",
            sectionStyle: "PLAIN",
            options: []
        }
        const PREFFERED_SECTION_MAX_SIZE = 3

        // Populate all 3 sections
        for (const paymentOption of paymentOptions) {
            if (!_.isNil(paymentOption)) {
                if (!_.isNil(paymentOption.offerText) && !modesExcludedFromOffersSection.includes(paymentOption.type)) {
                    // Populate Payment Methods with Offers
                    offersSection.options.push(paymentOption)
                } else if (!_.isNil(_.get(recommendedMethods, paymentOption.type)) || !_.isNil(_.get(recommendedMethods, paymentOption.gatewayType))) {
                    // Populate Preferred Payment Methods - Top prefToConsider
                    if (preferredSection.options.length < PREFFERED_SECTION_MAX_SIZE) {
                        preferredSection.options.push(paymentOption)
                    } else {
                        // Add to "Others" when Preferred payment list overflows
                        othersSection.options.push(paymentOption)
                    }
                }
                else {
                    // Populate Other Payment Methods
                    othersSection.options.push(paymentOption)
                }
            }
        }

        // POPULAR
        if (!_.isEmpty(offersSection.options)) {
            if (offersSection.options.length > 1) {
                offersSection.sectionHeaderText = "OFFERS"
            }
            multisectionOptionViews.push(offersSection)
            othersSection.sectionHeaderText = "OTHERS"
        }

        // FREQUENTLY USED
        if (!_.isEmpty(preferredSection.options)) {
            if (preferredSection.options.length > 1) {
                preferredSection.sectionHeaderText = "PREFERRED PAYMENTS"
            }
            multisectionOptionViews.push(preferredSection)
            othersSection.sectionHeaderText = "OTHERS"
        }

        // OTHERS
        if (!_.isEmpty(othersSection.options)) {
            multisectionOptionViews.push(othersSection)
            this.hackCodToSecondPosition(othersSection) // HACK - Arjun/Shanawar - To forcefully move COD option as second choice
        }
        return multisectionOptionViews
    }

    private hackCodToSecondPosition(paymentOptionsView: PaymentOptionView): void {
        if (paymentOptionsView.options.length <= 2) return
        const codIndex: number = paymentOptionsView.options.findIndex((paymentOption: PaymentOption) => {
            return paymentOption.type === "COD"
        })
        if (codIndex === -1 || codIndex === 1) return
        const codOption: PaymentOption = paymentOptionsView.options.splice(codIndex, 1)[0]
        paymentOptionsView.options.splice(1, 0, codOption)
    }

}
