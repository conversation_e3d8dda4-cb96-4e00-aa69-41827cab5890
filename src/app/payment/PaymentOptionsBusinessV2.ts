import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import * as _ from "lodash"
import { Logger, BASE_TYPES } from "@curefit/base"
import {
    PaymentOptionExtendedView, PaymentSummary, PaymentOptionLegacyView, IGetOrderMetaParams,
    IGetPaymentSummaryParams, IPaymentOptionRequestParams
} from "./paymentModels"
import {
    PaymentOptionView, PaymentModeConfigResponse, PaymentOption,
    IGetPaymentModesConfigParams
} from "@curefit/payment-common"
import { IOfferServiceV2, OfferServiceV3 } from "@curefit/offer-service-client"
import { BaseOrder, Order } from "@curefit/order-common"
import { GetBestPaymentOffersResponse, OfferV2 } from "@curefit/offer-common"
import { OrderMeta } from "@curefit/apps-common"
import { CacheHelper } from "../util/CacheHelper"
import AppUtil from "../util/AppUtil"
import { WalletBalance } from "@curefit/fitcash-common"
import { FITCASH_CLIENT_TYPES, IFitcashService } from "@curefit/fitcash-client"
import { recommendedPaymentChannel } from "@curefit/vm-models"
import { ICerberusServiceV2 } from "@curefit/vm-models"
import { OFFER_SERVICE_CLIENT_TYPES } from "@curefit/offer-service-client"
import { FitClubSavings } from "@curefit/fitclub-models"
import { OrderUtil, RUPEE_SYMBOL } from "@curefit/base-utils"
import { ListingBrandIdType } from "@curefit/eat-common"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { PAYMENT_TYPES, IPaymentClient } from "@curefit/payment-client"
import { PaymentOptionsViewBuilder } from "./PaymentOptionsViewBuilder"
import { PaymentUtil } from "@curefit/payment-models"
import { Vertical } from "@curefit/base-common"
import MixpanelEventService from "../cultsport/MixpanelEventService"
import { GearService } from "@curefit/gearvault-client"

export interface IPaymentOptionsBusinessV2 {
    getPaymentOptionsForApp(params: IPaymentOptionRequestParams): Promise<PaymentOptionExtendedView>
    getPaymentOptionsForWeb(params: IPaymentOptionRequestParams): Promise<PaymentOptionLegacyView>
}

interface PaymentOptionsConfig {
    orderMeta: OrderMeta
    paymentModesConfig: PaymentModeConfigResponse,
    paymentSummary: PaymentSummary
    recommendedPaymentChannel: recommendedPaymentChannel
    bestPaymentOffers: GetBestPaymentOffersResponse
}

@injectable()
export class PaymentOptionsBusinessV2 implements IPaymentOptionsBusinessV2 {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) private offerServiceV2: IOfferServiceV2,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) private offerServiceV3: OfferServiceV3,
        @inject(FITCASH_CLIENT_TYPES.FitcashService) private fitcashService: IFitcashService,
        @inject(CUREFIT_API_TYPES.CerberusServiceV2) private cerberusServiceV2: ICerberusServiceV2,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected cacheHelper: CacheHelper,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) protected catalogueService: ICatalogueService,
        @inject(PAYMENT_TYPES.IPaymentClient) protected paymentClient: IPaymentClient,
        @inject(CUREFIT_API_TYPES.PaymentOptionsViewBuilder) protected paymentOptionsViewBuilder: PaymentOptionsViewBuilder,
        @inject(CUREFIT_API_TYPES.MixpanelEventService) protected mixpanelEventService: MixpanelEventService
    ) {
    }

    public async getPaymentOptionsForApp(params: IPaymentOptionRequestParams): Promise<PaymentOptionExtendedView> {
        const paymentOptionsConfig: PaymentOptionsConfig = await this.getBasicPaymentOptionsConfig(
            params
        )

        const multiSectionPaymentOptionView: PaymentOptionView[] = await this.paymentOptionsViewBuilder.buildMultiSectionList(
            paymentOptionsConfig.paymentModesConfig.paymentModeConfigs,
            paymentOptionsConfig.recommendedPaymentChannel,
            params.order,
            paymentOptionsConfig.bestPaymentOffers,
            params.userContext
        )

        const result: PaymentOptionExtendedView = {
            paymentOptions: !_.isNil(multiSectionPaymentOptionView) ? multiSectionPaymentOptionView : [],
            orderMeta: paymentOptionsConfig.orderMeta,
            paymentSummary: paymentOptionsConfig.paymentSummary
        }
        return result
    }

    public async getPaymentOptionsForWeb(params: IPaymentOptionRequestParams): Promise<PaymentOptionLegacyView> {

        const paymentOptionsConfig: PaymentOptionsConfig = await this.getBasicPaymentOptionsConfig(params)

        const paymentOptionsView: PaymentOption[] = await this.paymentOptionsViewBuilder.buildLegacyList(
            paymentOptionsConfig.paymentModesConfig.paymentModeConfigs,
            paymentOptionsConfig.recommendedPaymentChannel,
            params.order,
            paymentOptionsConfig.bestPaymentOffers,
            params.userContext
        )

        const result: PaymentOptionLegacyView = {
            paymentOptions: !_.isNil(paymentOptionsView) ? paymentOptionsView : [],
            orderMeta: paymentOptionsConfig.orderMeta,
            paymentSummary: paymentOptionsConfig.paymentSummary
        }
        if (AppUtil.isCultSportWebApp(params.userContext)) {
            this.mixpanelEventService.sendCultsportPaymentOptionsEvents(params.userContext, params.order, result)
        }
        return result
    }

    private async getBasicPaymentOptionsConfig(
        params: IPaymentOptionRequestParams,
    ): Promise<PaymentOptionsConfig> {
        const offers: GetBestPaymentOffersResponse = await this.offerServiceV3.getBestPaymentOffers({
            offerIds: params.order.offersApplied
        })

        // Get order meta info (vertical, displayLabels etc.)
        const orderMetaParams: IGetOrderMetaParams = {
            userContext: params.userContext,
            order: params.order
        }
        const orderMetaPromise: Promise<OrderMeta> = this.getOrderMeta(orderMetaParams)

        // Get PG payment options view
        const recommendedPaymentChannel: recommendedPaymentChannel = await this.getRecommendedPaymentMethods(params.order)
        const baseOrderId: string = params.order.orderId.split("-")[0]
        const vertical: Vertical | "UNKNOWN" = OrderUtil.getVerticalName(params.order.productSnapshots[0])
        const paymentModesConfigParams: IGetPaymentModesConfigParams = {
            orderId: baseOrderId,
            userId: _.get(params, "userContext.userProfile.userId", undefined),
            appVersion: PaymentUtil.getUserAppVersion(params.userContext),
            deviceType: PaymentUtil.getDeviceType(params.userContext),
            countryCode: PaymentUtil.getCountryCode(params.order),
            isSubscriptionAutoRenew: false,
            vertical: vertical,
            orderSource: params.order.source
        }
        const paymentModesConfigPromise: Promise<PaymentModeConfigResponse> = this.paymentClient.getPaymentModesConfig(paymentModesConfigParams)

        const [orderMeta, paymentModesConfigResponse] = await Promise.all(
            [orderMetaPromise, paymentModesConfigPromise]
        )

        // Get payment summary
        const getPaymentSummaryParams: IGetPaymentSummaryParams = {
            order: params.order,
            userContext: params.userContext,
            isFitcashEnabled: paymentModesConfigResponse.isFitcashEnabled
        }
        const paymentSummary: PaymentSummary = await this.getPaymentSummary(getPaymentSummaryParams)

        try {
            const product = params.order?.productSnapshots?.[0]
            const isGymPT = product && ( product.productType === "GYM_PT_PRODUCT" || product.productType === "GYM_PT_PPC_PRODUCT" )
            if (isGymPT) {
                // disable fitcash display for pt products.
                paymentSummary.displayFitCash = false
            }
        } catch (err) {
            this.logger.error(`Failed to override fitcash flag orderId: ${params.order.orderId}`, { err })
            // suppress the errors in case anything breaks
        }
        return {
            orderMeta: orderMeta,
            paymentModesConfig: paymentModesConfigResponse,
            paymentSummary: paymentSummary,
            recommendedPaymentChannel: recommendedPaymentChannel,
            bestPaymentOffers: offers
        }
    }


    private async getRecommendedPaymentMethods(order: BaseOrder): Promise<recommendedPaymentChannel> {
        try {
            const product = order.productSnapshots[0]
            const vertical: string = OrderUtil.getVerticalName(product)
            const recommendedMethods: recommendedPaymentChannel = await this.cerberusServiceV2.getRecommendedPaymentChannel(order.userId, vertical, product.isPack === true)
            return recommendedMethods
        } catch (err) {
            this.logger.error(`Failed to getRecommendedPaymentMethods orderId: ${order.orderId}`, { err })
        }
    }

    private async getOrderMeta(orderMetaParams: IGetOrderMetaParams): Promise<OrderMeta> {
        const userId: string = _.get(orderMetaParams, "userContext.userProfile.userId", undefined)
        const order: BaseOrder = orderMetaParams.order
        if (order.productSnapshots[0].isPack && order.products[0].productType === "FOOD") {
            const user = await this.cacheHelper.getUser(userId)
            let numProducts = 0
            order.products.forEach(orderProduct => {
                numProducts = numProducts + orderProduct.quantity
            })

            const title = (order.productSnapshots[0].isPack) ? order.productSnapshots[0].title
                + (order.productSnapshots[0].option.subscriptionType !== undefined ? " " + _.capitalize(order.productSnapshots[0].option.subscriptionType) + " plan" : "")
                : (numProducts > 1 ? numProducts + " items ordered" : order.productSnapshots[0].title)
            const orderMeta: OrderMeta = {
                orderId: order.orderId,
                customerName: user.firstName + " " + user.lastName,
                customerEmail: user.email,
                customerPhone: user.phone,
                price: {
                    listingPrice: order.totalPayable,
                    mrp: order.totalPayable,
                    currency: order.productSnapshots[0].price.currency
                },
                productIds: order?.productSnapshots?.map(product => product.productId),
                orderType: "EAT_PACK",
                vertical: "EAT_FIT",
                title: title,
                displayText: title
            }
            return orderMeta
        } else {
            return undefined
        }
    }

    private async getPaymentSummary(getPaymentSummaryParams: IGetPaymentSummaryParams): Promise<PaymentSummary> {
        const order: BaseOrder = getPaymentSummaryParams.order
        const paymentSummary: PaymentSummary = {
            fitCashBalance: 0,
            isFitCashSelected: false,
            fitCashUsed: 0,
            displayFitCash: false,
            totalAmountPayable: order.totalAmountPayable,
            taxDescription: "Incl. of all taxes",
            fitCashOfferText: null,
            currency: OrderUtil.getCurrency(order)
        }
        this.logger.info(`getPaymentSummaryParams.isFitcashEnabled: ${getPaymentSummaryParams.isFitcashEnabled}`)
        if (!getPaymentSummaryParams.isFitcashEnabled || paymentSummary.currency !== "INR") {
            return paymentSummary
        }
        const currencyCode = _.get(getPaymentSummaryParams, "userContext.userProfile.city.country.currencyCode")
        const fitCashBalance: WalletBalance = await this.fitcashService.balance(order.userId, currencyCode)
        this.logger.info(`fitCashBalance: ${JSON.stringify(fitCashBalance)}`)
        paymentSummary.displayFitCash = true
        let isFitCashSelected: boolean = false
        if (order.totalFitCashPayable && order.totalFitCashPayable > 0) {
            isFitCashSelected = true
        }
        paymentSummary.fitCashUsed = order.totalFitCashPayable ? order.totalFitCashPayable : 0
        paymentSummary.fitCashBalance = fitCashBalance.balance ? (fitCashBalance.balance / 100) : 0
        paymentSummary.isFitCashSelected = isFitCashSelected
        return paymentSummary
    }

    public static getListingBrandFromOrder(baseOrder: BaseOrder): ListingBrandIdType {
        try {
            const order: Order = <Order>baseOrder
            if (order && order.eatOptions) {
                return order.eatOptions.listingBrand
            }
        } catch (e) {
            // do nothing
        }
    }

}
