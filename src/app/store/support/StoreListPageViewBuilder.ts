import { inject, injectable } from "inversify"
import { UserContext } from "@curefit/userinfo-common"
import { Order } from "@curefit/order-common"
import { SupportListPageView } from "../../crm/SupportListPageView"
import { PageWidget } from "../../page/Page"
import { Action } from "@curefit/apps-common"
import * as _ from "lodash"
import { SupportActionableCardWidget, SupportEmptyListingWidget } from "../../page/PageWidgets"
import AppUtil from "../../util/AppUtil"
import * as momentTz from "moment-timezone"
import { ActionUtil as AppActionUtil } from "../../util/ActionUtil"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import IssueBusiness from "../../crm/IssueBusiness"
import { GearService, GEARVAULT_CLIENT_TYPES } from "@curefit/gearvault-client"
import { GearOrderDetailObject, LineItem } from "@curefit/gear-common"
import { ALFRED_CLIENT_TYPES, FoodBooking, IShipmentService } from "@curefit/alfred-client"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { UrlPathBuilder } from "@curefit/product-common"
import { EatBookingStatus } from "../../order/MealListPageViewBuilder"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { ISegmentService } from "@curefit/vm-models"
import { BASE_TYPES, ILogger } from "@curefit/base"

@injectable()
export class StoreListPageViewBuilder {
    gearObjectsMap: {
        [orderId: string]: GearOrderDetailObject
    }
    wholefitFoodBookingMap: {
        [orderId: string]: FoodBooking
    }
    constructor(
        @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness,
        @inject(GEARVAULT_CLIENT_TYPES.GearService) private gearService: GearService,
        @inject(ALFRED_CLIENT_TYPES.ShipmentService) private shipmentService: IShipmentService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService,
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
    ) {
        this.gearObjectsMap = {}
        this.wholefitFoodBookingMap = {}
    }

    async buildStoreListPageView(userContext: UserContext, order: Order[], pageNumber: number): Promise<SupportListPageView> {
        const widgets: PageWidget[] = []
        let actions: Action[]
        if (_.isEmpty(order) && !(await AppUtil.isNewSupportInFlutterSupported(this.segmentService, userContext)) ) {
            if (pageNumber === 1) {
                widgets.push(new SupportEmptyListingWidget("STORE", "No Store Orders Yet!", "Get Going Today Only!"))
                actions = [{
                    actionType: "NAVIGATION",
                    url: "curefit://storeclp?pageId=storeclp&selectedTab=CultGear",
                    title: "Order from Store Today!"
                }]
            }
        } else {
            const cardWidgets: SupportActionableCardWidget[] = await this.getStoreListWidgets(userContext, order)
            cardWidgets.map(cardWidget => {
                widgets.push(cardWidget)
            })
        }
        return new SupportListPageView(widgets, actions)
    }

    async buildStoreListWidgets(userContext: UserContext, order: Order[]): Promise<SupportActionableCardWidget[]> {
        if (!_.isEmpty(order)) {
            return this.getStoreListWidgets(userContext, order)
        }
        return []
    }

    private async getStoreListWidgets(userContext: UserContext, order: Order[]): Promise<SupportActionableCardWidget[]> {
        const gearOrderIds = []
        const wholefitOrderIds = []
        for (let i = 0; i < order.length; i = i + 1) {
            if (this.isGearOrder(order[i])) {
                gearOrderIds.push(order[i].orderId)
            } else if (this.isWholefitOrder(order[i])) {
                wholefitOrderIds.push(order[i].orderId)
            }
        }

        const gearOrdersPromise: any = this.gearService.getOrders(gearOrderIds)

        const [gearOrders] = await Promise.all([gearOrdersPromise])
        const wholefitFoodBookings: FoodBooking[] = []

        this.gearObjectsMap = _.keyBy(gearOrders, (o: GearOrderDetailObject) => o.external_service_order_id )
        this.wholefitFoodBookingMap = _.keyBy(wholefitFoodBookings, (booking: FoodBooking) => booking.orderId)

        const cardWidgetPromises: Promise<SupportActionableCardWidget>[] = _.map(order, o => {
            return this.getActionableCardWidget(userContext, o)
        })
        const cardWidgets: SupportActionableCardWidget[] = _.compact(await Promise.all(cardWidgetPromises))
        return cardWidgets
    }

    private async getActionableCardWidget(userContext: UserContext, order: Order): Promise<SupportActionableCardWidget> {
        if (this.isWholefitOrder(order)) {
            const wholefitOrderFoodBooking = this.wholefitFoodBookingMap[order.orderId]
            if (wholefitOrderFoodBooking) {
                return this.getWholefitActionCard(userContext, wholefitOrderFoodBooking, wholefitOrderFoodBooking.timezone)
            }
        } else if (this.isGearOrder(order)) {
            const gearOrderObject = this.gearObjectsMap[order.orderId]
            if (gearOrderObject) {
                return this.getGearActionCard(userContext, gearOrderObject)
            }
        }

        return null
    }

    private async getGearActionCard(userContext: UserContext, gearOrder: GearOrderDetailObject): Promise<SupportActionableCardWidget> {
        const reportIssueParams = this.issueBusiness.getGearIssueParams(gearOrder)
        const lineItem = gearOrder.line_items[0]
        const imageUrl = lineItem ? lineItem.variant.images[0].mini_url : undefined

        return {
            widgetType: "SUPPORT_ACTIONABLE_CARD_WIDGET",
            title: this.productTitle(gearOrder.line_items),
            subTitle: `#${gearOrder.external_service_order_id}`,
            footer: AppUtil.isWeb(userContext) ? undefined : [{
                text: `${momentTz.tz(gearOrder.created_at, "Asia/Kolkata").format("ddd, D MMM")}`,
            }],
            cardAction: {
                actionType: "NAVIGATION",
                url: AppUtil.isWeb(userContext) ? AppActionUtil.getIssuesUrl() : `curefit://gearorder?orderId=${gearOrder.external_service_order_id}`
            },
            imageUrl: imageUrl,
            time: AppUtil.isWeb(userContext) ? `${momentTz.tz(gearOrder.created_at, "Asia/Kolkata").format("ddd, D MMM")}` : undefined,
            timestamp: momentTz(gearOrder.created_at, "YYYY-MM-DDTHH:mm:ss.SSSZ").valueOf(),
            timezone: "Asia/Kolkata"
        }
    }

    private async getWholefitActionCard(userContext: UserContext, foodBooking: FoodBooking, timezone: Timezone): Promise<SupportActionableCardWidget> {
        const reportIssueParams = await this.issueBusiness.getMealBookingIssueParams(foodBooking, timezone)
        const foodProduct = await this.catalogueService.getProduct(foodBooking.productId)
        return {
            widgetType: "SUPPORT_ACTIONABLE_CARD_WIDGET",
            title: foodProduct.title + (foodBooking.products.length > 1 ? ` + ${foodBooking.products.length - 1} items` : ``),
            subTitle: `#${foodBooking.orderId}`,
            footer: AppUtil.isWeb(userContext) ? undefined : [{
                text: `${TimeUtil.formatDateStringInTimeZone(foodBooking.deliveryDate, timezone, "ddd, D MMM")}`,
            }],
            cardAction: {
                meta: (await AppUtil.isNewSupportInFlutterSupported(this.segmentService, userContext)) ? {
                    title: foodProduct.title + (foodBooking.products.length > 1 ? ` + ${foodBooking.products.length - 1} items` : ``),
                    subTitle: "What is your primary concern with the meal"
                } : null,
                actionType: "NAVIGATION",
                url: AppActionUtil.getIssuesUrl()
            },
            vegIcon: foodProduct.attributes["isVeg"] === "TRUE" ? "VEG" : "NON_VEG", // foodProduct.isVeg ? "VEG" : "NON_VEG",
            imageUrl: UrlPathBuilder.getSingleImagePath(foodProduct.parentProductId ? foodProduct.parentProductId : foodProduct.productId, "FOOD", "THUMBNAIL", foodProduct.imageVersion),
            status: EatBookingStatus[foodBooking.state],
            time: AppUtil.isWeb(userContext) ? `${TimeUtil.formatDateStringInTimeZone(foodBooking.deliveryDate, timezone, "ddd, D MMM")}` : undefined,
            timestamp: foodBooking.eta.getTime(),
            timezone: timezone
        }
    }

    private isWholefitOrder(order: Order) {
        return _.get(order, "eatOptions.listingBrand") === "WHOLE_FIT"
    }

    private isGearOrder(order: Order) {
        return !_.isNil(order.productSnapshots[0].gearBrandName)
    }

    private productTitle(lineItems: LineItem[]) {
        if (lineItems.length > 1) {
            return lineItems[0].variant.name + " + " + (lineItems.length - 1) + " items ordered"
        }
        return lineItems[0].variant.name
    }
}
