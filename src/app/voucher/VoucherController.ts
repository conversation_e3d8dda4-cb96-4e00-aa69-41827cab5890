import { Container, inject } from "inversify"
import { controller, httpPost, httpGet } from "inversify-express-utils"
import { BASE_TYPES, DataError, Logger } from "@curefit/base"
import { CONSTELLO_CLIENT_TYPES, IConstelloService } from "@curefit/constello-client"
import * as express from "express"
import { UnauthorizedError } from "@curefit/base/dist/src/Errors"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { IRedisAuthTokenService } from "../auth/RedisAuthTokenService"
import moment = require("moment")
import * as crypto from "crypto"


export function VoucherControllerFactory(kernel: Container) {
    @controller("/voucher")
    class VoucherController {
        constructor(
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(CONSTELLO_CLIENT_TYPES.ConstelloService) private constelloService: IConstelloService,
            @inject(CUREFIT_API_TYPES.RedisAuthTokenService) private redisAuthTokenService: IRedisAuthTokenService
        ) {
        }


        @httpGet("/attribute/:clientId/authToken")
        async fetchAuthToken(request: express.Request) {
            const {clientId} = request.params
            if (!clientId) {
                throw new DataError("Kindly provide the client id to fetch auth token.")
            }
            await this.validateAuthToken(request, "GENERATE_ACCESS_TOKEN", clientId)
            this.logger.info("Auth token is valid")
            const tokenExpiration = moment().add(5, "days").unix()
            const timeToExpire = tokenExpiration - moment().unix()
            const newAuthToken = await this.redisAuthTokenService.generateToken(clientId, "VOUCHER_ATTRIBUTE", timeToExpire)
            const newRefreshToken = await this.redisAuthTokenService.generateToken(clientId, "GENERATE_ACCESS_TOKEN")
            return {
                authToken: newAuthToken,
                refreshToken: newRefreshToken,
                authTokenExpiry: tokenExpiration
            }
        }

        @httpPost("/attribute")
        public async attributeVoucherToUser(request: express.Request) {
            await this.validateAuthToken(request, "VOUCHER_ATTRIBUTE")
            this.logger.info("Auth token is valid")
            const {userPhoneHash, code} = request.body
            this.logger.info("Received request to attribute voucher to user", {userPhoneHash, code})
            if (!code || !userPhoneHash) {
                throw new DataError("Kindly provide the voucher code and user phone hash to attribute voucher.")
            }
            await this.constelloService.attributeCouponToUser(code, userPhoneHash)
        }

        private async validateAuthToken(request: express.Request, apiName: string, externalClientId?: string) {
            let authToken = request.headers["authorization"] as string
            if (authToken) {
                authToken = (request.headers["authorization"] as string).split(" ")?.[1]?.trim()
            } else {
                throw new UnauthorizedError("Kindly provide the auth token.")
            }
            if (!authToken) {
                throw new UnauthorizedError("Invalid Authorization header.")
            }
            const isAuthTokenValid = await this.redisAuthTokenService.isTokenValid(authToken, apiName)
            this.logger.info("isAuthTokenValid: ", isAuthTokenValid)
            if (!isAuthTokenValid) {
                throw new UnauthorizedError("Invalid Authorization Token in header")
            }
            if (externalClientId) {
                const clientMappedToToken = await this.redisAuthTokenService.getValue(`${apiName}.${authToken}`)
                if (!clientMappedToToken || clientMappedToToken !== externalClientId) {
                    throw new UnauthorizedError("Client Id not authorized.")
                }
            }
        }
    }

    return VoucherController
}

export default VoucherControllerFactory
