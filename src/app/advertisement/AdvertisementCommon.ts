import { CPAMetadata, CPAPlayPosition } from "@curefit/diy-common"
import { Action } from "@curefit/vm-models"
import { ProductType } from "@curefit/product-common"


export interface IAdvertisementResponse {
    cpaPosition: CPAPlayPosition
    cpaId: string
    cpaType: string
    meta: {
        sessionId: string
        cpaId: string,
        cpaUrl?: string;
    }
    cpaPlayTime?: CPAMetadata
    action?: Action
    contentCategory?: ProductType
}
