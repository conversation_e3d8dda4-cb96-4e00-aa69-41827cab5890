import { controller, httpGet, httpPost } from "inversify-express-utils"
import AuthMiddleware from "../auth/AuthMiddleware"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import * as express from "express"
import { Container, inject } from "inversify"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { UserContext } from "@curefit/userinfo-common"
import { IAdvertisementController } from "./IAdvertisementController"
import { ICPAResponse } from "@curefit/diy-common"
import { IAdvertisementResponse } from "./AdvertisementCommon"
import { AdvertisementUtil } from "../util/AdvertisementUtil"
import AppUtil from "../util/AppUtil"
import * as _ from "lodash"
import { CONTENT_CDN_BASE_PATH } from "@curefit/util-common"
import { ProductType } from "@curefit/product-common"

export const AdvertisementControllerFactory = (kernel: Container) => {
    @controller("/advertisement",
        kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession
    )
    class AdvertisementController implements IAdvertisementController {
        constructor(
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
            @inject(BASE_TYPES.ILogger) private logger: ILogger
        ) {
        }

        @httpGet("/content")
        async getAdvertisement(req: express.Request): Promise<{
            ads: IAdvertisementResponse[]
        }> {
            const userContext = req.userContext as UserContext
            const userId = userContext.userProfile.userId
            const contentId = req.query.adContentId
            const contentCategory = req.query.adContentCategory
            const isWeb: boolean = AppUtil.isWeb(userContext)
            const user = await this.serviceInterfaces.userCache.getUser(userId)
            const sessionId = AdvertisementUtil.getSessionIdForAdvertisement(userContext, user)

            const cpaResponse = await this.serviceInterfaces.cpaService.getCrossPromotionAdsForContent(userId, sessionId, contentCategory, contentId )
            const clientAdResponse = this.createAdResponsePromise(cpaResponse, isWeb, sessionId, contentCategory )

            return {
                ads: clientAdResponse
            }
        }

        @httpPost("/mark")
        async markAdvertisement(req: express.Request): Promise<boolean> {
            const userContext = req.userContext as UserContext
            const sessionId = req.body.sessionId
            const userId = userContext.userProfile.userId
            const cpaId = req.body.cpaId

            const markOperation = await this.serviceInterfaces.cpaService.markCPAViewedForUserSession(userId, sessionId, cpaId)

            return markOperation.status
        }


        // --------------------------------------- PRIVATE METHODS -----------------------------------------------------

        private createAdResponsePromise(cpaResponse: ICPAResponse[], isWeb: boolean, sessionId: string, contentCategory: ProductType): IAdvertisementResponse[] {
            const adResponse: IAdvertisementResponse[] = []

            cpaResponse.forEach(cpa => {
                const response: IAdvertisementResponse = {
                    cpaId: cpa.cpaId,
                    cpaPosition: cpa.cpaPlayPosition,
                    cpaPlayTime: cpa.cpaMetadata,
                    cpaType: cpa.cpaType,
                    meta: {
                        sessionId: sessionId,
                        cpaId: cpa.cpaId,
                        cpaUrl: isWeb ? CONTENT_CDN_BASE_PATH + cpa.cpaResources.webURL : CONTENT_CDN_BASE_PATH + cpa.cpaResources.appURL
                    },
                    action: {
                        title: "EXPLORE",
                        actionType: isWeb ? _.get(cpa, "cpaCTA.WEB.actionType") : _.get(cpa, "cpaCTA.APP.actionType"),
                        url: isWeb ? _.get(cpa, "cpaCTA.WEB.actionURL") : _.get(cpa, "cpaCTA.APP.actionURL")
                    },
                    contentCategory: contentCategory

                }
                adResponse.push(response)
            })

            return adResponse
        }

    }
}