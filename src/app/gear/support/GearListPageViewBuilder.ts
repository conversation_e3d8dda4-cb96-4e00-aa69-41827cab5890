import * as _ from "lodash"
import * as momentTz from "moment-timezone"
import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import IssueBusiness from "../../crm/IssueBusiness"
import { UserContext } from "@curefit/userinfo-common"
import { GearOrderDetailObject } from "@curefit/gear-common"
import { SupportListPageView } from "../../crm/SupportListPageView"
import { PageWidget } from "../../page/Page"
import { Action } from "../../common/views/WidgetView"
import { SupportActionableCardWidget, SupportEmptyListingWidget } from "../../page/PageWidgets"
import { ActionUtil as AppActionUtil } from "../../util/ActionUtil"
import { GearService, GEARVAULT_CLIENT_TYPES } from "@curefit/gearvault-client"
import { LineItem } from "@curefit/gear-common"
import AppUtil from "../../util/AppUtil"
import { ISegmentService } from "@curefit/vm-models"

@injectable()
class GearListPageViewBuilder {
    constructor(
        @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness,
        @inject(GEARVAULT_CLIENT_TYPES.GearService) private gearService: GearService,
        @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService,
    ) {
    }

    async buildGearListPageView(userContext: UserContext, gearOrders: GearOrderDetailObject[], pageNumber: number): Promise<SupportListPageView> {
        const widgets: PageWidget[] = []
        let actions: Action[]
        if (_.isEmpty(gearOrders) && !(await AppUtil.isNewSupportInFlutterSupported(this.segmentService, userContext))) {
            if (pageNumber === 1) {
                widgets.push(new SupportEmptyListingWidget("GEAR", "No Gear Orders Yet!", "On the court, on the road, on the streets. It's time to get Fashionably Active. Get your cultgear today!"))
                actions = [{
                    actionType: "NAVIGATION",
                    url: "curefit://cultgearclp?pageId=GearTab&selectedTab=CultGear",
                    title: "Order gear"
                }]
            }
        } else {
            const cardWidgetPromises: Promise<SupportActionableCardWidget>[] = _.map(gearOrders, gearOrder => {
                return this.getActionableCardWidget(userContext, gearOrder)
            })
            const cardWidgets: SupportActionableCardWidget[] = await Promise.all(cardWidgetPromises)
            cardWidgets.map(cardWidget => {
                widgets.push(cardWidget)
            })
        }
        return new SupportListPageView(widgets, actions)
    }

    async buildGearListWidgets(userContext: UserContext, gearOrders: GearOrderDetailObject[]): Promise<SupportActionableCardWidget[]> {
        if (!_.isEmpty(gearOrders)) {
            const cardWidgetPromises: Promise<SupportActionableCardWidget>[] = _.map(gearOrders, gearOrder => {
                return this.getActionableCardWidget(userContext, gearOrder)
            })
            const cardWidgets: SupportActionableCardWidget[] = await Promise.all(cardWidgetPromises)
            return cardWidgets
        }
        return []
    }

    private async getActionableCardWidget(userContext: UserContext, gearOrder: GearOrderDetailObject): Promise<SupportActionableCardWidget> {
        const reportIssueParams = this.issueBusiness.getGearIssueParams(gearOrder)
        const lineItem = gearOrder.line_items[0]
        const imageUrl = lineItem ? lineItem.variant.images[0].mini_url : undefined

        return {
            widgetType: "SUPPORT_ACTIONABLE_CARD_WIDGET",
            title: this.productTitle(gearOrder.line_items),
            subTitle: `#${gearOrder.external_service_order_id}`,
            footer: AppUtil.isWeb(userContext) ? undefined : [{
                text: `${momentTz.tz(gearOrder.created_at, "Asia/Kolkata").format("ddd, D MMM")}`,
            }],
            cardAction: {
                actionType: "NAVIGATION",
                url: AppUtil.isWeb(userContext) ? AppActionUtil.getIssuesUrlOld(reportIssueParams) : `curefit://gearorder?orderId=${gearOrder.external_service_order_id}`
            },
            imageUrl: imageUrl,
            time: AppUtil.isWeb(userContext) ? `${momentTz.tz(gearOrder.created_at, "Asia/Kolkata").format("ddd, D MMM")}` : undefined,
            timestamp: momentTz(gearOrder.created_at, "YYYY-MM-DDTHH:mm:ss.SSSZ").valueOf(),
            timezone: "Asia/Kolkata"
        }
    }

    private productTitle(lineItems: LineItem[]) {
        if (lineItems.length > 1) {
            return lineItems[0].variant.name + " + " + (lineItems.length - 1) + " items ordered"
        }
        return lineItems[0].variant.name
    }
}

export default GearListPageViewBuilder
