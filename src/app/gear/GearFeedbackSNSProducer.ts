import { inject, injectable } from "inversify"
import { EventData, EVENTS_TYPES, EventsService } from "@curefit/events-util"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { Feedback, Rating } from "@curefit/feedback-common"
import { Constants } from "@curefit/base-utils"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { USER_CLIENT_TYPES } from "@curefit/user-client"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CacheHelper } from "../util/CacheHelper"
import { User } from "@curefit/user-common"

@injectable()
export class GearFeedbackSNSProducer {

    private STAGE_PREFIX = "stage-"
    private PROD_PREFIX = "production-"
    private SNS_TOPIC = "d2c-analytics-feedbacks-sns"

    constructor(
        @inject(EVENTS_TYPES.EventsService) private eventsService: EventsService,
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected cacheHelper: <PERSON><PERSON><PERSON>elper,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService
    ) {
    }

    public async pushFeedbackToSNS(feedback: Feedback): Promise<void> {
        const feedbackPayload: GearFeedback = await this.getGearFeedbackPayload(feedback)
        const payload: EventData <GearFeedback> = new EventData<GearFeedback>("GEAR_FEEDBACK_SUBMIT", ["GEAR"], new Date().getUTCDate(), feedbackPayload)
        try {
            await this.eventsService.publishMessage<GearFeedback>(this.getSNSARN(), payload )
        } catch (e) {
            // not throwing an error
            this.rollbarService.sendError(e, )
            this.logger.error("Failed to push event to SNS with body:" + JSON.stringify(feedback), e)
        }

    }

    private getSNSARN() {
        let prefix = this.STAGE_PREFIX
        if (["PRODUCTION", "ALPHA"].includes(process.env.APP_ENV)) {
            prefix = this.PROD_PREFIX
        }
        return prefix + this.SNS_TOPIC
    }

    private async getGearFeedbackPayload(feedback: Feedback): Promise<GearFeedback> {

        const user: User = await this.cacheHelper.getUser(feedback.userId)
        return {
            feedbackId: feedback.feedbackId,
            productType: feedback.productType,
            feedbackType: feedback.feedbackType,
            userId: feedback.userId,
            orderId: feedback.orderId,
            dateTime: feedback.dateTime,
            updatedDate: feedback.dateTime,
            createdDate: feedback.dateTime,
            itemId: feedback.itemId,
            itemIds: feedback.itemIds,
            selectedItemIds: feedback.selectedItemIds,
            rating: feedback.rating,
            review: feedback.review,
            selectedTags: feedback.selectedTags,
            images: feedback.images,
            userName: user.firstName + " " + user.lastName,
            userImage: user.profilePictureUrl
        }
    }

}


interface GearFeedback {

    feedbackId: string

    productType: string

    feedbackType: string

    userId: string

    orderId: string

    dateTime: Date

    updatedDate: Date

    createdDate: Date

    itemId: string

    itemIds: string[]

    selectedItemIds: string[]

    rating: Rating

    review: string

    selectedTags: string[]

    images: string[]

    // user info
    userName: string

    userImage: string
}