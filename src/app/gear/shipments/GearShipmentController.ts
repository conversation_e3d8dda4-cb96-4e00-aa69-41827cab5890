import { Container, inject } from "inversify"
import { controller, httpGet } from "inversify-express-utils"
import AuthMiddleware from "../../auth/AuthMiddleware"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import * as express from "express"
import { GearService, TrackShipmentResponse, GEARVAULT_CLIENT_TYPES } from "@curefit/gearvault-client"
import GearShipmentViewBuilder, { GearTrackShipmentView } from "./GearShipmentViewBuilder"
import { Session, UserContext } from "@curefit/userinfo-common"
import GearInventoryUnitViewBuilder, { ModifyOrderType } from "../inventoryUnit/GearInventoryUnitViewBuilder"
import { ErrorCodes } from "../../error/ErrorCodes"
import { ErrorFactory } from "@curefit/error-client"
import AppUtil from "../../util/AppUtil"
import { CFServiceInterfaces } from "../../page/vm/ServiceInterfaces"
import { GearInventoryUnit } from "@curefit/gear-common"
import CultsportUtil from "../../util/CultsportUtil"
import { BaseOrder, Order } from "@curefit/order-common"
import { BASE_TYPES, Logger } from "@curefit/base"
import { IOrderService, OMS_API_CLIENT_TYPES } from "@curefit/oms-api-client"

function controllerFactory(kernel: Container) {
    @controller("/gear/v1/shipments", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class GearShipmentController {
        constructor(
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
            @inject(GEARVAULT_CLIENT_TYPES.GearService) private gearService: GearService,
            @inject(CUREFIT_API_TYPES.GearShipmentViewBuilder) private gearShipmentViewBuilder: GearShipmentViewBuilder,
            @inject(CUREFIT_API_TYPES.GearInventoryUnitViewBuilder) private gearInventoryUnitViewBuilder: GearInventoryUnitViewBuilder,
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(OMS_API_CLIENT_TYPES.OrderService) protected omsApiClient: IOrderService,
        ) { }

        @httpGet("/track/:shipmentId")
        async track(req: express.Request): Promise<GearTrackShipmentView> {
            const shipmentId: string = req.params.shipmentId
            const session: Session = req.session
            const userContext: UserContext = req.userContext as UserContext
            const trackShipmentResponse: TrackShipmentResponse = await this.gearService.trackShipment(shipmentId)
            if (trackShipmentResponse.external_service_user_id !== session.userId) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_AUTHORIZED_TO_VIEW_ORDER, 401).withDebugMessage("Not authorized to view order").build()
            }
            return this.gearShipmentViewBuilder.buildTrackingView(trackShipmentResponse, shipmentId, userContext)
        }

        @httpGet("/inventory/track/:shipmentId/:inventoryUnitId")
        async trackV2(req: express.Request): Promise<GearTrackShipmentView> {
            const shipmentId: string = req.params.shipmentId
            const inventoryUnitId: string = req.params.inventoryUnitId
            const userContext: UserContext = req.userContext as UserContext
            const trackShipmentResponse: TrackShipmentResponse = await this.gearService.trackShipment(shipmentId)
            const gearOrder = await this.gearService.getOrderWithShipmentStatus(trackShipmentResponse.external_service_order_id)
            const session: Session = req.session
            if (trackShipmentResponse.external_service_user_id !== session.userId) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_AUTHORIZED_TO_VIEW_ORDER, 401).withDebugMessage("Not authorized to view order").build()
            }
            return this.gearShipmentViewBuilder.buildTrackingView(trackShipmentResponse, shipmentId, userContext, gearOrder, inventoryUnitId)
        }

        @httpGet("/cancel-track/:shipmentId")
        async cancelTrack(req: express.Request): Promise<GearTrackShipmentView> {
            const shipmentId: string = req.params.shipmentId
            const userContext: UserContext = req.userContext as UserContext
            const trackShipmentResponse: TrackShipmentResponse = await this.gearService.trackShipment(shipmentId)
            const inventoryUnitsCount: number = trackShipmentResponse.inventory_units ? trackShipmentResponse.inventory_units.length : null
            const isShipmentCancelled: boolean = trackShipmentResponse.canceled
            const meta = {retry: !isShipmentCancelled}
            const itemCountBody: string = inventoryUnitsCount > 1 ? `${inventoryUnitsCount} items` : `${inventoryUnitsCount} item`
            const subTitleSuccessMessage: string = `We confirm cancellation of ${itemCountBody} from your recent order.`
            const title: string = isShipmentCancelled ? (inventoryUnitsCount > 1  ? "Items Cancelled" : "Item Cancelled") : "Taking longer than usual"
            const subtitle: string = isShipmentCancelled ? subTitleSuccessMessage : "We'll notify as soon as those items are cancelled. Item cancellation seems to be taking longer than usual currently."
            const session: Session = req.session
            if (trackShipmentResponse.external_service_user_id !== session.userId) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_AUTHORIZED_TO_VIEW_ORDER, 401).withDebugMessage("Not authorized to view order").build()
            }
            const order: BaseOrder = await this.omsApiClient.getOrder(trackShipmentResponse.external_service_order_id)
            const gearInventoryUnits: GearInventoryUnit[] = await this.gearService.getInventoryUnitsForCancellation(trackShipmentResponse.inventory_units[0].id)
            if (AppUtil.isCultSportWebApp(userContext)) {
                return this.gearInventoryUnitViewBuilder.buildCultsportItemCancelOrReturnSuccessViewV2(title, subtitle, ModifyOrderType.Cancel, meta, CultsportUtil.getCancelAnalyticsEvent(gearInventoryUnits[0], order))
            }
            return this.gearInventoryUnitViewBuilder.buildItemCancelOrReturnSuccessView(title, subtitle, meta)
        }
    }

    return GearShipmentController
}

export default controllerFactory
