import * as _ from "lodash"
import { inject, injectable } from "inversify"
import {
    DeliveryPartnerMetadata,
    GEAR_FORWARD_SHIPMENT_STATES_CHRONOLOGICAL_ORDER,
    GEAR_RETURN_SHIPMENT_STATES_CHRONOLOGICAL_ORDER,
    GearImage,
    GearOrderDetailObject, GearOrderShipment,
    ShipmentType,
    TrackShipmentResponse,
    TrackShipmentResponseV2,
} from "@curefit/gear-common"
import { Action, ProductDetailPage, WidgetType, WidgetView } from "../../common/views/WidgetView"
import { PriceUtil, TimeUtil, Timezone } from "@curefit/util-common"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import IssueBusiness from "../../crm/IssueBusiness"
import { GearUtil } from "@curefit/gearvault-client"
import { Constants } from "@curefit/base-utils"
import { UserContext } from "@curefit/userinfo-common"
import { ActionUtil } from "../../util/ActionUtil"
import { ActionUtil as BaseActionUtils } from "@curefit/base-utils"
import { IssueProductType } from "@curefit/issue-common"
import { InventoryUnit } from "../../order/GearOrderWithShipmentViewBuilder"
import AppUtil from "../../util/AppUtil"
import GearShipmentView, { GearTrackShipmentMiniStatesView } from "../../order/GearShipmentView"
import { ISegmentService } from "@curefit/vm-models"

interface BaseGearShipmentView extends ProductDetailPage {

}

interface TrackShipmentHeader {
    numberTitle: string
    number: string
    summary: string
    status: string
    shipmentHealth: string
    shipmentId: string
    waybill?: string
    courierPartner?: string
    action?: Action
}

interface SectionDetails {
    title: string
    subtitle: string
}

interface GearShipmentStatus extends SectionDetails {
    completePercent: number
}

export interface GearTrackShipmentView extends BaseGearShipmentView {

}

@injectable()
class GearShipmentViewBuilder extends GearShipmentView {
    private iosVersion = 7.28
    private androidVersion = 7.28

    @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness
    @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService

    async buildTrackingView(trackShipmentResponse: TrackShipmentResponse, shipmentId: string, userContext: UserContext, gearOrder?: GearOrderDetailObject, inventoryUnitId?: string): Promise<GearTrackShipmentView> {
        const widgetPromises: Promise<WidgetView>[] = []
        const timezone = userContext.userProfile.timezone

        widgetPromises.push(this.buildHeaderInfo(trackShipmentResponse, shipmentId, timezone))
        widgetPromises.push(this.buildVerticalStepIndicatorWidgetView(trackShipmentResponse, timezone))

        if (gearOrder && inventoryUnitId) {
            widgetPromises.push(this.getInventoryUnitsPrice(gearOrder, shipmentId, inventoryUnitId, userContext))
        }

        const widgets = await Promise.all(widgetPromises)
        const orderId: string = trackShipmentResponse.external_service_order_id

        let actions: Action[] = []
        if (GearUtil.isGearIssuesSupported(userContext.sessionInfo.userAgent, userContext.sessionInfo.appVersion, userContext.sessionInfo.osName, this.androidVersion, this.iosVersion)) {
            const productType: IssueProductType = "CULT_GEAR"
            const query = {
                productType: productType,
                productStates: this.issueBusiness.mapGearShipmentStateToIssueProductState(trackShipmentResponse.current_state, trackShipmentResponse.edd),
                meta: {
                    orderId: orderId,
                    shipmentId: shipmentId
                }
            }
            actions = [
                {
                    title: "Need Help",
                    actionType: "NAVIGATION",
                    url:  ActionUtil.getIssuesUrlOld(query)
                }]
        } else {
            actions = [{
                actionType: "EXTERNAL_DEEP_LINK",
                title: "Need Help",
                url: `mailto:${Constants.customerCareMail}?subject=[Gear] Need help with [Order ${trackShipmentResponse.external_service_order_id}] [Shipment ${shipmentId}]`
            }]
        }

        return { widgets, actions }
    }

    private async buildHeaderInfo(trackShipmentResponse: TrackShipmentResponse, shipmentId: string, timezone: Timezone): Promise<WidgetView> {
        const edd: Date = trackShipmentResponse.edd ? TimeUtil.parseDateTime(trackShipmentResponse.edd, timezone) : undefined
        const currentState: string = GearShipmentViewBuilder.getCurrentState(trackShipmentResponse)
        const isForwardShipment: boolean = trackShipmentResponse.shipment_type === "forward"
        const summary: string = isForwardShipment ? GearShipmentViewBuilder.getForwardShipmentSummary(currentState, edd, timezone) : GearShipmentViewBuilder.getReturnShipmentSummary(currentState)
        const number: string = trackShipmentResponse.external_service_order_id || "Not Available"
        const trackingHeaderInfo: TrackShipmentHeader = {
            numberTitle: "Order ID",
            status: isForwardShipment ? GearShipmentViewBuilder.getForwardShipmentStatus(currentState) : GearShipmentViewBuilder.getReturnShipmentStatus(currentState),
            shipmentHealth: isForwardShipment ? GearShipmentViewBuilder.getForwardShipmentHealth(currentState) : GearShipmentViewBuilder.getReturnShipmentHealth(currentState),
            number,
            summary,
            shipmentId,
            waybill: trackShipmentResponse.waybill,
            courierPartner: trackShipmentResponse.courier_partner,
            action: {
                actionType: "COPY_TO_CLIPBOARD",
                title: "Copy",
                meta: {
                    text: trackShipmentResponse.waybill,
                    dataType: "WAYBILL"
                }
            }
        }

        return new GearTrackingHeaderWidget(trackingHeaderInfo)
    }

    private static getCurrentState(response: TrackShipmentResponse): string {
        if (response.current_state === "refund_initiated") {
            return _.last(response.state_change_history.map(state_change => state_change.state))
        }

        switch (response.current_state) {
            case "pending":
                return "ready"
            case "received":
            case "qc_done":
                return "received"
            case "rto_out_for_delivery":
            case "rto_intransit":
            case "rto_delivered":
                return "rto_initiated"
        }

        return response.current_state
    }

    private static getForwardShipmentHealth(state: string): string {
        switch (state) {
            case "ready":
            case "packed":
            case "shipped":
            case "out_for_delivery":
            case "failed_delivery":
            case "rto_initiated":
            case "return_initiated":
                return "in_progress"

            case "delivered":
            case "received":
            case "exchange_picked_up":
                return "complete"

            default:
                return ""
        }
    }

    private static getReturnShipmentHealth(state: string): string {
        switch (state) {
            case "return_initiated_return_shipment":
            case "return_approved":
            case "out_for_pickup":
            case "failed_pickup":
            case "picked_up":
                return "in_progress"

            case "received":
                return "complete"

            case "pickup_canceled":
                return "stale"

            default:
                return ""
        }
    }

    private static getForwardShipmentStatus(state: string): string {
        switch (state) {
            case "ready":
                return "Order Confirmed"

            case "packed":
                return "Ready to ship"

            case "shipped":
                return "Shipped"

            case "out_for_delivery":
                return "Out for Delivery"

            case "failed_delivery":
                return "Delivery Failed"

            case "delivered":
                return "Delivered"

            case "return_initiated":
                return "Return Initiated"

            case "rto_initiated":
                return "Returning to Origin"

            case "received":
                return "Received at Warehouse"

            case "exchange_picked_up":
                return "Replaced"

            default:
                return ""
        }
    }

    private static getReturnShipmentStatus(state: string): string {
        switch (state) {
            case "return_initiated_return_shipment":
                return "Return Initiated"

            case "return_approved":
                return "Return Approved"

            case "out_for_pickup":
                return "Out for Pickup"

            case "failed_pickup":
                return "Pickup Failed"

            case "pickup_canceled":
                return "Pickup Cancelled"

            case "picked_up":
                return "Picked Up"

            case "received":
                return "Received at Warehouse"

            default:
                return ""
        }
    }


    private static getForwardShipmentSummary(state: string, edd: Date, timezone: Timezone) {
        let summary: string = ""

        switch (state) {
            case "ready":
                summary = "Order Confirmed."

                if (edd) {
                    summary += ` Expected to be delivered by ${TimeUtil.formatDateInTimeZone(timezone, edd, "Do MMMM h:mm a")}`
                }

                break

            case "packed":
                summary = "Order Packed."

                if (edd) {
                    summary += ` Expected to be delivered by ${TimeUtil.formatDateInTimeZone(timezone, edd, "Do MMMM h:mm a")}`
                }

                break

            case "shipped":
                summary = "Order Shipped."

                if (edd) {
                    summary += ` Expected to be delivered by ${TimeUtil.formatDateInTimeZone(timezone, edd, "Do MMMM h:mm a")}`
                }

                break

            case "out_for_delivery":
                summary = "Order out for Delivery."

                if (edd) {
                    summary += ` Expected to be delivered by ${TimeUtil.formatDateInTimeZone(timezone, edd, "Do MMMM h:mm a")}`
                }

                break

            case "failed_delivery":
                summary += "Failed to deliver."
                break

            case "delivered":
                summary = "Order Delivered."
                break

            case "exchange_picked_up":
                summary = "Item Replaced."
                break

            case "rto_initiated":
                summary = "Returning to Origin."
                break

            case "received":
                summary = "Received at warehouse."
                break
        }

        return summary
    }

    private static getReturnShipmentSummary(state: string) {
        switch (state) {
            case "return_initiated_return_shipment":
                return "Return request has been initiated. Pickup details will be shared once your request is approved."

            case "return_approved":
                return "Your return request has been approved. Pickup details will be shared shortly."

            case "out_for_pickup":
                return "Your item is out for pickup. It will be picked up today by our logistics partner."

            case "failed_pickup":
                return "Could not collect package from you."

            case "pickup_canceled":
                return "We will not attempt for pickup anymore."

            case "picked_up":
                return "Item picked up successfully by our logistics partner. Refund has been initiated. It should reflect in original mode of payment in 5-7 days."

            case "received":
                return "Item received at warehouse."
        }
    }

    private async buildVerticalStepIndicatorWidgetView(trackShipmentResponse: TrackShipmentResponse, timezone: Timezone): Promise<VerticalStepIndicatorWidget> {
        const shipmentStatusData: GearShipmentStatus[] = this.getShipmentStatusData(trackShipmentResponse, GearShipmentViewBuilder.getCurrentState(trackShipmentResponse), timezone)
        const shipmentMiniStates: GearTrackShipmentMiniStatesView = this.getShipmentMiniStates(trackShipmentResponse, timezone)
        return new VerticalStepIndicatorWidget(shipmentStatusData, shipmentMiniStates)
    }

    private getShipmentStatusData(trackShipmentResponse: TrackShipmentResponse, currentState: string, timezone: Timezone): GearShipmentStatus[] {
        const gearShipmentStatuses: GearShipmentStatus[] = []
        let marked: boolean = false
        const orderOfStatesToBeDisplayed = GearShipmentViewBuilder.getOrderOfStatesFor(trackShipmentResponse)
        let stateChanges = trackShipmentResponse.state_change_history

        orderOfStatesToBeDisplayed.forEach((state, index) => {
            const stateChange = _.find(stateChanges, { state: state })
            if (stateChange !== undefined) {
                stateChanges = _.without(stateChanges, stateChange)
            }
            const time = stateChange && stateChange.time ? TimeUtil.parseDateTime(stateChange.time, timezone) : undefined
            const edd = trackShipmentResponse.edd ? TimeUtil.parseDateTime(trackShipmentResponse.edd, timezone) : undefined
            const completed = (stateChange && true) || orderOfStatesToBeDisplayed.slice(0, orderOfStatesToBeDisplayed.indexOf(currentState) + 1).includes(state)
            const metadata = stateChange ? stateChange.metadata : undefined
            const placedAt = TimeUtil.parseDateTime(trackShipmentResponse.placed_at, timezone)
            const sectionDetails: SectionDetails = trackShipmentResponse.shipment_type === "forward" ? GearShipmentViewBuilder.getForwardSectionDetails(state, placedAt, metadata, completed, timezone, time, edd) : GearShipmentViewBuilder.getReturnSectionDetails(state, placedAt, completed, timezone, time, edd)

            if (completed) {
                gearShipmentStatuses.push({
                    title: sectionDetails.title,
                    subtitle: sectionDetails.subtitle,
                    completePercent: 1
                })
            } else {
                gearShipmentStatuses.push({
                    title: sectionDetails.title,
                    subtitle: sectionDetails.subtitle,
                    completePercent: 0
                })

                // Mark the last incomplete state as 50% completed
                if (index > 0 && !marked) {
                    (gearShipmentStatuses[index - 1]).completePercent = 0.5
                    marked = true
                }
            }
        })
        return gearShipmentStatuses
    }

    private static getOrderOfStatesFor(trackShipmentResponse: TrackShipmentResponse): string[] {
        let responseStates: string[] = trackShipmentResponse.state_change_history.map(state_change => state_change.state)
        const currentState = GearShipmentViewBuilder.getCurrentState(trackShipmentResponse)

        responseStates = _.remove(responseStates, state => {
            return !["qc_done", "rto_out_for_delivery", "rto_intransit", "rto_delivered"].includes(state)
        })

        if (trackShipmentResponse.shipment_type === "forward" && responseStates.includes("exchange_picked_up")) {
            return responseStates
        }

        if (trackShipmentResponse.shipment_type === "return" && !responseStates.includes("failed_pickup")) {
            return GEAR_RETURN_SHIPMENT_STATES_CHRONOLOGICAL_ORDER
        }

        if (trackShipmentResponse.shipment_type === "forward" && !responseStates.includes("failed_delivery")) {
            return GEAR_FORWARD_SHIPMENT_STATES_CHRONOLOGICAL_ORDER
        }

        if (currentState === "out_for_pickup" && responseStates.includes("failed_pickup")) {
            return _.concat(responseStates, ["picked_up", "received"])
        }

        if (currentState === "out_for_delivery" && responseStates.includes("failed_delivery")) {
            return _.concat(responseStates, ["delivered"])
        }

        if (currentState === "rto_initiated" || currentState === "picked_up" || currentState === "received" && !responseStates.includes("received")) {
            return _.concat(responseStates, ["received"])
        }

        return responseStates
    }

    private static getForwardSectionDetails(state: string, placedAt: Date, metadata: DeliveryPartnerMetadata, completed: boolean,
        timezone: Timezone, time: Date = undefined, edd: Date = undefined): SectionDetails {
        const sectionDetails: SectionDetails = {
            title: "",
            subtitle: ""
        }

        switch (state) {
            case "ready":
                sectionDetails.title = "Order Confirmed"
                sectionDetails.subtitle = `Placed on ${TimeUtil.formatDateInTimeZone(timezone, placedAt, "Do MMMM, dddd [at] h:mm A")}`
                break

            case "packed":
                sectionDetails.title = "Order Packed"

                if (time) {
                    sectionDetails.subtitle = `Packed on ${TimeUtil.formatDateInTimeZone(timezone, time, "Do MMMM, dddd [at] h:mm A")}`
                }

                break

            case "shipped":
                sectionDetails.title = "Order Shipped"

                if (time) {
                    sectionDetails.subtitle = `Shipped on ${TimeUtil.formatDateInTimeZone(timezone, time, "Do MMMM, dddd [at] h:mm A")}`
                }

                break

            case "out_for_delivery":
                sectionDetails.title = "Out for Delivery"

                if (time) {
                    sectionDetails.subtitle = `${TimeUtil.formatDateInTimeZone(timezone, time, "Do MMMM, dddd [at] h:mm A")}`
                }

                break

            case "failed_delivery":
                sectionDetails.title = "Failed to Deliver"
                sectionDetails.subtitle = metadata && metadata.reason_code ? GearShipmentViewBuilder.getNdrReason(metadata.reason_code) : "Could not deliver due to unavoidable reason. Will reattempt."
                break

            case "exchange_picked_up":
                sectionDetails.title = "Replaced"

                if (time) {
                    sectionDetails.subtitle = `${TimeUtil.formatDateInTimeZone(timezone, time, "Do MMMM, dddd [at] h:mm A")}`
                }
                break

            case "delivered":
                sectionDetails.title = "Delivery"

                if (completed) {
                    sectionDetails.title = "Delivered"
                    sectionDetails.subtitle = "Delivered"

                    if (time) {
                        sectionDetails.subtitle += ` on ${TimeUtil.formatDateInTimeZone(timezone, time, "Do MMMM, dddd [at] h:mm A")}`
                    }

                    if (metadata && metadata.remark) {
                        sectionDetails.subtitle += `\n(${metadata.remark})`
                    }
                } else if (edd) {
                    sectionDetails.subtitle = `Expected to be delivered by ${TimeUtil.formatDateInTimeZone(timezone, edd, "Do MMMM h:mm a")}`
                }

                break

            case "rto_initiated":
                sectionDetails.title = "Returning to Origin"
                sectionDetails.subtitle = "Not able to deliver"
                break

            case "received":
                sectionDetails.title = "Received at Warehouse"

                if (completed) {
                    sectionDetails.subtitle = "Received at Warehouse"

                    if (time) {
                        sectionDetails.subtitle += ` on ${TimeUtil.formatDateInTimeZone(timezone, time, "Do MMMM, dddd [at] h:mm A")}`
                    }

                }

                break
        }

        return sectionDetails
    }

    private static getNdrReason(reasonCode: number) {
        switch (reasonCode) {
            case 0:
                return "Could not deliver due to unavoidable reason. Will reattempt."

            case 1:
                return "Could not deliver as you were not available. Will reattempt."

            case 2:
                return "Could not deliver. Our team will contact with you to understand your concern."

            case 3:
                return "Could not deliver. Will reattempt basis your request."

            case 4:
                return "Could not deliver due to unavoidable reason. Will reattempt."

            case 5:
                return "Could not connect with you."

            case 6:
                return "Could not locate your address. We will contact you."

            case 7:
                return "Could not deliver. Our team will connect with you."

            case 8:
                return "Could not deliver to your address. Our team will connect with you."

            case 9:
                return "Order Already Cancelled."

            case 10:
                return "Self Collect."

            case 11:
                return "Shipment Seized By Customer."

        }
    }

    private static getReturnSectionDetails(state: string, placedAt: Date, completed: boolean,
        timezone: Timezone, time: Date = undefined, edd: Date = undefined): SectionDetails {
        const sectionDetails: SectionDetails = {
            title: "",
            subtitle: ""
        }

        switch (state) {
            case "return_initiated_return_shipment":
                sectionDetails.title = "Return initiated"
                sectionDetails.subtitle = `Initiated on ${TimeUtil.formatDateInTimeZone(timezone, placedAt, "Do MMMM, dddd [at] h:mm A")}`
                break

            case "return_approved":
                sectionDetails.title = "Return approved"

                if (time) {
                    sectionDetails.subtitle = `Approved on ${TimeUtil.formatDateInTimeZone(timezone, time, "Do MMMM, dddd [at] h:mm A")}`
                }

                break

            case "out_for_pickup":
                sectionDetails.title = "Out for pickup"

                if (time) {
                    sectionDetails.subtitle = `${TimeUtil.formatDateInTimeZone(timezone, time, "Do MMMM, dddd [at] h:mm A")}`
                }

                break

            case "failed_pickup":
                sectionDetails.title = "Pickup Failed"
                sectionDetails.subtitle = "Could not collect package from you"
                break

            case "pickup_canceled":
                sectionDetails.title = "Pickup Cancelled"
                sectionDetails.subtitle = "We will not attempt for pickup anymore"
                break

            case "picked_up":
                sectionDetails.title = "Picked Up"

                if (completed) {
                    sectionDetails.subtitle = "Picked up"

                    if (time) {
                        sectionDetails.subtitle += ` on ${TimeUtil.formatDateInTimeZone(timezone, time, "Do MMMM, dddd [at] h:mm A")}`
                    }
                }

                break

            case "received":
                sectionDetails.title = "Received at Warehouse"

                if (completed) {
                    sectionDetails.subtitle = "Received at Warehouse"

                    if (time) {
                        sectionDetails.subtitle += ` on ${TimeUtil.formatDateInTimeZone(timezone, time, "Do MMMM, dddd [at] h:mm A")}`
                    }

                }

                break
        }

        return sectionDetails
    }

    private async getInventoryUnitsPrice(gearOrder: GearOrderDetailObject, shipmentId: string, inventoryUnitId: string, userContext: UserContext): Promise<InventoryUnitPriceCard> {
        const lineItems = _.keyBy(gearOrder.line_items, "id")
        const shipment: GearOrderShipment = _.find(gearOrder.shipments, ["id", Number(shipmentId)])
        let inventoryUnits
        const gearShipment: TrackShipmentResponseV2 = {...shipment, state_change_history: shipment.history}
        switch (gearShipment.shipment_type) {
            case ShipmentType.RETURN:
                inventoryUnits = shipment.return_inventory_units
                break
            case ShipmentType.FORWARD:
            default:
                inventoryUnits = _.reject(shipment.inventory_units, "return_shipment_id")
                break
        }
        const inventoryUnit: any = _.find(inventoryUnits, ["id", Number(inventoryUnitId)])
        const product = lineItems[inventoryUnit.line_item_id]
        const images: GearImage = _.isArray(product.variant.images) ? _.first(product.variant.images) : product.variant.images

        const isGearBrandNameSupported = AppUtil.isGearBrandNameSupported(userContext)

        const result = {
            id: inventoryUnit.id,
            shipmentId: Number(shipmentId),
            productId: product.variant.product_id,
            line_item_id: product.id,
            size: product.variant.size,
            sku: product.variant.sku,
            title: isGearBrandNameSupported || product.variant.brand == null ? product.variant.name : `${product.variant.brand.toUpperCase()} ${product.variant.name}`,
            brand: product.variant.brand,
            optionsText: product.variant.options_text,
            inventoryStatus: inventoryUnit.status,
            price: {
                mrp: product.discount ? PriceUtil.formatMoney(product.display_price) : "",
                listingPrice: PriceUtil.formatMoney(product.total)
            },
            priceDetails: [
                {
                    title: "Price",
                    value: PriceUtil.formatMoney(product.display_price)
                },
                ...(product.discount
                    ? [
                        {
                            title: "Discount",
                            value: PriceUtil.formatMoney(product.discount),
                            isDiscount: true,
                        }
                    ]
                    : []),
                ...(product.fitcash_discount
                    ? [
                        {
                            title: "Fitcash Discount",
                            value: PriceUtil.formatMoney(product.fitcash_discount),
                            isDiscount: true,
                        }
                    ]
                    : []),
                {
                    title: "Total",
                    value: PriceUtil.formatMoney(product.total)
                }
            ],
            images: {
                mini_url: _.isEmpty(product.variant.images) ? "" : images.mini_url,
                product_url: _.isEmpty(product.variant.images) ? "" : images.product_url,
                small_url: _.isEmpty(product.variant.images) ? "" : images.small_url,
                large_url: _.isEmpty(product.variant.images) ? "" : images.large_url
            },
            pdpAction: {
                actionType: "NAVIGATION",
                title: "",
                url: BaseActionUtils.gearProduct(product.variant.product_id.toString(), undefined, AppUtil.isCultSportWebApp(userContext) ? product?.variant?.slug : undefined)
            }
        }
        return new InventoryUnitPriceCard(result)
    }
}

export class GearTrackingHeaderWidget {
    public widgetType: WidgetType = "GEAR_TRACKING_HEADER_WIDGET"
    private numberTitle: string = ""
    private number: string = ""
    private summary: string = ""
    private status: string = ""
    private shipmentHealth: string = ""
    private shipmentId: number
    private waybill: string
    private courierPartner: string = ""
    private action: Action

    constructor(trackingHeaderInfo: TrackShipmentHeader) {
        this.numberTitle = trackingHeaderInfo.numberTitle
        this.number = trackingHeaderInfo.number
        this.summary = trackingHeaderInfo.summary
        this.status = trackingHeaderInfo.status
        this.shipmentHealth = trackingHeaderInfo.shipmentHealth
        this.shipmentId = parseInt(trackingHeaderInfo.shipmentId)
        this.waybill = trackingHeaderInfo.waybill
        this.courierPartner = trackingHeaderInfo.courierPartner
        this.action = trackingHeaderInfo.action
    }
}

export class VerticalStepIndicatorWidget implements WidgetView {
    public widgetType: WidgetType = "VERTICAL_STEP_INDICATOR_WIDGET"
    private shipmentStatuses: GearShipmentStatus[]
    private shipmentMiniStates: GearTrackShipmentMiniStatesView

    constructor(shipmentStatusData: GearShipmentStatus[], shipmentMiniStates: GearTrackShipmentMiniStatesView) {
        this.shipmentStatuses = shipmentStatusData
        this.shipmentMiniStates = shipmentMiniStates
    }
}

export class InventoryUnitPriceCard implements WidgetView {
    public widgetType: WidgetType = "GEAR_INVENTORY_WITH_PRICE_WIDGET"
    private inventoryUnitData: InventoryUnit

    constructor(inventoryUnits: InventoryUnit) {
        this.inventoryUnitData = inventoryUnits
    }
}

export default GearShipmentViewBuilder
