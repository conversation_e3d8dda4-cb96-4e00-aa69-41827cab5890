import { Container, inject } from "inversify"
import { controller, httpPut, httpGet } from "inversify-express-utils"
import AuthMiddleware from "../../auth/AuthMiddleware"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { BASE_TYPES, Logger } from "@curefit/base"
import * as express from "express"
import {
    CancelInventoryUnitParam,
    GearCancelledInventoryUnitsResponse,
    GearInventoryUnit,
    ServiceabilityInfo,
    ServiceabilityType
} from "@curefit/gear-common"
import { ErrorFactory } from "@curefit/error-client"
import GearInventoryUnitViewBuilder, { GearCancelOrReturnSuccessView, ModifyOrderType } from "./GearInventoryUnitViewBuilder"
import { Order } from "@curefit/order-common"
import { GEARVAULT_CLIENT_TYPES, IGearService } from "@curefit/gearvault-client"
import { RUPEE_SYMBOL } from "@curefit/finance-common"
import { User } from "@curefit/user-common"
import { CacheHelper } from "../../util/CacheHelper"
import { PaymentUtil } from "@curefit/payment-models"
import IUserBusiness from "../../user/IUserBusiness"
import GearInventoryUnitCancelViewBuilder from "./GearInventoryUnitCancelViewBuilder"
import { ReturnOrReplaceItemView } from "./GearInventoryUnitViewBuilder"
import GearInventoryUnitViewBuilderV2 from "./GearInventoryUnitViewBuilderV2"
import { PaymentMode } from "@curefit/payment-common"
import { UserContext } from "@curefit/userinfo-common"
import { UserDeliveryAddress } from "@curefit/eat-common"
import * as _ from "lodash"
import { ErrorCodes } from "../../error/ErrorCodes"
import AppUtil from "../../util/AppUtil"
import { CFServiceInterfaces } from "../../page/vm/ServiceInterfaces"
import CultsportUtil from "../../util/CultsportUtil"
import { OMS_API_CLIENT_TYPES, IOrderService } from "@curefit/oms-api-client"

function controllerFactory(kernel: Container) {
    @controller("/gear/v2/inventory-units", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class GearInventoryUnitControllerV2 {
        constructor(
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(GEARVAULT_CLIENT_TYPES.GearService) private gearService: IGearService,
            @inject(OMS_API_CLIENT_TYPES.OrderService) private omsApiClient: IOrderService,
            @inject(CUREFIT_API_TYPES.UserBusiness) private userBusiness: IUserBusiness,
            @inject(CUREFIT_API_TYPES.GearInventoryUnitViewBuilder) private gearInventoryUnitViewBuilder: GearInventoryUnitViewBuilder,
            @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
            @inject(CUREFIT_API_TYPES.GearInventoryUnitCancelViewBuilder) private gearInventoryUnitCancelViewBuilder: GearInventoryUnitCancelViewBuilder,
            @inject(CUREFIT_API_TYPES.GearInventoryUnitViewBuilderV2) private gearInventoryUnitViewBuilderV2: GearInventoryUnitViewBuilderV2,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        ) { }

        @httpPut("/cancel-multiple")
        async cancelMultiple(req: express.Request): Promise<GearCancelOrReturnSuccessView> {
            const inventoryUnits: [CancelInventoryUnitParam] = req.body.inventoryUnits
            const orderId: string = req.body.orderId
            const userId: string = req.session.userId
            const user: User = await this.userCache.getUser(userId)
            const order: Order = await this.omsApiClient.getOrder(orderId)
            const isCodOrder = PaymentUtil.isCodOrder(order)
            const userContext: UserContext = req.userContext
            const gearInventoryUnits: GearInventoryUnit[] = await this.gearService.getInventoryUnitsForCancellation(inventoryUnits[0].id)
            const isReturnShipment: boolean = !!gearInventoryUnits[0].return_shipment_id
            const isReplacementShipment: boolean = gearInventoryUnits[0].is_replacement
            const inventoryUserId = (gearInventoryUnits[0] as any)?.user_id

            if ((order.userId !== userId) || (inventoryUserId !== userId)) {
                throw this.errorFactory.withCode(ErrorCodes.GEAR_NOT_AUTHORIZED_TO_MODIFY_ERR, 401).withDebugMessage("Not authorized to modify this order").build()
            }

            const gearCancelledInventoryUnitsResponse: GearCancelledInventoryUnitsResponse = await this.gearService.cancelMultipleInventoryUnits([inventoryUnits[0]], order.source)

            if (gearCancelledInventoryUnitsResponse.data && (gearCancelledInventoryUnitsResponse.data.refund_amount || gearCancelledInventoryUnitsResponse.data.refund_amount === 0)) {
                const title: string = "Cancelled successfully"
                const subtitle: string = !(isCodOrder || isReturnShipment || isReplacementShipment) ? `${RUPEE_SYMBOL}${gearCancelledInventoryUnitsResponse.data.refund_amount} will be refunded to your original payment method` : "Your order has been cancelled."
                if (AppUtil.isCultSportWebApp(userContext)) {
                    return this.gearInventoryUnitViewBuilder.buildCultsportItemCancelOrReturnSuccessViewV2(title, subtitle, ModifyOrderType.Cancel, CultsportUtil.getCancelAnalyticsEvent(gearInventoryUnits[0], order))
                }
                return this.gearInventoryUnitViewBuilder.buildItemCancelOrReturnSuccessView(title, subtitle)
            } else {
                this.logger.error(`Could not cancel order for orderId: ${orderId}, inventoryUnits: ${inventoryUnits}`)
                throw this.errorFactory.withCode(ErrorCodes.GEAR_ORDER_CANNOT_CANCEL_ERR, 500).withDebugMessage("Something went wrong, your order could not be cancelled").build()
            }
        }

        @httpGet("/return-replace/:id")
        async returnOrReplace(req: express.Request): Promise<ReturnOrReplaceItemView> {
            const userId: string = req.session.userId
            const inventoryUnitId: number = req.params.id
            const paymentMode: PaymentMode = req.params.paymentMode
            const userContext: UserContext = req.userContext
            const timezone = userContext.userProfile.timezone
            const addressId: string = req.query.addressId
            const inventoryUnit: GearInventoryUnit = await this.gearService.getInventoryUnitForReturn(inventoryUnitId)
            const inventoryUserId = (inventoryUnit as any)?.user_id || (inventoryUnit?.order as any)?.external_service_user_id

            if ((inventoryUserId !== userId)) {
                throw this.errorFactory.withCode(ErrorCodes.GEAR_NOT_AUTHORIZED_TO_MODIFY_ERR, 401).withDebugMessage("Not authorized to modify this order").build()
            }

            let address: UserDeliveryAddress = null
            let serviceabilityInfo: ServiceabilityInfo = null
            let pincode = null

            if (addressId) {
                address = await this.userBusiness.getAddress(userId, addressId)
                pincode = _.get(address, "structuredAddress.pincode")
            } else {
                pincode = _.get(inventoryUnit, "address.zipcode")
            }
            // Address serviceability for return / exchange.
            if (pincode) {
                serviceabilityInfo = await this.gearService.getOrderServiceability(pincode, null, [ServiceabilityType.EXCHANGE, ServiceabilityType.RETURNS], "" + inventoryUnitId,  userContext.userProfile.userId)
            }

            if (_.isEmpty(inventoryUnit)) {
                throw this.errorFactory.withCode(ErrorCodes.GEAR_ITEM_NOT_FOUND_ERR, 404).withDebugMessage("Your requested item could not be found").build()
            }

            if (!inventoryUnit.returnable && !inventoryUnit.replaceable) {
                this.logger.debug(`User ${userId} somehow landed to return page for inventoryUnit ${inventoryUnit}, throwing error`)
                throw this.errorFactory.withCode(ErrorCodes.GEAR_NOT_ALLOWED_TO_RETURN_ERR, 400).withDebugMessage("This operation is not allowed at this stage").build()
            }

            return this.gearInventoryUnitViewBuilderV2.buildReturnView(inventoryUnit, paymentMode, timezone, address, userContext, serviceabilityInfo)
        }
    }

    return GearInventoryUnitControllerV2
}

export default controllerFactory
