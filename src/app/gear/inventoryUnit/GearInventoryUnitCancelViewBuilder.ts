import { injectable } from "inversify"
import { Action, PriceDetailsWidget, ProductDetailPage, WidgetType, WidgetView } from "../../common/views/WidgetView"
import { GearInventoryUnit, GearOrderItem } from "@curefit/gear-common"
import { GearReason } from "@curefit/gear-common"
import { PriceComponent } from "../../order/OrderViewBuilder"
import * as _ from "lodash"
import { PriceUtil } from "@curefit/util-common"

export interface BaseGearOrderItemView extends ProductDetailPage {

}

export interface CancelOrderItemView extends BaseGearOrderItemView {

}

export interface GearReasonView {
    [key: string]: {
        title: string
        options: GearReason[]
    }
}

@injectable()
class GearInventoryUnitCancelViewBuilder {
    async buildCancelInventoryUnitView(gearInventoryUnits: GearInventoryUnit[], inventoryUnitId: number): Promise<CancelOrderItemView> {
        const widgetPromises: Promise<WidgetView>[] = []
        const isReturnShipment: boolean = !!gearInventoryUnits[0].return_shipment_id
        const isReplaceShipment: boolean = gearInventoryUnits[0].is_replacement
        const moreThanOneInventoryUnits = gearInventoryUnits.length > 1
        const cancellationReasons = gearInventoryUnits[0].cancel_reasons
        if (moreThanOneInventoryUnits) {
            const message: string = `Following ${gearInventoryUnits.length} items are packed together. You can cancel the entire shipment and not the individual items.`
            widgetPromises.push(Promise.resolve(new ShipmentCancellationMessageWidget(message)))
        }
        widgetPromises.push(this.getProductCardView(gearInventoryUnits))
        widgetPromises.push(this.getCancelOrderView(cancellationReasons))
        if (!(gearInventoryUnits[0].payment_method || isReturnShipment || isReplaceShipment)) {
            widgetPromises.push(this.getGearPriceDetailsView(gearInventoryUnits))
        }

        const widgets = await Promise.all(widgetPromises)

        let cancelTitle: string
        cancelTitle = moreThanOneInventoryUnits ? `Cancel ${gearInventoryUnits.length} Items` : "Confirm Cancellation"

        const actions: Action[] = [{
            actionType: "GEAR_ORDER_MODIFY_ACTION",
            title: cancelTitle,
            payload: {
                orderId: gearInventoryUnits[0].order.external_service_order_id,
                inventoryUnitId: Number(inventoryUnitId),
                shipmentId: isReturnShipment ? gearInventoryUnits[0].return_shipment_id : gearInventoryUnits[0].shipment_id,
                type: "cancel"
            }
        }]

        return { widgets, actions: actions }
    }

    private async getProductCardView(gearInventoryUnits: GearInventoryUnit[]): Promise<GearProductCardWidget> {
        const product: GearProduct[] = _.map(gearInventoryUnits, (inventoryUnit) => {
            return {
                id: inventoryUnit.product.id,
                name: inventoryUnit.product.name,
                price: PriceUtil.formatMoney(inventoryUnit.total_amount_including_fitcash),
                size: inventoryUnit.variant.size,
                image: inventoryUnit.variant.image
            }
        })

        return new GearProductCardWidget(product)
    }

    private async getCancelOrderView(cancellationReasons: GearReason[]): Promise<GearModifyOrderWidget> {
        const gearReasonView: GearReasonView = {
            default: {
                title: "Reason to cancel",
                options: cancellationReasons
            }
        }

        return new GearModifyOrderWidget(gearReasonView)
    }

    private async getGearPriceDetailsView(gearInventoryUnits: GearInventoryUnit[]): Promise<PriceDetailsWidget> {
        const priceDetails: PriceComponent[] = [{
            title: "Refund total",
            value: _.sum((_.map(gearInventoryUnits, "total_amount_including_fitcash").map(Number))).toString()
        }]

        return new PriceDetailsWidget(priceDetails, "GEAR_PRICE_DETAILS_WIDGET")
    }
}

export class GearProductCardWidget implements WidgetView {
    public widgetType: WidgetType = "GEAR_PRODUCT_CARD_WIDGET_V2"
    public productData: GearProduct[]

    constructor(products: GearProduct[]) {
        const productData = _.map(products, (product) => {
            return {
                id: product.id,
                name: product.name,
                price: PriceUtil.formatMoney(product.price),
                size: product.size,
                image: product.image,
                additionalInfo: product.additionalInfo
            }
        })
        this.productData = productData
    }
}

export class CultsportOrderStatusCardWidget implements WidgetView {
    public widgetType: WidgetType = "CULTSPORT_ORDER_STATUS_CARD_WIDGET"
    public title: string
    public imageUrl: string
    public action: Action
    public analyticsEventData?: any
    public meta: MetaInfo[]
    public text: string
    public subText: string
    constructor(title: string, imageUrl: string, action: Action, analyticsEventData?: any, text?: string, subText?: string, meta?: MetaInfo[]) {
        this.title = title
        this.imageUrl = imageUrl
        this.action = action
        this.analyticsEventData = analyticsEventData
        this.subText = subText
        this.meta = meta
        if (text) {
            this.text = text
        }
    }
}

export class ShipmentCancellationMessageWidget implements WidgetView {
    public widgetType: WidgetType = "GEAR_SHIPMENT_CANCELLATION_MESSAGE_WIDGET"
    public message: string

    constructor(message: string) {
        this.message = message
    }
}

export interface GearRequestType {
    text: "Return" | "Replacement"
    requestType: "return" | "replacement"
}

export class GearModifyOrderWidget implements WidgetView {
    public widgetType: WidgetType = "GEAR_MODIFY_ORDER_WIDGET"
    public defaultRequestType: string = "replacement"
    private reasons: GearReasonView
    private requestTypes: GearRequestType[]
    private requestTypeVisible: boolean

    constructor(reasons: GearReasonView, requestTypes: GearRequestType[] = undefined, requestTypeVisible: boolean = false) {
        this.reasons = reasons
        this.requestTypes = requestTypes
        this.requestTypeVisible = requestTypeVisible
    }
}

interface GearProduct {
    id: number
    name: string
    price: string
    size?: string
    image?: string
    orderLineItemId?: number
    inventoryUnitId?: number
    additionalInfo?: string
}

export interface MetaInfo {
    imageUrl: string
    subTitle: string
}

export default GearInventoryUnitCancelViewBuilder
