import * as _ from "lodash"
import { Container, inject } from "inversify"
import { controller, httpGet, httpPut } from "inversify-express-utils"
import AuthMiddleware from "../../auth/AuthMiddleware"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { Logger, BASE_TYPES } from "@curefit/base"
import * as express from "express"
import {
    CancelInventoryUnitParam,
    GearCancelledInventoryUnitsResponse,
    GearReason,
    GearRefundData,
    GearAddressUpdateDetails,
    ServiceabilityInfo,
    ServiceabilityType
} from "@curefit/gear-common"
import {
    ErrorFactory
} from "@curefit/error-client"
import GearInventoryUnitViewBuilder, {
    GearCancelOrReturnSuccessView,
    ModifyOrderType,
    ReturnOrReplaceItemView
} from "./GearInventoryUnitViewBuilder"
import { GearInventoryUnit } from "@curefit/gear-common"
import { UserDeliveryAddress } from "@curefit/eat-common"
import { Order } from "@curefit/order-common"
import { IGearService, GEARVAULT_CLIENT_TYPES } from "@curefit/gearvault-client"
import { RUPEE_SYMBOL } from "@curefit/finance-common"
import { User } from "@curefit/user-common"
import { CacheHelper } from "../../util/CacheHelper"
import { PaymentMode } from "@curefit/payment-common"
import { PaymentUtil } from "@curefit/payment-models"
import { UserContext } from "@curefit/userinfo-common"
import GearInventoryUnitCancelViewBuilder from "./GearInventoryUnitCancelViewBuilder"
import IUserBusiness from "../../user/IUserBusiness"
import { PAYMENT_TYPES, IPaymentClient } from "@curefit/payment-client"
import { ErrorCodes } from "../../error/ErrorCodes"
import { IMediaGatewayService, MEDIA_GATEWAY_CLIENT_TYPES } from "@curefit/media-gateway-js-client"
import AppUtil from "../../util/AppUtil"
import { CFServiceInterfaces } from "../../page/vm/ServiceInterfaces"
import CultsportUtil from "../../util/CultsportUtil"
import { OMS_API_CLIENT_TYPES, IOrderService } from "@curefit/oms-api-client"

function controllerFactory(kernel: Container) {
    @controller("/gear/v1/inventory-units", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class GearInventoryUnitController {
        constructor(
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(GEARVAULT_CLIENT_TYPES.GearService) private gearService: IGearService,
            @inject(OMS_API_CLIENT_TYPES.OrderService) private omsApiClient: IOrderService,
            @inject(CUREFIT_API_TYPES.UserBusiness) private userBusiness: IUserBusiness,
            @inject(CUREFIT_API_TYPES.GearInventoryUnitViewBuilder) private gearInventoryUnitViewBuilder: GearInventoryUnitViewBuilder,
            @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
            @inject(PAYMENT_TYPES.IPaymentClient) protected paymentClient: IPaymentClient,
            @inject(CUREFIT_API_TYPES.GearInventoryUnitCancelViewBuilder) private gearInventoryUnitCancelViewBuilder: GearInventoryUnitCancelViewBuilder,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
            @inject(MEDIA_GATEWAY_CLIENT_TYPES.IMediaGatewayService) private mediaGatewayClient: IMediaGatewayService,
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        ) { }

        @httpGet("/cancel-review/:inventoryUnitId")
        async cancelV2(req: express.Request): Promise<any> {
            const inventoryUnitId: number = req.params.inventoryUnitId
            const userContext = req.userContext as UserContext
            const userId: string = req.session.userId
            const gearInventoryUnits: GearInventoryUnit[] = await this.gearService.getInventoryUnitsForCancellation(inventoryUnitId)
            const orderId = gearInventoryUnits[0]?.order?.external_service_order_id
            const order = await this.fetchOrderWithValidation(orderId)
            const inventoryUserId = (gearInventoryUnits[0] as any)?.user_id
            if ((order.userId !== userId) || (inventoryUserId !== userId)) {
                throw this.errorFactory.withCode(ErrorCodes.GEAR_NOT_AUTHORIZED_TO_MODIFY_ERR, 401).withDebugMessage("Not authorized to modify this order").build()
            }
            return this.gearInventoryUnitCancelViewBuilder.buildCancelInventoryUnitView(gearInventoryUnits, inventoryUnitId)
        }

        @httpPut("/cancel-multiple")
        async cancelMultiple(req: express.Request): Promise<GearCancelOrReturnSuccessView> {
            const inventoryUnits: [CancelInventoryUnitParam] = req.body.inventoryUnits
            const orderId: string = req.body.orderId
            const userId: string = req.session.userId
            const user: User = await this.userCache.getUser(userId)
            const order: Order = await this.fetchOrderWithValidation(orderId)
            const isCodOrder = PaymentUtil.isCodOrder(order)
            const userContext: UserContext = req.userContext
            const gearInventoryUnits: GearInventoryUnit[] = await this.gearService.getInventoryUnitsForCancellation(inventoryUnits[0].id)
            const isReturnShipment: boolean = !!gearInventoryUnits[0].return_shipment_id
            const isReplacementShipment: boolean = gearInventoryUnits[0].is_replacement
            const inventoryUserId = (gearInventoryUnits[0] as any)?.user_id

            if ((order.userId !== userId) || (inventoryUserId !== userId)) {
                throw this.errorFactory.withCode(ErrorCodes.GEAR_NOT_AUTHORIZED_TO_MODIFY_ERR, 401).withDebugMessage("Not authorized to modify this order").build()
            }

            if (gearInventoryUnits[0].state === "shipped" || gearInventoryUnits[0].state === "return_approved") {
                this.logger.error(`Could not cancel order for orderId: ${orderId}, inventoryUnits: ${inventoryUnits}`)
                throw this.errorFactory.withCode(ErrorCodes.GEAR_ORDER_CANNOT_CANCEL_ERR, 500).withDebugMessage("Something went wrong, your order could not be cancelled").build()
            }

            const gearCancelledInventoryUnitsResponse: GearCancelledInventoryUnitsResponse = await this.omsApiClient.cancelMultipleInventoryUnits(orderId, [inventoryUnits[0]], user)

            if (gearCancelledInventoryUnitsResponse.data && (gearCancelledInventoryUnitsResponse.data.refund_amount || gearCancelledInventoryUnitsResponse.data.refund_amount === 0)) {
                const title: string = "Cancelled successfully"
                const subtitle: string = !(isCodOrder || isReturnShipment || isReplacementShipment) ? `${RUPEE_SYMBOL}${gearCancelledInventoryUnitsResponse.data.refund_amount} will be refunded to your original payment method` : "Your order has been cancelled."
                if (AppUtil.isCultSportWebApp(userContext)) {
                    return this.gearInventoryUnitViewBuilder.buildCultsportItemCancelOrReturnSuccessViewV2(title, subtitle, ModifyOrderType.Cancel)
                }
                return this.gearInventoryUnitViewBuilder.buildItemCancelOrReturnSuccessView(title, subtitle)
            } else {
                this.logger.error(`Could not cancel order for orderId: ${orderId}, inventoryUnits: ${inventoryUnits}`)
                throw this.errorFactory.withCode(ErrorCodes.GEAR_ORDER_CANNOT_CANCEL_ERR, 500).withDebugMessage("Something went wrong, your order could not be cancelled").build()
            }
        }

        @httpGet("/return-replace/:id")
        async returnOrReplace(req: express.Request): Promise<ReturnOrReplaceItemView> {
            const userId: string = req.session.userId
            const inventoryUnitId: number = req.params.id
            const paymentMode: PaymentMode = req.params.paymentMode
            const userContext: UserContext = req.userContext
            const timezone = userContext.userProfile.timezone
            const addressId: string = req.query.addressId
            const inventoryUnit: GearInventoryUnit = await this.gearService.getInventoryUnitForReturn(inventoryUnitId)
            const inventoryUserId = (inventoryUnit as any)?.user_id || (inventoryUnit?.order as any)?.external_service_user_id

            if ((inventoryUserId !== userId)) {
                throw this.errorFactory.withCode(ErrorCodes.GEAR_NOT_AUTHORIZED_TO_MODIFY_ERR, 401).withDebugMessage("Not authorized to modify this order").build()
            }

            let address: UserDeliveryAddress = null
            let serviceabilityInfo: ServiceabilityInfo = null
            let pincode = null

            if (addressId) {
                address = await this.userBusiness.getAddress(userId, addressId)
                pincode = _.get(address, "structuredAddress.pincode")
            } else {
                pincode = _.get(inventoryUnit, "address.zipcode")
            }
            // Address serviceability for return / exchange.
            if (pincode) {
                serviceabilityInfo = await this.gearService.getOrderServiceability(pincode, null, [ServiceabilityType.EXCHANGE, ServiceabilityType.RETURNS], "" + inventoryUnitId,  userContext.userProfile.userId )
            }
            if (_.isEmpty(inventoryUnit)) {
                throw this.errorFactory.withCode(ErrorCodes.GEAR_ITEM_NOT_FOUND_ERR, 404).withDebugMessage("Your requested item could not be found").build()
            }
            if (!inventoryUnit.returnable && !inventoryUnit.replaceable) {
                this.logger.debug(`User ${userId} somehow landed to return page for inventoryUnit ${inventoryUnit}, throwing error`)
                throw this.errorFactory.withCode(ErrorCodes.GEAR_NOT_ALLOWED_TO_RETURN_ERR, 400).withDebugMessage("This operation is not allowed at this stage").build()
            }

            return this.gearInventoryUnitViewBuilder.buildReturnView(inventoryUnit, paymentMode, timezone, address, userContext, serviceabilityInfo )
        }

        @httpPut("/return")
        async return(req: express.Request): Promise<GearCancelOrReturnSuccessView> {
            const inventoryUnits: [CancelInventoryUnitParam] = req.body.inventoryUnits
            const orderId: string = req.body.orderId
            const fundAccountId: string = req.body.fundAccountId
            const order: Order = await this.fetchOrderWithValidation(orderId)
            const userId: string = req.session.userId
            const user: User = await this.userCache.getUser(userId)
            const addressId: string = req.body.addressId
            const userContext: UserContext = req.userContext

            // validate Images
            if (inventoryUnits) {
                await Promise.all(inventoryUnits.map(async (inventoryUnit): Promise<void> => {
                    if (inventoryUnit?.reason?.images) {
                        await Promise.all(inventoryUnit.reason.images.map(async (imageUrl, index): Promise<void> => {
                            inventoryUnit.reason.images[index] = await this.mediaGatewayClient.validateAndGetDestinationUrl(imageUrl)
                        }))
                    }
                }))
            }

            const inventoryUnit: GearInventoryUnit = await this.gearService.getInventoryUnitForReturn(inventoryUnits[0].id)
            const inventoryUserId = (inventoryUnit as any)?.user_id || (inventoryUnit?.order as any)?.external_service_user_id

            if ((order.userId !== userId) || (inventoryUserId !== userId)) {
                throw this.errorFactory.withCode(ErrorCodes.GEAR_NOT_AUTHORIZED_TO_MODIFY_ERR, 401).withDebugMessage("Not authorized to modify this order").build()
            }

            if (PaymentUtil.isCodOrder(order) && _.isNil(fundAccountId)) {
                this.logger.error(`COD order return should not be called without fundAccountId, order: ${orderId}, inventoryUnits: ${JSON.stringify(inventoryUnits)}`)
                throw this.errorFactory.withCode(ErrorCodes.GEAR_ORDER_CANNOT_RETURN_ERR, 500).withDebugMessage("Something went wrong, your order could not be returned").build()
            }

            const gearPickupAddress: GearAddressUpdateDetails = await this.gearAddressUpdateDetails(userId, addressId)
            const refundData: GearRefundData = await this.gearService.returnInventoryUnits(order, [inventoryUnits[0]], user, gearPickupAddress)

            if (PaymentUtil.isCodOrder(order)) {
                await this.paymentClient.codAttachFundAccountToOrder(userId, orderId, fundAccountId, refundData.invoice_ids.map(String))
            }

            const title: string = "Return initiated"
            const subtitle: string = `Refund of ${RUPEE_SYMBOL}${refundData.refund_amount} will be initiated after successful pickup of the item.`
            if (AppUtil.isCultSportWebApp(userContext)) {
                return this.gearInventoryUnitViewBuilder.buildCultsportItemCancelOrReturnSuccessViewV2(title, subtitle, ModifyOrderType.Return)
            }
            return this.gearInventoryUnitViewBuilder.buildItemCancelOrReturnSuccessView(title, subtitle)
        }

        @httpPut("/replace")
        async replace(req: express.Request) {
            const userId = req.session.userId
            const inventoryUnits: [CancelInventoryUnitParam] = req.body.inventoryUnits
            const orderId: string = req.body.orderId
            const order: Order = await this.fetchOrderWithValidation(orderId)
            const user: User = await this.userCache.getUser(userId)
            const addressId: string = req.body.addressId
            const userContext: UserContext = req.userContext

            const inventoryUnit: GearInventoryUnit = await this.gearService.getInventoryUnitForReturn(inventoryUnits[0].id)
            const inventoryUserId = (inventoryUnit as any)?.user_id || (inventoryUnit?.order as any)?.external_service_user_id

            if ((order.userId !== userId) || (inventoryUserId !== userId)) {
                throw this.errorFactory.withCode(ErrorCodes.GEAR_NOT_AUTHORIZED_TO_MODIFY_ERR, 401).withDebugMessage("Not authorized to modify this order").build()
            }

            const gearPickupAddress: GearAddressUpdateDetails = await this.gearAddressUpdateDetails(userId, addressId)
            await this.gearService.replaceInventoryUnits(order, [inventoryUnits[0]], user, gearPickupAddress)
            const title: string = "Exchange initiated"
            const subtitle: string = "Your exchange is on it's way"
            if (AppUtil.isCultSportWebApp(userContext)) {
                return this.gearInventoryUnitViewBuilder.buildCultsportItemCancelOrReturnSuccessViewV2(title, subtitle, ModifyOrderType.Replace)
            }
            return this.gearInventoryUnitViewBuilder.buildItemCancelOrReturnSuccessView(title, subtitle)
        }

        private async gearAddressUpdateDetails(userId: string, addressId: string): Promise<GearAddressUpdateDetails> {
            let gearAddressUpdateDetails: GearAddressUpdateDetails = null

            if (addressId) {
                const userAddress: UserDeliveryAddress = await this.userBusiness.getAddress(userId, addressId)
                gearAddressUpdateDetails = {
                    address1: userAddress.addressLine1,
                    address2: userAddress.addressLine2,
                    city: userAddress.structuredAddress?.city,
                    state: userAddress.structuredAddress?.state,
                    pincode: userAddress.structuredAddress?.pincode,
                    external_service_address_id: userAddress.addressId,
                    floor_number: userAddress?.gearDeliveryInfo?.floorNumber,
                    is_service_lift_available: userAddress?.gearDeliveryInfo?.isServiceLiftPresent
                } as any
            }

            return gearAddressUpdateDetails
        }

        private async fetchOrderWithValidation(orderId: string) {
            const order = await this.omsApiClient.getOrder(orderId)
            if (_.isNil(order)) {
                this.logger.debug(`Order not found: ${orderId}`)
                throw this.errorFactory.withCode(ErrorCodes.ORDER_DOES_NOT_EXIST_ERR, 400).withDebugMessage("Order does not exist for orderId: " + orderId).build()
            }
            return order
        }
    }

    return GearInventoryUnitController
}

export default controllerFactory
