import { inject, injectable } from "inversify"
import {
    Action,
    AlertInfo,
    GearModifyPickupAddressWidget,
    GearOrderDetailsWidget,
    PriceDetailsWidget,
    ProductDetailPage,
    WidgetView
} from "../../common/views/WidgetView"
import { GearInventoryUnit, ServiceabilityInfo } from "@curefit/gear-common"
import {
    GearModifyOrderWidget,
    GearProductCardWidget,
    GearReasonView,
    GearRequestType
} from "../orderItem/GearOrderItemViewBuilder"
import { GearProduct, GearReason } from "@curefit/gear-common"
import { PriceComponent } from "../../order/OrderViewBuilder"
import { PaymentMode } from "@curefit/payment-common"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { UserContext } from "@curefit/vm-models"
import AppUtil from "../../util/AppUtil"
import { UserDeliveryAddress } from "@curefit/eat-common"
import { GearService, GEARVAULT_CLIENT_TYPES } from "@curefit/gearvault-client"
import { TypographyWidget } from "../../page/PageWidgets"
import { GEAR_RETURN_EXCHANGE_CHECKBOX_TEXT, GEAR_RETURN_EXCHANGE_POLICY_TEXT } from "../constants"

export interface BaseGearInventoryUnitView extends ProductDetailPage {

}

export interface GearCancelOrReturnSuccessView extends BaseGearInventoryUnitView {

}

export interface ReturnOrReplaceItemView extends BaseGearInventoryUnitView {

}

@injectable()
class GearInventoryUnitViewBuilderV2 {
    constructor(
        @inject(GEARVAULT_CLIENT_TYPES.GearService) private gearService: GearService
    ) {
    }

    async buildItemCancelOrReturnSuccessView(title: string, subtitle: string): Promise<GearCancelOrReturnSuccessView> {
        const widgets: WidgetView[] = []

        const alertInfo: AlertInfo = {
            title: title,
            subTitle: subtitle,
            actions: [{
                actionType: "HIDE_ALERT_MODAL",
                title: "Ok"
            }]
        }

        return { widgets, alertInfo }
    }

    getCheckBoxWidget(): Promise<WidgetView> {
        return Promise.resolve({
            widgetType: "GEAR_AGREE_CHECKBOX_WIDGET",
            action: {
                actionType: "UPDATE_GEAR_MODIFY_ORDER_AGREE"
            },
            title: GEAR_RETURN_EXCHANGE_CHECKBOX_TEXT,
            subtitle: undefined

        })
    }

    async getReverseQCTermsWidget(): Promise<WidgetView> {
        return {
            widgetType: "FITCLUB_CHECKOUT_SAVINGS_WIDGET",
            title: GEAR_RETURN_EXCHANGE_POLICY_TEXT,
        }
    }

    async buildReturnView(inventoryUnit: GearInventoryUnit, paymentMode: PaymentMode, timezone: Timezone, address: UserDeliveryAddress, userContext: UserContext, serviceabilityInfo: ServiceabilityInfo): Promise<ReturnOrReplaceItemView> {
        const widgetPromises: Promise<WidgetView>[] = []
        let {exchange = false, returns = false} = serviceabilityInfo || {}

        if (!inventoryUnit?.replaceable) {
            exchange = false
        }

        if (!inventoryUnit?.returnable) {
            returns = false
        }

        if (exchange || returns ) {
            widgetPromises.push(this.getReverseQCTermsWidget())
        }
        widgetPromises.push(this.getProductCardView(inventoryUnit, timezone, userContext))
        widgetPromises.push(this.getGearModifyPickupAddressView(inventoryUnit, address))
        if (exchange || returns ) {
            widgetPromises.push(this.getReturnReplaceOrderView(inventoryUnit, paymentMode, serviceabilityInfo))
        } else {
            widgetPromises.push(this.getNoServiceabilityText())
        }
        if (returns) {
            widgetPromises.push(this.getCheckBoxWidget())
        }
        widgetPromises.push(this.getGearPriceDetailsView(inventoryUnit))

        // xor on returns & exchange. Enable only when one of them is enabled
        // and not when both are.
        // if (!(exchange && returns) && (exchange || returns)) {
        //     widgetPromises.push(this.getNoServiceabilityText(exchange ? "returns" : "exchange"))
        // }

        const widgets = await Promise.all(widgetPromises)
        const actions: Action[] = []

        if (exchange || returns) {
            actions.push({
                actionType: "GEAR_ORDER_MODIFY_ACTION",
                title: `Confirm ${exchange ? "Exchange" : "Return"}`,
                payload: {
                    addressId: address && address.addressId,
                    orderId: inventoryUnit.order.external_service_order_id,
                    inventoryUnitId: inventoryUnit.id,
                    type: "replacement", // this will always be replacement, ideally server should not send this
                    meta: {
                        razorpayAccountId: AppUtil.getRazorpayId()
                    }
                }
            })
        }

        return { widgets, actions }
    }

    private async getProductCardView(inventoryUnit: GearInventoryUnit, timezone: Timezone, userContext: UserContext): Promise<GearProductCardWidget> {
        const isGearBrandNameSupported = AppUtil.isGearBrandNameSupported(userContext)

        const product: GearProduct = {
            id: inventoryUnit.product.id,
            name: isGearBrandNameSupported || inventoryUnit.product.brand == null ? inventoryUnit.product.name : `${inventoryUnit.product.brand.toUpperCase()} ${inventoryUnit.product.name}`,
            brand: inventoryUnit.product.brand,
            price: inventoryUnit.total_amount_including_fitcash,
            size: inventoryUnit.variant.size,
            image: inventoryUnit.variant.image,
            additionalInfo: this.getAdditionalInfo(inventoryUnit, timezone, userContext)
        }

        return new GearProductCardWidget(product)
    }


    private getAdditionalInfo(inventoryUnit: GearInventoryUnit, timezone: Timezone, userContext: UserContext) {

        if (inventoryUnit?.returnable) {
            return `Returnable till ${TimeUtil.formatDateInTimeZone(timezone, TimeUtil.parseDate(inventoryUnit.returnable_by, timezone), "Do MMMM")}`
        }

        if (inventoryUnit?.replaceable) {
            return `Exchangeable till ${TimeUtil.formatDateInTimeZone(timezone, TimeUtil.parseDate((inventoryUnit as any).replaceable_by, timezone), "Do MMMM")}`
        }

        return undefined

    }

    private async getReturnReplaceOrderView(inventoryUnit: GearInventoryUnit, paymentMode: PaymentMode, serviceabilityInfo: ServiceabilityInfo): Promise<GearModifyOrderWidget> {
        const replacementReasons: GearReason[] = await this.gearService.getReplacementReasonsWithSubReasons(inventoryUnit)
        const returnReasons: GearReason[] = inventoryUnit.return_reasons
        let  {exchange = false, returns= false } = serviceabilityInfo || {}

        if (!inventoryUnit?.replaceable) {
            exchange = false
        }

        if (!inventoryUnit?.returnable) {
            returns = false
        }

        const gearReasonView: GearReasonView = {
           ...(exchange && { replacement: {
            title: "Reason to exchange",
            options: replacementReasons
            }}),
           ...(returns && { return: {
            title: "Reason to return",
            options: returnReasons
        }})
        }

        const requestTypes: GearRequestType[] = []
        if (exchange) {
            requestTypes.push({
                text: "Exchange",
                requestType: "replacement"
            })
        }

        if (returns) {
            requestTypes.push({
                text: "Return",
                requestType: "return"
            })
        }

        return new GearModifyOrderWidget(gearReasonView, requestTypes, true, serviceabilityInfo.exchange ? "replacement" : "return")
    }

    private async getGearPriceDetailsView(inventoryUnit: GearInventoryUnit): Promise<PriceDetailsWidget> {
        const priceDetails: PriceComponent[] = [{
            title: "Refund Total",
            value: (inventoryUnit.total_amount_including_fitcash).toString()
        }]

        return new PriceDetailsWidget(priceDetails, "GEAR_PRICE_DETAILS_WIDGET")
    }

    private async getGearModifyPickupAddressView(inventoryUnit: GearInventoryUnit,
                                                 address: UserDeliveryAddress): Promise<GearModifyPickupAddressWidget> {
        return new GearModifyPickupAddressWidget(inventoryUnit, address)
    }

    private async getNoServiceabilityText(type?: "returns" | "exchange"): Promise<TypographyWidget> {
        const serviceabilityType = type ? type : "returns / exchanges"
        return {
            widgetType: "TYPOGRAPHY_WIDGET",
            tagColor: "#6ebcff",
            data: [{
                text: `We are unable to process ${serviceabilityType} in your location.`,
                fontColor: "#fc2e71",
                fontSize: 15
            }, {
                text: "",
                fontColor: "#fc2e71",
                fontSize: 15
            }, {
                text: `Please check back later. Additionally, we will notify you once we have opened up returns & exchanges for your pincode.`,
                fontColor: "#fc2e71",
                fontSize: 15
            }],
            dividerType: "SMALL"
        }
    }
}

export default GearInventoryUnitViewBuilderV2
