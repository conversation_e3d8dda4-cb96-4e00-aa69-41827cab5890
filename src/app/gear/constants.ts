import { ProductProperty } from "@curefit/gear-common"

// todo: this config should stay in the offer console
export const COUPON_DISPATCH_DELAY = 30

export const FREE_DELIVERY = "Free delivery within 5-7 days"
export const RETURN_POLICY_TEXT = "{DAYS} day return and exchange"
export const SLA_INSTALLATION_TEXT_WITHOUT_PINCODE = "Door-step installation by cultsport team after delivery"
export const CASH_ON_DELIVERY_NO_CONTACT =
  "Pay on Delivery is not available"
export const LOCATION_EMPTY_STRING = "Cash on delivery available on selected pincodes. Please enter your pincode to check availability"
export const UNSERVICEABLE_LOCATION = "Address not serviceable"
export const COVID_RESTRICTIONS =
  "Certain locations might be unserviceable due to restrictions imposed in our fight against the ongoing COVID-19 pandemic."
export const NO_RETURN_EXCHANGE = "Not eligible for return or exchange"

export const ONLY_RETURN = "Easy {DAYS} days return available"
export const NO_RETURN = "No returns available"
export const ONLY_EXCHANGE = "{DAYS} days exchange available"
export const NO_EXCHANGE = "No exchange available"

export const GEAR_ANNOTATION_BORDER_COLOR = "#E3E2FC"
export const GEAR_ANNOTATION_COLOR = "#736CF0"
export const GEAR_ANNOTATION_BG_COLOR = "#E3E2FC"

export const STORE_TAB_APP_SUPPORT_EXTERNAL = 8.51
export const STORE_TAB_APP_SUPPORT_INTERNAL = 8.49
export const STORE_TAB_CP_IOS = 402
export const STORE_TAB_CP_ANDROID = 427
const GEAR_TRACK_ORDER_ISSUE_ID = "329"
export const BLACKLISTED_ISSUES_FOR_WEB = [GEAR_TRACK_ORDER_ISSUE_ID]
export const GEAR_RETURN_EXCHANGE_CHECKBOX_TEXT = "I confirm that the product is unused with the original tags intact"
export const GEAR_RETURN_EXCHANGE_POLICY_TEXT = "Please note that the product(s) will go through a quality check for processing the return"
export const GEAR_RETURN_EXCHANGE_PARCEL_PACKAGING_TEXT = "Please ensure the parcel is prepared in its original packaging with all tags intact"
export const GEAR_RETURN_EXCHANGE_PARCEL_PICKUP_TEXT = "Our team will arrange a pickup within 4-5 working days"
export const GEAR_RETURN_REFUND_PROCESS_TEXT = "Your refund will be processed within 4-5 working days post order pickup"
export const GEAR_EXCHANGE_ORDER_UPDATE_TEXT = "We will deliver your updated order"
export const RETURN_EXCHANGE_REDIRECT_TEXT = "Refer to the order details page for tracking, pickup, and exchange information."

const YOGA_MAT_ICON_URL = "https://cdn-images.cure.fit/www-curefit-com/image/upload/cultgear-content/assets/gear-yoga-mat.png"
const EQUIPMENT_ICON_URL = "https://cdn-images.cure.fit/www-curefit-com/image/upload/cultgear-content/assets/gear-equipment-icon.png"

const GEAR_CROSS_SELL_MAP_STAGE = {
  YOGA: {
    collectionId: "training",
    title: "Power-up your workouts with ultra-light Yoga Mats by cultsport",
    icon: YOGA_MAT_ICON_URL
  },
  STRENGTH: {
    collectionId: "premium",
    title: "Power-up your workouts with home gym equipment by cultsport",
    icon: EQUIPMENT_ICON_URL,
  }
}
const GEAR_CROSS_SELL_MAP_PRODUCTION = {
  YOGA: {
    collectionId: "yoga-mats",
    title: "Power-up your workouts with ultra-light Yoga Mats by cultsport",
    icon: YOGA_MAT_ICON_URL
  },
  STRENGTH: {
    collectionId: "equipments",
    title: "Power-up your workouts with home gym equipment by cultsport",
    icon: EQUIPMENT_ICON_URL,
  }
}

export const GEAR_CROSS_SELL_MAP = process.env.ENVIRONMENT === "PRODUCTION" || process.env.ENVIRONMENT === "ALPHA"
  ? GEAR_CROSS_SELL_MAP_PRODUCTION
  : GEAR_CROSS_SELL_MAP_STAGE


export function getAllowedProductProperties(): ProductProperty[] {
    return [
      ProductProperty.COUNTRY_OF_ORIGIN
    ]
}

