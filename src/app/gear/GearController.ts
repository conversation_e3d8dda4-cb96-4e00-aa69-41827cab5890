import { <PERSON>rrorFactory, HTTP_CODE } from "@curefit/error-client"
import { ILocationService, LOCATION_SERVICE_TYPES } from "@curefit/location-service"
import { UserContext } from "@curefit/userinfo-common"
import * as express from "express"
import { Container, inject } from "inversify"
import { controller, httpGet } from "inversify-express-utils"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import AuthMiddleware from "../auth/AuthMiddleware"
import AppUtil from "../util/AppUtil"

function controllerFactory(kernel: Container) {
    @controller("/gear", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class GearController {
        constructor(
            @inject(LOCATION_SERVICE_TYPES.LocationService) private locationService: ILocationService,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: <PERSON>rrorFactory,
        ) { }

        @httpGet("/pincode/:pincode")
        async track(req: express.Request): Promise<any> {
            const userContext = req.userContext as UserContext
            if (AppUtil.isCultSportWebApp(userContext) && req.params.pincode && !isNaN(req.params.pincode) && req.params.pincode.length === 6) {
                return await this.locationService.getPlaceFromPincode(req.params.pincode, "GEAR")
            }
            throw this.errorFactory.withCode("Invalid Pincode", HTTP_CODE.BAD_REQUEST).build()
        }
    }

    return GearController
}

export default controllerFactory
