import { Container, inject } from "inversify"
import { controller, httpGet } from "inversify-express-utils"
import * as express from "express"

import { Logger, BASE_TYPES } from "@curefit/base"

import AuthMiddleware from "../../auth/AuthMiddleware"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import GearOrderItemViewBuilder from "./GearOrderItemViewBuilder"
import { GearOrderItem } from "@curefit/gear-common"
import { GearService, GEARVAULT_CLIENT_TYPES } from "@curefit/gearvault-client"
import * as _ from "lodash"
import { ErrorCodes } from "../../error/ErrorCodes"
import { UserContext } from "@curefit/userinfo-common"
import { ErrorFactory } from "@curefit/error-client"
import { OMS_API_CLIENT_TYPES, IOrderService } from "@curefit/oms-api-client"

function controllerFactory(kernel: Container) {
    @controller("/gear/v1/order-items", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class GearOrderItemController {
        constructor(
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(GEARVAULT_CLIENT_TYPES.GearService) private gearService: GearService,
            @inject(OMS_API_CLIENT_TYPES.OrderService) protected omsApiClient: IOrderService,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
            @inject(CUREFIT_API_TYPES.GearOrderItemViewBuilder) private gearOrderItemViewBuilder: GearOrderItemViewBuilder
        ) { }

        @httpGet("/cancel/:id")
        async cancel(req: express.Request): Promise<any> {
            const orderItemId: number = req.params.id
            const userContext = req.userContext as UserContext
            const gearOrderItem: GearOrderItem = await this.gearService.getOrderItemForCancellation(orderItemId)
            const order = await this.fetchOrderWithValidation(gearOrderItem?.order?.external_service_order_id)
            if (order.userId != userContext?.userProfile?.userId) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_AUTHORIZED_TO_VIEW_ORDER, 401).withDebugMessage("Not authorized to view order").build()
            }
            return this.gearOrderItemViewBuilder.buildCancelOrderItemView(gearOrderItem, req.userContext)
        }

        private async fetchOrderWithValidation(orderId: string) {
            const order = await this.omsApiClient.getOrder(orderId)
            if (_.isNil(order)) {
                throw this.errorFactory.withCode(ErrorCodes.ORDER_DOES_NOT_EXIST_ERR, 400).withDebugMessage("Order does not exist for orderId: " + orderId).build()
            }
            return order
        }
    }

    return GearOrderItemController
}

export default controllerFactory
