import { injectable } from "inversify"
import { Action, PriceDetailsWidget, ProductDetailPage, WidgetType, WidgetView } from "../../common/views/WidgetView"
import { GearOrderItem } from "@curefit/gear-common"
import { GEAR_CANCELLATION_REASONS, GearProduct, GearReason } from "@curefit/gear-common"
import { PriceComponent } from "../../order/OrderViewBuilder"
import { UserContext } from "@curefit/vm-models"
import AppUtil from "../../util/AppUtil"

export interface BaseGearOrderItemView extends ProductDetailPage {

}

export interface CancelOrderItemView extends BaseGearOrderItemView {

}

export interface GearReasonView {
    [key: string]: {
        title: string
        options: GearReason[]
    }
}

@injectable()
class GearOrderItemViewBuilder {
    async buildCancelOrderItemView(gearOrderItem: GearOrderItem, userContext: UserContext): Promise<CancelOrderItemView> {
        const widgetPromises: Promise<WidgetView>[] = []

        widgetPromises.push(this.getProductCardView(gearOrderItem, userContext))
        widgetPromises.push(this.getCancelOrderView())
        widgetPromises.push(this.getGearPriceDetailsView(gearOrderItem))

        const widgets = await Promise.all(widgetPromises)

        const actions: Action[] = [{
            actionType: "GEAR_ORDER_MODIFY_ACTION",
            title: "Confirm Cancellation",
            payload: {
                orderId: gearOrderItem.order.external_service_order_id,
                inventoryUnitId: gearOrderItem.inventoryUnit.id,
                type: "cancel"
            }
        }]

        return {widgets, actions: actions}
    }

    private async getProductCardView(gearOrderItem: GearOrderItem, userContext: UserContext): Promise<GearProductCardWidget> {
        const isGearBrandNameSupported = AppUtil.isGearBrandNameSupported(userContext)
        const product: GearProduct = {
            id: gearOrderItem.product.id,
            name: isGearBrandNameSupported || gearOrderItem.product.brand == null ? gearOrderItem.product.name : `${gearOrderItem.product.brand.toUpperCase()} ${gearOrderItem.product.name}`,
            brand: gearOrderItem.product.brand,
            price: gearOrderItem.total_amount_including_fitcash,
            size: gearOrderItem.variant.size,
            image: gearOrderItem.variant.image,
        }

        return new GearProductCardWidget(product, gearOrderItem.refundable_amount)
    }

    private async getCancelOrderView(): Promise<GearModifyOrderWidget> {
        const gearReasonView: GearReasonView = {
            default: {
                title: "Reason to cancel",
                options: GEAR_CANCELLATION_REASONS
            }
        }

        return new GearModifyOrderWidget(gearReasonView, undefined, false, "cancel")
    }

    private async getGearPriceDetailsView(gearOrderItem: GearOrderItem): Promise<PriceDetailsWidget> {
        const priceDetails: PriceComponent[] = [{
            title: "Refund total",
            value: gearOrderItem.refundable_amount.toString()
        }]

        return new PriceDetailsWidget(priceDetails, "GEAR_PRICE_DETAILS_WIDGET")
    }
}

export class GearProductCardWidget implements WidgetView {
    public widgetType: WidgetType = "GEAR_PRODUCT_CARD_WIDGET"
    private id: number
    private name: string
    private brand: string
    private price: number
    private size: string
    private image: string
    private additionalInfo: string
    private amountPaid: number

    constructor(product: GearProduct, amountPaid?: number) {
        this.id = product.id
        this.name = product.name
        this.price = product.price
        this.amountPaid = amountPaid
        this.size = product.size
        this.image = product.image
        this.brand = product.brand
        this.additionalInfo = product.additionalInfo
    }
}

export interface GearRequestType {
    text: "Return" | "Exchange"
    requestType: "return" | "replacement"
}

export class GearModifyOrderWidget implements WidgetView {
    public widgetType: WidgetType = "GEAR_MODIFY_ORDER_WIDGET"
    public defaultRequestType: string
    private reasons: GearReasonView
    private requestTypes: GearRequestType[]
    private requestTypeVisible: boolean

    constructor(reasons: GearReasonView, requestTypes: GearRequestType[] = undefined, requestTypeVisible: boolean = false, defaultRequestType = "replacement") {
        this.reasons = reasons
        this.requestTypes = requestTypes
        this.requestTypeVisible = requestTypeVisible
        this.defaultRequestType = defaultRequestType
    }
}

export default GearOrderItemViewBuilder
