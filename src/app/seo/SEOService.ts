import { ISEOService, SEOClient } from "./ISEOService"
import { inject, injectable } from "inversify"
import { ISEOReadOnlyDao, VM_MODELS_TYPES, ISEOLinksReadOnlyDao, ISEOFaqReadOnlyDao, ISEOFooterReadOnlyDao } from "@curefit/vm-models"
import { Logger, BASE_TYPES } from "@curefit/base"
import { ISEOFaq, ISEOFooter, ISEOLinks, SEO } from "@curefit/vm-common"
import { ICrudKeyValue, IMultiCrudKeyValue, REDIS_TYPES } from "@curefit/redis-utils"

const REDIS_PREFIX = {
    seoMeta: "seo-page-meta",
    seoLinks: "seo-links",
    seoFooter: "seo-footer",
    seoFaq: "seo-faq"
}

@injectable()
class SEOService implements ISEOService {
    private redisCrudDao: ICrudKeyValue

    constructor(
        @inject(VM_MODELS_TYPES.SEOReadOnlyDao) private seoDao: ISEOReadOnlyDao,
        @inject(VM_MODELS_TYPES.SEOLinksReadOnlyDao) private seoLinksDao: ISEOLinksReadOnlyDao,
        @inject(VM_MODELS_TYPES.SEOFaqReadOnlyDao) private faqDao: ISEOFaqReadOnlyDao,
        @inject(VM_MODELS_TYPES.SEOFooterReadOnlyDao) private seoFooterDao: ISEOFooterReadOnlyDao,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(REDIS_TYPES.MultiCrudKeyValueDao) private multiCrudKeyValueDao: IMultiCrudKeyValue,
    ) {
        this.redisCrudDao = this.multiCrudKeyValueDao.getICrudKeyValue("CFAPI-CACHE")
    }

    async getDataFromRedis<T>(redisPrefix: string, key: string): Promise<T> {
        const redisKey = `${redisPrefix}::${key}`
        let redisData: T

        try {
            redisData = JSON.parse(await this.redisCrudDao.read(redisKey))
        } catch (err) {
            this.logger.info(`Redis fetch error for key: ${redisKey}, ${err}`)
        }

        return redisData
    }

    async setDataInRedis(redisPrefix: string, key: string, data: SEO | ISEOLinks | ISEOFaq | ISEOFooter) {
        const redisKey = `${redisPrefix}::${key}`

        if (!data) {
            return
        }

        try {
            this.redisCrudDao.create(redisKey, JSON.stringify(data))
        } catch (error) {
            this.logger.info(`Redis set error for key: ${redisKey}, ${error}`)
        }
    }

    async getSeoMetaByPageId(pageId: string): Promise<SEOClient> {
        const redisPrefix = REDIS_PREFIX.seoMeta
        let pageData: SEOClient = await this.getDataFromRedis(redisPrefix, pageId)

        if (pageData) {
            return pageData
        }

        pageData = await this.seoDao.findOne({ seoPageId: pageId }) as SEOClient
        this.setDataInRedis(redisPrefix, pageId, pageData)

        return pageData
    }

    async getSeoLinksDataById(linkId: string): Promise<ISEOLinks> {
        const redisPrefix = REDIS_PREFIX.seoLinks
        let linksData: ISEOLinks = await this.getDataFromRedis(redisPrefix, linkId)

        if (linksData) {
            return linksData
        }

        linksData = await this.seoLinksDao.findOne({ seoLinksId: linkId })
        this.setDataInRedis(redisPrefix, linkId, linksData)

        return linksData
    }

    async getSeoFaqDataById(faqId: string): Promise<ISEOFaq> {
        const redisPrefix = REDIS_PREFIX.seoFaq
        let faqData: ISEOFaq = await this.getDataFromRedis(redisPrefix, faqId)

        if (faqData) {
            return faqData
        }

        faqData = await this.faqDao.findOne({ faqId: faqId })
        this.setDataInRedis(redisPrefix, faqId, faqData)

        return faqData
    }

    async getSeoFooterDataById(footerId: string): Promise<ISEOFooter> {
        const redisPrefix = REDIS_PREFIX.seoFooter
        let footerData: ISEOFooter = await this.getDataFromRedis(redisPrefix, footerId)

        if (footerData) {
            return footerData
        }

        footerData = await this.seoFooterDao.findOne({ seoFooterId: footerId})
        this.setDataInRedis(redisPrefix, footerId, footerData)

        return footerData
    }

    /**
     * This will get the meta data for a page by Id. This would check in cache first and if not found gets from DB.
     * @param id : This represents the page id or product id of every page. The product Id should be unique
     */
    async getMetaDataById(id: string): Promise<SEOClient> {
        let pageData: SEOClient

        try {
            pageData = await this.getSeoMetaByPageId(id) as SEOClient

            if (pageData?.seoLinksId) {
                pageData["seoLinks"] = await this.getSeoLinksDataById(pageData?.seoLinksId)
            }

            if (pageData?.faqId) {
                pageData["seoFaq"] = await this.getSeoFaqDataById(pageData.faqId)
            }

            if (pageData.seoFooterId) {
                pageData["seoFooter"] = await this.getSeoFooterDataById(pageData.seoFooterId)
            }
        } catch (err) {
            this.logger.error("Error while fetching seo data: ", err)
        }

        return pageData
    }
}
export default SEOService
