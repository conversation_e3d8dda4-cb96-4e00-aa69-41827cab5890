import {
    ChronicCareConsultationRescheduleConfirmationView,
    TeleconsultationSingleConfirmationViewV2
} from "./../order/OrderConfirmationViewBuilder"
import { CareWidgetUtil, ISEODataReadOnlyDao, VM_MODELS_TYPES } from "@curefit/vm-models"
import * as express from "express"
import { createHmac } from "crypto"
import { Container, inject } from "inversify"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import * as _ from "lodash"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { PaymentData, PrePaymentData } from "@curefit/payment-common"
import { Order } from "@curefit/order-common"
import {
    ConsultationProduct,
    DiagnosticProduct,
    HealthfaceTenant,
    IContentSection,
    IDiagnosticSlugResponse,
    ISlugMappingResponse,
    TRANSFORM_SUB_CATEGORY_CODE_TYPE
} from "@curefit/care-common"
import { DeliveryArea, FoodProduct as Product } from "@curefit/eat-common"
import { InstructionItem, ProductPrice } from "@curefit/product-common"
import { LatLong, OrderSource, UserAgentType as UserAgent } from "@curefit/base-common"
import { Session, SessionInfo, UserContext } from "@curefit/userinfo-common"
import { User } from "@curefit/user-common"
import { IRefundOrderResponse, OfferHelper, OMS_API_CLIENT_TYPES, OrderCreate, IOrderService } from "@curefit/oms-api-client"
import {
    AccessPolicy,
    ActiveBundleOrderDetail,
    AgentRankingScheme,
    ALBUS_CLIENT_TYPES,
    BookingDetail,
    BundleSellableProduct,
    CATEGORY_CODE,
    Center,
    ChatHistory,
    ChatHistoryResponse,
    ChatListHistoryResponse,
    ClientChatMessage,
    Consultation,
    ConsultationInstructionResponse,
    ConsultationOrderResponse,
    ConsultationSellableProduct,
    ConsultationSellableProductResponse,
    ConsumedMessageRequest,
    DiagnosticAtHomeInventoryRequest,
    DiagnosticAvailableSlotsResponse,
    DiagnosticEmailedTestReportResponse,
    DiagnosticInCenterInventoryRequest,
    DiagnosticReport,
    DiagnosticReportV2,
    DiagnosticsTestOrderResponse,
    Doctor,
    DOCTOR_TYPE,
    DoctorRecommendationRequest,
    GCInventoryRequest,
    HealthfaceProductInfo,
    ICovidMetricsInfoResponse,
    IHealthfaceService,
    INVENTORY_TYPE,
    MeetingEvent,
    MRNFormResponse,
    MultiConsultationRequest,
    MultiConsultationRescheduleRequest,
    OrderEvent,
    PackageProduct,
    Patient,
    PatientPreference,
    PractoConfirmBookingRequest,
    PractoCreateBookingRequest,
    PractoRescheduleBookingRequest,
    SoftBookingRequest,
    SoftBookingResponse,
    SPECIALITY_CODE,
    SUB_CATEGORY_CODE,
    TCAvailableSlotsResponse,
    TCBookingRequest,
    TCInventoryRequest,
    TestBookingRequest,
    TestType,
    TwilioAccessToken,
    VitalInfoItem
} from "@curefit/albus-client"
import { ManageOptionPayload, ProductDetailPage } from "../common/views/WidgetView"
import { EHR_CLIENT_TYPES, ISubSectionDataService } from "@curefit/ehr-client"
import { FEEDBACK_MONGO_TYPES, IFeedbackReadOnlyDao } from "@curefit/feedback-mongo"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { DELIVERY_CLIENT_TYPES, IDeliveryAreaService } from "@curefit/delivery-client"
import { BASE_TYPES, Logger } from "@curefit/base"
import AuthMiddleware from "../auth/AuthMiddleware"
import ICRMIssueService from "../crm/ICRMIssueService"
import { ConfirmationView } from "../order/BaseOrderConfirmationViewBuilder"
import {
    ChronicCareDiagnosticsRescheduleConfirmationView,
    DiagnosticSingleConfirmationView,
    TeleconsultationSingleConfirmationView
} from "../order/OrderConfirmationViewBuilder"
import { CareDatePickerView, CareDateSelectorPage } from "../page/Page"
import FeedbackPageConfigV2Cache from "../ugc/FeedbackPageConfigV2Cache"
import IUserBusiness from "../user/IUserBusiness"
import UserBusiness from "../user/UserBusiness"
import { ActionUtil, BASE_UTILS_TYPES, OfferUtil, S3Helper } from "@curefit/base-utils"
import DiagnosticsTestListPageView from "./DiagnosticsTestListPageView"
import DiagnosticTestPageView from "./DiagnosticsTestPageView"
import {
    CareCenterOfferRequestParams,
    IOfferServiceV2,
    OFFER_SERVICE_CLIENT_TYPES,
    OfferServiceV3,
    PackOffersResponse
} from "@curefit/offer-service-client"
import {
    CARE_DOCTOR_PAGE_SUPPORTED,
    CareUtil,
    CUSTOM_SUPPORTED_DOCTOR_FILTER,
    MIND_THERAPIST_RECOMMENDATION_MAX_LENGTH
} from "../util/CareUtil"
import CollectionUtil from "../util/CollectionUtil"
import { ActionUtil as AppActionUtil } from "../util/ActionUtil"
import CallCompletionView from "./CallCompletionView"
import CarePackDetailPageView from "./CarePackDetailPageView"
import ConsultationListPageViewBuilder from "./ConsultationListPageViewBuilder"
import ChatListHistoryPageView from "./ChatListHistoryPageView"
import DatePickerView from "./DatePickerView"
import DiagnosticHealthCheckupView from "./DiagnosticHealthCheckupView"
import DiagnosticReportViewBuilder, { DiagnosticReportView } from "./DiagnosticReportViewBuilder"
import { DoctorListingPageView, DoctorListingPageViewV1, DoctorRecommendationPageView } from "./DoctorListingPageView"
import HCUDetailsPageConfig from "./HCUDetailsPageConfig"
import MeHCUPackPageView from "./MeHCUPacksPageView"
import MeMPPacksPageView from "./MeMPPacksPageView"
import MultiConsultationDatePickerView from "./mp/MultiConsultationDatePickerView"
import ConsultationDatePickerViewV1 from "./ConsultationDatePickerViewV1"
import DatePickerViewV1 from "./DatePickerViewV1"
import { CenterNotSelectedError } from "../common/errors/CenterNotSelectedError"
import CareCenterViewBuilder, { CareCenterPageView } from "./CareCenterViewBuilder"
import PrescriptionView from "./PrescriptionView"
import TeleconsultationDatePickerView from "./TeleconsultationDatePickerView"
import TeleconsultationDetailsPageConfig from "./TeleconsultationDetailsPageConfig"
import TeleconsultationDetailsView from "./TeleconsultationDetailsView"
import ManagedPlanProductPageViewBuilder from "./ManagedPlanProductPageViewBuilder"
import CareCartProductPageViewBuilder from "./CareCartProductPageViewBuilder"
import { capitalizeFirstLetter, eternalPromise } from "@curefit/util-common"
import IssueBusiness, { IssueDetailParams, IssueDetailView } from "../crm/IssueBusiness"
import AppUtil, { CARE_BRAND_PAGE_V2_SUPPORTED } from "../util/AppUtil"
import TeleconsultationDetailsViewWeb from "./TeleConsultationDetailViewWeb"
import { CacheHelper } from "../util/CacheHelper"
import * as mime from "mime-types"
import CreateUserFormViewBuilder, { CreateUserFormPage, FormPageBuilderParams } from "./CreateUserFormViewBuilder"
import { AnnouncementBusiness } from "../announcement/AnnouncementBusiness"
import MeDiagnosticTestListPageViewBuilder from "./MeDiagnosticTestListPageView"
import { SupportListPageView } from "../crm/SupportListPageView"
import DoctorBookingPageView from "./DoctorBookingPageView"
import ChronicCareDoctorBookingPageView from "./ChronicCareDoctorBookingPageView"
import SelectSpecialityPageViewBuilder from "./SelectSpecialityPageViewBuilder"
import CareCenterBrowseViewBuilder from "./CareCenterBrowseViewBuilder"
import { OfferV2 } from "@curefit/offer-common"
import LocationUtil from "../util/LocationUtil"
import DiagnosticReportViewBuilderV2, { DiagnosticReportViewV2 } from "./DiagnosticReportViewBuilderV2"
import CareDoctorSearchPageView, {
    CareDoctorSearchPageParams,
    CareDoctorSearchPagePromises
} from "./CareDoctorSearchPageView"
import { AvailableProductTags } from "../page/PageWidgets"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import LivePTTrainerRecommendationPageView from "./LivePTTrainerRecommendationPageView"
import { ISEOData, WidgetWithMetric } from "@curefit/vm-common"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { ErrorFactory } from "@curefit/error-client"
import { ErrorCodes } from "../error/ErrorCodes"
import { CFS_TYPES } from "@curefit/cfs-client"
import { ICFSClient as IFormService } from "@curefit/cfs-client/dist/src/ICFSClient"
import { IOllivanderCityService, OLLIVANDER_CLIENT_TYPES } from "@curefit/ollivander-node-client"
import { ICareBusiness, LivePTSessionBookActionParams } from "./CareBusiness"
import LivePTBookingDetailViewBuilder from "./LivePTBookingDetailViewBuilder"
import { HERCULES_CLIENT_TYPES, IHerculesService } from "@curefit/hercules-client"
import { FUSE_CLIENT_TYPES, IDiagnosticService } from "@curefit/fuse-node-client"
import { GP99ProductPage, GP99ProductPageViewBuilder } from "./CareGP99ProductPageBuilder"
import { CareCultDayTransferPageViewBuilder } from "./CareCultDayTransferProductPageBuilder"
import AuthUtil from "../util/AuthUtil"
import { ICultBusiness } from "../cult/CultBusiness"
import { ICampaignService, IRIS_CLIENT_TYPES, SendCampaignNotificationsRequest } from "@curefit/iris-client"
import { CareHomeSampleUnavailableError } from "../common/errors/CareHomeSampleUnavailableError"
import { AnnouncementView } from "../announcement/AnnouncementViewBuilder"
import { MindTherapyPhysioPacksProductPageViewBuilder } from "./MindTherapyAndPhysioPacksProductPageBuilder"
import { TCPostBookingProductPageView } from "./TCPostBookingProductPageViewBuilder"
import { CustomerIssueType } from "@curefit/issue-common"
import { IChatEventLogReadWriteDao } from "./events/ChatEventLogDao"
import { ChatEventLog } from "./events/ChatEventLog"
import { ISEOService } from "../seo/ISEOService"
import DiagnosticsCartListPageView from "./DiagnosticsCartListPageView"
import DiagnosticCartDetailWebView from "./DiagnosticCartDetailWebView"
import { DiagnosticsCart } from "@curefit/fuse-node-client/dist/src/FuseClientTypes"
import { Action, IProductDetailPage } from "@curefit/apps-common"
import { CareDiagnosticsCartError } from "../common/errors/CareDiagnosticsCartError"
import IFeedbackBusiness from "../ugc/IFeedbackBusiness"
import SGTPreBookingDetailPageViewBuilder from "../cult/SGTPreBookingDetailPageViewBuilder"
import { PromiseCache } from "../util/VMUtil"
import SfGroupClassDatePickerView from "./SfGroupClassDatePickerView"
import * as moment from "moment/moment"

const clone = require("clone")

export function controllerFactory(kernel: Container) {
    @controller("/care", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class CareController {
        private seoService: ISEOService
        constructor(
            @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
            @inject(CUREFIT_API_TYPES.UserBusiness) private userBusiness: IUserBusiness,
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(OMS_API_CLIENT_TYPES.OrderService) private omsApiClient: IOrderService,
            @inject(CUREFIT_API_TYPES.TeleconsultationDetailsPageConfig) private tcDetailsPageConfig: TeleconsultationDetailsPageConfig,
            @inject(CUREFIT_API_TYPES.HCUDetailsPageConfig) private hcuDetailsPageConfig: HCUDetailsPageConfig,
            @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness,
            @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
            @inject(OLLIVANDER_CLIENT_TYPES.IOllivanderCityService) private ollivanderService: IOllivanderCityService,
            @inject(CUREFIT_API_TYPES.CRMIssueService) private CRMIssueService: ICRMIssueService,
            @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
            @inject(CUREFIT_API_TYPES.FeedbackPageConfigV2Cache) private feedbackPageConfigV2Cache: FeedbackPageConfigV2Cache,
            @inject(FEEDBACK_MONGO_TYPES.FeedbackReadOnlyDao) private feedbackDao: IFeedbackReadOnlyDao,
            @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) private offerService: IOfferServiceV2,
            @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) private offerServiceV3: OfferServiceV3,
            @inject(CUREFIT_API_TYPES.ManagedPlanProductPageViewBuilder) private managedPlanProductPageViewBuilder: ManagedPlanProductPageViewBuilder,
            @inject(CUREFIT_API_TYPES.CareCartProductPageViewBuilder) private cartProductPageViewBuilder: CareCartProductPageViewBuilder,
            @inject(DELIVERY_CLIENT_TYPES.DeliveryAreaService) private deliveryAreaService: IDeliveryAreaService,
            @inject(CUREFIT_API_TYPES.DiagnosticReportViewBuilder) private diagnosticReportViewBuilder: DiagnosticReportViewBuilder,
            @inject(CUREFIT_API_TYPES.DiagnosticReportViewBuilderV2) private diagnosticReportViewBuilderV2: DiagnosticReportViewBuilderV2,
            @inject(CUREFIT_API_TYPES.ConsultationListPageViewBuilder) private consultationListPageViewBuilder: ConsultationListPageViewBuilder,
            @inject(CUREFIT_API_TYPES.MeDiagnosticTestListPageViewBuilder) private meDiagnosticTestListPageViewBuilder: MeDiagnosticTestListPageViewBuilder,
            @inject(BASE_UTILS_TYPES.S3Helper) private s3Helper: S3Helper,
            @inject(CUREFIT_API_TYPES.CreateUserFormViewBuilder) private patientFormViewBuilder: CreateUserFormViewBuilder,
            @inject(CUREFIT_API_TYPES.AnnouncementBusiness) private announcementBusiness: AnnouncementBusiness,
            @inject(CUREFIT_API_TYPES.CareCenterViewBuilder) private careCenterViewBuilder: CareCenterViewBuilder,
            @inject(CUREFIT_API_TYPES.SelectSpecialityPageViewBuilder) private selectSpecialityPageViewBuilder: SelectSpecialityPageViewBuilder,
            @inject(CUREFIT_API_TYPES.CareCenterBrowseViewBuilder) private careCenterBrowseViewBuilder: CareCenterBrowseViewBuilder,
            @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness,
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
            @inject(CFS_TYPES.ICFSClient) public formService: IFormService,
            @inject(CUREFIT_API_TYPES.CareBusiness) public careBusiness: ICareBusiness,
            @inject(FUSE_CLIENT_TYPES.IDiagnosticSellerService) protected diagnosticService: IDiagnosticService,
            @inject(CUREFIT_API_TYPES.CareCultDayTransferPageViewBuilder) private careCultDayTransferPageViewBuilder: CareCultDayTransferPageViewBuilder,
            @inject(CUREFIT_API_TYPES.LivePTBookingDetailViewBuilder) private livePTBookingDetailViewBuilder: LivePTBookingDetailViewBuilder,
            @inject(HERCULES_CLIENT_TYPES.IHerculesService) public herculesService: IHerculesService,
            @inject(CUREFIT_API_TYPES.GP99ProductPageViewBuilder) private gp99ProductPageViewBuilder: GP99ProductPageViewBuilder,
            @inject(CUREFIT_API_TYPES.CultBusiness) private cultBusiness: ICultBusiness,
            @inject(CUREFIT_API_TYPES.MindTherapyPhysioPacksProductPageViewBuilder) private mindTherapyPhysioPacksProductPageViewBuilder: MindTherapyPhysioPacksProductPageViewBuilder,
            @inject(IRIS_CLIENT_TYPES.IrisCampaignService) private campaignService: ICampaignService,
            @inject(EHR_CLIENT_TYPES.ISubSectionDataService) private ehrService: ISubSectionDataService,
            @inject(CUREFIT_API_TYPES.ChatEventLogReadWriteDaoMongoImpl) private chatEventLogDao: IChatEventLogReadWriteDao,
            @inject(VM_MODELS_TYPES.SEODataReadOnlyDao) private seoDataDao: ISEODataReadOnlyDao,
            @inject(ALBUS_CLIENT_TYPES.CultPersonalTrainingService) private cultPTService: IHealthfaceService,
            @inject(CUREFIT_API_TYPES.FeedbackBusiness) private feedbackBusiness: IFeedbackBusiness,
            @inject(CUREFIT_API_TYPES.SGTPreBookingDetailPageViewBuilder) private sgtPreBookingDetailPageViewBuilder: SGTPreBookingDetailPageViewBuilder,
            @inject(OMS_API_CLIENT_TYPES.OfferHelper) private offerHelper: OfferHelper
        ) {
        }

        @httpGet("/checkup/:productId")
        async getCarefitDetail(req: express.Request): Promise<DiagnosticHealthCheckupView> {
            const session: Session = req.session
            const osName: string = req.headers["osname"] as string
            const appVersion: number = Number(req.headers["appversion"])
            const productId: string = req.params.productId
            const subCategoryCode: SUB_CATEGORY_CODE = req.query.subCategoryCode
            const bookingId = req.query.bookingId
            const userId = session.userId
            const deviceId = session.deviceId
            const userContext = req.userContext as UserContext
            let patientsListPromise: Promise<Patient[]>
            let healthfaceProductPromise: Promise<HealthfaceProductInfo[]>
            let bookingInfoPromise: Promise<BookingDetail>
            const centerInfoPromise = this.healthfaceService.getCenterDetails(1)
            let offerPromise
            if (!_.isEmpty(bookingId)) {
                bookingInfoPromise = this.healthfaceService.getBookingDetail(bookingId)
            } else {
                patientsListPromise = this.healthfaceService.getAllPatients(userId)
                healthfaceProductPromise = this.healthfaceService.getProductInfoDetailsCached("BUNDLE", subCategoryCode, productId, CareUtil.getHealthfaceTenant(subCategoryCode))
                offerPromise = CareWidgetUtil.getCareProductOffersFromUserContext(userContext, "BUNDLE", [productId], AppUtil.callSourceFromContext(userContext), this.serviceInterfaces, true)
            }

            const productPromise: Promise<Product> = this.catalogueService.getProduct(productId)
            const product = await productPromise
            const baseProduct: DiagnosticProduct = clone(<DiagnosticProduct>product)
            let packageProduct: PackageProduct
            let bundleoffers
            if (healthfaceProductPromise) {
                packageProduct = (<BundleSellableProduct>(await healthfaceProductPromise)[0].baseSellableProduct).packageProduct
                bundleoffers = await offerPromise
            }

            let issuesMap
            let newReportIssueManageOption
            const bookingInfo: BookingDetail = await bookingInfoPromise
            if (bookingInfo) {
                let issuesMapPromise
                let repostIssuesPromise
                if (AppUtil.isNewReportIssueSupported(userContext)) {
                    repostIssuesPromise = this.issueBusiness.getHCIssues(bookingInfo, userContext)
                } else {
                    issuesMapPromise = this.CRMIssueService.getIssuesMap()
                }

                if (AppUtil.isNewReportIssueSupported(userContext)) {
                    const issues: IssueDetailView[] = await repostIssuesPromise
                    newReportIssueManageOption = this.issueBusiness.toManageOptionPayload(issues, true, "Need Help")
                } else {
                    issuesMap = await issuesMapPromise
                }
            }

            const patientsList: Patient[] = await patientsListPromise
            let testBookingId: any = undefined
            let reportEstimationTime: any = undefined
            const bundleStepInfos = _.get(bookingInfo, "bundleSetupInfo.bundleStepInfos", [])
            if (!_.isEmpty(bundleStepInfos)) {
                const bookingDetail: any = bundleStepInfos.find((bundleStepInfo: any) => _.get(bundleStepInfo, "setupStep", null) === "DIAGNOSTIC_TEST" && _.get(bundleStepInfo, "stepState", null) === "TEST_COMPLETED")
                testBookingId = !_.isEmpty(bookingDetail) ? _.get(bookingDetail, "testBookingInfo.booking.id", null) : null
                if (testBookingId) {
                    reportEstimationTime = await this.healthfaceService.getTestReportEstimationTime(testBookingId)
                }
            }
            return new DiagnosticHealthCheckupView(userContext, this.hcuDetailsPageConfig, true,
                session.isNotLoggedIn, baseProduct, newReportIssueManageOption, issuesMap, await centerInfoPromise,
                packageProduct, patientsList, bookingInfo, bundleoffers, reportEstimationTime)
        }

        @httpGet("/managedPlan/:productId")
        async getManagedPlanDetail(req: express.Request): Promise<ProductDetailPage | IProductDetailPage> {
            const session: Session = req.session
            const userContext = req.userContext as UserContext
            const productId = req.params.productId
            const {
                subCategoryCode,
                selectedProductId,
                sellableProductId,
                subscriptionCode,
                patientId,
                bookingId,
                membershipType
            } = req.query
            const finalProductId = selectedProductId || sellableProductId || productId
            if (CareUtil.isCultTransferCareProducts(req.params.productId)) {
                return this.careCultDayTransferPageViewBuilder.getProductPage(userContext, productId, membershipType, selectedProductId)
            }
            const baseProduct = <DiagnosticProduct>await this.catalogueService.getProduct(finalProductId)
            if (_.isEmpty(baseProduct)) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Product Details is Missing").build()
            }
            const setCode = req.query.setCode || baseProduct?.setCode
            const clubCode = req.query.clubCode || baseProduct?.clubCode
            const isMultiCenterEnabled = CareUtil.isMultiCenterSupported(userContext, this.hcuDetailsPageConfig)
            const isDiagnosticPack = CareUtil.isDiagnosticPack(subCategoryCode)
            const isWeb = AppUtil.isWeb(userContext)
            const bookingExist = !_.isEmpty(req.query.bookingId)
            const isPrePurchaseDiagnosticCartViewFlow = isWeb && isDiagnosticPack && !bookingExist
            if (!bookingExist) {
                if (CareUtil.isPartOfSkinProducts(req.query.subCategoryCode)) {
                    return this.managedPlanProductPageViewBuilder.getSkinCareBeforeBookingPage(
                        userContext,
                        session.isNotLoggedIn,
                        session.userId,
                        session.deviceId,
                        productId,
                        subCategoryCode,
                        selectedProductId,
                        setCode,
                        clubCode
                    )
                } else if (CareUtil.isPartOfConsultationPackProducts(subCategoryCode)) {
                    if (CareUtil.isPartOfConsultationPackAyurvedaProducts(setCode)) {
                        return this.mindTherapyPhysioPacksProductPageViewBuilder.getAyurvedaBeforeBookingBundleSessionsPage(
                            userContext,
                            finalProductId,
                            subCategoryCode,
                            clubCode,
                            setCode
                        )
                    }
                    if (CareUtil.isPartOfConsultationCovidPackProducts(setCode)) {
                        return this.managedPlanProductPageViewBuilder.getCovidHomePrePurchaseProductPage(
                            userContext,
                            session.isNotLoggedIn,
                            session.userId,
                            session.deviceId,
                            productId,
                            subCategoryCode,
                            selectedProductId,
                            setCode,
                            clubCode
                        )
                    }
                    return this.managedPlanProductPageViewBuilder.getConsultationPrePurchaseProductPage(
                        userContext,
                        session.isNotLoggedIn,
                        session.userId,
                        session.deviceId,
                        productId,
                        subCategoryCode,
                        selectedProductId,
                        setCode,
                        clubCode
                    )
                } else if (isPrePurchaseDiagnosticCartViewFlow) {
                    return this.cartProductPageViewBuilder.getBeforeBookingPage(userContext, session, productId, subCategoryCode)
                }
                return this.managedPlanProductPageViewBuilder.getBeforeBookingPage(
                    userContext,
                    session.isNotLoggedIn,
                    session.userId,
                    session.deviceId,
                    productId,
                    subCategoryCode,
                    isMultiCenterEnabled,
                    subscriptionCode,
                    patientId
                )
            } else {
                return this.managedPlanProductPageViewBuilder.getAfterBookingPage(
                    userContext as UserContext,
                    productId,
                    bookingId)
            }
        }

        @httpGet("/carecartproduct/:productId")
        @httpGet("/carecartproduct")
        async getCareCartProductPage(req: express.Request): Promise<any> {
            const session: Session = req.session
            const userContext = req.userContext as UserContext
            const {
                subCategoryCode,
                slugValue
            } = req.query

            let productId: string = req.params.productId
            let diagnosticProduct: IDiagnosticSlugResponse

            if (!productId && slugValue) {
                diagnosticProduct = await this.diagnosticService.getDiagnosticProductDetailFromSlug(slugValue)
                productId = diagnosticProduct?.product?.code
            }

            return this.cartProductPageViewBuilder.getBeforeBookingPage(userContext, session, productId, subCategoryCode)
        }

        @httpGet("/diagnostic/cart/web")
        async getDiagnosticCartWeb(req: express.Request): Promise<any> {
            const userContext = req.userContext as UserContext
            const session: Session = req.session
            const userId = session?.userId || userContext?.userProfile?.userId
            const deviceId = session.deviceId
            const source = AppUtil.callSourceFromContext(userContext)

            let productCodes: string[],
                productInfos: HealthfaceProductInfo[],
                testsPromises,
                tests: DiagnosticProduct[],
                patientDetails: Patient,
                cartOffers: OfferV2[],
                careOffers: PackOffersResponse,
                isCartEmpty: boolean,
                cartResponse: DiagnosticsCart

            cartResponse = session?.isNotLoggedIn ? undefined :  await this.diagnosticService.getCart(Number(userId))
            isCartEmpty = _.isEmpty(cartResponse?.diagnosticCartItems)

            if (isCartEmpty) {
                return new DiagnosticCartDetailWebView(userContext, cartResponse?.id)
            }

            productCodes = cartResponse?.diagnosticCartItems?.map((item) => { return item.productCode })
            productInfos = await this.healthfaceService.getProductInfoDetails("DIAGNOSTICS", {
                productCodeCsv: productCodes.join(","),
                patientId: cartResponse?.cartMetaData?.patientId,
                skipProductDetails: false,
            })

            testsPromises = _.map(productCodes, async (productCode) => {
                return <DiagnosticProduct>(await this.catalogueService.getProduct(productCode))
            })
            tests = await Promise.all(testsPromises)

            patientDetails = cartResponse?.cartMetaData?.patientId && !isCartEmpty ? await this.healthfaceService.getPatientDetails(cartResponse?.cartMetaData?.patientId) : undefined
            const {offerIds} = await this.offerServiceV3.getApplicableCartOffersForCare({
                userInfo: {userId, deviceId},
                requiredOfferTypes: ["DIAGNOSTICS"],
                source,
                cityId: userContext.userProfile.cityId
            })
            cartOffers = _.values((await this.offerServiceV3.getOffersByIds(offerIds)).data)
            careOffers = productCodes ? await CareWidgetUtil.getCareProductOffersFromUserContext(userContext, "DIAGNOSTICS",
                productCodes, AppUtil.callSourceFromContext(userContext), this.serviceInterfaces, true) : undefined

            return new DiagnosticCartDetailWebView(userContext, cartResponse.id, patientDetails, productInfos, tests, cartOffers, careOffers)
        }

        @httpGet("/diagnosticCart/getCart")
        async getDiagnosticCart(req: express.Request): Promise<any> {
            const session: Session = req.session
            const userContext = req.userContext as UserContext
            const userId = Number(userContext.userProfile.userId)
            let cartResponse
            try {
                cartResponse = await this.diagnosticService.getCart(userId)
            } catch (err) {
                this.logger.info("CartDiagnosticsError: get:", userId, err)
                throw new CareDiagnosticsCartError().throwError(this.errorFactory, userContext, err.code, err.debugMessage)
            }
            const isCartEmpty = _.isEmpty(cartResponse?.diagnosticCartItems)
            const patientDetailsPromise = cartResponse?.cartMetaData?.patientId && !isCartEmpty ? this.healthfaceService.getPatientDetails(cartResponse?.cartMetaData?.patientId) : undefined
            const product = !isCartEmpty && cartResponse?.diagnosticCartItems[0]?.productCode ? await this.catalogueService.getProduct(cartResponse?.diagnosticCartItems[0]?.productCode) : undefined
            const diagnosticTestTitle = !_.isEmpty(product) ? product?.title || "Diagnostic Test" : undefined
            const cartForApp = CareUtil.processCart(cartResponse, await patientDetailsPromise, diagnosticTestTitle)
            return cartForApp
        }

        @httpGet("/diagnosticCart/add")
        async addToDiagnosticCart(req: express.Request): Promise<any> {
            const session: Session = req.session
            const userContext = req.userContext as UserContext
            const productCode = req.query.productCode
            const patientId = Number(req.query.patientId)
            const userId = Number(userContext.userProfile.userId)
            let cartResponse
            try {
                cartResponse = await this.diagnosticService.addToCart(userId, productCode, patientId)
            } catch (err) {
                this.logger.info("CartDiagnosticsError: add:", userId, err)
                throw new CareDiagnosticsCartError().throwError(this.errorFactory, userContext, err.code, err.debugMessage)
            }

            if (AppUtil.isWeb(userContext)) {
                return this.getDiagnosticCartWeb(req)
            }

            const isCartEmpty = _.isEmpty(cartResponse?.diagnosticCartItems)
            const patientDetailsPromise = cartResponse?.cartMetaData?.patientId && !isCartEmpty ? this.healthfaceService.getPatientDetails(cartResponse?.cartMetaData?.patientId) : undefined
            const product = !isCartEmpty && cartResponse?.diagnosticCartItems[0]?.productCode ? await this.catalogueService.getProduct(cartResponse?.diagnosticCartItems[0]?.productCode) : undefined
            const diagnosticTestTitle = !_.isEmpty(product) ? product?.title || "Diagnostic Test" : undefined
            const cartForApp = CareUtil.processCart(cartResponse, await patientDetailsPromise, diagnosticTestTitle)
            return cartForApp
        }

        @httpGet("/diagnosticCart/remove")
        async removeFromDiagnosticCart(req: express.Request): Promise<any> {
            const session: Session = req.session
            const userContext = req.userContext as UserContext
            const productCode = req.query.productCode
            const userId = Number(userContext.userProfile.userId)
            let cartResponse

            try {
                cartResponse = await this.diagnosticService.removeFromCart(userId, productCode)
            } catch (err) {
                this.logger.info("CartDiagnosticsError: remove:", userId, err)
                throw new CareDiagnosticsCartError().throwError(this.errorFactory, userContext, err.code, err.debugMessage)
            }

            if (AppUtil.isWeb(userContext)) {
                return this.getDiagnosticCartWeb(req)
            }

            const isCartEmpty = _.isEmpty(cartResponse?.diagnosticCartItems)
            const patientDetailsPromise = cartResponse?.cartMetaData?.patientId && !isCartEmpty ? this.healthfaceService.getPatientDetails(cartResponse?.cartMetaData?.patientId) : undefined
            const product = !isCartEmpty && cartResponse?.diagnosticCartItems[0]?.productCode ? await this.catalogueService.getProduct(cartResponse?.diagnosticCartItems[0]?.productCode) : undefined
            const diagnosticTestTitle = !_.isEmpty(product) ? product?.title || "Diagnostic Test" : undefined
            const cartForApp = CareUtil.processCart(cartResponse, await patientDetailsPromise, diagnosticTestTitle)
            return cartForApp
        }

        @httpGet("/diagnostics/serviceabilty")
        async getDiagnosticsServiceabilty(req: express.Request): Promise<any> {
            const pincode = req.query.pincode
            const isServiceable = pincode ? await this.diagnosticService.getDiagnosticServiceabilityInfo(pincode) : undefined
            return { "result": isServiceable }
        }

        @httpGet("/diagnostics/:productId")
        async getCarefitDiagnosticsDetail(req: express.Request): Promise<DiagnosticTestPageView> {
            const session: Session = req.session
            const osName: string = req.headers["osname"] as string
            const appVersion: number = Number(req.headers["appversion"])
            const productId: string = req.params.productId
            const bookingId = req.query.bookingId
            const userId = session.userId
            const codepushVersion: number = req.headers["codepushversion"] && req.headers["codepushversion"] !== "undefined" ? Number(req.headers["codepushversion"]) : 0
            const user = await this.userCache.getUser(userId)
            const userContext = req.userContext as UserContext
            const product1: Product = await this.catalogueService.getProduct(productId)
            let diagnosticsEmailedTestReport
            let reportActions: Map<string, any>
            reportActions = new Map()
            // hack to get imageUrl
            const product: Product = clone(product1)
            const bookingInfo: BookingDetail = await this.healthfaceService.getBookingDetail(bookingId)
            let issuesMapPromise
            let repostIssuesPromise
            if (AppUtil.isNewReportIssueSupported(userContext)) {
                const reportIssueParams = this.issueBusiness.getDiagnosticTestIssueParams(bookingInfo)
                repostIssuesPromise = this.issueBusiness.getIssues(reportIssueParams.productType, reportIssueParams.productStates, reportIssueParams.meta, userContext)
            } else {
                issuesMapPromise = this.CRMIssueService.getIssuesMap()
            }
            const patient: Patient = await this.healthfaceService.getPatientDetails(bookingInfo.booking.patientId)

            let issuesMap
            let newReportIssueManageOption
            if (AppUtil.isNewReportIssueSupported(userContext)) {
                const issues: IssueDetailView[] = await repostIssuesPromise
                newReportIssueManageOption = this.issueBusiness.toManageOptionPayload(issues, true, "Need Help")
            } else {
                issuesMap = await issuesMapPromise
            }
            const reportGeneratedBooking = bookingInfo.diagnosticsTestOrderResponse.find(item => item.status === "REPORT_GENERATED")
            if (!_.isEmpty(reportGeneratedBooking)) {
                diagnosticsEmailedTestReport = await this.healthfaceService.getDiagnosticsEmailedTestReport(reportGeneratedBooking.carefitOrderId)
            }
            const isPhleboCallActive = bookingInfo?.diagnosticsTestOrderResponse[0]?.atHomeStepInfo?.allowedActions?.indexOf("PHLEBO_CALLING") !== -1
            this.logger.info(`pre-diagnosticPhleboCall phleboNumber:${bookingInfo?.diagnosticsTestOrderResponse[0]?.atHomeDiagnosticOrder?.phleboMobileNumber} isPhleboCallActive:${isPhleboCallActive} userId:${userId} bookingId:${bookingInfo.booking.id}`)
            const carefitOrderId = bookingInfo.diagnosticsTestOrderResponse[0]?.carefitOrderId
            if (carefitOrderId) {
                const reportAction = await this.healthfaceService.getReportAction({ carefitOrderId })
                reportActions.set(carefitOrderId, reportAction)
            }
            return new DiagnosticTestPageView(
                userContext,
                this.hcuDetailsPageConfig,
                osName,
                appVersion,
                codepushVersion,
                user.isInternalUser,
                product,
                bookingInfo,
                patient,
                newReportIssueManageOption,
                issuesMap,
                diagnosticsEmailedTestReport,
                reportActions
            )
        }


        private getTCInventoryRequestParams(params: any, query: any, product?: ConsultationProduct, bookingStartTimeEpoch?: number, daysToGo?: number): TCInventoryRequest {
            const isGP99Product = product && CareUtil.isGP99DoctorType(product.doctorType)
            const requestObject: TCInventoryRequest = {
                bookingStartTimeEpoch: bookingStartTimeEpoch || new Date().getTime(),
                centerId: CareUtil.isCenterIdSkipableProduct(product) ? undefined : (query.centerId ? query.centerId : product?.tenant === "SUGARFIT" ? undefined : 1),
                parentBookingId: query.followUpParentBookingId || query.parentBookingId,
                patientId: query.patientId,
                followUpConsultationId: query.followUpBookingId || query.followUpConsultationId,
                productCode: params.productId,
                doctorId: query.doctorId ? Number(query.doctorId) : undefined,
                excludePreferredDoctor: query.excludePreferredDoctor ? query.excludePreferredDoctor : !(product && product.doctorType === "LC" || (CareUtil.isMindDoctorType(product.doctorType) || CareUtil.isLivePTDoctorType(product.doctorType) || CareUtil.isPTDoctorType(product.doctorType))),
                strictGenderCheck: query.strictGenderCheck === "true"
            }
            if ((CareUtil.isSugarfitExperienceCenterProduct(product) || CareUtil.isSugarfitExperienceCenterDiagnosticProduct(product)) && (!requestObject.centerId)) {
                requestObject.centerId = query.centerId
                this.logger.info(`Updated request object to :: ${requestObject}`)
            }
            if (isGP99Product) {
                return {
                    ...requestObject,
                    daysToGo: 3,
                }
            }
            if (daysToGo) {
                requestObject.daysToGo = daysToGo
            }
            return requestObject
        }

        private getGCInventoryRequestParams(params: any, query: any, userContext: UserContext, product?: ConsultationProduct, bookingStartTimeEpoch?: number): GCInventoryRequest {

            const startTimeEpoch = bookingStartTimeEpoch || new Date().getTime()
            // add 10 days
            const endTime = new Date(startTimeEpoch + (240) * (3600 * 1000))
            // get end of that day. 18:29.999 UTC = 23:59.999 IST
            endTime.setHours(18, 29, 59, 999)
            const requestObject: GCInventoryRequest = {
                bookingStartTimeEpoch: startTimeEpoch,
                bookingEndTimeEpoch: endTime.getTime(),
                subCategoryCode: "SF_INCENTER_GROUP_CLASS",
                userId: userContext.userProfile.userId,
                centerId: CareUtil.isCenterIdSkipableProduct(product) ? undefined : (query.centerId ? query.centerId : 1),
                parentBookingId: query.followUpParentBookingId || query.parentBookingId,
                patientId: query.patientId,
                followUpConsultationId: query.followUpBookingId || query.followUpConsultationId,
                productCode: "",
                doctorId: query.doctorId ? Number(query.doctorId) : undefined,
                excludePreferredDoctor: query.excludePreferredDoctor ? query.excludePreferredDoctor : !(product && product.doctorType === "LC" || (CareUtil.isMindDoctorType(product.doctorType) || CareUtil.isLivePTDoctorType(product.doctorType) || CareUtil.isPTDoctorType(product.doctorType))),
                strictGenderCheck: query.strictGenderCheck === "true"
            }
            return requestObject
        }

        async getTCDatePicker(query: any, params: any, isReschedule: string, userContext?: UserContext): Promise<CareDateSelectorPage> {
            const parentBookingId: number = query.parentBookingId
            const centerId: number = query.centerId
            const isExternal: string = query.isExternal
            const followUpConsultationId: string = query.followUpConsultationId
            const productId: string = params.productId
            const product: ConsultationProduct = <ConsultationProduct>await this.catalogueService.getProduct(productId)
            let bookingDetailPromise: Promise<BookingDetail>
            if (isReschedule === "true" && !_.isNil(parentBookingId)) {
                bookingDetailPromise = this.healthfaceService.getBookingDetail(parentBookingId)
            }
            let centerPromise: Promise<Center>
            if (centerId) {
                centerPromise = this.healthfaceService.getCenterDetails(centerId)
            }

            const datesToShowPromise: Promise<TCAvailableSlotsResponse> = this.healthfaceService.getTCAvailableSlotDetails(this.getTCInventoryRequestParams(params, query))

            const center: Center = await centerPromise
            const datesToShow: TCAvailableSlotsResponse = await datesToShowPromise

            let bookingDetail: BookingDetail
            if (bookingDetailPromise) {
                bookingDetail = await bookingDetailPromise
            }

            return new TeleconsultationDatePickerView(product, datesToShow, center, parentBookingId, isReschedule, isExternal, followUpConsultationId, bookingDetail, query.offerIds, userContext).datePicker
        }

        async getConsultationDatePickerV1(userContext: UserContext, userId: string, query: any = {}, params: any, isReschedule: string): Promise<CareDatePickerView> {
            let preferredCenterId: string, doctor: Doctor, bookingDetail: BookingDetail
            let preferredCenterResponse, centerId
            const isExternal: string = query.isExternal
            const followUpConsultationId: string = query.followUpConsultationId
            const productId: string = params.productId
            const subCategoryCode: SUB_CATEGORY_CODE = query.subCategoryCode as SUB_CATEGORY_CODE
            let forceCurrentCenter = query.forceCurrentCenter === "true" || false
            this.logger.info("SF-WEB===>STEP2", productId)
            const product = <ConsultationProduct>await this.catalogueService.getProduct(productId)
            this.logger.info("SF-WEB===>STEP3", product.tenant)
            const isCultLivePT = CareUtil.isLivePTDoctorType(product.doctorType)
            const isCultLiveSGT = CareUtil.isLiveSGTDoctorType(product.doctorType)
            const isTransform = CareUtil.isTransformDoctorType(product.doctorType) || product.tenant === "TRANSFORM"
            if (product.tenant === "SUGARFIT" && (!query.centerId) && (query.doctorId)) {
                try {
                    this.logger.info("SF-WEB===>STEP4", query)
                } catch {
                    this.logger.info("SF-WEB===>STEP4")
                }
                doctor = await this.healthfaceService.getDoctorDetails(query.doctorId, productId, false, "SUGARFIT")
                this.logger.info(`For doctor :: ${doctor.id}, updated center to :: ${doctor.doctorCenterMapping[0].centerId}`)
                query.centerId = doctor.doctorCenterMapping[0].centerId
            }
            if (isTransform && (!query.doctorId || !query.parentBookingId)) {
                try {
                    this.logger.info("SF-WEB===>STEP5", isTransform)
                } catch {
                    this.logger.info("SF-WEB===>STEP5")
                }
              const inventoryParams = await this.healthfaceService.getConsultationInventoryParamsForUser(userContext.userProfile.userId, subCategoryCode, product.doctorType, "TRANSFORM")
                if (!query.doctorId) {
                    query.doctorId = inventoryParams.doctorId
                }
              query.parentBookingId = inventoryParams.parentBookingId
            }
            userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
            if ((isCultLivePT || isCultLiveSGT) && !userContext.sessionInfo.isUserLoggedIn) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_LOGGED_IN_ERR, 400).build()
            }
            if (_.isNil(query.parentBookingId) && (isCultLivePT || isCultLiveSGT || isTransform)) {
                try {
                    this.logger.info("SF-WEB===>STEP6", isTransform, isCultLivePT, isCultLiveSGT)
                } catch {
                    this.logger.info("SF-WEB===>STEP6")
                }
                const activePacks = await this.healthfaceService.getActiveBundleOrders(userContext.userProfile.userId, "BUNDLE", isTransform ? subCategoryCode : (isCultLivePT ? "LIVE_PERSONAL_TRAINING" : "LIVE_SGT"), false, isTransform ? "TRANSFORM" : "CULTFIT")
                if (!_.isEmpty(activePacks)) {
                    query.parentBookingId = activePacks[0].bookingId
                }
            }
            const parentBookingId: number = query.parentBookingId
            let showChangeDoctorAction = query.showChangeDoctorAction === "true" || false
            const isCareCenterBrowseFlowSupported = CareUtil.isNewCenterSpecifiFlowSupported(userContext) && CareUtil.isCareCenterRestrictedProduct(product)
            const isMultiCenterSupported = CareUtil.isMultiCenterSupported(userContext, this.hcuDetailsPageConfig, product)
            if (CareUtil.isAnxietyTherapy(productId) && !query.patientId) {
                query.patientId = (await CareUtil.getOrCreatePatient(userContext, this.userCache, this.healthfaceService))?.id
            }
            const gender: "Female" | "Male" = query.gender
            if ((isCultLivePT || isCultLiveSGT || isTransform) && !query.patientId) {
                const selfPatient = await CareUtil.createOrUpdatePatient(userContext, this.userCache, this.healthfaceService, gender, product.tenant)
                query.patientId = selfPatient.id
            }

            // Adding hack to reset doctor id while changing format type in slot selection page
            if ((isCultLivePT || isCultLiveSGT || isTransform) && query.doctorId && query.doctorId !== -1) {
                const doctorDetails = await this.healthfaceService.getDoctorDetails(query.doctorId)
                const isDoctorTypeMatching = doctorDetails && !_.isEmpty(doctorDetails.subServiceTypes) ? doctorDetails.subServiceTypes.find(subServiceType => subServiceType.code === product.doctorType) : undefined
                if (!isDoctorTypeMatching) {
                    query.doctorId = undefined
                }
            }
            const doctorId: number = query.doctorId
            // Adding for restricting flow of center change in reschedule / followup
            const isCenterChangeRestricted = CareUtil.isCareCenterChangeRestricted(userContext, product, isReschedule, followUpConsultationId)
            if ((isReschedule === "true" || isCenterChangeRestricted) && !_.isNil(parentBookingId)) {
                try {
                    this.logger.info("SF-WEB===>STEP7", isReschedule, isCenterChangeRestricted, parentBookingId)
                } catch {
                    this.logger.info("SF-WEB===>STEP7")
                }
                bookingDetail = await this.healthfaceService.getBookingDetailForBookingId(parentBookingId, product.tenant)
                // For Followup consultation, we are showing slots more than 8 days while rescheduling,
                // to limit it to same as followup slots passing the followup id from order
                if (isReschedule === "true" && bookingDetail.booking.cfOrderId) {
                    const order: Order = await this.omsApiClient.getOrder(bookingDetail.booking.cfOrderId)
                    const followUpConsultationId = _.get(order, "products.0.option.followUpConsultationId")
                    if (followUpConsultationId && followUpConsultationId !== -1) {
                        query.followUpBookingId = followUpConsultationId
                        query.followUpParentBookingId = _.get(order, "products.0.option.parentBookingId") || undefined
                    }
                }
                query.centerId = query.centerId !== undefined && query.centerId !== "undefined" ? query.centerId : bookingDetail.consultationOrderResponse.center.id.toString()
                forceCurrentCenter = true
            }
            if (isCenterChangeRestricted && _.get(bookingDetail, "consultationOrderResponse.center.id")) {
                try {
                    this.logger.info("SF-WEB===>STEP8", isCenterChangeRestricted)
                } catch {
                    this.logger.info("SF-WEB===>STEP8")
                }
                await this.healthfaceService.updatePatientPreferredCenter(userId, bookingDetail.booking.patientId, bookingDetail.consultationOrderResponse.center.id, product.productType, product.categoryId, productId, CareUtil.getHealthfaceTenant(undefined, product.doctorType))
            }
            let center: Center
            if (CareUtil.isSugarfitExperienceCenterProduct(product) || CareUtil.isSugarfitExperienceCenterDiagnosticProduct(product)) {
                try {
                    this.logger.info("SF-WEB===>STEP9", query)
                } catch {
                    this.logger.info("SF-WEB===>STEP9")
                }
                if (!query.patiendId) {
                    query.patientId = (await CareUtil.getOrCreatePatient(userContext, this.userCache, this.healthfaceService, "SUGARFIT"))?.id
                }
                center = await this.healthfaceService.getCenterDetails(query.centerId, product.tenant)
                this.logger.info(`Updated center id to :: ${center.id}`)
                query.centerId = center.id
            }
            if (!CareUtil.isCenterIdSkipableProduct(product)) {
                try {
                    this.logger.info("SF-WEB===>STEP10", product)
                } catch {
                    this.logger.info("SF-WEB===>STEP10")
                }
                preferredCenterResponse = await this.healthfaceService.getPatientPreferredCenter(userId, query.patientId, product.productType, product.categoryId, productId, product.tenant)
                if (!_.isEmpty(preferredCenterResponse)) {
                    preferredCenterId = preferredCenterResponse.centerResponse.id.toString()
                }
                centerId = query.centerId !== undefined && query.centerId !== "undefined" ? query.centerId : preferredCenterId
                if (_.isEmpty(centerId)) {
                    if (product.tenant === "SUGARFIT") {
                        centerId = undefined
                    } else if (isMultiCenterSupported) {
                        throw new CenterNotSelectedError().throwError(this.errorFactory, "Please select a center", userContext)
                    } else {
                        centerId = 1
                    }
                }
            }
            // Having this check for if doctor is available in the user's pref center will take pref center
            if (doctorId && preferredCenterId && !(query.centerId && query.centerId === preferredCenterId)) {
                try {
                    this.logger.info("SF-WEB===>STEP11")
                } catch {
                    // Ignore
                }
                doctor = await this.healthfaceService.getDoctorDetails(doctorId)
                if (doctor && doctor.doctorCenterMapping) {
                    const docAvlInPrefCenter = doctor.doctorCenterMapping.find(item => {
                        return String(item.centerId) === preferredCenterId
                    })
                    if (docAvlInPrefCenter) {
                        forceCurrentCenter = false
                    }

                    if (product.consultationMode === "ONLINE") {
                        /**
                         * This is a band-aid for the time
                         * Doctor Mapping would have a virtual center for Anxiety Therapy
                         * This is to be fixed by Albus team later on
                         */
                        if (!CareUtil.isAnxietyTherapy(productId)) {
                            centerId = doctor.doctorCenterMapping.filter(
                                mapping => mapping.centerId !== CareUtil.getVirtualCenterId()
                            )[0]?.centerId
                        }
                        preferredCenterId = undefined
                    }
                }
            }
            if (!(forceCurrentCenter && query.centerId) && preferredCenterId) {
                center = preferredCenterResponse.centerResponse
            } else {
                if (centerId && !CareUtil.isCenterIdSkipableProduct(product)) {
                    try {
                        this.logger.info("SF-WEB===>STEP12", product)
                    } catch {
                        this.logger.info("SF-WEB===>STEP12")
                    }
                    const centerPromise = this.healthfaceService.getCenterDetails(centerId, product.tenant)
                    center = await centerPromise
                }
            }

            // update center id of query to pass over
            query.centerId = center?.id
            if ((CareUtil.isSugarfitExperienceCenterProduct(product) || CareUtil.isSugarfitExperienceCenterDiagnosticProduct(product)) && (!query.centerId)) {
                try {
                    this.logger.info("SF-WEB===>STEP13", {query, tenant: product.tenant})
                } catch {
                    this.logger.info("SF-WEB===>STEP13")
                }
                center = await this.healthfaceService.getCenterDetails(query.centerId, product.tenant)
                query.centerId = center.id
                this.logger.info(`Updated center id to :: ${center.id}`)
            }

            // Limiting Reschedule to Same Doctor
            if (isReschedule === "true" && bookingDetail && !CareUtil.isGP99DoctorType(product.doctorType) && (CareUtil.isCareConsultationProduct(product) || CareUtil.isMindDoctorType(product.doctorType))) {
                query.doctorId = bookingDetail.consultationOrderResponse.doctor.id
            }
            if (AppUtil.isSugarFitApp(userContext) && product?.productCode === "SF_INCENTER_GROUP_CLASS") {
                const datesToShowForGroupClass = await this.healthfaceService.getGCAvailableSlotDetails(this.getGCInventoryRequestParams(params, query, userContext, product), product.tenant)
                return new SfGroupClassDatePickerView().buildView(
                    userContext,
                    product,
                    datesToShowForGroupClass,
                    isMultiCenterSupported,
                    center,
                    query.patientId,
                    parentBookingId,
                    isReschedule,
                    isExternal,
                    followUpConsultationId,
                    bookingDetail,
                    this.healthfaceService,
                    query.offerIds,
                    showChangeDoctorAction,
                    isCultLivePT ? true : isCenterChangeRestricted,
                    isCareCenterBrowseFlowSupported,
                    this.announcementBusiness,
                    CareUtil.isCareConsultationProduct(product) && query.doctorId && product.consultationMode === "INCENTRE",
                    this.herculesService,
                    this.serviceInterfaces.segmentService,
                    doctorId?.toString() || query?.doctorId
                )
            }
            const datesToShow: TCAvailableSlotsResponse = await this.getConsultationInventoryData(product, params, query)
            if ((isCultLivePT || isCultLiveSGT || isTransform || CareUtil.isPTDoctorType(product.doctorType)) && !showChangeDoctorAction) {
                showChangeDoctorAction = !_.isEmpty(datesToShow.preferredDoctorMap)
            }
            let doesTrainerRecommendationExistPromise: Promise<boolean>
            if (isCultLivePT) {
                let excludedDoctorId
                if (doctorId && doctorId !== -1) {
                    excludedDoctorId = doctorId
                } else if (!_.isEmpty(datesToShow.preferredDoctorMap)) {
                    excludedDoctorId = (_.values(datesToShow.preferredDoctorMap))[0].id
                }
                const doctorRecommendationRequest: DoctorRecommendationRequest = {
                    subCategoryCode: "LIVE_PERSONAL_TRAINING",
                    doctorTypeCodesCsv: [product.doctorType],
                    patientId: query.patientId,
                    excludeDoctors: excludedDoctorId ? [excludedDoctorId] : []
                }
                doesTrainerRecommendationExistPromise = this.healthfaceService.haveDoctorRecommendations(doctorRecommendationRequest, "CULTFIT")
            }
            let availableProductTags: AvailableProductTags[] = []
            if (isCultLivePT || isCultLiveSGT || isTransform) {
                const sellableProducts = await this.cultPTService.getConsultationSellableProductsNotCached(userContext.userProfile.cityId, isCultLivePT ? "LIVE_PERSONAL_TRAINING" : "LIVE_SGT")
                availableProductTags = await Promise.all(_.map(sellableProducts.consultationTypes, async consultationItem => {
                    const productDetail: ConsultationProduct = <ConsultationProduct>await this.catalogueService.getProduct(consultationItem.products[0].code)
                    return {
                        title: productDetail.subTitle,
                        productId: productDetail.productId,
                        selected: productDetail.productId === productId,
                        // iconType: !_.isEmpty(datesToShow.metadata) && datesToShow.metadata.recommendedProductCode === productDetail.productId ? "BLUE_BULLET" : undefined
                        // icon: CareUtil.getFormatIconFromDoctorType(productDetail.doctorType)
                    }
                }))
            }
            const hasTrainerRecommendations = await doesTrainerRecommendationExistPromise
            let announcementView: AnnouncementView = undefined
            if (await AppUtil.isLivePTSGTNoShowEnabled(userContext) && isCultLiveSGT && !_.isNil(query.parentBookingId)) {
                announcementView = <AnnouncementView>await this.announcementBusiness.getAnnouncementToShow(userContext, "sgt_no_show_policy")
            }
            return new ConsultationDatePickerViewV1().buildView(
                userContext,
                product,
                datesToShow,
                isMultiCenterSupported,
                center,
                query.patientId,
                parentBookingId,
                isReschedule,
                isExternal,
                followUpConsultationId,
                bookingDetail,
                this.healthfaceService,
                query.offerIds,
                announcementView,
                showChangeDoctorAction,
                isCultLivePT ? true : isCenterChangeRestricted,
                isCareCenterBrowseFlowSupported,
                availableProductTags,
                hasTrainerRecommendations,
                this.announcementBusiness,
                CareUtil.isCareConsultationProduct(product) && query.doctorId && product.consultationMode === "INCENTRE",
                this.herculesService,
                this.serviceInterfaces.segmentService,
                doctorId?.toString() || query?.doctorId
            )
        }

        async getConsultationInventoryData(product: ConsultationProduct, params: any, query: any): Promise<TCAvailableSlotsResponse> {
            if (product.tenant === "CULTFIT" || CareUtil.isGP99DoctorType(product?.doctorType) || product.tenant === "TRANSFORM") {
                return this.healthfaceService.getTCAvailableSlotDetails(this.getTCInventoryRequestParams(params, query, product), product.tenant)
            } else {
                const currentDateTime = new Date().getTime()
                const currentDate = new Date()
                currentDate.setHours(0, 0, 0, 0)
                const currentDateFromStart = currentDate.getTime()
                const consultationInventroyDataPromise = [0, 5, 10].map(item => {
                    return this.healthfaceService.getTCAvailableSlotDetails(this.getTCInventoryRequestParams(
                        params,
                        query,
                        product,
                        item === 0 ? new Date(currentDateTime).getTime() : new Date(currentDateFromStart + item * 86400000).getTime(),
                        6
                    ), product.tenant)
                })
                const consultationInventoryData: TCAvailableSlotsResponse[] = await Promise.all(consultationInventroyDataPromise)
                const initialItem: TCAvailableSlotsResponse = {
                    ...consultationInventoryData[0],
                    preferredDoctorMap: {},
                    formattedAvailableSlotResponseList: [],
                    preferredDoctorUnavailableDays: [],
                    preferredDoctorUnavailability: []
                }
                const finalResponse = consultationInventoryData.reduce((
                    acc: TCAvailableSlotsResponse,
                    item: TCAvailableSlotsResponse
                ) => {
                    acc.preferredDoctorMap = {
                        ...acc.preferredDoctorMap,
                        ...item.preferredDoctorMap
                    }
                    if (item.formattedAvailableSlotResponseList) {
                        acc.formattedAvailableSlotResponseList = acc.formattedAvailableSlotResponseList.concat(item.formattedAvailableSlotResponseList)
                    }
                    if (item.preferredDoctorUnavailableDays) {
                        acc.preferredDoctorUnavailableDays = acc.preferredDoctorUnavailableDays.concat(item.preferredDoctorUnavailableDays)
                    }
                    if (item.preferredDoctorUnavailability) {
                        acc.preferredDoctorUnavailability = acc.preferredDoctorUnavailability.concat(item.preferredDoctorUnavailability)
                    }
                    return acc
                }, initialItem)
                if (!_.isEmpty(finalResponse.formattedAvailableSlotResponseList)) {
                    finalResponse.formattedAvailableSlotResponseList = _.uniqBy(finalResponse.formattedAvailableSlotResponseList, item => item.startTime)
                }
                if (!_.isEmpty(finalResponse.preferredDoctorUnavailableDays)) {
                    finalResponse.preferredDoctorUnavailableDays = [...new Set(finalResponse.preferredDoctorUnavailableDays)]
                }
                if (!_.isEmpty(finalResponse.preferredDoctorUnavailability)) {
                    finalResponse.preferredDoctorUnavailability = _.uniqBy(finalResponse.preferredDoctorUnavailability, item => item?.date)
                }
                return finalResponse
            }

        }

        @httpGet("/datepicker/:productId")
        async getCarefitDatePicker(req: express.Request): Promise<CareDateSelectorPage> {
            const type: INVENTORY_TYPE = req.query.type
            const category: string = req.query.category
            const isReschedule: string = req.query.isReschedule
            const addressId: string = req.query.addressId
            const customerid: string = req.session.userId
            const userContext: UserContext = req.userContext as UserContext
            const timezone = userContext.userProfile.timezone
            if (!type || type === "CONSULTATION") {
                return this.getTCDatePicker(req.query, req.params, isReschedule, userContext)
            } else if (type === "DIAGNOSTICS") {
                const bookingId: string = req.query.parentBookingId
                let params: DiagnosticAtHomeInventoryRequest | DiagnosticInCenterInventoryRequest
                const bookingDetail: BookingDetail = await this.healthfaceService.getBookingDetail(Number(bookingId))
                let productCodes = req.query.productCodes
                if (!_.isEmpty(productCodes)) {
                    productCodes = productCodes.split(",")
                } else if (bookingDetail.booking.categoryCode === "BUNDLE") {
                    productCodes = [bookingDetail.bundleOrderResponse.hcuTestDetails.baseTestCode, ...bookingDetail.bundleOrderResponse.hcuTestDetails.addonTestCodes]
                } else {
                    productCodes = bookingDetail.diagnosticsTestOrderResponse[0].productCodes
                }

                if (category === "AT_HOME_SLOT") {
                    const userDetailAddress = await this.userBusiness.getAddress(customerid, addressId)
                    const latLong: LatLong = UserBusiness.toLatLng(userDetailAddress.latLong)
                    const deliveryArea: DeliveryArea = await this.deliveryAreaService.findDeliveryArea(latLong, "DIAGNOSTICS", undefined, undefined, undefined, userContext.userProfile.cityId)
                    // hack to get values
                    const deliveryArea1: DeliveryArea = clone(deliveryArea)
                    params = {
                        pincode: deliveryArea1.pincode,
                        bookingStartTimeEpoch: new Date().getTime(),
                        days: 15,
                        addressId: userDetailAddress.addressId,
                        localityCode: deliveryArea1.localityId,
                        cityCode: deliveryArea1.cityId,
                        productCodes: productCodes,
                        latitude: latLong.lat,
                        longitude: latLong.long
                    }
                } else if (category === "IN_CENTRE_SLOT") {
                    const hasAtHome = req.query.hasAtHome
                    params = {
                        centreCode: "CAREFITDIAG",
                        bookingStartTimeEpoch: new Date().getTime(),
                        days: 15,
                        hasAtHome: hasAtHome ? hasAtHome : false,
                        productCodes: productCodes
                    }
                }
                const availableSlots: DiagnosticAvailableSlotsResponse = await this.healthfaceService.getDiagnosticAvailableSlotDetails(params, category)
                return new DatePickerView(userContext, bookingDetail.booking.id.toString(), productCodes[0], category, availableSlots, bookingDetail.booking.patientId, bookingDetail.booking.categoryCode, isReschedule, productCodes, req.query.nextAction, req.query.offerIds).datePicker
            }
        }

        @httpGet("/datepicker/v1/:productId")
        async getCarefitDatePickerV1(req: express.Request): Promise<CareDatePickerView> {
            const session: Session = req.session
            const productId: string = req.params.productId
            const type: INVENTORY_TYPE = req.query.type
            const category: string = req.query.category
            const isReschedule: string = req.query.isReschedule
            let addressId: string = req.query.addressId
            const customerid: string = req.session.userId
            let isDiagPack = false
            const userContext: UserContext = req.userContext as UserContext
            const cityId = userContext.userProfile.cityId
            const timezone = userContext.userProfile.timezone
            this.logger.info("SF-WEB===>STEP1", type)
            if (!type || type === "CONSULTATION") {
                return this.getConsultationDatePickerV1(userContext, session.userId, req.query, req.params, isReschedule)
            } else if (type === "DIAGNOSTICS") {
                const isMultiCenterSupported = CareUtil.isMultiCenterSupported(userContext, this.hcuDetailsPageConfig)
                const bookingId: string = req.query.parentBookingId
                const cartId = req.query.cartId
                let testBookingId, isBookingFromConsultation, bookingDetail, parentBookingId
                let patientId = req.query.patientId
                let categoryCode: CATEGORY_CODE = "DIAGNOSTICS"
                let params: DiagnosticAtHomeInventoryRequest | DiagnosticInCenterInventoryRequest
                const tenant: HealthfaceTenant = CareUtil.getHealthfaceTenant("DIAGNOSTICS", undefined, userContext.sessionInfo.orderSource)
                if (bookingId) {
                    bookingDetail = await this.healthfaceService.getBookingDetail(Number(bookingId), undefined, undefined, undefined, undefined, tenant)
                    isDiagPack = bookingDetail.booking.subCategoryCode === "DIAG_PACK_OT"
                    isBookingFromConsultation = bookingDetail.booking.subCategoryCode === "CF_INCENTRE_CONSULTATION" || bookingDetail.booking.subCategoryCode === "CF_ONLINE_CONSULTATION" || bookingDetail.booking.subCategoryCode === "EXTERNAL_INCENTRE_CONSULTATION" || bookingDetail.booking.subCategoryCode === "EXTERNAL_ONLINE_CONSULTATION"
                    parentBookingId = bookingDetail.booking.id
                    patientId = bookingDetail.booking.patientId
                    categoryCode = bookingDetail.booking.categoryCode
                }
                let productCodes = req.query.productCodes
                if (!_.isEmpty(productCodes)) {
                    productCodes = productCodes.split(",")
                } else if (bookingDetail?.booking?.categoryCode === "BUNDLE") {
                    productCodes = [bookingDetail.bundleOrderResponse.hcuTestDetails.baseTestCode, ...bookingDetail.bundleOrderResponse.hcuTestDetails.addonTestCodes]
                } else {
                    productCodes = bookingDetail?.diagnosticsTestOrderResponse[0]?.productCodes
                }
                let center: Center

                if (category === "AT_HOME_SLOT") {
                    addressId = req.query.addressIdV1 && isReschedule ? req.query.addressIdV1 : addressId
                    const userDetailAddress = await this.userBusiness.augmentStructuredAddress(customerid, addressId)
                    const latLong: LatLong = UserBusiness.toLatLng(userDetailAddress.latLong)
                    const isServiceable = await this.deliveryAreaService.checkLocationServiceability(latLong, userDetailAddress.structuredAddress.pincode, cityId, "DIAGNOSTICS")
                    this.logger.info(`Validate Address Post-Purchase userId:${userContext?.userProfile.userId} addressId: ${addressId} isServiceable:${isServiceable.isLocationServiceable} lat: ${latLong.lat} long: ${latLong.long} pincode: ${userDetailAddress.structuredAddress.pincode} cityId: ${cityId} appversion: ${userContext?.sessionInfo?.appVersion} userAgent: ${userContext?.sessionInfo?.userAgent} osName: ${userContext?.sessionInfo?.osName} cpVersion: ${userContext?.sessionInfo?.cpVersion}`)
                    if (!isServiceable.isLocationServiceable) {
                        throw new CareHomeSampleUnavailableError().throwError(this.errorFactory, "Location Not Serviceable - Apologies. We are yet to provide home collection to your address", userContext)
                    }
                    const deliveryArea = isServiceable.area
                    const deliveryArea1: DeliveryArea = clone(deliveryArea)
                    if (!_.isEmpty(bookingDetail) &&
                        isReschedule === "true" &&
                        bookingDetail.booking.subCategoryCode === "DIAGNOSTIC_TEST") {
                        testBookingId = bookingDetail.booking.id
                    }
                    let bookingAmount = 0
                    if (!_.isEmpty(bookingDetail)) {
                        const rootBookingDetail: BookingDetail = (bookingDetail.booking.parentBookingId && bookingDetail.booking.parentBookingId != -1) ? await this.healthfaceService.getBookingDetail(bookingDetail.booking.parentBookingId, undefined, undefined, undefined, undefined, tenant) : bookingDetail
                        const curefitOrderId = rootBookingDetail.booking.cfOrderId
                        const order: Order = await this.omsApiClient.getOrder(curefitOrderId)
                        bookingAmount = order.totalAmountPayable
                    } else {
                        const products = await this.serviceInterfaces.catalogueService.getProducts(productCodes)
                        products.forEach(product => {
                            bookingAmount += product.price.listingPrice
                        })
                    }
                    params = {
                        pincode: userDetailAddress.structuredAddress.pincode,
                        bookingStartTimeEpoch: new Date().getTime(),
                        days: 15,
                        addressId: userDetailAddress.addressId,
                        localityCode: deliveryArea1?.localityId,
                        cityCode: cityId,
                        productCodes: productCodes,
                        latitude: latLong.lat,
                        longitude: latLong.long,
                        bookingId: testBookingId,
                        bookingAmount: bookingAmount,
                    }
                } else if (category === "IN_CENTRE_SLOT") {
                    const product = <ConsultationProduct>await this.catalogueService.getProduct(productCodes[0])
                    let preferredCenterResponse, preferredCenterId
                    let centerId = req.query.centerId !== undefined && req.query.centerId !== "undefined" ? req.query.centerId : preferredCenterId

                    // Overriding centerid with centre code for reschedule flow alone
                    // To do replace center id based change to centre code across
                    let centreCode: string = req.query.centreCode || undefined
                    const cityCode: string = userContext.userProfile.cityId || undefined
                    if (isReschedule !== "true") {
                        preferredCenterResponse = await this.healthfaceService.getPatientPreferredCenter(customerid, bookingDetail.booking.patientId, product.productType, product.categoryId, productId, product.tenant)
                    }
                    if (!_.isEmpty(preferredCenterResponse)) {
                        preferredCenterId = preferredCenterResponse.centerResponse.id.toString()
                        centreCode = preferredCenterResponse.centerResponse.code
                    }
                    if (_.isEmpty(centerId) && _.isEmpty(centreCode)) {
                        if (isMultiCenterSupported) {
                            throw new CenterNotSelectedError().throwError(this.errorFactory, "Please select a center", userContext)
                        } else {
                            centerId = 1
                        }
                    }

                    if (preferredCenterId) {
                        center = preferredCenterResponse.centerResponse
                    } else {
                        let centerPromise
                        if (centreCode) {
                            centerPromise = this.healthfaceService.getCenterDetailsWithCenterCode(centreCode, product.tenant)
                        } else {
                            centerPromise = this.healthfaceService.getCenterDetails(centerId, product.tenant)
                        }
                        center = await centerPromise
                    }
                    const hasAtHome = req.query.hasAtHome

                    if (!_.isEmpty(bookingDetail) &&
                        isReschedule === "true" &&
                        bookingDetail.booking.subCategoryCode === "DIAGNOSTIC_TEST") {
                        testBookingId = bookingDetail.booking.id
                    }

                    params = {
                        centreCode: center.code,
                        bookingStartTimeEpoch: new Date().getTime(),
                        days: 15,
                        hasAtHome: hasAtHome ? hasAtHome : false,
                        productCodes: productCodes,
                        bookingId: testBookingId,
                        cityCode: cityCode,
                    }
                }
                const availableSlots: DiagnosticAvailableSlotsResponse = await this.healthfaceService.getDiagnosticAvailableSlotDetails(params, category, tenant)
                return new DatePickerViewV1(userContext, bookingId, productCodes[0], category, availableSlots, patientId, categoryCode, isReschedule, isMultiCenterSupported, productCodes, req.query.nextAction, req.query.offerIds, center, req.query.hasAtHome, isDiagPack, isBookingFromConsultation, cartId, addressId).datePicker
            }
        }

        @httpGet("/sgtBooking/:productId")
        async getSgtBookingData(req: express.Request): Promise<any> {
            const session: Session = req.session
            const productId: string = req.params.productId || req.query.productId
            const query = req.query
            const userContext: UserContext = req.userContext as UserContext
            const isCultLiveSGT = true
            if (!userContext.sessionInfo.isUserLoggedIn) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_LOGGED_IN_ERR, 400).build()
            }
            if (_.isNil(query.parentBookingId)) {
                const activePacks = await this.healthfaceService.getActiveBundleOrders(userContext.userProfile.userId, "BUNDLE",  "LIVE_SGT", false, "CULTFIT")
                if (!_.isEmpty(activePacks)) {
                    query.parentBookingId = activePacks[0].bookingId
                }
            }
            const parentBookingId: number = query.parentBookingId
            const gender: "Female" | "Male" = query.gender
            if (!query.patientId) {
                const selfPatient = await CareUtil.createOrUpdatePatient(userContext, this.userCache, this.healthfaceService, gender, "CULTFIT")
                query.patientId = selfPatient.id
            }

            let availableProductTags: AvailableProductTags[] = []
            if (isCultLiveSGT) {
                const sellableProducts = await this.cultPTService.getConsultationSellableProductsNotCached(userContext.userProfile.cityId, "LIVE_SGT")
                availableProductTags = await Promise.all(_.map(sellableProducts.consultationTypes, async consultationItem => {
                    const productDetail: ConsultationProduct = <ConsultationProduct>await this.catalogueService.getProduct(consultationItem.products[0].code)
                    return {
                        title: productDetail.subTitle,
                        productId: productDetail.productId,
                        selected: true,
                    }
                }))
            }
            let classByDateMap: { [id: string]: { [id: number]: any} } = {}
            let days: {id: string}[] = []
            let calorieCountMap = {}
            for (const product of availableProductTags) {
                const requestObject: TCInventoryRequest = {
                    bookingStartTimeEpoch: new Date().getTime(),
                    centerId: undefined,
                    parentBookingId: query.followUpParentBookingId || query.parentBookingId,
                    patientId: query.patientId,
                    followUpConsultationId: query.followUpBookingId || query.followUpConsultationId,
                    productCode: product.productId,
                    doctorId: query.doctorId ? Number(query.doctorId) : undefined,
                    excludePreferredDoctor: undefined,
                    strictGenderCheck: query.strictGenderCheck === "true",
                    daysToGo: 4
                }
                const datesToShow: TCAvailableSlotsResponse =   await this.healthfaceService.getTCAvailableSlotDetails(requestObject, "CULTFIT")
                calorieCountMap = datesToShow.calorieCountMap
                classByDateMap = CareUtil.getNewSgtBookingResponse(userContext, classByDateMap, product.productId, datesToShow, parentBookingId, query.patientId, availableProductTags, calorieCountMap)
            }
            const classByDateMapModified =  CareUtil.modifyResponse(userContext, classByDateMap)
            days = Object.keys(classByDateMapModified).map((key: string) => {
                return {
                    id: key
                }
            })

            return  {
                classByDateMap: classByDateMapModified,
                availableProductTags,
                header: {
                "title": "Choose your slot"
                },
                days,
                workoutFormatFilters: CareUtil.getworkoutFormatFilters(productId),
                workoutCategoryFilters: [],
                sgtClassFormatFilter: [
                    {
                        id: "ALL",
                        name: "ALL",
                        icon: "/image/tvAppIcons/all.png"
                    },
                    {
                        id: "CONS_CULT_LIVE_SGT_DF",
                        name: "DANCE",
                        icon: "/image/tvAppIcons/dance.png"
                    },
                    {
                        id: "CONS_CULT_LIVE_SGT_SnC",
                        name: "S&C",
                        icon: "/image/tvAppIcons/snc.png"
                    },
                    {
                        id: "CONS_CULT_LIVE_SGT_YOGA",
                        name: "YOGA",
                        icon: "/image/tvAppIcons/yoga.png"
                    },
                    {
                        id: "CONS_CULT_LIVE_SGT_BOXING",
                        name: "BOXING",
                        icon: "/image/tvAppIcons/boxing.png"
                    },
                    {
                        id:  "CONS_CULT_LIVE_SGT_HRX",
                        name: "HRX",
                        icon: "/image/tvAppIcons/boxing.png"
                    }
                ],
                timeSlotOptions: [
                    {
                        "slot": "morning",
                        "icon": "morning",
                        "displayText": "Morning",
                        "startTime": "06:00:00"
                    },
                    {
                        "slot": "evening",
                        "icon": "evening",
                        "displayText": "Evening",
                        "startTime": "16:00:00"
                    }
                ],
                notificationWidget: {
                    "widgetType": "NOTIFICATION_WIDGET",
                    "items": []
                },
                emptyClassesMessage: "Classes aren't yet scheduled for this date. Do stay tuned."
            }
        }

        @httpGet("/consultNow/:productId")
        async consultNow(req: express.Request): Promise<TCAvailableSlotsResponse> {
            const datesToShow: TCAvailableSlotsResponse = await this.healthfaceService.getTCAvailableSlotDetails(this.getTCInventoryRequestParams(req.params, req.query))
            if (_.isEmpty(datesToShow.formattedAvailableSlotResponseList)) {
                throw this.errorFactory.withCode(ErrorCodes.CARE_NO_SLOTS_AVAILABLE_ERR, 400).withDebugMessage("No slots are available").build()
            }
            datesToShow["consultationMode"] = "ONLINE"
            return datesToShow
        }

        @httpGet("/cancelTCOrder/:tcBookingId")
        async cancelTCOrder(req: express.Request): Promise<any> {
            const tcBookingId: string = req.params.tcBookingId
            const productId = req.query.productId
            const userContext = req.userContext as UserContext
            let product: ConsultationProduct
            if (productId) {
                product = <ConsultationProduct>await this.catalogueService.getProduct(productId)
            }
            const tenant = product ? product.tenant : CareUtil.getHealthfaceTenant(undefined, undefined, userContext.sessionInfo.orderSource)
            const bookingDetail: BookingDetail = await this.healthfaceService.getBookingDetailForBookingId(Number(tcBookingId), tenant)
            const order: Order = await this.omsApiClient.getOrder(bookingDetail.booking.cfOrderId)
            const paymentdata: PaymentData = order.payments.find((x) => x.status === "paid")
            const prePaymentData: PrePaymentData = order.prePayments.find((x) => x.status === "paid")
            const paymentAmount: number = paymentdata ? paymentdata.amount : 0
            const prePaymentAmount: number = prePaymentData ? prePaymentData.amount : 0
            const refundAmount: number = paymentAmount + prePaymentAmount
            const productSnapshot = order.productSnapshots.find(product => product.productId === bookingDetail.booking.productCode)
            const isDiagnosticsOrder = bookingDetail?.booking?.categoryCode === "DIAGNOSTICS"
            const refunds = isDiagnosticsOrder ? undefined : [
                {
                    productId: productSnapshot && productSnapshot.productId ? productSnapshot.productId : order.products[0].productId,
                    quantity: productSnapshot && productSnapshot.quantity ? productSnapshot.quantity : order.products[0].quantity,
                    refundAmount: refundAmount
                }
            ]
            this.logger.info("Attempting to cancel order at OMS", {orderId: order.orderId})
            const response = await this.omsApiClient.refundOrderOMS(order.orderId, refunds, "CUSTOMER_CANCELLED", false)
            this.logger.info("Cancel order at OMS results", {response})
            const isLivePTProduct = product && CareUtil.isLivePTProduct(product)
            const isLiveSGTProduct = product && CareUtil.isLiveSGTProduct(product)
            if (!_.isEmpty(order.offersApplied) && !(isLiveSGTProduct || isLivePTProduct)) {
                if (order.offersVersion === 3) {
                    await this.offerServiceV3.revertOffersForOrder(req.session.userId, order.orderId, order.offersApplied)
                } else {
                    const invalidateOfferPromises = _.map(order.offersApplied, offer => {
                        return eternalPromise(this.offerService.invalidateOfferConsumptionByCount(req.session.userId, offer, 1))
                    })
                    await Promise.all(invalidateOfferPromises)
                }
            }
            if (refundAmount === 0) {
                return { message: "Booking Cancelled" }
            } else {
                return { message: "Booking Cancelled, Refund Initated" }
            }
        }

        // TODO : remove this api if not used in web, this is not being used in app
        @httpGet("/getAllPatients")
        async getAllPatients(req: express.Request): Promise<Patient[]> {
            const session: Session = req.session
            const userId = session.userId
            return this.healthfaceService.getAllPatients(userId)
        }

        @httpPost("/createPatient")
        async createPatient(req: express.Request): Promise<Patient> {
            const session: Session = req.session
            const patient: Patient = req.body
            const userContext = req.userContext as UserContext
            patient["curefitUserId"] = session.userId
            if (patient.name !== undefined) {
                patient.name = capitalizeFirstLetter(patient.name)
            }
            if (patient.firstName !== undefined) {
                patient.firstName = capitalizeFirstLetter(patient.firstName)
            }
            if (patient.lastName !== undefined) {
                patient.lastName = capitalizeFirstLetter(patient.lastName)
            }
            if (patient.emergencyContactName !== undefined) {
                patient.emergencyContactName = capitalizeFirstLetter(patient.emergencyContactName.trim())
            }
            if (patient.guardianName !== undefined) {
                patient.guardianName = capitalizeFirstLetter(patient.guardianName.trim())
            }
            // temp fix only, once app is fixed remove this
            if (patient.relationship === undefined || _.isEmpty(patient.relationship)) {
                patient.relationship = "Self"
            }
            if (!patient.phoneNumber) {
                patient.phoneNumber = null
            }
            if (!patient.emailId) {
                patient.emailId = null
            }
            // Replicate guardian details in emergency details if emergency details are not present (for care)
            if (_.isEmpty(patient.emergencyContactName)) {
                patient.emergencyContactName = patient.guardianName
            }
            if (_.isEmpty(patient.emergencyPhoneNumber)) {
                patient.emergencyPhoneNumber = patient.guardianphoneNumber
            }
            if (_.isEmpty(patient.emergencyContactRelation)) {
                patient.emergencyContactRelation = patient.guardianRelation
            }
            this.logger.info(`Patient Creation relationShip:${patient?.relationship} Gender: ${patient?.gender}, appversion: ${userContext?.sessionInfo?.appVersion} userAgent: ${userContext?.sessionInfo?.userAgent} osName: ${userContext?.sessionInfo?.osName} cpVersion: ${userContext?.sessionInfo?.cpVersion}`)
            return this.healthfaceService.createPatient(patient, session.userId)
        }

        @httpPost("/updatePatient")
        async updatePatient(req: express.Request): Promise<Patient> {
            const session: Session = req.session
            const patient: Patient = req.body
            const userContext = req.userContext as UserContext
            patient["curefitUserId"] = session.userId
            if (patient.name !== undefined) {
                patient.name = capitalizeFirstLetter(patient.name)
            }
            if (patient.firstName !== undefined) {
                patient.firstName = capitalizeFirstLetter(patient.firstName)
            }
            if (patient.lastName !== undefined) {
                patient.lastName = capitalizeFirstLetter(patient.lastName)
            }
            if (patient.emergencyContactName !== undefined) {
                patient.emergencyContactName = capitalizeFirstLetter(patient.emergencyContactName.trim())
            }
            if (patient.guardianName !== undefined) {
                patient.guardianName = capitalizeFirstLetter(patient.guardianName.trim())
            }
            if (!patient.phoneNumber) {
                patient.phoneNumber = null
            }
            if (!patient.emailId) {
                patient.emailId = null
            }
            // Replicate guardian details in emergency details if emergency details are not present (for care)
            if (_.isEmpty(patient.emergencyContactName)) {
                patient.emergencyContactName = patient.guardianName
            }
            if (_.isEmpty(patient.emergencyPhoneNumber)) {
                patient.emergencyPhoneNumber = patient.guardianphoneNumber
            }
            if (_.isEmpty(patient.emergencyContactRelation)) {
                patient.emergencyContactRelation = patient.guardianRelation
            }
            this.logger.info(`Patient Update relationShip:${patient?.relationship} Gender: ${patient?.gender}, appversion: ${userContext?.sessionInfo?.appVersion} userAgent: ${userContext?.sessionInfo?.userAgent} osName: ${userContext?.sessionInfo?.osName} cpVersion: ${userContext?.sessionInfo?.cpVersion}`)
            return this.healthfaceService.updatePatient(patient, session.userId)
        }

        @httpGet("/teleconsultation")
        @httpGet("/teleconsultation/:productId")
        async getTCDetailsPage(req: express.Request): Promise<TeleconsultationDetailsViewWeb | TeleconsultationDetailsView | ProductDetailPage | GP99ProductPage> {
            const session: Session = req.session
            const bookingId: number = req.query.tcBookingId
            const paramProductId: string = req.params.productId || req.query.productId
            const vertical: string = req.query.vertical || "CAREFIT"
            const productSlug: string = req.query.productSlug
            const userId = session.userId
            const deviceId = session.deviceId
            const userContext = req.userContext as UserContext
            const source = req.headers["user-agent"] as string
            const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "APP")
            const centerId: string = req.query.centerId
            const isVideoEnabled = req.query.isVideoEnabled === "true" ? true : false
            const isCallFlow = req.query.isCallFlow === "true" ? true : false
            const cityCode: string = (req.userContext as UserContext).userProfile.cityId
            const canChangeCenter: boolean = req.query.canChangeCenter && req.query.canChangeCenter === "true"
            let parentBookingId = req.params.parentBookingId || req.query.parentBookingId
            const wodId = req.params.wodId || req.query.wodId
            let patientId = req.params.patientId || req.query.patientId
            const startTime = req.params.startTime || req.query.startTime
            const focusAreaId = req.params.focusAreaId || req.query.focusAreaId
            const isPreBookingDetailPage = req.params.isPreBookingDetail || req.query.isPreBookingDetail
            const endTime = req.params.endTime || req.query.endTime


            let productId: string
            let slugProduct: ConsultationProduct
            let baseProduct: ConsultationProduct
            let tenant: HealthfaceTenant

            if (productSlug) {
                try {
                    slugProduct = await this.healthfaceService.getConsultationProductDetailsByUrlPath(productSlug, vertical === "CULTFIT" ? "CULTFIT" : undefined)
                } catch (err) {}

                if (!slugProduct) {
                    throw this.errorFactory.withCode(ErrorCodes.CARE_PRODUCT_DETAILS_NOT_FOUND_ERR, 404).withDebugMessage("Error Fetching Product Slug Details").build()
                }
            }

            productId = paramProductId ? paramProductId : slugProduct ? (slugProduct.productCode || slugProduct.productId) : ""
            baseProduct = <ConsultationProduct>await this.catalogueService.getProduct(productId)

            if (!baseProduct) {
                throw this.errorFactory.withCode(ErrorCodes.CARE_PRODUCT_DETAILS_NOT_FOUND_ERR, 404).withDebugMessage("Error Fetching Product Slug Details").build()
            }

            const isSupportGroupCons = CareUtil.isSupportGroupProduct(baseProduct)
            tenant = CareUtil.getCareProductTenant(baseProduct)

            let bookingDetailPromise: Promise<BookingDetail>
            let bannerWidgetPromise: Promise<WidgetWithMetric>
            let consultationPackBannerWidgetPromise: Promise<WidgetWithMetric>
            let mrnFormConfigPromise: Promise<MRNFormResponse>
            let patientListPromise,
                feedbackPromise,
                userPromise: Promise<User>,
                followupProductsPromise: Promise<ConsultationProduct[]>,
                packageProductPromise,
                centers: Center[] = [],
                doctors: Doctor[] = [],
                consultationInstructionsPromise: Promise<ConsultationInstructionResponse[]>,
                centerInfoPromise: Promise<Center>,
                consultationOffer,
                patientsList: Patient[],
                covidVitalMetricInfoPromise: Promise<ICovidMetricsInfoResponse>,
                reportIssueDataPromise: Promise<{
                    issuesMapPromise: Promise<Map<string, CustomerIssueType[]>>,
                    newReportIssueManageOption: ManageOptionPayload
                }>

            if (CareUtil.useNewGP99ProductFlow(userContext, baseProduct, bookingId)) {
                return this.gp99ProductPageViewBuilder.getProductPage(userContext, baseProduct, isCallFlow)
            }
            userPromise = this.userCache.getUser(req.session.userId)

            if (!_.isEmpty(parentBookingId) && !_.isEmpty(wodId) && isPreBookingDetailPage === "true") {
                if (_.isNil(patientId)) {
                    const patientsList = await this.healthfaceService.getAllPatients(userId)
                    const selfPatient = !_.isEmpty(patientsList) ? _.find(patientsList, patient => patient.relationship === "Self") : undefined
                    patientId = selfPatient.id
                }
                if (parentBookingId === undefined || parentBookingId === "undefined") {
                    parentBookingId = -1
                }

                return await this.sgtPreBookingDetailPageViewBuilder.buildView(userContext, startTime, productId, patientId, parentBookingId, focusAreaId, wodId, endTime)
            }
            if (!_.isEmpty(bookingId)) {
                bookingDetailPromise = this.healthfaceService.getBookingDetailForBookingId(bookingId, baseProduct.tenant, undefined, undefined, true, userContext.userProfile.cityId)
                patientListPromise = CareUtil.isCoupleTherapist(baseProduct.doctorType) ? this.healthfaceService.getAllPatients(userId) : undefined
                if ((CareUtil.isLivePTProduct(baseProduct) || CareUtil.isLiveSGTProduct(baseProduct)) && AppUtil.isNewLivePTBookingPageSupported(userContext)) {
                    return await this.livePTBookingDetailViewBuilder.getView(userContext, baseProduct, await bookingDetailPromise)
                }

                reportIssueDataPromise = (async () => {
                    let repostIssues: IssueDetailView[] = [],
                        issuesMapPromise: Promise<Map<string, CustomerIssueType[]>>,
                        reportIssueParams: IssueDetailParams,
                        newReportIssueManageOption: ManageOptionPayload
                    const bookingDetail = await bookingDetailPromise
                    if (AppUtil.isNewReportIssueSupported(userContext)) {
                        if (CareUtil.isCultPTProduct(baseProduct)) {
                            repostIssues = await this.issueBusiness.getPersonalTrainingSessionIssue(bookingDetail, userContext)
                        } else if (CareUtil.isSkinProduct(baseProduct)) {
                            repostIssues = await this.issueBusiness.getSkinSessionIssues(bookingDetail, userContext)
                        } else if (CareUtil.isLCProduct(baseProduct)) {
                            repostIssues = await this.issueBusiness.getLCSessionIssue(bookingDetail, userContext)
                        } else if (CareUtil.isLivePTProduct(baseProduct)) {
                            repostIssues = await this.issueBusiness.getLivePersonalTrainingSessionIssue(bookingDetail, userContext)
                        } else if (CareUtil.isSupportGroupProduct(baseProduct)) {
                            repostIssues = await this.issueBusiness.getSupportGroupSessionIssues()
                        } else if (CareUtil.isTherapyProduct(baseProduct)) {
                            reportIssueParams = this.issueBusiness.getConsulationIssueParams(bookingDetail)
                            repostIssues = await this.issueBusiness.getIssues(reportIssueParams.productType, reportIssueParams.productStates, reportIssueParams.meta, userContext)
                        } else {
                            reportIssueParams = this.issueBusiness.getConsulationIssueParams(bookingDetail)
                            repostIssues = await this.issueBusiness.getIssues(reportIssueParams.productType, reportIssueParams.productStates, reportIssueParams.meta, userContext)
                        }
                        newReportIssueManageOption = this.issueBusiness.toManageOptionPayload(repostIssues, true, "Need Help")
                    } else {
                        issuesMapPromise = this.CRMIssueService.getIssuesMap()
                    }
                    return { issuesMapPromise, newReportIssueManageOption }
                })()

                consultationInstructionsPromise = (async () => {
                    return AppUtil.isFromFlutterAppFlow(userContext)
                        ?  this.healthfaceService.getStaticInstructionsV3(baseProduct.productId,
                            baseProduct.doctorType,
                            CareUtil.getConsultationMode(baseProduct, await bookingDetailPromise),
                            CareUtil.getInstructionTenant(baseProduct)
                        )
                        : this.healthfaceService.getConsultationInstructionsV2(
                            baseProduct.productId,
                            baseProduct.doctorType,
                            CareUtil.getConsultationMode(baseProduct, await bookingDetailPromise),
                            CareUtil.getInstructionTenant(baseProduct)
                        )
                })()

                feedbackPromise = (async () => {
                    const bookingDetail = await bookingDetailPromise
                    return this.feedbackDao.findOne({ shipmentId: `${bookingId}-${bookingDetail.consultationOrderResponse.id}` })
                })()

                if (!isSupportGroupCons) {
                    followupProductsPromise = (async () => {
                        const bookingDetail = await bookingDetailPromise
                        if (CareUtil.followUpActionRequired(bookingDetail.consultationOrderResponse.consultationUserState, bookingDetail.consultationOrderResponse.followUpContext)) {
                            return this.getFollowupProducts(bookingDetail.consultationOrderResponse.followUpContext.productCodes)
                        }
                        return undefined
                    })()

                    bannerWidgetPromise = (async () => {
                        if (CareUtil.showHowToVideoCallBanner(userContext, baseProduct, await bookingDetailPromise)) {
                            return CareUtil.getBannerCarouselWidget(userContext, this.serviceInterfaces, CareUtil.getCareHowToJoinVideoCallWidgetId())
                        }
                        return undefined
                    })()

                    mrnFormConfigPromise = (async () => {
                        const bookingDetail = await bookingDetailPromise
                        if (bookingDetail?.consultationOrderResponse?.center?.id) {
                            return this.healthfaceService.getMRNFormConfig(bookingDetail.consultationOrderResponse.center.id)
                        }
                        return undefined
                    })()

                    covidVitalMetricInfoPromise = (async () => {
                        const bookingDetail = await bookingDetailPromise
                        return !AppUtil.isWeb(userContext) && CareUtil.isCovidSpecialist(baseProduct) ? this.healthfaceService.getCovidVitalMetricData(Number(userId), bookingDetail.booking.patientId) : undefined
                    })()
                }

                if (AppUtil.isNewTCPostBookingProductPageSupported(userContext, baseProduct)) {
                    return new TCPostBookingProductPageView().buildView({
                        userContext,
                        product: baseProduct,
                        mrnFormConfigPromise,
                        bookingDetailPromise,
                        bannerWidgetPromise,
                        followupProductsPromise,
                        feedbackPromise,
                        userPromise,
                        consultationInstructionsPromise,
                        reportIssueDataPromise,
                        pageConfig: this.tcDetailsPageConfig,
                        patientListPromise,
                        feedbackPageConfigV2Cache: this.feedbackPageConfigV2Cache,
                        logger: this.logger,
                        covidVitalMetricInfoPromise
                    })
                }
            } else {
                patientListPromise = this.healthfaceService.getAllPatients(userId)
                if (CareUtil.isGP99DoctorType(baseProduct.doctorType)) {
                    consultationPackBannerWidgetPromise = CareUtil.getBannerCarouselWidget(userContext, this.serviceInterfaces, CareUtil.getCareConsultationPacksBannerId(baseProduct.doctorType))
                }
            }

            centerInfoPromise = (async () => {
                if (baseProduct.consultationMode === "INCENTRE") {
                    const bookingDetail = await bookingDetailPromise
                    if (!_.isEmpty(bookingDetail)) {
                        return bookingDetail.consultationOrderResponse.center
                    } else if (CareUtil.isNotCareProduct(baseProduct)) {
                        return await this.healthfaceService.getCenterDetails(1, baseProduct.tenant)
                    }
                }
                return undefined
            })()
            packageProductPromise = this.healthfaceService.getProductInfoDetailsCached("CONSULTATION", undefined, productId, baseProduct.tenant)
            if (centerId && CareUtil.isNewCenterSpecifiFlowSupported(userContext)) {
                consultationOffer = await this.offerService.getCareCenterOffers({
                    productType: "CONSULTATION",
                    userId,
                    cityId: userContext.userProfile.cityId,
                    deviceId,
                    productIds: [productId],
                    centerIds: [centerId],
                    source: "CUREFIT_APP"
                })
            } else {
                // TODO add check for not cancelable things if payment is manual/from center
                consultationOffer = await CareWidgetUtil.getCareProductOffersFromUserContext(userContext, "CONSULTATION", [productId], AppUtil.callSourceFromContext(userContext), this.serviceInterfaces, true)
            }
            if (patientListPromise) {
                patientsList = await patientListPromise
            }
            const user: User = await userPromise
            if (!bookingDetailPromise) {
                centers = await this.healthfaceService.getAllCenters(baseProduct.productType, baseProduct.categoryId, productId, tenant, cityCode)
                doctors = await this.healthfaceService.getAllDoctorsCached(baseProduct.doctorType, undefined, undefined, tenant)
            }
            const { newReportIssueManageOption, issuesMapPromise } = reportIssueDataPromise ? await reportIssueDataPromise : {
                newReportIssueManageOption: this.issueBusiness.toManageOptionPayload([], true, "Need Help"),
                issuesMapPromise: this.CRMIssueService.getIssuesMap()
            }
            const isZoomWebSDKEnabled = await CareUtil.isZoomWebSDKEnabled(userAgent, this.hamletBusiness, userContext)
            const isTwilioSupportedForSGT = await AppUtil.isLiveSGTTwilioSupported(userContext, this.hamletBusiness)
            return userAgent === "MBROWSER" || userAgent === "DESKTOP"
                ? TeleconsultationDetailsViewWeb.getView(source, isVideoEnabled, user, session.isNotLoggedIn, baseProduct, this.tcDetailsPageConfig, <ConsultationSellableProduct>(await packageProductPromise)[0].baseSellableProduct, await bookingDetailPromise, newReportIssueManageOption, await issuesMapPromise, await centerInfoPromise, session.sessionData.cityId, isZoomWebSDKEnabled, this.logger, consultationOffer, patientsList, await feedbackPromise, this.feedbackPageConfigV2Cache, await followupProductsPromise, userContext, doctors, centers, centerId, canChangeCenter, await consultationInstructionsPromise, isTwilioSupportedForSGT)
                : TeleconsultationDetailsView.getView(
                    userContext,
                    user,
                    session.isNotLoggedIn,
                    baseProduct,
                    this.tcDetailsPageConfig,
                    <ConsultationSellableProduct>(await packageProductPromise)[0].baseSellableProduct,
                    await bookingDetailPromise,
                    newReportIssueManageOption,
                    await issuesMapPromise,
                    await centerInfoPromise,
                    session.sessionData.cityId,
                    consultationOffer,
                    patientsList,
                    await feedbackPromise,
                    this.feedbackPageConfigV2Cache,
                    await followupProductsPromise,
                    doctors,
                    centers,
                    centerId,
                    canChangeCenter,
                    await consultationInstructionsPromise,
                    bannerWidgetPromise ? await bannerWidgetPromise : undefined,
                    this.careBusiness,
                    mrnFormConfigPromise ? await mrnFormConfigPromise : undefined,
                    consultationPackBannerWidgetPromise ? await consultationPackBannerWidgetPromise : undefined
                )
        }

        @httpPost("/reschedule/:bookingId")
        async rescheduleBooking(req: express.Request): Promise<ConfirmationView> {
            let bannerWidgetPromise: Promise<WidgetWithMetric>
            const reqBody: TCBookingRequest = req.body
            reqBody.cityCode = req.session.sessionData.cityId
            const bookingId: number = req.params.bookingId
            const userContext: UserContext = req.userContext
            const source = req.params.source ? req.params.source : "APP"
            const user = await this.userCache.getUser(userContext.userProfile.userId)
            const product: ConsultationProduct = <ConsultationProduct>await this.catalogueService.getProduct(req.body.code)
            const response: BookingDetail = await this.healthfaceService.resheduleBooking(reqBody, bookingId, source, product.tenant)
            // need to get info for booking as resheduleBooking response is not giving all necessary info (chat context etc)
            const bookingDetail: BookingDetail = await this.healthfaceService.getBookingDetailForBookingId(Number(response.booking.id), product.tenant, true)
            const consultationInstruction = await this.healthfaceService.getConsultationInstructionsV2(
                product.productId,
                product.doctorType,
                CareUtil.getConsultationMode(product, bookingDetail),
                CareUtil.getInstructionTenant(product)
            )
            const patientsList = CareUtil.isCoupleTherapist(product.doctorType) ? await this.healthfaceService.getAllPatients(userContext.userProfile.userId) : []
            if (CareUtil.showHowToVideoCallBanner(userContext, product, bookingDetail)) {
                bannerWidgetPromise = CareUtil.getBannerCarouselWidget(userContext, this.serviceInterfaces, CareUtil.getCareHowToJoinVideoCallWidgetId())
            }
            const showFeedbackWidget = !AppUtil.isWeb(userContext) && CareUtil.isTherapyOnlyDoctorType(product.doctorType) && CareUtil.getSeekingReasonSegment(product.doctorType) ?
                _.isEmpty(await this.serviceInterfaces.segmentService.doesUserBelongToSegment(CareUtil.getSeekingReasonSegment(product.doctorType), userContext)) &&
                _.isEmpty(await this.ehrService.getPatientConsultationSubmittedReason(bookingDetail.consultationOrderResponse.patient.id.toString(), product.doctorType))
                : false
            let reasons: string[] = []
            if (showFeedbackWidget) {
                reasons = await this.healthfaceService.getSessionSeekingReasons(product.doctorType, CareUtil.getInstructionTenant(product))
            }
            if (CareUtil.getIfChronicCareFromUserContext(userContext)) {
                let consultationType = "DOCTOR"
                if (_.get(bookingDetail, "consultationOrderResponse.doctor.type") === "LIFESTYLE_COACH") {
                    consultationType = "COACH"
                }
                const resetNavigationStack = CareUtil.isSugarfitExperienceCenterProduct(product) || CareUtil.isSugarfitExperienceCenterDiagnosticProduct(product)
                return new ChronicCareConsultationRescheduleConfirmationView(userContext, consultationType, product?.productId, response.booking.id.toString(), resetNavigationStack)
            }
            const ConfirmationView = AppUtil.isNewTCCheckoutUISupported(userContext, product) ? TeleconsultationSingleConfirmationViewV2 : TeleconsultationSingleConfirmationView
            return new ConfirmationView(userContext, product, user, bookingDetail, this.tcDetailsPageConfig, undefined, undefined, undefined, consultationInstruction, bannerWidgetPromise ? await bannerWidgetPromise : undefined, undefined, patientsList, showFeedbackWidget, reasons)
        }

        @httpPost("/rescheduleDiagnostics/:category/:bookingId")
        async rescheduleDiagnosticsBooking(req: express.Request): Promise<ConfirmationView> {
            const reqBody: TestBookingRequest = req.body
            const bookingId: number = req.params.bookingId
            const category: string = req.params.category
            const customerid: string = req.session.userId
            const userContext = req.userContext as UserContext
            const cityId = userContext.userProfile.cityId
            const timezone = userContext.userProfile.timezone
            const tenant = CareUtil.getHealthfaceTenant(undefined, undefined, userContext.sessionInfo.orderSource)
            if (category === "AT_HOME_SLOT") {
                const addressid: string = req.body.addressId
                if (addressid) {
                    const userDetailAddress = await this.userBusiness.augmentStructuredAddress(customerid, addressid)
                    const latLong: LatLong = UserBusiness.toLatLng(userDetailAddress.latLong)
                    const isServiceable = await this.deliveryAreaService.checkLocationServiceability(latLong, userDetailAddress.structuredAddress.pincode, cityId, "DIAGNOSTICS")
                    if (!isServiceable.isLocationServiceable) {
                        throw new CareHomeSampleUnavailableError().throwError(this.errorFactory, "Location Not Serviceable - Apologies. We are yet to provide home collection to your address", userContext)
                    }
                    const deliveryArea = isServiceable.area
                    // hack to get values
                    const d1: DeliveryArea = clone(deliveryArea)
                    reqBody.userAddressInfo = {
                        pincode: userDetailAddress.structuredAddress.pincode,
                        locality: d1?.locality,
                        address: userDetailAddress.addressLine1 + "," + userDetailAddress.addressLine2,
                        landmark: userDetailAddress.addressLine2,
                        addressId: userDetailAddress.addressId,
                        cityCode: cityId,
                        localityCode: d1?.localityId,
                        latitude: latLong.lat,
                        longitude: latLong.long
                    }
                }
            }
            const response: BookingDetail = await this.healthfaceService.resheduleBooking(reqBody, bookingId, "APP", tenant)
            if (CareUtil.getIfChronicCareFromUserContext(userContext)) {
                return new ChronicCareDiagnosticsRescheduleConfirmationView(userContext)
            }
            // need to get info for booking as resheduleBooking response is not giving all necessary info (chat context etc)
            const bookingDetail: BookingDetail = await this.healthfaceService.getBookingDetail(Number(response.booking.id), undefined, undefined, undefined, undefined, tenant)
            const diagnosticProduct = <DiagnosticProduct>await this.catalogueService.getProduct(bookingDetail?.booking?.productCode)
            let rootBooking: BookingDetail
            if (bookingDetail.booking.rootBookingId && bookingDetail.booking.rootBookingId != -1) {
                rootBooking = await this.healthfaceService.getBookingDetail(bookingDetail.booking.rootBookingId, undefined, undefined, undefined, undefined, tenant)
            }
            return new DiagnosticSingleConfirmationView(bookingDetail, rootBooking, userContext)
        }

        @httpGet("/getChatAccessToken")
        async getChatAccessToken(req: express.Request): Promise<any> {
            const userContext = req.userContext as UserContext
            const isPTChatReq = req.params.isPTChat || req.query.isPTChat
            const isPTChat = isPTChatReq === "true" ? true : false
            const tenant: HealthfaceTenant = CareUtil.getHealthfaceTenant(undefined, undefined, userContext.sessionInfo.orderSource)
            let twilioAccessToken: TwilioAccessToken
            try {
                const twilioIdentity: string = req.session.userId
                if (isPTChat) {
                    twilioAccessToken = await this.cultPTService.getChatToken(twilioIdentity)
                } else {
                    twilioAccessToken = await this.healthfaceService.getChatToken(twilioIdentity, tenant)
                }
                const result = {
                    "twilioToken": twilioAccessToken.tokenString,
                    "attachmentTags": this.getAttachmentTags(),
                    "isPTChat": isPTChat
                }
                return result
            } catch (e) {
                // Throw error below 8.53 and false for 8.53 and above as it's handled
                if (userContext.sessionInfo.appVersion >= 8.53) {
                    this.logger.error("getChatAccessToken error: " + e)
                    return false
                } else {
                    throw e
                }
            }
        }

        @httpPost("/saveChatEventLog")
        async saveChatEventLog(req: express.Request): Promise<any> {
            const userId: string = req.session.userId
            const events = req.body
            if (events && events.length > 0 && events.length < 20) {
                const data: ChatEventLog = { userId, events }
                await this.chatEventLogDao.create(data)
                return true
            }
            return false
        }

        @httpGet("/getUploadAssetCredentials")
        async getUploadAssetResponse(req: express.Request): Promise<any> {
            const isPTChatReq = req.params.isPTChat || req.query.isPTChat
            const isPTChat = isPTChatReq === "true" ? true : false
            const patientId: string = req.query.patientId
            const fileName: string = req.query.fileName
            if (!fileName || !patientId) throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid request params").build()
            const mimeType = mime.lookup(fileName)
            if (mimeType === false) throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid file extension").build()
            if (isPTChat) {
                const resp = await this.cultPTService.getUploadAssetCredentials(patientId)
                return { ...resp, mimeType }
            }
            const resp = await this.healthfaceService.getUploadAssetCredentials(patientId)
            return { ...resp, mimeType }
        }

        @httpGet("/getDownloadAssetCredentials")
        async getDownloadAssetResponse(req: express.Request): Promise<any> {
            const isPTChatReq = req.params.isPTChat || req.query.isPTChat
            const isPTChat = isPTChatReq === "true" ? true : false
            const patientId: string = req.query.patientId
            const s3KeyName: string = req.query.s3KeyName
            if (!patientId || !s3KeyName) throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid request params").build()
            if (isPTChat) {
                return this.cultPTService.getDownloadAssetCredentials(patientId, s3KeyName)
            }
            return this.healthfaceService.getDownloadAssetCredentials(patientId, s3KeyName)
        }

        @httpGet("/getDownloadAssetUrl")
        async getDownloadAssetUrl(req: express.Request): Promise<any> {
            const isPTChatReq = req.params.isPTChat || req.query.isPTChat
            const isPTChat = isPTChatReq === "true" ? true : false
            const reference: string = req.query.reference
            const referenceId: string = req.query.referenceId
            const s3KeyName: string = req.query.s3KeyName
            if (!reference || !referenceId || !s3KeyName) throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid request params").build()
            if (isPTChat) {
                return this.cultPTService.getDownloadAssetUrl(reference, referenceId, s3KeyName)
            }
            return this.healthfaceService.getDownloadAssetUrl(reference, referenceId, s3KeyName)
        }

        @httpGet("/getVideoAccessToken")
        async getVideoAccessToken(req: express.Request): Promise<any> {
            const bookingId: number = req.query.bookingId
            const twilioIdentity: string = req.session.userId
            const roomName: string = req.query.roomName
            const userContext: UserContext = req.userContext
            const isChronicCareApp = CareUtil.getIfChronicCareFromUserContext(userContext)
            const tenant: HealthfaceTenant = !_.isNil(req.query.tenant) ? req.query.tenant : CareUtil.getHealthfaceTenant(undefined, undefined, userContext.sessionInfo.orderSource)
            const bookingInfo = await this.healthfaceService.getBookingDetail(bookingId, undefined, undefined, undefined, undefined, tenant)
            const consultationProduct = <ConsultationProduct>await this.catalogueService.getProduct(bookingInfo.booking.productCode)
            if (CareUtil.isSupportGroupProduct(consultationProduct)) {
                this.logger.error("Video access token fetch called for support group consultation")
                return undefined
            }
            const vertical = CareUtil.getVerticalForConsultation(consultationProduct.doctorType)
            const twilioAccessToken: TwilioAccessToken = await this.healthfaceService.getVidoToken(twilioIdentity, roomName, bookingId, tenant)
            const delayedInMinutes = bookingInfo.consultationOrderResponse.delayedInMinutes
            const countDownDurationInMinutes = 5
            const result = {
                "twilioToken": twilioAccessToken.tokenString,
                "attachmentTags": this.getAttachmentTags(),
                consultationTime: bookingInfo.consultationOrderResponse.startTime,
                endCallUrl: tenant === "TRANSFORM" ? AppActionUtil.transformCLP(userContext) : ActionUtil.teleconsultationSingle(userContext, bookingInfo.booking.productCode, consultationProduct.urlPath, bookingId.toString(), undefined, vertical),
                timerMin: delayedInMinutes ? delayedInMinutes : 5,
                timerMinInMilliSec: delayedInMinutes ? delayedInMinutes * 60000 : 300000,
                afterFiveMinTimerMsg: tenant === "TRANSFORM" ? "" : `${bookingInfo.consultationOrderResponse.doctor.name} is delayed. You can continue for few more minutes (or) reschedule to a different time slot`,
                fiveMinTimerMsg: delayedInMinutes ? `${bookingInfo.consultationOrderResponse.doctor.name} is completing a previous appointment. Your consultation/session should start within` : `${bookingInfo.consultationOrderResponse.doctor.name} should join the call within the next 5 mins. Please wait`,
                chatEnabled: tenant !== "TRANSFORM" && CareUtil.getChatEnabled(bookingInfo),
                consultationDuration: consultationProduct?.duration,
                hasPrescription: consultationProduct?.hasPrescription,
                countDownDuration: countDownDurationInMinutes,
                durationExceededText: "Consultation Time Exceeded"
            }
            return result
        }

        getAttachmentTags(): string[] { // todo change to take from config
            return [
                "Lab Report",
                "Prescription"
            ]
        }

        // not in use
        @httpGet("/callComplete")
        async callComplete(req: express.Request): Promise<CallCompletionView> {
            const doctorId: number = req.query.doctorId
            const doctor: Doctor = await this.healthfaceService.getDoctorDetails(doctorId)
            return new CallCompletionView(doctor)
        }

        @httpPost("/saveAccessPolicy")
        async saveAccessPolicy(req: express.Request): Promise<any> {
            let accessPolicy: AccessPolicy
            if (req.body.isPsychiatristConsent) {
                accessPolicy = {
                    action: "EDIT",
                    consentType: "USER",
                    ownerReferenceEntity: "PATIENT",
                    ownerReferenceEntityId: req.body.patientId,
                    principleReferenceEntity: "CF_USER",
                    principleReferenceEntityId: (req.userContext as UserContext).userProfile.userId,
                    resourceType: "CONSULTATION",
                    resourceTags: []
                }
            } else {
               const doctor = await this.healthfaceService.getDoctorDetails(req.body.doctorId)
               const doctorTypeLC = doctor.doctorTypes.find(item => ["LC"].includes(item.type.code))
               if (!_.isNil(doctorTypeLC)) {
                accessPolicy = {
                    action: "EDIT",
                    consentType: "USER",
                    ownerReferenceEntity: "PATIENT",
                    ownerReferenceEntityId: req.body.patientId,
                    principleReferenceEntity: "AGENT",
                    principleReferenceEntityId: req.body.doctorId,
                    resourceType: "CONSULTATION",
                    resourceTags: [{
                        "resourceKey": "TENANT",
                        "resourceValue": "CARE3P"
                    }, {
                        "resourceKey": "TENANT",
                        "resourceValue": "CARE"
                    }]
                }
               } else {
               accessPolicy = {
                    action: "EDIT",
                    consentType: "USER",
                    ownerReferenceEntity: "PATIENT",
                    ownerReferenceEntityId: req.body.patientId,
                    principleReferenceEntity: "AGENT",
                    principleReferenceEntityId: req.body.doctorId,
                    resourceType: "CONSULTATION",
                    resourceTags: [{
                        "resourceKey": "TENANT",
                        "resourceValue": "MIND3P"
                    }, {
                        "resourceKey": "TENANT",
                        "resourceValue": "MIND"
                    }]
                }
            }
            }
            const response: any = await this.healthfaceService.saveAccessPolicy(accessPolicy)
            return response
        }

        @httpGet("/downloadAsset")
        async downloadAsset(req: express.Request): Promise<any> {
            const response = await this.healthfaceService.downloadPrescription(req.query.consultationId, "PRINT_PLAN", undefined, undefined)
            return Buffer.from(response).toString("base64")
        }

        @httpGet("/access-policy")
        async getAccessPolicy(req: express.Request): Promise<any> {
            const patientPromise = this.healthfaceService.getPatientDetails(req.query.patientId)
            const response: any = await this.healthfaceService.getAccessPolicy(req.query.patientId, Number((req.userContext as UserContext).userProfile.userId))
            return !_.isEmpty(response) || (await patientPromise)?.relationship === "Self" ? { result: true } : { result: false }
        }

        @httpPost("/logEvent")
        async sendEvent(req: express.Request): Promise<any> {
            const orderEvent: OrderEvent = req.body
            const userContext: UserContext = req.userContext as UserContext
            const orderSource: OrderSource = AppUtil.callSourceFromContext(userContext)
            return await this.healthfaceService.logOrderEvents(orderEvent, CareUtil.getHealthfaceTenant(undefined, undefined, orderSource))
        }

        @httpPost("/roomEventLog")
        async sendRoomEventLog(req: express.Request): Promise<any> {
            try {
                const userContext: UserContext = req.userContext as UserContext
                const orderSource: OrderSource = AppUtil.callSourceFromContext(userContext)
                await this.healthfaceService.addTwilioRoomEventLog(req.body, CareUtil.getHealthfaceTenant(undefined, undefined, orderSource))
            } catch (e) {
                this.logger.error("roomEventLog error: " + e)
                return false
            }
            return true
        }

        @httpPost("/roomEventLogBulk")
        async sendRoomEventLogBulk(req: express.Request): Promise<any> {
            const appVersion: string = req.headers["appversion"] as string
            const osName: string = req.headers["osname"] as string
            const codePushVersion: number = req.headers["codepushversion"] && req.headers["codepushversion"] !== "undefined" ? Number(req.headers["codepushversion"]) : 0
            const browsername: string = req.headers["browsername"] ? req.headers["browsername"] as string : ""
            const platform: string = browsername !== "" ? browsername : osName
            const version: string = appVersion + "." + codePushVersion
            const userContext: UserContext = req.userContext as UserContext
            const orderSource: OrderSource = AppUtil.callSourceFromContext(userContext)
            try {
                await this.healthfaceService.addTwilioRoomEventLogBulk(req.body, platform, version, CareUtil.getHealthfaceTenant(undefined, undefined, orderSource))
            } catch (e) {
                this.logger.error("roomEventLogBulk error: " + e)
                return false
            }
            return true
        }

        @httpPost("/roomEventLogBulkV2")
        async sendRoomEventLogBulkV2(req: express.Request): Promise<any> {
            const appVersion: string = req.headers["appversion"] as string
            const osName: string = req.headers["osname"] as string
            const codePushVersion: number = req.headers["codepushversion"] && req.headers["codepushversion"] !== "undefined" ? Number(req.headers["codepushversion"]) : 0
            const browsername: string = req.headers["browsername"] ? req.headers["browsername"] as string : ""
            const platform: string = browsername !== "" ? browsername : osName
            const version: string = appVersion + "." + codePushVersion
            const roomName: string = req.body.roomName
            const memberType: string = req.body.memberType
            const deviceModel: string = req.body.deviceModel
            const userContext: UserContext = req.userContext as UserContext
            const orderSource: OrderSource = AppUtil.callSourceFromContext(userContext)
            const tenant: HealthfaceTenant = !_.isNil(req.query.tenant) ? req.query.tenant : CareUtil.getHealthfaceTenant(undefined, undefined, orderSource)
            try {
                await this.healthfaceService.addTwilioRoomEventLogBulkV2(req.body.events, platform, version, roomName, memberType, deviceModel, tenant)
            } catch (e) {
                this.logger.error("roomEventLogBulkV2 error: " + e)
                return false
            }
            return true
        }

        @httpPost("/getChatHistory/:channelId")
        async getChatHistory(req: express.Request): Promise<ChatHistoryResponse> {
            const channelId: string = req.params.channelId
            const nextFetchMeta: string = req.body.nextFetchMeta
            const pageSize: number = req.body.pageSize
            const chatHistoryResponse: ChatHistoryResponse = this.getChatHistoryResponse(await this.healthfaceService.getChatHistory(channelId, nextFetchMeta, pageSize))
            return chatHistoryResponse
        }

        getChatHistoryResponse(chatHistory: ChatHistory): ChatHistoryResponse {
            const messages: ClientChatMessage[] = []
            chatHistory.messages.map(message => {
                messages.push({
                    body: message.body,
                    attributes: JSON.parse(message.attributes),
                    timestamp: message.dateCreated,
                    index: message.index,
                    author: message.from
                })
            })
            return {
                messages: messages,
                nextFetchMeta: chatHistory.nextFetchMeta
            }
        }


        @httpGet("/prescriptionView/:bookingId")
        async getPrescriptionView(req: express.Request): Promise<PrescriptionView> {
            const bookingId: number = Number(req.params.bookingId)
            const productId: string = req.query.productId
            const session: Session = req.session
            const userAgent: UserAgent = session.userAgent
            const userContext: UserContext = req.userContext
            const cityId: string = userContext.userProfile.cityId
            if (Number.isNaN(bookingId)) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Wrong booking id").build()
            }
            const bookingPromise = this.healthfaceService.getPrescription(bookingId, cityId)
            const bookingDetail: BookingDetail = await bookingPromise
            let followupProducts
            if (bookingDetail && bookingDetail.consultationOrderResponse && CareUtil.followUpActionRequired(bookingDetail.consultationOrderResponse.consultationUserState, bookingDetail.consultationOrderResponse.followUpContext)) {
                followupProducts = await this.getFollowupProducts(bookingDetail.consultationOrderResponse.followUpContext.productCodes)
            }

            const specialistRecommendation: ConsultationProduct[] = []
            const consultationSellableProduct: ConsultationSellableProductResponse = _.get(bookingDetail, "consultationOrderResponse.prescriptionInfo.sellableConsultationProductResponse", {})
            if (!_.isEmpty(consultationSellableProduct)) {
                if (!_.isEmpty(consultationSellableProduct.consultationTypes)) {
                    await _.map(consultationSellableProduct.consultationTypes, async consultationItem => {
                        const productCodes = consultationItem.products.map(product => product.code)
                        const productDetails = await this.catalogueService.getProducts(productCodes) as ConsultationProduct[]
                        if (!_.isEmpty(productDetails)) {
                            specialistRecommendation.push(...productDetails)
                        }
                        return productDetails
                    })
                }
            }
            let vitalInfo: VitalInfoItem[] = []
            if (!_.isNull(bookingDetail.booking.patientId) && !_.isNull(bookingDetail.consultationOrderResponse.id)) {
                const vitalInfoPromise = this.healthfaceService.getAllVitalForPatient(bookingDetail.booking.patientId, bookingDetail.consultationOrderResponse.id)
                vitalInfo = await vitalInfoPromise
            }
            return await new PrescriptionView().buildView(this.serviceInterfaces, req.userContext as UserContext, bookingDetail, productId, cityId, followupProducts, vitalInfo, specialistRecommendation)
        }


        @httpGet("/doctorList/:doctorType")
        async getDoctorList(req: express.Request): Promise<DoctorListingPageView> {
            const doctorType: DOCTOR_TYPE = req.params.doctorType
            const specialityCode: SPECIALITY_CODE = req.query.specialityCode
            const patientId: number = req.query.patientId
            const productId: string = req.query.productId
            const parentBookingId: number = req.query.parentBookingId
            const tenant: HealthfaceTenant = CareUtil.getHealthfaceTenant(undefined, doctorType)
            const doctors: Doctor[] = await this.healthfaceService.getAllDoctorsCached(doctorType, specialityCode, true, tenant)
            return new DoctorListingPageView(doctors, doctorType, specialityCode, patientId, productId, parentBookingId)
        }

        // unused check
        @httpGet("/packDetails/:productId")
        async getPackDetails(req: express.Request): Promise<CarePackDetailPageView> {
            return new CarePackDetailPageView(this.hcuDetailsPageConfig)
        }

        @httpGet("/sendPrescriptionOnEmail")
        async sendPrescriptionOnEmail(req: express.Request): Promise<any> {
            const bookingId: string = req.query.tcBookingId
            const consultationId: number = req.query.consultationId
            const emailId: string = req.query.emailId
            if (emailId) {
                const session: Session = req.session
                await this.healthfaceService.sendPrescriptionToCustomEmail(Number(session.userId), bookingId, consultationId, emailId)
            } else {
                await this.healthfaceService.sendPrescriptionOnEmail(bookingId, consultationId)
            }
            return { "result": true }
        }

        @httpGet("/sendPlanOnEmail")
        async sendPlanOnEmail(req: express.Request): Promise<any> {
            const bookingId: number = req.query.bookingId
            const appointmentId: number = req.query.appointmentId
            const emailId: string = req.query.emailId
            if (emailId) {
                const session: Session = req.session
                await this.healthfaceService.sendPlanToCustomEmail(Number(session.userId), bookingId, appointmentId, emailId)
            } else {
                await this.healthfaceService.sendPlanOnEmail(bookingId, appointmentId)
            }
            return { "result": true }
        }

        @httpGet("/sendReportOnEmail")
        async sendReportOnEmail(req: express.Request): Promise<any> {
            const testOrderId: string = req.query.testOrderId
            const emailId: string = req.query.emailId
            const response: any = await this.healthfaceService.sendReportOnEmail(testOrderId, emailId)
            return { "result": true, "message": "You should receive the email in 5 minutes", "debugMessage": response.debugMessage }
        }

        @httpGet("/getDiagnosticReport/:testOrderId")
        async getDiagnosticReport(req: express.Request): Promise<DiagnosticReportView> {
            const testOrderId: number = req.params.testOrderId
            const testId: number = req.query.testId
            const carefitOrderId: string = req.query.carefitOrderId
            const userContext: UserContext = req.userContext as UserContext
            let diagnosticsEmailedTestReport: DiagnosticEmailedTestReportResponse
            const queryParams = { diagnosticTestOrderId: testOrderId, pruneLevel: 1, testId: testId }
            const reportData: DiagnosticReport = await this.healthfaceService.getDiagnosticReport(queryParams)
            if (carefitOrderId) {
                diagnosticsEmailedTestReport = await this.healthfaceService.getDiagnosticsEmailedTestReport(carefitOrderId)
            }
            return this.diagnosticReportViewBuilder.buildDiagnosticReportView(userContext, testOrderId, reportData, testId, diagnosticsEmailedTestReport)
        }

        @httpGet("/getDiagnosticReportV2/:testOrderId")
        async getDiagnosticReportV2(req: express.Request): Promise<DiagnosticReportViewV2> {
            const testOrderId: number = req.params.testOrderId
            const testId: number = req.query.testId
            const carefitOrderId: string = req.query.carefitOrderId
            const userContext: UserContext = req.userContext as UserContext
            let diagnosticsEmailedTestReport: DiagnosticEmailedTestReportResponse
            const queryParams = { diagnosticTestOrderId: testOrderId, pruneLevel: 1, testId: testId }
            const reportData: DiagnosticReportV2 = await this.healthfaceService.getDiagnosticReportV2(queryParams)
            if (carefitOrderId) {
                diagnosticsEmailedTestReport = await this.healthfaceService.getDiagnosticsEmailedTestReport(carefitOrderId)
            }
            return this.diagnosticReportViewBuilderV2.buildDiagnosticReportView(userContext, testOrderId, reportData, testId, diagnosticsEmailedTestReport)
        }

        @httpPost("/getDiagnosticsTestDetails")
        async getDiagnosticsTestDetails(req: express.Request): Promise<DiagnosticsTestListPageView> {
            const userContext: UserContext = req.userContext as UserContext
            const session: Session = req.session
            const testCodes: string[] = req.body.testCodes
            const category: string = req.body.category
            const parentBookingId: string = req.body.parentBookingId
            const patientId: string = req.body.patientId
            const isWeb: boolean = AppUtil.isWeb(userContext)
            const source = AppUtil.callSourceFromContext(userContext)
            let selectedTestCodes: string[] = req.body.selectedDiagnosticTests
            let diagnosticTestInstruction
            const productInfos: HealthfaceProductInfo[] = await this.healthfaceService.getProductInfoDetails("DIAGNOSTICS", {
                productCodeCsv: testCodes.join(","),
                patientId: patientId,
                bookingId: parentBookingId,
                skipProductDetails: false,
            })
            const testsPromises = _.map(testCodes, async (testCode) => {
                return <DiagnosticProduct>(await this.catalogueService.getProduct(testCode))
            })
            const offerPromise = CareWidgetUtil.getCareProductOffersFromUserContext(userContext, "DIAGNOSTICS", testCodes, AppUtil.callSourceFromContext(userContext), this.serviceInterfaces, true)
            const {offerIds} = await this.offerServiceV3.getApplicableCartOffersForCare({
                userInfo: {userId: session.userId, deviceId: userContext.sessionInfo.deviceId},
                requiredOfferTypes: ["DIAGNOSTICS"],
                source,
                cityId: userContext.userProfile.cityId
            })
            const cartOffers = _.values((await this.offerServiceV3.getOffersByIds(offerIds)).data)
            const tests: DiagnosticProduct[] = await Promise.all(testsPromises)
            const offers = await offerPromise
            if (_.isEmpty(selectedTestCodes)) {
                selectedTestCodes = isWeb ? [] : testCodes
            }
            const cityCode = userContext.userProfile.cityId
            const sellerDetails = await this.diagnosticService.getDiagnosticSellerForCity(userContext.userProfile.cityId)
            const fulfilledDetails = userContext.sessionInfo.appVersion >= CARE_BRAND_PAGE_V2_SUPPORTED ? CareUtil.getFullfilledDetailsV2(userContext, sellerDetails) : CareUtil.getFulfilledDetails(sellerDetails, true)
            const sampleCollectionLocationResponse = await this.healthfaceService.getDiagnosticTestAllowedActionFromCollectionCodes(selectedTestCodes, parentBookingId, cityCode)
            if (sampleCollectionLocationResponse?.sampleCollectionLocations?.length === 1) {
                diagnosticTestInstruction = await this.healthfaceService.getProductInstructions("DIAGNOSTICS", selectedTestCodes.join(","), CareUtil.getDiagnosticTestType(sampleCollectionLocationResponse.sampleCollectionLocations[0]))
            }
            const bookingPromise = this.healthfaceService.getPrescription(Number(parentBookingId), userContext.userProfile.cityId)
            const bookingDetail: BookingDetail = await bookingPromise
            return new DiagnosticsTestListPageView(userContext, category, patientId, true, tests, parentBookingId, offers, cartOffers, productInfos, sampleCollectionLocationResponse, diagnosticTestInstruction, selectedTestCodes, fulfilledDetails, testCodes, bookingDetail)
        }

        @httpGet("/getDiagnosticCartListing")
        async getDiagnosticCartListing(req: express.Request): Promise<any> {
            const userContext: UserContext = req.userContext as UserContext
            const session: Session = req.session
            // XX
            const apiKey: string = req.headers["apikey"] as string
            const orderSource: OrderSource = AppUtil.callSource(apiKey)
            const userId = Number(userContext.userProfile.userId)
            const source = AppUtil.callSourceFromContext(userContext)
            const cartResponse = await this.diagnosticService.getCart(userId)
            const isNotLoggedIn = session.isNotLoggedIn
            const patientId = cartResponse?.cartMetaData?.patientId
            let productCodes = []
            productCodes = cartResponse?.diagnosticCartItems?.map((item) => { return item.productCode })
            if (_.isEmpty(productCodes)) {
                return new DiagnosticsCartListPageView(userContext, productCodes)
            }
            const recommendedAddonsResponse = await this.diagnosticService.getRecommendedAddons(productCodes.join(","))
            const {recommendedAddons, addonProductCodes } = CareUtil.processRecommendedAddons(recommendedAddonsResponse, productCodes)
            const combinedProductCodes = productCodes.concat(addonProductCodes)
            const offerPromise = await CareWidgetUtil.getCareProductOffersFromUserContext(userContext, "DIAGNOSTICS",
                combinedProductCodes, AppUtil.callSourceFromContext(userContext), this.serviceInterfaces, true)
            const {offerIds} = await this.offerServiceV3.getApplicableCartOffersForCare({
                userInfo: {userId: session.userId, deviceId: userContext.sessionInfo.deviceId},
                requiredOfferTypes: ["DIAGNOSTICS"],
                source,
                cityId: userContext.userProfile.cityId
            })
            const cartOffers = _.values((await this.offerServiceV3.getOffersByIds(offerIds)).data)
            const diagnosticTestInstructionPromise = this.healthfaceService.getProductInstructions("DIAGNOSTICS", productCodes.join(","), "AT_HOME")
            const productInfosPromise = this.healthfaceService.getProductInfoDetails("DIAGNOSTICS", {
                productCodeCsv: productCodes.join(","),
                patientId: cartResponse?.cartMetaData?.patientId,
                skipProductDetails: false,
            })
            const testsPromises = _.map(productCodes, async (productCode) => {
                return <DiagnosticProduct>(await this.catalogueService.getProduct(productCode))
            })
            const addonTestPromises = _.map(addonProductCodes, async (productCode) => {
                return <DiagnosticProduct>(await this.catalogueService.getProduct(productCode))
            })
            const patientsListPromise = this.healthfaceService.getAllPatients(userContext.userProfile.userId)
            const orderProducts = [{productId: productCodes[0] , quantity: 1, option: {productId: productCodes[0], productCode: productCodes[0], patientId: patientId}}]
            for (let i = 1; i < productCodes.length; i += 1) {
                orderProducts.push({
                  productId: productCodes[i],
                  quantity: 1,
                  option: {productId: productCodes[i], productCode: productCodes[i], patientId: patientId},
                })
            }
            const createOrderPayload: OrderCreate = {
                userId: session.userId,
                deviceId: session.deviceId,
                products: orderProducts,
                useOffersV2: true,
                source: orderSource,
                cityId: userContext.userProfile.cityId,
                dontCreateRazorpayOrder: true,
                useFitCash: true
            }
            const cartReviewResult = await this.omsApiClient.cartReview(createOrderPayload)
            const billingInfo = this.offerHelper.getOrderBilling(cartReviewResult?.order)
            return new DiagnosticsCartListPageView(userContext, productCodes, isNotLoggedIn, await productInfosPromise, await Promise.all(testsPromises), await offerPromise, cartOffers, await diagnosticTestInstructionPromise, cartReviewResult.order, billingInfo, cartResponse, recommendedAddons, await Promise.all(addonTestPromises), await patientsListPromise)
        }

        @httpGet("/updatePatientAssessment/:assessmentId")
        async updatePatientAssessment(req: express.Request): Promise<boolean> {
            const assessmentId: number = req.params.assessmentId
            const assessmentStatus: string = req.query.assessmentStatus
            try {
                const response = await this.healthfaceService.updatePatientAssessment(assessmentId, assessmentStatus)
                return true
            }
            catch (error) {
                this.logger.error("updatePatientAssessment error: " + error)
                return false
            }
        }

        @httpGet("/consultations")
        async getConsultations(req: express.Request): Promise<SupportListPageView> {
            const session: Session = req.session
            const userId = session.userId
            const appVersion: number = Number(req.headers["appversion"])
            const userContext: UserContext = req.userContext as UserContext
            const pageNumber = (req.query.pageNumber !== undefined && req.query.pageNumber !== "undefined") ? Number(req.query.pageNumber) : 0
            const pageSize = (req.query.pageSize !== undefined && req.query.pageSize !== "undefined") ? Number(req.query.pageSize) : 100
            const familyType = (req.query.consultationType !== undefined && req.query.consultationType !== "undefined") ? req.query.consultationType : undefined
            let consultations: Consultation[] = CollectionUtil.getNullSafeList(await this.healthfaceService.getConsultations(userId, pageNumber, pageSize, undefined, undefined, undefined, undefined, familyType))
            const consultationsPromise = _.map(consultations, async consultation => {
                if (CareUtil.followUpActionRequired(consultation.consultationUserState, consultation.followUpContext)) {
                    consultation.followupProducts = await this.getFollowupProducts(consultation.followUpContext.productCodes)
                }
                return consultation
            })
            consultations = await Promise.all(consultationsPromise)
            const consultationListPageView: SupportListPageView = await this.consultationListPageViewBuilder.buildConsultationListPageView(userContext, consultations, appVersion, session.sessionData.cityId, pageNumber, undefined, familyType)
            if (_.isEmpty(consultations)) {
                consultationListPageView.pageNumber = undefined
            } else {
                consultationListPageView.pageNumber = pageNumber + 1
            }
            return consultationListPageView
        }

        @httpGet("/chat/browse/:userType")
        async getChatListHistory(req: express.Request): Promise<ChatListHistoryPageView> {
            const session: Session = req.session
            const userId = session.userId
            const twilioUsertype = req.params.userType
            const isPTChatReq = req.params.isPTChat || req.query.isPTChat
            const isPTChat = isPTChatReq === "true" ? true : false
            if (isPTChat) {
                const chatList: ChatListHistoryResponse = await this.cultPTService.getChatListHistory(userId, twilioUsertype)
                return new ChatListHistoryPageView(req.userContext as UserContext, chatList, isPTChat)
            }
            const chatList: ChatListHistoryResponse = await this.healthfaceService.getChatListHistory(userId, twilioUsertype)
            const chatMessageViewListPromise = _.map(chatList.chatMessageViewList, async item => {
                item.doctorInfo = await this.ollivanderService.getDoctorDetails(item.peerId)
                return item
            })
            chatList.chatMessageViewList = await Promise.all(chatMessageViewListPromise)
            return new ChatListHistoryPageView(req.userContext as UserContext, chatList, isPTChat)
        }

        @httpGet("/diagnostictests")
        async getDiagnosticTests(req: express.Request): Promise<SupportListPageView> {
            const session: Session = req.session
            const userId = session.userId
            const appVersion: number = Number(req.headers["appversion"])
            const userContext: UserContext = req.userContext as UserContext
            const pageNumber = (req.query.pageNumber !== undefined && req.query.pageNumber !== "undefined") ? Number(req.query.pageNumber) : 0
            const pageSize = (req.query.pageSize !== undefined && req.query.pageSize !== "undefined") ? Number(req.query.pageSize) : 100
            let diagnostics: DiagnosticsTestOrderResponse[] = CollectionUtil.getNullSafeList(await this.healthfaceService.getDiagnosticTests(userId, pageNumber, pageSize))
            const diagnosticPromises = _.map(diagnostics, async diagnostic => {
                diagnostic.firstProductName = (await this.catalogueService.getProduct(diagnostic.productCodes[0])).title
                return diagnostic
            })
            diagnostics = await Promise.all(diagnosticPromises)
            const meDiagnosticTestListPageView: SupportListPageView = await this.meDiagnosticTestListPageViewBuilder.buildMeDiagnosticTestListPageView(userContext, diagnostics, appVersion, pageNumber)
            if (_.isEmpty(diagnostics)) {
                meDiagnosticTestListPageView.pageNumber = undefined
            } else {
                meDiagnosticTestListPageView.pageNumber = pageNumber + 1
            }
            return meDiagnosticTestListPageView
        }

        @httpPost("/diagnosticPhleboCall")
        async diagnosticPhleboCall(req: express.Request): Promise<any> {
            const userContext: UserContext = req.userContext as UserContext
            const phleboPhone: string = req.query.phleboPhone
            const userId = userContext.userProfile.userId
            const user = await this.userCache.getUser(userId)
            try {
                if (phleboPhone && user?.phone) {
                    const diagnosticCallContext = [{
                        phone: phleboPhone,
                        tags: { agentNumber: user.phone }
                    }]
                    const request: SendCampaignNotificationsRequest = {
                        campaignId: "CARE_HSC_CONTROL_TOWER_CALL",
                        creativeIds: ["CLICK_TO_CALL_HSC_CONTROL_TOWER_CALL"],
                        userContexts: diagnosticCallContext
                    }
                    // this.logger.info(`diagnosticPhleboCall userId:${userId} userPhoneNumber:${user.phone} phleboPhoneNumber:${phleboPhone} campaignRequest:${JSON.stringify(request)}`)
                    await this.campaignService.sendCampaignMessages(request)
                    return { success: true }
                }
            } catch {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Error in Diagnostic Phlebo Calling").build()
            }
        }

        @httpGet("/hcupacks")
        async getHCUPacks(req: express.Request): Promise<MeHCUPackPageView> {
            const session: Session = req.session
            const userId = session.userId
            const userContext: UserContext = req.userContext as UserContext
            const cityCode: string = userContext.userProfile.cityId
            const screeningPacks: BookingDetail[] = await this.healthfaceService.getBundlePacks(userId, "HCU,DIAG_PACK,HCU_PACK", 0, 100)
            const diagnosticPacks: BookingDetail[] = await this.healthfaceService.getPrescriptionLedDiagnostics(userId, "DIAGNOSTIC_TEST", 0, 20)
            let packs = screeningPacks.concat(diagnosticPacks)
            packs.sort((a, b) => { return a.booking.createdAt > b.booking.createdAt ? -1 : 1 })
            const packsPromise = _.map(packs, async pack => {
                if (pack && !_.isEmpty(pack.childBookingInfos)) {
                    const bundleBooking = pack.childBookingInfos.find(booking => booking.booking.subCategoryCode === "DIAG_PACK_OT" || booking.booking.subCategoryCode === "HCU_PACK_OT")
                    if (!_.isEmpty(bundleBooking) && !_.isEmpty(bundleBooking.stepInfosV2)) {
                        const isNotbookedState = bundleBooking.stepInfosV2.find(step => step.stepInfoType === "DIAGNOSTIC_TEST" && step.stepState === "NOT_BOOKED")
                        const bookingId = pack && pack.booking && pack.booking.id.toString()
                        if (isNotbookedState) {
                            pack.sampleCollectionLocationResponse = await this.healthfaceService.getDiagnosticTestAllowedActionFromCollectionCodes(bundleBooking.primaryActionV2.actionContext.testProductCodes, bookingId, cityCode)
                            if (pack.sampleCollectionLocationResponse?.sampleCollectionLocations?.length === 1) {
                                pack.diagnosticTestInstruction = await this.healthfaceService.getProductInstructions("DIAGNOSTICS", bundleBooking.primaryActionV2.actionContext.testProductCodes, CareUtil.getDiagnosticTestType(pack.sampleCollectionLocationResponse.sampleCollectionLocations[0]))
                            }
                        }
                    }
                }
                if (pack.booking.subCategoryCode === "DIAGNOSTIC_TEST") {
                    const rootBookingDetail = pack.booking.id !== pack.booking.rootBookingId && pack.booking.rootBookingId !== -1 ? await this.healthfaceService.getBookingDetail(Number(pack.booking.rootBookingId)) : undefined
                    if (rootBookingDetail?.booking?.categoryCode === "BUNDLE") {
                        return undefined
                    }
                    const product = await this.catalogueService.getProduct(pack.diagnosticsTestOrderResponse[0].productCodes[0])
                    const diagnosticTestTitle = product?.title || "Diagnostic Test"
                    const extraTestsTitle = (pack.diagnosticsTestOrderResponse[0].productCodes.length >= 2 && product?.title) ? " + " + (pack.diagnosticsTestOrderResponse[0].productCodes.length - 1) + (pack.diagnosticsTestOrderResponse[0].productCodes.length >= 3 ? " Tests" : " Test") : ""
                    pack.productTitle = diagnosticTestTitle + extraTestsTitle
                } else {
                    pack.productTitle = (await this.catalogueService.getProduct(pack.booking.productCode)).title
                }
                return pack
            })
            const initialPacks = await Promise.all(packsPromise)
            packs = _.filter(initialPacks, function (pack) { return !!pack })
            let reportActions: Map<string, any>
            reportActions = new Map()
            for (const pack of packs) {
                if (pack.booking.status !== "CANCELLED" && (pack.booking.subCategoryCode === "DIAG_PACK" || pack.booking.subCategoryCode === "HCU_PACK")) {
                    const bundleBooking = pack.childBookingInfos.find(booking => booking.booking.subCategoryCode === "DIAG_PACK_OT" || booking.booking.subCategoryCode === "HCU_PACK_OT" || booking.booking.subCategoryCode === "AI_MG_OT")
                    const stepInfo = bundleBooking.stepInfosV2.find(step => step.stepInfoType === "DIAGNOSTIC_TEST")
                    if (stepInfo && stepInfo.diagnosticsTestBookingInfo && stepInfo.diagnosticsTestBookingInfo.diagnosticsTestOrderResponse && !_.isEmpty(stepInfo.diagnosticsTestBookingInfo.diagnosticsTestOrderResponse[0])) {
                        const carefitOrderId = stepInfo.diagnosticsTestBookingInfo.diagnosticsTestOrderResponse[0].carefitOrderId
                        const reportAction = await this.healthfaceService.getReportAction({ carefitOrderId })
                        reportActions.set(carefitOrderId, reportAction)
                    }
                } else if (pack.booking.status !== "CANCELLED" && (pack.booking.subCategoryCode === "DIAGNOSTIC_TEST")) {
                    const carefitOrderId = pack.diagnosticsTestOrderResponse[0]?.carefitOrderId
                    const reportAction = await this.healthfaceService.getReportAction({ carefitOrderId })
                    reportActions.set(carefitOrderId, reportAction)
                }
            }
            return new MeHCUPackPageView(packs, true, req.userContext as UserContext, reportActions)
        }

        @httpGet("/mppacks")
        async getMPPacks(req: express.Request): Promise<MeMPPacksPageView> {
            const session: Session = req.session
            const userContext: UserContext = req.userContext as UserContext
            const userId = session.userId
            const appVersion: number = Number(req.headers["appversion"])
            let packs: BookingDetail[] = await this.healthfaceService.getBundlePacks(userId, "MP,MP_V2", 0, 100)
            const packsPromise = _.map(packs, async pack => {
                pack.productTitle = (await this.catalogueService.getProduct(pack.booking.productCode)).title
                return pack
            })
            packs = await Promise.all(packsPromise)
            return new MeMPPacksPageView(packs, appVersion, userContext)
        }

        @httpGet("/getInstructions/:type")
        async getInstructions(req: express.Request): Promise<InstructionItem[]> {
            const productType: CATEGORY_CODE = req.params.type
            const productCodeCsv: string = req.query.productCodeCsv
            const testType: TestType = req.query.testType
            return await this.healthfaceService.getProductInstructions(productType, productCodeCsv, testType)
        }

        private async getFollowupProducts(productCodes: string[]): Promise<ConsultationProduct[]> {
            return this.catalogueService.getProducts(productCodes) as Promise<ConsultationProduct[]>
        }

        @httpPost("/multiconsultationslots/:productCode")
        async getMultiConsultationSlots(req: express.Request): Promise<MultiConsultationDatePickerView> {
            const productCode: string = req.params.productCode
            const multiConsultationRequest: MultiConsultationRequest = req.body
            const index = multiConsultationRequest.productCodes.indexOf(productCode)
            const userContext = req.userContext as UserContext
            let startTime
            if (index === 0) {
                startTime = new Date().getTime()
            } else {
                const lastProductCode = multiConsultationRequest.productCodes[index - 1]
                const lastSelectedSlot = multiConsultationRequest.selectedTimeSlots[lastProductCode]
                startTime = lastSelectedSlot.endTime
            }
            const product = <ConsultationProduct>await this.catalogueService.getProduct(productCode)
            const parentBookingId = multiConsultationRequest.parentBookingId

            const preferredCenterResponse = await this.healthfaceService.getPatientPreferredCenter(req.session.userId, multiConsultationRequest.patientId, product.productType, product.categoryId, productCode, product.tenant)
            let preferredCenterId
            if (!_.isEmpty(preferredCenterResponse)) {
                preferredCenterId = preferredCenterResponse.centerResponse.id
            }
            const isMultiCenterSupported = CareUtil.isMultiCenterSupported(userContext, this.hcuDetailsPageConfig)
            let centerId = multiConsultationRequest.centerId !== undefined ? multiConsultationRequest.centerId : preferredCenterId
            if (_.isNil(centerId)) {
                if (isMultiCenterSupported) {
                    throw new CenterNotSelectedError().throwError(this.errorFactory, "Please select a center", userContext)
                } else {
                    centerId = 1
                }
            }

            let center: Center
            if (preferredCenterId) {
                center = preferredCenterResponse.centerResponse
            } else {
                const centerPromise = this.healthfaceService.getCenterDetails(centerId, product.tenant)
                center = await centerPromise
            }


            // update center id of query to pass over
            multiConsultationRequest.centerId = center.id

            const requestParams: TCInventoryRequest = {
                bookingStartTimeEpoch: startTime,
                daysToGo: 7,
                centerId: multiConsultationRequest.centerId,
                parentBookingId: parentBookingId,
                patientId: multiConsultationRequest.patientId,
                followUpConsultationId: -1,
                productCode
            }
            const datesToShowPromise = this.healthfaceService.getTCAvailableSlotDetails(requestParams)
            const productsPromise = this.catalogueService.getProducts(multiConsultationRequest.productCodes)
            return new MultiConsultationDatePickerView(userContext, await datesToShowPromise, productCode, multiConsultationRequest, await productsPromise, center, isMultiCenterSupported)
        }

        @httpPost("/softBook/:type")
        async softBook(req: express.Request): Promise<SoftBookingResponse> {
            const type = req.params.type
            const session: Session = req.session
            const reqBody: SoftBookingRequest = req.body
            const bookingReq: TCBookingRequest = {
                startTime: reqBody.startTime,
                endTime: reqBody.endTime,
                code: reqBody.productCode,
                centerId: reqBody.centerId ? reqBody.centerId : 1, // todo quick fix
                doctorId: reqBody.doctorId,
                cityCode: session.sessionData.cityId,
                productCode: reqBody.productCode,
            }
            const queryParams = {
                customerId: session.userId,
                patientId: reqBody.patientId,
                source: "APP",
                parentBookingId: reqBody.parentBookingId,
                productCode: reqBody.productCode,
                deviceId: session.deviceId
            }
            const bookingDetail = await this.healthfaceService.softbook(type, bookingReq, queryParams)
            return {
                bookingId: bookingDetail.booking.id
            }
        }

        @httpPost("/update/lastConsumedMessageIndex")
        async updateLastConsumedMessageIndex(req: express.Request): Promise<boolean> {
            const requestBody: ConsumedMessageRequest = req.body
            return await this.healthfaceService.updateLastConsumedMessageIndex(requestBody)
        }

        @httpPost("/multiconsultation/cancel")
        async cancelMultiConsultation(req: express.Request): Promise<boolean> {
            const bookingIds: string[] = req.body
            await this.cancelAllBookings(bookingIds)
            return true
        }

        private async cancelAllBookings(bookingIds: string[]): Promise<IRefundOrderResponse[]> {
            const bookingsPromise = _.map(bookingIds, async bookingId => {
                return await this.healthfaceService.getBookingDetail(Number(bookingId))
            })
            const bookings = await Promise.all(bookingsPromise)
            const cancelPromises = _.map(bookings, async booking => {
                const order = await this.omsApiClient.getOrder(booking.booking.cfOrderId)
                const paymentdata: PaymentData = order.payments.find((x) => x.status === "paid")
                const prePaymentData: PrePaymentData = order.prePayments.find((x) => x.status === "paid")
                const productSnapshot = order.productSnapshots.find(product => product.productId === booking.booking.productCode)
                const refunds: { productId: string, quantity: number, refundAmount: number }[] = []
                refunds.push({
                    productId: productSnapshot.productId,
                    quantity: productSnapshot.quantity,
                    refundAmount: (paymentdata || prePaymentData) ? productSnapshot.price.listingPrice : 0 // free orders
                })
                this.logger.info("Attempting to cancel order at OMS", {orderId: order.orderId})
                return await this.omsApiClient.refundOrderOMS(order.orderId, refunds, "customer requested cancellation", false)
            })
            return await Promise.all(cancelPromises)
        }

        @httpPost("/multiconsultation/reschedule/:bookingId")
        async reschedulemMulticonsultation(req: express.Request): Promise<ConfirmationView> {
            const reqBody: MultiConsultationRescheduleRequest = req.body
            let bannerWidgetPromise: Promise<WidgetWithMetric>
            const bookingId: number = req.params.bookingId
            const userContext = req.userContext
            const user = await this.userCache.getUser(userContext.userProfile.userId)
            if (!_.isEmpty(reqBody.dependentBookingIds)) { // cancel all dependent bookings
                await this.cancelAllBookings(reqBody.dependentBookingIds)
            }
            let action
            let isMPV2 = false
            const response: BookingDetail = await this.healthfaceService.resheduleBooking(reqBody, bookingId, "APP")
            // need to get info for booking as resheduleBooking response is not giving all necessary info (chat context etc)
            const bookingDetail: BookingDetail = await this.healthfaceService.getBookingDetail(Number(response.booking.id), undefined, undefined, undefined, undefined, undefined, true)
            const product = <ConsultationProduct>await this.catalogueService.getProduct(bookingDetail.booking.productCode)
            if (bookingDetail && bookingDetail.booking.rootBookingId && bookingDetail.booking.rootBookingId !== -1) {
                const parentBooking = await this.healthfaceService.getBookingDetail(bookingDetail.booking.rootBookingId)
                isMPV2 = parentBooking.booking.subCategoryCode === "MP_V2"
                action = isMPV2 ? ActionUtil.carefitbundle(parentBooking.booking.productCode, parentBooking.booking.subCategoryCode, parentBooking.booking.id.toString()) : undefined
            }
            const consultationInstruction = await this.healthfaceService.getConsultationInstructionsV2(
                product.productId,
                product.doctorType,
                CareUtil.getConsultationMode(product, bookingDetail),
                CareUtil.getInstructionTenant(product)
            )
            if (userContext.sessionInfo.userAgent === "APP" &&
                bookingDetail.consultationOrderResponse?.channel === "VIDEO" &&
                _.get(bookingDetail, "consultationOrderResponse.consultationProduct.consultationMode") === "ONLINE"
            ) {
                bannerWidgetPromise = CareUtil.getBannerCarouselWidget(userContext, this.serviceInterfaces, CareUtil.getCareHowToJoinVideoCallWidgetId())
            }
            const ConfirmationView = AppUtil.isNewTCCheckoutUISupported(userContext, product) ? TeleconsultationSingleConfirmationViewV2 : TeleconsultationSingleConfirmationView
            return new ConfirmationView(userContext, product, user, bookingDetail, this.tcDetailsPageConfig, undefined, isMPV2, action, consultationInstruction, bannerWidgetPromise ? await bannerWidgetPromise : undefined)
        }

        @httpGet("/centers")
        async getConsultationCenters(req: express.Request): Promise<CareCenterPageView> {
            const session: Session = req.session
            const userId: string = session.userId
            const productId = req.query.productId
            const userContext = req.userContext as UserContext
            const doctorId = req.query.doctorId
            const viewOnly = req.query.viewOnly === "true"
            const fromRecommendation = req.query.fromRecommendation === "true"
            const showCenterPricing = req.query.showCenterPricing === "true"
            const isNextStepCheckout = req.query.isNextStepCheckout
            const isNextValidateAddress = req.query.isNextValidateAddress
            const subCategoryCode = req.query.subCategoryCode
            const patientId = req.query.patientId
            let cityCode: string = userContext.userProfile.cityId
            let centerPricing: { [key: string]: { price: ProductPrice, offers: OfferV2[] } } = {}
            if (fromRecommendation) {
                let patientId = req.query.patientId
                const subCategoryCode = req.query.subCategoryCode
                // safe check if patientid is not coming in request
                if (!patientId && subCategoryCode === "MIND_THERAPY") {
                    const patientList = await this.healthfaceService.getAllPatients(userId)
                    const selfPatient = !_.isEmpty(patientList) ? _.find(patientList, patient => patient.relationship === "Self") : undefined
                    patientId = selfPatient ? selfPatient.id : undefined
                }
                if (patientId) {
                    const doctorTypes = req.query.doctorTypes ? (req.query.doctorTypes.includes(",") ? req.query.doctorTypes.split(",") : [req.query.doctorTypes]) : undefined
                    const centers = await this.healthfaceService.getCenterRecommendations(subCategoryCode, patientId, cityCode, doctorTypes, CareUtil.getHealthfaceTenant(subCategoryCode))
                    return this.careCenterViewBuilder.buildView(userContext, centers, undefined, false, fromRecommendation)
                }
            } else {
                const product = await this.catalogueService.getProduct(productId)
                const tenant: HealthfaceTenant = CareUtil.getCareProductTenant(product)
                // hack for ai_coach
                if (productId === "CONS_AI_MG_LC_ONLINE" || productId === "CONS_AI_MG_SUBS_LC_ONLINE") {
                    cityCode = "Bangalore"
                }
                const centers = await this.healthfaceService.getAllCenters(product.productType, product.categoryId, productId, tenant, cityCode, doctorId)
                this.sortCareCentersByLocation(userContext.sessionInfo, centers)
                if (centers && centers.length > 0 && showCenterPricing) {
                    const centerIds: string[] = centers && centers.map(center => {
                        return center.id.toString()
                    })
                    const offerInventoryRequestParams: CareCenterOfferRequestParams = {
                        userId: userContext.userProfile.userId,
                        cityId: userContext.userProfile.cityId,
                        deviceId: userContext.sessionInfo.deviceId,
                        productType: product.productType,
                        productIds: [product.productId],
                        centerIds,
                        source: "CUREFIT_APP"
                    }
                    const offerInventoryMap = {}
                    this.logger.info("CARE::DEBUG getCareCenterOffers attempted", { flow: "getConsultationCenters", request: offerInventoryRequestParams })
                    centerPricing = OfferUtil.getOfferAndPriceForCareCenter(product, centerIds, offerInventoryMap)
                }
                return this.careCenterViewBuilder.buildView(userContext, centers, product, viewOnly, false, showCenterPricing, centerPricing, isNextStepCheckout, patientId, isNextValidateAddress, subCategoryCode)
            }
        }

        @httpGet("/validateAddress")
        async getValidateAddress(req: express.Request): Promise<any> {
            const userContext = req.userContext as UserContext
            const userId = userContext.userProfile.userId
            const addressId = req.query.addressId
            const userDetailAddress = await this.userBusiness.augmentStructuredAddress(userId, addressId)
            const latLong: LatLong = UserBusiness.toLatLng(userDetailAddress.latLong)
            const pincode: string = userDetailAddress.structuredAddress.pincode
            const cityId = userContext.userProfile.cityId
            const serviceability = await this.deliveryAreaService.checkLocationServiceability(latLong, pincode, cityId, "DIAGNOSTICS")
            this.logger.info(`Validate Address Pre-Purchase userId:${userId} addressId: ${addressId} isServiceable:${serviceability.isLocationServiceable} lat: ${latLong.lat} long: ${latLong.long} pincode: ${pincode} cityId: ${cityId} appversion: ${userContext?.sessionInfo?.appVersion} userAgent: ${userContext?.sessionInfo?.userAgent} osName: ${userContext?.sessionInfo?.osName} cpVersion: ${userContext?.sessionInfo?.cpVersion}`)
            return { "result": serviceability.isLocationServiceable }
        }

        @httpPost("/updatePreferredCenter")
        async updatePreferredCenter(req: express.Request): Promise<boolean> {
            const session: Session = req.session
            const userId: string = session.userId
            const patientId = req.body.patientId
            const doctorType = req.body.doctorType
            const centerId = req.body.centerId || _.get(req.body, "centerInfo.id")
            const productId = req.body.productId
            if (productId) { // passed in app version 7.30
                const product = <ConsultationProduct>await this.catalogueService.getProduct(productId)
                return await this.healthfaceService.updatePatientPreferredCenter(userId, patientId, centerId, product.productType, product.categoryId, productId, CareUtil.getHealthfaceTenant(undefined, doctorType))
            } else {
                return await this.healthfaceService.updatePatientPreferredCenterByDoctorType(userId, patientId, centerId, doctorType, CareUtil.getHealthfaceTenant(undefined, doctorType))
            }
        }

        @httpGet("/patientCreationForm")
        async getPatientCreationFormData(req: express.Request): Promise<CreateUserFormPage> {
            const userId: string = req.session.userId
            const patientid: number = req.query.patientId
            const builderParams: FormPageBuilderParams = {
                relation: req.query.patientRelation,
                formUserType: req.query.formUserType,
                packId: req.query.packId,
                isMalePreselected: req.query.isMalePreselected
            }
            const patientDetails = patientid ? await this.healthfaceService.getPatientDetails(patientid) : undefined
            if (patientDetails != null && patientDetails.curefitUserId != userId) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_AUTHORIZED_TO_VIEW_PATIENT_DETAILS, 401).withDebugMessage("Not authorized to view patient details").build()
            }
            return this.patientFormViewBuilder.buildView(req.userContext as UserContext, builderParams, patientDetails)
        }

        /*  Practo APIs */
        @httpGet("/practo/doctor/availability")
        async getDoctorInventoryForPracto(req: express.Request): Promise<any> {
            const doctorId: number = req.query.doctorId
            const centerId: number = req.query.centerId
            const fromTime: number = req.query.fromTime
            const toTime: number = req.query.toTime

            const response: any = await this.healthfaceService.getPractoDoctorAvailability(doctorId, centerId, fromTime, toTime)
            return response
        }

        @httpPost("/practo/booking/cancel/:bookingId")
        async cancelBookingForPracto(req: express.Request): Promise<string> {
            const bookingId: number = req.params.bookingId

            const response: any = await this.healthfaceService.cancelPractoBooking(bookingId)
            return response
        }

        @httpPost("/practo/booking/reschedule/:bookingId")
        async rescheduleBookingForPracto(req: express.Request): Promise<string> {
            const requestBody: PractoRescheduleBookingRequest = req.body
            requestBody.bookingId = req.params.bookingId

            const response: any = await this.healthfaceService.reschedulePractoBooking(requestBody)
            return response
        }

        @httpGet("/practo/booking/status/:bookingId")
        async bookingStatusForPracto(req: express.Request): Promise<string> {
            const bookingId: number = req.params.bookingId

            const response: any = await this.healthfaceService.getPractoBookingStatus(bookingId)
            return response
        }

        @httpPost("/practo/booking/book")
        async bookAppointmentForPracto(req: express.Request): Promise<string> {
            const requestBody: PractoCreateBookingRequest = req.body

            const response: any = await this.healthfaceService.bookPractoAppointment(requestBody)
            return response
        }

        @httpGet("/practo/booking/fee")
        async getPractoBookingFee(req: express.Request): Promise<string> {
            const doctorId: number = req.query.doctorId
            const centerId: number = req.query.centerId
            const fromTime: number = req.query.fromTime
            const duration: number = req.query.duration
            const patientMobile: string = req.query.patientMobile
            const patientGender: string = req.query.patientGender
            const patientName: string = req.query.patientName

            const response: any = await this.healthfaceService.getPractoBookingFee(doctorId, centerId, fromTime, duration, patientMobile, patientGender, patientName)
            return response
        }

        /* Infini voice call back api */
        @httpGet("/infini/call-details")
        async infiniVoiceCallDetails(req: express.Request): Promise<string> {
            const response: any = await this.healthfaceService.infiniVoiceCallDetails(req.query)
            return response
        }

        @httpGet("/specialistListing")
        @httpPost("/specialistListing")
        async getSpecialistListing(req: express.Request): Promise<DoctorListingPageViewV1 | ProductDetailPage> {
            const userContext = req.userContext as UserContext
            const session: Session = req.session
            const appliedFilters = req.body.appliedFilters
            const userAgent: UserAgent = session.userAgent
            const userId = session.userId
            const deviceId = session.deviceId
            const productSlug = req.query.productSlug
            const specialityCode: SPECIALITY_CODE = req.query.specialityCode
            const showSearchWidget = req.query.showSearchWidget
            const patientId: number = req.query.patientId
            const parentBookingId: number = req.query.parentBookingId
            const isReschedule = req.query.isReschedule === "true"
            const showPrice = req.query.showPrice === "true"
            const isDoctorSearch = req.query.isDoctorSearch === "true" || Boolean(req.query.productSlug)
            let centerId: number = req.query.centerId
            let offerPromise, centersPromise, isPriceAvailableInListingApi = false
            const patientListPromise = this.healthfaceService.getAllPatients(userId)
            const showCenterSelection = req.query.showCenterSelection === "true" || false
            const isReplacePage: boolean = req.query.replacePage && req.query.replacePage === "true"
            let product: ConsultationProduct
            let specialistContentSection: IContentSection[]
            let productId: string = req.query.productId
            if (productId) {
                product = <ConsultationProduct>await this.catalogueService.getProduct(productId)
            } else if (productSlug) {
                const productInfo: ConsultationProduct = await this.healthfaceService.getConsultationProductDetailsByUrlPath(productSlug)

                if (!productInfo) {
                    throw this.errorFactory.withCode(ErrorCodes.CARE_DOCTOR_DETAILS_NOT_FOUND_ERR, 404).withDebugMessage("Error Fetching Doctor Slug Details").build()
                }

                specialistContentSection = productInfo.infoSection ? productInfo.infoSection.contentSection : null
                productId = productInfo.productCode || productInfo.productId
                product = <ConsultationProduct>await this.catalogueService.getProduct(productId)
            }
            if (_.isEmpty(product) || !product) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Product Details is Missing").build()
            }
            // Used in Specialist recommendation flow - Mind Therapy
            if (showCenterSelection && patientId && centerId) {
                const preferredCenterResponse = await this.healthfaceService.getPatientPreferredCenter(userId, patientId, product.productType, product.categoryId, productId, product.tenant)
                if (!_.isEmpty(preferredCenterResponse)) {
                    centerId = preferredCenterResponse.centerResponse.id ? preferredCenterResponse.centerResponse.id : centerId
                }
            }
            const doctorType = product?.doctorType || req?.query?.doctorType
            let doctorTypes: DOCTOR_TYPE[]
            // Adding custom doctor type for therapy alone
            const isMindTherapyFlow = CareUtil.isTherapyOnlyDoctorType(doctorType)
            doctorTypes = doctorType ? (doctorType.includes(",") ? doctorType.split(",") : [doctorType]) : [product.doctorType]
            const tenant: HealthfaceTenant = CareUtil.getCareProductTenant(product)
            if (CareUtil.isPTDoctorType(product.doctorType)) {
                const doctorsList: Doctor[] = await this.healthfaceService.getAllDoctorDetailsWithAvailability({
                    types: doctorTypes,
                    cityCode: userContext.userProfile.cityId,
                    speciality: specialityCode,
                    centerId
                }, CareUtil.getHealthfaceTenant(undefined, doctorTypes[0]))
                const doctors: Doctor[] = doctorsList ? [...new Set(doctorsList)] : []
                return new DoctorListingPageViewV1(
                    userContext,
                    product,
                    doctors,
                    req.query.doctorType,
                    await patientListPromise,
                    productId,
                    showSearchWidget,
                    patientId,
                    parentBookingId,
                    isReschedule,
                    isReplacePage,
                    showCenterSelection,
                    centerId,
                    false,
                    false,
                    { productId }
                )
            }
            const osName = userContext.sessionInfo.osName
            const codepushVersion = userContext.sessionInfo.cpVersion
            const user = await this.userCache.getUser(userId)
            const appVersion = userContext.sessionInfo.appVersion
            const hideInfoWidget = req?.query?.hideInfoWidget === "true" || AppUtil.isFromFlutterAppFlow(userContext)
            const isUnifiedDoctorUISupported = AppUtil.isUnifiedDoctorCardUISupported(userContext)
            const isFiltersSupported = userContext.sessionInfo.userAgent !== "APP" || CareUtil.isNewFilterSupported(osName, appVersion, codepushVersion, user.isInternalUser)
            const orderSource = userContext.sessionInfo.orderSource
            const healthfaceTenant = CareUtil.getHealthfaceTenant(undefined, doctorTypes[0], orderSource)
            const cityCode: string = (req.userContext as UserContext).userProfile.cityId
            const cityFilterSource = req.query.city ? req.query.city : (appliedFilters && appliedFilters.city) ? appliedFilters.city : undefined
            const languageFilterSource = req.query.language ? req.query.language : (appliedFilters && appliedFilters.language) ? appliedFilters.language : undefined
            const availabilityFilterSource = req.query.availability ? req.query.availability : (appliedFilters && appliedFilters.availability) ? appliedFilters.availability : undefined
            const queryCityFilters = cityFilterSource ? (cityFilterSource.includes(",") ? cityFilterSource.split(",") : [cityFilterSource]) : []
            const queryLanguageFilters = languageFilterSource ? (languageFilterSource.includes(",") ? languageFilterSource.split(",") : [languageFilterSource]) : []
            const queryAvailabiltyFilters = availabilityFilterSource ? (availabilityFilterSource.includes(",") ? availabilityFilterSource.split(",") : [availabilityFilterSource]) : []
            const sort_index = typeof req.query.sort_index !== "undefined" ? req.query.sort_index : (appliedFilters && typeof appliedFilters.sort_index !== "undefined" ? appliedFilters.sort_index : undefined)
            const filtersData = await this.healthfaceService.getDoctorListingFilters(productId, healthfaceTenant)
            const categoryFilters = AppUtil.isCareAgentFilterSupported(userContext)
                ? (filtersData.agentFilterInfos.filter(item => !CUSTOM_SUPPORTED_DOCTOR_FILTER.includes(item.internalName)))
                : undefined
            const filterModalItems = CareUtil.getDoctorSearchFilterModalItems(userContext, product.doctorType, filtersData, parentBookingId)
            const isOfflineMode = product.consultationMode === "INCENTRE"
            const sort_by = typeof sort_index !== "undefined" ? filterModalItems.sortItems[sort_index].sort_by : undefined
            const sort_order = typeof sort_index !== "undefined" ? filterModalItems.sortItems[sort_index].sort_order : undefined
            const selectedFilterItems = isFiltersSupported
                ? CareUtil.getDoctorSearchFilterModalItemsV2(
                    sort_index,
                    filterModalItems,
                    filtersData,
                    queryCityFilters,
                    queryLanguageFilters,
                    queryAvailabiltyFilters,
                    !_.isEmpty(categoryFilters) && appliedFilters ? { appliedFilters: appliedFilters, categoryFilters: CareUtil.createCategoryFilters(categoryFilters) } : undefined
                )
                : CareUtil.getDoctorSearchFilterModalItemsIndices(sort_index)
            const isCareFlow = !CareUtil.isPTDoctorType(product.doctorType) && !CareUtil.isLivePTDoctorType(product.doctorType) && (userAgent === "MBROWSER" || userAgent === "DESKTOP" || !isMindTherapyFlow || (isMindTherapyFlow && userContext.sessionInfo.appVersion >= CARE_DOCTOR_PAGE_SUPPORTED))
            let slotsPerDoctor
            if (isCareFlow || isMindTherapyFlow) {
                slotsPerDoctor = isUnifiedDoctorUISupported ? 3 : 2
            } else {
                slotsPerDoctor = 0
            }
            const cityId = isOfflineMode ? userContext.userProfile.cityId : undefined
            const isOverRideMindTherapyDoctortypeProduct = _.get(product, "infoSection.isPartOfConsultationPack")
            const doctorsListPromise = this.healthfaceService.getAllDoctorDetailsWithAvailabilityV3({
                types: doctorTypes,
                cityCode: cityId,
                speciality: specialityCode,
                centerId,
                consultationMode: product.consultationMode,
                sort_by,
                sort_order,
                slotsPerDoctor,
                userId,
                patientIds: undefined,
                doctorAvailability: queryAvailabiltyFilters,
                languages: queryLanguageFilters,
                cities: queryCityFilters,
                productCode: !isMindTherapyFlow || isOverRideMindTherapyDoctortypeProduct ? productId : undefined,
                userCityCode: userContext.userProfile.cityId,
                latitude: !_.isNaN(userContext.sessionInfo.lat) ? userContext.sessionInfo.lat : undefined,
                longitude: !_.isNaN(userContext.sessionInfo.lon) ? userContext.sessionInfo.lon : undefined,
                agentAttributeFilters: (categoryFilters && appliedFilters) ? CareUtil.createAgentAttributeFilters(appliedFilters, categoryFilters) : undefined,
                rankingScheme: AgentRankingScheme.COMPREHENSIVE,
                useFollowUpScore: CareUtil.isMindDoctorType(product.doctorType) && await AppUtil.isFollowupScoreEnbled(userContext, this.hamletBusiness)
            }, healthfaceTenant)
            const widgetParams: CareDoctorSearchPageParams = {
                productId,
                patientId,
                parentBookingId,
                replacePage: isReplacePage,
                selectedFilterItems,
                filtersData,
                isPriceAvailableInListingApi,
                isReschedule,
                isDoctorSearch,
                categoryFilters,
                isUnifiedDoctorUISupported,
                showPrice,
                hideInfoWidget
            }

            userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
            const isOptumUser = await AppUtil.doesUserBelongToOptumCorpSegment(this.serviceInterfaces.segmentService, userContext)

            // Separating out care flow
            if (isCareFlow) {
                if (_.isNil(parentBookingId) || showPrice) {
                    const doctors = await doctorsListPromise
                    isPriceAvailableInListingApi = Boolean(_.get(doctors, "0.doctorCenterMapping.0.price", undefined))
                    if (isPriceAvailableInListingApi || CareUtil.isTherapyOnlyDoctorType(product.doctorType)) {
                        offerPromise = CareWidgetUtil.getCareProductOffersFromUserContext(userContext, "CONSULTATION", [productId], AppUtil.callSourceFromContext(userContext), this.serviceInterfaces, true)
                    } else {
                        centersPromise = this.healthfaceService.getAllCenters(product.productType, product.categoryId, productId, tenant, product.consultationMode === "INCENTRE" ? cityCode : undefined)
                        offerPromise = Promise.resolve({})
                    }
                }
                widgetParams.isPriceAvailableInListingApi = isPriceAvailableInListingApi
                const pagePromise: CareDoctorSearchPagePromises = {
                    doctorsListPromise,
                    offerPromise,
                    centersPromise,
                    patientListPromise,
                    bannerWidgetPromise: _.isNil(widgetParams.parentBookingId) || showPrice ? CareUtil.getBannerCarouselWidget(userContext, this.serviceInterfaces, CareUtil.getCareConsultationPacksBannerId(product.doctorType)) : undefined
                }
                return new CareDoctorSearchPageView().getView(userContext, product, pagePromise, req.query.doctorType || product.doctorType, widgetParams, this.hamletBusiness, specialistContentSection, isOptumUser)
            } else {
                return new DoctorListingPageViewV1(
                    userContext,
                    product,
                    await doctorsListPromise,
                    req.query.doctorType,
                    await patientListPromise,
                    productId,
                    showSearchWidget,
                    patientId,
                    parentBookingId,
                    isReschedule,
                    isReplacePage,
                    showCenterSelection,
                    centerId,
                    isDoctorSearch,
                    AppUtil.isTherapistRecommendationV2UISupported(userContext),
                    widgetParams
                )
            }
        }

        @httpGet("/specialistRecommendation")
        async getSpecialistRecommendation(req: express.Request): Promise<ProductDetailPage> {
            const userContext = req.userContext as UserContext
            const session: Session = req.session
            const userId = session.userId
            let parentBookingId: number = req.query.parentBookingId || undefined
            let patientId: number = req.query.patientId
            const productId: string = req.query.productId as string
            const subCategoryCode: SUB_CATEGORY_CODE = req.query.subCategoryCode
            const centerId: number = req.query.centerId !== undefined && req.query.centerId !== "undefined" ? req.query.centerId : undefined
            const consultationMode: ConsultationProduct["consultationMode"] = subCategoryCode === "MIND_THERAPY" || subCategoryCode === "LIVE_PERSONAL_TRAINING" ? "ONLINE" : "INCENTRE"
            if (!centerId && consultationMode === "INCENTRE") {
                throw new CenterNotSelectedError().throwError(this.errorFactory, "Please select a center", userContext)
            }
            const orderSource = userContext.sessionInfo.orderSource
            const tenant: HealthfaceTenant = CareUtil.getHealthfaceTenant(subCategoryCode, undefined, orderSource)
            // safe check if patientid is not coming in request
            if (!patientId && subCategoryCode === "MIND_THERAPY") {
                const patientList = await this.healthfaceService.getAllPatients(userId)
                const selfPatient = !_.isEmpty(patientList) ? _.find(patientList, patient => patient.relationship === "Self") : undefined
                patientId = selfPatient ? selfPatient.id : undefined
            }
            if (patientId) {
                // getting parentBooking id if not being passed
                if (_.isNil(parentBookingId) && subCategoryCode === "MIND_THERAPY") {
                    let activePackBookings = await this.healthfaceService.getActivePacksByBookingType(userId, patientId, "MIND", "BUNDLE", "MIND_THERAPY")
                    if (!_.isEmpty(activePackBookings)) {
                        activePackBookings = activePackBookings.filter(activeBooking => activeBooking.booking.status === "CONFIRMED")
                        if (!_.isEmpty(activePackBookings)) {
                            parentBookingId = activePackBookings[0].booking.id
                        }
                    }
                }
                const doctorTypes = req.query.doctorTypes ? (req.query.doctorTypes.includes(",") ? req.query.doctorTypes.split(",") : [req.query.doctorTypes]) : undefined
                const excludeDoctorIds = req.query.excludeDoctorIds ? (req.query.excludeDoctorIds.includes(",") ? req.query.excludeDoctorIds.split(",") : [req.query.excludeDoctorIds]) : undefined
                const request: DoctorRecommendationRequest = {
                    subCategoryCode,
                    patientId,
                    withAvailability: true,
                    centerId,
                    doctorTypeCodesCsv: doctorTypes,
                    consultationMode,
                    excludeDoctors: excludeDoctorIds
                }
                const doctorsList: Doctor[] = await this.healthfaceService.getDoctorRecommendations(request, tenant)
                const doctors: Doctor[] = subCategoryCode === "MIND_THERAPY" ? (doctorsList ? [...new Set(doctorsList.slice(0, Math.min(MIND_THERAPIST_RECOMMENDATION_MAX_LENGTH, doctorsList.length)))] : []) : doctorsList
                /**
                 * commenting this code as this is not being used in mind therapy now. Since its online flow
                 */
                // const cityCode: string = (req.userContext as UserContext).userProfile.cityId
                // let centers: Center[] = []
                // if (subCategoryCode !== "MIND_THERAPY") {
                //     centers = await this.healthfaceService.getCenterRecommendations(subCategoryCode, patientId, cityCode, doctorTypes, tenant)
                // }
                // Creating a map object of center with center id as key
                // const centersList = centers.reduce((obj: any, item: Center) => {
                //     obj[item.id] = item
                //     return obj
                // }, {})
                if (subCategoryCode === "LIVE_PERSONAL_TRAINING") {
                    const crossGenderDetails = await this.cultBusiness.getLivePTCrossGenderMatchingDetails(userContext, patientId)
                    return new LivePTTrainerRecommendationPageView(userContext, doctors, subCategoryCode, patientId, doctorTypes, parentBookingId, productId, crossGenderDetails)
                }
                const patientListPromise = this.healthfaceService.getAllPatients(userId)
                const product: ConsultationProduct = <ConsultationProduct>await this.catalogueService.getProduct(productId)
                return new DoctorRecommendationPageView(userContext, doctors, subCategoryCode, [], patientId, centerId, await patientListPromise, product, doctorTypes, parentBookingId)
            }
        }

        @httpGet("/selectSpecialist")
        async getSelectSpecialist(req: express.Request): Promise<ProductDetailPage> {
            const userContext = req.userContext as UserContext
            const productId = req.query.productId
            const parentBookingId: number = req.query.parentBookingId || undefined
            const product: ConsultationProduct = <ConsultationProduct>await this.catalogueService.getProduct(productId)
            const doctorType: DOCTOR_TYPE = req.query.doctorType
            if (CareUtil.isMindDoctorType(doctorType)) {
                return this.selectSpecialityPageViewBuilder.getTherapyBookingPage(userContext, product, doctorType, parentBookingId)
            }
        }

        @httpGet("/chronicCareDoctorDetails")
        async chronicCareDoctorDetails(req: express.Request): Promise<ChronicCareDoctorBookingPageView> {
            const doctorId = req.query.doctorId
            const productId = req.query.productId
            const disableBooking = req.query?.disableBooking?.toLowerCase() === "true"
            const userContext = req.userContext as UserContext
            if (!doctorId) {
                throw this.errorFactory.withCode(ErrorCodes.CARE_DOCTOR_DETAILS_NOT_FOUND_ERR, 404).withDebugMessage(`Doctor not found`).build()
            }
            const tenant: HealthfaceTenant = "SUGARFIT"
            const doctor: Doctor = await this.healthfaceService.getDoctorDetails(doctorId, undefined, undefined, tenant)
            let bookingDetail
            if (productId) {
                bookingDetail = await this.healthfaceService.getActiveSugarfitSubscription(userContext.userProfile.userId)
                // this.logger.debug(`Active Pack response :: ${JSON.stringify(bookingDetail)}`)
            }
            return new ChronicCareDoctorBookingPageView(userContext, doctor, productId, bookingDetail, disableBooking)
        }

        @httpGet("/chronicCareStructuredAddress")
        async chronicCareStructuredAddress(req: express.Request): Promise<Boolean> {
            const addressId = req.query.addressId
            const userContext = req.userContext as UserContext
            const userId = userContext.userProfile.userId
            const finalAddress = await this.userBusiness.augmentStructuredAddress(userId, addressId, null, AppUtil.getAppTenantFromReq(req))
            return !_.isEmpty(finalAddress.structuredAddress)
        }

        @httpGet("/doctorDetails/")
        async doctorDetails(req: express.Request): Promise<DoctorBookingPageView> {
            const slugValue = req.query.slugValue
            const userAgent = AuthUtil.getUserAgent(req)
            const isDoctorSlugFlow = slugValue && slugValue !== ""
            let doctorSlugData: Doctor
            if (isDoctorSlugFlow) {
                doctorSlugData = await this.healthfaceService.getAllDoctorDataBySlugName(slugValue, req.query.productId)

                if (!doctorSlugData) {
                    throw this.errorFactory.withCode(ErrorCodes.CARE_DOCTOR_DETAILS_NOT_FOUND_ERR, 404).withDebugMessage(`Doctor not found for slugValue: ${slugValue}`).build()
                }
            }
            let doctorId = req.query.doctorId || (isDoctorSlugFlow ? doctorSlugData?.id : "")
            doctorId = CareUtil.getDoctorIdFromDeeplink(doctorId)
            if (!doctorId) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Doctor Details is Missing").build()
            }
            const centerId: string = req.query.centerId !== undefined && req.query.centerId !== "undefined" ? req.query.centerId : isDoctorSlugFlow ? doctorSlugData.doctorCenterMapping[0].centerId : ""
            const userContext = req.userContext as UserContext
            const session: Session = req.session
            const userId = session.userId
            const patientId: number = req.query.patientId
            const showPrice: boolean = req.query.showPrice === "true"
            const slugConsultationProduct: any = doctorSlugData ? doctorSlugData.consultationProducts.find(({ doctorType, consultationMode }) => (
                doctorType === doctorSlugData.primarySubServiceType.code && consultationMode === "ONLINE")) : {}
            let productId = req.query.productId || (isDoctorSlugFlow ? slugConsultationProduct?.productCode : "")
            let product: any = await this.catalogueService.getProduct(productId)
            // Fix for deeplink passing wrong productId and it is causing failures
            if (!product && isDoctorSlugFlow && slugConsultationProduct?.productCode) {
                product = await this.catalogueService.getProduct(slugConsultationProduct?.productCode)
                productId = product?.productId
            }
            if (!product) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Product is Missing").build()
            }
            const patientPromise = this.healthfaceService.getAllPatients(userId)
            const isLivePTDoctorType = CareUtil.isLivePTDoctorType(product.doctorType)
            const isLiveSGTDoctorType = CareUtil.isLiveSGTDoctorType(product.doctorType)
            const isPTFlow = product && product.doctorType && (CareUtil.isPTDoctorType(product.doctorType) || isLivePTDoctorType || isLiveSGTDoctorType)
            const isBundlePack: boolean = product && product.subCategoryCode && product.subCategoryCode === "MIND_THERAPY"
            const doctor: Doctor = isDoctorSlugFlow ? doctorSlugData : await this.healthfaceService.getDoctorDetails(doctorId, isBundlePack || isLivePTDoctorType || isPTFlow || isLiveSGTDoctorType ? undefined : productId, true)
            const categoryFilters = AppUtil.isCareAgentFilterSupported(userContext) && product.tenant !== "CULTFIT" ? (await this.healthfaceService.getDoctorListingFilters(productId)).agentFilterInfos.filter(item => !CUSTOM_SUPPORTED_DOCTOR_FILTER.includes(item.internalName)) : undefined
            let parentBookingId: number = req.query.parentBookingId
            const isReschedule: string = req.query.isReschedule
            const isDoctorSearch: boolean = req.query.isDoctorSearch && req.query.isDoctorSearch === "true"
            const deviceId = session.deviceId
            const doctorTypes = []
            let productList: { title: string, action: any }[]
            let offerPromise, isDoctorPriceAvailable = false
            const careSeoData: ISEOData = await this.seoDataDao.findOne({ seoDataId: slugValue })

            if (isBundlePack || (product && product.doctorType && (CareUtil.isMindDoctorType(product.doctorType) || CareUtil.isPTDoctorType(product.doctorType)))) {
                const doctorType = doctor.doctorTypes.find(item => ["MIND_THERAPIST", "PSYCHIATRIST"].includes(item.type.code) || CareUtil.isPTDoctorType(item.type.code))
                if (doctorType) {
                    doctorTypes.push(doctorType.type.code)
                }
            } else if (isDoctorSearch || isDoctorSlugFlow) {
                doctorTypes.push(product.doctorType)
            } else {
                doctor.doctorTypes && doctor.doctorTypes.map(doctorType => {
                    doctorTypes.push(doctorType.type.code)
                })
            }
            if (_.isNil(parentBookingId) && (isLivePTDoctorType || isLiveSGTDoctorType)) {
                const activePacks = await this.healthfaceService.getActiveBundleOrders(userId, "BUNDLE", isLivePTDoctorType ? "LIVE_PERSONAL_TRAINING" : "LIVE_SGT", false, "CULTFIT")
                if (!_.isEmpty(activePacks)) {
                    parentBookingId = activePacks[0].bookingId
                }
            } else if ((_.isNil(parentBookingId) || showPrice) && productId) {
                isDoctorPriceAvailable = Boolean(_.get(doctor, "doctorCenterMapping.0.price", undefined))
                if (isDoctorPriceAvailable || CareUtil.isTherapyOnlyDoctorType(product.doctorType)) {
                    offerPromise = CareWidgetUtil.getCareProductOffersFromUserContext(userContext, "CONSULTATION", [productId], AppUtil.callSourceFromContext(userContext), this.serviceInterfaces, true)
                } else {
                    const offerInventoryRequestParams: CareCenterOfferRequestParams = {
                        productType: "CONSULTATION",
                        userId,
                        cityId: userContext.userProfile.cityId,
                        deviceId,
                        productIds: [productId],
                        centerIds: doctor.doctorCenterMapping.map(item => item.centerId.toString()),
                        source: AppUtil.callSourceFromContext(userContext)
                    }
                    this.logger.info("CARE::DEBUG getCareCenterOffers attempted", { flow: "doctorDetails", request: offerInventoryRequestParams })
                    offerPromise = Promise.resolve({})
                }
            }
            if (!isPTFlow && !product) {
                productList = _.flatMap(await Promise.all(doctorTypes.map(async doctorType => {
                    const isPTDoctor = CareUtil.isPTDoctorType(doctorType)
                    const isMindDoctorType = CareUtil.isMindDoctorType(doctorType)
                    const consultationSellableProduct: ConsultationSellableProductResponse = await this.healthfaceService.getConsultationSellableProducts(userContext.userProfile.cityId, isMindDoctorType ? "MIND_THERAPIST" : doctorType)
                    let consultationtypes
                    if (isPTDoctor || isMindDoctorType || isDoctorSearch) {
                        consultationtypes = consultationSellableProduct && consultationSellableProduct.consultationTypes.filter(consultationType => consultationType.type === doctorType)
                    } else {
                        consultationtypes = consultationSellableProduct && consultationSellableProduct.consultationTypes
                    }
                    if (consultationtypes && consultationtypes.length > 0) {
                        const products = await Promise.all(_.map(consultationtypes, async consultationItem => {
                            let productDetails: ConsultationProduct[]
                            if (isDoctorSearch) {
                                productDetails = [product]
                                const tenant: HealthfaceTenant = CareUtil.getCareProductTenant(product)
                            } else {
                                productDetails = await Promise.all(_.map(consultationItem.products, async itemproduct => {
                                    const productDetail: ConsultationProduct = <ConsultationProduct>await this.catalogueService.getProduct(itemproduct.code)
                                    return productDetail
                                }))
                            }
                            const details = CareUtil.getDoctorDetails(consultationItem.type)
                            /**
                             * Filter out virtual center dedicated for anxiety therapy
                             * This is a hack, Albus needs to fix this later
                             */
                            const filteredDoctorMappings = doctor && Array.isArray(doctor.doctorCenterMapping) ? doctor.doctorCenterMapping.filter(
                                mapping => mapping.centerId !== CareUtil.getVirtualCenterId()
                            ) : []
                            const action = CareUtil.getConsultationPreBookingActions(userContext, productDetails.filter(product => !CareUtil.isAnxietyTherapy(product.productId)), !userContext.sessionInfo.isUserLoggedIn, await patientPromise, [], doctor.id, centerId ? Number(centerId) : filteredDoctorMappings[0]?.centerId, parentBookingId, isReschedule && isReschedule === "true", patientId)
                            return {
                                ...action,
                                title: details && details.title
                            }
                        }))
                        return products
                    }
                }))).filter(item => !!item)
            }
            let pageAction
            if (isLivePTDoctorType || isLiveSGTDoctorType) {
                const params: LivePTSessionBookActionParams = {
                    productId: productId,
                    strictGenderCheck: true,
                    doctorId: doctorId,
                    actionTitle: "BOOK SESSION",
                    parentBookingId,
                    subCategoryCode: isLiveSGTDoctorType ? "LIVE_SGT" : "LIVE_PERSONAL_TRAINING"
                }
                pageAction = await this.careBusiness.getLivePTSessionBookAction(userContext, params)
            }
            return new DoctorBookingPageView(
                userContext,
                doctor,
                await patientPromise,
                offerPromise ? await offerPromise : undefined,
                productList,
                product,
                patientId,
                parentBookingId,
                isDoctorSearch,
                centerId,
                pageAction,
                isDoctorPriceAvailable,
                isPTFlow,
                categoryFilters,
                showPrice,
                careSeoData
            )
        }

        @httpGet("/doctor/slug-details")
        async getDoctorSlugDetailMapping(req: express.Request): Promise<Doctor | Doctor[]> {
            const doctorId = CareUtil.getDoctorIdFromDeeplink(req.query.doctorId)
            const response = await this.healthfaceService.getDoctorSlugMappingById(doctorId)

            if (!response) {
                throw this.errorFactory.withCode(ErrorCodes.CARE_DOCTOR_DETAILS_NOT_FOUND_ERR, 404).withDebugMessage("Error Fetching Doctor Slug Details").build()
            }

            return response
        }

        @httpGet("/product/search-details")
        async getProductSearchDetails(req: express.Request): Promise<ConsultationProduct> {
            const productId = req.query.productId
            const response: ConsultationProduct = <ConsultationProduct>await this.catalogueService.getProduct(productId)

            if (!response) {
                throw this.errorFactory.withCode(ErrorCodes.CARE_PRODUCT_DETAILS_NOT_FOUND_ERR, 404).withDebugMessage("Error Fetching Product Details").build()
            }

            return response
        }

        @httpGet("/doctor/slug-mapping")
        async getDoctorSeoSlugMappingBySlugName(req: express.Request): Promise<ISlugMappingResponse> {
            const slugValue: string = req.query.slugValue
            const response = await this.healthfaceService.getDoctorSeoSlugValueBySlugName(slugValue)

            if (!response) {
                throw this.errorFactory.withCode(ErrorCodes.CARE_DOCTOR_DETAILS_NOT_FOUND_ERR, 404).withDebugMessage("Error Fetching Product Details").build()
            }

            return response
        }

        @httpPost("/practo/booking/confirm/:bookingId")
        async confirmPractoBooking(req: express.Request): Promise<string> {
            const bookingId: number = req.params.bookingId
            const requestBody: PractoConfirmBookingRequest = req.body

            const response: any = await this.healthfaceService.confirmPractoAppointment(bookingId, requestBody)
            return response
        }

        @httpGet("/sendPlanEmail/:userId/:patientId")
        async sendPlanEmail(req: express.Request): Promise<string> {
            const response: any = await this.healthfaceService.sendPlanEmail(req.params.userId, req.params.patientId)
            return response
        }

        @httpGet("/center/browse/:productId")
        async centerBrowse(req: express.Request): Promise<any> {
            const productId: string = req.params.productId
            const userContext = req.userContext as UserContext
            const baseProduct: ConsultationProduct = <ConsultationProduct>await this.catalogueService.getProduct(productId)
            const tenant: HealthfaceTenant = CareUtil.getCareProductTenant(baseProduct)
            const cityCode: string = (req.userContext as UserContext).userProfile.cityId
            const centerPromise = this.healthfaceService.getAllCenters(baseProduct.productType, baseProduct.categoryId, productId, tenant, cityCode)
            const centers = await centerPromise
            this.sortCareCentersByLocation(userContext.sessionInfo, centers)
            return this.careCenterBrowseViewBuilder.getView(userContext, centers, baseProduct)
        }

        private sortCareCentersByLocation(sessionInfo: SessionInfo, centers: Center[]) {
            if (_.isNil(sessionInfo.lat) || _.isNil(sessionInfo.lon))
                return
            LocationUtil.sortCareCentersByLocation(sessionInfo.lat, sessionInfo.lon, centers)
        }

        @httpPost("/zoomEvent")
        async sendZoomEvent(req: express.Request): Promise<any> {
            const userContext = req.userContext as UserContext
            const meetingEvent: MeetingEvent = {
                cfUserId: userContext.userProfile.userId,
                eventName: req.body.eventName,
                eventTime: new Date(),
                meetingId: req.body.meetingId,
                patientId: req.body.patientId
            }
            return await this.healthfaceService.postMeetingEvent(meetingEvent, "CULTFIT")
        }

        @httpPost("/zoom/signature")
        async getZoomSignature(req: express.Request): Promise<object> {
            const timestamp = new Date().getTime() - 30000
            const ZOOM_API_KEY = "9mOo9UCnQBKVDLWvS5SE8g"
            const ZOOM_API_SECRET = "MNDB26hdTS74DQjjrmqnnX2McmrJM7HdvzMT"
            const { body } = req
            const msg = Buffer.from(
                ZOOM_API_KEY + body.meetingNumber + timestamp + body.role
            ).toString("base64")
            const hash = createHmac("sha256", ZOOM_API_SECRET)
                .update(msg)
                .digest("base64")
            const signature = Buffer.from(
                `${ZOOM_API_KEY}.${body.meetingNumber}.${timestamp}.${body.role}.${hash}`
            ).toString("base64")
            return {
                signature: signature
            }
        }

        @httpPost("/updateConsultationChannel/:appointmentId")
        async updateConsultationChannel(req: express.Request): Promise<any> {
            const appointmentId: string = req.params.appointmentId
            const response = await this.healthfaceService.updateConsultationChannel(appointmentId, req.body)
            return { result: response ? true : false }
        }

        @httpPost("/updatePatientPreference")
        async updatePatientPreference(req: express.Request): Promise<any> {
            const userContext: UserContext = req.userContext as UserContext
            const preference: PatientPreference = req.body.preference
            const tenant: HealthfaceTenant = req.body.tenant || "CAREFIT"
            return await this.healthfaceService.updatePatientPreference(preference, tenant)
        }

        @httpPost("/updatePartnerDetails")
        async updatePartnerDetails(req: express.Request): Promise<any> {
            const { appointmentId, patientId } = req.body
            const userContext: UserContext = req.userContext as UserContext
            const response = await this.healthfaceService.updatePartnerPatientDetails(appointmentId, patientId)
            return response ? true : false
        }

        @httpPost("/updateConsultationFeedback")
        async updateConsultationFeedback(req: express.Request): Promise<any> {
            const {
                bookingId,
                userId,
                patientId,
                appointmentId,
                productCode,
                subServiceType,
                tags,
                reason
            } = req.body
            const userContext: UserContext = req.userContext as UserContext
            return this.ehrService.postPatientConsultationReason(appointmentId, patientId, {
                pastPsychiatricHistory: {
                    patientReasonsForConsultation: tags.map((tag: string) => (tag === "Other" ? {
                        tag,
                        reason
                    } : { tag })),
                    subServiceType,
                    productCode,
                    userId,
                }
            })
        }
        @httpGet("/twilioOnboardingDetails")
        async getTwilioOnboardingDetails(req: express.Request): Promise<any> {
            const session: Session = req.session
            const bookingId: number = req.query.tcBookingId
            const paramProductId: string = req.query.productId
            const productSlug: string = req.query.productSlug
            const isTwilio: boolean = req.query.isTwilio
            const userId = session.userId
            const deviceId = session.deviceId
            const userContext = req.userContext as UserContext
            const source = req.headers["user-agent"] as string
            const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "APP")
            const centerId: string = req.query.centerId
            const isVideoEnabled = req.query.isVideoEnabled === "true" ? true : false
            const isCallFlow = req.query.isCallFlow === "true" ? true : false
            const cityCode: string = (req.userContext as UserContext).userProfile.cityId
            const canChangeCenter: boolean = req.query.canChangeCenter && req.query.canChangeCenter === "true"
            let productId: string
            let baseProduct: ConsultationProduct
            let tenant: HealthfaceTenant

            if (paramProductId) {
                baseProduct = <ConsultationProduct>await this.catalogueService.getProduct(paramProductId)
            } else if (productSlug) {
                baseProduct = await this.healthfaceService.getConsultationProductDetailsByUrlPath(productSlug)
            }

            if (!baseProduct) {
                throw this.errorFactory.withCode(ErrorCodes.CARE_PRODUCT_DETAILS_NOT_FOUND_ERR, 404).withDebugMessage("Error Fetching Product Slug Details").build()
            }

            productId = baseProduct.productId || baseProduct.productCode
            tenant = CareUtil.getCareProductTenant(baseProduct)
            let bookingDetail: BookingDetail
            if (!_.isNil(bookingId) && CareUtil.isLiveSGTProduct(baseProduct)) {
                bookingDetail = await this.healthfaceService.getBookingDetailForBookingId(bookingId, baseProduct.tenant, undefined, undefined, true, userContext.userProfile.cityId)
                if (!_.isNil(bookingDetail)) {
                    const consultationResponse: ConsultationOrderResponse = bookingDetail.consultationOrderResponse
                    const appointmentGroupId = consultationResponse.appointmentGroupId
                    const consultation: Consultation[] = await this.healthfaceService.getAppointmentGroupDetails("CF_ONLINE_CONSULTATION", appointmentGroupId, "CULTFIT")
                    if (!_.isEmpty(consultation)) {
                        const action: Action = CareUtil.getTwilioAction(userContext, consultation, consultationResponse, productId)
                        action.title = "SKIP"
                        const nextScreenTime = 1000
                        const patientId = consultationResponse.patient.id
                        const doctorType = consultationResponse.doctorType
                        const response = CareUtil.getOnboardingAction(nextScreenTime, action, patientId, userAgent, bookingId)
                        return response
                    } else {
                        throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("Error fetching consultation").build()
                    }
                } else {
                    throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("Error fetching booking details").build()
                }
            } else {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("Error fetching booking details").build()
            }
        }

        @httpPost("/check-bundle-exists")
        async checkBundleExists(req: express.Request): Promise<any> {
            const userContext: UserContext = req.userContext as UserContext
            const {
                patientId,
                productCode,
            } = req.body
            const response: ActiveBundleOrderDetail[] = await this.healthfaceService.getExistingBundleByProductCode(productCode, patientId)
            return !_.isEmpty(response) ? {result: true} : {result: false}
        }
        @httpGet("/ptChatFeedback")
        async checkPTChatFeedback(req: express.Request): Promise<any> {
            const agentId: string = req.query.doctorId || req.params.doctorId
            const userContext: UserContext = req.userContext as UserContext
            const patientId: string = req.params.patientId || req.query.patientId
            const doctorName: string = req.params.doctorName || req.query.doctorName
            const shouldShowFeedback = await this.cultPTService.isChatFeedbackRequired(agentId, patientId)
            const itemId = `${agentId}-${patientId}`
            if (shouldShowFeedback) {
                return await this.feedbackBusiness.createPTTrainerCustomerChatFeedback(userContext.userProfile.userId, itemId, doctorName, "CULT_PT_TRAINER_CUSTOMER_CHAT")
            }
            return { created: false, feedbackId: "" }
        }

    }

    return CareController
}

export default controllerFactory
