import { ProductType } from "@curefit/product-common"
import * as _ from "lodash"
import { CustomerIssueType } from "@curefit/issue-common"
import { DiagnosticProduct, SUB_CATEGORY_CODE } from "@curefit/care-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import {
    ActionV2,
    BookingDetail,
    DiagnosticEmailedTestReportResponse,
    AggregatedMembershipInfo
} from "@curefit/albus-client"
import {
    Action,
    InstructionsWidget,
    ManageOptionPayload,
    ManageOptions,
    NavigationCardWidget,
    ProductDetailPage,
    WidgetView,
    PackProgress
} from "../common/views/WidgetView"
import {
    DiagnosticTestReportItem,
    DiagnosticsTestReportSummaryWidget,
    DiagnosticsEmailTestReportSummaryWidget
} from "../page/PageWidgets"
import { CareUtil, HomeCenterSelector } from "../util/CareUtil"
import { CareProductStateViewV2 } from "./CareProductStateViewV2"
import { ActionUtil } from "@curefit/base-utils"
import { UserContext } from "@curefit/userinfo-common"
import {
    IBreadCrumb
} from "@curefit/apps-common"
import { TimeUtil } from "@curefit/util-common"
import AppUtil, { SUPPORT_DEEP_LINK } from "../util/AppUtil"
import { COMBINED_WEIGHT_LOSS_CLP_DEEPLINK } from "../util/TransformUtil"

class ManagedPlanOnboardingProductPageView extends ProductDetailPage {
    public pageContext: any
    constructor(
        userContext: UserContext,
        product: DiagnosticProduct,
        bookingInfo: BookingDetail,
        aggregatedMembershipInfo: AggregatedMembershipInfo,
        issuesMap: Map<string, CustomerIssueType[]>,
        newReportIssueManageOption: ManageOptionPayload,
        reportEstimationTime: any,
        diagnosticsEmailedTestReportDetails?: DiagnosticEmailedTestReportResponse,
        homeCenterSelector?: HomeCenterSelector,
        reportAction?: string
    ) {
        super()
        this.widgets.push(this.getSummaryWidget(userContext, product, bookingInfo, aggregatedMembershipInfo, issuesMap, newReportIssueManageOption, bookingInfo.booking.subCategoryCode === "MP_V2"))
        if (bookingInfo.booking.status !== "CANCELLED") {
            let bundleBookingInfo
            if (bookingInfo.booking.subCategoryCode === "MP") {
                bundleBookingInfo = bookingInfo.childBookingInfos.find(info => info.booking.subCategoryCode === "MP_OT")
            } else if (bookingInfo.booking.subCategoryCode === "MP_V2") {
                bundleBookingInfo = bookingInfo.childBookingInfos.find(info => info.booking.subCategoryCode === "MP_V2_OT")
            } else if (CareUtil.isTransformTenant(bookingInfo.booking.subCategoryCode)) {
                bundleBookingInfo = bookingInfo
            } else {
                bundleBookingInfo = bookingInfo.childBookingInfos.find(info => info.booking.subCategoryCode === "DIAG_PACK_OT" || info.booking.subCategoryCode === "HCU_PACK_OT" || info.booking.subCategoryCode === "GENETICS_PACK_OT" || info.booking.subCategoryCode === "AI_MG_OT")
            }
            const productStateWidget = new CareProductStateViewV2(userContext, bookingInfo.booking.subCategoryCode, product.productId, bundleBookingInfo, reportEstimationTime, reportAction)
            this.widgets.push(productStateWidget.widget)
            if (bookingInfo.booking.subCategoryCode === "MP_V2") {
                const instructionWidget = this.getInstructionSummaryWidget(bundleBookingInfo)
                if (instructionWidget) {
                    this.widgets.push(instructionWidget)
                }
            }
            if (!CareUtil.isTransformTenant(bookingInfo.booking.subCategoryCode) && bundleBookingInfo.booking.subCategoryCode !== "AI_MG_OT" && bundleBookingInfo.booking.subCategoryCode !== "MP_V2_OT") {
                const isNewEmailedReportSupported = CareUtil.isNewEmailedReportSupported(userContext) && diagnosticsEmailedTestReportDetails && !_.isEmpty(diagnosticsEmailedTestReportDetails.productDetails)
                const reportWidget = this.getDiagnosticReportSummaryWidget(userContext, bundleBookingInfo, isNewEmailedReportSupported)
                if (reportWidget) {
                    this.widgets.push(reportWidget)
                }
                // if (isNewEmailedReportSupported) {
                //     const emailReportWidget = CareUtil.getDiagnosticsEmailTestReportSummaryWidget(diagnosticsEmailedTestReportDetails)
                //     if (emailReportWidget) {
                //         this.widgets.push(emailReportWidget)
                //     }
                // }
            }
            if (!_.isEmpty(bundleBookingInfo.primaryActionV2)) {
                this.actions = this.getActions(userContext, bundleBookingInfo.booking.patientId, bundleBookingInfo.primaryActionV2, productStateWidget.primaryAction, bundleBookingInfo.booking.subCategoryCode, homeCenterSelector)
            }
        }
    }


    private getIssueList(issuesMap: Map<string, CustomerIssueType[]>): { code: string, title: string, confirmation: string }[] {
        const issueList: { code: string, title: string, confirmation: string }[] = []
        issuesMap.get("DiagnosticsSingleBooked").forEach(customerIssueType => {
            issueList.push({
                code: customerIssueType.code,
                title: customerIssueType.subject,
                confirmation: customerIssueType.confirmation
            })
        })
        return issueList
    }

    private getManageOptions(bookingDetail: BookingDetail, orderId: string, productId: string,
        issuesMap: Map<string, CustomerIssueType[]>,
        newReportIssueManageOption: ManageOptionPayload): { manageOptions: ManageOptions, meta: any } {
        const options: ManageOptionPayload[] = []
        if (newReportIssueManageOption) {
            options.push(newReportIssueManageOption)
        } else {
            options.push(
                {
                    isEnabled: true,
                    displayText: "Need Help",
                    type: "REPORT_ISSUE",
                    meta: this.getIssueList(issuesMap),
                    action: SUPPORT_DEEP_LINK,
                }
            )
        }

        const manageOptions: ManageOptions = {
            displayText: "...",
            options: options
        }

        const meta: any = {
            orderId: orderId,
            productId: productId,
            tcBookingId: bookingDetail.booking.id
        }
        return { manageOptions: manageOptions, meta: meta }
    }

    private getActions(userContext: UserContext, patientId: number, action: ActionV2, primaryAction: Action, subCategoryCode: SUB_CATEGORY_CODE, homeCenterSelector?: HomeCenterSelector): Action[] {
        if (CareUtil.isTransformTenant(subCategoryCode)) {
            if (AppUtil.isTransformCombinedClpSupported(userContext)) {
                return [{actionType: "NAVIGATION", url: COMBINED_WEIGHT_LOSS_CLP_DEEPLINK, title: "Update your Habits"}]
            }
            return [{actionType: "NAVIGATION", url: COMBINED_WEIGHT_LOSS_CLP_DEEPLINK, title: "Update your Habits"}]
        }
        switch (action.actionType) {
            case "BOOK_TESTS":
                return CareUtil.getBookTestActionsFromActionContext(userContext, patientId, action, subCategoryCode, homeCenterSelector)
            case "TAKE_HEALTH_ASSESSMENT":
                return CareUtil.getHealthAssessmentActionsFromActionContext(action, "Start now")
            case "BOOK_CONSULTATION":
                return [primaryAction]
            case "VIEW_PLAN":
                return this.getViewPlanActions()
        }
    }


    private getViewPlanActions(): Action[] {
        return [
            {
                actionType: "NAVIGATION",
                title: "View plan"
            }
        ]
    }

    private getSummaryWidget(
        userContext: UserContext,
        product: DiagnosticProduct,
        bookingDetail: BookingDetail,
        aggregatedMembershipInfo: AggregatedMembershipInfo,
        issuesMap: Map<string, CustomerIssueType[]>,
        newReportIssueManageOption: ManageOptionPayload,
        isMPV2: boolean
    ): WidgetView {
        let manageOptionsView: { manageOptions: ManageOptions; meta: any } = null
        manageOptionsView = this.getManageOptions(bookingDetail, bookingDetail.booking.cfOrderId,
            bookingDetail.booking.productCode, issuesMap, newReportIssueManageOption)

        let breadCrumbs: IBreadCrumb[] = []
        if (CareUtil.isTransformTenant(product.subCategoryCode)) {
            breadCrumbs = [{ text: "Home", link: "/" }, { text: "Cult", link: "/cult" }, { text: "Transform Coach" }]
        } else {
            [{ text: "Home", link: "/" }, { text: "Care", link: "/care" }, { text: product.subCategoryCode === "DIAG_PACK" ? "Diagnostic Packs" : "Health Checkup" }]
        }
        const checkupSummaryWidget: WidgetView & {
            productId: string;
            title: string;
            subTitle: string;
            image: string;
            meta: any
            breadcrumb?: { text: string, link?: string }[],
            manageOptions: ManageOptions,
            hasDividerBelow: boolean,
            withOutMoreIndex?: boolean,
            packProgress?: PackProgress,
            productType?: ProductType
        } = {
            widgetType: "HEALTHCHECKUP_SUMMARY_WIDGET",
            subTitle: isMPV2 ? "Meet your doctor followed by nutritionist to start progress on your medical & lifestyle changes" : this.getSubTitle(bookingDetail, aggregatedMembershipInfo, userContext),
            title: product.title,
            productId: product.productId,
            image: product.heroImageUrl,
            breadcrumb: userContext.sessionInfo.userAgent === "DESKTOP" ?  breadCrumbs : [],
            manageOptions: manageOptionsView.manageOptions,
            meta: manageOptionsView.meta,
            hasDividerBelow: false,
            withOutMoreIndex: isMPV2 ? true : false,
            productType: "BUNDLE"
        }
        if (aggregatedMembershipInfo) {
            checkupSummaryWidget.packProgress = this.getPackProgress(userContext, aggregatedMembershipInfo)
        }
        return checkupSummaryWidget
    }

    private getPackProgress(userContext: UserContext, aggregatedMembershipInfo: AggregatedMembershipInfo): PackProgress {
        let packProgress: PackProgress
        if (_.isEmpty(aggregatedMembershipInfo.startEndEpoch)) {
            return undefined
        }
        const { total, completed, isCompleted, endDateFormatted } = CareUtil.getConsultationPackProgressInfo(userContext, aggregatedMembershipInfo, false)
        if (userContext.sessionInfo.userAgent  === "APP") {
            packProgress = {
                total,
                completed,
                state: "ACTIVE",
                type: <ProductType>"BUNDLE",
                leftText: endDateFormatted,
                rightText: " ", // Adding this hack to fix app alignment issue
                progressBarTextStyle: { marginTop: 0, fontSize: 14 },
                progressBarColor: "#008300"
            }
        } else {
            packProgress = {
                title: endDateFormatted,
                subTitle: " ", // Adding this hack to fix web alignment issue
                total,
                completed,
                state: isCompleted ? "COMPLETED" : "ACTIVE",
                packColor: "#008300",
                type: <ProductType>"BUNDLE",
                action: undefined
            }
        }
        return packProgress
    }

    private getSubTitle(bookingDetail: BookingDetail, aggregatedMembershipInfo: AggregatedMembershipInfo, userContext: UserContext) {
        const age = !_.isEmpty(bookingDetail) && !_.isEmpty(bookingDetail.bundleOrderResponse.patient.formattedAge) ? bookingDetail.bundleOrderResponse.patient.formattedAge.numOfYears : undefined
        if (bookingDetail.booking.status === "CANCELLED") {
            return "Cancelled"
        } else if (CareUtil.isTransformTenant(bookingDetail.booking.subCategoryCode)) {
            const tz = userContext.userProfile.timezone
            const endDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(bookingDetail.bundleOrderResponse.expiryTimeEpoch)), tz)
            const endDateFormatted = endDate.format("D MMM YYYY")
            const startDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(bookingDetail.bundleOrderResponse.startTimeEpoch)), tz)
            const startDateFormatted = startDate.format("D MMM YYYY")
            return `${startDateFormatted} - ${endDateFormatted}`
        } else if (_.isEmpty(aggregatedMembershipInfo)) {
            return _.get(bookingDetail, "bundleOrderResponse.patient.name") ? `For: ${bookingDetail.bundleOrderResponse.patient.name} | Pack Expired` : undefined
        } else if (age) {
            return `For ${bookingDetail.bundleOrderResponse.patient.name}, ${age}`
        } else {
            return `For ${bookingDetail.bundleOrderResponse.patient.name}`
        }
    }


    private getDiagnosticReportSummaryWidget(userContext: UserContext, bookingInfo: BookingDetail, isNewEmailedReportSupported?: boolean): DiagnosticsTestReportSummaryWidget | NavigationCardWidget {
        const reportItems: DiagnosticTestReportItem[] = []
        const diagnosticData = bookingInfo.stepInfosV2.find(stepInfo => {
            return stepInfo.stepInfoType === "DIAGNOSTIC_TEST"
        })
        if (!_.isEmpty(diagnosticData) && diagnosticData.stepState !== "REPORT_GENERATED") {
            return
        }
        const diagnosticOrderResponse = diagnosticData.diagnosticsTestBookingInfo.diagnosticsTestOrderResponse[0]
        const reportInfo = diagnosticOrderResponse.finalDiagnosticReport.diagnosticCheckUpReportInfo
        const widget = CareUtil.getTestReportNullAction(userContext, diagnosticOrderResponse, reportInfo)
        // If new emailed report widget supported, don't show null report widget
        // if (isNewEmailedReportSupported && widget) {
        //     return null
        // } else if (widget) {
        //     return widget
        // }
        if (widget) {
            return widget
        }
        const testDetails = CareUtil.getTestReportDetailsView(reportInfo)
        reportItems.push(
            {
                header: {
                    title: reportInfo.testId,
                    subtitle: "View now", // `${reportInfo.testCount} tests`,
                    action: {
                        url: ActionUtil.diagnosticReportPage(diagnosticOrderResponse.orderId, diagnosticOrderResponse.carefitOrderId),
                        actionType: "NAVIGATION"
                    }
                },
                // testInfos: testDetails
            }
        )
        return new DiagnosticsTestReportSummaryWidget(reportItems)
    }

    private getInstructionSummaryWidget(bookingInfo: BookingDetail): any {
        const consultationData = bookingInfo.stepInfosV2.filter(stepInfo => {
            return stepInfo.stepInfoType === "CONSULTATION"
        }).filter(item => {
            return item.stepState === "BOOKED"
        })
        if (!_.isEmpty(consultationData)) {
            return new InstructionsWidget(CareUtil.getInstructionsForMPV2ConsultationAppointments(), undefined, true)
        }
        return undefined
    }
}

export default ManagedPlanOnboardingProductPageView
