import { Container, inject } from "inversify"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import AuthMiddleware from "../auth/AuthMiddleware"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { IUserService, USER_CLIENT_TYPES } from "@curefit/user-client"
import * as express from "express"
import CartViewBuilder, { CartReviewView } from "../cart/CartViewBuilder"
import { BaseOrder, CartReviewPayload, Order, OrderSource } from "@curefit/order-common"
import * as _ from "lodash"
import { ErrorCodes } from "../error/ErrorCodes"
import { ErrorFactory, HTTP_CODE } from "@curefit/error-client"
import AppUtil from "../util/AppUtil"
import { Tenant, User } from "@curefit/user-common"
import { AppTenant } from "@curefit/base-common"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { CreateGatewayOrderResponse, OMS_API_CLIENT_TYPES, OrderCreate, IOrderService } from "@curefit/oms-api-client"
import { Session, UserContext } from "@curefit/userinfo-common"
import { OrderCheckoutDetail } from "@curefit/apps-common"
import BaseOrderConfirmationViewBuilder, {
    ConfirmationRequestParams,
    ConfirmationView,
    FitClubConfirmationView
} from "../order/BaseOrderConfirmationViewBuilder"
import { PromiseCache } from "../util/VMUtil"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import IUserBusiness from "../user/IUserBusiness"
import OrderViewBuilder from "../order/OrderViewBuilder"
import { UserDeliveryAddress } from "@curefit/eat-common"
import { PaymentOptionExtendedView } from "../payment/paymentModels"
import { OrderUtil } from "@curefit/base-utils"
import { IPaymentClient, PAYMENT_TYPES, PaymentPromUtil } from "@curefit/payment-client"
import { IPaymentOptionsBusinessV2 } from "../payment/PaymentOptionsBusinessV2"
import { BASE_TYPES, CLSUtil, Logger } from "@curefit/base"
import { DeviceType, InitiatePayloadRequest, InitiatePayloadResponse, PaymentChannel, PaymentData, PaymentMode } from "@curefit/payment-common"
import { PaymentUtil } from "@curefit/payment-models"
import OrderConfirmationViewBuilderV1 from "../order/OrderConfirmationViewBuilderV1"
import { ALBUS_CLIENT_TYPES, IHealthfaceService } from "@curefit/albus-client"
import { IPatientDetail, Patient } from "@curefit/care-common"
import { HealthfaceTenant, Product } from "@curefit/product-common"
import { IIndusService, INDUS_CLIENT_TYPES, UserOfferResponseV2 } from "@curefit/indus-client"
import { NEST_CLIENT_TYPES, NestService } from "@curefit/nest-node-client"
import { ICFAPICityService } from "../city/ICFAPICityService"
import { IUserAttributeClient, RASHI_CLIENT_TYPES } from "@curefit/rashi-client"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

export function controllerFactory(kernel: Container) {
    @controller("/chroniccare", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class ChronicCareController {

        constructor(@inject(BASE_TYPES.ClsUtil) private clsUtil: CLSUtil,
                    @inject(BASE_TYPES.ILogger) private logger: Logger,
                    @inject(CUREFIT_API_TYPES.ServiceInterfaces) private interfaces: CFServiceInterfaces,
                    @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
                    @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
                    @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
                    @inject(OMS_API_CLIENT_TYPES.OrderService) private omsApiClient: IOrderService,
                    @inject(CUREFIT_API_TYPES.CartViewBuilder) private cartViewBuilder: CartViewBuilder,
                    @inject(CUREFIT_API_TYPES.UserBusiness) private userBusiness: IUserBusiness,
                    @inject(CUREFIT_API_TYPES.OrderViewBuilder) private orderViewBuilder: OrderViewBuilder,
                    @inject(PAYMENT_TYPES.PaymentPromUtil) protected paymentPromUtil: PaymentPromUtil,
                    @inject(CUREFIT_API_TYPES.PaymentOptionsBusinessV2) protected paymentOptionsBusinessV2: IPaymentOptionsBusinessV2,
                    @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
                    @inject(CUREFIT_API_TYPES.OrderConfirmationViewBuilder) private orderConfirmationViewBuilder: BaseOrderConfirmationViewBuilder,
                    @inject(CUREFIT_API_TYPES.OrderConfirmationViewBuilderV1) private orderConfirmationViewBuilderV1: OrderConfirmationViewBuilderV1,
                    @inject(PAYMENT_TYPES.IPaymentClient) protected paymentClient: IPaymentClient,
                    @inject(INDUS_CLIENT_TYPES.IndusService) private indusService: IIndusService,
                    @inject(NEST_CLIENT_TYPES.NestService) private nestService: NestService,
                    @inject(CUREFIT_API_TYPES.CFAPICityService) private CFAPICityService: ICFAPICityService,
                    @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
                    @inject(RASHI_CLIENT_TYPES.UserAttributeClient) public userAttributeClient: IUserAttributeClient,
                    @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
                    ) {
        }

        @httpPost("/guest/cart/review/v1")
        async cartReviewV1(req: express.Request): Promise<CartReviewView> {
            const cartReviewPayload: CartReviewPayload = req.body
            const deviceId = req.headers["deviceid"] as string
            const userContext = req.userContext as UserContext
            const vertical: string = "CARE"
            const apiKey: string = req.headers["apikey"] as string
            const orderSource: OrderSource = AppUtil.callSource(apiKey)

            if (_.isEmpty(cartReviewPayload.orderProducts)) {
                throw this.errorFactory.withCode(ErrorCodes.CART_EMPTY_ERR, 400).withDebugMessage("No items in cart").build()
            }
            const uniqueProducts = [...new Map(cartReviewPayload.orderProducts.map(item => [item.productId, item])).values()]
            const user = await this.getUser(req.body.phoneNumber, req.body.name)
            const result = AppUtil.getUserAlertInfo(user, userContext)
            const code: string = result.code
            if (!_.isEmpty(code)) {
                throw this.errorFactory.withCode(result.code, result.alertInfo.statusCode).build()
            }
            const centerServiceId = uniqueProducts[0].option.centerId
            const createOrderPayload: OrderCreate = {
                userId: user.id,
                deviceId: deviceId,
                products: uniqueProducts,
                source: orderSource,
                useOffersV2: true,
                dontCreateRazorpayOrder: true,
                useFitCash: false,
                tenant: Tenant.SUGARFIT_APP,
                offersVersion: 3
            }
            const orderProduct: Product =  await this.catalogueService.getProduct(uniqueProducts.at(0).productId)
            if (orderProduct.productType !== "SF_CONSUMABLE") {
                const patient: Patient = await this.getOrCreatePatient(user)
                createOrderPayload.products[0].option.patientId = patient.id
            }

            let couponData: UserOfferResponseV2
            try {
                if (["SF_CONSUMABLE"].includes(orderProduct.productType)) {
                    if (cartReviewPayload?.couponId === "") {
                        // User manually removes the coupon
                        const couponResponse = await this.indusService.applyOfferV2(Number(user.id), " ")
                        if (couponResponse) {
                            couponData = { couponCode: cartReviewPayload?.couponId, message: "", isValid: couponResponse?.isValid }
                        }
                    } else if (!_.isNil(cartReviewPayload?.couponId)) {
                        // User applies the coupon
                        const couponResponse = await this.indusService.applyOfferV2(Number(user.id), cartReviewPayload?.couponId)
                        if (couponResponse) {
                            couponData = couponResponse
                        }
                    } else if (_.isNil(cartReviewPayload?.couponId)) {
                        // Check if perviously applied coupons are present. Could be user applied coupona nd logged out and relogged-in.
                        const couponResponse = await this.indusService.getAppliedOffers(Number(user.id))
                        if (couponResponse) {
                            couponData = couponResponse
                        }
                    }
                }
            } catch (e) {
                try {
                    this.logger.error("Error in coupon", e)
                } catch (e) {
                    this.logger.info("Error in coupon ==> Json parser")
                }
                couponData = { couponCode: cartReviewPayload?.couponId, message: "", isValid: false }
            }

            const cartReviewResult = await this.omsApiClient.cartReview(createOrderPayload)
            return this.cartViewBuilder.buildCartReviewViewV1(userContext, vertical, cartReviewResult.order, cartReviewResult.payByMembershipDetails, cartReviewPayload,
                user.id, deviceId, this.userAttributeClient, user, +centerServiceId, couponData)
        }

        @httpPost("/guest/cart/checkout/v1")
        async cartCheckoutV1(req: express.Request): Promise<OrderCheckoutDetail | CartReviewView | ConfirmationView> {
            const cartReviewPayload: CartReviewPayload = req.body
            const userContext: UserContext = req.userContext as UserContext
            userContext.userProfile.promiseMapCache ??= new PromiseCache(this.interfaces)
            const apiKey: string = req.headers["apikey"] as string
            const deviceId = req.headers["deviceid"] as string
            const orderSource: OrderSource = AppUtil.callSource(apiKey)
            if (_.isEmpty(cartReviewPayload.orderProducts)) {
                throw this.errorFactory.withCode(ErrorCodes.CART_EMPTY_ERR, 400).withDebugMessage("No items in cart").build()
            }
            const user = await this.getUser(req.body.phoneNumber, req.body.name)
            const result = AppUtil.getUserAlertInfo(user, userContext)
            const code: string = result.code
            if (!_.isEmpty(code))
                throw this.errorFactory.withCode(result.code, result.alertInfo.statusCode).build()
            const uniqueProducts = [...new Map(cartReviewPayload.orderProducts.map(item => [item.productId, item])).values()]
            const centerId = uniqueProducts[0].option.centerId
            const createOrderPayload: OrderCreate = {
                userId: user.id,
                deviceId: deviceId,
                products: uniqueProducts,
                useOffersV2: true,
                source: orderSource,
                dontCreateRazorpayOrder: true,
                useFitCash: false,
                tenant: Tenant.SUGARFIT_APP,
                osName: userContext.sessionInfo.osName,
                offersVersion: 3,
                appVersion: (userContext.sessionInfo.appVersion ?? 0).toString(),
            }
            const orderProduct: Product =  await this.catalogueService.getProduct(uniqueProducts.at(0).productId)
            if ( orderProduct.productType !== "SF_CONSUMABLE") {
                const patient: Patient = await this.getOrCreatePatient(user)
                createOrderPayload.products[0].option.patientId = patient.id
            }
            if (!_.isNil(cartReviewPayload.advertiserId)) createOrderPayload.advertiserId = cartReviewPayload.advertiserId
            if (!_.isEmpty(cartReviewPayload.careOptions)) {
                createOrderPayload.orderCreationMeta = {careMeta: {diagnosticCartId: cartReviewPayload.careOptions.diagnosticCartId}}
            }
            const addressid: string = createOrderPayload.products[0].option.addressId
            if (addressid) {
                createOrderPayload.address = await this.userBusiness.getAddress(user.id, addressid)
            }
            if (centerId) {
                createOrderPayload.clientMetadata = Object.assign({}, createOrderPayload.clientMetadata, {
                    centerId: centerId
                })
            }
            const order = await this.omsApiClient.createOrder(createOrderPayload)
            return await this.orderViewBuilder.buildView({
                userContext: userContext,
                order: order,
                product: order.productSnapshots[0]
            })
        }

        @httpPost("/guest/address")
        async getGuestAddresses(req: express.Request): Promise<UserDeliveryAddress[]> {
            const user = await this.getUser(req.body.phoneNumber, req.body.name)
            return await this.userBusiness.getAddresses(user.id, "CARE")
        }

        @httpPost("/guest/add/address")
        async addGuestAddresses(req: express.Request): Promise<UserDeliveryAddress> {
            const user = await this.getUser(req.body.phoneNumber, req.body.name)
            const userAddress: UserDeliveryAddress = req.body.userAddress
            userAddress.userId = user.id
            const userDeliveryAddress = await this.userBusiness.addAddress(user.id, userAddress)
            userDeliveryAddress.userId = null
            return userDeliveryAddress
        }

        @httpGet("/payment/options/v2", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        async paymentOptionsV2(req: express.Request, res: express.Response): Promise<PaymentOptionExtendedView> {
            const userContext: UserContext = req.userContext as UserContext
            this.logger.info(`Payment options user context :${JSON.stringify(userContext)}`)
            const user = await this.getUser(req.query.phoneNumber, req.query.name)
            userContext.userProfile.userId = user.id
            userContext.userPromise = this.getUser(req.query.phoneNumber, req.query.name)
            const userId: string = _.get(userContext, "userProfile.userId")
            const orderId: string = req.query.orderId
            const order: BaseOrder = await this.omsApiClient.getOrder(orderId)
            this.logger.info(`Sugarfit getting payment config, userID: ${userId}, orderID: ${orderId}`)
            this.paymentPromUtil.reportPaymentOptionsFetched(order.source, OrderUtil.getVerticalName(_.get(order, "productSnapshots[0]")))
            return this.paymentOptionsBusinessV2.getPaymentOptionsForApp({
                order: order,
                userContext: userContext
            })
        }

        @httpPost("/order/:orderId/paymentSuccess")
        async orderPaymentSuccess(req: express.Request): Promise<ConfirmationView | FitClubConfirmationView> {
            const orderId: string = req.params.orderId
            const order = await this.fetchOrderWithValidation(orderId)
            this.clsUtil.getNamespace().set(CLSUtil.USER_ID_FIELD, order.userId)
            const bypassPayment: boolean = req.query.bypassPayment ? true : false
            const data: { paymentData: PaymentData } = req.body
            let paymentData: PaymentData
            if (!_.isNil(data.paymentData.cfPaymentId)) {
                paymentData = PaymentUtil.findPaymentByCfPaymentId(order, data.paymentData.cfPaymentId)
            }
            if (_.isNil(paymentData) && !_.isNil(data.paymentData.paymentId)) {
                paymentData = PaymentUtil.findPaymentByPaymentId(order, data.paymentData.paymentId, data.paymentData.channel)
            }
            if (_.isNil(paymentData) && !_.isNil(data.paymentData.gatewayId)) {
                paymentData = PaymentUtil.findPaymentByGatewayId(order, data.paymentData.gatewayId, data.paymentData.channel)
            }
            if (_.isNil(paymentData) && !_.isEmpty(order.payments)) {
                paymentData = order.payments[order.payments.length - 1]
            }
            const paymentCompletionHandler = paymentData?.paymentCompletionHandler
            let successOrder: BaseOrder
            switch (paymentCompletionHandler) {
                case "OMS_IMMEDIATE":
                case "OMS_CUSTOM":
                    const res = await this.omsApiClient.paymentComplete({
                        orderId: order.orderId,
                        cfPaymentId: paymentData?.cfPaymentId,
                        initiationSource: "APP"
                    })
                    if (res.result !== "success") {
                        const errorCode = (res.currentStage === "PAYMENT_VERIFICATION")
                            ? "ERR_ALFRED_PAYMENT_VALIDATION_PENDING"
                            : "ERR_ALFRED_REFUND_INITIATED_FUFILMENT_FAILURE"
                        throw this.errorFactory.withCode(errorCode, HTTP_CODE.INTERNAL_SERVER_ERROR).build()
                    }
                    successOrder = await this.omsApiClient.getOrder(order.orderId)
                    break
                case "ALFRED":
                default:
                    successOrder = await this.omsApiClient.paymentSuccess(orderId, data, "APP")
                    break

            }
            await this.updateUser(order.userId, req.body.name, req.body.emailId)
            return this.getOrderConfirmationView(req, successOrder)
        }

        @httpPost("/guest/juspay/initiatePayload")
        async initateJuspayPayload(req: express.Request, res: express.Response) {
            const userContext: UserContext = req.userContext as UserContext
            const user = await this.getUser(req.body.phoneNumber, req.body.name)

            const userId: string = user.id
            const deviceId: string = req.headers["deviceid"] as string
            const appVersion: number = _.get(userContext, "sessionInfo.appVersion", 0)
            const osName: string = _.get(userContext, "sessionInfo.osName")
            const cityId = _.get(req, "session.sessionData.cityId")
            const apiKey: string = req.headers["api-key"] as string || req.headers["apikey"] as string
            const orderSource: OrderSource = AppUtil.callSource(apiKey)
            if (_.isNil(userId) || _.isNil(orderSource) || _.isNil(appVersion)) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Provide the userId, orderSource and appVersion").withContext({
                    userId, orderSource, appVersion
                }).build()
            }
            const {hyperSDKDiv, integrationType} = req.body
            const initateJuspayRequestPayload: InitiatePayloadRequest = {
                userId,
                deviceId,
                orderSource,
                appVersion,
                hyperSDKDiv,
                integrationType,
                osName: osName as DeviceType,
                meta: {
                    cityId
                }
            }
            return this.paymentClient.createInitiateJuspayPayload(initateJuspayRequestPayload)
        }

        @httpPost("/guest/juspay/processPayload/:orderId", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        async processJuspayPayload(req: express.Request): Promise<any> {
            const orderId: string = req.params.orderId
            const redirectUrl: string = req.body.redirectUrl
            const meta: any = req.body.meta || {}
            const userContext: UserContext = req.userContext as UserContext
            const user = await this.getUser(req.body.phoneNumber, req.body.name)
            this.clsUtil.getNamespace().set(CLSUtil.USER_ID_FIELD, `${user.id}`)
            userContext.userProfile.userId = user.id
            userContext.userPromise = this.getUser(req.query.phoneNumber, req.query.name)
            const userId: string = `${user.id}`
            const orderSource: OrderSource = _.get(userContext, "sessionInfo.orderSource")
            const appVersion: number = _.get(userContext, "sessionInfo.appVersion", 0)
            const osName: string = _.get(userContext, "sessionInfo.osName")
            const isMweb: boolean = AppUtil.isMWeb(userContext)
            _.set(meta, "appVersion", appVersion)
            _.set(meta, "osName", osName)
            _.set(meta, "isMweb", isMweb)
            this.logger.debug(`Trying to call OMS process payload ${orderId}`)
            return this.omsApiClient.processPayload({
                orderId,
                userId,
                orderSource,
                redirectUrl,
                meta,
            })
        }

        getStaticUserId() {
            if (process.env.ENVIRONMENT === "STAGE") {
                return 64892468
            } else {
                return 98411844
            }
        }

        @httpPost("/ew/chat/post")
        async lokiPostChat(req: express.Request, res: express.Response) {
            const { phoneNumber, name, postBody } = req.body
            if (
              _.isEmpty(phoneNumber) ||
              _.isEmpty(name) ||
              _.isEmpty(postBody)
            ) {
              res.status(400).send({ message: "Bad Request" })
              return
            }

            const userId = await this.getUserIdOrStaticUserId(phoneNumber, name)

            const post = await this.nestService.addLokiPost({
              body: postBody,
              userId,
              communityId: 3,
              userType: "USER",
              metadata: {
                userName: name,
                userPhoneNumber: phoneNumber
              }
            })

            return { postId: post.id }
        }

        @httpPost("/ew/chat/post/smart")
        async lokiPostSmartChat(req: express.Request, res: express.Response) {
            const { phoneNumber, name, postBody, bundlePrice, bundleProductCode, purchaseLink } = req.body
            if (
              _.isEmpty(phoneNumber) ||
              _.isEmpty(name) ||
              _.isEmpty(postBody) ||
              _.isEmpty(bundlePrice) ||
              _.isEmpty(bundleProductCode) ||
              _.isEmpty(purchaseLink)
            ) {
              res.status(400).send({ message: "Bad Request" })
              return
            }

            const userId = await this.getUserIdOrStaticUserId(phoneNumber, name)

            const post = await this.nestService.addSmartPost({
              body: postBody,
              userId: Number(userId),
              communityId: 4,
              userType: "USER",
              metadata: {
                EWBundlePrice: bundlePrice,
                EWBundleProductCode: bundleProductCode,
                EWPurchaseLink: purchaseLink,
                userName: name,
                userPhoneNumber: phoneNumber
              }
            })

            return { postId: post.id }
        }

        @httpGet("/ew/chat/reply")
        async lokiGetChatReply(req: express.Request, res: express.Response) {
            const { phoneNumber, name, postId } = req.query
            if (_.isEmpty(phoneNumber) || _.isEmpty(name) || _.isEmpty(postId)) {
                res.status(400).send({ message: "Bad Request" })
                return
            }
            const userId = await this.getUserIdOrStaticUserId(phoneNumber, name)

            return this.nestService.getLokiPostComment({ postId, userId })
        }

        @httpGet("/ip/details")
        async getLocationUsingIp(req: express.Request, res: express.Response) {
            const ip = req.ip || req.socket.remoteAddress

            const result = await this.CFAPICityService.getCityAndCountryByIp(Tenant.SUGARFIT_APP, ip)
            return { ip, ...result }
        }

        @httpPost("/order/:orderId/paymentFailure")
        async orderPaymentFailure(req: express.Request): Promise<boolean> {
            const orderId: string = req.params.orderId
            const paymentData: { paymentData: any } = {
                paymentData: req.body
            }
            return this.omsApiClient.paymentFailure(orderId, paymentData)
        }

        @httpPost("/order/createGatewayOrder/:orderId")
        async createGatewayOrder(req: express.Request): Promise<CreateGatewayOrderResponse> {
            const orderId: string = req.params.orderId
            const gatewayType: string = req.body.gatewayType
            const paymentMode: PaymentMode = req.body.paymentMode
            const redirectUrl: string = req.body.redirectUrl
            const failureUrl: string = req.body.failureUrl
            const requestMeta: any = req.body.meta
            const user = await this.getUser(req.body.phoneNumber, req.body.name)
            this.clsUtil.getNamespace().set(CLSUtil.USER_ID_FIELD, user.id)
            return this.createGatewayOrderId(orderId, user.id, gatewayType, paymentMode, redirectUrl, failureUrl, requestMeta)
        }

        async getUser(phoneNumber: string, name: string): Promise<User> {
            const existingUser = await this.userService.getUserUsingPhoneAndTenantV3(`+91-${phoneNumber}`, AppTenant.SUGARFIT)
            if (!existingUser) {
                const message = `No user present with phoneNumber:: ${phoneNumber}`
                throw new Error(message)
            } else {
                this.logger.info(`Existing user for phoneNumber :: ${phoneNumber}, user :: `, {existingUser})
                return existingUser
            }
        }

        async getUserIdOrStaticUserId(phoneNumber: string, name: string): Promise<number> {
            let userId = this.getStaticUserId()
            try {
              const user = await this.getUser(phoneNumber, name)
              userId = Number(user.id)
            } catch (error) {
              // Could not find the user, or user-service is not available.
              // Either case just go ahead with a static userId
              this.logger.error(`Error in finding user :: ${error}`)
              this.rollbarService.sendError(error)
              this.logger.info("Falling back to static userId")
            }

            return userId
        }

        async getOrCreatePatient(user: User): Promise<Patient> {
            this.clsUtil.getNamespace().set(CLSUtil.USER_ID_FIELD, user.id)
            const existingPatients = await this.healthfaceService.getAllPatients(user.id, "SUGARFIT")
            const selfPatient = existingPatients.filter(patient => patient.relationship === "Self")
            if (selfPatient.length > 0) {
                return selfPatient.at(0)
            } else {
                const patient: Patient = {
                    dateOfBirth: null,
                    emailId: null,
                    phoneNumber: user.phone,
                    name: user.firstName + " " + user.lastName,
                    relationship: "Self",
                    curefitUserId: user.id
                }
                return await this.healthfaceService.createPatient(patient, user.id, "SUGARFIT")
            }
        }
        async updateUser(userId: string, name: string, emailId: string) {
            try {
                const [firstName, lastName] = name.split(/\s+(.*)/)
                await this.userService.setName(userId, firstName, lastName)
                await this.userService.setEmail(userId, emailId)
            } catch (err) {
                this.logger.error(`Error in updating user, userId :: ${userId}, name :: ${name}, emailId :: ${emailId}`, err)
            }
        }

        private async createGatewayOrderId(orderId: string, userId: string, gatewayType: string, paymentMode: PaymentMode, redirectUrl: string, failureUrl: string, requestMeta: any): Promise<CreateGatewayOrderResponse> {
            if (gatewayType === "JUSPAY" && _.isEmpty(redirectUrl)) {
                redirectUrl = "https://www.cult.fit/eat/about"
            }
            if (gatewayType !== "MANUAL") {
                const initiatePaymentResponse: CreateGatewayOrderResponse = await this.omsApiClient.initiatePayment({
                    orderId: orderId,
                    userId: userId.toString(),
                    gatewayType: gatewayType as PaymentChannel,
                    paymentMode: paymentMode,
                    redirectUrl: redirectUrl,
                    failureUrl: failureUrl,
                    meta: requestMeta
                })
                return initiatePaymentResponse
            } else {
                this.logger.error("MANUAL payment mode not supported")
            }
        }

        private async fetchOrderWithValidation(orderId: string) {
            const order = await this.omsApiClient.getOrder(orderId)
            if (_.isNil(order)) {
                throw this.errorFactory.withCode(ErrorCodes.ORDER_DOES_NOT_EXIST_ERR, 400).withDebugMessage("Order does not exist for orderId: " + orderId).build()
            }
            return order
        }

        private async getOrderConfirmationView(req: express.Request, order: Order): Promise<ConfirmationView | FitClubConfirmationView> {
            const userContext = req.userContext as UserContext
            const orderSource = userContext.sessionInfo.orderSource

            const params: ConfirmationRequestParams = {
                orderSource,
                userContext: userContext
            }
            return this.orderConfirmationViewBuilderV1.buildOrderConfirmationView(userContext, order, params)
        }
    }

    return ChronicCareController
}

export default controllerFactory