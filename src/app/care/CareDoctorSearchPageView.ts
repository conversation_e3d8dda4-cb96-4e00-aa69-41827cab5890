import { Offer } from "./../page/Page"
import { Agent<PERSON><PERSON>er<PERSON>n<PERSON><PERSON>, DoctorListingFilterResponse } from "@curefit/albus-client"
import { Action, ProductOffer, ProductOfferWidgetV2, WidgetView, IBreadCrumb } from "@curefit/apps-common"
import { OfferUtil, RUPEE_SYMBOL } from "@curefit/base-utils"
import { AgentFilter, Center, ConsultationProduct, Doctor, DOCTOR_TYPE, Patient, IContentSection } from "@curefit/care-common"
import { HamletBusiness } from "@curefit/hamlet-node-sdk"
import { OfferV2, PackOffersResponse } from "@curefit/offer-common"
import { ProductPrice } from "@curefit/product-common"
import { TimeUtil } from "@curefit/util-common"
import { ActionType, WidgetWithMetric } from "@curefit/vm-common"
import { UserContext } from "@curefit/vm-models"
import * as _ from "lodash"
import * as momentTz from "moment-timezone"
import {
    ChronicCareDoctorBannerWidget,
    ChronicCareDoctorD<PERSON>ilWidget,
    <PERSON><PERSON><PERSON><PERSON>Widget,
    DoctorD<PERSON>ilWidgetV2,
    getOffersWidget as getOfferCalloutWidget,
    getOffersWidget,
    MobileBreadCrumbWidget,
    NCDetailWidget,
    OfferCalloutWidget,
    ProductDetailPage,
    SearchListingWidget,
    TextWidget
} from "../common/views/WidgetView"
import AppUtil from "../util/AppUtil"
import CareUtil, { CARE_DOCTOR_PAGE_SUPPORTED, DOCTOR_DETAIL_WIDGET_V2_SUPPORTED } from "../util/CareUtil"
import { UserAgentType as UserAgent } from "@curefit/base-common/dist/src/models/Common"
import { AppSourceType } from "@curefit/userinfo-common"


export interface IDoctorAvailableSlot {
    startTime: number
    endTime: number
    centerId: number
    title: string
    action?: Action

}

interface OfferItem {
    price: ProductPrice
    offers: OfferV2[]
}
export interface CareDoctorSearchPageParams {
    productId: string,
    patientId?: number,
    parentBookingId?: number,
    isReschedule?: boolean,
    replacePage?: boolean,
    isDoctorSearch?: boolean,
    selectedFilterItems?: any
    filtersData?: DoctorListingFilterResponse
    categoryFilters?: AgentFilterInfos[]
    isPriceAvailableInListingApi?: boolean
    isUnifiedDoctorUISupported?: boolean
    showPrice?: boolean
    hideInfoWidget?: boolean
}

export interface CareDoctorSearchPagePromises {
    doctorsListPromise: Promise<Doctor[]>,
    centersPromise?: Promise<Center[]>,
    patientListPromise: Promise<Patient[]>,
    bannerWidgetPromise: Promise<WidgetWithMetric>,
    offerPromise?: Promise<PackOffersResponse> | Promise<{ [key: string]: PackOffersResponse}>
}

const inactiveNutritionists = [109431, 109432, 109433, 109434, 109435]
class CareDoctorSearchPageView extends ProductDetailPage {
    public hideHeader: boolean = false
    constructor() {
        super()
    }

    async getView(
        userContext: UserContext,
        product: ConsultationProduct,
        pagePromises: CareDoctorSearchPagePromises,
        doctorType: DOCTOR_TYPE,
        widgetParams: CareDoctorSearchPageParams,
        hamletBusiness: HamletBusiness,
        specialistContentSection?: IContentSection[],
        isOptumUser?: boolean
    ): Promise<any> {
        const widgetPromises: Promise<any>[] = []
        const appVersion = userContext.sessionInfo.appVersion
        const isWeb = AppUtil.isWeb(userContext)
        const isDesktop = AppUtil.isDesktop(userContext)
        const isChronicCare = CareUtil.getIfChronicCareFromUserContext(userContext)
        const userAgent = userContext.sessionInfo.userAgent
        const isNewDSCardSupported = appVersion >= CARE_DOCTOR_PAGE_SUPPORTED || userAgent === "MBROWSER"
        const isNewNutritionistFlowSupported = false // AppUtil.isNewNutritionistFlowSupported(userContext)
        if (isDesktop) {
            widgetPromises.push(this.getTCSummaryWidget(userContext, product, undefined, []))
            widgetPromises.push(this.getTextWidget(product))
            widgetPromises.push(this.getOfferInfoWidget(userContext, product, pagePromises, widgetParams.parentBookingId, undefined, widgetParams.isPriceAvailableInListingApi, widgetParams.showPrice))
            widgetPromises.push(this.getContentHrefWidget(specialistContentSection))
        } else if (isNewDSCardSupported && !isChronicCare) {
            if (isNewNutritionistFlowSupported && doctorType === "LC") {
                widgetPromises.push(this.getOfferInfoWidget(userContext, product, pagePromises, widgetParams.parentBookingId, true, widgetParams.isPriceAvailableInListingApi, widgetParams.showPrice))
            } else if (!widgetParams.hideInfoWidget && (_.isNil(widgetParams.parentBookingId) || widgetParams.showPrice)) {
                widgetPromises.push(CareUtil.getCareProductInfoWidget(product, pagePromises, widgetParams.isPriceAvailableInListingApi))
                widgetPromises.push(this.getContentHrefWidget(specialistContentSection))
                widgetPromises.push(this.getOfferInfoWidget(userContext, product, pagePromises, widgetParams.parentBookingId, undefined, widgetParams.isPriceAvailableInListingApi, widgetParams.showPrice))
            }
        }
        if (!isChronicCare) {
            widgetPromises.push(this.getBannerWidget(userContext, product, widgetParams, pagePromises))
        }
        if (isChronicCare) {
            widgetPromises.push(this.getChronicCareBannerWidget())
        }
        widgetPromises.push(this.getSearchListingWidget(
            userContext,
            product,
            doctorType,
            pagePromises,
            "DOCTOR",
            widgetParams,
            isNewDSCardSupported,
            isNewNutritionistFlowSupported,
            isChronicCare,
            isOptumUser
        ))

        isWeb && widgetPromises.push(this.getSpecialistContentWidget(specialistContentSection))
        return {
            title: isChronicCare ? "Select your preferred Doctor" : isNewDSCardSupported ? undefined : `Find your ${CareUtil.getDoctorText(product.doctorType)}`,
            hideHeader: isNewDSCardSupported,
            widgets: (await Promise.all(widgetPromises)).filter(Boolean),
            pageImage: product.heroImageUrl
        }
    }

    private async getTextWidget(product: ConsultationProduct): Promise<TextWidget> {
        if (product.subTitle) {
            return new TextWidget("About", product.subTitle)
        }
        return undefined
    }

    private async getOfferInfoWidget(
        userContext: UserContext,
        product: ConsultationProduct,
        pagePromises: CareDoctorSearchPagePromises,
        parentBookingId?: number,
        useOfferCallout?: boolean,
        isPriceAvailableInListingApi?: boolean,
        showPrice?: boolean
    ) {
        if (_.isNil(parentBookingId) || showPrice) {
            const offerDetails: OfferItem = await CareUtil.getOfferDetailsInDoctorListing(product, pagePromises, isPriceAvailableInListingApi)
            if (offerDetails) {
                const offerIds = _.map(offerDetails.offers, offer => { return offer.offerId })
                return !_.isEmpty(offerIds) ? this.getOfferWidget(userContext, offerDetails, useOfferCallout) : undefined
            }
        }
        return undefined
    }

    private async getContentHrefWidget(specialistContentSection?: IContentSection[]) {
      if (_.isEmpty(specialistContentSection)) {
          return
      }
      const contentHrefList: string[] = []
      specialistContentSection.map(item => {
        contentHrefList.push(item.title)
      })
      const contentHrefWidget: WidgetView & {
        contentHrefList: string[],
        showDivider: boolean
      } = {
          widgetType: "CARE_CONTENT_HREF_WIDGET",
          contentHrefList: contentHrefList,
          showDivider: true
      }
      return contentHrefWidget
    }

    private async getSpecialistContentWidget(specialistContentSection?: IContentSection[]) {
        if (_.isEmpty(specialistContentSection)) {
          return
        }
        const specialistContentWidget: WidgetView & {
          contentList: IContentSection[],
         showDivider: boolean
        } = {
            widgetType: "CARE_CONTENT_WIDGET",
            contentList: specialistContentSection,
            showDivider: true
        }
        return specialistContentWidget
    }

    private async getBannerWidget(
        userContext: UserContext,
        product: ConsultationProduct,
        params: CareDoctorSearchPageParams,
        pagePromises: CareDoctorSearchPagePromises
    ): Promise<WidgetWithMetric> {
        const isCareDoctorType = !(CareUtil.isMindDoctorType(product.doctorType) || CareUtil.isPsychiatry(product.doctorType) || product.doctorType === "LC")
        if (userContext.sessionInfo.userAgent !== "DESKTOP" && pagePromises.bannerWidgetPromise && (_.isNil(params.parentBookingId) || params.showPrice) && isCareDoctorType) {
            const banner = await pagePromises.bannerWidgetPromise
            if (banner) {
                return banner
            }
        }
        return undefined
    }

    private async getChronicCareBannerWidget(
    ): Promise<ChronicCareDoctorBannerWidget> {
        const result: ChronicCareDoctorBannerWidget = { widgetType: "CHRONIC_CARE_DOCTOR_BANNER_WIDGET" as any, subTitle: "Select a doctor you’d like to consult with through your reversal journey"}
        return result
    }

    private async getSearchListingWidget(
        userContext: UserContext,
        product: ConsultationProduct,
        doctorType: DOCTOR_TYPE,
        widgetInfoPromises: CareDoctorSearchPagePromises,
        listType: string,
        widgetParams: CareDoctorSearchPageParams,
        scrollToTopSupported: boolean,
        isNewNutritionistFlowSupported?: boolean,
        isChronicCare?: boolean,
        isOptumUser?: boolean
    ): Promise<SearchListingWidget> {
        const isTransform = CareUtil.isTransformDoctorType(product.doctorType) || product.tenant === "TRANSFORM"
        const userAgent = userContext.sessionInfo.userAgent
        const appVersion = userContext.sessionInfo.appVersion
        let centerOfferDetails = {}
        const isNewDoctorDetailWidgetSupported = (userAgent !== "APP" || appVersion >= DOCTOR_DETAIL_WIDGET_V2_SUPPORTED)
        const { doctorsListPromise, centersPromise, patientListPromise, offerPromise } = widgetInfoPromises
        const isPriceAvailableInListingApi = widgetParams.isPriceAvailableInListingApi
        let doctors = await doctorsListPromise
        doctors = doctors.filter(doctor => !inactiveNutritionists.includes(doctor.id))
        if (isOptumUser && !isTransform) {
            doctors = doctors?.filter(doc => [100266, 103364].includes(doc.id))
        }
        const patientsList = await patientListPromise
        if (offerPromise && !isPriceAvailableInListingApi && !CareUtil.isTherapyOnlyDoctorType(product.doctorType)) {
            centerOfferDetails = OfferUtil.getOfferAndPriceForCareCenter(product, ((await centersPromise).map(center => center.id.toString())), await offerPromise as {
                [key: string]: PackOffersResponse;
            })
        }

        // Temp fix for psy consult to pass self patient id
        const selfPatient = (CareUtil.isMindDoctorType(product.doctorType) || (CareUtil.isPsychiatry(product.doctorType) && !AppUtil.isPsyMultiPatientSupported(userContext))) && !_.isEmpty(patientsList) ? _.find(patientsList, patient => patient.relationship === "Self") : undefined
        widgetParams.patientId = widgetParams.patientId || (selfPatient ? selfPatient.id : undefined)

        const hasPreferredDoctor = !_.isNil(doctors.find((doctor) => doctor.preferredDoctor))
        const list: any[] = await Promise.all(doctors.map(doctor => {
            if (isChronicCare) {
                return this.getChronicCareDoctorDetailWidget(userContext, product, doctor, widgetParams, centerOfferDetails, hasPreferredDoctor)
            }
                return isNewDoctorDetailWidgetSupported
                    ? getDoctorDetailWidgetV2(userContext, product, doctor, widgetParams, centerOfferDetails, hasPreferredDoctor, widgetInfoPromises.offerPromise)
                    : this.getDoctorDetailWidget(userContext, product, doctor, widgetParams, centerOfferDetails, widgetInfoPromises.offerPromise)

        }))
        const doctorText = CareUtil.getDoctorText(doctorType)
        const searchResultText = isChronicCare ? "Available Doctors" : `Available ${doctorText}s`
        return {
            widgetType: "SEARCH_LISTING_WIDGET",
            title: undefined,
            listingTitle: CareUtil.getDoctorListingTitle(doctorType),
            list: list.sort(a => {
                return a.isPreferredDoctor ? -1 : 1
            }),
            searchResultText: searchResultText,
            listType: listType,
            placeholder: ` Search by name`,
            showMoreShadow: false,
            showFilter: true,
            autoFocus: false,
            isUnderline: true,
            customSearchEnabled: true,
            emptySearchText: "No search results",
            emptySearchImage: undefined, // "/image/singles/empty_search.png",
            showResultForEmptySearch: true,
            scrollToTopOnFocus: scrollToTopSupported,
            filterModalItems: isChronicCare ? undefined : CareUtil.getDoctorSearchFilterModalItems(userContext, doctorType, widgetParams.filtersData, widgetParams.parentBookingId),
            selectedFilterItems: widgetParams.selectedFilterItems,
            doctorCardFooterAction: CareUtil.getDoctorFooterCardAction(userContext, product, patientsList, widgetParams),
            ...CareUtil.getDoctorListingPaginationViewMoreCount(doctorType)
        }
    }

    private async getChronicCareDoctorDetailWidget(userContext: UserContext,
        product: ConsultationProduct,
        doctor: Doctor,
        params: CareDoctorSearchPageParams,
        offerMap?: {
            [key: string]: OfferItem
        },
        hasPreferredDoctor?: boolean): Promise<ChronicCareDoctorDetailWidget> {
            const doctorAction: Action = { actionType: "NAVIGATION", url: `curefit://chroniccaredoctorprofile?doctorId=${doctor.id}`, title: "View Profile"}
            return {
                widgetType: "CHRONIC_CARE_DOCTOR_DETAIL_WIDGET" as any,
                name: doctor.name,
                qualification: doctor.qualification,
                experience: doctor.experience ? `${doctor.experience} yrs of experience ` : undefined,
                displayImage: doctor.displayImage,
                id: doctor.id,
                action: doctorAction,
            }


    }

    // To do remove after 8.17 adoption
    private async getDoctorDetailWidget(
        userContext: UserContext,
        product: ConsultationProduct,
        doctor: Doctor,
        params: CareDoctorSearchPageParams,
        offerMap?: {
            [key: string]: OfferItem
        },
        offerPromise?: CareDoctorSearchPagePromises["offerPromise"]
    ): Promise<DoctorDetailWidget> {
        const timezone = userContext.userProfile.timezone
        const {
            productId,
            patientId,
            parentBookingId,
            isReschedule,
            replacePage,
            isDoctorSearch,
            isPriceAvailableInListingApi,
            categoryFilters,
            showPrice
        } = params
        const length = doctor.doctorCenterMapping && doctor.doctorCenterMapping.length
        let actionText, centerName
        if (doctor.earliestAvailability) {
            actionText = momentTz.tz(momentTz.tz(doctor.earliestAvailability.startTime, timezone), timezone).calendar(null, {
                lastDay: "[]",
                sameDay: userContext.sessionInfo.userAgent === "APP" ? "[Available\n] hh:mm A" : "[Available] hh:mm A",
                nextDay: "[Available Tomorrow]",
                lastWeek: "[Available] DD MMM",
                nextWeek: "[Available] DD MMM",
                sameElse: "[Available] DD MMM"
            })
            if (doctor.earliestAvailability.centerId) {
                const doctorCenterMappingItem = doctor.doctorCenterMapping.find(item => item.centerId === doctor.earliestAvailability.centerId)
                if (doctorCenterMappingItem) {
                    centerName = doctorCenterMappingItem.centerName
                }
            }
        }
        if (!centerName) {
            centerName = length ? doctor.doctorCenterMapping[0].centerName : undefined
        }
        const centerId = _.get(doctor, "earliestAvailability.centerId", _.get(doctor, "doctorCenterMapping[0].centerId", undefined))
        const priceInfo = await getDoctorPrice(product, isPriceAvailableInListingApi, doctor, centerId, offerMap, offerPromise)
        const isLCProduct = product && product.doctorType === "LC"
        const finalPrice = _.isNil(parentBookingId) || showPrice ?  (isLCProduct ? _.get(priceInfo, "price.listingPrice") : _.get(priceInfo, "price.mrp")) : 0
        const priceText = finalPrice ? `${RUPEE_SYMBOL} ${finalPrice}` : undefined
        return {
            widgetType: "DOCTOR_DETAIL_WIDGET",
            displayImage: doctor.displayImage,
            isPreferredDoctor: doctor.preferredDoctor,
            earliestAvailability: doctor.earliestAvailability,
            name: (userContext.sessionInfo.userAgent === "APP" && doctor.name.length > 24)
                ? (doctor.name.substring(0, 24) + "...")
                : doctor.name,
            qualification: userContext.sessionInfo.userAgent === "APP" && doctor.qualification && doctor.qualification.length > 28
                ? (doctor.qualification.substring(0, 28) + "...")
                : doctor.qualification,
            id: doctor.id,
            experience: (userContext.sessionInfo.userAgent !== "APP"
                ? (doctor.experience ? `${doctor.experience} yrs of experience ` : undefined)
                : (doctor.experience ? `${doctor.experience} yrs of experience ${priceText ? `\n${priceText}` : ""}` : priceText)
            ),
            price: finalPrice,
            detailedQualification: doctor.detailedQualification,
            description: doctor.description,
            subSpecialities: CareUtil.getDoctorSubSpecialityValue(categoryFilters, doctor) as string,
            action: CareUtil.getDoctorBookingScreenAction(
                "Book your appointment",
                {
                    doctorId: doctor.id,
                    slugValue: doctor.seoSlugValue,
                    productId,
                    patientId,
                    centerId,
                    parentBookingId,
                    doctorType: product?.doctorType || doctor?.primarySubServiceType?.code,
                    isReschedule: isReschedule ? "true" : undefined,
                    isDoctorSearch: isDoctorSearch ? "true" : undefined,
                    replacePage: replacePage ? "true&popLevel=2" : undefined,
                    showPrice: showPrice ? true : undefined,
                    appsource: userContext.sessionInfo.appSource
                },
                userContext
            ),
            footerDetails: length ? (
                length > 1
                    ? {
                        name: centerName ? (centerName + (length > 1 ? (userContext.sessionInfo.userAgent === "APP" ? "\n" : " | ") + `+${String(length - 1)} more` : "")) : undefined,
                        actionText: actionText
                    }
                    : length === 1
                    ? {
                        name: centerName || undefined,
                        actionText: actionText
                    } : {
                        name: undefined,
                        actionText: actionText
                    }
            ) : {
                name: undefined,
                actionText: actionText
            },
            fallsInExploreBucket: _.get(doctor, "fallsInExploreBucket", false)
        }
    }

    private async getTCSummaryWidget(userContext: UserContext, product: ConsultationProduct, price: ProductPrice, offerIds?: string[]) {
        const tcSummaryWidget: WidgetView & {
            productId: string
            title: string,
            subTitle: string,
            price: ProductPrice,
            image: string,
            breadcrumb?: IBreadCrumb[],
            preventWidgetImageDisplay: boolean
        } = {
            widgetType: "TELECONSULTATION_SUMMARY_WIDGET",
            preventWidgetImageDisplay: AppUtil.isDesktop(userContext),
            title: product.title,
            productId: product.productId,
            price: price ? price : undefined,
            image: product.heroImageUrl,
            subTitle: undefined,
            breadcrumb: []
        }
        return tcSummaryWidget
    }

    private getOfferWidget(userContext: UserContext,
        offerDetails: { price: ProductPrice, offers: OfferV2[] },
        useOfferCallout: boolean
    ): ProductOfferWidgetV2 | OfferCalloutWidget {
        const userAgent = userContext.sessionInfo.userAgent
        if (useOfferCallout) {
            const widgetData = getOfferCalloutWidget("Offers Applied", offerDetails.offers, userAgent, true)
            return widgetData?.offers?.length > 0 ? widgetData : undefined
        }

        if (userAgent !== "DESKTOP" ) {
            const actionType: ActionType = "SHOW_OFFERS_TNC_MODAL"
            const offerItems: ProductOffer[] = []
            offerDetails.offers.forEach(offer => {
                if (!_.isEmpty(offer) && !offer?.displayContexts?.includes("NONE") && offer.description) {
                    offerItems.push({
                        title: offer.description,
                        iconType: "/image/icons/cult/tick.png",
                        tnc: {
                            title: "T&C",
                            action: {
                                actionType: actionType,
                                meta: {
                                    title: "Offer Details",
                                    dataItems: offer.tNc,
                                    url: offer.tNcUrl
                                }
                            }
                        }
                    })
                }
            })

            if (offerItems?.length > 0) {
                return {
                    widgetType: "PRODUCT_OFFER_WIDGET_V2",
                    offerItems: [...offerItems],
                    dividerType: "NONE",
                    contentContainerStyle: { marginBottom: 20, backgroundColor: "#dfefe6" },
                    offersBackground: "#dfefe6"
                }
            }
            return null
        } else {
            const widgetData = getOffersWidget("Offers Applied", offerDetails.offers, userAgent, true)
            return widgetData?.offers?.length > 0 ? widgetData : undefined
        }
    }

}

const getDoctorPrice = async (product: ConsultationProduct, isPriceAvailableInListingApi: boolean, doctor: Doctor, centerId: number, offerMap: { [key: string]: OfferItem }, offerPromise?: CareDoctorSearchPagePromises["offerPromise"])  => {
    let priceInfo
    if (CareUtil.isTherapyOnlyDoctorType(product.doctorType) || CareUtil.isLCProduct(product)) {
        return undefined
    }

    if (CareUtil.isCovidSpecialist(product) && offerPromise) {
        const productPrice = OfferUtil.getPackOfferAndPrice(product, <PackOffersResponse> await offerPromise).price
        priceInfo = {
            price: {
                mrp: productPrice.mrp,
                listingPrice: productPrice.listingPrice,
                currency: "INR"
            }
        }
        return priceInfo
    }
    if (isPriceAvailableInListingApi) {
        const doctorCenterMappingItem = doctor.doctorCenterMapping.find(item => item.centerId === centerId)
       if (doctorCenterMappingItem) {
            priceInfo = {
                price: {
                    mrp: doctorCenterMappingItem.price,
                    listingPrice: doctorCenterMappingItem.price,
                    currency: "INR"
                }
            }
       }
    } else {
        priceInfo = centerId && offerMap[centerId]
    }
    return priceInfo
}


export const getDoctorDetailWidgetV2 = async (
    userContext: UserContext,
    product: ConsultationProduct,
    doctor: Doctor,
    params: CareDoctorSearchPageParams,
    offerMap?: {
        [key: string]: OfferItem
    },
    hasPreferredDoctor?: boolean,
    offerPromise?: CareDoctorSearchPagePromises["offerPromise"],
    skipFeatured?: boolean
): Promise<DoctorDetailWidgetV2> => {
    const {
        productId,
        patientId,
        parentBookingId,
        isReschedule,
        replacePage,
        isDoctorSearch,
        isPriceAvailableInListingApi,
        categoryFilters,
        showPrice
    } = params
    const centerId = _.get(doctor, "earliestAvailability.centerId", _.get(doctor, "doctorCenterMapping[0].centerId", undefined))
    const doctorBookingAction: Action = CareUtil.getDoctorBookingScreenAction(
        "VIEW PROFILE",
        {
            doctorId: doctor.id,
            slugValue: doctor.seoSlugValue,
            productId,
            patientId,
            parentBookingId,
            centerId,
            doctorType: product?.doctorType || doctor?.primarySubServiceType?.code,
            isReschedule: isReschedule ? "true" : undefined,
            isDoctorSearch: isDoctorSearch ? "true" : undefined,
            replacePage: replacePage ? "true&popLevel=2" : undefined,
            showPrice: showPrice ? true : undefined,
            appsource: userContext.sessionInfo.appSource
        },
        userContext
    )
    const isFromFlutterAppFlow = AppUtil.isFromFlutterAppFlow(userContext)
    const isNewSlotUISupported = userContext.sessionInfo.appVersion >= 8.27
    const timezone = userContext.userProfile.timezone
    const length = doctor.doctorCenterMapping && doctor.doctorCenterMapping.length
    let actionText, centerName, isTodayAvailable
    if (doctor.earliestAvailability) {
        actionText = momentTz.tz(momentTz.tz(doctor.earliestAvailability.startTime, timezone), timezone).calendar(null, {
            lastDay: "[]",
            sameDay: () => {
                isTodayAvailable = true
                return isNewSlotUISupported ? "[]" : "[Next available at] hh:mm A"
            },
            nextDay: "[Next available tomorrow]",
            lastWeek: "[Next available] DD MMM",
            nextWeek: "[Next available] DD MMM",
            sameElse: "[Available] DD MMM"
        })
    } else {
        actionText = "No slots available"
    }
    let doctorAvailableSlots: IDoctorAvailableSlot[]
    if (isTodayAvailable && isNewSlotUISupported && !_.isEmpty(doctor.earliestAvailabilityList)) {
        doctorAvailableSlots = CareUtil.getDoctorEarliestAvailabilitySlotAction(userContext, doctor, productId, patientId, parentBookingId, product, centerId, hasPreferredDoctor)
    }
    if (!_.isEmpty(doctorAvailableSlots) && doctorAvailableSlots.length > 2) {
        doctorAvailableSlots = doctorAvailableSlots.slice(0, 2)
    }
    if (centerId && length) {
        const doctorCenterMappingItem = doctor.doctorCenterMapping.find(item => item.centerId === centerId)
        if (doctorCenterMappingItem) {
            centerName = doctorCenterMappingItem.centerName
        }
    }
    const priceInfo = await getDoctorPrice(product, isPriceAvailableInListingApi, doctor, centerId, offerMap, offerPromise)
    const isLCorMindProduct = product && (product.doctorType === "LC" || CareUtil.isTherapyOnlyDoctorType(product.doctorType))
    const isLCorMindorCovidProduct = isLCorMindProduct || (product && CareUtil.isCovidSpecialist(product))
    return {
        widgetType: "DOCTOR_DETAIL_WIDGET_V2",
        displayImage: doctor.displayImage,
        earliestAvailability: doctor.earliestAvailability,
        earliestAvailabilityList: doctor.earliestAvailabilityList,
        isPreferredDoctor: doctor.preferredDoctor,
        name: doctor.name,
        qualification: doctor.qualification ? doctor.qualification : undefined,
        id: doctor.id,
        price: _.isNil(parentBookingId) || showPrice ? isLCorMindorCovidProduct ? _.get(priceInfo, "price.listingPrice") : _.get(priceInfo, "price.mrp") : 0,
        priceInfo: priceInfo && priceInfo.price ? (isLCorMindorCovidProduct ? priceInfo.price : {
            listingPrice: priceInfo && priceInfo.price.mrp,
            mrp: priceInfo && priceInfo.price.mrp,
            currency: priceInfo && priceInfo.price.currency
        }) : undefined,
        detailedQualification: doctor.detailedQualification,
        description: doctor.description,
        experience: (doctor.experience && doctor.experience !== 0 && !CareUtil.isTherapyOnlyDoctorType(product.doctorType) ? `${doctor.experience} ${doctor.experience > 1 ? "years" : "year"} exp` : undefined),
        action: doctorBookingAction,
        doctorAvailableSlots,
        isFooterDisabled: !doctor.earliestAvailability,
        footerDetails: length ? (
            length > 1
                ? {
                    name: !isLCorMindProduct && centerName ? (centerName + (length > 1 ? ` +${String(length - 1)} more` : "")) : undefined,
                    actionText: actionText
                }
                : length === 1
                    ? {
                        name: !isLCorMindProduct ? `${centerName}` : undefined,
                        actionText: actionText
                    } : { name: undefined , actionText: actionText}
        ) : { name: undefined , actionText: actionText},
        actionQueryParam: `&doctorId=${doctor.id}&centerId=${centerId}${replacePage ? "&replacePage=true&popLevel=1" : ""}`,
        subSpecialities: CareUtil.getDoctorSubSpecialityValue(categoryFilters, doctor, false, isFromFlutterAppFlow ? " • " : ", ") as string,
        fallsInExploreBucket: _.get(doctor, "fallsInExploreBucket", false),
        noOfSessionsText: doctor?.noOfSessionsTaken ? `${doctor?.noOfSessionsTaken} HAPPY ${doctor?.noOfSessionsTaken > 1 ? "SESSIONS" : "SESSION"}` : "NEW ON MIND.FIT",
        isFeatured: !skipFeatured && isFromFlutterAppFlow &&  doctor.isFeatured ? true : false,
        videoUrl: doctor.displayVideo
    }
}

export default CareDoctorSearchPageView
