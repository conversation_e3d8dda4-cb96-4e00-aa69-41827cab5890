import { inject, injectable } from "inversify"
import { ICatalogueService, CATALOG_CLIENT_TYPES } from "@curefit/catalog-client"
import {
    DiagnosticReport,
    DiagnosticReportInfo,
    DiagnosticTestReportDetails,
    TEST_RESULT_TYPE,
    DiagnosticEmailedTestReportItem,
    DiagnosticEmailedTestReportResponse
} from "@curefit/albus-client"
import { Action, ProductDetailPage } from "../common/views/WidgetView"
import {
    DiagnosticsTestDetailedSummaryWidget,
    DiagnosticsTestReportDetailWidget,
    DiagnosticsTestReportSummaryWidget,
    DiagnosticTestReportCell,
    DiagnosticTestReportItem,
    TestReportDetail,
    DiagnosticEmailTestReportItem,
    DiagnosticsEmailTestReportDetailedSummaryWidget
} from "../page/PageWidgets"
import * as _ from "lodash"
import { DiagnosticTestDetailCellType, PageWidget } from "../page/Page"
import CareUtil, { ABNORMAL_STATE_COLOR, NORMAL_STATE_COLOR } from "../util/CareUtil"
import { DiagnosticProduct } from "@curefit/care-common"
import { UserContext } from "@curefit/userinfo-common"

export class DiagnosticReportView extends ProductDetailPage {
    header?: {
        title: string,
    }
    constructor(widgets: PageWidget[], header?: any) {
        super()
        this.widgets = widgets
        this.header = header
    }
}

@injectable()
class DiagnosticReportViewBuilder {
    constructor(@inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService) {
    }

    async buildDiagnosticReportView(userContext: UserContext, testOrderId: number, reportData: DiagnosticReport, testId?: number, diagnosticEmailedReportResponse?: DiagnosticEmailedTestReportResponse): Promise<DiagnosticReportView> {
        let widgets: PageWidget[] = []
        let header: any
        // if testId is present, build test detail report page, otherwise test summary page
        if (_.isNil(testId)) {
            const isNewEmailReportWidgetSupported = CareUtil.isNewEmailedReportSupported(userContext) && diagnosticEmailedReportResponse && !_.isEmpty(diagnosticEmailedReportResponse.productDetails)
            const isNullReport = reportData &&
                reportData.diagnosticCheckUpReportInfo &&
                reportData.diagnosticCheckUpReportInfo.normalCount === 0 &&
                reportData.diagnosticCheckUpReportInfo.testCount === 0
            header = {
                title: "Diagnostic Report",
                action: `curefit://sendReportOnEmail?testOrderId=${testOrderId}`
            }
            const summaryWidget = this.getDiagnosticReportSummaryWidget(reportData)
            if (!isNewEmailReportWidgetSupported || !isNullReport) {
                widgets.push(summaryWidget)
            }
            if (!isNullReport) {
                const reportInfoForProfileTests: DiagnosticReportInfo[] = []
                const reportInfoForChildrenTests: DiagnosticReportInfo[] = []
                _.forEach(reportData.diagnosticCheckUpReportInfo.childCheckUpReportInfo, childReportInfo => {
                    if (childReportInfo.testCount === 1 && !_.isEmpty(childReportInfo.reportDetails)) {
                        reportInfoForChildrenTests.push(childReportInfo)
                    }
                    else {
                        reportInfoForProfileTests.push(childReportInfo)
                    }
                })
                widgets.push(await this.getDiagnosticChildReportSummaryWidget(reportInfoForProfileTests, testOrderId))
                const childReportWidgets = await this.getChildReportWidgets(reportInfoForChildrenTests)
                widgets = widgets.concat(childReportWidgets)
            }
            if (isNewEmailReportWidgetSupported) {
                widgets.push(await this.getDiagnosticEmailedTestReportDetailedWidget(diagnosticEmailedReportResponse))
            }
        }
        else {
            widgets.push(await this.getDiagnosticTestDetailSummaryWidget(reportData))
            const reportWidgets = await this.getChildReportWidgets(reportData.diagnosticCheckUpReportInfo.childCheckUpReportInfo)
            widgets = widgets.concat(reportWidgets)
        }
        widgets = _.filter(widgets, widget => !_.isEmpty(widget))
        return new DiagnosticReportView(widgets, header)
    }

    private async getChildReportWidgets(childCheckUpReportInfos: DiagnosticReportInfo[]): Promise<PageWidget[]> {
        const reportDetailWidgetsPromises = _.map(childCheckUpReportInfos, async (reportInfo) => {
            return this.getDiagnosticsTestReportDetailWidget(reportInfo)
        })
        return await Promise.all(reportDetailWidgetsPromises)
    }

    private getDiagnosticReportSummaryWidget(reportData: DiagnosticReport): DiagnosticsTestReportSummaryWidget {
        const reportItems: DiagnosticTestReportItem[] = []
        const reportInfo = reportData.diagnosticCheckUpReportInfo
        const testDetails: TestReportDetail[] = CareUtil.getTestReportDetailsView(reportInfo, true)
        const noteText = "Test values are to be interpreted by a Physician or prescribing Doctor only"
        // reportItems.push({
        //     testInfos: testDetails
        // })
        return new DiagnosticsTestReportSummaryWidget(reportItems, false, undefined, noteText)
    }
    private async getDiagnosticChildReportSummaryWidget(reportInfo: DiagnosticReportInfo[], testOrderId: number): Promise<DiagnosticsTestReportSummaryWidget> {
        const childReportInfos = reportInfo.filter(reportInfo => (!_.isNil(reportInfo.testId)))
        const reportItemsPromises = _.map(childReportInfos, async (childReportInfo) => {
            const testInfo = await this.catalogueService.getProduct(childReportInfo.testId)
            if (_.isEmpty(testInfo)) {
                return
            }
            const action: Action = {
                url: `curefit://diagnostictestreport?testOrderId=${testOrderId}&testId=${childReportInfo.testId}`,
                actionType: "NAVIGATION"
            }
            return {
                header: {
                    title: testInfo.title,
                    subtitle: `${childReportInfo.testCount} tests`,
                    image: testInfo.imageUrl,
                    action: action
                },
                // testInfos: testDetails
            }
        })
        const reportItems: DiagnosticTestReportItem[] = await Promise.all(reportItemsPromises)
        return new DiagnosticsTestReportSummaryWidget(reportItems)
    }

    private async getDiagnosticTestDetailSummaryWidget(reportData: DiagnosticReport): Promise<DiagnosticsTestDetailedSummaryWidget> {
        const reportInfo = reportData.diagnosticCheckUpReportInfo
        const testInfo: DiagnosticProduct = <DiagnosticProduct>await this.catalogueService.getProduct(reportInfo.testId)
        const testDetails: TestReportDetail[] = CareUtil.getTestReportDetailsView(reportInfo, true)
        const reportItem: DiagnosticTestReportItem = {
            header: {
                title: testInfo?.title || "Diagnostic Test",
                subtitle: "View now", // `${reportInfo.testCount} tests`,
                image: testInfo?.imageUrl
            },
            // testInfos: testDetails
        }
        const summaryTitle = "Summary"
        const summaryText = testInfo.subTitle
        return new DiagnosticsTestDetailedSummaryWidget(reportItem, false, summaryTitle, summaryText)
    }

    private async getDiagnosticsTestReportDetailWidget(reportInfo: DiagnosticReportInfo): Promise<DiagnosticsTestReportDetailWidget> {
        const reportDetails = reportInfo.reportDetails
        const testInfo: DiagnosticProduct = <DiagnosticProduct>await this.catalogueService.getProduct(reportInfo.testId)
        if (_.isEmpty(testInfo)) {
            return
        }
        const cellType = this.getReportCellType(reportInfo.reportDetails.testResultType)
        const cellData = this.getReportCellData(reportInfo.reportDetails, cellType)
        const testGraphDetails: { testInfos: TestReportDetail[], markerIndex: number } = this.showReportGraph(cellType, reportInfo) && this.getTestReportGraphDetails(reportInfo)
        const reportItem: DiagnosticTestReportItem = {
            header: {
                title: testInfo.title,
                subtitle: testInfo.subTitle,
                // testState: reportInfo.normalCount === 1 ? "Normal" : reportInfo.abnormalCount === 1 ? "Out of range" : null,
                testStateColor: reportInfo.normalCount === 1 ? NORMAL_STATE_COLOR : ABNORMAL_STATE_COLOR
            },
            testResultValue: reportDetails.testNumericalResultValue,
            markerIndex: testGraphDetails.markerIndex,
            testInfos: testGraphDetails.testInfos
        }
        return new DiagnosticsTestReportDetailWidget(reportItem, cellType, cellData)
    }

    private getTestReportGraphDetails(reportInfo: DiagnosticReportInfo): { testInfos: TestReportDetail[], markerIndex: number } {
        const reportDetails = reportInfo.reportDetails
        const normalLineStartPoint = reportDetails.testMinRangeValue
        const normalLineEndPoint = reportDetails.testMaxRangeValue
        const thresholdValueFrom = reportDetails.thresholdValueFrom >= 0 ? reportDetails.thresholdValueFrom : Number.MAX_VALUE
        const thresholdValueTo = reportDetails.thresholdValueTo >= 0 ? reportDetails.thresholdValueTo : Number.MIN_VALUE
        const testNumericalResultValue = reportDetails.testNumericalResultValue
        const lineStartPoint = _.min([normalLineStartPoint, thresholdValueFrom, testNumericalResultValue])
        const lineEndPoint = _.max([normalLineEndPoint, thresholdValueTo, testNumericalResultValue])
        const testInfos: TestReportDetail[] = []
        const lineDetails = this.getTestReportLineValues(lineStartPoint, lineEndPoint, reportInfo)
        const isNormalResult: boolean = reportInfo.normalCount === 1
        if (lineStartPoint < normalLineStartPoint) {
            testInfos.push({
                lineStartValue: lineStartPoint,
                lineEndValue: normalLineStartPoint,
                stateColor: ABNORMAL_STATE_COLOR,
                lineWidthRatio: lineDetails.abNormalLineWidthRatio
            })
        }
        testInfos.push({
            lineStartValue: normalLineStartPoint,
            lineEndValue: normalLineEndPoint,
            stateColor: NORMAL_STATE_COLOR,
            lineWidthRatio: lineDetails.normalLineWidthRatio
        })
        if (lineEndPoint > normalLineEndPoint) {
            testInfos.push({
                lineStartValue: normalLineEndPoint,
                lineEndValue: lineEndPoint,
                stateColor: ABNORMAL_STATE_COLOR,
                lineWidthRatio: lineDetails.abNormalLineWidthRatio
            })
        }
        return { testInfos: testInfos, markerIndex: lineDetails.markerIndex }
    }

    private getReportCellType(testResultType: TEST_RESULT_TYPE): DiagnosticTestDetailCellType {
        // for now, using same cell type as test result type
        return testResultType
    }

    private showReportGraph(cellType: DiagnosticTestDetailCellType, reportInfo: DiagnosticReportInfo): boolean {
        return cellType === "NUMERICAL" && this.isNormalRangePresent(reportInfo.reportDetails)
    }

    private isNormalRangePresent(reportDetails: DiagnosticTestReportDetails): boolean {
        return !_.isNil(reportDetails.testMinRangeValue) && !_.isNil(reportDetails.testMaxRangeValue)
    }

    private getReportCellData(reportDetails: DiagnosticTestReportDetails, cellType: DiagnosticTestDetailCellType): DiagnosticTestReportCell {
        switch (cellType) {
            case "NUMERICAL":
                const testMeasuringUnitText = !_.isEmpty(reportDetails.testMeasuringUnit) ? reportDetails.testMeasuringUnit : ""
                const normalValueText = this.isNormalRangePresent(reportDetails) && reportDetails.testMinRangeValue + "-" + reportDetails.testMaxRangeValue + testMeasuringUnitText
                const measuredValueText = reportDetails.testNumericalResultValue + testMeasuringUnitText
                return {
                    measuredValueTitle: "Measured Value",
                    normalValueTitle: this.isNormalRangePresent(reportDetails) && "Normal Value",
                    measuredValue: measuredValueText,
                    normalValue: normalValueText
                }
            case "WORDS":
                return {
                    measuredValueTitle: "Measured Value",
                    normalValueTitle: "Normal Value",
                    measuredValue: reportDetails.testResultValue + "",
                    normalValue: reportDetails.testNormalValueWord
                }
        }
    }

    private getTestReportLineValues(lineStartPoint: number, lineEndPoint: number, reportInfo: DiagnosticReportInfo): { normalLineWidthRatio: number, abNormalLineWidthRatio: number, markerIndex: number } {
        const reportDetails = reportInfo.reportDetails
        const normalLineStartPoint = reportDetails.testMinRangeValue
        const normalLineEndPoint = reportDetails.testMaxRangeValue
        const testNumericalResultValue = reportDetails.testNumericalResultValue
        let normalLineWidthRatio, markerIndex
        const abNormalLineWidthRatio = 0.2
        if (lineStartPoint < normalLineStartPoint && lineEndPoint > normalLineEndPoint) {
            // means 3 lines will be there
            normalLineWidthRatio = 0.6
            if (reportInfo.normalCount === 1) {
                markerIndex = 1
            }
            else if (testNumericalResultValue <= normalLineStartPoint) {
                markerIndex = 0
            }
            else {
                markerIndex = 2
            }
        }
        else if (lineStartPoint < normalLineStartPoint || lineEndPoint > normalLineEndPoint) {
            // 2 lines will be there
            normalLineWidthRatio = 0.8
            if (lineStartPoint < normalLineStartPoint) {
                markerIndex = reportInfo.normalCount === 1 ? 1 : 0
            }
            else {
                markerIndex = reportInfo.normalCount === 1 ? 0 : 1
            }

        }
        else {
            // single line
            normalLineWidthRatio = 1.0
            markerIndex = 0
        }
        return {
            normalLineWidthRatio, abNormalLineWidthRatio, markerIndex
        }
    }

    private getDiagnosticEmailedTestReportDetailedWidget(diagnosticReport: DiagnosticEmailedTestReportResponse): DiagnosticsEmailTestReportDetailedSummaryWidget {
        const emailArray = diagnosticReport.emailId.split("@")
        let maskedEmail: string
        if (emailArray && emailArray[0] && emailArray[0].length > 3) {
            emailArray[0] = emailArray[0].slice(0, -3).concat("xxx")
            maskedEmail = emailArray.join("@")
        }
        const items: DiagnosticEmailTestReportItem[] = diagnosticReport.productDetails.map((item: DiagnosticEmailedTestReportItem) => {
            return {
                title: item.productName,
                description: item.productDescription,
                highlightedText: `This report is emailed to ${diagnosticReport.emailId}`,
                action: undefined
            }
        })
        return new DiagnosticsEmailTestReportDetailedSummaryWidget(
            "Emailed Reports",
            CareUtil.getDiagnosticsEmailTestReportAction(diagnosticReport.orderId, "RESEND"),
            items,
            true
        )
    }
}
export default DiagnosticReportViewBuilder
