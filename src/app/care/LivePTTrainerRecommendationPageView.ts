import {
    DescriptionWidget,
    DoctorDetailWidget,
    ProductDetailPage,
    SearchListingWidget
} from "../common/views/WidgetView"
import { UserContext } from "@curefit/userinfo-common"
import { Doctor, DOCTOR_TYPE, SUB_CATEGORY_CODE } from "@curefit/care-common"
import { HeaderWidget, InfoWidgetV2 } from "@curefit/apps-common"
import CareUtil from "../util/CareUtil"
import AppUtil from "../util/AppUtil"
import CultUtil from "../util/CultUtil"
import * as _ from "lodash"

class LivePTTrainerRecommendationPageView extends ProductDetailPage {

    constructor(userContext: UserContext,
        doctors: Doctor[],
        subCategoryCode: SUB_CATEGORY_CODE,
        patientId: number,
        doctorTypes?: DOCTOR_TYPE[],
        parentBookingId?: number,
        productId?: string,
        crossGenderDetails?: any
    ) {
        super()
        this.title = "Change Trainer"
        const crossGenderMatchingSupported = AppUtil.isLivePTCrossGenderMatchingSupported(userContext, crossGenderDetails.patient.gender)
        const hasTrainerRecommendation = !_.isEmpty(doctors)
        if (crossGenderMatchingSupported) {
            this.widgets.push(this.getCrossGenderAssignmentWidget(crossGenderDetails, productId, hasTrainerRecommendation))
        }
        if (!hasTrainerRecommendation && crossGenderMatchingSupported) {
            this.widgets.push(this.getNoRecommendationNoteWidget())
        }
        const autoAssignWidget = this.getTrainerAutoAssignWidget(patientId, parentBookingId, productId)
        if (hasTrainerRecommendation) {
            autoAssignWidget.dividerType = "OR"
        }
        this.widgets.push(autoAssignWidget)
        if (hasTrainerRecommendation) {
            const list: any[] = doctors.map(doctor => {
                return this.getTrainerDetailWidget(doctor, patientId, parentBookingId, productId)
            })
            this.widgets.push(this.getTrainerListingWidget(userContext, list, "DOCTOR"))
        }
    }

    private getTrainerAutoAssignWidget(patientId: number, parentBookingId: number, productId: string): InfoWidgetV2 {
        return {
            widgetType: "INFO_WIDGET_V2",
            title: "Auto-assign a new trainer",
            subtitle: "A personal trainer will be assigned to you based on your goal and workout preferences",
            bgColor: "#f2f4f8",
            action: {
                actionType: "REFRESH_CARE_SLOT_PAGE",
                meta: {
                    patientId,
                    parentBookingId,
                    productId,
                    excludePreferredDoctor: true
                }
            },
            analyticsData: {
                widgetName: "Live PT Trainer Auto Assign Widget"
            }
        }
    }

    private getTrainerDetailWidget(doctor: Doctor, patientId: number, parentBookingId?: number, productId?: string): DoctorDetailWidget {
        return {
            widgetType: "DOCTOR_DETAIL_WIDGET",
            displayImage: doctor.displayImage,
            name: doctor.name,
            qualification: doctor.qualification,
            id: doctor.id,
            detailedQualification: doctor.detailedQualification,
            richDescriptions: CareUtil.getLivePTTrainerRichDescriptions(doctor.recommendationAffinity),
            rowStyle: {
                marginTop: 20
            },
            action: {
                actionType: "REFRESH_CARE_SLOT_PAGE",
                meta: {
                    patientId,
                    parentBookingId,
                    productId,
                    doctorId: doctor.id
                }
            }
        }
    }

    getTrainerListingWidget(userContext: UserContext, list: any[], listType: string): SearchListingWidget {
        return {
            widgetType: "SEARCH_LISTING_WIDGET",
            title: "Pick a Trainer",
            list: list,
            searchResultText: undefined,
            listType: listType,
            showMoreShadow: undefined,
            showFilter: false,
            showResultForEmptySearch: true,
            orientation: userContext.sessionInfo.userAgent === "DESKTOP" ? "RIGHT" : undefined,
            key: "doctorId",
            subCategoryCode: "LIVE_PERSONAL_TRAINING"
        }
    }

    private getCrossGenderAssignmentWidget(crossGenderDetails: any, productId: string, hasTrainerRecommendation: boolean) {
        let containerStyle = {
            paddingBottom: 40
        }
        if (!hasTrainerRecommendation) {
            containerStyle = {
                paddingBottom: 0
            }
        }
        return CultUtil.getLivePTCrossGenderToggleBarWidget(crossGenderDetails, productId, true, containerStyle)
    }

    private getNoRecommendationNoteWidget() {
        const headerWidget = new HeaderWidget({ title: "Sorry, we don't have any recommended trainers now. Let us find a trainer for you." })
        headerWidget.titleStyle = { color: "#888E9E", fontSize: 14, fontFamily: "BrandonText-Regular", marginLeft: 0, lineHeight: 18 }
        headerWidget.style = { marginHorizontal: 20, marginBottom: 30 }
        return headerWidget
    }
}

export default LivePTTrainerRecommendationPageView