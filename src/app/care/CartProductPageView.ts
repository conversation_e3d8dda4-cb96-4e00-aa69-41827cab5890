import * as _ from "lodash"
import {
    AddOnProduct,
    BmiRange,
    DiagnosticProduct,
    DiagnosticProductResponse,
    DiagnosticTestProduct,
    ManagedPlanPackInfo,
    ManagedPlanSellableProduct,
    MPChildProduct,
    Patient
} from "@curefit/care-common"
import { ProductPrice } from "@curefit/product-common"
import { OfferV2, PackOffersResponse } from "@curefit/offer-common"
import {
    Action,
    InfoCard,
} from "../common/views/WidgetView"
import CareUtil from "../util/CareUtil"
import { PageWidget } from "../page/Page"
import {
    DiagnosticCardAddOnListWidget,
    DiagnosticsTestListWidget
} from "../page/PageWidgets"
import { ActionUtil, OfferUtil } from "@curefit/base-utils"
import { capitalizeFirstLetter, TimeUtil } from "@curefit/util-common"
import { EligibilityResponse } from "@curefit/gmf-client"
import { Session, UserContext } from "@curefit/userinfo-common"
import { Orientation } from "@curefit/vm-common"
import { User } from "@curefit/user-common"
import AppUtil from "../util/AppUtil"
import { DiagnosticsCart } from "@curefit/fuse-node-client/dist/src/FuseClientTypes"
import { PathToObjectIdentifiersList } from "aws-sdk/clients/clouddirectory"
import {
    IBaseProductSummeryWidget, ICareDiagnosticSellerWidget,
    IDiagnosticsCartAddonListWidget,
    IDiagnosticsCartAddonListWidgetAction, IFullFilledDetailsV2, IProductDetailPage
} from "@curefit/apps-common"

class CartProductPageView extends IProductDetailPage {
    public pageContext: any

    constructor(
        user: User,
        userContext: UserContext,
        session: Session,
        productCodes: string[],
        careCart: DiagnosticsCart,
        product: DiagnosticProduct,
        packageProduct: DiagnosticProductResponse,
        addOnProductInfos: DiagnosticProduct[],
        baseCartProduct?: DiagnosticProduct,
        patientsList?: Patient[],
        offers?: PackOffersResponse,
        cartOffers?: OfferV2[],
        sellerDetails?: any
    ) {
        super()
        const isNotLoggedIn = session.isNotLoggedIn
        const offerDetails = OfferUtil.getPackOfferAndPrice(product, offers)
        let pageAction: Action

        this.widgets = []
        this.action = undefined
        const baseProductSummaryWidget = this.baseProductSummaryWidget(isNotLoggedIn, packageProduct, offerDetails.price, careCart, patientsList)
        this.widgets.push(baseProductSummaryWidget)
        const offerDetailsWidget = this.getOfferDetailsWidget(userContext, productCodes, offers, cartOffers)
        offerDetailsWidget && this.widgets.push(offerDetailsWidget)
        const includedTests = packageProduct?.diagnosticProductResponse?.items
        if (!_.isEmpty(includedTests)) {
            this.widgets.push(this.getDiagnosticsTestListWidget(userContext, product.productId, "Tests Included", 1, includedTests))
        }
        const addOnProducts = packageProduct?.diagnosticProductResponse?.addOnProducts?.map(product => {return product.diagnosticProduct})
        if (!_.isEmpty(addOnProducts)) {
            this.widgets.push(this.getDiagnosticsCartAddonListWidget(userContext, isNotLoggedIn, careCart, product.productId, "Available Add-Ons", 1, addOnProducts, addOnProductInfos, offers, patientsList))
        }
        sellerDetails && this.widgets.push(this.getCareDiagnosticsSellerWidget(userContext, sellerDetails))

        pageAction = this.getPageAction(userContext, careCart, patientsList, baseCartProduct)
        this.action = pageAction ? pageAction : undefined

        // Added support to show bottom floating cta, if add button is enabled in the product page and cart is empty
        if (userContext.sessionInfo.userAgent === "APP") {
            if (!_.isEmpty(baseProductSummaryWidget.action) &&  _.isEmpty(careCart?.diagnosticCartItems)) {
                this.actions = [baseProductSummaryWidget.action]
            } else {
                this.actions = []
            }
        }
    }

    private getPageAction(userContext: UserContext, careCart: DiagnosticsCart, patientsList: Patient[], product?: DiagnosticProduct): Action | undefined {
        const isMWeb = AppUtil.isMWeb(userContext)
        const isCartEmpty = _.isEmpty(careCart?.diagnosticCartItems)
        let totalTests: number = 0
        let patientData: Patient

        if (!isMWeb || isCartEmpty || !product) {
            return
        }

        totalTests = careCart?.diagnosticCartItems?.length
        patientData = patientsList.find((patientItem) => patientItem.id === Number(careCart?.cartMetaData?.patientId))

        return {
            actionType: "NAVIGATION",
            title: product.title.concat(totalTests > 1 ? ` + ${totalTests - 1} Test${totalTests > 2 ? "s" : ""}` : ""),
            subtitle: patientData ? `For ${patientData?.name} | ${patientData.gender} | ${patientData.age} yrs` : undefined,
            url: "/care/diagnostic-tests/cart/review"
        }
    }

    private getOfferDetailsWidget(userContext: UserContext, productCodes: string[], offers?: PackOffersResponse, cartOffers?: OfferV2[]): any | null {
        const isDesktop = AppUtil.isDesktop(userContext)
        const isMWeb = AppUtil.isMWeb(userContext)
        const containerStyle = isMWeb ? { marginRight: 15, marginLeft: 15, marginTop: 0, marginBottom: 0 } : {}
        const orientation = isDesktop ? "RIGHT" : undefined
        const offerIds: string[] = []
        let finalOffers: OfferV2[] = []

        if (!_.isEmpty(offers)) {
            productCodes.map(productCode => {
                const productOffers = offers[productCode]?.offers
                if (!_.isEmpty(productOffers)) {
                    productOffers.map(offer => {
                        if (!offerIds.includes(offer.offerId)) {
                            offerIds.push(offer.offerId)
                            finalOffers.push(offer)
                        }
                    })
                }
            })
        }
        finalOffers = finalOffers.concat(cartOffers)
        return CareUtil.getCollapsibleOfferWidget(finalOffers, userContext, false, orientation, containerStyle)
    }

    private getCareDiagnosticsSellerWidget(userContext: UserContext, sellerDetails?: any): ICareDiagnosticSellerWidget {
        const fulfilledDetails = CareUtil.getFullfilledDetailsV2(userContext, sellerDetails)
        const isMWeb = AppUtil.isMWeb(userContext)
        const containerStyle = isMWeb ? { marginRight: 15, marginLeft: 15 } : {}
        return {
            widgetType: "CARE_DIAGNOSTICS_SELLER_WIDGET" as any,
            fulfilledDetails: fulfilledDetails as IFullFilledDetailsV2,
            containerStyle,
            ...(AppUtil.isDesktop(userContext) ? { orientation: "RIGHT" } : {})
        }
    }

    private baseProductSummaryWidget(isNotLoggedIn: boolean, packageProduct: DiagnosticProductResponse, price: ProductPrice, careCart: DiagnosticsCart, patientsList: Patient[]): IBaseProductSummeryWidget {
        const reportingTat = packageProduct?.diagnosticProductResponse?.reportingTat
        const reportReadyEta = reportingTat ? "Report Ready in " + reportingTat + " Hrs" : undefined
        const countTests = CareUtil.countParameters(packageProduct?.diagnosticProductResponse)
        const countTestsText = countTests > 1 ? countTests.toString() + " Tests" : countTests.toString() + " Test"
        const imageData = packageProduct.diagnosticProductResponse?.productAttributes?.find((data: any) => data.attributeName === "HERO_IMAGE")
        const imageUrl = imageData?.attributeValue || "/image/singles/care/hcu/HCU_Diagnostics_1.png"
        const productSummaryWidget = {
            widgetType: "BASE_PRODUCT_SUMMARY_WIDGET" as any,
            title: packageProduct.productName,
            productId: packageProduct.productCode,
            price: price,
            image: imageUrl,
            hasDividerBelow: false,
            action: CareUtil.getItemAction(isNotLoggedIn, careCart, packageProduct.productCode, patientsList),
            reportReadyEta,
            countTestsText,
            description: packageProduct.productDescription
        }

        return productSummaryWidget
    }

    private getDiagnosticsTestListWidget(userContext: UserContext, productId: string, title: string, gridSize: number, bundleProducts: DiagnosticTestProduct[]): PageWidget {
        const infoCards: InfoCard[] = []
        const isDesktop = AppUtil.isDesktop(userContext)
        const isWeb = AppUtil.isWeb(userContext)
        let widget: PageWidget
        let action: IDiagnosticsCartAddonListWidgetAction
        let totalParameters: number = 0

        bundleProducts.map((product, index) => {
            const countTests = CareUtil.countParameters(product)
            product.countParameters = countTests
            product.countParametersText = `${countTests} ${countTests > 1 ? "Tests" : "Test"}`
            product.reportReadyText = `Reports in ${product.reportingTat} Hrs`
            totalParameters = totalParameters + product.countParameters
            infoCards.push({
                title: product.name,
                image: product.imageUrl,
                moreIndex: index,
                countParameters: product.countParameters
            })
        })

        action = {
            actionType: CareUtil.getNewCarouselListActionType(userContext, "SHOW_DIAGNOSTICS_TEST_DETAIL_LIST"),
            meta: {
                type: "DIAGNOSTIC_TEST_DETAIL_LIST",
                productId: productId,
                bundleProducts: bundleProducts
            }
        }

        widget = isWeb ?
            new DiagnosticCardAddOnListWidget(infoCards, title, action, isDesktop ? "RIGHT" : undefined, false, undefined, { backgroundColor: "white" })
            : new DiagnosticsTestListWidget(gridSize, infoCards, { title }, action)

        return widget
    }

    private getDiagnosticsCartAddonListWidget(userContext: UserContext, notLoggedIn: boolean, careCart: DiagnosticsCart, productId: string, title: string, gridSize: number, bundleProducts: DiagnosticTestProduct[], addOnProductInfos?: DiagnosticProduct[], offers?: PackOffersResponse, patientsList?: Patient[]): IDiagnosticsCartAddonListWidget {
        const infoCards: InfoCard[] = []
        const isDesktop = AppUtil.isDesktop(userContext)
        let widget: IDiagnosticsCartAddonListWidget
        let action: IDiagnosticsCartAddonListWidgetAction

        bundleProducts.map((product, index) => {
            const countTests = CareUtil.countParameters(product)
            const cartAction = CareUtil.getItemAction(notLoggedIn, careCart, product.code, patientsList)
            const productInfo = addOnProductInfos?.find(addOnProduct => product.code === addOnProduct.productId)
            const offerDetails = OfferUtil.getPackOfferAndPrice(productInfo, offers)
            product.addOnPrice = offerDetails.price
            product.isAddon = "true"
            product.countParameters = countTests
            product.countParametersText = `${countTests} ${countTests > 1 ? "Tests" : "Test"}`
            product.reportReadyText = `Reports in ${product.reportingTat} Hrs`
            product.cartAction = cartAction

            infoCards.push({
                title: product.name,
                image: product.imageUrl,
                productCode: product.code,
                moreIndex: index,
                countParameters: product.countParameters,
                cartAction: cartAction,
                price: offerDetails.price
            })
        })

        action = {
            actionType: CareUtil.getNewCarouselListActionType(userContext, "SHOW_DIAGNOSTICS_TEST_DETAIL_LIST"),
            meta: {
                    type: "DIAGNOSTIC_TEST_DETAIL_LIST",
                    productId: productId,
                    bundleProducts: bundleProducts
            }
        }

        widget = new DiagnosticCardAddOnListWidget(infoCards, "Recommended Addons", action, isDesktop ? "RIGHT" : undefined, true, "Get better prices for these tests with this profile")

        return widget
    }
}

export default CartProductPageView
