import * as _ from "lodash"
import * as momentTz from "moment-timezone"
import { CareDateSelectorPage, PageWidget } from "../page/Page"
import { CATEGORY_CODE, DiagnosticAvailableSlotsResponse, DiagnosticSlot, Seller } from "@curefit/albus-client"
import { CalloutPageWidget, DatesAvailableWidget, DateWiseSlots, TimeSlot, TimeSlotCategory } from "../page/PageWidgets"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { UserContext } from "@curefit/userinfo-common"

class DatePickerView {

    public datePicker: CareDateSelectorPage

    constructor(userContext: UserContext, bookingId: string, productId: string, category: string, availableSlots: DiagnosticAvailableSlotsResponse, patientId: number, categoryCode: CATEGORY_CODE, isReschedule: string, productCodes?: string, nextAction?: string, offerIds?: string) {
        if (category === "IN_CENTRE_SLOT") {
            this.datePicker = this.inCentreDatePickerResponse(userContext, bookingId, productId, category, availableSlots, patientId, categoryCode, isReschedule, productCodes, nextAction, offerIds)
        } else if (category === "AT_HOME_SLOT") {
            this.datePicker = this.atHomeDatePickerResponse(userContext, bookingId, productId, category, availableSlots, patientId, categoryCode, isReschedule, productCodes, nextAction, offerIds)
        }
    }

    private inCentreDatePickerResponse(userContext: UserContext, bookingId: string, productId: string, category: string, availableSlots: DiagnosticAvailableSlotsResponse, patientId: number, categoryCode: CATEGORY_CODE, isReschedule: string, productCodes?: string, nextAction?: string, offerIds?: string): CareDateSelectorPage {
        const lists: { [key: string]: PageWidget[] } = {}
        const incentreWidgets: PageWidget[] = []
        const checkoutUrl = `curefit://diagnosticscheckout?type=DIAGNOSTICS&productId=${productId}&patientId=${patientId}&parentBookingId=${bookingId}&offerIds=${offerIds}`
        const rescheduleUrl = `curefit://rescheduleDiagnostics?type=DIAGNOSTICS&productId=${productId}&patientId=${patientId}&parentBookingId=${bookingId}&category=IN_CENTRE_SLOT&offerIds=${offerIds}`
        let actionUrl: string = isReschedule === "true" ? rescheduleUrl : checkoutUrl
        if (productCodes) {
            actionUrl = actionUrl + `&productCodes=${productCodes}`
        }
        incentreWidgets.push(this.availableDatesWidget(userContext, category, availableSlots, actionUrl, false))
        // TODO correct it once centre is discussed
        // incentreWidgets.push(new CalloutPageWidget("LOCATION", center.address, center.name, "VIEW MAP", `curefit://externalDeepLink?placeUrl=${center.placeUrl}`))
        incentreWidgets.push(new CalloutPageWidget("LOCATION", "Please reach the center on time"))
        lists["INCENTRE"] = incentreWidgets
        return {
            key: category,
            userLocation: "bangalore", // TODO check this isWithinBangalore ? "bangalore" : "others",
            sections: [{
                id: "INCENTRE",
                name: "Select slot for center" // todo change to center name
            }],
            lists: lists,
        }
    }

    private atHomeDatePickerResponse(userContext: UserContext, bookingId: string, productId: string, category: string, availableSlots: DiagnosticAvailableSlotsResponse, patientId: number, categoryCode: CATEGORY_CODE, isReschedule: string, productCodes?: string, nextAction?: string, offerIds?: string): CareDateSelectorPage {
        const lists: { [key: string]: PageWidget[] } = {}
        const atHomeWidgets: PageWidget[] = []
        const atCentreUrl = `curefit://selectCareDateV1?productId=${productId}&parentBookingId=${bookingId}&type=DIAGNOSTICS&category=IN_CENTRE_SLOT&nextAction=checkout&offerIds=${offerIds}&hasAtHome=true`
        const rescheduleUrl = `curefit://rescheduleDiagnostics?productId=${productId}&parentBookingId=${bookingId}&type=DIAGNOSTICS&category=AT_HOME_SLOT&offerIds=${offerIds}`
        const checkoutUrl = `curefit://diagnosticscheckout?type=DIAGNOSTICS&productId=${productId}&parentBookingId=${bookingId}&patientId=${patientId}&offerIds=${offerIds}&category=AT_HOME_SLOT`
        let actionUrl: string
        if (nextAction === "checkout") {
            actionUrl = checkoutUrl
        } else if (nextAction === "incentreSlot") {
            actionUrl = atCentreUrl
        } else {
            actionUrl = isReschedule === "true" ? rescheduleUrl : atCentreUrl
        }
        if (productCodes) {
            actionUrl = actionUrl + `&productCodes=${productCodes}`
        }
        atHomeWidgets.push(this.availableDatesWidget(userContext, category, availableSlots, actionUrl, true))
        atHomeWidgets.push(new CalloutPageWidget("LOCATION", "Phlebotomist will arrive in the time slot you select. It usually takes 10-15 mins to collect the samples"))
        lists["ATHOME"] = atHomeWidgets
        return {
            key: category,
            userLocation: "bangalore", // TODO check this isWithinBangalore ? "bangalore" : "others",
            sections: [{
                id: "ATHOME",
                name: "Select slot for home" // todo change to home address
            }],
            lists: lists,
        }
    }

    private availableDatesWidget(userContext: UserContext, category: string, availableSlotsResponse: DiagnosticAvailableSlotsResponse, actionUrl: string, showSelected: boolean): DatesAvailableWidget {
        const tz = userContext.userProfile.timezone
        const dates: string[] = TimeUtil.getDays(tz, 15)
        const datesAvailable: DateWiseSlots[] = []
        for (let i = 0; i < dates.length && datesAvailable.length < 15; i++) {
            let dateAppointments: DiagnosticSlot[]
            if (category === "IN_CENTRE_SLOT") {
                dateAppointments = availableSlotsResponse.inCentreSlots.filter((x) => TimeUtil.formatEpochInTimeZoneDateFns(tz, x.startTime, "yyyy-MM-dd") === dates[i])
            } else if (category === "AT_HOME_SLOT") {
                dateAppointments = availableSlotsResponse.phleboSlots.filter((x) => TimeUtil.formatEpochInTimeZoneDateFns(tz, x.startTime, "yyyy-MM-dd") === dates[i])
            }
            if (!_.isEmpty(dateAppointments)) {
                let timeslots: TimeSlotCategory[] = []
                timeslots = this.getHourSplitTimeSlots(dates[i], dateAppointments, availableSlotsResponse.sellers[0], showSelected, userContext, category === "AT_HOME_SLOT")
                if (!_.isEmpty(timeslots)) {
                    const dateWiseSlot: DateWiseSlots = {
                        date: dates[i],
                        dateType: TimeUtil.getMomentForDateString(dates[i], tz).isoWeekday() >= 6 ? "WEEKEND" : "WEEKDAY",
                        timeZones: timeslots
                    }
                    datesAvailable.push(dateWiseSlot)
                }
            }

        }
        return new DatesAvailableWidget(datesAvailable, actionUrl, { actionType: "NAVIGATION", url: actionUrl })
    }

    private getHourSplitTimeSlots(date: string, dateAppointments: DiagnosticSlot[], seller: Seller, showSelected: boolean, userContext: UserContext, isHomeSlot?: boolean): TimeSlotCategory[] {
        const timeslots: TimeSlotCategory[] = []
        const slot1 = this.getTimeslotBetweenRanges(date, dateAppointments, "06:00", "08:59", userContext.userProfile.timezone)
        if (!_.isEmpty(slot1)) {
            timeslots.push({
                timeSlots: this.parseToViewFormat(slot1, seller, showSelected, userContext, isHomeSlot),
                title: "06-09AM"
            })
        }

        const slot2 = this.getTimeslotBetweenRanges(date, dateAppointments, "09:00", "11:59", userContext.userProfile.timezone)
        if (!_.isEmpty(slot2)) {
            timeslots.push({
                timeSlots: this.parseToViewFormat(slot2, seller, showSelected, userContext, isHomeSlot),
                title: "09-12AM"
            })
        }

        const slot3 = this.getTimeslotBetweenRanges(date, dateAppointments, "12:00", "14:59", userContext.userProfile.timezone)
        if (!_.isEmpty(slot3)) {
            timeslots.push({
                timeSlots: this.parseToViewFormat(slot3, seller, showSelected, userContext, isHomeSlot),
                title: "12-03PM"
            })
        }

        const slot4 = this.getTimeslotBetweenRanges(date, dateAppointments, "15:00", "17:59", userContext.userProfile.timezone)
        if (!_.isEmpty(slot4)) {
            timeslots.push({
                timeSlots: this.parseToViewFormat(slot4, seller, showSelected, userContext, isHomeSlot),
                title: "03-06PM"
            })
        }
        const slot5 = this.getTimeslotBetweenRanges(date, dateAppointments, "18:00", "21:00", userContext.userProfile.timezone)
        if (!_.isEmpty(slot5)) {
            timeslots.push({
                timeSlots: this.parseToViewFormat(slot5, seller, showSelected, userContext, isHomeSlot),
                title: "06-09PM"
            })
        }
        return timeslots
    }

    private getTimeslotBetweenRanges(date: string, dateAppointments: DiagnosticSlot[], startTime: string, endTime: string, timezone: Timezone): DiagnosticSlot[] {
        const startRange = TimeUtil.getMomentForDateString(date + " " + startTime, timezone, "YYYY-MM-DD HH:mm a")
        const endRange = TimeUtil.getMomentForDateString(date + " " + endTime, timezone, "YYYY-MM-DD HH:mm a")
        return dateAppointments.filter((x) => momentTz.tz(x.startTime, timezone).isBetween(startRange, endRange, "second", "[]"))
    }

    private parseToViewFormat(slots: DiagnosticSlot[], seller: Seller, showSelected: boolean, userContext: UserContext, isHomeSlot?: boolean): TimeSlot[] {
        const timeSlots: TimeSlot[] = []
        slots.forEach(element => {
            const timeslot: TimeSlot = {
                availableType: element.isAvailable === true ? "AVAILABLE" : "UNAVAILABLE",
                text: this.getTimeText(element, userContext, isHomeSlot), // moment(element.startTime).format("hh:mm"),
                startTime: element.startTime,
                endTime: element.endTime,
                meta: {
                    slotId: element.slotId,
                    centreCode: element.centreCode,
                    sellerCode: seller.sellerCode,
                    sellerName: seller.sellerName,
                    sellerType: seller.sellerType
                },
                showSelected: showSelected
            }
            timeSlots.push(timeslot)
        })
        return timeSlots
    }

    private getTimeText(element: DiagnosticSlot, userContext: UserContext, isHomeSlot?: boolean) {
        let text
        const totalTimeOfSlot = userContext.userProfile.cityId === "Bangalore" ? 1800000 : 3600000
        if (element && element.startTime) {
            text = TimeUtil.formatEpochInTimeZoneDateFns(userContext.userProfile.timezone, element.startTime, "hh:mm")
        }
        // Temp hack Adding 30 mins for home slots alone
        if (text && isHomeSlot && element.endTime) {
            text += " - " + TimeUtil.formatEpochInTimeZoneDateFns(userContext.userProfile.timezone, element.endTime, "hh:mm")
        }
        return text
    }


}

export default DatePickerView
