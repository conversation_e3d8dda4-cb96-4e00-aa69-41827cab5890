import * as _ from "lodash"
import {
    <PERSON>miRange,
    DiagnosticProduct,
    DiagnosticTestProduct,
    ManagedPlanPackInfo,
    ManagedPlanSellableProduct,
    MPChildProduct,
    Patient
} from "@curefit/care-common"
import { FoodProduct as Product } from "@curefit/eat-common"
import { ProductPrice } from "@curefit/product-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { PreferredCenterResponse } from "@curefit/albus-client"
import { OfferV2, PackOffersResponse } from "@curefit/offer-common"
import {
    Action,
    CenterSelectionWidget,
    DescriptionWidget,
    getOffersWidget,
    GradientCard,
    GradientCardWithBottomActionWidget,
    GradientCarouselCard,
    Header,
    InfoCard,
    ProductDetailPage,
    ProductListWidget,
    SelectCenterAction,
    TextWidget,
    WeightLoseJourneyValue,
    WidgetView
} from "../common/views/WidgetView"
import CareUtil, { CARE_NEW_CENTER_SELECTION_SUPPORTED, CARE_HCU_FORWARD_FLOW_SUPPORTED } from "../util/CareUtil"
import { PageWidget, PageWidgetTitle } from "../page/Page"
import { DiagnosticsTestListWidget } from "../page/PageWidgets"
import { ActionUtil, OfferUtil } from "@curefit/base-utils"
import { capitalizeFirstLetter, TimeUtil } from "@curefit/util-common"
import { EligibilityResponse } from "@curefit/gmf-client"
import { UserContext } from "@curefit/userinfo-common"
import { Orientation } from "@curefit/vm-common"
import { User } from "@curefit/user-common"
import AppUtil from "../util/AppUtil"

const gradientColorsMap: { [key: string]: string[] } = { "OBESE": ["#f9707d", "#e56060"], "OVERWEIGHT": ["#f97842", "#e56f4d"], "NORMAL": ["#45b785", "#45b785"], "UNDERWEIGHT": ["#48c9b3", "#01b4bf"], "MODERATE": ["#f2b953", "#f2b953"] }
const isNewInstructionIconSupport = 7.44

class BundleProductPageView extends ProductDetailPage {
    public pageContext: any

    constructor(user: User, userContext: UserContext, isNotLoggedIn: boolean, product: DiagnosticProduct, isMultiCenterEnabled: boolean, packageProduct?: ManagedPlanSellableProduct, preferredCenter?: PreferredCenterResponse, patientsList?: Patient[], offers?: PackOffersResponse, cartOffers?: OfferV2[], subscriptionStartDate?: number, minSubscriptionProduct?: MPChildProduct, patientId?: number, sellerDetails?: any) {
        super()
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent")
        const loggedIn: boolean = _.get(userContext, "sessionInfo.isUserLoggedIn", false)
        const appVersion = _.get(userContext, "sessionInfo.appVersion")
        const osName = _.get(userContext, "sessionInfo.osName")
        const codepushVersion = _.get(userContext, "sessionInfo.cpVersion")
        const isWeb = AppUtil.isWeb(userContext)
        const isValidateAdressSupported = CareUtil.isValidateAdressSupported(osName, appVersion, codepushVersion, user.isInternalUser, isWeb)
        const showUpdateForPurchase = CareUtil.showUpdateForPurchase(osName, appVersion, codepushVersion, user.isInternalUser, userAgent)
        const isNewCenterSelectionSupported = appVersion && appVersion >= CARE_NEW_CENTER_SELECTION_SUPPORTED
        const isHCUForwardProgressSupported = appVersion && appVersion >= CARE_HCU_FORWARD_FLOW_SUPPORTED
        const calloutText = "This personalized plan is available only for yourself."
        const isAIMG = product.subCategoryCode === "AI_MG"
        const centerSelectionAction: SelectCenterAction = isNewCenterSelectionSupported ? CareUtil.getCenterSelectionAction(packageProduct, patientId) : undefined
        let offerDetails, isMalePreselected
        const productSpecs = packageProduct && packageProduct.productSpec
        if (!_.isEmpty(productSpecs)) {
            patientsList = patientsList.map((patient) => {
                if ((patient.gender !== productSpecs.gender || patient.formattedAge.numOfYears < productSpecs.ageLow || patient.formattedAge.numOfYears > productSpecs.ageHigh ) ) {
                    patient.disabled = true
                }
                return patient
            })
            patientsList.sort((a: Patient , b: Patient) => a.disabled === b.disabled ?  0 : a.disabled ? 1 : -1 )
            isMalePreselected = productSpecs.gender === "Male"
        }
        if (isAIMG) {
            offerDetails = OfferUtil.getPackOfferAndPriceForCare(minSubscriptionProduct.baseSellableProduct, offers)
            if (offerDetails.price.listingPrice > 0) {
                _.set(offerDetails, "price.title", "per month") //  Adding this changes for the price sub text in health checkup widget
            }
        } else {
            offerDetails = OfferUtil.getPackOfferAndPrice(product, offers)
        }
        const offerIds = _.map(offerDetails.offers, offer => { return offer.offerId })
        if (isAIMG) {
            this.actions = isNewCenterSelectionSupported
                ? this.getMPPreBookingActionsV2(userContext, isMultiCenterEnabled, packageProduct, isNotLoggedIn, patientsList, subscriptionStartDate, centerSelectionAction, patientId, preferredCenter)
                : this.getMPPreBookingActions(userContext, packageProduct, isNotLoggedIn, patientsList, subscriptionStartDate)
        } else {
            const isApp = userAgent === "APP"
            this.actions = isHCUForwardProgressSupported || userAgent === "DESKTOP" || userAgent === "MBROWSER"
                ? this.getPreBookingActionsV2(isNotLoggedIn, isMultiCenterEnabled, patientsList, packageProduct, isMalePreselected, isValidateAdressSupported, showUpdateForPurchase)
                : this.getPreBookingActions(userAgent, product, isNotLoggedIn, isMultiCenterEnabled, patientsList, packageProduct, offerIds, centerSelectionAction, patientId, preferredCenter)
        }
        this.widgets.push(this.summaryWidget(userAgent, packageProduct, offerDetails.price, product))

        if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
            const textWidget = new TextWidget("About", packageProduct.productDescription)
            if (userAgent === "DESKTOP") {
                textWidget.orientation = "RIGHT"
            }
            this.widgets.push(textWidget)
        } else {
            const descriptions = [{
                subTitle: packageProduct.productDescription,
            }]
            const descriptionWidget = new DescriptionWidget(descriptions)
            descriptionWidget.iconType = undefined
            descriptionWidget.dividerType = "SMALL"
            this.widgets.push(descriptionWidget)
        }

        if (!isHCUForwardProgressSupported) {
            const widget = CareUtil.buildUserSelectionWidget(isNotLoggedIn,
                isAIMG ? true : false,
                patientsList,
                {
                    actionType: "NAVIGATION",
                    meta: {
                        replacePage: true
                    },
                    url: ActionUtil.carefitbundle(packageProduct.productCode, packageProduct.subCategoryCode)
                },
                _.isEmpty(patientsList) ? "Add Customer" : "Select Customer",
                patientId,
                undefined,
                "SMALL",
                isAIMG ? calloutText : undefined,
                {
                    formUserType: isAIMG ? "MANAGED_PLAN_USER" : "CARE_USER"
                }
            )
            if (isAIMG && !patientId) {
                if (CareUtil.isInstructionModalSupportedInApp(userContext)) {
                    const instructionAction: Action = {
                        actionType: "SHOW_CARE_INSTRUCTION_MODAL",
                        title: widget.action.title || "Sign me up!",
                        meta: {
                            header: {
                                title: "This pack is for"
                            },
                            instructions: this.getInstructionsForAIMGPurchase(userContext),
                            action: { ...widget.action, title: "CONTINUE" },
                            showBookLater: true
                        }
                    }
                    widget.action = instructionAction
                }
            }
            this.widgets.push(widget)
        }

        if (isMultiCenterEnabled && !isHCUForwardProgressSupported ) {
            if (isNewCenterSelectionSupported) {
                const widget = CareUtil.getCenterSelectionWidget(preferredCenter, centerSelectionAction, patientId, false)
                widget.dividerType = "SMALL"
                // widget.hasDividerBelow = isAIMG ? false : true
                this.widgets.push(widget)
            } else {
                this.widgets.push(this.getAvailableCentersWidget(product))
            }
        }
        const otProducts = packageProduct.childProducts.filter(product => product.baseSellableProduct.subCategoryCode === "DIAG_PACK_OT" || product.baseSellableProduct.subCategoryCode === "HCU_PACK_OT" || product.baseSellableProduct.subCategoryCode === "GENETICS_PACK_OT")

        let howItWorksItem, packContentsDetailed, packOffering
        packageProduct.infoSection.children.map(infoSection => {
            switch (infoSection.type) {
                case "PACK_STEPS": howItWorksItem = infoSection; break
                case "PACK_CONTENTS_DETAILED": packContentsDetailed = infoSection; break
                case "PACK_OFFERING" : packOffering = infoSection; break
            }
        })
        // Combining product offers and cart offers
        const allOffers = offerDetails.offers.concat(cartOffers)
        const combinedOffers = _.filter(allOffers, function(offer) { return !!offer})
        if (!_.isEmpty(combinedOffers)) {
            this.widgets.push(CareUtil.getCareOfferWidget("Offers Applied", combinedOffers, userContext))
        }
        if (!_.isEmpty(otProducts)) {
            const items = otProducts[0].baseSellableProduct.packageProduct.basePackageProduct.items
            if (!_.isEmpty(items)) {
                this.widgets.push(this.getDiagnosticsTestListWidget(userContext, product.productId, "Tests Included", items.length < 4 ? 1 : 2, items, true, sellerDetails, false))
            }
            const adonItems = otProducts[0].baseSellableProduct.packageProduct.addOnProducts
            if (!_.isEmpty(adonItems)) {
                this.widgets.push(this.getDiagnosticsTestListWidget(userContext, product.productId, "Available Add-Ons", items.length < 4 ? 1 : 2, adonItems, false))
            }
        }
        if (!_.isEmpty(packContentsDetailed)) {
            const isGradientListCarouselSupported = userContext.sessionInfo.appVersion >= 7.24
            this.widgets.push(isGradientListCarouselSupported ? this.getWhatsInPackWidgetV2(userContext, product.productId, packContentsDetailed) : this.getWhatsInPackWidget(userAgent, packContentsDetailed))
        }
        if (!_.isEmpty(packOffering)) {
            this.widgets.push(this.getWhatsInPackWidgetV1(userAgent, packOffering))
        }
        this.widgets.push(CareUtil.getHowItWorksWidget(howItWorksItem, userAgent))
        this.widgets = this.widgets.filter(Boolean).map(widget => ({...widget, hasDividerBelow: false}))
    }

    private getPickerOptions(isMultiCenterEnabled: boolean, patientsList: Patient[], packageProduct: ManagedPlanSellableProduct, patientId?: number, preferredCenter?: PreferredCenterResponse) {
        const pickerOptions = [
            {
                optionType: "PATIENT_SELECTION",
                actions: this.getPatientSelectionAction(packageProduct,
                    patientsList,
                    false,
                    "Buy Now",
                    undefined,
                    {
                        replacePage: true,
                        formUserType: "CARE_USER"
                    }
                ),
                text: "For whom",
            }
        ]
        if (isMultiCenterEnabled) {
            pickerOptions.push({
                optionType: patientId ? "CENTER_SELECTION" : "CENTER_SELECTION_DISABLED",
                text: _.get(preferredCenter, "centerResponse.name", "Pick a center"),
                actions: patientId ? [CareUtil.getCenterSelectionAction(packageProduct, patientId)] : [],
            })
        }
        return pickerOptions
    }

    private getAvailableCentersWidget(product: DiagnosticProduct): CenterSelectionWidget {
        const action: Action = {
            actionType: "NAVIGATION",
            url: `curefit://selectcarecenter?productId=${product.productId}`
        }
        const centerSelectionWidget: CenterSelectionWidget = {
            title: "View Available Centres",
            canChangeCenter: true,
            widgetType: "CENTER_PICKER_WIDGET",
            dividerType: "SMALL",
            action: action
        }
        return centerSelectionWidget
    }

    private summaryWidget(
        userAgent: UserAgent,
        product: ManagedPlanSellableProduct,
        price: ProductPrice,
        diagnosticproduct: DiagnosticProduct
    ): WidgetView {
        const checkupSummaryWidget: WidgetView & {
            productId: string;
            title: string;
            image: string;
            price: ProductPrice;
            hasDividerBelow: boolean
            breadcrumb?: { text: string, link?: string }[]
            orientation?: Orientation,
            actions: Action[]
            pickerOptions?: any[]
        } = {
            widgetType: "HEALTHCHECKUP_SUMMARY_WIDGET",
            title: product.productName,
            productId: product.productCode,
            price: price,
            image: product.heroImageUrl,
            hasDividerBelow: false,
            orientation: userAgent === "DESKTOP" ? "RIGHT" : undefined,
            breadcrumb: userAgent === "DESKTOP" ? [{ text: "Home", link: "/" }, { text: "Care", link: "/care" }, { text:  diagnosticproduct.subCategoryCode === "DIAG_PACK" ? "Diagnostic Packs" : "Health Checkup" }] : [],
            actions: userAgent === "DESKTOP" || userAgent === "MBROWSER" ? this.actions : []
        }
        return checkupSummaryWidget
    }

    private getWhatsInPackWidgetV1(userAgent: UserAgent, packContentsDetailed: ManagedPlanPackInfo): ProductListWidget {
        const header: Header = {
            title: packContentsDetailed.title,
            color: "#000000"
        }

        const cards: GradientCard[] = []
        packContentsDetailed.children.forEach(item => {
            cards.push({
                title: item.title,
                subTitle: item.shortDescription,
                shadowColor: item.shadowColor,
                gradientColors: item.gradientColors,
                icon: item.icon
            })
        })
        const widget = new ProductListWidget("GARDIENT_CARD", header, cards)
        if (userAgent === "DESKTOP") {
            widget.orientation = "RIGHT"
        }
        return widget
    }

    private getWhatsInPackWidget(userAgent: UserAgent, packContentsDetailed: ManagedPlanPackInfo): ProductListWidget {
        const header: Header = {
            title: packContentsDetailed.title,
            color: "#000000"
        }

        const cards: GradientCard[] = []
        packContentsDetailed.children.forEach(item => {
            switch (item.type) {
                case "MP_CONSULTATION":
                    cards.push({
                        title: item.title,
                        shadowColor: "#76e997",
                        gradientColors: ["#76e997", "#2cc2d3"],
                        icon: item.type
                    })
                    break
                case "MP_DIAGNOTIC":
                    cards.push({
                        title: item.title,
                        shadowColor: "#17d8e5",
                        gradientColors: ["#17d8e5", "#ac9aff"],
                        icon: item.type
                    })
                    break
                case "MP_PLAN":
                    cards.push({
                        title: item.title,
                        shadowColor: "#fb8a72",
                        gradientColors: ["#fb8a72", "#f64cac"],
                        icon: item.type
                    })
                    break
                case "MP_FEEDBACK":
                    cards.push({
                        title: item.title,
                        shadowColor: "#f29458",
                        gradientColors: ["#f29458", "#fd796d"],
                        icon: item.type
                    })
                    break
                case "MP_DEVICE":
                    cards.push({
                        title: item.title,
                        shadowColor: "#8cc5f3",
                        gradientColors: ["#8cc5f3", "#c495e0"],
                        icon: item.type
                    })
                    break
                default:
                    cards.push({
                        title: item.title,
                        shadowColor: "#8cc5f3",
                        gradientColors: ["#8cc5f3", "#c495e0"],
                        icon: item.type
                    })
                    break
            }

        })
        const widget =  new ProductListWidget("GARDIENT_CARD", header, cards)
        if (userAgent === "DESKTOP") {
            widget.orientation = "RIGHT"
        }
        return widget
    }

    private getDiagnosticsTestListWidget(userContext: UserContext, productId: string, title: string, gridSize: number, bundleProducts: DiagnosticTestProduct[], showCountOfTests: boolean, sellerDetails?: any, serviceabilityCheck?: boolean): PageWidget {
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "DESKTOP")
        const infoCards: InfoCard[] = []
        let totalParameters: number = 0
        const fulfilledDetails = (userContext.sessionInfo.appVersion >= 8.43 || AppUtil.isWeb(userContext)) ? CareUtil.getFullfilledDetailsV2(userContext, sellerDetails) : CareUtil.getFulfilledDetails(sellerDetails, false)
        const diagnosticsServiceability = serviceabilityCheck ? CareUtil.getDiagnosticsServiceability() : undefined
        bundleProducts.map((product, index) => {
            product.countParameters = this.countParameters(product)
            totalParameters = totalParameters + product.countParameters
            infoCards.push({
                title: product.name,
                image: product.imageUrl,
                moreIndex: index,
                countParameters: product.countParameters
            })
        })
        const action: Action = {
            actionType: CareUtil.getNewCarouselListActionType(userContext, "SHOW_DIAGNOSTICS_TEST_DETAIL_LIST"),
            meta: {
                type: "DIAGNOSTIC_TEST_DETAIL_LIST",
                productId: productId,
                bundleProducts: bundleProducts,
                totalParameters: showCountOfTests ? totalParameters : undefined,
                fulfilledDetails: fulfilledDetails,
                diagnosticsServiceability: diagnosticsServiceability,
            }
        }
        const widgetTitle: PageWidgetTitle = {
            title: title
        }
        const widget = new DiagnosticsTestListWidget(gridSize, infoCards, widgetTitle, action)
        if (userAgent === "DESKTOP") {
            widget.orientation = "RIGHT"
        }
        return widget
    }

    private countParameters(product: DiagnosticTestProduct) {
        if (product.items?.length > 0) {
            let count = 0
            product.items.map(( item) => {
                count = count + this.countParameters(item)
            }
            )
            return count
        }
        return 1
    }

    private getPreBookingActionsV2(isNotLoggedIn: boolean, isMultiCenterEnabled: boolean, patientsList: Patient[], packageProduct: ManagedPlanSellableProduct, isMalePreselected?: boolean, isValidateAdressSupported?: boolean, showUpdateForPurchase?: boolean): Action[] {
        if (isNotLoggedIn === true) {
            return [
                {
                    actionType: "SHOW_ALERT_MODAL",
                    title: "Buy Now",
                    meta: {
                        title: "Login Required!",
                        subTitle: "Please login to continue",
                        actions: [{ actionType: "LOGOUT", title: "Login" }]
                    }
                }
            ]
        }
        if (showUpdateForPurchase) {
            return [{
                actionType: "SHOW_ERROR_TOAST",
                title: "Buy now",
                meta: {
                    data: "We have some important changes. Please update to the latest version of the app to purchase this test",
                    duration: 3500,
                    position: 1,
                }
            }]
        }
        return this.getPatientSelectionActionV2(packageProduct,
            patientsList,
            false,
            isMultiCenterEnabled,
            "Buy Now",
            undefined,
            {
                replacePage: true,
                formUserType: "CARE_USER"
            },
            isMalePreselected,
            isValidateAdressSupported
        )

    }

    private getPreBookingActions(userAgent: UserAgent, product: Product, isNotLoggedIn: boolean, isMultiCenterEnabled: boolean, patientsList: Patient[], packageProduct: ManagedPlanSellableProduct, offerIds: string[], centerSelectionAction: SelectCenterAction,  patientId?: number, preferredCenter?: PreferredCenterResponse): Action[] {
        if (isNotLoggedIn === true) {
            return [
                {
                    actionType: "SHOW_ALERT_MODAL",
                    title: "Buy Now",
                    meta: {
                        title: "Login Required!",
                        subTitle: "Please login to continue",
                        actions: [{ actionType: "LOGOUT", title: "Login" }]
                    }
                }
            ]
        } else {
            if (!patientId) {
                return this.getPatientSelectionAction(packageProduct,
                    patientsList,
                    false,
                    "Buy Now",
                    undefined,
                    {
                        replacePage: true,
                        formUserType: "CARE_USER"
                    }
                )
            }
            if (isMultiCenterEnabled && _.isEmpty(preferredCenter)) {
                if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
                    return [{...CareUtil.getCenterSelectionAction(packageProduct, patientId), title: "Buy Now"}]
                } else {
                    return [{...centerSelectionAction, title: "Buy Now"}]
                }
            }
            let actionString: string = `curefit://carecartcheckout?productId=${product.productId}&productCode=${packageProduct.productCode}&patientId=${patientId}&subCategoryCode=${packageProduct?.subCategoryCode}`
            if (isMultiCenterEnabled && preferredCenter) {
                actionString += `&centerId=${preferredCenter.centerResponse.id}`
            }
            if (!_.isEmpty(offerIds)) {
                actionString += `&offerIds=${offerIds.join(",")}`
            }
            return [
                {
                    actionType: "NAVIGATION",
                    url: actionString,
                    title: "Buy Now"
                }
            ]
        }

    }

    private getMPPreBookingActions(userContext: UserContext, packageProduct: ManagedPlanSellableProduct, isNotLoggedIn: boolean, patientsList: Patient[], subscriptionStartDate: number): Action[] {
        if (isNotLoggedIn === true) {
            return [
                {
                    actionType: "SHOW_ALERT_MODAL",
                    title: "Sign me up!",
                    meta: {
                        title: "Login Required!",
                        subTitle: "Please login to continue",
                        actions: [{ actionType: "LOGOUT", title: "Login" }]
                    }
                }
            ]
        } else {
            const tz = userContext.userProfile.timezone
            const subsProduct = packageProduct.childProducts.filter(product => product.baseSellableProduct.subCategoryCode === "MP_SUBS")
            const subProduct = subsProduct[0].baseSellableProduct
            const startDate = TimeUtil.formatEpochInTimeZoneDateFns(tz, subscriptionStartDate, "yyyy-MM-dd")
            const endDate = TimeUtil.addDays(tz, startDate, subProduct.duration)

            const orderProducts = [{
                productId: subProduct.productCode,
                productType: "BUNDLE",
                quantity: 1,
                option: {
                    // offerV2Ids: !_.isEmpty(offerDetail.offers) ? offerDetail.offers.map(offer => offer.offerId) : [],
                    parentProductCode: packageProduct.productCode,
                    categoryCode: subProduct.categoryCode,
                    subCategoryCode: subProduct.subCategoryCode,
                    groupCode: subsProduct[0].groupCode,
                    childProductType: subsProduct[0].childProductType,
                    startDate,
                    endDate,
                    subscriptionType: subProduct.infoSection.subscriptionType,
                    autorenewalEnabled: true
                }
            }]
            const action: Action = {
                actionType: "SUBSCRIBE_MP",
                meta: {
                    orderProducts
                }
            }
            const calloutText = "This personalized plan is available only for yourself."
            const pageAction = CareUtil.getSelfPatientSelectionModalAction(patientsList, action, "Sign me up!", calloutText, {
                formUserType: "MANAGED_PLAN_USER"
            })

            if (CareUtil.isInstructionModalSupportedInApp(userContext)) {
                const instructionAction: Action = {
                    actionType: "SHOW_CARE_INSTRUCTION_MODAL",
                    title: "Sign me up!",
                    meta: {
                        header: {
                            title: "This pack is for"
                        },
                        instructions: this.getInstructionsForAIMGPurchase(userContext),
                        action: { ...pageAction, title: "CONTINUE" },
                        showBookLater: true
                    }
                }
                return [instructionAction]
            }
            return [pageAction]
        }
    }

    private getMPPreBookingActionsV2(userContext: UserContext, isMultiCenterEnabled: boolean, packageProduct: ManagedPlanSellableProduct, isNotLoggedIn: boolean, patientsList: Patient[], subscriptionStartDate: number, centerSelectionAction: SelectCenterAction,  patientId?: number, preferredCenter?: PreferredCenterResponse): Action[] {
        if (isNotLoggedIn === true) {
            return [
                {
                    actionType: "SHOW_ALERT_MODAL",
                    title: "Sign me up!",
                    meta: {
                        title: "Login Required!",
                        subTitle: "Please login to continue",
                        actions: [{ actionType: "LOGOUT", title: "Login" }]
                    }
                }
            ]
        } else {
            const calloutText = "This personalized plan is available only for yourself."
            if (!patientId) {
                const action = this.getPatientSelectionAction(packageProduct,
                    patientsList,
                    true,
                    "Sign me up!",
                    calloutText,
                    {
                        replacePage: true,
                        formUserType: "MANAGED_PLAN_USER"
                    }
                )
                if (CareUtil.isInstructionModalSupportedInApp(userContext)) {
                    const instructionAction: Action = {
                        actionType: "SHOW_CARE_INSTRUCTION_MODAL",
                        title: "Sign me up!",
                        meta: {
                            header: {
                                title: "This pack is for"
                            },
                            instructions: this.getInstructionsForAIMGPurchase(userContext),
                            action: { ...action[0], title: "CONTINUE" },
                            showBookLater: true
                        }
                    }
                    return [instructionAction]
                } else {
                    return action
                }
            }
            if (isMultiCenterEnabled && _.isEmpty(preferredCenter)) {
                return [
                    centerSelectionAction
                ]
            }
            const tz = userContext.userProfile.timezone
            const subsProduct = packageProduct.childProducts.filter(product => product.baseSellableProduct.subCategoryCode === "MP_SUBS")
            const subProduct = subsProduct[0].baseSellableProduct
            const startDate = TimeUtil.formatEpochInTimeZoneDateFns(tz, subscriptionStartDate, "yyyy-MM-dd")
            const endDate = TimeUtil.addDays(tz, startDate, subProduct.duration)

            const orderProducts = [{
                productId: subProduct.productCode,
                productType: "BUNDLE",
                quantity: 1,
                option: {
                    // offerV2Ids: !_.isEmpty(offerDetail.offers) ? offerDetail.offers.map(offer => offer.offerId) : [],
                    parentProductCode: packageProduct.productCode,
                    categoryCode: subProduct.categoryCode,
                    subCategoryCode: subProduct.subCategoryCode,
                    groupCode: subsProduct[0].groupCode,
                    childProductType: subsProduct[0].childProductType,
                    startDate,
                    endDate,
                    patientId: patientId,
                    centerId: isMultiCenterEnabled && preferredCenter && preferredCenter.centerResponse ? String(preferredCenter.centerResponse.id) : undefined,
                    subscriptionType: subProduct.infoSection.subscriptionType,
                    autorenewalEnabled: true
                }
            }]
            const action: Action = {
                actionType: "SUBSCRIBE_MP",
                title: "Sign me up!",
                meta: {
                    patientId: patientId,
                    orderProducts: orderProducts
                }
            }
            return [action]
        }
    }

    private getInstructionsForAIMGPurchase(userContext: UserContext) {
        const isIcon = userContext.sessionInfo.appVersion < isNewInstructionIconSupport ? false : true
        return [
            {
                iconType: isIcon ? "TOP_ARROW" : undefined,
                text:  isIcon ? "Age between 18 to 45 years" : `\u2022 Age between 18 to 45 years`
            },
            {
                iconType: isIcon ? "BMI" : undefined,
                text: isIcon ?  "BMI is between 23 and 34" : `\u2022 BMI is between 23 and 34`
            },
            {
                iconType: isIcon ? "HEART" : undefined,
                text: isIcon ?  "Have no major medical conditions" : `\u2022 Have no major medical conditions`
            }
        ]
    }

    private getPatientSelectionActionV2(packageProduct: ManagedPlanSellableProduct, patientsList: Patient[], isSelf: boolean, isMultiCenterEnabled: boolean, title?: string, calloutText?: string, meta?: any, isMalePreselected?: boolean, isValidateAdressSupported?: boolean) {
        if (isSelf) {
            return [
                CareUtil.getSelfPatientSelectionModalAction(patientsList,
                    {...CareUtil.getCenterSelectionAction(packageProduct, undefined, true), title: "Buy Now"},
                    title ? title : _.isEmpty(patientsList) ? "Add Patient" : "Select Patient",
                    calloutText,
                    meta
                )
            ]
        }
        let nextAction: Action
        const centerSelectionAction = CareUtil.getCenterSelectionAction(packageProduct, undefined, true)
        const checkoutAction = isValidateAdressSupported ? this.getAddressValidationAction(packageProduct) : this.getCheckoutAction(packageProduct)
        nextAction = isMultiCenterEnabled ? centerSelectionAction : checkoutAction
        return [
            CareUtil.getPatientSelectionModalAction(patientsList,
                nextAction,
                title ? title : _.isEmpty(patientsList) ? "Add Patient" : "Select Patient", undefined, calloutText, isMalePreselected
            )
        ]
    }

    private getCheckoutAction(product: ManagedPlanSellableProduct) {
        const actionString: string = `curefit://carecartcheckout?productId=${product.productCode}&productCode=${product.productCode}&subCategoryCode=${product?.subCategoryCode}`
        const action: Action = {
            actionType: "NAVIGATION",
            url: actionString,
            title: "Buy Now",
            meta: {
                message: "Please purchase the pack and schedule the test later at your convenience",
                actionMessage: "Proceed to Book",
                header: "Location Serviceable"
            }
        }
        return action
    }

    private getAddressValidationAction(product: ManagedPlanSellableProduct) {
        const forwardAction: Action = this.getCheckoutAction(product)
        const backwardAction: Action = this.getProductPageAction(product)
        const action: Action = {
            actionType: "VALIDATE_ADDRESS_AND_NAVIGATE",
            meta: {
                forwardAction,
                backwardAction,
                title: "Select your Home Address",
            }
        }
        return action
    }

    private getProductPageAction(product: ManagedPlanSellableProduct) {
        const actionString: string = `curefit://carefitmp?id=${product.productCode}&subCategoryCode=${product.subCategoryCode}`
        const action: Action = {
            actionType: "NAVIGATION",
            url: actionString,
            meta: {
                message: "Apologies. We are yet to provide home collection to your address",
                actionMessage: "Okay",
                header: "Location Not Serviceable"
            }
        }
        return action
    }

    private getPatientSelectionAction(packageProduct: ManagedPlanSellableProduct, patientsList: Patient[], isSelf: boolean, title?: string, calloutText?: string, meta?: any) {
        if (isSelf) {
            return [
                CareUtil.getSelfPatientSelectionModalAction(patientsList,
                    {
                        actionType: "NAVIGATION",
                        meta : meta,
                        url: ActionUtil.carefitbundle(packageProduct.productCode, packageProduct.subCategoryCode)
                    },
                    title ? title : _.isEmpty(patientsList) ? "Add Patient" : "Select Patient",
                    calloutText,
                    meta
                )
            ]
        }
        return [
            CareUtil.getPatientSelectionModalAction(patientsList,
                {
                    actionType: "NAVIGATION",
                    meta : meta,
                    url: ActionUtil.carefitbundle(packageProduct.productCode, packageProduct.subCategoryCode)
                },
                title ? title : _.isEmpty(patientsList) ? "Add Patient" : "Select Patient"
            )
        ]
    }

    private getInEligiblityDataWidget(userFormId: string, bmiRanges: BmiRange[], eligibility: EligibilityResponse, message: string): GradientCardWithBottomActionWidget {
        const currentEmi = eligibility.currentBMI
        const currentBmiClass = bmiRanges.find(range => range.min <= currentEmi && range.max >= currentEmi).class
        // if (WLCompatibilityStatuses.indexOf(eligibility.planCompatibilityStatus) !== -1) {
        //     currentBmiClass = bmiRanges.find(range => range.min <= currentEmi && range.max >= currentEmi).class
        // }
        return {
            widgetType: "GRADIENT_CARD_WITH_TOP_ACTION_WIDGET",
            showDivider: true,
            header: {
                title: "Oops! Sorry",
                subTitle: "Where you are today",
                topAction: {
                    action: {
                        actionType: "NAVIGATION",
                        url: `curefit://userform?formId=${userFormId}`
                    },
                    actionString: "START AGAIN"
                }
            },
            gradientCard: currentBmiClass ? {
                gradientColors: gradientColorsMap[currentBmiClass],
                items: [{
                    title: "Weight(kgs)",
                    subtitle: eligibility.currentWeight.toFixed(1).toString()
                }, {
                    title: "BMI",
                    subtitle: currentEmi.toFixed(1).toString()
                }, {
                    subtitle: capitalizeFirstLetter(currentBmiClass)
                }]
            } : undefined,
            bottomAction: {
                backgroundColor: "#fff5f6",
                text: message
            }
        }
    }

    private getWeightLossJourney(eligibility: EligibilityResponse) {
        const weightLoseJourney: WeightLoseJourneyValue[] = [], { projectedUserWLMetrics, currentWeight } = eligibility
        weightLoseJourney.push({
            title: "TODAY", value: {
                weight: currentWeight,
                unit: "KG"
            }
        })
        projectedUserWLMetrics.forEach(bmiValue => {
            weightLoseJourney.push({
                title: `IN ${bmiValue.duration} ${bmiValue.durationUnit}${bmiValue.duration > 1 ? "S" : ""}`, value: {
                    weight: bmiValue.weight,
                    unit: "KG"
                }
            })
        })
        return weightLoseJourney
    }

    private getEligibleWidget(userFormId: string, bmiRanges: BmiRange[], eligibility: EligibilityResponse, message: string): GradientCardWithBottomActionWidget {
        const currentEmi = eligibility.currentBMI
        const currentBmiClass = bmiRanges.find(range => range.min <= currentEmi && range.max >= currentEmi).class
        return {
            widgetType: "GRADIENT_CARD_WITH_TOP_ACTION_WIDGET",
            showDivider: true,
            header: {
                title: "Yay! You are at right place",
                subTitle: "Where you are today",
                topAction: {
                    action: {
                        actionType: "NAVIGATION",
                        url: `curefit://userform?formId=${userFormId}`
                    },
                    actionString: "START AGAIN"
                }
            },
            gradientCard: {
                gradientColors: gradientColorsMap[currentBmiClass],
                items: [{
                    title: "Weight(kgs)",
                    subtitle: eligibility.currentWeight.toFixed(1).toString()
                }, {
                    title: "BMI",
                    subtitle: currentEmi.toFixed(1).toString()
                }, {
                    subtitle: capitalizeFirstLetter(currentBmiClass)
                }]
            },
            bottomAction: {
                backgroundColor: "#F8FCFE",
                text: "With our personalized plan from health coaches, this how your weight loss journey would look",
                weightLoseJourney: this.getWeightLossJourney(eligibility)
            }
        }
    }

    private getWhatsInPackWidgetV2(userContext: UserContext, productId: string, packContentsDetailed: ManagedPlanPackInfo): ProductListWidget {
        const header: Header = {
            title: packContentsDetailed.title,
            color: "#000000"
        }

        const cards: GradientCarouselCard[] = []
        const bundleProducts: any[] = []
        packContentsDetailed.children.forEach((item, index) => {
            bundleProducts.push({
                icon: item.imageUrl,
                title: item.title,
                subTitle: item.desc
            })
            switch (item.type) {
                case "MP_CONSULTATION":
                    cards.push({
                        title: item.title,
                        shadowColor: "#76e997",
                        gradientColors: ["#76e997", "#2cc2d3"],
                        icon: item.type,
                        moreIndex: index
                    })
                    break
                case "MP_DIAGNOTIC":
                    cards.push({
                        title: item.title,
                        shadowColor: "#17d8e5",
                        gradientColors: ["#17d8e5", "#ac9aff"],
                        icon: item.type,
                        moreIndex: index
                    })
                    break
                case "AI_FITNESS":
                case "MP_PLAN":
                    cards.push({
                        title: item.title,
                        shadowColor: "#fb8a72",
                        gradientColors: ["#fb8a72", "#f64cac"],
                        icon: item.type,
                        moreIndex: index
                    })
                    break
                case "AI_NUTRITION":
                case "MP_FEEDBACK":
                    cards.push({
                        title: item.title,
                        shadowColor: "#f29458",
                        gradientColors: ["#f29458", "#fd796d"],
                        icon: item.type,
                        moreIndex: index
                    })
                    break
                case "MP_DEVICE":
                    cards.push({
                        title: item.title,
                        shadowColor: "#8cc5f3",
                        gradientColors: ["#8cc5f3", "#c495e0"],
                        icon: item.type,
                        moreIndex: index
                    })
                    break
                default:
                    cards.push({
                        title: item.title,
                        shadowColor: "#8cc5f3",
                        gradientColors: ["#8cc5f3", "#c495e0"],
                        icon: item.type,
                        moreIndex: index
                    })
                    break
            }
        })
        const action: Action = {
            actionType: CareUtil.getNewCarouselListActionType(userContext, "SHOW_GRADIENT_CAROUSEL_LIST"),
            meta: {
                type: "GRADIENT_CAROUSEL_LIST",
                productId: productId,
                bundleProducts: bundleProducts
            }
        }
        return new ProductListWidget("GRADIENT_CAROUSEL_CARD", header, cards, null, false, "LEFT", false, 1, "", action)
    }
}

export default BundleProductPageView
