import * as _ from "lodash"
import {
    AggregatedMembershipInfo,
    BookingDetail,
    ManagedPlanPackInfo,
    ConsultationInstructionResponse,
    UserMembershipInfo,
} from "@curefit/albus-client"
import {
    Action,
    Header,
    InfoCard,
    ManageOptionPayload,
    ManageOptions,
    PackProgress,
    ProductDetailPage,
    ProductListWidget,
    WidgetView,
    LHRBodyPartsWidget,
    LHRBodyPart,
} from "../common/views/WidgetView"
import {
    ContentAsset,
    DiagnosticProductResponse,
    ConsultationProduct,
    AllowedBodyPart
} from "@curefit/care-common"
import { CareUtil } from "../util/CareUtil"
import { ActionUtil } from "@curefit/base-utils"
import { TimeUtil } from "@curefit/util-common"
import { UserContext } from "@curefit/userinfo-common"
import { CustomerIssueType } from "@curefit/issue-common"
import { ActionableCardItem, HorizontalActionableCardListingWidget } from "../page/PageWidgets"
import { UserAgent } from "@curefit/base-common"
import { Orientation } from "@curefit/vm-common"
import { ProductType } from "@curefit/product-common"
import { SUPPORT_DEEP_LINK } from "../util/AppUtil"

class SkinCareBundlePostPurchaseProductPageView extends ProductDetailPage {
    public pageContext: any
    constructor(
        userContext: UserContext,
        product: DiagnosticProductResponse,
        bookingInfo: BookingDetail,
        issuesMap: Map<string, CustomerIssueType[]>,
        newReportIssueManageOption: ManageOptionPayload,
        primaryAggregatedMembershipInfo: AggregatedMembershipInfo,
        secondaryAggregatedMembershipInfo?: AggregatedMembershipInfo,
    ) {
        super()
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent")
        const { howItWorksItem, packQnA } = CareUtil.getPackContent(product)
        this.actions = this.getPostBookingAction(bookingInfo, primaryAggregatedMembershipInfo)
        this.widgets.push(this.summaryWidget(userContext, product, bookingInfo, issuesMap, newReportIssueManageOption, primaryAggregatedMembershipInfo, userAgent))
        if (CareUtil.isLHRProduct(product.clubCode)) {
            this.widgets.push(this.getLHRBodyPartsWidget(product, bookingInfo))
        }
        if (!_.isEmpty(bookingInfo.childBookingInfos)) {
            this.widgets.push(this.getConsultationsWidget(userContext, bookingInfo.childBookingInfos))
        }
        if (!_.isEmpty(secondaryAggregatedMembershipInfo)) {
            this.widgets.push(this.getFreeConsultationWidget(bookingInfo, secondaryAggregatedMembershipInfo, userAgent))
        }
        if (!_.isEmpty(packQnA)) {
            this.widgets.push(CareUtil.getPackQnAWidget(userAgent, packQnA))
        }
        this.widgets.push(CareUtil.getHowItWorksWidget(howItWorksItem, userAgent))
        this.widgets = this.widgets.filter(Boolean).map(widget => ({...widget, hasDividerBelow: false}))
    }

    private getFreeConsultationWidget(bookingInfo: BookingDetail, secondaryAggregatedMembershipInfo: AggregatedMembershipInfo, userAgent: UserAgent): ProductListWidget {
        const action = this.getPostBookingAction(bookingInfo, secondaryAggregatedMembershipInfo)
        if (!_.isEmpty(action)) {
            const cardAction: Action = {
                ...action[0],
                actionType: userAgent !== "APP" ? "SELECT_CARE_DATE" : action[0].actionType,
                title: "Schedule Free Consultation"
            }
            const widget = userAgent === "APP" ?  new ProductListWidget("DETAIL", undefined, [{
                title: "Meet Doctor",
                subTitle: "Our skin/hair care experts will be ready to help you between treatments",
                icon: "qualityCare"
            }], { cardAction }) : new ProductListWidget("DETAIL", {title: "Meet Doctor"}, [{
                subTitle: "Our skin/hair care experts will be ready to help you between treatments",
                icon: "qualityCare"
            }], { cardAction })
            widget.hasDividerBelow = true
            widget.dividerType = "SMALL"
            if (userAgent === "DESKTOP") {
                widget.orientation = "RIGHT"
            }
            return widget
        }
    }

    private summaryWidget(userContext: UserContext, product: DiagnosticProductResponse, bookingDetail: BookingDetail,
        issuesMap: Map<string, CustomerIssueType[]>, newReportIssueManageOption: ManageOptionPayload,
        aggregatedMembershipInfo: AggregatedMembershipInfo, userAgent: UserAgent): WidgetView {
        const manageOptionsView: { manageOptions: ManageOptions; meta: any } = this.getManageOptions(bookingDetail,
            bookingDetail.booking.cfOrderId,
            bookingDetail.booking.productCode,
            issuesMap,
            newReportIssueManageOption
        )
        const title = _.get(product, "infoSection.packTitle", product.productName)
        const summaryWidget: WidgetView & {
            productId: string
            title: string
            subTitle?: string
            image: string
            meta: any
            assets?: ContentAsset[]
            packProgress?: PackProgress
            manageOptions: ManageOptions,
            dividerType: string
            hasDividerBelow: boolean
            breadcrumb?: { text: string, link?: string }[]
            orientation?: Orientation,
            actions: Action[],
            productType?: ProductType,
        } = {
            widgetType: "HEALTHCHECKUP_SUMMARY_WIDGET",
            subTitle: this.getSubTitle(bookingDetail, aggregatedMembershipInfo, userContext),
            title,
            productId: product.productCode,
            image: product.heroImageUrl,
            assets: _.get(product, "contentAssets.carouselAssets", []),
            manageOptions: manageOptionsView.manageOptions,
            meta: manageOptionsView.meta,
            dividerType: "SMALL",
            hasDividerBelow: true,
            orientation: userAgent === "DESKTOP" ? "RIGHT" : undefined,
            breadcrumb: userAgent === "DESKTOP" ? [{ text: "Home", link: "/" }, { text: "Care", link: "/care" }, { text: "Skin and Hair", link: "/care/skinhair" }, { text: _.get(product, "infoSection.packTitle", product.productName) }] : [],
            actions: userAgent === "DESKTOP" || userAgent === "MBROWSER" ? this.actions : [],
            productType: "BUNDLE"
        }
        if (CareUtil.isLHRProduct(product.clubCode)) {
            summaryWidget.style = {
                backgroundColor: "#f2f4f8"
            }
        }
        if (aggregatedMembershipInfo) {
            summaryWidget.packProgress = this.getPackProgress(userContext, title, aggregatedMembershipInfo, bookingDetail)
        }
        return summaryWidget
    }

    public getSubTitle(bookingDetail: BookingDetail, aggregatedMembershipInfo: AggregatedMembershipInfo, userContext: UserContext) {
        if (bookingDetail.booking.status === "CANCELLED") {
            return "Cancelled"
        }
        if (_.isEmpty(aggregatedMembershipInfo)) {
            return _.get(bookingDetail, "bundleOrderResponse.patient.name") ? `For: ${bookingDetail.bundleOrderResponse.patient.name} | Pack Expired` : undefined
        }
        return _.get(bookingDetail, "bundleOrderResponse.patient.name") ? `For: ${bookingDetail.bundleOrderResponse.patient.name}` : undefined
    }

    private getPackProgress(userContext: UserContext, title: string, aggregatedMembershipInfo: AggregatedMembershipInfo, bookingDetail: BookingDetail): PackProgress {
        let packProgress: PackProgress
        if (_.isEmpty(aggregatedMembershipInfo.startEndEpoch)) {
            return
        }
        const totalSessions = aggregatedMembershipInfo.totalTickets
        const productCodes = aggregatedMembershipInfo.productMetas.map(prouctMeta => prouctMeta.productCode)
        const completedSessions = aggregatedMembershipInfo.totalTicketsConsumed // _.filter(bookingDetail.childBookingInfos, childBooking => productCodes.includes(childBooking.booking.productCode) && childBooking.booking.status === "COMPLETED").length
        const { total, completed, isCompleted, endDateFormatted } = CareUtil.getConsultationPackProgressInfo(userContext, aggregatedMembershipInfo, true)
        if (userContext.sessionInfo.userAgent === "APP") {
            packProgress = {
                total,
                completed,
                state: "ACTIVE",
                type: "BUNDLE",
                leftText: `${completedSessions}/${totalSessions} Session Booked` ,
                rightText: endDateFormatted,
                progressBarColor: "#008300",
                progressBarTextStyle: { marginTop: 10, fontSize: 14 },
                isSplitView: true
            }
        } else {
            packProgress = {
                subTitle: endDateFormatted,
                title: `${completedSessions}/${totalSessions} Session Booked` ,
                total,
                completed,
                state: isCompleted ? "COMPLETED" : "ACTIVE",
                packColor: "#008300",
                type: "BUNDLE",
                action: !isCompleted && this.actions && this.actions[0] ? this.actions[0] : undefined
            }
        }
        return packProgress
    }

    private getIssueList(issuesMap: Map<string, CustomerIssueType[]>): { code: string, title: string, confirmation: string }[] {
        const issueList: { code: string, title: string, confirmation: string }[] = []
        issuesMap.get("DiagnosticsSingleBooked").forEach(customerIssueType => {
            issueList.push({
                code: customerIssueType.code,
                title: customerIssueType.subject,
                confirmation: customerIssueType.confirmation
            })
        })
        return issueList
    }

    private getManageOptions(bookingDetail: BookingDetail, orderId: string, productId: string,
        issuesMap: Map<string, CustomerIssueType[]>,
        newReportIssueManageOption: ManageOptionPayload): { manageOptions: ManageOptions, meta: any } {
        const options: ManageOptionPayload[] = []
        if (newReportIssueManageOption) {
            options.push(newReportIssueManageOption)
        } else {
            options.push(
                {
                    isEnabled: true,
                    displayText: "Need Help",
                    type: "REPORT_ISSUE",
                    meta: this.getIssueList(issuesMap),
                    action: SUPPORT_DEEP_LINK,
                }
            )
        }

        const manageOptions: ManageOptions = {
            displayText: "...",
            options: options
        }

        const meta: any = {
            orderId: orderId,
            productId: productId,
            tcBookingId: bookingDetail.booking.id
        }
        return { manageOptions: manageOptions, meta: meta }
    }

    private getLHRBodyPartsWidget(product: DiagnosticProductResponse, bookingInfo: BookingDetail): LHRBodyPartsWidget {
        const selectedBodyPartIds: string[] = _.get(bookingInfo, "bundleOrderResponse.hcuTestDetails.selectedBodyParts")
        const allowedBodyParts: AllowedBodyPart[] = _.get(product, "productSpec.allowedBodyParts")
        if (!_.isEmpty(selectedBodyPartIds) && !_.isEmpty(allowedBodyParts)) {
            const selectedParts: LHRBodyPart[] = []
            _.map(allowedBodyParts, part => {
                if (selectedBodyPartIds.indexOf(part.code) != -1) {
                    selectedParts.push({
                        imageUrl: part.assetUrl,
                        code: part.code,
                        title: part.displayName,
                    })
                }
            })
            const numberOfSessions = Number(_.get(product, "infoSection.numberOfSessions", 0))
            const numberOfSelectedBodyParts = selectedBodyPartIds.length
            const sessionCountPerBodyPart = numberOfSessions / numberOfSelectedBodyParts
            return {
                widgetType: "CARE_LHR_BODY_PARTS_WIDGET",
                title: `${sessionCountPerBodyPart} sessions each for ${numberOfSelectedBodyParts} body parts`,
                style: {
                    backgroundColor: "#f2f4f8",
                },
                bodyParts: selectedParts
            }
        }
        return undefined
    }


    private getConsultationsWidget(userContext: UserContext, consultations: BookingDetail[]): HorizontalActionableCardListingWidget {
        const cardItems: ActionableCardItem[] = []
        consultations.sort((a: BookingDetail, b: BookingDetail) => {
            return b.consultationOrderResponse.startTime - a.consultationOrderResponse.startTime
        })
        consultations.map(consultation => cardItems.push(this.getConsultationActionableCardWidget(userContext, consultation)))
        return {
            widgetType: "HORIZONTAL_ACTIONAL_CARD_LISTING_WIDGET",
            title: "Your Sessions",
            type: "CONSULTATION",
            cardItems,
            hasDividerBelow: true,
            dividerType: "SMALL",
            useNewWidgetInWeb: true,
            orientation: userContext.sessionInfo.userAgent === "DESKTOP" ? "RIGHT" : undefined
        }
    }

    private getConsultationActionableCardWidget(userContext: UserContext, booking: BookingDetail): ActionableCardItem {
        const tz = userContext.userProfile.timezone
        const actions: Action[] = []
        const vertical = CareUtil.getVerticalForConsultation(booking.consultationOrderResponse?.consultationProduct?.doctorType)
        return {
            tag: CareUtil.getConsultationTag(booking.consultationOrderResponse.consultationUserState, booking.consultationOrderResponse.status),
            title: `${_.get(booking, "consultationOrderResponse.consultationProduct.name", "Session")} with ${booking.consultationOrderResponse.doctor.name}`,
            subTitle: `for ${booking.consultationOrderResponse.patient.name}`,
            imageUrl: booking.consultationOrderResponse.doctor.displayImage,
            footer: CareUtil.getUpcomingConsultationFooterInfo(booking.consultationOrderResponse, tz),
            actions: actions,
            cardAction: {
                actionType: "NAVIGATION",
                url: ActionUtil.teleconsultationSingle(userContext, booking.booking.productCode, booking.consultationOrderResponse?.consultationProduct.urlPath, booking.consultationOrderResponse.bookingId.toString(), undefined, vertical)
            }
        }
    }

    private getPostBookingAction(bookingInfo: BookingDetail, aggregatedMembershipInfo: AggregatedMembershipInfo): Action[] {
        if (!aggregatedMembershipInfo) {
            return
        }
        if (aggregatedMembershipInfo.totalTickets - aggregatedMembershipInfo.totalTicketsConsumed < 1) {
            return
        }
        const bookingId = bookingInfo.booking.id
        const patientId = bookingInfo.booking.patientId
        const consultationProducts: { productDetail: ConsultationProduct, consultationInstruction: ConsultationInstructionResponse[] }[] = _.map(aggregatedMembershipInfo.userMembershipInfos, (userMembershipInfo: UserMembershipInfo) => {
            return {
                productDetail: <ConsultationProduct>userMembershipInfo.productDetail,
                consultationInstruction: userMembershipInfo.consultationInstruction
            }
        })
        if (!_.isEmpty(consultationProducts)) {
            if (consultationProducts.length > 1) {
                return this.getGroupBookAction(consultationProducts, patientId, bookingId)
            } else if (consultationProducts.length === 1) {
                const { productDetail, consultationInstruction } = consultationProducts[0]
                return [this.getIndividualBookAction(productDetail.productId, patientId, bookingId, consultationInstruction)]
            }
        }
        return []
    }

    private getIndividualBookAction(productId: string, patientId: number, bookingId: number, instructions: ConsultationInstructionResponse[]): Action {
        const url = ActionUtil.selectCareDateV1(productId, undefined, bookingId)
        const action = {
            actionType: "NAVIGATION",
            title: "Book A Session",
            url: `${url}&patientId=${patientId}`
        } as Action
        if (!_.isEmpty(instructions)) {
            return {
                actionType: "SHOW_CARE_INSTRUCTION_MODAL",
                title: "Book a session",
                meta: {
                    header: {
                        title: "Must follow Instructions"
                    },
                    instructions: [],
                    instructionMap: instructions,
                    action: { ...action, title: "CONTINUE" },
                    showBookLater: true
                }
            }
        }
        return action
    }

    private getGroupBookAction(consultationProducts: { productDetail: ConsultationProduct, consultationInstruction: ConsultationInstructionResponse[] }[], patientId: number, bookingId: number): any[] {
        const actions = consultationProducts.map(consultationProduct => {
            const { productDetail, consultationInstruction } = consultationProduct
            return {
                ...this.getIndividualBookAction(productDetail.productId, patientId, bookingId, consultationInstruction),
                title: productDetail.title
            }
        })
        return [
            {
                actionType: "ACTION_LIST",
                title: "Book a session",
                actions
            }
        ]
    }
}

export default SkinCareBundlePostPurchaseProductPageView
