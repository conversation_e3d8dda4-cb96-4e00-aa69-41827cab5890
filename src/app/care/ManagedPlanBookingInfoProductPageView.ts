import { CustomerIssueType } from "@curefit/issue-common"
import { DiagnosticProduct, DiagnosticProductResponse } from "@curefit/care-common"
import {
    ActionV2,
    AggregatedMembershipInfo,
    BookingDetail,
    BundleSetupInfoV2,
    CareTeam,
    DiagnosticsTestOrderResponse,
    DiagnosticTestProduct,
    RecommendLabTestResponse
} from "@curefit/albus-client"
import {
    Action,
    Header,
    InfoCard,
    LabTestListingWidget,
    ManageOptionPayload,
    ManageOptions,
    PackProgress,
    ProductDetailPage,
    ProductListWidget,
    WidgetView
} from "../common/views/WidgetView"
import {
    ActionableCardItem,
    ActionCard,
    CareProductStateWidget,
    CareTeamItem,
    CareTeamWidget,
    DiagnosticTestReportItem,
    HorizontalActionableCardListingWidget,
    ProductStateItem,
    StepStateCard
} from "../page/PageWidgets"
import CareUtil, { CARE_TEAM_V2_SUPPORTED, EMAIL_NEW_PLAN_SUPPORTED } from "../util/CareUtil"
import * as _ from "lodash"
import { ActionUtil } from "@curefit/base-utils"
import { TimeUtil } from "@curefit/util-common"
import { PackOffersResponse } from "@curefit/offer-common"
import { UserContext } from "@curefit/userinfo-common"
import { SUPPORT_DEEP_LINK } from "../util/AppUtil"


class ManagedPlanBookingInfoProductPageView extends ProductDetailPage {
    public pageContext: any
    constructor(userContext: UserContext,
        careTeam: CareTeam[],
        product: DiagnosticProduct,
        subcriptionBooking: BookingDetail,
        subscriptionProduct: DiagnosticProduct,
        bookingInfo: BookingDetail,
        issuesMap: Map<string, CustomerIssueType[]>,
        newReportIssueManageOption: ManageOptionPayload,
        consultations: BookingDetail[],
        devices: BookingDetail[],
        tests: BookingDetail[],
        reports: DiagnosticsTestOrderResponse[],
        aggregatedMemberships: AggregatedMembershipInfo[],
        renewAction: ActionV2,
        subscriptionOffer: PackOffersResponse,
        subscriptionStartDate: number,
        recommendedTests: RecommendLabTestResponse) {
        super()
        const isMPV2 = bookingInfo.booking.subCategoryCode === "MP_V2"
        const allowedDoctorId: number[] = []
        if (isMPV2) {
            const bundleBookingInfo = bookingInfo.childBookingInfos.find(info => info.booking.subCategoryCode === "MP_V2_OT")
            const stepInfosV2: BundleSetupInfoV2[]  =  bundleBookingInfo.stepInfosV2.filter(stepInfos => stepInfos.stepInfoType === "CONSULTATION")
            if (stepInfosV2) {
                stepInfosV2.map((stepInfo: BundleSetupInfoV2) => {
                    stepInfo.consultationBookingInfos.map((booking: any) => {
                        if (booking && booking.consultationOrderResponse && booking.consultationOrderResponse.status === "COMPLETED") {
                            allowedDoctorId.push(booking.consultationOrderResponse.doctor.id)
                        }
                    })
                })
            }
        }
        this.widgets.push(this.getSummaryWidget(userContext, product, bookingInfo, subcriptionBooking,
            subscriptionProduct, issuesMap, newReportIssueManageOption))
        this.widgets.push(this.getCareTeamWidget(userContext, bookingInfo, careTeam, allowedDoctorId, isMPV2))
        if (isMPV2) {
            const widget = this.getPendingConsultations(userContext, bookingInfo)
            widget && this.widgets.push(widget)
        }
        if (!_.isEmpty(recommendedTests) && !_.isEmpty(recommendedTests.labtestPrescriptionInfos)) {
            const widget = this.labtestWidget(bookingInfo.booking.patientId, recommendedTests, "Diagnostic Tests", bookingInfo.booking.id, userContext)
            if (!_.isEmpty(widget)) {
                this.widgets.push(widget)
            }
        }
        this.widgets.push(this.getConsultationsWidget(consultations, userContext))
        if (!_.isEmpty(tests)) {
            const scheduledTests = tests.filter(test => test.diagnosticsTestOrderResponse[0].status !== "REPORT_GENERATED")
            if (!_.isEmpty(scheduledTests)) {
                this.widgets.push(this.getDiagnosticTestWidget(userContext, scheduledTests))
            }
        }
        if (!_.isEmpty(aggregatedMemberships)) {
            this.widgets.push(this.getFreeTestsWidget(userContext, bookingInfo.booking.id, bookingInfo.booking.patientId, aggregatedMemberships))
        }
        if (!_.isEmpty(reports)) {
            const filteredReports = reports.filter(report => report.status === "REPORT_GENERATED")
            if (!_.isEmpty(filteredReports)) {
                this.widgets.push(this.getReportsWidget(userContext, filteredReports))
            }
        }
        if (!_.isEmpty(devices)) {
            const connectedDevices = devices.filter(device => device.deviceOrderResponse.status === "HANDED_OVER" && device.deviceOrderResponse.subCategory !== "INT_WELCOME_KIT")
            if (!_.isEmpty(connectedDevices)) {
                this.widgets.push(this.getDevicesWidget(connectedDevices))
            }
        }
        if (!_.isEmpty(renewAction)) {
            this.actions.push({
                title: isMPV2 ? "Renew Plan" : "Renew Subscription",
                actionType: "SHOW_RENEW_MP_SUBSCRIPTION_MODAL",
                meta: CareUtil.getRenewMPSubscriptionActionMeta(userContext, subcriptionBooking, renewAction, subscriptionOffer, subscriptionStartDate, bookingInfo.booking.subCategoryCode)
            })
        }
    }

    private getDevicesWidget(connectedDevices: BookingDetail[]): ProductListWidget {
        const header = {
            title: "Connected Devices"
        }
        const actionCards: ActionCard[] = []
        connectedDevices.map(device => {
            actionCards.push({
                title: device.deviceOrderResponse.deviceProduct.name,
                subTitle: device.deviceOrderResponse.deviceProduct.description,
                image: device.deviceOrderResponse.deviceProduct.imageUrl,
                actionObj: {
                    actionType: "SHOW_INFO_MODAL",
                    meta: {
                        title: device.deviceOrderResponse.deviceProduct.name,
                        subTitle: device.deviceOrderResponse.deviceProduct.description,
                        icon: device.deviceOrderResponse.deviceProduct.imageUrl
                    }
                }
            })
        })
        return new ProductListWidget("MEDIUM", header, actionCards)
    }

    private getSummaryWidget(userContext: UserContext, product: DiagnosticProduct, bookingDetail: BookingDetail,
        subcriptionBooking: BookingDetail, subscriptionProduct: DiagnosticProduct,
        issuesMap: Map<string, CustomerIssueType[]>,
        newReportIssueManageOption: ManageOptionPayload): WidgetView {
        let manageOptionsView: { manageOptions: ManageOptions; meta: any } = null
        manageOptionsView = this.getManageOptions(bookingDetail, bookingDetail.booking.cfOrderId,
            bookingDetail.booking.productCode, issuesMap, newReportIssueManageOption)
        const checkupSummaryWidget: WidgetView & {
            productId: string;
            title: string;
            subTitle: string;
            image: string;
            meta: any,
            packProgress: PackProgress,
            manageOptions: ManageOptions,
        } = {
            widgetType: "HEALTHCHECKUP_SUMMARY_WIDGET",
            subTitle: this.getSubTitle(bookingDetail),
            title: product.title,
            productId: product.productId,
            image: product.heroImageUrl,
            manageOptions: manageOptionsView.manageOptions,
            meta: manageOptionsView.meta,
            packProgress: this.getPackProgress(userContext, subcriptionBooking, subscriptionProduct)
        }
        return checkupSummaryWidget
    }

    getPackProgress(userContext: UserContext, subcriptionBooking: BookingDetail, subscriptionProduct: DiagnosticProduct): PackProgress {
        const tz = userContext.userProfile.timezone
        const startDate = TimeUtil.formatEpochInTimeZone(tz, subcriptionBooking.bundleOrderResponse.startTimeEpoch)
        const endDate = TimeUtil.formatEpochInTimeZone(tz, subcriptionBooking.bundleOrderResponse.expiryTimeEpoch)
        const today = TimeUtil.formatEpochInTimeZone(tz, TimeUtil.getCurrentEpoch())
        const total = TimeUtil.diffInDays(userContext.userProfile.timezone, startDate, endDate)
        const completed = TimeUtil.diffInDays(userContext.userProfile.timezone, startDate, today)
        const newEmailPlanSupported = userContext.sessionInfo.appVersion >= EMAIL_NEW_PLAN_SUPPORTED
        const packProgress: PackProgress = {
            startDate: startDate,
            endDate: endDate,
            total: total,
            completed: completed,
            state: subcriptionBooking.stepInfosV2[0].stepState === "SUBSCRIPTION_ACTIVE" ? "ACTIVE" : "EXPIRED",
            type: "BUNDLE",
            title: subscriptionProduct.subTitle,
            subTitle: newEmailPlanSupported  ? "Email Plan" : undefined,
            displayText: CareUtil.getMPSubscriptioDisplayText(userContext, subcriptionBooking.bundleOrderResponse.expiryTimeEpoch, subcriptionBooking.stepInfosV2[0].stepState),
            action: newEmailPlanSupported ? {
                actionType: "EMAIL_PLAN_NEW",
                meta: {
                    userId: userContext.userProfile.userId,
                    patientId: subcriptionBooking.booking.patientId
                }
            } : undefined
        }
        return packProgress
    }

    private getIssueList(issuesMap: Map<string, CustomerIssueType[]>): { code: string, title: string, confirmation: string }[] {
        const issueList: { code: string, title: string, confirmation: string }[] = []
        issuesMap.get("DiagnosticsSingleBooked").forEach(customerIssueType => {
            issueList.push({
                code: customerIssueType.code,
                title: customerIssueType.subject,
                confirmation: customerIssueType.confirmation
            })
        })
        return issueList
    }

    private getSubTitle(bookingDetail: BookingDetail) {
        const age = !_.isEmpty(bookingDetail) && !_.isEmpty(bookingDetail.bundleOrderResponse.patient.formattedAge) ? bookingDetail.bundleOrderResponse.patient.formattedAge.numOfYears : undefined
        if (bookingDetail.booking.status === "CANCELLED") {
            return "Cancelled"
        } else if (age) {
            return `For ${bookingDetail.bundleOrderResponse.patient.name}, ${age}`
        } else {
            return `For ${bookingDetail.bundleOrderResponse.patient.name}`
        }
    }

    private getManageOptions(bookingDetail: BookingDetail, orderId: string, productId: string,
        issuesMap: Map<string, CustomerIssueType[]>,
        newReportIssueManageOption: ManageOptionPayload): { manageOptions: ManageOptions, meta: any } {
        const options: ManageOptionPayload[] = []
        if (newReportIssueManageOption) {
            options.push(newReportIssueManageOption)
        } else {
            options.push(
                {
                    isEnabled: true,
                    displayText: "Need Help",
                    type: "REPORT_ISSUE",
                    meta: this.getIssueList(issuesMap),
                    action: SUPPORT_DEEP_LINK,
                }
            )
        }


        const manageOptions: ManageOptions = {
            displayText: "...",
            options: options
        }

        const meta: any = {
            orderId: orderId,
            productId: productId,
            tcBookingId: bookingDetail.booking.id
        }
        return { manageOptions: manageOptions, meta: meta }
    }

    private getCareTeamWidget(userContext: UserContext, bookingInfo: BookingDetail, careTeam: CareTeam[], allowedDoctorIds: number[], isMPV2: boolean): CareTeamWidget {
        const careTeamViewItems: CareTeamItem[] = []
        const isOldAction = userContext.sessionInfo.appVersion < CARE_TEAM_V2_SUPPORTED
        careTeam.map(item => {
            if (isMPV2 && !allowedDoctorIds.includes(item.doctor.id)) {
                return
            }
            careTeamViewItems.push({
                title: item.doctor.name,
                subTitle: item.doctorTypeCode.displayValue,
                imageUrl: item.doctor.displayImage,
                messageAction: this.getChatAction(userContext, item),
                actions: isOldAction
                    ? this.getCareTeamActions(userContext, bookingInfo.booking.patientId, item)
                    : this.getCareTeamActionsV2(userContext, bookingInfo.booking.patientId, item)
            })
        })
        return new CareTeamWidget(careTeamViewItems, "Care Team")
    }

    private getFreeTestsWidget(userContext: UserContext, bookingId: number, patientId: number, aggregatedMemberships: AggregatedMembershipInfo[]): HorizontalActionableCardListingWidget {
        const cardItems: ActionableCardItem[] = []
        const productCodes: string[] = []
        aggregatedMemberships.map(aggregatedMembership => {
            productCodes.push(aggregatedMembership.productMetas[0].productCode)
            cardItems.push(this.getFreeTestActionableCardWidget(userContext, aggregatedMembership))
        })
        const footer: {
            title: string,
            action: Action
        } = {
            title: "BOOK TEST",
            action: {
                actionType: "NAVIGATION",
                url: `curefit://testsListing?category=IN_CENTRE_SLOT&testCodes=${productCodes}&patientId=${patientId}&parentBookingId=${bookingId}`// todo add support for athome
            }
        }
        return {
            widgetType: "HORIZONTAL_ACTIONAL_CARD_LISTING_WIDGET",
            title: "Perodic Free test",
            type: "TEST",
            cardItems,
            footer
        }
    }

    private getFreeTestActionableCardWidget(userContext: UserContext, aggregatedMembership: AggregatedMembershipInfo): ActionableCardItem {
        return {
            title: (<DiagnosticTestProduct>(aggregatedMembership.productMetas[0].productDetail)).name,
            subTitle: `${aggregatedMembership.userMembershipInfos[0].tickets - aggregatedMembership.userMembershipInfos[0].ticketsConsumed}/${aggregatedMembership.userMembershipInfos[0].tickets} LEFT`,
            cardAction: {
                actionType: CareUtil.getNewCarouselListActionType(userContext, "SHOW_DIAGNOSTICS_TEST_DETAIL_LIST"),
                meta: {
                    type: "DIAGNOSTIC_TEST_DETAIL_LIST",
                    bundleProducts: (<DiagnosticTestProduct>(aggregatedMembership.productMetas[0].productDetail)).items
                }
            }
        }
    }

    private getReportsWidget(userContext: UserContext, reports: DiagnosticsTestOrderResponse[]): HorizontalActionableCardListingWidget {
        return {
            widgetType: "HORIZONTAL_ACTIONAL_CARD_LISTING_WIDGET",
            title: "Reports",
            type: "REPORT",
            cardItems: reports.map(report => this.getReportActionableCardWidget(userContext, report))
        }
    }

    getReportActionableCardWidget(userContext: UserContext, report: DiagnosticsTestOrderResponse): DiagnosticTestReportItem {
        const reportInfo = report.finalDiagnosticReport.diagnosticCheckUpReportInfo
        return {
            header: {
                title: reportInfo.testId || "Diagnostic Report",
                subtitle: !_.isEmpty(report.inCentreDiagnosticOrder) ? `${TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, report.inCentreDiagnosticOrder.slot.workingStartTime, "D MMM")}` : undefined,
                action: {
                    url: ActionUtil.diagnosticReportPage(report.orderId, report.carefitOrderId),
                    actionType: "NAVIGATION"
                }
            },
            testInfos: CareUtil.getTestReportDetailsView(reportInfo)
        }
    }

    private getDiagnosticTestWidget(userContext: UserContext, tests: BookingDetail[]): HorizontalActionableCardListingWidget {
        return {
            widgetType: "HORIZONTAL_ACTIONAL_CARD_LISTING_WIDGET",
            title: "Scheduled Tests",
            type: "CONSULTATION",
            cardItems: tests.map(test => CareUtil.getDiagnosticTestWidget(userContext, test.diagnosticsTestOrderResponse[0]))
        }
    }

    private getConsultationsWidget(consultations: BookingDetail[], userContext: UserContext): HorizontalActionableCardListingWidget {
        return {
            widgetType: "HORIZONTAL_ACTIONAL_CARD_LISTING_WIDGET",
            title: "Consultations",
            type: "CONSULTATION",
            cardItems: consultations.map(consultation => this.getConsultationActionableCardWidget(consultation, userContext))
        }
    }

    getConsultationActionableCardWidget(booking: BookingDetail, userContext: UserContext): ActionableCardItem {
        const actions: Action[] = this.getActions(booking, userContext)
        const tz = userContext.userProfile.timezone
        const vertical = CareUtil.getVerticalForConsultation(booking.consultationOrderResponse?.consultationProduct?.doctorType)
        return {
            tag: CareUtil.getConsultationTag(booking.consultationOrderResponse.consultationUserState, booking.consultationOrderResponse.status),
            title: (!_.isEmpty(booking.consultationOrderResponse.doctorTypeCodeResponse.displayValue)) ? `${booking.consultationOrderResponse.doctorTypeCodeResponse.displayValue} Consultation` : `${booking.consultationOrderResponse.doctor.doctorTypes[0].type.displayValue} Consultation`,
            subTitle: `With ${booking.consultationOrderResponse.doctor.name} for ${booking.consultationOrderResponse.patient.name}`,
            imageUrl: booking.consultationOrderResponse.doctor.displayImage,
            footer: CareUtil.getUpcomingConsultationFooterInfo(booking.consultationOrderResponse, tz),
            actions: actions,
            cardAction: {
                actionType: "NAVIGATION",
                url: ActionUtil.teleconsultationSingle(userContext, booking.booking.productCode, booking.consultationOrderResponse?.consultationProduct.urlPath, booking.consultationOrderResponse.bookingId.toString(), undefined, vertical)
            }
        }
    }

    private getActions(booking: BookingDetail, userContext: UserContext): Action[] {
        const actions: Action[] = []
        if (!_.isEmpty(booking) && CareUtil.isComplteted(booking)) {
            if (booking && booking.consultationOrderResponse && booking.consultationOrderResponse.hasPrescription) {
                actions.push({
                    title: "Prescription",
                    actionType: "NAVIGATION",
                    icon: "PRESCRIPTION",
                    url: `curefit://carefitPrescription?tcBookingId=${booking.booking.id}&productId=${booking.booking.productCode}`
                })
            }
        }

        // if (consultationOrderResponse.consultationUserState === "COMPLETED" && consultationOrderResponse.followUpContext && consultationOrderResponse.followUpContext.enabled) {
        //     const parentBookingId = booking.booking.id
        //     const followUpConsultationId = booking.consultationOrderResponse.followUpConsultationId

        //     actions.push(CareUtil.getFollowupAction(parentBookingId, consultationOrderResponse.followupProducts, followUpConsultationId, consultationOrderResponse.patient.id))
        // }
        const consultationOrderResponse = booking.consultationOrderResponse
        if (consultationOrderResponse && consultationOrderResponse.appointmentActionsWithContext && consultationOrderResponse.appointmentActionsWithContext.chatActionWithContext && consultationOrderResponse.appointmentActionsWithContext.chatActionWithContext.action.actionPermitted) {
            const chatAction: any = CareUtil.getChatMessageAction(
                userContext,
                _.get(consultationOrderResponse, "appointmentActionsWithContext.chatActionWithContext", null),
                consultationOrderResponse.patient.id,
                consultationOrderResponse.doctor.name,
                CareUtil.getChatChannelWithAppointmentContext(consultationOrderResponse.appointmentActionsWithContext),
                consultationOrderResponse.doctor.displayImage,
                consultationOrderResponse.doctor.qualification,
                booking.booking.id
            )
            chatAction && actions.push(chatAction)
        }
        if (actions.length <= 2) {
            if (consultationOrderResponse && consultationOrderResponse.appointmentActionsWithContext && consultationOrderResponse.appointmentActionsWithContext.rescheduleActionWithContext && consultationOrderResponse.appointmentActionsWithContext.rescheduleActionWithContext.action.actionPermitted && consultationOrderResponse?.center?.id) {
                actions.push({
                    title: "Reschedule",
                    isEnabled: true,
                    actionType: "RESCHEDULE_TC",
                    icon: "RESCHEDULE",
                    url: `curefit://rescheduleTc?parentBookingId=${consultationOrderResponse.bookingId}&isReschedule=true`,
                    meta: {
                        "tcBookingId": consultationOrderResponse.bookingId,
                        "centerId": consultationOrderResponse.center.id,
                        "productId": booking.booking.productCode,
                        "patientId": consultationOrderResponse.patient.id
                    }
                })
            }
        }
        return actions
    }

    private getCareTeamActions(userContext: UserContext, patientId: number, item: CareTeam): Action[] {
        const actions: Action[] = []
        const chatAction = this.getChatAction(userContext, item)
        chatAction && actions.push(chatAction)
        if (!_.isEmpty(item.consultationSellableProductUserProductInfos) && !_.isEmpty(item.consultationSellableProductUserProductInfos[0].userMembershipInfos)) {
            const parentBookingId = item.consultationSellableProductUserProductInfos[0].userMembershipInfos[0].bookingId
            actions.push(CareUtil.getBookinConsultationAction(userContext, parentBookingId, patientId, <DiagnosticProductResponse[]>item.consultationSellableProductUserProductInfos.map(product => product.baseSellableProduct)))
        }
        return actions
    }

    private getChatAction(userContext: UserContext, item: CareTeam) {
        if (item && item.chatActionWithContext && item.chatActionWithContext.action && item.chatActionWithContext.action.actionPermitted) {
            const chatAction: any = CareUtil.getChatMessageAction(
                userContext,
                _.get(item, "chatActionWithContext", null),
                item.patientId,
                item.doctor.name,
                _.get(item, "chatActionWithContext.context.twilioCommunicationMode.modeName", null),
                item.doctor.displayImage,
                item.doctor.qualification,
            )
            if (chatAction) {
                return chatAction
            }
        }
        return undefined
    }

    private getCareTeamActionsV2(userContext: UserContext, patientId: number, item: CareTeam): Action[] {
        if (!_.isEmpty(item.consultationSellableProductUserProductInfos) && !_.isEmpty(item.consultationSellableProductUserProductInfos[0].userMembershipInfos)) {
            const parentBookingId = item.consultationSellableProductUserProductInfos[0].userMembershipInfos[0].bookingId
            const actions = CareUtil.getBookinConsultationActionV2(userContext, parentBookingId, patientId, <DiagnosticProductResponse[]>item.consultationSellableProductUserProductInfos.map(product => product.baseSellableProduct))
            if (actions) {
                return [...actions]
            }
        }
        return []
    }

    private labtestWidget(patientId: number, recommendedTests: RecommendLabTestResponse, title: string, bookingId: number, userContext: UserContext): LabTestListingWidget {
        const testCodes: string[] = []
        const header: Header = {
            title: title,
            color: "#000000"
        }
        const items: InfoCard[] = []
        recommendedTests.labtestPrescriptionInfos.map((recommendedTest: any) => {
            items.push({
                subTitle: recommendedTest.name
            })
            testCodes.push(recommendedTest.code)
        })
        let url = CareUtil.getTestListingActionUrl(patientId, bookingId)
        const testCodePresent = !_.isEmpty(testCodes)
        if (testCodePresent) {
            url += `&testCodes=${testCodes.join(",")}`
        }
        const footer: ActionCard = userContext.userProfile.cityId === "Bangalore" && testCodePresent ? {
            action: url,
            title: "BOOK TESTS"
        } : undefined
        const labTestWidget = new ProductListWidget("BULLET", header, items, footer)
        const headers: Header = {
            title: "Tests",
            color: "#000000"
        }
        return new LabTestListingWidget(headers, [labTestWidget], true)
    }

    private getPendingConsultations(userContext: UserContext, bookingInfo: BookingDetail): CareProductStateWidget {
        const bundleBookingInfo = bookingInfo.childBookingInfos.find(info => info.booking.subCategoryCode === "MP_V2_OT")
        let widget: CareProductStateWidget
        if (bundleBookingInfo && bundleBookingInfo.stepInfosV2) {
            const stepInfosV2  =  bundleBookingInfo.stepInfosV2.filter(stepInfos => stepInfos.stepInfoType === "CONSULTATION")
            if (stepInfosV2) {
                stepInfosV2.map(stepInfo => {
                    if (stepInfo && stepInfo.consultationBookingInfos && stepInfo.consultationBookingInfos.length === 1) {
                        const doneconsultationBookingInfos = stepInfo.consultationBookingInfos
                        const isGPPending = doneconsultationBookingInfos[0].consultationOrderResponse.doctor.type !== "DOCTOR"
                        const action = this.getPendingConsultationAction(userContext, stepInfosV2, bookingInfo.booking.patientId)
                        const steps: StepStateCard = {
                            state: "STARTED",
                            viewType: "ACTIONABLE_V2",
                            views: [
                                {
                                    title: isGPPending ? "Schedule 60 min Consultation with Doctor for in depth diagnosis and evaluation of your current medical condition" : "Schedule 60 min Consultation with Health Coach for diet & lifestyle evaluation",
                                    action: action ? action : undefined
                                }
                            ]
                        }
                        const stateItem: ProductStateItem = {
                            header: undefined,
                            title: isGPPending ? "Meet Personal Doctor" : "Meet Personal Health Coach",
                            gradientColors: isGPPending ? ["#fb8676", "#f64cab"] : ["#17d8e5", "#ac9aff"],
                            dividerGradient: ["#5f5f5f", "#d8d8d8"],
                            icon: isGPPending ? "doctor" : "nutritionist",
                            states: [steps],
                            isExpanded: true
                        }
                        const gradientContent = {
                            gradientColors: ["rgb(241, 245, 255)", "rgb(255, 255, 255)"],
                            topHighLightText: "Pending",
                        }
                        widget = new CareProductStateWidget([stateItem], [0], true, gradientContent, true)
                        widget.hasDividerBelow = true
                    }
                })
            }
        }
        if (widget) {
            return widget
        } else {
            return undefined
        }
    }

    private getPendingConsultationAction(userContext: UserContext, stepInfosV2: BundleSetupInfoV2[], patientId: number): any {
        if (stepInfosV2) {
           let bookingAction: Action
            stepInfosV2.map(stepInfo => {
                const bookedProductCodes: string[] = []
                stepInfo.consultationBookingInfos.map(bookingInfo => {
                    bookedProductCodes.push(bookingInfo.booking.productCode)
                })
                const productCodes = stepInfo.consultationSellableProducts.map(product => product.productCode).filter(code => bookedProductCodes.indexOf(code) === -1)
                const action: Action = {
                    title: "BOOK CONSULTATION",
                    actionType: "NAVIGATION",
                    url: `curefit://selectCareDateV1?productId=${productCodes[0]}&patientId=${patientId}&parentBookingId=${stepInfo.parentBookingId}`
                }
                bookingAction = CareUtil.getInstructionModalAction(userContext, action, action.title, CareUtil.getInstructionsForMPV2ConsultationAppointments())
            })
            return bookingAction
        }
    }
}

export default ManagedPlanBookingInfoProductPageView
