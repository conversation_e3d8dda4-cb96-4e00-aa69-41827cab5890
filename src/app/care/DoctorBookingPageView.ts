import { InstructionsWidget } from "./../common/views/WidgetView"
import { IBreadCrumb, WidgetView } from "@curefit/apps-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { OfferUtil } from "@curefit/base-utils"
import { ConsultationProduct, Doctor, Patient } from "@curefit/care-common"
import { OfferV2, PackOffersResponse } from "@curefit/offer-common"
import { ProductPrice } from "@curefit/product-common"
import { UserContext } from "@curefit/userinfo-common"
import { titleCase, Timezone, TimeUtil } from "@curefit/util-common"
import { IBlogData, ISEOData, Orientation } from "@curefit/vm-common"
import { Action } from "@curefit/vm-models"
import * as _ from "lodash"
import * as momentTz from "moment-timezone"
import { DoctorProfileBenefitsInfoWidget, getOffersWidget, MobileBreadCrumbWidget } from "../common/views/WidgetView"
import AppUtil from "../util/AppUtil"
import { CareUtil } from "../util/CareUtil"
import { IDoctorAvailableSlot } from "./CareDoctorSearchPageView"
import { AgentFilterInfos } from "@curefit/albus-client"
const DO_NOT_SHOW_BANNER_DOCTOR_TYPE = ["PHYSIOTHERAPIST", "AYURVEDA", "LC", "PSYCHIATRIST", "COVID_SPECIALIST_PHYSICIAN_WITH_FOLLOWUPS", "COVID_SPECIALIST_PHYSICIAN_WITH_FOLLOW_UPS"]
class DoctorBookingPageView {
    public doctorDetails: Doctor
    public widgets?: WidgetView[] = []
    public actions: Action[]
    public pageImage: string
    constructor(
        userContext: UserContext,
        doctor: Doctor,
        patientsList: Patient[],
        offer: PackOffersResponse | { [key: string]: PackOffersResponse},
        productList: {title: string, action: any}[],
        product: ConsultationProduct,
        patientId?: number,
        parentBookingId?: number,
        isDoctorSearch?: boolean,
        centerId?: string,
        pageAction?: any,
        isDoctorPriceAvailable?: boolean,
        isPTFlow?: boolean,
        categoryFilters?: AgentFilterInfos[],
        showPrice?: boolean,
        careSeoData?: ISEOData
    ) {
        let centerOfferDetail: {
            price: ProductPrice;
            offers: OfferV2[];
        } = undefined
        let priceDetails: ProductPrice = undefined
        const userAgent = userContext.sessionInfo.userAgent
        const isCareDoctor = product && !(
            CareUtil.isMindDoctorType(product.doctorType) ||
            CareUtil.isPTDoctorType(product.doctorType) ||
            CareUtil.isLivePTDoctorType(product.doctorType) ||
            DO_NOT_SHOW_BANNER_DOCTOR_TYPE.includes(product.doctorType))
        const isAlaCarteBooking = _.isNil(parentBookingId) || showPrice
        if (isAlaCarteBooking && !_.isEmpty(offer)) {
            const details = this.getPriceInfo(product, doctor, offer, isDoctorPriceAvailable, centerId)
            priceDetails = details.priceDetails
            centerOfferDetail = details.centerOfferDetail
        }
        if (pageAction) {
            this.actions = [pageAction]
        } else {
            this.actions = this.getAction(
                userContext,
                doctor,
                productList,
                patientsList,
                product,
                patientId,
                parentBookingId,
                isDoctorSearch,
                centerId,
                isPTFlow,
                priceDetails
            )
        }

        const doctorDetailWidget = this.getDoctorDetailWidget(userContext, doctor, priceDetails, product, centerId, isCareDoctor && isAlaCarteBooking, categoryFilters)
        this.doctorDetails = AppUtil.isFromFlutterAppFlow(userContext) ? null : doctorDetailWidget

        const offerWidet =  !_.isEmpty(centerOfferDetail?.offers) && (product && product.doctorType !== "LC") ? CareUtil.getCareOfferWidget("OFFERS APPLIED", centerOfferDetail.offers, userContext, true, undefined, true)
        : null
        const isWeb = ["DESKTOP", "MBROWSER"].includes(userAgent)
        const bannerWidget =  isCareDoctor && isAlaCarteBooking ? this.getConsultationInfoBanner() : null
        const doctorProfileBenefitsWidget = isCareDoctor && isAlaCarteBooking && isWeb && this.getDoctorProfileBenefitsWidget(userAgent)
        const careBlogWidget = this.getCareBlogWidget(careSeoData)
        this.pageImage = product.heroImageUrl || product.imageUrl

        switch (userContext.sessionInfo.userAgent) {
            case "DESKTOP": {
                this.widgets = [
                    this.getTCSummaryWidget(userContext, product, priceDetails, doctor, undefined),
                    // To do undo this when web supports
                    doctorDetailWidget,
                    doctorProfileBenefitsWidget,
                    offerWidet,
                    careBlogWidget
                ]
                break
            }
            case "MBROWSER": {
                this.widgets = [
                    doctorDetailWidget,
                    doctorProfileBenefitsWidget,
                    offerWidet,
                    careBlogWidget
                ]
                break
            }
            case "APP": {
                if (AppUtil.isFromFlutterAppFlow(userContext)) {
                    this.widgets = [
                        doctorDetailWidget,
                        // this.getConsultationBenefitInfo(userContext, product, isAlaCarteBooking),
                        offerWidet
                    ]
                } else {
                    this.widgets = [
                        doctorDetailWidget,
                        bannerWidget,
                        offerWidet
                    ]
                }

            }
        }
        this.widgets = this.widgets && this.widgets.filter(Boolean)
    }

    private getAction(
        userContext: UserContext,
        doctor: Doctor,
        productList: {title: string, action: any}[],
        patientsList: Patient[],
        product?: ConsultationProduct,
        patientId?: number,
        parentBookingId?: number,
        isDoctorSearch?: boolean,
        centerId?: string,
        isPTFlow?: boolean,
        priceDetails?: ProductPrice
    ): any[] {
        let actions = []
        const finalCenterId = centerId || doctor?.earliestAvailability?.centerId || _.get(doctor, "doctorCenterMapping[0].centerId")
        if (AppUtil.isFromFlutterAppFlow(userContext) && product) {
            let doctorAvailableSlots: IDoctorAvailableSlot[] = []
            if (!_.isEmpty(doctor?.earliestAvailabilityList)) {
                doctorAvailableSlots = CareUtil.getDoctorEarliestAvailabilitySlotAction(userContext, doctor, product.productId, patientId, parentBookingId, product, Number(finalCenterId), false, true)
            }

            // hack to fix UI price for flutter app where mrp is shown to users
            if (AppUtil.isTherapyFlutterDoctorProfilePriceFixNotSupported(userContext) && priceDetails) {
                priceDetails.mrp = priceDetails.listingPrice
            }

            actions.push({
                leftText: CareUtil.isLiveWorkoutConsultationDoctorType(product.doctorType) ? undefined : this.getActionAvailabilityText(doctor, userContext.userProfile.timezone),
                priceDetails,
                slots: doctorAvailableSlots,
                actionQueryParam: `&doctorId=${doctor.id}&centerId=${finalCenterId}`,
                slotSelectionAction: CareUtil.getDoctorFooterCardAction(userContext, product, patientsList, {
                    patientId,
                    parentBookingId,
                    productId: product.productId,
                    isDoctorSearch
                }),
            })
        } else if (!userContext.sessionInfo.isUserLoggedIn) {
            actions.push(CareUtil.getLoginAlertAction())
        } else if (AppUtil.isPsyMultiPatientSupported(userContext) && CareUtil.isPsychiatry(product.doctorType)) {
            const doctorAction = CareUtil.consultationCheckoutAction(userContext, product, patientsList, undefined, doctor.id, patientId, finalCenterId, parentBookingId)
            if (patientId) {
                actions.push({
                    ...CareUtil.checkPsyConsentAction(doctorAction, doctor.id),
                    title: "BOOK APPOINTMENT"
                })
            } else {
                actions.push({
                    ...doctorAction,
                    meta: {
                        ...doctorAction?.meta,
                        action: CareUtil.checkPsyConsentAction(doctorAction?.meta?.action, doctor.id)
                    },
                    title: "BOOK APPOINTMENT"
                })
            }
        } else if (patientId && isDoctorSearch && product && parentBookingId) {
            actions.push(CareUtil.consultationCheckoutAction(userContext, product, patientsList, undefined, doctor.id, patientId, finalCenterId, parentBookingId))
        } else if (!_.isEmpty(productList)) {
            if (productList.length === 1) {
                actions.push({...productList[0], title: "Schedule appointment"})
            } else {
                actions.push({
                    title: "Schedule appointment",
                    actionType: "ACTION_LIST",
                    actions: productList
                })
            }
        } else if (product) {
            actions.push(CareUtil.consultationCheckoutAction(userContext, product, patientsList, undefined, doctor.id, patientId, finalCenterId, parentBookingId))
        }
        if (!isPTFlow && !doctor.earliestAvailability) {
            actions = actions.map(item => ({
                ...item,
                disabled: true
            }))
        }
        return actions
    }

    private getDoctorProfileBenefitsWidget(userAgent: UserAgent
    ): any {
        const doctorBenefitsProfileInfoWidget: DoctorProfileBenefitsInfoWidget = {
            widgetType: "DOCTOR_PROFILE_BENEFITS_INFO_WIDGET",
            image: ""
        }

        if (userAgent === "MBROWSER") {
            doctorBenefitsProfileInfoWidget.image =
                "image/carefit/consultation_benefit_banner_app.png"
        } else {
            doctorBenefitsProfileInfoWidget.image =
                "image/carefit/consultation_benefit_banner_web.png"
        }
        return doctorBenefitsProfileInfoWidget
    }

    private getCareBlogWidget(careSeoData?: ISEOData) {
        if (_.isEmpty(careSeoData)) {
          return
        }
        const careBlogWidget: WidgetView & {
            widgetType: string
            blogData: IBlogData[],
            widgetTitle: string
            showDivider: boolean
        } = {
            widgetType: "CARE_BLOG_WIDGET",
            blogData: careSeoData.data,
            widgetTitle: careSeoData.widgetTitle,
            showDivider: true
        }
        return careBlogWidget
    }

    private getDoctorDetailWidget(userContext: UserContext, doctor: Doctor, priceInfo?: ProductPrice, product?: ConsultationProduct, centerId?: string, isBannerPresent?: boolean, categoryFilters?: AgentFilterInfos[]): any {
        if (doctor) {
            const isFromFlutterAppFlow = AppUtil.isFromFlutterAppFlow(userContext)
            const subSpecialities = CareUtil.getDoctorSubSpecialityValue(categoryFilters, doctor, isFromFlutterAppFlow)
            const cuisineSpecialities = CareUtil.getNutritionistCuisineSpecialityValue(categoryFilters, doctor, isFromFlutterAppFlow)
            let doctorCenterMapping: string | any[] = []
            const timezone = userContext.userProfile.timezone
            if (centerId) {
                const doctorCenterMappingItem: any = doctor.doctorCenterMapping.find(item => item.centerId.toString() === centerId)
                if (doctorCenterMappingItem && doctorCenterMappingItem.centerName) {
                    doctorCenterMapping = [{
                        name: `${doctorCenterMappingItem.centerName}` || undefined,
                            placeUrl: doctorCenterMappingItem.placeUrl || undefined,
                            mapText: product && product.consultationMode === "INCENTRE" && doctorCenterMappingItem.placeUrl ? "VIEW MAP" : " "
                    }]
                }
            }
            if  (doctorCenterMapping && doctorCenterMapping.length === 0) {
                doctorCenterMapping = doctor.doctorCenterMapping && doctor.doctorCenterMapping.length ? doctor.doctorCenterMapping.map((doctorCenterItem: any) => {
                    if (doctorCenterItem && doctorCenterItem.centerName) {
                        return {
                            name: `${doctorCenterItem.centerName}` || undefined,
                            placeUrl: doctorCenterItem.placeUrl || undefined,
                            mapText: product && product.consultationMode === "INCENTRE" && doctorCenterItem.placeUrl ? "VIEW MAP" : " "
                        }
                    }
                }).filter((item: any) => !!item) : []
            }
            let doctorTypeInfoText = product && titleCase(CareUtil.getDoctorText(product.doctorType))
            const isMindDoctor = CareUtil.isMindDoctorType(doctor.primarySubServiceType.code)
            if (isMindDoctor && !_.isEmpty(doctor.doctorTypes)) {
                doctor.doctorTypes.forEach(doctorType => {
                    if (doctorType.type && doctorType.type.code === "PSYCHIATRIST") {
                        doctorTypeInfoText = doctorType.type.displayValue
                    }
                })
            }
            const isDontShowDoctorCenterProducts = product.doctorType === "LC" || CareUtil.isLivePTDoctorType(product.doctorType) || CareUtil.isTherapyOnlyDoctorType(product.doctorType)
            CareUtil.removeDoctorUnusedField(doctor)
            return {
                ...doctor,
                price: priceInfo?.listingPrice,
                priceInfo : priceInfo,
                languagesKnownInfo: isFromFlutterAppFlow ?  doctor.languagesSpoken.map(item => titleCase(item)) : this.getCommaSeperatedText(doctor.languagesSpoken, "Speaks"),
                specializedCuisines: cuisineSpecialities ? `Cuisines: ${cuisineSpecialities}` : undefined,
                registrationNumber: product.doctorType === "LC" || CareUtil.isLivePTDoctorType(product.doctorType) || CareUtil.isLiveSGTProduct(product) || CareUtil.isTherapyOnlyDoctorType(product.doctorType) ? undefined : doctor.registrationNumber,
                availabilityInfo: CareUtil.isLiveWorkoutConsultationDoctorType(product.doctorType) ? undefined : this.getAvailabilityText(doctor, timezone),
                doctorTypeInfoText,
                experience: doctor.primarySubServiceType && CareUtil.isTherapyOnlyDoctorType(doctor.primarySubServiceType.code) ? undefined : doctor?.experience,
                doctorCenterMapping: isDontShowDoctorCenterProducts ? undefined : doctorCenterMapping,
                centerHeader: isDontShowDoctorCenterProducts ? undefined : (doctorCenterMapping.length > 1 ? "AVAILABLE AT" : undefined),
                doctorInfoText: "ABOUT THE EXPERT",
                action: this.actions,
                isBannerPresent,
                subSpecialities: subSpecialities,
                subSpecialitiesInfoText: subSpecialities ? `Specializes in ${subSpecialities}` : undefined,
                widgetType: "DOCTOR_BOOKING_DETAIL_WIDGET",
                shareAction: this.getShareAction(userContext, doctor, product)
            }
        }  else {
            return undefined
        }
    }

    getTCSummaryWidget(userContext: UserContext, product: ConsultationProduct, price: ProductPrice, doctor: Doctor, offerIds?: string[]) {
        const isDesktop = AppUtil.isDesktop(userContext)

        const tcSummaryWidget: WidgetView & {
            productId: string
            title: string,
            subTitle: string,
            price: ProductPrice,
            image: string,
            offerId: string,
            offerIds: string[],
            breadcrumb?: IBreadCrumb[],
            orientation?: Orientation
            preventWidgetImageDisplay: boolean
        } = {
            widgetType: "TELECONSULTATION_SUMMARY_WIDGET",
            title: product.title,
            productId: product.productId,
            price: price ? price : undefined,
            image: product.heroImageUrl,
            subTitle: undefined,
            offerId: !_.isEmpty(offerIds) ? offerIds[0] : undefined,
            offerIds: !_.isEmpty(offerIds) ? offerIds : [],
            breadcrumb: [],
            preventWidgetImageDisplay: isDesktop
        }
        return tcSummaryWidget
    }

    private getConsultationInfoBanner(): any {
        return {
            widgetType: "BANNER_CAROUSEL_WIDGET",
            maxNumBanners: 1,
            edgeToEdge: true,
            showDivider: false,
            bannerRatio: "345:70",
            backgroundColor: "",
            data: [
              {
                contentMetric: {
                  contentId: "Care fit consultation offer tc"
                },
                action: undefined,
                image: "/image/carefit/consultation_benefit_banner_app.png",
                mwebImage: "image/carefit/consultation_benefit_banner_app.png",
                webImage: "image/carefit/consultation_benefit_banner_web.png"
              }
            ],
            layoutProps: {
              noVerticalPadding: true,
              interContentSpacing: 0,
              autoScroll: false,
              roundedCorners: false,
              edgeToEdge: true,
              backgroundColor: "",
              bannerHeight: 70,
              bannerWidth: 375,
              bannerOriginalHeight: 210,
              bannerOriginalWidth: 1125,
              originalHeight: 480,
              originalWidth: 480,
              alignment: "center",
              showPagination: false,
              useShadow: false,
              v2: false
            },
            widgetMetric: {
              widgetId: "Consultation-Benefit-Banner",
              widgetName: "Consultation Benefit Banner",
              widgetType: "BANNER_CAROUSEL_WIDGET"
            }
          }
    }

    private getConsultationBenefitInfo(userContext: UserContext, product: ConsultationProduct, isAlaCarteBooking: boolean): any {
        if (!isAlaCarteBooking) {
            return null
        }
        if (CareUtil.isMindDoctorType(product.doctorType) ) {
            return new InstructionsWidget([], userContext.sessionInfo.userAgent, true, [
                {
                    title: undefined,
                    priority: 0,
                    instructionResponseList: [
                        {
                            iconURL: "image/vm/cb77fdff-46a6-433d-93d2-2785b3e0d6a1.png",
                            text: "Get digital prescription",
                        },
                        {
                            iconURL: "image/vm/cb77fdff-46a6-433d-93d2-2785b3e0d6a1.png",
                            text: "Chat with therapist and share reports from booking to 15 days post sessions",
                        },
                        // {
                        //     iconURL: "image/vm/cb77fdff-46a6-433d-93d2-2785b3e0d6a1.png",
                        //     text: "Free follow-up in 8 days",
                        // }
                    ]
                }
            ], true, {title: "Benefit"})
        }
    }

    private getPriceInfo(
        product: ConsultationProduct,
        doctor: Doctor,
        offer: PackOffersResponse | { [key: string]: PackOffersResponse},
        isDoctorPriceAvailable: boolean,
        centerId?: string
    ) {
        let centerOfferDetail: {
            price: ProductPrice;
            offers: OfferV2[];
        } = undefined
        let priceDetails: ProductPrice = undefined
        const isLCorMindorCovidProduct = product && (product.doctorType === "LC" || CareUtil.isTherapyOnlyDoctorType(product.doctorType) || CareUtil.isCovidSpecialist(product))
        const finalCenterId = centerId || _.get(doctor, "doctorCenterMapping[0].centerId", undefined)
        if (isDoctorPriceAvailable || CareUtil.isTherapyOnlyDoctorType(product.doctorType)) {
            centerOfferDetail = OfferUtil.getPackOfferAndPrice(product,  offer as PackOffersResponse)
            const doctorCenterMapping = doctor.doctorCenterMapping.find(item => item.centerId == finalCenterId)
            priceDetails = centerOfferDetail?.price ? (isLCorMindorCovidProduct ? centerOfferDetail?.price : {
                listingPrice: doctorCenterMapping?.price,
                mrp: doctorCenterMapping?.price,
                currency: "INR"
            }) : undefined
        } else {
            const OfferDetail = OfferUtil.getOfferAndPriceForCareCenter(product,  [finalCenterId], offer as { [key: string]: PackOffersResponse})
            centerOfferDetail = OfferDetail[finalCenterId]
            priceDetails = centerOfferDetail?.price ? (isLCorMindorCovidProduct ? centerOfferDetail?.price : {
                listingPrice: centerOfferDetail?.price?.mrp,
                mrp: centerOfferDetail?.price?.mrp,
                currency: centerOfferDetail?.price?.currency
            }) : undefined
        }
        return { centerOfferDetail, priceDetails }
    }

    private getAvailabilityText(doctor: Doctor, timezone: Timezone) {
        let availabilityInfo
        if (doctor.earliestAvailability) {
            availabilityInfo = momentTz.tz(momentTz.tz(doctor.earliestAvailability.startTime, timezone), timezone).calendar(null, {
                lastDay: "[]",
                sameDay: "[Next available slot:] hh:mm A",
                nextDay: "[Next available tomorrow]",
                lastWeek: "[Next available] DD MMM",
                nextWeek: "[Next available] DD MMM",
                sameElse: "[Available] DD MMM"
            })
        } else {
            availabilityInfo = "No slots available"
        }
        return availabilityInfo
    }

    private getActionAvailabilityText(doctor: Doctor, timezone: Timezone) {
        let availabilityInfo
        if (doctor.earliestAvailability) {
            availabilityInfo = momentTz.tz(momentTz.tz(doctor.earliestAvailability.startTime, timezone), timezone).calendar(null, {
                lastDay: "[]",
                sameDay: "[]",
                nextDay: "[Available tomorrow]",
                lastWeek: "[Available] DD MMM",
                nextWeek: "[Available] DD MMM",
                sameElse: "[Available] DD MMM"
            })
        } else {
            availabilityInfo = "No slots available"
        }
        return availabilityInfo
    }

    private getCommaSeperatedText(values: string[], prefix?: string) {
        if (!_.isEmpty(values)) {
            const languageText = values.map(item => titleCase(item)).join(", ")
            return languageText ? (prefix ? `${prefix} ${languageText}` : languageText) : undefined
        }
        return undefined
    }

    private getShareAction(userContext: UserContext, doctor: Doctor, product: ConsultationProduct) {
        if (product.tenant !== "CULTFIT") {
            const url = ( ["PRODUCTION", "ALPHA"].includes(process.env.ENVIRONMENT) ? "https://www.cult.fit" : "https://stage.cult.fit" ) + CareUtil.getDoctorBookingScreenAction("Book", {
                slugValue: doctor.seoSlugValue || doctor.slugValue,
                productId: product.productId,
                doctorType: product?.doctorType || doctor?.primarySubServiceType?.code,
                appsource: userContext.sessionInfo.appSource
            }, userContext, true).url + "&sharedLink=true"
            const serviceType = doctor.primarySubServiceType
            let message = doctor.name || ""
            if (serviceType && serviceType.displayValue) {
                message += `\n${serviceType.displayValue}`
            }
            if (doctor.experience && !CareUtil.isTherapyOnlyDoctorType(product.doctorType)) {
                message += `\n${doctor.experience} years experience`
            }
            message += `\nBook Appointment on Care.fit - ${url || ""}`
            let image = "https://static.cure.fit/assets/images/social/social-share.jpg"
            if (doctor.displayImage) {
                image = "https://cdn-images.cure.fit/www-curefit-com/image/upload" + doctor.displayImage
            } else if (product.imageUrl) {
                image = "https://cdn-images.cure.fit/www-curefit-com/image/upload" + product.imageUrl
            }
            return {
                actionType: "SHARE_ACTION",
                title: "SHARE",
                meta: {
                    shareOptions: {
                        // type: "image/png",
                        // url: image,
                        message: message,
                        title: doctor?.primarySubServiceType?.displayValue
                    },
                    analyticsData: {
                        eventKey: "widget_click",
                        eventData: {
                            actionType: "DOCTOR_PROFILE_SHARE",
                            productCode: product.productId,
                            doctorId: doctor.id
                        }
                    }
                }
            }
        }
    }

}

export default DoctorBookingPageView
