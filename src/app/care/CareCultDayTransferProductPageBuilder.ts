import { Action, InfoCard, WidgetView } from "@curefit/apps-common"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { CULT_CLIENT_TYPES, ICultService } from "@curefit/cult-client"
import { ErrorFactory } from "@curefit/error-client"
import { IOfferServiceV2, OFFER_SERVICE_CLIENT_TYPES } from "@curefit/offer-service-client"
import { TimeUtil } from "@curefit/util-common"
import { Orientation, UserContext } from "@curefit/vm-models"
import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { ActionCell, GradientCard, Header, PricingWidgetRecurringValue, ProductDetailPage, ProductListWidget, ProductPricingSection, SinglesOrderConfirmationWidget } from "../common/views/WidgetView"
import { ErrorCodes } from "../error/ErrorCodes"
import BookingConfirmationPageConfig from "../order/BookingConfirmationPageConfig"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import AppUtil from "../util/AppUtil"
import { CacheHelper } from "../util/CacheHelper"
import CareUtil from "../util/CareUtil"


import _ = require("lodash")


interface ProductDetail {
    productId: string,
    title: string,
    subTitle: string,
    cultDays: number,
    image: string,
    ctaTitle: string,
    ctaSubtitle: string,
    offerId: string,
    confirmationTitle: string,
    actionCells: ActionCell[],
    ctaAction: Action
}

@injectable()
export class CareCultDayTransferPageViewBuilder {
    constructor(
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) private offerService: IOfferServiceV2,
        @inject(CULT_CLIENT_TYPES.ICultService) protected cultfitService: ICultService,
        @inject(CULT_CLIENT_TYPES.MindFitService) private mindfitService: ICultService,
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(CUREFIT_API_TYPES.BookingConfirmationPageConfig) protected bookingConfirmationConfig: BookingConfirmationPageConfig
    ) {
    }

    async getProductPage(userContext: UserContext, productId: string, memberShipType: string, selectedProductId?: string) {
        return new CareCultDayTransferProductPage().buildView(userContext, productId, memberShipType, this.productDetails(), selectedProductId)
    }

    async getCultDaysSwapOrderConfirmationPage(userContext: UserContext, productId: string, memberShipType: string = "CULT") {
        if (!userContext.sessionInfo.isUserLoggedIn) {
            throw this.errorFactory.withCode(ErrorCodes.NOT_LOGGED_IN_ERR, 400).build()
        }
        const memberShipService = memberShipType === "MIND" ? this.mindfitService : this.cultfitService
        const userId = userContext.userProfile.userId
        const selectedProduct = this.productDetails().find(item => item.productId === productId)
        let isOfferAppliedSuccess
        const membershipDetails = await this.getUserMemberShipDetails(memberShipService, userContext, selectedProduct.cultDays)
        const referralBannerWidget = await CareUtil.getBannerCarouselWidget(userContext, this.serviceInterfaces, CareUtil.getCareReferalBannerWidgetId())
        if (membershipDetails && membershipDetails.membershipId && membershipDetails.isMember) {
            if (membershipDetails.isEligible) {
                try {

                    this.logger.info(`Calling - Added Offer ${selectedProduct.offerId} to user ${userId}`)
                    await this.offerService.addOfferEligibilityForUser(selectedProduct.offerId, userId, "USERID", 1)
                    this.logger.info(`Success - Added Offer ${selectedProduct.offerId} to user ${userId}`)

                    isOfferAppliedSuccess = true

                    this.logger.info(`Calling - Reduce Membership Days ${membershipDetails.numDaysToDeduct} for user ${userId} with membership id ${membershipDetails.membershipId}`)
                    await memberShipService.reduceDuration(userId, membershipDetails.membershipId.toString(), membershipDetails.numDaysToDeduct, `Membership swap for care product ${productId}`, "CUREFIT")
                    this.logger.info(`Success - Reduce Membership Days ${membershipDetails.numDaysToDeduct} for user ${userId} with membership id ${membershipDetails.membershipId}`)

                } catch (e) {

                    this.logger.error(`Care Cult Swap Failed with following error ${e.message}`)

                    if (isOfferAppliedSuccess) {
                        this.logger.info(`Calling - Remove Offer ${selectedProduct.offerId} to user ${userId}`)
                        await this.offerService.removeOfferEligibilityForUser(selectedProduct.offerId, userId, "USERID", 1)
                        this.logger.info(`Success - Remove Offer ${selectedProduct.offerId} to user ${userId}`)
                    }

                    throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Membership days transfer failed").build()
                }
            } else {
                await AppUtil.timeout(500)
                return this.getNotSwappableErrorResponse()
            }
        } else {
            await AppUtil.timeout(500)
            return this.getNotMemberResponse()
        }
        return new CareCultDaysSwapOrderConfirmationPage().buildView(userContext, selectedProduct, referralBannerWidget)
    }

    private async getUserMemberShipDetails(memberShipService: ICultService, userContext: UserContext, numDaysToDeduct: number): Promise<{isMember: boolean, isEligible: boolean, membershipId?: number, numDaysToDeduct: number}> {
        const memberShipDetails = await memberShipService.activeMembership(userContext.userProfile.userId)
        if (!memberShipDetails || _.isEmpty(memberShipDetails.memberships)) {
            return { isMember: false, isEligible: false, numDaysToDeduct }
        }
        const activeMembership = memberShipDetails.memberships[0]
        const timezone = userContext.userProfile.timezone
        const todaysDate = TimeUtil.todaysDate(timezone)
        const membershipStartDate = activeMembership.startDate
        const startDate = todaysDate > membershipStartDate ? todaysDate : membershipStartDate
        const daysLeft = TimeUtil.diffInDaysReal(timezone, activeMembership.endDate, startDate)   // endDate - startDate
        let pauseDaysPassed = 0
        if (activeMembership.ActivePause) {
            pauseDaysPassed = TimeUtil.diffInDays(timezone, activeMembership.ActivePause.startTime + "", todaysDate)
        }

        if ((daysLeft + pauseDaysPassed) <= numDaysToDeduct) {
            return { isMember: true, isEligible: false, numDaysToDeduct }
        }
        return { isMember: true, isEligible: true, membershipId: activeMembership.id, numDaysToDeduct }
    }

    private productDetails(): ProductDetail[] {
        const isProdEnv = ["PRODUCTION", "ALPHA"].includes(process.env.ENVIRONMENT)
        return [
            {
                productId: "CULT_MEMBERSHIP_TRANSFER_GP99",
                title: "Consult a GP",
                subTitle: "Meet a General Physician",
                cultDays: 2,
                ctaTitle: "Consult General Physician",
                ctaSubtitle: "Swap your cult days and select General Physician consultation. Offer applies on checkout",
                image: "/image/singles/care/tc/Care_Cult_Swap.png",
                offerId: isProdEnv ? "-kLaOTk3z" : "T7_wC13xm",
                confirmationTitle: "Meet General Physician for free",
                ctaAction: {actionType: "NAVIGATION", url: "curefit://carefittc?id=CONS_GP_STANDARD_ONLINE", title: "Consult General Physician" },
                actionCells: [{
                    title: "Consult General Physician",
                    subTitle: "No health issues is too small, meet doctor for your medical issues.",
                    action: {actionType: "NAVIGATION", url: "curefit://carefittc?id=CONS_GP_STANDARD_ONLINE", title: "Consult General Physician" },
                    iconUrl: "/image/icons/referral/consult_steth.png",
                    removeHorizontalPadding: true,
                    hasTopDivider: true,
                    tintColor: "#000000"
                }]
            },
            {
                productId: "CULT_MEMBERSHIP_TRANSFER_SPECIALITIES",
                title: "Consult any Doctor",
                subTitle: "Choose any speciality",
                cultDays: 10,
                ctaTitle: "Consult any Specialist",
                ctaSubtitle: "Swap your cult days, select a speciality and doctor. Offer applies on checkout",
                image: "/image/singles/care/tc/Care_Cult_Swap.png",
                offerId: isProdEnv ? "9odNketBG" : "6iaLW1hIL",
                confirmationTitle: "Meet any doctor for free",
                ctaAction: {actionType: "NAVIGATION", url: "curefit://tabpage?pageId=careclptab&selectedTab=clpconsultation", title: "Consult Doctor" },
                actionCells: [{
                    title: "Consult Doctor Now",
                    subTitle: "Select a doctor from Specialists, Super-Specialists, Surgeons and Physiotherapists",
                    action: {actionType: "NAVIGATION", url: "curefit://tabpage?pageId=careclptab&selectedTab=clpconsultation", title: "Consult Doctor" },
                    iconUrl: "/image/icons/referral/consult_steth.png",
                    removeHorizontalPadding: true,
                    hasTopDivider: true,
                    tintColor: "#000000"
                }]
            }
        ]
    }

    private getNotMemberResponse(): CareCultDaysSwapOrderConfirmationPage {
        return {
            alertInfo:  {
                title: "Something went wrong!",
                subTitle: "Sorry, you do not have a CULT pack",
                actions: [{
                    actionType: "POP_ACTION",
                    title: "Ok"
                }]
            },
            widgets: [],
            actions: []
        } as CareCultDaysSwapOrderConfirmationPage
    }

    private getNotSwappableErrorResponse(): CareCultDaysSwapOrderConfirmationPage {
        return {
            alertInfo:  {
                title: "Something went wrong!",
                subTitle: "Sorry, You don't have enough cult pack days to swap",
                actions: [{
                    actionType: "POP_ACTION",
                    title: "Ok"
                }]
            },
            widgets: [],
            actions: []
        } as CareCultDaysSwapOrderConfirmationPage
    }
}

export class CareCultDayTransferProductPage extends ProductDetailPage {
    async buildView(userContext: UserContext, productId: string, memberShipType: string, allProducts: ProductDetail[], selectedProductId?: string) {
        selectedProductId = selectedProductId || productId
        const selectedProduct = allProducts.find(item => item.productId === selectedProductId)
        this.actions = [
           userContext.sessionInfo.isUserLoggedIn ?  {
            actionType: "SHOW_ALERT_MODAL",
            title: selectedProduct.ctaTitle,
            meta: {
                title: selectedProduct.ctaTitle,
                showCheckoutAction: userContext.sessionInfo.appVersion <= 8.41 ? false : true,
                price: {
                    listingPrice: " ",
                    currency: "dummy"
                },
                subText: `Swap ${selectedProduct.cultDays} Cult Day${selectedProduct.cultDays > 1 ? "s" : ""}`,
                subTitle: selectedProduct.ctaSubtitle,
                actions: [
                    {
                        title: "Confirm",
                        actionType: "NAVIGATION",
                        url: `curefit://carecartcheckout?productId=${selectedProductId}&memberShipType=${memberShipType || "CULT"}`
                    },
                    {
                        title: "Cancel",
                        actionType: "HIDE_ALERT_MODAL"
                    },

                ]
            }
        } : {
            actionType: "SHOW_ALERT_MODAL",
            title: selectedProduct.ctaTitle,
            meta: {
                title: "Login Required!",
                subTitle: "Please login to continue",
                actions: [{ actionType: "LOGOUT", title: "Login" }]
            }
        }
        ]
        this.widgets = [
            this.summaryWidget(userContext, selectedProductId, selectedProduct?.image),
            this.getProductPricingWidget(userContext, selectedProductId, allProducts),
            this.getCareCultTransferHowItWorksWidget(userContext, selectedProductId),
            this.getCareCultTransferKeyBenefitsWidget(userContext, selectedProductId)
        ]

        return this
    }

    private getProductPricingWidget(userContext: UserContext, selectedProductId: string, allProducts: ProductDetail[]): any {
        if (!_.isEmpty(allProducts)) {
            const sections: ProductPricingSection[] = []
            const isPriceSupported = userContext.sessionInfo.appVersion <= 8.35 || userContext.sessionInfo.userAgent !== "APP"
            const recurringSection: PricingWidgetRecurringValue [] = []
            allProducts.map((product: ProductDetail) => {
                let price
                if (isPriceSupported) {
                    price = userContext.sessionInfo.userAgent === "APP" ? {
                        mrp: undefined,
                        listingPrice: undefined,
                        currency: "dummy"
                    } as any : {
                        mrp: " ",
                        listingPrice: " ",
                        currency: "dummy"
                    }
                }
                recurringSection.push({
                    title: product.title,
                    subTitle: product.subTitle,
                    price,
                    priceMeta: `Swap ${product.cultDays} Cult Day${product.cultDays > 1 ? "s" : ""}`,
                    action: {
                        actionType: "NAVIGATION",
                        url: `curefit://carefittc?id=${product.productId}`,
                        meta: {
                            selectedProductId: product.productId
                        }
                    },
                    selected: selectedProductId === product.productId
                })
            })
            if (!_.isEmpty(recurringSection)) {
                sections.push({
                    // title: "CHOOSE A LONGER DURATION PLAN",
                    type: "RECURRING",
                    value: recurringSection
                })
            }
            return {
                widgetType: "PRODUCT_PRICING_WIDGET",
                dividerType: "SMALL",
                hasDividerBelow: false,
                header: {
                    title: "Choose"
                },
                footerText: undefined, // "Trial pack is for 1st time user only. After trial you need to choose 3/6/12 months plan to continue.",
                sections: sections,
                orientation: userContext.sessionInfo.userAgent === "DESKTOP" ? "RIGHT" : undefined
            }
        }
        return null
    }

    private summaryWidget(userContext: UserContext, productId: string, image: string): WidgetView {
        const checkupSummaryWidget: WidgetView & {
            productId: string;
            title: string;
            subTitle: string;
            image: string;
            hasDividerBelow: boolean;
            orientation?: Orientation;
            actions?: Action[]
        } = {
            widgetType: "HEALTHCHECKUP_SUMMARY_WIDGET",
            title: "Free Doctor Consultation",
            subTitle: "Get this offer with your cult days",
            productId,
            image,
            hasDividerBelow: false,
            orientation: userContext.sessionInfo.userAgent === "DESKTOP" ? "RIGHT" : undefined,
            actions: userContext.sessionInfo.userAgent !== "APP" ? this.actions : []
        }
        return checkupSummaryWidget
    }

    public getCareCultTransferHowItWorksWidget(userContext: UserContext, productId: string): ProductListWidget {
        const header: Header = {
            title: "How it works",
            color: "#000000"
        }
        const infoCards: InfoCard[] = [
            {
                icon: "/image/icons/howItWorks/hitButton_1.png",
                subTitle: "Swap your Cult Days"
            },
            {
                icon: "/image/icons/howItWorks/consult_1.png",
                subTitle: productId === "CULT_MEMBERSHIP_TRANSFER_SPECIALITIES"
                    ? "Select the Speciality/Doctor you want to consult"
                    : "Select General Physician and choose a convenient slot"
            },
            {
                icon: "/image/icons/howItWorks/tick.png",
                subTitle: "Offer applies directly on checkout"
            },
            {
                icon: "/image/icons/howItWorks/today_1.png",
                subTitle: "Offer valid for 30 days"
            }
        ]
        const widget = new ProductListWidget("SMALL", header, infoCards, undefined, undefined, userContext.sessionInfo.userAgent === "DESKTOP" ? "RIGHT" : undefined)
        widget.hasDividerBelow = false
        return widget
    }

    public getCareCultTransferKeyBenefitsWidget(userContext: UserContext, productId: string): ProductListWidget {
        const header: Header = {
            title: "Key Benefits",
            color: "#000000"
        }
        const infoCards: GradientCard[] = [
            {
                title: "Immediate Appointments",
                shadowColor: "#76e997",
                "gradientColors": [
                  "#76e997",
                  "#2cc2d3"
                ],
                icon: "onTimeStart"
              },
              {
                title: "Digital Prescriptions",
                shadowColor: "#fb8a72",
                gradientColors: [
                  "#fb8a72",
                  "#f64cac"
                ],
                icon: "digital"
              }
        ]
        if (productId === "CULT_MEMBERSHIP_TRANSFER_SPECIALITIES") {
            infoCards.push({
                title: "20+ Specialities",
                shadowColor: "#17d8e5",
                gradientColors: [
                  "#17d8e5",
                  "#ac9aff"
                ],
                icon: "MP_DIAGNOTIC"
            })
        }
        const widget = new ProductListWidget("GARDIENT_CARD", header, infoCards, undefined, undefined, userContext.sessionInfo.userAgent === "DESKTOP" ? "RIGHT" : undefined)
        widget.hasDividerBelow = false
        return widget
    }

}

export class CareCultDaysSwapOrderConfirmationPage extends ProductDetailPage {
    async buildView(userContext: UserContext, productDetail: ProductDetail, referalBannerWidget?: WidgetView) {
        this.actions = [productDetail.ctaAction]
        this.widgets = [
            { ...this.getOrderCellWidget(productDetail), hasDividerBelow: false }
        ]
        if (!_.isEmpty(referalBannerWidget)) {
            this.widgets.push(referalBannerWidget)
        }
        return this
    }

    private getOrderCellWidget(productDetail: ProductDetail): SinglesOrderConfirmationWidget {
        const orderCellWidget: SinglesOrderConfirmationWidget = {
            widgetType: "SINGLES_ORDER_CONFIRMATION_WIDGET",
            activityType: "CULT_CLASS",
            title: `Swapped ${productDetail.cultDays} Cult Day${productDetail.cultDays > 1 ? "s" : ""}`,
            subTitle: productDetail.confirmationTitle,
            activityName: "Offer Applied!",
            action: productDetail.actionCells[0].action,
            actionCells: productDetail.actionCells
        }
        return orderCellWidget
    }


}