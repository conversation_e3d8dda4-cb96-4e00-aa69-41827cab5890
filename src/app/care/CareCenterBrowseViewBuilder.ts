import { Center, ConsultationProduct } from "@curefit/care-common"
import {
    IOfferServiceV2,
    OFFER_SERVICE_CLIENT_TYPES,
    CareCenterOfferRequestParams
} from "@curefit/offer-service-client"
import { UserContext } from "@curefit/userinfo-common"
import { inject, injectable } from "inversify"
import * as _ from "lodash"
import { Action, WidgetView } from "../common/views/WidgetView"
import {
    CenterWiseSlot,
    CultOfferProductDetailPage,
} from "../cult/CultOfferDetailViewBuilder"
import CareUtil from "../util/CareUtil"
import { OfferUtil } from "@curefit/base-utils"
import { BASE_TYPES, Logger } from "@curefit/base"

export class CareOfferProductDetailPage extends CultOfferProductDetailPage {
    footer?: {
        icon: string,
        title: string,
        subTitle: string,
        containerStyling?: any,
        titleStyling?: any,
        subTitleStyling?: any
    }
}

export interface CareCenterWiseSlot extends CenterWiseSlot {
    isExternal?: boolean
    icon?: string
}
export interface CareCenterWiseSlotWidget extends WidgetView {
    title: string
    centerWiseSlots: CareCenterWiseSlot[]
}

@injectable()
class CareCenterBrowseViewBuilder {

    constructor(@inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) private offerServiceV2: IOfferServiceV2,
    @inject(BASE_TYPES.ILogger) private logger: Logger) {
    }

    async getView(userContext: UserContext, centers: Center[], product: ConsultationProduct): Promise<CareOfferProductDetailPage> {
        const productDetailPage = new CareOfferProductDetailPage()
        let isExternalCenterPresent = false
        const centerIds: string[] = centers && centers.map(center => {
            if (!isExternalCenterPresent && center.isExternal) {
                isExternalCenterPresent = center.isExternal
            }
            return center.id.toString()
        })
        const offerInventoryRequestParams: CareCenterOfferRequestParams = {
            userId: userContext.userProfile.userId,
            cityId: userContext.userProfile.cityId,
            deviceId: userContext.sessionInfo.deviceId,
            productType: product.productType,
            productIds: [product.productId],
            centerIds,
            source: "CUREFIT_APP"
        }
        this.logger.info("CARE::DEBUG attempted to call deprecated getCareCenterOffers", { flow: "CareCenterBrowseViewBuilder", request: offerInventoryRequestParams })
        const offerInventoryMap = {}
        const centersPricing = OfferUtil.getOfferAndPriceForCareCenter(product, centerIds, offerInventoryMap)
        const productPageAction = CareUtil.consultationProductPageAction(userContext, product)
        const centerWiseSlotPromises = _.map(centers, async center => {
            const actionUrl =  `${productPageAction.url}&centerId=${center.id}&canChangeCenter=true`
            const action: Action = {
                title: "SELECT CENTRE",
                actionType: "NAVIGATION",
                url: actionUrl
            }
            const centerWiseSlot: CareCenterWiseSlot = {
                centerId: center.id.toString(),
                centerName: center.name,
                action: actionUrl,
                buttonAction: action,
                centerAddress: center.address,
                price: centersPricing[center.id.toString()].price,
                isExternal: center.isExternal,
                icon: center.isExternal ? "EXTERNAL" : undefined
            }
            return centerWiseSlot
        })
        productDetailPage.header = {
            title: product.title,
            subTitle: product.subTitle,
            description: "Select doctor from"
        }
        productDetailPage.title = "Select doctor from"
        productDetailPage.footer = isExternalCenterPresent ? {
            icon: "EXTERNAL",
            title: "POWERED BY CAREFIT",
            subTitle: "Center certified for quality care and experience by Carefit",
            containerStyling: {
                backgroundColor: "#f3f3f3"
            },
            titleStyling: {},
            subTitleStyling: {}
        } : undefined
        const centerWiseSlots = await Promise.all(centerWiseSlotPromises)
        // since app is not consuming widgets data
        if (userContext.sessionInfo.userAgent === "APP") {
            productDetailPage.centerWiseSlots = centerWiseSlots
        } else {
            productDetailPage.breadcrumb = [{text: "Home", link: "/"}, {text: "Care", link: "/care"}, {text: "Select Consultation Center"}]
            productDetailPage.widgets = [this.getCenterWiseSlotWidget("Select doctor from", centerWiseSlots)]
        }
        return productDetailPage
    }

    public getCenterWiseSlotWidget(title: string, centerWiseSlots: CareCenterWiseSlot[]): CareCenterWiseSlotWidget {
        return {
            widgetType: "CENTER_WISE_SLOT_WIDGET",
            title: title,
            centerWiseSlots: centerWiseSlots
        }
    }

}
export default CareCenterBrowseViewBuilder
