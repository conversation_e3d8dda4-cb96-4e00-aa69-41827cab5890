import { ActiveBundleOrderDetail } from "@curefit/albus-client"
import { Action, WidgetView } from "@curefit/apps-common"
import { Doctor } from "@curefit/care-common"
import { UserContext } from "@curefit/userinfo-common"
import * as _ from "lodash"
import { ChronicCareDoctorDescriptionWidget, ChronicCareDoctorProfileWidget, DoctorWhatsAppChatWidget } from "../common/views/WidgetView"
import AppUtil from "../util/AppUtil"
import { UserMembershipInfo } from "@curefit/albus-client/dist/src/models"

class ChronicCareDoctorBookingPageView {
    public widgets?: WidgetView[] = []
    public actions: Action[]
    constructor(userContext: UserContext, doctor: Doctor, productId: string, bookingInfo?: ActiveBundleOrderDetail, disableBooking?: boolean) {
        const chronicCareDoctorProfileWidget = this.getProfileWidget(doctor)
        this.widgets = [chronicCareDoctorProfileWidget]
        let parentBookingId
        const userMembershipInfos: UserMembershipInfo[] = _.get(bookingInfo, "userMembershipInfos", [])
        const index = _.findIndex(userMembershipInfos, {"productCode": productId})
        if (index === -1 || ((userMembershipInfos[index].tickets - userMembershipInfos[index].ticketsConsumed) <= 0)) {
            parentBookingId = -1
        } else {
            parentBookingId = bookingInfo.bookingId
        }
        if (productId) {
            if (!disableBooking) {
                const centerId = doctor?.doctorCenterMapping?.length > 0 ? doctor?.doctorCenterMapping[0].centerId : 1
                const actionUrl = `curefit://selectCareDateV1?productId=${productId}&centerId=${centerId}&forceCurrentCenter=true&isExternal=true&doctorId=${doctor.id}&patientId=${bookingInfo.patientId}&parentBookingId=${parentBookingId}`
                const scheduleAction: Action = {title: "SCHEDULE" , actionType: "NAVIGATION", url: actionUrl}
                this.actions = [scheduleAction]
                if (productId === "SUGAR002" || productId === "SUGAR003" || productId === "ULTRA002" || productId === "ULTRA003") {
                    const doctorWhatsAppChatWidget = this.getDoctorWhatsAppChatWidget(userContext)
                    this.widgets.push(doctorWhatsAppChatWidget)
                }
            }
        } else {
            const saveDoctorAction: Action = {title: "Select this Doctor" , actionType: "SET_CHRONIC_CARE_DOCTOR" as any, meta: {doctorId: doctor.id}}
            this.actions = [saveDoctorAction]
        }
        const chronicCareDoctorDescriptionWidget = this.getDescriptionWidget(doctor)
        this.widgets.push(chronicCareDoctorDescriptionWidget)
    }

    private getDoctorWhatsAppChatWidget(
        userContext: UserContext): DoctorWhatsAppChatWidget {
        const isUltrafitApp: boolean = AppUtil.isUltraFitApp(userContext)
        const DOC_WHATSAPP_NUMBER = isUltrafitApp ? "919885022351" : "918792514509"
        const subTitleMessage = isUltrafitApp ? "Connect with your Metabolic Expert on Whatsapp" : "Connect with your Diabetes Expert on Whatsapp"
        const action: Action = {actionType: "EXTERNAL_DEEP_LINK",
        url: `https://wa.me/${DOC_WHATSAPP_NUMBER}`, title: "Chat Now" }
        const widget: DoctorWhatsAppChatWidget = {
            widgetType: "DOCTOR_WHATSAPP_CHAT_WIDGET" as any,
            title: "Chat Now",
            subTitle: subTitleMessage,
            chatAction: action,
        }
        return widget
    }

    private getProfileWidget(doctor: Doctor): ChronicCareDoctorProfileWidget {
        const widget: ChronicCareDoctorProfileWidget = {
            widgetType: "CHRONIC_CARE_DOCTOR_PROFILE_WIDGET" as any,
            displayImage: doctor.displayImage,
            name: doctor.name,
            subTitle: ` ${doctor.qualification} ${doctor.experience && doctor.type === "DOCTOR" ? ` • ${doctor.experience} Years of Experience` : ""}`
        }
        return widget
    }

    private getDescriptionWidget(doctor: Doctor): ChronicCareDoctorDescriptionWidget {
        const widget: ChronicCareDoctorDescriptionWidget = {
            widgetType: "CHRONIC_CARE_DOCTOR_DESCRIPTION_WIDGET" as any,
            title: "About",
            qualification: doctor.qualification,
            languages: `Speaks ${this.convertLanguagesFormat(doctor.languagesSpoken).join(", ")}`,
            regNumber: doctor.registrationNumber ? `Registration number: ${doctor.registrationNumber}` : null,
            detailedDescription: doctor.description,
        }
        return widget
    }

    private convertLanguagesFormat( languages: string[]): string[] {
        const result: string[] = []
        for (let i = 0; i < languages.length; i++) {
            const language = languages[i]
            result.push(language[0].toUpperCase() + language.slice(1).toLowerCase())
        }
        return result
    }
}

export default ChronicCareDoctorBookingPageView