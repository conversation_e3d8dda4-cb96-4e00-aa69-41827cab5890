import { inject, injectable } from "inversify"
import { DEFAULT_CACHE_REFRESH_TIME_IN_SECONDS, InMemoryCacheService } from "@curefit/memory-cache"
import { IPageConfigReadOnlyDao, IPageConfigReadWriteDao, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import * as _ from "lodash"
import { UrlPathBuilder } from "@curefit/product-common"
import { HowItWorksItem, InstructionItem, OfferingItem, WhatsInThePackItem } from "@curefit/product-common"
import { CancellationPolicyInfo } from "../therapy/TherapyPageConfig"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

@injectable()
class TeleconsultationDetailsPageConfig extends InMemoryCacheService<any> {

    constructor(
        @inject(PAGE_CONFIG_TYPES.PageConfigReadOnlySecondaryDao) protected pageConfigDao: IPageConfigReadOnlyDao,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger, 37 * 60)
        this.load("TeleconsultationDetailsPageConfig")
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    loadData(): Promise<any> {
        return this.pageConfigDao.findOne({ pageId: { $eq: "TeleconsultationPageConfig" } }).then(pageConfig => {
            const data = pageConfig.data
            this.howItWorksTitle = data.howItWorksTitle
            this.onlineInstructions = data.onlineInstructions
            this.offlineInstructions = data.offlineInstructions
            this.therapyOnlineInstructions = data.therapyOnlineInstructions
            this.therapyOfflineInstructions = data.therapyOfflineInstructions
            this.ptSessionInstructions = data.ptSessionInstructions
            this.howItWorksItemList = _.map(<HowItWorksItem[]>data.howItWorksItemList, item => {
                return {
                    text: item.text,
                    icon: UrlPathBuilder.getIconPath("HOWITWORKS", item.icon, 1)
                }
            })
            this.whatsInThePackTitle = data.whatsInThePackTitle
            this.whatsInThePackItemList = _.map(<WhatsInThePackItem[]>data.whatsInThePackItemList, item => {
                return {
                    title: item.title,
                    icon: item.icon,
                    shortDescription: item.shortDescription,
                    description: item.description,
                    shadowColor: item.shadowColor,
                    gradientColors: item.gradientColors
                }
            })

            this.offeringItemTitle = data.offeringItemTitle
            this.offeringItemList = _.map(<OfferingItem[]>data.offeringItemList, item => {
                return {
                    title: item.title,
                    icon: item.icon,
                    types: item.types,
                    description: item.description,
                    productCodes: item.productCodes
                }
            })

            this.lcHowItWorksItems = _.map(<HowItWorksItem[]>data.lcHowItWorksItems, item => {
                return {
                    text: item.text,
                    icon: UrlPathBuilder.getIconPath("HOWITWORKS", item.icon, 1)
                }
            })
            this.lcWhatsInThePackItemList = data.lcWhatsInThePackItemList
            this.lcBenefitsTitle = data.lcBenefitsTitle
            this.lcBenefitsDescription = data.lcBenefitsDescription
            this.lcBenefitsItems = data.lcBenefitsItems
            this.ptCancellationPolicyInfo = data.ptCancellationPolicyInfo
            return data
        })
    }

    public howItWorksTitle: string
    public howItWorksItemList: HowItWorksItem[]
    public whatsInThePackTitle: string
    public whatsInThePackItemList: WhatsInThePackItem[]
    public offeringItemTitle: string
    public offeringItemList: OfferingItem[]
    public onlineInstructions: InstructionItem[]
    public offlineInstructions: InstructionItem[]
    public therapyOnlineInstructions: InstructionItem[]
    public therapyOfflineInstructions: InstructionItem[]
    public ptSessionInstructions: InstructionItem[]

    public lcHowItWorksItems: HowItWorksItem[]
    public lcWhatsInThePackItemList: WhatsInThePackItem[]
    public lcBenefitsTitle: string
    public lcBenefitsDescription: string
    public lcBenefitsItems: string[]
    public ptCancellationPolicyInfo: CancellationPolicyInfo
}

export default TeleconsultationDetailsPageConfig
