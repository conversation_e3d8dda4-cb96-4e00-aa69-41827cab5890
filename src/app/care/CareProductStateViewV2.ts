import { CareProductStateWidget, ProductStateItem, StateView, STEP_STATE, StepStateCard } from "../page/PageWidgets"
import {
    BookingDetail,
    BundleSetupInfoV2,
    ConsultationOrderResponse,
    DiagnosticReportInfo,
    DiagnosticsTestOrderResponse,
    StepInfo,
    SUB_CATEGORY_CODE
} from "@curefit/albus-client"
import CareUtil, { CARE_STATE_ITEM_MPV2_SUPPORTED } from "../util/CareUtil"
import * as _ from "lodash"
import { Action } from "../common/views/WidgetView"
import { ActionUtil } from "@curefit/base-utils"
import { UserContext } from "@curefit/userinfo-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { TimeUtil } from "@curefit/util-common"
import { PHLEBO_CALLING_ACTION_SUPPORTED } from "../util/AppUtil"


const dividerGradientDisabled = ["#d8d8d8", "#d8d8d8"]
const dividerGradientEnabled = ["#5f5f5f", "#d8d8d8"]

export class CareProductStateViewV2 {
    public widget: CareProductStateWidget
    public primaryAction: Action

    constructor(userContext: UserContext, subCategoryCode: SUB_CATEGORY_CODE, productId: string, bookingInfo: BookingDetail, reportEstimationTime: any, reportAction?: string) {
        this.widget = this.getCareProductStateWidget(userContext, subCategoryCode, bookingInfo, reportEstimationTime, reportAction)
    }

    private getCareProductStateWidget(userContext: UserContext, subCategoryCode: SUB_CATEGORY_CODE, bookingInfo: BookingDetail, reportEstimationTime: any, reportAction?: string): CareProductStateWidget {
        const expandedItemIndices: number[] = []
        const totalSteps = !_.isEmpty(bookingInfo.stepInfosV2) ? bookingInfo.stepInfosV2.length : 0
        const isMPV2 = subCategoryCode === "MP_V2"
        const isTransform = CareUtil.isTransformTenant(subCategoryCode)

        let hasDividerBelow = false
        const items: (ProductStateItem | ProductStateItem[])[] = _.map(bookingInfo.stepInfosV2, (stepInfo, index) => {
            let stateItem
            switch (stepInfo.stepInfoType) {
                case "DIAGNOSTIC_TEST":
                    stateItem = this.getDiagnosticsStateItem(bookingInfo.booking.patientId, userContext, stepInfo, `STEP 0${index + 1}`, totalSteps, reportEstimationTime, reportAction)
                    if (stateItem.isExpanded) expandedItemIndices.push(index)
                    return stateItem
                case "HEALTH_ASSESSMENT":
                    stateItem = this.getHealthAssessmentStateItem(stepInfo, `STEP 0${index + 1}`, totalSteps)
                    if (stateItem.isExpanded) expandedItemIndices.push(index)
                    return stateItem
                case "CONSULTATION":
                    if (isMPV2) {
                        stateItem = this.getConsultationStateItemMPV2(userContext, subCategoryCode, stepInfo, bookingInfo.booking.patientId, `STEP 0${index + 1}`, totalSteps)
                        if (stateItem[0].isExpanded) expandedItemIndices.push(index)
                        if (stepInfo.stepState === "BOOKED") {
                            hasDividerBelow = true
                        }
                    } else {
                        stateItem = this.getConsultationStateItem(userContext, subCategoryCode, stepInfo, bookingInfo.booking.patientId, `STEP 0${index + 1}`, totalSteps)
                        if (stateItem.isExpanded) expandedItemIndices.push(index)
                    }
                    return stateItem
                case "FORM":
                    stateItem = this.getUserFormStateItem(stepInfo, bookingInfo.booking.patientId, `STEP 0${index + 1}`, totalSteps)
                    expandedItemIndices.push(index)
                    return stateItem
                case "PLAN":
                    stateItem = this.getConsultationStateItem(userContext, subCategoryCode, stepInfo, bookingInfo.booking.patientId, `STEP 0${index + 1}`, totalSteps)
                    if (stateItem.isExpanded) expandedItemIndices.push(index)
                    return stateItem
            }
        })
        const data: ProductStateItem[] = _.flatMap(items)
        const widget = new CareProductStateWidget(isTransform ? [] : data, expandedItemIndices, isMPV2, undefined, isMPV2 ? true : false)
        if (isMPV2) {
            widget.hasDividerBelow = hasDividerBelow
        }
        return widget
    }

    private getDiagnosticsStateItem(patientId: number, userContext: UserContext, stepInfo: BundleSetupInfoV2, stepName: string, totalSteps: number, reportEstimationTime: any, reportAction: string): ProductStateItem {
        let states: StepStateCard[]
        let dividerGradient = dividerGradientEnabled
        let action: Action
        let isExpanded: boolean = true
        let icon: string = "tests"
        let diagnosticsTestBookingInfo: DiagnosticsTestOrderResponse
        let testOrderId
        let reportInfo: DiagnosticReportInfo
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "APP")
        switch (stepInfo.stepState) {
            case "NOT_BOOKED":
                states = this.getDiagnosticsNotBookedSteps(stepInfo)
                dividerGradient = dividerGradientDisabled
                break
            case "BOOKED":
                diagnosticsTestBookingInfo = stepInfo.diagnosticsTestBookingInfo.diagnosticsTestOrderResponse[0]
                states = this.getSteps(patientId, userContext, diagnosticsTestBookingInfo, "NOT_STARTED", reportEstimationTime)
                if (!_.isEmpty(stepInfo.actionV2s)) {
                    const cancelAction = stepInfo.actionV2s.find(action => action.actionType === "CANCEL")
                    if (cancelAction) {
                        action = {
                            actionType: "CANCEL_HCU_TEST",
                            title: "CANCEL",
                            meta: {
                                tcBookingId: diagnosticsTestBookingInfo.bookingId
                            }
                        }
                    }
                } else {
                    action = this.getActionForDiagnosticState(userContext, stepInfo, reportAction)
                }
                break
            case "TEST_COMPLETED":
                diagnosticsTestBookingInfo = stepInfo.diagnosticsTestBookingInfo.diagnosticsTestOrderResponse[0]
                states = this.getSteps(patientId, userContext, diagnosticsTestBookingInfo, "STARTED", reportEstimationTime)
                action = this.getActionForDiagnosticState(userContext, stepInfo, reportAction)
                break
            case "REPORT_GENERATED":
                diagnosticsTestBookingInfo = stepInfo.diagnosticsTestBookingInfo.diagnosticsTestOrderResponse[0]
                states = this.getSteps(patientId, userContext, diagnosticsTestBookingInfo, "COMPLETED", reportEstimationTime)
                testOrderId = diagnosticsTestBookingInfo.orderId
                reportInfo = _.get(diagnosticsTestBookingInfo, "finalDiagnosticReport.diagnosticCheckUpReportInfo")
                if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
                    action = CareUtil.getDiagnosticsEmailTestReportAction(testOrderId, "Email Report")
                }
                isExpanded = false
                icon = "tick"
                break
            default:
                action = this.getActionForDiagnosticState(userContext, stepInfo, reportAction)
                break
        }
        const stateItem: ProductStateItem = {
            header: totalSteps === 1 ? undefined : stepName,
            title: "Diagnostic tests",
            gradientColors: ["#fb8676", "#f64cab"],
            dividerGradient: dividerGradient,
            icon: icon,
            states: states,
            action: action,
            isExpanded: isExpanded
        }
        return stateItem
    }

    private getDiagnosticsAtHomeStateCard(userContext: UserContext, patientId: number, diagnosticsTestOrderResponse: DiagnosticsTestOrderResponse): StepStateCard {
        const atHomeStepInfo: StepInfo = diagnosticsTestOrderResponse.atHomeStepInfo
        if (!_.isEmpty(atHomeStepInfo) && !_.isEmpty(diagnosticsTestOrderResponse.atHomeDiagnosticOrder)) {
            const sampleCollectionStartDate = diagnosticsTestOrderResponse.atHomeDiagnosticOrder.startTime
            const sampleCollectionDateText: string = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, sampleCollectionStartDate, "ddd, DD MMM, h:mm A")
            const atHomeTitle: string = "Sample collection at home"
            switch (atHomeStepInfo.stepState) {
                case "BOOKED":
                    const rescheduleAction = this.getRescheduleAction(patientId, atHomeStepInfo, diagnosticsTestOrderResponse, "AT_HOME_SLOT")
                    const atHomeDetailTopAction: any = undefined // this.getAtHomeDetailTopAction(diagnosticsTestOrderResponse)
                    const cardAction: Action = {
                        title: "Details",
                        actionType: "SHOW_DIAGNOSTIC_TEST_DETAILS",
                        meta: {
                            instructions: diagnosticsTestOrderResponse.atHomeInstructions,
                            topAction: atHomeDetailTopAction,
                            bottomAction: rescheduleAction,
                            title: atHomeTitle,
                            subtitle: sampleCollectionDateText
                        }
                    }
                    const action: Action = rescheduleAction ? {
                        ...rescheduleAction,
                        title: "Reschedule"
                    } : cardAction
                    return {
                        state: "STARTED",
                        viewType: "TEXT_ACTION",
                        views: [
                            {
                                title: atHomeTitle,
                                action,
                                cardAction,
                                text: sampleCollectionDateText

                            }
                        ]
                    }
                case "COMPLETED":
                case "SAMPLE_COLLECTED":
                    return {
                        state: "COMPLETED",
                        viewType: "TEXT_ACTION",
                        views: [
                            {
                                title: atHomeTitle
                            }
                        ]
                    }
            }
        }
    }

    private getDiagnosticsAtCenterStateCard(userContext: UserContext, patientId: number, diagnosticsTestOrderResponse: DiagnosticsTestOrderResponse, atHomeEnabled: boolean): StepStateCard {
        if (!_.isEmpty(diagnosticsTestOrderResponse.inCentreStepInfo) && !_.isEmpty(diagnosticsTestOrderResponse.inCentreDiagnosticOrder)) {
            const testStartDate = diagnosticsTestOrderResponse.inCentreDiagnosticOrder.slot.workingStartTime
            const inCentreTestDateStartText: string = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, testStartDate, "ddd, DD MMM, h:mm A")
            const inCentreTitle = atHomeEnabled ? "Radiology tests at center" : "All tests at center"

            switch (diagnosticsTestOrderResponse.inCentreStepInfo.stepState) {
                case "BOOKED":
                    const rescheduleAction = this.getRescheduleAction(patientId, diagnosticsTestOrderResponse.inCentreStepInfo, diagnosticsTestOrderResponse, "IN_CENTRE_SLOT", atHomeEnabled)
                    const inCenterTopAction = this.getInCenterDetailTopAction(diagnosticsTestOrderResponse)
                    const cardAction: Action = {
                        title: "Details",
                        actionType: "SHOW_DIAGNOSTIC_TEST_DETAILS",
                        meta: {
                            instructions: diagnosticsTestOrderResponse.inCentreInstructions,
                            topAction: inCenterTopAction,
                            bottomAction: rescheduleAction,
                            title: inCentreTitle,
                            subtitle: inCentreTestDateStartText
                        }
                    }
                    const action: Action = rescheduleAction ? {
                        ...rescheduleAction,
                        title: "Reschedule"
                    } : cardAction
                    return {
                        state: "STARTED",
                        viewType: "TEXT_ACTION",
                        views: [
                            {
                                title: inCentreTitle,
                                action,
                                cardAction,
                                text: inCentreTestDateStartText

                            }
                        ]
                    }
                case "COMPLETED":
                case "SAMPLE_COLLECTED":
                    return {
                        state: "COMPLETED",
                        viewType: "TEXT_ACTION",
                        views: [
                            {
                                title: inCentreTitle
                            }
                        ]
                    }
            }
        }
    }

    private getSteps(patientId: number, userContext: UserContext, diagnosticsTestBookingInfo: DiagnosticsTestOrderResponse, state: STEP_STATE, reportEstimation: any): StepStateCard[] {
        const states: StepStateCard[] = []
        const isDiagnosticInstructionSupported = CareUtil.isDiagnosticInstructionSupported(userContext)
        const statusInfo = CareUtil.getDiagnosticTestHomeCenterStatusInfo(diagnosticsTestBookingInfo)
        const reportEstimationTime = !_.isEmpty(reportEstimation) ? _.get(reportEstimation, "tat", null) : null
        const atHomeCard = isDiagnosticInstructionSupported ? this.getDiagnosticsAtHomeStateCardV2(userContext, patientId, diagnosticsTestBookingInfo, statusInfo) : this.getDiagnosticsAtHomeStateCard(userContext, patientId, diagnosticsTestBookingInfo)
        const atHomeEnabled: boolean = !_.isEmpty(atHomeCard)
        const incentreCard = isDiagnosticInstructionSupported ? this.getDiagnosticsAtCenterStateCardV2(userContext, patientId, diagnosticsTestBookingInfo, atHomeEnabled, statusInfo) : this.getDiagnosticsAtCenterStateCard(userContext, patientId, diagnosticsTestBookingInfo, atHomeEnabled)
        const atCenterEnabled: boolean = !_.isEmpty(incentreCard)
        if (statusInfo.sampleCollectionStartDateAtCenter < statusInfo.sampleCollectionStartDateAtHome) {
            if (atCenterEnabled) {
                states.push(incentreCard)
            }
            if (atHomeEnabled) {
                states.push(atHomeCard)
            }
        } else {
            if (atHomeEnabled) {
                states.push(atHomeCard)
            }
            if (atCenterEnabled) {
                states.push(incentreCard)
            }
        }
        states.push({
            state: state,
            viewType: "TEXT_ACTION",
            isStateCollapsible: false,
            views: [
                {
                    title: `Get your report${isDiagnosticInstructionSupported && reportEstimationTime ? " | Expected Time" : ""}`,
                    text: `${isDiagnosticInstructionSupported && reportEstimationTime ? TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, reportEstimationTime as number, "ddd, D MMM, h:mm A") : ""}`
                }
            ]
        })
        return states
    }

    private getDiagnosticsAtHomeStateCardV2(userContext: UserContext, patientId: number, diagnosticsTestOrderResponse: DiagnosticsTestOrderResponse, statusInfo: any): StepStateCard {
        const atHomeStepInfo: StepInfo = diagnosticsTestOrderResponse.atHomeStepInfo
        if (!_.isEmpty(atHomeStepInfo) && !_.isEmpty(diagnosticsTestOrderResponse.atHomeDiagnosticOrder)) {
            const sampleCollectionStartDate = _.get(diagnosticsTestOrderResponse, "atHomeDiagnosticOrder.startTime", null)
            const sampleCollectionEndDate = _.get(diagnosticsTestOrderResponse, "atHomeDiagnosticOrder.endTime", null)
            const rescheduleAction = this.getRescheduleAction(patientId, atHomeStepInfo, diagnosticsTestOrderResponse, "AT_HOME_SLOT", undefined)
            const atHomeDetailTopAction: any = userContext.sessionInfo.userAgent === "APP" ? CareUtil.getAtHomeDetailTopAction(userContext, diagnosticsTestOrderResponse.atHomeDiagnosticOrder, diagnosticsTestOrderResponse.atHomeStepInfo) : undefined
            // Temp hack Adding 30 mins for home slots alone and removed end time
            const sampleCollectionDateText: string = sampleCollectionStartDate ? CareUtil.getHomeCollectionTimeText(sampleCollectionStartDate, sampleCollectionEndDate, userContext) : ""
            const titleObj = {
                title: "Sample collection at home",
                completionTitle: "Sample Collected",
                startText: sampleCollectionDateText,
                endText: sampleCollectionDateText
            }
            return CareUtil.getDiagnosticsStateCardV2("AT_HOME", diagnosticsTestOrderResponse, statusInfo, titleObj, atHomeDetailTopAction, rescheduleAction, {})
        }
    }

    private getDiagnosticsAtCenterStateCardV2(userContext: UserContext, patientId: number, diagnosticsTestOrderResponse: DiagnosticsTestOrderResponse, atHomeEnabled: boolean, statusInfo: any): StepStateCard {
        if (!_.isEmpty(diagnosticsTestOrderResponse.inCentreStepInfo) && !_.isEmpty(diagnosticsTestOrderResponse.inCentreDiagnosticOrder)) {
            const tz = userContext.userProfile.timezone
            const testStartDate = _.get(diagnosticsTestOrderResponse, "inCentreDiagnosticOrder.slot.workingStartTime", null)
            const testEndDate = _.get(diagnosticsTestOrderResponse, "inCentreDiagnosticOrder.slot.workingEndTime", null)
            const titleObj = {
                title: atHomeEnabled ? "Radiology tests at center" : "All tests at center",
                completionTitle: atHomeEnabled ? "Radiology tests at center" : "All tests at center",
                startText: testStartDate ? TimeUtil.formatEpochInTimeZone(tz, testStartDate as number, "ddd, DD MMM, h:mm A") : "",
                endText: testEndDate ? TimeUtil.formatEpochInTimeZone(tz, testEndDate as number, "ddd, DD MMM, h:mm A") : ""
            }
            const rescheduleAction = this.getRescheduleAction(patientId, diagnosticsTestOrderResponse.inCentreStepInfo, diagnosticsTestOrderResponse, "IN_CENTRE_SLOT", atHomeEnabled)
            const inCenterTopAction = this.getInCenterDetailTopAction(diagnosticsTestOrderResponse)
            return CareUtil.getDiagnosticsStateCardV2("AT_CENTER", diagnosticsTestOrderResponse, statusInfo, titleObj, inCenterTopAction, rescheduleAction, {})
        }
    }

    private getInCenterDetailTopAction(diagnosticsTestOrderResponse: DiagnosticsTestOrderResponse): Action {
        const placeUrl = diagnosticsTestOrderResponse.inCentreDiagnosticOrder.slot.diagnosticCentre.placeUrl
        return {
            actionType: "EXTERNAL_DEEP_LINK",
            url: placeUrl,
            icon: "NAVIGATE",
            title: "View Map",
            bgColor: ["#ff3278", "#ff5972"],
            color: "#ffffff",
        }
    }

    private getRescheduleAction(patientId: number, stepInfo: StepInfo, diagnosticsTestOrderResponse: DiagnosticsTestOrderResponse, category: string, atHomeEnabled?: boolean): Action {
        if (_.includes(stepInfo.allowedActions, "RESCHEDULE")) {
            const addressId = diagnosticsTestOrderResponse.atHomeDiagnosticOrder && diagnosticsTestOrderResponse.atHomeDiagnosticOrder.addressMetadata && diagnosticsTestOrderResponse.atHomeDiagnosticOrder.addressMetadata.addressId || undefined
            const productId = diagnosticsTestOrderResponse.productCodes[0]
            const parentBookingId = diagnosticsTestOrderResponse.bookingId
            const productCodes = diagnosticsTestOrderResponse.productCodes.join(",")
            if (category === "AT_HOME_SLOT" && addressId ) {
                return {
                    title: "RESCHEDULE",
                    actionType: "NAVIGATION",
                    url: `curefit://selectCareDateV1?patientId=${patientId}&productId=${productId}&parentBookingId=${parentBookingId}
                        &type=DIAGNOSTICS&category=AT_HOME_SLOT&isReschedule=true&productCodes=${productCodes}&addressIdV1=${addressId}`
                }
            }
            return {
                title: "RESCHEDULE",
                actionType: "DIAGNOSTIC_TEST_RESCHEDULE",
                meta: {
                    parentBookingId: diagnosticsTestOrderResponse.bookingId,
                    patientId,
                    centerId: category === "IN_CENTRE_SLOT" ? diagnosticsTestOrderResponse.inCentreDiagnosticOrder.slot.diagnosticCentre.id : undefined,
                    centreCode: category === "IN_CENTRE_SLOT" ? diagnosticsTestOrderResponse.inCentreDiagnosticOrder.slot.diagnosticCentre.code : undefined,
                    productId: diagnosticsTestOrderResponse.productCodes[0],
                    type: "DIAGNOSTICS",
                    category: category,
                    hasAtHome: atHomeEnabled ? true : undefined,
                    title: category === "IN_CENTRE_SLOT" ? undefined : "Select your home address"
                }
            }
        }
    }

    private getDiagnosticsNotBookedSteps(stepInfo: BundleSetupInfoV2): StepStateCard[] {
        const states: StepStateCard[] = []
        states.push({
            state: "NOT_STARTED",
            viewType: "TEXT_ACTION",
            views: [
                {
                    title: "Schedule tests"
                }
            ]
        })
        states.push({
            state: "NOT_STARTED",
            viewType: "TEXT_ACTION",
            views: [
                {
                    title: "Get diagnostic report"
                }
            ]
        })
        return states
    }

    private getHealthAssessmentStateItem(stepInfo: BundleSetupInfoV2, stepName: string, totalSteps: number): ProductStateItem {
        let states: StepStateCard[]
        let isExpanded: boolean = true
        let icon: string = "assessment"
        switch (stepInfo.stepState) {
            case "NOT_COMPLETED":
                states = this.getHeathAssessmentNotCompletedSteps(stepInfo)
                break
            case "PARTIALLY":
                states = this.getHeathAssessmentPartiallyCompletedSteps(stepInfo)
                break
            case "COMPLETED":
                states = this.getHeathAssessmentCompletedSteps()
                icon = "tick"
                isExpanded = false
        }
        const stateItem: ProductStateItem = {
            header: totalSteps === 1 ? undefined : stepName,
            title: "Fill health assessement",
            subtitle: "Let us know more about your health",
            gradientColors: ["#77fd9d", "#48c9b3"],
            dividerGradient: dividerGradientEnabled,
            icon: icon,
            states: states,
            isExpanded: isExpanded
        }
        return stateItem
    }

    private getHeathAssessmentNotCompletedSteps(stepInfo: BundleSetupInfoV2): StepStateCard[] {
        const actionContext = stepInfo.actionV2s.find(action => action.actionType === "TAKE_HEALTH_ASSESSMENT").actionContext
        return [
            {
                state: "STARTED",
                viewType: "ACTIONABLE",
                views: [
                    {
                        action: {
                            meta: {
                                nextAction: {
                                    meta: {
                                        assessmentId: actionContext.assessmentClientAssessmentId,
                                        assessmentStatus: "COMPLETED"
                                    },
                                    actionType: "UPDATE_PATIENT_ASSESSMENT"
                                }
                            },
                            title: "Start now",
                            url: ActionUtil.webview(actionContext.assessmentUrl),
                            actionType: "OPEN_ASSESSMENT_FORM"
                        }
                    }
                ]
            }
        ]
    }

    private getHeathAssessmentPartiallyCompletedSteps(stepInfo: BundleSetupInfoV2): StepStateCard[] {
        const actionContext = stepInfo.actionV2s.find(action => action.actionType === "TAKE_HEALTH_ASSESSMENT").actionContext
        return [
            {
                state: "STARTED",
                viewType: "ACTIONABLE",
                views: [
                    {
                        progressPercent: actionContext.completionPerc > 0 ? actionContext.completionPerc : undefined,
                        action: {
                            meta: {
                                nextAction: {
                                    meta: {
                                        assessmentId: actionContext.assessmentClientAssessmentId,
                                        assessmentStatus: "COMPLETED"
                                    },
                                    actionType: "UPDATE_PATIENT_ASSESSMENT"
                                }
                            },
                            title: "Complete now",
                            url: ActionUtil.webview(actionContext.assessmentUrl),
                            actionType: "OPEN_ASSESSMENT_FORM"
                        }
                    }
                ]
            }
        ]
    }

    private getHeathAssessmentCompletedSteps(): StepStateCard[] {
        return [
            {
                state: "COMPLETED",
                viewType: "TEXT_ACTION",
                views: [
                    {
                        title: "Completed" // TODO
                    }
                ]
            }
        ]
    }

    private getFormCompletedSteps(title: string): StepStateCard[] {
        return [
            {
                state: "COMPLETED",
                viewType: "TEXT_ACTION",
                views: [
                    {
                        title: title
                    }
                ]
            }
        ]
    }

    private getFormNotStartedSteps(title: string, formId: string): StepStateCard[] {

        return [
            {
                state: "STARTED",
                viewType: "ACTIONABLE",
                views: [
                    {
                        title: title,
                        action: {
                            title: "Start Now",
                            url: `curefit://userform?formId=${formId}`,
                            actionType: "NAVIGATION"
                        }
                    }
                ]
            }
        ]
    }

    private getUserPlanStateItem(stepInfo: BundleSetupInfoV2, patientId: number, stepName: string, totalSteps: number): ProductStateItem {
        const stateItem: ProductStateItem = {
            header: totalSteps === 1 ? undefined : stepName,
            title: "Generate your plan",
            subtitle: "Let us know more about your health",
            gradientColors: ["#77fd9d", "#48c9b3"],
            dividerGradient: dividerGradientEnabled,
            icon: "assessment",
            states: [],
            isExpanded: false
        }
        return stateItem
    }

    private getUserFormStateItem(stepInfo: BundleSetupInfoV2, patientId: number, stepName: string, totalSteps: number): ProductStateItem {
        let states: StepStateCard[]
        let isExpanded: boolean = true
        let icon: string = "assessment"
        const action = stepInfo.actionV2s.find(action => action.actionType === "START_FORM" || action.actionType === "FILL_PARTIAL_FORM" || action.actionType === "FORM_COMPLETED")
        const title = action.actionContext.formOrderResponse.formSellableProduct.productName
        const subTitle = action.actionContext.formOrderResponse.formSellableProduct.productDescription
        const formId = action.actionContext.formOrderResponse.formSellableProduct.infoSection.formId
        switch (stepInfo.stepState) {
            case "NOT_STARTED":
                states = this.getFormNotStartedSteps(subTitle, formId)
                break
            case "PARTIAL":
                states = this.getFormNotStartedSteps(subTitle, formId)
                break
            case "COMPLETED":
                states = this.getFormCompletedSteps(subTitle)
                icon = "tick"
                isExpanded = false
        }
        const stateItem: ProductStateItem = {
            header: totalSteps === 1 ? undefined : stepName,
            title: title,
            gradientColors: ["#77fd9d", "#48c9b3"],
            dividerGradient: dividerGradientEnabled,
            icon: icon,
            states: states,
            isExpanded: isExpanded
        }
        return stateItem
    }

    private getConsultationStateItem(userContext: UserContext, subCategoryCode: SUB_CATEGORY_CODE, stepInfo: BundleSetupInfoV2, patientId: number, stepName: string, totalSteps: number): ProductStateItem {
        let states: StepStateCard[]
        let dividerGradient
        let isExpanded: boolean
        let icon: string = "report"
        switch (stepInfo.stepState) {
            case "NOT_ELIGIBLE":
                states = this.getConsultationNotEligibleSteps(subCategoryCode)
                dividerGradient = dividerGradientDisabled
                isExpanded = false
                break
            case "PARTIALLY_BOOKED":
            case "NOT_BOOKED":
                dividerGradient = dividerGradientEnabled
                isExpanded = true
                states = this.getConsultationNotBookedSteps(userContext, subCategoryCode, stepInfo, patientId)
                break
            case "BOOKED":
                isExpanded = true
                dividerGradient = dividerGradientEnabled
                states = this.getConsultationBookedSteps(userContext, subCategoryCode, stepInfo)
                break
            case "COMPLETED":
            case "PRESCRIPTION_GENERATED":
            case "PLAN_GENERATED":
                isExpanded = true
                dividerGradient = dividerGradientEnabled
                states = this.getConsultationBookedSteps(userContext, subCategoryCode, stepInfo)
                icon = "tick"
                break
        }
        const stateItem: ProductStateItem = {
            header: totalSteps === 1 ? undefined : stepName,
            title: subCategoryCode === "HCU_PACK" ? "Discuss report" : "Meet your care providers",
            gradientColors: ["#f29458", "#fd796d"],
            dividerGradient: dividerGradient,
            icon: icon,
            states: states,
            isExpanded: isExpanded
        }
        return stateItem
    }

    private getConsultationStateItemMPV2(userContext: UserContext, subCategoryCode: SUB_CATEGORY_CODE, stepInfo: BundleSetupInfoV2, patientId: number, stepName: string, totalSteps: number): ProductStateItem[] {
        let states: StepStateCard[][]
        let dividerGradient: any
        let isExpanded: boolean
        const icon: string = "doctor"
        const icon1: string = "nutritionist"
        const appVersion = userContext.sessionInfo.appVersion
        const header2 = "Meet Personal Health Coach"
        const header1 = "Meet Personal Doctor"
        switch (stepInfo.stepState) {
            case "NOT_ELIGIBLE":
                states = [this.getConsultationNotEligibleSteps(subCategoryCode)]
                dividerGradient = dividerGradientDisabled
                isExpanded = false
                break
            case "PARTIALLY_BOOKED":
            case "NOT_BOOKED":
                dividerGradient = dividerGradientEnabled
                isExpanded = true
                states = this.getConsultationNotBookedStepsMPV2(userContext, subCategoryCode, stepInfo, patientId)
                break
            case "BOOKED":
                isExpanded = true
                dividerGradient = dividerGradientEnabled
                states = appVersion < CARE_STATE_ITEM_MPV2_SUPPORTED ? [this.getConsultationBookedSteps(userContext, subCategoryCode, stepInfo)] : this.getConsultationBookedStepsMPV2(userContext, subCategoryCode, stepInfo)
                break
            case "COMPLETED":
            case "PRESCRIPTION_GENERATED":
            case "PLAN_GENERATED":
                isExpanded = true
                dividerGradient = dividerGradientEnabled
                states = appVersion < CARE_STATE_ITEM_MPV2_SUPPORTED ? [this.getConsultationBookedSteps(userContext, subCategoryCode, stepInfo)] : this.getConsultationBookedStepsMPV2(userContext, subCategoryCode, stepInfo)
                break
        }
        const stateItem: ProductStateItem[] = []
        if (states.length) {
            states.map((state, index) => {
                stateItem.push({
                    header: undefined,
                    title: states.length > 1 ? (index === 1 ? header2 : header1) : "Meet your care providers",
                    gradientColors: states.length > 1 ? (index === 1 ? ["#17d8e5", "#ac9aff"] : ["#fb8676", "#f64cab"]) : ["#f29458", "#fd796d"],
                    dividerGradient: dividerGradient,
                    icon: states.length > 1 ? (index === 1 ? icon1 : icon) : icon,
                    states: state,
                    isExpanded: isExpanded
                })
            })
        }
        return stateItem
    }

    private getConsultationBookedStaticStep(subCategoryCode: SUB_CATEGORY_CODE): StepStateCard {
        return {
            state: "NOT_STARTED",
            viewType: "TEXT_ACTION",
            views: [
                {
                    title: subCategoryCode === "HCU_PACK" ? "Discuss possible next steps" : "Get your health plan"
                }
            ]
        }
    }

    private getConsultationNotEligibleSteps(subCategoryCode: SUB_CATEGORY_CODE): StepStateCard[] {
        return [
            {
                state: "NOT_STARTED",
                viewType: "TEXT_ACTION",
                views: [
                    {
                        title: "Meet with our team and have them go over your report"
                    }]
            },
            this.getConsultationBookedStaticStep(subCategoryCode)
        ]
    }

    private getConsultationNotBookedSteps(userContext: UserContext, subCategoryCode: SUB_CATEGORY_CODE, stepInfo: BundleSetupInfoV2, patientId: number): StepStateCard[] {
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "DESKTOP")
        if (_.isEmpty(stepInfo.consultationBookingInfos)) {
            const productCodes = stepInfo.consultationSellableProducts.map(product => product.productCode)
            if (productCodes.length === 1) {
                // this.primaryAction = {
                //     title: "Book Consultation",
                //     url: `curefit://selectCareDateV1?productId=${productCodes[0]}&patientId=${patientId}&parentBookingId=${stepInfo.parentBookingId}`,
                //     actionType: userAgent === "DESKTOP" || userAgent === "MBROWSER" ? "SELECT_CARE_DATE" : "NAVIGATION"
                // }
                this.primaryAction = {
                    ...CareUtil.specialistListingAction(userContext, CareUtil.toConsultationProduct(stepInfo.consultationSellableProducts[0]), false, patientId, stepInfo.parentBookingId, undefined, undefined, false, undefined, undefined, undefined, true, "Select a Doctor"),
                    meta: {
                        name: "Select a Doctor"
                    },
                    title: "Book Consultation"
                }
            } else {
                this.primaryAction = {
                    title: stepInfo.consultationSellableProducts.length > 1 ? "Book Consultations" : "Book Consultation",
                    url: `curefit://multiConsultationSlots`,
                    actionType: userAgent === "DESKTOP" || userAgent === "MBROWSER" ? "GET_SLOTS_MULTI_CONSULTATION" : "NAVIGATION",
                    meta: {
                        productCodes,
                        currentProductCode: productCodes[0],
                        parentBookingId: stepInfo.parentBookingId,
                        patientId: patientId,
                    }
                }
            }
            const steps: StepStateCard[] = [{
                state: "STARTED",
                viewType: "ACTIONABLE",
                views: [
                    {
                        title: subCategoryCode === "HCU_PACK" ? "Schedule Doctor Consultation" : "Meet your care providers",
                        bullets: stepInfo.consultationSellableProducts.length > 1 ? stepInfo.consultationSellableProducts.map(product => CareUtil.getDoctorTypeAsString(product.consultationProduct.doctorType)) : undefined,
                        action: this.primaryAction
                    }
                ]
            },
            this.getConsultationBookedStaticStep(subCategoryCode)
            ]
            return steps
        } else {
            const selectedTimeSlots: {
                [key: string]: {
                    startTime: number,
                    endTime: number,
                    doctorIdList: number[],
                    bookingId?: number
                }
            } = {}
            const bookedProductCodes: string[] = []
            stepInfo.consultationBookingInfos.map(bookingInfo => {
                bookedProductCodes.push(bookingInfo.booking.productCode)
                selectedTimeSlots[bookingInfo.booking.productCode] = {
                    startTime: bookingInfo.consultationOrderResponse.startTime,
                    endTime: bookingInfo.consultationOrderResponse.endTime,
                    doctorIdList: [bookingInfo.consultationOrderResponse.doctor.id]
                }
            })
            const productCodes = stepInfo.consultationSellableProducts.map(product => product.productCode).filter(code => bookedProductCodes.indexOf(code) === -1)
            this.primaryAction = {
                title: "Book Consultation",
                url: `curefit://multiConsultationSlots`,
                actionType: userAgent === "DESKTOP" || userAgent === "MBROWSER" ? "GET_SLOTS_MULTI_CONSULTATION" : "NAVIGATION",
                meta: {
                    productCodes: [...bookedProductCodes, ...productCodes],
                    currentProductCode: productCodes[0],
                    parentBookingId: stepInfo.parentBookingId,
                    selectedTimeSlots: selectedTimeSlots,
                    patientId: patientId
                }
            }
            const steps: StepStateCard[] = [
                {
                    state: "COMPLETED",
                    viewType: "CONSULTATION_CARDS",
                    title: "Scheduled Consultation",
                    views: this.getBookedConsultationViews(userContext, stepInfo.consultationBookingInfos)
                },
                {
                    state: "STARTED",
                    viewType: "ACTIONABLE",
                    views: [
                        {
                            title: "Schedule Consultation",
                            action: this.primaryAction
                        }
                    ]
                },
                this.getConsultationBookedStaticStep(subCategoryCode)
            ]
            return steps
        }
    }

    private getDoctorConsultationStepsMPV2(userContext: UserContext): StepStateCard[] {
        return [
            {
                state: userContext.sessionInfo.appVersion < CARE_STATE_ITEM_MPV2_SUPPORTED ? "NOT_STARTED" : "STARTED",
                viewType: "TEXT_ACTION",
                views: [
                    {
                        title: "60 mins in depth diagnosis and evaluation of your current medical condition",
                        textStyling: true
                    }
                ]
            },
            {
                state: userContext.sessionInfo.appVersion < CARE_STATE_ITEM_MPV2_SUPPORTED ? "NOT_STARTED" : "STARTED",
                viewType: "TEXT_ACTION",
                views: [
                    {
                        title: "Get clinical plan",
                        textStyling: true
                    }
                ]
            }
        ]
    }

    private getHealthCoachStepsMPV2(userContext: UserContext): StepStateCard[] {
        return [
            {
                state: userContext.sessionInfo.appVersion < CARE_STATE_ITEM_MPV2_SUPPORTED ? "NOT_STARTED" : "STARTED",
                viewType: "TEXT_ACTION",
                views: [
                    {
                        title: "60 mins detailed evaluation to understand your current Diet and Lifestyle",
                        textStyling: true
                    }
                ]
            },
            {
                state: userContext.sessionInfo.appVersion < CARE_STATE_ITEM_MPV2_SUPPORTED ? "NOT_STARTED" : "STARTED",
                viewType: "TEXT_ACTION",
                views: [
                    {
                        title: "Get personalised Diet and Lifestyle Plan",
                        textStyling: true
                    }
                ]
            }
        ]
    }

    private getConsultationNotBookedStepsMPV2(userContext: UserContext, subCategoryCode: SUB_CATEGORY_CODE, stepInfo: BundleSetupInfoV2, patientId: number): StepStateCard[][] {
        if (_.isEmpty(stepInfo.consultationBookingInfos)) {
            const productCodes = stepInfo.consultationSellableProducts.map(product => product.productCode)
            const action: Action = {
                title: stepInfo.consultationSellableProducts.length > 1 ? "Book Consultations" : "Book Consultation",
                url: `curefit://multiConsultationSlots`,
                actionType: "NAVIGATION",
                meta: {
                    productCodes,
                    currentProductCode: productCodes[0],
                    parentBookingId: stepInfo.parentBookingId,
                    patientId: patientId,
                }
            }
            this.primaryAction = CareUtil.getInstructionModalAction(userContext, action, action.title, CareUtil.getInstructionsForMPV2ConsultationAppointments())
            const steps: StepStateCard[][] = [
                this.getDoctorConsultationStepsMPV2(userContext),
                this.getHealthCoachStepsMPV2(userContext)
            ]
            return steps
        } else {
            let isGPBooked = false
            const bookedProductCodes: string[] = []
            stepInfo.consultationBookingInfos.map(bookingInfo => {
                if (bookingInfo.consultationOrderResponse.doctorType === "GP") {
                    isGPBooked = true
                }
                bookedProductCodes.push(bookingInfo.booking.productCode)
            })
            const productCodes = stepInfo.consultationSellableProducts.map(product => product.productCode).filter(code => bookedProductCodes.indexOf(code) === -1)
            const action: Action = {
                title: "Book Consultation",
                actionType: "NAVIGATION",
                url: `curefit://selectCareDateV1?productId=${productCodes[0]}&patientId=${patientId}&parentBookingId=${stepInfo.parentBookingId}`
            }
            this.primaryAction = CareUtil.getInstructionModalAction(userContext, action, action.title, CareUtil.getInstructionsForMPV2ConsultationAppointments())
            const steps: StepStateCard[][] = isGPBooked ? [
                [
                    {
                        state: "STARTED",
                        viewType: "CONSULTATION_CARDS_V2",
                        views: this.getBookedConsultationViewsMPV2(userContext, stepInfo.consultationBookingInfos)
                    }
                ],
                this.getHealthCoachStepsMPV2(userContext)
            ] : [
                    this.getDoctorConsultationStepsMPV2(userContext),
                    [
                        {
                            state: "STARTED",
                            viewType: "CONSULTATION_CARDS_V2",
                            views: this.getBookedConsultationViewsMPV2(userContext, stepInfo.consultationBookingInfos)
                        }
                    ]
                ]
            return steps
        }
    }

    private getBookedConsultationViews(userContext: UserContext, consultationBookingInfos: BookingDetail[]): StateView[] {
        const views: StateView[] = []
        const tz = userContext.userProfile.timezone
        for (let i = 0; i < consultationBookingInfos.length; i++) {
            const bookingInfo = consultationBookingInfos[i]
            const consultationOrderResponse = bookingInfo.consultationOrderResponse
            const doctorDetails = consultationOrderResponse.doctor
            const isVideo = bookingInfo.booking.subCategoryCode === "CF_ONLINE_CONSULTATION"
            const subtitle = TimeUtil.formatEpochInTimeZone(tz, consultationOrderResponse.startTime, "h:mm A, DD MMM")
            const dependentBookingInfos = consultationBookingInfos.slice(i + 1, consultationBookingInfos.length)
            const precedingBookingInfos = consultationBookingInfos.slice(0, i)
            const vertical = CareUtil.getVerticalForConsultation(bookingInfo.consultationOrderResponse?.consultationProduct?.urlPath)
            views.push({
                title: `${consultationOrderResponse.doctorTypeCodeResponse.displayValue} Consultation`,
                subTitle: `With ${doctorDetails.name} for ${consultationOrderResponse.patient.name}`,
                imageUrl: doctorDetails.displayImage,
                footer: [
                    {
                        text: `${isVideo ? `Video ${consultationOrderResponse?.center?.name ? `| ${consultationOrderResponse?.center?.name}` : ""}` : `At Centre | ${consultationOrderResponse.center.name}`}`,
                        icon: isVideo ? "video" : "incentre"
                    },
                    {
                        text: `${TimeUtil.formatEpochInTimeZone(tz, consultationOrderResponse.startTime, "ddd, D MMM, h:mm A")}`,
                        icon: "RESCHEDULE"
                    }
                ],
                actions: this.getConsultationActions(userContext, bookingInfo, dependentBookingInfos, precedingBookingInfos),
                cardAction: {
                    title: doctorDetails.name,
                    subtitle: subtitle,
                    url: ActionUtil.teleconsultationSingle(userContext, bookingInfo.booking.productCode, bookingInfo.consultationOrderResponse?.consultationProduct.urlPath, bookingInfo.booking.id.toString(), undefined, vertical),
                    actionType: "NAVIGATION",
                }
            })
        }
        return views
    }

    private getBookedConsultationViewsMPV2(userContext: UserContext, consultationBookingInfos: BookingDetail[]): StateView[] {
        const views: StateView[] = []
        const tz = userContext.userProfile.timezone
        let isGPFirst = false
        const length = consultationBookingInfos.length
        if (length > 0) {
            isGPFirst = consultationBookingInfos[0].consultationOrderResponse.doctorType === "GP"
        }
        for (let i = 0; i < length; i++) {
            const bookingInfo = consultationBookingInfos[i]
            const consultationOrderResponse = bookingInfo.consultationOrderResponse
            const doctorDetails = consultationOrderResponse.doctor
            const isVideo = bookingInfo.booking.subCategoryCode === "CF_ONLINE_CONSULTATION"
            const subtitle = TimeUtil.formatEpochInTimeZone(tz, consultationOrderResponse.startTime, "h:mm A, DD MMM")
            const vertical = CareUtil.getVerticalForConsultation(bookingInfo.consultationOrderResponse?.consultationProduct?.urlPath)
            views.push({
                title: `${doctorDetails.name}`,
                imageUrl: doctorDetails.displayImage,
                footer: [
                    {
                        text: `${isVideo ? `Video | ${consultationOrderResponse.center.name}` : `At Centre | ${consultationOrderResponse.center.name}`}`,
                        icon: isVideo ? "video" : "incentre"
                    },
                    {
                        text: `${TimeUtil.formatEpochInTimeZone(tz, consultationOrderResponse.startTime, "ddd, D MMM, h:mm A")}`,
                        icon: "RESCHEDULE_LIGHT"
                    }
                ],
                actions: this.getConsultationActions(userContext, bookingInfo, [], [], true),
                cardAction: {
                    title: doctorDetails.name,
                    subtitle: subtitle,
                    url: ActionUtil.teleconsultationSingle(userContext, bookingInfo.booking.productCode, bookingInfo.consultationOrderResponse?.consultationProduct.urlPath, bookingInfo.booking.id.toString(), undefined, vertical),
                    actionType: "NAVIGATION",
                }
            })
        }
        return isGPFirst ? views : views.reverse()
    }

    private getConsultationActions(userContext: UserContext, bookingInfo: BookingDetail, dependentBookingInfos: BookingDetail[], precedingBookingInfos: BookingDetail[], isMPV2?: boolean): Action[] {
        const actions: Action[] = []
        const consultationOrderResponse: ConsultationOrderResponse = bookingInfo.consultationOrderResponse
        if (consultationOrderResponse.consultationUserState === "COMPLETED" && consultationOrderResponse.doctor.type !== "LIFESTYLE_COACH") {
            actions.push({
                title: "PRESCRIPTION",
                actionType: "NAVIGATION",
                icon: isMPV2 ? undefined : "PRESCRIPTION",
                url: `curefit://carefitPrescription?tcBookingId=${bookingInfo.booking.id}&productId=${bookingInfo.booking.productCode}`
            })
        }
        let rescheduleAction, cancelAction
        if (consultationOrderResponse.appointmentActionsWithContext && consultationOrderResponse.appointmentActionsWithContext.rescheduleActionWithContext && consultationOrderResponse.appointmentActionsWithContext.rescheduleActionWithContext.action.actionPermitted) {
            rescheduleAction = this.getConsultationRescheduleAction(userContext, bookingInfo, dependentBookingInfos, precedingBookingInfos, isMPV2)
        }

        if (consultationOrderResponse.appointmentActionsWithContext && consultationOrderResponse.appointmentActionsWithContext.cancelActionWithContext && consultationOrderResponse.appointmentActionsWithContext.cancelActionWithContext.action.actionPermitted) {
            cancelAction = this.getConsultationCancelAction(userContext, bookingInfo, dependentBookingInfos, isMPV2)
        }
        if (isMPV2) {
            const action = []
            rescheduleAction && action.push(rescheduleAction)
            cancelAction && action.push(cancelAction)
            if (action && action.length > 1) {
                actions.push({
                    actionType: "SHOW_CARE_ACTION_SHEET",
                    title: rescheduleAction && cancelAction ? "RESCHEDULE/ CANCEL" : rescheduleAction ? "RESCHEDULE" : cancelAction ? "CANCEL" : undefined,
                    meta: {
                        title: "Select your action",
                        cards: action.map(item => {
                            return {
                                ...item,
                                title: item.title ? item.title.charAt(0).toUpperCase() + item.title.substr(1).toLowerCase() : undefined,
                                icon: item.title === "RESCHEDULE" ? "RESCHEDULE" : item.title === "CANCEL" ? "CANCEL" : undefined,
                                action: item
                            }
                        })
                    }
                })
            } else if (action && action.length === 1) {
                actions.push({
                    ...action[0]
                })
            }
        } else {
            rescheduleAction && actions.push(rescheduleAction)
            cancelAction && actions.push(cancelAction)
        }

        if (actions.length < 2) {
            if (consultationOrderResponse.appointmentActionsWithContext && consultationOrderResponse.appointmentActionsWithContext.chatActionWithContext) {
                const chatAction: any = CareUtil.getChatMessageAction(userContext,
                    _.get(consultationOrderResponse, "appointmentActionsWithContext.chatActionWithContext", null),
                    consultationOrderResponse.patient.id,
                    consultationOrderResponse.doctor.name,
                    CareUtil.getChatChannelWithAppointmentContext(consultationOrderResponse.appointmentActionsWithContext),
                    consultationOrderResponse.doctor.displayImage,
                    consultationOrderResponse.doctor.qualification,
                    bookingInfo.booking.id,
                    undefined,
                    undefined,
                    isMPV2
                )
                chatAction && actions.push(chatAction)
            }
        }

        return isMPV2 ? actions.reverse() : actions
    }

    private getConsultationBookedSteps(userContext: UserContext, subCategoryCode: SUB_CATEGORY_CODE, stepInfo: BundleSetupInfoV2): StepStateCard[] {
        const cards: StepStateCard[] = []
        cards.push({
            state: "COMPLETED",
            viewType: "CONSULTATION_CARDS",
            title: "Scheduled Consultation",
            views: this.getBookedConsultationViews(userContext, stepInfo.consultationBookingInfos)
        })
        if (stepInfo.stepState !== "PLAN_GENERATED" && subCategoryCode !== "MP_V2") {
            cards.push(this.getConsultationBookedStaticStep(subCategoryCode))
        }
        return cards
    }

    private getConsultationBookedStepsMPV2(userContext: UserContext, subCategoryCode: SUB_CATEGORY_CODE, stepInfo: BundleSetupInfoV2): StepStateCard[][] {
        const cards: StepStateCard[][] = []
        const views = this.getBookedConsultationViewsMPV2(userContext, stepInfo.consultationBookingInfos)
        if (!_.isEmpty(views)) {
            views.map(item => {
                cards.push([{
                    state: "STARTED",
                    viewType: "CONSULTATION_CARDS_V2",
                    views: [item]
                }])
            })

        }
        return cards
    }

    private getConsultationRescheduleAction(userContext: UserContext, bookingInfo: BookingDetail, nextBookingInfos: BookingDetail[], precedingBookingInfos: BookingDetail[], noIcon: boolean): Action {
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "DESKTOP")
        const selectedTimeSlots: {
            [key: string]: {
                startTime: number,
                endTime: number,
                doctorIdList: number[],
                bookingId?: number
            }
        } = {}
        const productCodes: string[] = []

        precedingBookingInfos.map(bookingInfo => {
            productCodes.push(bookingInfo.booking.productCode)
            selectedTimeSlots[bookingInfo.booking.productCode] = {
                startTime: bookingInfo.consultationOrderResponse.startTime,
                endTime: bookingInfo.consultationOrderResponse.endTime,
                doctorIdList: [bookingInfo.consultationOrderResponse.doctor.id],
                bookingId: bookingInfo.booking.id
            }
        })

        productCodes.push(bookingInfo.booking.productCode)
        if (!_.isEmpty(nextBookingInfos)) {
            nextBookingInfos.map(bookingInfo => {
                productCodes.push(bookingInfo.booking.productCode)
                selectedTimeSlots[bookingInfo.booking.productCode] = {
                    startTime: bookingInfo.consultationOrderResponse.startTime,
                    endTime: bookingInfo.consultationOrderResponse.endTime,
                    doctorIdList: [bookingInfo.consultationOrderResponse.doctor.id],
                    bookingId: bookingInfo.booking.id
                }
            })
        }
        if (productCodes.length === 1) {
            return {
                title: "RESCHEDULE",
                icon: noIcon ? undefined : "RESCHEDULE",
                url: `curefit://selectCareDateV1?productId=${productCodes[0]}&patientId=${bookingInfo.booking.patientId}&parentBookingId=${bookingInfo.booking.id}&doctorId=${bookingInfo.consultationOrderResponse.doctor.id}&centerId=${bookingInfo.consultationOrderResponse.center.id}&isReschedule=true`,
                actionType: userAgent === "DESKTOP" || userAgent === "MBROWSER" ? "SELECT_CARE_DATE" : "NAVIGATION"
            }
        }
        return {
            title: "RESCHEDULE",
            actionType: userAgent === "DESKTOP" || userAgent === "MBROWSER" ? "GET_RESCHEDULE_SLOTS_MULTI_CONSULTATION" : "NAVIGATION",
            icon: noIcon ? undefined : "RESCHEDULE",
            url: `curefit://multiConsultationSlots`,
            meta: {
                isReschedule: true,
                selectedTimeSlots,
                productCodes: productCodes,
                currentProductCode: bookingInfo.booking.productCode,
                parentBookingId: bookingInfo.booking.id,
                patientId: bookingInfo.booking.patientId
            }
        }


    }

    private getConsultationCancelAction(userContext: UserContext, bookingInfo: BookingDetail, dependentBookingInfos: BookingDetail[], noIcon: boolean): Action {
        if (_.isEmpty(dependentBookingInfos)) {
            return {
                title: "CANCEL",
                icon: noIcon ? undefined : "CANCEL",
                actionType: "CANCEL_MULTI_CONSULTATION",
                meta: {
                    bookingIds: [bookingInfo.booking.id],
                    orderId: bookingInfo.booking.cfOrderId,
                    productId: bookingInfo.booking.productCode,
                    patientId: bookingInfo.booking.patientId
                }
            }
        } else {
            return {
                title: "CANCEL",
                icon: noIcon ? undefined : "CANCEL",
                actionType: "SHOW_CANCEL_MULTI_CONSULTATION_MODAL",
                meta: {
                    action: {
                        actionType: "CANCEL_MULTI_CONSULTATION",
                        meta: {
                            bookingIds: [bookingInfo.booking.id, ...dependentBookingInfos.map(bookingInfo => bookingInfo.booking.id)]
                        }
                    },
                    alertInfo: {
                        title: "Cancel appointment",
                        subTitle: "Cancelling this appointment will cancel following appointments:",
                        bullets: dependentBookingInfos.map(dependentBookingInfo => {
                            return {
                                leftText: dependentBookingInfo.consultationOrderResponse.doctorTypeCodeResponse.displayValue,
                                rightText: `${TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, dependentBookingInfo.consultationOrderResponse.startTime, "D MMM | h:mm A")}`
                            }
                        })
                    }
                }
            }
        }
    }

    private getActionForDiagnosticState(userContext: UserContext, stepInfo: BundleSetupInfoV2, reportAction: string): Action {
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "APP")
        let action: Action
        if (userAgent !== "DESKTOP" && userAgent !== "MBROWSER" && reportAction) {
            const diagnosticsTestBookingInfo = stepInfo && stepInfo.diagnosticsTestBookingInfo && stepInfo.diagnosticsTestBookingInfo.diagnosticsTestOrderResponse && !_.isEmpty(stepInfo.diagnosticsTestBookingInfo.diagnosticsTestOrderResponse) && stepInfo.diagnosticsTestBookingInfo.diagnosticsTestOrderResponse[0]
            if (diagnosticsTestBookingInfo && diagnosticsTestBookingInfo.orderId && diagnosticsTestBookingInfo.carefitOrderId) {
                const testOrderId = diagnosticsTestBookingInfo.orderId
                let cta
                switch (reportAction) {
                    case "VIEW_REPORT":
                        cta = "View Report"
                        break
                    case "TRACK_REPORT":
                        cta = "Track Report"
                        break
                    default:
                        cta = undefined
                }
                if (cta) {
                    action = {
                        actionType: "NAVIGATION",
                        url: ActionUtil.diagnosticReportPage(testOrderId, diagnosticsTestBookingInfo.carefitOrderId),
                        title: cta
                    }
                }
            }
        }
        return action
    }
}
