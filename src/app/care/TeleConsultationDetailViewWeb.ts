import * as _ from "lodash"
import { Feedback } from "@curefit/feedback-common"
import { OfferV2 } from "@curefit/offer-common"
import { FoodProduct as Product } from "@curefit/eat-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { ConsultationProduct } from "@curefit/care-common"
import { CustomerIssueType } from "@curefit/issue-common"
import { ProductPrice } from "@curefit/product-common"
import { User, MiniAppId } from "@curefit/user-common"
import {
    BookingDetail,
    Center,
    ConsultationOrderResponse,
    ConsultationSellableProduct,
    Doctor,
    ManagedPlanPackInfo,
    Patient,
    ConsultationInstructionResponse
} from "@curefit/albus-client"
import { PackOffersResponse } from "@curefit/offer-common"
import { ActionUtil, RUPEE_SYMBOL } from "@curefit/base-utils"
import { OfferUtil } from "@curefit/base-utils"
import {
    Action,
    BenefitsWidget,
    CalloutWidget,
    getOffersWidget,
    GradientCard,
    Header,
    InfoCard,
    InstructionsWidget,
    ManageOptionPayload,
    ManageOptions,
    ManageOptionsWidget,
    NavigationCardWidget,
    ProductDetailPage,
    ProductFeedbackWidget,
    ProductGridWidget,
    ProductListWidget,
    WidgetView,
    DoctorListWidgetV2,
    TextWidget
} from "../common/views/WidgetView"
import FeedbackPageConfigV2Cache from "../ugc/FeedbackPageConfigV2Cache"
import { CareUtil } from "../util/CareUtil"
import TeleconsultationDetailsPageConfig from "./TeleconsultationDetailsPageConfig"
import { TimeUtil } from "@curefit/util-common"
import { UserContext } from "@curefit/userinfo-common"
import { Orientation } from "@curefit/vm-common"
import * as useragent from "express-useragent"
import { HamletBusiness } from "@curefit/hamlet-node-sdk"
import AppUtil, { SUPPORT_DEEP_LINK, ZOOM_WEB_SDK } from "../util/AppUtil"
import { LexRuntime } from "aws-sdk"
import { Logger } from "@curefit/base"
const clone = require("clone")

class TeleconsultationDetailsViewWeb extends ProductDetailPage {
    constructor() {
        super()
    }
    public pageContext: any
    private buildView(
        source: string,
        isVideoEnabled: boolean,
        user: User,
        isNotLoggedIn: boolean,
        product: ConsultationProduct,
        pageConfig: TeleconsultationDetailsPageConfig,
        packageProduct: ConsultationSellableProduct,
        bookingDetail: BookingDetail,
        newReportIssueManageOption: ManageOptionPayload,
        issuesMap: Map<string, CustomerIssueType[]>,
        centerInfo: Center,
        cityId: string,
        isZoomWebSDKEnabled: boolean,
        logger: Logger,
        offer?: PackOffersResponse | { [key: string]: PackOffersResponse },
        patientsList?: Patient[],
        feedback?: Feedback,
        feedbackPageConfigV2Cache?: FeedbackPageConfigV2Cache,
        followupProducts?: ConsultationProduct[],
        feedbackQuestion?: string,
        userContext?: UserContext,
        doctorsList?: Doctor[],
        centers?: Center[],
        centerId?: string,
        canChangeCenter?: boolean,
        consultationInstruction?: ConsultationInstructionResponse[],
        isTwilioSupportedForSGT?: boolean
    ) {
        const centersList = CareUtil.getCareCenterObjFromArray(centers)
        const careSelectedCenter: Center = centerId && centersList[centerId]
        const isGP99Product = CareUtil.isGP99DoctorType(product.doctorType)
        const orderId = _.isEmpty(bookingDetail) ? null : bookingDetail.booking.cfOrderId
        let offerDetails: {
            price: ProductPrice;
            offers: OfferV2[];
        }
        if (careSelectedCenter) {
            const centerPricing: { [key: string]: { price: ProductPrice, offers: OfferV2[] } } = OfferUtil.getOfferAndPriceForCareCenter(product, [centerId], <{ [key: string]: PackOffersResponse }>offer)
            offerDetails = centerPricing[careSelectedCenter.id]
        } else {
            offerDetails = OfferUtil.getPackOfferAndPrice(product, <PackOffersResponse>offer)
        }
        const offerIds = _.map(offerDetails.offers, offer => { return offer.offerId })
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "DESKTOP")
        this.actions = this.getActions(source, isVideoEnabled, userContext, product, offerDetails, isNotLoggedIn, patientsList, bookingDetail, user, cityId, pageConfig, logger, isZoomWebSDKEnabled, followupProducts, offerIds, userAgent, careSelectedCenter, consultationInstruction, isTwilioSupportedForSGT)
        this.widgets.push(this.getTCSummaryWidget(product, bookingDetail,
            newReportIssueManageOption, issuesMap, orderId, offerDetails.price, userContext, offerIds, centerInfo, careSelectedCenter, canChangeCenter))
        if (!_.isEmpty(bookingDetail) && CareUtil.isPending(bookingDetail)) {
            if ((CareUtil.getChatEnabled(bookingDetail) || CareUtil.getRescheduleEnabled(bookingDetail) || CareUtil.getCancelEnabled(bookingDetail)) && userAgent === "MBROWSER") {
                const options = this.getSecondaryActions(userContext, bookingDetail)
                options.forEach(option => {
                    const manageOption: ManageOptions = {
                        icon: option.type === "RESCHEDULE_TC" ? "RESCHEDULE_TC" : option.type === "CANCEL_TC" ? "CANCEL_TC" : option.type === "MESSAGE" ? "MESSAGE" : "VIEW_MAP",
                        displayText: option.displayText,
                        options: [option]
                    }
                    const mageOptionsWidget = new ManageOptionsWidget(manageOption, {})
                    this.widgets.push(mageOptionsWidget)
                })
            }
        }
        let chatActive: boolean = false
        if (!_.isEmpty(bookingDetail)) {
            if (userAgent !== "DESKTOP")
                chatActive = CareUtil.getChatEnabled(bookingDetail)
            if (!_.isEmpty(bookingDetail.consultationOrderResponse.doctor)) {
                this.widgets.push(this.getDoctorSummaryWidget(bookingDetail))
            }
            if (feedback && (feedback.rating === "NOT_RATED" || feedback.rating === "DISMISSED")) {
                const feedbackProductType = bookingDetail.booking.subCategoryCode
                this.widgets.push(new ProductFeedbackWidget(feedbackPageConfigV2Cache.getQuestion(feedbackProductType), feedback.feedbackId, userAgent))
            }
        } else {
            let howItWorksItem, packContentsDetailed, packbenefits, packofferings, packSummary, whatYouGet
            if (packageProduct?.infoSection?.children) {
                packageProduct.infoSection.children.map(infoSection => {
                    switch (infoSection.type) {
                        case "PACK_STEPS": howItWorksItem = infoSection; break
                        case "PACK_CONTENTS_DETAILED": packContentsDetailed = infoSection; break
                        case "PACK_BENEFITS": packbenefits = infoSection; break
                        case "PACK_OFFERING": packofferings = infoSection; break
                        case "PACK_CONTENTS_SUMMARY": packSummary = infoSection; break
                        case "WHAT_YOU_GET": whatYouGet = infoSection; break
                    }
                })
            }
            if (isGP99Product && product.subTitle) {
                const textWidget = new TextWidget("About", product.subTitle)
                textWidget.orientation = userAgent === "DESKTOP" ? "RIGHT" : undefined
                this.widgets.push(textWidget)
            }

            if (product.consultationMode === "INCENTRE") {
                this.widgets.push((this.getCoronaInstructionWidget(userContext)))
            }
            if (!_.isEmpty(packbenefits)) {
                this.widgets.push(this.getBenefitsWidget(packbenefits, userContext))
            }
            if (!_.isEmpty(packofferings)) {
                this.widgets.push(this.getDescriptionWidget(packofferings, userContext))
            }
            if (!_.isEmpty(doctorsList)) {
                const widget = CareUtil.getDoctorListWidget(userContext, undefined, doctorsList, centersList, careSelectedCenter)
                widget && this.widgets.push(widget)
            }
            if (!isGP99Product && !_.isEmpty(offerIds)) {
                this.widgets.push(getOffersWidget("Offers Applied", offerDetails.offers, userAgent))
            }
            if (!_.isEmpty(packContentsDetailed)) {
                this.widgets.push(this.getWhatsInPackWidget(packContentsDetailed, userContext))
            }

            if (!_.isEmpty(howItWorksItem)) {
                this.widgets.push(this.getHowItWorksWidget(howItWorksItem, userContext))
            }
            if (!_.isEmpty(packSummary)) {
                this.widgets.push(this.getHowItWorksWidget(packSummary, userContext, true))
            }
            if (!_.isEmpty(whatYouGet)) {
                this.widgets.push(CareUtil.getWhatYouGetInstructionWidget(whatYouGet, userContext))
            }
        }

        if (!_.isEmpty(bookingDetail) && CareUtil.isPending(bookingDetail)) {
            const isSgtConsultation = CareUtil.isLiveSGTSessionConsultation(bookingDetail.consultationOrderResponse)
            if (!CareUtil.isPartOfMP(bookingDetail) && CareUtil.getCancelEnabled(bookingDetail)) {
                if (CareUtil.isLivePTSessionConsultation(bookingDetail.consultationOrderResponse) || isSgtConsultation) {
                    this.widgets.push({
                        ...CareUtil.getLivePTCancellationCutoffWidget(bookingDetail, userContext.userProfile.timezone),
                        orientation: userAgent === "DESKTOP" ? "RIGHT" : undefined
                    })
                } else {
                    const cancellationInfo = CareUtil.getCancellationInfo(userContext, bookingDetail)
                    if (cancellationInfo) {
                        this.widgets.push(new CalloutWidget(cancellationInfo.text, userAgent, true))
                    }
                }
            }
            if ((CareUtil.isLivePTSessionConsultation(bookingDetail.consultationOrderResponse) || isSgtConsultation) && !CareUtil.getZoomLinkEnabled(bookingDetail)) {
                this.widgets.push({
                    ...CareUtil.getLivePTSessionNoteWidget(),
                    orientation: userAgent === "DESKTOP" ? "RIGHT" : undefined
                })
            }
            if (CareUtil.isLivePTSessionConsultation(bookingDetail.consultationOrderResponse) || isSgtConsultation) {
                this.widgets.push({
                    ...CareUtil.getLivePThowItWorksWidget(userContext),
                    orientation: userAgent === "DESKTOP" ? "RIGHT" : undefined
                })
            } else {
                // To do remove it when launching couple therapist in web
                // this.widgets.push(CareUtil.getCoupleTherapyInfoWidget(product))
                // this.widgets.push(CareUtil.getPartnerUpdateInfoWidget(product, bookingDetail, patientsList, {actionType: "REFRESH_PAGE"}))
                this.widgets.push(CareUtil.getConsultationModeWidget(userContext, patientsList, user, product, bookingDetail, consultationInstruction, pageConfig, _.get(this, "actions.0.isDownloadAction", false)))
                const instructionWidget = CareUtil.getConsultationInstructionWidget(bookingDetail, pageConfig, undefined, consultationInstruction)
                if (instructionWidget && userAgent === "DESKTOP") {
                    instructionWidget.orientation = "RIGHT"
                }
                this.widgets.push(instructionWidget)
            }
        }
        if (!_.isEmpty(bookingDetail) && CareUtil.isMissed(bookingDetail) && userAgent !== "DESKTOP") {
            if ((CareUtil.getChatEnabled(bookingDetail) || CareUtil.getRescheduleEnabled(bookingDetail) || CareUtil.getCancelEnabled(bookingDetail)) && userAgent === "MBROWSER") {
                const options = this.getSecondaryActions(userContext, bookingDetail)
                options.forEach(option => {
                    const manageOption: ManageOptions = {
                        icon: option.type === "RESCHEDULE_TC" ? "RESCHEDULE_TC" : option.type === "CANCEL_TC" ? "CANCEL_TC" : option.type === "MESSAGE" ? "MESSAGE" : "VIEW_MAP",
                        displayText: option.displayText,
                        options: [option]
                    }
                    const mageOptionsWidget = new ManageOptionsWidget(manageOption, {})
                    this.widgets.push(mageOptionsWidget)
                })
            }
        }
        if (!_.isEmpty(bookingDetail) && CareUtil.isComplteted(bookingDetail)) {
            if (!CareUtil.isTherapyOnlyConsultation(bookingDetail.consultationOrderResponse)) {
                if (bookingDetail.consultationOrderResponse && bookingDetail.consultationOrderResponse.hasPrescription && !_.isEmpty(bookingDetail.consultationOrderResponse.prescription)) {
                    if (bookingDetail.consultationOrderResponse.hasDigitalDocument) {
                        let action: Action
                        action = {
                            actionType: "NAVIGATION",
                            url: ActionUtil.prescriptionUrl(bookingDetail.booking.id)
                        }
                        this.widgets.push(new NavigationCardWidget("Prescription generated", undefined, action, true, undefined, userAgent, true))
                    } else {
                        this.widgets.push(new NavigationCardWidget("Handwritten prescription provided for this consultation", undefined, undefined, true, undefined, userAgent, undefined, undefined, true))
                    }
                } else if (product.doctorType === "LC") {
                    const action: Action = {
                        actionType: "EMAIL_PLAN",
                        meta: {
                            bookingId: bookingDetail.booking.id,
                            appointmentId: bookingDetail.consultationOrderResponse.id,
                        },
                        title: "Email Plan"
                    }
                    this.widgets.push(new NavigationCardWidget("Diet & Lifestyle Plan Generated", "Email Now", action, false, undefined, userAgent))
                }
            }
            if (!_.isEmpty(bookingDetail.consultationOrderResponse.followUpContext) && bookingDetail.consultationOrderResponse.followUpContext.enabled
                && !_.isEmpty(bookingDetail.consultationOrderResponse.followUpContext.followupMembershipDetails)) {
                this.widgets.push(this.getFollowupCutoffWidget(bookingDetail.consultationOrderResponse.followUpContext.followupMembershipDetails[0].userMessage, userAgent))
            }
        }
        if (!_.isEmpty(offerIds)) {
            this.pageContext = {
                ...this.pageContext,
                "offerId": offerIds[0],
                offerIds: offerIds
            }
        }
        this.widgets = this.widgets.filter(Boolean)
        return this
    }

    public static async getView(
        source: string,
        isVideoEnabled: boolean,
        user: User,
        isNotLoggedIn: boolean,
        product: ConsultationProduct,
        pageConfig: TeleconsultationDetailsPageConfig,
        packageProduct: ConsultationSellableProduct,
        bookingDetail: BookingDetail,
        newReportIssueManageOption: ManageOptionPayload,
        issuesMap: Map<string, CustomerIssueType[]>,
        centerInfo: Center,
        cityId: string,
        isZoomWebSDKEnabled: boolean,
        logger: Logger,
        offer?: PackOffersResponse | { [key: string]: PackOffersResponse },
        patientsList?: Patient[],
        feedback?: Feedback,
        feedbackPageConfigV2Cache?: FeedbackPageConfigV2Cache,
        followupProducts?: ConsultationProduct[],
        userContext?: UserContext,
        doctorsList?: Doctor[],
        centers?: Center[],
        centerId?: string,
        canChangeCenter?: boolean,
        consultationInstruction?: ConsultationInstructionResponse[],
        isTwilioSupportedForSGT?: boolean
    ): Promise<TeleconsultationDetailsViewWeb> {

        const feedbackQuestion: string = await feedbackPageConfigV2Cache.getQuestionV2(feedback)
        const view: TeleconsultationDetailsViewWeb = new TeleconsultationDetailsViewWeb().buildView(source, isVideoEnabled, user, isNotLoggedIn, product, pageConfig, packageProduct, bookingDetail, newReportIssueManageOption, issuesMap, centerInfo, cityId, isZoomWebSDKEnabled, logger, offer, patientsList, feedback, feedbackPageConfigV2Cache, followupProducts, feedbackQuestion, userContext, doctorsList, centers, centerId, canChangeCenter, consultationInstruction, isTwilioSupportedForSGT)
        return view
    }

    private getActions(
        source: string,
        isVideoEnabled: boolean,
        userContext: UserContext,
        product: ConsultationProduct,
        offerDetails: {
            price: ProductPrice;
            offers: OfferV2[];
        },
        isNotLoggedIn: boolean,
        patientsList: Patient[],
        bookingDetail: BookingDetail,
        user: User,
        cityId: string,
        pageConfig: TeleconsultationDetailsPageConfig,
        logger: Logger,
        isZoomWebSDKEnabled?: boolean,
        followupProducts?: ConsultationProduct[],
        offerIds?: string[],
        userAgent?: UserAgent,
        selectedCenter?: Center,
        consultationInstruction?: ConsultationInstructionResponse[],
        isTwilioSupportedForSGT?: boolean,
    ): Action[] {
        const isGP99Product = CareUtil.isGP99DoctorType(product.doctorType)
        const title = isGP99Product
            ? `BOOK CONSULT ${offerDetails?.price?.listingPrice ? `@ ${RUPEE_SYMBOL}${offerDetails?.price?.listingPrice}` : ""}`
            : "Book your appointment"
        if (isNotLoggedIn === true) {
            return [
                {
                    actionType: "SHOW_ALERT_MODAL",
                    title,
                    meta: {
                        title: "Login Required!",
                        subTitle: "Please login to continue",
                        actions: [{ actionType: "LOGOUT", title: "Login" }]
                    }
                }
            ]
        }
        if (_.isEmpty(bookingDetail)) {
            const isNewCenterSpecificFlow = CareUtil.isNewCenterSpecifiFlowSupported(userContext)
            return isNewCenterSpecificFlow && !_.isEmpty(selectedCenter) ? this.getPreBookingActionsV2(product, patientsList, selectedCenter) : this.getPreBookingActions(product, patientsList, offerIds, title)
        }
        if (!CareUtil.isComplteted(bookingDetail)) {
            const status = _.get(bookingDetail, "consultationOrderResponse.consultationUserState", "")
            const isSgtConsultation = CareUtil.isLiveSGTSessionConsultation(bookingDetail.consultationOrderResponse)
            if (CareUtil.isLivePTSessionConsultation(bookingDetail.consultationOrderResponse) || isSgtConsultation) {
                if (isSgtConsultation && isTwilioSupportedForSGT) {
                    const isEnabled = CareUtil.getTwilioEnabled(bookingDetail.consultationOrderResponse.appointmentActionsWithContext)
                    const action: Action = {
                        title: "JOIN NOW",
                        actionType: "NAVIGATION",
                        url: `/sgtconverse?patientId=${bookingDetail.booking.patientId}&tcBookingId=${bookingDetail.consultationOrderResponse.bookingId}&productId=${bookingDetail.booking.productCode}`,
                        isEnabled: CareUtil.getTwilioEnabled(bookingDetail.consultationOrderResponse.appointmentActionsWithContext)
                    }
                    if (!isEnabled) {
                        return
                    }
                    return [action]
                }
                const zoomUrl = CareUtil.getZoomLink(bookingDetail)
                const isActionEnabled = CareUtil.getZoomLinkEnabled(bookingDetail)

                if (!isActionEnabled) {
                    return
                }
                const zoomWeAction = CareUtil.getZoomSdkActionFromUrl(userContext, zoomUrl, user, bookingDetail.booking.patientId, bookingDetail.consultationOrderResponse.doctorType, bookingDetail.booking.id)

                let action: Action = {
                    title: "JOIN NOW",
                    actionType: "EXTERNAL_DEEP_LINK",
                    url: zoomUrl,
                    meta: {
                        ...zoomWeAction.meta,
                        deepLinkType: "ZOOM"
                    }
                }
                if (isZoomWebSDKEnabled) {
                    action = zoomWeAction
                }
                action.isEnabled = isActionEnabled
                return [action]
            } else {
                const inProgressState = ["BOOKED", "SCHEDULED", "RESCHEDULED"].includes(status)
                const isVideoConsultation = CareUtil.isVideoBookingConsultation(bookingDetail)
                let isAndroid = false, isSupportedBrowser = false
                let browser
                if (source) {
                    const ua = useragent.parse(source)
                    isAndroid = ua.isAndroid
                    browser = ua.browser
                    isSupportedBrowser = ua.isChrome || ua.isFirefox
                }
                const appDownloadAction: Action[] = [{
                    title: "Download App to Join call",
                    actionType: "NAVIGATION_NEWTAB",
                    isDownloadAction: true, // This is used to identify whether action is download or other in consultation mode widget
                    url: isAndroid ? "https://play.google.com/store/apps/details?id=fit.cure.android" : "https://itunes.apple.com/us/app/cure-fit/id1217794588"
                }]
                const isFromPhonePe = userContext.sessionInfo.orderSource === "PHONEPE_CUREFIT_CARE"
                if (isVideoConsultation && inProgressState) {
                    if (isFromPhonePe && !isVideoEnabled) {
                        return appDownloadAction
                    } else if (!isFromPhonePe && userAgent === "MBROWSER" && (!isAndroid || (isAndroid && !isSupportedBrowser))) {
                        return appDownloadAction
                    }
                }
            }
            if (CareUtil.getVideoEnabled(bookingDetail)) {
                return CareUtil.getVideoJoinCallAction(
                    userContext,
                    patientsList,
                    user,
                    product,
                    bookingDetail,
                    consultationInstruction,
                    pageConfig,
                    logger
                )
            }
        }
        if (CareUtil.isComplteted(bookingDetail)) {
            const actions: Action[] = []
            if (!_.isEmpty(bookingDetail.consultationOrderResponse.followUpContext) && bookingDetail.consultationOrderResponse.followUpContext.enabled) {
                const parentBookingId = bookingDetail.booking.id
                const followUpConsultationId = bookingDetail.consultationOrderResponse.id
                actions.push(CareUtil.getFollowupAction(
                    userContext,
                    parentBookingId,
                    followupProducts,
                    followUpConsultationId,
                    bookingDetail.consultationOrderResponse.patient.id,
                    bookingDetail.consultationOrderResponse?.doctor?.id,
                    bookingDetail?.consultationOrderResponse?.center?.id
                ))
            }
            return actions
        }
    }

    private getDoctorSummaryWidget(bookingDetail: BookingDetail) {
        const consultationResponse: ConsultationOrderResponse = bookingDetail.consultationOrderResponse
        const doctorDetailsWidget: WidgetView & {
            image: string
            title: string,
            subTitle: string,
            actionUrl: string
        } = {
            widgetType: "DOCTOR_SUMMARY_WIDGET",
            image: consultationResponse.doctor.displayImage,
            title: `${consultationResponse.doctor.name}`,
            subTitle: consultationResponse.doctor.qualification,
            actionUrl: undefined
        }
        return doctorDetailsWidget
    }


    private getPreBookingActions(product: ConsultationProduct, patientsList: Patient[], offerIds?: string[], actionTitle?: string): Action[] {
        if (CareUtil.isTherapyProduct(product)) {
            const action = CareUtil.getTherapyConsultationAction(product.productId, patientsList, undefined)
            action.title = "Book your appointment"
            return [action]
        }
        const actionStringOnline: string = ActionUtil.selectCareDateV1(product.productId)
        const relations = CareUtil.getRelations()
        if (!_.isEmpty(patientsList)) {
            const isSelfPatientPresent = patientsList.find(patient => patient.relationship === "Self")
            const defaultRelationShip = isSelfPatientPresent ? { patientRelation: "Other" } : {}
            return [
                {
                    actionType: "SHOW_PATIENT_SELECTION",
                    title: actionTitle || "Book your appointment",
                    meta: {
                        url: actionStringOnline,
                        action: {
                            actionType: "NAVIGATION",
                            url: actionStringOnline
                        },
                        patientsList: patientsList,
                        relations: isSelfPatientPresent ? CareUtil.getOtherRelation() : relations,
                        emergencyContactRelations: CareUtil.getEmergencyRelations(),
                        guardianRelations: CareUtil.getGuardianRelations(),
                        reqParams: {
                            formUserType: "CARE_USER",
                            ...defaultRelationShip
                        }
                    }
                }]
        } else {
            return [
                {
                    actionType: "ADD_PATIENT",
                    title: actionTitle || "Book your appointment",
                    meta: {
                        url: actionStringOnline,
                        action: {
                            actionType: "NAVIGATION",
                            url: actionStringOnline
                        },
                        relations: relations,
                        emergencyContactRelations: CareUtil.getEmergencyRelations(),
                        guardianRelations: CareUtil.getGuardianRelations(),
                        reqParams: {
                            formUserType: "CARE_USER"
                        }
                    }
                }]
        }
    }

    private getPreBookingActionsV2(product: Product, patientsList: Patient[], selectedCenter: Center): Action[] {
        const actionUrl = ActionUtil.selectCareDateV1(product.productId)
        const actionStringOnline: string = `${actionUrl}&centerId=${selectedCenter.id}`
        const relations = CareUtil.getRelations()
        const actionMeta = selectedCenter ? {
            doctorType: (<ConsultationProduct>product).doctorType,
            productId: product.productId,
            id: product.productId,
            subCategoryCode: product.categoryId,
            centerInfo: {
                id: selectedCenter.id,
                name: selectedCenter.name
            },
            showCenterPricing: true
        } : {}
        if (!_.isEmpty(patientsList)) {
            const isSelfPatientPresent = patientsList.find(patient => patient.relationship === "Self")
            const defaultRelationShip = isSelfPatientPresent ? { patientRelation: "Other" } : {}
            return [
                {
                    actionType: "SHOW_PATIENT_SELECTION",
                    title: "Book your appointment",
                    meta: {
                        url: actionStringOnline,
                        action: {
                            actionType: "UPDATE_CARE_CENTER",
                            url: actionStringOnline,
                            meta: actionMeta
                        },
                        patientsList: patientsList,
                        relations: isSelfPatientPresent ? CareUtil.getOtherRelation() : relations,
                        emergencyContactRelations: CareUtil.getEmergencyRelations(),
                        guardianRelations: CareUtil.getGuardianRelations(),
                        reqParams: {
                            formUserType: "CARE_USER",
                            ...defaultRelationShip
                        }
                    }
                }]
        } else {
            return [
                {
                    actionType: "ADD_PATIENT",
                    title: "Book your appointment",
                    meta: {
                        url: actionStringOnline,
                        action: {
                            actionType: "UPDATE_CARE_CENTER",
                            url: actionStringOnline,
                            meta: actionMeta
                        },
                        relations: relations,
                        emergencyContactRelations: CareUtil.getEmergencyRelations(),
                        guardianRelations: CareUtil.getGuardianRelations(),
                        reqParams: {
                            formUserType: "CARE_USER"
                        }
                    }
                }]
        }
    }

    private getFollowupCutoffWidget(userMessage: string, userAgent?: UserAgent): WidgetView {
        // const text = `Free followup till ${momentTz.tz(cutoff, "Asia/Kolkata").format("DD MMM, h:mm a")}`
        const options: ManageOptionPayload[] = [{
            displayText: userMessage,
            isEnabled: false,
            type: "DONE"
        }]
        if (userAgent === "DESKTOP") {
            options[0].orientation = "RIGHT"
        }
        const manageoptions: ManageOptions = {
            icon: "TIME",
            displayText: userMessage,
            options: options,
        }
        if (userAgent === "DESKTOP") {
            manageoptions.orientation = "RIGHT"
        }
        const calloutWidget = new ManageOptionsWidget(manageoptions, {})
        calloutWidget.isDisabled = true
        return calloutWidget
    }

    private getInstructionWidget(bookingDetail: BookingDetail, pageConfig: TeleconsultationDetailsPageConfig, userAgent?: UserAgent) {
        return CareUtil.getConsultationInstructionWidget(bookingDetail, pageConfig, userAgent)
    }

    private getTCSummaryWidget(product1: ConsultationProduct, bookingDetail: BookingDetail,
        newReportIssueManageOption: ManageOptionPayload, issuesMap: Map<string, CustomerIssueType[]>,
        orderId: string, price: ProductPrice, userContext: UserContext, offerIds?: string[], centerInfo?: Center, careSelectedCenter?: Center, canChangeCenter?: boolean) {
        product1.consultationMode === "ONLINE"
        let manageOptionsView: { manageOptions: ManageOptions, meta: any } = null
        let manageOptions: ManageOptions
        if (newReportIssueManageOption) {
            manageOptions = {
                displayText: "Manage",
                options: [newReportIssueManageOption]
            }
            manageOptionsView = {
                manageOptions: manageOptions,
                meta: undefined
            }
        } else {
            if (CareUtil.isComplteted(bookingDetail) || CareUtil.isPending(bookingDetail)) {
                manageOptionsView = this.getTCManageOptions(bookingDetail, orderId, product1.productId, issuesMap)
            }
        }
        return this.getTCSummaryWidgetWeb(userContext, product1, bookingDetail, manageOptionsView, price, offerIds, centerInfo, careSelectedCenter, canChangeCenter)
    }

    private getTherapySlotSelectionAction(centerInfo: Center): { optionType: string, actions: Action[], text: string } | {} {
        const selectMindDateAction = this.actions && this.actions.find(action => {
            return action.actionType === "ADD_PATIENT" || action.actionType === "SHOW_PATIENT_SELECTION" || action.actionType === "NAVIGATION" && action.url.indexOf("selectCareDateV1") !== -1
        })
        return selectMindDateAction ? {
            optionType: "CENTER_SLOT_SELECTION",
            actions: [
                {
                    actionType: "SELECT_MIND_DATE",
                    meta: {
                        ...selectMindDateAction.meta,
                        centerId: centerInfo ? centerInfo.id : -1
                    },
                    title: "Book your appointment"
                }
            ],
            text: "Select Preferred Slot"
        } : {}
    }

    private getSlotSelectionAction(centerInfo: Center, careSelectedCenter?: Center): { optionType: string, actions: Action[], text: string } | {} {
        const selectDateAction = this.actions && this.actions.find((action) => {
            return action.actionType === "ADD_PATIENT" || action.actionType === "SHOW_PATIENT_SELECTION"
        })
        return selectDateAction ? {
            optionType: "CENTER_SLOT_SELECTION",
            actions: [
                {
                    actionType: "SELECT_CARE_DATE",
                    meta: {
                        ...selectDateAction.meta,
                        showCenterPricing: !_.isEmpty(careSelectedCenter),
                        centerId: centerInfo ? centerInfo.id : -1
                    },
                    title: "Book your appointment"
                }
            ],
            text: "Select Preferred Slot"
        } : {}
    }

    private getTherapyPickerOptions(centerInfo: Center) {
        const pickerOptions = []
        const patientSelectionAction = this.actions && this.actions.find(action => {
            return action.actionType === "ADD_PATIENT" || action.actionType === "SHOW_PATIENT_SELECTION"
        })
        if (!_.isEmpty(patientSelectionAction)) {
            pickerOptions.push({
                optionType: "PATIENT_SELECTION",
                actions: this.actions,
                text: "Add Details",
            })
        }
        const slotAction = this.getTherapySlotSelectionAction(centerInfo)
        if (!_.isEmpty(slotAction)) {
            pickerOptions.push(slotAction as { optionType: string, actions: Action[], text: string })
        }
        return pickerOptions
    }

    private getPickerOptions(centerInfo: Center, careSelectedCenter?: Center, canChangeCenter?: boolean) {
        const pickerOptions = []
        pickerOptions.push({
            optionType: "PATIENT_SELECTION",
            actions: this.actions,
            text: "For whom",
        })
        const slotAction = this.getSlotSelectionAction(centerInfo, careSelectedCenter)
        if (!_.isEmpty(slotAction)) {
            pickerOptions.push(slotAction as { optionType: string, actions: Action[], text: string })
        }
        if (centerInfo) {
            pickerOptions.push({
                optionType: "CENTER_SELECTION",
                text: `Center Location | ${centerInfo.name}`,
                actions: [],
            })
        } else if (!_.isEmpty(careSelectedCenter)) {
            pickerOptions.push({
                optionType: "CENTER_SELECTION",
                text: careSelectedCenter.name,
                actions: canChangeCenter ? [{
                    actionType: "GO_BACK_ACTION"
                }] : [],
            })
        }
        return pickerOptions
    }

    private getSecondaryActions(userContext: UserContext, bookingDetail: BookingDetail): ManageOptionPayload[] {
        const options: any[] = []
        const isPartOfMP = CareUtil.isPartOfMP(bookingDetail)
        const resheduleEnabled = !isPartOfMP && CareUtil.getRescheduleEnabled(bookingDetail)
        const cancelEnabled = !isPartOfMP && CareUtil.getCancelEnabled(bookingDetail)
        const videoCall: boolean = bookingDetail.booking.subCategoryCode === "CF_ONLINE_CONSULTATION"
        const secondaryText: string = videoCall ? "Video call" : "VIEW MAP"
        const consultationDetail = bookingDetail.consultationOrderResponse
        const doctorData = consultationDetail?.doctor
        const center = consultationDetail?.center
        if (!videoCall) {
            options.push({
                displayText: secondaryText,
                action: center ? {
                    actionType: "NAVIGATION_NEWTAB",
                    url: `${center.placeUrl}`
                } : undefined,
                type: videoCall ? "VIDEO_CALL" : "NAVIGATION"
            })
        }
        if (CareUtil.getChatEnabled(bookingDetail)) {
            const chatAction = CareUtil.getChatMessageActionWithoutContext(userContext, consultationDetail.patient.id, doctorData?.name, CareUtil.getChatChannel(bookingDetail), doctorData?.displayImage, doctorData?.qualification, undefined, bookingDetail.booking.id)

            options.push({
                displayText: "MESSAGE",
                isEnabled: true,
                action: {
                    actionType: "OPEN_MESSAGE_DRAWER",
                    url: chatAction.url
                },
                type: "MESSAGE"
            })
        }

        if (resheduleEnabled) {
            options.push({
                displayText: "RESCHEDULE",
                isEnabled: true,
                type: "RESCHEDULE_TC",
                action: {
                    actionType: "SELECT_CARE_DATE",
                    url: `curefit://rescheduleTc?parentBookingId=${bookingDetail.booking.id}&patientId=${bookingDetail.booking.patientId}&isReschedule=true&productId=${bookingDetail.booking.productCode}${center ? `&centerId=${center.id}` : ""}`
                }
            })
        }
        if (cancelEnabled) {
            options.push({
                displayText: "CANCEL",
                isEnabled: true,
                type: "CANCEL_TC",
                action: {
                    url: `curefit://cancelTeleconsultation?tcBookingId=${bookingDetail.booking.id}&packId=${bookingDetail.booking.productCode}`,
                    actionType: "CANCEL_TC"
                }
            })
        }
        return options
    }

    private getSummaryWidgetTitle(bookingDetail: BookingDetail, product: ConsultationProduct, userAgent: UserAgent) {
        return userAgent === "DESKTOP" ? `${product.title || product.name}${!_.isEmpty(bookingDetail) ? ` | ${bookingDetail.booking.status.toLowerCase()}` : ""}` : (product.title || product.name)
    }

    private getTCSummaryWidgetWeb(userContext: UserContext, product1: ConsultationProduct, bookingDetail: BookingDetail,
        manageOptionsView: { manageOptions: ManageOptions, meta: any }, price: ProductPrice, offerIds?: string[],
        centerInfo?: Center, careSelectedCenter?: Center, canChangeCenter?: boolean) {
        const product: ConsultationProduct = clone(product1) // known hack
        let pickerOptions: any[] = []
        let secondaryActions: any[] = []
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "DESKTOP")
        const loggedIn: boolean = _.get(userContext, "sessionInfo.isUserLoggedIn", false)
        const productType = !_.isEmpty(bookingDetail) ? bookingDetail.consultationOrderResponse.consultationProduct.doctorType : product.doctorType
        const vertical = CareUtil.getVerticalForConsultation(productType)
        if (loggedIn) {
            if (_.isEmpty(bookingDetail)) {
                pickerOptions = CareUtil.isTherapyProduct(product1) ? this.getTherapyPickerOptions(centerInfo) : this.getPickerOptions(centerInfo, careSelectedCenter, canChangeCenter)
            } else {
                pickerOptions = []
                if (vertical === "CAREFIT") {
                    pickerOptions.push({ text: bookingDetail.consultationOrderResponse.patient.name, optionType: "PATIENT_SELECTION_DISABLED" })
                }
                pickerOptions.push({ text: TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, bookingDetail.consultationOrderResponse.startTime, "Do MMM, h:mm a"), optionType: "CENTER_SLOT_SELECTION_DISABLED" })
                if (product1.consultationMode === "INCENTRE") {
                    pickerOptions.push({ text: `Center Location | ${bookingDetail.consultationOrderResponse.center.name}`, optionType: "CENTER_SELECTION_DISABLED" })
                }
            }
            if (userAgent === "DESKTOP" && !_.isEmpty(bookingDetail)) {
                secondaryActions = this.getSecondaryActions(userContext, bookingDetail)
            }
        }

        const tcSummaryWidget: WidgetView & {
            productId: string
            title: string,
            subTitle: string,
            metaText?: string,
            price: ProductPrice,
            image: string,
            meta: any
            manageOptions: ManageOptions,
            offerId: string,
            offerIds: string[],
            pickerOptions?: any[],
            breadcrumb?: { text: string, link?: string }[],
            actions: Action[],
            secondaryActions?: any[],
            orientation?: Orientation,
            doctorSummary?: {}
        } = {
            widgetType: "TELECONSULTATION_SUMMARY_WIDGET",
            title: this.getSummaryWidgetTitle(bookingDetail, product, userAgent),
            productId: product.productId,
            price: !_.isEmpty(bookingDetail) ? null : price,
            image: product.heroImageUrl,
            manageOptions: !_.isEmpty(manageOptionsView) ? manageOptionsView.manageOptions : null,
            meta: !_.isEmpty(manageOptionsView) ? manageOptionsView.meta : null,
            subTitle: this.getSummarySubtitle(userContext, bookingDetail, product, userAgent),
            offerId: !_.isEmpty(offerIds) ? offerIds[0] : undefined,
            offerIds,
            breadcrumb: this.getBreadCrumb(userAgent, product),
            actions: this.actions
        }
        if (pickerOptions.length && userAgent === "DESKTOP") {
            tcSummaryWidget.pickerOptions = pickerOptions
        }
        if (secondaryActions.length) {
            tcSummaryWidget.secondaryActions = secondaryActions
        }
        if (userAgent === "DESKTOP") {
            tcSummaryWidget.orientation = "RIGHT"
        }
        if (!_.isEmpty(bookingDetail) && loggedIn) {
            tcSummaryWidget.doctorSummary = {
                image: _.get(bookingDetail, "consultationOrderResponse.doctor.displayImage", ""),
                title: _.get(bookingDetail, "consultationOrderResponse.doctor.name", ""),
                subTitle: _.get(bookingDetail, "consultationOrderResponse.doctor.qualification", "")
            }
        }
        return tcSummaryWidget
    }

    private getBreadCrumb(userAgent: UserAgent, product: ConsultationProduct) {
        if (userAgent === "DESKTOP") {
            if (CareUtil.isTherapyProduct(product)) {
                return [{ text: "Home", link: "/" }, { text: "Mind", link: "/mind" }, { text: "Consultation" }]
            } else if (CareUtil.isLivePTProduct(product) || CareUtil.isLiveSGTProduct(product)) {
                return [{ text: "Home", link: "/" }, { text: "Cult", link: "/cult" }, { text: "Consultation" }]
            }
            return [{ text: "Home", link: "/" }, { text: "Care", link: "/care" }, { text: "Consultation" }]
        }
        return []
    }

    private getSummarySubtitle(userContext: UserContext, bookingDetail: BookingDetail, product: ConsultationProduct, userAgent: UserAgent): string {
        const tz = userContext.userProfile.timezone
        if (userAgent === "MBROWSER") {
            if (!_.isEmpty(bookingDetail)) {
                if (bookingDetail.booking.status === "CANCELLED") {
                    return "Consultation got cancelled"
                } else if (bookingDetail.consultationOrderResponse.startTime) {
                    if (CareUtil.isMindDoctorType(product.doctorType) || CareUtil.isPsychiatry(product.doctorType)) {
                        const duration = TimeUtil.diffInMinutes(tz, TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(bookingDetail.consultationOrderResponse.startTime), "YYYY-MM-DD HH:mm:ss"), TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(bookingDetail.consultationOrderResponse.endTime), "YYYY-MM-DD HH:mm:ss"))
                        return duration + " mins"
                    }
                    return `For ${bookingDetail.consultationOrderResponse.patient.name} | ${TimeUtil.formatEpochInTimeZone(tz, bookingDetail.consultationOrderResponse.startTime, "DD MMM, h:mm a")}`
                } else if (bookingDetail.consultationOrderResponse.preferredStartTime) {
                    if (CareUtil.isMindDoctorType(product.doctorType) || CareUtil.isPsychiatry(product.doctorType)) {
                        const duration = TimeUtil.diffInMinutes(tz, TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(bookingDetail.consultationOrderResponse.preferredStartTime), "YYYY-MM-DD HH:mm:ss"), TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(bookingDetail.consultationOrderResponse.preferredEndTime), "YYYY-MM-DD HH:mm:ss"))
                        return duration + " mins"
                    }
                    return `For ${bookingDetail.consultationOrderResponse.patient.name} | ${TimeUtil.formatEpochInTimeZone(tz, bookingDetail.consultationOrderResponse.preferredStartTime, "DD MMM, h:mm a")}`
                }
            } else {
                return null
            }
        } else {
            return null
        }
    }

    private getCutOffTime(userContext: UserContext, bookingDetail: BookingDetail): string {
        const time = _.get(bookingDetail, "consultationOrderResponse.appointmentActionsWithContext.cancelActionWithContext.action.thresholdEpoch")
        return TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, time, "h:mm a")
    }

    private getTCManageOptions(bookingDetail: BookingDetail, orderId: string, productId: string, issuesMap: Map<string, CustomerIssueType[]>): { manageOptions: ManageOptions, meta: any } {
        const options: ManageOptionPayload[] = []
        options.push(
            {
                isEnabled: true,
                displayText: "Need Help",
                type: "REPORT_ISSUE",
                meta: CareUtil.getTCIssueList(issuesMap),
                action: SUPPORT_DEEP_LINK,
            }
        )

        const manageOptions: ManageOptions = {
            displayText: "...",
            options: options
        }

        const meta: any = {
            orderId: orderId,
            productId: productId,
            tcBookingId: bookingDetail.booking.id
        }
        return { manageOptions: manageOptions, meta: meta }
    }

    private getHowItWorksWidget(packInfo: ManagedPlanPackInfo, userContext?: UserContext, hideSepratorLines?: boolean): ProductListWidget {
        const header: Header = {
            title: packInfo.title,
            color: "#000000"
        }
        const infoCards: InfoCard[] = []
        packInfo.children.forEach(item => {
            infoCards.push({
                subTitle: item.desc,
                icon: item.imageUrl
            })
        })
        const userAgent = _.get(userContext, "sessionInfo.userAgent", "APP")
        const orientation: Orientation = userAgent === "DESKTOP" ? "RIGHT" : undefined
        return new ProductListWidget("SMALL", header, infoCards, undefined, undefined, orientation, hideSepratorLines)
    }

    private getCoronaInstructionWidget(userContext: UserContext): InstructionsWidget {
        const header: Header = {
            title: "Important Advisory",
            color: "#000000"
        }
        const userAgent = _.get(userContext, "sessionInfo.userAgent", "APP")
        return new InstructionsWidget(CareUtil.getCoronaAdvisoryInstructions(), userAgent, true, [], true, header)
    }

    private getWhatsInPackWidget(packContentsDetailed: ManagedPlanPackInfo, userContext?: UserContext): ProductGridWidget {
        const header: Header = {
            title: packContentsDetailed.title,
            color: "#000000"
        }
        const cards: GradientCard[] = []
        packContentsDetailed.children.forEach(item => {
            cards.push({
                title: item.title,
                subTitle: item.desc,
                shadowColor: item.shadowColor,
                gradientColors: item.gradientColors,
                icon: item.type
            })
        })
        const userAgent = _.get(userContext, "sessionInfo.userAgent", "APP")
        const orientation: Orientation = userAgent === "DESKTOP" ? "RIGHT" : undefined
        return new ProductGridWidget("GARDIENT_CARD", header, cards, undefined, orientation, false)
    }

    private getBenefitsWidget(packbenefits: ManagedPlanPackInfo, userContext?: UserContext): BenefitsWidget {
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "DESKTOP")
        const widget: BenefitsWidget = {
            widgetType: "BENEFITS_WIDGET",
            title: packbenefits.title,
            description: packbenefits.children[0].desc,
            tags: packbenefits.children[0].bullets
        }
        if (userAgent === "DESKTOP") {
            widget.orientation = "RIGHT"
        }
        if (userAgent === "MBROWSER") {
            widget.hideSepratorLines = false
        }
        return widget
    }

    private getDescriptionWidget(packofferings: ManagedPlanPackInfo, userContext?: UserContext) {
        const header: Header = {
            title: packofferings.title,
            color: "#000000"
        }
        const cards: InfoCard[] = []
        packofferings.children.forEach(item => {
            cards.push({
                title: item.title,
                subTitle: item.desc,
                icon: item.imageUrl
            })
        })
        const userAgent = _.get(userContext, "sessionInfo.userAgent", "APP")
        const orientation: Orientation = userAgent === "DESKTOP" ? "RIGHT" : undefined
        return new ProductListWidget("DETAIL", header, cards, undefined, undefined, orientation)
    }

}
export default TeleconsultationDetailsViewWeb
