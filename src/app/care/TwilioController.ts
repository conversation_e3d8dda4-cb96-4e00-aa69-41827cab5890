import * as express from "express"
import { Container, inject } from "inversify"
import { controller, httpPost } from "inversify-express-utils"
import { Constants } from "@curefit/base-utils"
import { IQueueService, SQS_CLIENT_TYPES } from "@curefit/sqs-client"
import { Logger, BASE_TYPES } from "@curefit/base"

const twilio = require("twilio")
const shouldValidateTwilio: boolean = process.env.ENVIRONMENT === "PRODUCTION" ? false : false
process.env.TWILIO_AUTH_TOKEN = "d78aa56a7b33cafce1e57f700d68110a"

export function controllerFactory(kernel: Container) {
    @controller("/twilio",
        twilio.webhook({ validate: shouldValidateTwilio })
    )
    class TwilioController {
        constructor(@inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(SQS_CLIENT_TYPES.QueueService) private queueService: IQueueService
        ) {

        }


        @httpPost("/webhook/post")
        public async webhookPost(req: express.Request, res: express.Response): Promise<boolean> {
            const key: string = req.query.key
            if (key !== "ac62732f-ec07-4525-929b-8a3dc7fe35b3") {
                this.logger.info("Invalid key for twilio webhook :: " + key)
                return false
            }
            // const authKey = req.headers["authorization"]
            // if (authKey === Constants.getFuseWebhookSecret()) {
            const queueName = Constants.getSQSQueue("ALBUS_TWILIO_WEBHOOK")
            return this.queueService.sendMessage(queueName, req.body)
            // } else {
            // return Promise.reject("Auth failiure")
            // }

        }


    }
    return TwilioController
}

export default controllerFactory

