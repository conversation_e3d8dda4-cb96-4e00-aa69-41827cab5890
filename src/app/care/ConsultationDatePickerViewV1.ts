import { Action, ActionType, ProductListWidget } from "../common/views/WidgetView"
import { ConsultationProduct, DOCTOR_TYPE, Patient } from "@curefit/care-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { TimeUtil } from "@curefit/util-common"
import * as _ from "lodash"
import {
    BookingDetail,
    Center,
    Doctor,
    SGTWorkoutDetails,
    IHealthfaceService,
    TCAvailableSlotsDetails,
    TCAvailableSlotsResponse
} from "@curefit/albus-client"
import { CareDatePickerView, PageWidget } from "../page/Page"

import {
    AvailableProductTags, BannerCarouselWidget,
    CalloutPageWidget,
    DatesAvailableWidget,
    DateWiseSlots,
    PreferedDoctor,
    PreferedDoctorListWidget,
    TimeSlot,
    TimeSlotCategory
} from "../page/PageWidgets"
import { CareUtil, SGTClass } from "../util/CareUtil"
import { UserContext } from "@curefit/userinfo-common"
import { AnnouncementView, AnnouncementDetails } from "../announcement/AnnouncementViewBuilder"
import { NEW_SPECIALITY_BOOKING_PAGE_SUPPORTED } from "@curefit/base-utils"

import {
    DoctorInfoFooterWidget,
    WidgetView,
    TooltipAnnouncement,
    ToggleBarWidget,
    ClassByTimeV2, CultClassRecommendationWidgetView, IWidgetType, ClassByCenter
} from "@curefit/apps-common"
import AppUtil, { AppFont } from "../util/AppUtil"
import { AnnouncementBusiness } from "../announcement/AnnouncementBusiness"
import { IHerculesService } from "@curefit/hercules-client"
import { SimpleWod } from "@curefit/fitness-common"
import CultUtil from "../util/CultUtil"
import { ClassScheduleResponse, CULT_AREA_TOOL_TIP, TimeSlotOption } from "../cult/ClassListViewBuilderV2"
import { LiveFitWorkoutFormat } from "@curefit/diy-common"
import { LiveFitMasterClassFormat } from "../util/LiveUtil"
import { isNil } from "lodash"
import { ISegmentService } from "@curefit/vm-models"
import * as momentTz from "moment-timezone"

export interface ClassByDateV2SGT {
    id: string
    classByTimeList: ClassByTimeV2SGT[]
    recommendationWidget?: CultClassRecommendationWidgetView
    timeSlots?: any[]
    formats?: any[]
}

export interface ClassByTimeV2SGT {
    id: string
    classes: SGTClass[]
}

export interface SgtClassSchedule extends ClassScheduleResponse {
    classByDateMap: { [id: string]: ClassByDateV2SGT }
    timeSlotOptions: TimeSlotOption[]
    trainers?: any
    workoutCategoryFilters: { id: number, name: string, displayText: string }[]
    liveClassFormatFilter?: Array<LiveFitWorkoutFormat | LiveFitMasterClassFormat>
}

class ConsultationDatePickerViewV1 {

    public datePicker: CareDatePickerView

    async buildView(
        userContext: UserContext,
        product: ConsultationProduct,
        daywiseSlots: TCAvailableSlotsResponse,
        isMultCenterSupported: boolean,
        center: Center,
        patientId: string,
        parentBookingId: number,
        isReschedule: string,
        isExternal: string,
        followUpConsultationId: string,
        bookingDetail: BookingDetail,
        healthfaceService: IHealthfaceService,
        offerIds?: string,
        announcementView?: AnnouncementView,
        showChangeDoctorAction?: boolean,
        isCenterChangeRestricted?: boolean,
        isCareCenterBrowseFlowSupported?: boolean,
        availableProductTags?: AvailableProductTags[],
        hasTrainerRecommendations?: boolean,
        announcementBusiness?: AnnouncementBusiness,
        hideCenterDropDown?: boolean,
        herculesService?: IHerculesService,
        segmentService?: ISegmentService,
        doctorId?: String
    ) {

        const widgets: PageWidget[] = [], footerWidget: (PageWidget | WidgetView)[] = []
        const preferredDoctorMap: { [key: string]: PreferedDoctor } = this.getPreferedDoctorMap(daywiseSlots.preferredDoctorMap)
        const inActivePreferredDoctorMap: { [key: string]: PreferedDoctor } = this.getPreferedDoctorMap(daywiseSlots.inactivePreferredDoctorMap)
        const patientPromise = healthfaceService.getPatientDetails(Number(patientId))
        const tooltipAnnouncement = await this.getTooltipAnnouncementSupported(product, userContext, announcementBusiness, showChangeDoctorAction)
        let consultAgentWidgetPresent = false
        let actionButtonPadding = 0
        if (!CareUtil.isSugarfitPhleboConsult(product?.productId) && !CareUtil.isSugarfitExperienceCenterProduct(product) && !CareUtil.isSugarfitExperienceCenterDiagnosticProduct(product) && AppUtil.isSfCoachPenaltySupported(userContext) && !_.isNil(doctorId) && (product?.doctorType === "LC" || product?.doctorType === "GP")) {
            const doctor: Doctor = await healthfaceService.getDoctorDetails(Number(doctorId), undefined, undefined, "SUGARFIT")
            widgets.push({
                widgetType: "SF_CONSULT_AGENT_WIDGET" as any,
                name: doctor?.name,
                picUrl: doctor?.displayImage,
                designation: product?.doctorType === "LC" ? "Personal Health Coach" : "Personal Doctor",
                doctorType: product?.doctorType
            })
            consultAgentWidgetPresent = true
        } else if (!isCenterChangeRestricted) {
            actionButtonPadding = 20
        }
        const isOptumUser = await AppUtil.doesUserBelongToOptumCorpSegment(segmentService, userContext)
        widgets.push(await this.availableDatesWidget(
            userContext,
            product,
            daywiseSlots,
            parentBookingId,
            isReschedule,
            center,
            patientId,
            isExternal,
            followUpConsultationId,
            preferredDoctorMap,
            inActivePreferredDoctorMap,
            offerIds,
            availableProductTags,
            tooltipAnnouncement,
            herculesService,
            healthfaceService,
            patientPromise,
            doctorId,
            consultAgentWidgetPresent,
            actionButtonPadding,
            isOptumUser
        ))

        const centerId = CareUtil.isCenterIdSkipableProduct(product) ? undefined : center?.id ? center.id : undefined
        if (_.isEmpty(preferredDoctorMap) || CareUtil.isTherapyOnlyDoctorType(product.doctorType) || product.tenant !== "CULTFIT") {
            if (product.consultationMode === "ONLINE") {
                let text = "Visit a hospital immediately for any medical emergencies"
                if (CareUtil.isLiveSGTDoctorType(product.doctorType)) {
                    if (AppUtil.isCultSGTUpgradeAppSupported(userContext)) {
                        text = "Over 44,000 online GX user sessions have been rated \"Awesome\" by users"
                        const calloutWidget = new CalloutPageWidget("BLUE_BULLET", text)
                        calloutWidget.subTitleStyle = {
                            color: "#33363f",
                            fontSize: 13,
                            fontFamily: AppFont.Regular,
                        }
                        footerWidget.push(calloutWidget)
                    }
                } else if (CareUtil.isAnxietyTherapy(product?.productId)) {
                    text = "Note: This session is only available via video/telephonic mode"
                    footerWidget.push(new CalloutPageWidget("VIDEO", text))
                } else {
                    if (CareUtil.isTransformDoctorType(product.doctorType)) {
                        let description = "Your Transform Coach"
                        if (!_.isNil(doctorId) && !_.isNil(segmentService)) {
                            const response = await healthfaceService.getDoctorDetails(Number(doctorId), null, null, "TRANSFORM", null)
                            if (product.doctorType === "NUTRITIONIST".toString()) {
                                description = "Your Nutritionist"
                            } else if (product.doctorType === "FITNESS_COACH".toString()) {
                                description = "Your Fitness Coach"
                            } else if (await AppUtil.doesUserBelongToTransformBootcampSegment(userContext, segmentService)) {
                                description = "Your Lifestyle Coach"
                            }
                            footerWidget.push(new DoctorInfoFooterWidget(response.displayImage, response.name, description))
                        }
                    }
                    if (CareUtil.isMindDoctorType(product.doctorType) || CareUtil.isPsychiatry(product.doctorType)) {
                        text = `You can join the call from any comfortable location using the cult.fit app${CareUtil.isCoupleTherapist(product.doctorType) ? ` (Partners need to join the session from the same device)` : ""}`
                    }
                }
            } else {
                if (CareUtil.isSugarfitExperienceCenterProduct(product) || CareUtil.isSugarfitExperienceCenterDiagnosticProduct(product)) {
                    // Don't add anything
                } else {
                    footerWidget.push(new CalloutPageWidget("LOCATION", center.address, center.name, "VIEW MAP", `curefit://externalDeepLink?placeUrl=${center.placeUrl}`))
                }
            }
        } else {
            const action = showChangeDoctorAction ? await this.getChangeDoctorAction(userContext, product, parentBookingId, isReschedule, centerId, patientId, hasTrainerRecommendations, _.values(preferredDoctorMap), patientPromise) : undefined
            if ((CareUtil.isLivePTDoctorType(product.doctorType) || CareUtil.isLiveSGTDoctorType(product.doctorType)) && AppUtil.livePTFooterWidgetSupported(userContext)) {
                footerWidget.push(this.getDoctorInfoFooterWidget(_.values(daywiseSlots.preferredDoctorMap), action))
            } else {
                footerWidget.push(new PreferedDoctorListWidget(_.values(preferredDoctorMap), action))
            }
        }


        this.datePicker = {
            noSlotsInfo: {
                title: "No slots available",
                subtitle: "Please check for slots on other days",
                imageUrl: "/image/transform/slot_unavailable.png"
            },
            key: product.productId,
            header: this.getDatePickerHeader(userContext, isCenterChangeRestricted, center, product, isCareCenterBrowseFlowSupported, isMultCenterSupported, hideCenterDropDown),
            widgets: widgets,
            footerWidget: this.showFooterWidget(product, preferredDoctorMap) ? footerWidget : undefined,
            announcementView: announcementView,
            tooltipAnnouncement: tooltipAnnouncement
        }
        return this.datePicker
    }

    private showFooterWidget(product: ConsultationProduct, preferredDoctorMap: { [key: string]: PreferedDoctor }): boolean {
        if (product?.productId === "SUGAR002") {
            return false
        }
        if (_.isEmpty(preferredDoctorMap) && CareUtil.isLivePTDoctorType(product.doctorType)) {
            return false
        }
        return true
    }

    private async getTooltipAnnouncementSupported(product: ConsultationProduct, userContext: UserContext, announcementBusiness?: AnnouncementBusiness, showChangeDoctorAction?: boolean) {
        if (AppUtil.isChangeTrainerTooltipSupported(userContext) && CareUtil.isLivePTDoctorType(product.doctorType) && showChangeDoctorAction && announcementBusiness) {
            const announcementDetails: TooltipAnnouncement = <TooltipAnnouncement>await announcementBusiness.createAndGetChangeTrainerTooltipAnnouncementDetails(userContext)
            if (announcementDetails.state === "CREATED")
                return announcementDetails
        }
        return undefined
    }

    private async getChangeDoctorAction(userContext: UserContext, product: ConsultationProduct, parentBookingId: number, isReschedule: string, centerId: number, patientId: string, hasTrainerRecommendations: boolean, preferedDoctors: PreferedDoctor[], patientPromise: Promise<Patient>) {
        if (CareUtil.isLivePTDoctorType(product.doctorType) || CareUtil.isLiveSGTDoctorType(product.doctorType)) {
            const patient = await patientPromise
            const showChangeTrainerModal = hasTrainerRecommendations || AppUtil.isLivePTCrossGenderMatchingSupported(userContext, patient.gender)
            if (AppUtil.isLivePTTrainerRecommendationSupported(userContext) && showChangeTrainerModal) {
                return {
                    actionType: "SHOW_CHANGE_DOCTOR_MODAL",
                    title: "CHANGE TRAINER",
                    meta: {
                        doctorTypes: product.doctorType,
                        subCategoryCode: "LIVE_PERSONAL_TRAINING",
                        productId: product.productId,
                        parentBookingId: parentBookingId,
                        patientId: patientId,
                        excludeDoctorIds: [preferedDoctors[0].id]
                    },
                    analyticsData: {
                        actionType: "SHOW_CHANGE_TRAINER_PAGE",
                        productId: product.productId
                    },

                }
            }
            return {
                actionType: "CHANGE_DOCTOR",
                title: "CHANGE TRAINER",
                meta: {
                    doctorType: product.doctorType,
                    productId: product.productId,
                    parentBookingId: parentBookingId,
                    patientId: patientId,
                    excludePreferredDoctor: true
                },
                changeTrainerToast: "No trainer recommendations available for this format. Select a slot and we will find the best available trainer.",
                analyticsData: {
                    actionType: "AUTO_ASSIGN_TRAINER",
                    productId: product.productId
                }
            }
        }
        // disable change trainer for offline pt as its breaking
        if (!CareUtil.isPTDoctorType(product.doctorType)) {
            return CareUtil.specialistListingAction(userContext, product, false, Number(patientId), parentBookingId, isReschedule && isReschedule === "true", true, true, "#FDFDFD,#EEF2F5")
        }
    }

    private getDatePickerHeader(
        userContext: UserContext,
        isCenterChangeRestricted: boolean,
        center: Center,
        product: ConsultationProduct,
        isCareCenterBrowseFlowSupported: boolean,
        isMultCenterSupported: boolean,
        hideCenterDropDown?: boolean
    ) {
        const {consultationMode} = product
        const screenNavHeaderTitle = "Select consultation slot"
        if (consultationMode === "ONLINE" || hideCenterDropDown || CareUtil.isSugarfitExperienceCenterProduct(product) || CareUtil.isSugarfitExperienceCenterDiagnosticProduct(product) || (CareUtil.isLivePTDoctorType(product.doctorType) || CareUtil.isLiveSGTDoctorType(product.doctorType))) {
            /**
             * Set header to "Choose your slot"
             */
            if (AppUtil.isSugarFitOrUltraFitApp(userContext)) {
                return CareUtil.getDatePickerViewHeader(
                    false,
                    center,
                    screenNavHeaderTitle
                )
            }
            return CareUtil.getDatePickerViewHeader(
                false,
                center,
                "Choose Your Slot"
            )
        }
        if (CareUtil.isAnxietyTherapy(product?.productId)) {
            return CareUtil.getDatePickerViewHeader(
                false,
                center,
                "Choose slot"
            )
        }
        if (isCenterChangeRestricted) {
            return CareUtil.getDatePickerViewHeader(false, center, center.name)
        }
        return CareUtil.getDatePickerViewHeader(isMultCenterSupported, center, undefined, isCareCenterBrowseFlowSupported)
    }

    private async availableDatesWidget(
        userContext: UserContext,
        product: ConsultationProduct,
        availableSlotsResponse: TCAvailableSlotsResponse,
        parentBookingId: number,
        isReschedule: string,
        center: Center,
        patientId: string,
        isExternal: string,
        followUpConsultationId: string,
        preferredDoctorMap?: { [key: string]: PreferedDoctor },
        inActivePreferredDoctorMap?: { [key: string]: PreferedDoctor },
        offerIds?: string,
        availableProductTags?: AvailableProductTags[],
        tooltipAnnouncement?: TooltipAnnouncement,
        herculesService?: IHerculesService,
        healthfaceService?: IHealthfaceService,
        patientPromise?: Promise<Patient>,
        doctorId?: String,
        consultAgentWidgetPresent?: boolean,
        actionButtonPadding?: number,
        isOptumUser?: boolean
    ): Promise<DatesAvailableWidget> {
        const isTransform = CareUtil.isTransformDoctorType(product.doctorType) || product.tenant === "TRANSFORM"
        const MAX_NO_DAYS_TO_SHOW = 16
        let datesAvailable: DateWiseSlots[] = []
        const tz = userContext.userProfile.timezone,
            dates: string[] = TimeUtil.getDays(tz, MAX_NO_DAYS_TO_SHOW),
            slotAvailableDates: string[] = []
        let preferredDoctorUnavailableDates
        if (!_.isEmpty(availableSlotsResponse.preferredDoctorUnavailability)) {
            preferredDoctorUnavailableDates = _.map(availableSlotsResponse.preferredDoctorUnavailability, preferredDoctorUnavailability => {
                return TimeUtil.formatEpochInTimeZoneDateFns(tz, preferredDoctorUnavailability.date, "yyyy-MM-dd")
            })
        }
        const preferredDoctorUnavailability = availableSlotsResponse.preferredDoctorUnavailability
        const vertical = CareUtil.getCareConsultationProductVertical(product)
        const centerId = CareUtil.isCenterIdSkipableProduct(product) ? undefined : center?.id ? center.id : undefined
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "APP")
        let pageName = ""
        if (CultUtil.hasSGTOneStepBooking(userContext, userAgent, CareUtil.isLiveSGTDoctorType(product.doctorType))) {
            pageName = "sgtprebookingpage"
        } else if (CareUtil.isTransformDoctorType(product.doctorType)) {
            pageName = "tf_trial_checkout"
        } else if (CareUtil.isMindDoctorType(product.doctorType) && AppUtil.isFromFlutterAppFlow(userContext)) {
            pageName = "fl_slot_checkout"
        } else {
            pageName = "carecheckout"
        }
        const doctorList = _.values(availableSlotsResponse.preferredDoctorMap)
        let doctorImageUrl
        if (doctorList.length > 0) {
            doctorImageUrl = doctorList[0].displayImage
        }
        let actionUrl = isReschedule === "true" ? `curefit://rescheduleConsultation?productId=${product.productId}&followUpConsultationId=${followUpConsultationId}&parentBookingId=${parentBookingId}&centerId=${centerId}&patientId=${patientId}&vertical=${vertical}&doctorType=${product?.doctorType}&doctorId=${doctorId}` : `curefit://${pageName}?productId=${product.productId}&parentBookingId=${parentBookingId}&centerId=${centerId}&followUpConsultationId=${followUpConsultationId}&offerIds=${offerIds}&patientId=${patientId}&doctorType=${product?.doctorType}&doctorId=${doctorId}`
        if (availableSlotsResponse && !_.isEmpty(availableSlotsResponse.formattedAvailableSlotResponseList) && _.isEmpty(availableSlotsResponse.action)) {
            const datesMap: { [key: string]: TCAvailableSlotsDetails[] } = _.groupBy(
                availableSlotsResponse.formattedAvailableSlotResponseList,
                slot => {
                    return TimeUtil.formatEpochInTimeZoneDateFns(tz, slot.startTime, "yyyy-MM-dd")
                })
            for (let i = dates.length - 1 || 0; i >= 0 && datesAvailable.length < MAX_NO_DAYS_TO_SHOW; i--) {
                let dateAppointments: TCAvailableSlotsDetails[] = datesMap[dates[i]]
                const moment = momentTz.tz(tz)
                const currentDate = moment.format("YYYY-MM-DD")
                if (dates[i] === currentDate &&  CareUtil.isLCProduct(product)) {
                    const currentTimeInEpochInMillis = new Date().getTime() + 1800000
                    dateAppointments = dateAppointments?.filter(dateAppointment => dateAppointment.startTime > currentTimeInEpochInMillis )
                }
                // showing slots of limited times for the selected doctors
                if (isOptumUser && !isTransform && !_.isEmpty(dateAppointments)) {
                    dateAppointments = dateAppointments.filter(dateAppointment => {
                        if (this.findStartHour(userContext, dateAppointment.startTime) >= 17 && this.findStartHour(userContext, dateAppointment.startTime) <= 22 && dateAppointment?.doctorIdList?.includes(100266)) {
                            return true
                        }
                        if (this.findStartHour(userContext, dateAppointment.startTime) >= 10 && this.findStartHour(userContext, dateAppointment.startTime) <= 15 && dateAppointment?.doctorIdList?.includes(103364)) {
                            return true
                        }
                        return false
                    })
                }
                if (!_.isEmpty(dateAppointments)) {
                    let isSlotAvailableToday = dateAppointments.findIndex(slot => slot.isAvailable) !== -1
                    if (CareUtil.isLivePTDoctorType(product.doctorType) || CareUtil.isLiveSGTDoctorType(product.doctorType)) {
                        isSlotAvailableToday = dateAppointments.findIndex(slot => (!this.showSlotNotAvailable(userContext, slot) && (slot.isAvailable || slot.bookedByPatient))) !== -1
                    }
                    isSlotAvailableToday && slotAvailableDates.unshift(dates[i])
                    const timeslots: TimeSlotCategory[] = await this.getHourSplitTimeSlots(userContext, isExternal, dateAppointments, product, actionUrl, herculesService)
                    if (!_.isEmpty(timeslots)) {
                        datesAvailable.unshift(CareUtil.getDatewiseSlot(
                            dates[i],
                            tz,
                            timeslots,
                            !isSlotAvailableToday,
                            slotAvailableDates,
                            "CONSULTATION",
                            this.getNoSlotImageUrl(product.doctorType),
                            this.getNoSlotText(availableSlotsResponse),
                            product?.productId,
                            parentBookingId,
                            patientId,
                            isReschedule === "true")
                        )
                    }
                } else if (!_.isEmpty(preferredDoctorUnavailableDates)) {
                    const preferredDoctorUnavailabilityItem = _.find(preferredDoctorUnavailability, preferredDoctorUnavailabilityItem => TimeUtil.formatEpochInTimeZone(tz, preferredDoctorUnavailabilityItem.date) === dates[i])
                    if (!_.isEmpty(preferredDoctorUnavailabilityItem)) {
                        datesAvailable.unshift(CareUtil.getDatewiseSlot(
                            dates[i],
                            tz,
                            [],
                            true,
                            [],
                            "CONSULTATION",
                            this.getNoSlotImageUrl(product.doctorType),
                            `${preferredDoctorUnavailabilityItem.reason}`,
                            undefined,
                            undefined,
                            undefined,
                            isReschedule === "true")
                        )
                    }
                }
            }
        } else {
            if (!_.isEmpty(preferredDoctorUnavailability)) {
                _.forEach(preferredDoctorUnavailability, preferredDoctorUnavailabilityItem => {
                    datesAvailable.push(CareUtil.getDatewiseSlot(
                        TimeUtil.formatEpochInTimeZone(tz, preferredDoctorUnavailabilityItem.date),
                        tz,
                        [],
                        true,
                        [],
                        "CONSULTATION",
                        this.getNoSlotImageUrl(product.doctorType),
                        `${preferredDoctorUnavailabilityItem.reason}`,
                        undefined,
                        undefined,
                        undefined,
                        isReschedule === "true"
                        )
                    )
                })
            } else {
                datesAvailable.push(CareUtil.getDatewiseSlot(dates[0],
                    tz,
                    [],
                    true,
                    [],
                    "CONSULTATION",
                    this.getNoSlotImageUrl(product.doctorType),
                    this.getNoSlotText(availableSlotsResponse),
                    undefined,
                    undefined,
                    undefined,
                    isReschedule === "true"))
            }
        }
        if (isOptumUser && !isTransform) {
            datesAvailable = datesAvailable?.filter(date => date?.dateType.toString() === "WEEKDAY")
        }
        let preferredDoctorChangeMessage
        let therapistChangeConsentMessage
        if (!_.isEmpty(preferredDoctorMap) || !_.isEmpty(inActivePreferredDoctorMap)) {
            if (CareUtil.isMindDoctorType(product.doctorType) || CareUtil.isPsychiatry(product.doctorType)) {
                preferredDoctorChangeMessage = "Are you sure you want to change your personal expert?\n\nChanging  experts might impact your treatment outcomes"
                therapistChangeConsentMessage = "If you want to proceed, please provide permission to share your treatment history with your new therapist/psychiatrist, for continuity of care."
            } else if (product.doctorType === "LC") {
                preferredDoctorChangeMessage = "Are you sure you want to change your personal expert?\n\nChanging  experts might impact your outcomes"
                therapistChangeConsentMessage = "If you want to proceed, please provide consent to share your nutrition history with your new Dietician for continuity of experience."
            }
        }
        if (CareUtil.isLivePTDoctorType(product.doctorType)) {
            actionUrl = `${actionUrl}&pageName=Checkout`
        }
        let actionObj: Action = {
            actionType: "NAVIGATION",
            url: actionUrl
        }
        if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
            if (isReschedule === "true") {
                actionObj.actionType = "RESCHEDULE_TELECONSULTATION"
            } else {
                actionObj.actionType = "GET_TELECONSULTATION"
            }
        }
        if (CareUtil.isLiveSGTDoctorType(product.doctorType) && !_.isEmpty(availableSlotsResponse.metadata) && !_.isEmpty(availableSlotsResponse.metadata.dateWorkoutDetailsMap)) {
            actionObj = await this.getLiveSGTAction(userContext, availableSlotsResponse, actionUrl, herculesService)
            actionUrl = actionObj.url
        }
        const showBanner = !CareUtil.isSugarfitPhleboConsult(product?.productId) && !(AppUtil.isSugarFitApp(userContext) && product.consultationMode === "INCENTRE")
        const dateAvailableWidget = new DatesAvailableWidget(datesAvailable, actionUrl, actionObj, preferredDoctorMap, undefined, false, preferredDoctorChangeMessage, undefined, undefined, availableProductTags, therapistChangeConsentMessage, showBanner)
        if (CareUtil.isLivePTDoctorType(product.doctorType) || CareUtil.isLiveSGTDoctorType(product.doctorType)) {
            dateAvailableWidget.showPreferredDoctorBullets = false
            dateAvailableWidget.backgroundColor = "#f2f4f8"
            if (tooltipAnnouncement) {
                dateAvailableWidget.tooltipAnnouncement = tooltipAnnouncement
            }
            // show cross gender banner for first booking
            if (_.isEmpty(preferredDoctorMap) && CareUtil.isLivePTDoctorType(product.doctorType)) {
                dateAvailableWidget.bannerWidget = await this.getCrossGenderBannerWidget(userContext, product, patientPromise, healthfaceService)
            }
        } else if (CareUtil.isTherapyOnlyDoctorType(product.doctorType) || product.tenant !== "CULTFIT") {
            dateAvailableWidget.showPreferredDoctorBullets = false
        }
        if (product) {
            dateAvailableWidget.doctorType = product?.doctorType
        }
        dateAvailableWidget.consultAgentWidgetPresent = consultAgentWidgetPresent
        dateAvailableWidget.actionButtonPadding = actionButtonPadding
        return dateAvailableWidget
    }

    private getNoSlotImageUrl(doctorType: DOCTOR_TYPE): string {
        if (CareUtil.isLivePTDoctorType(doctorType) || CareUtil.isLiveSGTDoctorType(doctorType) || CareUtil.isPTDoctorType(doctorType)) {
            return "/image/icons/cult/live_pt_no_slot_1.png"
        }
        return "/image/carefit/consultation_empty.png"
    }

    private getNoSlotText(slotsResponse: TCAvailableSlotsResponse): string {
        if (slotsResponse.action && slotsResponse.action.actionPermitted === false) {
            return slotsResponse.action.reasonForProhibition
        }
        return "Sorry, No slots available today"
    }

    private async getHourSplitTimeSlots(userContext: UserContext, isExternal: string, dateAppointments: TCAvailableSlotsDetails[], product: ConsultationProduct, actionUrl: string, herculesService: IHerculesService): Promise<TimeSlotCategory[]> {
        let timeslots: TimeSlotCategory[] = []
        const timeSlotsMap = <{ [key: string]: TCAvailableSlotsDetails[] }>CareUtil.getTimeSlotMap(dateAppointments, userContext.userProfile.timezone)
        if (!_.isEmpty(timeSlotsMap)) {
            timeslots = await Promise.all(Object.keys(timeSlotsMap).sort().map(async timeSlotKey => {
                if (!_.isEmpty(timeSlotsMap[timeSlotKey])) {
                    return await this.parseToViewFormat(
                        userContext,
                        isExternal,
                        timeSlotsMap[timeSlotKey],
                        timeSlotKey,
                        product,
                        actionUrl,
                        herculesService
                    )
                }
            }))
        }
        return timeslots
    }

    private getPreferedDoctorMap(preferredDoctorMap?: { [key: string]: Doctor }): { [key: string]: PreferedDoctor } {
        const preferedDoctorValueMap: { [key: string]: PreferedDoctor } = {}
        let index: number = 0
        if (!_.isEmpty(preferredDoctorMap)) {
            Object.keys(preferredDoctorMap).forEach(key => {
                preferedDoctorValueMap[key] = {
                    id: key,
                    name: `${preferredDoctorMap[key].name}`,
                    color: this.getColor(index++),
                    type: CareUtil.getPreferredDoctorType(preferredDoctorMap[key].preferredDoctorType, preferredDoctorMap[key].preferredDoctorCode)
                }
            })
        }
        return preferedDoctorValueMap
    }

    private getColor(index: number) {
        const colors = ["#ff316d", "#778dff"]
        return colors[index % 2]
    }

    private async parseToViewFormat(userContext: UserContext, showEnd: string, slots: TCAvailableSlotsDetails[], timeSlotKey: string, product: ConsultationProduct, actionUrl: string, herculesService: IHerculesService): Promise<{ timeSlots: TimeSlot[], title: string }> {
        const tz = userContext.userProfile.timezone
        const additionalWaitingTimeInMillis = CareUtil.isSugarfitExperienceCenterProduct(product) ? 900000 : 0
        const timeSlots: TimeSlot[] = await Promise.all(slots.map(async element => {
            let listingCode: string, doctorId: number
            if (!_.isEmpty(element.preferredDoctorIdList)) {
                doctorId = element.preferredDoctorIdList[0]
            }
            if (!_.isEmpty(element.doctorMetaList)) {
                const doctorMeta = doctorId ? element.doctorMetaList.find(doctorMetaItem => doctorMetaItem.doctorId === doctorId) : element.doctorMetaList[0]
                if (!_.isEmpty(doctorMeta)) {
                    listingCode = doctorMeta.listingCode
                }
            }
            let action: Action
            const showSlotNotAvailable = this.showSlotNotAvailable(userContext, element)
            if (!_.isEmpty(element.slotInfoMetadata) && !_.isEmpty(element.slotInfoMetadata.workoutDetails)) {
                action = await this.getLiveSGTSlotAction(userContext, element.startTime, element.slotInfoMetadata.workoutDetails, actionUrl, herculesService)
            }
            if (AppUtil.isSugarFitOrUltraFitApp(userContext)) {
                const timeslot: TimeSlot = {
                    availableType: element.isAvailable === true && !showSlotNotAvailable ? "AVAILABLE" : "UNAVAILABLE",
                    eligibleDoctorIds: element.doctorIdList,
                    doctorIdList: !_.isEmpty(element.doctorMetaList) ? [element.doctorMetaList[0].doctorId] : undefined,
                    text: showEnd === "true" ? `${TimeUtil.formatEpochInTimeZoneDateFns(tz, element.startTime - additionalWaitingTimeInMillis, "hh:mm")}-${TimeUtil.formatEpochInTimeZoneDateFns(tz, element.endTime + additionalWaitingTimeInMillis, "hh:mm")}` : TimeUtil.formatEpochInTimeZoneDateFns(tz, element.startTime, "hh:mm"),
                    startTime: element.startTime,
                    endTime: element.endTime,
                    bookedByPatient: CareUtil.isLivePTDoctorType(product.doctorType) || CareUtil.isLiveSGTDoctorType(product.doctorType) ? element.bookedByPatient : false,
                    preferedDoctorIdList: element.preferredDoctorIdList,
                    detailedTimeText: TimeUtil.formatEpochInTimeZoneDateFns(tz, element.startTime, "EEE do MMM, hh:mm a"),
                    listingCode,
                    action
                }
                return timeslot
            }
            const timeslot: TimeSlot = {
                availableType: element.isAvailable === true && !showSlotNotAvailable ? "AVAILABLE" : "UNAVAILABLE",
                eligibleDoctorIds: element.doctorIdList,
                doctorIdList: !_.isEmpty(element.doctorMetaList) ? [element.doctorMetaList[0].doctorId] : undefined,
                text: showEnd === "true" ? `${TimeUtil.formatEpochInTimeZone(tz, element.startTime - additionalWaitingTimeInMillis, "hh:mm")}-${TimeUtil.formatEpochInTimeZone(tz, element.endTime + additionalWaitingTimeInMillis, "hh:mm")}` : TimeUtil.formatEpochInTimeZone(tz, element.startTime, "hh:mm"),
                startTime: element.startTime,
                endTime: element.endTime,
                bookedByPatient: CareUtil.isLivePTDoctorType(product.doctorType) || CareUtil.isLiveSGTDoctorType(product.doctorType) ? element.bookedByPatient : false,
                preferedDoctorIdList: element.preferredDoctorIdList,
                detailedTimeText: TimeUtil.formatEpochInTimeZone(tz, element.startTime, "ddd Do MMM, hh:mm A"),
                listingCode,
                action
            }
            return timeslot
            // timeSlots.push(timeslot)
        }))
        return {
            timeSlots,
            title: CareUtil.getTimeSlotTitleFromKey(timeSlotKey)
        }
    }

    private getDoctorInfoFooterWidget(preferredDoctorList: Doctor[], action?: Action): DoctorInfoFooterWidget {
        const doctorDetails = preferredDoctorList[0]
        const formatName = CareUtil.getFormatNameFromDoctorType(doctorDetails.preferredDoctorCode)
        return new DoctorInfoFooterWidget(doctorDetails.displayImage, doctorDetails.name, formatName, action)
    }

    private async getLiveSGTSlotAction(userContext: UserContext, slotStartTime: number, workoutDetails: SGTWorkoutDetails[], checkoutUrl: string, herculesService: IHerculesService): Promise<Action> {
        let action: Action
        const date = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, slotStartTime, "YYYY-MM-DD")
        const dateWorkoutMap: { [date: string]: any } = {}
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "APP")
        let navigationActionType: ActionType = "NAVIGATION"
        if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
            navigationActionType = "GET_TELECONSULTATION"
        }
        const hasSGTOneStepBooking = CultUtil.hasSGTOneStepBooking(userContext, userAgent, true)
        if (hasSGTOneStepBooking) {
            dateWorkoutMap[date] = await this.getOneStepLiveSGTMappedWorkoutDetails(workoutDetails)
            action = {
                actionType: navigationActionType,
                url: checkoutUrl,
                meta: {
                    dateWorkoutMap
                }
            }
        } else {
            if (workoutDetails.length > 1) {
                const mappedWorkoutDetails = dateWorkoutMap[date] = await this.getLiveSGTMappedWorkoutDetails(userContext, workoutDetails, checkoutUrl, herculesService)
                action = {
                    actionType: "SHOW_LIVE_SGT_BOOKING_MODAL",
                    meta: {
                        header: {
                            title: "Select Workout",
                        },
                        workoutDetails: mappedWorkoutDetails,
                        dateWorkoutMap
                    }
                }
            } else {
                const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "APP")
                let navigationActionType: ActionType = "NAVIGATION"
                if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
                    navigationActionType = "GET_TELECONSULTATION"
                }
                checkoutUrl += `&focusAreaId=${workoutDetails[0].focusAreaId}&pageName=Checkout`
                action = {
                    actionType: navigationActionType,
                    url: checkoutUrl
                }
            }
        }
        return action
    }

    private async getLiveSGTAction(userContext: UserContext, availableSlotsResponse: TCAvailableSlotsResponse, checkoutUrl: string, herculesService: IHerculesService): Promise<Action> {
        let action: Action
        const dateWorkoutDetailsMap = availableSlotsResponse.metadata.dateWorkoutDetailsMap
        let multipleWorkouts = false
        const dateWorkoutMap: { [date: string]: any } = {}
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "APP")
        let navigationActionType: ActionType = "NAVIGATION"
        if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
            navigationActionType = "GET_TELECONSULTATION"
        }

        const hasSGTOneStepBooking = CultUtil.hasSGTOneStepBooking(userContext, userAgent, true)
        if (hasSGTOneStepBooking) {
            for (const key of Object.keys(dateWorkoutDetailsMap)) {
                const workoutDetailsForDate = dateWorkoutDetailsMap[key]
                dateWorkoutMap[key] = await this.getOneStepLiveSGTMappedWorkoutDetails(workoutDetailsForDate)
            }
            action = {
                actionType: navigationActionType,
                url: checkoutUrl,
                meta: {
                    dateWorkoutMap
                }
            }
        } else {
            for (const key of Object.keys(dateWorkoutDetailsMap)) {
                const workoutDetailsForDate = dateWorkoutDetailsMap[key]
                if (workoutDetailsForDate.length > 1) {
                    multipleWorkouts = true
                    dateWorkoutMap[key] = await this.getLiveSGTMappedWorkoutDetails(userContext, workoutDetailsForDate, checkoutUrl, herculesService)
                } else {
                    checkoutUrl += `&focusAreaId=${workoutDetailsForDate[0].focusAreaId}&pageName=Checkout`
                    action = {
                        actionType: navigationActionType,
                        url: checkoutUrl
                    }
                }
                if (multipleWorkouts) {
                    action = {
                        actionType: "SHOW_LIVE_SGT_BOOKING_MODAL",
                        meta: {
                            header: {
                                title: "Select Workout",
                            },
                            dateWorkoutMap
                        }
                    }
                }
            }
        }
        return action
    }

    private getOneStepLiveSGTMappedWorkoutDetails(sgtWorkoutDetails: SGTWorkoutDetails[]): any[] {
        const workoutDetails = _.map(sgtWorkoutDetails, workoutDetail => {
            return {
                focusAreaId: workoutDetail.focusAreaId
            }
        })
        return workoutDetails
    }

    private async getLiveSGTMappedWorkoutDetails(userContext: UserContext, sgtWorkoutDetails: SGTWorkoutDetails[], checkoutUrl: string, herculesService: IHerculesService): Promise<any> {
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "APP")
        let navigationActionType: ActionType = "NAVIGATION"
        if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
            navigationActionType = "GET_TELECONSULTATION"
        }
        const workoutDetailsPromises = _.map(sgtWorkoutDetails, async workoutDetail => {
            let subTitle = undefined
            if (workoutDetail.wodId) {
                const wod: SimpleWod = await herculesService.getSimpleWodById(workoutDetail.wodId)
                subTitle = CultUtil.getWodMovements(wod)
            }
            return {
                title: `${workoutDetail.workoutName} - ${workoutDetail.focusAreaName}`,
                subTitle: subTitle,
                tag: workoutDetail.isRecommended ? "RECOMMENDED" : undefined,
                action: {
                    actionType: navigationActionType,
                    title: "SELECT",
                    url: `${checkoutUrl}&focusAreaId=${workoutDetail.focusAreaId}&pageName=Checkout`
                }
            }
        })
        const workoutDetails = await Promise.all(workoutDetailsPromises)
        return workoutDetails
    }

    private showSlotNotAvailable(userContext: UserContext, slotDetails: TCAvailableSlotsDetails): boolean {
        if (slotDetails.slotInfoMetadata && !_.isEmpty(slotDetails.slotInfoMetadata.workoutDetails)) {
            if (AppUtil.isWeb(userContext)) {
                return false
            }
            return userContext.sessionInfo.appVersion < 8.40
        }
        return false
    }

    private async getCrossGenderBannerWidget(userContext: UserContext, product: ConsultationProduct, patientPromise: Promise<Patient>, healthfaceService: IHealthfaceService) {
        const patient = await patientPromise
        if (AppUtil.isLivePTCrossGenderMatchingSupported(userContext, patient.gender)) {
            const isCrossGenderEnabled = await healthfaceService.isPatientLivePTTrainerCrossGenderMatchEnabled(patient.id, "CULTFIT")
            if (!isCrossGenderEnabled) {
                const crossGenderModalAction: Action = {
                    actionType: "SHOW_WIDGETIZED_MODAL",
                    meta: {
                        position: "bottom",
                        url: "/cult/lpt/crossGender",
                        params: {
                            patientId: patient.id,
                            productId: product.productId
                        }
                    }
                }
                return new BannerCarouselWidget("335:60", [{
                    id: product.productCode,
                    image: patient.gender === "Female" ? "/image/icons/livept/cross_gender_banner_female.png" : "/image/icons/livept/cross_gender_banner_male.png",
                    action: crossGenderModalAction,
                    contentMetric: {
                        contentId: "cross_gender_banner"
                    }
                }], {
                    bannerWidth: 335,
                    bannerHeight: 49,
                    noVerticalPadding: true,
                    backgroundColor: undefined
                }, 1, false, false)
            }
        }
    }

    private findStartHour(userContext: UserContext, startTime: number) {
        const startHour = Number(TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, startTime, "HH"))
        return startHour
    }
}

export default ConsultationDatePickerViewV1
