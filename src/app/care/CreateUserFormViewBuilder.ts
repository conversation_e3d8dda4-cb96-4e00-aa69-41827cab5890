import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { User } from "@curefit/user-common"
import * as _ from "lodash"
import { UserContext } from "@curefit/userinfo-common"
import { Action } from "../common/views/WidgetView"
import { CacheHelper } from "../util/CacheHelper"
import { CareUtil } from "../util/CareUtil"
import { CultParqFormDetails } from "@curefit/cult-common"
import { ICultServiceOld as ICultService, CULT_CLIENT_TYPES } from "@curefit/cult-client"
import CultUtil from "../util/CultUtil"
import { Patient } from "@curefit/care-common"
import AppUtil from "../util/AppUtil"

export type FormFieldType = "FIRST_NAME" | "LAST_NAME" | "FULL_NAME" | "EMAIL" | "CONTACT_NO" | "DOB" | "AGE" | "GENDER" | "GENDER_V2" | "RELATION" | "RELATION_V2" | "EMERGENCY_PERSON_NAME" | "EMERGENCY_CONTACT_NO" | "EMERGENCY_RELATION" | "TERMS_AND_CONDITION" | "GUARDIAN_RELATION" | "GUARDIAN_NAME" | "GUARDIAN_CONTACT_NO" | "PARENTS_CONTACT_NO" | "PARENTS_EMAIL"
export type FormValidatorType = "AGE_VALIDATOR"
export type FormUserType = "PT_USER" | "CARE_USER" | "THERAPY_USER" | "MANAGED_PLAN_USER" | "CULT_JUNIOR_USER" | "COUPLE_PARTNER_USER"

export interface FormField {
    type: FormFieldType
    defaultValue?: any
    placeholder?: string
    fieldName?: string
    isOptional?: boolean
    shouldDisplayInUI?: boolean
    disableSelection?: boolean
}

export interface FormItem {
    type: FormFieldType
    defaultValue?: any
    placeholder?: string
    fieldName: string
    isOptional?: boolean
    shouldDisplayInUI?: boolean
    meta?: any // for any other info required for that item
}

export interface FormValidator {
    type: FormValidatorType
    meta?: any
}

export interface FormSection {
    title: string,
    data: FormItem[]
}

export class CreateUserFormPage {
    items?: FormItem[] // TODO - remove this after 7.3 version gets adopted
    sections: FormSection[]
    formValidators?: FormValidator[]
    pageAction: Action
    cultParqJuniorFrom: CultParqFormDetails
    constructor(
        sections: FormSection[],
        formValidators?: FormValidator[],
        cultParqJuniorFrom?: CultParqFormDetails) {
        this.sections = sections
        this.formValidators = formValidators
        this.cultParqJuniorFrom = cultParqJuniorFrom
    }
}

export interface FormPageBuilderParams {
    relation: string
    formUserType: FormUserType,
    packId?: string,
    isMalePreselected?: string
}

@injectable()
export class CreateUserFormViewBuilder {

    constructor(
        @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
        @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
    ) {

    }

    async buildView(userContext: UserContext, builderParams: FormPageBuilderParams, patientDetails?: Patient): Promise<CreateUserFormPage> {
        switch (builderParams.formUserType) {
            case "THERAPY_USER":
                return this.getTherapyPatientFormData(userContext, builderParams)
            case "PT_USER":
                return this.getPTUserFormData(userContext, builderParams)
            case "MANAGED_PLAN_USER":
                return this.getMPPatientFormData(userContext, builderParams)
            case "COUPLE_PARTNER_USER":
                return this.getPartnerPatientFormData(userContext, builderParams)
            case "CARE_USER":
                return patientDetails ? this.getEditCarePatientFormData(userContext, builderParams, patientDetails) : this.getCarePatientForm(userContext, builderParams)
            case "CULT_JUNIOR_USER":
                return this.getCultJuniorUserFormData(userContext, builderParams)
            default:
                break
        }
    }
    async getCarePatientForm(userContext: UserContext, builderParams: FormPageBuilderParams): Promise<CreateUserFormPage> {
        return (AppUtil.isWeb(userContext) || (userContext.sessionInfo.userAgent === "APP" && userContext.sessionInfo.appVersion >= 8.27)) ? this.getCarePatientFormDataV2(userContext, builderParams) : this.getCarePatientFormData(userContext, builderParams)
    }

    async getCarePatientFormData(userContext: UserContext, builderParams: FormPageBuilderParams): Promise<CreateUserFormPage> {
        const userInfo = await this.userCache.getUser(userContext.userProfile.userId)
        const notShowRelationShip = builderParams.relation !== "undefined" && builderParams.relation !== undefined
        const defaultRelationShipValue = notShowRelationShip ? builderParams.relation : "Select Relation"
        const defaultGender = builderParams.isMalePreselected !== undefined ? ( builderParams.isMalePreselected === "true" ? "Male" : "Female" ) : undefined
        const disableGenderSelection = defaultGender ? true : false
        const formFields: FormField[] = []
        formFields.push({ type: "FIRST_NAME"}, { type: "LAST_NAME"})
        formFields.push({ type: "DOB"}, { type: "GENDER", defaultValue: defaultGender, disableSelection : disableGenderSelection  })
        formFields.push({ type: "RELATION", defaultValue: defaultRelationShipValue, shouldDisplayInUI: !notShowRelationShip })
        // formFields.push({ type: "CONTACT_NO" })
        // formFields.push({ type: "EMAIL"})
        formFields.push({
            type: "TERMS_AND_CONDITION", defaultValue: "I am lawfully authorised to add the above information on behalf of the owner of profile."
        })
        return this.buildFormViewWithFields(userContext, formFields, builderParams.formUserType)
    }

    async getPartnerPatientFormData(userContext: UserContext, builderParams: FormPageBuilderParams): Promise<CreateUserFormPage> {
        const formFields: FormField[] = []
        formFields.push({ type: "FIRST_NAME", fieldName: "Name", placeholder: "Enter Name" })
        formFields.push({ type: "RELATION", defaultValue: "Partner", shouldDisplayInUI: false})
        formFields.push({ type: "CONTACT_NO", placeholder: "Enter Phone Number", fieldName: "Partner's Phone Number", isOptional: true })
        formFields.push({
            type: "TERMS_AND_CONDITION", defaultValue: "I am lawfully authorised to add the above information on behalf of the owner of profile."
        })
        return this.buildFormViewWithFields(userContext, formFields, builderParams.formUserType)
    }

    async getCarePatientFormDataV2(userContext: UserContext, builderParams: FormPageBuilderParams): Promise<CreateUserFormPage> {
        const userInfo = await this.userCache.getUser(userContext.userProfile.userId)
        const notShowRelationShip = builderParams.relation !== "undefined" && builderParams.relation !== undefined
        const defaultRelationShipValue = notShowRelationShip ? builderParams.relation : "Select Relation"
        const defaultGender = builderParams.isMalePreselected !== undefined ? ( builderParams.isMalePreselected === "true" ? "Male" : "Female" ) : undefined
        const disableGenderSelection = defaultGender ? true : false
        const formFields: FormField[] = []
        formFields.push({ type: "RELATION_V2", defaultValue: defaultRelationShipValue, shouldDisplayInUI: !notShowRelationShip })
        formFields.push({ type: "FULL_NAME"})
        formFields.push({ type: "GENDER_V2", defaultValue: defaultGender, disableSelection : disableGenderSelection  })
        formFields.push({ type: "DOB"})
        formFields.push({
            type: "TERMS_AND_CONDITION", defaultValue: "I am lawfully authorised to add the above information on behalf of the owner of profile."
        })
        return this.buildFormViewWithFields(userContext, formFields, builderParams.formUserType)
    }

    async getEditCarePatientFormData(userContext: UserContext, builderParams: FormPageBuilderParams, patientDetails?: Patient): Promise<CreateUserFormPage> {
        const userInfo = await this.userCache.getUser(userContext.userProfile.userId)
        const defaultGender = builderParams.isMalePreselected !== undefined ? ( builderParams.isMalePreselected === "true" ? "Male" : "Female" ) : (patientDetails.gender !== null ? patientDetails.gender : undefined )
        const disableGenderSelection = defaultGender && builderParams.isMalePreselected !== undefined ? true : false
        const defaultFirstName = _.get(patientDetails, "firstName", undefined)
        const defaultLastName = _.get(patientDetails, "lastName", undefined)
        const defaultDob =  _.get(patientDetails, "dateOfBirth", undefined)
        const defaultRelation = _.get(patientDetails, "relationship", "Other")
        const formFields: FormField[] = []
        formFields.push({ type: "FIRST_NAME" , defaultValue: defaultFirstName }, { type: "LAST_NAME", defaultValue: defaultLastName })
        formFields.push({ type: "DOB", defaultValue: defaultDob }, { type: "GENDER", defaultValue: defaultGender, disableSelection : disableGenderSelection  })
        formFields.push({ type: "RELATION", defaultValue: defaultRelation, shouldDisplayInUI: false })
        formFields.push({
            type: "TERMS_AND_CONDITION", defaultValue: "I am lawfully authorised to add the above information on behalf of the owner of profile."
        })
        return this.buildFormViewWithFields(userContext, formFields, builderParams.formUserType)
    }

    async getMPPatientFormData(userContext: UserContext, builderParams: FormPageBuilderParams): Promise<CreateUserFormPage> {
        const userInfo = await this.userCache.getUser(userContext.userProfile.userId)
        const formFields: FormField[] = this.getCommonPatientFormData(userInfo, "Self", true, true)
        return this.buildFormViewWithFields(userContext, formFields, builderParams.formUserType)
    }

    getCommonPatientFormData(userInfo: User, relation: string, shouldDisplayEmailInUI?: boolean, shouldDisplayMobileInUI?: boolean) {
        const isEmailIdNotPresent = shouldDisplayEmailInUI || _.isEmpty(userInfo.email)
        const isPhoneNumberNotPresent = shouldDisplayMobileInUI || _.isEmpty(userInfo.phone)
        const isRelationNotSelf = relation !== "Self"
        const formFields: FormField[] = []
        formFields.push({ type: "FIRST_NAME", defaultValue: userInfo.firstName ? userInfo.firstName.trim() : undefined }, { type: "LAST_NAME", defaultValue: userInfo.lastName ? userInfo.lastName.trim() : undefined })
        formFields.push({ type: "RELATION", defaultValue: relation, shouldDisplayInUI: isRelationNotSelf })
        formFields.push({ type: "DOB" }, { type: "GENDER" })
        formFields.push({ type: "CONTACT_NO", defaultValue: userInfo.phone, shouldDisplayInUI: isPhoneNumberNotPresent })
        formFields.push({ type: "EMAIL", defaultValue: userInfo.email, shouldDisplayInUI: isEmailIdNotPresent })
        return formFields
    }

    async getTherapyPatientFormData(userContext: UserContext, builderParams: FormPageBuilderParams): Promise<CreateUserFormPage> {
        const userInfo = await this.userCache.getUser(userContext.userProfile.userId)
        const isWeb = AppUtil.isWeb(userContext)
        const formFields: FormField[] = this.getCommonPatientFormData(userInfo, "Self", false, false)
        formFields.push({ type: "EMERGENCY_PERSON_NAME", defaultValue: "" })
        formFields.push({ type: "EMERGENCY_CONTACT_NO", defaultValue: "" })
        formFields.push({ type: "EMERGENCY_RELATION", ...(isWeb ? { placeholder: "Select Relation" } : { defaultValue: "" }) })
        return this.buildFormViewWithFields(userContext, formFields, builderParams.formUserType)
    }

    async getPTUserFormData(userContext: UserContext, builderParams: FormPageBuilderParams): Promise<CreateUserFormPage> {
        const userInfo = await this.userCache.getUser(userContext.userProfile.userId)
        const formFields: FormField[] = this.getCommonPatientFormData(userInfo, "Self", false, false)
        const formView = this.buildFormViewWithFields(userContext, formFields, builderParams.formUserType)
        // TODO - remove this after 7.3 version gets adopted
        if (userContext.sessionInfo.appVersion < 7.3) {
            // assuming only one section is present for PT
            formView.items = formView.sections[0].data
        }
        return formView
    }

    async getCultJuniorUserFormData(userContext: UserContext, builderParams: FormPageBuilderParams): Promise<CreateUserFormPage> {
        const userInfo = await this.userCache.getUser(userContext.userProfile.userId)
        const cultParqJuniorFrom = builderParams.packId && await this.cultFitService.getCultJuniorParqForm(builderParams.packId)
        const formFields: FormField[] = [{ type: "FIRST_NAME" }, { type: "LAST_NAME" }, { type: "DOB" }, { type: "GENDER" }]
        formFields.push({ type: "PARENTS_EMAIL", placeholder: "Parents Email Id" })
        formFields.push({ type: "PARENTS_CONTACT_NO", placeholder: "Parents Contact Number" })
        formFields.push({
            type: "TERMS_AND_CONDITION", defaultValue: "I am lawfully authorised to add the above information and voluntarily signing up my child for the Cult Junior membership. Fitness certificate from a physician is a must if your child has any health issues. However, this does not guarantee admission, keeping in mind the child's safety"
        })
        formFields.push({ type: "EMERGENCY_PERSON_NAME", defaultValue: "" })
        formFields.push({ type: "EMERGENCY_CONTACT_NO", defaultValue: "" })
        formFields.push({ type: "EMERGENCY_RELATION", defaultValue: "" })

        return this.buildFormViewWithFields(userContext, formFields, builderParams.formUserType, true, cultParqJuniorFrom && cultParqJuniorFrom[0])
    }

    private buildFormViewWithFields(userContext: UserContext, fields: FormField[], formUserType: FormUserType, cultJuniorRelation?: boolean, cultParqJuniorFrom?: CultParqFormDetails): CreateUserFormPage {
        const isCultJuniorRelation = cultJuniorRelation ? true : false
        const items: FormItem[] = _.map(fields, field => {
            switch (field.type) {
                case "FIRST_NAME":
                    return this.getFirstNameView(field)
                case "LAST_NAME":
                    return this.getLastNameView(field)
                case "FULL_NAME":
                    return this.getNameView(field)
                case "EMAIL":
                    return this.getEmailView(field)
                case "PARENTS_EMAIL":
                    return this.getEmailView(field)
                case "CONTACT_NO":
                    return this.getContactView(field)
                case "PARENTS_CONTACT_NO":
                    return this.getContactView(field)
                case "DOB":
                    return this.getDOBView(field)
                case "AGE":
                    return this.getAgeView(field)
                case "GENDER":
                case "GENDER_V2":
                    return this.getGenderView(field)
                case "RELATION":
                case "RELATION_V2":
                    return this.getRelationView(field)
                case "EMERGENCY_PERSON_NAME":
                    return this.getEmergencyContactPersonName(field)
                case "EMERGENCY_CONTACT_NO":
                    return this.getEmergencyContactNumberName(field)
                case "EMERGENCY_RELATION":
                    return this.getEmergencyContactRelationName(field, isCultJuniorRelation)
                case "TERMS_AND_CONDITION":
                    return this.getTermsAndConditionView(field)
            }
        })
        const formValidators = this.createFormValidators(userContext, formUserType)
        const sections = this.createSectionWithTitle(userContext, items, formUserType)
        return new CreateUserFormPage(sections, formValidators, cultParqJuniorFrom)
    }

    private buildGuardianFormView(userContext: UserContext, fields: FormField[]): CreateUserFormPage {
        const items: FormItem[] = _.map(fields, field => {
            switch (field.type) {
                case "GUARDIAN_CONTACT_NO":
                    return this.getContactView(field)
                case "GUARDIAN_RELATION":
                    return this.getGuardianRelation(field)
                case "GUARDIAN_NAME":
                    return this.getGuardianName(field)
            }
        })
        const sections = this.createSectionWithTitle(userContext, items)
        return new CreateUserFormPage(sections)
    }


    createFormValidators(userContext: UserContext, formUserType: FormUserType): FormValidator[] {
        // add more validators based on form types
        switch (formUserType) {
            case "PT_USER":
                return [this.getPTAgeValidator("PT_USER", 18, "Personal Training")]

            case "THERAPY_USER":
            case "MANAGED_PLAN_USER":
            case "CARE_USER":
                return [this.getCareAgeValidator(userContext, "CARE_USER", 18)]

            case "CULT_JUNIOR_USER":
                return [this.getCultJuniorAgeValidator(5, 13)]
        }
    }


    getPTAgeValidator(formUserType: FormUserType, minAge: number, productName?: string): FormValidator {
        return {
            type: "AGE_VALIDATOR",
            meta: {
                minAge,
                errorInfo: {
                    title: `Age Under ${minAge}`,
                    tilte: `Age Under ${minAge}`, // TODO - remove this after 7.3 version gets adopted, old app is reading wrong name
                    subTitle: `${productName} is only available to users above ${minAge} years. For any queries, contact <NAME_EMAIL>.`
                }
            }
        }
    }

    getCareAgeValidator(userContext: UserContext, formUserType: FormUserType, minAge: number): FormValidator {
        const formFields: FormField[] = []
        const isWeb = AppUtil.isWeb(userContext)
        const guardianRelationParamWeb = { placeholder: "Select relation" }
        formFields.push({ type: "GUARDIAN_RELATION", ...(isWeb ? guardianRelationParamWeb : { defaultValue: "Select relation" })})
        formFields.push({ type: "GUARDIAN_NAME", placeholder: "Enter Parents / Guardian name" })
        formFields.push({ type: "GUARDIAN_CONTACT_NO" })
        const formFieldItems = this.buildGuardianFormView(userContext, formFields)
        return {
            type: "AGE_VALIDATOR",
            meta: {
                minAge,
                errorInfo: {
                    title: "Add Guardian Details", // `Age less than ${minAge} years `,
                    subTitle: "Your age is lower than allowed limit to proceed further. We need your parents/guardian details."
                },
                guardianModal: formFieldItems
            }
        }
    }

    getCultJuniorAgeValidator(minAge: number, maxAge: number): FormValidator {
        return {
            type: "AGE_VALIDATOR",
            meta: {
                minAge,
                maxAge,
                errorInfo: {
                    title: `Age Between ${minAge} - ${maxAge}`,
                    subTitle: `This pack is available for children from age ${minAge} to ${maxAge} years only. For any queries, contact <NAME_EMAIL>.`
                },
            },
        }
    }

    private addToSection(sections: Array<Object>, title: string, items: FormItem[], alignRow?: boolean) {
        if (items.length > 0) {
            sections.push({
                title: title,
                data: items,
                alignRow
            })
        }
    }

    createSectionWithTitle(userContext: UserContext, items: FormItem[], formUserType?: FormUserType) {
        const isDesktop = AppUtil.isDesktop(userContext)
        const personalDetailItems: FormItem[] = [],
            emergencyContactItems: FormItem[] = [],
            termsAndConditionItem: FormItem[] = [],
            sections: FormSection[] = [],
            guardianItem: FormItem[] = [],
            desktopRelationSection: FormItem[] = [],
            desktopPersonalDetailItems: FormItem[] = [],
            desktopGenderDetailItems: FormItem[] = [],
            desktopEmergencyDetailItems: FormItem[] = []
        items.map(item => {
            switch (item.type) {
                case "AGE":
                case "PARENTS_CONTACT_NO":
                case "PARENTS_EMAIL":
                    personalDetailItems.push(item)
                    break
                case "RELATION":
                case "RELATION_V2":
                    isDesktop ? desktopRelationSection.push(item) : personalDetailItems.push(item)
                    break
                case "FIRST_NAME":
                case "LAST_NAME":
                case "FULL_NAME":
                case "DOB":
                case "EMAIL":
                case "CONTACT_NO":
                    isDesktop ? desktopPersonalDetailItems.push(item) : personalDetailItems.push(item)
                    break
                case "GENDER":
                case "GENDER_V2":
                    isDesktop ? desktopGenderDetailItems.push(item) : personalDetailItems.push(item)
                    break
                case "EMERGENCY_PERSON_NAME":
                case "EMERGENCY_CONTACT_NO":
                case "EMERGENCY_RELATION":
                    isDesktop ? desktopEmergencyDetailItems.push(item) : emergencyContactItems.push(item)
                    break

                case "TERMS_AND_CONDITION":
                    termsAndConditionItem.push(item)
                    break

                case "GUARDIAN_CONTACT_NO":
                case "GUARDIAN_RELATION":
                case "GUARDIAN_NAME":
                    guardianItem.push(item)
                default:
                    break
            }
        })
        const personalDetailsSectionTitle = formUserType === "CULT_JUNIOR_USER" ? "Junior Details" : "Personal Details"
        const desktopPersonalDetailsTitle = personalDetailItems.length ? "" : personalDetailsSectionTitle
        this.addToSection(sections, personalDetailsSectionTitle, personalDetailItems)

        if (isDesktop) {
            this.addToSection(sections, desktopPersonalDetailsTitle, desktopRelationSection)
            this.addToSection(sections, "", desktopPersonalDetailItems, true)
            this.addToSection(sections, "", desktopGenderDetailItems)
            this.addToSection(sections, "Emergency contact details", desktopEmergencyDetailItems, true)
        }

        this.addToSection(sections, "Emergency contact details", emergencyContactItems)
        this.addToSection(sections, "", termsAndConditionItem)
        this.addToSection(sections, "", guardianItem, isDesktop)
        return sections
    }

    getFirstNameView(field: FormField): FormItem {
        return {
            fieldName: "First Name",
            ...field,
            placeholder: field.placeholder || "Enter First Name"
        }
    }

    getLastNameView(field: FormField): FormItem {
        return {
            fieldName: "Last Name",
            ...field,
            placeholder: field.placeholder || "Enter Last Name"
        }
    }

    getNameView(field: FormField): FormItem {
        return {
            fieldName: "Name",
            ...field,
            placeholder: field.placeholder || "Enter Name"
        }
    }

    getEmailView(field: FormField): FormItem {
        return {
            fieldName: "Email id",
            ...field,
            placeholder: field.placeholder || "Enter your email id"
        }
    }

    getContactView(field: FormField): FormItem {
        return {
            fieldName: "Mobile no.",
            ...field,
            placeholder: field.placeholder || "Enter Contact Number",
            meta: {
                prefix: "+91-"
            }
        }
    }

    getDOBView(field: FormField): FormItem {
        return {
            fieldName: "Date of birth",
            ...field,
        }
    }

    getAgeView(field: FormField): FormItem {
        return {
            fieldName: "Age",
            ...field
        }
    }

    getGenderView(field: FormField): FormItem {
        return {
            fieldName: "Gender",
            ...field,
            meta: {
                inputTypes: ["Male", "Female"]
            }
        }
    }

    getRelationView(field: FormField): FormItem {
        return {
            fieldName: "This is for",
            ...field,
            meta: {
                relationTypes: CareUtil.getRelations()
            }
        }
    }

    getEmergencyContactPersonName(field: FormField): FormItem {
        return {
            fieldName: "Name",
            ...field,
            placeholder: field.placeholder || "Enter Full Name"
        }
    }

    getEmergencyContactNumberName(field: FormField): FormItem {
        return {
            fieldName: "Emergency Contact no.",
            ...field,
            placeholder: field.placeholder || "Enter Emergency Number",
            meta: {
                prefix: "+91-"
            }
        }
    }

    getEmergencyContactRelationName(field: FormField, isCultJuniorRelation: boolean): FormItem {
        let relationTypes
        if (isCultJuniorRelation) {
            relationTypes = CultUtil.getCultJuniorEmergencyRelations()
        } else {
            relationTypes = CareUtil.getEmergencyRelations()
        }
        return {
            fieldName: "Emergency Relation",
            ...field,
            meta: {
                relationTypes: relationTypes,
                optional: true
            }
        }
    }

    getTermsAndConditionView(field: FormField): FormItem {
        return {
            fieldName: "Terms and Conditions",
            ...field
        }
    }

    getGuardianRelation(field: FormField): FormItem {
        return {
            fieldName: "Relation",
            ...field,
            meta: {
                relationTypes: CareUtil.getGuardianRelations(),
            }
        }
    }

    getGuardianName(field: FormField): FormItem {
        return {
            fieldName: "Name",
            ...field,
            placeholder: field.placeholder || "Enter Parents / Guardian name"
        }
    }
}

export default CreateUserFormViewBuilder
