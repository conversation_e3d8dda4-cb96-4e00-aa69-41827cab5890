import { MONG<PERSON>_TYPES, MultiMongooseAccess, MultiMongooseSchema } from "@curefit/mongo-utils"
import { inject } from "inversify"
import { Schema } from "mongoose"
import { ChatEventLogModel } from "./ChatEventLogModel"
import { ReadPreference } from "mongodb"

export class ChatEventLogSchema extends MultiMongooseSchema<ChatEventLogModel> {

    constructor(
        @inject(MONGO_TYPES.MultiMongooseAccess) mongooseAccess: MultiMongooseAccess
    ) {
        super(mongooseAccess, "ChatEventLog", "DEFAULT", ReadPreference.SECONDARY_PREFERRED)
    }

    protected schema() {
        return {
            userId: {type: String, index: true, required: true},
            events: [{type: ChatEventSchema, required: false}]
        }
    }

}

export const ChatEventSchema = new Schema({
    type: {type: String},
    timestamp: {type: Number}
}, {_id: false})
