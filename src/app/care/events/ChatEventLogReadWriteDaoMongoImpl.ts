import { inject, injectable } from "inversify"
import { MongoReadWriteDao } from "@curefit/mongo-utils"
import { BASE_TYPES, ILogger } from "@curefit/base"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { ChatEventLogModel } from "./ChatEventLogModel"
import { ChatEventLogSchema } from "./ChatEventLogSchema"
import { ChatEventLog } from "./ChatEventLog"
import { IChatEventLogReadWriteDao } from "./ChatEventLogDao"
import { ChatEventLogReadOnlyDaoMongoImpl } from "./ChatEventLogReadOnlyDaoMongoImpl"

@injectable()
export class ChatEventLogReadWriteDaoMongoImpl extends MongoReadWriteDao<ChatEventLogModel, ChatEventLog> implements IChatEventLogReadWriteDao {
    constructor(
        @inject(CUREFIT_API_TYPES.ChatEventLogSchema) ChatEventsLogSchema: ChatEventLogSchema,
        @inject(CUREFIT_API_TYPES.ChatEventLogReadOnlyDaoMongoImpl) readonlyDao: ChatEventLogReadOnlyDaoMongoImpl,
        @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
        super(ChatEventsLogSchema.mongooseModel, readonlyDao, logger)
    }
}