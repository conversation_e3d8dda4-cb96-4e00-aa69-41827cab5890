import { inject, injectable } from "inversify"
import { MongoReadonlyDao } from "@curefit/mongo-utils"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ChatEventLogModel } from "./ChatEventLogModel"
import { ChatEventLog } from "./ChatEventLog"
import { ChatEventLogSchema } from "./ChatEventLogSchema"
import { IChatEventLogReadOnlyDao } from "./ChatEventLogDao"

@injectable()
export class ChatEventLogReadOnlyDaoMongoImpl extends MongoReadonlyDao<ChatEventLogModel, ChatEventLog> implements IChatEventLogReadOnlyDao {
    constructor(
        @inject(CUREFIT_API_TYPES.ChatEventLogSchema) ChatEventLogSchema: ChatEventLogSchema,
        @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
        super(ChatEventLogSchema.mongooseModel, logger, ChatEventLogSchema.isLeanQueryEnabled)
    }
}