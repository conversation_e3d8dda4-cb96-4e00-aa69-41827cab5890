import {
    Action,
    ManageOptionPayload,
    ManageOptions,
    NavigationCardWidget,
    ProductDetailPage,
    WidgetView
} from "../common/views/WidgetView"
import { CustomerIssueType } from "@curefit/issue-common"
import { InstructionItem } from "@curefit/product-common"
import { ProductPrice } from "@curefit/product-common"
import { FoodProduct as Product } from "@curefit/eat-common"
import * as _ from "lodash"
import { CareUtil } from "../util/CareUtil"
import HCUDetailsPageConfig from "./HCUDetailsPageConfig"
import {
    BookingDetail,
    DiagnosticsTestOrderResponse,
    Patient,
    StepInfo,
    DiagnosticEmailedTestReportItem,
    DiagnosticEmailedTestReportResponse
} from "@curefit/albus-client"
import {
    CareProductStateWidget,
    DiagnosticsTestReportSummaryWidget,
    DiagnosticTestReportItem,
    ProductStateItem
} from "../page/PageWidgets"
import { ActionUtil } from "@curefit/base-utils"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { UserContext } from "@curefit/userinfo-common"
import AppUtil, { PHLEBO_CALLING_ACTION_SUPPORTED, SUPPORT_DEEP_LINK } from "../util/AppUtil"
class DiagnosticTestPageView extends ProductDetailPage {
    public pageContext: any

    constructor(
        userContext: UserContext,
        pageConfig: HCUDetailsPageConfig,
        osName: string,
        appVersion: number,
        codepushVersion: number,
        isInternalUser: boolean,
        product: Product,
        bookingInfo: BookingDetail,
        patient: Patient,
        newReportIssueManageOption: ManageOptionPayload,
        issuesMap: Map<string, CustomerIssueType[]>,
        diagnosticEmailedTestReportDetails?: DiagnosticEmailedTestReportResponse,
        reportActions?: Map<string, any>
    ) {
        super()
        let subTitle, isCancelled
        if (bookingInfo.booking.status !== "CANCELLED") {
            subTitle = `For ${patient.name}`
            isCancelled = false
        } else {
            subTitle = "Cancelled"
            isCancelled = true
        }
        const tz = userContext.userProfile.timezone
        if (AppUtil.isDesktop(userContext)) {
            this.pageImage = product.imageUrl
        }
        this.widgets.push(this.getHealthCheckupSummaryWidget(product, bookingInfo, subTitle, issuesMap, newReportIssueManageOption, userContext, isCancelled))
        if (bookingInfo.booking.status !== "CANCELLED" && CareUtil.isDiagnosticPageSupported(osName, appVersion, codepushVersion, isInternalUser)) {
            const expandedItemIndices: number[] = []
            const items: ProductStateItem[] = []
            let itemCount: number = 0
            const diagnosticOrderResponse = bookingInfo.diagnosticsTestOrderResponse[0]
            const atHomeEnabled = !_.isEmpty(diagnosticOrderResponse.atHomeDiagnosticOrder)
            if (atHomeEnabled) {
                const stateItem = this.getAtHomeItem(userContext, bookingInfo.booking.patientId, diagnosticOrderResponse.atHomeStepInfo, diagnosticOrderResponse.atHomeDiagnosticOrder, bookingInfo.booking.id.toString(), diagnosticOrderResponse.atHomeInstructions, diagnosticOrderResponse.productCodes[0])
                items.push(stateItem)
                if (stateItem.isExpanded) expandedItemIndices.push(itemCount)
                itemCount++
            }
            if (!_.isEmpty(diagnosticOrderResponse.inCentreDiagnosticOrder)) {
                const stateItem = this.getInCentreItem(userContext, bookingInfo.booking.patientId, diagnosticOrderResponse.inCentreStepInfo, diagnosticOrderResponse.inCentreDiagnosticOrder,
                    bookingInfo.booking.id.toString(), diagnosticOrderResponse.inCentreInstructions, diagnosticOrderResponse.productCodes[0], atHomeEnabled, tz)
                items.push(stateItem)
                if (stateItem.isExpanded) expandedItemIndices.push(itemCount)
                itemCount++
            }
            const stateItem = this.getReportItem(diagnosticOrderResponse, userContext, reportActions)
            items.push(stateItem)
            if (stateItem.isExpanded) expandedItemIndices.push(itemCount)
            this.widgets.push(new CareProductStateWidget(items, expandedItemIndices, false))
        }
        if (bookingInfo.diagnosticsTestOrderResponse[0].status === "REPORT_GENERATED") {
            const isNewEmailedReportSupported = CareUtil.isNewEmailedReportSupported(userContext) && diagnosticEmailedTestReportDetails && !_.isEmpty(diagnosticEmailedTestReportDetails.productDetails)
            const widget = this.getDiagnosticReportSummaryWidget(userContext, bookingInfo, isNewEmailedReportSupported)
            if (widget) {
                this.widgets.push(widget)
            }
            // if (isNewEmailedReportSupported) {
            //     this.widgets.push(CareUtil.getDiagnosticsEmailTestReportSummaryWidget(diagnosticEmailedTestReportDetails))
            // }
        }
    }

    private getAtHomeItem(userContext: UserContext, patientId: number, atHomeStepInfo: StepInfo, atHomeDiagnosticOrder: any, bookingId: string, instructions: InstructionItem[], productId: string): ProductStateItem {
        switch (atHomeStepInfo.stepState) {
            case "BOOKED":
                return {
                    title: "Sample collection at home",
                    subtitle: CareUtil.getHomeCollectionTimeText(atHomeDiagnosticOrder.startTime, atHomeDiagnosticOrder.endTime, userContext),
                    gradientColors: ["#fb8676", "#f64cab"],
                    icon: "home",
                    states: [{
                        state: "NOT_STARTED",
                        viewType: "MULTI_ACTIONS",
                        views: this.getDetailActionForHome(userContext, patientId, atHomeStepInfo, atHomeDiagnosticOrder, bookingId, instructions, productId),
                    }],
                    isExpanded: true,
                    withoutLine: true,
                    dividerGradient: AppUtil.isWeb(userContext) ? ["#d8d8d8", "#d8d8d8"] : undefined
                }
            case "COMPLETED":
            case "SAMPLE_COLLECTED":
                return {
                    title: "Sample collection at home",
                    subtitle: CareUtil.getHomeCollectionTimeText(atHomeDiagnosticOrder.startTime, atHomeDiagnosticOrder.endTime, userContext),
                    gradientColors: ["#fb8676", "#f64cab"],
                    dividerGradient: AppUtil.isWeb(userContext) ? ["#d8d8d8", "#d8d8d8"] : undefined,
                    icon: "tick",
                    states: [],
                    isExpanded: false,
                    withoutLine: true
                }
        }
    }

    getDetailActionForHome(userContext: UserContext, patientId: number, atHomeStepInfo: StepInfo, atHomeDiagnosticOrder: any, bookingId: string, instructions: InstructionItem[], productId: string): Action[] {
        const isApp = userContext.sessionInfo.userAgent === "APP"
        const rescheduleAction = this.getRescheduleAction(patientId, atHomeStepInfo, bookingId, "AT_HOME_SLOT", productId, undefined, undefined, isApp, atHomeDiagnosticOrder)
        const atHomeDetailTopAction: any = userContext.sessionInfo.userAgent === "APP" ? CareUtil.getAtHomeDetailTopAction(userContext, atHomeDiagnosticOrder, atHomeStepInfo) : undefined
        // const sampleCollectionDateText: string = momentTz.tz(atHomeDiagnosticOrder.startTime, TimeUtil.IST_TIMEZONE).format("ddd, DD MMM, h:mm")
        // const sampleCollectionTimeText: string = sampleCollectionDateText + momentTz.tz(atHomeDiagnosticOrder.endTime, TimeUtil.IST_TIMEZONE).format("-h:mm A")
        const atHomeTitle: string = "Sample collection at home"
        const cardAction: Action = {
            title: "Details",
            actionType: "SHOW_DIAGNOSTIC_TEST_DETAILS",
            meta: {
                instructions: instructions,
                topAction: atHomeDetailTopAction,
                bottomAction: rescheduleAction,
                title: atHomeTitle,
                subtitle: CareUtil.getHomeCollectionTimeText(atHomeDiagnosticOrder.startTime, atHomeDiagnosticOrder.endTime, userContext)
            }
        }
        const actions: Action[] = [cardAction]
        if (rescheduleAction) {
            actions.push({
                ...rescheduleAction,
                title: "Reschedule"
            })
        }
        return actions
    }

    private getInCenterDetailTopAction(inCentreDiagnosticOrder: any): Action {
        const placeUrl = inCentreDiagnosticOrder.slot.diagnosticCentre.placeUrl
        return {
            actionType: "EXTERNAL_DEEP_LINK",
            url: placeUrl,
            icon: "NAVIGATE",
            title: "View Map"
        }
    }

    getDetailActionForInCentre(patientId: number, inCentreStepInfo: StepInfo, inCentreDiagnosticOrder: any, bookingId: string, instructions: InstructionItem[],
        productId: string, atHomeEnabled?: boolean, timezone: Timezone = TimeUtil.IST_TIMEZONE): Action[] {
        const rescheduleAction = this.getRescheduleAction(patientId, inCentreStepInfo, bookingId, "IN_CENTRE_SLOT", productId, inCentreDiagnosticOrder, atHomeEnabled)
        const getInCenterDetailTopAction = this.getInCenterDetailTopAction(inCentreDiagnosticOrder)
        const inCentreTestDateStartText: string = TimeUtil.formatEpochInTimeZoneDateFns(timezone, inCentreDiagnosticOrder.slot.workingStartTime, "eee, dd MMM, h:mm")
        const inCentreTestTimeText: string = inCentreTestDateStartText + TimeUtil.formatEpochInTimeZoneDateFns(timezone, inCentreDiagnosticOrder.slot.workingEndTime, "-h:mm a")
        const inCenterTitle: string = "Tests at Center"

        const cardAction: Action = {
            title: "Details",
            actionType: "SHOW_DIAGNOSTIC_TEST_DETAILS",
            meta: {
                instructions: instructions,
                topAction: getInCenterDetailTopAction,
                bottomAction: rescheduleAction,
                title: inCenterTitle,
                subtitle: inCentreTestTimeText
            }
        }
        const actions: Action[] = [cardAction]
        if (rescheduleAction) {
            actions.push({
                ...rescheduleAction,
                title: "Reschedule"
            })
        }
        return actions
    }

    private getRescheduleAction(patientId: number, stepInfo: StepInfo, bookingId: string, category: string, productId: string, inCentreDiagnosticOrder?: any, atHomeEnabled?: boolean, isApp?: boolean, atHomeDiagnosticOrder?: any): Action {
        const addressId = atHomeDiagnosticOrder && atHomeDiagnosticOrder.addressMetadata && atHomeDiagnosticOrder.addressMetadata.addressId ? atHomeDiagnosticOrder.addressMetadata.addressId : undefined
        if (_.includes(stepInfo.allowedActions, "RESCHEDULE")) {
            if (category === "AT_HOME_SLOT" && addressId) {
                return {
                    title: "RESCHEDULE",
                    actionType: "NAVIGATION",
                    url: `curefit://selectCareDateV1?patientId=${patientId}&productId=${productId}&parentBookingId=${bookingId}
                        &type=DIAGNOSTICS&category=AT_HOME_SLOT&isReschedule=true&addressIdV1=${addressId}`
                }
            }
            return {
                title: "RESCHEDULE",
                actionType: "DIAGNOSTIC_TEST_RESCHEDULE",
                meta: {
                    parentBookingId: bookingId,
                    patientId,
                    centerId: category === "IN_CENTRE_SLOT" && inCentreDiagnosticOrder ? inCentreDiagnosticOrder.slot.diagnosticCentre.id : undefined,
                    centreCode: category === "IN_CENTRE_SLOT" && inCentreDiagnosticOrder ? inCentreDiagnosticOrder.slot.diagnosticCentre.code : undefined,
                    productId: productId,
                    type: "DIAGNOSTICS",
                    category: category,
                    hasAtHome: atHomeEnabled ? true : undefined,
                    title: category === "IN_CENTRE_SLOT" ? undefined : "Select your home address"
                }
            }
        }
    }

    private getAtHomeDetailTopAction(atHomeDiagnosticOrder: any): Action {
        const phoneNumber = atHomeDiagnosticOrder.phleboMobileNumber
        if (!_.isEmpty(phoneNumber)) {
            return {
                actionType: "PHONE_CALL_NAVIGATION",
                icon: "PHONE_CALL",
                title: " Associate",
                meta: {
                    phoneNumber: phoneNumber
                }
            }
        }
    }

    private getReportItem(diagnosticOrderResponse: DiagnosticsTestOrderResponse, userContext: UserContext, reportActions: Map<string, any>): ProductStateItem {
        const states = this.getDetailActionForReport(diagnosticOrderResponse, userContext, reportActions)
        const reportActionStatus = reportActions.get(diagnosticOrderResponse.carefitOrderId)
        const isExpanded = diagnosticOrderResponse.status === "REPORT_GENERATED" || reportActionStatus !== "NONE"
        switch (diagnosticOrderResponse.status) {
            case "REPORT_GENERATED": return {
                title: "Report Generated",
                gradientColors: ["#f29458", "#fd796d"],
                dividerGradient: AppUtil.isWeb(userContext) ? ["#d8d8d8", "#d8d8d8"] : undefined,
                icon: "tick",
                states,
                isExpanded,
                withoutLine: true
            }
            default: return {
                title: "Get your report",
                gradientColors: ["#f29458", "#fd796d"],
                icon: "report1",
                states,
                isExpanded,
                withoutLine: true,
                dividerGradient: AppUtil.isWeb(userContext) ? ["#d8d8d8", "#d8d8d8"] : undefined
            }
        }
    }
    private getInCentreItem(userContext: UserContext, patientId: number, inCentreStepInfo: StepInfo, inCentreDiagnosticOrder: any, bookingId: string, instructions: InstructionItem[],
        productId: string, atHomeEnabled?: boolean, timezone: Timezone = TimeUtil.IST_TIMEZONE): ProductStateItem {
        switch (inCentreStepInfo.stepState) {
            case "BOOKED": return {
                title: "Tests at center",
                subtitle: TimeUtil.formatEpochInTimeZone(timezone, inCentreDiagnosticOrder.slot.workingStartTime, "ddd, D MMM, h:mm A"),
                gradientColors: ["#77fd9d", "#48c9b3"], // ["#f29458", "#fd796d"],
                icon: "center",
                states: [{
                    state: "NOT_STARTED",
                    viewType: "MULTI_ACTIONS",
                    views: this.getDetailActionForInCentre(patientId, inCentreStepInfo, inCentreDiagnosticOrder, bookingId, instructions, productId, atHomeEnabled, timezone),
                }],
                isExpanded: true,
                withoutLine: true,
                dividerGradient: AppUtil.isWeb(userContext) ? ["#d8d8d8", "#d8d8d8"] : undefined
            }
            case "COMPLETED":
            case "SAMPLE_COLLECTED":
                return {
                    title: "Tests at center",
                    subtitle: TimeUtil.formatEpochInTimeZone(timezone, inCentreDiagnosticOrder.slot.workingStartTime, "ddd, D MMM, h:mm A"),
                    gradientColors: ["#77fd9d", "#48c9b3"], // ["#f29458", "#fd796d"],
                    icon: "tick",
                    states: [],
                    isExpanded: false,
                    withoutLine: true,
                    dividerGradient: AppUtil.isWeb(userContext) ? ["#d8d8d8", "#d8d8d8"] : undefined
                }
        }
    }


    private getIssueList(issuesMap: Map<string, CustomerIssueType[]>): { code: string, title: string, confirmation: string }[] {
        const issueList: { code: string, title: string, confirmation: string }[] = []
        issuesMap.get("DiagnosticsSingleBooked").forEach(customerIssueType => {
            issueList.push({
                code: customerIssueType.code,
                title: customerIssueType.subject,
                confirmation: customerIssueType.confirmation
            })
        })
        return issueList
    }

    private getManageOptions(bookingDetail: BookingDetail, orderId: string, productId: string, issuesMap: Map<string, CustomerIssueType[]>): { manageOptions: ManageOptions, meta: any } {
        const options: ManageOptionPayload[] = []
        options.push(
            {
                isEnabled: true,
                displayText: "Need Help",
                type: "REPORT_ISSUE",
                meta: this.getIssueList(issuesMap),
                action: SUPPORT_DEEP_LINK,
            }
        )

        const manageOptions: ManageOptions = {
            displayText: "...",
            options: options
        }

        const meta: any = {
            orderId: orderId,
            productId: productId,
            tcBookingId: bookingDetail.booking.id
        }
        return { manageOptions: manageOptions, meta: meta }
    }

    private getHealthCheckupSummaryWidget(product: Product, bookingInfo: BookingDetail, subTitle: string,
        issuesMap: Map<string, CustomerIssueType[]>,
        newReportIssueManageOption: ManageOptionPayload, userContext: UserContext, isCancelled: boolean): WidgetView {
        const isDesktop = AppUtil.isDesktop(userContext)
        const statusToCheck = ["ACTIVE", "SAMPLE_COLLECTED", "SAMPLE_RECEIVED", "REPORT_GENERATED", "PHLEBO_ARRIVED"]
        const isNotCancelable = statusToCheck.includes(bookingInfo?.diagnosticsTestOrderResponse?.[0]?.status) || statusToCheck.includes(bookingInfo?.diagnosticsTestOrderResponse?.[0]?.atHomeDiagnosticOrder?.status) || statusToCheck.includes(bookingInfo?.diagnosticsTestOrderResponse?.[0]?.inCentreDiagnosticOrder?.status)
        let manageOptionsView: { manageOptions: ManageOptions, meta: any } = null
        let manageOptions: ManageOptions
        if (newReportIssueManageOption) {
            const cancelAction: ManageOptionPayload = { displayText: "Cancel and Refund", isEnabled: !isCancelled && !isNotCancelable , type: "CANCEL_TC", meta: { tcBookingId: bookingInfo?.booking?.id}}
            manageOptions = {
                displayText: "Manage",
                options: [newReportIssueManageOption, cancelAction]
            }
            manageOptionsView = {
                manageOptions: manageOptions,
                meta: { tcBookingId: bookingInfo?.booking?.id }
            }
        } else {
            manageOptionsView = this.getManageOptions(bookingInfo, bookingInfo.booking.cfOrderId, bookingInfo.booking.productCode, issuesMap)
        }
        const checkupSummaryWidget: WidgetView & {
            productId: string;
            title: string;
            subTitle: string;
            price: ProductPrice;
            image?: string;
            meta: any
            manageOptions: ManageOptions,
        } = {
            widgetType: "HEALTHCHECKUP_SUMMARY_WIDGET",
            title: "Diagnostic Tests",
            subTitle: subTitle,
            productId: product.productId,
            price: !_.isEmpty(bookingInfo) ? null : product.price,
            image: isDesktop ? undefined : product.imageUrl,
            manageOptions: !_.isEmpty(manageOptionsView) ? manageOptionsView.manageOptions : null,
            meta: !_.isEmpty(manageOptionsView) ? manageOptionsView.meta : null,
            breadcrumb: isDesktop ? [{text: "Home", link: "/"}, {text: "Care", link: "/care"}, {text: "Diagnostics"}] : []
        }
        return checkupSummaryWidget
    }

    private getDiagnosticReportSummaryWidget(userContext: UserContext, bookingInfo: BookingDetail, isNewEmailedReportSupported: boolean): DiagnosticsTestReportSummaryWidget | NavigationCardWidget {
        const reportItems: DiagnosticTestReportItem[] = []
        const diagnosticOrderResponse = bookingInfo.diagnosticsTestOrderResponse[0]
        const reportInfo = diagnosticOrderResponse.finalDiagnosticReport.diagnosticCheckUpReportInfo
        const widget = CareUtil.getTestReportNullAction(userContext, diagnosticOrderResponse, reportInfo)
        // If new emailed report widget supported, don't show null report widget
        if (isNewEmailedReportSupported && widget) {
            return null
        } else if (widget) {
            return widget
        }
        reportItems.push(
            {
                header: {
                    title: reportInfo.testId,
                    subtitle: "View now", // `${reportInfo.testCount} tests`,
                    action: {
                        url: ActionUtil.diagnosticReportPage(diagnosticOrderResponse.orderId, diagnosticOrderResponse.carefitOrderId),
                        actionType: "NAVIGATION"
                    }
                },
                // testInfos: testDetails
            }
        )
        return new DiagnosticsTestReportSummaryWidget(reportItems)
    }

    private getDetailActionForReport(diagnosticOrderResponse: DiagnosticsTestOrderResponse, userContext: UserContext, reportActions: Map<string, any>): ProductStateItem["states"] {
        const actions: ProductStateItem["states"] = []
        if (AppUtil.isWeb(userContext)) {
            return actions
        }
        return [{
            state: "NOT_STARTED",
            viewType: "MULTI_ACTIONS",
            views: CareUtil.getDiagosticTestActions(userContext, diagnosticOrderResponse, reportActions)
        }]
    }
}

export default DiagnosticTestPageView
