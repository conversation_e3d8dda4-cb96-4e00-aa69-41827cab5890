import { ALBUS_CLIENT_TYPES, ConsultationSellableProduct, DoctorAvlImmediateResponse, IHealthfaceService, HealthfaceProductInfo } from "@curefit/albus-client"
import { CareProductInfoWidget, WidgetView } from "@curefit/apps-common"
import { RUPEE_SYMBOL, OfferUtil } from "@curefit/base-utils"
import { ConsultationProduct, Doctor, Patient } from "@curefit/care-common"
import { PackOffersResponse } from "@curefit/offer-common"
import { ProductPrice } from "@curefit/product-common"
import { CareWidgetUtil, UserContext } from "@curefit/vm-models"
import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { Action, ProductDetailPage } from "../common/views/WidgetView"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import AppUtil from "../util/AppUtil"
import { CareUtil } from "../util/CareUtil"


import _ = require("lodash")
import { WidgetWithMetric } from "@curefit/vm-common"


interface IGP99ProductPagePromise {
    consultationOfferPromise: Promise<PackOffersResponse>
    doctorsPromise: Promise<Doctor[]>
    patientListPromise: Promise<Patient[]>
    packageProductPromise: Promise<HealthfaceProductInfo[]>
    consultationPackBannerWidgetPromise: Promise<WidgetWithMetric>
    immediateAvlDoctorInfoPromise: Promise<DoctorAvlImmediateResponse>
}
@injectable()
export class GP99ProductPageViewBuilder {
    constructor(
        @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces
    ) {
    }

    async getProductPage(userContext: UserContext, product: ConsultationProduct, isCallFlow: boolean) {
        const deviceId = userContext.sessionInfo.deviceId
        const userId = userContext.userProfile.userId
        const cityId = userContext.userProfile.cityId
        let packageProductPromise
        if (!isCallFlow) {
            packageProductPromise = this.healthfaceService.getProductInfoDetailsCached("CONSULTATION", undefined, product.productId, product.tenant)
        }
        const patientListPromise = this.healthfaceService.getAllPatients(userContext.userProfile.userId)
        const doctorsPromise = this.healthfaceService.getAllDoctorsCached(product.doctorType, undefined, undefined, product.tenant)
        const consultationPackBannerWidgetPromise = CareUtil.getBannerCarouselWidget(userContext, this.serviceInterfaces, CareUtil.getCareConsultationPacksBannerId(product.doctorType))
        const immediateAvlDoctorInfoPromise = this.healthfaceService.getImmediateAvlDoctorForProduct(product.doctorType, product.productId, product.consultationMode, product.tenant)
        const consultationOfferPromise = CareWidgetUtil.getCareProductOffersFromUserContext(userContext, "CONSULTATION", [product.productId], AppUtil.callSourceFromContext(userContext), this.serviceInterfaces, true)
        const pagePromise = {
            doctorsPromise,
            patientListPromise,
            packageProductPromise,
            consultationOfferPromise,
            immediateAvlDoctorInfoPromise,
            consultationPackBannerWidgetPromise
        }
        return new GP99ProductPage().buildView(userContext, product, pagePromise, isCallFlow)
    }
}

export class GP99ProductPage extends ProductDetailPage {
    async buildView(
        userContext: UserContext,
        product: ConsultationProduct,
        pagePromise: IGP99ProductPagePromise,
        isCallFlow: boolean
    ) {
        const widgetPromises: Promise<WidgetView>[] = []
        const actions = (async () => {
            return this.getActions(userContext, product, await pagePromise.patientListPromise,  isCallFlow, await pagePromise.immediateAvlDoctorInfoPromise)
        })()

        widgetPromises.push((async () => {
            const offerDetails = OfferUtil.getPackOfferAndPrice(product, <PackOffersResponse>await pagePromise.consultationOfferPromise)
            const price = offerDetails?.price?.listingPrice === 0 ? `@ FREE` : ""
            // const items = CareUtil.getGP99ProductItem(product, (await pagePromise.immediateAvlDoctorInfoPromise), isCallFlow)
            return new CareProductInfoWidget(`General Physician ${price}`, product.subTitle, product.heroImageUrl, { marginBottom: -50, paddingBottom: 15 }, product?.subTitle?.length > 100, 100, "See less", "Know more", offerDetails?.price?.listingPrice ? offerDetails?.price?.listingPrice : undefined)
        })())

        widgetPromises.push((async () => {
            return CareUtil.getDoctorListWidget(userContext, "Our General Physicians", await pagePromise.doctorsPromise, {}, undefined, true)
        })())

        widgetPromises.push((async () => {
            return await pagePromise.consultationPackBannerWidgetPromise
        })())

        widgetPromises.push((async () => {
            if (pagePromise.packageProductPromise) {
                const packageProduct = <ConsultationSellableProduct>(await pagePromise.packageProductPromise)[0].baseSellableProduct
                const {  packHowItHelp } = CareUtil.getPackContent(packageProduct)
                return CareUtil.getWhatYouGetInstructionWidget(packHowItHelp, userContext)
            }
        })())

        widgetPromises.push((async () => {
            if (pagePromise.packageProductPromise && !isCallFlow) {
                const packageProduct = <ConsultationSellableProduct>(await pagePromise.packageProductPromise)[0].baseSellableProduct
                const { howItWorksItem } = CareUtil.getPackContent(packageProduct)
                return CareUtil.getHowItWorksWidget(howItWorksItem)
            }
            return CareUtil.getGP99CallNowHowItWorksWidget()
        })())

        widgetPromises.push((async () => {
            if (pagePromise.packageProductPromise && !isCallFlow) {
                const packageProduct = <ConsultationSellableProduct>(await pagePromise.packageProductPromise)[0].baseSellableProduct
                const { packSummary } = CareUtil.getPackContent(packageProduct)
                return CareUtil.getHowItWorksWidget(packSummary)
            }
            return CareUtil.getGP99CallNowAudioWidget()
        })())

        this.widgets = (await Promise.all(widgetPromises)).filter(Boolean).map(item => ({...item, hasDividerBelow: false}))
        this.actions = await actions

        return this
    }

    getActions(
        userContext: UserContext,
        product: ConsultationProduct,
        patientsList: Patient[],
        isCallFlow: boolean,
        doctAvlInfo: DoctorAvlImmediateResponse
    ): Action[] {
        const isCallFlowActive = isCallFlow && doctAvlInfo.immediateSlotAvailable
        const title = isCallFlowActive ? `GET CALL NOW` : `SCHEDULE APPOINTMENT`
        if (userContext.sessionInfo.isUserLoggedIn) {
            return [
                CareUtil.getTCBookingAction(
                    patientsList,
                    product as ConsultationProduct,
                    title,
                    isCallFlowActive
                    ? {
                        actionType: "NAVIGATION",
                        title,
                        url: `curefit://carecheckout?productId=${product.productId}&isCallFlow=true`
                    } : undefined)]
        }
        return [{
                actionType: "SHOW_ALERT_MODAL",
                title,
                meta: {
                    title: "Login Required!",
                    subTitle: "Please login to continue",
                    actions: [{ actionType: "LOGOUT", title: "Login" }]
                }
            }
        ]
    }
}
