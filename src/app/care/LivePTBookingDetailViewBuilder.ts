import { ProductDetailPage } from "../common/views/WidgetView"
import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { UserContext } from "@curefit/userinfo-common"
import { ConsultationProduct } from "@curefit/care-common"
import {
    ALBUS_CLIENT_TYPES,
    BookingDetail, CareActionContext, Consultation,
    ConsultationOrderResponse, IHealthfaceService, TwilioChatChannelDetails, TwilioUnreadMessageCountViewMap, TwilioUser
} from "@curefit/albus-client"
import {
    Action,
    DoctorInfoWidget,
    HeaderWidget,
    ImageOverlayCardContainerWidget,
    MovementsInfo, RightInfo,
    WODInfoWidget,
    WorkoutInstruction,
    WorkoutMovement,
} from "@curefit/apps-common"
import { CdnUtil, pluralizeStringIfRequired, TimeUtil } from "@curefit/util-common"
import CareUtil from "../util/CareUtil"
import * as _ from "lodash"
import IssueBusiness, { IssueDetailView } from "../crm/IssueBusiness"
import { ICareBusiness } from "./CareBusiness"
import { CULT_CLIENT_TYPES } from "@curefit/cult-client"
import { ICultServiceOld as ICultService } from "@curefit/cult-client/dist/src/ICultServiceOld"
import WodViewBuilder from "../cult/WodViewBuilder"
import { WodDetailsResponse } from "@curefit/cult-common"
import { DisplayMovement } from "@curefit/fitness-common"
import {
    AnnouncementView,
    WidgetAction,
    WidgetizedAnnouncement,
    WidgetizedAnnouncementMeta
} from "../announcement/AnnouncementViewBuilder"
import { AppointmentDoctorChangeDetails } from "@curefit/albus-client/dist/src/models"
import { AnnouncementBusiness } from "../announcement/AnnouncementBusiness"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import AppUtil from "../util/AppUtil"

@injectable()
class LivePTBookingDetailViewBuilder extends ProductDetailPage {

    constructor(@inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness,
        @inject(CUREFIT_API_TYPES.CareBusiness) public careBusiness: ICareBusiness,
        @inject(CULT_CLIENT_TYPES.CultFitService) public cultFitService: ICultService,
        @inject(CUREFIT_API_TYPES.WodViewBuilder) public wodViewBuilder: WodViewBuilder,
        @inject(CUREFIT_API_TYPES.AnnouncementBusiness) private announcementBusiness: AnnouncementBusiness,
        @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
        @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness,
        @inject(ALBUS_CLIENT_TYPES.CultPersonalTrainingService) public cultPTService: IHealthfaceService) {
        super()
    }

    async getView(userContext: UserContext, product: ConsultationProduct, bookingDetail: BookingDetail): Promise<ProductDetailPage> {
        const productDetailPage = new ProductDetailPage()
        const isLivePTProduct = CareUtil.isLivePTProduct(product)
        const isLiveSGTProduct = CareUtil.isLiveSGTProduct(product)
        const isChatSupportedForUser = await AppUtil.isLivePTChatSupported(userContext, this.hamletBusiness)
        if (isLivePTProduct && isChatSupportedForUser && _.get(bookingDetail.consultationOrderResponse.appointmentActionsWithContext.chatActionWithContext, "context.twilioCommunicationMode.modeSid", false)) {
            const channelSid = bookingDetail.consultationOrderResponse.appointmentActionsWithContext.chatActionWithContext.context.twilioCommunicationMode.modeSid
            const chatChannelDetails: TwilioChatChannelDetails = await this.healthfaceService.getChatChannelDetails(channelSid, "CULTFIT")
            productDetailPage.widgets.push({ ...await this.getSummaryWidget(userContext, product, bookingDetail, isChatSupportedForUser, chatChannelDetails), hasDividerBelow: false })
        } else {
            productDetailPage.widgets.push({
                ...await this.getSummaryWidget(userContext, product, bookingDetail),
                hasDividerBelow: false
            })
        }
        const wodWidget = await this.wodViewBuilder.getLivePTWodWidget(userContext, bookingDetail)
        if (!_.isEmpty(wodWidget)) {
            productDetailPage.widgets.push(wodWidget)
        }
        if (CareUtil.isPending(bookingDetail)) {
            const noteListWidget = CareUtil.getLivePTSessionNoteListWidget(bookingDetail, userContext, "#ffffff", isLivePTProduct ? 20 : 5, isLiveSGTProduct)
            productDetailPage.widgets.push({ ...noteListWidget, hasDividerBelow: false })
        }
        // if (!CareUtil.isComplteted(bookingDetail)) {
        //     productDetailPage.widgets.push(CareUtil.getLivePThowItWorksWidget(userContext))
        // }
        productDetailPage.actions = await this.getActions(userContext, bookingDetail, isLiveSGTProduct, product)
        productDetailPage.announcementData = await this.getTrainerChangeAnnouncement(userContext, bookingDetail)
        return productDetailPage
    }

    private async getSummaryWidget(userContext: UserContext, product: ConsultationProduct, bookingDetail: BookingDetail, isChatSupportedForUser?: boolean, chatChannelDetails?: TwilioChatChannelDetails): Promise<ImageOverlayCardContainerWidget> {
        const imageOverlayContainerWidget = new ImageOverlayCardContainerWidget()
        imageOverlayContainerWidget.assets.push({
            assetType: "IMAGE",
            assetUrl: product.heroImageUrl
        })
        const isLiveSGTProduct = CareUtil.isLiveSGTProduct(product)
        const isLivePTProduct =  CareUtil.isLivePTProduct(product)
        const subTitle = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, bookingDetail.consultationOrderResponse.startTime, "ddd, MMM Do, hh:mm A")
        const actions: Action[] = []
        if (CareUtil.getCancelEnabled(bookingDetail)) {
            const cancelAction = this.getCancelBookingAction(userContext, bookingDetail)
            if (cancelAction) {
                actions.push(cancelAction)
            }
        }
        const reportIssueAction = await this.getReportIssueAction(userContext, bookingDetail, isLiveSGTProduct)
        if (reportIssueAction) {
            actions.push(reportIssueAction)
        }
        let moreAction: Action
        if (!_.isEmpty(actions)) {
            moreAction = {
                actionType: "ACTION_LIST",
                actions
            }
        }
        const headerWidget: HeaderWidget = {
            widgetType: "HEADER_WIDGET",
            widgetTitle: {
                title: product.title,
                subTitle: subTitle
            },
            headerStyle: {
                marginLeft: 0,
                fontSize: 22,
                color: "#000000",
                width: "90%"
            },
            subTitleStyle: {
                marginLeft: 0,
                fontSize: 14
            },
            subHeader: isLiveSGTProduct && bookingDetail.consultationOrderResponse.metadata.focusAreaName ? {
                text: bookingDetail.consultationOrderResponse.metadata.focusAreaName
            } : undefined,
            moreAction
        }
        imageOverlayContainerWidget.widgets.push(headerWidget)
        const trainerInfoWidget = this.getTrainerWidget(bookingDetail, isLivePTProduct, isChatSupportedForUser, chatChannelDetails)
        if (trainerInfoWidget) {
            imageOverlayContainerWidget.widgets.push(trainerInfoWidget)
        }
        return imageOverlayContainerWidget
    }

    private getCancelBookingAction(userContext: UserContext, bookingDetail: BookingDetail): Action {
        if (!_.isEmpty(bookingDetail) && CareUtil.isPending(bookingDetail)) {
            return {
                actionType: "CANCEL_TC",
                title: "Cancel session",
                meta: {
                    tcBookingId: bookingDetail.booking.id,
                    productId: bookingDetail.booking.productCode,
                    code: "TELECONSULTATION03"
                },
                analyticsData: {
                    bookingId: bookingDetail.booking.id,
                }
            }
        }
    }

    private async getReportIssueAction(userContext: UserContext, bookingDetail: BookingDetail, isLiveSGTProduct: boolean): Promise<Action> {
        let repostIssues: IssueDetailView[]
        if (isLiveSGTProduct) {
            repostIssues = await this.issueBusiness.getLiveSGTSessionIssue(bookingDetail, userContext)
        } else {
            repostIssues = await this.issueBusiness.getLivePersonalTrainingSessionIssue(bookingDetail, userContext)
        }
        const newReportIssueManageOption = this.issueBusiness.toManageOptionPayload(repostIssues, true)
        if (newReportIssueManageOption) {
            return {
                actionType: newReportIssueManageOption.type,
                title: "Need Help",
                meta: newReportIssueManageOption.meta
            }
        }
    }

    private async getActions(userContext: UserContext, bookingDetail: BookingDetail, isLiveSGTProduct: boolean, product: ConsultationProduct): Promise<Action[]> {
        if (!CareUtil.isComplteted(bookingDetail)) {
            const zoomUrl = CareUtil.getZoomLink(bookingDetail)
            const isActionEnabled = CareUtil.getZoomLinkEnabled(bookingDetail)
            const isUserTwilioEnabled = await AppUtil.isLiveSGTTwilioSupported(userContext, this.hamletBusiness)
            const isTwilioExperimentEnabled = await AppUtil.isTwilioEnabled(userContext, this.hamletBusiness)
            if (isLiveSGTProduct) {
                if (isUserTwilioEnabled) {
                    const isEnabled = CareUtil.getTwilioEnabled(bookingDetail.consultationOrderResponse.appointmentActionsWithContext)
                    const action: Action = await this.careBusiness.getJoinTwilioMeetingAction(userContext, bookingDetail.booking.patientId, bookingDetail.booking.id, isLiveSGTProduct, product.productId, undefined)
                    action.isEnabled = isEnabled
                    if (!isEnabled) {
                        action.meta = {
                            reason: bookingDetail.consultationOrderResponse.appointmentActionsWithContext.videoActionWithContext.action.reasonForProhibition
                        }
                    }
                    return [action]
                } else if (isTwilioExperimentEnabled) {
                    const isEnabled = CareUtil.getTwilioEnabled(bookingDetail.consultationOrderResponse.appointmentActionsWithContext)
                    const action: Action = AppUtil.TwilioAppUpdateAction(userContext.sessionInfo.osName)
                    action.isEnabled = isEnabled
                    return [action]
                }
            }
            const action: Action = await this.careBusiness.getJoinZoomMeetingActionfromUrl(userContext, zoomUrl, bookingDetail.booking.patientId, bookingDetail.consultationOrderResponse.doctorType, bookingDetail.booking.id, bookingDetail.zoomParticipantId)
            action.isEnabled = isActionEnabled
            return [action]
        }
    }

    private getTrainerWidget(bookingDetail: BookingDetail, isLivePTProduct: boolean, isChatSupportedForUser?: boolean, chatChannelDetails?: TwilioChatChannelDetails): DoctorInfoWidget {
        const consultationResponse: ConsultationOrderResponse = bookingDetail.consultationOrderResponse
        const doctor = consultationResponse.doctor
        let rightInfo: RightInfo = null
        let unreadMessageCount = 0
        let unreadMessageText = ""
        if (!_.isEmpty(chatChannelDetails) && isChatSupportedForUser && isLivePTProduct) {
            const chatContext: CareActionContext = bookingDetail.consultationOrderResponse.appointmentActionsWithContext.chatActionWithContext.context
            const twilioUnreadMessageCountViewMap: TwilioUnreadMessageCountViewMap = chatContext.twilioUnreadMessageCountViewMap
            if (_.get(twilioUnreadMessageCountViewMap, "PATIENT", false)) {
                const patientTwilioMessageDetails = twilioUnreadMessageCountViewMap.PATIENT
                if (!_.isNull(patientTwilioMessageDetails)) {
                    unreadMessageCount = patientTwilioMessageDetails.unreadMessageCount
                    unreadMessageText = patientTwilioMessageDetails.lastMessageText
                }
            }
            rightInfo = {
                icon: "MESSAGE_BOX",
                action: {
                    actionType: "NAVIGATION",
                    url: `curefit://twiliomessagechatv2?identity=${chatChannelDetails.patientTwilioUser.identity}&channel=${chatChannelDetails.modeName}&sid=${chatContext.twilioCommunicationMode.modeSid}&memberIdentity=${chatChannelDetails.patientTwilioUser.identity}&doctorId=${consultationResponse.doctor.id}&patientId=${consultationResponse.patient.id}&isPTChat=${true}&docName=${doctor.name}&docImage=${doctor.displayImage}&subTitle=MCA`,
                    meta: {
                        unreadMessageCount,
                        unreadMessageText,
                        twilioUnreadMessageCountViewMap,
                    }
                }
            }
        }
        if (doctor) {
            return {
                widgetType: "DOCTOR_INFO_WIDGET",
                title: consultationResponse.doctor.name,
                subtitle: doctor.experience ? `${doctor.experience} ${pluralizeStringIfRequired("year", doctor.experience)} of experience` : "Trainer",
                imageUrl: consultationResponse.doctor.displayImage,
                action: {
                    actionType: "SHOW_DOCTOR_DETAILS_MODAL_V2",
                    meta: {
                        image: doctor.displayImage,
                        title: doctor.name,
                        subtitles: [
                            CareUtil.getFormatDetailedNameFromDoctorType(doctor.primarySubServiceType.code),
                            doctor.qualification,
                            doctor.experience ? `${doctor.experience} ${pluralizeStringIfRequired("year", doctor.experience)} of experience` : ""
                        ],
                        richDescriptions: CareUtil.getLivePTTrainerRichDescriptions(doctor.recommendationAffinity),
                        description: doctor.description
                    }
                },
                rightInfo: rightInfo
            }
        }
    }



    private async getTrainerChangeAnnouncement(userContext: UserContext, bookingDetail: BookingDetail): Promise<AnnouncementView> {
        if (CareUtil.isLivePTSessionConsultation(bookingDetail.consultationOrderResponse) && bookingDetail.isDoctorChanged && CareUtil.isPending(bookingDetail) && userContext.sessionInfo.appVersion >= 8.39) {
            const announcementId = `pt_trainer_change_${bookingDetail.booking.id}`
            const trainerChangeAnnouncement = await this.announcementBusiness.createAndGetAnnouncementDetails(userContext, announcementId)
            if (trainerChangeAnnouncement && trainerChangeAnnouncement.state === "CREATED") {
                const cancelAction = {
                    ...this.getCancelBookingAction(userContext, bookingDetail),
                    title: "CANCEL CLASS"
                }
                const doctorChangeDetails: AppointmentDoctorChangeDetails = await this.cultPTService.getAppointmentDoctorChangeDetails(bookingDetail.consultationOrderResponse.id)
                const headerWidget: HeaderWidget = {
                    widgetType: "HEADER_WIDGET",
                    widgetTitle: {
                        title: "Sorry! Your trainer has been changed.",
                        subTitle: doctorChangeDetails.reason
                    },
                    style: {
                        marginLeft: 20
                    },
                    headerStyle: {
                        marginLeft: 0,
                        fontSize: 18,
                        color: "#000000",
                        width: "90%"
                    },
                    subTitleStyle: {
                        marginLeft: 0,
                        fontSize: 14,
                        color: "#33363f"
                    },
                }
                const doctorInfoWidget: DoctorInfoWidget = {
                    widgetType: "DOCTOR_INFO_WIDGET",
                    title: doctorChangeDetails.newDoctor.name,
                    subtitle: doctorChangeDetails.newDoctor.experience ? `${doctorChangeDetails.newDoctor.experience} ${pluralizeStringIfRequired("year", doctorChangeDetails.newDoctor.experience)} of experience` : undefined,
                    imageUrl: doctorChangeDetails.newDoctor.displayImage,
                    containerStyle: {
                        marginLeft: 20
                    }
                }
                const dismissAction: Action = {
                    actionType: "REST_API",
                    title: "Got it",
                    meta: {
                        method: "POST",
                        url: `/user/announcement/${trainerChangeAnnouncement.announcementId}`,
                        body: { "state": "DISMISSED" }
                    }
                }
                const actions: WidgetAction[] = []
                actions.push({
                    title: "CANCEL CLASS",
                    buttonStyle: {
                        gradientColors: ["#ffffff", "#ffffff"],
                        textStyle: {
                            color: "#ff3278",
                            fontSize: 16
                        },
                        containerStyle: {
                            height: 50,
                            marginRight: 16,
                            elevation: 4
                        }
                    },
                    action: [
                        {
                            actionType: "HIDE_ANNOUNCEMENT_MODAL",
                        },
                        { ...cancelAction, completionAction: { actionType: "POP_ACTION" } },
                        dismissAction
                    ]
                })
                actions.push({
                    title: "ATTEND CLASS",
                    buttonStyle: {
                        gradientColors: ["#ff3278", "#ff3278"],
                        textStyle: {
                            color: "#ffffff",
                            fontSize: 16
                        },
                        containerStyle: {
                            height: 50,
                            elevation: 4
                        }
                    },
                    action: [
                        {
                            actionType: "HIDE_ANNOUNCEMENT_MODAL",
                        },
                        dismissAction
                    ]
                })
                const meta: WidgetizedAnnouncementMeta = {
                    position: "bottom",
                    modalStyle: {
                        justifyContent: "flex-end",
                    },
                    containerStyle: {
                        width: "100%",
                        borderBottomLeftRadius: 0,
                        borderBottomRightRadius: 0,
                        paddingTop: 20,
                    },
                    swipeToClose: false,
                    showHeader: false,
                    actionButtonContainer: {
                        left: 0,
                        right: 0
                    }
                }
                const announcementView: WidgetizedAnnouncement = {
                    type: "WIDGETIZED_GENERIC",
                    widgets: [
                        headerWidget,
                        doctorInfoWidget
                    ],
                    action: actions,
                    actionText: "Would you like to go ahead and attend this class?",
                    meta,
                    analyticsData: {
                        announcement_id: trainerChangeAnnouncement.announcementId
                    }
                }
                return announcementView
            }
        }
    }
}
export default LivePTBookingDetailViewBuilder
