import * as _ from "lodash"
import { ProductDetailPage } from "../common/views/WidgetView"
import { ChatListHistoryResponse, ChatListMessageItem } from "@curefit/albus-client"
import { CareMeEmptyListingWidget, ChatListingActionableCardWidget } from "../page/PageWidgets"
import { ActionUtil } from "@curefit/base-utils"
import { UserContext } from "@curefit/userinfo-common"
import * as momentTz from "moment-timezone"
import CareUtil from "../util/CareUtil"
import { ChatHistoryWithChannel } from "../page/vm/widgets/TrainerCustomerChatWidgetView"
import AppUtil from "../util/AppUtil"

class ChatListHistoryPageView extends ProductDetailPage {
    constructor(userContext: UserContext, chatListHistory: ChatListHistoryResponse, isPTChat: boolean ) {
        super()
        if (_.isEmpty(chatListHistory.chatMessageViewList)) {
            this.widgets.push(new CareMeEmptyListingWidget("CONSULTATION", "No Chats Yet!", "Consult our General Physicans & Paediatricians to provide care from common issues to complex illness. We promise on-time consultation - no queues & waiting."))
            this.actions = [{
                actionType: "NAVIGATION",
                url: AppUtil.isWeb(userContext) ? ActionUtil.careFitClp("clpconsultation") : CareUtil.getConsultationClpListPage(),
                title: "Book Now"
            }]
        } else {
            const itemLength = chatListHistory.chatMessageViewList.length > 0  ? chatListHistory.chatMessageViewList.length - 1 : 0
            chatListHistory.chatMessageViewList.map((item, index) => {
                this.widgets.push(this.getActionableCardWidget(userContext, item, !(itemLength === index), isPTChat))
            })
        }
    }

    getActionableCardWidget(userContext: UserContext, chatMessageItem: ChatListMessageItem, showDivider: boolean, isPTChat: boolean): ChatListingActionableCardWidget {
        const { patientId, doctorInfo, modeName, channelSid } = chatMessageItem
        const name = doctorInfo?.name
        const qualification = doctorInfo?.qualification
        const displayImage = doctorInfo?.displayImage
        let url = null
        const chatAction = CareUtil.getChatMessageActionWithoutContext(userContext, patientId, name, modeName, displayImage, qualification)
        if (isPTChat) {
            url = `curefit://twiliomessagechatv2?identity=${chatMessageItem.memberIdentity}&channel=${modeName}&doctorId=${chatMessageItem.peerId}&patientId=${chatMessageItem.patientId}&docName=${doctorInfo?.name}&docImage=${doctorInfo?.displayImage}&subTitle=MCA`
        } else {
            url = chatAction.url
        }
        if (channelSid) {
            url += `&sid=${channelSid}`
        }
        if (chatMessageItem.isExpired) {
            url += `&chatExpired=true`
        }
        if (_.get(chatMessageItem, "memberIdentity", null)) {
            url += `&memberIdentity=${chatMessageItem.memberIdentity}`
        }
        url += `&isPTChat=${isPTChat}`
        return {
            widgetType: "CHAT_LISTING_ACTIONABLE_CARD_WIDGET",
            title: `${!_.isEmpty(chatMessageItem.doctorInfo?.name) ? chatMessageItem.doctorInfo?.name : ""}`,
            subTitle: `${!_.isEmpty(chatMessageItem.lastMessageText) ? chatMessageItem.lastMessageText : ""}`,
            imageUrl: chatMessageItem.doctorInfo?.displayImage,
            unReadMessageCount: chatMessageItem.unreadMessageCount || 0,
            lastReadTimeText: momentTz.tz(chatMessageItem.lastMessageTimestamp, userContext.userProfile.timezone).calendar(null, {
                sameDay: "[Today]",
                lastDay: "DD MMM",
                lastWeek: "DD MMM",
                sameElse: "DD MMM"
            }),
            specialityInfo: [], // chatMessageItem.peerType removed it for now
            cardAction: {
                actionType: "NAVIGATION",
                url
            },
            showDivider
        }
    }
}
export default ChatListHistoryPageView
