import { CULT_CLIENT_TYPES, ICultServiceOld as ICultService } from "@curefit/cult-client"
import { inject, injectable } from "inversify"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { BASE_TYPES, Logger } from "@curefit/base"
import { UserContext } from "@curefit/userinfo-common"
import { Action } from "../common/views/WidgetView"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import AppUtil, { LIVE_PT_ZOOM_MEETING_JOIN_TITLE, ZOOM_WEB_SDK } from "../util/AppUtil"
import { ActionUtil } from "@curefit/base-utils"
import * as _ from "lodash"
import { ConsultationProduct, Doctor, DOCTOR_TYPE, SUB_CATEGORY_CODE } from "@curefit/care-common"
import { CFS_TYPES } from "@curefit/cfs-client"
import { ICFSClient as IFormService } from "@curefit/cfs-client/dist/src/ICFSClient"
import { ALBUS_CLIENT_TYPES, BookingDetail, IHealthfaceService } from "@curefit/albus-client"
import { User } from "@curefit/user-common"
import { AnnouncementBusiness } from "../announcement/AnnouncementBusiness"
import { PageTypes } from "@curefit/apps-common"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import CareUtil from "../util/CareUtil"
import { CacheHelper } from "../util/CacheHelper"
import { pluralizeStringIfRequired } from "@curefit/util-common"
import { UserAgent } from "@curefit/base-common"
import { UserAllocation } from "@curefit/hamlet-common"

export interface LivePTSessionBookActionParams {
    productId: string
    actionTitle?: string
    parentBookingId?: number
    doctorId?: number
    strictGenderCheck?: boolean
    recommendations?: Doctor[]
    fromTrialWidget?: boolean
    subCategoryCode: SUB_CATEGORY_CODE
    useFormat?: boolean
}

export interface ICareBusiness {
    getLivePTSessionBookAction(userContext: UserContext, params: LivePTSessionBookActionParams): Promise<Action>
    getJoinZoomMeetingActionfromUrl(userContext: UserContext, zoomLink: string, patientId: number, doctorType: DOCTOR_TYPE, bookingId: number, zoomParticipantId?: string, actionTitle?: string): Promise<Action>
    getJoinTwilioMeetingAction(userContext: UserContext, patientId: number, bookingId: number, isLivSGTProduct: boolean, productId: string, actionTitle?: string): Promise<Action>
}

@injectable()
export class CareBusiness implements ICareBusiness {

    constructor(
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
        @inject(BASE_TYPES.ILogger) protected logger: Logger,
        @inject(CFS_TYPES.ICFSClient) public formService: IFormService,
        @inject(ALBUS_CLIENT_TYPES.HealthfaceService) public healthfaceService: IHealthfaceService,
        @inject(ALBUS_CLIENT_TYPES.CultPersonalTrainingService) public cultPTService: IHealthfaceService,
        @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
        @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
        @inject(CUREFIT_API_TYPES.AnnouncementBusiness) public announcementBusiness: AnnouncementBusiness,
    ) {
    }

    async getLivePTSessionBookAction(userContext: UserContext, params: LivePTSessionBookActionParams): Promise<Action> {
        if (!userContext.sessionInfo.isUserLoggedIn) {
            return {
                actionType: "SHOW_ALERT_MODAL",
                title: params.actionTitle,
                meta: {
                    title: "Login Required!",
                    subTitle: "Please login to continue",
                    actions: [{ actionType: "LOGOUT", title: "Login" }]
                }
            }
        }
        const isGenderSelectionModalSupported = AppUtil.isGenderSelectionModalSupported(userContext)
        const nextAction: Action = {
            actionType: "NAVIGATION",
            url: ActionUtil.selectCareDateV1(params.productId, undefined, params.parentBookingId)
        }
        if (params.subCategoryCode === "LIVE_SGT" && await AppUtil.isNewSGTBookingEnabled(userContext, this.hamletBusiness)) {
            if (params.useFormat) {
                nextAction.url = `curefit://sgtbookingpage?productId=${params.productId}&parentBookingId=${params.parentBookingId}`
            } else {
                nextAction.url = `curefit://sgtbookingpage?parentBookingId=${params.parentBookingId}`
            }
        }
        // needed for web
        nextAction.url += "&vertical=CULTFIT"
        nextAction.url += "&productType=" + params.subCategoryCode
        if (params.doctorId) {
            nextAction.url += "&doctorId=" + params.doctorId
        }
        if (params.strictGenderCheck) {
            nextAction.url += "&strictGenderCheck=" + params.strictGenderCheck
        }
        nextAction.title = params.actionTitle
        if (params.subCategoryCode === "LIVE_SGT") {
            return nextAction
        }
        const patientsList = await this.cultPTService.getAllPatients(userContext.userProfile.userId)
        const selfPatient = !_.isEmpty(patientsList) ? _.find(patientsList, patient => patient.relationship === "Self") : undefined
        const userInfo = await this.userCache.getUser(userContext.userProfile.userId)
        const recommendationsInGenderSelectionModal = AppUtil.isWeb(userContext) || AppUtil.isLiveSGTSupported(userContext)
        if (selfPatient && (isGenderSelectionModalSupported ? selfPatient.gender : true)) {
            nextAction.url += `&patientId=${selfPatient.id}`
            if (recommendationsInGenderSelectionModal && !_.isEmpty(params.recommendations) && selfPatient.gender) {
                return await this.getRecommendationsModalAction(userContext, params, nextAction)
            } else if (selfPatient.gender && params.fromTrialWidget) {
                return await this.getWorkoutSelectionModalAction(userContext, params, nextAction)
            } else {
                return nextAction
            }
        }
        if (userInfo.gender && (!selfPatient || (selfPatient && !selfPatient.gender))) {
            const gender = userInfo.gender === "male" ? "Male" : "Female"
            nextAction.url += `&gender=${gender}`
            if (params.fromTrialWidget) {
                return await this.getWorkoutSelectionModalAction(userContext, params, nextAction)
            }
            return nextAction
        }
        else {
            if (isGenderSelectionModalSupported) {
                return await this.getGenderWorkoutSelectionModalAction(userContext, params, nextAction)
            }
            return {
                actionType: "CREATE_PATIENT",
                title: params.actionTitle,
                meta: {
                    reqParams: {
                        patientRelation: "Self",
                        formUserType: "PT_USER"
                    },
                    title: "",
                    url: nextAction.url,
                    action: nextAction
                }
            }
        }
    }

    async getJoinZoomMeetingActionfromUrl(userContext: UserContext, zoomLink: string, patientId: number, doctorType: DOCTOR_TYPE, bookingId: number, zoomParticipantId?: string, actionTitle?: string): Promise<Action> {
        const user = await userContext.userPromise
        const zoomWeAction = CareUtil.getZoomSdkActionFromUrl(userContext, zoomLink, user, patientId, doctorType, bookingId, zoomParticipantId)
        if (AppUtil.isWeb(userContext)) {
            const experimentId = ZOOM_WEB_SDK // owner shubham.raj
            const { assignmentsMap }: UserAllocation = { assignmentsMap : {}}
            if (assignmentsMap && assignmentsMap[ZOOM_WEB_SDK]) {
                const zoomSDKBucket = assignmentsMap[ZOOM_WEB_SDK].bucket
                if (!_.isEmpty(zoomSDKBucket)) {
                    const user = await userContext.userPromise
                    return CareUtil.getZoomSdkActionFromUrl(userContext, zoomLink, user, patientId, doctorType, bookingId, zoomParticipantId)
                }
            }
        }
        if (AppUtil.isLivePtTncEnabled(userContext)) {
            let nextAction: Action
            if (await AppUtil.isLivePTNewOnboardingSupported(userContext, this.hamletBusiness)) {
                if (!AppUtil.isZoomSdkSupported(userContext)) {
                    nextAction = AppUtil.zoomAppUpdateAction(userContext.sessionInfo.osName)
                } else {
                    nextAction = {
                        title: LIVE_PT_ZOOM_MEETING_JOIN_TITLE,
                        actionType: "NAVIGATION",
                        url: `curefit://${PageTypes.LivePtOnboardingOne}?nextAction=${zoomLink}&patientId=${patientId}&doctorType=${doctorType}&bookingId=${bookingId}&zoomParticipantId=${zoomParticipantId}`
                    }
                }
            } else {
                nextAction = {
                    title: LIVE_PT_ZOOM_MEETING_JOIN_TITLE,
                    actionType: "EXTERNAL_DEEP_LINK",
                    url: zoomLink,
                    meta: {
                        ...zoomWeAction.meta,
                        deepLinkType: "ZOOM",
                        deeplinkType: "ZOOM" // since app is using this variable (small L)
                    }

                }
            }
            const announcement = await this.announcementBusiness.createAndGetLivePTTnCAnnouncement(userContext, nextAction)
            if (announcement) {
                return {
                    actionType: "SHOW_ANNOUNCEMENT",
                    title: actionTitle || LIVE_PT_ZOOM_MEETING_JOIN_TITLE,
                    meta: announcement
                }
            } else {
                if (actionTitle) {
                    nextAction.title = actionTitle
                }
                return nextAction
            }
        }
        return {
            title: LIVE_PT_ZOOM_MEETING_JOIN_TITLE,
            actionType: "EXTERNAL_DEEP_LINK",
            url: zoomLink,
            meta: {
                ...zoomWeAction.meta,
                deepLinkType: "ZOOM",
            }
        }
    }

    async getJoinTwilioMeetingAction(userContext: UserContext, patientId: number, bookingId: number, isLivSGTProduct: boolean, productId: string, actionTitle?: string): Promise<Action> {
        const user = await userContext.userPromise
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "APP")
        const appVersion = userContext.sessionInfo.appVersion
        const osName = userContext.sessionInfo.osName
        let nextAction: Action
        if (appVersion >= 8.70 && osName === "android") {
            nextAction = {
                actionType: "SSOWEB",
                title: "JOIN NOW",
                url: `sgtconverse?patientId=${patientId}&tcBookingId=${bookingId}&productId=${productId}&redirectToAppOnEndCall=${true}&isMWebTwilio=${true}`,
            }
        } else if (appVersion >= 8.71 && osName === "ios") {
            nextAction = {
                title: "JOIN NOW",
                actionType: "NAVIGATION",
                url: `curefit://liveptonboardingone?isTwilio=${true}&patientId=${patientId}&tcBookingId=${bookingId}&productId=${productId}`
            }
        } else {
            nextAction = AppUtil.TwilioAppUpdateAction(userContext.sessionInfo.osName)
        }
        const announcement = await this.announcementBusiness.createAndGetLivePTTnCAnnouncement(userContext, nextAction)
        if (announcement) {
            return {
                actionType: "SHOW_ANNOUNCEMENT",
                title: actionTitle || "JOIN NOW",
                meta: announcement
            }
        } else {
            if (actionTitle) {
                nextAction.title = actionTitle
            }
            return nextAction
        }
    }

    private getRecommendationView(recommendations: Doctor[]) {
        return _.map(recommendations, doctor => {
            return {
                displayImage: doctor.displayImage,
                name: doctor.name,
                id: doctor.id,
                subtitles: [
                    CareUtil.getFormatDetailedNameFromDoctorType(doctor.primarySubServiceType.code),
                    doctor.qualification,
                    doctor.experience ? `${doctor.experience} ${pluralizeStringIfRequired("year", doctor.experience)} of experience` : ""
                ],
                richDescriptions: CareUtil.getLivePTTrainerRichDescriptions(doctor.recommendationAffinity),
            }
        })
    }

    private async getLivePTWorkouts(userContext: UserContext, subCategoryCode: SUB_CATEGORY_CODE) {
        const sellableProducts = await this.cultPTService.getConsultationSellableProducts(userContext.userProfile.cityId, subCategoryCode)
        return await Promise.all(_.map(sellableProducts.consultationTypes, async consultationItem => {
            const productDetail: ConsultationProduct = <ConsultationProduct>await this.catalogueService.getProduct(consultationItem.products[0].code)
            return {
                displayText: productDetail.subTitle,
                value: productDetail.productId
            }
        }))
    }

    private async getRecommendationsModalAction(userContext: UserContext, params: LivePTSessionBookActionParams, nextAction: Action): Promise<Action> {
        const availableWorkouts = await this.getLivePTWorkouts(userContext, params.subCategoryCode)
        return {
            actionType: "SHOW_GENDER_SELECTION_MODAL",
            title: params.actionTitle,
            meta: {
                data: [{
                    title: "Choose a workout format",
                    key: "productId",
                    type: "workout",
                    selectedValue: params.productId,
                    options: availableWorkouts
                }, {
                    type: "dividerOR"
                }, {
                    title: "Choose from our recommended trainers",
                    key: "doctorId",
                    type: "recommendation",
                    options: this.getRecommendationView(params.recommendations)
                }],
                action: {
                    ...nextAction,
                    title: "PROCEED"
                },
                showCTA: false,
                toastMessage: "Please select all the fields",
            }
        }
    }

    private async getWorkoutSelectionModalAction(userContext: UserContext, params: LivePTSessionBookActionParams, nextAction: Action): Promise<Action> {
        const availableWorkouts = await this.getLivePTWorkouts(userContext, params.subCategoryCode)
        return {
            actionType: "SHOW_GENDER_SELECTION_MODAL",
            title: params.actionTitle,
            meta: {
                data: [{
                    title: "Choose a workout format",
                    key: "productId",
                    type: "workout",
                    selectedValue: params.productId,
                    options: availableWorkouts
                }],
                action: {
                    ...nextAction,
                    title: "PROCEED"
                },
                toastMessage: "Please select all the fields",
            }
        }
    }

    private async getGenderWorkoutSelectionModalAction(userContext: UserContext, params: LivePTSessionBookActionParams, nextAction: Action): Promise<Action> {
        const availableWorkouts = await this.getLivePTWorkouts(userContext, params.subCategoryCode)
        return {
            actionType: "SHOW_GENDER_SELECTION_MODAL",
            title: "BOOK SESSION",
            meta: {
                data: [{
                    title: "Please select your gender",
                    // subTitle: "Helps assign a trainer of the same gender for your comfort",
                    key: "gender",
                    type: "gender",
                    options: [{
                        displayText: "Male",
                        value: "Male"
                    }, {
                        displayText: "Female",
                        value: "Female"
                    }]
                }, {
                    title: "Which workout would you like to book?",
                    key: "productId",
                    selectedValue: params.productId,
                    type: "workout",
                    options: availableWorkouts
                }],
                action: {
                    ...nextAction,
                    title: "PROCEED",
                    url: `${nextAction.url}`,
                },
                toastMessage: "Please select all the fields",
            }
        }
    }

    private async getGoalSelectionModalAction(userContext: UserContext, params: LivePTSessionBookActionParams, nextAction: Action, announcementId: string) {
        const skipAction: Action = {
            actionType: "REST_API",
            title: "SKIP",
            meta: {
                method: "POST",
                url: `/user/announcement/${announcementId}`,
                body: { "state": "DISMISSED" }
            }
        }
        return {
            actionType: "SHOW_GENDER_SELECTION_MODAL",
            title: "BOOK SESSION",
            meta: {
                data: [{
                    title: "Select your fitness goal",
                    type: "goal",
                    options: [{
                        displayText: "Weight Loss",
                        value: "Weight Loss"
                    }, {
                        displayText: "Muscle Gain",
                        value: "Muscle Gain"
                    }, {
                        displayText: "Stay Fit",
                        value: "Stay Fit"
                    }]
                }, {
                    title: "How often do you workout?",
                    type: "goal",
                    options: [{
                        displaytext: "Twice a week",
                        value: "Twice a week"
                    }, {
                        displaytext: "3 days a week",
                        value: "3 days a week"
                    }, {
                        displaytext: "5 days a week",
                        value: "5 days a week"
                    }, {
                        displaytext: "All days",
                        value: "All days"
                    }]
                }],
                nextAction,
                action: {
                    title: "CONFIRM",
                    actionType: "SET_LIVE_PT_GOAL",
                    nextAction
                },
                skipAction,
                toastMessage: "Please select all the fields",
            }
        }
    }
}
