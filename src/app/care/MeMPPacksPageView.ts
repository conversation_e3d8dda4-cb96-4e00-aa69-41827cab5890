import * as _ from "lodash"
import { BookingDetail } from "@curefit/albus-client"
import { Action, ProductDetailPage } from "../common/views/WidgetView"
import { CareMeEmptyListingWidget, ListingActionableCardWidget } from "../page/PageWidgets"
import { ActionUtil } from "@curefit/base-utils"
import { TimeUtil } from "@curefit/util-common"
import { UserContext } from "@curefit/userinfo-common"

class MeMPPacksPageView extends ProductDetailPage {
    constructor(packs: BookingDetail[], appVersion: number, userContext: UserContext) {
        super()
        if (_.isEmpty(packs)) {
            this.widgets.push(new CareMeEmptyListingWidget("HCU", "No Health plans Yet!", "The best way to prevent diseases is to detect them early. Opt for a full-body health check-up and get a medical & lifestyle care plan from the doctor."))
            this.actions = [{
                actionType: "NAVIGATION",
                url: ActionUtil.careFitClp("clpmp"),
                title: "Buy Health Plan Now!"
            }]
        } else {
            packs.map(pack => {
                if (pack.booking.status !== "CANCELLED" && !_.isEmpty(pack.childBookingInfos)) {
                    this.widgets.push(this.getActionableCardWidget(pack, userContext))
                }
            })
            if (_.isEmpty(this.widgets)) {
                this.widgets.push(new CareMeEmptyListingWidget("HCU", "No Health plans Yet!", "The best way to prevent diseases is to detect them early. Opt for a full-body health check-up and get a medical & lifestyle care plan from the doctor."))
                this.actions = [{
                    actionType: "NAVIGATION",
                    url: ActionUtil.careFitClp("clpmp"),
                    title: "Buy Health Plan Now!"
                }]
            }
        }
    }

    getActionableCardWidget(pack: BookingDetail, userContext: UserContext): ListingActionableCardWidget {
        let planGenerated
        const isMPV2 = _.get(pack, "booking.subCategoryCode", "") === "MP_V2"
        if (isMPV2) {
            planGenerated = pack.childBookingInfos.find(booking => booking.booking.subCategoryCode === "MP_V2_OT").stepInfosV2.find(stepInfo => stepInfo.stepInfoType === "CONSULTATION").stepState === "PLAN_GENERATED"
        } else {
            planGenerated = pack.childBookingInfos.find(booking => booking.booking.subCategoryCode === "MP_OT").stepInfosV2.find(stepInfo => stepInfo.stepInfoType === "CONSULTATION").stepState === "PLAN_GENERATED"
        }
        const subscriptionBooking = pack.childBookingInfos.find(booking => booking.booking.subCategoryCode === "MP_SUBS")
        return {
            widgetType: "LISTING_ACTIONABLE_CARD_WIDGET",
            title: pack.productTitle,
            subTitle: `For ${pack.bundleOrderResponse.patient.name}`,
            footer: subscriptionBooking ? [this.getFooter(subscriptionBooking, userContext)] : undefined,
            actions: subscriptionBooking ? this.getActions(pack.booking.id, pack.booking.productCode, planGenerated, isMPV2) : [],
            cardAction: {
                actionType: "NAVIGATION",
                url: ActionUtil.carefitbundle(pack.booking.productCode, pack.booking.subCategoryCode, pack.booking.id.toString())
            }
        }
    }

    getFooter(subscriptionBooking: BookingDetail, userContext: UserContext): {
        text: string,
        icon: string
    } {
        const stepInfo = subscriptionBooking.stepInfosV2.find(info => info.stepInfoType === "SUBSCRIPTION")
        const tz = userContext.userProfile.timezone
        switch (stepInfo.stepState) {
            case "SUBSCRIPTION_ACTIVE": return {
                text: `Active till ${TimeUtil.formatEpochInTimeZone(tz, subscriptionBooking.bundleOrderResponse.expiryTimeEpoch, "D MMM YYYY")}`,
                icon: "TIME"
            }
            case "SUBSCRIPTION_ACTIVE_WITH_AUTO_RENEW": return {
                text: `Renews on ${TimeUtil.formatEpochInTimeZone(tz, subscriptionBooking.bundleOrderResponse.expiryTimeEpoch, "D MMM YYYY")}`,
                icon: "RENEW"
            }
            case "EXPIRED": return {
                text: `Expired on ${TimeUtil.formatEpochInTimeZone(tz, subscriptionBooking.bundleOrderResponse.expiryTimeEpoch, "D MMM YYYY")}`,
                icon: "EXPIRE"
            }
        }
    }

    getActions(bookingId: number, productCode: string, planGenerated: boolean, isMPV2: boolean): Action[] {
        const actions: Action[] = []
        actions.push({
            actionType: "NAVIGATION",
            icon: "MANAGE",
            url: ActionUtil.carefitbundle(productCode, isMPV2 ? "MP_V2" : "MP", bookingId.toString()),
            title: "Manage"
        })
        return actions
    }

}
export default MeMPPacksPageView
