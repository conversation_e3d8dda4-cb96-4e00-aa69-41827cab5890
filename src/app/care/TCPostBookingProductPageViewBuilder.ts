import { ActionWithContext, BookingDetail, CareAction, ConsultationInstructionResponse, ICovidMetricsInfoResponse, MRNFormResponse } from "@curefit/albus-client"
import {
    CareProductInfoWidget,
    ConsultationInfoWidget,
    IBadgeDescriptionWidget,
    IProductWidgetDoctorInfo,
    User,
    WidgetView
} from "@curefit/apps-common"
import { ConsultationProduct, Patient } from "@curefit/care-common"
import { Feedback } from "@curefit/feedback-common"
import { CustomerIssueType } from "@curefit/issue-common"
import { titleCase } from "@curefit/util-common"
import { TimeUtil } from "@curefit/util-common/dist/src/TimeUtil"
import { Orientations, WidgetWithMetric } from "@curefit/vm-common"
import { UserContext } from "@curefit/vm-models"
import { Action, ManageOptionPayload, ProductDetailPage } from "../common/views/WidgetView"
import FeedbackPageConfigV2Cache from "../ugc/FeedbackPageConfigV2Cache"
import AppUtil, { AppFont } from "../util/AppUtil"
import { CareUtil } from "../util/CareUtil"
import TeleconsultationDetailsPageConfig from "./TeleconsultationDetailsPageConfig"


import _ = require("lodash")
import { ActionUtil } from "@curefit/base-utils"
import { Logger } from "@curefit/base"


export interface TCProductPageInterface {
    userContext: UserContext,
    product: ConsultationProduct,
    userPromise: Promise<User>,
    patientListPromise: Promise<Patient[]>,
    pageConfig: TeleconsultationDetailsPageConfig,
    bookingDetailPromise: Promise<BookingDetail>,
    bannerWidgetPromise: Promise<WidgetWithMetric>,
    mrnFormConfigPromise: Promise<MRNFormResponse>,
    feedbackPromise: Promise<Feedback>,
    followupProductsPromise: Promise<ConsultationProduct[]>,
    feedbackPageConfigV2Cache: FeedbackPageConfigV2Cache,
    consultationInstructionsPromise: Promise<ConsultationInstructionResponse[]>,
    reportIssueDataPromise: Promise<{
        issuesMapPromise: Promise<Map<string, CustomerIssueType[]>>,
        newReportIssueManageOption: ManageOptionPayload
    }>
    logger: Logger,
    covidVitalMetricInfoPromise: Promise<ICovidMetricsInfoResponse>
}
interface ConsultationInfo {
    isRescheduleEnabled: boolean,
    isCancelEnabled: boolean,
    isVideoEnabled: boolean,
    isMissedConsultation: boolean,
    isCompleted: boolean,
    isChatEnabled: boolean,
    isAudioConsultation: boolean,
    isVideoConsultation: boolean,
    isPendingConsultation: boolean,
    isDoctorUnavailableState: boolean,
    isTherapyConsultation: boolean,
    isCustomerMissed: boolean,
    isSessionType: boolean,
    isCancelledConsultation: boolean,
    isScheduledConsultation: boolean,
    isStartedConsultation: boolean,
    status: BookingDetail["consultationOrderResponse"]["status"],
    delayedInMinutes: BookingDetail["consultationOrderResponse"]["delayedInMinutes"],
}



export class TCPostBookingProductPageView extends ProductDetailPage {
    async buildView(pageParams: TCProductPageInterface) {
        const isSupportGroupCons = CareUtil.isSupportGroupProduct(pageParams.product)
        const widgetPromise: Promise<WidgetView>[] = []
        const consultationInfoPromise: Promise<ConsultationInfo> = this.getConsultationInfo(pageParams, isSupportGroupCons)
        const allWidgetPromises = this.getWidgetPromise(pageParams, consultationInfoPromise, isSupportGroupCons)
        const isDesktop = AppUtil.isDesktop(pageParams.userContext)

        widgetPromise.push(allWidgetPromises.flutterSummaryWidgetPromise)

        let actions: Action[]
        const consultationInfo = await consultationInfoPromise
        const orderResponse = (await pageParams.bookingDetailPromise).consultationOrderResponse

        if (isSupportGroupCons && !AppUtil.isWeb(pageParams.userContext)) {
            widgetPromise.push(allWidgetPromises.instructionWidgetPromise)

            let zoomAction: Action
            const zoomLink = orderResponse.patientZoomLink
            if (consultationInfo.isCompleted || consultationInfo.isMissedConsultation) {
                zoomAction = {
                    title: consultationInfo.isCompleted ? "COMPLETED" : "MISSED",
                    actionType: "NONE",
                }
            } else if (zoomLink) {
                zoomAction = {
                    title: "JOIN SESSION",
                    subtitle: "Link will activate 15 mins before the session",
                    url: zoomLink,
                    actionType: "OPEN_WEBPAGE",
                }
            } else {
                zoomAction = {
                    title: "CHECKIN",
                    subtitle: "You can check-in 15 mins before the session",
                    actionType: "NAVIGATION",
                }
            }
            // Enable only 15 mins before the session start time and till session end time
            zoomAction.disabled = zoomAction.actionType === "NONE" || (new Date().getTime() < orderResponse.startTime - 900000) || (new Date().getTime() > orderResponse.endTime)
            actions = [zoomAction]
        } else {
            widgetPromise.push(allWidgetPromises.summaryWidgetPromise)
            widgetPromise.push(allWidgetPromises.consultationStatusWidgetPromise)
            widgetPromise.push(allWidgetPromises.prescriptionWidgetPromise)
            widgetPromise.push(allWidgetPromises.bookLabTestWidgetPromise)
            widgetPromise.push(allWidgetPromises.messageChatWidgetPromise)
            widgetPromise.push(allWidgetPromises.feedbackWidgetPromise)
            // widgetPromise.push(allWidgetPromises.whyCoupleTherapyWidgetPromise)
            widgetPromise.push(allWidgetPromises.covidVitalInfoWidgetPromise)
            widgetPromise.push(allWidgetPromises.partnerUpdateWidgetPromise)
            widgetPromise.push(allWidgetPromises.mrnWidgetPromise)
            widgetPromise.push(allWidgetPromises.consultationModeWidgetPromise)
            widgetPromise.push(allWidgetPromises.bannerVideoWidgetPromise)
            widgetPromise.push(allWidgetPromises.instructionWidgetPromise)

            actions = await this.getActions(pageParams, consultationInfoPromise)
        }

        this.widgets = (await Promise.all(widgetPromise)).filter(Boolean).map(item => ({
            ...item,
            ...(isDesktop ? { orientation: "RIGHT" } : {}),
            hasDividerBelow: false
        }))

        this.actions = actions
        return this
    }

    async getConsultationInfo(pageParams: TCProductPageInterface, isSupportGroupCons: boolean): Promise<ConsultationInfo> {
        const bookingDetail = await pageParams.bookingDetailPromise
        const isFlutterFlow = AppUtil.isFromFlutterAppFlow(pageParams.userContext)
        return {
            isRescheduleEnabled: CareUtil.getRescheduleEnabled(bookingDetail) && (!isFlutterFlow || AppUtil.isTherapyRescheduleFlutterFixSupported(pageParams.userContext)),
            isCancelEnabled: CareUtil.getCancelEnabled(bookingDetail),
            isVideoEnabled: CareUtil.getVideoEnabled(bookingDetail),
            isMissedConsultation: CareUtil.isMissed(bookingDetail),
            isCustomerMissed: CareUtil.isCustomerMissed(bookingDetail),
            isCompleted: CareUtil.isComplteted(bookingDetail),
            isChatEnabled: isSupportGroupCons ? false : CareUtil.getChatEnabled(bookingDetail),
            isAudioConsultation: isSupportGroupCons ? false : CareUtil.isAudioBookingConsultation(bookingDetail),
            isVideoConsultation: CareUtil.isVideoBookingConsultation(bookingDetail),
            isPendingConsultation: CareUtil.isPending(bookingDetail),
            isDoctorUnavailableState: CareUtil.isDoctorUnavailable(bookingDetail),
            isCancelledConsultation: CareUtil.isCancelledConsultation(bookingDetail),
            isScheduledConsultation: CareUtil.isScheduledConsultation(bookingDetail),
            isStartedConsultation: CareUtil.isConsultationStarted(bookingDetail),
            isSessionType: CareUtil.isLCProduct(pageParams.product) || CareUtil.isTherapyOnlyDoctorType(pageParams.product.doctorType) || CareUtil.isPhysiotherapyProduct(pageParams.product),
            isTherapyConsultation: CareUtil.isTherapyOnlyDoctorType(pageParams.product.doctorType),
            status: bookingDetail.consultationOrderResponse.status,
            delayedInMinutes: bookingDetail.consultationOrderResponse?.delayedInMinutes
        }
    }

    async getActions(pageParams: TCProductPageInterface, consultationInfoPromise: Promise<ConsultationInfo>): Promise<Action[]> {
        const actions: Action[] = []
        const { userContext, userPromise, product, pageConfig, consultationInstructionsPromise } = pageParams
        const bookingDetail = await pageParams.bookingDetailPromise
        const followupProducts = await pageParams.followupProductsPromise
        const { isVideoEnabled, isCompleted, isRescheduleEnabled } = await consultationInfoPromise
        const isWeb = AppUtil.isWeb(pageParams.userContext)
        const rescheduleQuery = ActionUtil.serializeAsQueryParams({
            parentBookingId: bookingDetail.booking.id,
            patientId: bookingDetail.booking.patientId,
            productId: bookingDetail.booking.productCode,
            ...(isWeb ? {
                isReschedule: true,
                centerId: bookingDetail.consultationOrderResponse.center.id
            } : {})
        })
        if (isVideoEnabled || CareUtil.isSupportGroupProduct(product)) {
            const actions: Action[] = CareUtil.getVideoJoinCallAction(
                    userContext,
                    await pageParams.patientListPromise || [],
                    await userPromise,
                    product,
                    bookingDetail,
                    await consultationInstructionsPromise,
                    pageConfig,
                    pageParams.logger
            )
            if (actions) {
                return actions
            }
        }
        if (isRescheduleEnabled) {
            return [{
                title: "Reschedule",
                actionType: "RESCHEDULE_TC",
                url: `curefit://rescheduleTc${rescheduleQuery}`,
                meta: {
                    "tcBookingId": bookingDetail.booking.id,
                    "centerId": bookingDetail.consultationOrderResponse.center.id,
                    "productId": product.productId,
                    "patientId": bookingDetail.booking.patientId
                }
            }]
        }
        if (isCompleted) {
            if (!CareUtil.isSupportGroupProduct(pageParams.product) && bookingDetail?.consultationOrderResponse?.followUpContext?.enabled) {
                const parentBookingId = bookingDetail.booking.id
                const followUpConsultationId = bookingDetail.consultationOrderResponse.id
                actions.push(CareUtil.getFollowupAction(
                    userContext,
                    parentBookingId,
                    followupProducts,
                    followUpConsultationId,
                    bookingDetail.consultationOrderResponse.patient.id,
                    bookingDetail.consultationOrderResponse?.doctor?.id,
                    bookingDetail?.consultationOrderResponse?.center?.id
                ))
            }
        }
        return actions
    }

    getWidgetPromise(
        pageParams: TCProductPageInterface,
        consultationInfoPromise: Promise<ConsultationInfo>,
        isSupportGroupCons: boolean,
    ) {
        const instructionWidgetPromise = (async () => {
            const bookingDetail = await pageParams.bookingDetailPromise
            if (
                    isSupportGroupCons ||
                    (await consultationInfoPromise).isScheduledConsultation ||
                    (await consultationInfoPromise).isStartedConsultation ||
                    (
                            (await consultationInfoPromise).isMissedConsultation &&
                            bookingDetail.consultationOrderResponse.endTime >= TimeUtil.getCurrentEpoch()
                    )
            ) {
                const instructionWidget = CareUtil.getConsultationInstructionWidget(
                        await pageParams.bookingDetailPromise,
                        pageParams.pageConfig,
                        undefined,
                        await pageParams.consultationInstructionsPromise
                )
                if (isSupportGroupCons) {
                    if (AppUtil.isWeb(pageParams.userContext)) {
                        if (instructionWidget?.instructionMap?.length > 0) {
                            instructionWidget.instructionMap[0].title = "Instructions to join"
                        }
                    } else {
                        instructionWidget.showHeader = true
                        instructionWidget.header = {
                            title: "Instructions to join"
                        }
                    }
                }
                return instructionWidget
            }
            return undefined
        })()

        if (isSupportGroupCons) {
            return {
                messageChatWidgetPromise: undefined,
                mrnWidgetPromise: undefined,
                prescriptionWidgetPromise: undefined,
                consultationModeWidgetPromise: undefined,
                feedbackWidgetPromise: undefined,
                bookLabTestWidgetPromise: undefined,
                consultationStatusWidgetPromise: undefined,
                bannerVideoWidgetPromise: undefined,
                instructionWidgetPromise,
                partnerUpdateWidgetPromise: undefined,
                whyCoupleTherapyWidgetPromise: undefined,
                covidVitalInfoWidgetPromise: undefined,
                summaryWidgetPromise: !AppUtil.isWeb(pageParams.userContext) ? undefined : this.getSummaryWidget(pageParams, consultationInfoPromise, true),
                flutterSummaryWidgetPromise: this.getSummaryWidgetFlutterApp(pageParams, consultationInfoPromise, true)
            }
        }

        // All state widgets
        const messageChatWidgetPromise = (async () => {
            if ((await consultationInfoPromise).isCancelledConsultation) {
                return undefined
            }
            if (AppUtil.isTCDoctorCardUISupported(pageParams.userContext)) {
                return CareUtil.getChatWidgetWithDoctorCard(pageParams.userContext, await pageParams.bookingDetailPromise, pageParams.product)
            }
            return CareUtil.getChatWidget(pageParams.userContext, await pageParams.bookingDetailPromise, pageParams.product)
        })()

        const partnerUpdateWidgetPromise = (async () => {
            return CareUtil.getPartnerUpdateInfoWidget(pageParams.userContext, pageParams.product, await pageParams.bookingDetailPromise, await pageParams.patientListPromise, {actionType: "POP_ACTION"})
        })()

        const covidVitalInfoWidgetPromise = (async () => {
            const bookingDetail = await pageParams.bookingDetailPromise
            if (!CareUtil.isCovidSpecialist(pageParams.product)) {
                return undefined
            }
            if (
                (await consultationInfoPromise).isCancelledConsultation ||
                AppUtil.isWeb(pageParams.userContext) ||
                !pageParams?.covidVitalMetricInfoPromise ||
                ((await consultationInfoPromise).isCompleted && !bookingDetail?.consultationOrderResponse?.followUpContext?.enabled)
            ) {
                return undefined
            }

            return CareUtil.getCovidVitalInfoWidget(
                pageParams.userContext,
                bookingDetail.booking.patientId,
                pageParams?.covidVitalMetricInfoPromise ? await pageParams?.covidVitalMetricInfoPromise : undefined,
                false
            )
        })()

        // Missed/Doctor Unavailable State Widget
        const consultationStatusWidgetPromise = (async () => {
            const consultationInfo = await consultationInfoPromise
            const userContext = pageParams.userContext
            const isWeb = AppUtil.isWeb(userContext)

            if (consultationInfo.isDoctorUnavailableState) {
                const title = "UNAVAILABLE"
                const description = `${titleCase(CareUtil.getDoctorText(pageParams.product.doctorType))} is unavailable for your ${consultationInfo.isSessionType ? "session" : "consultation"}. We request you to reschedule your appointment to a different slot. Apologize for the inconvenience.`

                return isWeb ? this.getBadgeDescriptionWidget(userContext, title, description, "DANGER", "smoke") : this.getColoredInfoWidget(
                    title,
                    description,
                    "#d61212"
                )
            } else if (
                consultationInfo.isPendingConsultation &&
                consultationInfo.delayedInMinutes
            ) {
                const bookingDetail = await pageParams.bookingDetailPromise
                const startTime: number = bookingDetail.consultationOrderResponse.startTime || bookingDetail.consultationOrderResponse.preferredStartTime
                const title = "UPDATE"
                const description = `Your ${CareUtil.getDoctorText(pageParams.product.doctorType)} is delayed. They will join at ${TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, startTime + consultationInfo.delayedInMinutes * 60000, "hh:mm a")}`

                return isWeb ? this.getBadgeDescriptionWidget(userContext, title, description, "WARNING", "smoke") : this.getColoredInfoWidget(
                    title,
                    description,
                    "#f6d205"
                )
            } else if (consultationInfo.isMissedConsultation) {
                const title = consultationInfo.isCustomerMissed ? "CONSULTATION MISSED" : "DELAYED"
                const bookingDetail = await pageParams.bookingDetailPromise
                const startTime: number = bookingDetail.consultationOrderResponse.startTime || bookingDetail.consultationOrderResponse.preferredStartTime
                const delayedDescription = consultationInfo.delayedInMinutes && TimeUtil.getCurrentEpoch() <= (startTime + consultationInfo.delayedInMinutes * 60000)
                    ? `Your ${CareUtil.getDoctorText(pageParams.product.doctorType)} is delayed. They will join at ${TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, startTime + consultationInfo.delayedInMinutes * 60000, "hh:mm a")}`
                    : `Your ${CareUtil.getDoctorText(pageParams.product.doctorType)} is delayed. You can wait for few more minutes, or cancel or reschedule the appointment anytime.`
                const description = consultationInfo.isCustomerMissed
                    ? `You missed your ${consultationInfo.isSessionType ? "session" : "consultation"} at the scheduled slot.`
                    : delayedDescription

                return isWeb ? this.getBadgeDescriptionWidget(userContext, title, description, "WARNING", "smoke") : this.getColoredInfoWidget(
                    title,
                    description,
                    "#f6d205"
                )
            }
            return undefined
        })()

        // Missed/Scheduled/Started State Widget
        const mrnWidgetPromise = (async () => {
            const bookingDetail = await pageParams.bookingDetailPromise
            if (
                (await consultationInfoPromise).isScheduledConsultation ||
                (await consultationInfoPromise).isStartedConsultation ||
                (
                    (await consultationInfoPromise).isMissedConsultation &&
                    bookingDetail.consultationOrderResponse.endTime >= TimeUtil.getCurrentEpoch()
                )
            ) {
                return CareUtil.getConsultationMRNWidget(pageParams.userContext, bookingDetail, await pageParams.mrnFormConfigPromise)
            }
            return undefined
        })()

        const consultationModeWidgetPromise = (async () => {
            const bookingDetail = await pageParams.bookingDetailPromise
            if (
                (await consultationInfoPromise).isScheduledConsultation ||
                (await consultationInfoPromise).isStartedConsultation ||
                (
                    (await consultationInfoPromise).isMissedConsultation &&
                    bookingDetail.consultationOrderResponse.endTime >= TimeUtil.getCurrentEpoch()
                )
            ) {
                return  CareUtil.getConsultationModeWidget(
                    pageParams.userContext,
                    await pageParams.patientListPromise || [],
                    await pageParams.userPromise,
                    pageParams.product,
                    bookingDetail,
                    await pageParams.consultationInstructionsPromise,
                    pageParams.pageConfig
                )
            }
            return undefined
        })()

        const bannerVideoWidgetPromise = (async () => {
            if ((await consultationInfoPromise).isScheduledConsultation) {
                const bannerVideoWidget = await pageParams.bannerWidgetPromise
                if (!_.isEmpty(bannerVideoWidget)) {
                    return bannerVideoWidget
                }
            }
            return undefined
        })()

        const whyCoupleTherapyWidgetPromise = (async () => {
            if ((await consultationInfoPromise).isScheduledConsultation) {
                return CareUtil.getCoupleTherapyInfoWidget(pageParams.product)
            }
            return undefined
        })()

        // Completed State Widgets
        const prescriptionWidgetPromise = (async () => {
            if ((await consultationInfoPromise).isCompleted) {
                const bookingDetail = await pageParams.bookingDetailPromise
                if (!CareUtil.isTherapyOnlyConsultation(bookingDetail.consultationOrderResponse)) {
                    return CareUtil.getPrescriptionWidget(pageParams.userContext, bookingDetail, pageParams.product)
                }
            }
            return undefined
        })()

        const feedbackWidgetPromise = (async () => {
            if ((await consultationInfoPromise).isCompleted) {
                const feedback = await pageParams.feedbackPromise
                if (feedback && (feedback.rating === "NOT_RATED" || feedback.rating === "DISMISSED")) {
                    if (feedback?.cfsFormId && AppUtil.isUserformFeedBackSupported(pageParams.userContext)) {
                        return CareUtil.getProductFeedBackWidgetV2(pageParams.userContext, feedback)
                    } else {
                        const feedbackQuestion: string = await pageParams.feedbackPageConfigV2Cache.getQuestionV2(feedback)
                        return CareUtil.getProductFeedBackWidget(pageParams.userContext, feedbackQuestion, feedback)
                    }

                }
            }
            return undefined
        })()

        const bookLabTestWidgetPromise = (async () => {
            return (await consultationInfoPromise).isCompleted ? CareUtil.getLabTestWidget(pageParams.userContext, await pageParams.bookingDetailPromise) : undefined
        })()

        return {
            messageChatWidgetPromise,
            mrnWidgetPromise,
            prescriptionWidgetPromise,
            consultationModeWidgetPromise,
            feedbackWidgetPromise,
            bookLabTestWidgetPromise,
            consultationStatusWidgetPromise,
            bannerVideoWidgetPromise,
            instructionWidgetPromise,
            partnerUpdateWidgetPromise,
            whyCoupleTherapyWidgetPromise,
            covidVitalInfoWidgetPromise,
            summaryWidgetPromise: this.getSummaryWidget(pageParams, consultationInfoPromise, false),
            flutterSummaryWidgetPromise: this.getSummaryWidgetFlutterApp(pageParams, consultationInfoPromise, false)
        }
    }

    async getSummaryWidget(
        pageParams: TCProductPageInterface,
        consultationInfoPromise: Promise<ConsultationInfo>,
        isSupportGroupCons: boolean,
    ) {
        if (AppUtil.isFromFlutterAppFlow(pageParams.userContext)) {
            return undefined
        }
        const bookingDetail = await pageParams.bookingDetailPromise
        const isWeb = AppUtil.isWeb(pageParams.userContext)
        const isDesktop = AppUtil.isDesktop(pageParams.userContext)
        const isTCDoctorCardUISupported = AppUtil.isTCDoctorCardUISupported(pageParams.userContext)
        let footerInfo
        if ((await consultationInfoPromise).isCompleted && bookingDetail.consultationOrderResponse?.followUpContext?.followupMembershipDetails?.[0]?.userMessage) {
            footerInfo = {
                text: bookingDetail.consultationOrderResponse?.followUpContext?.followupMembershipDetails?.[0]?.userMessage,
                icon: "RESCHEDULE"
            }
        } else if (
            (await consultationInfoPromise).isScheduledConsultation && !(await consultationInfoPromise).isDoctorUnavailableState
        ) {
            footerInfo = CareUtil.getCancellationInfo(pageParams.userContext, bookingDetail)
        }
        if (isSupportGroupCons && isWeb) {
            footerInfo = undefined
        }
        if (!isWeb && footerInfo) {
            footerInfo.icon = undefined
        }
        let action: Action
        if (isSupportGroupCons && AppUtil.isWeb(pageParams.userContext)) {
            action = {
                actionType: "NONE",
                title: "MODERATOR",
            }
        } else {
            action = {
                actionType: "SHOW_DOCTOR_DETAILS_MODAL",
                title: "VIEW PROFILE",
                meta: {
                    ...bookingDetail.consultationOrderResponse.doctor,
                    experience: CareUtil.isTherapyOnlyDoctorType(pageParams.product.doctorType) ? undefined : bookingDetail?.consultationOrderResponse?.doctor?.experience
                }
            }
        }
        const doctorDetails: IProductWidgetDoctorInfo = isTCDoctorCardUISupported ? undefined : {
            title: bookingDetail.consultationOrderResponse.doctor.name,
            imageUrl: bookingDetail.consultationOrderResponse.doctor.displayImage,
            action,
            ctaAction: isDesktop ? (await this.getActions(pageParams, consultationInfoPromise))?.[0] : undefined
        }
        return new CareProductInfoWidget(
            pageParams.product.title,
            this.getSummarySubtitle(pageParams.userContext, bookingDetail, pageParams.product, await consultationInfoPromise),
            pageParams.product.heroImageUrl,
            isWeb ? {} : { marginBottom: -50 },
            false,
            undefined,
            undefined,
            undefined,
            undefined,
            undefined,
            await this.getSummaryActions(pageParams, consultationInfoPromise, isSupportGroupCons),
            doctorDetails,
            footerInfo,
            isSupportGroupCons ? undefined : CareUtil.getDoctorBookingPageBreadcrumbs(pageParams.userContext.sessionInfo.userAgent, pageParams.product, bookingDetail.consultationOrderResponse.doctor),
            isTCDoctorCardUISupported ? this.getSummarySubText(bookingDetail, await consultationInfoPromise) : undefined

        )
    }


    async getSummaryWidgetFlutterApp(
        pageParams: TCProductPageInterface,
        consultationInfoPromise: Promise<ConsultationInfo>,
        isSupportGroupCons: boolean,
    ) {
        if (!AppUtil.isFromFlutterAppFlow(pageParams.userContext)) {
            return undefined
        }
        const bookingDetail = await pageParams.bookingDetailPromise
        const consultationInfo = await consultationInfoPromise
        let footerInfo
        if (consultationInfo.isCompleted && bookingDetail.consultationOrderResponse?.followUpContext?.followupMembershipDetails?.[0]?.userMessage) {
            footerInfo = {
                text: bookingDetail.consultationOrderResponse?.followUpContext?.followupMembershipDetails?.[0]?.userMessage,
                icon: "RESCHEDULE"
            }
        } else if (
            consultationInfo.isScheduledConsultation && !consultationInfo.isDoctorUnavailableState
        ) {
            footerInfo = CareUtil.getCancellationInfo(pageParams.userContext, bookingDetail)
        }
        if (footerInfo) {
            footerInfo.icon = undefined
        }

        const { actions, moreAction } = this.getFlutterAppMoreAction(await this.getSummaryActions(pageParams, consultationInfoPromise, isSupportGroupCons))

        let subText = this.getSummarySubText(bookingDetail, consultationInfo)
        const rescheduleAction = bookingDetail?.consultationOrderResponse?.appointmentActionsWithContext?.rescheduleActionWithContext?.action
        if (isSupportGroupCons && rescheduleAction && rescheduleAction?.actionPermitted && rescheduleAction?.reasonForProhibition) {
            // Due to hack in Albus, reason field contains message to be shown to user in case of permitted action too
            subText = rescheduleAction?.reasonForProhibition
        }
        const tcSummaryWidget: WidgetView & {
            title: string,
            subTitle: string,
            imageUrl: string,
            footerInfo: any
            subText: string
            actions: Action[],
            moreAction?: Action,
        } = {
            widgetType: "TELECONSULTATION_SUMMARY_WIDGET",
            title: pageParams.product.title,
            imageUrl: pageParams.product.heroImageUrl,
            subTitle: this.getSummarySubtitle(pageParams.userContext, bookingDetail, pageParams.product, consultationInfo),
            footerInfo,
            actions,
            moreAction,
            subText
        }
        return tcSummaryWidget
    }

    private getSummarySubtitle(
        userContext: UserContext,
        bookingDetail: BookingDetail,
        product: ConsultationProduct,
        consultationInfo: ConsultationInfo
    ): string {
        const tz = userContext.userProfile.timezone
        const startTime = bookingDetail.consultationOrderResponse.startTime || bookingDetail.consultationOrderResponse.preferredStartTime
        const endTime = bookingDetail.consultationOrderResponse.endTime || bookingDetail.consultationOrderResponse.preferredEndTime
        if (!_.isEmpty(bookingDetail)) {
            if (consultationInfo.isCancelledConsultation) {
                return consultationInfo.isSessionType ? "Session got cancelled" : "Consultation got cancelled"
            } else if (startTime) {
                if (consultationInfo.isTherapyConsultation) {
                    const duration = TimeUtil.diffInMinutes(tz, TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(startTime), "YYYY-MM-DD HH:mm:ss"), TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(endTime), "YYYY-MM-DD HH:mm:ss"))
                    let subText = duration + " mins | "
                    subText += product.consultationMode === "INCENTRE" ? "Face to Face" : "Online"
                    subText += ` Session on ${TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, startTime, "DD MMM, h:mm a")}`
                    return subText
                } else if (AppUtil.isTCDoctorCardUISupported(userContext)) {
                    return `${TimeUtil.formatEpochInTimeZone(tz, startTime, "D MMM • h:mm a")}`
                } else {
                    return `For ${bookingDetail.consultationOrderResponse.patient.name} | ${TimeUtil.formatEpochInTimeZone(tz, startTime, "DD MMM, h:mm a")}`
                }
            }
        }
        return undefined
    }

    private getSummarySubText(
        bookingDetail: BookingDetail,
        consultationInfo: ConsultationInfo
    ): string {
        if (!_.isEmpty(bookingDetail)) {
            if (consultationInfo.isTherapyConsultation) {
                return undefined
            } else {
                return `For ${bookingDetail.consultationOrderResponse.patient.name}`
            }
        }
        return undefined
    }

    async getSummaryActions(
        pageParams: TCProductPageInterface,
        consultationInfoPromise: Promise<ConsultationInfo>,
        isSupportGroupCons: boolean,
    ) {

        const {
            isRescheduleEnabled,
            isCancelEnabled,
            isCancelledConsultation,
            isCompleted
        } = await consultationInfoPromise
        const newReportIssueManageOption = await (await pageParams.reportIssueDataPromise).newReportIssueManageOption
        const reportIssueAction = {
            actionType: "REPORT_ISSUE",
            title: "HELP",
            iconUrl: "image/icons/consultationInfoWidget/newIcons/reportIssue.png",
            meta: newReportIssueManageOption?.meta
                ? newReportIssueManageOption?.meta
                : CareUtil.getTCIssueList(await (await pageParams.reportIssueDataPromise).issuesMapPromise)
        }
        if (isCancelledConsultation) {
            return [reportIssueAction]
        }
        const {
            userContext,
            product,
        } = pageParams
        const bookingDetail = await pageParams.bookingDetailPromise
        const followupProducts = await pageParams.followupProductsPromise
        const isWeb = AppUtil.isWeb(pageParams.userContext)
        const rescheduleQuery = ActionUtil.serializeAsQueryParams({
            parentBookingId: bookingDetail.booking.id,
            patientId: bookingDetail.booking.patientId,
            productId: bookingDetail.booking.productCode,
            doctorType: pageParams.product.doctorType,
            ...(isWeb ? {
                isReschedule: true,
                centerId: bookingDetail.consultationOrderResponse.center.id
            } : {})
        })
        const meta = {
            tcBookingId: bookingDetail.booking.id,
            appointmentId: bookingDetail.consultationOrderResponse.id,
            centerId: bookingDetail.consultationOrderResponse?.center?.id,
            productId: product.productId,
            patientId: bookingDetail.booking.patientId
        }
        let rescheduleAction: Action,
            cancelAction: Action,
            // bookLabTestAction,
            emailPrescriptionAction,
            followupAction,
            chatMessageAction


        rescheduleAction = (isRescheduleEnabled || isSupportGroupCons) && !(isSupportGroupCons && isWeb) ? {
            title: "RESCHEDULE",
            actionType: "RESCHEDULE_TC",
            iconUrl: "image/icons/consultationInfoWidget/newIcons/reschedule.png",
            url: `curefit://rescheduleTc${rescheduleQuery}`,
            meta
        } : undefined

        if (isSupportGroupCons && rescheduleAction) {
            const actionWithContext: ActionWithContext = bookingDetail.consultationOrderResponse?.appointmentActionsWithContext?.rescheduleActionWithContext
            rescheduleAction.disabled = actionWithContext?.action ? !actionWithContext?.action?.actionPermitted : false
        }

        cancelAction = (isCancelEnabled || isSupportGroupCons) && !(isSupportGroupCons && isWeb) ? {
            title: "CANCEL",
            actionType: "CANCEL_TC",
            meta: meta,
            iconUrl: "image/icons/consultationInfoWidget/newIcons/cancel.png",
            color: "black",
            ...(isWeb ? {
                url: `curefit://cancelTeleconsultation${ActionUtil.serializeAsQueryParams(meta)}`
            } : {})
        } : undefined

        if (isSupportGroupCons && cancelAction) {
            const actionWithContext: ActionWithContext = bookingDetail.consultationOrderResponse?.appointmentActionsWithContext?.cancelActionWithContext
            cancelAction.disabled = actionWithContext?.action ? !actionWithContext?.action?.actionPermitted : false
        }

        if (isCompleted) {
            // let testCodesCsv
            // const age = bookingDetail.consultationOrderResponse?.patient?.formattedAge?.numOfYears
            // const cityAvailability = CareUtil.getCityAvailabilityForPrescriptionDiagTests(userContext.userProfile.cityId)
            // const diagnosticLabtestInfo: PrescriptionInfo["diagnosticLabtestInfo"] =  bookingDetail?.consultationOrderResponse?.prescriptionInfo?.diagnosticLabtestInfo

            // if (!_.isEmpty(diagnosticLabtestInfo)) {
            //     const { atHomeLabTests = [], inCentreLabTests = [] }  = diagnosticLabtestInfo
            //     const totalTests: LabTest[] = [].concat(atHomeLabTests, inCentreLabTests).filter(Boolean)
            //     if (!(!age || age < 12 || !cityAvailability || _.isEmpty(totalTests))) {
            //         testCodesCsv = totalTests.map(test => test.code).join(",")
            //     }
            // }
            // bookLabTestAction = testCodesCsv ? {
            //     actionType: "NAVIGATION",
            //     url: CareUtil.getTestListingActionUrl(bookingDetail.consultationOrderResponse.patient.id, bookingDetail.booking.id, "AT_HOME_SLOT", testCodesCsv, testCodesCsv),
            //     iconUrl: "image/icons/consultationInfoWidget/labTest.png",
            //     title: "BOOK TESTS"
            // } : undefined

            emailPrescriptionAction = CareUtil.getPrescriptionSendEmailAction(bookingDetail)
            if (emailPrescriptionAction) {
                emailPrescriptionAction = {
                    ...emailPrescriptionAction,
                    iconUrl: "image/icons/consultationInfoWidget/newIcons/mail.png",
                    title: bookingDetail.consultationOrderResponse?.consultationProduct?.hasPlan ? "PLAN" : "PRESCRIPTION",
                }
            }

            if (!_.isEmpty(bookingDetail.consultationOrderResponse.followUpContext) && bookingDetail.consultationOrderResponse.followUpContext.enabled
                    && CareUtil.isFollowUpConsultationAllowed(bookingDetail.consultationOrderResponse)) {
                followupAction = {
                    ...CareUtil.getFollowupAction(
                        userContext,
                        bookingDetail.booking.id,
                        followupProducts,
                        bookingDetail.consultationOrderResponse.id,
                        bookingDetail.consultationOrderResponse.patient.id,
                        bookingDetail.consultationOrderResponse?.doctor?.id,
                        bookingDetail?.consultationOrderResponse?.center?.id
                    ),
                    iconUrl: "image/icons/consultationInfoWidget/newIcons/reschedule.png",
                    title: "FOLLOW UP"
                }
            }
        }

        const chatAction = rescheduleAction || cancelAction || isSupportGroupCons ? undefined : CareUtil.getChatMessageAction(
            userContext,
            _.get(bookingDetail.consultationOrderResponse, "appointmentActionsWithContext.chatActionWithContext", null),
            bookingDetail.consultationOrderResponse.patient.id,
            bookingDetail.consultationOrderResponse.doctor?.name,
            CareUtil.getChatChannel(bookingDetail),
            bookingDetail.consultationOrderResponse.doctor?.displayImage,
            bookingDetail.consultationOrderResponse.doctor?.qualification,
            bookingDetail.booking.id
        )
        if (chatAction) {
            let superText
            if (chatAction.title.includes("(") && chatAction.title.includes(")")) {
                superText = Number(chatAction.title.match(/\(([^)]+)\)/)[1])
            }
            chatMessageAction = {
                ...chatAction,
                title: "MESSAGE",
                iconUrl: "image/icons/consultationInfoWidget/newIcons/message.png",
                superText
            }
        }

        return [
            followupAction,
            // emailPrescriptionAction,
            chatMessageAction,
            rescheduleAction,
            cancelAction,
            reportIssueAction
        ].filter(Boolean)
    }

    getFlutterAppMoreAction(actions: Action[]) {
        if (actions?.length > 2) {
            const top2Actions = actions.splice(0, 2)
            top2Actions[0].viewType = "PRIMARY_BUTTON"
            return {
                actions: top2Actions,
                moreAction: {
                    title: "More",
                    icon: "TRIPLE_DOT",
                    actionType: "ACTION_LIST",
                    actions: actions
                } as Action
            }
        }
        return {
            actions,
            moreAction: undefined
        }

    }

    getColoredInfoWidget(
        tagText: string,
        subtitle: string,
        tagBGColor: string,
        backgroundColor: string = "rgb(244, 244, 244)"
    ) {
        return new ConsultationInfoWidget(
            [],
            tagText,
            subtitle,
            {
                marginHorizontal: 0,
                paddingHorizontal: 20,
                paddingTop: 12,
                paddingBottom: 0,
                backgroundColor: backgroundColor || "transparent",
                shadowColor: "transparent",
                elevation: 0,
                borderRadius: 0,
            },
            {
                color: "white",
                backgroundColor: tagBGColor || "#d61212",
                borderRadius: 3,
                fontSize: 12,
                fontFamily: AppFont.Bold,
                textAlign: "center",
                paddingTop: 3,
            },
            {
                color: "rgb(102, 102, 102)",
                fontSize: 14,
                fontFamily: AppFont.Regular
            }
        )
    }

    getBadgeDescriptionWidget(userContext: UserContext, title: string, description: string, badgeType: "WARNING" | "SUCCESS" | "DANGER", cardColor: "smoke"): IBadgeDescriptionWidget {
        const orientation = AppUtil.isDesktop(userContext) ? "RIGHT" : undefined

        return {
            title,
            description,
            badgeType,
            cardColor,
            orientation,
            widgetType: "BADGE_DESCRIPTION_WIDGET"
        }
    }
}
