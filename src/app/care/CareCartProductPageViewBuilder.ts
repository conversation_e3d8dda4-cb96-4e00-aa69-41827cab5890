import { inject, injectable } from "inversify"
import * as _ from "lodash"
import {
    ALBUS_CLIENT_TYPES,
    IHealthfaceService,
    Patient,
    SUB_CATEGORY_CODE,
} from "@curefit/albus-client"
import {
    DiagnosticProduct,
    DiagnosticProductResponse,
    AddOnProduct
} from "@curefit/care-common"
import {
    CATALOG_CLIENT_TYPES,
    ICatalogueService
} from "@curefit/catalog-client"
import {
    GMF_CLIENT_TYPES,
    IGMFClient as IGoalManagementService
} from "@curefit/gmf-client"
import {
    OFFER_SERVICE_CLIENT_TYPES, OfferServiceV3
} from "@curefit/offer-service-client"
import AppUtil from "../util/AppUtil"
import { Session, UserContext } from "@curefit/userinfo-common"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import ICRMIssueService from "../crm/ICRMIssueService"
import TherapyPageConfig from "../therapy/TherapyPageConfig"
import CartProductPageView from "./CartProductPageView"
import IssueBusiness  from "../crm/IssueBusiness"
import { CacheHelper } from "../util/CacheHelper"
import { CFS_TYPES } from "@curefit/cfs-client"
import { ICFSClient as IFormService } from "@curefit/cfs-client/dist/src/ICFSClient"
import { CULT_CLIENT_TYPES } from "@curefit/cult-client"
import { ICultServiceOld as ICultService } from "@curefit/cult-client/dist/src/ICultServiceOld"
import { HamletBusiness, HAMLET_TYPES } from "@curefit/hamlet-node-sdk"
import { ICareBusiness } from "./CareBusiness"
import { FUSE_CLIENT_TYPES, IDiagnosticService } from "@curefit/fuse-node-client"
import { OLLIVANDER_CLIENT_TYPES, IOllivanderCityService } from "@curefit/ollivander-node-client"
import { BASE_TYPES, Logger } from "@curefit/base"
import { CareWidgetUtil } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
const clone = require("clone")

@injectable()
export class CareCartProductPageViewBuilder {
    constructor(
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) protected catalogueService: ICatalogueService,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) private offerServiceV3: OfferServiceV3,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(CUREFIT_API_TYPES.CRMIssueService) private CRMIssueService: ICRMIssueService,
        @inject(GMF_CLIENT_TYPES.IGMFClient) public gmfService: IGoalManagementService,
        @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
        @inject(ALBUS_CLIENT_TYPES.CultPersonalTrainingService) private cultPtService: IHealthfaceService,
        @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness,
        @inject(CUREFIT_API_TYPES.TherapyPageConfig) private therapyPageConfig: TherapyPageConfig,
        @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
        @inject(CFS_TYPES.ICFSClient) public formService: IFormService,
        @inject(CULT_CLIENT_TYPES.CultFitService) public cultFitService: ICultService,
        @inject(CULT_CLIENT_TYPES.MindFitService) public mindFitService: ICultService,
        @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
        @inject(CUREFIT_API_TYPES.CareBusiness) public careBusiness: ICareBusiness,
        @inject(FUSE_CLIENT_TYPES.IDiagnosticSellerService) protected diagnosticService: IDiagnosticService,
        @inject(OLLIVANDER_CLIENT_TYPES.IOllivanderCityService) private ollivanderService: IOllivanderCityService,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
    ) {}

    async getBeforeBookingPage(userContext: UserContext, session: Session, productId: string, subCategoryCode: SUB_CATEGORY_CODE) {
        const isNotLoggedIn = session.isNotLoggedIn, deviceId = session.deviceId
        const userId = userContext.userProfile.userId
        const productPromise = this.catalogueService.getProduct(productId)
        const patientsListPromise: Promise<Patient[]> = this.healthfaceService.getAllPatients(userId)
        const healthfaceProductPromise = this.healthfaceService.getProductInfoDetails("DIAGNOSTICS", {
            productCodeCsv: productId,
            skipProductDetails: false,
        })
        const product = await productPromise
        const baseProduct: DiagnosticProduct = <DiagnosticProduct>clone(product)
        const packageProduct = <DiagnosticProductResponse>((await healthfaceProductPromise)[0].baseSellableProduct)
        const addOnProducts: AddOnProduct[] = packageProduct?.diagnosticProductResponse?.addOnProducts
        const addOnProductCodes = addOnProducts?.map(product => {return product?.diagnosticProduct?.code})
        const addOnProductInfosPromise = _.map(addOnProductCodes, async (productCode) => {
            return <DiagnosticProduct>(await this.catalogueService.getProduct(productCode))
        })
        const careCart = await this.diagnosticService.getCart(Number(userId))
        const userPromise = this.userCache.getUser(userId)
        const productCodes = [packageProduct.productCode].concat(addOnProductCodes)
        const offersPromise = CareWidgetUtil.getCareProductOffersFromUserContext(userContext, "DIAGNOSTICS",
            productCodes, AppUtil.callSourceFromContext(userContext), this.serviceInterfaces, true)
        const source = AppUtil.callSourceFromContext(userContext)
        const {offerIds} = await this.offerServiceV3.getApplicableCartOffersForCare({
            userInfo: {userId, deviceId},
            requiredOfferTypes: ["DIAGNOSTICS"],
            source,
            cityId: userContext.userProfile.cityId
        })
        const cartOffers = _.values((await this.offerServiceV3.getOffersByIds(offerIds)).data)
        const sellerDetailsPromise = this.diagnosticService.getDiagnosticSellerForCity(userContext.userProfile.cityId)
        let baseCartProduct: DiagnosticProduct

        if (!_.isEmpty(careCart?.diagnosticCartItems)) {
            baseCartProduct = <DiagnosticProduct>await this.catalogueService.getProduct(careCart.diagnosticCartItems[0].productCode)
        }

        return new CartProductPageView(await userPromise, userContext, session, productCodes, await careCart, baseProduct, packageProduct, await Promise.all(addOnProductInfosPromise), baseCartProduct, await patientsListPromise, await offersPromise, cartOffers, await sellerDetailsPromise)
    }
}

export default CareCartProductPageViewBuilder
