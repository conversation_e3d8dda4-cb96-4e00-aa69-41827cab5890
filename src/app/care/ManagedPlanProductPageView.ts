import * as _ from "lodash"
import { RUPEE_SYMBOL } from "@curefit/finance-common"
import { OrderProduct } from "@curefit/order-common"
import { DiagnosticProduct } from "@curefit/care-common"
import { ProductPrice } from "@curefit/product-common"
import { SubscriptionType } from "@curefit/base-common"
import {
    DiagnosticTestProduct,
    ManagedPlanPackInfo,
    ManagedPlanSellableProduct,
    MPChildProduct,
    Patient
} from "@curefit/care-common"
import { OfferV2, PackOffersResponse } from "@curefit/offer-common"
import { OfferUtil } from "@curefit/base-utils"
import {
    Action,
    CenterSelectionWidget,
    DescriptionWidget,
    getOffersWidget,
    GradientCard,
    GradientCarouselCard,
    Header,
    InfoCard,
    PricingWidgetExpandedValue,
    PricingWidgetRecurringValue,
    ProductDetailPage,
    ProductListWidget,
    ProductPricingWidget,
    WidgetView
} from "../common/views/WidgetView"
import { CareUtil } from "../util/CareUtil"
import { PageWidget, PageWidgetTitle } from "../page/Page"
import { DiagnosticsTestListWidget } from "../page/PageWidgets"
import { ActionUtil } from "@curefit/base-utils"
import { CareAddonItem } from "../cart/CartViewBuilder"
import { UserContext } from "@curefit/userinfo-common"
import { IssueDetailView } from "../crm/IssueBusiness"
import { TimeUtil } from "@curefit/util-common"

const SUPPORTED_GRADIENT_LIST_CAROUSEL = 7.24
export interface ReportIssueMeta {
    code: string
    title: string
    confirmation: string
}

class ManagedPlanProductPageView extends ProductDetailPage {
    public pageContext: any

    constructor(userContext: UserContext, isNotLoggedIn: boolean, product: DiagnosticProduct, isMultiCenterEnabled: boolean, packageProduct: ManagedPlanSellableProduct, subscriptionStartDate: number, patientsList?: Patient[], subscriptionCode?: string,
        offers?: PackOffersResponse, deviceOffers?: PackOffersResponse, recurringTests?: DiagnosticTestProduct[], issues?: IssueDetailView[]) {
        super()
        this.widgets.push(this.summaryWidget(packageProduct))
        // this.navigationAction = {
        //     title: "NEED HELP ?",
        //     action: {
        //         actionType: "REPORT_ISSUE",
        //         meta: {
        //             title: "Help",
        //             issues: issues
        //         }
        //     },
        //     textStyle: {},
        //     containerStyle: {}
        // }
        const descriptions = [{
            subTitle: packageProduct.productDescription,
        }]
        const isGradientListCarouselSupported = userContext.sessionInfo.appVersion >= SUPPORTED_GRADIENT_LIST_CAROUSEL
        const widget = new DescriptionWidget(descriptions)
        widget.iconType = undefined
        widget.dividerType = "SMALL"
        this.widgets.push(widget)
        const devices = packageProduct.childProducts.filter(product => product.baseSellableProduct.categoryCode === "DEVICE" && product.baseSellableProduct.subCategoryCode !== "INT_WELCOME_KIT")
        const mandatoryProducts: MPChildProduct[] = packageProduct.childProducts.filter(product => product.childProductType === "CHILD_MANDATORY")
        const subscriptionProducts: MPChildProduct[] = mandatoryProducts.filter(product => product.baseSellableProduct.subCategoryCode === "MP_SUBS")
        if (subscriptionCode === undefined) {
            subscriptionCode = subscriptionProducts[0].baseSellableProduct.productCode
        }

        const otProducts = mandatoryProducts.filter(product => product.baseSellableProduct.subCategoryCode === "MP_OT")
        const deviceMandatoryProducts = mandatoryProducts.filter(product => product.baseSellableProduct.categoryCode === "DEVICE" && product.baseSellableProduct.subCategoryCode !== "INT_WELCOME_KIT")

        const expandedSectionData: { title: string, price: ProductPrice }[] = []
        let totalListingPrice = 0
        let totalMRPPrice = 0
        let recurringPrice = 0
        let duration = 0
        let currency = "INR"
        const orderProducts: OrderProduct[] = []
        const appliedOffers: OfferV2[] = []
        otProducts.map(otMainProduct => {
            const otProduct = otMainProduct.baseSellableProduct
            const offerDetail = OfferUtil.getPackOfferAndPriceForCare(otProduct, offers)
            currency = offerDetail.price.currency
            appliedOffers.push(...offerDetail.offers)
            const otProductPrice = offerDetail.price.listingPrice
            totalListingPrice += otProductPrice
            totalMRPPrice += otProduct.mrp
            expandedSectionData.push({
                title: otProduct.productName,
                price: {
                    listingPrice: otProductPrice,
                    mrp: otProduct.mrp,
                    currency: offerDetail.price.currency
                }
            })
            orderProducts.push({
                productId: otProduct.productCode,
                productType: "BUNDLE",
                quantity: 1,
                option: {
                    source: "APP",
                    offerV2Ids: !_.isEmpty(offerDetail.offers) ? offerDetail.offers.map(offer => offer.offerId) : [],
                    parentProductCode: product.productId,
                    categoryCode: otProduct.categoryCode,
                    subCategoryCode: otProduct.subCategoryCode,
                    groupCode: otMainProduct.groupCode,
                    childProductType: otMainProduct.childProductType
                }
            })
        })

        if (!_.isEmpty(deviceMandatoryProducts)) {
            deviceMandatoryProducts.map(deviceMainProduct => {
                const deviceProduct = deviceMainProduct.baseSellableProduct
                const offerDetail = OfferUtil.getPackOfferAndPriceForCare(deviceProduct, deviceOffers)
                appliedOffers.push(...offerDetail.offers)
                const deviceListingPrice = offerDetail.price.listingPrice
                totalListingPrice += deviceListingPrice
                totalMRPPrice += deviceProduct.mrp
                expandedSectionData.push({
                    title: deviceProduct.productName,
                    price: {
                        listingPrice: deviceListingPrice,
                        mrp: deviceProduct.mrp,
                        currency: offerDetail.price.currency
                    }
                })
                orderProducts.push({
                    productId: deviceProduct.productCode,
                    productType: "DEVICE",
                    quantity: 1,
                    option: {
                        parentProductCode: product.productId,
                        offerV2Ids: !_.isEmpty(offerDetail.offers) ? offerDetail.offers.map(offer => offer.offerId) : [],
                        categoryCode: deviceProduct.categoryCode,
                        subCategoryCode: deviceProduct.subCategoryCode,
                        groupCode: deviceMainProduct.groupCode,
                        childProductType: deviceMainProduct.childProductType
                    }
                })
            })

        }

        const expandedSection: PricingWidgetExpandedValue = {
            title: "Onboarding charges",
            price: {
                listingPrice: totalListingPrice,
                mrp: totalMRPPrice,
                currency
            },
            data: expandedSectionData
        }
        const recurringSection: PricingWidgetRecurringValue[] = []
        subscriptionProducts.map(subscriptionMainProduct => {
            const subscriptionProduct = subscriptionMainProduct.baseSellableProduct
            const offerDetail = OfferUtil.getPackOfferAndPriceForCare(subscriptionProduct, offers)
            const subscriptionProductListingPrice = offerDetail.price.listingPrice
            const perMonthPrice = Math.ceil(subscriptionProductListingPrice / (subscriptionProduct.duration / 30))

            recurringSection.push({
                title: subscriptionProduct.infoSection.shortname,
                price: {
                    listingPrice: subscriptionProductListingPrice,
                    mrp: subscriptionProduct.mrp,
                    currency: offerDetail.price.currency
                },
                priceMeta: perMonthPrice === subscriptionProductListingPrice ? `Per Month` : `${RUPEE_SYMBOL} ${perMonthPrice}/Month`,
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtil.carefitbundle(product.productId, "MP"),
                    meta: {
                        subscriptionCode: subscriptionProduct.productCode
                    }
                },
                selected: subscriptionCode === subscriptionProduct.productCode
            })
            if (subscriptionCode === subscriptionProduct.productCode) {
                totalListingPrice += subscriptionProductListingPrice
                totalMRPPrice += subscriptionProduct.mrp
                recurringPrice = subscriptionProductListingPrice
                duration = subscriptionProduct.duration / 30
                appliedOffers.push(...offerDetail.offers)
                const startDate = TimeUtil.formatEpochInTimeZoneDateFns(userContext.userProfile.timezone, subscriptionStartDate, "yyyy-MM-dd")
                const endDate = TimeUtil.addDays(userContext.userProfile.timezone, startDate, subscriptionProduct.duration)
                const subscriptionType: SubscriptionType = subscriptionProduct.infoSection.subscriptionType
                orderProducts.push({
                    productId: subscriptionProduct.productCode,
                    productType: "BUNDLE",
                    quantity: 1,
                    option: {
                        offerV2Ids: !_.isEmpty(offerDetail.offers) ? offerDetail.offers.map(offer => offer.offerId) : [],
                        parentProductCode: product.productId,
                        categoryCode: subscriptionProduct.categoryCode,
                        subCategoryCode: subscriptionProduct.subCategoryCode,
                        groupCode: subscriptionMainProduct.groupCode,
                        childProductType: subscriptionMainProduct.childProductType,
                        startDate,
                        endDate,
                        subscriptionType,
                        autorenewalEnabled: true
                    }
                })
            }
        })

        const finalPrice = {
            listingPrice: totalListingPrice,
            mrp: totalMRPPrice,
            currency
        }

        const priceWidget: ProductPricingWidget = this.getProductPricingWidget(expandedSection, recurringSection)
        priceWidget.dividerType = "SMALL"
        if (isMultiCenterEnabled) {
            this.widgets.push(this.getAvailableCentersWidget(product))
        }
        this.widgets.push(priceWidget)
        if (!_.isEmpty(appliedOffers)) {
            this.widgets.push(getOffersWidget("Offers Applied", appliedOffers))
        }
        let howItWorksItem, packContentsDetailed
        packageProduct.infoSection.children.map(infoSection => {
            switch (infoSection.type) {
                case "PACK_STEPS": howItWorksItem = infoSection; break
                case "PACK_CONTENTS_DETAILED": packContentsDetailed = infoSection; break
            }
        })
        this.widgets.push(isGradientListCarouselSupported ? this.getWhatsInPackWidgetV2(userContext, product.productId, packContentsDetailed) : this.getWhatsInPackWidget(packContentsDetailed))
        if (!_.isEmpty(devices)) {
            this.widgets.push(this.getDeviceListWidget(devices))
        }
        this.widgets.push(this.getDiagnosticsTestListWidget(userContext, product.productId, "Onboarding Tests Included", 2, otProducts[0].baseSellableProduct.packageProduct.basePackageProduct.items))
        if (!_.isEmpty(recurringTests)) {
            this.widgets.push(this.getDiagnosticsTestListWidget(userContext, product.productId, "Recurring Tests", 1, recurringTests))
        }
        this.widgets.push(this.getHowItWorksWidget(howItWorksItem))
        this.actions = this.getPreBookingActions(isNotLoggedIn, patientsList, finalPrice, recurringPrice, duration, orderProducts)
    }

    private getAvailableCentersWidget(product: DiagnosticProduct): CenterSelectionWidget {
        const action: Action = {
            actionType: "NAVIGATION",
            url: `curefit://selectcarecenter?productId=${product.productId}`
        }
        const centerSelectionWidget: CenterSelectionWidget = {
            title: "View Available Centres",
            canChangeCenter: true,
            widgetType: "CENTER_PICKER_WIDGET",
            dividerType: "SMALL",
            action: action
        }
        return centerSelectionWidget
    }

    private getProductPricingWidget(expandedSection: PricingWidgetExpandedValue, recurringSection: PricingWidgetRecurringValue[]): ProductPricingWidget {

        return {
            widgetType: "PRODUCT_PRICING_WIDGET",
            header: {
                title: "Pricing"
            },
            footerText: "Subscription will renew automatically until cancelled",
            sections: [{
                title: "ONE TIME CHARGES",
                type: "EXPANDED",
                value: expandedSection
            },
            {
                title: "CHOOSE A SUBSCRIPTION PLAN",
                type: "RECURRING",
                value: recurringSection
            }]
        }
    }

    private summaryWidget(product: ManagedPlanSellableProduct): WidgetView {
        const checkupSummaryWidget: WidgetView & {
            productId: string;
            title: string;
            image: string;
            hasDividerBelow: boolean
        } = {
            widgetType: "HEALTHCHECKUP_SUMMARY_WIDGET",
            title: product.productName,
            productId: product.productCode,
            image: product.heroImageUrl,
            hasDividerBelow: false
        }
        return checkupSummaryWidget
    }


    private getDeviceListWidget(devices: MPChildProduct[]): WidgetView {
        const items: CareAddonItem[] = []
        devices.map(device => {
            items.push({
                image: device.baseSellableProduct.imageUrl,
                title: device.baseSellableProduct.productName,
                price: {
                    listingPrice: device.baseSellableProduct.listingPrice,
                    mrp: device.baseSellableProduct.mrp,
                    currency: "INR"
                },
                action: {
                    actionType: "SHOW_INFO_MODAL",
                    meta: {
                        icon: device.baseSellableProduct.imageUrl,
                        title: device.baseSellableProduct.productName,
                        subTitle: device.baseSellableProduct.productDescription
                    }
                },
                added: device.childProductType === "CHILD_MANDATORY",
                productId: device.baseSellableProduct.productCode,
            })
        })
        const deviceListWidget: WidgetView & {
            title: string,
            items: CareAddonItem[]
        } = {
            widgetType: "DEVICE_LIST_WIDGET",
            title: "Devices",
            items
        }
        return deviceListWidget
    }

    private getWhatsInPackWidget(packContentsDetailed: ManagedPlanPackInfo): ProductListWidget {
        const header: Header = {
            title: packContentsDetailed.title,
            color: "#000000"
        }

        const cards: GradientCard[] = []
        packContentsDetailed.children.forEach(item => {
            switch (item.type) {
                case "MP_CONSULTATION":
                    cards.push({
                        title: item.title,
                        action: {
                            actionType: "SHOW_INFO_MODAL",
                            meta: {
                                icon: item.imageUrl,
                                title: item.title,
                                subTitle: item.desc
                            }
                        },
                        shadowColor: "#76e997",
                        gradientColors: ["#76e997", "#2cc2d3"],
                        icon: item.type
                    })
                    break
                case "MP_DIAGNOTIC":
                    cards.push({
                        title: item.title,
                        action: {
                            actionType: "SHOW_INFO_MODAL",
                            meta: {
                                icon: item.imageUrl,
                                title: item.title,
                                subTitle: item.desc
                            }
                        },
                        shadowColor: "#17d8e5",
                        gradientColors: ["#17d8e5", "#ac9aff"],
                        icon: item.type
                    })
                    break
                case "MP_PLAN":
                    cards.push({
                        title: item.title,
                        action: {
                            actionType: "SHOW_INFO_MODAL",
                            meta: {
                                icon: item.imageUrl,
                                title: item.title,
                                subTitle: item.desc
                            }
                        },
                        shadowColor: "#fb8a72",
                        gradientColors: ["#fb8a72", "#f64cac"],
                        icon: item.type
                    })
                    break
                case "MP_FEEDBACK":
                    cards.push({
                        title: item.title,
                        action: {
                            actionType: "SHOW_INFO_MODAL",
                            meta: {
                                imageUrl: item.imageUrl,
                                title: item.title,
                                subTitle: item.desc
                            }
                        },
                        shadowColor: "#f29458",
                        gradientColors: ["#f29458", "#fd796d"],
                        icon: item.type
                    })
                    break
                case "MP_DEVICE":
                    cards.push({
                        title: item.title,
                        action: {
                            actionType: "SHOW_INFO_MODAL",
                            meta: {
                                icon: item.imageUrl,
                                title: item.title,
                                subTitle: item.desc
                            }
                        },
                        shadowColor: "#8cc5f3",
                        gradientColors: ["#8cc5f3", "#c495e0"],
                        icon: item.type
                    })
                    break
            }

        })
        return new ProductListWidget("GARDIENT_CARD", header, cards)
    }

    private getWhatsInPackWidgetV2(userContext: UserContext, productId: string, packContentsDetailed: ManagedPlanPackInfo): ProductListWidget {
        const header: Header = {
            title: packContentsDetailed.title,
            color: "#000000"
        }

        const cards: GradientCarouselCard[] = []
        const bundleProducts: any[] = []
        packContentsDetailed.children.forEach((item, index) => {
            bundleProducts.push({
                icon: item.imageUrl,
                title: item.title,
                subTitle: item.desc
            })
            switch (item.type) {
                case "MP_CONSULTATION":
                    cards.push({
                        title: item.title,
                        shadowColor: "#76e997",
                        gradientColors: ["#76e997", "#2cc2d3"],
                        icon: item.type,
                        moreIndex: index
                    })
                    break
                case "MP_DIAGNOTIC":
                    cards.push({
                        title: item.title,
                        shadowColor: "#17d8e5",
                        gradientColors: ["#17d8e5", "#ac9aff"],
                        icon: item.type,
                        moreIndex: index
                    })
                    break
                case "MP_PLAN":
                    cards.push({
                        title: item.title,
                        shadowColor: "#fb8a72",
                        gradientColors: ["#fb8a72", "#f64cac"],
                        icon: item.type,
                        moreIndex: index
                    })
                    break
                case "MP_FEEDBACK":
                    cards.push({
                        title: item.title,
                        shadowColor: "#f29458",
                        gradientColors: ["#f29458", "#fd796d"],
                        icon: item.type,
                        moreIndex: index
                    })
                    break
                case "MP_DEVICE":
                    cards.push({
                        title: item.title,
                        shadowColor: "#8cc5f3",
                        gradientColors: ["#8cc5f3", "#c495e0"],
                        icon: item.type,
                        moreIndex: index
                    })
                    break
            }
        })
        const action: Action = {
            actionType: CareUtil.getNewCarouselListActionType(userContext, "SHOW_GRADIENT_CAROUSEL_LIST"),
            meta: {
                type: "GRADIENT_CAROUSEL_LIST",
                productId: productId,
                bundleProducts: bundleProducts
            }
        }
        return new ProductListWidget("GRADIENT_CAROUSEL_CARD", header, cards, null, false, "LEFT", false, 1, "", action)
    }

    private getHowItWorksWidget(packInfo: ManagedPlanPackInfo): ProductListWidget {
        const header: Header = {
            title: packInfo.title,
            color: "#000000"
        }
        const infoCards: InfoCard[] = []
        packInfo.children.forEach(item => {
            infoCards.push({
                subTitle: item.desc,
                icon: item.imageUrl
            })
        })
        return new ProductListWidget("SMALL", header, infoCards)
    }

    private getPreBookingActions(isNotLoggedIn: boolean, patientsList: Patient[], finalPrice: ProductPrice, recurringPrice: number, duration: number, orderProducts: OrderProduct[]): Action[] {

        if (isNotLoggedIn === true) {
            return [
                {
                    actionType: "SHOW_ALERT_MODAL",
                    title: "Get pack",
                    meta: {
                        title: "Login Required!",
                        subTitle: "Please login to continue",
                        actions: [{ actionType: "LOGOUT", title: "Login" }]
                    }
                }
            ]
        } else {
            const action: Action = {
                actionType: "SUBSCRIBE_MP",
                meta: {
                    orderProducts: orderProducts
                }
            }
            const subText = duration === 1 ? `${RUPEE_SYMBOL} ${recurringPrice} charged monthly starting next month` : `${RUPEE_SYMBOL} ${recurringPrice} charged every ${duration} months`
            const meta = {
                subText: subText,
                title: "Total Pay",
                price: finalPrice,
                showCheckoutAction: true,
                formUserType: "MANAGED_PLAN_USER"
            }
            const calloutText = "This personalized plan is available only for yourself."
            const pageAction = CareUtil.getSelfPatientSelectionModalAction(patientsList, action, undefined, calloutText, meta)
            return [pageAction]
        }
    }

    private getDiagnosticsTestListWidget(userContext: UserContext, productId: string, title: string, gridSize: number, bundleProducts: DiagnosticTestProduct[]): PageWidget {
        const infoCards: InfoCard[] = []
        bundleProducts.map((product, index) => {
            infoCards.push({
                title: product.name,
                image: product.imageUrl,
                moreIndex: index
            })
        })
        const action: Action = {
            actionType: CareUtil.getNewCarouselListActionType(userContext, "SHOW_DIAGNOSTICS_TEST_DETAIL_LIST"),
            meta: {
                type: "DIAGNOSTIC_TEST_DETAIL_LIST",
                productId: productId,
                bundleProducts: bundleProducts
            }
        }
        const widgetTitle: PageWidgetTitle = {
            title: title
        }
        return new DiagnosticsTestListWidget(gridSize, infoCards, widgetTitle, action)
    }
}

export default ManagedPlanProductPageView
