import {
    Action,
    CenterSelectionWidget,
    DescriptionWidget,
    getOffersWidget,
    InfoCard,
    PricingWidgetRecurringValue,
    ProductDetailPage,
    ProductPricingSection,
    ProductPricingWidget,
    SelectCenterAction,
    WidgetType,
    WidgetView
} from "../common/views/WidgetView"
import * as _ from "lodash"
import { RUPEE_SYMBOL } from "@curefit/finance-common"
import { OrderProduct } from "@curefit/order-common"
import { ProductPrice } from "@curefit/product-common"
import { DiagnosticProduct } from "@curefit/care-common"
import {
    ManagedPlanPackInfo,
} from "@curefit/care-common"
import {
    ManagedPlanPackInfoDetail,
} from "@curefit/care-common"
import {
    ManagedPlanSellableProduct,
} from "@curefit/care-common"
import { MPChildProduct } from "@curefit/care-common"
import { Patient } from "@curefit/care-common"
import { PreferredCenterResponse } from "@curefit/albus-client"
import { OfferV2, PackOffersResponse } from "@curefit/offer-common"
import { OfferUtil } from "@curefit/base-utils"
import CareUtil, { CARE_NEW_CENTER_SELECTION_SUPPORTED } from "../util/CareUtil"
import { PageWidgetTitle } from "../page/Page"
import { JourneyWidget } from "../page/PageWidgets"
import { ActionUtil } from "@curefit/base-utils"
import { TimeUtil } from "@curefit/util-common"
import { UserContext } from "@curefit/userinfo-common"
import { IssueDetailView } from "../crm/IssueBusiness"

class ManagedPlanProductPageViewV1 extends ProductDetailPage {
    public pageContext: any

    constructor(userContext: UserContext,
        isNotLoggedIn: boolean,
        product: DiagnosticProduct,
        packageProduct: ManagedPlanSellableProduct,
        subscriptionStartDate: number,
        isMultiCenterEnabled: boolean,
        patientsList?: Patient[],
        subscriptionCode?: string,
        offers?: PackOffersResponse,
        issues?: IssueDetailView[],
        preferredCenter?: PreferredCenterResponse,
        patientId?: number
    ) {
        super()
        let whatYouGetWidget, journeyWidget, faqWidget
        const appVersion = _.get(userContext, "sessionInfo.appVersion")
        const calloutText = "This personalized plan is available only for yourself."
        const descriptions = [{ subTitle: packageProduct.productDescription }]
        const subscriptionProducts: MPChildProduct[] = !_.isEmpty(packageProduct.childProducts) ? packageProduct.childProducts.filter(product => product.baseSellableProduct.subCategoryCode === "MP_SUBS") : []
        const isNewCenterSelectionSupported = appVersion && appVersion >= CARE_NEW_CENTER_SELECTION_SUPPORTED
        const centerSelectionAction: SelectCenterAction = isNewCenterSelectionSupported ? CareUtil.getCenterSelectionAction(packageProduct, patientId) : undefined
        this.widgets.push(this.summaryWidget(packageProduct, offers))
        const widget = new DescriptionWidget(descriptions)
        widget.iconType = undefined
        widget.hasDividerBelow = false
        this.widgets.push(widget)
        if (isNewCenterSelectionSupported) {
            const widget = CareUtil.buildUserSelectionWidget(isNotLoggedIn,
                true,
                patientsList,
                {
                    actionType: "NAVIGATION",
                    meta: {
                        replacePage: true
                    },
                    url: ActionUtil.carefitbundle(product.productId, product.subCategoryCode)
                },
                _.isEmpty(patientsList) ? "Add Customer" : "Select Customer",
                patientId,
                undefined,
                "SMALL",
                calloutText,
                {
                    formUserType: "MANAGED_PLAN_USER"
                }
            )
            this.widgets.push(widget)
        }

        if (isMultiCenterEnabled) {
            if (isNewCenterSelectionSupported) {
                const widget = CareUtil.getCenterSelectionWidget(preferredCenter, centerSelectionAction, patientId, false)
                widget.dividerType = "SMALL"
                // widget.hasDividerBelow = false
                this.widgets.push(widget)
            } else {
                this.widgets.push(this.getAvailableCentersWidget(product))
            }
        }

        if (!_.isEmpty(packageProduct.infoSection.children)) {
            packageProduct.infoSection.children.map(item => {
                switch (item.type) {
                    case "WHAT_YOU_GET": whatYouGetWidget = this.getWhatYouGetWidget(userContext, item, "WHAT_YOU_GET_WIDGET"); break
                    case "JOURNEY": journeyWidget = this.getJourneyWidget(userContext, item); break
                    case "FAQ": faqWidget = CareUtil.getFAQWidget(item); break
                }
            })
        }
        if (whatYouGetWidget) {
            this.widgets.push(whatYouGetWidget)
        }
        if (!_.isEmpty(subscriptionProducts)) {
            subscriptionCode = (subscriptionCode === undefined ? subscriptionProducts[0].baseSellableProduct.productCode : subscriptionCode)
            const expandedSectionData: { title: string, price: ProductPrice }[] = []
            let totalListingPrice = 0
            let totalMRPPrice = 0
            let currency = "INR"
            let duration = 0
            const orderProducts: OrderProduct[] = []
            const appliedOffers: OfferV2[] = []
            const sections: ProductPricingSection[] = []
            // To do add a check for user has already availed trail pack or not
            const recurringSection: PricingWidgetRecurringValue[] = []
            subscriptionProducts.map(subscriptionMainProduct => {
                const subscriptionProduct = subscriptionMainProduct.baseSellableProduct
                const offerDetail = OfferUtil.getPackOfferAndPriceForCare(subscriptionProduct, offers)
                currency = offerDetail.price.currency
                const subscriptionProductListingPrice = offerDetail.price.listingPrice
                duration = subscriptionProduct.duration
                const perMonthPrice = Math.ceil(subscriptionProductListingPrice / (duration / 30))
                const isTrialPack = _.get(subscriptionProduct, "infoSection.trial", false)
                if (!isTrialPack) {
                    recurringSection.push({
                        title: subscriptionProduct.infoSection.shortname,
                        price: {
                            listingPrice: subscriptionProductListingPrice,
                            mrp: subscriptionProduct.mrp,
                            currency: offerDetail.price.currency
                        },
                        priceMeta: perMonthPrice === subscriptionProductListingPrice ? `Per Month` : `${RUPEE_SYMBOL} ${perMonthPrice}/mo`,
                        action: {
                            actionType: "NAVIGATION",
                            url: ActionUtil.carefitbundle(product.productId, product.subCategoryCode),
                            meta: {
                                subscriptionCode: subscriptionProduct.productCode
                            }
                        },
                        selected: subscriptionCode === subscriptionProduct.productCode
                    })
                } else if (isTrialPack) {
                    const subscriptionProduct = subscriptionMainProduct.baseSellableProduct
                    const offerDetail = OfferUtil.getPackOfferAndPriceForCare(subscriptionProduct, offers)
                    duration = Math.floor(subscriptionProduct.duration / 30)
                    totalListingPrice = Math.ceil(offerDetail.price.listingPrice / duration)
                    totalMRPPrice = Math.ceil(offerDetail.price.mrp / duration)
                    sections.push({
                        title: "ONE MONTH TRIAL",
                        type: "INTRODUCTIONARY",
                        action: {
                            actionType: "NAVIGATION",
                            url: ActionUtil.carefitbundle(product.productId, product.subCategoryCode),
                            meta: {
                                subscriptionCode: subscriptionProduct.productCode
                            }
                        },
                        selected: subscriptionProduct.productCode === subscriptionCode ? true : false,
                        highlightText: "INTRODUCTORY PRICE",
                        value: {
                            title: _.get(subscriptionProduct, "infoSection.shortname", "1st Month"),
                            price: {
                                listingPrice: totalListingPrice,
                                mrp: totalMRPPrice,
                                currency: offerDetail.price.currency
                            },
                            data: expandedSectionData
                        }
                    })
                }
                if (subscriptionCode && subscriptionCode === subscriptionProduct.productCode) {
                    totalListingPrice = subscriptionProductListingPrice
                    totalMRPPrice = subscriptionProduct.mrp
                    appliedOffers.push(...offerDetail.offers)
                    const startDate = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, subscriptionStartDate)
                    const endDate = TimeUtil.addDays(userContext.userProfile.timezone, startDate, duration)
                    orderProducts.push({
                        productId: subscriptionProduct.productCode,
                        productType: "BUNDLE",
                        quantity: 1,
                        option: {
                            offerV2Ids: !_.isEmpty(offerDetail.offers) ? offerDetail.offers.map(offer => offer.offerId) : [],
                            parentProductCode: product.productId,
                            categoryCode: subscriptionProduct.categoryCode,
                            subCategoryCode: subscriptionProduct.subCategoryCode,
                            groupCode: subscriptionMainProduct.groupCode,
                            childProductType: subscriptionMainProduct.childProductType,
                            startDate,
                            endDate,
                            patientId: patientId,
                            centerId: isMultiCenterEnabled && preferredCenter && preferredCenter.centerResponse ? String(preferredCenter.centerResponse.id) : undefined,
                        }
                    })
                }
            })
            const finalPrice = {
                listingPrice: totalListingPrice,
                mrp: totalMRPPrice,
                currency
            }
            if (!_.isEmpty(recurringSection)) {
                sections.push({
                    title: "CHOOSE A LONGER DURATION PLAN",
                    type: "RECURRING",
                    value: recurringSection
                })
            }
            const priceWidget: ProductPricingWidget = this.getProductPricingWidget(sections)
            this.widgets.push(priceWidget)
            if (!_.isEmpty(appliedOffers)) {
                this.widgets.push(getOffersWidget("Offers Applied", appliedOffers))
            }
            this.actions = isNewCenterSelectionSupported ? this.getPreBookingActionsV2(product, isNotLoggedIn, patientsList, finalPrice, orderProducts, calloutText, isMultiCenterEnabled, centerSelectionAction, patientId, preferredCenter) : this.getPreBookingActions(isNotLoggedIn, patientsList, finalPrice, orderProducts, calloutText)
        }
        if (journeyWidget) {
            this.widgets.push(journeyWidget)
        }
        if (faqWidget) {
            this.widgets.push(faqWidget)
        }
        // this.navigationAction = {
        //     title: "NEED HELP ?",
        //     action: {
        //         actionType: "REPORT_ISSUE",
        //         meta: {
        //             title: "Help",
        //             issues: issues
        //         }
        //     },
        //     textStyle: {},
        //     containerStyle: {}
        // }
    }

    private getProductPricingWidget(sections: any): ProductPricingWidget {
        return {
            widgetType: "PRODUCT_PRICING_WIDGET",
            header: {
                title: "Pricing"
            },
            footerText: "Trial pack is for 1st time user only. After trial you need to choose 3/6/12 months plan to continue.",
            sections: sections
        }
    }

    private summaryWidget(product: ManagedPlanSellableProduct, offers?: PackOffersResponse): WidgetView {
        let subText
        const minSubscriptionProduct = _.minBy(product.childProducts, subsProduct => {
            const price = OfferUtil.getPackOfferAndPriceForCare(subsProduct.baseSellableProduct, offers).price
            const durationInMonth = Math.floor(subsProduct.baseSellableProduct.duration / 30)
            return Math.ceil(price.listingPrice / durationInMonth)
        })
        if (!_.isEmpty(minSubscriptionProduct)) {
            const durationInMonth = Math.floor(minSubscriptionProduct.baseSellableProduct.duration / 30)
            const minsubsPrice = OfferUtil.getPackOfferAndPriceForCare(minSubscriptionProduct.baseSellableProduct, offers).price
            const price = Math.ceil(minsubsPrice.listingPrice / durationInMonth)
            subText = price > 0 ? `Starting from ${RUPEE_SYMBOL} ${price}/mo` : undefined
        }
        const checkupSummaryWidget: WidgetView & {
            productId: string;
            title: string;
            // subTitle?: String;
            subText?: string;
            image: string;
            hasDividerBelow: boolean
        } = {
            widgetType: "HEALTHCHECKUP_SUMMARY_WIDGET",
            title: product.productName,
            // subTitle: product.productDescription || undefined,
            subText: subText,
            productId: product.productCode,
            image: product.heroImageUrl,
            hasDividerBelow: false
        }
        return checkupSummaryWidget
    }

    private getAvailableCentersWidget(product: DiagnosticProduct): CenterSelectionWidget {
        const action: Action = {
            actionType: "NAVIGATION",
            url: `curefit://selectcarecenter?productId=${product.productId}`
        }
        const centerSelectionWidget: CenterSelectionWidget = {
            title: "View Available Centres",
            canChangeCenter: true,
            widgetType: "CENTER_PICKER_WIDGET",
            dividerType: "LARGE",
            showCustomSeparator: true,
            action: action
        }
        return centerSelectionWidget
    }

    private getWhatYouGetWidget(userContext: UserContext, item: ManagedPlanPackInfo, widgetType: WidgetType): WidgetView {
        const widget: WidgetView & {
            title?: string,
            image?: string,
            data?: ManagedPlanPackInfoDetail[]
        } = {
            widgetType: widgetType,
            title: item.title ? item.title : undefined,
            data: !_.isEmpty(item.children) ? this.getWhatYouGetWidgetData(userContext, item) : []
        }
        return widget
    }

    private getWhatYouGetWidgetData(userContext: UserContext, item: any): any {
        return item.children.map((content: any) => {
            const { action, ...rest } = content
            const actionData = {
                ...rest,
                imageStyle: content.type === "MP_CONSULTATION" ? {
                    width: 64,
                    height: 50
                } : {
                        width: 44,
                        height: 40
                    },
                action: action ? action : undefined,
                resizeMode: content.type === "MP_CONSULTATION" ? "cover" : "contain"
            }
            const children = _.get(action, "children", [])
            if (!_.isEmpty(children) && action && action.actionType && action.actionType === "SHOW_CAROUSEL_LIST") {
                actionData.action = {
                    ...action,
                    actionType: CareUtil.getNewCarouselListActionType(userContext, "SHOW_CAROUSEL_LIST"),
                    meta: {
                        type: "IMAGE_LIST",
                        bundleProducts: children.map((item: any) => {
                            return {
                                ...item,
                                ar: 0.598
                            }
                        })
                    }
                }
            }
            return actionData
        }).filter((data: any) => data)
    }

    private getJourneyWidget(userContext: UserContext, item: ManagedPlanPackInfo): WidgetView {
        const infocards: InfoCard[] = []
        if (!_.isEmpty(item.children)) {
            item.children.map((product, index) => {
                infocards.push({
                    title: product.text ? product.text : undefined,
                    subTitle: undefined,
                    image: product.imageUrl,
                    moreIndex: index,
                    imageStyle: product.text ? {
                        width: 218,
                        height: 290
                    } : {
                            width: 221,
                            height: 370
                        }
                })
            })
            const action: Action = {
                actionType: CareUtil.getNewCarouselListActionType(userContext, "SHOW_CAROUSEL_LIST"),
                meta: {
                    type: "JOURNEY_LIST",
                    bundleProducts: item.children.map((product) => {
                        return {
                            ...product,
                            ar: 0.598
                        }
                    })
                }
            }
            const widgetTitle: PageWidgetTitle = {
                title: item.title ? item.title : undefined
            }
            const gridSize = 1
            return new JourneyWidget(gridSize, infocards, widgetTitle, action)
        }
        return undefined
    }

    private getPreBookingActions(isNotLoggedIn: boolean, patientsList: Patient[], finalPrice: ProductPrice, orderProducts: OrderProduct[], calloutText: string): Action[] {

        if (isNotLoggedIn === true) {
            return [
                {
                    actionType: "SHOW_ALERT_MODAL",
                    title: "Get pack",
                    meta: {
                        title: "Login Required!",
                        subTitle: "Please login to continue",
                        actions: [{ actionType: "LOGOUT", title: "Login" }]
                    }
                }
            ]
        } else {
            const action: Action = {
                actionType: "SUBSCRIBE_MP",
                meta: {
                    orderProducts: orderProducts
                }
            }
            // const subText = duration === 1  ? "For 1st time users only" : `${RUPEE_SYMBOL} ${recurringPrice} charged every ${duration} months`
            const meta = {
                // subText: subText,
                title: "Total Pay",
                price: finalPrice,
                showCheckoutAction: true,
                formUserType: "MANAGED_PLAN_USER"
            }
            const pageAction = CareUtil.getSelfPatientSelectionModalAction(patientsList, action, undefined, calloutText, meta)
            return [pageAction]
        }
    }

    private getPreBookingActionsV2(product: DiagnosticProduct, isNotLoggedIn: boolean, patientsList: Patient[], finalPrice: ProductPrice, orderProducts: OrderProduct[], calloutText: string, isMultiCenterEnabled: boolean, centerSelectionAction: SelectCenterAction, patientId?: number, preferredCenter?: PreferredCenterResponse): Action[] {

        if (isNotLoggedIn === true) {
            return [
                {
                    actionType: "SHOW_ALERT_MODAL",
                    title: "Get pack",
                    meta: {
                        title: "Login Required!",
                        subTitle: "Please login to continue",
                        actions: [{ actionType: "LOGOUT", title: "Login" }]
                    }
                }
            ]
        } else {
            if (!patientId) {
                return [
                    CareUtil.getSelfPatientSelectionModalAction(patientsList,
                        {
                            actionType: "NAVIGATION",
                            meta: {
                                replacePage: true
                            },
                            url: ActionUtil.carefitbundle(product.productId, product.subCategoryCode)
                        },
                        "Get pack", // _.isEmpty(patientsList) ? "Add Patient" : "Select Patient"
                        calloutText,
                        {
                            formUserType: "MANAGED_PLAN_USER"
                        }
                    )
                ]
            }
            if (isMultiCenterEnabled && _.isEmpty(preferredCenter)) {
                return [
                    { ...centerSelectionAction, title: "Get pack" }
                ]
            }
            const action: Action = {
                actionType: "SUBSCRIBE_MP",
                meta: {
                    title: "Total Pay",
                    patientId: patientId,
                    orderProducts: orderProducts,
                    price: finalPrice,
                    showCheckoutAction: true,
                    formUserType: "MANAGED_PLAN_USER"
                }
            }
            return [action]
        }
    }
}

export default ManagedPlanProductPageViewV1
