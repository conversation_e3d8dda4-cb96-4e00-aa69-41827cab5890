import { CFServiceInterfaces } from "./../page/vm/ServiceInterfaces"
import { BaseWidget, CalloutImageWidget, Action as VMAction } from "@curefit/vm-models"
import { CareLHRPackWidget, LHRPack, LHRBodyPart } from "./../common/views/WidgetView"
import { Orientation } from "@curefit/vm-common"
import * as _ from "lodash"
import {
    ContentAsset,
    DiagnosticProduct,
    DiagnosticProductResponse,
    ManagedPlanPackInfo,
    Patient
} from "@curefit/care-common"
import { FoodProduct as Product } from "@curefit/eat-common"
import { ProductPrice } from "@curefit/product-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { OfferV2, PackOffersResponse } from "@curefit/offer-common"
import {
    Action,
    DescriptionWidget,
    getOffersWidget,
    GradientCard,
    Header,
    InfoCard,
    PricingWidgetRecurringValue,
    ProductDetailPage,
    ProductListWidget,
    ProductPricingSection,
    WidgetView,
    TextWidget
} from "../common/views/WidgetView"
import CareUtil, { CARE_HCU_FORWARD_FLOW_SUPPORTED } from "../util/CareUtil"
import { ActionUtil, OfferUtil, RUPEE_SYMBOL } from "@curefit/base-utils"
import { UserContext } from "@curefit/userinfo-common"
import AppUtil from "../util/AppUtil"

class SkinCareBundlePrePurchaseProductView extends ProductDetailPage {
    public pageContext: any

    constructor(
        userContext: UserContext,
        isNotLoggedIn: boolean,
        product: DiagnosticProduct,
        allClubbedProducts: { diagnosticProduct: DiagnosticProduct, product: DiagnosticProductResponse }[],
        packageProduct: DiagnosticProductResponse,
        patientsList: Patient[],
        offers: PackOffersResponse,
        serviceInterface?: CFServiceInterfaces,
        userSegments?: string[]
    ) {
        super()
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent")
        const offerDetails = OfferUtil.getPackOfferAndPrice(product, offers)
        const offerIds = _.map(offerDetails.offers, offer => {return offer.offerId})
        const appVersion = _.get(userContext, "sessionInfo.appVersion")
        const isCenterSelectionSupported = appVersion && appVersion >= CARE_HCU_FORWARD_FLOW_SUPPORTED
        const { howItWorksItem, packContentsDetailed, packOffering, packQnA, bundleInstructionPreBooking } = CareUtil.getPackContent(packageProduct)
        this.actions = isCenterSelectionSupported || userAgent === "DESKTOP" || userAgent === "MBROWSER"
            ? this.getPreBookingActionsV2(userContext, isNotLoggedIn, patientsList, packageProduct, bundleInstructionPreBooking, offerIds, offerDetails, serviceInterface)
            : this.getPreBookingActions(userContext, product, isNotLoggedIn, patientsList, packageProduct, bundleInstructionPreBooking,  offerIds, offerDetails, serviceInterface)

        const widgetArray: WidgetView[] | BaseWidget[] = [
            this.getDescriptionWidget(packageProduct.productDescription, !_.isEmpty(packQnA) ? packQnA : undefined, userAgent)
        ]
        if (CareUtil.isLHRProduct(packageProduct.clubCode)) {
            widgetArray.push(this.getLHRPackWidget(userContext, product.productId, allClubbedProducts, offers, userAgent))
        } else {
            widgetArray.push(this.getProductPricingWidget(product.productId, allClubbedProducts, offers, userAgent, userSegments, serviceInterface))
        }

        this.widgets = [
            this.summaryWidget(packageProduct, offerDetails.price, userAgent),
            ...(userAgent === "DESKTOP" ? widgetArray.reverse() : widgetArray)
        ]

        // removing it for now @Munaf
        // const image = AppUtil.isNewKayaConsultImageSupported(userContext) ? "image/carefit/kaya_consult_online_v3.png" : "image/carefit/callout_online_procedure_v2.png"
        // this.widgets.push(this.getCalloutImageWidget(image, this.getOnlineConsultationAction(isNotLoggedIn, patientsList, "DERMATOLOGIST_KAYA_ONLINE")))
        this.widgets.push(CareUtil.getKayaFulfillmentWidget())

        if (!_.isEmpty(offerIds)) {
            this.widgets.push(CareUtil.getCareOfferWidget("Offers Applied", offerDetails.offers, userContext))
        }
        if (!_.isEmpty(packQnA)) {
            this.widgets.push(CareUtil.getPackQnAWidget(userAgent, packQnA))
        }
        if (!_.isEmpty(packContentsDetailed)) {
            this.widgets.push(this.getWhatsInPackWidget(userAgent, packContentsDetailed))
        }
        if (!_.isEmpty(packOffering)) {
            this.widgets.push(this.getWhatsInPackWidget(userAgent, packOffering))
        }
        if (!_.isEmpty(howItWorksItem)) {
            this.widgets.push(CareUtil.getHowItWorksWidget(howItWorksItem, userAgent))
        }
        this.widgets = this.widgets.filter(item => !!item).map(widget => ({...widget, hasDividerBelow: false}))
    }

    private getProductPricingWidget(selectedProductId: string, allClubbedProducts: { diagnosticProduct: DiagnosticProduct, product: DiagnosticProductResponse }[], offers: PackOffersResponse, userAgent: UserAgent, userSegments?: string[], serviceInterface?: CFServiceInterfaces): any {
        if (!_.isEmpty(allClubbedProducts)) {
            const sections: ProductPricingSection[] = []
            const recurringSection: PricingWidgetRecurringValue [] = []
            allClubbedProducts.map((clubProduct: { diagnosticProduct: DiagnosticProduct, product: DiagnosticProductResponse }) => {
                const offerDetail = OfferUtil.getPackOfferAndPrice(clubProduct.diagnosticProduct, offers)
                const numberOfSession = Number(_.get(clubProduct, "product.infoSection.numberOfSessions", 0))
                const priceMeta = numberOfSession > 1 ? `${RUPEE_SYMBOL} ${Math.ceil(offerDetail.price.listingPrice / numberOfSession)} / session` : undefined
                const isDisabled = !_.isEmpty(userSegments) && _.get(clubProduct, "product.infoSection.segmentName") && userSegments.includes(_.get(clubProduct, "product.infoSection.segmentName"))
                // if (!_.isEmpty(userSegments)) {
                //     serviceInterface.logger.info(`UserSegments::SKIN::${clubProduct.diagnosticProduct.productId}:: is EMPTY`)
                // } else {
                //     serviceInterface.logger.info(`UserSegments::SKIN::${clubProduct.diagnosticProduct.productId}::${JSON.stringify(userSegments)}`)
                //     serviceInterface.logger.info(`UserSegments::SKIN::infoSection::${clubProduct.diagnosticProduct.productId}::${_.get(clubProduct, "product.infoSection.segmentName", "EMPTY")}`)
                // }
                recurringSection.push({
                    isDisabled,
                    title: _.get(clubProduct, "product.infoSection.shortTitle"),
                    subTitle: _.get(clubProduct, "product.infoSection.shortSubTitle"),
                    price: {
                        listingPrice: offerDetail.price.listingPrice,
                        mrp: offerDetail.price.mrp,
                        currency: offerDetail.price.currency
                    },
                    priceMeta: priceMeta,
                    action: {
                        actionType: "NAVIGATION",
                        url: ActionUtil.carefitbundle(
                            clubProduct.diagnosticProduct.productId,
                            clubProduct.diagnosticProduct.subCategoryCode,
                            undefined,
                            undefined,
                            undefined,
                            undefined,
                            undefined,
                            undefined,
                            userAgent
                        ),
                        meta: {
                            selectedProductId: clubProduct.diagnosticProduct.productId
                        }
                    },
                    selected: selectedProductId === clubProduct.diagnosticProduct.productId
                })
            })
            if (!_.isEmpty(recurringSection)) {
                sections.push({
                    // title: "CHOOSE A LONGER DURATION PLAN",
                    type: "RECURRING",
                    value: recurringSection
                })
            }
            return {
                widgetType: "PRODUCT_PRICING_WIDGET",
                dividerType: "SMALL",
                hasDividerBelow: true,
                header: {
                    title: "Choose"
                },
                orientation: userAgent === "DESKTOP" ? "RIGHT" : undefined,
                footerText: undefined, // "Trial pack is for 1st time user only. After trial you need to choose 3/6/12 months plan to continue.",
                sections: sections
            }
        }
        return null
    }

    private getCalloutImageWidget(imageUrl: string, action: Action): WidgetView {
        return {
            widgetType: "CALLOUT_IMAGE_WIDGET",
            style: {
                width: 335,
                height: 131,
            },
            image: imageUrl,
            showDivider: false,
            dividerType: "NONE",
            action: action as VMAction,
        }
    }

    private getLHRPackWidget(userContext: UserContext, selectedProductId: string, allClubbedProducts: { diagnosticProduct: DiagnosticProduct, product: DiagnosticProductResponse }[], offers: PackOffersResponse, userAgent: UserAgent): CareLHRPackWidget {
        if (!_.isEmpty(allClubbedProducts)) {
            const packs: LHRPack[] = []
            const bodyParts: LHRBodyPart[] = []
            let packSubTitle = ""
            allClubbedProducts.map((clubProduct: { diagnosticProduct: DiagnosticProduct, product: DiagnosticProductResponse }) => {
                const isSelectedProduct = selectedProductId === clubProduct.diagnosticProduct.productId
                const offerDetail = OfferUtil.getPackOfferAndPrice(clubProduct.diagnosticProduct, offers)
                const numberOfSessions = Number(_.get(clubProduct, "product.infoSection.numberOfSessions", 0))
                const numberOfBodyParts = _.get(clubProduct, "product.infoSection.numberOfBodyParts", 0)
                const sessionCountPerBodyPart = numberOfSessions / numberOfBodyParts
                const priceMeta = numberOfSessions > 1 ? `${RUPEE_SYMBOL} ${Math.ceil(offerDetail.price.listingPrice / numberOfSessions)} / session` : undefined
                if (isSelectedProduct)
                    packSubTitle = `${sessionCountPerBodyPart} sessions each for ${numberOfBodyParts} body parts`
                packs.push({
                    title: _.get(clubProduct, "product.infoSection.shortTitle"),
                    subTitle: _.get(clubProduct, "product.infoSection.shortSubTitle"),
                    numberOfSessions,
                    numberOfBodyParts,
                    price: {
                        listingPrice: offerDetail.price.listingPrice,
                        mrp: AppUtil.isStrikeThroughPriceSupportedForLHR(userContext) ? offerDetail.price.mrp : offerDetail.price.listingPrice,
                        currency: offerDetail.price.currency
                    },
                    priceMeta: priceMeta,
                    action: {
                        actionType: "NAVIGATION",
                        url: ActionUtil.carefitbundle(
                            clubProduct.diagnosticProduct.productId,
                            clubProduct.diagnosticProduct.subCategoryCode,
                            undefined,
                            undefined,
                            undefined,
                            undefined,
                            undefined,
                            clubProduct.diagnosticProduct.clubCode,
                            userAgent
                        ),
                        meta: {
                            selectedProductId: clubProduct.diagnosticProduct.productId,
                        }
                    },
                    isSelected: isSelectedProduct
                })

                if (isSelectedProduct && !_.isEmpty(_.get(clubProduct, "product.productSpec.allowedBodyParts"))) {
                    _.map(clubProduct.product.productSpec.allowedBodyParts, part => {
                        bodyParts.push({
                            imageUrl: part.assetUrl,
                            code: part.code,
                            title: part.displayName,
                        })
                    })
                }
            })
            return {
                widgetType: "CARE_LHR_PACK_WIDGET",
                dividerType: "SMALL",
                hasDividerBelow: true,
                header: {
                    title: "Available Packs",
                    subTitle: packSubTitle,
                },
                packs,
                bodyPartsData: {
                    title: "Select body parts for laser hair reduction",
                    bodyParts,
                  }
            }
        }
        return undefined
    }

    private summaryWidget(product: DiagnosticProductResponse, price: ProductPrice, userAgent: UserAgent): WidgetView {
        const checkupSummaryWidget: WidgetView & {
            productId: string;
            title: string;
            image: string;
            // price?: ProductPrice;
            assets?: ContentAsset[]
            hasDividerBelow: boolean
            breadcrumb?: { text: string, link?: string }[]
            orientation?: Orientation,
            actions: Action[]
        } = {
            widgetType: "HEALTHCHECKUP_SUMMARY_WIDGET",
            title: _.get(product, "infoSection.packTitle", product.productName),
            productId: product.productCode,
            // price: userAgent !== "APP" ? price : undefined,
            image: product.heroImageUrl,
            assets: _.get(product, "contentAssets.carouselAssets", []),
            hasDividerBelow: false,
            orientation: userAgent === "DESKTOP" ? "RIGHT" : undefined,
            breadcrumb: userAgent === "DESKTOP" ? [{ text: "Home", link: "/" }, { text: "Care", link: "/care" }, { text: "Skin and Hair", link: "/care/skinhair" }, { text: _.get(product, "infoSection.packTitle", product.productName) }] : [],
            actions: userAgent === "DESKTOP" || userAgent === "MBROWSER" ? this.actions : []
        }
        return checkupSummaryWidget
    }

    private getWhatsInPackWidget(userAgent: UserAgent, packContentsDetailed: ManagedPlanPackInfo): ProductListWidget {
        const header: Header = {
            title: packContentsDetailed.title,
            color: "#000000"
        }

        const cards: GradientCard[] = []
        packContentsDetailed.children.forEach(item => {
            cards.push({
                title: item.title,
                subTitle: item.shortDescription || item.desc,
                shadowColor: item.shadowColor || "#76e997",
                gradientColors: item.gradientColors || ["#76e997", "#2cc2d3"],
                icon: item.icon || item.type
            })
        })
        const widget = new ProductListWidget("GARDIENT_CARD", header, cards)
        if (userAgent === "DESKTOP") {
            widget.orientation = "RIGHT"
        }
        widget.hasDividerBelow = true
        widget.dividerType = "SMALL"
        return widget
    }

    private getPreBookingActions(userContext: UserContext, product: Product, isNotLoggedIn: boolean, patientsList: Patient[], packageProduct: DiagnosticProductResponse, bundleInstructionPreBooking: ManagedPlanPackInfo, offerIds: string[], offerDetails: {
        price: ProductPrice,
        offers: OfferV2[]
    }, serviceInterface?: CFServiceInterfaces): Action[] {
        const isTrailPack = _.get(packageProduct, "infoSection.isTrial", false)
        const title = isTrailPack ? "Book free trail"  : `Book ${String(_.get(packageProduct, "infoSection.shortTitle", "")).toLowerCase()}`
        if (isNotLoggedIn === true) {
            return [
                CareUtil.loginAlertModal(title)
            ]
        } else {
            let actionString: string = `curefit://carecartcheckout?productId=${product.productId}&productCode=${packageProduct.productCode}&subCategoryCode=${packageProduct.subCategoryCode}`
            if (!_.isEmpty(offerIds)) {
                actionString += `&offerIds=${offerIds.join(",")}`
            }
            const pageAction = CareUtil.getPatientSelectionModalAction(patientsList,
                {
                    actionType: "NAVIGATION",
                    meta: {
                        formUserType: "CARE_USER"
                    },
                    url: actionString,
                },
                title,
                "CARE_USER"
            )
            if (isTrailPack) {
                return [pageAction]
            }
            pageAction.meta = this.getPageActionMeta(title, pageAction, packageProduct, offerDetails, serviceInterface)
            const alertAction = this.getTnCAlertAction(title, pageAction, packageProduct, offerDetails, bundleInstructionPreBooking)
            return [alertAction]
        }

    }

    private getOnlineConsultationAction(isNotLoggedIn: boolean, patientsList: Patient[], productId: string): Action {
        const title = "Book Session"
        if (isNotLoggedIn === true) {
            return CareUtil.loginAlertModal(title)
        }

        const actionStringOnline: string = ActionUtil.selectCareDateV1(productId)
        const consultAction = CareUtil.getPatientSelectionModalAction(patientsList,
            {
                actionType: "NAVIGATION",
                url: actionStringOnline,
            })
        return consultAction
    }

    private getPreBookingActionsV2(userContext: UserContext, isNotLoggedIn: boolean, patientsList: Patient[], packageProduct: DiagnosticProductResponse, bundleInstructionPreBooking: ManagedPlanPackInfo, offerIds: string[], offerDetails: {
        price: ProductPrice,
        offers: OfferV2[]
    }, serviceInterface?: CFServiceInterfaces): Action[] {
        const isTrailPack = _.get(packageProduct, "infoSection.isTrial", false)
        const title = isTrailPack ? "Book free trail"  : `Book ${String(_.get(packageProduct, "infoSection.shortTitle", "")).toLowerCase()}`
        if (isNotLoggedIn === true) {
            return [
                CareUtil.loginAlertModal(title)
            ]
        }
        const pageAction = CareUtil.getPatientSelectionModalAction(patientsList,
            {...CareUtil.getCenterSelectionAction(packageProduct, undefined, true), title},
            title ? title : _.isEmpty(patientsList) ? "Add Patient" : "Select Patient"
        )

        if (isTrailPack) {
            return [pageAction]
        }

        pageAction.meta = this.getPageActionMeta(title, pageAction, packageProduct, offerDetails, serviceInterface)
        const alertAction = this.getTnCAlertAction(title, pageAction, packageProduct, offerDetails, bundleInstructionPreBooking)
        return [alertAction]
    }

    private getTnCAlertAction(
        actionTitle: string,
        positiveAction: Action,
        packageProduct: DiagnosticProductResponse,
        offerDetails: {  price: ProductPrice, offers: OfferV2[] },
        bundleInstructionPreBooking: ManagedPlanPackInfo
    ): Action {
        return {
            actionType: "SHOW_CARE_INSTRUCTION_MODAL",
            title: actionTitle,
            meta: {
                instructions: [],
                header: {
                    title: bundleInstructionPreBooking?.title || "Please Note", // _.get(packageProduct, "infoSection.shortSubTitle", actionTitle)
                },
                instructionMap: [
                    {
                        "priority": 1,
                        "instructionResponseList": bundleInstructionPreBooking?.children || [
                            {
                                "text": "For cases like: Pregnancy, Metal Implants, Vitiligo patch and Keloids, Laser procedure is not recommended ",
                                "showBullet": true
                            },
                            {
                                "text": "The procedure is performed post a free skin examination by a doctor at the clinic ",
                                "showBullet": true
                            },
                            {
                                "text": "This is done to ensure that the procedure is best suitable for you",
                                "showBullet": true
                            }
                        ]
                    }
                ],
                action: {...positiveAction, title: "PROCEED"},
                showBookLater: true,
                title: actionTitle,
                subText: _.get(packageProduct, "infoSection.shortSubTitle"),
                // subTitle: "The procedure will be performed only post a free examination by a doctor at the clinic. If it is unsuitable for your skin then alternatives maybe suggested.",
                // actions: [{...positiveAction, title: "PROCEED"}],
                price: offerDetails.price,
                showCheckoutAction: true,
                formUserType: "CARE_USER"
            }
        }
    }

    private getPageActionMeta (title: string, pageAction: Action, packageProduct: DiagnosticProductResponse, offerDetails: {  price: ProductPrice,
        offers: OfferV2[]
    }, serviceInterface?: CFServiceInterfaces) {
        serviceInterface.logger.info(`CTA DATA ========> packID: ${packageProduct.productCode} ::: Title: ${packageProduct.infoSection.shortTitle} ::: subTitle: ${packageProduct.infoSection.shortSubTitle}`)
       return  {
            ...pageAction.meta,
            subText: _.get(packageProduct, "infoSection.shortSubTitle"),
            title: title,
            price: offerDetails.price,
            showCheckoutAction: true,
            formUserType: "CARE_USER"
        }
    }

    private getDescriptionWidget(productDescription: string, packInfo?: ManagedPlanPackInfo, userAgent?: UserAgent): DescriptionWidget | TextWidget {
        if (userAgent === "APP") {
            const descriptionWidget = new DescriptionWidget([{
                subTitle: productDescription,
            }])
            descriptionWidget.iconType = undefined
            descriptionWidget.dividerType = "SMALL"
            descriptionWidget.hasDividerBelow = true
            if (packInfo && packInfo.children && !_.isEmpty(packInfo.children)) {
                descriptionWidget.seeMore = "Know More"
                descriptionWidget.seeMoreAction = {
                    actionType: "NAVIGATION",
                    url: `curefit://infopage?contentId=${packInfo.type}&pageType=LIST`,
                    meta: {
                        contentId: packInfo.type,
                        data: CareUtil.getQnAInfoPage(packInfo)
                    }
                }
            }
            return descriptionWidget
        }
        const textWidget = new TextWidget("About", productDescription)
        textWidget.orientation = userAgent === "DESKTOP" ? "RIGHT" : undefined
        return textWidget
    }

}

export default SkinCareBundlePrePurchaseProductView
