import * as _ from "lodash"
import { Action, ProductDetailPage } from "../common/views/WidgetView"
import { CareUtil, HomeCenterSelector } from "../util/CareUtil"
import {
    AtHomeDiagnosticOrder,
    BookingDetail,
    DiagnosticReportInfo,
    InCentreDiagnosticOrder
} from "@curefit/albus-client"
import { CareMeEmptyListingWidget, ListingActionableCardWidget } from "../page/PageWidgets"
import { ActionUtil } from "@curefit/base-utils"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { UserContext } from "@curefit/userinfo-common"
import { TimeUtil, Timezone } from "@curefit/util-common"
import AppUtil from "../util/AppUtil"

class MeHCUPackPageView extends ProductDetailPage {
    constructor(packs: BookingDetail[], isHomeCollectionEnabled: boolean, userContext: UserContext, reportActions: Map<string, any>) {
        super()
        if (_.isEmpty(packs)) {
            this.widgets.push(new CareMeEmptyListingWidget("HCU", "No Health Check-ups Yet!", "The best way to prevent diseases is to detect them early. Opt for a full-body health check-up and get a medical & lifestyle care plan from the doctor."))
            this.actions = [{
                actionType: "NAVIGATION",
                url: AppUtil.isWeb(userContext) ? ActionUtil.careFitClp("clphcu") : CareUtil.getLabTestClpListPage(),
                title: "Book Health Check-up Now!"
            }]
        } else {
            packs.map(pack => {
                if (pack.booking.status !== "CANCELLED") {
                    if (pack.booking.subCategoryCode === "HCU" && !_.isEmpty(pack.bundleSetupInfo)) {
                        this.widgets.push(this.getActionableCardWidget(userContext, pack, isHomeCollectionEnabled))
                    } else if (pack.booking.subCategoryCode === "DIAG_PACK" || pack.booking.subCategoryCode === "HCU_PACK") {
                        this.widgets.push(this.getActionableCardWidgetV2(userContext, pack, reportActions))
                    } else if (pack.booking.subCategoryCode === "DIAGNOSTIC_TEST") {
                        this.widgets.push(this.getDiagnosticsCardWidget(userContext, pack, reportActions))
                    }
                }
            })
            if (_.isEmpty(this.widgets)) {
                this.widgets.push(new CareMeEmptyListingWidget("HCU", "No Health Check-ups Yet!", "The best way to prevent diseases is to detect them early. Opt for a full-body health check-up and get a medical & lifestyle care plan from the doctor."))
                this.actions = [{
                    actionType: "NAVIGATION",
                    url: AppUtil.isWeb(userContext) ? ActionUtil.careFitClp("clphcu") : CareUtil.getLabTestClpListPage(),
                    title: "Book Health Check-up Now!"
                }]
            }
        }
    }

    getActionableCardWidgetV2(userContext: UserContext, pack: BookingDetail, reportActions: Map<string, any>): ListingActionableCardWidget {
        const bundleBooking = pack.childBookingInfos.find(booking => booking.booking.subCategoryCode === "DIAG_PACK_OT" || booking.booking.subCategoryCode === "HCU_PACK_OT" || booking.booking.subCategoryCode === "AI_MG_OT")
        const homeCenterSelector: HomeCenterSelector = {
            sampleCollectionLocationResponse: pack.sampleCollectionLocationResponse,
            diagnosticTestInstruction: pack.diagnosticTestInstruction
        }
        const actions: Action[] = this.getActionsV2(userContext, bundleBooking, homeCenterSelector, reportActions)
        const tz = userContext.userProfile.timezone
        const footers = []
        if (bundleBooking.stepInfosV2[0].stepState !== "NOT_BOOKED") {
            const diagnosticTest = bundleBooking.stepInfosV2[0].diagnosticsTestBookingInfo.diagnosticsTestOrderResponse[0]
            if (!_.isEmpty(diagnosticTest.atHomeDiagnosticOrder) && !_.isEmpty(diagnosticTest.inCentreDiagnosticOrder)) {
                const statusInfo = CareUtil.getDiagnosticTestHomeCenterStatusInfo(diagnosticTest)
                if (statusInfo.sampleCollectionStartDateAtCenter < statusInfo.sampleCollectionStartDateAtHome) {
                    footers.push(this.getInCentreTitle(diagnosticTest.inCentreDiagnosticOrder, tz))
                    footers.push(this.getAthomeTitle(userContext, diagnosticTest.atHomeDiagnosticOrder))
                } else {
                    footers.push(this.getAthomeTitle(userContext, diagnosticTest.atHomeDiagnosticOrder))
                    footers.push(this.getInCentreTitle(diagnosticTest.inCentreDiagnosticOrder, tz))
                }
            } else if (!_.isEmpty(diagnosticTest.atHomeDiagnosticOrder)) {
                footers.push(this.getAthomeTitle(userContext, diagnosticTest.atHomeDiagnosticOrder))
            } else if (!_.isEmpty(diagnosticTest.inCentreDiagnosticOrder)) {
                footers.push(this.getInCentreTitle(diagnosticTest.inCentreDiagnosticOrder, tz))
            }
        }
        return {
            widgetType: "LISTING_ACTIONABLE_CARD_WIDGET",
            title: pack.productTitle,
            subTitle: `For ${pack.bundleOrderResponse.patient.name}`,
            footer: footers,
            actions: actions,
            cardAction: {
                actionType: "NAVIGATION",
                url: ActionUtil.carefitbundle(pack.booking.productCode, pack.booking.subCategoryCode, pack.booking.id.toString())
            }
        }
    }

    getDiagnosticsCardWidget(userContext: UserContext, pack: BookingDetail, reportActions: Map<string, any>): ListingActionableCardWidget {
        const tz = userContext.userProfile.timezone
        const footers = []
        const diagnosticTest = pack.diagnosticsTestOrderResponse[0]
        const actions = CareUtil.getDiagosticTestActions(userContext, diagnosticTest, reportActions)
        if (!_.isEmpty(diagnosticTest.atHomeDiagnosticOrder) && !_.isEmpty(diagnosticTest.inCentreDiagnosticOrder)) {
            const statusInfo = CareUtil.getDiagnosticTestHomeCenterStatusInfo(diagnosticTest)
            if (statusInfo.sampleCollectionStartDateAtCenter < statusInfo.sampleCollectionStartDateAtHome) {
                footers.push(this.getInCentreTitle(diagnosticTest.inCentreDiagnosticOrder, tz))
                footers.push(this.getAthomeTitle(userContext, diagnosticTest.atHomeDiagnosticOrder))
            } else {
                footers.push(this.getAthomeTitle(userContext, diagnosticTest.atHomeDiagnosticOrder))
                footers.push(this.getInCentreTitle(diagnosticTest.inCentreDiagnosticOrder, tz))
            }
        } else if (!_.isEmpty(diagnosticTest.atHomeDiagnosticOrder)) {
            footers.push(this.getAthomeTitle(userContext, diagnosticTest.atHomeDiagnosticOrder))
        } else if (!_.isEmpty(diagnosticTest.inCentreDiagnosticOrder)) {
            footers.push(this.getInCentreTitle(diagnosticTest.inCentreDiagnosticOrder, tz))
        }
        return {
            widgetType: "LISTING_ACTIONABLE_CARD_WIDGET",
            title: pack.productTitle,
            subTitle: `For ${pack.diagnosticsTestOrderResponse[0].patient.name}`,
            footer: footers,
            actions: actions,
            cardAction: {
                actionType: "NAVIGATION",
                url: ActionUtil.diagnostics(pack.booking.productCode, pack.booking.id.toString())
            }
        }
    }

    getActionableCardWidget(userContext: UserContext, pack: BookingDetail, isHomeCollectionEnabled: boolean): ListingActionableCardWidget {
        const actions: Action[] = this.getActions(userContext, pack, isHomeCollectionEnabled)
        const footers = []
        const tz = userContext.userProfile.timezone
        let type
        const testStep = pack.bundleSetupInfo.bundleStepInfos.find(step => step.setupStep === "DIAGNOSTIC_TEST")
        if (testStep.stepState !== "NOT_BOOKED") {
            const diagnosticTest = testStep.testBookingInfo.diagnosticsTestOrderResponse[0]
            if (!_.isEmpty(diagnosticTest.atHomeDiagnosticOrder) && !_.isEmpty(diagnosticTest.inCentreDiagnosticOrder)) {
                type = "HOME+CENTRE"
                const sampleCollectionStartDateAtHome = _.get(diagnosticTest, "atHomeDiagnosticOrder.startTime", 0)
                const sampleCollectionStartDateAtCenter = _.get(diagnosticTest, "inCentreDiagnosticOrder.slot.workingStartTime", 0)
                if (sampleCollectionStartDateAtCenter < sampleCollectionStartDateAtHome) {
                    footers.push(this.getInCentreTitle(diagnosticTest.inCentreDiagnosticOrder, tz))
                    footers.push(this.getAthomeTitle(userContext, diagnosticTest.atHomeDiagnosticOrder))
                } else {
                    footers.push(this.getAthomeTitle(userContext, diagnosticTest.atHomeDiagnosticOrder))
                    footers.push(this.getInCentreTitle(diagnosticTest.inCentreDiagnosticOrder, tz))
                }
            } else if (!_.isEmpty(diagnosticTest.atHomeDiagnosticOrder)) {
                type = "ATHOME"
                footers.push(this.getAthomeTitle(userContext, diagnosticTest.atHomeDiagnosticOrder))
            } else if (!_.isEmpty(diagnosticTest.inCentreDiagnosticOrder)) {
                type = "INCENTRE"
                footers.push(this.getInCentreTitle(diagnosticTest.inCentreDiagnosticOrder, tz))
            }
        }

        return {
            widgetType: "LISTING_ACTIONABLE_CARD_WIDGET",
            title: pack.productTitle,
            subTitle: `For ${pack.bundleOrderResponse.patient.name}`,
            footer: footers,
            actions: actions,
            cardAction: {
                actionType: "NAVIGATION",
                url: ActionUtil.carefitbundle(pack.booking.productCode, pack.booking.subCategoryCode, pack.booking.id.toString())
            }
        }
    }

    private getAthomeTitle(userContext: UserContext, atHomeDiagnosticOrder: AtHomeDiagnosticOrder): { text: string, icon: string } {
        return {
            text: `At Home | ${CareUtil.getHomeCollectionTimeText(atHomeDiagnosticOrder.startTime, atHomeDiagnosticOrder.endTime, userContext)}`,
            icon: "home"
        }
    }

    private getInCentreTitle(inCentreDiagnosticOrder: InCentreDiagnosticOrder, timezone: Timezone): { text: string, icon: string } {
        return {
            text: `At Centre | ${TimeUtil.formatEpochInTimeZone(timezone, inCentreDiagnosticOrder.slot.workingStartTime, "ddd, D MMM, h:mm A")}`,
            icon: "incentre"
        }
    }


    private getActions(userContext: UserContext, pack: BookingDetail, isHomeCollectionEnabled: boolean): Action[] {
        switch (pack.bundleSetupInfo.bundleBookingCTA) {
            case "BOOK_TEST":
                return CareUtil.getBookTestActions(userContext, pack, isHomeCollectionEnabled)
            case "TAKE_HEALTH_ASSESSMENT":
                return CareUtil.getHealthAssessmentActions(pack, "Fill Health assessment")
            case "DISCUSS_REPORT":
                const consultationStep = pack.bundleSetupInfo.bundleStepInfos.find(step => step.setupStep === "CONSULTATION")
                const testStep = pack.bundleSetupInfo.bundleStepInfos.find(step => step.setupStep === "DIAGNOSTIC_TEST")
                const actions: Action[] = []
                const testOrderId = testStep.testBookingInfo.diagnosticsTestOrderResponse[0].orderId
                const reportInfo: DiagnosticReportInfo = _.get(testStep, "testBookingInfo.diagnosticsTestOrderResponse.0.finalDiagnosticReport.diagnosticCheckUpReportInfo")
                const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "APP")
                if (userAgent === "DESKTOP" || userAgent === "MBROWSER" || (CareUtil.isNewEmailedReportSupported(userContext) && CareUtil.isTestReportIsNull(reportInfo))) {
                    actions.push(CareUtil.getDiagnosticsEmailTestReportAction(testOrderId, "Email Report", "REPORT"))
                } else {
                    actions.push({
                        actionType: "NAVIGATION",
                        icon: "REPORT",
                        url: ActionUtil.diagnosticReportPage(testOrderId, testStep.testBookingInfo.diagnosticsTestOrderResponse[0].carefitOrderId),
                        title: "View Report"
                    })
                }
                actions.push({
                    title: "Discuss Report",
                    icon: "DISCUSS",
                    url: CareUtil.getBookConsultationActionUrl(pack.booking.id.toString(), pack.booking.patientId, consultationStep.stepMeta),
                    actionType: "NAVIGATION"
                })
                return actions
            case "VIEW_PRESCRIPTION":
                return CareUtil.getViewPrescriptionActions(pack)
        }
    }

    private getActionsV2(userContext: UserContext, pack: BookingDetail, homeCenterSelector: HomeCenterSelector, reportActions: Map<string, any>): Action[] {
        const actions: Action[] = []
        const stepInfo = pack.stepInfosV2.find(step => step.stepInfoType === "DIAGNOSTIC_TEST")
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "APP")
        let testOrderId, carefitOrderId
        switch (stepInfo && stepInfo.stepState) {
            case "NOT_BOOKED":
                return CareUtil.getBookTestActionsFromActionContext(userContext, pack.booking.patientId, pack.primaryActionV2, pack.booking.subCategoryCode, homeCenterSelector)
            case "REPORT_GENERATED":
                testOrderId = stepInfo.diagnosticsTestBookingInfo.diagnosticsTestOrderResponse[0].orderId
                const reportInfo: DiagnosticReportInfo = _.get(stepInfo, "diagnosticsTestBookingInfo.diagnosticsTestOrderResponse.0.finalDiagnosticReport.diagnosticCheckUpReportInfo")
                if (userAgent === "DESKTOP" || userAgent === "MBROWSER" || (CareUtil.isNewEmailedReportSupported(userContext) && CareUtil.isTestReportIsNull(reportInfo))) {
                    return [CareUtil.getDiagnosticsEmailTestReportAction(testOrderId, "Email Report", "REPORT")]
                }
                carefitOrderId = stepInfo.diagnosticsTestBookingInfo.diagnosticsTestOrderResponse[0].carefitOrderId
                return [{
                    actionType: "NAVIGATION",
                    icon: "REPORT",
                    url: ActionUtil.diagnosticReportPage(testOrderId, carefitOrderId),
                    title: "View Report"
                }]
            default:
                if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
                    return actions
                } else {
                    carefitOrderId = stepInfo.diagnosticsTestBookingInfo.diagnosticsTestOrderResponse[0].carefitOrderId
                    let cta
                    switch (reportActions.get(carefitOrderId)) {
                        case "VIEW_REPORT":
                            cta = "View Report"
                            break
                        case "TRACK_REPORT":
                            cta = "Track Report"
                            break
                        case "NONE":
                        default:
                            return actions
                    }
                    testOrderId = stepInfo.diagnosticsTestBookingInfo.diagnosticsTestOrderResponse[0].orderId
                    return [{
                        actionType: "NAVIGATION",
                        icon: "REPORT",
                        url: ActionUtil.diagnosticReportPage(testOrderId, carefitOrderId),
                        title: cta
                    }]
                }
        }
        return actions
    }

}
export default MeHCUPackPageView
