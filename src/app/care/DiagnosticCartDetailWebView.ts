import {
    RouteNames,
    IDiagnosticCartWeb,
    IDiagnosticAddedProduct,
    IPatientDetails,
    Action,
    IDiagnosticCartWebAction, IEmptyCartSection, IToolTipSection
} from "@curefit/apps-common"
import { UserContext } from "@curefit/userinfo-common"
import { DiagnosticProduct, Patient } from "@curefit/care-common"
import { HealthfaceProductInfo } from "@curefit/albus-client"
import { OfferV2, PackOffersResponse } from "@curefit/offer-common"
import { OfferUtil } from "@curefit/base-utils"
import * as _ from "lodash"
import { ProductPrice } from "@curefit/product-common"
import CareUtil from "../util/CareUtil"

class DiagnosticCartDetailWebView implements IDiagnosticCartWeb {
    constructor(userContext: UserContext, cartId?: string, patientDetails?: Patient, productInfoList?: HealthfaceProductInfo[], testList?: DiagnosticProduct[], cartOfferList?: OfferV2[], careOfferList?: PackOffersResponse) {
        const totalItems: number = testList ? testList?.length : 0
        let totalTests: number = 0
        let maxReportReadyTime: number = 0
        let totalPrice: number = 0

        this.itemCount = totalItems
        this.addedProducts = {}
        this.id = cartId

        if (!patientDetails || !productInfoList || !productInfoList.length) {
            this.emptyCart = this.getEmptyCartData()
            return
        }

        testList.map((test) => {
            const productCode = test.productId
            let offerDetails,
                membershipInfo: HealthfaceProductInfo,
                price: ProductPrice,
                countTest: number,
                testCountString: string,
                reportReadyTime: number

            if (_.isEmpty(test)) {
                return
            }

            offerDetails = OfferUtil.getPackOfferAndPrice(test, careOfferList)
            membershipInfo = productInfoList.find(productInfo => productInfo.baseSellableProduct.productCode === test.productId)
            price = offerDetails.price
            totalPrice += price.listingPrice

            countTest = CareUtil.countParameters(membershipInfo?.baseSellableProduct?.diagnosticProductResponse)
            totalTests += countTest
            testCountString = `${countTest} Test${countTest > 1 ? "s" : ""}`

            reportReadyTime = membershipInfo?.baseSellableProduct?.diagnosticProductResponse?.reportingTat
            maxReportReadyTime = Math.max(maxReportReadyTime, reportReadyTime)

            this.addedProducts[productCode] = {
                title: test.title,
                productCode,
                price,
                action: this.getRemoveFromCartAction(test.productId),
                countTestsCtaText: testCountString,
                testCountString
            }
        })

        this.patientDetails = this.getPatientDetails(patientDetails)
        this.offers = cartOfferList
        this.testAndReportDescription = this.getTestAndReportDescription(totalItems, totalTests, maxReportReadyTime)
        this.action = this.getCartReviewNavigationAction(totalPrice)
        this.toolTipContent = this.getToolTipContent(testList[0].title || "Diagnostic Test", totalItems, patientDetails)
    }

    private getPatientDetails (patient: Patient): IPatientDetails {
        return {
            ...patient,
            ageString: patient.age ? `${patient.age} yrs` : undefined
        }
    }

    private getRemoveFromCartAction(productCode: string): Action {
        return {
            actionType : "REMOVE_ITEM_FROM_DIAGNOSTIC_CART" as any,
            meta: {
                productCode
            }
        }
    }

    private getEmptyCartData (): IEmptyCartSection {
        return {
            image: "/image/carefit/empty-diagnostic-cart.svg",
            title: "Your cart is empty",
            subTitle: "Choose from a range of preventive, chronic and lifestyle related tests we offer",
            action: {
                actionType: "NAVIGATION",
                title: "BOOK TESTS ON CARE.FIT",
                url: RouteNames.CareDiagnosticTests
            }
        }
    }

    private getTestAndReportDescription (totalItems: number, totalTests: number,  maxReportReadyTime?: number): string {
        let finalString = `${totalItems} ITEM${totalItems > 1 ? "S" : ""}.`.concat(" ", `${totalTests} TEST${totalTests > 1 ? "S" : ""}`)

        if (maxReportReadyTime) {
            finalString = finalString.concat(" | ", `REPORT READY IN ${maxReportReadyTime} HOUR${maxReportReadyTime > 1 ? "S" : ""}`)
        }

        return finalString
    }

    private getCartReviewNavigationAction (totalPrice: number): IDiagnosticCartWebAction {
        return {
            actionType: "NAVIGATION",
            url: RouteNames.CareDiagnosticsCartReview,
            leftPart: `Total ₹${totalPrice}`,
            rightPart: "Proceed"
        }
    }

    private getToolTipContent (title: string, totalItems: number, patientDetail: Patient): IToolTipSection {
        const nameString = patientDetail.name,
          genderString = patientDetail.gender,
          ageString = patientDetail.age

        return {
            title: title.concat(totalItems > 1 ? ` + ${totalItems - 1} Test${totalItems > 2 ? "s" : ""}` : ""),
            subTitle: "For ".concat(nameString, genderString ? ` | ${genderString}` : "", ageString ? ` | ${ageString}` : "")
        }
    }

    addedProducts: IDiagnosticAddedProduct
    emptyCart: IEmptyCartSection
    toolTipContent: IToolTipSection
    itemCount: number
    patientDetails: IPatientDetails
    testAndReportDescription: string
    action: IDiagnosticCartWebAction
    offers: OfferV2[]
    id: string
}

export default DiagnosticCartDetailWebView
