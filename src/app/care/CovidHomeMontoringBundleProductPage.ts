import { ConsultationInfoWidget } from "@curefit/apps-common"
import { UserAgentType } from "@curefit/base-common"
import { Constants, OfferUtil, RUPEE_SYMBOL } from "@curefit/base-utils"
import { DiagnosticProduct, DiagnosticProductResponse, ManagedPlanPackIn<PERSON>, <PERSON><PERSON>, <PERSON> } from "@curefit/care-common"
import { PackOffersResponse } from "@curefit/offer-common"
import { UserContext } from "@curefit/vm-models"
import { Action, Header, ProductDetailPage, WidgetView, RoundedImageGridWidget } from "../common/views/WidgetView"
import { CareUtil } from "../util/CareUtil"
import AppUtil, { AppFont } from "./../util/AppUtil"
import _ = require("lodash")
import { titleCase } from "@curefit/util-common"

interface CovidHomeMonitoringPrePurchasePagePromise {
    patientsListPromise: Promise<Patient[]>,
    offerPromise: Promise<PackOffersResponse>
    doctorsPromise: Promise<Doctor[]>
    allProductsPromise: Promise<DiagnosticProductResponse[]>
}

export class CovidHomeMonitoringPrePurchasePage extends ProductDetailPage {
    async buildView(
        userContext: UserContext,
        selectedProduct: DiagnosticProduct,
        pagePromise: CovidHomeMonitoringPrePurchasePagePromise
    ) {
        const widgetPromises: Promise<WidgetView | WidgetView[]>[] = []
        const actions = (async () => {
            return this.getActions(userContext, selectedProduct, await pagePromise.patientsListPromise)
        })()

        widgetPromises.push((async () => {
            return this.getSummaryWidget(userContext, selectedProduct)
        })())

        widgetPromises.push((async () => {
            return await this.getProductPricingWidget(userContext, selectedProduct, pagePromise)
        })())

        widgetPromises.push((async () => {
            const offers = await pagePromise.offerPromise
            const offerDetails = OfferUtil.getPackOfferAndPrice(selectedProduct, offers)
            return CareUtil.getCareOfferWidget("Offers Applied", offerDetails?.offers, userContext, false, true)
        })())

        widgetPromises.push((async () => {
            const bundleProducts: DiagnosticProductResponse[] = await pagePromise.allProductsPromise
            const doctors = pagePromise.doctorsPromise ? await pagePromise.doctorsPromise : undefined
            const selectedBundleProduct: DiagnosticProductResponse  = bundleProducts.find(product => product.productCode === selectedProduct.productId)
            const { howItWorksItem, packOffering, packBenefit } = CareUtil.getPackContent(selectedBundleProduct)
            const userAgent = userContext.sessionInfo.userAgent
            return [
                ...(!_.isEmpty(doctors) ? CareUtil.getPackDoctorListingWidgets(userContext, doctors, [selectedBundleProduct.infoSection.doctorInfo[0].doctorType], selectedBundleProduct) : []),
                // this.getPackOfferingImageWidget(packOffering, userAgent),
                this.getWhyCovidWidget(),
                CareUtil.getCareProductBenefitWidget(userContext, packBenefit),
                CareUtil.getHowItWorksWidget(howItWorksItem, userAgent)
            ]
        })())

        const widgets = await Promise.all(widgetPromises)
        this.widgets = _.flatMap(widgets).filter(Boolean).map(item => ({ ...item, hasDividerBelow: false }))
        this.actions = await actions

        return this
    }

    async getProductPricingWidget(
        userContext: UserContext,
        selectedProduct: DiagnosticProduct,
        pagePromise: CovidHomeMonitoringPrePurchasePagePromise
    ) {
        const bundleProducts: DiagnosticProductResponse[] = await pagePromise.allProductsPromise
        const offers = await pagePromise.offerPromise
        const isNewUIStylingSupported = AppUtil.isCovidHomeMonitoringNewUISupported(userContext)
        let recurringSection = await Promise.all(bundleProducts.map(async (product: DiagnosticProductResponse) => {
            const diagnosticProduct = CareUtil.toDiagnosticsProduct(product)
            const offerDetails = OfferUtil.getPackOfferAndPrice(diagnosticProduct, offers)
            const infoAction = (await this.getActions(userContext, diagnosticProduct, await pagePromise.patientsListPromise))[0]
            const duration = Number(diagnosticProduct.duration || 0)
            const priceMeta = duration > 1 ? `${RUPEE_SYMBOL} ${Math.ceil(offerDetails.price.listingPrice / duration)} / day` : undefined
            return {
                title: product.infoSection.sellingTitle,
                subTitle: product.infoSection.headerDescription,
                description: isNewUIStylingSupported ? product.infoSection.aboutSection : `${product.infoSection.sellingTitle} \n\n ${product.infoSection.aboutSection}`,
                price: {
                    mrp: offerDetails.price.mrp,
                    listingPrice: offerDetails.price.listingPrice,
                    showPriceCut: offerDetails.price.listingPrice < offerDetails.price.mrp,
                    currency: offerDetails.price.currency
                },
                priceMeta: priceMeta,
                selected: product.productCode === selectedProduct.productId,
                action: {
                    actionType: "NAVIGATION",
                    meta: {
                        selectedProductId: product.productCode
                    }
                },
                modalTitle: "ABOUT PACKAGE",
                modalSubTitle: product.infoSection.sellingTitle,
                infoAction: {
                    ...infoAction
                }
            }
        }))
        recurringSection = recurringSection.filter(Boolean)
        if (_.isEmpty(recurringSection)) {
            return undefined
        }
        return {
            widgetType: "PRODUCT_PRICING_WIDGET",
            header: {
                title: "Packages"
            },
            footerText: `${selectedProduct.duration} day pack valid from the date of purchase`,
            footerTextStyle: { marginLeft: 10 },
            sections: [{
                type: "RECURRING",
                value: recurringSection
            }],
            style: {
                backgroundColor: "#f2f4f8",
                paddingLeft: 10,
                marginBottom: 20,
                borderBottomLeftRadius: 7,
                borderBottomRightRadius: 7
            },
            hasDividerBelow: false
        } as WidgetView
    }

    getSummaryWidget(
        userContext: UserContext,
        selectedProduct: DiagnosticProduct
    ) {
        return CareUtil.getProductSummaryWidget({
            title: "Covid Home Care",
            description: selectedProduct.subTitle,
            imageUrl: selectedProduct.heroImageUrl,
            productCode: selectedProduct.productId
        },
            "BUNDLE",
            [],
            userContext.sessionInfo.userAgent)
    }

    getWhyCovidWidget() {
        const supportAction = {
            actionType: "EXTERNAL_DEEP_LINK",
            title: "WRITE TO US",
            url: `mailto:${Constants.customerCareMail}`
        } as Action
        return new ConsultationInfoWidget(
            [
                {
                    title: `This package is suitable for those recommended home quarantine after any of following: \n  •   Covid positive diagnosis\n  •   Inter State (or) International travel\n  •   Exposure to Covid Positive cases\n  •   With No or Mild Symptoms\n  •   Ages 14 and above\n\nYou need a self-isolated facility and 24hr access to a caregiver. You should be able to reach a hospital in case of emergencies.\nYou need Thermometer and Pulse Oximeter devices for reporting vitals to doctor`,
                    subtitle: undefined,
                    imageUrl: undefined,
                    itemClickAction: {
                        actionType: "NAVIGATION",
                        disabled: true
                    },
                    isSelected: false,
                    itemContainerStyle: {
                        paddingBottom: 15,
                        borderBottomWidth: 1,
                        borderBottomColor: "#eff1f6",
                    },
                    titleStyle: {
                        color: "#8d93a0",
                        fontSize: 14,
                        marginTop: -5,
                        fontFamily: AppFont.Regular,
                    },
                },
                {
                    title: "Have further questions?",
                    subtitle: undefined,
                    imageUrl: undefined,
                    isSelected: false,
                    itemClickAction: supportAction,
                    titleAction: supportAction,
                    itemContainerStyle: {
                        marginTop: -10,
                        paddingBottom: 0,
                    },
                    titleStyle: {
                        color: "#8d93a0",
                        fontSize: 14,
                        fontFamily: AppFont.Regular,
                    },
                }
            ],
            "Who is Covid Home Monitoring for?",
            undefined,
            {
                paddingTop: 11,
                paddingBottom: 5,
                marginBottom: 10,
            },
            {
                fontSize: 16,
                fontFamily: AppFont.Bold,
            },
        )
    }

    getPackOfferingImageWidget(packOffering: ManagedPlanPackInfo, userAgent?: UserAgentType): WidgetView {
        if (_.isEmpty(packOffering)) {
            return undefined
        }
        const header = {
            title: packOffering.title,
            subTitle: packOffering.subtitle,
            titleProps: {
                style: {
                    fontSize: 18,
                    color: "black",
                    fontFamily: AppFont.Bold
                }
            }
        }

        return new RoundedImageGridWidget(
            header,
            packOffering.children.map((item, index) => {
                return {
                    title: undefined,
                    image: item.imageUrl,
                    action: item?.action?.url,
                }
            })
        )
    }

    async getActions(
        userContext: UserContext,
        product: DiagnosticProduct,
        patientsList: Patient[]
    ): Promise<Action[]> {
        const title = "Get Home Care"
        if (!userContext.sessionInfo.isUserLoggedIn) {
            return [
                CareUtil.loginAlertModal(title)
            ]
        } else {
            const action: Action = {
                actionType: "NAVIGATION",
                meta: {
                    formUserType: "CARE_USER"
                },
                url: `curefit://carecartcheckout?productId=${product.productId}&subCategoryCode=${product.subCategoryCode}`,
            }
            return [
                CareUtil.getPatientSelectionModalAction(patientsList,
                    AppUtil.isExistingPackPurchaseForPatientSupported(userContext) ? {
                        actionType: "CHECK_BUNDLE_EXISTS",
                        title,
                        meta: {
                            productCode: product.productId,
                            onSuccessAction: CareUtil.getBundleConsentInfoModal(action),
                            onFailureAction: action
                        }
                    } : action as Action,
                    title,
                    "CARE_USER"
                )
            ]
        }
    }


}
