import { inject, injectable } from "inversify"
import * as _ from "lodash"
import {
    ALBUS_CLIENT_TYPES,
    BookingDetail,
    BundleFilterType,
    BundleSellableProduct,
    BundleSessionSellableProduct,
    CareTeam,
    IHealthfaceService,
    ManagedPlanSellableProduct,
    MPChildProduct,
    Patient,
    RecommendLabTestResponse,
    SUB_CATEGORY_CODE,
    ConsultationInstructionResponse
} from "@curefit/albus-client"
import {
    ConsultationProduct,
    DiagnosticProduct,
    DiagnosticProductResponse,
    HealthfaceTenant,
    Doctor
} from "@curefit/care-common"
import {
    CATALOG_CLIENT_TYPES,
    ICatalogueService
} from "@curefit/catalog-client"
import {
    GMF_CLIENT_TYPES,
    IGMFClient as IGoalManagementService
} from "@curefit/gmf-client"
import {
    OFFER_SERVICE_CLIENT_TYPES, OfferServiceV3
} from "@curefit/offer-service-client"
import {
    ManageOptionPayload,
    ProductDetailPage
} from "../common/views/WidgetView"
import AppUtil from "../util/AppUtil"
import CollectionUtil from "../util/CollectionUtil"
import { UserContext } from "@curefit/userinfo-common"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import ICRMIssueService from "../crm/ICRMIssueService"
import { CustomerIssueType } from "@curefit/issue-common"
import TherapyPageConfig from "../therapy/TherapyPageConfig"
import { CareUtil, HomeCenterSelector } from "../util/CareUtil"
import BundleProductPageView from "./BundleProductPageView"
import ManagedPlanProductPageView from "./ManagedPlanProductPageView"
import IssueBusiness, { IssueDetailView } from "../crm/IssueBusiness"
import ManagedPlanProductPageViewV1 from "./ManagedPlanProductPageViewV1"
import BundleSessionAfterBookingPageView from "../pack/BundleSessionAfterBookingPageView"
import ManagedPlanOnboardingProductPageView from "./ManagedPlanOnboardingProductPageView"
import ManagedPlanBookingInfoProductPageView from "./ManagedPlanBookingInfoProductPageView"
import BundleSessionBeforeBookingPageView from "../pack/BundleSessionBeforeBookingPageView"
import MindTherapyBookingInfoProductPageView from "../pack/MindTherapyBookingInfoProductPageView"
import SkinCareBundlePostPurchaseProductPageView from "./SkinCareBundlePostPurchaseProductPageView"
import SkinCareBundlePrePurchaseProductPageView from "./SkinCareBundlePrePurchaseProductPageView"
import { CacheHelper } from "../util/CacheHelper"
import { CFS_TYPES } from "@curefit/cfs-client"
import { ICFSClient as IFormService } from "@curefit/cfs-client/dist/src/ICFSClient"
import { CULT_CLIENT_TYPES } from "@curefit/cult-client"
import { ICultServiceOld as ICultService } from "@curefit/cult-client/dist/src/ICultServiceOld"
import { InAppNotificationsService, IRIS_CLIENT_TYPES } from "@curefit/iris-client"
import { MembershipPaymentType } from "@curefit/order-common"
import { ALFRED_CLIENT_TYPES, IPayByMembershipService } from "@curefit/alfred-client"
import { HamletBusiness, HAMLET_TYPES } from "@curefit/hamlet-node-sdk"
import { CareBusiness, ICareBusiness } from "./CareBusiness"
import ConsultationPackPrePurchaseProductView from "./ConsultationPackPrePurchaseProductPage"
import ConsultationPackPostPurchaseProductPage from "./ConsultationPackPostPurchaseProductPage"
import { FUSE_CLIENT_TYPES, IDiagnosticService } from "@curefit/fuse-node-client"
import { OLLIVANDER_CLIENT_TYPES, IOllivanderCityService } from "@curefit/ollivander-node-client"
import { CovidHomeMonitoringPrePurchasePage } from "./CovidHomeMontoringBundleProductPage"
import { CareWidgetUtil } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"

const clone = require("clone")

@injectable()
export class ManagedPlanProductPageViewBuilder {

    constructor(
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) protected catalogueService: ICatalogueService,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) private offerServiceV3: OfferServiceV3,
        @inject(CUREFIT_API_TYPES.CRMIssueService) private CRMIssueService: ICRMIssueService,
        @inject(GMF_CLIENT_TYPES.IGMFClient) public gmfService: IGoalManagementService,
        @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
        @inject(ALBUS_CLIENT_TYPES.CultPersonalTrainingService) private cultPtService: IHealthfaceService,
        @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness,
        @inject(CUREFIT_API_TYPES.TherapyPageConfig) private therapyPageConfig: TherapyPageConfig,
        @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
        @inject(CFS_TYPES.ICFSClient) public formService: IFormService,
        @inject(CULT_CLIENT_TYPES.CultFitService) public cultFitService: ICultService,
        @inject(CULT_CLIENT_TYPES.MindFitService) public mindFitService: ICultService,
        @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
        @inject(ALFRED_CLIENT_TYPES.PayByMembershipService) public payByMembershipService: IPayByMembershipService,
        @inject(CUREFIT_API_TYPES.CareBusiness) public careBusiness: ICareBusiness,
        @inject(FUSE_CLIENT_TYPES.IDiagnosticSellerService) protected diagnosticService: IDiagnosticService,
        @inject(OLLIVANDER_CLIENT_TYPES.IOllivanderCityService) private ollivanderService: IOllivanderCityService,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
    ) {

    }

    async getBeforeBookingPage(userContext: UserContext, isNotLoggedIn: boolean, userId: string, deviceId: string, productId: string, subCategoryCode: SUB_CATEGORY_CODE, isMultiCenterEnabled: boolean, subscriptionCode?: string, patientId?: number): Promise<ProductDetailPage> {
        const productPromise = this.catalogueService.getProduct(productId)
        const patientsListPromise: Promise<Patient[]> = this.healthfaceService.getAllPatients(userId)
        const healthfaceProductPromise = this.healthfaceService.getProductInfoDetailsCached("BUNDLE", subCategoryCode, productId, CareUtil.getHealthfaceTenant(subCategoryCode))
        const product = await productPromise
        const baseProduct: DiagnosticProduct = clone(<DiagnosticProduct>product)
        const packageProduct = <ManagedPlanSellableProduct>((await healthfaceProductPromise)[0].baseSellableProduct)
        const user = await this.userCache.getUser(userId)
        let preferredCenter
        if (patientId) {
            preferredCenter = this.healthfaceService.getPatientPreferredCenter(userId, patientId, "BUNDLE", packageProduct.subCategoryCode, packageProduct.productCode)
        }
        if (subCategoryCode === "DIAG_PACK" || subCategoryCode === "HCU_PACK" || subCategoryCode === "AI_MG" || subCategoryCode === "GENETICS_PACK") {
            if (subCategoryCode === "AI_MG") {
                isMultiCenterEnabled = false
                const subscriptionProducts = packageProduct.childProducts.filter(product => product.baseSellableProduct.subCategoryCode === "MP_SUBS")
                const minSubscriptionProduct = _.minBy(subscriptionProducts, product => product.baseSellableProduct.duration)
                const offers = CareWidgetUtil.getCareProductOffersFromUserContext(
                    userContext,
                    "BUNDLE",
                    [minSubscriptionProduct.baseSellableProduct.productCode],
                    AppUtil.callSourceFromContext(userContext),
                    this.serviceInterfaces,
                    true
                )
                const subscriptionStartDate = this.healthfaceService.getStartDate("MP", -1)
                // const eligibility = this.healthfaceService.checkEligibility(userId, "BUNDLE", subCategoryCode, packageProduct.productCode)
                // const eligibilityData = this.gmfService.checkEligibility(userId)
                return new BundleProductPageView(user, userContext, isNotLoggedIn, baseProduct, isMultiCenterEnabled, packageProduct, await preferredCenter, await patientsListPromise, await offers, undefined, await subscriptionStartDate, minSubscriptionProduct, patientId)
            } else {
                // disabling center selection for screening packs also
                if (subCategoryCode === "DIAG_PACK") {
                    isMultiCenterEnabled = false
                }
                const offers = CareWidgetUtil.getCareProductOffersFromUserContext(
                    userContext,
                    "BUNDLE",
                    [packageProduct.productCode],
                    AppUtil.callSourceFromContext(userContext),
                    this.serviceInterfaces,
                    true
                )
                const source = AppUtil.callSourceFromContext(userContext)
                const {offerIds} = await this.offerServiceV3.getApplicableCartOffersForCare({
                    userInfo: {userId, deviceId},
                    requiredOfferTypes: ["BUNDLE"],
                    source,
                    cityId: userContext.userProfile.cityId
                })
                const cartOffers = _.values((await this.offerServiceV3.getOffersByIds(offerIds)).data)
                const sellerDetails = await this.diagnosticService.getDiagnosticSellerForCity(userContext.userProfile.cityId)
                return new BundleProductPageView(user, userContext, isNotLoggedIn, baseProduct, isMultiCenterEnabled, packageProduct, await preferredCenter, await patientsListPromise, await offers, cartOffers, undefined, undefined, patientId, sellerDetails)
            }
        } else {
            const isMPV2 = (subCategoryCode === "MP_V2")
            const subscriptionStartDate = this.healthfaceService.getStartDate(isMPV2 ? "MP_V2" : "MP", -1)
            const offers = CareWidgetUtil.getCareProductOffersFromUserContext(
                userContext,
                "BUNDLE",
                packageProduct.childProducts.filter(product => product.baseSellableProduct.categoryCode !== "DEVICE").map(product => product.baseSellableProduct.productCode),
                AppUtil.callSourceFromContext(userContext),
                this.serviceInterfaces,
                true
            )
            let issues: IssueDetailView[]
            if (AppUtil.isNewReportIssueSupported(userContext)) {
                issues = await this.issueBusiness.getMPPrePurchaseIssue(userContext)
            }
            if (isMPV2) {
                return new ManagedPlanProductPageViewV1(userContext, isNotLoggedIn, baseProduct, packageProduct, await subscriptionStartDate, isMultiCenterEnabled, await patientsListPromise, subscriptionCode, await offers, issues, await preferredCenter, patientId)
            } else {
                const devices = packageProduct.childProducts.filter(product => product.baseSellableProduct.categoryCode === "DEVICE")
                let deviceOffers
                if (!_.isEmpty(devices)) {
                    deviceOffers = CareWidgetUtil.getCareProductOffersFromUserContext(
                        userContext,
                        "DEVICE",
                        devices.map(product => product.baseSellableProduct.productCode),
                        AppUtil.callSourceFromContext(userContext),
                        this.serviceInterfaces,
                        true
                    )
                }
                let recurringTests
                const subscriptionsProducts = packageProduct.childProducts.filter(product => product.childProductType === "CHILD_MANDATORY").filter(product => product.baseSellableProduct.subCategoryCode === "MP_SUBS")
                if (!_.isEmpty(subscriptionsProducts)) {
                    const highetsubscriptionProduct = _.maxBy(subscriptionsProducts, product => product.baseSellableProduct.duration)
                    const tests = highetsubscriptionProduct.baseSellableProduct.childProducts.filter(product => product.baseSellableProduct.categoryCode === "DIAGNOSTICS")
                    recurringTests = _.map(tests, async (test) => {
                        const product = (<BundleSellableProduct>(await this.healthfaceService.getProductInfoDetailsCached("DIAGNOSTICS", undefined, test.baseSellableProduct.productCode))[0].baseSellableProduct).diagnosticProductResponse
                        product.metaText = test.displayText
                        return product
                    })
                }
                return new ManagedPlanProductPageView(userContext, isNotLoggedIn, baseProduct, isMultiCenterEnabled, packageProduct, await subscriptionStartDate, await patientsListPromise, subscriptionCode, await offers, await deviceOffers, recurringTests ? await Promise.all(recurringTests) : undefined, issues)
            }
        }
    }

    async getSkinCareBeforeBookingPage(
        userContext: UserContext,
        isNotLoggedIn: boolean,
        userId: string,
        deviceId: string,
        productId: string,
        subCategoryCode: SUB_CATEGORY_CODE,
        selectedProductId?: string,
        setCode?: string,
        clubCode?: string
    ) {
        const selectedProduct = selectedProductId || productId
        let allClubbedProducts: DiagnosticProductResponse[] = [], allProductsCombinedResponse: { diagnosticProduct: DiagnosticProduct, product: DiagnosticProductResponse }[] = []
        const baseProduct = <DiagnosticProduct>await this.catalogueService.getProduct(selectedProduct)
        const patientsListPromise: Promise<Patient[]> = this.healthfaceService.getAllPatients(userId)
        const tenant: HealthfaceTenant = CareUtil.getHealthfaceTenant(subCategoryCode)
        const productFullDetails = await this.healthfaceService.getProductInfoDetailsCached("BUNDLE", subCategoryCode, selectedProduct, CareUtil.getHealthfaceTenant(subCategoryCode))
        const packageProduct = <DiagnosticProductResponse>(productFullDetails[0].baseSellableProduct)
        if (!_.get(packageProduct, "infoSection.isTrial", false)) {
            allClubbedProducts = await this.healthfaceService.browseProducts("BUNDLE", subCategoryCode, tenant, true, setCode || packageProduct.setCode, clubCode || packageProduct.clubCode, false, undefined, true)
            allClubbedProducts = allClubbedProducts.filter(product => product.userFacing === true)
            allProductsCombinedResponse = allClubbedProducts.map(product => {
                return {
                    diagnosticProduct: CareUtil.toDiagnosticsProduct(product),
                    product: <DiagnosticProductResponse>product
                }
            }).sort((a, b) => {
                if (a.product && a.product.infoSection && a.product.infoSection.numberOfSessions && b.product && b.product.infoSection && b.product.infoSection.numberOfSessions) {
                    return Number(a.product.infoSection.numberOfSessions) - Number(b.product.infoSection.numberOfSessions)
                } else {
                    return 0
                }
            })
        }
        const offers = CareWidgetUtil.getCareProductOffersFromUserContext(
            userContext,
            "BUNDLE",
            !_.isEmpty(allClubbedProducts) ? allClubbedProducts.map(product => product.productCode) : [baseProduct.productId],
            AppUtil.callSourceFromContext(userContext),
            this.serviceInterfaces,
            true
        )
        const userSegments = await this.serviceInterfaces.clsUtil.getPlatformSegments()
        return new SkinCareBundlePrePurchaseProductPageView(userContext, isNotLoggedIn, baseProduct, allProductsCombinedResponse, packageProduct, await patientsListPromise, await offers, this.serviceInterfaces, userSegments)
    }

    async getBeforeBookingBundleSessionsPage(userContext: UserContext, isNotLoggedIn: boolean, userId: string, deviceId: string, productId: string, subCategoryCode: SUB_CATEGORY_CODE, filterType?: BundleFilterType, useCultMemberShipForPayment?: boolean, membershipPaymentType?: MembershipPaymentType, clubCode?: string) {
        const healthfaceTenant: HealthfaceTenant = CareUtil.getHealthfaceTenant(subCategoryCode)
        let bundleProductIds: string[] = []
        if (filterType) {
            const bundleSellableProductResponse = await this.healthfaceService.getBundleSellableProducts(subCategoryCode, healthfaceTenant)
            const filteredBundleType = _.find(bundleSellableProductResponse.bundleTypes, bundleType => bundleType.type === filterType)
            if (!_.isEmpty(filteredBundleType)) {
                bundleProductIds = filteredBundleType.products
            }
        } else {
            const products: DiagnosticProductResponse[] = await this.healthfaceService.browseProducts("BUNDLE", subCategoryCode, healthfaceTenant, true, undefined, clubCode, undefined, undefined, true)
            bundleProductIds = products.map(product => product.productCode)
        }
        const patientsListPromise = this.healthfaceService.getAllPatients(userId)
        const offerPromise = CareWidgetUtil.getCareProductOffersFromUserContext(userContext, "BUNDLE", bundleProductIds, AppUtil.callSourceFromContext(userContext), this.serviceInterfaces, true)
        const bundleProductsPromises = _.map(bundleProductIds, async (productId) => {
            const healthfaceProduct = await this.healthfaceService.getProductInfoDetailsCached("BUNDLE", subCategoryCode, productId, healthfaceTenant)
            return <BundleSessionSellableProduct>(healthfaceProduct[0].baseSellableProduct)
        })
        let bundleProducts = await Promise.all(bundleProductsPromises)
        bundleProducts = bundleProducts.filter(bundleProduct => bundleProduct.productStatus === "LIVE")
        let selectedProduct = _.find(bundleProducts, product => product.productCode === productId)
        if (_.isEmpty(selectedProduct)) {
            selectedProduct = bundleProducts[0]
        }
        return new BundleSessionBeforeBookingPageView().buildView(userContext, isNotLoggedIn, bundleProducts, selectedProduct, await patientsListPromise, await offerPromise, this.userCache, useCultMemberShipForPayment, membershipPaymentType, this.payByMembershipService)
    }

    async getAfterBookingPage(userContext: UserContext, productId: string, bookingId: number, source?: string): Promise<ProductDetailPage> {
        const deviceId = userContext.sessionInfo.deviceId
        const userId = userContext.userProfile.userId
        const product: DiagnosticProduct = <DiagnosticProduct>await this.catalogueService.getProduct(productId)
        const healthfaceTenant: HealthfaceTenant = CareUtil.getHealthfaceTenant(product.subCategoryCode)
        const isPartOfSkinProducts = CareUtil.isPartOfSkinProducts(product.subCategoryCode)
        const isPartOfConsultationPackProducts = CareUtil.isPartOfConsultationPackProducts(product.subCategoryCode)
        const bookingInfo = await this.healthfaceService.getBookingDetailForBookingId(bookingId, healthfaceTenant, isPartOfConsultationPackProducts || isPartOfSkinProducts)
        const cityCode: string = userContext.userProfile.cityId
        if (bookingInfo.booking.status !== "CANCELLED") {
            const baseProduct: DiagnosticProduct = clone(<DiagnosticProduct>product)
            const { issuesMap, newReportIssueManageOption } = await this.getIssueManageOptions(userContext, bookingInfo)
            if (["MIND_THERAPY", "PERSONAL_TRAINING", "PHYSIOTHERAPY", "NUTRITIONIST", "LIVE_PERSONAL_TRAINING", "LIVE_SGT"].indexOf(bookingInfo.booking.subCategoryCode) !== -1) {
                return this.getPTMindPhysioLCPacksAfterBookingPage(userContext, deviceId, userId, source, bookingInfo, baseProduct, healthfaceTenant, issuesMap, newReportIssueManageOption)
            } else if (isPartOfSkinProducts) {
                return this.getSkinAfterBookingPage(userContext, deviceId, userId, bookingInfo, baseProduct, healthfaceTenant, issuesMap, newReportIssueManageOption)
            } else if (isPartOfConsultationPackProducts) {
                return this.getConsultationPostPurchaseProductPage(userContext, deviceId, userId, bookingInfo, baseProduct, healthfaceTenant, issuesMap, newReportIssueManageOption)
            }
            const bundleBookingInfo = this.getBundleBookingInfo(bookingInfo)
            if ((bookingInfo.booking.subCategoryCode !== "MP_V2" && bookingInfo.booking.subCategoryCode !== "MP" && bookingInfo.booking.subCategoryCode !== "AI_MG") || CareUtil.checkMPOnboardingNotDone(bundleBookingInfo)) {// todo change once backend add support
                let testBookingId: any = undefined, reportEstimationTime: any = undefined, diagnosticsEmailedTestReport, sampleCollectionLocationResponse, diagnosticTestInstruction, aggregatedMembershipsPromise
                const bundleStepInfos = _.get(bundleBookingInfo, "stepInfosV2", [])
                let reportAction: string
                if (["HCU_PACK", "DIAG_PACK"].includes(product.subCategoryCode)) {
                    aggregatedMembershipsPromise = this.healthfaceService.getAggregatedMembershipsByRootBookingId(bookingInfo.booking.id, bookingInfo.booking.patientId, product.subCategoryCode === "DIAG_PACK" ? "DIAGNOSTICS" : "CONSULTATION", undefined, healthfaceTenant)
                }
                if (!_.isEmpty(bundleStepInfos)) {
                    const bookingDetail = bundleStepInfos.find(item => _.get(item, "stepInfoType", null) === "DIAGNOSTIC_TEST" && _.get(item, "stepState", null) === "TEST_COMPLETED")
                    testBookingId = !_.isEmpty(bookingDetail) ? _.get(bookingDetail, "diagnosticsTestBookingInfo.booking.id", null) : null
                    if (testBookingId) {
                        reportEstimationTime = await this.healthfaceService.getTestReportEstimationTime(testBookingId)
                    }
                    const reportbookingDetail = bundleStepInfos.find(item => _.get(item, "stepInfoType", null) === "DIAGNOSTIC_TEST" && _.get(item, "stepState", null) !== "NOT_BOOKED")
                    const carefitOrderId = !_.isEmpty(reportbookingDetail) ? _.get(reportbookingDetail, "diagnosticsTestBookingInfo.diagnosticsTestOrderResponse.carefitOrderId", _.get(reportbookingDetail, "diagnosticsTestBookingInfo.diagnosticsTestOrderResponse.0.carefitOrderId", null)) : null
                    if (carefitOrderId) {
                        diagnosticsEmailedTestReport = await this.healthfaceService.getDiagnosticsEmailedTestReport(carefitOrderId)
                        reportAction = await this.healthfaceService.getReportAction({ carefitOrderId })
                    }
                    const isNotbookedState = bundleStepInfos.find(item => _.get(item, "stepInfoType", null) === "DIAGNOSTIC_TEST" && _.get(item, "stepState", null) === "NOT_BOOKED")
                    if (isNotbookedState) {
                        sampleCollectionLocationResponse = await this.healthfaceService.getDiagnosticTestAllowedActionFromCollectionCodes(bundleBookingInfo.primaryActionV2.actionContext.testProductCodes, bookingId.toString(), cityCode)
                        if (sampleCollectionLocationResponse?.sampleCollectionLocations?.length === 1) {
                            diagnosticTestInstruction = await this.healthfaceService.getProductInstructions("DIAGNOSTICS", bundleBookingInfo.primaryActionV2.actionContext.testProductCodes, CareUtil.getDiagnosticTestType(sampleCollectionLocationResponse.sampleCollectionLocations[0]))
                        }
                    }
                }
                const homeCenterSelector: HomeCenterSelector = { sampleCollectionLocationResponse: sampleCollectionLocationResponse, diagnosticTestInstruction: diagnosticTestInstruction }
                return new ManagedPlanOnboardingProductPageView(
                    userContext,
                    baseProduct,
                    bookingInfo,
                    aggregatedMembershipsPromise ? (await aggregatedMembershipsPromise)[0] : undefined,
                    issuesMap,
                    newReportIssueManageOption,
                    reportEstimationTime,
                    diagnosticsEmailedTestReport,
                    homeCenterSelector,
                    reportAction
                )
            } else {
                const aggregatedMembershipsPromise = this.healthfaceService.getAggregatedMembershipsByRootBookingId(bookingInfo.booking.id, bookingInfo.booking.patientId, "DIAGNOSTICS", "MP_SUBS,MP,MP_V2")
                const careTeamPromise = this.healthfaceService.getYourTeam(userId, bookingInfo.booking.patientId, bookingInfo.booking.productCode)
                const consultationsPromise = this.healthfaceService.getAllBookingsByRootBookingId(bookingInfo.booking.id, "CONSULTATION", userId, 5)
                const devices = this.healthfaceService.getAllBookingsByRootBookingId(bookingInfo.booking.id, "DEVICE", userId)
                const reports = this.healthfaceService.getAllReportsByRootBookingId(bookingInfo.booking.id, 5)
                let recommendedTestsPromise: Promise<RecommendLabTestResponse>
                if (bookingInfo.booking.subCategoryCode === "MP_V2") {
                    recommendedTestsPromise = this.healthfaceService.getRecommendedTests(bookingInfo.booking.patientId, 0)
                }
                const aggregatedMemberships = await aggregatedMembershipsPromise
                const aggregatedMembershipsProductPromises = _.map(aggregatedMemberships, async (aggregatedMembership) => {
                    aggregatedMembership.productMetas[0].productDetail = (<BundleSellableProduct>(await this.healthfaceService.getProductInfoDetailsCached("DIAGNOSTICS", undefined, aggregatedMembership.productMetas[0].productCode))[0].baseSellableProduct).diagnosticProductResponse
                    return aggregatedMembership
                })
                const subscriptionBooking = bookingInfo.childBookingInfos.find(booking => booking.booking.subCategoryCode === "MP_SUBS")
                let subsOffers
                let renewAction
                if (!_.isEmpty(subscriptionBooking.stepInfosV2[0].actionV2s)) {
                    renewAction = subscriptionBooking.stepInfosV2[0].actionV2s.find(action => action.actionType === "RENEW_SUBSCRIPTION")
                    if (!_.isEmpty(renewAction))
                        subsOffers = CareWidgetUtil.getCareProductOffersFromUserContext(userContext, "BUNDLE", renewAction.actionContext.subscriptionProducts.map((product: MPChildProduct) => product.baseSellableProduct.productCode), AppUtil.callSourceFromContext(userContext), this.serviceInterfaces, true)
                }
                const subscriptionProduct = this.catalogueService.getProduct(subscriptionBooking.booking.productCode)
                let tests = await this.healthfaceService.getAllBookingsByRootBookingId(bookingInfo.booking.id, "DIAGNOSTICS", userId, 5)
                const diagnosticPromises = _.map(tests, async diagnostic => {
                    diagnostic.diagnosticsTestOrderResponse[0].firstProductName = (await this.catalogueService.getProduct(diagnostic.diagnosticsTestOrderResponse[0].productCodes[0])).title
                    return diagnostic
                })
                tests = await Promise.all(diagnosticPromises)
                const subscriptionStartDate = this.healthfaceService.getStartDate(bookingInfo.booking.subCategoryCode, bookingId)
                let consultations = await consultationsPromise
                consultations = await Promise.all(consultations.map(async (consultation) => {
                    consultation.consultationProduct = await this.catalogueService.getProduct(consultation.booking.productCode)
                    return consultation
                }))
                return new ManagedPlanBookingInfoProductPageView(
                    userContext,
                    await careTeamPromise,
                    baseProduct,
                    subscriptionBooking,
                    <DiagnosticProduct>await subscriptionProduct,
                    bookingInfo,
                    issuesMap,
                    newReportIssueManageOption,
                    consultations,
                    await devices,
                    await tests,
                    CollectionUtil.getNullSafeList(await reports),
                    await Promise.all(aggregatedMembershipsProductPromises),
                    renewAction,
                    await subsOffers,
                    await subscriptionStartDate,
                    <RecommendLabTestResponse>await recommendedTestsPromise
                )
            }
        }
    }

    async getPTMindPhysioLCPacksAfterBookingPage(
        userContext: UserContext,
        deviceId: string,
        userId: string,
        source: string,
        bookingInfo: BookingDetail,
        baseProduct: DiagnosticProduct,
        healthfaceTenant: HealthfaceTenant,
        issuesMap: Map<string, CustomerIssueType[]>,
        newReportIssueManageOption: ManageOptionPayload,
    ): Promise<ProductDetailPage> {
        const aggregatedMembershipsPromise = this.healthfaceService.getAggregatedMembershipsByRootBookingId(bookingInfo.booking.id, bookingInfo.booking.patientId, "CONSULTATION", undefined, healthfaceTenant)
        const consultationsPromise = this.healthfaceService.getAllBookingsByRootBookingId(bookingInfo.booking.id, "CONSULTATION", userId, undefined, healthfaceTenant)
        const patientListsPromise = this.healthfaceService.getAllPatients(userId)
        const sellableProductPromise = this.healthfaceService.getProductInfoDetailsCached("BUNDLE", bookingInfo.booking.subCategoryCode, bookingInfo.booking.productCode, healthfaceTenant)
        const aggregatedMembership = (await aggregatedMembershipsPromise)[0]
        let offerPromise
        if (!_.isEmpty(aggregatedMembership)) {
            offerPromise = CareWidgetUtil.getCareProductOffersFromUserContext(userContext, "CONSULTATION", aggregatedMembership.productMetas.map(productMeta => productMeta.productCode), AppUtil.callSourceFromContext(userContext), this.serviceInterfaces, true)
            const membershipProductsPromises = _.map(aggregatedMembership.productMetas, async (productMeta) => {
                productMeta.productDetail = <ConsultationProduct>(await this.catalogueService.getProduct(productMeta.productCode))
                return productMeta
            })
            aggregatedMembership.productMetas = await Promise.all(membershipProductsPromises)
        }

        let isSupportGroupBundle = false
        const benefits = await this.healthfaceService.getProductBenefits({ productCodes: [bookingInfo.booking.productCode] })
        if (benefits && benefits.content && benefits.content[0]) {
            const productDetail: ConsultationProduct = <ConsultationProduct>await this.catalogueService
                    .getProduct(benefits.content[0].offerProductCode.toString())
            if (productDetail && CareUtil.isSupportGroupProduct(productDetail)) {
                isSupportGroupBundle = true
            }
        }
        const careTeam = isSupportGroupBundle ? undefined : await this.healthfaceService.getYourTeam(userId, bookingInfo.booking.patientId, bookingInfo.booking.productCode, healthfaceTenant)

        if (bookingInfo.booking.subCategoryCode === "PERSONAL_TRAINING" || bookingInfo.booking.subCategoryCode === "PHYSIOTHERAPY" || bookingInfo.booking.subCategoryCode === "NUTRITIONIST" || bookingInfo.booking.subCategoryCode === "LIVE_PERSONAL_TRAINING" || bookingInfo.booking.subCategoryCode === "LIVE_SGT" || CareUtil.isMindTherapyPageWeb(userContext, bookingInfo.booking.subCategoryCode)) {
            let consultations = await consultationsPromise
            consultations = await Promise.all(consultations.map(async (consultation) => {
                consultation.consultationProduct = await this.catalogueService.getProduct(consultation.booking.productCode)
                return consultation
            }))
            let preferedDoctor: CareTeam[]
            if (bookingInfo.booking.subCategoryCode === "PERSONAL_TRAINING") {
                preferedDoctor = await this.healthfaceService.getYourTeam(userContext.userProfile.userId, bookingInfo.booking.patientId, bookingInfo.booking.productCode, CareUtil.getHealthfaceTenant(bookingInfo.booking.subCategoryCode))
            }
            return new BundleSessionAfterBookingPageView().buildView(userContext, source, careTeam, <BundleSessionSellableProduct>(await sellableProductPromise)[0].baseSellableProduct, bookingInfo, issuesMap, newReportIssueManageOption, consultations, await patientListsPromise, aggregatedMembership, preferedDoctor, this.healthfaceService, this.cultFitService, this.careBusiness, this.catalogueService)
        }
        return new MindTherapyBookingInfoProductPageView(
            userContext,
            careTeam,
            baseProduct,
            bookingInfo,
            this.therapyPageConfig,
            issuesMap,
            newReportIssueManageOption,
            await consultationsPromise,
            await patientListsPromise,
            aggregatedMembership,
            await offerPromise,
            AppUtil.isFromFlutterAppFlow(userContext) ? <BundleSessionSellableProduct>(await sellableProductPromise)[0].baseSellableProduct : undefined
        )
    }

    async getSkinAfterBookingPage(
        userContext: UserContext,
        deviceId: string,
        userId: string,
        bookingInfo: BookingDetail,
        baseProduct: DiagnosticProduct,
        healthfaceTenant: HealthfaceTenant,
        issuesMap: Map<string, CustomerIssueType[]>,
        newReportIssueManageOption: ManageOptionPayload,
    ): Promise<ProductDetailPage> {
        const sellableProductPromise = this.healthfaceService.getProductInfoDetailsCached("BUNDLE", bookingInfo.booking.subCategoryCode, bookingInfo.booking.productCode, healthfaceTenant)
        let aggregatedMemberships = await this.healthfaceService.getAggregatedMembershipsByRootBookingId(bookingInfo.booking.id, bookingInfo.booking.patientId, "CONSULTATION", undefined, healthfaceTenant)
        if (!_.isEmpty(aggregatedMemberships)) {
            const aggregatedMembershipPromises = _.map(aggregatedMemberships, async (aggregatedMembership) => {
                const userMembershipInfosPromises = _.map(aggregatedMembership.userMembershipInfos, async (userMembershipInfo) => {
                    const product = <ConsultationProduct>(await this.catalogueService.getProduct(userMembershipInfo.productCode))
                    userMembershipInfo.productDetail = product
                    userMembershipInfo.consultationInstruction = <ConsultationInstructionResponse[]>(await this.healthfaceService.getConsultationInstructionsV2(
                        product.productId,
                        product.doctorType,
                        CareUtil.getConsultationMode(product, undefined),
                        CareUtil.getInstructionTenant(product)
                    ))
                    return userMembershipInfo
                })
                aggregatedMembership.userMembershipInfos = await Promise.all(userMembershipInfosPromises)
                return aggregatedMembership
            })
            aggregatedMemberships = await Promise.all(aggregatedMembershipPromises)
        }
        const primaryAggregatedMembership = aggregatedMemberships.find(aggregatedMembership => aggregatedMembership.aggregatedBenefitType === "PRIMARY")
        const auxillaryAggregatedMembership = aggregatedMemberships.find(aggregatedMembership => aggregatedMembership.aggregatedBenefitType === "ANCILLARY")
        return new SkinCareBundlePostPurchaseProductPageView(userContext, <DiagnosticProductResponse>(await sellableProductPromise)[0].baseSellableProduct, bookingInfo, issuesMap, newReportIssueManageOption, primaryAggregatedMembership, auxillaryAggregatedMembership)
    }

    getBundleBookingInfo(bookingInfo: BookingDetail): BookingDetail {
        let bundleBookingInfo: BookingDetail
        if (bookingInfo.booking.subCategoryCode === "MP") {
            bundleBookingInfo = bookingInfo.childBookingInfos.find(info => info.booking.subCategoryCode === "MP_OT")
        } else if (bookingInfo.booking.subCategoryCode === "MP_V2") {
            bundleBookingInfo = bookingInfo.childBookingInfos.find(info => info.booking.subCategoryCode === "MP_V2_OT")
        } else {
            bundleBookingInfo = bookingInfo.childBookingInfos.find(info => info.booking.subCategoryCode === "DIAG_PACK_OT" || info.booking.subCategoryCode === "HCU_PACK_OT" || info.booking.subCategoryCode === "AI_MG_OT" || info.booking.subCategoryCode === "GENETICS_PACK_OT")
        }
        return bundleBookingInfo
    }

    async getIssueManageOptions(userContext: UserContext, bookingInfo: BookingDetail): Promise<{
        issuesMap: Map<string, CustomerIssueType[]>,
        newReportIssueManageOption: ManageOptionPayload
    }> {
        let issuesMap: Map<string, CustomerIssueType[]>, newReportIssueManageOption: ManageOptionPayload
        if (AppUtil.isNewReportIssueSupported(userContext)) {
            let issues: IssueDetailView[]
            if (bookingInfo.booking.subCategoryCode === "MIND_THERAPY") {
                issues = await this.issueBusiness.getTherapyIssues(bookingInfo.booking)
            } else if (bookingInfo.booking.subCategoryCode === "DIAG_PACK") {
                issues = await this.issueBusiness.getScreeningPacksIssue(bookingInfo, userContext)
            } else if (bookingInfo.booking.subCategoryCode === "HCU_PACK") {
                issues = await this.issueBusiness.getHCUPacksIssue(bookingInfo, userContext)
            } else if (bookingInfo.booking.subCategoryCode === "PERSONAL_TRAINING") {
                issues = await this.issueBusiness.getPersonalTrainingPacksIssue(bookingInfo, userContext)
            } else if (bookingInfo.booking.subCategoryCode === "PHYSIOTHERAPY") {
                issues = await this.issueBusiness.getPhysiotherapyPacksIssue(bookingInfo, userContext)
            } else if (CareUtil.isPartOfSkinProducts(bookingInfo.booking.subCategoryCode)) {
                issues = await this.issueBusiness.getSkinPackIssues(bookingInfo, userContext)
            } else if (bookingInfo.booking.subCategoryCode === "NUTRITIONIST") {
                issues = await this.issueBusiness.getLCPacksIssue(bookingInfo, userContext)
            } else if (bookingInfo.booking.subCategoryCode === "LIVE_PERSONAL_TRAINING") {
                issues = await this.issueBusiness.getLivePersonalTrainingPacksIssue(bookingInfo, userContext)
            } else if (CareUtil.isTransformTenant(bookingInfo.booking.subCategoryCode )) {
                issues = await this.issueBusiness.getTransformPackProductIssues(bookingInfo, userContext)
            } else if (CareUtil.isPartOfConsultationPackProducts(bookingInfo.booking.subCategoryCode)) {
                issues = await this.issueBusiness.getConsultationPackIssues(bookingInfo, userContext)
            } else {
                issues = await this.issueBusiness.getManagedPlanIssue(bookingInfo, userContext)
            }
            const isReportIssueTextChange = !["PERSONAL_TRAINING", "LIVE_PERSONAL_TRAINING", ].includes(bookingInfo.booking.subCategoryCode)
            newReportIssueManageOption = this.issueBusiness.toManageOptionPayload(issues, true, isReportIssueTextChange ? "Need Help" : "Report Issue")
        } else {
            issuesMap = await this.CRMIssueService.getIssuesMap()
        }
        return { issuesMap, newReportIssueManageOption }
    }

    async getConsultationPrePurchaseProductPage(
        userContext: UserContext,
        isNotLoggedIn: boolean,
        userId: string,
        deviceId: string,
        productId: string,
        subCategoryCode: SUB_CATEGORY_CODE,
        selectedProductId?: string,
        setCode?: string,
        clubCode?: string
    ) {
        const selectedProduct = selectedProductId || productId
        let allClubbedProducts: DiagnosticProductResponse[] = [],
            doctorsList: Doctor[],
            allProductsCombinedResponse: { diagnosticProduct: DiagnosticProduct, product: DiagnosticProductResponse }[] = []
        const baseProduct = <DiagnosticProduct>await this.catalogueService.getProduct(selectedProduct)
        const patientsListPromise: Promise<Patient[]> = this.healthfaceService.getAllPatients(userId)
        const tenant: HealthfaceTenant = CareUtil.getHealthfaceTenant(subCategoryCode)
        const productFullDetails = await this.healthfaceService.getProductInfoDetailsCached("BUNDLE", subCategoryCode, selectedProduct, CareUtil.getHealthfaceTenant(subCategoryCode))
        const packageProduct = <DiagnosticProductResponse>(productFullDetails[0].baseSellableProduct)
        const childProductCodes = packageProduct.childProducts.map(childProduct => childProduct?.baseSellableProduct?.consultationProduct?.productCode).filter(Boolean)
        const doctorTypes = packageProduct.childProducts.map(childProduct => childProduct?.baseSellableProduct?.consultationProduct?.doctorType).filter(Boolean)
        if (!_.isEmpty(childProductCodes)) {
            const doctorsListPromise = _.map(childProductCodes, async productCode => {
                return await this.ollivanderService.getAllDoctorsWithBasicDetailsBasedOnProductCode(productCode)
            })
            doctorsList = _.flatMap(await Promise.all(doctorsListPromise))
        }
        allClubbedProducts = await this.healthfaceService.browseProducts("BUNDLE", subCategoryCode, tenant, true, setCode || packageProduct.setCode, clubCode || packageProduct.clubCode, false, undefined, true)
        allClubbedProducts = allClubbedProducts.filter(product => product.userFacing === true)
        allProductsCombinedResponse = allClubbedProducts.map(product => {
            return {
                diagnosticProduct: CareUtil.toDiagnosticsProduct(product),
                product: <DiagnosticProductResponse>product
            }
        })
        const offers = CareWidgetUtil.getCareProductOffersFromUserContext(userContext, "BUNDLE", !_.isEmpty(allClubbedProducts) ? allClubbedProducts.map(product => product.productCode) : [baseProduct.productId], AppUtil.callSourceFromContext(userContext), this.serviceInterfaces, true)
        return new ConsultationPackPrePurchaseProductView(
            userContext,
            isNotLoggedIn,
            baseProduct,
            allProductsCombinedResponse,
            packageProduct,
            doctorTypes,
            doctorsList || [],
            await patientsListPromise,
            await offers
        )
    }

    async getConsultationPostPurchaseProductPage(
        userContext: UserContext,
        deviceId: string,
        userId: string,
        bookingInfo: BookingDetail,
        baseProduct: DiagnosticProduct,
        healthfaceTenant: HealthfaceTenant,
        issuesMap: Map<string, CustomerIssueType[]>,
        newReportIssueManageOption: ManageOptionPayload,
    ): Promise<ProductDetailPage> {
        const sellableProductPromise = this.healthfaceService.getProductInfoDetailsCached("BUNDLE", bookingInfo.booking.subCategoryCode, bookingInfo.booking.productCode, healthfaceTenant)
        const packageProduct = <DiagnosticProductResponse>(await sellableProductPromise)[0].baseSellableProduct
        let aggregatedMemberships = await this.healthfaceService.getAggregatedMembershipsByRootBookingId(bookingInfo.booking.id, bookingInfo.booking.patientId, "CONSULTATION", undefined, healthfaceTenant)
        const isCovidHomePack = CareUtil.isPartOfConsultationCovidPackProducts(packageProduct.setCode)
        const covidVitalMetricInfoPromise = isCovidHomePack ? this.healthfaceService.getCovidVitalMetricData(Number(userId), bookingInfo.booking.patientId) : undefined
        const careTeamPromise = isCovidHomePack ? this.healthfaceService.getYourTeam(userId, bookingInfo.booking.patientId, bookingInfo.booking.productCode) : undefined
        if (!_.isEmpty(aggregatedMemberships)) {
            const aggregatedMembershipPromises = _.map(aggregatedMemberships, async (aggregatedMembership) => {
                const userMembershipInfosPromises = _.map(aggregatedMembership.userMembershipInfos, async (userMembershipInfo) => {
                    const product = <ConsultationProduct>(await this.catalogueService.getProduct(userMembershipInfo.productCode))
                    userMembershipInfo.productDetail = product
                    userMembershipInfo.consultationInstruction = []
                    return userMembershipInfo
                })
                aggregatedMembership.userMembershipInfos = await Promise.all(userMembershipInfosPromises)
                return aggregatedMembership
            })
            aggregatedMemberships = await Promise.all(aggregatedMembershipPromises)
        }
        return isCovidHomePack
            ? new ConsultationPackPostPurchaseProductPage().buildCovidHomeView(
                userContext,
                packageProduct,
                bookingInfo,
                issuesMap,
                newReportIssueManageOption,
                aggregatedMemberships.filter(aggregatedMembership => aggregatedMembership.aggregatedBenefitType === "PRIMARY"),
                covidVitalMetricInfoPromise ? await covidVitalMetricInfoPromise : undefined,
                careTeamPromise ? await careTeamPromise : undefined
            )
            : new ConsultationPackPostPurchaseProductPage().buildView(
                userContext,
                packageProduct,
                bookingInfo,
                issuesMap,
                newReportIssueManageOption,
                aggregatedMemberships.filter(aggregatedMembership => aggregatedMembership.aggregatedBenefitType === "PRIMARY")
            )
    }

    async getCovidHomePrePurchaseProductPage(
        userContext: UserContext,
        isNotLoggedIn: boolean,
        userId: string,
        deviceId: string,
        productId: string,
        subCategoryCode: SUB_CATEGORY_CODE,
        selectedProductId?: string,
        setCode?: string,
        clubCode?: string
    ) {
        selectedProductId = selectedProductId || productId
        const baseProduct = <DiagnosticProduct>await this.catalogueService.getProduct(selectedProductId)
        const tenant: HealthfaceTenant = CareUtil.getHealthfaceTenant(subCategoryCode)
        const patientsListPromise: Promise<Patient[]> = this.healthfaceService.getAllPatients(userId)
        const allProductsPromise: Promise<DiagnosticProductResponse[]> = this.healthfaceService.browseProducts("BUNDLE", subCategoryCode, tenant, true, setCode, clubCode, false, undefined, true)
        const offerPromise = (async () => {
            const allProducts = await allProductsPromise
            return CareWidgetUtil.getCareProductOffersFromUserContext(
                userContext,
                "BUNDLE",
                !_.isEmpty(allProducts) ? allProducts.map(product => product.productCode) : [baseProduct.productId],
                AppUtil.callSourceFromContext(userContext),
                this.serviceInterfaces,
                true
            )
        })()
        const doctorsPromise = (async () => {
            const bundleProducts = await allProductsPromise
            const selectedBundleProduct: DiagnosticProductResponse = bundleProducts.find(product => product.productCode === baseProduct.productId)
            if (_.get(selectedBundleProduct, "infoSection.doctorInfo.0.productCode")) {
                return this.ollivanderService.getAllDoctorsWithBasicDetailsBasedOnProductCode(selectedBundleProduct.infoSection.doctorInfo[0].productCode)
            }
            return undefined
        })()
        return new CovidHomeMonitoringPrePurchasePage().buildView(
            userContext,
            baseProduct,
            { patientsListPromise, allProductsPromise, offerPromise, doctorsPromise }
        )
    }

}

export default ManagedPlanProductPageViewBuilder
