import { UserContext } from "@curefit/userinfo-common"
import * as _ from "lodash"
import {
    CenterSelectionWidget,
    DoctorDetailWidget,
    ProductDetailPage,
    SearchListingWidget,
    SearchWidget,
    WeeklySchedule,
    WidgetView,
    DoctorDetailWidgetV2
} from "../common/views/WidgetView"
import { ConsultationProduct, Doctor, DOCTOR_TYPE, Patient, SUB_CATEGORY_CODE, Center } from "@curefit/care-common"
import { SPECIALITY_CODE } from "@curefit/albus-client"
import { capitalizeFirstLetter, TimeUtil } from "@curefit/util-common"
import { CareUtil } from "../util/CareUtil"
import { ActionUtil } from "@curefit/base-utils"
import * as momentTz from "moment-timezone"
import { WidgetWithMetric } from "@curefit/vm-common"
import { CareDoctorSearchPageParams, IDoctorAvailableSlot } from "./CareDoctorSearchPageView"
import { OfferItem } from "@curefit/vm-models"
import { Action } from "@curefit/apps-common"
import AppUtil from "../util/AppUtil"

class DoctorListingPageView extends ProductDetailPage {


    constructor(doctors: Doctor[], doctorType: DOCTOR_TYPE, specialityCode: SPECIALITY_CODE, patientId: number, productId: string, parentBookingId: number) {
        super()
        doctors.forEach(doctor => {
            this.widgets.push(
                this.getDoctorDetailWidget(doctor, doctorType, specialityCode, patientId, productId, parentBookingId)
            )
        })
    }

    getDoctorDetailWidget(doctor: Doctor, doctorType: DOCTOR_TYPE, specialityCode: SPECIALITY_CODE, patientId: number, productId: string, parentBookingId: number): DoctorDetailWidget {
        const weeklySchedule: WeeklySchedule[] = []
        doctor.weeklyScheduleByCenter.map(weeklyScheduleByCenter => {
            weeklySchedule.push({
                action: `curefit://selectCareDateV1?productId=${productId}&isExternal=${weeklyScheduleByCenter.center.isExternal}&doctorType=${doctorType}&specialityCode=${specialityCode}&patientId=${patientId}&parentBookingId=${parentBookingId}&doctorId=${doctor.id}&centerId=${weeklyScheduleByCenter.center.id}`,
                centerName: weeklyScheduleByCenter.center.name,
                online: weeklyScheduleByCenter.availableOnline,
                offline: weeklyScheduleByCenter.availableOffline,
                days: (_.keys(weeklyScheduleByCenter.dayOfWeekSchedule)).map(day => capitalizeFirstLetter(day.substring(0, 3)))
            })
        })
        return {
            widgetType: "DOCTOR_DETAIL_WIDGET",
            displayImage: doctor.displayImage,
            name: `Dr ${doctor.name}`,
            qualification: doctor.qualification,
            id: doctor.id,
            detailedQualification: doctor.detailedQualification,
            description: doctor.description,
            experience: `${doctor.experience} yrs of experience`,
            weeklySchedule: weeklySchedule,
            action: {
                actionType: "SHOW_DOCTOR_DETAILS_MODAL"
            }
        }
    }

}

class DoctorListingPageViewV1 extends ProductDetailPage {

    constructor(
        userContext: UserContext,
        product: ConsultationProduct,
        doctors: Doctor[],
        doctorType: DOCTOR_TYPE,
        patientList: Patient[],
        productId: string,
        showSearchWidget: boolean,
        patientId?: number,
        parentBookingId?: number,
        isReschedule?: boolean,
        replacePage?: boolean,
        showCenterSelection?: boolean,
        centerId?: number,
        isDoctorSearch?: boolean,
        isFiltersUISupported?: boolean,
        widgetParams?: CareDoctorSearchPageParams
    ) {
        super()
        this.title = `Find a ${CareUtil.getDoctorText(product.doctorType)}`
        const selfPatient = (CareUtil.isMindDoctorType(product.doctorType) || CareUtil.isPTDoctorType(product.doctorType)) && !_.isEmpty(patientList) ? _.find(patientList, patient => patient.relationship === "Self") : undefined
        widgetParams.patientId = patientId || widgetParams.patientId || (selfPatient ? selfPatient.id : undefined)
        patientId = widgetParams.patientId
        if (showSearchWidget && CareUtil.isPTDoctorType(doctorType)) {
            this.widgets.push(this.getSearchWidget(userContext, product, patientList))
        }
        if (showCenterSelection && centerId && patientId && CareUtil.isMindDoctorType(doctorType)) {
            let centerName: string
            doctors.forEach(doctor => {
                const centerMapping = doctor.doctorCenterMapping.find(item => item.centerId === centerId)
                if (!centerName && centerMapping) {
                    centerName = centerMapping.centerName
                }
            })
            if (centerName) {
                this.widgets.push(this.getCenterSelectionWidget(product, centerName, patientId))
            }
        }
        const list: any[] = doctors.map(doctor => {
            return isFiltersUISupported && !CareUtil.isPTDoctorType(doctorType)
                ? getDoctorDetailWidgetV2(userContext, doctor, widgetParams)
                : this.getDoctorDetailWidget(userContext, doctor, product, productId, patientList, patientId, parentBookingId, isReschedule, replacePage, isDoctorSearch)
        })
        this.widgets.push(this.getSearchListingWidget(userContext, doctorType, list, "DOCTOR", product, patientList, showSearchWidget, showCenterSelection, isDoctorSearch, isFiltersUISupported, widgetParams))
    }

    getSearchWidget(userContext: UserContext, product: ConsultationProduct, patientList: Patient[]): SearchWidget {
        return {
            widgetType: "SEARCH_WIDGET",
            type: "TRAINER",
            title: "Assign me a trainer",
            subTitle: "We will assign you a personal trainer on your first booking.",
            actionText: "Continue",
            containerStyling: {
                backgroundColor: "white" // "#f1f4f7"
            },
            action: CareUtil.getBundleSessionPurchaseAction(
                userContext,
                product,
                patientList,
                {
                    actionType: "NAVIGATION",
                    title: "Continue",
                    url: ActionUtil.selectCareDateV1(product.productId, undefined, undefined)
                },
                "Continue",
                undefined
            ),
            hasDividerBelow: false
        }
    }

    getCenterSelectionWidget(product: ConsultationProduct, centerName: string, patientId?: number): CenterSelectionWidget {
        return {
            widgetType: "CENTER_PICKER_WIDGET",
            title: centerName,
            action: {
                actionType: "NAVIGATION",
                url: `curefit://selectcarecenter?productId=${product.productId}${patientId ? `&patientId=${patientId}` : ""}`
            },
            isHeaderStyling: true,
            canChangeCenter: true,
            hasDividerBelow: true
        }
    }

    getSearchListingWidget(
        userContext: UserContext,
        doctorType: DOCTOR_TYPE,
        list: any[],
        listType: string,
        product: ConsultationProduct,
        patientsList: Patient[],
        showSearchWidget?: boolean,
        showCenterSelection?: boolean,
        isDoctorSearch?: boolean,
        isFiltersUISupported?: boolean,
        widgetParams?: CareDoctorSearchPageParams
    ): SearchListingWidget {
        const isMindDoctorType = CareUtil.isMindDoctorType(doctorType)
        const showFilterUI = isMindDoctorType && isFiltersUISupported
        isDoctorSearch = showFilterUI || isDoctorSearch
        if (showFilterUI) {
            const doctorText = CareUtil.getDoctorText(doctorType)
            return {
                widgetType: "SEARCH_LISTING_WIDGET",
                title: undefined,
                listingTitle: CareUtil.getDoctorListingTitle(doctorType),
                list: list.sort(a => {
                    return a.isPreferredDoctor ? -1 : 1
                }),
                searchResultText: `Available ${doctorText}s`,
                listType: listType,
                placeholder: ` Search by name`,
                showMoreShadow: false,
                showFilter: true,
                autoFocus: false,
                isUnderline: true,
                customSearchEnabled: true,
                emptySearchText: "No search results",
                emptySearchImage: undefined, // "/image/singles/empty_search.png",
                showResultForEmptySearch: true,
                scrollToTopOnFocus: true,
                orientation: userContext.sessionInfo.userAgent === "DESKTOP" ? "RIGHT" : undefined,
                filterModalItems: CareUtil.getDoctorSearchFilterModalItems(userContext, doctorType, widgetParams.filtersData, widgetParams.parentBookingId),
                selectedFilterItems: widgetParams.selectedFilterItems,
                doctorCardFooterAction: CareUtil.getDoctorFooterCardAction(userContext, product, patientsList, widgetParams),
                ...CareUtil.getDoctorListingPaginationViewMoreCount(doctorType)
            }
        }

        return {
            widgetType: "SEARCH_LISTING_WIDGET",
            title: showCenterSelection ? "OUR RECOMMENDATIONS" : showSearchWidget ? `Or select a ${CareUtil.getDoctorText(doctorType)}` : undefined,
            list: list,
            searchResultText: ((CareUtil.isPTDoctorType(doctorType) || showCenterSelection) ? undefined : (isDoctorSearch ? `Available ${CareUtil.getDoctorText(doctorType)}s` : "Search Results")),
            listType: listType,
            placeholder: ` Search by name`,
            showMoreShadow: showSearchWidget ? true : undefined,
            showFilter: showCenterSelection ? false : true,
            autoFocus: isMindDoctorType ? true : false,
            isUnderline: (isMindDoctorType || isDoctorSearch) ? true : false,
            customSearchEnabled: true,
            emptySearchText: "No Search Results",
            emptySearchImage: undefined, // "/image/singles/empty_search.png",
            showResultForEmptySearch: true, // !showCenterSelection && isMindDoctorType ? false : true,
            orientation: userContext.sessionInfo.userAgent === "DESKTOP" ? "RIGHT" : undefined,
        }
    }

    private getDoctorDetailWidget(userContext: UserContext, doctor: Doctor, product: ConsultationProduct, productId: string, patientsList: Patient[], patientId?: number, parentBookingId?: number, isReschedule?: boolean, replacePage?: boolean, isDoctorSearch?: boolean): DoctorDetailWidget {
        const timezone = userContext.userProfile.timezone
        const length = doctor.doctorCenterMapping && doctor.doctorCenterMapping.length
        let actionText, centerName, action
        if (doctor.earliestAvailability) {
            actionText = momentTz.tz(momentTz.tz(doctor.earliestAvailability.startTime, timezone), timezone).calendar(null, {
                lastDay: "[]",
                sameDay: userContext.sessionInfo.userAgent === "APP" ? "[Available\n] hh:mm A" : "[Available] hh:mm A",
                nextDay: "[Available Tomorrow]",
                lastWeek: "[Available] DD MMM",
                nextWeek: "[Available] DD MMM",
                sameElse: "[Available] DD MMM"
            })
            if (doctor.earliestAvailability.centerId ) {
                const doctorCenterMappingItem = doctor.doctorCenterMapping.find(item => item.centerId === doctor.earliestAvailability.centerId)
                if (doctorCenterMappingItem) {
                    centerName = doctorCenterMappingItem.centerName
                }
            }
        }
        if (!centerName) {
            centerName = length ? doctor.doctorCenterMapping[0].centerName : undefined
        }
        const centerId = _.get(doctor, "earliestAvailability.centerId", _.get(doctor, "doctorCenterMapping[0].centerId", undefined))
        if (CareUtil.isPTDoctorType(product.doctorType)) {
            action = CareUtil.consultationCheckoutAction(userContext, product, patientsList, undefined, doctor.id, patientId, centerId, parentBookingId, isReschedule)
            action.url = `${action.url}${replacePage ? "&replacePage=true&popLevel=1" : ""}`
        }
        return {
            widgetType: "DOCTOR_DETAIL_WIDGET",
            displayImage: doctor.displayImage,
            name: (userContext.sessionInfo.userAgent === "APP" && doctor.name.length > 24)
                ? (doctor.name.substring(0, 24) + "...")
                : doctor.name,
            qualification: userContext.sessionInfo.userAgent === "APP" && doctor.qualification && doctor.qualification.length > 28
                ? (doctor.qualification.substring(0, 28) + "...")
                : doctor.qualification,
            id: doctor.id,
            detailedQualification: doctor.detailedQualification,
            description: doctor.description,
            experience: CareUtil.isMindDoctorType(product.doctorType)
                ? undefined
                : (doctor?.experience ? `${doctor.experience} yrs of experience ` : undefined),
            action: action ? action : CareUtil.getDoctorBookingScreenAction("Book your appointment",
                {
                    doctorId: doctor.id,
                    slugValue: doctor.seoSlugValue,
                    productId: productId,
                    patientId,
                    parentBookingId,
                    doctorType: product?.doctorType || doctor?.primarySubServiceType?.code,
                    isReschedule: isReschedule ? "true" : undefined,
                    isDoctorSearch: isDoctorSearch ? "true" : undefined,
                    replacePage : replacePage ? "true&popLevel=2" : undefined,
                    appsource: userContext.sessionInfo.appSource
                }, userContext),
            footerDetails: length ? (
                length > 1
                    ? {
                        name: centerName ? (centerName + (length > 1 ? (userContext.sessionInfo.userAgent === "APP" ? "\n" : " | ") + `+${String(length - 1)} more` : "")) : undefined,
                        actionText: actionText
                    }
                    : length === 1
                    ? {
                        name: centerName || undefined,
                        actionText: actionText
                    } : {
                        name: undefined,
                        actionText: actionText
                    }
            ) : {
                name: undefined,
                actionText: actionText
            },
            fallsInExploreBucket: _.get(doctor, "fallsInExploreBucket", false)
        }
    }
}

class DoctorRecommendationPageView extends ProductDetailPage {

    constructor(
        userContext: UserContext,
        doctors: Doctor[],
        subCategoryCode: SUB_CATEGORY_CODE,
        centersList: any,
        patientId: number,
        centerId: number,
        patientsList: Patient[],
        product: ConsultationProduct,
        doctorTypes?: DOCTOR_TYPE[],
        parentBookingId?: number
    ) {
        super()
        const widgetParams = { productId: product?.productId, parentBookingId, patientId }
        const selfPatient = CareUtil.isMindDoctorType(product?.doctorType) && !_.isEmpty(patientsList) ? _.find(patientsList, patient => patient.relationship === "Self") : undefined
        widgetParams.patientId = widgetParams.patientId || (selfPatient ? selfPatient.id : undefined)
        const isTherapistRecommendationV2UISupported = CareUtil.isMindDoctorType(product?.doctorType) && AppUtil.isTherapistRecommendationV2UISupported(userContext)
        if (isTherapistRecommendationV2UISupported) {
            this.widgets.push(this.getSearchWidget(userContext, product, doctorTypes ? doctorTypes[0] : undefined, parentBookingId))
        }
        if (centersList && centersList[centerId]) {
            this.widgets.push(this.getCenterSelectionWidget(subCategoryCode, centersList[centerId], patientId, doctorTypes))
        }
        const list: any[] = doctors.map(doctor => {
            return isTherapistRecommendationV2UISupported
                ? getDoctorDetailWidgetV2(userContext, doctor, widgetParams, false)
                : this.getDoctorDetailWidget(userContext, doctor, doctor.doctorTypes[0].type.code, centersList, patientId, parentBookingId, product?.productId)
        })
        this.widgets.push(this.getSearchListingWidget(list, "DOCTOR", isTherapistRecommendationV2UISupported ? CareUtil.getDoctorFooterCardAction(userContext, product, patientsList, widgetParams) : undefined))
    }

    getCenterSelectionWidget(subCategoryCode: SUB_CATEGORY_CODE, centerDetails: any, patientId: number, doctorTypes?: DOCTOR_TYPE[]): CenterSelectionWidget {
        const queryParams = {
            subCategoryCode: subCategoryCode,
            fromRecommendation: true,
            patientId: patientId,
            doctorTypes: !_.isEmpty(doctorTypes) ? doctorTypes.join(",") : undefined
        }
        return {
            widgetType: "CENTER_PICKER_WIDGET",
            title: centerDetails.name,
            action: {
                actionType: "NAVIGATION",
                url: `curefit://selectcarecenter${ActionUtil.serializeAsQueryParams(queryParams)}`
            },
            isHeaderStyling: true,
            canChangeCenter: true,
            hasDividerBelow: false
        }
    }

    private getSearchWidget(userContext: UserContext, product: ConsultationProduct, doctorType: DOCTOR_TYPE, parentBookingId?: number): WidgetView {
        const action = CareUtil.specialistListingAction(userContext, product, false, undefined, parentBookingId, undefined, undefined, true, "#FDFDFD,#EEF2F5")
        return {
            widgetType: "SEARCH_WIDGET",
            type: "DOCTOR",
            placeHolder: "Search by name",
            action: {
                ...action,
                meta: CareUtil.isMindDoctorType(doctorType) ? {
                    name: `Find a Therapist`
                } : {}
            },
            hasDividerBelow: true,
            dividerType: "OR"
        }
    }

    private getDoctorDetailWidget(userContext: UserContext, doctor: Doctor, doctorType: string, centers: any, patientId: number, parentBookingId?: number, productId?: string): DoctorDetailWidget {
        const length = doctor.doctorCenterMapping && doctor.doctorCenterMapping.length
        let actionText, centerName
        if (doctor.earliestAvailability) {
            actionText = momentTz.tz(momentTz.tz(doctor.earliestAvailability.startTime, userContext.userProfile.timezone), userContext.userProfile.timezone).calendar(null, {
                lastDay: "[Available Yesterday]",
                sameDay: "[]",
                nextDay: "[Available Tomorrow]",
                lastWeek: "[Available] DD MMM",
                nextWeek: "[Available] DD MMM",
                sameElse: "[Available] DD MMM"
            })
            if (doctor.earliestAvailability.centerId) {
                const doctorCenterMappingItem = doctor.doctorCenterMapping.find(item => item.centerId === doctor.earliestAvailability.centerId)
                if (doctorCenterMappingItem) {
                    centerName = doctorCenterMappingItem.centerName
                }
            }
        }
        if (!centerName) {
            centerName = length ? doctor.doctorCenterMapping[0].centerName : undefined
        }
        return {
            widgetType: "DOCTOR_DETAIL_WIDGET",
            displayImage: doctor.displayImage,
            name: doctor.name,
            qualification: doctor.qualification,
            id: doctor.id,
            detailedQualification: doctor.detailedQualification,
            description: doctor.description,
            experience: CareUtil.isMindDoctorType(doctorType) ? undefined : `${doctor?.experience} yrs of experience`,
            action: CareUtil.getDoctorBookingScreenAction(
                "Book your appointment",
                {
                    doctorId: doctor.id,
                    slugValue: doctor.seoSlugValue,
                    patientId,
                    parentBookingId,
                    productId,
                    doctorType: doctor?.primarySubServiceType?.code,
                    appsource: userContext.sessionInfo.appSource
                },
                userContext
            ),
            footerDetails: length ? (
                length > 1
                    ? {
                        name: centerName ? (centerName + (length > 1 ? ` | +${String(length - 1)} more` : "")) : undefined,
                        actionText: actionText
                    }
                    : length === 1
                    ? {
                        name: centerName || undefined,
                        actionText: actionText
                    } : undefined
            ) : undefined,
        }
    }

    getSearchListingWidget(
        list: any[],
        listType: string,
        doctorCardFooterAction?: Action): SearchListingWidget {
        return {
            widgetType: "SEARCH_LISTING_WIDGET",
            title: "OUR RECOMMENDATIONS",
            list: list,
            searchResultText: undefined,
            listType: listType,
            placeholder: "",
            showMoreShadow: true,
            showFilter: false,
            autoFocus: true,
            isUnderline: false,
            customSearchEnabled: true,
            emptySearchText: "No Search Results",
            emptySearchImage: undefined, // "/image/singles/empty_search.png",
            showResultForEmptySearch: true,
            doctorCardFooterAction
        }
    }
}

function getDoctorDetailWidgetV2(
    userContext: UserContext,
    doctor: Doctor,
    params: CareDoctorSearchPageParams,
    hidePreferredDoctorInfo?: boolean
): DoctorDetailWidgetV2 {
    const {
        productId,
        patientId,
        parentBookingId,
        isReschedule,
        replacePage,
        isDoctorSearch,
        categoryFilters,
        showPrice
    } = params
    const centerId = _.get(doctor, "earliestAvailability.centerId", _.get(doctor, "doctorCenterMapping[0].centerId", undefined))
    const doctorBookingAction: Action = CareUtil.getDoctorBookingScreenAction(
        "VIEW PROFILE",
        {
            doctorId: doctor.id,
            slugValue: doctor.seoSlugValue,
            productId,
            patientId,
            parentBookingId,
            centerId,
            doctorType: doctor?.primarySubServiceType?.code,
            isReschedule: isReschedule ? "true" : undefined,
            isDoctorSearch: isDoctorSearch ? "true" : undefined,
            replacePage: replacePage ? "true&popLevel=2" : undefined,
            showPrice: showPrice ? true : undefined,
            appsource: userContext.sessionInfo.appSource
        },
        userContext
    ) as Action
    const isNewSlotUISupported = userContext.sessionInfo.appVersion >= 8.27
    const timezone = userContext.userProfile.timezone
    let actionText, isTodayAvailable
    if (doctor.earliestAvailability) {
        actionText = momentTz.tz(momentTz.tz(doctor.earliestAvailability.startTime, timezone), timezone).calendar(null, {
            lastDay: "[]",
            sameDay: () => {
                isTodayAvailable = true
                return isNewSlotUISupported ? "[]" : "[Next available at] hh:mm A"
            },
            nextDay: "[Next available tomorrow]",
            lastWeek: "[Next available] DD MMM",
            nextWeek: "[Next available] DD MMM",
            sameElse: "[Available] DD MMM"
        })
    } else {
        actionText = "No slots available"
    }
    let doctorAvailableSlots: IDoctorAvailableSlot[]
    const tz = userContext.userProfile.timezone
    const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDateWithTimezone(tz), tz)
    if (isTodayAvailable && isNewSlotUISupported && !_.isEmpty(doctor.earliestAvailabilityList)) {
        const earliestAvailabilityList = doctor.earliestAvailabilityList.slice(0, 2)
        doctorAvailableSlots = _.map(earliestAvailabilityList, slot => {
            const slotDate = TimeUtil.getDefaultMomentForDateString(TimeUtil.formatEpochInTimeZone(tz, Number(slot.startTime), "D MMM YYYY"), tz)
            const doctorCenterMappingItem = doctor.doctorCenterMapping.find(item => item.centerId === centerId)
            if (slotDate.isSame(today, "day")) {
                return {
                    title: TimeUtil.formatEpochInTimeZone(tz, slot.startTime, "h:mm A"),
                    startTime: slot.startTime,
                    endTime: slot.endTime,
                    centerId: slot.centerId,
                    action: userContext.sessionInfo.isUserLoggedIn ? {
                        actionType: "NAVIGATION",
                        url: `curefit://carecheckout?productId=${productId}&parentBookingId=${parentBookingId}&centerId=${slot.centerId}${patientId ? `&patientId=${patientId}` : ""}`,
                        meta: {
                            selectedSlot: {
                                ...slot,
                                doctorIdList: [doctor.id],
                                listingCode: doctorCenterMappingItem && doctorCenterMappingItem.listingCode
                            },
                            productId: productId,
                            patientId: patientId,
                            parentBookingId: parentBookingId,
                            doctorId: doctor.id,
                            centerId: slot.centerId,
                            listingCode: doctorCenterMappingItem && doctorCenterMappingItem.listingCode
                        }
                    } : CareUtil.getLoginAlertAction() as Action
                }
            }
            return null
        })
        doctorAvailableSlots = doctorAvailableSlots.filter(Boolean)
    }
    return {
        widgetType: "DOCTOR_DETAIL_WIDGET_V2",
        displayImage: doctor.displayImage,
        earliestAvailability: doctor.earliestAvailability,
        earliestAvailabilityList: doctor.earliestAvailabilityList,
        isPreferredDoctor: hidePreferredDoctorInfo ? false : doctor.preferredDoctor,
        name: doctor.name,
        qualification: doctor.qualification ? doctor.qualification : undefined,
        id: doctor.id,
        price: undefined,
        priceInfo: undefined,
        detailedQualification: doctor.detailedQualification,
        description: doctor.description,
        experience: undefined,
        action: doctorBookingAction,
        doctorAvailableSlots,
        isFooterDisabled: !doctor.earliestAvailability,
        footerDetails: { name: undefined , actionText: actionText},
        subSpecialities: CareUtil.getDoctorSubSpecialityValue(categoryFilters, doctor) as string,
        actionQueryParam: `&doctorId=${doctor.id}&centerId=${centerId}${replacePage ? "&replacePage=true&popLevel=1" : ""}`,
        fallsInExploreBucket: _.get(doctor, "fallsInExploreBucket", false)
    }
}

export { DoctorListingPageView, DoctorListingPageViewV1, DoctorRecommendationPageView }
