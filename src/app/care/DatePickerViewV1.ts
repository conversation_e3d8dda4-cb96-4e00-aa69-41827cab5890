import * as _ from "lodash"
import { CareDatePickerView, PageWidget } from "../page/Page"
import { CATEGORY_CODE, Center, DiagnosticAvailableSlotsResponse, DiagnosticSlot, Seller } from "@curefit/albus-client"
import { CalloutPageWidget, DatesAvailableWidget, DateWiseSlots, TimeSlot, TimeSlotCategory, CareFooterInfoWidget } from "../page/PageWidgets"
import { TimeUtil, Timezone } from "@curefit/util-common"
import CareUtil, { FOOTER_WIDGET_DATE_PICKER_SUPPORTED, CARE_FOOTER_INFO_WIDGET_SUPPORTED } from "../util/CareUtil"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { UserContext } from "@curefit/userinfo-common"
import { Action, ActionType } from "../common/views/WidgetView"
import * as momentTz from "moment-timezone"

class DatePickerViewV1 {

    public datePicker: CareDatePickerView
    constructor(
        userContext: UserContext,
        bookingId: string,
        productId: string,
        category: string,
        availableSlots: DiagnosticAvailableSlotsResponse,
        patientId: number,
        categoryCode: CATEGORY_CODE,
        isReschedule: string,
        isMultiCenterSupported: boolean,
        productCodes?: string,
        nextAction?: string,
        offerIds?: string,
        center?: Center,
        hasAtHome?: string,
        isDiagPack?: boolean,
        isBookingFromConsultation?: boolean,
        cartId?: string,
        addressId?: string
    ) {
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "DESKTOP")
        if (category === "IN_CENTRE_SLOT") {
            this.datePicker = this.inCentreDatePickerResponse(
                userContext,
                isMultiCenterSupported,
                bookingId,
                productId,
                category,
                availableSlots,
                patientId,
                categoryCode,
                isReschedule,
                productCodes,
                nextAction,
                offerIds,
                center,
                hasAtHome,
                isDiagPack,
                isBookingFromConsultation
            )
        } else if (category === "AT_HOME_SLOT") {
            this.datePicker = this.atHomeDatePickerResponse(
                userContext,
                bookingId,
                productId,
                category,
                availableSlots,
                patientId,
                categoryCode,
                isReschedule,
                productCodes,
                nextAction,
                offerIds,
                cartId,
                addressId
            )
        }
    }

    private inCentreDatePickerResponse(
        userContext: UserContext,
        isMultiCenterSupported: boolean,
        bookingId: string,
        productId: string,
        category: string,
        availableSlots: DiagnosticAvailableSlotsResponse,
        patientId: number,
        categoryCode: CATEGORY_CODE,
        isReschedule: string,
        productCodes?: string,
        nextAction?: string,
        offerIds?: string,
        center?: Center,
        hasAtHome?: string,
        isDiagPack?: boolean,
        isBookingFromConsultation?: boolean
    ): CareDatePickerView {

        const incentreWidgets: PageWidget[] = [], footerWidget: PageWidget[] = []
        const checkoutUrl = `curefit://diagnosticscheckout?type=DIAGNOSTICS&productId=${productId}&patientId=${patientId}&parentBookingId=${bookingId}&offerIds=${offerIds}&subCategoryCode=DIAGNOSTICS`
        const rescheduleUrl = `curefit://rescheduleDiagnostics?type=DIAGNOSTICS&productId=${productId}&patientId=${patientId}&parentBookingId=${bookingId}&category=IN_CENTRE_SLOT&offerIds=${offerIds}&&subCategoryCode=DIAGNOSTICS`
        let actionUrl: string = isReschedule === "true" ? rescheduleUrl : checkoutUrl
        if (productCodes) {
            actionUrl = actionUrl + `&productCodes=${productCodes}`
        }
        const isFooterWidgetSupported = userContext.sessionInfo.appVersion >= FOOTER_WIDGET_DATE_PICKER_SUPPORTED
        const datesMap: { [key: string]: DiagnosticSlot[]} = _.groupBy(availableSlots.inCentreSlots, slot => {
            return TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, slot.startTime)
        })
        incentreWidgets.push(this.availableDatesWidget(userContext, datesMap, category, availableSlots, actionUrl, false, !_.isEmpty(availableSlots.inCentreSlots)))
        if (hasAtHome === "true" || isReschedule === "true" || isBookingFromConsultation) {
            const widget = new CalloutPageWidget("LOCATION", center.address, center.name, "VIEW MAP", `curefit://externalDeepLink?placeUrl=${center.placeUrl}`)
            if (isFooterWidgetSupported) {
                footerWidget.push(widget)
            } else {
                incentreWidgets.push(widget)
            }
        }
        else {
            const widget = new CalloutPageWidget("LOCATION",
            isDiagPack
                ? "Not getting slot on preferred day? Go back and try 'All at Home' option for more slots"
                : "Not getting slot on preferred day? Go back and try 'Home + Center' option for better slot availability."
            )
            if (isFooterWidgetSupported) {
                footerWidget.push(widget)
            } else {
                incentreWidgets.push(widget)
            }
        }
        if (isReschedule === "true") { // hack to disable center selection for reschedule
            isMultiCenterSupported = false
        }
        return {
            noSlotsInfo: {
            title: "No slots available",
            subtitle: "Please check for slots on other days",
            imageUrl: "/image/transform/slot_unavailable.png"
        }, key: category,
            userLocation: "bangalore", // TODO check this isWithinBangalore ? "bangalore" : "others",
            header: center ? CareUtil.getDatePickerViewHeader(isMultiCenterSupported, center, "Select centre visit slot") : {
                title: "Select centre visit slot"
            },
            widgets: incentreWidgets,
            footerWidget: footerWidget ? footerWidget : undefined

        }
    }

    private atHomeDatePickerResponse(
        userContext: UserContext,
        bookingId: string,
        productId: string,
        category: string,
        availableSlots: DiagnosticAvailableSlotsResponse,
        patientId: number,
        categoryCode: CATEGORY_CODE,
        isReschedule: string,
        productCodes?: string,
        nextAction?: string,
        offerIds?: string,
        cartId?: string,
        addressId?: string,
    ): CareDatePickerView {

        const atHomeWidgets: PageWidget[] = [], footerWidget: PageWidget[] = []
        const tz = userContext.userProfile.timezone
        const atCentreUrl = `curefit://selectCareDateV1?productId=${productId}&parentBookingId=${bookingId}&type=DIAGNOSTICS&category=IN_CENTRE_SLOT&nextAction=checkout&offerIds=${offerIds}&hasAtHome=true&patientId=${patientId}&subCategoryCode=DIAGNOSTICS`
        const rescheduleUrl = `curefit://rescheduleDiagnostics?productId=${productId}&parentBookingId=${bookingId}&type=DIAGNOSTICS&category=AT_HOME_SLOT&offerIds=${offerIds}&patientId=${patientId}&subCategoryCode=DIAGNOSTICS`
        const checkoutUrl = `curefit://diagnosticscheckout?type=DIAGNOSTICS&productId=${productId}&parentBookingId=${bookingId}&patientId=${patientId}&offerIds=${offerIds}&category=AT_HOME_SLOT&subCategoryCode=DIAGNOSTICS`
        let actionUrl: string, isSlotClubbingAvailable = false
        const checkoutApiParams: any = {}
        const isFooterWidgetSupported = userContext.sessionInfo.appVersion >= FOOTER_WIDGET_DATE_PICKER_SUPPORTED
        const isCareFooterInfoWidgetSupported = userContext.sessionInfo.appVersion >= CARE_FOOTER_INFO_WIDGET_SUPPORTED
        if (nextAction === "checkout") {
            actionUrl = checkoutUrl
        } else if (nextAction === "incentreSlot") {
            actionUrl = atCentreUrl
        } else {
            actionUrl = isReschedule === "true" ? rescheduleUrl : atCentreUrl
        }
        // Handled addressId in url params for chronicCare App because addressId is populated from backend.
        const isChronicCare = CareUtil.getIfChronicCareFromUserContext(userContext)
        if ( addressId && isChronicCare ) {
            actionUrl = actionUrl + `&addressId=${addressId}`
        }
        if (cartId) {
            actionUrl = actionUrl + `&cartId=${cartId}`

            checkoutApiParams.careOptions = {
                diagnosticCartId: cartId
            }
        }
        if (productCodes) {
            actionUrl = actionUrl + `&productCodes=${productCodes}`
        }
        const datesMap: { [key: string]: DiagnosticSlot[]} = _.groupBy(availableSlots.phleboSlots, slot => {
            if (!isSlotClubbingAvailable && slot.bookedForSameAddress) {
                isSlotClubbingAvailable = true
            }
            return TimeUtil.formatEpochInTimeZone(tz, slot.startTime)
        })
        atHomeWidgets.push(this.availableDatesWidget(userContext, datesMap, category, availableSlots, actionUrl, true, !_.isEmpty(availableSlots.phleboSlots), checkoutApiParams))
        if (isFooterWidgetSupported) {
            if (isSlotClubbingAvailable && isCareFooterInfoWidgetSupported) {
                // footerWidget.push(new CareFooterInfoWidget("Recommended Slot", "You have another appointment at this time", undefined, true, "#ff3278"))
                footerWidget.push(new CareFooterInfoWidget("Adding more tests", "To add more tests to the same address and time slot, you can directly tell the phlebotomist. He will add them on the spot", undefined, true, "#ff3278"))
            }
            footerWidget.push(new CalloutPageWidget("LOCATION", "Phlebotomist will arrive in the time slot you select. It usually takes 10-15 mins to collect the samples"))
        } else {
            atHomeWidgets.push(new CalloutPageWidget("LOCATION", "Phlebotomist will arrive in the time slot you select. It usually takes 10-15 mins to collect the samples"))
        }
        return {
            key: category,
            userLocation: "bangalore", // TODO check this isWithinBangalore ? "bangalore" : "others",
            header: {
                title: "Select home visit slot"
            },
            widgets: atHomeWidgets,
            footerWidget: footerWidget ? footerWidget : undefined
        }
    }

    private availableDatesWidget(
        userContext: UserContext,
        datesMap: { [key: string]: DiagnosticSlot[]},
        category: string,
        availableSlotsResponse: DiagnosticAvailableSlotsResponse,
        actionUrl: string,
        showSelected: boolean,
        isSlotsNotEmpty: boolean,
        params: any = {}
    ): DatesAvailableWidget {
        const dates: string[] = TimeUtil.getDays(userContext.userProfile.timezone, 15),
            datesAvailable: DateWiseSlots[] = [],
            slotAvailableDates: string[] = []
        if (isSlotsNotEmpty) {
            for (let i = dates.length - 1 || 0 ; i >= 0 && datesAvailable.length < 15; i--) {
                const dateAppointments: DiagnosticSlot[] = datesMap[dates[i]]
                if (!_.isEmpty(dateAppointments)) {
                    const isSlotAvailableToday = dateAppointments.findIndex(slot => slot.isAvailable) !== -1
                    const isBookedForSameAddress = dateAppointments.findIndex(slot => slot.bookedForSameAddress) !== -1
                    isSlotAvailableToday && slotAvailableDates.unshift(dates[i])
                    const timeslots: TimeSlotCategory[] = this.getHourSplitTimeSlots(dateAppointments, availableSlotsResponse.sellers[0], showSelected, userContext, category === "AT_HOME_SLOT")
                    if (!_.isEmpty(timeslots)) {
                        datesAvailable.unshift(CareUtil.getDatewiseSlot(
                                dates[i],
                                userContext.userProfile.timezone,
                                timeslots,
                                !isSlotAvailableToday,
                                slotAvailableDates,
                                "DIAGNOSTICS",
                                undefined,
                                isBookedForSameAddress ? " " : undefined // Passing empty string to hide the text content
                            )
                        )
                    }
                }
            }
        } else {
            datesAvailable.push(CareUtil.getDatewiseSlot(dates[0], userContext.userProfile.timezone, [], true, [], "DIAGNOSTICS"))
        }
        let actionType: ActionType = "NAVIGATION"
        if (userContext.sessionInfo.userAgent === "DESKTOP" || userContext.sessionInfo.userAgent === "MBROWSER") {
            if (actionUrl.includes("rescheduleDiagnostics")) {
                actionType = "RESCHEDULE_DIAGNOSTICS"
            } else if (actionUrl.includes("diagnosticscheckout")) {
                actionType = "BOOK_DIAGNOSTICS"
            }
        }
        const action: Action = {
            actionType: actionType,
            url: actionUrl,
            meta: {
                params
            }
        }
        return new DatesAvailableWidget(datesAvailable, actionUrl, action)
    }

    private getHourSplitTimeSlots(dateAppointments: DiagnosticSlot[], seller: Seller, showSelected: boolean, userContext: UserContext, isHomeSlot?: boolean): TimeSlotCategory[] {
        const timeslots: TimeSlotCategory[] = []
        const timeSlotsMap = <{[key: string]: DiagnosticSlot[] }>CareUtil.getTimeSlotMap(dateAppointments, userContext.userProfile.timezone)
        if (!_.isEmpty(timeSlotsMap)) {
            Object.keys(timeSlotsMap).sort().map(timeSlotKey => {
                if (!_.isEmpty(timeSlotsMap[timeSlotKey])) {
                    timeslots.push(this.parseToViewFormat(
                        timeSlotKey,
                        timeSlotsMap[timeSlotKey],
                        seller,
                        showSelected,
                        userContext,
                        isHomeSlot
                    ))
                }
            })
        }
        return timeslots
    }

    private parseToViewFormat(timeSlotKey: string, slots: DiagnosticSlot[], seller: Seller, showSelected: boolean, userContext: UserContext, isHomeSlot?: boolean): { timeSlots: TimeSlot[], title: string } {
        const timeSlots: TimeSlot[] = []
        slots.forEach(element => {
            const timeslot: TimeSlot = {
                availableType: element.isAvailable === true ? "AVAILABLE" : "UNAVAILABLE",
                text: this.getTimeText(element, userContext, isHomeSlot),
                startTime: element.startTime,
                endTime: element.endTime,
                meta: {
                    slotId: element.slotId,
                    centreCode: element.centreCode,
                    sellerCode: seller.sellerCode,
                    sellerName: seller.sellerName,
                    sellerType: seller.sellerType
                },
                showSelected: showSelected,
                bookedForSameAddress: element.bookedForSameAddress,
                slotClubbingBulletColor: "#ff3278"
            }
            timeSlots.push(timeslot)
        })
        return {
            timeSlots,
            title: CareUtil.getTimeSlotTitleFromKey(timeSlotKey)
        }
    }

    private getTimeText(element: DiagnosticSlot, userContext: UserContext, isHomeSlot?: boolean) {
        let text
        const totalTimeOfSlot = userContext.userProfile.cityId === "Bangalore" ? 1800000 : 3600000
        if (element && element.startTime) {
            text = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, element.startTime, "hh:mm")
        }
        // Temp hack Adding 30 mins for home slots alone
        if (text && isHomeSlot && element.endTime) {
            text += " - " + TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, element.endTime, "hh:mm")
        }
        return text
    }

}

export default DatePickerViewV1
