import * as _ from "lodash"
import { Action } from "../common/views/WidgetView"
import { IHealthfaceService, BookingDetail, DiagnosticsTestOrderResponse, ALBUS_CLIENT_TYPES } from "@curefit/albus-client"
import { CareMeEmptyListingWidget, ListingActionableCardWidget, SupportActionableCardWidget } from "../page/PageWidgets"
import { ActionUtil } from "@curefit/base-utils"
import { ActionUtil as AppActionUtil } from "../util/ActionUtil"
import { CareUtil } from "../util/CareUtil"
import { PageWidget } from "../page/Page"
import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import IssueBusiness from "../crm/IssueBusiness"
import { SupportListPageView } from "../crm/SupportListPageView"
import { UserContext } from "@curefit/userinfo-common"
import AppUtil from "../util/AppUtil"
import { ISegmentService } from "@curefit/vm-models"

@injectable()
class MeDiagnosticTestListPageViewBuilder {

    constructor(
        @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness,
        @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
        @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService,
    ) {
    }

    async buildMeDiagnosticTestListPageView(userContext: UserContext, diagnosticTests: DiagnosticsTestOrderResponse[], appVersion: number, pageNumber: number, pageFrom?: string): Promise<SupportListPageView> {
        const widgets: PageWidget[] = []
        let actions: Action[]
        if (_.isEmpty(diagnosticTests) && !(await AppUtil.isNewSupportInFlutterSupported(this.segmentService, userContext))) {
            if (pageNumber === 0) {
                widgets.push(new CareMeEmptyListingWidget("DIAGNOSTICS", "No Diagnostic Test Yet!", "Consult our General Physicians & Paediatricians to provide care from common issues to complex illness. We promise on-time consultation - no queues & waiting."))
                actions = [{
                    actionType: "NAVIGATION",
                    url: AppUtil.isWeb(userContext) ? ActionUtil.careFitClp("clphcu") : CareUtil.getLabTestClpListPage(),
                    title: "Book Diagnostic Tests Now"
                }]
            }
        } else if (pageFrom === "support") {
            const cardWidgetPromises: Promise<SupportActionableCardWidget>[] = _.map(diagnosticTests, diagnosticTest => {
                return this.getActionableCardWidgetSupport(userContext, diagnosticTest)
            })
            const cardWidgets: SupportActionableCardWidget[] = await Promise.all(cardWidgetPromises)
            cardWidgets.map(cardWidget => {
                widgets.push(cardWidget)
            })
        } else {
            diagnosticTests.map(diagnosticTest => {
                widgets.push(this.getActionableCardWidget(userContext, diagnosticTest))
            })
        }
        return new SupportListPageView(widgets, actions)
    }

    async buildMeDiagnosticTestListWidgets(userContext: UserContext, diagnosticTests: DiagnosticsTestOrderResponse[], appVersion: number, pageFrom?: string): Promise<SupportActionableCardWidget[]> {
        const widgets: SupportActionableCardWidget[] = []
        if (pageFrom === "support") {
            const cardWidgetPromises: Promise<SupportActionableCardWidget>[] = _.map(diagnosticTests, diagnosticTest => {
                return this.getActionableCardWidgetSupport(userContext, diagnosticTest)
            })
            const cardWidgets: SupportActionableCardWidget[] = await Promise.all(cardWidgetPromises)
            cardWidgets.map(cardWidget => {
                widgets.push(cardWidget)
            })
        } else {
            diagnosticTests.map(diagnosticTest => {
                widgets.push(this.getActionableCardWidget(userContext, diagnosticTest))
            })
        }
        return widgets
    }

    private getActionableCardWidget(userContext: UserContext, diagnosticTest: DiagnosticsTestOrderResponse): ListingActionableCardWidget {
        const testWidgetData = CareUtil.getDiagnosticTestWidget(userContext, diagnosticTest)
        return {
            widgetType: "LISTING_ACTIONABLE_CARD_WIDGET",
            ...testWidgetData
        }
    }

    private async getActionableCardWidgetSupport(userContext: UserContext, diagnosticTest: DiagnosticsTestOrderResponse): Promise<SupportActionableCardWidget> {
        const testWidgetData = CareUtil.getDiagnosticTestWidget(userContext, diagnosticTest)
        const { timestamp, timezone } = CareUtil.getTimestampAndTimezone(userContext, diagnosticTest)
        const bookingDetail: BookingDetail = await this.healthfaceService.getBookingDetail(diagnosticTest.bookingId)
        const reportIssueParams = this.issueBusiness.getDiagnosticTestIssueParams(bookingDetail)
        const time = testWidgetData.footer[0].text
        testWidgetData.cardAction = {
            meta: (await AppUtil.isNewSupportInFlutterSupported(this.segmentService, userContext)) ? {
                title: testWidgetData.title,
                subTitle: "What is your primary concern with the activity"
            } : null,
            actionType: "NAVIGATION",
            url: AppActionUtil.getIssuesUrl()
        }
        if (AppUtil.isWeb(userContext)) {
            testWidgetData.footer = []
        }
        const supportActionableCardWidget: SupportActionableCardWidget = {
            widgetType: "SUPPORT_ACTIONABLE_CARD_WIDGET",
            time: AppUtil.isWeb(userContext) ? time : undefined,
            timestamp: timestamp,
            timezone: timezone,
            ...testWidgetData
        }
        supportActionableCardWidget.imageUrl = "image/<EMAIL>"
        return supportActionableCardWidget
    }
}

export default MeDiagnosticTestListPageViewBuilder
