import { inject, injectable } from "inversify"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import {
    DiagnosticEmailedTestReportItem,
    DiagnosticEmailedTestReportResponse,
    DiagnosticReportV2,
    DiagnosticTestReportDetails,
    TEST_RESULT_TYPE,
    TestDetails
} from "@curefit/albus-client"
import { Action, ProductDetailPage } from "../common/views/WidgetView"
import {
    DiagnosticEmailTestReportItem,
    DiagnosticsEmailTestReportDetailedSummaryWidget,
    DiagnosticsTestReportDetailWidget,
    DiagnosticsTestReportSummaryWidget,
    DiagnosticTestReportCell,
    DiagnosticTestReportItem,
    ReportStatusWidget,
    TestReportDetail
} from "../page/PageWidgets"
import * as _ from "lodash"
import { DiagnosticTestDetailCellType, PageWidget } from "../page/Page"
import CareUtil, { ABNORMAL_STATE_COLOR, NORMAL_STATE_COLOR } from "../util/CareUtil"
import { DiagnosticProduct } from "@curefit/care-common"
import { UserContext } from "@curefit/userinfo-common"

export class DiagnosticReportViewV2 extends ProductDetailPage {
    header?: {
        title: string,
    }

    constructor(widgets: PageWidget[], header?: any) {
        super()
        this.widgets = widgets
        this.header = header
    }
}

@injectable()
class DiagnosticReportViewBuilderV2 {
    constructor(@inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService) {
    }

    async buildDiagnosticReportView(userContext: UserContext, testOrderId: number, reportData: DiagnosticReportV2, testId?: number, diagnosticEmailedReportResponse?: DiagnosticEmailedTestReportResponse): Promise<DiagnosticReportViewV2> {
        let widgets: PageWidget[] = []
        let header: any
        // if testId is present, build test detail report page, otherwise test summary page
        if (_.isNil(testId)) {
            const isNewEmailReportWidgetSupported = CareUtil.isNewEmailedReportSupported(userContext) && diagnosticEmailedReportResponse && !_.isEmpty(diagnosticEmailedReportResponse.productDetails)
            const isNullReport = reportData && reportData.completedTestsDetails && reportData.completedTestsDetails.testCount === 0
            widgets.push(this.getReportStatusWidget(reportData, testOrderId, diagnosticEmailedReportResponse))
            if (!isNullReport) {
                header = {
                    title: "Diagnostic Report",
                    action: `curefit://sendReportOnEmail?testOrderId=${testOrderId}`
                }
                const reportInfoForProfileTests: TestDetails[] = []
                const reportInfoForChildrenTests: TestDetails[] = []
                _.forEach(reportData.completedTestsDetails.testDetails, testDetail => {
                    if (testDetail.testCount === 1 && !_.isEmpty(testDetail.reportDetailsResponse)) {
                        reportInfoForChildrenTests.push(testDetail)
                    } else {
                        reportInfoForProfileTests.push(testDetail)
                    }
                })
                const readyWidget: DiagnosticsTestReportSummaryWidget = CareUtil.getDiagnosticChildReportSummaryWidget(reportInfoForProfileTests, testOrderId)
                readyWidget.noteText = "Test values are to be interpreted by a Physician or prescribing Doctor only"
                readyWidget.widgetTitle = {
                    title: "READY REPORTS"
                }
                widgets.push(readyWidget)
                const childReportWidgets = await this.getChildReportWidgets(reportInfoForChildrenTests)
                widgets = widgets.concat(childReportWidgets)
            } else {
                header = {
                    title: "Diagnostic Report",
                }
            }
            if (isNewEmailReportWidgetSupported) {
                widgets.push(await this.getDiagnosticEmailedTestReportDetailedWidget(diagnosticEmailedReportResponse))
            }
        }
        widgets = _.filter(widgets, widget => !_.isEmpty(widget))
        return new DiagnosticReportViewV2(widgets, header)
    }

    private async getChildReportWidgets(childCheckUpReportInfos: TestDetails[]): Promise<PageWidget[]> {
        const reportDetailWidgetsPromises = _.map(childCheckUpReportInfos, async (reportInfo) => {
            return this.getDiagnosticsTestReportDetailWidget(reportInfo)
        })
        return await Promise.all(reportDetailWidgetsPromises)
    }

    private getReportStatusWidget(reportData: DiagnosticReportV2, testOrderId: number, diagnosticEmailedReportResponse?: DiagnosticEmailedTestReportResponse): ReportStatusWidget {
        const totalCount = (reportData && reportData.completedTestsDetails ? reportData.completedTestsDetails.testCount : 0) +
            (reportData && reportData.inProgressTestsDetails ? reportData.inProgressTestsDetails.testCount : 0) +
            (reportData && reportData.pendingTestsDetails ? reportData.pendingTestsDetails.testCount : 0) +
            (diagnosticEmailedReportResponse && diagnosticEmailedReportResponse.productDetails ? diagnosticEmailedReportResponse.productDetails.length : 0)
        let inProgressTests
        if (reportData && reportData.inProgressTestsDetails && reportData.inProgressTestsDetails.testCount !== 0) {
            const inProgressAction: Action = {
                actionType: "NAVIGATION",
                url: `curefit://infopage?contentId=InProgress${testOrderId}&pageType=LIST`,
                meta: {
                    contentId: "InProgress" + testOrderId,
                    data: CareUtil.getInProgressTestsPage(reportData, testOrderId, totalCount)
                }
            }
            inProgressTests = {
                action: inProgressAction,
                cta: "TRACK",
                count: reportData.inProgressTestsDetails.testCount,
                title: "In Progress",
                subtitle: ""
            }
        }
        let pendingTests
        if (reportData && reportData.pendingTestsDetails && reportData.pendingTestsDetails.testCount !== 0) {
            const pendingAction: Action = {
                actionType: "NAVIGATION",
                url: `curefit://infopage?contentId=Pending${testOrderId}&pageType=LIST`,
                meta: {
                    contentId: "Pending" + testOrderId,
                    data: CareUtil.getPendingTestsPage(reportData, testOrderId, totalCount)
                }
            }
            pendingTests = {
                action: pendingAction,
                cta: "VIEW",
                count: reportData.pendingTestsDetails.testCount,
                title: "Tests Pending",
                subtitle: ""
            }
        }
        const completedCount = (reportData && reportData.completedTestsDetails ? reportData.completedTestsDetails.testCount : 0) + (diagnosticEmailedReportResponse && diagnosticEmailedReportResponse.productDetails ? diagnosticEmailedReportResponse.productDetails.length : 0)
        const completedTitle = completedCount !== 0 ? "REPORTS READY" : "REPORTS ARE\nBEING GENERATED"
        return new ReportStatusWidget(totalCount, completedCount, completedTitle, "COMPLETED", inProgressTests, pendingTests)
    }

    private async getDiagnosticsTestReportDetailWidget(reportInfo: TestDetails): Promise<DiagnosticsTestReportDetailWidget> {
        const reportDetails = reportInfo.reportDetailsResponse
        const testInfo: DiagnosticProduct = <DiagnosticProduct>await this.catalogueService.getProduct(reportInfo.testId)
        if (_.isEmpty(testInfo)) {
            return
        }
        const cellType = this.getReportCellType(reportInfo.reportDetailsResponse.testResultType)
        const cellData = this.getReportCellData(reportInfo.reportDetailsResponse, cellType)
        const testGraphDetails: { testInfos: TestReportDetail[], markerIndex: number } = this.showReportGraph(cellType, reportInfo) && this.getTestReportGraphDetails(reportInfo)
        const reportItem: DiagnosticTestReportItem = {
            header: {
                title: testInfo.title,
                subtitle: testInfo.subTitle,
                // testState: reportInfo.normalCount === 1 ? "Normal" : reportInfo.abnormalCount === 1 ? "Out of range" : null,
                testStateColor: reportInfo.normalCount === 1 ? NORMAL_STATE_COLOR : ABNORMAL_STATE_COLOR
            },
            testResultValue: reportDetails.testNumericalResultValue,
            markerIndex: testGraphDetails.markerIndex,
            testInfos: testGraphDetails.testInfos
        }
        return new DiagnosticsTestReportDetailWidget(reportItem, cellType, cellData)
    }

    private getTestReportGraphDetails(reportInfo: TestDetails): { testInfos: TestReportDetail[], markerIndex: number } {
        const reportDetails = reportInfo.reportDetailsResponse
        const normalLineStartPoint = reportDetails.testMinRangeValue
        const normalLineEndPoint = reportDetails.testMaxRangeValue
        const thresholdValueFrom = reportDetails.thresholdValueFrom >= 0 ? reportDetails.thresholdValueFrom : Number.MAX_VALUE
        const thresholdValueTo = reportDetails.thresholdValueTo >= 0 ? reportDetails.thresholdValueTo : Number.MIN_VALUE
        const testNumericalResultValue = reportDetails.testNumericalResultValue
        const lineStartPoint = _.min([normalLineStartPoint, thresholdValueFrom, testNumericalResultValue])
        const lineEndPoint = _.max([normalLineEndPoint, thresholdValueTo, testNumericalResultValue])
        const testInfos: TestReportDetail[] = []
        const lineDetails = this.getTestReportLineValues(lineStartPoint, lineEndPoint, reportInfo)
        const isNormalResult: boolean = reportInfo.normalCount === 1
        if (lineStartPoint < normalLineStartPoint) {
            testInfos.push({
                lineStartValue: lineStartPoint,
                lineEndValue: normalLineStartPoint,
                stateColor: ABNORMAL_STATE_COLOR,
                lineWidthRatio: lineDetails.abNormalLineWidthRatio
            })
        }
        testInfos.push({
            lineStartValue: normalLineStartPoint,
            lineEndValue: normalLineEndPoint,
            stateColor: NORMAL_STATE_COLOR,
            lineWidthRatio: lineDetails.normalLineWidthRatio
        })
        if (lineEndPoint > normalLineEndPoint) {
            testInfos.push({
                lineStartValue: normalLineEndPoint,
                lineEndValue: lineEndPoint,
                stateColor: ABNORMAL_STATE_COLOR,
                lineWidthRatio: lineDetails.abNormalLineWidthRatio
            })
        }
        return {testInfos: testInfos, markerIndex: lineDetails.markerIndex}
    }

    private getReportCellType(testResultType: TEST_RESULT_TYPE): DiagnosticTestDetailCellType {
        // for now, using same cell type as test result type
        return testResultType
    }

    private showReportGraph(cellType: DiagnosticTestDetailCellType, reportInfo: TestDetails): boolean {
        return cellType === "NUMERICAL" && this.isNormalRangePresent(reportInfo.reportDetailsResponse)
    }

    private isNormalRangePresent(reportDetails: DiagnosticTestReportDetails): boolean {
        return !_.isNil(reportDetails.testMinRangeValue) && !_.isNil(reportDetails.testMaxRangeValue)
    }

    private getReportCellData(reportDetails: DiagnosticTestReportDetails, cellType: DiagnosticTestDetailCellType): DiagnosticTestReportCell {
        switch (cellType) {
            case "NUMERICAL":
                const testMeasuringUnitText = !_.isEmpty(reportDetails.testMeasuringUnit) ? reportDetails.testMeasuringUnit : ""
                const normalValueText = this.isNormalRangePresent(reportDetails) && reportDetails.testMinRangeValue + "-" + reportDetails.testMaxRangeValue + testMeasuringUnitText
                const measuredValueText = reportDetails.testNumericalResultValue + testMeasuringUnitText
                return {
                    measuredValueTitle: "Measured Value",
                    normalValueTitle: this.isNormalRangePresent(reportDetails) && "Normal Value",
                    measuredValue: measuredValueText,
                    normalValue: normalValueText
                }
            case "WORDS":
                return {
                    measuredValueTitle: "Measured Value",
                    normalValueTitle: "Normal Value",
                    measuredValue: reportDetails.testResultValue + "",
                    normalValue: reportDetails.testNormalValueWord
                }
        }
    }

    private getTestReportLineValues(lineStartPoint: number, lineEndPoint: number, reportInfo: TestDetails): { normalLineWidthRatio: number, abNormalLineWidthRatio: number, markerIndex: number } {
        const reportDetails = reportInfo.reportDetailsResponse
        const normalLineStartPoint = reportDetails.testMinRangeValue
        const normalLineEndPoint = reportDetails.testMaxRangeValue
        const testNumericalResultValue = reportDetails.testNumericalResultValue
        let normalLineWidthRatio, markerIndex
        const abNormalLineWidthRatio = 0.2
        if (lineStartPoint < normalLineStartPoint && lineEndPoint > normalLineEndPoint) {
            // means 3 lines will be there
            normalLineWidthRatio = 0.6
            if (reportInfo.normalCount === 1) {
                markerIndex = 1
            } else if (testNumericalResultValue <= normalLineStartPoint) {
                markerIndex = 0
            } else {
                markerIndex = 2
            }
        } else if (lineStartPoint < normalLineStartPoint || lineEndPoint > normalLineEndPoint) {
            // 2 lines will be there
            normalLineWidthRatio = 0.8
            if (lineStartPoint < normalLineStartPoint) {
                markerIndex = reportInfo.normalCount === 1 ? 1 : 0
            } else {
                markerIndex = reportInfo.normalCount === 1 ? 0 : 1
            }

        } else {
            // single line
            normalLineWidthRatio = 1.0
            markerIndex = 0
        }
        return {
            normalLineWidthRatio, abNormalLineWidthRatio, markerIndex
        }
    }

    private getDiagnosticEmailedTestReportDetailedWidget(diagnosticReport: DiagnosticEmailedTestReportResponse): DiagnosticsEmailTestReportDetailedSummaryWidget {
        const items: DiagnosticEmailTestReportItem[] = diagnosticReport.productDetails.map((item: DiagnosticEmailedTestReportItem) => {
            return {
                title: item.productName,
                description: item.productDescription,
                action: undefined
            }
        })
        return new DiagnosticsEmailTestReportDetailedSummaryWidget(
            "Available on Email",
            CareUtil.getDiagnosticsEmailTestReportAction(diagnosticReport.orderId, "SEND"),
            items,
            true
        )
    }
}

export default DiagnosticReportViewBuilderV2
