import { inject, injectable } from "inversify"
import { DEFAULT_CACHE_REFRESH_TIME_IN_SECONDS, InMemoryCacheService } from "@curefit/memory-cache"
import { IPageConfigReadOnlyDao, IPageConfigReadWriteDao, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import * as _ from "lodash"
import { UrlPathBuilder } from "@curefit/product-common"
import { HowItWorksItem, WhatsInThePackItem } from "@curefit/product-common"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

@injectable()
class HCUDetailsPageConfig extends InMemoryCacheService<any> {

    constructor(
        @inject(PAGE_CONFIG_TYPES.PageConfigReadOnlySecondaryDao) protected pageConfigDao: IPageConfigReadOnlyDao,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger, 39 * 60)
        this.load("HCUDetailsPageConfig")
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    loadData(): Promise<any> {
        return this.pageConfigDao.findOne({ pageId: { $eq: "HCUPageConfig" } }).then(pageConfig => {
            const data = pageConfig.data
            this.howItWorksTitle = data.howItWorksTitle
            this.howItWorksItemList = _.map(<HowItWorksItem[]>data.howItWorksItemList, item => {
                return {
                    text: item.text,
                    icon: UrlPathBuilder.getIconPath("HOWITWORKS", item.icon, 1)
                }
            })

            this.whatsInThePackTitle = data.whatsInThePackTitle
            this.whatsInThePackItemList = _.map(<WhatsInThePackItem[]>data.whatsInThePackItemList, item => {
                return {
                    title: item.title,
                    icon: item.icon,
                    shortDescription: item.shortDescription,
                    description: item.description,
                    shadowColor: item.shadowColor,
                    gradientColors: item.gradientColors
                }
            })
            this.homeCollectionEnableIds = data.homeCollectionEnableIds
            this.isMultiCenterSupported = data.isMultiCenterSupported
            return data
        })
    }

    public howItWorksTitle: string
    public howItWorksItemList: HowItWorksItem[]
    public whatsInThePackTitle: string
    public whatsInThePackItemList: WhatsInThePackItem[]
    public homeCollectionEnableIds: string[]
    public isMultiCenterSupported: boolean

}

export default HCUDetailsPageConfig
