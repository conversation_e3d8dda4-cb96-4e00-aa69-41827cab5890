import { ProductType } from "@curefit/product-common"
import { AggregatedMembershipInfo, BookingDetail, ICovidMetricInfo, ICovidMetricItem, ICovidMetricsInfoResponse, ICovidMetricUserStateLevel, CareTeam } from "@curefit/albus-client"
import { ConsultationInfoItem, ConsultationInfoWidget, ColoredCodedInfoItem } from "@curefit/apps-common"
import { ActionUtil } from "@curefit/base-utils"
import { DiagnosticProductResponse } from "@curefit/care-common"
import { CustomerIssueType } from "@curefit/issue-common"
import { UserContext } from "@curefit/userinfo-common"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { Orientation } from "@curefit/vm-models"
import * as _ from "lodash"
import * as momentTz from "moment-timezone"
import { Action, ManageOptionPayload, ManageOptions, ProductDetailPage, WidgetView, PackProgress } from "../common/views/WidgetView"
import AppUtil, { AppFont, SUPPORT_DEEP_LINK } from "../util/AppUtil"
import {
    CareUtil,
    ConsultationInfoEmptyDisabledAction as emptyDisabledAction,
    ConsultationInfoSubTitleStyle as SubTitleStyle,
    ConsultationInfoTitleStyle as TitleStyle
 } from "../util/CareUtil"
import { CareTeamWidget, CareTeamItem } from "../page/PageWidgets"
import { UserAgent } from "@curefit/base-common"
class ConsultationPackPostPurchaseProductPage extends ProductDetailPage {

    async buildView(
        userContext: UserContext,
        product: DiagnosticProductResponse,
        bookingInfo: BookingDetail,
        issuesMap: Map<string, CustomerIssueType[]>,
        newReportIssueManageOption: ManageOptionPayload,
        aggregatedMembershipInfo: AggregatedMembershipInfo[]
    ) {
        const howItWorksItem = _.find(product.infoSection.children, infoSection => infoSection.type === "PACK_STEPS")
        const isMultipleBenefitFlow = CareUtil.isPartOfConsultationPackAyurvedaProducts(product?.setCode) || CareUtil.isPartOfConsultationPackDiabeticProducts(product?.setCode)
        this.actions = CareUtil.getConsultationPackPostBookingActions(userContext, aggregatedMembershipInfo, bookingInfo, [], isMultipleBenefitFlow, product)
        this.widgets.push(this.summaryWidget(userContext, product, bookingInfo, issuesMap, newReportIssueManageOption, aggregatedMembershipInfo && aggregatedMembershipInfo[0]))
        if (!_.isEmpty(bookingInfo.childBookingInfos)) {
            this.widgets.push(CareUtil.getBundleConsultationsWidget(userContext, bookingInfo.childBookingInfos))
        }
        this.widgets.push(CareUtil.getHowItWorksWidget(howItWorksItem, userContext.sessionInfo.userAgent))
        this.widgets = this.widgets.filter(Boolean).map(widget => ({ ...widget, hasDividerBelow: false }))
        return this
    }

    async buildCovidHomeView(
        userContext: UserContext,
        product: DiagnosticProductResponse,
        bookingInfo: BookingDetail,
        issuesMap: Map<string, CustomerIssueType[]>,
        newReportIssueManageOption: ManageOptionPayload,
        aggregatedMembershipInfo: AggregatedMembershipInfo[] = [],
        covidMetricDetails?: ICovidMetricsInfoResponse,
        careTeam?: CareTeam[]
    ) {
        const { packFAQ, packContentsDetailed, howItWorksItem } = CareUtil.getPackContent(product)
        const userAgent = userContext.sessionInfo.userAgent
        const isApp = userAgent === "APP"
        this.actions = CareUtil.getConsultationPackPostBookingActions(userContext, aggregatedMembershipInfo, bookingInfo, careTeam, true)
        this.widgets.push(this.summaryWidget(userContext, product, bookingInfo, issuesMap, newReportIssueManageOption, aggregatedMembershipInfo && aggregatedMembershipInfo[0]))
        if (isApp && !_.isEmpty(careTeam)) {
            this.widgets.push(this.getCareTeamWidget(userContext, careTeam))
        }
        if (!_.isEmpty(bookingInfo.childBookingInfos)) {
            this.widgets.push(CareUtil.getBundleConsultationsWidget(userContext, bookingInfo.childBookingInfos))
        }
        if (isApp) {
            this.widgets.push(this.covidVitalInfoWidget(userContext, bookingInfo.booking.patientId, covidMetricDetails, aggregatedMembershipInfo && aggregatedMembershipInfo[0]))
            // this.widgets.push(this.getCovidMonitoringDevicesWidget())
            this.widgets.push(CareUtil.getFAQWidget(packFAQ))
            this.widgets.push(this.getCovidEmergencyWidget())
        } else {
        }
        this.widgets.push(CareUtil.getHowItWorksWidget(packContentsDetailed || howItWorksItem, userAgent))
        this.widgets = this.widgets.filter(Boolean).map(widget => ({ ...widget, hasDividerBelow: false }))
        return this
    }

    public summaryWidget(
        userContext: UserContext,
        product: DiagnosticProductResponse,
        bookingDetail: BookingDetail,
        issuesMap: Map<string, CustomerIssueType[]>,
        newReportIssueManageOption: ManageOptionPayload,
        aggregatedMembershipInfo: AggregatedMembershipInfo,
    ): WidgetView {
        const manageOptionsView: { manageOptions: ManageOptions; meta: any } = this.getManageOptions(bookingDetail, issuesMap, newReportIssueManageOption)
        const isApp = userContext.sessionInfo.userAgent === "APP"
        const summaryWidget: WidgetView & {
            productId: string
            title: string
            subTitle?: string
            image: string
            meta: any
            packProgress?: PackProgress
            manageOptions: ManageOptions,
            breadcrumb?: { text: string, link?: string }[],
            orientation?: Orientation,
            actions?: Action[],
            hasDividerBelow: boolean,
            productType: ProductType
        } = {
            widgetType: "HEALTHCHECKUP_SUMMARY_WIDGET",
            subTitle: this.getSubTitle(bookingDetail, aggregatedMembershipInfo, userContext),
            title: product.productName,
            productId: product.productCode,
            image: product.heroImageUrl,
            manageOptions: isApp ? manageOptionsView.manageOptions : undefined,
            meta: isApp ? manageOptionsView.meta : undefined,
            packProgress: this.getPackProgress(product, userContext, aggregatedMembershipInfo, bookingDetail),
            orientation: userContext.sessionInfo.userAgent === "DESKTOP" ? "RIGHT" : undefined,
            breadcrumb: userContext.sessionInfo.userAgent === "DESKTOP" ? [{ text: "Home", link: "/" }, { text: "Care", link: "/care" }, { text: "Health Packs", link: "/care/healthpack" }, { text: _.get(product, "infoSection.packTitle", product.productName) }] : [],
            actions: userContext.sessionInfo.userAgent !== "APP" ? this.actions : [],
            hasDividerBelow: false,
            productType: "BUNDLE"

        }
        return summaryWidget
    }

    getPackProgress(product: DiagnosticProductResponse, userContext: UserContext, aggregatedMembershipInfo: AggregatedMembershipInfo, bookingDetail: BookingDetail) {
        if (!aggregatedMembershipInfo) {
            return
        }
        if (_.isEmpty(aggregatedMembershipInfo.startEndEpoch)) {
            return
        }
        const isSessionBasedPack = CareUtil.isPartOfConsultationPackAyurvedaProducts(product?.setCode)
        const { total, completed, isCompleted, endDateFormatted } = CareUtil.getConsultationPackProgressInfo(userContext, aggregatedMembershipInfo, isSessionBasedPack)
        const subTitle = _.get(bookingDetail, "bundleOrderResponse.patient.name") ? `For: ${bookingDetail.bundleOrderResponse.patient.name}` : undefined
        const shortTitle = _.get(product, "infoSection.shortTitle")
        let packProgress

        if (isSessionBasedPack) {
            const totalSessions = aggregatedMembershipInfo.totalTickets
            const completedSessions = aggregatedMembershipInfo.totalTicketsConsumed // _.filter(bookingDetail.childBookingInfos, childBooking => childBooking.booking.status === "COMPLETED").length
            if (userContext.sessionInfo.userAgent === "APP") {
                packProgress = {
                    total,
                    completed,
                    state: "ACTIVE",
                    type: <ProductType>"BUNDLE",
                    leftText: `${completedSessions}/${totalSessions} Session Booked`,
                    rightText: endDateFormatted,
                    progressBarTextStyle: { marginTop: 10, fontSize: 14 },
                    progressBarColor: "#008300",
                    isSplitView: true
                }
            } else {
                packProgress = {
                    title: `${completedSessions}/${totalSessions} Session Booked` ,
                    subTitle: endDateFormatted,
                    total,
                    completed,
                    state: isCompleted ? "COMPLETED" : "ACTIVE",
                    packColor: "#008300",
                    type: <ProductType>"BUNDLE",
                    action: !isCompleted && this.actions && this.actions[0] ? this.actions[0] : undefined
                }
            }
        } else if (userContext.sessionInfo.userAgent === "APP") {
            packProgress = {
                total,
                completed,
                type: <ProductType>"BUNDLE",
                state: "ACTIVE",
                title: userContext.sessionInfo.appVersion < 7.85 ? shortTitle : subTitle,
                subTitle: userContext.sessionInfo.appVersion < 7.85 ? endDateFormatted : undefined,
                leftText: shortTitle,
                rightText: endDateFormatted,
                progressBarTextStyle: { marginTop: 10, fontSize: 14 },
                progressBarColor: "#008300"
            }
        } else {
            packProgress = {
                subTitle: `${shortTitle ? `${shortTitle} | ` : ""}${endDateFormatted}`,
                total,
                completed,
                state: isCompleted ? "COMPLETED" : "ACTIVE",
                packColor: "#008300",
                type: <ProductType>"BUNDLE",
                action: !isCompleted && this.actions && this.actions[0] ? this.actions[0] : undefined
            }
        }
        return packProgress
    }

    public getIssueList(issuesMap: Map<string, CustomerIssueType[]>): { code: string, title: string, confirmation: string }[] {
        const issueList: { code: string, title: string, confirmation: string }[] = []
        issuesMap.get("DiagnosticsSingleBooked").forEach(customerIssueType => {
            issueList.push({
                code: customerIssueType.code,
                title: customerIssueType.subject,
                confirmation: customerIssueType.confirmation
            })
        })
        return issueList
    }

    public getSubTitle(bookingDetail: BookingDetail, aggregatedMembershipInfo: AggregatedMembershipInfo, userContext: UserContext) {
        if (bookingDetail.booking.status === "CANCELLED") {
            return "Cancelled"
        }
        if (_.isEmpty(aggregatedMembershipInfo)) {
            return _.get(bookingDetail, "bundleOrderResponse.patient.name") ? `For: ${bookingDetail.bundleOrderResponse.patient.name} | Pack Expired` : undefined
        }
        return userContext.sessionInfo.appVersion < 7.85 && _.get(bookingDetail, "bundleOrderResponse.patient.name") ? `For: ${bookingDetail.bundleOrderResponse.patient.name}` : undefined
    }

    public getManageOptions(
        bookingDetail: BookingDetail,
        issuesMap: Map<string, CustomerIssueType[]>,
        newReportIssueManageOption: ManageOptionPayload
    ): { manageOptions: ManageOptions, meta: any } {
        const options: ManageOptionPayload[] = []
        if (newReportIssueManageOption) {
            options.push(newReportIssueManageOption)
        } else {
            options.push(
                {
                    isEnabled: true,
                    displayText: "Need Help",
                    type: "REPORT_ISSUE",
                    meta: this.getIssueList(issuesMap),
                    action: SUPPORT_DEEP_LINK,
                }
            )
        }

        const manageOptions: ManageOptions = {
            displayText: "...",
            options: options
        }

        const meta: any = {
            orderId: bookingDetail.booking.cfOrderId,
            productId: bookingDetail.booking.productCode,
            tcBookingId: bookingDetail.booking.id
        }
        return { manageOptions: manageOptions, meta: meta }
    }

    private getCareTeamWidget(userContext: UserContext, careTeam: CareTeam[]): CareTeamWidget {
        const careTeamViewItems: CareTeamItem[] = []
        careTeam.map(item => {
            careTeamViewItems.push({
                title: item.doctor.name,
                subTitle: item.doctor.primarySubServiceType?.displayValue || item.doctorTypeCode?.displayValue,
                imageUrl: item.doctor.displayImage,
                actions: this.getCareTeamActions(userContext, item)
            })
        })
        return new CareTeamWidget(careTeamViewItems,
            "My Doctors",
            undefined, // { backgroundColor: "white" },
            false,
            false
        )
    }

    private getCareTeamActions(userContext: UserContext, item: CareTeam): Action[] {
        const actions: Action[] = []
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "DESKTOP")
        if (item.chatActionWithContext && item.chatActionWithContext.action && item.chatActionWithContext.action.actionPermitted) {
            actions.push({
                title: "Message",
                actionType: (userAgent === "DESKTOP" || userAgent === "MBROWSER") ? "OPEN_MESSAGE_DRAWER" : "NAVIGATION",
                icon: "MESSAGE",
                url: ActionUtil.chatMessageActionUrl(item.patientId, item.chatActionWithContext.context.twilioCommunicationMode.modeName, item.doctor.name, item.doctor.displayImage, item.doctor.qualification, undefined, undefined, "managedplan")
            })
        }
        return actions
    }

    public covidVitalInfoWidget(
        userContext: UserContext,
        subUserId: number,
        covidMetricDetails: ICovidMetricsInfoResponse,
        aggregatedMembershipInfo: AggregatedMembershipInfo
    ): WidgetView {
        if (!aggregatedMembershipInfo) {
            return undefined
        }
        if (_.isEmpty(aggregatedMembershipInfo.startEndEpoch)) {
            return undefined
        }
        const { isCompleted } = CareUtil.getConsultationPackProgressInfo(userContext, aggregatedMembershipInfo, false)
        return CareUtil.getCovidVitalInfoWidget(userContext, subUserId, covidMetricDetails, isCompleted)
    }

    public getCovidEmergencyWidget() {
        return new ConsultationInfoWidget(
            [
                {
                    title: "When and What is an Emergency",
                    subtitle: "Identify Signs and Understand How to Act",
                    imageUrl: "image/carefit/covidPack/sheild.png",
                    itemClickAction: emptyDisabledAction,
                    titleAction: {
                        title: "VIEW",
                        url: "curefit://listpage?pageId=Covid_FAQ_4",
                        actionType: "NAVIGATION"
                    },
                    isSelected: true,
                    subtitleStyle: SubTitleStyle,
                    titleStyle: TitleStyle,
                }
            ]
        )
    }

    public getCovidMonitoringDevicesWidget() {
        const action: Action = {
            actionType: "NAVIGATION",
            title: "GET",
            disabled: false,
            url: "curefit://gearlist?pageName=yoga-mats" // To do Update the link from vishnu
        }
        return new ConsultationInfoWidget(
            [
                {
                    title: "ORDER MONITORING DEVICES",
                    subtitle: "Get your monitoring devices",
                    imageUrl: "image/carefit/covidPack/thermometer.png",
                    itemClickAction: action,
                    titleAction: action,
                    isSelected: true,
                    subtitleStyle: SubTitleStyle,
                    titleStyle: TitleStyle,
                }
            ]
        )
    }

}

export default ConsultationPackPostPurchaseProductPage
