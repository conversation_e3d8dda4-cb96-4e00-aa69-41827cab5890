import { PackDetailWidget, ProductDetailPage } from "../common/views/WidgetView"
import HCUDetailsPageConfig from "./HCUDetailsPageConfig"
import { WhatsInThePackItem } from "@curefit/product-common"


class CarePackDetailPageView extends ProductDetailPage {
    private pageTitle: string
    constructor(hCUDetailsPageConfig: HCUDetailsPageConfig) {
        super()
        this.pageTitle = "What is in the pack ?"
        hCUDetailsPageConfig.whatsInThePackItemList.forEach(item => {
            this.widgets.push(
                this.getPackDetailWidget(item)
            )
        })
    }

    private getPackDetailWidget(item: WhatsInThePackItem): PackDetailWidget {
        return {
            widgetType: "PACK_DETAIL_WIDGET",
            title: item.title,
            description: item.description,
            icon: item.icon
        }
    }


}
export default CarePackDetailPageView
