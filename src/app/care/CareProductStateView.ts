import { CareProductStateWidget, ProductStateItem, STEP_STATE, StepStateCard } from "../page/PageWidgets"
import { BookingDetail, BundleStepInfo, DiagnosticsTestOrderResponse, StepInfo } from "@curefit/albus-client"
import { CareUtil } from "../util/CareUtil"
import * as _ from "lodash"
import { Action } from "../common/views/WidgetView"
import { ActionUtil } from "@curefit/base-utils"
import { UserContext } from "@curefit/userinfo-common"
import { TimeUtil } from "@curefit/util-common"

const dividerGradientDisabled = ["#d8d8d8", "#d8d8d8"]
const dividerGradientEnabled = ["#5f5f5f", "#d8d8d8"]

export class CareProductStateView {
  public widget: CareProductStateWidget
  public primaryAction: Action
  constructor(userContext: UserContext, productId: string, bookingInfo: BookingDetail, reportEstimationTime: any) {
    this.widget = this.getCareProductStateWidget(userContext, productId, bookingInfo, reportEstimationTime)
  }

  private getCareProductStateWidget(userContext: UserContext, productId: string, bookingInfo: BookingDetail, reportEstimationTime: any): CareProductStateWidget {
    const expandedItemIndices: number[] = []
    let consultationEnabled: boolean
    const items: ProductStateItem[] = _.map(bookingInfo.bundleSetupInfo.bundleStepInfos, (stepInfo, index) => {
      let stateItem
      switch (stepInfo.setupStep) {
        case "DIAGNOSTIC_TEST":
          if (stepInfo.stepState === "REPORT_GENERATED") {
            consultationEnabled = true
          }
          stateItem = this.getDiagnosticsStateItem(bookingInfo.booking.patientId, userContext, productId, stepInfo, `STEP 0${index + 1}`, reportEstimationTime)
          if (stateItem.isExpanded) expandedItemIndices.push(0)
          return stateItem
        case "HEALTH_ASSESSMENT":
          stateItem = this.getHealthAssessmentStateItem(stepInfo, `STEP 0${index + 1}`)
          if (stateItem.isExpanded) expandedItemIndices.push(1)
          return stateItem
        case "CONSULTATION":
          stateItem = this.getConsultationStateItem(userContext, bookingInfo, stepInfo, consultationEnabled, `STEP 0${index + 1}`)
          if (stateItem.isExpanded) expandedItemIndices.push(2)
          return stateItem
      }
    })
    return new CareProductStateWidget(items, expandedItemIndices, false)
  }

  private getDiagnosticsStateItem(patientId: number, userContext: UserContext, productId: string, stepInfo: BundleStepInfo, stepName: string, reportEstimationTime: any): ProductStateItem {
    let states: StepStateCard[]
    let dividerGradient = dividerGradientEnabled
    let action: Action
    let isExpanded: boolean = true
    let icon: string = "tests"
    switch (stepInfo.stepState) {
      case "NOT_BOOKED":
        states = this.getDiagnosticsNotBookedSteps()
        dividerGradient = dividerGradientDisabled
        break
      case "BOOKED":
        states = this.getSteps(patientId, userContext, productId, stepInfo, "NOT_STARTED", reportEstimationTime)
        if (!_.isEmpty(stepInfo.allowedActions) && stepInfo.allowedActions.indexOf("CANCEL") >= 0) {
          action = {
            actionType: "CANCEL_HCU_TEST",
            title: "Cancel",
            meta: {
              tcBookingId: stepInfo.testBookingInfo.booking.id
            }
          }
        }
        break
      case "TEST_COMPLETED":
        states = this.getSteps(patientId, userContext, productId, stepInfo, "STARTED", reportEstimationTime)
        break
      case "REPORT_GENERATED":
        states = this.getSteps(patientId, userContext, productId, stepInfo, "COMPLETED", reportEstimationTime)
        const testOrderId = stepInfo.testBookingInfo.diagnosticsTestOrderResponse[0].orderId
        action = {
          actionType: "NAVIGATION",
          url: ActionUtil.diagnosticReportPage(testOrderId, stepInfo.testBookingInfo.diagnosticsTestOrderResponse[0].carefitOrderId),
          title: "View Report"
        }
        isExpanded = false
        icon = "tick"
        break
    }
    const stateItem: ProductStateItem = {
      header: stepName,
      title: "Diagnostic tests",
      gradientColors: ["#fb8676", "#f64cab"],
      dividerGradient: dividerGradient,
      icon: icon,
      states: states,
      action: action,
      isExpanded: isExpanded
    }
    return stateItem
  }

  private getDiagnosticsAtHomeStateCard(userContext: UserContext, patientId: number, stepInfo: BundleStepInfo): StepStateCard {
    const diagnosticsTestOrderResponse = stepInfo.testBookingInfo.diagnosticsTestOrderResponse[0]
    const atHomeStepInfo: StepInfo = diagnosticsTestOrderResponse.atHomeStepInfo
    if (!_.isEmpty(atHomeStepInfo) && !_.isEmpty(diagnosticsTestOrderResponse.atHomeDiagnosticOrder)) {
      const sampleCollectionStartDate = diagnosticsTestOrderResponse.atHomeDiagnosticOrder.startTime
      const sampleCollectionEndDate = diagnosticsTestOrderResponse.atHomeDiagnosticOrder.endTime
      const sampleCollectionDateText: string = CareUtil.getHomeCollectionTimeText(sampleCollectionStartDate, sampleCollectionEndDate, userContext)
      const atHomeTitle: string = "Sample collection at home"
      switch (atHomeStepInfo.stepState) {
        case "BOOKED":
          const rescheduleAction = this.getRescheduleAction(patientId, diagnosticsTestOrderResponse.productCodes[0], atHomeStepInfo, stepInfo, "AT_HOME_SLOT")
          const atHomeDetailTopAction: any = undefined // this.getAtHomeDetailTopAction(diagnosticsTestOrderResponse)
          const cardAction: Action = {
            title: "Details",
            actionType: "SHOW_DIAGNOSTIC_TEST_DETAILS",
            meta: {
              instructions: diagnosticsTestOrderResponse.atHomeInstructions,
              topAction: atHomeDetailTopAction,
              bottomAction: rescheduleAction,
              title: atHomeTitle,
              subtitle: sampleCollectionDateText
            }
          }
          const action: Action = rescheduleAction ? {
            ...rescheduleAction,
            title: "Reschedule"
          } : cardAction
          return {
            state: "STARTED",
            viewType: "TEXT_ACTION",
            views: [
              {
                title: atHomeTitle,
                action,
                cardAction,
                text: sampleCollectionDateText

              }
            ]
          }
        case "COMPLETED":
        case "SAMPLE_COLLECTED":
          return {
            state: "COMPLETED",
            viewType: "TEXT_ACTION",
            views: [
              {
                title: atHomeTitle
              }
            ]
          }
      }
    }
  }

  private getDiagnosticsAtCenterStateCard(userContext: UserContext, patientId: number, stepInfo: BundleStepInfo, atHomeEnabled: boolean): StepStateCard {
    const diagnosticsTestOrderResponse: DiagnosticsTestOrderResponse = stepInfo.testBookingInfo.diagnosticsTestOrderResponse[0]
    if (!_.isEmpty(diagnosticsTestOrderResponse.inCentreStepInfo) && !_.isEmpty(diagnosticsTestOrderResponse.inCentreDiagnosticOrder)) {
      const testStartDate = diagnosticsTestOrderResponse.inCentreDiagnosticOrder.slot.workingStartTime
      const inCentreTestDateStartText: string = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, testStartDate, "ddd, DD MMM, h:mm A")
      const inCentreTitle = atHomeEnabled ? "Radiology tests at center" : "All tests at center"

      switch (diagnosticsTestOrderResponse.inCentreStepInfo.stepState) {
        case "BOOKED":
          const rescheduleAction = this.getRescheduleAction(patientId, diagnosticsTestOrderResponse.productCodes[0], diagnosticsTestOrderResponse.inCentreStepInfo, stepInfo, "IN_CENTRE_SLOT", atHomeEnabled)
          const inCenterTopAction = this.getInCenterDetailTopAction(stepInfo)
          const cardAction: Action = {
            title: "Details",
            actionType: "SHOW_DIAGNOSTIC_TEST_DETAILS",
            meta: {
              instructions: diagnosticsTestOrderResponse.inCentreInstructions,
              topAction: inCenterTopAction,
              bottomAction: rescheduleAction,
              title: inCentreTitle,
              subtitle: inCentreTestDateStartText
            }
          }
          const action: Action = rescheduleAction ? {
            ...rescheduleAction,
            title: "Reschedule"
          } : cardAction
          return {
            state: "STARTED",
            viewType: "TEXT_ACTION",
            views: [
              {
                title: inCentreTitle,
                action,
                cardAction,
                text: inCentreTestDateStartText

              }
            ]
          }
        case "COMPLETED":
        case "SAMPLE_COLLECTED":
          return {
            state: "COMPLETED",
            viewType: "TEXT_ACTION",
            views: [
              {
                title: inCentreTitle
              }
            ]
          }
      }
    }
  }

  private getSteps(patientId: number, userContext: UserContext, productId: string, stepInfo: BundleStepInfo, state: STEP_STATE, reportEstimation: any): StepStateCard[] {
    const states: StepStateCard[] = []
    const isDiagnosticInstructionSupported: boolean = CareUtil.isDiagnosticInstructionSupported(userContext)
    const diagnosticsTestOrderResponse = stepInfo.testBookingInfo.diagnosticsTestOrderResponse[0]
    const statusInfo = CareUtil.getDiagnosticTestHomeCenterStatusInfo(diagnosticsTestOrderResponse)
    const reportEstimationTime = !_.isEmpty(reportEstimation) ? _.get(reportEstimation, "tat", null) : null
    const atHomeCard = isDiagnosticInstructionSupported ? this.getDiagnosticsAtHomeStateCardV2(userContext, patientId, stepInfo, statusInfo)  : this.getDiagnosticsAtHomeStateCard(userContext, patientId, stepInfo)
    const atHomeEnabled: boolean = !_.isEmpty(atHomeCard)
    const incentreCard = isDiagnosticInstructionSupported ? this.getDiagnosticsAtCenterStateCardV2(userContext, patientId, stepInfo, atHomeEnabled, statusInfo) : this.getDiagnosticsAtCenterStateCard(userContext, patientId, stepInfo, atHomeEnabled)
    const atCenterEnabled: boolean = !_.isEmpty(incentreCard)
    if (statusInfo.sampleCollectionStartDateAtCenter < statusInfo.sampleCollectionStartDateAtHome) {
      if (atCenterEnabled) {
        states.push(incentreCard)
      }
      if (atHomeEnabled) {
        states.push(atHomeCard)
      }
    } else {
      if (atHomeEnabled) {
        states.push(atHomeCard)
      }
      if (atCenterEnabled) {
        states.push(incentreCard)
      }
    }
    states.push({
      state: state,
      viewType: "TEXT_ACTION",
      views: [
        {
          title: `Get your report${isDiagnosticInstructionSupported && reportEstimationTime ? " | Expected Time" : ""}`,
          text: `${isDiagnosticInstructionSupported &&
            reportEstimationTime ? TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, reportEstimationTime, "ddd, D MMM, h:mm A") : ""}`
        }
      ]
    })
    return states
  }

  private getDiagnosticsAtHomeStateCardV2(userContext: UserContext, patientId: number, stepInfo: BundleStepInfo,
    statusInfo: any): StepStateCard {
    const diagnosticsTestOrderResponse = stepInfo.testBookingInfo.diagnosticsTestOrderResponse[0]
    const atHomeStepInfo: StepInfo = diagnosticsTestOrderResponse.atHomeStepInfo
    if (!_.isEmpty(atHomeStepInfo) && !_.isEmpty(diagnosticsTestOrderResponse.atHomeDiagnosticOrder)) {
      const sampleCollectionStartDate = _.get(diagnosticsTestOrderResponse, "atHomeDiagnosticOrder.startTime", null)
      const sampleCollectionEndDate = _.get(diagnosticsTestOrderResponse, "atHomeDiagnosticOrder.endTime", null)
      const rescheduleAction = this.getRescheduleAction(patientId, diagnosticsTestOrderResponse.productCodes[0], atHomeStepInfo, stepInfo, "AT_HOME_SLOT")
      const atHomeDetailTopAction: any = undefined // this.getAtHomeDetailTopAction(diagnosticsTestOrderResponse)
      // Temp hack Adding 30 mins for home slots alone and removed end time
      const sampleCollectionDateText: string = sampleCollectionStartDate ? CareUtil.getHomeCollectionTimeText(sampleCollectionStartDate, sampleCollectionEndDate, userContext) : undefined
      const titleObj = {
          title: "Sample collection at home",
          completionTitle: "Sample Collected",
          startText: sampleCollectionDateText,
          endText: sampleCollectionDateText
      }
      return CareUtil.getDiagnosticsStateCardV2("AT_HOME", diagnosticsTestOrderResponse, statusInfo, titleObj, atHomeDetailTopAction, rescheduleAction, {})
    }
  }

  private getDiagnosticsAtCenterStateCardV2(userContext: UserContext, patientId: number, stepInfo: BundleStepInfo,
    atHomeEnabled: boolean, statusInfo: any): StepStateCard {
    const tz = userContext.userProfile.timezone
    const diagnosticsTestOrderResponse: DiagnosticsTestOrderResponse = stepInfo.testBookingInfo.diagnosticsTestOrderResponse[0]
    if (!_.isEmpty(diagnosticsTestOrderResponse.inCentreStepInfo) && !_.isEmpty(diagnosticsTestOrderResponse.inCentreDiagnosticOrder)) {
      const testStartDate = _.get(diagnosticsTestOrderResponse, "inCentreDiagnosticOrder.slot.workingStartTime", null)
      const testEndDate = _.get(diagnosticsTestOrderResponse, "inCentreDiagnosticOrder.slot.workingEndTime", null)
      const titleObj = {
        title: atHomeEnabled ? "Radiology tests at center" : "All tests at center",
        completionTitle: atHomeEnabled ? "Radiology tests at center" : "All tests at center",
        startText: testStartDate ? TimeUtil.formatEpochInTimeZone(tz, testStartDate, "ddd, DD MMM, h:mm A") : "",
        endText: testEndDate ? TimeUtil.formatEpochInTimeZone(tz, testEndDate, "ddd, DD MMM, h:mm A") : ""
      }
      const rescheduleAction = this.getRescheduleAction(patientId, diagnosticsTestOrderResponse.productCodes[0], diagnosticsTestOrderResponse.inCentreStepInfo, stepInfo, "IN_CENTRE_SLOT", atHomeEnabled)
      const inCenterTopAction = this.getInCenterDetailTopAction(stepInfo)
      return CareUtil.getDiagnosticsStateCardV2("AT_CENTER", diagnosticsTestOrderResponse, statusInfo, titleObj, inCenterTopAction, rescheduleAction, {})
    }
  }

  private getAtHomeDetailTopAction(diagnosticsTestOrderResponse: DiagnosticsTestOrderResponse): Action {
    const phoneNumber = diagnosticsTestOrderResponse.atHomeDiagnosticOrder.phleboMobileNumber
    if (!_.isEmpty(phoneNumber)) {
      return {
        actionType: "PHONE_CALL_NAVIGATION",
        icon: "PHONE_CALL",
        title: " Associate",
        meta: {
          phoneNumber: phoneNumber
        }
      }
    }
  }

  private getInCenterDetailTopAction(bundleStepInfo: BundleStepInfo): Action {
    const placeUrl = bundleStepInfo.testBookingInfo.diagnosticsTestOrderResponse[0].inCentreDiagnosticOrder.slot.diagnosticCentre.placeUrl
    return {
      actionType: "EXTERNAL_DEEP_LINK",
      url: placeUrl,
      icon: "NAVIGATE",
      title: "View Map"
    }
  }

  private getRescheduleAction(patientId: number, productId: string, stepInfo: StepInfo, bundleStepInfo: BundleStepInfo, category: string, atHomeEnabled?: boolean): Action {
    if (_.includes(stepInfo.allowedActions, "RESCHEDULE")) {
      return {
        title: "RESCHEDULE",
        actionType: "DIAGNOSTIC_TEST_RESCHEDULE",
        meta: {
          productId: productId,
          patientId,
          centerId: category === "IN_CENTRE_SLOT" ? bundleStepInfo.testBookingInfo.diagnosticsTestOrderResponse[0].inCentreDiagnosticOrder.slot.diagnosticCentre.id : undefined,
          centreCode: category === "IN_CENTRE_SLOT" ? bundleStepInfo.testBookingInfo.diagnosticsTestOrderResponse[0].inCentreDiagnosticOrder.slot.diagnosticCentre.code : undefined,
          parentBookingId: bundleStepInfo.testBookingInfo.booking.id,
          type: "DIAGNOSTICS",
          category: category,
          hasAtHome: atHomeEnabled ? true : undefined,
          title: category === "IN_CENTRE_SLOT" ? undefined : "Select your home address"
        }
      }
    }
  }

  private getDiagnosticsNotBookedSteps(): StepStateCard[] {
    const states: StepStateCard[] = []
    states.push({
      state: "NOT_STARTED",
      viewType: "TEXT_ACTION",
      views: [
        {
          title: "Schedule tests"
        }
      ]
    })
    states.push({
      state: "NOT_STARTED",
      viewType: "TEXT_ACTION",
      views: [
        {
          title: "Get diagnostic report"
        }
      ]
    })
    return states
  }

  private getHealthAssessmentStateItem(stepInfo: BundleStepInfo, stepName: string): ProductStateItem {
    let states: StepStateCard[]
    let isExpanded: boolean = true
    let icon: string = "assessment"
    switch (stepInfo.stepState) {
      case "NOT_COMPLETED":
        states = this.getHeathAssessmentNotCompletedSteps(stepInfo)
        break
      case "PARTIALLY":
        states = this.getHeathAssessmentPartiallyCompletedSteps(stepInfo)

        break
      case "COMPLETED":
        states = this.getHeathAssessmentCompletedSteps(stepInfo)
        icon = "tick"
        isExpanded = false
    }
    const stateItem: ProductStateItem = {
      header: stepName,
      title: "Fill health assessement",
      subtitle: "Let us know more about your health",
      gradientColors: ["#77fd9d", "#48c9b3"],
      dividerGradient: dividerGradientEnabled,
      icon: icon,
      states: states,
      isExpanded: isExpanded
    }
    return stateItem
  }

  private getHeathAssessmentNotCompletedSteps(stepInfo: BundleStepInfo): StepStateCard[] {
    const patientAssessment = stepInfo.patientAssessment
    if (_.isNil(patientAssessment) || _.isEmpty(patientAssessment)) {
      return []
    }
    return [
      {
        state: "STARTED",
        viewType: "ACTIONABLE",
        views: [
          {
            action: {
              meta: {
                nextAction: {
                  meta: {
                    assessmentId: patientAssessment.id,
                    assessmentStatus: "COMPLETED"
                  },
                  actionType: "UPDATE_PATIENT_ASSESSMENT"
                }
              },
              title: "Start now",
              url: ActionUtil.webview(patientAssessment.assessmentUrl),
              actionType: "OPEN_ASSESSMENT_FORM"
            }
          }
        ]
      }
    ]
  }

  private getHeathAssessmentPartiallyCompletedSteps(stepInfo: BundleStepInfo): StepStateCard[] {
    const patientAssessment = stepInfo.patientAssessment
    if (_.isNil(patientAssessment) || _.isEmpty(patientAssessment)) {
      return []
    }
    return [
      {
        state: "STARTED",
        viewType: "ACTIONABLE",
        views: [
          {
            progressPercent: patientAssessment.completionPerc > 0 ? patientAssessment.completionPerc : undefined,
            action: {
              meta: {
                nextAction: {
                  meta: {
                    assessmentId: patientAssessment.id,
                    assessmentStatus: "COMPLETED"
                  },
                  actionType: "UPDATE_PATIENT_ASSESSMENT"
                }
              },
              title: "Complete now",
              url: ActionUtil.webview(patientAssessment.assessmentUrl),
              actionType: "OPEN_ASSESSMENT_FORM"
            }
          }
        ]
      }
    ]
  }

  private getHeathAssessmentCompletedSteps(stepInfo: BundleStepInfo): StepStateCard[] {
    const patientAssessment = stepInfo.patientAssessment
    return [
      {
        state: "COMPLETED",
        viewType: "TEXT_ACTION",
        views: [
          {
            title: "Completed" // TODO
          }
        ]
      }
    ]
  }

  private getConsultationStateItem(userContext: UserContext, bookingInfo: BookingDetail, stepInfo: BundleStepInfo,
    consultationEnabled: boolean, stepName: string): ProductStateItem {
    let states: StepStateCard[]
    const dividerGradient = consultationEnabled ? dividerGradientEnabled : dividerGradientDisabled
    let isExpanded: boolean = consultationEnabled
    let icon: string = "report"
    switch (stepInfo.stepState) {
      case "NOT_BOOKED":
        states = this.getConsultationNotBookedSteps(bookingInfo, consultationEnabled, stepInfo.stepMeta)
        break
      case "BOOKED":
        states = this.getConsultationBookedSteps(userContext, stepInfo)
        break
      case "COMPLETED":
        states = this.getConsultationCompletedSteps(userContext, stepInfo)
        break
      case "PRESCRIPTION_GENERATED":
        isExpanded = false
        icon = "tick"
        states = this.getPresescriptionGeneratedSteps(userContext, stepInfo)
        break
      case "PLAN_GENERATED":
    }
    const stateItem: ProductStateItem = {
      header: stepName,
      title: "Discuss report",
      gradientColors: ["#f29458", "#fd796d"],
      dividerGradient: dividerGradient,
      icon: icon,
      states: states,
      isExpanded: isExpanded
    }
    return stateItem
  }

  private getConsultationNotBookedSteps(bookingInfo: BookingDetail, consultationEnabled: boolean, meta: any): StepStateCard[] {
    if (consultationEnabled) {

      this.primaryAction = {
        title: "Book consultation",
        url: CareUtil.getBookConsultationActionUrl(bookingInfo.booking.id.toString(), bookingInfo.booking.patientId, meta),
        actionType: "NAVIGATION"
      }
      return [
        {
          state: "STARTED",
          viewType: "ACTIONABLE",
          views: [
            {
              title: "Schedule Doctor Consultation",
              action: this.primaryAction
            }
          ]
        },
        {
          state: "NOT_STARTED",
          viewType: "TEXT_ACTION",
          views: [
            {
              title: "Get medical & lifestyle prescription"
            }
          ]
        }
      ]
    }
    return [
      {
        state: "NOT_STARTED",
        viewType: "TEXT_ACTION",
        views: [
          {
            title: "Meet with our team and have them go over your report"
          }
        ]
      },
      {
        state: "NOT_STARTED",
        viewType: "TEXT_ACTION",
        views: [
          {
            title: "Discuss possible next steps"
          }
        ]
      }
    ]
  }

  private getConsultationBookedSteps(userContext: UserContext, stepInfo: BundleStepInfo): StepStateCard[] {
    const consultationOrderResponse = stepInfo.consultationBookingInfos[0].consultationOrderResponse
    const doctorDetails = consultationOrderResponse.doctor
    const subtitle = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, consultationOrderResponse.startTime, "h:mm A, DD MMM")
    return [
      {
        state: "COMPLETED",
        viewType: "ACTION_ICON",
        views: [
          {
            title: "Schedule Doctor Consultation",
            image: doctorDetails.displayImage,
            action: {
              title: doctorDetails.name,
              subtitle: subtitle,
              // TODO handle for two product codes
              url: ActionUtil.teleconsultationSingle(userContext, stepInfo.stepMeta[0].productCodes[0], stepInfo.consultationBookingInfos[0].consultationOrderResponse.consultationProduct.urlPath, `${stepInfo.consultationBookingInfos[0].booking.id}`, undefined, "CAREFIT"),
              actionType: "NAVIGATION",

            }
          }
        ]
      },
      {
        state: "NOT_STARTED",
        viewType: "TEXT_ACTION",
        views: [
          {
            title: "Get medical & lifestyle prescription"
          }
        ]
      }
    ]
  }

  private getConsultationCompletedSteps(userContext: UserContext, stepInfo: BundleStepInfo): StepStateCard[] {
    const consultationOrderResponse = stepInfo.consultationBookingInfos[0].consultationOrderResponse
    const doctorDetails = consultationOrderResponse.doctor
    const subtitle = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, consultationOrderResponse.startTime, "h:mm A, DD MMM")
    return [
      {
        state: "COMPLETED",
        viewType: "ACTION_ICON",
        views: [
          {
            title: "Schedule Doctor Consultation",
            image: doctorDetails.displayImage,
            action: {
              title: doctorDetails.name,
              subtitle: subtitle,
              // TODO handle for two product codes
              url: ActionUtil.teleconsultationSingle(userContext, stepInfo.stepMeta[0].productCodes[0], stepInfo.consultationBookingInfos[0].consultationOrderResponse.consultationProduct.urlPath, `${stepInfo.consultationBookingInfos[0].booking.id}`, undefined, "CAREFIT"),
              actionType: "NAVIGATION",

            }
          }
        ]
      },
      {
        state: "STARTED",
        viewType: "TEXT_ACTION",
        views: [
          {
            title: "Get medical & lifestyle prescription"
          }
        ]
      }
    ]
  }

  private getPresescriptionGeneratedSteps(userContext: UserContext, stepInfo: BundleStepInfo): StepStateCard[] {
    const consultationOrderResponse = stepInfo.consultationBookingInfos[0].consultationOrderResponse
    const doctorDetails = consultationOrderResponse.doctor
    const subtitle = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, consultationOrderResponse.startTime, "h:mm A, DD MMM")
    return [
      {
        state: "COMPLETED",
        viewType: "ACTION_ICON",
        views: [
          {
            title: "Schedule Doctor Consultation",
            image: doctorDetails.displayImage,
            action: {
              title: doctorDetails.name,
              subtitle: subtitle,
              // TODO handle for two product codes
              url: ActionUtil.teleconsultationSingle(userContext, stepInfo.stepMeta[0].productCodes[0], stepInfo.consultationBookingInfos[0].consultationOrderResponse.consultationProduct.urlPath, `${stepInfo.consultationBookingInfos[0].booking.id}`, undefined, "CAREFIT"),
              actionType: "NAVIGATION",

            }
          }
        ]
      },
      {
        state: "COMPLETED",
        viewType: "TEXT_ACTION",
        views: [
          {
            title: "Get medical & lifestyle prescription"
          }
        ]
      }
    ]
  }
}
