import { DataListingType, IListingItem, WidgetHeader } from "@curefit/apps-common"
import {
    Action,
    ActionCard,
    DataListingWidget,
    DataListingWidgetV2,
    DescriptionWithFooterActionWidget,
    Header,
    InfoCard,
    LabTestListingWidget,
    MedicineView,
    PrescribedMedicationWidget,
    PrescriptionSummaryWidget,
    ProductDetailPage,
    ProductListWidget
} from "../common/views/WidgetView"
import { ConsultationProduct } from "@curefit/care-common"
import * as _ from "lodash"
import {
    BookingDetail,
    ConsultationOrderResponse,
    ConsultationSellableProductItem,
    ConsultationSellableProductResponse,
    DoctorTypeCode,
    LabTest,
    Medicine,
    MedicineIntakeCondition,
    MedicineSlot,
    Prescription,
    Symptom,
    VitalInfoItem,
    MedicineDuration,
    MedicineQuantity,
    BodyPartWorkedOn,
} from "@curefit/albus-client"
import { pluralizeStringIfRequired, TimeUtil } from "@curefit/util-common"
import { CareUtil } from "../util/CareUtil"
import { UserContext } from "@curefit/userinfo-common"
import AppUtil, { CARE_PRESCRIPTION_CUSTOM_EMAIL, CARE_NEW_PRESCRIPTION_LAB_TESTS_DESIGN_SUPPORTED, CARE_NEW_PRESCRIPTION_MEDICINE_SUPPORTED } from "../util/AppUtil"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"

const SUPPORTED_PRESCRIPTION_V2_VERSION = 7.25
const SUPPORTED_MERGED_TESTS_VERSION = 7.71
class PrescriptionView extends ProductDetailPage {
    public header: {
        title: string
        action?: Action | string
        widgetType?: string
    }

    private setHeaderData (bookingDetail: BookingDetail, userContext: UserContext) {
        const consultation: ConsultationOrderResponse = bookingDetail.consultationOrderResponse
        const emailAction: Action = CareUtil.getPrescriptionEmailAction(bookingDetail, userContext)
        const isWeb = AppUtil.isWeb(userContext)
        const isMWeb = AppUtil.isMWeb(userContext)

        this.header = { title: "Prescription" }

        if (isWeb) {
            this.header.action = emailAction && isMWeb ? emailAction : undefined
            this.header.widgetType = isMWeb ? "SEND_EMAIL" : undefined
        } else {
            this.header.action = userContext.sessionInfo.appVersion >= CARE_PRESCRIPTION_CUSTOM_EMAIL ? {actionType: "EMAIL_PRESCRIPTION", meta: {bookingId: bookingDetail.booking.id, consultationId: consultation.id}} : `curefit://sendPrescriptionOnEmail?tcBookingId=${bookingDetail.booking.id}&consultationId=${consultation.id}`
        }
    }

    async buildView(serviceInterfaces: CFServiceInterfaces, userContext: UserContext, bookingDetail: BookingDetail, productId: string, cityId: string, followupProducts?: ConsultationProduct[], vitalInfo?: VitalInfoItem[], consultationProducts?: ConsultationProduct[]) {
        const consultation: ConsultationOrderResponse = bookingDetail.consultationOrderResponse
        const userId = userContext.userProfile.userId
        const vitals = !_.isEmpty(vitalInfo) ? this.getVitals(vitalInfo) : []
        const parentBookingId = bookingDetail.booking.id
        const followUpConsultationId = consultation.id
        const patientId = consultation.patient.id
        const prescription: Prescription = consultation.prescriptionInfo && consultation.prescriptionInfo.prescription || {} as any
        const diagnosticLabtestInfo = consultation.prescriptionInfo && consultation.prescriptionInfo.diagnosticLabtestInfo || {} as any
        const { atHomeLabTests, inCentreLabTests, unMappedLabTests }  = diagnosticLabtestInfo
        const { medicinePrescriptionSet,
            diagnosisPrescriptionSet,
            advicePrescriptionSet,
            doctorAdvicePrescriptionSet,
            patientNoteSet,
            doctorCheckinInfoSet,
            patientSymptomSet,
            bodyPartsWorkedOn,
        } = prescription
        const cityAvailability = CareUtil.getCityAvailabilityForPrescriptionDiagTests(cityId)
        const isWeb = AppUtil.isWeb(userContext)
        const isDesktop = AppUtil.isDesktop(userContext)
        const isNewPrescriptionLabTestsDesignSupported: boolean = isWeb || userContext.sessionInfo.appVersion >= CARE_NEW_PRESCRIPTION_LAB_TESTS_DESIGN_SUPPORTED
        const isNewPrescriptionMedicineSupported: boolean = isWeb || userContext.sessionInfo.appVersion >= CARE_NEW_PRESCRIPTION_MEDICINE_SUPPORTED
        const emailAction: Action = CareUtil.getPrescriptionEmailAction(bookingDetail, userContext)
        const productCode = bookingDetail.booking.productCode
        const displayFollowUpInPrescriptionDetail: boolean = isDesktop
            && consultation
            && !_.isEmpty(consultation.followUpContext)
            && consultation.followUpContext.enabled
            && !_.isEmpty(consultation.followUpContext.followupMembershipDetails)

        let pathalogyTest, radiologyTest, otherTest, combinedTest, items

        this.setHeaderData(bookingDetail, userContext)

        if (isWeb) {
            const prescriptionTitle = "FOLLOW-UP"
            const action: Action = displayFollowUpInPrescriptionDetail ? CareUtil.getFollowupAction(userContext, parentBookingId, followupProducts, followUpConsultationId, bookingDetail.consultationOrderResponse.patient.id, bookingDetail.consultationOrderResponse?.doctor?.id, bookingDetail.consultationOrderResponse?.center?.id, prescriptionTitle) : undefined

            this.widgets.push(new PrescriptionSummaryWidget(consultation.patient.name, TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, consultation.startTime, "DD MMM, hh:mm a"), consultation.doctor.name, action, productCode, emailAction && isDesktop ? emailAction : undefined))
        } else {
            const registrationNumberText = "Registration number: " + consultation.doctor?.registrationNumber
            const doctorDetailAction: Action = {
                actionType: "SHOW_DOCTOR_DETAILS_MODAL",
                meta: {
                    ...consultation.doctor,
                    registrationNumberText,
                    viewCta : "VIEW",
                    experience: CareUtil.isTherapyOnlyDoctorType(bookingDetail.consultationOrderResponse.doctorType) ? undefined : consultation?.doctor?.experience
                }
            }
            this.widgets.push(new PrescriptionSummaryWidget(consultation.patient.name, TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, consultation.startTime, "DD MMM, hh:mm a"), undefined, undefined, undefined, undefined, doctorDetailAction))
        }

        if (!_.isEmpty(vitals) && !isWeb) {
            this.widgets.push(new DataListingWidget("Today's Vitals", DataListingType.GRID_WITHOUT_BULLET, vitals, "VITALS"))
        }
        if (!_.isEmpty(patientSymptomSet)) {
            this.widgets.push(new DataListingWidget("Symptoms", DataListingType.LIST, this.getSymptoms(patientSymptomSet), "SYMPTOMS"))
        }
        if (!_.isEmpty(bodyPartsWorkedOn)) {
            const bodyParts: IListingItem[] = bodyPartsWorkedOn.map((part: BodyPartWorkedOn) => {
                return {
                    title: part.displayValue,
                    comment: part.comments
                }
            })
            if (!_.isEmpty(bodyParts)) {
                this.widgets.push(new DataListingWidgetV2("Body parts catered to", DataListingType.LIST, bodyParts, "BODY_PARTS"))
            }
        }
        if (!_.isEmpty(diagnosisPrescriptionSet)) {
            const diagnosis = diagnosisPrescriptionSet.map((diagnosis: any) => diagnosis.diagnosis).filter((item: any) => !!item)
            if (!_.isEmpty(diagnosis)) {
                this.widgets.push(new DataListingWidget("Diagnosis", DataListingType.LIST, diagnosis, "DIAGNOSIS"))
            }
        }
        if (!_.isEmpty(medicinePrescriptionSet)) {
            isNewPrescriptionMedicineSupported ? this.widgets.push(this.medicineWidgetV2(medicinePrescriptionSet)) : this.widgets.push(this.medicineWidget(medicinePrescriptionSet))
        }
        if ((isWeb || userContext.sessionInfo.appVersion >= SUPPORTED_MERGED_TESTS_VERSION) && ( !_.isEmpty(atHomeLabTests) || !_.isEmpty(inCentreLabTests))) {
            const combinedLabTests = [].concat(atHomeLabTests, inCentreLabTests).filter(items => !!items)
            combinedTest = await this.labtestWidget(serviceInterfaces, patientId, combinedLabTests, "IN_CENTRE_SLOT", "Tests", bookingDetail.booking.id, cityAvailability, cityId, isNewPrescriptionLabTestsDesignSupported, isWeb)
        }
        if (!_.isEmpty(unMappedLabTests)) {
            otherTest = await this.labtestWidget(serviceInterfaces, patientId, unMappedLabTests, "IN_CENTRE_SLOT", "Other Tests", bookingDetail.booking.id, false, cityId, false, isWeb)
        }
        if (isWeb || userContext.sessionInfo.appVersion >= SUPPORTED_MERGED_TESTS_VERSION) {
                items = [].concat(combinedTest, otherTest).filter(items => !!items)
        } else {
            if (!_.isEmpty(atHomeLabTests)) {
                pathalogyTest = await this.labtestWidget(serviceInterfaces , patientId, atHomeLabTests, "AT_HOME_SLOT", "Pathology Tests", bookingDetail.booking.id, cityAvailability, cityId, false, isWeb)
            }
            if (!_.isEmpty(inCentreLabTests)) {
                radiologyTest = await this.labtestWidget(serviceInterfaces, patientId, inCentreLabTests, "IN_CENTRE_SLOT", "Radiology Tests", bookingDetail.booking.id, cityAvailability, cityId, false, isWeb)
            }
            items = [].concat(pathalogyTest, radiologyTest, otherTest).filter(items => !!items)
        }
        const headers = {
            title: "Recommended Lab Tests",
            subTitle: this.getSymptomsText(patientSymptomSet),
            color: "#000000",
            icon: "LABTEST"
        }
        items.length > 0 && this.widgets.push(new LabTestListingWidget(headers, items))
        if (!_.isEmpty(advicePrescriptionSet)) {
            this.widgets.push(new DataListingWidget("Lifestyle Advice", DataListingType.LIST, this.getAdviceInstructions(advicePrescriptionSet), "ADVICE"))
        }
        if (!_.isEmpty(doctorAdvicePrescriptionSet)) {
            this.widgets.push(new DataListingWidget("Advice", DataListingType.LIST, this.getAdviceInstructions(doctorAdvicePrescriptionSet), "ADVICE"))
        }
        if (!_.isEmpty(patientNoteSet)) {
            this.widgets.push(new DataListingWidget("Notes", DataListingType.LIST, this.getPatientNotes(patientNoteSet), "NOTES"))
        }
        if (consultation && !_.isEmpty(consultation.followUpContext) && consultation.followUpContext.enabled
            && !_.isEmpty(consultation.followUpContext.followupMembershipDetails)) {
            const actionUrl = `curefit://selectCareDateV1?productId=${productId}&followUpConsultationId=${followUpConsultationId}&patientId=${patientId}&parentBookingId=${parentBookingId}`
            const heading = consultation.followUpContext.followupMembershipDetails[0].userMessage
            const action: Action = CareUtil.getFollowupAction(userContext, parentBookingId, followupProducts, followUpConsultationId, patientId, consultation?.doctor?.id, consultation?.center?.id)
            this.widgets.push(new DescriptionWithFooterActionWidget("Follow-up", heading, "BOOK FOLLOW-UP", actionUrl, action, undefined, "FOLLOW_UP"))
        }
        if (!_.isEmpty(doctorCheckinInfoSet)) {
            const checkinInfoSet = doctorCheckinInfoSet.filter((info: any) => info.checkinType === "REFERRAL")
            if (!_.isNull(checkinInfoSet)) {
                checkinInfoSet.map((checkinInfo: any) => {
                    if (checkinInfo && !_.isEmpty(checkinInfo.doctorType)) {
                        const consultationSellableProductItems = this.getBookingConsultationProducts(checkinInfo, consultation.prescriptionInfo.sellableConsultationProductResponse)
                        let heading = `${checkinInfo.doctorType.displayValue}`
                        if (!_.isEmpty(checkinInfo.activityDate)) {
                            heading = `${heading} | In ${checkinInfo.activityDate.count} ${pluralizeStringIfRequired(checkinInfo.activityDate.unit.toLowerCase(), checkinInfo.activityDate.count)}`
                        }
                        const description = `As per the doctor recommendation, you should schedule a consultation with ${checkinInfo.doctorType.displayValue}`
                        let action
                        if (!_.isEmpty(consultationSellableProductItems)) {
                            const consultationProduct = (consultationSellableProductItems?.products || []).map(product => consultationProducts.find(consultationProduct => consultationProduct.productId === product.code))
                            if (!_.isEmpty(consultationProduct)) {
                                action = CareUtil.specialistListingAction(userContext, consultationProduct[0], true, patientId, parentBookingId, false, false, false, undefined, undefined, undefined, true, undefined, undefined, true)
                            }
                            // CareUtil.getFollowupAction(userContext, parentBookingId, consultationProduct, followUpConsultationId, patientId, cityId)
                        }
                        this.widgets.push(new DescriptionWithFooterActionWidget("Recommended Specialist", heading, action ? "BOOK NOW" : undefined, undefined, action, description, "SPECIALIST_RECOMENDATION"))
                    }
                })
            }
        }

        if (bookingDetail.booking.subCategoryCode === "CF_ONLINE_CONSULTATION") {
            this.widgets.push(new DescriptionWithFooterActionWidget("Terms and conditions", `This prescription is generated over ${CareUtil.isAudioBookingConsultation(bookingDetail) ? "audio" : "video"} consultation`, undefined, undefined, undefined, undefined, "TNC"))
        }
        return this
    }

    private getBookingConsultationProducts(checkinInfo: { doctorType: DoctorTypeCode }, sellableProduct: ConsultationSellableProductResponse): ConsultationSellableProductItem {
        return sellableProduct.consultationTypes.find(consultationType => consultationType.type === checkinInfo.doctorType.code)
    }

    private getSymptoms(patientSymptomSet: Symptom[]): string[] {
        const symptoms: string[] = []
        patientSymptomSet.map((symptom: Symptom) => symptoms.push(symptom.displayValue))
        return symptoms
    }

    private getSymptomsText(patientSymptomSet: Symptom[]): string {
        if (_.isEmpty(patientSymptomSet)) {
            return "As per the doctor recommendation, you should take up the following test"
        }
        const length = patientSymptomSet.length
        let symptomText = "Need a following test done to rule out " + (length > 1 ? "symptoms of " : "symptom of ")
        if (length === 1) {
            symptomText += patientSymptomSet[0].displayValue
        } else if (length > 1) {
            patientSymptomSet.forEach((symptom: Symptom, index) => {
                symptomText +=  symptom && symptom.displayValue ? (length - 1 === index ? " and " : "") + symptom.displayValue + (length - 1 !== index && length - 2 !== index ? ", " : "") : ""
            })
        }
        return symptomText
    }

    private getVitals(vitalInfo: VitalInfoItem[]): any[] {
        const vitals: any[] = []
        vitalInfo.map((vital: VitalInfoItem) => {
            if (vital.vitalHistoryList.length > 0) {
                let title = this.getVitalTitle(vital)
                if (vital.unit) {
                    title = title + " (" + vital.unit + ")"
                }
                vitals.push({
                    title: title.toUpperCase(),
                    subTitle: vital.vitalHistoryList[vital.vitalHistoryList.length - 1].value
                })
            }
        })
        return vitals
    }

    private getVitalTitle(vital: VitalInfoItem): string {
        switch (vital.vitalName) {
            case "bmi": return "BMI"
            case "height": return "HT"
            case "weight": return "WT"
            case "bp": return "BP"
            case "systolic bp": return "SYS BP"
            case "diastolic bp": return "DIA BP"
            case "temperature": return "TEMP."
            case "respiratory rate": return "RR"
            case "whr": return "WHR"
            case "pulse rate": return "PR"
            case "spo2": return "SPO2"
            case "waist": return "WAIST"
            case "hip": return "HIP"
            default: return vital.vitalName
        }
    }

    private getAdviceInstructions(advicePrescriptionSet: any): string[] {
        const advices: string[] = []
        advicePrescriptionSet.map((advice: any) => advices.push(advice.name))
        return advices
    }

    private getPatientNotes(patientNoteSet: any): string[] {
        const notes: string[] = []
        patientNoteSet.map((noteItem: any) => notes.push(noteItem.note))
        return notes
    }

    private async labtestWidget(serviceInterfaces: CFServiceInterfaces, patientId: number, labTests: LabTest[], category: "AT_HOME_SLOT" | "IN_CENTRE_SLOT", title: string, bookingId: number, showFooter?: boolean, cityId?: string, isNewPrescriptionLabTestsDesignSupported?: boolean, isWeb?: boolean): Promise<ProductListWidget> {
        const items: InfoCard[] = []
        const bundleProductsList: any[] = []
        const labTestCodes: string[] = []
        const testCodeList: string[] = labTests.map(test => test.code)
        const testCodes = testCodeList.join(",")
        const navigationPath: string = CareUtil.getTestListingActionUrl(patientId, bookingId, category, testCodes, testCodes)
        const header: Header = {
            title: title,
            color: "#000000"
        }
        const footer: ActionCard = showFooter === false ? undefined : {
            title: "BOOK TESTS",
            isPrescriptionTests: true,
            ...(isWeb ? {
                cardAction: {
                    actionType: "NAVIGATION",
                    url: navigationPath
                }
            } : {
                action: navigationPath
            })
        }

        labTests.map(labTest => {
            labTest.productSellerResponse?.product && bundleProductsList.push(labTest.productSellerResponse.product)
            labTestCodes.push(labTest.code)
            items.push({
                subTitle: labTest.name.trim().toUpperCase(),
                countParameters: CareUtil.countParameters(labTest.productSellerResponse?.product),
                doctorsComment: labTest.comment,
                testSuggestion: CareUtil.getTestSuggestion(labTest.testDate),
                isCareFlow: true,
            })
        })
        const action: Action = !_.isEmpty(bundleProductsList) ? {actionType: "SHOW_CAROUSEL_LIST", meta: {
            type: "DIAGNOSTIC_TEST_DETAIL_LIST",
            bundleProducts: bundleProductsList
        }} : undefined
        if (showFooter && isNewPrescriptionLabTestsDesignSupported) {
            const sampleCollectionLocationResponse = await serviceInterfaces.healthfaceService.getDiagnosticTestAllowedActionFromCollectionCodes(labTestCodes, bookingId.toString(), cityId)
            footer.homeSampleCollectionAvailable = sampleCollectionLocationResponse.sampleCollectionLocations?.indexOf("ALL_AT_HOME") !== -1 || sampleCollectionLocationResponse.sampleCollectionLocations?.indexOf("HOME_PLUS_CENTRE") !== -1 ? "Home Sample Collection available" : undefined
        }
        return new ProductListWidget("BULLET", header, items, footer, undefined, undefined, undefined, undefined, undefined, action)
    }

    private medicineWidget(medicines: Medicine[]): PrescribedMedicationWidget {
        const header: WidgetHeader = {
            title: "Medicines",
            icon: "MEDICINES"
        }
        const items: MedicineView[] = []
        if (!_.isEmpty(medicines))
            medicines.map((medicine => {
                items.push({
                    type: medicine.medicineType,
                    title: medicine.dosage ? `${medicine.brand} | ${medicine.dosage}` : medicine.brand,
                    brandDetail: medicine.name,
                    subtitle: !_.isEmpty(medicine.medicineFrequency) ? `${medicine.medicineFrequency.count} ${pluralizeStringIfRequired("time", medicine.medicineFrequency.count)}/${medicine.medicineFrequency.unit.toLowerCase()}` : undefined,
                    dosageText: !_.isEmpty(medicine.medicineDuration) ? `${medicine.medicineDuration.count} ${pluralizeStringIfRequired(medicine.medicineDuration.unit.toLowerCase(), medicine.medicineDuration.count)}` : undefined,
                    intakeCondition: !_.isEmpty(medicine.slotList) && medicine.slotList.length > 0 ? this.getIntakeCondition(medicine.intakeCondition ? medicine.intakeCondition.intakeConditionList : undefined, medicine.slotList) : undefined,
                    additionalInstructions: this.getInstructions(medicine.comment, medicine.intakeCondition ? medicine.intakeCondition.intakeConditionList : undefined),
                    tags: !_.isEmpty(medicine.medicineFrequency) && !_.isEmpty(medicine.medicineFrequency.weeklySchedule) ?
                        medicine.medicineFrequency.weeklySchedule.map(val => val.toLowerCase()) : undefined
                })
            }))
        return new PrescribedMedicationWidget(header, items, false)
    }

    private medicineWidgetV2(medicines: Medicine[]): PrescribedMedicationWidget {
        const header: WidgetHeader = {
            title: "Medicines",
            icon: "MEDICINES"
        }
        const items: MedicineView[] = []
        if (!_.isEmpty(medicines))
            medicines.map((medicine => {
                items.push({
                    type: medicine.medicineType,
                    title: medicine.dosage ? `${medicine.brand} | ${medicine.dosage}` : medicine.brand,
                    brandDetail: medicine.name,
                    allIntakeDetails: this.getAllIntakeDetails(medicine),
                    doctorsComment: medicine.comment,
                    medicineDetails: {
                        title : medicine.dosage ? `${medicine.brand} | ${medicine.dosage}` : medicine.brand,
                        chemicalComposition: medicine.genericName?.displayValue,
                        header: "Chemical Formulation"}
                })
            }))
        return new PrescribedMedicationWidget(header, items, true)
    }

    private getAllIntakeDetails(medicine: Medicine): string {
        const medicineQuantity = this.getMedicineQuantity(medicine.medicineQuantity)
        const medicineSlots = this.getMedicineTimings(medicine.slotList, medicine.medicineDosage)
        const intakeCondition = this.getIntakeConditionV2(medicine.intakeCondition?.intakeConditionList)
        const medicineDuration = this.getMedicineDuration(medicine.medicineDuration, medicine.frequency)
        return `${medicineQuantity ? medicineQuantity + `  |  ` : ``}${medicineSlots ? medicineSlots + `  |  ` : ``}${intakeCondition ? intakeCondition + `  |  ` : ``}${medicineDuration ? medicineDuration : ``}`
    }

    private getMedicineQuantity(medicineQuantity: MedicineQuantity): string {
        if (medicineQuantity?.count && medicineQuantity?.unit) {
            return medicineQuantity.count.toString() + " " + medicineQuantity.unit.toLowerCase()
        } else {
            return undefined
        }
    }

    private getMedicineTimings(medicineSlots: MedicineSlot[], medicineDosage: string): string {
        let medicineSlotText: string = ""
        if (!_.isEmpty(medicineSlots)) {
            let i = 0
            for (i = 0 ; i < medicineSlots.length ; i++ ) {
                if (medicineSlots[i].quantity > 0) {
                    if (medicineSlotText) {
                        medicineSlotText += ", "
                    }
                    medicineSlotText += this.capitaliseFirstLetter(medicineSlots[i].slot)
                }
            }
            return medicineSlotText
        } else if (medicineDosage) {
            return medicineDosage
        } else {
            return undefined
        }
    }

    private capitaliseFirstLetter( string: string): string {
        return string[0].toUpperCase() + string.slice(1).toLowerCase()
    }

    private getIntakeConditionV2 (inTakeConditions: MedicineIntakeCondition[]): string {
        let intake: string = undefined
        if (!_.isEmpty(inTakeConditions)) {
            if (_.includes(inTakeConditions, "BEFORE_FOOD")) {
                intake = "Before Food"
            } else if (_.includes(inTakeConditions, "AFTER_FOOD")) {
                intake = "After Food"
            } else if (_.includes(inTakeConditions, "EMPTY_STOMACH")) {
                intake = "On Empty Stomach"
            } else if (_.includes(inTakeConditions, "BEFORE_SLEEP")) {
                intake = "Before Sleep"
            } else if (_.includes(inTakeConditions, "BELOW_THE_TONGUE")) {
                intake = "Below the tongue"
            } else if (_.includes(inTakeConditions, "CHEWABLE")) {
                intake = "Chewable"
            }
        }
        return intake
    }

    private getMedicineDuration (medicineDuration: MedicineDuration, frequency: string): string {
        let frequencyText = ""
        let durationText = ""
        if (frequency) {
            frequencyText += frequency
        }
        if (medicineDuration?.count >= 0 && medicineDuration?.unit) {
            const count = medicineDuration.count
            switch (medicineDuration.unit) {
                case "DAY":
                    durationText += " for " + count + " Days"
                    break
                case "MONTH":
                    durationText += " for " + count + " Months"
                    break
                case "WEEK":
                    durationText += " for " + count + " Weeks"
                    break
                case "YEAR":
                    durationText += " for " + count + " Years"
                    break
                case "SOS":
                    durationText += " As and when required"
                    break
                default :
                    break
            }
        }
        return frequencyText + durationText
    }

    private getIntakeCondition(inTakeConditions: MedicineIntakeCondition[], dosageList: MedicineSlot[]): string {
        const subtitle: string[] = []
        let intake: string = undefined
        if (_.includes(inTakeConditions, "BEFORE_FOOD")) {
            intake = "before"
        } else if (_.includes(inTakeConditions, "AFTER_FOOD")) {
            intake = "after"
        }
        if (intake) {
            dosageList.map((dosage: MedicineSlot) => {
                if (dosage.quantity !== 0) {
                    subtitle.push(`${dosage.quantity} ${intake} ${dosage.slot.toLowerCase()}`)
                }
            })
        } else {
            dosageList.map((dosage: MedicineSlot) => {
                if (dosage.quantity !== 0) {
                    subtitle.push(`${dosage.quantity} at ${dosage.slot.toLowerCase()}`)
                }
            })
        }
        return !_.isEmpty(subtitle) ? subtitle.join(", ") : undefined
    }

    private getInstructions(comment: string, inTakeConditions: MedicineIntakeCondition[]): string[] {
        const instructions: string[] = []
        if (comment) {
            instructions.push(comment)
        }
        if (!_.isEmpty(inTakeConditions)) {
            inTakeConditions.map(condition => {
                switch (condition) {
                    case "BEFORE_SLEEP": instructions.push("Take before sleep")
                        break
                    case "BELOW_THE_TONGUE": instructions.push("Take below the tongue")
                        break
                    case "EMPTY_STOMACH": instructions.push("Take empty stomach")
                        break
                    case "CHEWABLE": instructions.push("Chew the medicine")
                        break
                    case "SOS": instructions.push("Take when needed")
                        break
                }
            })
        }
        return instructions
    }
}
export default PrescriptionView
