import { Action, ActionType } from "../common/views/WidgetView"
import { ConsultationProduct } from "@curefit/care-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { TimeUtil, Timezone } from "@curefit/util-common"
import * as _ from "lodash"
import {
    BookingDetail,
    Center,
    CONSULTATION_MODE,
    Doctor,
    TCAvailableSlotsDetails,
    TCAvailableSlotsResponse
} from "@curefit/albus-client"
import * as momentTz from "moment-timezone"
import { CareDateSelectorPage, PageWidget } from "../page/Page"

import {
    CalloutPageWidget,
    DatesAvailableWidget,
    DateWiseSlots,
    PreferedDoctor,
    PreferedDoctorListWidget,
    TimeSlot,
    TimeSlotCategory
} from "../page/PageWidgets"
import { CareUtil } from "../util/CareUtil"
import { UserContext } from "@curefit/userinfo-common"

class TeleconsultationDatePickerView {

    public datePicker: CareDateSelectorPage

    constructor(product: ConsultationProduct, daywiseSlots: TCAvailableSlotsResponse, center: Center, parentBookingId: number, isReschedule: string, isExternal: string, followUpConsultationId: string, bookingDetail: BookingDetail, offerIds?: string, userContext?: UserContext) {
        const widgets: PageWidget[] = []
        const preferredDoctorMap: { [key: string]: PreferedDoctor } = this.getPreferedDoctorMap(daywiseSlots.preferredDoctorMap)
        widgets.push(this.availableDatesWidget(userContext, product.productId, product.consultationMode, daywiseSlots, parentBookingId, isReschedule, center, isExternal, followUpConsultationId, preferredDoctorMap, offerIds))
        if (_.isEmpty(preferredDoctorMap)) {
            if (product.consultationMode === "ONLINE") {
                // widgets.push(new CalloutPageWidget("VIDEO", "Consult doctor in-person, for chest pain, loss of consciousness, weakness in limb/face, Sudden loss of speech or slurred speech"))
                widgets.push(new CalloutPageWidget("LOCATION", "Visit a hospital immediately for any medical emergencies"))
            } else {
                widgets.push(new CalloutPageWidget("LOCATION", center.address, center.name, "VIEW MAP", `curefit://externalDeepLink?placeUrl=${center.placeUrl}`))
            }
        } else {
            widgets.push(new PreferedDoctorListWidget(_.values(preferredDoctorMap)))
        }

        // As app support tabs so data is being send in tab format
        this.datePicker = {
            key: product.productId,
            userLocation: "bangalore", // TODO check this isWithinBangalore ? "bangalore" : "others",
            sections: [{
                id: "SLOTS",
                name: "Select a slot"
            }],
            lists: { "SLOTS": widgets },
        }
    }

    private availableDatesWidget(userContext: UserContext, productId: string, consultationMode: CONSULTATION_MODE, availableSlotsResponse: TCAvailableSlotsResponse, parentBookingId: number, isReschedule: string, center: Center, isExternal: string, followUpConsultationId: string, preferredDoctorMap?: { [key: string]: PreferedDoctor }, offerIds?: string): DatesAvailableWidget {
        const tz = userContext.userProfile.timezone
        const dates: string[] = TimeUtil.getDays(tz, 15)
        const datesAvailable: DateWiseSlots[] = []
        for (let i = 0; i < dates.length && datesAvailable.length < 15; i++) {
            const dateAppointments: TCAvailableSlotsDetails[] = availableSlotsResponse.formattedAvailableSlotResponseList.filter((x) => TimeUtil.formatEpochInTimeZoneDateFns(tz, x.startTime, "yyyy-MM-dd") === dates[i])
            if (!_.isEmpty(dateAppointments)) {
                let timeslots: TimeSlotCategory[] = []
                timeslots = this.getHourSplitTimeSlots(isExternal, dates[i], dateAppointments, tz)
                if (!_.isEmpty(timeslots)) {
                    const dateWiseSlot: DateWiseSlots = {
                        date: dates[i],
                        dateType: TimeUtil.getMomentForDateString(dates[i], tz).isoWeekday() >= 6 ? "WEEKEND" : "WEEKDAY",
                        timeZones: timeslots
                    }
                    datesAvailable.push(dateWiseSlot)
                }
            }
        }
        const actionUrl = isReschedule === "true" ? `curefit://rescheduleConsultation?productId=${productId}&followUpConsultationId=${followUpConsultationId}&parentBookingId=${parentBookingId}&centerId=${center.id}` : `curefit://carecheckout?productId=${productId}&parentBookingId=${parentBookingId}&centerId=${center.id}&followUpConsultationId=${followUpConsultationId}&offerIds=${offerIds}`
        let actionType: ActionType = "NAVIGATION"
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "APP")
        if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
            if (isReschedule) {
                actionType = "RESCHEDULE_TELECONSULTATION"
            } else {
                actionType = "GET_TELECONSULTATION"
            }
        }
        const actionObj: Action = {
            actionType,
            url: actionUrl
        }
        return new DatesAvailableWidget(datesAvailable, actionUrl, actionObj, preferredDoctorMap, availableSlotsResponse.consultationProductType) // consultationProductType here is productCode which is necessary for old apps to reshedule
    }

    private getHourSplitTimeSlots(isExternal: string, date: string, dateAppointments: TCAvailableSlotsDetails[], timezone: Timezone): TimeSlotCategory[] {
        const timeslots: TimeSlotCategory[] = []
        // TODO refactor it later
        const slot11 = this.getTimeslotBetweenRanges(date, dateAppointments, "00:00", "02:59", timezone)
        if (!_.isEmpty(slot11)) {
            timeslots.push({
                timeSlots: this.parseToViewFormat(isExternal, slot11, timezone),
                title: "12-3AM"
            })
        }

        const slot12 = this.getTimeslotBetweenRanges(date, dateAppointments, "03:00", "05:59", timezone)
        if (!_.isEmpty(slot12)) {
            timeslots.push({
                timeSlots: this.parseToViewFormat(isExternal, slot12, timezone),
                title: "3-6AM"
            })
        }


        const slot1 = this.getTimeslotBetweenRanges(date, dateAppointments, "06:00", "08:59", timezone)
        if (!_.isEmpty(slot1)) {
            timeslots.push({
                timeSlots: this.parseToViewFormat(isExternal, slot1, timezone),
                title: "6-9AM"
            })
        }

        const slot2 = this.getTimeslotBetweenRanges(date, dateAppointments, "09:00", "11:59", timezone)
        if (!_.isEmpty(slot2)) {
            timeslots.push({
                timeSlots: this.parseToViewFormat(isExternal, slot2, timezone),
                title: "9-12AM"
            })
        }

        const slot3 = this.getTimeslotBetweenRanges(date, dateAppointments, "12:00", "14:59", timezone)
        if (!_.isEmpty(slot3)) {
            timeslots.push({
                timeSlots: this.parseToViewFormat(isExternal, slot3, timezone),
                title: "12-3PM"
            })
        }

        const slot4 = this.getTimeslotBetweenRanges(date, dateAppointments, "15:00", "17:59", timezone)
        if (!_.isEmpty(slot4)) {
            timeslots.push({
                timeSlots: this.parseToViewFormat(isExternal, slot4, timezone),
                title: "3-6PM"
            })
        }
        const slot5 = this.getTimeslotBetweenRanges(date, dateAppointments, "18:00", "20:59", timezone)
        if (!_.isEmpty(slot5)) {
            timeslots.push({
                timeSlots: this.parseToViewFormat(isExternal, slot5, timezone),
                title: "6-9PM"
            })
        }
        const slot13 = this.getTimeslotBetweenRanges(date, dateAppointments, "21:00", "23:59", timezone)
        if (!_.isEmpty(slot13)) {
            timeslots.push({
                timeSlots: this.parseToViewFormat(isExternal, slot13, timezone),
                title: "9-12PM"
            })
        }

        return timeslots
    }

    private getPreferedDoctorMap(preferredDoctorMap?: { [key: string]: Doctor }): { [key: string]: PreferedDoctor } {
        const preferedDoctorValueMap: { [key: string]: PreferedDoctor } = {}
        let index: number = 0
        if (!_.isEmpty(preferredDoctorMap)) {
            Object.keys(preferredDoctorMap).forEach(key => {
                preferedDoctorValueMap[key] = {
                    id: key,
                    name: `${preferredDoctorMap[key].name}`,
                    color: this.getColor(index++),
                    type: CareUtil.getPreferredDoctorType(preferredDoctorMap[key].preferredDoctorType, preferredDoctorMap[key].preferredDoctorCode)
                }
            })
        }
        return preferedDoctorValueMap
    }

    private getTimeslotBetweenRanges(date: string, appointmentDetails: TCAvailableSlotsDetails[], startTime: string, endTime: string, timezone: Timezone): TCAvailableSlotsDetails[] {
        const startRange = TimeUtil.getMomentForDateString(date + " " + startTime, timezone, "YYYY-MM-DD HH:mm a")
        const endRange = TimeUtil.getMomentForDateString(date + " " + endTime, timezone, "YYYY-MM-DD HH:mm a")
        return appointmentDetails.filter((x) => momentTz.tz(x.startTime, timezone).isBetween(startRange, endRange, "second", "[]"))
    }

    private getColor(index: number) {
        const colors = ["#ff316d", "#778dff"]
        return colors[index % 2]
    }


    private parseToViewFormat(showEnd: string, slots: TCAvailableSlotsDetails[], timezone: Timezone): TimeSlot[] {
        const timeSlots: TimeSlot[] = []
        slots.forEach(element => {
            const timeslot: TimeSlot = {
                availableType: element.isAvailable === true ? "AVAILABLE" : "UNAVAILABLE",
                doctorIdList: !_.isEmpty(element.doctorIdList) ? [element.doctorIdList[0]] : undefined,
                text: showEnd === "true" ? `${TimeUtil.formatEpochInTimeZone(timezone, element.startTime, "hh:mm")}-${TimeUtil.formatEpochInTimeZone(timezone, element.endTime, "hh:mm")}` : TimeUtil.formatEpochInTimeZone(timezone, element.startTime, "hh:mm"),
                startTime: element.startTime,
                endTime: element.endTime,
                preferedDoctorIdList: element.preferredDoctorIdList
            }
            timeSlots.push(timeslot)
        })
        return timeSlots
    }

}
export default TeleconsultationDatePickerView
