import {
    CalloutPageWidget,
    DatesAvailableWidget,
    DateWiseSlots,
    MultiConsultationDetailWidget,
    MultiConsultationItem,
    PreferedDoctor,
    PreferedDoctorListWidget,
    TimeSlot,
    TimeSlotCategory
} from "../../page/PageWidgets"
import { Action, ProductDetailPage } from "../../common/views/WidgetView"
import { Center, ConsultationProduct, Doctor } from "@curefit/care-common"
import { FoodProduct as Product } from "@curefit/eat-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { TimeUtil, Timezone } from "@curefit/util-common"
import * as _ from "lodash"
import { MultiConsultationRequest, TCAvailableSlotsDetails, TCAvailableSlotsResponse } from "@curefit/albus-client"
import * as momentTz from "moment-timezone"
import { DATE_PICKER_HEADER_TYPE, PageWidget } from "../../page/Page"
import { CareUtil } from "../../util/CareUtil"
import { UserContext } from "@curefit/userinfo-common"

class MultiConsultationDatePickerView extends ProductDetailPage {
    public footerWidget: PageWidget
    public header: { title: string, type?: DATE_PICKER_HEADER_TYPE, action?: Action }
    constructor(userContext: UserContext, daywiseSlots: TCAvailableSlotsResponse, productCode: string, multiConsultationRequest: MultiConsultationRequest, products: Product[], center: Center, isMultiCenterSupported: boolean) {
        super()
        const preferredDoctorMap: { [key: string]: PreferedDoctor } = this.getPreferedDoctorMap(daywiseSlots.preferredDoctorMap)
        if (products.length > 1) {
            this.widgets.push(this.getMultiConsultationDetailWidget(userContext, multiConsultationRequest, productCode, products))
        }
        this.widgets.push(this.availableDatesWidget(userContext, productCode, products, multiConsultationRequest, daywiseSlots, preferredDoctorMap))
        if (!_.isEmpty(preferredDoctorMap)) {
            this.footerWidget = new PreferedDoctorListWidget(_.values(preferredDoctorMap))
        } else if (products.length === 1) {
            this.footerWidget = new CalloutPageWidget("LOCATION", center.address, center.name, "VIEW MAP", `curefit://externalDeepLink?placeUrl=${center.placeUrl}`)
        }
        this.header = CareUtil.getDatePickerViewHeader(isMultiCenterSupported, center)
    }


    private getMultiConsultationDetailWidget(userContext: UserContext, multiConsultationRequest: MultiConsultationRequest, currentProductCode: string, products: Product[]): MultiConsultationDetailWidget {
        const items: MultiConsultationItem[] = []
        const currentIndex = products.findIndex(product => product.productId === currentProductCode)
        products.map(product => {
            const consultationProduct = <ConsultationProduct>product
            const productIndex = products.findIndex(product => product.productId === consultationProduct.productId)
            if (!_.isEmpty(multiConsultationRequest.selectedTimeSlots) && multiConsultationRequest.selectedTimeSlots[consultationProduct.productId]) {
                const selectedSlot = multiConsultationRequest.selectedTimeSlots[consultationProduct.productId]
                items.push({
                    title: CareUtil.getDoctorTypeAsString(consultationProduct.doctorType),
                    subtitle: `${TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, selectedSlot.startTime, "D MMM | h:mm A")}`,
                    state: productIndex < currentIndex ? "PAST" : "FUTURE"
                })
            } else {
                items.push({
                    title: CareUtil.getDoctorTypeAsString(consultationProduct.doctorType),
                    subtitle: `${consultationProduct.duration / (60 * 1000)} mins slot`,
                    state: currentIndex === productIndex ? "PRESENT" : "FUTURE"
                })
            }
        })
        return new MultiConsultationDetailWidget(items)
    }



    private availableDatesWidget(userContext: UserContext, productCode: string, products: Product[], multiConsultationRequest: MultiConsultationRequest, availableSlotsResponse: TCAvailableSlotsResponse, preferredDoctorMap?: { [key: string]: PreferedDoctor }): DatesAvailableWidget {
        const tz = userContext.userProfile.timezone
        const dates: string[] = TimeUtil.getDays(tz, 7)
        const datesAvailable: DateWiseSlots[] = []
        const slotAvailableDates: string[] = []
        if (availableSlotsResponse && !_.isEmpty(availableSlotsResponse.formattedAvailableSlotResponseList)) {
            const datesMap: { [key: string]: TCAvailableSlotsDetails[]} = _.groupBy(
                availableSlotsResponse.formattedAvailableSlotResponseList,
                slot => { return TimeUtil.formatEpochInTimeZone(tz, slot.startTime)
            })
            for (let i = dates.length - 1 || 0 ; i >= 0 && datesAvailable.length < 7; i--) {
                const dateAppointments: TCAvailableSlotsDetails[] = datesMap[dates[i]]
                if (!_.isEmpty(dateAppointments)) {
                    const isSlotAvailableToday = dateAppointments.findIndex(slot => slot.isAvailable) !== -1
                    isSlotAvailableToday && slotAvailableDates.unshift(dates[i])
                    const timeslots: TimeSlotCategory[] = this.getHourSplitTimeSlots(userContext, "false", dateAppointments)
                    if (!_.isEmpty(timeslots)) {
                        datesAvailable.unshift(CareUtil.getDatewiseSlot(
                            dates[i],
                            tz,
                            timeslots,
                            !isSlotAvailableToday,
                            slotAvailableDates,
                            "CONSULTATION")
                        )
                    }
                }
            }
        } else {
            datesAvailable.push(CareUtil.getDatewiseSlot(dates[0], tz, [], true, [], "CONSULTATION"))
        }
        const actionObj: Action = this.getAction(userContext, productCode, products, multiConsultationRequest)
        return new DatesAvailableWidget(datesAvailable, undefined, actionObj, preferredDoctorMap, undefined, false, undefined, products.length > 1 ? true : false)
    }

    private getHourSplitTimeSlots(userContext: UserContext, isExternal: string, dateAppointments: TCAvailableSlotsDetails[]): TimeSlotCategory[] {
        const timeslots: TimeSlotCategory[] = []
        const timeSlotsMap = <{[key: string]: TCAvailableSlotsDetails[]}>CareUtil.getTimeSlotMap(dateAppointments, userContext.userProfile.timezone)
        if (!_.isEmpty(timeSlotsMap)) {
            Object.keys(timeSlotsMap).sort().map(timeSlotKey => {
                if (!_.isEmpty(timeSlotsMap[timeSlotKey])) {
                    timeslots.push(this.parseToViewFormat(
                        userContext,
                        isExternal,
                        timeSlotsMap[timeSlotKey],
                        timeSlotKey,
                    ))
                }
            })
        }
        return timeslots
    }

    private getResheduleAction(userContext: UserContext, productCode: string, products: Product[], multiConsultationRequest: MultiConsultationRequest, currentIndex: number): Action {
        const dependentCodes = multiConsultationRequest.productCodes.slice(currentIndex + 1, multiConsultationRequest.productCodes.length)
        const dependentBookedSlot = multiConsultationRequest.selectedTimeSlots[dependentCodes[0]]
        const dependentBookingIds: number[] = []
        dependentCodes.map(code => dependentBookingIds.push(multiConsultationRequest.selectedTimeSlots[code].bookingId))
        return {
            actionType: "RESCHEDULE_MULTI_CONSULTATION",
            meta: {
                centerId: multiConsultationRequest.centerId,
                productCode: productCode,
                code: productCode,
                dependentBookedSlot,
                parentBookingId: multiConsultationRequest.parentBookingId,
                alertInfo: {
                    title: "Reschedule appointment",
                    subTitle: "Choosing this slot will cancel following appointments:",
                    bullets: dependentCodes.map(code => {
                        return {
                            leftText: CareUtil.getDoctorTypeAsString((<ConsultationProduct>(products.find(product => product.productId === code))).doctorType),
                            rightText: `${TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, multiConsultationRequest.selectedTimeSlots[code].startTime, "D MMM | h:mm A")}`
                        }
                    })
                },
                action: {
                    actionType: "RESCHEDULE_MULTI_CONSULTATION",
                    meta: {
                        centerId: multiConsultationRequest.centerId,
                        productCode: productCode,
                        code: productCode,
                        dependentBookingIds,
                        parentBookingId: multiConsultationRequest.parentBookingId,
                    }
                }
            }
        }
    }
    private getAction(userContext: UserContext, productCode: string, products: Product[], multiConsultationRequest: MultiConsultationRequest): Action {
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "DESKTOP")
        const currentIndex = multiConsultationRequest.productCodes.findIndex(code => code === productCode)
        if (currentIndex < multiConsultationRequest.productCodes.length - 1) {
            if (multiConsultationRequest.isReschedule) {
                return this.getResheduleAction(userContext, productCode, products, multiConsultationRequest, currentIndex)
            } else {
                return {
                    actionType: userAgent === "DESKTOP" || userAgent === "MBROWSER" ?  "GET_SLOTS_MULTI_CONSULTATION" : "NAVIGATION",
                    url: `curefit://multiConsultationSlots`,
                    meta: {
                        centerId: multiConsultationRequest.centerId,
                        productCodes: multiConsultationRequest.productCodes,
                        currentProductCode: multiConsultationRequest.productCodes[currentIndex + 1],
                        parentBookingId: multiConsultationRequest.parentBookingId,
                        patientId: multiConsultationRequest.patientId,
                        selectedTimeSlots: multiConsultationRequest.selectedTimeSlots
                    }
                }
            }
        } else {
            if (multiConsultationRequest.isReschedule) {
                return {
                    actionType: "RESCHEDULE_MULTI_CONSULTATION",
                    meta: {
                        centerId: multiConsultationRequest.centerId,
                        productCode: productCode,
                        code: productCode,
                        // productCodes: multiConsultationRequest.productCodes,
                        // currentProductCode: multiConsultationRequest.productCodes[currentIndex + 1],
                        parentBookingId: multiConsultationRequest.parentBookingId,
                        // patientId: multiConsultationRequest.patientId,
                        // selectedTimeSlots: multiConsultationRequest.selectedTimeSlots
                    }
                }
            } else {
                return {
                    actionType: "BOOK_MULTI_CONSULTATION",
                    meta: {
                        centerId: multiConsultationRequest.centerId,
                        productCodes: multiConsultationRequest.productCodes,
                        currentProductCode: multiConsultationRequest.productCodes[currentIndex + 1],
                        parentBookingId: multiConsultationRequest.parentBookingId,
                        patientId: multiConsultationRequest.patientId,
                        selectedTimeSlots: multiConsultationRequest.selectedTimeSlots
                    }
                }
            }
        }
    }

    private getPreferedDoctorMap(preferredDoctorMap?: { [key: string]: Doctor }): { [key: string]: PreferedDoctor } {
        const preferedDoctorValueMap: { [key: string]: PreferedDoctor } = {}
        let index: number = 0
        if (!_.isEmpty(preferredDoctorMap)) {
            Object.keys(preferredDoctorMap).forEach(key => {
                preferedDoctorValueMap[key] = {
                    id: key,
                    name: `${preferredDoctorMap[key].name}`,
                    color: this.getColor(index++),
                    type: CareUtil.getPreferredDoctorType(preferredDoctorMap[key].preferredDoctorType, preferredDoctorMap[key].preferredDoctorCode)
                }
            })
        }
        return preferedDoctorValueMap
    }

    private getTimeslotBetweenRanges(date: string, appointmentDetails: TCAvailableSlotsDetails[], startTime: string, endTime: string, timezone: Timezone): TCAvailableSlotsDetails[] {
        const startRange = TimeUtil.getMomentForDateString(date + " " + startTime, timezone, "YYYY-MM-DD HH:mm a")
        const endRange = TimeUtil.getMomentForDateString(date + " " + endTime, timezone, "YYYY-MM-DD HH:mm a")
        return appointmentDetails.filter((x) => momentTz.tz(x.startTime, timezone).isBetween(startRange, endRange, "second", "[]"))
    }

    private getColor(index: number) {
        const colors = ["#ff316d", "#778dff"]
        return colors[index % 2]
    }


    private parseToViewFormat(userContext: UserContext, showEnd: string, slots: TCAvailableSlotsDetails[], timeSlotKey: string): { timeSlots: TimeSlot[], title: string } {
        const timeSlots: TimeSlot[] = []
        const tz = userContext.userProfile.timezone
        slots.forEach(element => {
            let listingCode: string, doctorId: number
            if (!_.isEmpty(element.preferredDoctorIdList)) {
                doctorId = element.preferredDoctorIdList[0]
            }
            if (!_.isEmpty(element.doctorMetaList)) {
                const doctorMeta = doctorId ? element.doctorMetaList.find(doctorMetaItem => doctorMetaItem.doctorId === doctorId) : element.doctorMetaList[0]
                if (!_.isEmpty(doctorMeta)) {
                    listingCode = doctorMeta.listingCode
                }
            }
            const timeslot: TimeSlot = {
                availableType: element.isAvailable === true ? "AVAILABLE" : "UNAVAILABLE",
                doctorIdList: !_.isEmpty(element.doctorMetaList) ? [element.doctorMetaList[0].doctorId] : undefined,
                text: showEnd === "true" ? `${TimeUtil.formatEpochInTimeZone(tz, element.startTime, "hh:mm")}-${TimeUtil.formatEpochInTimeZone(tz, element.endTime, "hh:mm")}` : TimeUtil.formatEpochInTimeZone(tz, element.startTime, "hh:mm"),
                startTime: element.startTime,
                endTime: element.endTime,
                preferedDoctorIdList: element.preferredDoctorIdList,
                listingCode
            }
            timeSlots.push(timeslot)
        })
        return {
            timeSlots,
            title: CareUtil.getTimeSlotTitleFromKey(timeSlotKey)
        }
    }

}
export default MultiConsultationDatePickerView
