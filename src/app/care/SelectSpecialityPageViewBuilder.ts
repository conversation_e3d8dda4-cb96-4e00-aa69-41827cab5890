import { inject, injectable } from "inversify"
import * as _ from "lodash"
import {
    ALBUS_CLIENT_TYPES,
    ConsultationSellableProductResponse,
    DOCTOR_TYPE,
    IHealthfaceService
} from "@curefit/albus-client"
import { UserContext } from "@curefit/userinfo-common"
import { ConsultationProduct } from "@curefit/care-common"
import SelectSpecialityPageView from "./SelectSpecialityPageView"
import { SpecialityItem } from "../page/PageWidgets"
import { CareUtil } from "../util/CareUtil"
import AppUtil from "../util/AppUtil"
import MindTherapySelectSpecialityView from "./MindTherapySelectSpecialityView"
import { ProductDetailPage } from "../common/views/WidgetView"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"

@injectable()
export class SelectSpecialityPageViewBuilder {

    constructor(
        @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService
    ) {
    }

    async getTherapyBookingPage(userContext: UserContext, product: ConsultationProduct, doctorType: DOCTOR_TYPE, parentBookingId?: number): Promise<ProductDetailPage> {
        const userId = userContext.userProfile.userId
        const patientListPromise = this.healthfaceService.getAllPatients(userId)
        if (AppUtil.isTherapistRecommendationSupported(userContext)) {
            const patientList = await patientListPromise
            const selfPatient = !_.isEmpty(patientList) ? _.find(patientList, patient => patient.relationship === "Self") : undefined
            if (selfPatient) {
                const preferredTherapist = await this.healthfaceService.getPreferredDoctorForDoctorType(userId, selfPatient.id, "MIND_THERAPIST")
                // const preferredPsychiatrist = await this.healthfaceService.getPreferredDoctorForDoctorType(userId, selfPatient.id, "PSYCHIATRIST")
                // const patientMedicalData = await this.healthfaceService.getPatientPreferenceData("MIND_THERAPY", selfPatient.id)
                return new MindTherapySelectSpecialityView(userContext, patientList, product, doctorType, selfPatient.id, false, preferredTherapist, undefined, parentBookingId)
            } else {
                return new MindTherapySelectSpecialityView(userContext, patientList, product, doctorType)
            }
        } else {
            const consultationSellableProduct: ConsultationSellableProductResponse = await this.healthfaceService.getConsultationSellableProducts(userContext.userProfile.cityId, doctorType)
            const consultationtypes = consultationSellableProduct && consultationSellableProduct.consultationTypes
            let productList: SpecialityItem[]
            if (consultationtypes && consultationtypes.length > 0) {
                productList = await Promise.all(_.map(consultationtypes, async consultationItem => {
                    const productPromises = _.map(consultationItem.products, async product => {
                        return <ConsultationProduct>await this.catalogueService.getProduct(product.code)
                    })
                    const productDetails = await Promise.all(productPromises)
                    const details = CareUtil.getDoctorDetails(consultationItem.type)
                    return {
                        title: details && details.title,
                        subTitle: productDetails && productDetails[0] && productDetails[0].duration ? `${Math.round(productDetails[0].duration / 60000)} min session` : undefined,
                        description: details && details.description,
                        imageUrl: details && details.imageUrl,
                        action: CareUtil.getConsultationPreBookingActions(userContext, productDetails, !userContext.sessionInfo.isUserLoggedIn, await patientListPromise, [], undefined, undefined, parentBookingId),
                    }
                }))
            }
            return new SelectSpecialityPageView(userContext, product.productId, doctorType, productList, await patientListPromise, product, parentBookingId)
        }
    }

}

export default SelectSpecialityPageViewBuilder
