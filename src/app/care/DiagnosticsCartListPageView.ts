import * as _ from "lodash"
import { Action, DiagnosticCartListingWidget, DiagnosticCartTestItem, DiagnosticTestItem, EmptyDiagnosticCartListingWidget, ProductDetailPage, TestDetailWidget } from "../common/views/WidgetView"
import { DiagnosticProduct, DiagnosticAllowedLocations, DiagnosticTestProduct } from "@curefit/care-common"
import { PackOffersResponse, OfferV2 } from "@curefit/offer-common"
import { OfferUtil } from "@curefit/base-utils"
import { HealthfaceProductInfo, SampleCollectionLocationResponse, Patient } from "@curefit/albus-client"
import AppUtil, { CARE_COLLAPSIBLE_OFFER_SUPPORTED, NEW_PRESCRIPTION_LAB_TEST_BOOKING_PAGE } from "../util/AppUtil"
import {
    IProductDetailPage,
    IDiagnosticCartTestSummary, IDiagnosticCartTestItem, IPaymentDetailsWidgetV2, InfoCard, IDiagnosticsCartAddonListWidget, IDiagnosticsCartAddonListWidgetAction
} from "@curefit/apps-common"
import { UserContext } from "@curefit/userinfo-common"
import CareUtil from "../util/CareUtil"
import { InstructionItem } from "@curefit/product-common"
import { PaymentDetailsWidget, PriceComponent } from "../order/OrderViewBuilder"
import { Order } from "@curefit/order-common"
import { BillingInfo, RUPEE_SYMBOL } from "@curefit/finance-common"
import { DiagnosticsCart } from "@curefit/fuse-node-client"
import { DiagnosticCardAddOnListWidget } from "../page/PageWidgets"

class DiagnosticsCartListPageView extends IProductDetailPage {
    private pageTitle: string
    constructor(userContext: UserContext, productCodes: string[], isNotLoggedIn?: boolean, productInfos?: HealthfaceProductInfo[], tests?: DiagnosticProduct[], offers?: PackOffersResponse, cartOffers?: OfferV2[], diagnosticTestInstruction?: InstructionItem[], order?: Order, billingInfo?: BillingInfo, cartResponse?: DiagnosticsCart, recommendedAddons?: DiagnosticTestProduct[], addonTestDetails?: DiagnosticProduct[], patientList?: Patient[]) {
        const isWeb = AppUtil.isWeb(userContext),
          isDesktop = AppUtil.isDesktop(userContext),
          cartId = cartResponse?.id
        let diagnosticCartListingWidget,
          collpsibleOfferWidget,
          paymentDetailWidget,
          recommendedAddonsWidget

        super()

        this.pageTitle = "Lab Tests"
        this.widgets = []
        this.leftWidgets = []
        this.rightWidgets = []

        if (_.isEmpty(productCodes)) {
            const data = { emptyText: "Your cart is empty", info: "Choose from a range of preventive, chronic and lifestyle related tests we offer", imageUrl: "/image/carefit/empty-diagnostic-cart.svg"}
            const emptyCartAction: Action = {actionType: "NAVIGATION", url: "curefit://listpage?pageId=clphcu", title: "BOOK TESTS ON CARE.FIT"}
            const emptyCartWidget = new EmptyDiagnosticCartListingWidget(data, emptyCartAction)
            this.widgets.push(emptyCartWidget)
            this.action = undefined
        } else {
            const items: IDiagnosticCartTestItem[] = []
            let offerIds: string[] = []
            const bundleProductsList: any[] = []
            const productOffersList: OfferV2[] = []
            const patientDetails = patientList?.find(patient => patient.id ===  Number(cartResponse?.cartMetaData?.patientId))
            let totalParameters = 0, totalReportEta = 0
            tests.map(test => {
                if (_.isEmpty(test)) {
                    return null
                }
                const offerDetails = OfferUtil.getPackOfferAndPrice(test, offers)
                const newOfferIds = _.map(offerDetails.offers, offer => { return offer.offerId })
                const membershipInfo = productInfos.find(productInfo => productInfo.baseSellableProduct.productCode === test.productId)

                const price = offerDetails.price
                offerDetails?.offers.map(offer => {productOffersList.push(offer)})
                offerIds = offerIds.concat(newOfferIds)
                const productInfo = membershipInfo?.baseSellableProduct?.diagnosticProductResponse

                const countTests = CareUtil.countParameters(membershipInfo?.baseSellableProduct?.diagnosticProductResponse)
                totalParameters += countTests
                const countTestsCtaText = countTests > 1 ? countTests.toString() + " Tests" : countTests.toString() + " Test"
                const reportingTat = membershipInfo?.baseSellableProduct?.diagnosticProductResponse?.reportingTat
                totalReportEta = totalReportEta <= reportingTat ? reportingTat : totalReportEta
                const reportReadyEta = reportingTat ? "Report in " + reportingTat + " Hrs" : undefined
                const action: Action = { actionType: "REMOVE_ITEM_FROM_DIAGNOSTIC_CART", meta: { productCode: test.productId }}
                productInfo.countParametersText = countTestsCtaText
                productInfo.reportReadyText = reportReadyEta
                items.push({ productCode: test.productId, title: test.title, price, testCountString: countTestsCtaText, countTestsCtaText, action })
                bundleProductsList.push(productInfo)
            })
            const testListAction: Action = !_.isEmpty(bundleProductsList) ? {actionType: "SHOW_CAROUSEL_LIST", meta: {
                type: "DIAGNOSTIC_TEST_DETAIL_LIST",
                bundleProducts: bundleProductsList
            }} : undefined
            const patientData = { name: patientDetails.name,
                detailText: patientDetails.gender + " | " + patientDetails.age + " Yrs"
            }
            const testsSummary = this.getTestsSummary(totalParameters, productCodes.length, totalReportEta)

            diagnosticCartListingWidget = new DiagnosticCartListingWidget(items, testListAction, patientData, testsSummary)
            recommendedAddonsWidget = !_.isEmpty(recommendedAddons) ? this.getDiagnosticsCartAddonListWidget(userContext, isNotLoggedIn, cartResponse, productCodes[0], recommendedAddons, addonTestDetails, offers, patientList) : undefined
            const homeCollectionChargesApplicable = order.collectionCharges?.total > 0
            collpsibleOfferWidget = CareUtil.getCollapsibleOfferWidget(cartOffers, userContext, homeCollectionChargesApplicable)
            paymentDetailWidget = this.getPaymentDetailWidget(userContext, order, billingInfo)

            const actionUrl = `curefit://selectCareDateV1?patientId=${patientDetails.id}&productId=${tests[0].productId}&type=DIAGNOSTICS&category=AT_HOME_SLOT&nextAction=checkout&productCodes=${productCodes.join(",")}&cartId=${cartId}`
            const cartAction = this.getCartAction(userContext, actionUrl, diagnosticTestInstruction, order)

            this.actions = [cartAction]

            if (isDesktop) {
                this.leftWidgets.push(diagnosticCartListingWidget)
                recommendedAddonsWidget && this.leftWidgets.push(recommendedAddonsWidget)
                collpsibleOfferWidget && this.rightWidgets.push(collpsibleOfferWidget)
                this.rightWidgets.push(paymentDetailWidget)
            } else {
                this.widgets.push(diagnosticCartListingWidget)
                collpsibleOfferWidget && this.widgets.push(collpsibleOfferWidget)
                recommendedAddonsWidget && this.widgets.push(recommendedAddonsWidget)
                this.widgets.push(paymentDetailWidget)
            }

            if (isWeb) {
                this.action = cartAction
            } else {
                this.actions = [cartAction]
            }
        }
    }

    private getDiagnosticsCartAddonListWidget(userContext: UserContext, notLoggedIn: boolean, careCart: DiagnosticsCart, productId: string, bundleProducts: DiagnosticTestProduct[], addOnProductInfos?: DiagnosticProduct[], offers?: PackOffersResponse, patientsList?: Patient[]): IDiagnosticsCartAddonListWidget {
        const infoCards: InfoCard[] = []
        const isDesktop = AppUtil.isDesktop(userContext)
        let widget: IDiagnosticsCartAddonListWidget
        let action: IDiagnosticsCartAddonListWidgetAction

        bundleProducts.map((product, index) => {
            const countTests = CareUtil.countParameters(product)
            const cartAction = CareUtil.getItemAction(notLoggedIn, careCart, product.code, patientsList)
            const productInfo = addOnProductInfos?.find(addOnProduct => product.code === addOnProduct.productId)
            const offerDetails = OfferUtil.getPackOfferAndPrice(productInfo, offers)
            product.addOnPrice = offerDetails.price
            product.countParameters = countTests
            product.countParametersText = `${countTests} ${countTests > 1 ? "Tests" : "Test"}`
            product.reportReadyText = `Reports in ${product.reportingTat} Hrs`
            // Commenting this for disabling ADD option in Diagnostic Test Modal.
            // product.isAddon = "true"
            // product.cartAction = cartAction

            infoCards.push({
                title: product.name,
                image: product.imageUrl,
                productCode: product.code,
                moreIndex: index,
                countParameters: product.countParameters,
                cartAction: cartAction,
                price: offerDetails.price
            })
        })

        action = {
            actionType: CareUtil.getNewCarouselListActionType(userContext, "SHOW_DIAGNOSTICS_TEST_DETAIL_LIST"),
            meta: {
                    type: "DIAGNOSTIC_TEST_DETAIL_LIST",
                    productId: productId,
                    bundleProducts: bundleProducts
            }
        }

        widget = new DiagnosticCardAddOnListWidget(infoCards, "Recommended Addons", action, undefined, true, "Get better prices for these tests")

        return widget
    }

    private getTestsSummary(totalParameters: number, totaltests: number, totalReportEta: number): IDiagnosticCartTestSummary {
        const testDetails = `${totaltests}${totaltests > 1 ? ` ITEMS` : ` ITEM`} | ${totalParameters}${totalParameters > 1 ? ` TESTS` : ` TEST`}`
        const reportDetails = `REPORTS IN ${totalReportEta} HRS`
        return {testDetails, reportDetails}
    }

    private getCartAction(userContext: UserContext, actionUrl: string, diagnosticTestInstruction: InstructionItem[], order: Order): Action {
       const isWeb = userContext.sessionInfo.userAgent !== "APP"
       const totalPrice = order.totalPayable
       const action: Action = {
            actionType: "SHOW_CARE_INSTRUCTION_MODAL",
            title: isWeb ? "Schedule Tests" : undefined,
            disabled: false,
            meta: {
                header: {
                    title: "Instructions"
                },
                instructions: diagnosticTestInstruction,
                action: {
                    actionType: "SELECT_ADDRESS_AND_NAVIGATE",
                    url: actionUrl,
                    title: "Proceed With Booking"
                },
                showBookLater: false,
                diagnosticsCart: true,
            },
            cartInfo: {
                totalPrice: "Total " + RUPEE_SYMBOL + totalPrice,
                rightTitle: "Schedule Tests"
            }
        }
        return action
    }

    private getPaymentDetailWidget(userContext: UserContext, order: Order, billingInfo: BillingInfo): PaymentDetailsWidget {
        const priceDetails: PriceComponent[] = []
        const discount = billingInfo?.discount
        const isWeb = AppUtil.isWeb(userContext)
        priceDetails.push({title: "Total", value: billingInfo.mrp.toString()})
        discount > 0 && priceDetails.push({title: "Product Discount", value: discount.toString(), isDiscount: true})
        const totalPayable = billingInfo.total + (order.collectionCharges?.total || 0)
        order.collectionCharges?.total > 0 && priceDetails.push({title: "Home Sample Collection Charges", value: order.collectionCharges?.total.toString()})
        priceDetails.push({title: "Total Payable", value: totalPayable.toString()})
        const paymentDetailWidget: PaymentDetailsWidget | IPaymentDetailsWidgetV2 = {
            widgetType: isWeb ? "PAYMENT_DETAILS_WIDGET_V2" : "PAYMENT_DETAILS_WIDGET",
            priceDetails: priceDetails,
            finalPrice: {
                listingPrice: _.round(billingInfo.total, 2),
                mrp: _.round(billingInfo.total, 2),
                currency: billingInfo.currency
            }
        }
        return paymentDetailWidget
    }
}

export default DiagnosticsCartListPageView
