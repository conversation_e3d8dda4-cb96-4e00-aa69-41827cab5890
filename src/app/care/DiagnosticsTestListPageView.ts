import * as _ from "lodash"
import { Action, DiagnosticTestItem, ProductDetailPage, TestDetailWidget } from "../common/views/WidgetView"
import { DiagnosticProduct, DiagnosticAllowedLocations } from "@curefit/care-common"
import { PackOffersResponse, OfferV2 } from "@curefit/offer-common"
import { OfferUtil } from "@curefit/base-utils"
import { BookingDetail, HealthfaceProductInfo, SampleCollectionLocationResponse } from "@curefit/albus-client"
import AppUtil, { CARE_COLLAPSIBLE_OFFER_SUPPORTED, NEW_PRESCRIPTION_LAB_TEST_BOOKING_PAGE, UNMAPPED_TESTS_WIDGET_SUPPORTED } from "../util/AppUtil"
import { ProductOfferWidgetV2, ProductOffer, ActionType, IFullFilledDetailsV2, ICareDiagnosticSellerWidget, IUnmappedTestsWidget } from "@curefit/apps-common"
import { UserContext } from "@curefit/userinfo-common"
import { isEmpty } from "lodash"
import CareUtil from "../util/CareUtil"


class DiagnosticsTestListPageView extends ProductDetailPage {
    private pageTitle: string
    constructor(userContext: UserContext, category: string, patientId: string, isHomeCollectionEnabled: boolean, tests: DiagnosticProduct[], parentBookingId: string, offers: PackOffersResponse, cartOffers: OfferV2[], productInfos: HealthfaceProductInfo[], sampleCollectionLocationResponse: SampleCollectionLocationResponse, diagnosticTestInstruction: any, selectedTestCodes: string[], fulfilledDetails: any, allTestCodes: string[], bookingDetail: BookingDetail) {
        super()
        const isWeb = AppUtil.isWeb(userContext)
        const isDesktop = AppUtil.isDesktop(userContext)
        this.pageTitle = "Book Tests"
        const items: DiagnosticTestItem[] = []
        let offerIds: string[] = []
        const bundleProductsList: any[] = []
        const productOffersList: OfferV2[] = []
        tests.map(test => {
            if (_.isEmpty(test)) {
                return null
            }
            const offerDetails = OfferUtil.getPackOfferAndPrice(test, offers)
            const newOfferIds = _.map(offerDetails.offers, offer => { return offer.offerId })
            const isSelected = selectedTestCodes.includes(test.productId)
            const membershipInfo = productInfos.find(productInfo => productInfo.baseSellableProduct.productCode === test.productId)

            let price = offerDetails.price
            let itemAction: Action

            offerDetails?.offers.map(offer => {productOffersList.push(offer)})
            offerIds = offerIds.concat(newOfferIds)
            membershipInfo?.baseSellableProduct?.diagnosticProductResponse && bundleProductsList.push(membershipInfo?.baseSellableProduct?.diagnosticProductResponse)

            if (!_.isEmpty(membershipInfo.userMembershipInfos)) {
                price = {
                    listingPrice: membershipInfo.userMembershipInfos[0].price,
                    mrp: membershipInfo.userMembershipInfos[0].price,
                    currency: offerDetails.price.currency
                }
            }

            const countTests = CareUtil.countParameters(membershipInfo?.baseSellableProduct?.diagnosticProductResponse)
            const countTestsText = countTests > 1 ? countTests.toString() + " TESTS" : countTests.toString() + " TEST"
            const subTitle = userContext.sessionInfo.appVersion >= NEW_PRESCRIPTION_LAB_TEST_BOOKING_PAGE ? undefined : test.subTitle
            const reportReadyEta = membershipInfo?.baseSellableProduct?.diagnosticProductResponse?.reportingTat ? "REPORT READY IN " + membershipInfo?.baseSellableProduct?.diagnosticProductResponse?.reportingTat + " HOURS" : undefined

            if (isWeb) {
                itemAction = {
                    actionType: "GET_DIAGNOSTIC_TEST_LIST",
                    meta: {
                        patientId,
                        category,
                        parentBookingId,
                        bookingId: parentBookingId,
                        testCodes: allTestCodes,
                        selectedDiagnosticTests: isSelected ? selectedTestCodes.filter((testCode) => testCode !== test.productId) : Array.from(selectedTestCodes).concat(test.productId)
                    }
                }
            }

            items.push({ productCode: test.productId, title: test.title, subTitle, price, offerIds, countTestsText, reportReadyEta, isSelected, action: itemAction })
        })

        if (isWeb) {
            this.pageImage = "/image/carefit/bundle/DiagPackLipidHero070219.png"
        }
        productOffersList.push(...cartOffers)
        const combinedOffers = _.filter(productOffersList, function(offer) { return !!offer})
        const isCollapsibleOfferWidgetSupported = userContext.sessionInfo.appVersion >= CARE_COLLAPSIBLE_OFFER_SUPPORTED
        const offerCalloutWidget = isCollapsibleOfferWidgetSupported ? CareUtil.getCollapsibleOfferWidget(combinedOffers, userContext) : this.getOfferCalloutWidget(combinedOffers, isWeb)
        this.widgets.push(offerCalloutWidget)
        const testListAction: Action = !_.isEmpty(bundleProductsList) ? {actionType: "SHOW_CAROUSEL_LIST", meta: {
            type: "DIAGNOSTIC_TEST_DETAIL_LIST",
            bundleProducts: bundleProductsList
        }} : undefined
        let actionUrl = `curefit://selectCareDateV1?patientId=${patientId}&productId=${tests[0].productId}&parentBookingId=${parentBookingId}&type=DIAGNOSTICS&nextAction=checkout&productCodes=${selectedTestCodes.join(",")}`
        const diagnosticsTestAllowedLocation = sampleCollectionLocationResponse?.sampleCollectionLocations
        const homeCenterNotAllowed = !_.isEmpty(diagnosticsTestAllowedLocation) && diagnosticsTestAllowedLocation.indexOf("HOME_PLUS_CENTRE") === -1
        if (!_.isEmpty(offerIds)) {
            actionUrl += `&offerIds=${offerIds.join(",")}`
        }
        let action: Action
        const shouldDisableAction = isWeb && !selectedTestCodes.length
        if (_.isEmpty(diagnosticsTestAllowedLocation)) {
            action = {...CareUtil.showErrorToast(sampleCollectionLocationResponse?.errorMessage), title: "BOOK"}
        } else if (diagnosticsTestAllowedLocation && diagnosticsTestAllowedLocation.length === 1 ) {
            const isAllAtHome = diagnosticsTestAllowedLocation && diagnosticsTestAllowedLocation[0] === "ALL_AT_HOME"
            if (diagnosticTestInstruction) {
                action = {
                    actionType: "SHOW_CARE_INSTRUCTION_MODAL",
                    title: "BOOK",
                    disabled: shouldDisableAction ? true : false,
                    meta: {
                        header: {
                            title: "Instructions"
                        },
                        instructions: diagnosticTestInstruction,
                        action: {
                            actionType: isAllAtHome ? "SELECT_ADDRESS_AND_NAVIGATE" : "NAVIGATION",
                            url: `${actionUrl}&category=${isAllAtHome ? "AT_HOME_SLOT" : "IN_CENTRE_SLOT"}`,
                            title: "CONTINUE"
                        },
                        showBookLater: true
                    }
                }

            } else {
                action = {
                    title: "BOOK",
                    actionType: isAllAtHome ? "SELECT_ADDRESS_AND_NAVIGATE" : "NAVIGATION",
                    disabled: shouldDisableAction ? true : false,
                    url: `${actionUrl}&category=${isAllAtHome ? "AT_HOME_SLOT" : "IN_CENTRE_SLOT"}`
                }
            }
        } else {
            // show bottom sheet for selecting
            const homeCenterActionUrl = `curefit://selectCareDateV1?patientId=${patientId}&productId=${tests[0].productId}&parentBookingId=${parentBookingId}&type=DIAGNOSTICS&nextAction=incentreSlot&productCodes=${selectedTestCodes.join(",")}&title=Select your home address`
            const cards = [
                {
                    type: "HomeCenter",
                    title: homeCenterNotAllowed ? "All At Home" : "Home + Centre",
                    icon: "HOME",
                    disabled: isHomeCollectionEnabled !== true,
                    comingsoonTag: isHomeCollectionEnabled !== true,
                    instructionMeta: {
                        type: "DIAGNOSTICS",
                        testType: homeCenterNotAllowed ? "AT_HOME" : "BOTH",
                        productCodeCsv: selectedTestCodes
                    },
                    action: {
                        actionType: "SELECT_ADDRESS_AND_NAVIGATE",
                        url: `${homeCenterNotAllowed ? actionUrl : homeCenterActionUrl}&category=AT_HOME_SLOT`
                    },
                    url: `${homeCenterNotAllowed ? actionUrl : homeCenterActionUrl}&category=AT_HOME_SLOT`
                },
                {
                    type: "Center",
                    title: "At Centre",
                    icon: "CENTRE",
                    instructionMeta: {
                        type: "DIAGNOSTICS",
                        testType: "IN_CENTRE",
                        productCodeCsv: selectedTestCodes
                    },
                    action: {
                        actionType: "NAVIGATION",
                        url: `${actionUrl}&category=IN_CENTRE_SLOT`
                    },
                    url: `${actionUrl}&category=IN_CENTRE_SLOT`
                }
            ]

            action = {
                actionType: "SCHEDULE_TEST",
                title: "BOOK",
                icon: "SCHEDULE_TEST",
                disabled: shouldDisableAction ? true : false,
                meta: {
                    title: "Schedule your tests",
                    cards: isWeb ? {
                        HomeCenter: cards.find(item => item.type === "HomeCenter"),
                        Center: cards.find(item => item.type === "Center")
                    } : cards
                },
            }
        }

        if (isWeb && isDesktop) {
            this.widgets.push(new TestDetailWidget(items, fulfilledDetails, testListAction, [action]))
            this.actions = []
        } else {
            const isUnmappedTestsWidgetSupported = userContext.sessionInfo.appVersion >= UNMAPPED_TESTS_WIDGET_SUPPORTED
            const finalFulfilledDetails = isUnmappedTestsWidgetSupported ? undefined : fulfilledDetails
            this.widgets.push(new TestDetailWidget(items, finalFulfilledDetails, testListAction))
            if (isUnmappedTestsWidgetSupported) {
                const unmappedTestsWidget = this.getUnmappedTestsWidget(bookingDetail)
                unmappedTestsWidget && this.widgets.push(unmappedTestsWidget)
                this.widgets.push(this.getCareDiagnosticsSellerWidget(userContext, fulfilledDetails))
            }
            this.actions = [action]
        }
    }

    private getUnmappedTestsWidget (bookingDetail: BookingDetail): IUnmappedTestsWidget | null {
        const unMappedTests = bookingDetail?.consultationOrderResponse?.prescriptionInfo.diagnosticLabtestInfo?.unMappedLabTests || []
        const unMappedTestsNames: string[] = unMappedTests.map(test => test.name)
        if (!_.isEmpty(unMappedTestsNames)) {
            return  {
                widgetType: "UNMAPPED_TESTS_WIDGET",
                unMappedTestsNames
            }
        }
        return null
    }

    private getCareDiagnosticsSellerWidget(userContext: UserContext, fulfilledDetails?: any): ICareDiagnosticSellerWidget {
        const isMWeb = AppUtil.isMWeb(userContext)
        const containerStyle = isMWeb ? { marginRight: 15, marginLeft: 15 } : {}
        return {
            widgetType: "CARE_DIAGNOSTICS_SELLER_WIDGET" as any,
            fulfilledDetails: fulfilledDetails as IFullFilledDetailsV2,
            containerStyle,
            ...(AppUtil.isDesktop(userContext) ? { orientation: "RIGHT" } : {})
        }
    }

    private getOfferCalloutWidget(offers: OfferV2[], isWeb?: boolean): ProductOfferWidgetV2 | null {
        const uniqueOfferIds: string[] = []
        const webContainerStyle = { boxShadow: "none", border: "none", margin: 0 }
        const contentContainerStyle = { marginBottom: 20, backgroundColor: "#dfefe6", ...(isWeb ? webContainerStyle : {}) }
        if (!_.isEmpty(offers)) {
            const actionType: ActionType = "SHOW_OFFERS_TNC_MODAL"
            const offerItems: ProductOffer[] = []
            offers.map(offer => {
                    if (!_.isEmpty(offer)) {
                        if ( uniqueOfferIds.indexOf(offer.offerId) === -1 && !offer.displayContexts?.includes("NONE")) {
                            uniqueOfferIds.push(offer.offerId)
                            offerItems.push({
                            title: offer.description.toString(),
                            iconType: "/image/icons/cult/tick.png",
                            tnc: {
                                title: "T&C",
                                action: {
                                    actionType: actionType,
                                    meta: {
                                        title: "Offer Details",
                                        dataItems: offer.tNc,
                                        url: offer.tNcUrl
                                    }
                                }
                            }
                        })
                    }
                    }
            })

            if (offerItems.length > 0) {
                return {
                    widgetType: "PRODUCT_OFFER_WIDGET_V2",
                    offerItems: offerItems,
                    dividerType: "NONE",
                    contentContainerStyle,
                    hideDashedBorder: true,
                    offersBackground: "#dfefe6"
                }
            } else {
                return undefined
            }
        } else {
            return undefined
        }

    }
}
export default DiagnosticsTestListPageView
