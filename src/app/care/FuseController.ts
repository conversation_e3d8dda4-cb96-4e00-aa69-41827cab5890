import * as express from "express"
import { controller, httpPost } from "inversify-express-utils"
import { Container, inject } from "inversify"
import { IQueueService, SQS_CLIENT_TYPES } from "@curefit/sqs-client"
import { Constants } from "@curefit/base-utils"
import { Logger, BASE_TYPES } from "@curefit/base"

export function controllerFactory(kernel: Container) {
    @controller("/fuse")
    class FuseController {
        constructor( @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(SQS_CLIENT_TYPES.QueueService) private queueService: IQueueService
        ) {

        }


        @httpPost("/droplet/order-status")
        public async dropletOrderStatus(req: express.Request, res: express.Response): Promise<boolean> {
            // this.logger.info(`droplet order-status event came with body ${JSON.stringify(req.body)}`)
            const authKey = req.headers["authorization"]
            if (authKey === Constants.getFuseWebhookSecret()) {
                const queueName = Constants.getSQSQueue("DROPLET-EVENTS")
                return this.queueService.sendMessage(queueName, req.body)
            } else {
                return Promise.reject("Auth failiure")
            }

        }


    }
    return FuseController
}

export default controllerFactory

