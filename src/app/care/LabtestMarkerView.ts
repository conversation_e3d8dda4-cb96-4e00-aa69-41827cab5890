import { WidgetView } from "../common/views/WidgetView"


class LabtestMarkerView {

    public widgets: WidgetView[] = []
    public pageContext: any

    /*constructor( labtestResult: LabtestResultSummary, labtestId: number) {
        const labtestData: LabTestResult = labtestResult.labtestResultSummaries.find((x) => (x.labtestId == labtestId))
        this.widgets.push(this.getMarkerSummaryWidget(labtestData))
        this.widgets.push(this.getMarkerLegendWidget())
        this.widgets.push(this.getMarkerResultListWidget(labtestData))
        this.pageContext = {
            "NORMAL": new MarkerLegend("Normal", "#24E795"),
            "BORDERLINE": new MarkerLegend("Borderline", "#ffc900"),
            "HIGH/LOW": new MarkerLegend("High/Low", "#f62eb5"),
            "title": labtestData.labtestTitle
        }
    }

    private getMarkerResultListWidget(labtestData: LabTestResult): MarkerResultListWidget {
        const header: Header = {
            title: "Markers"
        }
        let markerViews: MarkerListView[] = []
        for (let i = 0; i < labtestData.markers.length; i++) {
            const marker: Marker = labtestData.markers[i]
            let markerRefRange: MarkerReferenceRange[] = []
            const markerRange: MarkerRange = labtestData.markerRanges.find((x) => (x.marker.markerid == marker.markerid))
            // ASSUMING THAT EACH MARKER WILL HAVE ATLEAST ONE RANGE VALUE
            const markerRangeValues: MarkerRangeValue = markerRange.markerRangeValues[0]
            if (!_.isEmpty(markerRangeValues)) {
                const rangemaps: MarkerRangeCategoryMap[] = markerRangeValues.markerRangeCategoryMaps
                const rangenums: number[] = []
                for (let k = 0; k < rangemaps.length; k++) {

                    const markerRangeCatMap: MarkerRangeCategoryMap = rangemaps[k]

                    rangenums.push(parseFloat(markerRangeCatMap.rangevalue.split("-")[1]))
                    rangenums.push(parseFloat(markerRangeCatMap.rangevalue.split("-")[0]))

                    const markerReferenceRangeView: MarkerReferenceRange = {
                        type: <MarkerResultState>markerRangeCatMap.category.name,
                        maxVal: parseFloat(markerRangeCatMap.rangevalue.split("-")[1]),
                        minVal: parseFloat(markerRangeCatMap.rangevalue.split("-")[0])
                    }
                    markerRefRange.push(markerReferenceRangeView)

                }
                let markerResultView: MarkerResultView = null
                if (!_.isEmpty(labtestData.markerResults)) {

                    header.title = "Markers Tested"
                    const result: MarkerResult = labtestData.markerResults.find((x) => (x.markerid == marker.markerid))
                    const markerResultView1: MarkerResultView = {
                        type: this.findMatchingType(parseFloat(result.observedvalue), markerRefRange),
                        value: parseFloat(result.observedvalue),
                        valueUnits: result.units
                    }
                    markerResultView = markerResultView1

                }
                //sort marker ranges by min val
                markerRefRange = markerRefRange.sort(
                    function (a: MarkerReferenceRange, b: MarkerReferenceRange) {
                        if (_.isEmpty(a) && _.isEmpty(b))
                            return 0
                        else if (_.isEmpty(a) || _.isEmpty(b)) {
                            if (!_.isEmpty(a))
                                return -1
                            else
                                return 1
                        }
                        else {
                            if (a.minVal > b.minVal) return 1
                            else if (a.minVal < b.maxVal) return -1
                            else return 0
                        }
                    })

                const markerListView: MarkerListView = {
                    title: marker.name,
                    subtitle: marker.description,
                    legendRanges: markerRefRange,
                    minRange: _.min(rangenums),
                    maxRange: _.max(rangenums),
                    markerResult: markerResultView
                }
                markerViews.push(markerListView)
            }

        }
        //sort markerviews by result markeresultstate  highlow > borderline > normal > null

        markerViews = markerViews.sort(
            function (a: MarkerListView, b: MarkerListView) {
                if (_.isEmpty(a.markerResult) && _.isEmpty(b.markerResult)) {
                    if (a.title > b.title)
                        return 1
                    else if (a.title < b.title)
                        return -1
                    else
                        return 0
                }

                else if (_.isEmpty(a.markerResult) || _.isEmpty(b.markerResult)) {
                    if (!_.isEmpty(a))
                        return -1
                    else
                        return 1
                }
                else {
                    if (a.markerResult.type > b.markerResult.type) return 1
                    else if (a.markerResult.type < b.markerResult.type) return -1
                    else return 0
                }
            })

        return new MarkerResultListWidget(header, markerViews)
    }

    private findMatchingType(observedvalue: number, markerRefRange: MarkerReferenceRange[]): MarkerResultState {

        const refrange: MarkerReferenceRange = markerRefRange.find((x) => (observedvalue >= x.minVal && observedvalue <= x.maxVal))
        if (!_.isEmpty(refrange)) {
            return refrange.type
        }
        return null
    }
    private getMarkerLegendWidget(): MarkerLegendWidget {

        const markerLegendStates: MarkerResultState[] = []
        markerLegendStates.push("NORMAL")
        markerLegendStates.push("BORDERLINE")
        markerLegendStates.push("HIGH/LOW")
        const markerLegendWidget: MarkerLegendWidget = new MarkerLegendWidget("Legend", markerLegendStates)
        return markerLegendWidget
    }
    private getMarkerSummaryWidget(labtestData: LabTestResult): MarkerSummaryWidget {


        const markerSummaryWidget: MarkerSummaryWidget = new MarkerSummaryWidget()
        markerSummaryWidget.title = labtestData.labtestTitle
        markerSummaryWidget.subTitle = null
        markerSummaryWidget.image = "/image/singles/care/hcu/MarkerTitleImage.png"
        markerSummaryWidget.labtestId = labtestData.labtestId.toString()
        markerSummaryWidget.markerSummaryResult = this.getAggregateMarkerResult(labtestData)
        if (_.isEmpty(markerSummaryWidget.markerSummaryResult)) {
            markerSummaryWidget.image = "/image/singles/care/hcu/MarkerTitleImage_small.png"
        }
        return markerSummaryWidget

    }

    private getAggregateMarkerResult(labtestresult: LabTestResult): MarkerSummaryResult {
        const total: number = labtestresult.markerResults.length
        let abnormal: number = 0
        for (let _i = 0; _i < labtestresult.markerResults.length; _i++) {
            const marker: MarkerResult = labtestresult.markerResults[_i]
            if (marker.indicator.type == "ABNORMAL")
                abnormal++
        }
        if (total == 0) {
            return null
        }
        const ans: MarkerSummaryResult = {
            outOfRangeRatio: (abnormal) + "/" + total,
            statusText: "Markers Out of Range"
        }
        return ans
    }*/

}

export default LabtestMarkerView
