import {
    BookingDetail,
    Center,
    ConsultationInstructionResponse,
    ConsultationOrderResponse,
    ConsultationSellableProduct,
    Doctor,
    ManagedPlanPackInfo,
    MRNFormResponse, Patient, PrescriptionInfo, LabTest
} from "@curefit/albus-client"
import { CareProductInfoWidget, ConsultationInfoWidget } from "@curefit/apps-common"
import { ActionUtil, OfferUtil, RUPEE_SYMBOL } from "@curefit/base-utils"
import { ConsultationProduct } from "@curefit/care-common"
import { FoodProduct as Product } from "@curefit/eat-common"
import { Feedback } from "@curefit/feedback-common"
import { CustomerIssueType } from "@curefit/issue-common"
import { OfferV2, PackOffersResponse } from "@curefit/offer-common"
import { ProductPrice } from "@curefit/product-common"
import { User } from "@curefit/user-common"
import { UserContext } from "@curefit/userinfo-common"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { WidgetWithMetric } from "@curefit/vm-common"
import * as _ from "lodash"
import {
    Action,
    ActionCard,
    BenefitsWidget,
    CalloutWidget,
    CenterSelectionWidget,
    getOffersWidget,
    GradientCard,
    Header,
    InfoCard,
    ManageOptionPayload,
    ManageOptions,
    ManageOptionsWidget,
    NavigationCardWidget,
    PackInfoWidget, ProductDetailPage,
    ProductFeedbackWidget,
    ProductGridWidget,
    ProductListWidget,
    WidgetView
} from "../common/views/WidgetView"
import FeedbackPageConfigV2Cache from "../ugc/FeedbackPageConfigV2Cache"
import CareUtil, {
    ConsultationInfoEmptyDisabledAction,
    ConsultationInfoSingleItemWidgetContainerStyle,
    ConsultationInfoSubTitleStyle,
    ConsultationInfoTitleStyle,
    DOCTOR_LIST_WIDGET_V2_SUPPORTED,
    MESSAGE_CARD_WIDGET_IN_PRODUCT_PAGE_SUPPORTED,
    NEW_SUMMARY_WIDGET_FOR_TC_SUPPORTED
} from "../util/CareUtil"
import { ICareBusiness } from "./CareBusiness"
import TeleconsultationDetailsPageConfig from "./TeleconsultationDetailsPageConfig"
import AppUtil, { SUPPORT_DEEP_LINK } from "../util/AppUtil"
const clone = require("clone")

class TeleconsultationDetailsView extends ProductDetailPage {

    public pageContext: any

    private async buildView(
        userContext: UserContext,
        user: User,
        isNotLoggedIn: boolean,
        product: ConsultationProduct,
        pageConfig: TeleconsultationDetailsPageConfig,
        packageProduct: ConsultationSellableProduct,
        bookingDetail: BookingDetail,
        newReportIssueManageOption: ManageOptionPayload,
        issuesMap: Map<string, CustomerIssueType[]>,
        centerInfo: Center,
        cityId: string,
        offer?: PackOffersResponse | { [key: string]: PackOffersResponse },
        patientsList?: Patient[],
        feedback?: Feedback,
        feedbackPageConfigV2Cache?: FeedbackPageConfigV2Cache,
        followupProducts?: ConsultationProduct[],
        feedbackQuestion?: string,
        doctorsList?: Doctor[],
        centers?: Center[],
        centerId?: string,
        canChangeCenter?: boolean,
        consultationInstruction?: ConsultationInstructionResponse[],
        bannerVideoWidget?: WidgetWithMetric,
        careBusiness?: ICareBusiness,
        mrnFormConfigResponse?: MRNFormResponse,
        consultaionPacksBannerWidget?: WidgetWithMetric,
    ) {
        let chatActive: boolean = false
        const centersList = CareUtil.getCareCenterObjFromArray(centers)
        const careSelectedCenter: Center = centerId && centersList[centerId]
        const orderId = _.isEmpty(bookingDetail) ? null : bookingDetail.booking.cfOrderId
        const isNewSummaryWidgetSupported = userContext.sessionInfo.appVersion >= NEW_SUMMARY_WIDGET_FOR_TC_SUPPORTED && !AppUtil.isSugarFitOrUltraFitApp(userContext)
        const isGP99Product = CareUtil.isGP99DoctorType(product.doctorType)
        let offerDetails: {
            price: ProductPrice;
            offers: OfferV2[];
        }
        if (careSelectedCenter) {
            const centerPricing: { [key: string]: { price: ProductPrice, offers: OfferV2[] } } = OfferUtil.getOfferAndPriceForCareCenter(product, [centerId], <{ [key: string]: PackOffersResponse }>offer)
            offerDetails = centerPricing[careSelectedCenter.id]
        } else {
            offerDetails = OfferUtil.getPackOfferAndPrice(product, <PackOffersResponse>offer)
        }
        const offerIds = _.map(offerDetails.offers, offer => { return offer.offerId })
        const summaryWidget = this.getTCSummaryWidget(userContext, pageConfig, product, bookingDetail,
            newReportIssueManageOption, issuesMap, orderId, offerDetails.price, offerIds)
        if (!_.isEmpty(bookingDetail)) {
            chatActive = CareUtil.getChatEnabled(bookingDetail)
            const isDocUnavailableState = CareUtil.isDoctorUnavailabilitySupported(userContext) && CareUtil.isDoctorUnavailable(bookingDetail)
            if (isDocUnavailableState) {
                summaryWidget.hasDividerBelow = false
                this.widgets.push(summaryWidget)
                this.widgets.push(CareUtil.getDoctorUnavailablityWidget())
            } else {
                this.widgets.push(summaryWidget)
            }
            if (bookingDetail.consultationOrderResponse?.doctorType !== "ANXIETY_THERAPIST") {
                this.widgets.push(this.getDoctorSummaryWidget(userContext, bookingDetail, chatActive))
            }
            if (CareUtil.isPending(bookingDetail)) {
                if (!CareUtil.isLivePTSessionConsultation(bookingDetail.consultationOrderResponse)) {
                    this.widgets.push(this.getConsultationDetailWidget(bookingDetail))
                }
                if (CareUtil.getRescheduleEnabled(bookingDetail) || CareUtil.getCancelEnabled(bookingDetail)) {
                    this.widgets.push(this.getActionsWidget(product, orderId, bookingDetail))
                }
                if (CareUtil.isLivePTSessionConsultation(bookingDetail.consultationOrderResponse)) {
                    this.widgets.push(CareUtil.getLivePTCancellationCutoffWidget(bookingDetail, userContext.userProfile.timezone))
                } else {
                    if (!CareUtil.isPartOfMP(bookingDetail) && (CareUtil.getRescheduleEnabled(bookingDetail) || CareUtil.getCancelEnabled(bookingDetail))) {
                        const cancellationInfo = CareUtil.getCancellationInfo(userContext, bookingDetail)
                        if (cancellationInfo) {
                            this.widgets.push(new CalloutWidget(cancellationInfo.text))
                        }
                    }
                }
                if (CareUtil.isLivePTSessionConsultation(bookingDetail.consultationOrderResponse) && !CareUtil.getZoomLinkEnabled(bookingDetail)) {
                    this.widgets.push(CareUtil.getLivePTSessionNoteWidget())
                }
                if (CareUtil.isLivePTSessionConsultation(bookingDetail.consultationOrderResponse)) {
                    this.widgets.push(CareUtil.getLivePThowItWorksWidget(userContext))
                } else {
                    this.widgets.push(CareUtil.getConsultationMRNWidget(userContext, bookingDetail, mrnFormConfigResponse))
                    this.widgets.push(CareUtil.getChatWidget(userContext, bookingDetail, product))
                    // this.widgets.push(CareUtil.getCoupleTherapyInfoWidget(product))
                    this.widgets.push(CareUtil.getPartnerUpdateInfoWidget(userContext, product, bookingDetail, patientsList, { actionType: "POP_ACTION" }))
                    this.widgets.push(CareUtil.getConsultationModeWidget(userContext, patientsList, user, product, bookingDetail, consultationInstruction, pageConfig))
                    if (!_.isEmpty(bannerVideoWidget)) {
                        this.widgets.push({ ...bannerVideoWidget, hasDividerBelow: false })
                    }
                    this.widgets.push(CareUtil.getConsultationInstructionWidget(bookingDetail, pageConfig, undefined, consultationInstruction))
                }
            } else if (CareUtil.isMissed(bookingDetail)) {
                this.widgets.push(this.getActionsWidget(product, orderId, bookingDetail))
                this.widgets.push(CareUtil.getChatWidget(userContext, bookingDetail, product))
            } else if (CareUtil.isComplteted(bookingDetail)) {
                if (!_.isEmpty(bookingDetail.consultationOrderResponse.followUpContext) && bookingDetail.consultationOrderResponse.followUpContext.enabled
                    && !_.isEmpty(bookingDetail.consultationOrderResponse.followUpContext.followupMembershipDetails)) {
                    this.widgets.push(this.getFollowupCutoffWidget(bookingDetail.consultationOrderResponse.followUpContext.followupMembershipDetails[0].userMessage))
                }
                // remove prescription/plan for therapy consultations
                if (!CareUtil.isTherapyOnlyConsultation(bookingDetail.consultationOrderResponse)) {
                    this.widgets.push(CareUtil.getPrescriptionWidget(userContext, bookingDetail, product))
                }
                if (feedback && (feedback.rating === "NOT_RATED" || feedback.rating === "DISMISSED")) {
                    this.widgets.push(CareUtil.getProductFeedBackWidget(userContext, feedbackQuestion, feedback))
                }
                this.widgets.push(CareUtil.getLabTestWidget(userContext, bookingDetail))
                this.widgets.push(CareUtil.getChatWidget(userContext, bookingDetail, product))
            }
        } else {
            let howItWorksItem, packContentsDetailed, packbenefits, packofferings, packSummary
            if (packageProduct?.infoSection?.children) {
                packageProduct.infoSection.children.map(infoSection => {
                    switch (infoSection.type) {
                        case "PACK_STEPS": howItWorksItem = infoSection; break
                        case "PACK_CONTENTS_DETAILED": packContentsDetailed = infoSection; break
                        case "PACK_BENEFITS": packbenefits = infoSection; break
                        case "PACK_OFFERING": packofferings = infoSection; break
                        case "PACK_CONTENTS_SUMMARY": packSummary = infoSection; break
                    }
                })
            }
            if (isNewSummaryWidgetSupported) {
                this.widgets.push({ ...this.getCareProductInfoWidget(product, offerDetails), hasDividerBelow: false })
            } else {
                this.widgets.push(summaryWidget)
            }
            if (!_.isEmpty(packbenefits)) {
                this.widgets.push(this.getBenefitsWidget(packbenefits))
            }
            if (!_.isEmpty(packofferings)) {
                this.widgets.push(this.getDescriptionWidget(packofferings))
            }
            if (!_.isEmpty(careSelectedCenter)) {
                this.widgets.push(this.showSelectedCenterWidget(careSelectedCenter, canChangeCenter))
            }
            if ((
                    !_.isEmpty(doctorsList) &&
                    userContext.sessionInfo.appVersion >= DOCTOR_LIST_WIDGET_V2_SUPPORTED
                    && !(CareUtil.isAnxietyTherapy(product.productId))
                    && !CareUtil.isKayaTrailConsult(product.productId)
                ) && !_.get(packageProduct?.infoSection, "doNotShowDoctorWidget", false)
            ) {
                const widget = CareUtil.getDoctorListWidget(userContext, undefined, doctorsList, centersList, careSelectedCenter)
                widget && this.widgets.push(widget)
            }
            if (isGP99Product && consultaionPacksBannerWidget) {
                this.widgets.push(consultaionPacksBannerWidget)
            }
            if (!isGP99Product && !_.isEmpty(offerIds)) {
                this.widgets.push(getOffersWidget("Offers Applied", offerDetails.offers))
            }
            if (!_.isEmpty(packContentsDetailed)) {
                this.widgets.push(this.getWhatsInPackWidget(userContext, packContentsDetailed))
            }
            if (!_.isEmpty(howItWorksItem)) {
                this.widgets.push(this.getHowItWorksWidget(howItWorksItem))
            }
            if (!_.isEmpty(packSummary)) {
                this.widgets.push(this.getHowItWorksWidget(packSummary, true))
            }
        }
        this.widgets = this.widgets.filter(item => !!item)
        this.actions = await this.getActions(userContext, product,  isNotLoggedIn, offerDetails, chatActive, patientsList, consultationInstruction, bookingDetail, user, cityId, pageConfig, followupProducts, offerIds, careSelectedCenter, careBusiness)
        if (!_.isEmpty(this.actions)) {
            this.actions = this.actions.map(action => {
                return {...action, title: _.get(packageProduct?.infoSection, "ctaTitle", action?.title)}
            })
        }
        if (!_.isEmpty(offerIds)) {
            this.pageContext = {
                "offerId": offerIds[0],
                offerIds: offerIds
            }
        }
        return this
    }

    public static async getView(
        userContext: UserContext,
        user: User,
        isNotLoggedIn: boolean,
        product: ConsultationProduct,
        pageConfig: TeleconsultationDetailsPageConfig,
        packageProduct: ConsultationSellableProduct,
        bookingDetail: BookingDetail,
        newReportIssueManageOption: ManageOptionPayload,
        issuesMap: Map<string, CustomerIssueType[]>,
        centerInfo: Center,
        cityId: string,
        offer?: PackOffersResponse | { [key: string]: PackOffersResponse },
        patientsList?: Patient[],
        feedback?: Feedback,
        feedbackPageConfigV2Cache?: FeedbackPageConfigV2Cache,
        followupProducts?: ConsultationProduct[],
        doctorsList?: Doctor[],
        centers?: Center[],
        centerId?: string,
        canChangeCenter?: boolean,
        consultationInstruction?: ConsultationInstructionResponse[],
        bannerVideoWidget?: WidgetWithMetric,
        careBusiness?: ICareBusiness,
        mrnFormConfigResponse?: MRNFormResponse,
        consultaionPacksBannerWidget?: WidgetWithMetric,
    ): Promise<TeleconsultationDetailsView> {
        const feedbackQuestion: string = await feedbackPageConfigV2Cache.getQuestionV2(feedback)
        const view: TeleconsultationDetailsView = await new TeleconsultationDetailsView().buildView(userContext, user, isNotLoggedIn, product, pageConfig, packageProduct, bookingDetail, newReportIssueManageOption, issuesMap, centerInfo, cityId, offer, patientsList, feedback, feedbackPageConfigV2Cache, followupProducts, feedbackQuestion, doctorsList, centers, centerId, canChangeCenter, consultationInstruction, bannerVideoWidget, careBusiness, mrnFormConfigResponse, consultaionPacksBannerWidget)
        return view
    }

    private async getActions(
        userContext: UserContext,
        product: ConsultationProduct,
        isNotLoggedIn: boolean,
        offerDetails: {
            price: ProductPrice;
            offers: OfferV2[];
        },
        chatActive: boolean,
        patientsList: Patient[],
        consultationInstruction: ConsultationInstructionResponse[],
        bookingDetail: BookingDetail,
        user: User,
        cityId: string,
        pageConfig: TeleconsultationDetailsPageConfig,
        followupProducts?: ConsultationProduct[],
        offerIds?: string[],
        careSelectedCenter?: Center,
        careBusiness?: ICareBusiness
    ): Promise<Action[]> {
        const isGP99Product = CareUtil.isGP99DoctorType(product.doctorType)
        const isMessgageCardWidgetSupported = userContext.sessionInfo.appVersion >= MESSAGE_CARD_WIDGET_IN_PRODUCT_PAGE_SUPPORTED
        const title = isGP99Product
            ? `BOOK CONSULT ${offerDetails?.price?.listingPrice ? `@ ${RUPEE_SYMBOL}${offerDetails?.price?.listingPrice}` : ""}`
            : "Book your appointment"
        if (_.isEmpty(bookingDetail)) {
            if (isNotLoggedIn === true) {
                return [
                    {
                        actionType: "SHOW_ALERT_MODAL",
                        title: title,
                        meta: {
                            title: "Login Required!",
                            subTitle: "Please login to continue",
                            actions: [{ actionType: "LOGOUT", title: "Login" }]
                        }
                    }
                ]
            }
            const isNewCenterSpecificFlow = CareUtil.isNewCenterSpecifiFlowSupported(userContext)
            const action = isNewCenterSpecificFlow && !_.isEmpty(careSelectedCenter) ? this.getPreBookingActionsV2(userContext, product, patientsList, careSelectedCenter) : this.getPreBookingActions(userContext, product, patientsList, offerIds, title)
            // if (product.consultationMode === "INCENTRE") {
            //     return [CareUtil.getCoronaCareInstructionModal(userContext, action[0])]
            // }
            return action
        }
        if (
            !CareUtil.isPartOfMP(bookingDetail) &&
            CareUtil.isMissed(bookingDetail) &&
            CareUtil.getRescheduleEnabled(bookingDetail)
        ) {
            if (CareUtil.getVideoEnabled(bookingDetail)) {
                return CareUtil.getVideoJoinCallAction(
                    userContext,
                    patientsList,
                    user,
                    product,
                    bookingDetail,
                    consultationInstruction,
                    pageConfig
                )
            }
            return [{
                title: "Reschedule",
                actionType: "RESCHEDULE_TC",
                url: `curefit://rescheduleTc?parentBookingId=${bookingDetail.booking.id}`,
                meta: {
                    "tcBookingId": bookingDetail.booking.id,
                    "centerId": bookingDetail.consultationOrderResponse.center.id,
                    "productId": product.productId,
                    "patientId": bookingDetail.booking.patientId
                }
            }]
        }
        const chatAction = CareUtil.getChatEnabled(bookingDetail) ? CareUtil.getChatMessageAction(
            userContext,
            _.get(bookingDetail.consultationOrderResponse, "appointmentActionsWithContext.chatActionWithContext", null),
            bookingDetail.consultationOrderResponse.patient.id,
            bookingDetail.consultationOrderResponse.doctor.name,
            CareUtil.getChatChannel(bookingDetail),
            bookingDetail.consultationOrderResponse.doctor.displayImage,
            bookingDetail.consultationOrderResponse.doctor.qualification,
            bookingDetail.booking.id
        ) : undefined
        if (!CareUtil.isComplteted(bookingDetail)) {
            if (CareUtil.isLivePTSessionConsultation(bookingDetail.consultationOrderResponse)) {
                const zoomUrl = CareUtil.getZoomLink(bookingDetail)
                const isActionEnabled = CareUtil.getZoomLinkEnabled(bookingDetail)
                const action: Action = await careBusiness.getJoinZoomMeetingActionfromUrl(userContext, zoomUrl, bookingDetail.booking.patientId, bookingDetail.consultationOrderResponse.doctorType, bookingDetail.booking.id, bookingDetail.zoomParticipantId)
                action.isEnabled = isActionEnabled
                return [action]
            }

            if (CareUtil.getVideoEnabled(bookingDetail)) {
                return CareUtil.getVideoJoinCallAction(
                    userContext,
                    patientsList,
                    user,
                    product,
                    bookingDetail,
                    consultationInstruction,
                    pageConfig
                )
            }
            if (chatAction && !isMessgageCardWidgetSupported) {
                return [chatAction]
            }
        }
        if (CareUtil.isComplteted(bookingDetail)) {
            const actions: Action[] = []
            if (!_.isEmpty(bookingDetail.consultationOrderResponse.followUpContext) && bookingDetail.consultationOrderResponse.followUpContext.enabled) {
                const parentBookingId = bookingDetail.booking.id
                const followUpConsultationId = bookingDetail.consultationOrderResponse.id
                actions.push(CareUtil.getFollowupAction(
                    userContext,
                    parentBookingId,
                    followupProducts,
                    followUpConsultationId,
                    bookingDetail.consultationOrderResponse.patient.id,
                    bookingDetail.consultationOrderResponse?.doctor?.id,
                    bookingDetail?.consultationOrderResponse?.center?.id
                ))
            }

            if (chatAction && !isMessgageCardWidgetSupported) {
                actions.push(chatAction)
            }
            // if (!_.isEmpty(bookingDetail.consultationOrderResponse.referralInfo)) {
            //     const referalInfo: ReferralInfo = bookingDetail.consultationOrderResponse.referralInfo
            //     actions.push({
            //         title: "Talk to specialist",
            //         actionType: "NAVIGATION",
            //         url: `curefit://specialistSelection?productId=${product.productId}&patientId=${bookingDetail.consultationOrderResponse.patient.id}&parentBookingId=${bookingDetail.booking.id}&specialityCode=${referalInfo.specialityCode}&specialityDisplay=${referalInfo.specialityDisplay}`
            //     })
            // }
            return actions
        }
        // if (chatActive) {
        //     return [{
        //         title: msgdisplayText,
        //         actionType: "NAVIGATION",
        //         url: `curefit://twiliomessagechat?appointmentId=${bookingDetail.booking.id}&identity=Patient-${bookingDetail.booking.id}&channel=${CareUtil.getChatChannel(bookingDetail)}&docName=${bookingDetail.consultationOrderResponse.doctor.name}`
        //     }]
        // }

        // return [{
        //     title: "Done",
        //     actionType: "POP_ACTION"
        // }]

    }

    private getDoctorSummaryWidget(userContext: UserContext, bookingDetail: BookingDetail, chatActive: boolean) {
        const consultationResponse: ConsultationOrderResponse = bookingDetail.consultationOrderResponse
        const chatAction: any = CareUtil.getChatMessageAction(
            userContext,
            _.get(consultationResponse, "appointmentActionsWithContext.chatActionWithContext", null),
            consultationResponse.patient.id,
            consultationResponse.doctor.name,
            CareUtil.getChatChannel(bookingDetail),
            consultationResponse.doctor.displayImage,
            consultationResponse.doctor.qualification,
            null,
            bookingDetail.booking.id,
            "consultationdetail"
        )
        let unReadMessageCount: number = 0
        if (chatAction && chatAction.title.includes("(") && chatAction.title.includes(")")) {
            unReadMessageCount = Number(chatAction.title.match(/\(([^)]+)\)/)[1])
        }
        const isNotMessgageCardWidgetSupported = userContext.sessionInfo.appVersion < MESSAGE_CARD_WIDGET_IN_PRODUCT_PAGE_SUPPORTED

        const doctorDetailsWidget: WidgetView & {
            image: string
            title: string,
            subTitle: string,
            actionUrl: string,
            unReadMessageCount: number
            profileImageAction: Action
        } = {
            widgetType: "DOCTOR_SUMMARY_WIDGET",
            image: consultationResponse.doctor.displayImage,
            title: `${consultationResponse.doctor.name}`,
            subTitle: consultationResponse.doctor.qualification,
            actionUrl: chatAction && isNotMessgageCardWidgetSupported ? chatAction.url : undefined,
            profileImageAction: {
                actionType: "SHOW_DOCTOR_DETAILS_MODAL",
                meta: {
                    ...consultationResponse.doctor,
                    experience: CareUtil.isTherapyOnlyDoctorType(consultationResponse.doctorType) ? undefined : consultationResponse?.doctor?.experience
                }
            },
            unReadMessageCount
        }
        return doctorDetailsWidget
    }

    private getPreBookingActions(userContext: UserContext, product: Product, patientsList: Patient[], offerIds?: string[], actionTitle?: string): Action[] {
        return this.getInstructionAction(product, userContext, CareUtil.getTCBookingAction(patientsList, product as ConsultationProduct, actionTitle), actionTitle)
    }

    private getInstructionAction(product: Product, userContext: UserContext, action: Action, actionTitle?: string): Action[] {
        if (CareUtil.isInstructionModalSupported(product) && CareUtil.isInstructionModalSupportedInApp(userContext)) {
            return [
                {
                    title: actionTitle ? actionTitle : "Book your appointment",
                    actionType: "SHOW_CARE_INSTRUCTION_MODAL",
                    meta: {
                        header: {
                            title: "Instructions"
                        },
                        instructions: CareUtil.getInstructionsForAppointments(),
                        action: { ...action, title: "CONTINUE" },
                        showBookLater: true
                    }
                }
            ]
        } else {
            return [action]
        }
    }

    private getPreBookingActionsV2(userContext: UserContext, product: Product, patientsList: Patient[], careSelectedCenter?: Center): Action[] {
        if (_.isEmpty(careSelectedCenter)) {
            return []
        }
        const actionUrl = ActionUtil.selectCareDateV1(product.productId)
        const actionStringOnline: string = `${actionUrl}&centerId=${careSelectedCenter.id}`
        const relations = CareUtil.getRelations()
        const actionMeta = careSelectedCenter ? {
            doctorType: (<ConsultationProduct>product).doctorType,
            productId: product.productId,
            id: product.productId,
            subCategoryCode: product.categoryId,
            centerInfo: {
                id: careSelectedCenter.id,
                name: careSelectedCenter.name
            }
        } : {}
        let action: Action = null
        if (!_.isEmpty(patientsList)) {
            const isSelfPatientPresent = patientsList.find(patient => patient.relationship === "Self")
            const defaultRelationShip = isSelfPatientPresent ? { patientRelation: "Other" } : {}
            action = {
                actionType: "SHOW_PATIENT_SELECTION",
                title: "Book your appointment",
                meta: {
                    action: {
                        actionType: "UPDATE_CARE_CENTER",
                        url: actionStringOnline,
                        meta: actionMeta
                    },
                    patientsList: patientsList,
                    relations: isSelfPatientPresent ? CareUtil.getOtherRelation() : relations,
                    emergencyContactRelations: CareUtil.getEmergencyRelations(),
                    guardianRelations: CareUtil.getGuardianRelations(),
                    reqParams: {
                        formUserType: "CARE_USER",
                        ...defaultRelationShip
                    },
                }
            }

        } else {
            action = {
                actionType: "ADD_PATIENT",
                title: "Book your appointment",
                meta: {
                    url: actionStringOnline,
                    action: {
                        actionType: "UPDATE_CARE_CENTER",
                        url: actionStringOnline,
                        meta: actionMeta
                    },
                    reqParams: {
                        formUserType: "CARE_USER"
                    },
                    relations: relations,
                    emergencyContactRelations: CareUtil.getEmergencyRelations(),
                    guardianRelations: CareUtil.getGuardianRelations()
                }
            }
        }
        return this.getInstructionAction(product, userContext, action)
    }

    private getActionsWidget(product: ConsultationProduct, orderId: string, bookingDetail: BookingDetail): WidgetView {
        const options: ManageOptionPayload[] = []
        const isPartOfMP = CareUtil.isPartOfMP(bookingDetail)
        const resheduleEnabled = !isPartOfMP && CareUtil.getRescheduleEnabled(bookingDetail)
        const cancelEnabled = !isPartOfMP && CareUtil.getCancelEnabled(bookingDetail)
        if (resheduleEnabled) {
            options.push({
                displayText: "Reschedule",
                isEnabled: true,
                type: "RESCHEDULE_TC", action: `curefit://rescheduleTc?parentBookingId=${bookingDetail.booking.id}`
            })
        }
        if (cancelEnabled) {
            options.push(
                {
                    displayText: "Cancel",
                    isEnabled: true,
                    type: "CANCEL_TC", action: "curefit://cancelTeleconsultation?tcBookingId=" + bookingDetail.booking.id
                }
            )
        }
        if (isPartOfMP) {
            options.push(
                {
                    displayText: "Manage",
                    isEnabled: true,
                    type: "NAVIGATION", action: ActionUtil.carefitbundle(bookingDetail.consultationOrderResponse.rootBooking.productCode, "MP", bookingDetail.booking.rootBookingId.toString())
                }
            )
        }
        const rescheduleText = CareUtil.isNotDoctorConsulation(bookingDetail.consultationOrderResponse) ? "Reschedule session" : "Reschedule consultation"
        const title = isPartOfMP ? "Manage" : (resheduleEnabled && cancelEnabled ? "Reschedule or cancel" : cancelEnabled ? "Cancel" : (resheduleEnabled ? rescheduleText : undefined))
        const meta = {
            tenant: product.tenant,
            "tcBookingId": bookingDetail.booking.id,
            "orderId": orderId,
            "centerId": bookingDetail.consultationOrderResponse.center.id,
            "productId": product.productId,
            "patientId": bookingDetail.booking.patientId
        }
        // Added fix for empty options being sent to app and rendering empty widget
        if (_.isEmpty(options)) {
            return null
        }
        const manageoptions: ManageOptions = {
            icon: "RESCHEDULE_OR_CANCEL",
            displayText: title, // momentTz.tz(bookingDetail.consultationOrderResponse.startTime, "Asia/Kolkata").format("DD MMM,h:mm a"),
            options: options
        }
        return new ManageOptionsWidget(manageoptions, meta)
    }

    private getFollowupCutoffWidget(userMessage: string): WidgetView {
        // const text = `Free followup till ${momentTz.tz(cutoff, "Asia/Kolkata").format("DD MMM, h:mm a")}`
        const options: ManageOptionPayload[] = [{
            displayText: userMessage,
            isEnabled: false,
            type: "DONE"
        }]
        const manageoptions: ManageOptions = {
            icon: "TIME",
            displayText: userMessage,
            options: options
        }
        const calloutWidget = new ManageOptionsWidget(manageoptions, {})
        calloutWidget.isDisabled = true
        calloutWidget.hasDividerBelow = false
        return calloutWidget
    }

    private getConsultationDetailWidget(bookingDetail: BookingDetail): WidgetView {
        if (CareUtil.isAudioBookingConsultation(bookingDetail)) {
            return undefined
        }
        const videoCall: boolean = bookingDetail.booking.subCategoryCode === "CF_ONLINE_CONSULTATION"
        const text: string = videoCall ? "Video call" : `${bookingDetail.consultationOrderResponse.center.name}`
        const options: ManageOptionPayload[] = []
        const center = bookingDetail.consultationOrderResponse.center
        options.push({
            displayText: text,
            isEnabled: videoCall ? false : true,
            action: center ? `curefit://externalDeepLink?placeUrl=${center.placeUrl}` : undefined,
            type: videoCall ? "DONE" : "NAVIGATION"
        })
        const meta: any = {
            "tcBookingId": bookingDetail.booking.id,
            "mapUrl": center ? center.placeUrl : undefined
        }

        const manageoptions: ManageOptions = {
            icon: videoCall ? "VIDEO_DISABLED" : "LOCATION",
            displayText: text,
            secondaryIcon: videoCall ? undefined : "NAVIGATE",
            options: options
        }
        const calloutWidget = new ManageOptionsWidget(manageoptions, meta)
        if (videoCall) {
            calloutWidget.isDisabled = true
        }
        return calloutWidget
    }

    private getInstructions(bookingDetail: BookingDetail, pageConfig: TeleconsultationDetailsPageConfig) {
        return CareUtil.getConsulationInstructions(bookingDetail, pageConfig)
    }

    private getTCSummaryWidget(userContext: UserContext, pageConfig: TeleconsultationDetailsPageConfig, product1: ConsultationProduct,
        bookingDetail: BookingDetail, newReportIssueManageOption: ManageOptionPayload,
        issuesMap: Map<string, CustomerIssueType[]>, orderId: string, price: ProductPrice, offerIds?: string[]) {
        product1.consultationMode === "ONLINE"
        let manageOptionsView: { manageOptions: ManageOptions, meta: any } = null
        let manageOptions: ManageOptions
        if (newReportIssueManageOption) {
            manageOptions = {
                displayText: "Manage",
                options: [newReportIssueManageOption]
            }
            manageOptionsView = {
                manageOptions: manageOptions,
                meta: undefined
            }
        } else {
            if (CareUtil.isComplteted(bookingDetail) || CareUtil.isPending(bookingDetail)) {
                manageOptionsView = this.getTCManageOptions(bookingDetail, orderId, product1.productId, issuesMap)
            }
        }
        const product: ConsultationProduct = clone(product1) // known hack
        const tcSummaryWidget: WidgetView & {
            productId: string
            title: string,
            subTitle: string,
            metaText?: string,
            price: ProductPrice,
            image: string,
            meta: any
            manageOptions: ManageOptions,
            offerId: string,
            offerIds: string[]
        } = {
            widgetType: "TELECONSULTATION_SUMMARY_WIDGET",
            title: product.title,
            productId: product.productId,
            price: !_.isEmpty(bookingDetail) ? null : (CareUtil.isGP99DoctorType(product.doctorType) ? {
                listingPrice: price.listingPrice,
                mrp: price.listingPrice,
                currency: price.currency
            } : price),
            image: product.heroImageUrl,
            manageOptions: !_.isEmpty(manageOptionsView) ? manageOptionsView.manageOptions : null,
            meta: !_.isEmpty(manageOptionsView) ? manageOptionsView.meta : null,
            subTitle: this.getSummarySubtitle(userContext, bookingDetail, product),
            offerId: !_.isEmpty(offerIds) ? offerIds[0] : undefined,
            offerIds: offerIds,
            metaText: this.getSummaryMetaText(bookingDetail, product, userContext.userProfile.timezone)
        }
        return tcSummaryWidget
    }

    private getCareProductInfoWidget(product: ConsultationProduct, offerDetails: {
        price: ProductPrice;
        offers: OfferV2[];
    }): CareProductInfoWidget {
        const subtitle = product.subTitle || ""
        return new CareProductInfoWidget(product.title, product.subTitle, product.heroImageUrl, { marginBottom: -50 }, subtitle.length > 100, 100, "See less", "Know more", offerDetails?.price?.listingPrice)
    }

    private getSummarySubtitle(userContext: UserContext, bookingDetail: BookingDetail, product: ConsultationProduct): string {
        const tz = userContext.userProfile.timezone
        if (!_.isEmpty(bookingDetail)) {
            if (bookingDetail.booking.status === "CANCELLED") {
                return CareUtil.isCultPTProduct(product) ? "Session got cancelled" : "Consultation got cancelled"
            } else if (bookingDetail.consultationOrderResponse.startTime) {
                if (CareUtil.isMindDoctorType(product.doctorType) || CareUtil.isPsychiatry(product.doctorType)) {
                    const duration = TimeUtil.diffInMinutes(tz, TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(bookingDetail.consultationOrderResponse.startTime), "YYYY-MM-DD HH:mm:ss"), TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(bookingDetail.consultationOrderResponse.endTime), "YYYY-MM-DD HH:mm:ss"))
                    return duration + " mins"
                }
                return `For ${bookingDetail.consultationOrderResponse.patient.name} | ${TimeUtil.formatEpochInTimeZone(tz, bookingDetail.consultationOrderResponse.startTime, "DD MMM, h:mm a")}`
            } else if (bookingDetail.consultationOrderResponse.preferredStartTime) {
                if (CareUtil.isMindDoctorType(product.doctorType) || CareUtil.isPsychiatry(product.doctorType)) {
                    const duration = TimeUtil.diffInMinutes(tz, TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(bookingDetail.consultationOrderResponse.preferredStartTime), "YYYY-MM-DD HH:mm:ss"), TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(bookingDetail.consultationOrderResponse.preferredEndTime), "YYYY-MM-DD HH:mm:ss"))
                    return duration + " mins"
                }
                return `For ${bookingDetail.consultationOrderResponse.patient.name} | ${TimeUtil.formatEpochInTimeZone(tz, bookingDetail.consultationOrderResponse.preferredStartTime, "DD MMM, h:mm a")}`
            }
        } else {
            return null
        }
    }

    private getSummaryMetaText(bookingDetail: BookingDetail, product: ConsultationProduct, timezone: Timezone): string {
        if (!_.isEmpty(bookingDetail) && bookingDetail.booking.status !== "CANCELLED") {
            if (CareUtil.isMindDoctorType(product.doctorType) || CareUtil.isPsychiatry(product.doctorType)) {
                let subText = product.consultationMode === "INCENTRE" ? "Face to Face" : "Online"
                subText += ` Session on ${TimeUtil.formatEpochInTimeZone(timezone, bookingDetail.consultationOrderResponse.startTime, "DD MMM, h:mm a")}`
                return subText
            }
        }
    }

    private getTCManageOptions(bookingDetail: BookingDetail, orderId: string, productId: string, issuesMap: Map<string, CustomerIssueType[]>): { manageOptions: ManageOptions, meta: any } {
        const options: ManageOptionPayload[] = []
        options.push(
            {
                isEnabled: true,
                displayText: "Need Help",
                type: "REPORT_ISSUE",
                meta: CareUtil.getTCIssueList(issuesMap),
                action: SUPPORT_DEEP_LINK,
            }
        )

        const manageOptions: ManageOptions = {
            displayText: "...",
            options: options
        }

        const meta: any = {
            orderId: orderId,
            productId: productId,
            tcBookingId: bookingDetail.booking.id
        }
        return { manageOptions: manageOptions, meta: meta }
    }

    private getHowItWorksWidget(packInfo: ManagedPlanPackInfo, hideSepratorLines?: boolean): ProductListWidget {
        const header: Header = {
            title: packInfo.title,
            color: "#000000"
        }
        const infoCards: InfoCard[] = []
        packInfo.children.forEach(item => {
            infoCards.push({
                subTitle: item.desc,
                icon: item.imageUrl
            })
        })
        return new ProductListWidget("SMALL", header, infoCards, undefined, undefined, undefined, hideSepratorLines)
    }

    private getWhatsInPackWidget(userContext: UserContext, packContentsDetailed: ManagedPlanPackInfo): ProductListWidget | ProductGridWidget {
        const header: Header = {
            title: packContentsDetailed.title,
            color: "#000000"
        }
        const cards: GradientCard[] = []
        packContentsDetailed.children.forEach(item => {
            cards.push({
                title: item.title,
                subTitle: item.desc,
                shadowColor: item.shadowColor,
                gradientColors: item.gradientColors,
                icon: item.type
            })
        })

        if (userContext.sessionInfo.appVersion >= 7.48) { // fixed no images in the app for this icons
            return new ProductListWidget("GARDIENT_CARD", header, cards)
        }
        return new ProductGridWidget("GARDIENT_CARD", header, cards)
    }

    private getDescriptionWidget(packbenefits: ManagedPlanPackInfo) {
        const header: Header = {
            title: packbenefits.title,
            color: "#000000"
        }
        const cards: InfoCard[] = []
        packbenefits.children.forEach(item => {
            cards.push({
                title: item.title,
                subTitle: item.desc,
                icon: item.imageUrl
            })
        })
        return new ProductListWidget("DETAIL", header, cards)
    }

    private getBenefitsWidget(packbenefits: ManagedPlanPackInfo): BenefitsWidget {
        return {
            widgetType: "BENEFITS_WIDGET",
            title: packbenefits.title,
            description: packbenefits.children[0].desc,
            tags: packbenefits.children[0].bullets
        }
    }

    private showSelectedCenterWidget(center: Center, canChangeCenter?: boolean): CenterSelectionWidget {
        const centerSelectionWidget: CenterSelectionWidget = {
            title: center.name,
            canChangeCenter: canChangeCenter,
            widgetType: "CENTER_PICKER_WIDGET",
            dividerType: "SMALL",
            action: {
                actionType: "POP_ACTION"
            }
        }
        return centerSelectionWidget
    }

    private getPackInfoWidget(bookingDetail: BookingDetail, product: Product): WidgetView {
        if (bookingDetail.booking.parentBookingId !== -1) {
            const action: ActionCard = {
                title: "Your Pack",
                action: ActionUtil.carefitbundle(bookingDetail.booking.productCode, bookingDetail.booking.subCategoryCode, bookingDetail.booking.parentBookingId.toString())
            }
            return new PackInfoWidget(bookingDetail.booking.productCode, product.title, action)
        }
        return null
    }
}

export default TeleconsultationDetailsView
