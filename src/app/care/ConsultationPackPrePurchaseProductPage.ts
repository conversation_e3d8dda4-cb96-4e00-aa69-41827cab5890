import { UserAgentType as UserAgent } from "@curefit/base-common"
import { OfferUtil, RUPEE_SYMBOL, ActionUtil } from "@curefit/base-utils"
import { ContentAsset, DiagnosticProduct, DiagnosticProductResponse, ManagedPlanPackInfo, Patient, Doctor } from "@curefit/care-common"
import { FoodProduct as Product } from "@curefit/eat-common"
import { OfferV2, PackOffersResponse } from "@curefit/offer-common"
import { ProductPrice } from "@curefit/product-common"
import { UserContext } from "@curefit/userinfo-common"
import { Orientation } from "@curefit/vm-common"
import * as _ from "lodash"
import { Action, DescriptionWidget, getOffersWidget, Header, InfoCard, PricingWidgetRecurringValue, ProductDetailPage, ProductListWidget, ProductPricingSection, TextWidget, WidgetView } from "../common/views/WidgetView"
import CareUtil from "../util/CareUtil"
import { titleCase } from "@curefit/util-common"
import AppUtil from "../util/AppUtil"

class ConsultationPackPrePurchaseProductView extends ProductDetailPage {
    public pageContext: any

    constructor(
        userContext: UserContext,
        isNotLoggedIn: boolean,
        product: DiagnosticProduct,
        allClubbedProducts: { diagnosticProduct: DiagnosticProduct, product: DiagnosticProductResponse }[],
        packageProduct: DiagnosticProductResponse,
        doctorTypes: string[],
        doctors: Doctor[],
        patientsList: Patient[],
        offers: PackOffersResponse
    ) {
        super()
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent")
        const offerDetails = OfferUtil.getPackOfferAndPrice(product, offers)
        const offerIds = _.map(offerDetails.offers, offer => {return offer.offerId})
        const { howItWorksItem, packBenefit  } = CareUtil.getPackContent(packageProduct)
        this.actions = this.getPreBookingActions(userContext, product, isNotLoggedIn, patientsList, packageProduct, offerIds, offerDetails)
        const widget = [
            this.getDescriptionWidget(packageProduct.productDescription, undefined, userAgent),
            this.getProductPricingWidget(product.productId, allClubbedProducts, offers, userAgent)
        ]
        this.widgets = [
            this.summaryWidget(packageProduct, offerDetails.price, userAgent),
            ...(userAgent === "DESKTOP" ? widget.reverse() : widget),
            CareUtil.getCareOfferWidget("Offers Applied", offerDetails?.offers, userContext),
            ...this.getDoctorListingWidgets(userContext, doctors, doctorTypes, packageProduct),
            CareUtil.getCareProductBenefitWidget(userContext, packBenefit),
            CareUtil.getHowItWorksWidget(howItWorksItem, userAgent)
        ]
        this.widgets = this.widgets.filter(Boolean).map(widget => ({...widget, hasDividerBelow: false}))
    }

    private getProductPricingWidget(selectedProductId: string, allClubbedProducts: { diagnosticProduct: DiagnosticProduct, product: DiagnosticProductResponse }[], offers: PackOffersResponse, userAgent: UserAgent): any {
        if (!_.isEmpty(allClubbedProducts)) {
            const sections: ProductPricingSection[] = []
            const recurringSection: PricingWidgetRecurringValue [] = []
            allClubbedProducts.map((clubProduct: { diagnosticProduct: DiagnosticProduct, product: DiagnosticProductResponse }) => {
                const offerDetail = OfferUtil.getPackOfferAndPrice(clubProduct.diagnosticProduct, offers)
                const numberOfSession = Number(_.get(clubProduct, "product.infoSection.numberOfSessions", 0))
                const priceMeta = numberOfSession > 1 ? `${RUPEE_SYMBOL} ${Math.ceil(offerDetail.price.listingPrice / numberOfSession)} / month` : undefined
                recurringSection.push({
                    title: _.get(clubProduct, "product.infoSection.shortTitle"),
                    subTitle: _.get(clubProduct, "product.infoSection.shortSubTitle"),
                    price: {
                        listingPrice: offerDetail.price.listingPrice,
                        mrp: offerDetail.price.mrp,
                        currency: offerDetail.price.currency
                    },
                    priceMeta: priceMeta,
                    action: {
                        actionType: "NAVIGATION",
                        url: ActionUtil.carefitbundle(
                            clubProduct.diagnosticProduct.productId,
                            clubProduct.diagnosticProduct.subCategoryCode,
                            undefined,
                            undefined,
                            undefined,
                            undefined,
                            undefined,
                            undefined,
                            userAgent
                        ),
                        meta: {
                            selectedProductId: clubProduct.diagnosticProduct.productId
                        }
                    },
                    selected: selectedProductId === clubProduct.diagnosticProduct.productId
                })
            })
            if (!_.isEmpty(recurringSection)) {
                sections.push({
                    // title: "CHOOSE A LONGER DURATION PLAN",
                    type: "RECURRING",
                    value: recurringSection
                })
            }
            return {
                widgetType: "PRODUCT_PRICING_WIDGET",
                hasDividerBelow: false,
                header: {
                    title: "Choose"
                },
                orientation: userAgent === "DESKTOP" ? "RIGHT" : undefined,
                footerText: undefined,
                sections: sections
            }
        }
        return null
    }

    private summaryWidget(product: DiagnosticProductResponse, price: ProductPrice, userAgent: UserAgent): WidgetView {
        const checkupSummaryWidget: WidgetView & {
            productId: string;
            title: string;
            image: string;
            // price?: ProductPrice;
            assets?: ContentAsset[]
            hasDividerBelow: boolean
            breadcrumb?: { text: string, link?: string }[]
            orientation?: Orientation,
            actions: Action[]
        } = {
            widgetType: "HEALTHCHECKUP_SUMMARY_WIDGET",
            title: _.get(product, "infoSection.packTitle", product.productName),
            productId: product.productCode,
            // price: userAgent !== "APP" ? price : undefined,
            image: product.heroImageUrl,
            assets: _.get(product, "contentAssets.carouselAssets", []),
            hasDividerBelow: false,
            orientation: userAgent === "DESKTOP" ? "RIGHT" : undefined,
            breadcrumb: userAgent === "DESKTOP" ? [{ text: "Home", link: "/" }, { text: "Care", link: "/care" }, { text: "Health Packs", link: "/care/healthpack" }, { text: _.get(product, "infoSection.packTitle", product.productName) }] : [],
            actions: userAgent === "DESKTOP" || userAgent === "MBROWSER" ? this.actions : []
        }
        return checkupSummaryWidget
    }

    private getPreBookingActions(userContext: UserContext, product: Product, isNotLoggedIn: boolean, patientsList: Patient[], packageProduct: DiagnosticProductResponse, offerIds: string[], offerDetails: {
        price: ProductPrice,
        offers: OfferV2[]
    }): Action[] {
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent")
        const title = `Book ${String(_.get(packageProduct, "infoSection.shortTitle", "")).toLowerCase()}`
        if (isNotLoggedIn === true) {
            return [
                CareUtil.loginAlertModal(title)
            ]
        } else {
            let actionString: string = `curefit://carecartcheckout?productId=${product.productId}&productCode=${packageProduct.productCode}&subCategoryCode=${packageProduct.subCategoryCode}`
            if (!_.isEmpty(offerIds)) {
                actionString += `&offerIds=${offerIds.join(",")}`
            }
            const action: Action = {
                actionType: "NAVIGATION",
                meta: {
                    formUserType: "CARE_USER"
                },
                url: actionString,
            }

            const pageAction = CareUtil.getPatientSelectionModalAction(patientsList,
                AppUtil.isExistingPackPurchaseForPatientSupported(userContext) ? {
                    actionType: "CHECK_BUNDLE_EXISTS",
                    title,
                    meta: {
                        productCode: packageProduct.productCode,
                        onSuccessAction: CareUtil.getBundleConsentInfoModal(action),
                        onFailureAction: action
                    }
                } : action as Action,
                title,
                "CARE_USER"
            )
            if (userAgent === "APP") {
                pageAction.meta = this.getPageActionMeta(title, pageAction, packageProduct, offerDetails)
            }
            return [pageAction]
        }

    }

    private getPageActionMeta (title: string, pageAction: Action, packageProduct: DiagnosticProductResponse, offerDetails: {  price: ProductPrice,
        offers: OfferV2[]
    }) {
       return  {
            ...pageAction.meta,
            subText: _.get(packageProduct, "infoSection.shortSubTitle"),
            title: title,
            price: offerDetails.price,
            showCheckoutAction: true,
            formUserType: "CARE_USER"
        }
    }

    private getDescriptionWidget(productDescription: string, packInfo?: ManagedPlanPackInfo, userAgent?: UserAgent): DescriptionWidget | TextWidget {
        if (userAgent === "APP") {
            const descriptionWidget = new DescriptionWidget([{
                subTitle: productDescription,
            }])
            descriptionWidget.iconType = undefined
            descriptionWidget.dividerType = "SMALL"
            descriptionWidget.hasDividerBelow = false
            if (packInfo && packInfo.children && !_.isEmpty(packInfo.children)) {
                descriptionWidget.seeMore = "Know More"
                descriptionWidget.seeMoreAction = {
                    actionType: "NAVIGATION",
                    url: `curefit://infopage?contentId=${packInfo.type}&pageType=LIST`,
                    meta: {
                        contentId: packInfo.type,
                        data: CareUtil.getQnAInfoPage(packInfo)
                    }
                }
            }
            return descriptionWidget
        }
        const textWidget = new TextWidget("About", productDescription)
        textWidget.orientation = userAgent === "DESKTOP" ? "RIGHT" : undefined
        return textWidget
    }

    private getDoctorListingWidgets(userContext: UserContext, doctors: Doctor[], doctorTypes: string[], packageProduct: DiagnosticProductResponse) {
        if (_.isEmpty(doctors) && _.isEmpty(doctorTypes)) {
            return []
        }
        const doctorInfo = _.get(packageProduct, "infoSection.doctorInfo")
        if (!_.isEmpty(doctorInfo) && Array.isArray(doctorInfo)) {
            return !_.isEmpty(doctors) ? doctorTypes.map(doctorType => {
                const doctorInfoItem = doctorInfo.find((item: { doctorType: string, title: string }) => item.doctorType === doctorType)
                return CareUtil.getDoctorListWidget(
                    userContext,
                    doctorInfoItem && doctorInfoItem.title ? doctorInfoItem.title : (doctorType ? `Our ${titleCase(CareUtil.getDoctorText(doctorType))}s` : "Our Doctors"),
                    doctors.filter(doctor => _.get(doctor, "primarySubServiceType.code") === doctorType).filter(Boolean), // Commenting out since we have only one product as of now
                    // doctors,
                    {},
                    undefined,
                    true
                )
            }).filter(Boolean) : []
        } else {
            const finalTitle =  (doctorTypes && doctorTypes[0] ? `Our ${titleCase(CareUtil.getDoctorText(doctorTypes[0]))}s` : "Our Doctors")
            return !_.isEmpty(doctors) ? [
                CareUtil.getDoctorListWidget(
                    userContext,
                    finalTitle,
                    doctors,
                    {},
                    undefined,
                    true
                )
            ] : []
        }
    }

}

export default ConsultationPackPrePurchaseProductView
