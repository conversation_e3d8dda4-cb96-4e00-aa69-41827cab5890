import * as _ from "lodash"
import { FoodProduct as Product } from "@curefit/eat-common"
import { Address } from "@curefit/eat-common"
import { CustomerIssueType } from "@curefit/issue-common"
import { DiagnosticProduct } from "@curefit/care-common"
import { ProductPrice } from "@curefit/product-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { BookingDetail, Center, DiagnosticTestProduct, PackageProduct, Patient } from "@curefit/albus-client"
import { PackOffersResponse } from "@curefit/offer-common"
import { OfferUtil } from "@curefit/base-utils"
import {
  Action,
  CenterInfoWidget,
  CenterSelectionWidget,
  getOffersWidget,
  GradientCard,
  Header,
  InfoCard,
  ManageOptionPayload,
  ManageOptions,
  NavigationCardWidget,
  ProductDetailPage,
  ProductGridWidget,
  ProductListWidget,
  WidgetView
} from "../common/views/WidgetView"
import { PageWidget, PageWidgetTitle } from "../page/Page"
import {
  DiagnosticsTestListWidget,
  DiagnosticsTestReportSummaryWidget,
  DiagnosticTestReportItem
} from "../page/PageWidgets"
import { ActionUtil } from "@curefit/base-utils"
import { CareUtil } from "../util/CareUtil"
import { CareProductStateView } from "./CareProductStateView"
import HCUDetailsPageConfig from "./HCUDetailsPageConfig"
import { UserContext } from "@curefit/userinfo-common"
import { SUPPORT_DEEP_LINK } from "../util/AppUtil"

export interface ReportIssueMeta {
  code: string
  title: string
  confirmation: string
}

class DiagnosticHealthCheckupView extends ProductDetailPage {
  public pageContext: any

  constructor(userContext: UserContext, pageConfig: HCUDetailsPageConfig, isInternalUser: boolean, isNotLoggedIn: boolean, product: DiagnosticProduct,
    newReportIssueManageOption: ManageOptionPayload,
    issuesMap: Map<string, CustomerIssueType[]>,
    centerInfo: Center, packageProduct?: PackageProduct, patientsList?: Patient[], bookingInfo?: BookingDetail, bundleoffers?: PackOffersResponse,
    reportEstimationTime?: any) {
    super()
    const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "DESKTOP")
    const offerDetails = OfferUtil.getPackOfferAndPrice(product, bundleoffers)
    const offerIds = _.map(offerDetails.offers, offer => { return offer.offerId })
    this.widgets.push(this.getHealthCheckupSummaryWidget(userAgent, product, bookingInfo, newReportIssueManageOption, issuesMap, offerDetails.price, offerIds))
    if (_.isEmpty(bookingInfo)) {
      if (CareUtil.isMultiCenterSupported(userContext, pageConfig)) {
        this.widgets.push(this.getAvailableCentersWidget(product))
      }

      const subtitle = packageProduct.basePackageProduct.items.length > 0 ? `${packageProduct.basePackageProduct.items.length} basic test that will be conducted during the tests` : undefined
      if (!_.isEmpty(offerIds)) {
        this.widgets.push(getOffersWidget("Offers applied", offerDetails.offers))
      }
      this.widgets.push(this.getWhatsInPackWidget(pageConfig, product.productId))
      if (!_.isEmpty(centerInfo)) {
        const address: Address = {
          addressString: centerInfo.address,
          locality: "",
          pincode: "560102",
          latLong: {
            lat: centerInfo.latitude,
            long: centerInfo.longitude
          },
        }
        this.widgets.push(new CenterInfoWidget(centerInfo.id, centerInfo.name, ActionUtil.viewMap(centerInfo.placeUrl), address, centerInfo.placeUrl))
      }

      this.widgets.push(this.getDiagnosticsTestListWidget(userContext, product.productId, "What is being tested", 2, packageProduct.basePackageProduct.items,
        false, subtitle, true))
      this.widgets.push(this.getDiagnosticsTestListWidget(userContext, product.productId, "Available Add Ons", 1, packageProduct.addOnProducts, true, undefined, false))
      this.widgets.push(this.getHowItWorksWidget(pageConfig))
      this.actions = this.getPreBookingActions(product, isNotLoggedIn, patientsList, packageProduct.basePackageProduct.code, offerIds)

    } else if (bookingInfo.booking.status !== "CANCELLED") {
      const productStateWidget = new CareProductStateView(userContext, product.productId, bookingInfo, reportEstimationTime)
      this.widgets.push(productStateWidget.widget)
      const reportWidget = this.getDiagnosticReportSummaryWidget(userContext, bookingInfo)
      if (reportWidget) {
        this.widgets.push(reportWidget)
      }
      this.actions = this.getActions(userContext, bookingInfo, isInternalUser, productStateWidget.primaryAction)
    }
  }

  private getAvailableCentersWidget(product: DiagnosticProduct): CenterSelectionWidget {
    const action: Action = {
      actionType: "NAVIGATION",
      url: `curefit://selectcarecenter?productId=${product.productId}`
    }
    const centerSelectionWidget: CenterSelectionWidget = {
      title: "View Available Centres",
      canChangeCenter: true,
      widgetType: "CENTER_PICKER_WIDGET",
      dividerType: "SMALL",
      action: action
    }
    return centerSelectionWidget
  }

  private getIssueList(issuesMap: Map<string, CustomerIssueType[]>): { code: string, title: string, confirmation: string }[] {
    const issueList: { code: string, title: string, confirmation: string }[] = []
    issuesMap.get("DiagnosticsSingleBooked").forEach(customerIssueType => {
      issueList.push({
        code: customerIssueType.code,
        title: customerIssueType.subject,
        confirmation: customerIssueType.confirmation
      })
    })
    return issueList
  }

  private getManageOptions(bookingDetail: BookingDetail, orderId: string, productId: string, issuesMap: Map<string, CustomerIssueType[]>): { manageOptions: ManageOptions, meta: any } {
    const options: ManageOptionPayload[] = []
    options.push(
      {
        isEnabled: true,
        displayText: "Need Help",
        type: "REPORT_ISSUE",
        meta: this.getIssueList(issuesMap),
        action: SUPPORT_DEEP_LINK
      }
    )

    const manageOptions: ManageOptions = {
      displayText: "...",
      options: options
    }

    const meta: any = {
      orderId: orderId,
      productId: productId,
      tcBookingId: bookingDetail.booking.id
    }
    return { manageOptions: manageOptions, meta: meta }
  }

  private getActions(userContext: UserContext, bookingInfo: BookingDetail, isHomeCollectionEnabled: boolean, primaryAction?: Action): Action[] {
    const bundleBookingCTA = bookingInfo.bundleSetupInfo.bundleBookingCTA
    switch (bundleBookingCTA) {
      case "BOOK_TEST":
        return CareUtil.getBookTestActions(userContext, bookingInfo, isHomeCollectionEnabled)
      case "TAKE_HEALTH_ASSESSMENT":
        return CareUtil.getHealthAssessmentActions(bookingInfo, "Start now")
      case "DISCUSS_REPORT":
        return (primaryAction ? [primaryAction] : this.getBookConsultationActions(bookingInfo))
      case "VIEW_PRESCRIPTION":
        return CareUtil.getViewPrescriptionActions(bookingInfo)
      case "VIEW_PLAN":
        return this.getViewPlanActions(bookingInfo)
    }
  }

  private getBookConsultationActions(bookingInfo: BookingDetail): Action[] {
    const consultationStep = bookingInfo.bundleSetupInfo.bundleStepInfos.find(step => step.setupStep === "CONSULTATION")
    return [
      {
        // TODO add support for two product codes
        url: `curefit://selectCareDateV1?productId=${consultationStep.stepMeta[0].productCodes[0]}&parentBookingId=${bookingInfo.booking.id}&patientId=${bookingInfo.booking.patientId}`,
        actionType: "NAVIGATION",
        title: "Book consultation"
      }
    ]
  }


  private getViewPlanActions(bookingInfo: BookingDetail): Action[] {
    return [
      {
        actionType: "NAVIGATION",
        title: "View plan"
      }
    ]
  }

  private getHealthCheckupSummaryWidget(userAgent: UserAgent, product: DiagnosticProduct, bookingDetail: BookingDetail,
    newReportIssueManageOption: ManageOptionPayload,
    issuesMap: Map<string, CustomerIssueType[]>, price: ProductPrice, offerIds?: string[]): WidgetView {
    let manageOptionsView: { manageOptions: ManageOptions; meta: any } = null
    if (!_.isEmpty(bookingDetail)) {
      let manageOptions: ManageOptions
      if (newReportIssueManageOption) {
        manageOptions = {
          displayText: "Manage",
          options: [newReportIssueManageOption]
        }
        manageOptionsView = {
          manageOptions: manageOptions,
          meta: undefined
        }
      } else {
        manageOptionsView = this.getManageOptions(bookingDetail, bookingDetail.booking.cfOrderId, bookingDetail.booking.productCode, issuesMap)
      }
    }

    const checkupSummaryWidget: WidgetView & {
      productId: string;
      title: string;
      subTitle: string;
      price: ProductPrice;
      image: string;
      meta: any
      manageOptions: ManageOptions,
      breadcrumb?: { text: string, link?: string }[],
      dividerType?: string,
      offerIds?: string[]
    } = {
      widgetType: "HEALTHCHECKUP_SUMMARY_WIDGET",
      subTitle: this.getSubTitle(bookingDetail),
      title: product.title,
      productId: product.productId,
      price: !_.isEmpty(bookingDetail) ? null : price,
      image: product.heroImageUrl,
      dividerType: "SMALL",
      manageOptions: !_.isEmpty(manageOptionsView) ? manageOptionsView.manageOptions : null,
      meta: !_.isEmpty(manageOptionsView) ? manageOptionsView.meta : null,
      breadcrumb: userAgent === "DESKTOP" ? [{ text: "Home", link: "/" }, { text: "Care", link: "/care" }, { text: "Health checkup" }] : [],
      offerIds: offerIds
    }
    return checkupSummaryWidget
  }

  private getSubTitle(bookingDetail: BookingDetail) {
    const age = !_.isEmpty(bookingDetail) && !_.isEmpty(bookingDetail.bundleOrderResponse.patient.formattedAge) ? bookingDetail.bundleOrderResponse.patient.formattedAge.numOfYears : undefined
    if (!_.isEmpty(bookingDetail)) {
      if (bookingDetail.booking.status === "CANCELLED") {
        return "Cancelled"
      }
      else if (age) {
        return `For ${bookingDetail.bundleOrderResponse.patient.name}, ${age}`
      } else {
        return `For ${bookingDetail.bundleOrderResponse.patient.name}`
      }
    } else {
      return `The best way to prevent diseases is to detect them early. Opt for our personalised full-body health check-up and get complete medical & lifestyle care advice & prescription from our doctor.`
    }
  }

  private getWhatsInPackWidget(pageConfig: HCUDetailsPageConfig, productId: string): ProductGridWidget {
    const header: Header = {
      title: pageConfig.whatsInThePackTitle,
      color: "#000000"
    }

    const cards: GradientCard[] = []
    pageConfig.whatsInThePackItemList.forEach(item => {
      cards.push({
        title: item.title,
        subTitle: item.shortDescription,
        shadowColor: item.shadowColor,
        gradientColors: item.gradientColors,
        icon: item.icon
      })
    })
    return new ProductGridWidget("GARDIENT_CARD", header, cards)
  }

  private getHowItWorksWidget(
    pageConfig: HCUDetailsPageConfig
  ): ProductListWidget {
    const header: Header = {
      title: pageConfig.howItWorksTitle,
      color: "#000000"
    }

    const infoCards: InfoCard[] = []
    pageConfig.howItWorksItemList.forEach(item => {
      infoCards.push({
        subTitle: item.text,
        icon: item.icon
      })
    })
    return new ProductListWidget("SMALL", header, infoCards)
  }

  private getDiagnosticsTestListWidget(userContext: UserContext, productId: string, title: string, gridSize: number, bundleProducts: DiagnosticTestProduct[], addOns: boolean, subTitle?: string, showCountOfTests?: boolean): PageWidget {
    const infoCards: InfoCard[] = []
    let totalParameters: number = 0
    bundleProducts.map((product, index) => {
      product.countParameters = this.countParameters(product)
      totalParameters = totalParameters + product.countParameters
      infoCards.push({
        title: product.name,
        image: product.imageUrl,
        moreIndex: index,
        countParameters: product.countParameters
      })
    })
    const action: Action = {
        actionType: CareUtil.getNewCarouselListActionType(userContext, "SHOW_DIAGNOSTICS_TEST_DETAIL_LIST"),
        meta: {
        type: "DIAGNOSTIC_TEST_DETAIL_LIST",
        productId: productId,
        bundleProducts: bundleProducts,
        totalParameters: showCountOfTests ? totalParameters : undefined
      }
    }
    const widgetTitle: PageWidgetTitle = {
      title: title
    }
    return new DiagnosticsTestListWidget(
      gridSize,
      infoCards,
      widgetTitle,
      action
    )
  }

  private countParameters(product: DiagnosticTestProduct) {
    if (product.items.length > 0) {
        let count = 0
        product.items.map(( item) => {
            count = count + this.countParameters(item)
        }
        )
        return count
    }
    return 1
}

  private getDiagnosticReportSummaryWidget(userContext: UserContext, bookingInfo: BookingDetail): DiagnosticsTestReportSummaryWidget | NavigationCardWidget {
    const reportItems: DiagnosticTestReportItem[] = []
    const indexOfDiagnosticStep = bookingInfo.bundleSetupInfo.bundleStepInfos.findIndex((stepInfo => {
      return stepInfo.setupStep === "DIAGNOSTIC_TEST"
    }))
    const state = bookingInfo.bundleSetupInfo.bundleStepInfos[indexOfDiagnosticStep].stepState
    if (state !== "REPORT_GENERATED") {
      return
    }
    const diagnosticOrderResponse = bookingInfo.bundleSetupInfo.bundleStepInfos[indexOfDiagnosticStep].testBookingInfo.diagnosticsTestOrderResponse[0]
    const reportInfo = diagnosticOrderResponse.finalDiagnosticReport.diagnosticCheckUpReportInfo
    const widget = CareUtil.getTestReportNullAction(userContext, diagnosticOrderResponse, reportInfo)
    if (widget) {
      return widget
    }
    const testDetails = CareUtil.getTestReportDetailsView(reportInfo)
    reportItems.push(
      {
        header: {
          title: reportInfo.testId,
          subtitle: "View now", // `${reportInfo.testCount} tests`,
          action: {
            url: ActionUtil.diagnosticReportPage(diagnosticOrderResponse.orderId, diagnosticOrderResponse.carefitOrderId),
            actionType: "NAVIGATION"
          }
        },
        // testInfos: testDetails
      }
    )
    return new DiagnosticsTestReportSummaryWidget(reportItems)
  }

  private getPreBookingActions(product: Product, isNotLoggedIn: boolean, patientsList: Patient[], productCode: string, offerIds: string[]): Action[] {
    if (isNotLoggedIn === true) {
      return [
        {
          actionType: "SHOW_ALERT_MODAL",
          title: "Get pack",
          meta: {
            title: "Login Required!",
            subTitle: "Please login to continue",
            actions: [{ actionType: "LOGOUT", title: "Login" }]
          }
        }
      ]
    } else {
      let actionString: string = `curefit://carecartcheckout?productId=${product.productId}&productCode=${productCode}&subCategoryCode=${(<DiagnosticProduct>product)?.subCategoryCode}`
      if (!_.isEmpty(offerIds)) {
        actionString += `&offerIds=${offerIds.join(",")}`
      }
      if (!_.isEmpty(patientsList)) {
        const isSelfPatientPresent = patientsList.find(patient => patient.relationship === "Self")
        const defaultRelationShip = isSelfPatientPresent ? {patientRelation: "Other"} : {}
        return [
          {
            actionType: "SHOW_PATIENT_SELECTION",
            title: "Get pack",
            meta: {
              action: {
                actionType: "NAVIGATION",
                url: actionString
              },
              url: actionString,
              patientsList: patientsList,
              relations: isSelfPatientPresent ? CareUtil.getOtherRelation() : CareUtil.getRelations(),
              emergencyContactRelations: CareUtil.getEmergencyRelations(),
              guardianRelations: CareUtil.getGuardianRelations(),
              reqParams: {
                formUserType: "CARE_USER",
                ...defaultRelationShip
              },
            }
          }
        ]
      } else {
        return [
          {
            actionType: "ADD_PATIENT",
            title: "Get pack",
            meta: {
              reqParams: {
                formUserType: "CARE_USER"
              },
              action: {
                actionType: "NAVIGATION",
                url: actionString
              },
              url: actionString,
              relations: CareUtil.getRelations(),
              emergencyContactRelations: CareUtil.getEmergencyRelations(),
              guardianRelations: CareUtil.getGuardianRelations()
            }
          }
        ]
      }
    }

  }
}

export default DiagnosticHealthCheckupView
