import * as _ from "lodash"
import { Action } from "../common/views/WidgetView"
import { CareUtil } from "../util/CareUtil"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { IHealthfaceService, BookingDetail, Consultation, ALBUS_CLIENT_TYPES } from "@curefit/albus-client"
import {
    ActionableCardItem,
    CareMeEmptyListingWidget,
    ListingActionableCardWidget, Status,
    SupportActionableCardWidget, SupportEmptyListingWidget
} from "../page/PageWidgets"
import { ActionUtil } from "@curefit/base-utils"
import { ActionUtil as AppActionUtil } from "../util/ActionUtil"
import { UserContext } from "@curefit/userinfo-common"
import IssueBusiness from "../crm/IssueBusiness"
import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { PageWidget } from "../page/Page"
import { SupportListPageView } from "../crm/SupportListPageView"
import { TimeUtil } from "@curefit/util-common"
import AppUtil from "../util/AppUtil"
import { ProductType } from "@curefit/product-common"
import { ISegmentService } from "@curefit/vm-models"


const confirmedStatus: Status = {
    text: "CONFIRMED",
    colour: "#000000"
}

export const ConsultationBookingUserStatus: { [key: string]: any; } = {
    BOOKED: confirmedStatus,
    SCHEDULED: confirmedStatus,
    STARTED: confirmedStatus,
    COMPLETED: {
        text: "COMPLETED",
        colour: "#50d166"
    },
    CANCELLED: {
        text: "CANCELLED",
        colour: "#e05343"
    },
    MISSED: {
        text: "MISSED",
        colour: "#d1d1d1"
    }
}

@injectable()
class ConsultationListPageViewBuilder {
    constructor(
        @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness,
        @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
        @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService,
    ) {
    }

    getBookingPageUrl(userContext: UserContext) {
        if (AppUtil.isInternationalTLApp(userContext)) {
            return "curefit://tl-workouts-tab?pageId=tl-workouts-tab"
        }
        if (AppUtil.isInternationalApp(userContext)) {
            return "curefit://tabpage?pageId=cult-intl"
        }
        return "curefit://tabpage?pageId=cult&selectedTab=LivePT"
    }

    async buildConsultationListPageView(userContext: UserContext, consultations: Consultation[], appVersion: number, cityId: string, pageNumber: number, pageFrom?: string, familyType?: string, productType?: ProductType): Promise<SupportListPageView> {
        const widgets: PageWidget[] = []
        let actions: Action[]
        if (_.isEmpty(consultations) && !(await AppUtil.isNewSupportInFlutterSupported(this.segmentService, userContext)) ) {
            if (pageNumber === 0) {
                const icon = "CONSULTATION"
                let title, subTitle
                switch (familyType) {
                    case "PROCEDURE":
                        title = "No Procedure Yet!"
                        subTitle = "Consult our Dermatologists & Trichologists for all your skin and hair care needs at the care.fit center. We promise on-time consultation - no queues with minimum waiting time."
                        break
                    case "CONSULTATION":
                    default:
                        title = "No Consultation Yet!"
                        subTitle = "Consult our General Physicians & Paediatricians to provide care from common issues to complex illness. We promise on-time consultation - no queues & waiting."
                        break
                }
                if (productType === "LIVE_PERSONAL_TRAINING") {
                    widgets.push(new SupportEmptyListingWidget("PERSONAL_TRAINING", "No Classes Yet!", "Book an at home session and get your fitness journey going. It's fitness made fun and easy."))
                } else {
                    widgets.push(new CareMeEmptyListingWidget(icon, title, subTitle))
                }
                if (productType === "LIVE_PERSONAL_TRAINING") {
                    actions = [{
                        actionType: "NAVIGATION",
                        url: this.getBookingPageUrl(userContext),
                        title: "Book Now"
                    }]
                } else {
                    actions = [{
                        actionType: "NAVIGATION",
                        url: AppUtil.isWeb(userContext) ? ActionUtil.careFitClp("clpconsultation") : CareUtil.getConsultationClpListPage(),
                        title: "Book Now"
                    }]
                }

            }
        } else if (pageFrom === "support") {
            const cardWidgetPromises: Promise<SupportActionableCardWidget>[] = _.map(consultations, consultation => {
                return this.getActionableCardWidgetSupport(userContext, consultation, cityId)
            })
            const cardWidgets: SupportActionableCardWidget[] = await Promise.all(cardWidgetPromises)
            cardWidgets.map(cardWidget => {
                widgets.push(cardWidget)
            })
        } else {
            consultations.map(consultation => {
                widgets.push(this.getActionableCardWidget(userContext, consultation, cityId))
            })
        }
        return new SupportListPageView(widgets, actions)
    }

    async buildConsultationListWidgets(userContext: UserContext, consultations: Consultation[], appVersion: number, cityId: string, pageFrom?: string): Promise<SupportActionableCardWidget[]> {
        const widgets: SupportActionableCardWidget[] = []
        if (pageFrom === "support") {
            const cardWidgetPromises: Promise<SupportActionableCardWidget>[] = _.map(consultations, consultation => {
                return this.getActionableCardWidgetSupport(userContext, consultation, cityId)
            })
            const cardWidgets: SupportActionableCardWidget[] = await Promise.all(cardWidgetPromises)
            cardWidgets.map(cardWidget => {
                widgets.push(cardWidget)
            })
        } else {
            consultations.map(consultation => {
                widgets.push(this.getActionableCardWidget(userContext, consultation, cityId))
            })
        }
        return widgets
    }

    private getActionableCardWidget(userContext: UserContext, consultation: Consultation, cityId: string): ListingActionableCardWidget {
        const consultationWidgetData = this.getConsultationWidget(userContext, consultation, cityId)
        return {
            widgetType: "LISTING_ACTIONABLE_CARD_WIDGET",
            ...consultationWidgetData
        }
    }

    private async getActionableCardWidgetSupport(userContext: UserContext, consultation: Consultation, cityId: string): Promise<SupportActionableCardWidget> {
        const consultationWidgetData = this.getConsultationWidget(userContext, consultation, cityId)
        const bookingDetailPromise: Promise<BookingDetail> = this.healthfaceService.getBookingDetailForBookingId(consultation.bookingId, consultation.consultationProduct.tenant)
        const bookingDetail: BookingDetail = await bookingDetailPromise
        const reportIssueParams = this.issueBusiness.getConsulationIssueParams(bookingDetail)
        const time = consultationWidgetData.footer[0].text
        consultationWidgetData.cardAction = {
            meta: (await AppUtil.isNewSupportInFlutterSupported(this.segmentService, userContext)) ? {
                title: consultationWidgetData.title,
                subTitle: "What is your primary concern with the session"
            } : null,
            actionType: "NAVIGATION",
            url: await AppActionUtil.getIssuesUrl()
        }
        if (AppUtil.isWeb(userContext)) {
            consultationWidgetData.footer = []
        }
        if (consultationWidgetData.imageUrl == null || consultationWidgetData.imageUrl === "") {
            consultationWidgetData.imageUrl = "image/<EMAIL>"
        }
        return {
            widgetType: "SUPPORT_ACTIONABLE_CARD_WIDGET",
            status: ConsultationBookingUserStatus[consultation.consultationUserState],
            time: time,
            timestamp: consultation.startTime,
            timezone: userContext.userProfile.timezone,
            ...consultationWidgetData
        }
    }

    private getConsultationWidget(userContext: UserContext, consultation: Consultation, cityId: string): ActionableCardItem {
        const tz = userContext.userProfile.timezone
        const actions: Action[] = this.getActions(userContext, consultation, cityId)
        const subTitle = consultation.doctor ? `With ${consultation.doctor.name} for ${consultation.patient.name}` : `for ${consultation.patient.name}`
        const redirectToOrderPage = CareUtil.isLiveWorkoutConsultationDoctorType(consultation.doctorType)
        const consultationProduct = consultation.consultationProduct
        const vertical = CareUtil.getVerticalForConsultation(consultationProduct?.doctorType)
        const url = redirectToOrderPage ? `curefit://myorderdetail?orderId=${consultation.booking.cfOrderId}` : ActionUtil.teleconsultationSingle(userContext, consultation.booking.productCode, consultationProduct?.urlPath, consultation.bookingId.toString(), undefined, vertical)
        const meta = redirectToOrderPage ? {
            orderDetail: {
                orderId: consultation.booking.cfOrderId
            }
        } : undefined
        return {
            tag: CareUtil.getConsultationTag(consultation.consultationUserState, consultation.status),
            title: `${_.get(consultation, "consultationProduct.name", consultation.doctorTypeCodeResponse.displayValue + " Consultation")}`,
            subTitle: subTitle,
            imageUrl: consultation.doctor ? consultation.doctor.displayImage : "",
            imageShape: "circle",
            footer: CareUtil.getUpcomingConsultationFooterInfo(consultation, tz),
            actions: actions,
            cardAction: {
                actionType: "NAVIGATION",
                url,
                meta
            }
        }
    }

    private getActions(userContext: UserContext, consultation: Consultation, cityId: string): Action[] {
        const actions: Action[] = []
        const userAgent: UserAgent = userContext.sessionInfo.userAgent
        if (consultation.consultationUserState === "COMPLETED") {
            if (consultation.hasPrescription && !CareUtil.isMindDoctorType(consultation.doctorType) && consultation.hasDigitalDocument) {
                actions.push({
                    title: "Prescription",
                    actionType: "NAVIGATION",
                    icon: "PRESCRIPTION",
                    url: `curefit://carefitPrescription?tcBookingId=${consultation.bookingId}&productId=${consultation.booking.productCode}`
                })
            }
            if (consultation.doctorType === "PHYSIOTHERAPIST" && consultation.consultationProduct.hasPlan && consultation.booking.id && consultation.id) {
                actions.push({
                    title: "Email Plan",
                    actionType: "EMAIL_PLAN",
                    icon: "EMAIL",
                    meta: {
                        bookingId: consultation.booking.id,
                        appointmentId: consultation.id
                    }
                })
            }
            if (consultation.followUpContext && consultation.followUpContext.enabled) {
                const parentBookingId = consultation.bookingId
                const followUpConsultationId = consultation.id
                actions.push(CareUtil.getFollowupAction(userContext, parentBookingId, consultation.followupProducts, followUpConsultationId, consultation.patient.id, consultation?.doctor?.id, consultation?.center?.id))
            }
        }
        if (consultation.appointmentActionsWithContext && consultation.appointmentActionsWithContext.chatActionWithContext && consultation.appointmentActionsWithContext.chatActionWithContext.action.actionPermitted && (userAgent === "DESKTOP" || userAgent === "MBROWSER") && consultation.doctor) {
            const doctorSummary = {
                image: _.get(consultation, "doctor.displayImage", ""),
                title: _.get(consultation, "doctor.name", ""),
                subTitle: _.get(consultation, "doctor.qualification", "")
            }
            actions.push({
                title: "Message",
                actionType: "OPEN_MESSAGE_DRAWER",
                icon: "MESSAGE",
                url: ActionUtil.chatMessageActionUrl(consultation.patient.id, CareUtil.getChatChannelWithAppointmentContext(consultation.appointmentActionsWithContext), consultation.doctor.name, consultation.doctor.displayImage, consultation.doctor.qualification, consultation.bookingId),
                meta: doctorSummary
            })
        } else if (consultation.appointmentActionsWithContext && consultation.appointmentActionsWithContext.chatActionWithContext) {
            const action: any = CareUtil.getChatMessageAction(
                userContext,
                _.get(consultation, "appointmentActionsWithContext.chatActionWithContext", null),
                consultation.patient.id,
                consultation.doctor ? consultation.doctor.name : "",
                CareUtil.getChatChannelWithAppointmentContext(consultation.appointmentActionsWithContext),
                consultation.doctor?.displayImage,
                consultation.doctor?.qualification,
                consultation.bookingId
            )
            if (action) {
                actions.push(action)
            }
        }
        if (actions.length <= 2) {
            if (!consultation.isPartOfMultiConsultation &&
                consultation?.appointmentActionsWithContext?.rescheduleActionWithContext?.action?.actionPermitted &&
                consultation?.center?.id
            ) {
                actions.push({
                    title: "Reschedule",
                    isEnabled: true,
                    actionType: userAgent === "DESKTOP" || userAgent === "MBROWSER" ? "SELECT_CARE_DATE" : "RESCHEDULE_TC",
                    icon: "RESCHEDULE",
                    url: ActionUtil.rescheduleUrl(consultation.bookingId, consultation.patient.id, consultation.bookingId, consultation?.center?.id, consultation.booking.productCode),
                    meta: {
                        "tcBookingId": consultation.bookingId,
                        "centerId": consultation?.center?.id,
                        "productId": consultation.booking.productCode,
                        "patientId": consultation.patient.id
                    }
                })
            }
        }
        return actions
    }
}

export default ConsultationListPageViewBuilder
