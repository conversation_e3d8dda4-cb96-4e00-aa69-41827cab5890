import { ConsultationProduct, DOCTOR_TYPE, Patient } from "@curefit/care-common"
import * as _ from "lodash"
import { ProductDetailPage, SearchWidget } from "../common/views/WidgetView"
import { UserContext } from "@curefit/userinfo-common"
import { SpecialistSelectionListWidget, SpecialityItem } from "../page/PageWidgets"
import { CareUtil } from "../util/CareUtil"
import { ActionUtil } from "@curefit/base-utils"
import { Header } from "@curefit/vm-models"

class SelectSpecialityPageView extends ProductDetailPage {
    public header?: Header
    constructor(
        userContext: UserContext,
        productId: string,
        doctorType: DOCTOR_TYPE,
        productList: SpecialityItem[],
        patientList: Patient[],
        product: ConsultationProduct,
        parentBookingId?: number
    ) {
        super()
        // Adding header for deep link as meta is not passed
        this.header = CareUtil.isPTDoctorType(doctorType) ? undefined : {
            title: `Find a ${CareUtil.isMindDoctorType(doctorType) ? "Therapist / Psychiatrist" : CareUtil.getDoctorText(doctorType)}`
        }
        this.widgets.push(this.getSearchWidget(userContext, patientList, product, parentBookingId))
        const widget = this.getSpecilaityListWidget(productList)
        widget && this.widgets.push(widget)
    }

    private getSearchWidget(userContext: UserContext, patientList: Patient[], product: ConsultationProduct, parentBookingId?: number): SearchWidget {
        if (CareUtil.isPTDoctorType(product?.doctorType)) {
            return {
                widgetType: "SEARCH_WIDGET",
                type: "TRAINER",
                title: "Assign me a trainer",
                subTitle: "We will assign you a personal trainer on your first booking.",
                actionText: "Continue",
                containerStyling: {
                    backgroundColor: "white" // "#f1f4f7"
                },
                action: CareUtil.getBundleSessionPurchaseAction(
                    userContext,
                    product,
                    patientList,
                    {
                        actionType: "NAVIGATION",
                        title: "Continue",
                        url: ActionUtil.selectCareDateV1(product.productId, undefined, undefined)
                    },
                    "Continue"
                ),
                hasDividerBelow: false
            }
        }
        const action = CareUtil.specialistListingAction(userContext, product, false, undefined, parentBookingId, undefined, undefined, true, "#FDFDFD,#EEF2F5")
        return {
            widgetType: "SEARCH_WIDGET",
            type: "DOCTOR",
            placeHolder: "Search by name",
            action: {
                ...action,
                meta: CareUtil.isMindDoctorType(product?.doctorType) ? {
                    name: `Find a Therapist / Psychiatrist`
                } : {}
            },
            dividerType: "OR",
        }
    }

    private getSpecilaityListWidget(productList: SpecialityItem[]): SpecialistSelectionListWidget {
        if (_.isEmpty(productList)) {
            return undefined
        }
        return  new SpecialistSelectionListWidget(1, productList, {
            title: "Pick a specialist",
            subTitle: undefined
        })
    }
}
export default SelectSpecialityPageView
