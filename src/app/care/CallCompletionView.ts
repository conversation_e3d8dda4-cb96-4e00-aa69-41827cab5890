import { InfoWidget, ProductDetailPage, WidgetView } from "../common/views/WidgetView"
import { Doctor } from "@curefit/care-common"

class CallCompletionView extends ProductDetailPage {

    constructor(doctor: Doctor) {
        super()
        this.widgets.push(this.getCallCompletionSummaryWidget())
        // this.widgets.push(this.appointmentWithWidget(doctor))
        this.widgets.push(this.reportWillBeReady())

    }

    private getCallCompletionSummaryWidget() {
        const callSummaryWidget: WidgetView & {
            title: string,
            image: string
        } = {
                widgetType: "CALL_COMPLETE_SUMMARY_WIDGET",
                title: "Call Completed!",
                image: "/image/singles/care/tc/confirmation.png"
            }
        return callSummaryWidget
    }


    private appointmentWithWidget(doctor: Doctor) {
        // return new DoctorWidget(doctor)
    }

    private reportWillBeReady() {

        const centerWidget: InfoWidget = {
            widgetType: "INFO_WIDGET",
            icon: "REPORT",
            title: "Preparing Report",
            subTitle: "Your report will be ready in 15 mins. "
        }
        return centerWidget
    }

}
export default CallCompletionView
