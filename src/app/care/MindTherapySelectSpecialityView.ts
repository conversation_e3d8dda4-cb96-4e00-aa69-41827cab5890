import { ConsultationProduct, DiagnosticProduct<PERSON><PERSON>po<PERSON>, Doctor, DOCTOR_TYPE, Patient } from "@curefit/care-common"
import * as _ from "lodash"
import { ProductDetailPage, SearchWidget, SuggestionWidget } from "../common/views/WidgetView"
import { UserContext } from "@curefit/userinfo-common"
import { SpecialistSelectionListWidget, SpecialityItem } from "../page/PageWidgets"
import { CareUtil } from "../util/CareUtil"
import { ActionUtil, CareUtil as BaseCareUtil } from "@curefit/base-utils"
import { CareTeam } from "@curefit/albus-client"

class MindTherapySelectSpecialityView extends ProductDetailPage {
    constructor(
        userContext: UserContext,
        patientList: Patient[],
        product: ConsultationProduct,
        doctorType: DOCTOR_TYPE,
        patientId?: number,
        isMedicalDataRecorded?: boolean,
        preferredTherapists?: CareTeam[],
        preferredPsychiatrists?: CareTeam[],
        parentBookingId?: number
    ) {
        super()
        this.widgets.push(this.getSearchWidget(userContext, patientList, product, doctorType, parentBookingId))
        if (!_.isEmpty(preferredTherapists)) {
            this.widgets.push(CareUtil.getPreferredDoctorBookingWidget(userContext, preferredTherapists[0], patientId))
        }
        if (!_.isEmpty(preferredPsychiatrists)) {
            this.widgets.push(CareUtil.getPreferredDoctorBookingWidget(userContext, preferredPsychiatrists[0], patientId))
        }
        this.widgets.push(this.getRecommendationWidget(userContext, isMedicalDataRecorded, patientId, parentBookingId))
    }

    private getSearchWidget(userContext: UserContext, patientList: Patient[], product: ConsultationProduct, doctorType: DOCTOR_TYPE, parentBookingId?: number): SearchWidget {
        if (CareUtil.isPTDoctorType(product.doctorType)) {
            return {
                widgetType: "SEARCH_WIDGET",
                type: "TRAINER",
                title: "Assign me a trainer",
                subTitle: "We will assign you a personal trainer on your first booking.",
                actionText: "Continue",
                containerStyling: {
                    backgroundColor: "white"
                },
                action: CareUtil.getBundleSessionPurchaseAction(
                    userContext,
                    product,
                    patientList,
                    {
                        actionType: "NAVIGATION",
                        title: "Continue",
                        url: ActionUtil.selectCareDateV1(product.productId, undefined, undefined)
                    },
                    "Continue",
                    undefined
                ),
                hasDividerBelow: false
            }
        }
        const action = CareUtil.specialistListingAction(userContext, product, false, undefined, parentBookingId, undefined, undefined, true, "#FDFDFD,#EEF2F5")
        return {
            widgetType: "SEARCH_WIDGET",
            type: "DOCTOR",
            placeHolder: "Search by name",
            action: {
                ...action,
                meta: CareUtil.isMindDoctorType(doctorType) ? {
                    name: `Find a Therapist`
                } : {}
            },
            dividerType: "OR",
        }
    }

    private getRecommendationWidget(userContext: UserContext, isMedicalDataRecorded: boolean, patientId?: number, parentBookingId?: number): SuggestionWidget {
        return {
            widgetType: "SUGGESTION_WIDGET",
            title: "Help me select",
            description: "Tell us a bit more about yourself. We will connect you to the right Therapist",
            action: CareUtil.getTherapistRecommendationAction("LETS BEGIN", isMedicalDataRecorded, patientId, parentBookingId)
        }
    }
}
export default MindTherapySelectSpecialityView
