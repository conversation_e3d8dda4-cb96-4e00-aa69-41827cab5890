import { Address, FoodProduct as Product } from "@curefit/eat-common"
import { ConsultationProduct, DiagnosticProduct, SUB_CATEGORY_CODE } from "@curefit/care-common"
import * as _ from "lodash"
import { Center, DOCTOR_TYPE } from "@curefit/care-common"
import { Action } from "../common/views/WidgetView"
import { CareUtil } from "../util/CareUtil"
import { AnnouncementView, AnnouncementDetails } from "../announcement/AnnouncementViewBuilder"
import { AnnouncementBusiness } from "../announcement/AnnouncementBusiness"
import { UserContext } from "@curefit/userinfo-common"
import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { ProductPrice } from "@curefit/product-common"
import { OfferV2 } from "@curefit/offer-common"
import LocationUtil from "../util/LocationUtil"

export interface CareCenterHeaderView {
    title: string
    subTitle?: string
    calloutView?: {
        title?: string
        subTitle?: string
        icon?: string
    }
    announcementView?: AnnouncementView
}

export interface CareCenterFooterView {
    title: string
    icon?: string
    subTitle?: string
    imageStyling?: any
    containerStyling?: any
    titleStyling?: any
    subTitleStyling?: any
}


export class CareCenterView {
    constructor(center: Center, action?: Action, price?: ProductPrice, distanceInKms?: number) {
        this.id = center.id
        this.name = center.name
        this.address = {
            addressString: center.address,
            latLong: {
                lat: center.latitude,
                long: center.longitude
            },
            pincode: undefined, // center.code, // Removed it since it is not used and also center code is passing the center phone number for security reasons getting it removed
            mapUrl: center.placeUrl
        }
        this.distanceInKms = distanceInKms,
        this.icon = center.isExternal ? "EXTERNAL" : undefined
        this.mapUrl = center.placeUrl
        this.action = action
        this.price = price
    }
    public id: number
    public name: string
    public action?: Action
    public address: Address
    public distanceInKms?: number
    public mapUrl: string
    public icon: string
    public images?: string[] = []
    public price?: ProductPrice
}

export class CareCenterPageView {
    constructor(public header: CareCenterHeaderView, public centerViews: CareCenterView[], public doctorType?: DOCTOR_TYPE, public footer?: CareCenterFooterView) {

    }
}
@injectable()
class CareCenterViewBuilder {
    constructor(
        @inject(CUREFIT_API_TYPES.AnnouncementBusiness) private announcementBusiness: AnnouncementBusiness
    ) {}
    async buildView(userContext: UserContext, centers: Center[], product?: Product, viewOnly: boolean = false, fromRecommendation?: boolean, showCenterPricing?: boolean, centerPricing?: {[key: string]: {price: ProductPrice, offers: OfferV2[]}}, isNextStepCheckout?: boolean, patientId?: number, isNextValidateAddress?: boolean, subCategoryCode?: SUB_CATEGORY_CODE): Promise<CareCenterPageView> {
        let header: CareCenterHeaderView, footer: CareCenterFooterView
        let isExternalPresent = false
        let doctorType: DOCTOR_TYPE, consultationMode: "ONLINE" | "INCENTRE"
        if (!_.isEmpty(product) && product.productType === "CONSULTATION") { // assuming center selection will be opened in product pages/datepicker
            if (product.productType === "CONSULTATION") {
                doctorType = (<ConsultationProduct>product).doctorType
                consultationMode = (<ConsultationProduct>product).consultationMode
            }
            header = this.buildHeaderForCenterPage(product, doctorType, consultationMode)
        } else if (isNextStepCheckout) {
            header = {title: "Select a Center"}
        } else {
            header = {title: "Centers"}
        }
        if (!subCategoryCode) {
            subCategoryCode = (<DiagnosticProduct>product)?.subCategoryCode
        }
        const centerViews = _.map(centers, center => {
            let action: Action
            // assuming center selection will be opened in product pages/datepicker
            if (!_.isEmpty(product) && !viewOnly) {
                action = {
                    actionType: "UPDATE_CARE_CENTER",
                    meta: {
                        doctorType: doctorType,
                        productId: product.productId,
                        id: product.productId,
                        subCategoryCode: product.categoryId,
                        centerInfo: {
                            id: center.id,
                            name: center.name
                        }
                    }
                }
            } else if (fromRecommendation) {
                action = {
                    actionType: "UPDATE_RECOMMENDATION_CENTER",
                    meta: {
                        centerInfo: {
                            id: center.id,
                            name: center.name
                        }
                    }
                }
            }
            if (isNextStepCheckout) {
                let actionString: string = `curefit://carecartcheckout?productId=${product.productId}&productCode=${product.productId}&patientId=${patientId}&subCategoryCode=${subCategoryCode}`
                actionString += `&centerId=${center.id}`
                action = {
                        actionType: "NAVIGATION",
                        url: actionString,
                        title: "Get pack"
                }
            } else if (isNextValidateAddress) {
                action = this.getAddressValidationAction(product.productId, patientId, subCategoryCode, center.id)
            }
            if (!isExternalPresent && center.isExternal) {
                isExternalPresent = center.isExternal
            }
            const centerPrice = centerPricing && centerPricing[center.id.toString()]
            const price = showCenterPricing && centerPrice && centerPrice.price ? centerPrice.price : undefined
            let distanceInKms
            if (center && center?.latitude && center?.longitude && userContext.sessionInfo.lat && userContext.sessionInfo.lon) {
                distanceInKms = LocationUtil.getDistanceFromLatLonInKm(userContext.sessionInfo.lat, userContext.sessionInfo.lon, center.latitude, center.longitude)
            }
            return new CareCenterView(center, action, price, distanceInKms)
        })
        if (isExternalPresent) {
            footer = {
                icon: "EXTERNAL",
                title: "POWERED BY CAREFIT",
                subTitle: "Center certified for quality care and experience by Carefit",
                containerStyling: {
                    backgroundColor: "#f3f3f3"
                },
                titleStyling: {},
                subTitleStyling: {}
            }
            if (CareUtil.getCareAnnouncementEligibility(userContext)) {
                header.announcementView = <AnnouncementView>await this.announcementBusiness.getAnnouncementToShow(userContext, "care_3p_policy")
            }
        }
        return new CareCenterPageView(header, centerViews,  doctorType, footer)
    }

    private getAddressValidationAction(productCode: string, patientId: number, subCategoryCode: SUB_CATEGORY_CODE, centerId: number ) {
        const forwardAction: Action = this.getCheckoutAction(productCode, patientId, centerId, subCategoryCode)
        const backwardAction: Action = this.getProductPageAction(productCode, subCategoryCode)
        const action: Action = {
            actionType: "VALIDATE_ADDRESS_AND_NAVIGATE",
            meta: {
                forwardAction,
                backwardAction,
                title: "Select your Home Address",
            }
        }
        return action
    }

    private getCheckoutAction(productCode: string, patientId: number, centerId: number, subCategoryCode?: string) {
        const actionString: string = `curefit://carecartcheckout?productId=${productCode}&productCode=${productCode}&patientId=${patientId}&centerId=${centerId}&subCategoryCode=${subCategoryCode}`
        const action: Action = {
            actionType: "NAVIGATION",
            url: actionString,
            title: "Buy Now",
            meta: {
                message: "Location Serviceable - Please purchase the pack and schedule the test later at your convenience",
                actionMessage: "Proceed to Book",
                header: "Serviceability Check"
            }
        }
        return action
    }

    private getProductPageAction(productCode: string, subCategoryCode: SUB_CATEGORY_CODE ) {
        const actionString: string = `curefit://carefitmp?id=${productCode}&subCategoryCode=${subCategoryCode}`
        const action: Action = {
            actionType: "NAVIGATION",
            url: actionString,
            meta: {
                message: "Location Not Serviceable - Apologies. We are yet to provide home collection to your address",
                actionMessage: "Okay",
                header: "Serviceability Check"
            }
        }
        return action
    }
    private  buildHeaderForCenterPage(product: Product, doctorType: DOCTOR_TYPE, consultationMode: "ONLINE" | "INCENTRE"): CareCenterHeaderView {
        if (CareUtil.isPTDoctorType(doctorType)) {
            return {
                title:  "Select a Center",
                calloutView: {
                    title:  "PRO TIP:",
                    subTitle:  "To ensure continuity with your trainer, pick a centre where you can workout regularly."
                }
            }
        } else if (doctorType === "AI_LC") {
            return {
                title:  "Select any Center",
                calloutView: {
                    title:  "Note:",
                    subTitle:  "Coaches are based out of these locations. You will be having a video consultation with them."
                }
            }
        } if (product.productType === "CONSULTATION") {
            if (consultationMode === "ONLINE") {
                return {
                    title: "Select a Center"
                }
            }
        }
        return { title:  "Select a Center" }
    }
}

export default CareCenterViewBuilder
