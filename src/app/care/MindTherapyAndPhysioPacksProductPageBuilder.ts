import {
    ALBUS_CLIENT_TYPES,
    BundleSessionSellableProduct,
    CLPPackItem,
    HealthfaceProductInfo,
    IHealthfaceService, ManagedPlanPackInfo, ConsultationSellableProductResponse
} from "@curefit/albus-client"
import {
    Action,
    ImageOverlayCardContainerWidget,
    MediaAsset,
    OfferData,
    ProductBenefit,
    ProductBenefitWidget
} from "@curefit/apps-common"
import { UserAgentType } from "@curefit/base-common"
import { OfferUtil, RUPEE_SYMBOL } from "@curefit/base-utils"
import {
    ConsultationProduct,
    DiagnosticProductResponse,
    HealthfaceTenant,
    Patient,
    SUB_CATEGORY_CODE,
    CONSULTATION_MODE,
    DiagnosticProduct,
    BaseSellableProduct,
    Doctor
} from "@curefit/care-common"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { OfferV2, PackOffersResponse } from "@curefit/offer-common"
import { ProductType } from "@curefit/product-common"
import { IBaseWidget } from "@curefit/vm-common"
import { CareWidgetUtil, UserContext } from "@curefit/vm-models"
import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import {
    getOffersWidget,
    PricingWidgetRecurringValue, PricingWidgetRecurringValueV2,
    ProductDetailPage,
    ProductPricingSection,
    ProductSummaryWidgetV2
} from "../common/views/WidgetView"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import TimerWidgetV2View from "../page/vm/widgets/TimerWidgetV2View"
import AppUtil from "../util/AppUtil"
import CareUtil, { IProductSummaryParams } from "../util/CareUtil"
import { WidgetView } from "./../common/views/WidgetView"
import _ = require("lodash")
import { ErrorCodes } from "../error/ErrorCodes"
import { ErrorFactory } from "@curefit/error-client"
import { OLLIVANDER_CLIENT_TYPES, IOllivanderCityService } from "@curefit/ollivander-node-client"

const isProdEnv = ["PRODUCTION", "ALPHA"].includes(process.env.ENVIRONMENT)

interface IOfferTimerWidget {
  timerStyle: any
  offerEndDate: any
  timerTitle: string
}

@injectable()
export class MindTherapyPhysioPacksProductPageViewBuilder {
    constructor(
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) protected catalogueService: ICatalogueService,
        @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        @inject(OLLIVANDER_CLIENT_TYPES.IOllivanderCityService) private ollivanderService: IOllivanderCityService
    ) {}
    async getBeforeBookingBundleSessionsPage(userContext: UserContext, productId: string, subCategoryCode: SUB_CATEGORY_CODE, clubCode?: string, setCode?: string) {
        const healthfaceTenant: HealthfaceTenant = CareUtil.getHealthfaceTenant(subCategoryCode)
        const patientsListPromise = this.healthfaceService.getAllPatients(userContext.userProfile.userId)
        let bundleProductIds: string[] = [], singleSessionProduct
        const products: DiagnosticProductResponse[] = await this.healthfaceService.browseProducts("BUNDLE", subCategoryCode, healthfaceTenant, true, setCode, clubCode, undefined, undefined, true)
        bundleProductIds = products.map(product => product.productCode)
        const offerPromise = CareWidgetUtil.getCareProductOffersFromUserContext(
            userContext,
            "BUNDLE",
            bundleProductIds,
            AppUtil.callSourceFromContext(userContext),
            this.serviceInterfaces,
            true
        )
        const bundleProductsPromises = _.map(bundleProductIds, async (productId) => {
        return this.healthfaceService.getProductInfoDetailsCached("BUNDLE", subCategoryCode, productId, healthfaceTenant)
        })
        const healthfaceProducts = await Promise.all(bundleProductsPromises)
        const bundleProducts = _.map(healthfaceProducts, (healthfaceProduct: HealthfaceProductInfo[]) => {
        return <BundleSessionSellableProduct>healthfaceProduct[0].baseSellableProduct
        })

        const selectedProduct = _.find(bundleProducts, product => product.productCode === productId)
        if (subCategoryCode === "PHYSIOTHERAPY") {
            if (!selectedProduct) {
                const productDetails = await this.serviceInterfaces.catalogueService.getProducts([productId]) as ConsultationProduct[]
                singleSessionProduct = _.find(productDetails, product => product.productId === productId)
            }
            return new PhysioPackBeforeBookingPageView().buildView(userContext, clubCode, this.serviceInterfaces, bundleProducts, selectedProduct, singleSessionProduct, await offerPromise, await patientsListPromise)
        }

        if (subCategoryCode === "MIND_THERAPY" && AppUtil.isWeb(userContext)) {
            return this.getTherapyWebPage(userContext, this.serviceInterfaces, bundleProducts, selectedProduct, singleSessionProduct, await offerPromise, await patientsListPromise)
        }

        if (subCategoryCode === "MIND_THERAPY") {
            return new MindTherapyPackBeforeBookingPageView().buildView(userContext, this.serviceInterfaces, bundleProducts, selectedProduct, singleSessionProduct, await offerPromise, await patientsListPromise)
        }
    }

    async getBeforeBookingServiceDetailsPage(userContext: UserContext, subCategoryCode?: SUB_CATEGORY_CODE, selectedProductId?: string, groupType?: string, clubCode?: string, setCode?: string) {
        const { userProfile } = userContext
        const patientsList = await this.healthfaceService.getAllPatients(userContext.userProfile.userId)
        let bundleProductIds: string[] = []
        let bundleProducts
        let bundleOffers
        let selectedBundleProduct
        let consultationProduct
        let consultationOffers
        if (subCategoryCode && clubCode && setCode) {
            const healthfaceTenant: HealthfaceTenant = CareUtil.getHealthfaceTenant(subCategoryCode)
            const diagnosticProductResponse: DiagnosticProductResponse[] = await this.healthfaceService.browseProducts("BUNDLE", subCategoryCode, healthfaceTenant, true, setCode, clubCode, undefined, undefined, true)
            bundleProductIds = diagnosticProductResponse.map(product => product.productCode)
            bundleOffers = await CareWidgetUtil.getCareProductOffersFromUserContext(
                userContext,
                "BUNDLE",
                bundleProductIds,
                AppUtil.callSourceFromContext(userContext),
                this.serviceInterfaces,
                true
            )
            const bundleProductsPromises = _.map(bundleProductIds, async (pId) => {
                return this.healthfaceService.getProductInfoDetailsCached("BUNDLE", subCategoryCode, pId, healthfaceTenant)
            })
            const healthfaceProducts = await Promise.all(bundleProductsPromises)
            bundleProducts = _.map(healthfaceProducts, (healthfaceProduct: HealthfaceProductInfo[]) => {
            return <BundleSessionSellableProduct>healthfaceProduct[0].baseSellableProduct
            })
            selectedBundleProduct = _.find(bundleProducts, product => product.productCode === selectedProductId)
        }

        if (groupType) {
            const consultationSellableProduct = await this.serviceInterfaces.healthfaceService.getConsultationSellableProducts(userProfile.cityId, groupType)
            if (consultationSellableProduct.consultationTypes.length > 0) {
                const { products: consProducts } = consultationSellableProduct.consultationTypes[0]
                const productCodes = consProducts.map(p => p.code)
                const productDetails = await this.serviceInterfaces.catalogueService.getProducts(productCodes) as ConsultationProduct[]
                consultationProduct = productDetails[0]
                consultationOffers = await CareWidgetUtil.getCareProductOffersFromUserContext(
                    userContext,
                    "CONSULTATION",
                    productDetails.map(p => p.productId),
                    AppUtil.callSourceFromContext(userContext),
                    this.serviceInterfaces,
                    true
                )
            }
        }
        if (subCategoryCode === "MIND_THERAPY") {
            return new MindServiceBeforeBookingPageView().buildView(userContext, this.serviceInterfaces, bundleProducts, selectedBundleProduct, bundleOffers, consultationProduct, consultationOffers, patientsList)
        }
    }

    async getAyurvedaBeforeBookingBundleSessionsPage(userContext: UserContext, productId: string, subCategoryCode: SUB_CATEGORY_CODE, clubCode?: string, setCode?: string) {
        const selectedProductId = productId
        const baseProduct = await this.catalogueService.getProduct(selectedProductId)
        const tenant: HealthfaceTenant = CareUtil.getHealthfaceTenant(subCategoryCode)
        const patientsListPromise: Promise<Patient[]> = this.healthfaceService.getAllPatients(userContext.userProfile.userId)
        const allProductsPromise: Promise<DiagnosticProductResponse[]> = this.healthfaceService.browseProducts("BUNDLE", subCategoryCode, tenant, true, setCode, clubCode, false, undefined, true)
        const consultationSellableProduct = await this.healthfaceService.getConsultationSellableProducts(userContext.userProfile.cityId, "AYURVEDA")
        const { products } = consultationSellableProduct.consultationTypes[0]
        const filterType = clubCode === "AYURVEDA_ONLINE" ? "ONLINE" : "INCENTRE"
        const finalProduct = products.filter(product => product.mode === filterType)
        const productCodes = finalProduct.map(product => product.code)
        const consultationProductDetails = await this.catalogueService.getProducts(productCodes) as ConsultationProduct[]
        const singleSessionProduct: DiagnosticProductResponse =  (await this.healthfaceService.browseProducts("CONSULTATION",
            undefined,
            consultationProductDetails[0].tenant,
            true,
            undefined,
            undefined,
            false,
            consultationProductDetails[0].productId,
            true
        ))[0]
        const offerPromise = (async () => {
            const allProducts = await allProductsPromise
            return CareWidgetUtil.getCareProductOffersFromUserContext(
                userContext,
                "BUNDLE",
                !_.isEmpty(allProducts) ? allProducts.map(product => product.productCode) : [baseProduct.productId],
                AppUtil.callSourceFromContext(userContext),
                this.serviceInterfaces,
                true
            )
        })()
        const doctorsPromise = (async () => {
            const bundleProducts = await allProductsPromise
            const selectedBundleProduct: DiagnosticProductResponse = bundleProducts.find(product => product.productCode === baseProduct.productId)
            const productCode = _.get(selectedBundleProduct, "infoSection.doctorInfo.0.productCode", _.get(consultationProductDetails, "0.productId"))
            if (productCode) {
                return this.ollivanderService.getAllDoctorsBasedOnProductCode(productCode)
            }
            return undefined
        })()

        const selectedProduct = _.find(await allProductsPromise, product => product.productCode === productId)
        return new AyurvedaPackBeforeBookingPageView().buildView(
            userContext,
            clubCode,
            this.serviceInterfaces,
            consultationSellableProduct,
            await allProductsPromise,
            selectedProduct,
            singleSessionProduct,
            await offerPromise,
            await patientsListPromise,
            doctorsPromise ? await doctorsPromise : undefined,
        )
    }

    async getTherapyWebPage(userContext: UserContext,
                            interfaces: CFServiceInterfaces,
                            bundleProducts: BundleSessionSellableProduct[],
                            bundleProduct?: BundleSessionSellableProduct,
                            singleSessionProduct?: ConsultationProduct,
                            bundleoffers?: PackOffersResponse,
                            patientsList?: Patient[]) {

        const selectedProduct: BundleSessionSellableProduct  = bundleProduct ? bundleProduct : bundleProducts[0]
        if (_.isEmpty(selectedProduct)) {
            throw this.errorFactory.withCode(ErrorCodes.MIND_THERAPY_PACK_NOT_FOUND_ERR, 400).withDebugMessage("Pack not found").build()
        }

        const offerDetails = OfferUtil.getPackOfferAndPriceForCare(selectedProduct, bundleoffers)
        const offerIds = _.map(offerDetails.offers, (offer: any) => { return offer.offerId })
        const appliedOffers: any[] = CareUtil.getOffersApplied(bundleProducts, selectedProduct, bundleoffers)

        const widgets = []

        let howItWorksItem, whatsInPackItem, packBenefits, packDescription
        selectedProduct.infoSection.children.map(infoSection => {
            switch (infoSection.type) {
                case "PACK_STEPS": howItWorksItem = infoSection; break
                case "PACK_CONTENTS_DETAILED": whatsInPackItem = infoSection; break
                case "FORMATTED_DESCRIPTION": packDescription = infoSection; break
                case "PACK_BENEFITS": packBenefits = infoSection; break
            }
        })

        let offerWidget
        if (!_.isEmpty(appliedOffers)) {
            // offerWidget = CareUtil.getOffersWidget("Offers applied", appliedOffers, userContext.sessionInfo.userAgent)
            offerWidget = getOffersWidget("Offers Applied", appliedOffers, userContext.sessionInfo.userAgent, true)
        }

        widgets.push(this.getPackDetailWidget(bundleProducts, selectedProduct, bundleoffers, appliedOffers, packDescription, userContext))
        offerWidget && widgets.push(offerWidget)
        // widgets.push(this.getProductBenefitWidget(whatsInPackItem, offerWidget && true))
        widgets.push(CareUtil.getHowItWorksWidget(howItWorksItem, userContext.sessionInfo.userAgent, true))

        const numberOfSessions = bundleProduct ? bundleProduct.infoSection.numberOfSessions : 1
        const selectedProductCode = selectedProduct && selectedProduct.productCode

        const selfPatient = !_.isEmpty(patientsList) ? _.find(patientsList, patient => patient.relationship === "Self") : undefined
        let url = `curefit://carecartcheckout?productId=${selectedProductCode}&subCategoryCode=${selectedProduct?.subCategoryCode}`
        if (selfPatient) {
            url += `&patientId=${selfPatient.id}`
        }

        const actions = CareUtil.getMindBundlePreBookingActions(userContext, numberOfSessions, !_.isNil(bundleProduct), offerIds, url, patientsList, selectedProductCode)
        return { widgets: widgets, actions: actions }
    }

    getPackDetailWidget(bundleProducts: BundleSessionSellableProduct[], selectedProduct: BundleSessionSellableProduct, bundleoffers: PackOffersResponse, appliedOffers: any[], packDescription: ManagedPlanPackInfo, userContext: UserContext): ImageOverlayCardContainerWidget {
        const imageOverlayContainerWidget = new ImageOverlayCardContainerWidget()
        imageOverlayContainerWidget.assets.push({
            assetType: "IMAGE",
            assetUrl: selectedProduct.heroImageUrl,
        })
        imageOverlayContainerWidget.cardContainerStyle = { marginHorizontal: 20 }
        imageOverlayContainerWidget.widgets.push(CareUtil.getProductInfoWidget(selectedProduct, packDescription))
        imageOverlayContainerWidget.widgets.push(CareUtil.getProductRadioSelectionWidget(bundleProducts, selectedProduct, bundleoffers, appliedOffers, undefined, undefined,  (_.get(userContext, "sessionInfo.userAgent") === "MBROWSER")))
        imageOverlayContainerWidget.productType = "MIND_THERAPY"
        return imageOverlayContainerWidget
    }

    private getProductBenefitWidget(packBenefits: ManagedPlanPackInfo, offerWidgetExist?: boolean): ProductBenefitWidget {
        if (_.isEmpty(packBenefits)) {
            return undefined
        }
        const data: ProductBenefit[] = []
        packBenefits.children.forEach(item => {
            data.push({
                title: item.title,
                subTitle: item.desc,
                icon: item.imageUrl
            })
        })
        return {
            widgetType: "PRODUCT_BENEFIT_WIDGET",
            header: { title: "Why buy a pack" },
            data: data,
            containerStyle: { marginTop: offerWidgetExist ? 10 : 40 },
            note: {
                tag: {
                    color: "#3888ff",
                    title: "NOTE"
                },
                data: [{
                    info: "Our Therapy packs are available only for yourself. This ensures data security and privacy.",
                    bullets: ""
                }]
            },
            layoutType: "GRID"
        }
    }
}

class PackBasePageView extends ProductDetailPage {

    getPackItemDetailsList(productDetails: ConsultationProduct[], offers: PackOffersResponse, appliedOffers?: any): CLPPackItem[]  {
        const minPricedProduct = _.minBy(productDetails, product => {
            const offerDetails = OfferUtil.getPackOfferAndPrice(product, offers)
            return Math.ceil(offerDetails.price.listingPrice)
        })
        const offerDetails = OfferUtil.getPackOfferAndPrice(minPricedProduct, offers)
        if (appliedOffers) {
            appliedOffers.push(...offerDetails.offers)
        }
        return [{
            title: "1 Session",
            price: offerDetails.price.mrp,
            discountPrice: offerDetails.price.listingPrice,
            hasOfferTag: false,
            currency: offerDetails.price.currency || "INR",
            showPriceCut: offerDetails.price.listingPrice < offerDetails.price.mrp
        }]
    }

    async offerTimerWidget(params: IOfferTimerWidget, interfaces: CFServiceInterfaces, userContext: UserContext): Promise<IBaseWidget> {
        const timerWidgetV2 = new TimerWidgetV2View()
        timerWidgetV2.timerStyle = params.timerStyle
        timerWidgetV2.data = {
            timerEndTimeWithTz: {
                date: params.offerEndDate,
                timezone: userContext.userProfile.timezone
            },
            title: params.timerTitle,
            timerEndTime: undefined,
            privateOfferId: undefined,
          style: undefined,
          action: undefined
        }
        return timerWidgetV2.buildView(interfaces, userContext, undefined)
    }

    async getProductPricingWidget(
        filterType: CONSULTATION_MODE,
        bundleProducts: DiagnosticProductResponse[] | BundleSessionSellableProduct[],
        sellableProductId: string,
        userContext: UserContext,
        interfaces: CFServiceInterfaces,
        bundleoffers?: PackOffersResponse,
        appliedOffers?: any,
        patientsList?: Patient[],
        consultationSellableProduct?: ConsultationSellableProductResponse,
        isMultilinePrice?: boolean
    ): Promise<WidgetView> {
        const recurringSection: PricingWidgetRecurringValue[] = []
        recurringSection.push(await this.getSingleSessionProduct(filterType, userContext, interfaces, sellableProductId, appliedOffers, patientsList, consultationSellableProduct))
        bundleProducts.forEach((product: DiagnosticProductResponse | BundleSessionSellableProduct) => {
            const offerDetails = OfferUtil.getPackOfferAndPriceForCare(<BaseSellableProduct>product, bundleoffers)
            if (product.productCode === sellableProductId && offerDetails && offerDetails.offers) {
                appliedOffers.push(...offerDetails.offers)
            }
            product.listingPrice = offerDetails.price.listingPrice
            const noOfSession = Number(product.infoSection.numberOfSessions)
            const perSessionPrice = Math.floor(product.listingPrice / noOfSession)
            const offerIds = _.map(offerDetails.offers, (offer: any) => { return offer.offerId })
            const infoAction: Action = this.getBundlePreBookingActions(userContext, noOfSession, true, offerIds, `curefit://carecartcheckout?productId=${product.productCode}&subCategoryCode=${product?.subCategoryCode}`, patientsList, product.productCode)[0]
            recurringSection.push({
                title: _.get(product, "infoSection.sellingTitle") ? product.infoSection.sellingTitle : `${noOfSession} Sessions`,
                subTitle: product.infoSection.headerDescription,
                description: product.infoSection.aboutSection,
                price: {
                    mrp: product.mrp,
                    listingPrice: product.listingPrice,
                    showPriceCut: product.listingPrice < product.mrp,
                    currency: offerDetails.price.currency
                },
                priceMeta: `${RUPEE_SYMBOL}${perSessionPrice}/Session`,
                selected: product.productCode === sellableProductId,
                action: {
                    actionType: "NAVIGATION",
                    meta: {
                        sellableProductId: product.productCode
                    }
                },
                isMultilinePrice,
                infoAction
            })
        })
        return {
            widgetType: "PRODUCT_PRICING_WIDGET",
            header: {
                title: "Select Pack"
            },
            sections: [{
            type: "RECURRING",
                value: recurringSection.filter(Boolean)
            }],
            style: {
                // backgroundColor: "#f2f4f8"
            },
            hasDividerBelow: false
        }
    }

    async getProductPricingWidgetV2(
        sellableProductId: string,
        bundleProducts?: DiagnosticProductResponse[] | BundleSessionSellableProduct[],
        bundleOffers?: PackOffersResponse,
        consultationProduct?: ConsultationProduct,
        consultationOffers?: PackOffersResponse,
    ): Promise<WidgetView> {
        const sections: ProductPricingSection[] = []

        if (consultationProduct) {
            const offerDetails = OfferUtil.getPackOfferAndPrice(consultationProduct, consultationOffers)
            consultationProduct.price.listingPrice = offerDetails.price.listingPrice
            const offers: OfferData[] = _.map(offerDetails.offers, (offer: OfferV2) => {
                if (offer.displayContexts?.includes("NONE") || !offer.description) return undefined
                return {
                    title: offer.title,
                    description: offer.description,
                    tnc: offer.tNc,
                    tncURL: offer.tNcUrl
                }
            })
            const consultationPricingItem: PricingWidgetRecurringValueV2 = {
                title: _.get(consultationProduct, "infoSection.sellingTitleV2") ? _.get(consultationProduct, "infoSection.sellingTitleV2") : `Book a session`,
                subTitle: `Validity: Unlimited`,
                offers: offers.filter(offer => !_.isNil(offer)),
                price: {
                    mrp: consultationProduct.price.mrp,
                    listingPrice: offerDetails.price.listingPrice,
                    showPriceCut: offerDetails.price.listingPrice < consultationProduct.price.mrp,
                    currency: offerDetails.price.currency
                },
                selected: consultationProduct.productId === sellableProductId,
                action: {
                    actionType: "NAVIGATION",
                    meta: {
                        sellableProductId: consultationProduct.productId,
                        doctorType: consultationProduct?.doctorType
                    }
                }
            }
            const consultationSection: ProductPricingSection = {
                type: "RECURRING",
                value: [consultationPricingItem]
            }
            sections.push(consultationSection)
        }

        const bundlePricingItems: PricingWidgetRecurringValueV2[] = []
        if (!_.isEmpty(bundleProducts)) {
            bundleProducts.forEach((product: DiagnosticProductResponse | BundleSessionSellableProduct) => {
                const offerDetails = OfferUtil.getPackOfferAndPriceForCare(<BaseSellableProduct>product, bundleOffers)
                product.listingPrice = offerDetails.price.listingPrice
                const noOfSession = Number(product.infoSection.numberOfSessions)
                const perSessionPrice = Math.floor(product.listingPrice / noOfSession)
                const offers = _.map(offerDetails.offers, (offer: OfferV2) => {
                    if (offer.displayContexts?.includes("NONE") || !offer.description) return undefined
                    return {
                        title: offer.title,
                        description: offer.description,
                        tnc: offer.tNc,
                        tncURL: offer.tNcUrl
                    }
                })
                const durationInMonths = _.get(product, "duration") ? `${Math.floor(product.duration / 30)} months` : "Unlimited"
                bundlePricingItems.push({
                    title: _.get(product, "infoSection.sellingTitleV2") ? _.get(product, "infoSection.sellingTitleV2") : `${noOfSession} sessions`,
                    subTitle: `Validity: ${durationInMonths}`,
                    description: product.infoSection.aboutSection,
                    offers: offers.filter(offer => !_.isNil(offer)),
                    price: {
                        mrp: product.mrp,
                        listingPrice: product.listingPrice,
                        showPriceCut: product.listingPrice < product.mrp,
                        currency: offerDetails.price.currency
                    },
                    priceMeta: `${RUPEE_SYMBOL}${perSessionPrice}/session`,
                    selected: product.productCode === sellableProductId,
                    action: {
                        actionType: "NAVIGATION",
                        meta: {
                            sellableProductId: product.productCode
                        }
                    }
                })
            })
        }
        sections.push({
            type: "RECURRING",
                value: bundlePricingItems.filter(Boolean)
            })
        return {
            widgetType: "PRODUCT_PRICING_WIDGET_V2",
            sections,
            hasDividerBelow: false,
        }
    }

    getProductBenefitWidget(packBenefits: ManagedPlanPackInfo): ProductBenefitWidget {
        if (_.isEmpty(packBenefits)) {
            return undefined
        }
        const data: ProductBenefit[] = []
        packBenefits.children.forEach(item => {
            data.push({
                title: item.title,
                subTitle: item.desc,
                icon: item.imageUrl
            })
        })
        return {
            widgetType: "PRODUCT_BENEFIT_WIDGET",
            header: { title: packBenefits.title ? packBenefits.title : "What You Get" },
            data: data,
            layoutType: "GRID",
            hasDividerBelow: false,
        }
    }

    summaryWidget(params: IProductSummaryParams, productType: ProductType,  offerIds?: string[], userAgent?: UserAgentType): ProductSummaryWidgetV2 {
        return CareUtil.getProductSummaryWidget(params, productType, offerIds, userAgent)
      }

    getSingleSessionAboutSection(): string {
        return undefined
    }

    getHeaderDescription(): string {
        return undefined
    }

    getBundlePreBookingActions(userContext: UserContext,  numberOfSessions: number, showPatientSelectionModal: boolean, offerIds: string[], url: string, patientsList: Patient[], productCode: string): Action[] {
        return []
    }

    getSinglePreBookingActions(userContext: UserContext,  numberOfSessions: number, productDetails: ConsultationProduct[], patientsList: Patient[]): Action[] {
        return []
    }

    async getSingleSessionProduct(modeFilterType: CONSULTATION_MODE, userContext: UserContext, interfaces: CFServiceInterfaces, sellableProductId: string, appliedOffers: any, patientsList: Patient[], consultationSellableProduct?: ConsultationSellableProductResponse): Promise<PricingWidgetRecurringValue> {
        return undefined
    }
}

class PhysioPackBeforeBookingPageView extends PackBasePageView {
    async buildView(
      userContext: UserContext,
      clubCode: string,
      interfaces: CFServiceInterfaces,
      bundleProducts: BundleSessionSellableProduct[],
      bundleProduct?: BundleSessionSellableProduct,
      singleSessionProduct?: ConsultationProduct,
      bundleoffers?: PackOffersResponse,
      patientsList?: Patient[],
    ) {
        let offerIds = []
        let appliedOffers: any[] = []
        const filterType = clubCode === "PHYSIOTHERAPY_ONLINE" ? "ONLINE" : "INCENTRE"
        const title = filterType === "ONLINE" ? "Pain Expert Online Consultation" : "Pain Expert Incentre Consultation"
        let productSummaryWidget: ProductSummaryWidgetV2
        const selectedProduct: any  = bundleProduct ? bundleProduct : singleSessionProduct
        const { howItWorksItem, packContentsDetailed, packBenefit } = CareUtil.getPackContent(selectedProduct)
        let selectedProductCode
        if (selectedProduct) {
            selectedProductCode = selectedProduct.productCode || selectedProduct.productId
            if (bundleProduct) {
                const offerDetails = OfferUtil.getPackOfferAndPriceForCare(bundleProduct, bundleoffers)
                offerIds = _.map(offerDetails.offers, (offer: any) => { return offer.offerId })
                appliedOffers = []
            }
            productSummaryWidget = this.summaryWidget({
                    title: title,
                    description: this.getHeaderDescription(),
                    imageUrl: selectedProduct.heroImageUrl,
                    productCode: selectedProductCode
                },
                bundleProduct ? "BUNDLE" : "CONSULTATION",
                offerIds,
                userContext.sessionInfo.userAgent
            )
            this.widgets.push(productSummaryWidget)
        }
        this.widgets.push(await this.getProductPricingWidget(filterType, bundleProducts, selectedProductCode, userContext, interfaces, bundleoffers, appliedOffers, patientsList))
        if (!_.isEmpty(appliedOffers)) {
            // productSummaryWidget.offerTimerWidget = await this.offerTimerWidget({
            // offerEndDate: appliedOffers[0].endDate, timerTitle: "OFFER EXPIRES IN:", timerStyle: {
            //     background: "#3788ff",
            //     borderTopRightRadius: 5,
            //     borderTopLeftRadius: 5,
            // }
            // }, interfaces, userContext)
            this.widgets.push(CareUtil.getCareOfferWidget("Offers Applied", appliedOffers, userContext))
        }
        this.widgets.push(await this.getWhatWeCanHelpWithWidget(userContext, interfaces))
        this.widgets.push(await this.getWhatConditionTreatWidget(userContext, interfaces))
        this.widgets.push(CareUtil.getWhyPackWidget(packContentsDetailed, userContext.sessionInfo.userAgent))
        this.widgets.push(CareUtil.getHowItWorksWidget(packContentsDetailed, userContext.sessionInfo.userAgent))
        this.widgets.push(CareUtil.getHowItWorksWidget(howItWorksItem, userContext.sessionInfo.userAgent))
        this.widgets = this.widgets.filter(Boolean)
        const numberOfSessions = bundleProduct ? bundleProduct.infoSection.numberOfSessions : 1
        if (_.isEmpty(this.actions)) {
            this.actions = this.getBundlePreBookingActions(userContext, numberOfSessions, !_.isNil(bundleProduct), offerIds, `curefit://carecartcheckout?productId=${selectedProductCode}&subCategoryCode=${selectedProduct?.subCategoryCode}`, patientsList, selectedProductCode)
        }
        return this
    }

    async getSingleSessionProduct(modeFilterType: CONSULTATION_MODE, userContext: UserContext, interfaces: CFServiceInterfaces, sellableProductId: string, appliedOffers: any, patientsList: Patient[]): Promise<PricingWidgetRecurringValue> {
        const consultationSellableProduct = await interfaces.healthfaceService.getConsultationSellableProducts(userContext.userProfile.cityId, "PHYSIOTHERAPIST")
        const { products } = consultationSellableProduct.consultationTypes[0]
        const finalProduct = products.filter(product => product.mode === modeFilterType)
        const productCodes = finalProduct.map(product => product.code)
        const offerPromise = CareWidgetUtil.getCareProductOffersFromUserContext(
            userContext,
            "CONSULTATION",
            productCodes,
            AppUtil.callSourceFromContext(userContext),
            interfaces,
            true
        )
        const productDetails = await interfaces.catalogueService.getProducts(productCodes) as ConsultationProduct[]
        const currency = "INR"
        const offersResponse = await offerPromise
        const packItemDetails: CLPPackItem[] = this.getPackItemDetailsList(productDetails, offersResponse, productDetails[0].productId === sellableProductId ? appliedOffers : undefined)
        const action: Action = {
            actionType: "NAVIGATION",
            meta: {
                sellableProductId: productCodes[0],
                doctorType: productDetails[0]?.doctorType
            }
        }
        const isSelectedProduct = productCodes.includes(sellableProductId)
        const actions = this.getSinglePreBookingActions(userContext, 1, productDetails, patientsList)
        if (isSelectedProduct) {
            this.actions = actions
        }
        const infoAction: Action = actions[0]
        const data = {
            title: `1 Session. Kickstarter`,
            subTitle: "Personal assessment and exercise plan",
            action: action,
            description: this.getSingleSessionAboutSection(),
            priceMeta: ``,
            price: {
                title: packItemDetails[0].title,
                listingPrice: packItemDetails[0].discountPrice,
                discountText: packItemDetails.length > 1 ? packItemDetails[1].title : undefined,
                mrp: packItemDetails[0].price,
                currency,
                showPriceCut: packItemDetails[0].showPriceCut
            },
            selected: isSelectedProduct,
            infoAction
        }
        return data
    }

    getSingleSessionAboutSection() {
        return "Our Physiotherapist expert will use this 30-minute session to understand your condition in detail, evaluate possible treatment plans specific to the condition and train you on the relevant therapeutic techniques to treat your condition well. The Physiotherapist will also provide a detailed exercise and therapy plan based on the condition for faster recovery."
    }

    getHeaderDescription() {
      return "Get help for your posture, pain and mobility issues with our expert physiotherapists"
    }

    getBundlePreBookingActions(userContext: UserContext,  numberOfSessions: number, showPatientSelectionModal: boolean, offerIds: string[], url: string, patientsList: Patient[], productCode: string): Action[] {
        const actionTitle = `Book ${numberOfSessions} ${numberOfSessions > 1 ? "Sessions" : "Session"}`
        if (!userContext.sessionInfo.isUserLoggedIn) {
            return [
                {
                    actionType: "SHOW_ALERT_MODAL",
                    title: actionTitle,
                    meta: {
                        title: "Login Required!",
                        subTitle: "Please login to continue",
                        actions: [{ actionType: "LOGOUT", title: "Login" }]
                    }
                }
            ]
        } else {
            if (!_.isEmpty(offerIds)) {
                url += `&offerIds=${offerIds.join(",")}`
            }
            const action: Action = {
                title: actionTitle,
                actionType: "NAVIGATION",
                url: url,
            }
            if (showPatientSelectionModal) {
                const pageAction = CareUtil.getPatientSelectionModalAction(
                    patientsList,
                    AppUtil.isExistingPackPurchaseForPatientSupported(userContext) ? {
                        actionType: "CHECK_BUNDLE_EXISTS",
                        title: actionTitle,
                        meta: {
                            productCode,
                            onSuccessAction: CareUtil.getBundleConsentInfoModal(action),
                            onFailureAction: action
                        }
                    } : action,
                    actionTitle
                )
                return [pageAction]
            } else {
                return [action]
            }
        }
    }

    getSinglePreBookingActions(userContext: UserContext,  numberOfSessions: number, productDetails: ConsultationProduct[], patientsList: Patient[]): Action[] {
        const actionTitle = `Book ${numberOfSessions} ${numberOfSessions > 1 ? "Sessions" : "Session"}`
        if (!userContext.sessionInfo.isUserLoggedIn) {
            return [
                {
                    actionType: "SHOW_ALERT_MODAL",
                    title: actionTitle,
                    meta: {
                        title: "Login Required!",
                        subTitle: "Please login to continue",
                        actions: [{ actionType: "LOGOUT", title: "Login" }]
                    }
                }
            ]
        } else if (!_.isEmpty(productDetails)) {
            if (productDetails.length > 1) {
                const cards: { action: Action[] }[] | { title: string; icon: string; action: any }[] = []
                productDetails.map(product => {
                    const action = CareUtil.specialistListingAction(userContext, product, false, undefined, undefined, undefined, undefined, false, undefined, undefined, undefined, true)
                    if (product.consultationMode === "ONLINE") {
                        cards.push({
                            title: "Video call",
                            icon: "VIDEO",
                            action: action
                        })
                    }
                    else if (product.consultationMode === "INCENTRE") {
                        cards.push({
                            title: "At Centre",
                            icon: "CENTRE",
                            action: action
                        })
                    }
                })
                return [{
                    title: actionTitle,
                    actionType: "SHOW_CARE_ACTION_SHEET",
                    meta: {
                        title: "Choose you consultation mode",
                        cards
                    }
                }]
            }
            return [
                {
                    ...CareUtil.specialistListingAction(userContext, productDetails[0], false, undefined, undefined, undefined, undefined, false, undefined, undefined, undefined, true),
                    title: actionTitle
                }
            ]
        }
    }

    private async getWhatWeCanHelpWithWidget(userContext: UserContext, interfaces: CFServiceInterfaces) {
        const widgetId = isProdEnv ? "37e6a2d9-7148-4638-be6d-d1d06d2f4db6" : "01a18698-535d-47be-b044-bb635e894b1a"
        return CareUtil.getVmWidget(userContext, interfaces, widgetId, "HOW_IT_HELPS_WIDGET")
    }

    private async getWhatConditionTreatWidget(userContext: UserContext, interfaces: CFServiceInterfaces) {
        const widgetId = isProdEnv ? "a4726b63-bf51-4090-a101-d37b79290252" : "1ff5a725-6c41-4928-9157-ec7cbf1e1332"
        return CareUtil.getVmWidget(userContext, interfaces, widgetId, "HOW_IT_HELPS_WIDGET")
    }

}

class MindTherapyPackBeforeBookingPageView extends PackBasePageView {
    async buildView(
      userContext: UserContext,
      interfaces: CFServiceInterfaces,
      bundleProducts: BundleSessionSellableProduct[],
      bundleProduct?: BundleSessionSellableProduct,
      singleSessionProduct?: ConsultationProduct,
      bundleoffers?: PackOffersResponse,
      patientsList?: Patient[],
    ) {
        let offerIds = []
        let appliedOffers: any[] = []
        let productSummaryWidget: ProductSummaryWidgetV2
        const selectedProduct: BundleSessionSellableProduct  = bundleProduct ? bundleProduct : bundleProducts[0]
        let selectedProductCode, howItWorksItem, whatsInPackItem, packFAQ
        if (selectedProduct) {
            selectedProductCode = selectedProduct.productCode
            if (bundleProduct) {
                const offerDetails = OfferUtil.getPackOfferAndPriceForCare(bundleProduct, bundleoffers)
                offerIds = _.map(offerDetails.offers, (offer: any) => { return offer.offerId })
                appliedOffers = []
                productSummaryWidget = this.summaryWidget({
                    title: bundleProduct.infoSection.packTitle || bundleProduct.productName,
                    description: bundleProduct.infoSection.packDescription || selectedProduct.productDescription,
                    imageUrl: selectedProduct.heroImageUrl,
                    productCode: selectedProductCode
                },
                    bundleProduct ? "BUNDLE" : "CONSULTATION",
                    offerIds,
                    userContext.sessionInfo.userAgent
                )
                this.widgets.push(productSummaryWidget)
            }
            _.get(selectedProduct, "infoSection.children", []).map((infoSection: { type: any }) => {
                switch (infoSection.type) {
                    case "PACK_STEPS": howItWorksItem = infoSection; break
                    case "PACK_CONTENTS_DETAILED": whatsInPackItem = infoSection; break
                    case "FAQ": packFAQ = infoSection; break
                }
            })
        }
        this.widgets.push(await this.getProductPricingWidget(undefined, bundleProducts, selectedProductCode, userContext, interfaces, bundleoffers, appliedOffers, patientsList, undefined, true))
        if (!_.isEmpty(appliedOffers)) {
            // productSummaryWidget.offerTimerWidget = await this.offerTimerWidget({
            // offerEndDate: appliedOffers[0].endDate, timerTitle: "OFFER EXPIRES IN:", timerStyle: {
            //     background: "#3788ff",
            //     borderTopRightRadius: 5,
            //     borderTopLeftRadius: 5,
            // }
            // }, interfaces, userContext)
            this.widgets.push(CareUtil.getCareOfferWidget("Offers Applied", appliedOffers, userContext))
            // this.widgets.push({...getOffersWidget("Offers Applied", appliedOffers, userContext.sessionInfo.userAgent), hasDividerBelow: false})
        }
        // this.widgets.push(CareUtil.getWhyPackWidget(whatsInPackItem, userContext.sessionInfo.userAgent))
        this.widgets.push(CareUtil.getFAQWidget(packFAQ))
        this.widgets.push(CareUtil.getHowItWorksWidget(howItWorksItem, userContext.sessionInfo.userAgent))
        this.widgets = this.widgets.filter(Boolean)
        const numberOfSessions = bundleProduct ? bundleProduct.infoSection.numberOfSessions : 1
        if (_.isEmpty(this.actions)) {
            this.actions = CareUtil.getMindBundlePreBookingActions(userContext, numberOfSessions, !_.isNil(bundleProduct), offerIds, `curefit://carecartcheckout?productId=${selectedProductCode}&subCategoryCode=${selectedProduct?.subCategoryCode}`, patientsList, selectedProductCode)
        }
        return this
    }
}

class MindServiceBeforeBookingPageView extends PackBasePageView {
    async buildView(
      userContext: UserContext,
      interfaces: CFServiceInterfaces,
      bundleProducts?: BundleSessionSellableProduct[],
      bundleProduct?: BundleSessionSellableProduct,
      bundleOffers?: PackOffersResponse,
      consultationProduct?: ConsultationProduct,
      consultationOffers?: PackOffersResponse,
      patientsList?: Patient[],
    ) {
        let offerIds = []
        let productSummaryWidget: ProductSummaryWidgetV2
        let selectedProductCode, howItWorksItem, whatsInPackItem, packFAQ
        if (bundleProduct) {
            selectedProductCode = bundleProduct.productCode
            const offerDetails = OfferUtil.getPackOfferAndPriceForCare(bundleProduct, bundleOffers)
            offerIds = _.map(offerDetails.offers, (offer: any) => { return offer.offerId })
            productSummaryWidget = this.summaryWidget({
                title: bundleProduct.infoSection.packTitle || bundleProduct.productName,
                description: bundleProduct.infoSection.packDescription || bundleProduct.productDescription,
                imageUrl: bundleProduct.heroImageUrl,
                productCode: selectedProductCode
            },
                "BUNDLE",
                offerIds,
                userContext.sessionInfo.userAgent
            )
            this.widgets.push(productSummaryWidget)
            _.get(bundleProduct, "infoSection.children", []).map((infoSection: { type: any }) => {
                switch (infoSection.type) {
                    case "PACK_STEPS": howItWorksItem = infoSection; break
                    case "PACK_CONTENTS_DETAILED": whatsInPackItem = infoSection; break
                    case "FAQ": packFAQ = infoSection; break
                }
            })
        } else if (consultationProduct) {
            selectedProductCode = consultationProduct.productId
            const offerDetails = OfferUtil.getPackOfferAndPrice(consultationProduct, consultationOffers)
            offerIds = _.map(offerDetails.offers, (offer: any) => { return offer.offerId })
            productSummaryWidget = this.summaryWidget({
                title: consultationProduct.timelineTitle,
                description: consultationProduct.subTitle,
                imageUrl: consultationProduct.heroImageUrl,
                productCode: selectedProductCode
            },
                "CONSULTATION",
                offerIds,
                userContext.sessionInfo.userAgent
            )
            this.widgets.push(productSummaryWidget)
            _.get(consultationProduct, "infoSection.children", []).map((infoSection: { type: any }) => {
                switch (infoSection.type) {
                    case "PACK_STEPS": howItWorksItem = infoSection; break
                    case "PACK_CONTENTS_DETAILED": whatsInPackItem = infoSection; break
                    case "FAQ": packFAQ = infoSection; break
                }
            })
        }
        this.widgets.push(await this.getProductPricingWidgetV2(selectedProductCode, bundleProducts, bundleOffers, consultationProduct, consultationOffers))
        this.widgets.push(this.getProductBenefitWidget(whatsInPackItem))
        this.widgets.push(CareUtil.getFAQWidget(packFAQ))
        this.widgets.push(CareUtil.getHowItWorksWidget(howItWorksItem, userContext.sessionInfo.userAgent))
        this.widgets = this.widgets.filter(Boolean)
        const numberOfSessions = bundleProduct ? bundleProduct.infoSection.numberOfSessions : 1
        if (_.isEmpty(this.actions)) {
            if (consultationProduct && consultationProduct.productId === selectedProductCode) {
                const doctorTypeText = CareUtil.getDoctorText(consultationProduct.doctorType)
                const pagetitle = `Select a ${doctorTypeText}`
                this.actions = consultationProduct.consultationMode === "INCENTRE"
                    ? [CareUtil.getCareCenterBrowseAction(userContext, [consultationProduct], true)]
                    : [{
                        ...CareUtil.specialistListingAction(userContext, consultationProduct, false, undefined, undefined, undefined, undefined, false, undefined, undefined, undefined, true, pagetitle, undefined, true, true),
                        title: `Find ${doctorTypeText}`,
                        meta: {
                            name: pagetitle,
                        },
                    }]
            } else {
                this.actions = CareUtil.getMindBundlePreBookingActions(userContext, numberOfSessions, !_.isNil(bundleProduct), offerIds, `curefit://carecartcheckout?productId=${selectedProductCode}&subCategoryCode=${bundleProduct?.subCategoryCode}`, patientsList, selectedProductCode)
            }
        }
        return this
    }
}

class AyurvedaPackBeforeBookingPageView extends PackBasePageView {
    async buildView(
      userContext: UserContext,
      clubCode: string,
      interfaces: CFServiceInterfaces,
      consultationSellableProduct: ConsultationSellableProductResponse,
      bundleProducts: DiagnosticProductResponse[],
      bundleProduct?: DiagnosticProductResponse,
      singleSessionProductResponse?: DiagnosticProductResponse,
      bundleoffers?: PackOffersResponse,
      patientsList?: Patient[],
      doctorsList?: Doctor[]
    ) {
        let offerIds = []
        let appliedOffers: any[] = []
        const filterType = clubCode === "AYURVEDA_ONLINE" ? "ONLINE" : "INCENTRE"
        let productSummaryWidget: ProductSummaryWidgetV2
        const singleSessionProduct = singleSessionProductResponse ? CareUtil.toConsultationProduct(singleSessionProductResponse) : undefined
        const selectedProduct: any  = bundleProduct ? bundleProduct : singleSessionProduct
        const selectedProductDoctorType = _.get(selectedProduct, "infoSection.doctorInfo.0.doctorType", _.get(selectedProduct, "doctorType"))
        let selectedProductCode
        const { packBenefit, packContentsDetailed, packHowItHelp } = CareUtil.getPackContent(bundleProduct ? bundleProduct : singleSessionProductResponse)
        if (selectedProduct) {
            selectedProductCode = selectedProduct.productCode || selectedProduct.productId
            if (bundleProduct) {
                const offerDetails = OfferUtil.getPackOfferAndPrice(CareUtil.toDiagnosticsProduct(bundleProduct), bundleoffers)
                offerIds = _.map(offerDetails.offers, (offer: any) => { return offer.offerId })
                appliedOffers = []
            }
            productSummaryWidget = this.summaryWidget({
                    title: singleSessionProduct?.title || selectedProduct.productName,
                    description: this.getHeaderDescription() || singleSessionProduct?.subTitle || selectedProduct.productDescription,
                    imageUrl: selectedProduct.heroImageUrl,
                    productCode: selectedProductCode
                },
                bundleProduct ? "BUNDLE" : "CONSULTATION",
                offerIds,
                userContext.sessionInfo.userAgent
            )
            this.widgets.push(productSummaryWidget)
        }
        this.widgets.push(await this.getProductPricingWidget(filterType, bundleProducts, selectedProductCode, userContext, interfaces, bundleoffers, appliedOffers, patientsList, consultationSellableProduct))
        if (!_.isEmpty(appliedOffers)) {
            this.widgets.push(CareUtil.getCareOfferWidget("Offers Applied", appliedOffers, userContext))
        }
        this.widgets.push(...(!_.isEmpty(doctorsList) && selectedProductDoctorType ? CareUtil.getPackDoctorListingWidgets(userContext, doctorsList, [selectedProductDoctorType], selectedProduct) : []))
        this.widgets.push(CareUtil.getHowItHelpsWidget(packHowItHelp))
        this.widgets.push(CareUtil.getCareProductBenefitWidget(userContext, packBenefit))
        this.widgets.push(CareUtil.getHowItWorksWidget(packContentsDetailed, userContext.sessionInfo.userAgent))
        this.widgets = this.widgets.filter(Boolean)
        const numberOfSessions = bundleProduct ? bundleProduct.infoSection.numberOfSessions : 1
        if (_.isEmpty(this.actions)) {
            this.actions = this.getBundlePreBookingActions(userContext, Number(numberOfSessions), !_.isNil(bundleProduct), offerIds, `curefit://carecartcheckout?productId=${selectedProductCode}&subCategoryCode=${selectedProduct?.subCategoryCode}`, patientsList, selectedProductCode)
        }
        return this
    }

    async getSingleSessionProduct(
        modeFilterType: CONSULTATION_MODE,
        userContext: UserContext,
        interfaces: CFServiceInterfaces,
        sellableProductId: string,
        appliedOffers: any,
        patientsList: Patient[],
        consultationSellableProduct?: ConsultationSellableProductResponse
    ): Promise<PricingWidgetRecurringValue> {
        if (!consultationSellableProduct) {
            consultationSellableProduct = await interfaces.healthfaceService.getConsultationSellableProducts(userContext.userProfile.cityId, "AYURVEDA")
        }
        const { products } = consultationSellableProduct.consultationTypes[0]
        const finalProduct = products.filter(product => product.mode === modeFilterType)
        const productCodes = finalProduct.map(product => product.code)
        const offerPromise = CareWidgetUtil.getCareProductOffersFromUserContext(
            userContext,
            "CONSULTATION",
            productCodes,
            AppUtil.callSourceFromContext(userContext),
            interfaces,
            true
        )
        const productDetails = await interfaces.catalogueService.getProducts(productCodes) as ConsultationProduct[]
        const currency = "INR"
        const offersResponse = await offerPromise
        const packItemDetails: CLPPackItem[] = this.getPackItemDetailsList(productDetails, offersResponse, productDetails[0].productId === sellableProductId ? appliedOffers : undefined)
        const action: Action = {
            actionType: "NAVIGATION",
            meta: {
                sellableProductId: productCodes[0],
                doctorType: productDetails[0]?.doctorType,
            }
        }
        const isSelectedProduct = productCodes.includes(sellableProductId)
        const actions = this.getSinglePreBookingActions(userContext, 1, productDetails, patientsList)
        if (isSelectedProduct) {
            this.actions = actions
        }
        const infoAction: Action = actions[0]
        const data = {
            title: "Single Session",
            // subTitle: undefined,
            action: action,
            description: this.getSingleSessionAboutSection(),
            priceMeta: ``,
            price: {
                title: packItemDetails[0].title,
                listingPrice: packItemDetails[0].discountPrice,
                discountText: packItemDetails.length > 1 ? packItemDetails[1].title : undefined,
                mrp: packItemDetails[0].price,
                currency,
                showPriceCut: packItemDetails[0].showPriceCut
            },
            selected: isSelectedProduct,
            infoAction
        }
        return data
    }

    getBundlePreBookingActions(userContext: UserContext,  numberOfSessions: number, showPatientSelectionModal: boolean, offerIds: string[], url: string, patientsList: Patient[], productCode: string): Action[] {
        const actionTitle = `Book ${numberOfSessions} ${numberOfSessions > 1 ? "Sessions" : "Session"}`
        if (!userContext.sessionInfo.isUserLoggedIn) {
            return [
                {
                    actionType: "SHOW_ALERT_MODAL",
                    title: actionTitle,
                    meta: {
                        title: "Login Required!",
                        subTitle: "Please login to continue",
                        actions: [{ actionType: "LOGOUT", title: "Login" }]
                    }
                }
            ]
        } else {
            if (!_.isEmpty(offerIds)) {
                url += `&offerIds=${offerIds.join(",")}`
            }
            const action: Action = {
                title: actionTitle,
                actionType: "NAVIGATION",
                url: url,
            }
            if (showPatientSelectionModal) {
                return [
                    CareUtil.getPatientSelectionModalAction(
                        patientsList,
                        AppUtil.isExistingPackPurchaseForPatientSupported(userContext) ? {
                            actionType: "CHECK_BUNDLE_EXISTS",
                            title: actionTitle,
                            meta: {
                                productCode,
                                onSuccessAction: CareUtil.getBundleConsentInfoModal(action),
                                onFailureAction: action
                            }
                        } : action,
                        actionTitle
                    )
                ]
            } else {
                return [action]
            }
        }
    }

    getSinglePreBookingActions(userContext: UserContext,  numberOfSessions: number, productDetails: ConsultationProduct[], patientsList: Patient[]): Action[] {
        const actionTitle = `Book ${numberOfSessions} ${numberOfSessions > 1 ? "Sessions" : "Session"}`
        if (!userContext.sessionInfo.isUserLoggedIn) {
            return [
                {
                    actionType: "SHOW_ALERT_MODAL",
                    title: actionTitle,
                    meta: {
                        title: "Login Required!",
                        subTitle: "Please login to continue",
                        actions: [{ actionType: "LOGOUT", title: "Login" }]
                    }
                }
            ]
        } else if (!_.isEmpty(productDetails)) {
            if (productDetails.length > 1) {
                const cards: { action: Action[] }[] | { title: string; icon: string; action: any }[] = []
                productDetails.map(product => {
                    const action = CareUtil.specialistListingAction(userContext, product, false, undefined, undefined, undefined, undefined, false, undefined, undefined, undefined, true)
                    if (product.consultationMode === "ONLINE") {
                        cards.push({
                            title: "Video call",
                            icon: "VIDEO",
                            action: action
                        })
                    }
                    else if (product.consultationMode === "INCENTRE") {
                        cards.push({
                            title: "At Centre",
                            icon: "CENTRE",
                            action: action
                        })
                    }
                })
                return [{
                    title: actionTitle,
                    actionType: "SHOW_CARE_ACTION_SHEET",
                    meta: {
                        title: "Choose you consultation mode",
                        cards
                    }
                }]
            }
            return [
                {
                    ...CareUtil.specialistListingAction(userContext, productDetails[0], false, undefined, undefined, undefined, undefined, false, undefined, undefined, undefined, true),
                    title: actionTitle
                }
            ]
        }
    }

    getSingleSessionAboutSection() {
        return "Our experts in the first session will understand your condition in detail, holistically create a suitable treatment plan to help manage and alleviate the condition for you. Our experts can treat conditions such as thyroid, diabetes, hypertension, gastric conditions, bone and joint conditions, skin and hair conditions, PCOS / PCOD conditions, Weight Management, Panchakarma and many more."
    }

    getHeaderDescription() {
        return `Ayurveda or "The Science of Life" places great emphasis on prevention and encourages the maintenance of health through close attention to balance in one’s life, right thinking, diet, lifestyle and the use of herbs. Knowledge of Ayurveda enables one to understand how to create this balance of body, mind and consciousness according to one’s own individual constitution and how to make lifestyle changes to bring about and maintain this balance.`
    }

}
