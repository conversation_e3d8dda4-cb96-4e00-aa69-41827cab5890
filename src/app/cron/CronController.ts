import * as _ from "lodash"
import { controller, httpGet, httpPut, httpDelete } from "inversify-express-utils"
import { Container, inject } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import AuthMiddleware from "../auth/AuthMiddleware"
import * as express from "express"
import { WidgetRanking } from "./widgetRanking/WidgetRankingModel"
import { WidgetRankingBusiness } from "./widgetRanking/WidgetRankingBusiness"


function controllerFactory(kernel: Container) {

    @controller("/cron", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class CronController {
        constructor(
            @inject(CUREFIT_API_TYPES.WidgetRankingBusiness) private widgetRankingBusiness: WidgetRankingBusiness
        ) {
        }

        // @httpPut("/widgetRankings")
        // public async addWidgetRankings(request: express.Request): Promise<void> {
        //     const date = request.body.date
        //     const hcUrl = request.body.hcUrl
        //     await this.widgetRankingBusiness.addWidgetRankingDetail(date, hcUrl)
        // }

    }
}
export default controllerFactory
