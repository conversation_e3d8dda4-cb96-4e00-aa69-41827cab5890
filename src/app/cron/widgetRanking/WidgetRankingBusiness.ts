import { BASE_TYPES, <PERSON>tchUtil, ILogger } from "@curefit/base"
import * as AWS from "aws-sdk"
import { inject, injectable } from "inversify"
import parse = require("csv-parse/lib/sync")
import { TimeUtil } from "@curefit/util-common"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { WidgetRankingModel, WidgetRanking } from "./WidgetRankingModel"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { ICrudKeyValue, REDIS_TYPES, IMultiCrudKeyValue } from "@curefit/redis-utils"
const fetch = require("node-fetch")

@injectable()
export class WidgetRankingBusiness {

    private redisCrudDao: ICrudKeyValue
    private readonly BUCKET_NAME = "production-cf-recommendation"

    private s3: AWS.S3 = new AWS.S3({
        region: "ap-south-1"
    })

    private readonly S3_PREFIX = "homeWidgetRanking/"
    private readonly REDIS_KEY_PREFIX = "homeWidgetRanking:"


    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(REDIS_TYPES.MultiCrudKeyValueDao) private multiCrudKeyValueDao: IMultiCrudKeyValue,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,

        @inject(BASE_TYPES.FetchUtil) protected fetchHelper: FetchUtil
    ) {
        if (process.env.APP_ENV === "LOCAL") {
            const credentials = new AWS.SharedIniFileCredentials({ profile: "mfa" })
            AWS.config.credentials = credentials
        }
        this.s3 = new AWS.S3({
            region: "ap-south-1"
        })
        this.redisCrudDao = this.multiCrudKeyValueDao.getICrudKeyValue("CFAPI-CACHE")
    }

    private getAllS3KeysForPrefix(prefix: string, bucketName: string): Promise<string[]> {
        this.logger.info(`Getting S3 keys for prefix - ${prefix} and  bucket ${bucketName}`)
        return new Promise<string[]>((resolve, reject) => {
            const s3params = {
                Bucket: bucketName,
                Prefix: prefix
            }
            this.s3.listObjectsV2(s3params, (err, data) => {
                if (err) {
                    reject(err)
                    return
                }
                const keys: string[] = []
                for (const content of data.Contents) {
                    if (content.Key.endsWith("_SUCCESS")) {
                        continue
                    }
                    keys.push(content.Key)
                }
                resolve(keys)
            })
        })
    }

    private getFileContent(key: string, bucketName: string): Promise<string> {
        this.logger.info(`Getting file content for key - ${key}`)
        return new Promise<any>((resolve, reject) => {
            const s3params = {
                Key: key,
                Bucket: bucketName
            }
            this.s3.getObject(s3params, (err, data) => {
                if (err) {
                    reject(err)
                    return
                }
                resolve(data.Body.toString())
            })
        })
    }

    private getS3PrefixFor(date: string): string {
        return this.S3_PREFIX + date
    }

    private getBucketName(): string {
        return this.BUCKET_NAME
    }

    public async addWidgetRankingDetail(date: string, hcUrl: string): Promise<void> {
        try {
            this.logger.info(`Updating widget ranking detail for home page for date ${date}`)
            const s3Prefix = this.getS3PrefixFor(date)
            const bucketName = this.getBucketName()
            const keys: string[] = await this.getAllS3KeysForPrefix(s3Prefix, bucketName)
            for (const key of keys) {
                this.logger.info(`Inserting widget ranking for file - ${key}`)
                const content: string = await this.getFileContent(key, bucketName)
                const records: any[] = parse(content, { columns: null, skip_empty_lines: true, delimiter: "|", relax: true })
                await this.updateWidgetRankingsWithRecords(records, date)
            }
            this.logger.info(`Making a call to health checks to report success : ${hcUrl}`)
            await fetch(hcUrl, this.fetchHelper.get({}))
            this.logger.info(`Job succeeded successfully`)
        } catch (e) {
            const msg = `Couldn't update widget rankings at processedDate - ${date} with error ${JSON.stringify(e.message)}`
            this.logger.error(msg, e)
            this.rollbarService.handleError(msg)
        }
    }

    private async updateWidgetRankingsWithRecords(records: any[], date: string): Promise<void> {
        const userToWidgerRanking: Map<string, Map<string, string>> = new Map()
        for (const record of records) {
            const userId: string = record[0]
            const hourOfDay: string = record[1]
            const rankingDetail: string = record[2]

            const key: string =  this.REDIS_KEY_PREFIX + userId + ":" + date
            if (!userToWidgerRanking.get(key)) {
                userToWidgerRanking.set(key, new Map<string, string>())
            }
            userToWidgerRanking.get(key).set(hourOfDay, rankingDetail)
        }
        for (const [key, value] of userToWidgerRanking.entries()) {
            await this.redisCrudDao.updateHashMulti(key, value)
            await this.redisCrudDao.setExpiry(key, TimeUtil.TIME_IN_SECONDS.DAY * 2.5)
        }
    }
}
