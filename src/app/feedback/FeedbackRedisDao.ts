import { inject } from "inversify"
import { BaseRedisDaoImpl, IMultiCrudKeyValue, REDIS_TYPES } from "@curefit/redis-utils"
import { AppFeedbackKey } from "./Feedback"
import { UserFormRequest } from "@curefit/cfs-common"

export class FeedbackRedisDao extends BaseRedisDaoImpl<AppFeedbackKey, UserFormRequest> {
    constructor(@inject(REDIS_TYPES.MultiCrudKeyValueDao) multiCrudKeyValueDao: IMultiCrudKeyValue) {
        super(multiCrudKeyValueDao)
    }
}