import { Key } from "@curefit/redis-utils"
import { AnnouncementId, AnnouncementState } from "../announcement/Announcement"

const NAMESPACE = "cf-app:"

export class AppFeedback<PERSON>ey implements Key {
    userId: string
    key: string

    constructor(userId: string) {
        this.userId = userId
        this.key = AppFeedbackKey.get(userId)
    }

    lookupKey(): string {
        return this.key
    }

    static from(userId: string) {
        return new AppFeedbackKey(userId)
    }

    static get(userId: string) {
        return `${NAMESPACE}appFeedback:${userId}`
    }
}

export class AppFeedbackCountKey implements Key {
    key: string

    constructor(formId: string) {
        this.key = AppFeedbackCountKey.get(formId)
    }

    lookupKey(): string {
        return this.key
    }

    static from(formId: string) {
        return new AppFeedbackCountKey(formId)
    }

    static get(formId: string) {
        return `${NAMESPACE}appFeedbackCount:${formId}`
    }
}

export class AppFeedbackSuccess<PERSON>ount<PERSON>ey implements Key {
    key: string

    constructor(formId: string) {
        this.key = AppFeedbackSuccessCountKey.get(formId)
    }

    lookupKey(): string {
        return this.key
    }

    static from(formId: string) {
        return new AppFeedbackSuccessCountKey(formId)
    }

    static get(formId: string) {
        return `${NAMESPACE}AppFeedbackSuccessCount:${formId}`
    }
}


