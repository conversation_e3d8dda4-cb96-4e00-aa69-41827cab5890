import { inject, injectable } from "inversify"
import { Feedback } from "@curefit/feedback-common"
import { CultGearProductTypes } from "@curefit/feedback-common/dist/src/Feedback"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { GearFeedbackSNSProducer } from "../gear/GearFeedbackSNSProducer"
import { BASE_TYPES, ILogger } from "@curefit/base"

@injectable()
export class VerticalSpecificFeedbackConsumer {
    constructor(
        // inject verticle specific consumers
        @inject(CUREFIT_API_TYPES.GearFeedbackSNSProducer) private gearFeedbackSNSProducer: GearFeedbackSNSProducer,
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
    ) {
    }

    public async consumeFeedback(feedback: Feedback): Promise<void> {
        switch (feedback?.productType) {
            case "GEAR":
                await this.gearFeedbackSNSProducer.pushFeedbackToSNS(feedback)
                break
        }
    }

}