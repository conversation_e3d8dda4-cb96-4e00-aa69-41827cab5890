import CUREFIT_API_TYPES from "../../config/ioc/types"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import AuthMiddleware from "../auth/AuthMiddleware"
import { Container, inject } from "inversify"
import { BASE_TYPES, Logger } from "@curefit/base"
import * as express from "express"
import { Session, UserContext } from "@curefit/userinfo-common"
import { ErrorFactory } from "@curefit/error-client"
import { Action, ActionType, FitCashSummaryWidget, ProductListWidget } from "../common/views/WidgetView"
import * as _ from "lodash"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { ErrorCodes } from "../error/ErrorCodes"
import { ApiErrorCountMiddleware } from "../auth/ApiErrorCountMiddleware"
import { VoucherUtil } from "../coupon/VoucherUtil"
import { ICouponResponse } from "../coupon/CouponController"
import { IUserGiftCardWalletService, PAYMENT_TYPES } from "@curefit/payment-client"
import { CardRedeemResponse, GiftCardDetails, GiftCardRedemptionRequest, UserWalletBalanceResponse, UserWalletTransaction } from "@curefit/payment-common"
import ActionUtil from "../util/ActionUtil"
import { TransactionItem } from "../fitcash/FitcashController"
import { isEmpty } from "lodash"
import { FITCASH_CLIENT_TYPES } from "@curefit/fitcash-client/dist/src/ioc/FitcashClientTypes"
import { IFitcashService } from "@curefit/fitcash-client"
import { WalletTransaction } from "@curefit/fitcash-common"
import AppUtil from "../util/AppUtil"
import { BlockingType } from "../metrics/models/BlockingType"
import { ICaptchaBusiness } from "../referral/CaptchaBusiness"
import AuthUtil from "../util/AuthUtil"
import MetricsUtil from "../metrics/MetricsUtil"



export function GiftVoucherControllerFactory(kernel: Container) {

    @controller("/giftvoucher",
        kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession,
        kernel.get<ApiErrorCountMiddleware>(CUREFIT_API_TYPES.ApiErrorCountMiddleware).checkErrorCountMiddleware)
    class GiftVoucherController {
        constructor(
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
            @inject(PAYMENT_TYPES.IUserGiftCardWalletService) private giftCardWalletService: IUserGiftCardWalletService,
            @inject(FITCASH_CLIENT_TYPES.FitcashService) private fitcashService: IFitcashService,
            @inject(CUREFIT_API_TYPES.CaptchaBusiness) private captchaBusiness: ICaptchaBusiness,

            @inject(CUREFIT_API_TYPES.MetricsUtil) private metricsUtil: MetricsUtil,
        ) {
        }

        @httpGet("/page")
        async getFitnessPassPage(request: express.Request) {
            return VoucherUtil._redeemGiftCouponPage()
        }

        @httpGet("/eligibility")
        async getUserEligibilityForCode(request: express.Request): Promise<ICouponResponse> {
            const couponCode: string = request.query.couponCode
            const session: Session = request.session
            const userContext: UserContext = request.userContext as UserContext
            try {
                const response: GiftCardDetails = await this.giftCardWalletService.getGiftCardDetails(couponCode)
                return this._giftCouponResponse(response, couponCode, userContext.userProfile.timezone)
            } catch (error) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_COUPON_ERROR, 400).withMeta({
                    message: error.code,
                    errorMsg: error.code,
                    errorCode: error.statusCode,
                    modalHeight: 250,
                }).build()
            }
        }

        @httpPost("/eligibility") // cultsport and cult.fit websites are calling this...
        async getUserEligibilityFitnessCode(request: express.Request, res: express.Response): Promise<ICouponResponse> {
            const { couponCode, captchaResponse } = request.body
            const session: Session = request.session
            const userId: string = session.userId
            this.logger.info("Fetching coupon for User " + userId)
            const userContext: UserContext = request.userContext as UserContext
            const verifyCaptchaResp = await this.captchaBusiness.verifyCaptchaLogin(captchaResponse, AppUtil.callSource(AuthUtil.getApiKeyFromReq(request)))
            this.logger.info(` /eligibility  verifyCaptchaResp`, verifyCaptchaResp)
            if (verifyCaptchaResp.success && verifyCaptchaResp.score > 0.6) {
                try  {
                    const response: GiftCardDetails = await this.giftCardWalletService.getGiftCardDetails(couponCode)
                    return this._giftCouponResponse(response, couponCode, userContext.userProfile.timezone)
                } catch (error) {
                    throw this.errorFactory.withCode(ErrorCodes.GENERIC_COUPON_ERROR, 400).withMeta({
                        message: error.code,
                        errorMsg: error.code,
                        buttonText: "RE ENTER CODE",
                        errorCode: error.statusCode,
                        couponCode,
                        modalHeight: 250,
                    }).build()
                }
            } else {
                this.metricsUtil.reportBlockedRequest(BlockingType.CAPTCHA_FAILURE)
                this.logger.error(`AuthController:Failed to verify captcha Error: Failed to verify. Please try again`)
                res.status(400).send({statusCode: 400, error: "Bad Request", message: "captcha not valid"})
            }
        }

        @httpPost("/activate")
        async activateCouponForUser(request: express.Request) {
            const { couponCode } = request.body
            const session: Session = request.session
            const userId: string = session.userId
            try {
                const giftCardRedemptionRequest: GiftCardRedemptionRequest = {userId, giftCardId: couponCode}
                const cardRedeemResponse: CardRedeemResponse = await this.giftCardWalletService.redeemGiftCard(giftCardRedemptionRequest)
                const action: Action = {
                    actionType: "NAVIGATION",
                    url: ActionUtil.getGiftCardTransactionsPageUrl(),
                    title: "VIEW BALANCE"
                }
                return {
                    "title": "Congrats!",
                    "subTitle": `₹${cardRedeemResponse.redeemedAmount} successfully added to Gift Card balance`,
                    "icon": "/image/gymfit/success_tickv2.png",
                    "action": action
                }
            } catch (error) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_COUPON_ERROR, 400).withMeta({
                    message: error.code,
                    errorMsg: error.code,
                    errorCode: error.statusCode,
                    modalHeight: 250,
                }).build()
            }
        }

        @httpGet("/transactions")
        async getWalletTransactions(request: express.Request) {
            const session: Session = request.session
            const userId: string = session.userId
            const userContext: UserContext = request.userContext as UserContext
            let totalBalance: number = 0
            let giftCardBalance: number = 0
            let fitcashBalance: number = 0
            let subtitle: string = null
            let showTransactions: boolean = false
            let transactionsWidget: ProductListWidget = null
            let finalTransactionsItems: TransactionItem[] = []
            let giftCardTransactionsItems: TransactionItem[] = []
            let fitCashTransactionsItems: TransactionItem[] = []
            const isUnifiedGiftCardSupported = await AppUtil.isUnifiedGiftCardPageSupported(userContext) || AppUtil.isCultSportWebApp(userContext)
            let fitcashTransactions: WalletTransaction[]
            try {
                const userWalletResponse: UserWalletBalanceResponse = await this.giftCardWalletService.getWalletBalance({userId})
                giftCardBalance = userWalletResponse ? userWalletResponse.balance : 0
                const walletMap = GiftVoucherController.getWalletMap(userWalletResponse, userContext)
                if (walletMap.size > 0) {
                    subtitle =  `₹${Array.from(walletMap)[0][1]} expires on ${Array.from(walletMap)[0][0]}`
                }
                const userWalletTransactions: UserWalletTransaction[] = await this.giftCardWalletService.getTransactionHistory({userId, noOfTransaction: 50, startIndex: 1})
                const filteredWalletTransactions: UserWalletTransaction[] = userWalletTransactions.filter((w) => !isEmpty(w.notes) && w.notes.toUpperCase() !== "CF WALLET CREATION")
                giftCardTransactionsItems = GiftVoucherController.transformGiftTransactionsToItems(filteredWalletTransactions, userContext)
            } catch (error) {}
            if (isUnifiedGiftCardSupported) {
                try {
                    const fitcashBalanceResponse = await this.fitcashService.balance(userId, userContext.userProfile.city.country.currencyCode)
                    fitcashBalance = fitcashBalanceResponse && fitcashBalanceResponse.balance ? (fitcashBalanceResponse.balance / 100) : 0
                } catch (error) {}
                fitcashTransactions = await this.fitcashService.getTransactions(userId)
                if (fitcashTransactions !== null && fitcashTransactions.length > 0) {
                    fitCashTransactionsItems = GiftVoucherController.transformFitcashTransactionsToItems(fitcashTransactions, userContext)
                }
            }
            totalBalance = giftCardBalance + fitcashBalance
            if (giftCardTransactionsItems !== null && giftCardTransactionsItems.length > 0) {
                finalTransactionsItems = [...finalTransactionsItems, ...giftCardTransactionsItems]
            }
            if (fitCashTransactionsItems !== null && fitCashTransactionsItems.length > 0) {
                finalTransactionsItems = [...finalTransactionsItems, ...fitCashTransactionsItems]
            }
            if (finalTransactionsItems.length > 0) {
                finalTransactionsItems.sort((objA, objB) => objB.sortingDate - objA.sortingDate)
                transactionsWidget = {
                    widgetType: "PRODUCT_LIST_WIDGET",
                    type: "CREDIT",
                    hideSepratorLines: false,
                    header: {
                        title: "TRANSACTION HISTORY",
                        color: "#000000"
                    },
                    items: finalTransactionsItems
                }
                showTransactions = true
            }
            const fitCashSummaryWidget: FitCashSummaryWidget = {
                widgetType: "FITCASH_SUMMARY_WIDGET",
                title: "TOTAL BALANCE",
                balance: totalBalance,
                subtitle: giftCardBalance > 0 ? subtitle : fitcashBalance > 0 ? " " : null,
                ctaString : giftCardBalance > 0 ? "DETAILS" : fitcashBalance > 0 ? "VIEW DETAILS" : null
            }
            return {
                title: "",
                faqLink: isUnifiedGiftCardSupported ? "https://s3.ap-south-1.amazonaws.com/vm-html-pages/giftcardAndFitcashFaq.html" :  "https://s3.ap-south-1.amazonaws.com/vm-html-pages/giftcard.html",
                widgets: [
                    fitCashSummaryWidget,
                    transactionsWidget
                ],
                actions: [
                    transactionsWidget ? {
                      "title": "EXPLORE OFFERINGS",
                      "url": "curefit://homeTab",
                      "actionType": "NAVIGATION"
                    } : {
                      "title": "REDEEM GIFT CARD",
                      "url": "curefit://redeemgiftcardpage",
                      "actionType": "NAVIGATION"
                    }
                  ]
            }
        }

        @httpGet("/details")
        async getWalletDetails(request: express.Request) {
            const session: Session = request.session
            const userId: string = session.userId
            const userContext: UserContext = request.userContext as UserContext
            let giftCardBalance: number = 0
            let fitcashBalance: number = 0
            let totalBalance: number = 0
            let userWalletMap = null
            let userWalletResponse: UserWalletBalanceResponse = null
            const isUnifiedGiftCardSupported = await AppUtil.isUnifiedGiftCardPageSupported(userContext)
            try {
                userWalletResponse = await this.giftCardWalletService.getWalletBalance({userId})
                giftCardBalance = userWalletResponse ? userWalletResponse.balance : 0
            } catch (error) {}
            if (isUnifiedGiftCardSupported) {
                const fitcashBalanceResponse = await this.fitcashService.balance(userId, userContext.userProfile.city.country.currencyCode)
                fitcashBalance = fitcashBalanceResponse && fitcashBalanceResponse.balance ? (fitcashBalanceResponse.balance / 100) : 0
            }
            totalBalance = giftCardBalance + fitcashBalance
            if (giftCardBalance > 0) {
                userWalletMap = GiftVoucherController.getWalletMap(userWalletResponse, userContext)
            }
            const walletItemList: { title: string; subTitle: string }[] = []
            if (fitcashBalance > 0) {
                walletItemList.push({
                    "title": `₹${fitcashBalance}`,
                    "subTitle": `Fitcash`
                })
            }
            if (giftCardBalance > 0) {
                userWalletMap.forEach((value, key) => {
                    walletItemList.push({
                        "title": `₹${value}`,
                        "subTitle": `Gift Card • Expires ${key}`
                    })
                })
            }
            return {
                "totalBalance": `₹${totalBalance}`,
                "subTitle": "TOTAL BALANCE",
                "ctaTitle": "DONE",
                "walletItems": walletItemList
            }
        }

        _giftCouponResponse(giftCard: GiftCardDetails, couponCode: string, timezone: Timezone): ICouponResponse {
            const endDate = TimeUtil.formatDateStringInTimeZoneDateFns(giftCard.expiry, timezone, "MMM dd, yyyy")
            const actionType: ActionType = "ACTIVATE_COUPON"
            const action: Action = {
                actionType: actionType,
                meta: {
                    couponCode: couponCode,
                    shouldShowTnc: false,
                },
                title: "REDEEM NOW"
            }
            const footerNote: string = "Amount will be credited to your Gift Card Balance after you redeem the Gift Card."
            return {
                type: "GIFT_CARD",
                header: {
                    title: "Gift Card",
                    heroImage: "/image/icons/coupons/cult_coupon_banner.jpg",
                    tnc: {
                        text: "View T&C",
                        link: "https://s3.ap-south-1.amazonaws.com/vm-html-pages/CRED_MMR_JAN_22-1600213c-18fd-4de6-b59e-fa42368644ab.html"
                    },
                    icon: null
                },
                couponDetails: {
                    title: couponCode,
                    subTitle: `Valid till ${endDate}`,
                    info: {
                        title: "Gift Card",
                        items: [
                            {
                                icon: "/image/icons/howItWorks/packDetail_1.png",
                                subTitle: `Gift card worth ₹${giftCard.amount}`
                            },
                            {
                                icon: "/image/icons/howItWorks/packDetail_1.png",
                                subTitle: "Applicable on all products & memberships offered by cult"
                            }
                        ]
                    },
                    tnc: {
                        actionType: "NAVIGATION",
                        title: "View More",
                        url: "curefit://webview?uri=https://s3.ap-south-1.amazonaws.com/vm-html-pages/giftcard.html"
                    }
                },
                action: action,
                footerNote: footerNote,
                shouldShowTnc: false,
            }
        }

        private static transformGiftTransactionsToItems(transactions: UserWalletTransaction[], userContext: UserContext): TransactionItem[] {
            return _.map(transactions, (trxn: UserWalletTransaction) => {
                let subtitle = ""
                    if (trxn.transactionType === "ADD CARD TO WALLET" &&  trxn.notes.toUpperCase().indexOf("credit") == -1) {
                    subtitle +=  "Code# "
                } else {
                    subtitle += "Order# "
                }
                return {
                    iconUrl: trxn.transactionType === "WALLET REDEEM" ? "/image/icons/fitcash/withdraw.png" : "/image/icons/fitcash/deposit.png",
                    title: trxn.transactionType !== "WALLET REDEEM" ? "Gift card added" : "Gift card debited",
                    subTitle: isEmpty(trxn.invoiceNumber) ? null : subtitle + trxn.invoiceNumber,
                    amount: trxn.transactionAmount,
                    credited: trxn.transactionType !== "WALLET REDEEM",
                    date: TimeUtil.formatDateStringInTimeZoneDateFns(trxn.transactionDate, userContext.userProfile.timezone, "dd MMM"),
                    sortingDate: new Date(trxn.transactionDate).getTime()
                }
            })
        }

        private static transformFitcashTransactionsToItems(transactions: WalletTransaction[], userContext: UserContext): TransactionItem[] {
            return _.map(transactions, (trxn: WalletTransaction) => {
                return {
                    iconUrl: trxn.transactionType === "WITHDRAW" ? "/image/icons/fitcash/withdraw.png" : "/image/icons/fitcash/deposit.png",
                    title: trxn.transactionType !== "WITHDRAW" ? "Fitcash added" : "Fitcash debited",
                    subTitle: trxn.title,
                    amount: trxn.amount ? (trxn.amount / 100) : 0,
                    credited: trxn.transactionType !== "WITHDRAW",
                    date: TimeUtil.formatDateInTimeZoneDateFns(userContext.userProfile.timezone, trxn.createdAt, "dd MMM"),
                    sortingDate: new Date(trxn.createdAt).getTime()
                }
            })
        }

        private static getWalletMap(userWalletResponse: UserWalletBalanceResponse, userContext: UserContext) {
            let walletMap = new Map()
                userWalletResponse.cards.map((card) => {
                    const expiryDate = TimeUtil.formatDateStringInTimeZoneDateFns(card.expiry, userContext.userProfile.timezone, "MMM dd, yyyy")
                    if (walletMap.has(expiryDate)) {
                        walletMap.set(expiryDate, walletMap.get(expiryDate) + card.amount)
                    } else if (card.amount !== 0) {
                        walletMap.set(expiryDate, card.amount)
                    }
                })
            const sortedEntries = Array.from(walletMap.entries()).sort(([keyA], [keyB]) => {
                return new Date(keyA).getTime() - new Date(keyB).getTime()
            })

            walletMap = new Map(sortedEntries)
            return walletMap
        }

    }

    return GiftVoucherController
}

export default GiftVoucherControllerFactory
