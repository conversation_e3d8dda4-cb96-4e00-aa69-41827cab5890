import { ALBUS_CLIENT_TYPES, Doctor, IHealthfaceService } from "@curefit/albus-client"
import { RouteNames, VmPageRoutes, VmPageSectionRoutes } from "@curefit/apps-common"
import { BASE_TYPES, Logger } from "@curefit/base"
import { Status } from "@curefit/base-common"
import { ActionUtil, ActionUtilV1, CareUtil, SeoUrlParams } from "@curefit/base-utils"
import { cacheKey, CacheService, CACHE_CLIENT_TYPES, wrapWithMethodCache } from "@curefit/cache-client"
import { CAESAR_CLIENT_TYPES, IMenuService } from "@curefit/caesar-client"
import { ConsultationProduct } from "@curefit/care-common"
import { CATALOG_CLIENT_TYPES, ICatalogueService, ICatalogueServicePMS } from "@curefit/catalog-client"
import { CULT_CLIENT_TYPES, ICultServiceOld as ICultService } from "@curefit/cult-client"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService } from "@curefit/diy-client"
import { OnDemandVideoCategory } from "@curefit/diy-common"
import { FUSE_CLIENT_TYPES, IDiagnosticService, ProductSearchApiParams } from "@curefit/fuse-node-client"
import { GEARVAULT_CLIENT_TYPES, IGearService } from "@curefit/gearvault-client"
import { GYMFIT_CLIENT_TYPES, IGymfitService } from "@curefit/gymfit-client"
import {
    GymfitCenter,
    GymfitListingType,
    MediaType
} from "@curefit/gymfit-common"
import { City } from "@curefit/location-common"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { CollectionWidgetConfig, IMagnetoService, MAGNETO_CLIENT_TYPES } from "@curefit/magneto-client"
import { CLOUDINARY_PREFIX, ProductType, UrlPathBuilder } from "@curefit/product-common"
import { Tenant } from "@curefit/user-common"
import { TimeUtil, titleCase } from "@curefit/util-common"
import { CSBlogArticle, CSBlogCategory, CSBlogTag, ICSBlogArticleReadOnlyDao, ICSBlogCategoryReadOnlyDao, ICSBlogTagReadOnlyDao, IListingPageCreatorReadOnlyDao, IPageReadOnlyDao, ISEOReadOnlyDao, Page, VM_MODELS_TYPES } from "@curefit/vm-models"
import * as express from "express"
import { Container, inject } from "inversify"
import { controller, httpGet } from "inversify-express-utils"
import * as _ from "lodash"
import { CULTSPORT_BASE_URL } from "../util/AppUtil"
import { CatalogueServiceUtilities } from "../util/CatalogueServiceUtilities"
import CultsportUtil from "../util/CultsportUtil"
import GymfitUtil from "../util/GymfitUtil"
import { IFindQuery } from "@curefit/mongo-utils"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import fetch from "node-fetch"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import DIYPackService from "../digital/diy/DIYPackService"
import { Namespace, OfflineFitnessPack, ProductSubType, Visibility } from "@curefit/pack-management-service-common"
import { IOfflineFitnessPackService, PACK_CLIENT_TYPES } from "@curefit/pack-management-service-client"

const { SitemapStream, streamToPromise } = require("sitemap")
const { Readable } = require("stream")

export interface SiteMapUrl {
    url: string
    changefreq: CHANGE_FREQUENCY
    priority: number
    img?: SiteMapImage[]
    lastmodrealtime: boolean,
    cdata?: boolean
}
export type CHANGE_FREQUENCY = "weekly" | "monthly" | "daily"
export const CITY_BANGALORE = "Bangalore"

const stageBaseUrl = "https://stage.cult.fit"
const prodBaseUrl  = "https://www.cult.fit"

const IMAGE_LICENSE_URL = "https://creativecommons.org/licenses/by/4.0/"

export interface SiteMapImage {
    url: string
    caption?: string
    title: string
    geoLocation?: string
    license: string
}
export function SitemapControllerFactory(kernel: Container) {
    @controller("/sitemap")
    class SitemapController {

        constructor(
            @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
            @inject(CATALOG_CLIENT_TYPES.CatalogServiceReaderPMS) private catalogueServicePMS: ICatalogueServicePMS,
            @inject(PACK_CLIENT_TYPES.OfflineFitnessPackService) private offlineFitnessPackService: IOfflineFitnessPackService,
            @inject(CAESAR_CLIENT_TYPES.MenuService) private menuService: IMenuService,
            @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
            @inject(CULT_CLIENT_TYPES.MindFitService) private mindFitService: ICultService,
            @inject(LOCATION_TYPES.CityService) private cityService: ICityService,
            @inject(CUREFIT_API_TYPES.DIYPackService) private packService: DIYPackService,
            @inject(VM_MODELS_TYPES.ListingPageCreatorReadOnlyDao) private listingPageCreatorReadDao: IListingPageCreatorReadOnlyDao,
            @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) private diyFulfilmentService: IDIYFulfilmentService,
            @inject(FUSE_CLIENT_TYPES.IDiagnosticSellerService) private diagnosticService: IDiagnosticService,
            @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
            @inject(GYMFIT_CLIENT_TYPES.GymfitService) private gymfitService: IGymfitService,
            @inject(GEARVAULT_CLIENT_TYPES.GearService) private gearService: IGearService,
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(VM_MODELS_TYPES.PageReadOnlyDao) private pageDao: IPageReadOnlyDao,
            @inject(MAGNETO_CLIENT_TYPES.Service) private magnetoService: IMagnetoService,
            @inject(VM_MODELS_TYPES.CSBlogArticleReadOnlyDao) private csBlogArticleReadOnlyDao: ICSBlogArticleReadOnlyDao,
            @inject(VM_MODELS_TYPES.CSBlogCategoryReadOnlyDao) private csBlogCategoryReadOnlyDao: ICSBlogCategoryReadOnlyDao,
            @inject(VM_MODELS_TYPES.CSBlogTagReadOnlyDao) private csBlogTagReadOnlyDao: ICSBlogTagReadOnlyDao,
            @inject(ERROR_COMMON_TYPES.RollbarService) public rollbarService: RollbarService,
            @inject(VM_MODELS_TYPES.SEOReadOnlyDao) private seoDao: ISEOReadOnlyDao,
        ) {

        }

        @httpGet("/cultsport")
        async getCultSportSitemap(req: express.Request): Promise<string> {
            const isFromUI = req?.query?.fromUI
            const clpUrlsPromise = this.cultSportStaticUrls()
            const nonIndexablePageSeoPageIds = await this.getCultsportNonIndexablePageIds()
            const collectionListPageUrlPromise = isFromUI ? undefined : this.getCultsportCollectionSEOUrl("daily", nonIndexablePageSeoPageIds)
            const pdpPageUrlPromise = isFromUI ? undefined : this.getCultsportPDPSEOUrl("daily", nonIndexablePageSeoPageIds)
            const lpRoutePageUrlPromise = this.getCultsportLPRouteSEOUrl("daily", nonIndexablePageSeoPageIds)
            const blogArticlesUrlPromise = this.getCultsportBlogArticlesUrl("daily")
            const blogCategoriesUrlPromise = this.getCultsportBlogCategoriesUrl("daily")
            const blogTagsUrlPromise = this.getCultsportBlogTagsUrl("daily")
            const categoryWisePopularPDPPromise = isFromUI ? this.getMixpanelCategoryWisePopularPDPIDSPages("daily") : undefined
            const sitemapUrls: SiteMapUrl[] = []
            let siteMapForUI: any = {}

            let siteMapSectionList: SiteMapUrl[][] = []

            try {
                siteMapSectionList = await Promise.all([
                    clpUrlsPromise
                ])

                siteMapForUI["home"] = {
                    title: "Home page",
                    urlList: siteMapSectionList?.[0]?.map((item: any) => {
                        return {
                            url: item.url,
                            title: item?.img?.[0]?.title || "Home page"
                        }
                    })
                }
                siteMapSectionList.forEach((siteMapUrlList) => (
                    sitemapUrls.push(...siteMapUrlList)
                ))
            } catch (err) {
                this.rollbarService.sendError(err)
                this.logger.error(err)
            }

            try {
                if (collectionListPageUrlPromise) {
                    siteMapSectionList = await Promise.all([
                        collectionListPageUrlPromise
                    ])
                    siteMapSectionList.forEach((siteMapUrlList) => (
                        sitemapUrls.push(...siteMapUrlList)
                    ))
                }
                siteMapForUI = {...siteMapForUI, ...CultsportUtil.getCollectionSiteMap()}
            } catch (err) {
                this.rollbarService.sendError(err)
                this.logger.error(err)
            }

            try {
                if (pdpPageUrlPromise) {
                    siteMapSectionList = await Promise.all([
                        pdpPageUrlPromise
                    ])
                    siteMapSectionList.forEach((siteMapUrlList) => (
                        sitemapUrls.push(...siteMapUrlList)
                    ))
                }
                if (categoryWisePopularPDPPromise) {
                    const categoryWisePopularPDPMap =  await categoryWisePopularPDPPromise
                    siteMapForUI = {...siteMapForUI, ...categoryWisePopularPDPMap}
                }
            } catch (err) {
                this.rollbarService.sendError(err)
                this.logger.error(err)
            }

            try {
                siteMapSectionList = await Promise.all([
                    lpRoutePageUrlPromise
                ])
                siteMapForUI["landingPage"] = {
                    title: "Landing Pages",
                    urlList: siteMapSectionList?.[0]?.map((item: any) => {
                        return {
                            url: item.url,
                            title: item?.img?.[0]?.title || item.url.replace("/lp/", "")
                        }
                    })
                }
                siteMapSectionList.forEach((siteMapUrlList) => (
                    sitemapUrls.push(...siteMapUrlList)
                ))
            } catch (err) {
                this.rollbarService.sendError(err)
                this.logger.error(err)
            }

            try {
                siteMapSectionList = await Promise.all([
                    blogArticlesUrlPromise
                ])
                siteMapForUI["posts"] = {
                    title: "Blogs",
                    urlList: siteMapSectionList?.[0]?.map((item: any) => {
                        return {
                            url: item.url,
                            title: item?.img?.[0]?.title || "Article"
                        }
                    })
                }

                siteMapSectionList.forEach((siteMapUrlList) => (
                    sitemapUrls.push(...siteMapUrlList)
                ))
            } catch (err) {
                this.rollbarService.sendError(err)
                this.logger.error(err)
            }

            try {
                siteMapSectionList = await Promise.all([
                    blogCategoriesUrlPromise
                ])
                siteMapForUI["categories"] = {
                    title: "Categories",
                    urlList: siteMapSectionList?.[0]?.map((item: any) => {
                        return {
                            url: item.url,
                            title: titleCase(item?.img?.[0]?.title || item.url.replace("/category/", ""))
                        }
                    })
                }

                siteMapSectionList.forEach((siteMapUrlList) => (
                    sitemapUrls.push(...siteMapUrlList)
                ))
            } catch (err) {
                this.rollbarService.sendError(err)
                this.logger.error(err)
            }

            try {
                siteMapSectionList = await Promise.all([
                    blogTagsUrlPromise
                ])
                siteMapForUI["tag"] = {
                    title: "Tags",
                    urlList: siteMapSectionList?.[0]?.map((item: any) => {
                        return {
                            url: item.url,
                            title: titleCase(item?.img?.[0]?.title || item.url.replace("/tag/", ""))
                        }
                    })
                }

                siteMapSectionList.forEach((siteMapUrlList) => (
                    sitemapUrls.push(...siteMapUrlList)
                ))
            } catch (err) {
                this.rollbarService.sendError(err)
                this.logger.error(err)
            }

            if (isFromUI) {
                return {...siteMapForUI, ...CultsportUtil.getCompanySiteMap()}
            }
            const stream = new SitemapStream( { hostname:  CULTSPORT_BASE_URL[process.env.APP_ENV] || CULTSPORT_BASE_URL["PRODUCTION"] } )
            return streamToPromise(Readable.from(sitemapUrls).pipe(stream)).then((data: any) =>
                data.toString()
            )
        }

        @httpGet("/")
        async getSitemap(req: express.Request): Promise<string> {
            const cities = (await this.cityService.listCities(Tenant.CUREFIT_APP)).filter(city => {
                return city.cityId !== "Other"
            })
            const clpUrlsPromise = this.staticUrls()
            const cultPackUrlsPromise = this.getCultOrMindPacksUrl("FITNESS", cities)
            const cultDIYPackUrlsPromise = this.getCultOrMindDIYPacksUrl("DIY_FITNESS")
            const mindDIYPackUrlsPromise = this.getCultOrMindDIYPacksUrl("DIY_MEDITATION")
            const cultCenterUrlsPromise = this.getCultOrMindCenterUrl("FITNESS", cities)
            const mindCenterUrlsPromise = this.getCultOrMindCenterUrl("MIND", cities)
            const diyRecipesPromise = this.getDIYRecipesUrl("monthly")
            const diySeriesUrlPromise = this.getDIYSeriesUrl("weekly")
            const recipeShowsListPage = this.getRecipeShowsListPage("EAT")
            const diagnosticTestUrlPromise = this.getDiagnosticTestUrl("LIVE")
            const gymPackUrlsPromise = this.getGymPacksUrl("weekly", cities)
            const gymCenterUrlPromise = this.getGymCenterUrl()
            const getListingPageUrlPromise = this.getSEOListingPageUrls("monthly")
            const sitemapUrls: SiteMapUrl[] = []

            /**
             * NOTE: Removing /workout/:workout-slug/:workoutId Urls. Website revamp will accommodate new URL structure,
             * and since Cyclops currently doesn't have linking to workout page, this is resulting in drop in ranking.
             */
                // const cultWorkoutUrlsPromise = this.getCultOrMindWorkoutsUrl("FITNESS")
                // const mindWorkoutUrlsPromise = this.getCultOrMindWorkoutsUrl("MIND")

            /**
             * NOTE: Removing /mind/cultmindpack/* Urls. Website revamp will accommodate new URL structure,
             * and since Cyclops currently doesn't have linking to workout page, this is resulting in drop in ranking.
             */
                // const mindPackUrlsPromise = this.getCultOrMindPacksUrl("MIND", cities)

            let siteMapSectionList: SiteMapUrl[][] = []

            try {
                siteMapSectionList = await Promise.all([
                    clpUrlsPromise,
                    cultPackUrlsPromise
                ])

                siteMapSectionList.forEach((siteMapUrlList) => (
                    sitemapUrls.push(...siteMapUrlList)
                ))
            } catch (err) {
                this.logger.error(err)
            }

            try {
                siteMapSectionList = await Promise.all([
                    cultDIYPackUrlsPromise,
                    mindDIYPackUrlsPromise,
                    cultCenterUrlsPromise,
                    mindCenterUrlsPromise
                ])

                siteMapSectionList.forEach((siteMapUrlList) => (
                    sitemapUrls.push(...siteMapUrlList)
                ))
            } catch (err) {
                this.logger.error(err)
            }

            try {
                siteMapSectionList = await Promise.all([
                    diyRecipesPromise,
                    diySeriesUrlPromise,
                    gymPackUrlsPromise,
                    gymCenterUrlPromise
                ])

                siteMapSectionList.forEach((siteMapUrlList) => (
                    sitemapUrls.push(...siteMapUrlList)
                ))
            } catch (err) {
                this.logger.error(err)
            }

            try {
                siteMapSectionList = await Promise.all([
                    recipeShowsListPage,
                    getListingPageUrlPromise
                ])

                siteMapSectionList.forEach((siteMapUrlList) => (
                    sitemapUrls.push(...siteMapUrlList)
                ))
            } catch (err) {
                this.logger.error(err)
            }

            try {
                siteMapSectionList = [await diagnosticTestUrlPromise]

                siteMapSectionList.forEach((siteMapUrlList) => (
                    sitemapUrls.push(...siteMapUrlList)
                ))
            } catch (err) {
                this.logger.error(err)
            }

            const stream = new SitemapStream( { hostname: process.env.ENVIRONMENT === "STAGE" ? stageBaseUrl : prodBaseUrl } )
            return streamToPromise(Readable.from(sitemapUrls).pipe(stream)).then((data: any) =>
                data.toString()
            )
        }

        private async staticUrls(): Promise<SiteMapUrl[]> {
            const commonStaticMeta: { changefreq: CHANGE_FREQUENCY, priority: number, lastmodrealtime: boolean } = {
                    changefreq: "weekly",
                    priority: 0.8,
                    lastmodrealtime: true
                },
                sitemapUrls: SiteMapUrl[] = []

            const staticVMPages: string[] = [
                "/",
                    VmPageRoutes.Livefit,
                    VmPageRoutes.Cult,
                    VmPageRoutes.Care,
                    VmPageRoutes.Mind,
                    VmPageRoutes.Fitness
                ],
                staticVMPageSections: string[] = [
                    VmPageSectionRoutes.CultGym,
                    VmPageSectionRoutes.CultDIY,
                    VmPageSectionRoutes.CultLivePT,
                    VmPageSectionRoutes.LiveSGT,
                    VmPageSectionRoutes.LiveFitness,
                    VmPageSectionRoutes.LiveMindfulness,
                    VmPageSectionRoutes.LiveRecipie,
                    VmPageSectionRoutes.CareDiagnosticTests,
                    VmPageSectionRoutes.CultPassElite,
                    VmPageSectionRoutes.CultPassPro,
                    VmPageSectionRoutes.CultPassLive,
                    VmPageSectionRoutes.CultTransform
                ]

            staticVMPages.forEach((url) => (sitemapUrls.push({
                url,
                ...commonStaticMeta
            })))

            staticVMPageSections.forEach((url) => (sitemapUrls.push({
                url,
                ...commonStaticMeta
            })))

            return sitemapUrls
        }

        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), 7 * TimeUtil.TIME_IN_SECONDS.DAY)
        private async getDIYSeriesUrl(@cacheKey changeFrequency: CHANGE_FREQUENCY): Promise<SiteMapUrl[]> {
            const sitemapUrls: SiteMapUrl[] = [],
              commonSiteMapData = {
                  changefreq: changeFrequency,
                  lastmodrealtime: true,
                  priority: 1.0,
                  cdata: true,
              }
            let diySeriesCategoryList = [],
                url = ""

            diySeriesCategoryList = await this.diyFulfilmentService.getDIYSeries("", undefined, "IN")

            diySeriesCategoryList.forEach((categoryData) => {
                if (!categoryData || !categoryData.category) {
                    return
                }

                url = ActionUtilV1.diySeriesPage(categoryData, "DESKTOP")

                sitemapUrls.push({ ...commonSiteMapData, url })
            })

            return sitemapUrls
        }


        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), 7 * TimeUtil.TIME_IN_SECONDS.DAY)
        private async getCultOrMindPacksUrl(@cacheKey productType: ProductType, cities: City[]): Promise<SiteMapUrl[]> {
            const sitemapUrls: SiteMapUrl[] = []
            for (let i = 0; i < cities.length; i++) {
                const city = cities[i]
                const fitnessPacks: OfflineFitnessPack[] = await this.offlineFitnessPackService.searchCachedPacks({
                    namespace: Namespace.OFFLINE_FITNESS,
                    productTypes: ["FITNESS"],
                    restrictions: {cities: [city.cityId]},
                    productSubType: ProductSubType.GENERAL,
                    status: "ACTIVE",
                    saleEnabled: true,
                    isPrivate: false
                })
                fitnessPacks.forEach(fitnessPack => {
                    const seoUrlParam: SeoUrlParams = {
                        city: city.name,
                        productName: fitnessPack.title
                    }
                    const imageSuffix = CatalogueServiceUtilities.getFitnessProductImage(fitnessPack, "MAGAZINE", "DESKTOP")
                    const image: SiteMapImage = {
                        url: CLOUDINARY_PREFIX + imageSuffix,
                        title: fitnessPack.title,
                        caption: CatalogueServiceUtilities.getCultSubtitle(fitnessPack),
                        geoLocation: city.name,
                        license: IMAGE_LICENSE_URL
                    }
                    const sitemapUrl: SiteMapUrl = {
                        changefreq: "weekly",
                        lastmodrealtime: true,
                        priority: 1.0,
                        img: [image],
                        cdata: true,
                        url: productType === "FITNESS" ? CatalogueServiceUtilities.getPackPageAction(fitnessPack, "DESKTOP", seoUrlParam, true) : null
                    }

                    if (fitnessPack.status == "ACTIVE") sitemapUrls.push(sitemapUrl)
                })
            }
            return sitemapUrls
        }

        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), 7 * TimeUtil.TIME_IN_SECONDS.DAY)
        private async getCultOrMindDIYPacksUrl(@cacheKey productType: ProductType): Promise<SiteMapUrl[]> {
            const diyPacks = productType === "DIY_FITNESS" ? await this.packService.browseFitnessDIYPacksV2("", "VISIBLE") :
                await this.packService.browseMindDIYPacksV2("", "VISIBLE")
            const sitemapUrls: SiteMapUrl[] = []
            diyPacks.forEach(pack => {
                const imageSuffix = UrlPathBuilder.getPackImagePathV1("DESKTOP", pack.productId, productType, "MAGAZINE")
                const image: SiteMapImage = {
                    url: CLOUDINARY_PREFIX + imageSuffix,
                    title: pack.title,
                    caption: pack.title,
                    license: IMAGE_LICENSE_URL
                }
                const sitemapUrl: SiteMapUrl = {
                    changefreq: "weekly",
                    lastmodrealtime: true,
                    priority: 1.0,
                    img: [image],
                    cdata: true,
                    url: ActionUtilV1.diyPackProductPage(pack, "DESKTOP")
                }

                if (!pack.category || !pack.subCategory) {
                    return
                }

                if (!pack?.locations?.includes("WORLDWIDE") && !pack?.locations?.includes("IN")) {
                    return
                }

                if (pack.status === "LIVE") sitemapUrls.push(sitemapUrl)
            })
            return sitemapUrls
        }

        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), 7 * TimeUtil.TIME_IN_SECONDS.DAY)
        private async getCultOrMindWorkoutsUrl(@cacheKey productType: ProductType): Promise<SiteMapUrl[]> {
            const workouts = productType === "FITNESS" ? await this.cultFitService.browseWorkoutsV2() :
                await this.mindFitService.browseWorkoutsV2()
            const sitemapUrls: SiteMapUrl[] = []
            workouts.forEach(workout => {
                const seoUrlParam: SeoUrlParams = {
                    productName: workout.name
                }
                const sitemapUrl: SiteMapUrl = {
                    changefreq: "weekly",
                    lastmodrealtime: true,
                    priority: 1.0,
                    cdata: true,
                    url: ActionUtilV1.cultWorkoutV2(seoUrlParam, workout.id, productType, undefined, undefined, undefined, true)
                }

                const document = _.find(workout.documents, { tagName: "PRODUCT_BNR" })
                const imageSuffix = document ? document.URL : undefined
                if (imageSuffix) {
                    const image: SiteMapImage = {
                        url: CLOUDINARY_PREFIX + imageSuffix,
                        title: workout.name,
                        caption: workout.info,
                        license: IMAGE_LICENSE_URL
                    }
                    sitemapUrl.img = [image]
                }
                sitemapUrls.push(sitemapUrl)
            })
            return sitemapUrls
        }

        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), 7 * TimeUtil.TIME_IN_SECONDS.DAY)
        private async getCultOrMindCenterUrl(@cacheKey productType: ProductType, cities: City[]): Promise<SiteMapUrl[]> {
            const sitemapUrls: SiteMapUrl[] = []
            const tz = "Asia/Kolkata"
            for (let i = 0; i < cities.length; i++) {
                const city = cities[i]
                const centers = productType === "FITNESS" ?
                    await this.cultFitService.browseFitnessCenter(tz, "CUREFIT-API", undefined, city.cultCityId) :
                        await this.mindFitService.browseFitnessCenter(tz, "CUREFIT-API", undefined, city.cultCityId)
                centers.forEach(center => {
                    const seoUrlParam: SeoUrlParams = {
                        city: city.name,
                        locality: center.locality,
                        productName: center.name
                    }
                    const sitemapUrl: SiteMapUrl = {
                        changefreq: "weekly",
                        lastmodrealtime: true,
                        priority: 1.0,
                        cdata: true,
                        url: productType === "FITNESS" ? ActionUtilV1.cultCenter(seoUrlParam, center.id.toString(), undefined, true) : ActionUtilV1.mindCenter(seoUrlParam, center.id.toString(), undefined, true),
                    }

                    const document = _.find(center.documents, { tagName: "PRODUCT_BNR" })
                    const imageSuffix = document ? document.URL : undefined
                    if (imageSuffix) {
                        const image: SiteMapImage = {
                            url: CLOUDINARY_PREFIX + imageSuffix,
                            title: center.name,
                            caption: center.name,
                            geoLocation: center.locality,
                            license: IMAGE_LICENSE_URL
                        }
                        sitemapUrl.img = [image]
                    }
                     if (center.status === "ACTIVE") sitemapUrls.push(sitemapUrl)
                })
            }
            return sitemapUrls
        }

        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), 7 * TimeUtil.TIME_IN_SECONDS.DAY)
        private async getRecipeShowsListPage(@cacheKey category: OnDemandVideoCategory): Promise<SiteMapUrl[]> {
            const sitemapUrls: SiteMapUrl[] = []
            const recipeShowDataList = await this.diyFulfilmentService.getOnDemandVideoCollectionsForCategory("0", category, "LIVE")

            recipeShowDataList.forEach((recipeData) => {
                let siteMapImage: SiteMapImage
                let siteMapUrl: SiteMapUrl

                siteMapUrl = {
                    changefreq: "monthly",
                    lastmodrealtime: true,
                    priority: 1.0,
                    cdata: true,
                    url: ActionUtil.recipeShowsPage(recipeData.onDemandVideoCollectionId, { productName: recipeData.title }, "EAT", "", "DESKTOP")
                }

                if (recipeData.imageUrlMap["MAGAZINE"]) {
                    siteMapImage = {
                        title: recipeData.title,
                        caption: recipeData.description,
                        url: CLOUDINARY_PREFIX + recipeData.imageUrlMap["MAGAZINE"],
                        license: IMAGE_LICENSE_URL
                    }

                    siteMapUrl.img = [siteMapImage]
                }

                sitemapUrls.push(siteMapUrl)
            })

            return sitemapUrls
        }

        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), 7 * TimeUtil.TIME_IN_SECONDS.DAY)
        private async getSEOListingPageUrls(@cacheKey changeFrequency: CHANGE_FREQUENCY): Promise<SiteMapUrl[]> {
            const sitemapUrls: SiteMapUrl[] = []
            const listingPageList = await this.listingPageCreatorReadDao.retrieve()

            listingPageList.forEach((listingPage) => {
                sitemapUrls.push({
                    changefreq: changeFrequency,
                    lastmodrealtime: true,
                    priority: 1.0,
                    cdata: true,
                    url: `/lp/${listingPage.category}/${listingPage.subCategory}/${listingPage.slug}`
                })
            })

            return sitemapUrls
        }

        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), 7 * TimeUtil.TIME_IN_SECONDS.DAY)
        private async getDIYRecipesUrl(@cacheKey changeFrequency: CHANGE_FREQUENCY): Promise<SiteMapUrl[]> {
            const sitemapUrls: SiteMapUrl[] = []
            const response = await this.diyFulfilmentService.getCustomSEORecipeMeta()
            if (!response.data)
                return sitemapUrls
            response.data.forEach((recipe) => {
                const seoUrlParam: SeoUrlParams = {
                    productName: recipe.recipeTitle
                }
                const sitemapUrl: SiteMapUrl = {
                    changefreq: changeFrequency,
                    lastmodrealtime: true,
                    priority: 1.0,
                    cdata: true,
                    url: ActionUtilV1.recipeHomePage(seoUrlParam, recipe.recipeId),
                }
                const thumbnailImage = _.get(recipe, "imageDetails.thumbnailImage")
                if (thumbnailImage) {
                    const image: SiteMapImage = {
                        url: CLOUDINARY_PREFIX + thumbnailImage,
                        title: recipe.recipeTitle,
                        license: IMAGE_LICENSE_URL
                    }
                    if (recipe.recipeSubTitle) {
                        image.caption = recipe.recipeSubTitle
                    }
                    sitemapUrl.img = [image]
                }
                sitemapUrls.push(sitemapUrl)
            })
            return sitemapUrls
        }

        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), 7 * TimeUtil.TIME_IN_SECONDS.DAY)
        private async getDiagnosticTestUrl(@cacheKey productStatus: Status): Promise<SiteMapUrl[]> {
            const sitemapUrlList: SiteMapUrl[] = []
            const diagnosticAttribute: ProductSearchApiParams = {
                pageNo: 0,
                pageSize: 1000,
                productStatus
            }
            const diagnosticSummaryList = await this.diagnosticService.getDiagnosticProductSummaryWithAttributes(diagnosticAttribute)

            if (!diagnosticSummaryList || !diagnosticSummaryList.content.length) {
                return sitemapUrlList
            }

            diagnosticSummaryList.content.forEach((diagnosticProduct) => {
                const { slugValue, name, imageUrl } = diagnosticProduct
                const url = slugValue ? ActionUtilV1.diagnosticTestProductPage(slugValue) : undefined
                let siteMapUrl: SiteMapUrl
                let siteMapImage: SiteMapImage

                if (!url) {
                    return
                }

                siteMapUrl = {
                    changefreq: "monthly",
                    lastmodrealtime: true,
                    priority: 1.0,
                    cdata: true,
                    url
                }

                if (imageUrl) {
                    siteMapImage = {
                        url: CLOUDINARY_PREFIX + imageUrl,
                        title: name,
                        license: IMAGE_LICENSE_URL
                    }

                    siteMapUrl.img = [siteMapImage]
                }

                sitemapUrlList.push(siteMapUrl)
            })

            return sitemapUrlList
        }

        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), 7 * TimeUtil.TIME_IN_SECONDS.DAY)
        private async getDoctorBookingPageSEOUrl(@cacheKey changeFreq: CHANGE_FREQUENCY): Promise<SiteMapUrl[]> {
            const doctorDetailList = await this.healthfaceService.getDoctorSlugMappingById() as Doctor[],
                siteMapUrls: SiteMapUrl[] = []

            doctorDetailList?.forEach((doctorData) => {
                let siteMapUrl: SiteMapUrl
                let siteMapImage: SiteMapImage
                let imageUrl: string
                let url: string

                if (!doctorData.slugValue) {
                    return
                }

                url = RouteNames.CareDoctorBookingByName.replace(":slugValue", doctorData.seoSlugValue || doctorData.slugValue)
                imageUrl = doctorData.displayImage

                siteMapUrl = {
                    changefreq: changeFreq,
                    lastmodrealtime: true,
                    priority: 1.0,
                    cdata: true,
                    url
                }

                if (imageUrl) {
                    siteMapImage = {
                        url: CLOUDINARY_PREFIX + imageUrl,
                        title: doctorData.name,
                        license: IMAGE_LICENSE_URL
                    }

                    siteMapUrl.img = [siteMapImage]
                }

                siteMapUrls.push(siteMapUrl)
            })

            return siteMapUrls
        }

        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), 7 * TimeUtil.TIME_IN_SECONDS.DAY)
        private async getDoctorListingPageUrl(@cacheKey changeFrequency: CHANGE_FREQUENCY): Promise<SiteMapUrl[]> {
            const siteMapUrlList: SiteMapUrl[] = []
            const specialityGroupTypes = [
                "GP",
                "COVID_ADULTS",
                "COVID_KIDS",
                "COVID_UNLIMITED",
                "PULMONOLOGIST",
                "SPECIALIST",
                "SUPER_SPECIALIST",
                "SURGEON",
                "LC",
                "MIND_THERAPIST",
                "COVID_THERAPIST",
                "SLEEP_THERAPIST",
                "PSYCHIATRY",
                "PHYSIOTHERAPIST"
            ]

            for (const groupType of specialityGroupTypes) {
                const response = await this.healthfaceService.getConsultationSellableProducts(null, groupType)

                for (const consultationSellableProduct of response.consultationTypes) {
                    for (const product of consultationSellableProduct.products) {
                        const consultationProduct = <ConsultationProduct>await this.catalogueService.getProduct(product.code)
                        const url = CareUtil.getDoctorListingWebRoute(consultationProduct)
                        const imageUrl = consultationProduct.imageUrl

                        let siteMapUrl: SiteMapUrl
                        let siteMapImage: SiteMapImage

                        if (!consultationProduct.urlPath) {
                            continue
                        }

                        siteMapUrl = {
                            changefreq: changeFrequency,
                            lastmodrealtime: true,
                            priority: 1.0,
                            cdata: true,
                            url
                        }

                        if (imageUrl) {
                            siteMapImage = {
                                url: CLOUDINARY_PREFIX + imageUrl,
                                title: consultationProduct.name || consultationProduct.title,
                                license: IMAGE_LICENSE_URL,
                                ...(consultationProduct.subTitle ? { caption: consultationProduct.subTitle } : {})
                            }

                            siteMapUrl.img = [siteMapImage]
                        }

                        siteMapUrlList.push(siteMapUrl)
                    }
                }
            }

            return siteMapUrlList
        }

        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), 7 * TimeUtil.TIME_IN_SECONDS.DAY)
        private async getGymPacksUrl(@cacheKey changeFrequency: CHANGE_FREQUENCY, cities: City[]): Promise<SiteMapUrl[]> {
            const sitemapUrls: SiteMapUrl[] = []
            for (let i = 0; i < cities.length; i++) {
                const city = cities[i]
                const supportedPacks: OfflineFitnessPack[] = await this.offlineFitnessPackService.searchCachedPacks({
                    namespace: Namespace.OFFLINE_FITNESS,
                    productTypes: ["GYMFIT_FITNESS_PRODUCT"],
                    restrictions: {cities: [city.cityId]},
                    productSubType: ProductSubType.GENERAL,
                    status: "ACTIVE",
                    saleEnabled: true,
                    isPrivate: false
                })
                const packsSupportedInWeb = supportedPacks.filter(pack => GymfitUtil.isPackSupportedinWeb(pack) )
                const packsSupportedInApp = supportedPacks.filter(pack => GymfitUtil.isPackSupportedinApp(pack) )
                const packs = _.union(packsSupportedInApp, packsSupportedInWeb)

                packs.forEach(pack => {
                    const imageSuffix = pack.product.images?.[0]?.imageURL
                    const image: SiteMapImage = {
                        url: CLOUDINARY_PREFIX + imageSuffix,
                        title: pack.title,
                        caption: pack.title,
                        geoLocation: city.name,
                        license: IMAGE_LICENSE_URL
                    }
                    const sitemapUrl: SiteMapUrl = {
                        changefreq: changeFrequency,
                        lastmodrealtime: true,
                        priority: 1.0,
                        img: [image],
                        cdata: true,
                        url: ActionUtilV1.getMembershipPackWebUrl(pack.productId),
                    }
                    sitemapUrls.push(sitemapUrl)
                })
            }
            return sitemapUrls
        }

        private async getGymCenterUrl(): Promise<SiteMapUrl[]> {
            const sitemapUrls: SiteMapUrl[] = []
            const tz = "Asia/Kolkata"
            const centers: GymfitCenter[] = await this.gymfitService.getGymfitCenters({includeAddress: true})
            centers.forEach(center => {
                const seoUrlParam: SeoUrlParams = GymfitUtil.getCenterSeoUrlParam(center)
                const sitemapUrl: SiteMapUrl = {
                    changefreq: "weekly",
                    lastmodrealtime: true,
                    priority: 1.0,
                    cdata: true,
                    url: ActionUtilV1.getGymCenterWebUrl(seoUrlParam, center.id)
                }

                // const document = _.find(center.documents, { tagName: "PRODUCT_BNR" })
                const imageMedia = center.media && center.media.heroMedia ? center.media.heroMedia.find(media => media.type === MediaType.IMAGE) : undefined
                const imageSuffix = imageMedia ? imageMedia.mediaUrl : undefined
                if (imageSuffix) {
                    const image: SiteMapImage = {
                        url: CLOUDINARY_PREFIX + imageSuffix,
                        title: center.name,
                        caption: center.name,
                        geoLocation: center.locality,
                        license: IMAGE_LICENSE_URL
                    }
                    sitemapUrl.img = [image]
                }
                if (center.status === "ACTIVE") sitemapUrls.push(sitemapUrl)
            })
            return sitemapUrls
        }

        /*
        *
        *
        *
        *  CULTSPORT SITEMAP PRIVATE METHODS
        *
        *
        *
        */
        private async cultSportStaticUrls(): Promise<SiteMapUrl[]> {
            const commonStaticMeta: { changefreq: CHANGE_FREQUENCY, priority: number, lastmodrealtime: boolean } = {
                    changefreq: "weekly",
                    priority: 0.8,
                    lastmodrealtime: true
                },
                sitemapUrls: SiteMapUrl[] = []

            const staticVMPages: string[] = ["/"]

            staticVMPages.forEach((url) => (sitemapUrls.push({
                url,
                ...commonStaticMeta
            })))
            return sitemapUrls
        }

        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), 1 * TimeUtil.TIME_IN_SECONDS.DAY)
        private async getCultsportCollectionSEOUrl(@cacheKey changeFreq: CHANGE_FREQUENCY, @cacheKey nonIndexablePageSeoPageIds: string[]): Promise<SiteMapUrl[]> {
            let breakLoop: boolean = false
            let finalCollectionList: CollectionWidgetConfig[] = []
            const siteMapUrls: SiteMapUrl[] = []
            const collectionRedirectionMap = CultsportUtil.getCollectionRedirectionMap()
            const deletedCollectionList = CultsportUtil.getDeletedCollectionList()
            for (let i: number = 0; !breakLoop; i++) {
                const list: CollectionWidgetConfig[] = (await this.magnetoService.listCollectionWidgetConfig("", i, 50))?.data || []
                finalCollectionList = finalCollectionList.concat(list)
                if (_.isEmpty(list) || list.length < 50) {
                    breakLoop = true
                }
            }
            finalCollectionList?.forEach(collection => {
                if (!nonIndexablePageSeoPageIds.includes(collection.widgetId) && !collectionRedirectionMap[collection.widgetId] && !deletedCollectionList.includes(collection.widgetId.toString()) && !collection.widgetId.toLowerCase().includes("test") && !collection.widgetId.toLowerCase().includes("sale")) {
                    siteMapUrls.push({
                        changefreq: changeFreq,
                        lastmodrealtime: true,
                        priority: 1.0,
                        cdata: true,
                        url: `/${collection.widgetId}`
                    })
                }
            })

            return siteMapUrls
        }

        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), 1 * TimeUtil.TIME_IN_SECONDS.DAY)
        private async getCultsportNonIndexablePageIds(): Promise<string[]> {
            return (await this.seoDao.find({condition: {"nonIndexablePage": true}}))?.map(item => item.seoPageId)
        }

        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), 1 * TimeUtil.TIME_IN_SECONDS.DAY)
        private async getCultsportPDPList() {
            const categories = ["APPAREL", "SUPPLEMENTS", "EQUIPMENT", "ACCESSORIES", "FOOTWEAR", "COSMETICS", "OTHER", "LARGE EQUIPMENT", "BIOSENSOR"]
            const productListPromises = categories.map((async (category) => {
                let page = 0, size = 1000
                const products = []
                while (size === 1000) {
                    page++
                    const productsResponse = await this.gearService.getProductPaginatedFeed(page, category)
                    size = productsResponse.size
                    products.push(...productsResponse.data)
                }
                return products
            }))
            const results = await Promise.all(productListPromises)
            return _.flatMap(results).filter(Boolean).filter(product => product?.attributes?.orderable)
        }

        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), 1 * TimeUtil.TIME_IN_SECONDS.DAY)
        private async getCultsportPDPSEOUrl(@cacheKey changeFreq: CHANGE_FREQUENCY, @cacheKey nonIndexablePageSeoPageIds: string[]): Promise<SiteMapUrl[]> {
            const siteMapUrls: SiteMapUrl[] = []
            const finalProductList = await this.getCultsportPDPList()
            finalProductList?.forEach((product) => {
                if (!nonIndexablePageSeoPageIds.includes(product?.attributes?.slug)) {
                    siteMapUrls.push({
                        changefreq: changeFreq,
                        lastmodrealtime: true,
                        priority: 1.0,
                        cdata: true,
                        url: `/${product?.attributes?.slug || "v2"}/product/${product.id}`,
                        img: typeof product?.attributes?.imageKey?.[0] === "string" ? [{
                            url: CLOUDINARY_PREFIX + product?.attributes?.imageKey?.[0],
                            title: product.title,
                            caption: product.description,
                            license: IMAGE_LICENSE_URL
                        }] : undefined
                    })
                }
            })

            return siteMapUrls
        }

        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), 1 * TimeUtil.TIME_IN_SECONDS.DAY)
        private async getCultsportLPRouteSEOUrl(@cacheKey changeFreq: CHANGE_FREQUENCY,  @cacheKey nonIndexablePageSeoPageIds: string[]): Promise<SiteMapUrl[]> {
            const pages: Page[] = await this.pageDao.find({ condition: { tenant: "cultsport" }, select: {"history": 0} })
            const siteMapUrls: SiteMapUrl[] = []
            pages?.filter(page => {
                return page.status === "LIVE" && !nonIndexablePageSeoPageIds.includes(page.pageId) && (!(["cultsport", "cultsport-pdp", "cultsport-plp", "cultsport-blog", "watch-support"].includes(page.pageId) || page.pageId.includes("sale") || page.pageId.includes("test")))
            }).forEach((page) => {
                // LP Routes
                siteMapUrls.push({
                    changefreq: changeFreq,
                    lastmodrealtime: true,
                    priority: 1.0,
                    cdata: true,
                    url: `/lp/${page.pageId}`,
                    img: undefined
                })
            })

             return siteMapUrls
        }

        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), 1 * TimeUtil.TIME_IN_SECONDS.DAY)
        private async getCultsportBlogArticlesUrl(@cacheKey changeFreq: CHANGE_FREQUENCY): Promise<SiteMapUrl[]> {
            const query: IFindQuery = {
                condition: {
                    status: "LIVE"
                }
            }
            const articles: CSBlogArticle[] = await this.csBlogArticleReadOnlyDao.find(query)
            const siteMapUrls: SiteMapUrl[] = []
            articles?.forEach((article) => {
                // LP Routes
                siteMapUrls.push({
                    changefreq: changeFreq,
                    lastmodrealtime: true,
                    priority: 1.0,
                    cdata: true,
                    url: `/blog/${article.category}/${article.slug}`,
                    img: article.imageUrl ? [{
                        url: CLOUDINARY_PREFIX + article.imageUrl,
                        title: article.title,
                        caption: undefined,
                        license: IMAGE_LICENSE_URL
                    }] : undefined
                })
            })

             return siteMapUrls
        }

        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), 1 * TimeUtil.TIME_IN_SECONDS.DAY)
        private async getCultsportBlogCategoriesUrl(@cacheKey changeFreq: CHANGE_FREQUENCY): Promise<SiteMapUrl[]> {
            const query: IFindQuery = {
                condition: {
                    status: "LIVE"
                }
            }
            const categories: CSBlogCategory[] = await this.csBlogCategoryReadOnlyDao.find(query)
            const siteMapUrls: SiteMapUrl[] = []
            categories?.forEach((category) => {
                siteMapUrls.push({
                    changefreq: changeFreq,
                    lastmodrealtime: true,
                    priority: 1.0,
                    cdata: true,
                    url: `/category/${category.slug}`,
                    img: undefined
                })
            })

             return siteMapUrls
        }

        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), 1 * TimeUtil.TIME_IN_SECONDS.DAY)
        private async getCultsportBlogTagsUrl(@cacheKey changeFreq: CHANGE_FREQUENCY): Promise<SiteMapUrl[]> {
            const query: IFindQuery = {
                condition: {
                    status: "LIVE"
                }
            }
            const tags: CSBlogTag[] = await this.csBlogTagReadOnlyDao.find(query)
            const siteMapUrls: SiteMapUrl[] = []
            tags?.forEach((tag: CSBlogTag) => {
                siteMapUrls.push({
                    changefreq: changeFreq,
                    lastmodrealtime: true,
                    priority: 1.0,
                    cdata: true,
                    url: `/tag/${tag.slug}`,
                    img: undefined
                })
            })

             return siteMapUrls
        }

        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), 1 * TimeUtil.TIME_IN_SECONDS.DAY)
        public async getMixpanelCategoryWisePopularPDPIDSPages(@cacheKey changeFreq: CHANGE_FREQUENCY) {
            const finalProductListMap = {} as any
            (await this.getCultsportPDPList()).forEach((product: any) => {
               finalProductListMap[product.id] = {
                url: `/${product?.attributes?.slug || "v2"}/product/${product.id}`,
                title: product.title,
               }
            })
            const options = {
              method: "GET",
              headers: {
                accept: "application/json",
                authorization: "Basic Q0YtQVBJLjMyYzk1NS5tcC1zZXJ2aWNlLWFjY291bnQ6OVl1TGlmNEdvTHdzNFhURUtGVHpGMXBoS3QzWW9yZnE="
              }
            }
            const response = await fetch("https://eu.mixpanel.com/api/2.0/insights?project_id=2928315&bookmark_id=********", options)
              .then((response: any) => response.json())
              .then((response: any) => {
               return response
              })
              .catch((err: any) => {
                this.rollbarService.sendError(err)
                return {}
              })

            const siteMapData = {} as any
            const categoryWisePopularPDPMap = response?.series?.data
            if (categoryWisePopularPDPMap) {
                delete categoryWisePopularPDPMap?.["$overall"]
                const response = Object.keys(categoryWisePopularPDPMap).map(category => {
                    if (categoryWisePopularPDPMap[category]) {
                        delete categoryWisePopularPDPMap[category]?.["$overall"]
                        let productIdCountList: {productId: string, count: number}[] = Object.keys(categoryWisePopularPDPMap[category]).map(productId => {
                            return {
                                productId,
                                count: Number(categoryWisePopularPDPMap[category]?.[productId]?.["all"] || 0)
                            }
                        }).filter(item => item.count > 100)
                        _.orderBy(productIdCountList, "count", "desc")
                        if (productIdCountList?.length > 40) {
                            productIdCountList = productIdCountList.slice(0, 40)
                        }
                        productIdCountList = productIdCountList.map(item => item.productId).map(productId => finalProductListMap[productId]).filter(Boolean)
                        return {
                            title: category,
                            count: productIdCountList.length,
                            urlList: productIdCountList
                        }
                    }
                    return null
                }).filter(Boolean)
                _.orderBy(response, "count", "desc")
                response.forEach(item  => {
                    delete item.count
                    siteMapData[item.title] = item
                })
                return siteMapData
            }
            return {}
        }
    }
    return SitemapController
}
export default SitemapControllerFactory
