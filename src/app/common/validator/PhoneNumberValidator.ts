import IValidator from "./IValidator"
import { GenericError } from "@curefit/error-client"


class PhoneNumberValidator implements IValidator<number> {

    public validate(phoneNumber: number): Promise<boolean> {
        if (phoneNumber == null || phoneNumber < 1000000000 || phoneNumber > 9999999999) {
            const genericErr = new GenericError({message: "Invalid phone number"})
            genericErr.statusCode = 400
            return Promise.reject(genericErr)
        }
        return Promise.resolve(true)
    }

}

export default PhoneNumberValidator
