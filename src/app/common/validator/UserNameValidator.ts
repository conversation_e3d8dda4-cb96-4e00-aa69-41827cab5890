import IValidator from "./IValidator"
import { GenericError } from "@curefit/error-client"

class UserNameValidator implements IValidator<string> {

    public validate(name: string, entityName: string = "Name"): Promise<boolean> {
        if (name == null || name.length < 2) {
            const genericErr = new GenericError({message: `${entityName} too short`})
            genericErr.statusCode = 400
            return Promise.reject(genericErr)
        } else if (name.length >= 100) {
            const genericErr = new GenericError({message: `${entityName} exceeds 100 characters limit.`})
            genericErr.statusCode = 400
            return Promise.reject(genericErr)
        }
        return Promise.resolve(true)
    }

    public validateFullName(firstName: string, lastName: string): Promise<boolean> {
        return this.validate(firstName, "First Name").catch((err) => {
            const genericErr = new GenericError({message: "First" + err.message})
            genericErr.statusCode = 400
            return Promise.reject(genericErr)
        }).then((isValid: boolean) => {
            return this.validate(lastName, "Last Name").catch((err) => {
                const genericErr = new GenericError({message: "Last" + err.message})
                genericErr.statusCode = 400
                return Promise.reject(genericErr)
            })
        })
    }
}

export default UserNameValidator
