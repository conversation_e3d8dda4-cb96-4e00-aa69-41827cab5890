import IValidator from "./IValidator"
import { GenericError } from "@curefit/error-client"
import { TimeUtil } from "@curefit/util-common"

class DateValidator implements IValidator<string> {

    public validate(date: string): Promise<boolean> {
        if (TimeUtil.isValidDate(date)) {
            return Promise.resolve(true)
        } else {
            const genericErr = new GenericError({message: "Date is invalid"})
            genericErr.statusCode = 400
            return Promise.reject(genericErr)
        }
    }
}

export default DateValidator
