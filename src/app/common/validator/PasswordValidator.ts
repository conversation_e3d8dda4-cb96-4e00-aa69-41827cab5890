import IValidator from "./IValidator"
import { GenericError } from "@curefit/error-client"


class PasswordValidator implements IValidator<string> {

    public validate(password: string): Promise<boolean> {
        if (password == null || password.length < 6) {
            const genericErr = new GenericError({message: "Password should be atleast 6 characters long."})
            genericErr.statusCode = 400
            return Promise.reject(genericErr)
        }
        return Promise.resolve(true)
    }

}

export default PasswordValidator
