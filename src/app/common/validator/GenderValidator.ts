import IValidator from "./IValidator"
import { Gender } from "@curefit/user-common"
import { GenericError } from "@curefit/error-client"

class GenderValidator implements IValidator<Gender> {

    public validate(gender: Gender): Promise<boolean> {
        const genderValues = Object.values(Gender)
        if (genderValues.includes(gender)) {
            return Promise.resolve(true)
        } else {
            const genericErr = new GenericError({message: "Gender must be one of the values " + genderValues})
            genericErr.statusCode = 400
            return Promise.reject(genericErr)
        }
    }
}

export default GenderValidator
