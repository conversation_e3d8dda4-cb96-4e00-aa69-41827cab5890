import IValidator from "./IValidator"
import { GenericError } from "@curefit/error-client"

class EmailValidator implements IValidator<string> {

    public validate(email: string): Promise<boolean> {
        const atpos = email.indexOf("@")
        const dotpos = email.lastIndexOf(".")
        if (atpos < 1 || dotpos < atpos + 2 || dotpos + 2 >= email.length) {
            const genericErr = new GenericError({message: "Invalid email ID"})
            genericErr.statusCode = 400
            return Promise.reject(genericErr)
        }
        return Promise.resolve(true)
    }

}

export default EmailValidator
