import { IPlatformSegmentFetchHelper } from "@curefit/base"
import { ISegmentationCacheClient } from "@curefit/segmentation-service-client"


export class PlatformSegmentWrapper implements IPlatformSegmentFetchHelper {

    private userId: string
    private segmentationCacheClient: ISegmentationCacheClient
    private platformSegments: string[]


    constructor(userId: string, segmentationCacheClient: ISegmentationCacheClient) {
        this.userId = userId
        this.segmentationCacheClient = segmentationCacheClient
        this.platformSegments = undefined
    }

    public async getSegments(): Promise<string[]> {
        if (this.platformSegments === undefined) {
            this.platformSegments = await this.segmentationCacheClient.getUserSegments(this.userId)
        }
        return this.platformSegments
    }
}
