import { injectable, inject } from "inversify"
import { BASE_TYPES, <PERSON>tch<PERSON><PERSON>, <PERSON><PERSON> } from "@curefit/base"
import { Constants } from "@curefit/base-utils"
import { IBranchRequestBody, IBranchRequestHeader, IBranchResponse } from "./IBranchService"
import { Tenant } from "@curefit/user-common"
import AppUtil from "../../util/AppUtil"

const fetch = require("node-fetch")

const BRANCH_IO_BASE_URL = "https://api.branch.io/v1"
@injectable()
export class BranchService {
    constructor(
        @inject(BASE_TYPES.FetchUtil) protected fetchHelper: FetchUtil,
        @inject(BASE_TYPES.ILogger) private logger: Logger
    ) { }

    private static getBranchKey(tenant: Tenant) {
        if (tenant === Tenant.LIVEFIT_APP) {
            switch (process.env.ENVIRONMENT) {
                case "PRODUCTION": {
                    return "key_live_gjGKZSrop9M9RW5JGhxa4dmpxBokv5hO"
                }
                case "LOCAL": {
                    return "key_test_enUT5Hrje6N1M85S4dtuQmhnAAcpw8fY"
                }
                case "STAGE": {
                    return "key_test_enUT5Hrje6N1M85S4dtuQmhnAAcpw8fY"
                }
                default: {
                    return "key_live_gjGKZSrop9M9RW5JGhxa4dmpxBokv5hO"
                }
            }
        } else {
            switch (process.env.ENVIRONMENT) {
                case "PRODUCTION": {
                    return "key_live_anzEyViCZLYFjFyzpJgf5eedEqgnCP25"
                }
                case "LOCAL": {
                    return "key_test_hmyzFVcx3J7FmDDyfKmj8fhowqcouZkl"
                }
                case "STAGE": {
                    return "key_test_hmyzFVcx3J7FmDDyfKmj8fhowqcouZkl"
                }
                default: {
                    return "key_live_anzEyViCZLYFjFyzpJgf5eedEqgnCP25"
                }
            }
        }

    }

    async linkCreate(tenant: Tenant, body: IBranchRequestBody, headers?: IBranchRequestHeader): Promise<IBranchResponse> {
        const url = `${BRANCH_IO_BASE_URL}/url`
        body.branch_key = BranchService.getBranchKey(tenant)
        try {
            const response = await fetch(url, this.fetchHelper.post(body, headers))
            return await this.fetchHelper.parseResponse<any>(response)
        }
        catch (error) {
            console.log(error)
            throw error
        }
    }

}
