import { Tenant } from "@curefit/user-common"

export interface IBranchRequestBody {
    [key: string]: (string | number | boolean) | { [index: string]: (string | number | boolean) }
}

export interface IBranchRequestHeader {
    [key: string]: string
}

export interface IBranchResponse {
    [key: string]: string
}

interface IBranchService {
    linkCreate: (tenant: Tenant, body: IBranchRequestBody, headers?: IBranchRequestHeader) => Promise<IBranchResponse>
}

export default IBranchService
