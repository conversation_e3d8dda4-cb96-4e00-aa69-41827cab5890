import { Action, ManageOptions, WidgetType, WidgetView } from "./WidgetView"
import * as _ from "lodash"
import { UserReward } from "@curefit/quest-common"
import * as momentTz from "moment-timezone"
import { FitclubUtil } from "../../util/FitclubUtil"
import { UserContext } from "@curefit/userinfo-common"
import { TimeUtil } from "@curefit/util-common"
import { LedgerEntry, Response } from "@curefit/reward-common"

export interface RewardItem {
    title: string,
    description: string
    subTitle?: string
    showDivider?: boolean
    action?: any
    leftInfo: {
        type: string
        title: string
        icon: string
        subTitle: string
    }
    rightInfo: {
        moreAction: Action
    }
}

export interface RewardWidgetHeader {
    title: string,
    action?: any
}

export class RewardListingSummaryWidget implements WidgetView {
    widgetType: WidgetType
    header: RewardWidgetHeader
    dividerType?: string
    hasDividerBelow?: boolean
    data: RewardItem[]

    constructor(header: RewardWidgetHeader, data: RewardItem[], dividerType?: string, hasDividerBelow?: boolean ) {
        this.widgetType = "REWARD_LISTING_SUMMARY_WIDGET"
        this.header = header
        this.data = data
        if (dividerType && hasDividerBelow) {
            this.dividerType = dividerType
            this.hasDividerBelow = hasDividerBelow
        }
    }
}

export class RewardClaimedWidget implements WidgetView {
    widgetType: WidgetType
    dividerType?: string
    hasDividerBelow?: boolean
    data: RewardItem[]

    constructor(data: LedgerEntry<Response>[], userContext: UserContext, reportIssues: any,  dividerType?: string, hasDividerBelow?: boolean) {
        this.widgetType = "REWARD_CLAIMED_WIDGET"
        const claimedItems: RewardItem[] = _.map(data, (reward) => {
            return FitclubUtil.getClaimedRewardItem(reward, userContext.userProfile.timezone, reportIssues)
        })
        this.data = claimedItems
        if (dividerType && hasDividerBelow) {
            this.dividerType = dividerType
            this.hasDividerBelow = hasDividerBelow
        }
    }
}

export class RewardExpiredWidget implements WidgetView {
    widgetType: WidgetType
    dividerType?: string
    hasDividerBelow?: boolean
    data: RewardItem[]

    constructor(data: UserReward[], dividerType?: string, hasDividerBelow?: boolean) {
        this.widgetType = "REWARD_CLAIMED_WIDGET"
        const claimedItems: RewardItem[] = _.map(data, (reward) => {
            return FitclubUtil.getExpiredRewardItem(reward)
        })
        this.data = claimedItems
        if (dividerType && hasDividerBelow) {
            this.dividerType = dividerType
            this.hasDividerBelow = hasDividerBelow
        }
    }
}


export class RewardUnclaimedWidget implements WidgetView {
    widgetType: WidgetType
    imageUrl: string
    title: string
    subTitle?: string
    description?: string
    cardAction: Action
    backgroundColor?: string
    constructor(userReward: UserReward, userContext: UserContext) {
        const subTitle = momentTz.tz(userReward.date.date, userReward.date.timezone).format("Do MMM")
        TimeUtil.getMomentNow(userContext.userProfile.timezone)
        const diffTimeInDays = TimeUtil.diffInDays(
            userContext.userProfile.timezone,
            TimeUtil.getMomentForDate(userReward.date.date, userContext.userProfile.timezone).toDate().toDateString(),
            TimeUtil.getDateNow(userContext.userProfile.timezone).toDateString())
        const expiringInDays = 30 - diffTimeInDays > 0 ?  30 - diffTimeInDays : 1 // 30 days hardcoded logic
        this.widgetType = "REWARD_UNCLAIMED_WIDGET"
        this.imageUrl = "/image/icons/fitclub/gift_icon.png" // s3 hardcoded url
        this.title = FitclubUtil.getUnclaimedRewardTitle(userReward.fulfilmentId) // extracting from title=
        this.subTitle = subTitle // extracting from created date
        this.description = `Expires in ${expiringInDays} days`
        this.backgroundColor = "#552c47"
        this.cardAction = {
            actionType: "CLAIM_FITCLUB_REWARD",
            meta: {
                userRewardId: userReward.userRewardId
            }
         }

    }
}
