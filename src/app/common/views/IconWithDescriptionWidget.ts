import { WidgetType, WidgetView } from "./WidgetView"
import * as _ from "lodash"

export interface IconWithDescriptionItem {
    icon: string,
    imageUrl: string,
    description: string
}

export class IconWithDescriptionWidget implements WidgetView {
    widgetType: WidgetType
    data: IconWithDescriptionItem[]

    constructor(data: IconWithDescriptionItem[]) {
        this.widgetType = "ICON_WITH_DESCRIPTION_WIDGET"
        this.data = _.map(data, (item) => {
            return {
                icon: item.icon,
                imageUrl: item.imageUrl,
                description: item.description
            }
        })
    }
}
