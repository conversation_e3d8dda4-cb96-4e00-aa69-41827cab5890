import { WidgetType, WidgetView } from "./WidgetView"
import { DIYRecipeProduct } from "@curefit/diy-common"
import * as _ from "lodash"

export interface INutritionDetails {
    type: string
    quantity: number
    unit: string
    color: string
}

export class NutritionWidget implements WidgetView {
    widgetType: WidgetType
    title: string
    values: INutritionDetails[]
    calories: string
    showDivider?: boolean // for web/mweb

    constructor(recipe: DIYRecipeProduct) {
        if (_.isNil(recipe.nutritionInfo)) {
            return
        }
        this.widgetType = "NUTRITION_WIDGETV2"
        this.values = []
        this.calories = Math.ceil(recipe.nutritionInfo.energy) + " Cal",
        this.title = "Nutrition Info. (per serving)"
        this.values.push(
            {
                type: "Protein",
                quantity: recipe.nutritionInfo.protein,
                unit: "g",
                color: "#4fc6ff"
            },
            {
                type: "Fat",
                quantity: recipe.nutritionInfo.fat,
                unit: "g",
                color: "#ffa522"
            },
            {
                type: "Carbs",
                quantity: recipe.nutritionInfo.carbohydrates,
                unit: "g",
                color: "#f46da0"
            },
            {
                type: "Fibre",
                quantity: recipe.nutritionInfo.crudeFiber,
                unit: "g",
                color: "#916cae"
            }
        )
    }
    async buildView(): Promise<WidgetView> {
        return this
    }
}
