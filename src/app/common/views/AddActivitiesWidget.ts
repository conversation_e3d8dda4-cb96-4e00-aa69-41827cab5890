import { Action, WidgetType, WidgetView } from "./WidgetView"
import { IGMFClient } from "@curefit/gmf-client"
import { User as UserCache, User } from "@curefit/user-common"
import { UserContext } from "@curefit/userinfo-common"
import { eternalPromise } from "@curefit/util-common"
import { ActionUtil as EtherActionUtil } from "@curefit/base-utils"
import * as _ from "lodash"
import AppUtil from "../../util/AppUtil"
import { ActionUtil } from "../../util/ActionUtil"
import { ProductType } from "@curefit/product-common"
import { City } from "@curefit/location-common"
import { ICityService } from "@curefit/location-mongo"
import { CacheHelper } from "../../util/CacheHelper"

interface AddActivity {
    title: string,
    subTitle: string,
    action: Action
    icon?: string,
    color: string
}

export interface IAddActivitiesWidget extends WidgetView {
    cards: AddActivity[]
}

export interface IAddActivityParams {
    gmfClient: IGMFClient,
    cityService: ICityService,
    user: User,
    userContext: UserContext,
    cacheHelper: CacheHelper
}

export class AddActivitiesFitclubWidget implements IAddActivitiesWidget {
    widgetType: WidgetType
    cards: AddActivity[]
    dividerType: string
    hasDividerBelow: boolean
    constructor(userContext: UserContext, productType: ProductType, user: UserCache) {
        this.cards = [
            {
                title: "BOOK A CLASS",
                subTitle: "Daily Reward",
                icon: "/image/addActivity/widgeticon/workout_icon.png",
                action: ActionUtil.getClassBookingAction(userContext, productType, user, undefined, "fitclubMembership"),
                color: "#8080B3"
            },
            {
                title: "ORDER MEAL",
                subTitle: "50% fitcash",
                icon: "/image/addActivity/widgeticon/eat_icon.png",
                action: {
                    actionType: "NAVIGATION",
                    url: EtherActionUtil.eatFitClp()
                },
                color: "#FFA522"
            }
        ]
        this.widgetType = "ADD_ACTIVITIES_WIDGET_V2"
        this.dividerType = "LARGE"
        this.hasDividerBelow = true
    }
}

export class AddActivitiesWidget implements IAddActivitiesWidget {
    widgetType: WidgetType
    cards: AddActivity[]

    constructor() {
        this.cards = []
        this.widgetType = "ADD_ACITIVITES_WIDGET"
    }

    async buildView(params: IAddActivityParams): Promise<void> {
        // const userPlanExistsResult = await eternalPromise(params.gmfClient.checkExistsUserPlan(params.user.id))
        const userPlanExists: boolean = false // _.get(userPlanExistsResult.obj, "exists", false)
        const city: City = params.cityService.getCityById(params.userContext.sessionInfo.sessionData.cityId)

        // const childUserRelations: SubUserRelation[] = AppUtil.filterUsersType(params.user.subUserRelations, Relation.CHILD) // May required when to show booking only for child relation
        const childUserPromises = Array.isArray(params.user.subUserRelations) && params.user.subUserRelations.map((item) => {
            return params.cacheHelper.getUser(item.subUserId)
        })
        const childUsers: User[] = childUserPromises && await Promise.all(childUserPromises) || undefined
        if (!_.isNil(city) && !_.isEmpty(city.availableOfferings)) {
            city.availableOfferings.forEach((offering) => {
                const addActivity: AddActivity = this.createActivtyActionForType(offering, params, childUsers)
                if (!_.isNil(addActivity)) {
                    this.cards.push(addActivity)
                }
            })
        }
    }

    createActivtyActionForType(productType: ProductType, params: IAddActivityParams, subUsers?: User[]): AddActivity {
        if (productType === "FITNESS") {
            return {
                title: "BOOK",
                subTitle: "cult class",
                icon: "/image/addActivity/widgeticon/workout_icon.png",
                action: ActionUtil.getClassBookingAction(params.userContext, productType, params.user, subUsers, "addActivity"),
                color: "#8080B3"
            }
        } else if (productType === "DIY_FITNESS") {
            return {
                title: "WORKOUT",
                subTitle: "at home",
                icon: "/image/addActivity/widgeticon/workout_icon.png",
                action: {
                    actionType: "NAVIGATION",
                    url: "curefit://listpage?pageId=CultAtHome&widgetId=a0b04951-0ff2-4794-a4d0-a8bf34e8b2f2"
                },
                color: "#8080B3"
            }

        } else if (productType === "MIND") {
            return {
                title: "BOOK",
                subTitle: "mind class",
                icon: "/image/addActivity/widgeticon/mind_icon.png",
                action: {
                    actionType: "NAVIGATION",
                    url: EtherActionUtil.getBookCultClassUrl("MIND", AppUtil.isNewClassBookingSuppoted(params.userContext), "addActivity", undefined)
                },
                color: "#F46DA0"
            }
        } else if (productType === "DIY_MEDITATION") {
            return {
                title: "MEDITATE",
                subTitle: "at home",
                icon: "/image/addActivity/widgeticon/mind_icon.png",
                action: {
                    actionType: "NAVIGATION",
                    url: "curefit://listpage?pageId=MindAtHome"
                },
                color: "#F46DA0"
            }
        } else if (productType === "FOOD") {
            return {
                title: "ORDER",
                subTitle: "meals",
                icon: "/image/addActivity/widgeticon/eat_icon.png",
                action: {
                    actionType: "NAVIGATION",
                    url: EtherActionUtil.eatFitClp()
                },
                color: "#FFA522"
            }
        } else if (productType === "RECIPE") {
            return {
                title: "COOK",
                subTitle: "at home",
                icon: "/image/addActivity/widgeticon/eat_icon.png",
                action: {
                    actionType: "NAVIGATION",
                    url: "curefit://listpage?pageId=eatrecipe",
                },
                color: "#FFA522"
            }
        } else if (productType === "CONSULTATION") {
            return {
                title: "CONSULT",
                subTitle: "doctor",
                icon: "/image/addActivity/widgeticon/care_icon.png",
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtil.getConsultationClpPage(),
                },
                color: "#45C6CF"
            }
        } else if (productType === "GEAR") {
            return {
                title: "BUY",
                subTitle: "sportswear",
                icon: "/image/addActivity/widgeticon/gear_icon.png",
                action: {
                    actionType: "NAVIGATION",
                    url: "curefit://cultgearclp?pageId=GearTab"
                },
                color: "#7FB486"
            }
        } else if (productType === "THERAPY") {
            return {
                title: "BOOK",
                subTitle: "therapy",
                icon: "/image/addActivity/widgeticon/therapy_icon.png",
                action: {
                    actionType: "NAVIGATION",
                    url: "curefit://listpage?pageId=MindTherapy",
                },
                color: "#D67B9D"
            }
        } else if (productType === "DIAGNOSTICS") {
            return {
                title: "BOOK",
                subTitle: "Lab Tests",
                icon: "/image/addActivity/widgeticon/diagnostics.png",
                action: {
                    actionType: "NAVIGATION",
                    url: "curefit://listpage?pageId=clphcu",
                },
                color: "#D67B9D"
            }
        }
    }
}
