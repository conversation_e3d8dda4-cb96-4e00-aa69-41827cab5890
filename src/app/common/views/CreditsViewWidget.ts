import { CREDIT_PILL_ICON } from "../../util/AppUtil"

interface IContainerStyling {
    background: string,
    borderRadius: number,
    padding: string,
    border: string
    fontFamily: string
    alignItems: string
    gridGap: number
    backdropFilter: string
}

interface IIcconStyling {
    marginBottom: string
}

interface ICreditsStyling {
    color: string
}

// interface TextStyling {
//     margin: string
// }

class CreditsView {
    constructor(creditsNum: number, text?: string) {
        this.image = CREDIT_PILL_ICON,
        this.credits = creditsNum,
        this.containerStyling = {
            background: "linear-gradient(319deg, rgba(246, 116, 109, 0.10) 0%, rgba(215, 105, 182, 0.10) 6.98%, rgba(171, 119, 255, 0.10) 103.1%)",
            borderRadius: 10,
            padding: "0 4px 0 1px",
            border: "0.7px solid rgba(0, 0, 0, 0.10)",
            fontFamily: "Inter-Medium",
            alignItems: "center",
            gridGap: 3,
            backdropFilter: "blur(40px)"
        },
        this.creditsStyling = {
            color: "#000000"
        },
        this.iconStyling = {
            marginBottom: "1.6px"
        },
        this.appendingText =  text
    }
    public image: string
    public credits: number
    public containerStyling: IContainerStyling
    public iconStyling: IIcconStyling
    public creditsStyling: ICreditsStyling
    public appendingText: string
}

export default CreditsView
