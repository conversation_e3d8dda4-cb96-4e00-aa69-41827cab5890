import { CarouselWidget, ImageCardCarouselView } from "./CarouselWidget"
import { ActionUtil } from "../../util/ActionUtil"
import { UserContext } from "@curefit/userinfo-common"
import { Logger } from "@curefit/base"
import { ITodoActivityWidget } from "./TodoActivityWidget"
import { WalkActivity } from "@curefit/atlas-client"
import { ActivityState } from "@curefit/logging-common"
import { ActivityType } from "@curefit/product-common"

export interface IStepsWidgetParams {
    walkActivity: WalkActivity,
    userContext: UserContext,
    logger: Logger
}

export class StepsCarouselWidget extends CarouselWidget implements ITodoActivityWidget {
    timestamp: number
    status: ActivityState
    activityType: ActivityType
    constructor() {
        super()
        this.widgetType = "TODO_ACTIVITY_WIDGET"
    }

    async buildView(widgetBuilderParams: IStepsWidgetParams): Promise<void> {
        const walkActivity: WalkActivity = widgetBuilderParams.walkActivity
        try {
            // Assign title
            this.title = walkActivity.steps.done + " Steps"

            // Assign text items (Time of class)
            this.leftCalloutText = "DUE"

            // Assign right callout text
            this.rightCalloutText = walkActivity.steps.goal + " Target"

            // Assign status
            this.status = "TODO"

            // Assign activity type
            this.activityType = "WALK"

            // Create the steps card
            const stepsCard: ImageCardCarouselView = await this.getCardForSteps(widgetBuilderParams)
            this.cards.push(stepsCard)
        } catch (e) {
            const loggingParams = {
                walkActivity: walkActivity
            }
            widgetBuilderParams.logger.error("Steps widget building failed for params :: " + JSON.stringify(loggingParams) + " User ID :: " + widgetBuilderParams.userContext.userProfile.userId)
        }
    }

    private async getCardForSteps(widgetBuilderParams: IStepsWidgetParams): Promise<ImageCardCarouselView> {
        const walkActivity: WalkActivity = widgetBuilderParams.walkActivity

        const stepsCard: ImageCardCarouselView = {
            viewType: "BORDERLESS",
            styleType: "WIDE",
            title: "",
            images: [],
            action: {
                actionType: "NAVIGATION",
                url: ""
            },
            quickActions: []
        }

        // Assign title
        stepsCard.title = "Target " + walkActivity.steps.goal + " Steps"

        // Assign image URL
        stepsCard.images.push("/image/today/steps.png")

        // Generate action
        stepsCard.action.url = ActionUtil.getStepsUrl(walkActivity)

        return stepsCard
    }
}
