import { BUNDLE_TYPE, CATEGORY_CODE } from "@curefit/care-common"
import { IBaseWidget, IServiceInterfaces, TestimonialWidgetItem, TestimonialWidgetV2, UserContext } from "@curefit/vm-models"
import * as _ from "lodash"

export class TestimonialWidgetViewV2 extends TestimonialWidgetV2 {
  async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
    const testimonials = await interfaces.healthfaceService.getTestimonials(this.listSize, this.categoryCode as CATEGORY_CODE, this.subCategoryCode as BUNDLE_TYPE)
    if (_.isEmpty(testimonials)) {
      return undefined
    }

    const data: TestimonialWidgetItem[] = []
    const isConfidential = this.subCategoryCode === "MIND_THERAPY"
    testimonials.forEach(item => {
      data.push({
        title: isConfidential ? "Anonymous" : item.patientName,
        subTitle: item.impact,
        description: item.experience,
        readMore: item.experience,
        image: isConfidential ? undefined : item.images.length > 0 ? item.images[0] : undefined})
    })
    this.data = data
    return this
  }
}