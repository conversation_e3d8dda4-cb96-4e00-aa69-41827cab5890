import * as _ from "lodash"
import { WidgetView, WidgetType } from "./WidgetView"
import { OfferV2, OfferAddon } from "@curefit/offer-common"
import { Logger } from "@curefit/base"
import { ProductType } from "@curefit/product-common"
import { Orientation } from "@curefit/vm-common"
import { Action } from "@curefit/apps-common"
import { OfferUtil } from "@curefit/base-utils"

export interface IOfferAddonWidgetParams {
    logger?: Logger,
    offerV2MapPromise: Promise<{
        [key: string]: OfferV2;
    }>,
    useLabel: "CART_LABEL" | "ORDER_LABEL"
    staticOfferItems?: IOfferAddonData[]
    title?: string
}

export interface IOfferAddonData {
    description?: string
    tnc?: string[],
    tncURL?: string
    tag?: {
        title: string,
        bgColor?: string
    }
    action?: Action
    prefixImageurl?: string
    fontSize?: string
    hexColor?: string

    maxLines?: number
}
interface IOfferAddonWidgetData {
    title: string
    offers: IOfferAddonData[]
    type: "ORDER" | "CHECKOUT"
}

export class OfferAddonWidget implements WidgetView {
    widgetType: WidgetType
    data: IOfferAddonWidgetData
    productType?: ProductType
    orientation?: Orientation
    containerStyle?: any
    imageContainerStyle?: any
    bulletViewStyle?: any
    actionSupported?: boolean
    constructor() {
        this.widgetType = "OFFER_ADDONS_WIDGET"
        this.data = {
            title: "Additional Offers",
            offers: [],
            type: "CHECKOUT"
        }
    }

    private getLabel = (item: OfferV2 | OfferAddon, widgetBuilderParams: IOfferAddonWidgetParams): string => {
        let label: string
        if (widgetBuilderParams.useLabel === "CART_LABEL") {
            label = _.get(item, "uiLabels.cartLabel", undefined)
        } else if (widgetBuilderParams.useLabel === "ORDER_LABEL") {
            label = _.get(item, "uiLabels.orderSummaryLabel", undefined)
        }
        return label
    }

    private getType = (widgetBuilderParams: IOfferAddonWidgetParams): IOfferAddonWidgetData["type"] => {
        if (widgetBuilderParams.useLabel === "CART_LABEL") {
            return "CHECKOUT"
        } else if (widgetBuilderParams.useLabel === "ORDER_LABEL") {
            return "ORDER"
        }
        return "CHECKOUT"
    }

    private shouldAddTNC(type: IOfferAddonWidgetData["type"], addTnc?: boolean) {
        return addTnc || type === "ORDER"
    }

    private getTitle(type: IOfferAddonWidgetData["type"]): string {
        switch (type) {
            case "ORDER":
                return "What else you got"
            case "CHECKOUT":
                return "What else you get"
        }
    }

    async  buildView(
        widgetBuilderParams: IOfferAddonWidgetParams,
        addTnc?: boolean,
        excludeNoCostEMI?: boolean
    ): Promise<OfferAddonWidget> {
        this.data.type = this.getType(widgetBuilderParams)
        this.data.title = widgetBuilderParams.title || this.getTitle(this.data.type)
        if (!_.isEmpty(widgetBuilderParams.staticOfferItems)) {
            widgetBuilderParams.staticOfferItems.forEach(offerData => {
                this.data.offers.push(offerData)
            })
        }
        const offerMap: {
            [key: string]: OfferV2;
        } = await widgetBuilderParams.offerV2MapPromise

        _.each(offerMap, (offer: OfferV2) => {
            const offerList: IOfferAddonData[] = []
            let isNoCostEmiOffer = false
            const offerLabel = this.getLabel(offer, widgetBuilderParams)
            const offerData: IOfferAddonData = { description: "" }
            if (this.shouldAddTNC(this.data.type, addTnc)) {
                if (!_.isUndefined(offer.tNcUrl)) {
                    offerData.tncURL = offer.tNcUrl
                }
                if (!_.isUndefined(offer.tNc)) {
                    offerData.tnc = offer.tNc
                }
            }
            if (!_.isUndefined(offerLabel) && offerLabel.length > 0) {
                offerData.description = offerLabel
                offerList.push(offerData)
            }
            if (!_.isEmpty(offer.addons)) {
                _.each(offer.addons, (offerAddon: OfferAddon) => {
                    let label: string
                    label = this.getLabel(offerAddon, widgetBuilderParams)
                    const offerData: IOfferAddonData = { description: "" }
                    if (this.shouldAddTNC(this.data.type, addTnc)
                        && !_.isUndefined(offerAddon.tncUrl)) {
                        offerData.tncURL = offerAddon.tncUrl
                    }
                    if (!_.isUndefined(label) && label.length > 0) {
                        offerData.description = label
                        offerList.push(offerData)
                    }
                    if (offerAddon.addonType === "NO_COST_EMI") {
                        isNoCostEmiOffer = true
                    }
                })
            }

            if (excludeNoCostEMI && isNoCostEmiOffer) {
                return
            } else {
                this.data.offers.push(...offerList)
            }
        })
        if (_.isEmpty(this.data.offers)) {
            return undefined
        }
        return this
    }
}
