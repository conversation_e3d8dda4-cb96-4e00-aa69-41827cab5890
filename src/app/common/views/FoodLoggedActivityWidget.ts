import * as _ from "lodash"
import { ActionUtil } from "../../util/ActionUtil"
import { ILoggedActivityWidgetItem, LoggedActivityWidget } from "./LoggedActivityWidget"
import { LoggableActivitySlot } from "../../logging/ActivityLogging"
import { ActivityDS, FoodDetail } from "@curefit/logging-common"
import { ICatalogueService } from "@curefit/catalog-client"
import { FoodBooking, FoodBookingProduct, IShipmentService } from "@curefit/alfred-client"
import { Logger } from "@curefit/base"
import { MealSlot, FoodProduct as Product } from "@curefit/eat-common"
import IProductBusiness from "../../product/IProductBusiness"
import { SlotUtil } from "@curefit/eat-util"
import { HourMin } from "@curefit/base-common"
import { IActivityLoggingBusiness } from "../../logging/ActivityLoggingBusiness"
import { UserContext } from "@curefit/userinfo-common"

export interface IFoodLogBuilderParams {
    catalogueService: ICatalogueService,
    productBusiness: IProductBusiness,
    activityLoggingBusiness: IActivityLoggingBusiness,
    activities: ActivityDS[],
    date: string,
    logger: Logger,
    userContext: UserContext
}

export class FoodLoggedActivityWidget extends LoggedActivityWidget {
    constructor() {
        super()
        this.title = "Nutrition"
        this.calloutText = ""
        this.priority = 0
    }

    async buildView(activities: ActivityDS[], widgetBuilderParams: IFoodLogBuilderParams): Promise<void> {
        const mealAdditionPromise: Promise<void>[] = []
        const userContext: UserContext = widgetBuilderParams.userContext

        // Convert food activities to widget items
        activities.forEach((activity: ActivityDS) => {
            if (activity.activityType === "FOOD_CONSUMPTION") {
                if (!_.isNil(activity.foodConsumption)) {
                    this.items.push(this.transformFoodConsumptionToWidget(activity, widgetBuilderParams))
                }
            } else if (activity.activityType === "GMF_FOOD_RECOMMENDATION") {
                mealAdditionPromise.push(this.addRecommendationData(activity, widgetBuilderParams))
            }
        })

        await Promise.all(mealAdditionPromise)

        // Sort widget items based on time of meal
        this.items.sort((a: ILoggedActivityWidgetItem, b: ILoggedActivityWidgetItem) => {
            if (!_.isFinite(a.timestamp)) {
                return 1
            } else if (!_.isFinite(b.timestamp)) {
                return -1
            } else {
                return a.timestamp < b.timestamp ? -1 : 1
            }
        })
    }

    private transformFoodConsumptionToWidget(activity: ActivityDS, widgetBuilderParams: IFoodLogBuilderParams): ILoggedActivityWidgetItem {
        const loggedSlot: LoggableActivitySlot = <LoggableActivitySlot>activity.foodConsumption.mealSlotId
        let title: string
        let totalCals: number = 0
        activity.foodConsumption.items.forEach((foodDetail: FoodDetail) => {
            title = !_.isNil(title) ? title + ", " + foodDetail.dishName : foodDetail.dishName
            if (_.isNumber(foodDetail.calories)) totalCals = totalCals + foodDetail.calories
        })
        const loggedSlotName: string = widgetBuilderParams.activityLoggingBusiness.getLoggableSlotName(loggedSlot)
        title = _.isNil(title) ? loggedSlotName : loggedSlotName + " - " + title

        const trimmedTitle = title + " - " + totalCals.toFixed(0) + " cal"

        const slotTime: { start: HourMin, end: HourMin} = widgetBuilderParams.activityLoggingBusiness.getLoggedSlotTime(loggedSlot)
        const timestamp: Date = new Date(activity.date)
        timestamp.setHours(slotTime.start.hour, slotTime.start.min)

        const result: ILoggedActivityWidgetItem = {
            title: trimmedTitle,
            action: {
                actionType: "NAVIGATION",
                url: ActionUtil.addLoggableActivityUrl("DIET", {
                    slotId: loggedSlot,
                    logTypeId: "DIET",
                    date: activity.date,
                    slotName: _.capitalize(loggedSlot)
                })
            },
            status: activity.status,
            activityType: "FOOD_CONSUMPTION",
            timestamp: timestamp.getTime()
        }
        return result
    }

    private async addRecommendationData(activity: ActivityDS, widgetBuilderParams: IFoodLogBuilderParams): Promise<void> {
        try {
            this.items.push({
                title: activity.activityName,
                status: activity.status,
                activityType: activity.activityType,
                action: undefined
            })
        } catch (e) {
            return
        }
    }
}
