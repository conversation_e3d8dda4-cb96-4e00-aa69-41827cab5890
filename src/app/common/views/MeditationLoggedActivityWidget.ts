import * as _ from "lodash"
import { ActionUtil } from "../../util/ActionUtil"
import { LoggedActivityWidget } from "./LoggedActivityWidget"
import { ActivityDS } from "@curefit/logging-common"
import { ICatalogueService } from "@curefit/catalog-client"
import { SleepActivity } from "@curefit/atlas-client"
import TimelineUtil from "../../util/TimelineUtil"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { CultBooking } from "@curefit/cult-common"
import { ICultServiceOld as ICultService } from "@curefit/cult-client"
import { DIYPack } from "@curefit/diy-common"
import { ActionUtil as EtherActionUtil } from "@curefit/base-utils"
import { IActivityLoggingBusiness } from "../../logging/ActivityLoggingBusiness"
import { MindLoggableItem } from "@curefit/maverick-common"
import { UserContext } from "@curefit/userinfo-common"
import AppUtil from "../../util/AppUtil"

export interface IMeditationLogBuilderParams {
    catalogueService: ICatalogueService,
    mindFitService: ICultService,
    activityLoggingBusiness: IActivityLoggingBusiness,
    activities: ActivityDS[],
    date: string
    userContext: UserContext
}

export class MeditationLoggedActivityWidget extends LoggedActivityWidget {
    constructor() {
        super()
        this.title = "Mindfulness"
        this.calloutText = ""
        this.priority = 2
    }

    async buildView(widgetBuilderParams: IMeditationLogBuilderParams): Promise<void> {
        const activities: ActivityDS[] = widgetBuilderParams.activities
        const meditationItemAdditionPromise: Promise<void>[] = []
        let sleepDataPresent: boolean = false

        // Convert meditation activities to widget items
        activities.forEach((activity: ActivityDS) => {
            if (activity.activityType === "SLEEP") {
                sleepDataPresent = true
                meditationItemAdditionPromise.push(this.addSleepData(widgetBuilderParams.userContext, activity))
            } else if (activity.activityType === "MIND_CLASS") {
                meditationItemAdditionPromise.push(this.addMindClassData(activity, widgetBuilderParams))
            } else if (activity.activityType === "DIY_MEDITATION") {
                meditationItemAdditionPromise.push(this.addDiyMindData(activity, widgetBuilderParams))
            } else if (activity.activityType === "MIND_WORKOUT_ACTIVITY") {
                meditationItemAdditionPromise.push(this.addSelfWorkoutData(activity, widgetBuilderParams))
            } else if (activity.activityType === "GMF_MIND_RECOMMENDATION") {
                meditationItemAdditionPromise.push(this.addRecommendationData(activity, widgetBuilderParams))
            }
        })

        if (!sleepDataPresent && AppUtil.isSleepEnabled(widgetBuilderParams.userContext.userProfile.city)) {
            this.addLogSleepAction(widgetBuilderParams.date, widgetBuilderParams.userContext.userProfile.timezone)
        }

        await Promise.all(meditationItemAdditionPromise)
    }

    private async addSleepData(userContext: UserContext, activity: ActivityDS): Promise<void> {
        try {
            const isReviewed: boolean = _.get(activity, "meta.sleep.sourceType") === "USER" ? true : false
            const sleepActivity: SleepActivity = TimelineUtil.createSleepAtlasActivity(activity, isReviewed)
            this.items.push({
                title: this.sleepDisplayText(sleepActivity.duration),
                calloutText: activity.score + " pts",
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtil.getSleepUrl(sleepActivity, userContext)
                },
                status: "DONE",
                activityType: "SLEEP"
            })
        } catch (e) {
            return
        }
    }

    private addLogSleepAction(date: string, tz: Timezone): void {
        const yesterday: string = TimeUtil.getDaysFrom(tz, date, 2, true)[1]
        const sleepStartTime: number = TimeUtil.getDate(yesterday, 23, 0, tz).getTime()
        const sleepEndTime: number = TimeUtil.getDate(date, 7, 0, tz).getTime()
        if (new Date().getTime() >= sleepEndTime) {
            this.items.push({
                title: "Log your last night sleep time",
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtil.getLogSleepUrl(date, sleepStartTime, sleepEndTime)
                },
                status: "SKIPPED",
                activityType: "SLEEP"
            })
        }
    }

    private sleepDisplayText(durationInSeconds: number): string {
        const minutes = durationInSeconds / 60
        const displayHours = Math.floor(minutes / 60)
        const displayMins = minutes % 60
        if (displayMins === 0) {
            return `${displayHours} hrs Sleep`
        } else {
            return `${displayHours} hrs ${Math.round(displayMins)} min Sleep`
        }
    }

    private async addMindClassData(activity: ActivityDS, widgetBuilderParams: IMeditationLogBuilderParams): Promise<void> {
        try {
            const mindbooking: CultBooking = await widgetBuilderParams.mindFitService.getBookingById(activity.meta.fulfilmentId, activity.userId)
            if (mindbooking.label !== "Cancelled") {
                this.items.push({
                    title: mindbooking.Class.Workout.name,
                    calloutText: activity.score + " pts",
                    action: {
                        actionType: "NAVIGATION",
                        url: ActionUtil.getCultMindClassUrl("MIND", mindbooking.bookingNumber)
                    },
                    status: TimelineUtil.getCultStatus(mindbooking),
                    activityType: "MIND_CLASS"
                })
            }
        } catch (e) {
            return
        }
    }

    private async addDiyMindData(activity: ActivityDS, widgetBuilderParams: IMeditationLogBuilderParams): Promise<void> {
        try {
            const diyPack = <DIYPack>await widgetBuilderParams.catalogueService.getProduct(activity.meta.packId)
            const sessionIndex = diyPack.sessionIds.indexOf(activity.productId) + 1
            this.items.push({
                title: `${diyPack.title} (${sessionIndex} of ${diyPack.sessionIds.length})`,
                calloutText: activity.score + " pts",
                action: {
                    actionType: "NAVIGATION",
                    url: EtherActionUtil.mindDiyPack(activity.meta.packId)
                },
                status: "DONE",
                activityType: "DIY_MEDITATION"
            })
        } catch (e) {
            return
        }
    }

    private async addSelfWorkoutData(activity: ActivityDS, widgetBuilderParams: IMeditationLogBuilderParams): Promise<void> {
        try {
            const tz = widgetBuilderParams.userContext.userProfile.timezone
            if (activity.status === "DELETED") return
            const selfWorkout: MindLoggableItem = await widgetBuilderParams.activityLoggingBusiness.searchByMindWorkoutId(activity.workout.workoutId)
            this.items.push({
                title: selfWorkout.name,
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtil.addLoggableActivityUrl("MEDITATE", {
                        slotId: "ALLDAY",
                        date: TimeUtil.formatDateInTimeZone(tz, new Date(activity.workout.activityTime)),
                    })
                },
                status: activity.status,
                activityType: "MIND_WORKOUT_ACTIVITY"
            })
        } catch (e) {
            return
        }
    }

    private async addRecommendationData(activity: ActivityDS, widgetBuilderParams: IMeditationLogBuilderParams): Promise<void> {
        try {
            this.items.push({
                title: activity.activityName,
                status: activity.status,
                activityType: activity.activityType,
                action: undefined
            })
        } catch (e) {
            return
        }
    }
}
