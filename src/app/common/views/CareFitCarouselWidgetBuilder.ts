import * as _ from "lodash"
import { ActivityType } from "@curefit/product-common"
import { ActivityState } from "@curefit/logging-common"
import { CustomerIssueType } from "@curefit/issue-common"
import { User } from "@curefit/user-common"
import { CarouselWidget, ICarouselWidget, ImageCardCarouselView, TextListCarouselView } from "./CarouselWidget"
import { ITodoActivityWidget } from "./TodoActivityWidget"
import { UserContext } from "@curefit/userinfo-common"
import { Logger } from "@curefit/base"
import { IHealthfaceService, RecommendedTimelineActivity, TimelineActivity } from "@curefit/albus-client"
import { TimeUtil } from "@curefit/util-common"
import { Action, ActionList } from "./WidgetView"
import { ActionUtil } from "../../util/ActionUtil"
import IProductBusiness from "../../product/IProductBusiness"
import TimelineUtil from "../../util/TimelineUtil"

export interface ICareFitWidgetBuilderParams {
    timelineActivity?: TimelineActivity,
    recommendedTimelineActivity?: RecommendedTimelineActivity,
    issuesMap: Map<string, CustomerIssueType[]>,
    healthfaceService: IHealthfaceService,
    productBusiness: IProductBusiness,
    user: User,
    userContext: UserContext,
    logger: Logger,
    isRecommendedActivity: boolean
}

export class CareFitCarouselWidgetBuilder {
    async buildCareFitWidget(widgetBuilderParams: ICareFitWidgetBuilderParams): Promise<ICarouselWidget> {
        let careFitWidget: ICarouselWidget
        if (widgetBuilderParams.isRecommendedActivity) {
            careFitWidget = new CareFitRecommendedActivityCarouselWidget()
            careFitWidget = await (<CareFitRecommendedActivityCarouselWidget>careFitWidget).buildView(widgetBuilderParams)
        } else {
            careFitWidget = new CareFitActivityCarouselWidget()
            careFitWidget = await (<CareFitActivityCarouselWidget>careFitWidget).buildView(widgetBuilderParams)
        }
        return careFitWidget
    }
}

export class CareFitActivityCarouselWidget extends CarouselWidget implements ITodoActivityWidget {
    timestamp: number
    status: ActivityState
    activityType: ActivityType
    constructor() {
        super()
        this.widgetType = "TODO_ACTIVITY_WIDGET"
    }

    async buildView(widgetBuilderParams: ICareFitWidgetBuilderParams): Promise<CareFitActivityCarouselWidget> {
        const careFitTimelineActivity: TimelineActivity = widgetBuilderParams.timelineActivity
        const tz = widgetBuilderParams.userContext.userProfile.timezone
        if (_.isNil(careFitTimelineActivity)) return undefined
        try {
            // Assign title
            this.title = careFitTimelineActivity.title

            // Assign text items (start time)
            const parsedDate: Date = TimeUtil.parseDateFromEpoch(careFitTimelineActivity.timestamp)
            const startTime: string = TimeUtil.get12HRTimeFormat(parsedDate, tz)
            this.leftCalloutText = startTime

            // Assign timestamp
            this.timestamp = careFitTimelineActivity.timestamp

            // Assign status
            const isMissed: boolean = !_.isEmpty(careFitTimelineActivity.bookingInfo.consultationOrderResponse) && careFitTimelineActivity.bookingInfo.consultationOrderResponse.status === "MISSED"
            this.status = TimelineUtil.getCareStatus(careFitTimelineActivity, isMissed)

            // Assign activity type
            this.activityType = careFitTimelineActivity.carefitActivity

            // Create the carefit activity card
            const careFitCard: ImageCardCarouselView = await this.getCardForCareActivity(widgetBuilderParams)
            this.cards.push(careFitCard)
            return this
        } catch (e) {
            const loggingParams = {
                activityType: careFitTimelineActivity.carefitActivity,
                subCategory: careFitTimelineActivity.subCategory,
                title: careFitTimelineActivity.title,
                timestamp: careFitTimelineActivity.timestamp
            }
            widgetBuilderParams.logger.error(e.stack)
            widgetBuilderParams.logger.error("CareFit widget building failed for params :: " + JSON.stringify(loggingParams) + " User ID :: " + widgetBuilderParams.userContext.userProfile.userId)
            return undefined
        }
    }

    private async getCardForCareActivity(widgetBuilderParams: ICareFitWidgetBuilderParams): Promise<ImageCardCarouselView> {
        const careFitTimelineActivity: TimelineActivity = widgetBuilderParams.timelineActivity
        const careFitCard: ImageCardCarouselView = {
            viewType: "BORDERLESS",
            styleType: "WIDE",
            title: "",
            images: [],
            action: {
                actionType: "NAVIGATION",
                url: ""
            },
            quickActions: []
        }

        // Assign image URL
        if (!_.isNil(careFitTimelineActivity.timelineImageUrl)) {
            careFitCard.images.push(careFitTimelineActivity.timelineImageUrl)
        }

        // Generate action and title
        careFitCard.title = careFitTimelineActivity.description
        careFitCard.action = await ActionUtil.getCareActivityAction(widgetBuilderParams.userContext, careFitTimelineActivity, widgetBuilderParams.healthfaceService, widgetBuilderParams.logger)

        // Generate quick actions
        const possibleActions: Action[] = this.getPossibleActionsForCareFitActivity(widgetBuilderParams)
        const primaryAction: Action = possibleActions[0]
        const primaryActionList: ActionList = _.isNil(primaryAction) ? undefined : _.assign({ actions: [] }, primaryAction)
        const secondaryActions: Action[] = possibleActions.slice(1)
        const secondaryActionList: ActionList = _.isEmpty(secondaryActions) ? undefined : { actionType: "ACTION_LIST", actions: secondaryActions, icon: "MANAGE" }
        if (!_.isNil(primaryActionList)) {
            careFitCard.quickActions.push(primaryActionList)
        }
        if (!_.isNil(secondaryActionList)) {
            careFitCard.quickActions.push(secondaryActionList)
        }

        return careFitCard
    }

    private getPossibleActionsForCareFitActivity(widgetBuilderParams: ICareFitWidgetBuilderParams): Action[] {
        const possibleActions: Action[] = []
        const careFitTimelineActivity: TimelineActivity = widgetBuilderParams.timelineActivity
        const subCategoryCode = _.get(careFitTimelineActivity, "bookingInfo.booking.subCategoryCode", undefined)
        if (subCategoryCode === "CF_INCENTRE_CONSULTATION") {
            possibleActions.push(ActionUtil.centerNavigationAction(careFitTimelineActivity.bookingInfo.consultationOrderResponse.center.placeUrl))
        }
        const otherActions: Action[] = ActionUtil.getCareQuickActions(widgetBuilderParams.userContext, careFitTimelineActivity, widgetBuilderParams.issuesMap, widgetBuilderParams.user, widgetBuilderParams.productBusiness, widgetBuilderParams.logger, false)
        if (!_.isEmpty(otherActions)) {
            const smartActions: Action[] = TimelineUtil.createSmartManageOptionActions(otherActions)
            smartActions.forEach(action => {
                if (action.actionType === "ACTION_LIST") {
                    const actionList: ActionList = <ActionList>action
                    possibleActions.push(...actionList.actions)
                } else {
                    possibleActions.push(action)
                }
            })
        }
        return possibleActions
    }
}

export class CareFitRecommendedActivityCarouselWidget extends CarouselWidget implements ITodoActivityWidget {
    timestamp: number
    status: ActivityState
    activityType: ActivityType
    widgetHash: string
    constructor() {
        super()
        this.widgetType = "TODO_ACTIVITY_WIDGET"
    }

    async buildView(widgetBuilderParams: ICareFitWidgetBuilderParams): Promise<CareFitRecommendedActivityCarouselWidget> {
        const careFitRecommendedActivity: RecommendedTimelineActivity = widgetBuilderParams.recommendedTimelineActivity
        if (_.isNil(careFitRecommendedActivity)) return undefined
        try {
            // Assign title
            this.title = careFitRecommendedActivity.title

            // Assign text items (start time)
            this.leftCalloutText = "DUE"

            // Assign status
            this.status = "TODO"

            // Assign activity type
            this.activityType = careFitRecommendedActivity.careFitRecommendedActivityType

            // Assign widget hash
            this.widgetHash = this.title

            // Create the carefit activity card
            const careFitCard: TextListCarouselView = await this.getCardForCareActivity(widgetBuilderParams)
            this.cards.push(careFitCard)
            return this
        } catch (e) {
            const loggingParams = {
                title: careFitRecommendedActivity.title,
                careFitRecommendedActivityType: careFitRecommendedActivity.careFitRecommendedActivityType
            }
            widgetBuilderParams.logger.error(e.stack)
            widgetBuilderParams.logger.error("CareFit Recommended widget building failed for params :: " + JSON.stringify(loggingParams) + " User ID :: " + widgetBuilderParams.userContext.userProfile.userId)
            return undefined
        }
    }

    private async getCardForCareActivity(widgetBuilderParams: ICareFitWidgetBuilderParams): Promise<TextListCarouselView> {
        const careFitRecommendedActivity: RecommendedTimelineActivity = widgetBuilderParams.recommendedTimelineActivity
        const careFitCard: TextListCarouselView = {
            viewType: "BORDERLESS",
            styleType: "WIDE",
            title: "",
            textList: [],
            action: {
                actionType: "NAVIGATION",
                url: ""
            },
            quickActions: []
        }

        // Generate action and title
        careFitCard.title = careFitRecommendedActivity.description
        careFitCard.action = ActionUtil.getCareRecommendedActivityAction(widgetBuilderParams.userContext, careFitRecommendedActivity, widgetBuilderParams.logger)

        // Generate text items
        if (!_.isEmpty(careFitRecommendedActivity.subTaskList)) {
            _.each(careFitRecommendedActivity.subTaskList, (subTask) => {
                careFitCard.textList.push({
                    descriptionText: subTask.title
                })
            })
        }

        // Generate quick actions
        const possibleActions: Action[] = ActionUtil.getCareRecommendedActivityQuickActions(widgetBuilderParams.userContext, careFitRecommendedActivity, widgetBuilderParams.issuesMap, widgetBuilderParams.productBusiness, widgetBuilderParams.logger)
        const primaryAction: Action = possibleActions[0]
        const primaryActionList: ActionList = _.isNil(primaryAction) ? undefined : _.assign({ actions: [] }, primaryAction)
        const secondaryActions: Action[] = possibleActions.slice(1)
        const secondaryActionList: ActionList = _.isEmpty(secondaryActions) ? undefined : { actionType: "ACTION_LIST", actions: secondaryActions, icon: "MANAGE" }
        if (!_.isNil(primaryActionList)) {
            careFitCard.quickActions.push(primaryActionList)
        }
        if (!_.isNil(secondaryActionList)) {
            careFitCard.quickActions.push(secondaryActionList)
        }

        return careFitCard
    }
}
