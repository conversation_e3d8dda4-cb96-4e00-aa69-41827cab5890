import * as _ from "lodash"
import { Action, ActionList } from "./WidgetView"
import { ActivityState } from "@curefit/logging-common"
import { ActivityType } from "@curefit/product-common"
import { BaseCarouselCardView, CarouselWidget, HeaderTextCarouselView, ImageCardCarouselView } from "./CarouselWidget"
import { ActionUtil } from "../../util/ActionUtil"
import { SessionInfo, UserContext } from "@curefit/userinfo-common"
import { ProductRecommendation, TodoPlanActivityView } from "@curefit/gmf-common"
import { FitnessLoggableItem } from "@curefit/maverick-common"
import { UrlPathBuilder } from "@curefit/product-common"
import { CultDocument, CultWorkout } from "@curefit/cult-common"
import { IGMSWidgetBuilderParams } from "./GMSCarouselWidgetBuilder"
import { IMissedMeta, ITodoActivityWidget } from "./TodoActivityWidget"
import { TimeUtil } from "@curefit/util-common"
import { DIYFitnessPackExtended, DIYProduct } from "@curefit/diy-common"
import AppUtil from "../../util/AppUtil"

export class CultWorkoutRecommendationCarouselWidget extends CarouselWidget implements ITodoActivityWidget {
    timestamp: number
    status: ActivityState
    activityType: ActivityType
    missedMeta: IMissedMeta
    constructor() {
        super()
        this.widgetType = "TODO_ACTIVITY_WIDGET"
    }

    async buildView(widgetBuilderParams: IGMSWidgetBuilderParams): Promise<CultWorkoutRecommendationCarouselWidget> {
        const recommendedItem: TodoPlanActivityView = widgetBuilderParams.recommendedItem
        const userContext: UserContext = widgetBuilderParams.userContext
        const timezone = userContext.userProfile.timezone
        try {
            // Assign title
            this.title = "Workout - " + recommendedItem.name
            this.subTitle = "Pick any one"
            // Assign text items (Time of class)
            this.leftCalloutText = "DUE"
            const recommendationTime: Date = TimeUtil.getDefaultMomentForDateString(recommendedItem.todoDate, timezone).add(1, "day").startOf("day").subtract(1, "minute").toDate()
            this.timestamp = recommendationTime.getTime()

            // Assign status
            this.status = "TODO"

            // Assign activity type
            this.activityType = "CULT_RECOMMENDATION"

            // Create the cult booking cards
            const cardPromises: Promise<BaseCarouselCardView>[] = []
            recommendedItem.recommendations.forEach((item: ProductRecommendation) => {
                cardPromises.push(this.getCardForRecommendation(item, widgetBuilderParams))
            })
            for (const cardPromise of cardPromises) {
                let carouselCard: BaseCarouselCardView
                try {
                    carouselCard = await cardPromise
                } catch (e) {
                    // Do nothing
                }
                if (!_.isNil(carouselCard)) {
                    this.cards.push(carouselCard)
                }
            }
            if (_.isEmpty(this.cards)) {
                return undefined
            }

            // Assign missedMeta
            const recommendationId: string = (<any>recommendedItem)._id // HACK -- FIX later when GMS fixes reco Id
            this.missedMeta = {
                recommendationId: recommendationId,
                activityType: "GMF_FITNESS_RECOMMENDATION",
                description: recommendedItem.name,
                activityTime: this.timestamp
            }

            return this
        } catch (e) {
            const loggingParams = {
                prescriptionItemId: recommendedItem.prescriptionItemId,
                name: recommendedItem.name
            }
            widgetBuilderParams.logger.error("Cult recommendation widget building failed for recommendations :: " + JSON.stringify(loggingParams) + " User ID :: " + userContext.userProfile.userId)
            return undefined
        }
    }

    private async getCardForRecommendation(recommendedItem: ProductRecommendation, widgetBuilderParams: IGMSWidgetBuilderParams): Promise<BaseCarouselCardView> {
        if (_.isEmpty(recommendedItem.productIds)) return undefined

        if (recommendedItem.catalogueProductType === "FITNESS") {
            return this.getCultWorkoutCard(recommendedItem, widgetBuilderParams)
        } else if (recommendedItem.catalogueProductType === "FITNESS_RECOMMENDATION") {
            return this.getSelfWorkoutCard(recommendedItem, widgetBuilderParams)
        } else if (recommendedItem.catalogueProductType === "DIY_FITNESS") {
            return this.getDIYWorkoutCard(recommendedItem, widgetBuilderParams)
        }

        return undefined
    }

    private async getCultWorkoutCard(recommendedItem: ProductRecommendation, widgetBuilderParams: IGMSWidgetBuilderParams): Promise<ImageCardCarouselView> {
        const imageCard: ImageCardCarouselView = {
            viewType: "BORDERED",
            styleType: "REGULAR",
            title: "",
            images: [],
            action: {
                actionType: "NAVIGATION",
                url: ""
            },
            quickActions: []
        }

        const cultWorkoutPromiseMap: Promise<Map<string, CultWorkout>> = widgetBuilderParams.cultWorkoutPromiseMap
        const cultWorkoutMap: Map<string, CultWorkout> = await cultWorkoutPromiseMap
        const productId: string = recommendedItem.productIds[0]
        if (_.isNil(productId)) return undefined

        const cultWorkout: CultWorkout = cultWorkoutMap.get(productId)
        if (_.isNil(cultWorkout)) return undefined

        const cultWorkoutList: CultWorkout[] = []
        _.each(recommendedItem.productIds, (productId: string) => {
            const cultWorkout: CultWorkout = cultWorkoutMap.get(productId)
            if (!_.isNil(cultWorkout)) {
                cultWorkoutList.push(cultWorkout)
            }
        })

        // Assign image
        let imageUrl: string
        const imageDetails = recommendedItem.meta && recommendedItem.meta.imageDetails
        if (imageDetails && imageDetails.thumbnail) {
            const thumbnails = imageDetails.thumbnail
            imageUrl = thumbnails.mobileUrl
        } else {
            const document: CultDocument = _.find(cultWorkout.documents, doc => doc.tagID === 11)
            imageUrl = !_.isNil(document) ? "/" + document.URL : undefined
        }

        if (!_.isNil(imageUrl)) imageCard.images.push(imageUrl)


        // Assign action url
        const userContext: UserContext = widgetBuilderParams.userContext
        const cultCenterId: string = userContext.sessionInfo.sessionData ? userContext.sessionInfo.sessionData.cultCenterId : undefined
        const isNewClassBookingSupported = AppUtil.isNewClassBookingSuppoted(userContext, (await userContext.userPromise).isInternalUser)
        const bookAction: Action = ActionUtil.getBookClassActionMultipleWorkouts(cultWorkoutList, "FITNESS", isNewClassBookingSupported, "timelineV4", cultCenterId)
        bookAction.title = "BOOK"
        imageCard.action.url = bookAction.url

        // Generate quick actions
        const possibleActions: Action[] = []
        possibleActions.push(bookAction)
        const primaryAction: Action = possibleActions[0]
        const primaryActionList: ActionList = _.isNil(primaryAction) ? undefined : _.assign({ actions: [] }, primaryAction)
        const secondaryActions: Action[] = possibleActions.slice(1)
        const secondaryActionList: ActionList = _.isEmpty(secondaryActions) ? undefined : { actionType: "ACTION_LIST", actions: secondaryActions, icon: "MANAGE" }
        if (!_.isNil(primaryActionList)) {
            imageCard.quickActions.push(primaryActionList)
        }
        if (!_.isNil(secondaryActionList)) {
            imageCard.quickActions.push(secondaryActionList)
        }

        return imageCard
    }

    private async getSelfWorkoutCard(recommendedItem: ProductRecommendation, widgetBuilderParams: IGMSWidgetBuilderParams): Promise<HeaderTextCarouselView> {
        const tz = widgetBuilderParams.userContext.userProfile.timezone
        const headerCard: HeaderTextCarouselView = {
            viewType: "BORDERED",
            styleType: "REGULAR",
            title: "",
            header: {
                title: ""
            },
            action: {
                actionType: "NAVIGATION",
                url: ""
            },
            quickActions: []
        }

        const productId: string = recommendedItem.productIds[0]
        if (_.isNil(productId)) return undefined

        const selfWorkout: FitnessLoggableItem = await widgetBuilderParams.activityLoggingBusiness.searchByWorkoutId(productId)
        if (_.isNil(selfWorkout)) return undefined

        // Assign header text
        headerCard.header.title = selfWorkout.name

        // Action URL -- nothing as of now

        const workoutDate: Date = new Date(widgetBuilderParams.recommendedItem.todoDate)
        const date: string = TimeUtil.formatDateInTimeZone(tz, workoutDate)
        const todayDate: string = TimeUtil.todaysDate(tz)

        // Quick Actions
        const logSelfWorkoutAction: Action = {
            actionType: "NAVIGATION",
            url: ActionUtil.addLoggableActivityUrl("FITNESS", {
                slotId: "ALLDAY",
                date: TimeUtil.formatDateInTimeZone(tz, new Date(widgetBuilderParams.recommendedItem.todoDate)),
                recommendedSelfWorkoutId: productId,
                isExpanded: true
            }),
            title: "LOG",
            icon: "LOG",
            isEnabled: date <= todayDate
        }
        headerCard.quickActions.push(_.assign({ actions: [] }, logSelfWorkoutAction))

        return headerCard
    }

    private async getDIYWorkoutCard(recommendedItem: ProductRecommendation, widgetBuilderParams: IGMSWidgetBuilderParams): Promise<ImageCardCarouselView> {
        const imageCard: ImageCardCarouselView = {
            viewType: "BORDERED",
            styleType: "REGULAR",
            title: "",
            images: [],
            action: {
                actionType: "NAVIGATION",
                url: ""
            },
            quickActions: []
        }

        const productId: string = recommendedItem.productIds[0]
        if (_.isNil(productId)) return undefined

        const packId: string = _.get(recommendedItem.meta, "packId")
        if (_.isNil(packId)) return undefined

        const diyWorkout: DIYProduct = <DIYProduct>(await widgetBuilderParams.catalogueService.getProduct(productId))
        if (_.isNil(diyWorkout)) return undefined

        const userId = widgetBuilderParams.userContext.userProfile.userId
        const diyPacks: DIYFitnessPackExtended[] = await widgetBuilderParams.diyService.getDIYFitnessPacksForIds(userId, [packId])
        if (_.isEmpty(diyPacks)) return undefined

        const diyPack = diyPacks[0]
        // Assign image
        imageCard.images.push(UrlPathBuilder.prefixSlash(diyPack.imageDetails.heroImage))

        // Action URL -- nothing as of now

        // Quick Actions
        const sessionInfo: SessionInfo = widgetBuilderParams.userContext.sessionInfo
        const possibleActions: Action[] = ActionUtil.diyActionsFromSessionDetailV2(diyWorkout, diyPack, sessionInfo)
        const primaryAction: Action = possibleActions[0]
        const primaryActionList: ActionList = _.isNil(primaryAction) ? undefined : _.assign({ actions: [] }, primaryAction)
        const secondaryActions: Action[] = possibleActions.slice(1)
        imageCard.action = !_.isEmpty(secondaryActions) ? secondaryActions[0] : undefined
        if (!_.isNil(primaryActionList)) {
            imageCard.quickActions.push(primaryActionList)
        }
        if (AppUtil.isLargePlayIconSupported(sessionInfo.osName, sessionInfo.appVersion)) {
            imageCard.cardActions = secondaryActions
        } else {
            const secondaryActionList: ActionList = _.isEmpty(secondaryActions) ? undefined : { actionType: "ACTION_LIST", actions: secondaryActions, icon: "MANAGE" }
            if (!_.isNil(secondaryActionList)) {
                imageCard.quickActions.push(secondaryActionList)
            }
        }

        return imageCard
    }
}
