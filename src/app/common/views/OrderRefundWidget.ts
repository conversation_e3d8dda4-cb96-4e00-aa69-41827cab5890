import * as _ from "lodash"
import { WidgetView } from "./WidgetView"
import { PaymentChannel, RefundDetailsResponse, IRefundDetailsPgMeta } from "@curefit/payment-common"
import { IPaymentClient } from "@curefit/payment-client"
import { WidgetType } from "@curefit/apps-common"
import { Logger } from "@curefit/base"
import { UserContext } from "@curefit/vm-models"
import { Timezone, TimeUtil } from "@curefit/util-common"

export interface IOrderRefundWidgetParams {
    userContext: UserContext
    orderId: string
    paymentId: string
    paymentChannel: PaymentChannel
    refundDetailsPromise: Promise<RefundDetailsResponse[]>
    logger: Logger
}

export interface ITransactionListView {
    title: string
    subText: string
    value: string
    copyingEnabled: boolean
}

export interface IOrderRefundWidget extends WidgetView {
    title: string
    status: string
    refundDate: string
    listTitle: string
    transactionsList: ITransactionListView[]
}

export class OrderRefundWidgetBuilder {
    public async getOrderRefundWidgetList(orderRefundWidgetParams: IOrderRefundWidgetParams): Promise<IOrderRefundWidget[]> {
        const result: IOrderRefundWidget[] = []
        let refundDetails: RefundDetailsResponse[]
        try {
            refundDetails = await orderRefundWidgetParams.refundDetailsPromise
        } catch (err) {
            orderRefundWidgetParams.logger.error(`OrderRefundWidgetBuilder orderId: ${orderRefundWidgetParams.orderId} || err from paymentClient.getRefundDetails`, err)
        }
        orderRefundWidgetParams.logger.info(`OrderRefundWidgetBuilder orderId: ${orderRefundWidgetParams.orderId} || response from payment service: ${JSON.stringify(refundDetails, null, 2)}`)
        if (_.isEmpty(refundDetails)) return undefined

        refundDetails.forEach((refundDetail: RefundDetailsResponse) => {
            const orderRefundWidget: IOrderRefundWidget = new OrderRefundWidget().buildView(orderRefundWidgetParams.userContext, refundDetail)
            if (!_.isNil(orderRefundWidget)) {
                result.push(orderRefundWidget)
            }
        })
        orderRefundWidgetParams.logger.info(`OrderRefundWidgetBuilder orderId: ${orderRefundWidgetParams.orderId} || resultant widgets: ${JSON.stringify(result, null, 2)}`)

        if (_.isEmpty(result)) {
            return undefined
        } else {
            return result
        }
    }
}

export class OrderRefundWidget implements IOrderRefundWidget {
    title: string
    status: string
    refundDate: string
    listTitle: string
    transactionsList: ITransactionListView[]
    widgetType: WidgetType = "ORDER_REFUND_WIDGET"
    constructor() {
        this.transactionsList = []
    }


    buildView(userContext: UserContext, refundDetails: RefundDetailsResponse): IOrderRefundWidget {
        try {
            if (_.isEmpty(refundDetails)) {
                return undefined
            }

            // Assign title
            this.title = "Refund Status"

            // Assign Status
            this.assignStatus(refundDetails)

            // Assign Refund Date
            this.assignRefundDateText(userContext, refundDetails)

            // Assign list title
            this.listTitle = "Refund To"

            // Assign transaction list
            this.assignTransactionList(refundDetails)

            return this
        } catch (e) {
            return undefined
        }
    }

    private assignStatus(refundDetails: RefundDetailsResponse): void {
        switch (refundDetails.status) {
            case "failure": {
                this.status = "REFUND FAILED"
                break
            }
            case "pending": {
                this.status = "REFUND IN PROGRESS"
                break
            }
            case "success": {
                this.status = "REFUND PROCESSED"
                break
            }
        }
    }

    private assignRefundDateText(userContext: UserContext, refundDetails: RefundDetailsResponse): void {
        if (!_.isFinite(refundDetails.refundStatusEpoch)) return
        const timezone: Timezone = <Timezone>_.get(userContext, "userProfile.timezone", TimeUtil.IST_TIMEZONE)
        const dateString: string = TimeUtil.formatEpochInTimeZone(timezone, refundDetails.refundStatusEpoch, "Do MMM, YYYY")
        this.refundDate = `(on ${dateString})`
    }

    private assignTransactionList(refundDetails: RefundDetailsResponse): void {
        const fitcashPresent: boolean = refundDetails.fitcashAmount > 0 ? true : false
        if (fitcashPresent) {
            this.transactionsList.push({
                title: "Fitcash",
                subText: "",
                value: (refundDetails.fitcashAmount / 100).toFixed(2),
                copyingEnabled: false
            })
        }
        if (refundDetails.pgAmount > 0) {
            this.transactionsList.push({
                title: refundDetails.refundSourceDisplayText,
                subText: fitcashPresent ? "" : "(approx. 5-7 working days)",
                value: (refundDetails.pgAmount / 100).toFixed(2),
                copyingEnabled: false
            })
        }
        if (fitcashPresent) {
            this.transactionsList.push({
                title: "Total Refundable Amount",
                subText: "(approx. 5-7 working days)",
                value: ((refundDetails.fitcashAmount + refundDetails.pgAmount) / 100).toFixed(2),
                copyingEnabled: false
            })
        }
        if (!_.isNil(refundDetails.refundDetailsPgMeta)) {
            refundDetails.refundDetailsPgMeta.forEach((refundPgMeta: IRefundDetailsPgMeta) => {
                this.transactionsList.push({
                    title: refundPgMeta.key,
                    subText: "",
                    value: refundPgMeta.value,
                    copyingEnabled: true
                })
            })
        }
    }
}
