import { Header, WidgetType, WidgetView } from "./WidgetView"
import { MembershipReward, RewardState } from "@curefit/cult-common"
import { pluralizeStringIfRequired, TimeUtil } from "@curefit/util-common"
import { UserContext } from "@curefit/userinfo-common"

export interface ChallengeViewItem {
    viewType: "SINGLE" | "MULTIPLE"
    title: string
    subTitle?: string
    icon?: string
    children?: {
        title: string
        subTitle: string
        heading: string
        subTitleColor?: string
    }[]
}

export class CultConsistencyChallengeWidget implements WidgetView {
    widgetType: WidgetType = "CULT__MEMBERSHIP_CONSISTENCY_WIDGET"
    header: Header
    viewItems: ChallengeViewItem[]
    seeMoreColor?: string
    constructor(reward: MembershipReward, userContext: UserContext) {
        const tz = userContext.userProfile.timezone
        const startDate = TimeUtil.formatDateStringInTimeZone(reward.rewardStartDate, tz, "Do MMM")
        const endDate = TimeUtil.formatDateStringInTimeZone(reward.rewardEndDate, tz, "Do MMM")
        const today = TimeUtil.todaysDateWithTimezone(tz)
        const daysRemaining = TimeUtil.diffInDays(tz, today, reward.rewardEndDate)
        this.header = {
            title: "Consistency Challenge",
            subTitle: `${startDate} - ${endDate}`,
            seeMoreText: reward.state === "EXPIRED" ? "Expired" : reward.state === "ACTIVATED" ? "Completed" : `${daysRemaining} ${pluralizeStringIfRequired("day", daysRemaining)} left`,
        }
        this.seeMoreColor = reward.state === "EXPIRED" ? "#ff3278" : reward.state === "ACTIVATED" ? "#23cb75" : "#000000"
        this.viewItems = [
            {
                viewType: "SINGLE",
                title: "Goal",
                subTitle: `${reward.requiredCount} (or more) classes for ${reward.requiredStreak} consecutive weeks`,
                icon: "/image/icons/cultChallenge/trophy.png"
            },
            {
                viewType: "SINGLE",
                title: "Reward",
                subTitle: `Get a Cult T-shirt on completing the challenge`,
                icon: "/image/icons/cultChallenge/reward.png"
            },
            {
                viewType: "MULTIPLE",
                title: "Your report card",
                icon: "/image/icons/cultChallenge/report.png",
                children: [
                    {
                        heading: `${reward.currentStreak}`,
                        title: "Current streak (weeks)",
                        subTitle: this.getConsistencyChallengeStreakDescription(reward.state, endDate, reward.requiredCount, reward.currentStreak, reward.requiredStreak),
                        subTitleColor: reward.state === "ACTIVATED" && "#23cb75"
                    },
                    {
                        heading: `${reward.currentCount}`,
                        title: "Classes this week (mon - sun)",
                        subTitle: this.getConsistencyChallengeClassesDescription(reward.state, endDate, reward.currentCount, reward.requiredCount),
                        subTitleColor: reward.state === "ACTIVATED" && "#23cb75"
                    }
                ]
            }
        ]
        if (reward.state === "ACTIVATED") {
            this.viewItems.push({
                viewType: "SINGLE",
                title: "Please refer to the email sent by us to share your delivery details",
                icon: "/image/icons/cultChallenge/white.png"
            })
        }
    }

    private getConsistencyChallengeStreakDescription(state: RewardState, expiryDate: string, requiredCount: number, currentStreak: number, requiredStreak: number): string {
        if (state === "EXPIRED") {
            return `Challenge expired on ${expiryDate}`
        }
        else if (state === "ACTIVATED") {
            return "Streak target completed"
        }
        if (currentStreak === 0) {
            return `${requiredCount} (or more) classes this week for streak 1`
        }
        else if (currentStreak > 0 && currentStreak < requiredStreak) {
            return `${currentStreak} done, ${requiredStreak - currentStreak} more to go`
        }

    }

    private getConsistencyChallengeClassesDescription(state: RewardState, expiryDate: string, currentCount: number, requiredCount: number): string {
        if (state === "EXPIRED") {
            return `Challenge expired on ${expiryDate}`
        }
        else if (state === "ACTIVATED") {
            return "Weekly target completed"
        }
        if (currentCount === 0) {
            return `No classes done in this week yet`
        }
        else if (currentCount > 0 && currentCount < requiredCount) {
            return `${currentCount} done, ${requiredCount - currentCount} more to go`
        }
        else if (currentCount >= requiredCount) {
            return "Weekly target completed"
        }
    }
}
