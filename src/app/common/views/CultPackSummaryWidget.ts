import * as _ from "lodash"
import { ManageOptionPayload, PackProgress, WidgetType, WidgetView } from "./WidgetView"
import { CustomerIssueType } from "@curefit/issue-common"
import { AccessLevel, CultCenter } from "@curefit/cult-common"
import { MindPack } from "@curefit/mind-common"
import { ImageCategoryType as ImageCategory, ProductPrice, ProductType, UrlPathBuilder } from "@curefit/product-common"
import { CultPackProgress } from "@curefit/cult-client"
import { UserContext } from "@curefit/userinfo-common"
import { CultProductPricesResponse, PackOffersResponse } from "@curefit/offer-common"
import IProductBusiness from "../../product/IProductBusiness"
import { CultMembershipMetadata } from "@curefit/order-common"
import { TimeUtil } from "@curefit/util-common"
import CultUtil, { CultPackProgressV2, SELECT_IMAGE_URL } from "../../util/CultUtil"
import { CultPackStartDateOptions } from "../../pack/CultPackDetailViewBuilder"
import { MoneyBackOfferDetail } from "../../cult/CultBusiness"
import { User } from "@curefit/user-common"
import AppUtil from "../../util/AppUtil"
import { Membership, MembershipType } from "@curefit/membership-commons"
import { MembershipTransferable } from "../../cult/cultpackpage/CultPackCommonViewBuilder"
import serviceInterfaces, { CFServiceInterfaces } from "../../page/vm/ServiceInterfaces"
import { OfferUtil } from "@curefit/base-utils"
import {
    OfferUtil as OfferUtilService
} from "@curefit/offer-service-client"
import { CenterResponse } from "@curefit/center-service-common"
import PlayUtil from "../../util/PlayUtil"
import { MembershipItemUtil } from "../../util/MembershipItemUtil"
import { AugmentedOfflineFitnessPack, OfflineFitnessPack } from "@curefit/pack-management-service-common"
import CatalogueServiceUtilities from "../../util/CatalogueServiceUtilities"
import { ExtraChargeType } from "@curefit/pack-management-service-common"

export interface ICultPackSummaryWidgetBuilderParams {
    userContext: UserContext
    isBuyFlow: boolean
    isPreRegPack: boolean
    packInfo: AugmentedOfflineFitnessPack
    productBusiness: IProductBusiness
    preAdvancePaid: boolean
    canChangeCenter: boolean
    startDateOptions: CultPackStartDateOptions
    issuesMapPromise?: Promise<Map<string, CustomerIssueType[]>>
    preferredCenter?: CultCenter | CenterResponse
    cultPackProgress?: CultPackProgressV2
    membership?: Membership
    offersPromise?: Promise<PackOffersResponse>
    offersV3Promise?: Promise<CultProductPricesResponse>
    moneyBackOfferPromise?: Promise<MoneyBackOfferDetail>
    userPromise?: Promise<User>
    membershipTransferable?: MembershipTransferable
    serviceInterfaces: CFServiceInterfaces
    offerUtil: OfferUtilService
    preferredCenterId?: number
}

export const generateTaxAndFeeBreakup = ({gstObject, extraChargeFee}: Record<string, any>) => ([
    {
        title: `${"CGST + SGST" + " @ "}${gstObject.rate}%`,
        value: (gstObject?.amount)?.toFixed(2),
    },
    {
        title: "Platform Fee", // unit
        value: (extraChargeFee?.unitPrice)?.toFixed(2)
    },
    {
        title: "GST on Platform fee ", // taxAmount
        value: (extraChargeFee?.taxAmount)?.toFixed(2)
    },
    {
        title: "Total",
        value: (gstObject?.amount + extraChargeFee?.totalFee)?.toFixed(2), // totalFee + GST
        isTotal: true
    }
])
export interface ICultPackSummaryWidgetBuilderParamsV2 {
    userContext: UserContext
    isBuyFlow: boolean
    // isPreRegPack: boolean
    packInfo: AugmentedOfflineFitnessPack
    productBusiness: IProductBusiness
    preAdvancePaid: boolean
    canChangeCenter: boolean
    startDateOptions: CultPackStartDateOptions
    issuesMapPromise?: Promise<Map<string, CustomerIssueType[]>>
    preferredCenter?: CultCenter | CenterResponse
    cultPackProgress?: CultPackProgressV2
    membership?: Membership
    offersPromise?: Promise<PackOffersResponse>
    offersV3Promise?: Promise<CultProductPricesResponse>
    moneyBackOfferPromise?: Promise<MoneyBackOfferDetail>
    userPromise?: Promise<User>
    membershipTransferable?: MembershipTransferable
    serviceInterfaces: CFServiceInterfaces
    offerUtil: OfferUtilService
    preferredCenterId?: number

}

export interface ICultPackSummaryWidget extends WidgetView {
    title: string
    image: string
    // packId: number
    productType: ProductType
    productId: string
    duration: number
    price: ProductPrice
    packProgress?: PackProgress
    startDateOptions?: string[]
    remainingNoShowCount?: number
    preferredCenterName?: string
    preferredCenterId?: number
    canChangeCenter?: boolean
    clientMetadata?: CultMembershipMetadata
    meta?: any
    offerId?: string
    offerIds?: string[]
    manageOptions?: { displayText: string, options: ManageOptionPayload[] }
    priceBreakup?: Record<string, any>
}

export class CultPackSummaryWidget implements ICultPackSummaryWidget {
    widgetType: WidgetType = "FITNESS_PACK_SUMMARY"
    title: string
    subText?: string
    image: string
    productType: ProductType
    productId: string
    duration: number
    price: ProductPrice
    packProgress?: PackProgress
    startDateOptions?: string[]
    remainingNoShowCount?: number
    preferredCenterName?: string
    preferredCenterId?: number
    canChangeCenter?: boolean
    clientMetadata?: CultMembershipMetadata
    meta?: any
    offerId?: string
    offerIds?: string[]
    manageOptions?: { displayText: string, options: ManageOptionPayload[] }
    priceBreakup?: Record<string, any>
    taxAndFeeItemList?: Array<Record<string, any>>
    centerServiceCenterId?: number
    preferredWorkoutId?: number
    preferredWorkoutName?: string
    devPackInfo?: AugmentedOfflineFitnessPack // TODO: remove before PR merge

    constructor() {
    }

    async buildView(widgetBuilderParams: ICultPackSummaryWidgetBuilderParams): Promise<CultPackSummaryWidget> {
        const userAgent = widgetBuilderParams.userContext.sessionInfo.userAgent
        const tz = widgetBuilderParams.userContext.userProfile.timezone
        let category: ImageCategory = userAgent === "DESKTOP" ? "MAGAZINE" : "HERO"
        let customSuffix
        // different image url for desktop for prereg product
        if (userAgent === "DESKTOP" && widgetBuilderParams.preAdvancePaid) {
            category = "CUSTOM_SUFFIX"
            customSuffix = "_prereg_web.jpg"
        }
        const packInfo = widgetBuilderParams.packInfo
        const preferredCenter = widgetBuilderParams.preferredCenter
        this.productType = packInfo.productType
        this.productId = packInfo.productId
        this.title = packInfo.title
        this.duration = packInfo.product.durationInDays
        this.price = packInfo.price
        this.canChangeCenter = widgetBuilderParams.canChangeCenter
        this.preferredCenterName = preferredCenter ? preferredCenter.name : undefined
        this.preferredCenterId = preferredCenter ? preferredCenter.id : undefined
        this.image = CatalogueServiceUtilities.getAccessLevel(packInfo) === AccessLevel.CENTER ? SELECT_IMAGE_URL : CatalogueServiceUtilities.getFitnessProductImage(packInfo, category, userAgent)
        if (PlayUtil.isPlayWebRequest(widgetBuilderParams.userContext, packInfo?.productType)) {
            if (widgetBuilderParams?.membership?.metadata?.workoutId) {
                this.preferredWorkoutId = widgetBuilderParams.membership.metadata.workoutId
                this.preferredWorkoutName = PlayUtil.getSportNameById(widgetBuilderParams.membership.metadata.workoutId.toString())
            }
            this.image = PlayUtil.PLAY_PACK_MAGAZINE_WEB
            const slpMembership = MembershipItemUtil.isPlaySportLevelMembership(widgetBuilderParams.membership)
            if (slpMembership?.isSLPPack) {
                this.image = PlayUtil.getPackImageByWorkoutId(slpMembership.accessWorkout)
            }
        }
        // purchase flow

        if (widgetBuilderParams.isBuyFlow) {
            // 🌕 TEST WEB FLOW HERE!!
            const offersV3PromiseData = await widgetBuilderParams.offersV3Promise
            const isPartOfGstSegment = await AppUtil.doesUserBelongToGSTsplitExperiment(widgetBuilderParams.userContext, widgetBuilderParams.serviceInterfaces)
            const taxBreakup = await widgetBuilderParams.offerUtil.getTaxBreakUpForCultAndGymPacks(offersV3PromiseData)
            const priceBreakupMap = taxBreakup[packInfo.productId]
            const extraChargesPlatformFee = packInfo?.augments?.extraCharges?.filter(charge => charge.extraChargeType === ExtraChargeType.PLATFORM_FEE)?.[0]?.extraChargeFee
            const isPlatformFeeApplicable = extraChargesPlatformFee && Object.keys(extraChargesPlatformFee).length > 0

            let priceBreakup = null
            const result = CultUtil.getPackPriceAndOfferIdV2(packInfo, offersV3PromiseData)
            // TODO: something similar to cartview builder once pack has keys deps: @adithta
            // TODO: for web this ain't only tax but also a key used for any additional fee, change key name later from tax to additionalCharges
            this.price = {
                ...result.price,
                mrp: AppUtil.priceCutSupportedInProductPage(widgetBuilderParams.userContext) ? result.price.mrp : result.price.listingPrice, // Hack since app is reading mrp always
                listingPrice: result.price.listingPrice,
            }
            if (isPartOfGstSegment && priceBreakupMap) {
                priceBreakup = {
                    basePrice: priceBreakupMap?.basePrice,
                    tax: isPlatformFeeApplicable ? extraChargesPlatformFee.totalFee + priceBreakupMap?.taxAmount : priceBreakupMap?.taxAmount   // fitness extra charge
                }
            } else if (!isPartOfGstSegment && isPlatformFeeApplicable) {
                this.price = {
                    ...result.price,
                    mrp: AppUtil.priceCutSupportedInProductPage(widgetBuilderParams.userContext) ? result.price.mrp : result.price.listingPrice, // Hack since app is reading mrp always
                    listingPrice: result.price.listingPrice + extraChargesPlatformFee.totalFee,
                }
            }


            if (isPlatformFeeApplicable) {
                this.taxAndFeeItemList = generateTaxAndFeeBreakup({gstObject: {
                        amount: priceBreakupMap?.taxAmount,
                        rate: priceBreakupMap?.taxPercent
                    }, extraChargeFee: packInfo?.augments?.extraCharges?.filter(charge => charge.extraChargeType === ExtraChargeType.PLATFORM_FEE)?.[0]?.extraChargeFee
                })
            }

            this.priceBreakup = priceBreakup
            this.devPackInfo =  packInfo // TODO: remove before PR merge
            this.offerId = !_.isEmpty(result.offerIds) ? result.offerIds[0] : undefined
            this.offerIds = result.offerIds
            if (widgetBuilderParams.startDateOptions.selectedDate) {
                if (widgetBuilderParams.startDateOptions.canChangeStartDate)
                    this.startDateOptions = TimeUtil.getDaysFrom(tz, widgetBuilderParams.startDateOptions.selectedDate, 7, false)
                else
                    this.startDateOptions = [widgetBuilderParams.startDateOptions.selectedDate]
            }
        }
        else {
            // already having membership for this pack or preadvanced paid
            const membership = widgetBuilderParams.membership
            if (membership.type == MembershipType.COMPLIMENTARY) {
                this.image = userAgent === "DESKTOP" ? "/image/packs/cult/CULTPACK37/80_mag_web.jpg" : "/image/packs/cult/CULTPACK37/80.jpg"
            }
            this.title = membership.name
            // if (widgetBuilderParams.packInfo.ageCategory === "JUNIOR") {
            //     const subUser = await widgetBuilderParams.userPromise
            //     this.subText = `For ${subUser.firstName} ${subUser.lastName}`
            // }
            this.canChangeCenter = false
            const endDateString = TimeUtil.formatEpochInTimeZone(tz, membership.end)
            const startDateString = TimeUtil.formatEpochInTimeZone(tz, membership.start)
            const endDate = TimeUtil.getMomentForDateString(endDateString, tz)
            const endDateFormatted = endDate.format("D MMM YYYY")
            const startDate = TimeUtil.getMomentForDateString(startDateString, tz)
            const startDateFormatted = startDate.format("D MMM YYYY")
            const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDate(tz), tz)
            const numDaysToEndFromToday = endDate.diff(today, "days")
            const startDateText = widgetBuilderParams.cultPackProgress && widgetBuilderParams.cultPackProgress.clientMembershipState === "FUTURE" ? `Starts: ${startDateFormatted}` : `Started:  ${startDateFormatted}`
            const endDateText = widgetBuilderParams.cultPackProgress && widgetBuilderParams.cultPackProgress.clientMembershipState === "EXPIRED" ? `Expired: ${endDateFormatted}` : `Ends: ${endDateFormatted} ${(membership.remainingPauseCount === membership.maxPauseCount) ? "" : "(updated)" }`
            this.packProgress = {
                title: `${startDateText} \u2022 ${endDateText}`,
                startDate: startDateString,
                endDate: endDateString,
                total: endDate.diff(startDate, "days"),
                completed: numDaysToEndFromToday > 0 ? today.diff(startDate, "days") : endDate.diff(startDate, "days"),
                state: numDaysToEndFromToday > 0 ? widgetBuilderParams.cultPackProgress && widgetBuilderParams.cultPackProgress.state : "COMPLETED", // for membership pause
                type: widgetBuilderParams?.packInfo?.productType ?? "FITNESS"
            }
            const cultPackManageOptions = await widgetBuilderParams.productBusiness.getCultPackManageOptions(widgetBuilderParams.userContext, packInfo, membership, await widgetBuilderParams.issuesMapPromise, widgetBuilderParams.serviceInterfaces.segmentService, await widgetBuilderParams.moneyBackOfferPromise, widgetBuilderParams.membershipTransferable)
            this.manageOptions = cultPackManageOptions.manageOptions
            this.meta = cultPackManageOptions.meta
        }
        return this
    }


    async buildViewV2(widgetBuilderParams: ICultPackSummaryWidgetBuilderParamsV2): Promise<CultPackSummaryWidget> {
        const userAgent = widgetBuilderParams.userContext.sessionInfo.userAgent
        const tz = widgetBuilderParams.userContext.userProfile.timezone
        let category: ImageCategory = userAgent === "DESKTOP" ? "MAGAZINE" : "HERO"
        let customSuffix
        // different image url for desktop for prereg product
        if (userAgent === "DESKTOP" && widgetBuilderParams.preAdvancePaid) {
            category = "CUSTOM_SUFFIX"
            customSuffix = "_prereg_web.jpg"
        }
        const packInfo = widgetBuilderParams.packInfo
        const preferredCenter = widgetBuilderParams.preferredCenter
        // this.packId = packInfo.cultPackId
        this.productType = packInfo.productType
        this.productId = packInfo.productId

        const accessLevelCenter = CatalogueServiceUtilities.getAccessLevel(widgetBuilderParams.packInfo) === AccessLevel.CENTER
        this.title = !accessLevelCenter ?  packInfo.title : packInfo.title + " " + packInfo.clientMetadata.centerName

        this.duration = packInfo.product.durationInDays
        this.price = packInfo.price
        this.canChangeCenter = widgetBuilderParams.canChangeCenter
        this.preferredCenterName = preferredCenter ? preferredCenter.name : undefined
        this.preferredCenterId = preferredCenter ? preferredCenter.id : undefined

        if (widgetBuilderParams.preferredCenterId && accessLevelCenter) {
            this.preferredCenterId = widgetBuilderParams.preferredCenterId
        }

        console.log("debug preferredCenterId" + this.preferredCenterId  + " : " + accessLevelCenter)
        this.image = CatalogueServiceUtilities.getAccessLevel(packInfo) === AccessLevel.CENTER ? SELECT_IMAGE_URL : CatalogueServiceUtilities.getFitnessProductImage(packInfo, category, userAgent, customSuffix)

        if (PlayUtil.isPlayWebRequest(widgetBuilderParams.userContext, packInfo?.productType)) {
            if (widgetBuilderParams?.membership?.metadata?.workoutId) {
                this.preferredWorkoutId = widgetBuilderParams.membership.metadata.workoutId
                this.preferredWorkoutName = PlayUtil.getSportNameById(widgetBuilderParams.membership.metadata.workoutId.toString())
            }
            this.image = PlayUtil.PLAY_PACK_MAGAZINE_WEB
            const slpMembership = MembershipItemUtil.isPlaySportLevelMembership(widgetBuilderParams.membership)
            if (slpMembership?.isSLPPack) {
                this.image = PlayUtil.getPackImageByWorkoutId(slpMembership.accessWorkout)
            }
        }

        // purchase flow

        if (widgetBuilderParams.isBuyFlow) {
            const offersV3PromiseData = await widgetBuilderParams.offersV3Promise
            const isPartOfGstSegment = await AppUtil.doesUserBelongToGSTsplitExperiment(widgetBuilderParams.userContext, widgetBuilderParams.serviceInterfaces)
            const taxBreakup = await widgetBuilderParams.offerUtil.getTaxBreakUpForCultAndGymPacks(offersV3PromiseData)
            const priceBreakupMap = taxBreakup[packInfo.productId]
            const extraChargesPlatformFee = packInfo?.augments?.extraCharges?.filter(charge => charge.extraChargeType === ExtraChargeType.PLATFORM_FEE)?.[0]?.extraChargeFee
            const isPlatformFeeApplicable = extraChargesPlatformFee && Object.keys(extraChargesPlatformFee).length > 0

            let priceBreakup = null
            const result = CultUtil.getPackPriceAndOfferIdV2(packInfo, offersV3PromiseData)
            this.price = {
                ...result.price,
                mrp: AppUtil.priceCutSupportedInProductPage(widgetBuilderParams.userContext) ? result.price.mrp : result.price.listingPrice, // Hack since app is reading mrp always
                listingPrice: result.price.listingPrice,
            }
            if (isPartOfGstSegment && priceBreakupMap) {
                priceBreakup = {
                    basePrice: priceBreakupMap?.basePrice,
                    tax: isPlatformFeeApplicable ? extraChargesPlatformFee.totalFee + priceBreakupMap?.taxAmount : priceBreakupMap?.taxAmount
                }
            } else if (!isPartOfGstSegment && isPlatformFeeApplicable) {
                this.price = {
                    ...result.price,
                    mrp: AppUtil.priceCutSupportedInProductPage(widgetBuilderParams.userContext) ? result.price.mrp : result.price.listingPrice, // Hack since app is reading mrp always
                    listingPrice: result.price.listingPrice + extraChargesPlatformFee.totalFee,
                }
            }
            this.priceBreakup = priceBreakup
            this.offerId = !_.isEmpty(result.offerIds) ? result.offerIds[0] : undefined
            this.offerIds = result.offerIds
            if (widgetBuilderParams.startDateOptions.selectedDate) {
                if (widgetBuilderParams.startDateOptions.canChangeStartDate)
                    this.startDateOptions = TimeUtil.getDaysFrom(tz, widgetBuilderParams.startDateOptions.selectedDate, 7, false)
                else
                    this.startDateOptions = [widgetBuilderParams.startDateOptions.selectedDate]
            }
            if (isPlatformFeeApplicable) {
                this.taxAndFeeItemList = generateTaxAndFeeBreakup({
                    gstObject: {
                        amount: priceBreakupMap?.taxAmount,
                        rate: priceBreakupMap?.taxPercent
                    },
                    extraChargeFee: packInfo?.augments?.extraCharges?.filter(charge => charge.extraChargeType === ExtraChargeType.PLATFORM_FEE)?.[0]?.extraChargeFee
                })
            }
            this.devPackInfo = packInfo
        }
        else {
            // already having membership for this pack or preadvanced paid
            const membership = widgetBuilderParams.membership
            if (membership.type == MembershipType.COMPLIMENTARY) {
                this.image = userAgent === "DESKTOP" ? "/image/packs/cult/CULTPACK37/80_mag_web.jpg" : "/image/packs/cult/CULTPACK37/80.jpg"
            }
            this.title = membership.name
            // if (widgetBuilderParams.packInfo.ageCategory === "JUNIOR") {
            //     const subUser = await widgetBuilderParams.userPromise
            //     this.subText = `For ${subUser.firstName} ${subUser.lastName}`
            // }
            this.canChangeCenter = false
            const endDateString = TimeUtil.formatEpochInTimeZone(tz, membership.end)
            const startDateString = TimeUtil.formatEpochInTimeZone(tz, membership.start)
            const endDate = TimeUtil.getMomentForDateString(endDateString, tz)
            const endDateFormatted = endDate.format("D MMM YYYY")
            const startDate = TimeUtil.getMomentForDateString(startDateString, tz)
            const startDateFormatted = startDate.format("D MMM YYYY")
            const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDate(tz), tz)
            const numDaysToEndFromToday = endDate.diff(today, "days")
            const startDateText = widgetBuilderParams.cultPackProgress && widgetBuilderParams.cultPackProgress.clientMembershipState === "FUTURE" ? `Starts: ${startDateFormatted}` : `Started:  ${startDateFormatted}`
            const endDateText = widgetBuilderParams.cultPackProgress && widgetBuilderParams.cultPackProgress.clientMembershipState === "EXPIRED" ? `Expired: ${endDateFormatted}` : `Ends: ${endDateFormatted} ${(membership.remainingPauseCount === membership.maxPauseCount) ? "" : "(updated)" }`
            this.packProgress = {
                title: `${startDateText} \u2022 ${endDateText}`,
                startDate: startDateString,
                endDate: endDateString,
                total: endDate.diff(startDate, "days"),
                completed: numDaysToEndFromToday > 0 ? today.diff(startDate, "days") : endDate.diff(startDate, "days"),
                state: numDaysToEndFromToday > 0 ? widgetBuilderParams.cultPackProgress && widgetBuilderParams.cultPackProgress.state : "COMPLETED", // for membership pause
                type: packInfo?.productType
            }
            const cultPackManageOptions = await widgetBuilderParams.productBusiness.getCultPackManageOptions(widgetBuilderParams.userContext, packInfo, membership, await widgetBuilderParams.issuesMapPromise, widgetBuilderParams.serviceInterfaces.segmentService, await widgetBuilderParams.moneyBackOfferPromise, widgetBuilderParams.membershipTransferable)
            this.manageOptions = cultPackManageOptions.manageOptions
            this.meta = cultPackManageOptions.meta
        }
        return this
    }
}
