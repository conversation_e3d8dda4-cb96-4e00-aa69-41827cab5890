import { Action, ActionList, WidgetType, WidgetView } from "./WidgetView"

export type CarouselCardViewType = "BORDERLESS" | "BORDERED"
export type CarouselCardStyleType = "REGULAR" | "REGULAR_TALL" | "WIDE" | "WIDE_TALL"
export type IconType = "WARNING"

export interface ImageAction {
    url: string,
    action: Action
}
export interface BaseCarouselCardView {
    viewType: CarouselCardViewType,
    styleType: CarouselCardStyleType,
    title: string,
    subTitle?: string
    cardActions?: Action[]
}

export interface ImageCardCarouselView extends BaseCarouselCardView {
    images: string[]
    action: Action
    quickActions: ActionList[]
    calloutText?: string
    imageOverlayView?: {
        orientation: "BOTTOM_RIGHT"
        theme: "DARK"
        text: string
    },
    imageActions?: ImageAction[],
    isCafe?: boolean,
    showSubTitleOnCollapse?: boolean,
    firstImageWidthPercentage?: number,
}

export interface TextListCarouselView extends BaseCarouselCardView {
    textList: {
        icon?: string
        leftMarginText?: string
        descriptionText: string
    }[]
    action: Action
    quickActions: ActionList[]
    calloutText?: string
}

export interface HeaderTextCarouselView extends BaseCarouselCardView {
    header: {
        title: string
        backgroundColor?: string
    }
    action: Action
    quickActions: ActionList[]
    calloutText?: string
}

export interface ICarouselWidget extends WidgetView {
    cards: BaseCarouselCardView[]
    title: string
    leftCalloutText: string
    rightCalloutText?: string
}

export abstract class CarouselWidget implements ICarouselWidget {
    widgetType: WidgetType = "CAROUSEL_WIDGET"
    cards: BaseCarouselCardView[]
    title: string
    subTitle?: string
    titleStyle?: any
    leftCalloutText: string
    rightCalloutText?: string
    rightCalloutView?: {
        borderColor: string
        textColor: string
        text: string
    }
    leftIcon?: IconType

    constructor() {
        this.cards = []
    }
}
