import { FoodShipmentStatus, MenuType, OrderTrackingStatus } from "@curefit/eat-common"
import { Action, WidgetType, WidgetView } from "./WidgetView"
import { FoodBooking } from "@curefit/shipment-common"
import { TimeUtil } from "@curefit/util-common"
import EatUtil from "../../../app/util/EatUtil"
import * as _ from "lodash"
import { UserContext } from "@curefit/userinfo-common"
import { MealUtil } from "@curefit/base-utils"
import { ActionUtil } from "@curefit/base-utils"
import AppUtil from "../../util/AppUtil"

class OrderTrackingStatusWidget implements WidgetView {
    public widgetType: WidgetType = "ORDER_TRACKING_WIDGET"
    public values: {
        status: OrderTrackingStatus;
        active: boolean;
        title: string;
        subtitle?: string;
        expectedDeliveryTime?: string;
    }[]
    public phoneNumber: string
    public actions: Action[]
    public action: Action
    public state: FoodShipmentStatus | "NOT_STARTED" | "CANCELLED"
    public setTimer: boolean
    public foodOrderId?: string
    constructor(foodBooking?: FoodBooking, mealSlot?: MenuType, userContext?: UserContext) {
        if (foodBooking && mealSlot && userContext) {
            const bookingTz = foodBooking.timezone
            this.widgetType = "ORDER_TRACKING_WIDGET"
            const currentStatus: OrderTrackingStatus = EatUtil.getEatFitDeliveryStatus(foodBooking.state, foodBooking.listingBrand)
            this.state = foodBooking.state
            this.phoneNumber = foodBooking.crewContactNumber
            const isKiosk = foodBooking.address.kioskId !== undefined
            if (currentStatus !== undefined && MealUtil.isOrderTrackingSupported(userContext) && currentStatus !== "DELIVERED" && currentStatus !== "REJECTED" && !isKiosk && foodBooking.disableOrderTracking !== true) {
                if (AppUtil.isNewOrderTrackingWidgetSupported(userContext)) {
                    this.actions = [{
                        actionType: "NAVIGATION",
                        title: "Track Order",
                        meta: {
                            icon: "LOCATION_PIN"
                        },
                        url: ActionUtil.trackFoodOrder(foodBooking.fulfilmentId, foodBooking.deliveryDate)
                    }]
                } else {
                    this.action = {
                        actionType: "NAVIGATION",
                        title: "Track Order",
                        meta: {
                            icon: "LOCATION_PIN"
                        },
                        url: ActionUtil.trackFoodOrder(foodBooking.fulfilmentId, foodBooking.deliveryDate)
                    }
                }
            }
            if (currentStatus !== undefined) {
                const eta: string = foodBooking.eta ? TimeUtil.get12HRTimeFormat(foodBooking.eta, bookingTz) : undefined
                const etaStr: string = foodBooking.eta ? TimeUtil.formatDateInTimeZone(bookingTz, foodBooking.eta, "YYYY-MM-DD HH:mm:ss") : undefined
                const timeDiff: number = etaStr ? TimeUtil.diffInMinutes(bookingTz, TimeUtil.todaysDate(bookingTz, "YYYY-MM-DD HH:mm:ss"), etaStr) : undefined
                this.values = [
                    {
                        status: "PREPARING",
                        active: currentStatus === "PREPARING",
                        title: this.getOrderTitle("PREPARING", foodBooking, mealSlot),
                        expectedDeliveryTime: eta
                    },
                    {
                        status: "PACKING",
                        active: currentStatus === "PACKING",
                        title: this.getOrderTitle("PACKING", foodBooking, mealSlot),
                        expectedDeliveryTime: eta
                    },
                    {
                        status: "ON_ITS_WAY",
                        active: currentStatus === "ON_ITS_WAY",
                        title: this.getOrderTitle("ON_ITS_WAY", foodBooking, mealSlot),
                        expectedDeliveryTime: eta
                    },
                    {
                        status: "ARRIVED",
                        active: currentStatus === "ARRIVED",
                        title: this.getTitle(!_.isNil(foodBooking.address.kioskId), foodBooking.address.kioskType === "CAFE", mealSlot),
                        subtitle: this.getSubTitle(!_.isNil(foodBooking.address.kioskId), foodBooking.address.kioskType === "CAFE")
                    },
                    {
                        status: currentStatus === "REJECTED" ? "REJECTED" : "DELIVERED",
                        active: currentStatus === "DELIVERED" || currentStatus === "REJECTED",
                        title: this.getOrderTitle(currentStatus === "REJECTED" ? "REJECTED" : "DELIVERED", foodBooking, mealSlot),
                        subtitle: this.getOrderSubTitle(currentStatus === "REJECTED" ? "REJECTED" : "DELIVERED", foodBooking, mealSlot)
                    }
                ]
            }
        }
    }

    private getOrderTitle(status: OrderTrackingStatus, foodBooking: FoodBooking, mealSlot: MenuType) {
        switch (status) {
            case "PREPARING":
                return `Preparing ${this.getMealSlotCamelCase(mealSlot)}.`
            case "PACKING":
                return `${this.getMealSlotCamelCase(mealSlot)} being packed.`
            case "ON_ITS_WAY":
                return `${this.getMealSlotCamelCase(mealSlot)} is on the way`
            case "DELIVERED":
                return `${this.getMealSlotCamelCase(mealSlot)} ${foodBooking.address.kioskId ? "picked up" : "delivered"}.`
            case "REJECTED":
                return `Your ${this.getMealSlotCamelCase(mealSlot)} was not delivered`
            default:
                return ""
        }
    }

    private getOrderSubTitle(status: OrderTrackingStatus, foodBooking: FoodBooking, mealSlot: MenuType) {
        switch (status) {
            case "DELIVERED":
                return (foodBooking.address.eatDeliveryInstruction && foodBooking.address.eatDeliveryInstruction.dropInstruction !== "ME" && foodBooking.address.eatDeliveryInstruction.dropInstruction !== "NONE") ?
                    "Your meal has been dropped at the " + foodBooking.address.eatDeliveryInstruction.dropInstruction.toLowerCase() : ""
            case "REJECTED":
                return this.getUserFriendlyRejectionReason(foodBooking.reason)
        }
    }

    // Should be changed to get a type in the future
    private getUserFriendlyRejectionReason(reason: string) {
        switch (reason) {
            case "Customer not at location":
            case "Customer asked to wait":
                return "No one was there at the location to collect the order"
            case "Customer does not want the order":
                return "Order returned as per your discussion with Health Crew"
            default:
                return "Order returned"
        }
    }

    private getMealSlotCamelCase(mealSlot: MenuType): string {
        switch (mealSlot) {
            case "BREAKFAST":
                return "Breakfast"
            case "LUNCH":
                return "Lunch"
            case "SNACKS":
                return "Snacks"
            case "DINNER":
                return "Dinner"
            case "ALL":
                return "Order"
        }
    }

    private getTitle(isKioskId: boolean, isCafe: boolean, mealSlot: MenuType): string {
        if (isCafe && isKioskId) {
            return "Pending for pickup"
        }
        else if (isKioskId) {
            return `Your ${this.getMealSlotCamelCase(mealSlot)} has arrived`
        }
        else {
            return "Your order will be delivered shortly."
        }
    }
    private getSubTitle(isKioskId: boolean, isCafe: boolean): string {
        if (isCafe && isKioskId) {
            return "Please pick up your meal from the cafe"
        }
        else if (isKioskId) {
            return "Please pick up your meal from the kiosk"
        }
        else {
           return "Our health crew is about to reach your location."
        }
    }
}

export default OrderTrackingStatusWidget
