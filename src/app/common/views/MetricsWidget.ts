import * as _ from "lodash"
import { Action, WidgetType, WidgetView } from "./WidgetView"
import { UserActivityDetails } from "@curefit/quest-common"
import { GoalAdherenceStatusTypes, UserGoalSearchParams, UserGoalsSummary } from "@curefit/gmf-common"
import { IMetricServiceClient as IMetricService } from "@curefit/metrics"
import { GoalUtil } from "../../goals/GoalUtil"
import { HIGH_PRIORITY_TIMESTAMP, ITodoActivityWidget } from "./TodoActivityWidget"
import { ActivityState } from "@curefit/logging-common"
import { ActivityType } from "@curefit/product-common"
import { UserContext } from "@curefit/userinfo-common"
import { Logger } from "@curefit/base"
import { IGMFClient } from "@curefit/gmf-client"
import { eternalPromise } from "@curefit/util-common"

export type GoalCardViewType = "LARGE" | "SMALL" | "MEDIUM" | "ADD_GOAL"
export type MetricCardType = "GOAL" | "SCORE"


export interface GoalCardCalloutView {
    title: string,
    subTitle: string
}

export interface GoalCardView {
    header?: string,
    type: MetricCardType,
    viewType: GoalCardViewType,
    action?: Action,
    status?: number,
    icon?: string,
    progress?: number,
    leftView?: GoalCardCalloutView,
    rightView?: GoalCardCalloutView,
    gradientColors?: string[]
}

export interface IMetricWidgetBuildParams {
    userContext: UserContext,
    scoreMetric?: ScoreMetricsWidget,
    gmfClient: IGMFClient,
    metricService: IMetricService,
    logger: Logger
}

export interface IMetricsCardsWidget extends WidgetView {
    cards: GoalCardView[]
}



export class MetricsWidget implements IMetricsCardsWidget {
    widgetType: WidgetType
    cards: GoalCardView[]
    constructor() {
        this.widgetType = "METRICS_TIMELINE_CARDS_WIDET"
        this.cards = []
    }


    public async buildView(metricBuildParams: IMetricWidgetBuildParams): Promise<void> {
        // const userPlanExistsResult = await eternalPromise(metricBuildParams.gmfClient.checkExistsUserPlan(metricBuildParams.userContext.userProfile.userId))
        const userPlanExists: boolean = false // _.get(userPlanExistsResult.obj, "exists", false)
        // TODO: Delete code as gmf client always returns false for userPlanExists
        if (userPlanExists) {
            await this.buildGoalTickersView(metricBuildParams)
        }
        const scoreMetricCards: GoalCardView[] = _.get(metricBuildParams, "scoreMetric.cards")
        if (_.isArray(scoreMetricCards)) {
            this.cards = [...metricBuildParams.scoreMetric.cards, ...this.cards]
        }
    }

    public async buildGoalTickersView(metricBuildParams: IMetricWidgetBuildParams): Promise<void> {
        const goalCards: GoalCardView[] = []
        const userGoalsSummary: UserGoalsSummary = await getUserGoalsSummaryPromise(metricBuildParams.userContext, metricBuildParams.gmfClient, metricBuildParams.logger)
        if (!_.isEmpty(userGoalsSummary)) {
            if (!_.isEmpty(userGoalsSummary.userGoalDetail)) {

                const goalCardPromises = _.map(userGoalsSummary.userGoalDetail, async (goal) => {
                    const targetMetric = _.maxBy(goal.userGoal.goalMetricTargets, metricTarget => metricTarget.priority)
                    const metricDetail = await metricBuildParams.metricService.getMetricById(targetMetric.metricId)
                    const targetValue = GoalUtil.getGoalTargetDisplayValue(targetMetric, metricDetail)
                    let currentValue
                    let metricUnit
                    if (_.isEmpty(goal.userMetrics)) {
                        currentValue = "-"
                        metricUnit = ""
                    }
                    else {
                        currentValue = goal.userMetrics[0].values[0].displayValue
                        metricUnit = goal.userMetrics[0].unit ? `- ${goal.userMetrics[0].unit}` : ""
                    }
                    const cardView: GoalCardView = {
                        type: "GOAL",
                        header: goal.userGoal.goalName,
                        viewType: "SMALL",
                        action: {
                            actionType: "NAVIGATION",
                            url: `curefit://goaldetails?goalId=${goal.userGoal.goalId}`
                        },
                        // because status is opposite in client (0 - Poor)
                        status: (GoalAdherenceStatusTypes.length - 1) - GoalAdherenceStatusTypes.indexOf(goal.goalAdherenceStatus.overAllStatus),
                        leftView: {
                            title: currentValue,
                            subTitle: `${targetMetric.metricName}${metricUnit}`
                        },
                        rightView: {
                            title: targetValue,
                            subTitle: "Target"
                        },
                        gradientColors: GoalUtil.getGoalGradientColor(goal.goalAdherenceStatus.overAllStatus)
                    }
                    return cardView
                })
                const goalCardViews = await Promise.all(goalCardPromises)
                goalCards.push(...goalCardViews)
            }
            if (userGoalsSummary.eligibleForMoreGoals) {
                goalCards.push({
                    type: "GOAL",
                    header: "ADD GOAL",
                    viewType: "ADD_GOAL",
                    icon: "ADD_GOAL",
                    action: {
                        actionType: "NAVIGATION",
                        url: "curefit://listpage?pageId=ai_plan"
                    }
                })
            }
        }
        this.cards = goalCards
    }

}


export class ScoreMetricsWidget extends MetricsWidget implements ITodoActivityWidget {
    timestamp: number
    status: ActivityState
    activityType: ActivityType

    public buildScoreView(userActivityDetails: UserActivityDetails) {
        const goalCards: GoalCardView[] = []
        let scoreCardView: GoalCardView
        const numDays = 30
        // Build the dashboard summary from the user activity detail
        const globalActivity = _.find(userActivityDetails.activitiesBreakUp, activity => {
            return activity.vertical === "GLOBAL"
        })
        const targetForNextLevel = userActivityDetails.pointsLeftForNextLevel + userActivityDetails.activityPoints
        const currentLevelPoints = userActivityDetails.level.levelId === 0 ? 0 : userActivityDetails.level.activityCount
        let progress = (userActivityDetails.activityPoints - currentLevelPoints) / (targetForNextLevel - currentLevelPoints)
        progress = _.isNaN(progress) ? 0 : parseFloat(progress.toFixed(2))
        const title = "Level " + userActivityDetails.level.levelId.toString()
        const subTitle = userActivityDetails.activityPoints + (userActivityDetails.numActivities == 1 ? " point in " : " points in ") + numDays + " days"
        const viewType: GoalCardViewType = "LARGE"

        scoreCardView = {
            type: "SCORE",
            viewType: viewType,
            action: {
                actionType: "NAVIGATION",
                url: "curefit://dashboardv2"
            },
            progress: progress,
            status: userActivityDetails.level.levelId,
            leftView: {
                title: title,
                subTitle: subTitle
            },
            rightView: {
                title: globalActivity.currentStreak.toString(),
                subTitle: "Streak"
            }
        }

        goalCards.push(scoreCardView)
        this.timestamp = HIGH_PRIORITY_TIMESTAMP
        this.status = "TODO"
        this.activityType = "METRICS"
        this.cards = goalCards
    }
}

async function getUserGoalsSummaryPromise(userContext: UserContext, gmfClient: IGMFClient, logger: Logger): Promise<UserGoalsSummary> {
    try {
        const userGoalSearchParams: UserGoalSearchParams = {
            userId: userContext.userProfile.userId,
            statuses: ["LIVE"],
            isSelf: true
        }
        return await gmfClient.getUserGoalsSummary(userContext.userProfile.userId, userGoalSearchParams)
    } catch (e) {
        logger.error("Error while trying to contact gms Service !! ", e)
        return
    }
}
