import { DeliveryGate } from "@curefit/eat-common"
import { Status } from "@curefit/base-common"

class DeliveryGateView {
    constructor(gate: DeliveryGate) {
        this.gateId = gate.gateId
        this.name = gate.name
        this.address = gate.locality
        this.addressString =  gate.addressString + ", " + gate.locality + ", " + gate.city
        this.status = gate.status ? gate.status : "LIVE"
    }
    public gateId: string
    public name: string
    public address: string
    public status: Status
    public addressString: string
}

export default DeliveryGateView
