import { Action, WidgetView } from "./WidgetView"
import { AggregationRange } from "@curefit/metrics-common"
import { DisplayCategory } from "@curefit/logging-common"

export type GraphViewType = "LABELS_ONLY" | "DASHED_LINE" | "SOLID_LINE" | "IDEAL_VALUE_LINE"

export interface GraphAggregationType {
  type: AggregationRange
  displayText: string
  action: Action
}

export interface GraphProperties {
  labelColor?: string
  circleColor?: string
  lineStrokeColor?: string
  lineStrokeWidth?: number
  lineStrokeDashLength?: number
}

export interface GraphHeader {
  title: string
  subTitle: string
  more: Action
  graphType: GraphAggregationType
  meta?: Object
}

export interface GraphMetric {
  displayText: string
  isPrimary: boolean
  selected: boolean
  metricId?: number
  category?: DisplayCategory
  unit: string
}

export interface MetricsGraphView {
  data: { key: string; value: any }[]
  type: GraphViewType
  properties?: GraphProperties
}

export interface LastLoggedInfoView {
  text: string
  logAction?: Action
}

export interface IMetricsGraphWidget extends WidgetView {
  header: GraphHeader
  graphs: MetricsGraphView[]
  graphType: GraphAggregationType
  graphMetrics: GraphMetric[]
  lastLoggedInfo?: LastLoggedInfoView
  fromDate: string
  toDate: string
  range: Array<number>
  goalValue?: number
  calloutText?: string
}

export interface IGraphWidgetBuilderParams {
  fromDate: string
  toDate: string
  graphType: AggregationRange
}
