import FeedbackUtil from "../../util/FeedbackUtil"
import {
  DeliveryChannel,
  EatCLPTabs,
  FoodPack,
  FoodPackOption,
  MealSlot,
  MenuType,
  Address,
  ListingBrandIdType
} from "@curefit/eat-common"
import { ActivityType, ProductPrice, ProductType } from "@curefit/product-common"
import {
  CONSULTATION_MODE,
  Patient,
  DoctorAvailability,
  ConsultationProduct
} from "@curefit/care-common"
import { CartOffer, FoodPackOffersResponseV2 } from "@curefit/offer-common"
import { HourMin, LatLong, UserAgentType as UserAgent } from "@curefit/base-common"
import { Session } from "@curefit/userinfo-common"
import { SubscriptionType } from "@curefit/base-common"
import { UserDeliveryAddress } from "@curefit/eat-common"
import DeliveryAreaView from "../../user/DeliveryAreaView"
import { PlaceData } from "@curefit/location-common"
import UserAddressView from "../../user/UserAddressView"
import { PreferredLocation } from "@curefit/userinfo-common"
import { BookingDetail, ConsultationOrderResponse, Doctor, Medicinetype, ConsultationInstructionResponse } from "@curefit/albus-client"
import { DiyContentDetail, DiyContentMeta } from "../../util/AtlasUtil"
import { PageWidget, PageWidgetType } from "../../page/Page"
import { injectable } from "inversify"
import { CFImageData, ImageStyle, Orientation, TemplateWidget, TextStyle, WidgetTypes } from "@curefit/vm-common"
import { CultNoShowOption } from "@curefit/intervention-common"
import * as _ from "lodash"
import { FoodFulfilment, OrderProduct } from "@curefit/order-common"
import { PaymentChannel, SavedCard } from "@curefit/payment-common"
import { OfferUtil } from "@curefit/base-utils"
import { FoodPackOffersResponse, OfferV2, OfferV2Lite } from "@curefit/offer-common"
import { capitalizeFirstLetter, TimeUtil, Timezone } from "@curefit/util-common"
import { ActionUtil } from "@curefit/base-utils"
import MealPackPageConfig from "../../pack/MealPackPageConfig"
import { FoodPackBooking } from "@curefit/shipment-common"
import { ActionType as VMActionType, WidgetType as VMWidgetType } from "@curefit/vm-common"
import { BillingInfo } from "@curefit/finance-common"
import { CareUtil, SF_FREEMIUM_FULL_BODY_TEST_LIST } from "../../util/CareUtil"
import { CardDescription, DailyMealWidget, FONT_WEIGHT, IBaseWidget, RoundedImageGridItem } from "@curefit/vm-models"
import { PriceComponent, SfEcommerceProduct } from "../../order/OrderViewBuilder"
import { BaseWidget, UserContext } from "@curefit/vm-models"
import { IssueDetailViewAction } from "../../crm/IssueBusiness"
import { CultBooking } from "@curefit/cult-common"
import { ActivityState } from "@curefit/logging-common"
import AppUtil, { LIVE_SGT_SUPPORTED_VERSION } from "../../util/AppUtil"
import { CountryData } from "../../user/UserController"
import {
  WidgetView as AppWidgetView,
  WidgetType as AppWidgetType,
  WidgetHeader,
  MediaAsset,
  CultBuddiesJoiningListLargeWidget,
  CultBuddiesJoiningListSmallWidget,
  ViewStyle,
  RNTextStyle,
  BuddiesInviteJoiningListWidgetV2,
  IPostWorkoutDiyWidget,
  IPrescribedMedicationWidget,
  IDataListingWidget,
  IListingItem,
  DataListingType,
  IDescriptionWithFooterActionWidget,
  IDiagnosticTestItem,
  IDiagnosticTestListDetailWidget,
  ImagesInText,
  IEmptyDiagnosticCartListingWidget,
  IDiagnosticCartListingWidget,
  IDiagnosticCartTestItem,
  IDiagnosticsConfirmationDesktopWidget,
  IDiagnosticConfirmationHeading,
  IDiagnosticConfirmationTestDetails,
  IDiagnosticConfirmationPaymentDetails,
  IWidgetType,
  ICartDiagnosticsCheckoutSummaryWidget,
  IMobileBreadCrumbWidget,
  Action,
  ActionType,
  ActionIcon,
  ManageOptionIcon,
  IBreadCrumb as IAppsBreadCrumb,
  IDiagnosticsConfirmationSummaryDetails,
  SeoWidget
} from "@curefit/apps-common"
import { User } from "@curefit/user-common"

interface AppWidgetViewStyle extends AppWidgetView {
  style?: any
  [styleFlag: string]: any
}

export type WidgetType = AppWidgetType | VMWidgetType
export type WidgetView = AppWidgetViewStyle

export type SlotActionType = "ALERT" | "TOAST"

export interface FreemiumUserOffer {
  packId: string
  packPrice: number
  offerPrice: number
  fromDate: Date
  toDate: Date
}

export interface AlertInfo {
  unavailableItems?: string[]
  title: string
  subTitle: string
  isInlineMessage?: boolean
  actions: Action[]
  statusCode?: number
  meta?: any
  listItems?: { title: string; subTitle?: string }[]
}

export class PausePackInfo {
  membershipInfo: CultMembershipInfo
  widgets: WidgetView[]
  existingBookings: ExistingBookings
  showPauseProgressBar?: boolean
  headerWidgets?: WidgetView[]
}

export interface EmiOptions {
  title: string
  widgets: WidgetView[]
  allowMultipleOpen: boolean
}

export interface OfferCalloutCardWidget extends WidgetView {
  subTitle: string
  action?: Action
  icon: string
}

export interface EmiWidget extends WidgetView {
  defaultOpenIndex: number
  widgets: NoCostEmiDropdownWidget[]
  fromPage: string
  allowMultipleOpen: boolean
}

export interface ToggleNeuPassWidget extends WidgetView {
  widgetType: "TOGGLE_NEUPASS_ACTIVATION_WIDGET",
  data: {
    title: string,
    icon: string,
    title2: string,
    subTitle: string,
    checked: boolean,
    action: Action,
    style?: ViewStyle
    knowMore?: Action,
  }
}

export interface BookingAction {
  title: string,
  action: Action,
  attribute?: string,
  trailingIcon?: string
  leadingIcon?: string
  titleType?: string,
  trailingIconType?: string,
  leadingIconImg?: CFImageDataWithScaleContext
}

export interface CFImageDataWithScaleContext extends CFImageData {
  scaleWithContext?: boolean
}

export interface CreditsFooter {
  creditPillData?: CreditPillData,
  description?: string,
}

export interface NeuPassEarnedCoinsWidget extends WidgetView {
  widgetType: "NEUPASS_EARNED_COINS_WIDGET",
  data: {
    title?: string,
    headerText?: string,
    subTitle?: string,
    icon: string,
    style?: ViewStyle
    infoAction?: Action
  }
}

export interface NoCostEmiDropdownWidget extends WidgetView {
  title: string
  expandIcon: string
  info: {
    titles: EmiValues[]
    values: EmiValues[][]
  }
  tnc: string[],
  tncUrl?: string,
  tncTitle: string,
  amountPayable: {
    title: string,
    amount: string
  }
  disclaimer: string
}

export interface EmiValues {
  title: string,
  titleColor: string,
  type: string,
  bracketInfo?: {
    title: string,
    titleColor: string
  }
}

export interface ExistingBookings {
  action: {
    positiveActionText: string,
    negativeActionText: string
  },
  confirmationTitle: string,
  confirmationDescription: string,
  cultBookings: CultBookingInfo[]
}

export interface CultBookingInfo {
  workoutName: string,
  centerName: string,
  date: string,
  workoutImageUrl: string
}

export interface CultMembershipInfo {
  membershipId: number,
  pauseMaxDays: number,
  remainingPauseDays: number,
  startDateParam: {
    date: string,
    limit: string,
    canEdit: boolean
  },
  endDate?: string,
  pauseInfo: PauseInfo[]
}

export interface PauseInfo {
  title: string,
  value: string
}

export interface CultPausePackOption {
  optionText: string,
  optionId: string
}

@injectable()
export class ProductDetailPage {
  pageAction?: ActionCard
  // Remove after killing 5.2
  pageActions?: Action[] = undefined
  pageImage?: string
  actions?: Action[] = []
  widgets: (BaseWidget | WidgetView | PageWidget | TemplateWidget | IBaseWidget | TrialCardWidget)[] = []
  footerWidgets?: (BaseWidget | WidgetView | PageWidget | TemplateWidget | IBaseWidget | TrialCardWidget)[] = []
  navigationAction?: (NavigationAction) = {}
  message?: {
    title: string
    subTitle: string
  }
  alertInfo?: AlertInfo
  wodInfo?: {
    wodId: string
  }
  addons?: {
    widget: any[],
    pageAction: ActionType
  }
  title?: string
  announcementData?: AnnouncementView
  refreshTimeEpoch?: number
  breadCrumbs?: IBreadCrumb[]
  meta?: any
  seoData?: any
  themeType?: string
  creditCost?: number
  isPilateGymPage?: boolean
}

@injectable()
export class SGTOneStepBookingPage {
  widgets: any[]
  actions: Action[]
}

@injectable()
export class CultWorkoutPage {
  title: string
  workoutFamilies: string[]
  workoutFamilyVsWorkout: { family: string, info: RoundedImageGridItem[] }[]
}

@injectable()
export class CultWorkoutDetailPage {
  categories: { category: string, workoutId: string }[]
  selectedWorkoutIndex: number
  widgets: WidgetView[] = []
  action: Action
  actions?: Action[]
  images?: string[]
}

export class LPTGoalPage {
  header?: Header
  widgets: WidgetView[] = []
  actions: Action[]
}

export interface LiveSessionReportMessage {
  title: string
  subTitle: string,
  containerStyle?: any
  textStyle?: any
}

export interface AllMovementsPage {
  widgets: WidgetView[]
  searchPlaceHolder: string
}

export interface AllMovements extends WidgetView {
  type: string
  title: string
  movementCards: MovementCard[]
  emptyState: {
    text: string
    icon: string
  }
}

export interface MovementCard {
  action: Action
  title: string
  description: string
  media?: {
    url: string,
    type: string,
    _id?: string
  }
  type: "LARGE" | "SMALL"
  imageThumbnail: string
  showDivider: boolean
  metaIcon?: string
  bodyParts: string[]
}

export interface NavigationAction {
  title?: string
  action?: Action
  textStyle?: any
  containerStyle?: any
  analyticsData?: any
  type?: "SHARE" | null
}

export interface MovementsFilter extends WidgetView {
  type: string
  title: string
  filters: {
    color: string
    title: string
  }[]
}

@injectable()
export class AddChangeMealPage extends ProductDetailPage {
  cartOffers: CartOffer[]
  productTitle?: string
  productId?: string
  fulfilmentId?: string
  date?: string
  mealSlot?: string
  onlyAddEnabled?: boolean
  action?: Action
}

@injectable()
export class CancelSubscriptionPage {
  widgets: (WidgetView | ExpandableListWidget)[] = []
  constructor(
    pageType: string,
    foodFulFilment: FoodFulfilment,
    mealPackPageConfig: MealPackPageConfig,
    packIds?: string[]
  ) {
    const fulfilmentId = foodFulFilment.fulfilmentId
    const packId = foodFulFilment.products[0].productId
    this.widgets.push(
      new ProductListWidget(
        "INFO_ROW",
        { title: "Don't lose out on these awesome benefits" },
        mealPackPageConfig.whySubscribeItems
      )
    )
    this.widgets.push({
      widgetType: "EXPANDABLE_LIST_WIDGET",
      title: "Tell us why you want to cancel",
      showDivider: true,
      allowMultipleOpen: false,
      widgets: [
        {
          widgetType: "CANCEL_OPTION_WIDGET",
          reason: "I'm travelling out of town",
          rebuttal:
            "You can choose to cancel all your meals till you return. You will receive a refund for all cancelled meals.",
          actions: [
            {
              actionType: "CANCEL_SUBSCRIPTION",
              title: "Still Cancel",
              meta: {
                packIds: packIds,
                packId: packId,
                fulfilmentId: fulfilmentId,
                reasonCode: "TRAVEL",
                reason: "I'm travelling out of town",
              }
            },
            {
              actionType: "POP_ACTION",
              title: "Cancel Meals"
            }
          ]
        },
        {
          widgetType: "CANCEL_OPTION_WIDGET",
          reason: "I want a change of food",
          rebuttal:
            "You can change your upcoming meal and cancel as many meals as you like. You will receive a refund for all cancelled meals",
          actions: [
            {
              actionType: "CANCEL_SUBSCRIPTION",
              title: "Still Cancel",
              meta: {
                packIds: packIds,
                packId: packId,
                fulfilmentId: fulfilmentId,
                reasonCode: "CHANGE_FOOD",
                reason: "I want a change of food",
              }
            },
            {
              actionType: "POP_ACTION",
              title: "Change / Cancel"
            }
          ]
        },
        {
          widgetType: "CANCEL_OPTION_WIDGET",
          reason: "I want a different address / timeslot",
          rebuttal:
            "You can update the address and timeslot of your subscription without having to cancel it.",
          actions: [
            {
              actionType: "CANCEL_SUBSCRIPTION",
              title: "Still Cancel",
              meta: {
                packIds: packIds,
                packId: packId,
                fulfilmentId: fulfilmentId,
                reasonCode: "CHANGE_INFO",
                reason: "I want a different address / timeslot",
              }
            },
            {
              actionType: "POP_ACTION",
              title: "Update Info"
            }
          ]
        },
        {
          widgetType: "CANCEL_OPTION_WIDGET",
          reason: "I do not like the food",
          cancelPrompt:
            "We're terribly sorry to hear that. We're constantly innovating to improve our food offering. We'd love to hear what we could've done better",
          actions: [
            {
              actionType: "CANCEL_SUBSCRIPTION",
              title: "Cancel Subscription",
              meta: {
                packIds: packIds,
                packId: packId,
                fulfilmentId: fulfilmentId,
                reasonCode: "BAD_FOOD",
                reason: "I do not like the food",
              }
            }
          ]
        },
        {
          widgetType: "CANCEL_OPTION_WIDGET",
          reason: "I'm not sure the food is healthy",
          rebuttal:
            "All our meals contain no refined sugar, is calorie counted and specially prepared with balanced portions of protein, carbs, fibre and fats to deliver the best health benefits to you.",
          actions: [
            {
              actionType: "CANCEL_SUBSCRIPTION",
              title: "Still Cancel",
              meta: {
                packIds: packIds,
                packId: packId,
                fulfilmentId: fulfilmentId,
                reasonCode: "UNHEALTHY",
                reason: "I'm not sure the food is healthy",
              }
            },
            {
              actionType: "NAVIGATION",
              title: "Tell me more",
              url: ActionUtil.infoPage("whyeatfit")
            }
          ]
        },
        {
          widgetType: "CANCEL_OPTION_WIDGET",
          reason: "My reason is not listed",
          cancelPrompt: "We're sorry to see you go! Tell us what went wrong",
          actions: [
            {
              actionType: "CANCEL_SUBSCRIPTION",
              title: "Cancel Subscription",
              meta: {
                packIds: packIds,
                packId: packId,
                fulfilmentId: fulfilmentId,
                reasonCode: "NOT_LISTED",
                reason: "My reason is not listed",
              }
            }
          ]
        }
      ]
    })
  }
}

export interface CancelOptionWidget extends WidgetView {
  widgetType: "CANCEL_OPTION_WIDGET"
  reason: string
  rebuttal: string
  actions: Action[]
  cancelPrompt?: string
}

export interface CareServiceFulfillmentWidget extends WidgetView {
  widgetType: "CARE_SERVICE_FULFILLMENT_WIDGET",
  title: string
  imageUrl: string,
  pageItems: string [],
  backgroundColor: string,
  borderColor: string,
  action?: Action
}

export interface CareLHRPackWidget extends WidgetView {
  widgetType: "CARE_LHR_PACK_WIDGET",
  header: {
    title: string,
    subTitle: string
  },
  packs: LHRPack[],
  bodyPartsData: {
    title: string,
    bodyParts: LHRBodyPart[]
  }
}

export interface LHRPack {
  isSelected: boolean,
  title: string,
  subTitle: string,
  numberOfSessions: number,
  numberOfBodyParts: number,
  price: ProductPrice & {
    showFreeForZeroPrice?: boolean
  }
  priceMeta?: string
  action: Action
}

export interface LHRBodyPart {
  imageUrl: string,
  code: string,
  title: string,
}

export interface LHRBodyPartsWidget extends WidgetView {
  widgetType: "CARE_LHR_BODY_PARTS_WIDGET",
  title?: string,
  bodyParts: LHRBodyPart[]
}

export interface ExpandableListWidget extends WidgetView {
  widgetType: "EXPANDABLE_LIST_WIDGET"
  title: string,
  titleFontSize?: number,
  allowMultipleOpen: boolean
  widgets: WidgetView[]
  showDivider?: boolean
  defaultOpenIndex?: number
}

export interface FitClubCheckoutSavingsWidget extends WidgetView {
  widgetType: "FITCLUB_CHECKOUT_SAVINGS_WIDGET"
  title: string
  fitcashBalanceText: string
}

export class MealProductDetailPage extends ProductDetailPage {
  pageActions?: (MealAction | Action)[]
  cartOffers?: CartOffer[]
}

export interface TransferMembershipConfirmationWidget extends WidgetView {
  widgetType: "TRANSFER_MEMBERSHIP_CONFIRMATION_WIDGET",
  title: string,
  subTitle: string,
  transferredLocation: string,
  membershipName: string,
  membershipEndDate: string,
  transferredMemberName?: string,
  transferredMemberImageUrl?: string
}

export interface ActionPageWidgetView {
  actions?: Action[],
  widgets: WidgetView[],
  pageActionTitle?: string
}

export interface AddTransferMemberDetails {
  data: {
    title: string,
    helpText: string,
    info: string,
  },
  referrerAction: Action,
  actions: Action[]
}

export type ManageOption =
  | "PAUSE_MEAL_PACK"
  | "RESUME_MEAL_PACK"
  | "GET_MEAL_PACK"
  | "RENEW_MEAL_PACK"
  | "RESCHEDULE_MEAL"
  | "RESCHEDULE_MEALV2"
  | "CANCEL_MEAL"
  | "MEAL_SLOT_OPTION"
  | "REPORT_ISSUE"
  | "PACK_UN_SUBSCRIBE"
  | "CANCEL_CULT_CLASS"
  | "UNDO_CANCEL_MEAL"
  | "DONE"
  | "RESCHEDULE_CHECKUP"
  | "CANCEL_CHECKUP"
  | "RENEW_CULT_PACK"
  | "PACK_INTRO"
  | "CANCEL_TC"
  | "RESCHEDULE_TC"
  | "BOOK_TC"
  | "SHOW_PRESCRIPTION"
  | "CANCEL_OFFER"
  | "EMAIL_LABREPORT"
  | "CANCEL_CART"
  | "RESCHEDULE_CART"
  | "RESCHEDULE_CARTV2"
  | "CHANGE_PACK_ADDRESS"
  | "CHANGE_PACK_SLOT"
  | "CHANGE_MEAL_ADDRESS"
  | "CHANGE_MEAL"
  | "RESUME_MEMBERSHIP"
  | "PAUSE_MEMBERSHIP"
  | "NAVIGATION"
  | "DOWNLOAD_OR_PLAY"
  | "RESUME_PACK"
  | "CANCEL_SUBSCRIPTION"
  | "SKIP_MEALS"
  | "DIAGNOSTIC_TEST_RESCHEDULE"
  | "CHANGE_CART_ADDRESS"
  | "UNDO_CANCEL_CART"
  | "LOG_CULT_SCORE"
  | "PLAY"
  | "MESSAGE"
  | "SHOW_ALERT_MODAL"
  | "CANCEL_CULT_MEAL_CLASS_MODAL"
  | "CANCEL_CULT_PACK_PAUSE"
  | "CULT_CLASS_DROPOUT"
  | "SHOW_CANCEL_ADDON"
  | "RESUME_CULT_MEMEBERSHIP"
  | "PAUSE_CULT_MEMEBERSHIP"
  | "SHOW_CLASS_RESCHEDULE_MODAL"
  | "SHOW_CULT_WAITLIST_NOTIFICATION_MODAL"

export type Icon =
  | ProductType
  | "DATE"
  | "LOCATION"
  | "REPORT"
  | "SYRINGE"
  | "AUTO_RENEW"
  | "CALENDAR"
  | "PERSON"
  | "DATE_TIME"
  | "CUTLERY"
  | "NO_DATE"
  | "SUB_USER_ICON"
  | "NO_COST_EMI"
  | "GYM_ICON"
  | "EMAIL"
  | "INSTRUCTION"
  | "INFO_PINK"
  | "TATA_NEU_LOGO"
  | "DOWNLOAD"

export type ManageOptionActionId =
  | "REPORT_ISSUE"
  | "CANCEL"
  | "RESCHEDULE"
  | "MESSAGE"
  | "JOIN_CALL"

export interface ManageOptions {
  displayText: string
  icon?: ManageOptionIcon
  secondaryIcon?: ManageOptionIcon
  options: ManageOptionPayload[]
  helperText?: string
  subHelper?: string
  subTitle?: string
  orientation?: Orientation
  info?: {
    title: string;
    subTitle: string;
  }
  creditCost?: number
}

export type MealActionType =
  | "MEAL_RESCHEDULE_OPTIONS"
  | "CANCEL_MEAL"
  | "CANCEL_CART"
  | "UNDO_CANCEL_MEAL"
  | "UNDO_CANCEL_CART"
  | "CHANGE_MEAL"
  | "TRACK_MEAL"
  | "PICKUP_MEAL"

export const POSSIBLE_FOOD_BOOKING_ACTION_TYPES: MealActionType[] = [
  "MEAL_RESCHEDULE_OPTIONS",
  "CANCEL_MEAL",
  "CANCEL_CART",
  "UNDO_CANCEL_MEAL",
  "UNDO_CANCEL_CART",
  "CHANGE_MEAL",
  "TRACK_MEAL",
  "PICKUP_MEAL"
]

export type ActionId = "FITBIT_OAUTH" | ManageOptionActionId

export interface PickerWidget extends WidgetView {
  title: string
  subTitle: string
  selectedOption: string
  action: Action
  defaultCountryCode?: string
  countriesData?: CountryData[],
  canChangeBuddy?: boolean
}

import { Media } from "@curefit/gymfit-common"
import { IDoctorAvailableSlot } from "../../care/CareDoctorSearchPageView"
import { AnnouncementView } from "../../announcement/AnnouncementViewBuilder"
import { IBreadCrumb } from "../../page/ondemand/OnDemandCommon"
import { TrialCardWidget } from "../../digital/TrialCardWidget"
import { CreditPillData } from "../../page/PageWidgets"
import CreditsView from "./CreditsViewWidget"
import { AugmentedOfflineFitnessPack } from "@curefit/pack-management-service-common"
export { Action, ActionType, ActionIcon, ViewType, ManageOptionIcon } from "@curefit/apps-common"

export interface ChainedAction extends Action {
  nextAction: Action
}

export interface ActionList extends Action {
  actions: Action[]
  icon?: ActionIcon
}
export interface SelectCenterAction extends Action {
  showHelp: boolean
  title: string
  showFavourite?: boolean
  productType: ProductType,
  queryParams?: any
}

export interface OgmPackOfferWidget extends WidgetView {
  title: string
  subTitle: string
  icon: string
  action: Action
}

export interface UserSelectionWidget extends WidgetView {
  title: string
  action: Action
  prefixText?: string
  selectedUserName: string
  dividerType?: string,
  header?: string
}

export interface EmptyStateWidget extends WidgetView {
  title: string
  subTitle: string
  image?: string
  action?: Action
}
export interface CenterAddressWidget extends WidgetView {
  header: {
    title: string;
  }
  mapUrl: string
  addressText: string
  latLong: LatLong
  footer?: string
}

export interface GymAccessInfoItem {
  title: string
  icons: Array<string>
  highlightType: string
  highlightTextInBlack?: string
  highlightTextInGold?: string
  notHighlightText?: string
}

export interface GymAccessInfoWidget extends WidgetView {
  header: string
  items: GymAccessInfoItem[]
  containerStyle?: any
}

export interface GymMedia extends Media {
  assetUrl?: string
}

export interface MediaData {
  media: GymMedia
  action?: Action
  fullScreenIcon?: string
}

export interface CenterSummaryWidget extends WidgetView {
  title: string
  subTitle: {
    text: string,
    color: string
  }
  mediaData: MediaData[]
  images: Media[]
  widgets?: WidgetView[]
  centerId?: string
  tagIcon?: string
  schedule?: CenterSchedule[]
  weeksSchedule?: CenterSchedule[]
  todaysSchedule?: string
  distanceFromSearchOrigin?: string
  subType?: string
  creditsData?: CreditsView
}

export interface CenterSchedule {
  title: string
  image?: string
  value: string
  dimText: boolean
  timing?: Timing[]
}

export interface Timing {
  startTime: number,
  endTime: number
}

export interface GoalSelectionDetailWidget extends WidgetView {
  title: string
  bullets: string[]
  showArrow: boolean
  action: Action
  gradientColors: string[]
  price: {
    priceCultValue?: string;
    value: string;
    subTitle?: string;
  }
}

export interface ActionSheet extends Action {
  selectedOption: number
  options: ActionSheetOption[]
  meta?: any
}

export interface ActionList extends Action {
  actions: Action[]
}

export interface ActionSheetOption {
  optionId?: string
  optionText: string
  optionSubText?: string
  payload?: any
}

export interface MealAction extends Action {
  productId: string
  productName: string
  image?: string
  customisable?: boolean
  mealSlot: {
    id: MenuType;
    name: string;
  }
  slot?: MenuType
  option?: any
  stock: number
  date: string
  price: ProductPrice
  maxCartSize: number
  offerId?: string
  offerIds?: string[]
  deliverySlot?: string
  clpTab?: EatCLPTabs
  listingBrand?: ListingBrandIdType
}
export interface PackAction extends Action {
  orderProduct: OrderProduct
  mealSlot: {
    id: MealSlot;
    name: string;
  }
}
export interface AccessoriesAction extends Action {
  productId: string
  offerId?: string
}
export interface ManageOptionPayload {
  type: ManageOption
  displayText: string
  isEnabled: boolean // To cater to showing the option like reschedule under manage option in old app and new app will skip based on the flag
  action?: string | Action | VMActionType
  meta?: any
  slots?: any
  deliverySlot?: string
  deliveryDate?: string
  orientation?: Orientation
  showDisabled?: boolean
  listingBrand?: string
}

export type BADGE = "VEG" | "NON_VEG"
export type TAG = "NEW"

export interface Header {
  title: string,
  titleFontSize?: number,
  subTitle?: string
  image?: string
  color?: string
  icon?: string
  action?: string | Action
  tag?: TAG
  seeMoreText?: string
  topAction?: {
    actionString: string;
    action: Action;
  },
  style?: any,
  titleProps?: any,
  rowStyling?: any,
  newCheckoutNavigation?: boolean,
  isExpandable?: boolean
  creditsData?: CreditsView
}

export interface ExpandableListHeader {
  titleWhenOpen: string
  titleWhenClosed: string
}

export interface InfoCard {
  id?: string
  title?: string
  date?: string
  state?: string
  subTitle?: string,
  subTitleFont?: string,
  subTitleColor?: string,
  imagesInTextProps?: ImagesInText
  textOrder?: string[]
  flexOneContainerStyle?: any
  subTitleFontSize?: number,
  image?: string
  imageV2?: {
    url: string,
    cloudinaryConfig: string[]
  }
  badge?: BADGE
  moreIndex?: number
  icon?: string,
  iconSize?: number,
  imageStyle?: any,
  showDivider?: boolean
  knowMore?: Action
  countParameters?: number
  number?: string,
  numberStyle?: any,
  textStyle?: any,
  cellStyle?: any,
  style?: any
  subtitleStyle?: any,
  doctorsComment?: string,
  testSuggestion?: string,
  isPrescriptionTests?: boolean,
  homeSampleCollectionAvailable?: string,
  isCareFlow?: boolean,
  cartAction?: Action,
  reportReadyEta?: number,
  productCode?: string,
  price?: ProductPrice,
  padding?: number,
  readMoreProps?: {
    textStyle: any
  },
  titleStyle?: any
  layoutProps?: {
    iconStyle?: any
  },
  items?: Array<string>
  extraWidth?: number
}

export interface GradientCard {
  title: string
  subTitle?: string
  subTitleFontSize?: number,
  gradientColors: string[]
  shadowColor: string
  icon: string
  action?: Action
}

export interface GradientCarouselCard extends GradientCard {
  moreIndex?: number
}

export type PackProgressProductType =
  | "FITNESS_SUBSCRIPTION"
  | "MIND_SUBSCRIPTION"

export interface PackProgress {
  startDate?: string
  endDate?: string
  total: number
  completed: number
  state: string
  type: ProductType | PackProgressProductType
  displayText?: string
  subTitle?: string
  title?: string
  noPadding?: boolean
  action?: Action
  countParameters?: number
  packColor?: string
  leftText?: string
  rightText?: string
  progressBarTextStyle?: object
  leftTextStyle?: object
  rightTextStyle?: object
  progressBarColor?: string
  isSplitView?: boolean
}

export interface DIYPackProgress extends PackProgress {
  resumeAction?: string
  resumeNewAction?: Action
}

export interface ActionCard extends InfoCard {
  cardAction?: Action | IssueDetailViewAction
  seeMoreAction?: Action
  action?: string
  seeMoreText?: string
  tryAction?: ActionCard
  detailAction?: Action
}

export interface DiagnosticCartSummaryDetails {
  testDetails: string,
  reportReadyEta: string,
  patientInfo: string,
  slotDetails: string,
  homeAddressDetails: string
}

export interface ColorCodedInfoCard extends InfoCard {
  color?: string
  showBullet?: boolean
  subTitleStyle?: any,
  titleStyle?: any,
  containerStyle?: any,
  tag?: {
    text: string;
    color: string;
    textColor?: string
  }
  highlightedTitle?: string,
  highlightedTitleStyle?: any,
  tagTextStyle?: any,
  topTitle?: string,
  topTitleStyle?: any
}

export type ResultState = "NORMAL" | "ABNORMAL"

export interface CheckupResult {
  state: ResultState
  stateTitle: string
  text?: string
}

export interface CheckupResultActionCard extends ActionCard {
  result?: CheckupResult
}

export type ActionSheetIcon =
  | "INSTRUCTION"
  | "DURATION"
  | "DATE"
  | "PICKUP_MEAL"

export interface ActionSheetWidget extends WidgetView {
  title?: string
  subTitle?: string
  action: ActionSheet
  isDisabled?: boolean
  icon?: ActionSheetIcon
}

export interface MembershipContextWidget extends WidgetView {
  title: string,
  description: string,
  action?: Action,
  analyticsDataOnClick?: any
}

export class PickupWidget implements WidgetView {
  widgetType: WidgetType = "PICKUP_WIDGET"
  qrCodeImage: string
  title: string
  subTitle?: string
  qrCodeString?: string
  constructor(
    qrCodeImage: string,
    address: UserDeliveryAddress,
    foodPackBookng?: FoodPackBooking,
    qrCodeString?: string
  ) {
    this.qrCodeImage = qrCodeImage
    this.qrCodeString = qrCodeString
    if (foodPackBookng && foodPackBookng.packType === "KIOSKPACK") {
      this.title = "Meal pickup code"
      this.subTitle = `Scan this QR code at the ${foodPackBookng.address.addressLine1
        } kiosk and pick up your order. Valid on only 1 item per meal slot.`
    } else {
      if (qrCodeImage || qrCodeString) {
        this.title = address.kioskType !== "CAFE" ? `Scan this QR code at the ${address.addressLine1
          } kiosk to pickup your order` : `Complete the biometric or QR code scan & pickup your order`
      }
      else
        this.title = `Your QR code will appear when your order is ready for pickup. Scan this code at the eat.fit kiosk to pickup your order `
    }
  }
}

export class PackDurationPickerWidget implements WidgetView {
  widgetType: WidgetType = "PACK_DURATION_PICKER_WIDGET"
  title: "Choose a plan"
  options: PackDurationPickerOption[] = []
  constructor(
    foodPack: FoodPack,
    deliveryChannel: DeliveryChannel,
    packOfferResponse?: FoodPackOffersResponseV2,
    subscriptionType?: SubscriptionType,
    weeklyWeekendsEnabled?: boolean,
    monthlyWeekendsEnabled?: boolean,
    numTickets?: number
  ) {
    const foodPackOptions: PackDurationPickerOption[] = _.map(
      foodPack.options.filter(option => {
        return subscriptionType
          ? option.subscriptionType !== undefined &&
          option.weekendsEnabled ===
          (option.subscriptionType === "WEEKLY"
            ? weeklyWeekendsEnabled
            : monthlyWeekendsEnabled)
          : option.subscriptionType === undefined
      }),
      option => {
        const packOfferAndPrice = OfferUtil.getFoodPackOfferAndPrice(
          foodPack,
          option,
          packOfferResponse
        )
        const offerIds = !_.isEmpty(packOfferAndPrice.offers)
          ? _.map(packOfferAndPrice.offers, offer => {
            return offer.offerId
          })
          : undefined
        let title = subscriptionType
          ? capitalizeFirstLetter(option.subscriptionType)
          : `${option.numTickets} Day Meal Pack`
        if (foodPack.packType === "KIOSKPACK") {
          title = option.numTickets + " meal plan"
        }
        const offerText =
          packOfferAndPrice.price.mrp !== packOfferAndPrice.price.listingPrice
            ? option.subscriptionType === "WEEKLY"
              ? "FIRST WEEK"
              : "FIRST MONTH"
            : undefined
        const packDurationPickerOption: PackDurationPickerOption = {
          orderProduct: {
            productId: foodPack.productId,
            quantity: 1,
            productType: "FOOD",
            option: {
              offerIds: offerIds,
              offerId: !_.isEmpty(offerIds) ? offerIds[0] : undefined,
              numTickets: option.numTickets,
              weekendEnabled:
                option.numTickets === 7 &&
                  foodPack.mealSlot !== "LUNCH" &&
                  foodPack.mealSlot !== "SNACKS"
                  ? true
                  : false, // not used in subscription
              isPack: true,
              numDays: option.numDays,
              subscriptionType: option.subscriptionType
            }
          },
          action: {
            actionType: "SELECT_DURATION",
            meta: {
              subscriptionType: option.subscriptionType,
              numTickets: option.numTickets
            }
          },
          offerText,
          weekendAction: undefined,
          isWeekendsEnabled:
            option.subscriptionType === "WEEKLY"
              ? weeklyWeekendsEnabled
              : monthlyWeekendsEnabled,
          isSelected: subscriptionType
            ? option.subscriptionType === subscriptionType &&
            option.numTickets === Number(numTickets)
            : option.numTickets === 7,
          numTickets: option.numTickets,
          subTitle:
            "Pause & change meal or address. Valid for " +
            option.numDays +
            " days",
          price: packOfferAndPrice.price,
          subscriptionType: option.subscriptionType,
          title: title
        }
        if (deliveryChannel !== "KIOSK" && deliveryChannel !== "CAFE" && foodPack.packType !== "KIOSKPACK") {
          if (option.subscriptionType === "WEEKLY") {
            let metaNumTickets = weeklyWeekendsEnabled ? 5 : 7
            if (!packDurationPickerOption.isSelected) {
              metaNumTickets = numTickets
            }
            packDurationPickerOption.weekendAction = {
              actionType: "TOGGLE_WEEKENDS",
              meta: {
                numTickets: metaNumTickets,
                weeklyWeekendsEnabled: !weeklyWeekendsEnabled,
                monthlyWeekendsEnabled: monthlyWeekendsEnabled
              }
            }
          } else {
            let metaNumTickets = monthlyWeekendsEnabled ? 22 : 30
            if (!packDurationPickerOption.isSelected) {
              metaNumTickets = numTickets
            }
            packDurationPickerOption.weekendAction = foodPack.tags?.indexOf("WEEKENDS_TOGGLE_NOT_SUPPORTED") >= 0 ? undefined : {
              actionType: "TOGGLE_WEEKENDS",
              meta: {
                numTickets: metaNumTickets,
                monthlyWeekendsEnabled: !monthlyWeekendsEnabled,
                weeklyWeekendsEnabled: weeklyWeekendsEnabled
              }
            }
          }
        }
        return packDurationPickerOption
      }
    )
    foodPackOptions.sort(
      (
        foodPackA: PackDurationPickerOption,
        foodPackB: PackDurationPickerOption
      ) => {
        return foodPackA.numTickets > foodPackB.numTickets ? -1 : 1
      }
    )
    this.options = foodPackOptions
  }
}

export class PackDurationWidget implements WidgetView {
  widgetType: WidgetType = "PACK_DURATION_WIDGET"
  title: string
  numDays: number
  offerText: string
  price: ProductPrice
  weekendAction: Action
  isWeekendsEnabled: boolean
  subscriptionType: SubscriptionType
  constructor(
    foodPack: FoodPack,
    foodPackOption: FoodPackOption,
    subscriptionType: SubscriptionType,
    monthlyWeekendsEnabled: boolean,
    weeklyWeekendsEnabled: boolean,
    numTickets?: number,
    packOfferResponse?: FoodPackOffersResponseV2
  ) {
    const packOfferAndPrice = OfferUtil.getFoodPackOfferAndPrice(
      foodPack,
      foodPackOption,
      packOfferResponse
    )
    this.title = capitalizeFirstLetter(foodPackOption.subscriptionType)
    this.numDays = foodPackOption.numDays
    this.offerText =
      packOfferAndPrice.price.mrp !== packOfferAndPrice.price.listingPrice
        ? foodPackOption.subscriptionType === "WEEKLY"
          ? "FIRST WEEK"
          : "FIRST MONTH"
        : undefined
    this.price = packOfferAndPrice.price
    this.isWeekendsEnabled =
      foodPackOption.subscriptionType === "WEEKLY"
        ? weeklyWeekendsEnabled
        : monthlyWeekendsEnabled
    this.weekendAction =
      foodPack.tags.indexOf("WEIGHT_LOSS") >= 0
        ? undefined
        : {
          actionType: "TOGGLE_WEEKENDS",
          meta: {
            numTickets: numTickets,
            weeklyWeekendsEnabled:
              subscriptionType === "WEEKLY"
                ? !weeklyWeekendsEnabled
                : weeklyWeekendsEnabled,
            monthlyWeekendsEnabled:
              subscriptionType === "MONTHLY"
                ? !monthlyWeekendsEnabled
                : monthlyWeekendsEnabled
          }
        }
    this.subscriptionType = subscriptionType
  }
}

export interface PackDurationPickerOption {
  orderProduct: {
    productId: string;
    quantity: Number;
    productType: ProductType;
    option: {
      offerId?: string;
      offerIds?: string[];
      numTickets: Number;
      weekendEnabled: boolean;
      isPack: boolean;
      numDays: Number;
      subscriptionType: SubscriptionType;
    };
  }
  isWeekendsEnabled: boolean
  subscriptionType: SubscriptionType
  weekendAction: Action
  action: Action
  title: string
  subTitle: string
  numTickets: Number
  offerText?: string
  price: ProductPrice
  isSelected: Boolean
}

export class PulseRentalRemainingWidget implements WidgetView {
  widgetType: "PULSE_RENTAL_REMAINING_WIDGET"
  title: string
  icon?: string
  shadingColor?: string
  remainingText: string
  remainingAmount: string | number
  constructor(title: string, remainingText: string, remainingAmount: string | number, shadingColor: string, icon?: string) {
    this.widgetType = "PULSE_RENTAL_REMAINING_WIDGET"
    this.title = title
    this.shadingColor = shadingColor
    if (icon) {
      this.icon = icon
    } else {
      this.icon = "PULSE_DEVICE"
    }
    this.remainingText = remainingText
    this.remainingAmount = remainingAmount
  }
}

export class PulseBenefitWidget implements WidgetView {
  widgetType: "PULSE_BENEFIT_WIDGET"
  title: {
    text: string
  }
  infolets: Array<{
    iconUrl: string,
    text: string,
    width?: number,
    height?: number,
  }>
  constructor(title: string, infoletArray: Array<{
    iconUrl: string,
    text: string,
    width?: number,
    height?: number,
  }>) {
    this.widgetType = "PULSE_BENEFIT_WIDGET"
    this.title = {
      text: title,
    }
    this.infolets = [...infoletArray]
  }
}

export class PulseTermsAndConditionsWidget implements WidgetView {
  title: string
  rightText: string
  detailedTerms: string
  widgetType: "PULSE_TERMS_AND_CONDITIONS_WIDGET"
  action: Action
  constructor(title: string, rightText: string, detailedTerms: string, action: Action) {
    this.title = title
    this.rightText = rightText
    this.detailedTerms = detailedTerms
    this.widgetType = "PULSE_TERMS_AND_CONDITIONS_WIDGET"
    this.action = action
  }
}

export class PromptUseFitcash implements WidgetView {
  widgetType: "PROMPT_USE_FITCASH"
  prompt: string
  fitcash: {
    icon: "FITCASH",
    description: number,
    action: Action
  }
  constructor(prompt: string, balance: number, action?: Action) {
    this.widgetType = "PROMPT_USE_FITCASH"
    this.prompt = prompt
    this.fitcash = {
      icon: "FITCASH",
      description: balance,
      action: action || {
        actionType: "NAVIGATION",
        url: "curefit://fitcash"
      }
    }
  }
}

export interface HIWGridWidget extends WidgetView {
  title: string
  items: InfoCard[]
}

export class SwitchWidget implements WidgetView {
  widgetType: WidgetType = "SWITCH_WIDGET"
  constructor(
    public title: string,
    public icon: Icon,
    public action: Action,
    public isEnabled: boolean,
    public subTitle?: string,
    public infoAction?: Action
  ) { }
}

export class SwitchWidgetV3 implements WidgetView {
  widgetType: WidgetType = "SWITCH_WIDGET_V3"
  constructor(
    public title: string,
    public action: Action,
    public isEnabled: boolean,
    public subTitle?: string,
    public infoAction?: Action,
  ) { }
}

export class AutoRenewWidget implements WidgetView {
  widgetType: WidgetType = "AUTO_RENEW_WIDGET"
  constructor(
    public title: string,
    public switchText: string,
    public action: Action,
    public isEnabled: boolean
  ) { }
}

export interface AboutProductWidget extends WidgetView {
  header: Header
  description: string
  video: string
  image: string
}

export interface ProductRowTagWidget extends WidgetView {
  header: Header
  items: InfoCard[]
}

export interface ProductColumnTagWidget extends WidgetView {
  header: Header
  items: InfoCard[]
}
export class PackInfoWidget {
  public widgetType: WidgetType = "PACK_INFO"

  constructor(
    public packId: string,
    public packName: string,
    public action: ActionCard,
    public title?: string
  ) { }
}

export interface ProductSummaryWidget extends WidgetView {
  title: string
  subTitle?: string
  image: string
  price?: ProductPrice
}

export interface ProductSummaryWidgetV2 extends WidgetView {
  title: string
  assets: MediaAsset[]
  productId: string
  productType: ProductType
  productPoints?: string[]
  dateMeta?: DatePickerWidget
  subTitle?: string
  help?: Action
  price?: ProductPrice
  manageOptions?: ManageOptions
  meta?: any
  progress?: PackProgress
  offerId?: string
  offerIds?: string[]
  offerTimerWidget?: WidgetView
  outerDateMeta?: DatePickerWidget
  priceBreakup?: {
    basePrice: number,
    tax: number
  },
  taxAndFeeItemList?: Array<Record<string, any>>,
  devPackInfo?: AugmentedOfflineFitnessPack // TODO: remove before PR merge
}

export interface CenterSelectionWidget extends WidgetView {
  title: string
  preferredCenterName?: string
  preferredCenterId?: number
  canChangeCenter: boolean
  prefixText?: string
  dividerType?: string
  showCustomSeparator?: boolean
  action?: Action
  isHeaderStyling?: boolean
  disabledToastText?: string
  disabledText?: boolean
  hasDividerBelow?: boolean,
  header?: string,
  showHighlightedText?: boolean,
  infoAction?: Action,
  colocatedCenter?: {
    title: string,
    action: Action
  },
  preferredWorkoutId?: number,
  preferredWorkoutName?: string
}

export interface DisplayUserDetailWidget extends WidgetView {
  header: string,
  mobileNumber?: string,
  hasDividerAbove?: boolean,
  profilePictureUrl: string,
  name: string,
  designation?: string,
  showElevatedCard?: boolean,
  address?: string,
  hasSeparatorAbove?: boolean
}

export interface TitleDescriptionWidget extends WidgetView {
  title: string,
  description: string,
  hasDividerAbove: boolean
}

export interface InviteUserWidget extends WidgetView {
  hasDividerAbove: boolean,
  title?: string,
  inviteeDetails?: string,
  action: Action
}

export interface MembershipTransferSummeryWidget extends WidgetView {
  header: string,
  transferFrom: {
    title: string,
    name?: string,
    imageUrl?: string,
    location: string,
  },
  transferTo: {
    title: string,
    name?: string,
    imageUrl?: string,
    location: string,
  },
  name?: string,
  imageUrl?: string,
  membershipTransferType: string
}

export interface TransferFeeSummaryWidget extends WidgetView {
  membershipDetails: {
    title: string,
    value: string | number
  }[],
  paymentDetails?: {
    title: string,
    value: string | number
  }[],
  membershipDaysReduce?: {
    title: string,
    value: string | number,
    action: Action
  },
  remainingMembershipDays?: {
    title: string,
    value: string | number
  }[]
}
export interface DiyDownloadAllSessionWidget extends WidgetView {
  title: string
  subTitle: string
  childOfImageOverlayCard?: boolean
  hasDividerAbove?: boolean
  diyContentDetails: DiyContentDetail[]
}

export interface DiyPlaySessionWidget extends WidgetView {
  isActive: boolean
  content: DiyContentDetail
  meta: DiyContentMeta
  nextActivity?: string
  previousActivity?: string
}

export class HighlightInfoWidget implements WidgetView {
  widgetType: WidgetType
  highlightText: string
  displayText: string
  knowMore: Action
  constructor(highlightText: string, displayText: string, knowMore: Action) {
    this.highlightText = highlightText
    this.displayText = displayText
    this.knowMore = knowMore
    this.widgetType = "HIGHLIGHT_INFO_WIDGET"
  }
}

export interface DatePickerWidget extends WidgetView {
  startDate: string
  selectedDate: string
  canChangeStartDate: boolean
  endDate: string
  title?: string
  calloutText?: string
  selectedDateText?: string
  titleColour?: string
  confirmActionType?: ActionType
}

export interface TransferMembershipTypeInfo {
  title: string
  description: string
  action: Action
  packTransferType: string
}

export interface TransferMembershipWidget extends WidgetView {
  separatorText: string
  hasDividerAbove: boolean
  title: string
  transferMembershipType?: TransferMembershipTypeInfo[]
}

export interface InfoSeeMoreWidget extends WidgetView {
  title: string,
  seemore: Action
}

export interface DiyShareCard extends WidgetView {
  header: {
    title: string,
    subTitle: string,
    backgroundImageUrl: string,
    noPaddingTop?: boolean,
  },
  metrics: ClassMetric[],
  user: {
    userId: string,
    firstName: string,
    profilePictureUrl: string
  },
  isReportV2: boolean,
  isMoment: boolean,
  isReportIncomplete: boolean,
  fitnessReportFilters: {
    [key: string]: {
      text: string,
      emoji?: string
    }
  }[],
  imageUrl: string,
  imageFilter: string,
  productType: ProductType,
  fulfilmentId: string,
  shareAction?: Action,
  achievement?: string,
  hasDividerBelow?: boolean,
  containerStyle?: object,
}

export interface PostWorkoutDIY extends IPostWorkoutDiyWidget, WidgetView { }

export interface ClassMetric {
  title: string,
  data: string,
  units: string,
  headerColor?: string
}

export class DiySummaryWidget {
  public widgetType: WidgetType = "DIY_SUMMARY"
  public productType: ProductType
  public title: string
  public subTitle: string
  public content: {
    id: string;
    image: string;
    type: string;
    format: string;
    URL: string;
    absoluteUrl: string;
  }
  public exercies?: string[]
  constructor() { }
}

export class DiyPackSummaryWidget {
  public widgetType: WidgetType = "DIY_PACK_SUMMARY"

  public title: string
  public packId: string
  public subTitle: string
  public productType: ProductType
  public content: {
    id?: string;
    image: string;
    video: string;
    type?: string;
    format?: string;
    URL?: string;
    absoluteUrl?: string;
  }
  public packProgress: PackProgress
  public manageOptions?: {
    displayText: string;
    options: ManageOptionPayload[];
  }
  public meta?: any
  public childOfImageOverlayCard?: boolean
  public hasDividerBelow?: boolean
  public description?: string
  public imagesInTextProps?: ImagesInText
  constructor() { }
}

export class ReminderWidget {
  public widgetType: WidgetType = "REMINDER_WIDGET"
  public packId?: string
  public title: string
  public subTitle: string
  public toggleState: boolean
  disabled?: boolean
  public action: any
  public expanded: boolean
  public childOfImageOverlayCard?: boolean
}

export class DayPickerWidget {
  public widgetType: WidgetType = "DAY_PICKER_WIDGET"
  public productId: string
  public preferredDays: number[]
  public isExpanded?: boolean
  public productType?: string
}

export class TimePickerWidget {
  public widgetType: WidgetType = "TIME_PICKER_WIDGET"
  public packId?: string
  title: string
  subTitle?: string
  preferredTime: HourMin
  errorString?: string
}

export class DayTimePickerWidget {
  public widgetType: WidgetType = "DAY_TIME_PICKER_WIDGET"
  public productId: string
  public preferredDays: number[]
  preferredTime: HourMin
  public hasDividerAbove?: boolean
  public childOfImageOverlayCard?: boolean
  errorString?: string
}

export interface DaySelection {
  day: string
  selected: boolean
}

export class ImageWidget {
  public widgetType: WidgetType = "IMAGE_WIDGET"
  constructor(public action: string, public image: string) { }
}

export class ManageOptionsWidget {
  public widgetType: WidgetType = "MANAGE_OPTIONS_WIDGET"
  public invertStyle?: boolean
  public isDisabled?: boolean
  public orientation?: Orientation
  public childOfImageOverlayCard?: boolean
  public hasDividerBelow?: boolean
  public hasDividerAbove?: boolean
  constructor(
    public manageOptions: ManageOptions,
    public meta?: any,
    isDisabled?: boolean,
    childOfImageOverlayCard?: boolean,
  ) {
    this.isDisabled = isDisabled
    this.orientation = manageOptions.orientation
    if (childOfImageOverlayCard == true) {
      this.hasDividerAbove = true
      this.childOfImageOverlayCard = true
    }
  }
}

export class ManageOptionsWidgetV2 {
  public widgetType: WidgetType = "MANAGE_OPTIONS_WIDGET_V2"
  public title: string
  public moreAction?: Action
  public style?: any
  public icon?: string
  public headerAction?: Action
  public headerActionIcon?: string
  public isLocked?: boolean
  public headerActionIconStyle?: ViewStyle & { [styleProp: string]: any }
  public titleStyle?: any
  public sceneStyle?: any
  public tags?: string[]
}


export type ListSubType =
  | "SMALL"
  | "MEDIUM"
  | "LARGE"
  | "BULLET"
  | "CLP_LARGE"
  | "MAGAZINE_OFFER"
  | "DETAIL"
  | "ACTION_ROW"
  | "INFO_CELL"
  | "INFO_ROW"
  | "GARDIENT_CARD"
  | "NUMBERED"
  | "CREDIT"
  | "MAGAZINE_CELL"
  | "DYNAMIC_ICON"
  | "GRADIENT_CAROUSEL_CARD"
  | "MEDIUM_CARD"
  | "DAILY_MEAL_WIDGET_CARD"
  | "ACTION_CELL_ROW"
  | "ACTION_DETAIL_ROW"
  | "CUSTOM_ROW"
  | "GRID"

export type GridSubType = "SQUARE" | "ICON" | "LANDSCAPE" | "GARDIENT_CARD" | "SMALL_ICON"

export class ProductListWidget implements WidgetView {
  public widgetType: WidgetType = "PRODUCT_LIST_WIDGET"
  public analyticsData?: any
  public hasDividerBelow?: boolean
  public noTopPadding?: boolean
  public noBottomPadding?: boolean
  public styles?: any
  public style?: any
  public showDivider?: boolean
  public collapsable?: boolean
  public layoutProps?: any
  public iconBackgroundOpacity?: any
  public showSquareIcons?: boolean
  public footerAction?: Action
  constructor(
    public type: ListSubType,
    public header: Header,
    public items:
      | InfoCard[]
      | ActionCard[]
      | GradientCard[]
      | GradientCarouselCard[]
      | DailyMealWidget[]
      | InfoItem[],
    public footer?: ActionCard,
    public isHorizontal?: boolean,
    public orientation?: Orientation,
    public hideSepratorLines: boolean = false,
    public numCols?: number,
    public backgroundColor?: string,
    public action?: Action,
    public dividerType?: string,
    public homeCollectionAvailable?: string,
  ) { }
}

export class ShareActionWidgetV2 implements WidgetView {
  public widgetType: WidgetType = "SHARE_ACTION_WIDGET_V2"
  public analyticsData?: any
  public layoutProps?: any
  public showCopyIcon?: boolean
  public referralLink?: CfTextData
  public title?: CfTextData
  public actionList?: Action[]
}

export class CenterHeaderWidget implements WidgetView {
  public widgetType: WidgetType = "CENTER_HEADER_WIDGET"
  public analyticsData?: any
  public layoutProps?: any
  public title?: CfTextData
  public subtitle?: CfTextData
}

export class CfMediaWidget implements WidgetView {
  public widgetType: WidgetType = "CF_MEDIA_WIDGET"
  public analyticsData?: any
  public layoutProps?: any
  public hasDivideBelow?: boolean
  public hasDividerTop?: boolean
  public mediaData?: {
    mediaUrl?: string;
    type?: string;
    width?: number;
    height?: number;
    topPadding?: number;
    bottomPadding?: number;
  }
}

export class CfTextData {
  public text?: string
  public color?: string
  public maxLine?: string
  public alignment?: string
  public typeScale?: string
  public letterSpacing?: number
  public opacity?: number
  public richText?: boolean
}

export class BillingAddressWidget implements WidgetView {
  public widgetType: WidgetType = "BILLING_ADDRESS_WIDGET"
  public analyticsData?: any
  public hasDividerBelow?: boolean
  public noTopPadding?: boolean
  public noBottomPadding?: boolean
  public styles?: any
  public style?: any
  public showDivider?: boolean
  public layoutProps?: any
  public showSquareIcons?: boolean
  constructor(
      public billingPlaceholderInfo: any,
      public orientation?: { subheader: string; chevronIcon: string; icon: string; header: string } | {},
      public startDateInfo?: any,
  ) { }
}

export class StartDateWidget implements WidgetView {
  public widgetType: WidgetType = "START_DATE_WIDGET"
  public analyticsData?: any
  public hasDividerBelow?: boolean
  public noTopPadding?: boolean
  public noBottomPadding?: boolean
  public styles?: any
  public style?: any
  public showDivider?: boolean
  public layoutProps?: any
  public showSquareIcons?: boolean
  constructor(
    public startDateInfo: any,
  ) { }
}

export class TrialClassMissedWidget implements WidgetView {
  public widgetType: WidgetType = "PRODUCT_LIST_WIDGET"
  public title?: string
  public options: any[]
  public subTitle: string
}

export class AssessmentScoreView implements WidgetView {
  public widgetType: WidgetType = "ASSESSMENT_SCORE_VIEW"
  public gradientView: {
    colors: string[];
    title: string;
    total: number;
    resultCount: number;
    resultValue: number;
  }
  public ranges: {
    color: string;
    name: string;
  }[]
}

export class ProductPricingWidget implements WidgetView {
  public widgetType: WidgetType = "PRODUCT_PRICING_WIDGET"
  public header?: {
    title: string;
  }
  public footerText?: string
  public sections: ProductPricingSection[]
  public orientation?: Orientation
  dividerType?: string
  style?: ViewStyle
}

export class ProductPricingSection {
  title?: string
  action?: Action
  highlightText?: string
  selected?: boolean
  type: "EXPANDED" | "RECURRING" | "INTRODUCTIONARY" | "SINGLE_SELECTION"
  value: PricingWidgetExpandedValue | PricingWidgetRecurringValue[] | PricingSingleSelectionValue
}

export class ProductPricingWidgetV2 extends ProductPricingWidget {
  public widgetType: WidgetType = "PRODUCT_PRICING_WIDGET_V2"
}

export class PricingSingleSelectionValue {
  title: string
  action: Action
  selected: boolean
}

export class PricingWidgetExpandedValue {
  title: string
  price: ProductPrice
  data: {
    title: string;
    price: ProductPrice;
  }[]
}

export class PricingWidgetRecurringValue {
  isDisabled?: boolean
  title: string
  subTitle?: string
  description?: string
  price: ProductPrice & {
    showFreeForZeroPrice?: boolean
  }
  productId?: string
  priceMeta?: string
  selected: boolean
  action: Action
  infoAction?: Action
  isMultilinePrice?: boolean
}

export class PricingWidgetRecurringValueV2 extends PricingWidgetRecurringValue {
  offers?: OfferData[]
}
export class BenefitsWidget implements WidgetView {
  public widgetType: WidgetType = "BENEFITS_WIDGET"
  public title: string
  public description: string
  public tags: string[]
  public orientation?: Orientation
  public hideSepratorLines?: boolean
}

export class ListWidget implements WidgetView {
  public widgetType: WidgetType = "LIST_WIDGET"
  public hideSepratorLines: boolean = false
  public headerWidget: WidgetView
  constructor(
    public widgets: WidgetView[],
    public isHorizontal?: boolean,
    public header?: WidgetView
  ) {
    this.headerWidget = header
  }
}

export class CenterInfoWidget implements WidgetView {
  public widgetType: WidgetType = "CENTER_INFO_WIDGET"
  public style?: any
  constructor(
    public id: number,
    public name: string,
    public action: string,
    public address: Address,
    public mapUrl: string
  ) { }
}

export class AddItemHeaderWidget {
  public widgetType: WidgetType = "ADD_ITEM_HEADER_WIDGET"
  constructor(
    public title: string,
    public items: { title: string; icon: ManageOptionIcon }[]
  ) { }
}

export class ListHeaderWidget implements WidgetView {
  public widgetType: WidgetType = "LIST_HEADER_WIDGET"
  constructor(public title: string, public subTitle?: string) { }
}

export type ImageType = "MAGAZINE" | "THUMBNAIL"
export interface CardItem {
  title: string
  subTitle: string
  image: string
  imageType: ImageType
  action: Action
}

export interface MealCardItem extends CardItem {
  isVeg: boolean
  quantity: number
}
export interface HorizontalScrollCardWidget extends WidgetView {
  items: CardItem[]
}
export class ProductGridWidget {
  public widgetType: WidgetType = "PRODUCT_GRID_WIDGET"
  public containerStyle?: any
  constructor(
    public type: GridSubType,
    public header: Header,
    public items: InfoCard[] | ActionCard[] | GradientCard[],
    footer?: InfoCard,
    public orientation?: Orientation,
    public hideSepratorLines: boolean = false
  ) { }
}

export class RoundedImageGridWidget {
  public widgetType: WidgetType = "ROUNDED_IMAGE_GRID_WIDGET"
  constructor(
    public header: WidgetHeader,
    public items: RoundedImageGridItem[],
    public imageTextColor?: String
  ) {
  }
}

export class SearchGymsResponse {
  constructor(
    public searchAndFilter: GymSearchAndFilter,
    public gymCards: GymCard[],
    public emptyState: { text: string, icon: string },
    public meta?: any
  ) { }
}

export interface GymSearchAndFilter {
  searchPlaceholder: string
  isSearchEnabled: boolean
  isFilterEnabled: boolean
  gymFilters: GymFilter[]
}

export interface GymCard {
  gymName: string
  id: string
  extraAction?: Action
  location: string
  images: string[]
  cardAction: Action,
  icon: string,
  address: {
    latLong: {
      lat: number,
      long: number
    }
  },
  creditsData?: CreditsView
  description?: CardDescription[]
}

export interface GymFilter {
  displayType: "TOGGLE_FILTER" | "HORIZONTAL_LIST_FILTER"
  dataType: "AMENITIES" | "LOCALITY" | "TYPE" | "OPEN_ONLY"
  title: string
  filters?: {
    id: string
    name: string
    selected: boolean
  }[]
  toggleSelected?: boolean
}

export interface GymLocalitySelector {
  header: Header
  gpsSearch?: {
    title: string
    icon: string
    action?: Action
  }
  searchBar?: {
    title: string
    icon: string
  }
  localities: {
    title: string
    localityId: number
    isSelected: boolean
    action?: Action
  }[]
  onDismissAction?: Action
}

export class QRWidget implements WidgetView {
  widgetType: WidgetType = "QR_WIDGET"
  data: {
    header?: {
      title: string
    }
    qrCodeInfo: {
      type: "STATIC" | "DYNAMIC"
      qrCodeString: string
      refreshUrl?: string
      refreshInterval?: number
      refreshIcon?: string
      refreshText?: string
    }
    footer?: {
      title: string
      description?: string
    }
    sessionsLeftText?: string
    eventData?: any
    creditsFooter?: CreditsFooter
    lockQrDescription?: string
  }
  containerStyle?: any
  creditsData?: CreditsView
  eventData?: any
  showWidgetDivider?: boolean
}

export class ExpandableInfoWidget implements WidgetView {
  widgetType: WidgetType = "EXPANDABLE_INFO_WIDGET"
  data: {
    header?: {
      title: string
      style?: any
    },
    title: string
    titleStyle?: any
    subTitle?: string
    itemsHighlight?: string
    expandedItems:
      {
        title: string
        subTitle?: string
      }[]
  }
}

export interface CenterOtherDetailsItem {
  type: "EMAIL" | "TIME"
  title: string
  subTitle: string
}

export class CenterOtherDetailsWidget {
  public widgetType: WidgetType = "CENTER_OTHER_DETAILS_WIDGET"
  public header: string
  constructor(public data: CenterOtherDetailsItem[], header: string) {
    this.header = header
  }
}

export interface InfoItem {
  text?: string
  icon?: string
  textStyle?: RNTextStyle
  iconStyle?: ViewStyle
  style?: any
}

export class ExpandableProductListWidget {
  public widgetType: WidgetType = "EXPANDABLE_PRODUCT_LIST_WIDGET"
  constructor(
    public type: ListSubType,
    public header: ExpandableListHeader,
    public items: InfoCard[] | ActionCard[],
    footer?: InfoCard,
  ) { }
}

export class ProductListCustomRowWidget {
  public widgetType: WidgetType = "PRODUCT_LIST_WIDGET"
  constructor(
    public type: ListSubType,
    public items: InfoItem[],
    public noTopPadding?: boolean,
    public noBottomPadding?: boolean
  ) { }
}

export class GiftVoucherWidget {
  public widgetType: WidgetType = "REFER_TO_WIDGET"
  constructor(
    public heading: {
      text: string,
      textStyle: RNTextStyle,
      icon: string
    },
    public domain: {
      text: string,
      textStyle?: RNTextStyle,
      style?: ViewStyle,
    },
    public items: {
      head?: string,
      description: string,
      key?: string | number
    }[],
    public action?: Action[],
    public style?: ViewStyle,
    public containerStyle?: ViewStyle,
    public itemHeadTextStyle?: RNTextStyle,
    public itemDescriptionTextStyle?: RNTextStyle,
    public headingContainer?: ViewStyle,
    public footer?: {
      icon: string;
    }
  ) { }
}

export class CalloutWidget {
  public widgetType: WidgetType = "CALLOUT_WIDGET"
  public orientation: Orientation
  public hideSeperatorLines: boolean = false
  public backgroundColor: string
  public title: string
  public style?: {
    scene?: any,
    title?: any
    subTitle?: any
    navArrow?: any
  }
  public gradientColors: string[]
  public action?: Action
  constructor(
    public subTitle: string,
    userAgent?: UserAgent,
    hideSeperatorLines?: boolean,
    backgroundColor?: string,
    title?: string,
    style?: any,
    action?: Action,
    gradientColors?: string[]
  ) {
    if (userAgent === "DESKTOP") {
      this.orientation = "RIGHT"
    }
    if (hideSeperatorLines) {
      this.hideSeperatorLines = hideSeperatorLines
    }
    if (backgroundColor) {
      this.backgroundColor = backgroundColor
    }
    this.title = title
    this.style = style
    this.gradientColors = gradientColors
    this.action = action
  }
}

export interface OfferData {
  title?: string
  description: string
  tnc: string[]
  tncURL?: string
  tncIcon?: string
}

export interface IconDescriptionWidget extends WidgetView {
  dividerType?: string
  showDivider?: boolean
  paddingTop?: boolean
  actions?: Action[]
}

export class OfferCalloutWidget implements WidgetView {
  public widgetType: WidgetType = "OFFER_CALLOUT_WIDGET"
  public containerStyle?: ViewStyle
  public offerTextStyle?: RNTextStyle
  public offerIconStyle?: any
  public gradientColors?: String[]
  public margin?: Number
  public tncIconStyle?: any
  public offerInnerContainer?: any
  public backgroundColor?: string
  public titleStyle?: any
  constructor(
    public title: string,
    public offers: OfferData[],
    public orientation?: Orientation,
    public hideSeperatorLines?: boolean,
    public icon?: string,
    public offerIconType?: string,
    public style?: any
  ) {
    this.title = title
    this.offers = offers
    this.orientation = orientation
    this.hideSeperatorLines = hideSeperatorLines
    this.icon = icon
    this.offerIconType = offerIconType
    this.offerTextStyle = style?.offerTextStyle
    this.offerIconStyle = style?.offerIconStyle
    this.tncIconStyle = style?.tncIconStyle
    this.offerInnerContainer = style?.offerInnerContainer
    this.backgroundColor = style?.backgroundColor
    this.gradientColors = style?.gradientColors
    this.titleStyle = style?.titleStyle
  }
}

export class DescriptionWidget implements WidgetView {
  public widgetType: WidgetType = "DESCRIPTION_WIDGET"
  public showDivider: boolean = true
  public showBullet?: boolean = true
  public dividerType?: string = "LARGE"
  public hasDividerBelow?: boolean = true
  public iconType?: string
  public containerStyle?: any
  public showWidgetDivider?: boolean = false
  public seeMore?: string
  public seeMoreAction?: Action
  public orientation?: Orientation
  public productType?: ProductType
  public rowStyle?: any
  constructor(
    public descriptions: ActionCard[] | InfoCard[],
    public meta?: any,
    public tnc?: string[]
  ) { }
}

export class NoCostEmiWidget implements WidgetView {
  public widgetType: WidgetType = "INFO_WIDGET"
  constructor(
    public title: string,
    public subTitle: string,
    public icon: string,
    public action: Action,
    public calloutText: string
  ) { }
}

export interface SeeMore {
  text: string
  seeMore: Action
}

export class TextWidget implements WidgetView {
  public widgetType: WidgetType = "TEXT_WIDGET"
  public showDivider: boolean = true
  constructor(
    public title: string,
    public description: string,
    public orientation?: Orientation
  ) { }
}

interface GradientCardMultiValue {
  gradientColors: string[]
  items: {
    title?: string;
    subtitle: string;
  }[]
}

export interface WeightLoseJourneyValue {
  title: string
  value: {
    weight: number;
    unit: string;
  }
}

export class GradientCardWithBottomActionWidget implements WidgetView {
  public widgetType: WidgetType = "GRADIENT_CARD_WITH_TOP_ACTION_WIDGET"
  public showDivider: boolean = true
  public header: Header
  public gradientCard?: GradientCardMultiValue
  public bottomAction: {
    action?: Action;
    backgroundColor: string;
    text: string;
    actionString?: string;
    weightLoseJourney?: WeightLoseJourneyValue[];
  }
}

export class ColorCodedDescriptionWidget {
  public widgetType: WidgetType = "COLOR_CODED_DESCRIPTION_WIDGET"
  constructor(public descriptions: ColorCodedInfoCard[]) { }
}

export class HealthcheckupListWidget {
  public widgetType: WidgetType = "LIST_CHECKUP_WIDGET"
  constructor(public header: Header, public items: CheckupResultActionCard[]) { }
}

export class DeliveryAddressWidget {
  public widgetType: WidgetType = "DELIVERY_ADDRESS_WIDGET"
  public area: DeliveryAreaView
  public address: UserAddressView
  public place: PlaceData
  public isWithinServicableCity: boolean
  public isInServicableArea: boolean
  public action: string
  constructor(preferredLocation: PreferredLocation, pageFrom: string) {
    this.area = preferredLocation.defaultArea
      ? new DeliveryAreaView(preferredLocation.defaultArea)
      : undefined
    this.area = preferredLocation.area
      ? new DeliveryAreaView(preferredLocation.area)
      : this.area
    this.address = preferredLocation.address
      ? new UserAddressView(preferredLocation.address)
      : undefined
    this.place = preferredLocation.placeData
      ? preferredLocation.placeData
      : undefined
    this.isWithinServicableCity = preferredLocation.isWithinServicableCity
    this.isInServicableArea = preferredLocation.isInServicableArea
    let selectAddressAction = "curefit://selectaddress?pageFrom=" + pageFrom
    if (preferredLocation.latLong) {
      selectAddressAction =
        selectAddressAction +
        "&lat=" +
        preferredLocation.latLong.lat +
        "&long=" +
        preferredLocation.latLong.long
    }
    this.action = selectAddressAction
  }
}

export class ProductFeedbackWidget {
  public widgetType: WidgetType = "PRODUCT_FEEDBACK_WIDGET"
  public question: String
  public feedbackId: string
  public icons = FeedbackUtil.getFeedbackIcons()
  public orientation: Orientation
  public headerAlignment = "LEFT"
  public subtitle?: string
  public sceneStyle?: object
  public containerStyle?: object
  public useV2Style?: boolean
  public style?: object
  constructor(
    feedbackQuestion: string,
    feedbackId: string,
    userAgent?: UserAgent,
    icons?: string[],
    headerAlignment?: string,
    subtitle?: string,
    useV2Style?: boolean,
    sceneStyle?: object,
    containerStyle?: object,
    style?: object
  ) {
    this.question = feedbackQuestion
    this.feedbackId = feedbackId
    this.useV2Style = useV2Style
    if (icons) {
      this.icons = icons
    }
    if (userAgent === "DESKTOP") {
      this.orientation = "RIGHT"
    }
    if (headerAlignment) {
      this.headerAlignment = headerAlignment
    }
    if (subtitle) {
      this.subtitle = subtitle
    }
    if (sceneStyle) {
      this.sceneStyle = sceneStyle
    }

    if (containerStyle) {
      this.containerStyle = containerStyle
    }
    if (style) {
      this.style = style
    }
  }
}

export interface InfoWidget extends WidgetView {
  title?: string
  subTitle?: string
  icon?: Icon
  seperatorLine?: boolean
  orientation?: Orientation
  calloutText?: string
  tagsTitle?: string
  tags?: string[]
  actions?: Action[]
  action?: Action
  actionText?: string
}

export type MarkerResultState = "NORMAL" | "BORDERLINE" | "HIGH/LOW"

export interface MarkerSummaryResult {
  outOfRangeRatio: string
  statusText: string
}

export interface TabWidget extends WidgetView {
  header?: Header
  tabs: {
    tabLabel: string;
    title: string;
    subTitle: string;
  }[]
}

export class MarkerLegend {
  title: string
  color: string
  constructor(title: string, color: string) {
    this.title = title
    this.color = color
  }
}
export class MarkerSummaryWidget {
  public widgetType: WidgetType = "HEALTHCHECKUP_MARKER_SUMMARY_WIDGET"
  title: string
  subTitle: string
  labtestId: string
  image: string
  markerSummaryResult: MarkerSummaryResult
  states: any
  constructor() { }
}
export class MarkerLegendWidget {
  public widgetType: WidgetType = "MARKER_LEGEND_WIDGET"
  title: string
  markerLegendStates: MarkerResultState[]
  constructor(title: string, markerstates: MarkerResultState[]) {
    this.title = title
    this.markerLegendStates = markerstates
  }
}

export interface MarkerResultView {
  value: number
  valueUnits: string
  type: MarkerResultState
}
export interface MarkerReferenceRange {
  minVal: number
  maxVal: number
  type: MarkerResultState
}
export interface MarkerListView {
  title: string
  subtitle: string
  minRange: number
  maxRange: number
  markerResult: MarkerResultView
  legendRanges: MarkerReferenceRange[]
}
export class MarkerResultListWidget {
  public widgetType: WidgetType = "MARKER_RESULT_LIST_WIDGET"
  header: Header
  markers: MarkerListView[]
  constructor(header: Header, markersList: MarkerListView[]) {
    this.header = header
    this.markers = markersList
  }
}

export class CareCheckoutSummaryWidget implements WidgetView {
  public widgetType: WidgetType
  public title: string
  public subtitle: string
  public imageUrl: string
  public doctorDetails: Doctor
  public actionUrl?: string
  public price?: ProductPrice
  public orientation?: Orientation
  public profileImageAction?: Action
  public priceDetails?: PriceComponent[]
  public consultationMode?: CONSULTATION_MODE
  public topSpacing?: number
  public containerStyle?: any
  public tagView?: {
    text: string
    bgColor: string
    icon: string
  }
  public footer: {
    title: string;
    subtitle: string;
    subTitleStyle?: any;
    timestamp: number;
    footerShadow: boolean;
    center?: {
      centerName: string;
      icon?: string;
      isVideo?: boolean;
      isAudio?: boolean;
      actionString?: string;
      actionUrl?: string;
    };
    changeAction?: Action
  }
  public cancellationInfo?: {
    text: string
    icon: string
  }
  public hasDividerBelow?: boolean
  constructor(
    userContext: UserContext,
    user: User,
    productCode: string,
    consultation: ConsultationOrderResponse,
    footerShadow: boolean,
    offline: boolean,
    bookingId: string,
    billingInfo?: BillingInfo,
    bookingDetail?: BookingDetail,
    isLivePtNewCheckoutSupported?: boolean
  ) {
    const tz = userContext.userProfile.timezone
    const userAgent = userContext.sessionInfo.userAgent
    const isAudioCall = consultation?.channel === "AUDIO"
    // Remove this after appversion 7.16
    this.title = consultation.doctor ? `${consultation.doctor.name}` : ""
    this.subtitle = consultation.doctor ? consultation.doctor.qualification : ""
    this.imageUrl = CareUtil.isAnxietyTherapy(productCode) ? null : consultation.doctor ? consultation.doctor.displayImage : ""
    // End of Remove this after appversion 7.16
    this.doctorDetails = {
      ...consultation.doctor,
      experience: CareUtil.isTherapyOnlyDoctorType(bookingDetail?.consultationOrderResponse?.doctorType) ? undefined : consultation?.doctor?.experience,
      displayImage: isLivePtNewCheckoutSupported || CareUtil.isAnxietyTherapy(productCode) ? null : consultation.doctor ? consultation?.doctor?.displayImage : ""
    }
    this.widgetType =
      userAgent === "DESKTOP"
        ? "ORDER_SUMMARY_WIDGET"
        : "CARE_CHECKOUT_SUMMARY_WIDGET"
    this.profileImageAction = {
      actionType: "SHOW_DOCTOR_DETAILS_MODAL"
    }
    const vertical = CareUtil.getVerticalForConsultation(consultation.consultationProduct.doctorType)
    const isLivePTSessionConsultation = CareUtil.isLivePTSessionConsultation(consultation)
    const isLiveSGTSessionConsultation = CareUtil.isLiveSGTSessionConsultation(consultation)
    if (userAgent === "DESKTOP") {
      this.price = {
        listingPrice: _.get(billingInfo, "total", 0),
        mrp: _.get(billingInfo, "mrp", 0),
        currency: _.get(billingInfo, "currency", "INR")
      }
      this.orientation = "LEFT"
      this.actionUrl = ActionUtil.teleconsultationSingle(
        userContext,
        productCode,
        _.get(consultation, "consultationProduct.urlPath", ""),
        bookingId,
        undefined,
        vertical

      )
      if (consultation && consultation.consultationProduct) {
        this.consultationMode = consultation.consultationProduct.consultationMode
      }
    } else {
      if (footerShadow) {
        const appointmentId = _.get(
          bookingDetail,
          "consultationOrderResponse.id",
          undefined
        )
        this.actionUrl = ActionUtil.teleconsultationSingle(
          userContext,
          productCode,
          _.get(consultation, "consultationProduct.urlPath", ""),
          bookingId,
          appointmentId,
          vertical
        )
      }
      const center = consultation.center
      const isExternal = center && center.isExternal
      const startTimeStamp = consultation.startTime
      const endTimeStamp = consultation.endTime
      const duration = TimeUtil.diffInMinutes(
        tz,
        TimeUtil.formatDateInTimeZone(
          tz,
          TimeUtil.parseDateFromEpoch(startTimeStamp),
          "YYYY-MM-DD HH:mm:ss"
        ),
        TimeUtil.formatDateInTimeZone(
          tz,
          TimeUtil.parseDateFromEpoch(endTimeStamp),
          "YYYY-MM-DD HH:mm:ss"
        )
      )
      if (consultation && consultation.consultationProduct) {
        this.consultationMode = consultation.consultationProduct.consultationMode
      }
      const isCheckoutChangesSupported = (isLiveSGTSessionConsultation && AppUtil.isLiveSGTCheckoutChangesSupported(userContext))
      this.footer = {
        title: CareUtil.getCheckoutConsultTitle(consultation, offline, isExternal, tz),
        subtitle: CareUtil.isTherapyConsultation(consultation) || CareUtil.isTransformSessionConsultation(consultation)
          ? `${duration} mins session`
          : CareUtil.isPTSessionConsultation(consultation)
            ? CareCheckoutSummaryWidget.getPTSessionSubTitle(consultation, tz)
            : isLivePTSessionConsultation || isLiveSGTSessionConsultation
              ? CareCheckoutSummaryWidget.getLivePTSessionSubTitle(consultation, tz, isCheckoutChangesSupported)
              : `For ${consultation.patient.name}`,
        timestamp: startTimeStamp,
        footerShadow: footerShadow,
      }
      if (offline) {
        this.footer.center = {
          centerName: center.name,
          actionString: "VIEW MAP",
          icon: center.isExternal ? "EXTERNAL" : undefined,
          actionUrl: `curefit://externalDeepLink?placeUrl=${center.placeUrl}`
        }
      } else if (CareUtil.showCareFooterInCheckout(consultation, userContext)) {
        this.footer.center = {
          centerName: CareUtil.isPTSessionConsultation(consultation) || isLivePTSessionConsultation || isLiveSGTSessionConsultation
            ? "Video Call"
            : (isAudioCall ? `You will receive a phone call on +91 - ${CareUtil.getAudioModeName(bookingDetail, user)}` : "Audio/Video Call"),
          isVideo: isAudioCall ? false : true,
          isAudio: isAudioCall
        }
      }
      if (isCheckoutChangesSupported && (isLiveSGTSessionConsultation || isLivePTSessionConsultation)) {
        this.tagView = {
          text: isLivePTSessionConsultation ? "PERSONAL TRAINING" : "ONLINE GROUP CLASS",
          bgColor: "#888e9e",
          icon: "/image/icons/livept/video_white.png"
        }
        this.containerStyle = {
          BackgroundColor: "#f2f4f8"
        }
        this.footer.subTitleStyle = {
          marginTop: 0,
          lineHeight: 18,
          paddingBottom: 30,
          color: "#3888ff",
          fontFamily: "BrandonText-Medium",
        }
        this.topSpacing = 30
      }
      const tenant = ["CARE", "MINDFIT"].includes(consultation?.consultationProduct?.tenant)
      if (tenant && (
        bookingDetail?.consultationOrderResponse?.appointmentActionsWithContext ||
        bookingDetail?.consultationOrderResponse?.cancellationThresholdEpoch ||
        bookingDetail?.consultationOrderResponse?.rescheduleThresholdEpoch
      )) {
        this.hasDividerBelow = false
        this.cancellationInfo = CareUtil.getCancellationInfo(userContext, bookingDetail, true)
        if (this?.footer?.footerShadow) {
          this.footer.footerShadow = false
        }
      }
    }
  }

  private static getPTSessionSubTitle(
    consultation: ConsultationOrderResponse,
    tz: Timezone
  ): string {
    let title = `${TimeUtil.formatEpochInTimeZone(tz, consultation.startTime, "ddd D MMM")}, ${TimeUtil.formatEpochInTimeZone(tz, consultation.startTime, "h:mm a")}`
    title += `\nFor ${consultation.patient.name}`
    return title
  }

  private static getLivePTSessionSubTitle(consultation: ConsultationOrderResponse, tz: Timezone, isCheckoutChangesSupported: boolean): string {
    const centerName = CareUtil.isPTSessionConsultation(consultation) || CareUtil.isLivePTSessionConsultation(consultation) || CareUtil.isLiveSGTSessionConsultation(consultation) ? "Online Video Call" : "Online Video/Audio Call"
    let subTitle = `${TimeUtil.formatEpochInTimeZone(tz, consultation.startTime, "ddd Do MMM, h:mm a")} - ${TimeUtil.formatEpochInTimeZone(tz, consultation.endTime, "h:mm a")}`
    if (!isCheckoutChangesSupported) {
      subTitle = `${subTitle}\n${centerName}`
    }
    return subTitle
  }
}

export class CareBookingSummaryWidget implements WidgetView {
  public widgetType: WidgetType
  public title: string
  public subtitle: string
  public items: {
    title: string;
    value: string[];
  }
  constructor(title: string, subtitle: string, items: any) {
    this.widgetType = "CARE_BOOKING_SUMMARY_WIDGET"
    this.title = title
    this.subtitle = subtitle
    this.items = items
  }
}

export class ActionableCardWidget implements WidgetView {
  public widgetType: WidgetType
  public title: string
  public subTitle: string
  public action: string
  constructor(title: string, subTitle: string, action: string) {
    this.title = title
    this.subTitle = subTitle
    this.action = action
    this.widgetType = "ACTIONABLE_CARD_WIDGET"
  }
}

export class VideoAlertCardWidget implements WidgetView {
  public widgetType: WidgetType
  public title: string
  public subTitle: string
  public action: Action
  constructor(title: string, subTitle: string, action: Action) {
    this.title = title
    this.subTitle = subTitle
    this.action = action
    this.widgetType = "VIDEO_ALERT_CARD_WIDGET"
  }
}

export class NavigationCardWidget implements WidgetView {
  public widgetType: WidgetType
  public title: string
  public subTitle: string
  public action?: string
  public cardAction: Action
  public showArrow: boolean
  public orientation: Orientation
  public hideSepratorLines: boolean = true
  public secondaryAction?: Action
  public handwrittenPrescription?: boolean
  public cardIcon?: string
  constructor(
    title: string,
    subTitle: string,
    cardAction: Action,
    showArrow: boolean,
    action?: string,
    userAgent?: UserAgent,
    hideSepratorLines?: boolean,
    secondaryAction?: Action,
    handwrittenPrescription?: boolean,
    cardIcon?: "PRESCRIPTION" | "MESSAGE" | "LABTEST" | "TATA_NEU_LOGO"
  ) {
    this.title = title
    this.subTitle = subTitle
    this.action = action
    this.widgetType = "NAVIGATION_CARD_WIDGET"
    this.cardAction = cardAction
    this.showArrow = showArrow
    this.hideSepratorLines = hideSepratorLines
    this.secondaryAction = secondaryAction
    this.handwrittenPrescription = handwrittenPrescription
    this.cardIcon = cardIcon
    if (userAgent === "DESKTOP") {
      this.orientation = "RIGHT"
    }
  }
}

export class DataListingWidget implements IDataListingWidget {
  public widgetType: AppWidgetType
  public title: string
  public listType: DataListingType
  public items: string[]
  public imageType?: string
  public hideSepratorLines?: boolean

  constructor(
    title: string,
    listType: DataListingType,
    items: string[],
    imageType?: string,
    hideSepratorLines: boolean = false
  ) {
    this.title = title
    this.listType = listType
    this.items = items
    this.widgetType = "DATA_LISTING_WIDGET"
    if (imageType) this.imageType = imageType
    this.hideSepratorLines = hideSepratorLines
  }
}

export class DataListingWidgetV2 implements WidgetView {
  public widgetType: AppWidgetType
  public title: string
  public listType: DataListingType
  public items: IListingItem[]
  public imageType?: string
  public hideSepratorLines?: boolean

  constructor(
    title: string,
    listType: DataListingType,
    items: IListingItem[],
    imageType?: string,
    hideSepratorLines: boolean = false
  ) {
    this.title = title
    this.listType = listType
    this.items = items
    this.widgetType = "DATA_LISTING_WIDGET_V2"
    if (imageType) this.imageType = imageType
    this.hideSepratorLines = hideSepratorLines
  }
}

export class LabTestListingWidget implements WidgetView {
  public widgetType: WidgetType
  public header: Header
  public items: ProductListWidget[]
  public isCardItem?: boolean
  constructor(
    header: Header,
    items: ProductListWidget[],
    isCardItem?: boolean
  ) {
    this.header = header
    this.items = items
    this.isCardItem = isCardItem ? true : false
    this.widgetType = "LABTEST_LISTING_WIDGET"
  }
}

export class DescriptionWithFooterActionWidget implements IDescriptionWithFooterActionWidget {
  public widgetType: WidgetType
  public imageType?: string
  public hideSepratorLines?: boolean
  public isCard?: boolean
  public highlightText?: string
  constructor(
    public title: string,
    public subTitle: string,
    public actionTitle: string,
    public actionUrl?: string,
    public action?: Action,
    public description?: string,
    imageType?: string,
    hideSepratorLines: boolean = false,
    isCard: boolean = false,
    highlightText?: string
  ) {
    this.widgetType = "DESCRIPTION_WITH_FOOTER_WIDGET"
    if (imageType) this.imageType = imageType
    this.hideSepratorLines = hideSepratorLines
    this.isCard = isCard
    this.highlightText = highlightText
  }
}

export class PrescriptionSummaryWidget implements WidgetView {
  public widgetType: WidgetType
  public title: string
  public subtitle: string
  public doctorName?: string
  public action?: Action
  public emailAction?: Action
  public productCode?: string
  public doctorDetailsAction?: Action
  constructor(
    title: string,
    subtitle: string,
    doctorName?: string,
    action?: Action,
    productCode?: string,
    emailAction?: Action,
    doctorDetailsAction?: Action
  ) {
    this.title = title
    this.subtitle = subtitle
    this.widgetType = "PRESCRIPTION_SUMMARY_WIDGET"
    this.doctorName = doctorName
    this.action = action
    this.productCode = productCode
    this.emailAction = emailAction
    this.doctorDetailsAction = doctorDetailsAction
  }
}

export class InstructionsWidget implements WidgetView {
  public widgetType: WidgetType
  public header?: Header
  public orientation?: Orientation | "VERTICAL"
  public instructions: {
    iconType: string;
    text: string;
  }[]
  public hideHeaderBullet?: boolean
  public instructionMap?: ConsultationInstructionResponse[]
  public showHeader: boolean = false
  public blurEnabled: boolean = false
  constructor(
    instructions: any,
    userAgent?: UserAgent,
    hideHeaderBullet?: boolean,
    instructionMap?: ConsultationInstructionResponse[],
    showHeader?: boolean,
    header?: Header,
    orientation?: Orientation | "VERTICAL",
    blurEnabled?: boolean
  ) {
    this.instructions = instructions
    if (userAgent === "DESKTOP") {
      this.orientation = "RIGHT"
    }
    this.instructionMap = instructionMap
    this.hideHeaderBullet = hideHeaderBullet ? true : false
    this.widgetType = "INSTRUCTIONS_WIDGET"
    if (showHeader !== undefined) {
      this.showHeader = showHeader
    }
    if (header) {
      this.header = header
    }
    this.orientation = orientation
    this.blurEnabled = blurEnabled
  }
}

export class TestBookingSummaryWidget implements WidgetView {
  public widgetType: WidgetType
  public items: ActionCard[]
  public orientation: Orientation
  constructor(items: any) {
    this.items = items
    this.widgetType = "TEST_BOOKING_SUMMARY_WIDGET"
  }
}

export class CartDiagnosticsCheckoutSummaryWidget implements ICartDiagnosticsCheckoutSummaryWidget {
  public widgetType: WidgetType
  public summaryDetails: IDiagnosticsConfirmationSummaryDetails
  public orientation: Orientation

  constructor(summaryDetails: IDiagnosticsConfirmationSummaryDetails, orientation?: Orientation) {
    this.widgetType = "CART_DIAGNOSTICS_CHECKOUT_SUMMARY_WIDGET"
    this.summaryDetails = summaryDetails
    this.orientation = orientation
  }
}

export class TestDetailWidget implements IDiagnosticTestListDetailWidget {
  public widgetType: WidgetType
  public items: DiagnosticTestItem[]
  public fulfilledDetails: any
  public testListAction: Action
  public selectedTestCodes: string[]
  public actions: Action[]
  constructor(items: DiagnosticTestItem[], fulfilledDetails: any, testListAction: Action, actions?: Action[]) {
    this.widgetType = "DIAGNOSTICS_TEST_DETAIL_WIDGET"
    this.items = items
    this.fulfilledDetails = fulfilledDetails
    this.testListAction = testListAction
    this.selectedTestCodes = []
    this.actions = actions
  }
}

export class DiagnosticCartListingWidget implements IDiagnosticCartListingWidget {
  public widgetType: WidgetType
  public items: IDiagnosticCartTestItem[]
  public testListAction: Action
  public patientData: any
  public testsSummary: any
  constructor(items: IDiagnosticCartTestItem[], testListAction: Action, patientData: any, testsSummary: any) {
    this.widgetType = "DIAGNOSTICS_CART_LISTING_WIDGET" as any
    this.items = items
    this.testListAction = testListAction
    this.patientData = patientData
    this.testsSummary = testsSummary
  }
}

export class EmptyDiagnosticCartListingWidget implements IEmptyDiagnosticCartListingWidget {
  public widgetType: AppWidgetType

  constructor(public data: any, public action: Action) {
    this.widgetType = "EMPTY_DIAGNOSTIC_CART_LISTING_WIDGET" as any,
    this.data = data,
    this.action = action
  }
}
export class DiagnosticTestItem implements IDiagnosticTestItem {
  productCode: string
  title: string
  subTitle: string
  price: ProductPrice
  offerIds?: string[]
  countTestsText?: string
  reportReadyEta?: string
  isSelected: boolean
  action?: Action
}

export class DiagnosticCartTestItem {
  productCode: string
  title: string
  price: ProductPrice
  countTestsCtaText: string
  action: Action
}


export interface Tag {
  title: string
}
export class ColorCodedTagsWidget implements WidgetView {
  public widgetType: WidgetType
  public header: Header
  public tags: Tag[]

  constructor(header: Header, tags: Tag[]) {
    this.header = header
    this.tags = tags
    this.widgetType = "COLOR_CODED_TAGS_WIDGET"
  }
}

export type DosageColorCodes = "#32d3ff" | "#ffa119" | "#ff3278"
export const DosageColors: string[] = ["#32d3ff", "#ffa119", "#ff3278"]

export interface MedicineView {
  type: Medicinetype
  title: string
  brandDetail: string
  subtitle?: string
  dosageText?: string
  intakeCondition?: string
  tags?: string[]
  additionalInstructions?: string[]
  allIntakeDetails?: string
  doctorsComment?: string
  medicineDetails?: MedicineDetails
}

export interface MedicineDetails {
  title: string,
  chemicalComposition: string,
  header: string,
}
export class PrescribedMedicationWidget implements IPrescribedMedicationWidget {
  public widgetType: AppWidgetType
  public header: WidgetHeader
  public items: MedicineView[]
  public isV2Supported: boolean
  constructor(header: WidgetHeader, items: MedicineView[], isV2Supported?: boolean) {
    this.header = header
    this.items = items
    this.widgetType = "PRESCRIBED_MEDICATION_WIDGET"
    this.isV2Supported = isV2Supported
  }
}

export class CultNoShowPopupWidget implements WidgetView {
  public widgetType: WidgetType = "CULT_NO_SHOW_POPUP"
  constructor(
    public title: string,
    public noShowsRemainingText: string,
    public options: CultNoShowOption[]
  ) {
    this.title = title
    this.options = options
    this.noShowsRemainingText = noShowsRemainingText
  }
}

export class DoctorDetailWidget implements WidgetView {
  public widgetType: WidgetType
  public displayImage: string
  public name: string
  public id: number
  public price?: number
  public qualification?: string
  public experience?: string
  public action: Action
  public weeklySchedule?: WeeklySchedule[]
  public detailedQualification?: string
  public description?: string
  public footerDetails?: any
  public containerStyle?: any
  public hasDividerBelow?: boolean
  public rowStyle?: any
  public richDescriptions?: [{
    text: string
    color: string
  }]
  public subSpecialities?: string
  public isPreferredDoctor?: boolean
  public earliestAvailability?: DoctorAvailability
  public earliestAvailabilityList?: DoctorAvailability[]
  public orientation?: Orientation
  public fallsInExploreBucket?: boolean
  public isFeatured?: boolean
  public videoUrl?: string
  public noOfSessionsText?: string

}

export class ChronicCareDoctorDetailWidget {
  public widgetType: WidgetType
  public displayImage: string
  public name: string
  public id: number
  public qualification?: string
  public experience?: string
  public action: Action
}

export class ChronicCareDoctorProfileWidget {
  public widgetType: WidgetType
  public displayImage: string
  public name: string
  public subTitle: string
}

export class DoctorWhatsAppChatWidget {
  public widgetType: WidgetType
  public title: string
  public subTitle: string
  public chatAction: Action
}

export class ChronicCareCreativeWidget {
  public widgetType: WidgetType
  public aspectRatio: number
  public creativeType: string
  public marginLeft: number
  public marginRight: number
  public marginTop: number
  public marginBottom: number
}

export class ChronicCarePackCheckoutWidget {
  public widgetType: WidgetType
  public value: number
  public valueType: string
  public subHeading: string
  public heading: string
  public price: string
  public gradientColor?: string[]
  public packType: string
  public renderingInfo: any
}

export class ChronicCareCGMCheckoutWidget {
  public widgetType: WidgetType
  public cgmCount: number
  public heading: string
  public mrp: string
  public listingPrice: string
  public gradientColor?: string[]
  public cgmText: string
  public subTitle?: string
}

export class SfEComCartAddressWidget {
  public widgetType: WidgetType
  public deliveryByTitle: string
  public deliveryEtaFrom: Date
  public deliveryEtaTo: Date
  public discountAmount: string
}

export class SfDiagnosticCartAddressWidget {
  public widgetType: WidgetType
}

export class SfEComCartListWidget {
  public widgetType: WidgetType
  public cartItems: any[]
  public addMoreEnabled: boolean
  public note: string

  // below fields are depricated
  public cartShipments: {
    deliveryByTitle: string
    deliveryEtaFrom: Date
    deliveryEtaTo?: Date
    deliveryChargePrice: {
      price: number
      currency: string
    }
    freeDeliveryMessage: string
    moreAmountToBuyForFreeDelivery: number
    shipmentProducts: {
      product: {
        title: string
        images: any
        productCode: string
        unitQuantity: number
        unit: string
        displayUnitQuantity: number
        displayUnit: String
        priceDetails: {
            mrp: number
            listingPrice: number
            currency: string
        },
      }
      orderQuantity: number
      isCouponApplicable?: boolean
    }[]
  }[]
  public couponData: {
    couponOptionEnabled: boolean,
    appliedCouponId?: string;
    couponMessage?: string;
    isValid?: boolean;

    // New keys
    isEligible: boolean
    couponCode: string
    description?: string
    offerDiscount?: number
    eligibleProductCodes?: string[]
    message?: string;
  }
  public priceDetails: PriceComponent[]
  public finalPrice: ProductPrice
  public freeDeliveryOverrideMessage?: string
  public fulfilledByText: string
}

export class SfDiagnosticCartListWidget {
  public widgetType: WidgetType
  public cartShipments: {
    sampleCollectionTitle: string
    startDate: number
    endDate?: number
    shipmentProducts: {
      product: {
        title: string
        imgUrl: string
        productCode: string
        priceDetails: {
            mrp: number
            listingPrice: number
            currency: string
        },
      }
      orderQuantity: number
    }[]
  }
}

export class SfEComCartBillingWidget {
  public widgetType: WidgetType
  public priceDetails: PriceComponent[]
  public finalPrice: ProductPrice
  public freeDeliveryOverrideMessage?: string
  public moreAmountToBuyForFreeDelivery?: number
  public fulfilledByText: string
  public shippingPolicyLink: string
  public refundPolicyLink: string
  public policyMessage: string
  public analyticsData?: any
}

export class SfEComCartCouponWidget {
  public widgetType: WidgetType
  public title: string
  public couponData: {
    couponOptionEnabled: boolean
    appliedCouponId?: string
    couponMessage?: string
    isValid?: boolean

    // New keys
    isEligible: boolean
    couponCode: string
    description?: string
    offerDiscount?: number
    eligibleProductCodes?: string[]
    savingsAmount: string
    message?: string;
  }
  public couponRequestPayload: SfEcomCartCouponsPayload
}

export class SfEcomCartCouponsPayload {
  public userId: number
  public orderedProductsList: SfEcomCarCouponOrderProduct[]
  public totalCartValue: number
  public shippingCharge: number
  public source: string
}

export class SfEcomCarCouponOrderProduct {
  public productCode: string
  public quantity: number
  public price: number
}

export class SfWidgetSeparator {
  public widgetType: WidgetType
  public height?: number
  public width?: number
  public gradientColors?: string[]
}

export class SfEComCartListItemWidget {
  public widgetType: WidgetType
  public product: SfEcommerceProduct
  public noReturnText: string
  public orderQuantity: number
}

export class SfEComPeopleBoughtWidget {
  public widgetType: WidgetType
  public title: string
  public widgets: any[]
}

export class SfEComProductItemSmallWidget {
  public widgetType: WidgetType
  public product: any
  public cartAction: any
}

export class SfExperienceCenterWorkFlowWidget {
  public widgetType: WidgetType
  public title: string
  public workflows: {
    iconUrl: string
    title: string
    timeText: string
    description: string
  }[]

  constructor(userContext: UserContext, consultationOrderResponse: ConsultationOrderResponse) {
    const tz = userContext.userProfile.timezone
    const additionalWaitingTimeInMillis = 900000
    this.widgetType = "SF_EXPERIENCE_CENTER_WORKFLOW_WIDGET" as any,
    this.title = "What's included?"
    const step1 = {
      iconUrl: "image/chroniccare/experiencecenter/vital.png",
      title: "Vitals checkup",
      timeText: `${TimeUtil.formatEpochInTimeZone(tz, consultationOrderResponse.startTime - additionalWaitingTimeInMillis, "hh:mm")} - ${TimeUtil.formatEpochInTimeZone(tz, consultationOrderResponse.startTime, "hh:mm A")}`,
      description: "Vital checkup includes a comprehensive assessment of your vital signs.",
    }
    this.workflows = []
    this.workflows.push(step1)
    const step2 = {
      iconUrl: "image/chroniccare/experiencecenter/medicine.png",
      title: "Diabetes Expert consultation",
      timeText: `${TimeUtil.formatEpochInTimeZone(tz, consultationOrderResponse.startTime, "hh:mm")} - ${TimeUtil.formatEpochInTimeZone(tz, consultationOrderResponse.endTime, "hh:mm A")}`,
      description: "Discussion about your health concerns, symptoms, and medical history.",
    }
    this.workflows.push(step2)
    const step3 = {
      iconUrl: "image/chroniccare/experiencecenter/tech.png",
      title: "Experience the tech",
      timeText: `${TimeUtil.formatEpochInTimeZone(tz, consultationOrderResponse.endTime, "hh:mm")} - ${TimeUtil.formatEpochInTimeZone(tz, consultationOrderResponse.endTime + additionalWaitingTimeInMillis, "hh:mm A")}`,
      description: "Explore the exciting innovative advancements for diabetes reversal.",
    }
    this.workflows.push(step3)
  }
}

export class SfExperienceCenteFullBodyTestWidget {
  public widgetType: WidgetType
  public title: string
  public tests: string[]

  constructor() {
    this.widgetType = "SF_EXPERIENCE_CENTER_FULL_BODY_TEST_LIST_WIDGET" as any,
    this.title = "Below tests including 70+ parameters "
    this.tests = SF_FREEMIUM_FULL_BODY_TEST_LIST
  }
}

export class ChronicCareDoctorDescriptionWidget {
  public widgetType: WidgetType
  public title: string
  public regNumber: string
  public qualification: string
  public languages: string
  public detailedDescription: string
}
export class SlotTimerWidget {
  public widgetType: WidgetType
  public countdownTime: number
  public header?: string
  public slotExpired?: string
  public meta?: any
}

export class DoctorProfileBenefitsInfoWidget {
  public widgetType: WidgetType = "DOCTOR_PROFILE_BENEFITS_INFO_WIDGET"
  public image: string
}

export class DoctorDetailWidgetV2 extends DoctorDetailWidget {
  public actionQueryParam?: string
  public priceInfo?: ProductPrice
  public isFooterDisabled?: boolean
  public doctorAvailableSlots?: IDoctorAvailableSlot[]
}
export class NCDetailWidget extends DoctorDetailWidgetV2 {
  public doctorAvailableSlots?: IDoctorAvailableSlot[]
}
export class WeeklySchedule {
  centerName: string
  action: string
  online: boolean
  offline: boolean
  days: string[]
}

export class CartBaseProductSummaryWidget implements WidgetView {
  public widgetType: WidgetType = "CART_BASE_PRODUCT_SUMMARY_WIDGET"
  public showDivider?: boolean = undefined
  productType?: ProductType
  constructor(
    public title: string,
    public subTitle: string,
    public description?: string,
    public icon?: string,
    public price?: ProductPrice,
    public orientation?: Orientation,
    public imageUrl?: string
  ) {
    this.title = title
    this.subTitle = subTitle
    this.description = description
    this.icon = icon
    this.orientation = orientation
    this.imageUrl = imageUrl ? imageUrl : undefined
  }
}

export class TransformPaymentDetailsWidget implements WidgetView {
  public widgetType: WidgetType = "TRANSFORM_PAYMENT_DETAILS_WIDGET"
  constructor(
      public title: string,
      public footer: string,
      public suffix: string,
      public packHeader: string,
      public hexColor: string,
      public duration: string,
      public productType: ProductType,
      public priceDetails: PriceComponent[],
      public finalPrice: ProductPrice,
  ) {
    this.title = title
    this.footer = footer
    this.suffix = suffix
    this.packHeader = packHeader
    this.hexColor = hexColor
    this.duration = duration
    this.productType = productType
    this.priceDetails = priceDetails
    this.finalPrice = finalPrice
  }
}

export class CoachInfoWidget implements WidgetView {
  public widgetType: WidgetType = "COACH_INFO_WIDGET"
  constructor(
      public coachInfoCards: CoachInfoCard[],
      public layoutProps?: any,
      public horizontalPadding?: number,
  ) {
    this.coachInfoCards = coachInfoCards
    this.layoutProps = layoutProps
    this.horizontalPadding = horizontalPadding
  }
}

export interface CoachInfoCard {
  tagTitle: string,
  coachInfoItems: CoachInfoItem[],
}

export interface CoachInfoItem {
  title: string,
  subtitle: string,
  imageUrl: string,
}

export class TransformNoCostEMIWidget implements WidgetView {
  public widgetType: WidgetType = "TRANSFORM_NO_COST_EMI_WIDGET"
  constructor(
      public title: string,
      public items: TransformEMIItem[],
  ) {
    this.title = title
    this.items = items
  }
}

export interface TransformEMIItem {
  imageUrl: string,
  title: string,
  symbol: string,
  price: string,
  suffix: string,
  action: Action,
}

export class CardClickableWidget implements WidgetView {
  public widgetType: WidgetType = "CARD_CLICKABLE_WIDGET"
  constructor(
      public title: string,
      public subtitle: string,
      public action: Action,
      public layoutProps?: any,
  ) {
    this.title = title
    this.subtitle = subtitle
    this.action = action
    this.layoutProps = layoutProps
  }
}

export class CheckoutActionsWidget implements WidgetView {
  public widgetType: WidgetType = "CHECKOUT_ACTIONS_WIDGET"
  constructor(
      public stepItems: CheckoutStepItem[],
      public actionsMap: any,
      public isTransformProduct: boolean,
  ) {
    this.stepItems = stepItems
    this.actionsMap = actionsMap
    this.isTransformProduct = isTransformProduct
  }
}

export class CheckoutActionsWidgetV2 implements WidgetView {
  public widgetType: WidgetType = "CHECKOUT_ACTIONS_WIDGET_V2"
  constructor(
      public stepItems: CheckoutStepItem[],
      public actionsMap: any,
      public subCategoryCode: string,
  ) {
    this.stepItems = stepItems
    this.actionsMap = actionsMap
    this.subCategoryCode = subCategoryCode
  }
}

export class PlaySelectCheckoutActionsWidget implements WidgetView {
  public widgetType: WidgetType = "PLAY_SELECT_CHECKOUT_ACTIONS_WIDGET"
  constructor(
      public stepItems: CheckoutStepItem[],

      public currentAction: Action,
      public subCategoryCode: string,
  ) {
    this.stepItems = stepItems
    this.currentAction = currentAction
    this.subCategoryCode = subCategoryCode
  }
}

export interface CheckoutStepItem {
  id: string,
  iconName?: string,
  imageUrl?: string,
  hintText?: string,
  nextId?: string,
  visible: boolean,
  showDivider?: boolean,
  action?: Action,
}

export class CartPatientSummaryWidget implements WidgetView {
  public widgetType: WidgetType = "CART_PATIENT_SUMMARY_WIDGET"
  public orientation: Orientation
  constructor(
    public title: string,
    public subTitle: string,
    public icon: string,
    public seperatorLine: boolean = true
  ) {
    this.title = title
    this.subTitle = subTitle
    this.icon = icon
    this.seperatorLine = seperatorLine
  }
}

export class PackDetailWidget implements WidgetView {
  public widgetType: WidgetType
  public icon: string
  public title: string
  public description: string
}

export function getOffersWidget(
  title: string,
  offers: (OfferV2Lite | OfferV2)[],
  userAgent?: UserAgent,
  hideOrientation?: boolean,
  style?: any
): OfferCalloutWidget {
  const offerDataList: OfferData[] = _.map(offers, offer => {
    if (offer.displayContexts?.includes("NONE") || !offer.description) return undefined
    return {
      title: offer.title,
      description: offer.description,
      tnc: offer.tNc,
      tncURL: offer.tNcUrl,
      tncIcon: "/image/gymfit/right_arrow_3.png",
    }
  })
  const filteredOffers = _.filter(offerDataList, function (offer) { return !!offer })
  let orientation: Orientation
  let hideSepratorLines: boolean = true
  if (userAgent === "DESKTOP" && !hideOrientation) {
    orientation = "RIGHT"
  }
  if (userAgent === "MBROWSER" || userAgent === "DESKTOP") {
    hideSepratorLines = false
  }
  return new OfferCalloutWidget(
    title,
    filteredOffers,
    orientation,
    hideSepratorLines,
    null,
    null,
    style
  )
}

export class FitClubPackOfferWidget implements WidgetView {

  public widgetType: WidgetType = "FITCLUB_PACK_OFFER_WIDGET"
  public offerTitle: string
  public offerDescription: string
  public action: Action
  constructor(offer: OfferV2, userContext: UserContext) {
    this.offerTitle = offer.title
    this.offerDescription = offer.description
    if (offer.priceOfferDetails.value > 0) {
      this.action = {
        title: "Activate",
        actionType: "NAVIGATION",
        url: undefined
      }
    }
  }

}

export class AutoRenewalWidget implements WidgetView {
  public widgetType: WidgetType
  public icon: Icon
  public title: string
  public action: Action
  public infoAction: Action
  public isEnabled: Boolean

  constructor(isEnabled?: boolean) {
    this.widgetType = "SWITCH_WIDGET"
    this.icon = "AUTO_RENEW"
    this.title = "Auto renew with payTm"
    this.isEnabled = !_.isNil(isEnabled) ? isEnabled : false
    this.action = {
      actionType: "TOGGLE_CART_AUTO_RENEW",
      isEnabled: !_.isNil(isEnabled) ? !isEnabled : true
    }
    this.infoAction = {
      actionType: "SHOW_ALERT_MODAL",
      meta: {
        title: "Auto Renew with payTm",
        subTitle:
          "Do away with the hassle of paying every week! Turn Auto renew on, and your pack will be auto renewed through payTm",
        actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
      }
    }
  }
}

export class CultMembershipRewardWidget implements WidgetView {
  public widgetType: WidgetType = "CULT_MEMBERSHIP_REWARDS_WIDGET"
  constructor(
    public title: string,
    public highlightedText: string,
    public description: { line1: string; line2?: string }
  ) {
    this.title = title
    this.description = description
    this.highlightedText = highlightedText
  }
}

export interface ChallengeViewItem {
  viewType: "SINGLE" | "MULTIPLE"
  title: string
  subTitle?: string
  icon?: string
  children?: {
    title: string;
    subTitle: string;
    heading: string;
    subTitleColor?: string;
  }[]
}

export class CultConsistencyRewardWidget implements WidgetView {
  public widgetType: WidgetType = "CULT__MEMBERSHIP_CONSISTENCY_WIDGET"
  constructor(
    public header: Header,
    public viewItems: ChallengeViewItem[],
    public seeMoreColor?: string
  ) { }
}

export class DiagnosticsConfirmationSummaryWidget implements WidgetView {
  public widgetType: WidgetType = "DIAGNOSTICS_CONFIRMATION_SUMMARY_WIDGET"
  constructor(
    public title: string,
    public subTitle1: string,
    public subTitle2: string,
    public icon: string,
    public action?: Action
  ) {
    this.title = title
    this.subTitle1 = subTitle1
    this.subTitle2 = subTitle2
    this.icon = icon
    this.action = action
  }
}

export class DiagnosticsConfirmationV2Widget implements WidgetView {
  public widgetType: WidgetType
  constructor(public summaryDetails: any, public orderDetails: any, public paymentDetails: any, public headerDetails: any) {
    this.widgetType = "DIAGNOSTICS_CONFIRMATION_V2_WIDGET" as any,
    this.summaryDetails = summaryDetails,
    this.orderDetails = orderDetails,
    this.paymentDetails = paymentDetails,
    this.headerDetails = headerDetails
  }
}

export class DiagnosticsConfirmationDesktopWidget implements IDiagnosticsConfirmationDesktopWidget {
  public widgetType: IWidgetType = "DIAGNOSTICS_CONFIRMATION_DESKTOP_WIDGET"

  constructor(public imageUrl: string, public headingDetails: IDiagnosticConfirmationHeading, public testDetails: IDiagnosticConfirmationTestDetails, public paymentDetails: IDiagnosticConfirmationPaymentDetails) {
    this.testDetails = testDetails
    this.imageUrl = imageUrl
    this.headingDetails = headingDetails
    this.paymentDetails = paymentDetails
  }
}

export class HCUBundleConfirmationSummaryWidget implements WidgetView {
  public widgetType: WidgetType = "HCU_CONFIRMATION_SUMMARY_WIDGET"
  constructor(
    public summary: string,
    public title: string,
    public subTitle: string,
    public action?: Action,
    public imageUrl?: string
  ) {
    this.summary = summary
    this.title = title
    this.subTitle = subTitle
    this.action = action
    this.imageUrl = imageUrl
  }
}

export interface TransformConfirmationCoachInfo {
  title: string
  name: string
  experience?: string
  imageUrl?: string
  action?: Action

}

export class TransformConfirmationSummaryWidget implements WidgetView {
  public widgetType: WidgetType = "TRANSFORM_CONFIRMATION_WIDGET"
  constructor(
    public summary: string,
    public title: string,
    public subTitle: string,
    public coachInfo?: TransformConfirmationCoachInfo,
    public action?: Action,
  ) {
    this.summary = summary
    this.title = title
    this.subTitle = subTitle
    this.action = action
    this.coachInfo
  }
}

export class CongoWidget implements WidgetView {
  public widgetType: WidgetType = "CONGO_WIDGET"
  constructor(
      public title: string,
      public subTitle: string,
      public action?: Action,
  ) {
    this.title = title
    this.subTitle = subTitle
    this.action = action
  }
}

export class CongratulationWidget implements WidgetView {
  public widgetType: WidgetType = "CONGRATULATION_WIDGET" as any
  constructor(
    public title: string,
    public subTitle: string,
    public bottomAction: Action,
    public startDateEpoch: number

  ) {
    this.title = title
    this.subTitle = subTitle
    this.bottomAction = bottomAction
    this.startDateEpoch = startDateEpoch
  }
}

export interface FitCashSummaryWidget extends WidgetView {
  title: string
  balance: number
  subtitle: string
  ctaString?: string
}

export class FormattedTextWidget implements WidgetView {
  public widgetType: WidgetType = "FORMATTED_TEXT_WIDGET"
  constructor(
    public data: FormattedTextItem[],
    public header?: Header,
    public subHeader?: Header
  ) { }
}

export class ActionCardWidget implements WidgetView {
  public widgetType: WidgetType = "ACTION_CARD_WIDGET"
  public title?: string
  public displayText?: string
  public action?: Action
  public icon?: {
    bgColor?: string;
    iconType: string;
    gradientColors?: string[];
    textColor?: string;
    url?: string;
  }
  public displayTextStyle?: any
  public displayTextValue?: string
  public displayTextValueStyle?: any
  public isDisabled?: boolean
  public meta?: any
  public analyticsData?: any
  public showDivider?: boolean
  public hasDividerBelow?: boolean
  public hasDividerAbove?: boolean
  public footer?: {
    text: string
    seeMore?: Action
  }
  public iconViewStyle?: any
  public titleTextStyle?: any
  public textContainerStyle?: any
  public textValueEnd?: string
  public textValueMiddle?: string
  public textValueMiddleStyle?: any
  public actionContainerStyle?: any
  public imageTextContainerStyle?: any
  public rowContainerStyle?: any
  public style?: any
  public wlNotificationTime?: number
}

export class CultCafeWidget implements WidgetView {
  public widgetType: WidgetType = "ACTION_CARD_WIDGET"
  public title: string
  public subTitle: string
  public action: Action
  public hasTopDivider?: boolean
  public hasDividerBelow?: boolean
  // a must requirement for logging analytics events
  public analyticsData?: {
    eventKey: string,
    eventData?: any
  }
}

export class MobileBreadCrumbWidget implements IMobileBreadCrumbWidget {
  public widgetType: "MOBILE_BREADCRUMB_WIDGET"
  public breadcrumbs: IAppsBreadCrumb[]

  constructor(breadcrumbs: IAppsBreadCrumb[]) {
    this.breadcrumbs = breadcrumbs
    this.widgetType = "MOBILE_BREADCRUMB_WIDGET"
  }
}


export class WorkoutSnackWidget implements WidgetView {
  public widgetType: WidgetType = "ACTION_CARD_WIDGET"
  public action: Action
  public images: {
    url: string
    title: string;
    calories?: string | number
    isVeg?: boolean
  }[]
}

export interface FormattedTextItem {
  text: string
  fontColor: string
  fontSize?: number
  fontWeight: FONT_WEIGHT
  lineHeight?: number
}

export interface SubscriptionPriceDetail {
  title: string
  price: ProductPrice
  meta?: string
}

export interface SubscriptionPriceView {
  initialPrice: SubscriptionPriceDetail
  recurringPrice: SubscriptionPriceDetail
}

export class SubscriptionCartSummaryWidget implements WidgetView {
  public widgetType: WidgetType = "SUBSCRIPTION_CART_SUMMARY_WIDGET"
  constructor(
    public title: string,
    public icon: string,
    public subTitle: string,
    public price: string,
    public description: string
  ) { }
}

export interface CallReminderWidget extends WidgetView {
  bookingNumber: string,
  title: string
  info: Action
  isEditable: boolean
  isReminderSet: boolean
  selectedCallTimeBefore: string
  selectedCallTime?: string
  isWaitlisted: boolean
  slots: {
    title: string,
    checkBoxTitle?: string,
    slotTimes: CallReminderSlotTime[]
  }
  ivrStatusMessage?: string
  notEditableAction?: {
    type: string,
    title: string,
    message: string,
    button: Action
  }
  slotSelectedAction?: SlotSelectedAction
  listItems?: {
    description: string,
    image: string,
    link?: Action
  }[]
}

export interface SlotSelectedAction {
  actionType: SlotActionType
  title: string
  message: string
  actionButton: Action
}

export interface CallReminderSlotTime {
  minutesBefore?: number
  callTimeBefore: string
  callTime?: string,
  selected: boolean,
  mostOpted?: boolean,
  key?: string
}

export class OTPInfoWidget implements WidgetView {
  public widgetType: WidgetType = "OTP_INFO_WIDGET"
  constructor(
    public title: string,
    public otpString: string,
    public subTitle: string
  ) { }
}

export interface WalletInfo {
  paymentChannel: PaymentChannel
  isLinked: boolean
  balance?: number
  title: string
  subTitle: string
  fitcash?: number
  action?: Action
}

export interface SavedCardView extends SavedCard {
  paymentChannel: PaymentChannel
}

export interface MyPaymentInfoView {
  wallets: WalletInfo[]
  cards: SavedCardView[]
  tataNeuLoyaltyPoints?: TataLoyaltyPointsBalanceView,
  paymentModes?: any
}

export interface TataLoyaltyPointsBalanceView {
  title: string
  subTitle: string
  buttonSubTitle: string
  buttonTitle: string
}

export interface CafeDeliverySlotAction extends Action {
  deliveryWindowTitle?: string,
  slots: {
    deliverySlot: string;
    title: string;
    isEnabled: boolean;
  }[]
  deliveryWindows?: {
    startingHours: HourMin,
    closingHours: HourMin
    title: string,
    isEnabled: boolean;
  }[]
  mealSlot: MenuType
}
export interface DeliverySlotAction extends Action {
  slots: {
    deliverySlot: string;
    title: string;
    isEnabled: boolean;
  }[]
  mealSlot: MenuType
}

export interface DateWiseDeliverySlotAction extends Action {
  slots: {
    deliveryDate: string
    title: string,
    deliverySlots: {
      slotId: string,
      title: string,
      isEnabled: boolean
    }[]
  }[]
  mealSlot: MenuType,
  deliveryDate: string,
  deliverySlot: string
}

export interface SearchListingWidget extends WidgetView {
  widgetType: "SEARCH_LISTING_WIDGET",
  listingTitle?: string
  title?: string,
  searchResultText?: string,
  list: any[]
  listType?: string,
  showFilter: boolean,
  placeholder?: string,
  showMoreShadow?: boolean,
  autoFocus?: boolean,
  isUnderline?: boolean,
  emptySearchImage?: string,
  emptySearchText?: string,
  showResultForEmptySearch?: boolean
  customSearchEnabled?: boolean
  orientation?: string
  scrollToTopOnFocus?: boolean
  filterModalItems?: any
  selectedFilterItems?: any
  doctorCardFooterAction?: Action,
  searchTextStyle?: {
    fontSize?: string,
    fontColor?: string
  }
  key?: string
  isViewMoreEnabled?: boolean
  viewCount?: number
}

export interface SearchWidget extends WidgetView {
  title?: string,
  type: string,
  subTitle?: string,
  actionText?: string,
  containerStyling?: any
  widgetType: "SEARCH_WIDGET"
  placeHolder?: string,
  action: any,
  dividerType?: string
}

export interface ChronicCareDoctorBannerWidget extends WidgetView {
  subTitle?: string,
}

export class DoctorListWidgetV2 implements WidgetView {
  public widgetType: WidgetType = "DOCTOR_LIST_WIDGET_V2"
  public action?: Action
  public title: string
  public doctors: any[]
  public dividerType?: string
  public orientation?: Orientation
  public hideSepratorLines?: boolean
  public hasDividerBelow?: boolean
}


export class PackOrderInfoWidget implements WidgetView {
  public widgetType: WidgetType
  public title: string
  public description: string
  public icon: string
  public action: Action
  constructor(title: string, description: string, icon: string, action: Action) {
    this.title = title
    this.description = description
    this.icon = icon
    this.action = action
    this.widgetType = "PACK_ORDER_INFO_CARD_WIDGET"
  }
}

export interface OrderSuccessWidget extends WidgetView {
  title: string
  icon: string
  gradientColors?: string[]
  containerStyle?: any
  iconContainerStyle?: any
  iconStyle?: any
  titleStyle?: any
}

export interface GymPackInfo extends WidgetView {
  title: string
  description: string
  action: Action
}

export class SinglesOrderConfirmationWidget implements WidgetView {
  public widgetType: WidgetType = "SINGLES_ORDER_CONFIRMATION_WIDGET"
  public activityType: ActivityType
  public subActivityType?: string
  public title: string
  public subTitle?: string
  public activityName: string
  public action?: Action
  public tagView?: {
    bgColor: string
    icon?: string
    tagText: string
  }
  public infoBlock?: {
    title?: string
    message?: string
    calloutView?: {
      bgColor?: string
      tag?: string
      title: string
      subTitle: string
    }
  }
  public ivrConfig?: CallReminderWidget
  public savings?: string
  public cultCafeBlock?: CultCafeWidget
  isPulseClass?: boolean
  pulseDeviceName?: string
  isWaitlistedClass?: boolean
  waitlistNumber?: string
  description?: string
  confirmationWrapper?: {
    backgroundImage: string,
    header: string
  }
  header?: {
    title: string,
    description?: string
  }
  inviteBuddy?: ShareActionWidget
  buddiesJoining?: CultBuddiesJoiningListSmallView
  actionCells?: ActionCell[]
  useLiveCell?: boolean
  liveCellRightAction?: Action
  liveCellLeftAction?: Action
  liveCellBuddiesJoiningV2?: BuddiesInviteJoiningListWidgetV2
  duration?: string
  public fitcashSavings?: any
  icon?: string
  rescheduleClassHeader?: Header
}

export interface HeaderWidget {
  widgetType: string,
  widgetTitle: {
    title: string,
    subTitle: string
  },
  titleStyle?: {},
  subtitleStyle?: {},
  style?: {}
}

export interface ClassInfoCardWidget {
  widgetType: string,
  items: {
    title: string,
    description: string,
    benefits: string,
    image: string,
    workoutCategoryId: string
  }[]
}

export interface CultClassRecommendation {
  widgets: Array<HeaderWidget | ClassInfoCardWidget>,
  actions: Array<Action>
}

export class SuggestionWidget implements WidgetView {
  public widgetType: WidgetType = "SUGGESTION_WIDGET"
  public action?: Action
  public cardAction?: Action
  public title: string
  public description?: string
  public contentImage?: string
  public contentTitle?: string
  public contentSubtitle?: string
}

export class UserFeedbackWidget implements WidgetView {
  public widgetType: WidgetType = "USER_FEEDBACK_WIDGET"
  public header: Header
  public feedbackType: "THUMBS_UP_DOWN"
  public userFeedback?: number
}

interface IClpCalloutWidget extends WidgetView {
  subTitle?: string
  title?: string
  imageUri?: string
  icon?: string
  action?: Action
  linearGradientColors?: Array<string>
  fromPage?: string
  imageStyle?: any
  iconBackgroundColor?: string
  subTitleColor?: string
  borderRadius?: number
}

export class ClpCalloutWidget implements WidgetView {
  public widgetType: WidgetType = "CLP_CALLOUT_WIDGET"
  public subTitle: string
  public title?: string
  public imageUri?: string
  public icon?: string
  public action?: Action
  public linearGradientColors?: Array<string>
  public fromPage?: string
  public imageStyle?: any
  public iconBackgroundColor?: string
  public subTitleColor?: string
  public borderRadius?: number
  public titleStyle?: any
  public containerStyle?: any
  constructor(public clpCalloutWidget: IClpCalloutWidget) {
    this.subTitle = clpCalloutWidget.subTitle
    this.imageUri = clpCalloutWidget.imageUri
    this.title = clpCalloutWidget.title
    this.action = clpCalloutWidget.action
    this.icon = clpCalloutWidget.icon
    this.linearGradientColors = clpCalloutWidget.linearGradientColors
    this.fromPage = clpCalloutWidget.fromPage
    this.imageStyle = clpCalloutWidget.imageStyle
    this.subTitleColor = clpCalloutWidget.subTitleColor
    this.iconBackgroundColor = clpCalloutWidget.iconBackgroundColor
    this.borderRadius = clpCalloutWidget.borderRadius
    this.containerStyle = clpCalloutWidget.containerStyle
    this.titleStyle = clpCalloutWidget.titleStyle
  }
}

export class ShareActionWidget implements WidgetView {
  public widgetType: WidgetType = "SHARE_ACTION_WIDGET"
  public action?: Action
  public title: string
  public subTitle: string
  public iconUrl: string
  public removeHorizontalPadding?: boolean
  public hasTopDivider?: boolean
}

export class QRCodeWidget implements WidgetView {
  public widgetType: WidgetType = "QR_CODE_WIDGET"
  public header: WidgetHeader
  public qrCodeString: string
  public title: string
  public subTitle: string
}

export class ActionCell {
  public action?: Action
  public title: string
  public subTitle?: string
  public iconUrl?: string
  public removeHorizontalPadding?: boolean
  public hasTopDivider?: boolean
  public tintColor?: string
  public inlineAction?: Action
  public shareCellType?: string
  public analyticsData?: any
  public iconContainerStyle?: any
}

export class ExpandableHeaderWidget implements WidgetView {
  public widgetType: WidgetType = "EXPANDABLE_HEADER_WIDGET"
  public showAddExerciseButton?: boolean
  public layoutProps?: any
  public header: {
     title?: any
     subTitle?: any
  }
  public widgets?: WidgetView[]
}

export type CultBuddiesJoiningListLargeView = Omit<CultBuddiesJoiningListLargeWidget, "widgetType">
export type CultBuddiesJoiningListSmallView = Omit<CultBuddiesJoiningListSmallWidget, "widgetType">

export class StructuredSchemaVideoWidget implements SeoWidget {
  description: string
  name: string
  thumbnailImages: string[]
  uploaded: string
  url: string
  widgetType: "STRUCTURED_SCHEMA_VIDEO_WIDGET"

  constructor(name: string, description: string, thumbnailImages: string[], videoCreatedDate: string, videoUrl: string) {
    this.name = name
    this.description = description
    this.thumbnailImages = thumbnailImages
    this.uploaded = videoCreatedDate
    this.url = videoUrl
    this.widgetType = "STRUCTURED_SCHEMA_VIDEO_WIDGET"
  }
}

export * from "./GearWidgetView"

interface MediaObject {
  index: number,
  url: string,
  type: string,
}

interface InstructionsObject {
  text: string,
  media: MediaObject[],
}

export class InstructionsWithMedia implements WidgetView {
  public widgetType: WidgetType = "INSTRUCTIONS_WITH_MEDIA_WIDGET"
  public showDivider: boolean = true
  public showBullet?: boolean = true
  public dividerType?: string = "LARGE"
  public hasDividerBelow?: boolean = true
  public showWidgetDivider?: boolean = false
  public aspectRatio?: string = "320:152"
  constructor(
      public title: string,
      public instructions: InstructionsObject[],
      public layoutProps?: any
  ) {
    this.title = title
    this.instructions = instructions
  }
}

export class AddOnProductWidget implements WidgetView {
  headerTitle: string
  title: string
  mrp: string
  packDetail: string
  offerText: string
  isAddOnSelected: boolean
  action: Action
  widgetType: WidgetType
}
