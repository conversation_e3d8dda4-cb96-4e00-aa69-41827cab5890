import { WidgetType, WidgetView } from "./WidgetView"
import { MembershipReward, RewardState } from "@curefit/cult-common"
import { TimeUtil } from "@curefit/util-common"
import { UserContext } from "@curefit/userinfo-common"

export class CultMembershipRewardsWidget implements WidgetView {
    public widgetType: WidgetType = "CULT_MEMBERSHIP_REWARDS_WIDGET"
    title: string
    description: { line1: string, line2?: string }
    highlightedText: string
    constructor(reward: MembershipReward, userContext: UserContext) {
        const currentCount = reward.currentCount
        const tz = userContext.userProfile.timezone
        const remainingCount = reward.requiredCount - reward.currentCount
        const today = TimeUtil.todaysDateWithTimezone(tz)
        const daysSinceDone = TimeUtil.diffInDays(tz, reward.rewardStartDate, today)
        const daysRemaining = TimeUtil.diffInDays(tz, today, reward.rewardEndDate)
        const totalDaysOfOffer = TimeUtil.diffInDays(tz, reward.rewardStartDate, reward.rewardEndDate)
        this.description = this.getMembershipRewardDescription(reward.state, currentCount, daysSinceDone, remainingCount, daysRemaining, totalDaysOfOffer)
        this.title = "Membership Rewards"
        this.highlightedText = `${currentCount}/${reward.requiredCount}`
    }

    private getMembershipRewardDescription(state: RewardState, currentCount: number, daysSinceDone: number, remainingCount: number, daysRemaining: number, totalDaysOfOffer: number): { line1: string, line2?: string } {
        switch (state) {
            case "CREATED":
                return {
                    line1: `You have attended ${currentCount} classes in ${daysSinceDone} days.`,
                    line2: `Hit the target by attending ${remainingCount} more classes in next ${daysRemaining} days and get a Cult T-shirt!`
                }
            case "ACTIVATED":
                return {
                    line1: "Target achieved! Please collect your T-shirt from the center manager of your preferred center"
                }
            case "EXPIRED":
                return {
                    line1: `Offer period is over.`,
                    line2: `You have attended ${currentCount} classes in the first ${totalDaysOfOffer} days of your membership`
                }
        }
    }
}
