import * as _ from "lodash"
import { Container, inject, injectable } from "inversify"
import { Action, WidgetType, WidgetView } from "./WidgetView"
import { InAppNotificationData, InAppNotificationType, NotificationMeta } from "@curefit/iris-common"
import AppUtil, { INAPP_NOTIFICATION_APPID } from "../../util/AppUtil"
import AppActionUtil from "../../util/ActionUtil"
import { ActivityState } from "@curefit/logging-common"
import { ActivityType } from "@curefit/product-common"
import { FitbitUserInfo, UserContext } from "@curefit/userinfo-common"
import { IUserService } from "@curefit/user-client"
import { HIGH_PRIORITY_TIMESTAMP, ITodoActivityWidget } from "./TodoActivityWidget"
import { AnnouncementBusiness } from "../../announcement/AnnouncementBusiness"
import { Announcement } from "../../announcement/Announcement"
import { RUPEE_SYMBOL } from "@curefit/finance-common"
import { TimeUtil, Timezone } from "@curefit/util-common"
import * as momentTz from "moment-timezone"
import { AtlasActivityService } from "../../atlas/AtlasActivityService"
import { CenterNotification, CenterNotificationType } from "@curefit/cult-common"
import { AnnouncementDetails } from "../../announcement/AnnouncementViewBuilder"
import { ICultServiceOld } from "@curefit/cult-client"
import { InAppNotificationsService } from "@curefit/iris-client"

export type AnnouncementNotificationType = "CULT_CENTER_SHUTDOWN_NOTIFICATION" | "CULT_CENTER_STAGGERED_SLOT_NOTIFICATION" | "FITCLUB_NOTIFICATION" | "TEXT_NOTIFICATION"

export interface INotification {
    notificationId?: string
    notificationType: InAppNotificationType | AnnouncementNotificationType
    title: string
    action: Action
    closeAction: Action
    image?: string
    imageStyle?: any
    textColor?: string
    backgroundColor?: string
    shouldRefreshPage?: boolean
}
export interface INotificationWidget extends WidgetView {
    items: INotification[]
}

export interface INotificationWidgetParams {
    userId: string
    inAppNotificationService: InAppNotificationsService
    canAskHealthKit: boolean
    canAskFitbit: boolean
    announcementBusiness: AnnouncementBusiness
    userService: IUserService
    userContext: UserContext
    atlasActivityService: AtlasActivityService
}

export interface CultSchedulePageNotificationParams {
    cultService: ICultServiceOld
    announcementBusiness: AnnouncementBusiness
    userContext: UserContext
    shutdownNotificationData: any
    fitClubNotificationData: any
    centerNotificationResponse: any
}

export class NotificationWidget implements INotificationWidget, ITodoActivityWidget {
    timestamp: number
    widgetType: WidgetType
    items: INotification[]
    status: ActivityState
    activityType: ActivityType

    constructor() {
        this.widgetType = "NOTIFICATION_WIDGET"
        this.items = []
    }


    public async buildView(notificationWidgetParams: INotificationWidgetParams): Promise<NotificationWidget> {
        try {
            if (notificationWidgetParams.canAskHealthKit) {
                this.items.push(this.healthKitPermissionNotification())
            }
            if (notificationWidgetParams.canAskFitbit) {
                const notification = await this.fitbitPermissionNotification(notificationWidgetParams.userId, notificationWidgetParams.atlasActivityService)
                if (notification)
                    this.items.push(notification)
            }
            await this.addServerNotifications(notificationWidgetParams.userId, notificationWidgetParams.inAppNotificationService)

            // Assign timestamp
            this.timestamp = HIGH_PRIORITY_TIMESTAMP

            // Assign status
            this.status = "TODO"

            // Assign activity type
            this.activityType = "NOTIFICATION"
            return this
        } catch (e) {
            return undefined
        }
    }

    private healthKitPermissionNotification(timezone: Timezone = TimeUtil.IST_TIMEZONE): INotification {
        let title = "Track steps and sleep."
        let action: Action = { actionType: "HEALTH_KIT_PERMISSION", title: "Enable", meta: { "howitworks": AppActionUtil.getStepsHowItWorks() } }
        let image = "/image/permission/devices_icon.png"
        if (momentTz.tz(timezone).valueOf() > 1557512999000 && momentTz.tz(timezone).valueOf() < 1558506600000) {
            title = `Enter Step It Up Challenge to earn upto ${RUPEE_SYMBOL}1500 off`
            action = { actionType: "NAVIGATION", title: "Join Now", url: "curefit://challengedetails?id=steps_challenge_may_2019" }
            image = `/image/steps/plan_icon.png`
        }
        const notification: INotification = {
            notificationId: "HEALTH_KIT_PERMISSION",
            notificationType: "PERMISSION",
            title: title,
            image: image,
            action: action,
            closeAction: { actionType: "DENIED_HEALTH_KIT_PERMISSION" }
        }
        return notification
    }

    private async fitbitPermissionNotification(userId: string, atlasActivityService: AtlasActivityService): Promise<INotification> {
        const fitbitPromise: Promise<FitbitUserInfo> = atlasActivityService.getFitbitDataForUser(userId)
        let fitbitData: FitbitUserInfo
        try {
            fitbitData = await fitbitPromise
        } catch (e) {
            return undefined
        }

        const isfitbitAuthenticated: boolean = !_.isEmpty(fitbitData) && !_.isNil(fitbitData.fitbitToken) && fitbitData.fitbitToken !== "" ? true : false
        const fitbitInfo = {
            authenticated: isfitbitAuthenticated,
            url: isfitbitAuthenticated ? "curefit://fitbit/logout" : AppUtil.fitbitLoginParams(userId)
        }
        if (!fitbitInfo.authenticated) {
            const notification: INotification = {
                notificationId: "FITBIT_PERMISSION",
                notificationType: "PERMISSION",
                title: "Link your fitbit device.",
                image: "/image/permission/fitbit_access1.png",
                action: { actionType: "EXTERNAL_DEEP_LINK", title: "Link", url: fitbitInfo.url, actionId: "FITBIT_OAUTH" },
                closeAction: { actionType: "DENIED_FITBIT_PERMISSION" }
            }
            return notification
        }
        return undefined
    }

    private async addServerNotifications(userId: string, inAppNotificationService: any): Promise<void> {
        let inAppNotifications = []
        try {
            inAppNotifications = await inAppNotificationService.getActiveInAppNotificationsForUser(userId, INAPP_NOTIFICATION_APPID)
        } catch (e) {
            return
        }
        const serverNotifications = _.map(inAppNotifications, (inAppNotification) => {
            const inAppNotificationData: InAppNotificationData = JSON.parse(inAppNotification.dataBlob)
            const notification: INotification = {
                title: inAppNotificationData.title,
                action: {
                    actionType: "NAVIGATION",
                    url: inAppNotificationData.action.url,
                    title: inAppNotificationData.action.title
                },
                closeAction: {
                    actionType: "REST_API",
                    meta: {
                        method: "POST",
                        url: "/user/inAppNotification/" + inAppNotification.notificationId,
                        body: { "state": "CLOSED" }
                    }
                },
                notificationId: inAppNotification.notificationId,
                notificationType: inAppNotification.type,
                image: inAppNotificationData.image,
                textColor: inAppNotificationData.textColor,
                backgroundColor: inAppNotificationData.backgroundColor
            }
            return notification
        })
        this.items.push(...serverNotifications)
    }


    private async addAnnouncements(userContext: UserContext, userId: string, announcementBusiness: AnnouncementBusiness): Promise<void> {
        // TODO: Make it segment aware and cyclops driven
        const announcement = <Announcement>await announcementBusiness.getAnnouncementToShow(userContext, "mb_sale_prebuzz")
        if (announcement) {
            const notification: INotification = {
                title: "Cult 100% Money Back Sale. Ends 24th March",
                action: {
                    actionType: "NAVIGATION",
                    url: "curefit://listpage?pageId=Money_Back_sale",
                    title: "Know More"
                },
                closeAction: {
                    actionType: "REST_API",
                    meta: {
                        method: "POST",
                        url: `/user/announcement/${announcement.announcementId}`,
                        body: { "state": "DISMISSED" }
                    }
                },
                notificationId: announcement.announcementId,
                notificationType: "META",
                image: "/image/notification/mb_nudge_2.png"
            }
            this.items.push(notification)
        }
    }

    public async buildViewForCultBookingPage(builderParams: CultSchedulePageNotificationParams): Promise<NotificationWidget> {
        if (builderParams.shutdownNotificationData) {
            this.items.push({
                notificationType: "CULT_CENTER_SHUTDOWN_NOTIFICATION",
                image: builderParams.shutdownNotificationData.icon,
                title: builderParams.shutdownNotificationData.description,
                action: builderParams.shutdownNotificationData.action,
                closeAction: builderParams.shutdownNotificationData.dismissAction,
                shouldRefreshPage: true,
                imageStyle: {
                    width: 30,
                    height: 30
                }
            })
        }
        if (builderParams.fitClubNotificationData) {
            this.items.push({
                notificationType: "FITCLUB_NOTIFICATION",
                image: builderParams.fitClubNotificationData.icon,
                title: builderParams.fitClubNotificationData.description,
                action: builderParams.fitClubNotificationData.action,
                closeAction: builderParams.fitClubNotificationData.dismissAction,
                imageStyle: {
                    width: 30,
                    height: 30
                }
            })
        }
        try {
            const centerNotificationResponse = builderParams.centerNotificationResponse
            const staggeredSlotNotification = _.find(centerNotificationResponse.notifications, centerNotification => centerNotification.type === CenterNotificationType.STAGGERED_SLOT)
            if (staggeredSlotNotification) {
                const announcementIdPromises = _.map(staggeredSlotNotification.centerIDs, async centerId => {
                    // to check if user has seen the notification already
                    const announcementDetails = <AnnouncementDetails>await builderParams.announcementBusiness.createAndGetAnnouncementDetails(builderParams.userContext, `STAGGERED_SLOT_${centerId}`)
                    if (announcementDetails.state === "CREATED") {
                        return {
                            announcementId: announcementDetails.announcementId,
                            state: "DISMISSED"
                        }
                    }
                })
                let announcementData = await Promise.all(announcementIdPromises)
                announcementData = _.filter(announcementData, item => !_.isEmpty(item))
                // user has seen notification for all centers
                if (!_.isEmpty(announcementData)) {
                    const closeAction: Action = {
                        actionType: "REST_API",
                        title: "DISMISS",
                        meta: {
                            method: "POST",
                            url: `/user/updateBulkAnnouncement`,
                            body: announcementData
                        }
                    }
                    this.items.push({
                        notificationType: "CULT_CENTER_STAGGERED_SLOT_NOTIFICATION",
                        image: "",
                        title: staggeredSlotNotification.text,
                        action: {
                            title: "KNOW MORE",
                            actionType: "NAVIGATION",
                            url: `curefit://webview?uri=${staggeredSlotNotification.meta.link}`
                        },
                        closeAction: closeAction
                    })
                }
            }
            const genericNotification: CenterNotification = _.find(centerNotificationResponse.notifications, centerNotification => centerNotification.type === CenterNotificationType.GENERIC)
            if (genericNotification) {
                const announcementDetails = <AnnouncementDetails>await builderParams.announcementBusiness.createAndGetAnnouncementDetails(builderParams.userContext, `CULT_GENERIC_NOTIFICATION_${genericNotification.notificationID}`)
                if (announcementDetails.state === "CREATED") {
                    const closeAction: Action = {
                        actionType: "REST_API",
                        title: "DISMISS",
                        meta: {
                            method: "POST",
                            url: `/user/announcement/${announcementDetails.announcementId}`,
                            body: { "state": "DISMISSED" }
                        }
                    }
                    this.items.push({
                        notificationType: "TEXT_NOTIFICATION",
                        image: "",
                        title: genericNotification.text,
                        action: genericNotification.meta && genericNotification.meta.link ? {
                            title: "KNOW MORE",
                            actionType: "NAVIGATION",
                            url: `curefit://webview?uri=${genericNotification.meta.link}`
                        } : {
                            actionType: "NAVIGATION",
                            disabled: true
                        },
                        closeAction: closeAction
                    })
                }
            }
        }
        catch (ex) {
        }
        return this
    }
}
