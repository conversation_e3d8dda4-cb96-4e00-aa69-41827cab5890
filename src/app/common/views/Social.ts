/*
    Challenges View Models
*/

import { Action, WidgetView } from "./WidgetView"
import { PageWidget } from "../../page/Page"

export interface Reward {
    badge: {
        name: string
        imageUrl: string
    }
    title: string
    subTitle: string
    description?: string
    bgColor?: string
}

export interface ChallengeVM {
    id: string
    imageUrl: string
    title: string
    leftSubText: string
    rightSubText: string
    rewards: Array<Reward>
    socialText: string
    action: Action
}

export interface IInvite {
    challengeId: string
    title: string
    subTitle: string
    imageUrl: string
    from: string
    sentTime?: string
    topText: string
    actions?: Array<Action>
}

export interface IInviteWidget extends WidgetView {
    invites: Array<IInvite>
}

export interface IActiveChallengeWidget extends WidgetView {
    title: string
    subTitle: string
    metric: string
    value?: string
    rank: string
    totalParticipants: string
    imageUrl: string,
    progress?: {
        completed: number
        colors?: Array<string>
    }
    action: Action
    leftSubText: string
    rightSubText: string
    labels?: any
}

export interface IChallengeSummaryWidget extends WidgetView {
    challengeId: string
    imageUrl: string
    title: string
    subTitle: string
    invite?: Action
}

export interface IGoalWidget extends WidgetView {
    list: Array<{
        icon: string
        key: string
        value: string
    }>
}

export interface IRewardsWidget extends WidgetView {
    title: string
    action?: Action
    rewards?: Array<Reward>
}

interface IKeyValue {
    key: string
    value: string
}

export interface ITableWidget extends WidgetView {
    title: IKeyValue
    contents: Array<IKeyValue>
}

export enum ChallengeState {
    NOT_STARTED = "NOT_STARTED",
    ACTIVE = "ACTIVE",
    COMPLETED = "COMPLETED"
}

export interface IWidgetSection {
    data: Array<WidgetView | PageWidget>
    header?: Array<WidgetView | PageWidget>
}

export interface IChallengeDetail {
    actions?: Array<Action>
    sectionHeader: IChallengeSummaryWidget
    sections: Array<IWidgetSection>
}

export interface IChallengeSection {
    id: string
    name: string
    priority?: number
    total: string
}

export enum ParticipantType {
    GLOBAL = "GLOBAL",
    FRIEND = "FRIEND",
    INVITED = "INVITED",
    INVITEE = "INVITEE"
}

export interface IChallengeParticipants {
    rank: number | string
    name: string
    imageUrl: string
    metric?: string
    score: string
    value: string
    progress?: {
        completed: number
        colors?: Array<string>
    }
    type: ParticipantType,
    updates?: {
        title: string
        value: number
        increase?: string
        decrease?: string
    },
    activities?: Array<IActivityData>
    highlight?: boolean
    showImage?: boolean
}

export interface ILeaderboardWidget extends WidgetView {
    leaderboardId: string,
    challengeState: ChallengeState,
    sections: Array<IChallengeSection>
    lists: { [id: string]: Array<IChallengeParticipants> }
    challengeId?: string
}

export const SocialColors = {
    reddishPink: "#ff3278",
    purple: "#736cf0"
}

export interface IActivityData {
    type: string,
    score: number,
    title: string,
    status: number
}

export interface IUserProgressWidget extends WidgetView {
    userInfo: {
        image: string,
        name: string
    },
    leftSubText: string,
    rightSubText: string,
    rank: number,
    progress: {
        completed: number,
        colors?: Array<string>
    },
    activities: Array<IActivityData>
}

export enum FriendInviteState {
    PENDING = "PENDING",
    ACCEPTED = "ACCEPTED"
}

export interface IFriendInvite {
    name: string
    imageUrl: string
    state: FriendInviteState
}

export interface IInviteFriendWidget extends WidgetView {
    title: string
    subTitle: string
    list: Array<IFriendInvite>
}

export interface IUserPerformanceWidget extends WidgetView {
    title: string
    userDetails: {
        name: string
        profileImageUrl: string
        result: string
        rank: number
        score: number
    }
    badge?: any
    badgeUrl?: string
    reward?: {
        title: string
        subTitle: string
        action: Action
    }
}

export interface IChallengeActionWidget extends WidgetView {
    action: Action
}

export interface IUserChallengeActivityData {
    activityPoints: number
    currentLevelId: number
}
