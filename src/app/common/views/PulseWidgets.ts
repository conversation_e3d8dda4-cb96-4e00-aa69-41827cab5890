// import { IServiceInterfaces, IBaseWidget, UserContext } from "./BaseWidget"
import { WidgetView } from "./WidgetView"

interface IPulseMetric {
  // pulse metrics would be an array
  fancyTextHeader: string // header text
  data: string // data to be displayed
  units: string // units of data
  headerColor?: string // optional color for header,
  big?: boolean
}

interface IPulseInfolet {
  header: string
  dataText: string
  subText: string
}

interface IMuscleGroupData {
  imageUri: string
  caption: string
}

interface IFooterData {
  text: string
  bold: boolean
}

interface IPulseHighIntensityZoneWidget extends WidgetView {
  intensity_info: IPulseMetric
  auxiliary_data: Array<IPulseInfolet>
}

interface IPulseShareCardWidget extends WidgetView {
  headerTitle: string
  pulse_metrics: Array<IPulseMetric>
  muscle_groups: {
    title: string;
    data: Array<IMuscleGroupData>;
  }
  footers: Array<IFooterData>
  share_button: {
    text: string;
    iconUri: string;
  }
}

interface IGroupBarDataPoint {
  values: {
    user: number; // time spent by user in a zone
    avg: number; // time spent by class avg in a zone
  }
  label: string
  colors: {
    user: string; // hex or rgb color
    avg: string; // hex or rgb color
  }
}

interface IPulseGroupBarGraphWidget extends WidgetView {
  graph_data: {
    type: "group";
    title: string;
    legend?: string;
    data: Array<IGroupBarDataPoint>;
  }
}

interface IPulseProTipWidget extends WidgetView {
  header: string
  text: string
}

interface IPulseTip {
  text: string
}

interface IPulseCalorieBurnWidget extends WidgetView {
  intensity_info: IPulseMetric
  auxiliary_data: Array<IPulseInfolet>
  tips?: Array<IPulseTip>
}

interface IPulseHeartRateWidget extends WidgetView {
  auxiliary_data: Array<IPulseInfolet>
}

interface IColorStop {
  value: number
  color: string
}

interface IPulseGraphDataPoint {
  x: number
  y: number
}

interface IPulseBarGraphWidget extends WidgetView {
  graph_data: {
    type: "normal";
    title: string;
    colorStops: Array<IColorStop>;
    data: Array<IPulseGraphDataPoint>;
  }
}

export class PulseShareCardWidget implements IPulseShareCardWidget {
  widgetType: IPulseShareCardWidget["widgetType"]
  headerTitle: IPulseShareCardWidget["headerTitle"]
  pulse_metrics: IPulseShareCardWidget["pulse_metrics"]
  muscle_groups: IPulseShareCardWidget["muscle_groups"]
  footers: IPulseShareCardWidget["footers"]
  share_button: IPulseShareCardWidget["share_button"]

  constructor(shareCardWidget: IPulseShareCardWidget) {
    this.widgetType = "PULSE_SHARE_CARD_WIDGET"
    this.headerTitle = shareCardWidget.headerTitle
    this.pulse_metrics = shareCardWidget.pulse_metrics.reduce(
      (acc: IPulseShareCardWidget["pulse_metrics"], metric) =>
        acc.concat([{ ...metric }]),
      []
    )
    this.muscle_groups.title = shareCardWidget.muscle_groups.title
    this.muscle_groups.data = shareCardWidget.muscle_groups.data.reduce(
      (acc: IPulseShareCardWidget["muscle_groups"]["data"], muscle_data) =>
        acc.concat([{ ...muscle_data }]),
      []
    )
    this.footers = shareCardWidget.footers.reduce(
      (acc: Array<IFooterData>, footer) => acc.concat([{ ...footer }]),
      []
    )
    this.share_button = {
      ...shareCardWidget.share_button
    }
  }

  // async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: any }): Promise<IBaseWidget> {
  //   return this
  // }
}

export class PulseHighIntensityZoneWidget
  implements IPulseHighIntensityZoneWidget {
  widgetType: IPulseHighIntensityZoneWidget["widgetType"]
  intensity_info: IPulseHighIntensityZoneWidget["intensity_info"]
  auxiliary_data: IPulseHighIntensityZoneWidget["auxiliary_data"]

  constructor(highIntensityZoneWidget: IPulseHighIntensityZoneWidget) {
    this.widgetType = "PULSE_HIGH_INTENSITY_ZONE_WIDGET"
    this.intensity_info = highIntensityZoneWidget.intensity_info
    this.auxiliary_data = highIntensityZoneWidget.auxiliary_data.reduce(
      (acc: IPulseHighIntensityZoneWidget["auxiliary_data"], dataPoint) =>
        acc.concat([{ ...dataPoint }]),
      []
    )
  }

  // async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: any }): Promise<IBaseWidget> {
  //   return this
  // }
}

export class PulseGroupBarGraphWidget implements IPulseGroupBarGraphWidget {
  graph_data: IPulseGroupBarGraphWidget["graph_data"]
  widgetType: IPulseGroupBarGraphWidget["widgetType"]

  constructor(graphData: IPulseGroupBarGraphWidget) {
    this.widgetType = "PULSE_GROUP_BAR_GRAPH_WIDGET"
    this.graph_data = {
      title: graphData.graph_data.title,
      type: graphData.graph_data.type || "group",
      legend: graphData.graph_data.legend,
      data: graphData.graph_data.data.reduce(
        (acc: IPulseGroupBarGraphWidget["graph_data"]["data"], item) =>
          acc.concat([
            {
              ...item
            }
          ]),
        []
      )
    }
  }
  // async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: any }): Promise<IBaseWidget> {
  //   return this
  // }
}

export class PulseProTipWidget implements IPulseProTipWidget {
  header: IPulseProTipWidget["header"]
  text: IPulseProTipWidget["text"]
  widgetType: IPulseProTipWidget["widgetType"]

  constructor(proTip: IPulseProTipWidget) {
    this.widgetType = "PULSE_PRO_TIP_WIDGET"
    this.text = proTip.text
    this.header = proTip.header
  }

  // async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: any }): Promise<IBaseWidget> {
  //   return this
  // }
}

export class PulseCalorieBurnWidget implements IPulseCalorieBurnWidget {
  intensity_info: IPulseCalorieBurnWidget["intensity_info"]
  auxiliary_data: IPulseCalorieBurnWidget["auxiliary_data"]
  tips: IPulseCalorieBurnWidget["tips"]
  widgetType: IPulseCalorieBurnWidget["widgetType"]

  constructor(calorieBurnData: IPulseCalorieBurnWidget) {
    this.widgetType = "PULSE_CALORIE_BURN_WIDGET"
    this.intensity_info = calorieBurnData.intensity_info
    this.auxiliary_data = calorieBurnData.auxiliary_data.reduce(
      (acc: IPulseCalorieBurnWidget["auxiliary_data"], item) =>
        acc.concat([{ ...item }]),
      []
    )
    if (Array.isArray(calorieBurnData.tips)) {
      this.tips = calorieBurnData.tips.reduce(
        (acc: IPulseCalorieBurnWidget["tips"], item) =>
          acc.concat([{ ...item }]),
        []
      )
    }
  }
  // async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: any }): Promise<IBaseWidget> {
  //   return this
  // }
}

export class PulseHeartRateWidget implements IPulseHeartRateWidget {
  auxiliary_data: IPulseHeartRateWidget["auxiliary_data"]
  widgetType: IPulseHeartRateWidget["widgetType"]

  constructor(hearRateData: IPulseHeartRateWidget) {
    this.widgetType = "PULSE_HEART_RATE_WIDGET"
    this.auxiliary_data = hearRateData.auxiliary_data.reduce(
      (acc: IPulseHeartRateWidget["auxiliary_data"], item) =>
        acc.concat([
          {
            ...item
          }
        ]),
      []
    )
  }
  // async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: any }): Promise<IBaseWidget> {
  //   return this
  // }
}

export class PulseBarGraphWidget implements IPulseBarGraphWidget {
  graph_data: IPulseBarGraphWidget["graph_data"]
  widgetType: IPulseBarGraphWidget["widgetType"]

  constructor(graphData: IPulseBarGraphWidget) {
    this.widgetType = "PULSE_BAR_GRAPH_WIDGET"
    this.graph_data = {
      title: graphData.graph_data.title,
      type: graphData.graph_data.type,
      colorStops: graphData.graph_data.colorStops.reduce(
        (acc: IPulseBarGraphWidget["graph_data"]["colorStops"], item) =>
          acc.concat([{ ...item }]),
        []
      ),
      data: graphData.graph_data.data.reduce(
        (acc: IPulseBarGraphWidget["graph_data"]["data"], item) =>
          acc.concat([{ ...item }]),
        []
      )
    }
  }
  // async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: any }): Promise<IBaseWidget> {
  //   return this
  // }
}
