import * as _ from "lodash"
import { Action, ActionList } from "./WidgetView"
import { ICatalogueService } from "@curefit/catalog-client"
import { ActivityState } from "@curefit/logging-common"
import { ActivityType, ProductType } from "@curefit/product-common"
import { HourMin } from "@curefit/base-common"
import { UrlPathBuilder } from "@curefit/product-common"
import { TimeUtil } from "@curefit/util-common"
import { SessionInfo, UserContext } from "@curefit/userinfo-common"
import { CarouselWidget, ImageCardCarouselView } from "./CarouselWidget"
import { Logger } from "@curefit/base"
import { DIYPack } from "@curefit/diy-common"
import { ActionUtil } from "../../util/ActionUtil"
import { ActionUtil as EtherActionUtil, ActionUtilV1, SeoUrlParams } from "@curefit/base-utils"
import { ITodoActivityWidget } from "./TodoActivityWidget"
import AppUtil from "../../util/AppUtil"
import { IDIYFulfilmentService } from "@curefit/diy-client"

export interface IDiyCarouselWidgetParams {
    date: string,
    packId: string,
    productId: string,
    status: ActivityState,
    diyService: IDIYFulfilmentService,
    catalogueService: ICatalogueService,
    preferredTime: HourMin,
    userContext: UserContext,
    logger: Logger
}

export class DiyCarouselWidget extends CarouselWidget implements ITodoActivityWidget {
    timestamp: number
    status: ActivityState
    activityType: ActivityType
    widgetHash: string
    constructor() {
        super()
        this.widgetType = "TODO_ACTIVITY_WIDGET"
    }

    async buildView(widgetBuilderParams: IDiyCarouselWidgetParams): Promise<DiyCarouselWidget> {
        try {
            const tz = widgetBuilderParams.userContext.userProfile.timezone
            const diyPack = <DIYPack>await widgetBuilderParams.catalogueService.getProduct(widgetBuilderParams.packId)
            const packType: ProductType = diyPack.productType

            // Assign title
            this.title = packType === "DIY_MEDITATION_PACK" ? "Meditation" : "Workout"
            this.title += " - " + diyPack.title

            // Assign activity type
            this.activityType = packType === "DIY_MEDITATION_PACK" ? "DIY_MEDITATION" : "DIY_FITNESS"

            // Assign text items (start time) and timestamp
            const preferredTime: HourMin = widgetBuilderParams.preferredTime
            if (!_.isNil(preferredTime)) {
                this.timestamp = TimeUtil.getDate(widgetBuilderParams.date, preferredTime.hour, preferredTime.min, tz).getTime()
                const currentEpochTime: number = TimeUtil.getDateNow(tz).getTime() - (30 * TimeUtil.TIME_IN_MILLISECONDS.SECOND) // 30 seconds buffer
                if (this.timestamp < currentEpochTime) {
                    this.leftCalloutText = "DUE"
                } else {
                    const startTime: string = TimeUtil.getTimeIn12HRFormat(widgetBuilderParams.date, preferredTime.hour, preferredTime.min, false, tz)
                    this.leftCalloutText = startTime
                }
            } else {
                this.leftCalloutText = "DUE"
            }

            // Assign status
            this.status = widgetBuilderParams.status

            const userId = widgetBuilderParams.userContext.userProfile.userId
            // Create the DIY card
            const diyCard: ImageCardCarouselView = await this.getCardForDIYPack(userId, diyPack, widgetBuilderParams)
            this.rightCalloutText = diyCard.calloutText
            diyCard.calloutText = undefined

            // Assign widget hash
            this.widgetHash = diyCard.title

            this.cards.push(diyCard)
            return this
        } catch (e) {
            const loggingParams = {
                packId: widgetBuilderParams.packId,
                productId: widgetBuilderParams.productId,
                date: widgetBuilderParams.date
            }
            widgetBuilderParams.logger.error(e.stack)
            widgetBuilderParams.logger.error("DIY widget building failed for params :: " + JSON.stringify(loggingParams) + " User ID :: " + widgetBuilderParams.userContext.userProfile.userId)
            return undefined
        }
    }

    private async getCardForDIYPack(userId: string, diyPack: DIYPack, widgetBuilderParams: IDiyCarouselWidgetParams): Promise<ImageCardCarouselView> {
        const diyCard: ImageCardCarouselView = {
            viewType: "BORDERLESS",
            styleType: "WIDE",
            title: "",
            images: [],
            action: {
                actionType: "NAVIGATION",
                url: ""
            },
            quickActions: []
        }
        const packType: ProductType = diyPack.productType
        const tenant = AppUtil.getTenantFromUserContext(widgetBuilderParams.userContext)
        const sessions = packType === "DIY_MEDITATION_PACK" ? await widgetBuilderParams.diyService.getDIYMeditationProductsByProductIds(userId, [widgetBuilderParams.productId], tenant) : await widgetBuilderParams.diyService.getDIYFitnessProductsByProductIds(userId, [widgetBuilderParams.productId], tenant)
        const session = sessions[0]
        const sessionIndex = diyPack.sessionIds.indexOf(widgetBuilderParams.productId) + 1

        // Generate image URLs
        const imageUrl: string = packType === "DIY_MEDITATION_PACK" ? UrlPathBuilder.prefixSlash(diyPack.imageDetails.todayImage) : UrlPathBuilder.prefixSlash(session.imageDetails.todayImage)
        diyCard.images.push(imageUrl)

        // Generate action and title
        const actionUrl = ActionUtilV1.diyPackProductPage(diyPack, widgetBuilderParams.userContext.sessionInfo.userAgent)
        diyCard.action.url = actionUrl
        diyCard.title = `Session (${sessionIndex} of ${diyPack.sessionIds.length})`
        diyCard.calloutText = TimeUtil.durationFromSecondsToMins(session.duration / 1000)

        // Generate quick actions
        const sessionInfo: SessionInfo = widgetBuilderParams.userContext.sessionInfo
        const possibleActions: Action[] = ActionUtil.diyActionsFromSessionDetailV2(session, diyPack, sessionInfo)
        const primaryAction: Action = possibleActions[0]
        const primaryActionList: ActionList = _.isNil(primaryAction) ? undefined : _.assign({ actions: [] }, primaryAction)
        const secondaryActions: Action[] = possibleActions.slice(1)
        if (!_.isNil(primaryActionList)) {
            diyCard.quickActions.push(primaryActionList)
        }
        if (AppUtil.isLargePlayIconSupported(sessionInfo.osName, sessionInfo.appVersion)) {
            diyCard.cardActions = secondaryActions
        } else {
            const secondaryActionList: ActionList = _.isEmpty(secondaryActions) ? undefined : { actionType: "ACTION_LIST", actions: secondaryActions, icon: "MANAGE" }
            if (!_.isNil(secondaryActionList)) {
                diyCard.quickActions.push(secondaryActionList)
            }
        }

        return diyCard
    }
}
