import { Action, WidgetType } from "./WidgetView"
import { GoalAdherenceMetrics, GoalAdherenceStatusType } from "@curefit/gmf-common"
import {
  GraphAggregationType,
  GraphHeader,
  GraphMetric,
  IGraphWidgetBuilderParams,
  IMetricsGraphWidget,
  LastLoggedInfoView,
  MetricsGraphView
} from "./IMetricsGraphWidget"
import { DisplayCategory } from "@curefit/logging-common"
import * as _ from "lodash"
import * as momentTz from "moment-timezone"
import { TimeUtil } from "@curefit/util-common"
import { ActionUtil } from "../../util/ActionUtil"
import { LoggableActivityType } from "../../logging/ActivityLogging"
import {
  getFinalGraphArrayForMonth,
  getFinalGraphArrayForWeekAndMonth,
  getFinalGraphArrayForYear
} from "../../util/GraphUtil"
import { AggregationRange } from "@curefit/metrics-common"
import { ADHERENCE_STATUS } from "../../goals/UserGoalBusiness"
import { UserContext } from "@curefit/userinfo-common"

export interface IAdherenceMetricsWidgetBuilderParams
  extends IGraphWidgetBuilderParams {
  goalAdherenceMetrics: GoalAdherenceMetrics
  adherenceCategory?: DisplayCategory
  adherenceStatus?: GoalAdherenceStatusType
}
export class GoalAdherenceGraphWidget implements IMetricsGraphWidget {
  widgetType: WidgetType
  goalId: string
  header: GraphHeader
  graphs: MetricsGraphView[]
  graphType: GraphAggregationType
  graphMetrics: GraphMetric[]
  fromDate: string
  toDate: string
  range: Array<number>
  hideGraph: boolean
  adherenceStatus: GoalAdherenceStatusType
  lastLoggedInfo?: LastLoggedInfoView
  goalValue?: number
  calloutText?: string

  constructor(userContext: UserContext, widgetBuilderParams: IAdherenceMetricsWidgetBuilderParams) {
    this.widgetType = "GOAL_ADHERENCE_GRAPH_WIDGET"
    const options = [
      {
        displayText: AggregationRange.WEEK,
        value: AggregationRange.WEEK
      },
      {
        displayText: AggregationRange.MONTH,
        value: AggregationRange.MONTH
      },
      {
        displayText: "6-MONTHS",
        value: AggregationRange.SEMI_ANNUAL
      }
    ]
    const graphType: GraphAggregationType = {
      type: widgetBuilderParams.graphType,
      displayText: widgetBuilderParams.graphType === AggregationRange.SEMI_ANNUAL ? "6-MONTHS" : widgetBuilderParams.graphType.toUpperCase(),
      action: {
        actionType: "NAVIGATION",
        meta: {
          options: options
        }
      }
    }
    this.goalId = widgetBuilderParams.goalAdherenceMetrics.goalID
    this.header = this.getHeader(graphType, widgetBuilderParams.adherenceStatus)
    this.graphType = graphType
    this.graphMetrics = this.getGraphMetrics(widgetBuilderParams)
    this.fromDate = widgetBuilderParams.fromDate
    this.toDate = widgetBuilderParams.toDate
    this.graphs = this.getAllGraphData(userContext, widgetBuilderParams)
    this.range = this.getYAxisRangeValues()
    this.lastLoggedInfo = this.getLastLoggedInfoView(userContext, widgetBuilderParams)
    this.goalValue = 80
    this.calloutText = this.getCalloutText(widgetBuilderParams)
    this.hideGraph = widgetBuilderParams.adherenceStatus === ADHERENCE_STATUS.EARLY
    this.adherenceStatus = widgetBuilderParams.adherenceStatus
  }

  private getHeader(graphType: GraphAggregationType, adherenceStatus: GoalAdherenceStatusType): GraphHeader {
    let header: GraphHeader = {
      title: "ADHERENCE",
      subTitle: "Adherence is a measure of how closely you are following plan",
      more: {
        title: "Learn more...",
        actionType: "SHOW_ALERT_MODAL",
        meta: {
          title: "ADHERENCE",
          subTitle: "Based on your plan we compare your logged activities against recommended activities and compute how closely you are following the plan. To achieve your goal we recommend that you should score at least 70% on all activities and log regularly so that we can help you better.",
          actions: [
            { actionType: "HIDE_ALERT_MODAL", title: "Ok" }
          ]
        }
      },
      graphType: graphType
    }
    if (adherenceStatus === ADHERENCE_STATUS.EARLY) {
      header = {
        title: "ADHERENCE",
        subTitle: "You need to log all your meals, workouts and medication (if prescribed) for at least 3 days to see your adherence chart.",
        more: null,
        meta: {
          isAdherenceTooEarly: true,
        },
        graphType: graphType
      }
    }
    return header
  }

  private getGraphMetrics(
    widgetBuilderParams: IAdherenceMetricsWidgetBuilderParams
  ): GraphMetric[] {
    const keyCategories = widgetBuilderParams.goalAdherenceMetrics.keyCategories
    const graphMetrics = _.map(
      widgetBuilderParams.goalAdherenceMetrics.allCategories,
      category => {
        const isSelectedCategory =
          widgetBuilderParams.adherenceCategory === category
        const graphMetric: GraphMetric = {
          category: category,
          displayText: category,
          isPrimary: keyCategories.indexOf(category) > -1,
          unit: "percent",
          selected: isSelectedCategory
        }
        return graphMetric
      }
    )
    return graphMetrics
  }

  private getLastLoggedInfoView(userContext: UserContext, widgetBuilderParams: IAdherenceMetricsWidgetBuilderParams): LastLoggedInfoView {
    const loggableActivityType = this.mapToLoggableActivityType(widgetBuilderParams.adherenceCategory)
    const tz = userContext.userProfile.timezone
    let logAction: Action = {
      actionType: "NAVIGATION",
      title: "LOG NOW",
      url: ActionUtil.addLoggableActivityUrl(loggableActivityType)
    }
    // special case where LOG NOW not to be shown
    if (loggableActivityType === undefined || loggableActivityType === "MEDICINE") {
      logAction = undefined
    }
    let logText
    const lastLoggedEpoch = widgetBuilderParams.goalAdherenceMetrics.adherenceMetrics.lastLoggedEpoch
    const lastLoggedDate = TimeUtil.formatEpochInTimeZone(tz, lastLoggedEpoch)
    const today = TimeUtil.formatEpochInTimeZone(tz, TimeUtil.getCurrentEpoch())
    const diffDays = TimeUtil.diffInDays(tz, lastLoggedDate, today)
    if (widgetBuilderParams.adherenceStatus === ADHERENCE_STATUS.EARLY) {
      logText = "You haven’t logged enough yet."
    }
    else {
      if (diffDays < 2) {
        logText = `Last logged ${TimeUtil.getDayText(lastLoggedDate, tz)}`
      }
      else {
        logText = `You havent logged since ${diffDays} days`
      }
    }
    return { text: logText, logAction: logAction }
  }

  private getAllGraphData( userContext: UserContext,
    widgetBuilderParams: IAdherenceMetricsWidgetBuilderParams
  ): MetricsGraphView[] {
    const graphs: MetricsGraphView[] = []
    const adherenceData =
      widgetBuilderParams.goalAdherenceMetrics.adherenceMetrics.values
    const graphData: Array<{
      key: string
      value: number
    }> = this.getLoggedValueGraphData(
      userContext,
      adherenceData,
      widgetBuilderParams.graphType,
      widgetBuilderParams.fromDate,
      widgetBuilderParams.toDate
    )
    const labelGraphData: Array<{ key: string, value: any }> = _.map(graphData, item => {
      const labelItem = {
        key: item.key,
        value: !_.isNil(item.value) ? `${_.round(item.value, 2)}%` : item.value
      }
      return labelItem
    })
    const idealValueGraphData = this.getIdealValueGraphData()
    graphs.push({
      type: "SOLID_LINE",
      data: graphData
    })
    graphs.push({
      type: "LABELS_ONLY",
      data: labelGraphData
    })
    graphs.push({
      type: "IDEAL_VALUE_LINE",
      data: idealValueGraphData
    })
    return graphs
  }

  private getLoggedValueGraphData( userContext: UserContext,
    adherenceData: { date: number; value: number }[],
    graphType: AggregationRange,
    fromDate: string,
    toDate: string
  ) {
    const tz = userContext.userProfile.timezone
    // sorting the array based on timestamp
    const sortedData = adherenceData.sort((a, b) =>
      momentTz.tz(a.date, tz).diff(momentTz.tz(b.date, tz))
    )

    const graphArray: Array<{ key: string; value: number }> = _.map(
      sortedData,
      item => {
        const key = this.getKeyForGraphData(userContext, item.date, graphType)
        return {
          key: key,
          value: item.value
        }
      }
    )

    if (graphType === AggregationRange.WEEK) {
      return getFinalGraphArrayForWeekAndMonth(fromDate, toDate, graphArray, tz)
    } else if (graphType === AggregationRange.MONTH) {
      // TODO pass start and end week number
      return getFinalGraphArrayForMonth(graphArray)
    } else {
      const startMonth = TimeUtil.getMomentForDateString(fromDate, tz).month()
      const endMonth = TimeUtil.getMomentForDateString(toDate, tz).month()
      return getFinalGraphArrayForYear(graphArray, startMonth, endMonth)
    }
  }

  private getIdealValueGraphData(): Array<{ key: string; value: string }> {
    const graphData: Array<{ key: string; value: string }> = [
      {
        key: "0",
        value: "Target"
      },
      {
        key: "1",
        value: "80%"
      }
    ]
    return graphData
  }

  private getCalloutText(widgetBuilderParams: IAdherenceMetricsWidgetBuilderParams): string {
    let contribution = widgetBuilderParams.goalAdherenceMetrics.contribution
    if (!_.isEmpty(contribution)) {
      contribution = _.orderBy(contribution, "weightage", ["desc"])
      let contributionString = ""
      _.forEach(contribution, (contributionValue, index) => {
        contributionString += (index > 0 && index % 2 == 0) ? "\n" : ""
        contributionString += `${contributionValue.category} - ${_.round(contributionValue.weightage, 2)}% `
      })
      return contributionString
    }
  }

  private getYAxisRangeValues() {
    const minValue = -25, interval = 25
    return [
      minValue,
      minValue + interval,
      minValue + 2 * interval,
      minValue + 3 * interval,
      minValue + 4 * interval,
      minValue + 5 * interval,
    ]
  }

  private getKeyForGraphData(userContext: UserContext, timeStamp: number, graphType: AggregationRange) {
    const date = TimeUtil.getMomentForDate(new Date(timeStamp), userContext.userProfile.timezone)
    if (graphType === AggregationRange.WEEK) {
      return date.format("YYYY-MM-DD")
    } else if (graphType === AggregationRange.MONTH) {
      const d = new Date(timeStamp)
      return (Math.ceil((d.getDate() - 1 - d.getDay()) / 7) - 1).toString()
    } else {
      return (date.month() - 1).toString()
    }
  }

  private mapToLoggableActivityType(
    adherenceCategory: DisplayCategory
  ): LoggableActivityType {
    switch (adherenceCategory) {
      case "Diet":
        return "DIET"
      case "Fitness":
        return "FITNESS"
      case "Medication":
        return "MEDICINE"
      case "Mindfulness":
        return "MEDITATE"
    }
  }
}
