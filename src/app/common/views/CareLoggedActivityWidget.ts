import * as _ from "lodash"
import { ActionUtil } from "../../util/ActionUtil"
import { LoggedActivityWidget } from "./LoggedActivityWidget"
import { ActivityDS } from "@curefit/logging-common"
import TimelineUtil from "../../util/TimelineUtil"
import { TimeUtil } from "@curefit/util-common"
import { BookingDetail, IHealthfaceService, TimelineActivity } from "@curefit/albus-client"
import { Logger } from "@curefit/base"
import { Action } from "./WidgetView"
import { ConsultationProduct } from "@curefit/care-common"
import { ICatalogueService } from "@curefit/catalog-client"
import { UserContext } from "@curefit/userinfo-common"

export interface ICareLogBuilderParams {
    healthfaceService: IHealthfaceService,
    catalogueService: ICatalogueService,
    activities: ActivityDS[],
    date: string,
    logger: Logger
    userContext: UserContext
}

export class CareLoggedActivityWidget extends LoggedActivityWidget {
    constructor() {
        super()
        this.title = "Care"
        this.calloutText = ""
        this.priority = 3
    }

    async buildView(widgetBuilderParams: ICareLogBuilderParams): Promise<void> {
        const activities: ActivityDS[] = widgetBuilderParams.activities
        const careItemAdditionPromise: Promise<void>[] = []

        // Convert care activities to widget items
        activities.forEach((activity: ActivityDS) => {
            if (activity.activityType === "CONSULTATION") {
                careItemAdditionPromise.push(this.addConsultationData(activity, widgetBuilderParams))
            } else if (activity.activityType === "AT_HOME_SAMPLE_COLLECTION") {
                careItemAdditionPromise.push(this.addAtHomeSampleCollectionData(activity, widgetBuilderParams))
            } else if (activity.activityType === "IN_CENTRE_VISIT_FOR_TEST") {
                careItemAdditionPromise.push(this.addInCenterTestData(activity, widgetBuilderParams))
            } else if (activity.activityType === "MEDICINE_INTAKE") {
                careItemAdditionPromise.push(this.addMedicineIntakeData(activity, widgetBuilderParams))
            }
        })

        await Promise.all(careItemAdditionPromise)
    }

    private async addConsultationData(activity: ActivityDS, widgetBuilderParams: ICareLogBuilderParams): Promise<void> {
        const tz = widgetBuilderParams.userContext.userProfile.timezone
        const bookingId: number = Number(activity.clientActivityId.split("-")[0])
        const appointmentId: number = Number(activity.clientActivityId.split("-")[1])
        const bookingDetail: BookingDetail = await widgetBuilderParams.healthfaceService.getBookingDetail(bookingId, appointmentId)
        const product: ConsultationProduct = <ConsultationProduct>await widgetBuilderParams.catalogueService.getProduct(bookingDetail.booking.productCode)
        const title: string = product.timelineTitle
        const careTimelineActivity: TimelineActivity = {
            carefitActivity: "CONSULTATION",
            subCategory: bookingDetail.booking.subCategoryCode,
            consultationStatus: bookingDetail.consultationOrderResponse.consultationUserState,
            title: title,
            description: `With ${bookingDetail.consultationOrderResponse.doctor.name}`,
            timestamp: bookingDetail.consultationOrderResponse.startTime,
            bookingInfo: bookingDetail
        }
        const isMissed: boolean = !_.isEmpty(careTimelineActivity.bookingInfo.consultationOrderResponse) && careTimelineActivity.bookingInfo.consultationOrderResponse.status === "MISSED"
        const parsedDate = TimeUtil.parseDateFromEpoch(careTimelineActivity.timestamp)
        const parsedDateMoment = TimeUtil.getMomentForDate(parsedDate, tz)
        const startTime = TimeUtil.getTimeIn12HRFormat(TimeUtil.formatDateInTimeZone(tz, parsedDate), parsedDateMoment.hours(), parsedDateMoment.minutes(), false, tz)
        const action: Action = await ActionUtil.getCareActivityAction(widgetBuilderParams.userContext, careTimelineActivity, widgetBuilderParams.healthfaceService, widgetBuilderParams.logger)
        this.items.push({
            title: title,
            calloutText: startTime,
            action: action,
            status: TimelineUtil.getCareStatus(careTimelineActivity, isMissed),
            activityType: "CONSULTATION"
        })
    }

    private async addAtHomeSampleCollectionData(activity: ActivityDS, widgetBuilderParams: ICareLogBuilderParams): Promise<void> {
        const tz = widgetBuilderParams.userContext.userProfile.timezone
        const bookingId: number = Number(activity.clientActivityId.split("-")[0])
        const bookingDetail: BookingDetail = await widgetBuilderParams.healthfaceService.getBookingDetail(bookingId)
        const careTimelineActivity: TimelineActivity = {
            carefitActivity: "DIAGNOSTICS",
            subCategory: bookingDetail.booking.subCategoryCode,
            consultationStatus: "COMPLETED",
            title: "At Home Test",
            description: `For ${bookingDetail.diagnosticsTestOrderResponse[0].patient.name}`,
            timestamp: bookingDetail.diagnosticsTestOrderResponse[0].atHomeDiagnosticOrder.startTime,
            bookingInfo: bookingDetail
        }
        const isMissed: boolean = !_.isEmpty(careTimelineActivity.bookingInfo.consultationOrderResponse) && careTimelineActivity.bookingInfo.consultationOrderResponse.status === "MISSED"
        const parsedDate = TimeUtil.parseDateFromEpoch(careTimelineActivity.timestamp)
        const parsedDateMoment = TimeUtil.getMomentForDate(parsedDate, tz)
        const startTime = TimeUtil.getTimeIn12HRFormat(TimeUtil.formatDateInTimeZone(tz, parsedDate), parsedDateMoment.hours(), parsedDateMoment.minutes(), false, tz)
        const action: Action = await ActionUtil.getCareActivityAction(widgetBuilderParams.userContext, careTimelineActivity, widgetBuilderParams.healthfaceService, widgetBuilderParams.logger)
        this.items.push({
            title: careTimelineActivity.title,
            calloutText: startTime,
            action: action,
            status: TimelineUtil.getCareStatus(careTimelineActivity, isMissed),
            activityType: "AT_HOME_SAMPLE_COLLECTION"
        })
    }

    private async addInCenterTestData(activity: ActivityDS, widgetBuilderParams: ICareLogBuilderParams): Promise<void> {
        const tz = widgetBuilderParams.userContext.userProfile.timezone
        const bookingId: number = Number(activity.clientActivityId.split("-")[0])
        const bookingDetail: BookingDetail = await widgetBuilderParams.healthfaceService.getBookingDetail(bookingId)
        const careTimelineActivity: TimelineActivity = {
            carefitActivity: "DIAGNOSTICS",
            subCategory: bookingDetail.booking.subCategoryCode,
            consultationStatus: "COMPLETED",
            title: "At Center Test",
            description: `For ${bookingDetail.diagnosticsTestOrderResponse[0].patient.name}`,
            timestamp: bookingDetail.diagnosticsTestOrderResponse[0].inCentreDiagnosticOrder.slot.workingStartTime,
            bookingInfo: bookingDetail
        }
        const isMissed: boolean = !_.isEmpty(careTimelineActivity.bookingInfo.consultationOrderResponse) && careTimelineActivity.bookingInfo.consultationOrderResponse.status === "MISSED"
        const parsedDate = TimeUtil.parseDateFromEpoch(careTimelineActivity.timestamp)
        const parsedDateMoment = TimeUtil.getMomentForDate(parsedDate, tz)
        const startTime = TimeUtil.getTimeIn12HRFormat(TimeUtil.formatDateInTimeZone(tz, parsedDate), parsedDateMoment.hours(), parsedDateMoment.minutes(), false, tz)
        const action: Action = await ActionUtil.getCareActivityAction(widgetBuilderParams.userContext, careTimelineActivity, widgetBuilderParams.healthfaceService, widgetBuilderParams.logger)
        this.items.push({
            title: careTimelineActivity.title,
            calloutText: startTime,
            action: action,
            status: TimelineUtil.getCareStatus(careTimelineActivity, isMissed),
            activityType: "IN_CENTRE_VISIT_FOR_TEST"
        })
    }

    private async addMedicineIntakeData(activity: ActivityDS, widgetBuilderParams: ICareLogBuilderParams): Promise<void> {
        if (activity.status === "DELETED") return
        this.items.push({
            title: activity.medicine.description,
            action: undefined,
            status: activity.status,
            activityType: "MEDICINE_INTAKE"
        })
    }
}
