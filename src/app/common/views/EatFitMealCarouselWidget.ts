import * as _ from "lodash"
import { Action, ActionList } from "./WidgetView"
import { FoodBooking, FoodBookingProduct } from "@curefit/shipment-common"
import { ICatalogueService } from "@curefit/catalog-client"
import IProductBusiness from "../../product/IProductBusiness"
import { ActivityState } from "@curefit/logging-common"
import { ActivityType } from "@curefit/product-common"
import { CustomerIssueType } from "@curefit/issue-common"
import { MealSlot, FoodProduct as Product } from "@curefit/eat-common"
import { SlotUtil } from "@curefit/eat-util"
import { UrlPathBuilder } from "@curefit/product-common"
import { TimeUtil } from "@curefit/util-common"
import { UserContext } from "@curefit/userinfo-common"
import { CarouselWidget, ImageCardCarouselView } from "./CarouselWidget"
import { Logger } from "@curefit/base"
import { ITodoActivityWidget } from "./TodoActivityWidget"
import TimelineUtil from "../../util/TimelineUtil"

export interface IEatFitMealCarouselWidgetParams {
    booking: FoodBooking,
    isChangeable: boolean,
    catalogueService: ICatalogueService,
    productBusiness: IProductBusiness,
    issuesMap: Map<string, CustomerIssueType[]>,
    userContext: UserContext,
    logger: Logger
}

export class EatFitMealCarouselWidget extends CarouselWidget implements ITodoActivityWidget {
    timestamp: number
    status: ActivityState
    activityType: ActivityType
    bookingNumber?: string
    constructor() {
        super()
        this.widgetType = "TODO_ACTIVITY_WIDGET"
    }

    async buildView(eatfitWidgetParams: IEatFitMealCarouselWidgetParams): Promise<EatFitMealCarouselWidget> {
        const booking: FoodBooking = eatfitWidgetParams.booking
        try {
            // Assign title
            const isCafe: boolean = !_.isNil(booking.address) && (booking.address.kioskType === "CAFE")
            const mealSlot: MealSlot = SlotUtil.getMealSlotForSlotId(booking.deliverySlot.slotId, booking.timezone)
            this.title = !isCafe ? _.capitalize(mealSlot) : "Workout Snack"

            // Assign text items (ETA, current status) and timestamp
            const orderStatus: { eta: Date, status: string, statusText: string } = eatfitWidgetParams.productBusiness.getOrderStatusForFoodBooking(booking, eatfitWidgetParams.userContext)

            let timeDiff = _.ceil((orderStatus.eta.getTime() - new Date().getTime()) / 60000)
            if (orderStatus.status === "ARRIVED" || timeDiff < 0) {
                timeDiff = 2
            }
            const etaStr: string = TimeUtil.get12HRTimeFormat(orderStatus.eta, booking.timezone)
            if (booking.state === "REJECTED") {
                this.rightCalloutText = undefined
                this.title += " not delivered!"
                this.leftIcon = "WARNING"
            } else if (timeDiff > 60) {
                this.rightCalloutText = "ETA: " + etaStr
            }
            else {
                this.rightCalloutText = "ETA: " + timeDiff + " mins"
            }
            // to not show the eta on the right until the shipment is created.
            if (!booking.state || booking.state === "NOT_STARTED") {
                this.rightCalloutText = undefined
            }

            this.leftCalloutText = booking.state === "REJECTED" ? undefined : etaStr
            // TODO: Remove this. Adding to avoid inconsistency with tracking screen
            this.rightCalloutText = booking.state === "REJECTED" ? undefined : "ETA " + etaStr
            this.timestamp = orderStatus.eta.getTime()

            // Assign status
            this.status = TimelineUtil.getEatFitStatus(booking.state)

            // Assign activity type
            this.activityType = "EATFIT_MEAL"

            if (!_.isNil(booking.meta)) {
                this.bookingNumber = booking.meta.referrerId
            }
            // Create the meal card
            const mealCard: ImageCardCarouselView = await this.getCardForMeal(eatfitWidgetParams)
            mealCard.calloutText = orderStatus.statusText
            this.cards.push(mealCard)
            return this
        } catch (e) {
            eatfitWidgetParams.logger.error("Widget Building failed for Fulfilment ID : " + booking.fulfilmentId + " User ID: " + eatfitWidgetParams.userContext.userProfile.userId)
            eatfitWidgetParams.logger.error(e.stack)
            return undefined
        }
    }

    private async getCardForMeal(eatfitWidgetParams: IEatFitMealCarouselWidgetParams): Promise<ImageCardCarouselView> {
        const booking: FoodBooking = eatfitWidgetParams.booking
        const userContext: UserContext = eatfitWidgetParams.userContext
        const mealCard: ImageCardCarouselView = {
            viewType: "BORDERLESS",
            styleType: "WIDE",
            title: "",
            images: [],
            action: {
                actionType: "NAVIGATION",
                url: ""
            },
            quickActions: []
        }
        // Get all products ordered from catalogue service
        const productIds: string[] = _.map(booking.products, (foodProduct: FoodBookingProduct) => {
            return foodProduct.productId
        })
        const productMap: { [productId: string]: Product } = await eatfitWidgetParams.catalogueService.getProductMap(productIds)

        // Generate image URLs for all products
        booking.products.forEach((foodBooking: FoodBookingProduct) => {
            const foodProduct: Product = productMap[foodBooking.productId]
            const image = UrlPathBuilder.getSingleImagePath(foodProduct.parentProductId ? foodProduct.parentProductId : foodProduct.productId, "FOOD", booking.products.length > 1 ? "HERO" : "LANDSCAPE", foodProduct.imageVersion)
            mealCard.images.push(image)
        })

        // Generate action and title
        mealCard.action.url = await eatfitWidgetParams.productBusiness.getNavigationUrlForFoodBooking(userContext, booking)
        if (booking.state === "REJECTED") {
            mealCard.title = booking.products.length > 1 ? booking.products.length + " items ordered" : productMap[booking.productId].title
            mealCard.subTitle = this.getUserFriendlyRejectionReason(booking.reason)
            mealCard.showSubTitleOnCollapse = true
        } else if (booking.products.length > 1) {
            mealCard.title = booking.products.length + " items ordered"
        } else {
            const foodProduct: Product = productMap[booking.productId]
            mealCard.title = foodProduct.title
        }

        // Generate quick actions
        const possibleActions: Action[] = await eatfitWidgetParams.productBusiness.getPossibleActionsForFoodBooking(booking, eatfitWidgetParams.userContext, null, null, ["CHANGE_MEAL", "PICKUP_MEAL", "TRACK_MEAL", "UNDO_CANCEL_MEAL", "UNDO_CANCEL_CART", "CANCEL_MEAL", "CANCEL_CART"])
        const primaryAction: Action = possibleActions[0]
        const primaryActionList: ActionList = _.isNil(primaryAction) ? undefined : _.assign({ actions: [] }, primaryAction)
        const secondaryActions: Action[] = possibleActions.slice(1)
        const secondaryActionList: ActionList = _.isEmpty(secondaryActions) ? undefined : { actionType: "ACTION_LIST", actions: secondaryActions, icon: "MANAGE" }
        if (!_.isNil(primaryActionList)) {
            mealCard.quickActions.push(primaryActionList)
        }
        if (!_.isNil(secondaryActionList)) {
            mealCard.quickActions.push(secondaryActionList)
        }
        return mealCard
    }

    // Should be changed to get a type in the future
    private getUserFriendlyRejectionReason(reason: string) {
        switch (reason) {
            case "Customer not at location":
            case "Customer asked to wait":
                return "No one was there at the location to collect the order"
            case "Customer does not want the order":
                return "Order returned as per your discussion with Health Crew"
            case "Customer not reachable":
                return "Health Crew could not reach you over call"
            default:
                return "Order returned"
        }
    }
}
