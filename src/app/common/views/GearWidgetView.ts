import * as _ from "lodash"
import { Action, ActionType, WidgetType, WidgetView } from "./WidgetView"
import { UserDeliveryAddress } from "@curefit/eat-common"
import { ActionUtil } from "@curefit/base-utils"
import { PriceComponent } from "../../order/OrderViewBuilder"
import {
    ActivityType,
    GearAddress,
    GearOrderShipment,
    LineItem,
    TRACKABLE_INVENTORY_UNIT_STATES,
    GearOrderDetailObject,
    AddressV2,
    GearInventoryUnit,
    GearShipmentInventoryUnit
} from "@curefit/gear-common"
import { PaymentChannel, PaymentMode } from "@curefit/payment-common"
import { TimeUtil, Timezone, PriceUtil } from "@curefit/util-common"
import { BaseOrder, CultsportMetadata, Order } from "@curefit/order-common"
import { InventoryUnit } from "../../order/GearOrderWithShipmentViewBuilder"
import { UserContext } from "@curefit/userinfo-common"
import AppUtil from "../../util/AppUtil"
import { GearUtil } from "@curefit/gearvault-client"
import { GearProductDetails, OfferAddonType } from "@curefit/offer-common"
import { CouponInfoWidget, IWidgetType, User } from "@curefit/apps-common"
import { GearCartListWidget } from "../../cart/GearCartViewBuilder"
import { FeedbackRequest, IRating } from "@curefit/cultsport-feedback-client"
import { DividerType, WidgetMetric, WidgetWithMetric } from "@curefit/vm-common"
export interface IStructuredAddressBuildParams {
    isEmpty: boolean
    addressId?: string
    name?: string
    addressLine1?: string,
    addressLine2?: string
    locality?: string
    landmark?: string
    city?: string,
    state?: string
    country?: string
    pincode?: string
    phoneNumber?: string
}

export interface IPriceDetail {
    mrp: number,
    listingPrice: number
}

export interface AddonOfferWidgetData {
    title: string
    message: string
    addonType: OfferAddonType
    couponInfo?: CouponInfoWidget
}

export class GearAddressWidget implements WidgetView {
    public widgetType: WidgetType
    public addressId: string
    public address: string
    public phone: string = ""
    public actions: Action[] = []

    constructor(address: UserDeliveryAddress) {
        this.widgetType = "GEAR_ADDRESS_WIDGET"
        this.address = "Please add address"

        const localActions = {
            actionType: "CHANGE_ADDRESS" as ActionType,
            url: ActionUtil.selectAddress("gear_cart_review"),
            title: "Add/Modify"
        }

        if (!_.isEmpty(address)) {
            this.addressId = address.addressId
            this.address = `${address.addressLine1}, ${address.addressLine2}`

            if (address.phoneNumber) {
                this.phone = `Mobile: ${address.phoneNumber}`
            }

            localActions.title = "Modify"
        }

        this.actions.push(localActions)
    }
}

export class CSCheckoutVoucherWidgetV2 implements WidgetView {
    public widgetType: any = "CULTSPORT_CHECKOUT_VOUCHER_WIDGET_V2"
    public title: string
    public action: any
    public benefitText: string
    public voucherInfo: {
        savingsInfo: any
        couponCode: string
        benefits?: string[]
        endDate?: string
        offerTitle?: string
        offerId: string
        isEligible: boolean
        savings: number
    }

    constructor(order: Order, gearCartListWidget: GearCartListWidget) {
        const offerMap: any = {}
        const offerIdToDiscountMap: any = {}
        const couponCode = order?.couponCode
        const isCouponPresent = !_.isEmpty(couponCode)
        const clientMetaData: CultsportMetadata = order?.clientMetadata as CultsportMetadata
        const isEligible = !_.isEmpty(clientMetaData?.offerId)
        const couponOfferId = clientMetaData?.offerId
        const skuIdToProductDetails: { [x: string]: GearProductDetails } = {}
        const articleTypeToMasterCategory: { [x: string]: string } = {}
        const skuToListingPriceBeforeOrderDiscount: { [x: string]: number } = {}

        gearCartListWidget.orderItems.forEach(orderItem => {
            if (orderItem.in_stock && orderItem.is_serviceable) {
                skuIdToProductDetails[orderItem.productId] = {
                    masterProductId: orderItem.masterProductId.toString(),
                    productCategory: orderItem.category,
                    productBrand : orderItem.productBrand
                }
                articleTypeToMasterCategory[orderItem.category] = orderItem.superCategory
                skuToListingPriceBeforeOrderDiscount[orderItem.productId] = orderItem.price.listingPrice * orderItem.quantity
            }
        })

        const cultsportVouchersRequestBody = !isCouponPresent ? {
            skuIdToProductDetails,
            articleTypeToMasterCategory,
            skuToListingPriceBeforeOrderDiscount,
            orderTotalPayable: order.totalPayable,
            orderTotalPayableWithoutDiscount: order.priceDetails.total_without_discount,
            productPriceDetails: gearCartListWidget.orderItems.map(orderItem => {
                if (orderItem.in_stock && orderItem.is_serviceable) {
                    return {
                        listingPrice: orderItem.price.mrp,
                        mrp: orderItem.price.mrp,
                        productId: orderItem.productId,
                        productVariantIdentifier: orderItem.productId,
                        quantity: orderItem.quantity,
                        productType: "GEAR"
                    }
                }
               return undefined
            }).filter(Boolean)
        } : undefined
        if (order?.offersInfo && isEligible) {
            order.offersInfo.forEach((offer) => {
                offerMap[offer.offerId] = offer
            })
            order.productSnapshots.map((productSnapShot => {
                if (productSnapShot?.option?.offersInfo?.length > 0) {
                    productSnapShot?.option?.offersInfo.forEach(offerInfo => {
                        if (offerInfo?.offerId && offerInfo.discount > 0) {
                            const offerId = offerInfo?.offerId
                            if (offerIdToDiscountMap[offerId]) {
                                offerIdToDiscountMap[offerId] =  (offerIdToDiscountMap[offerId] ? offerIdToDiscountMap[offerId] : 0) + offerInfo.discount * productSnapShot.quantity
                            } else {
                                offerIdToDiscountMap[offerId] = offerInfo.discount * productSnapShot.quantity
                            }
                        }
                    })
                }
            }))
        }

        this.title = "Offers and Benefits"
        this.benefitText = isCouponPresent ? undefined :  "Get more with special discounts for you"

        this.voucherInfo = isCouponPresent ? {
            couponCode: couponCode,
            offerId: couponOfferId,
            isEligible: isEligible,
            savings: isEligible ? offerIdToDiscountMap[couponOfferId] || 0 : 0,
            savingsInfo: [{
                    "items": isEligible
                        ? [{
                            "text": "Saved ₹" + Math.round(offerIdToDiscountMap[couponOfferId] || 0),
                            "textStyle": "BOLD",
                            "textColor": "#009217"
                        },
                        {
                            "text": " on this order",
                            "textStyle": "REGULAR",
                            "textColor": "#000000"
                        }
                        ]
                    : [{
                        "text": "Your order is not eligible for this coupon",
                        "textStyle": "REGULAR",
                        "textColor": "#FF6759"
                      },
                    ]
            }]
        } : undefined
        this.action = isCouponPresent
            ? {actionType: "REMOVE_COUPON", title: "Remove"}
            : {
                actionType: "SHOW_VOUCHER_LIST_MODAL",
                title: isEligible ? "Remove" : "Apply Coupon",
                meta: cultsportVouchersRequestBody
            }
    }
}


export class CSOrderDeliveryAddressWidget implements WidgetWithMetric {
    public widgetType: any = "CULTSPORT_CHECKOUT_ADDRESS_WIDGET"
    public addressId: string
    public address: string
    public name: string
    public phoneNumber: string
    public headerTitle: string
    public iconUrl: string
    public widgetMetric: any
    public showDivider: boolean
    public headerDivider: boolean
    public dividerType: DividerType

    constructor(user: User, address: UserDeliveryAddress) {
        this.name = address?.name ?? (user && (user.firstName + " " + user.lastName))
        this.phoneNumber = address?.phoneNumber ?? user?.phone
        this.widgetType = "CULTSPORT_CHECKOUT_ADDRESS_WIDGET"
        this.headerTitle = "Delivery Details"
        this.iconUrl = "/assets/images/address-icon-2.svg"
        this.headerDivider = false
        if (!_.isEmpty(address)) {
            this.addressId = address.addressId
            this.address = `${address.addressLine1}, ${address.addressLine2}`
        }
    }
}

export class CSOrderDeliveryTrackWidget implements WidgetWithMetric {
    public widgetType: any = "CULTSPORT_ORDER_DELIVERY_TRACK_WIDGET"
    public actions: Action[] = []
    public subTitle: string
    public trackOrderURL: string
    public widgetMetric: any
    public showDivider: boolean
    public dividerType: DividerType

    constructor() {
        this.widgetType = "CULTSPORT_ORDER_DELIVERY_TRACK_WIDGET"
        this.subTitle = "Track/view orders from order details page"
        this.trackOrderURL = "/assets/images/trackOrder.svg"
        const actions = {
            actionType: "NAVIGATION" as ActionType,
            url: `/me/orders`,
            title: "View Order Details"
        }

        this.actions.push(actions)
    }
}

export class CultsportOrderProductCardWidget implements WidgetWithMetric {
    public widgetType: any = "CULTSPORT_ORDER_PRODUCT_CARD_WIDGET"
    public title: string
    public subtitle: string
    public brand: string
    public imageUrl: string
    public action: Action
    public primaryAction: Action
    public dividerType: DividerType
    public showDivider: boolean
    public widgetMetric: WidgetMetric

    constructor(title: string, subtitle: string, brand: string, imageUrl: string, action: Action, primaryAction: Action) {
        this.title = title
        this.subtitle = subtitle
        this.brand = brand
        this.imageUrl = imageUrl
        this.action = action
        this.primaryAction = primaryAction
    }
}

export class CSOrderItemsWidget implements WidgetWithMetric {
    public widgetType: any = "CULTSPORT_ORDER_ITEMS_WIDGET"
    public dividerType: DividerType
    public showDivider: boolean
    public widgetMetric: WidgetMetric
    public cultsportOrderProductCardWidgets: CultsportOrderProductCardWidget[]
    public csOrderUpdateUserInfo: CsOrderUpdateUserInfo

    constructor(cultsportOrderProductCardWidgets: CultsportOrderProductCardWidget[], csOrderUpdateUserInfo: CsOrderUpdateUserInfo) {
        this.cultsportOrderProductCardWidgets = cultsportOrderProductCardWidgets
        this.csOrderUpdateUserInfo = csOrderUpdateUserInfo
    }
}

export class CSOrderFeedbackInputWidget implements WidgetWithMetric {
    public widgetType: any = "CULTSPORT_ORDER_FEEDBACK_INPUT_WIDGET"
    public title: string
    public ratings: IRating[]
    public feedbackAction: any
    public widgetMetric: any
    public showDivider: boolean
    public dividerType: DividerType
    public headerDivider: boolean
    constructor(FeedbackRequest: FeedbackRequest) {
        if (FeedbackRequest && FeedbackRequest.feedbackConfig) {
            this.headerDivider = true
            this.title = FeedbackRequest.feedbackConfig.feedbackQuestion
            this.ratings = FeedbackRequest.feedbackConfig.ratings
            this.feedbackAction = {
                    actionType: "NAVIGATION",
                    url: `/feedback/${FeedbackRequest.id}`
                }
        }
    }
}

export class CsOrderUpdateUserInfo {
    public title: string
    public imageUrl: string
    public phoneNumber: string

    constructor(title: string, phoneNumber: string, imageUrl: string) {
        this.title = title
        this.phoneNumber = phoneNumber
        this.imageUrl = imageUrl
    }
}

export class GearModifyPickupAddressWidget implements WidgetView {
    public widgetType: WidgetType
    public addressId: string
    public address: string
    public actions: Action[] = []
    public title: string

    constructor(inventoryUnit: GearInventoryUnit, address: UserDeliveryAddress) {
        this.widgetType = "GEAR_ADDRESS_WIDGET"
        this.title = "Pickup Address"
        this.address = `${inventoryUnit.address.address1}, ${inventoryUnit.address.address2}`

        if (!_.isEmpty(address)) {
            this.addressId = address.addressId
            this.address = `${address.addressLine1}, ${address.addressLine2}`
        }

        const actions = {
            actionType: "MODIFY_GEAR_PICKUP_ADDRESS" as ActionType,
            url: ActionUtil.selectAddress("modify_gear_order"),
            title: "Modify"
        }

        this.actions.push(actions)
    }
}

export class GearShipmentArrivalInfoWidget implements WidgetView {
    public widgetType: WidgetType
    public title: string
    public estimatedTime: string

    constructor(eta: string, timezone: Timezone = TimeUtil.IST_TIMEZONE) {
        this.widgetType = "GEAR_DELIVERY_ESTIMATE_WIDGET"
        this.title = "Delivery expected by"
        this.estimatedTime = eta ? TimeUtil.formatDateInTimeZone(timezone, new Date(eta), "Do MMMM YYYY [by] h:mm a") :
            "Please fill your address to see the estimated delivery date"
    }
}

export class GearLineItemsWidget implements WidgetView {
    public widgetType: WidgetType
    public lineItems: LineItem[]

    constructor(lineItems: LineItem[]) {
        this.widgetType = "GEAR_LINE_ITEMS_WIDGET"
        this.lineItems = lineItems
    }
}

export class GearOrderTrackingWidget implements WidgetView {
    public widgetType: WidgetType
    public shipments: GearOrderShipment[]

    constructor(shipments: GearOrderShipment[], paymentMode: PaymentMode, userContext: UserContext) {
        const disabledCancellationStates = ["shipped", "return_approved"]
        shipments.forEach(shipment => {
            shipment.inventory_units = _.map(shipment.inventory_units, (inventoryUnit: any) => {
                const actions: Action[] = [{
                    actionType: "NAVIGATION",
                    url: ""
                }]

                if (AppUtil.isWeb(userContext) && inventoryUnit.is_replacement) {
                    inventoryUnit.cancelable = false
                }

                // TODO: @kartik, move trackable logic to gearvault
                if (TRACKABLE_INVENTORY_UNIT_STATES.indexOf(inventoryUnit.state) > -1) {
                    // if return shipment is present, show that in tracking
                    actions[0].url = ActionUtil.gearTrackingUrl(inventoryUnit.return_shipment_id || shipment.id)
                }

                if (inventoryUnit.cancelable && !_.includes(disabledCancellationStates, inventoryUnit.state)) {
                    inventoryUnit.modifyAction = {
                        actionType: "NAVIGATION",
                        title: "Cancel",
                        url: ActionUtil.gearCancelGetUrl(inventoryUnit.line_item.id)
                    }
                } else if (inventoryUnit.returnable && inventoryUnit.replaceable) {
                    inventoryUnit.modifyAction = {
                        actionType: "NAVIGATION",
                        title: "Return/Exchange",
                        url: ActionUtil.gearReturnOrReplaceGetUrl(inventoryUnit.id, paymentMode as string)
                    }
                } else if (inventoryUnit.returnable) {
                    inventoryUnit.modifyAction = {
                        actionType: "NAVIGATION",
                        title: "Return",
                        url: ActionUtil.gearReturnOrReplaceGetUrl(inventoryUnit.id, paymentMode as string)
                    }
                } else if (inventoryUnit.replaceable) {
                    inventoryUnit.modifyAction = {
                        actionType: "NAVIGATION",
                        title: "Exchange",
                        url: ActionUtil.gearReturnOrReplaceGetUrl(inventoryUnit.id, paymentMode as string)
                    }
                }
                const finalInstallationCancelationText = AppUtil.getInstallationCancellationInfoText(inventoryUnit.installation_text, inventoryUnit.non_cancelable_text)

                inventoryUnit.installation_text = !_.isEmpty(finalInstallationCancelationText) ? finalInstallationCancelationText : undefined

                inventoryUnit.actions = actions

                if (inventoryUnit.is_replacement) {
                    inventoryUnit.variant.name += "\n(Replacement)"
                }

                return {
                    ...inventoryUnit,
                    action: {
                        actionType: "NAVIGATION",
                        url: ActionUtil.gearProduct(inventoryUnit.variant.product_id.toString(), inventoryUnit.variant.sku,  AppUtil.isCultSportWebApp(userContext) ? inventoryUnit.variant?.slug : undefined)
                    }
                }
            })
        })

        this.widgetType = "GEAR_ORDER_TRACKING_WIDGET"
        this.shipments = shipments
    }
}

export interface PaymentDetail {
    refundAmount: number
    cashbackAmount: number
    paymentChannel: PaymentChannel
    paymentMode: PaymentMode
}

export interface GearAddressV2 extends AddressV2 {
    addressType?: string
    addressId: string
    userId: string
}

interface GearOrderDetailV2 {
    orderId: string
    paymentMode: string
    amountPaid: string
    priceDetails: PriceComponent[]
    activityType: string
    orderDate: Date
    refundAmount?: number
    cashbackAmount?: number
    pageToken?: string
    address?: GearAddressV2
    inventoryUnits?: InventoryUnit[]
}

export class GearOrderTrackingWidgetV2 implements WidgetView {
    public widgetType: WidgetType
    public orderDetail: GearOrderDetailV2
    constructor(gearOrder: GearOrderDetailObject, order: BaseOrder, paymentDetail: PaymentDetail, inventoryUnits: InventoryUnit[]) {
        const orderDetail: GearOrderDetailV2 = {
            orderId: gearOrder.external_service_order_id,
            paymentMode: GearUtil.getPresentationNameForPaymentMode(paymentDetail.paymentMode) || paymentDetail.paymentChannel,
            amountPaid: PriceUtil.formatMoney(gearOrder.payment_total),
            priceDetails: [
                {
                    title: "Price",
                    value: PriceUtil.formatMoney(order.priceDetails.total_without_discount)
                },
                {
                    title: "Discount (-)",
                    value: PriceUtil.formatMoney(Math.abs(order.priceDetails.discount))
                },
                ...(order.totalFitCashPayable
                    ? [
                        {
                            title: "Fitcash Discount (-)",
                            value: PriceUtil.formatMoney(order.totalFitCashPayable)
                        }
                    ]
                    : []),
                {
                    title: "Total",
                    value: PriceUtil.formatMoney(order.totalAmountPayable)
                }
            ],
            activityType: ActivityType.CULT_GEAR_ECOMMERCE,
            orderDate: new Date(gearOrder.created_at),
            refundAmount: paymentDetail.refundAmount,
            cashbackAmount: paymentDetail.cashbackAmount,
            pageToken: gearOrder.created_at ? gearOrder.created_at.toString() : undefined,
            address: {
                addressId: order.userAddress.addressId,
                addressLine1: order.userAddress.addressLine1,
                addressLine2: order.userAddress.addressLine2,
                addressType: order.userAddress.addressType,
                structuredAddress: order.userAddress.structuredAddress,
                googlePlacesId: order.userAddress.googlePlacesId,
                latLong: order.userAddress.latLong,
                userId: order.userAddress.userId
            },
            inventoryUnits: inventoryUnits
        }
        this.widgetType = "GEAR_ORDER_TRACKING_WIDGET_V2"
        this.orderDetail = orderDetail
    }
}

export class GearOrderDetailsWidget implements WidgetView {
    public widgetType: WidgetType
    public order: { bill_address: GearAddress, ship_address: GearAddress }

    constructor(order: { bill_address: GearAddress, ship_address: GearAddress }) {
        this.widgetType = "GEAR_ORDER_DETAILS_WIDGET"
        this.order = order
    }
}

export class PriceDetailsWidget implements WidgetView {
    public widgetType: WidgetType
    public components: PriceComponent[]

    constructor(components: PriceComponent[], widgetType: WidgetType = "PRICE_DETAILS_WIDGET") {
        this.widgetType = widgetType
        this.components = components
    }
}

export class AddonOfferCalloutWidget implements WidgetView {
    public widgetType: WidgetType
    private rewardData: AddonOfferWidgetData
    private message: string

    constructor(rewardData: AddonOfferWidgetData, widgetType: WidgetType = "GEAR_ORDER_ADDON_OFFER_CALLOUT_WIDGET") {
        this.widgetType = widgetType
        this.rewardData = rewardData
    }
}

export interface IGearOrderDetailBuildParams {
    orderId: string
    paymentMode: string
    price: IPriceDetail
    title: string,
    activityType: string
    subActivityType?: string
    orderDate: string
    widgets: WidgetView[]
    refundAmount: number
    cashbackAmount: number
    pageToken: Date
    actions: Action[]
}

export class GearOrderDetail {
    public orderId: string
    public paymentMode: string
    public price: IPriceDetail
    public title: string
    public activityType: string
    public subActivityType?: string
    public orderDate: string
    public widgets: WidgetView[]
    public refundAmount: number
    public cashbackAmount: number
    public pageToken: Date
    public actions: Action[]

    constructor(attributes: IGearOrderDetailBuildParams) {
        this.orderId = attributes.orderId
        this.paymentMode = attributes.paymentMode
        this.price = attributes.price
        this.title = attributes.title
        this.activityType = attributes.activityType
        this.orderDate = attributes.orderDate
        this.widgets = attributes.widgets
        this.refundAmount = attributes.refundAmount
        this.cashbackAmount = attributes.cashbackAmount
        this.pageToken = attributes.pageToken
        this.actions = attributes.actions
        this.subActivityType = attributes.subActivityType
    }
}

export interface GearAddon {
    name: string,
    price: string,
    primaryPrice: string,
    secondaryPrice: string,
    image_url: string
    actions: Action[]
}

export interface GearCartAddonsBuildParams {
    title: string
    addons: GearAddon[]
}

export class GearCartAddonWidget implements WidgetView {
    public widgetType: WidgetType
    public title: string
    public addons: GearAddon[]

    constructor(attributes: GearCartAddonsBuildParams) {
        this.widgetType = "GEAR_CART_ADDON_WIDGET"
        this.title = attributes.title
        this.addons = attributes.addons
    }
}
