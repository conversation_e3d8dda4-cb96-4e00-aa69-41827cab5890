import * as _ from "lodash"
import { ActionUtil } from "../../util/ActionUtil"
import { LoggedActivityWidget } from "./LoggedActivityWidget"
import { ActivityDS, WorkoutMetric } from "@curefit/logging-common"
import { ICatalogueService } from "@curefit/catalog-client"
import { WalkActivity } from "@curefit/atlas-client"
import TimelineUtil from "../../util/TimelineUtil"
import { CultBooking } from "@curefit/cult-common"
import { ICultServiceOld as ICultService } from "@curefit/cult-client"
import { DIYPack } from "@curefit/diy-common"
import { ActionUtil as EtherActionUtil, ActionUtilV1, SeoUrlParams } from "@curefit/base-utils"
import { IActivityLoggingBusiness } from "../../logging/ActivityLoggingBusiness"
import { FitnessLoggableItem } from "@curefit/maverick-common"
import { TimeUtil } from "@curefit/util-common"
import { ActivityLoggingUtil } from "../../util/ActivityLoggingUtil"
import { IHealthfaceService, BookingDetail, TimelineActivity } from "@curefit/albus-client"
import { ConsultationProduct } from "@curefit/care-common"
import { Action } from "./WidgetView"
import { Logger } from "@curefit/base"
import { UserContext } from "@curefit/userinfo-common"
import { CacheHelper } from "../../util/CacheHelper"

export interface IFitnessLogBuilderParams {
    catalogueService: ICatalogueService,
    cultFitService: ICultService,
    activityLoggingBusiness: IActivityLoggingBusiness
    activities: ActivityDS[]
    cultPTService: IHealthfaceService
    logger: Logger,
    userContext: UserContext,
    userCache: CacheHelper
}

export class FitnessLoggedActivityWidget extends LoggedActivityWidget {
    constructor() {
        super()
        this.title = "Fitness"
        this.calloutText = ""
        this.priority = 1
    }

    async buildView(widgetBuilderParams: IFitnessLogBuilderParams): Promise<void> {
        const activities: ActivityDS[] = widgetBuilderParams.activities
        // Convert fitness activities to widget items
        const fitnessItemAdditionPromise: Promise<void>[] = activities.map(async (activity: ActivityDS) => {
            let subUserName = ""
            if (widgetBuilderParams.userContext.userProfile.userId !== activity.userId) {
                const subUserDetails = await widgetBuilderParams.userCache.getUser(activity.userId)
                subUserName = `${subUserDetails.firstName} ${subUserDetails.lastName}`
            }
            if (activity.activityType === "WALK") {
                return this.addStepsData(activity)
            } else if (activity.activityType === "CULT_CLASS") {
                return this.addCultClassData(activity, widgetBuilderParams, subUserName)
            } else if (activity.activityType === "CULT_SCORE_DIY") {
                return this.addCultScoreData(activity)
            } else if (activity.activityType === "DIY_FITNESS") {
                return this.addDiyFitnessData(activity, widgetBuilderParams)
            } else if (activity.activityType === "WORKOUT_ACTIVITY") {
                return this.addSelfWorkoutData(activity, widgetBuilderParams)
            } else if (activity.activityType === "GMF_FITNESS_RECOMMENDATION") {
                return this.addRecommendationData(activity, widgetBuilderParams)
            } else if (activity.activityType === "CONSULTATION_PT") {
                return this.addCultPTSessionData(activity, widgetBuilderParams)
            }
        })

        await Promise.all(fitnessItemAdditionPromise)
    }

    private async addStepsData(activity: ActivityDS): Promise<void> {
        try {
            const walkActivity: WalkActivity = TimelineUtil.createStepsAtlasActivity(activity)
            this.items.push({
                title: walkActivity.steps.done + " Steps",
                calloutText: activity.score + " pts",
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtil.getStepsUrl(walkActivity)
                },
                status: "DONE",
                activityType: "WALK"
            })
        } catch (e) {
            return
        }
    }

    private async addCultClassData(activity: ActivityDS, widgetBuilderParams: IFitnessLogBuilderParams, subUserName: string): Promise<void> {
        try {
            const cultbooking: CultBooking = await widgetBuilderParams.cultFitService.getBookingById(activity.meta.fulfilmentId, activity.userId)
            if (cultbooking.label !== "Cancelled" && cultbooking.label !== "Dropped out") {
                const title = subUserName ? `${cultbooking.Class.Workout.name} (For ${subUserName})` : cultbooking.Class.Workout.name
                this.items.push({
                    title: title,
                    calloutText: activity.score + " pts",
                    action: {
                        actionType: "NAVIGATION",
                        url: ActionUtil.getCultMindClassUrl("FITNESS", cultbooking.bookingNumber)
                    },
                    status: TimelineUtil.getCultStatus(cultbooking),
                    activityType: "CULT_CLASS"
                })
            }
        } catch (e) {
            return
        }
    }

    private async addDiyFitnessData(activity: ActivityDS, widgetBuilderParams: IFitnessLogBuilderParams): Promise<void> {
        try {
            const diyPack = <DIYPack>await widgetBuilderParams.catalogueService.getProduct(activity.meta.packId)
            const sessionIndex = diyPack.sessionIds.indexOf(activity.productId) + 1
            this.items.push({
                title: `${diyPack.title} (${sessionIndex} of ${diyPack.sessionIds.length})`,
                calloutText: activity.score + " pts",
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtilV1.diyPackProductPage(diyPack, widgetBuilderParams.userContext.sessionInfo.userAgent)
                },
                status: "DONE",
                activityType: "DIY_FITNESS"
            })
        } catch (e) {
            return
        }
    }

    private async addCultScoreData(activity: ActivityDS): Promise<void> {
        this.items.push({
            title: "Cult Score",
            calloutText: activity.score + " pts",
            action: undefined,
            status: "DONE",
            activityType: "CULT_SCORE_DIY"
        })
    }

    private async addSelfWorkoutData(activity: ActivityDS, widgetBuilderParams: IFitnessLogBuilderParams): Promise<void> {
        try {
            const tz = widgetBuilderParams.userContext.userProfile.timezone
            if (activity.status === "DELETED") return
            const selfWorkout: FitnessLoggableItem = await widgetBuilderParams.activityLoggingBusiness.searchByWorkoutId(activity.workout.workoutId)
            this.items.push({
                title: this.generateTitleForSelfWorkoutActivity(selfWorkout, activity),
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtil.addLoggableActivityUrl("FITNESS", {
                        slotId: "ALLDAY",
                        date: TimeUtil.formatDateInTimeZone(tz, new Date(activity.workout.activityTime)),
                    })
                },
                status: activity.status,
                activityType: "WORKOUT_ACTIVITY"
            })
        } catch (e) {
            return
        }
    }

    private async addRecommendationData(activity: ActivityDS, widgetBuilderParams: IFitnessLogBuilderParams): Promise<void> {
        try {
            this.items.push({
                title: activity.activityName,
                status: activity.status,
                activityType: activity.activityType,
                action: undefined
            })
        } catch (e) {
            return
        }
    }

    private async addCultPTSessionData(activity: ActivityDS, widgetBuilderParams: IFitnessLogBuilderParams): Promise<void> {
        const tz = widgetBuilderParams.userContext.userProfile.timezone
        const bookingId: number = Number(activity.clientActivityId.split("-")[0])
        const appointmentId: number = Number(activity.clientActivityId.split("-")[1])
        const bookingDetail: BookingDetail = await widgetBuilderParams.cultPTService.getBookingDetail(bookingId, appointmentId)
        const product: ConsultationProduct = <ConsultationProduct>await widgetBuilderParams.catalogueService.getProduct(bookingDetail.booking.productCode)
        const title: string = product.timelineTitle
        const ptTimelineActivity: TimelineActivity = {
            carefitActivity: "CONSULTATION",
            subCategory: bookingDetail.booking.subCategoryCode,
            consultationStatus: bookingDetail.consultationOrderResponse.consultationUserState,
            title: title,
            description: `With ${bookingDetail.consultationOrderResponse.doctor.name}`,
            timestamp: bookingDetail.consultationOrderResponse.startTime,
            bookingInfo: bookingDetail
        }
        const isMissed: boolean = !_.isEmpty(ptTimelineActivity.bookingInfo.consultationOrderResponse) && ptTimelineActivity.bookingInfo.consultationOrderResponse.status === "MISSED"
        const parsedDate = TimeUtil.parseDateFromEpoch(ptTimelineActivity.timestamp)
        const parsedDateMoment = TimeUtil.getMomentForDate(parsedDate, tz)
        const startTime = TimeUtil.getTimeIn12HRFormat(TimeUtil.formatDateInTimeZone(tz, parsedDate), parsedDateMoment.hours(), parsedDateMoment.minutes(), false, tz)
        const action: Action = await ActionUtil.getCareActivityAction(widgetBuilderParams.userContext, ptTimelineActivity, widgetBuilderParams.cultPTService, widgetBuilderParams.logger)
        this.items.push({
            title: title,
            calloutText: startTime,
            action: action,
            status: TimelineUtil.getCareStatus(ptTimelineActivity, isMissed),
            activityType: "CONSULTATION_PT"
        })
    }

    private generateTitleForSelfWorkoutActivity(selfWorkout: FitnessLoggableItem, activity: ActivityDS): string {
        let title: string = selfWorkout.name
        const workoutMetrics: WorkoutMetric[] = _.get(activity, "workout.metrics")
        if (!_.isArray(workoutMetrics)) return title
        const timeMetric: WorkoutMetric = workoutMetrics.find((metric) => {
            return metric.name === "Time"
        })
        if (!_.isNil(timeMetric)) {
            const timeStr: string = ActivityLoggingUtil.stringifyTimeWorkoutMetric(timeMetric)
            if (!_.isEmpty(timeStr)) {
                title += " - " + timeStr
            }
        }
        return title
    }
}
