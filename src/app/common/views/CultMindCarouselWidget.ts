import * as _ from "lodash"
import { Action, ActionList, ManageOptionPayload } from "./WidgetView"
import IProductBusiness from "../../product/IProductBusiness"
import { FoodProduct as Product } from "@curefit/eat-common"
import { ActivityState } from "@curefit/logging-common"
import { ActivityType } from "@curefit/product-common"
import { CustomerIssueType } from "@curefit/issue-common"
import { ProductType } from "@curefit/product-common"
import { TimeUtil } from "@curefit/util-common"
import { CarouselWidget, ImageCardCarouselView } from "./CarouselWidget"
import { CultBooking } from "@curefit/cult-common"
import { ActionUtil } from "../../util/ActionUtil"
import { ActionUtil as FoodUtil } from "@curefit/base-utils"
import CultUtil, { RUNNING_EVENT_WORKOUT_ID } from "../../util/CultUtil"
import { UserContext } from "@curefit/userinfo-common"
import { Logger } from "@curefit/base"
import { ITodoActivityWidget } from "./TodoActivityWidget"
import TimelineUtil from "../../util/TimelineUtil"
import { User } from "@curefit/user-common"
import AppUtil from "../../util/AppUtil"
import { UrlPathBuilder } from "@curefit/product-common"
import { MealSlot, MenuType } from "@curefit/eat-common"
import { FoodBooking } from "@curefit/alfred-client"

export interface ICultMindCarouselWidgetParams {
    booking: CultBooking,
    productBusiness: IProductBusiness,
    issuesMap: Map<string, CustomerIssueType[]>
    userContext: UserContext,
    productType: ProductType,
    logger: Logger,
    subUserDetail?: User,
    cafeFoodDetails?: FoodBooking,
    productMap?: { [productId: string]: Product }
    kioskId?: string,
    isCafe?: boolean,
}

export class CultMindCarouselWidget extends CarouselWidget implements ITodoActivityWidget {
    timestamp: number
    status: ActivityState
    activityType: ActivityType
    bookingNumber?: string
    constructor() {
        super()
        this.widgetType = "TODO_ACTIVITY_WIDGET"
    }


    async buildView(cultMindWidgetParams: ICultMindCarouselWidgetParams): Promise<CultMindCarouselWidget> {
        const tz = cultMindWidgetParams.userContext.userProfile.timezone
        const booking: CultBooking = cultMindWidgetParams.booking
        try {
            // Assign title
            const cultUnderlyingClass = booking.CultClass
            this.title = CultUtil.pulsifyClassName(cultUnderlyingClass, cultMindWidgetParams.userContext) + " at " + booking.Center.name
            if (cultMindWidgetParams.subUserDetail) {
                this.title = this.title + " for " + cultMindWidgetParams.subUserDetail.firstName + " " +
                    cultMindWidgetParams.subUserDetail.lastName
            }
            // Assign text items (Time of class)
            const hour: string = TimeUtil.getMomentForDateString(booking.Class.startTime, tz, "HH:mm:ss").format("HH")
            const minute: string = TimeUtil.getMomentForDateString(booking.Class.startTime, tz, "HH:mm:ss").format("mm")
            const startTime: string = TimeUtil.getTimeIn12HRFormat(TimeUtil.formatDateInTimeZone(tz, new Date()), +hour, +minute, true, tz)
            this.leftCalloutText = startTime

            // Assign right callout for waitlist class
            this.rightCalloutView = this.getRightCalloutView(booking, cultMindWidgetParams.userContext)

            // text will be inactive for waitlisted booking
            this.titleStyle = (booking.wlBookingNumber || booking.label === "Dropped out") && "INACTIVE"

            // Assign timestamp
            this.timestamp = TimeUtil.getDate(booking.Class.date, parseInt(hour), parseInt(minute), tz).getTime()

            // Assign activity type
            this.activityType = "CULT_CLASS"
            this.bookingNumber = booking.bookingNumber
            // Create the cult class card
            const classCard: ImageCardCarouselView = await this.getCardForClassBooking(cultMindWidgetParams)
            this.cards.push(classCard)

            // Assign status
            this.status = TimelineUtil.getCultStatus(booking)
            if (this.isCultScoreLogActionPending(classCard.quickActions)) {
                // Overwrite status to TODO till cult score is not logged
                this.status = "TODO"
            }
            return this
        } catch (e) {
            const loggingParams = {
                id: booking.id,
                orderId: booking.orderID,
                bookingNumber: booking.bookingNumber || booking.wlBookingNumber,
                membershipID: booking.membershipID
            }
            cultMindWidgetParams.logger.error("Cult/Mind class widget building failed for booking :: " + JSON.stringify(loggingParams) + " User ID :: " + cultMindWidgetParams.userContext.userProfile.userId + "stack " + e.stack)
            return undefined
        }
    }

    private async getCardForClassBooking(cultMindWidgetParams: ICultMindCarouselWidgetParams): Promise<ImageCardCarouselView> {
        const booking: CultBooking = cultMindWidgetParams.booking
        const isCafe: boolean = cultMindWidgetParams.isCafe
        const cafeFoodDetails: FoodBooking = cultMindWidgetParams.cafeFoodDetails
        const classCard: ImageCardCarouselView = {
            viewType: "BORDERLESS",
            styleType: "WIDE",
            title: "",
            images: [],
            isCafe: false,
            imageActions: [],
            action: {
                actionType: "NAVIGATION",
                url: ""
            },
            quickActions: []
        }
        // sign out pin for junior bookings
        if (!_.isEmpty(booking.info) && !_.isNil(booking.info.signOutOTP)) {
            classCard.imageOverlayView = {
                orientation: "BOTTOM_RIGHT",
                text: `PIN: ${booking.info.signOutOTP}`,
                theme: "DARK"
            }
        }

        // Generate image URL
        const document = _.find(booking.Class.Workout.documents, document => {
            return document.tagID === 11
        })
        const imageUrl: string = document ? "/" + document.URL : undefined
        if (!_.isNil(imageUrl)) {
            classCard.images.push(imageUrl)
        }

        // Generate action
        // action for waitlist rejected class card
        if (booking.state === "REJECTED") {
            classCard.action = {
                actionType: "SHOW_ALERT_MODAL",
                meta: {
                    title: "",
                    subTitle: "Waitlist was not confirmed due to unavailability of open slots.",
                    actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Okay" }]
                }
            }
        } else if (booking.label === "Dropped out") {
            classCard.action = undefined
        } else {
            classCard.action.url = ActionUtil.getCultMindClassUrl(cultMindWidgetParams.productType, booking.bookingNumber || booking.wlBookingNumber)
        }
        const isDroppedOutBooking = booking.label === "Dropped out"
        // no quick action for waitlist rejected class
        if (booking.state !== "REJECTED" && !isDroppedOutBooking) {
            // Generate quick actions
            const possibleActions: Action[] = await this.getPossibleActionsForClass(cultMindWidgetParams)
            const primaryAction: Action = possibleActions[0]
            const primaryActionList: ActionList = _.isNil(primaryAction) ? undefined : _.assign({ actions: [] }, primaryAction)
            const secondaryActions: Action[] = possibleActions.slice(1)
            const secondaryActionList: ActionList = _.isEmpty(secondaryActions) ? undefined : { actionType: "ACTION_LIST", actions: secondaryActions, icon: "MANAGE" }
            if (!_.isNil(primaryActionList)) {
                classCard.quickActions.push(primaryActionList)
            }
            if (!_.isNil(secondaryActionList)) {
                classCard.quickActions.push(secondaryActionList)
            }
        }
        if (isCafe) {
            classCard.isCafe = isCafe
            classCard.firstImageWidthPercentage = 0.6
            classCard.imageActions.push({
                url: imageUrl,
                action: classCard.action
            })
            if (_.isNil(cafeFoodDetails)) {
                this.getCafeClassCard(classCard, cultMindWidgetParams)
            }
            else {
                await this.getCafeFoodWidget(classCard, cultMindWidgetParams)
            }
        }
        return classCard
    }

    private async getPossibleActionsForClass(cultMindWidgetParams: ICultMindCarouselWidgetParams): Promise<Action[]> {
        const possibleActions: Action[] = []
        const booking: CultBooking = cultMindWidgetParams.booking
        const cafeFoodDetails = cultMindWidgetParams.cafeFoodDetails
        const isRunningEvent = booking.Class.Workout.id === RUNNING_EVENT_WORKOUT_ID
        const isUpcomingOrOngoingClass = booking.label === "Upcoming" || booking.label === "Ongoing"
        const showNavigationAction = !isRunningEvent && isUpcomingOrOngoingClass
        const timezone = cultMindWidgetParams.userContext.userProfile.timezone
        if (showNavigationAction) {
            possibleActions.push(ActionUtil.centerNavigationAction(booking.Center.placeUrl))
        }
        let cultManageOptions
        if (booking.wlBookingNumber) {
            cultManageOptions = await cultMindWidgetParams.productBusiness.getCultWaitlistManageOptions(cultMindWidgetParams.userContext,
                cultMindWidgetParams.productType, booking)
        } else {
            cultManageOptions = await cultMindWidgetParams.productBusiness.getCultManageOptions(cultMindWidgetParams.userContext,
                cultMindWidgetParams.productType, booking, cultMindWidgetParams.issuesMap, true, false)
        }
        const enabledOptions = cultManageOptions.manageOptions.options.filter((option: ManageOptionPayload) => {
            return option.isEnabled || option.showDisabled
        })
        if (enabledOptions.length > 0) {

            const cancelClassAction = await ActionUtil.cancelClassAction(enabledOptions, cultMindWidgetParams.userContext)
            if (cancelClassAction) {
                possibleActions.push(cancelClassAction)
                if (!cancelClassAction.disabled && cafeFoodDetails && AppUtil.isCultCafeSupported(cultMindWidgetParams.userContext)) {
                    possibleActions.push({
                        ...cancelClassAction,
                        meta: { ...cancelClassAction.meta, fulfilmentId: cafeFoodDetails.fulfilmentId, date: TimeUtil.formatDateStringInTimeZoneDateFns(cafeFoodDetails.deliveryDate, timezone, "yyyy-MM-dd") },
                        title: "Cancel class + Meal"
                    })
                }
            }
            const dropoutAction = ActionUtil.classDropoutAction(enabledOptions)
            if (dropoutAction) {
                if (!booking.hasUserDroppedOutBefore) {
                    dropoutAction.actionType = "NAVIGATION"
                    const params = { ...dropoutAction.meta, fromDropoutAction: true, actionTitle: "Dropout" }
                    dropoutAction.url = "curefit://listpage?pageId=dropouthiw" + UrlPathBuilder.serializeAsQueryParams(params)
                }
                possibleActions.push(dropoutAction)
                if (!dropoutAction.disabled && cafeFoodDetails && AppUtil.isCultCafeSupported(cultMindWidgetParams.userContext)) {
                    possibleActions.push({
                        ...dropoutAction,
                        meta: { ...dropoutAction.meta, fulfilmentId: cafeFoodDetails.fulfilmentId, date: TimeUtil.formatDateStringInTimeZoneDateFns(cafeFoodDetails.deliveryDate, timezone, "yyyy-MM-dd") },
                        title: "Dropout + Cancel Meal"
                    })
                }
            }
            const cultScoreLogAction = ActionUtil.cultScoreLogAction(enabledOptions)
            if (cultScoreLogAction) {
                possibleActions.push(cultScoreLogAction)
            }
        }
        return possibleActions
    }

    private isCultScoreLogActionPending(actions: Action[]): boolean {
        const cultLogAction: Action = _.find(actions, (action) => {
            return action.actionType === "LOG_CULT_SCORE"
        })
        return _.isNil(cultLogAction) ? false : true
    }

    private getRightCalloutView(booking: CultBooking, userContext: UserContext): any {
        const isPulseEnabled = CultUtil.isClassAvailableForPulse(booking.CultClass, AppUtil.isCultPulseFeatureSupported(userContext))
        if (booking.label === "Dropped out") {
            return {
                borderColor: "#000000",
                textColor: "#000000",
                text: "Dropped out"
            }
        } else {
            if (isPulseEnabled && !_.isEmpty(booking.pulseDeviceName)) {
                return {
                    borderColor: "#000000",
                    textColor: "#000000",
                    text: CultUtil.pulsifyDeviceName(booking)
                }
            }
            if (booking.wlBookingNumber) {
                const text = booking.state === "PENDING" ? `Waitlisted ${booking.waitlistNumber}` : "Not Confirmed"
                const borderColor = booking.state === "PENDING" ? "#5bdbb6" : "#e05303"
                const textColor = booking.state === "PENDING" ? "#000000" : "#e05303"
                return {
                    text, borderColor, textColor
                }
            }
        }
    }
    private getCafeClassCard(classCard: ImageCardCarouselView, cultMindWidgetParams: ICultMindCarouselWidgetParams) {
        const booking: CultBooking = cultMindWidgetParams.booking
        const kioskId: string = cultMindWidgetParams.kioskId
        const tz = cultMindWidgetParams.userContext.userProfile.timezone
        classCard.subTitle = "Order a workout snack",
            classCard.imageActions.push({
                url: "/image/icons/cult/WorkoutSnack.png",
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtil.getEatClpUrl(kioskId, booking, tz, true),
                    title: "ADD A SNACK"
                }
            })
    }
    private async getCafeFoodWidget(classCard: ImageCardCarouselView, cultMindWidgetParams: ICultMindCarouselWidgetParams) {
        const cafeFoodDetails: FoodBooking = cultMindWidgetParams.cafeFoodDetails
        const productMap: { [productId: string]: Product } = cultMindWidgetParams.productMap
        const menuType: MenuType = "ALL"
        const seoParams = {
            productName: productMap[cafeFoodDetails.productId].title
        }
        const action: Action = {
            actionType: "NAVIGATION",
            url: (cafeFoodDetails.products.length === 1) ? FoodUtil.foodSingle(cafeFoodDetails.productId, cafeFoodDetails.deliveryDate, menuType, false, cafeFoodDetails.fulfilmentId, cultMindWidgetParams.userContext.sessionInfo.userAgent, seoParams) : FoodUtil.foodCart(cafeFoodDetails.fulfilmentId, <MealSlot>"ALL", cafeFoodDetails.deliveryDate)
        }
        const productQuantityMap: { [productId: string]: number } = {}
        for (const j in cafeFoodDetails.products) {
            if (_.isNil(productQuantityMap[cafeFoodDetails.products[j].productId])) {
                productQuantityMap[cafeFoodDetails.products[j].productId] = 0
            }
            productQuantityMap[cafeFoodDetails.products[j].productId] += 1
        }
        for (const productId in productMap) {
            const product: Product = productMap[productId]
            const url: string = UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, "FOOD", "THUMBNAIL", product.imageVersion)
            let quantity: number = !_.isNil(productQuantityMap[productId]) ? productQuantityMap[productId] : 0
            while (quantity > 0) {
                classCard.imageActions.push({ url, action })
                quantity--
            }
        }
    }
}
