import { Action, WidgetView } from "./WidgetView"
import { ProductType } from "@curefit/product-common"

export interface ProfileNameIconWidget extends WidgetView {
    widgetType: "PROFILE_NAME_ICON_WIDGET"
    name: string
    profilePictureUrl?: string
    action?: Action
    profileCompletion: number
    subText?: string
    fitclubEnabled?: boolean
    fitcash?: number
    fitcashAction?: Action
    fitclub?: {
        title: string
        subtitle: string
        action: Action
    }
}

export interface MembershipItem {
    productType?: ProductType
    packId?: string
    productId?: string
    image?: string
    title: string
    descriptionText?: string
    action?: Action
    ctaAction?: Action
    progress?: number
    topRightIcon?: string
    status?: string
    footer?: {
        text: string
        image: string
    }
    progressBar?: ProgressBar
    membershipState?: string
    membershipStateTextColor?: string
    cardAction?: Action
    bottomCtaAction?: Action
    iconDimensions?: IconDimensions
    subtitle?: string
}

export interface ProgressBar {
    leftText?: string
    total?: number
    completed?: number
    type?: string
    noPadding?: boolean
    isSplitView?: boolean
    progressBarColor?: string
    progressBarBackgroundColor?: string
    progress?: number
}

export interface IconDimensions {
    iconTopSpacing?: number,
    iconWidth?: number,
    iconHeight?: number,
}

export interface ProfileSummaryWidget extends WidgetView {
    widgetType: "PROFILE_SUMMARY_WIDGET"
    name: string
    profilePictureUrl?: string
    actions?: Action[]
    profileCompletion: number
    membershipItems: MembershipItem[]
}

export interface MembershipListWidget extends WidgetView {
    widgetType: "MEMBERSHIP_LIST_WIDGET"
    backgroundColor: string
    data: MembershipItem[]
}


export interface ScoreCardWidget extends WidgetView {
    widgetType: "SCORE_CARD_WIDGET"
    title: string
    score: number
    status: string
    subtitle: string
    action: Action
}

export interface HealthMetric {
    title: string
    value: number
    action: Action
}

export interface HealthMetricWidget extends WidgetView {
    widgetType: "HEALTH_METRIC_WIDGET"
    fitclubEnabled: boolean,
    metrics: HealthMetric[]
}

export interface SwitchSectionItem {
    type: string
    title: string,
    meta?: any
}

export interface AccordionSectionItem {
    title: string
    description?: string
    isExpandable?: boolean
    icon: string
    data?: AccordionSectionItem[] | SwitchSectionItem[] | (AccordionSectionItem | SwitchSectionItem)[]
    action?: Action
    type?: string,
    isExpanded?: boolean
    tag?: string
    subtitle?: string
}

export interface AccordionSectionList extends WidgetView {
    widgetType: "ACCORDION_SECTION_LIST",
    sections: AccordionSectionItem[]
}

export interface AccountNotLoginWidget extends WidgetView {
    widgetType: "ACCOUNT_NOT_LOGIN_WIDGET",
    title: string,
    action: Action
}
