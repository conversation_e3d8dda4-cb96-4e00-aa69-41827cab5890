import * as _ from "lodash"
import { WidgetView } from "./WidgetView"
import { TimeUtil } from "@curefit/util-common"
import { ActivityState } from "@curefit/logging-common"
import { ActivityType } from "@curefit/product-common"
import { ACTIVITY_PRIORITY_MAP } from "../../user/ITimelineBusiness"
import { ActivityTypeDS } from "@curefit/logging-common"
import { UserContext } from "@curefit/userinfo-common"

export interface ITodoActivityWidget extends WidgetView {
    timestamp: number
    status: ActivityState
    activityType: ActivityType
    missedMeta?: IMissedMeta // missedMeta is used to mark a particular activity as missed. This object is returned by client while marking an activity as missed.
    widgetHash?: string
}

export interface IMissedMeta {
    recommendationId: string
    description: string
    activityType: ActivityTypeDS
    [key: string]: any
}

export const HIGH_PRIORITY_TIMESTAMP: number = Number.MAX_SAFE_INTEGER

interface ITodoActivityWidgetWrapper {
    widget: ITodoActivityWidget
    modifiedTimestamp: number
}

export function sortTodoWidgets(userContext: UserContext, todoWidgets: Array<ITodoActivityWidget>): void {
    const tz = userContext.userProfile.timezone
    const wrapperWidgets: ITodoActivityWidgetWrapper[] = []
    const currentEpochTime: number = TimeUtil.getDateNow(tz).getTime() - (30 * TimeUtil.TIME_IN_MILLISECONDS.SECOND) // 30 seconds buffer
    todoWidgets.forEach((todoWidget) => {
        if (_.isNil(todoWidget.timestamp) || todoWidget.timestamp === HIGH_PRIORITY_TIMESTAMP) {
            wrapperWidgets.push({
                widget: todoWidget,
                modifiedTimestamp: todoWidget.timestamp
            })
        } else {
            const modifiedTimestamp: number = todoWidget.timestamp - currentEpochTime
            wrapperWidgets.push({
                widget: todoWidget,
                modifiedTimestamp: modifiedTimestamp
            })
        }
    })
    wrapperWidgets.sort(compareTodoWidgets)
    todoWidgets.splice(0, todoWidgets.length)
    wrapperWidgets.forEach((wrappedWidget) => {
        todoWidgets.push(wrappedWidget.widget)
    })
}

function compareTodoWidgets(w1: ITodoActivityWidgetWrapper, w2: ITodoActivityWidgetWrapper): number {
    if (_.isNil(w1.modifiedTimestamp) && _.isNil(w2.modifiedTimestamp)) return compareWidgetsWithNoTimeData(w1, w2)
    if (_.isNil(w1.modifiedTimestamp)) return 1
    if (_.isNil(w2.modifiedTimestamp)) return -1

    // HIGH_PRIORITY_TIMESTAMP is assigned as a modifiedTimestamp to special widgets which always will have higher priority over other widgets
    if (w1.modifiedTimestamp === HIGH_PRIORITY_TIMESTAMP && w2.modifiedTimestamp === HIGH_PRIORITY_TIMESTAMP) return compareWidgetsWithNoTimeData(w1, w2)
    if (w1.modifiedTimestamp === HIGH_PRIORITY_TIMESTAMP) return -1
    if (w2.modifiedTimestamp === HIGH_PRIORITY_TIMESTAMP) return 1

    if (w1.modifiedTimestamp >= 0 && w2.modifiedTimestamp >= 0) {
        // Both activities are in future
        return w1.modifiedTimestamp <= w2.modifiedTimestamp ? -1 : 1 // Give preference to the closer activity from current time
    } else if (w1.modifiedTimestamp < 0 && w2.modifiedTimestamp < 0) {
        // Both activities in the past
        return w1.modifiedTimestamp <= w2.modifiedTimestamp ? 1 : -1 // Give preference to the closer activity from current time
    } else if (w1.modifiedTimestamp < 0) {
        return 1 // Case where w1 is in past and w2 is in future. Give preference to w2
    } else { // (w2Time < 0)
        return -1 // Case where w1 is in future and w2 is in past. Give preference to w1
    }
}

function compareWidgetsWithNoTimeData(w1: ITodoActivityWidgetWrapper, w2: ITodoActivityWidgetWrapper): number {
    const priority1: number = ACTIVITY_PRIORITY_MAP.get(w1.widget.activityType)
    const priority2: number = ACTIVITY_PRIORITY_MAP.get(w2.widget.activityType)

    if (!_.isNumber(priority1)) return 1
    if (!_.isNumber(priority2)) return -1

    if (priority1 === priority2) {
        return w1.widget.widgetHash <= w2.widget.widgetHash ? -1 : 1
    }

    return priority1 < priority2 ? -1 : 1
}
