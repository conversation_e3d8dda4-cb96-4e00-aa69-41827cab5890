import { WidgetType, WidgetView } from "./WidgetView"
import { CultClass, CultWorkout } from "@curefit/cult-common"
import { IVideo, IWODDetail } from "../../cult/WodViewBuilder"
import { PageWidgetTitle } from "../../page/Page"
import * as _ from "lodash"
import { UserContext } from "@curefit/userinfo-common"
import { TimeUtil } from "@curefit/util-common"
import { WodSection } from "./CultWodWidget"
import { HourMin } from "@curefit/base-common"

export class LiveWodWidget implements WidgetView {
    public widgetType: WidgetType = "CULT_WOD_VIDEO_WIDGET"
    public header?: PageWidgetTitle
    public sections: WodSection[] = [
        {
            title: "Workout of the Day",
            type: "WOD_VIDEO",
            data: []
        }
    ]
    public showDivider?: boolean = false
    constructor(wods: IWODDetail, userContext: UserContext, duration: number, liveCalorieAttributes?: any, showDivider?: boolean) {
        if (showDivider !== undefined) {
            this.showDivider = showDivider
        }
        if (!_.isEmpty(wods.preview)) {
            const tz = userContext.userProfile.timezone
            const durationHourMin: HourMin = TimeUtil.convertDurationSecondsToHourMin(duration / 1000)
            let formattedTimeString: string = durationHourMin.min > 0 ? durationHourMin.min + " min" : ""
            if (durationHourMin.hour > 0) {
                formattedTimeString = durationHourMin.hour + " hr " + formattedTimeString
            }

            const caloriesInfo = this.getCaloriesInfo(liveCalorieAttributes)
            wods.preview.title = "Workout Focus:"
            wods.preview.subTitle += `\n${formattedTimeString}`
            if (caloriesInfo) {
                wods.preview.subTitle += ` \u2022 ${caloriesInfo} kcal`
            }
            this.sections.push({
                title: "PREVIEW",
                type: "WODITEM",
                data: [wods.preview]
            })
        }
        if (!_.isEmpty(wods.movements)) {
            this.sections.push({
                title: "MOVEMENTS",
                type: "WODITEM",
                data: wods.movements
            })
        }
    }

    getCaloriesInfo(calorieAttributes: any) {
        if (!_.isEmpty(calorieAttributes)) {
            if (calorieAttributes.minCaloriesBurnt && calorieAttributes.maxCaloriesBurnt) {
                return calorieAttributes.minCaloriesBurnt + "-" + calorieAttributes.maxCaloriesBurnt
            } else if (calorieAttributes.averageCaloriesBurnt) {
                return calorieAttributes.averageCaloriesBurnt
            }
        }
    }
}
