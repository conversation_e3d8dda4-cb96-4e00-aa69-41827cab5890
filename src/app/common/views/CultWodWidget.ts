import { WidgetType, WidgetView } from "./WidgetView"
import { CultClass, CultWorkout } from "@curefit/cult-common"
import { IVideo, IWODDetail } from "../../cult/WodViewBuilder"
import { PageWidgetTitle } from "../../page/Page"
import * as _ from "lodash"
import { UserContext } from "@curefit/userinfo-common"
import { TimeUtil } from "@curefit/util-common"
import CultUtil from "../../util/CultUtil"

export type WodSectionType = "WODITEM" | "WOD_VIDEO"
export interface WodSection {
    title: string
    type: WodSectionType
    data: IVideo[]
}

export class CultWodWidget implements WidgetView {
    public widgetType: WidgetType = "CULT_WOD_VIDEO_WIDGET"
    public header?: PageWidgetTitle
    public sections: WodSection[] = [
        {
            title: "Workout of the Day",
            type: "WOD_VIDEO",
            data: []
        }
    ]
    public showDivider?: boolean = false
    public isWodPresent: boolean = false
    constructor(wods: IWODDetail, cultClass: CultClass, userContext: UserContext, showDivider?: boolean) {
        if (showDivider !== undefined) {
            this.showDivider = showDivider
        }
        if (!_.isEmpty(wods.preview)) {
            const tz = userContext.userProfile.timezone
            const endTime = TimeUtil.getMomentForDateString(cultClass.endTime, tz, "HH:mm:ss")
            const startTime = TimeUtil.getMomentForDateString(cultClass.startTime, tz, "HH:mm:ss")
            const classDurationInMins = Math.abs(endTime.diff(startTime, "minutes"))
            const caloriesInfo = CultUtil.getWorkoutCaloriesInfo(cultClass.Workout)
            wods.preview.title = "Workout Focus:"
            wods.preview.subTitle += `\n${classDurationInMins} min`
            if (caloriesInfo) {
                wods.preview.subTitle += ` \u2022 ${caloriesInfo}`
            }
            this.isWodPresent = true
            this.sections.push({
                title: "PREVIEW",
                type: "WODITEM",
                data: [wods.preview]
            })
        }
        if (!_.isEmpty(wods.movements)) {
            this.isWodPresent = true
            this.sections.push({
                title: "MOVEMENTS",
                type: "WODITEM",
                data: wods.movements
            })
        }
    }
}
