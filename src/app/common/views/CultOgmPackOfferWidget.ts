import { Action, OgmPackOfferWidget, WidgetType } from "./WidgetView"
import { OgmDetail } from "@curefit/cult-common"
import { ProductType } from "@curefit/product-common"

export class CultOgmPackOfferWidget implements OgmPackOfferWidget {
    widgetType: WidgetType = "OGM_PACK_OFFER_WIDGET"
    title: string
    subTitle: string
    icon: string
    action: Action

    constructor(ogmDetail: OgmDetail, productType: ProductType) {
        this.title = "Gym Exchange"
        this.subTitle = `Additional ${this.formatDays(ogmDetail.extraDays)} free on this pack`
        this.icon = "/image/icons/cult/ogm_pack_detail.png"
        this.action = {
            title: "Remove",
            actionType: "REMOVE_OGM",
            meta: {
                productType: productType,
                OGMId: ogmDetail.id
            }
        }
    }

    private formatDays(days: number): string {
        let freeText = ""
        if (days > 30) {
            const months = Math.floor(days / 30).toFixed(0)
            const remainingDays = days % 30
            freeText = `${months} months ${remainingDays} days`
        } else {
            freeText = `${days} days`
        }
        return freeText
    }
}
