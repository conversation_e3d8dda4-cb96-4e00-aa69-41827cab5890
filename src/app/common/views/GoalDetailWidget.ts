import * as _ from "lodash"
import { Action, WidgetType, WidgetView } from "./WidgetView"
import { GoalCardCalloutView } from "./MetricsWidget"
import { GoalAdherenceStatusType, GoalAdherenceStatusTypes, UserGoalDetail } from "@curefit/gmf-common"
import { pluralizeStringIfRequired, TimeUtil } from "@curefit/util-common"
import { IMetric } from "@curefit/metrics-common"
import { GoalUtil } from "../../goals/GoalUtil"
import { BookingDetail } from "@curefit/albus-client"
import { ActionUtil } from "@curefit/base-utils"
import { ADHERENCE_STATUS } from "../../goals/UserGoalBusiness"
import { UserContext } from "@curefit/userinfo-common"

export interface GoalDetailView {
  title: string
  subTitle: string
  seemore?: Action
  welcomeText?: string
}

export interface GoalAnalysisView {
  sectionTitle: string
  items: GoalSectionItem[]
}

export interface GoalSectionItem {
  title: string
  value?: string
  color?: string
  fontWeight?: string
  titleColor?: string
}
export interface ColorCodingView {
  color: string
  description: string
}

export interface GoalDetailCardView {
  header: string
  colors: string[]
  leftView?: GoalCardCalloutView
  rightView?: GoalCardCalloutView
  colorCodings: ColorCodingView[]
}

export interface IGoalDetailWidget extends WidgetView {
  goalDetail: GoalDetailView
  cardView: GoalDetailCardView
  goalAnalysisView: GoalAnalysisView
  welcomeText?: GoalAnalysisView
}

export class GoalDetailWidget implements IGoalDetailWidget {
  widgetType: WidgetType
  goalDetail: GoalDetailView
  cardView: GoalDetailCardView
  goalAnalysisView: GoalAnalysisView
  welcomeText: GoalAnalysisView

  constructor(userContext: UserContext, userGoalDetail: UserGoalDetail, metricDetail: IMetric, mpActiveBookings: BookingDetail[]) {
    this.widgetType = "GOAL_DETAIL_WIDGET"
    this.goalDetail = this.getGoalDetailView(userContext, userGoalDetail, mpActiveBookings)
    if (userGoalDetail.goalAdherenceStatus.overAllStatus === ADHERENCE_STATUS.EARLY) {
      this.welcomeText = this.getWelcomeText(userGoalDetail)
    }
    this.cardView = this.getGoalCardView(userGoalDetail, metricDetail)
    this.goalAnalysisView = this.getGoalAnalysisView(userGoalDetail)
  }

  private getGoalDetailView(userContext: UserContext, userGoalDetail: UserGoalDetail, mpActiveBookings: BookingDetail[]): GoalDetailView {
    const tz = userContext.userProfile.timezone
    const parsedStartDate: Date = TimeUtil.parseDateFromEpoch(userGoalDetail.userGoal.startEpoch)
    const goalStartTime = `${TimeUtil.formatDateInTimeZone(tz, parsedStartDate, "Do MMM")}`
    const parsedEndDate: Date = TimeUtil.parseDateFromEpoch(userGoalDetail.userGoal.endEpoch)
    const goalEndTime = `${TimeUtil.formatDateInTimeZone(tz, parsedEndDate, "Do MMM")}`
    let seeMoreAction: Action = null
    if (!_.isEmpty(mpActiveBookings)) {
      const activePackBooking = mpActiveBookings[0]
      seeMoreAction = {
        actionType: "NAVIGATION",
        title: "DETAILS",
        url: ActionUtil.carefitbundle(activePackBooking.bundleOrderResponse.productCode, activePackBooking.booking.subCategoryCode, activePackBooking.booking.id.toString())
      }
    }
    return {
      title: userGoalDetail.userGoal.goalName,
      subTitle: `${goalStartTime} - ${goalEndTime}`,
      seemore: seeMoreAction
    }
  }

  private getLikelihood(adherenceStatus: GoalAdherenceStatusType) {
    switch (adherenceStatus) {
      case "Great": return "High"
      case "Average": return "Medium"
      case "Poor": return "Low"
    }
  }

  private getColorCodings(adherenceStatus: GoalAdherenceStatusType): ColorCodingView[] {
    const goalAdherenceStatus = adherenceStatus === ADHERENCE_STATUS.EARLY ? GoalAdherenceStatusTypes : GoalAdherenceStatusTypes.slice(1)
    const colorCodings = _.map(
      goalAdherenceStatus,
      (adherenceStatusType: GoalAdherenceStatusType) => {
        let value: string = adherenceStatusType
        if (adherenceStatusType === ADHERENCE_STATUS.EARLY) {
          value = "Too Early to \n Calculate"
        }
        return {
          color: GoalUtil.getGoalGradientColor(adherenceStatusType)[0],
          description: value
        }
      }
    )
    return colorCodings
  }

  private getGoalCardView(userGoalDetail: UserGoalDetail, metricDetail: IMetric): GoalDetailCardView {
    const targetMetric = _.maxBy(userGoalDetail.userGoal.goalMetricTargets, metricTarget => metricTarget.priority)
    const targetValue = GoalUtil.getGoalTargetDisplayValue(targetMetric, metricDetail)
    let currentValue
    let metricUnit
    if (_.isEmpty(userGoalDetail.userMetrics)) {
      currentValue = "-"
      metricUnit = ""
    }
    else {
      currentValue = userGoalDetail.userMetrics[0].values[0].displayValue
      metricUnit = userGoalDetail.userMetrics[0].unit
    }
    const goalCard: GoalDetailCardView = {
      header: `${targetMetric.metricName} ${metricUnit}`,
      colors: GoalUtil.getGoalGradientColor(
        userGoalDetail.goalAdherenceStatus.overAllStatus
      ),
      leftView: {
        title: currentValue,
        subTitle: "Current"
      },
      rightView: {
        title: targetValue,
        subTitle: "Goal"
      },
      colorCodings: this.getColorCodings(userGoalDetail.goalAdherenceStatus.overAllStatus)
    }
    return goalCard
  }

  private getWelcomeText(userGoalDetail: UserGoalDetail) {
    const goalAnalysisView: GoalAnalysisView = {
      sectionTitle: "WELCOME!",
      items: [{
        title: "Visit this page to see all the information regarding your progress towards your goal.",
        value: null,
        titleColor: "#6D6D6D",
      }]
    }
    return goalAnalysisView
  }

  private getGoalAnalysisView(userGoalDetail: UserGoalDetail): GoalAnalysisView {
    let currentTrackSectionItems: GoalSectionItem[] = [
      {
        title: "Overall you are doing",
        value: userGoalDetail.goalAdherenceStatus.overAllStatus,
        color: GoalUtil.getGoalGradientColor(
          userGoalDetail.goalAdherenceStatus.overAllStatus
        )[0],
        fontWeight: "BOLD"
      },
      {
        title: userGoalDetail.goalAdherenceStatus.likelihood
      },
      {
        title: "You are following the plan",
        value: Math.round(userGoalDetail.goalAdherenceStatus.adherenceScore) + "%"
      },
      {
        title: "In the past 14 days, you have logged",
        value: `${userGoalDetail.goalAdherenceStatus.daysLoggedCount} ${pluralizeStringIfRequired("day", userGoalDetail.goalAdherenceStatus.daysLoggedCount)}`
      }
    ]
    if (ADHERENCE_STATUS.EARLY === userGoalDetail.goalAdherenceStatus.overAllStatus) {
      currentTrackSectionItems = [{
        title: "You need to log all your meals, workouts and medication (if prescribed) for at least 3 days to see your performance.",
        value: null,
        color: "#6D6D6D",
        fontWeight: "500",
        titleColor: "#6D6D6D"
      }]
    }
    const goalAnalysisView: GoalAnalysisView = {
      sectionTitle: "HOW AM I DOING",
      items: currentTrackSectionItems
    }
    return goalAnalysisView
  }
}
