import * as _ from "lodash"
import { Action } from "./WidgetView"
import { ActivityState } from "@curefit/logging-common"
import { ActivityType } from "@curefit/product-common"
import { BaseCarouselCardView, CarouselWidget, TextListCarouselView } from "./CarouselWidget"
import { UserContext } from "@curefit/userinfo-common"
import { ProductRecommendation, TodoPlanActivityView } from "@curefit/gmf-common"
import { IGMSWidgetBuilderParams } from "./GMSCarouselWidgetBuilder"
import { IMissedMeta, ITodoActivityWidget } from "./TodoActivityWidget"
import { TimeUtil } from "@curefit/util-common"

export class MedicineRecommendationCarouselWidget extends CarouselWidget implements ITodoActivityWidget {
    timestamp: number
    status: ActivityState
    activityType: ActivityType
    missedMeta: IMissedMeta
    constructor() {
        super()
        this.widgetType = "TODO_ACTIVITY_WIDGET"
    }

    async buildView(widgetBuilderParams: IGMSWidgetBuilderParams): Promise<MedicineRecommendationCarouselWidget> {
        const recommendedItem: TodoPlanActivityView = widgetBuilderParams.recommendedItem
        const userContext: UserContext = widgetBuilderParams.userContext
        try {
            // Assign title
            this.title = recommendedItem.name

            // Assign text items (Time of class)
            const recommendationTime: Date = new Date(recommendedItem.todoDate)
            this.leftCalloutText = TimeUtil.get12HRTimeFormat(recommendationTime, userContext.userProfile.timezone)
            this.timestamp = recommendationTime.getTime()

            // Assign status
            this.status = "TODO"

            // Assign activity type
            this.activityType = "CARE_RECOMMENDATION"

            // Create the care booking cards
            const cardPromises: Promise<BaseCarouselCardView>[] = []
            recommendedItem.recommendations.forEach((item: ProductRecommendation) => {
                cardPromises.push(this.getCardForRecommendation(item, widgetBuilderParams))
            })
            for (const cardPromise of cardPromises) {
                let carouselCard: BaseCarouselCardView
                try {
                    carouselCard = await cardPromise
                } catch (e) {
                    // Do nothing
                }
                if (!_.isNil(carouselCard)) {
                    this.cards.push(carouselCard)
                }
            }
            if (_.isEmpty(this.cards)) {
                return undefined
            }

            // Assign missedMeta
            const recommendationId: string = (<any>recommendedItem)._id // HACK -- FIX later when GMS fixes reco Id
            const productRecommendation: ProductRecommendation = recommendedItem.recommendations[0]
            const productId: string = !_.isNil(productRecommendation.productIds) && !_.isEmpty(productRecommendation.productIds) && productRecommendation.productIds[0]
            this.missedMeta = {
                recommendationId: recommendationId,
                activityType: "GMF_MEDICINE_RECOMMENDATION",
                medicineId: productId,
                medicineName: "TODO-DUMMY-NAME", // TODO -- Fix this
                description: recommendedItem.name,
                activityTime: this.timestamp
            }

            return this
        } catch (e) {
            const loggingParams = {
                prescriptionItemId: recommendedItem.prescriptionItemId,
                name: recommendedItem.name
            }
            widgetBuilderParams.logger.error("Care recommendation widget building failed for recommendations :: " + JSON.stringify(loggingParams) + " User ID :: " + userContext.userProfile.userId)
            return undefined
        }
    }

    private async getCardForRecommendation(recommendedItem: ProductRecommendation, widgetBuilderParams: IGMSWidgetBuilderParams): Promise<BaseCarouselCardView> {

        if (recommendedItem.catalogueProductType === "MEDICINE") {
            return this.getMedicineCard(recommendedItem, widgetBuilderParams)
        }

        return undefined
    }

    private async getMedicineCard(recommendedItem: ProductRecommendation, widgetBuilderParams: IGMSWidgetBuilderParams): Promise<TextListCarouselView> {
        const tz = widgetBuilderParams.userContext.userProfile.timezone
        const headerCard: TextListCarouselView = {
            viewType: "BORDERLESS",
            styleType: "WIDE",
            title: "",
            textList: [],
            action: {
                actionType: "NAVIGATION",
                url: ""
            },
            quickActions: []
        }

        const productId: string = !_.isNil(recommendedItem.productIds) && !_.isEmpty(recommendedItem.productIds) && recommendedItem.productIds[0]
        // Action URL -- nothing as of now

        const medicineTime: Date = new Date(widgetBuilderParams.recommendedItem.todoDate)
        const date: string = TimeUtil.formatDateInTimeZone(tz, medicineTime)
        const todayDate: string = TimeUtil.todaysDate(tz)

        // Quick Actions
        const logMedicineAction: Action = {
            actionType: "REST_API",
            meta: {
                method: "POST",
                url: "/user/log/activities/logMedicine",
                body: {
                    "medicineId": productId,
                    "recommendationId": (<any>widgetBuilderParams.recommendedItem)._id, // hack to get _id : TODO - remove this once GMS starts sending it in proper object
                    "intakeTime": new Date(widgetBuilderParams.recommendedItem.todoDate).getTime(),
                    "description": widgetBuilderParams.recommendedItem.name,
                    "name": "TODO-DUMMY-NAME" // TODO -- Fix this
                },
                successMessage: "Your Activity has been logged successfully"
            },
            title: "LOG",
            icon: "LOG",
            isEnabled: date <= todayDate
        }

        const recommendationTime: Date = new Date(widgetBuilderParams.recommendedItem.todoDate)
        const activityTime: number = recommendationTime.getTime()
        const skipMedicineAction: Action = {
            actionType: "REST_API",
            meta: {
                method: "POST",
                url: "/user/log/activities/markAsMissed",
                body: [
                    {
                        "activityType": "GMF_MEDICINE_RECOMMENDATION",
                        "recommendationId": (<any>widgetBuilderParams.recommendedItem)._id, // hack to get _id : TODO - remove this once GMS starts sending it in proper object
                        "description": widgetBuilderParams.recommendedItem.name,
                        "medicineName": "TODO-DUMMY-NAME", // TODO -- Fix this
                        "activityTime": activityTime
                    }],
                successMessage: "Activity marked as Skipped"
            },
            title: "SKIP",
            isEnabled: date <= todayDate
        }
        headerCard.quickActions.push(_.assign({ actions: [] }, logMedicineAction))
        headerCard.quickActions.push(_.assign({ actions: [] }, skipMedicineAction))

        return headerCard
    }
}
