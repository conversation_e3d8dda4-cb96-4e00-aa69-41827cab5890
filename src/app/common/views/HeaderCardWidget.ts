import { BaseWidget, IBaseWidget, IServiceInterfaces, UserContext } from "@curefit/vm-models"
import { Action } from "@curefit/apps-common"

export interface CardHeader {
    imageUrl?: string,
    isVideo?: boolean,
    action?: Action,
}

export class HeaderCardWidget extends BaseWidget {
    cardWidgets: BaseWidget[]
    header?: CardHeader

    constructor(widgets: BaseWidget[], header?: CardHeader) {
        super("HEADER_CARD_WIDGET")
        this.cardWidgets = widgets
        this.header = header
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }, shareData?: any): Promise<HeaderCardWidget> {
        return this
    }
}
