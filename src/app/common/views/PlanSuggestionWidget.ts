import * as _ from "lodash"
import { Action, ActionList, WidgetType, WidgetView } from "./WidgetView"
import { UrlPathBuilder } from "@curefit/product-common"
import { ActionUtil as EtherActionUtil, ActionUtilV1, SeoUrlParams } from "@curefit/base-utils"
import { FitnessPack } from "@curefit/cult-common"
import { FoodProduct as Product } from "@curefit/eat-common"
import { ProductType } from "@curefit/product-common"
import { ICatalogueService, ICatalogueServicePMS } from "@curefit/catalog-client"
import { DIYFitnessPackExtended, DIYMeditationPackExtended } from "@curefit/diy-common"
import {
    BookLCConsultationActionMeta,
    ProductRecommendation,
    UserGuidelineItem,
    UserGuidelineView,
    UserPlanActionWithMeta
} from "@curefit/gmf-common"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { ActionUtil } from "../../util/ActionUtil"
import CareUtil, { SUPPORTED_MY_CHAT } from "../../util/CareUtil"
import { IHealthfaceService } from "@curefit/albus-client"
import IssueBusiness from "../../crm/IssueBusiness"
import { UserContext } from "@curefit/userinfo-common"
import { IDIYFulfilmentService } from "@curefit/diy-client"
import { ConsultationProduct } from "@curefit/care-common"
import CatalogueServiceUtilities from "../../util/CatalogueServiceUtilities"

const SPECIAL_INSTRUCTIONS_TITLE: string = "Special Instructions"
const TESTS_TITLE: string = "Tests"
const MEDICATION_TITLE: string = "Medication"

export interface IPlanActionWidget extends WidgetView {
    actions: ActionList[]
}

export interface IPlanSuggestionWidget extends WidgetView {
    title: string
    packCountText: string
    planItems: PlanItemView[]
    packDetails: IPackRecommendationView[]
}

export interface IPackRecommendationView {
    image: string
    action: Action
}

export interface PlanItemView {
    title?: string
    data: string[]
}

export class PlanSuggestionWidget implements IPlanSuggestionWidget {
    widgetType: WidgetType = "SUGGESTED_PLAN_WIDGET"
    title: string
    packCountText: string
    planItems: PlanItemView[]
    packDetails: IPackRecommendationView[]

    constructor() {
        this.planItems = []
        this.packDetails = []
    }

    async buildView(userId: string, userGuideline: UserGuidelineView, catalogueServicePMS: ICatalogueServicePMS, diyService: IDIYFulfilmentService, userContext: UserContext) {
        if (userGuideline.category === "FITNESS") {
            this.updateFitnessPlan(userGuideline)
        } else if (userGuideline.category === "FOOD") {
            this.updateFoodPlan(userGuideline)
        } else if (userGuideline.category === "MIND") {
            this.updateMindPlan(userGuideline)
        }
        this.packDetails = await this.getPackRecommendationView(userId, userGuideline, catalogueServicePMS, diyService, userContext)
        if (this.packDetails.length > 0) {
            this.packCountText = "+ " + this.packDetails.length + " pack suggestions"
        }
    }

    async buildCareView(userContext: UserContext, careGuidelines: UserGuidelineView[], catalogueService: ICatalogueService) {
        this.title = "CARE"

        // Pre-fill different sections of Care Widget
        this.planItems.push(
            {
                title: MEDICATION_TITLE,
                data: []
            },
            {
                title: TESTS_TITLE,
                data: []
            },
            {
                title: SPECIAL_INSTRUCTIONS_TITLE,
                data: []
            }
        )

        // Add the different Components into the pre-filled sections
        careGuidelines.forEach((guideline: UserGuidelineView) => {
            if (guideline.category === "CONSULTATION") {
                this.updateConsultationPlan(guideline)
            } else if (guideline.category === "DIAGNOSTICS") {
                this.updateDiagnosticsPlan(guideline)
            } else if (guideline.category === "HABIT") {
                this.updateHabitPlan(guideline)
            } else if (guideline.category === "MEDICINE") {
                this.updateMedicinePlan(userContext, guideline)
            }
        })

        // Remove empty sections
        let arraySplicedFlag: boolean = true
        while (arraySplicedFlag) {
            const emptyIndex: number = _.findIndex(this.planItems, (planItem: PlanItemView) => {
                return _.isEmpty(planItem.data)
            })
            if (emptyIndex === -1) {
                arraySplicedFlag = false
            } else {
                this.planItems.splice(emptyIndex, 1)
            }
        }
    }

    private updateFitnessPlan(userGuideline: UserGuidelineView) {
        this.title = "FITNESS"
        userGuideline.guidelines.forEach((guideline: UserGuidelineItem) => {
            this.planItems.push({
                data: [this.buildTextItem(guideline)]
            })
        })
    }

    private updateFoodPlan(userGuideline: UserGuidelineView) {
        this.title = "NUTRITION"
        userGuideline.guidelines.forEach((guideline: UserGuidelineItem) => {
            this.planItems.push({
                data: [this.buildTextItem(guideline)]
            })
        })
    }

    private updateConsultationPlan(userGuideline: UserGuidelineView) {
        const consultationSectionIndex: number = this.getPlanItemIndexWithTitle(SPECIAL_INSTRUCTIONS_TITLE)

        userGuideline.guidelines.forEach((guideline: UserGuidelineItem) => {
            this.planItems[consultationSectionIndex].data.push(this.buildTextItem(guideline))
        })
    }

    private updateDiagnosticsPlan(userGuideline: UserGuidelineView) {
        const diagnosticsSectionIndex: number = this.getPlanItemIndexWithTitle(TESTS_TITLE)

        userGuideline.guidelines.forEach((guideline: UserGuidelineItem) => {
            this.planItems[diagnosticsSectionIndex].data.push(this.buildTextItem(guideline))
        })
    }

    private updateHabitPlan(userGuideline: UserGuidelineView) {
        const habitsSectionIndex: number = this.getPlanItemIndexWithTitle(SPECIAL_INSTRUCTIONS_TITLE)

        userGuideline.guidelines.forEach((guideline: UserGuidelineItem) => {
            this.planItems[habitsSectionIndex].data.push(this.buildTextItem(guideline))
        })
    }

    private updateMedicinePlan(userContext: UserContext, userGuideline: UserGuidelineView) {
        const medicineSectionIndex: number = this.getPlanItemIndexWithTitle(MEDICATION_TITLE)

        userGuideline.guidelines.forEach((guideline: UserGuidelineItem) => {
            let medicinePlanText: string = this.buildTextItem(guideline)
            const startEpoch: number = _.get(guideline, "dateInfo.startEpoch")
            const endEpoch: number = _.get(guideline, "dateInfo.endEpoch")
            let timeZone: Timezone = _.get(guideline, "dateInfo.timeZone")
            if (_.isUndefined(timeZone)) timeZone = userContext.userProfile.timezone
            let dateRangeText: string
            if (!_.isNil(startEpoch)) {
                const startDate: Date = TimeUtil.parseDateFromEpochWithTimezone(timeZone, startEpoch)
                dateRangeText = TimeUtil.formatDateInTimeZone(timeZone, startDate, "MMM Do")
            }
            if (!_.isNil(endEpoch)) {
                const endDate: Date = TimeUtil.parseDateFromEpochWithTimezone(timeZone, endEpoch)
                const endDateText = TimeUtil.formatDateInTimeZone(timeZone, endDate, "MMM Do")
                if (endDateText !== dateRangeText) {
                    dateRangeText += " - " + endDateText
                }
            }
            if (!_.isNil(dateRangeText)) {
                medicinePlanText += ", " + dateRangeText
            }
            this.planItems[medicineSectionIndex].data.push(medicinePlanText)
        })
    }

    private updateMindPlan(userGuideline: UserGuidelineView) {
        this.title = "MEDITATE"
        userGuideline.guidelines.forEach((guideline: UserGuidelineItem) => {
            this.planItems.push({
                data: [this.buildTextItem(guideline)]
            })
        })
    }

    private getPlanItemIndexWithTitle(title: string): number {
        if (_.isEmpty(this.planItems)) return -1

        const result: number = _.findIndex(this.planItems, (planItem: PlanItemView) => {
            return (!_.isNil(planItem.title) && planItem.title === title)
        })

        return result
    }

    protected buildTextItem(guideline: UserGuidelineItem): string {
        let result: string = guideline.text
        if (!_.isNil(guideline.subText)) {
            result = result + ", " + guideline.subText
        }
        return result
    }

    private async getPackRecommendationView(userId: string, userGuideline: UserGuidelineView, catalogueServicePMS: ICatalogueServicePMS, diyService: IDIYFulfilmentService, userContext: UserContext): Promise<IPackRecommendationView[]> {
        const packRecommendationPromises: { productId: string, productType: ProductType, productPromise: Promise<Product> }[] = []
        const packRecommendations: IPackRecommendationView[] = []
        if (!_.isEmpty(userGuideline.packRecommendations)) {
            userGuideline.packRecommendations.forEach((pack: ProductRecommendation) => {
                if (_.isEmpty(pack.productIds)) return
                packRecommendationPromises.push({
                    productId: pack.productIds[0],
                    productType: <ProductType>pack.catalogueProductType,
                    productPromise: this.getProductPromise(userId, pack.productIds[0], <ProductType>pack.catalogueProductType, catalogueServicePMS, diyService)
                })
            })
        }
        for (const packPromise of packRecommendationPromises) {
            const product: Product = await packPromise.productPromise
            if (_.isEmpty(product)) {
                continue
            }
            const seoParams: SeoUrlParams = {
              productName: product.title
            }
            let productUrl: string
            let imageUrl: string
            if (product.productType === "FITNESS") {
                productUrl = CatalogueServiceUtilities.getPackDetailsPageAction(product.productId)
                imageUrl = UrlPathBuilder.getPackImagePath(product.productId, packPromise.productType, "MAGAZINE", product.imageVersion, "APP")
            } else if (product.productType === "DIY_FITNESS_PACK") {
                productUrl = ActionUtilV1.diyPackProductPage(<DIYFitnessPackExtended>product, userContext.sessionInfo.userAgent)
                imageUrl = UrlPathBuilder.prefixSlash((<DIYFitnessPackExtended>product).imageDetails.magazineImage)
            } else if (product.productType === "DIY_MEDITATION_PACK") {
                productUrl = ActionUtilV1.diyPackProductPage(<DIYMeditationPackExtended>product, userContext.sessionInfo.userAgent)
                imageUrl = UrlPathBuilder.prefixSlash((<DIYMeditationPackExtended>product).imageDetails.magazineImage)
            }
            if (_.isNil(productUrl)) continue
            packRecommendations.push({
                image: imageUrl,
                action: {
                    actionType: "NAVIGATION",
                    url: productUrl
                }
            })
        }
        return packRecommendations
    }

    private async getProductPromise(userId: string, productId: string, productType: ProductType, catalogueServicePMS: ICatalogueServicePMS, diyService: IDIYFulfilmentService): Promise<Product> {
        if (productType === "DIY_FITNESS_PACK") {
            const products = await diyService.getDIYFitnessPacksForIds(userId, [productId])
            if (!_.isEmpty(products)) {
                return products[0]
            }
        } else if (productType === "DIY_MEDITATION_PACK") {
            const products = await diyService.getDIYMeditationPacksForIds(userId, [productId])
            if (!_.isEmpty(products)) {
                return products[0]
            }
        } else if (productType === "FITNESS") {
            const product = await catalogueServicePMS.getCatalogProduct("CULTPACK" + productId) // PMS::TODO Migrate
            return product
        }
        return undefined
    }
}

export class PlanActionWidget implements IPlanActionWidget {
    widgetType: WidgetType
    actions: ActionList[]

    constructor() {
        this.widgetType = "ACTION_CONTAINER_WIDGET"
        this.actions = []
    }

    async buildView(userContext: UserContext, planActionDetails: UserPlanActionWithMeta[], healthfaceService: IHealthfaceService, issueBusiness: IssueBusiness, catalogueService: ICatalogueService): Promise<void> {
        const possibleActions: Action[] = []
        const groupedActions: Action[] = []
        await Promise.all(_.map(planActionDetails, async (actionDetail: UserPlanActionWithMeta) => {
            if (actionDetail.actionType === "CANCEL_PLAN") {
                groupedActions.push(ActionUtil.getCancelPlanAction())
            } else if (actionDetail.actionType === "BOOK_LC_CONSULTATION" && actionDetail.actionMeta) {
                const actionMeta: BookLCConsultationActionMeta = <BookLCConsultationActionMeta>(actionDetail.actionMeta)
                const consultationProduct = <ConsultationProduct>await catalogueService.getProduct(actionMeta.productCode)
                const vertical = CareUtil.getVerticalForConsultation(consultationProduct?.doctorType)
                possibleActions.push({
                    url: EtherActionUtil.teleconsultationSingle(userContext, actionMeta.productCode, consultationProduct.urlPath, undefined, undefined, vertical),
                    actionType: "NAVIGATION",
                    icon: "CALL"
                })
            } else if (actionDetail.actionType === "VIEW_FAQ") {
                groupedActions.push({
                    actionType: "REPORT_ISSUE",
                    icon: "REPORT_ISSUE",
                    title: "FAQ",
                    meta: {
                        title: "FAQ",
                        issues: await issueBusiness.getAIPlanIssue(userContext)
                    }
                })
            } else if (actionDetail.actionType === "VIEW_PLAN_SOURCE_DETAIL") {
                // mp active booking must be there if this action is coming and should only be one
                const activePackBookings = (await healthfaceService.getActivePacksByBookingType(userContext.userProfile.userId, undefined, "CARE", "BUNDLE", "MP"))
                if (!_.isEmpty(activePackBookings)) {
                    const activePackBooking = activePackBookings[0]
                    possibleActions.push({
                        actionType: "NAVIGATION",
                        icon: "MANAGE",
                        url: EtherActionUtil.carefitbundle(activePackBooking.bundleOrderResponse.productCode, activePackBooking.booking.subCategoryCode, activePackBooking.booking.id.toString())
                    })
                } else {
                    const activePackBookings = (await healthfaceService.getActivePacksByBookingType(userContext.userProfile.userId, undefined, "CARE", "BUNDLE", "AI_MG"))
                    if (!_.isEmpty(activePackBookings)) {
                        const activePackBooking = activePackBookings[0]
                        possibleActions.push({
                            actionType: "NAVIGATION",
                            icon: "MANAGE",
                            url: EtherActionUtil.carefitbundle(activePackBooking.bundleOrderResponse.productCode, activePackBooking.booking.subCategoryCode, activePackBooking.booking.id.toString())
                        })
                    }
                }
            }

        }))

        if (userContext.sessionInfo.appVersion >= SUPPORTED_MY_CHAT) {
            possibleActions.push({
                actionType: "NAVIGATION",
                icon: "MESSAGE",
                url: `curefit://chathistory`
            })
        }

        possibleActions.forEach((action: Action) => {
            const actionList: ActionList = _.assign({ actions: [] }, action)
            this.actions.push(actionList)
        })

        if (!_.isEmpty(groupedActions)) {
            this.actions.push({
                actions: groupedActions,
                actionType: "ACTION_LIST",
                icon: "MANAGE",
                title: "more options"
            })
        }
    }
}
