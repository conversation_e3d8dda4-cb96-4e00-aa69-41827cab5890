import * as _ from "lodash"
import { WidgetType } from "./WidgetView"
import { UserGoal } from "@curefit/gmf-common"
import {
    GraphAggregationType,
    GraphHeader,
    GraphMetric,
    IGraphWidgetBuilderParams,
    IMetricsGraphWidget,
    LastLoggedInfoView,
    MetricsGraphView
} from "./IMetricsGraphWidget"
import { AggregationRange, IAverageMetricData, IMetric, IMetricData } from "@curefit/metrics-common"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { getDateSortedGraphData, getYAxisRange } from "../../util/GraphUtil"
import { GoalUtil } from "../../goals/GoalUtil"
import ActivityLoggingUtil from "../../util/ActivityLoggingUtil"
import { UserContext } from "@curefit/userinfo-common"

export interface IGoalMetricsWidgetBuilderParams
    extends IGraphWidgetBuilderParams {
    userGoal: UserGoal,
    userContext: UserContext
    metricId?: number,
}
export class GoalMetricsGraphWidget implements IMetricsGraphWidget {
    widgetType: WidgetType
    goalId: string
    header: GraphHeader
    graphs: MetricsGraphView[]
    graphType: GraphAggregationType
    graphMetrics: GraphMetric[]
    fromDate: string
    toDate: string
    range: Array<number>
    lastLoggedInfo: LastLoggedInfoView
    goalValue?: number
    calloutText?: string

    constructor(
        metricRangeData: IAverageMetricData,
        latestMetricData: IMetricData,
        widgetBuilderParams: IGoalMetricsWidgetBuilderParams
    ) {
        this.widgetType = "GOAL_METRICS_GRAPH_WIDGET"
        const options = [
            {
                displayText: AggregationRange.WEEK,
                value: AggregationRange.WEEK
            },
            {
                displayText: AggregationRange.MONTH,
                value: AggregationRange.MONTH
            },
            {
                displayText: "6-MONTHS",
                value: AggregationRange.SEMI_ANNUAL
            }
        ]
        const graphType: GraphAggregationType = {
            type: widgetBuilderParams.graphType,
            displayText: widgetBuilderParams.graphType === AggregationRange.SEMI_ANNUAL ? "6-MONTHS" : widgetBuilderParams.graphType.toUpperCase(),
            action: { actionType: "NAVIGATION", meta: { options: options } }
        }
        this.goalId = widgetBuilderParams.userGoal.goalId
        this.header = {
            title: "KEY METRICS",
            subTitle: "Key Metrics is a measure of your progression to goal",
            more: {
                title: "Learn more...",
                actionType: "SHOW_ALERT_MODAL",
                meta: {
                    title: "KEY METRICS",
                    subTitle: "Key Metrics is a measure of your progression to goal. You should log your metric regularly so that we can help you more proactively.",
                    actions: [
                        { actionType: "HIDE_ALERT_MODAL", title: "Ok" }
                    ]
                }
            },
            graphType: graphType
        }
        this.graphType = graphType
        this.graphMetrics = this.getGraphMetrics(
            metricRangeData,
            widgetBuilderParams
        )
        this.lastLoggedInfo = this.getLastLoggedInfoView(
            metricRangeData,
            latestMetricData,
            widgetBuilderParams.userContext
        )
        this.fromDate = widgetBuilderParams.fromDate
        this.toDate = widgetBuilderParams.toDate
        const currentMetricIndex = _.findIndex(
            widgetBuilderParams.userGoal.goalMetricTargets,
            metricTarget => metricTarget.metricId === metricRangeData.metric.id
        )
        if (currentMetricIndex > -1) {
            const latestTarget = _.maxBy(widgetBuilderParams.userGoal.goalMetricTargets[currentMetricIndex].targets, target => target.targetEpoch)
            this.goalValue = _.round(latestTarget.value, 2)
        }
        const graphData = this.getAllGraphData(metricRangeData, widgetBuilderParams, this.goalValue, widgetBuilderParams.userContext.userProfile.timezone)
        this.graphs = graphData.data
        this.range = graphData.range
        const primaryMetric = _.maxBy(
            widgetBuilderParams.userGoal.goalMetricTargets,
            metricTarget => metricTarget.priority
        )
        this.calloutText = `Key Metrics: ${primaryMetric.metricName}`
    }

    private getGraphMetrics(
        metricRangeData: IAverageMetricData,
        widgetBuilderParams: IGoalMetricsWidgetBuilderParams
    ): GraphMetric[] {
        const primaryMetric = _.maxBy(
            widgetBuilderParams.userGoal.goalMetricTargets,
            metricTarget => metricTarget.priority
        )
        const graphMetrics = _.map(
            widgetBuilderParams.userGoal.goalMetricTargets,
            metricTarget => {
                const isSelectedmetric =
                    widgetBuilderParams.metricId === metricTarget.metricId
                const graphMetric: GraphMetric = {
                    metricId: metricTarget.metricId,
                    displayText: metricTarget.metricName,
                    isPrimary: primaryMetric.metricId === metricTarget.metricId,
                    unit: isSelectedmetric ? metricRangeData.metric.unit : undefined,
                    selected: isSelectedmetric
                }
                return graphMetric
            }
        )
        return graphMetrics
    }

    private getLastLoggedInfoView(
        metricRangeData: IAverageMetricData,
        latestMetricData: IMetricData,
        userContext: UserContext
    ) {
        let text = ""
        const tz = userContext.userProfile.timezone
        if (!_.isEmpty(latestMetricData)) {
            const lastLoggedDate = TimeUtil.formatDateInTimeZone(
                tz,
                TimeUtil.parseDateFromEpoch(latestMetricData.metricDate),
                "Do MMM"
            )
            text = `Last updated on ${lastLoggedDate}`
        }
        const logAction = ActivityLoggingUtil.getActionforMetric(metricRangeData.metric, TimeUtil.todaysDate(tz), userContext)
        if (logAction) {
            logAction.title = "UPDATE NOW"
        }
        return { text, logAction }
    }

    private getAllGraphData(
        metricRangeData: IAverageMetricData,
        widgetBuilderParams: IGoalMetricsWidgetBuilderParams,
        goalValue: number, timezone: Timezone = TimeUtil.IST_TIMEZONE
    ): { data: MetricsGraphView[]; range: Array<number> } {
        const graphs: MetricsGraphView[] = []
        const graphData: Array<{
            key: string
            value: number
        }> = getDateSortedGraphData(
            metricRangeData.averageValue,
            widgetBuilderParams.graphType,
            widgetBuilderParams.fromDate,
            widgetBuilderParams.toDate,
            true, timezone
        )
        const isRangeValue = !_.isEmpty(metricRangeData.metric.rangeBuckets)
        const idealValueGraphData = this.getIdealValueGraphData(
            metricRangeData.metric,
            goalValue
        )
        const labelData: Array<{ key: string, value: any }> = _.map(graphData, item => {
            const labelItem = {
                key: item.key,
                value: isRangeValue ? GoalUtil.mapMetricValueToDisplayLabel(metricRangeData.metric.rangeBuckets, item.value) : _.round(item.value, 2)
            }
            return labelItem
        })
        graphs.push({ type: "SOLID_LINE", data: graphData })
        graphs.push({ type: "LABELS_ONLY", data: labelData })
        if (idealValueGraphData) {
            graphs.push({
                type: "IDEAL_VALUE_LINE",
                data: idealValueGraphData
            })
        }
        const yAxisRange = this.getYAxisRangeValues(
            metricRangeData,
            goalValue
        )
        return { data: graphs, range: yAxisRange }
    }

    private getIdealValueGraphData(
        metric: IMetric,
        goalValue: number
    ): Array<{ key: string; value: string }> {
        if (goalValue) {
            const graphData: Array<{ key: string; value: any }> = [
                { key: "0", value: "Goal" },
                { key: "1", value: GoalUtil.mapMetricValueToDisplayLabel(metric.rangeBuckets, goalValue) }
            ]
            return graphData
        }
    }

    private getYAxisRangeValues(
        metricRangeData: IAverageMetricData,
        goalValue: number
    ) {
        const rangeValues: { [id: string]: number } = metricRangeData.averageValue
        // handle for number values
        if (!_.isEmpty(metricRangeData.metric.rangeBuckets)) {
            _.map(
                metricRangeData.metric.rangeBuckets,
                (bucket, index) => {
                    rangeValues[index] = bucket.rangeMin
                }
            )
        }
        rangeValues["GoalValue"] = goalValue
        return getYAxisRange(rangeValues, metricRangeData.metric)
    }
}
