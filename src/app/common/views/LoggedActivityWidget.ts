import * as _ from "lodash"
import { Action, WidgetType, WidgetView } from "./WidgetView"
import { ActivityState, ActivityTypeDS } from "@curefit/logging-common"
import { LoggableActivitySlot } from "../../logging/ActivityLogging"

export const LoggableActivitySlotPriority: Map<LoggableActivitySlot, number> = new Map<LoggableActivitySlot, number>()
LoggableActivitySlotPriority.set("MORNING", 1)
LoggableActivitySlotPriority.set("BREAKFAST", 2)
LoggableActivitySlotPriority.set("LUNCH", 3)
LoggableActivitySlotPriority.set("AFTERNOON", 4)
LoggableActivitySlotPriority.set("SNACKS", 5)
LoggableActivitySlotPriority.set("EVENING", 6)
LoggableActivitySlotPriority.set("DINNER", 7)

export interface ILoggedActivityWidget extends WidgetView {
    title: string
    calloutText?: string
    items: ILoggedActivityWidgetItem[]
    priority: number
}

export interface ILoggedActivityWidgetItem {
    title: string
    calloutText?: string
    action: Action
    status: ActivityState
    activityType: ActivityTypeDS
    timestamp?: number
}

export abstract class LoggedActivityWidget implements ILoggedActivityWidget {
    widgetType: WidgetType = "LOGGED_ACTIVITY_WIDGET"
    title: string
    calloutText: string
    items: ILoggedActivityWidgetItem[]
    priority: number

    constructor() {
        this.items = []
        this.priority = 0
    }
}

export function sortLoggedActivityWidgets(loggedActivityWidgetList: Array<ILoggedActivityWidget>): void {
    loggedActivityWidgetList.sort(compareLoggedActivityWidgets)
}

function compareLoggedActivityWidgets(w1: ILoggedActivityWidget, w2: ILoggedActivityWidget): number {
    if (_.isNil(w1.priority)) return 1
    if (_.isNil(w2.priority)) return -1
    if (w1.priority <= w2.priority) return -1
    else return 1
}
