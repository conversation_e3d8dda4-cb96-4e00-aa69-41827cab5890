import { UserContext } from "@curefit/userinfo-common"
import { Logger } from "@curefit/base"
import { ICatalogueService } from "@curefit/catalog-client"
import { CultWorkout } from "@curefit/cult-common"
import { CultWorkoutRecommendationCarouselWidget } from "./CultWorkoutRecommendationCarouselWidget"
import { IActivityLoggingBusiness } from "../../logging/ActivityLoggingBusiness"
import { ITodoActivityWidget } from "./TodoActivityWidget"
import { MindWorkoutRecommendationCarouselWidget } from "./MindWorkoutRecommendationCarouselWidget"
import { MedicineRecommendationCarouselWidget } from "./MedicineRecommendationCarouselWidget"
import { TodoPlanActivityView } from "@curefit/gmf-common"
import { Meal } from "@curefit/food-common"
import { IDIYFulfilmentService } from "@curefit/diy-client"

export interface IGMSWidgetBuilderParams {
    recommendedItem: TodoPlanActivityView,
    userContext: UserContext,
    logger: Logger,
    catalogueService: ICatalogueService,
    activityLoggingBusiness: IActivityLoggingBusiness
    cultWorkoutPromiseMap: Promise<Map<string, CultWorkout>>,
    mindWorkoutPromiseMap: Promise<Map<string, CultWorkout>>,
    mealsPromiseList: Promise<Meal[]>,
    diyService: IDIYFulfilmentService
}

export class GMSCarouselWidgetBuilder {
    static async buildGMSWidget(widgetBuilderParams: IGMSWidgetBuilderParams): Promise<ITodoActivityWidget> {
        let gmsWidget: ITodoActivityWidget
        if (widgetBuilderParams.recommendedItem.category === "FITNESS") {
            gmsWidget = new CultWorkoutRecommendationCarouselWidget()
            gmsWidget = await (<CultWorkoutRecommendationCarouselWidget>gmsWidget).buildView(widgetBuilderParams)
        } else if (widgetBuilderParams.recommendedItem.category === "MIND") {
            gmsWidget = new MindWorkoutRecommendationCarouselWidget()
            gmsWidget = await (<MindWorkoutRecommendationCarouselWidget>gmsWidget).buildView(widgetBuilderParams)
        } else if (widgetBuilderParams.recommendedItem.category === "MEDICINE") {
            gmsWidget = new MedicineRecommendationCarouselWidget()
            gmsWidget = await (<MedicineRecommendationCarouselWidget>gmsWidget).buildView(widgetBuilderParams)
        }

        return gmsWidget
    }
}
