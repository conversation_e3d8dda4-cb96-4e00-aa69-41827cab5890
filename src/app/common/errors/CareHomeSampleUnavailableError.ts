import { ErrorFactory } from "@curefit/error-client"
import { UserContext } from "@curefit/userinfo-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import * as _ from "lodash"
import { ErrorCodes } from "../../error/ErrorCodes"

export class CareHomeSampleUnavailableError {
    throwError(errorFactory: ErrorFactory, debugMessage?: string, userContext?: UserContext) {
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "APP")
        if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
            throw errorFactory.withCode(ErrorCodes.HOME_SAMPLE_UNAVAILABLE_ERROR, 400).withDebugMessage(debugMessage).build()
        } else {
            throw errorFactory.withCode(ErrorCodes.HOME_SAMPLE_UNAVAILABLE_ERROR, 400).withDebugMessage(debugMessage).build()
        }
    }
}
