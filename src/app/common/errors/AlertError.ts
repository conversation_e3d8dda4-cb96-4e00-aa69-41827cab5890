import { ErrorBase } from "@curefit/error-client"
import { Action } from "../views/WidgetView"

export class AlertError extends ErrorBase {
    public title: string
    public subTitle: string
    public actions: Action[]
    public meta?: any

    constructor(title: string, subTitle: string, actions: Action[], error: ErrorBase, meta?: any) {
        super(error.name, error, error.code, error.statusCode)
        this.title = title
        this.subTitle = subTitle
        this.actions = actions
        this.meta = meta
    }
}
