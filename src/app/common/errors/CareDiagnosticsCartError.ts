import { ErrorFactory } from "@curefit/error-client"
import { UserContext } from "@curefit/userinfo-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import * as _ from "lodash"
import { ErrorCodes } from "../../error/ErrorCodes"

export class CareDiagnosticsCartError {
    throwError(errorFactory: ErrorFactory, userContext: UserContext, errorCode: string, debugMessage?: string) {
        let cyclopsErrorCode: string
        switch (errorCode) {
            case "ERR_DIAG_CART_DUPLICATE_ITEM" : {
            cyclopsErrorCode = ErrorCodes.ERR_DIAG_CART_DUPLICATE_ITEM
                break
            }
            case "ERR_DIAG_CART_GENDER_UNSUPPORTED" : {
                cyclopsErrorCode = ErrorCodes.ERR_DIAG_CART_GENDER_UNSUPPORTED
                break
            }
            case "ERR_DIAG_CART_NO_ACTIVE_CART" : {
                cyclopsErrorCode = ErrorCodes.ERR_DIAG_CART_NO_ACTIVE_CART
                break
            }
            case "ERR_DIAG_CART_NO_CART_ITEM" : {
                cyclopsErrorCode = ErrorCodes.ERR_DIAG_CART_NO_CART_ITEM
                break
            }
            default:
                cyclopsErrorCode = ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR

        }
        throw errorFactory.withCode(cyclopsErrorCode, 400).withDebugMessage(debugMessage).build()
    }
}
