import * as express from "express"
import { Container, inject } from "inversify"

import { controller, httpPost , httpGet } from "inversify-express-utils"
import AuthMiddleware from "../../auth/AuthMiddleware"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { REPORT_ISSUES_CLIENT_TYPES, ISupportArticleService, ISupportArticleFeedback, ISupportAticle, SupportArticleStatus } from "@curefit/report-issues-client"

import { ICaptchaBusiness } from "../../referral/CaptchaBusiness"
import MetricsUtil from "../../metrics/MetricsUtil"
import { BASE_TYPES, Logger } from "@curefit/base"
import { BlockingType } from "../../metrics/models/BlockingType"
import AuthUtil from "../../util/AuthUtil"
import { ErrorFactory } from "@curefit/error-client"
import { ErrorCodes } from "../../error/ErrorCodes"
import AppUtil from "../../util/AppUtil"
interface FAQResponse {
    title: string
    FAQ: FAQQuestion[]
}
interface FAQQuestion {
    question: string
    answer: string
}
function controllerFactory(kernel: Container) {
    @controller("/faq", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class FaqController {
        constructor(
            @inject(REPORT_ISSUES_CLIENT_TYPES.ISupportArticleService) private articleService: ISupportArticleService,
            @inject(CUREFIT_API_TYPES.CaptchaBusiness) private captchaBusiness: ICaptchaBusiness,
            @inject(CUREFIT_API_TYPES.MetricsUtil) private metricsUtil: MetricsUtil,
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory) {

        }
        @httpPost("/:faqId/feedback")
        async postFeedback(req: express.Request, res: express.Response): Promise<ISupportArticleFeedback> {
            const articleId: string = req.params.faqId
            const feedback = req.body.feedback
            const token = req.body.token
            const userAgent = AuthUtil.getUserAgent(req)

            if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
                const verifyCaptchaResp = await this.captchaBusiness.verifyCaptchaLogin(token, AppUtil.callSource(AuthUtil.getApiKeyFromReq(req)))
                if (!(verifyCaptchaResp.success && verifyCaptchaResp.score > 0.6)) {
                    this.metricsUtil.reportBlockedRequest(BlockingType.CAPTCHA_FAILURE)
                    this.logger.error(`AuthController:Failed to verify captcha Error: Failed to verify. Please try again`)
                    throw this.errorFactory.withCode(ErrorCodes.GENERIC_ERROR_WITH_MESSAGE, 400).withMeta({ message: verifyCaptchaResp["error-codes"][0] }).build()
                }
            }
            return this.articleService.postFeedbackForArticle(articleId, feedback)
        }

        @httpGet("/article/:articleId")
        async getArticlesFromIds(req: express.Request): Promise<ISupportAticle> {
            const articleId: string = req.params.articleId
            const result = await this.articleService.getArticlesFromIds([articleId])
            return result[0]
        }


        @httpGet("/folder/:folderId")
        async getArticlesForFolder(req: express.Request): Promise<FAQResponse> {
            const folderId: string = req.params.folderId
            const status = SupportArticleStatus.PUBLISHED
            const articles = await this.articleService.getAllArticles(undefined, undefined, undefined, folderId, status)

            const title = articles[0]?.folderName ?? "FAQ" // fallback if empty
            const links: FAQQuestion[] = articles.map((article) => ({
                question: article.title,
                answer: article.descriptionText,
            }))

            return { title, FAQ: links }
        }



    }
}

export default controllerFactory