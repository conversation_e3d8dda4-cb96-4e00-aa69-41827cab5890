export interface FirebasePayload {
  "collapse_key"?: string,
  "time_to_live": number,
  "notification"?: any,
  "data"?: any,
  "priority"?: any,
  "content-available": boolean,
  "registration_ids": string[]
}

interface IFirebaseService {
    sendNudge(token: string, payload: any, sendPayloadInsideData: boolean): Promise<{ success: boolean }>
    sendFirebaseMessage(firebasePayload: FirebasePayload): Promise<{ success: boolean }>
}

export default IFirebaseService
