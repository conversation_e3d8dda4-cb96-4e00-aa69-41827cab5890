import { inject, injectable } from "inversify"
import IFirebaseService, { FirebasePayload } from "./IFirebaseService"
import { Constants } from "@curefit/base-utils"
import { Logger, BASE_TYPES } from "@curefit/base"
import { FetchUtil } from "@curefit/base"
import { NotificationPayload } from "../../ugc/NotificationQueueListener"
import { CheckinNotificationState } from "../../gymfit/CheckinNotificationState"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import MetricsUtil from "../../metrics/MetricsUtil"

const fetch = require("node-fetch")

@injectable()
class FirebaseService implements IFirebaseService {
    constructor(@inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(BASE_TYPES.FetchUtil) protected fetchHelper: FetchUtil, @inject(CUREFIT_API_TYPES.MetricsUtil) private metricsUtil: MetricsUtil) {
    }

    // sendPayloadInsideData added for supporting lower versions after adding image support in android.
    sendNudge(token: string, payload: NotificationPayload, sendPayloadInsideData: boolean): Promise<{ success: boolean }> {
        const twentyFourHourInSeconds = 24 * 60 * 60
        payload.android_channel_id = payload.android_channel_id ? payload.android_channel_id : "curefit_notification_id"
        const firebasePayload: FirebasePayload = {
            "time_to_live": twentyFourHourInSeconds,
            "notification": payload,
            "data": sendPayloadInsideData === true ? payload : null,
            "content-available": true,
            "priority": "high",
            "registration_ids": [token]
        }
        // Passing the data as separate key and as well within  notification key  to cater to android and ios respectively.
        const finalPayload = sendPayloadInsideData === true ? firebasePayload : Object.assign(firebasePayload, payload)
        return this.sendFirebaseMessage(finalPayload)
    }

    sendFirebaseMessage(firebasePayload: FirebasePayload) {
        return fetch(Constants.getFireBaseUrl("/fcm/send"), this.fetchHelper.post(firebasePayload, { Authorization: Constants.getCheckinNotificationFireBaseApiKey() }))
            .then((response: any) => {
                return this.fetchHelper.parseResponse<any>(response).then((res) => {
                    this.logger.debug(`firebase response: ${res?.toString()}`)
                        let success = true
                        res.results?.forEach((item: any) => {if (item.error) {
                            success = false
                            this.logger.info("checkin-notification error - " + item.error)
                            this.metricsUtil.reportCheckinNotificationState(CheckinNotificationState.CHECKIN_FIREBASE_ERROR)
                        }})
                    return { "success": success }
                })
            }).catch((error: any) => {
                this.logger.error("Firebase error : " + error)
                // this.logger.error("Failed for payload " + JSON.stringify(firebasePayload))
                return { "success": false }
            })
    }
}

export default FirebaseService
