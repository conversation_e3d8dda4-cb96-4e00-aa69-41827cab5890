import { inject, injectable } from "inversify"

import { ForbiddenError } from "@curefit/base"

import { Session } from "@curefit/userinfo-common"
import { IMultiCrudKeyValue, REDIS_TYPES } from "@curefit/redis-utils"
import { ICrudKeyValue } from "@curefit/redis-utils"

import { ISSOBusiness, PersistedSession, SSO_TOKEN_EXPIRY_SECS, SSOToken } from "./ISSOBusiness"

const crypto = require("crypto")

@injectable()
class SSOBusiness implements ISSOBusiness {

    private crudDao: ICrudKeyValue

    constructor(@inject(REDIS_TYPES.MultiCrudKeyValueDao) private multiCrudKeyValueDao: IMultiCrudKeyValue) {
        // moved to CFAPI-CACHE from REDIS_CACHE
        this.crudDao = multiCrudKeyValueDao.getICrudKeyValue("CFAPI-CACHE")
    }

    private ssoToken(userId: string) {
        return `CFAPP:SSO:${userId}:${crypto.randomUUID()}`
    }

    async createSession(userId: string, session: Session, url: string): Promise<SSOToken> {
        const ssoToken: string = this.ssoToken(userId)
        const payload: string = JSON.stringify({url, session})
        return this.crudDao.createWithExpiry(ssoToken, payload, SSO_TOKEN_EXPIRY_SECS).then(function () {
            return {
                token: ssoToken
            }
        })
    }

    async resolveSession(ssoToken: string): Promise<PersistedSession> {
        return this.crudDao.read(ssoToken).then(function (payload) {
            if (payload) {
                return JSON.parse(payload)
            } else {
                throw new ForbiddenError("Incorrect token")
            }
        })
    }

    deleteSession(ssoToken: string): Promise<Boolean> {
        return this.crudDao.delete(ssoToken)
    }
}

export default SSOBusiness
