import { Session } from "@curefit/userinfo-common"

export const SSO_TOKEN_EXPIRY_SECS: number = 5 * 60

export interface SSOToken {
    token: string
}

export interface PersistedSession {
    session: Session
    url: string
}

export interface ISSOBusiness {

    createSession(userId: string, session: Session, url: string): Promise<SSOToken>

    resolveSession(ssoToken: string): Promise<PersistedSession>
}
