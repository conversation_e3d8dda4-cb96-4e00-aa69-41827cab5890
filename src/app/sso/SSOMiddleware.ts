import * as _ from "lodash"
import { inject, injectable } from "inversify"
import { NextFunction, Request, Response } from "express-serve-static-core"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { PersistedSession } from "./ISSOBusiness"
import SSOBusiness from "./SSOBusiness"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import AuthUtil from "../util/AuthUtil"

@injectable()
export class SSOMiddleware {

    constructor(@inject(BASE_TYPES.ILogger) private logger: ILogger, @inject(CUREFIT_API_TYPES.SSOBusiness) private ssoBusiness: SSOBusiness) {
        this.validateSsoSession = this.validateSsoSession.bind(this)
    }

    protected hasToken(req: Request, token: string): boolean {
        const tokenInHeader: string = req.headers[token] as string
        const tokenInCookies: string = req.signedCookies[token] || req.cookies[token] as string
        return !_.isNil(tokenInHeader) || !_.isNil(tokenInCookies)
    }

    protected hasAuth(req: Request): boolean {
        return this.hasToken(req, "at") || this.hasToken(req, "st")
    }

    public async validateSsoSession (req: Request, res: Response, next: NextFunction): Promise<void> {
        const ssoToken: string = req.headers["ssotoken"] as string
        const hasAuth: boolean = this.hasAuth(req)
        if (ssoToken) {
            try {
                const persistedSession: PersistedSession = await this.ssoBusiness.resolveSession(ssoToken)
                if (persistedSession) {
                    req.headers["st"] = persistedSession.session.st
                    req.headers["at"] = persistedSession.session.at
                    req.headers["osname"] = "browser"
                    AuthUtil.setCookies(persistedSession.session.st, persistedSession.session.at, undefined, req, res)
                    await this.ssoBusiness.deleteSession(ssoToken)
                } else {
                    res.status(401).send({ message: "invalid sso token" })
                }
            } catch (e) {
                next(e)
            }
        }
        next()
    }

}
