import * as express from "express"
import { controller, httpPost } from "inversify-express-utils"
import { inject, Container } from "inversify"

import AuthMiddleware from "../auth/AuthMiddleware"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { ISSOBusiness, SSOToken } from "./ISSOBusiness"
import { ErrorFactory, GenericError, HTTP_CODE } from "@curefit/error-client"
import { AuthErrorV2, ForbiddenError } from "@curefit/base"

export function ssoAuthControllerFactory(kernel: Container) {

    @controller("/sso",
        kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class SSOAuthenticatedController {

        constructor(
            @inject(CUREFIT_API_TYPES.SSOBusiness) private ssoBusiness: ISSOBusiness,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        ) {}

        @httpPost("/")
        async createSession(request: express.Request): Promise<SSOToken> {
            if (!request.session || !request.userContext.userProfile) {
                const genericErr: GenericError = new GenericError({ message: "invalid session" })
                genericErr.statusCode = 403
                throw genericErr
            }
            return this.ssoBusiness.createSession(request.userContext.userProfile.userId, request.session, request.body.url)
        }
    }

    return SSOAuthenticatedController
}

export default ssoAuthControllerFactory
