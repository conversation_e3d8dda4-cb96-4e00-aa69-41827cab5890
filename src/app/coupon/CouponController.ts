import CUREFIT_API_TYPES from "../../config/ioc/types"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import * as mustache from "mustache"
import AuthMiddleware from "../auth/AuthMiddleware"
import { Container, inject } from "inversify"
import { BASE_TYPES, Logger } from "@curefit/base"
import { CONSTELLO_CLIENT_TYPES, IConstelloService, IGiftCardService } from "@curefit/constello-client"
import {
    AllowedRedemptionSourcesConfig,
    CFVertical, ConsumedCoupon,
    Coupon,
    CouponConsumption,
    CouponConsumptionEligibility,
    CouponOffer,
    CouponType, ExplicitlyAppliedOffer,
    MultiCouponReward,
    Offer,
    PostActivation,
    RewardWrapper
} from "@curefit/constello-common"
import * as express from "express"
import { Session, UserContext } from "@curefit/userinfo-common"
import { ErrorFactory, HTTP_CODE, MOBILE_NUMBER_NOT_FOUND_ERROR_CODE } from "@curefit/error-client"
import { Action, ActionType } from "../common/views/WidgetView"
import { ICaptchaBusiness } from "../referral/CaptchaBusiness"
import * as _ from "lodash"
import { ActionUtil as EtherActionUtil } from "@curefit/base-utils"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { ErrorCodes } from "../error/ErrorCodes"
import { IUserService, USER_CLIENT_TYPES } from "@curefit/user-client"
import AppUtil, { AppFont, VOUCHER_CAPTCHA_APP_VERSION } from "../util/AppUtil"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import AuthUtil from "../util/AuthUtil"
import { BlockingType } from "../metrics/models/BlockingType"
import MetricsUtil from "../metrics/MetricsUtil"
import { LedgerEntry } from "@curefit/reward-common"
import {
    CultResponse,
    FitcashResponse,
    OfferEligibilityResponse,
    Response
} from "@curefit/reward-common/dist/src/FulfilmentResponse"
import { Membership } from "@curefit/membership-commons"
import { OFFER_SERVICE_CLIENT_TYPES, OfferServiceV3 } from "@curefit/offer-service-client"
import { AttributedText, VoucherUtil } from "./VoucherUtil"
import { CultAccess, LiveAccess } from "@curefit/constello-common/dist/src/Rewards"
import { ApiErrorCountMiddleware } from "../auth/ApiErrorCountMiddleware"
import { CouponSavingRequest, GearProductDetails } from "@curefit/offer-common/src/OfferV3"
import { CouponSavingsResponse, PriceComputeRequest } from "@curefit/offer-common"
import { OrderSource } from "@curefit/order-common"

export interface  CSCouponEligibilityResponse {
    rewardDescription: string,
    isEligible: boolean,
    refreshCart?: boolean
}

export interface ICouponResponse {
    type: string,
    header: {
        title: string,
        heroImage: string,
        tnc: {
            link: string,
            text: string
        },
        icon?: string
    },
    couponDetails: {
        title: string,
        subTitle: string,
        info: {
            title: string,
            items: {
                icon: string,
                subTitle: string | AttributedText[]
            }[]
        },
        cityModal?: {
            showCityModal: boolean,
            title: string,
            subtitle: string,
            isCityRestricted?: boolean
            allowedCities?: string[]
        },
        tnc: Action
    },
    action: Action,
    shouldShowTnc: boolean,
    footerNote: string
}

export function CouponControllerFactory(kernel: Container) {
    @controller("/fitnessPass",
        kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession,
        kernel.get<ApiErrorCountMiddleware>(CUREFIT_API_TYPES.ApiErrorCountMiddleware).checkErrorCountMiddleware)
    class CouponController {
        constructor(
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(CUREFIT_API_TYPES.MetricsUtil) private metricsUtil: MetricsUtil,
            @inject(CONSTELLO_CLIENT_TYPES.ConstelloService) private constelloService: IConstelloService,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
            @inject(CONSTELLO_CLIENT_TYPES.GiftCardService) private giftCardService: IGiftCardService,
            @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
            @inject(CUREFIT_API_TYPES.CaptchaBusiness) private captchaBusiness: ICaptchaBusiness,
            @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) private offerServiceV3: OfferServiceV3
        ) {
        }

        @httpGet("/page")
        async getFitnessPassPage(request: express.Request) {
            const userContext: UserContext = request.userContext as UserContext
            return this.constelloService.getFitnessPassPage().then(couponDetail => {
                return VoucherUtil._transformCouponPage(userContext, couponDetail)
            }).catch(error => {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_COUPON_ERROR, 400).withMeta({
                    message: error.message || "Failed to apply coupon",
                    errorMsg: error.message || "Failed to apply coupon"
                }).build()
            })
        }

        @httpGet("/eligibility") // app profile page is calling this...
        async getUserEligibilityForCode(request: express.Request): Promise<ICouponResponse> {
            const couponCode: string = request.query.couponCode
            const session: Session = request.session
            const userId: string = session.userId
            const voucherType: CouponType = request.query.voucherType as CouponType
            this.logger.info("Fetching coupon for User " + userId + " couponCode: " + couponCode )
            const userContext: UserContext = request.userContext as UserContext
            const user = await userContext.userPromise
            if (!user || !user.phone) {
                this.logger.error(`User ${userId} does not have a phone while trying to add eligibility`)
                throw this.errorFactory.withCode(ErrorCodes.INVALID_PHONE, MOBILE_NUMBER_NOT_FOUND_ERROR_CODE).build()
            }
            return this.constelloService.checkUserEligibilityForCouponCode(couponCode, userId, session.deviceId, AppUtil.callSource(AuthUtil.getApiKeyFromReq(request))).then((couponEligibility) => {
                if (!AppUtil.isCultSportWebApp(userContext) &&  couponEligibility?.coupon?.vertical === "CULT_GEAR") {
                    throw this.errorFactory.withCode(ErrorCodes.GENERIC_COUPON_ERROR, 400).withMeta({
                        message: "Unfortunately, Vouchers for cult.sport cannot be redeemed here.",
                        errorMsg: "Unfortunately, Vouchers for cult.sport cannot be redeemed here.",
                        buttonText: "RE ENTER CODE",
                        errorCode: couponEligibility.status.code,
                        couponCode: couponEligibility.code,
                        modalHeight: 250,
                    }).build()
                }
                else if (couponEligibility.status.code === "ELIGIBLE") {
                    const {code, coupon} = couponEligibility
                    const userAgent: UserAgent = AuthUtil.getUserAgent(request)
                    if (userAgent === "APP" && coupon.shouldShowTnc) {
                        throw VoucherUtil.throwTncActionError(this.errorFactory, couponCode)
                    }
                    const actionType: ActionType = "ACTIVATE_COUPON"
                    const action: Action = {
                        actionType: actionType,
                        meta: {
                            couponCode: code,
                            shouldShowTnc: coupon.shouldShowTnc,
                        },
                        title: "REDEEM NOW"
                    }
                    if (["tvos", "ios"].includes(userContext.sessionInfo?.osName?.toLowerCase()) && this.isLiveCoupon(coupon)) {
                        throw this.errorFactory.withCode(ErrorCodes.GENERIC_COUPON_ERROR, 400).withMeta({
                            message: "Unfortunately, Vouchers for cultpass HOME cannot be redeemed on iOS devices",
                            errorMsg: "Unfortunately, Vouchers for cultpass HOME cannot be redeemed on iOS devices",
                            buttonText: "RE ENTER CODE",
                            errorCode: "NOT_VALID_FOR_USER",
                            couponCode: couponEligibility.code,
                            modalHeight: 250,
                        }).build()
                    }
                    if (coupon.couponType === "CORPORATE" && AppUtil.isEnterpriseNewVoucherNotSupported(userContext)) {
                        throw this.errorFactory.withCode(ErrorCodes.GENERIC_COUPON_ERROR, 400).withMeta({
                            message: "Please update the app to redeem this voucher",
                            errorMsg: "Please update the app to redeem this voucher",
                            couponCode: couponEligibility.code,
                            modalHeight: 250,
                        })
                    }
                    return this._transformCouponResponse(code, coupon, "ELIGIBILITY", action, userContext.userProfile.timezone)
                } else if (couponEligibility.status.code === "USER_HAS_NO_PHONE") {
                    throw this.errorFactory.withCode(ErrorCodes.INVALID_PHONE, MOBILE_NUMBER_NOT_FOUND_ERROR_CODE).build()
                } else if (couponEligibility.status.code === "INVALID_REDEMPTION_SOURCE" && !_.isEmpty(this._convertCfSourceToUserSource(couponEligibility.coupon?.redemptionSourcesConfig))) {
                        throw this.errorFactory.withCode(ErrorCodes.COUPON_INVALID_SOURCE, 400)
                        .withMeta({validSource: this._convertCfSourceToUserSource(couponEligibility?.coupon?.redemptionSourcesConfig)}).build()
                } else {
                    throw this.errorFactory.withCode(ErrorCodes.GENERIC_COUPON_ERROR, 400).withMeta({
                        message: couponEligibility.status.message,
                        errorMsg: couponEligibility.status.message,
                        buttonText: "RE ENTER CODE",
                        errorCode: couponEligibility.status.code,
                        couponCode: couponEligibility.code,
                        modalHeight: 250,
                    }).build()
                }
            })
        }

        @httpPost("/csVoucherEligibility")
        async getUserEligibilityForCSVoucherCode(request: express.Request, res: express.Response): Promise<CSCouponEligibilityResponse> {
            const {couponCode, captchaResponse, productPriceDetails, orderTotalPayableWithoutDiscount, skuIdToProductDetails } = request.body
            this.logger.info("getUserEligibilityForCSVoucherCode, requestBody" + JSON.stringify(request.body, null, 4))
            const session: Session = request.session
            const userId: string = session.userId
            const userContext = request.userContext as UserContext
            this.logger.info("getUserEligibilityForCSVoucherCode, Fetching coupon for User " + userId + " couponCode: " + couponCode )
            const verifyCaptchaResp = await this.captchaBusiness.verifyCaptchaLogin(captchaResponse, AppUtil.callSource(AuthUtil.getApiKeyFromReq(request)))
            this.logger.info(`getUserEligibilityForCSVoucherCode,  verifyCaptchaResp ${verifyCaptchaResp} , userId ${userId}`)
            if (verifyCaptchaResp.success && verifyCaptchaResp.score > 0.6) {
                const orderSource: OrderSource = AppUtil.callSource(AuthUtil.getApiKeyFromReq(request))
                return this.constelloService.checkUserEligibilityForCouponCode(couponCode, userId, session.deviceId, AppUtil.callSource(AuthUtil.getApiKeyFromReq(request))).then(async (couponEligibility) => {
                    if (couponEligibility.status.code === "ELIGIBLE") {
                        this.logger.info("getUserEligibilityForCSVoucherCode, couponEligibility" + JSON.stringify(couponEligibility, null, 4))
                        if (couponEligibility.coupon.couponType !== "EXPLICITLY_APPLIED_OFFER") {
                            return this.constelloService.activateCouponForUser(couponCode, userId, session.deviceId, orderSource, null).then(() => {
                                return {
                                    rewardDescription: "Congratulations, coupon has been applied.",
                                    isEligible: true,
                                    refreshCart: true
                                }
                            }).catch(error => {
                                throw this.errorFactory.withCode(ErrorCodes.GENERIC_COUPON_ERROR, 400).withMeta({
                                    message: error.message || "Failed to apply coupon",
                                    errorMsg: error.message || "Failed to apply coupon"
                                }).build()
                            })
                        } else {
                            const offerId = (couponEligibility.coupon.reward as ExplicitlyAppliedOffer).offerId
                            const user = await userContext.userPromise
                            const priceComputeRequest: PriceComputeRequest = {
                                orderSource: userContext.sessionInfo.orderSource,
                                orderTotalPayable: orderTotalPayableWithoutDiscount,
                                userInfo: {
                                    userId: userContext.userProfile.userId,
                                    deviceId: userContext.sessionInfo.deviceId,
                                    email: user?.email,
                                    phone: user?.phone,
                                    workEmail: user?.workEmail
                                },
                                productPriceDetails,
                                verticalSpecificPriceComputationDetails: {
                                    skuIdToProductDetails
                                }
                            }
                            const request: CouponSavingRequest = {
                                offerIds: [offerId],
                                priceComputeRequest
                            }
                            this.logger.info("getUserEligibilityForCSVoucherCode, request" + JSON.stringify(request, null, 4))
                            const couponSavings: CouponSavingsResponse = await this.offerServiceV3.getCouponSavings(request)
                            this.logger.info("getUserEligibilityForCSVoucherCode, response " + JSON.stringify(couponSavings, null, 4))
                            // @ts-ignore
                            const savingAmount = couponSavings?.couponSavings?.[offerId]?.savings
                            if (savingAmount > 0) {
                                this.logger.info("getUserEligibilityForCSVoucherCode, couponSavings" + JSON.stringify(couponSavings, null, 4))
                                return {
                                    rewardDescription: `You saved ₹${savingAmount} `,
                                    isEligible: true
                                }
                            } else {
                                this.logger.info("getUserEligibilityForCSVoucherCode, couponSavings" + JSON.stringify(couponSavings, null, 4))
                                throw this.errorFactory.withCode(ErrorCodes.GENERIC_COUPON_ERROR, 400).withMeta({
                                    message: "CouponCode not unlocked for this cart value.",
                                    errorMsg: "CouponCode not unlocked for this cart value.",
                                    errorCode: couponEligibility.status.code,
                                    couponCode: couponEligibility.code,
                                }).build()

                            }
                        }
                    } else if (couponEligibility.status.code === "USER_HAS_NO_PHONE") {
                        throw this.errorFactory.withCode(ErrorCodes.INVALID_PHONE, MOBILE_NUMBER_NOT_FOUND_ERROR_CODE).build()
                    } else if (couponEligibility.status.code === "INVALID_REDEMPTION_SOURCE" && !_.isEmpty(this._convertCfSourceToUserSource(couponEligibility.coupon?.redemptionSourcesConfig))) {
                        throw this.errorFactory.withCode(ErrorCodes.COUPON_INVALID_SOURCE, 400)
                        .withMeta({validSource: this._convertCfSourceToUserSource(couponEligibility?.coupon?.redemptionSourcesConfig)}).build()
                    } else {
                        throw this.errorFactory.withCode(ErrorCodes.GENERIC_COUPON_ERROR, 400).withMeta({
                            message: couponEligibility.status.message,
                            errorMsg: couponEligibility.status.message,
                            buttonText: "RE ENTER CODE",
                            errorCode: couponEligibility.status.code,
                            couponCode: couponEligibility.code,
                            modalHeight: 250,
                        }).build()
                    }
                })
            } else {
                this.metricsUtil.reportBlockedRequest(BlockingType.CAPTCHA_FAILURE)
                this.logger.error(`AuthController:Failed to verify captcha Error: Failed to verify. Please try again`)
                res.status(400).send({statusCode: 400, error: "Bad Request", message: "captcha not valid"})
            }
        }

        @httpPost("/eligibility") // cultsport and cult.fit websites are calling this...
        async getUserEligibilityFitnessCode(request: express.Request, res: express.Response): Promise<ICouponResponse> {
            const {couponCode, captchaResponse} = request.body
            const session: Session = request.session
            const userId: string = session.userId
            this.logger.info("Fetching coupon for User " + userId)
            const userContext: UserContext = request.userContext as UserContext
            const verifyCaptchaResp = await this.captchaBusiness.verifyCaptchaLogin(captchaResponse, AppUtil.callSource(AuthUtil.getApiKeyFromReq(request)))
            this.logger.info(` /eligibility  verifyCaptchaResp`, verifyCaptchaResp)
            if (verifyCaptchaResp.success && verifyCaptchaResp.score > 0.6) {
                return this.constelloService.checkUserEligibilityForCouponCode(couponCode, userId, session.deviceId, AppUtil.callSource(AuthUtil.getApiKeyFromReq(request))).then((couponEligibility) => {
                    if (!AppUtil.isCultSportWebApp(userContext) && couponEligibility?.coupon?.vertical === "CULT_GEAR") {
                        throw this.errorFactory.withCode(ErrorCodes.GENERIC_COUPON_ERROR, 400).withMeta({
                            message: "Unfortunately, Vouchers for cult.sport cannot be redeemed here.",
                            errorMsg: "Unfortunately, Vouchers for cult.sport cannot be redeemed here.",
                            buttonText: "RE ENTER CODE",
                            errorCode: couponEligibility.status.code,
                            couponCode: couponEligibility.code,
                            modalHeight: 250,
                        }).build()
                    }
                    else if (couponEligibility.status.code === "ELIGIBLE") {
                        const {code, coupon} = couponEligibility
                        const userAgent: UserAgent = AuthUtil.getUserAgent(request)
                        if (userAgent === "APP" && coupon.shouldShowTnc) {
                            throw VoucherUtil.throwTncActionError(this.errorFactory, couponCode)
                        }
                        const actionType: ActionType = "ACTIVATE_COUPON"
                        const action: Action = {
                            actionType: actionType,
                            meta: {
                                couponCode: code,
                                shouldShowTnc: coupon.shouldShowTnc,
                            },
                            title: "REDEEM NOW"
                        }
                        if (["tvos", "ios"].includes(userContext.sessionInfo?.osName?.toLowerCase()) && this.isLiveCoupon(coupon)) {
                            throw this.errorFactory.withCode(ErrorCodes.GENERIC_COUPON_ERROR, 400).withMeta({
                                message: "Unfortunately, Vouchers for cultpass HOME cannot be redeemed on iOS devices",
                                errorMsg: "Unfortunately, Vouchers for cultpass HOME cannot be redeemed on iOS devices",
                                buttonText: "RE ENTER CODE",
                                errorCode: "NOT_VALID_FOR_USER",
                                couponCode: couponEligibility.code,
                                modalHeight: 250,
                            }).build()
                        }
                        return this._transformCouponResponse(code, coupon, "ELIGIBILITY", action, userContext.userProfile.timezone)
                    } else if (couponEligibility.status.code === "USER_HAS_NO_PHONE") {
                        throw this.errorFactory.withCode(ErrorCodes.INVALID_PHONE, MOBILE_NUMBER_NOT_FOUND_ERROR_CODE).build()
                    } else if (couponEligibility.status.code === "INVALID_REDEMPTION_SOURCE" && !_.isEmpty(this._convertCfSourceToUserSource(couponEligibility.coupon?.redemptionSourcesConfig))) {
                        throw this.errorFactory.withCode(ErrorCodes.COUPON_INVALID_SOURCE, 400)
                        .withMeta({validSource: this._convertCfSourceToUserSource(couponEligibility?.coupon?.redemptionSourcesConfig)}).build()
                    } else {
                        throw this.errorFactory.withCode(ErrorCodes.GENERIC_COUPON_ERROR, 400).withMeta({
                            message: couponEligibility.status.message,
                            errorMsg: couponEligibility.status.message,
                            buttonText: "RE ENTER CODE",
                            errorCode: couponEligibility.status.code,
                            couponCode: couponEligibility.code,
                            modalHeight: 250,
                        }).build()
                    }
                })
            } else {
                this.metricsUtil.reportBlockedRequest(BlockingType.CAPTCHA_FAILURE)
                this.logger.error(`AuthController:Failed to verify captcha Error: Failed to verify. Please try again`)
                res.status(400).send({statusCode: 400, error: "Bad Request", message: "captcha not valid"})
            }
        }

        private isLiveCoupon(coupon: Coupon): boolean {
            if (coupon.vertical === "LIVE_FIT" || coupon.couponType === "LIVE") {
                return true
            }
            if (coupon.couponType == "MULTI_COUPON") {
                const reward: MultiCouponReward = coupon.reward as MultiCouponReward
                for (const details of reward.rewards) {
                    if (details.rewardType == "LIVE_MEMBERSHIP") {
                        return true
                    }
                }
            }
            return false
        }

        @httpPost("/activate")
        async activateCouponForUser(request: express.Request): Promise<ICouponResponse> {
            const {couponCode, shouldShowTnc, tncAccepted, captchaResponse, meta} = request.body
            const session: Session = request.session
            const userId: string = session.userId
            this.logger.info("Activating coupon for User " + userId)
            const userContext: UserContext = request.userContext as UserContext
            const userAgent: UserAgent = AuthUtil.getUserAgent(request)
            const verifyCaptchaResp = await this.captchaBusiness.verifyCaptchaLogin(captchaResponse, AppUtil.callSource(AuthUtil.getApiKeyFromReq(request)))
            if (userAgent === "APP" && shouldShowTnc) {
                throw VoucherUtil.throwTncActionError(this.errorFactory, couponCode)
            }

            if ((verifyCaptchaResp.success && verifyCaptchaResp.score > 0.6) || userAgent === "APP") {
                return this.constelloService.activateCouponForUser(couponCode, userId, session.deviceId, AppUtil.callSource(AuthUtil.getApiKeyFromReq(request)), tncAccepted, null, meta).then((couponConsumed) => {
                    const {code, coupon} = couponConsumed
                    const action: Action = this._getPostActivationAction(coupon.couponType, coupon.vertical, {
                        couponCode: code,
                        campaignId: coupon.campaignId
                    })
                    return this._transformCouponResponse(code, coupon, "ACTIVATED", action, userContext.userProfile.timezone)
                }).catch(error => {
                    throw this.errorFactory.withCode(ErrorCodes.GENERIC_COUPON_ERROR, 400).withMeta({
                        message: error.message || "Failed to apply coupon",
                        errorMsg: error.message || "Failed to apply coupon"
                    }).build()
                })
            } else {
                this.metricsUtil.reportBlockedRequest(BlockingType.CAPTCHA_FAILURE)
                this.logger.error(`AuthController:Failed to verify captcha Error: Failed to verify. Please try again`)
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_COUPON_ERROR, 400).withMeta({
                    message: "Failed to apply coupon",
                    errorMsg: "Failed to apply coupon"
                }).build()
            }
        }

        @httpPost("/v2/activate")
        async activateCoupon(request: express.Request, res: express.Response) {
            const userAgent: UserAgent = AuthUtil.getUserAgent(request)
            const appVersion: string = request.headers["appversion"] as string
            if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
                const captchaToken = request.body.captchaToken
                const verifyCaptchaResp = await this.captchaBusiness.verifyCaptchaLogin(captchaToken, AppUtil.callSource(AuthUtil.getApiKeyFromReq(request)))
                if (verifyCaptchaResp.success && verifyCaptchaResp.score > 0.6) {
                    return await this._activateCoupon(request, userAgent)
                } else {
                    this.metricsUtil.reportBlockedRequest(BlockingType.CAPTCHA_FAILURE)
                    this.logger.error(`CouponController:Failed to verify captcha Error: Failed to verify. Please try again`)
                    res.status(400).send({statusCode: 400, error: "Bad Request", message: "captcha not valid"})
                }
            } else {
                return await this._activateCoupon(request, userAgent)
            }
        }

        @httpGet("/coupon/:couponCode")
        async getCoupon(request: express.Request) {
            const {couponCode} = request.params
            const session: Session = request.session
            const isApp = AuthUtil.getUserAgent(request) === "APP"
            return this.constelloService.getCoupon(couponCode).then(async ({coupon}) => {
                if (_.isNil(coupon)) {
                    return VoucherUtil._transformV2CouponFailResponse({
                        status: {
                            code: "INVALID_CODE"
                        }
                    }, isApp)
                }
                return this._transformCoupon(coupon, session.isNotLoggedIn, isApp)
            }).catch(error => {
                this.logger.error(`Got error while fetching coupon: ${JSON.stringify(error, ["message", "stack"])}`)
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_COUPON_ERROR, 400).withMeta({
                    message: error.message || "Failed to fetch coupon",
                    errorMsg: error.message || "Failed to fetch coupon"
                }).build()
            })
        }

        private async _activateCoupon(request: express.Request, userAgent: UserAgent) {
            const {couponCode} = request.body
            const session: Session = request.session
            const userId: string = session.userId
            const userContext: UserContext = request.userContext as UserContext
            const tz = userContext.userProfile.timezone
            this.logger.info("Activating coupon for User " + userId)

            const couponEligibility: CouponConsumptionEligibility = await this.constelloService.checkUserEligibilityForCouponCode(couponCode, userId, session.deviceId, AppUtil.callSource(AuthUtil.getApiKeyFromReq(request)))
            this.logger.info(`Fetching coupon eligibilty ${couponEligibility}`)
            if (couponEligibility.status.code !== "ELIGIBLE") {
                return VoucherUtil._transformV2CouponFailResponse(couponEligibility, userAgent === "APP")
            }
            const coupon = couponEligibility.coupon
            if (["tvos", "ios"].includes(userContext.sessionInfo?.osName?.toLowerCase()) && this.isLiveCoupon(coupon)) {
                couponEligibility.status = {
                    code: "NOT_VALID_FOR_USER",
                    message: coupon.couponType === "GIFT_CARD" ? "Unfortunately, cultpass HOME referral rewards are not available for iOS devices. You can still use referral rewards for other products."
                        : "Unfortunately, Vouchers for cultpass HOME cannot be redeemed on iOS devices"
                }
                return  VoucherUtil._transformV2CouponFailResponse(couponEligibility, userAgent === "APP")
            }

            try {
                const {code, coupon, couponConsumption} = await this.constelloService.activateCouponForUser(couponCode, userId, session.deviceId, AppUtil.callSource(AuthUtil.getApiKeyFromReq(request)))
                this.logger.info(`Activating coupon code: ${code} ${coupon} ${couponConsumption}`)
                return this._transformV2CouponResponse(userContext, coupon, couponConsumption, tz, userAgent)
            } catch (error) {
                this.logger.error(`Error while activating voucher v2: ${error}`)
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_COUPON_ERROR, 500).withMeta({
                    message: error.message || "Failed to activate coupon",
                    errorMsg: error.message || "Failed to activate coupon"
                }).build()
            }
        }

        async _transformCoupon(coupon: Coupon, isNotLoggedIn: boolean, isApp: boolean) {
            const couponBenefits = await this._getCouponBenefits(coupon, isApp)
            if (!isNotLoggedIn) {
                return {
                    sections: [couponBenefits, VoucherUtil._getLoggedInView(coupon)],
                    action: VoucherUtil._getBenefitAction(coupon),
                }
            }
            return {
                sections: [couponBenefits, VoucherUtil._getLoginView(coupon)],
                action: [{
                    title: "VERIFY NUMBER",
                    actionType: "PHONE_LOGIN",
                    meta: {
                        action: VoucherUtil._getBenefitAction(coupon),
                    },
                    note: VoucherUtil._getBenefitActionNote(coupon)
                }]
            }
        }

        async _getCouponBenefits(coupon: Coupon, isApp: boolean) {
            return {
                viewType: "BENEFITS_INFO",
                title: [{
                    text: await this._getPreActivationMessage(coupon),
                    style: {
                        fontFamily: AppFont.Medium,
                        fontSize: 18,
                        color: "#000000",
                        lineHeight: 26,
                        textAlign: "center"
                    }
                }],
                rightInfo: {
                    title: "View T&C",
                    action: {
                        actionType: "NAVIGATION",
                        url: VoucherUtil._getTCLink(isApp, coupon.tnc.link),
                    },
                },
                offerText: coupon.animationText || VoucherUtil._getDefaultAnimationText(coupon),
                imgUrl: coupon.heroImage
            }
        }

        async _getPreActivationMessage(coupon: Coupon) {
            return await this._renderTemplate(coupon.preActivationMessage, coupon)
        }

        async _renderTemplate(template: string, coupon: Coupon) {
            if (_.isNil(template)) return undefined
            return mustache.render(template, {
                partnerName: coupon.partnerName || "A Friend",
                ...await this._getRewardVariables(coupon)
            })
        }

        async _getPostActivationMessage(coupon: Coupon, defaultPostActivation: PostActivation) {
            const title = await this._renderTemplate(coupon.postActivation?.title || defaultPostActivation.title, coupon)
            const subTitle = await this._renderTemplate(coupon.postActivation?.subTitle || defaultPostActivation.subTitle, coupon)
            return {
                title: title && [{
                    text: title,
                    style: {
                        fontFamily: AppFont.Bold,
                        fontSize: 18,
                        color: "#000000",
                        lineHeight: 26,
                        textAlign: "center"
                    }
                }],
                subTitle: subTitle && [{
                    text: subTitle,
                    style: {
                        fontFamily: AppFont.Medium,
                        fontSize: 18,
                        color: "#000000",
                        lineHeight: 26,
                        textAlign: "center"
                    }
                }]
            }
        }

        _getPostActivationAction(couponType: CouponType, vertical: CFVertical, meta: any): Action {
            const actionType: ActionType = "NAVIGATION"
            let actionTitle: string = "OK"
            let actionUrl: string = undefined
            switch (couponType) {
                case "OFFER":
                    actionTitle = this._getActionTitleForOffer(vertical, meta.campaignId)
                    actionUrl = this._getClpActionUrl(vertical)
                    break
                case "CULT":
                    actionTitle = "BOOK A CLASS"
                    actionUrl = this._getBookingActionUrl(vertical)
                    break
                case "FITCASH":
                    actionTitle = "OK"
                    break
                case "GIFT_CARD":
                    actionTitle = this._getActionTitleForGiftCards(vertical, meta.campaignId)
                    actionUrl = this._getClpActionUrl(vertical, meta.campaignId)
                    break
                case "LIVE":
                    actionTitle = this._getActionTitle(vertical)
                    actionUrl = this._getBookingActionUrl(vertical)
                    break
                default:
                    actionTitle = "OK"
            }

            return {
                actionType: actionType ? actionType : undefined,
                meta: meta,
                title: actionTitle,
                url: actionUrl ? actionUrl : null
            }
        }

        _convertCfSourceToUserSource(redemptionSourcesConfig: AllowedRedemptionSourcesConfig): string {
            const sourceToMessageMap: {[source in OrderSource]?: string} = {
                "CUREFIT_APP": "cult.fit app",
                "CUREFIT_NEW_WEBSITE": "cult.fit website",
                "CULTSPORT_WEBSITE": "cultsport.com",
                "SUGARFIT_APP": "sugar.fit app"
            }
            const validSourcesToMessage = _.compact(redemptionSourcesConfig?.allowedOrderSources?.map(source => sourceToMessageMap[source]))
            return validSourcesToMessage?.[0]
        }

        async _transformV2CouponResponse(userContext: UserContext, coupon: Coupon, couponConsumption: CouponConsumption, tz: Timezone, userAgent: UserAgent) {
            const defaultPostActivation = VoucherUtil.getDefaultPostActivation(userContext, coupon, userAgent)
            return {
                sections: [{
                    ...await this._getPostActivationMessage(coupon, defaultPostActivation),
                    viewType: "BENEFITS_INFO",
                    rightInfo: {
                        title: "View T&C",
                        action: {
                            actionType: "NAVIGATION",
                            url: VoucherUtil._getTCLink(userAgent === "APP", coupon.tnc.link),
                        },
                    },
                    offerText: coupon.animationText || VoucherUtil._getDefaultAnimationText(coupon),
                    imgUrl: coupon.heroImage,
                }, {
                    viewType: "INFO_VIEW",
                    info: {
                        title: "Please Note",
                        items: this._getNotes(!_.isEmpty(coupon.postActivation?.notes) ?  coupon.postActivation?.notes : defaultPostActivation?.notes, coupon.benefitsIconImage, couponConsumption, tz)
                    },
                }],
                action: VoucherUtil._getSuccessAction(coupon.postActivation?.actions || defaultPostActivation?.actions, userAgent),
                result: "SUCCESS"
            }
        }

        _getActionTitleForOffer(vertical: CFVertical, campaignId?: string) {
            switch (vertical) {
                case "CARE":
                    if (campaignId && campaignId.includes("bootcamp")) {
                        return "BUY NOW"
                    }
                    return "BOOK CONSULTATION"
                default:
                    return "AVAIL OFFER NOW"
            }
        }

        _getActionTitleForGiftCards(vertical: CFVertical, campaignId: string) {
            switch (vertical) {
                case "MIND":
                    return "EXPLORE MIND"
                case "CULT":
                    return "EXPLORE CULT"
                case "EAT":
                    return "AVAIL NOW"
                case "CARE":
                    return "AVAIL NOW"
                case "LIVE_FIT": {
                    if (campaignId.startsWith("live_referral_live_members") || campaignId.startsWith("live_referral_cult_members")) {
                        return "BOOK A CLASS"
                    }
                    return "AVAIL NOW"
                }
            }
        }

        _getActionTitle(vertical: CFVertical) {
            switch (vertical) {
                case "MIND":
                case "CULT":
                    return "BOOK A CLASS"
                case "EAT":
                    return "AVAIL NOW"
                case "CARE":
                    return "AVAIL NOW"
                case "LIVE_FIT":
                    return "BOOK A CLASS"
            }
        }

        _getBookingActionUrl(vertical: CFVertical): string {
            let actionUrl = undefined
            switch (vertical) {
                case "CULT":
                    actionUrl = EtherActionUtil.getBookCultClassUrl("FITNESS", false, "coupons")
                    break
                case "MIND":
                    actionUrl = EtherActionUtil.bookMindClass("coupons")
                    break
                case "EAT":
                    actionUrl = EtherActionUtil.eatFitClp()
                    break
                case "CARE":
                    actionUrl = "curefit://listpage?pageId=clphcu"
                    break
                case "LIVE_FIT":
                    actionUrl = "curefit://liveclassbooking?productType=FITNESS&isLiveBookingPage=true"
                    break
                default:
                    actionUrl = undefined
            }
            return actionUrl
        }

        _getClpActionUrl(vertical: CFVertical, campaignId?: string): string {
            let actionUrl = undefined
            switch (vertical) {
                case "CULT":
                    actionUrl = "curefit://tabpage?pageId=cult&selectedTab=CultAtCenter"
                    break
                case "MIND":
                    actionUrl = EtherActionUtil.mindCLPVMPage()
                    break
                case "EAT":
                    actionUrl = EtherActionUtil.eatFitClp()
                    break
                case "CARE":
                    if (campaignId && campaignId.startsWith("live_pt_in_referral")) {
                        return "curefit://tabpage?pageId=cult&selectedTab=LivePT"
                    } else if (campaignId && campaignId.includes("bootcamp")) {
                        return "curefit://fl_listpage?pageId=transform_bootcamp&disableAnimation=true"
                    }
                    actionUrl = "curefit://listpage?pageId=clphcu"
                    break
                case "LIVE_FIT":
                    if (campaignId?.startsWith("live_referral_live_members") || campaignId?.startsWith("live_referral_cult_members")) {
                        return "curefit://liveclassbooking?productType=FITNESS&isLiveBookingPage=true"
                    }
                    actionUrl = "curefit://livefitnessbrowsepage"
                    break
                default:
                    actionUrl = undefined
            }
            return actionUrl
        }

        _transformCouponResponse(code: string, coupon: Coupon, type: string, action: Action, timezone: Timezone): ICouponResponse {
            const endDate = TimeUtil.formatDateInTimeZone(timezone, coupon.endDate, "LL")
            if (coupon.couponType === "CULT") {
                if (!coupon.note) {
                    coupon.note = "Free trials will be revoked on usage of this voucher."
                }
                else {
                    coupon.note = coupon.note + "\nFree trials will be revoked on usage of this voucher."
                }
            }
            if (coupon.deviceCheckOnConsumption) {
                coupon.note = coupon.note + "\nOnly this device can be used to book classes or mark attendance."
            }
            const isCityRestricted = false
            return {
                type: type,
                header: {
                    title: coupon.description,
                    heroImage: coupon.heroImage,
                    tnc: coupon.tnc,
                    icon: type === "ACTIVATED" ? "/image/confirmation-icon.svg" : null
                },
                couponDetails: {
                    title: code,
                    subTitle: `Valid till ${endDate}`,
                    info: {
                        title: coupon.description,
                        items: this._getInfoItems(coupon.benefits, coupon.benefitsIconImage)
                    },
                    tnc: {
                        actionType: "NAVIGATION",
                        title: coupon.tnc.text,
                        url: `curefit://webview?uri=${encodeURIComponent(coupon.tnc.link)}`
                    },
                    cityModal: type === "ELIGIBILITY" ? {
                        showCityModal: coupon.couponType === "CORPORATE",
                        title: "Please select city to avail your benefits",
                        subtitle: "",
                        isCityRestricted,
                        allowedCities: isCityRestricted ? [] : null
                    } : null
                },
                action: action,
                footerNote: coupon.note,
                shouldShowTnc: coupon.shouldShowTnc,
            }
        }

        _getInfoItems(items: string[], iconImage: string, attributedText?: boolean): { icon: string, subTitle: string | AttributedText[] }[] {
            const infoItems: { icon: string, subTitle: string | AttributedText[] }[] = []
            _.forEach(items, (item: string) => {
                infoItems.push({
                    icon: iconImage,
                    subTitle: attributedText ? VoucherUtil._getAttributedText(item) : item
                })
            })
            return infoItems
        }

        _getNotes(items: string[], iconImage: string, couponConsumption: CouponConsumption, tz: Timezone): { icon: string, subTitle: AttributedText[] }[] {
            return items?.map(item => ({
                icon: iconImage,
                subTitle: VoucherUtil._getAttributedText(this._renderNoteTemplate(item, couponConsumption, tz))
            }))
        }

        _renderNoteTemplate(item: string, couponConsumption: CouponConsumption, tz: Timezone): string {
            const vars = this._getActivationVariables(couponConsumption, tz)
            return mustache.render(item, vars)
        }

        async _getRewardVariables(coupon: Coupon, couponType?: CouponType): Promise<any> {
            switch (couponType || coupon.couponType) {
                case "CULT": {
                    return {days: (coupon.reward as CultAccess).days}
                }
                case "LIVE": {
                    return {days: (coupon.reward as LiveAccess).days}
                }
                case "OFFER": {
                    const offerId = (coupon.reward as Offer)?.couponOffers?.[0].offerId || (coupon.reward as any as CouponOffer).offerId
                    const {data: offers} = offerId && await this.offerServiceV3.getOffersByIds([offerId])
                    return {
                        offerDescription: offers && offers[offerId].description
                    }
                }
                case "GIFT_CARD": {
                    const reward = (coupon.reward as any as RewardWrapper).rewardData as any
                    return this._getRewardVariables({...coupon, reward}, VoucherUtil._getGiftCardCouponType(coupon))
                }
            }
        }

        _getActivationVariables(couponConsumption: CouponConsumption, tz: Timezone): { startDate?: string, endDate?: string, amount?: number, offerDescription?: string } {
            const activation = couponConsumption.activation as any
            switch (couponConsumption.couponType) {
                case "CULT":
                    return {
                        startDate: VoucherUtil._formatDate(activation.startDate || activation.membership?.startDate, tz),
                        endDate: VoucherUtil._formatDate(activation.endDate || activation.membership?.endDate, tz)
                    }
                case "OFFER": {
                    return {
                        startDate: VoucherUtil._formatDate(activation?.[0]?.userOfferEligibility?.startDate, tz),
                        endDate: VoucherUtil._formatDate(activation?.[0]?.userOfferEligibility?.endDate, tz),
                    }
                }
                case "LIVE":
                    return {
                        startDate: VoucherUtil._formatDate(activation.start, tz),
                        endDate: VoucherUtil._formatDate(activation.end, tz)
                    }
                case "GIFT_CARD": {
                    const firstReward = activation?.[0]?.rewardFulfilmentResponse as LedgerEntry<Response>
                    if (_.isNil(firstReward)) return {}
                    switch (firstReward.rewardType) {
                        case "LIVE_MEMBERSHIP": {
                            const resp = firstReward.fulfilmentResponse.data as Membership
                            return {
                                startDate: VoucherUtil._formatDate(resp?.start, tz),
                                endDate: VoucherUtil._formatDate(resp?.end, tz)
                            }
                        }
                        case "FITCASH": {
                            const resp = firstReward.fulfilmentResponse.data as FitcashResponse
                            return {
                                amount: resp.amount
                            }
                        }
                        case "OFFER": {
                            const resp = firstReward.fulfilmentResponse.data as OfferEligibilityResponse
                            return {
                                startDate: VoucherUtil._formatDate(resp?.userOfferEligibility?.startDate, tz),
                                endDate: VoucherUtil._formatDate(resp?.userOfferEligibility?.endDate, tz)
                            }
                        }
                        case "CULT": {
                            const resp = firstReward.fulfilmentResponse.data as CultResponse
                            return {
                                startDate: VoucherUtil._formatDate(resp?.startDate || resp.membership?.startDate, tz),
                                endDate: VoucherUtil._formatDate(resp?.endDate || resp.membership?.endDate, tz)
                            }
                        }
                    }
                }
            }
        }
    }

    return CouponController
}

export default CouponControllerFactory
