import { Consumable } from "@curefit/offer-common"
import {
    CouponInfoWidget,
    Coupon
} from "@curefit/apps-common"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { UserContext } from "@curefit/userinfo-common"
import * as _ from "lodash"
import { capitalizeFirstLetter } from "@curefit/base"
import AppUtil from "../util/AppUtil"


export class CouponUtil {

    static getCouponsInfoWidget(consumables: Array<Consumable>, userContext: UserContext, type?: string, orderId?: string): any {
        const filteredConsumables: Array<Consumable> =
            _.isEmpty(consumables) ? [] : consumables.filter(consumable => consumable.type == "XOXODAY_COUPON")
        if (!_.isEmpty(filteredConsumables)) {
            const containerStyle = type === "GEAR" ? {
                // for aligning it with the other widgets for gear order screen
                marginLeft: 10,
                marginRight: 10
            } : undefined
            return {
                widgetType: "COUPON_INFO_WIDGET",
                title: "Vouchers",
                type: type,
                containerStyle: containerStyle,
                consumable: _.map(filteredConsumables, (consumable: Consumable) => {
                    return {
                        title: this.getCouponTitle(consumable),
                        subTitle: !_.isEmpty(consumable.expiresOn) ? `Valid till ${this.getCouponExpiryDate(consumable, userContext.userProfile.timezone)}` : "",
                        tncAction: (AppUtil.isAppDynamicGIFBannerWidgetSupported(userContext) && orderId != null) ? this.showCustomBottomSheetForVoucher(orderId, consumable) : {
                            title: "T&C",
                            actionType: "SHOW_OFFERS_TNC_MODAL",
                            meta: {
                                title: "Terms and Conditions",
                                url: consumable.tncUrl
                            }
                        },
                        payload: {
                            showNavAction: true,
                        },
                        coupons: (AppUtil.isAppDynamicGIFBannerWidgetSupported(userContext) && orderId != null) ? null : _.map(consumable.data.coupons, (coupon: Coupon) => ({
                                couponCode: coupon.couponCode,
                                couponPin: coupon.couponPin,
                                action: {
                                    actionType: "COPY_TO_CLIPBOARD",
                                    title: "Copy",
                                    meta: {
                                        text: coupon.couponCode,
                                        dataType: "VOUCHER"
                                    }
                                }
                            })
                        )
                    }
                })
            }
        }
    }
    public static showCustomBottomSheetForVoucher(orderId: string, consumableList: Consumable) {
        const firstCoupon = _.get(consumableList, "data.coupons[0]", null)
        return {
            "actionType": "SHOW_CUSTOM_BOTTOM_SHEET",
            "isFlutterScreenAction": true,
            "meta": {
                "showTopNotch": true,
                "widgets": [
                    {
                        "widgetType": "SHOW_COUPON_VOUCHER_WIDGET",
                        "title": "Offer Details",
                        "description": CouponUtil.getCouponTitle(consumableList) ?? "Claim your FREE voucher",
                        couponString: firstCoupon ? "click here to claim" : "Voucher is being generated",
                        isCouponValid: !!firstCoupon,
                        orderId: orderId,
                        coupon: firstCoupon ? firstCoupon.couponCode : null,
                    },
                    {
                        "widgetType": "LADDER_INFO_WIDGET",
                        "title": "Steps to claim your voucher",
                        "items": [
                            {
                                "title": "Click on the above link",
                                "description": "This link will redirect you to a third-party website",
                            },
                            {
                                "title": "Login With your registered number",
                                "description": "Use the same phone number registered with cult.fit",
                            },
                            {
                                "title": "Verify phone number",
                                "description": "You will require to verify phone number via OTP",
                            },
                            {
                                "title": "Redeem your voucher on the respective website",
                            }
                        ]
                    },
                    {
                        "widgetType": "ACTION_LIST_WIDGET",
                        "hasDivideBelow": false,
                        "hasDividerTop": false,
                        "layoutProps": {
                            "spacing": {
                                "top": "0",
                                "bottom": "50"
                            }
                        },
                        "actionList": [
                            {
                                "title": "CLOSE",
                                "actionType": "POP_NAVIGATION",
                                "variant": "secondary"
                            }
                        ]
                    }
                ]
            },
            "title": "GET NOW",
        }
    }

    public static getCouponExpiryDate = (consumable: Consumable, userTimeZone: Timezone): string => {
        // One day less
        const oneDayLess = TimeUtil.subtractDays(userTimeZone, consumable.expiresOn, 1)
        return TimeUtil.formatDateStringInTimeZone(oneDayLess, userTimeZone, "Do MMM, YYYY")
    }

    private static nonAmountBrands = ["ZOMATO", "ZEE5", "NUTRI_CONSULT", "HINDU", "JBL", "LENSKART_GOLD", "SHAWACADEMY", "TIMESPRIME", "1MG", "CULTACCESS", "FLIPKARTFLIGHTS", "JIOSAAVN_CFLIVE", "MAGZTER_CFLIVE", "MIVI", "Wakefit", "ZOMATO_CFLIVE"]

    public static getCouponTitle(consumable: Consumable) {
        if (consumable.benefits) {
            return consumable.benefits
        }
        if (consumable.type === "ZOMATO") {
            return `${capitalizeFirstLetter(consumable.type)} Gold voucher`
        } else if (consumable.type === "CULTACCESS") {
            return `${consumable.value} day Cult access`
        }
        return `${capitalizeFirstLetter(consumable.type)} voucher` + (CouponUtil.nonAmountBrands.indexOf(consumable.type) === -1 ? ` worth Rs. ${consumable.value}` : `` )
    }
}
