import { Coupon, CouponOffer, CouponType, Offer, PostActivation, RewardWrapper } from "@curefit/constello-common"
import { CFVertical } from "@curefit/constello-common/dist/src/Constello"
import { AppFont } from "../util/AppUtil"
import { UserContext } from "@curefit/userinfo-common"
import { Timezone } from "@curefit/util-common"
import * as _ from "lodash"
import * as moment from "moment-timezone"
import { Action } from "@curefit/apps-common"
import { UserAgentType as UserAgent } from "@curefit/base-common/dist/src/models/Common"
import { ErrorCodes } from "../error/ErrorCodes"
import { ErrorFactory } from "@curefit/error-client"
import { ErrorBuilder } from "@curefit/error-client/dist/src/GenericError"
import LiveUtil from "../util/LiveUtil"


export interface AttributedText {
    text: string
    style?: any
}


export class VoucherUtil {

    static isProd(): boolean {
        return process.env.<PERSON>NVIRONMENT === "PRODUCTION"
    }

    static getUrl(path: string): string {
        const baseUrl = this.isProd() ? "https://www.cure.fit" : "https://stage1234.cure.fit"
        return baseUrl + path
    }

    static getDesktopURL(): string {
        const widgetId = this.isProd() ?
            "?widgetId=2c3934ce-ed53-43c5-9e5b-83b9b6042515"
            : "?widgetId=ebbe5b0a-bb91-4be9-8737-f41a04be5a6c"
        return this.getUrl(widgetId)
    }

    static _getGiftCardCouponType(coupon: Coupon): CouponType {
        switch ((coupon.reward as any as RewardWrapper).rewardType) {
            case "CULT":
                return "CULT"
            case "OFFER":
                return "OFFER"
            case "LIVE_MEMBERSHIP":
                return "LIVE"
            case "FITCASH":
                return "FITCASH"
        }
    }

    static resolveDeeplink({ appLink, stageBranchLink, prodBranchLink, userAgent }: { appLink: string, stageBranchLink: string, prodBranchLink: string, userAgent: UserAgent }) {
        if (userAgent === "APP") {
            return appLink
        }
        return this.isProd() ? prodBranchLink : stageBranchLink
    }

    static getDefaultPostActivation(userContext: UserContext, coupon: Coupon, userAgent: UserAgent, couponType?: CouponType): PostActivation {
        switch (couponType || coupon.couponType) {
            case "CULT":
                return {
                    title: "Complimentary Access Unlocked",
                    subTitle: "Thanks to {{partnerName}}, you've unlocked {{days}} day complimentary access to cult centers",
                    notes: [
                        "Complimentary access is valid till {{endDate}}",
                        "Login via same phone number to use it on cult.fit app or website"

                    ],
                    actions: {
                        primaryButton: {
                            text: "EXPLORE CULT ON APP",
                            url: this.resolveDeeplink({
                                appLink: "curefit://classbookingv2",
                                prodBranchLink: "https://cure.app.link/FShOwgj619",
                                stageBranchLink: "https://cure.test-app.link/nSaXXEtF09",
                                userAgent
                            })
                        },
                        secondaryButton: {
                            text: "OR EXPLORE ON WEBSITE",
                            url: this.getUrl("/cult")
                        }
                    }
                }
            case "OFFER":
                return {
                    title: "Offer Unlocked",
                    subTitle: "Thanks to {{partnerName}}, you've unlocked {{offerDescription}}",
                    notes: [
                        "The offer is valid till {{endDate}}",
                        "Login via same phone number to use it on cult.fit app or website"
                    ],
                    actions: this.getCLPActionsForOffer(coupon.vertical, userAgent, coupon.campaignId)
                }
            case "LIVE":
                return {
                    title: "Membership Unlocked",
                    subTitle: `Thanks to {{partnerName}}, you've unlocked {{days}} day ${LiveUtil.getLiveBranding(userContext)} membership`,
                    notes: [
                        "The membership is valid till {{endDate}}",
                        "Login via same phone number to use it on cult.fit app or website"
                    ],
                    actions: {
                        primaryButton: {
                            text: "BOOK A CLASS ON APP",
                            url: this._getClpDeepLink("LIVE_FIT", userAgent)
                        },
                        secondaryButton: {
                            text: "OR BOOK ON WEBSITE",
                            url: this.getUrl("/live")
                        }
                    }
                }
            case "GIFT_CARD":
                return this.getDefaultPostActivation(userContext, coupon, userAgent, this._getGiftCardCouponType(coupon))
        }
        return undefined
    }

    static getCLPActionsForOffer(vertical: CFVertical, userAgent: UserAgent, campaignId?: string) {
        switch (vertical) {
            case "CULT":
                return {
                    primaryButton: {
                        text: "USE IT ON APP",
                        url: this._getClpDeepLink(vertical, userAgent)
                    },
                    secondaryButton: {
                        text: "OR USE IT ON WEBSITE",
                        url: this.getUrl("/cult")
                    }
                }
            case "CARE":
                let secondaryUrl = this.getUrl("/care")
                const primaryUrl = this._getClpDeepLink(vertical, userAgent, campaignId)
                if (campaignId && campaignId.startsWith("live_pt_in_referral")) {
                    secondaryUrl = this.getUrl("/cult/online-personal-training")
                }
                return {
                    primaryButton: {
                        text: "USE IT ON APP",
                        url: primaryUrl
                    },
                    secondaryButton: {
                        text: "OR USE IT ON WEBSITE",
                        url: secondaryUrl
                    }
                }
            case "EAT":
                return {
                    primaryButton: {
                        text: "USE IT ON APP",
                        url: this._getClpDeepLink(vertical, userAgent)
                    },
                    secondaryButton: {
                        text: "OR USE IT ON WEBSITE",
                        url: this.getUrl("/eat")
                    }
                }
            case "LIVE_FIT":
                return {
                    primaryButton: {
                        text: "USE IT ON APP",
                        url: this.resolveDeeplink({
                            appLink: "curefit://livefitnessbrowsepage",
                            prodBranchLink: "https://cure.app.link/PwPczwWz79",
                            stageBranchLink: "https://cure.test-app.link/cMgYD0Xz79",
                            userAgent
                        })
                    },
                    secondaryButton: {
                        text: "OR USE IT ON WEBSITE",
                        url: this.getUrl("/livefitnessbrowsepage")
                    }
                }
        }
    }

    static _getDefaultAnimationText(coupon: Coupon, couponType?: CouponType): string {
        switch (couponType || coupon.couponType) {
            case "OFFER":
                return "OFFER UNLOCKED"
            case "CULT":
                return "CULT ACCESS PASS UNLOCKED"
            case "LIVE":
                return "LIVE MEMBERSHIP UNLOCKED"
            case "GIFT_CARD":
                return this._getDefaultAnimationText(coupon, this._getGiftCardCouponType(coupon))
            default:
                return "BENEFITS UNLOCKED"
        }
    }

    static _getDefaultUnlockBenefitsCTA(coupon: Coupon, couponType?: CouponType): string {
        switch (couponType || coupon.couponType) {
            case "OFFER":
                return "UNLOCK OFFER"
            case "CULT":
                return "UNLOCK CULT ACCESS PASS"
            case "LIVE":
                return "UNLOCK LIVE MEMBERSHIP"
            case "GIFT_CARD":
                return this._getDefaultUnlockBenefitsCTA(coupon, this._getGiftCardCouponType(coupon))
            default:
                return "UNLOCK BENEFITS"
        }
    }

    static _getErrorMessage(code: string) {
        switch (code) {
            case "ALREADY_CONSUMED":
                return `You have already unlocked this gift before :(`
            case "USER_HAS_NO_PHONE":
                return `To avail this gift, you need to update your phone number in cure.fit account first.`
            case "PAST_END_DATE":
                return `You got late :( This gift has already expired`
            case "CULT_ACTIVATION_ERROR":
                return `One pass at a time, please. You already have an ongoing cult pass. Please , come back later.`
            case "INVALID_CODE":
                return `This is not valid :( Either the code or link is wrong. Please re-check.`
            case "MAX_GLOBAL_COUNT":
                return `This gift have already unlocked to maximum number of times :(`
            case "MAX_USER_COUNT":
                return `You have already unlocked this gift maximum number of times :(`
            case "MAX_USER_GLOBAL_CAP_COUNT":
                return `You got late :( This gift is already used maximum number of times`
            case "NOT_NEW_USER":
                return `Looks like you are not eligible for this gift. This is only applicable for new users`
            case "NOT_VALID_FOR_USER":
                return `You are not eligible to avail this gift :( For more details, please check terms and conditions`
            case "FAILED_TO_APPLY":
            default:
                return `Failed to apply coupon`
        }
    }

    static _getAttributedText(item: string): AttributedText[] {
        return [{ text: item, style: { color: "#333747" } }]
    }

    static _getTCLink(isApp: boolean, link: string) {
        if (isApp) return `curefit://webview?uri=${encodeURIComponent(link)}`
        return link
    }

    static _getLoggedInView(coupon: Coupon) {
        return {
            viewType: "LOGGED_IN_VIEW",
            title: this._getBenefitTitle(coupon),
            note: this._getBenefitActionNote(coupon)
        }
    }

    static _getLoginView(coupon: Coupon) {
        return {
            viewType: "LOGIN_VIEW",
            title: this._getBenefitTitle(coupon),
            note: this._getBenefitActionNote(coupon)
        }
    }

    static _getBenefitTitle(coupon: Coupon) {
        return [{
            text: coupon.preActivationSubTitle || "Unlock the gift of health in just 1 step!",
            style: { fontFamily: AppFont.Regular, fontSize: 16, color: "#000000" }
        }]
    }

    static _getBenefitAction(coupon: Coupon) {
        return [{
            title: coupon.unlockBenefitsCTA || VoucherUtil._getDefaultUnlockBenefitsCTA(coupon),
            actionType: "UNLOCK_BENEFITS",
            note: this._getBenefitActionNote(coupon)
        }]
    }

    static _getBenefitActionNote(coupon: Coupon, couponType?: CouponType): AttributedText[] {
        switch (couponType || coupon.couponType) {
            case "OFFER":
                const reward = coupon.reward as Offer
                return [{
                    text: "Note - Once unlocked, offer will be valid for",
                    style: { fontFamily: AppFont.Regular, fontSize: 12, color: "#888e9e" }
                }, {
                    text: ` ${(reward.couponOffers?.[0] || (coupon.reward as any as CouponOffer)).validity} days`,
                    style: { fontFamily: AppFont.Regular, fontSize: 12, color: "#000000" }
                }]
            case "CULT":
                return [{
                    text: `Note - Once unlocked, the access pass will start immediately`,
                    style: { fontFamily: AppFont.Regular, fontSize: 12, color: "#888e9e" }
                }]
            case "GIFT_CARD": {
                const reward = (coupon.reward as any as RewardWrapper).rewardData as any
                return this._getBenefitActionNote({ ...coupon, reward }, VoucherUtil._getGiftCardCouponType(coupon))
            }
            case "FITCASH":
                return [{
                    text: `Note - Once unlocked, the Fitcash will reflect in your wallet immediately`,
                    style: { fontFamily: AppFont.Regular, fontSize: 12, color: "#888e9e" }
                }]
            case "LIVE":
                return [{
                    text: `Note - Once unlocked, the membership will start immediately`, // TODO: may not start immediately
                    style: { fontFamily: AppFont.Regular, fontSize: 12, color: "#888e9e" }
                }]
            default:
                return [{
                    text: `Note - Once unlocked, benefits will reflect immediately`,
                    style: { fontFamily: AppFont.Regular, fontSize: 12, color: "#888e9e" }
                }]
        }
    }

    static _formatDate(dateStr: string | number | Date, tz: Timezone): string {
        if (_.isNil(dateStr)) return undefined
        return moment(dateStr).tz(tz).format("Do MMM, YYYY")
    }

    static _transformV2CouponFailResponse(error: any, isApp: boolean) {
        return {
            sections: [{
                viewType: "ERROR_STATE",
                title: " ",
                rightInfo: {
                    title: error.coupon ? "View T&C" : "",
                    action: {
                        actionType: "NAVIGATION",
                        url: VoucherUtil._getTCLink(isApp, error.coupon?.tnc.link)
                    },
                },
                subTitle: error.status?.message || VoucherUtil._getErrorMessage(error.status?.code)
            }],
            result: "ERROR_STATE"
        }
    }

    static _transformCouponPage(userContext: UserContext, data: any) {
        const items = [`- Vouchers unlock special benefits on cult.fit app and website.`]
        if (["tvos", "ios"].includes(userContext.sessionInfo?.osName?.toLowerCase())) {
            items.push(`- Vouchers for digital based products (cultpass HOME) are not supported on iOS.`)
            data.warningInfo = {
                text: "Vouchers for cultpass HOME are not available on iOS",
                icon: "/image/icons/referral/ios/info.png",
                textStyle: {
                    color: "statusRed",
                    variant: "ctaText"
                }
            }
        }
        items.push(`- These vouchers are available via select corporate partners or events.`)

        return {
            ...data,
            header: {
                ...data.header,
                rightInfo: {
                    action: {
                        actionType: "SHOW_OFFERS_TNC_MODAL",
                        title: "KNOW MORE",
                        meta: {
                            title: "Redeem Vouchers",
                            dataItems: items
                        },
                    },
                }
            }
        }
    }

    static _redeemGiftCouponPage() {
        return {
            howItWorksItemList: {
                title: "HOW TO REDEEM A GIFT CARD",
                items: [
                    {
                        icon: "/image/icons/coupons/howItWorks/input.png",
                        subTitle: "Enter gift card code"
                    },
                    {
                        icon: "/image/icons/coupons/howItWorks/review.png",
                        subTitle: "Review benefits and terms & condition carefully"
                    },
                    {
                        icon: "/image/icons/coupons/howItWorks/activate.png",
                        subTitle: "Confirm and redeem your gift card"
                    },
                    {
                        icon: "/image/icons/coupons/howItWorks/enjoy.png",
                        subTitle: "Use your gift card balance during payment"
                    }
                ]
            },
            header: {
                title: "REDEEM GIFT CARD",
                subTitle: ""
            },
            couponSearchBarProps: {
                placeHolderText: "Enter gift card code here",
                buttonText: "CHECK VALIDITY"
            }
        }
    }

    static _getSuccessAction(actions: any, userAgent: UserAgent) {
        const rActions: Action[] = []
        const pBAction = _.get(actions, "primaryButton")
        if (pBAction?.url) rActions.push(this._getAction(pBAction, userAgent))
        if (userAgent === "APP") {
            return rActions
        }
        const sBAction = _.get(actions, "secondaryButton")
        if (sBAction?.url) rActions.push(this._getAction(sBAction))
        return rActions
    }

    static _getAction(action: any, userAgent?: UserAgent) {
        const { text: title, url } = action
        return {
            actionType: "NAVIGATION",
            title,
            url: userAgent === "DESKTOP" ? VoucherUtil.getDesktopURL() : url
        } as Action
    }

    static _getClpDeepLink(vertical: CFVertical, userAgent: UserAgent, campaignId?: string) {
        if (userAgent === "APP") {
            switch (vertical) {
                case "CULT":
                    return "curefit://tabpage?pageId=cult&selectedTab=CultAtCenter"
                case "EAT":
                    return "curefit://eatfitclp"
                case "CARE":
                    if (campaignId && campaignId.startsWith("live_pt_in_referral")) {
                        return "curefit://tabpage?pageId=cult&selectedTab=LivePT"
                    }
                    return "curefit://tabpage?pageId=careclptab"
                case "LIVE_FIT":
                    return "curefit://tabpage?pageId=live"
            }
        }
        const env = this.isProd() ? "prod" : "stage"
        return {
            prod: {
                CULT: "https://cure.app.link/hrnGcTyz79",
                CARE: "https://cure.app.link/69TUeno619",
                EAT: "https://cure.app.link/T2uL2lc619",
                LIVE: "https://cure.app.link/CjUfey5519",
                MIND: "",
                FITNESS_FIRST: "",
                CULT_GEAR: "",
                CURE_FIT: "",
                GYM_FIT: "",
                LIVE_FIT: "https://cure.app.link/CjUfey5519"
            },
            stage: {
                CULT: "https://cure.test-app.link/YGzvcFwz79",
                CARE: "https://cure.test-app.link/ktSOfrp219",
                EAT: "https://cure.test-app.link/I5KyMLmF09",
                LIVE: "https://cure.test-app.link/HOtZi46v09",
                MIND: "",
                FITNESS_FIRST: "",
                CULT_GEAR: "",
                CURE_FIT: "",
                GYM_FIT: "",
                LIVE_FIT: "https://cure.test-app.link/HOtZi46v09"
            }
        }[env][vertical]
    }

    static throwTncActionError(errorFactory: ErrorFactory, couponCode: string): Error {
        return errorFactory.withCode(ErrorCodes.GENERIC_COUPON_ERROR, 400).withMeta({
            message: "This voucher can be activated on the cure.fit website only",
            errorMsg: "This voucher can be activated on the cure.fit website only",
            couponCode,
            modalHeight: 250,
        }).build()
    }
}
