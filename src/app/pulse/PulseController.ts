import * as express from "express"
import * as _ from "lodash"
import { controller, httpGet } from "inversify-express-utils"
import { Container, inject } from "inversify"
import AuthMiddleware from "../auth/AuthMiddleware"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import {
  IPulseService,
  HeartRangeZone,
  CULT_CLIENT_TYPES
} from "@curefit/cult-client"
import {
  PulseProduct,
  UserRemainingAccess,
  UserAccessLedger
} from "@curefit/cult-common"
import { ErrorFactory } from "@curefit/error-client"
import { CacheHelper } from "../util/CacheHelper"
import { UserContext } from "@curefit/userinfo-common"
import PulseViewBuilder from "./PulseViewBuilder"
import { ICultServiceOld as ICultService } from "@curefit/cult-client"
import {
  ICatalogueService,
  CATALOG_CLIENT_TYPES
} from "@curefit/catalog-client"
import { Session } from "@curefit/userinfo-common"
import { CultBooking } from "@curefit/cult-common"
import {
  HERCULES_CLIENT_TYPES,
  IHerculesService
} from "@curefit/hercules-client"
import { IFitcashService, FITCASH_CLIENT_TYPES } from "@curefit/fitcash-client"
import { BodyPart } from "@curefit/fitness-common"
import AppUtil from "../../app/util/AppUtil"
import * as momentTz from "moment-timezone"
import { WalletBalance } from "@curefit/fitcash-common"
import { ErrorCodes } from "../error/ErrorCodes"
import { CdnUtil } from "@curefit/util-common"

function _keys<T extends object>(obj: T): Array<keyof T> {
  return Object.keys(obj) as Array<keyof T>
}

const zoneDisplayNames: {
  [key in HeartRangeZone]: string;
} = {
  normal: "Normal Zone",
  recovery: "Low Intensity",
  aerobic: "Medium Intensity",
  endurance: "High Intensity",
  anaerobic: "Max Intensity"
}

export function controllerFactory(kernel: Container) {
  @controller(
    "/pulse",
    kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession
  )
  class PulseController {
    constructor(
      @inject(CULT_CLIENT_TYPES.PulseService)
      private pulseService: IPulseService,
      @inject(CULT_CLIENT_TYPES.CultFitService)
      private cultFitService: ICultService,
      @inject(HERCULES_CLIENT_TYPES.IHerculesService)
      private herculeService: IHerculesService,
      @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader)
      private catalogueService: ICatalogueService,
      @inject(FITCASH_CLIENT_TYPES.FitcashService)
      private fitCashService: IFitcashService,
      @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
      @inject(CUREFIT_API_TYPES.PulseViewBuilder)
      protected pulseViewBuilder: PulseViewBuilder,
      @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
    ) {}

    @httpGet("/faq")
    public async getPulseFaqData(req: express.Request): Promise<any> {
      const userContext: UserContext = req.userContext as UserContext
      return await this.pulseViewBuilder.buildPulseFAQ(userContext)
    }

    @httpGet("/kit-ledger")
    public async getPulseRentalKitLedger(req: express.Request): Promise<any> {
      const userContext: UserContext = req.userContext as UserContext
      const session: Session = req.session as Session
      const userId: string = session.userId as string
      const [totalKitBalance, kitPurchaseHistory] = await Promise.all([
        this.pulseService.getUserAccess(userId),
        this.pulseService.getUserAccessLedger(userId)
      ])
      return await this.pulseViewBuilder.buildPulseKitHistory(
        totalKitBalance as UserRemainingAccess,
        kitPurchaseHistory as UserAccessLedger,
        userContext as UserContext
      )
    }

    @httpGet("/packs")
    public async getPulseRentalPacks(req: express.Request): Promise<any> {
      const userContext: UserContext = req.userContext as UserContext
      const session: Session = req.session as Session
      const userId = session.userId as string
      const cultClassId = req.query.classId as string

      let showUnlimitedPack = false

      if (!_.isEmpty(cultClassId)) {
        // newer app version
        // sending meta data
        // send unlimited pack
        showUnlimitedPack = true
      }

      const promises: Array<Promise<any>> = [
        this.catalogueService.getPulsePacks(true),
        this.fitCashService.balance(
          userId,
          userContext.userProfile.city.country.currencyCode
        )
      ]

      if (showUnlimitedPack) {
        promises.push(
          this.cultFitService.getCultClass(
            cultClassId,
            userId,
            "CUREFIT_APP",
            false,
            userContext.userProfile.subUserId,
            undefined,
            session.deviceId
          )
        )
      } else {
        promises.push(this.pulseService.getUserAccess(userId))
      }

      if (showUnlimitedPack) {
        const [pulseProducts, fitcashBalance, pulseClass] = await Promise.all(
          promises
        )
        if (
          _.isEmpty(pulseClass.PulseContext) ||
          _.isEmpty(pulseClass.PulseContext.UserAccess)
        ) {
          throw this.errorFactory.withCode(ErrorCodes.PULSE_ACCESS_INFO_NOT_FOUND, 400).build()
        }
        return await this.pulseViewBuilder.buildPulseRentalPackView(
          pulseProducts as Array<PulseProduct>,
          pulseClass.PulseContext.UserAccess,
          fitcashBalance as WalletBalance,
          userContext as UserContext,
          true
        )
      } else {
        const [pulseProducts, fitcashBalance, pulseAccess] = await Promise.all(
          promises
        )
        return await this.pulseViewBuilder.buildPulseRentalPackView(
          pulseProducts as Array<PulseProduct>,
          pulseAccess as UserRemainingAccess,
          fitcashBalance as WalletBalance,
          userContext as UserContext
        )
      }
    }

    @httpGet("/reports/")
    public async getPulseReportList(req: express.Request): Promise<any> {
      // extract user info
      const userContext = req.userContext as UserContext
      const session: Session = req.session as Session
      const userId = session.userId
      const pageNumber: any = 1
      const pageSize: any = 20
      const cultBulkBookings = await this.cultFitService.getPulseBookingsByPage(
        userId,
        pageNumber,
        pageSize
      )
      const cultBookings = cultBulkBookings[userId]
      return await this.pulseViewBuilder.buildPulseReportList(
        cultBookings.bookings,
        userContext
      )
    }

    @httpGet("/report/:bookingId")
    public async getPulseReport(req: express.Request): Promise<any> {
      // extract user info
      const userContext = req.userContext as UserContext
      const { osName, appVersion } = userContext.sessionInfo
      const tz = userContext.userProfile.timezone
      // get the response from pulse service
      // const [userData, res] = await Promise.all([
      //   userContext.userPromise,
      //   this.pulseService.getReport({ bookingId: req.params.bookingId})
      // ])
      const res = await this.pulseService.getReport({
        bookingId: req.params.bookingId
      })
      /**
       * If user did not have the booking
       * throw 4xx
       */
      if (res.userId !== userContext.userProfile.userId) {
        throw this.errorFactory.withCode(ErrorCodes.PULSE_USER_BOOKING_NOT_FOUND, 400).build()
      }
      /**
       * Do data processing
       * Convert into widget format
       */
      if (res.reportReady !== true) {
        throw this.errorFactory.withCode(ErrorCodes.PULSE_REPORT_NOT_READY, 400).build()
      }

      if (res.dataPresent !== true) {
        throw this.errorFactory.withCode(ErrorCodes.PULSE_REPORT_NOT_READY, 400).build()
      }

      const booking: CultBooking = await this.cultFitService.getBookingById(
        req.params.bookingId,
        res.userId
      )
      const wodId: string = booking.CultClass && booking.CultClass.wodId
      let bodyParts: BodyPart[] = []
      if (!_.isEmpty(wodId)) {
        bodyParts = await this.herculeService.getBodyPartsByWodId(wodId)
      }

      // filter the bodyparts if it has images
      // in curefit-content bucket
      const muscleGroupData: BodyPart[] = bodyParts
        .filter(
          bodyPart =>
            Array.isArray(bodyPart.media) &&
            bodyPart.media[0] &&
            !_.isEmpty(bodyPart.media[0].url) &&
            bodyPart.media[0].url.startsWith("/image")
        )
        .slice(0, 3)

      const shouldMuscleGroupBeDisplayed = muscleGroupData.length > 0 && AppUtil.isPulseMuscleGroupSupported(osName, appVersion)

      const userData = await this.userCache.getUser(res.userId)

      const userName = userData.firstName

      const zones: {
        [key in HeartRangeZone]: {
          label: string;
          color: string;
        };
      } = {
        normal: {
          label: zoneDisplayNames.normal,
          color: "rgb(222, 150, 255)"
        },
        recovery: {
          label: zoneDisplayNames.recovery,
          color: "rgb(71, 208, 255)"
        },
        aerobic: {
          label: zoneDisplayNames.aerobic,
          color: "rgb(158, 216, 100)"
        },
        endurance: {
          label: zoneDisplayNames.endurance,
          color: "rgb(255, 206, 103)"
        },
        anaerobic: {
          label: zoneDisplayNames.anaerobic,
          color: "rgb(255, 125, 127)"
        }
      }

      const colorMaps: {
        [key in HeartRangeZone]: string;
      } = {
        normal: "rgb(231, 182, 254)",
        recovery: "rgb(117, 220, 255)",
        aerobic: "rgb(184, 233, 134)",
        endurance: "rgb(255, 205, 100)",
        anaerobic: "rgb(255, 138, 140)"
      }

      const timeSpentInHighIntensityZone =
        res.pulseClassBookingReport &&
        res.pulseClassBookingReport.timeSpentInRightZoneMS
      const formattedTime = momentTz
        .utc(timeSpentInHighIntensityZone)
        .format("mm:ss")

      const highIntensityInfolet = {
        fancyTextHeader: "High\nIntensity+",
        data: `${formattedTime}`,
        units: "Min"
      }

      const calorieInfolet = {
        fancyTextHeader: "Calories\nBurnt",
        data: `${Math.round(
          res.pulseClassBookingReport &&
            res.pulseClassBookingReport.totalCalorie
        )}`,
        units: "KCal",
        headerColor: "#f1506e"
      }

      const classRankTimeSpentInZone =
        res.pulseClassBookingReport &&
        res.pulseClassBookingReport["timeSpentInRightZoneRank"]
      const classRankCalorieBurnt =
        res.pulseClassBookingReport && res.pulseClassBookingReport.calorieRank
      const totalUsers =
        res.pulseClassReport && res.pulseClassReport["totalUsers"]

      let classDurarion = 0
      if (res.classDate && res.classStartTime && res.classEndTime) {
        const startTimeStamp = momentTz
          .tz(`${res.classDate} ${res.classStartTime}`, tz)
          .toDate()
          .getTime()
        const endTimeStamp = momentTz
          .tz(`${res.classDate} ${res.classEndTime}`, tz)
          .toDate()
          .getTime()
        classDurarion = (endTimeStamp - startTimeStamp) / (60 * 1000)
      }

      // build share card widget
      const pulseSharedCardWidget = {
        widgetType: "PULSE_SHARE_CARD_WIDGET",
        header: {
          title: `Kudos${userName ? ` ${userName}` : ""}!`,
          backgroundImageUrl: "/image/pulse/report-bg-image.png"
        },
        pulse_metrics: [
          highIntensityInfolet,
          calorieInfolet,
        ],
        [shouldMuscleGroupBeDisplayed ? "muscle_groups" : undefined]: {
          title: "Muscle Groups",
          data: muscleGroupData.map(x => ({
            imageUri: x.media[0].url,
            caption: x.title
          }))
        },
        footers: [
          {
            text: `PULSE ${res.workoutName}`,
            bold: true
          },
          {
            text: res.classDate
              ? momentTz.tz(res.classDate, tz).format("ddd MMM D")
              : "",
            bold: false
          },
          {
            text: `${classDurarion} Min Workout`,
            bold: false
          }
        ],
        [shouldMuscleGroupBeDisplayed ? "share_button" : undefined]: {
          text: "SHARE YOUR ACHIEVEMENT",
          iconUri:
            CdnUtil.getCdnUrl("curefit-content/image/pulse/share-icon.png"),
          action: {
            actionType: "SHARE_SCREENSHOT",
            meta: {
              shareTitle: `PULSE Report`,
              shareMessage: `Just spent ${formattedTime} minutes in High-Intensity+ zone, and burnt ${calorieInfolet.data} Calories in PULSE - ${res.workoutName} at Cult! #BeatMyPulse`
            }
          }
        }
      }

      // if (muscleGroups.length === 3) {
      //   pulseSharedCardWidget["muscle_groups"] = {
      //     title: "Muscle Groups",
      //     data: muscleGroups.map(muscle => ({
      //       imageUri: muscle.media[0].url,
      //       caption: muscle.title
      //     }))
      //   }
      // }

      // build the high intensity widget
      const highIntensityWidget = {
        widgetType: "PULSE_HIGH_INTENSITY_ZONE_WIDGET",
        intensity_info: {
          ...highIntensityInfolet,
          headerColor: "#ffc431",
          big: true
        },
        auxiliary_data: [
          {
            header: "Better\nThan",
            dataText: `${Math.round(
              res.pulseClassBookingReport &&
                res.pulseClassBookingReport.timeSpentInRightZoneBetterThan
            )}%`,
            subText: "of attendees",
            vertical: true
          },
          {
            header: "Target\nTime",
            dataText: momentTz
              .utc(
                res.rightZoneTargetDurationMS && res.rightZoneTargetDurationMS
              )
              .format("mm:ss"),
            subText: "Min"
          },
          {
            header: "Class\nRank",
            dataText: classRankTimeSpentInZone.toString(),
            subText: `/${totalUsers}`
          }
        ]
      }

      const groupBarGraphWidget = {
        widgetType: "PULSE_GROUP_BAR_GRAPH_WIDGET",
        graph_data: {
          type: "group",
          title: "Minutes in Each Zone",
          legend: "Class Average",
          data: _keys(zones).map(zone => ({
            values: {
              user: Math.round(
                res.pulseClassBookingReport.timeSpentInZonesMS &&
                  (res.pulseClassBookingReport.timeSpentInZonesMS as any)[
                    zone
                  ] / 60000
              ),
              avg: Math.round(
                res.pulseClassReport.avgTimeSpentInZonesMS &&
                  (res.pulseClassReport.avgTimeSpentInZonesMS as any)[zone] /
                    60000
              )
            },
            label: zones[zone].label.toUpperCase(),
            colors: {
              user: zones[zone].color,
              avg: "rgb(195, 195, 195)"
            }
          }))
        }
      }

      const proTip1 = {
        widgetType: "PULSE_PRO_TIP_WIDGET",
        header: "PRO TIP",
        text:
          "Spend 30-40 minutes in High Intensity Zone every week for sustained health benefits"
      }

      const calorieBurnWidget = {
        widgetType: "PULSE_CALORIE_BURN_WIDGET",
        intensity_info: {
          ...calorieInfolet,
          headerColor: "#f1506e",
          big: true
        },
        auxiliary_data: [
          {
            header: "Better\nThan",
            dataText: `${Math.round(
              res.pulseClassBookingReport &&
                res.pulseClassBookingReport.calorieBetterThan
            )}%`,
            subText: "of attendees",
            vertical: true
          },
          {
            header: "Typical\nRange",
            dataText: "400-700", // needed
            subText: "KCal"
          },
          {
            header: "Class\nRank",
            dataText: `${classRankCalorieBurnt}`,
            subText: `/ ${totalUsers}`
          }
        ],
        tips: [
          {
            text:
              "Working out in High Intensity Zone helps you burn up to 15% more calories after workout"
          }
        ]
      }

      const pulseHeartRateWidget = {
        widgetType: "PULSE_HEART_RATE_WIDGET",
        auxiliary_data: [
          {
            header: "Your Avg.\nHeart Rate",
            dataText: `${res.pulseClassBookingReport.avgHR}`,
            subText: "BPM"
          },
          {
            header: "Expected\nRange",
            dataText: "120-150",
            subText: "BPM"
          }
        ]
      }

      const barGraphWidget = {
        widgetType: "PULSE_BAR_GRAPH_WIDGET",
        graph_data: {
          type: "normal",
          title: "Minute by Minute Avg. Heart Rate",
          colorStops:
            res.hrZoneRanges &&
            _keys(res.hrZoneRanges).map(zone => ({
              value: res.hrZoneRanges[zone].end,
              color: colorMaps[zone]
            })),
          data: res.pulseClassBookingReport.sixtyMinAvgHR
            .map((x, i) => ({
              x: i,
              y: x
            }))
            .slice(0, 60)
        }
      }

      const proTip2 = {
        widgetType: "PULSE_PRO_TIP_WIDGET",
        header: "PRO TIP",
        text:
          "By increasing your Avg heart rate by 10 bpm, you can burn 20% more calories during workouts"
      }
      return {
        widgets: [
          pulseSharedCardWidget,
          highIntensityWidget,
          groupBarGraphWidget,
          proTip1,
          calorieBurnWidget,
          pulseHeartRateWidget,
          barGraphWidget,
          proTip2
        ]
      }
    }
  }
  return PulseController
}

export default controllerFactory
