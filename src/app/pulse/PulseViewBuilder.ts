import { inject, injectable } from "inversify"
import * as _ from "lodash"
import * as momentTz from "moment-timezone"
import {
  CultBooking,
  PulseProduct,
  UserRemainingAccess,
  UserAccess<PERSON>edger,
  UserAccessTransaction
} from "@curefit/cult-common"
import AppUtil from "../util/AppUtil"
import { SupportEmptyListingWidget, HeaderWidget } from "../page/PageWidgets"
import { CdnUtil, TimeUtil } from "@curefit/util-common"
import { ActionUtil } from "@curefit/base-utils"
import { UserContext } from "@curefit/userinfo-common"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { User } from "@curefit/user-common"
import { CacheHelper } from "../util/CacheHelper"
import {
  PulseBenefitWidget,
  ProductListWidget,
  PulseTermsAndConditionsWidget,
  PromptUseFitcash
} from "../common/views/WidgetView"
import { WalletBalance } from "@curefit/fitcash-common"
import CultUtil from "../util/CultUtil"
import { PulseOptOutWidget } from "@curefit/apps-common"
import { ICultBusiness } from "../cult/CultBusiness"
const moment = require("moment")

export interface IBaseRentalPlan {
  name: string
  validity: string
  disabled: boolean
  productId: string | 0 // 0 when free plan is selected
  actionButtonText: string
}

export interface IFreeRentalPlan extends IBaseRentalPlan {
  displayPrice: string
  strikePrice?: string
}

export interface IPaidRentalPlan extends IBaseRentalPlan {
  unit: string
  displayPricePerClass: string
}

type IRentalPlan = IFreeRentalPlan | IPaidRentalPlan
// link to detail
const linkToDetailedTnC =
  "https://s3.ap-south-1.amazonaws.com/vm-html-pages/PULSE%20-%20T&C-f0207f4b-6d08-4264-a44e-48eb4dbaa12c.html"

function getTransactionTitle(transaction: UserAccessTransaction): string {
  if (transaction.type === "cr") {
    // credit can also be a refund
    // credit transaction can be either "bulk" or "trial"
    if (transaction.meta.accessType === "trial") {
      // trial top-up
      return "Cure.fit Rewards"
    } else if (transaction.meta.accessType === "bulk") {
      // bulk purchase
      if (!_.isEmpty(transaction.meta.totalAccess) && transaction.meta.totalAccess.match(/unlimited/i)) {
        return "Unlimited Pack Purchase"
      }
      return "Bulk Rental Purchase"
    } else if (transaction.meta.workoutName) {
      // refund
      return ["PULSE", transaction.meta.workoutName].join(" - ")
    }
  } else {
    // debit transaction against class booking
    return ["PULSE", transaction.meta.workoutName].join(" - ")
  }
}

function getTransactionSubtitle(
  transaction: UserAccessTransaction,
  userContext: UserContext
): string {
  if (transaction.type === "cr") {
    if (transaction.meta.orderId) {
      return `Order #${transaction.meta.orderId}`
    } else if (transaction.meta.accessType === "trial") {
      // trial
      return "PULSE Free Trials"
    }
  }
  // either a class was booked
  // or refunded on class cancellation
  const [hour, min, _] = transaction.meta.workoutStartTime.split(":")
  return [
    TimeUtil.formatDateInTimeZone(
      userContext.userProfile.timezone,
      new Date(transaction.meta.workoutDate),
      "DD MMM"
    ),
    `${hour}:${min}`
  ].join(" ")
}

function getTransactionAmount(transaction: UserAccessTransaction): string {
  if (transaction.type === "cr" && !_.isEmpty(transaction.meta.totalAccess)) {
    return transaction.meta.isUnlimitedPack
      ? "\u221E"
      : transaction.meta.totalAccess
  }
  return "1"
}

const getPulseFaqWidgets = _.memoize(() =>
  [
    {
      reason: "What is PULSE?",
      rebuttal:
        "PULSE uses heart-rate monitoring to take your usual CULT workouts to the next level. In PULSE classes, you can measure your heart rate & calorie burn in real-time and compete with other members in the class. After every workout, you also get a detailed workout summary on your app."
    },
    {
      reason: "What is PULSE kit?",
      rebuttal:
        "Pulse kit includes a wearable chest band and a heart rate monitor. You need a PULSE kit to attend a PULSE class"
    },
    {
      reason: "How do I get my PULSE kit?",
      rebuttal:
        "When booking a PULSE class, you make the payment to rent the PULSE kit. You will get your kit number on the booking confirmation. You can collect your kit at the center before your PULSE class and return the kit after class"
    },
    {
      reason: "Can I use fitcash to pay for my PULSE kit?",
      rebuttal: "Yes, you can use fitcash to pay and rent a PULSE kit."
    },
    {
      reason: "Can I pay for the rent at the center while collecting the kit?",
      rebuttal:
        "No, you have to pay at the time of booking and collect the kit at the center before the start of your PULSE class."
    },
    {
      reason: "What will happen if I cancel/fail-to-attend my class?",
      rebuttal:
        "In case of cancellation, no-show or any other failure to attend the PULSE class, your Pulse rental access will be credited back to your cure.fit account and you can use it to book the next PULSE classes."
    },
    {
      reason: "Why do I need to pay extra for the PULSE kit?",
      rebuttal:
        "PULSE kit helps us measure your workouts and take your Cult class experience to the next level.  With the use of Heart Rate Monitoring technology, PULSE classes support realtime workout-tracking, in-class competition, and a detailed workout report. PULSE kit rent is a pocket-friendly way in which you can help us cover the cost of bringing this enhanced experience to you."
    },
    {
      reason: "Is it mandatory to use a PULSE kit in the PULSE class?",
      rebuttal:
        "Yes. Pulse classes are all about real-time tracking and in-class competition. To ensure the best class experience, renting a PULSE kit is mandatory for all."
    },
    {
      reason: "Can I use my smartwatch in PULSE classes?",
      rebuttal:
        "Not yet. But we are working on supporting external devices in PULSE classes."
    },
    {
      reason: "I can’t find the answer to my question?",
      rebuttal:
        "Kindly <NAME_EMAIL>, and we would be happy to address your query"
    }
  ].map(({ reason, rebuttal }) => ({
    widgetType: "CANCEL_OPTION_WIDGET",
    reason,
    rebuttal,
    reasonFontSize: 14,
    reasonFontColor: "rgba(0, 0, 0, 1.0)",
    rebuttalFontSize: 12
  }))
)

const getPulsePackName = (pack: PulseProduct) => {
  const { subTitle } = pack
  if (!_.isEmpty(subTitle)) {
    return subTitle
      .split(" ")
      .map(x => `${x.charAt(0).toUpperCase()}${x.substring(1).toLowerCase()}`)
      .join(" ")
  }
}

const getPulsePackValidity = (pack: PulseProduct) => {
  const { validity, validityUnit } = pack
  if (!_.isEmpty(validity) && validity.match(/unlimited/i)) {
    return validity
      .split("")
      .map((character, index) =>
        index === 0 ? character.toUpperCase() : character.toLowerCase()
      )
      .join("")
  }

  return [validity, validityUnit].join(" ")
}

const shouldDisplayPerClassBreakDown = (pack: PulseProduct) => {
  const { totalAccess } = pack
  return !(
    totalAccess === "1" || CultUtil.isUnlimitedPulseAccessPack(pack)
  )
}

@injectable()
class PulseViewBuilder {
  constructor(
    @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
    @inject(CUREFIT_API_TYPES.CultBusiness) protected cultBusiness: ICultBusiness
  ) { }

  public async buildPulseKitHistory(
    totalKitBalance: UserRemainingAccess,
    kitPurchaseHistory: UserAccessLedger,
    userContext: UserContext
  ) {
    const widgets = []
    const topWidget = {
      widgetType: "FITCASH_SUMMARY_WIDGET",
      title: "REMAINING",
      balance: totalKitBalance.isUnlimitedPack
        ? "Unlimited"
        : totalKitBalance.remainingAccess,
      subtitle: totalKitBalance.isUnlimitedPack
        ? `Expires ${moment(totalKitBalance.validity).format("Do MMM")}`
        : ""
    }
    widgets.push(topWidget)
    if (kitPurchaseHistory.accessTransactions.length > 0) {
      // there are transactions
      const transactionWidgets: Array<any> = kitPurchaseHistory.accessTransactions.map(
        transaction => ({
          iconUrl:
            transaction.type === "dr"
              ? "/image/pulse/device-icon.png"
              : "/image/pulse/device2.png",
          title: getTransactionTitle(transaction),
          subTitle: getTransactionSubtitle(transaction, userContext),
          amount: getTransactionAmount(transaction),
          credited: transaction.type === "cr",
          date: TimeUtil.formatDateInTimeZone(
            userContext.userProfile.timezone,
            new Date(transaction.date),
            "DD MMM"
          )
        })
      )
      widgets.push(
        new ProductListWidget(
          "CREDIT",
          {
            title: "PULSE Rental Kit Usage History",
            color: "rgba(0, 0, 0, 0)" // hide it
          },
          transactionWidgets
        )
      )
    }
    return {
      faqLink: linkToDetailedTnC,
      widgets
    }
  }

  public async buildPulseRentalPackView(
    pulseProducts: Array<PulseProduct>,
    pulseUserAccess: UserRemainingAccess,
    fitCashBalance: WalletBalance,
    userContext: UserContext,
    showUnlimitedPack?: boolean
  ): Promise<any> {
    let action
    let currencyTicker: string
    switch (userContext.userProfile.city.country.currencyCode) {
      case "INR":
        currencyTicker = "₹"
        break
      default:
        currencyTicker = ""
    }
    const { appVersion, cpVersion, osName } = userContext.sessionInfo
    switch (pulseUserAccess.type) {
      case "trial":
        // member, has trial
        // book class without paying anything
        action = {
          actionType: AppUtil.isPulseTrialOptOutSupported(
            osName,
            appVersion,
            cpVersion
          ) ? "CULT_BUY_PULSE_RENTAL_PACK_AND_BOOK_CLASS" : "CULT_BOOK_CLASS",
          title: "Book"
        }
        break
      case "bulk":
        // member should not come here
        // if they already have bulk pack
        break
      case "no_access":
        // member has to be taken to check out screen
        // to buy some bulk pack
        action = {
          actionType: "CULT_BUY_PULSE_RENTAL_PACK_AND_BOOK_CLASS",
          title: "Book"
        }
        break
      default:
      // error
    }
    const sortedRentalPlans = pulseProducts
      .filter(x => x.active)
      .filter(x =>
        showUnlimitedPack
          ? true // show all packs
          : !(CultUtil.isUnlimitedPulseAccessPack(x)) // exclude unlimited pack
      )
      .sort((a, b) => b.totalAccessCount - a.totalAccessCount)
    const rentalPlans: Array<IRentalPlan> = [
      pulseUserAccess.remainingAccess > 0 &&
      pulseUserAccess.type === "trial" && {
        name: "Free Rental",
        validity: `(${pulseUserAccess.remainingAccess} of 2) free trial${
          pulseUserAccess.remainingAccess > 0 ? "s" : ""
          } available`,
        displayPrice: "FREE",
        disabled: false,
        productId: 0,
        actionButtonText: `Pay ${currencyTicker}0 and Book`
      },
      ...sortedRentalPlans.map(x => ({
        name: getPulsePackName(x),
        validity: `Validity: ${getPulsePackValidity(x)}`,
        displayPrice: `${
          !shouldDisplayPerClassBreakDown(x)
            ? `${currencyTicker}${Math.round(x.price.listingPrice)}`
            : ""
          }`,
        strikePrice: `${
          !shouldDisplayPerClassBreakDown(x) && CultUtil.isUnlimitedPulseAccessPack(x)
            ? `${currencyTicker}${Math.round(x.price.mrp)}`
            : ""
          }`,
        [shouldDisplayPerClassBreakDown(x) ? "unit" : undefined]: "/class",
        [shouldDisplayPerClassBreakDown(x)
          ? "displayPricePerClass"
          : undefined]: `${currencyTicker}${Math.floor(
            x.price.listingPrice / x.totalAccessCount
          )}`,
        [shouldDisplayPerClassBreakDown(x)
          ? "displayTotalPrice"
          : undefined]: `Total: ${currencyTicker}${Math.round(
            x.price.listingPrice
          )}`,
        disabled: pulseUserAccess.remainingAccess > 0,
        productId: x.productId,
        actionButtonText: `Pay ${currencyTicker}${Math.round(
          x.price.listingPrice
        )} and Book`
      }))
    ]

    const widgets = [
      {
        ...new HeaderWidget({
          title: "RENT PULSE KIT",
          subTitle: "Select a rental plan and continue to book"
        }),
        headerStyle: {
          fontSize: 18,
          color: "rgba(0, 0, 0, 1.0)"
        },
        subHeaderStyle: {
          fontSize: 14,
          color: "rgba(102, 102, 102, 1.0)"
        },
        hasTopPadding: true
      },
      {
        widgetType: "PULSE_RENTAL_PLAN_SELECTION_WIDGET",
        selectedIndex: rentalPlans.filter(x => Boolean(x))[0].productId,
        rentalPlans: rentalPlans.filter(x => Boolean(x))
      },
      AppUtil.isPulseOptOutSupported(
        userContext.sessionInfo.osName,
        userContext.sessionInfo.appVersion
      ) && {
        ...new PulseOptOutWidget(
          "Opt Out",
          "You will miss out on live tracking & detailed report",
          {
            actionType: "SELECT_PULSE_OPT_OUT",
            type: "SELECT_PULSE_OPT_OUT",
            meta: {
              actionButtonText: "Book"
            }
          }
        )
      },
      fitCashBalance.balance &&
      new PromptUseFitcash(
        "You can pay with FitCash",
        Math.round(fitCashBalance.balance / 100)
      )
    ].filter(x => Boolean(x))
    return {
      actions: [
        {
          ...action
        }
      ],
      widgets
    }
  }

  public async buildPulseFAQ(userContext: UserContext) {
    const widgets = [
      AppUtil.isPulseRentalVideoSupported(userContext)
        ? {
          widgetType: "PULSE_INFO_VIDEO_WIDGET",
          thumbnail: "/image/pulse/pulsevideo3.png",
          videoUri:
            CdnUtil.getCdnUrl("curefit-content/video/pulse/CultFit_Pulse_686_360_2.mp4"),
          description:
            "Workouts that use PULSE Kits to measure your performance and help achieve better results"
        }
        : {
          widgetType: "IMAGE_BANNER_WIDGET",
          imageUri:
            CdnUtil.getCdnUrl("curefit-content/image/pulse/find_your_pulse.png"),
          lowResImageUri:
            CdnUtil.getCdnUrl("curefit-content/image/pulse/find_your_pulse_low_res.png"),
          description:
            "Workouts that use PULSE Kits to measure your performance and help achieve better results"
        },
      {
        ...new PulseBenefitWidget("BENEFITS OF PULSE", [
          {
            iconUrl: "/image/pulse/pulsebenefit13.png",
            text: "Track heart-rate & calories burnt in real-time",
            width: 31,
            height: 32
          },
          {
            iconUrl: "/image/pulse/pulsebenefit23.png",
            text: "Compete, push more & achieve better results",
            width: 36,
            height: 32
          },
          {
            iconUrl: "/image/pulse/pulsebenefit33.png",
            text: "Get detailed workout summary & track progress",
            width: 28,
            height: 32
          }
        ]),
        showDivider: true
      },
      {
        ...new ProductListWidget(
          "SMALL",
          {
            title: "HOW IT WORKS",
            color: "#000000",
            titleFontSize: 15
          },
          [
            {
              subTitle: "Rent the PULSE Kit while booking or buy a Rental pack",
              icon: "/image/pulse/pulse_device1.png",
              subTitleFontSize: 14
            },
            {
              subTitle: "Get a Kit Number on the booking confirmation",
              icon: "/image/pulse/hash_info1.png",
              subTitleFontSize: 14
            },
            {
              subTitle:
                "Collect the Kit at the center before the class and join the workout",
              icon: "/image/pulse/pulse-wrap1.png",
              subTitleFontSize: 14
            },
            {
              subTitle: "Return the Kit in the drop bin after the class",
              icon: "/image/pulse/pulse-return1.png",
              subTitleFontSize: 14
            }
          ]
        ),
        dividerType: "LARGE",
        showDivider: true
      },
      {
        ...new PulseTermsAndConditionsWidget(
          "TERMS OF USAGE",
          "DETAILED T&C",
          "If you fail to return the Kit, your account will be blocked in 72 hrs. You will not be able to book any class until you return the Kit.",
          {
            actionType: "NAVIGATION",
            url: ActionUtil.webview(linkToDetailedTnC)
          }
        ),
        showDivider: true
      },
      {
        widgetType: "EXPANDABLE_LIST_WIDGET",
        title: "FAQs",
        titleFontSize: 15,
        showDivider: true,
        dividerType: "LARGE",
        allowMultipleOpen: false,
        hasTitleStyling: true,
        widgets: getPulseFaqWidgets()
      }
    ]
    return {
      widgets
    }
  }

  public async buildPulseReportList(
    pulseBookings: CultBooking[],
    userContext: UserContext
  ): Promise<any> {
    const timezone = userContext.userProfile.timezone
    const contextFullBookings = pulseBookings
      .filter(
        booking =>
          booking.Class &&
          booking.Class.PulseContext &&
          booking.Class.PulseContext.isActive &&
          booking.Class.PulseContext.isReportReady
      )
      .sort((a, b) => {
        // if same date, return by start Time
        if (a.Class.date === b.Class.date) {
          return momentTz
            .tz(a.Class.startTime, "hh:mm:ss", timezone)
            .isBefore(momentTz.tz(b.Class.startTime, "hh:mm:ss", timezone))
            ? 1
            : -1
        }
        return momentTz.utc(a.Class.date).isBefore(momentTz.utc(b.Class.date))
          ? 1
          : -1
      })
    let widgets = []
    const actions = []
    const userPromise = this.userCache.getUser(userContext.userProfile.userId)
    const user: User = await userPromise
    const isNewClassBookingSuppoted = AppUtil.isNewClassBookingSuppoted(
      userContext,
      user.isInternalUser
    )
    // const supportListPageView: ProductDetailPage = new ProductDetailPage()
    if (pulseBookings.length === 0) {
      // user has no pulse booking
      widgets.push(
        new SupportEmptyListingWidget(
          "CULT",
          "No Pulse Classes Yet!",
          "Attend a Pulse class and get a detailed report after every workout"
        )
      )
      actions.push({
        actionType: "NAVIGATION",
        url: ActionUtil.getBookCultClassUrl(
          "FITNESS",
          isNewClassBookingSuppoted,
          "support"
        ),
        title: "Book a Class"
      })
    } else {
      widgets = pulseBookings.map(pulseBooking => {
        const hour: string = momentTz
          .tz(pulseBooking.Class.startTime, "HH:mm:ss", timezone)
          .format("HH")
        const minute: string = momentTz
          .tz(pulseBooking.Class.startTime, "HH:mm:ss", timezone)
          .format("mm")
        const startTime: string = TimeUtil.getTimeIn12HRFormat(
          TimeUtil.formatDateInTimeZone(timezone, new Date()),
          +hour,
          +minute,
          true,
          timezone
        )
        const document = _.find(
          pulseBooking.Class.Workout.documents,
          document => {
            return document.tagID === 11
          }
        )
        return {
          widgetType: "SUPPORT_ACTIONABLE_CARD_WIDGET",
          title: `PULSE - ${pulseBooking.Class.Workout.name}`,
          subTitle: pulseBooking.Center.name,
          footer: [
            {
              text: `${momentTz
                .tz(pulseBooking.Class.date, timezone)
                .format("ddd, D MMM")}, ${startTime}`
            }
          ],
          cardAction: {
            actionType: "NAVIGATION",
            url: `curefit://pulsereport?bookingId=${pulseBooking.id}`,
            meta: {
              name: "Report"
            }
          },
          imageUrl: document ? "/" + document.URL : undefined
        }
      })
    }
    return {
      widgets,
      actions
    }
  }
}

export default PulseViewBuilder
