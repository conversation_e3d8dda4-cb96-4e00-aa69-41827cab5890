import { inject, injectable } from "inversify"
import { ITPOffersBusiness } from "./ITPOffersBusiness"
import { Logger, BASE_TYPES } from "@curefit/base"
import { UserOfferEligibilityType } from "@curefit/offer-common"
import { TPOfferEligibilityResponse } from "@curefit/reward-common"
import { ErrorFactory } from "@curefit/error-client"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import {
    REWARD_CLIENT_TYPES,
    ITPRewardService
} from "@curefit/reward-client"
import { TPRewardStatusResponse, ActivateTPRewardResponse } from "@curefit/reward-common"

@injectable()
class TPOffersBusiness implements ITPOffersBusiness {

    constructor(@inject(BASE_TYPES.ILogger) private logger: Logger,
                @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
                @inject(REWARD_CLIENT_TYPES.ITPRewardService) private rewardService: ITPRewardService
    ) {
    }

    async getOfferForUser(tpId: string, tpOfferId: string, eligibilityValue: string, eligibilityType: UserOfferEligibilityType): Promise<TPOfferEligibilityResponse> {
        return await this.rewardService.getOfferEligibilityForUser({
            tpId,
            tpOfferId,
            eligibilityType,
            eligibilityValue
        })
    }

    async addOfferForUser(tpId: string, tpOfferId: string, eligibilityValue: string, eligibilityType: UserOfferEligibilityType, count: number): Promise<TPOfferEligibilityResponse> {
        return await this.rewardService.addTPOfferForUser({
            tpId,
            tpOfferId,
            eligibilityType,
            eligibilityValue,
            count
        })
    }

    async removeOfferForUser(tpId: string, tpOfferId: string, eligibilityValue: string, eligibilityType: UserOfferEligibilityType, count: number): Promise<TPOfferEligibilityResponse> {
        return await this.rewardService.removeTPOfferForUser({
            tpId,
            tpOfferId,
            eligibilityType,
            eligibilityValue,
            count
        })
    }

    async activateRewardForUser(tpId: string, idempotenceKey: string, phone: string, email: string, productIds: string[], externalUserId: string): Promise<ActivateTPRewardResponse> {
        return await this.rewardService.activateRewardForTPUser({
            idempotenceId: idempotenceKey,
            phone,
            email,
            productIds,
            externalUserId,
            tpId
        })
    }

    async getRewardStatusForUser(tpId: string, phone: string, productId: string): Promise<TPRewardStatusResponse> {
        return await this.rewardService.getTPRewardStatusForUser({
            productId,
            phone,
            tpId
        })
    }
}

export default TPOffersBusiness
