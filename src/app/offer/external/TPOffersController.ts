import { Container, inject } from "inversify"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import AuthMiddleware from "../../auth/AuthMiddleware"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import * as express from "express"
import { Logger, BASE_TYPES } from "@curefit/base"
import { ITPOffersBusiness } from "./ITPOffersBusiness"
import { IApiKeyService, BASE_UTILS_TYPES } from "@curefit/base-utils"
import { ErrorFactory } from "@curefit/error-client"
import { ActivateOfferResponse, CONSTELLO_CLIENT_TYPES, IConstelloService } from "@curefit/constello-client"
import { TPOfferResponse } from "@curefit/constello-common"
import { ErrorCodes } from "../../error/ErrorCodes"
import { TPRewardStatusResponse, ActivateTPRewardResponse } from "@curefit/reward-common"
import * as _ from "lodash"

function controllerFactory(kernel: Container) {
    @controller("/:tpId", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class TPOffersController {

        constructor(
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(CUREFIT_API_TYPES.TPOffersBusiness) private tpOffersBusiness: ITPOffersBusiness,
            @inject(BASE_UTILS_TYPES.ApiKeyService) private apiKeyService: IApiKeyService,
            @inject(CONSTELLO_CLIENT_TYPES.ConstelloService) private constelloService: IConstelloService,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        ) {
        }

        @httpPost("/bundles/activate")
        public async activateRewardForUserByPhone(request: express.Request, res: express.Response): Promise<ActivateTPRewardResponse> {
            const tpId = request.params.tpId
            const {idempotenceId, phone, email, productIds, externalUserId} = request.body
            const idempotenceKey = idempotenceId
            const apiKey = this.apiKeyService.getApiKey(request.headers["authorization"]?.split(" ")?.[1]?.trim())
            if (!idempotenceId) {
                return {
                    idempotenceId,
                    status: "failure",
                    message: "IdempotenceId missing",
                    activationTime: null,
                    products: null
                }
            }
            if (!phone || phone.length < 10) {
                return {
                    idempotenceId,
                    status: "failure",
                    message: "Mobile no. invalid",
                    activationTime: null,
                    products: null
                }
            }
            if (!productIds || !_.isArray(productIds)) {
                return {
                    idempotenceId,
                    status: "failure",
                    message: "productIds invalid",
                    activationTime: null,
                    products: null
                }
            }
            if ( apiKey?.partner != "FLIPKART" ) {
                return {
                    idempotenceId,
                    status: "failure",
                    message: "wrong authorization token",
                    activationTime: null,
                    products: null
                }
            }
            this.logger.info(`Offer activation request for thirdparty ${tpId} for user phone ${phone} products: ${JSON.stringify(productIds)}`)
            return this.tpOffersBusiness.activateRewardForUser(tpId.toString().toUpperCase(), idempotenceKey, phone, email, productIds, externalUserId)
        }

        @httpGet("/bundles/status/:phone/:productId")
        public async getRewardStatusForUserByPhone(request: express.Request, res: express.Response): Promise<TPRewardStatusResponse> {
            const {tpId, phone, productId} = request.params
            const apiKey = this.apiKeyService.getApiKey(request.headers["authorization"]?.split(" ")?.[1]?.trim())
            if (!phone || phone.length < 10 || !productId) {
                res.status(400).send({
                    statusCode: 400,
                    error: "Bad Request",
                    message: "Field(s) missing or in wrong format"
                })
                return
            }
            this.logger.info(`Status check for thirdparty ${tpId} for user phone ${phone} product: ${productId}`)
            return this.tpOffersBusiness.getRewardStatusForUser(tpId.toString().toUpperCase(), phone, productId)
        }

        @httpGet("/offer")
        public async getOfferForUserByPhone(request: express.Request): Promise<TPOfferResponse> {
            const tpId = request.params.tpId
            const tpOfferId = request.query.offerId
            const eligibilityValue = request.query.phone
            const eligibilityType = "PHONE"
            const apiKey = request.headers["apikey"] as string
            if (!this.apiKeyService.hasPartnerKeyWithActionPermission(tpId, apiKey, "READ_USER_OFFER_BY_PHONE")) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid api key").build()
            }
            this.logger.info(`Offer get request for eligibilityValue ${eligibilityValue} for offer ${tpOfferId} of tpid: ${tpId}`)
            return this.tpOffersBusiness.getOfferForUser(tpId, tpOfferId, eligibilityValue, eligibilityType)
        }

        @httpPost("/offer/phone/add")
        public async addOfferForUserByPhone(request: express.Request): Promise<TPOfferResponse> {
            const apiKey = request.headers["apikey"] as string
            const tpId = request.params.tpId

            const tpOfferId = request.body.offerId
            const eligibilityValue = request.body.phone
            const eligibilityType = "PHONE"
            const count = request.body.count ? request.body.count : 1
            if (!this.apiKeyService.hasPartnerKeyWithActionPermission(tpId, apiKey, "ADD_USER_OFFER_BY_PHONE")) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid api key").build()
            }
            this.logger.info(`Offer create/add request for eligibilityValue ${eligibilityValue} for offer ${tpOfferId} of tpid: ${tpId} with count ${count}`)
            return this.tpOffersBusiness.addOfferForUser(tpId, tpOfferId, eligibilityValue, eligibilityType, count)
        }


        @httpPost("/offer/phone/remove")
        public async removeOfferForUserByPhone(request: express.Request): Promise<TPOfferResponse> {
            const apiKey = request.headers["apikey"] as string
            const tpId = request.params.tpId
            const tpOfferId = request.body.offerId
            const eligibilityValue = request.body.phone
            const eligibilityType = "PHONE"
            const count = request.body.count ? request.body.count : 1
            if (!this.apiKeyService.hasPartnerKeyWithActionPermission(tpId, apiKey, "REMOVE_USER_OFFER_BY_PHONE")) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid api key").build()
            }
            this.logger.info(`Offer delete/remove request for eligibilityValue ${eligibilityValue} for offer ${tpOfferId} of tpid: ${tpId} with count ${count}`)
            return this.tpOffersBusiness.removeOfferForUser(tpId, tpOfferId, eligibilityValue, eligibilityType, count)
        }

        @httpPost("/offer/phone/add/:tpOfferId")
        public async addOfferForUserByPhoneWithTPOfferId(request: express.Request): Promise<ActivateOfferResponse> {
            const apiKey = request.headers["apikey"] as string
            const tpId = request.params.tpId
            const tpOfferId = request.params.tpOfferId
            this.logger.info("apiKey " + apiKey)
            if (!this.apiKeyService.hasPartnerKeyWithActionPermission(tpId, apiKey, "ADD_USER_OFFER_BY_PHONE")) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid api key").build()
            }
            this.logger.info(`Offer create/add request for userInfo ${request.body.userInfo}  with info ${request.body.info} for offer ${tpOfferId}`)
            return this.constelloService.activateOffer(tpOfferId, request.body.userInfo, request.body.info)
        }
    }
    return TPOffersController

}

export default controllerFactory
