import { TPOfferResponse } from "@curefit/constello-common"
import { UserOfferEligibilityType } from "@curefit/offer-common"
import { TPRewardStatusResponse, ActivateTPRewardResponse } from "@curefit/reward-common"
export interface ITPOffersBusiness {
    getOfferForUser(tpId: string, tpOfferId: string, eligibilityValue: string, eligibilityType: UserOfferEligibilityType): Promise<TPOfferResponse>
    addOfferForUser(tpId: string, tpOfferId: string, eligibilityValue: string, eligibilityType: UserOfferEligibilityType, count: number): Promise<TPOfferResponse>
    removeOfferForUser(tpId: string, tpOfferId: string, eligibilityValue: string, eligibilityType: UserOfferEligibilityType, count: number): Promise<TPOfferResponse>
    activateRewardForUser(tpId: string, idempotenceKey: string, phone: string, email: string, productIds: string[], externalUserId: string): Promise<ActivateTPRewardResponse>
    getRewardStatusForUser(tpId: string, phone: string, productId: string): Promise<TPRewardStatusResponse>
}
