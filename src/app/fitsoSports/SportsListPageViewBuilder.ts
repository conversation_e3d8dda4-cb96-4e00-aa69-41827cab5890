import { BaseBooking as <PERSON><PERSON><PERSON>Booking, <PERSON>Workout } from "@curefit/sports-api-common"
import { UserContext } from "@curefit/userinfo-common"
import { TimeUtil } from "@curefit/util-common"
import { ISegmentService } from "@curefit/vm-models"
import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import IssueBusiness from "../crm/IssueBusiness"
import { SupportListPageView } from "../crm/SupportListPageView"
import { IGymfitBusiness } from "../gymfit/GymfitBusiness"
import { PageWidget } from "../page/Page"
import { Status, SupportActionableCardWidget } from "../page/PageWidgets"
import { ActionUtil as AppActionUtil } from "../util/ActionUtil"
import AppUtil from "../util/AppUtil"
import PlayUtil from "../util/PlayUtil"
import { Workout } from "./model/Models"
import _ = require("lodash")

const PlayBookingStatus: { [key: string]: any; } = {
    WaitListed: {
        text: "WAITLISTED",
        colour: "#fec62e"
    },
    UPCOMING: {
        text: "UPCOMING",
        colour: "#000000"
    },
    ATTENDED: {
        text: "ATTENDED",
        colour: "#50d166"
    },
    Ongoing: {
        text: "ONGOING",
        colour: "#00B4df"
    },
    CANCELLED: {
        text: "CANCELLED",
        colour: "#e05343"
    },
    NO_SHOW: {
        text: "NO SHOW",
        colour: "#d1d1d1"
    },
    LATE_ENTRY: {
        text: "LATE ENTRY",
        colour: "#e05343"
    },
    MISSED: {
        text: "MISSED",
        colour: "#d1d1d1"
    }
}

@injectable()
export default class SportsListPageViewBuilder {

    constructor(
        @inject(CUREFIT_API_TYPES.GymfitBusiness) private gymfitBusiness: IGymfitBusiness,
        @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness,
        @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService,
    ) {}

    async buildSportsListPageViewBuilder(userContext: UserContext, playSessions: PlayBaseBooking[]): Promise<SupportListPageView> {
        const widgets: PageWidget[] = []
        if (!_.isEmpty(playSessions)) {
            for (const session of playSessions) {
                widgets.push(await this.getSportBookingCard(userContext, session, true))
            }
        }

        return new SupportListPageView(widgets, undefined)
    }

    async buildSportsListWidgetsBuilder(userContext: UserContext, playSessions: PlayBaseBooking[]): Promise<SupportActionableCardWidget[]> {
        const widgets: SupportActionableCardWidget[] = []
        if (!_.isEmpty(playSessions)) {
            for (const session of playSessions) {
                widgets.push(await this.getSportBookingCard(userContext, session, false))
            }
        }
        return widgets
    }

    private async getSportBookingCard(
        userContext: UserContext,
        session: PlayBaseBooking,
        isWhiteTheme: boolean
    ): Promise<SupportActionableCardWidget> {
        const tz = userContext.userProfile.timezone
        const reportIssueParams = this.issueBusiness.getPlayBookingIssueParams(session)
        return {
            widgetType: "SUPPORT_ACTIONABLE_CARD_WIDGET",
            title: session.workout.name ? session.workout.name : undefined,
            subTitle: session.center?.name ? session.center?.name : "Center closed",
            footer: [{
                text: `${TimeUtil.formatDateInTimeZone(tz, new Date(session.cultClass.startDateTimeUTC), "D MMM | h:mm A")}`,
            }],
            cardAction: {
                actionType: "NAVIGATION",
                url: AppActionUtil.getIssuesUrl(),
                meta: null,
            },
            status : this.getPlayBookingStatus(session),
            imageUrl: isWhiteTheme ? PlayUtil.getSupportWhitePageIcon(String(session.workout.id)) : SportsListPageViewBuilder.getSportImage(session.workout),
            time: AppUtil.isWeb(userContext) ? `${TimeUtil.formatDateInTimeZone(tz, new Date(session.cultClass.startDateTimeUTC), "D MMM | h:mm A")}` : undefined,
            timestamp: session.bookingStartTime,
            timezone: tz
        }
    }

    private getPlayBookingStatus(classBooking: PlayBaseBooking) {
        return (<Status>PlayBookingStatus[classBooking.bookingStatus])
    }

    private static getSportImage(workout: IWorkout): string {
        if (workout.sportMedia != null && workout.sportMedia.length > 0)
            return workout.sportMedia.at(0 ).mediaUrl
        else
            return workout.iconUrl ? workout.iconUrl : "image/<EMAIL>"
    }

}