import { MediaType } from "@curefit/gymfit-common/dist/src/Models"

export interface Center {
    id: number,
    name: string,
    locality: string,
    fullAddress1: string,
    latitude: number,
    longitude: number,
    city: string,
    mapUrl: string,
    hasSeasonalPool: boolean,
    hasMembership: boolean,
    hasTrial: boolean,
    logoImage?: string,
}

export interface Workout {
    id: number,
    sportId: number,
    name: string,
    iconUrl: string,
    image: string,
    sportBanner: string,
    hasMembership: boolean,
    hasTrial: boolean,
    logoImage?: string,
    sportMedia?: WorkoutMedia[]
}

export interface WorkoutMedia {
    type: MediaType,
    mediaUrl: string,
    thumbnailUrl?: string,
}

export interface SessionDetail {
    id: number,
    bookingId: string,
    startTime: string,
    endTime: string,
    startDateTimeUTC?: string,
    endDateTimeUTC?: string,
    workoutID: string,
    classType: string,
    membershipId?: number,
}

export enum BookingStatus {
    "CANCELLED" = "CANCELLED",
    "UPCOMING" = "UPCOMING",
    "NO_SHOW" = "NO_SHOW",
    "LATE_ENTRY" = "LATE_ENTRY",
    "ATTENDED" = "ATTENDED"
}