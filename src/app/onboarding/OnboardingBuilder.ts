import {
    BannerAction,
    OnboardingBanner,
    OnboardingTheme,
    OnboardingStoriesData,
} from "../page/OnboardingPageConfig"
import { CountryData } from "../user/UserController"
import { LoginPageTypesCompat } from "../util/AppUtil"

interface DefaultResponse {
    countryId?: string
    attributionSource?: string
    attributionEmail?: string
    defaultLogin: LoginPageTypesCompat
    socialLoginSupportedForChronicCare?: boolean
}

export interface OnboardingResponse extends DefaultResponse {
    countriesData?: CountryData[]
    banners: OnboardingBanner[]
    cityId?: string
    theme?: OnboardingTheme
    pageAction: BannerAction,
    shouldSkipLogin?: boolean
    onboardingStories?: OnboardingStoriesData
}

export interface UnknownLocationResponse extends DefaultResponse {
    countriesData: CountryData[]
    message: string
}

export interface SkipLoginResponse {
    shouldSkipLogin: boolean
}
