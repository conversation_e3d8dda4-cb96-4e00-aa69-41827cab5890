
import * as express from "express"
import { controller, httpGet } from "inversify-express-utils"
import { Container, inject } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { Logger, BASE_TYPES } from "@curefit/base"
import OnboardingPageConfig, { OnboardingData, OnboardingType } from "../page/OnboardingPageConfig"
import { ICFAPICityService } from "../city/ICFAPICityService"
import { OnboardingResponse, UnknownLocationResponse, SkipLoginResponse } from "./OnboardingBuilder"
import AppUtil, { LoginPageTypesCompat } from "../util/AppUtil"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { IUserService, USER_CLIENT_TYPES } from "@curefit/user-client"
import IAuthBusiness from "../auth/IAuthBusiness"
import { User, Tenant } from "@curefit/user-common"
import * as _ from "lodash"
import { LOCATION_TYPES, ICityService, ICountryService } from "@curefit/location-mongo"
import { CountryData } from "../user/UserController"

export function controllerFactory(kernel: Container) {
  @controller("/onboarding")
  class OnboardingController {
    constructor(
      @inject(BASE_TYPES.ILogger) private logger: Logger,
      @inject(CUREFIT_API_TYPES.OnboardingPageConfig) private pageConfig: OnboardingPageConfig,
      @inject(CUREFIT_API_TYPES.CFAPICityService) private CFAPICityService: ICFAPICityService,
      @inject(LOCATION_TYPES.CityService) private cityService: ICityService,
      @inject(LOCATION_TYPES.CountryService) private countryService: ICountryService,
      @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
      @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
      @inject(CUREFIT_API_TYPES.AuthBusiness) private authBusiness: IAuthBusiness,
    ) { }

    @httpGet("/skipLogin")
    async getSkipLogin(req: express.Request, res: express.Response): Promise<SkipLoginResponse> {
      const shouldSkipLogin = await this.getShouldSkipLogin(req)
      return { shouldSkipLogin: shouldSkipLogin }
    }

    @httpGet("/appLaunch")
    async getForAppLaunch(req: express.Request, res: express.Response): Promise<OnboardingResponse | UnknownLocationResponse> {
      const type: OnboardingType = "APP_LAUNCH"
      const dataByCityId = this.pageConfig.data.byType.get(type).byCityId
      const onboardingData = this.pageConfig.data.byType.get(type).onboardingData
      const onboardingTheme = onboardingData?.theme
      const isOnboardingThemeSupported = AppUtil.isNewOnboardingFlowSupported(req)
      const onboardingStories = onboardingData?.stories
      const countriesData = await this.getCountriesData(req)
      const cityAndCountry = await this.getCityAndCountry(req, res)
      const cityId = cityAndCountry.cityId
      const countryId = cityAndCountry.countryId
      const socialLoginSupportedForChronicCare = true
      if (!cityId) {
        this.logger.info(`unable to determine city id for ip: ${res.locals.ip}`)
        return {
          countriesData: countriesData,
          countryId: cityAndCountry.countryId,
          onboardingStories,
          theme: isOnboardingThemeSupported ? onboardingTheme : undefined,
          message: undefined,
          defaultLogin: await this.getDefaultLogin(req),
          socialLoginSupportedForChronicCare,
        }
      }
      let data: OnboardingData = dataByCityId && dataByCityId.get(cityId)
      if (!data) {
        data = dataByCityId && dataByCityId.get("default")
      }
      const shouldSkipLogin = await this.getShouldSkipLogin(req)
      const { banners, theme, pageAction } = data
      return {
        countriesData, banners,
        theme: isOnboardingThemeSupported ? onboardingTheme : theme,
        cityId, countryId, pageAction,
        shouldSkipLogin: shouldSkipLogin,
        onboardingStories,
        defaultLogin: await this.getDefaultLogin(req),
        socialLoginSupportedForChronicCare,
      }
    }

    async getDefaultLogin(req: express.Request): Promise<LoginPageTypesCompat> {
      return await AppUtil.getDefaultLogin(req, this.countryService, this.CFAPICityService)
    }

    async getShouldSkipLogin(req: express.Request): Promise<boolean> {
      return false
    }


    async getCountriesData(req: express.Request): Promise<CountryData[]> {
      const tenant: Tenant = AppUtil.getTenantFromReq(req)
      const countries = await this.countryService.listCountries(tenant)
      const countriesData: CountryData[] = _.map(countries, country => {
        return {
          countryId: country.countryId,
          name: country.name,
          countryCallingCode: country.countryCallingCode,
          phoneLoginSupported: country.phoneLoginSupported,
          flagImage: country.flagImage,
          phoneNumberMaxLength: country.phoneNumberMaxLength
        }
      })
      return countriesData
    }

    async getCityAndCountry(req: express.Request, res: express.Response): Promise<{ cityId: string, countryId: string }> {
      const cityId = req.headers["cityid"] as string
      if (cityId) {
        const city = this.cityService.getCityById(cityId)
        return { cityId: city.cityId, countryId: city.countryId }
      } else {
        const ip: string = res.locals.ip
        const tenant: Tenant = AppUtil.getTenantFromReq(req)
        const cityBasedOnIp = await this.CFAPICityService.getCityAndCountryByIp(tenant, ip)
        return {
          cityId: cityBasedOnIp.city ? cityBasedOnIp.city.cityId : undefined,
          countryId: cityBasedOnIp.country ? cityBasedOnIp.country.countryId : undefined
        }
      }
    }
    @httpGet("/goalPlan")
    async getForGoalPlan(req: express.Request, res: express.Response): Promise<OnboardingResponse> {
      const type: OnboardingType = "GOAL_PLAN"
      const dataByCityId = this.pageConfig.data.byType.get(type).byCityId
      const cityId = "default" // same irrespective of city
      const data: OnboardingData = dataByCityId && dataByCityId.get(cityId)
      if (!data) {
        throw new Error("no data found")
      }
      const { banners, theme, pageAction } = data
      return { banners, theme, pageAction, defaultLogin: await this.getDefaultLogin(req) }
    }

    @httpGet("/pulse")
    async getForPulse(req: express.Request, res: express.Response): Promise<OnboardingResponse> {
      // const type: OnboardingType = "PULSE_ONBOARDING"
      // const cityId: string = "default"
      // const dataByCityId = this.pageConfig.data.byType.get(type).byCityId
      // const data: OnboardingData = dataByCityId && dataByCityId.get(cityId)
      const data: OnboardingData = {
        banners: [
          {
            title: "Experience PULSE Workouts",
            subTitle: "Workout with PULSE wearable kits and measure your heart rate, calorie burn and workout effort.",
            image: "/image/onlineOnboarding/pulse_onboarding11.png",
            imageRatio: "364:308"
          },
          {
            title: "Collect Kit at Centre",
            subTitle: "Rent your Kit while booking and get a Kit Number. Pick it up at the center before the workout and return after class.",
            image: "/image/onlineOnboarding/pulse_onboarding222.png",
            imageRatio: "355:327"
          },
          {
            title: "Track Performance Live",
            subTitle: "Watch your workout stats in real time and push yourself to achieve the target.",
            image: "/image/onlineOnboarding/pulse_onboarding3.png",
            imageRatio: "355:327"
          },
          {
            title: "Get Comprehensive Reports",
            subTitle: "Get a comprehensive summary of your workout. Track your progress and share your achievements!",
            image: "/image/onlineOnboarding/pulse_onboarding44.png",
            imageRatio: "355:327"
          }
        ],
        theme: "dark",
        pageAction: {
          actionType: "CLOSE_ONBOARDING",
          title: "GOT IT"
        }
      }
      if (!data) {
        throw new Error("No data found for Pulse onboarding")
      }
      const { banners, theme, pageAction } = data
      return { banners, theme, pageAction, defaultLogin: await this.getDefaultLogin(req) }
    }
  }
  return OnboardingController
}
export default controllerFactory
