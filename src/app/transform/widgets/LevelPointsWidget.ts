import { Header } from "../../common/views/WidgetView"
import { CFServiceInterfaces } from "../../page/vm/ServiceInterfaces"
import { BaseWidget, IBaseWidget, UserContext } from "@curefit/vm-models"

export class LevelPointsWidget extends BaseWidget {
    header?: Header
    items?: LevelCard[]
    subCategoryCode?: string

    constructor() {
        super("LEVEL_POINTS_WIDGET")
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: any }): Promise<IBaseWidget> {
        return this
    }
}

export interface LevelCard {
    imageUrl?: string
    tagText?: string
    title: string
}