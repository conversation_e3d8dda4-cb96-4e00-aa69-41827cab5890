import * as _ from "lodash"
import {
    Action,
    BaseWidget,
    Header,
    IBaseWidget,
    UserContext
} from "@curefit/vm-models"
import { GiftCardUtil } from "../../referral/GiftCardUtil"
import { CodeState } from "@curefit/apps-common"
import { CardConfig, DynamicLink, MultiCardConfig } from "@curefit/constello-common"
import { TimeUtil } from "@curefit/util-common"
import { CFServiceInterfaces } from "../../page/vm/ServiceInterfaces"

export class ShareActionWidget extends BaseWidget {

    header?: Header
    title?: string
    showCopyButton?: boolean
    copyText?: string
    actionList?: ActionList[]
    campaignId?: string
    subCategoryCode?: string

    constructor() {
        super("SHARE_ACTION_WIDGET")
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: any }): Promise<IBaseWidget> {
        const userId: string = userContext.userProfile.userId
        const {countryCallingCode} = await userContext.userPromise
        const countryId = GiftCardUtil.getCountryFromPhone(countryCallingCode)
        const campaignId = this.campaignId
        if (_.isNil(campaignId)) {
            return null
        }
        const {data, success} = await interfaces.giftCardService.getOrCreateGiftCardsWithPolicy(
            userId,
            campaignId,
            countryId
        )
        if (!success) {
            return null
        }
        this.header = {
            title: "REFER NOW VIA",
            titleAlignment: "CENTER",
        }
        const isAndroid: boolean = userContext.sessionInfo.osName === "android"
        const policy = data.policyEntity.policy
        const uiConfig = policy.uiConfig
        const tz = userContext.userProfile.timezone
        const expiryDate = policy.endDate
        const expirationTime = TimeUtil.getEpochFromDate(expiryDate) - TimeUtil.getEpochFromDate(TimeUtil.getDateNow(tz))
        const couponsHaveExpired = expirationTime <= 0

        data.summary.cards.forEach((card: { code: string, consumedCount: number, dynamicLink?: DynamicLink }) => {
            const consumed = this.isUsedCompletely(card.consumedCount, policy.cardConfig)
            const state = consumed ? CodeState.CONSUMED : couponsHaveExpired ? CodeState.EXPIRED : CodeState.ACTIVE
            const whatsAppMessageTags: any = {
                code: card.code
            }

            if (policy.cardConfig.generateLink) {
                if (card && card.dynamicLink) {
                    whatsAppMessageTags.dll = card.dynamicLink.url
                }
            }
            this.title = whatsAppMessageTags?.dll
            const whatsAppMessage: string = GiftCardUtil.getWhatsappMessage(uiConfig.whatsApp.message, whatsAppMessageTags)
            this.copyText = whatsAppMessage
            const actionList: ActionList[] = []
            actionList.push({
                title: "WHATSAPP",
                imageUrl: "image/transform/whatsapp_share.png",
                action: {
                    actionType: "OPEN_WEBPAGE",
                    url: "https://api.whatsapp.com/send?text=" + encodeURIComponent(whatsAppMessage)
                }
            })
            actionList.push({
                title: "OTHERS",
                imageUrl: "image/transform/others_share.png",
                action: {
                    actionType: "SHARE_ACTION",
                    title: GiftCardUtil.getCouponTitle(state, policy.cardConfig),
                    meta: {
                        shareOptions: {
                            url: uiConfig.whatsApp.image,
                            type: "image/png",
                            message: whatsAppMessage,
                            title: "Curefit Referrals",
                            whatsAppNumber: isAndroid ? "" : undefined
                        },
                        shareChannel: isAndroid ? "WHATSAPP" : undefined
                    }
                }
            })
            this.actionList = actionList
        })

        this.showCopyButton = true
        return this
    }

    private isUsedCompletely(consumptionCount: number, cardConfig: CardConfig): boolean {
        switch (cardConfig.cardType) {
            case "MULTI":
                return consumptionCount == (cardConfig as MultiCardConfig).maxConsumptions
            case "SINGLE":
                return consumptionCount == 1
        }
    }
}

export interface ActionList {
    title?: string,
    imageUrl?: string
    action?: Action
}