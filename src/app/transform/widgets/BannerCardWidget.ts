import { Action, BaseWidget, IBaseWidget, UserContext } from "@curefit/vm-models"
import * as _ from "lodash"
import { CFServiceInterfaces } from "../../page/vm/ServiceInterfaces"
import { ActionV2 } from "@curefit/vm-common"

export class BannerCardWidget extends BaseWidget {
    title?: string
    subTitle?: string
    description?: string
    imageUrl?: string
    bgImageUrl?: string
    lottieUrl?: string
    campaignId?: string
    action?: Action
    actionV2?: ActionV2

    constructor() {
        super("BANNER_CARD_WIDGET")
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: any }): Promise<IBaseWidget> {
        if (!_.isEmpty(this.actionV2)) {
            const vmAction = await interfaces.userActionMappingBusiness.getVMActionFromActionV2(this.actionV2, interfaces, userContext)
            if (!_.isEmpty(vmAction)) {
                this.action = vmAction
            }
        }
        return this
    }
}