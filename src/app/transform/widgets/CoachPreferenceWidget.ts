import { IWidgetType, WidgetView } from "@curefit/apps-common"
import { UserContext } from "@curefit/vm-models"
import AppUtil from "../../util/AppUtil"

interface Option {
    title: string
    subtitle?: string
    id: string
}

interface CoachQuestion {
    title: String
    subtitle?: String
    id: String
    options: Option[]
    type?: String
}

export class CoachPreferenceWidget implements WidgetView {

    bookingId: string
    subCategoryCode: string
    questions: CoachQuestion[]
    defaultOptionId?: string

    constructor(bookingId: string, userContext: UserContext, subCategoryCode: string) {
        this.bookingId = bookingId
        this.defaultOptionId = "NONE"
        this.widgetType = "COACH_PREFERENCE_WIDGET"
        this.subCategoryCode = subCategoryCode
        const idealCoachQuestion: CoachQuestion = {
            id: "IDEAL_COACH_PREFERENCE",
            title: "How would you describe your ideal coach? Choose all that apply.",
            type: "MULTIPLE_SELECT",
            options: [
                {
                    title: "High-energy",
                    id: "HIGH_ENERGY"
                },
                {
                    title: "Knows when to give me tough love",
                    id: "TOUGH"
                },
                {
                    title: "Calm, cool and collected",
                    id: "CALM_COOL"
                },
                {
                    title: "Drill sergeant",
                    id: "HARD"
                },
                {
                    title: "Has a sense of humor",
                    id: "FUNNY"
                },
                {
                    title: "Analytical and results-driven",
                    id: "ANALYTIC"
                },
            ]
        }
        const paceQuestion: CoachQuestion = {
            id: "PACE_PREFERENCE",
            title: "What type of coaching do you respond best to?",
            options: [
                {
                    title: "I want to be pushed more",
                    subtitle: "I am ready to make big changes immediately",
                    id: "FAST"
                },
                {
                    title: "I want to take things at my own pace",
                    subtitle: "I want easy changes. Speed doesn't matter",
                    id: "NORMAL"
                },
            ]
        }
        const genderQuestion: CoachQuestion = {
            id: "GENDER",
            title: "Do you have a coach preference?",
            subtitle : "You might be assigned the next best-suited coach if a coach of your choice is not available. Your comfort is a priority to us, so please reach out in case you need a coach change.",
            options: [
                {
                    title: "No particular preference",
                    id: "NONE"
                },
                {
                    title: "Female",
                    id: "FEMALE"
                },
                {
                    title: "Male",
                    id: "MALE"
                }
            ]
        }
        // const goalQuestion: CoachQuestion = {
        //     id: "GOAL",
        //     title: "What is your goal?",
        //     options: [
        //         {
        //             title: "Lose Weight",
        //             id: "LOSE_WEIGHT"
        //         },
        //         {
        //             title: "Build Muscle",
        //             id: "BUILD_MUSCLE"
        //         },
        //         {
        //             title: "Stay Fit",
        //             id: "STAY_FIT"
        //         },
        //         {
        //             title: "Run a 5K",
        //             id: "RUN_5K"
        //         }
        //     ]
        // }
        if (AppUtil.isTransformMultiSelectQuestionSupported(userContext)) {
            this.questions = [idealCoachQuestion]
        } else if (AppUtil.isNewCoachPacePreferenceSupported(userContext)) {
            this.questions = [paceQuestion]
        } else {
            this.questions = [genderQuestion]
        }
      }
    widgetType: IWidgetType
}