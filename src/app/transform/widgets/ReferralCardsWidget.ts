import { Header } from "../../common/views/WidgetView"
import { CFServiceInterfaces } from "../../page/vm/ServiceInterfaces"
import { Action, BaseWidget, IBaseWidget, UserContext } from "@curefit/vm-models"
import { SortOrder } from "@curefit/mongo-utils"
import { MyReferral } from "../../referral/myreferrals/MyReferral"
import * as _ from "lodash"
import { TimeUtil } from "@curefit/util-common"
import { GiftCardUtil } from "../../referral/GiftCardUtil"
import { DynamicLink } from "@curefit/constello-common"

export class ReferralCardsWidget extends BaseWidget {
    header?: Header
    campaignId?: string
    items?: ReferralCard[]
    summaryCard?: SummaryCard
    errorMessage?: string
    subCategoryCode?: string

    constructor() {
        super("REFERRAL_CARDS_WIDGET")
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: User<PERSON>ontext, queryParams: { [filterName: string]: any }): Promise<IBaseWidget> {
        const userId = userContext.userProfile.userId
        const tz = userContext.userProfile.timezone
        const [giftCardResponse, referralResponse] = await Promise.all([
            interfaces.giftCardService.getAllConsumedCards(userId),
            interfaces.referralDao.find({
                condition: {userId},
                sortField: "createdDate",
                sortOrder: SortOrder.DESC
            })
        ])
        if (!giftCardResponse.success) {
            this.errorMessage = "Error fetching referrals"
            return this
        }
        const giftCardReferrals = await interfaces.giftCardTransformationService.transformReferralsResponse(giftCardResponse.data, tz)
        let referrals: MyReferral[] = giftCardReferrals
            .concat(await interfaces.referralTransformationService.transformReferralsResponse(referralResponse))
            .filter(_ => (new Date(_.activationDate).getTime() >= new Date("2019-07-01T00:00").getTime()))
        if (!_.isNil(this.campaignId)) {
            referrals = referrals.filter(_ => (_.policy?.couponConfig?.campaignId == this.campaignId))
        }

        const sortedReferrals = _.reverse(_.sortBy(referrals, o => new Date(o.activationDate)))
        if (_.isArray(sortedReferrals) && !_.isEmpty(sortedReferrals)) {
            let referralRedeemed: number = 0
            const items: ReferralCard[] = []

            const {countryCallingCode} = await userContext.userPromise
            const countryId = GiftCardUtil.getCountryFromPhone(countryCallingCode)
            const {data, success} = await interfaces.giftCardService.getOrCreateGiftCardsWithPolicy(
                userId,
                this.campaignId,
                countryId
            )
            if (!success) {
                this.errorMessage = "Error fetching referrals"
                return this
            }
            const policy = data.policyEntity.policy
            const uiConfig = policy.uiConfig
            let whatsAppMessage: string = ""

            data.summary.cards.forEach((card: { code: string, consumedCount: number, dynamicLink?: DynamicLink }) => {
                const whatsAppMessageTags: any = {
                    code: card.code
                }
                if (policy.cardConfig.generateLink) {
                    if (card && card.dynamicLink) {
                        whatsAppMessageTags.dll = card.dynamicLink.url
                    }
                }
                whatsAppMessage = GiftCardUtil.getWhatsappMessage(uiConfig.whatsApp.message, whatsAppMessageTags)
            })

            sortedReferrals.forEach(myReferral => {
                if (myReferral.conversions.length) {
                    referralRedeemed = referralRedeemed + 1
                }
                // const whatsAppMessage: string = myReferral.policy?.uiConfig?.whatsApp?.message
                items.push({
                    title: myReferral.referee.name || myReferral.referee.phone,
                    imageUrl: myReferral.conversions.length ? "image/transform/redeemed.png" : "image/transform/not_redeemed.png",
                    subTitle: myReferral.conversions.length ? `Redeemed on ${TimeUtil.formatDateInTimeZone(tz, myReferral.conversions[0].date, "MMM DD, YYYY")}` : `Valid from ${TimeUtil.formatDateInTimeZone(tz, myReferral.activationDate, "MMM DD, YYYY")} - ${TimeUtil.formatDateInTimeZone(tz, myReferral.endDate, "MMM DD, YYYY")}`,
                    footerTitle: myReferral.conversions.length ? null : "No reward yet",
                    footerSubTitle: myReferral.conversions.length ? null : "Remind to redeem",
                    description: myReferral.conversions.length ? "YOU GOT ₹1000 CULTSPORT VOUCHER  🎉" : null,
                    action: myReferral.conversions.length && myReferral.referee.phone ? null : {
                        actionType: "OPEN_WEBPAGE",
                        url: "https://wa.me/" + myReferral.referee.phone.toString() + "?text=" + encodeURIComponent(whatsAppMessage)
                    }
                })
            })
            this.items = items
            if (referralRedeemed > 0) {
                const summaryItems: SummaryItem[] = []
                summaryItems.push({
                    title: "₹ " + (1000 * referralRedeemed).toString(),
                    subtitle: "VOUCHERS EARNED"
                })
                summaryItems.push({
                    title: referralRedeemed.toString(),
                    subtitle: "REFERRALS"
                })
                this.summaryCard = {
                    items: summaryItems,
                    imageUrl: "image/transform/trophy.png"
                }
            }
        } else {
            this.errorMessage = "No referral made"
        }
        return this
    }
}

export interface ReferralCard {
    imageUrl?: string
    title?: string
    subTitle?: string
    description?: string
    footerTitle?: string
    footerSubTitle?: string
    action?: Action
}

export interface SummaryCard {
    imageUrl?: string
    items?: SummaryItem[]
}

export interface SummaryItem {
    title?: string,
    subtitle?: string
}