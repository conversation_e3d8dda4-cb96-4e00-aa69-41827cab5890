import * as _ from "lodash"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import { Container, inject } from "inversify"
import AuthMiddleware from "../auth/AuthMiddleware"
import * as express from "express"
import { SignedUrlResponse } from "@curefit/user-client"
import { BASE_TYPES, Logger } from "@curefit/base"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import {
    IMediaGatewayService,
    MEDIA_GATEWAY_CLIENT_TYPES,
    MediaType,
    ObjectAcl
} from "@curefit/media-gateway-js-client"
import { TransformUtil } from "../util/TransformUtil"
import { Action, UserContext } from "@curefit/vm-models"
import { IQueueService, SQS_CLIENT_TYPES } from "@curefit/sqs-client"
import IUserBusiness from "../user/IUserBusiness"
import { Session } from "@curefit/userinfo-common"
import { UserDeliveryAddress } from "@curefit/eat-common"
import { ICrudKeyValue, REDIS_TYPES } from "@curefit/redis-utils"

export function controllerFactory(kernel: Container) {
    @controller("/transform",
        kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession
    )
    class TransformController {

        constructor(
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
            @inject(MEDIA_GATEWAY_CLIENT_TYPES.IMediaGatewayService) private mediaGatewayClient: IMediaGatewayService,
            @inject(SQS_CLIENT_TYPES.QueueService) private queueService: IQueueService,
            @inject(CUREFIT_API_TYPES.UserBusiness) private userBusiness: IUserBusiness,
            @inject(REDIS_TYPES.RedisDao) private crudDao: ICrudKeyValue,
        ) {
        }

        @httpGet("/s3/getSignedUrl")
        async getTransformImageSignedUrl(req: express.Request): Promise<SignedUrlResponse> {
            const fileName: string = req.query.fileName as string
            return await this.mediaGatewayClient.getPresignedPutUrl({
                bucketName: "curefit-transform",
                path: "",
                fileName,
                contentType: MediaType.IMAGE,
                maxUploadSize: 104857600, // need to be decided
                objectAcl: ObjectAcl.PUBLIC_READ
            })
        }

        @httpGet("/s3/getValidImageUrl")
        async get(req: express.Request): Promise<any> {
            const imageUrl: string = await this.mediaGatewayClient.validateAndGetDestinationUrl(req.query.imageUrl as string)
            return {url: imageUrl}
        }

        @httpPost("/pushVideoGuideMetric")
        async validatePhoneNumber(req: express.Request): Promise<Boolean> {
            const userId = req.session.userId
            const userContext: UserContext = req.userContext as UserContext

            const contentId: string = req.body.contentId
            const duration: number = req.body.duration

            if (_.isEmpty(contentId)) {
                await this.logger.info(`could not submit data to activity Store Queue for userId: ${userId} as contentId came as null`)
                return false
            }

            return await TransformUtil.pushVideoGuideActivity(userContext, contentId, duration, this.queueService)
        }

        @httpGet("/addressOption")
        async getAddressOption(req: express.Request): Promise<any> {
            const session: Session = req.session
            const verticalQuery = req.query.verticalQuery
            const userContext = req.userContext as UserContext
            const pageTitle: string = "Select billing address"
            const addressesData: any = []
            let selectedAddressId: string = ""
            let errorMessage: string = ""
            const addresses: UserDeliveryAddress[] = await this.userBusiness.getAddresses(session.userId, verticalQuery)
            if (addresses && addresses.length > 0) {
                addresses.forEach(address => {
                    addressesData.push({
                        "id": address.addressId,
                        "title": address.addressType.toString(),
                        "subtitle": address.addressLine1 + " " + address.addressLine2
                    })
                })
                selectedAddressId = addressesData[0]["id"]
            } else {
                errorMessage = "No Address Added"
            }
            const addAction: Action = {
                actionType: "NAVIGATION",
                title: "+ ADD NEW ADDRESS",
                url: "curefit://tf_address_form?vertical=TRANSFORM"
            }
            const action: Action = {
                actionType: "EMPTY_ACTION",
                title: "CONTINUE",
            }
            return {
                pageTitle: pageTitle,
                selectedAddressId: selectedAddressId,
                addressesData: addressesData,
                addAction: addAction,
                errorMessage: errorMessage,
                action: action
            }
        }

        @httpGet("/addressForm")
        async getAddressForm(req: express.Request): Promise<any> {
            const pageTitle: string = "Enter your address"
            const formFields: any = []
            const nameField: any = {
                id: "name",
                enabled: true,
                expanded: true,
                placeholder: "Full Name*",
                mandatory: true,
                errorMessage: "Enter Valid Full Name",
                type: "TEXT",
            }
            const phoneField: any = {
                id: "phoneNumber",
                enabled: true,
                expanded: true,
                placeholder: "Phone Number*",
                mandatory: true,
                errorMessage: "Enter Valid Phone Number",
                type: "NUMBER",
            }
            const address1Field: any = {
                id: "addressLine1",
                enabled: true,
                expanded: true,
                placeholder: "House no, Building name*",
                mandatory: true,
                errorMessage: "Enter Valid Address",
                type: "TEXT",
            }
            const address2Field: any = {
                id: "addressLine2",
                enabled: true,
                expanded: true,
                placeholder: "Road name, Area, Colony*",
                errorMessage: "Enter Valid Address",
                mandatory: true,
                type: "TEXT",
            }
            const pinCodeField: any = {
                id: "pincode",
                enabled: true,
                expanded: false,
                placeholder: "Pincode*",
                mandatory: true,
                errorMessage: "Enter Valid PinCode Number",
                nested: "structuredAddress",
                type: "NUMBER",
            }
            const cityField: any = {
                id: "city",
                otherId: "locality",
                enabled: true,
                expanded: false,
                placeholder: "City*",
                mandatory: true,
                errorMessage: "Enter Valid City Name",
                nested: "structuredAddress",
                type: "TEXT",
            }

            const stateField: any = {
                id: "state",
                enabled: true,
                expanded: true,
                placeholder: "State*",
                mandatory: true,
                errorMessage: "Enter Valid State Name",
                nested: "structuredAddress",
                type: "TEXT",
            }
            const countryField: any = {
                id: "country",
                enabled: true,
                expanded: true,
                placeholder: "Country*",
                mandatory: true,
                errorMessage: "Enter Valid Country Name",
                nested: "structuredAddress",
                type: "TEXT",
            }
            const addressType: any = {
                id: "addressType",
                enabled: true,
                expanded: true,
                placeholder: "SAVE ADDRESS AS:",
                mandatory: true,
                errorMessage: "Select Correct Address Type",
                values: ["HOME", "OFFICE", "OTHER"]
            }

            formFields.push(nameField)
            formFields.push(phoneField)
            formFields.push(address1Field)
            formFields.push(address2Field)
            formFields.push(pinCodeField)
            formFields.push(cityField)
            formFields.push(stateField)
            formFields.push(countryField)
            formFields.push(addressType)

            const action: Action = {
                actionType: "EMPTY_ACTION",
                title: "SAVE ADDRESS",
            }
            return {
                pageTitle: pageTitle,
                formFields: formFields,
                action: action
            }
        }

        @httpPost("/bootcampDismissToolTip")
        async dismissBootcampToolTip(req: express.Request): Promise<any> {
            const userId = req.session.userId
            const userContext: UserContext = req.userContext as UserContext
            const redisKey: string = "bootcamp_tooltip_status_" + userId
            const val = await this.crudDao.update(redisKey, `{"status":"CLOSED"}`)
            return {"response": val}
        }

        @httpPost("/bootcampImpressionCount")
        async increaseBootcampImpressionCount(req: express.Request): Promise<any> {
            const userId = req.session.userId
            const userContext: UserContext = req.userContext as UserContext
            const redisKey: string = "bootcamp_impression_count_" + userId
            let count = 1
            await this.crudDao.read(redisKey).then(async function (payload) {
                if (payload) {
                    const impressionCount = JSON.parse(payload)
                    count = parseInt(impressionCount["count"]) + 1
                }
            })
            const val = await this.crudDao.update(redisKey, `{"count":"${count}"}`)
            return {"response": val}
        }

        @httpGet("/referral")
        async getBootcampReferralView(req: express.Request): Promise<any> {
            const userId = req.session.userId
            const userContext: UserContext = req.userContext as UserContext
            const pageDataList: any[] = []
            pageDataList.push({
                "title": "INVITE A FRIEND",
                "pageId": "invite_friend"
            })
            pageDataList.push({
                "title": "ALL REFERRALS",
                "pageId": "all_referrals"
            })
            pageDataList.push({
                "title": "FAQs",
                "pageId": "bootcamp_referral_faq"
            })
            return {"pageDataList": pageDataList}
        }
    }


    return
    TransformController
}

export default controllerFactory
