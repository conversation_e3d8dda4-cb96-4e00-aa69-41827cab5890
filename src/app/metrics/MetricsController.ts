import * as express from "express"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import { Container, inject } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import AuthMiddleware from "../auth/AuthMiddleware"
import { Logger, BASE_TYPES } from "@curefit/base"
import { Session } from "@curefit/userinfo-common"
import { IAddMetric, ILatestMetric, IMetricServiceClient as IMetricService, METRIC_TYPES } from "@curefit/metrics"
import {
  AggregationRange,
  IAverageMetricData,
  IMetric,
  IMetricData,
  IMetricOptions,
  METRICS_CATEGORY_NAME,
  METRICS_SUBCATEGOTY_ID
} from "@curefit/metrics-common"
import * as _ from "lodash"
import * as momentTz from "moment-timezone"
import { Action, WidgetView } from "../common/views/WidgetView"
import { getCMFromFeet, getCMFromInches, getMeasurementOptions, MeasurementUnit } from "../util/UnitUtil"
import { AuthUtil } from "../util/AuthUtil"
import { calculateBMI } from "../util/MetricUtil"
import { getDateSortedGraphData, getYAxisRange } from "../util/GraphUtil"
import { UserContext } from "@curefit/userinfo-common"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { CacheHelper } from "../util/CacheHelper"

interface ILatestMetricViewModel {
  title: string
  data: Array<{ type: string, value: string, action: Action }>
}

interface IMetricGraphWidget extends WidgetView {
  graphData: Array<{ key: string, value: number }>
  graphType: AggregationRange
  fromDate: string
  toDate: string
  graphOptions: Array<{ type: AggregationRange, selected: boolean, displayText: string }>
  metric: IMetric,
  range: Array<number>
}

interface IMetricStatsWidget extends WidgetView {
  data: Array<{ key: string, value: string }>
}

interface IMetricTitleWidget extends WidgetView {
  title: string,
  value: string,
  target?: string
}

interface IAddMetricPayload {
  unit: MeasurementUnit
  metricId: number
  values: Array<{
    unit: MeasurementUnit
    value: number
  }>
}

interface IAddDeviceMetric {
  value: number,
  metricId: number
}

export function MetricControllerFactory(kernel: Container) {

  @controller("/metrics", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
  class MetricController {
    constructor(
      @inject(BASE_TYPES.ILogger) private logger: Logger,
      @inject(METRIC_TYPES.MetricServiceClient) private metricService: IMetricService,
      @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
    ) { }

    @httpGet("/latestMetrics")
    async getLatestMetrics(req: express.Request): Promise<Array<ILatestMetric>> {

      const session: Session = req.session
      const userId: string = session.userId

      const [availablePhysicalMetrics, physicalStats] = await Promise.all([
        this.metricService.getAvailableMetric(METRICS_CATEGORY_NAME.VITAL),
        this.metricService.getMetricsForCategory(userId, METRICS_CATEGORY_NAME.VITAL),
      ])

      const title = this.getCategoryTitle(availablePhysicalMetrics)

      // create the view model
      const response: ILatestMetricViewModel[] = []
      response.push({ title: this.getCategoryTitle(availablePhysicalMetrics), data: this.getStatsDataArray(availablePhysicalMetrics, physicalStats) })

      // const [availableFitnessMetrics, fitnessStats] = await Promise.all([
      //   this.metricService.getAvailableMetric(METRICS_CATEGORY_NAME.FITNESS),
      //   this.metricService.getMetricsForCategory(userId, METRICS_CATEGORY_NAME.FITNESS),
      // ])
      // response.push({ title: this.getCategoryTitle(availableFitnessMetrics), data: this.getStatsDataArray(availableFitnessMetrics, fitnessStats) })

      return response
    }

    getMetricUnit(unit: string): string {
      if (unit === "value" || unit === "score") {
        return ""
      }
      return unit
    }

    getCategoryTitle(metrics: Array<IMetric>): string {
      if (metrics.length > 0) {
        const metric = metrics[0]
        return metric.category.description
      }

      return ""
    }

    getStatsDataArray(metrics: Array<IMetric>, stats: { [id: string]: IMetricData }): Array<{ type: string, value: string, action: Action }> {
      return metrics.map((metric, index) => {
        const latestMetric = stats[metric.id]
        const action: Action = {
          actionType: "NAVIGATION",
          url: `curefit://metricDetail?metricId=${metric.id}`
        }
        const unit = this.getMetricUnit(metric.unit)
        return {
          type: metric.name,
          value: latestMetric ? `${latestMetric.value} ${unit}` : "--",
          action
        }
      })
    }

    @httpGet("/detailedStats")
    async getDetailedStats(req: express.Request): Promise<{ actions?: Array<Action>, widgets: Array<WidgetView> }> {

      const session: Session = req.session
      const userId: string = session.userId
      const metricId: number = req.query.metricId
      const userContext: UserContext = req.userContext as UserContext
      const tz = userContext.userProfile.timezone

      const fromDate = TimeUtil.getMomentNow(tz).startOf("week").format("YYYY-MM-DD")
      const toDate = TimeUtil.getMomentNow(tz).startOf("week").add(6, "day").format("YYYY-MM-DD")


      const rangeOptions: IMetricOptions = {
        userId,
        metricId,
        fromDate,
        toDate,
        aggregationRange: AggregationRange.WEEK
      }

      const loggedOptions: IMetricOptions = {
        userId: userId,
        metricId: metricId,
        count: 10
      }

      const [rangeData, loggedData] = await Promise.all([
        this.metricService.getAverageMetricsForCategoryForRange(rangeOptions),
        this.metricService.getMetricsForCategoryByCount(loggedOptions)
      ])

      const widgets: Array<WidgetView> = []
      widgets.push(this._getMetricTitleWidget(loggedData[0], rangeData.metric))
      widgets.push(this._getGraphWidget(rangeData.averageValue, AggregationRange.WEEK, fromDate, toDate, rangeData.metric, tz))
      widgets.push(this._getHistoryLoggedWidget(loggedData, userContext))
      const userDetails = await this.userCache.getUser(userContext.userProfile.userId)
      const defaultValue = (loggedData && loggedData[0]) ? loggedData[0].value : undefined
      const actions: Array<Action> = this._getMetricDetailActions(rangeData, userContext, userDetails.isInternalUser, defaultValue)

      return {
        actions: actions.length > 0 ? actions : undefined,
        widgets
      }
    }

    _getMetricTitleWidget(value: IMetricData, metric: IMetric): IMetricTitleWidget {
      if (value) {
        const unit = this.getMetricUnit(value.metric.unit)
        return {
          widgetType: "METRIC_TITLE_WIDGET",
          title: value.metric.name,
          value: `${value.value} ${unit}`
        }
      } else {
        return {
          widgetType: "METRIC_TITLE_WIDGET",
          title: metric.name,
          value: "--"
        }
      }
    }

    _getGraphWidget(values: { [id: string]: number }, aggregationRange: AggregationRange, fromDate: string, toDate: string, metric: IMetric,
      timezone: Timezone = TimeUtil.IST_TIMEZONE) {
      const graphWidget: IMetricGraphWidget = {
        widgetType: "METRIC_GRAPH_WIGDET",
        graphData: getDateSortedGraphData(values, aggregationRange, fromDate, toDate, undefined, timezone),
        graphType: aggregationRange,
        graphOptions: this.getGraphOptions(aggregationRange),
        fromDate,
        toDate,
        metric,
        range: getYAxisRange(values, metric)
      }
      return graphWidget
    }

    getGraphOptions(selectedType: AggregationRange): Array<{ type: AggregationRange, selected: boolean, displayText: string }> {
      const options: Array<{ type: AggregationRange, selected: boolean, displayText: string }> = []
      options.push({
        type: AggregationRange.WEEK,
        displayText: "Week",
        selected: selectedType === AggregationRange.WEEK ? true : false
      })
      options.push({
        type: AggregationRange.MONTH,
        displayText: "Month",
        selected: selectedType === AggregationRange.MONTH ? true : false
      })
      options.push({
        type: AggregationRange.YEAR,
        displayText: "Year",
        selected: selectedType === AggregationRange.YEAR ? true : false
      })
      return options
    }

    _getHistoryLoggedWidget(values: Array<IMetricData>, userContext: UserContext): IMetricStatsWidget {
      return {
        widgetType: "METRIC_LOGGED_WIDGET",
        data: _.map(values, (item) => {
          const unit = this.getMetricUnit(item.metric.unit)
          return {
            key: TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, new Date(Number(item.metricDate)), "DD MMM"),
            value: `${item.value} ${unit}`
          }
        })
      }
    }

    _getMetricDetailActions(data: IAverageMetricData, userContext: UserContext, isInternalUser: boolean, defaultValue?: number): Array<Action> {
      if (!data.metric.isLoggable) {
        return []
      }

      const actions: Array<Action> = []
      const metricOption = getMeasurementOptions(data.metric, userContext, isInternalUser, defaultValue)
      actions.push({
        actionType: "UPDATE_METRIC",
        title: `Log Current ${data.metric.name}`,
        meta: { ...data.metric, metricOption }
      })
      return actions
    }

    @httpGet("/graphData")
    async getHistoricalGraphData(req: express.Request): Promise<IMetricGraphWidget> {
      const session: Session = req.session
      const userId: string = session.userId
      const metricId: number = req.query.metricId

      const fromDate = req.query.fromDate
      const toDate = req.query.toDate
      const aggregationRange = req.query.graphType
      const metric = req.query.metric
      const userContext: UserContext = req.userContext as UserContext

      const options: IMetricOptions = {
        userId,
        metricId,
        fromDate,
        toDate,
        aggregationRange
      }

      const rangeData = await this.metricService.getAverageMetricsForCategoryForRange(options)
      const graphData = getDateSortedGraphData(rangeData.averageValue, aggregationRange, fromDate, toDate, undefined, userContext.userProfile.timezone)

      return {
        widgetType: "METRIC_GRAPH_WIGDET",
        graphData,
        graphType: aggregationRange,
        fromDate,
        toDate,
        metric: rangeData.metric,
        graphOptions: this.getGraphOptions(aggregationRange),
        range: getYAxisRange(rangeData.averageValue, rangeData.metric)
      }
    }



    @httpPost("/addMetricValue")
    async addMetricValue(req: express.Request): Promise<any> {
      const session: Session = req.session
      const userId: string = session.userId
      const body: any = req.body
      const metricId: number = body.metricId
      const value: number = this.getMetricValue(body)
      const source: string = req.session.userAgent

      const payload: IAddMetric = {
        userId,
        metricId,
        validated: false,
        source: req.session.userAgent,
        value,
        metricDate: (new Date).getTime()
      }
      const bmiUpdate = this.processMetricData(userId, metricId, source, value)

      if (bmiUpdate) {
        const [response, bmi] = await Promise.all([
          this.metricService.addMetricValue(payload),
          bmiUpdate
        ])
        return [response, bmi]
      } else {
        return [await this.metricService.addMetricValue(payload)]
      }
    }

    @httpPost("/addMultiUnitMetrics")
    async addMultiUnitMetrics(req: express.Request): Promise<any> {
      const session: Session = req.session
      const userId: string = session.userId
      const body: any = req.body
      const source: string = "DEVICE"
      const metricsPromises: Promise<IMetricData>[] = []
      body.metrics && body.metrics.forEach((item: IAddDeviceMetric) => {
        const payload: IAddMetric = {
          userId,
          metricId: item.metricId,
          validated: false,
          source: source,
          value: item.value,
          metricDate: (new Date).getTime()
        }

        const processedData = this.processMetricData(userId, item.metricId, source, item.value)
        if (processedData) {
          metricsPromises.push(processedData)
        }
        metricsPromises.push(this.metricService.addMetricValue(payload))
      })
      const response = await Promise.all(metricsPromises)
      return response
    }



    async processMetricData(userId: string, metricId: number, source: string, value: number): Promise<any> {
      /*
        added hack to accommodate BMI calculation on api layer
        ensure that we remove the hack when metric service starts having computations
      */

      let bmiUpdate
      switch (metricId) {
        /*
          if heigh look for latest weight entry.
          if found calculate BMI and push.
        */
        case METRICS_SUBCATEGOTY_ID.HEIGHT:
          {
            const options: IMetricOptions = {
              userId,
              metricId: METRICS_SUBCATEGOTY_ID.WEIGHT,
              count: 1
            }
            const weightData = await this.metricService.getMetricsForCategoryByCount(options)
            if (weightData.length > 0) {

              const weight = weightData[0].value
              const payload: IAddMetric = {
                userId,
                metricId: METRICS_SUBCATEGOTY_ID.BMI,
                validated: false,
                source,
                value: Number(calculateBMI(value, weight).toFixed(2)),
                metricDate: (new Date).getTime()
              }
              bmiUpdate = this.metricService.addMetricValue(payload)
            }
          }
          break
        /*
          if weight look for latest height entry.
          if found calculate BMI and push.
        */
        case METRICS_SUBCATEGOTY_ID.WEIGHT:
          {
            const options: IMetricOptions = {
              userId,
              metricId: METRICS_SUBCATEGOTY_ID.HEIGHT,
              count: 1
            }
            const heightData = await this.metricService.getMetricsForCategoryByCount(options)
            if (heightData.length > 0) {

              const height = heightData[0].value
              const payload: IAddMetric = {
                userId,
                metricId: METRICS_SUBCATEGOTY_ID.BMI,
                validated: false,
                source,
                value: Number(calculateBMI(height, value).toFixed(2)),
                metricDate: (new Date).getTime()
              }
              bmiUpdate = this.metricService.addMetricValue(payload)
            }
          }
          break
      }

      return bmiUpdate

    }



    getMetricValue(body: IAddMetricPayload): number {
      let value = 0
      body.values.map((item, index) => {
        switch (item.unit) {
          case MeasurementUnit.FEET:
            value = value + getCMFromFeet(item.value)
            break

          case MeasurementUnit.INCH:
            value = value + getCMFromInches(item.value)
            break

          // case MeasurementUnit.GRAM:
          //   value = value + getKGFromGrams(item.value)
          //   break

          default:
            value = value + item.value
            break
        }
      })
      return value
    }
  }

  return MetricController
}

export default MetricControllerFactory
