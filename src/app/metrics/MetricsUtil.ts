import { Counter, Summary } from "prom-client"
import { inject, injectable } from "inversify"
import { Logger, BASE_TYPES } from "@curefit/base"
import * as express from "express"
import { BlockingType } from "./models/BlockingType"
import { CheckinNotificationState } from "../gymfit/CheckinNotificationState"

@injectable()
class MetricsUtil {
    constructor(@inject(BASE_TYPES.ILogger) private logger: Logger) { }


    private static cfApiBlockedCounter = new Counter({
        name: MetricsUtil.getMetricsKey("request_blocked"),
        help: "Number of blocked requests",
        labelNames: ["blocking_type"],
        aggregator: "sum"
    })

    private static cfApiCheckingNotificationCounter = new Counter({
        name: MetricsUtil.getMetricsKey("gym_checkin_notification"),
        help: "Number of notification states",
        labelNames: ["checkin_notification_state"],
        aggregator: "sum"
    })

    private static notificationQueueCounter = new Counter({
        name: MetricsUtil.getMetricsKey("notification_queue_counter"),
        help: "Number of notifications for a message type",
        labelNames: ["message_type"],
        aggregator: "sum"
    })

    private static captchaFailuresCounter = new Counter({
        name: MetricsUtil.getMetricsKey("captcha_failures_counter"),
        help: "Number of captcha failures",
        labelNames: ["userAgent", "api"],
        aggregator: "sum"
    })

    private static eventLoopLagSummary = new Summary({
        name: MetricsUtil.getMetricsKey("event_loop_lag"),
        help: "event_loop_lag",
        percentiles: [0.5, 0.9, 0.95, 0.99]
    })

    private static getMetricsKey(event: string): string {
        return process.env.ENVIRONMENT + "_cf_api_" + event
    }

    public reportBlockedRequest(blockingType: BlockingType) {
        try {
            MetricsUtil.cfApiBlockedCounter.labels(blockingType).inc()
        } catch (e) {
            this.logger.error("Error while updating blocking metrics " + e)
        }
    }

    public reportCheckinNotificationState(checkinNotificationState: CheckinNotificationState) {
        try {
            MetricsUtil.cfApiCheckingNotificationCounter.labels(checkinNotificationState).inc()
        } catch (e) {
            this.logger.error("Error while updating checkin notif state " + e)
        }
    }

    public reportCaptchaFailure(userAgent: string, api: string) {
        try {
            MetricsUtil.captchaFailuresCounter.labels(userAgent, api).inc()
        } catch (e) {
            this.logger.error("Error while updating captchaFailureCounter metrics " + e)
        }
    }

    public reportNotificationMessageType(messageType: string) {
        try {
            MetricsUtil.notificationQueueCounter.labels(messageType).inc()
        } catch (e) {
            this.logger.error("Error while updating notificationQueueCounter metrics " + e)
        }
    }

    public reportEventLoopLag(lagInMs: number) {
        try {
            MetricsUtil.eventLoopLagSummary.observe(lagInMs)
        } catch (e) {
            this.logger.error("Error while reporting event loop lag " + e)
        }
    }
}

export default MetricsUtil
