import * as _ from "lodash"
import { Container, inject } from "inversify"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import AuthMiddleware from "../auth/AuthMiddleware"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CULT_CLIENT_TYPES } from "@curefit/cult-client"
import { ICultServiceOld as ICultService } from "@curefit/cult-client/dist/src/ICultServiceOld"
import * as express from "express"
import { UserContext } from "@curefit/userinfo-common"
import { CenterNotSelectedError } from "../common/errors/CenterNotSelectedError"
import { CultClass } from "@curefit/cult-common"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { ErrorFactory, HTTP_CODE } from "@curefit/error-client"
import { BASE_TYPES, Logger } from "@curefit/base"
import AppUtil from "../util/AppUtil"
import { <PERSON>acheHelper } from "../util/CacheHelper"
import { ALFRED_CLIENT_TYPES, IShipmentService } from "@curefit/alfred-client"
import { ICultBusiness } from "../cult/CultBusiness"
import { ErrorCodes } from "../error/ErrorCodes"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { CENTER_SERVICE_CLIENT_TYPES, ICenterService } from "@curefit/center-service-client"
import {
    CyclingBookingItem,
    CyclingBookingView,
    CyclingCancellationView,
    CyclingCityCenterCache,
    CyclingClassView,
    ICyclingService,
    OUTDOOR_CYCLING_SEARCH_PARAMS,
} from "./CyclingInterface"

const FOUR_HRS_IN_MILLIS: number = 4 * 60 * 60 * 1000

export function controllerFactory(kernel: Container) {

    @controller("/cycling", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class CyclingController {

        lastUpdated: number
        cache: CyclingCityCenterCache

        constructor(
                @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
                @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
                @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
                @inject(BASE_TYPES.ILogger) private logger: Logger,
                @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
                @inject(LOCATION_TYPES.CityService) private cityService: ICityService,
                @inject(CUREFIT_API_TYPES.CyclingService) private cyclingService: ICyclingService,
                @inject(CENTER_SERVICE_CLIENT_TYPES.CenterService) private centerService: ICenterService,
                @inject(CUREFIT_API_TYPES.CultBusiness) private cultBusiness: ICultBusiness,
        ) {
        }

        @httpGet("/cities")
        public async getCyclingCities(req: express.Request) {
            return (await this.getCache()).cities
        }

        @httpGet("/centers")
        public async getCyclingCenters(req: express.Request) {
            const cityId = req.query.cityId
            if (!cityId) {
                throw this.errorFactory.withCode(ErrorCodes.CITY_NOT_SELECTED, HTTP_CODE.BAD_REQUEST)
                        .withDebugMessage("City is not selected")
                        .build()
            }
            const cityCenterMap = (await this.getCache()).cityCenterMap
            return cityCenterMap.has(cityId) ? cityCenterMap.get(cityId) : []
        }

        @httpGet("/classes")
        public async getCyclingClasses(req: express.Request): Promise<CyclingClassView[]> {
            const centerId = req.query.centerId
            if (!centerId) {
                throw new CenterNotSelectedError().throwError(this.errorFactory, "Please select a cult center")
            }
            const cultResponse = await this.cultFitService.browseFitnessClass(centerId, req.session.userId, null, false,
                    false, OUTDOOR_CYCLING_SEARCH_PARAMS)
            if (!cultResponse || _.isEmpty(cultResponse.classes)) {
                return []
            }
            const classes: CyclingClassView[] = []

            for (const cultClass of cultResponse.classes) {
                if (cultClass.cultAppAvailableSeats > 0) {
                    classes.push({
                        classId: cultClass.id,
                        time: cultClass.date + " " + cultClass.startTime,
                        bookedForUser: !!cultClass.bookingNumber,
                        bookingNumber: cultClass.bookingNumber,
                    })
                }
            }
            return classes
        }

        @httpPost("/classes/book")
        public async bookClass(req: express.Request): Promise<CyclingBookingView> {
            const session = req.session
            const userContext = req.userContext as UserContext

            const loggedInResult = AppUtil.getUserAlertInfo(await this.userCache.getUser(session.userId), userContext)
            if (!_.isEmpty(loggedInResult.code)) {
                throw this.errorFactory.withCode(loggedInResult.code, loggedInResult.alertInfo.statusCode).build()
            }
            const apiKey = req.headers["api-key"] as string || req.headers["apikey"] as string
            const orderSource = AppUtil.callSource(apiKey)

            const pulseOptOut = req.query.pulseOptOut as boolean
            const advertiserId = req.body.advertiserId
            const clientMetaData = {
                subUserId: userContext.userProfile.subUserId,
                enableCultPARQ: false,
                rescheduleSourceBookingNumber: req.body.rescheduleSourceBookingNumber,
                attributionSource: session.sessionData.attributionSource,
                deviceId: session.deviceId
            }

            const centerId = req.body.centerId
            if (!centerId) {
                throw new CenterNotSelectedError().throwError(this.errorFactory, "Please select a cult center")
            }
            const cultResponse = await this.cultFitService.browseFitnessClass(centerId, session.userId, null, false,
                    false, OUTDOOR_CYCLING_SEARCH_PARAMS)

            const cultClassMap = new Map<number, CultClass>()
            if (cultResponse && !_.isEmpty(cultResponse.classes)) {
                for (const cultClass of cultResponse.classes) {
                    cultClassMap.set(cultClass.id, cultClass)
                }
            }

            const classIds: number[] = req.body.classIds
            const successes: CyclingBookingItem[] = []
            const failures: CyclingBookingItem[] = []

            for (const classId of classIds) {
                const classIdStr = classId.toString()
                const cultClass = cultClassMap.get(classId)
                if (!cultClass) {
                    throw this.errorFactory.withCode("INVALID_CLASS_ID", HTTP_CODE.BAD_REQUEST)
                            .withMessage("Invalid class id " + classId)
                            .build()
                }
                const time = cultClass.date + " " + cultClass.startTime
                if (!!cultClass.bookingNumber) {
                    successes.push({
                        classId,
                        time,
                        cultBookingId: cultClass.bookingNumber,
                    })
                    continue
                }
                let cultBooking
                const reqBody: any  = {
                    cultClassID: classId,
                    classVersion: "0",
                    amountPaid: 0,
                    source: orderSource,
                    clientMetadata: clientMetaData,
                    advertiserId,
                    pulseOptOut
                }
                try {
                    cultBooking = await this.cultFitService.createFitnessBooking(reqBody, session.userId, "CUREFIT_API", session.deviceId)
                    successes.push({
                        classId,
                        time,
                        cultBookingId: cultBooking.id.toString(),
                    })
                } catch (ex) {
                    try {
                        // 2nd try
                        cultBooking = await this.cultFitService.createFitnessBooking(reqBody, session.userId, "CUREFIT_API", session.deviceId)
                        successes.push({
                            classId,
                            time,
                            cultBookingId: cultBooking.id.toString(),
                        })
                    } catch (ex) {
                        this.logger.error("Failure in booking cycling classId " + classId, ex)
                        failures.push({
                            classId,
                            time,
                            error: ex.code,
                        })
                    }
                }
            }
            return {
                hasFailures: failures.length > 0,
                successes,
                failures,
            }
        }

        @httpPost("/classes/cancel")
        public async cancelClass(req: express.Request): Promise<CyclingCancellationView> {
            const session = req.session
            const bookingNumber = req.query.bookingNumber
            if (!bookingNumber) {
                throw this.errorFactory.withCode(ErrorCodes.BOOKING_NUMBER_NOT_FOUND, HTTP_CODE.BAD_REQUEST)
                        .withDebugMessage("BookingNumber is null/empty")
                        .build()
            }
            try {
                this.cultFitService.cancelBookingV2(bookingNumber, session.userId, "CUREFIT_APP", session.userId, session.deviceId)
                return {
                    bookingNumber,
                    success: true,
                }
            } catch (ex) {
                this.logger.error("Failure in cancelling cycling booking " + bookingNumber, ex)
                return {
                    bookingNumber,
                    success: false,
                    error: ex.code,
                }
            }
        }

        private async getCache() {
            if (this.cache) {
                if (!this.lastUpdated || this.lastUpdated < new Date().getTime() - FOUR_HRS_IN_MILLIS) {
                    this.updateCache()
                }
            } else {
                await this.updateCache()
            }
            return this.cache
        }

        private async updateCache() {
            this.cache = await this.cyclingService.getCyclingCityCenterCache()
        }
    }

    return CyclingController
}

export default controllerFactory
