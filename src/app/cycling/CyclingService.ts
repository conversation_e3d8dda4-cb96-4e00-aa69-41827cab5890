import { inject, injectable } from "inversify"
import { CULT_CLIENT_TYPES } from "@curefit/cult-client"
import { ICultServiceOld as ICultService } from "@curefit/cult-client/dist/src/ICultServiceOld"
import { BASE_TYPES, Logger } from "@curefit/base"
import { CENTER_SERVICE_CLIENT_TYPES, ICenterService } from "@curefit/center-service-client"
import { Tenant } from "@curefit/user-common"
import { City } from "@curefit/location-common"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { ICultBusiness } from "../cult/CultBusiness"
import { UserContext } from "@curefit/userinfo-common"
import {
    CyclingCenterView,
    CyclingCityCenterCache,
    ICyclingService,
    OUTDOOR_CYCLING_SEARCH_PARAMS,
} from "./CyclingInterface"

const emptyUserContext: UserContext = {
    userProfile: null,
    userPromise: null,
    sessionInfo: {
        osName: null,
        appVersion: null,
        userAgent: null,
        cpVersion: null,
        deviceId: null,
        isUserLoggedIn: false,
        lat: null,
        lon: null,
        sessionData: null,
        at: null,
        apiKey: null,
        orderSource: null,
        ip: null
    }
}

@injectable()
export class CyclingService implements ICyclingService {

    constructor(
            @inject(CULT_CLIENT_TYPES.CultFitService) public cultFitService: ICultService,
            @inject(LOCATION_TYPES.CityService) private cityService: ICityService,
            @inject(CENTER_SERVICE_CLIENT_TYPES.CenterService) private centerService: ICenterService,
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(CUREFIT_API_TYPES.CultBusiness) private cultBusiness: ICultBusiness,
    ) {
    }

    public async getCyclingCityCenterCache(): Promise<CyclingCityCenterCache> {
        const cache: CyclingCityCenterCache = {
            cities: [],
            cityCenterMap: new Map<string, CyclingCenterView[]>()
        }

        const cities = this.cityService.listCities(Tenant.CUREFIT_APP)
        for (const city of cities) {
            const centerViews = await this.getCenterViews(city)
            if (centerViews.length > 0) {
                cache.cities.push({
                    cityId: city.cityId,
                    name: city.name,
                })
                cache.cityCenterMap.set(city.cityId, centerViews)
            }
        }
        return cache
    }

    private async getCenterViews(city: City) {
        const centers = await this.cultBusiness.getAllCenters(emptyUserContext, city.cityId)
        const centerViews: CyclingCenterView[] = []

        for (const center of centers) {
            if (!center.meta || !center.meta.cultCenterId) {
                continue
            }
            const cultCenterId = center.meta.cultCenterId
            let cultResponse
            try {
                cultResponse = await this.cultFitService.browseFitnessClass(cultCenterId.toString(), null, null, false,
                        false, OUTDOOR_CYCLING_SEARCH_PARAMS)
                if (cultResponse.classes.length <= 0) {
                    continue
                }
            } catch (ex) {
                this.logger.error("Exception in getting classes for centerId " + cultCenterId + ": " + ex.code)
                continue
            }
            for (const cultClass of cultResponse.classes) {
                if (cultClass.cultAppAvailableSeats > 0) {
                    centerViews.push({
                        id: cultCenterId,
                        name: center.name,
                    })
                    break
                }
            }
        }
        return centerViews
    }
}
