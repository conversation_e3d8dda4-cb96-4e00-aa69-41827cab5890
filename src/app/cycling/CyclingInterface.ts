export const OUTDOOR_CYCLING_SEARCH_PARAMS = {
    maxDays: 30,
    workoutId: 338,
}

export interface CyclingClassView {
    classId: number
    time: string
    bookedForUser: boolean
    bookingNumber?: string
}

export interface CyclingCancellationView {
    bookingNumber: string
    success: boolean
    error?: string
}

export interface CyclingBookingItem {
    classId: number
    time: string
    cultBookingId?: string
    error?: string
}

export interface CyclingBookingView {
    hasFailures: boolean
    successes: CyclingBookingItem[]
    failures: CyclingBookingItem[]
}

export interface CyclingCityView {
    cityId: string
    name: string
}

export interface CyclingCenterView {
    id: number
    name: string
}

export interface CyclingCityCenterCache {
    cities: CyclingCityView[]
    cityCenterMap: Map<string, CyclingCenterView[]>
}

export interface ICyclingService {
    getCyclingCityCenterCache: () => Promise<CyclingCityCenterCache>
}
