export enum ErrorCodes {
    GENERIC_SOMETHING_WENT_WRONG_ERROR = "GENERIC_SOMETHING_WENT_WRONG_ERROR",
    GENERIC_PAGE_NOT_FOUND_ERROR = "GENERIC_PAGE_NOT_FOUND_ERROR",
    FITBIT_USER_NOT_LOGGED_IN_ERR = "FITBIT_USER_NOT_LOGGED_IN_ERR",
    FRESHDESK_UPDATE_NUMBER_ERR = "FRESHDESK_UPDATE_NUMBER_ERR",
    FRESHDESK_TICKET_DETAILS_NOT_FOUND_ERR = "FRESHDESK_TICKET_DETAILS_NOT_FOUND_ERR",
    FEEDBACK_SAVE_ERR = "FEEDBACK_SAVE_ERR",
    CARE_NO_SLOTS_AVAILABLE_ERR = "CARE_NO_SLOTS_AVAILABLE_ERR",
    CENTER_NOT_SELECTED_ERR = "CENTER_NOT_SELECTED_ERR",
    HOME_SAMPLE_UNAVAILABLE_ERROR = "HOME_SAMPLE_UNAVAILABLE_ERROR",
    CART_EMPTY_ERR = "CART_EMPTY_ERR",
    MAX_ITEMS_IN_CART_ERR = "MAX_ITEMS_IN_CART_ERR",
    NO_ADDRESS_ON_CHECKOUT_ERR = "NO_ADDRESS_ON_CHECKOUT_ERR",
    INVITE_EXPIRED_ERR = "INVITE_EXPIRED_ERR",
    INVITE_INVALID_ERR = "INVITE_INVALID_ERR",
    CHALLENGE_ACCESS_ERR = "CHALLENGE_ACCESS_ERR",
    UNAUTHORIZED_ERR = "UNAUTHORIZED_ERR",
    UNAUTHORIZED_SUB_USER_ERR = "UNAUTHORIZED_SUB_USER_ERR",
    AUTH_LOCATION_UNSERVICEABLE = "AUTH_LOCATION_UNSERVICEABLE",
    APP_VERSION_NOT_SUPPORTED_IOS_ERR = "APP_VERSION_NOT_SUPPORTED_IOS_ERR",
    APP_VERSION_NOT_SUPPORTED_ANDROID_ERR = "APP_VERSION_NOT_SUPPORTED_ANDROID_ERR",
    GOOGLE_PAY_LOGIN_ERR = "GOOGLE_PAY_LOGIN_ERR",
    PAYTM_PWA_LOGIN_ERR = "PAYTM_PWA_LOGIN_ERR",
    INVALID_EMAIL = "INVALID_EMAIL",
    INVALID_PHONE = "INVALID_PHONE",
    INVALID_PHONE_CHURNED = "INVALID_PHONE_CHURNED",
    INVALID_NAME = "INVALID_NAME",
    NOT_LOGGED_IN_ERR = "NOT_LOGGED_IN_ERR",
    S3_SIGNED_URL_CREATION_ERR = "S3_SIGNED_URL_CREATION_ERR",
    MAX_ADDRESS_ADDITION_LIMIT_ERR = "MAX_ADDRESS_ADDITION_LIMIT_ERR",
    GEAR_SOLD_OUT_ERR = "GEAR_SOLD_OUT_ERR",
    OFFER_NOT_VALID_ERR = "OFFER_NOT_VALID_ERR",
    OTP_LIMIT_REACHED_ERR = "OTP_LIMIT_REACHED_ERR",
    CULT_UPGRADE_MEMBERSHIP_TARGET_PACK_NOT_FOUND_ERR = "CULT_UPGRADE_MEMBERSHIP_TARGET_PACK_NOT_FOUND_ERR",
    DEVICE_INFO_NOT_PRESENT_ERR = "DEVICE_INFO_NOT_PRESENT_ERR",
    CACHE_NOT_LOADED_ERR = "CACHE_NOT_LOADED_ERR",
    MOMENT_OF_DAY_CREATION_ERR = "MOMENT_OF_DAY_CREATION_ERR",
    AREA_NOT_SERVICEABLE_ERR = "AREA_NOT_SERVICEABLE_ERR",
    GEAR_NOT_ALLOWED_TO_RETURN_ERR = "GEAR_NOT_ALLOWED_TO_RETURN_ERR",
    GEAR_ITEM_NOT_FOUND_ERR = "GEAR_ITEM_NOT_FOUND_ERR",
    GEAR_NOT_AUTHORIZED_TO_MODIFY_ERR = "GEAR_NOT_AUTHORIZED_TO_MODIFY_ERR",
    GEAR_ORDER_CANNOT_CANCEL_ERR = "GEAR_ORDER_CANNOT_CANCEL_ERR",
    GEAR_ORDER_CANNOT_RETURN_ERR = "GEAR_ORDER_CANNOT_RETURN_ERR",
    UNLINK_NOT_ALLOWED_ERR = "UNLINK_NOT_ALLOWED_ERR",
    SEND_LINK_INVALID_PHONE_NUMBER_ERR = "SEND_LINK_INVALID_PHONE_NUMBER_ERR",
    ADDRESS_LINE1_INCOMPLETE_ERR = "ADDRESS_LINE1_INCOMPLETE_ERR",
    ADDRESS_DOES_NOT_EXIST_ERR = "ADDRESS_DOES_NOT_EXIST_ERR",
    PAUSE_INACTIVE_PACK_ERR = "PAUSE_INACTIVE_PACK_ERR",
    PACK_RESUME_FAILED = "PACK_RESUME_FAILED",
    CANNOT_CHANGE_MEAL_ERR = "CANNOT_CHANGE_MEAL_ERR",
    PULSE_ACCESS_INFO_NOT_FOUND = "PULSE_ACCESS_INFO_NOT_FOUND",
    PULSE_USER_BOOKING_NOT_FOUND = "PULSE_USER_BOOKING_NOT_FOUND",
    PULSE_REPORT_NOT_READY = "PULSE_REPORT_NOT_READY",
    SQUAD_NOT_CREATED_ERR = "SQUAD_NOT_CREATED_ERR",
    REFFERAL_NOT_VALID_FOR_USER_ERR = "REFFERAL_NOT_VALID_FOR_USER_ERR",
    GENERIC_ERROR_WITH_MESSAGE = "GENERIC_ERROR_WITH_MESSAGE",
    GENERIC_COUPON_ERROR = "GENERIC_COUPON_ERROR",
    COUPON_INVALID_SOURCE = "COUPON_INVALID_SOURCE",
    CULT_MONEY_BACK_CANCEL_ERR = "CULT_MONEY_BACK_CANCEL_ERR",
    NOT_AUTHORIZED_TO_VIEW_ORDER = "NOT_AUTHORIZED_TO_VIEW_ORDER",
    ORDER_DOES_NOT_EXIST_ERR = "ORDER_DOES_NOT_EXIST_ERR",
    EMAIL_ID_NOT_PROVIDED_ERR = "EMAIL_ID_NOT_PROVIDED_ERR",
    USER_ENTERED_EMAIL_CONTAINS_FILTERED_CHARACTERS_AND_IS_NOT_SAME_AS_PROFILE_EMAILS = "USER_ENTERED_EMAIL_CONTAINS_FILTERED_CHARACTERS_AND_IS_NOT_SAME_AS_PROFILE_EMAILS",
    LIVE_PT_PACK_NOT_FOUND_ERR = "LIVE_PT_PACK_NOT_FOUND_ERR",
    MIND_THERAPY_PACK_NOT_FOUND_ERR = "MIND_THERAPY_PACK_NOT_FOUND_ERR",
    CULT_LOCATION_PREFERENCE_UPDATE_ERR = "CULT_LOCATION_PREFERENCE_UPDATE_ERR",
    CARE_PRODUCT_DETAILS_NOT_FOUND_ERR = "CARE_PRODUCT_DETAILS_NOT_FOUND_ERR",
    CARE_DOCTOR_DETAILS_NOT_FOUND_ERR = "CARE_DOCTOR_DETAILS_NOT_FOUND_ERR",
    ERR_DIAG_CART_DUPLICATE_ITEM = "ERR_DIAG_CART_DUPLICATE_ITEM",
    ERR_DIAG_CART_GENDER_UNSUPPORTED = "ERR_DIAG_CART_GENDER_UNSUPPORTED",
    ERR_DIAG_CART_NO_ACTIVE_CART = "ERR_DIAG_CART_NO_ACTIVE_CART",
    ERR_DIAG_CART_NO_CART_ITEM = "ERR_DIAG_CART_NO_CART_ITEM",
    APPLE_IN_APP_REFRESH_RECEIPT_NO_ACTIVE_PURCHASE = "APPLE_IN_APP_REFRESH_RECEIPT_NO_ACTIVE_PURCHASE",
    APPLE_IN_APP_REFRESH_RECEIPT_INVALID_REQUEST = "APPLE_IN_APP_REFRESH_RECEIPT_INVALID_REQUEST",
    CAPTCHA_INVALID = "CAPTCHA_INVALID",
    CITY_NOT_SELECTED = "CITY_NOT_SELECTED",
    BOOKING_NUMBER_NOT_FOUND = "BOOKING_NUMBER_NOT_FOUND",
    PHONE_NUMBER_UPDATE_NOT_ALLOWED_ERR = "PHONE_NUMBER_UPDATE_NOT_ALLOWED_ERR",
    CULT_ANOTHER_USER_TRANSFER_NOT_ALLOWED = "CULT_ANOTHER_USER_TRANSFER_NOT_ALLOWED",
    CULT_PAUSE_END_DATE_EXCEEDING_LIMIT = "CULT_PAUSE_END_DATE_EXCEEDING_LIMIT",
    CULT_CITY_TRANSFER_NOT_ALLOWED_ANDROID = "CULT_CITY_TRANSFER_NOT_ALLOWED_ANDROID",
    CULT_CITY_TRANSFER_NOT_ALLOWED_IOS = "CULT_CITY_TRANSFER_NOT_ALLOWED_IOS",
    EMAIL_UNLINK_NOT_ALLOWED_ERR = "EMAIL_UNLINK_NOT_ALLOWED_ERR",
    // TODO: add user-action mapping in cyclops for any addition here
    TATA_NEU_USER_NOT_FOUND = "TATA_NEU_USER_NOT_FOUND",
    TATA_NEU_NO_USER_CONSENT = "TATA_NEU_NO_USER_CONSENT",
    TATA_NEU_ORDER_USER_MISMATCH = "TATA_NEU_ORDER_USER_MISMATCH",
    CANNOT_DELETE_ACCOUNT = "CANNOT_DELETE_ACCOUNT",
    ERR_PLAY_PACK_INVALID_CITY = "ERR_PLAY_PACK_INVALID_CITY",
    ERR_METHOD_NOT_IMPLEMENTED = " ERR_METHOD_NOT_IMPLEMENTED",
    ERR_TRUECALLER_LOGIN_FAILED = "ERR_TRUECALLER_LOGIN_FAILED",
    ERR_TRUECALLER_WAITING_CALLBACK = "ERR_TRUECALLER_WAITING_CALLBACK",
    ERR_TRUECALLER_DISABLED = "ERR_TRUECALLER_DISABLED",
    PLAY_UPGRADE_SELECT_PAUSED_ERR = "PLAY_UPGRADE_SELECT_PAUSED_ERR",
    ERR_CHECKIN_FORBID_VOUCHER_DEVICE_MISMATCH = "ERR_CHECKIN_FORBID_VOUCHER_DEVICE_MISMATCH",
    REGISTER_PRIMARY_DEVICE_ERR = "REGISTER_PRIMARY_DEVICE_ERR",
    CHECKIN_FROM_PRIMARY_DEVICE = "CHECKIN_FROM_PRIMARY_DEVICE",
    PAYMENT_FAILED_JUSPAY = "PAYMENT_FAILED_JUSPAY",
    PAYMENT_DATA_MISSING = "PAYMENT_DATA_MISSING",
    CALL_NOT_ALLOWED = "CUREFIT_CALL_NOT_ALLOWED_ERR",
    ERR_MEMBERSHIP_PURCHASE_PACK_NOT_AVAILABLE_IN_CITY = "ERR_MEMBERSHIP_PURCHASE_PACK_NOT_AVAILABLE_IN_CITY",
    NOT_AUTHORIZED_TO_VIEW_PATIENT_DETAILS = "NOT_AUTHORIZED_TO_VIEW_PATIENT_DETAILS",
    SPRINKLR_TICKET_NOT_FOUND_ERR = "SPRINKLR_TICKET_NOT_FOUND_ERR"
}
