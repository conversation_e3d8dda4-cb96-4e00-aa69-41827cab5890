import { inject, injectable } from "inversify"
import { MustacheUtils } from "@curefit/base-utils"
import * as _ from "lodash"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import UserActionMappingBusiness from "./UserActionMappingBusiness"
import { BASE_TYPES, Logger } from "@curefit/base"
import * as mustache from "mustache"
import { ActionUtil as VMActionUtil } from "@curefit/vm-common"
import { TemplatedAction, UserContext } from "@curefit/vm-models"
import { Action } from "@curefit/apps-common"
import { USER_MESSAGE_TRANSLATOR_TYPES, UserMessageTranslatorService } from "@curefit/user-message-translator"
import { UserAgentType } from "@curefit/base-common"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"

@injectable()
class UserErrorBusiness {
    constructor(
        @inject(USER_MESSAGE_TRANSLATOR_TYPES.UserMessageTranslatorService) private userMessageTranslatorService: UserMessageTranslatorService,
        @inject(CUREFIT_API_TYPES.UserActionMappingBusiness) private userActionMappingBusiness: UserActionMappingBusiness,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
    ) {
    }

    public async getTemplatedError(err: any, userContext: UserContext) {
        const translatedError = await this.userMessageTranslatorService.translateErrorToUserMessage(err)
        if (_.isEmpty(translatedError)) {
            return undefined
        }
        const userError = translatedError.userError
        const meta = err.meta
        const actions: Action[] = []
        let tagLocations: string[] = []
        if (!_.isEmpty(userError.userActions)) {
            for (const userAction of userError.userActions) {
                if (!userAction) {
                    this.logger.warn(`empty userAction in error`, {userError,  err})
                }
                const templatedAction: TemplatedAction = await this.userActionMappingBusiness.getAction(userAction, this.serviceInterfaces, userContext)
                if (_.isEmpty(templatedAction)) {
                    continue
                }
                actions.push(VMActionUtil.getActionFromTemplatedAction(templatedAction))
                tagLocations.push(templatedAction.title?.value)
                tagLocations.push(templatedAction.url?.value)
                tagLocations.push(templatedAction.subtitle?.value)
            }
        }
        tagLocations = tagLocations.filter(tag => !_.isEmpty(tag))
        const allTags = MustacheUtils.allTagsUsed(tagLocations)
        if (!MustacheUtils.contextHasAllTags(allTags, meta)) {
            this.logger.error(`Missing data for templating actions with tags: ${allTags} and meta: ${JSON.stringify(meta)}`)
            return undefined
        }
        _.forEach(actions, action => {
            action.title = !_.isEmpty(action.title) ? mustache.render(action.title, meta) : undefined
            action.url = !_.isEmpty(action.url) ? mustache.render(action.url, meta) : undefined
        })
        return {
            title: userError.title,
            message: userError.message,
            actions: actions
        }
    }
}

export default UserErrorBusiness
