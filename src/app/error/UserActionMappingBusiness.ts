import { inject, injectable } from "inversify"
import { Action as VMAction, UserActionMapping, UserActionMappingOverRide, UserContext, TemplatedAction } from "@curefit/vm-models"
import { BASE_TYPES, ILogger } from "@curefit/base"
import * as _ from "lodash"
import * as util from "util"
import { ActionUtil as VMActionUtil, ActionV2, getUserActionsRedisKey } from "@curefit/vm-common"
import { UserAgentType } from "@curefit/base-common"
import { ICrudKeyValue, IMultiCrudKeyValue, REDIS_TYPES } from "@curefit/redis-utils"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { MustacheUtils } from "@curefit/base-utils"
import * as mustache from "mustache"
import * as LRUCache from "lru-cache"

@injectable()
class UserActionMappingBusiness {
    private redisCrudDao: ICrudKeyValue
    private userActionRedisMemCache: LRUCache<string, string>

    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(REDIS_TYPES.MultiCrudKeyValueDao) private multiCrudKeyValueDao: IMultiCrudKeyValue,
    ) {
        this.redisCrudDao = this.multiCrudKeyValueDao.getICrudKeyValue("VM-CACHE")

        this.userActionRedisMemCache = new LRUCache<string, string>({
            max: 1000,
            ttl: 1000_000
        })
    }

    private async getUserActionMapping(userAction: string): Promise<string> {
        const key = getUserActionsRedisKey(userAction)
        if (this.userActionRedisMemCache.has(key)) {
            return this.userActionRedisMemCache.get(key)
        }
        const stringifiedActionMapping = await this.redisCrudDao.read(key)
        this.userActionRedisMemCache.set(key, stringifiedActionMapping)
        return stringifiedActionMapping
    }

    public async getAction(userAction: string, interfaces: CFServiceInterfaces, userContext: UserContext): Promise<TemplatedAction> {
        const userAgent: UserAgentType = userContext?.sessionInfo?.userAgent || null
        const appVersion: number = userContext?.sessionInfo?.appVersion || null
        const osname: string = userContext?.sessionInfo?.osName || null

        const userActionMapping: UserActionMapping = <UserActionMapping>JSON.parse(await this.getUserActionMapping(userAction))
        if (_.isEmpty(userActionMapping)) {
            this.logger.error(`Could not find user error mapping for userAction: ${userAction}`)
            return undefined
        }
        let defaultAction = userActionMapping.defaultAction
        let overrideAction = {} as TemplatedAction
        if (defaultAction && !_.isEmpty(defaultAction.segmentIds)) {
            const segment = await interfaces.segmentService.doesUserBelongToAnySegment(defaultAction.segmentIds, userContext)
            if (!segment) {
                defaultAction = {} as TemplatedAction
            }
        }
        if (!_.isEmpty(userActionMapping.overrides) && !_.isEmpty(userAgent)) {
            for (const override of userActionMapping.overrides) {
                if (userAgent === override.userAgent && (
                    (userAgent === "APP" && !_.isEmpty(osname) && !_.isEmpty(appVersion) && this.checkAppVersion(override, osname, appVersion)) ||
                    (userAgent === "DESKTOP" || userAgent === "MBROWSER")
                )) {
                    overrideAction = override.action
                }
                // Segment Check & pick as per precedence
                if (!_.isEmpty(overrideAction) && !_.isEmpty(overrideAction.segmentIds)) {
                    const segment = await interfaces.segmentService.doesUserBelongToAnySegment(overrideAction.segmentIds, userContext)
                    if (!segment) {
                        overrideAction = {} as TemplatedAction
                    } else {
                        break
                    }
                }
            }
        }
        return !_.isEmpty(overrideAction) ? overrideAction : !_.isEmpty(defaultAction) ? defaultAction : {} as TemplatedAction
    }

    public async getVMActionFromActionV2(actionV2: ActionV2, interfaces: CFServiceInterfaces, userContext: UserContext): Promise<VMAction> {
        if (_.isEmpty(actionV2.userAction) || actionV2.userAction === "undefined") {
            this.logger.warn(`empty userAction for action: ${util.inspect(actionV2, {depth: 5})}`)
        }
        const templatedAction = await this.getAction(actionV2.userAction, interfaces, userContext)
        if (_.isEmpty(templatedAction)) {
            return undefined
        }
        const action = VMActionUtil.getActionFromTemplatedAction(templatedAction)
        let tagLocations = [action.title, action.url]
        tagLocations = tagLocations.filter(tag => !_.isEmpty(tag))
        const allTags = MustacheUtils.allTagsUsed(tagLocations)
        const meta: { [tagName: string]: string } = {}
        _.forEach(actionV2.dynamicFields, dynamicField => {
            meta[dynamicField.key] = dynamicField.value
        })
        if (!MustacheUtils.contextHasAllTags(allTags, meta)) {
            return undefined
        }
        action.title = action.title ? mustache.render(action.title, meta) : undefined
        action.url = action.url ? mustache.render(action.url, meta) : undefined
        return action
    }

    private checkAppVersion(override: UserActionMappingOverRide, osname: string, appVersion: number): boolean {
        const minVersionIos = override.minAppVersion ? override.minAppVersion.ios : "0"
        const maxVersionIos = override.maxAppVersion ? override.maxAppVersion.ios : "99999"

        const minVersionAndroid = override.minAppVersion ? override.minAppVersion.android : "0"
        const maxVersionAndroid = override.maxAppVersion ? override.maxAppVersion.android : "99999"

        if (osname.toLowerCase() === "ios" && appVersion.toString() >= minVersionIos && appVersion.toString() <= maxVersionIos) {
            return true
        }
        if (osname.toLowerCase() === "android" && appVersion.toString() >= minVersionAndroid && appVersion.toString() <= maxVersionAndroid) {
            return true
        }
        return false
    }


}

export default UserActionMappingBusiness
