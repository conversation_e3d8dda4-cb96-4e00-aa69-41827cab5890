import { BASE_TYPES, C<PERSON>Util, FetchUtilV2, <PERSON>og<PERSON> } from "@curefit/base"
import { ActionUtil } from "@curefit/base-utils"
import { Tenant } from "@curefit/user-common"
import { Session } from "@curefit/userinfo-common"
import { UserContext } from "@curefit/vm-models"
import * as express from "express"
import { inject, injectable } from "inversify"
import { FreemiumUserOffer } from "./common/views/WidgetView"
import { JavaWidgetResponse, Page } from "./page/vm/VMPageBuilder"
import { UserStatusResponse } from "./user/UserController"
import { CLS_UTIL_REQ_HEADERS } from "./util/AuthUtil"
import { ClassScheduleResponse } from "./cult/ClassListViewBuilderV2"
import _ = require("lodash")

const http = require("http")
const https = require("https")
const fetch = require("node-fetch")
const async = require("async")

export interface SlackRegisterUserResponse {
    slackAppChannelUrl: string
}

export interface UserNotificationResponse {
    id: number
    subscriptionStatus: boolean
    dimensionName: string
    dimensionValue: string
    children: UserNotificationResponse[]
}

@injectable()
class CFAPIJavaService {

    private agent: any

    constructor(
        @inject(BASE_TYPES.FetchUtilV2) protected fetchHelper: FetchUtilV2,
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(BASE_TYPES.ClsUtil) private clsUtil: CLSUtil,
    ) {
        const cfAPiJavaHttpAgent = new http.Agent({
            keepAliveMsecs: 50,
            keepAlive: true,
            maxSockets: 2000,
            maxFreeSockets: 20,
            timeout: 90_000,
        })

        const cfAPiJavaHttpsAgent = new https.Agent({
            keepAliveMsecs: 50,
            keepAlive: true,
            maxSockets: 2000,
            maxFreeSockets: 10,
            timeout: 90_000,
        })

        this.agent = (_parsedURL: any) => _parsedURL.protocol == "http:" ? cfAPiJavaHttpAgent : cfAPiJavaHttpsAgent

    }

    baseJavaServiceUrl(): string {
        if (process.env.APP_ENV === "PRODUCTION") {
            return "http://cfapi-internal.production.cure.fit.internal"
        } else if (process.env.APP_ENV === "ALPHA") {
            return "http://cfapi-internal.alpha.cure.fit.internal"
        } else if (process.env.APP_ENV === "STAGE") {
            return "http://cfapi-internal.stage.cure.fit.internal"
        } else {
            return "http://localhost:8383"
        }
    }

    baseCultsportJavaServiceUrl (): string {
        if (process.env.APP_ENV === "PRODUCTION") {
            return "http://cfapi-cs-internal.production.cure.fit.internal"
        } else if (process.env.APP_ENV === "ALPHA") {
            return "http://cfapi-cs-internal.alpha.cure.fit.internal"
        } else if (process.env.APP_ENV === "STAGE") {
            return "http://cfapi-cs-internal.stage.cure.fit.internal"
        } else {
            return "http://localhost:8383"
        }
    }

    deleteUnwantedHeaders(apiHeaders: any) {
        delete apiHeaders["host"]
        delete apiHeaders["accept"]
        delete apiHeaders["content-type"]
        delete apiHeaders["accept-encoding"]
        delete apiHeaders["content-length"]
    }

    async homePage(req: express.Request): Promise<Page> {
        const url = this.baseJavaServiceUrl() + req.originalUrl
        const apiHeaders: any = req.headers
        this.deleteUnwantedHeaders(apiHeaders)
        const session: Session = req.session
        const userId: string = session.userId
        const response = await fetch(url, this.fetchHelper.get({
            headers: apiHeaders,
            timeout: 6_000,
            agent: this.agent
        }))
        return this.fetchHelper.parseResponse<Page>(response)
    }

    async statusPage(req: express.Request): Promise<UserStatusResponse> {
        const url = this.baseJavaServiceUrl() + req.originalUrl
        const apiHeaders: any = req.headers
        this.deleteUnwantedHeaders(apiHeaders)
        const session: Session = req.session
        const userId: string = session.userId
        const response = await fetch(url, this.fetchHelper.post({
            body: req.body,
            headers: apiHeaders,
            timeout: 4_000,
            agent: this.agent
        }))
        return this.fetchHelper.parseResponse<UserStatusResponse>(response)
    }

    async getWidgets(widgetIds: any, queryParams: { [filterName: string]: string }, appTenant: Tenant, req?: express.Request): Promise<JavaWidgetResponse> {
        const baseUrl = appTenant === Tenant.CULTSPORT_APP ? this.baseCultsportJavaServiceUrl() : this.baseJavaServiceUrl()
        const url = baseUrl + "/page/widgets/ids" + encodeURI(ActionUtil.serializeAsQueryParams(queryParams))
        const apiHeaders: any = req?.headers ?? JSON.parse(this.clsUtil.getNamespace().get(CLS_UTIL_REQ_HEADERS) ?? "{}")
        if (_.isEmpty(apiHeaders)) {
            return  {
                widgets: []
            }
        }
        this.deleteUnwantedHeaders(apiHeaders)
        return async.retry({times: 2, interval: 0, errorFilter: () => true}, async () => {
            const response = await fetch(url, this.fetchHelper.post({
                body: widgetIds,
                headers: apiHeaders,
                timeout: 4_000,
                agent: this.agent
            }))
            return this.fetchHelper.parseResponse<JavaWidgetResponse>(response)
        })
    }

    async registerSlackUser(req: express.Request): Promise<SlackRegisterUserResponse> {
        const url = this.baseJavaServiceUrl() + "/user/slackAuth"
        const apiHeaders: any = req.headers
        apiHeaders["at"] = req.session.at
        this.deleteUnwantedHeaders(apiHeaders)
        const response = await fetch(url, this.fetchHelper.post({
            body: req.body,
            headers: apiHeaders,
            timeout: 5_000,
            agent: this.agent
        }))
        return this.fetchHelper.parseResponse<SlackRegisterUserResponse>(response)
    }

    async getUserNotificationPreference(userId: string): Promise<UserNotificationResponse> {
        const baseUrl = process.env.ENVIRONMENT === "STAGE" ? "http://rashi.stage.cure.fit.internal/" : "http://rashi.production.cure.fit.internal/"
        const url = baseUrl + "v1/user-notification-preference/" + "get-user-preference?userId=" + userId
        const response = await fetch(url, this.fetchHelper.get({
            headers: {},
            timeout: 4_000
        }))
        const res = await this.fetchHelper.parseResponse<UserNotificationResponse[]>(response)
        if (!_.isEmpty(res)) {
            const whatsappNotifcations = res.filter(value => value.dimensionValue.toLowerCase() === "whatsapp")
            if (_.isEmpty(whatsappNotifcations)) {
                return undefined
            }
            const reminders = whatsappNotifcations[0].children.filter(value => value.dimensionValue.toLowerCase() === "reminders")
            if (_.isEmpty(reminders)) {
                return undefined
            }
            const fitnessStatusFilter = reminders[0].children.filter(value => value.dimensionValue.toLowerCase() === "fitness")
            if (_.isEmpty(fitnessStatusFilter)) {
                return undefined
            }
            const motivations = whatsappNotifcations[0].children.filter(value => value.dimensionValue.toLowerCase() === "recommendations & more")
            if (_.isEmpty(motivations)) {
                return undefined
            }
            const motivationFitnessFilter = motivations[0].children.filter(value => value.dimensionValue.toLowerCase() === "fitness")
            if (_.isEmpty(motivationFitnessFilter)) {
                return undefined
            }
            return {
                id: whatsappNotifcations[0].id,
                dimensionValue: whatsappNotifcations[0].dimensionValue,
                dimensionName: whatsappNotifcations[0].dimensionName,
                subscriptionStatus: whatsappNotifcations[0].subscriptionStatus,
                children: [
                    {
                        id: fitnessStatusFilter[0].id,
                        dimensionValue: reminders[0].dimensionValue,
                        dimensionName: fitnessStatusFilter[0].dimensionName,
                        subscriptionStatus: fitnessStatusFilter[0].subscriptionStatus,
                        children: []
                    },
                    {
                        id: motivationFitnessFilter[0].id,
                        dimensionValue: motivations[0].dimensionValue,
                        dimensionName: motivationFitnessFilter[0].dimensionName,
                        subscriptionStatus: motivationFitnessFilter[0].subscriptionStatus,
                        children: []
                    }
                ]
            }
        }
        return undefined
    }

    public async disableNudgesFlagForFitness(userContext: UserContext): Promise<UserNotificationResponse[]> {
        const id = process.env.ENVIRONMENT === "STAGE" ? 26 : 24
        const baseUrl = process.env.ENVIRONMENT === "STAGE" ? "http://rashi.stage.cure.fit.internal/" : "http://rashi.production.cure.fit.internal/"
        const url = baseUrl + "v1/user-notification-preference/update-preference?userId=" + userContext.userProfile.userId + "&id=" + id + "&status=false"

        const response = await fetch(url, this.fetchHelper.post({
            body: "",
            headers: {},
            timeout: 4_000,
        }))
        return this.fetchHelper.parseResponse<UserNotificationResponse[]>(response)
    }

    async getFreemiumOffer(userID: string, req: express.Request): Promise<FreemiumUserOffer[]> {
        const baseUrl = process.env.ENVIRONMENT === "STAGE" ? "http://sf-lms.stage.cure.fit.internal/" : "http://lms.production.cure.fit.internal/"
        const url = `${baseUrl}v1/user/offer?userId=${userID}`
        const apiHeaders: any = req?.headers
        this.deleteUnwantedHeaders(apiHeaders)
        try {
            const response = await fetch(url, this.fetchHelper.get({
                headers: apiHeaders || {},
                agent: this.agent
            }))
            return await this.fetchHelper.parseResponse<FreemiumUserOffer[]>(response)
        }
        catch (error) {
            console.log(error)
            throw error
        }
    }

    async cultClasses(req: express.Request): Promise<ClassScheduleResponse> {
        const url = this.baseJavaServiceUrl() + "/cult/classes/v2" + encodeURI(ActionUtil.serializeAsQueryParams(req.query))
        const apiHeaders: any = req.headers
        this.deleteUnwantedHeaders(apiHeaders)
        const response = await fetch(url, this.fetchHelper.get({
            headers: apiHeaders,
            timeout: 6_000,
            agent: this.agent
        }))
        return this.fetchHelper.parseResponse<ClassScheduleResponse>(response)
    }
}
export default CFAPIJavaService
