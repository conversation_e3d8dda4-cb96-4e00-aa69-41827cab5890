import { inject, injectable } from "inversify"
import { DEFAULT_CACHE_REFRESH_TIME_IN_SECONDS, InMemoryCacheService } from "@curefit/memory-cache"
import { ICustomerIssueTypeReadOnlyDao, ISSUE_MODELS_TYPES } from "@curefit/issue-models"
import IICRMIssueService from "./ICRMIssueService"
import { CustomerIssueType, IssueCategory } from "@curefit/issue-common"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

interface CRMIssueCache {
    byCategory: Map<string, CustomerIssueType[]>
    byCode: Map<string, CustomerIssueType>
}

@injectable()
class CRMIssueService extends InMemoryCacheService<CRMIssueCache> implements IICRMIssueService {

    constructor(
        @inject(ISSUE_MODELS_TYPES.CustomerIssueTypeReadOnlyDao) private customerIssueDao: ICustomerIssueTypeReadOnlyDao,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
    ) {
        super(logger, 31 * 60)
        this.load("CRMIssueService")
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    loadData(): Promise<CRMIssueCache> {
        return this.customerIssueDao.retrieve().then(issues => {
            const sortedIssues = issues.sort((a, b) => {
                return a.code < b.code ? -1 : 1
            })
            const byCategory = new Map<string, CustomerIssueType[]>()
            const byCode = new Map<string, CustomerIssueType>()
            sortedIssues.forEach(issue => {
                if (!byCategory.has(issue.category)) {
                    byCategory.set(issue.category, [])
                }
                byCategory.get(issue.category).push(issue)
                byCode.set(issue.code, issue)
            })
            return { byCategory, byCode }
        })
    }

    getIssues(category: IssueCategory): Promise<CustomerIssueType[]> {
        return Promise.resolve(this.cache.byCategory.get(category))
    }

    getIssue(code: string): Promise<CustomerIssueType> {
        return Promise.resolve(this.cache.byCode.get(code))
    }

    getIssuesMap(): Promise<Map<string, CustomerIssueType[]>> {
        return Promise.resolve(this.cache.byCategory)
    }
}

export default CRMIssueService
