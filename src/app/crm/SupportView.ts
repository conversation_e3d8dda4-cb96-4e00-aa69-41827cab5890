import * as _ from "lodash"
import { ProductDetailPage, ProductListWidget } from "../common/views/WidgetView"
import { Constants } from "@curefit/base-utils"
import {
    HorizontalActionableCardListingWidget,
    HorizontalActionProductListWidget,
    SupportCardItem
} from "../page/PageWidgets"
import { Tenant } from "@curefit/user-common"
import { UserContext } from "@curefit/userinfo-common"
import AppUtil, { AppFont } from "../util/AppUtil"
import { OrderDetail } from "../order/OrderViewBuilder"

export const FAQ_PAGE_ID = "faq"
export const POLICY_PAGE_ID = "policy"
export const CONTACT_US_PAGE_ID = "contact_us"
export const SUPPORT_BASE = "support_base"
export const BOOKINGS = "bookings"

const POLICY_URL_MAP: any = {
    [Tenant.CUREFIT_APP]: {
        privacy: "http://static.cure.fit/privacy.html",
        terms: "http://static.cure.fit/terms.html",
        foss: "http://static.cure.fit/foss.html"
    },
    [Tenant.LIVEFIT_APP]: {
        privacy: "http://static.cure.fit/privacyInternational.html",
        terms: "http://static.cure.fit/termsInternational.html",
        foss: "http://static.cure.fit/fossInternational.html"
    },
}

export class SupportView extends ProductDetailPage {

    constructor(pageId: string, faqs: any[], supportSections: any[], numTickets: number, openTicketsView: SupportCardItem[], userContext: UserContext, orderDetails: OrderDetail[]) {
        super()

        switch (pageId) {
            case FAQ_PAGE_ID:
                this.setFAQWidgets(faqs)
                break

            case POLICY_PAGE_ID:
                this.setPolicyWidgets(userContext)
                break

            case CONTACT_US_PAGE_ID:
                this.setContactUsWidgets()
                break

            case SUPPORT_BASE:
                this.setSupportWidgets(supportSections, numTickets, openTicketsView, userContext, orderDetails)
                break

            case BOOKINGS:
                this.setBookingsWidgets()
                break
        }
    }

    setBookingsWidgets() {
        const bookingVerticals: any[] = []

        bookingVerticals.push({
            title: "Cult Center Classes",
            action: "curefit://supportlistpage?pageId=classes&title=Cult+Center+Classes"
        })
        bookingVerticals.push({
            title: "Play Sessions",
            action: "curefit://supportlistpage?pageId=sports&title=Play+Sessions"
        })
        bookingVerticals.push({
            title: "Gym Workouts",
            action: "curefit://supportlistpage?pageId=gymfitclasses&title=Gym+Workouts"
        })
        bookingVerticals.push({
            title: "OnePass Sessions",
            action: "curefit://supportlistpage?pageId=onepassclasses&title=OnePass+Sessions"
        })
        bookingVerticals.push(
            {
                title: "Therapy Consultations",
                action: `curefit://supportlistpage?pageId=consultations&title=Consultations&pageFrom=support`
            },
            {
                title: "Diagnostic Tests",
                action: "curefit://supportlistpage?pageId=diagnostictests&title=Diagnostic+Tests&pageFrom=support"
            })
        bookingVerticals.push({
            title: "Store Orders",
            action: "curefit://supportlistpage?pageId=store&title=Store+Orders"
        })
        const bookingsWidget: ProductListWidget = {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "ACTION_ROW",
            hideSepratorLines: false,
            header: undefined,
            items: bookingVerticals
        }

        this.widgets.push(bookingsWidget)
    }

    setSupportWidgets(supportSections: any[], numTickets: number, openTicketsView: SupportCardItem[], userContext: UserContext, orderDetails: OrderDetail[]) {
        const supportTicketsViewEnabled: boolean = AppUtil.isSupportTicketViewEnabled(userContext)
        const contactUsTicketCreationEnabled: boolean = AppUtil.isContactUsSupportTicketCreationEnabled(userContext)
        const isSupportOrdersViewEnabled = AppUtil.isSupportRecentOrdersViewEnabled(userContext)
        const isLoggedIn: boolean = userContext.sessionInfo.isUserLoggedIn

        if (isLoggedIn && supportTicketsViewEnabled && openTicketsView.length > 0) {
            const openTicketsWidget: HorizontalActionableCardListingWidget = {
                widgetType: "HORIZONTAL_ACTIONAL_CARD_LISTING_WIDGET",
                title: "Open Queries",
                titleStyling: {
                    color: "black",
                    fontSize: 18,
                    paddingBottom: isSupportOrdersViewEnabled ? 3 : 10,
                    paddingLeft: isSupportOrdersViewEnabled ? 18 : 37
                },
                hasDividerBelow: false,
                type: "OPEN_SUPPORT_TICKETS",
                cardItems: openTicketsView
            }
            this.widgets.push(openTicketsWidget)
        }

        if (isLoggedIn && supportSections.length && !isSupportOrdersViewEnabled) {
            const reportIssueWidget: ProductListWidget = {
                widgetType: "PRODUCT_LIST_WIDGET",
                type: "ACTION_ROW",
                hideSepratorLines: false,
                backgroundColor: "white",
                header: {
                    title: "Need Help",
                    color: "#000000"
                },
                items: supportSections
            }

            this.widgets.push(reportIssueWidget)
        }

        if (isLoggedIn && supportSections.length && isSupportOrdersViewEnabled) {
            const reportIssueWidget: HorizontalActionProductListWidget = {
                widgetType: "HORIZONTAL_ACTION_CARD_PRODUCT_LIST_WIDGET",
                hideSepratorLines: false,
                hasDividerBelow: true,
                backgroundColor: "white",
                dividerType: "SMALL",
                header: {
                    title: !_.isEmpty(orderDetails) ? "Need help with Recent activities?" : "What can we help you with?",
                    color: "#000000"
                },
                items: supportSections,
                cardItems: _.isEmpty(orderDetails) ? undefined : orderDetails
            }

            this.widgets.push(reportIssueWidget)
        }

        if (isLoggedIn && supportTicketsViewEnabled) {
            const allTicketsWidget: ProductListWidget = {
                widgetType: "PRODUCT_LIST_WIDGET",
                type: "ACTION_ROW",
                hideSepratorLines: false,
                hasDividerBelow: true,
                backgroundColor: "white",
                noBottomPadding: true,
                noTopPadding: true,
                dividerType: "SMALL",
                header: {
                    title: "My Queries",
                    color: (numTickets > 0) ? "#000000" : "#d1d1d1",
                    topAction: {
                        action: (numTickets > 0) ? {
                            actionType: "NAVIGATION",
                            url: "curefit://alltickets",
                            meta: { title: "My Queries" }
                        } : undefined,
                        actionString: undefined
                    },
                    rowStyling: {
                      paddingTop: 15,
                      paddingBottom: 5,
                    },
                    style: {
                      paddingLeft: isSupportOrdersViewEnabled ? 20 : 37,
                      fontSize: 14,
                      fontFamily: "BrandonText-Regular",
                      lineHeight: 22
                    },
                },
                style: {
                    paddingVertical: 0,
                },
                items: []
            }
            this.widgets.push(allTicketsWidget)
        }

        const faqPolicyWidget: ProductListWidget = {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "ACTION_ROW",
            hideSepratorLines: !isSupportOrdersViewEnabled,
            backgroundColor: (supportTicketsViewEnabled) ? "white" : "#e8ebf7",
            noTopPadding: true,
            header: undefined,
            items: [
                {
                    title: "FAQs",
                    action: "curefit://support?pageId=faq&launchNewInstance=true",
                    style: isSupportOrdersViewEnabled ? { paddingHorizontal: 20, paddingTop: 10, } : undefined,
                    textStyle: { fontSize: 14, fontFamily: AppFont.Regular, lineHeight: 22 }
                },
                {
                    title: "T&C and Privacy Policy",
                    action: "curefit://support?pageId=policy&launchNewInstance=true",
                    style: isSupportOrdersViewEnabled ? { paddingHorizontal: 20 } : undefined,
                    textStyle: { fontSize: 14, fontFamily: AppFont.Regular, lineHeight: 22 }
                }
            ]
        }

        this.widgets.push(faqPolicyWidget)
    }

    setFAQWidgets(faqs: any[]) {
        const faqWidget: ProductListWidget = {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "ACTION_ROW",
            hideSepratorLines: false,
            header: undefined,
            items: faqs
        }

        this.widgets.push(faqWidget)
    }

    setPolicyWidgets(userContext: UserContext ) {
        const tenant: Tenant = AppUtil.getTenantFromUserContext(userContext)
        const policyUrls = POLICY_URL_MAP[tenant] ? POLICY_URL_MAP[tenant] : POLICY_URL_MAP[Tenant.CUREFIT_APP]
        const policyWidget: ProductListWidget = {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "ACTION_ROW",
            hideSepratorLines: false,
            header: undefined,
            items: [
                {
                    title: "Privacy Policy",
                    action: `curefit://webview?uri=${policyUrls.privacy}&title=Privacy Policy`
                },
                {
                    title: "Terms and Conditions",
                    action: `curefit://webview?uri=${policyUrls.terms}&title=Terms and Conditions`
                },
                {
                    title: "Foss",
                    action: `curefit://webview?uri=${policyUrls.foss}&title=Foss`
                }
            ]
        }

        this.widgets.push(policyWidget)
    }

    setContactUsWidgets() {
        const contactUsWidget: ProductListWidget = {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "ACTION_ROW",
            hideSepratorLines: false,
            header: undefined,
            items: [
                {
                    title: "Email at",
                    subTitle: Constants.customerCareMail,
                    cardAction: {
                        actionType: "EXTERNAL_DEEP_LINK",
                        url: `mailto:${Constants.customerCareMail}`
                    }
                }
            ]
        }

        this.widgets.push(contactUsWidget)
    }
}
