import { inject, injectable } from "inversify"
import { LedgerEntry, Response } from "@curefit/reward-common"
import { HourM<PERSON>, Tenant } from "@curefit/base-common"
import { Product, ProductType } from "@curefit/product-common"
import * as _ from "lodash"
import {
    ClassProductState,
    ConsultationPackProductState,
    ConsultationProductState,
    CSAutomationType,
    CultParqProductState,
    CultPTPackProductState,
    CultPTSessionProductState,
    CultSocialState,
    CultSubscriptionPrePurchaseState,
    DiagnosticProductState,
    FitclubRewardState,
    GearProductState,
    GymfitCheckinProductState,
    GymfitProductState,
    HCProductState,
    IFaqCta,
    IssueActionType,
    IssueDetail,
    IssueProductState,
    IssueProductType,
    LCPackProductState,
    LCSessionProductState,
    ManagedCarePlanState,
    MealProductState,
    MindSubscriptionPrePurchaseState,
    PhysiotherapyPackProductState,
    PhysiotherapySessionProductState, PlaySessionState,
    SkinPackProductState,
    SkinSessionProductState,
    SubscriptionProductState
} from "@curefit/issue-common"
import { FoodBooking } from "@curefit/shipment-common"
import { SlotUtil } from "@curefit/eat-util"
import { CultBooking, CultMembership } from "@curefit/cult-common"
import { CULT_CLIENT_TYPES, ICultServiceOld as ICultService } from "@curefit/cult-client"
import { TimeUtil, Timezone } from "@curefit/util-common"

import {
    ALBUS_CLIENT_TYPES,
    BookingDetail,
    BundleSetupInfoV2,
    BundleStepInfo,
    DiagnosticsTestOrderResponse,
    IHealthfaceService
} from "@curefit/albus-client"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { ActionType, ManageOptionPayload } from "../common/views/WidgetView"
import { IIssueService } from "./IssueService"
import { ActionUtil, BASE_UTILS_TYPES, S3Helper } from "@curefit/base-utils"
import {
    GearOrderDetailObject,
    GearOrderShipment,
    ShipmentState,
    ShipmentType,
    TrackShipmentResponseV2
} from "@curefit/gear-common"
import { PackState } from "@curefit/eat-common"
import { CareUtil } from "../util/CareUtil"
import { UserContext } from "@curefit/userinfo-common"
import { BaseOrder, GymfitFitnessFulfilment } from "@curefit/order-common"
import { Membership } from "@curefit/membership-commons"
import { OMS_API_CLIENT_TYPES, IOrderService } from "@curefit/oms-api-client"
import { EAT_API_CLIENT_TYPES, IEatApiService } from "@curefit/eat-api-client"
import { BASE_TYPES, Logger } from "@curefit/base"
import * as mime from "mime-types"
import * as path from "path"
import { SignedUrlResponse } from "@curefit/user-client"
import {
    IExceptionTypes,
    IProcessExceptionResponse,
    IUserProfileService,
    REPORT_ISSUES_CLIENT_TYPES
} from "@curefit/report-issues-client"
import { GearService, GEARVAULT_CLIENT_TYPES } from "@curefit/gearvault-client"
import LiveUtil from "../util/LiveUtil"
import { ErrorFactory, HTTP_CODE } from "@curefit/error-client"
import { ErrorCodes } from "../error/ErrorCodes"
import { CFAPIGymfitCheckInState } from "../gymfit/GymfitBusiness"
import { Action } from "@curefit/vm-models"
import { Booking } from "@curefit/albus-client/dist/src/models"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { ConsultationProduct } from "@curefit/care-common"
import {
    CSAutomationBookingRequest,
    CSAutomationResponse,
    ICSAutomationService
} from "@curefit/report-issues-client/dist/src/ICSAutomationService"
import AppUtil, { SUPPORT_DEEP_LINK } from "../util/AppUtil"
import { SessionDetail } from "../fitsoSports/model/Models"
import { AlertError } from "../common/errors/AlertError"
import { BaseBooking as PlayBaseBooking } from "@curefit/sports-api-common"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"

enum RequestCallBackActionType {
    CANCEL = "CANCEL",
    PROCEED = "PROCEED"
}

interface RaiseTicketParamsView {
    issueDescription: string
    confirmationMessage: string
    // depreciated post 10.52 version
    optionalModalMeta?: {
        title: string,
        options: { title: string, meta: any }[]
    },
    // Used from 10.52 version
    requestCallBackMeta?: {
        ctaTitle: string,
        meta: any,
        modalText: string,
        modalActions: {title: string, type: RequestCallBackActionType}[]
    },
    quickSolveContent?: {
        title: string
        subTitle: string
        ctas?: Action[]
        faqCtas?: IFaqCta[]
    },
    ctas?: Action[]
}

interface QuickSolveContentParamsView {
    quickSolveContent?: {
        title: string
        subTitle: string
        ctas?: Action[]
        faqCtas?: IFaqCta[]
    }
}

export type SupportOptionsModalType = "NO_SHOW_ATTENDANCE_CHECK"
    | "NO_SHOW_REASONS_WHEN_ATTENDED" | "NO_SHOW_REASONS_WHEN_NOT_ATTENDED"
    | "CANCEL_CLASS_AFTER_CUTOFF" | "NO_SHOW_KEYWORD_CHECK"
    | "NO_SHOW_COACHING_GROUP_CLASSES" | "PLAY_NO_SHOW_REASONS_WHEN_ATTENDED"
    | "PLAY_NO_SHOW_ATTENDANCE_CHECK" | "PLAY_NO_SHOW_REASONS_WHEN_NOT_ATTENDED"
export type PreSubmissionFlowType = "NO_SHOW_EXCEPTION"

interface OptionsModal {
    title: string
    buttons?: Action[]
    choices?: Action[]
}

export interface IssueDetailViewAction {
    actionType: ActionType | IssueActionType | "SERVER_SIDE_ACTION"
    url?: string
    raiseTicketParams?: RaiseTicketParamsView
    quickSolveContentParams?: QuickSolveContentParamsView
    modalType?: SupportOptionsModalType
    meta?: IssueDetailViewMeta,
    issueId?: string,
    payload?: any
}

interface MealMeta {
    slotId: string
    deliveryDate: string
    fulfilmentId: string
    productIds: string[]
}

interface ClassMeta {
    bookingNumber: string,
    membershipID?: string,
}

interface GymCheckinMeta {
    checkinId: number
}

export interface IssueDetailViewMeta {
    mealMeta?: MealMeta
    cultClassMeta?: ClassMeta
    mindClassMeta?: ClassMeta
    fitsoClassMeta?: SessionDetail
    shipmentId?: string
    gymCheckinMeta?: GymCheckinMeta
    orderId?: string
}

export interface IssueDetailView {
    issueId: string
    issueDescription: string
    action: IssueDetailViewAction
}

export interface IssueDetailParams {
    productType: IssueProductType,
    productStates: IssueProductState[],
    meta: any
}

@injectable()
class IssueBusiness {

    static readonly THERAPY_RAISE_TICKET: IssueDetail = {
        issueDescription: "",
        actionType: "RAISE_TICKET",
        includeSources: ["CUREFIT_APP"],
        excludeSources: [],
        raiseTicketParams: {
            confirmationMessage: "Thank you for contacting us, we will get back to you as soon as possible",
            initiateCall: false,
            priority: "Medium",
            acknowledgementMessage: "Thank you for contacting us, we will get back to you as soon as possible",
        }
    }

    constructor(@inject(CUREFIT_API_TYPES.IssueService) private issueService: IIssueService,
        @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
        @inject(CULT_CLIENT_TYPES.MindFitService) private mindFitService: ICultService,
        @inject(OMS_API_CLIENT_TYPES.OrderService) private omsApiClient: IOrderService,
        @inject(EAT_API_CLIENT_TYPES.IEatApiService) private eatApiService: IEatApiService,
        @inject(GEARVAULT_CLIENT_TYPES.GearService) private gearService: GearService,
        @inject(BASE_UTILS_TYPES.S3Helper) private s3Helper: S3Helper,
        @inject(REPORT_ISSUES_CLIENT_TYPES.UserProfileService) private userProfileService: IUserProfileService,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        @inject(REPORT_ISSUES_CLIENT_TYPES.CSAutomationService) private csAutomationService: ICSAutomationService,
    ) {
    }

    async getChildIssues(issueId: string, meta: IssueDetailViewMeta, userContext: UserContext): Promise<IssueDetailView[]> {
        const issue = await this.issueService.getIssue(issueId)
        const childIssueIds = issue?.childIssueParams?.childIssues
        const childIssues = _.isEmpty(childIssueIds)
            ? []
            : await Promise.all(childIssueIds.map(async (childIssueId) => {
                return this.issueService.getIssue(childIssueId)
            }))
        const filteredChildIssues = this.issueService.getFilteredIssueDetails(childIssues, userContext)
        return this.transform(filteredChildIssues, meta, userContext)
    }

    async getIssues(productType: IssueProductType, productStates: IssueProductState[], meta: IssueDetailViewMeta, userContext: UserContext, tenant?: Tenant): Promise<IssueDetailView[]> {
        const issueDetails: IssueDetail[] = []
        tenant = tenant || Tenant.CUREFIT_APP
        if (!_.isEmpty(productStates)) {
            await Promise.all(
                productStates.map(async state => {
                    issueDetails.push(...await this.issueService.getIssuesByProductTypeAndState(productType, state, userContext, tenant))
                })
            )
        }
        return this.transform(_.uniqBy(issueDetails, issueDetail => (<any>issueDetail).id), meta, userContext)
    }

    async getIssue(issueId: string): Promise<IssueDetail> {
        return this.issueService.getIssue(issueId)
    }
    async performAction(userContext: UserContext, issueId: string, meta: IssueDetailViewMeta) {
        const issue = await this.issueService.getIssue(issueId)
        const userId = userContext.userProfile.userId
        switch (issue?.actionType) {
            case "SELF_SERVER_CANCEL":
                if (meta.cultClassMeta) {
                    return await this.processExceptionForUser(userId, IExceptionTypes.FORGOT_TO_CANCEL_CLASS, meta.cultClassMeta.bookingNumber, "FITNESS")
                }
                if (meta.mindClassMeta) {
                    return await this.processExceptionForUser(userId, IExceptionTypes.FORGOT_TO_CANCEL_CLASS, meta.mindClassMeta.bookingNumber, "MIND")
                }
                break
            case "SELF_SERVER_MARK_ATTENDANCE":
                if (meta.cultClassMeta) {
                    return await this.processExceptionForUser(userId, IExceptionTypes.FORGOT_TO_MARK_ATTENDANCE, meta.cultClassMeta.bookingNumber, "FITNESS")
                }
                if (meta.mindClassMeta) {
                    return await this.processExceptionForUser(userId, IExceptionTypes.FORGOT_TO_MARK_ATTENDANCE, meta.mindClassMeta.bookingNumber, "MIND")
                }
                break
            case "FORGOT_TO_CANCEL_MEAL":
                if (meta.mealMeta) {
                    return await this.processExceptionForUser(userId, IExceptionTypes.CANCEL_MEAL, meta.mealMeta.fulfilmentId, "FOOD")
                }
                break
            case "TRIGGER_REFUND":
                const refund = await this.omsApiClient.checkAndRefund(meta.mealMeta.fulfilmentId, "FOOD", TimeUtil.formatDateStringInTimeZone(meta.mealMeta.deliveryDate, userContext.userProfile.timezone))
                this.logger.info("Delivery date: " + meta.mealMeta.deliveryDate)
                if (refund.state === "APPLICABLE") {
                    switch (refund.refundStatus) {
                        case "REFUND_INITIATED":
                            return { message: "As a one time exception, we have processed the refund for this order. The amount should be credited to your account within 5-7 business days. " }
                        case "PAYMENT_REFUNDED":
                            return { message: "As a one time exception, we have processed the refund for this order." }
                        default:
                            return { message: "Not applicable" }
                    }
                } else {
                    return { message: "Not applicable" }
                }
        }
    }

    async performActionV2(userContext: UserContext, meta: IssueDetailViewMeta, type: CSAutomationType, reason?: string) {
        this.logger.info("performActionV2", {meta, type, reason})
        let issueId: string
        if (AppUtil.isProdLike) {
            switch (type) {
                case CSAutomationType.NO_SHOW_EXCEPTION:
                    issueId = "1881"
                    break

                case CSAutomationType.NO_SHOW_EXCEPTION_PLAY:
                    issueId = "1943"
                    break

                case CSAutomationType.CANCEL_CLASS:
                    issueId = "1882"
                    break
            }
        } else {
            switch (type) {
                case CSAutomationType.NO_SHOW_EXCEPTION:
                    issueId = "1439"
                    break

                case CSAutomationType.NO_SHOW_EXCEPTION_PLAY:
                    issueId = "1890"
                    break

                case CSAutomationType.CANCEL_CLASS:
                    issueId = "1440"
                    break
            }
        }
        const issueDetail: IssueDetail = await this.getIssue(issueId)
        let failureAction: any = this.toIssueDetailViewAction(issueDetail, meta)
        failureAction = {
            ...failureAction,
            issueId,
        }

        const validMeta = this.validateRequestParams(meta, type)
        if (!validMeta) {
            return {
                success: false,
                failureAction: failureAction,
            }
        }
        const CSAutomationRequestParams: CSAutomationBookingRequest =
                this.getCSAutomationRequestParams(meta, type, userContext, reason)
        let response: CSAutomationResponse
        try {
            switch (type) {
                case CSAutomationType.NO_SHOW_EXCEPTION:
                    this.logger.info("performActionV2", {type})
                    response = await this.csAutomationService.processNoShowException(CSAutomationRequestParams)
                    break
                case CSAutomationType.NO_SHOW_EXCEPTION_PLAY:
                    this.logger.info("performActionV2", {type})
                    response = await this.csAutomationService.processPlayNoShowException(CSAutomationRequestParams)
                    break
                case CSAutomationType.CANCEL_CLASS:
                    response = await this.csAutomationService.cancelBooking(CSAutomationRequestParams)
                    response = {
                        ...response,
                        message: "Don't worry! We have gone ahead and cancelled your class without penalty!",
                    }
                    break
            }
        } catch (e) {
            this.logger.error("performActionV2", e)
            return {
                success: false,
                failureAction: failureAction
            }
        }
        this.logger.info("performActionV2", {response, failureAction})
        if (
            response
            && !response.success
            && (type === CSAutomationType.NO_SHOW_EXCEPTION || type === CSAutomationType.NO_SHOW_EXCEPTION_PLAY)
        ) {
            try {
                throw this.errorFactory.withCode("No Show Reversal Error", HTTP_CODE.PRECONDITION_FAILED).build()
            } catch (err) {
                throw new AlertError("No Show Reversal Error", response.message, [], err)
            }
        }

        return {
            ...response,
            failureAction,
            "reason": reason
        }
    }

    public validateRequestParams(
        meta: IssueDetailViewMeta,
        type: CSAutomationType
    ): boolean {
        switch (type) {
            case CSAutomationType.NO_SHOW_EXCEPTION_PLAY:
                if (!meta.fitsoClassMeta.bookingId || !meta.fitsoClassMeta.membershipId) {
                    return false
                }
                return true
            case CSAutomationType.NO_SHOW_EXCEPTION:
            case CSAutomationType.CANCEL_CLASS:
            default:
                if (!meta.cultClassMeta.membershipID || !meta.cultClassMeta.bookingNumber) {
                    return false
                }
                return true
        }
    }

    public getCSAutomationRequestParams(
        meta: IssueDetailViewMeta,
        type: CSAutomationType,
        userContext: UserContext,
        reason?: string
    ): CSAutomationBookingRequest {
        switch (type) {
            case CSAutomationType.NO_SHOW_EXCEPTION_PLAY:
                return {
                    bookingNumber: meta.fitsoClassMeta.bookingId,
                    userId: userContext.userProfile.userId,
                    membershipId: String(meta.fitsoClassMeta.membershipId),
                    reason,
                }
            case CSAutomationType.NO_SHOW_EXCEPTION:
            case CSAutomationType.CANCEL_CLASS:
            default:
                return {
                    membershipId: meta.cultClassMeta.membershipID,
                    bookingNumber: meta.cultClassMeta.bookingNumber,
                    userId: userContext.userProfile.userId,
                    reason,
                }
        }
    }

    public getCommonTherapyPackIssues(): IssueDetailView[] {
        const views: IssueDetailView[] = []

        let issueDetail: IssueDetail = IssueBusiness.THERAPY_RAISE_TICKET
        issueDetail.issueDescription = "I’m not able to cancel/reschedule the session"
        views.push(this.toIssueDetailView(issueDetail))

        issueDetail = IssueBusiness.THERAPY_RAISE_TICKET
        issueDetail.issueDescription = "I want help extending my pack"
        views.push(this.toIssueDetailView(issueDetail))

        issueDetail = IssueBusiness.THERAPY_RAISE_TICKET
        issueDetail.issueDescription = "My issue isn’t listed here"
        views.push(this.toIssueDetailView(issueDetail))

        return views
    }

    public getSupportGroupSessionIssues(): IssueDetailView[] {
        const views: IssueDetailView[] = []

        let issueDetail: IssueDetail = IssueBusiness.THERAPY_RAISE_TICKET
        issueDetail.issueDescription = "What are support groups?"
        views.push(this.toIssueDetailView(issueDetail))

        issueDetail = IssueBusiness.THERAPY_RAISE_TICKET
        issueDetail.issueDescription = "What can I expect in a session?"
        views.push(this.toIssueDetailView(issueDetail))

        issueDetail = IssueBusiness.THERAPY_RAISE_TICKET
        issueDetail.issueDescription = "I’m not able to cancel/reschedule the session"
        views.push(this.toIssueDetailView(issueDetail))

        issueDetail = IssueBusiness.THERAPY_RAISE_TICKET
        issueDetail.issueDescription = "Not able to join session"
        views.push(this.toIssueDetailView(issueDetail))

        issueDetail = IssueBusiness.THERAPY_RAISE_TICKET
        issueDetail.issueDescription = "My issue isn’t listed here"
        views.push(this.toIssueDetailView(issueDetail))

        return views
    }

    public getSupportGroupPackIssues(): IssueDetailView[] {
        const views: IssueDetailView[] = []

        let issueDetail: IssueDetail = IssueBusiness.THERAPY_RAISE_TICKET
        issueDetail.issueDescription = "What are support groups?"
        views.push(this.toIssueDetailView(issueDetail))

        issueDetail = IssueBusiness.THERAPY_RAISE_TICKET
        issueDetail.issueDescription = "What can I expect in a session?"
        views.push(this.toIssueDetailView(issueDetail))

        issueDetail = IssueBusiness.THERAPY_RAISE_TICKET
        issueDetail.issueDescription = "I’m not able to book slot"
        views.push(this.toIssueDetailView(issueDetail))

        issueDetail = IssueBusiness.THERAPY_RAISE_TICKET
        issueDetail.issueDescription = "I want help extending my pack"
        views.push(this.toIssueDetailView(issueDetail))

        issueDetail = IssueBusiness.THERAPY_RAISE_TICKET
        issueDetail.issueDescription = "My issue isn’t listed here"
        views.push(this.toIssueDetailView(issueDetail))
        return views
    }

    private async processExceptionForUser(userId: string, issueType: IExceptionTypes, bookingId: string, productType: ProductType) {
        const exceptionResponse: IProcessExceptionResponse = await this.userProfileService.processException({
            userId: userId,
            name: issueType,
            bookingId: bookingId,
            tenant: this.getTenantToProcessException(productType)
        })
        let message = ""
        if (exceptionResponse.type === IExceptionTypes.FORGOT_TO_MARK_ATTENDANCE || exceptionResponse.type === IExceptionTypes.FORGOT_TO_CANCEL_CLASS) {
            if (exceptionResponse.description === "ATTENDANCE_MARKED") {
                message = "We have reverted your no-show and provided you an exception for just this instance. Please note that we have discontinued free No-Shows. To avoid a No-Show in the future, please " + (exceptionResponse.type === "FORGOT_TO_MARK_ATTENDANCE" ? "mark your attendance before the class starts." : "cancel your class within the allotted time.")
            } else if (exceptionResponse.description === "ALREADY_PROCESSED") {
                message = "We see that you have been already provided an exception for this class. We will this not be able to provide an exception again. To avoid a No-Show in the future, please " + (exceptionResponse.type === "FORGOT_TO_MARK_ATTENDANCE" ? "mark your attendance before the class starts." : "cancel your class within the allotted time.")
            } else {
                message = "We have discontinued free No-Shows. We will thus not be able to make an exception. To avoid a No-Show in the future, please " + (exceptionResponse.type === "FORGOT_TO_MARK_ATTENDANCE" ? "mark your attendance before the class starts." : "cancel your class within the allotted time.")
            }
        } else if (exceptionResponse.type === IExceptionTypes.CANCEL_MEAL) {
            if (exceptionResponse.description === "PACKAGING_STARTED") {
                message = "Your meal preparation has already started, we cannot cancel your order."
            } else if (exceptionResponse.description === "MEAL_CANCELLATION_PROCESSED") {
                message = "Your order is cancelled, refund will be processed to source account within 7 working days."
            } else if (exceptionResponse.description === "ALREADY_PROCESSED") {
                message = "We see your order is already cancelled, refund will be processed to source account within 7 working days."
            } else {
                message = "This order cannot be cancelled."
            }
        }
        return { message: message }
    }

    private getTenantToProcessException(productType: ProductType) {
        switch (productType) {
            case "FITNESS":
                return "cult"
            case "FOOD":
                return "eat"
            case "MIND":
                return "mind"
        }
    }

    private getFailureAction(text: string) {
        return {
            "actionType": "SHOW_CUSTOM_LIST_PAGE",
            "meta": {
            "canvasTheme": "GRAYISH_NAVY",
                "showCrossButton": true,
                "pageType": "custom_page",
                "blurEnabled": false,
                "showTitleBar": false,
                "pageId": "no_show_reversal_failure",
                "pageName": "No Show Reversal Failure",
                "widgets": [
                {
                    "widgetType": "CF_MEDIA_WIDGET",
                    "mediaData": {
                        "mediaUrl": "image/m1_experience/test/Cross.json",
                        "type": "lottie",
                        "width": 150,
                        "height": 150,
                        "repeat": false,
                        "topPadding": 150,
                    }
                },
                {
                    "widgetType": "RICH_TEXT_LIST_WIDGET",
                    "defaultStyle": "H1",
                    "defaultColor": "#FFFFFF",
                    "textAlign": "center",
                    "listItems": [
                        [
                            {
                                "text": text
                            }
                        ]
                    ],
                    "maxLines": 10,
                    "sideSpacing": 50.0
                }
            ]
            }
        }
    }

    async getMealBookingIssueParams(foodBooking: FoodBooking, timezone: Timezone = TimeUtil.IST_TIMEZONE): Promise<IssueDetailParams> {
        const startDate = TimeUtil.getMomentForDate(foodBooking.deliveryWindow.start, timezone)
        const endDate = TimeUtil.getMomentForDate(foodBooking.deliveryWindow.end, timezone)
        const startTime: HourMin = {
            hour: startDate.hours(),
            min: startDate.minutes()
        }
        const endTime: HourMin = {
            hour: endDate.hours(),
            min: endDate.minutes()
        }
        const isSubscriptionMeal = foodBooking.packId ? true : false
        const isOnDemandMeal = foodBooking.deliveryType === "ON_DEMAND" ? true : false
        const productType: IssueProductType = isSubscriptionMeal ? "EAT_SUBSCRIPTION_MEAL" : (isOnDemandMeal ? "EAT_ONDEMAND_MEAL" : "EAT_SLOTTED_MEAL")
        const isCancelCutOffPassed: boolean = foodBooking.deliveryType === "ON_DEMAND" ? true : SlotUtil.isCancelCutOffPassed(foodBooking.deliveryDate, timezone, foodBooking.deliverySlot ? foodBooking.deliverySlot.slotId : undefined, foodBooking.deliveryWindow)
        let isSlotEnded: boolean
        if (foodBooking.deliveryType === "ON_DEMAND") {
            isSlotEnded = foodBooking.originalEta ? new Date(foodBooking.originalEta).getTime() < new Date().getTime() : false
        } else {
            isSlotEnded = SlotUtil.isDeliveryWindowEnded(foodBooking.deliveryDate, endTime, timezone)
        }
        let state: MealProductState
        if (foodBooking.state === "DELIVERED") {
            state = "DELIVERED"
        } else if (foodBooking.state === "DRIVING") {
            state = !isSlotEnded ? "DRIVING_WITHIN_SLOT" : "DRIVING_BEYOND_SLOT"
        } else if (foodBooking.state === "DELIVERING") {
            state = !isSlotEnded ? "DELIVERING_WITHIN_SLOT" : "DELIVERING_BEYOND_SLOT"
        } else if (foodBooking.state === "REJECTED") {
            const isValidRefund = await this.eatApiService.isValidRefund(foodBooking.fulfilmentId, foodBooking.deliveryDate)
            this.logger.info("IsValidRefund: " + isValidRefund)
            state = isValidRefund ? "REJECTED_REFUND_APPLICABLE" : "REJECTED_REFUND_NOT_APPLICABLE"
        } else {
            state = isCancelCutOffPassed ? "AFTER_CUT_OFF" : "BEFORE_CUT_OFF"
        }
        return {
            productType,
            productStates: [state],
            meta: {
                mealMeta: {
                    deliveryDate: foodBooking.deliveryDate,
                    fulfilmentId: foodBooking.fulfilmentId,
                    deliveryWindow: {
                        startTime: startTime,
                        endTime: endTime
                    },
                    productIds: _.map(foodBooking.products, product => product.productId)
                },
                shipmentId: foodBooking.shipmentId,
                orderId: foodBooking.orderId
            }
        }
    }

    async getImageSignedUrl(fileName: string, config: string): Promise<SignedUrlResponse> {
        let url = ""
        let s3FileName = ""
        let mimeType = ""
        let fileBaseName = ""
        const bucketResp = this.getBucketToUpload(config)
        if (_.isEmpty(bucketResp.bucketName)) {
            return null
        }
        let s3Url = `https://${bucketResp.bucketName}.s3.amazonaws.com`
        if (!_.isEmpty(bucketResp.folder)) {
            s3Url = s3Url + "/" + bucketResp.folder
        }
        let completeFilePath = ""
        if (fileName) {
            try {
                const extName = path.extname(fileName)
                fileBaseName = extName ? fileName.slice(0, fileName.indexOf(extName)) : fileName
                const timeStamp = TimeUtil.getCurrentEpoch()
                mimeType = mime.lookup(fileName) as string
                s3FileName = fileBaseName ? `${fileBaseName}-${timeStamp}${extName}` : ""
                const bucketName = !_.isEmpty(bucketResp.folder) ? bucketResp.bucketName + "/" + bucketResp.folder : bucketResp.bucketName
                if (s3FileName) {
                    const params = {
                        Bucket: bucketName,
                        Key: s3FileName,
                        ACL: "public-read"
                    }
                    url = await this.s3Helper.getSignedUrlWithPutObject(params)
                    completeFilePath = `${s3Url}/${s3FileName}`
                }
                this.logger.info("s3fileName is ", s3FileName)
                return {
                    url,
                    fileName: completeFilePath,
                    mimeType
                }
            } catch (error) {
                this.logger.error("error in getting signed url is : ", error)
                throw this.errorFactory.withCode(ErrorCodes.S3_SIGNED_URL_CREATION_ERR, 500).withDebugMessage("Error while creating a signed url").build()
            }
        } else {
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid request params in getting image signed url").build()
        }
    }

    private getBucketToUpload(config: string): { bucketName: string, folder: string } {
        let bucketName = undefined
        let folder = undefined
        switch (config) {
            case "report-issues":
                bucketName = process.env.ENVIRONMENT === "PRODUCTION" ? "cf-app-upload" : "cf-app-upload"
                folder = process.env.ENVIRONMENT === "PRODUCTION" ? "REPORTED_ISSUES" : "REPORTED_ISSUES"
                break
        }
        return { bucketName: bucketName, folder: folder }
    }

    async getMealSubscriptionIssues(packState: PackState, orderId: string, userContext: UserContext): Promise<IssueDetailView[]> {
        const productType: IssueProductType = "EAT_SUBSCRIPTION"
        let state: SubscriptionProductState = "ON_GOING_SUBSCRIPTION"
        if (packState === "PAUSED") {
            state = "PAUSED_SUBSCRIPTION"
        } else if (packState === "HALTED" || packState === "CANCELLED") {
            state = "CANCELLED_SUBSCRIPTION"
        } else if (packState === "COMPLETED") {
            state = "COMPLETED_SUBSCRIPTION"
        }
        const issuesResult = await this.issueService.getIssuesByProductTypeAndState(productType, state, userContext)
        return this.transform(issuesResult, {
            orderId: orderId
        })
    }

    getFitclubRewardIssueParams(reward: LedgerEntry<Response>): IssueDetailParams {
        const productType: IssueProductType = "FIT_CLUB_REWARD"
        const state: FitclubRewardState = "FITCASH"
        return {
            productType,
            productStates: [state],
            meta: {
                orderId: reward.context ? reward.context.orderId : null
            }
        }
    }
    getCultOrMindCenterBookingIssueParams(booking: CultBooking, productType: ProductType): IssueDetailParams {
        const issueProductType: IssueProductType = productType === "FITNESS" ? "CULT_SESSION" : "MIND_SESSION"
        let state: ClassProductState
        if (issueProductType === "MIND_SESSION") {
            state = "BEFORE_CUT_OFF"
            if (!booking.bookingNumber) {
                // handling for waitlist class
                state = "WAITLISTED"
            } else {
                if (booking.label === "Cancelled") {
                    state = "CANCELLED"
                } else if (!booking.isBookingCancellable) {
                    state = "AFTER_CUT_OFF"
                }
            }
        } else if (issueProductType === "CULT_SESSION") {
            if (!booking.bookingNumber) {
                state = "WAITLISTED"
            } else {
                switch (booking.label) {
                    case "Cancelled":
                        state = "CANCELLED"
                        break
                    case "Completed":
                        state = "COMPLETED"
                        break
                    case "Ongoing":
                        state = "ONGOING"
                        break
                    case "Dropped out":
                        state = "DROPPED_OUT"
                        break
                    case "No Show":
                        state = "MISSED"
                        break
                    case "Upcoming":
                        if (booking.isBookingCancellable) {
                            state = "CONFIRMED_BEFORE_CUTOFF"
                        } else {
                            state = "CONFIRMED_AFTER_CUTOFF"
                        }
                        break
                    default:
                       state = "CONFIRMED_BEFORE_CUTOFF"
                }
            }
        }

        const issueMeta: IssueDetailViewMeta = {
            orderId: booking.orderID ? booking.orderID.toString() : undefined,
            shipmentId: booking.bookingNumber
        }
        if (productType === "FITNESS") {
            issueMeta.cultClassMeta = {
                bookingNumber: booking.bookingNumber || booking.wlBookingNumber,
                membershipID: booking.membershipID?.toString()
            }
        } else if (productType === "MIND") {
            issueMeta.mindClassMeta = {
                bookingNumber: booking.bookingNumber || booking.wlBookingNumber
            }
        }
        return {
            productType: issueProductType,
            productStates: [state],
            meta: issueMeta
        }
    }

    async getDigitalMembershipIssues(userContext: UserContext, membership: Membership, product: Product): Promise<IssueDetailView[]> {
        const issueProductType: IssueProductType = "CF_LIVE_SUBSCRIPTION"
        const membershipState = LiveUtil.getLiveFitMembershipState(userContext, membership, product)
        let state: SubscriptionProductState = "ON_GOING_SUBSCRIPTION"
        if (membershipState === "UPCOMING") {
            state = "UPCOMING_SUBSCRIPTION"
        } else if (membershipState === "EXPIRED") {
            state = "COMPLETED_SUBSCRIPTION"
        }
        const issueResult = await this.issueService.getIssuesByProductTypeAndState(issueProductType, state, userContext)
        return this.transform(issueResult, {
            orderId: membership.orderId ? membership.orderId.toString() : undefined,
        }, userContext)
    }

    async getDigitalOrderIssues(userContext: UserContext, orderId: string): Promise<IssueDetailView[]> {
        const issueProductType: IssueProductType = "CF_LIVE_SUBSCRIPTION"
        const state: SubscriptionProductState = "COMPLETED_SUBSCRIPTION"
        const issueResult = await this.issueService.getIssuesByProductTypeAndState(issueProductType, state, userContext)
        return this.transform(issueResult, {
            orderId,
        }, userContext)
    }

    async getCultPackPreRegSubscriptionIssues(userContext: UserContext): Promise<IssueDetailView[]> {
        const issueProductType: IssueProductType = "CULT_SUBSCRIPTION"
        const state: SubscriptionProductState = "PRE_REG"
        const issuesResult = await this.issueService.getIssuesByProductTypeAndState(issueProductType, state, userContext)
        return this.transform(issuesResult)
    }

    async getIssuesByFilter(userContext: UserContext, issueProductType: IssueProductType, state: IssueProductState, orderId: string): Promise<IssueDetailView[]> {
        const issuesResult = await this.issueService.getIssuesByProductTypeAndState(issueProductType, state, userContext)
        return this.transform(issuesResult, { orderId }, userContext)
    }

    async getCultOrMindSubscriptionIssues(userContext: UserContext, membership: CultMembership, productType: ProductType): Promise<IssueDetailView[]> {
        const issueProductType: IssueProductType = productType === "FITNESS" ? "CULT_SUBSCRIPTION" : "MIND_SUBSCRIPTION"
        let state: SubscriptionProductState = "ON_GOING_SUBSCRIPTION"
        if (membership.state === "PAUSED") {
            state = "PAUSED_SUBSCRIPTION"
        } else if (membership.state === "ADVANCE_PAID") {
            state = "PRE_REG"
        } else if (membership.state === "MANUAL_CANCELLED") {
            state = "CANCELLED_SUBSCRIPTION"
        } else if (membership.state === "PURCHASED") {
            if (membership.endDate < TimeUtil.todaysDate(userContext.userProfile.timezone)) {
                state = "COMPLETED_SUBSCRIPTION"
            } else {
                state = "ON_GOING_SUBSCRIPTION"
            }
        }
        const issuesResult = await this.issueService.getIssuesByProductTypeAndState(issueProductType, state, userContext)
        return this.transform(issuesResult, {
            orderId: membership.orderID ? membership.orderID.toString() : undefined,
        }, userContext)
    }

    async getCultOrMindSubscriptionIssuesV2(userContext: UserContext, membership: Membership, productType: ProductType): Promise<IssueDetailView[]> {
        const issueProductType: IssueProductType = productType === "FITNESS" ? "CULT_SUBSCRIPTION" : "MIND_SUBSCRIPTION"
        let state: SubscriptionProductState = "ON_GOING_SUBSCRIPTION"
        if (membership.status === "PAUSED") {
            state = "PAUSED_SUBSCRIPTION"
        } else if (membership.status === "CANCELLED") {
            state = "CANCELLED_SUBSCRIPTION"
        } else if (membership.status === "PURCHASED") {
            if (TimeUtil.formatEpochInTimeZoneDateFns(userContext.userProfile.timezone, membership.end, "yyyy-MM-dd") < TimeUtil.todaysDate(userContext.userProfile.timezone)) {
                state = "COMPLETED_SUBSCRIPTION"
            } else {
                state = "ON_GOING_SUBSCRIPTION"
            }
        }
        const issuesResult = await this.issueService.getIssuesByProductTypeAndState(issueProductType, state, userContext)
        return this.transform(issuesResult, {
            orderId: membership.orderId ? membership.orderId.toString() : undefined,
        }, userContext)
    }

    async getCultOrMindSubscriptionPrePurchaseIssues(fitnessPack: OfflineFitnessPack, userContext: UserContext): Promise<IssueDetailView[]> {
        const isSelectPack: boolean = false // OLD SELECT is deprecated

        const issueProductType: IssueProductType = fitnessPack.productType === "FITNESS" ? "CULT_SUBSCRIPTION_PREPURCHASE" : "MIND_SUBSCRIPTION_PREPURCHASE"
        let state: CultSubscriptionPrePurchaseState | MindSubscriptionPrePurchaseState

        if (issueProductType === "CULT_SUBSCRIPTION_PREPURCHASE") {
            if (isSelectPack) {
                state = "SELECT"
            } else {
                state = "UNLIMITED"
            }
        } else {
            state = "UNLIMITED"
        }

        const issuesResult = await this.issueService.getIssuesByProductTypeAndState(issueProductType, state, userContext)
        return this.transform(issuesResult)
    }

    async getCultOrMindJuniorSubscriptionIssues(userContext: UserContext, membership: CultMembership, productType: ProductType): Promise<IssueDetailView[]> {
        const issueProductType: IssueProductType = productType === "FITNESS" ? "CULT_JUNIOR_SUBSCRIPTION" : "MIND_JUNIOR_SUBSCRIPTION"
        let state: SubscriptionProductState = "ON_GOING_SUBSCRIPTION"
        if (membership.state === "PAUSED") {
            state = "PAUSED_SUBSCRIPTION"
        } else if (membership.state === "ADVANCE_PAID") {
            state = "PRE_REG"
        } else if (membership.state === "MANUAL_CANCELLED") {
            state = "CANCELLED_SUBSCRIPTION"
        } else if (membership.state === "PURCHASED") {
            if (membership.endDate < TimeUtil.todaysDate(userContext.userProfile.timezone)) {
                state = "COMPLETED_SUBSCRIPTION"
            } else if (membership.startDate > TimeUtil.todaysDate(userContext.userProfile.timezone)) {
                state = "UPCOMING_SUBSCRIPTION"
            } else {
                state = "ON_GOING_SUBSCRIPTION"
            }
        }
        const issuesResult = await this.issueService.getIssuesByProductTypeAndState(issueProductType, state, userContext)
        return this.transform(issuesResult, {
            orderId: membership.orderID ? membership.orderID.toString() : undefined,
        }, userContext)
    }

    isGearOrderDeliveryOutsideSLA(shipmentType: ShipmentType, shipment: GearOrderShipment): boolean {
        const gearInTransitStates = [
            ShipmentState.PENDING,
            ShipmentState.READY,
            ShipmentState.PACKED,
            ShipmentState.SHIPPED,
            ShipmentState.OUT_FOR_DELIVERY,
            ShipmentState.FAILED_DELIVERY
        ]
        const currentTimeInEpochInMillis = new Date().getTime()
        return (shipmentType === ShipmentType.FORWARD &&
            new Date(shipment.original_customer_edd).getTime() < currentTimeInEpochInMillis &&
            gearInTransitStates.includes(shipment.state))
    }

    getGearIssueParams(gearOrderDetailObject: GearOrderDetailObject): IssueDetailParams {
        const productType: IssueProductType = "CULT_GEAR"
        const productStates: GearProductState[] = []

        const hasSingleShipment = gearOrderDetailObject.shipments.length === 1
        const {
            isOrderCancellable,
            deliveryOutsideEdd,
            deliveryWithinEdd,
            hasReturnShipment,
            hasExchangeShipment,
            orderDelivered,
            isReturnExchangeCancelable
        } = gearOrderDetailObject.shipments.reduce(
            (accumulator: any, shipment: GearOrderShipment) => {
                let {
                    isOrderCancellable,
                    deliveryOutsideEdd,
                    deliveryWithinEdd,
                    hasReturnShipment,
                    hasExchangeShipment,
                    orderDelivered,
                    isReturnExchangeCancelable
                } = accumulator

                const gearShipment: TrackShipmentResponseV2 = {
                    ...shipment
                }

                const shipmentStateHistory = shipment.history && shipment.history.map((hist: any) => hist.state) || []

                // An order is cancellable when all the inventory units in all forward shipments are
                // cancellable.
                isOrderCancellable = !isOrderCancellable ?
                    false :
                    gearShipment.shipment_type === ShipmentType.FORWARD && shipment.inventory_units && shipment.inventory_units.every(
                        (inventoryUnit) => inventoryUnit.cancelable
                    )

                deliveryOutsideEdd = deliveryOutsideEdd || this.isGearOrderDeliveryOutsideSLA(gearShipment.shipment_type, shipment)

                // Checking for gearShipment.state || shipmentHistory since shipment history is not
                // available for /allOrders. So using gearShipment.state as fallback.
                deliveryWithinEdd = !deliveryWithinEdd ? false :
                    !deliveryOutsideEdd && (gearShipment.shipment_type === ShipmentType.FORWARD &&
                        (gearShipment.state === ShipmentState.DELIVERED || shipmentStateHistory.includes(ShipmentState.DELIVERED)))

                // When a shipment has RETURN_APPROVED in its state history and the return is not cancelled.
                const isReturn = shipmentStateHistory.includes(ShipmentState.RETURN_APPROVED) &&
                    !shipmentStateHistory.includes(ShipmentState.PICKUP_CANCELED)

                hasReturnShipment = hasReturnShipment || isReturn

                // When a shipment has EXCHANGE_APPROVED in its state history and the exchange is not cancelled.
                const isExchange = shipmentStateHistory.includes(ShipmentState.EXCHANGE_INITIATED) &&
                    !shipmentStateHistory.includes(ShipmentState.PICKUP_CANCELED)

                hasExchangeShipment = hasExchangeShipment || isExchange

                // Order is delivered when all the shipments (forward) are DELIVERED and there are no active return / exchange.
                orderDelivered = !orderDelivered ? false : !(isExchange || isReturn) && shipmentStateHistory.includes(ShipmentState.DELIVERED)

                isReturnExchangeCancelable = !isReturnExchangeCancelable ? false : (isReturn || isExchange) ?
                    shipment.inventory_units && shipment.inventory_units.every(
                        (inventoryUnit) => inventoryUnit.cancelable
                    ) : false

                return ({
                    ...accumulator,
                    isOrderCancellable,
                    deliveryOutsideEdd,
                    deliveryWithinEdd,
                    hasReturnShipment,
                    hasExchangeShipment,
                    orderDelivered
                })
            }, {
            isOrderCancellable: true,
            deliveryOutsideEdd: false,
            deliveryWithinEdd: true,
            hasReturnShipment: false,
            hasExchangeShipment: false,
            orderDelivered: true,
            isReturnExchangeCancelable: true
        }
        )

        if (hasSingleShipment) {
            productStates.push("SINGLE_SHIPMENT_ORDER")
        } else {
            productStates.push("MULTI_SHIPMENT_ORDER")
        }

        if (isOrderCancellable) {
            productStates.push("CANCELLABLE_ORDER")
        } else {
            productStates.push("NON_CANCELLABLE_ORDER")
        }

        if (deliveryOutsideEdd) {
            productStates.push("ORDER_DELIVERY_AFTER_SLA")
        }

        if (deliveryWithinEdd) {
            productStates.push("ORDER_DELIVERY_WITHIN_SLA")
        }

        if (orderDelivered) {
            productStates.push("ORDER_DELIVERED")
        }

        if (hasReturnShipment) {
            productStates.push("RETURN_SHIPMENT_ORDER")
        }

        if (hasExchangeShipment) {
            productStates.push("EXCHANGE_SHIPMENT_ORDER")
        }

        if (!hasExchangeShipment && !hasReturnShipment && !orderDelivered) {
            productStates.push("ORDER_PLACED")
        }
        if ((hasReturnShipment || hasExchangeShipment)) {
            productStates.push(isReturnExchangeCancelable ? "RETURN_EXCHANGE_CANCELLABLE" : "RETURN_EXCHANGE_NOT_CANCELLABLE")
        }

        const meta = {
            orderId: gearOrderDetailObject.external_service_order_id
        }
        return {
            productType, productStates, meta
        }
    }

    getConsulationIssueParams(bookingDetail: BookingDetail): IssueDetailParams {
        const productType: IssueProductType = (bookingDetail.booking.subCategoryCode === "CF_ONLINE_CONSULTATION"
            || bookingDetail.booking.subCategoryCode === "EXTERNAL_ONLINE_CONSULTATION") ? "CARE_TELE_CONSULTATION" : "CARE_INCENTER_CONSULTATION"

        let state: ConsultationProductState = "CONSULTATION_BOOKED"
        const currentTime = new Date().getTime()
        if (bookingDetail.booking.status === "CANCELLED") {
            state = "CANCELLED"
        } else if (bookingDetail.booking.status === "COMPLETED") {
            state = "POST_CONSULTATION"
            if (bookingDetail.consultationOrderResponse.followUpContext
                && !_.isEmpty(bookingDetail.consultationOrderResponse.followUpContext.followupMembershipDetails)) {
                const followUpMembershipDetail = bookingDetail.consultationOrderResponse.followUpContext.followupMembershipDetails[0]
                if (followUpMembershipDetail.hasFollowupMembership && followUpMembershipDetail.hasValidityExpired) {
                    state = "POST_FREE_FOLLOWUP_CUTOFF"
                }
            }
        } else if (bookingDetail.booking.status === "CONFIRMED") {
            if (bookingDetail.consultationOrderResponse.startTime <= currentTime && currentTime <= bookingDetail.consultationOrderResponse.endTime) {
                state = "DURING_SLOT"
            }
        }
        return {
            productType,
            productStates: [state],
            meta: { orderId: bookingDetail.booking.cfOrderId }
        }
    }

    async getTherapyIssues(booking: Booking): Promise<IssueDetailView[]> {
        const benefits = await this.healthfaceService.getProductBenefits({ productCodes: [booking.productCode] })
        if (benefits && benefits.content && benefits.content[0]) {
            const productDetail: ConsultationProduct = <ConsultationProduct>await this.catalogueService
                    .getProduct(benefits.content[0].offerProductCode.toString())
            if (productDetail && CareUtil.isSupportGroupProduct(productDetail)) {
                return this.getSupportGroupPackIssues()
            }
        }
        return this.getCommonTherapyPackIssues()
    }

    async getPersonalTrainingPacksIssue(bookingDetail: BookingDetail, userContext: UserContext): Promise<IssueDetailView[]> {
        const productType: IssueProductType = "CULT_PT_PACK"
        let state: CultPTPackProductState = "BOUGHT"
        if (bookingDetail.booking.status === "CONFIRMED") {
            const ptBooking = bookingDetail.childBookingInfos && bookingDetail.childBookingInfos.find(booking => booking.booking.status !== "CANCELLED")
            if (!_.isEmpty(ptBooking)) {
                state = "BOOKED_SESSION"
            }
        }
        const issuesResult = await this.issueService.getIssuesByProductTypeAndState(productType, state, userContext)
        return this.transform(issuesResult, {
            orderId: bookingDetail.booking.cfOrderId
        })
    }

    async getLivePersonalTrainingPacksIssue(bookingDetail: BookingDetail, userContext: UserContext): Promise<IssueDetailView[]> {
        const productType: IssueProductType = "LIVE_PT_PACK"
        let state: CultPTPackProductState = "BOUGHT"
        if (bookingDetail.booking.status === "CONFIRMED") {
            const ptBooking = bookingDetail.childBookingInfos && bookingDetail.childBookingInfos.find(booking => booking.booking.status !== "CANCELLED")
            if (!_.isEmpty(ptBooking)) {
                state = "BOOKED_SESSION"
            }
        }
        const issuesResult = await this.issueService.getIssuesByProductTypeAndState(productType, state, userContext)
        return this.transform(issuesResult, {
            orderId: bookingDetail.booking.cfOrderId
        })
    }

    async getPersonalTrainingSessionIssue(bookingDetail: BookingDetail, userContext: UserContext): Promise<IssueDetailView[]> {
        const productType: IssueProductType = "CULT_PT_SESSION"
        let state: CultPTSessionProductState = "BOOKED_SESSION"
        const currentTime = new Date().getTime()
        if (bookingDetail.booking.status === "CANCELLED") {
            state = "CANCELLED"
        } else if (bookingDetail.booking.status === "COMPLETED") {
            state = "POST_SESSION"
        } else if (bookingDetail.booking.status === "CONFIRMED") {
            if (bookingDetail.consultationOrderResponse.startTime <= currentTime && currentTime <= bookingDetail.consultationOrderResponse.endTime) {
                state = "DURING_SLOT"
            }
        }
        const issuesResult = await this.issueService.getIssuesByProductTypeAndState(productType, state, userContext)
        return this.transform(issuesResult, {
            orderId: bookingDetail.booking.cfOrderId
        })
    }

    async getLivePersonalTrainingSessionIssue(bookingDetail: BookingDetail, userContext: UserContext): Promise<IssueDetailView[]> {
        const productType: IssueProductType = "LIVE_PT_SESSION"
        let state: CultPTSessionProductState = "BOOKED_SESSION"
        const currentTime = new Date().getTime()
        if (bookingDetail.booking.status === "CANCELLED") {
            state = "CANCELLED"
        } else if (bookingDetail.booking.status === "COMPLETED") {
            state = "POST_SESSION"
        } else if (bookingDetail.booking.status === "CONFIRMED") {
            if (bookingDetail.consultationOrderResponse.startTime <= currentTime && currentTime <= bookingDetail.consultationOrderResponse.endTime) {
                state = "DURING_SLOT"
            }
        }
        const issuesResult = await this.issueService.getIssuesByProductTypeAndState(productType, state, userContext)
        return this.transform(issuesResult, {
            orderId: bookingDetail.booking.cfOrderId
        })
    }

    async getLiveSGTPacksIssue(bookingDetail: BookingDetail, userContext: UserContext): Promise<IssueDetailView[]> {
        const productType: IssueProductType = "LIVE_SGT_PACK"
        let state: CultPTPackProductState = "BOUGHT"
        if (bookingDetail.booking.status === "CONFIRMED") {
            const ptBooking = bookingDetail.childBookingInfos && bookingDetail.childBookingInfos.find(booking => booking.booking.status !== "CANCELLED")
            if (!_.isEmpty(ptBooking)) {
                state = "BOOKED_SESSION"
            }
        }
        const issuesResult = await this.issueService.getIssuesByProductTypeAndState(productType, state, userContext)
        return this.transform(issuesResult, {
            orderId: bookingDetail.booking.cfOrderId
        })
    }

    async getLiveSGTSessionIssue(bookingDetail: BookingDetail, userContext: UserContext): Promise<IssueDetailView[]> {
        const productType: IssueProductType = "LIVE_SGT_SESSION"
        let state: CultPTSessionProductState = "BOOKED_SESSION"
        const currentTime = new Date().getTime()
        if (bookingDetail.booking.status === "CANCELLED") {
            state = "CANCELLED"
        } else if (bookingDetail.booking.status === "COMPLETED") {
            state = "POST_SESSION"
        } else if (bookingDetail.booking.status === "CONFIRMED") {
            if (bookingDetail.consultationOrderResponse.startTime <= currentTime && currentTime <= bookingDetail.consultationOrderResponse.endTime) {
                state = "DURING_SLOT"
            }
        }
        const issuesResult = await this.issueService.getIssuesByProductTypeAndState(productType, state, userContext)
        return this.transform(issuesResult, {
            orderId: bookingDetail.booking.cfOrderId
        })
    }


    async getPhysiotherapyPacksIssue(bookingDetail: BookingDetail, userContext: UserContext): Promise<IssueDetailView[]> {
        const productType: IssueProductType = "PHYSIOTHERAPY_PACK"
        const state: PhysiotherapyPackProductState = "BOUGHT"
        return this.getPackIssues(bookingDetail, productType, state, userContext)
    }

    async getLCPacksIssue(bookingDetail: BookingDetail, userContext: UserContext): Promise<IssueDetailView[]> {
        const productType: IssueProductType = "LC_PACK"
        let state: LCPackProductState = "BOUGHT"
        if (bookingDetail.booking.status === "CONFIRMED") {
            const ptBooking = bookingDetail.childBookingInfos && bookingDetail.childBookingInfos.find(booking => booking.booking.status !== "CANCELLED")
            if (!_.isEmpty(ptBooking)) {
                state = "BOOKED_SESSION"
            }
        }
        const issuesResult = await this.issueService.getIssuesByProductTypeAndState(productType, state, userContext)
        return this.transform(issuesResult, {
            orderId: bookingDetail.booking.cfOrderId
        })
    }

    async getPhysiotherapySessionIssue(bookingDetail: BookingDetail, userContext: UserContext): Promise<IssueDetailView[]> {
        const productType: IssueProductType = "PHYSIOTHERAPY_SESSION"
        const state: PhysiotherapySessionProductState = "BOOKED_SESSION"
        return this.getSessionIssues(bookingDetail, productType, state, userContext)
    }

    async getLCSessionIssue(bookingDetail: BookingDetail, userContext: UserContext): Promise<IssueDetailView[]> {
        const productType: IssueProductType = "LC_SESSION"
        const state: LCSessionProductState = "BOOKED_SESSION"
        return this.getSessionIssues(bookingDetail, productType, state, userContext)
    }

    async getSkinPackIssues(bookingDetail: BookingDetail, userContext: UserContext): Promise<IssueDetailView[]> {
        const productType: IssueProductType = "SKIN_HAIR_BEAUTY_PACK"
        const state: SkinPackProductState = "BOUGHT"
        return this.getPackIssues(bookingDetail, productType, state, userContext)
    }

    async getSkinSessionIssues(bookingDetail: BookingDetail, userContext: UserContext): Promise<IssueDetailView[]> {
        const productType: IssueProductType = "SKIN_HAIR_BEAUTY_SESSION"
        const state: SkinSessionProductState = "BOOKED_SESSION"
        return this.getSessionIssues(bookingDetail, productType, state, userContext)
    }

    async getConsultationPackIssues(bookingDetail: BookingDetail, userContext: UserContext): Promise<IssueDetailView[]> {
        const productType: IssueProductType = "CONSULTATION_PACK"
        const state: ConsultationPackProductState = "BOUGHT"
        return this.getPackIssues(bookingDetail, productType, state, userContext)
    }

    async getSessionIssues(bookingDetail: BookingDetail, productType: IssueProductType, state: PhysiotherapySessionProductState | SkinSessionProductState | LCSessionProductState, userContext: UserContext) {
        const currentTime = new Date().getTime()
        if (bookingDetail.booking.status === "CANCELLED") {
            state = "CANCELLED"
        } else if (bookingDetail.booking.status === "COMPLETED") {
            state = "POST_SESSION"
        } else if (bookingDetail.booking.status === "CONFIRMED") {
            if (bookingDetail.consultationOrderResponse.startTime <= currentTime && currentTime <= bookingDetail.consultationOrderResponse.endTime) {
                state = "DURING_SLOT"
            }
        }
        const issuesResult = await this.issueService.getIssuesByProductTypeAndState(productType, state, userContext)
        return this.transform(issuesResult, {
            orderId: bookingDetail.booking.cfOrderId
        })
    }

    async getPackIssues(bookingDetail: BookingDetail, productType: IssueProductType, state: PhysiotherapyPackProductState | SkinPackProductState | LCPackProductState, userContext: UserContext) {
        if (bookingDetail.booking.status === "CONFIRMED") {
            const ptBooking = bookingDetail.childBookingInfos && bookingDetail.childBookingInfos.find(booking => booking.booking.status !== "CANCELLED")
            if (!_.isEmpty(ptBooking)) {
                state = "BOOKED_SESSION"
            }
        }
        const issuesResult = await this.issueService.getIssuesByProductTypeAndState(productType, state, userContext)
        return this.transform(issuesResult, {
            orderId: bookingDetail.booking.cfOrderId
        })
    }

    async getAIPlanIssue(userContext: UserContext): Promise<IssueDetailView[]> {
        const issuesResult = await this.issueService.getIssuesByProductTypeAndState("AI_PLAN", "BOUGHT", userContext)
        return this.transform(issuesResult)
    }

    async getGenericIssue(userContext: UserContext): Promise<IssueDetailView[]> {
        const issuesResult = await this.issueService.getIssuesByProductTypeAndState("GENERIC_ISSUE", "DEFAULT", userContext)
        return this.transform(issuesResult)
    }

    async getCultParQIssue(status: string, userContext: UserContext): Promise<IssueDetailView[]> {
        const productType: IssueProductType = "CULT_PARQ"
        let states: CultParqProductState[]
        if (status === "UNDER_18") {
            states = ["UNDER_18"]
        } else {
            states = ["FAILED_UNDER_DOCTOR_APPROVAL"]
        }
        const issueDetails: IssueDetail[] = []
        await Promise.all(states.map(async state => {
            issueDetails.push(...await this.issueService.getIssuesByProductTypeAndState(productType, state, userContext))
        }))
        return this.transform(_.uniqBy(issueDetails, issueDetail => (<any>issueDetail).id), {})
    }


    async getMPPrePurchaseIssue(userContext: UserContext) {
        const issuesResult = await this.issueService.getIssuesByProductTypeAndState("MANAGED_CARE_PLAN", "PRE_PURCHASE", userContext)
        return this.transform(issuesResult)
    }

    async getHCUPacksIssue(bookingDetail: BookingDetail, userContext: UserContext): Promise<IssueDetailView[]> {
        const productType: IssueProductType = "CARE_HEALTH_CHECKUP"
        const currentTimeInEpochInMillis = new Date().getTime()
        let states: HCProductState[] = ["BOUGHT"]
        if (bookingDetail.booking.status === "CONFIRMED") {
            const otBooking = bookingDetail.childBookingInfos.find(booking => booking.booking.subCategoryCode === "HCU_PACK_OT")
            if (!_.isEmpty(otBooking)) {
                const diagnosticResponse: BundleSetupInfoV2 = otBooking.stepInfosV2.find(stepInfo => stepInfo.stepInfoType === "DIAGNOSTIC_TEST")
                if (diagnosticResponse.stepState === "BOOKED") {
                    states = this.getTestStates(diagnosticResponse.diagnosticsTestBookingInfo.diagnosticsTestOrderResponse[0], currentTimeInEpochInMillis)
                } else {
                    if (diagnosticResponse.stepState === "TEST_COMPLETED") {
                        states = ["TEST_COMPLETED"]
                    } else if (diagnosticResponse.stepState === "REPORT_GENERATED") {
                        states = ["REPORT_GENERATED"]
                    }
                }
                const consultationStep: BundleSetupInfoV2 = otBooking.stepInfosV2.find(stepInfo => stepInfo.stepInfoType === "CONSULTATION")
                if (consultationStep.stepState === "COMPLETED") {
                    states = ["POST_DOCTOR_CONSULTATION"]
                }
            }
        }
        const issueDetails: IssueDetail[] = []
        await Promise.all(states.map(async state => {
            issueDetails.push(...await this.issueService.getIssuesByProductTypeAndState(productType, state, userContext))
        }))
        return this.transform(_.uniqBy(issueDetails, issueDetail => (<any>issueDetail).id),
            {
                orderId: bookingDetail.booking.cfOrderId
            })
    }

    async getScreeningPacksIssue(bookingDetail: BookingDetail, userContext: UserContext): Promise<IssueDetailView[]> {
        const productType: IssueProductType = "DIAGNOSTIC_SCREENING_PACK"
        let states: DiagnosticProductState[] = ["POST_PURCHASE"]
        if (bookingDetail.booking.status === "CONFIRMED") {
            const otBooking = bookingDetail.childBookingInfos.find(booking => booking.booking.subCategoryCode === "DIAG_PACK_OT")
            if (!_.isEmpty(otBooking)) {
                const diagnosticResponse: BundleSetupInfoV2 = otBooking.stepInfosV2.find(stepInfo => stepInfo.stepInfoType === "DIAGNOSTIC_TEST")
                if (diagnosticResponse.stepState === "TEST_COMPLETED" || diagnosticResponse.stepState === "REPORT_GENERATED") {
                    states = ["POST_PURCHASE_TEST_COMPLETED"]
                } else if (diagnosticResponse.stepState !== "NOT_BOOKED") {
                    states = ["POST_PURCHASE_TEST_SCHEDULED"]
                }
            }
        }
        const issueDetails: IssueDetail[] = []
        await Promise.all(states.map(async state => {
            issueDetails.push(...await this.issueService.getIssuesByProductTypeAndState(productType, state, userContext))
        }))
        return this.transform(_.uniqBy(issueDetails, issueDetail => (<any>issueDetail).id),
            {
                orderId: bookingDetail.booking.cfOrderId
            })
    }

    async getManagedPlanIssue(bookingDetail: BookingDetail, userContext: UserContext): Promise<IssueDetailView[]> {
        const productType: IssueProductType = "MANAGED_CARE_PLAN"
        let states: ManagedCarePlanState[] = ["BOUGHT"]
        if (bookingDetail.booking.status === "CONFIRMED") {
            const otBooking = bookingDetail.childBookingInfos.find(booking => booking.booking.subCategoryCode === "MP_OT" || booking.booking.subCategoryCode === "MP_V2_OT")
            if (!_.isEmpty(otBooking)) {
                if (!CareUtil.checkMPOnboardingNotDone(otBooking)) {
                    states = ["ONBOARDING_DONE"]
                } else {
                    const diagnosticResponse: BundleSetupInfoV2 = otBooking.stepInfosV2.find(stepInfo => stepInfo.stepInfoType === "DIAGNOSTIC_TEST")
                    if (diagnosticResponse && diagnosticResponse.stepState === "TEST_COMPLETED") {
                        states = ["PREONBOARDING_TESTS_COMPLETED"]
                    } else if (diagnosticResponse && diagnosticResponse.stepState === "REPORT_GENERATED") {
                        states = ["PREONBOARDING_REPORT_GENERATED"]
                    }
                }
            }
        }
        const issueDetails: IssueDetail[] = []
        await Promise.all(states.map(async state => {
            issueDetails.push(...await this.issueService.getIssuesByProductTypeAndState(productType, state, userContext))
        }))
        return this.transform(_.uniqBy(issueDetails, issueDetail => (<any>issueDetail).id),
            {
                orderId: bookingDetail.booking.cfOrderId
            })
    }


    async getHCIssues(bookingDetail: BookingDetail, userContext: UserContext): Promise<IssueDetailView[]> {
        const productType: IssueProductType = "CARE_HEALTH_CHECKUP"
        const currentTimeInEpochInMillis = new Date().getTime()
        let states: HCProductState[] = ["BOUGHT"]
        if (bookingDetail.booking.status === "CONFIRMED") {
            const diagnosticResponse: BundleStepInfo = bookingDetail.bundleSetupInfo.bundleStepInfos.find(stepInfo => stepInfo.setupStep === "DIAGNOSTIC_TEST")
            if (diagnosticResponse.stepState === "BOOKED") {
                states = this.getTestStates(diagnosticResponse.testBookingInfo.diagnosticsTestOrderResponse[0], currentTimeInEpochInMillis)
            } else {
                if (diagnosticResponse.stepState === "TEST_COMPLETED") {
                    states = ["TEST_COMPLETED"]
                } else if (diagnosticResponse.stepState === "REPORT_GENERATED") {
                    states = ["REPORT_GENERATED"]
                }
            }
            const consultationStep: BundleStepInfo = bookingDetail.bundleSetupInfo.bundleStepInfos.find(stepInfo => stepInfo.setupStep === "CONSULTATION")
            if (consultationStep.stepState === "COMPLETED") {
                states = ["POST_DOCTOR_CONSULTATION"]
            }
        }
        const issueDetails: IssueDetail[] = []
        await Promise.all(states.map(async state => {
            issueDetails.push(...await this.issueService.getIssuesByProductTypeAndState(productType, state, userContext))
        }))
        return this.transform(_.uniqBy(issueDetails, issueDetail => (<any>issueDetail).id),
            {
                orderId: bookingDetail.booking.cfOrderId
            })
    }

    getDiagnosticTestIssueParams(bookingDetail: BookingDetail): IssueDetailParams {
        const productType: IssueProductType = "CARE_HEALTH_CHECKUP"
        const currentTimeInEpochInMillis = new Date().getTime()
        let states: HCProductState[] = ["BOUGHT"]
        const diagnosticsTestOrderResponse = bookingDetail.diagnosticsTestOrderResponse[0]
        if (bookingDetail.booking.status === "CONFIRMED") {
            states = this.getTestStates(diagnosticsTestOrderResponse, currentTimeInEpochInMillis)
        } else {
            if (diagnosticsTestOrderResponse.status === "TEST_COMPLETED") {
                states = ["TEST_COMPLETED"]
            }
            else if (diagnosticsTestOrderResponse.status === "REPORT_GENERATED") {
                states = ["REPORT_GENERATED"]
            }
        }
        return {
            productType,
            productStates: states,
            meta: {
                orderId: bookingDetail.booking.cfOrderId
            }
        }
    }

    private getTestStates(diagnosticsTestOrderResponse: DiagnosticsTestOrderResponse, currentTimeInEpochInMillis: number): HCProductState[] {
        let states: HCProductState[] = []
        if (!_.isEmpty(diagnosticsTestOrderResponse.atHomeDiagnosticOrder)) {
            let atHomeState: HCProductState
            if (diagnosticsTestOrderResponse?.status === "TEST_COMPLETED") {
                states = ["TEST_COMPLETED"]
            } else if (diagnosticsTestOrderResponse?.status === "REPORT_GENERATED") {
                states = ["REPORT_GENERATED"]
            } else {
                switch (diagnosticsTestOrderResponse.atHomeStepInfo.stepState) {
                    case "NOT_BOOKED": break
                    case "BOOKED":
                        atHomeState = "BOOKED_HOME_SAMPLE"
                        if (diagnosticsTestOrderResponse.atHomeDiagnosticOrder.startTime <= currentTimeInEpochInMillis
                            && diagnosticsTestOrderResponse.atHomeDiagnosticOrder.endTime >= currentTimeInEpochInMillis) {
                            atHomeState = "DURING_SAMPLE_PICKUP_AT_HOME"
                        } else if (currentTimeInEpochInMillis > diagnosticsTestOrderResponse.atHomeDiagnosticOrder.endTime) {
                            atHomeState = "SAMPLE_NOT_COLLECTED_ON_TIME_AT_HOME"
                        }
                        break
                    case "SAMPLE_COLLECTED": atHomeState = "SAMPLE_COLLECTED_AT_HOME"; break
                    case "COMPLETED": atHomeState = "SAMPLE_COLLECTED_AT_HOME"; break
                }
                if (atHomeState) {
                    states.push(atHomeState)
                }
            }
        }
        if (!_.isEmpty(diagnosticsTestOrderResponse.inCentreDiagnosticOrder)) {
            let inCentreState: HCProductState
            switch (diagnosticsTestOrderResponse.inCentreStepInfo.stepState) {
                case "NOT_BOOKED": break
                case "BOOKED": inCentreState = "BOOKED_AT_CENTRE_SAMPLE"
                    if (diagnosticsTestOrderResponse.inCentreDiagnosticOrder.slot.workingEndTime >= currentTimeInEpochInMillis
                        && diagnosticsTestOrderResponse.inCentreDiagnosticOrder.slot.workingEndTime <= currentTimeInEpochInMillis) {
                        inCentreState = "DURING_SAMPLE_PICKUP_AT_CENTRE"
                    } else if (currentTimeInEpochInMillis > diagnosticsTestOrderResponse.inCentreDiagnosticOrder.slot.workingEndTime) {
                        inCentreState = "SAMPLE_NOT_COLLECTED_ON_TIME_AT_CENTRE"
                    }
                    break
                case "SAMPLE_COLLECTED": inCentreState = "SAMPLE_COLLECTED_AT_CENTRE"; break
                case "COMPLETED": inCentreState = "SAMPLE_COLLECTED_AT_CENTRE"; break
            }

            states.push(inCentreState)
        }
        return states
    }

    toManageOptionPayload(issues: IssueDetailView[], enableReportIssue: boolean, displayText: string = "Need Help"): ManageOptionPayload {
        const manageOptionPayload: ManageOptionPayload = {
            isEnabled: enableReportIssue,
            displayText,
            type: "REPORT_ISSUE",
            meta: { issues: issues }
        }
        return manageOptionPayload
    }


    toSupportPackActionPayload(issues: IssueDetailView[], enableReportIssue: boolean, displayText: string = "Need Help"): Action {
        const manageOptionPayload: Action = {
            actionType: "NAVIGATION",
            url: SUPPORT_DEEP_LINK,
            meta: { issues: issues }
        }
        return manageOptionPayload
    }

    transform(issues: IssueDetail[], meta: IssueDetailViewMeta = {}, userContext?: UserContext): IssueDetailView[] {
        const supportedIssues = _.filter(issues, issue => {
            return userContext ? this.isSupportedInAppVersionOrOtherChecks(issue, userContext) : true
        })
        return _.map(supportedIssues, issue => this.toIssueDetailView(issue, meta))
    }

    private isSupportedInAppVersionOrOtherChecks(issue: IssueDetail, userContext: UserContext): boolean {
        let isNotSupported: boolean = false
        if (issue.minAppVersion && !_.isEmpty(issue.minAppVersion.android) && !_.isEmpty(issue.minAppVersion.ios) && userContext.sessionInfo.userAgent === "APP") {
            isNotSupported = (userContext.sessionInfo.osName.toLowerCase() === "ios" && (+issue.minAppVersion.ios) > userContext.sessionInfo.appVersion) || (userContext.sessionInfo.osName.toLowerCase() === "android" && (+issue.minAppVersion.android) > userContext.sessionInfo.appVersion)
        }
        if (issue.maxAppVersion && !_.isEmpty(issue.maxAppVersion.android) && !_.isEmpty(issue.maxAppVersion.ios) && userContext.sessionInfo.userAgent === "APP") {
            isNotSupported = (userContext.sessionInfo.osName.toLowerCase() === "ios" && (+issue.maxAppVersion.ios) < userContext.sessionInfo.appVersion) || (userContext.sessionInfo.osName.toLowerCase() === "android" && (+issue.maxAppVersion.android) < userContext.sessionInfo.appVersion)
        }
        return !isNotSupported
    }


    private async selfServeCancel(productType: ProductType, classMeta: ClassMeta, userId: string) {
        if (productType === "FITNESS") {
            return this.cultFitService.selfServeCancelBooking(classMeta.bookingNumber, userId)
        } else if (productType === "MIND") {
            return this.mindFitService.selfServeCancelBooking(classMeta.bookingNumber, userId)
        }
    }

    private async selfServeAttendance(productType: ProductType, classMeta: ClassMeta, userId: string) {
        if (productType === "FITNESS") {
            return this.cultFitService.selfServeMarkAttendance(classMeta.bookingNumber, userId)
        } else if (productType === "MIND") {
            return this.mindFitService.selfServeMarkAttendance(classMeta.bookingNumber, userId)
        }
    }

    toIssueDetailView(issueDetail: IssueDetail, meta?: IssueDetailViewMeta): IssueDetailView {
        const issueDetailView: IssueDetailView = {
            issueDescription: issueDetail.issueDescription,
            issueId: (<any>issueDetail).id,
            action: this.toIssueDetailViewAction(issueDetail, meta)
        }
        return issueDetailView
    }

    toIssueDetailViewAction(issueDetail: IssueDetail, meta: IssueDetailViewMeta): IssueDetailViewAction {
        let quickSolveContent = undefined
        switch (issueDetail.actionType) {
            case "DEEP_LINK":
                return {
                    actionType: "NAVIGATION",
                    url: issueDetail.deeplinkParams.url,
                    meta: meta
                }
            case "WEB_LINK":
                return {
                    actionType: "NAVIGATION",
                    url: `curefit://webview?uri=${encodeURIComponent(issueDetail.deeplinkParams.url)}`,
                    meta: meta
                }
            case "RAISE_TICKET":
                if (issueDetail.raiseTicketParams.quickSolveContent) {
                    quickSolveContent = {
                        title: issueDetail.raiseTicketParams.quickSolveContent.title,
                        subTitle: issueDetail.raiseTicketParams.quickSolveContent.description,
                        ctas: issueDetail.raiseTicketParams.ctas,
                        faqCtas: issueDetail.raiseTicketParams.quickSolveContent.faqCtas
                    }
                }
                return {
                    actionType: "RAISE_TICKET",
                    raiseTicketParams: {
                        confirmationMessage: issueDetail.raiseTicketParams.confirmationMessage,
                        issueDescription: issueDetail.issueDescription,
                        quickSolveContent: quickSolveContent,
                        // depreciated post 10.52 version
                        optionalModalMeta: issueDetail.raiseTicketParams.initiateCall ? {
                            title: "Would you like to speak to our consultant about the issue?",
                            options: [{ title: "Skip", meta: { requestedCall: false } }, { title: "Call me", meta: { requestedCall: true } }]
                        } : undefined,
                        // Used from 10.52 version
                        requestCallBackMeta: issueDetail.raiseTicketParams.initiateCall ? {
                            ctaTitle: "REQUEST CALL BACK",
                            meta: { requestedCall: true },
                            modalText: this.getRequestCallbackMessage(),
                            modalActions: [{title: "CANCEL", type: RequestCallBackActionType.CANCEL}, {title: "PROCEED", type: RequestCallBackActionType.PROCEED}]
                        } : undefined
                    },
                    meta: meta
                }
            case "QUICK_SOLVE_CONTENT":
                if (issueDetail.quickSolveContentParams.quickSolveContent) {
                    quickSolveContent = {
                        title: issueDetail.quickSolveContentParams.quickSolveContent.title,
                        subTitle: issueDetail.quickSolveContentParams.quickSolveContent.description,
                        ctas: issueDetail.quickSolveContentParams.ctas,
                        faqCtas: issueDetail.quickSolveContentParams.quickSolveContent.faqCtas
                    }
                }
                return {
                    actionType: "QUICK_SOLVE_CONTENT",
                    quickSolveContentParams: {
                        quickSolveContent: quickSolveContent,
                    }
                }
            case "OPEN_CHILD":
                return {
                    actionType: "OPEN_CHILD",
                    meta: meta
                }
            case "ORDER_TRACKING":
                const mealMeta = meta.mealMeta
                const url = ActionUtil.trackFoodOrder(mealMeta.fulfilmentId, mealMeta.deliveryDate)
                return {
                    actionType: "NAVIGATION",
                    url: url,
                    meta: meta
                }
            case "SELF_SERVER_CANCEL":
            case "SELF_SERVER_MARK_ATTENDANCE":
            case "TRIGGER_REFUND":
            case "FORGOT_TO_CANCEL_MEAL":
            case "FORGOT_TO_MODIFY_MEAL":
                return {
                    actionType: "SERVER_SIDE_ACTION",
                    meta: meta
                }
            case "CS_AUTOMATION":
                const type = issueDetail.csAutomationParams.type
                const modalType: SupportOptionsModalType = this.getSupportOptionsModalType(type)
                return {
                    actionType: "SHOW_SUPPORT_OPTIONS_MODAL",
                    modalType: modalType,
                    meta: meta,
                }
        }
        return undefined
    }

    getRequestCallbackMessage() {
        const currentTime = new Date()
        const currentHour = currentTime.getHours()
        const currentMinutes = currentTime.getMinutes()
        if ((currentHour > 9 || (currentHour === 9 && currentMinutes >= 0)) && (currentHour < 20 || (currentHour === 20 && currentMinutes <= 30))) {
            return "Our team will give you a call on your registered mobile number in the next 2 hours"
        } else {
            return "Our team will give you a call on your registered mobile number tomorrow between 9:00am to 12:00pm"
        }
    }

    getSupportOptionsModalData(modalType: SupportOptionsModalType, meta: IssueDetailViewMeta, payload: any) {
        switch (modalType) {
            case "NO_SHOW_ATTENDANCE_CHECK":
                return {
                    title: "Did you attend this class?",
                    buttons: [
                        {
                            title: "YES",
                            actionType: "SHOW_SUPPORT_OPTIONS_MODAL",
                            modalType: "NO_SHOW_REASONS_WHEN_ATTENDED",
                            meta: meta
                        },
                        {
                            title: "NO",
                            actionType: "SHOW_SUPPORT_OPTIONS_MODAL",
                            modalType: "NO_SHOW_REASONS_WHEN_NOT_ATTENDED",
                            meta: meta
                        }
                    ]
                }
            case "NO_SHOW_REASONS_WHEN_ATTENDED":
                return {
                    title: "Select Reason for not marking attendance",
                    choices: [
                        {
                            title: "QR Code/Biometric not working",
                            actionType: "SERVER_SIDE_ACTION_V2",
                            csAutomationParams: {
                                type: "NO_SHOW_EXCEPTION",
                                reason: "QR Code/Biometric not working"
                            },
                            meta: meta
                        },
                        {
                            title: "Forgot to mark attendance",
                            actionType: "SERVER_SIDE_ACTION_V2",
                            csAutomationParams: {
                                type: "NO_SHOW_EXCEPTION",
                                reason: "Forgot to mark attendance"
                            },
                            meta: meta
                        },
                        {
                            title: "CM Forgot to mark attendance",
                            actionType: "SERVER_SIDE_ACTION_V2",
                            csAutomationParams: {
                                type: "NO_SHOW_EXCEPTION",
                                reason: "CM Forgot to mark attendance"
                            },
                            meta: meta
                        }
                    ]
                }
            case "NO_SHOW_REASONS_WHEN_NOT_ATTENDED":
                return {
                    title: "Select reason for not attending",
                    choices: [
                        {
                            title: "Was denied entry",
                            actionType: "SERVER_SIDE_ACTION_V2",
                            csAutomationParams: {
                                type: "NO_SHOW_EXCEPTION",
                                reason: "Was denied entry"
                            },
                            meta: meta
                        },
                        {
                            title: "Dropped out but received no-show",
                            actionType: "SERVER_SIDE_ACTION_V2",
                            csAutomationParams: {
                                type: "NO_SHOW_EXCEPTION",
                                reason: "Dropped out but received no-show"
                            },
                            meta: meta
                        },
                        {
                            title: "Late waitlist confirmation",
                            actionType: "SERVER_SIDE_ACTION_V2",
                            csAutomationParams: {
                                type: "NO_SHOW_EXCEPTION",
                                reason: "Late waitlist confirmation"
                            },
                            meta: meta
                        },
                        {
                            title: "Traffic/Rain/Health Issue",
                            actionType: "SERVER_SIDE_ACTION_V2",
                            csAutomationParams: {
                                type: "NO_SHOW_EXCEPTION",
                                reason: "Traffic/Rain/Health Issue"
                            },
                            meta: meta
                        },

                    ]
                }
            case "CANCEL_CLASS_AFTER_CUTOFF":
                return {
                    title: "Select reason for not attending",
                    choices: [
                        {
                            title: "Late waitlist confirmation",
                            actionType: "SERVER_SIDE_ACTION_V2",
                            csAutomationParams: {
                                type: "CANCEL_CLASS",
                                reason: "Late waitlist confirmation"
                            },
                            meta: meta
                        },
                        {
                            title: "Traffic/Rain/Health Issue",
                            actionType: "SERVER_SIDE_ACTION_V2",
                            csAutomationParams: {
                                type: "CANCEL_CLASS",
                                reason: "Traffic/Rain/Health Issue"
                            },
                            meta: meta
                        },
                    ]
                }
            case "NO_SHOW_KEYWORD_CHECK":
                return {
                    title: "Is this regarding a no-show penalty?",
                    buttons: [
                        {
                            title: "YES",
                            actionType: "SHOW_SUPPORT_OPTIONS_MODAL",
                            modalType: "NO_SHOW_COACHING_GROUP_CLASSES"
                        },
                        {
                            title: "NO",
                            actionType: "SUBMIT_SUPPORT_TICKET",
                            meta: {
                                requestBody: payload.requestBody,
                                queryParams: payload.queryParams,
                            },
                            shouldPopFirst: true,
                        }
                    ]
                }
            case "NO_SHOW_COACHING_GROUP_CLASSES":
                const pasteToIssueIds: string[] = (AppUtil.isProdLike) ? ["1881", "1882"] : ["1439", "1440"]
                return {
                    title: "Select the missed class for which you received a no-show penalty",
                    buttons: [
                        {
                            title: "OK",
                            actionType: "SUPPORT_NAVIGATION",
                            url: "curefit://support_activity_list?activityType=classes&productType=FITNESS_HUB_GX&productStates[0]=NEW_USER",
                            meta: {
                                popUntilRouteName: "/fl_support",
                                saveTextToIssueIds: pasteToIssueIds
                            }
                        }
                    ]
                }
            case "PLAY_NO_SHOW_ATTENDANCE_CHECK":
                return {
                    title: "Did you attend this session?",
                    buttons: [
                        {
                            title: "YES",
                            actionType: "SHOW_SUPPORT_OPTIONS_MODAL",
                            modalType: "PLAY_NO_SHOW_REASONS_WHEN_ATTENDED",
                            meta: meta
                        },
                        {
                            title: "NO",
                            actionType: "SHOW_SUPPORT_OPTIONS_MODAL",
                            modalType: "PLAY_NO_SHOW_REASONS_WHEN_NOT_ATTENDED",
                            meta: meta
                        }
                    ]
                }
            case "PLAY_NO_SHOW_REASONS_WHEN_ATTENDED":
                return {
                    title: "Select Reason for not marking attendance",
                    choices: [
                        {
                            title: "Face recognition not working",
                            actionType: "SERVER_SIDE_ACTION_V2",
                            csAutomationParams: {
                                type: "NO_SHOW_EXCEPTION_PLAY",
                                reason: "Face recognition not working"
                            },
                            meta: meta
                        },
                        {
                            title: "Forgot to mark attendance",
                            actionType: "SERVER_SIDE_ACTION_V2",
                            csAutomationParams: {
                                type: "NO_SHOW_EXCEPTION_PLAY",
                                reason: "Forgot to mark attendance"
                            },
                            meta: meta
                        },
                        {
                            title: "CM Forgot to mark attendance",
                            actionType: "SERVER_SIDE_ACTION_V2",
                            csAutomationParams: {
                                type: "NO_SHOW_EXCEPTION_PLAY",
                                reason: "CM Forgot to mark attendance"
                            },
                            meta: meta
                        },
                        {
                            title: "Marked attendance late",
                            actionType: "SERVER_SIDE_ACTION_V2",
                            csAutomationParams: {
                                type: "NO_SHOW_EXCEPTION_PLAY",
                                reason: "Marked attendance late"
                            },
                            meta: meta
                        }
                    ]
                }
            case "PLAY_NO_SHOW_REASONS_WHEN_NOT_ATTENDED":
                return {
                    title: "Select reason for not attending",
                    choices: [
                        {
                            title: "Was denied entry",
                            actionType: "SERVER_SIDE_ACTION_V2",
                            csAutomationParams: {
                                type: "NO_SHOW_EXCEPTION_PLAY",
                                reason: "Was denied entry"
                            },
                            meta: meta
                        },
                        {
                            title: "Dropped out but received no-show",
                            actionType: "SERVER_SIDE_ACTION_V2",
                            csAutomationParams: {
                                type: "NO_SHOW_EXCEPTION_PLAY",
                                reason: "Dropped out but received no-show"
                            },
                            meta: meta
                        },
                        {
                            title: "Late waitlist confirmation",
                            actionType: "SERVER_SIDE_ACTION_V2",
                            csAutomationParams: {
                                type: "NO_SHOW_EXCEPTION_PLAY",
                                reason: "Late waitlist confirmation"
                            },
                            meta: meta
                        },
                        {
                            title: "Traffic/Rain/Health Issue",
                            actionType: "SERVER_SIDE_ACTION_V2",
                            csAutomationParams: {
                                type: "NO_SHOW_EXCEPTION_PLAY",
                                reason: "Traffic/Rain/Health Issue"
                            },
                            meta: meta
                        },

                    ]
                }
        }
    }

    getSupportOptionsModalType(type: CSAutomationType): SupportOptionsModalType {
        switch (type) {
            case CSAutomationType.NO_SHOW_EXCEPTION:
                return "NO_SHOW_ATTENDANCE_CHECK"
            case CSAutomationType.NO_SHOW_EXCEPTION_PLAY:
                return "PLAY_NO_SHOW_ATTENDANCE_CHECK"
            case CSAutomationType.CANCEL_CLASS:
                return "CANCEL_CLASS_AFTER_CUTOFF"
        }
    }

    mapGearShipmentStateToIssueProductState(shipmentState: string, edd: string) {
        let states: GearProductState[] = ["ORDER_PLACED"]
        const eddDate: number = TimeUtil.getEpochFromDate(new Date(edd))
        const todayDate: number = TimeUtil.getEpochFromDate(new Date())
        switch (shipmentState) {
            case "delivered": {
                states = ["ORDER_DELIVERED"]
                break
            }
            default: {
                states = ["ORDER_PLACED"]
                if (todayDate < eddDate) {
                    states.push("ORDER_DELIVERY_WITHIN_SLA")
                } else {
                    states.push("ORDER_DELIVERY_AFTER_SLA")
                }
                break
            }
        }
        return states
    }

    async getGymfitFitnessPackIssues(fulfilment: GymfitFitnessFulfilment, userContext: UserContext) {
        const productType: IssueProductType = "GYMFIT_FITNESS_PACK"
        const productState: GymfitProductState = fulfilment.status === "CREATED" ? "PRODUCT_PURCHASED" : "PRODUCT_CANCELLED"
        const issuesResult = await this.issueService.getIssuesByProductTypeAndState(productType, productState, userContext)
        return this.transform(issuesResult, {
            orderId: fulfilment.orderId
        })
    }

    async getGymfitFitnessProductIssues(userContext: UserContext, productState: IssueProductState, orderId?: string) {
        const productType: IssueProductType = "GYMFIT_FITNESS_PRODUCT"
        const issuesResult = await this.issueService.getIssuesByProductTypeAndState(productType, productState, userContext)
        return this.transform(issuesResult, {orderId})
    }

    async getTransformPackProductIssues(bookingDetail: BookingDetail, userContext: UserContext) {
        const productType: IssueProductType = "CULT_TRANSFORM_PACK"
        const issuesResult = await this.issueService.getIssuesByProductTypeAndState(productType, "AFTER_PURCHASE", userContext)
        return this.transform(issuesResult)
    }


    async getGymfitFitnessCheckinIssues(checkinId: number, gymfitCheckinState: CFAPIGymfitCheckInState, userContext: UserContext) {
        const productType: IssueProductType = "GYMFIT_FITNESS_CHECKIN"
        let productState: GymfitCheckinProductState
        switch (gymfitCheckinState) {
            case "UPCOMING":
                productState = "BOOKED"
                break
            case "ONGOING":
                productState = "ONGOING"
                break
            case "COMPLETED":
                productState = "COMPLETED"
                break
            case "CANCELLED":
                productState = "CANCELLED"
                break
            case "MISSED":
                productState = "MISSED"
                break
        }
        const issuesResult = await this.issueService.getIssuesByProductTypeAndState(productType, productState, userContext)
        return this.transform(issuesResult, {
            shipmentId: String(checkinId),
            gymCheckinMeta: {checkinId}
        })
    }

    getPlayBookingIssueParams(session: PlayBaseBooking): IssueDetailParams {
        const issueProductType: IssueProductType = "FITNESS_HUB_SPORTS"
        let productState: PlaySessionState
        switch (session.bookingStatus) {
            case "UPCOMING":
                productState = "UPCOMING_SESSION"
                break
            case "ATTENDED":
                productState = "PAST_SESSION"
                break
            case "CANCELLED":
                productState = "PAST_SESSION"
                break
            case "NO_SHOW":
                productState = "PAST_SESSION"
                break
            case "MISSED":
                productState = "PAST_SESSION"
                break
        }

        const cultClass = session?.cultClass ? session.cultClass : undefined
        const issueMeta: IssueDetailViewMeta = {
            fitsoClassMeta: cultClass ? {
                ...cultClass,
                bookingId: String(cultClass.bookingId),
                membershipId: session?.membership?.membershipId,
                workoutID: String(cultClass.workoutID)
            } : undefined,
        }
        return {
            productType: issueProductType,
            productStates: [productState],
            meta: issueMeta
        }
    }

    async getCultSocialIssues(userContext: UserContext): Promise<IssueDetailView[]> {
        const productType: IssueProductType = "CULT_SOCIAL"
        const productState: CultSocialState = "SOCIAL_PUBLIC"

        const issuesResult = await this.issueService.getIssuesByProductTypeAndState(productType, productState, userContext)
        return this.transform(issuesResult)
    }

    async getCultBikeIssue(userContext: UserContext, order: BaseOrder): Promise<IssueDetailView[]> {
        const productType: any = "CULTBIKE"
        const state = "Order Confirmed"
        const issuesResult = await this.issueService.getIssuesByProductTypeAndState(productType, state as any, userContext)
        return this.transform(issuesResult, {
            orderId: order.orderId
        })
    }
}

export default IssueBusiness
