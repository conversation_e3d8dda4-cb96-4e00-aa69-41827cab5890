import { UserAgentType } from "@curefit/base-common"

export interface IFreshdeskTicketsStatusMapping {
    [key: number]: string,
}

export const FreshdeskTicketsStatusMapping: IFreshdeskTicketsStatusMapping = {
    1: "NA",
    2: "Open",
    3: "Pending",
    4: "Resolved",
    5: "Closed",
    6: "Waiting for Customer Revert",
    7: "Waiting for Business Update",
    8: "Waiting for Ops Update",
    9: "Waiting for Tech Update",
    10: "Callback/Followup",
    11: "NA",
    12: "Test"
}

export const FreshdeskTicketSourceMapping = {
    "PORTAL" : 2,
    "FEEDBACK": 9,
    "DESKTOP": process.env.ENVIRONMENT === "STAGE" ? 102 : 101
}

export const freshdeskDesktopSourceAgents: UserAgentType[] = ["DESKTOP", "MBROWSER"]
