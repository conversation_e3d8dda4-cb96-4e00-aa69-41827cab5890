import { Action } from "@curefit/vm-models"

export class SupportCSATPageView {
    ticketTitle: string
    ticketStatus: string
    primaryQuestionAnswer: SupportCSATQuestionAnswer
    secondaryQuestionAnswers: { [id: string]: SupportCSATQuestionAnswer[] }

    constructor(
        ticketTitle?: string,
        ticketStatus?: string,
        primaryQuestionAnswer?: SupportCSATQuestionAnswer,
        secondaryQuestionAnswers?: { [id: string]: SupportCSATQuestionAnswer[] }
    ) {
        this.ticketTitle = ticketTitle
        this.ticketStatus = ticketStatus
        this.primaryQuestionAnswer = primaryQuestionAnswer
        this.secondaryQuestionAnswers = secondaryQuestionAnswers
    }
}

export interface SupportCSATQuestionAnswer {
    question: SupportCSATQuestion
    answers?: SupportCSATAnswer[]
}


export interface SupportCSATQuestion {
    questionId: string
    questionTitle: string
    questionType: string
}


export interface SupportCSATAnswer {
    tagId: string
    tagTitle: string
    unselectedImage: string
    selectedImage: string
}
