import * as _ from "lodash"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import { Container, inject } from "inversify"
import AuthMiddleware from "../auth/AuthMiddleware"
import * as express from "express"
import { ProductType } from "@curefit/product-common"
import { Session, UserContext } from "@curefit/userinfo-common"
import { SPRINKLR_CLIENT_TYPES, ISprinklrService , SprinklrCase } from "@curefit/sprinklr-node-client"

import {
    CSAutomationType,
    IssueProductState,
    IssueProductType,
    RaiseTicketParams,
    Ticket as IssueTicket,
    TicketPriority
} from "@curefit/issue-common"
import ICRMIssueService from "./ICRMIssueService"
import { CULT_CLIENT_TYPES, ICultServiceOld as ICultService } from "@curefit/cult-client"
import IssueBusiness, { IssueDetailView, IssueDetailViewMeta, SupportOptionsModalType } from "./IssueBusiness"
import { Action } from "../common/views/WidgetView"
import { SUPPORT_BASE, SupportView } from "./SupportView"
import { SourceWithWidgetCount, SupportListCombinedPageView, SupportListPageView } from "./SupportListPageView"
import {
    IAddCustomerReplyRequest,
    IConversation,
    ICSATQuestionnaireResponse,
    IFreshdeskService,
    IGetTicketConversationResponse,
    IGetUserTicketResponse,
    IIssueContext,
    IIssueContextService,
    REPORT_ISSUES_CLIENT_TYPES,
    Ticket
} from "@curefit/report-issues-client"
import { IFreshdeskTicket, ITicketConversation } from "@curefit/freshdesk-common"
import { User } from "@curefit/user-common"
import {
    Status,
    SupportActionableCardWidget,
    SupportCardItem,
    TicketCSATFooterWidget,
    TicketDescriptionWidget
} from "../page/PageWidgets"
import { PageWidget } from "../page/Page"
import { OrderSource } from "@curefit/order-common"
import AppUtil from "../util/AppUtil"
import { FreshdeskTicketSourceMapping, FreshdeskTicketsStatusMapping } from "./FreshdeskConstants"
import { SignedUrlResponse } from "@curefit/user-client"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import CRMBusiness from "./CRMBusiness"
import { SupportWebView } from "./SupportWebView"
import { BASE_TYPES, Logger } from "@curefit/base"
import { ErrorFactory } from "@curefit/error-client"
import { ErrorCodes } from "../error/ErrorCodes"
import { Tenant } from "@curefit/base-common"
import { SupportCSATPageView } from "./SupportCSATPageView"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { PromiseCache } from "../util/VMUtil"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import {
    IMediaGatewayService,
    MEDIA_GATEWAY_CLIENT_TYPES,
    MediaType,
    ObjectAcl
} from "@curefit/media-gateway-js-client"
import { OrderDetail, OrderWidgetData } from "../order/OrderViewBuilder"

const clone = require("clone")

export const SELF_SERVE_ATTENDANCE_CODES = ["CultPackSessionBooked03", "CultPackSessionAttended03", "CultSingleSessionBooked03", "CultSingleSessionAttended03"]
export const SELF_SERVE_CANCEL_CODES = ["CultPackSessionBooked02", "CultPackSessionAttended02", "CultSingleSessionBooked02", "CultSingleSessionAttended02"]


export function controllerFactory(kernel: Container) {
    @controller("/crm",
        kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession
    )
    class CRMController {

        constructor(
            @inject(SPRINKLR_CLIENT_TYPES.Service) private sprinklrClient: ISprinklrService,
            @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
            @inject(CULT_CLIENT_TYPES.MindFitService) private mindFitService: ICultService,
            @inject(CUREFIT_API_TYPES.CRMIssueService) private CRMIssueService: ICRMIssueService,
            @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness,
            @inject(LOCATION_TYPES.CityService) private cityService: ICityService,
            @inject(CUREFIT_API_TYPES.CRMBusiness) private crmBusiness: CRMBusiness,
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(REPORT_ISSUES_CLIENT_TYPES.FreshdeskService) private freshdeskService: IFreshdeskService,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
            @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness,
            @inject(ERROR_COMMON_TYPES.RollbarService) public rollbarService: RollbarService,
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
            @inject(REPORT_ISSUES_CLIENT_TYPES.IIssueContextService) private issueContextService: IIssueContextService,
            @inject(MEDIA_GATEWAY_CLIENT_TYPES.IMediaGatewayService) private mediaGatewayClient: IMediaGatewayService
        ) {
        }

        /**
         * This method is deprecated. Please use {@link createTicketV2}
         */
        @httpPost("/createTicket")
        async createTicket(req: express.Request): Promise<{ success: boolean, message: string }> {
            const session: Session = req.session
            const userId: string = session.userId
            const apiKey: string = req.headers["apikey"] as string
            const orderSource: OrderSource = AppUtil.callSource(apiKey)
            const userContext: UserContext = req.userContext as UserContext
            const ticketData: {
                code: string
                orderId: string
                shipmentId: string
                productType?: ProductType
                comment?: string
                command?: string // Temp hack as app is passing against wrong key
            } = req.body
            if (ticketData.productType === "FITNESS") {
                if (SELF_SERVE_CANCEL_CODES.indexOf(ticketData.code) >= 0) {
                    return await this.cultFitService.selfServeCancelBooking(ticketData.shipmentId, userId)
                }
                if (SELF_SERVE_ATTENDANCE_CODES.indexOf(ticketData.code
                ) >= 0) {
                    return await this.cultFitService.selfServeMarkAttendance(ticketData.shipmentId, userId)
                }
            }
            if (ticketData.productType === "MIND") {
                if (SELF_SERVE_CANCEL_CODES.indexOf(ticketData.code) >= 0) {
                    return await this.mindFitService.selfServeCancelBooking(ticketData.shipmentId, userId)
                }
                if (SELF_SERVE_ATTENDANCE_CODES.indexOf(ticketData.code
                ) >= 0) {
                    return await this.mindFitService.selfServeMarkAttendance(ticketData.shipmentId, userId)
                }
            }
            const issue = await this.CRMIssueService.getIssue(ticketData.code)
            let description
            if (ticketData.comment) {
                description = ticketData.comment
            } else if (ticketData.command) {
                description = ticketData.command
            } else {
                description = issue.subject
            }
            const ticket: IssueTicket = {
                userId: session.userId,
                subject: issue.subject,
                desc: description,
                priority: issue.priority,
                orderId: ticketData.orderId,
                shipmentId: ticketData.shipmentId,
                deviceId: session.deviceId,
                source: this.crmBusiness.getTicketSource(userContext),
                tags: ["SOURCE: " + orderSource]
            }
            const tenant: Tenant = AppUtil.getTenantFromUserContext(userContext)
            const countryId: string = AppUtil.getCountryId(userContext)
            const ticketId = await this.freshdeskService.createTicket(userId, ticket, tenant, countryId)
            if (ticketId) {
                return { success: true, message: issue.acknowledgement }
            } else {
                return { success: false, message: "Error while creating ticket. Try again." }
            }
        }

        @httpPost("/createTicket/v2")
        async createTicketV2(req: express.Request): Promise<{ success: boolean, message: string, actionUrl?: string }> {

            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400)
            .withMessage("Please update app to latest version or email <NAME_EMAIL> to raise ticket")
            .build()
            const session: Session = req.session
            const userId: string = session.userId
            const userContext: UserContext = req.userContext as UserContext
            const user: User = await userContext.userPromise
            const apiKey: string = req.headers["apikey"] as string
            const orderSource: OrderSource = AppUtil.callSource(apiKey)
            const requestedCall: boolean = req.query.requestedCall === "true"
            if (requestedCall && (!user.phone || user.isPhoneChurned)) {
                throw this.errorFactory.withCode(ErrorCodes.FRESHDESK_UPDATE_NUMBER_ERR, 400).withDebugMessage("User attempting request call me button with no number added in account").build()
            }
            req.body.comment = await this.crmBusiness.validateAttachments(req.body.comment)
            const data: {
                issueId: string
                comment: string
                meta?: any
            } = req.body
            const issue = await this.issueBusiness.getIssue(data.issueId)
            const description = data.comment || issue.issueDescription
            const raiseTicketParams: RaiseTicketParams = clone(issue.raiseTicketParams)
            raiseTicketParams.initiateCall = requestedCall

            const ticket: IssueTicket = {
                userId,
                subject: issue.issueDescription,
                desc: description,
                priority: raiseTicketParams.priority,
                orderId: data.meta?.orderId,
                shipmentId: data.meta?.shipmentId,
                deviceId: session.deviceId,
                ticketParams: raiseTicketParams,
                source: this.crmBusiness.getTicketSource(userContext),
                tags: ["SOURCE: " + orderSource]
            }
            const tenant: Tenant = AppUtil.getTenantFromUserContext(userContext)
            const countryId: string = AppUtil.getCountryId(userContext)
            // this.logger.info(`Calling report-issues with ticket payload ${JSON.stringify(ticket)}`)
            const ticketId = await this.freshdeskService.createTicket(userId, ticket, tenant, countryId)
            if (ticketId) {
                if (AppUtil.isCultWatchApp(userContext)) {
                    return {
                        success: true,
                        message: raiseTicketParams.acknowledgementMessage,
                        actionUrl: "cswatch://apptabs"
                    }
                }

                return {
                    success: true,
                    message: requestedCall ? this.getRequestCallbackMessage() : raiseTicketParams.acknowledgementMessage,
                    actionUrl: AppUtil.isSupportTicketViewEnabled(userContext) ? "curefit://ticketdetails?ticketId=" + ticketId : undefined
                }
            } else {
                return { success: false, message: "Error while creating ticket. Try again." }
            }
        }

        @httpPost("/createTicket/userRequest")
        async createGenericTicket(req: express.Request): Promise<{ success: boolean, action: Action }> {
            const session: Session = req.session
            const userId: string = session.userId
            const userContext: UserContext = req.userContext as UserContext
            const apiKey: string = req.headers["apikey"] as string
            const orderSource: OrderSource = AppUtil.callSource(apiKey)
            const data: {
                subject: string,
                comment: string,
                priority: TicketPriority
                meta: any,
                groupId: number
            } = req.body

            const priority: TicketPriority = data.priority || "Low"
            const ticket: IssueTicket = {
                userId,
                subject: data.subject,
                desc: data.comment,
                priority,
                orderId: data.meta.orderId,
                shipmentId: data.meta.shipmentId,
                type: "Enquiry",
                groupId: data.meta.groupId,
                deviceId: session.deviceId,
                source: this.crmBusiness.getTicketSource(userContext),
                tags: ["SOURCE: " + orderSource]
            }
            const tenant: Tenant = AppUtil.getTenantFromUserContext(userContext)
            const countryId: string = AppUtil.getCountryId(userContext)
            const ticketId = await this.freshdeskService.createTicket(userId, ticket, tenant, countryId)
            if (ticketId) {
                return {
                    success: true,
                    action: {
                        actionType: "SHOW_ALERT_MODAL",
                        title: "Thank You",
                        meta: {
                            title: `${data.subject} suggested`,
                            subTitle: "We will get back to you in the next few days with an update.",
                            actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
                        }
                    }
                }
            } else {
                return {
                    success: false,
                    action: {
                        actionType: "SHOW_ALERT_MODAL",
                        title: "Error",
                        meta: {
                            title: "Error while creating ticket. Try again.",
                            subTitle: "",
                            actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
                        }
                    }
                }
            }
        }

        @httpPost("/childIssues")
        async getChildIssues(req: express.Request): Promise<IssueDetailView[]> {
            const userContext: UserContext = req.userContext as UserContext
            const session: Session = req.session
            const userId: string = session.userId
            const data: {
                issueId: string,
                meta: IssueDetailViewMeta
            } = req.body
            return this.issueBusiness.getChildIssues(data.issueId, data.meta, userContext)
        }

        @httpGet("/issues")
        async getIssues(req: express.Request): Promise<{ issues: IssueDetailView[], title: string }> {
            const productType: IssueProductType = req.query.productType
            const productStates: IssueProductState[] = req.query.productStates
            const issueProductType: IssueProductType = req.query.issueProductType
            const state: IssueProductState = req.query.state
            const meta: IssueDetailViewMeta = req.query.meta || {}
            const orderId: string = req.query.orderId || meta.orderId
            const userContext: UserContext = req.userContext as UserContext
            const tenant: Tenant = AppUtil.getTenantFromUserContext(userContext)
            if (!_.isNil(issueProductType) && !_.isNil(state) && !_.isEmpty(issueProductType) && !_.isEmpty(state)) {
                return { issues: await this.issueBusiness.getIssuesByFilter(userContext, issueProductType, state, orderId), title: "HELP" }
            } else if (!_.isEmpty(orderId)) {
                const orderProductDetail = await this.crmBusiness.getOrderDetailsFromOrderIdOrNull(orderId, userContext)
                return { issues: orderProductDetail && orderProductDetail.reportIssues || [], title: "HELP" }
            }
            return { issues: await this.issueBusiness.getIssues(productType, productStates, meta, userContext, tenant), title: "HELP" }
        }

        @httpGet("/s3/getSignedUrl")
        async getReportIssueImageSignedUrl(req: express.Request): Promise<SignedUrlResponse> {
            const fileName: string = req.query.fileName as string
            const config: string = req.query.config as string // left config if required for future purposes if any after integrating media gateway
            return await this.mediaGatewayClient.getPresignedPutUrl({
                bucketName : "cf-app-upload",
                path : "REPORTED_ISSUES",
                fileName,
                contentType : MediaType.IMAGE,
                maxUploadSize : 104857600, // need to be decided
                objectAcl : ObjectAcl.PUBLIC_READ
            })
        }

        @httpGet("/s3/getValidImageUrl")
        async get(req: express.Request): Promise<String> {
            return await this.mediaGatewayClient.validateAndGetDestinationUrl(req.query.imageUrl as string)
        }

        @httpGet("/support/:pageId")
        async getSupportPage(req: express.Request): Promise<SupportView | SupportWebView> {
            const pageId: string = req.params.pageId
            const session: Session = req.session
            const userId: string = session.userId
            const userContext: UserContext = req.userContext as UserContext

            const city = userContext.userProfile.cityId ? await this.cityService.getCityById(userContext.userProfile.cityId) : undefined
            userContext.userProfile.hamletExperimentMap = await this.populateHamletExperimentMap(userContext)
            this.populatePromiseMapCache(userContext, this.serviceInterfaces)

            let numTickets = 0
            const items: SupportCardItem[] = []
            let supportSections: any[] = []
            let orderDetails: OrderDetail[] = undefined

            if (pageId === SUPPORT_BASE && userContext.sessionInfo.isUserLoggedIn) {
                const userTicketResponse: IGetUserTicketResponse = await this.freshdeskService.getUserTickets(userId, 1, 30)
                numTickets = userTicketResponse.results.length // this is just to ensure the tickets are greater than 0 or not. Can be later used to show total count of tickets in App.
                let userTickets = userTicketResponse.results
                userTickets = _.filter(userTickets, ticket => {
                    return !(ticket.status === "Closed" || ticket.status === "Resolved")
                })
                const ticketsToShow: Ticket[] = userTickets.slice(0, 5)
                const supportCardPromises = _.map(ticketsToShow, async ticket => {
                    return await this.crmBusiness.getSupportCardItem(userContext, ticket)
                })
                items.push(...await Promise.all(supportCardPromises))
                supportSections = await this.crmBusiness.getSupportSections(city, userContext)
                orderDetails = await this.crmBusiness.getRecentOrders(userContext)
            }
            const faqs = await this.crmBusiness.getFAQs(userContext, city)
            if (AppUtil.isWeb(userContext)) {
                return new SupportWebView(userContext, faqs, supportSections, numTickets, items)
            }
            return new SupportView(pageId, faqs, supportSections, numTickets, items, userContext, orderDetails)
        }

        @httpGet("/recent/orders")
        async getRecentOrdersData(req: express.Request): Promise<OrderWidgetData[]> {
            const userContext: UserContext = req.userContext as UserContext
            return this.crmBusiness.getRecentOrdersForSupportPageWidget(userContext)
        }

        @httpGet("/recentActiveTickets")
        async getOpenTickets(req: express.Request): Promise<Ticket[]> {
            const session: Session = req.session
            const userId: string = session.userId
            const limit: number = (req.query.limit !== undefined && req.query.limit !== "undefined") ? Number(req.query.limit) : 3
            const pageSize: number = (req.query.poolLimit !== undefined && req.query.poolLimit !== "undefined") ? Number(req.query.poolLimit) : 30
            const userTicketResponse: IGetUserTicketResponse = await this.freshdeskService.getUserTickets(userId, 1, pageSize)
            let userTickets: Ticket[] = userTicketResponse.results
            const openUserTickets: Ticket[] = _.filter(userTickets, ticket => {
                return (this.crmBusiness.getTicketStatusV2(ticket.status, ticket.merged) === "Open")
            })
            let extraUserTickets: Ticket[] = _.filter(userTickets, ticket => {
                return !(this.crmBusiness.getTicketStatusV2(ticket.status, ticket.merged) === "Open" || this.crmBusiness.getTicketStatusV2(ticket.status, ticket.merged) === "Closed")
            })
            if (openUserTickets.length < limit) {
                const extraSlots: number = limit - openUserTickets.length
                if (extraUserTickets.length > extraSlots) {
                    extraUserTickets = extraUserTickets.slice(0, extraSlots)
                }
                userTickets = openUserTickets.concat(extraUserTickets)
            } else {
                userTickets = openUserTickets.slice(0, limit)
            }
            userTickets?.forEach(ticket => {
                ticket.status = this.crmBusiness.getTicketStatusV2(ticket.status, ticket.merged)
            })
            return userTickets
        }

        @httpPost("/supportModal")
        async getSupportModal(req: express.Request) {
            const modalType: SupportOptionsModalType = req.body.modalType
            const meta: IssueDetailViewMeta = req.body.meta
            const payload: any = req.body.payload
            return this.issueBusiness.getSupportOptionsModalData(modalType, meta, payload)
        }

        @httpPost("/preSubmissionAction")
        async getActionForTicketSubmission(req: express.Request) {
            const data: {
                submissionQueryParams: any,
                issueId?: string,
                requestBody: any,
                comment: string
            } = req.body
            return this.crmBusiness.getPreSubmissionAction(data.issueId, data.comment, data.requestBody, data.submissionQueryParams)
        }

        @httpPost("/performActionV2")
        async performActionV2(req: express.Request): Promise<any> {
            const data: {
                meta: IssueDetailViewMeta,
                type: CSAutomationType,
                reason?: string
            } = req.body
            return this.issueBusiness.performActionV2(req.userContext, data.meta, data.type, data.reason)
        }

        @httpPost("/performAction")
        async performAction(req: express.Request): Promise<any> {
            const session: Session = req.session
            const userId: string = session.userId
            const data: {
                issueId: string,
                meta: IssueDetailViewMeta
            } = req.body
            return this.issueBusiness.performAction(req.userContext, data.issueId, data.meta)
        }

        @httpGet("/allTickets")
        async getAllTickets(req: express.Request): Promise<SupportListPageView> {
            const session: Session = req.session
            const userId = session.userId
            const userContext: UserContext = req.userContext as UserContext
            const tz = userContext.userProfile.timezone

            let pageNumber = (req.query.pageNumber !== undefined && req.query.pageNumber !== "undefined") ? Number(req.query.pageNumber) : 1
            if (pageNumber <= 0) { // added for backward compatibility
                pageNumber = 1
            }
            const pageSize = (req.query.limit !== undefined && req.query.limit !== "undefined") ? Number(req.query.limit) : 20
            const userTicketResponse: IGetUserTicketResponse = await this.freshdeskService.getUserTickets(userId, pageNumber, pageSize)
            const userTickets = userTicketResponse.results

            const supportWidgets: SupportActionableCardWidget[] = []
            _.map(userTickets, ticket => {
                supportWidgets.push(this.crmBusiness.getActionableCardForTicket(ticket, tz))
            })

            const supportListPageView = new SupportListPageView(supportWidgets, [])
            supportListPageView.totalHits = ((pageNumber - 1) * pageSize) + userTickets.length + (userTicketResponse.next ? 1 : 0)
            if (userTickets.length === 0) {
                supportListPageView.pageNumber = undefined
            } else {
                supportListPageView.pageNumber = pageNumber + 1
            }
            return supportListPageView
        }

        /*
         * This method will be deprecated. Please use {@link getTicketConversation} for future purposes. This method is just for older apps
         * Introduced in appVersion: 7.60. Removed in appVersion: 7.70
         */

        @httpGet("/ticketDetails")
        async getTicketDetails(req: express.Request): Promise<SupportListPageView> {
            const session: Session = req.session
            const userId = session.userId
            const userContext: UserContext = req.userContext as UserContext
            const ticketId = req.query.ticketId

            if (isNaN(ticketId)) {
                return null
            }

            try {
                const freshDeskTicket: IFreshdeskTicket = await this.freshdeskService.getTicketDetails(userId, ticketId)
                const widgets: PageWidget[] = []

                widgets.push(await this.crmBusiness.getTicketDetailsWidget(freshDeskTicket, userId, userContext))
                const descAndAttachmentsResp = this.crmBusiness.getDescAndAttachments(freshDeskTicket.description_text, freshDeskTicket.attachments)
                if (!_.isEmpty(descAndAttachmentsResp.desc)) {
                    const descriptionWidget: TicketDescriptionWidget = {
                        widgetType: "TICKET_DESCRIPTION_WIDGET",
                        type: "DESCRIPTION",
                        title: "ISSUE DESCRIPTION",
                        description: descAndAttachmentsResp.desc,
                        hasDividerBelow: (!_.isEmpty(descAndAttachmentsResp.attachmentUrls)) ? true : false,
                        dividerType: (!_.isEmpty(descAndAttachmentsResp.attachmentUrls)) ? "SMALL_MARGINATED" : undefined
                    }
                    widgets.push(descriptionWidget)
                }

                if (!_.isEmpty(descAndAttachmentsResp.attachmentUrls)) {
                    const attachmentsWidget: TicketDescriptionWidget = {
                        widgetType: "TICKET_DESCRIPTION_WIDGET",
                        type: "ATTACHMENT",
                        title: "ATTACHMENT",
                        urls: descAndAttachmentsResp.attachmentUrls
                    }
                    widgets.push(attachmentsWidget)
                }
                return new SupportListPageView(widgets, [])
            } catch (e) {
                this.logger.error(e)
                throw this.errorFactory.withCode(ErrorCodes.FRESHDESK_TICKET_DETAILS_NOT_FOUND_ERR, 500).withDebugMessage("Unable to fetch ticket details for ticketId: " + ticketId).build()
            }
        }

        @httpGet("/ticketDetails/v2")
        async getTicketConversation(req: express.Request): Promise<SupportListPageView> {
            const session: Session = req.session
            const userId = session.userId
            const userContext: UserContext = req.userContext as UserContext
            const ticketId = req.query.ticketId
            const overrideStatus: number = parseInt(req.query.overrideStatus)
            if (isNaN(ticketId)) {
                return null
            }

            try {
                const conversations: IConversation[] = []
                const widgets: PageWidget[] = []
                const actions: Action[] = []

                const freshDeskTicket: IFreshdeskTicket = await this.freshdeskService.getTicketDetails(userId, ticketId)
                if (overrideStatus && overrideStatus !== 2) {
                    await this.freshdeskService.updateTicketStatus(userId, ticketId, overrideStatus)
                    freshDeskTicket.status = overrideStatus
                }
                let isUserReply: boolean = true
                widgets.push(await this.crmBusiness.getTicketDetailsWidget(freshDeskTicket, userId, userContext))
                widgets.push(...this.crmBusiness.getChatWidgets(freshDeskTicket.description_text, freshDeskTicket.attachments, true, freshDeskTicket.created_at, userContext, isUserReply, "Issue Description"))

                let conversationResp: IGetTicketConversationResponse
                let pageNumber = 1
                const pageSize = 30
                do {
                    conversationResp = await this.freshdeskService.getTicketPublicConversations(userId, ticketId, pageNumber, pageSize)
                    conversations.push(...conversationResp.results)
                    pageNumber++
                } while (conversationResp.next && pageNumber <= 25)
                conversations.forEach(conversation => {
                    widgets.push(...this.crmBusiness.getChatWidgets(conversation.body_text, conversation.attachments, conversation.isUserReply, new Date(conversation.created_at), userContext, isUserReply !== conversation.isUserReply))
                    isUserReply = conversation.isUserReply
                })

                const ticketStatus: Status = this.crmBusiness.getTicketStatus(FreshdeskTicketsStatusMapping[freshDeskTicket.status], false)
                let footerWidget: any = undefined
                if (ticketStatus.text === "OPEN" || overrideStatus == 2) {
                    actions.push({
                        actionType: "NAVIGATION",
                        title: "Reply",
                        url: "curefit://ticketReply?ticketId=" + ticketId + "&isSprinklrTicket=false"
                    })
                } else {
                    const ticketStatus: string = this.crmBusiness.getTicketStatus(FreshdeskTicketsStatusMapping[freshDeskTicket.status], false).text
                    widgets.push(this.crmBusiness.getTicketResolutionWidget(ticketStatus, freshDeskTicket.id))
                    if (ticketStatus === "CLOSED" || ticketStatus === "RESOLVED") {
                        if (freshDeskTicket.custom_fields?.cf_merged_ticket !== true && !freshDeskTicket.tags.includes("waiting automation")) {
                            footerWidget = await this.crmBusiness.getTicketCSATWidgetIfApplicable(ticketId, userContext)
                        }
                    }
                }

                return new SupportListPageView(widgets, actions, footerWidget)
            } catch (e) {
                this.logger.error(e)
                throw this.errorFactory.withCode(ErrorCodes.FRESHDESK_TICKET_DETAILS_NOT_FOUND_ERR, 500).withDebugMessage("Unable to fetch ticket details for ticketId: " + ticketId).build()
            }
        }

        @httpGet("/getTicketCSATWidget")
        async getTicketCSATWidgetIfApplicable(req: express.Request): Promise<TicketCSATFooterWidget> {
            const session: Session = req.session
            const userId = session.userId
            const userContext: UserContext = req.userContext as UserContext
            const ticketId = req.query.ticketId
            const isSprinklrTicket: boolean = JSON.parse(req.query.isSprinklrTicket || "false")
            if (!ticketId) {
                return null
            }
            return await this.crmBusiness.getTicketCSATWidgetIfApplicable(ticketId, userContext, isSprinklrTicket)
        }

        @httpPost("/ticket/reply")
        async replyToTicket(req: express.Request): Promise<{ message: string, widgets: PageWidget[] }> {
            const session: Session = req.session
            const userId = session.userId
            const userContext: UserContext = req.userContext as UserContext
            const ticketId = req.query.ticketId
            if (isNaN(ticketId)) {
                return null
            }
            req.body.reply = await this.crmBusiness.validateAttachments(req.body.reply)
            const data: {
                reply: string
            } = req.body
            const reply: IAddCustomerReplyRequest = { body: data.reply }
            const conversation: ITicketConversation = await this.freshdeskService.addCustomerReplyToTicket(userId, ticketId, reply)
            if (conversation) {
                const widgets: PageWidget[] = []
                widgets.push(...this.crmBusiness.getChatWidgets(conversation.body_text, conversation.attachments, true, new Date(conversation.created_at), userContext, true))
                return { message: "Your reply has been successfully recorded!", widgets: widgets }
            } else {
                return { message: "Failed to reply to the ticket! Please try again after some time.", widgets: [] }
            }
        }

        @httpGet("/supportList/:pageId")
        async getSupportList(req: express.Request): Promise<SupportListPageView> {
            const session: Session = req.session
            const userId = session.userId
            const pageId: string = req.params.pageId
            const pageFrom = req.query.pageFrom
            const appVersion: number = Number(req.headers["appversion"])
            const userContext: UserContext = req.userContext as UserContext
            userContext.userProfile.hamletExperimentMap = await this.populateHamletExperimentMap(userContext)
            const pageSize = (req.query.limit !== undefined && req.query.limit !== "undefined") ? Number(req.query.limit) : 20
            const itemResp = await this.crmBusiness.getSupportItemsForVerticals(pageId, req.query.pageNumber, userId, pageSize, userContext, appVersion, pageFrom, session, req)
            const supportListPageViewPromise = itemResp.supportListPageViewPromise
            const nextPageNumber = itemResp.nextPageNumber
            if (supportListPageViewPromise === undefined) {
                this.logger.error("Invalid request params in getting support list")
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("invalid request params while getting support list").build()
            }
            const supportListPageView = await supportListPageViewPromise
            supportListPageView.pageNumber = (nextPageNumber === undefined) ? undefined : nextPageNumber
            return supportListPageView
        }

        @httpPost("/supportListAll")
        async getAllSupportList(req: express.Request): Promise<SupportListCombinedPageView> {
            const session: Session = req.session
            const userId = session.userId
            const pageFrom = req.query.pageFrom
            const appVersion: number = Number(req.headers["appversion"])
            const userContext: UserContext = req.userContext as UserContext
            const requestWidgetCountBySource: SourceWithWidgetCount[] = req.body
            userContext.userProfile.hamletExperimentMap = await this.populateHamletExperimentMap(userContext)
            const pageSize = (req.query.limit !== undefined && req.query.limit !== "undefined") ? Number(req.query.limit) : 20
            return this.crmBusiness.getSupportItemsForAllVerticals(requestWidgetCountBySource, pageSize, userId, userContext, appVersion, pageFrom, session, req)
        }

        @httpGet("/ticket/csat")
        async getTicketCSATQuestions(req: express.Request): Promise<SupportCSATPageView> {
            const session: Session = req.session
            const userId = session.userId
            const userContext: UserContext = req.userContext as UserContext
            const ticketId = req.query.ticketId
            const isSprinklrTicket: boolean = JSON.parse(req.query.isSprinklrTicket || "false")
            const questionId = req.query.questionId
            let freshDeskTicket: IFreshdeskTicket
            if (isSprinklrTicket) {
                const tickets = (await this.sprinklrClient.getCaseByFilters({
                    type: "AND",
                    filters: [{
                        type: "IN",
                        key: "caseNumber",
                        values: [ticketId]
                    }]
                })).data.results
                if (_.isEmpty(tickets)) {
                    throw this.errorFactory.withCode(ErrorCodes.SPRINKLR_TICKET_NOT_FOUND_ERR, 404).withDebugMessage("Sprinklr ticket not found for ticketId: " + ticketId).build()
                }
                return this.crmBusiness.buildSupportCSATPageView(null, questionId, userId, tickets[0] )
            } else {
                freshDeskTicket = await this.freshdeskService.getTicketDetails(userId, ticketId)
                return this.crmBusiness.buildSupportCSATPageView(freshDeskTicket, questionId, userId)
            }
        }

        @httpPost("/ticket/csat")
        async postTicketCSATResponse(req: express.Request) {
            const session: Session = req.session
            const userId = session.userId
            const userContext: UserContext = req.userContext as UserContext
            const ticketId = req.query.ticketId
            const isSprinklrTicket: boolean = JSON.parse(req.query.isSprinklrTicket || "false")
            const data: {
                response: ICSATQuestionnaireResponse[]
            } = req.body
            return this.crmBusiness.postCSATResponse(ticketId, data.response, isSprinklrTicket)
        }

        @httpGet("/contact/email")
        async getContactUsEmailId(req: express.Request): Promise<{emailId: string}> {
            // return {emailId: Constants.customerCareMail}
            return {emailId: undefined}
        }

        @httpPost("/contact/submit")
        async submitContactUsResponse(req: express.Request): Promise<{ success: boolean, message: string, actionUrl?: string }> {
            const session: Session = req.session
            const userId = session.userId
            const userContext: UserContext = req.userContext as UserContext
            const apiKey: string = req.headers["apikey"] as string
            const orderSource: OrderSource = AppUtil.callSource(apiKey)
            req.body.message = await this.crmBusiness.validateAttachments(req.body.message)
            const details: {
                subject: string,
                message: string
            } = req.body
            const issueContext: IIssueContext = await this.issueContextService.getIssueContextByName("CONTACT_US")
            if (_.isEmpty(issueContext) || !issueContext.isActive || _.isEmpty(issueContext.issueId)) {
                this.logger.error("problem with issueCOntext for contextName: CONTACT_US. IssueContext: " + issueContext)
                return { success: false, message: "Error while creating ticket. Try again." }
            }
            const issue = await this.issueBusiness.getIssue(issueContext.issueId)
            const raiseTicketParams: RaiseTicketParams = issue.raiseTicketParams

            const ticket: IssueTicket = {
                userId,
                subject: details.subject,
                desc: details.message,
                priority: raiseTicketParams.priority,
                deviceId: session.deviceId,
                ticketParams: raiseTicketParams,
                source: this.crmBusiness.getTicketSource(userContext),
                tags: ["SOURCE: " + orderSource]
            }
            const tenant: Tenant = AppUtil.getTenantFromUserContext(userContext)
            const countryId: string = AppUtil.getCountryId(userContext)
            // this.logger.info(`Calling report-issues with ticket payload ${JSON.stringify(ticket)} for creating contactus FD ticket`)
            const ticketId = await this.freshdeskService.createTicket(userId, ticket, tenant, countryId)
            if (ticketId) {
                return {
                    success: true,
                    message: raiseTicketParams.acknowledgementMessage,
                    actionUrl: AppUtil.isSupportTicketViewEnabled(userContext) ? "curefit://ticketdetails?ticketId=" + ticketId : undefined
                }
            } else {
                return { success: false, message: "Error while creating ticket. Try again." }
            }
        }

        // --------------------------------------------- PRIVATE METHODS -----------------------------------------------

        private async populateHamletExperimentMap(userContext: UserContext) {
            let map = {}
            try {
                map = (await this.hamletBusiness.getUserAllocations(AppUtil.getHamletContext(userContext, []))).assignmentsMap
            } catch (e) {
                map = {}
                this.logger.error(`Hamlet user allocation failing for user: ${userContext.userProfile.userId}`)
                this.rollbarService.sendError(e, {user: {id: userContext.userProfile.userId}})
            }

            return map
        }

        private populatePromiseMapCache(userContext: UserContext, serviceInterfaces: CFServiceInterfaces) {
            if (!userContext.userProfile.promiseMapCache) {
                userContext.userProfile.promiseMapCache ??= new PromiseCache(serviceInterfaces)
            }
        }

        private getRequestCallbackMessage() {
            const currentTime = new Date()
            const currentHour = currentTime.getHours()
            const currentMinutes = currentTime.getMinutes()
            if ((currentHour > 9 || (currentHour === 9 && currentMinutes >= 0)) && (currentHour < 20 || (currentHour === 20 && currentMinutes <= 30))) {
                return "Our team will give you a call on your registered mobile number in the next 2 hours"
            } else {
                return "Our team will give you a call on your registered mobile number tomorrow between 9:00am to 12:00pm"
            }
        }
    }

    return CRMController
}

export default controllerFactory
