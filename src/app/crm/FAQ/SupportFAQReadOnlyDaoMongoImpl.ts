import { inject, injectable } from "inversify"
import { MongoReadonlyDao } from "@curefit/mongo-utils"
import { ISupportFAQReadOnlyDao } from "./SupportFAQDao"
import { SupportFAQModel } from "./SupportFAQModel"
import { SupportFAQ } from "./SupportFAQ"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { SupportFAQSchema } from "./SupportFAQSchema"

@injectable()
export class SupportFAQReadOnlyDaoMongoImpl extends MongoReadonlyDao<SupportFAQModel, SupportFAQ> implements ISupportFAQReadOnlyDao {
    constructor(
        @inject(CUREFIT_API_TYPES.SupportFAQSchema) SupportPageFAQSchema: SupportFAQSchema,
        @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
        super(SupportPageFAQSchema.mongooseModel, logger, SupportPageFAQSchema.isLeanQueryEnabled)
    }
}
