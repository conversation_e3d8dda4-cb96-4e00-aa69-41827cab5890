import { MONG<PERSON>_TYPES, MultiMongooseAccess, MultiMongooseSchema } from "@curefit/mongo-utils"
import { SupportFAQModel } from "./SupportFAQModel"
import { Schema } from "mongoose"
import { inject } from "inversify"
import { ReadPreference } from "mongodb"

export class SupportFAQSchema extends MultiMongooseSchema<SupportFAQModel> {

    constructor(
        @inject(MONGO_TYPES.MultiMongooseAccess) mongooseAccess: MultiMongooseAccess
    ) {
        super(mongooseAccess, "SupportFAQs", "DEFAULT", ReadPreference.SECONDARY_PREFERRED)
    }

    protected schema() {
        return {
            FAQType: {type: String},
            filterType: {type: String, index: true},
            filterId: { type: String},
            tenant: {type: [String]},
            meta: {type: URLMetaSchema}
        }
    }

}

export const URLMetaSchema = new Schema({
    title: {type: String},
    url: {type: String, required: true}
}, { _id: false })

