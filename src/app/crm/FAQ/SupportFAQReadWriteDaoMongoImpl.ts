import { inject, injectable } from "inversify"
import { ISupportFAQReadWriteDao } from "./SupportFAQDao"
import { MongoReadWriteDao } from "@curefit/mongo-utils"
import { SupportFAQModel } from "./SupportFAQModel"
import { SupportFAQ } from "./SupportFAQ"
import { BASE_TYPES, ILogger } from "@curefit/base"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { SupportFAQReadOnlyDaoMongoImpl } from "./SupportFAQReadOnlyDaoMongoImpl"
import { SupportFAQSchema } from "./SupportFAQSchema"

@injectable()
export class SupportFAQReadWriteDaoMongoImpl extends MongoReadWriteDao<SupportFAQModel, SupportFAQ> implements ISupportFAQReadWriteDao  {
    constructor(
        @inject(CUREFIT_API_TYPES.SupportFAQSchema) SupportPageFAQSchema: SupportFAQSchema,
        @inject(CUREFIT_API_TYPES.SupportFAQReadOnlyDaoMongoImpl) readonlyDao: SupportFAQReadOnlyDaoMongoImpl,
        @inject(BASE_TYPES.ILogger) logger: ILogger
    ) {
        super(SupportPageFAQSchema.mongooseModel, readonlyDao, logger)
    }
}
