import { inject, injectable } from "inversify"
import { InMemoryCacheService } from "@curefit/memory-cache"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { BASE_TYPES, ILogger } from "@curefit/base"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { ISupportFAQReadOnlyDao } from "./SupportFAQDao"
import { City } from "@curefit/location-common"
import { FAQMeta } from "./SupportFAQ"
import * as _ from "lodash"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { Tenant } from "@curefit/user-common"

@injectable()
class SupportFAQConfigCache extends InMemoryCacheService<Map<Tenant, Map<string, Map<string, FAQMeta>>>> {

    constructor(
        @inject(CUREFIT_API_TYPES.SupportFAQReadOnlyDaoMongoImpl) private supportFaqReadOnlyDao: ISupportFAQReadOnlyDao,
        @inject(LOCATION_TYPES.CityService) private cityService: ICityService,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger, 33 * 60, [cityService as any])
        this.load("SupportFAQConfigCache")
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    async loadData(): Promise<Map<Tenant, Map<string, Map<string, FAQMeta>>>> {
        const TENANT_TO_CITY_FAQ_URLS_CONFIG_MAP = new Map<Tenant, Map<string, Map<string, FAQMeta>>>()
        TENANT_TO_CITY_FAQ_URLS_CONFIG_MAP.set(Tenant.CUREFIT_APP, await this.evaluateCityToFaqMap(Tenant.CUREFIT_APP))
        TENANT_TO_CITY_FAQ_URLS_CONFIG_MAP.set(Tenant.LIVEFIT_APP, await this.evaluateCityToFaqMap(Tenant.LIVEFIT_APP))
        TENANT_TO_CITY_FAQ_URLS_CONFIG_MAP.set(Tenant.CULTSPORT_APP, await this.evaluateCityToFaqMap(Tenant.CULTSPORT_APP))
        return TENANT_TO_CITY_FAQ_URLS_CONFIG_MAP
    }

    private async evaluateCityToFaqMap(tenant: Tenant) {
        const cities: City[] = this.cityService.listCities(tenant)
        const CITY_ID_TO_FAQ_URLS_CONFIG_MAP = new Map<string, Map<string, FAQMeta>>()

        for (const city of cities) {
            const FAQ_TYPE_VS_FAQ_URL = new Map<string, FAQMeta>()

            const cityLevelFAQs = await this.supportFaqReadOnlyDao.find({condition: {filterType: "CITY", filterId: city.cityId, tenant: { $in : [tenant] }}})
            cityLevelFAQs.map(faq => {FAQ_TYPE_VS_FAQ_URL.set(faq.FAQType, faq.meta)})

            const countryLevelFAQs = await this.supportFaqReadOnlyDao.find({condition: {filterType: "COUNTRY", filterId: city.countryId,  tenant: { $in : [tenant] }}})
            countryLevelFAQs.map(faq => {
                if (!FAQ_TYPE_VS_FAQ_URL.has(faq.FAQType)) {
                    FAQ_TYPE_VS_FAQ_URL.set(faq.FAQType, faq.meta)
                }
            })

            const defaultLevelFAQs = await this.supportFaqReadOnlyDao.find({condition: {filterType: "DEFAULT",  tenant: { $in : [tenant] }}})
            defaultLevelFAQs.map(faq => {
                if (!FAQ_TYPE_VS_FAQ_URL.has(faq.FAQType)) {
                    FAQ_TYPE_VS_FAQ_URL.set(faq.FAQType, faq.meta)
                }
            })
            CITY_ID_TO_FAQ_URLS_CONFIG_MAP.set(city.cityId, FAQ_TYPE_VS_FAQ_URL)
        }
        return CITY_ID_TO_FAQ_URLS_CONFIG_MAP
    }

    public getFAQConfigForCity(cityId: string, tenant: Tenant) {
        return _.isEmpty(cityId) || _.isEmpty(tenant) ? undefined : this.cache.get(tenant).get(cityId)
    }

}

export default SupportFAQConfigCache
