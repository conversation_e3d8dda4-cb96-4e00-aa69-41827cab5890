import {
    ALBUS_CLIENT_TYPES,
    Consultation,
    DiagnosticsTestOrderResponse,
    IHealthfaceService
} from "@curefit/albus-client"
import { FoodBooking } from "@curefit/alfred-client"
import { Action } from "@curefit/apps-common"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ConsultationProduct } from "@curefit/care-common"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { CULT_CLIENT_TYPES, ICultServiceOld as ICultService } from "@curefit/cult-client"
import { BulkBookingResponse, CultBooking, CultBookingResponse } from "@curefit/cult-common"
import { ListingBrandIdType } from "@curefit/eat-common"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { IFreshdeskTicket } from "@curefit/freshdesk-common"
import { GEARVAULT_CLIENT_TYPES, GearService } from "@curefit/gearvault-client"
import { GYMFIT_CLIENT_TYPES, IGymfitService } from "@curefit/gymfit-client"
import { SprinklrCase } from "@curefit/sprinklr-node-client"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { City, VerticalType } from "@curefit/location-common"
import { IMediaGatewayService, MEDIA_GATEWAY_CLIENT_TYPES } from "@curefit/media-gateway-js-client"
import { SortOrder } from "@curefit/mongo-utils"
import { IOrderService as IOmsApiClient, OMS_API_CLIENT_TYPES } from "@curefit/oms-api-client"
import { BaseOrder, Order } from "@curefit/order-common"
import {
    CsatResponseService,
    ICSATQuestionnaire,
    ICSATQuestionnaireResponse,
    ICSATQuestionnaireResponseRequest,
    ITagResponse,
    REPORT_ISSUES_CLIENT_TYPES,
    Ticket
} from "@curefit/report-issues-client"
import { SPORT_API_CLIENT_TYPES, SportsApi } from "@curefit/sports-api-client-node"
import { BaseBooking as PlayBaseBooking } from "@curefit/sports-api-common"
import { Session, UserContext } from "@curefit/userinfo-common"
import { CdnUtil, TimeUtil, Timezone, promiseWithTimeout } from "@curefit/util-common"
import { ISegmentService } from "@curefit/vm-models"
import * as express from "express"
import { inject, injectable } from "inversify"
import * as _ from "lodash"
import * as moment from "moment"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import CFAPIJavaService from "../CFAPIJavaService"
import ConsultationListPageViewBuilder from "../care/ConsultationListPageViewBuilder"
import MeDiagnosticTestListPageViewBuilder from "../care/MeDiagnosticTestListPageView"
import ClassListPageViewBuilder from "../cult/ClassListPageViewBuilder"
import SportListPageViewBuilder from "../fitsoSports/SportsListPageViewBuilder"
import GearListPageViewBuilder from "../gear/support/GearListPageViewBuilder"
import GymListPageViewBuilder from "../gymfit/GymListPageViewBuilder"
import MealListPageViewBuilder from "../order/MealListPageViewBuilder"
import OrderProductDetailBuilder, { OrderProductDetail } from "../order/OrderProductDetailBuilder"
import OrderViewBuilder, { OrderDetail, OrderWidgetData } from "../order/OrderViewBuilder"
import { PageWidget, PageWidgetWithSource } from "../page/Page"
import {
    Status,
    SupportActionableCardWidget,
    SupportCardItem,
    TicketCSATFooterWidget,
    TicketChatWidget,
    TicketDetailsWidget,
    TicketResolutionWidget
} from "../page/PageWidgets"
import { StoreListPageViewBuilder } from "../store/support/StoreListPageViewBuilder"
import INavBarBusiness from "../user/INavBarBusiness"
import IUserBusiness from "../user/IUserBusiness"
import ActionUtil from "../util/ActionUtil"
import AppUtil from "../util/AppUtil"
import { CareUtil } from "../util/CareUtil"
import CollectionUtil from "../util/CollectionUtil"
import { FAQMeta } from "./FAQ/SupportFAQ"
import SupportFAQConfigCache from "./FAQ/SupportFAQConfigCache"
import {
    FreshdeskTicketSourceMapping,
    FreshdeskTicketsStatusMapping,
    freshdeskDesktopSourceAgents
} from "./FreshdeskConstants"
import { PreSubmissionFlowType } from "./IssueBusiness"
import {
    SupportCSATAnswer,
    SupportCSATPageView,
    SupportCSATQuestion,
    SupportCSATQuestionAnswer
} from "./SupportCSATPageView"
import { SourceWithWidgetCount, SupportListCombinedPageView, SupportListPageView } from "./SupportListPageView"

const SUPPORT_CSAT_PRIMARY_QUESTION_ID = 1
const SUPPORT_CSAT_PRIMARY_QUESTION_ID_NEW = 6
const CSAT_NEW_FLOW_APP_VERSION = 9.47

@injectable()
class CRMBusiness {

    constructor(
        @inject(CUREFIT_API_TYPES.NavBarBusiness) private navBarBusiness: INavBarBusiness,
        @inject(CUREFIT_API_TYPES.UserBusiness) private userBusiness: IUserBusiness,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
        @inject(CUREFIT_API_TYPES.OrderProductDetailBuilder) private orderProductDetailBuilder: OrderProductDetailBuilder,
        @inject(CUREFIT_API_TYPES.ClassListPageViewBuilder) private classListPageViewBuilder: ClassListPageViewBuilder,
        @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
        @inject(ALBUS_CLIENT_TYPES.CultPersonalTrainingService) private cultPTService: IHealthfaceService,
        @inject(GEARVAULT_CLIENT_TYPES.GearService) private gearService: GearService,
        @inject(CUREFIT_API_TYPES.GearListPageViewBuilder) private gearListPageViewBuilder: GearListPageViewBuilder,
        @inject(CUREFIT_API_TYPES.StoreListPageBuilder) private storeListPageBuilder: StoreListPageViewBuilder,
        @inject(CUREFIT_API_TYPES.MealListPageViewBuilder) private mealListPageViewBuilder: MealListPageViewBuilder,
        @inject(CUREFIT_API_TYPES.MeDiagnosticTestListPageViewBuilder) private meDiagnosticTestListPageViewBuilder: MeDiagnosticTestListPageViewBuilder,
        @inject(CUREFIT_API_TYPES.ConsultationListPageViewBuilder) private consultationListPageViewBuilder: ConsultationListPageViewBuilder,
        @inject(CUREFIT_API_TYPES.SupportFAQConfigCache) private supportFAQConfigCache: SupportFAQConfigCache,
        @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
        @inject(GYMFIT_CLIENT_TYPES.GymfitService) private gymfitService: IGymfitService,
        @inject(CUREFIT_API_TYPES.GymListPageViewBuilder) private gymListPageViewBuilder: GymListPageViewBuilder,
        @inject(CUREFIT_API_TYPES.SportListPageViewBuilder) private sportListPageViewBuilder: SportListPageViewBuilder,
        @inject(REPORT_ISSUES_CLIENT_TYPES.CsatReponseService) private csatResponseService: CsatResponseService,
        @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService,
        @inject(MEDIA_GATEWAY_CLIENT_TYPES.IMediaGatewayService) private mediaGatewayClient: IMediaGatewayService,
        @inject(CUREFIT_API_TYPES.OrderViewBuilder) private orderViewBuilder: OrderViewBuilder,
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(CUREFIT_API_TYPES.CFAPIJavaService) public cfAPIJavaService: CFAPIJavaService,
        @inject(OMS_API_CLIENT_TYPES.OrderService) private omsApiClient: IOmsApiClient,
        @inject(SPORT_API_CLIENT_TYPES.SportsApi) private sportsApi: SportsApi
    ) {
    }

    public async getRecentOrders(userContext: UserContext): Promise<OrderDetail[]> {
        const userId = userContext.userProfile.userId
        const timestamp = moment().subtract(1, "month").toISOString()
        const orders: BaseOrder[] = await this.omsApiClient.getViableOrdersForUser(userId, {
            pageToken: undefined,
            count: 5,
            sortOrder: SortOrder.DESC,
            vertical: "ALL",
            dateLimit: timestamp,
            paginated: true,
            orderSourceFilter: {orderSources: ["CULTGEAR_APP", "CULTGEAR_PHONEPE_APP", "SUGARFIT_APP", "SUGARFIT_CMT"], include: false}
        })
        const orderDetailPromises: Promise<OrderDetail>[] = _.map(orders, order => {
            // Temp hack as some sub order entity does not have user id
            order.userId = userId
            return this.orderViewBuilder.buildOrderDetailView(userContext, order, false, "ORDER_V1", "support")
        })
        const orderDetailsArray: OrderDetail[] = await Promise.all(orderDetailPromises)
        return _.filter(orderDetailsArray, orderDetail => !_.isEmpty(orderDetail))
    }

    public async getRecentOrdersForSupportPageWidget(userContext: UserContext): Promise<OrderWidgetData[]> {
        const orders: OrderDetail[] = await this.getRecentOrders(userContext)
        return _.map(orders, order => {
            return {
                orderId: order.orderId,
                price: order.price,
                amountPaid: order.amountPaid,
                activityType: order.activityType,
                orderStatus: order.orderStatus,
                reportIssues: order.reportIssues,
                title: order.title,
                subTitle: order.subTitle,
                action: order.action,
                orderDate: order.orderDate,
                seeDetailAction: order.seeDetailAction
            }
        })
    }

    public async getSupportItemsForVerticals(pageId: string, page: string, userId: string, pageSize: number, userContext: UserContext, appVersion: number, pageFrom: string, session: Session, req: express.Request) {
        let pageNumber
        let nextPageNumber
        let supportListPageViewPromise: Promise<SupportListPageView>
        switch (pageId) {
            case "classes":
                pageNumber = (page !== undefined && page !== "undefined") ? Number(page) : 1
                const cultBulkBookings: BulkBookingResponse = await this.cultFitService.getBookingsByPage(userId, pageNumber, pageSize)
                const cultBookings = cultBulkBookings[userId]
                supportListPageViewPromise = this.classListPageViewBuilder.buildClassListPageView(userContext, cultBookings.bookings, cultBookings.waitlists, pageNumber)
                nextPageNumber = (cultBookings.bookings.length + cultBookings.waitlists.length > 0) ? pageNumber + 1 : undefined
                break

            case "meals":
                pageNumber = (page !== undefined && page !== "undefined") ? Number(page) : 1
                let listingBrand: ListingBrandIdType = undefined
                if (AppUtil.isStoreTabSupported(userContext)) {
                    // filtering out the whole.fit orders when store tab is supported
                    listingBrand = "EAT_FIT"
                }
                const foodBookings: FoodBooking[] = []
                supportListPageViewPromise = this.mealListPageViewBuilder.buildMealListPageView(userContext, foodBookings, pageNumber)
                nextPageNumber = (foodBookings.length > 0) ? pageNumber + 1 : undefined
                break

            case "gear":
                pageNumber = (page !== undefined && page !== "undefined") ? Number(page) : 1
                const gearOrders = await this.gearService.getAllOrders(userId)
                supportListPageViewPromise = this.gearListPageViewBuilder.buildGearListPageView(userContext, gearOrders, pageNumber)
                nextPageNumber = undefined // setting deliberately in gear. Consult gear team before changing
                break

            case "store":
                pageNumber = (page !== undefined && page !== "undefined") ? Number(page) : 1
                const start = (pageNumber - 1) * pageSize
                const storeOrders: Order[] = await this.omsApiClient.getViableOrdersForUser(userId, {
                    pageToken: undefined,
                    offset: start,
                    count: pageSize,
                    sortOrder: SortOrder.DESC,
                    vertical: "STORE",
                    purelySuccessful: true
                })
                supportListPageViewPromise = this.storeListPageBuilder.buildStoreListPageView(userContext, storeOrders, pageNumber)
                nextPageNumber = (storeOrders.length > 0) ? pageNumber + 1 : undefined
                break

            case "diagnostictests":
                pageNumber = (page !== undefined && page !== "undefined") ? Number(page) : 0
                let diagnostics: DiagnosticsTestOrderResponse[] = CollectionUtil.getNullSafeList(await this.healthfaceService.getDiagnosticTests(userId, pageNumber, pageSize))
                const diagnosticPromises = _.map(diagnostics, async diagnostic => {
                    const product = await this.catalogueService.getProduct(diagnostic.productCodes[0])
                    diagnostic.firstProductName = product?.title || "Diagnostic Test"
                    return diagnostic
                })
                diagnostics = await Promise.all(diagnosticPromises)
                supportListPageViewPromise = this.meDiagnosticTestListPageViewBuilder.buildMeDiagnosticTestListPageView(userContext, diagnostics, appVersion, pageNumber, pageFrom)
                nextPageNumber = (diagnostics.length > 0) ? pageNumber + 1 : undefined
                break

            case "consultations":
                pageNumber = (page !== undefined && page !== "undefined") ? Number(page) : 0
                let consultations: Consultation[] = CollectionUtil.getNullSafeList(await this.healthfaceService.getConsultations(userId, pageNumber, pageSize))
                const consultationsPromise = _.map(consultations, async consultation => {
                    if (CareUtil.followUpActionRequired(consultation.consultationUserState, consultation.followUpContext)) {
                        consultation.followupProducts = await this.getFollowupProducts(consultation.followUpContext.productCodes)
                    }
                    return consultation
                })
                consultations = await Promise.all(consultationsPromise)
                supportListPageViewPromise = this.consultationListPageViewBuilder.buildConsultationListPageView(userContext, consultations, appVersion, session.sessionData.cityId, pageNumber, pageFrom)
                nextPageNumber = (consultations.length > 0) ? pageNumber + 1 : undefined
                break

            case "gymfitclasses":
                pageNumber = (page !== undefined && page !== "undefined") ? Number(page) : 1
                const gymfitCheckIns = await this.gymfitService.getCheckinsForUser(userId, null, 10, pageNumber)
                supportListPageViewPromise = this.gymListPageViewBuilder.buildGymClassListPageViewBuilder(userContext, gymfitCheckIns, pageNumber)
                nextPageNumber = gymfitCheckIns.length > 0 ? pageNumber + 1 : undefined
                break
            case "onepassclasses":
                pageNumber = (page !== undefined && page !== "undefined") ? Number(page) : 1
                const onepassCheckins = await this.gymfitService.getCheckinsForUser(userId, null, 10, pageNumber, "ONEPASS")
                supportListPageViewPromise = this.gymListPageViewBuilder.buildGymClassListPageViewBuilder(userContext, onepassCheckins, pageNumber, true)
                nextPageNumber = onepassCheckins.length > 0 ? pageNumber + 1 : undefined
                break
            case "ptSessions":
                pageNumber = (page !== undefined && page !== "undefined") ? Number(page) : 0
                let ptSessions: Consultation[] = CollectionUtil.getNullSafeList(await this.cultPTService.getConsultations(userId, pageNumber, pageSize))
                const ptSessionsPromise = _.map(ptSessions, async session => {
                    if (CareUtil.followUpActionRequired(session.consultationUserState, session.followUpContext)) {
                        session.followupProducts = await this.getFollowupProducts(session.followUpContext.productCodes)
                    }
                    return session
                })
                ptSessions = await Promise.all(ptSessionsPromise)
                supportListPageViewPromise = this.consultationListPageViewBuilder.buildConsultationListPageView(userContext, ptSessions, appVersion, session.sessionData.cityId, pageNumber, pageFrom, undefined, "LIVE_PERSONAL_TRAINING")
                nextPageNumber = (ptSessions.length > 0) ? pageNumber + 1 : undefined
                break
            case "sports":
                pageNumber = (page !== undefined && page !== "undefined") ? Number(page) : 1
                const playSessions: PlayBaseBooking[] = await this.sportsApi.getUserBookings(userId, 20, pageNumber)
                supportListPageViewPromise = this.sportListPageViewBuilder.buildSportsListPageViewBuilder(userContext, (playSessions != null) ? playSessions : [])
                nextPageNumber = (playSessions != null && playSessions.length > 0) ? pageNumber + 1 : undefined
                break
        }
        return { nextPageNumber, supportListPageViewPromise }
    }

    private async getSupportItemsForClasses(userContext: UserContext, pageNumber: number, pageSize: number): Promise<SupportActionableCardWidget[]> {
        const userId = userContext.userProfile.userId
        const cultBulkBookings = await this.cultFitService.getBookingsByPage(userId, pageNumber, pageSize)
        const cultBookings = cultBulkBookings[userId]
        return this.classListPageViewBuilder.buildClassListWidgets(userContext, cultBookings.bookings, cultBookings.waitlists)
    }

    private async getSupportItemsForMeals(userContext: UserContext, pageNumber: number, pageSize: number): Promise<SupportActionableCardWidget[]> {
        const userId = userContext.userProfile.userId
        let listingBrand: ListingBrandIdType = undefined
        if (AppUtil.isStoreTabSupported(userContext)) {
            listingBrand = "EAT_FIT" // filtering out the whole.fit orders when store tab is supported
        }
        const foodBookings: FoodBooking[] = []
        // Deprecating the flow. API returns empty response for all users.
        return this.mealListPageViewBuilder.buildMealListWidgets(userContext, foodBookings)
    }

    private async getSupportItemsForGymfit(userContext: UserContext, pageNumber: number, pageSize: number): Promise<SupportActionableCardWidget[]> {
        const userId = userContext.userProfile.userId
        const gymfitCheckIns = await this.gymfitService.getCheckinsForUser(userId, null, pageSize, pageNumber)
        return this.gymListPageViewBuilder.buildGymClassListWidgetsBuilder(userContext, gymfitCheckIns)
    }

    private async getSupportItemsForDiagnostics(userContext: UserContext, pageNumber: number, pageSize: number, appVersion: number, pageFrom: string): Promise<SupportActionableCardWidget[]> {
        const userId = userContext.userProfile.userId
        const diagnosticTests: DiagnosticsTestOrderResponse[] = CollectionUtil.getNullSafeList(await this.healthfaceService.getDiagnosticTests(userId, pageNumber, pageSize))
        const diagnosticPromises = _.map(diagnosticTests, async diagnostic => {
            const product = await this.catalogueService.getProduct(diagnostic.productCodes[0])
            diagnostic.firstProductName = product?.title ?? "Diagnostic Test"
            return diagnostic
        })
        const diagnostics: DiagnosticsTestOrderResponse[] = await Promise.all(diagnosticPromises)
        return this.meDiagnosticTestListPageViewBuilder.buildMeDiagnosticTestListWidgets(userContext, diagnostics, appVersion, pageFrom)
    }

    private async getSupportItemsForConsultations(userContext: UserContext, pageNumber: number, pageSize: number, appVersion: number, pageFrom: string, session: Session): Promise<SupportActionableCardWidget[]> {
        const userId = userContext.userProfile.userId

        const consultations: Consultation[] = CollectionUtil.getNullSafeList(await this.healthfaceService.getConsultations(userId, pageNumber, pageSize))
        const consultationsPromise = _.map(consultations, async consultation => {
            if (CareUtil.followUpActionRequired(consultation.consultationUserState, consultation.followUpContext)) {
                consultation.followupProducts = await this.getFollowupProducts(consultation.followUpContext.productCodes)
            }
            return consultation
        })
        const modifiedConsultations: Consultation[] = await Promise.all(consultationsPromise)
        return this.consultationListPageViewBuilder.buildConsultationListWidgets(userContext, modifiedConsultations, appVersion, session.sessionData.cityId, pageFrom)
    }

    private async getSupportItemsForPersonalTraining(userContext: UserContext, pageNumber: number, pageSize: number, appVersion: number, pageFrom: string, session: Session): Promise<SupportActionableCardWidget[]> {
        const userId = userContext.userProfile.userId
        const ptSessions: Consultation[] = CollectionUtil.getNullSafeList(await this.cultPTService.getConsultations(userId, pageNumber, pageSize))
        const ptSessionsPromise = _.map(ptSessions, async session => {
            if (CareUtil.followUpActionRequired(session.consultationUserState, session.followUpContext)) {
                session.followupProducts = await this.getFollowupProducts(session.followUpContext.productCodes)
            }
            return session
        })
        const modifiedPtSessions = await Promise.all(ptSessionsPromise)
        return this.consultationListPageViewBuilder.buildConsultationListWidgets(userContext, modifiedPtSessions, appVersion, session.sessionData.cityId, pageFrom)
    }

    public async getSupportItemsForAllVerticals(requestWidgetCountBySource: SourceWithWidgetCount[], pageSize: number, userId: string, userContext: UserContext, appVersion: number, pageFrom: string, session: Session, req: express.Request): Promise<SupportListCombinedPageView> {
        const verticals = ["class", "meal", "gear", "store", "gymClass", "diagnosticTest", "consultation", "ptSession", "sports"]
        let pageNumber: number

        if (requestWidgetCountBySource === undefined || _.isEmpty(requestWidgetCountBySource)) {
            requestWidgetCountBySource = verticals.map(vertical => ({ source: vertical, count: 0 }))
        } else {
            requestWidgetCountBySource = verticals.map(vertical => {
                return requestWidgetCountBySource.filter(x => x.source === vertical).pop() ?? { source: vertical, count: 0 }
            })
        }

        const verticalFetchTimeout = 2000

        // classes
        pageNumber = this.getPageNumberForVertical(pageSize, requestWidgetCountBySource.filter(widgetCount => widgetCount.source === verticals[0]).pop().count)
        const classBookingWidgetsPromise = promiseWithTimeout(verticalFetchTimeout, this.getSupportItemsForClasses(userContext, pageNumber, pageSize), `timeout fetching support items for classes`)

        // meals
        pageNumber = this.getPageNumberForVertical(pageSize, requestWidgetCountBySource.filter(widgetCount => widgetCount.source === verticals[1]).pop().count)
        const mealListWidgetsPromise = promiseWithTimeout(verticalFetchTimeout, this.getSupportItemsForMeals(userContext, pageNumber, pageSize), `timeout fetching support items for meals`)

        // gear
        // const gearListWidgetsPromise = promiseWithTimeout(verticalFetchTimeout, this.getSupportItemsForGear(userContext, pageNumber, pageSize), `timeout fetching support items for gear`)
        const gearListWidgetsPromise = Promise.resolve([])

        // store
        // prevCount = requestWidgetCountBySource.filter(widgetCount => widgetCount.source === verticals[3]).pop().count
        // const storeListWidgetsPromise = promiseWithTimeout(verticalFetchTimeout, this.getSupportItemsForStore(userContext, prevCount, pageSize), `timeout fetching support items for store`)
        const storeListWidgetsPromise = Promise.resolve([])

        // gymfit
        pageNumber = this.getPageNumberForVertical(pageSize, requestWidgetCountBySource.filter(widgetCount => widgetCount.source === verticals[4]).pop().count)
        const gymClassesListWidgetsPromise = promiseWithTimeout(verticalFetchTimeout, this.getSupportItemsForGymfit(userContext, pageNumber, pageSize), `timeout fetching support items for gymfit`)

        // diagnostics
        pageNumber = this.getPageNumberForVertical(pageSize, requestWidgetCountBySource.filter(widgetCount => widgetCount.source === verticals[5]).pop().count)
        const diagnosticTestListWidgetsPromise = promiseWithTimeout(3000, this.getSupportItemsForDiagnostics(userContext, pageNumber, pageSize, appVersion, pageFrom), `timeout fetching support items for diagnostics`)

        // consultations
        pageNumber = this.getPageNumberForVertical(pageSize, requestWidgetCountBySource.filter(widgetCount => widgetCount.source === verticals[6]).pop().count)
        const consultationListWidgetsPromise = promiseWithTimeout(3000, this.getSupportItemsForConsultations(userContext, pageNumber, pageSize, appVersion, pageFrom, session), `timeout fetching support items for consultations`)

        // personal trainer
        pageNumber = this.getPageNumberForVertical(pageSize, requestWidgetCountBySource.filter(widgetCount => widgetCount.source === verticals[7]).pop().count)
        const ptSessionListWidgetsPromise = promiseWithTimeout(verticalFetchTimeout, this.getSupportItemsForPersonalTraining(userContext, pageNumber, pageSize, appVersion, pageFrom, session), `timeout fetching support items for pt`)

        // Sports
        pageNumber = this.getPageNumberForVertical(pageSize, requestWidgetCountBySource.filter(widgetCount => widgetCount.source === verticals[8]).pop().count)
        const sportsSessionsWidget = promiseWithTimeout(verticalFetchTimeout, this.getSupportItemsForSportsSession(userContext, pageNumber, req), `timeout fetching support items for sports`)

        const settledPromises = await Promise.allSettled([
            classBookingWidgetsPromise, mealListWidgetsPromise, gearListWidgetsPromise, storeListWidgetsPromise, gymClassesListWidgetsPromise, diagnosticTestListWidgetsPromise,
            consultationListWidgetsPromise, ptSessionListWidgetsPromise, sportsSessionsWidget
        ])
        const allWidgetsWithSource: PageWidgetWithSource[] = []

        for (let i = 0; i < settledPromises.length; i++) {
            const settledPromise = settledPromises[i]
            if (settledPromise.status === "fulfilled") {
                const widgets = settledPromise.value ?? []
                for (const widget of widgets) {
                    allWidgetsWithSource.push({ widget, source: verticals[i] })
                }
            } else {
                this.logger.error("Error in fetching recent activities", settledPromise.reason)
                this.rollbarService.sendError(settledPromise.reason)
            }
        }

        return this.mergeAllWidgetsForSupportListPageView(allWidgetsWithSource, userContext, pageSize, requestWidgetCountBySource, verticals)
    }

    private async getSupportItemsForSportsSession(userContext: UserContext, pageNumber: number, req: express.Request): Promise<SupportActionableCardWidget[]> {
        const playSessions: PlayBaseBooking[] = await this.sportsApi.getUserBookings(userContext.userProfile.userId, 20, pageNumber)
        return this.sportListPageViewBuilder.buildSportsListWidgetsBuilder(userContext, playSessions)
    }

    private mergeAllWidgetsForSupportListPageView(allWidgetsWithSource: PageWidgetWithSource[], userContext: UserContext,
        pageSize: number, requestWidgetCountBySource: SourceWithWidgetCount[], verticals: string[]
    ): SupportListCombinedPageView {

        // Sorting on widget date
        allWidgetsWithSource.sort((a, b) => {
            return ((a.widget.timestamp ?? 0) > (b.widget.timestamp ?? 0)) ? -1 : 1
        })

        // Slicing till pageSize
        const finalAllWidgetsWithSource: PageWidgetWithSource[] = allWidgetsWithSource.slice(0, pageSize)

        // populating widget count for each vertical
        const newWidgetCountBySource: SourceWithWidgetCount[] = []
        verticals.forEach(vertical => {
            const previousWidgetCount = requestWidgetCountBySource.filter(widgetCount => widgetCount.source === vertical).pop().count
            const newWidgetCount = finalAllWidgetsWithSource.filter(widget => widget.source === vertical).length
            newWidgetCountBySource.push({ source: vertical, count: newWidgetCount + previousWidgetCount })
        })
        return { widgets: finalAllWidgetsWithSource.map(widgetWithSource => widgetWithSource.widget), widgetCountBySource: newWidgetCountBySource }
    }

    private getPageNumberForVertical(pageSize: number, prevCount: number): number {
        return Math.floor(prevCount / pageSize) + 1
    }

    public getChatWidgets(description: string, attachments: any[], isUserReply: boolean, time: Date, userContext: UserContext, hasPadding: boolean, title?: string): PageWidget[] {
        const tz = userContext.userProfile.timezone
        const widgets: PageWidget[] = []
        const descAndAttachmentsResp = this.getDescAndAttachments(description, attachments)
        if (!_.isEmpty(descAndAttachmentsResp.desc)) {
            const descriptionWidget: TicketChatWidget = {
                widgetType: "TICKET_CHAT_WIDGET",
                type: isUserReply ? "USER_CHAT" : "CF_CHAT",
                description: descAndAttachmentsResp.desc,
                time: `${TimeUtil.formatDateInTimeZone(tz, time, "D MMM, h:mm A")}`,
                title: title ? title : undefined,
                hasTopPadding: hasPadding
            }
            widgets.push(descriptionWidget)
        }
        if (!_.isEmpty(descAndAttachmentsResp.attachmentUrls)) {
            const attachmentWidget: TicketChatWidget = {
                widgetType: "TICKET_CHAT_WIDGET",
                type: isUserReply ? "USER_ATTACHMENT" : "CF_ATTACHMENT",
                urls: descAndAttachmentsResp.attachmentUrls,
                time: `${TimeUtil.formatDateInTimeZone(tz, time, "D MMM, h:mm A")}`
            }
            widgets.push(attachmentWidget)
        }
        return widgets
    }

    public getTicketResolutionWidget(ticketStatus: string, ticketId: number): PageWidget {
        let title: string = undefined
        const actions: Action[] = []

        if (ticketStatus === "RESOLVED") {
            title = "This issue has been resolved. Is there anything else that we can help you with?"
            actions.push({
                actionType: "UPDATE_TICKET_STATUS",
                title: "Yes, please",
                meta: {
                    "params": {
                        "ticketId": ticketId,
                        "overrideStatus": 2
                    }
                }
            })
            actions.push({
                actionType: "UPDATE_TICKET_STATUS",
                title: "No, thanks!",
                meta: {
                    "params": {
                        "ticketId": ticketId,
                        "overrideStatus": 4
                    }
                }
            })
        } else if (ticketStatus === "CLOSED") {
            title = "This issue has been marked as closed."
            actions.push({
                actionType: "UPDATE_TICKET_STATUS",
                title: "Reopen Issue",
                meta: {
                    "params": {
                        "ticketId": ticketId,
                        "overrideStatus": 2
                    }
                }
            })
        }

        const resolutionWidget: TicketResolutionWidget = {
            widgetType: "TICKET_RESOLUTION_WIDGET",
            title: title,
            actions: actions
        }
        return resolutionWidget
    }

    private getCSATQuestionId(userContext: UserContext) {
        let questionId = SUPPORT_CSAT_PRIMARY_QUESTION_ID
        if (userContext.sessionInfo && userContext.sessionInfo.clientVersion && userContext.sessionInfo.clientVersion >= CSAT_NEW_FLOW_APP_VERSION) {
            questionId = SUPPORT_CSAT_PRIMARY_QUESTION_ID_NEW
        }
        return questionId
    }

    public async getTicketCSATWidgetIfApplicable(ticketId: number, userContext: UserContext, isSprinklrTicket?: boolean ) {
        const csatResponses = await this.csatResponseService.getAllCsatResponses(ticketId , isSprinklrTicket ? "SPRINKLR" : "DEFAULT")
        if (!_.isEmpty(csatResponses)) {
            return undefined
        }
        const question: ICSATQuestionnaire = await this.csatResponseService.getQuestionnaireById(this.getCSATQuestionId(userContext))

        const ticketCSATWidget: TicketCSATFooterWidget = {
            widgetType: "TICKET_CSAT_FOOTER_WIDGET",
            questionId: question.questionId.toString(),
            questionTitle: question.questionTitle,
            tags: _.map(question.tags, tag => {
                const action: Action = {
                    actionType: "NAVIGATION",
                    title: tag.title,
                    url: "curefit://ticketcsat?questionId=" + question.questionId.toString() + "&ticketId=" + ticketId + "&tagId=" + tag.id.toString() + "&isSprinklrTicket=" + (isSprinklrTicket ? isSprinklrTicket.toString() : "false"),
                    unselectedImage: tag.mobileImageDefault,
                    selectedImage: tag.mobileImageSelected
                }
                return action
            })
        }
        return ticketCSATWidget
    }

    public async getTicketDetailsWidget(freshDeskTicket: IFreshdeskTicket, userId: string, userContext: UserContext) {
        const tz = userContext.userProfile.timezone
        const cardWidget = await this.getSupportTicketCardWidget(freshDeskTicket, userId, userContext, tz)
        const status = this.getTicketStatus(FreshdeskTicketsStatusMapping[freshDeskTicket.status], freshDeskTicket.custom_fields && freshDeskTicket.custom_fields.cf_merged_ticket)
        const detailsWidget: TicketDetailsWidget = {
            widgetType: "TICKET_DETAILS_WIDGET",
            title: freshDeskTicket.subject,
            status: status,
            createdOn: `${TimeUtil.formatDateInTimeZone(tz, freshDeskTicket.created_at, "ddd, D MMM, h:mm A")}`,
            ticketId: "#" + freshDeskTicket.id,
            orderDetailWidget: cardWidget,
            hasDividerBelow: false,
            footer: [{
                text: "Status",
                value: {
                    text: status.text,
                    color: status.colour
                }
            }, {
                text: "Ticket ID",
                value: {
                    text: "#" + freshDeskTicket.id,
                }
            }, {
                text: "Date Created",
                value: {
                    text: `${TimeUtil.formatDateInTimeZone(tz, freshDeskTicket.created_at, "ddd, D MMM, h:mm A")}`,
                }
            }]
        }
        return detailsWidget
    }

    public getTicketSource(userContext: UserContext): number {
        if (freshdeskDesktopSourceAgents.includes(_.get(userContext, "sessionInfo.userAgent"))) {
            return FreshdeskTicketSourceMapping.DESKTOP
        }
        return FreshdeskTicketSourceMapping.PORTAL
    }

    public getDescAndAttachments(description: string, attachments: any[]): { attachmentUrls: string[], desc: string } {
        let desc: string = description
        const attachmentUrls: string[] = []
        if (desc.match("attachment: ")) {
            const endOfDesc = desc.lastIndexOf("attachment: ")
            const startOfUrl: number = desc.lastIndexOf("https://")
            attachmentUrls.push(desc.substr(startOfUrl))
            desc = desc.substring(0, endOfDesc)
        }

        if (!_.isEmpty(attachments)) {
            _.map(attachments, attachment => {
                if (!_.isEmpty(attachment.attachment_url)) {
                    attachmentUrls.push(attachment.attachment_url)
                }
            })
        }
        return { "attachmentUrls": attachmentUrls, "desc": desc }
    }

    public async validateAttachments(description: string, attachments: any[] = []): Promise<string> {
        if (_.isEmpty(description)) {
            return description
        }
        const attachmentUrls: string[] = this.getDescAndAttachments(description, attachments).attachmentUrls
        let finalDescription: string = description
        for (const attachmentUrl of attachmentUrls) {
            finalDescription = finalDescription.replace(attachmentUrl, await this.mediaGatewayClient.validateAndGetDestinationUrl(attachmentUrl))
        }
        return finalDescription
    }

    public async getSupportCardItem(userContext: UserContext, ticket: Ticket): Promise<SupportCardItem> {
        const tz = userContext.userProfile.timezone
        const orderId = ticket.orderId
        const shipmentId = ticket.shipmentId
        const isWeb = AppUtil.isWeb(userContext)
        const emailTicketPath = "/image/icons/support/email_ticket.png"
        let imageURL = undefined
        let subTitle = undefined

        if (_.isEmpty(orderId) && (_.isEmpty(shipmentId) || shipmentId.toLowerCase() === "na")) {
            imageURL = isWeb ? emailTicketPath : CdnUtil.getCdnUrl("curefit-content".concat(emailTicketPath))
            subTitle = undefined
        } else if (_.isEmpty(orderId)) {
            try {
                const cultBooking: CultBookingResponse = await this.cultFitService.getBookingV2(shipmentId, userContext.userProfile.userId)
                const booking: CultBooking = (cultBooking.booking) ? cultBooking.booking : cultBooking.waitlist
                const document = _.find(booking.Class.Workout.documents, document => {
                    return document.tagID === 11
                })
                imageURL = document ? "/" + document.URL : undefined
                subTitle = booking.Class.Workout.name
            } catch (e) {
                this.logger.error(`Error while fetching cult booking`, e)
            }
        } else {
            const order = await this.omsApiClient.getOrder(orderId)
            if (!_.isEmpty(order)) {
                const orderProductDetail = await this.getOrderDetailsFromOrderIdOrNull(orderId, userContext)
                imageURL = this.getImageUrlForTicket(userContext, order, orderProductDetail)
                subTitle = (!_.isEmpty(orderProductDetail.itemDetails) && orderProductDetail.itemDetails.length > 0) ?
                    orderProductDetail.itemDetails[0].title + " + " + (orderProductDetail.itemDetails.length - 1) + " items ordered" : orderProductDetail.title
            }
        }

        return {
            title: ticket.subject,
            footer: [{
                text: `${TimeUtil.formatDateStringInTimeZone(ticket.createdOn, tz, "ddd, D MMM, h:mm A")}`
            }],
            status: this.getTicketStatus(ticket.status, ticket.merged),
            cardAction: {
                actionType: "NAVIGATION",
                url: "curefit://ticketdetails?ticketId=" + ticket.id
            },
            subTitle: subTitle,
            imageUrl: imageURL,
            ticketId: ticket.id
        }
    }

    public async getOrderDetailsFromOrderIdOrNull(orderId: string, userContext: UserContext): Promise<OrderProductDetail> {
        if (!_.isEmpty(orderId)) {
            let order: Order
            try {
                order = await this.omsApiClient.getOrder(orderId)
            }
            catch (err) {
                this.logger.error("Error while fetching order from CRM", {orderId, err})
            }
            if (_.isEmpty(order)) {
                return null
            }
            const userAgent = userContext.sessionInfo.userAgent
            return await this.orderProductDetailBuilder.getOrderProductDetails(userContext, order)
        }
        return null
    }

    public getActionableCardForTicket(ticket: Ticket, tz: Timezone): SupportActionableCardWidget {
        const status = this.getTicketStatus(ticket.status, ticket.merged)
        return {
            widgetType: "SUPPORT_ACTIONABLE_CARD_WIDGET",
            title: ticket.subject,
            titleStyling: (status.text === "RESOLVED") ? { color: "rgba(0,0,0, 0.5)" } : undefined,
            footer: [{
                text: "#" + ticket.id,
                backgroundColor: "#F3F3F3"
            }],
            subTitle: undefined,
            cardAction: {
                actionType: "NAVIGATION",
                url: "curefit://ticketdetails?ticketId=" + ticket.id + "&isSprinklrTicket=false"
            },
            status: status,
            isNotCard: true,
            hasDividerBelow: false,
            time: `${TimeUtil.formatDateStringInTimeZone(ticket.updatedAt, tz, "ddd, D MMM, h:mm A")}`,
            ticketId: ticket.id,
            timestamp: new Date(ticket.updatedAt).getTime()
        }
    }

    public getTicketStatus(status: string, isMerged: boolean): Status {
        const closedStatus = { text: "CLOSED", colour: "#000000" }
        const resolvedStatus = { text: "RESOLVED", colour: "#60636b" }
        const openStatus = { text: "OPEN", colour: "#57AC49" }
        const mergedStatus = { text: "DUPLICATE", colour: "#898b91" }
        if (isMerged) {
            return mergedStatus
        }
        if (status === "Resolved") {
            return closedStatus
        } else if (status === "Closed") {
            return resolvedStatus
        }
        return openStatus
    }

    public getTicketStatusV2(status: string, isMerged: boolean): string {
        if (isMerged) {
            return "Duplicate"
        }
        if (status === "Resolved") {
            return "Closed"
        } else if (status === "Closed") {
            return "Resolved"
        }
        return "Open"
    }

    public async getSupportSections(city: City, userContext: UserContext): Promise<any[]> {
        const isSupportOrdersViewEnabled = AppUtil.isSupportRecentOrdersViewEnabled(userContext)
        const items = []
        const isCultSportWebApp = AppUtil.isCultSportWebApp(userContext)
        const isInternationalApp = AppUtil.isInternationalApp(userContext)
        if (isInternationalApp) {
            items.push(
                {
                    title: "Online classes",
                    action: "curefit://supportlistpage?pageId=ptSessions&title=Online+classes",
                    style: isSupportOrdersViewEnabled ? { paddingHorizontal: 20 } : undefined
                })
        }

        if (!isInternationalApp && AppUtil.isSupportBookingsScreenSupported(userContext)) {
            items.push({
                title: "My activities",
                action: `curefit://support?pageId=bookings&title=My+activities`,
                style: isSupportOrdersViewEnabled ? { paddingHorizontal: 20 } : undefined
            })
        }
        if (!isInternationalApp) {
            items.push({
                title: "Orders and purchases",
                action: `curefit://order?pageFrom=support&title=Orders+and+purchases`,
                style: isSupportOrdersViewEnabled ? { paddingHorizontal: 20 } : undefined
            })
        }
        if (!isInternationalApp && !isCultSportWebApp) {
            items.push({
                title: "Memberships and subscriptions",
                action: `curefit://activepacks?title=Memberships+and+subscriptions`,
                style: isSupportOrdersViewEnabled ? { paddingHorizontal: 20 } : undefined
            })
        }

        if (!isCultSportWebApp) {
            items.push({
                title: "Other enquiries",
                action: ActionUtil.getIssuesUrl(),
                style: isSupportOrdersViewEnabled ? { paddingHorizontal: 20 } : undefined
            })
        }
        return items
    }

    public getPreSubmissionAction(issueId: string, comment: string, requestBody: any, queryParams: any) {
        // as no show policy is changes we do not need this flow for now
        // const preSubmissionFlowTypes: PreSubmissionFlowType[] = this.getPreSubmissionFlowTypesForIssue(issueId)
        // let finalPreSubmissionFlowType: PreSubmissionFlowType
        // preSubmissionFlowTypes.some(type => {
        //     if (this.checkIfKeywordPresent(comment.toLowerCase(), type)) {
        //         finalPreSubmissionFlowType = type
        //         return true
        //     }
        //     return false
        // })
        // if (finalPreSubmissionFlowType) {
        //     return this.getActionForPreSubmissionFlowType(finalPreSubmissionFlowType, requestBody, queryParams)
        // }
        return {
            actionType: "EMPTY_ACTION"
        }
    }

    async getFAQs(userContext: UserContext, city: City): Promise<any[]> {
        const { sessionInfo } = userContext
        const isWeb = AppUtil.isWeb(userContext)
        if (AppUtil.isCultSportWebApp(userContext)) {
            return this.getCultSportFAQ(isWeb)
        }
        const isTataNeuWebApp = AppUtil.isTataNeuWebApp(userContext)
        const defaultFaq = {
            title: "My Account",
            action: "curefit://webview?uri=http://support.cure.fit/support/solutions/***********&title=My Account"
        }
        if (_.isEmpty(city)) {
            return [defaultFaq]
        }
        const supportedVerticals: VerticalType[] = await this.navBarBusiness.getSupportedVerticals(userContext, city)
        const faqCityCache: Map<string, FAQMeta> = this.supportFAQConfigCache.getFAQConfigForCity(city.cityId, AppUtil.getTenantFromUserContext(userContext))
        if (_.isEmpty(faqCityCache)) {
            return [defaultFaq]
        }
        const items = []
        const isFitnessAvailableCity = city.availableOfferings.includes("FITNESS"),
            isGymFitAvailableCity = city.availableOfferings.includes("GYMFIT_FITNESS_PRODUCT"),
            isStoreSupported = supportedVerticals.includes("STORE")

        if (AppUtil.isCultSportWebApp(userContext)) {
            items.push(this.getFAQFormat(faqCityCache.get("store"), isWeb))
            items.push(this.getFAQFormat(faqCityCache.get("account"), isWeb))
        } else if (isTataNeuWebApp) {
            isFitnessAvailableCity && items.push(this.getFAQFormat(faqCityCache.get("cult"), isWeb, true))
            isGymFitAvailableCity && items.push(this.getFAQFormat(faqCityCache.get("gyms"), isWeb, true))
            isStoreSupported && items.push(this.getFAQFormat(faqCityCache.get("store"), isWeb, true))

            items.push(this.getFAQFormat(faqCityCache.get("account"), isWeb, true))
        } else if (!AppUtil.isInternationalApp(userContext)) {
            if (isFitnessAvailableCity) {
                items.push(this.getFAQFormat(faqCityCache.get("cult"), isWeb))
            }
            if (isGymFitAvailableCity) {
                items.push(this.getFAQFormat(faqCityCache.get("gyms"), isWeb))
            }
            // TODO: Nisheet to fix it properly for wellness
            if (supportedVerticals.includes("CARE")) {
                items.push(this.getFAQFormat(faqCityCache.get("care"), isWeb))
            }
            if (supportedVerticals.includes("EAT")) {
                items.push(this.getFAQFormat(faqCityCache.get("eat"), isWeb))
            }
            if (isStoreSupported) {
                items.push(this.getFAQFormat(faqCityCache.get("store"), isWeb))
            } else {
                items.push(this.getFAQFormat(faqCityCache.get("gear"), isWeb))
                if (supportedVerticals.includes("WHOLE")) {
                    items.push(this.getFAQFormat(faqCityCache.get("whole"), isWeb))
                }
            }
            items.push(this.getFAQFormat(faqCityCache.get("live"), isWeb))
            items.push(this.getFAQFormat(faqCityCache.get("account"), isWeb))
        } else {
            items.push(this.getFAQFormat(faqCityCache.get("about"), isWeb))
            items.push(this.getFAQFormat(faqCityCache.get("live"), isWeb))
            items.push(this.getFAQFormat(faqCityCache.get("diy"), isWeb))
            items.push(this.getFAQFormat(faqCityCache.get("workout"), isWeb))
            items.push(this.getFAQFormat(faqCityCache.get("subscription"), isWeb))
            items.push(this.getFAQFormat(faqCityCache.get("connectedDevices"), isWeb))
        }
        return items
    }

    public async buildSupportCSATPageView(freshDeskTicket: IFreshdeskTicket, questionId: string, userId: string, sprinklrTicket?: SprinklrCase ): Promise<SupportCSATPageView> {
        const primaryQuestion: ICSATQuestionnaire = await this.csatResponseService.getQuestionnaireById(parseInt(questionId))
        const followupQuestionResponse: ITagResponse[] = await this.csatResponseService.getFollowUpQuestions(parseInt(questionId))
        const followupQuestions: { [id: string]: SupportCSATQuestionAnswer[] } = {}
        if (!_.isEmpty(followupQuestionResponse)) {
            for (const followupQuestion of followupQuestionResponse) {
                followupQuestions[followupQuestion.tag.id] = _.map(followupQuestion.questions, question => this.getSupportCSATQuestionAnswer(question))
            }
        }
        // show agent L2 tag as a subject in case of a sprinklr case
        const sprinklrTitle = sprinklrTicket?.workflow?.customProperties?.["_c_673eda4a773423325c100d1f"]?.[0] ?? undefined
        const ticketTitle: string = sprinklrTicket ? sprinklrTitle : freshDeskTicket.subject
        const ticketStatus: string = sprinklrTicket ? this.getSprinklrTicketStatus(sprinklrTicket.status).text : this.getTicketStatus(FreshdeskTicketsStatusMapping[freshDeskTicket.status], freshDeskTicket.custom_fields && freshDeskTicket.custom_fields.cf_merged_ticket).text
        return new SupportCSATPageView(ticketTitle, ticketStatus, this.getSupportCSATQuestionAnswer(primaryQuestion), followupQuestions)
    }

    public getSprinklrTicketStatus(status: string): Status {
        switch (status) {
            case "Hard Closed":
                return { text: "CLOSED", colour: "#000000" }
            case "Resolved":
            case "Auto-Closed":
            case "Closed":
            case "Auto Closed":
                return { text: "RESOLVED", colour: "#60636b" }
            case null:
            default:
                return { text: "OPEN", colour: "#57AC49" }
        }
    }

    public async postCSATResponse(ticketId: number, response: ICSATQuestionnaireResponse[], isSprinklrTicket: boolean): Promise<boolean> {
        const request: ICSATQuestionnaireResponseRequest = {
            csatResponse: response
        }
        return this.csatResponseService.postCSATResponse(ticketId, request, isSprinklrTicket ? "SPRINKLR" : "DEFAULT")
    }

    private getSupportCSATQuestionAnswer(questionnaire: ICSATQuestionnaire): SupportCSATQuestionAnswer {
        const question: SupportCSATQuestion = {
            questionId: questionnaire.questionId.toString(),
            questionTitle: questionnaire.questionTitle,
            questionType: questionnaire.questionType
        }
        let answers: SupportCSATAnswer[] = undefined
        if (!_.isEmpty(questionnaire.tags)) {
            answers = []
            for (const tag of questionnaire.tags) {
                answers.push({
                    tagId: tag.id.toString(),
                    tagTitle: tag.title,
                    unselectedImage: tag.mobileImageDefault,
                    selectedImage: tag.mobileImageSelected,
                })
            }
        }
        const questionAnswer: SupportCSATQuestionAnswer = {
            question: question,
            answers: answers
        }
        return questionAnswer
    }

    // ----------------------------------------------------------Private Methods---------------------------------------------------------

    private getFAQFormat(faqMeta: FAQMeta, isWeb: boolean, isTataNeuWebApp: boolean = false): any {
        if (isWeb) {
            return !isTataNeuWebApp ? {
                title: faqMeta.title,
                action: faqMeta.url
            } : {
                title: faqMeta.title,
                action: {
                    actionType: "NAVIGATE_TO_IFRAME_PAGE",
                    title: faqMeta.title,
                    url: faqMeta.url
                }
            }
        }
        return {
                title: faqMeta.title,
                action: "curefit://webview?uri=" + faqMeta.url + "&title=" + faqMeta.title
            }
    }

    private async getFollowupProducts(productCodes: string[]): Promise<ConsultationProduct[]> {
        const productPromises = _.map(productCodes, async productCode => {
            return <ConsultationProduct>await this.catalogueService.getProduct(productCode)
        })
        return await Promise.all(productPromises)
    }

    private async getSupportTicketCardWidget(freshDeskTicket: IFreshdeskTicket, userId: string, userContext: UserContext, tz: Timezone): Promise<SupportActionableCardWidget> {
        const orderId: string = freshDeskTicket.custom_fields.order_id
        let cardWidget: SupportActionableCardWidget = undefined
        if (_.isEmpty(orderId)) {
            const shipmentId = freshDeskTicket.custom_fields.shipmentid
            if (!_.isEmpty(shipmentId) && shipmentId.toLowerCase() !== "na") {
                try {
                    const cultBooking: CultBookingResponse = await this.cultFitService.getBookingV2(shipmentId, userId)
                    const booking: CultBooking = (cultBooking.booking) ? cultBooking.booking : cultBooking.waitlist
                    cardWidget = await this.classListPageViewBuilder.getActionableCardWidget(booking, userContext)
                    cardWidget.cardAction = undefined
                } catch (e) {
                    this.logger.error("Error while fetching cult booking", e)
                }
            }
        } else {
            const order: BaseOrder = await this.omsApiClient.getOrder(orderId)
            if (!_.isEmpty(order)) {
                const subTitle = await this.getSubTitle(orderId, order)
                const orderProductDetail: OrderProductDetail = await this.getOrderDetailsFromOrderIdOrNull(orderId, userContext)
                const title = (!_.isEmpty(orderProductDetail.itemDetails) && orderProductDetail.itemDetails.length > 0) ?
                    orderProductDetail.itemDetails[0].title + " + " + (orderProductDetail.itemDetails.length - 1) + " items ordered" : orderProductDetail.title
                const imageUrl = this.getImageUrlForTicket(userContext, order, orderProductDetail)
                cardWidget = {
                    widgetType: "SUPPORT_ACTIONABLE_CARD_WIDGET",
                    title: title,
                    imageUrl: imageUrl,
                    subTitle: subTitle,
                    footer: [{
                        text: `${TimeUtil.formatDateInTimeZone(tz, new Date(order.createdDate), "ddd, D MMM")}`,
                    }],
                    cardAction: undefined
                }
            }
        }
        return cardWidget
    }

    private getImageUrlForTicket(userContext: UserContext, order: BaseOrder, orderProductDetail: OrderProductDetail) {
        const isWeb = AppUtil.isWeb(userContext)
        const emailTicketPath = "/image/icons/support/email_ticket.png"
        const diagnosticTextPath = "/image/icons/support/email_ticket.png"

        let imageUrl: string = isWeb ? emailTicketPath : CdnUtil.getCdnUrl("curefit-content".concat(emailTicketPath))

        if (order.productSnapshots[0].productType === "DIAGNOSTICS") {
            imageUrl = isWeb ? diagnosticTextPath : CdnUtil.getCdnUrl("curefit-content".concat(diagnosticTextPath))
        }
        if (orderProductDetail.thumbnailImages && !_.isEmpty(orderProductDetail.thumbnailImages[0])) {
            imageUrl = orderProductDetail.thumbnailImages[0]
        }
        return imageUrl
    }

    private async getSubTitle(orderId: string, order: BaseOrder) {
        let subTitle = "#" + orderId
        if (order.productSnapshots[0].productType === "CONSULTATION") {
            try {
                const doctorId = order.products[0].option.doctorId
                const doctor = await this.healthfaceService.getDoctorDetails(doctorId)
                subTitle = doctor.name
            } catch (e) {
                // eat
            }
        }
        return subTitle
    }

    private async getCultSportFAQ(isWeb: boolean) {
        return [
            {
                title: "store",
                action: "http://support.cult.fit/support/solutions/***********"
            },
            {
                title: "My Account",
                action: "http://support.cult.fit/support/solutions/***********"
            }
        ]
    }

    private getPreSubmissionFlowTypesForIssue(issueId: string): PreSubmissionFlowType[] {
        if (AppUtil.isProdLike) {
            switch (issueId) {
                case "1844":
                case "1828":
                case "1831":
                case "1848":
                case "1835":
                case "1849":
                case "1870":
                case "1809":
                case "1822":
                case "1692":
                case "1841":
                case "1874":
                case "1219":
                case "1815":
                case "1811":
                case "1218":
                case "1803":
                case "1810":
                case "1818":
                case "1392":
                case "1693":
                case "1406":
                case "1399":
                case "117":
                case "1550":
                case "1770":
                case "1548":
                case "1402":
                case "1396":
                case "1761":
                case "1544":
                case "1539":
                case "1547":
                case "1541":
                case "435":
                case "1766":
                case "1395":
                    return ["NO_SHOW_EXCEPTION"] // arrange flow types in decreasing order of priority, the first match is the type that will be used
                default:
                    return []
            }
        } else {
            switch (issueId) {
                case "108":
                    return ["NO_SHOW_EXCEPTION"]
                default:
                    return []
            }
        }

    }

    private checkIfKeywordPresent(comment: string, type: PreSubmissionFlowType): boolean {
        const keywords: string[] = this.getKeywordsForPreSubmissionFlowType(type)
        let isKeywordPresent: boolean = false
        keywords.some(keyword => {
            if (comment.indexOf(keyword) != -1) {
                isKeywordPresent = true
                return true
            }
            return false
        })
        return isKeywordPresent
    }

    private getKeywordsForPreSubmissionFlowType(type: PreSubmissionFlowType): string[] {
        switch (type) {
            case "NO_SHOW_EXCEPTION":
                return ["cancel", "due", "day", "cancelled", "raining", "membership", "today", "booked", "attend", "cult", "session", "able", "time", "classes", "centre", "unable", "weather", "last", "one", "drop", "bad", "rain", "dropout", "cancellation", "center", "deducted", "request", "minute", "book", "morning", "heavily", "dropped", "reach", "team", "add", "deduct", "hrx", "window"]
            default:
                return []
        }
    }

    private getActionForPreSubmissionFlowType(preSubmissionFlowType: PreSubmissionFlowType, requestBody: any, queryParams: any) {
        switch (preSubmissionFlowType) {
            case "NO_SHOW_EXCEPTION":
                return {
                    actionType: "SHOW_SUPPORT_OPTIONS_MODAL",
                    title: "Is this regarding a no-show penalty?",
                    modalType: "NO_SHOW_KEYWORD_CHECK",
                    payload: {
                        requestBody,
                        queryParams,
                    },
                }
        }
    }


}

export default CRMBusiness
