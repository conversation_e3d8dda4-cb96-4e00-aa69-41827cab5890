import { inject, injectable } from "inversify"
import * as _ from "lodash"
import { IssueDetail, IssueProductState, IssueProductType } from "@curefit/issue-common"
import { BASE_TYPES, ILogger, PromUtil } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { UserContext } from "@curefit/userinfo-common"
import { REPORT_ISSUES_CLIENT_TYPES } from "@curefit/report-issues-client"
import { Tenant } from "@curefit/base-common"
import { IssueDetailsCacheHelper } from "@curefit/report-issues-client"


export interface IIssueService {
    getIssuesByProductTypeAndState(productType: IssueProductType, state: IssueProductState, userContext: UserContext, tenant?: Tenant): Promise<IssueDetail[]>

    getIssue(issueId: string): Promise<IssueDetail>

    getFilteredIssueDetails(issueDetails: IssueDetail[], userContext: UserContext): IssueDetail[]
}

@injectable()
class IssueService implements IIssueService {

    constructor(
        @inject(REPORT_ISSUES_CLIENT_TYPES.IssueDetailsCacheHelper) private issueDetailsCacheHelper: IssueDetailsCacheHelper,
        @inject(BASE_TYPES.PromUtil) private promUtil: PromUtil,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
    ) {
    }

    getFilteredIssueDetails(issueDetails: IssueDetail[], userContext: UserContext): IssueDetail[] {
        const orderSource = userContext.sessionInfo.orderSource
        return _.filter(issueDetails, issueDetail => {
            if (_.isEmpty(issueDetail)) {
                return false
            }
            if (!_.isEmpty(issueDetail.excludeSources)) {
                const isSourceExcluded = issueDetail.excludeSources.some(source => {
                    if (orderSource === source)
                        return true
                })
                return !isSourceExcluded
            }
            if (!_.isEmpty(issueDetail.includeSources)) {
                return issueDetail.includeSources.some(source => {
                    if (orderSource === source)
                        return true
                })
            }
            return true
        })
    }

    async getIssuesByProductTypeAndState(productType: IssueProductType, state: IssueProductState, userContext: UserContext, tenant?: Tenant): Promise<IssueDetail[]> {
        tenant = tenant || Tenant.CUREFIT_APP
        const issueDetails: IssueDetail[] = (await this.issueDetailsCacheHelper.getIssuesByProductTypeStateName(productType, state as string, tenant)) || []
        return this.getFilteredIssueDetails(issueDetails, userContext)
    }

    async getIssue(issueId: string): Promise<IssueDetail> {
        return this.issueDetailsCacheHelper.getIssueById(Number(issueId))
    }

}

export default IssueService
