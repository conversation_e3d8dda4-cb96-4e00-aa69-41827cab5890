import { ProductDetailPage, ProductListWidget } from "../common/views/WidgetView"
import { City } from "@curefit/location-common"
import { HorizontalActionableCardListingWidget, SupportCardItem } from "../page/PageWidgets"
import * as _ from "lodash"
import ActionUtil from "../util/ActionUtil"
import { Constants } from "@curefit/base-utils"
import { UserContext } from "@curefit/userinfo-common"
import { Tenant } from "@curefit/user-common"
import AppUtil from "../util/AppUtil"

const POLICY_URL_MAP: any = {
    [Tenant.CUREFIT_APP]: {
        privacy: "https://static.cult.fit/privacy_cult.html",
        terms: "https://static.cult.fit/terms_cult.html",
        foss: "https://static.cult.fit/foss.html"
    },
    [Tenant.LIVEFIT_APP]: {
        privacy: "http://static.cure.fit/privacyInternational.html",
        terms: "http://static.cure.fit/termsInternational.html",
        foss: "http://static.cure.fit/fossInternational.html"
    },
}


export class SupportWebView extends ProductDetailPage {
    constructor(userContext: UserContext, faqs: any[], supportSections: any[], numTickets: number, openTicketsView: SupportCardItem[]) {
        super()
        // this.setOpenTicketsWidget(userContext, numTickets, openTicketsView)
        // this.setSupportSectionsWidget(supportSections)
        this.setFAQWidgets(userContext, faqs)
        this.setPolicyWidgets(userContext)
        this.setContactUsWidgets()
    }

    setOpenTicketsWidget(userContext: UserContext, numTickets: number, openTicketsView: SupportCardItem[]) {
        if (AppUtil.isInternationalApp(userContext)) {
            return
        }
        if (AppUtil.isCultSportWebApp(userContext) && openTicketsView?.length === 0) {
            return
        }
        if (numTickets === 0) {
            return
        }

        const openTicketsWidget: HorizontalActionableCardListingWidget = {
            widgetType: "HORIZONTAL_ACTIONAL_CARD_LISTING_WIDGET",
            title: openTicketsView.length > 0 ? "Open Issues" : "No Open Issues",
            hasDividerBelow: false,
            type: "OPEN_SUPPORT_TICKETS",
            cardItems: openTicketsView,
            header: numTickets > 0  && !AppUtil.isCultSportWebApp(userContext) ? {
                action: {
                    actionType: "NAVIGATION",
                    url: "curefit://alltickets",
                    meta: {title: "All Queries"}
                }
            } : undefined
        }
        this.widgets.push(openTicketsWidget)
    }

    setSupportSectionsWidget(supportSections: any[]) {
        if (_.isEmpty(supportSections)) {
            return
        }
        const reportIssueWidget: ProductListWidget = {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "ACTION_CELL_ROW",
            hideSepratorLines: false,
            backgroundColor: "white",
            header: {
                title: "Need Help",
                color: "#00000000"
            },
            items: supportSections
        }
        this.widgets.push(reportIssueWidget)
    }

    setFAQWidgets(userContext: UserContext, faqs: any[]) {
        const isTataNeuWebApp = AppUtil.isTataNeuWebApp(userContext)
        const faqWidget: ProductListWidget = {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: isTataNeuWebApp ? "ACTION_CELL_ROW" : "ACTION_ROW",
            hideSepratorLines: false,
            header: {
                title: "FAQs"
            },
            items: faqs
        }

        this.widgets.push(faqWidget)
    }

    setPolicyWidgets(userContext: UserContext) {
        const tenant: Tenant = AppUtil.getTenantFromUserContext(userContext)
        const isTataNeuWebApp = AppUtil.isTataNeuWebApp(userContext)
        const policyUrls = POLICY_URL_MAP[tenant] ? POLICY_URL_MAP[tenant] : POLICY_URL_MAP[Tenant.CUREFIT_APP]
        const policyWidget: ProductListWidget = {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: isTataNeuWebApp ? "ACTION_CELL_ROW" : "ACTION_ROW",
            hideSepratorLines: false,
            header: {
                title: "T&C and Privacy Policy"
            },
            items: [
                {
                    title: "Privacy Policy",
                    action: isTataNeuWebApp ? {
                        actionType: "NAVIGATE_TO_IFRAME_PAGE",
                        url: policyUrls.privacy,
                        title: "Privacy Policy"
                    } : policyUrls.privacy
                },
                {
                    title: "Terms and Conditions",
                    action: isTataNeuWebApp ? {
                        actionType: "NAVIGATE_TO_IFRAME_PAGE",
                        url: policyUrls.terms,
                        title: "Terms and Conditions"
                    } : policyUrls.terms
                },
                {
                    title: "Foss",
                    action: isTataNeuWebApp ? {
                        actionType: "NAVIGATE_TO_IFRAME_PAGE",
                        url: policyUrls.foss,
                        title: "Foss"
                    } : policyUrls.foss
                }
            ]
        }

        this.widgets.push(policyWidget)
    }

    setContactUsWidgets() {
        const contactUsWidget: ProductListWidget = {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "ACTION_DETAIL_ROW",
            hideSepratorLines: false,
            header: {
                title: "Contact Us"
            },
            items: [
                {
                    title: "For any further queries, write to us at",
                    subTitle: Constants.customerCareMail,
                    cardAction: {
                        actionType: "EXTERNAL_DEEP_LINK",
                        url: `mailto:${Constants.customerCareMail}`
                    }
                }
            ]
        }

        this.widgets.push(contactUsWidget)
    }
}
