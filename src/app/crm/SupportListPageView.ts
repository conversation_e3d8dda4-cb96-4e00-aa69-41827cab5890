import { Action, ProductDetailPage, WidgetView } from "../common/views/WidgetView"
import { PageWidget } from "../page/Page"
import { BaseWidget, IBaseWidget } from "@curefit/vm-models"
import { TemplateWidget } from "@curefit/vm-common"
import { TrialCardWidget } from "../digital/TrialCardWidget"

export class SupportListPageView extends ProductDetailPage {

    pageNumber: number
    pageSize: number = 20
    totalHits?: number
    footerWidget?: WidgetView

    constructor(widgets: PageWidget[], actions: Action[], footerWidget?: WidgetView) {
        super()
        this.widgets = widgets
        this.actions = actions
        this.footerWidget = footerWidget
    }
}

export class SupportListCombinedPageView {
    widgets: (BaseWidget | WidgetView | PageWidget | TemplateWidget | IBaseWidget | TrialCardWidget)[] = []
    widgetCountBySource: SourceWithWidgetCount[] = []
}

export class SourceWithWidgetCount {
    source: string
    count: number
}