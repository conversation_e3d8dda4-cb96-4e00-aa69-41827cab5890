import { inject, injectable } from "inversify"
import { CONFIG_STORE_CLIENT_TYPES, ConfigurationEntry, IConfigStoreService } from "@curefit/config-store-client"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { InMemoryCacheService } from "@curefit/memory-cache"

export interface IAppConfigStoreService {
    getConfig(key: string, defaultValue: any): any
}

@injectable()
export class AppConfigStoreService extends InMemoryCacheService<Map<String, any>> implements IAppConfigStoreService {
    constructor(@inject(CONFIG_STORE_CLIENT_TYPES.ConfigStoreService) private configStore: IConfigStoreService,
                @inject(BASE_TYPES.ILogger) protected logger: ILogger,
                @inject(BASE_TYPES.AppName) private appName: string
    ) {
        super(logger, 30)
        this.load("AppConfigCache")
    }

    public getConfig(key: string, defaultValue: any): any {
        if (typeof(this.cache) !== "undefined" && this.cache.has(key)) {
            return this.cache.get(key)
        }
        return defaultValue
    }

    protected async loadData(): Promise<Map<String, any>> {
        const map = new Map<String, any>()
        const config: ConfigurationEntry[] = await this.configStore.getAppConfigs(this.appName)
        config.forEach((conf) => map.set(conf.key, conf.value))
        return map
    }
}