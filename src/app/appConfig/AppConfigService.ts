import { APP_CONFIG_TYPES, AppConfigService } from "@curefit/app-config-client"
import { UserContext } from "@curefit/vm-models"
import * as _ from "lodash"
import { EvaluatorContext } from "@curefit/app-config-common"
import kernel from "../../config/ioc/ioc"
import AppUtil from "../util/AppUtil"

export class AppConfigUtil {
    private static appConfigService: AppConfigService

    // AppConfigService is initialized after creation of bindings.
    public static _initialize(
    ) {
        if (_.isNil(AppConfigUtil.appConfigService)) {
            AppConfigUtil.appConfigService = kernel.get<AppConfigService>(APP_CONFIG_TYPES.AppConfigService)
        }
    }

    private static createEvaluatorContext(userContext: UserContext): EvaluatorContext {
        return {
            CLIENT_VERSION: _.get(userContext, "sessionInfo.clientVersion"),
            CODEPUSH_VERSION: _.get(userContext, "sessionInfo.cpVersion"),
            OSNAME_TYPES: _.get(userContext, "sessionInfo.osName") ? _.get(userContext, "sessionInfo.osName").toLowerCase() : undefined,
            SOURCES: _.get(userContext, "sessionInfo.orderSource") ? _.get(userContext, "sessionInfo.orderSource") : undefined,
            USER_AGENTS: _.get(userContext, "sessionInfo.userAgent") ? _.get(userContext, "sessionInfo.userAgent") : undefined
        }
    }

    public static evaluateBoolean(configId: string, userContext: UserContext): boolean {
        const context: EvaluatorContext = this.createEvaluatorContext(userContext)
        const result: Boolean = AppConfigUtil.appConfigService.getBooleanValueFromConfig(configId, AppUtil.getTenantFromUserContext(userContext), context)
        return _.isNil(result) ? false : result.valueOf()
    }

    public static evaluateNumber(configId: string, userContext: UserContext): number {
        const context: EvaluatorContext = this.createEvaluatorContext(userContext)
        const result: Number = AppConfigUtil.appConfigService.getNumberValueFromConfig(configId, AppUtil.getTenantFromUserContext(userContext), context)
        return _.isNil(result) ? undefined : result.valueOf()
    }

    public static evaluateString(configId: string, userContext: UserContext): string {
        const context: EvaluatorContext = this.createEvaluatorContext(userContext)
        const result: String = AppConfigUtil.appConfigService.getStringValueFromConfig(configId, AppUtil.getTenantFromUserContext(userContext), context)
        return _.isNil(result) ? undefined : result.valueOf()
    }

    public static evaluateObject(configId: string, userContext: UserContext): Object {
        const context: EvaluatorContext = this.createEvaluatorContext(userContext)
        const result: Object = AppConfigUtil.appConfigService.getObjectValueFromConfig(configId, AppUtil.getTenantFromUserContext(userContext), context)
        return _.isNil(result) ? undefined : result.valueOf()
    }
}
