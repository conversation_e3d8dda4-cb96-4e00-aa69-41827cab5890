import { inject, injectable } from "inversify"
import * as express from "express"
import { <PERSON>rrorC<PERSON> } from "../error/ErrorCodes"
import { ErrorFactory, HTTP_CODE } from "@curefit/error-client"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { IUserSegmentClient, SEGMENTATION_CLIENT_TYPES } from "@curefit/segmentation-service-client"
import { TataNeuUtil } from "../util/TataNeuUtil"
import { BASE_UTILS_TYPES, IApiKeyService } from "@curefit/base-utils"
import { BASE_TYPES, Logger } from "@curefit/base"

const API_KEY_HEADER = "x-api-key"

@injectable()
export class TataNeuAuthMiddleware {
    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        @inject(SEGMENTATION_CLIENT_TYPES.UserSegmentClient) private segmentationClient: IUserSegmentClient,
        @inject(BASE_UTILS_TYPES.ApiKeyService) private apiKeyService: IApiKeyService
    ) {
        this.authenticate = this.authenticate.bind(this)
    }

    public async authenticate(req: express.Request, res: express.Response, next: express.NextFunction) {
        try {
            const apiKey: string = req.headers[API_KEY_HEADER] as string
            if (!this.apiKeyService.isValid(apiKey) || this.apiKeyService.getApiKey(apiKey).partner !== "TATA_NEU") {
                throw this.errorFactory.withCode(ErrorCodes.UNAUTHORIZED_ERR, HTTP_CODE.UNAUTHORIZED)
                    .withMessage("Invalid api key")
                    .build()
            }
            next()
        } catch (err) {
            next(err)
        }
    }
}
