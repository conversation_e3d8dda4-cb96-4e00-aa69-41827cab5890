import { Container, inject } from "inversify"
import { controller, httpPost } from "inversify-express-utils"
import * as express from "express"
import { Lo<PERSON>, BASE_TYPES } from "@curefit/base"
import { ErrorFactory } from "@curefit/error-client"
import { IApiKeyService, BASE_UTILS_TYPES } from "@curefit/base-utils"
import * as _ from "lodash"
import {
    GearWebhookData,
} from "@curefit/oms-api-client"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { ErrorCodes } from "../error/ErrorCodes"
import { OMS_API_CLIENT_TYPES, IOrderService } from "@curefit/oms-api-client"

function controllerFactory(kernel: Container) {
    @controller("/gearwebhooks")
    class GearWebhooksController {
        constructor(
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(BASE_UTILS_TYPES.ApiKeyService) private apiKeyService: IApiKeyService,
            @inject(OMS_API_CLIENT_TYPES.OrderService) private omsApiClient: IOrderService,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        ) { }

        @httpPost("/order-status-change/:orderId")
        async orderStatusChange(req: express.Request, res: express.Response) {
            const apiKey = req.headers["apikey"] as string

            if (!this.apiKeyService.hasPartnerKeyWithActionPermission("CULTGEAR_INBOUND", apiKey, "PUSH_ORDER_STATUS_CHANGE")) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid api key").build()
            }

            const orderId: string = req.params.orderId
            const order = await this.omsApiClient.getOrder(orderId)
            const webhookData: GearWebhookData = req.body.webhook_data
            const status: string = req.body.status

            if (_.isEmpty(order) || _.isEmpty(status)) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage(`Invalid params: orderId: ${orderId}, status: ${status}`).build()
            }

            res.json(await this.omsApiClient.pushNotificationsFromWebhook(orderId, status, webhookData))
        }
    }

    return GearWebhooksController
}

export default controllerFactory
