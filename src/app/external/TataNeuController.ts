import * as express from "express"
import { controller, httpGet } from "inversify-express-utils"
import { Container, inject } from "inversify"
import { BASE_TYPES, Logger } from "@curefit/base"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { TataNeuAuthMiddleware } from "./TataNeuAuthMiddleware"
import { TataNeuService } from "./TataNeuService"

export function tataNeuControllerFactory(kernel: Container) {

    @controller("/external/tata-neu", kernel.get<TataNeuAuthMiddleware>(CUREFIT_API_TYPES.TataNeuAuthMiddleware).authenticate)
    class TataNeuController {
        constructor(
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(CUREFIT_API_TYPES.TataNeuService) private tataNeuService: TataNeuService,
        ) {

        }

        @httpGet("/orders")
        async getOrders(req: express.Request) {
            const tataCustomerHash: string = req.query.tataCustomerHash
            const userId: string = await this.tataNeuService.getCFUserIdFromTataCustomerHash(tataCustomerHash)
            const after = req.query.after as number
            const before = req.query.before as number
            const count = req.query.count as number || 10
            return this.tataNeuService.getOrders(userId, after, before, count)
        }

        @httpGet("/memberships")
        async getMemberships(req: express.Request) {
            const tataCustomerHash: string = req.query.tataCustomerHash
            const userId: string = await this.tataNeuService.getCFUserIdFromTataCustomerHash(tataCustomerHash)
            return this.tataNeuService.getMemberships(userId)
        }

        @httpGet("/order/:orderId")
        async getOrderById(req: express.Request) {
            const tataCustomerHash: string = req.query.tataCustomerHash
            const orderId: string = req.params.orderId
            const userId: string = await this.tataNeuService.getCFUserIdFromTataCustomerHash(tataCustomerHash)
            return this.tataNeuService.getOrderById(orderId, userId)
        }

    }

    return TataNeuController
}

export default tataNeuControllerFactory