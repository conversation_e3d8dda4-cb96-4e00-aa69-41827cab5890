import { inject, injectable } from "inversify"
import { BASE_TYPES, Logger } from "@curefit/base"
import { OMS_API_CLIENT_TYPES, OrderService } from "@curefit/oms-api-client"
import { AugmentedOrderFetchFilter } from "@curefit/oms-api-client/dist/src/IOrderService"
import {
    CollectionCharge,
    DeliveryCharge,
    ExtraCharge,
    ProductPrice,
    ProductType,
    TippingCharge
} from "@curefit/product-common"
import { GEARVAULT_CLIENT_TYPES, IGearService } from "@curefit/gearvault-client"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { ErrorFactory, HTTP_CODE } from "@curefit/error-client"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import * as _ from "lodash"
import {
    GearPriceDetails,
    Order,
    OrderItemStatusChange,
    OrderProduct,
    OrderStatusChange
} from "@curefit/order-common/src/Order"
import {
    MembershipType,
    OwnershipType,
    Pause,
    Status as MembershipStatus
} from "@curefit/membership-commons/src/common"
import * as moment from "moment"
import { GearOrderItem, GearOrderShipment } from "@curefit/gear-common/dist/src/models/Item"
import { OrderSource } from "@curefit/base-common"
import { PaymentData, PrePaymentData } from "@curefit/payment-common"
import { UserDeliveryAddress } from "@curefit/eat-common"
import { ShipmentState, StateChangeHistory } from "@curefit/gear-common/dist/src/models/Fulfilment"
import { CatalogueVariantOption } from "@curefit/gear-common"
import { IThirdPartyService, THIRD_PARTY_CLIENT_TYPES } from "@curefit/third-party-integrations-client"
import { ErrorCodes } from "../error/ErrorCodes"

const TATA_NEU_SUPPORTED_PRODUCT_TYPES: ProductType[] = ["GEAR"]
const CULT_MEMBERSHIP_BENEFITS = ["CULT", "CULT_COMPLIMENTARY_ACCESS", "CF_LIVE"]
const TRANSFORM_MEMBERSHIP_BENEFITS = ["COACH"]
const GYMFIT_MEMBERSHIP_BENEFITS = ["GYMFIT_GX", "GYMFIT_GA"]

export interface OrderDetails {
// requested fields
    orderId: string
    createdDate: Date
    statusHistory: OrderStatusChange[]
    products: ProductDetails[]
    shipments: ShipmentDetails[]
// other fields
    gearOrderId?: string,
// payment and offers related
    source?: OrderSource
    payments?: PaymentData[]
    prePayments?: PrePaymentData[]
    deliveryCharges?: DeliveryCharge
    packagingCharges?: ExtraCharge
    collectionCharges?: CollectionCharge
    tipAmount?: TippingCharge
    totalPayable: number
    totalAmountPayable: number
    totalFitCashPayable: number
    offersApplied?: string[]
    offersInfo?: {
        productVariantIdentifier?: string
        offerId?: string
        offerVersion?: number
        offerDiscount?: number
        offerTitle?: string
        meta?: any
    }[]
    orderDiscount?: number
    orderDiscountSplit?: {
        offerId: string
        offerDiscount: number
        meta?: any
    }[]
    userAddress?: UserDeliveryAddress
    priceDetails?: GearPriceDetails,
// tata neu fields
    tataLoyaltyPointsGranted?: number
    tataLoyaltyPointsRevoked?: number
}

export interface ProductDetails {
    productId: string
    productType?: ProductType
    quantity?: number
    statusHistory?: OrderItemStatusChange[]
    price?: ProductPrice
    categoryId?: string
    addonParentProductId?: string
    addonProductIds?: string[]
    cultGearVariantId?: string
}

export interface ShipmentDetails {
    id: number
    number?: string
    state?: ShipmentState
    summary?: string
    edd?: string
    promised_edd?: string
    delivered_at?: string
    shipped_at?: string
    awb?: string
    logistics_partner_name?: string
    stock_location_name?: string
    inventory_units?: {
        id: number
        state: string
        variant_id?: number
        quantity?: number
        health?: string
        status?: string
        summary?: string
        cancelable?: boolean
        returnable?: boolean
        admin_returnable?: boolean
        replaceable?: boolean
        admin_replaceable?: boolean
        refundable?: boolean
        is_replacement?: boolean
        has_replacement?: boolean
        variant: ProductVariantDetails;
        line_item?: GearOrderItem
    }[]
    history: StateChangeHistory[]
}

export interface ProductVariantDetails {
    id: number
    sku: string
    name: string
    brand: string
    slug?: string
    description: string
    price: string
    display_price?: string
    options_text?: string
    option_values?: CatalogueVariantOption[]
    in_stock: boolean
    is_backorderable: boolean
    is_orderable: boolean
    total_on_hand: number
    is_destroyed: boolean
}

export interface MembershipDetails {
    // requested fields
    id: number
    createdDate: Date
    startDate: Date
    endDate: Date
    type: MembershipType
    pauseDaysUtilized: number
    pauseDaysAvailable: number
    // other fields
    name: string
    tenant: string
    status: MembershipStatus
    orderId: string,
    productId: string
    price: number
    owner: OwnershipType
    activePause?: Pause
}

@injectable()
export class TataNeuService {
    constructor(
        @inject(BASE_TYPES.ILogger) logger: Logger,
        @inject(OMS_API_CLIENT_TYPES.OrderService) private omsApiClient: OrderService,
        @inject(GEARVAULT_CLIENT_TYPES.GearService) private gearService: IGearService,
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) private membershipService: IMembershipService,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        @inject(THIRD_PARTY_CLIENT_TYPES.ThirdPartyService) private thirdPartyService: IThirdPartyService,
    ) {

    }

    public async getCFUserIdFromTataCustomerHash(tataCustomerHash: string): Promise<string> {
        if (_.isEmpty(tataCustomerHash)) {
            throw this.errorFactory.withCode(ErrorCodes.TATA_NEU_USER_NOT_FOUND, HTTP_CODE.BAD_REQUEST)
                .withMessage("Invalid TataNeu user hash")
                .build()
        }
        let cfUser
        try {
            cfUser = await this.thirdPartyService.getTataUserProfileForCustomerHash(tataCustomerHash)
        } catch (err) {
            if (err.statusCode == 400) {
                throw this.errorFactory.withCode(ErrorCodes.TATA_NEU_USER_NOT_FOUND, HTTP_CODE.BAD_REQUEST)
                    .withMessage("Invalid TataNeu user hash")
                    .build()
            } else {
                throw err
            }
        }
        if (!cfUser.hasGivenConsent) {
            throw this.errorFactory.withCode(ErrorCodes.TATA_NEU_NO_USER_CONSENT, HTTP_CODE.BAD_REQUEST)
                .withMessage("No user consent for TataNeu")
                .build()
        }
        return cfUser?.cfUserId
    }

    public async getOrders(userId: string, after?: number, before?: number, count: number = 10): Promise<OrderDetails[]> {
        const orderFetchFilter: AugmentedOrderFetchFilter = {
            productTypes: TATA_NEU_SUPPORTED_PRODUCT_TYPES,
            sortKey: "CREATED_DATE",
            sortDirection: "DESC",
            ...(after && {after: Number(after)}),
            ...(before && {before: Number(before)}),
            count
        }
        const orders = await this.omsApiClient.getFilteredOrdersForUser(userId, orderFetchFilter)
        const result: OrderDetails[] = []
        for (const order of orders) {
            result.push(await this.fetchOrderDetails(order))
        }
        return result
    }

    public async getOrderById(orderId: string, userId: string): Promise<OrderDetails> {
        const order = await this.omsApiClient.getOrder(orderId)
        if (order.userId !== userId) {
            throw this.errorFactory.withCode(ErrorCodes.TATA_NEU_ORDER_USER_MISMATCH, HTTP_CODE.BAD_REQUEST)
                .withMessage("OrderId doesn't belong to User")
                .build()
        }
        return this.fetchOrderDetails(order)
    }

    private async fetchOrderDetails(order: Order): Promise<OrderDetails> {
        let gearOrderDetails, gearOrderDetailsWithShipmentStatus
        try {
            gearOrderDetails = await this.gearService.getOrder(order.orderId)
            gearOrderDetailsWithShipmentStatus = await this.gearService.getOrderWithShipmentStatus(order.orderId)
        } catch (err) {
            if (err.code == 404) {
                gearOrderDetails = {}
                gearOrderDetailsWithShipmentStatus = {}
            } else
                throw err
        }
        return {
            ...this.filterOrderFields(order),
            products: this.transformProductDetails(order.products),
            shipments: this.transformShipmentDetails(gearOrderDetails.shipments, gearOrderDetailsWithShipmentStatus.shipments)
        } as OrderDetails
    }

    private filterOrderFields(order: Order): Partial<OrderDetails> {
        return _.pick(order, [
            "orderId", "createdDate", "statusHistory", "gearOrderId", "source",
            "payments", "prePayments", "deliveryCharges", "packagingCharges", "collectionCharges", "tipAmount",
            "totalPayable", "totalAmountPayable", "totalFitCashPayable", "offersApplied", "offersInfo", "orderDiscount",
            "orderDiscountSplit", "userAddress",
            "priceDetails", "tataLoyaltyPointsGranted", "tataLoyaltyPointsRevoked"
        ])
    }

    private transformProductDetails(products: OrderProduct[]): Partial<ProductDetails>[] {
        if (_.isEmpty(products)) return []
        return products.map(product => ({
            ...(_.pick(product, [
                "productId", "productType", "quantity", "statusHistory", "price", "categoryId", "addonParentProductId",
                "addonProductIds", "cultGearVariantId"
            ])),
            cultGearVariantId: _.get(product, "option.cultGearVariantId")
        }))
    }

    private transformShipmentDetails(shipments: GearOrderShipment[], shipmentsWithStatusHistory: GearOrderShipment[]): Partial<ShipmentDetails>[] {
        if (_.isEmpty(shipments)) return []
        const shipmentIdMap: Map<number, GearOrderShipment> = new Map<number, GearOrderShipment>()
        shipmentsWithStatusHistory.forEach(shipment => shipmentIdMap.set(shipment.id, shipment))
        return shipments.map(shipment => ({
            ...(_.pick(shipment, [
                "id", "number", "state", "status", "summary", "edd", "promised_edd", "awb",
                "logistics_partner_name", "stock_location_name", "line_item", "shipped_at"
            ])),
            delivered_at: shipmentIdMap.get(shipment.id)?.delivered_at,
            history: shipmentIdMap.get(shipment.id)?.history as StateChangeHistory[],
            inventory_units: shipment.inventory_units?.map(unit => ({
                ...(_.pick(unit, [
                    "id", "state", "variant_id", "quantity", "health", "status", "summary", "cancelable", "returnable",
                    "admin_returnable", "replaceable", "admin_replaceable", "refundable", "is_replacement",
                    "has_replacement", "line_item"
                ])),
                variant: _.pick(unit.variant, [
                    "id", "sku", "name", "brand", "slug", "description", "price", "display_price", "options_text",
                    "option_values", "in_stock", "is_backorderable", "is_orderable", "total_on_hand", "is_destroyed"
                ])
            } as any)),
        }))
    }

    public async getMemberships(userId: string): Promise<MembershipDetails[]> {
        const memberships = await this.membershipService.getCachedMembershipsForUser(userId, "curefit", _.concat(CULT_MEMBERSHIP_BENEFITS, TRANSFORM_MEMBERSHIP_BENEFITS, GYMFIT_MEMBERSHIP_BENEFITS))
        return memberships.map(membership => {
            return {
                id: membership.id,
                createdDate: _.has(membership, "createdOn") ? new Date(_.get(membership, "createdOn")) : undefined,
                startDate: new Date(membership.start),
                endDate: new Date(membership.end),
                type: membership.type,
                pauseDaysUtilized: (
                    Math.floor(moment.duration(membership.maxPauseDuration, "milliseconds").asDays())
                    - Math.floor(moment.duration(membership.remainingPauseDuration, "milliseconds").asDays())
                ),
                pauseDaysAvailable: Math.floor(moment.duration(membership.remainingPauseDuration, "milliseconds").asDays()),
                name: membership.name,
                tenant: membership.tenant,
                status: membership.status,
                orderId: membership.orderId,
                productId: membership.productId,
                price: membership.price,
                owner: membership.owner,
                activePause: membership.activePause
            } as MembershipDetails
        })
    }
}
