import { Container, inject } from "inversify"
import { controller, httpGet } from "inversify-express-utils"
import AuthMiddleware from "../auth/AuthMiddleware"
import kernel from "../../config/ioc/ioc"
import { Logger, BASE_TYPES } from "@curefit/base"
import * as express from "express"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { IUserService } from "@curefit/user-client"
import { Session } from "@curefit/userinfo-common"
import { ApiKeyConf } from "@curefit/auth-models"
import * as _ from "lodash"
import { Action, FitCashSummaryWidget, ProductListWidget, WidgetView } from "../common/views/WidgetView"
import { FITCASH_CLIENT_TYPES } from "@curefit/fitcash-client/dist/src/ioc/FitcashClientTypes"
import { IFitcashService } from "@curefit/fitcash-client"
import { WalletTransaction } from "@curefit/fitcash-common"
import { UserContext } from "@curefit/userinfo-common"
import { USER_CLIENT_TYPES } from "@curefit/user-client"
import { AUTH_MODELS_TYPES } from "@curefit/auth-models"
import { TimeUtil } from "@curefit/util-common"
import { ReferralUtil } from "../referral/ReferralUtil"

const LOG_PREFIX = "WalletController"
const FITCASH_FAQ_LINK = "https://s3.ap-south-1.amazonaws.com/vm-html-pages/Fitcash%20FAQs%2024022021-f4beeee4-46cb-4cd3-bb89-86acbd193dca.html"

export interface TransactionItem {
    iconUrl: string,
    title: string,
    subTitle: string,
    amount: number,
    credited: boolean,
    date: string,
    sortingDate?: number
}

interface TransactionsResponse {
  faqLink: string,
  widgets: WidgetView[],
  noTransactionView?: {
    title: string,
    subTitle: string,
    image: string,
  },
  actions?: Action[]
}


export function FitcashControllerFactory(kernal: Container) {

    @controller("/wallet", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class FitcashController {
        constructor(
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(FITCASH_CLIENT_TYPES.FitcashService) private fitcashService: IFitcashService,
            @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
            @inject(AUTH_MODELS_TYPES.ApiKeyConfiguration) private apiKeysConfs: ApiKeyConf[]) {
        }

        @httpGet("/transactions")
        async getWalletTransactions(request: express.Request): Promise<TransactionsResponse> {
            const session: Session = request.session
            const userId: string = session.userId
            const userContext: UserContext = request.userContext as UserContext
            this.logger.info(`${LOG_PREFIX}: Fetching wallet transactions for userId:: ${userId}`)
            const walletBalancePromise =  this.fitcashService.balance(userId, userContext.userProfile.city.country.currencyCode)
            const transactionsPromise =  this.fitcashService.getTransactions(userId)
            const isNewReferralSupported = ReferralUtil.isNewReferralPageSupported(userContext)
            const promises: Promise<any>[] = []
            promises.push(walletBalancePromise)
            promises.push(transactionsPromise)
            await Promise.all(promises)
            const walletBalance = await walletBalancePromise
            const transactions = await transactionsPromise
            const fitCashSummaryWidget: FitCashSummaryWidget = {
                widgetType: "FITCASH_SUMMARY_WIDGET",
                title: "TOTAL BALANCE",
                balance: walletBalance && walletBalance.balance ? (walletBalance.balance / 100) : 0,
                subtitle: "1 Fitcash = ₹1"
            }

            if (transactions.length > 0) {
                const transactionsWidget: ProductListWidget = {
                    widgetType: "PRODUCT_LIST_WIDGET",
                    type: "CREDIT",
                    hideSepratorLines: false,
                    header: {
                        title: "TRANSACTION HISTORY",
                        color: "#000000"
                    },
                    items: FitcashController.transformTransactionsToItems(transactions, userContext)
                }
                return {
                    faqLink: FITCASH_FAQ_LINK,
                    widgets: [
                        fitCashSummaryWidget,
                        transactionsWidget
                    ]
                }
            } else {
                return {
                    faqLink: FITCASH_FAQ_LINK,
                    widgets: [
                        fitCashSummaryWidget
                    ],
                    noTransactionView: {
                      title: "Refer & Earn",
                      subTitle: "Referring friends can help you earn Fitcash. You can get ₹300 for every referral.",
                      image: "/image/icons/fitcash/emptyWallet.png"
                    },
                    actions: [
                      {
                        "title": "REFER A FRIEND",
                        "url": isNewReferralSupported ? "curefit://fl_referral_page" : "curefit://referralpagev2",
                        "actionType": "NAVIGATION"
                      }
                    ]
                }
            }
        }

        private static transformTransactionsToItems(transactions: WalletTransaction[], userContext: UserContext): TransactionItem[] {

            return _.map(transactions, (trxn: WalletTransaction) => {
                return {
                    iconUrl: trxn.transactionType === "WITHDRAW" ? "/image/icons/fitcash/withdraw.png" : "/image/icons/fitcash/deposit.png", // todo(@hunny): to get icon according to vertical
                    title: trxn.title,
                    subTitle: trxn.subTitle,
                    amount: trxn.amount ? (trxn.amount / 100) : 0,
                    credited: trxn.transactionType !== "WITHDRAW",
                    date: TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, trxn.createdAt, "DD MMM")
                }
            })
        }
    }

    return FitcashController
}

export default FitcashControllerFactory
