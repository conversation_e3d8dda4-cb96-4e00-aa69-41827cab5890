import { BaseDelayedBatched<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BaseDelayedQueueHandler, Message } from "@curefit/sqs-client"
import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { IQueueService, SQS_CLIENT_TYPES } from "@curefit/sqs-client"
import { FeedbackProductType, FeedbackType } from "@curefit/feedback-common"
import IDeviceBusiness from "../device/IDeviceBusiness"
import { Constants } from "@curefit/base-utils"
import { Logger, BASE_TYPES } from "@curefit/base"
import { CultMembership } from "@curefit/cult-common"
import * as _ from "lodash"
import { AnnouncementBusiness } from "../announcement/AnnouncementBusiness"

interface FitnessReportPayload {
    reportId: number
    userId: string
    startDate: string,
    endDate: string,
    productType: "FITNESS" | "MIND"
}

@injectable()
class FitnessReportQueueListener extends BaseDelayedBatchedQ<PERSON>ue<PERSON><PERSON>ler {

    constructor(
        @inject(SQS_CLIENT_TYPES.QueueService) queueService: IQueueService,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(CUREFIT_API_TYPES.AnnouncementBusiness) private announcementBusiness: AnnouncementBusiness,
    ) {
        super(Constants.getSQSQueue("FITNESS_REPORT"), 10, queueService, 60 * 1000)
    }

    async handle(body: Message[]): Promise<boolean[]> {
        return Promise.all(body.map(message => this.handleMessage(message.data, message.attributes)))
    }

    async handleMessage(message: string, attributes: { [key: string]: any }): Promise<boolean> {
        this.logger.debug("Fitness report: " + message)
        const fitnessReportMessage: FitnessReportPayload = <FitnessReportPayload>JSON.parse(message)
        return this.createFitnessAnnouncement(fitnessReportMessage)
    }

    createFitnessAnnouncement(fitnessReportMessage: any): Promise<boolean> {
        if (fitnessReportMessage.userId, fitnessReportMessage.startDate) {
            return this.announcementBusiness.createReportGeneratedAnnouncement(fitnessReportMessage.userId, fitnessReportMessage.startDate)
        }
        return Promise.resolve(false)
    }
}

export default FitnessReportQueueListener
