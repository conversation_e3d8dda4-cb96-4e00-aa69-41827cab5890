import * as express from "express"
import { controller, httpPut } from "inversify-express-utils"
import { Container, inject } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { IQueueService, SQS_CLIENT_TYPES } from "@curefit/sqs-client"
import { ConvertedEventRequest, EVENT_TYPE } from "@curefit/iris-client"
import { Constants } from "@curefit/base-utils"
import { PromUtil } from "../../util/PromUtil"

export function controllerFactory(kernel: Container) {
    @controller("/notifications")
    class NotificationController {
        constructor(
            @inject(SQS_CLIENT_TYPES.QueueService) private queueService: IQueueService,
            @inject(CUREFIT_API_TYPES.PromUtil) private promUtil: PromUtil) {
        }

        @httpPut("/:id/:type")
        public submit(req: express.Request): Promise<boolean> {
            this.promUtil.reportNotificationCount(req.params.type)
            const notificationId: string = req.params.id
            const eventType: EVENT_TYPE = req.params.type
            // const timestamp: string = req.params.timestamp
            const convertedDataType: string = req.body.type
            const convertedDataId: string = req.body.id
            const convertedData: ConvertedEventRequest = {
                entityType: convertedDataType, entityId: convertedDataId
            }
            const queueName = Constants.getSQSQueue("IRIS_EVENTS")
            const attributes: Map<string, any> = new Map<string, any>()
            attributes.set("notificationId", notificationId)
            attributes.set("eventType", eventType)
            attributes.set("timestamp", Date.now() + "")
            return Promise.resolve(this.queueService.sendMessageAsync(queueName, convertedData, attributes))
        }

    }

    return NotificationController
}

export default controllerFactory

