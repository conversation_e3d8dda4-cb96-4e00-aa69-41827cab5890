import { Action } from "@curefit/apps-common"
import { BASE_TYPES, Logger } from "@curefit/base"
import { MealU<PERSON> } from "@curefit/base-utils"
import { ConsultationProduct } from "@curefit/care-common"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { CULT_CLIENT_TYPES, ICultServiceOld as ICultService } from "@curefit/cult-client"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService } from "@curefit/diy-client"
import { AddressTag, FoodProduct as Product } from "@curefit/eat-common"
import { ErrorFactory } from "@curefit/error-client"
import {
    AdditionalFeedback,
    EntryWidget,
    Feedback,
    FeedbackMeta,
    FeedbackTag,
    FitScore,
    FitStreak,
    Questions,
    Rating,
    Tag
} from "@curefit/feedback-common"
import { FEEDBACK_MONGO_TYPES, IFeedbackReadWriteDao } from "@curefit/feedback-mongo"
import { GYMFIT_CLIENT_TYPES, IGymfitService } from "@curefit/gymfit-client"
import { OrderSource } from "@curefit/order-common"
import { Session, UserContext } from "@curefit/userinfo-common"
import { eternalPromise, TimeUtil } from "@curefit/util-common"
import * as express from "express"
import { Container, inject } from "inversify"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import * as _ from "lodash"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import AuthMiddleware from "../auth/AuthMiddleware"
import { ErrorCodes } from "../error/ErrorCodes"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import FeedbackPageConfigV2Cache from "../ugc/FeedbackPageConfigV2Cache"
import AppUtil from "../util/AppUtil"
import { CacheHelper } from "../util/CacheHelper"
import { PromiseCache } from "../util/VMUtil"
import FeedbackDetailView from "./FeedbackDetailView"
import FeedbackViewBuilder from "./FeedbackViewBuilder"
import { IFeedbackBusiness } from "./IFeedbackBusiness"
import { CultClass } from "@curefit/cult-common"
import { CENTER_SERVICE_CLIENT_TYPES, ICenterService } from "@curefit/center-service-client"
import { ISportsApi, SPORT_API_CLIENT_TYPES } from "@curefit/sports-api-client-node"
import { IAppConfigStoreService } from "../appConfig/AppConfigStoreService"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { IShipmentService, OMS_API_CLIENT_TYPES } from "@curefit/oms-api-client"
import { IPersonalTrainingService, PERSONAL_TRAINING_CLIENT_TYPES } from "@curefit/personal-training-v2-client"
import { PtSession, PtSessionStatus, SessionSearchRequest, SortOrder, } from "@curefit/personal-training-v2-common"
import { HeadersUtil } from "../../util/HeadersUtil"


export interface SubmitFeedbackResponse {
    title: string,
    subtitle: string,
    showContactSupportOption: boolean,
    contactSupportText: string,
    forceTicketCreation: boolean,
    showPopUp: boolean,
    feedbackId: string,
    actions: Action[],
    interventionAction?: Action,
    showAppRatingPrompt: boolean,
    isNewAppRatingFlow: boolean,
    ratingPromptHeader: string,
    ratingPromptSubtext: string,
    ratingPromptDismissText: string,
    ratingPromptAcceptText: string,
}

export type ProductDetails = CultClass


export function controllerFactory(kernel: Container) {
    @controller("/feedback",
        kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class FeedbackController {
        constructor(@inject(CUREFIT_API_TYPES.FeedbackBusiness) private feedbackBusiness: IFeedbackBusiness,
                    @inject(FEEDBACK_MONGO_TYPES.FeedbackReadWriteDao) private feedbackDao: IFeedbackReadWriteDao,
                    @inject(CULT_CLIENT_TYPES.CultService) private cultService: ICultService,
                    @inject(OMS_API_CLIENT_TYPES.ShipmentService) private shipmentService: IShipmentService,
                    @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalougeService: ICatalogueService,
                    @inject(CUREFIT_API_TYPES.FeedbackPageConfigV2Cache) private feedbackPageConfigV2Cache: FeedbackPageConfigV2Cache,
                    @inject(CUREFIT_API_TYPES.FeedbackViewBuilder) private feedbackViewBuilder: FeedbackViewBuilder,
                    @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) private diyFulfilmentService: IDIYFulfilmentService,
                    @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
                    @inject(GYMFIT_CLIENT_TYPES.GymfitService) private gymfitService: IGymfitService,
                    @inject(BASE_TYPES.ILogger) private logger: Logger,
                    @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
                    @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
                    @inject(CENTER_SERVICE_CLIENT_TYPES.CenterService) protected centerService: ICenterService,
                    @inject(SPORT_API_CLIENT_TYPES.SportsApi) private sportsApiService: ISportsApi,
                    @inject(CUREFIT_API_TYPES.AppConfigStoreService) private appConfigStore?: IAppConfigStoreService,
                    @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness?: HamletBusiness,
                    @inject(PERSONAL_TRAINING_CLIENT_TYPES.PersonalTrainingService) private ptService?: IPersonalTrainingService,

        ) {
        }

        @httpPost("/submit")
        public async submit(req: express.Request, res: express.Response): Promise<boolean> {
            const session: Session = req.session
            const feedbackId: string = req.body.feedbackId
            const userContext = req.userContext as UserContext
            const rating: Rating = req.body.rating
            const review: string = req.body.review
            const forceTriggerTicket: boolean = req.body.forceTriggerTicket || false
            const selectedTags: string[] = req.body.selectedTags
            const selectedItemIds: string[] = req.body.selectedItemIds
            const apiKey: string = req.headers["apikey"] as string
            const orderSource: OrderSource = AppUtil.callSource(apiKey)
            const feedback = await this.feedbackBusiness.submitFeedback(userContext.userProfile.timezone, userContext.userProfile.userId, feedbackId, session.deviceId, rating, review, selectedTags, selectedItemIds, orderSource, forceTriggerTicket)
            return !!feedback
        }

        @httpPost("/submit/v2")
        public async submitV2(req: express.Request, res: express.Response): Promise<SubmitFeedbackResponse> {
            const userId: string = req.body.userId || req.session.userId
            const feedbackId: string = req.body.feedbackId
            const itemId: string = req.body.itemId
            const rating: Rating = req.body.rating
            const review: string = req.body.review
            const selectedTags: Tag[] = req.body.selectedTags || []
            const selectedItemIds: string[] = req.body.selectedItemIds || []
            const images: string[] = req.body.images || []
            const additionalFeedbacks: any = req.body.additionalFeedbacks || []
            const meta: FeedbackMeta = req.body.meta
            const selectedTagsV2: FeedbackTag[] = req.body.selectedTagsV2?.map((entry: any) => {
                const curr: FeedbackTag = {
                    text: entry.text,
                    isSelected: entry.selected || entry.isSelected,
                    triggerTicket: entry.triggerTicket,
                    subTags: entry.subTags
                }
                return curr})
            // TODO Remove this check when the appVersion > 9.38 is rolled out to 100%
            const additionalFeedbacksArray: AdditionalFeedback[] = !Array.isArray(additionalFeedbacks) ?
                    additionalFeedbacks &&
                    Object.keys(additionalFeedbacks).length > 0 &&
                    Object.keys(additionalFeedbacks).map((key: any) => {
                        const additionalFeedback: any = additionalFeedbacks[key]
                        if (additionalFeedback) {
                            const extraQuestionData =  additionalFeedback.questions &&
                                Object.keys(additionalFeedback.questions).length > 0 &&
                                additionalFeedback.questions[Object.keys(additionalFeedback.questions)[0]] || {}
                            return {
                                review: additionalFeedback.review,
                                filterId: extraQuestionData && extraQuestionData.filterId,
                                questions:
                                    additionalFeedback.questions &&
                                    Object.keys(additionalFeedback.questions).length > 0 &&
                                    Object.keys(additionalFeedback.questions).map(
                                        (key: any) => {
                                            const question = additionalFeedback.questions[key] || {}
                                            const ratingTags = question.ratingTags || []
                                            ratingTags.forEach((tag: any) => {
                                                let isSelected = tag.isSelected
                                                // if isSelected is undefined then set it to false
                                                if (!isSelected) {
                                                    isSelected = false
                                                } else {
                                                    isSelected = true
                                                }
                                                tag.isSelected = isSelected
                                            })
                                            return {
                                                rating: question.rating && question.rating.toString(),
                                                tags: question.selectedTags || [],
                                                ratingTags: question.ratingTags || [],
                                            }
                                        }
                                    ),
                            }
                        }
                    }) : additionalFeedbacks

            this.logger.info(`additionalFeedbacksArray_1 : ${JSON.stringify(additionalFeedbacksArray)}`)
            additionalFeedbacksArray && additionalFeedbacksArray.forEach((additionalFeedback: AdditionalFeedback) => {
                additionalFeedback.questions && additionalFeedback.questions.forEach((question: Questions) => {
                    question.ratingTags && question.ratingTags.forEach((tag: FeedbackTag) => {
                        let isSelected = tag.isSelected
                        // if isSelected is undefined then set it to false
                        if (!isSelected) {
                            isSelected = false
                        } else {
                            isSelected = true
                        }
                        tag.isSelected = isSelected
                    })
                })
            })

            this.logger.info(`additionalFeedbacksArray_2 : ${JSON.stringify(additionalFeedbacksArray)}`)

            const userContext = req.userContext as UserContext
            const tenant = AppUtil.getTenantFromReq(req)
            if (!userContext.userProfile.promiseMapCache) {
                userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
            }
            return this.feedbackBusiness.submitFeedBackV2(userContext, userId, feedbackId, itemId, rating, review, selectedTags, selectedItemIds, images, tenant, additionalFeedbacksArray, meta, selectedTagsV2)
        }

        @httpPost("/contactSupport")
        public async contactSupport(req: express.Request, res: express.Response): Promise<boolean> {
            const session: Session = req.session
            const forceTicket: boolean = req.body.forceTicketCreation
            const feedbackId: string = req.body.feedbackId
            const apiKey: string = req.headers["apikey"] as string
            const orderSource: OrderSource = AppUtil.callSource(apiKey)
            const feedback: Feedback = await this.feedbackBusiness.createSupportTicketAndUpdateFeedback(feedbackId, forceTicket, session.deviceId, orderSource)
            return !!feedback
        }

        @httpGet("/getWidgets/:feedbackId")
        public async getWidgets(req: express.Request, res: express.Response): Promise<FeedbackDetailView> {
            const session: Session = req.session
            const feedbackId: string = req.params.feedbackId
            if (!feedbackId || feedbackId === "undefined") {
                this.logger.error("Invalid feedback id")
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("invalid feedbackId: " + feedbackId).build()
            }
            const userId: string = session.userId
            const getDashBoard: boolean = req.query.dashboard
            const itemId: string = req.query.itemId
            const endRoute: string = req.query.endRoute
            const userContext = req.userContext as UserContext
            const countryId = AppUtil.getCountryId(userContext)
            const timezone = userContext && userContext.userProfile && userContext.userProfile.timezone || TimeUtil.IST_TIMEZONE
            let feedback: Feedback
            try {
                feedback = await this.feedbackDao.findOne({feedbackId: feedbackId})
            } catch (e) {
                this.logger.error("ERROR::Error fetching feedback for id:" + feedbackId)
                this.serviceInterfaces.rollbarService.sendError(e)
            }
            // TODO: @chetan.jha to fix the live video call feedback flow properly. Currently, looks like  implemented in a hacky way
            if (!feedback && AppUtil.isLiveVideoCallFeedback(feedbackId)) {
                feedback = this.getLiveVideoCallFeedback(feedbackId, itemId, userId)
            }
            if (_.isNil(feedback)) {
                this.logger.error("Invalid feedback id")
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid feedback id").build()
            }
            let productTitle: string = undefined
            let products: Product[] = undefined
            const productDetails = await this.getProductDetailsFromFeedback(feedback, userId, userContext)
            if (feedback.productType === "FOOD" || feedback.productType === "GEAR" || feedback.productType === "FOOD_MARKETPLACE") {
                products = await this.catalougeService.getProducts(feedback.itemIds)
                productTitle = await this.getProductTitleFromFeedback(userContext, feedback, userId, products, countryId)
            } else {
                productTitle = await this.getProductTitleFromFeedback(userContext, feedback, userId, undefined, countryId)
            }
            this.logger.info("product title: " + productTitle)
            const score: { fitScore: FitScore, fitStreak: FitStreak } = undefined
            const entryWidget: EntryWidget = await this.getEntryDetialsfromFeedback(userContext, feedback, userId)
            let subType = undefined,
                familyType,
                addressType: AddressTag
            if (feedback.productType === "FOOD") {
                const shipment = await this.shipmentService.getFoodShipmentsByShipmentId(feedback.shipmentId)
                if (shipment.deliveryChannel !== "CAFE") {
                    subType = MealUtil.getSlotName(shipment.deliverySlot, timezone)
                }
                if (shipment.listingBrand === "WHOLE_FIT") {
                    feedback.productType = "WHOLE_FIT"
                    subType = "Order"
                }
                addressType = shipment.userAddress.addressType
            } else if (feedback.productType === "CF_INCENTRE_CONSULTATION" || feedback.productType === "CF_ONLINE_CONSULTATION") {
                const consultationProduct = <ConsultationProduct>await this.catalougeService.getProduct(feedback.itemId)
                subType = consultationProduct.doctorType
                familyType = consultationProduct.familyType
            }

            const isForcedFeedbackEnabled = await AppUtil.isForcedFeedbackEnabled(userContext, this.hamletBusiness)
            this.logger.info(`feedbackforce : ${isForcedFeedbackEnabled}`)

            return Promise.resolve(this.feedbackViewBuilder.getView(this.feedbackPageConfigV2Cache, userContext, feedback, feedback.productType, subType, productTitle, feedback.feedbackId, endRoute, score, undefined, products, addressType, familyType, productDetails, isForcedFeedbackEnabled, entryWidget))
        }

        private getLiveVideoCallFeedback(feedbackId: string, itemId: string, userId: string): Feedback {
            return {
                feedbackId,
                productType: "LIVE_VIDEO_CALL",
                itemId,
                userId,
                itemIds: [itemId],
                selectedTags: [],
                selectedItemIds: [itemId],
                rating: "NOT_RATED",
                dateTime: new Date()
            }
        }
        private static formatDate(dateString: string): string {
            const date = new Date(dateString)
            const today = new Date()
            const yesterday = new Date(today)
            yesterday.setDate(today.getDate() - 1)

            // Check if the date is today
            if (date.toDateString() === today.toDateString()) {
                return "Today"
            }

            // Check if the date is yesterday
            if (date.toDateString() === yesterday.toDateString()) {
                return "Yesterday"
            }

            // Otherwise, format as 'DD MMM' (e.g., '28 Oct')
            const day = date.getDate()
            const month = date.toLocaleString("default", { month: "short" })
            return `${day} ${month}`
        }
        private static formatTime(timeString: string, IST: boolean): string {
            let [hours, minutes] = timeString.split(":").map(Number)
            if (!IST) {
                hours += 5
                minutes += 30

                // Adjust for overflow in minutes and hours
                if (minutes >= 60) {
                    hours += 1
                    minutes -= 60
                }
                if (hours >= 24) {
                    hours -= 24 // Keep hours within a 24-hour format
                }
            }
            const period = hours >= 12 ? "PM" : "AM"
            const formattedHours = hours % 12 || 12 // Convert 0 or 12 to 12 for AM/PM
            const formattedMinutes = String(minutes).padStart(2, "0")

            return `${formattedHours}:${formattedMinutes} ${period}`
        }
        private static convertEpochtoDateTime(epoch: number): { date: string; time: string } {
            const dateObj = new Date(epoch)

            // Format date as "DD MMM" (e.g., "10 Feb")
            const optionsDate: Intl.DateTimeFormatOptions = { day: "2-digit", month: "short" }
            const date = dateObj.toLocaleDateString("en-US", optionsDate)

            // Format time as "hh:mm A" (e.g., "07:00 PM")
            const optionsTime: Intl.DateTimeFormatOptions = { hour: "2-digit", minute: "2-digit", hour12: true }
            const time = dateObj.toLocaleTimeString("en-US", optionsTime).replace(/^0/, "") // Remove leading zero if any

            return { date, time }
        }

        private async getEntryDetialsfromFeedback(userContext: UserContext, feedback: Feedback, userId: string): Promise<EntryWidget> {
            switch (feedback.productType) {
                case "PLAY":
                    const playSessionDetails = await this.sportsApiService.getPlaySessionData(Number(feedback.itemId), Number(feedback.userId), false)
                    const workoutName = playSessionDetails.workout ? playSessionDetails.workout : ""
                    const center = playSessionDetails.center ? playSessionDetails.center : ""
                    const date = playSessionDetails.date ? playSessionDetails.date : ""
                    const time = playSessionDetails.startTime ? playSessionDetails.startTime : ""
                    const formattedDatePlay = FeedbackController.formatDate(date)
                    const formattedTimePlay = FeedbackController.formatTime(time, false)
                    return { date: formattedDatePlay, time: formattedTimePlay, type: workoutName, center }
                case "FITNESS":
                    const userPromise = this.userCache.getUser(userId)
                    const user = await userPromise
                    const subUserIds = user.subUserRelations && user.subUserRelations.map(subUserRelation => {
                        return subUserRelation.subUserId
                    })
                    const isSubUserFeedback = subUserIds && subUserIds.indexOf(feedback.userId) >= 0 ? true : false

                    let classs, subUser
                    if (isSubUserFeedback) {
                        subUser = await this.userCache.getUser(feedback.userId)
                        classs = await this.cultService.getCultClass(feedback.itemId, userId, undefined, undefined, feedback.userId, undefined, userContext.sessionInfo.deviceId)
                    } else {
                        classs = await this.cultService.getCultClass(feedback.itemId, userId, undefined, undefined, undefined, undefined, userContext.sessionInfo.deviceId)
                    }
                    const formattedDate = FeedbackController.formatDate(classs.date)
                    const formattedTime = FeedbackController.formatTime(classs.startTime, true)
                    return { date: formattedDate, time: formattedTime, type: classs.Workout.name, center: classs.Center.name }
                case "GYMFIT_FITNESS_PRODUCT":
                    // const gym = await this.gymfitService.getCheckinById(parseInt(feedback.itemId), userId)
                    const gymCenter = (await this.gymfitService.getGymfitCenterById(feedback.itemId)).center
                    // const startTime = gym.startTime
                    // const {date: gymdate, time: gymtime} = FeedbackController.convertEpochDateTime(startTime)
                    return { date: "", time: "", type: "Gym Workout", center: gymCenter.name }
                case "GYM_PT_PRODUCT":
                case "GYM_PT_PPC_PRODUCT":
                     const searchRequest: SessionSearchRequest = {
                                            tenantId: 1,
                                            userIds: [userId.toString()],
                                            startDateSortOrder : SortOrder.DESC,
                                            statuses : [PtSessionStatus.ATTENDED]
                                        }
                    const PT_details: PtSession[] = await this.ptService.latestSessionByUser(searchRequest,  HeadersUtil.getCommonHeaders(userContext))
                    if (PT_details.length > 0) {
                            const centerSericeID = PT_details[0].centerId
                            const center = (await this.centerService.getCenterById(Number(centerSericeID))).name
                            const epoch = PT_details[0].startTime
                            const {date, time} = FeedbackController.convertEpochtoDateTime(epoch)
                            return { date: date, time: time, type: "Personal Training Workout", center: center }
                        }
                    return { date: " ", time: " ", type: "Personal Training Workout", center: " " }
                default:
                    return { date: " ", time: " ", type: " ", center: " " }

            }
        }
        private async getProductTitleFromFeedback(userContext: UserContext, feedback: Feedback, userId: string, products?: Product[], countryId?: string): Promise<string> {

            switch (feedback.productType) {
                case "DIY_MEDITATION":
                case "DIY_FITNESS":
                    let title
                    const product = await this.catalougeService.getProduct(feedback.itemId)
                    title = product.title
                    if (feedback.packId) {
                        const pack = await this.catalougeService.getProduct(feedback.packId)
                        title = title + " of " + pack.title
                    }
                    return title
                case "FITNESS": {
                    const userPromise = this.userCache.getUser(userId)
                    const user = await userPromise
                    const subUserIds = user.subUserRelations && user.subUserRelations.map(subUserRelation => {
                        return subUserRelation.subUserId
                    })
                    const isSubUserFeedback = subUserIds && subUserIds.indexOf(feedback.userId) >= 0 ? true : false

                    let classs, subUser
                    if (isSubUserFeedback) {
                        subUser = await this.userCache.getUser(feedback.userId)
                        classs = await this.cultService.getCultClass(feedback.itemId, userId, undefined, undefined, feedback.userId, undefined, userContext.sessionInfo.deviceId)
                        return `${classs.Workout.name}\n(for ${subUser.firstName} ${subUser.lastName})`
                    } else {
                        classs = await this.cultService.getCultClass(feedback.itemId, userId, undefined, undefined, undefined, undefined, userContext.sessionInfo.deviceId)
                    }
                    return classs.Workout.name
                }
                case "MIND":
                    const mindClass = await this.cultService.getMindClass(feedback.itemId, userId, undefined, undefined, undefined, userContext.sessionInfo.deviceId)
                    return mindClass.Workout.name
                case "FOOD":
                    return this.getFoodProductTitleFromFeedback(products)
                case "CF_ONLINE_CONSULTATION":
                case "CF_INCENTRE_CONSULTATION":
                    return (await this.catalougeService.getProduct(feedback.itemId)).title
                case "CULT_SCORE_DIY":
                    return "Cult Score"
                case "GEAR":
                    return this.getFoodProductTitleFromFeedback(products)
                case "RECIPE":
                    return (await this.diyFulfilmentService.getRecipeById(feedback.itemId, AppUtil.getTenantFromUserContext(userContext), userId, countryId)).title
                case "FITNESS_REPORT":
                    return `Performance for the week`
                case "LIVE_SESSION_REPORT":
                    const userScoreMetricsResponse = await this.diyFulfilmentService.getUserScoreMetricsByReportId(feedback.itemId)
                    const liveVideosResponse = (await eternalPromise(this.diyFulfilmentService.getDigitalCatalogueEntry(userScoreMetricsResponse.contentId))).obj
                    return !_.isEmpty(liveVideosResponse) ? liveVideosResponse.title : `Live Session`
                case "GYMFIT_FITNESS_PRODUCT":
                    const center = (await this.gymfitService.getGymfitCenterById(feedback.itemId)).center
                    return center.name
                case "LIVE":
                case "LIVE_VIDEO_CALL":
                    const liveClass = await this.diyFulfilmentService.getDigitalCatalogueEntry(feedback.itemId)
                    return liveClass.title
                case "CULT_PT_TRAINER_CUSTOMER_CHAT":
                    return `Rate your chat experience with ${feedback.orderId}`
                case "GYM_PT_PRODUCT":
                case "GYM_PT_PPC_PRODUCT":
                    const centerServiceCenter = (await this.centerService.getCenterById(Number(feedback.itemId)))
                    return centerServiceCenter.name
                case "PLAY":
                    const playSessionDetails = await this.sportsApiService.getPlaySessionData(Number(feedback.itemId), Number(feedback.userId), false)
                    const workoutName = playSessionDetails.workout ? (playSessionDetails.workout + " at ") : ""
                    const subtitle: string = workoutName + (playSessionDetails.center ? playSessionDetails.center : "")
                    return subtitle
                    return "Rate your session"
            }
        }

        private async getProductDetailsFromFeedback(feedback: Feedback, userId: string, userContext?: UserContext): Promise<ProductDetails> {
            switch (feedback.productType) {
                case "FITNESS": {
                    const userPromise = this.userCache.getUser(userId)
                    const user = await userPromise
                    const subUserIds = user.subUserRelations && user.subUserRelations.map(subUserRelation => {
                        return subUserRelation.subUserId
                    })
                    const isSubUserFeedback = subUserIds && subUserIds.indexOf(feedback.userId) >= 0
                    const cultClassParams = {
                        includeFeedbackDetails: 1
                    }

                    let classs, subUser
                    if (isSubUserFeedback) {
                        subUser = await this.userCache.getUser(feedback.userId)
                        classs = await this.cultService.getCultClass(feedback.itemId, userId, undefined, undefined, feedback.userId, cultClassParams, userContext.sessionInfo.deviceId)
                    } else {
                        classs = await this.cultService.getCultClass(feedback.itemId, userId, undefined, undefined, undefined, cultClassParams, userContext.sessionInfo.deviceId)
                    }
                    return classs
                }
            }
        }


        private getFoodProductTitleFromFeedback(products: Product[]): string {
            return products.reduce((currentText, product, index) => {
                return index === 0 ? product.title :
                    index === 1 ? currentText + ", " + product.title :
                        index === 2 ? currentText + " + " + (products.length - index) + " more" : currentText
            }, "")
        }

    }

    return FeedbackController
}

export default controllerFactory

