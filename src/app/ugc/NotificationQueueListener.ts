import { BaseDelayedBatch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BaseDelayed<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Message } from "@curefit/sqs-client"
import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { IQueueService, SQS_CLIENT_TYPES } from "@curefit/sqs-client"
import { FoodProduct as Product, ListingBrandIdType, MenuType } from "@curefit/eat-common"
import { ProductType } from "@curefit/product-common"
import IDeviceBusiness from "../device/IDeviceBusiness"
import { Constants, SeoUrlParams } from "@curefit/base-utils"
import { Logger, BASE_TYPES } from "@curefit/base"
import { ICatalogueService, CATALOG_CLIENT_TYPES } from "@curefit/catalog-client"
import { FoodPackStatus } from "./IFeedbackBusiness"
import { FoodShipmentStatus } from "@curefit/eat-common"
import { capitalizeFirstLetter, Timezone, TimeUtil } from "@curefit/util-common"
import * as _ from "lodash"
import { NotificationType } from "@curefit/iris-common"
import { ActionUtil } from "@curefit/base-utils"
import EatUtil from "../util/EatUtil"
import MetricsUtil from "../metrics/MetricsUtil"
import { Tenant } from "@curefit/base-common"


interface BaseDeviceNotification {
    productType: ProductType
    userId: string
    title: string
    subTitle: string
}

interface MealNotification extends BaseDeviceNotification {
    productId: string
    productName: string
    fulfilmentId: string
    deliveryDate: string
    deliverySlot: string
    state: FoodShipmentStatus | "UPCOMING"
    shipmentId?: string
    pack?: FoodPackStatus
}

interface PackExpiryNotification extends BaseDeviceNotification {
    packId?: string
}

interface DIYReminderNotification extends BaseDeviceNotification {
    packId: string
    activityId: string
}

export interface NotificationPayload {
    title: string
    body: string
    android_channel_id?: string
    sound: string
    icon: string
    image?: string
    // TODO data should not be optional field
    data?: {
        action: string,
        analyticData: NotificationAnalyticData
    }
}

interface NotificationAnalyticData {
    notificationId: string,
    campaignId?: string,
    creativeId?: string,
    notificationType?: NotificationType,
    minAppVersion?: number
}

interface NotificationMessage {
    title: string
    subTitle: string
    action: string
    image?: string
}

interface NpsFeedbackNotification extends BaseDeviceNotification {
    formId: string
    auth: string
}

@injectable()
class NotificationQueueListener extends BaseDelayedBatchedQueueHandler {

    constructor(
        @inject(SQS_CLIENT_TYPES.QueueService) queueService: IQueueService,
        @inject(CUREFIT_API_TYPES.DeviceBusiness) private deviceBusiness: IDeviceBusiness,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(CUREFIT_API_TYPES.MetricsUtil) private metricsUtil: MetricsUtil
    ) {
        super(Constants.getSQSQueue("DEVICE_NOTIFICATION"), 10, queueService, 60 * 1000)

    }

    async handle(body: Message[]): Promise<boolean[]> {
        return Promise.all(body.map(message => this.handleMessage(message.data, message.attributes)))
    }

    async handleMessage(message: string, attributes: { [key: string]: any }, timezone: Timezone = TimeUtil.IST_TIMEZONE): Promise<boolean> {
        this.logger.debug("Notification message: " + message)
        const messageType: string = <string>(attributes && attributes["messageType"] ? attributes["messageType"].StringValue : undefined)
        let userId: string
        // Using Indian Tenant as this part of code is not used.
        const tenant: Tenant = Tenant.CUREFIT_APP
        const analyticData: NotificationAnalyticData = {
            notificationId: <string>(attributes && attributes["notificationId"] ? attributes["notificationId"].StringValue : undefined),
            creativeId: <string>(attributes && attributes["creativeId"] ? attributes["creativeId"].StringValue : undefined),
            campaignId: <string>(attributes && attributes["campaignId"] ? attributes["campaignId"].StringValue : undefined),
            notificationType: <NotificationType>(attributes && attributes["notificationType"] ? attributes["notificationType"].StringValue : undefined),
            minAppVersion: (attributes && attributes["minAppVersion"] ? parseFloat(attributes["minAppVersion"].StringValue) : undefined),
        }
        const activeDevices = this.deviceBusiness.getActiveDevice(userId, tenant)
        if (analyticData && analyticData.notificationId && !_.isEmpty(activeDevices)) {
            const queueName = Constants.getSQSQueue("IRIS_EVENTS")
            const attributes: Map<string, any> = new Map<string, any>()
            attributes.set("notificationId", analyticData.notificationId)
            attributes.set("eventType", "PROCESSED")
            attributes.set("timestamp", Date.now() + "")
            this.queueService.sendMessageAsync(queueName, {}, attributes)
        }

        this.metricsUtil.reportNotificationMessageType(messageType)
        switch (messageType) {

            case "NUDGE_USER":
                // TODO make changes to move it to function
                userId = <string>attributes["userId"].StringValue
                const nudgePayload: NotificationPayload = <NotificationPayload>JSON.parse(message)
                nudgePayload.data = {
                    action: "curefit://today",
                    analyticData: analyticData
                }
                return this.deviceBusiness.sendNotification(userId, nudgePayload, tenant)
            case "FITNESS_PACK_EXPIRY":
                return this.createPackExpiryNotification(<PackExpiryNotification>JSON.parse(message), analyticData, tenant)
            case "NOTIFY_USER":
                userId = <string>attributes["userId"].StringValue
                const notificationMessage: NotificationMessage = <NotificationMessage>JSON.parse(message)
                return this.createNotification(userId, notificationMessage, analyticData, tenant)
            case "DIY_REMINDER":
                return this.createDIYReminderNotification(<DIYReminderNotification>JSON.parse(message), analyticData, tenant)
            case "NPS_FEEDBACK":
                return this.createNpsFeedbackNotification(<NpsFeedbackNotification>JSON.parse(message), analyticData, tenant)
            default:
                return Promise.resolve(false)
        }

    }

    createNpsFeedbackNotification(notification: NpsFeedbackNotification, analyticData: NotificationAnalyticData, tenant: Tenant): Promise<boolean> {
        const formURL = "https://curefit.typeform.com/to/" + notification.formId + "?auth=" + notification.auth
        const notificationPayload: NotificationPayload = {
            "title": notification.title,
            "body": notification.subTitle,
            "sound": "default",
            "icon": "app_resources_logo_logo",
            "data": {
                action: "curefit://webview?url=" + encodeURIComponent(formURL),
                analyticData: analyticData
            }
        }
        return this.deviceBusiness.sendNotification(notification.userId, notificationPayload, tenant)
    }

    createNotification(userId: string, notificationMessage: NotificationMessage, analyticData: NotificationAnalyticData, tenant: Tenant): Promise<boolean> {
        const notificationPayload: NotificationPayload = {
            "title": notificationMessage.title,
            "body": notificationMessage.subTitle,
            "sound": "default",
            "icon": "app_resources_logo_logo",
            "image": notificationMessage.image,
            "data": {
                action: notificationMessage.action,
                analyticData: analyticData
            }
        }
        return this.deviceBusiness.sendNotification(userId, notificationPayload, tenant)
    }

    createDIYReminderNotification(diyReminderNotification: DIYReminderNotification, analyticData: NotificationAnalyticData, tenant: Tenant): Promise<boolean> {
        let actionString = undefined
        if (diyReminderNotification.productType === "DIY_FITNESS") {
            actionString = `curefit://diycultsingles?packId=` + diyReminderNotification.packId
        } else if (diyReminderNotification.productType === "DIY_MEDITATION") {
            actionString = `curefit://mindsingles?packId=` + diyReminderNotification.packId
        }
        if (diyReminderNotification.activityId)
            actionString = actionString + "&activityId=" + diyReminderNotification.activityId + "&activityType=" + diyReminderNotification.productType

        const notificationPayload: NotificationPayload = {
            "title": diyReminderNotification.title,
            "body": diyReminderNotification.subTitle,
            "sound": "default",
            "icon": "app_resources_logo_logo",
            "data": {
                action: actionString,
                analyticData: analyticData
            }
        }
        return this.deviceBusiness.sendNotification(diyReminderNotification.userId, notificationPayload, tenant)
    }

    createPackExpiryNotification(packExpiryNotification: PackExpiryNotification, analyticData: NotificationAnalyticData, tenant: Tenant): Promise<boolean> {
        let actionString = undefined
        if (packExpiryNotification.productType === "FOOD") {
            if (packExpiryNotification.packId) {
                actionString = `curefit://foodpack?packId=` + packExpiryNotification.packId
            } else {
                actionString = `curefit://eatfitclp`
            }
        } else if (packExpiryNotification.productType === "FITNESS") {
            if (packExpiryNotification.packId) {
                this.logger.info("PMS::DEPR Pack expiry notification", {packExpiryNotification})
                actionString = ActionUtil.cultFitPack(packExpiryNotification.packId) // PMS::TODO Migrate to productId
            } else {
                actionString = `curefit://cultclp`
            }
        }
        const notificationPayload: NotificationPayload = {
            "title": packExpiryNotification.title,
            "body": packExpiryNotification.subTitle,
            "sound": "default",
            "icon": "app_resources_logo_logo",
            "data": {
                action: actionString,
                analyticData: analyticData
            }
        }
        return this.deviceBusiness.sendNotification(packExpiryNotification.userId, notificationPayload, tenant)
    }
}

export default NotificationQueueListener
