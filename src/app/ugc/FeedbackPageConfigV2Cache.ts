import { inject, injectable } from "inversify"
import * as _ from "lodash"
import { InMemoryCacheService } from "@curefit/memory-cache"
import { FEEDBACK_MONGO_TYPES, IFeedbackPageConfigV2ReadOnlyDao } from "@curefit/feedback-mongo"
import { Feedback, FeedbackPageConfigV2, FeedbackRating, Rating, Tag, TicketOperation } from "@curefit/feedback-common"
import { ConsultationProduct } from "@curefit/care-common"
import { FoodProduct as Product } from "@curefit/eat-common"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { CultClass } from "@curefit/cult-common"
import { CULT_CLIENT_TYPES, ICultServiceOld as ICultService } from "@curefit/cult-client"
import { CareUtil } from "../util/CareUtil"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService } from "@curefit/diy-client"
import { ALBUS_CLIENT_TYPES, IHealthfaceService } from "@curefit/albus-client"
import { ProductType } from "@curefit/product-common/dist/src/BaseProduct"
import { DIYProductsType } from "@curefit/diy-common"
import { FeedbackQuestion } from "@curefit/feedback-common/dist/src/Feedback"
import { ProductDetails } from "./FeedbackController"
import { GYMFIT_CLIENT_TYPES, IGymfitService } from "@curefit/gymfit-client"
import { Entity, FeedbackQuestionSearchRequest, GymfitEntityPaginationResponse } from "@curefit/gymfit-common"
import { ISportsApi, SPORT_API_CLIENT_TYPES } from "@curefit/sports-api-client-node"
import { UserContext } from "@curefit/userinfo-common"
import { HamletConfigRequest } from "@curefit/hamlet-common"
import { Tenant } from "@curefit/user-common"
import { HAMLET_CLIENT_TYPES, IHamletService } from "@curefit/hamlet-client"
import { userInfo } from "os"
import AppUtil from "../util/AppUtil"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"

const clone = require("clone")

export interface AdditionalFeedbackQuestions {
    filterId: string,
    questions: FeedbackQuestion[],
    ratings?: FeedbackRating[],
    isReviewEnabled?: boolean,
    title?: string,
}

export interface FeedbackQuestions {
    id: number,
    userId: string,
    bookingId: string,
    feedbackObject: AdditionalFeedbackQuestions[]
}

export interface FeedbackConfig {
    ratings: FeedbackRating[],
    question: string,
    additionalFeedbackQuestions?: AdditionalFeedbackQuestions[],
}

@injectable()
class FeedbackPageConfigV2Cache extends InMemoryCacheService<Map<string, FeedbackPageConfigV2>> {

    constructor(
        @inject(FEEDBACK_MONGO_TYPES.FeedbackPageConfigV2ReadOnlyDao) private feedbackPageConfigDao: IFeedbackPageConfigV2ReadOnlyDao,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
        @inject(CULT_CLIENT_TYPES.MindFitService) private mindFitService: ICultService,
        @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) private diyFulfilmentService: IDIYFulfilmentService,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(ALBUS_CLIENT_TYPES.CultPersonalTrainingService) public cultPTService: IHealthfaceService,
        @inject(GYMFIT_CLIENT_TYPES.GymfitService) private gymfitService: IGymfitService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(HAMLET_CLIENT_TYPES.HamletService) private hamletService: IHamletService,
        @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
    ) {
        super(logger, 33 * 60)
        this.load("FeedbackPageConfigV2Cache")
    }

    protected trainerBTTags = [
        "Trainer Not available",
        "Trainer Not interested to help",
        "Trainer behaviour",
        "Trainer unclear instruction"
    ]

    protected trainerAGTags = [
        "Trainer skills and knowledge'",
        "Time spent by trainer",
        "Clarity of instructions",
        "Posture correction",
        "Workout plan"
    ]

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    async loadData(): Promise<Map<string, FeedbackPageConfigV2>> {
        const feedbackPageConfigs: FeedbackPageConfigV2[] = await this.feedbackPageConfigDao.retrieve()
        const FILTER_ID_TO_FEEDBACK_CONFIG_MAP = new Map<string, FeedbackPageConfigV2>()
        this.logger.info("Feedback config fetched for :" + JSON.stringify(feedbackPageConfigs?.map( config => config.filterId )))
        feedbackPageConfigs.forEach(feedbackPageConfig => {
            FILTER_ID_TO_FEEDBACK_CONFIG_MAP.set(feedbackPageConfig.filterId, feedbackPageConfig)
        })
        return FILTER_ID_TO_FEEDBACK_CONFIG_MAP
    }

    public getRatings(id: string): FeedbackRating[] {
        if (!this.cache.has(id)) return undefined
        return this.cache.get(id).ratings
    }

    public getQuestion(id: string): string {
        if (!this.cache.has(id)) return undefined
        return this.cache.get(id).feedbackQuestion
    }

    public getFeedbackObj(id: string): FeedbackConfig {
        const feedbackObj: FeedbackConfig = {
            ratings: undefined,
            question: "",
            additionalFeedbackQuestions: [],
        }
        try {
            feedbackObj.question = this.cache.get(id).feedbackQuestion
            feedbackObj.ratings = this.cache.get(id).ratings
            if (this.cache.get(id)?.questions?.length > 0) {
                feedbackObj.additionalFeedbackQuestions = [{
                    filterId: id,
                    questions: this.cache.get(id).questions,
                }]
            }
        } catch (e) {
            this.logger.error("SUPPRESS::Feedback config breaking for CONFIG KEY: " + id)
        }

        return clone(feedbackObj)
    }

    public getTriggerTicketTags(id: string, rating: Rating): any {
        const result: any = {}
        const feedbackRatings: FeedbackRating[] = this.getRatings(id)
        for (let i = 0; i < feedbackRatings.length; i++) {
            const feedbackRating: FeedbackRating = feedbackRatings[i]
            if (feedbackRating.rating === rating) {
                for (let j = 0; j < feedbackRating.tags.length; j++) {
                    const tag: Tag = feedbackRating.tags[j]
                    if (tag.triggerTicket === TicketOperation.FORCE) {
                        result[tag.text] = true
                    }
                }
                break
            }
        }
        return result
    }

    public async getRatingsV2(feedback: Feedback): Promise<FeedbackRating[]> {
        let feedbackRatings: FeedbackRating[] = undefined
        if (_.isNil(feedback)) return feedbackRatings
        switch (feedback.productType) {
            case "FOOD":
                feedbackRatings = (await this.getFoodFeedbackObjV2(feedback)).ratings
                break
            case "FOOD_MARKETPLACE":
                feedbackRatings = (await this.getFoodMarketplaceFeedbackObjV2(feedback)).ratings
                break
            case "FITNESS":
                feedbackRatings = (await this.getFitnessFeedbackObjV2(feedback)).ratings
                break
            case "MIND":
                feedbackRatings = (await this.getMindFeedbackObjV2(feedback)).ratings
                break
            case "CF_ONLINE_CONSULTATION":
            case "CF_INCENTRE_CONSULTATION":
                feedbackRatings = (await this.getCareConsultationFeedbackObjV2(feedback)).ratings
                break
            case "GROUP_CLASS":
                feedbackRatings = (await this.getSugarfitGroupClassFeedbackObjV2(feedback)).ratings
                break
            case "SUGARFIT_MASTER_CLASS":
                feedbackRatings = (await this.getSugarfitMasterClassFeedbackObjV2(feedback)).ratings
                break
            case "SUGARFIT_FITNESS_CLASS":
                feedbackRatings = (await this.getSugarfitFitnessClassFeedbackObjV2(feedback)).ratings
                break
            case "GEAR":
                feedbackRatings = (await this.getGearFeedbackObj(feedback)).ratings
                break
            case "GYMFIT_FITNESS_PRODUCT":
                feedbackRatings = (await this.getGymfitFeedbackObj(feedback)).ratings
                break
            case "LIVE":
            case "LIVE_VIDEO_CALL":
                feedbackRatings = (await this.getLiveSessionFeedbackObj(feedback)).ratings
                break
            case "DIY_FITNESS":
            case "DIY_MEDITATION":
                feedbackRatings = (await this.getDIYSessionFeedbackObj(feedback)).ratings
                break
            case "GYM_PT_PRODUCT":
            case "GYM_PT_PPC_PRODUCT":
                feedbackRatings = (await this.getGymPtFeedbackObj(feedback)).ratings
            case "PLAY":
                feedbackRatings = (await this.getPlayFeedbackObj(feedback)).ratings
            default:
                feedbackRatings = this.getRatings(feedback.productType)
                break
        }
        return clone(feedbackRatings)
    }

    public async getQuestionV2(feedback: Feedback): Promise<string> {
        let question: string = undefined
        if (_.isNil(feedback)) return ""
        switch (feedback.productType) {
            case "FOOD":
                question = (await this.getFoodFeedbackObjV2(feedback)).question
                break
            case "FOOD_MARKETPLACE":
                question = (await this.getFoodMarketplaceFeedbackObjV2(feedback)).question
                break
            case "FITNESS":
                question = (await this.getFitnessFeedbackObjV2(feedback)).question
                break
            case "MIND":
                question = (await this.getMindFeedbackObjV2(feedback)).question
                break
            case "CF_ONLINE_CONSULTATION":
            case "CF_INCENTRE_CONSULTATION":
                question = (await this.getCareConsultationFeedbackObjV2(feedback)).question
                break
            case "GROUP_CLASS":
                question = (await this.getSugarfitGroupClassFeedbackObjV2(feedback)).question
                break
            case "SUGARFIT_MASTER_CLASS":
                question = (await this.getSugarfitMasterClassFeedbackObjV2(feedback)).question
                break
            case "SUGARFIT_FITNESS_CLASS":
                question = (await this.getSugarfitFitnessClassFeedbackObjV2(feedback)).question
                break
            case "GYMFIT_FITNESS_PRODUCT":
                question = (await this.getGymfitFeedbackObj(feedback)).question
                break
            case "FITNESS_REPORT":
            case "LIVE_SESSION_REPORT":
                question = "DID YOU FIND IT USEFUL"
                break
            case "LIVE":
            case "LIVE_VIDEO_CALL":
                question = (await this.getLiveSessionFeedbackObj(feedback)).question
                break
            case "DIY_FITNESS":
            case "DIY_MEDITATION":
                question = (await this.getDIYSessionFeedbackObj(feedback)).question
                break
            case "GYM_PT_PRODUCT":
            case "GYM_PT_PPC_PRODUCT":
                question = (await this.getGymPtFeedbackObj(feedback)).question
            case "PLAY":
                question = (await this.getPlayFeedbackObj(feedback)).question
            default:
                question = this.getQuestion(feedback.productType)
                break
        }
        return question
    }

    public async getFeedbackObjV2(feedback: Feedback, productDetails?: ProductDetails, userContext?: UserContext): Promise<FeedbackConfig> {
        let feedbackObj: FeedbackConfig = { ratings: undefined, question: "" }
        if (_.isNil(feedback)) return feedbackObj
        switch (feedback.productType) {
            case "FOOD":
                feedbackObj = await this.getFoodFeedbackObjV2(feedback)
                break
            case "FOOD_MARKETPLACE":
                feedbackObj = await this.getFoodMarketplaceFeedbackObjV2(feedback)
                break
            case "FITNESS":
                feedbackObj = await this.getFitnessFeedbackObjV2(feedback, productDetails, userContext)
                break
            case "MIND":
                feedbackObj = await this.getMindFeedbackObjV2(feedback)
                break
            case "CF_ONLINE_CONSULTATION":
            case "CF_INCENTRE_CONSULTATION":
                feedbackObj = await this.getCareConsultationFeedbackObjV2(feedback)
                break
            case "GROUP_CLASS":
                feedbackObj = (await this.getSugarfitGroupClassFeedbackObjV2(feedback))
                break
            case "SUGARFIT_MASTER_CLASS":
                feedbackObj = (await this.getSugarfitMasterClassFeedbackObjV2(feedback))
                break
            case "PHLEBOTOMIST_TASK":
                feedbackObj = await this.getPhlebotomistTaskFeedbackObj(feedback)
                break
            case "GEAR":
                feedbackObj = await this.getGearFeedbackObj(feedback)
                break
            case "GYMFIT_FITNESS_PRODUCT":
                feedbackObj = await this.getGymfitFeedbackObj(feedback)
                break
            case "LIVE":
            case "LIVE_VIDEO_CALL":
                feedbackObj = await this.getLiveSessionFeedbackObj(feedback)
                break
            case "DIY_FITNESS":
            case "DIY_MEDITATION":
                feedbackObj = await this.getDIYSessionFeedbackObj(feedback)
                break
            case "GYM_PT_PRODUCT":
            case "GYM_PT_PPC_PRODUCT":
                feedbackObj = await this.getGymPtFeedbackObj(feedback)
                break
            case "PLAY":
                feedbackObj = await this.getPlayFeedbackObj(feedback)
                break
            default:
                feedbackObj = this.getFeedbackObj(feedback.productType)
                break
        }
        return clone(feedbackObj)
    }

    public async getTriggerTicketTagsV2(feedback: Feedback): Promise<any> {
        const result: any = {}
        if (_.isNil(feedback)) return result
        const rating = feedback.rating
        const feedbackRatings: FeedbackRating[] = await this.getRatingsV2(feedback)
        for (let i = 0; i < feedbackRatings.length; i++) {
            const feedbackRating: FeedbackRating = feedbackRatings[i]
            if (feedbackRating.rating === rating) {
                for (let j = 0; j < feedbackRating.tags.length; j++) {
                    const tag: Tag = feedbackRating.tags[j]
                    if (tag.triggerTicket === TicketOperation.FORCE) {
                        result[tag.text] = true
                    }
                }
                break
            }
        }
        return result
    }

    private async getFoodFeedbackObjV2(feedback: Feedback): Promise<FeedbackConfig> {
        let feedbackObj: {
            ratings: FeedbackRating[],
            question: string
        } = {
            ratings: undefined,
            question: ""
        }
        const foodProduct: Product = await this.catalogueService.getProduct(feedback.itemId)
        if (!_.isNil(foodProduct)) {
            feedbackObj = this.getFeedbackObj(foodProduct.productId) // 1st priority -- product ID
            if (_.isNil(feedbackObj.question) || feedbackObj.question === "") {
                feedbackObj = this.getFeedbackObj(foodProduct.categoryId) // 2nd priority -- category ID
            }
        }
        if (_.isNil(feedbackObj.question) || feedbackObj.question === "") {
            feedbackObj = this.getFeedbackObj(feedback.productType) // 3rd priority -- vertical ID (FOOD, FITNESS, etc)
        }
        return feedbackObj
    }

    private async getFoodMarketplaceFeedbackObjV2(feedback: Feedback): Promise<FeedbackConfig> {
        let feedbackObj: {
            ratings: FeedbackRating[],
            question: string
        } = {
            ratings: undefined,
            question: ""
        }
        const foodProduct: Product = await this.catalogueService.getProduct(feedback.itemId)
        if (!_.isNil(foodProduct)) {
            feedbackObj = this.getFeedbackObj(foodProduct.productId) // 1st priority -- product ID
            if (_.isNil(feedbackObj.question) || feedbackObj.question === "") {
                feedbackObj = this.getFeedbackObj(foodProduct.categoryId) // 2nd priority -- category ID
            }
        }
        if (_.isNil(feedbackObj.question) || feedbackObj.question === "") {
            feedbackObj = this.getFeedbackObj(feedback.productType) // 3rd priority -- vertical ID (FOOD, FITNESS, etc)
        }
        return feedbackObj
    }

    private getNewIcons(icon: string) {
        switch (icon) {
            case "bad_rating" : return "terrible_rating"
            case "terrible_rating" : return "angry_rating"
            default : return icon
        }
        return icon
    }

    private getNewEmotionDetail(rating: FeedbackRating) {
        switch (rating.rating) {
            case "GOOD" : return "Liked it, but can improve further"
            case "AVERAGE" : return "It was fine, but could have been better"
            default : return rating.emotionDetail
        }
        return rating.emotionDetail
    }

private getNewRatingsForExperiment(feedbackObj: FeedbackConfig) {
        let goodRating: FeedbackRating
        feedbackObj.ratings.map((entry) => {
            if (entry.rating === "GOOD") goodRating = entry
            const newRating: FeedbackRating = entry
            newRating.icon = this.getNewIcons(entry.icon)
            newRating.emotionDetail = this.getNewEmotionDetail(entry)
            return newRating
        })
        if (feedbackObj.ratings?.length === 4) {
            const fineRating: FeedbackRating = _.cloneDeep(goodRating)
            fineRating.icon = "bad_rating"
            fineRating.rating = "AVERAGE"
            fineRating.emotionDetail = this.getNewEmotionDetail(fineRating)
            feedbackObj.ratings.splice(2, 0, fineRating)
        }
        return feedbackObj
    }

    private async getFitnessFeedbackObjV2(feedback: Feedback, productDetails?: ProductDetails, userContext?: UserContext): Promise<FeedbackConfig> {
        let feedbackObj: FeedbackConfig = {
            ratings: undefined,
            question: "",
            additionalFeedbackQuestions: []
        }
        const response: { attendanceCount: number } = await this.cultFitService.getAttendanceRankForBooking(feedback.shipmentId, feedback.userId)
        // for nux
        if (response.attendanceCount === 1) {
            feedbackObj = this.getFeedbackObj("NUX")
        } else {
            const cultClass: CultClass = await this.cultFitService.getCultClass(feedback.itemId, feedback.userId)
            if (!_.isNil(cultClass)) {
                feedbackObj = this.getFeedbackObj("CULTWORKOUT" + cultClass.workoutID) // 1st priority -- workout ID
                // hack to show Pulse Tag for pulse enabled classes
                if (cultClass.isPulseEnabled) {
                    feedbackObj.ratings = _.map(feedbackObj.ratings, (rating: FeedbackRating) => {
                        rating.tags.push({
                            text: "Pulse",
                            triggerTicket: TicketOperation.OPT
                        })
                        return rating
                    })
                }
            }
        }
        if (_.isNil(feedbackObj.question) || feedbackObj.question === "") {
            feedbackObj = this.getFeedbackObj(feedback.productType) // 2nd priority -- vertical ID (FOOD, FITNESS, etc)
        }

        const feedbackQuestionSearchRequest: FeedbackQuestionSearchRequest = {
            userIds: [feedback.userId],
            // for testing - Add this - bookingIds: ["5562046"],
            bookingIds: [feedback.shipmentId],
            tenantIds: [2]
        }
        this.logger.debug("feedback quesion search request to gymfit", JSON.stringify(feedbackQuestionSearchRequest))
        const additionalResponse: GymfitEntityPaginationResponse<FeedbackQuestions> = await this.gymfitService.filterEntity(Entity.FEEDBACK_QUESTIONS, feedbackQuestionSearchRequest)
        this.logger.debug("feedback questions from gymfit", JSON.stringify(additionalResponse))
        const feedbackQuestions: FeedbackQuestions[] = additionalResponse.result
        if (feedbackQuestions.length > 0) {
            feedbackObj.additionalFeedbackQuestions.push(...feedbackQuestions[0].feedbackObject)
        }
        this.logger.debug("feedback questions array", JSON.stringify(feedbackObj))

        if (productDetails && productDetails.isCMReviewEnable && productDetails.cmName) {
            const cmFeedbackQuestions = this.getFeedbackObj("CULT_CM_FEEDBACK_QUESTIONS")
            if (cmFeedbackQuestions?.additionalFeedbackQuestions[0]?.questions?.length > 0) {
                const cmFeedbackQuestionArray = cmFeedbackQuestions.additionalFeedbackQuestions[0].questions.map((feedbackQuestion) => {
                    const questionText = feedbackQuestion.question.replace("${cmName}", productDetails.cmName)
                    return {
                        ...feedbackQuestion,
                        question: questionText,
                        header: {
                            title: productDetails.cmName,
                            subTitle: "Centre Manager",
                            imageUrl: productDetails?.cmImageUrl || "image/icons/cult/profile-placeholder.png",
                        },
                    }
                })
                feedbackObj.additionalFeedbackQuestions.push({
                    ...cmFeedbackQuestions,
                    filterId: "CULT_CM_FEEDBACK_QUESTIONS",
                    title: "We would really appreciate if you could share some feedback for our center manager too.",
                    questions: cmFeedbackQuestionArray,
                })
            }
        }
        this.logger.debug("feedback questions final return", JSON.stringify(feedbackObj))

        if (userContext != null && await AppUtil.isExtraRatingEnabled(userContext, this.hamletBusiness)) {
            this.logger.info(`user part of experiment : ${userContext.userProfile.userId}`)
            feedbackObj = this.getNewRatingsForExperiment(feedbackObj)
        }

        return feedbackObj
    }

    private async getMindFeedbackObjV2(feedback: Feedback): Promise<FeedbackConfig> {
        let feedbackObj: {
            ratings: FeedbackRating[],
            question: string
        } = {
            ratings: undefined,
            question: ""
        }
        const mindClass: CultClass = await this.mindFitService.getCultClass(feedback.itemId, feedback.userId)
        if (!_.isNil(mindClass)) {
            feedbackObj = this.getFeedbackObj("MINDWORKOUT" + mindClass.workoutID) // 1st priority -- workout ID
        }
        if (_.isNil(feedbackObj.question) || feedbackObj.question === "") {
            feedbackObj = this.getFeedbackObj(feedback.productType) // 2nd priority -- vertical ID (FOOD, FITNESS, etc)
        }
        return feedbackObj
    }

    private async getGymfitFeedbackObj(feedback: Feedback): Promise<FeedbackConfig> {
        const feedbackQuestionSearchRequest: FeedbackQuestionSearchRequest = {
            userIds: [feedback.userId],
            bookingIds: [feedback.shipmentId],
            tenantIds: [1]
        }
        const response: GymfitEntityPaginationResponse<FeedbackQuestions> = await this.gymfitService.filterEntity(Entity.FEEDBACK_QUESTIONS, feedbackQuestionSearchRequest)
        let feedbackObj: FeedbackConfig = { ratings: undefined, question: "" }
        if (!_.isNil(feedback.itemId)) {
            feedbackObj = this.getFeedbackObj("GYMFIT_CENTER_" + feedback.itemId)
        }
        if (_.isEmpty(feedback.itemId) || _.isNil(feedbackObj.question) || feedbackObj.question === "") {
            feedbackObj = this.getFeedbackObj("GYMFIT_CENTER")
        }
        // try {
        //     feedbackObj.ratings.map((e) => _.map(e.tags, (tag: Tag) => {
        //         if (tag.text.toUpperCase() == "TRAINER") {
        //             if (e.rating === "GOOD" || e.rating === "AWESOME")
        //                 this.trainerAGTags.map((tagText: any) => {
        //                     tag.subTags.push({
        //                         text: tagText,
        //                         triggerTicket: TicketOperation.NONE,
        //                         subTags: []
        //                     })
        //                 })
        //             else if (e.rating === "BAD" || e.rating === "TERRIBLE")
        //                 this.trainerBTTags.map((tagText: any) => {
        //                     tag.subTags.push({
        //                         text: tagText,
        //                         triggerTicket: TicketOperation.NONE,
        //                         subTags: []
        //                     })
        //                 })
        //         }
        //     }))
        // } catch (e) {
        //     this.serviceInterfaces.rollbarService.sendError(e)
        // }
        const feedbackQuestions: FeedbackQuestions[] = response.result
        if (feedbackQuestions.length > 0) {
            feedbackObj.additionalFeedbackQuestions.push(...feedbackQuestions[0].feedbackObject)
        }
        return feedbackObj
    }

    private async getGymPtFeedbackObj(feedback: Feedback): Promise<FeedbackConfig> {
        if (!_.isNil(feedback.shipmentId)) {
            return  this.getFeedbackObj(feedback.productType)
        }
    }

    private async getPlayFeedbackObj(feedback: Feedback): Promise<FeedbackConfig> {
        if (!_.isNil(feedback.shipmentId)) {
            if (feedback.workout != null)
                return  this.getFeedbackObj(feedback.workout.toUpperCase())
            else
                return  this.getFeedbackObj(feedback.productType)

        }
    }

    private async getPhlebotomistTaskFeedbackObj(feedback: Feedback): Promise<FeedbackConfig> {
        const key = `PHLEBOTOMIST_TASK_${feedback.itemId}`
        this.logger.debug(`Fetching phlebotomist feedback config for ${key}`)
        return this.getFeedbackObj(key)
    }

    private async getSugarfitMasterClassFeedbackObjV2(feedback: Feedback): Promise<FeedbackConfig> {
        if (feedback.subCategoryCode === "MASTER-CLASS-DOCTOR") {
            return this.getFeedbackObj("SUGARFIT_MASTER_CLASS_DOCTOR")
        } else if (feedback.subCategoryCode === "MASTER-CLASS-NUTRITION") {
            return this.getFeedbackObj("SUGARFIT_MASTER_CLASS_COACH")
        } else if (feedback.subCategoryCode === "MASTER-CLASS-JUICE") {
            return this.getFeedbackObj("SUGARFIT_MASTER_CLASS_CONSUMABLE")
        } else {
            return this.getFeedbackObj("SUGARFIT_MASTER_CLASS_FITNESS")
        }
    }

    private async getSugarfitGroupClassFeedbackObjV2(feedback: Feedback): Promise<FeedbackConfig> {
        return this.getFeedbackObj("SUGARFIT_GROUP_CLASS")
    }

    private async getSugarfitFitnessClassFeedbackObjV2(feedback: Feedback): Promise<FeedbackConfig> {
        return this.getFeedbackObj("SUGARFIT_FITNESS_CLASS")
    }

    private async getCareConsultationFeedbackObjV2(feedback: Feedback): Promise<FeedbackConfig> {
        const consultationProduct: ConsultationProduct = <ConsultationProduct>await this.catalogueService.getProduct(feedback.itemId)
        if (consultationProduct.doctorType === "LC" || consultationProduct.doctorType === "SF_PSYCHIATRIST") {
            return this.getFeedbackObj(`${feedback.productType}_LC`)
        } else if (consultationProduct.tenant === "SUGARFIT") {
            if (consultationProduct.doctorType === "GP") {
                return this.getFeedbackObj("SUGARFIT_DOCTOR_CONSULTATION")
            } else if (consultationProduct.doctorType === "SF_VR_TRAINER"  || consultationProduct.doctorType === "SF_MUSIC_TRAINER"
                || consultationProduct.doctorType === "SF_MASSEUSE" || consultationProduct.doctorType === "SF_BCA_TRAINER"
                || consultationProduct.doctorType === "SF_BODY_TONING") {
                return this.getFeedbackObj("SUGARFIT_OFFLINE_THERAPY")
            }
        } else if (["MIND_THERAPIST", "COVID_THERAPIST", "SLEEP_THERAPIST"].includes(consultationProduct.doctorType)) {
            return this.getFeedbackObj("THERAPIST_CONSULTATION")
        } else if (consultationProduct.doctorType === "ANXIETY_THERAPIST") {
            return this.getFeedbackObj("EFA_CONSULTATION")
        } else if (consultationProduct.doctorType === "PSYCHIATRIST") {
            return this.getFeedbackObj("PSYCHIATRIST_CONSULTATION")
        } else if (CareUtil.isPTDoctorType(consultationProduct.doctorType)) {
            return this.getFeedbackObj("CULT_PT_CONSULTATION")
        } else if (CareUtil.isLivePTDoctorType(consultationProduct.doctorType)) {
            const appointmentId = feedback.shipmentId.split("-")[1]
            const isFirstConsultation = await this.cultPTService.isFirstConsultation("LIVE_PERSONAL_TRAINING", Number(appointmentId))
            if (isFirstConsultation) {
                return this.getFeedbackObj("LIVE_PT_FIRST_CONSULTATION")
            }
            return this.getFeedbackObj("LIVE_PT_CONSULTATION")
        } else if (CareUtil.isLiveSGTDoctorType(consultationProduct.doctorType)) {
            return this.getFeedbackObj("LIVE_SGT_CONSULTATION")
        } else if (consultationProduct.familyType && consultationProduct.familyType === "PROCEDURE") {
            return this.getFeedbackObj("PROCEDURE")
        } else {
            return this.getFeedbackObj(feedback.productType)
        }
    }

    private async getGearFeedbackObj(feedback: Feedback): Promise<FeedbackConfig> {
        let feedbackObj: {
            ratings: FeedbackRating[],
            question: string
        } = {
            ratings: undefined,
            question: ""
        }

        /**
         * GEAR products have multiple levels of feedback e.g. one 3 days after delivery (overall order feedback),
         * and another 15 days after delivery (aimed to get feedback about products)
         */
        const filterKey = feedback.productType + "_" + feedback.feedbackType
        feedbackObj = this.getFeedbackObj(filterKey)
        if (_.isEmpty(feedbackObj.question)) {
            this.rollbarService.sendError(new Error("ERROR:: Not able to find feedback config for feedbackId:" + feedback.feedbackId + "and key:" + filterKey))
            this.logger.error("ERROR:: Not able to find feedback config for feedbackId:" + feedback.feedbackId + "and key:" + filterKey)
            if (_.includes(feedback.feedbackType, "DELIVERY")) {
                feedbackObj.ratings = this.cache.get("GEAR_AFTER_DELIVERY").ratings
                feedbackObj.question = this.cache.get("GEAR_AFTER_DELIVERY").feedbackQuestion
            } else if (_.includes(feedback.feedbackType, "USE")) {
                feedbackObj.ratings = this.cache.get("GEAR_AFTER_USE").ratings
                feedbackObj.question = this.cache.get("GEAR_AFTER_USE").feedbackQuestion
            }
        }
        return feedbackObj
    }

    private async getLiveSessionFeedbackObj(feedback: Feedback): Promise<FeedbackConfig> {
        let feedbackObj: FeedbackConfig = { ratings: undefined, question: "" }
        const liveClass = await this.diyFulfilmentService.getDigitalCatalogueEntry(feedback.itemId)
        if (!_.isNil(liveClass) && !_.isNil(liveClass.format)) {
            if (feedback.productType === "LIVE") {
                feedbackObj = this.getFeedbackObj("LIVE_" + liveClass.format)
            } else if (feedback.productType === "LIVE_VIDEO_CALL") {
                feedbackObj = this.getFeedbackObj("LIVE_VIDEO_CALL_" + liveClass.format)
                if (_.isNil(feedbackObj.question) || feedbackObj.question === "") {
                    feedbackObj = this.getFeedbackObj("LIVE_VIDEO_CALL")
                }
            }
        }
        if (_.isEmpty(feedback.itemId) || _.isNil(feedbackObj.question) || feedbackObj.question === "") {
            feedbackObj = this.getFeedbackObj("LIVE")
        }
        return feedbackObj
    }

    private async getDIYSessionFeedbackObj(feedback: Feedback): Promise<FeedbackConfig> {
        let feedbackObj: FeedbackConfig = { ratings: undefined, question: "" }
        const diyProducts: DIYProductsType[]  = await this.diyFulfilmentService.getDIYProductsByIds([feedback.itemId], feedback.productType as ProductType)
        const diyProduct: DIYProductsType = !_.isEmpty(diyProducts) && diyProducts[0]
        if (!_.isNil(diyProduct) && !_.isNil(diyProduct.format)) {
            feedbackObj = this.getFeedbackObj("DIY_" + diyProduct.format)
        }
        if (_.isEmpty(feedback.itemId) || _.isNil(feedbackObj.question) || feedbackObj.question === "") {
            feedbackObj = this.getFeedbackObj(feedback.productType)
        }
        return feedbackObj
    }
}

export default FeedbackPageConfigV2Cache
