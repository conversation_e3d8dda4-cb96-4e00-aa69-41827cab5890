import { inject, injectable } from "inversify"
import { DEFAULT_CACHE_REFRESH_TIME_IN_SECONDS, InMemoryCacheService } from "@curefit/memory-cache"
import { IPageConfigReadOnlyDao, IPageConfigReadWriteDao, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import { FeedbackProductType, FeedbackRating, Rating, Tag, TicketOperation } from "@curefit/feedback-common"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

@injectable()
class FeedbackPageConfig extends InMemoryCacheService<any> {

    public data: any
    constructor(
        @inject(PAGE_CONFIG_TYPES.PageConfigReadOnlySecondaryDao) protected pageConfigDao: IPageConfigReadOnlyDao,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger, 36 * 60)
        this.load("FeedbackPageConfig")
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    loadData(): Promise<any> {
        return this.pageConfigDao.findOne({ pageId: { $eq: "FeedbackPageConfig" } }).then(pageConfig => {
            this.data = pageConfig.data
        })
    }

    public getRatings(type: FeedbackProductType): FeedbackRating[] {
        return this.data[type].ratings
    }

    public getQuestion(type: FeedbackProductType): string {
        return this.data[type].feedbackQuestion
    }

    public getTriggerTicketTags(type: FeedbackProductType, rating: Rating): any {
        const result: any = {}
        const feedbackRatings: FeedbackRating[] = this.getRatings(type)
        for (let i = 0; i < feedbackRatings.length; i++) {
            const feedbackRating: FeedbackRating = feedbackRatings[i]
            if (feedbackRating.rating === rating) {
                for (let j = 0; j < feedbackRating.tags.length; j++) {
                    const tag: Tag = feedbackRating.tags[j]
                    if (tag.triggerTicket === TicketOperation.FORCE) {
                        result[tag.text] = true
                    }
                }
                break
            }
        }
        return result
    }
}

export default FeedbackPageConfig
