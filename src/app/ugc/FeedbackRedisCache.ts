import { injectable, inject } from "inversify"
import { <PERSON><PERSON><PERSON>, BASE_TYPES } from "@curefit/base"
import { <PERSON><PERSON><PERSON><PERSON>eyValue, REDIS_TYPES } from "@curefit/redis-utils"
import { Feedback } from "@curefit/feedback-common"
import * as _ from "lodash"
import { TimeUtil } from "@curefit/util-common"

const ACTIVE_FEEDBACKS_REDIS_KEY: string = "activeFeedbacks"
const FEEDBACK_DATA_STORE_REDIS_KEY: string = "feedbackStore"
const FEEDBACK_LAST_SUBMIT_TIME_KEY: string = "lastSubmittedFeedbackTimestamp"

// MAX_FEEDBACK_REDIS_TTL_SECONDS is set to 2 days as of now because as per current logic only those feedbacks
// which are less than 1 day old can be shown to a user. Extra day added in TTL just for buffer.
const MAX_FEEDBACK_REDIS_TTL_SECONDS: number = TimeUtil.TIME_IN_SECONDS.DAY * 2

export interface IFeedbackRedisCache {
    getAllActiveFeedbacksForUser(userId: string): Promise<Feedback[]>
    storeFeedback(feedback: Feedback): Promise<boolean>
    purgeAllFeedbacksForUser(userId: string): Promise<boolean>
    purgeActiveFeedbackForUser(feedbackId: string, userId: string): Promise<boolean>
    getFeedbackById(feedbackId: string): Promise<Feedback>
    getLastSubmittedFeedbackTimestamp(userId: string): Promise<number>
    setLastSubmittedFeedbackTimestamp(userId: string, timestamp: number): Promise<boolean>
}

/**
 * @description There are 3 different caches being maintained for feedbacks.
 * 1. feedback-store cache: Stores the entire feedback details. Any feedback can be retrieved via its ID.
 * 2. user-feedback cache (active feedbacks): Stores the feedback IDs in a hash set against a user ID. These are the active
 *    feedbacks which are possible candidates for being shown to the user.
 * 3. last-feedback-submission-time: Stores the latest timestamp for a particular user when he had submitted a feedback
 */
@injectable()
export class FeedbackRedisCache implements IFeedbackRedisCache {

    constructor(
        @inject(REDIS_TYPES.RedisDao) private redisDao: ICrudKeyValue,
        @inject(BASE_TYPES.ILogger) private logger: ILogger
    ) {
    }

    /**
     * @description All possible candidates of feedbacks which can be shown to the user are returned from this function
     * It first checks in the user-feedback cache to get the list of possible feedbackId.
     * Then it hits the feedback-store cache to get the actual content of the feedback.
     */
    public async getAllActiveFeedbacksForUser(userId: string): Promise<Feedback[]> {
        const activeFeedbackIdList: string[] = await this.redisDao.getMembersOfSet(this.geActiveFeedbacksTokenKey(userId))
        if (_.isEmpty(activeFeedbackIdList)) return []
        const activeFeedbackList: Feedback[] = await this.retrieveMultipleFeedbacks(activeFeedbackIdList)
        return activeFeedbackList
    }

    /**
     * @description Stores the feedback in the feedback-store cache. Also stores the feedbackId in the
     * user-feedback cache
     */
    public async storeFeedback(feedback: Feedback): Promise<boolean> {
        this.logger.info(`Storing feedback in Redis`, { feedback: feedback })
        const redisStoragePromises: Promise<any>[] = [
            this.redisDao.upsertWithExpiry(
                this.getFeedbackDataStoreTokenKey(feedback.feedbackId),
                JSON.stringify(feedback),
                MAX_FEEDBACK_REDIS_TTL_SECONDS
            ),
            this.redisDao.addToSet(
                this.geActiveFeedbacksTokenKey(feedback.userId),
                feedback.feedbackId
            )
        ]
        await Promise.all(redisStoragePromises)
        this.redisDao.setExpiry(this.geActiveFeedbacksTokenKey(feedback.userId), MAX_FEEDBACK_REDIS_TTL_SECONDS)
        return true
    }

    /**
     * @description Removes all entries from the user-feedback cache for a particular user
     */
    public async purgeAllFeedbacksForUser(userId: string): Promise<boolean> {
        this.logger.info(`Purging all feedbacks in Redis for user Id: ${userId}`)
        return this.redisDao.delete(
            this.geActiveFeedbacksTokenKey(userId)
        )
    }

    /**
     * @description Removes a single feedbackId from a user's user-feedback-cache
     */
    public async purgeActiveFeedbackForUser(feedbackId: string, userId: string): Promise<boolean> {
        this.logger.info(`Removing feedback in Redis for user Id: ${userId}, feedbackId: ${feedbackId}`)
        return this.redisDao.removeFromSet(
            this.geActiveFeedbacksTokenKey(userId),
            feedbackId
        )
    }

    /**
     * @description Gets the entire feedback details from the feedback-store cache
     */
    public async getFeedbackById(feedbackId: string): Promise<Feedback> {
        const feedbackStr: string = await this.redisDao.read(this.getFeedbackDataStoreTokenKey(feedbackId))
        if (!_.isString(feedbackStr)) {
            return undefined
        }
        return this.parseFeedbackJsonString(feedbackStr)
    }

    /**
     * @description Gets the latest timestamp when a particular user had submitted a feedback.
     * If the timestamp is older than 6 hrs then 0 will be returned because TTL against this key is for 6hrs
     */
    public async getLastSubmittedFeedbackTimestamp(userId: string): Promise<number> {
        const lastSubmitTimestampStr: string = await this.redisDao.read(this.geLastSubmittedFeedbackTokenKey(userId))
        if (_.isString(lastSubmitTimestampStr)) {
            return Number(lastSubmitTimestampStr)
        } else {
            return 0
        }
    }

    /**
     * @description Sets the latest timestamp when a particular user had submitted a feedback. TTL is set to 6hrs as of now.
     */
    public async setLastSubmittedFeedbackTimestamp(userId: string, timestamp: number): Promise<boolean> {
        return this.redisDao.createWithExpiry(
            this.geLastSubmittedFeedbackTokenKey(userId),
            `${timestamp}`,
            6 * TimeUtil.TIME_IN_SECONDS.HOUR // TTL of 6 hours
        )
    }

    private async retrieveMultipleFeedbacks(feedbackIdList: string[]): Promise<Feedback[]> {
        const feedbackPromises: Promise<Feedback>[] = []
        feedbackIdList.forEach((feedbackId: string) => {
            feedbackPromises.push(this.getFeedbackById(feedbackId))
        })
        await Promise.all(feedbackPromises)
        const result: Feedback[] = []
        for (let i = 0; i < feedbackPromises.length; i++) {
            const feedback: Feedback = await feedbackPromises[i]
            if (!_.isNil(feedback)) result.push(feedback)
        }
        return result
    }

    private geActiveFeedbacksTokenKey(userId: string): string {
        return `${ACTIVE_FEEDBACKS_REDIS_KEY}:${userId}`
    }

    private getFeedbackDataStoreTokenKey(feedbackId: string): string {
        return `${FEEDBACK_DATA_STORE_REDIS_KEY}:${feedbackId}`
    }

    private geLastSubmittedFeedbackTokenKey(userId: string): string {
        return `${FEEDBACK_LAST_SUBMIT_TIME_KEY}:${userId}`
    }

    private parseFeedbackJsonString(feedbackJsonStr: string): Feedback {
        if (_.isEmpty(feedbackJsonStr)) return undefined
        const feedback: Feedback = <Feedback>JSON.parse(feedbackJsonStr)
        // JSON.parse will not parse dates. Hence forcefully converting string to Date
        if (!_.isNil(feedback.dateTime)) feedback.dateTime = new Date(feedback.dateTime)
        if (!_.isNil(feedback.feedbackDisplayTime)) feedback.feedbackDisplayTime = new Date(feedback.feedbackDisplayTime)
        return feedback
    }
}