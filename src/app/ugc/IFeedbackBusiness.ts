import {
    AdditionalFeedback,
    Feedback,
    FeedbackMeta,
    FeedbackProductType,
    FeedbackTag,
    Rating,
    Tag
} from "@curefit/feedback-common"
import { UserContext } from "@curefit/userinfo-common"
import { SubmitFeedbackResponse } from "./FeedbackController"
import { Tenant } from "@curefit/user-common"
import { QSRFeedback } from "@curefit/curio-client"
import { Timezone } from "@curefit/util-common"
import { OrderSource } from "@curefit/order-common"

export interface FoodPackStatus {
    packId: string
    name: string
    ticketsTotal: number
    ticketsLeft: number
}

export interface DiyPack {
    packId: string
}

export interface IFeedbackBusiness {
    getConfigForContactSupportCard(feedback: Feedback, selectedTags: Tag[], review: string): Promise<any>
    getFeedbackToShow(userIds: string[], userContext: UserContext): Promise<Feedback>
    createDIYRecipeFeedback(userId: string, productId: string): Promise<Feedback>
    createLiveSessionReportFeedback(userId: string, reportId: string): Promise<Feedback>
    createPTTrainerCustomerChatFeedback(userId: string, productCode: string, doctorName: string, productType: FeedbackProductType): Promise<any>
    createQSRFeedback(qsrFeedback: QSRFeedback): Promise<boolean>
    submitFeedBackV2(userContext: UserContext, userId: string, feedbackId: string, itemId: string, rating: Rating, review: string, selectedTags: Tag[], selectedItemIds: string[], images: string[], tenant: Tenant, additionalFeedbacks?: AdditionalFeedback[], meta?: FeedbackMeta, selectedTagsV2?: FeedbackTag[]): Promise<SubmitFeedbackResponse>
    submitFeedback(timezone: Timezone, userId: string, feedbackId: string, deviceId: string, rating: Rating, review: string, selectedTags: string[], selectedItemIds: string[], orderSource: OrderSource, forceTriggerTicket?: boolean): Promise<Feedback>
    createSupportTicketAndUpdateFeedback(feedbackId: string, forceTriggerTicket: boolean, deviceId: string, orderSource: OrderSource): Promise<Feedback>
}

export default IFeedbackBusiness
