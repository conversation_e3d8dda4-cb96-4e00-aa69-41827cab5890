import { inject, injectable } from "inversify"
import { TimeUtil } from "@curefit/util-common"
import FeedbackDetailView from "./FeedbackDetailView"
import { WidgetView } from "../common/views/WidgetView"
import { UrlPathBuilder } from "@curefit/product-common"
import { FoodProduct as Product } from "@curefit/eat-common"
import { ActivityType } from "@curefit/product-common"
import { AddressTag } from "@curefit/eat-common"
import { FitScore } from "@curefit/feedback-common"
import { FitStreak } from "@curefit/feedback-common"
import { Vertical } from "@curefit/base-common"
import { Feedback, FeedbackProduct, FeedbackProductType, FeedbackRating } from "@curefit/feedback-common"
import { PackStatus, UserActivity } from "../user/TimelineView"
import FeedbackPageConfigV2Cache, { FeedbackConfig } from "./FeedbackPageConfigV2Cache"
import { UserContext } from "@curefit/userinfo-common"
import { ProductDetails } from "./FeedbackController"
import { EntryWidget, FeedbackQuestion } from "@curefit/feedback-common/dist/src/Feedback"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import _ = require("lodash")
import CultUtil from "../util/CultUtil"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"

function shuffleArray<T>(array: T[]): T[] {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]]
    }
    return array
}
@injectable()
class FeedbackViewBuilder {
    constructor(
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness) {}

    async getView(feedbackPageConfigV2Cache: FeedbackPageConfigV2Cache,
                  userContext: UserContext,
                  feedback: Feedback,
                  type: FeedbackProductType,
                  subType: string,
                  productName: string,
                  feedbackId: string,
                  endRoute: string,
                  score?: { fitScore: FitScore, fitStreak: FitStreak },
                  status?: PackStatus,
                  products?: Product[],
                  addressType?: AddressTag,
                  familyType?: string,
                  productDetails?: ProductDetails,
                  isForcedFeedbackEnabled?: boolean,
                  entryWidget?: EntryWidget,
                  ): Promise<FeedbackDetailView> {

        const feedbackObj: FeedbackConfig = await feedbackPageConfigV2Cache.getFeedbackObjV2(feedback, productDetails, userContext)
        feedbackObj.ratings.forEach(rating => {
            rating.tags = shuffleArray(rating.tags)
        })
        const view: FeedbackDetailView = new FeedbackDetailView(feedbackPageConfigV2Cache, userContext, feedback, feedback.productType, subType, productName, feedback.feedbackId, endRoute, score, undefined, products, addressType, feedbackObj.question, feedbackObj.ratings, familyType, feedbackObj.additionalFeedbackQuestions, isForcedFeedbackEnabled, entryWidget)
        const enableGymLoggingForUser = await CultUtil.shouldEnableGymLoggingForUserExperiment(userContext, this.hamletBusiness)
        if (enableGymLoggingForUser && view.widgets && !_.isEmpty(view.widgets)) {
            const isSubmittedFeedback = !_.isNil(feedback.rating) && feedback.rating != "NOT_RATED" && feedback.rating != "DISMISSED"
            view.widgets = view.widgets.map((obj) => { if (obj.widgetType === "FEEDBACK_INPUT_WIDGET") { obj.isSubmitted = isSubmittedFeedback } return obj })
            if (view.widgets[0]["activityType"] && view.widgets[0]["activityType"] === "GYMFIT_FITNESS") {
                const queryParams = { "checkinId": feedbackId }
                const loggingWidgets: any = await this.serviceInterfaces.widgetBuilder.buildWidgets(["c20a81d6-9860-438f-9a4f-14b753b7b0ce"], this.serviceInterfaces, userContext, queryParams, undefined)
                if (!_.isEmpty(loggingWidgets) && !_.isEmpty(loggingWidgets.widgets)) {
                    view.widgets.push(...loggingWidgets.widgets)
                }
            }
        }
        return view
    }

    protected getPackScreenBaseURL(): string {
        return "curefit://feedbackDetail"
    }

}

export default FeedbackViewBuilder
