import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { IFeedbackBusiness } from "./IFeedbackBusiness"
import {
    AdditionalFeedback,
    Feedback, FeedbackMeta,
    FeedbackProductType,
    FeedbackProductTypes, FeedbackTag,
    Rating,
    Tag
} from "@curefit/feedback-common"
import { TOAStatusResponse } from "@curefit/cult-common"
import { BASE_TYPES, Logger } from "@curefit/base"
import * as _ from "lodash"
import { UserContext } from "@curefit/userinfo-common"
import {
    FeedbackService,
    IValidateFeedbackResponse,
    IValidateFeedbackType,
    REPORT_ISSUES_CLIENT_TYPES
} from "@curefit/report-issues-client"
import { INTERVENTION_EVENTS } from "../announcement/AnnouncementBusiness"
import { Action } from "@curefit/apps-common"
import { ErrorFactory } from "@curefit/error-client"
import { ErrorCodes } from "../error/ErrorCodes"
import { ISegmentation<PERSON>acheClient, SEGMENTATION_CLIENT_TYPES } from "@curefit/segmentation-service-client"
import AppUtil from "../util/AppUtil"
import { PromiseCache } from "../util/VMUtil"
import { SubmitFeedbackResponse } from "./FeedbackController"
import { Tenant } from "@curefit/user-common"
import { EventInterventionMappingBusiness } from "../intervention/EventInterventionMappingBusiness"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { CURIO_CLIENT_TYPES } from "@curefit/curio-client/dist/src/ioc/CurioClientTypes"
import { IFeedbackService, QSRFeedback } from "@curefit/curio-client"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { OrderSource } from "@curefit/order-common"
import { IFeedbackRedisCache } from "./FeedbackRedisCache"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { GearFeedbackSNSProducer } from "../gear/GearFeedbackSNSProducer"
import { CultGearProductTypes, GymFitnessProductFeedbackMeta } from "@curefit/feedback-common/dist/src/Feedback"
import { VerticalSpecificFeedbackConsumer } from "../feedback/VerticalSpecificFeedbackConsumer"
import { HamletConfigRequest } from "@curefit/hamlet-common"
import { HAMLET_CLIENT_TYPES, IHamletService } from "@curefit/hamlet-client"
import { constants } from "fs/promises"
import { FEEDBACK_MONGO_TYPES, IFeedbackReadWriteDao } from "@curefit/feedback-mongo"

@injectable()
class FeedbackBusiness implements IFeedbackBusiness {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(REPORT_ISSUES_CLIENT_TYPES.FeedbackService) private feedbackService: FeedbackService,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(SEGMENTATION_CLIENT_TYPES.SegmentationCacheClient) private segmentationCacheClient: ISegmentationCacheClient,
        @inject(CUREFIT_API_TYPES.EventInterventionMappingBusiness) private eventInterventionMappingBusiness: EventInterventionMappingBusiness,
        @inject(CURIO_CLIENT_TYPES.FeedbackService) private curioFeedbackService: IFeedbackService,
        @inject(CUREFIT_API_TYPES.FeedbackRedisCache) private feedbackRedisCache: IFeedbackRedisCache,
        @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
        @inject(CUREFIT_API_TYPES.VerticleSpecificFeedbackConsumer) private verticalSpecificFeedbackConsumer: VerticalSpecificFeedbackConsumer,
        @inject(HAMLET_CLIENT_TYPES.HamletService) private hamletService: IHamletService,
        @inject(FEEDBACK_MONGO_TYPES.FeedbackReadWriteDao) private feedbackDao: IFeedbackReadWriteDao,
    ) {
    }

    async createQSRFeedback(qsrFeedback: QSRFeedback): Promise<boolean> {
        await this.curioFeedbackService.createQSRFeedback(qsrFeedback)
        return true
    }

    async createDIYRecipeFeedback(userId: string, productId: string): Promise<Feedback> {
        return this.curioFeedbackService.createDIYRecipeFeedback(userId, productId)
    }

    async createLiveSessionReportFeedback(userId: string, reportId: string): Promise<Feedback> {
        return this.curioFeedbackService.createLiveSessionReportFeedback(userId, reportId)
    }

    async createPTTrainerCustomerChatFeedback(userId: string, productCode: string, doctorName: string, productType: FeedbackProductType): Promise<any> {
        return this.curioFeedbackService.createPTTrainerCustomerChatFeedback(userId, productCode, doctorName, productType)
    }

    async getConfigForContactSupportCard(feedback: Feedback, selectedTags: Tag[], review: string): Promise<any> {
        const TOAMessage: string = "We apologise you did not have a great experience. As a token of apology from us, please accept a one day extension to your membership."
        const NoTOAMessage: string = "We apologise that you did not have a great class. Feedback like yours help us to improve the class experience for all users."
        let showPopUp: boolean = false
        let showContactSupportCard: boolean
        let subtitleText: string
        let forceTriggerTicket: boolean = false

        if (feedback.productType === "FITNESS") {
            const overCrowdingFeedback: IValidateFeedbackResponse = await this.feedbackService.validateFeedbackForOvercrowding(feedback.feedbackId)
            const feedbackType: IValidateFeedbackType = overCrowdingFeedback.type
            if (feedbackType === IValidateFeedbackType.OVER_CROWDING) {
                showPopUp = true
                subtitleText = overCrowdingFeedback.result === TOAStatusResponse.TOA_APPROVED ? TOAMessage : NoTOAMessage
                showContactSupportCard = !_.isEmpty(review) // if there is no review, then no optIn option because of overcrowding
                _.remove(selectedTags, tag => {
                    return tag.text === "Overcrowding"
                }) // remove overcrowding from selected tags so that we can check config of other tags
            }
        }
        forceTriggerTicket = forceTriggerTicket || _.some(selectedTags, tag => tag.triggerTicket === "FORCE")
        showContactSupportCard = showContactSupportCard || _.some(selectedTags, tag => tag.triggerTicket === "OPT")

        if (forceTriggerTicket) {
            showPopUp = false
        } else {
            showPopUp = showPopUp || showContactSupportCard
        }
        showContactSupportCard = false
        showPopUp = false
        forceTriggerTicket = false

        const actions: Action[] = []
        if (showContactSupportCard) {
            actions.push({ actionType: "FEEDBACK_CONTACT_SUPPORT", title: "No", meta: { forceTicketCreation: false } })
            actions.push({ actionType: "FEEDBACK_CONTACT_SUPPORT", title: "Yes", meta: { forceTicketCreation: true } })
        } else {
            actions.push({ actionType: "FEEDBACK_CONTACT_SUPPORT", title: "Ok", meta: { forceTicketCreation: false } })
        }
        return { showPopUp, showContactSupportCard, subtitleText, forceTriggerTicket, actions }
    }

    async getFeedbackToShow(userIds: string[], userContext: UserContext): Promise<Feedback> {
        // if there is a feedback that was updated > (now - 6 hrs) then don't return any feedback
        // Changed to UTC TimeZone. As it is fetching values from Db with these dates.
        const tz = userContext.userProfile.timezone
        const currentEpoch: number = TimeUtil.getDateNow(tz).getTime()
        const lastSubmittedFeedbackTimestamp: number = await this.feedbackRedisCache.getLastSubmittedFeedbackTimestamp(userContext.userProfile.userId)
        if (currentEpoch - lastSubmittedFeedbackTimestamp < 6 * TimeUtil.TIME_IN_MILLISECONDS.HOUR) {
            return undefined
        }

        const feedbackResultUpdationPromises: Promise<void>[] = []
        let feedbackToReturn: Feedback
        userIds.forEach((userId: string) => {
            feedbackResultUpdationPromises.push(
                this.getLatestValidFeedbackForUser(userId, currentEpoch)
                    .then((feedback: Feedback) => {
                        if (!_.isNil(feedback)) {
                            feedbackToReturn = feedback
                        }
                    }))
            this.purgeInvalidActiveFeedbacks(userId, currentEpoch)
        })
        await Promise.all(feedbackResultUpdationPromises)
        return feedbackToReturn
    }

    private async getLatestValidFeedbackForUser(userId: string, currentEpoch: number): Promise<Feedback> {
        const allActiveFeedbacks: Feedback[] = await this.feedbackRedisCache.getAllActiveFeedbacksForUser(userId)
        if (_.isEmpty(allActiveFeedbacks)) return undefined
        let selectedFeedback: Feedback // Final Feedback to be shown to the user
        let selectedFeedbackDisplayTimeLowerBound: number = -Infinity // Lower Bound of the final feedback that is selected
        allActiveFeedbacks.forEach((feedback: Feedback) => {
            const feedbackDisplayTimeUpperBound: number = this.getMaxEpochForFeedbackToBeDisplayed(feedback)
            const feedbackDisplayTimeLowerBound: number = this.getMinEpochForFeedbackToBeDisplayed(feedback)
            // Check if current time falls inside the window of the lower bound and upper bound
            if (feedbackDisplayTimeLowerBound <= currentEpoch && currentEpoch <= feedbackDisplayTimeUpperBound) {
                // If it falls within the window, select the feedback with the latest lower bound display time
                if (feedbackDisplayTimeLowerBound > selectedFeedbackDisplayTimeLowerBound) {
                    selectedFeedback = feedback
                    selectedFeedbackDisplayTimeLowerBound = feedbackDisplayTimeLowerBound
                }
            }
        })
        return selectedFeedback
    }

    private async purgeInvalidActiveFeedbacks(userId: string, currentEpoch: number): Promise<void> {
        this.logger.info(`Purging feedbacks in Redis for user Id: ${userId} before cutoffEpoch: ${currentEpoch}`)
        const allActiveFeedbacks: Feedback[] = await this.feedbackRedisCache.getAllActiveFeedbacksForUser(userId)
        allActiveFeedbacks.forEach((feedback: Feedback) => {
            const feedbackDisplayTimeUpperBound: number = this.getMaxEpochForFeedbackToBeDisplayed(feedback)
            const feedbackDisplayTimeLowerBound: number = this.getMinEpochForFeedbackToBeDisplayed(feedback)
            if (currentEpoch < feedbackDisplayTimeLowerBound || currentEpoch > feedbackDisplayTimeUpperBound) {
                this.feedbackRedisCache.purgeActiveFeedbackForUser(feedback.feedbackId, userId)
            }
        })
    }

    private getMaxEpochForFeedbackToBeDisplayed(feedback: Feedback): number {
        const notToBeShownBefore: number = this.getMinEpochForFeedbackToBeDisplayed(feedback)
        const notToBeShownAfter: number = notToBeShownBefore + TimeUtil.TIME_IN_MILLISECONDS.DAY
        return notToBeShownAfter
    }

    private getMinEpochForFeedbackToBeDisplayed(feedback: Feedback): number {
        const notToBeShownBefore: number = _.isDate(feedback.feedbackDisplayTime) ? feedback.feedbackDisplayTime.getTime() : feedback.dateTime.getTime()
        return notToBeShownBefore
    }

    public async submitFeedBackV2(
        userContext: UserContext,
        userId: string,
        feedbackId: string,
        itemId: string,
        rating: Rating,
        review: string,
        selectedTags: Tag[],
        selectedItemIds: string[],
        images: any[],
        tenant: Tenant,
        additionalFeedbacks?: AdditionalFeedback[],
        meta?: FeedbackMeta,
        selectedTagsV2?: FeedbackTag[]
    ): Promise<SubmitFeedbackResponse> {
        const timeZone = userContext.userProfile.timezone
        if (!userContext.userProfile.promiseMapCache) {
            userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
        }

        const selectedTagNames: string[] = []
        selectedTags.map((tag: Tag) => {
            selectedTagNames.push(tag.text)
            tag.subTags && tag.subTags.map((subTag: Tag) => {
                selectedTagNames.push(subTag.text)
            })
        })
        try {
            this.logger.info(`Sending feedback to curio with ${JSON.stringify({meta, selectedTagsV2})}`)
            let newMeta: any
            if (_.isNil(meta)) {
                newMeta = {}
            } else {
                newMeta = _.cloneDeep(meta)
            }
            try {
                if (await AppUtil.isExtraRatingEnabled(userContext, this.hamletBusiness)) {
                    let preSubmitFeedback: Feedback
                    preSubmitFeedback = await this.feedbackDao.findOne({feedbackId: feedbackId})
                    if (preSubmitFeedback.productType === "FITNESS") {
                        newMeta.totalRatings = 5
                        newMeta.ratingV2 = rating
                        if (rating === "AVERAGE") rating = "GOOD"
                    }
                }
            } catch (e) {
                this.logger.error("ERROR::Error fetching feedback for id:" + feedbackId)
                this.serviceInterfaces.rollbarService.sendError(e)
            }
            const feedback: Feedback = await this.curioFeedbackService.submitFeedBackV2(timeZone, userId, feedbackId,
                itemId, rating, review, selectedTags, selectedItemIds, images, tenant, additionalFeedbacks, newMeta, selectedTagsV2)
            const resp = await this.getConfigForContactSupportCard(feedback, selectedTags, review)
            const shouldShowIntervention = !_.isEmpty(feedback.productType) && (_.includes(["AD", "DIY_MEDITATION", "DIY_FITNESS"], feedback.productType)) && (rating === "AWESOME" || rating === "GOOD" || rating === "DISMISSED") && AppUtil.isPostRatingInterventionSupported(userContext)
            const segments = await this.segmentationCacheClient.getUserSegments(userId)
            const expId = "935"
            const hamletExperimentMap = await this.hamletBusiness.getUserAllocations(AppUtil.getHamletContext(userContext, [expId]))
            const userAssignment = hamletExperimentMap ? hamletExperimentMap.assignmentsMap[expId] : undefined
            const isCultAppRatingPromptEnabled = process.env.ENVIRONMENT === "STAGE" ? true : userAssignment ? userAssignment.bucket.bucketId === "1" : false
            const isSugarFitApp = AppUtil.isSugarFitApp(userContext)
            const showFlutterInAppReviewPrompt = await AppUtil.doesUserBelongToInAppReviewPromptSegment(this.serviceInterfaces.segmentService, userContext)
            const showAppRatingPrompt = (showFlutterInAppReviewPrompt || isSugarFitApp) && rating === "AWESOME"
            const isNewAppRatingFlow = !isSugarFitApp && userContext.sessionInfo.appVersion >= 8.84
            this.verticalSpecificFeedbackConsumer.consumeFeedback(feedback) // fire and forget
            return {
                title: "Thank you for your feedback!",
                subtitle: resp.subtitleText,
                showContactSupportOption: resp.showContactSupportCard,
                contactSupportText: "Do you want the customer support to contact you?",
                forceTicketCreation: resp.forceTriggerTicket,
                showPopUp: resp.showPopUp,
                feedbackId: feedbackId,
                actions: resp.actions,
                showAppRatingPrompt: showAppRatingPrompt,
                isNewAppRatingFlow: isNewAppRatingFlow,
                ratingPromptHeader: isSugarFitApp ? "You are doing great on your Diabetes reversal journey!" : "You are nailing your fitness journey!",
                ratingPromptSubtext: isSugarFitApp ? "Are you enjoying your sugar.fit experience?" : "Are you enjoying your Cult.fit experience?",
                ratingPromptAcceptText: "Loving it!",
                ratingPromptDismissText: "Not so much",
                interventionAction: shouldShowIntervention ? await this.eventInterventionMappingBusiness.buildInterventionAction(INTERVENTION_EVENTS.postRatingEvent, this.serviceInterfaces.pageService, this.serviceInterfaces.segmentService, userContext) : undefined
            }
        } catch (e) {
            return Promise.reject<any>(this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("No feedback found for feedbackId: " + feedbackId).build())
        }
    }

    public async submitFeedback(timezone: Timezone, userId: string, feedbackId: string, deviceId: string, rating: Rating, review: string, selectedTags: string[], selectedItemIds: string[], orderSource: OrderSource, forceTriggerTicket?: boolean) {
        const success = await this.curioFeedbackService.submitFeedback(timezone, userId, feedbackId, deviceId, rating, review, selectedTags, selectedItemIds, orderSource, forceTriggerTicket)
        const feedback: Feedback = await this.curioFeedbackService.getFeedback(feedbackId)
        this.verticalSpecificFeedbackConsumer.consumeFeedback(feedback) // fire and forget
        return Promise.resolve(success)
    }

    public async createSupportTicketAndUpdateFeedback(feedbackId: string, forceTriggerTicket: boolean, deviceId: string, orderSource: OrderSource): Promise<Feedback> {
        return this.curioFeedbackService.createSupportTicketAndUpdateFeedback(feedbackId, forceTriggerTicket, deviceId, orderSource)
    }

}

export default FeedbackBusiness
