import { TimeUtil } from "@curefit/util-common"
import { ActionCard, ProductDetailPage, WidgetView } from "../common/views/WidgetView"
import { UrlPathBuilder } from "@curefit/product-common"
import { FoodProduct as Product } from "@curefit/eat-common"
import { ActivityType } from "@curefit/product-common"
import { AddressTag } from "@curefit/eat-common"
import { EntryWidget, FeedbackInputItem, FitScore, FitStreak } from "@curefit/feedback-common"
import { Vertical } from "@curefit/base-common"
import {
    Feedback,
    FeedbackProduct,
    FeedbackProductType,
    FeedbackRating,
    QuestionInputCard
} from "@curefit/feedback-common"
import { PackStatus, UserActivity } from "../user/TimelineView"
import FeedbackPageConfigV2Cache, { AdditionalFeedbackQuestions } from "./FeedbackPageConfigV2Cache"
import { UserContext } from "@curefit/userinfo-common"
import { CareUtil } from "../util/CareUtil"
import { GearProductState, GearProductStates, IssueProductType } from "@curefit/issue-common"
import { ActionUtil as GearActionUtil } from "../util/ActionUtil"
import AppUtil from "../util/AppUtil"
import { Action } from "@curefit/apps-common"
import { PageTypes } from "@curefit/apps-common"
import FoodMarketplaceUtil from "../util/FoodMarketplaceUtil"
import _ = require("lodash")
import { COMBINED_WEIGHT_LOSS_CLP_DEEPLINK } from "../util/TransformUtil"

class FeedbackDetailView extends ProductDetailPage {

    public widgets: WidgetView[] = []
    public pageAction?: ActionCard
    public endRouteAction?: Action
    public itemId?: string

    constructor(feedbackPageConfigV2Cache: FeedbackPageConfigV2Cache,
                userContext: UserContext,
                feedback: Feedback,
                type: FeedbackProductType,
                subType: string,
                productName: string,
                feedbackId: string,
                endRoute: string,
                score?: { fitScore: FitScore, fitStreak: FitStreak },
                status?: PackStatus,
                products?: Product[],
                addressType?: AddressTag,
                feedbackQuestion?: string,
                feedbackRatings?: FeedbackRating[],
                familyType?: string,
                additionalFeedbackQuestions?: AdditionalFeedbackQuestions[],
                isForcedFeedbackEnabled?: boolean,
                entryWidget?: EntryWidget

    ) {
        super()
        if (score) {
            this.widgets.push(this.fitScoreWidget(score))
        }

        if (AppUtil.isLiveVideoCallFeedback(feedbackId)) {
            this.itemId = feedback?.itemId // used on web
        }
        const vertical: Vertical = this.getVertical(type)
        this.widgets.push(this.summaryWidget(userContext, feedbackQuestion, vertical, feedbackId, productName, type, subType, score ? true : false, status, familyType, entryWidget))
        this.widgets.push(this.inputWidget(feedbackQuestion, feedbackRatings, feedback, type, feedbackId, products, addressType, userContext, additionalFeedbackQuestions, vertical, isForcedFeedbackEnabled))
        this.pageAction = {
            title: "Submit",
            action: "SUBMIT_FEEDBACK"
        }
        this.actions = [
            {
                title: "Submit",
                actionType: "SUBMIT_FEEDBACK"
            }
        ]

        if (endRoute === PageTypes.LiveSessionReport) {
            this.pageAction = {
                title: "Submit & Proceed",
                action: "SUBMIT_FEEDBACK"
            }
            let url = `curefit://livesessionreport?contentId=${feedback.itemId}&prevPage=videoplayer`
            if (feedback.productType === "DIY_FITNESS" || feedback.productType === "DIY_MEDITATION") {
                url = `${url}&userSessionId=${feedback.feedbackId}&contentType=${feedback.productType}`
            }
            this.endRouteAction = {
                actionType: "NAVIGATION",
                navigationType: "NAVIGATE_REPLACE",
                url
            }
        } else if (endRoute === "activity_streak_game") {
            this.endRouteAction = {
                actionType: "POP_AND_TRIGGER_ACTION",
                meta: {
                    nextAction: {
                        actionType: "NAVIGATION",
                        meta: {
                            viaDeeplink: true,
                        },
                        url: "curefit://activity_streak_game"
                    }
                }
            }
        } else if (endRoute === "highlights_page") {
            this.endRouteAction = {
                actionType: "POP_AND_TRIGGER_ACTION",
                meta: {
                    nextAction: {
                        actionType: "NAVIGATION",
                        meta: {
                            viaDeeplink: true,
                        },
                        url: "curefit://highlights_page"
                    }
                }
            }

        } else if (endRoute === "POP_ACTION") {
            this.endRouteAction = {
                actionType: "POP_ACTION",
            }
        } else if (feedback.productType === "TRANSFORM_CONTENT") {
            this.endRouteAction = {
                actionType: "NAVIGATION",
                url: COMBINED_WEIGHT_LOSS_CLP_DEEPLINK
            }
        }
    }

    protected getPackScreenBaseURL(): string {
        return "curefit://feedbackDetail"
    }
    private summaryWidget(userContext: UserContext, question: string, vertical: Vertical, feebackId: string, productName: string, type: FeedbackProductType, subType: string, collapsable: boolean, status?: PackStatus, familyType?: string, entryWidget?: EntryWidget): WidgetView & UserActivity & { message?: string, collapsable: boolean, vertical: Vertical, question: string } {

        const subActivityType: string = undefined
        if (productName) {
            const subActivityType = type === "FITNESS" ? productName.toUpperCase() : undefined
        }
        const title = productName || ""
        return {
            userActivityId: feebackId,
            question: question, // Sending question here addition to input widget as website needs here
            date: TimeUtil.todaysDate(userContext.userProfile.timezone),
            widgetType: "FEEDBACK_SUMMARY",
            activityName: this.getActivityName(type, subType, familyType),
            activityType: this.getActivityType(type, subType),
            subActivityType: subActivityType,
            title: title,
            message: this.getActivityMessage(type, productName, familyType),
            packStatus: status,
            collapsable: collapsable,
            vertical: vertical,
            entryWidget: [
                {
                    widgetType: "FEEDBACK_ENTRY",
                    date: entryWidget.date,
                    time: entryWidget.time,
                    type: entryWidget.type,
                    center: entryWidget.center


                }
            ],
        }
    }

    private getActivityName(type: FeedbackProductType, subType: string, familyType?: string): string {
        switch (type) {
            case "FOOD":
                return subType
            case "FOOD_MARKETPLACE":
                return "Food Marketplace Order"
            case "LIVE":
                return "At Home Live session"
            case "FITNESS":
                return "Workout"
            case "MIND":
                return "mind.fit session"
            case "DIY_MEDITATION":
                return "Meditation"
            case "DIY_FITNESS":
                return "Workout"
            case "CF_ONLINE_CONSULTATION":
            case "CF_INCENTRE_CONSULTATION":
                return CareUtil.isPTDoctorType(subType) || (familyType && familyType === "PROCEDURE") ? "Session" : CareUtil.isLivePTDoctorType(subType) ? "Online Personal Training" : CareUtil.isLiveSGTDoctorType(subType) ? "Online Group Class" : "Consultation"
            case "AT_HOME_SAMPLE_COLLECTION":
            case "IN_CENTRE_VISIT_FOR_TEST":
                return "Diagnostic Tests"
            case "PHLEBOTOMIST_TASK":
                return "CGM Technician Visit"
            case "GROUP_CLASS":
                return "Wellness Group Class"
            case "SUGARFIT_MASTER_CLASS":
                return "Online Consultation"
            case "SUGARFIT_FITNESS_CLASS":
                return "Live Fitness Class"
            case "CULT_SCORE_DIY":
                return "Fitness Test"
            case "GEAR":
                return "CultSport Order"
            case "RECIPE":
                return "Recipe"
            case "FITNESS_REPORT":
                return "Fitness Report"
            case "LIVE_SESSION_REPORT":
                return "Live Session Report"
            case "GYMFIT_FITNESS_PRODUCT":
                return "Gym Workout"
            case "CULT_PT_TRAINER_CUSTOMER_CHAT":
                return "Online Personal Trainer Chat"
            case "LIVE_VIDEO_CALL":
                return "Live Interactive"
            case "TRANSFORM_PRODUCT":
                return "Cult Transform"
            case "TRANSFORM_COACH_CALL":
                return "Coach Call"
            case "GYM_PT_PRODUCT":
            case "GYM_PT_PPC_PRODUCT":
                return "Gym Personal Training"
            case "PLAY":
                return "Play session"
            default:
                return ""

        }
    }

    private getVertical(type: FeedbackProductType): Vertical {
        switch (type) {
            case "LIVE_VIDEO_CALL":
            case "DIY_FITNESS":
            case "FITNESS":
            case "CULT_SCORE_DIY":
                return "CULT_FIT"
            case "DIY_MEDITATION":
            case "MIND":
                return "MIND_FIT"
            case "FOOD":
            case "RECIPE":
                return "EAT_FIT"
            case "CF_ONLINE_CONSULTATION":
            case "CF_INCENTRE_CONSULTATION":
            case "AT_HOME_SAMPLE_COLLECTION":
            case "IN_CENTRE_VISIT_FOR_TEST":
                return "CARE_FIT"
            case "PHLEBOTOMIST_TASK":
                return "SUGAR_FIT"
            case "GROUP_CLASS":
                return "SUGAR_FIT"
            case "SUGARFIT_MASTER_CLASS":
                return "SUGAR_FIT"
            case "SUGARFIT_FITNESS_CLASS":
                return "SUGAR_FIT"
            case "GEAR":
                return "CULT_GEAR"
            case "FOOD_MARKETPLACE":
                return "WELLNESS"
            case "TRANSFORM_PRODUCT":
            case "TRANSFORM_COACH_CALL":
            case "TRANSFORM_CONTENT":
            case "TRANSFORM_MEAL_PLAN_RATING":
            case "TRANSFORM_AUDIO_CALL_RATING":
                return "TRANSFORM"
            case "PLAY":
                return "PLAY_FIT"
        }
    }

    private getActivityMessage(type: FeedbackProductType, productName: string, familyType?: string): string {
        switch (type) {
            case "FOOD":
            case "FOOD_MARKETPLACE":
                return `Hope you enjoyed your meal!`
            case "LIVE_VIDEO_CALL":
            case "FITNESS":
            case "MIND":
            case "CULT_SCORE_DIY":
            case "LIVE":
                return `Hope you had a great ${productName} session!`
            case "DIY_MEDITATION":
                return "Hope you had a mindful meditation session."
            case "DIY_FITNESS":
                return "Hope you had an energizing workout session."
            case "CF_ONLINE_CONSULTATION":
            case "CF_INCENTRE_CONSULTATION":
                return familyType && familyType === "PROCEDURE" ? "Hope you had a great session." : "Hope you had a great consultation."
            case "AT_HOME_SAMPLE_COLLECTION":
            case "PHLEBOTOMIST_TASK":
            case "IN_CENTRE_VISIT_FOR_TEST":
                return "Hope you had a great experience."
            case "GROUP_CLASS":
                return "Hope you had a great session."
            case "SUGARFIT_MASTER_CLASS":
                return "Hope you had a great consultation."
            case "SUGARFIT_FITNESS_CLASS":
                return "Hope you had a great session."
            case "RECIPE":
                return "Hope you enjoyed the cooking session!"
            case "TRANSFORM_PRODUCT":
                return "Product Experience"
            case "TRANSFORM_MEAL_PLAN_RATING":
            case "TRANSFORM_AUDIO_CALL_RATING":
            case "TRANSFORM_COACH_CALL":
                return "Cult Transform"
            case "PLAY":
                return `Hope you had a great session!`
        }
    }

    private getActivityType(type: FeedbackProductType, subType: string): ActivityType {
        switch (type) {
            case "WHOLE_FIT":
                return "WHOLEFIT_ORDER"
            case "FOOD_MARKETPLACE":
                return "FOOD_MARKETPLACE_MEAL"
            case "FOOD":
                return "EATFIT_MEAL"
            case "FITNESS":
            case "CULT_SCORE_DIY":
            case "LIVE":
                return "CULT_CLASS"
            case "MIND":
                return "MIND_CLASS"
            case "DIY_MEDITATION":
                return "DIY_MEDITATION"
            case "DIY_FITNESS":
                return "DIY_FITNESS"
            case "CF_ONLINE_CONSULTATION":
            case "CF_INCENTRE_CONSULTATION":
                return CareUtil.isLivePTDoctorType(subType) || CareUtil.isLiveSGTDoctorType(subType) || CareUtil.isPTDoctorType(subType) ? "CULT_CLASS" : "CONSULTATION"
            case "AT_HOME_SAMPLE_COLLECTION":
            case "PHLEBOTOMIST_TASK":
            case "IN_CENTRE_VISIT_FOR_TEST":
                return "DIAGNOSTICS"
            case "GROUP_CLASS":
                return "CULT_CLASS"
            case "SUGARFIT_MASTER_CLASS":
                return "CONSULTATION"
            case "SUGARFIT_FITNESS_CLASS":
                return "LIVE_CLASS"
            case "GEAR":
                return "CULT_GEAR_ECOMMERCE"
            case "RECIPE":
                return "DIY_RECIPE"
            case "FITNESS_REPORT":
                return "FITNESS_REPORT"
            case "LIVE_SESSION_REPORT":
                return "LIVE_SESSION_REPORT"
            case "GYMFIT_FITNESS_PRODUCT":
            case "GYM_PT_PRODUCT":
            case "GYM_PT_PPC_PRODUCT":
                return "GYMFIT_FITNESS"
            case "LIVE_VIDEO_CALL":
                return "LIVE_VIDEO_CALL"
            case "PLAY":
                return "PLAY_CLASS"
        }
    }

    private fitScoreWidget(score: { fitScore: FitScore, fitStreak: FitStreak }): WidgetView & {
        fitScore: FitScore, fitStreak: FitStreak
    } {
        return {
            widgetType: "FIT_SCORE_WIDGET",
            fitScore: score.fitScore,
            fitStreak: score.fitStreak
        }
    }

    private getInputWidgetImageForProduct(product: Product): string {

        switch (product.productType) {
            case "FOOD_MARKETPLACE":
                return product.imageUrl || FoodMarketplaceUtil.getDummyMealCardImageUrl()
            case "GEAR":
                return product?.imageUrls?.[0]
            case "FOOD":
            default:
                return UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, "FOOD", "THUMBNAIL", product.imageVersion)

        }
    }

    private inputWidget(feedbackQuestion: string, feedbackRatings: FeedbackRating[], feedback: Feedback, type: FeedbackProductType, feebackId: string, productList: Product[], addressType?: AddressTag, userContext?: UserContext, additionalFeedbackQuestions?: AdditionalFeedbackQuestions[], vertical?: Vertical, isForcedFeedbackEnabled?: boolean) {
        const feedbackProducts: FeedbackProduct[] = []
        if (productList) {
            productList.map((product: Product) => {
                const fbp: FeedbackProduct = {
                    title: product.title,
                    productId: product.productId,
                    imageUrl: this.getInputWidgetImageForProduct(product)
                }
                if (product.productType === "GEAR") {
                    fbp.aspectRatio = 0.75
                }
                feedbackProducts.push(fbp)
            })
        }
        if (addressType === "KIOSK" && type === "FOOD")
            feedbackRatings.forEach(feedbackRatings => {
                feedbackRatings.tags = feedbackRatings.tags.filter(tag => {
                    return tag.text !== "Delivery"
                })
            })

        const isTabbedFeedbackFlowSupported = AppUtil.isTabbedFeedbackFlowSupported(userContext)
        const isGearWidget = type === "GEAR" && AppUtil.isL2FeedbackSupported(userContext)
        const isTabbedFeedbackFlowSupportedForSatAndMusic = AppUtil.isTabbedFeedbackFlowSupportedForSATAndMusic(userContext)
        const trainerFeedbackEnabledGyms = ["1098", "471", "607", "686", "1068", "1019", "1099", "1069", "1077", "931", "1071", "1027", "1026", "1100", "1078", "1070", "869", "1014", "1064", "685", "492", "1118", "7", "99", "100", "101", "916", "908", "931", "1032", "1076"]
        const feedbackMeta: any = _.cloneDeep(feedback.meta)
        const newFeedbackRatings = _.cloneDeep(feedbackRatings)
        if (isForcedFeedbackEnabled) {
            newFeedbackRatings.map((entry) => {
                if (entry.rating == "TERRIBLE" || entry.rating == "BAD") entry.disabledSubmitText = "Please tell us what you didn't like"
                else if (entry.rating == "GOOD") entry.disabledSubmitText = "Please tell us what can be better"
                else if (entry.rating == "AWESOME") entry.disabledSubmitText = "Please tell us what you liked"
            })
        }

        const inputWidget: WidgetView & ({
            feedbackId: string,
            items: FeedbackInputItem[],
        } | {
            question: string, ratings: FeedbackRating[],
            feebackId: string, feedbackId: string, productList: FeedbackProduct[],
            selectionTitle: string,
        }) = isTabbedFeedbackFlowSupported && vertical != "TRANSFORM" && !isGearWidget ? {
            widgetType: "FEEDBACK_INPUT_WIDGET",
            feedbackId: feebackId,
            items: [
                {
                    type: "FEEDBACK_INPUT_CARD",
                    question: feedbackQuestion,
                    selectionTitle: "Which item(s) were you unhappy with?",
                    productList: feedbackProducts.length > 1 && feedback.productType !== "WHOLE_FIT" ? feedbackProducts : undefined,
                    ratings: newFeedbackRatings,
                    feedbackMeta: feedbackMeta,
                    showTrainerRatings: true,
                    forceTagSelect: isForcedFeedbackEnabled ?? false
                }
            ],
        } : vertical === "TRANSFORM" && AppUtil.isTransformNewFeedbackSupported(userContext) ? {
            widgetType: "FEEDBACK_INPUT_WIDGET",
            feedbackId: feebackId,
            items: [
                {
                    type: "FEEDBACK_INPUT_CARD",
                    question: feedbackQuestion,
                    selectionTitle: "Which item(s) were you unhappy with?",
                    productList: feedbackProducts.length > 1 && feedback.productType !== "WHOLE_FIT" ? feedbackProducts : undefined,
                    ratings: newFeedbackRatings,
                    feedbackMeta: feedbackMeta,
                    showTrainerRatings: false,
                    forceTagSelect: isForcedFeedbackEnabled ?? false
                }
            ],
        } : {
            widgetType: "FEEDBACK_INPUT_WIDGET",
            question: feedbackQuestion,
            feebackId: feebackId, // to support older apps
            feedbackId: feebackId,
            selectionTitle: "Which item(s) were you unhappy with?",
            productList: feedbackProducts.length > 1 && feedback.productType !== "WHOLE_FIT" ? feedbackProducts : undefined,
            ratings: feedbackRatings,
            items: [{}]
        }

        if (isGearWidget) {
            inputWidget.widgetType = "FEEDBACK_GEAR_INPUT_WIDGET"
            inputWidget.selectionTitle = "What could be improved?"
            inputWidget.ratings.map((feedbackRating: FeedbackRating) => {
                return feedbackRating.rating === "TERRIBLE" ?
                    this.ratingForTerribleEmotion(feedbackRating, feedback.orderId) : feedbackRating
            })
        }

    //     if (isTabbedFeedbackFlowSupported && additionalFeedbackQuestions && additionalFeedbackQuestions.length > 0 &&
    //         vertical != "TRANSFORM" && !isGearWidget) {
    //         additionalFeedbackQuestions.forEach((additionalFeedbackQuestion) => {
    //             if (additionalFeedbackQuestion && additionalFeedbackQuestion.filterId === "CULT_SAT_FEEDBACK_QUESTIONS" &&
    //                 additionalFeedbackQuestion.questions && additionalFeedbackQuestion.questions.length > 0 && isTabbedFeedbackFlowSupportedForSatAndMusic) {
    //                 const {ratings, filterId, title, questions, isReviewEnabled} = additionalFeedbackQuestion
    //                 inputWidget.items.push({
    //                     type: "SAT_QUESTION_INPUT_CARD",
    //                     ratings,
    //                     filterId,
    //                     title,
    //                     questions,
    //                     isReviewEnabled,
    //                 } as QuestionInputCard)
    //             } else if (additionalFeedbackQuestion && additionalFeedbackQuestion.filterId
    //                 && additionalFeedbackQuestion.filterId === "CULT_MUSIC_FEEDBACK_QUESTIONS" && isTabbedFeedbackFlowSupportedForSatAndMusic &&
    //                 additionalFeedbackQuestion.questions && additionalFeedbackQuestion.questions.length > 0) {
    //                 const {ratings, filterId, title, questions, isReviewEnabled} = additionalFeedbackQuestion
    //                 inputWidget.items.push({
    //                     type: "QUESTION_INPUT_CARD",
    //                     ratings,
    //                     filterId,
    //                     title,
    //                     questions,
    //                     isReviewEnabled,
    //                 } as QuestionInputCard)
    //             } else if (additionalFeedbackQuestion && additionalFeedbackQuestion.filterId &&
    //                 additionalFeedbackQuestion.filterId !== "CULT_SAT_FEEDBACK_QUESTIONS" && additionalFeedbackQuestion.filterId !== "CULT_MUSIC_FEEDBACK_QUESTIONS"
    //                 && additionalFeedbackQuestion.questions && additionalFeedbackQuestion.questions.length > 0) {
    //                 const {ratings, filterId, title, questions, isReviewEnabled} = additionalFeedbackQuestion
    //                 inputWidget.items.push({
    //                     type: "QUESTION_INPUT_CARD",
    //                     ratings,
    //                     filterId,
    //                     title,
    //                     questions,
    //                     isReviewEnabled,
    //                 } as QuestionInputCard)
    //             }
    //         })
    //     }
        return inputWidget
    }

    private ratingForTerribleEmotion(feedbackRating: FeedbackRating, orderId: string) {
        const actionText = {
            text: "Sorry that you had such a bad experience"
        }
        const productType: IssueProductType = "CULT_GEAR"
        const query = {
            productType: productType,
            productStates: GearProductStates as GearProductState[],
            meta: {
                orderId: orderId
            }
        }
        feedbackRating.action = {
            title: "Contact Support",
            actionType: "NAVIGATION",
            url: GearActionUtil.getIssuesUrlOld(query),
            meta: actionText
        }
        return feedbackRating
    }
}

export default FeedbackDetailView
