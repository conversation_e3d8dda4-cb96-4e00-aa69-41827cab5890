import * as _ from "lodash"
import { Action } from "../common/views/WidgetView"
import { PageWidget } from "../page/Page"
import { inject, injectable } from "inversify"
import IssueBusiness from "../crm/IssueBusiness"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { FoodBooking } from "@curefit/shipment-common"
import { ActionUtil } from "@curefit/base-utils"
import { ActionUtil as AppActionUtil } from "../util/ActionUtil"
import { SupportActionableCardWidget, SupportEmptyListingWidget } from "../page/PageWidgets"
import { ICatalogueService, CATALOG_CLIENT_TYPES } from "@curefit/catalog-client"
import { UrlPathBuilder } from "@curefit/product-common"
import { SupportListPageView } from "../crm/SupportListPageView"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { UserContext } from "@curefit/userinfo-common"
import AppUtil from "../util/AppUtil"
import * as momentTz from "moment-timezone"
import { ISegmentService } from "@curefit/vm-models"


const OngoingStatus: {[key: string]: any} = {
    text: "ONGOING",
    colour: "#00b4df"
}

export const EatBookingStatus: { [key: string]: any; } = {
    LOT_ASSIGNED: OngoingStatus,
    ACCEPTING: OngoingStatus,
    COOKING: OngoingStatus,
    PACKING: OngoingStatus,
    DISPATCHING: OngoingStatus,
    RECEIVING: OngoingStatus,
    DRIVING: OngoingStatus,
    DELIVERING: OngoingStatus,
    DELIVERED: {
        text: "DELIVERED",
        colour: "#50d166"
    },
    REJECTED: {
        text: "REJECTED",
        colour: "#e05343"
    },
    CANCELLED: {
        text: "CANCELLED",
        colour: "#e05343"
    },
    UPCOMING_CANCELED: {
        text: "CANCELLED",
        colour: "#e05343"
    }
}

@injectable()
class MealListPageViewBuilder {
    constructor(
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness,
        @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService,
    ) {
    }

    async buildMealListPageView(userContext: UserContext, foodBookings: FoodBooking[], pageNumber: number): Promise<SupportListPageView> {
        const widgets: PageWidget[] = []
        let actions: Action[]
        if (_.isEmpty(foodBookings)) {
            if (pageNumber === 1 && !(await AppUtil.isNewSupportInFlutterSupported(this.segmentService, userContext))) {
                widgets.push(new SupportEmptyListingWidget("MEAL", "No Meals Yet!", "From gourmet dishes to simple Indian food, eat.fit's got a healthy treat for every palette and every meal."))
                actions = [{
                    actionType: "EXTERNAL_DEEP_LINK",
                    url: "https://www.eatfit.in/?utm_source=cultfit",
                    title: "Order food"
                }]
            }
        } else {
            const cardWidgetPromises: Promise<SupportActionableCardWidget>[] = _.map(foodBookings, foodBooking => {
                return this.getActionableCardWidget(userContext, foodBooking, foodBooking.timezone)
            })
            const cardWidgets: SupportActionableCardWidget[] = await Promise.all(cardWidgetPromises)
            cardWidgets.map(cardWidget => {
                widgets.push(cardWidget)
            })
        }
        return new SupportListPageView(widgets, actions)
    }

    async buildMealListWidgets(userContext: UserContext, foodBookings: FoodBooking[]): Promise<SupportActionableCardWidget[]> {
        if (!_.isEmpty(foodBookings)) {
            const cardWidgetPromises: Promise<SupportActionableCardWidget>[] = _.map(foodBookings, foodBooking => {
                return this.getActionableCardWidget(userContext, foodBooking, foodBooking.timezone)
            })
            const cardWidgets: SupportActionableCardWidget[] = await Promise.all(cardWidgetPromises)
            return cardWidgets
        }
        return []
    }

    private async getActionableCardWidget(userContext: UserContext, foodBooking: FoodBooking, timezone: Timezone): Promise<SupportActionableCardWidget> {
        const reportIssueParams = await this.issueBusiness.getMealBookingIssueParams(foodBooking, timezone)
        const foodProduct = await this.catalogueService.getProduct(foodBooking.productId)
        // console.log("Food booking eta time: Type: " + typeof(foodBooking.eta) + " . Value: " + foodBooking.eta)
        return {
            widgetType: "SUPPORT_ACTIONABLE_CARD_WIDGET",
            title: foodProduct.title + (foodBooking.products.length > 1 ? ` + ${foodBooking.products.length - 1} items` : ``),
            subTitle: `#${foodBooking.orderId}`,
            footer: AppUtil.isWeb(userContext) ? undefined : [{
                text: `${TimeUtil.formatDateStringInTimeZone(foodBooking.deliveryDate, timezone, "ddd, D MMM")}`,
            }],
            cardAction: {
                actionType: "NAVIGATION",
                url: AppActionUtil.getIssuesUrl(),
                meta: (await AppUtil.isNewSupportInFlutterSupported(this.segmentService, userContext)) ? {
                    title: foodProduct.title + (foodBooking.products.length > 1 ? ` + ${foodBooking.products.length - 1} items` : ``),
                    subTitle: "What is your primary concern with the meal"
                } : null,
            },
            vegIcon: foodProduct.attributes["isVeg"] === "TRUE" ? "VEG" : "NON_VEG", // foodProduct.isVeg ? "VEG" : "NON_VEG",
            imageUrl: UrlPathBuilder.getSingleImagePath(foodProduct.parentProductId ? foodProduct.parentProductId : foodProduct.productId, "FOOD", "THUMBNAIL", foodProduct.imageVersion),
            status: EatBookingStatus[foodBooking.state],
            time: AppUtil.isWeb(userContext) ? `${TimeUtil.formatDateStringInTimeZone(foodBooking.deliveryDate, timezone, "ddd, D MMM")}` : undefined,
            timestamp: momentTz(String(foodBooking.eta), "YYYY-MM-DDTHH:mm:ss.SSSZ").valueOf(),
            timezone: timezone
        }
    }
}

export default MealListPageViewBuilder
