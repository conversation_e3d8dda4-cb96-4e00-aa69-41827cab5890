import { controller, httpPost } from "inversify-express-utils"
import { Container, inject } from "inversify"
import * as express from "express"
import * as _ from "lodash"

import { IApiKeyService, BASE_UTILS_TYPES } from "@curefit/base-utils"
import { Logger, BASE_TYPES } from "@curefit/base"
import { ErrorFactory } from "@curefit/error-client"
import AuthMiddleware from "../../../auth/AuthMiddleware"
import CUREFIT_API_TYPES from "../../../../config/ioc/types"
import { FetchUtil } from "@curefit/base"
import { Constants } from "@curefit/base-utils"
import { ErrorCodes } from "../../../error/ErrorCodes"
const fetch = require("node-fetch")


export function controllerFactory(kernel: Container) {

    @controller("/external/urbanpiper/order", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class UrbanPiperOrderController {

        constructor(
            @inject(BASE_UTILS_TYPES.ApiKeyService) private apiKeyService: IApiKeyService,
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(BASE_TYPES.FetchUtil) private fetchHelper: FetchUtil,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        ) { }

        @httpPost("/place")
        public async placeOrder(req: express.Request) {
            const lookup = req.headers["apikey"] as string
            const apiKey = await this.apiKeyService.getApiKey(lookup)
            if (!apiKey) {
                this.logger.info(`Invalid look up key ${lookup} for /place`)
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid api key").build()
            }
            if (!_.includes(apiKey.actions, "ORDER_PLACE")) {
                this.logger.info(`Invalid look up key ${lookup} for /place`)
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid api key").build()
            }
            const brand = req.headers["brand"]

            try {
                const url = Constants.getAlfredUrl("/external/urbanpiper/order/place")
                const response: any = await fetch(url,
                    this.fetchHelper.post(req.body, { "alfred_client": process.env.APP_NAME, "brand": brand }))
                const resp = await this.fetchHelper.parseResponse<any>(response)
                return resp
            } catch (err) {
                this.logger.error(`Error while placing order: ${JSON.stringify(err)}`)
                throw err
            }
        }

        @httpPost("/cancel")
        public async cancelOrder(req: express.Request) {
            const lookup = req.headers["apikey"] as string
            const apiKey = await this.apiKeyService.getApiKey(lookup)
            if (!apiKey) {
                this.logger.info(`Invalid look up key ${lookup} for /cancel`)
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid api key").build()
            }
            if (!_.includes(apiKey.actions, "ORDER_CANCEL")) {
                this.logger.info(`Invalid look up key ${lookup} for /cancel`)
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid api key").build()
            }

            try {
                const url = Constants.getAlfredUrl("/external/urbanpiper/order/cancel")
                const response: any = await fetch(url,
                    this.fetchHelper.post(req.body, { "alfred_client": process.env.APP_NAME }))
                const resp = await this.fetchHelper.parseResponse<any>(response)
                return resp
            } catch (err) {
                this.logger.error(`Error while order cancel: ${JSON.stringify(err)}`)
                throw err
            }
        }

        @httpPost("/status")
        public async orderStatus(req: express.Request) {
            const lookup = req.headers["apikey"] as string
            const apiKey = await this.apiKeyService.getApiKey(lookup)
            if (!apiKey) {
                this.logger.info(`Invalid look up key ${lookup} for /status`)
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid api key").build()
            }
            if (!_.includes(apiKey.actions, "ORDER_STATUS")) {
                this.logger.info(`Invalid look up key ${lookup} for /status`)
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid api key").build()
            }

            try {
                const url = Constants.getAlfredUrl("/external/urbanpiper/order/status/notify")
                const response: any = await fetch(url,
                    this.fetchHelper.post(req.body, { "alfred_client": process.env.APP_NAME }))
                const resp = await this.fetchHelper.parseResponse<any>(response)
                return resp
            } catch (err) {
                this.logger.error(`Error while order status notify: ${JSON.stringify(err)}`)
                throw err
            }
        }

    }

    return UrbanPiperOrderController
}

export default controllerFactory
