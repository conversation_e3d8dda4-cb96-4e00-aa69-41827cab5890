import { controller, httpPost } from "inversify-express-utils"
import { Container, inject } from "inversify"
import * as express from "express"
import * as _ from "lodash"

import { IApiKeyService, BASE_UTILS_TYPES } from "@curefit/base-utils"
import { Logger, BASE_TYPES } from "@curefit/base"
import { ErrorFactory } from "@curefit/error-client"
import AuthMiddleware from "../../../auth/AuthMiddleware"
import CUREFIT_API_TYPES from "../../../../config/ioc/types"
import { FetchUtil } from "@curefit/base"
import { Constants } from "@curefit/base-utils"
import { ErrorCodes } from "../../../error/ErrorCodes"
const fetch = require("node-fetch")


export function controllerFactory(kernel: Container) {

    @controller("/external/kitchens_at", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class KitchensAtController {

        constructor(
            @inject(BASE_UTILS_TYPES.ApiKeyService) private apiKeyService: IApiKeyService,
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(BASE_TYPES.FetchUtil) private fetchHelper: FetchUtil,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        ) { }

        @httpPost("/order/place")
        public async placeOrder(req: express.Request) {
            const apiKey = await this.apiKeyService.getApiKey(req.headers["apikey"] as string)
            if (!apiKey) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid api key").build()
            }
            if (!_.includes(apiKey.actions, "ORDER_PLACE")) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid api key").build()
            }
            const brand = req.headers["brand"]
            return fetch(Constants.getAlfredUrl("/external/kitchensAt/order/place"), this.fetchHelper.post(
                req.body, { "alfred_client": process.env.APP_NAME, "brand": brand })).then((response: any) => {
                    return this.fetchHelper.parseResponse<any>(response)
                }).catch((err: any) => {
                    throw err
                })
        }

        @httpPost("/order/cancel")
        public async cancelOrder(req: express.Request) {
            const apiKey = await this.apiKeyService.getApiKey(req.headers["apikey"] as string)
            if (!apiKey) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid api key").build()
            }
            if (!_.includes(apiKey.actions, "ORDER_CANCEL")) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid api key").build()
            }
            return fetch(Constants.getAlfredUrl("/external/kitchensAt/order/cancel"), this.fetchHelper.post(
                req.body, { "alfred_client": process.env.APP_NAME })).then((response: any) => {
                    return this.fetchHelper.parseResponse<any>(response)
                }).catch((err: any) => {
                    throw err
                })
        }

        @httpPost("/order/status")
        public async orderStatus(req: express.Request) {
            const apiKey = await this.apiKeyService.getApiKey(req.headers["apikey"] as string)
            if (!apiKey) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid api key").build()
            }
            if (!_.includes(apiKey.actions, "ORDER_STATUS")) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid api key").build()
            }
            return fetch(Constants.getAlfredUrl("/external/kitchensAt/order/status/notify"), this.fetchHelper.post(
                req.body, { "alfred_client": process.env.APP_NAME })).then((response: any) => {
                    return this.fetchHelper.parseResponse<any>(response)
                }).catch((err: any) => {
                    throw err
                })
        }

    }

    return KitchensAtController
}

export default controllerFactory
