import { Container, inject } from "inversify"
import { controller, httpPost } from "inversify-express-utils"
import * as _ from "lodash"

import { Logger, BASE_TYPES } from "@curefit/base"
import { IApiKeyService, BASE_UTILS_TYPES } from "@curefit/base-utils"
import { ErrorFactory } from "@curefit/error-client"
import { Constants } from "@curefit/base-utils"
import { FetchUtil } from "@curefit/base"
import CUREFIT_API_TYPES from "../../../../config/ioc/types"
import AuthMiddleware from "../../../auth/AuthMiddleware"
import { ErrorCodes } from "../../../error/ErrorCodes"
const fetch = require("node-fetch")

export function ExtLogisticsControllerFactory(kernel: Container) {

    @controller("/external/logistics", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class ExtLogisticsController {

        constructor(
            @inject(BASE_UTILS_TYPES.ApiKeyService) private apiKeyService: IApiKeyService,
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(BASE_TYPES.FetchUtil) private fetchHelper: FetchUtil,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        ) {
        }

        @httpPost("/order/status/update")
        public async updateOrderStatus(req: any) {

            let key = <string>req.headers["apikey"]
            if (req.query.apikey) {
                key = req.query.apikey
            }
            const apiKey = await this.apiKeyService.getApiKey(key)
            if (!apiKey) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid api key").build()
            }
            if (!_.includes(apiKey.actions, "ORDER_STATUS_UPDATE")) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Operation not allowed").build()
            }

            return fetch(Constants.getFlashURL("/external/logistics/order/status/update"),
                this.fetchHelper.post(req.body, { provider: apiKey.partner })).then((response: any) => {
                    return this.fetchHelper.parseResponse<any>(response)
                }).catch((err: any) => {
                    this.logger.error(`Error during order status update`, err)
                    return { status: false }
                })
        }

        @httpPost("/callcartshipment/:cartShipmentId/:pilotPhone")
        public async callCartShipment(req: any) {
            let key = <string>req.headers["apikey"]
            if (req.query.apikey) {
                key = req.query.apikey
            }
            const apiKey = await this.apiKeyService.getApiKey(key)
            if (!apiKey) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid api key").build()
            }

            if (!_.includes(apiKey.actions, "ORDER_PILOT_CUSTOMER_CALL")) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Operation not allowed").build()
            }
            const cartShipmentId = req.params.cartShipmentId
            const pilotPhone = req.params.pilotPhone
            this.logger.info(`Got request ${apiKey.partner} /callcartshipment/${cartShipmentId}/${pilotPhone}`)
            return fetch(Constants.getFlashURL(`/lots/callcartshipment/${cartShipmentId}/${pilotPhone}/${apiKey.partner}`),
                this.fetchHelper.post({}, { provider: apiKey.partner })).then((response: any) => {
                    return this.fetchHelper.parseResponse<any>(response)
                }).catch((err: any) => {
                    this.logger.error(`Error during pilot call`, err)
                    return { status: false }
                })
        }


        @httpPost("/pilot/location/update")
        public async updatePilotLocation(req: any) {
            let key = <string>req.headers["apikey"]
            if (req.query.apikey) {
                key = req.query.apikey
            }
            const apiKey = await this.apiKeyService.getApiKey(key)
            if (!apiKey) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid api key").build()
            }

            if (!_.includes(apiKey.actions, "PILOT_LOCATION_UPDATE")) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Operation not allowed").build()
            }
            return fetch(Constants.getFlashURL("/external/logistics/pilot/location/update"),
                this.fetchHelper.post(req.body, { provider: apiKey.partner })).then((response: any) => {
                    return this.fetchHelper.parseResponse<any>(response)
                }).catch((err: any) => {
                    this.logger.error(`Error during location update`, err)
                    return { status: false }
                })
        }

        @httpPost("/pilot/info/update")
        public async updatePilotInfo(req: any) {
            let key = <string>req.headers["apikey"]
            if (req.query.apikey) {
                key = req.query.apikey
            }
            const apiKey = await this.apiKeyService.getApiKey(key)
            if (!apiKey) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid api key").build()
            }

            if (!_.includes(apiKey.actions, "PILOT_INFO_UPDATE")) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Operation not allowed").build()
            }
            return fetch(Constants.getFlashURL("/external/logistics/pilot/info/update"),
                this.fetchHelper.post(req.body, { provider: apiKey.partner })).then((response: any) => {
                    return this.fetchHelper.parseResponse<any>(response)
                }).catch((err: any) => {
                    this.logger.error(`Error during info update`, err)
                    return { status: false }
                })
        }
    }

    return ExtLogisticsController
}

export default ExtLogisticsControllerFactory
