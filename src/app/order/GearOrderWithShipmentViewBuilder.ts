import { injectable, inject } from "inversify"
import * as _ from "lodash"
import {
    GearOrderDetailObject,
    TrackShipmentResponseV2,
    GearImage
} from "@curefit/gear-common"
import { BaseOrder } from "@curefit/order-common"
import { PaymentData, PaymentChannel, PaymentMode } from "@curefit/payment-common"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import GearShipmentViewBuilderV2, { GearShipmentStatus } from "./GearShipmentViewBuilderV2"
import { BASE_TYPES, Logger } from "@curefit/base"
import {
    GearLineItemsWidget, GearOrderDetail,
    GearOrderDetailsWidget,
    GearOrderTrackingWidgetV2,
    IPriceDetail, PriceDetailsWidget, PaymentDetail, AddonOfferCalloutWidget, AddonOfferWidgetData
} from "../common/views/GearWidgetView"
import { WidgetView, Action, InfoWidget } from "../common/views/WidgetView"
import { GearUtil } from "@curefit/gearvault-client"
import { GearProductState, GearProductStates, IssueProductType } from "@curefit/issue-common"
import { Constants } from "@curefit/base-utils"
import { UserContext } from "@curefit/userinfo-common"
import { TimeUtil, PriceUtil } from "@curefit/util-common"
import OrderViewBuilder, { PriceComponent } from "./OrderViewBuilder"
import { ActionUtil } from "@curefit/base-utils"
import { ActionUtil as GearActionUtil } from "../util/ActionUtil"
import { PaymentUtil } from "@curefit/payment-models"
import AppUtil from "../util/AppUtil"
import { User } from "@curefit/user-common"
import { OfferV2 } from "@curefit/offer-common"
import { IOfferService, OFFER_SERVICE_CLIENT_TYPES } from "@curefit/offer-service-client"
import { CouponUtil } from "../coupon/CouponUtil"
import { CouponInfoWidget } from "@curefit/apps-common"
import { COUPON_DISPATCH_DELAY } from "../gear/constants"
import { IConsumablesService, REWARD_CLIENT_TYPES } from "@curefit/reward-client"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { ISegmentService } from "@curefit/vm-models"

enum ShipmentType {
    FORWARD = "forward",
    RETURN = "return"
}

const DISABLED_IU_STATES = [
    "rto_initiated",
    "received",
    "picked_up",
    "exchange_initiated",
    "return_initiated",
    "pickup_canceled",
    "canceled"
]

export interface InventoryUnit {
    id: number
    shipmentId?: number
    title: string
    optionsText?: string
    price: { mrp: string, listingPrice: string }
    priceDetails?: PriceComponent[]
    images?: GearImage
    shipmentStatuses?: GearShipmentStatus[]
    inventoryStatus?: string
    productId?: number
    line_item_id?: number
    disabled?: boolean
    exchanged?: boolean
    cancelable?: boolean
    returnable?: boolean
    returnableBy?: string
    summary?: string
    issuesBaseUrl?: string
    issuesParam?: string
    feedbackId?: string
    size?: string
    sku?: string
    installation_text?: string
}

interface RewardData {
    offer: OfferV2
    isEligible: boolean
    amount?: number
}
@injectable()
export default class GearOrderWithShipmentViewBuilder {
    private iosVersion = 7.26
    private androidVersion = 7.26

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(CUREFIT_API_TYPES.GearShipmentViewBuilderV2) protected shipmentView: GearShipmentViewBuilderV2,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferService) private offerServiceV1: IOfferService,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(REWARD_CLIENT_TYPES.IConsumablesService) protected consumablesService: IConsumablesService,
        @inject(CUREFIT_API_TYPES.OrderViewBuilder) private orderViewBuilder: OrderViewBuilder,
        @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService) {
    }

    public async buildOrderDetails(order: BaseOrder, gearOrder: GearOrderDetailObject, userContext: UserContext, gearDetails: GearOrderDetailObject, rewardsData?: RewardData[]): Promise<GearOrderDetail> {
        const paymentDetail = this.getPaymentDetail(order.payments)
        const lineItems = _.keyBy(gearOrder.line_items, "id")
        const discount = order.priceDetails.discount
        const totalPayable = order.priceDetails.total_payable
        const totalAmountPayable = order.totalAmountPayable
        const timezone = userContext.userProfile.timezone
        const redirectionUrl = AppUtil.getRedirectionUrlForStoreWebview()
        const inventoryUnits: InventoryUnit[] = _(gearOrder.shipments)
            .map(shipment => {
                const gearShipment: TrackShipmentResponseV2 = { ...shipment, state_change_history: shipment.history }
                const gearShipmentStatusTimeline: GearShipmentStatus[] = this.shipmentView.getShipmentStatusTimeline(
                    gearShipment, timezone
                )
                const issueState = this.shipmentView.getIssueState(gearShipment)

                let inventoryUnits: any
                switch (gearShipment.shipment_type) {
                    case ShipmentType.RETURN:
                        inventoryUnits = shipment.return_inventory_units
                        break
                    case ShipmentType.FORWARD:
                    default:
                        inventoryUnits = _.reject(shipment.inventory_units, "return_shipment_id")
                        break
                }

                return _.map(inventoryUnits, inventoryUnit => {
                    const product = lineItems[inventoryUnit.line_item_id]
                    this.logger.info(
                        "For inventoryUnitId: " + inventoryUnit.id + ": Got the inventoryUnit state as: " + inventoryUnit.state
                    )
                    const disabledCancelStates = ["shipped", "return_approved"]
                    if (!AppUtil.isGearShippedItemCancelationSupported(userContext) && _.includes(disabledCancelStates, inventoryUnit.state)) {
                        inventoryUnit.cancelable = false
                    }
                    let modifyAction: Action
                    if (inventoryUnit.cancelable) {
                        modifyAction = {
                            actionType: "NAVIGATION",
                            title: "Cancel",
                            url: AppUtil.isGearShippedItemCancelationSupported(userContext) ? ActionUtil.gearCancelGetUrlByInventoryUnitId(inventoryUnit.id) : ActionUtil.gearCancelGetUrl(inventoryUnit.line_item_id)
                        }
                    } else if (inventoryUnit.returnable && inventoryUnit.replaceable) {
                        modifyAction = {
                            actionType: "NAVIGATION",
                            title: "EXCHANGE/RETURN",
                            url: ActionUtil.gearReturnOrReplaceGetUrl(inventoryUnit.id, gearDetails.payment_method as string)
                        }
                    } else if (inventoryUnit.returnable) {
                        modifyAction = {
                            actionType: "NAVIGATION",
                            title: "RETURN",
                            url: ActionUtil.gearReturnOrReplaceGetUrl(inventoryUnit.id, gearDetails.payment_method as string)
                        }
                    } else if (inventoryUnit.replaceable) {
                        modifyAction = {
                            actionType: "NAVIGATION",
                            title: "EXCHANGE",
                            url: ActionUtil.gearReturnOrReplaceGetUrl(inventoryUnit.id, gearDetails.payment_method as string)
                        }
                    }
                    let action
                    if (userContext.sessionInfo.appVersion >= 10.36) {
                        action = {
                            actionType: "SSO_STORE_WEB",
                            url: `${redirectionUrl}/order/${order.orderId}/${inventoryUnit.id}`,
                            meta: {
                                fromFlutter: true
                            }
                        }
                    } else {
                        action = {
                            actionType: "NAVIGATION",
                            title: "",
                            url: ActionUtil.gearTrackingV2Url(inventoryUnit.return_shipment_id || shipment.id, inventoryUnit.id)
                        }
                    }
                    const pdpAction: Action = {
                        actionType: "NAVIGATION",
                        title: "",
                        url: ActionUtil.gearProduct(product.variant.product_id.toString(), undefined, AppUtil.isCultSportWebApp(userContext) ? product?.variant?.slug : undefined)
                    }
                    const images: GearImage = _.isArray(product.variant.images) ? _.first(product.variant.images) : product.variant.images

                    const isGearBrandNameSupported = AppUtil.isGearBrandNameSupported(userContext)
                    const finalInstallationCancelationText = AppUtil.getInstallationCancellationInfoText(inventoryUnit.installation_text, inventoryUnit.non_cancelable_text)
                    return {
                        id: inventoryUnit.id,
                        shipmentId: shipment.id,
                        productId: product.variant.product_id,
                        line_item_id: product.id,
                        title: isGearBrandNameSupported || product.variant.brand == null ? product.variant.name : `${product.variant.brand.toUpperCase()} ${product.variant.name}`,
                        brand: product.variant.brand,
                        size: product.variant.size,
                        sku: product.variant.sku,
                        optionsText: product.variant.options_text,
                        inventoryStatus: inventoryUnit.status,
                        disabled: _.includes(DISABLED_IU_STATES, inventoryUnit.state),
                        exchanged: !!inventoryUnit.is_replacement,
                        price: {
                            mrp: product.discount ? PriceUtil.formatMoney(product.display_price) : "",
                            listingPrice: PriceUtil.formatMoney(product.total)
                        },
                        priceDetails: [
                            {
                                title: "Price",
                                value: PriceUtil.formatMoney(product.display_price)
                            },
                            ...(product.discount
                                ? [
                                    {
                                        title: "Discount (-)",
                                        value: PriceUtil.formatMoney(product.discount)
                                    }
                                ]
                                : []),
                            ...(product.fitcash_discount
                                ? [
                                    {
                                        title: "Fitcash Discount (-)",
                                        value: PriceUtil.formatMoney(product.fitcash_discount)
                                    }
                                ]
                                : []),
                            {
                                title: "Total",
                                value: PriceUtil.formatMoney(product.total)
                            }
                        ],
                        images: {
                            mini_url: _.isEmpty(product.variant.images) ? "" : images.mini_url,
                            product_url: _.isEmpty(product.variant.images) ? "" : images.product_url,
                            small_url: _.isEmpty(product.variant.images) ? "" : images.small_url,
                            large_url: _.isEmpty(product.variant.images) ? "" : images.large_url
                        },
                        cancelable: inventoryUnit.cancelable,
                        returnable: inventoryUnit.returnable,
                        returnableBy: inventoryUnit.returnable_by,
                        summary: inventoryUnit.summary,
                        shipmentStatuses: inventoryUnit.state === "canceled" ? null : gearShipmentStatusTimeline,
                        feedbackId: inventoryUnit.feedback_id,
                        installationText : !_.isEmpty(finalInstallationCancelationText) ? finalInstallationCancelationText : undefined,
                        pdpAction,
                        action,
                        modifyAction
                    } as InventoryUnit
                })
            })
            .flatten()
            .compact()
            .value()

        const price: IPriceDetail = {
            mrp: totalAmountPayable,
            listingPrice: totalAmountPayable
        }

        let itemCount = 0
        gearOrder.shipments.forEach(shipment => {
            itemCount += shipment.inventory_units.length
        })
        const title: string = `${itemCount} items ordered`
        let activityType: string = "CULT_GEAR_ECOMMERCE"
        let subActivityType: string
        if (AppUtil.isStoreTabSupported(userContext)) {
            activityType = "STORE"
            subActivityType = "CULT_GEAR_ECOMMERCE"
        }
        const orderDate: string = TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, new Date(gearOrder.created_at), "Do MMMM YYYY, h:mm a")

        const widgets: WidgetView[] = [
            new GearLineItemsWidget(gearOrder.line_items),
            new GearOrderTrackingWidgetV2(gearOrder, order, paymentDetail, inventoryUnits),
            // TODO : Enable this back once cult gear team adds invoice flow in alfred
            // this.getInvoiceWidget(await userContext.userPromise, order.orderId)
        ]

        if (!_.isEmpty(rewardsData)) {
            // Coupons/Fitcash are dispacthed or scheduled to dispatch when all items in the original order are delivered.
            const forwardShipments = _.filter(gearOrder.shipments, shipment => shipment.shipment_type === ShipmentType.FORWARD && !shipment.is_hand_in_hand_exchange)
            const customerEdd = _.max(forwardShipments.map(shipment => shipment.delivered_at || shipment.customer_edd))
            const addonOfferPromises: Promise<AddonOfferWidgetData>[] = rewardsData.map(reward => {
                return this.getOfferAddonDetails(reward, customerEdd, userContext, gearOrder)
            })
            const addonsData = await Promise.all(addonOfferPromises)
            addonsData.map(addonData => {
                // showing only one of either callout widget or the coupons
                if (!_.isEmpty(addonData?.couponInfo)) {
                    widgets.push(addonData.couponInfo)
                    addonData.couponInfo = undefined
                } else if (!_.isEmpty(addonData)) {
                    widgets.push(
                        new AddonOfferCalloutWidget(addonData, "GEAR_ORDER_ADDON_OFFER_CALLOUT_WIDGET")
                    )
                }
            })
        }

        widgets.push(
            new GearOrderDetailsWidget({
                bill_address: gearDetails.bill_address,
                ship_address: gearDetails.ship_address
            })
        )

        const priceDetails = {
            total_without_discount: (Number(totalPayable.toFixed(2)) + Number(discount)).toFixed(2),
            discount: discount.toFixed(2),
            fitcash: order.totalFitCashPayable.toFixed(2),
            total_payable: order.totalAmountPayable.toFixed(2)
        }

        widgets.push(
            new PriceDetailsWidget(GearUtil.getPriceDetailsComponents(priceDetails, order, true), "GEAR_PRICE_DETAILS_WIDGET")
        )
        this.orderViewBuilder.addNeuPassEarnedCoinsWidget(order, widgets, true)
        const pageToken: Date = gearOrder.created_at ? new Date(gearOrder.created_at) : undefined

        let actions: Action[] = []
        if (GearUtil.isGearIssuesSupported(userContext.sessionInfo.userAgent, userContext.sessionInfo.appVersion, userContext.sessionInfo.osName, this.androidVersion, this.iosVersion)) {
            const productType: IssueProductType = "CULT_GEAR"
            const query = {
                productType: productType,
                productStates: GearProductStates as GearProductState[],
                meta: {
                    orderId: gearOrder.external_service_order_id
                }
            }
            actions = [
                {
                    title: "Need Help",
                    actionType: "NAVIGATION",
                    url: GearActionUtil.getIssuesUrlOld(query)
                }]

        } else {
            actions = [{
                title: "Need Help",
                actionType: "EXTERNAL_DEEP_LINK",
                url: `mailto:${Constants.customerCareMail}?subject=[Gear] Need help with [Order ${gearOrder.external_service_order_id}]`
            }]
        }

        const orderDetail: GearOrderDetail = new GearOrderDetail({
            orderId: gearOrder.external_service_order_id,
            paymentMode: "MANUAL",
            price,
            title,
            activityType,
            subActivityType,
            orderDate,
            widgets,
            refundAmount: 0,
            cashbackAmount: 0,
            pageToken,
            actions
        })
        return orderDetail
    }

    private getInvoiceWidget(user: User, orderId: string) {
        const getInvoiceWidget: InfoWidget = {
            widgetType: "INFO_WIDGET",
            icon: "EMAIL",
            title: "Email",
            subTitle: user.email ? user.email : "Email id is missing",
            actions: [{
                title: "GET INVOICE",
                actionType: "GET_INVOICE",
                meta: {
                    user: user.email,
                    orderId: orderId
                }
            }]
        }
        return getInvoiceWidget
    }

    private getPaymentDetail(payments: PaymentData[]): PaymentDetail {
        const successfulPayment: PaymentData = payments.find(payment => {
            const paymentMode: PaymentMode = _.get(payment, "data.selectedPaymentMode", undefined)
            return (
                PaymentUtil.isCodPayment(payment.channel, paymentMode) ||
                payment.status === "adjusted" ||
                payment.status === "paid" ||
                payment.statusHistory.filter(x => x.status === "paid").length > 0
            )
        })
        const refunds = successfulPayment.refunds
        let refundAmount = 0
        let cashbackAmount = 0
        for (let i = 0; i < refunds.length; i++) {
            const refund = refunds[i]
            if (refund.refundType === "CASHBACK") {
                cashbackAmount = cashbackAmount + refund.amount / 100
            } else {
                refundAmount += refund.amount / 100
            }
        }
        if (successfulPayment.cashback) {
            cashbackAmount = cashbackAmount + successfulPayment.cashback.amount / 100
        }
        return {
            refundAmount: refundAmount,
            cashbackAmount: cashbackAmount,
            paymentChannel: successfulPayment ? successfulPayment.channel : null,
            paymentMode: _.get(successfulPayment, "data.selectedPaymentMode", "")
        }
    }

    private async getOfferAddonDetails(rewardData: RewardData , customerEdd: string, userContext: UserContext, gearOrder: GearOrderDetailObject): Promise<AddonOfferWidgetData> {
        const {offer : {addons: [addon]}, isEligible, amount} = rewardData
        const tz = userContext.userProfile.timezone

        // As part of reward automation v2, we will be moving this config offer console.
        const eddInEpoch: number = new Date(customerEdd).getTime()
        const todayInEpoch: number = new Date().getTime()

        const rewardWidgetData = {
            addonType: addon.addonType
        }
        if (!isEligible) {
            return {
                ...rewardWidgetData,
                title: `${addon.description} stands cancelled`,
                message: "As offer conditions are not met"
            }
        }
        if (addon.addonType === "COUPON") {
            const couponDispatchDate = TimeUtil.addDays(tz, customerEdd, COUPON_DISPATCH_DELAY)
            const activationDate = TimeUtil.formatEpochInTimeZone(tz, new Date(couponDispatchDate).getTime(), "DD MMM")
            // Assuming that the dispatch has happened successfully. Ideally, we should check the coupon history
            // and show the status here.
            const showVouchers = couponDispatchDate <= TimeUtil.todaysDate(tz)
            const message = `Voucher code will be sent via SMS & Email on ${activationDate}`
            const couponInfoWidget: CouponInfoWidget = showVouchers
                ? await this.consumablesService.getConsumableForOrder(gearOrder.external_service_order_id).catch(err => {
                    this.logger.error(`Error while fetching consumables for order: ${gearOrder.external_service_order_id}`, err)
                    return undefined
                }).then(consumables => {
                    if (consumables) {
                        return CouponUtil.getCouponsInfoWidget(consumables, userContext, "GEAR")
                    }
                }) : undefined

            return {
                ...rewardWidgetData,
                title: addon.description,
                message,
                couponInfo: couponInfoWidget,
            }
        }

        if (addon.addonType === "FITCASH") {
            const activationDate = TimeUtil.formatEpochInTimeZone(tz, eddInEpoch, "DD MMM")
            // Assuming that the dispatch has happened successfully. Ideally, we should check the fitcash ledger
            // and show the status here.
            const message = eddInEpoch >= todayInEpoch ?
                    `Estimated date of credit: ${activationDate}` :
                    `has been credited on ${activationDate}`
            return {
                ...rewardWidgetData,
                title: `Reward of ${amount / 100} Fitcash`,
                message
            }
        }
        return null
    }
}
