import { inject, injectable } from "inversify"
import {
    GearLineItemsWidget,
    GearOrderDetail,
    GearOrderDetailsWidget,
    GearOrderTrackingWidget,
    IPriceDetail,
    PriceDetailsWidget
} from "../common/views/GearWidgetView"
import { Action, NeuPassEarnedCoinsWidget, WidgetView } from "../common/views/WidgetView"
import { BaseOrder, Order } from "@curefit/order-common"
import { GearOrderDetailObject } from "@curefit/gear-common"
import { GearUtil } from "@curefit/gearvault-client"
import IssueBusiness from "../crm/IssueBusiness"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { Constants } from "@curefit/base-utils"
import { UserContext } from "@curefit/userinfo-common"
import { GearProductState, GearProductStates, IssueProductType } from "@curefit/issue-common"
import { ActionUtil } from "../util/ActionUtil"
import { TimeUtil } from "@curefit/util-common"
import AppUtil from "../util/AppUtil"
import OrderViewBuilder, { PaymentDetail, RefundConfig } from "./OrderViewBuilder"
import * as _ from "lodash"
import { PaymentData, PaymentMode, RefundDetailsResponse } from "@curefit/payment-common"
import { IOrderRefundWidget, OrderRefundWidgetBuilder } from "../common/views/OrderRefundWidget"
import { PaymentUtil } from "@curefit/payment-models"
import OrderUtil from "../util/OrderUtil"
import { IPaymentClient, PAYMENT_TYPES } from "@curefit/payment-client"
import { BASE_TYPES, Logger } from "@curefit/base"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { ISegmentService } from "@curefit/vm-models"

@injectable()
export default class GearOrderViewBuilder {
    private iosVersion = 7.26
    private androidVersion = 7.26

    @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness
    @inject(CUREFIT_API_TYPES.OrderViewBuilder) private orderViewBuilder: OrderViewBuilder
    @inject(PAYMENT_TYPES.IPaymentClient) protected paymentClient: IPaymentClient
    @inject(BASE_TYPES.ILogger) private logger: Logger
    @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces
    @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService

    async buildOrderDetails(gearOrder: GearOrderDetailObject, alfredOrder: BaseOrder, userContext: UserContext, totalPrepaymentAmount: number): Promise<GearOrderDetail> {
        const discount = alfredOrder.priceDetails.discount
        const totalPayable = alfredOrder.priceDetails.total_payable
        const totalAmountPayable = alfredOrder.totalAmountPayable

        const price: IPriceDetail = {
            mrp: totalAmountPayable,
            listingPrice: totalAmountPayable
        }
        let itemCount = 0
        gearOrder.shipments.forEach(shipment => {
            itemCount += shipment.inventory_units.length
        })
        const title: string = `${itemCount} items ordered`
        let activityType: string = "CULT_GEAR_ECOMMERCE"
        let subActivityType: string
        if (AppUtil.isStoreTabSupported(userContext)) {
            activityType = "STORE"
            subActivityType = "CULT_GEAR_ECOMMERCE"
        }
        const orderDate: string = TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, new Date(gearOrder.created_at), "Do MMMM YYYY, h:mm a")

        const widgets: WidgetView[] = [
            new GearLineItemsWidget(gearOrder.line_items),
            new GearOrderTrackingWidget(gearOrder.shipments, gearOrder.payment_method, userContext)
        ]

        widgets.push(
            new GearOrderDetailsWidget({
                bill_address: gearOrder.bill_address,
                ship_address: gearOrder.ship_address
            })
        )

        const priceDetails = {
            total_without_discount: (Number(totalPayable.toFixed(2)) + Number(discount)).toFixed(2),
            discount: discount.toFixed(2),
            fitcash: alfredOrder.totalFitCashPayable.toFixed(2),
            totalPrepaymentAmount: totalPrepaymentAmount.toFixed(2),
            total_payable: (alfredOrder.totalAmountPayable - totalPrepaymentAmount).toFixed(2)
        }

        widgets.push(
            new PriceDetailsWidget(GearUtil.getPriceDetailsComponents(priceDetails, alfredOrder, true), "GEAR_PRICE_DETAILS_WIDGET")
        )
        this.orderViewBuilder.addNeuPassEarnedCoinsWidget(alfredOrder, widgets, true)
        const shipmentState: string = gearOrder.shipments[0].state
        const pageToken: Date = gearOrder.created_at ? new Date(gearOrder.created_at) : undefined

        let actions: Action[] = []
        if (GearUtil.isGearIssuesSupported(userContext.sessionInfo.userAgent, userContext.sessionInfo.appVersion, userContext.sessionInfo.osName, this.androidVersion, this.iosVersion)) {
            const productType: IssueProductType = "CULT_GEAR"
            const query = {
                productType: productType,
                productStates: GearProductStates as GearProductState[],
                meta: {
                    orderId: gearOrder.external_service_order_id
                }
            }
            actions = [
                {
                    title: "Need Help",
                    actionType: "NAVIGATION",
                    url:  ActionUtil.getIssuesUrlOld(query)
                }]

        } else {
            actions = [{
                title: "Need Help",
                actionType: "EXTERNAL_DEEP_LINK",
                url: `mailto:${Constants.customerCareMail}?subject=[Gear] Need help with [Order ${gearOrder.external_service_order_id}]`
            }]
        }


        if (AppUtil.isCultSportWebApp(userContext)) {
            const refundWidgetPromise: Promise<IOrderRefundWidget[]> = this.getRefundDetailsWidgets(userContext, alfredOrder)
            const refundWidgets: IOrderRefundWidget[] = await refundWidgetPromise
            if (refundWidgets) {
                widgets.push(...refundWidgets)
            }
        }
        const orderDetail: GearOrderDetail = new GearOrderDetail({
            orderId: gearOrder.external_service_order_id,
            paymentMode: "MANUAL",
            price,
            title,
            activityType,
            subActivityType,
            orderDate,
            widgets,
            refundAmount: 0,
            cashbackAmount: 0,
            pageToken,
            actions
        })
        return orderDetail
    }

    private async getRefundDetailsWidgets(userContext: UserContext, order: BaseOrder): Promise<IOrderRefundWidget[]> {
        const paymentDetail: PaymentDetail = this.getPaymentDetail(order.payments)
        let refundDetailsPromise: Promise<RefundDetailsResponse[]>

        if (_.isNil(paymentDetail) || _.isNil(paymentDetail.successfulPaymentId) || _.isNil(order)) {
            return undefined
        }
        if (!_.isNil(paymentDetail.successfulPaymentId)) {
            refundDetailsPromise = this.paymentClient.getRefundDetails(
                order.orderId,
                paymentDetail.successfulPaymentId,
                paymentDetail.paymentChannel
            )
        }
        const paymentData: PaymentData = PaymentUtil.findPaymentByPaymentId(order, paymentDetail.successfulPaymentId, paymentDetail.paymentChannel)
        if (_.isNil(paymentData)) {
            return undefined
        }
        const orderRefundWidgetBuilder: OrderRefundWidgetBuilder = new OrderRefundWidgetBuilder()
        return orderRefundWidgetBuilder.getOrderRefundWidgetList({
            userContext: userContext,
            orderId: order.orderId,
            paymentId: paymentData.paymentId,
            paymentChannel: paymentData.channel,
            refundDetailsPromise: refundDetailsPromise,
            logger: this.logger
        })
    }

    getPaymentDetail(payments: PaymentData[]): PaymentDetail {
        const successfulPayment: PaymentData = payments.find(payment => {
            const paymentMode: PaymentMode = _.get(payment, "data.selectedPaymentMode", undefined)
            return PaymentUtil.isCodPayment(payment.channel, paymentMode)
                || payment.status === "adjusted"
                || payment.status === "paid"
                || payment.channel === "MANUAL"
                || (!_.isNil(payment.statusHistory) && payment.statusHistory.filter((x) => (x.status === "paid")).length > 0)
            // Adding the (!_.isNil(payment.statusHistory)) check because some old orders from 2017 do not have statusHistory
        })
        if (_.isNil(successfulPayment)) return undefined
        const refunds = successfulPayment.refunds
        let refundAmount = 0
        let cashbackAmount = 0
        _.forEach(refunds, refund => {
            if (refund.refundType === "CASHBACK") {
                cashbackAmount = cashbackAmount + (refund.amount / 100)
            } else {
                refundAmount += refund.amount
            }
        })
        refundAmount /= 100
        if (successfulPayment.cashback) {
            cashbackAmount = cashbackAmount + (successfulPayment.cashback.amount / 100)
        }
        return {
            refundAmount: refundAmount,
            cashbackAmount: cashbackAmount,
            paymentChannel: successfulPayment ? OrderUtil.getFormattedPaymentChannel(successfulPayment.channel) : null,
            successfulPaymentId: successfulPayment ? successfulPayment.paymentId : undefined,
            paymentMode: successfulPayment?.data?.selectedPaymentMode
        }
    }
}