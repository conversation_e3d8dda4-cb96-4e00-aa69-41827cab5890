import { inject, injectable } from "inversify"
import { CATALOG_CLIENT_TYPES, CatalogueServiceV2Utilities, ICatalogueService, ICatalogueServicePMS } from "@curefit/catalog-client"
import {
    ALFRED_CLIENT_TYPES,
    FoodBooking,
    IFulfilmentService,
    IShipmentService
} from "@curefit/alfred-client"
import { DELIVERY_CLIENT_TYPES, IDeliverySlotService, IGateService } from "@curefit/delivery-client"
import { ConsultationProduct, DiagnosticProduct, Doctor, GroupClassProduct } from "@curefit/care-common"
import {
    AccessLevel,
    Campaign,
    CultCenter,
    CultClass,
    CultPackType,
    CultWorkout,
    FitnessEvent,
    MembershipTransferDetailResponse,
    PulseProduct,
    User
} from "@curefit/cult-common"
import {
    ActivityType,
    DeliveryCharge,
    ExtraCharge,
    HealthfaceTenant,
    ProductBilling,
    ProductPrice,
    ProductType,
    ProgramPackProduct,
    TippingCharge,
    UrlPathBuilder
} from "@curefit/product-common"
import { CustomerIssueType, IssueCategory } from "@curefit/issue-common"
import {
    DeliverySlot,
    FoodPack,
    FoodProduct as Product,
    FoodShipmentStatus,
    ListingBrandIdType,
    UserDeliveryAddress
} from "@curefit/eat-common"
import { UserAddress } from "@curefit/user-common"
import { Tenant, UserAgentType as UserAgent, Vertical } from "@curefit/base-common"
import {
    BaseOrder,
    CultMembershipMetadata,
    CultMindMembershipUpgradeMetadata,
    CultPackTransferMetadata,
    FoodFulfilment,
    isMembershipUpgradeClientMetadata,
    Order,
    OrderProduct,
    OrderProductSnapshots
} from "@curefit/order-common"
import { PaymentChannel, PaymentData, PaymentMode, RefundDetailsResponse } from "@curefit/payment-common"
import IProductBusiness from "../product/IProductBusiness"
import {
    Action as AppAction,
    ActionType,
    AlertInfo,
    AppointmentDetailsWidget,
    CareCheckoutInfoItemWidget,
    CheckoutInfoItem,
    CouponInfoWidget,
    HeaderWidget,
    OrderCheckoutDetail,
    orderType,
    ProductInfo,
    ProductOffer,
    ProductOfferWidgetV2,
    TncDetails,
    WidgetView as AppWidgetView,
    MembershipUpgradeSummaryWidget,
    MembershipPostUpdateStatusWidget,
    MembershipFeesDetailWidget,
    MembershipFeeInfoLineItemWidget,
    OfferData
} from "@curefit/apps-common"

import {
    Action,
    ActionCard,
    ActionPageWidgetView,
    AddOnProductWidget,
    CareCheckoutSummaryWidget,
    CartBaseProductSummaryWidget,
    CartDiagnosticsCheckoutSummaryWidget,
    DescriptionWidget,
    Header,
    HorizontalScrollCardWidget,
    InfoCard,
    InfoWidget,
    InstructionsWidget,
    ManageOptionPayload,
    MealCardItem,
    MembershipTransferSummeryWidget,
    NeuPassEarnedCoinsWidget, OfferCalloutWidget, ProductListWidget,
    SfExperienceCenteFullBodyTestWidget,
    SfExperienceCenterWorkFlowWidget,
    TestBookingSummaryWidget,
    TransferFeeSummaryWidget,
    WidgetType,
    WidgetView
} from "../common/views/WidgetView"
import { BillingInfo, RUPEE_SYMBOL } from "@curefit/finance-common"
import * as momentTz from "moment-timezone"
import { capitalizeFirstLetter, eternalPromise, pad, TimeUtil } from "@curefit/util-common"
import { BASE_TYPES, Logger } from "@curefit/base"
import CultUtil, {
    DEFAULT_IMAGE_URL,
    ENABLE_CENTER_SELECTION_FOR_PACK,
    isUpgradeMembershipOrder
} from "../util/CultUtil"
import { MealUtil, OrderUtil as EtherOrderUtil } from "@curefit/base-utils"
import EatUtil from "../util/EatUtil"
import OrderUtil, {
    CHECKOUT_TIMER_PROD_WIDGET_ID,
    CHECKOUT_TIMER_STAGE_WIDGET_ID,
    GST_RATE_INSTANT_DISCOUNT
} from "../util/OrderUtil"
import { SlotUtil } from "@curefit/eat-util"
import { CULT_CLIENT_TYPES, ICultServiceOld as ICultService } from "@curefit/cult-client"
import { PackStatus } from "../user/TimelineView"
import UserAddressView from "../user/UserAddressView"
import ICRMIssueService from "../crm/ICRMIssueService"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import * as _ from "lodash"
import {
    ALBUS_CLIENT_TYPES,
    BookingDetail,
    Center,
    ConsultationOrderResponse,
    ConsultationSellableProduct,
    DiagnosticsTestOrderResponse,
    HealthfaceProductInfo,
    IHealthfaceService,
    Patient
} from "@curefit/albus-client"
import OrderProductDetailBuilder, { ItemDetail, OrderProductDetail } from "./OrderProductDetailBuilder"
import OrderTrackingStatusWidget from "../common/views/OrderTrackingStatusWidget"
import { UserContext } from "@curefit/userinfo-common"
import careUtil, { CareUtil } from "../util/CareUtil"
import { Orientation } from "@curefit/vm-common"
import { CacheHelper } from "../util/CacheHelper"
import AppUtil, {
    CARE_COLLAPSIBLE_OFFER_SUPPORTED,
    CFTextData,
    CREDIT_PILL_ICON,
    NAVI_MUMBAI_AND_THANE_CITY_ID,
    REST_OF_MUMBAI_CITY_ID,
    SLOT_TIMER_WIDGET_SUPPORTED,
    SUPPORT_DEEP_LINK
} from "../util/AppUtil"
import {
    IOfferService,
    IOfferServiceV2,
    OFFER_SERVICE_CLIENT_TYPES,
    OfferServiceV3
} from "@curefit/offer-service-client"
import { Consumable, OfferV2 } from "@curefit/offer-common"
import { IOfferAddonData, IOfferAddonWidgetParams, OfferAddonWidget } from "../common/views/OfferAddonWidget"
import { City } from "@curefit/location-common"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { EmiInterestReadonlyDaoMongoImpl, PAYMENT_MODELS_TYPES, PaymentUtil } from "@curefit/payment-models"
import { IUserSegmentClient, SEGMENTATION_CLIENT_TYPES } from "@curefit/segmentation-service-client"
import { CENTER_SERVICE_CLIENT_TYPES, ICenterService } from "@curefit/center-service-client"
import { OfferHelper, OMS_API_CLIENT_TYPES,  FulfillmentTimeline, OrderUtil as OMSOrderUtil, IOrderService as IOmsApiClient, EnhancedFulfillmentResult } from "@curefit/oms-api-client"
import { CouponUtil } from "../coupon/CouponUtil"
import {
    GymfitAccessLevel,
    LuxFitnessProduct, SubAccessLevel,
    SubLevelAccessListing,
    UpgradePricingRequest
} from "@curefit/gymfit-common"
import { IBaseWidget, ISegmentService } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import UpgradeMembershipViewBuilder from "../cult/UpgradeMembershipViewBuilder"
import { IOrderRefundWidget, OrderRefundWidgetBuilder } from "../common/views/OrderRefundWidget"
import { IPaymentClient, PAYMENT_TYPES } from "@curefit/payment-client"
import { CFLiveProduct } from "@curefit/diy-common"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import LiveUtil from "../util/LiveUtil"
import { SimpleWod } from "@curefit/fitness-common"
import { HERCULES_CLIENT_TYPES, IHerculesService } from "@curefit/hercules-client"
import WodViewBuilder from "../cult/WodViewBuilder"
import { ConfigService, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import { HamletConfigRequest } from "@curefit/hamlet-common"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { Membership } from "@curefit/membership-commons"
import { ActionUtil } from "../util/ActionUtil"
import { IDENTITY_CLIENT_TYPES, IIdentityService } from "@curefit/identity-client"
import { IOllivanderCityService, OLLIVANDER_CLIENT_TYPES } from "@curefit/ollivander-node-client"
import { IdentityResponse } from "@curefit/identity-common"
import { DoctorAssetsResponse } from "@curefit/ollivander-node-client/dist/src/OllivanderCityService"
import CartViewBuilder from "../cart/CartViewBuilder"
import GymfitUtil from "../util/GymfitUtil"
import {
    IThirdPartyService,
    PotentialTataPointsEarnResponse,
    TataUtil,
    THIRD_PARTY_CLIENT_TYPES
} from "@curefit/third-party-integrations-client"
import { TataNeuUtil } from "../util/TataNeuUtil"
import { GYMFIT_CLIENT_TYPES, IGymfitService } from "@curefit/gymfit-client"
import { HAMLET_CLIENT_TYPES, IHamletService } from "@curefit/hamlet-client"
import { IConsumablesService, REWARD_CLIENT_TYPES } from "@curefit/reward-client"
import PlayUpgradeDetailViewBuilder from "../pack/PlayUpgradeDetailViewBuilder"
import { PaymentCompletionHandler } from "@curefit/payment-common/dist/src/Payment"
import { GymfitProductType } from "@curefit/gymfit-common/dist/src/Listing"
import { AugmentedOfflineFitnessPack, BenefitEntry, Namespace, OfflineFitnessPack, ProductSubType, PurchaseFlow } from "@curefit/pack-management-service-common"
import { PACK_CLIENT_TYPES, IOfflineFitnessPackService } from "@curefit/pack-management-service-client"
import { GearService, GEARVAULT_CLIENT_TYPES } from "@curefit/gearvault-client"
import { GearOrderDetailObject, ShipmentType } from "@curefit/gear-common"
import { IAppConfigStoreService } from "../appConfig/AppConfigStoreService"
import CatalogueServiceUtilities from "../util/CatalogueServiceUtilities"
import Any = jasmine.Any
import OrderUtilV1 from "../util/OrderUtil"
import { ExhaustivePackBenefit } from "@curefit/pack-management-service-common/src/requests"
import WidgetBuilder from "../page/vm/WidgetBuilder"
import { GYM_PT_MUSCLE_BLAZE_BANNER_WIDGET_SECOND } from "../common/Constants"

const moment = require("moment")

export interface OrderMeta {
    orderId: string
    title?: string
    cookingInstructions?: string
    displayText?: string
    subTitle?: string
    price: ProductPrice
    customerName: string
    customerPhone: string
    customerEmail: string
    orderType: orderType
    productType?: ProductType
    vertical?: Vertical,
    footerText?: string
    productId?: string
    totalAmountPayable?: number,
    totalFitCashPayable?: number,
    productInfoList?: ProductInfo[],
    isTransferredMembership?: boolean,
    isUpgradeMembership?: boolean,
    sourceCityName?: string
}

export interface RefundConfig {
    refundAmount: number
    refundStatus: "in_progress" | "complete"
}

export interface PaymentDetail {
    refundAmount: number,
    cashbackAmount: number,
    paymentChannel: PaymentChannel,
    successfulPaymentId: string,
    paymentMode?: string,
    paymentCompletionHandler?: PaymentCompletionHandler
}

export interface OrderDetail extends OrderProductDetail {
    orderId: string
    price: ProductPrice
    amountPaid: Number
    activityType: ActivityType
    subActivityType?: string
    packStatus?: PackStatus
    orderDate: Date
    deliveryDate?: string
    deliverySlot?: DeliverySlot
    paymentMode: string
    refundAmount?: number
    inventoryUnitId?: number
    orderClickAction?: Action
    cashbackAmount?: number
    reportData: {
        action: string
        meta: any
    }
    widgets: AppWidgetView[]
    pageToken: string
    expectedDeliveryTime?: string
    orderStatus?: string
    refundConfig: RefundConfig
    orderStatusText?: string
    orderStatusColor?: string
    orderStatusImage?: string
}

export interface OrderWidgetData extends OrderProductDetail {
    orderId: string
    price: ProductPrice
    amountPaid: Number,
    activityType: ActivityType
    orderStatus?: string,
    orderDate: Date
}

export type ThirdPartyTenant = "TATA"

export interface ThirdPartyDetails {
    tenant: ThirdPartyTenant
    pointsBurned: number
    pointsToBeEarned: number
}


export interface OrderInfo {
    orderId: string
    orderDate: Date
    customerName: string
    customerPhone: string
    customerEmail: string
    paymentMode: string
    deliverySlot: string
    deliveryAddress: UserAddressView
    product: OrderProductInfo
    orderTracking: OrderTrackingStatusWidget
    orderStatus: FoodShipmentStatus | "NOT_STARTED" | "CANCELLED"
}

export interface OrderProductInfo {
    title: string
    subTitle: string
    image: string
    mrp: number
    discount?: number
    amountPayable: number
}

export interface PriceComponent {
    title: string
    value: string,
    isEnabled?: boolean,
    isFitcashPrice?: boolean,
    offerText?: string,
    isValueStrikeOff?: boolean,
    fitcashBalance?: number,
    isTotalWithoutFitcash?: boolean,
    valueWithCurrency?: string,
    symbol?: string,
    isDiscount?: boolean,
    action?: Action,
    showBottomDivider?: boolean,
    prefixIcon?: string
}

export interface SfEcommerceProduct {
    productCode: string,
    productCategory: string
    productSubCategory: string,
    title: string,
    images: any,
    description: string,
    unitQuantity: number,
    unit: string,
    priceDetails: {
      mrp: number,
      listingPrice: number,
      currency: string,
    },
    isAvailable: boolean,
    maxOrderQuantity: number,
    availableQuantity: number,
  }

export interface OrderViewBuilderParams {
    userContext: UserContext
    order: Order
    product: Product
    listingBrand?: ListingBrandIdType
    paymentDetail?: PaymentDetail
    alertInfo?: AlertInfo
    apiType?: "ORDER_CHECKOUT" | "ORDER_V1"
    transferMembershipResponse?: MembershipTransferDetailResponse | null,
    membershipTransferDetails?: any,
    selectTransferMembershipResponse?: any,
    allPossibleActionsForFoodBooking?: Action[],
    foodBooking?: FoodBooking,
    orderDetail?: OrderDetail,
    consumables?: Consumable,
    refundDetailsPromise?: Promise<RefundDetailsResponse[]>,
    appConfigStore?: IAppConfigStoreService
}

const paymentModesToUILabel: any = {
    "CREDIT_CARD": "CARD",
    "DEBIT_CARD": "CARD",
    "NET_BANKING": "NET BANKING",
    "PAYTM": "PAYTM",
    "AMAZONPAY": "AMAZONPAY",
    "AMAZON_PAY": "AMAZONPAY",
    "SODEXO": "SODEXO",
    "WALLET": "WALLET",
    "UPI": "UPI",
    "PHONEPE": "PHONEPE",
    "DEBIT_OR_CREDIT_CARD": "CARD",
    "EMI": "EMI",
    "FITCASH": "FITCASH",
    "PAYPAL": "PAYPAL",
    "PAYPAL_RT": "PAYPAL",
    "PAYMENT_LINK": "PAYMENT LINK",
    "COD": "PAY ON DELIVERY",
    "PAY_LATER": "PAY LATER",
    "CARDLESS_EMI": "CARDLESS EMI",
    "BAJAJ_FINSERV": "BAJAJ FINSERV",
    "ZESTMONEY": "ZEST MONEY",
    "APPLE_IN_APP": "APP STORE",
    "PLAY_STORE_BILLING": "PLAY STORE",
    "POS": "IN CENTER PAYMENT",
    "CRED": "CRED"
}

@injectable()
class OrderViewBuilder {

    constructor(
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReaderPMS) private catalogueServicePMS: ICatalogueServicePMS,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(ALFRED_CLIENT_TYPES.ShipmentService) private shipmentService: IShipmentService,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
        @inject(CUREFIT_API_TYPES.ProductBusiness) private productBusiness: IProductBusiness,
        @inject(DELIVERY_CLIENT_TYPES.GateService) private gateService: IGateService,
        @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
        @inject(CULT_CLIENT_TYPES.MindFitService) private mindFitService: ICultService,
        @inject(CUREFIT_API_TYPES.CRMIssueService) private CRMIssueService: ICRMIssueService,
        @inject(CUREFIT_API_TYPES.OrderProductDetailBuilder) private orderProductDetailBuilder: OrderProductDetailBuilder,
        @inject(CUREFIT_API_TYPES.UpgradeMembershipViewBuilder) private upgradeMembershipViewBuilder: UpgradeMembershipViewBuilder,
        @inject(CUREFIT_API_TYPES.PlayUpgradeDetailViewBuilder) private playUpgradeMembershipViewBuilder: PlayUpgradeDetailViewBuilder,
        @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
        @inject(ALFRED_CLIENT_TYPES.FulfilmentService) private fulfilmentService: IFulfilmentService,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) private offerService: IOfferServiceV2,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) private offerServiceV3: OfferServiceV3,
        @inject(LOCATION_TYPES.CityService) private cityService: ICityService,
        @inject(DELIVERY_CLIENT_TYPES.DeliverySlotService) private deliverySlotService: IDeliverySlotService,
        @inject(PAYMENT_MODELS_TYPES.EmiInterestReadonlyDao) private paymentDao: EmiInterestReadonlyDaoMongoImpl,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferService) private offerServiceV1: IOfferService,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(PAYMENT_TYPES.IPaymentClient) protected paymentClient: IPaymentClient,
        @inject(GEARVAULT_CLIENT_TYPES.GearService) private gearService: GearService,
        @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness,
        @inject(HERCULES_CLIENT_TYPES.IHerculesService) public herculesService: IHerculesService,
        @inject(CUREFIT_API_TYPES.WodViewBuilder) public wodViewBuilder: WodViewBuilder,
        @inject(PAGE_CONFIG_TYPES.ConfigService) private configService: ConfigService,
        @inject(ERROR_COMMON_TYPES.RollbarService) public rollbarService: RollbarService,
        @inject(THIRD_PARTY_CLIENT_TYPES.ThirdPartyService) private thirdPartyService: IThirdPartyService,
        @inject(SEGMENTATION_CLIENT_TYPES.UserSegmentClient) public segmentationClient: IUserSegmentClient,
        @inject(IDENTITY_CLIENT_TYPES.IdentityService) protected identityService: IIdentityService,
        @inject(CENTER_SERVICE_CLIENT_TYPES.CenterService) protected centerService: ICenterService,
        @inject(OLLIVANDER_CLIENT_TYPES.IOllivanderCityService) private ollivanderService: IOllivanderCityService,
        @inject(GYMFIT_CLIENT_TYPES.GymfitService) private gymfitService: IGymfitService,
        @inject(HAMLET_CLIENT_TYPES.HamletService) private hamletService: IHamletService,
        @inject(OMS_API_CLIENT_TYPES.OfferHelper) private offerHelper: OfferHelper,
        @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService,
        @inject(REWARD_CLIENT_TYPES.IConsumablesService) protected consumablesService: IConsumablesService,
        @inject(PACK_CLIENT_TYPES.OfflineFitnessPackService) private offlineFitnessPackService: IOfflineFitnessPackService,
        @inject(OMS_API_CLIENT_TYPES.OrderService) private omsApiClient: IOmsApiClient,
        @inject(CUREFIT_API_TYPES.AppConfigStoreService) private appConfigStore: IAppConfigStoreService,
        @inject(CUREFIT_API_TYPES.WidgetBuilder) public widgetBuilder: WidgetBuilder
    ) {
    }

    async getOrderInformation(order: Order, userContext: UserContext): Promise<OrderInfo> {
        const user = await this.userCache.getUser(order.userId)
        const tz = userContext.userProfile.timezone
        const orderProduct = order.products[0]
        const product = order.productSnapshots[0]
        let orderProductInfo: OrderProductInfo
        let orderInfo: OrderInfo
        const billingInfo: BillingInfo = this.offerHelper.getOrderBilling(order)
        if (product.productType === "FOOD" && product.isPack === false) {
            const imageProductId = EatUtil.getProductId(product)
            const orderTackingWidget = await this.getOrderTrackingWidget(order.orderId, userContext)
            orderProductInfo = {
                title: product.title,
                image: UrlPathBuilder.getSingleImagePath(imageProductId, "FOOD", "THUMBNAIL", product.imageVersion),
                subTitle: product.attributes ? `${product.attributes.nutritionInfo.Calories["Total Calories"]} Calories` : "",
                mrp: billingInfo.mrp,
                discount: billingInfo.discount,
                amountPayable: order.totalAmountPayable
            }
            const orderDate = new Date(order.statusHistory[0].timestamp)
            const slotId: string = orderProduct.option.deliverySlot ? orderProduct.option.deliverySlot : undefined
            const slot = this.deliverySlotService.getSlotById(slotId)
            orderInfo = {
                product: orderProductInfo,
                orderId: order.orderId,
                orderDate: orderDate,
                customerName: user.firstName + " " + user.lastName,
                customerEmail: user.email,
                customerPhone: user.phone,
                paymentMode: "Online",
                deliverySlot: "",
                deliveryAddress: new UserAddressView(order.userAddress),
                orderTracking: orderTackingWidget,
                orderStatus: orderTackingWidget ? orderTackingWidget.state : undefined
            }
        }
        return orderInfo
    }

    async mapFoodMarketplaceOrderInfo(order: Order, product: Product): Promise<OrderInfo> {

        const user = await this.userCache.getUser(order.userId)

        return {
            orderId: order.orderId,
            orderDate: new Date(),
            orderStatus: "NOT_STARTED",
            orderTracking: undefined,
            customerEmail: user.email,
            customerName: user.firstName + " " + user.lastName,
            customerPhone: user.phone,
            product: {
                title: product.title,
                subTitle: product.subTitle,
                image: product.imageUrl,
                mrp: order.totalAmountPayable,
                discount: undefined,
                amountPayable: order.totalAmountPayable
            },
            deliverySlot: undefined,
            deliveryAddress: new UserAddressView(order.userAddress),
            paymentMode: "Online"
        }

    }


    getPaymentDetail(order: BaseOrder): PaymentDetail {
        const payments: PaymentData[] = order.payments
        let paymentData: PaymentData = payments.find(payment => {
            const paymentMode: PaymentMode = _.get(payment, "data.selectedPaymentMode", undefined)
            return PaymentUtil.isCodPayment(payment.channel, paymentMode)
                || payment.status === "adjusted"
                || payment.status === "paid"
                || payment.channel === "MANUAL"
                || (!_.isNil(payment.statusHistory) && payment.statusHistory.filter((x) => (x.status === "paid")).length > 0)
            // Adding the (!_.isNil(payment.statusHistory)) check because some old orders from 2017 do not have statusHistory
        })
        if (_.isNil(paymentData)) {
            if (!_.isEmpty(order.fulfillmentDetails?.steps)) {
                paymentData = PaymentUtil.findPaymentByCfPaymentId(order, order.fulfillmentDetails.steps[0].requestIdentifier)
            } else {
                return undefined
            }
        }
        const refunds = paymentData.refunds
        let refundAmount = 0
        let cashbackAmount = 0
        _.forEach(refunds, refund => {
            if (refund.refundType === "CASHBACK") {
                cashbackAmount = cashbackAmount + (refund.amount / 100)
            } else {
                refundAmount += refund.amount
            }
        })
        refundAmount /= 100
        if (paymentData.cashback) {
            cashbackAmount = cashbackAmount + (paymentData.cashback.amount / 100)
        }
        return {
            refundAmount: refundAmount,
            cashbackAmount: cashbackAmount,
            paymentChannel: paymentData ? OrderUtil.getFormattedPaymentChannel(paymentData.channel) : null,
            successfulPaymentId: paymentData ? paymentData.paymentId : undefined,
            paymentMode: paymentData?.data?.selectedPaymentMode,
            paymentCompletionHandler: paymentData?.paymentCompletionHandler,
        }
    }

    private async getRefundConfig(refundDetailsPromise: Promise<RefundDetailsResponse[]>): Promise<RefundConfig> {
        let refundDetails: RefundDetailsResponse[]
        try {
            refundDetails = await refundDetailsPromise
        } catch (err) {
            this.logger.error(`Cannot get refund config.`, err)
            return undefined
        }
        let pendingAmountPaise: number = 0
        let successfulAmountPaise: number = 0
        refundDetails.forEach((refundDetail: RefundDetailsResponse) => {
            if (refundDetail.status === "pending") {
                pendingAmountPaise += refundDetail.fitcashAmount + refundDetail.pgAmount
            } else if (refundDetail.status === "success") {
                successfulAmountPaise += refundDetail.fitcashAmount + refundDetail.pgAmount
            }
        })
        pendingAmountPaise = Math.round(pendingAmountPaise)
        successfulAmountPaise = Math.round(successfulAmountPaise)
        if (pendingAmountPaise === 0 && successfulAmountPaise === 0) return undefined
        if (pendingAmountPaise > 0) {
            return {
                refundAmount: pendingAmountPaise / 100,
                refundStatus: "in_progress"
            }
        } else if (successfulAmountPaise > 0) {
            return {
                refundAmount: successfulAmountPaise / 100,
                refundStatus: "complete"
            }
        } else return undefined
    }

    async getOrderTrackingWidget(orderId: string, userContext: UserContext): Promise<OrderTrackingStatusWidget> {
        // Based on assumption single order will have only one shipment document
        // get fulfilment from order id
        // get shipment from fulfilment id
        // get food booking object from shipment
        // const timezone = userContext.userProfile.timezone
        // let foodFulfilment: FoodFulfilment
        // try {
        //     foodFulfilment = await this.fulfilmentService.getFoodFulfilmentByOrderId(orderId)
        // } catch (e) {
        //     this.logger.error("Food Fulfilment fetch failed for orderId:" + orderId, e)
        //     return undefined
        // }
        // Handling case where fulfilment not created as order came past cut off
        return undefined
    }

    async buildOrderDetailView(userContext: UserContext, order: BaseOrder, isDetailed: boolean, apiType: "ORDER_CHECKOUT" | "ORDER_V1", source?: string, user?: User): Promise<OrderDetail> {
        const product: Product = order.productSnapshots[0]
        const isCultPTOrder = await this.isCultPTOrder(order)
        const orderProduct: OrderProduct = order.products[0]
        const orderProductSnapshot = order.productSnapshots[0]
        const paymentDetail: PaymentDetail = this.getPaymentDetail(order)
        const redirectionUrl = AppUtil.getRedirectionUrlForStoreWebview()
        const numProducts = this.numberOfProducts(order)
        const isSingleGearItem = numProducts === 1 && orderProduct.productType === "GEAR"
        let inventoryUnitId: number
        let action: Action
        let inventoryUnits: any
        if (isSingleGearItem) {
            const gearOrder: GearOrderDetailObject = await
                this.gearService.getOrderWithShipmentStatus(order.orderId)
            gearOrder.shipments.forEach((shipment) => {
                switch (shipment.shipment_type) {
                    case ShipmentType.RETURN:
                        inventoryUnits = shipment.return_inventory_units
                        break
                    case ShipmentType.FORWARD:
                    default:
                        inventoryUnits = _.reject(shipment.inventory_units, "return_shipment_id")
                        break
                }
                if (!inventoryUnits || (Array.isArray(inventoryUnits) && !inventoryUnits.length)) {
                    return
                }
                this.logger.info(`buildOrderDetailView inventoryUnits ${JSON.stringify(inventoryUnits, null, 4)} `)
                inventoryUnitId = inventoryUnits[0].id
            })

            action = {
                actionType: "SSO_STORE_WEB",
                url: `${redirectionUrl}/order/${order.orderId}/${inventoryUnitId}`,
                meta: {
                    fromFlutter: true
                }
            }
        }
        if (_.isNil(paymentDetail)) return undefined
        // TODO: Once we kill version before 4.51 we can send only the limited information in my orders
        const userAgent = userContext.sessionInfo.userAgent

        // ui is different for web/mweb and mobile for orders and order detail
        const isWeb = ["DESKTOP", "MBROWSER"].includes(userAgent)

        // Added this change to remove the support for viewing the order from the website/older app since it might break there
        if (orderProduct.productType === "CULT_BIKE" && !AppUtil.isCultBikeOrderSupported(userContext)) {
            return undefined
        }

        // only for food singles and web/mweb
        let refundDetailsPromise: Promise<RefundDetailsResponse[]>
        let refundConfigPromise: Promise<RefundConfig>
        if (["APPLE_IN_APP", "PLAY_STORE_BILLING"].indexOf(paymentDetail.paymentChannel) < 0 && !_.isNil(paymentDetail.successfulPaymentId)) {
            refundDetailsPromise = this.paymentClient.getRefundDetails(
                order.orderId,
                paymentDetail.successfulPaymentId,
                paymentDetail.paymentChannel
            )
            refundConfigPromise = this.getRefundConfig(refundDetailsPromise)
        }
        const orderTitlePromise: Promise<string> = this.getOrderTitle(userContext, numProducts, product, orderProduct, source)
        const orderDetail: OrderDetail = {
            orderId: order.orderId,
            paymentMode: this.getPaymentModeText(paymentDetail),
            price: {
                mrp: parseFloat(order.totalPayable.toFixed(2)),
                listingPrice: parseFloat(order.totalPayable.toFixed(2)),
                currency: orderProductSnapshot.price.currency
            },
            amountPaid: parseFloat(order.totalAmountPayable.toFixed(2)),
            title: await orderTitlePromise,
            /*
             * this activity type is used by cultgear to nav to a diff client page for a detailed view of gear orders
             * for the case of "CULT_GEAR_ECOMMERCE" || "STORE" with sub activity type of "CULT_GEAR_ECOMMERCE"
             */
            activityType: await this.getActivityType(userContext, order, product.productType, product.isPack),
            subTitle: this.getOrderSubTitle(userContext, product, orderProduct),
            orderDate: new Date(order.statusHistory[0].timestamp),
            widgets: undefined,
            refundAmount: paymentDetail.refundAmount,
            cashbackAmount: paymentDetail.cashbackAmount,
            reportData: undefined,
            reportIssues: undefined,
            pageToken: order.createdDate?.toString(),
            refundConfig: await refundConfigPromise,
            inventoryUnitId,
            orderClickAction: action,
            orderStatusImage: "/image/icons/greenTick.png"
        }


        if (orderDetail.activityType === "STORE") {
            // creating sub activity for the gear and wholefit orders when store tab is supported
            orderDetail.subActivityType = this.isWholefitOrder(order) ? "WHOLE_FIT" : "CULT_GEAR_ECOMMERCE"
        }

        const orderProductDetail = await this.orderProductDetailBuilder.getOrderProductDetails(userContext, order)
        if (_.isEmpty(orderProductDetail)) {
            this.logger.error("found empty orderProductDetail for order: " + JSON.stringify(order))
            this.rollbarService.sendError(new Error("found empty orderProductDetail for order: " + JSON.stringify(order)))
        } else {
            orderDetail.magazineImage = orderProductDetail.magazineImage
            orderDetail.thumbnailImage = orderProductDetail.thumbnailImage
            orderDetail.thumbnailImages = orderProductDetail.thumbnailImages
        }
        const orderCreationStatus = OMSOrderUtil.getOrderFulfillmentStatus(order)
        if (isDetailed) {
            const data: OrderCheckoutDetail = await this.buildDetailView({
                userContext: userContext,
                order: order,
                product: product,
                paymentDetail: paymentDetail,
                apiType: apiType,
                orderDetail,
                refundDetailsPromise: refundDetailsPromise,
                appConfigStore: this.appConfigStore
            })
            orderDetail.widgets = data.widgets
            // if (orderTrackingWidget && !_.isEmpty(orderTrackingWidget.values)) {
            //     orderDetail.widgets = [orderTrackingWidget, ...orderDetail.widgets]
            // }
            orderDetail.reportData = data.reportData
            orderDetail.reportIssues = orderProductDetail?.reportIssues
            if (product.productType === "FITNESS" || isCultPTOrder || product.productType === "PLAY") {
                orderDetail.pageActionTitle = "Need Help"
            }
            const LIVE_BRANDING = LiveUtil.getLiveBranding(userContext)
            orderDetail.title = product.productType === "CF_LIVE" ? orderProductDetail?.title + " " + LIVE_BRANDING : orderProductDetail?.title
            if (product.categoryId !== "GYMFIT_PERSONAL_TRAINING") {
                orderDetail.action = orderProductDetail?.action
            }
            orderDetail.state = orderProductDetail?.state
            if (isWeb) {
                orderDetail.widgets = [this.getProductDetailWebWidget(orderProductDetail), ...orderDetail.widgets]
            }
            if (paymentDetail.paymentCompletionHandler === "OMS_CUSTOM" && product.productType !== "ADDON") {
                const timeline = OMSOrderUtil.getOrderFulfillmentTimeline(order)
                const paymentErrorRes = await this.omsApiClient.getFulfillmentStatus(order.orderId)
                if (timeline) {
                    orderDetail.widgets = [this.getOrderTimeLineWidget(timeline, paymentErrorRes), ...orderDetail.widgets]
                }
            }

            // Added support for this in 9.21 app onwards
            orderDetail.pageActions = orderProductDetail?.pageActions
            orderDetail.seeDetailAction = orderProductDetail?.seeDetailAction
        } else if (source === "support") {
            orderDetail.action = SUPPORT_DEEP_LINK
            if (await AppUtil.isNewSupportInFlutterSupported(this.segmentService, userContext)) {
                orderDetail.seeDetailAction = orderProductDetail?.seeDetailAction
            }
        }
        if (!product.isPack && (product.productType === "FITNESS" || product.productType === "PLAY")) {
            orderDetail.subActivityType = product.title.toUpperCase()
        }
        if (product.isPack) {
            orderDetail.packStatus = {
                total: 0,
                current: 0,
                packId: product.productId
            }
        }
        if (orderCreationStatus) {
            if (orderCreationStatus.result === "pending" || orderCreationStatus.result === "under_review") {
                if (orderCreationStatus.currentStage === "PAYMENT_VERIFICATION") {
                    orderDetail.orderStatusText = "Payment in progress"
                } else {
                    orderDetail.orderStatusText = "Membership creation in progress"
                }
                orderDetail.orderStatusColor = "#F57500"
                orderDetail.orderStatusImage = "/image/icons/pending.png"
            }
            if (orderCreationStatus.result === "failed" && orderCreationStatus.currentStage === "PAYMENT_VERIFICATION") {
                orderDetail.orderStatusText = "Payment Failed"
                orderDetail.orderStatusColor = "#F50000"
                orderDetail.orderStatusImage = "/image/icons/warningCircle.png"
            }
        }

        if (product.productType === "FOOD" && !product.isPack) {
            orderDetail.deliveryDate = orderProduct.option.startDate
            orderDetail.deliverySlot = SlotUtil.getSlotById(orderProduct.option.deliverySlot)
        }

        if (product.productType === "LUX_FITNESS_PRODUCT") {
            const luxPack = product as unknown as OfflineFitnessPack
            orderDetail.title = luxPack.displayName ?? luxPack.title
        }
        this.logger.info(`orderLog orderDetail ${JSON.stringify(orderDetail)}`)

        if (orderDetail.paymentMode === "APPLE") {
            orderDetail.widgets = isDetailed ? [this.getApplePurchaseDetailsWidget(userContext)] : []
            orderDetail.price = {
                listingPrice: NaN,
                currency: ""
            }
            orderDetail.amountPaid = NaN
        }

        return orderDetail
    }

    public getSuccessfulPayment(order: BaseOrder): PaymentData {
        const codPayment: PaymentData = PaymentUtil.findCodPayment(order)
        if (!_.isNil(codPayment)) {
            return codPayment
        }
        return PaymentUtil.findPaidPaymentForOrder(order, undefined)
    }

    public get3PEarnAndBurnDetails(order: BaseOrder): ThirdPartyDetails {
        const paidPayment = this.getSuccessfulPayment(order)
           return {
                tenant: this.get3PTenant(order),
                pointsBurned: this.get3PPointsBurned(paidPayment),
                pointsToBeEarned: this.get3PPointsToBeEarned(order)
            }
     }

    private get3PTenant(order: BaseOrder): ThirdPartyTenant {
        return "TATA"
    }

    private get3PPointsBurned(paidPayment: PaymentData): number {
        return _.defaultTo(paidPayment?.data?.tataPaymentInfo?.loyalytPointsRedemptionDetails?.redeemedAmount, 0)
    }

    private get3PPointsToBeEarned(order: BaseOrder): number {
        return _.defaultTo(order.tataLoyaltyPointsGranted, 0) - _.defaultTo(order.tataLoyaltyPointsRevoked, 0)
    }

    private getPaymentModeText(paymentDetail: PaymentDetail): string {
        let uiPaymentModeLabel: string = paymentDetail.paymentChannel
        if (!_.isEmpty(paymentDetail?.paymentMode)) {
            const uiLabel: string = paymentModesToUILabel[paymentDetail.paymentMode]
            if (!_.isEmpty(uiLabel)) {
                uiPaymentModeLabel = uiLabel
            }
        }
        return uiPaymentModeLabel
    }

    async getOrderTitle(userContext: UserContext, numProducts: number, product: Product, orderProduct: OrderProduct, source?: string): Promise<string> {
        if (product.productType === "DIAGNOSTICS") {
            return "Diagnostic test"
        } else if (product.productType === "CF_LIVE") {
            return product.title + " " + LiveUtil.getLiveBranding(userContext)
        } else if (orderProduct.productType === "BUNDLE" && !_.isEmpty(orderProduct.option.parentProductCode)) {
            const parentProduct = await this.catalogueService.getProduct(orderProduct.option.parentProductCode)
            return parentProduct.title
        } else if (product.productType === "BUNDLE" || numProducts <= 1) {
            return product.title
        } else if (product.productType === "CONSULTATION") {
            return "Consultations"
        } else if (source === "support") {
            return product.title + " + " + (numProducts - 1) + " items ordered"
        } else if (product.productType === "LUX_FITNESS_PRODUCT") {
            const luxPack: OfflineFitnessPack = product as unknown as OfflineFitnessPack
            return luxPack.displayName ?? luxPack.product.title
        } else {
            return numProducts + " items ordered"
        }
    }

    getProductDetailWebWidget(detail: OrderProductDetail): WidgetView {
        const productDetailsWidget: HorizontalScrollCardWidget = {
            widgetType: "HORIZONTAL_SCROLL_CARD_WIDGET",
            items: []
        }
        if (_.isEmpty(detail.itemDetails)) {
            productDetailsWidget.items = [{
                title: detail.title,
                subTitle: detail.subTitle,
                image: detail.magazineImage ? detail.magazineImage : detail.thumbnailImage,
                imageType: detail.magazineImage ? "MAGAZINE" : "THUMBNAIL",
                action: {
                    actionType: "NAVIGATION",
                    url: detail.action
                }
            }]
        } else {
            productDetailsWidget.items = _.map(detail.itemDetails, (itemDetail: ItemDetail) => {
                const item: MealCardItem = {
                    title: itemDetail.title,
                    subTitle: itemDetail.subTitle,
                    image: itemDetail.magazineImage ? itemDetail.magazineImage : itemDetail.thumbnailImage,
                    imageType: itemDetail.magazineImage ? "MAGAZINE" : "THUMBNAIL",
                    isVeg: itemDetail.isVeg,
                    quantity: itemDetail.quantity,
                    action: {
                        actionType: "NAVIGATION",
                        url: itemDetail.action
                    }
                }
                return item
            })
        }
        return productDetailsWidget
    }

    async getActivityType(userContext: UserContext, order: Order, type: ProductType, isPack: boolean): Promise<ActivityType> {
        const isLivePTOrder = await this.isLiveCultPTOrder(order)
        const isWholefitOrder = this.isWholefitOrder(order)

        switch (type) {
            case "FOOD":
                if (isWholefitOrder && AppUtil.isStoreTabSupported(userContext)) {
                    // passing STORE icon for wholefit and gear orders
                    return "STORE"
                }
                return "EATFIT_MEAL"
            case "FITNESS":
            case "GYMFIT_FITNESS_PACK":
            case "THIRD_PARTY_FITNESS_PRODUCT":
            case "FITNESS_ACCESSORIES":
                if (isPack)
                    return "DIY_FITNESS" // Passing diy fitness icon for cult pack temporarily
                else
                    return "CULT_CLASS"
            case "GYMFIT_FITNESS_PRODUCT":
                return "GYMFIT_FITNESS"
            case "MIND":
                if (isPack)
                    return "DIY_MEDITATION" // Passing diy fitness icon for cult pack temporarily
                else
                    return "MIND_CLASS"
            case "FITNESS_PREREGISTRATION":
                return "DIY_FITNESS"
            case "MIND_PREREGISTRATION":
                return "DIY_MEDITATION"
            case "CONSULTATION":
                if (isLivePTOrder) {
                    return "CULT_CLASS"
                }
                return "CONSULTATION"
            case "BUNDLE":
                if (isLivePTOrder) {
                    return "DIY_FITNESS"
                }
                return "CONSULTATION"
            case "DIAGNOSTICS":
                return "CONSULTATION"
            case "GEAR":
                // return "CULT_GEAR_ECOMMERCE"
                // passing STORE icon for wholefit and gear orders if store tab is supported
                if (AppUtil.isStoreTabSupported(userContext)) {
                    return "STORE"
                }
                return "CULT_GEAR_ECOMMERCE"
            case "CF_LIVE":
                return "LIVE_PACK_BOOK"
            case "FOOD_MARKETPLACE": // todo maybe change?
                return "FOOD_MARKETPLACE_MEAL"
        }

        return undefined
    }

    isWholefitOrder(order: Order) {
        return _.get(order, "eatOptions.listingBrand", undefined) === "WHOLE_FIT"
    }

    getOrderSubTitle(userContext: UserContext, product: Product, orderProduct: OrderProduct): string {
        if (product.isPack) {
            if (product.productType === "FOOD") {
                return `${pad(orderProduct.option.numTickets, 2)} meals`
            } else if (product.productType === "FITNESS" || product.productType === "PLAY") {
                return CultUtil.getDescriptionFromProductId(product.productId, product.productType)
            }
        } else {
            const tz = userContext.userProfile.timezone
            if (product.productType === "FOOD") {
                return TimeUtil.formatDateStringInTimeZone(orderProduct.option.startDate, tz, "ddd,D MMM")
            } else if (product.productType === "FITNESS" || product.productType === "PLAY") {
                return TimeUtil.formatDateStringInTimeZone(orderProduct.option.startDate, tz, "ddd,D MMM")
            } else if (product.productType === "FITNESS_PREREGISTRATION") {
                return "Fitness PreRegistration"
            } else if (product.productType === "MIND_PREREGISTRATION") {
                return "Mind PreRegistration"
            }
        }
        return undefined
    }

    getIssueList(customerIssueTypes: CustomerIssueType[]): { code: string, title: string }[] {
        const issueList: { code: string, title: string }[] = []
        customerIssueTypes.forEach(customerIssueType => {
            issueList.push({
                code: customerIssueType.code,
                title: customerIssueType.subject
            })
        })
        return issueList
    }

    private numberOfProducts(order: BaseOrder): number {
        let numProducts = 0
        order.products.forEach((orderProduct: OrderProduct) => {
            numProducts = numProducts + orderProduct.quantity
        })
        return numProducts
    }

    private getOrderCheckoutAction(userAgent: UserAgent, order: Order): AppAction {
        const action: AppAction = {
            actionType: "SHOW_PAYMENT_OPTIONS_MODAL",
            url: "",
            title: "Proceed to pay",
        }
        const orderProductOption = order.products[0].option
        const listingPrice = order.totalPayable ? order.totalPayable : order.totalAmountPayable
        const isMember = orderProductOption.parentBookingId && orderProductOption.parentBookingId !== -1 && listingPrice === 0
        if (isMember) {
            action.title = "Proceed to confirm"
        }
        if (userAgent === "DESKTOP") {
            action.orientation = "RIGHT"
        }
        return action
    }

    async buildDetailView(orderBuildViewParams: OrderViewBuilderParams): Promise<OrderCheckoutDetail> {
        const [detail, consumables] = await Promise.all([
            this.buildView(orderBuildViewParams),
            this.consumablesService.getConsumableForOrder(orderBuildViewParams.order.orderId).catch(err => {
                this.logger.error(`Error while fetching consumables for order: ${orderBuildViewParams.order.orderId}`, err)
                return undefined
            })
        ])
        if (consumables) {
            const userContext: UserContext = orderBuildViewParams.userContext
            const couponInfoWidget: CouponInfoWidget = CouponUtil.getCouponsInfoWidget(consumables, userContext, null, orderBuildViewParams.order.orderId)
            if (couponInfoWidget) {
                detail.widgets.push(couponInfoWidget)
            }
        }
        return detail
    }

    public async getPotentialNeuPassEarnWidget(order: Order, userId: string): Promise<NeuPassEarnedCoinsWidget> {
        const potentialNeuPointsToBeEarned: PotentialTataPointsEarnResponse = await this.getPotentialNeuPointsToBeEarned(order, userId)
        if (potentialNeuPointsToBeEarned.points > 0) {
            return {
                widgetType: "NEUPASS_EARNED_COINS_WIDGET",
                data: {
                    title: "Earn upto " + potentialNeuPointsToBeEarned.points + " NeuCoins!",
                    subTitle: "1 neucoin= ₹1.\nUpto " + potentialNeuPointsToBeEarned.points + " NeuCoins will be credited on purchase.",
                    icon: "image/tata/tata-neu.png",
                    infoAction: {
                        iconUrl: "image/tata/Info.png",
                        actionType: "SHOW_ALERT_MODAL",
                        meta: {
                          title: "Info",
                          subTitle: "0.25% NeuCoins will be awarded on the final payable amount post discounts",
                          actions: [{ "actionType": "HIDE_ALERT_MODAL", "title": "OKAY" }],
                          type: "Info"
                        }
                    }
                }
            }
        }
    }

    public addNeuPassEarnedCoinsWidget(order: Order, widgets: WidgetView[], v2?: boolean) {
        const earnAndBurnDetails: ThirdPartyDetails = this.get3PEarnAndBurnDetails(order)
        if (!_.isEmpty(earnAndBurnDetails) && (earnAndBurnDetails.pointsBurned > 0 || earnAndBurnDetails.pointsToBeEarned > 0)) {
            widgets.push({
                widgetType: "NEUPASS_EARNED_COINS_WIDGET",
                data: {
                    subTitle: "You burned " + earnAndBurnDetails.pointsBurned + " NeuCoins and earned " + earnAndBurnDetails.pointsToBeEarned + " NeuCoins on this transaction",
                    icon: "image/tata/tata-neu.png",
                    style: {
                        paddingHorizontal: 0,
                        ...v2 && {
                            marginHorizontal: 20,
                        }
                    },
                    ...v2 && {
                        varient: "small"
                    }
                }
            })
        }
    }
    async getPotentialNeuPointsToBeEarned(order: BaseOrder, userId: string): Promise<PotentialTataPointsEarnResponse> {
        try {
            return await this.thirdPartyService.getPotentialNeuPointsToBeEarned({
                productSnapshots: order.productSnapshots,
                totalAmountPayable: order.totalAmountPayable,
                orderSource: order.source,
                userId: userId
            })
        } catch (err) {
            this.logger.error(`Failed to getPotentialNeuPointsToBeEarned`, {err})
            return {
                points: 0
            }
        }
    }

    async buildView(orderBuildViewParams: OrderViewBuilderParams): Promise<OrderCheckoutDetail> {
        const baseOrder: BaseOrder = orderBuildViewParams.order
        const product: Product = orderBuildViewParams.product
        const userContext: UserContext = orderBuildViewParams.userContext
        const paymentDetail: PaymentDetail = orderBuildViewParams.paymentDetail
        const allPossibleActionsForFoodBooking = orderBuildViewParams.allPossibleActionsForFoodBooking
        const foodBooking = orderBuildViewParams.foodBooking
        const orderProduct: OrderProduct = baseOrder.products[0]
        const userAddress: UserDeliveryAddress = baseOrder.userAddress
        let billingInfo: BillingInfo = this.offerHelper.getOrderBilling(baseOrder)
        const user = await this.userCache.getUser(baseOrder.userId)
        const reportIssueAction: string = SUPPORT_DEEP_LINK
        const userAgent = orderBuildViewParams.userContext.sessionInfo.userAgent
        const transferMembershipResponse: MembershipTransferDetailResponse = orderBuildViewParams.transferMembershipResponse
        const selectTransferMembershipResponse = orderBuildViewParams.selectTransferMembershipResponse
        const membershipTransferDetails: any = orderBuildViewParams.membershipTransferDetails
        const orderDetail = orderBuildViewParams.orderDetail
        const numberOfProducts = this.numberOfProducts(baseOrder)

        const now = TimeUtil.getMomentNow(userContext.userProfile.timezone)
        const birthdayDate = TimeUtil.getMomentForDateString(user.birthday, userContext.userProfile.timezone,
            "YYYY-MM-DDTHH:mm:ss.sssZ")
        const age = now.diff(birthdayDate, "year")
        const response: OrderCheckoutDetail = {
            orderMeta: {
                orderId: baseOrder.orderId,
                customerName: user.firstName + " " + user.lastName,
                customerEmail: user.email,
                customerPhone: user.phone,
                customerGender: user.gender,
                customerAge: age,
                price: {
                    listingPrice: baseOrder.totalPayable ? baseOrder.totalPayable : baseOrder.totalAmountPayable,
                    mrp: baseOrder.totalPayable ? baseOrder.totalPayable : baseOrder.totalAmountPayable, // todo(@hunny): remove totalPayable check once order migration is done
                    currency: billingInfo.currency
                },
                cookingInstructions: "",
                orderType: "OTHER",
                productType: product.productType,
                productId: orderBuildViewParams.product.productId,
                productIds: baseOrder?.productSnapshots?.map((product) => { return product.productId }),
                totalAmountPayable: baseOrder.totalAmountPayable,
                totalFitCashPayable: baseOrder.totalFitCashPayable,
            }, widgets: [],
            alertInfo: orderBuildViewParams.alertInfo,
            meta: {
                cleverTap: {
                    af_price: baseOrder.totalAmountPayable,
                    af_content_type: baseOrder.productSnapshots.map((product) => { return product.categoryId }),
                    af_content_id: baseOrder.productSnapshots.map((product) => { return product.productId }),
                    af_content: baseOrder.productSnapshots.map((product) => { return product.title }),
                    af_quantity: numberOfProducts
                }
            }
        }
        const refundWidgetPromise: Promise<IOrderRefundWidget[]> = this.getRefundDetailsWidgets(userContext, baseOrder, paymentDetail, orderBuildViewParams.refundDetailsPromise)
        const checkoutTimerWidgetPromise: Promise<IBaseWidget> = this.buildCheckoutTimerWidget(userContext, baseOrder)

        const refundWidgets: IOrderRefundWidget[] = await refundWidgetPromise
        const checkoutTimerWidget: IBaseWidget = await checkoutTimerWidgetPromise
        this.logger.info(`refundWidgets for orderID: ${baseOrder.orderId} => ${JSON.stringify(refundWidgets, null, 2)}`)
        if (!_.isEmpty(refundWidgets)) {
            response.widgets = [...refundWidgets]
        }
        this.logger.info(`response.widgets for orderID: ${baseOrder.orderId} => ${JSON.stringify(response.widgets, null, 2)}`)
        if (!_.isNil(checkoutTimerWidget)) {
            response.widgets.push(checkoutTimerWidget)
        }

        const summaryWidget: BaseOrderSummaryWidget = undefined
        const isMealChanged = baseOrder.productSnapshots.reduce((isMealChanged: boolean, productSnapShot: OrderProductSnapshots) => {
            return isMealChanged || (productSnapShot.option && productSnapShot.option.isChangedMeal)
        }, false)
        if (isMealChanged && numberOfProducts > 1) {
            let title = "Meal changed"
            if (numberOfProducts === 2)
                title += " +1 item ordered"
            else if (numberOfProducts > 2)
                title += "  +" + (numberOfProducts - 1) + " items ordered"
            response.orderMeta.title = title
        } else {
            response.orderMeta.title = numberOfProducts > 1 ? numberOfProducts + " items ordered" : product.title
        }
        response.orderMeta.displayText = response.orderMeta.title
        // Razorpay pg checksum fails when passing special character
        // Hack for + alone as we sell cult 1 + 3 pre reg pack
        response.orderMeta.title = response.orderMeta.title.replace("+", "plus")
        response.orderMeta.title = response.orderMeta.title.replace(/[^\w\s]/gi, "")

        if (billingInfo?.orderDiscountsByOffer?.length) {
            const orderDiscountsWithTitle = (await this.offerServiceV3.getOffersByIds(billingInfo.orderDiscountsByOffer.map(discount => discount.offerId))).data
            billingInfo = { ...billingInfo, orderDiscountsWithTitle }
        }

        // Restricting Get Invoice for Pack Sessions
        let enableInvoiceWidget = true
        if (orderProduct?.productId && orderProduct?.option?.parentBookingId) {
            const productInfo = <ConsultationProduct>await this.catalogueService.getProduct(orderProduct.productId)
            if (["CARE", "CULTFIT", "MINDFIT"].includes(productInfo?.tenant) || ["BUNDLE", "CONSULTATION", "DIAGNOSTICS"].includes(product?.productType)) {
                enableInvoiceWidget = false
            }
        }

        if (orderBuildViewParams.paymentDetail && AppUtil.isNewSigninFlowSupported(userContext) && enableInvoiceWidget) {
            if (AppUtil.isWeb(userContext)) {
                response.widgets.push(this.downloadInvoiceWidget(baseOrder.orderId))
            } else {
                response.widgets.push(this.getInvoiceWidget(user, baseOrder.orderId))
            }
        }
        if (product.isPack) {
            if (product.productType === "FITNESS") {
                const packInfo = await this.catalogueServicePMS.getProduct(orderProduct.productId)
                const productInfoList: ProductInfo[] = baseOrder.productSnapshots.map((product) => ({
                    title: product.title,
                    quantity: product.quantity
                }))
                response.widgets.push(...await this.getCultPackConfirmationWidgets(userContext, paymentDetail, product, billingInfo, baseOrder, orderProduct, packInfo, orderBuildViewParams.apiType))
                response.orderMeta.orderType = "CULT_PACK"
                response.orderMeta.vertical = "CULT_FIT"
                response.orderMeta.productInfoList = productInfoList
                if (orderBuildViewParams.apiType === "ORDER_CHECKOUT") {
                    await this.addTransformAddOnProductWidget(userContext, response, baseOrder)
                }
                await this.addNeuPassCheckoutWidget(userContext, response, baseOrder, orderBuildViewParams.apiType, true)
                await this.addOfferAddonWidget(response, baseOrder, orderBuildViewParams.apiType, undefined)
                return this.appendReportIssueData("CultPack", reportIssueAction, response)
            } else if (product.productType === "CF_LIVE") {
                const packInfo = await this.catalogueService.getCFLiveProduct(orderProduct.productId)
                const productInfoList: ProductInfo[] = baseOrder.productSnapshots.map((product) => ({
                    title: product.title,
                    quantity: product.quantity
                }))
                response.widgets.push(...await this.getLivePackConfirmationWidgets(userContext, paymentDetail, product, billingInfo, baseOrder, orderProduct, packInfo, orderBuildViewParams.apiType))
                response.orderMeta.orderType = "LIVE_PACK"
                response.orderMeta.vertical = "LIVE_FIT"
                response.orderMeta.productInfoList = productInfoList
                await this.addNeuPassCheckoutWidget(userContext, response, baseOrder, orderBuildViewParams.apiType)
                await this.addOfferAddonWidget(response, baseOrder, orderBuildViewParams.apiType)
                return this.appendReportIssueData("LivePackPurchased", reportIssueAction, response)
            } else if (product.productType === "PULSE") {
                /**
                 * There are two ways user can end up here
                 * 1. Member buying a bulk pack of Pulse Rental classes
                 * 2. PPC user buying a Pulse Rental Pack with Class
                 *
                 * In case 1, only the Pulse Rental pack product would be checked out
                 * In case 2, both the Pulse Pack of 1 class, and the Class itself would be in products
                 */
                const pulseProduct: OrderProduct = baseOrder.products.find(item => item.productType === "PULSE")
                const packInfo = await this.catalogueService.getPulsePackById(pulseProduct && pulseProduct.productId)
                response.widgets.push(...await this.getPulsePackConfirmationWidgets(userContext, paymentDetail, product, billingInfo, baseOrder, orderProduct, packInfo))
                response.orderMeta.orderType = "PULSE_PACK"
                response.orderMeta.vertical = "CULT_FIT"
                await this.addOfferAddonWidget(response, baseOrder, orderBuildViewParams.apiType)
                return response
            } else if (product.productType === "FOOD") {
                const foodPack = await this.catalogueService.getFoodPack(orderProduct.productId)
                response.widgets.push(...this.getFoodPackConfirmationWidgets(userContext, paymentDetail, foodPack, billingInfo, baseOrder.deliveryCharges, baseOrder.packagingCharges, orderProduct, userAddress, allPossibleActionsForFoodBooking))
                if (baseOrder.productSnapshots[0].option.subscriptionType !== undefined)
                    response.orderMeta.title += " " + capitalizeFirstLetter(baseOrder.productSnapshots[0].option.subscriptionType) + " plan"
                response.orderMeta.orderType = "EAT_PACK"
                response.orderMeta.vertical = "EAT_FIT"
                if (_.isEmpty(response.orderMeta.productIds)) {
                    if (orderProduct?.productId) {
                        response.orderMeta.productIds = [orderProduct.productId]
                    } else if (orderBuildViewParams?.product) {
                        response.orderMeta.productIds = [orderBuildViewParams?.product?.productId]
                    }

                }
                return this.appendReportIssueData("EatPack", reportIssueAction, response)
            } else if (product.productType === "PROGRAM") {
                // const packInfo = <ProgramPackProduct>await this.catalogueService.getProduct(orderProduct.productId)
                // response.widgets.push(... await this.getProgramConfirmationWidgets(userAgent, paymentDetail, product, billingInfo, order, orderProduct, packInfo))
                // response.orderMeta.orderType = packInfo.tenantId === "cult.fit" ? "CULT_PROGRAM_PACK" : "MIND_PROGRAM_PACK"
                // response.orderMeta.vertical = packInfo.tenantId === "cult.fit" ? "CULT_FIT" : "MIND_FIT"
                // await this.addToggleNeuPassActivationWidget(userContext, response, baseOrder, orderBuildViewParams.apiType, activateNeuPass)
                // return this.appendReportIssueData("CultPack", reportIssueAction, response)
            } else if (product.productType === "FITNESS_FIRST_PACK") {
                response.widgets.push(...await this.getFitnessFirstConfirmationWidgets(userContext, paymentDetail, product, billingInfo, baseOrder, orderProduct))
                response.orderMeta.orderType = "FITNESS_FIRST_PACK"
                response.orderMeta.vertical = "FITNESS_FIRST"
                return response

            } else if (product.productType === "GYMFIT_FITNESS_PACK" || product.productType === "THIRD_PARTY_FITNESS_PRODUCT") {
                const fulfilment = await this.fulfilmentService.getGymfitFitnessFulfilmentByOrderId(baseOrder.orderId)
                response.widgets.push(...await this.getGymfitFitnessPackConfirmationWidgets(userContext, paymentDetail, product, billingInfo, baseOrder, orderProduct, orderBuildViewParams.apiType))
                response.orderMeta.orderType = "GYMFIT_FITNESS_PACK"
                response.orderMeta.vertical = "GYM_FIT"
                await this.addNeuPassCheckoutWidget(userContext, response, baseOrder, orderBuildViewParams.apiType, true)
                if (!_.isNil(fulfilment)) {
                    const issueCategory: IssueCategory = fulfilment.status === "CREATED" ? "GymfitFitnessProductPurchased" : "GymfitFitnessProductCancelled"
                    await this.appendReportIssueData(issueCategory, reportIssueAction, response)
                }
                return response
            } else if (product.productType === "GYMFIT_FITNESS_PRODUCT") {
                const resp = await this.serviceInterfaces.membershipService.filter({orderId: baseOrder.orderId})
                const gymMembership = resp.elements
                const gymfitFitnessProduct = await this.catalogueServicePMS.getAugmentedPackById(product.productId, {
                    includeExhaustiveBenefits: true,
                })
                const isCenterLevelPack = CatalogueServiceUtilities.getAccessLevel(gymfitFitnessProduct) === GymfitAccessLevel.CENTER

                const isWeb = AppUtil.isWeb(userContext)

                this.logger.info(`debug-orderview ${baseOrder.orderId} ${JSON.stringify(gymMembership)}`)
                if (isCenterLevelPack && isWeb) {
                    response.widgets.push(...(await this.getGymFitPackWidgets(userContext, gymfitFitnessProduct, paymentDetail, product, billingInfo, baseOrder, orderProduct, gymMembership, orderBuildViewParams.apiType)))

                } else {
                    response.widgets.push(...(await this.getGymFitPackWidgets(userContext, gymfitFitnessProduct, paymentDetail, product, billingInfo, baseOrder, orderProduct, gymMembership, orderBuildViewParams.apiType)))
                }
                if (AppUtil.isBoosterPackFlowActive()) {
                    let boosterPack: OfflineFitnessPack = undefined
                    try {
                        const addOnPacksPostPurchase: OfflineFitnessPack[] = await this.offlineFitnessPackService.fetchAddOnPacks({
                            basePackId: product.productId, purchaseFlow: PurchaseFlow.POST,
                            namespace: Namespace.OFFLINE_FITNESS
                        })
                        if (addOnPacksPostPurchase && addOnPacksPostPurchase.length > 0) {
                            boosterPack = addOnPacksPostPurchase[0]
                        }
                        if (boosterPack)
                            response.widgets.push(await this.getBoosterPackBenefits(boosterPack))
                    } catch (e) {
                        await this.logger.info("Exception : " + e)
                    }
                }
                const isPlusPackOrder: boolean = OrderUtil.isPlusMembershipOrder(baseOrder)
                if (isPlusPackOrder) {
                    response.widgets.push(await this.getPlusPackBenefits(baseOrder?.productSnapshots?.[0]?.exhaustiveBenefitList))
                }
                await this.addNeuPassCheckoutWidget(userContext, response, baseOrder, orderBuildViewParams.apiType, true)
                await this.addOfferAddonWidget(response, baseOrder, orderBuildViewParams.apiType, undefined, "Offers You Get")
                response.orderMeta.orderType = "GYMFIT_FITNESS_PRODUCT"
                response.orderMeta.vertical = "GYM_FIT"
                return response
            } else if (product.productType === "LUX_FITNESS_PRODUCT") {
                const resp = await this.serviceInterfaces.membershipService.filter({orderId: baseOrder.orderId})
                const luxMemberships = resp.elements
                const luxPack = await this.catalogueServicePMS.getProduct(product.productId)
                response.widgets.push(...(await this.getLuxPackWidgetsV2(userContext, luxPack, paymentDetail, product, billingInfo, baseOrder, orderProduct, luxMemberships, orderBuildViewParams.apiType)))
                await this.addNeuPassCheckoutWidget(userContext, response, baseOrder, orderBuildViewParams.apiType, true)
                await this.addOfferAddonWidget(response, baseOrder, orderBuildViewParams.apiType, undefined, "Offers You Get")
                response.orderMeta.orderType = "GYMFIT_FITNESS_PRODUCT"
                response.orderMeta.vertical = "GYM_FIT"
                return response
            } else if (product.productType === "GYM_PT_PRODUCT") {
                response.orderMeta.orderType = "GYMFIT_FITNESS_PRODUCT"
                response.orderMeta.vertical = "GYM_FIT"
                response.orderMeta.title = product.title
                const productInfoList: ProductInfo[] = baseOrder.productSnapshots.map((product) => ({
                    title: product.title,
                    quantity: product.quantity
                }))
                response.orderMeta.displayText = response.orderMeta.title
                response.orderMeta.subTitle = product.subTitle
                response.orderMeta.productInfoList = productInfoList
                this.addTitleToOrderMeta(response, summaryWidget)
                const widgetViews: WidgetView[] = []
                if (!_.isEmpty(orderBuildViewParams.apiType) && orderBuildViewParams.apiType === "ORDER_V1") {
                    this.addNeuPassEarnedCoinsWidget(baseOrder, widgetViews)
                }
                response.widgets.push(...widgetViews)
                await this.addNeuPassCheckoutWidget(userContext, response, baseOrder, orderBuildViewParams.apiType, true)
                return response
            } else if (product.productType === "GYM_PT_PPC_PRODUCT") {
                response.orderMeta.orderType = "GYMFIT_FITNESS_PRODUCT"
                response.orderMeta.vertical = "GYM_FIT"
                response.orderMeta.title = product.title
                const productInfoList: ProductInfo[] = baseOrder.productSnapshots.map((product) => ({
                    title: product.title,
                    quantity: product.quantity
                }))
                response.orderMeta.displayText = response.orderMeta.title
                response.orderMeta.subTitle = product.subTitle
                response.orderMeta.productInfoList = productInfoList
                this.addTitleToOrderMeta(response, summaryWidget)
                response.widgets.push(...await this.getGymPtPpcBookingConfirmationWidgets(
                    userContext,
                    paymentDetail,
                    product,
                    billingInfo,
                    orderProduct,
                    baseOrder,
                    orderBuildViewParams.appConfigStore,
                    orderBuildViewParams.apiType
                ))
                await this.addNeuPassCheckoutWidget(userContext, response, baseOrder, orderBuildViewParams.apiType, true)
                return response
            } else if (product.productType === "PLAY") {
                const productInfoList: ProductInfo[] = baseOrder.productSnapshots.map((product) => ({
                    title: product.title,
                    quantity: product.quantity
                }))
                response.widgets.push(...await this.getPlayPackConfirmationWidgets(userContext, paymentDetail, product, billingInfo, baseOrder, orderProduct, orderBuildViewParams.apiType))
                response.orderMeta.orderType = "PLAY_PACK"
                response.orderMeta.vertical = "CULT_FIT"
                response.orderMeta.productInfoList = productInfoList

                await this.addNeuPassCheckoutWidget(userContext, response, baseOrder, orderBuildViewParams.apiType, true)
                await this.addOfferAddonWidget(response, baseOrder, orderBuildViewParams.apiType, null)
                return response
                // return this.appendReportIssueData("CultPack", reportIssueAction, response)
            } else if (product.productType === "ADDON") {
                const packInfo = await this.catalogueServicePMS.getProduct(orderProduct.productId)
                const productInfoList: ProductInfo[] = baseOrder.productSnapshots.map((product) => ({
                    title: product.title,
                    quantity: product.quantity
                }))
                response.widgets.push(...await this.getPauseAddOnPackWidgets(userContext, paymentDetail, product, billingInfo, baseOrder, orderProduct, packInfo, orderBuildViewParams.apiType))
                response.orderMeta.orderType = "CULT_PACK"
                response.orderMeta.vertical = "CULT_FIT"
                response.orderMeta.productInfoList = productInfoList
                await this.addNeuPassCheckoutWidget(userContext, response, baseOrder, orderBuildViewParams.apiType, true)
                await this.addOfferAddonWidget(response, baseOrder, orderBuildViewParams.apiType, undefined)
                return this.appendReportIssueData("CultPack", reportIssueAction, response)
            }
        }
        else if ((selectTransferMembershipResponse && membershipTransferDetails) || (transferMembershipResponse && membershipTransferDetails) || (product.productType === "FITNESS" && orderBuildViewParams.order.clientMetadata && (orderBuildViewParams.order.clientMetadata as CultPackTransferMetadata).isMembershipTransfer)) {
            if (selectTransferMembershipResponse && membershipTransferDetails) {
                const membershipTransferSummeryView = await this.getSelectMembershipTransferSummeryView(selectTransferMembershipResponse, membershipTransferDetails, user, userContext)
                return {
                    ...membershipTransferSummeryView,
                    orderMeta: {
                        ...response.orderMeta,
                        isTransferredMembership: true,
                        orderType: "CULT_PACK",
                        vertical: "CULT_FIT",
                        price: {
                            listingPrice: selectTransferMembershipResponse.totalAmount,
                            currency: "INR"
                        }
                    }
                }
            } else if (transferMembershipResponse && membershipTransferDetails) {
                let vertical: Vertical, orderType: orderType
                if (transferMembershipResponse.tenantId === 1) {
                    vertical = "CULT_FIT"
                    orderType = "CULT_PACK"
                } else {
                    vertical = "MIND_FIT"
                    orderType = "MIND_PACK"
                }
                const membershipTransferSummeryView = await this.getMembershipTransferSummeryView(transferMembershipResponse, membershipTransferDetails, user, userContext)
                const sourceCity = await this.cityService.getCityByCultCityId(transferMembershipResponse.sourceCityId)
                return {
                    ...membershipTransferSummeryView,
                    orderMeta: {
                        ...response.orderMeta,
                        isTransferredMembership: true,
                        orderType,
                        vertical,
                        sourceCityName: sourceCity.name,
                        price: {
                            listingPrice: transferMembershipResponse.price,
                            currency: "INR"
                        }
                    }
                }
            } else {
                const packInfo = await this.catalogueServicePMS.getProduct(orderProduct.productId)
                response.widgets.push(...await this.getCultPackConfirmationWidgets(userContext, paymentDetail, product, billingInfo, baseOrder, orderProduct, packInfo, orderBuildViewParams.apiType))
                response.orderMeta.orderType = "CULT_PACK"
                response.orderMeta.vertical = "CULT_FIT"
                await this.addNeuPassCheckoutWidget(userContext, response, baseOrder, orderBuildViewParams.apiType, true)
                // await this.addOfferAddonWidget(response, baseOrder, orderBuildViewParams.apiType)
                // return this.appendReportIssueData("CultPack", reportIssueAction, response)
                return response
            }
        }
        else {
            if (product.productType === "LUX_FITNESS_PRODUCT") {
                const resp = await this.serviceInterfaces.membershipService.filter({orderId: baseOrder.orderId})
                const luxMemberships = resp.elements
                const luxPack = await this.offlineFitnessPackService.getCachedPackById(product.productId)
                response.widgets.push(...(await this.getLuxPackWidgetsV2(userContext, luxPack, paymentDetail, product, billingInfo, baseOrder, orderProduct, luxMemberships, orderBuildViewParams.apiType)))
                await this.addNeuPassCheckoutWidget(userContext, response, baseOrder, orderBuildViewParams.apiType, true)
                await this.addOfferAddonWidget(response, baseOrder, orderBuildViewParams.apiType, undefined, "Offers You Get")
                response.orderMeta.orderType = "GYMFIT_FITNESS_PRODUCT"
                response.orderMeta.vertical = "GYM_FIT"
                return response
            } else if (product.productType === "ADDON") {
                const packInfo = await this.catalogueServicePMS.getProduct(orderProduct.productId)
                const productInfoList: ProductInfo[] = baseOrder.productSnapshots.map((product) => ({
                    title: product.title,
                    quantity: product.quantity
                }))
                response.widgets.push(...await this.getPauseAddOnPackWidgets(userContext, paymentDetail, product, billingInfo, baseOrder, orderProduct, packInfo, orderBuildViewParams.apiType))
                response.orderMeta.orderType = "CULT_PACK"
                response.orderMeta.vertical = "CULT_FIT"
                response.orderMeta.productInfoList = productInfoList
                await this.addNeuPassCheckoutWidget(userContext, response, baseOrder, orderBuildViewParams.apiType, true)
                await this.addOfferAddonWidget(response, baseOrder, orderBuildViewParams.apiType, undefined)
                return this.appendReportIssueData("CultPack", reportIssueAction, response)
            } else if (product.productType === "REGISTRATION") {
                const packInfo = await this.catalogueServicePMS.getProduct(orderProduct.productId)
                const productInfoList: ProductInfo[] = baseOrder.productSnapshots.map((product) => ({
                    title: product.title,
                    quantity: product.quantity
                }))
                response.widgets.push(...await this.getCultPackConfirmationWidgets(userContext, paymentDetail, product, billingInfo, baseOrder, orderProduct, packInfo, orderBuildViewParams.apiType))
                response.orderMeta.orderType = "CULT_PACK"
                response.orderMeta.vertical = "CULT_FIT"
                response.orderMeta.productInfoList = productInfoList
                await this.addNeuPassCheckoutWidget(userContext, response, baseOrder, orderBuildViewParams.apiType, true)
                await this.addOfferAddonWidget(response, baseOrder, orderBuildViewParams.apiType, undefined)
                return this.appendReportIssueData("CultPack", reportIssueAction, response)
            }

            if (product.productType === "FOOD_MARKETPLACE") {

                response.orderMeta.orderType = "FOOD_MARKETPLACE"
                response.orderMeta.vertical = "WELLNESS"
                response.widgets.push(...await this.getFoodMarketplaceConfirmationWidgets(userContext, paymentDetail, product, billingInfo, baseOrder, orderProduct, orderBuildViewParams.apiType))
                await this.addNeuPassCheckoutWidget(userContext, response, baseOrder, orderBuildViewParams.apiType)
                return this.appendReportIssueData("CultSingleSessionBooked", reportIssueAction, response) // todo: Nisheet update the category

            }
            else if (product.productType === "FITNESS" && !CultUtil.isCultEventOrder(orderProduct)) {
                if (isUpgradeMembershipOrder(baseOrder as Order)) {
                    // Construct Upgrade Membership Summary View
                    const clientMetadata = (baseOrder as Order).clientMetadata
                    /**
                     * These are two ways to end up in this block
                     * - While checking out an order
                     * - While checking transaction history after payment
                     */
                    let pageActionTitle: string
                    if (Array.isArray((baseOrder as Order).statusHistory)) {
                        const paidForOrder = (baseOrder as Order).statusHistory.find(s => s.status === "PAYMENT_SUCCESS")
                        if (!paidForOrder) {
                            // checkout view
                            response.widgets = await this.upgradeMembershipViewBuilder.buildUpgradeMembershipOrderCheckoutSummary(
                                clientMetadata,
                                userContext
                            )
                            // perform type-check here on clientMetadata
                            if (isMembershipUpgradeClientMetadata(clientMetadata)) {
                                response.orderMeta.isUpgradeMembership = true
                                const {currency, listingPrice} = response.orderMeta.price
                                if (currency === "INR") {
                                    pageActionTitle = `Pay ${RUPEE_SYMBOL}${listingPrice}`
                                } else {
                                    pageActionTitle = `Pay ${currency}${listingPrice}`
                                }
                            }
                        } else {
                            // order detail view after purchase
                            const orderSummaryWidget: OrderSummaryWidget = this.upgradeMembershipViewBuilder.buildUpgradeOrderSummaryWidget(product, billingInfo, paymentDetail, this.getPriceDetails.bind(this))
                            response.widgets = [...response.widgets, orderSummaryWidget]
                        }
                    }
                    if (!_.isEmpty(pageActionTitle)) {
                        return {
                            ...response,
                            pageActionTitle
                        }
                    }
                    return response
                }
                else {
                    const cultClass = await this.cultFitService.getCultClass(orderProduct.option.classId, baseOrder.userId, "CUREFIT_API", true, userContext.userProfile.subUserId, undefined, userContext.sessionInfo.deviceId)
                    const isPulseEnabled = CultUtil.isClassAvailableForPulse(cultClass, AppUtil.isCultPulseFeatureSupported(userContext))
                    const productInfoList: ProductInfo[] = baseOrder.productSnapshots.map((product) => ({
                        title: product.title,
                        quantity: product.quantity
                    }))
                    response.widgets.push(...await this.getCultSingleConfirmationWidgets(userContext, paymentDetail, product, billingInfo, baseOrder.deliveryCharges, orderProduct, cultClass, baseOrder, orderBuildViewParams.apiType))
                    response.orderMeta.orderType = "CULT_SINGLE"
                    response.orderMeta.vertical = "CULT_FIT"
                    response.orderMeta.productInfoList = productInfoList
                    if (isPulseEnabled) {
                        response.orderMeta.displayText = CultUtil.pulsifyClassName(cultClass, userContext)
                        response.orderMeta.title = CultUtil.pulsifyClassName(cultClass, userContext)
                    }
                    await this.addOfferAddonWidget(response, baseOrder, orderBuildViewParams.apiType)
                    await this.addNeuPassCheckoutWidget(userContext, response, baseOrder, orderBuildViewParams.apiType)
                    return this.appendReportIssueData("CultSingleSessionBooked", reportIssueAction, response)
                }
            }
            else if (product.productType == "PLAY" && isUpgradeMembershipOrder(baseOrder as Order)) {
                const clientMetadata = (baseOrder as Order).clientMetadata
                const upgradeData = clientMetadata as CultMindMembershipUpgradeMetadata
                const playProduct = await this.catalogueService.getProduct(upgradeData.originalPack.productId)
                /**
                 * These are two ways to end up in this block
                 * - While checking out an order
                 * - While checking transaction history after payment
                 */
                let pageActionTitle: string
                if (Array.isArray((baseOrder as Order).statusHistory)) {
                    const paidForOrder = (baseOrder as Order).statusHistory.find(s => s.status === "PAYMENT_SUCCESS")
                    if (!paidForOrder) {
                        // checkout view
                        response.widgets = await this.playUpgradeMembershipViewBuilder.buildUpgradeMembershipOrderCheckoutSummary(
                            product,
                            clientMetadata,
                            userContext,
                            playProduct.subLevelAccessListings)
                        // perform type-check here on clientMetadata
                        if (isMembershipUpgradeClientMetadata(clientMetadata)) {
                            response.orderMeta.isUpgradeMembership = true
                            const {currency, listingPrice} = response.orderMeta.price
                            if (currency === "INR") {
                                pageActionTitle = `Pay ${RUPEE_SYMBOL}${listingPrice}`
                            } else {
                                pageActionTitle = `Pay ${currency}${listingPrice}`
                            }
                        }
                    } else {
                        // order detail view after purchase
                        const orderSummaryWidget: OrderSummaryWidget = this.playUpgradeMembershipViewBuilder.buildUpgradeOrderSummaryWidget(product, billingInfo, paymentDetail, this.getPriceDetails.bind(this))
                        response.widgets = [...response.widgets, orderSummaryWidget]
                    }
                }
                if (!_.isEmpty(pageActionTitle)) {
                    return {
                        ...response,
                        pageActionTitle
                    }
                }
                return response
            }
            else if (product.productType === "GYMFIT_FITNESS_PRODUCT") {
                const resp = await this.serviceInterfaces.membershipService.filter({orderId: baseOrder.orderId})
                const gymMembership = resp.elements
                const gymfitFitnessProduct = await this.catalogueServicePMS.getProduct(product.productId)
                const isUpgradeFlowToPro = (orderBuildViewParams.order.clientMetadata as CultMindMembershipUpgradeMetadata)?.isUpgradeMembership
                if (isUpgradeFlowToPro) {
                    response.widgets.push(...(await this.getProUpgradeWidgets(userContext, gymfitFitnessProduct, paymentDetail, product, billingInfo, baseOrder, orderProduct, gymMembership, orderBuildViewParams.apiType)))
                }
                return response
            }

            if (product.productType === "FITNESS" && CultUtil.isCultEventOrder(orderProduct)) {
                const cultEvent = await this.cultFitService.getFitnessEvent(orderProduct.option.fitnessEventId)
                response.widgets.push(...await this.getCultEventSingleConfirmationWidgets(userContext, paymentDetail, product, billingInfo, baseOrder.deliveryCharges, orderProduct, cultEvent, baseOrder, orderBuildViewParams.apiType))
                response.orderMeta.orderType = "CULT_SINGLE"
                response.orderMeta.vertical = "CULT_FIT"
                await this.addNeuPassCheckoutWidget(userContext, response, baseOrder, orderBuildViewParams.apiType)
                await this.addOfferAddonWidget(response, baseOrder, orderBuildViewParams.apiType)
                return response
            }

            if (product.productType === "MIND" && !CultUtil.isCultEventOrder(orderProduct)) {
                const cultClass = await this.mindFitService.getCultClass(orderProduct.option.classId, baseOrder.userId, "CUREFIT_API", true, userContext.userProfile.subUserId, undefined, userContext.sessionInfo.deviceId)
                const productInfoList: ProductInfo[] = baseOrder.productSnapshots.map((product) => ({
                    title: product.title,
                    quantity: product.quantity
                }))
                response.widgets.push(...await this.getCultSingleConfirmationWidgets(userContext, paymentDetail, product, billingInfo, baseOrder.deliveryCharges, orderProduct, cultClass, baseOrder, orderBuildViewParams.apiType))
                response.orderMeta.orderType = "MIND_SINGLE"
                response.orderMeta.vertical = "MIND_FIT"
                response.orderMeta.productInfoList = productInfoList
                await this.addNeuPassCheckoutWidget(userContext, response, baseOrder, orderBuildViewParams.apiType)
                await this.addOfferAddonWidget(response, baseOrder, orderBuildViewParams.apiType)
                return this.appendReportIssueData("CultSingleSessionBooked", reportIssueAction, response)
            }
            else if (product.productType === "MIND" && CultUtil.isCultEventOrder(orderProduct)) {
                const cultEvent = await this.mindFitService.getFitnessEvent(orderProduct.option.fitnessEventId)
                response.widgets.push(...await this.getCultEventSingleConfirmationWidgets(userContext, paymentDetail, product, billingInfo, baseOrder.deliveryCharges, orderProduct, cultEvent, baseOrder, orderBuildViewParams.apiType))
                response.orderMeta.orderType = "CULT_SINGLE"
                response.orderMeta.vertical = "CULT_FIT"
                await this.addNeuPassCheckoutWidget(userContext, response, baseOrder, orderBuildViewParams.apiType)
                await this.addOfferAddonWidget(response, baseOrder, orderBuildViewParams.apiType)
                return response
            }
            else if (product.productType === "FOOD") {
                const isDelivered = await this.isOrderDelivered(baseOrder.orderId)
                const productInfoList: ProductInfo[] = baseOrder.productSnapshots.map((product) => ({
                    title: product.title,
                    quantity: product.quantity
                }))
                const packagingCharges = baseOrder.packagingCharges
                if (orderBuildViewParams.listingBrand !== "EAT_3P")
                    response.widgets.push(...await this.getFoodSingleConfirmationWidgets(userContext, paymentDetail, product, billingInfo,
                        baseOrder.deliveryCharges, packagingCharges, orderProduct, userAddress, numberOfProducts, baseOrder.orderId, allPossibleActionsForFoodBooking, foodBooking, orderDetail, _.get(baseOrder, "tipAmount", undefined) as TippingCharge))
                response.orderMeta.productInfoList = productInfoList
                response.orderMeta.orderType = orderBuildViewParams.listingBrand === "WHOLE_FIT" ? "WHOLE_FIT" : "EAT_SINGLE"
                response.orderMeta.vertical = "EAT_FIT"
                return this.appendReportIssueData(isDelivered ? "EatSingleMealDelivered" : "EatSingleMealNotDelivered", reportIssueAction, response)
            }
            else if (product.productType === "FIT_CLUB_MEMBERSHIP") {
                const productInfoList: ProductInfo[] = baseOrder.productSnapshots.map((product) => ({
                    title: product.title,
                    quantity: product.quantity
                }))
                const packagingCharges = baseOrder.packagingCharges
                response.widgets.push(...await this.getFitClubConfirmationWidgets(userContext, paymentDetail, product, billingInfo, orderProduct))
                response.orderMeta.productInfoList = productInfoList
                response.orderMeta.orderType = "FIT_CLUB_MEMBERSHIP"
                response.orderMeta.vertical = "EAT_FIT"
                return this.appendReportIssueData("FitClubPurchased", reportIssueAction, response)
            }

            else if (product.productType === "CONSULTATION") {
                const productInfo: ConsultationProduct = <ConsultationProduct>await this.catalogueService.getProduct(orderProduct.productId)
                let bookingDetail: BookingDetail
                if (CareUtil.isLiveWorkoutConsultationDoctorType(productInfo.doctorType)) {
                    bookingDetail = await this.healthfaceService.getBookingDetailV2({
                        bookingId: Number(baseOrder.products[0].option.bookingId),
                        tenant: productInfo.tenant,
                        options: {
                            isParentBookingRequired: false,
                            allRelatedBookingsRequired: false,
                            consultationInfoParams: {
                                isDoctorRequired: true,
                                isConsultationProductRequired: true,
                                patientDoctorAffinityRequired: CareUtil.isLivePTDoctorType(productInfo.doctorType)
                            },
                            isForceUpdate: false
                        }
                    })
                } else {
                    bookingDetail = await this.healthfaceService.getBookingDetail(Number(baseOrder.products[0].option.bookingId), undefined, false, undefined, undefined, productInfo.tenant, true)
                }
                if (AppUtil.isFromFlutterAppFlow(userContext) && (productInfo.tenant === "CARE" || productInfo.tenant === "MIND")) {
                    const productDetailedInfo = <ConsultationSellableProduct>(await this.healthfaceService.getProductInfoDetailsCached("CONSULTATION", undefined, productInfo.productId, productInfo.tenant))[0].baseSellableProduct
                    response.widgets.push(...await this.getFlutterTeleConsultationWidgets(
                        userContext,
                        baseOrder,
                        paymentDetail,
                        product,
                        billingInfo,
                        baseOrder.deliveryCharges,
                        orderProduct,
                        bookingDetail,
                        productDetailedInfo,
                        orderBuildViewParams.apiType,
                        )
                    )
                } else if ((!AppUtil.isSugarFitOrUltraFitApp(userContext) && productInfo.tenant !== "CULTFIT" && productInfo.tenant !== "TRANSFORM") && AppUtil.isNewTCCheckoutUISupported(userContext, productInfo)) {
                    const productDetailedInfo = <ConsultationSellableProduct>(await this.healthfaceService.getProductInfoDetailsCached("CONSULTATION", undefined, productInfo.productId, productInfo.tenant))[0].baseSellableProduct
                    response.widgets.push(...await this.getTeleConsultationWidgetsV2(
                        userContext,
                        paymentDetail,
                        product,
                        billingInfo,
                        baseOrder.deliveryCharges,
                        orderProduct,
                        bookingDetail,
                        productDetailedInfo,
                        orderBuildViewParams.apiType,
                    )
                    )
                } else {
                    response.widgets.push(...await this.getTeleConsultationWidgets(userContext, paymentDetail, product, billingInfo, baseOrder.deliveryCharges, response.orderMeta.price.listingPrice, orderProduct, bookingDetail, orderBuildViewParams.apiType))
                }
                const productInfoList: ProductInfo[] = baseOrder.productSnapshots.map((product) => ({
                    title: product.title,
                    quantity: product.quantity
                }))
                const orderSubtype = CareUtil.getCareConsultationSubType(productInfo)
                response.orderMeta.orderType = "CONSULTATION_SINGLE"
                response.orderMeta.vertical = "CARE_FIT"
                response.orderMeta.title = product.title
                response.orderMeta.subTitle = product.subTitle
                response.orderMeta.productInfoList = productInfoList
                response.orderMeta.orderSubType = orderSubtype
                response.orderMeta.doctorType = productInfo.doctorType
                this.addTitleToOrderMeta(response, summaryWidget)
                if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
                    response.action = this.getOrderCheckoutAction(userAgent, baseOrder)
                }
                const tncDetails: TncDetails = {
                    tncAction: CareUtil.getTherapySessionTncAction(),
                    policyAction: CareUtil.getTherapySessionPolicyAction()
                }
                response.tncDetails = undefined
                if (bookingDetail.consultationOrderResponse.doctorType === "ANXIETY_THERAPIST") {
                    // Display T&C details for Anxiety Coach
                    response.tncDetails = tncDetails
                }
                if (CareUtil.isLivePTSessionConsultation(bookingDetail.consultationOrderResponse) || CareUtil.isLiveSGTSessionConsultation(bookingDetail.consultationOrderResponse) || CareUtil.isTransformSessionConsultation(bookingDetail.consultationOrderResponse)) {
                    response.pageActionTitle = this.getLivePTOrderCheckoutActionTitle(baseOrder)
                    const isLiveSGTSessionConsultation = CareUtil.isLiveSGTSessionConsultation(bookingDetail.consultationOrderResponse)

                    if (isLiveSGTSessionConsultation && AppUtil.isLiveSGTCheckoutChangesSupported(userContext)) {
                        response.headerStyle = {
                            backgroundColor: "#f2f4f8",
                            // paddingBottom: 30,
                            alignItems: "center",
                            justifyContent: "flex-start",
                            marginLeft: 0,
                            paddingLeft: 20
                        }
                        response.navBarTitleText = {
                            fontSize: 22,
                            marginLeft: 13,
                            color: "#000000"
                        }
                    }
                }
                if (!_.isEmpty(orderBuildViewParams.apiType) && orderBuildViewParams.apiType === "ORDER_V1") {
                    this.addNeuPassEarnedCoinsWidget(baseOrder, response.widgets)
                }
                await this.addNeuPassCheckoutWidget(userContext, response, baseOrder, orderBuildViewParams.apiType)
                return this.appendReportIssueData("TeleconsultationSingleBooked", reportIssueAction, response)
            }
            else if (AppUtil.isSugarFitApp(userContext) && product.productType === "GROUP_CLASS") {
                const productInfo: GroupClassProduct = <GroupClassProduct>await this.catalogueService.getProduct(orderProduct.productId)
                const bookingDetail = await this.healthfaceService.getBookingDetail(Number(baseOrder.products[0].option.bookingId), undefined, false, undefined, undefined, productInfo.tenant, false)
                const center: Center = await this.healthfaceService.getCenterDetails(bookingDetail?.groupClassOrderResponse?.centerId, productInfo.tenant)
                response.widgets.push(...await this.getGroupClassConsultWidget(userContext, paymentDetail, product, billingInfo, baseOrder.deliveryCharges, response.orderMeta.price.listingPrice, productInfo, bookingDetail, center, orderBuildViewParams.apiType))

                const productInfoList: ProductInfo[] = baseOrder.productSnapshots.map((product) => ({
                    title: product.title,
                    quantity: product.quantity
                }))
                response.orderMeta.orderType = "CONSULTATION_SINGLE"
                response.orderMeta.vertical = "CARE_FIT"
                response.orderMeta.title = product.title
                response.orderMeta.subTitle = product.subTitle
                response.orderMeta.productInfoList = productInfoList
                this.addTitleToOrderMeta(response, summaryWidget)
                if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
                    response.action = this.getOrderCheckoutAction(userAgent, baseOrder)
                }
                const tncDetails: TncDetails = {
                    tncAction: CareUtil.getTherapySessionTncAction(),
                    policyAction: CareUtil.getTherapySessionPolicyAction()
                }
                response.tncDetails = undefined
                if (!_.isEmpty(orderBuildViewParams.apiType) && orderBuildViewParams.apiType === "ORDER_V1") {
                    this.addNeuPassEarnedCoinsWidget(baseOrder, response.widgets)
                }
                return this.appendReportIssueData("TeleconsultationSingleBooked", reportIssueAction, response)
            }
            else if (product.productType === "FITNESS_ACCESSORIES") {
                const cultCenter = await this.catalogueService.getCultCenter(orderProduct.option.centerId)
                response.widgets.push(...await this.getAccessoriesConfirmationWidgets(userAgent, paymentDetail, product, billingInfo, baseOrder.deliveryCharges, orderProduct, cultCenter, baseOrder, orderBuildViewParams.apiType))
                response.orderMeta.orderType = "CULT_SINGLE"
                response.orderMeta.vertical = "CULT_FIT"
                await this.addNeuPassCheckoutWidget(userContext, response, baseOrder, orderBuildViewParams.apiType)
                // Temp hack , get proper report issue code
                return this.appendReportIssueData("EatPack", reportIssueAction, response)
            }
            else if (product.productType === "BUNDLE" || ((product.productType === "DEVICE" || product.productType === "SF_CONSUMABLE") && AppUtil.isSugarFitOrUltraFitApp(userContext))) {
                response.orderMeta.orderType = this.getOrderType(product)
                const productInfo = <DiagnosticProduct>await this.catalogueService.getProduct(orderProduct.productId)
                response.orderMeta.vertical = productInfo && CareUtil.isTransformTenant(productInfo.subCategoryCode) ? "TRANSFORM" : "CARE_FIT"
                if (!_.isEmpty(product.option.parentProductCode)) {
                    response.orderMeta.title = (await this.catalogueService.getProduct(product.option.parentProductCode)).title
                } else {
                    response.orderMeta.title = product.title
                }
                const productInfoList: ProductInfo[] = baseOrder.productSnapshots.map((product) => ({
                    title: product.title,
                    quantity: product.quantity
                }))
                response.orderMeta.displayText = response.orderMeta.title
                response.orderMeta.subTitle = product.subTitle
                response.orderMeta.productInfoList = productInfoList
                response.orderMeta.subCategoryCode = this.getSubCategoryCode(product)
                this.addTitleToOrderMeta(response, summaryWidget)
                if (orderBuildViewParams?.apiType === "ORDER_V1") {
                    this.addNeuPassEarnedCoinsWidget(baseOrder, response.widgets)
                }
                await this.addNeuPassCheckoutWidget(userContext, response, baseOrder, orderBuildViewParams.apiType)
                if (CareUtil.isTransformTenant(productInfo.subCategoryCode)) {
                    await this.addOfferAddonWidget(response, baseOrder, orderBuildViewParams.apiType)
                }
                return this.appendReportIssueData("TeleconsultationSingleBooked", reportIssueAction, response)
            }
            else if (product.productType === "DIAGNOSTICS") {
                const userContext = orderBuildViewParams.userContext
                const tenant: HealthfaceTenant = CareUtil.getHealthfaceTenant(product.productType, undefined, userContext.sessionInfo.orderSource)
                const bookingDetail: BookingDetail = await this.healthfaceService.getBookingDetail(Number(baseOrder.products[0].option.bookingId), undefined, false, undefined, undefined, tenant)
                const productCodes = baseOrder?.products?.map((product) => product.productId)
                const productInfos: HealthfaceProductInfo[] = await this.healthfaceService.getProductInfoDetails("DIAGNOSTICS", {
                    productCodeCsv: productCodes.join(","),
                    patientId: baseOrder?.products[0]?.option?.patientId,
                    skipProductDetails: false,
                }, tenant)
                const patientDetails = baseOrder?.products[0]?.option?.patientId ? await this.healthfaceService.getPatientDetails(baseOrder?.products[0]?.option?.patientId) : undefined
                const isPaymentRequired = billingInfo.total > 0
                const isSlotTimerWidgetSupported = userContext.sessionInfo.appVersion >= SLOT_TIMER_WIDGET_SUPPORTED && !AppUtil.isSugarFitOrUltraFitApp(userContext)
                userContext.sessionInfo.userAgent === "APP" && orderBuildViewParams.apiType !== "ORDER_V1" && isSlotTimerWidgetSupported && response.widgets.push({
                    ...CareUtil.getSlotTimerWidget(),
                    hasDividerBelow: false
                })
                if (!AppUtil.isSugarFitOrUltraFitApp(userContext)) {
                    response.widgets.push(...await this.getDiagnosticsTestBookingWidgets(userContext, userAgent, paymentDetail, product, billingInfo, baseOrder.deliveryCharges, orderProduct, bookingDetail, baseOrder, productCodes, productInfos, patientDetails, orderBuildViewParams.apiType))
                }
                const productInfoList: ProductInfo[] = baseOrder.productSnapshots.map((product) => ({
                    title: product.title,
                    quantity: product.quantity
                }))
                response.orderMeta.orderType = "DIAGNOSTICS_SINGLE"
                const productInfo = <DiagnosticProduct>await this.catalogueService.getProduct(orderProduct.productId)
                response.orderMeta.vertical = productInfo && CareUtil.isTransformTenant(productInfo.subCategoryCode) ? "TRANSFORM" : "CARE_FIT"
                response.orderMeta.title = product.title
                response.orderMeta.subTitle = product.subTitle
                response.orderMeta.productInfoList = productInfoList
                if (AppUtil.isWeb(userContext) && isPaymentRequired) {
                    response.action = this.getOrderCheckoutAction(userAgent, baseOrder)
                }
                this.addTitleToOrderMeta(response, summaryWidget)
                response.orderMeta.subCategoryCode = product.productType
                await this.addNeuPassCheckoutWidget(userContext, response, baseOrder, orderBuildViewParams.apiType)
                return this.appendReportIssueData("TeleconsultationSingleBooked", reportIssueAction, response)
            }
            else if (product.productType === "GEAR") {
                // response.widgets.push(... await this.getGearOrderStatusWidgets())
                response.orderMeta.orderType = "GEAR"
                response.orderMeta.vertical = "CULT_GEAR"
                response.orderMeta.title = product.title
                response.orderMeta.subTitle = product.subTitle
                // overriding productIds with master product ids as we do not need the sku ids
                response.orderMeta.productIds = baseOrder?.productSnapshots?.map((product) => product.masterProductId)
                if (orderBuildViewParams?.apiType === "ORDER_V1") {
                    this.addNeuPassEarnedCoinsWidget(baseOrder, response.widgets)
                }
                if (!AppUtil.isCultSportWebApp(userContext)) {
                    await this.addNeuPassCheckoutWidget(userContext, response, baseOrder, orderBuildViewParams.apiType)
                }
                return response
            }
            else if (product.productType === "VACCINE") {
                response.orderMeta.orderType = "VACCINE"
                response.orderMeta.vertical = "CULT_FIT"
                response.orderMeta.title = product.title
                response.orderMeta.subTitle = product.subTitle
                return response
            }
            else if (product.productType === "CULT_BIKE") {
                response.orderMeta.vertical = "WELLNESS"
                response.widgets.push(...await this.getCultBikeOrderDetailWidgets(userContext, paymentDetail, product, billingInfo, baseOrder, orderBuildViewParams.apiType))
                await this.addNeuPassCheckoutWidget(userContext, response, baseOrder, orderBuildViewParams.apiType)
                return response
            }
        }
    }

    async getBoosterPackBenefits(boosterPack: OfflineFitnessPack): Promise<ProductListWidget> {
        const benefitItems: InfoCard[] = []
        boosterPack?.product?.benefits?.map((benefit: any) => {
            benefitItems.push({
                subTitle: benefit?.displayTitle,
                icon: "/image/icons/dot_small.png",
                cellStyle: {
                    paddingBottom: 0
                },
                subtitleStyle: {
                    fontFamily: "BrandonText-Medium",
                    fontSize: 16,
                    color: "#000000"
                }
            })
        })

        return {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "SMALL",
            hideSepratorLines: true,
            header: {
                title: "Booster Benefits"
            },
            items: benefitItems,
            noTopPadding: false,
            layoutProps: {spacing: { top: 20, bottom: 0 } }
        }
    }

    async getPlusPackBenefits(benefits: any): Promise<ProductListWidget> {
        const benefitItems: InfoCard[] = []
        benefits?.map((benefit: any) => {
            if (benefit?.displayTitle != null && !_.isEmpty(benefit?.displayTitle))
                benefitItems.push({
                    subTitle: benefit?.displayTitle,
                    icon: "/image/icons/dot_small.png",
                    cellStyle: {
                        paddingBottom: 0
                    },
                    subtitleStyle: {
                        fontFamily: "BrandonText-Medium",
                        fontSize: 16,
                        color: "#000000"
                    }
                })
        })
        if (benefitItems.length === 0)
            return null

        return {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "SMALL",
            hideSepratorLines: true,
            header: {
                title: "What else you got"
            },
            items: benefitItems,
            noTopPadding: false,
            layoutProps: {spacing: { top: 20, bottom: 0 } }
        }
    }

    private async getRefundDetailsWidgets(userContext: UserContext, order: BaseOrder, paymentDetail: PaymentDetail, refundDetailsPromise: Promise<RefundDetailsResponse[]>): Promise<IOrderRefundWidget[]> {
        if (_.isNil(paymentDetail) || _.isNil(refundDetailsPromise) || _.isNil(order)) {
            return undefined
        }
        const paymentData: PaymentData = PaymentUtil.findPaymentByPaymentId(order, paymentDetail.successfulPaymentId, paymentDetail.paymentChannel)
        if (_.isNil(paymentData)) {
            return undefined
        }
        const orderRefundWidgetBuilder: OrderRefundWidgetBuilder = new OrderRefundWidgetBuilder()
        return orderRefundWidgetBuilder.getOrderRefundWidgetList({
            userContext: userContext,
            orderId: order.orderId,
            paymentId: paymentData.paymentId,
            paymentChannel: paymentData.channel,
            refundDetailsPromise: refundDetailsPromise,
            logger: this.logger
        })
    }

    private async getProUpgradeWidgets(userContext: UserContext, gymfitFitnessProduct: OfflineFitnessPack, paymentDetail: PaymentDetail, product: Product, billingInfo: BillingInfo, order: Order, orderProduct: OrderProduct, gymMemberships: Membership[], apiType?: "ORDER_CHECKOUT" | "ORDER_V1") {
        const tz = userContext.userProfile.timezone
        const widgetViews: WidgetView[] = []
        const upgradePricePayload: UpgradePricingRequest = {
            membershipServiceId: (order.clientMetadata as CultMindMembershipUpgradeMetadata).membershipId,
            targetAccessLevel: CatalogueServiceUtilities.getAccessLevel(gymfitFitnessProduct) as GymfitAccessLevel,
            targetAccessLevelId: CatalogueServiceUtilities.getExternalAccessLevelId(gymfitFitnessProduct),
            targetProductType: gymfitFitnessProduct.productType as GymfitProductType
        }
        const upgradeFeeBreakdown = await this.gymfitService.getUpgradePrices(upgradePricePayload)
        const upgradeMembershipSummaryWidget: MembershipUpgradeSummaryWidget = {
            widgetType: "MEMBERSHIP_UPGRADE_SUMMARY_WIDGET",
            title: "UPGRADE PACK",
            originalPack: {
              title: "",
              image: "/image/packs/upgrade/cultpass_select.png"
            },
            destinationPack: {
              title: "",
              image: "/image/packs/gymfit/GYMFIT25/2_mag.jpg"
            },
            arrowImageUrl: "image/down_arrow.png"
        }
        widgetViews.push(upgradeMembershipSummaryWidget)
        const membershipPostUpdateStatusWidget: MembershipPostUpdateStatusWidget = {
            widgetType: "MEMBERSHIP_POST_UPDATE_STATUS_WIDGET",
            status: [
              {
                header: "Membership Ends on",
                infoText: moment(TimeUtil.addDays(tz, TimeUtil.todaysDate(tz), upgradeFeeBreakdown.daysLeft)).format("D MMM YYYY")
              },
              {
                header: "Remaining days",
                infoText: `${upgradeFeeBreakdown.daysLeft}`
              },
            ]
        }
        widgetViews.push(membershipPostUpdateStatusWidget)
        const feesDetailWidget: MembershipFeesDetailWidget = {
            widgetType: "MEMBERSHIP_FEE_INFO_DETAIL_WIDGET",
            header: "Upgrade Fees Details",
            subHeader:
              "An amount is being charged because the pack you want to upgrade to costs more than your current pack",
            feeItemRows: [
              {
                label: `Cost of ${gymfitFitnessProduct.title}`,
                data: `₹ ${upgradeFeeBreakdown.remainingDaysAmount}`
              },
              {
                label: "Number of days remaining",
                data: `${upgradeFeeBreakdown.daysLeft}`
              },
              {
                label: `Estimated price difference for ${upgradeFeeBreakdown.daysLeft} days`,
                data: `₹ ${upgradeFeeBreakdown.totalAmount -
                  upgradeFeeBreakdown.fees}`
              },
              {
                label: "Upgrade fee",
                data: `₹ ${upgradeFeeBreakdown.fees}`
              }
            ],
            bottomLine: {
              label: "Total upgrade fee to be paid",
              data: `₹ ${upgradeFeeBreakdown.totalAmount}`
            }
        }
        const totalFeeLineItemWidget: MembershipFeeInfoLineItemWidget = {
            widgetType: "MEMBERSHIP_FEE_INFO_LINE_WIDGET",
            text: "Total Payable",
            backgroundColor: "rgba(247, 247, 247, 1.0)",
            fees: `₹ ${upgradeFeeBreakdown.totalAmount}`,
            infoIcon: true,
            infoIconAction: {
              type: "SHOW_UPGRADE_MEMBERSHIP_FEE_DETAILS_MODAL",
              actionType: "SHOW_UPGRADE_MEMBERSHIP_FEE_DETAILS_MODAL",
              meta: {
                widgets: [{ ...feesDetailWidget }]
              }
            },
            faded: false
          }
        widgetViews.push(totalFeeLineItemWidget)

        if (upgradeFeeBreakdown.creditsLeft >= 0 && upgradeFeeBreakdown.creditsToIncrease > 0) {
            const creditsModal: MembershipFeesDetailWidget = {
                widgetType: "MEMBERSHIP_FEE_INFO_DETAIL_WIDGET",
                header: "Calculation of Transferable Days",
                subHeader:
                    `Credits are transferred proportionally, reflecting your current membership type, to the membership at the new center`,
                feeItemRows: [
                    {
                        label: `Credits remaining`,
                        data: `${upgradeFeeBreakdown.creditsLeft} credits`,
                        iconUrl: CREDIT_PILL_ICON
                    },
                    {
                        label: "Extra credits adjusted",
                        data: `+${upgradeFeeBreakdown.creditsToIncrease} credits`,
                        iconUrl: CREDIT_PILL_ICON
                    }
                ],
                bottomLine: {
                    label: `Total Credits`,
                    data: `${upgradeFeeBreakdown.creditsLeft + upgradeFeeBreakdown.creditsToIncrease} credits`,
                    iconUrl: CREDIT_PILL_ICON
                }
            }
            const creditsWidget: MembershipFeeInfoLineItemWidget = {
                widgetType: "MEMBERSHIP_FEE_INFO_LINE_WIDGET",
                text: "Additional Credits",
                backgroundColor: "rgba(247, 247, 247, 1.0)",
                fees: `+${upgradeFeeBreakdown.creditsToIncrease} credits`,
                infoIcon: true,
                infoIconAction: {
                    type: "SHOW_UPGRADE_MEMBERSHIP_FEE_DETAILS_MODAL",
                    actionType: "SHOW_UPGRADE_MEMBERSHIP_FEE_DETAILS_MODAL",
                    meta: {
                        widgets: [{...creditsModal}]
                    }
                },
                faded: false,
                iconUrl: CREDIT_PILL_ICON
            }
            const pointsToNoteWidget = this.pointsToNoteWidget()
            widgetViews.push(creditsWidget)
            widgetViews.push(pointsToNoteWidget)
        } else if (_.isNil(upgradeFeeBreakdown.creditsLeft) && upgradeFeeBreakdown.creditsLeft !== 0 && upgradeFeeBreakdown.creditsToIncrease > 0) {
            const creditsWidget: MembershipFeeInfoLineItemWidget = {
                widgetType: "MEMBERSHIP_FEE_INFO_LINE_WIDGET",
                text: "Credits to access other centers",
                backgroundColor: "rgba(247, 247, 247, 1.0)",
                fees: `${upgradeFeeBreakdown.creditsToIncrease} credits`,
                infoIcon: false,
                faded: false,
                iconUrl: CREDIT_PILL_ICON
            }
            widgetViews.push(creditsWidget)
        }

        return widgetViews
    }

    private async getGymFitPackWidgets(userContext: UserContext, gymfitFitnessProduct: AugmentedOfflineFitnessPack, paymentDetail: PaymentDetail, product: Product, billingInfo: BillingInfo, order: Order, orderProduct: OrderProduct, gymMemberships: Membership[], apiType?: "ORDER_CHECKOUT" | "ORDER_V1") {
        const isWeb = AppUtil.isWeb(userContext)
        const widgetViews: WidgetView[] = []
        let showGst = false
        const isPartOfGstSegment = await AppUtil.doesUserBelongToGSTsplitExperiment(userContext, this.serviceInterfaces)
        if (isPartOfGstSegment) {
            showGst = true
        }
        const orderSummaryWidget = this.getOrderSummaryWidget(userContext.sessionInfo.userAgent, paymentDetail, product, billingInfo, order.deliveryCharges, false, false, undefined, undefined, undefined, order, showGst)
        const tz = userContext.userProfile.timezone
        const endDate = gymMemberships[0]?.end ? TimeUtil.formatEpochInTimeZone(tz, gymMemberships[0].end, "Do MMMM YYYY") : TimeUtil.formatDateStringInTimeZone(
            TimeUtil.addDays(tz, orderProduct.option.startDate, gymfitFitnessProduct.product.durationInDays)
            , tz, "Do MMMM YYYY")
        widgetViews.push(orderSummaryWidget)
        if (!_.isEmpty(apiType) && apiType === "ORDER_V1") {
            this.addNeuPassEarnedCoinsWidget(order, widgetViews)
        }
        const awayCenterBenefit: BenefitEntry = gymfitFitnessProduct.product.benefits?.find(benefit => benefit.name === "CENTER_AWAY")
        const exhaustiveAwayCenterBenefit: ExhaustivePackBenefit = gymfitFitnessProduct.augments?.exhaustiveBenefitList?.find(exhaustiveBenefit => exhaustiveBenefit.name === "CENTER_AWAY")
        const cultBenefit: BenefitEntry = gymfitFitnessProduct.product?.benefits?.find(benefit => benefit.name === "CULT")
        const exhaustiveCultBenefit: ExhaustivePackBenefit = gymfitFitnessProduct.augments?.exhaustiveBenefitList?.find(exhaustiveBenefit => exhaustiveBenefit.name === "CULT")
        const awayCreditsBenefit: BenefitEntry = gymfitFitnessProduct.product.benefits?.find(benefit => benefit.name === "ACCESS_CREDITS")
        const exhaustiveAwayCreditsBenefit: ExhaustivePackBenefit = gymfitFitnessProduct.augments?.exhaustiveBenefitList?.find(exhaustiveBenefit => exhaustiveBenefit.name === "ACCESS_CREDITS")
        const liveBenefit: BenefitEntry = gymfitFitnessProduct.product.benefits?.find(benefit => benefit.name === "CF_LIVE")
        const exhaustiveLiveBenefit: ExhaustivePackBenefit = gymfitFitnessProduct.augments?.exhaustiveBenefitList?.find(exhaustiveBenefit => exhaustiveBenefit.name === "CF_LIVE")


        const cultBenefitText = cultBenefit || exhaustiveCultBenefit ? `\n\n• Includes ${exhaustiveCultBenefit ? exhaustiveCultBenefit.tickets : cultBenefit?.tickets} session per month at ELITE gyms or cult centers.` : ""
        let calloutText = ""

        const isCenterLevelPack = CatalogueServiceUtilities.getAccessLevel(gymfitFitnessProduct) === GymfitAccessLevel.CENTER

        // let calloutText = ""
        if (isCenterLevelPack && isWeb) {
            calloutText = `• Unlimited access To  ${gymfitFitnessProduct.clientMetadata?.centerName} in ${gymfitFitnessProduct.clientMetadata?.cityId} from ${orderProduct.option.startDate} Onwards.\n`
        } else if (isCenterLevelPack && !isWeb && (awayCenterBenefit || exhaustiveAwayCenterBenefit)) {
            const center = await this.centerService.getCenterById(parseInt(CatalogueServiceUtilities.getExternalAccessLevelId(gymfitFitnessProduct)))
            calloutText = `• Unlimited access to ${center.name} till ${endDate}.\n\n• Includes ${exhaustiveAwayCenterBenefit ? exhaustiveAwayCenterBenefit.tickets : awayCenterBenefit.tickets} session per month in other centres in ${this.cityService.getCityById(gymfitFitnessProduct.clientMetadata?.cityId).name}.`
        } else if (isCenterLevelPack && !isWeb && (awayCreditsBenefit || exhaustiveAwayCreditsBenefit)) {
            const center = await this.centerService.getCenterById(parseInt(CatalogueServiceUtilities.getExternalAccessLevelId(gymfitFitnessProduct)))
            calloutText = `• Unlimited access to ${center.name} till ${endDate}.\n\n• Includes ${exhaustiveAwayCreditsBenefit ? exhaustiveAwayCreditsBenefit.tickets : awayCreditsBenefit.tickets} credits to access any centre across India.`
        } else if (!isWeb && (awayCreditsBenefit || exhaustiveAwayCreditsBenefit)) {
            if (liveBenefit || exhaustiveLiveBenefit) calloutText = `• Unlimited access to LIVE workouts & all PRO gyms in selected city till ${endDate}.`
            if (userContext.sessionInfo.osName.toLowerCase() === "ios") {
                calloutText = `• Unlimited access to all PRO gyms in selected city till ${endDate}.`
            }
            calloutText += `\n\n• Includes ${exhaustiveAwayCreditsBenefit ? exhaustiveAwayCreditsBenefit.tickets : awayCreditsBenefit.tickets} credits to access any centre across India.`
        } else {
            if (liveBenefit || exhaustiveLiveBenefit) calloutText = `• Unlimited access to LIVE workouts & all PRO gyms in selected city till ${endDate}.`
            calloutText += cultBenefitText
            if (userContext.sessionInfo.osName.toLowerCase() === "ios") {
                calloutText = `• Unlimited access to all PRO gyms in selected city till ${endDate}.${cultBenefitText}`
            }
        }

        widgetViews.push(this.getOrderDateWidget(userContext, orderProduct.option.startDate, true,
            endDate ? calloutText : undefined))
        const city = userContext.userProfile.city
        let cityName = city.name
        if (city.parentCityId && await AppUtil.isCitySplitFeatureSupported(userContext)) {
            if (city.cityId === REST_OF_MUMBAI_CITY_ID) {
                cityName = "Mumbai (excluding Navi Mumbai & Thane)"
            } else if (city.cityId === NAVI_MUMBAI_AND_THANE_CITY_ID) {
                cityName = `Only ${cityName}`
            }
        }
        if (isCenterLevelPack && isWeb) {
            widgetViews.push(this.getCenterOfPurchaseWidgetSelectPack(gymfitFitnessProduct.clientMetadata?.centerName))
        } else {
            widgetViews.push(this.getCityOfPurchaseWidget(userContext, gymMemberships[0]?.metadata?.cityId ?? cityName))
        }
        return widgetViews
    }

    private async getLuxPackWidgets(userContext: UserContext, luxFitnessProduct: LuxFitnessProduct, paymentDetail: PaymentDetail, product: Product, billingInfo: BillingInfo, order: Order, orderProduct: OrderProduct, luxMemberships: Membership[], apiType?: "ORDER_CHECKOUT" | "ORDER_V1") {
        const widgetViews: WidgetView[] = []
        let showGst = false
        const centerName = luxFitnessProduct.centerName
        const isPartOfGstSegment = await AppUtil.doesUserBelongToGSTsplitExperiment(userContext, this.serviceInterfaces)
        if (isPartOfGstSegment) {
            showGst = true
        }
        const orderSummaryWidget = this.getOrderSummaryWidget(userContext.sessionInfo.userAgent, paymentDetail, product, billingInfo, order.deliveryCharges, false, false, undefined, undefined, undefined, order, showGst)
        const tz = userContext.userProfile.timezone
        const endDate = luxMemberships[0]?.end ? TimeUtil.formatEpochInTimeZone(tz, luxMemberships[0].end, "Do MMMM YYYY") : TimeUtil.formatDateStringInTimeZone(
            TimeUtil.addDays(tz, orderProduct.option.startDate, luxFitnessProduct.durationInDays)
            , tz, "Do MMMM YYYY")
        widgetViews.push(orderSummaryWidget)
        if (!_.isEmpty(apiType) && apiType === "ORDER_V1") {
            this.addNeuPassEarnedCoinsWidget(order, widgetViews)
        }
        const calloutText = `• Unlimited access to ${centerName} till ${endDate}.`
        widgetViews.push(this.getOrderDateWidget(userContext, orderProduct.option.startDate, true,
            endDate && centerName ? calloutText : undefined))
        if (!_.isNil(centerName)) {
            widgetViews.push(this.getCenterOfPurchaseWidget(userContext, centerName))
        }
        return widgetViews
    }

    private async getLuxPackWidgetsV2(userContext: UserContext, luxPack: OfflineFitnessPack, paymentDetail: PaymentDetail, product: Product, billingInfo: BillingInfo, order: Order, orderProduct: OrderProduct, luxMemberships: Membership[], apiType?: "ORDER_CHECKOUT" | "ORDER_V1") {
        const widgetViews: WidgetView[] = []
        let showGst = false
        const centerName = luxPack.clientMetadata?.centerName
        const isPartOfGstSegment = await AppUtil.doesUserBelongToGSTsplitExperiment(userContext, this.serviceInterfaces)
        if (isPartOfGstSegment) {
            showGst = true
        }
        const orderSummaryWidget = this.getOrderSummaryWidget(userContext.sessionInfo.userAgent, paymentDetail, product, billingInfo, order.deliveryCharges, false, false, undefined, undefined, undefined, order, showGst)
        const tz = userContext.userProfile.timezone
        const endDate = luxMemberships[0]?.end ? TimeUtil.formatEpochInTimeZone(tz, luxMemberships[0].end, "Do MMMM YYYY") : TimeUtil.formatDateStringInTimeZone(
            TimeUtil.addDays(tz, orderProduct.option.startDate, luxPack.product.durationInDays)
            , tz, "Do MMMM YYYY")
        widgetViews.push(orderSummaryWidget)
        if (!_.isEmpty(apiType) && apiType === "ORDER_V1") {
            this.addNeuPassEarnedCoinsWidget(order, widgetViews)
        }
        const calloutText = `• Access to ${centerName} till ${endDate}.`
        widgetViews.push(this.getOrderDateWidget(userContext, orderProduct.option.startDate, true,
            endDate && centerName ? calloutText : undefined))
        if (!_.isNil(centerName)) {
            widgetViews.push(this.getCenterOfPurchaseWidget(userContext, centerName))
        }
        return widgetViews
    }

    private async getPauseAddOnPackWidgets(userContext: UserContext, paymentDetail: PaymentDetail, product: Product, billingInfo: BillingInfo,
                                           order: Order, orderProduct: OrderProduct, packInfo?: OfflineFitnessPack, apiType?: string): Promise<WidgetView[]> {
        const widgetViews: WidgetView[] = []
        let showGst = false
        const isPartOfGstSegment = await AppUtil.doesUserBelongToGSTsplitExperiment(userContext, this.serviceInterfaces)
        if (isPartOfGstSegment) {
            showGst = true
        }
        const orderSummaryWidget = this.getOrderSummaryWidget(userContext.sessionInfo.userAgent, paymentDetail, product, billingInfo, order.deliveryCharges, false, false, undefined, undefined, undefined, order, showGst)
        const tz = userContext.userProfile.timezone
        widgetViews.push(orderSummaryWidget)
        return widgetViews
    }

    private getOrderType(product: Product): orderType {
        if (product && product.productType === "BUNDLE") {
            const categoryId = _.get(product, "categoryId", "")
            switch (categoryId) {
                case "MP":
                case "MP_V2":
                case "MP_OT":
                case "MP_SUBS":
                    return "MANAGED_PLAN"
                case "HCU_PACK":
                case "HCU":
                    return "HCU"
                case "DIAG_PACK":
                    return "SCREENING_PACK"
                default:
                    return "DIAGNOSTICS_SINGLE"
            }
        }
        return "DIAGNOSTICS_SINGLE"
    }

    private getSubCategoryCode(product: Product): any {
        if (product && product.productType === "BUNDLE") {
            const categoryId = _.get(product, "categoryId", "")
            switch (categoryId) {
                case "MP":
                case "MP_V2":
                case "MP_OT":
                case "MP_SUBS":
                    return "MANAGED_PLAN"
                case "HCU_PACK":
                case "HCU":
                    return "HCU"
                case "DIAG_PACK":
                    return "SCREENING_PACK"
                case "CONSULTATION_PACK":
                case "MIND_THERAPY":
                case "NUTRITIONIST":
                case "PHYSIOTHERAPY":
                case "HAIR_PACK":
                case "SKIN_PACK":
                case "BEAUTY_PACK":
                case "DIAGNOSTICS":
                    return categoryId
                default:
                    return "DIAGNOSTICS_SINGLE"
            }
        }
        return "DIAGNOSTICS_SINGLE"
    }

    private async isOrderDelivered(orderId: string): Promise<boolean> {
        const fulfilment: FoodFulfilment = await this.fulfilmentService.getFoodFulfilmentByOrderId(orderId)
        return fulfilment && fulfilment.packState && fulfilment.packState === "COMPLETED"
    }

    private addTitleToOrderMeta(response: OrderCheckoutDetail, summaryWidget: BaseOrderSummaryWidget) {
        if (summaryWidget) {
            response.orderMeta.title = summaryWidget.title ? summaryWidget.title.replace(/\W/g, "") : undefined
            response.orderMeta.subTitle = summaryWidget.subTitle
        }
    }

    private getCultOfferWidget(campaign: Campaign): DescriptionWidget {
        const infoCards: InfoCard[] = []
        infoCards.push({
            title: "Offer: " + campaign.info,
            subTitle: campaign.tnc,
            moreIndex: 168
        })
        return new DescriptionWidget(infoCards)
    }
    private appendReportIssueData(issueCategory: IssueCategory, action: string, response: OrderCheckoutDetail): Promise<OrderCheckoutDetail> {
        return this.CRMIssueService.getIssues(issueCategory).then(customerIssueTypes => {
            response.reportData = {
                action: action,
                meta: _.map(customerIssueTypes, customerIssueType => {
                    return {
                        code: customerIssueType.code,
                        title: customerIssueType.subject,
                        confirmation: customerIssueType.confirmation
                    }
                })
            }
            return response
        })
    }

    private async getCultSingleConfirmationWidgets(userContext: UserContext, paymentDetail: PaymentDetail, product: Product, billingInfo: BillingInfo, deliveryCharge: DeliveryCharge, orderProduct: OrderProduct, cultClass: CultClass, order?: BaseOrder, apiType?: "ORDER_CHECKOUT" | "ORDER_V1"): Promise<WidgetView[]> {
        const widgetViews: WidgetView[] = []
        if (CultUtil.isClassAvailableForPulse(cultClass, AppUtil.isCultPulseFeatureSupported(userContext))) {
            // pulse class booking
            product = {
                ...product,
                title: CultUtil.pulsifyClassName(cultClass, userContext)
            }
        }
        const orderSummaryWidget: OrderSummaryWidget = this.getOrderSummaryWidget(userContext.sessionInfo.userAgent, paymentDetail, product, billingInfo, deliveryCharge, false, false)
        let thumbnailimagedoc
        if (!_.isNil(cultClass.Workout) && !_.isNil(cultClass.Workout.documents))
            thumbnailimagedoc = cultClass.Workout.documents.find((x) => { if (x.tagName === "PRODUCT_BNR") return true; return false })
        if (!_.isNil(thumbnailimagedoc)) {
            orderSummaryWidget.thumbnailImages = [thumbnailimagedoc.URL]
        }
        widgetViews.push(this.getOrderDateWidget(userContext, cultClass.date, false))
        widgetViews.push(this.getCultCenterWidget(cultClass.Center.name))
        widgetViews.push(orderSummaryWidget)
        if (!_.isEmpty(apiType) && !_.isEmpty(order) && apiType === "ORDER_V1") {
            this.addNeuPassEarnedCoinsWidget(order, widgetViews)
        }
        return widgetViews
    }

    private async getCultEventSingleConfirmationWidgets(userContext: UserContext, paymentDetail: PaymentDetail, product: Product, billingInfo: BillingInfo, deliveryCharge: DeliveryCharge, orderProduct: OrderProduct, cultEvent: FitnessEvent, order?: BaseOrder, apiType?: "ORDER_CHECKOUT" | "ORDER_V1"): Promise<WidgetView[]> {
        const widgetViews: WidgetView[] = []
        const orderSummaryWidget = this.getOrderSummaryWidget(userContext.sessionInfo.userAgent, paymentDetail, product, billingInfo, deliveryCharge, false, false)
        const thumbnailimagedoc = cultEvent.documents.find((x) => {
            if (x.tagName === "PRODUCT_BNR") return true
            return false
        })
        if (!_.isEmpty(thumbnailimagedoc)) {
            orderSummaryWidget.thumbnailImages = [thumbnailimagedoc.URL]
        }
        widgetViews.push(this.getOrderDateWidget(userContext, cultEvent.launchDate, false))
        if (!_.isNil(cultEvent.Address))
            widgetViews.push(this.getCultEventLocationWidget(cultEvent.Address))
        widgetViews.push(orderSummaryWidget)
        if (!_.isEmpty(apiType) && !_.isEmpty(order) && apiType === "ORDER_V1") {
            this.addNeuPassEarnedCoinsWidget(order, widgetViews)
        }
        return widgetViews
    }

    private async getAccessoriesConfirmationWidgets(userAgent: UserAgent, paymentDetail: PaymentDetail, product: Product, billingInfo: BillingInfo, deliveryCharge: DeliveryCharge, orderProduct: OrderProduct, cultCenter: CultCenter, order?: BaseOrder, apiType?: "ORDER_CHECKOUT" | "ORDER_V1"): Promise<WidgetView[]> {
        const widgetViews: WidgetView[] = []
        // Temp hack
        const title = `September 18th onwards`
        const subTitle = `You can collect your band at your chosen centre between 18th & 30th September`
        const dateWidget: InfoWidget = {
            widgetType: "INFO_WIDGET",
            icon: "DATE",
            title: title,
            subTitle: subTitle
        }
        widgetViews.push(dateWidget)
        const centerWidget: InfoWidget = this.getCultCenterWidget(cultCenter.name)
        centerWidget.title = "Pickup Center"
        widgetViews.push(centerWidget)
        widgetViews.push(this.getOrderSummaryWidget(userAgent, paymentDetail, product, billingInfo, deliveryCharge, false, false))
        if (!_.isEmpty(apiType) && !_.isEmpty(order) && apiType === "ORDER_V1") {
            this.addNeuPassEarnedCoinsWidget(order, widgetViews)
        }
        return widgetViews
    }

    private async getFitnessFirstConfirmationWidgets(userContext: UserContext, paymentDetail: PaymentDetail, product: Product, billingInfo: BillingInfo, order: Order, orderProduct: OrderProduct) {
        const widgetViews: WidgetView[] = []
        const orderSummaryWidget = this.getOrderSummaryWidget(userContext.sessionInfo.userAgent, paymentDetail, product, billingInfo, order.deliveryCharges, false, false)
        widgetViews.push(this.getOrderDateWidget(userContext, orderProduct.option.startDate, true))
        widgetViews.push(orderSummaryWidget)
        return widgetViews
    }

    private async getGymfitFitnessPackConfirmationWidgets(userContext: UserContext, paymentDetail: PaymentDetail, product: Product, billingInfo: BillingInfo, order: Order, orderProduct: OrderProduct, apiType?: "ORDER_CHECKOUT" | "ORDER_V1") {
        const widgetViews: WidgetView[] = []
        const orderSummaryWidget = this.getOrderSummaryWidget(userContext.sessionInfo.userAgent, paymentDetail, product, billingInfo, order.deliveryCharges, false, false)
        if (!_.isEmpty(apiType) && apiType === "ORDER_V1") {
            this.addNeuPassEarnedCoinsWidget(order, widgetViews)
        }
        let centerInfoWidget: InfoWidget
        if (product && product.option && product.option.centerId) {
            const gymfitCenter = await this.catalogueService.getGymfitCenterById(product.option.centerId)

            if (gymfitCenter) {
                const centerName = (gymfitCenter.seller && gymfitCenter.seller.brand ? gymfitCenter.seller.brand.name + " - " : "") + gymfitCenter.name
                centerInfoWidget = this.getGymfitCenterWidget(centerName, "Only At")
            }
        }
        widgetViews.push(orderSummaryWidget)
        widgetViews.push(this.getOrderDateWidget(userContext, orderProduct.option.startDate, true))
        if (centerInfoWidget) {
            widgetViews.push(centerInfoWidget)
        }
        return widgetViews
    }

    private async getLuxFitnessProductConfirmationWidgets(userContext: UserContext, paymentDetail: PaymentDetail, product: Product, billingInfo: BillingInfo, order: Order, orderProduct: OrderProduct, apiType?: "ORDER_CHECKOUT" | "ORDER_V1") {
        const widgetViews: WidgetView[] = []
        const orderSummaryWidget = this.getOrderSummaryWidget(userContext.sessionInfo.userAgent, paymentDetail, product, billingInfo, order.deliveryCharges, false, false)
        if (!_.isEmpty(apiType) && apiType === "ORDER_V1") {
            this.addNeuPassEarnedCoinsWidget(order, widgetViews)
        }
        let centerInfoWidget: InfoWidget
        if (product && product.option && product.option.centerId) {
            const gymfitCenter = await this.catalogueService.getGymfitCenterById(product.option.centerId)

            if (gymfitCenter) {
                const centerName = (gymfitCenter.seller && gymfitCenter.seller.brand ? gymfitCenter.seller.brand.name + " - " : "") + gymfitCenter.name
                centerInfoWidget = this.getGymfitCenterWidget(centerName, "Only At")
            }
        }
        widgetViews.push(orderSummaryWidget)
        widgetViews.push(this.getOrderDateWidget(userContext, orderProduct.option.startDate, true))
        if (centerInfoWidget) {
            widgetViews.push(centerInfoWidget)
        }
        return widgetViews
    }

    public async getGymPtBookingConfirmationWidgets(userContext: UserContext, startTime: number, endTime: number, user: User, trainerId: number, centerId: number, product: Product, orderMeta: OrderMeta): Promise<OrderCheckoutDetail> {
        const widgetViews: WidgetView[] = []
        const duration: string = ""
        const userName: string = user.firstName + " " + user.lastName
        const center = await this.centerService.getCenterById(centerId)
        const centerAddress = center.name + ", " + center.fullAddress1
        const appointmentDetails: AppointmentDetailsWidget = this.getGymPtAppointmentDetails(userContext, startTime, duration, userName, center.name + ", " + center.locality, false)
        const centerInfoWidget: WidgetView = this.addCenterLocationWidget("Address" , centerAddress)
        widgetViews.push(appointmentDetails)
        const banners = await this.widgetBuilder.buildWidgets([GYM_PT_MUSCLE_BLAZE_BANNER_WIDGET_SECOND], this.serviceInterfaces, userContext, undefined, undefined)
        let muscleBlazeBanner = null
        if (!_.isEmpty(banners)) {
            muscleBlazeBanner = banners.widgets[0]
            if (!_.isEmpty(muscleBlazeBanner)) {
                widgetViews.push(muscleBlazeBanner)
            }
        }
        const agentDetails: Doctor[] = await this.ollivanderService.getDoctorDetailsByIdentityId(trainerId)
        const identityResponse: IdentityResponse = await this.identityService.getIdentityById("CUREFIT", trainerId)
        const agentAssets: DoctorAssetsResponse = await this.ollivanderService.getDoctorAssets(agentDetails[0].id)
        const ptServiceType = agentDetails[0]?.resourceServiceMapping.find(mapping => mapping.subServiceType?.groupType && mapping.subServiceType.groupType === "GYMFIT_PERSONAL_TRAINING")
        const trainerLevel = CartViewBuilder.getTrainersLevelText(ptServiceType?.subServiceTypeCode)
        const ptTrainerWidget = await this.getYourTrainerWidget(identityResponse.name, trainerLevel, center.name, agentAssets?.mediaList && agentAssets.mediaList.length > 0 && agentAssets.mediaList[0].mediaUrl ? agentAssets.mediaList[0].mediaUrl : undefined)
        widgetViews.push(ptTrainerWidget)
        widgetViews.push(centerInfoWidget)
        return {
            orderMeta: orderMeta,
            widgets: widgetViews
        }
    }

    private async getGymPtPpcBookingConfirmationWidgets(
        userContext: UserContext,
        paymentDetail: PaymentDetail,
        product: Product,
        billingInfo: BillingInfo,
        orderProduct: OrderProduct,
        order: BaseOrder,
        appConfigStore: IAppConfigStoreService,
        apiType?: "ORDER_CHECKOUT" | "ORDER_V1"
    ): Promise<WidgetView[]> {
        const widgetViews: WidgetView[] = []
        const duration: string = ""
        const user = await this.userCache.getUser(userContext.userProfile.userId)
        const userName: string = user.firstName + " " + user.lastName

        const orderProductOption = orderProduct.option
        const gymfitCenter = await this.gymfitService.getGymfitCenterById(orderProductOption.centerId.toString())
        const centerServiceCenter = await this.centerService.getCenterById(gymfitCenter.center.centerServiceId)
        const centerAddress = centerServiceCenter.name + ", " + centerServiceCenter.fullAddress1

        let isGymAssessmentCenter = false
        const gymAssessmentCenter: Number[] = JSON.parse(appConfigStore.getConfig("ASSESSMENT_ENABLED_CENTER_SERVICE_IDS", []))
        if (gymAssessmentCenter.includes(gymfitCenter.center.centerServiceId) || gymAssessmentCenter.includes(Number(gymfitCenter.center.centerServiceId))) {
            isGymAssessmentCenter = true
        }
        const appointmentDetails: AppointmentDetailsWidget = this.getGymPtAppointmentDetails(userContext, orderProductOption.gymPtOrderOptions.ppcSlotStartTime, duration, userName, centerServiceCenter.name + ", " + centerServiceCenter.locality, isGymAssessmentCenter)
        widgetViews.push(appointmentDetails)
        const banners = await this.widgetBuilder.buildWidgets([GYM_PT_MUSCLE_BLAZE_BANNER_WIDGET_SECOND], this.serviceInterfaces, userContext, undefined, undefined)
        let muscleBlazeBanner = null
        if (!_.isEmpty(banners)) {
            muscleBlazeBanner = banners.widgets[0]
            if (!_.isEmpty(muscleBlazeBanner)) {
                widgetViews.push(muscleBlazeBanner)
            }
        }
        const agentDetails: Doctor[] = await this.ollivanderService.getDoctorDetailsByIdentityId(Number(orderProductOption.gymPtOrderOptions.preferredTrainerId))
        const identityResponse: IdentityResponse = await this.identityService.getIdentityById("CUREFIT", Number(orderProductOption.gymPtOrderOptions.preferredTrainerId))
        const agentAssets: DoctorAssetsResponse = await this.ollivanderService.getDoctorAssets(agentDetails[0].id)
        const trainerLevel = CartViewBuilder.getTrainersLevelText(agentDetails[0]?.resourceServiceMapping[0]?.subServiceTypeCode)
        const ptTrainerWidget = await this.getYourTrainerWidget(identityResponse.name, trainerLevel, centerServiceCenter.name, agentAssets?.mediaList && agentAssets.mediaList.length > 0 && agentAssets.mediaList[0].mediaUrl ? agentAssets.mediaList[0].mediaUrl : undefined)
        widgetViews.push(ptTrainerWidget)

        const centerInfoWidget: WidgetView = this.addCenterLocationWidget("Address", centerAddress)
        widgetViews.push(centerInfoWidget)
        const userAgent = userContext.sessionInfo.userAgent
        const areDetailsForCheckout: boolean = !paymentDetail || paymentDetail.paymentChannel === null
        const paymentDetailsWidget = this.getPaymentDetailWidget(product, billingInfo, undefined, true, false, areDetailsForCheckout, userAgent)
        widgetViews.push(paymentDetailsWidget)
        if (!_.isEmpty(apiType) && apiType === "ORDER_V1") {
            this.addNeuPassEarnedCoinsWidget(order, widgetViews)
        }
        return widgetViews.filter(item => !!item)
    }

    private getYourTrainerWidget(trainerName: string, trainerLevel: string, centerName: string, image: string): WidgetView {
        return {
            widgetType: "YOUR_TRAINER_WIDGET",
            heading: trainerLevel,
            title: GymfitUtil.capitalizeFirstLetterOfAllWords(trainerName),
            description: "",
            image
        }
    }


    private getGymPtAppointmentDetails(userContext: UserContext, startTime: number, duration: string, userName: string, centerName: string, isGymAssessmentCenterAndPPCSession: boolean): AppointmentDetailsWidget {
        return {
            widgetType: "GYMPT_APPOINTMENT_DETAILS_WIDGET",
            appointmentInfo: {
                date: TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, startTime, "DD"),
                month: TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, startTime, "MMM"),
                day: TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, startTime, "dddd"),
                time: TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, startTime, "h:mm a"),
            },
            duration,
            description: isGymAssessmentCenterAndPPCSession ? "Gym Onboarding Session" : "Personal Training Session",
            title: "",
            patientName: "at " + centerName
        }
    }


    private addCenterLocationWidget(title: string, subtitle: string): WidgetView {
        return {
            widgetType: "TOPIC_DETAILS_LIST_WIDGET",
            title: title,
            items: [
                {
                    subTitle: subtitle,
                    icon: "/image/icons/cult/location_gray.png",
                }
            ],
        }
    }

    private async getPulsePackConfirmationWidgets(
        userContext: UserContext,
        paymentDetail: PaymentDetail,
        product: Product,
        billingInfo: BillingInfo,
        order: Order,
        orderProduct: OrderProduct,
        packInfo?: PulseProduct
    ): Promise<Array<WidgetView>> {
        /**
         * order can have one (for member buying a Pulse Rental bulk pack)
         * or two items (for PPC user buying Class with a Pulse Pack single class)
         */
        const widgetViews: Array<WidgetView> = []
        const pulseClassProduct = order.productSnapshots.find(product => product.productType === "FITNESS")
        const isPPCOrder = order.products.length > 1 && pulseClassProduct
        const pulseOrderSummaryWidget: CartBaseProductSummaryWidget = new CartBaseProductSummaryWidget(
            product.title,
            product.subTitle,
            null,
            "PULSE"
        )
        const pulsePaymentDetailWidget: PaymentDetailsWidget = this.getPaymentDetailWidget(
            product,
            billingInfo,
            order.deliveryCharges,
            false,
            false,
            false,
            userContext.sessionInfo.userAgent
        )
        if (isPPCOrder) {
            const cultClass = await this.cultFitService.getCultClass(pulseClassProduct.option.classId, order.userId, "CUREFIT_API", true, userContext.userProfile.subUserId, undefined, userContext.sessionInfo.deviceId)
            const pulseClass = await this.getCultSingleConfirmationWidgets(
                userContext,
                paymentDetail,
                pulseClassProduct,
                billingInfo,
                order.deliveryCharges,
                orderProduct,
                cultClass
            )
            widgetViews.push(...pulseClass)
        } else {
            // for PPC users, no need to share details of Pulse Pack
            // price is already factored in class price
            widgetViews.push(pulseOrderSummaryWidget)
        }
        widgetViews.push(pulsePaymentDetailWidget)
        return widgetViews
    }

    private async getCultPackConfirmationWidgets(userContext: UserContext, paymentDetail: PaymentDetail, product: Product, billingInfo: BillingInfo,
                                                 order: Order, orderProduct: OrderProduct, packInfo?: OfflineFitnessPack, apiType?: string): Promise<WidgetView[]> {
        const widgetViews: WidgetView[] = []
        const isWeb = AppUtil.isWeb(userContext)
        let showGst = false
        const isPartOfGstSegment = await AppUtil.doesUserBelongToGSTsplitExperiment(userContext, this.serviceInterfaces)
        if (isPartOfGstSegment) {
            showGst = true
        }
        const isPlusPackOrder: boolean = OrderUtil.isPlusMembershipOrder(order)
        const orderSummaryWidget = this.getOrderSummaryWidget(userContext.sessionInfo.userAgent, paymentDetail, product, billingInfo, order.deliveryCharges, false, false, undefined, undefined, undefined, order, showGst)
        // for some older orders, secondaryCustomers might not be there in DB.
        // if (!_.isEmpty(order.products[0].option.secondaryCustomers) && packInfo.isGroupPack) {
        //     const secondaryCustomerName = order.products[0].option.secondaryCustomers[0].name ? `${order.products[0].option.secondaryCustomers[0].name}\n` : ""
        //     const phoneNumberToDisplay = (secondaryCustomerName ? "(" : "") + order.products[0].option.secondaryCustomers[0].phone + (secondaryCustomerName ? ")" : "")
        //     orderSummaryWidget.subTitle = `with ${secondaryCustomerName}${phoneNumberToDisplay}`
        // }
        // if (!_.isEmpty(order.products[0].option.secondaryCustomers) && packInfo.isGiftPack) {
        //     orderSummaryWidget.subTitle = `to ${order.products[0].option.secondaryCustomers[0].name}\n(${order.products[0].option.secondaryCustomers[0].phone})`
        // }
        widgetViews.push(orderSummaryWidget)
        if (!_.isEmpty(apiType) && apiType === "ORDER_V1") {
            this.addNeuPassEarnedCoinsWidget(order, widgetViews)
        }
        // const isPreRegistrationPayRemainingFlow = order.clientMetadata && (order.clientMetadata as CultMembershipMetadata).preRegistrationOfferId
        // if (packInfo && !isPreRegistrationPayRemainingFlow && !_.isEmpty(packInfo.campaigns)) {
        //     widgetViews.push(this.getCultOfferWidget(packInfo.campaigns[0]))
        // }
        if (orderProduct.option.startDate) {
            /**
             * Provide subtitle to override default subtitle
             * This is to replace the text `On $date$`
             * with `Book unlimited classes from $date$ onwards`
             * in Cult and Mind Subscription bookings
             */
            const centerName = packInfo.clientMetadata?.centerName // TODO: Fetch from order's productSnapshot
            const cityId = order.cityId
            const city = await this.cityService.getCityById(cityId)
            let cityName = city.name
            if (city.parentCityId && city.cityId === REST_OF_MUMBAI_CITY_ID && await AppUtil.isCitySplitFeatureSupported(userContext)) {
                cityName = "Mumbai (excluding Navi Mumbai & Thane)"
            }

            const isSelectPack = CatalogueServiceUtilities.getAccessLevel(packInfo) === AccessLevel.CENTER
            console.log("centerName debug: " + isSelectPack + ": duration  " + packInfo.product.durationInDays)
            let subtitle
            if (order?.productSnapshots?.[0]?.productSubType === ProductSubType.LITE) {
                subtitle = `Unlimited access to all gyms in ${cityName} from ${TimeUtil.formatDateInTimeZone(
                    userContext.userProfile.timezone,
                    new Date(orderProduct.option.startDate),
                    "Do MMMM YYYY"
                )} onwards`
            } else {
                subtitle = `Unlimited access to all gyms & centers in ${cityName} from ${TimeUtil.formatDateInTimeZone(
                    userContext.userProfile.timezone,
                    new Date(orderProduct.option.startDate),
                    "Do MMMM YYYY"
                )} onwards`
            }

            if (CatalogueServiceUtilities.getAccessLevel(packInfo) === AccessLevel.CENTER && !isWeb) {
                const center = await this.centerService.getCenterById(parseInt(CatalogueServiceUtilities.getExternalAccessLevelId(packInfo)))
                if (order?.productSnapshots?.[0]?.productSubType === ProductSubType.LITE) {
                    subtitle = `Unlimited gym access to ${center.name} from ${TimeUtil.formatDateInTimeZone(
                        userContext.userProfile.timezone,
                        new Date(orderProduct.option.startDate),
                        "Do MMMM YYYY"
                    )} onwards`
                } else {
                    subtitle = `Unlimited access to ${center.name} from ${TimeUtil.formatDateInTimeZone(
                        userContext.userProfile.timezone,
                        new Date(orderProduct.option.startDate),
                        "Do MMMM YYYY"
                    )} onwards`
                }
            }

            if (isSelectPack && isWeb) {
                subtitle = `Unlimited Access To ${centerName} In ${cityName} from ${TimeUtil.formatDateInTimeZone(
                    userContext.userProfile.timezone,
                    new Date(orderProduct.option.startDate),
                    "Do MMMM YYYY"
                )} Onwards`
            }

            if (CatalogueServiceUtilities.isAllIndiaPack(packInfo)) { // ALL India Pack Changes
                subtitle = `Unlimited access to all centers across cities from ${TimeUtil.formatDateInTimeZone(
                    userContext.userProfile.timezone,
                    new Date(orderProduct.option.startDate),
                    "Do MMMM YYYY"
                )} onwards`
            }

            widgetViews.push(
                this.getOrderDateWidget(
                    userContext,
                    orderProduct.option.startDate,
                    true,
                    subtitle
                )
            )
        }

        if (product?.productType === "REGISTRATION") {
            const recurringPaymentId = order.payments[0].data.recurringPaymentId
            const productInfo = await this.paymentClient.getRecurringPaymentById(recurringPaymentId)
            const startDate = productInfo.productInfo.subscriptionStartDate
            const subtitle = `Book unlimited classes from ${TimeUtil.formatDateInTimeZone(
                userContext.userProfile.timezone,
                startDate,
                "Do MMMM YYYY"
            )} *if auto-debit payment is successful`
            widgetViews.push(
                this.getOrderDateWidget(
                    userContext,
                    startDate,
                    true,
                    subtitle
                )
            )
        }

        if (order.productSnapshots?.[0]?.productSubType === "RECURRING") {
            widgetViews.push(this.getRecurringNote(order))
        }

        if (orderProduct.option.centerId && ENABLE_CENTER_SELECTION_FOR_PACK) {
            let center
            if (product.productType === "FITNESS" || product.productType === "FITNESS_PREREGISTRATION") {
                center = await this.catalogueService.getCultCenter(orderProduct.option.centerId)
            } else {
                center = await this.catalogueService.getCultMindCenter(orderProduct.option.centerId)
            }
            // for order history, old center might not be present in catalogueService
            if (!_.isEmpty(center)) {
                let title = "Preferred Center"
                const isSelectPack = CatalogueServiceUtilities.getAccessLevel(packInfo) === AccessLevel.CENTER
                console.log("isSelectPack pin point " + isSelectPack)
                if (isSelectPack) {
                    title = "Access you'll get to"
                    widgetViews.push(this.getCultCenterWidget(center.name, title, undefined))
                } else {
                    widgetViews.push(this.getCultCenterWidget(center.name, title, undefined))
                }

                // hack for launching single studio centre, remove it wih proper fix asap
                if (center.id.toString() === "245" || center.id.toString() === "248") {
                    widgetViews.push(this.getSingleStudioCenterNoteWIdget())
                }
            }
        }
        const areDetailsForCheckout: boolean = !paymentDetail || null === paymentDetail.paymentChannel
        if (!areDetailsForCheckout && (product.productType === "FITNESS" || product.productType === "MIND") && packInfo) {
            if (!_.isEmpty(order.payments)) {
                const payment = order.payments.find((payment) => payment.status === "paid")
                if (!_.isNil(payment)) {
                    // Instant discount amount check is to see if user actually chose emi option on payment page
                    const isNoCostEmiOfferApplied = payment.instantDiscount && payment.instantDiscount.amount && payment.instantDiscount.isNoCostEmi
                    if (isNoCostEmiOfferApplied) {
                        widgetViews.push(await this.getNoCostEmiAvailedWidget(order))
                    }
                }
            }
        }
        if (isPlusPackOrder) {
            widgetViews.push(await this.getPlusPackBenefits(order?.productSnapshots?.[0]?.exhaustiveBenefitList))
        }

        if (AppUtil.isBoosterPackFlowActive()) {
            try {
                let boosterPack: OfflineFitnessPack = undefined
                const addOnPacksPostPurchase: OfflineFitnessPack[] = await this.offlineFitnessPackService.fetchAddOnPacks({
                    basePackId: product.productId, purchaseFlow: PurchaseFlow.POST,
                    namespace: Namespace.OFFLINE_FITNESS
                })
                if (addOnPacksPostPurchase && addOnPacksPostPurchase.length > 0) {
                    boosterPack = addOnPacksPostPurchase[0]
                }
                if (boosterPack)
                    widgetViews.push(await this.getBoosterPackBenefits(boosterPack))
            } catch (e) {
                this.logger.info("Booster Benefit Error" + e.toString())
            }
        }
        return widgetViews
    }

    private async getPlayPackConfirmationWidgets(userContext: UserContext, paymentDetail: PaymentDetail, product: Product, billingInfo: BillingInfo,
                                                 order: Order, orderProduct: OrderProduct, apiType?: string) {
        const widgetViews: WidgetView[] = []
        const packInfo = await this.catalogueService.getPlayPack(product.productId)
        const isPlayLitePack = packInfo.isRestrictedToCenter
        let isSlpPack = false
        const isPartOfGstSegment = await AppUtil.doesUserBelongToGSTsplitExperiment(userContext, this.serviceInterfaces)

        let center
        if (orderProduct.option.centerId) {
            center = await this.centerService.getCenterDetails(Number(orderProduct.option.centerId), true)
        }

        const fitnessPack = await this.catalogueService.getPlayPack(product.productId)
        const subLevelAccessListings: SubLevelAccessListing[] = fitnessPack.subLevelAccessListings
        if (subLevelAccessListings != null && subLevelAccessListings?.length > 0) {
            for (const subAccessLevel of subLevelAccessListings) {
                await this.logger.info("Play Sub Access", subAccessLevel)
                if (subAccessLevel?.subAccessLevel == SubAccessLevel.ACTIVITY && subAccessLevel?.subAccessLevelId != null) {
                    isSlpPack = true
                    break
                }
            }
        }

        const orderSummaryWidget = this.getOrderSummaryWidget(
            userContext.sessionInfo.userAgent,
            paymentDetail,
            product,
            billingInfo,
            order.deliveryCharges,
            false,
            false,
            undefined,
            undefined,
            undefined,
            order,
            isPartOfGstSegment)
        widgetViews.push(orderSummaryWidget)
        if (!_.isEmpty(apiType) && apiType === "ORDER_V1") {
            this.addNeuPassEarnedCoinsWidget(order, widgetViews)
        }
        const isPreRegistrationPayRemainingFlow = order.clientMetadata && (order.clientMetadata as CultMembershipMetadata).preRegistrationOfferId
        if (packInfo && !isPreRegistrationPayRemainingFlow && !_.isEmpty(packInfo.campaigns)) {
            widgetViews.push(this.getCultOfferWidget(packInfo.campaigns[0]))
        }
        if (orderProduct.option.startDate) {
            /**
             * Provide subtitle to override default subtitle
             * This is to replace the text `On $date$`
             * with `Book unlimited classes from $date$ onwards`
             * in Cult and Mind Subscription bookings
             */
            const city = await this.cityService.getCityById(order?.cityId ?? center?.city)
            let cityName = city != null ? city.name : ""
            if (city.parentCityId && city.cityId === REST_OF_MUMBAI_CITY_ID && await AppUtil.isCitySplitFeatureSupported(userContext)) {
                cityName = "Mumbai (excluding Navi Mumbai & Thane)"
            }

            const playSubTitle: string = `Unlimited access to all cult play centers in ${cityName} from ${TimeUtil.formatDateInTimeZone(
                userContext.userProfile.timezone,
                new Date(orderProduct.option.startDate),
                "Do MMMM YYYY"
            )} onwards`

            let playLiteSubTitle = `Unlimited access to all sports at given center in ${cityName} from ${TimeUtil.formatDateInTimeZone(
                userContext.userProfile.timezone,
                new Date(orderProduct.option.startDate),
                "Do MMMM YYYY"
            )} onwards`

            const playSLPSubTitle = `Unlimited access to given sports at given center in ${cityName} from ${TimeUtil.formatDateInTimeZone(
                userContext.userProfile.timezone,
                new Date(orderProduct.option.startDate),
                "Do MMMM YYYY"
            )} onwards`

            if (isPlayLitePack && !_.isEmpty(center)) {
                playLiteSubTitle = `Unlimited access to all sports at ${center.name} in ${cityName} from ${TimeUtil.formatDateInTimeZone(
                    userContext.userProfile.timezone,
                    new Date(orderProduct.option.startDate),
                    "Do MMMM YYYY"
                )} onwards`
            }
            widgetViews.push(
                this.getOrderDateWidget(
                    userContext,
                    orderProduct.option.startDate,
                    true,
                    isSlpPack ? playSLPSubTitle : isPlayLitePack ? playLiteSubTitle : playSubTitle
                )
            )
        }

        // for order history, old center might not be present in catalogueService
        if (!_.isEmpty(center)) {
            let workoutName = ""
            center.activities.forEach(activities => {
                if (activities.id === Number((order.clientMetadata as CultMembershipMetadata).workoutId)) {
                   workoutName = activities.name
                }
            })
            if (!_.isEmpty(workoutName)) {
                const title = "Preferred Sport & Center"
                const sportAndCenter = workoutName + ", " + center.name
                widgetViews.push(this.getCultCenterWidget(sportAndCenter, title, null))
            } else {
                const title = "Preferred Center"
                widgetViews.push(this.getCultCenterWidget(center.name, title, null))
            }
        }

        const areDetailsForCheckout: boolean = !paymentDetail || null === paymentDetail.paymentChannel
        if (!areDetailsForCheckout && product.productType == "PLAY" && packInfo) {
            if (!_.isEmpty(order.payments)) {
                const payment = order.payments.find((payment) => payment.status === "paid")
                if (!_.isNil(payment)) {
                    // Instant discount amount check is to see if user actually chose emi option on payment page
                    const isNoCostEmiOfferApplied = payment.instantDiscount && payment.instantDiscount.amount && payment.instantDiscount.isNoCostEmi
                    if (isNoCostEmiOfferApplied) {
                        widgetViews.push(await this.getNoCostEmiAvailedWidget(order))
                    }
                }
            }
        }
        return widgetViews
    }

    private async getLivePackConfirmationWidgets(userContext: UserContext, paymentDetail: PaymentDetail, product: Product, billingInfo: BillingInfo,
                                                 order: Order, orderProduct: OrderProduct, packInfo?: CFLiveProduct, apiType?: "ORDER_CHECKOUT" | "ORDER_V1"): Promise<WidgetView[]> {
        const widgetViews: WidgetView[] = []
        const orderSummaryWidget = this.getOrderSummaryWidget(userContext.sessionInfo.userAgent, paymentDetail, product, billingInfo, order.deliveryCharges, false, false)
        // for some older orders, secondaryCustomers might not be there in DB.
        if (!_.isEmpty(order.products[0].option.secondaryCustomers)) {
            const secondaryCustomerName = order.products[0].option.secondaryCustomers[0].name ? `${order.products[0].option.secondaryCustomers[0].name}\n` : ""
            const phoneNumberToDisplay = (secondaryCustomerName ? "(" : "") + order.products[0].option.secondaryCustomers[0].phone + (secondaryCustomerName ? ")" : "")
            orderSummaryWidget.subTitle = `with ${secondaryCustomerName}${phoneNumberToDisplay}`
        }
        if (!_.isEmpty(order.products[0].option.secondaryCustomers)) {
            orderSummaryWidget.subTitle = `to ${order.products[0].option.secondaryCustomers[0].name}\n(${order.products[0].option.secondaryCustomers[0].phone})`
        }
        widgetViews.push(orderSummaryWidget)
        if (!_.isEmpty(apiType) && apiType === "ORDER_V1") {
            this.addNeuPassEarnedCoinsWidget(order, widgetViews)
        }
        if (orderProduct.option.startDate) {
            /**
             * Provide subtitle to override default subtitle
             * This is to replace the text `On $date$`
             * with `Book unlimited classes from $date$ onwards`
             * in Cult and Mind Subscription bookings
             */
            widgetViews.push(
                this.getOrderDateWidget(
                    userContext,
                    orderProduct.option.startDate,
                    true,
                    `Book unlimited classes from ${TimeUtil.formatDateInTimeZone(
                        userContext.userProfile.timezone,
                        new Date(orderProduct.option.startDate),
                        "Do MMMM YYYY"
                    )} onwards`
                )
            )
        }
        const areDetailsForCheckout: boolean = !paymentDetail || null === paymentDetail.paymentChannel
        if (!areDetailsForCheckout && packInfo) {
            if (!_.isEmpty(order.payments)) {
                const payment = order.payments.find((payment) => payment.status === "paid")
                if (!_.isNil(payment)) {
                    // Instant discount amount check is to see if user actually chose emi option on payment page
                    const isNoCostEmiOfferApplied = payment.instantDiscount && payment.instantDiscount.amount && payment.instantDiscount.isNoCostEmi
                    if (isNoCostEmiOfferApplied) {
                        widgetViews.push(await this.getNoCostEmiAvailedWidget(order))
                    }
                }
            }
        }
        return widgetViews
    }

    private getSingleStudioCenterNoteWIdget(): WidgetView {
        const infoCard: InfoCard = {
            title: "Note:",
            subTitle: "This is a Dance and Yoga only centre. You can access other workout formats like S & C, HRX Workout, Boxing in nearby Cult centres"
        }
        return new DescriptionWidget([infoCard])
    }

    private async getNoCostEmiAvailedWidget(order: Order): Promise<InfoWidget> {
        const payment = order.payments.find((payment) => payment.status === "paid")
        if (!_.isNil(payment)) {
            const amountPayable = Math.round(payment.amount / 100)
            const orderValue = Math.round(amountPayable + (payment.instantDiscount.amount / 100))
            const period = payment.instantDiscount.emiMeta ? payment.instantDiscount.emiMeta.duration : 1
            const gst = orderValue * ((orderValue - amountPayable) / orderValue) * GST_RATE_INSTANT_DISCOUNT
            return {
                widgetType: "INFO_WIDGET",
                title: "Successfully availed No Cost EMI",
                subTitle: `You'll be charged Rs.${(orderValue / period).toFixed(2)} for ${period} months for your Rs.${orderValue} pack. One time GST charge of Rs.${gst.toFixed(2)} extra(usually charged with 1st/2nd installment)`,
                icon: "NO_COST_EMI",
                action: {
                    actionType: "NAVIGATION",
                    title: "Know More",
                    url: OrderUtil.getNoCostEmiKnowMoreUrl(order)
                }
            }
        }
    }

    private getRecurringNote(order: Order): OfferCalloutWidget {
        const isRegistrationOrder = order.productSnapshots[0].productType === "REGISTRATION"
        let offerDataItems: OfferData[]
        if (isRegistrationOrder) {
            offerDataItems = [
                {
                    title: undefined,
                    tnc: undefined,
                    description: "Membership activates only upon payment confirmation. No access until monthly payment is received."
                },
                {
                    title: undefined,
                    tnc: undefined,
                    description: "This registration fee is non-refundable"
                }
            ]
        } else {
            offerDataItems = [
                {
                    title: undefined,
                    tnc: undefined,
                    description: "Once start date is selected, it cannot be changed"
                },
                {
                    title: undefined,
                    tnc: undefined,
                    description: "Membership will renew monthly upon receipt of the membership fee."
                }
            ]
        }

        return new OfferCalloutWidget(
            "Note",
            offerDataItems,
            undefined,
            true,
            "",
            "BULLET"
        )

    }

    private getFoodPackConfirmationWidgets(userContext: UserContext, paymentDetail: PaymentDetail, foodPack: FoodPack, billingInfo: BillingInfo,
        deliveryCharge: DeliveryCharge, packagingCharge: ExtraCharge, orderProduct: OrderProduct, address: UserDeliveryAddress, allPossibleActionsForFoodBooking?: Action[]): WidgetView[] {
        const widgetViews: WidgetView[] = []
        const isWeb = ["MBROWSER", "DESKTOP"].includes(userContext.sessionInfo.userAgent)
        widgetViews.push(this.getOrderAddressWidget(address))
        widgetViews.push(this.getOrderDateWidget(userContext, orderProduct.option.startDate, true, "", allPossibleActionsForFoodBooking))
        widgetViews.push(this.getOrderSummaryWidget(userContext.sessionInfo.userAgent, paymentDetail, foodPack, billingInfo, deliveryCharge, false, false, packagingCharge))
        return widgetViews
    }

    private async getHealthCheckupWidgets(userContext: UserContext, userAgent: UserAgent, paymentDetail: PaymentDetail, product: Product, billingInfo: BillingInfo,
        deliveryCharge: DeliveryCharge, orderProduct: OrderProduct, address: UserDeliveryAddress): Promise<WidgetView[]> {
        const widgetViews: WidgetView[] = []
        const appointmentDateStart: string = TimeUtil.getMomentForDateString(orderProduct.option.appointmentDate, userContext.userProfile.timezone, "YYYY-MM-DD HHmm").format("YYYY-MM-DD HH:mm")
        widgetViews.push(this.getOrderDateTimeWidget(appointmentDateStart, userContext))
        widgetViews.push(this.getOrderAddressWidget(address))
        widgetViews.push(this.getOrderSummaryWidget(userAgent, paymentDetail, product, billingInfo, deliveryCharge, true, true))
        return widgetViews
    }

    private async getGroupClassConsultWidget(userContext: UserContext, paymentDetail: PaymentDetail, product: Product, billingInfo: BillingInfo,
        deliveryCharge: DeliveryCharge,
        totalAmountPayable: number,
        productInfo: GroupClassProduct,
        bookingDetail: BookingDetail,
        center: Center,
        apiType: "ORDER_CHECKOUT" | "ORDER_V1")
        : Promise<WidgetView[]> {

        const tz = userContext.userProfile.timezone
        const checkoutSummaryWidget: CareCheckoutSummaryWidget = {
            widgetType: "CARE_CHECKOUT_SUMMARY_WIDGET",
            title: productInfo.productName,
            subtitle: productInfo.productDescription,
            imageUrl: productInfo.imageUrl,
            doctorDetails: undefined,
            footer: {
                title: productInfo.productName + " at " + TimeUtil.formatEpochInTimeZone(tz, bookingDetail?.groupClassOrderResponse?.startTimeEpoch, "hh:mm a"),
                subtitle: productInfo.productDescription,
                timestamp: bookingDetail?.groupClassOrderResponse?.startTimeEpoch,
                footerShadow: false,
                center: {
                    centerName: center?.name,
                    actionString: "VIEW MAP",
                    actionUrl: center?.placeUrl
                },
              }
        }
        const paymentDetailsWidget = this.getPaymentDetailWidget(product, billingInfo, deliveryCharge, true, false, true)
        return [checkoutSummaryWidget, paymentDetailsWidget]

    }

    private async getTeleConsultationWidgets(userContext: UserContext, paymentDetail: PaymentDetail, product: Product, billingInfo: BillingInfo,
                                             deliveryCharge: DeliveryCharge,
                                             totalAmountPayable: number,
                                             orderProduct: OrderProduct, bookingDetail: BookingDetail,
                                             apiType: "ORDER_CHECKOUT" | "ORDER_V1")
        : Promise<WidgetView[]> {
        const widgetViews: WidgetView[] = []
        const user = await this.userCache.getUser(userContext.userProfile.userId)
        const offline: boolean = CareUtil.isOfflineConsultation(bookingDetail)
        const consultation: ConsultationOrderResponse = bookingDetail.consultationOrderResponse
        const isLiveWorkoutConsultation = CareUtil.isLivePTSessionConsultation(consultation) || CareUtil.isLiveSGTSessionConsultation(consultation)
        const isLivePtNewCheckoutSupported = isLiveWorkoutConsultation && AppUtil.isLivePTTrainerRecommendationSupported(userContext)
        const isLivePTSessionConsultation = CareUtil.isLivePTSessionConsultation(consultation)
        const isLiveSGTSessionConsultation = CareUtil.isLiveSGTSessionConsultation(consultation)
        const isTransformSessionConsultation = CareUtil.isTransformSessionConsultation(consultation)

        const userAgent = userContext.sessionInfo.userAgent
        const vertical = CareUtil.getVerticalForConsultation(consultation.consultationProduct.doctorType)
        const isSlotTimerWidgetSupported = userContext.sessionInfo.appVersion >= SLOT_TIMER_WIDGET_SUPPORTED && !AppUtil.isSugarFitOrUltraFitApp(userContext)
        if (vertical !== "CULTFIT" && vertical !== "TRANSFORM") {
            userAgent === "APP" && apiType !== "ORDER_V1" && isSlotTimerWidgetSupported && widgetViews.push({ ...CareUtil.getSlotTimerWidget(), hasDividerBelow: false })
        }
        const checkoutSummary = new CareCheckoutSummaryWidget(userContext, user, bookingDetail.booking.productCode, consultation, false, offline, bookingDetail.booking.id.toString(), billingInfo, bookingDetail, isLivePtNewCheckoutSupported)
        const areDetailsForCheckout: boolean = !paymentDetail || paymentDetail.paymentChannel === null
        const isLiveSGTCheckoutChangesSupported = AppUtil.isLiveSGTCheckoutChangesSupported(userContext)
        const hasNewLivePTCheckoutPageChanges = AppUtil.hasNewLivePTCheckoutPageChanges(userContext, userAgent, isLivePTSessionConsultation)
        const tz = userContext.userProfile.timezone
        if (isLiveSGTCheckoutChangesSupported && userAgent !== "DESKTOP" && (isLiveSGTSessionConsultation || isLivePTSessionConsultation)) {
            if (hasNewLivePTCheckoutPageChanges) {
                const workoutDetail = CareUtil.getWorkoutDetailWidget(tz, consultation)
                widgetViews.push({ ...workoutDetail, hasDividerBelow: false })
            } else {
                widgetViews.push({ ...checkoutSummary, hasDividerBelow: false })
            }
        }

        if (isLivePtNewCheckoutSupported && !CareUtil.isLiveSGTSessionConsultation(consultation) && !CareUtil.isSugarfitExperienceCenterProduct(consultation.consultationProduct) && !CareUtil.isSugarfitExperienceCenterDiagnosticProduct(consultation.consultationProduct)) {
            if (hasNewLivePTCheckoutPageChanges) {
                const doctorDetailWidget = CareUtil.getDoctorDetailWidgetV3(bookingDetail.consultationOrderResponse.doctor)
                doctorDetailWidget.hasDividerBelow = false
                widgetViews.push(doctorDetailWidget)
            } else {
                const doctorDetailWidget = CareUtil.getDoctorDetailWidget(bookingDetail.consultationOrderResponse.doctor)
                doctorDetailWidget.containerStyle = {
                    margin: 20
                }
                doctorDetailWidget.hasDividerBelow = false
                doctorDetailWidget.orientation = "LEFT"
                widgetViews.push(doctorDetailWidget)
            }
        }

        if (userAgent === "DESKTOP") {
            checkoutSummary.priceDetails = this.getPriceDetails(product.productType, billingInfo, undefined, undefined, true, false, areDetailsForCheckout)
            if (isLiveSGTSessionConsultation) {
                const wodInfoWidget = await this.wodViewBuilder.getLivePTWodWidget(userContext, bookingDetail)
                if (wodInfoWidget) {
                    wodInfoWidget.orientation = "LEFT"
                    widgetViews.push(wodInfoWidget)
                } else {
                    const checkoutSummaryWidget: WidgetView = {
                        widgetType: "ORDER_SUMMARY_WIDGET",
                        imageUrl: consultation.consultationProduct.heroImageUrl,
                        orientation: "LEFT",
                        productType: "LIVE_SGT"
                    }
                    widgetViews.push(checkoutSummaryWidget)
                }
            }
        }
        if (isLivePtNewCheckoutSupported) {
            checkoutSummary.topSpacing = 10
        }
        if (CareUtil.isLiveSGTSessionConsultation(consultation)) {
            checkoutSummary.topSpacing = 30
        }
        if (CareUtil.isSugarfitExperienceCenterProduct(consultation.consultationProduct) || CareUtil.isSugarfitExperienceCenterDiagnosticProduct(consultation.consultationProduct)) {
            const experienceCenterPaymentDetailsWidget = this.getExperienceCenterPaymentDetailWidget(product, billingInfo, deliveryCharge, true, false, areDetailsForCheckout, userAgent)
            widgetViews.push(experienceCenterPaymentDetailsWidget)
        }
        if (isLiveSGTCheckoutChangesSupported) {
            if (userAgent !== "DESKTOP" || !CareUtil.isLivePTSessionConsultation(consultation)) {
                if (!(isLiveSGTSessionConsultation || isLivePTSessionConsultation)) {
                    if (CareUtil.isSugarfitExperienceCenterProduct(consultation.consultationProduct)) {
                        const sfExperienceCenterWorkFlowWidget = new SfExperienceCenterWorkFlowWidget(userContext, consultation)
                        widgetViews.push(sfExperienceCenterWorkFlowWidget)
                    } else if (CareUtil.isSugarfitExperienceCenterDiagnosticProduct(consultation.consultationProduct)) {
                        const sfExperienceCenterWorkFlowWidget = new SfExperienceCenteFullBodyTestWidget()
                        widgetViews.push(sfExperienceCenterWorkFlowWidget)
                    } else {
                        widgetViews.push(checkoutSummary)
                    }
                }
            }
        } else {
            if (userAgent !== "DESKTOP" || !isLiveWorkoutConsultation) {
                if (CareUtil.isSugarfitExperienceCenterProduct(consultation.consultationProduct)) {
                    const sfExperienceCenterWorkFlowWidget = new SfExperienceCenterWorkFlowWidget(userContext, consultation)
                    widgetViews.push(sfExperienceCenterWorkFlowWidget)
                } else if (CareUtil.isSugarfitExperienceCenterDiagnosticProduct(consultation.consultationProduct)) {
                    const sfExperienceCenterWorkFlowWidget = new SfExperienceCenteFullBodyTestWidget()
                    widgetViews.push(sfExperienceCenterWorkFlowWidget)
                } else {
                    if (isLiveSGTSessionConsultation || isLivePTSessionConsultation) {
                        widgetViews.push({ ...checkoutSummary, hasDividerBelow: false })
                    } else {
                        widgetViews.push(checkoutSummary)
                    }
                }
            }
        }
        if (userAgent === "DESKTOP") {
            if (!isLivePTSessionConsultation && !isLiveSGTSessionConsultation) {
                widgetViews.push(this.getPatientInfoWidget(consultation.patient))
            }
            if (offline) {
                let centerNameTitle = "Carefit address"
                if (CareUtil.isTherapyConsultation(consultation)) {
                    centerNameTitle = "Centre address"
                }
                widgetViews.push(this.getCareCenterWidget(consultation.center, centerNameTitle))
            }
            widgetViews.push(this.getCareConsultationTimeWidget(userContext, consultation, offline))
        }
        if (isLiveWorkoutConsultation && userAgent === "APP") {
            if (!_.isEmpty(bookingDetail.bundleConsultationRejectionContext)) {
                widgetViews.push(CareUtil.getLivePTMembershipContextWidget(bookingDetail, userContext))
            }
        }
        const isNoteWidgetSupported = AppUtil.isNewNoteWidgetSuported(userContext)
        if ((isLivePTSessionConsultation || isLiveSGTSessionConsultation) && !isNoteWidgetSupported) {
            widgetViews.push({
                ...CareUtil.getLivePTCancellationCutoffWidget(bookingDetail, userContext.userProfile.timezone),
                orientation: userAgent === "DESKTOP" ? "RIGHT" : undefined,
                productType: "LIVE_PERSONAL_TRAINING"
            })
        }
        if (userAgent !== "DESKTOP" && isLiveSGTSessionConsultation && !_.isEmpty(consultation.metadata) && consultation.metadata.wodId) {
            const wod: SimpleWod = await this.herculesService.getSimpleWodById(consultation.metadata.wodId)
            const subTitle = CultUtil.getWodMovements(wod)
            if (!_.isEmpty(subTitle)) {
                if (AppUtil.isLiveSGTReadMoreSupported(userContext)) {
                    const descriptions = [{
                        subTitle,
                        title: "WORKOUT",
                        moreIndex: 80,
                        readMoreProps: {
                            viewMoreText: "more",
                            viewLessText: "read less",
                            readMoreStyle: {
                                fontFamily: "BrandonText-Bold",
                                fontSize: 12,
                                color: "#000000",
                                textDecorationLine: "underline",
                            },
                            text: `${subTitle} `,
                            moreIndex: 80,
                            textStyle: { fontFamily: "BrandonText-Regular", fontSize: 12, color: "#000000", lineHeight: 18, marginBottom: 0, marginTop: 0 }
                        },
                        titleStyle: { fontSize: 12, fontFamily: "BrandonText-Bold", lineHeight: 18, color: "#888e9e", marginBottom: 3 },
                    }]
                    const widget = new DescriptionWidget(descriptions)
                    widgetViews.push({
                        ...widget, hasDividerBelow: isLiveSGTCheckoutChangesSupported ? true : false
                    })
                } else {
                    const headerWidget: HeaderWidget = {
                        widgetType: "HEADER_WIDGET",
                        widgetTitle: {
                            title: "WORKOUT",
                            subTitle,
                            subTitleNumberOfLine: 2
                        },
                        style: { marginTop: 25, marginBottom: 25, marginRight: 20 },
                        titleStyle: { fontSize: 12, fontFamily: "BrandonText-Bold", lineHeight: 18, color: "#888e9e", marginBottom: 3 },
                        subTitleStyle: { fontFamily: "BrandonText-Regular", fontSize: 12, color: "#000000", lineHeight: 18, marginBottom: 0, marginTop: 0 },
                        hasDividerBelow: false,
                    }
                    widgetViews.push({ ...headerWidget, orientation: "RIGHT" })
                }
            }
        }
        const isParentBookingIdPresent = !_.isNil(orderProduct.option.parentBookingId) && orderProduct.option.parentBookingId >= 0
        const isPhleboConsultation = CareUtil.isSugarfitPhleboConsult(orderProduct.productId)
        const isChronicCare = CareUtil.getIfChronicCareFromUserContext(userContext)
        const shouldNotShowPaymentDetails = isPhleboConsultation || ((isTransformSessionConsultation || isLiveWorkoutConsultation || isChronicCare) && isParentBookingIdPresent && totalAmountPayable === 0) || CareUtil.isSugarfitExperienceCenterDiagnosticProduct(consultation.consultationProduct)
        if (!shouldNotShowPaymentDetails && !CareUtil.isSugarfitExperienceCenterProduct(consultation.consultationProduct)) {
            const paymentDetailsWidget = this.getPaymentDetailWidget(product, billingInfo, deliveryCharge, true, false, areDetailsForCheckout, userAgent)
            if (isNoteWidgetSupported) {
                paymentDetailsWidget.hasDividerBelow = false
            }
            widgetViews.push(paymentDetailsWidget)
        }
        if (CareUtil.isPTSessionConsultation(consultation)) {
            const ptNoteWidget = await this.getCultPTSessionNoteWidget(bookingDetail)
            if (!_.isEmpty(ptNoteWidget)) {
                widgetViews.push(ptNoteWidget)
            }
        } else if (isLiveWorkoutConsultation && !isNoteWidgetSupported) {
            const ptNoteWidget = await this.getLivePTSessionNoteWidget()
            if (!_.isEmpty(ptNoteWidget)) {
                widgetViews.push(ptNoteWidget)
            }
        }
        if (isLiveWorkoutConsultation && isNoteWidgetSupported) {
            const noteListWidget = CareUtil.getLivePTSessionNoteListWidget(bookingDetail, userContext, isLiveSGTCheckoutChangesSupported ? "#FFFFFF" : "#f2f4f8", isLiveSGTSessionConsultation ? 5 : 20, isLiveSGTSessionConsultation)
            if (userAgent === "DESKTOP") {
                noteListWidget.orientation = "RIGHT"
            }
            widgetViews.push(noteListWidget)
        }
        return widgetViews
    }

    private async getTeleConsultationWidgetsV2(
        userContext: UserContext,
        paymentDetail: PaymentDetail,
        product: Product,
        billingInfo: BillingInfo,
        deliveryCharge: DeliveryCharge,
        orderProduct: OrderProduct,
        bookingDetail: BookingDetail,
        packProduct: ConsultationSellableProduct,
        apiType: "ORDER_CHECKOUT" | "ORDER_V1"
    ): Promise<WidgetView[]> {
        const widgetViews: WidgetView[] = []
        const consultation: ConsultationOrderResponse = bookingDetail.consultationOrderResponse
        const userAgent = userContext.sessionInfo.userAgent
        const orderProductOption = orderProduct.option
        const { packHowItHelp } = CareUtil.getPackContent(packProduct)
        const isOrderDetailsPage = apiType === "ORDER_V1"
        const areDetailsForCheckout: boolean = !paymentDetail || paymentDetail.paymentChannel === null
        if (!isOrderDetailsPage) {
            widgetViews.push({ ...CareUtil.getSlotTimerWidget(), hasDividerBelow: false })
        }
        widgetViews.push(this.getCareCheckoutInfoWidget(userContext, bookingDetail, packProduct, isOrderDetailsPage))
        widgetViews.push(CareUtil.getWhatYouGetInstructionWidget(packHowItHelp, userContext))
        if ((_.isNil(orderProductOption.parentBookingId) || orderProductOption.parentBookingId === -1)) {
            const paymentDetailsWidget = this.getPaymentDetailWidget(product, billingInfo, deliveryCharge, true, false, areDetailsForCheckout, userAgent)
            widgetViews.push(paymentDetailsWidget)
        }
        return widgetViews.filter(item => !!item)
    }

    private async getFlutterTeleConsultationWidgets(
        userContext: UserContext,
        order: Order,
        paymentDetail: PaymentDetail,
        product: Product,
        billingInfo: BillingInfo,
        deliveryCharge: DeliveryCharge,
        orderProduct: OrderProduct,
        bookingDetail: BookingDetail,
        packProduct: ConsultationSellableProduct,
        apiType: "ORDER_CHECKOUT" | "ORDER_V1"
    ): Promise<WidgetView[]> {
        const widgetViews: WidgetView[] = []
        const userAgent = userContext.sessionInfo.userAgent
        const orderProductOption = orderProduct.option
        const { packHowItHelpV2, howItWorksItemV2 } = CareUtil.getPackContent(packProduct)
        const isOrderDetailsPage = apiType === "ORDER_V1"
        const areDetailsForCheckout: boolean = !paymentDetail || paymentDetail.paymentChannel === null
        widgetViews.push(this.getFlutterCareAppointmentDetailsWidget(userContext, bookingDetail, packProduct))
        if (bookingDetail.consultationOrderResponse?.doctorType === "SUPPORT_GROUP" || packProduct?.consultationProduct?.doctorType === "SUPPORT_GROUP") {
            widgetViews.push(this.getFlutterCareCheckoutSummaryWidget(userContext, bookingDetail))
        } else {
            widgetViews.push(this.getFlutterCareCheckoutInfoWidget(userContext, bookingDetail))
        }
        if ((_.isNil(orderProductOption.parentBookingId) || orderProductOption.parentBookingId === -1)) {
            const paymentDetailsWidget = this.getPaymentDetailWidget(product, billingInfo, deliveryCharge, true, false, areDetailsForCheckout, userAgent)
            widgetViews.push(paymentDetailsWidget)
        }
        if (!isOrderDetailsPage) {
            const orientation: Orientation = "TOP"
            widgetViews.push(await this.getOfferInfoWidget(order))
            widgetViews.push(CareUtil.getWhatYouGetInstructionWidget(packHowItHelpV2, userContext, orientation))
            widgetViews.push(CareUtil.getHowItWorksWidget(howItWorksItemV2, userContext.sessionInfo.userAgent))
        }
        return widgetViews.filter(item => !!item)
    }


    private getCareCheckoutInfoWidget(
        userContext: UserContext,
        bookingDetail: BookingDetail,
        packProduct: ConsultationSellableProduct,
        isOrderDetails: boolean
    ): CareCheckoutInfoItemWidget {
        const tz = userContext.userProfile.timezone
        const offline: boolean = CareUtil.isOfflineConsultation(bookingDetail)
        const isAudioConsult = CareUtil.isAudioBookingConsultation(bookingDetail)
        const consultationInfo = bookingDetail.consultationOrderResponse
        const { packHowItHelp } = CareUtil.getPackContent(packProduct)
        const isNotKayaTrialConsult = !CareUtil.isKayaTrailConsult(bookingDetail.booking.productCode)
        const rightTitleText = !isNotKayaTrialConsult ? undefined : (isAudioConsult ? `AUDIO CONSULT` : (offline ? `IN-CENTRE CONSULT` : `ONLINE CONSULT`))
        const items: CheckoutInfoItem[] = [
            {
                title: `${TimeUtil.formatEpochInTimeZone(tz, consultationInfo.startTime, "ddd, D MMM • h:mm a")}`,
                subTitle: CareUtil.getCancellationInfo(userContext, bookingDetail, true)?.text,
                imageType: "SMALL",
                imageUrl: "/image/icons/checkout/care/slot.png",
                titleStyleType: "REGULAR",
                subTitleStyleType: "REGULAR_GRAY"
            },
            {
                title: `For ${consultationInfo.patient.name}`,
                subTitle: `${consultationInfo.patient.formattedAge.numOfYears ? `${consultationInfo.patient.formattedAge.numOfYears} yrs,` : ""} ${consultationInfo.patient.gender}`,
                imageType: "SMALL",
                imageUrl: "/image/icons/checkout/care/profile.png",
                showDivider: true,
                titleStyleType: "REGULAR",
                subTitleStyleType: "REGULAR_GRAY"
            }
        ]
        if (isNotKayaTrialConsult) {
            items.push({
                title: `${consultationInfo.doctor.name}`,
                subTitle: CareUtil.isTherapyOnlyDoctorType(consultationInfo.doctorType) ? undefined : `${consultationInfo.doctor.experience ? `${consultationInfo.doctor.experience} years of experience` : ""}`,
                subText: consultationInfo.doctor.qualification || consultationInfo.doctor.detailedQualification,
                imageType: "LARGE",
                imageUrl: consultationInfo.doctor.displayImage,
                showArrow: true,
                action: {
                    actionType: "SHOW_DOCTOR_DETAILS_MODAL",
                    meta: {
                        ...bookingDetail.consultationOrderResponse.doctor,
                        experience: CareUtil.isTherapyOnlyDoctorType(consultationInfo.doctorType) ? undefined : consultationInfo?.doctor?.experience
                    }
                },
                titleStyleType: "MEDIUM",
                subTitleStyleType: "REGULAR_GRAY",
                subTextStyleType: "REGULAR_GRAY"
            })
        }
        return {
            widgetType: "CARE_CHECKOUT_INFO_ITEM_WIDGET",
            leftTitleText: CareUtil.getCheckoutConsultTitle(consultationInfo, offline, consultationInfo?.center?.isExternal, userContext.userProfile.timezone, true),
            rightTitleText,
            items,
            hasDividerBelow: !packHowItHelp ? true : false
        }
    }

    private getFlutterCareCheckoutInfoWidget(
        userContext: UserContext,
        bookingDetail: BookingDetail
    ): CareCheckoutInfoItemWidget {
        const consultationInfo = bookingDetail.consultationOrderResponse
        const isNotKayaTrialConsult = !CareUtil.isKayaTrailConsult(bookingDetail.booking.productCode)
         const params = {
            isDoctorSearch: false,
            slugValue: consultationInfo.doctor.seoSlugValue,
            doctorId: consultationInfo.doctor.id,
            productId: consultationInfo.consultationProduct.productCode,
            patientId: consultationInfo.patient.id,
            centerId: consultationInfo.center.id,
            doctorType: consultationInfo?.doctorType,
            appsource: userContext.sessionInfo.appSource
        }
        const queryString = ActionUtil.serializeAsQueryParams(params)
        const items: CheckoutInfoItem[] = []
        if (isNotKayaTrialConsult) {
            items.push({
                title: `${consultationInfo.doctor.name}`,
                subTitle: CareUtil.isTherapyOnlyDoctorType(consultationInfo.doctorType) ? undefined : `${consultationInfo.doctor.experience ? `${consultationInfo.doctor.experience} years of experience` : ""}`,
                subText: consultationInfo.doctor.qualification || consultationInfo.doctor.detailedQualification,
                imageType: "LARGE",
                imageUrl: consultationInfo.doctor.displayImage,
                showArrow: true,
                action: {
                    url: `curefit://fl_doctorbooking${queryString}`,
                    actionType: "SHOW_DOCTOR_DETAILS_MODAL",
                    meta: {
                        ...bookingDetail.consultationOrderResponse.doctor,
                        experience: CareUtil.isTherapyOnlyDoctorType(consultationInfo.doctorType) ? undefined : consultationInfo?.doctor?.experience
                    }
                },
                titleStyleType: "MEDIUM",
                subTitleStyleType: "REGULAR_GRAY",
                subTextStyleType: "REGULAR_GRAY"
            })
            return {
                widgetType: "CARE_CHECKOUT_INFO_ITEM_WIDGET",
                items
            }
        }
        return null
    }

    private getFlutterCareCheckoutSummaryWidget(userContext: UserContext, bookingDetail: BookingDetail): CareCheckoutSummaryWidget {
        const consultation = bookingDetail.consultationOrderResponse
        const doctor = consultation.doctor
        const product = consultation.consultationProduct

        const tz = userContext.userProfile.timezone
        const startTime =  TimeUtil.formatEpochInTimeZone(tz, consultation.startTime, "YYYY-MM-DD HH:mm:ss")
        const endTime =  TimeUtil.formatEpochInTimeZone(tz, consultation.endTime, "YYYY-MM-DD HH:mm:ss")

        return {
            widgetType: "CARE_CHECKOUT_SUMMARY_WIDGET",
            title: doctor.name,
            subtitle: "Moderator",
            imageUrl: doctor.displayImage,
            consultationMode: product.consultationMode,
            doctorDetails: {
                ...doctor
            },
            footer: {
                title: product.name + " at " + TimeUtil.formatEpochInTimeZone(tz, consultation.startTime, "hh:mm a"),
                subtitle: TimeUtil.diffInMinutes(tz, startTime, endTime) + " mins session",
                timestamp: consultation.startTime,
                footerShadow: false,
            },
        }
    }

    private getFlutterCareAppointmentDetailsWidget(
        userContext: UserContext,
        bookingDetail: BookingDetail,
        packProduct: ConsultationSellableProduct
    ): AppointmentDetailsWidget {
        const consultationInfo = bookingDetail.consultationOrderResponse
        const tz = userContext.userProfile.timezone
        const duration = TimeUtil.diffInMinutes(tz, TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(consultationInfo.startTime), "YYYY-MM-DD HH:mm:ss"), TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(consultationInfo.endTime), "YYYY-MM-DD HH:mm:ss")) + " mins"
        return new AppointmentDetailsWidget(
            {
                date: TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, consultationInfo.startTime, "DD"),
                month: TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, consultationInfo.startTime, "MMM"),
                day: TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, consultationInfo.startTime, "dddd"),
                time: TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, consultationInfo.startTime, "h:mm a"),

            },
            duration,
            packProduct.productName,
            CareUtil.getCancellationInfo(userContext, bookingDetail, true)?.text,
            `${consultationInfo.patient.name}`,
        )
    }

    private async getCultPTSessionNoteWidget(bookingDetail: BookingDetail): Promise<WidgetView> {
        const ptSessionAlreadyDone = await this.healthfaceService.validateAssociatedSegment(bookingDetail.booking.userId, bookingDetail.booking.patientId, "SEGMENT_CONS_CULT_PT_COMPLETED", "CULTFIT")
        if (!ptSessionAlreadyDone) {
            const infoCard: InfoCard = {
                title: "Note:",
                subTitle: "The trainer will conduct a comprehensive fitness assessment in this session to gauge your strengths & weaknesses."
            }
            return new DescriptionWidget([infoCard])
        }
    }

    private async getLivePTSessionNoteWidget(): Promise<WidgetView> {
        const infoCard: InfoCard = {
            title: "Note:",
            subTitle: "Zoom video link to join the class will be enabled during your time slot."
        }
        return new DescriptionWidget([infoCard])
    }

    private getPatientInfoWidget(patient: Patient): InfoWidget {
        const age = !_.isEmpty(patient) && !_.isEmpty(patient.formattedAge) ? patient.formattedAge.numOfYears : undefined
        return {
            widgetType: "INFO_WIDGET",
            icon: "PERSON",
            title: "For whom",
            subTitle: age && age !== -1 ? `${patient.name} | ${age} yrs` : `${patient.name}`,
            seperatorLine: true,
            orientation: "RIGHT"
        }
    }

    private getCareCenterWidget(center: Center, title: string): InfoWidget {
        return {
            widgetType: "INFO_WIDGET",
            icon: "LOCATION",
            title,
            subTitle: `${center.name} | ${center.address}`,
            seperatorLine: true,
            orientation: "RIGHT"
        }
    }

    private getCareConsultationTimeWidget(userContext: UserContext, consultation: ConsultationOrderResponse, offline: boolean): InfoWidget {
        const onlineTitle = consultation?.channel === "AUDIO" ? "Audio Call" : "Video call"
        const title = CareUtil.isLivePTSessionConsultation(consultation)
            ? `Online Personal Training | ${consultation.consultationProduct.description}` : CareUtil.isLiveSGTSessionConsultation(consultation) ? `Online Group Class | ${consultation.consultationProduct.description}` : CareUtil.isNotDoctorConsulation(consultation)
                ? "Session at"
                : _.get(consultation, "consultationProduct.name", offline ? "Consultation" : onlineTitle)
        return {
            widgetType: "INFO_WIDGET",
            icon: "DATE_TIME",
            title: title ? `${title}` : "Consultation at",
            subTitle: `${TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, consultation.startTime, "Do MMM | hh:mm a")}`,
            seperatorLine: true,
            orientation: "RIGHT"
        }
    }

    private async getDiagnosticsTestBookingWidgets(userContext: UserContext, userAgent: UserAgent, paymentDetail: PaymentDetail, product: Product, billingInfo: BillingInfo,
        deliveryCharge: DeliveryCharge,
        orderProduct: OrderProduct, bookingDetail: BookingDetail, baseOrder: BaseOrder, productCodes: string[], productInfos: HealthfaceProductInfo[], patientDetails: Patient, apiType?: "ORDER_CHECKOUT" | "ORDER_V1")
        : Promise<WidgetView[]> {
        const widgetViews: WidgetView[] = []
        const testResponse: DiagnosticsTestOrderResponse = bookingDetail.diagnosticsTestOrderResponse[0]
        const areDetailsForCheckout: boolean = !paymentDetail || paymentDetail.paymentChannel === null
        let items: ActionCard[] = []
        const tz = userContext.userProfile.timezone
        const title: string = "Tests at Centre"
        const isPaymentRequired = billingInfo.total > 0
        const isWeb = AppUtil.isWeb(userContext)
        const isDesktop = AppUtil.isDesktop(userContext)
        const isCareNewCheckoutSupported = CareUtil.isCareDiagnosticCartSupported(userContext)
        const homeSampleChargesApplicable = baseOrder?.collectionCharges?.total > 0
        if (isWeb && isPaymentRequired) {
            const orderSummaryWidget = this.getOrderSummaryWidget(userAgent, paymentDetail, product, billingInfo, baseOrder.deliveryCharges, false, false, undefined, baseOrder.products.length)
            if (isDesktop) {
                orderSummaryWidget.orientation = "LEFT"
            }
            widgetViews.push(orderSummaryWidget)
        }
        if (isCareNewCheckoutSupported) {
            const title = "Order",
                totalProducts = productCodes.length,
                orientation = isDesktop ? "LEFT" : undefined
            let testDetails, reportReadyEta, patientInfo, slotDetails, homeAddressDetails, maxReportEta = 0, totalParameters = 0
            if (!_.isEmpty(testResponse.atHomeDiagnosticOrder)) {
                slotDetails = CareUtil.getHomeCollectionTimeText(testResponse.atHomeDiagnosticOrder.startTime, testResponse.atHomeDiagnosticOrder.endTime, userContext)
                homeAddressDetails = !_.isEmpty(testResponse.atHomeDiagnosticOrder.addressMetadata) ? testResponse.atHomeDiagnosticOrder.addressMetadata.address : undefined
            }
            productCodes.map(productCode => {
                const productInfo = productInfos.find(productInfo => productInfo.baseSellableProduct.productCode === productCode)
                const reportingTat = productInfo?.baseSellableProduct?.diagnosticProductResponse?.reportingTat
                maxReportEta = maxReportEta <= reportingTat ? reportingTat : maxReportEta
                const countTests = CareUtil.countParameters(productInfo?.baseSellableProduct?.diagnosticProductResponse)
                totalParameters += countTests
            })
            reportReadyEta = `Reports in ${maxReportEta} Hrs`
            testDetails = `${totalProducts}${totalProducts > 1 ? ` Items` : ` Item`} | ${totalParameters}${totalParameters > 1 ? ` Tests` : ` Test`}`
            patientInfo = patientDetails ? `For ${patientDetails.name} | ${patientDetails.gender} | ${patientDetails.age} Yrs` : undefined
            const summaryDetails = { testDetails, reportReadyEta, patientInfo, slotDetails, homeAddressDetails, title }
            const cartDiagnosticsCheckoutSummaryWidget = new CartDiagnosticsCheckoutSummaryWidget(summaryDetails, orientation)
            widgetViews.push(cartDiagnosticsCheckoutSummaryWidget)
        } else {
            if (!_.isEmpty(testResponse.atHomeDiagnosticOrder)) {
                items.push({
                    image: "LOCATION_PIN",
                    title: "Sample collection at home",
                    date: CareUtil.getHomeCollectionTimeText(testResponse.atHomeDiagnosticOrder.startTime, testResponse.atHomeDiagnosticOrder.endTime, userContext),
                    subTitle: !_.isEmpty(testResponse.atHomeDiagnosticOrder.addressMetadata) ? testResponse.atHomeDiagnosticOrder.addressMetadata.address : undefined
                })
            }

            if (!_.isEmpty(testResponse.inCentreDiagnosticOrder)) { // for canceled order this would be null
                items.push({
                    image: "LOCATION_PIN",
                    title: title,
                    date: `${TimeUtil.formatEpochInTimeZone(tz, testResponse.inCentreDiagnosticOrder.slot.workingStartTime, "ddd, D MMM, h:mm A")}`,
                    subTitle: testResponse.inCentreDiagnosticOrder.slot.diagnosticCentre.name,
                    seeMoreText: "VIEW MAP",
                    action: `curefit://externalDeepLink?placeUrl=${testResponse.inCentreDiagnosticOrder.slot.diagnosticCentre.placeUrl}`
                })
            }

            if (!_.isEmpty(items)) {
                if (items.length === 2 && testResponse.atHomeDiagnosticOrder.startTime > testResponse.inCentreDiagnosticOrder.slot.workingStartTime) {
                    items = items.reverse()
                }
                const testBookingSummaryWidget = new TestBookingSummaryWidget(items)
                if (isWeb && isDesktop) {
                    testBookingSummaryWidget.orientation = "RIGHT"
                }
                widgetViews.push(testBookingSummaryWidget)
            }
        }
        const source = AppUtil.callSourceFromContext(userContext)
        if (billingInfo && billingInfo.amountPayable > 0) {
            // Fetch Care Cart Offers for Diagnostics
            if (userContext.sessionInfo.userAgent === "APP") {
                const { offerIds } = await this.offerServiceV3.getApplicableCartOffersForCare({
                    userInfo: { userId: userContext.userProfile.userId, deviceId: userContext.sessionInfo.deviceId },
                    requiredOfferTypes: ["DIAGNOSTICS"],
                    source,
                    cityId: userContext.userProfile.cityId
                })
                const offers = _.values((await this.offerServiceV3.getOffersByIds(offerIds)).data)
                const isCollapsibleOfferWidgetSupported = userContext.sessionInfo.appVersion >= CARE_COLLAPSIBLE_OFFER_SUPPORTED
                const offerWidget = isCollapsibleOfferWidgetSupported ? CareUtil.getCollapsibleOfferWidget(offers, userContext, homeSampleChargesApplicable, undefined, undefined) : this.getOfferCalloutWidget(offers, userContext)
                widgetViews.push(offerWidget)
            }
            widgetViews.push(this.getPaymentDetailWidget(product, billingInfo, deliveryCharge, true, false, areDetailsForCheckout, userAgent, baseOrder))
        } else if (bookingDetail.booking.status !== "CANCELLED" && !_.isEmpty(testResponse)) {
            widgetViews.push(new InstructionsWidget(CareUtil.getTestInstructions(testResponse.inCentreInstructions, testResponse.atHomeInstructions)))
        }
        if (!_.isEmpty(apiType) && !_.isEmpty(baseOrder) && apiType === "ORDER_V1") {
            this.addNeuPassEarnedCoinsWidget(baseOrder, widgetViews)
        }
        return widgetViews
    }

    private getOfferCalloutWidget(offers: OfferV2[], userContext: UserContext): ProductOfferWidgetV2 | null {
        const isWeb = userContext.sessionInfo.userAgent !== "APP"
        const isDesktop = userContext.sessionInfo.userAgent === "DESKTOP"
        const uniqueOfferIds: string[] = []
        const webContainerStyle = { boxShadow: "none", border: "none", margin: 0 }
        const contentContainerStyle = { marginBottom: 10, marginTop: 10, backgroundColor: "#dfefe6", ...(isWeb ? webContainerStyle : {}) }
        if (!_.isEmpty(offers)) {
            const actionType: ActionType = "SHOW_OFFERS_TNC_MODAL"
            const offerItems: ProductOffer[] = []
            offers.map(offer => {
                if (!_.isEmpty(offer)) {
                    if (uniqueOfferIds.indexOf(offer.offerId) === -1 && !offer.displayContexts?.includes("NONE")) {
                        uniqueOfferIds.push(offer.offerId)
                        offerItems.push({
                            title: offer.description.toString(),
                            iconType: "/image/icons/cult/tick.png",
                            tnc: {
                                title: "T&C",
                                action: {
                                    actionType: actionType,
                                    meta: {
                                        title: "Offer Details",
                                        dataItems: offer.tNc,
                                        url: offer.tNcUrl
                                    }
                                }
                            }
                        })
                    }
                }
            })

            if (offerItems.length > 0) {
                return {
                    widgetType: "PRODUCT_OFFER_WIDGET_V2",
                    offerItems: offerItems,
                    dividerType: "NONE",
                    contentContainerStyle,
                    hideDashedBorder: true,
                    offersBackground: "#dfefe6",
                    orientation: isDesktop ? "RIGHT" : undefined,
                }
            } else {
                return undefined
            }
        } else {
            return undefined
        }

    }


    private async getFoodMarketplaceConfirmationWidgets(userContext: UserContext, paymentDetail: PaymentDetail, product: Product, billingInfo: BillingInfo,
                                                        order: Order, orderProduct: OrderProduct, apiType?: "ORDER_CHECKOUT" | "ORDER_V1"): Promise<WidgetView[]> {

        const widgets: WidgetView[] = []
        const { createdDate, userAddress, deliveryCharges, packagingCharges, tipAmount = undefined } = order
        const orderDate = TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, createdDate)

        const dateWidget = orderDate ? this.getOrderDateWidget(userContext, orderDate, false, "", [], false, undefined, undefined, true ) : undefined
        const addressWidget = this.getOrderAddressWidget(userAddress)
        const summaryWidget = this.getOrderSummaryWidget(userContext.sessionInfo.userAgent, paymentDetail, product, billingInfo, deliveryCharges, true, true, packagingCharges, this.numberOfProducts(order), tipAmount)

        widgets.push(addressWidget, dateWidget, summaryWidget)
        if (!_.isEmpty(apiType) && apiType === "ORDER_V1") {
            this.addNeuPassEarnedCoinsWidget(order, widgets)
        }

        return Promise.resolve(_.compact(widgets.filter(Boolean)))


    }

    private async getCultBikeOrderDetailWidgets(
        userContext: UserContext,
        paymentDetail: PaymentDetail,
        product: Product,
        billingInfo: BillingInfo,
        order: Order,
        apiType: "ORDER_CHECKOUT" | "ORDER_V1"): Promise<WidgetView[]> {

        const widgets: WidgetView[] = []
        const { createdDate, userAddress, deliveryCharges, packagingCharges, tipAmount = undefined } = order
        userAddress.eatDeliveryInstruction = undefined
        const addressWidget = userAddress ? this.getOrderAddressWidget(userAddress) : undefined
        const summaryWidget = this.getOrderSummaryWidget(userContext.sessionInfo.userAgent, paymentDetail, product, billingInfo, deliveryCharges, true, true, packagingCharges, this.numberOfProducts(order), tipAmount)

        widgets.push(addressWidget, summaryWidget)
        if (!_.isEmpty(apiType) && apiType === "ORDER_V1") {
            this.addNeuPassEarnedCoinsWidget(order, widgets)
        }
        widgets.push(
            {
                widgetType: "FORMATTED_TEXT_WIDGET",
                data: [
                    {
                        text: "Please write to us at ",
                        fontWeight: "400",
                        fontSize: 16,
                        fontColor: "#000000",
                        lineHeight: 20
                    },
                    {
                        text: "<EMAIL>",
                        fontWeight: "700",
                        fontSize: 16,
                        fontColor: "#FF3277",
                        lineHeight: 20
                    },
                    {
                        text: " for any cancellation/ refund requests, or queries",
                        fontWeight: "400",
                        fontSize: 16,
                        fontColor: "#000000",
                        lineHeight: 20
                    },
                ]
            }
        )

        widgets.push(
            {
                widgetType: "FORMATTED_TEXT_WIDGET",
                data: [
                    {
                        text: "* ",
                        fontWeight: "700",
                        fontSize: 16,
                        fontColor: "#FF3277",
                        lineHeight: 20
                    },
                    {
                        text: "Track order works only after the product has been shipped",
                        fontWeight: "400",
                        fontSize: 16,
                        fontColor: "#000000",
                        lineHeight: 20
                    },
                ]
            }
        )

        return Promise.resolve(_.compact(widgets.filter(Boolean)))


    }

    private async getFoodSingleConfirmationWidgets(userContext: UserContext, paymentDetail: PaymentDetail, product: Product, billingInfo: BillingInfo,
                                                   deliveryCharge: DeliveryCharge, packagingCharge: ExtraCharge, orderProduct: OrderProduct, address: UserDeliveryAddress, numProducts: number, orderId: string, allPossibleActionsForFoodBooking?: Action[], foodBooking?: FoodBooking, orderDetail?: OrderDetail, tipAmount?: TippingCharge): Promise<WidgetView[]> {
        const tz = userContext.userProfile.timezone
        const widgetViews: WidgetView[] = []
        const slotId = orderProduct.option.deliverySlot
        const slot = this.deliverySlotService.getSlotById(slotId)
        const isWeb = ["MBROWSER", "DESKTOP"].includes(userContext.sessionInfo.userAgent)
        // widgetViews.push(this.getOrderAddressWidget(address))

        let shouldAddDateWidgetForWeb: boolean = true
        const orderTrackingWidget = await this.getOrderTrackingWidget(orderId, userContext)
        const isOrderTrackingWidgetToBeAdded: boolean = !!(orderTrackingWidget && !_.isEmpty(orderTrackingWidget.values))
        if (isOrderTrackingWidgetToBeAdded) {

            // order tracking widget is there so don't show the date widget for web
            widgetViews.push(orderTrackingWidget)
            shouldAddDateWidgetForWeb = false
            if (orderTrackingWidget.values && orderDetail) {
                const expectedDeliveryTimeValueObject: any = orderTrackingWidget.values.find(value => !!value.expectedDeliveryTime)
                if (expectedDeliveryTimeValueObject) {
                    orderDetail.expectedDeliveryTime = expectedDeliveryTimeValueObject.expectedDeliveryTime
                }
            }
        }

        if (shouldAddDateWidgetForWeb || !isWeb) {
            // if the food singles meal is cancellable or reschedulable, subtitle logic is different for date widget
            const isCancellable = !!(allPossibleActionsForFoodBooking && allPossibleActionsForFoodBooking.find(action => ["CANCEL_MEAL", "CANCEL_CART"].includes(action.actionType)))

            // get cut off time for food booking
            let cutoffDisplayText: string
            if (isCancellable) {
                const cancelCutoffTime = SlotUtil.getCancelCutOffByChannelOrSlot(foodBooking.deliverySlot ? foodBooking.deliverySlot.slotId : undefined, foodBooking.deliveryWindow, foodBooking.deliveryChannel, tz)
                cutoffDisplayText = MealUtil.getCancelCutOffDisplayTextForTime(cancelCutoffTime)
            }

            const orderDateWidget = this.getOrderDateWidget(userContext, orderProduct.option.startDate, false, "", allPossibleActionsForFoodBooking, isCancellable, cutoffDisplayText)
            // subtitle should not be overriden if the order is cancellable or reschedulable
            if (!isCancellable) {
                if (_.isNil(orderProduct.option.deliverySlot) && EtherOrderUtil.isSubOrderId(orderId)) {
                    orderDateWidget.subTitle = slotId ? MealUtil.getDisplayTextForSlot(slot, undefined, undefined, undefined, address.kioskType === "CAFE") :
                        MealUtil.getSlotDisplayTextFromTime(orderProduct.option.deliveryWindow.startingHours, orderProduct.option.deliveryWindow.closingHours, address.addressType === "KIOSK", momentTz.tz(orderProduct.option.startDate, tz).toDate(), undefined)
                } else if ((slotId && slotId !== SlotUtil.ON_DEMAND_SLOT) || orderProduct.option.deliveryWindow) {
                    orderDateWidget.subTitle = slotId ? MealUtil.getDisplayTextForSlot(slot, undefined, undefined, undefined, address.kioskType === "CAFE") :
                        MealUtil.getSlotDisplayTextFromTime(orderProduct.option.deliveryWindow.startingHours, orderProduct.option.deliveryWindow.closingHours, address.addressType === "KIOSK", momentTz.tz(orderProduct.option.startDate, tz).toDate())
                }
            }
            widgetViews.push(orderDateWidget)
        }
        widgetViews.push(await this.getOrderSummaryWidget(userContext.sessionInfo.userAgent, paymentDetail, product, billingInfo, deliveryCharge, false, false, packagingCharge, numProducts, tipAmount))
        return widgetViews
    }

    async getFitClubConfirmationWidgets(userContext: UserContext, paymentDetail: PaymentDetail, product: Product, billingInfo: BillingInfo, orderProduct: OrderProduct) {
        const widgetViews: WidgetView[] = []
        const orderDateWidget = this.getOrderDateWidget(userContext, orderProduct.option.startDate, false)
        widgetViews.push(orderDateWidget)
        widgetViews.push(this.getOrderSummaryWidget(userContext.sessionInfo.userAgent, paymentDetail, product, billingInfo, undefined, false, false, undefined, 1))
        return widgetViews
    }

    private async getAppointmentConfirmationWidgets(userContext: UserContext, paymentDetail: PaymentDetail, product: Product, productBilling: ProductBilling, deliveryCharge: DeliveryCharge,
        orderProduct: OrderProduct, address: UserDeliveryAddress): Promise<WidgetView[]> {
        const widgetViews: WidgetView[] = []
        widgetViews.push(this.getOrderDateWidget(userContext, orderProduct.option.appointmentDate, false))
        widgetViews.push(this.getOrderAddressWidget(address))
        widgetViews.push(this.getOrderSummaryWidget(userContext.sessionInfo.userAgent, paymentDetail, product, productBilling, deliveryCharge, false, false))
        return widgetViews
    }

    private getPriceDetails(productType: ProductType, billingInfo: BillingInfo, deliveryCharge: DeliveryCharge, packagingCharge: ExtraCharge, removeBasePrice: boolean, removeTax: boolean, areDetailsForCheckout: boolean, tipAmount?: TippingCharge, baseOrder?: BaseOrder, showGst?: boolean): PriceComponent[] {
        const priceDetails: PriceComponent[] = []
        let platformFee: string = undefined
        let platformFeeTax: string = undefined
        let totalPlatformFeeIncTax: number = undefined
        if (baseOrder?.extraCharges && baseOrder?.extraCharges?.platformFee) {
            platformFee = baseOrder?.extraCharges?.platformFee?.unitPrice?.toFixed(2)
            platformFeeTax = baseOrder?.extraCharges?.platformFee?.tax?.amount?.toFixed(2)
            totalPlatformFeeIncTax = baseOrder?.extraCharges?.platformFee?.total
        }

        // BOOSTER PACKS PRICING
        const boosterPackProductPriceObject = OrderUtilV1.isPlusMembershipOrder(baseOrder) ? undefined :  baseOrder?.productSnapshots?.[0]?.addOnSnapshots?.[0]?.price
        let totalBoosterPackPriceIncTax: number = undefined
        let boosterPackTax: number = undefined
        if (boosterPackProductPriceObject) {
            totalBoosterPackPriceIncTax = boosterPackProductPriceObject?.listingPrice
            boosterPackTax = boosterPackProductPriceObject?.tax?.taxAmount
        }

        priceDetails.push({
            title: "MRP",
            value: billingInfo.mrp.toFixed(2)
        })

        if (billingInfo.discount && !showGst) {
            priceDetails.push(...OrderUtil.getDiscountPriceDetails(billingInfo))
        }

        if (!areDetailsForCheckout && billingInfo.fitCashPayable) {
            priceDetails.push({
                title: "Fitcash Discount",
                value: billingInfo.fitCashPayable.toFixed(2),
                isDiscount: true
            })
        }

        if (!areDetailsForCheckout) {
            if (!removeBasePrice && !showGst) {
                priceDetails.push({
                    title: "Base Price",
                    value: billingInfo.unitPrice.toFixed(2)

                })
            }
        }

        if (!areDetailsForCheckout) {
            if (!removeTax && !showGst) {
                if (totalBoosterPackPriceIncTax && boosterPackTax) {
                    priceDetails.push({
                        title: "Booster Price",
                        value: (totalBoosterPackPriceIncTax - boosterPackTax).toFixed(2)
                    })
                }
                if (boosterPackTax) {
                    priceDetails.push({
                        title: "GST on Booster",
                        value: boosterPackTax.toFixed(2)
                    })
                }
                priceDetails.push({
                    title: billingInfo.tax.type + " @" + billingInfo.tax.percentage + "%",
                    value: billingInfo.tax.amount.toFixed(2)
                })
                if (platformFee) {
                    priceDetails.push({
                        title: "Platform fee",
                        value: platformFee,
                        action: {
                            actionType: "NAVIGATION",
                            url: "curefit://native_fl_sheet",
                            meta: {
                                screenAction: CartViewBuilder.getTaxesAndFeeBottomSheetAction()
                            }
                        }
                    })
                }
                if (platformFeeTax) {
                    priceDetails.push({
                        title: "GST on Platform fee",
                        value: platformFeeTax
                    })
                }
            }
        }

        const sellingAmount = _.round((billingInfo.total < 0 && productType === "FOOD_MARKETPLACE") ? 0 : billingInfo.total, 2)
        if (showGst) {
            priceDetails.push({
                title: "Discounted Price",
                value: (parseFloat(sellingAmount.toFixed(2)) - parseFloat(billingInfo.tax.amount.toFixed(2))).toFixed(2).toString()
            })
        }

        if (showGst) {
            if (totalBoosterPackPriceIncTax && boosterPackTax) {
                priceDetails.push({
                    title: "Booster Price",
                    value: (totalBoosterPackPriceIncTax - boosterPackTax).toFixed(2)
                })
            }
            if (boosterPackTax) {
                priceDetails.push({
                    title: "GST on Booster",
                    value: boosterPackTax.toFixed(2)
                })
            }
            priceDetails.push({
                title: "Taxes (GST) ",
                value: billingInfo.tax.amount.toFixed(2)
            })
            if (platformFee) {
                priceDetails.push({
                    title: "Platform fee",
                    value: platformFee,
                    action: {
                        actionType: "NAVIGATION",
                        url: "curefit://native_fl_sheet",
                        meta: {
                            screenAction: CartViewBuilder.getTaxesAndFeeBottomSheetAction()
                        }
                    }
                })
            }
            if (platformFeeTax) {
                priceDetails.push({
                    title: "GST on Platform fee",
                    value: platformFeeTax
                })
            }
        }

        if (deliveryCharge) {
            const deliveryChargeValue = deliveryCharge.total + (deliveryCharge.fitCashPayable ? deliveryCharge.fitCashPayable : 0)
            priceDetails.push({
                // title: `Delivery charge\n(for orders under ${RUPEE_SYMBOL}200)`,
                title: `Delivery charge`,
                value: deliveryChargeValue.toFixed(2)
            })
        }
        if (packagingCharge) {
            const packagingChargeValue = packagingCharge.total + (packagingCharge.fitCashPayable ? packagingCharge.fitCashPayable : 0)
            priceDetails.push({
                title: `Packaging charge`,
                value: packagingChargeValue.toFixed(2)
            })
        }

        if (tipAmount) {
            const tipAmountValue = _.get(tipAmount, "total", 0) + _.get(tipAmount, "fitCashPayable", 0)
            priceDetails.push({
                title: "Tip",
                value: tipAmountValue.toFixed(2)
            })
        }

        let total = deliveryCharge ? billingInfo.total + deliveryCharge.total : billingInfo.total
        total = packagingCharge ? total + packagingCharge.total : total
        total = total + _.get(tipAmount, "total", 0)
        total = totalPlatformFeeIncTax ? total + totalPlatformFeeIncTax : total
        total = totalBoosterPackPriceIncTax ? total + totalBoosterPackPriceIncTax : total

        if (baseOrder?.collectionCharges?.total >= 0) {
            const careExtraChargeConfig = this.configService.getConfig("CARE_EXTRA_CHARGE_CONFIG")
            const collectionChargeLimit = _.get(careExtraChargeConfig, "configs.collectionChargeLimit")
            const collectionChargeAmount = _.get(careExtraChargeConfig, "configs.collectionCharge")
            priceDetails.push({
                title: `Home Sample Collection Charges`,
                value: baseOrder?.collectionCharges?.total > 0 ? baseOrder?.collectionCharges?.total.toString() : "FREE",
                valueWithCurrency: collectionChargeAmount > 0 ? RUPEE_SYMBOL + collectionChargeAmount.toString() : undefined,
                offerText: "FREE",
                isValueStrikeOff: baseOrder?.collectionCharges?.total > 0 ? false : true
            })
            total = total + (baseOrder?.collectionCharges?.total > 0 ? baseOrder.collectionCharges.total : 0)
        }

        if ((baseOrder as Order)?.productTypeOptions?.totalPartnerTaxes) {
            const partnerTaxes = (baseOrder as Order)?.productTypeOptions?.totalPartnerTaxes
            priceDetails.push({
                title: "Partner Tax",
                value: partnerTaxes.toFixed(2)
            })
        }

        if ((baseOrder as Order)?.productTypeOptions?.totalPartnerCharges) {
            const partnerCharges = (baseOrder as Order)?.productTypeOptions?.totalPartnerCharges
            priceDetails.push({
                title: "Partner Charge",
                value: partnerCharges.toFixed(2)
            })
        }

        priceDetails.push({
            title: "Total Payable",
            value: total.toFixed(2)

        })
        return priceDetails
    }

    private getPaymentDetailWidget(product: Product, billingInfo: BillingInfo, deliveryCharge: DeliveryCharge, removeBasePrice: boolean, removeTax: boolean, areDetailsForCheckout: boolean, userAgent?: UserAgent, baseOrder?: BaseOrder): PaymentDetailsWidget {
        const paymentDetailWidget: PaymentDetailsWidget = {
            widgetType: "PAYMENT_DETAILS_WIDGET",
            priceDetails: this.getPriceDetails(product.productType, billingInfo, undefined, undefined, true, removeTax, areDetailsForCheckout, undefined, baseOrder),
            finalPrice: {
                listingPrice: billingInfo.total,
                mrp: billingInfo.total,
                currency: billingInfo.currency
            }
        }
        if (userAgent === "DESKTOP") {
            paymentDetailWidget.orientation = "RIGHT"
        }
        return paymentDetailWidget
    }

    private getExperienceCenterPaymentDetailWidget(product: Product, billingInfo: BillingInfo, deliveryCharge: DeliveryCharge, removeBasePrice: boolean, removeTax: boolean, areDetailsForCheckout: boolean, userAgent?: UserAgent, baseOrder?: BaseOrder): SfExperienceCenterPaymentDetailsWidget {
        const paymentDetailWidget: PaymentDetailsWidget = {
            widgetType: "SF_EXPERIENCE_CENTER_PAYMENT_DETAILS_WIDGET" as any,
            priceDetails: this.getPriceDetails(product.productType, billingInfo, undefined, undefined, true, removeTax, areDetailsForCheckout, undefined, baseOrder),
            hasDividerBelow: false,
            finalPrice: {
                listingPrice: billingInfo.total,
                mrp: billingInfo.total,
                currency: billingInfo.currency
            }
        }
        return paymentDetailWidget
    }

    private getOrderSummaryWidget(userAgent: UserAgent, paymentDetail: PaymentDetail, product: Product, billingInfo: BillingInfo, deliveryCharge: DeliveryCharge, removeBasePrice: boolean, removeTax: boolean, packagingCharge?: ExtraCharge, numProducts: number = 1, tipAmount?: TippingCharge, baseOrder?: BaseOrder, showGst?: boolean): OrderSummaryWidget {
        const productType = product.productType
        let thumbnailImages, magazineImage, title = product.title
        if ((product.productType === "GYMFIT_FITNESS_PACK" || product.productType === "THIRD_PARTY_FITNESS_PRODUCT") && product.isPack) {
            thumbnailImages = product.imageUrls && product.imageUrls.length ? [product.imageUrls[0]] : []
        } else if (product.productType === "GYMFIT_FITNESS_PRODUCT") {
            magazineImage = GymfitUtil.getDefaultMagazineImage()
        } else if (product.productType === "LUX_FITNESS_PRODUCT") {
            magazineImage = GymfitUtil.getDefaultLuxMagazineImage()
            const luxPack = product as unknown as OfflineFitnessPack
            title = luxPack.displayName ?? product.title
        } else if (product.productType === "FITNESS" || product.productType === "ADDON") {
            magazineImage = DEFAULT_IMAGE_URL
        } else if (product.productType === "FIT_CLUB_MEMBERSHIP" || product.isPack) {
            if ((userAgent === "DESKTOP" || userAgent === "MBROWSER") && product.productType === "PLAY") {
                magazineImage = "/image/vm/fae44bca-caec-4624-9110-73dd38d707b8.jpg"
            } else {
                magazineImage = UrlPathBuilder.getPackImagePath(product.productId, product.productType, "MAGAZINE", product.imageVersion, userAgent)
            }
        } else {
            const imageProductId = EatUtil.getProductId(product)
            thumbnailImages = [UrlPathBuilder.getSingleImagePath(imageProductId, product.productType, "THUMBNAIL", product.imageVersion)]
        }
        let productTypeforIconUrl = (product.productType === "FOOD_MARKETPLACE" ? "FOOD" : product.productType === "ADDON" ? "FITNESS" : product.productType)?.toLowerCase()
        if (productType == "PLAY") {
            productTypeforIconUrl = product.productType.toLowerCase() + "_1"
        }
        const areDetailsForCheckout: boolean = !paymentDetail || paymentDetail.paymentChannel === null
        const sellingAmount = _.round((billingInfo.total < 0 && product.productType === "FOOD_MARKETPLACE") ? 0 : billingInfo.total, 2)
        const orderSummaryWidget: OrderSummaryWidget = {
            productId: product.productId,
            widgetType: "ORDER_SUMMARY_WIDGET",
            title: numProducts > 1 ? numProducts + " items ordered" : title,
            thumbnailImages: thumbnailImages,
            magazineImage: magazineImage,
            icon: product.productType === "FITNESS_ACCESSORIES" ? "FITNESS" : (product.productType === "FOOD_MARKETPLACE" ? "FOOD" : product.productType),
            iconUrl: `/image/icons/checkout/${productTypeforIconUrl}.png`,
            price: {
                listingPrice: sellingAmount,
                mrp: sellingAmount,
                amountPaid: showGst ? sellingAmount - Math.round(billingInfo.tax.amount) : sellingAmount, // this refers to base amount (app uses) _.round(billingInfo.totalAmountPaid, 2) [sending listing price for now here because totalAmountPayable will give wrong impression on checkout page]
                currency: billingInfo.currency,
                totalInclusiveAll: (baseOrder?.extraCharges && baseOrder?.extraCharges?.platformFee) ? (sellingAmount + +baseOrder?.extraCharges?.platformFee?.total.toFixed(2)) : sellingAmount
            },
            priceDetails: this.getPriceDetails(productType, billingInfo, deliveryCharge, packagingCharge, removeBasePrice, removeTax, areDetailsForCheckout, tipAmount, baseOrder, showGst),
            paymentDetail: paymentDetail
        }
        if (!!baseOrder && careUtil.isTransformSecondaryProduct(baseOrder.products)) {
            orderSummaryWidget.title = product.title + " + " + baseOrder.productSnapshots[1].title
        }
        return orderSummaryWidget
    }
    private getOrderDateWidget(userContext: UserContext, date: string, isPack: boolean, subtitle?: string, allPossibleActionsForFoodBooking?: Action[], isCancellable?: boolean, cutoffDisplayText?: string, titleOverride?: string, hideSubtitle?: boolean): InfoWidget {
        const tz = userContext.userProfile.timezone
        let title: string = ""
        if (isPack) {
            title = `Starting ${TimeUtil.formatDateStringInTimeZone(date, tz, "Do MMMM YYYY")}`
        } else {
            title = TimeUtil.formatDateStringInTimeZone(date, tz, "Do MMMM YYYY")
            if (allPossibleActionsForFoodBooking) {
                const mealRescheduleOptions = allPossibleActionsForFoodBooking.find((action) => action.actionType === "MEAL_RESCHEDULE_OPTIONS")
                if (mealRescheduleOptions) {
                    const activeOption = mealRescheduleOptions.meta.options.find((option: ManageOptionPayload) => _.get(option, "meta.slotId", null) === mealRescheduleOptions.meta.activeDeliverySlot)
                    if (activeOption) {
                        title = `Delivering ${activeOption.displayText}`
                    }
                }
            }
        }
        if (titleOverride) {
            title = titleOverride
        }
        const subTitle: string = hideSubtitle ? undefined : (subtitle || TimeUtil.getDayText(date, tz))
        let actionText: string
        if (isCancellable && !subtitle) {
            actionText = `Make changes to your order before ${cutoffDisplayText}`
        }
        const dateWidget: InfoWidget = {
            widgetType: "INFO_WIDGET",
            icon: "DATE",
            title: title,
            subTitle: subTitle,
            actions: allPossibleActionsForFoodBooking,
            actionText
        }
        return dateWidget
    }

    private getInvoiceWidget(user: User, orderId: string) {
        const getInvoiceWidget: InfoWidget = {
            widgetType: "INFO_WIDGET",
            icon: "EMAIL",
            title: "Email",
            subTitle: user.email ? user.email : "Email id is missing",
            actions: [{
                title: "GET INVOICE",
                actionType: "GET_INVOICE",
                meta: {
                    user: user.email,
                    orderId: orderId
                }
            }]
        }
        return getInvoiceWidget
    }

    private downloadInvoiceWidget(orderId: string) {
        const getInvoiceWidget: InfoWidget = {
            widgetType: "INFO_WIDGET",
            icon: "DOWNLOAD",
            actions: [{
                title: "DOWNLOAD INVOICE",
                actionType: "REST_API",
                trigger: true,
                url: `/order/${orderId}/downloadInvoice`
            }]
        }
        return getInvoiceWidget
    }

    private getApplePurchaseDetailsWidget(userContext: UserContext) {
        const applePurchaseWidget: InfoWidget = {
            widgetType: "INFO_WIDGET",
            icon: "INFO_PINK",
            title: "In-App Purchase",
            subTitle: userContext.sessionInfo.osName === "ios" ? "" : "Purchased with Apple",
            actions: userContext.sessionInfo.osName === "ios" ? [{
                title: "SEE DETAILS",
                actionType: "EXTERNAL_DEEP_LINK",
                meta: {},
                url: `https://apps.apple.com/account/subscriptions`
            }] : []
        }
        return applePurchaseWidget
    }

    private getOrderDateTimeWidget(datetime: string, userContext: UserContext): WidgetView {
        const tz = userContext.userProfile.timezone
        const title = TimeUtil.formatDateStringInTimeZone(datetime, tz, "Do MMMM YYYY")
        const subTitle = TimeUtil.formatDateStringInTimeZone(datetime, tz, "hh:mm A")
        const dateWidget: InfoWidget = {
            widgetType: "INFO_WIDGET",
            icon: "DATE",
            title: title,
            subTitle: subTitle
        }
        return dateWidget
    }
    private getOrderAddressWidget(address: UserDeliveryAddress): WidgetView {
        let subTitle = (address.addressLine1 ? address.addressLine1 + " , " + address.addressLine2 : address.addressLine2)
        if (address.addressType !== "KIOSK" && address.eatDeliveryInstruction) {
            subTitle += "\n\nInstruction: " + EatUtil.getStandingInstruction(address.eatDeliveryInstruction)
        }
        const addressWidget: InfoWidget = {
            widgetType: "INFO_WIDGET",
            icon: "LOCATION",
            title: address.addressType,
            subTitle: subTitle
        }
        return addressWidget
    }

    private getCultEventLocationWidget(address: any): InfoWidget {
        const addressWidget: InfoWidget = {
            widgetType: "INFO_WIDGET",
            icon: "LOCATION",
            title: "Event Address",
            subTitle: address.addressLine1 + " , " + address.state,
        }
        return addressWidget
    }
    private getCultCenterWidget(centerName: string, title: string = "Preferred Center", workouts?: CultWorkout[]): InfoWidget {

        const addressWidget: InfoWidget = {
            widgetType: "INFO_WIDGET",
            icon: "LOCATION",
            title: title,
            subTitle: centerName
        }
        if (!_.isEmpty(workouts)) {
            const workoutTags = _.map(workouts, workout => workout.name)
            addressWidget.tags = workoutTags
            addressWidget.tagsTitle = "Workouts available at center:"
        }
        return addressWidget
    }

    private getGymfitCenterWidget(centerName: string, title: string = "Only At"): InfoWidget {
        const addressWidget: InfoWidget = {
            widgetType: "INFO_WIDGET",
            icon: "LOCATION",
            title: title,
            subTitle: centerName
        }
        return addressWidget
    }

    private getSubUserNameWidget(subUserName: string, title: string = "For Whom"): InfoWidget {
        const addressWidget: InfoWidget = {
            widgetType: "INFO_WIDGET",
            icon: "SUB_USER_ICON",
            title: title,
            subTitle: subUserName
        }
        return addressWidget
    }

    private async isCultPTOrder(order: Order): Promise<boolean> {
        const product = order.productSnapshots[0]
        if (product.productType === "CONSULTATION") {
            const consultationProduct: ConsultationProduct = <ConsultationProduct>await this.catalogueService.getProduct(product.productId)
            return !_.isNil(consultationProduct) && consultationProduct.tenant === "CULTFIT"
        } else if (product.productType === "BUNDLE") {
            return product.categoryId === "LIVE_PERSONAL_TRAINING" || product.categoryId === "LIVE_SGT"
        }
        return false
    }

    private getLivePTOrderCheckoutActionTitle(order: Order): string {
        const orderProductOption = order.products[0].option
        const listingPrice = order.totalPayable ? order.totalPayable : order.totalAmountPayable
        const isMember = orderProductOption.parentBookingId && orderProductOption.parentBookingId !== -1 && listingPrice === 0
        if (isMember) {
            return "PROCEED TO CONFIRM"
        }
        if (listingPrice === 0) {
            return "BOOK FOR FREE"
        } else {
            return `BOOK FOR ${RUPEE_SYMBOL}${listingPrice}`
        }
    }

    private async isLiveCultPTOrder(order: Order): Promise<boolean> {
        const product = order.productSnapshots[0]
        if (product.categoryId === "CF_ONLINE_CONSULTATION") {
            const consultationProduct: ConsultationProduct = <ConsultationProduct>await this.catalogueService.getProduct(product.productId)
            return !_.isNil(consultationProduct) && consultationProduct.tenant === "CULTFIT"
        } else return product.categoryId === "LIVE_PERSONAL_TRAINING" || product.categoryId === "LIVE_SGT"
    }

    private async addOfferAddonWidget(response: OrderCheckoutDetail, order: Order, apiType: string, staticOfferItems?: IOfferAddonData[], title?: string): Promise<void> {
        if (_.isEmpty(order.offersApplied)) return
        const offerV2MapPromise: Promise<{
            [key: string]: OfferV2;
        }> = this.offerServiceV3.getOffersByOffersInfo(order.offersInfo).then(o => o.data)
        let useLabel: "CART_LABEL" | "ORDER_LABEL"
        if (apiType === "ORDER_CHECKOUT") {
            useLabel = "CART_LABEL"
        } else if (apiType === "ORDER_V1") {
            useLabel = "ORDER_LABEL"
        } else {
            return
        }
        const offerAddonWidgetParams: IOfferAddonWidgetParams = {
            logger: this.logger,
            offerV2MapPromise: offerV2MapPromise,
            useLabel: useLabel,
            staticOfferItems: staticOfferItems,
            title
        }
        let offerAddonWidget: OfferAddonWidget = new OfferAddonWidget()
        offerAddonWidget = await offerAddonWidget.buildView(offerAddonWidgetParams)
        if (!_.isNil(offerAddonWidget)) {
            response.widgets.push(offerAddonWidget)
        }
    }

    private async addNeuPassCheckoutWidget(userContext: UserContext, response: OrderCheckoutDetail, order: Order, apiType: string, showActivationWidget: boolean = false): Promise<void> {
        this.logger.info("userContext.userProfile?.userId in addNeuPassCheckoutWidget", {
            userId: userContext.userProfile?.userId
        })
        if (apiType !== "ORDER_CHECKOUT") return
        if (!["CUREFIT_APP", "CUREFIT_NEW_WEBSITE", "TATA_NEU_WEB_APP", "TATA_NEU_CULTSPORT_WEBSITE"].includes(order.source)) return
        const userId: string = userContext.userProfile?.userId
        if (_.isEmpty(userId)) return
        if (AppUtil.isTataNeuWebFlow(order?.source) || await this.isTataOrganicTestUser(userContext)) {
            const segmentsForTataNeu = TataNeuUtil.getSegmentsForTataNeu()
            const isPotentialTataUser: boolean = await TataNeuUtil.doesUserBelongToTataSegment(this.segmentationClient, segmentsForTataNeu.POTENTIAL_TATA_USER, userId)
            const isNeuPassConsentPending: boolean = await TataNeuUtil.doesUserBelongToTataSegment(this.segmentationClient, segmentsForTataNeu.NEUPASS_CONSENT_PENDING, userId)
            const isNeupassNotActivated: boolean = await TataNeuUtil.doesUserBelongToTataSegment(this.segmentationClient, segmentsForTataNeu.NEUPASS_NOT_ACTIVATED, userId)
            if (showActivationWidget && TataUtil.isTataNeuProduct(order) && (isPotentialTataUser || isNeuPassConsentPending || isNeupassNotActivated)) {
                const activationCheckboxWidget: WidgetView = await this.getNeupassActivationWidget(order, userId, isNeuPassConsentPending)
                if (!_.isEmpty(activationCheckboxWidget)) {
                    response.widgets.push(activationCheckboxWidget)
                }
            } else {
                const isNeuPassUser: boolean = await TataNeuUtil.doesUserBelongToTataSegment(this.segmentationClient, segmentsForTataNeu.NEUPASS_USER, userId)
                if (TataUtil.isOrderSourceSupportedForTataNeu(order) && TataUtil.isTataNeuProduct(order) && isNeuPassUser) {
                    const potentialCoinsWidget: NeuPassEarnedCoinsWidget = await this.getPotentialNeuPassEarnWidget(order, userId)
                    if (!_.isEmpty(potentialCoinsWidget)) {
                        response.widgets.push(potentialCoinsWidget)
                    }
                }
            }
        }
    }

    public async getNeupassActivationWidget(order: Order, userId: string, isNeuPassConsentPending = false): Promise<WidgetView> {
        const potentialNeuPointsToBeEarned: PotentialTataPointsEarnResponse = await this.getPotentialNeuPointsToBeEarned(order, userId)
        if (potentialNeuPointsToBeEarned.points > 0) {
            const programId: string = process.env.ENVIRONMENT === "PRODUCTION" ? "01eae2e7-4cd5-18c9-8f71-044d55bacult" : "cultpqr-5bff-2c6e-fadd-2bee065eef45"
            const knowMoreURLPrefix: string = process.env.ENVIRONMENT === "PRODUCTION" ? "https://tatadigital.com" : "https://aem-dev.tatadigital.com"
            let title2 = " NeuPass"
            if (isNeuPassConsentPending) {
                title2 = " NeuPass Benefits"
            }
            return {
                widgetType: "TOGGLE_NEUPASS_ACTIVATION_WIDGET",
                data: {
                    subTitle: potentialNeuPointsToBeEarned.points > 0 ? "Get upto " + potentialNeuPointsToBeEarned.points + " NeuCoins\n1 NeuCoin = ₹1" : "",
                    title: "Activate your ",
                    icon: "image/tata/tata-neu.png",
                    title2: title2,
                    checked: true,
                    // knowMore: {
                    //     actionType: "NAVIGATION",
                    //     url: `curefit://webview?uri=${encodeURIComponent(`${knowMoreURLPrefix}/v2/neupass/knowmore?source=brand&programId=${programId}`)}&theme=dark&title=${encodeURIComponent("About Neupass")}`,
                    //     title: "KNOW MORE"
                    // },
                    action: {
                        actionType: "REST_API",
                        title: "DISMISS",
                        meta: {
                            method: "POST",
                            url: `/order/toggleActivateNeuPass`,
                            body: {
                                orderId: order.orderId,
                                activateNeuPass: false
                            },
                            showLoader: true,
                        },
                    },
                }
            }
        }
    }

    // TODO: Remove later when Tata organic will be rolled out to all users.
    public async isTataOrganicTestUser(userContext: UserContext): Promise<boolean> {
        const userId: string = userContext?.userProfile?.userId
        if (_.isEmpty(userId) || userId === "0") {
            return false
        }
        let isTataTestUser: boolean = false
        try {
            const experimentId = ["production", "alpha"].includes(process.env.ENVIRONMENT.toLowerCase()) ? "1064" : "527"
            const hamletRequest: HamletConfigRequest<boolean> = {
                query: {
                    experimentId,
                    configKey: "isTataTestUser",
                    defaultValue: false
                },
                context: {
                    userId,
                    deviceId: userContext?.sessionInfo?.deviceId,
                    tenant: Tenant.CUREFIT_APP
                }
            }
            isTataTestUser = await this.hamletService.getConfig(hamletRequest)
        } catch (err) {
            this.logger.error(`isTataTestUser error for userId: ${userId}: `, { err })
        }
        this.logger.info(`isTataTestUser for userId: ${userId} = ${isTataTestUser}`)
        return isTataTestUser
    }

    async getSelectMembershipTransferSummeryView(selectTransferMembershipResponse: any, membershipTransferDetails: any, user: User, userContext: UserContext): Promise<ActionPageWidgetView> {
        const widgetPromises: WidgetView[] = []
        widgetPromises.push(await this.selectTransferSummeryWidget(selectTransferMembershipResponse, membershipTransferDetails, user))
        widgetPromises.push(await this.selectTransferFeeSummeryWidget(selectTransferMembershipResponse, membershipTransferDetails, userContext))
        widgetPromises.push(...(await this.selectTransferFeeDetailWidget(selectTransferMembershipResponse, membershipTransferDetails, userContext)))
        const pageActionTitle = selectTransferMembershipResponse.totalAmount > 0 ? `Pay ${RUPEE_SYMBOL}${selectTransferMembershipResponse.totalAmount}` : `Confirm transfer`
        return { widgets: widgetPromises, pageActionTitle: pageActionTitle }
    }

    private async selectTransferSummeryWidget(selectTransferMembershipResponse: any, membershipTransferDetails: any, user: User): Promise<MembershipTransferSummeryWidget> {
        const transferSummeryWidget: MembershipTransferSummeryWidget = {
            widgetType: "MEMBERSHIP_TRANSFER_SUMMERY_WIDGET",
            header: `TRANSFER CULTPASS SELECT`,
            membershipTransferType: "ANOTHER_CENTER",
            transferFrom: {
                title: "FROM",
                location: membershipTransferDetails.currentCenterName
            },
            transferTo: {
                title: "TO",
                location: membershipTransferDetails.selectedCenterName
            }
        }
        transferSummeryWidget["name"] = `${user.firstName} ${user.lastName ? user.lastName : ""}`
        transferSummeryWidget["imageUrl"] = user.profilePictureUrl
        return transferSummeryWidget
    }

    private async selectTransferFeeDetailWidget(selectTransferMembershipResponse: any, membershipTransferDetails: any, userContext: UserContext) {
        const tz = userContext.userProfile.timezone
        const widgets = []
        const totalFeeLineItemWidget: MembershipFeeInfoLineItemWidget = {
            widgetType: "MEMBERSHIP_FEE_INFO_LINE_WIDGET",
            text: "Total Payable",
            backgroundColor: "rgba(247, 247, 247, 1.0)",
            fees: `${RUPEE_SYMBOL}${selectTransferMembershipResponse.totalAmount}`,
            faded: false
        }
        widgets.push(totalFeeLineItemWidget)
        if (selectTransferMembershipResponse.daysToIncrease > 0) {
            const daysIncreaseModal: MembershipFeesDetailWidget = {
                widgetType: "MEMBERSHIP_FEE_INFO_DETAIL_WIDGET",
                header: "Calculation of Remaining Days",
                subHeader:
                    `Your current pack costs less in ${membershipTransferDetails.selectedCenterName}. The remaining days of your pack will be adjusted to account for the price difference.`,
                feeItemRows: [
                    {
                        label: `Number of days remaining`,
                        data: selectTransferMembershipResponse.daysLeft
                    },
                    {
                        label: "Number of days adjusted",
                        data: `+${selectTransferMembershipResponse.daysToIncrease}`
                    },
                    {
                        label: `Remaining days transferred`,
                        data: selectTransferMembershipResponse.daysLeft + selectTransferMembershipResponse.daysToIncrease
                    }
                ],
                bottomLine: {
                    label: "Membership will end on",
                    data: momentTz.tz(membershipTransferDetails.membershipEndDate, tz).add(selectTransferMembershipResponse.daysToIncrease, "days").format("DD MMM YYYY")
                }
            }
            const daysIncreaseWidget: MembershipFeeInfoLineItemWidget = {
                widgetType: "MEMBERSHIP_FEE_INFO_LINE_WIDGET",
                text: "Days Increased for transfer",
                backgroundColor: "rgba(247, 247, 247, 1.0)",
                fees: `${selectTransferMembershipResponse.daysToIncrease}`,
                infoIcon: true,
                infoIconAction: {
                    type: "SHOW_UPGRADE_MEMBERSHIP_FEE_DETAILS_MODAL",
                    actionType: "SHOW_UPGRADE_MEMBERSHIP_FEE_DETAILS_MODAL",
                    meta: {
                        widgets: [{...daysIncreaseModal}]
                    }
                },
                faded: false
            }
            widgets.push(daysIncreaseWidget)
        }
        if (selectTransferMembershipResponse.creditsLeft >= 0 && selectTransferMembershipResponse.creditsToIncrease > 0) {
            const creditsModal: MembershipFeesDetailWidget = {
                widgetType: "MEMBERSHIP_FEE_INFO_DETAIL_WIDGET",
                header: "Calculation of Transferable Days",
                subHeader:
                    `Credits are transferred proportionally, reflecting your current membership type, to the membership at the new center`,
                feeItemRows: [
                    {
                        label: `Credits remaining`,
                        data: `${selectTransferMembershipResponse.creditsLeft} credits`,
                        iconUrl: CREDIT_PILL_ICON
                    },
                    {
                        label: "Extra credits adjusted",
                        data: `+${selectTransferMembershipResponse.creditsToIncrease} credits`,
                        iconUrl: CREDIT_PILL_ICON
                    }
                ],
                bottomLine: {
                    label: `Total Credits`,
                    data: `${selectTransferMembershipResponse.creditsLeft + selectTransferMembershipResponse.creditsToIncrease} credits`,
                    iconUrl: CREDIT_PILL_ICON
                }
            }
            const creditsWidget: MembershipFeeInfoLineItemWidget = {
                widgetType: "MEMBERSHIP_FEE_INFO_LINE_WIDGET",
                text: "Additional Credits",
                backgroundColor: "rgba(247, 247, 247, 1.0)",
                fees: `+${selectTransferMembershipResponse.creditsToIncrease} credits`,
                infoIcon: true,
                infoIconAction: {
                    type: "SHOW_UPGRADE_MEMBERSHIP_FEE_DETAILS_MODAL",
                    actionType: "SHOW_UPGRADE_MEMBERSHIP_FEE_DETAILS_MODAL",
                    meta: {
                        widgets: [{...creditsModal}]
                    }
                },
                faded: false,
                iconUrl: CREDIT_PILL_ICON
            }
            const pointsToNoteWidget = this.pointsToNoteWidget(true,  membershipTransferDetails.cityId, membershipTransferDetails.destinationCityId)
            widgets.push(creditsWidget)
            widgets.push(pointsToNoteWidget)
        } else if (_.isNil(selectTransferMembershipResponse.creditsLeft) && selectTransferMembershipResponse.creditsLeft !== 0 && selectTransferMembershipResponse.creditsToIncrease > 0) {
            const creditsWidget: MembershipFeeInfoLineItemWidget = {
                widgetType: "MEMBERSHIP_FEE_INFO_LINE_WIDGET",
                text: "Credits to access other centers",
                backgroundColor: "rgba(247, 247, 247, 1.0)",
                fees: `${selectTransferMembershipResponse.creditsToIncrease} credits`,
                infoIcon: false,
                faded: false,
                iconUrl: CREDIT_PILL_ICON
            }
            widgets.push(creditsWidget)
            const pointsToNoteWidget = this.pointsToNoteWidget(true,  membershipTransferDetails.cityId, membershipTransferDetails.destinationCityId)
            widgets.push(pointsToNoteWidget)
        } else if ((_.isNil(selectTransferMembershipResponse.areCreditsTransferred) || selectTransferMembershipResponse.areCreditsTransferred === false) && !_.isNil(selectTransferMembershipResponse.creditsLeft)) {
            const pointsToNoteWidget: WidgetView = {
                widgetType: "PRODUCT_LIST_WIDGET",
                type: "SMALL",
                header: {
                    title: "Points to note",
                    style: {
                        fontSize: 18
                    },
                    color: "#000000"
                },
                items: [
                    {
                        subTitle: "You have access to 2 sessions per month at other centers in " + (membershipTransferDetails.destinationCityId ? membershipTransferDetails.destinationCityId : "new city") + " in contrast to credit-based access in " + (membershipTransferDetails.cityId ? membershipTransferDetails.cityId : "current city"),
                        icon: "/image/icons/diff.png"
                    }
                ],
                hideSepratorLines: true,
                hasDividerBelow: false
            }
            widgets.push(pointsToNoteWidget)
        }
        return widgets
    }

    private async selectTransferFeeSummeryWidget(selectTransferMembershipResponse: any, membershipTransferDetails: any, userContext: UserContext): Promise<MembershipPostUpdateStatusWidget> {
        const tz = userContext.userProfile.timezone
        const selectTransferFeeSummeryWidget: MembershipPostUpdateStatusWidget = {
            widgetType: "MEMBERSHIP_POST_UPDATE_STATUS_WIDGET",
            status: [
                {
                    header: "Ends on",
                    infoText: momentTz.tz(membershipTransferDetails.membershipEndDate, tz).format("DD MMM YYYY")
                },
                {
                    header: "Remaining days",
                    infoText: selectTransferMembershipResponse.daysLeft
                },
                {
                    header: "Pause days",
                    infoText: membershipTransferDetails.pauseDays
                }
            ]
        }
        return selectTransferFeeSummeryWidget
    }


    async getMembershipTransferSummeryView(transferMembershipResponse: MembershipTransferDetailResponse, membershipTransferDetails: any, user: User, userContext: UserContext): Promise<ActionPageWidgetView> {
        const widgetPromises: WidgetView[] = []
        widgetPromises.push(await this.transferSummeryWidget(transferMembershipResponse, membershipTransferDetails, user))
        widgetPromises.push(await this.transferFeeSummeryWidget(transferMembershipResponse, membershipTransferDetails, userContext))
        const pageActionTitle = transferMembershipResponse.price > 0 ? `Pay ${RUPEE_SYMBOL}${transferMembershipResponse.price}` : `Confirm transfer`
        return { widgets: widgetPromises, pageActionTitle: pageActionTitle }
    }

    private async transferSummeryWidget(transferMembershipResponse: MembershipTransferDetailResponse, membershipTransferDetails: any, user: User): Promise<MembershipTransferSummeryWidget> {
        const transferMemberDetails: User = await this.userCache.getUser(membershipTransferDetails.destinationUserId)
        const transferMemberCity: City = await this.cityService.getCityById(membershipTransferDetails.destinationCityId)
        const sourceCity = await this.cityService.getCityByCultCityId(transferMembershipResponse.sourceCityId)
        const transferSummeryWidget: MembershipTransferSummeryWidget = {
            widgetType: "MEMBERSHIP_TRANSFER_SUMMERY_WIDGET",
            header: `TRANSFER ${transferMembershipResponse.packName.toUpperCase()}`,
            membershipTransferType: membershipTransferDetails.membershipTransferType,
            transferFrom: {
                title: "FROM",
                location: sourceCity && sourceCity.name
            },
            transferTo: {
                title: "TO",
                location: transferMemberCity.name
            }
        }
        if (membershipTransferDetails.membershipTransferType === "ANOTHER_CITY") {
            transferSummeryWidget["name"] = `${user.firstName} ${user.lastName ? user.lastName : ""}`
            transferSummeryWidget["imageUrl"] = user.profilePictureUrl
        } else {
            transferSummeryWidget.transferFrom["name"] = `${user.firstName} ${user.lastName ? user.lastName : ""}`
            transferSummeryWidget.transferFrom["imageUrl"] = user.profilePictureUrl
            transferSummeryWidget.transferTo["name"] = `${transferMemberDetails.firstName} ${transferMemberDetails.lastName ? transferMemberDetails.lastName : ""}`
            transferSummeryWidget.transferTo["imageUrl"] = transferMemberDetails.profilePictureUrl
        }
        return transferSummeryWidget
    }

    private async transferFeeSummeryWidget(transferMembershipResponse: MembershipTransferDetailResponse, membershipTransferDetails: any, userContext: UserContext): Promise<TransferFeeSummaryWidget> {
        const transferMemberCity: City = await this.cityService.getCityById(membershipTransferDetails.destinationCityId)
        const tz = userContext.userProfile.timezone
        const transferSummeryWidget: TransferFeeSummaryWidget = {
            widgetType: "TRANSFER_FEE_SUMMERY_WIDGET",
            membershipDetails: [{
                title: "Ends on",
                value: transferMembershipResponse.membershipEndDate
            },
            {
                title: "Remaining days",
                value: transferMembershipResponse.remainingMembershipDays
            },
            {
                title: "Pause days",
                value: transferMembershipResponse.remainingPauseDays
            }]
        }
        if (transferMembershipResponse.isMembershipDaysChanged) {
            transferSummeryWidget["membershipDaysReduce"] = {
                title: `Days ${transferMembershipResponse.differenceInDays > 0 ? "added" : "deducted"} for transfer`,
                value: Math.abs(transferMembershipResponse.differenceInDays),
                action: {
                    title: "View details",
                    actionType: "SHOW_MEMBERSHIP_DAYS_REDUCE_MODAL",
                    meta: {
                        header: `Calculation of Remaining Days `,
                        description: `Your current pack costs ${transferMembershipResponse.differenceInDays > 0 ? "less" : " more"} in ${transferMemberCity.name}. The remaining days of your pack will be adjusted to account for the price difference.`,
                        daysDifference: [{
                            title: "Number of days remaining",
                            value: transferMembershipResponse.remainingMembershipDays
                        },
                        {
                            title: "Number of days adjusted",
                            value: transferMembershipResponse.differenceInDays > 0 ? `+${transferMembershipResponse.differenceInDays}` : transferMembershipResponse.differenceInDays
                        }],
                        totalDifference: [{
                            title: "Remaining days transferred",
                            value: transferMembershipResponse.newRemainingMembershipDays
                        }]
                    }
                }
            }
            transferSummeryWidget["remainingMembershipDays"] = [{
                title: "Remaining days after transfer",
                value: transferMembershipResponse.newRemainingMembershipDays
            }]
            const todaysDate = TimeUtil.todaysDate(tz)
            if (transferMembershipResponse.newMembershipStartDate === todaysDate) {
                transferSummeryWidget["remainingMembershipDays"] = [...transferSummeryWidget["remainingMembershipDays"],
                {
                    title: "Membership will end on",
                    value: momentTz.tz(transferMembershipResponse.newMembershipEndDate, tz).format("DD MMM YYYY")
                }]
            }
        }
        if (membershipTransferDetails.membershipTransferType === "ANOTHER_USER") {
            transferSummeryWidget["paymentDetails"] = [{
                title: "Transfer fees",
                value: `${RUPEE_SYMBOL}${transferMembershipResponse.price}`
            },
            {
                title: "Total Payable",
                value: `${RUPEE_SYMBOL}${transferMembershipResponse.price}`
            }]
        } else if (transferMembershipResponse.price > 0) {
            transferSummeryWidget["paymentDetails"] = [
            {
                title: "Total Payable",
                value: `${RUPEE_SYMBOL}${transferMembershipResponse.price}`
            }]
            if (transferMembershipResponse.fees > 0) {
                transferSummeryWidget["paymentDetails"].unshift({
                    title: "Transfer fees",
                    value: `${RUPEE_SYMBOL}${transferMembershipResponse.fees}`
                })
            }
        }
        return transferSummeryWidget
    }

    private async buildCheckoutTimerWidget(userContext: UserContext, order: Order): Promise<IBaseWidget> {
        const queryParams: { [filterName: string]: any } = {}
        queryParams["order"] = order
        const checkoutTimerWidgetId = process.env.ENVIRONMENT === "PRODUCTION" || process.env.ENVIRONMENT === "ALPHA" ? CHECKOUT_TIMER_PROD_WIDGET_ID : CHECKOUT_TIMER_STAGE_WIDGET_ID
        const widget = (await eternalPromise(this.serviceInterfaces.widgetBuilder.buildWidgets([checkoutTimerWidgetId], this.serviceInterfaces, userContext, queryParams, undefined))).obj
        if (widget && !_.isEmpty(widget.widgets) && widget.widgets[0].widgetType === "TIMER_WIDGET_V2") {
            return widget.widgets[0]
        }
    }

    private async getOfferInfoWidget(order: Order): Promise<WidgetView> {
        if (_.isEmpty(order.offersApplied)) return
        const offerV2MapPromise: Promise<{
            [key: string]: OfferV2;
        }> = this.offerServiceV3.getOffersByIds(order.offersApplied).then(o => o.data)
        const offerAddonWidgetParams: IOfferAddonWidgetParams = {
            logger: this.logger,
            offerV2MapPromise: offerV2MapPromise,
            useLabel: "CART_LABEL"
        }
        let offerAddonWidget: OfferAddonWidget = new OfferAddonWidget()
        offerAddonWidget = await offerAddonWidget.buildView(offerAddonWidgetParams)
        if (_.isEmpty(offerAddonWidget)) {
            return undefined
        }
        offerAddonWidget.orientation = "RIGHT"
        offerAddonWidget.containerStyle = {
            paddingLeft: 25
        }
        offerAddonWidget.imageContainerStyle = {
            flex: 0.12
        }
        offerAddonWidget.bulletViewStyle = {
            flex: 0.12
        }
        return offerAddonWidget
    }

    private pointsToNoteWidget(transferToNonCreditToCreditCity?: boolean, currentCity?: string, newCity?: string): WidgetView {
        const items = [
            {
                subTitle: `Credits are required to access all other centers, apart from your home center.`,
                icon: "/image/icons/diff.png"
            },
            {
                subTitle: "Credit amount differs at each center based on the center demand.",
                icon: "/image/icons/chart.png",
            }
        ]
        if (transferToNonCreditToCreditCity) {
            items.push(
                {
                subTitle: "You get credit-based access at other centers in " + (newCity ? newCity : "new city") + " in contrast to 2 session per month access in " + (currentCity ? currentCity : "current city"),
                icon: "/image/icons/diff.png",
                }
            )
        }
        return {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "SMALL",
            header: {
                title: "Points to note",
                style: {
                    fontSize: 18
                },
                color: "#000000"
            },
            items: items,
            hideSepratorLines: true,
            hasDividerBelow: false
        }
    }

    private getCityOfPurchaseWidget(userContext: UserContext, cityId: string) {

        const cityWidget: InfoWidget = {
            widgetType: "INFO_WIDGET",
            icon: "LOCATION",
            title: "City",
            subTitle: cityId
        }
        return cityWidget
    }

    private getCenterOfPurchaseWidgetSelectPack(centerName: string) {
        const centerNameWidget: InfoWidget = {
            widgetType: "INFO_WIDGET",
            icon: "LOCATION",
            title: "Access you'll get to",
            subTitle: centerName
        }
        return centerNameWidget
    }

    private getCenterOfPurchaseWidget(userContext: UserContext, centerName: string) {

        const centerWidget: InfoWidget = {
            widgetType: "INFO_WIDGET",
            icon: "LOCATION",
            title: "Center",
            subTitle: centerName
        }
        return centerWidget
    }

    private async addTransformAddOnProductWidget(userContext: UserContext, response: OrderCheckoutDetail, order: Order): Promise<void> {
        const albusTransformConfig = this.configService.getConfig("ALBUS_TRANSFORM")
        const isTransformAddOnEnabled: boolean = _.get(albusTransformConfig, "configs.isCultEliteTransformAddOnEnabled", false)
        if ((!isTransformAddOnEnabled || !(await CultUtil.isTransformAddOnSupported(userContext, this.hamletBusiness)) || (await AppUtil.doesUserBelongToTransformActiveMembershipSegment(userContext, this.serviceInterfaces.segmentService)))) {
            return
        }
        const addOnProductId: string = _.get(albusTransformConfig, "configs.transformCultEliteAddOnProductId")
        const transformAddOnProduct = <DiagnosticProduct>await this.catalogueService.getProduct(addOnProductId)
        const isAddOnSelected: Boolean = CareUtil.isTransformSecondaryProduct(order.products)
        const addOnProductWidget: AddOnProductWidget = {
            headerTitle: isAddOnSelected ? "ADDED" : "ADD TO MEMBERSHIP",
            title: "Goal-based meal and workout plans with habit coaching",
            mrp: AppUtil.getCurrencySymbol(transformAddOnProduct.price.currency) + transformAddOnProduct.price.mrp,
            packDetail: AppUtil.getCurrencySymbol(transformAddOnProduct.price.currency) + transformAddOnProduct.price.listingPrice + " • " + (transformAddOnProduct.duration / 30).toFixed(0) + " Months",
            offerText: isAddOnSelected ? "Congrats! You saved an additional ₹1500 with this deal" : "Limited offer: 33% off only with cultpass ELITE",
            isAddOnSelected: isAddOnSelected.valueOf(),
            action: {
                title: isAddOnSelected ? "REMOVE" : "+ADD",
                meta: {
                    productData: {
                        productId: addOnProductId,
                        quantity: 1,
                        option: {
                            productId: addOnProductId,
                            useFitcash: true
                        }
                    }
                },
                actionType: "ADD_TO_CART"
            },
            widgetType: "ADD_ON_PRODUCT_WIDGET"
        }

        response.widgets.push(addOnProductWidget)
    }

    public async isOMSCheckoutFlow(userId: string, deviceId: string, product: Product | OrderProduct, flow: "CART-REVIEW" | "CHECKOUT" | "LIVE_IAP", tenant: Tenant): Promise<boolean> {
        const productType = product?.productType
        if (_.isEmpty(userId) || userId === "0" || _.isNil(productType)) {
            return false
        }
        if (tenant === Tenant.SUGARFIT_APP && productType === "DIAGNOSTICS") return true
        if (tenant !== Tenant.CUREFIT_APP && productType !== "GROUP_CLASS") return false
        if (flow === "CART-REVIEW" && !["GYMFIT_FITNESS_PRODUCT", "FITNESS", "LUX_FITNESS_PRODUCT", "BUNDLE", "GYM_PT_PRODUCT", "PLAY"].includes(productType)) {
            this.logger.info(`Directing CART-REVIEW call for ${productType} to Alfred`)
            return false
        }
        switch (productType) {
            case "FOOD":
            case "PLAY":
            case "CF_LIVE":
            case "FITNESS":
            case "GYMFIT_FITNESS_PRODUCT":
            case "GYM_PT_PRODUCT":
            case "GYM_PT_PPC_PRODUCT":
            case "LUX_FITNESS_PRODUCT":
            case "GROUP_CLASS":
                return true
            case "BUNDLE":
                const categoriesToCheck = ["TRANSFORM_PLUS", "BOOTCAMP", "TRANSFORM"]
                const categoryId = product?.categoryId
                if (!categoriesToCheck.includes(categoryId)) return false
                return true
            case "DIAGNOSTICS":
            case "CONSULTATION":
                return true
            default:
                this.logger.debug(`Not allowing ${productType} to checkout through OMS`)
                return false
        }
    }

    private getOrderTimeLineWidget(timeline: FulfillmentTimeline, paymentErrorRes: EnhancedFulfillmentResult): WidgetView {
        const orderTimeWidget: OrderCreationTimelineWidget = new OrderCreationTimelineWidget()
        const formatDate = (dateInString: Date) => {
            const date = new Date(dateInString)
            let hours = date.getHours()
            const minutes = date.getMinutes()
            const ampm = hours >= 12 ? "PM" : "AM"
            hours = hours % 12
            hours = hours ? hours : 12 // the hour '0' should be '12'
            const minStr = minutes < 10 ? "0" + minutes : minutes
            const strTime = hours + ":" + minStr + " " + ampm + " • " + date.toDateString()
            return strTime
        }
        orderTimeWidget.heading = "Status"
        if (timeline.steps != null && timeline.steps.length >= 2) {
            const steps = timeline.steps
            if (steps[0].status === "failed") {
                orderTimeWidget.stepDetails.push({
                    title: "Transaction failed",
                    subTitle: paymentErrorRes?.paymentErrorDetails.errorReason ? paymentErrorRes?.paymentErrorDetails.errorReason : `If any money was deducted, refund will be initiated.`,
                    stepStatus: "FAILURE",
                })
            } else if (steps[0].status === "success") {
                const time = steps[0].completionTime ? ` on ${formatDate(steps[0].completionTime)}` : ""
                orderTimeWidget.stepDetails.push({
                    title: "Payment confirmed",
                    subTitle: `Successful${time}`,
                    stepStatus: "SUCCESS",
                })
                for (let i = steps.length - 1; i > 0; i--) {
                    const stepStatus = steps[i].status
                    if (stepStatus === "failed") {
                        orderTimeWidget.stepDetails.push({
                            title: "Membership creation failed",
                            subTitle: "We’re sorry but this step couldn’t be completed.",
                            stepStatus: "FAILURE",
                        })
                        orderTimeWidget.note = "We couldn't create your membership. We have initiated a full refund and the amount should reflect back to the original source of payment within 5-7 days"
                        break
                    } else if (stepStatus === "success") {
                        if (i === steps.length - 1) {
                            const time = steps[i].completionTime ? ` on ${formatDate(steps[i].completionTime)}` : ""
                            orderTimeWidget.stepDetails.push({
                                title: "Membership created",
                                subTitle: `Completed${time}`,
                                stepStatus: "SUCCESS",
                            })
                        } else {
                            orderTimeWidget.stepDetails.push({
                                title: "Membership creation",
                                subTitle: "Your cultpass membership is almost here!",
                                stepStatus: "STARTED",
                            })
                        }
                        break
                    } else if (stepStatus === "not_started") {
                        if (i === 1) {
                            orderTimeWidget.stepDetails.push({
                                title: "Membership creation",
                                subTitle: "Your cultpass membership is almost here!",
                                stepStatus: "STARTED",
                            })
                            break
                        }
                    } else {
                        orderTimeWidget.stepDetails.push({
                            title: "Membership creation",
                            subTitle: "Your cultpass membership is almost here!",
                            stepStatus: "STARTED",
                        })
                        break
                    }
                }
            } else {
                const time = steps[0].initiationTime ? ` on ${formatDate(steps[0].initiationTime)}` : ""
                orderTimeWidget.stepDetails.push({
                    title: "Payment confirmation pending",
                    subTitle: `Request initiated${time}`,
                    stepStatus: "STARTED",
                })
                orderTimeWidget.stepDetails.push({
                    title: "Membership creation",
                    stepStatus: "PENDING",
                })
            }
        }
        return orderTimeWidget
    }
}

class OrderCreationTimelineWidget implements WidgetView {
    public widgetType: WidgetType
    public heading: string
    public note: string
    public stepDetails: {
        title: string;
        subTitle?: string;
        stepStatus: "PENDING" | "FAILURE" | "SUCCESS" | "STARTED";
    }[]

    constructor() {
        this.widgetType = "ORDER_CREATION_TIMELINE_WIDGET"
        this.stepDetails = []
    }
}

class AppointmentWidget implements WidgetView {
    public widgetType: WidgetType
    public date: string
    public time: string
    public day: String
    constructor(datetime: string, userContext: UserContext) {
        this.widgetType = "APPOINTMENT_WIDGET"
        const tz = userContext.userProfile.timezone
        this.date = TimeUtil.formatDateStringInTimeZone(datetime, tz, "Do MMMM YYYY")
        this.time = TimeUtil.formatDateStringInTimeZone(datetime, tz, "hh:mm A")
        this.day = TimeUtil.formatDateStringInTimeZone(datetime, tz, "dddd")
    }

}

export interface ExtendedPrice extends ProductPrice {
    totalInclusiveAll?: number
}
export interface OrderSummaryWidget extends InfoWidget {
    productId: string
    price: ExtendedPrice
    thumbnailImages: string[]
    magazineImage: string
    priceDetails: PriceComponent[]
    paymentDetail: PaymentDetail
    iconUrl: string
}

export interface PaymentDetailsWidget extends WidgetView {
    title?: string
    finalPrice: ProductPrice
    priceDetails: PriceComponent[]
    orientation?: Orientation
    productType?: ProductType
    noteText?: NoteText
    fulfilledByText?: string
    duration?: string
    suffix?: string
    footer?: string
    maxStartDate?: number
    minStartDate?: number
    expiryDate?: string
    topPaddingNeeded?: boolean
    wrapInGradient?: boolean
    packName?: PackName
    isCollapseOpen?: boolean
    showCollapse?: boolean
}

export interface PackName {
    title?: CFTextData,
    subtitle1?: CFTextData,
    subtitle2?: CFTextData
}


export interface SfExperienceCenterPaymentDetailsWidget extends WidgetView {
    title?: string
    finalPrice: ProductPrice
    priceDetails: PriceComponent[]
    orientation?: Orientation
    productType?: ProductType
    noteText?: NoteText
    fulfilledByText?: string
    duration?: string
    suffix?: string
    footer?: string
    maxStartDate?: number
    minStartDate?: number
    expiryDate?: string
}

export interface NoteText {
    iconType?: string,
    title?: string
}
export interface ManagedPlanPaymentWidget extends WidgetView {
    header: Header
    footer: PriceValue
    items: PriceValue[]
}

export interface PriceValue {
    title: string
    price: ProductPrice
    items?: PriceValue[]
}

class AddressWidget implements WidgetView {
    public widgetType: WidgetType
    public address: UserAddress
    public userAddressView: UserAddressView
    public icon?: string
    constructor(userDeliveryAddress: UserDeliveryAddress, icon?: string) {
        this.widgetType = "ADDRESS"
        this.address = this.toOldAddressFormat(userDeliveryAddress)
        this.userAddressView = new UserAddressView(userDeliveryAddress)
    }

    toOldAddressFormat(userDeliveryAddress: UserDeliveryAddress): UserAddress {
        const userAddress: UserAddress = {
            addressId: userDeliveryAddress.addressId,
            address: _.filter([userDeliveryAddress.addressLine1, userDeliveryAddress.addressLine2]).join(","),
            userId: userDeliveryAddress.userId,
            gateId: userDeliveryAddress.gateId,
            userName: userDeliveryAddress.name,
            phoneNumber: userDeliveryAddress.phoneNumber,
            name: capitalizeFirstLetter(userDeliveryAddress.addressType.toLowerCase()),
            lastUsed: new Date(),
        }
        return userAddress
    }
}


abstract class BaseOrderSummaryWidget implements WidgetView {
    public widgetType: WidgetType
    public title: string
    public subTitle: string
    public date?: string
    public image: string
    public price: ProductPrice
    public taxDetail: {
        type: string,
        amount: number
        percentage: number
    }
    public mrp: number
    public discount?: number
    public amountPayable: number
    public icon?: string

    constructor(product: Product, billingInfo: BillingInfo) {
        this.title = product.title
        this.price = product.price
        this.taxDetail = billingInfo.tax
        this.mrp = billingInfo.mrp
        this.discount = billingInfo.discount
        this.amountPayable = billingInfo.total

    }
}
class FitnessOrderSummaryWidget extends BaseOrderSummaryWidget {
    constructor(product: Product, billingInfo: BillingInfo, cultClass: CultClass, userContext: UserContext) {
        super(product, billingInfo)
        this.widgetType = "FITNESS_ORDER_SUMMARY"
        this.title = `${cultClass.Workout.name} at ${cultClass.Center.name}`
        const tz = userContext.userProfile.timezone
        this.subTitle = TimeUtil.formatDateStringInTimeZone(cultClass.date, tz, "Do MMM, YYYY")
        const startTime = TimeUtil.formatDateStringInTimeZone(cultClass.date + " " + cultClass.startTime, tz, "hh:mm")
        const endTime = TimeUtil.formatDateStringInTimeZone(cultClass.date + " " + cultClass.endTime, tz, "hh:mm A")
        this.date = `${startTime} - ${endTime}`

    }
}
class FitnessEventOrderSummaryWidget extends BaseOrderSummaryWidget {
    constructor(userContext: UserContext, product: Product, billingInfo: BillingInfo, cultEvent: FitnessEvent) {
        super(product, billingInfo)
        const tz = userContext.userProfile.timezone
        const formatDate = TimeUtil.formatDateStringInTimeZone(cultEvent.launchDate, tz, "Do MMM, YYYY")
        const formatTime = _.isNil(cultEvent.startTime) ? null : TimeUtil.formatDateStringInTimeZone(cultEvent.launchDate + " " + cultEvent.startTime, tz, "hh:mm")
        this.widgetType = "FITNESS_ORDER_SUMMARY"
        this.title = `${cultEvent.name}`
        this.subTitle = formatDate
        if (!_.isNil(formatTime))
            this.date = `${formatDate} - ${formatTime}`
        else
            this.date = `${formatDate}`
    }
}

class FitnessPackOrderSummaryWidget extends BaseOrderSummaryWidget {
    constructor(userContext: UserContext, product: Product, billingInfo: BillingInfo, orderProduct: OrderProduct) {
        super(product, billingInfo)
        this.widgetType = "ORDER_FITNESS_PACK_SUMMARY"
        this.subTitle = CultUtil.getDescriptionFromProductId(product.productId, product.productType)
        this.date = `From ${TimeUtil.formatDateStringInTimeZone(orderProduct.option.startDate, userContext.userProfile.timezone, "Do MMM, YYYY")}`
    }
}

class CultOfferOrderSummaryWidget extends BaseOrderSummaryWidget {
    constructor(product: Product, billingInfo: BillingInfo, orderProduct: OrderProduct) {
        super(product, billingInfo)
        this.widgetType = "ORDER_FITNESS_PACK_SUMMARY"
        this.subTitle = product.subTitle
        this.date = `Fitness PreRegistration`
    }
}


class FoodPackOrderSummaryWidget extends BaseOrderSummaryWidget {
    constructor(userContext: UserContext, foodPack: FoodPack, billingInfo: BillingInfo, orderPoduct: OrderProduct) {
        super(foodPack, billingInfo)
        this.widgetType = "ORDER_FOOD_PACK_SUMMARY"
        this.numTickets = orderPoduct.option.numTickets
        this.numDays = orderPoduct.option.numDays
        this.subTitle = `${pad(orderPoduct.option.numTickets, 2)} weekdays ${foodPack.mealSlot.toLowerCase()} pack`
        this.date = this.getDeliveryDateRange(userContext, orderPoduct.option.startDate, orderPoduct.option.numDays)
        this.mealType = foodPack.mealSlot.toLowerCase()
    }

    private getDeliveryDateRange(userContext: UserContext, startDate: string, numDays: number): string {
        const tz = userContext.userProfile.timezone
        return TimeUtil.formatDateStringInTimeZone(startDate, tz, "Do MMM") + " to "
            + TimeUtil.formatDateStringInTimeZone(TimeUtil.getDaysFrom(tz, startDate, numDays)[numDays - 1], tz, "Do MMM, YYYY")
    }

    public numTickets: number
    public numDays: number
    public mealType: string
}

class FoodOrderSummaryWidget extends BaseOrderSummaryWidget {
    constructor(userContext: UserContext, product: Product, billingInfo: BillingInfo, orderProduct: OrderProduct) {
        super(product, billingInfo)
        this.widgetType = "ORDER_FOOD_SUMMARY"
        this.isVeg = product.attributes.isVeg
        this.subTitle = `${product.attributes.nutritionInfo.Calories["Total Calories"]} Calories`
        this.date = TimeUtil.formatDateStringInTimeZone(orderProduct.option.startDate, userContext.userProfile.timezone, "Do MMM, YYYY")
    }

    public isVeg: boolean
    public calories: number
}

export default OrderViewBuilder
