import { Action } from "@curefit/vm-models"
import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { ICatalogueService, CATALOG_CLIENT_TYPES, CatalogueServiceV2Utilities, ICatalogServiceV3Reader, ICatalogueServicePMS } from "@curefit/catalog-client"
import { IShipmentService, ALFRED_CLIENT_TYPES } from "@curefit/alfred-client"
import { IFulfilmentService } from "@curefit/alfred-client"
import { ICultServiceOld as ICultService, CULT_CLIENT_TYPES } from "@curefit/cult-client"
import { BaseOrder, Order, OrderProduct, isMembershipUpgradeClientMetadata } from "@curefit/order-common"
import { TimeUtil } from "@curefit/util-common"
import { UrlPathBuilder } from "@curefit/product-common"
import { ConsultationProduct, DiagnosticProduct, HealthfaceTenant } from "@curefit/care-common"
import { FoodProduct as Product } from "@curefit/eat-common"
import { ProgramPackProduct } from "@curefit/product-common"
import { ActionUtil, SeoUrlParams } from "@curefit/base-utils"
import { CatalogueServiceUtilities } from "../util/CatalogueServiceUtilities"
import CultUtil, { DEFAULT_IMAGE_URL, PRO_IMAGE_URL, SELECT_IMAGE_URL, isTransferMembershipOrder, isUpgradeMembershipOrder } from "../util/CultUtil"
import * as _ from "lodash"
import { IProgramService, PROGRAM_CLIENT_TYPES } from "@curefit/program-client"
import { OrderUtil } from "@curefit/base-utils"
import IssueBusiness, { IssueDetailView } from "../crm/IssueBusiness"
import { IHealthfaceService, ALBUS_CLIENT_TYPES } from "@curefit/albus-client"
import { CareUtil } from "../util/CareUtil"
import { UserContext } from "@curefit/userinfo-common"
import { GearService, GEARVAULT_CLIENT_TYPES } from "@curefit/gearvault-client"
import { IssueProductState } from "@curefit/issue-common"
import AppUtil from "../util/AppUtil"
import {
    GymfitAccessLevel,
    GymPtPpcProduct,
    GymPtProduct, SubAccessLevel,
    SubLevelAccessListing
} from "@curefit/gymfit-common"
import { PageTypes } from "@curefit/apps-common"
import { GearImage, GearOrderDetailObject } from "@curefit/gear-common"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService } from "@curefit/diy-client"
import LiveUtil from "../util/LiveUtil"
import EatUtil from "../util/EatUtil"
import { BLACKLISTED_ISSUES_FOR_WEB } from "../gear/constants"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { GYMFIT_CLIENT_TYPES, IGymfitService } from "@curefit/gymfit-client"
import GymfitUtil from "../util/GymfitUtil"
import FoodMarketplaceUtil from "../util/FoodMarketplaceUtil"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import { CULT_BIKE_TYPES, ICultBikeService } from "@curefit/cult-bike-client"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { OMS_API_CLIENT_TYPES, IOrderService } from "@curefit/oms-api-client"
import { AccessLevel } from "@curefit/cult-common"
import PlayUtil from "../util/PlayUtil"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"

export type OrderProductState = "UPCOMING" | "ONGOING"
interface BaseDetail {
    title: string
    thumbnailImage?: string
    thumbnailImages?: string[]
    magazineImage?: string
    action?: string
    subTitle?: string
    state?: OrderProductState
}

export interface ItemDetail extends BaseDetail {
    isVeg?: boolean
    quantity?: number
}
export interface OrderProductDetail extends BaseDetail {
    reportIssues: IssueDetailView[]
    itemDetails?: ItemDetail[]
    pageActionTitle?: string
    seeDetailAction?: Action
    pageActions?: Action[]
}

@injectable()
class OrderProductDetailBuilder {

    constructor(
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceV3Reader) private catalogServiceV3: ICatalogServiceV3Reader,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReaderPMS) private catalogueServicePMS: ICatalogueServicePMS,
        @inject(ALFRED_CLIENT_TYPES.ShipmentService) private shipmentService: IShipmentService,
        @inject(ALFRED_CLIENT_TYPES.FulfilmentService) private fulfilmentService: IFulfilmentService,
        @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
        @inject(CULT_CLIENT_TYPES.MindFitService) private mindFitService: ICultService,
        @inject(PROGRAM_CLIENT_TYPES.ProgramService) private programService: IProgramService,
        @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness,
        @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
        @inject(GEARVAULT_CLIENT_TYPES.GearService) private gearService: GearService,
        @inject(OMS_API_CLIENT_TYPES.OrderService) private omsApiClient: IOrderService,
        @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) private diyFulfilmentService: IDIYFulfilmentService,
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(ERROR_COMMON_TYPES.RollbarService) public rollbarService: RollbarService,
        @inject(GYMFIT_CLIENT_TYPES.GymfitService) protected gymfitService: IGymfitService,
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) public membershipService: IMembershipService,
        @inject(CULT_BIKE_TYPES.CultBikeService) public cultBikeClient: ICultBikeService,
        @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness,
    ) {
    }

    private async getProgramPackDetail(userContext: UserContext, product: Product, orderId: string, userId: string): Promise<OrderProductDetail> {
        const magazineImage = UrlPathBuilder.getPackImagePath(product.productId, "PROGRAM", "MAGAZINE", product.imageVersion, userContext.sessionInfo.userAgent)
        const membershipsPromise = this.programService.activeMemberships(userId, orderId, undefined, userContext.userProfile.timezone)
        const productPromise = this.catalogueService.getProduct(product.productId)
        const memberships = await membershipsPromise
        const packProduct = <ProgramPackProduct>await productPromise

        const orderProductDetail: OrderProductDetail = {
            title: product.title,
            magazineImage: magazineImage,
            reportIssues: undefined
        }

        if (!_.isEmpty(memberships)) {
            const membership = memberships[0]
            const centerProductType = packProduct.tenantId === "mind.fit" ? "MIND" : "FITNESS"
            orderProductDetail.action = ActionUtil.programPack(packProduct.packId, centerProductType, membership.id)
            if (membership.startAt > new Date().getTime()) {
                orderProductDetail.state = "UPCOMING"
            } else {
                orderProductDetail.state = "ONGOING"
            }
        }
        return orderProductDetail
    }

    private async getFitnessFirstPackDetail(userContext: UserContext, product: Product, orderId: string): Promise<OrderProductDetail> {
        const fitnessPack = await this.catalogueService.getFitnessFirstPack(product.productId)
        const magazineImage = UrlPathBuilder.getPackImagePath(product.productId, "FITNESS_FIRST_PACK", "MAGAZINE", fitnessPack.imageVersion, userContext.sessionInfo.userAgent)
        const orderProductDetail: OrderProductDetail = {
            title: product.title,
            magazineImage: magazineImage,
            reportIssues: undefined
        }
        return orderProductDetail
    }

    private async getGymfitFitnessPackDetail(userContext: UserContext, product: Product, orderId: string): Promise<OrderProductDetail> {
        let magazineImage: string = undefined
        if (product && product.option && product.option.centerId) {
            const gymfitCenter = await this.catalogueService.getGymfitCenterById(product.option.centerId)
            magazineImage = AppUtil.isDesktop(userContext) ? (gymfitCenter.imageUrls && gymfitCenter.imageUrls.clpWebImageUrls ? gymfitCenter.imageUrls.clpWebImageUrls[0] : undefined) :
                (gymfitCenter.imageUrls && gymfitCenter.imageUrls.clpImageUrls ? gymfitCenter.imageUrls.clpImageUrls[0] : undefined)
        }
        const fulfilment = await this.fulfilmentService.getGymfitFitnessFulfilmentByOrderId(orderId)
        let reportIssues: IssueDetailView[]

        if (!_.isNil(fulfilment)) {
            reportIssues = await this.issueBusiness.getGymfitFitnessPackIssues(fulfilment, userContext)
        }

        const orderProductDetail: OrderProductDetail = {
            title: product.title,
            magazineImage: magazineImage,
            reportIssues: reportIssues
        }

        return orderProductDetail
    }

    private async getGymfitFitnessProductDetail(userContext: UserContext, product: Product, orderId: string): Promise<OrderProductDetail> {
        const fulfilment = await this.fulfilmentService.getGymfitFitnessFulfilmentByOrderId(orderId)
        let reportIssues: IssueDetailView[]
        let actionUrl = ""
        if (fulfilment) {
            let issueProductState: IssueProductState = null
            if (fulfilment.status === "CANCELLED") {
                issueProductState = "CANCELLATION"
            } else if (fulfilment.status === "CREATED") {
                issueProductState = "AFTER_PURCHASE"
            } else {
                issueProductState = "BEFORE_PURCHASE"
            }
            reportIssues = await this.issueBusiness.getGymfitFitnessProductIssues(userContext, issueProductState, orderId)
            const resp = await this.membershipService.filter({orderId})
            const goldMemberships = resp.elements
            actionUrl = goldMemberships[0] ? await GymfitUtil.getGoldMembershipDetailsUrl(goldMemberships[0], userContext) : ""
        }

        const gymfitProduct = await this.catalogueServicePMS.getProduct(product.productId)
        const magazineImage = CatalogueServiceUtilities.getAccessLevel(gymfitProduct) === GymfitAccessLevel.CENTER ? SELECT_IMAGE_URL : PRO_IMAGE_URL
        const orderProductDetail: OrderProductDetail = {
            title: product.title,
            magazineImage: magazineImage,
            reportIssues: reportIssues,
            action: actionUrl
        }

        return orderProductDetail
    }

    private async getLuxFitnessProductDetail(userContext: UserContext, product: Product, orderId: string): Promise<OrderProductDetail> {
        const fulfilment = await this.fulfilmentService.getGymfitFitnessFulfilmentByOrderId(orderId)
        const reportIssues: IssueDetailView[] = []
        let actionUrl = ""
        if (fulfilment) {
            let issueProductState: IssueProductState = null
            if (fulfilment.status === "CANCELLED") {
                issueProductState = "CANCELLATION"
            } else if (fulfilment.status === "CREATED") {
                issueProductState = "AFTER_PURCHASE"
            } else {
                issueProductState = "BEFORE_PURCHASE"
            }
            // reportIssues = await this.issueBusiness.getGymfitFitnessProductIssues(userContext, issueProductState, orderId) TODO - check this
            const resp = await this.membershipService.filter({orderId})
            const luxMemberships = resp.elements
            actionUrl = luxMemberships[0] ? await GymfitUtil.getLuxMembershipDetailsUrl(luxMemberships[0], userContext) : ""
        }
        const luxPack = product as unknown as OfflineFitnessPack
        const orderProductDetail: OrderProductDetail = {
            title: luxPack.displayName ?? product.title,
            magazineImage: GymfitUtil.getDefaultLuxMagazineImage(),
            reportIssues: reportIssues,
            action: actionUrl
        }

        return orderProductDetail
    }

    private async getAddOnProductDetail(userContext: UserContext, product: Product, orderId: string): Promise<OrderProductDetail> {
        const orderProductDetail: OrderProductDetail = {
            title: product.title,
            magazineImage: DEFAULT_IMAGE_URL,
            reportIssues: undefined,
            action: undefined
        }

        return orderProductDetail
    }

    private async getRegistrationProductDetail(product: Product): Promise<OrderProductDetail> {
        const orderProductDetail: OrderProductDetail = {
            title: product.title,
            magazineImage: DEFAULT_IMAGE_URL,
            reportIssues: undefined,
            action: undefined
        }
        return orderProductDetail
    }

    private async getGymPtProductDetail(userContext: UserContext, product: Product, orderId: string): Promise<OrderProductDetail> {
        const gymPtProduct: GymPtProduct = await this.catalogueService.getGymPtProductById(product.productId)

        const orderProductDetail: OrderProductDetail = {
            title: gymPtProduct.title,
            thumbnailImages: gymPtProduct.imageUrls.length > 0 ? [product.imageUrls[0]] : [],
            thumbnailImage: gymPtProduct.imageUrls.length > 0 ? product.imageUrls[0] : null,
            reportIssues: undefined,
            action: null
        }

        return orderProductDetail
    }

    private async getGymPtPpcProductDetail(userContext: UserContext, product: Product, orderId: string): Promise<OrderProductDetail> {
        const gymPtPpcProduct: GymPtPpcProduct = await this.catalogueService.getGymPtPpcProductById(product.productId)

        const orderProductDetail: OrderProductDetail = {
            title: gymPtPpcProduct.title,
            thumbnailImages: gymPtPpcProduct.imageUrls.length > 0 ? [product.imageUrls[0]] : [],
            thumbnailImage: gymPtPpcProduct.imageUrls.length > 0 ? product.imageUrls[0] : null,
            reportIssues: undefined,
            action: null
        }

        return orderProductDetail
    }


    private async getCultFitPackDetail(userContext: UserContext, product: Product, orderId: string): Promise<OrderProductDetail> {
        const today = TimeUtil.todaysDateWithTimezone(userContext.userProfile.timezone)
        const fitnessFulfilment = await this.fulfilmentService.getFitnessFulfilmentByOrderId(orderId)
        const fitnessPack = await this.catalogueServicePMS.getProduct(product.productId)
        const magazineImage = CatalogueServiceUtilities.getAccessLevel(fitnessPack) == AccessLevel.CENTER ? SELECT_IMAGE_URL : DEFAULT_IMAGE_URL
        let state: OrderProductState
        let action = ""
        let reportIssues: IssueDetailView[]
        // Temp hack as for gift pack cultMembershipId is not present
        if (fitnessFulfilment && fitnessFulfilment.cultMembershipId) {
            const membership = await this.cultFitService.getMembershipById(parseInt(fitnessFulfilment.cultMembershipId), fitnessFulfilment.userId)
            if (membership.startDate > today) {
                state = "UPCOMING"
            } else if (membership.startDate <= today && membership.endDate <= today) {
                state = "ONGOING"
            }
            reportIssues = await this.issueBusiness.getCultOrMindSubscriptionIssues(userContext, membership, product.productType)
            const membershipId = await CultUtil.getMembershipIdByCultMembershipId(userContext, membership.id.toString(), this.membershipService)
            if (AppUtil.isMembershipDetailV2PageSupported(userContext)) {
                action = AppUtil.getNewMembershipDetailPage(membershipId, false)
            } else if (CatalogueServiceUtilities.getAccessLevel(fitnessPack) !== AccessLevel.CENTER) {
                action = _.isEmpty(membershipId) ? CatalogueServiceUtilities.getPackPageAction(fitnessPack) : await CatalogueServiceUtilities.getCultMembershipDetailsPageAction(membershipId, userContext)
            }
        }

        const orderProductDetail: OrderProductDetail = {
            title: product.title,
            magazineImage: magazineImage,
            action: action,
            state: state,
            reportIssues: reportIssues
        }
        return orderProductDetail
    }

    private async getPlayFitPackDetail(userContext: UserContext, product: Product, orderId: string, isUpgrade: boolean): Promise<OrderProductDetail> {
        let imageVersion = product.imageVersion
        let subLevelAccessListings: SubLevelAccessListing[] = null
        if (!isUpgrade) {
            const fitnessPack = await this.catalogueService.getPlayPack(product.productId)
            imageVersion = fitnessPack.imageVersion
            subLevelAccessListings = fitnessPack.subLevelAccessListings
        }
        let magazineImage = PlayUtil.getMagazineImage(userContext.sessionInfo.userAgent)
        // Temp hack as for gift pack cultMembershipId is not present
        if (subLevelAccessListings != null && subLevelAccessListings?.length > 0) {
            for (const subAccessLevel of subLevelAccessListings) {
                if (subAccessLevel?.subAccessLevel == SubAccessLevel.ACTIVITY && subAccessLevel?.subAccessLevelId != null) {
                    magazineImage = PlayUtil.getUpgradePlayIcon(subAccessLevel.subAccessLevelId)
                    break
                }
            }
        }

        const orderProductDetail: OrderProductDetail = {
            title: product.title,
            magazineImage: magazineImage,
            reportIssues: undefined // TODO: Add reportissues for play orders.
        }
        const userMemberships = await this.membershipService.getCachedMembershipsForUser(userContext?.userProfile?.userId, AppUtil.getTenantFromUserContext(userContext), ["PLAY"], ["PURCHASED", "PAUSED"])
        const playMembership = userMemberships?.filter(i => i.orderId?.toString() === orderId)
        if (playMembership?.length && playMembership[0].id) {
            orderProductDetail.action = await PlayUtil.getPlayMembershipDetailsUrl(userContext, playMembership[0])
        }
        return orderProductDetail
    }

    private async getLivePackDetail(userContext: UserContext, product: Product, orderId: string): Promise<OrderProductDetail> {
        const fitnessFulfilment = await this.fulfilmentService.getFitnessFulfilmentByOrderId(orderId)
        // let action = ""
        let reportIssues: IssueDetailView[]
        // Temp hack as for gift pack cultMembershipId is not present
        // if (fitnessFulfilment && fitnessFulfilment.fulfilmentId) {
        //     const membership = await this.diyFulfilmentService.getMembershipById(parseInt(fitnessFulfilment.fulfilmentId))
        //     const membershipState = LiveUtil.getLiveFitMembershipState(membership, userContext.userProfile.timezone)
        //     if (membershipState === "UPCOMING") {
        //         state = "UPCOMING"
        //     }
        //     else {
        //         state = "ONGOING"
        //     }
        //     // action = ActionUtil.cultFitPack(livePack.cultPackId.toString(), membership.id.toString(), false, undefined, undefined, undefined, isSubscriptionPack)
        // }
        reportIssues = await this.issueBusiness.getDigitalOrderIssues(userContext, orderId)

        const orderProductDetail: OrderProductDetail = {
            title: product.title,
            magazineImage: !_.isEmpty(product.imageUrl) ? product.imageUrl : LiveUtil.getDefaultLivePackUrl(userContext),
            action: undefined,
            reportIssues: reportIssues
        }
        return orderProductDetail
    }

    private async getGearOrderDetail(product: Product, orderId: string, userContext: UserContext): Promise<OrderProductDetail> {
        const gearOrder: GearOrderDetailObject = await this.gearService.getOrderWithShipmentStatus(orderId)
        const reportIssueParams = this.issueBusiness.getGearIssueParams(gearOrder)
        let reportIssues = await this.issueBusiness.getIssues(reportIssueParams.productType, reportIssueParams.productStates, reportIssueParams.meta, userContext)

        const shipmentId = gearOrder.shipments && gearOrder.shipments.length === 1 ? gearOrder.shipments[0].id : ""
        // block few issues on web since there is no order landing page for gear.
        if (userContext.sessionInfo.userAgent === "DESKTOP" || userContext.sessionInfo.userAgent === "MBROWSER") {
            reportIssues = reportIssues.filter(reportIssue => !BLACKLISTED_ISSUES_FOR_WEB.includes(reportIssue.issueId))
        }
        const reportIssuesWithOrderId = reportIssues.map(reportIssue => {
        // Replace the placeholder with actual orderId if any
        const {action: { actionType, url = ""}} = reportIssue
            if (actionType === "NAVIGATION" || actionType === "WEB_LINK") {
                const dynamicUrl = url.replace("$ORDER_ID", orderId).replace("$SHIPMENT_ID", `${shipmentId}`)
                return {
                    ...reportIssue,
                    action: {
                        ...reportIssue.action,
                        url: dynamicUrl
                    }
                }
            }
            return reportIssue
        })
        const lineItem = gearOrder.line_items[0]
        const images: GearImage = _.isArray(lineItem.variant.images) ? _.first(lineItem.variant.images) : lineItem.variant.images

        const orderProductDetail: OrderProductDetail = {
            title: product.title,
            reportIssues: reportIssuesWithOrderId,
            magazineImage: images.mini_url
        }
        return orderProductDetail
    }


    private async getMindFitPackDetail(userContext: UserContext, product: Product, orderId: string): Promise<OrderProductDetail> {
        const today = TimeUtil.todaysDateWithTimezone(userContext.userProfile.timezone)
        let action = ""
        let state: OrderProductState
        const fitnessFulfilment = await this.fulfilmentService.getFitnessFulfilmentByOrderId(orderId)
        const mindPack = await this.catalogueService.getMindPack(product.productId)
        const magazineImage = UrlPathBuilder.getPackImagePath(product.productId, "MIND", "MAGAZINE", mindPack.imageVersion, userContext.sessionInfo.userAgent)
        let reportIssues: IssueDetailView[]
        if (fitnessFulfilment && fitnessFulfilment.cultMembershipId) {
            const membership = await this.mindFitService.getMembershipById(parseInt(fitnessFulfilment.cultMembershipId), fitnessFulfilment.userId)
            if (membership.startDate > today) {
                state = "UPCOMING"
            } else if (membership.startDate <= today && membership.endDate <= today) {
                state = "ONGOING"
            }
            const isSubscriptionPack = !_.isEmpty(mindPack.subscriptionPack)
            action = AppUtil.getNewPackPageActionUrl(ActionUtil.mindFitPack(mindPack.cultPackId.toString(), membership.id.toString(), false, undefined, undefined, undefined, isSubscriptionPack))

            reportIssues = await this.issueBusiness.getCultOrMindSubscriptionIssues(userContext, membership, product.productType)
        }
        const orderProductDetail: OrderProductDetail = {
            title: product.title,
            magazineImage: magazineImage,
            action: action,
            state: state,
            reportIssues: reportIssues
        }
        return orderProductDetail
    }

    private async getFoodPackDetail(userContext: UserContext, product: Product, order: Order): Promise<OrderProductDetail> {
        let packId = product.productId

        if (order && OrderUtil.isSubOrderId(order.orderId)) {
            const parentOrderId = OrderUtil.getParentOrderId(order.orderId)
            const parentOrder = await this.omsApiClient.getOrder(parentOrderId)
            packId = parentOrder.productSnapshots[0].productId
        } else {
        }
        const tz = userContext.userProfile.timezone
        const foodPack = await this.catalogueService.getFoodPack(packId)
        const magazineImage = UrlPathBuilder.getPackImagePath(product.productId, "FOOD", "MAGAZINE", foodPack.imageVersion, userContext.sessionInfo.userAgent)
        const orderProductDetail: OrderProductDetail = {
            title: product.title,
            magazineImage: magazineImage,
            action: "",
            reportIssues: []
        }
        return orderProductDetail
    }

    private async getPackUpgradeOrderDetail(product: Product, orderId: string, userContext: UserContext): Promise<OrderProductDetail> {
        const orderProductDetail: OrderProductDetail = {
            title: product.title,
            reportIssues: [],
            magazineImage: "/image/packs/upgrade/upgrade-pack%402x.png",
        }
        return orderProductDetail
    }

    private async getPackTransferOrderDetail(product: Product, orderId: string, userContext: UserContext): Promise<OrderProductDetail> {
        const orderProductDetail: OrderProductDetail = {
            title: product.title,
            reportIssues: [],
            magazineImage: DEFAULT_IMAGE_URL,
        }
        return orderProductDetail
    }

    private async getCultFitBookingDetail(product: Product, orderId: string, userContext: UserContext): Promise<OrderProductDetail> {
        const fitnessShipment = await this.shipmentService.getFitnessShipmentByOrderId(orderId)
        let cultFitBooking = undefined
        let reportIssues: IssueDetailView[]

        if (fitnessShipment) {
            cultFitBooking = await this.cultFitService.getBookingById(fitnessShipment.cultBookingId, fitnessShipment.userId)
            const reportIssueParams = this.issueBusiness.getCultOrMindCenterBookingIssueParams(cultFitBooking, product.productType)
            reportIssues = await this.issueBusiness.getIssues(reportIssueParams.productType, reportIssueParams.productStates, reportIssueParams.meta, userContext)
        }
        const orderProductDetail: OrderProductDetail = {
            reportIssues: reportIssues,
            title: product.title,
            thumbnailImages: fitnessShipment ? [CatalogueServiceUtilities.getWorkoutImageByProductId(product.productId)] : undefined,
            action: cultFitBooking ? ActionUtil.cultFitBooking(cultFitBooking.bookingNumber) : undefined
        }
        return orderProductDetail
    }

    private async getCultFitEventDetail(product: Product, orderId: string): Promise<OrderProductDetail> {

        const cultEvent = await this.cultFitService.getFitnessEvent(product.option.fitnessEventId)
        const thumbnailimagedoc = cultEvent.documents.find((x) => { if (x.tagName === "PRODUCT_BNR") return true; return false })
        const orderProductDetail: OrderProductDetail = {
            reportIssues: undefined,
            title: product.title,
            thumbnailImages: !_.isEmpty(thumbnailimagedoc) ? [thumbnailimagedoc.URL] : null,
        }
        return orderProductDetail
    }
    private async getMindFitEventDetail(product: Product, orderId: string): Promise<OrderProductDetail> {

        const cultEvent = await this.mindFitService.getFitnessEvent(product.option.fitnessEventId)
        const thumbnailimagedoc = cultEvent.documents.find((x) => { if (x.tagName === "PRODUCT_BNR") return true; return false })
        const orderProductDetail: OrderProductDetail = {
            reportIssues: undefined,
            title: product.title,
            thumbnailImages: !_.isEmpty(thumbnailimagedoc) ? [thumbnailimagedoc.URL] : null,
        }
        return orderProductDetail
    }

    private async getMindFitBookingDetail(product: Product, orderId: string, userContext: UserContext): Promise<OrderProductDetail> {
        const fitnessShipment = await this.shipmentService.getFitnessShipmentByOrderId(orderId)
        let reportIssues: IssueDetailView[]
        let mindFitBooking = undefined

        if (fitnessShipment) {
            mindFitBooking = await this.mindFitService.getBookingById(fitnessShipment.cultBookingId, fitnessShipment.userId)
            const reportIssueParams = this.issueBusiness.getCultOrMindCenterBookingIssueParams(mindFitBooking, product.productType)
            reportIssues = await this.issueBusiness.getIssues(reportIssueParams.productType, reportIssueParams.productStates, reportIssueParams.meta, userContext)
        }
        const orderProductDetail: OrderProductDetail = {
            title: product.title,
            reportIssues: reportIssues,
            thumbnailImages: fitnessShipment ? [CatalogueServiceUtilities.getWorkoutImageByProductId(product.productId)] : undefined,
            action: mindFitBooking ? ActionUtil.mindFitBooking(mindFitBooking.bookingNumber) : undefined
        }
        return orderProductDetail
    }

    private async getFitclubMembershipDetail(userContext: UserContext, order: BaseOrder): Promise<OrderProductDetail> {
        const product: Product = order.productSnapshots[0]
        const thumbnailImage: string = UrlPathBuilder.getPackImagePath(product.productId, "FIT_CLUB_MEMBERSHIP", "THUMBNAIL", product.imageVersion, "APP")
        const orderProductDetail: OrderProductDetail = {
            title: product.title,
            reportIssues: undefined,
            thumbnailImage: thumbnailImage,
            thumbnailImages: [thumbnailImage]
        }
        return orderProductDetail
    }

    private async getFoodSingleDetail(userContext: UserContext, order: Order, orderId: string, date: string): Promise<OrderProductDetail> {
        const tz = userContext.userProfile.timezone
        const today = TimeUtil.parseDate(TimeUtil.todaysDateWithTimezone(tz), tz)
        let foodFulfilment = undefined
        if (OrderUtil.isSubOrderId(orderId)) {
            const parentOrderId = OrderUtil.getParentOrderId(orderId)
            foodFulfilment = await this.fulfilmentService.getFoodFulfilmentByOrderId(parentOrderId)
        } else {
            foodFulfilment = await this.fulfilmentService.getFoodFulfilmentByOrderId(orderId)
        }
        let thumbnailImage
        const thumbnailImages: string[] = []
        const numProducts = this.numberOfProducts(order)
        const itemDetails: ItemDetail[] = []
        if (numProducts > 1) {
            for (let i = 0; i < order.productSnapshots.length; i++) {
                const foodProduct = order.productSnapshots[i]
                if (_.isNil(foodProduct)) {
                    this.logger.error(`Invalid foodProduct in order snapshots for orderId: ${orderId}`)
                    this.rollbarService.sendError(new Error("Invalid foodProduct in order snapshots"), {
                        extra: {
                            orderId,
                        }
                    })
                    continue
                }
                const orderProduct = _.find(order.products, orderProduct => {
                    if (_.isNil(orderProduct?.productId)) {
                        this.logger.error(`Invalid foodProduct in order: ${orderId} in ${JSON.stringify(orderProduct)}`)
                        this.rollbarService.sendError( new Error("Invalid foodProduct in order products"), {
                            extra: {
                                orderId,
                            }
                        })
                        return false
                    }
                    return orderProduct.productId === foodProduct.productId
                })
                const seoParams: SeoUrlParams = {
                    productName: foodProduct.title
                }
                itemDetails.push({
                    title: foodProduct.title,
                    thumbnailImage: UrlPathBuilder.getSingleImagePath(foodProduct.parentProductId ? foodProduct.parentProductId : foodProduct.productId, "FOOD", "THUMBNAIL", foodProduct.imageVersion),
                    isVeg: foodProduct.attributes && foodProduct.attributes.isVeg === "TRUE",
                    quantity: orderProduct.quantity,
                    action: ActionUtil.foodSingle(foodProduct.productId, TimeUtil.todaysDateWithTimezone(tz), undefined, undefined, undefined, userContext.sessionInfo.userAgent, seoParams)
                })
                if (order.products[i].productType === "FIT_CLUB_MEMBERSHIP") {
                    thumbnailImages.push(UrlPathBuilder.getPackImagePath(order.products[i].productId, "FIT_CLUB_MEMBERSHIP", "THUMBNAIL", foodProduct.imageVersion, "APP"))
                } else {
                    thumbnailImages.push(UrlPathBuilder.getSingleImagePath(EatUtil.getProductId(foodProduct), "FOOD", "THUMBNAIL", foodProduct.imageVersion))
                }
            }
        } else {
            const foodProduct = order.productSnapshots[0]
            if (_.isNil(foodProduct)) {
                this.logger.error(`Invalid foodProduct in order snapshots for orderId: ${orderId}`)
                this.rollbarService.sendError( new Error("Invalid foodProduct in order snapshots"), {
                    extra: {
                        orderId,
                    }
                })
            }
            thumbnailImage = UrlPathBuilder.getSingleImagePath(EatUtil.getProductId(foodProduct), "FOOD", "THUMBNAIL", foodProduct.imageVersion)
            thumbnailImages.push(thumbnailImage)
        }

        let orderProductDetail: OrderProductDetail
        // Handling case where fulfilment not created as order came past cut off
        let reportIssues: IssueDetailView[] = undefined
        if (foodFulfilment && foodFulfilment.packState !== "CANCELLED") {
            try {
                reportIssues = await this.issueBusiness.getIssues("CAFE", ["DEFAULT"], {orderId}, userContext)
            } catch (e) {
                this.logger.error("Fetch failed report issues for orderId:" + order.orderId, e)
            }
        }

        orderProductDetail = {
            title: numProducts > 1 ? numProducts + " items ordered" : order.productSnapshots[0].title,
            thumbnailImages: thumbnailImages,
            thumbnailImage: thumbnailImage,
            action: "",
            itemDetails: itemDetails,
            reportIssues: reportIssues
        }

        return orderProductDetail
    }


    async getFoodMarketplaceOrderDetails(userContext: UserContext, order: Order): Promise<OrderProductDetail> {
        const numProducts = this.numberOfProducts(order)
        const { productSnapshots, productTypeOptions, orderId } = order
        const { externalServiceFulfilmentId } = productTypeOptions
        const thumbnailImages: string[] = []
        const itemDetails: ItemDetail[] = []
        let imageThumbnail: string
        const listingBrand = "FOOD_MARKETPLACE"

        for (let i = 0; i < productSnapshots?.length || 0; i = i + 1) {

            const snapshot = productSnapshots[i]
            const imageUrl = snapshot?.imageUrl || FoodMarketplaceUtil.getDummyMealCardImageUrl()
            thumbnailImages.push(imageUrl)
            if (productSnapshots?.length === 1) {
                imageThumbnail = imageUrl
            }
            itemDetails.push({
                title: snapshot?.title,
                thumbnailImage: imageUrl,
                isVeg: snapshot?.foodType === "Veg",
                quantity: snapshot?.quantity,
                action: undefined // as no pdp for now
            })
        }

        const response: OrderProductDetail = {
            reportIssues: undefined, // todo define issues for foodmp and integrate
            itemDetails: itemDetails,
            title: numProducts > 1 ? `${numProducts} items ordered` : order.productSnapshots[0].title,
            subTitle: undefined,
            action: FoodMarketplaceUtil.generateFoodMarketplaceOrderTrackingUrl(externalServiceFulfilmentId, orderId, listingBrand)
        }
        return response
    }

    // TODO: Move to common util
    private numberOfProducts(order: BaseOrder): number {
        let numProducts = 0
        order.products.forEach(orderProduct => {
            numProducts = numProducts + orderProduct.quantity
        })
        return numProducts
    }

    private async getFitnessAccessoriesDetail(product: Product, orderProduct: OrderProduct): Promise<OrderProductDetail> {
        const orderProductDetail: OrderProductDetail = {
            title: product.title,
            thumbnailImages: [UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, product.productType, "HERO", product.imageVersion)],
            action: ActionUtil.productPage(product.productId),
            reportIssues: undefined
        }
        return orderProductDetail
    }

    private async getBundleDetail(product: Product, orderProduct: OrderProduct, userContext: UserContext): Promise<OrderProductDetail> {
        const diagnosticProduct: DiagnosticProduct = <DiagnosticProduct>await this.catalogueService.getProduct(product.productId)
        const orderSource = userContext.sessionInfo.orderSource
        const tenant: HealthfaceTenant = CareUtil.getHealthfaceTenant(diagnosticProduct.subCategoryCode, undefined, orderSource)
        const bookingDetail = await this.healthfaceService.getBookingDetailForBookingId(Number(orderProduct.option.bookingId), tenant)
        let productId = orderProduct.productId, subCategoryCode = diagnosticProduct.subCategoryCode, bookingId = orderProduct.option.bookingId
        let rootBooking
        if (bookingDetail.booking.rootBookingId !== -1) {
            rootBooking = await this.healthfaceService.getBookingDetailForBookingId(bookingDetail.booking.rootBookingId, tenant)
            productId = rootBooking.booking.productCode
            subCategoryCode = rootBooking.booking.subCategoryCode
            bookingId = rootBooking.booking.id.toString()
        }
        if (orderProduct.productType === "BUNDLE" && !_.isEmpty(orderProduct.option.parentProductCode)) {
            product = await this.catalogueService.getProduct(orderProduct.option.parentProductCode)
        }
        let reportIssues: IssueDetailView[]
        if (diagnosticProduct.subCategoryCode === "HCU") {
            reportIssues = await this.issueBusiness.getHCIssues(bookingDetail, userContext)
        } else if (diagnosticProduct.subCategoryCode === "HCU_PACK") {
            reportIssues = await this.issueBusiness.getHCUPacksIssue(bookingDetail, userContext)
        } else if (diagnosticProduct.subCategoryCode === "DIAG_PACK") {
            reportIssues = await this.issueBusiness.getScreeningPacksIssue(bookingDetail, userContext)
        } else if (diagnosticProduct.subCategoryCode === "MIND_THERAPY") {
            reportIssues = await this.issueBusiness.getTherapyIssues(bookingDetail.booking)
        } else if (diagnosticProduct.subCategoryCode === "PERSONAL_TRAINING") {
            reportIssues = await this.issueBusiness.getPersonalTrainingPacksIssue(bookingDetail, userContext)
        } else if (diagnosticProduct.subCategoryCode === "PHYSIOTHERAPY") {
            reportIssues = await this.issueBusiness.getPhysiotherapyPacksIssue(bookingDetail, userContext)
        } else if (diagnosticProduct.subCategoryCode === "NUTRITIONIST") {
            reportIssues = await this.issueBusiness.getLCPacksIssue(bookingDetail, userContext)
        } else if (diagnosticProduct.subCategoryCode === "LIVE_PERSONAL_TRAINING") {
            reportIssues = await this.issueBusiness.getLivePersonalTrainingPacksIssue(bookingDetail, userContext)
        } else if (diagnosticProduct.subCategoryCode === "LIVE_SGT") {
            reportIssues = await this.issueBusiness.getLiveSGTPacksIssue(bookingDetail, userContext)
        } else {
            reportIssues = await this.issueBusiness.getManagedPlanIssue(rootBooking ? rootBooking : bookingDetail, userContext)
        }
        const orderProductDetail: OrderProductDetail = {
            title: product.title,
            thumbnailImages: [product.imageUrl ? product.imageUrl : diagnosticProduct.imageUrl],
            thumbnailImage: product.imageUrl ? product.imageUrl : diagnosticProduct.imageUrl,
            reportIssues: reportIssues,
            action: bookingDetail.booking.status !== "CANCELLED" ? ActionUtil.carefitbundle(productId, subCategoryCode, bookingId, undefined, undefined, undefined, undefined, undefined, userContext.sessionInfo.userAgent) : null
        }
        return orderProductDetail
    }

    private async getDiagnosticsSingleDetail(product: Product, orderProduct: OrderProduct, userContext: UserContext, numProducts: number): Promise<OrderProductDetail> {
        const diagnosticsProduct: DiagnosticProduct = <DiagnosticProduct> await this.catalogueService.getProduct("DIAGNOSTIC_TEST") // todo get this from bookingInfo
        const bookingDetail = await this.healthfaceService.getBookingDetail(Number(orderProduct.option.bookingId))
        const rootBookingDetail = await this.healthfaceService.getBookingDetail(Number(bookingDetail.booking.rootBookingId))
        const rootDiagnosticsProduct: DiagnosticProduct = <DiagnosticProduct> await this.catalogueService.getProduct(rootBookingDetail.booking.productCode)
        const productId = rootDiagnosticsProduct.productId, subCategoryCode = rootDiagnosticsProduct.subCategoryCode, bookingId = bookingDetail.booking.rootBookingId.toString()
        const isSubCategoryCodeHCUorDIAG = subCategoryCode === "HCU_PACK" || subCategoryCode === "DIAG_PACK"
        const action = isSubCategoryCodeHCUorDIAG ? ActionUtil.carefitbundle(productId, subCategoryCode, bookingId) : ActionUtil.diagnostics(diagnosticsProduct.productId, orderProduct.option.bookingId)
        const reportIssueParams = this.issueBusiness.getDiagnosticTestIssueParams(bookingDetail)
        const orderProductDetail: OrderProductDetail = {
            title: numProducts > 1 ? `${product.title} + ${(numProducts - 1)} ${numProducts > 2 ? " Tests" : " Test"}` : product.title,
            thumbnailImages: [diagnosticsProduct.imageUrl],
            thumbnailImage: diagnosticsProduct.imageUrl,
            reportIssues: await this.issueBusiness.getIssues(reportIssueParams.productType, reportIssueParams.productStates, reportIssueParams.meta, userContext),
            action: bookingDetail.booking.status !== "CANCELLED" ? action : null
        }
        return orderProductDetail
    }


    private async getTeleconsultationSingleDetail(product: Product, orderProduct: OrderProduct, userContext: UserContext): Promise<OrderProductDetail> {
        const consultationProduct: ConsultationProduct = <ConsultationProduct>await this.catalogueService.getProduct(product.productId)
        if (_.isNil(consultationProduct)) {
           return { reportIssues: [], title: ""}
        }
        let bookingDetail
        try {
            bookingDetail = await this.healthfaceService.getBookingDetailForBookingId(Number(orderProduct.option.bookingId), consultationProduct.tenant)
        } catch (e) {
            this.logger.error("Teleconsultation getBookingDetailForBookingId failed for bookingId: " + orderProduct.option.bookingId, e)
            this.rollbarService.sendError(e, {
                extra: {
                    bookingId: orderProduct.option.bookingId,
                }
            })
            return {
                title: product.title,
                reportIssues: []
            }
        }
        let reportIssueParams
        let reportIssues: IssueDetailView[]
        if (!_.isNil(consultationProduct) && CareUtil.isCultPTProduct(consultationProduct)) {
            reportIssues = await this.issueBusiness.getPersonalTrainingSessionIssue(bookingDetail, userContext)
        } else if (CareUtil.isLCProduct(consultationProduct)) {
            reportIssues = await this.issueBusiness.getLCSessionIssue(bookingDetail, userContext)
        } else if (CareUtil.isLivePTProduct(consultationProduct)) {
            reportIssues = await this.issueBusiness.getLivePersonalTrainingSessionIssue(bookingDetail, userContext)
        } else if (CareUtil.isLiveSGTProduct(consultationProduct)) {
            reportIssues = await this.issueBusiness.getLiveSGTSessionIssue(bookingDetail, userContext)
        } else {
            reportIssueParams = this.issueBusiness.getConsulationIssueParams(bookingDetail)
            reportIssues = await this.issueBusiness.getIssues(reportIssueParams.productType, reportIssueParams.productStates, reportIssueParams.meta, userContext)
        }
        const vertical = CareUtil.getVerticalForConsultation(consultationProduct.doctorType)
        const isMindTherapyAuroraUser = await AppUtil.checkIfUserPartOfMindTherapyAuroraExperiment(userContext, this.hamletBusiness)
        const orderProductDetail: OrderProductDetail = {
            title: product.title,
            thumbnailImages: ["/image/singles/care/tc/tcThumbnail.png"],
            thumbnailImage: "/image/singles/care/tc/tcThumbnail.png",
            reportIssues: reportIssues,
            action: ActionUtil.teleconsultationSingle(userContext, product.productId, consultationProduct.urlPath, orderProduct.option.bookingId, undefined, vertical, isMindTherapyAuroraUser),
        }
        return orderProductDetail
    }

    private async getCultBikeOrderPDPDetail(product: Product,  userContext: UserContext, order: BaseOrder): Promise<OrderProductDetail> {

        const externalOrderId = (<Order>order)?.productTypeOptions?.externalServiceFulfilmentId
        const orderTrackingDetails = externalOrderId ? await this.cultBikeClient.getOrderTrackingDetails({
            order_id: (<Order>order)?.productTypeOptions?.externalServiceFulfilmentId
        }) : undefined
        const seeDetailAction = _.isEmpty(orderTrackingDetails?.data?.tracking_link) ?  undefined : {
            actionType: "NAVIGATION",
            url: "curefit://webview?uri=" + orderTrackingDetails.data.tracking_link + "&processUrl=true&title=Track Your Order",
            title: "Track order"
        } as Action
        const imageUrl = product.imageUrl || (await this.catalogueService.getProduct(product.productId))?.imageUrl
        const orderProductDetail: OrderProductDetail = {
            title: product.title,
            thumbnailImages: [ imageUrl],
            thumbnailImage: imageUrl,
            reportIssues: await this.issueBusiness.getCultBikeIssue(userContext, order), // to do enable once the issue config is done
            action: undefined,
            pageActions: undefined,
            seeDetailAction: seeDetailAction
        }
        return orderProductDetail
    }

    async getOrderProductDetails(userContext: UserContext, order: BaseOrder): Promise<OrderProductDetail> {
        const product: Product = order.productSnapshots[0]
        const orderProduct: OrderProduct = order.products[0]
        const userAgent = userContext.sessionInfo.userAgent

        if (product.isPack) {
            if (product.productType === "FITNESS") {
                return this.getCultFitPackDetail(userContext, product, order.orderId)
            } else if (product.productType === "MIND") {
                return this.getMindFitPackDetail(userContext, product, order.orderId)
            } else if (product.productType === "FOOD") {
                return this.getFoodPackDetail(userContext, product, order)
            } else if (product.productType === "PROGRAM") {
                return this.getProgramPackDetail(userContext, product, order.orderId, order.userId)
            } else if (product.productType === "FITNESS_FIRST_PACK") {
                return this.getFitnessFirstPackDetail(userContext, product, order.orderId)
            } else if (product.productType === "GYMFIT_FITNESS_PACK" || product.productType === "THIRD_PARTY_FITNESS_PRODUCT") {
                return this.getGymfitFitnessPackDetail(userContext, product, order.orderId)
            } else if (product.productType === "GYMFIT_FITNESS_PRODUCT") {
                return this.getGymfitFitnessProductDetail(userContext, product, order.orderId)
            } else if (product.productType === "CF_LIVE") {
                return this.getLivePackDetail(userContext, product, order.orderId)
            } else if (product.productType === "GYM_PT_PRODUCT") {
                return this.getGymPtProductDetail(userContext, product, order.orderId)
            } else if (product.productType === "GYM_PT_PPC_PRODUCT") {
                return this.getGymPtPpcProductDetail(userContext, product, order.orderId)
            } else if (product.productType === "PLAY") {
                return this.getPlayFitPackDetail(userContext, product, order.orderId, false)
            } else if (product.productType === "LUX_FITNESS_PRODUCT") {
                return this.getLuxFitnessProductDetail(userContext, product, order.orderId)
            } else if (product.productType === "ADDON") {
                return this.getAddOnProductDetail(userContext, product, order.orderId)
            }
        } else {
            if (product.productType === "LUX_FITNESS_PRODUCT") {
                return this.getLuxFitnessProductDetail(userContext, product, order.orderId)
            } else if (product.productType === "ADDON") {
                return this.getAddOnProductDetail(userContext, product, order.orderId)
            } else if ((product.productType === "FITNESS" || product.productType === "GYMFIT_FITNESS_PRODUCT") && !CultUtil.isCultEventOrder(orderProduct)) {
                /**
                 * Could be an upgrade pack purchase
                 */
                if (isUpgradeMembershipOrder(order)) {
                    return this.getPackUpgradeOrderDetail(product, order.orderId, userContext)
                } else if (isTransferMembershipOrder(order)) {
                    return this.getPackTransferOrderDetail(product, order.orderId, userContext)
                }
                return this.getCultFitBookingDetail(product, order.orderId, userContext)
            } else if (product.productType === "FITNESS" && CultUtil.isCultEventOrder(orderProduct)) {
                return this.getCultFitEventDetail(product, order.orderId)
            }
            else if (product.productType === "MIND" && !CultUtil.isCultEventOrder(orderProduct)) {
                return this.getMindFitBookingDetail(product, order.orderId, userContext)
            }
            else if (product.productType === "MIND" && CultUtil.isCultEventOrder(orderProduct)) {
                return this.getMindFitEventDetail(product, order.orderId)
            }
            else if (product.productType === "FOOD") {
                return this.getFoodSingleDetail(userContext, order, order.orderId, orderProduct.option.startDate)
            } else if (product.productType === "BUNDLE") {
                return this.getBundleDetail(product, orderProduct, userContext)
            } else if (product.productType === "DIAGNOSTICS") {
                const numProducts = this.numberOfProducts(order)
                return this.getDiagnosticsSingleDetail(product, orderProduct, userContext, numProducts)
            } else if (product.productType === "CONSULTATION") {
                return this.getTeleconsultationSingleDetail(product, orderProduct, userContext)
            } else if (product.productType === "FITNESS_ACCESSORIES") {
                return this.getFitnessAccessoriesDetail(product, orderProduct)
            }  else if (product.productType === "GEAR") {
                return this.getGearOrderDetail(product, order.orderId, userContext)
            } else if (product.productType === "FIT_CLUB_MEMBERSHIP") {
                return this.getFitclubMembershipDetail(userContext, order)
            } else if (product.productType === "FOOD_MARKETPLACE") {
                return this.getFoodMarketplaceOrderDetails(userContext, order)
            } else if (product.productType === "CULT_BIKE") {
                return this.getCultBikeOrderPDPDetail(product, userContext, order)
            } else if (product.productType === "PLAY") {
                if (isUpgradeMembershipOrder(order) && isMembershipUpgradeClientMetadata((order as Order).clientMetadata)) {
                    if (Array.isArray((order as Order).statusHistory)) {
                        const paidForOrder = (order as Order).statusHistory.find(s => s.status === "PAYMENT_SUCCESS")
                        if (paidForOrder) {
                            // transaction history
                            return this.getPlayFitPackDetail(userContext, product, order.orderId, true)
                        }
                    }
                }
            } else if (product.productType === "REGISTRATION") {
                return this.getRegistrationProductDetail(product)
            }
        }
    }
}
export default OrderProductDetailBuilder
