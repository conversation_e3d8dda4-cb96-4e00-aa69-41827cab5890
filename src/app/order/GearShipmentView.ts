import * as _ from "lodash"
import { decorate, injectable } from "inversify"
import { TrackShipmentResponse, GearShipmentMiniState, TrackShipmentResponseV2 } from "@curefit/gear-common"
import { TimeUtil, Timezone } from "@curefit/util-common"

export interface GearTrackShipmentMiniStatesView {
    [key: string]: GearShipmentMiniState[]
}

export default class GearShipmentView {
    protected getShipmentMiniStates(trackShipmentResponse: TrackShipmentResponse | TrackShipmentResponseV2, timezone: Timezone): GearTrackShipmentMiniStatesView {
        if (_.isEmpty(trackShipmentResponse.mini_states)) {
            return {}
        }
        return trackShipmentResponse.mini_states.reduce(
            (
                accumulator: {
                    [key: string]: GearShipmentMiniState[]
                },
                current: GearShipmentMiniState
            ) => {
                const {
                    event_time
                } = current
                // Changing the date and time to words
                const dateInWords = TimeUtil.getMomentForDateString(event_time.date, timezone).format("ddd, DD MMM")
                const modifiedCurrent = {
                    ...current,
                    event_time: {
                        ...current.event_time,
                        time: TimeUtil.getMomentForDateString(
                            `${event_time.date} ${event_time.time}`,
                            timezone,
                            "YYYY-MM-DD HH:mm:ss"
                            ).format("LT")
                    }
                }
                return {
                    ...accumulator,
                    [dateInWords]: [
                        ...(accumulator[dateInWords] || []),
                        modifiedCurrent,
                    ],
                }
            }, {}
        )
    }
}

decorate(injectable(), GearShipmentView)
