import { injectable, inject } from "inversify"
import {
    Action,
    ActionableCardWidget,
    ActionCard,
    CareCheckoutSummaryWidget,
    ChainedAction,
    DescriptionWidget,
    DiagnosticsConfirmationSummaryWidget,
    EmiOptions,
    getOffersWidget,
    GymPackInfo,
    HCUBundleConfirmationSummaryWidget,
    Header,
    HighlightInfoWidget,
    InfoCard,
    InstructionsWidget,
    OfferCalloutWidget,
    OfferData,
    OrderSuccessWidget,
    PackOrderInfoWidget,
    ProductListWidget,
    SinglesOrderConfirmationWidget,
    TextWidget,
    VideoAlertCardWidget,
    WidgetView,
    ManageOptionPayload,
    ManageOptions,
    ManageOptionsWidget,
    GradientCard,
    ClpCalloutWidget,
    DiagnosticsConfirmationV2Widget,
    DiagnosticsConfirmationDesktopWidget,
    CongratulationWidget,
    CSOrderFeedbackInputWidget,
    CSOrderDeliveryAddressWidget,
    CSOrderDeliveryTrackWidget,
    CSOrderItemsWidget,
    CultsportOrderProductCardWidget, CsOrderUpdateUserInfo
} from "../common/views/WidgetView"
import {
    CultBooking,
    CultCenter,
    CultClass,
    CultWorkout,
    FitnessEvent,
    VaccinationProduct,
    VaccineAppointmentResponse
} from "@curefit/cult-common"
import { ConsultationProduct, DiagnosticProduct, HealthfaceTenant } from "@curefit/care-common"
import { ActivityType, ProductPrice, ProductType, ProgramPackProduct } from "@curefit/product-common"
import { IdentityResponse } from "@curefit/identity-common"
import { FitnessPack } from "@curefit/cult-common"
import { DeliverySlot, FoodProduct as Product, UserDeliveryAddress } from "@curefit/eat-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { Vertical } from "@curefit/base-common"
import { BaseFoodFulfilment, CultsportMetadata, FoodFulfilment, GearProductSnapshots, Order, OrderProduct, OrderProductSnapshots, OrderStatusChange } from "@curefit/order-common"
import { ActionUtilV1, Constants, MealUtil, OfferUtil, SeoUrlParams } from "@curefit/base-utils"
import { TimeUtil } from "@curefit/util-common"
import * as momentTz from "moment-timezone"
import { UserActivity } from "../user/TimelineView"
import AtlasUtil from "../util/AtlasUtil"
import { OrderInfo, PriceComponent } from "./OrderViewBuilder"
import {
    BookingDetail,
    ConsultationOrderResponse,
    DiagnosticsTestOrderResponse,
    Doctor,
    Patient,
    ConsultationInstructionResponse,
    HealthfaceProductInfo
} from "@curefit/albus-client"
import { CareUtil } from "../util/CareUtil"
import CultUtil, { RUNNING_EVENT_WORKOUT_ID } from "../util/CultUtil"
import BaseOrderConfirmationViewBuilder, {
    ConfirmationRequestParams,
    ConfirmationView,
    DIYConfirmationView
} from "./BaseOrderConfirmationViewBuilder"
import OrderUtil, {
    CARE_ORDER_CONFIRMATION_ACTION,
    GEAR_ORDER_CONFIRMATION_ACTION,
    ORDER_CONFIRMATION_ACTION, ORDER_CONFIRMATION_V1_ACTION,
    ORDER_CONFIRMATION_EAT_MARKETPLACE_ACTION,
    TRANSFORM_ORDER_CONFIRMATION_V1_ACTION,
    CARE_AURORA_ORDER_CONFIRMATION_V1_ACTION
} from "../util/OrderUtil"
import * as _ from "lodash"
import { ProgramPack } from "@curefit/program-client"
import { CFLiveProduct, DIYPack, DIYPackFulfilment, DIYProduct } from "@curefit/diy-common"
import TeleconsultationDetailsPageConfig from "../care/TeleconsultationDetailsPageConfig"
import { ActionUtil } from "@curefit/base-utils"
import { DiyContentDetail, IBaseWidget, Orientation, UserContext } from "@curefit/vm-models"
import { SlotUtil } from "@curefit/eat-util"
import { ListingActionableCardWidget } from "../page/PageWidgets"
import TherapyPageConfig from "../therapy/TherapyPageConfig"
import { BillingInfo, RUPEE_SYMBOL } from "@curefit/finance-common"
import AppUtil, { CANCELLATION_WINDOW_FOR_NO_SHOW, SUPPORT_DEEP_LINK } from "../util/AppUtil"
import { OfferV2 } from "@curefit/offer-common"
import { EmiInterest } from "@curefit/payment-common"
import { SUPPORTED_MICROAPPS_ORDER_SOURCE } from "../util/OrderUtil"
import { capitalise } from "../util/StringUtil"
import { InsuranceProduct, InsuranceMemberPolicy } from "@curefit/insurance-common"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { ActionType, WidgetMetric, WidgetWithMetric } from "@curefit/vm-common"
import LiveUtil from "../util/LiveUtil"
import { Membership } from "@curefit/membership-commons"
import { IOfferAddonWidgetParams, OfferAddonWidget } from "../common/views/OfferAddonWidget"
import { User } from "@curefit/user-common"
import { IssueDetailView } from "../crm/IssueBusiness"
import {
    CareCheckoutInfoItemWidget, CareTCConfirmInfoWidget, CheckoutInfoItem,
    IDiagnosticConfirmationHeading,
    IDiagnosticConfirmationOrderProductDetail,
    IDiagnosticConfirmationPaymentDetails,
    IDiagnosticConfirmationTestDetails,
    IDiagnosticsConfirmationDesktopWidget,
    IDiagnosticsConfirmationV2Widget, VaccinationSummaryWidget
} from "@curefit/apps-common"
import GymfitUtil from "../util/GymfitUtil"
import { VaccinationConfig } from "@curefit/config-mongo"
import { ListPage } from "../page/vm/VMPageBuilder"
import { GymPTOrderConfirmationSummaryWidget } from "../common/views/GymPTOrderConfirmationSummarWidget"
import { CenterResponse } from "@curefit/center-service-common"
import { DoctorAssetsResponse } from "@curefit/ollivander-node-client/dist/src/OllivanderCityService"
import { CGMProduct } from "@curefit/care-common/src/product"
import { FeedbackRequest } from "@curefit/cultsport-feedback-client"
import { CFUserProfile } from "../page/vm/CFUserProfile"
import { PromiseCache } from "../util/VMUtil"
import CultsportUtil from "../util/CultsportUtil"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"
import CreditsView from "../common/views/CreditsViewWidget"
import { MembershipItemUtil } from "../util/MembershipItemUtil"
import PlayUtil from "../util/PlayUtil"


@injectable()
class OrderConfirmationViewBuilder extends BaseOrderConfirmationViewBuilder {

    async buildFoodPackConfirmationView(userContext: UserContext, product: Product, order: Order): Promise<ConfirmationView> {
        return null
    }

    async buildFitnessPackConfirmationView(product: Product, order: Order, params: ConfirmationRequestParams): Promise<ConfirmationView> {
        const cultPack = await this.catalogueServicePMS.getProduct(product.productId)
        let offers: { [key: string]: OfferV2 } = {}
        if (AppUtil.isNewPackOrderConformationSupported(params.userContext, (await params.userContext.userPromise).isInternalUser) && order.offersInfo && !_.isEmpty(order.offersInfo)) {
            const offerIds = order.offersInfo.map(offerInfo => offerInfo.offerId)
            offers = (await this.offerServiceV3.getOffersByIds(offerIds)).data
        }
        if (product.productType === "FITNESS" || product.productType === "FITNESS_PREREGISTRATION") {
            const recoWidgets = await this.buildConfirmationVmRecoWidgets(params.userContext, product.productType)
            const doesUserBelongToAccPartnerMemExp = await CultUtil.doesUserBelongToAccPartnerExp(params.userContext, this.serviceInterfaces)
            let memExpDL = null
            if (doesUserBelongToAccPartnerMemExp) {
                memExpDL = "curefit://fl_form?formId=post_pack_purchase_onboarding_v2&fillPreviousResponse=true"
            }
            return Promise.resolve(new CultFitnessPackConfirmationView(cultPack, order, params.userContext, offers, (await params.userContext.userPromise).isInternalUser, undefined, recoWidgets, memExpDL))
        }
    }

    async buildPlayPackConfirmationView(product: Product, order: Order, params: ConfirmationRequestParams): Promise<ConfirmationView> {
        const playPack = await this.catalogueServicePMS.getProduct(product.productId)
        let offers: { [key: string]: OfferV2 } = {}
        if (order.offersInfo && !_.isEmpty(order.offersInfo)) {
            const offerIds = order.offersInfo.map(offerInfo => offerInfo.offerId)
            offers = (await this.offerServiceV3.getOffersByIds(offerIds)).data
        }
        if (product.productType === "PLAY") {
            return Promise.resolve(
                new PlayPackConfirmationView(
                    playPack,
                    order,
                    params.userContext,
                    offers,
                    (await params.userContext.userPromise).isInternalUser,
                    undefined,
                    null
                )
            )
        }
    }

    async buildCFLivePackConfirmationView(userContext: UserContext, product: Product, order: Order, params: ConfirmationRequestParams): Promise<ConfirmationView> {
        const liveMemberships = await this.diyFulfilmentService.getAllMembershipDetails(userContext.userProfile.userId, AppUtil.getTenantFromUserContext(userContext))
        const currentOrderMembership: Membership[] = []
        if (liveMemberships.length > 0) {
            liveMemberships.forEach((membership: Membership) => {
                if (membership.orderId === order.orderId) {
                    currentOrderMembership.push(membership)
                }
            })
        }
        currentOrderMembership.sort((a, b) => {
            return a.start - b.start
        })
        return new LivePackConfirmationView(userContext, order, currentOrderMembership[0], product)
    }

    async buildVaccinationAppointmentConfirmationView(userContext: UserContext, product: Product, order: Order, params: ConfirmationRequestParams): Promise<ConfirmationView> {
        const appointmentId = _.get(order, "products[0].option.vaccineAppointmentId", undefined)
        const appointmentDetails = await this.cultFitService.getAppointmentById("CUREFIT_API", userContext.userProfile.userId, appointmentId)
        const vaccinationConfig = this.configService.getConfig("MEMBER_VACCINATION")
        return new VaccinationAppointmentConfirmationView(userContext, order, product, appointmentDetails, vaccinationConfig)
    }

    buildDIYSubscriptionConfirmationViewV2(userContext: UserContext, pack: DIYPack, sessionMap: { [productId: string]: Product }, fulfilment: DIYPackFulfilment): Promise<DIYConfirmationView> {
        return Promise.resolve(new SubscriptionConfirmationViewV2(userContext, pack, sessionMap, fulfilment))
    }

    buidFitnessFirstPackConfirmationView(userContext: UserContext, product: Product): Promise<ConfirmationView> {
        return Promise.resolve(new FitnessFirstPackConfirmationView(userContext, product))
    }

    async buildAddonConfirmationView(product: Product): Promise<ConfirmationView> {
        const productId = product.productId
        const offlineFitnessPack: OfflineFitnessPack = await this.catalogueServicePMS.getProduct(productId)
        return new AddonPackConfirmationViewV1(offlineFitnessPack)
    }

    buildGymfitFitnessPackConfirmationView(userContext: UserContext, product: Product, order: Order): Promise<ConfirmationView> {
        return Promise.resolve(new GymfitFitnessPackConfirmationView(userContext, product, order))
    }

    async buildGymfitFitnessProductConfirmationView(userContext: UserContext, product: Product, order: Order, params: ConfirmationRequestParams): Promise<ConfirmationView> {
        const resp = await this.serviceInterfaces.membershipService.filter({orderId: order.orderId})
        const goldMemberships = resp.elements
        // const goldMemberships: Membership[] = await this.membershipService.filter({orderId: order.orderId}) // First element would be the purchased membership *facepalm*
        return Promise.resolve(new GymFitnessProductConfirmationView( userContext, goldMemberships[0]))
    }

    buildCultEventConfirmationView(fitnessEventId: string, userId: string): Promise<ConfirmationView> {
        return this.cultFitService.getFitnessEvent(fitnessEventId, userId).then(cultEvent => {
            return Promise.resolve(new CultEventConfirmationView(cultEvent))
        })
    }

    buildCultMindEventConfirmationView(fitnessEventId: string, userId: string): Promise<ConfirmationView> {
        return this.mindFitService.getFitnessEvent(fitnessEventId, userId).then(cultEvent => {
            return Promise.resolve(new CultEventConfirmationView(cultEvent))
        })
    }

    buildCultClassConfirmationView(userContext: UserContext, cultBookingId: string, cultBooking: CultBooking, userId: string): Promise<ConfirmationView> {
        return this.cultFitService.getBookingById(cultBookingId, userId).then(cultBooking => {
            return new CultFitnessClassConfirmationView(userContext, cultBooking)
        })
    }

    buildMindClassConfirmationView(userContext: UserContext, cultBookingId: string, userId: string): Promise<ConfirmationView> {
        return this.mindFitService.getBookingById(cultBookingId, userId).then(mindBooking => {
            return new MindFitnessClassConfirmationView(userContext, mindBooking)
        })
    }


    async buildEatMarketplaceConfirmationView(product: Product, order: Order, params: ConfirmationRequestParams): Promise<ConfirmationView> {
        return Promise.resolve(new EatMarketplaceConfirmationView(order))
    }

    // async buildFoodMarketplaceConfirmationView(product: Product, order: Order, userContext: UserContext): Promise<ConfirmationView> {
    //
    //     const orderInfo = await this.orderViewBuilder.mapFoodMarketplaceOrderInfo(order, product)
    //
    //     return await new FoodMarketplaceConfirmationView().buildView(userContext, product, order, orderInfo, undefined, undefined)
    // }

    async buildFoodSingleConfirmationView(product: Product, order: Order, params: ConfirmationRequestParams): Promise<ConfirmationView> {
        const orderInfo = await this.orderViewBuilder.getOrderInformation(order, params.userContext)
        let onDemandText = ""
        if (order.products[0].option.deliverySlot === SlotUtil.ON_DEMAND_SLOT
            && (!order.eatOptions || !order.eatOptions.onDemandPromise)) {

            this.logger.info(`Refetching on demand promise as its not present`)
            if (!order.eatOptions) {
                order.eatOptions = {}
            }
            order.eatOptions.onDemandPromise = await this.capacityService.getDefaultOnDemandPromise(order.userAddress.areaId)
            onDemandText = order.eatOptions.onDemandPromise.message
        }

        const slotId = _.get(order, "products[0].option.deliverySlot", undefined)
        const deliverySlot = slotId ? this.serviceInterfaces.deliverySlotService.getSlotById(slotId) : undefined
        return Promise.resolve(new FoodSingleConfirmationView(params.userContext, product, order, orderInfo, onDemandText, params, deliverySlot))
    }

    buildAccessoriesConfirmationView(userContext: UserContext, product: Product, order: Order): Promise<ConfirmationView> {
        return Promise.resolve(new AccessoriesConfirmationView(userContext, product, order))
    }

    async buildCareConsultationConfirmationView(userContext: UserContext, product: Product, order: Order, params?: ConfirmationRequestParams, userAgent?: UserAgent): Promise<ConfirmationView> {
        const isChronicCareApp = CareUtil.getIfChronicCareFromUserContext(userContext)
        if (isChronicCareApp) {
            const bookingDetail: BookingDetail = await this.healthfaceService.getBookingDetailForBookingId(Number(product.option.bookingId), "SUGARFIT", true)
            let consultationType = "DOCTOR"
            if (_.get(bookingDetail, "consultationOrderResponse.doctor.type") === "LIFESTYLE_COACH") {
                consultationType = "COACH"
            }
            const resetNavigationStack = CareUtil.isSugarfitPhleboConsult(product?.productId) || CareUtil.isSugarfitExperienceCenterProduct(product as ConsultationProduct) || CareUtil.isSugarfitExperienceCenterDiagnosticProduct(product as ConsultationProduct)
            const bookingIdOfBooking = order.products.length > 1 ? product.option.bookingId : order.products[0].option.bookingId
            return new ChronicCareConsultationConfirmationView(userContext, consultationType, product?.productId, bookingIdOfBooking, resetNavigationStack)
        }
        let action: string
        if (order.products.length > 1) {
            let isMPV2 = false
            let patient
            const doctors: Doctor[] = []
            const consultationProducts: ConsultationProduct[] = []
            await Promise.all(_.map(order.products, async product => {
                const bookingDetail = await this.healthfaceService.getBookingDetail(Number(product.option.bookingId), undefined, undefined, undefined, undefined, undefined, true)
                patient = _.get(bookingDetail, "consultationOrderResponse.patient", {})
                doctors.push(_.get(bookingDetail, "consultationOrderResponse.doctor", {}))
                consultationProducts.push(_.get(bookingDetail, "consultationOrderResponse.consultationProduct", {}))
                if (!isMPV2 && bookingDetail && bookingDetail.booking.rootBookingId && bookingDetail.booking.rootBookingId !== -1) {
                    const parentBooking = await this.healthfaceService.getBookingDetail(bookingDetail.booking.rootBookingId)
                    isMPV2 = parentBooking.booking.subCategoryCode === "MP_V2"
                    action = isMPV2 ? ActionUtil.carefitbundle(parentBooking.booking.productCode, parentBooking.booking.subCategoryCode, parentBooking.booking.id.toString()) : undefined
                }
            }))
            return new MultiConsultationConfirmationView(userContext, consultationProducts, this.tcDetailsPageConfig, doctors, patient, order, isMPV2, action)
        } else {
            const consultationProduct: ConsultationProduct = <ConsultationProduct>(await this.catalogueService.getProduct(product.productId))
            const bookingDetail: BookingDetail = await this.healthfaceService.getBookingDetailForBookingId(Number(order.products[0].option.bookingId), consultationProduct.tenant, true)
            const user = await this.userCache.getUser(userContext.userProfile.userId)
            let isMPV2 = false, bannerWidgetPromise: Promise<WidgetWithMetric>, refferalbannerPromise: Promise<WidgetWithMetric>
            if (bookingDetail && bookingDetail.booking.rootBookingId && bookingDetail.booking.rootBookingId !== -1) {
                const parentBooking = await this.healthfaceService.getBookingDetail(bookingDetail.booking.rootBookingId, undefined, undefined, undefined, undefined, CareUtil.getCareProductTenant(consultationProduct))
                isMPV2 = parentBooking && parentBooking.booking.subCategoryCode === "MP_V2"
                action = isMPV2 ? ActionUtil.carefitbundle(parentBooking.booking.productCode, parentBooking.booking.subCategoryCode, parentBooking.booking.id.toString()) : undefined
            }
            const billingInfo: BillingInfo = this.offerHelper.getOrderBilling(order)
            const isLiveWorkoutConsultation = CareUtil.isLiveWorkoutConsultationDoctorType(consultationProduct.doctorType)
            const consultationInstructions = isLiveWorkoutConsultation ? undefined : await this.healthfaceService.getConsultationInstructionsV2(
                consultationProduct.productId,
                consultationProduct.doctorType,
                CareUtil.getConsultationMode(consultationProduct, bookingDetail),
                CareUtil.getInstructionTenant(consultationProduct)
            )
            if (CareUtil.showHowToVideoCallBanner(userContext, consultationProduct, bookingDetail)) {
                bannerWidgetPromise = CareUtil.getBannerCarouselWidget(userContext, this.serviceInterfaces, CareUtil.getCareHowToJoinVideoCallWidgetId())
            }
            if (userContext.sessionInfo.userAgent === "APP") {
                refferalbannerPromise = CareUtil.getBannerCarouselWidget(userContext, this.serviceInterfaces, CareUtil.getCareReferalBannerWidgetId())
            }
            const patientsList = CareUtil.isCoupleTherapist(consultationProduct.doctorType) ? await this.healthfaceService.getAllPatients(userContext.userProfile.userId) : []
            const showFeedbackWidget = !AppUtil.isWeb(userContext) && CareUtil.isTherapyOnlyDoctorType(consultationProduct.doctorType) && CareUtil.getSeekingReasonSegment(consultationProduct.doctorType) ?
                _.isEmpty(await this.serviceInterfaces.segmentService.doesUserBelongToSegment(CareUtil.getSeekingReasonSegment(consultationProduct.doctorType), userContext)) &&
                _.isEmpty(await this.ehrService.getPatientConsultationSubmittedReason(bookingDetail.consultationOrderResponse.patient.id.toString(), consultationProduct.doctorType))
                : false
            let reasons: string[] = []
            if (showFeedbackWidget) {
                reasons = await this.healthfaceService.getSessionSeekingReasons(consultationProduct.doctorType, CareUtil.getInstructionTenant(consultationProduct))
            }
            // this.logger.info(`Booking Info with Chat Channel in OrderConfirmation ${bookingDetail.booking.id} Channel: ${CareUtil.getChatChannel(bookingDetail)} Chat Enabled: ${CareUtil.getChatEnabled(bookingDetail)}`, bookingDetail.consultationOrderResponse.consultationUserState)
            const ConfirmationView = AppUtil.isNewTCCheckoutUISupported(userContext, consultationProduct) ? TeleconsultationSingleConfirmationViewV2 : TeleconsultationSingleConfirmationView
            return new ConfirmationView(
                params.userContext,
                consultationProduct,
                user,
                bookingDetail,
                this.tcDetailsPageConfig,
                billingInfo,
                isMPV2,
                action,
                consultationInstructions,
                bannerWidgetPromise ? await bannerWidgetPromise : undefined,
                refferalbannerPromise ? await refferalbannerPromise : undefined,
                patientsList,
                showFeedbackWidget,
                reasons
            )
        }
    }

    async buildGroupClassConfirmationView(product: Product): Promise<ConfirmationView> {
        const bookingId: number = product.option.bookingId
        const productCode: string = product.productId
        return new SugarfitGroupClassConfirmationView(bookingId, productCode)
    }

    async buildCareDiagnosticsConfirmationView(userContext: UserContext, product: Product, order: Order): Promise<ConfirmationView> {
        if (CareUtil.getIfChronicCareFromUserContext(userContext)) {
            return new ChronicCareDiagnosticsConfirmationView(userContext, order.orderId)
        }
        let drivenBannerWidgetPromise: Promise<WidgetWithMetric[]>
        if (AppUtil.isDrivenBannerUISupported(userContext)) {
            drivenBannerWidgetPromise = this.buildVmWidgetsV0(userContext, product.productType, false)
        }
        const bookingDetail: BookingDetail = await this.healthfaceService.getBookingDetail(Number(order.products[0].option.bookingId))
        let rootBooking: BookingDetail
        if (bookingDetail.booking.parentBookingId && bookingDetail.booking.parentBookingId != -1) {
            rootBooking = await this.healthfaceService.getBookingDetail(bookingDetail.booking.parentBookingId)
        }
        const isCareNewCheckoutSupported = CareUtil.isCareDiagnosticCartSupported(userContext) || userContext.sessionInfo.userAgent !== "APP"
        if (isCareNewCheckoutSupported && !["HCU_PACK", "DIAG_PACK"].includes(rootBooking?.booking?.subCategoryCode)) {
            const billingInfo: BillingInfo = this.offerHelper.getOrderBilling(order)
            const productCodes = order?.products?.map((product) => product.productId)
            const productInfosPromise = this.healthfaceService.getProductInfoDetails("DIAGNOSTICS", {
                productCodeCsv: productCodes.join(","),
                patientId: order?.products[0]?.option?.patientId,
                skipProductDetails: false,
            })
            const patientDetailsPromise = order?.products[0]?.option?.patientId ? this.healthfaceService.getPatientDetails(order?.products[0]?.option?.patientId) : undefined
            const reportIssueParams = this.issueBusiness.getDiagnosticTestIssueParams(bookingDetail)
            const reportIssues: IssueDetailView[] = await this.issueBusiness.getIssues(reportIssueParams.productType, reportIssueParams.productStates, reportIssueParams.meta, userContext)
            return new NewDiagnosticsConfirmationView(userContext, order, bookingDetail, await productInfosPromise, billingInfo, productCodes, await patientDetailsPromise, reportIssues, drivenBannerWidgetPromise ? await drivenBannerWidgetPromise : undefined)
        }
        return new DiagnosticSingleConfirmationView(bookingDetail, rootBooking, userContext, drivenBannerWidgetPromise ? await drivenBannerWidgetPromise : undefined)
    }


    async buildCareBundleConfirmationView(userContext: UserContext, product: Product, order: Order): Promise<ConfirmationView> {
        const productId = order.products[0].option.parentProductCode ? order.products[0].option.parentProductCode : product.productId
        let diagnosticProduct = <DiagnosticProduct>await this.catalogueService.getProduct(productId)
        const orderSource = userContext.sessionInfo.orderSource
        const tenant: HealthfaceTenant = CareUtil.getHealthfaceTenant(diagnosticProduct.subCategoryCode, undefined, orderSource)
        let bookingDetail: BookingDetail = await this.healthfaceService.getBookingDetailForBookingId(Number(order.products[0].option.bookingId), tenant)
        if (diagnosticProduct.subCategoryCode === "MIND_THERAPY") {
            return new MindTherapyPackConfirmationView(diagnosticProduct, bookingDetail, this.therapyPageConfig, userContext)
        } else if (diagnosticProduct.subCategoryCode === "PERSONAL_TRAINING") {
            return new BundleSessionPackConfirmationView(diagnosticProduct, bookingDetail)
        }
        if (!_.isNaN(bookingDetail.booking.rootBookingId) && bookingDetail.booking.rootBookingId !== -1) {
            bookingDetail = await this.healthfaceService.getBookingDetailForBookingId(bookingDetail.booking.rootBookingId, tenant)
            diagnosticProduct = <DiagnosticProduct>await this.catalogueService.getProduct(bookingDetail.booking.productCode)
        }
        return new BundleSingleConfirmationView(userContext, diagnosticProduct, bookingDetail)
    }

    buildGymPtConfirmationView(userContext: UserContext, product: Product, order: Order): Promise<ConfirmationView> {
        throw new Error("Method not implemented.")
    }

    buildLuxConfirmationView(userContext: UserContext, product: Product, order: Order): Promise<ConfirmationView> {
        throw new Error("Method not implemented.")
    }

    async buildProgramPackConfirmationView(userContext: UserContext, product: Product, order: Order): Promise<ConfirmationView> {
        const programPackProduct = <ProgramPackProduct>await this.catalogueService.getProduct(product.productId)
        const programPack = await this.programBusiness.getProgramPack(order.userId, programPackProduct.packId)
        return new ProgramPackConfirmationView(userContext, product, order, programPack)
    }

    async buildGearOrderConfirmationView(user: User, userContext: UserContext, product: Product, order: Order, params?: ConfirmationRequestParams, feedback?: FeedbackRequest, isNewCultsportOrderFlowSupported?: boolean, merchantInfoDetailsForGokwik?: any, address?: UserDeliveryAddress): Promise<ConfirmationView> {
        let widgets: WidgetWithMetric[] = []
       if (AppUtil.isCultSportWebApp(userContext)) {
            // Prefilling segment data for the order confirmation banner segmentation
            const userProfile: CFUserProfile = userContext.userProfile
            userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
            userProfile.cultsportSegmentsMetaData =  {
                articleTypes: order.productSnapshots.map(product => product.gearArticleType),
                categories: order.productSnapshots.map(product => product.gearCategory),
                brands: order.productSnapshots.map(product => product.gearBrandName),
                collectionSlugs: undefined,
                productIds: order.productSnapshots.map(product => product.masterProductId),
            }
            userContext.userProfile =  userProfile
            widgets = AppUtil.isProdLike ?  await this.getRecoWidgetsForPageId(userContext, product.productType, "cultsport-reco-page") : []
       }
       return new GearOrderConfirmationView(user, userContext, order, feedback, isNewCultsportOrderFlowSupported, widgets, merchantInfoDetailsForGokwik, address)
    }

    buildWaitlistClassConfirmationView(wlBookingNumber: string, productType: ProductType, userContext: UserContext, params?: ConfirmationRequestParams): Promise<ConfirmationView> {
        return
    }

    buildLiveClassConfirmationView(userContext: UserContext, classId: string): Promise<ConfirmationView> {
        return undefined
    }

    async buildCultBikeConfirmationView(userContext: UserContext, product: Product, order: Order, params: ConfirmationRequestParams): Promise<ConfirmationView> {
        const user = await this.userCache.getUser(userContext.userProfile.userId)
        return new CultBikeConfirmationView(userContext, product, user, order, params)
    }

    private async buildVmWidgetsV0(userContext: UserContext, productType: ProductType, isPack: boolean) {
        if (isPack && AppUtil.isWeb(userContext)) {
            return []
        }

        const bannerConfig = isPack
            ? this.bookingConfirmationConfig.packRecommendationConfigData(productType)
            : this.bookingConfirmationConfig.recommendationConfigData(productType)
        this.logger.info("recommendation view banner config=" + JSON.stringify(bannerConfig))
        if (!_.isEmpty(bannerConfig) && bannerConfig.pageId) {
            const recoPage: ListPage = await this.VMPageBuilder.getPage(bannerConfig.pageId, userContext, {
                "crossSellPageType": CareUtil.getCrossSellPageTypeFromProduct(productType, isPack)
            }) as ListPage
            const recoPageWidgets = []
            if (recoPage && recoPage.body) {
                for (const widget of recoPage.body) {
                    recoPageWidgets.push(widget)
                }
                return recoPageWidgets
            }
        }
    }

    private async buildConfirmationVmRecoWidgets(userContext: UserContext, productType: ProductType) {
        const isIndiaApp = AppUtil.getCountryId(userContext) === "IN"
        if (!isIndiaApp) {
            return []
        }
        if (AppUtil.isWeb(userContext)) {
            return []
        }
        const bannerConfig = this.bookingConfirmationConfig.packRecommendationConfigData(productType)
        this.logger.info("recommendation view banner config=" + JSON.stringify(bannerConfig))
        if (!_.isEmpty(bannerConfig) && bannerConfig.pageId) {
            return await this.getRecoWidgetsForPageId(userContext, productType, bannerConfig.pageId)
        }
        return []
    }

    private async getRecoWidgetsForPageId(userContext: UserContext, productType: ProductType, pageId: string) {
        const recoPage: ListPage = await this.VMPageBuilder.getPage(pageId, userContext, {
            "crossSellPageType": CareUtil.getCrossSellPageTypeFromProduct(productType, true)
        }) as ListPage
        const recoPageWidgets = []
        if (recoPage && recoPage.body) {
            for (const widget of recoPage.body) {
                recoPageWidgets.push(widget)
            }
            return recoPageWidgets
        }
    }
}

export class TeleconsultationSingleConfirmationView extends ConfirmationView {
    constructor(
        userContext: UserContext,
        product: ConsultationProduct,
        user: User,
        bookingDetail: BookingDetail,
        tcDetailsPageConfig: TeleconsultationDetailsPageConfig,
        billingInfo?: BillingInfo,
        isMpV2?: boolean,
        action?: string,
        consultationInstruction?: ConsultationInstructionResponse[],
        bannerJoinVideoWidget?: WidgetWithMetric,
        refferalBannerWidget?: WidgetWithMetric,
        patientsList?: Patient[],
        showFeedbackWidget?: boolean,
        reasons?: string[],
        drivenBannerWidget?: WidgetWithMetric[],
    ) {
        super(CARE_ORDER_CONFIRMATION_ACTION)
        this.widgets = []
        const offline: boolean = CareUtil.isOfflineConsultation(bookingDetail)
        const isLiveWorkoutConsultation: boolean = CareUtil.isLiveWorkoutConsultationDoctorType(bookingDetail.consultationOrderResponse.consultationProduct.doctorType)
        const consultation: ConsultationOrderResponse = bookingDetail.consultationOrderResponse
        const checkoutSummaryWidget = new CareCheckoutSummaryWidget(userContext, user, bookingDetail.booking.productCode, consultation, true, offline, bookingDetail.booking.id.toString(), billingInfo, bookingDetail)
        this.widgets.push(checkoutSummaryWidget)
        if (!offline && !isLiveWorkoutConsultation) {
            if (userContext.sessionInfo.osName === "android") {
                this.widgets.push(new VideoAlertCardWidget("Enable Video For Online Consultation", "Please download the video module prior to your scheduled online video consultation", { actionType: "SHOW_VIDEO_MODULE_DOWNLOAD" }))
            }
            if (AppUtil.isCalendarEventSupportedForLive(userContext.sessionInfo, user)) {
                this.widgets.push(new ClpCalloutWidget({
                    widgetType: "CLP_CALLOUT_WIDGET",
                    subTitle: "Add this Consultation to your Calendar and never miss an appointment",
                    linearGradientColors: ["rgb(255, 193, 214)", "rgb(255, 241, 163)"],
                    icon: "TIMELY",
                    hasDividerBelow: false,
                    dividerType: "SMALL",
                    subTitleColor: "#000000",
                    iconBackgroundColor: "rgba(255, 255, 255, 0)",
                    borderRadius: 10,
                    action: CareUtil.getCareCreateCalendarEventAction(user, userContext, bookingDetail)
                }))
            }

        }
        const isLc = bookingDetail.consultationOrderResponse.consultationProduct.doctorType === "LIFESTYLE_COACH"
        const text = isLc ? "Upload any health check or diagnostic report" : "Let us know more about your problem"
        const isNotDoctorConsultation = CareUtil.isNotDoctorConsulation(bookingDetail.consultationOrderResponse)
        const vertical = CareUtil.getVerticalForConsultation(bookingDetail?.consultationOrderResponse?.consultationProduct?.doctorType)
        if (
            !bookingDetail.consultationOrderResponse.center.isExternal
            && !isNotDoctorConsultation && CareUtil.getChatEnabled(bookingDetail)
            && bookingDetail.consultationOrderResponse?.doctorType !== "ANXIETY_THERAPIST"
        ) {
            this.widgets.push(new ActionableCardWidget("Add details", text, this.getChatAction(userContext, bookingDetail)))
        }
        if (isLiveWorkoutConsultation) {
            this.widgets.push(CareUtil.getLivePTCancellationCutoffWidget(bookingDetail, userContext.userProfile.timezone))
            this.widgets.push(CareUtil.getLivePThowItWorksWidget(userContext))
        } else {
            if (bookingDetail?.consultationOrderResponse?.appointmentActionsWithContext &&
                bookingDetail?.consultationOrderResponse?.consultationProduct &&
                CareUtil.isTherapyOnlyDoctorType(bookingDetail.consultationOrderResponse?.doctorType)
            ) {
                this.widgets.push(CareUtil.getPartnerUpdateInfoWidget(userContext, bookingDetail.consultationOrderResponse.consultationProduct, bookingDetail, patientsList, {
                    actionType: "NAVIGATION",
                    url: ActionUtil.teleconsultationSingle(userContext, bookingDetail.booking.productCode, bookingDetail?.consultationOrderResponse?.consultationProduct.urlPath, bookingDetail.booking.id.toString(), undefined, vertical)
                }))
                if (showFeedbackWidget) {
                    this.widgets.push(CareUtil.getWhyTherapyFeedbackWidget(bookingDetail.consultationOrderResponse.consultationProduct, bookingDetail, reasons))
                }
            }
            if (!_.isEmpty(refferalBannerWidget)) {
                this.widgets.push(refferalBannerWidget)
            }
            if (!_.isEmpty(bannerJoinVideoWidget)) {
                this.widgets.push(bannerJoinVideoWidget)
            }
            this.widgets.push(new InstructionsWidget(isMpV2 ? CareUtil.getInstructionsForMPV2ConsultationAppointments() : CareUtil.getConsulationInstructions(bookingDetail, tcDetailsPageConfig), userContext.sessionInfo.userAgent, true, consultationInstruction))
        }
        if (!_.isEmpty(drivenBannerWidget)) {
            this.widgets = [...this.widgets, ...drivenBannerWidget]
        }
        this.vertical = "CARE_FIT"
        if (isLiveWorkoutConsultation) {
            this.vertical = "CULT_FIT"
        }
        this.header = {
            title: isNotDoctorConsultation ? "Booking Confirmed" : "Appointment Confirmed",
            action: isMpV2 && action ? action : undefined
        }
        if (bookingDetail.consultationOrderResponse?.doctorType === "ANXIETY_THERAPIST") {
            this.widgets.push(CareUtil.getWhatToExpectForAnxietyTherapistBookingConfirmation())
            this.header.title = "Appointment Confirmed"
        }
        if (userContext.sessionInfo.userAgent === "MBROWSER" || userContext.sessionInfo.userAgent === "DESKTOP") {
            this.action = checkoutSummaryWidget.actionUrl
        }
        this.widgets = this.widgets.filter(Boolean)
    }

    private getChatAction(userContext: UserContext, bookingDetail: BookingDetail): string {
        const { patient, doctor } = bookingDetail.consultationOrderResponse
        const name = doctor?.name
        const qualification = doctor?.qualification
        const displayImage = doctor?.displayImage
        const chatAction = CareUtil.getChatMessageActionWithoutContext(userContext, patient.id, name, CareUtil.getChatChannel(bookingDetail), displayImage, qualification, undefined, bookingDetail.booking.id)

        return chatAction.url
    }

}

export class TeleconsultationSingleConfirmationViewV2 extends ConfirmationView {
    constructor(
        userContext: UserContext,
        product: ConsultationProduct,
        user: User,
        bookingDetail: BookingDetail,
        tcDetailsPageConfig: TeleconsultationDetailsPageConfig,
        billingInfo?: BillingInfo,
        isMpV2?: boolean,
        action?: string,
        consultationInstruction?: ConsultationInstructionResponse[],
        bannerJoinVideoWidget?: WidgetWithMetric,
        refferalBannerWidget?: WidgetWithMetric,
        patientsList?: Patient[],
        showFeedbackWidget?: boolean,
        reasons?: string[],
        drivenBannerWidget?: WidgetWithMetric[]
    ) {
        const isTransform = CareUtil.isTransformDoctorType(product.doctorType)
        const isAuroraTheme =  AppUtil.isFromFlutterAppFlow(userContext)
        let pageAction = CARE_ORDER_CONFIRMATION_ACTION
        if (isTransform) {
            pageAction = TRANSFORM_ORDER_CONFIRMATION_V1_ACTION
        } else if (isAuroraTheme) {
            pageAction = CARE_AURORA_ORDER_CONFIRMATION_V1_ACTION
        }
        super(pageAction)
        this.widgets = []
        const offline: boolean = CareUtil.isOfflineConsultation(bookingDetail)
        const vertical = CareUtil.getVerticalForConsultation(product.doctorType)
        let actionUrl = ActionUtil.teleconsultationSingle(
            userContext,
            product.productId,
            product.urlPath,
            bookingDetail.booking.id.toString(),
            undefined,
            vertical
        )
        actionUrl += `&doctorId=${bookingDetail?.consultationOrderResponse?.doctor?.id}`
        this.widgets.push(isTransform ? this.getTransformConfirmWidget(userContext, bookingDetail) : this.getCareTCConfirmWidget(userContext, bookingDetail))
        if (!offline) {
            if (userContext.sessionInfo.osName === "android") {
                this.widgets.push(new VideoAlertCardWidget("Enable Video For Online Consultation", "Please download the video module prior to your scheduled online video consultation", { actionType: "SHOW_VIDEO_MODULE_DOWNLOAD" }))
            }
        }
        this.widgets.push(this.getTCConfirmInfoWidget(userContext, product, bookingDetail, user))
        if (bookingDetail.consultationOrderResponse.endTime >= TimeUtil.getCurrentEpoch()) {
            this.widgets.push(CareUtil.getConsultationModeWidget(
                userContext,
                patientsList,
                user,
                product,
                bookingDetail,
                consultationInstruction,
                tcDetailsPageConfig,
                undefined,
                true
            ))
        }
        if (bookingDetail?.consultationOrderResponse?.appointmentActionsWithContext && CareUtil.isTherapyOnlyDoctorType(bookingDetail.consultationOrderResponse?.doctorType)) {
            this.widgets.push(CareUtil.getPartnerUpdateInfoWidget(userContext, product, bookingDetail, patientsList, { actionType: "NAVIGATION", url: actionUrl }))
            showFeedbackWidget && this.widgets.push(CareUtil.getWhyTherapyFeedbackWidget(product, bookingDetail, reasons))
        }
        if (!_.isEmpty(refferalBannerWidget)) {
            this.widgets.push(refferalBannerWidget)
        }
        if (!_.isEmpty(bannerJoinVideoWidget)) {
            this.widgets.push(bannerJoinVideoWidget)
        }
        this.widgets.push(new InstructionsWidget(isMpV2 ? CareUtil.getInstructionsForMPV2ConsultationAppointments() : CareUtil.getConsulationInstructions(bookingDetail, tcDetailsPageConfig), userContext.sessionInfo.userAgent, true, consultationInstruction))
        if (!_.isEmpty(drivenBannerWidget)) {
            this.widgets = [...this.widgets, ...drivenBannerWidget]
        }
        if (isTransform) {
            this.widgets.push({
                widgetType: "FORMATTED_TEXT_WIDGET",
                header: {
                    title: "",
                },
                data: [
                    {
                        text: "Audio recordings are done based on consent and for quality purposes.",
                        fontWeight: "MEDIUM",
                        fontSize: 15,
                        fontColor: "rgba(0, 0, 0, 0.5)",
                        lineHeight: 25
                    }
                ]
            })
        }
        this.vertical = isTransform ? "TRANSFORM" : "CARE_FIT"
        this.header = {
            title: undefined,
            action: isMpV2 && action ? action : actionUrl
        }
        if (AppUtil.isWeb(userContext)) {
            this.action = ActionUtil.teleconsultationSingle(
                userContext,
                product.productId,
                product.urlPath,
                bookingDetail.booking.id.toString(),
                undefined,
                vertical
            )
        }
        this.widgets = this.widgets.filter(Boolean)
    }
    private getTCConfirmInfoWidget(userContext: UserContext, product: ConsultationProduct, bookingDetail: BookingDetail, user: User): CareCheckoutInfoItemWidget {
        const items: CheckoutInfoItem[] = []
        const isChatEnabled = CareUtil.getChatEnabled(bookingDetail)
        const isFromFlutterApp = AppUtil.isFromFlutterAppFlow(userContext)
        if (AppUtil.isCalendarEventSupportedForLive(userContext.sessionInfo, user)) {
            items.push({
                title: `Add to Calendar`,
                imageType: "SMALL",
                imageUrl: isFromFlutterApp && !CareUtil.isTransformDoctorType(product.doctorType) ? "image/vm/35e81232-5034-4829-beca-6ae2461721ff.png" : "image/transform/Union.png",
                completedImageUrl: "image/transform/bounded_tick_check.png",
                showArrow: true,
                showDivider: isChatEnabled,
                titleStyleType: "MEDIUM_GRAY",
                action: CareUtil.getCareCreateCalendarEventAction(user, userContext, bookingDetail, product.tenant)
            })
        }
        if (isChatEnabled) {
            const { patient, doctor } = bookingDetail.consultationOrderResponse
            const name = doctor?.name
            const qualification = doctor?.qualification
            const displayImage = doctor?.displayImage
            items.push({
                title: CareUtil.isTherapyOnlyDoctorType(product.doctorType) ? "Let us know more about your problem" : CareUtil.isLCProduct(product) ? "Upload any health check or diagnostic report" : "Share reports with doctor",
                imageType: "SMALL",
                imageUrl: isFromFlutterApp ? "image/vm/f34ae50c-95f5-4b19-b6bd-e1f13aa05550.png" : "/image/icons/checkout/care/chat.png",
                showArrow: true,
                titleStyleType: "MEDIUM_GRAY",
                action: CareUtil.getChatMessageActionWithoutContext(userContext, patient.id, name, CareUtil.getChatChannel(bookingDetail), displayImage, qualification, undefined, bookingDetail.booking.id)
            })
        }
        if (_.isEmpty(items)) {
            return undefined
        }
        return {
            widgetType: "CARE_CHECKOUT_INFO_ITEM_WIDGET",
            items
        }
    }

    private getCareTCConfirmWidget(userContext: UserContext, bookingDetail: BookingDetail): CareTCConfirmInfoWidget {
        const consultationInfo = bookingDetail.consultationOrderResponse
        const offline: boolean = CareUtil.isOfflineConsultation(bookingDetail)
        return {
            widgetType: "CARE_TC_CONFIRM_INFO_WIDGET",
            imageUrl: "/image/icons/checkout/care/success_tick.png",
            textInfoItems: [
                {
                    title: `${TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, bookingDetail?.consultationOrderResponse?.startTime, "ddd, D MMM • h:mm a")}`,
                    textStyleType: "BOLD"
                },
                {
                    title: CareUtil.getCheckoutConsultTitle(consultationInfo, offline, consultationInfo?.center?.isExternal, userContext.userProfile.timezone, true),
                    textStyleType: "BOLD_GRAY"
                },
                {
                    title: `With ${consultationInfo.doctor.name}`,
                    textStyleType: "REGULAR"
                },
                {
                    title: `For ${consultationInfo.patient.name}`,
                    textStyleType: "REGULAR"
                }
            ]
        }
    }
    private getTransformConfirmWidget(userContext: UserContext, bookingDetail: BookingDetail): CareTCConfirmInfoWidget {
        const consultationInfo = bookingDetail.consultationOrderResponse
        const offline: boolean = CareUtil.isOfflineConsultation(bookingDetail)
        const nameList = consultationInfo.doctor.name.split(" ")
        const doctorFirstName = nameList.length > 0 ? nameList[0] : consultationInfo.doctor.name
        const startTime = bookingDetail?.consultationOrderResponse?.startTime
        const endTime = bookingDetail?.consultationOrderResponse?.endTime
        const userTimeZone = userContext.userProfile.timezone
        const description = consultationInfo.doctor.qualification
        const updatedDescription = description.replace(/,([^,]*)$/, ",\n$1")

        return {
            widgetType: "CARE_TC_CONFIRM_INFO_WIDGET",
            imageUrl: "/image/icons/checkout/care/success_tick.png",
            textInfoItems: [
                {
                    title: `${TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, bookingDetail?.consultationOrderResponse?.startTime, "ddd, D MMM • h:mm")} - ${TimeUtil.formatEpochInTimeZone(userTimeZone, endTime, "h:mm a")}`,
                    textStyleType: "BOLD"
                },
            ],
            timeStamp: consultationInfo.startTime,
            coachImageUrl: consultationInfo.doctor.displayImage,
            coachName: consultationInfo.doctor.name,
            coachDescription: updatedDescription,

        }
    }
}

export class MultiConsultationConfirmationView extends ConfirmationView {
    constructor(userContext: UserContext, consultationProducts: ConsultationProduct[],
                tcDetailsPageConfig: TeleconsultationDetailsPageConfig, doctors: Doctor[], patient: Patient, order: Order, isMPV2: boolean, action?: string) {
        super(CARE_ORDER_CONFIRMATION_ACTION)
        this.widgets = []
        consultationProducts.map((product, index) => {
            this.widgets.push(this.getMultiConsultationCard(userContext, product, doctors[index], patient, order.products.find(orderProduct => product.productCode === orderProduct.productId)))
        })
        const offline: boolean = consultationProducts[0].consultationMode === "INCENTRE"
        const instructions = isMPV2 ? CareUtil.getInstructionsForMPV2ConsultationAppointments() : offline ? tcDetailsPageConfig.offlineInstructions : tcDetailsPageConfig.onlineInstructions
        this.widgets.push(new InstructionsWidget(instructions, undefined, true))
        this.vertical = "CARE_FIT"
        this.header = {
            title: "Appointments Confirmed",
            action: isMPV2 && action ? action : undefined
        }
    }

    private getMultiConsultationCard(userContext: UserContext, consultationProduct: ConsultationProduct, doctor: Doctor, patient: Patient, orderProduct: OrderProduct): ListingActionableCardWidget {
        const isVideo = consultationProduct.consultationMode === "ONLINE"
        const isLC = consultationProduct.doctorType === "LC"
        const tz = userContext.userProfile.timezone
        const vertical = CareUtil.getCareConsultationProductVertical(consultationProduct)
        const widget: ListingActionableCardWidget = {
            widgetType: "LISTING_ACTIONABLE_CARD_WIDGET",
            tag: undefined,
            showDivider: false,
            title: `${isLC ? "Lifestyle coach" : "Doctor"} Consultation`,
            subTitle: `With ${doctor.name} for ${patient.name}`,
            imageUrl: doctor.displayImage,
            footer: [{
                text: `${isVideo ? `Video |
                    ${TimeUtil.formatEpochInTimeZone(tz, orderProduct.option.startTime, "ddd, D MMM, h:mm A")}` :
                    `At Centre | ${TimeUtil.formatEpochInTimeZone(tz, orderProduct.option.startTime, "ddd, D MMM, h:mm A")}`}`,
                icon: isVideo ? "video" : "incentre"
            }],
            actions: [],
            cardAction: {
                actionType: "NAVIGATION",
                url: ActionUtil.teleconsultationSingle(userContext, orderProduct.productId, consultationProduct.urlPath, orderProduct.option.bookingId.toString(), undefined, vertical)
            }
        }
        return widget
    }


}


export class BundleSingleConfirmationView extends ConfirmationView {
    constructor(userContext: UserContext, product: DiagnosticProduct, bookingDetail: BookingDetail, drivenBannerWidget?: WidgetWithMetric[]) {
        super(CARE_ORDER_CONFIRMATION_ACTION)
        this.widgets = []
        const patient: Patient = bookingDetail.bundleOrderResponse.patient
        const vertical = CareUtil.getVerticalForBundle(product.subCategoryCode)
        const action: Action = {
            actionType: "NAVIGATION",
            url: `${ActionUtil.carefitbundle(product.productId, product.subCategoryCode, bookingDetail.booking.id.toString(), undefined, undefined, undefined, undefined, undefined, userContext.sessionInfo.userAgent)}&vertical=${vertical}`
        }
        let summary
        if (product.subCategoryCode === "HCU") {
            summary = "Thank you for your purchase\nPlease proceed to schedule your test"
        } else if (product.subCategoryCode === "LIVE_PERSONAL_TRAINING" || product.subCategoryCode === "LIVE_SGT") {
            summary = "Thank you for your purchase\nPlease proceed to book session"
        } else if (CareUtil.isTransformTenant(product.subCategoryCode)) {
            summary = "Thank you for your purchase\nDownload app and schedule a call with your coach"
            action.url = "curefit://externalDeepLink?placeUrl=https://cure.app.link/xbulebvTvjb&sameTab=true"
            action.title = "Download App"
        }
        else {
            summary = "Thank you for your purchase\nplease proceed to set-up your pack"
            if (!_.isEmpty(bookingDetail.childBookingInfos) && !_.isEmpty(bookingDetail.childBookingInfos.filter(booking => booking.booking.categoryCode === "DEVICE" && booking.booking.subCategoryCode !== "INT_WELCOME_KIT"))) {
                this.footer = {
                    title: "Device will be handed over on your first care center visit"
                }
            }
        }
        const age = !_.isEmpty(patient) && !_.isEmpty(patient.formattedAge) ? patient.formattedAge.numOfYears : undefined
        const subtitle = age && age !== -1 ? `${patient.name} | ${age} yrs` : `${patient.name}`
        this.widgets.push(new HCUBundleConfirmationSummaryWidget(summary, product.title, subtitle, action))
        if (!_.isEmpty(drivenBannerWidget)) {
            this.widgets = [...this.widgets, ...drivenBannerWidget]
        }
        this.vertical = "CARE_FIT"
        this.header = {
            title: "Purchase successful",
            action: product.subCategoryCode === "HCU_PACK" || product.subCategoryCode === "DIAG_PACK" ? ActionUtil.careFitClp("clphcu") : undefined
        }
        if (product.subCategoryCode === "LIVE_PERSONAL_TRAINING") {
            this.action = "curefit://cultfitclp?pageId=cult&selectedTab=LivePT"
            this.header.action = "curefit://cultfitclp?pageId=cult&selectedTab=LivePT"
        } else if (product.subCategoryCode === "LIVE_SGT") {
            this.action = "curefit://cultfitclp?pageId=cult&selectedTab=LiveSGT"
            this.header.action = "curefit://cultfitclp?pageId=cult&selectedTab=LiveSGT"
        }

        this.widgets = this.widgets.filter(Boolean)
    }
}

export class ChronicCareConfirmationView extends ConfirmationView {
    constructor(userContext: UserContext, bookingDetail: BookingDetail, bundleProduct: DiagnosticProduct) {
        const startDateEpoch = bookingDetail.bundleOrderResponse.startTimeEpoch
        let actionUrl
        let actionTitle
        if (TimeUtil.getCurrentEpoch() < startDateEpoch) {
            // start date is in future.
            actionUrl = "curefit://chroniccarehomepage"
            actionTitle = "Homepage"
        } else {
            // date is in the past
            actionUrl = AppUtil.isSfNuxV2Supported(userContext) ? "curefit://nuxpreferences" : "curefit://preferenceform?pageNumber=1"
            actionTitle = "Start your journey"
        }
        if ((bundleProduct.productSpec as any) ["isRenewal"]) {
            const pageUrl = AppUtil.isSfSubscriptionRenewalV2Supported(userContext) ? "curefit://renewsubscriptionsuccesspage" : "curefit://successpage"
            super(pageUrl)
        } else {
            let type = "PURCHASE_COMPLETE_ANNUAL"
            if (bookingDetail.booking.productCode === "SUGARFIT_WELLNESS") {
                type = "GENERIC"
                actionUrl = "curefit://chroniccarehomepage"
                actionTitle = "Start"
            } else if (bookingDetail.booking.productCode === "SUGARFIT_TRIAL") {
                type = "PURCHASE_COMPLETED_TRIAL"
                actionUrl = "curefit://chroniccarehomepage"
                actionTitle = "Start"
            } else if (bookingDetail.booking.productCode === "SUGARFIT_DIGITAL"
                || bookingDetail.booking.productCode === "SUGARFIT_DIGITAL_TRIAL"
                || bookingDetail.booking.productCode === "SUGARFIT_DIGITAL_PACK_1M"
                || bookingDetail.booking.productCode === "SUGARFIT_DIGITAL_PACK_2M"
                || bookingDetail.booking.productCode === "SUGARFIT_DIGITAL_PACK_3M") {
                type = "PURCHASE_COMPLETED_DIGITAL"
                actionUrl = "curefit://apptabs"
                actionTitle = "Start"
            }
            super(`curefit://congratspage?type=${type}`)
            this.widgets = []
            const title = "Congratulations!"
            const subTitle = "Taking the first step is the toughest. Let us now help you manage your condition and bring out the best in you!"
            const bottomAction: Action = {
                url: actionUrl,
                title: actionTitle,
                actionType: "NAVIGATION"
            }
            this.widgets.push(new CongratulationWidget(title, subTitle, bottomAction, startDateEpoch))
        }
    }
}

export class SfEcomOrderConfirmationView extends ConfirmationView {
    constructor(orderId: string) {
        super(`curefit://sfecommercepaymentsuccess?orderId=${orderId}`)
    }
}

export class ChronicCareCGMConfirmationView extends ConfirmationView {
    constructor(userContext: UserContext, bookingDetail: BookingDetail, bundleProduct: CGMProduct) {
        super(`curefit://successpage?showSuccessText=true`)
    }
}

export class SugarfitGroupClassConfirmationView extends ConfirmationView {
    constructor(bookingId: number, productCode: string) {
        super(`curefit://congratspage?type=GROUP_CLASS&bookingId=${bookingId}&productId=${productCode}&resetNavStack=true`)
    }
}

export class ChronicCareConsultationConfirmationView extends ConfirmationView {
    constructor(userContext: UserContext, consultationType: string, productId: string, bookingId: string, resetNavStack?: boolean) {
        super(`curefit://congratspage?type=CONSULTATION_BOOKED&consultationType=${consultationType}&bookingId=${bookingId}&productId=${productId}&resetNavStack=${resetNavStack}`)
        this.widgets = []
        const title = "Congratulations!"
        const subTitle = "Taking the first step is the toughest. Let us now help you manage your condition and bring out the best in you!"
        const bottomAction: Action = {
            url: AppUtil.isSfNuxV2Supported(userContext) ? "curefit://nuxpreferences" : "curefit://preferenceform?pageNumber=1",
            title: "Start your journey",
            actionType: "NAVIGATION"
        }
        this.widgets.push(new CongratulationWidget(title, subTitle, bottomAction, 0))
    }
}

export class ChronicCareDiagnosticsConfirmationView extends ConfirmationView {
    constructor(userContext: UserContext, orderId: string) {
        const isSfPaidDiagnosticSupported = AppUtil.isSfPaidDiagnosticSupported(userContext)
        super(isSfPaidDiagnosticSupported ? `curefit://sfdiagnostictestordersuccessscreen?orderId=${orderId}` : "curefit://congratspage?type=DIAGNOSTICS_BOOKED")
    }
}

export class ChronicCareDiagnosticsRescheduleConfirmationView extends ConfirmationView {
    constructor(userContext: UserContext) {
        super("curefit://congratspage?type=DIAGNOSTICS_RESCHEDULED")
        this.widgets = []
    }
}

export class ChronicCareConsultationRescheduleConfirmationView extends ConfirmationView {
    constructor(userContext: UserContext, consultationType: string, productId: string, bookingId: string, resetNavStack?: boolean) {
        super(`curefit://congratspage?type=CONSULTATION_RESCHEDULED&consultationType=${consultationType}&bookingId=${bookingId}&productId=${productId}&resetNavStack=${resetNavStack}`)
        this.widgets = []
    }
}

export class MindTherapyPackConfirmationView extends ConfirmationView {
    constructor(product: DiagnosticProduct, bookingDetail: BookingDetail, therapyConfig: TherapyPageConfig, userContext?: UserContext, drivenBannerWidget?: WidgetWithMetric[]) {
        super(CARE_ORDER_CONFIRMATION_ACTION)
        this.widgets = []
        const patient: Patient = bookingDetail.bundleOrderResponse.patient
        const action: Action = {
            actionType: "NAVIGATION",
            url: ActionUtil.carefitbundle(product.productId, product.subCategoryCode, bookingDetail.booking.id.toString(), undefined, undefined, undefined, undefined, undefined, userContext.sessionInfo.userAgent)
        }
        const summary = `Thank you for your purchase\nPlease proceed to schedule session`
        const age = !_.isEmpty(patient) && !_.isEmpty(patient.formattedAge) ? patient.formattedAge.numOfYears : undefined
        const subtitle = age && age !== -1 ? `${patient.name} | ${age} yrs` : `${patient.name}`
        this.widgets.push(new HCUBundleConfirmationSummaryWidget(summary, product.title, subtitle, action))
        this.widgets.push(new InstructionsWidget(therapyConfig.confirmationIntsructions, undefined, true))
        if (!_.isEmpty(drivenBannerWidget)) {
            this.widgets = [...this.widgets, ...drivenBannerWidget]
        }
        this.vertical = "CARE_FIT"
        this.header = {
            title: "Pack Purchased"
        }
        this.widgets = this.widgets.filter(Boolean)
    }
}

export class LuxPackConfirmationView extends ConfirmationView {
    constructor(pack: OfflineFitnessPack, order: Order, userContext: UserContext, memberships?: Membership[]) {
        super(CARE_AURORA_ORDER_CONFIRMATION_V1_ACTION)
        this.widgets = []

        const duration = Math.floor(pack.product.durationInDays / 30)
        const tz = userContext.userProfile.timezone
        let startDateString = null
        if (memberships != null && memberships.length > 0)
            startDateString = TimeUtil.formatEpochInTimeZoneDateFns(tz, memberships[0].start, "yyyy-MM-dd")

        const luxOrderConfirmationSummaryWidget: GymPTOrderConfirmationSummaryWidget = {
            widgetType: "GYM_PT_ORDER_CONFIRMATION",
            imageUrl: "/image/icons/checkout/care/success_tick.png",
            textInfoItems: [
                {
                    title: "Pack Purchased",
                    textStyleType: "PAYMENT_SUCCESS",
                },
                {
                    title: pack.displayName ?? pack.product.title,
                    textStyleType: "HEADING",
                },
                {
                    title: `${duration} ${duration > 1 ? "Months" : "Month"}${startDateString ? (" • Starts on " + startDateString) : ""}`,
                    textStyleType: "DESCRIPTION",
                }
            ]
        }

        this.widgets.push(luxOrderConfirmationSummaryWidget)

        this.vertical = "GYM_FIT"
        this.header = {
            title: "Pack Purchased"
        }
        this.widgets = this.widgets.filter(Boolean)
    }
}

export class FitnessPackConfirmationViewV1 extends ConfirmationView {
    constructor(product: OfflineFitnessPack, order: Order, userContext: UserContext, memberships?: Membership[]) {
        super(CARE_AURORA_ORDER_CONFIRMATION_V1_ACTION)
        this.widgets = []

        const duration = Math.floor(product.product.durationInDays / 30)
        const tz = userContext.userProfile.timezone
        let startDateString = null
        if (memberships != null && memberships.length > 0)
            startDateString = TimeUtil.formatEpochInTimeZoneDateFns(tz, memberships[0].start, "yyyy-MM-dd")

        const orderConfirmationSummaryWidget: GymPTOrderConfirmationSummaryWidget = {
            widgetType: "GYM_PT_ORDER_CONFIRMATION",
            imageUrl: "/image/icons/checkout/care/success_tick.png",
            textInfoItems: [
                {
                    title: "Pack Purchased",
                    textStyleType: "PAYMENT_SUCCESS",
                },
                {
                    title: product.title,
                    textStyleType: "HEADING",
                },
                {
                    title: `${duration} ${duration > 1 ? "Months" : "Month"}${startDateString ? (" • Starts on " + startDateString) : ""}`,
                    textStyleType: "DESCRIPTION",
                }
            ]
        }

        this.widgets.push(orderConfirmationSummaryWidget)

        this.vertical = "CULT_FIT"
        this.header = {
            title: "Pack Purchased"
        }
        this.widgets = this.widgets.filter(Boolean)
    }
}

export class PlayPackConfirmationViewV1 extends ConfirmationView {
    constructor(
        order: Order,
        userContext: UserContext,
        memberships: Membership[],
        offers: Promise<{ [key: string]: OfferV2 }>,
        centerResponsePromise: Promise<CenterResponse>,
    ) {
        super(CARE_AURORA_ORDER_CONFIRMATION_V1_ACTION)
        this.widgets = []

        this.addOrderInfoWidget(
            userContext,
            memberships,
            centerResponsePromise
        )
        this.addOfferAddonWidget(order, offers)

        this.vertical = "PLAY_FIT"
        this.header = {
            title: "Pack Purchased"
        }
        this.widgets = this.widgets.filter(Boolean)
    }

    private async addOrderInfoWidget(
        userContext: UserContext,
        memberships: Membership[],
        centerResponsePromise: Promise<CenterResponse>
    ) {
        const tz = userContext.userProfile.timezone
        let startDateString = null
        let endDateString = null
        if (memberships != null && memberships.length > 0) {
            startDateString = TimeUtil.formatEpochInTimeZoneDateFns(tz, memberships[0].start, "dd MMM, yy")
            endDateString = TimeUtil.formatEpochInTimeZoneDateFns(tz, memberships[0].end, "dd MMM, yy")
        }

        let productTitle = "cultpass PLAY"
        const slpMembership = MembershipItemUtil.isPlaySportLevelMembership(memberships[0])
        const citySlpMembership = MembershipItemUtil.isPlaySportCityLevelMembership(memberships[0])
        if (slpMembership.isSLPPack) {
            const workoutName = PlayUtil.getSportNameById(slpMembership.accessWorkout)
            const centerName = await centerResponsePromise.then(centerResponse => centerResponse.name)
            productTitle = `cultpass ${workoutName} ${centerName}`
        }
        if (citySlpMembership.isCitySLPPack) {
            const workoutName = PlayUtil.getSportNameById(slpMembership.accessWorkout)
            productTitle = `cultpass ${workoutName}`
        }

        const orderConfirmationSummaryWidget: GymPTOrderConfirmationSummaryWidget = {
            widgetType: "GYM_PT_ORDER_CONFIRMATION",
            imageUrl: "/image/icons/checkout/care/success_tick.png",
            textInfoItems: [
                {
                    title: "Pack Purchased",
                    textStyleType: "PAYMENT_SUCCESS",
                },
                {
                    title: productTitle,
                    textStyleType: "HEADING",
                },
                {
                    title: `${startDateString} - ${endDateString}`,
                    textStyleType: "DESCRIPTION",
                }
            ]
        }

        this.widgets.push(orderConfirmationSummaryWidget)
    }

    private async addOfferAddonWidget(
        order: Order,
        offersPromise: Promise<{ [key: string]: OfferV2 }>
    ): Promise<WidgetView> {
        if (_.isEmpty(order.offersApplied)) return

        const offerAddonWidgetParams: IOfferAddonWidgetParams = {
            offerV2MapPromise: offersPromise,
            useLabel: "CART_LABEL"
        }

        offerAddonWidgetParams.title = "Offers Applied"

        let offerAddonWidget: OfferAddonWidget = new OfferAddonWidget()
        offerAddonWidget = await offerAddonWidget.buildView(offerAddonWidgetParams, true, true)
        if (_.isEmpty(offerAddonWidget)) {
            return undefined
        }

        offerAddonWidget.actionSupported = true
        offerAddonWidget.data.offers.forEach(offer => {
            offer.action = {
                actionType: "SHOW_OFFERS_TNC_MODAL",
                meta: {
                    title: "Offer Details",
                    dataItems: offer.tnc,
                    url: offer.tncURL
                }
            }

            offer.prefixImageurl = "/image/icons/cult/offer_icon.svg"
            offer.fontSize = "P5"
            offer.hexColor = "#AAAAAA"
        })

        this.widgets.push(offerAddonWidget)
    }
}

export class GymFitFitnessPackConfirmationViewV1 extends ConfirmationView {
    constructor(product: OfflineFitnessPack, order: Order, userContext: UserContext, memberships?: Membership[]) {
        super(CARE_AURORA_ORDER_CONFIRMATION_V1_ACTION)
        this.widgets = []

        const duration = Math.floor(product.product.durationInDays / 30)
        const tz = userContext.userProfile.timezone
        let startDateString = null
        if (memberships != null && memberships.length > 0)
            startDateString = TimeUtil.formatEpochInTimeZoneDateFns(tz, memberships[0].start, "yyyy-MM-dd")

        const orderConfirmationSummaryWidget: GymPTOrderConfirmationSummaryWidget = {
            widgetType: "GYM_PT_ORDER_CONFIRMATION",
            imageUrl: "/image/icons/checkout/care/success_tick.png",
            textInfoItems: [
                {
                    title: "Pack Purchased",
                    textStyleType: "PAYMENT_SUCCESS",
                },
                {
                    title: product.title,
                    textStyleType: "HEADING",
                },
                {
                    title: `${duration} ${duration > 1 ? "Months" : "Month"}${startDateString ? (" • Starts on " + startDateString) : ""}`,
                    textStyleType: "DESCRIPTION",
                }
            ]
        }

        this.widgets.push(orderConfirmationSummaryWidget)

        this.vertical = "GYM_FIT"
        this.header = {
            title: "Pack Purchased"
        }
        this.widgets = this.widgets.filter(Boolean)
    }
}

export class AddonPackConfirmationViewV1 extends ConfirmationView {
    constructor(product: OfflineFitnessPack) {
        super(CARE_AURORA_ORDER_CONFIRMATION_V1_ACTION)
        this.widgets = []

        const description: string = "Your membership now includes extra pause days"

        const orderConfirmationSummaryWidget: GymPTOrderConfirmationSummaryWidget = {
            widgetType: "GYM_PT_ORDER_CONFIRMATION",
            imageUrl: "/image/icons/checkout/care/success_tick.png",
            textInfoItems: [
                {
                    title: "Pack Purchased",
                    textStyleType: "PAYMENT_SUCCESS",
                },
                {
                    title: product.title,
                    textStyleType: "HEADING",
                },
                {
                    title: description,
                    textStyleType: "DESCRIPTION",
                }
            ]
        }

        this.widgets.push(orderConfirmationSummaryWidget)

        this.vertical = "ADDON"
        this.header = {
            title: "Pack Purchased"
        }
        this.widgets = this.widgets.filter(Boolean)
    }
}


export class GymFitPersonalTrainingPackConfirmationView extends ConfirmationView {
    constructor(product: Product, order: Order, trainerDetails: IdentityResponse, centerResponse: CenterResponse, userContext: UserContext, memberships?: Membership[], agentAssets?: DoctorAssetsResponse, isNoShowBookingConfirmation?: boolean, durationInDays?: number, confirmAction?: Action[]) {
        super(CARE_AURORA_ORDER_CONFIRMATION_V1_ACTION)
        this.widgets = []
        this.confirmAction = confirmAction

        const gymPTOrderConfirmationSummaryWidget: GymPTOrderConfirmationSummaryWidget = {
            widgetType: "GYM_PT_ORDER_CONFIRMATION",
            imageUrl: "/image/icons/checkout/care/success_tick.png",
            textInfoItems: [
                {
                    title: "Pack Purchased",
                    textStyleType: "PAYMENT_SUCCESS",
                },
                {
                    title: product.title,
                    textStyleType: "HEADING",
                },
                {
                    title: trainerDetails && GymfitUtil.capitalizeFirstLetterOfAllWords(trainerDetails.name),
                    textStyleType: "DESCRIPTION",
                },
                {
                    title: centerResponse.name,
                    textStyleType: "DESCRIPTION",
                }
            ]
        }

        const productListWidget: ProductListWidget = {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "SMALL",
            header: {
                title: "Things to know",
                style: {
                    fontSize: 18,
                },
                color: "#000000"
            },
            hideSepratorLines: false,
            hasDividerBelow: false,
            items: [
                {
                    "subTitle": "Reach the center 10 minutes before your slot start time",
                    "icon": "/image/vm/517d1184-a494-4745-af95-e474ec035f2d.png"
                },
                {
                    "subTitle": "Checkin at the attendance tab to enter the gym",
                    "icon": "/image/vm/9cb8176d-24ef-49c6-b0a0-80abcb3d5455.png"
                },
                {
                    "subTitle": "Share your session QR code with the trainer to start your session",
                    "icon": "image/vm/dba4a2d5-2e42-4723-aee2-f0374630cbbe.png"
                }
            ]
        }
        this.widgets.push(gymPTOrderConfirmationSummaryWidget)
        if (memberships && agentAssets) {
            const bookSessionWidget: CareCheckoutInfoItemWidget = {
                widgetType: "CARE_CHECKOUT_INFO_ITEM_WIDGET",
                items: [{
                    title: "Book a training session",
                    subTitle: trainerDetails.name,
                    imageType: "SMALL",
                    imageUrl: agentAssets?.mediaList[0]?.mediaUrl,
                    showArrow: true,
                    titleStyleType: "MEDIUM",
                    subTitleStyleType: "REGULAR_GRAY",
                    subTextStyleType: "REGULAR_GRAY",
                    isCommonImageSpacing: true,
                    action: {
                        actionType: "NAVIGATION",
                        url: "curefit://pt_slot_selection?productId=" + product.productId + "&doctorId=" + trainerDetails.id + "&centerId=" + centerResponse.id + "&patientId=" + userContext.userProfile.userId + "&parentBookingId=" + memberships[0].id + "&isRescheduled=false",
                    }
                }
                ]
            }
            this.widgets.push(bookSessionWidget)
        }
        const topicDetailWidget: WidgetView = {
            widgetType: "TOPIC_DETAILS_LIST_WIDGET",
            title: "Please note",
            items: [
                {
                    subTitle: `Cancel or reschedule your PT session up to ${CANCELLATION_WINDOW_FOR_NO_SHOW} hours before the booked session time. This is to appropriately compensate your trainer for their time.`,
                    icon: "/image/fitnessPlanner/tick.png",
                },
                {
                    subTitle: `Complete your sessions within ${durationInDays} days from purchase, as any remaining sessions will expire.`,
                    icon: "/image/fitnessPlanner/tick.png",
                },
                {
                    subTitle: "No-shows will count towards your total sessions.",
                    icon: "/image/fitnessPlanner/tick.png",
                },
                {
                    subTitle: "Remember to scan your session QR code with your personal trainer to avoid it being counted as a no-show.",
                    icon: "/image/fitnessPlanner/tick.png",
                },
                {
                    subTitle: "For any no-shows marked due to trainer unavailability or tech problems, contact the center manager for a credit.",
                    icon: "/image/fitnessPlanner/tick.png",
                },
            ],
            noBorderIcon: true,
        }

        if (isNoShowBookingConfirmation) {
            this.widgets.push(topicDetailWidget)
        } else {
            this.widgets.push(productListWidget)
        }

        this.vertical = "GYM_FIT"
        this.header = {
            title: "Pack Purchased"
        }
        this.widgets = this.widgets.filter(Boolean)
    }
}

export class GymFitPersonalTrainingSlotConfirmationView extends ConfirmationView {
    constructor(date: string, trainerDetails: IdentityResponse, centerResponse: CenterResponse, isNoShowBookingConfirmation?: boolean, durationInDays?: number, userContext?: UserContext, isPpcSession?: boolean, confirmAction?: Action[], muscleBlazeBanner?: WidgetView) {
        super(CARE_AURORA_ORDER_CONFIRMATION_V1_ACTION)
        this.widgets = []

        this.confirmAction = confirmAction

        const gymPTOrderConfirmationSummaryWidget: GymPTOrderConfirmationSummaryWidget = {
            widgetType: "GYM_PT_ORDER_CONFIRMATION",
            imageUrl: "/image/icons/checkout/care/success_tick.png",
            textInfoItems: [
                {
                    title: "Session Confirmed",
                    textStyleType: "PAYMENT_SUCCESS",
                },
                {
                    title: date,
                    textStyleType: "HEADING",
                },
                {
                    title: trainerDetails && GymfitUtil.capitalizeFirstLetterOfAllWords(trainerDetails.name),
                    textStyleType: "DESCRIPTION",
                },
                {
                    title: centerResponse.name,
                    textStyleType: "DESCRIPTION",
                }
            ]
        }

        const gymConfimWidget: any = {
          widgetType: "ORDER_CONFIRMATION_INFO_WIDGET",
          textInfoItems: [
            {
              title: trainerDetails && GymfitUtil.capitalizeFirstLetterOfAllWords(trainerDetails.name),
              styleProps: {
                variant: "paragraph4Text",
              },
            },
            {
              title: date,
              styleProps: {
                variant: "header1Text",
              },
            },
          ],
          orderMeta: {
            title: centerResponse.name,
            isDropDown: true,
            dropDownInfo: [
              {
                icon: "location-small",
                subTitle:
                centerResponse.fullAddress1,
                cardAction: {
                  actionType: "EXTERNAL_DEEP_LINK",
                  url: centerResponse.mapUrl,
                  title: "NAVIGATE",
                },
              },
            ],
            styleProps: {
              variant: "paragraph5Text",
            },
            icon: "location-small",
          },
        }

        const notesForPpcSession = [
            {
                "subTitle": "Reach the center 10 minutes before your slot start time",
                "icon": "/image/vm/517d1184-a494-4745-af95-e474ec035f2d.png"
            },
            {
                "subTitle": "Your trainer will assess your body type and fitness level",
                "icon": "/image/vm/9cb8176d-24ef-49c6-b0a0-80abcb3d5455.png"
            },
            {
                "subTitle": "For best results, share specific, measureable and time bound goals with your trainer",
                "icon": "image/vm/dba4a2d5-2e42-4723-aee2-f0374630cbbe.png"
            }
        ]

        const productListWidget: ProductListWidget = {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "SMALL",
            header: {
                title: "Things to know",
                style: {
                    fontSize: 18,
                },
                color: "#000000"
            },
            hideSepratorLines: false,
            hasDividerBelow: false,
            items: isPpcSession ? notesForPpcSession : [
                {
                    "subTitle": "Reach the center 10 minutes before your slot start time",
                    "icon": "/image/vm/517d1184-a494-4745-af95-e474ec035f2d.png"
                },
                {
                    "subTitle": "Checkin at the attendance tab to enter the gym",
                    "icon": "/image/vm/9cb8176d-24ef-49c6-b0a0-80abcb3d5455.png"
                },
                {
                    "subTitle": "Share your session QR code with the trainer to start your session",
                    "icon": "image/vm/dba4a2d5-2e42-4723-aee2-f0374630cbbe.png"
                }
            ]
        }

        const topicDetailWidget: WidgetView = {
            widgetType: "TOPIC_DETAILS_LIST_WIDGET",
            title: "Please note",
            items: [
                {
                    subTitle: `Cancel or reschedule your PT session up to ${CANCELLATION_WINDOW_FOR_NO_SHOW} hours before the booked session time. This is to appropriately compensate your trainer for their time.`,
                    icon: "/image/fitnessPlanner/tick.png",
                },
                {
                    subTitle: `Complete your sessions within ${durationInDays} days from purchase, as any remaining sessions will expire.`,
                    icon: "/image/fitnessPlanner/tick.png",
                },
                {
                    subTitle: "No-shows will count towards your total sessions.",
                    icon: "/image/fitnessPlanner/tick.png",
                },
                {
                    subTitle: "Remember to scan your session QR code with your personal trainer to avoid it being counted as a no-show.",
                    icon: "/image/fitnessPlanner/tick.png",
                },
                {
                    subTitle: "For any no-shows marked due to trainer unavailability or tech problems, contact the center manager for a credit.",
                    icon: "/image/fitnessPlanner/tick.png",
                },
            ],
            noBorderIcon: true,
        }

        this.widgets.push(gymPTOrderConfirmationSummaryWidget)
        if (!_.isEmpty(muscleBlazeBanner)) {
            this.widgets.push(muscleBlazeBanner)
        }
        if (isNoShowBookingConfirmation) {
            this.widgets.push(topicDetailWidget)
        } else {
            this.widgets.push(productListWidget)
        }

        this.vertical = "GYM_FIT"
        this.header = {
            title: "Session Confirmed"
        }
        this.widgets = this.widgets.filter(Boolean)
    }
}


export class BundleSessionPackConfirmationView extends ConfirmationView {
    constructor(product: DiagnosticProduct, bookingDetail: BookingDetail) {
        super(CARE_ORDER_CONFIRMATION_ACTION)
        this.widgets = []
        const patient: Patient = bookingDetail.bundleOrderResponse.patient
        const action: Action = {
            actionType: "NAVIGATION",
            url: ActionUtil.carefitbundle(product.productId, product.subCategoryCode, bookingDetail.booking.id.toString())
        }
        const summary = `Thank you for your purchase\nPlease proceed to schedule session`
        const age = !_.isEmpty(patient) && !_.isEmpty(patient.formattedAge) ? patient.formattedAge.numOfYears : undefined
        const subtitle = age && age !== -1 ? `${patient.name} | ${age} yrs` : `${patient.name}`
        this.widgets.push(new HCUBundleConfirmationSummaryWidget(summary, product.title, subtitle, action))
        this.vertical = "CARE_FIT"
        this.header = {
            title: "Pack Purchased"
        }
    }
}

export class DiagnosticSingleConfirmationView extends ConfirmationView {
    constructor(bookingDetail: BookingDetail, parentBooking: BookingDetail, userContext: UserContext, drivenBannerWidget?: WidgetWithMetric[]) {
        const action: Action = {
            actionType: "NAVIGATION",
            url: ["HCU_PACK", "DIAG_PACK"].includes(parentBooking?.booking?.subCategoryCode) && (userContext.sessionInfo.appVersion >= 8.53 || AppUtil.isWeb(userContext))
                ? ActionUtil.carefitbundle(parentBooking?.booking?.productCode, parentBooking?.booking?.subCategoryCode, parentBooking?.booking?.id.toString())
                : ActionUtil.diagnostics(bookingDetail.booking.productCode, bookingDetail.booking.id.toString())
        }
        if (AppUtil.isWeb(userContext)) {
            super(action.url)
        } else {
            super(CARE_ORDER_CONFIRMATION_ACTION)
        }
        this.widgets = []
        const testResponse: DiagnosticsTestOrderResponse = bookingDetail.diagnosticsTestOrderResponse[0]
        const tz = userContext.userProfile.timezone
        let title: string
        let icon: string
        let subtitle1: string
        let subtitle2: string
        if (!_.isEmpty(testResponse.atHomeDiagnosticOrder)) {
            const atHomeTime = CareUtil.getHomeCollectionTimeText(testResponse.atHomeDiagnosticOrder.startTime, testResponse.atHomeDiagnosticOrder.endTime, userContext)
            if (!_.isEmpty(testResponse.inCentreDiagnosticOrder)) {
                const atCenterTime = TimeUtil.formatEpochInTimeZone(tz, testResponse.inCentreDiagnosticOrder.slot.workingStartTime, "ddd, D MMM, h:mm A")
                title = "Diagnostic tests home + center"
                icon = "homeCenter"
                subtitle1 = `Home | ${atHomeTime}`
                subtitle2 = `Center | ${atCenterTime}`
            } else {
                title = "Diagnostic tests at home"
                icon = "home"
                subtitle1 = atHomeTime
            }
        } else {
            const atCenterTime = TimeUtil.formatEpochInTimeZone(tz, testResponse.inCentreDiagnosticOrder.slot.workingStartTime, "ddd, D MMM, h:mm A")
            title = "Diagnostic tests at center"
            icon = "center"
            subtitle1 = atCenterTime
        }
        this.widgets.push(new DiagnosticsConfirmationSummaryWidget(title, subtitle1, subtitle2, icon, action))
        this.widgets.push(new InstructionsWidget(CareUtil.getTestInstructions(testResponse.inCentreInstructions, testResponse.atHomeInstructions), undefined, true))
        if (!_.isEmpty(drivenBannerWidget)) {
            this.widgets = [...this.widgets, ...drivenBannerWidget]
        }
        this.vertical = "CARE_FIT"
        this.header = {
            title: "Booking Confirmed"
        }
        this.widgets = this.widgets.filter(Boolean)
    }
}

export class NewDiagnosticsConfirmationView extends ConfirmationView {

    constructor(userContext: UserContext, order: Order, bookingDetail: BookingDetail, productInfos: HealthfaceProductInfo[], billingInfo: BillingInfo, productCodes: string[], patientDetails: Patient, reportIssues: IssueDetailView[], drivenBannerWidget?: WidgetWithMetric[]) {
        const action: Action = {
            actionType: "NAVIGATION",
            url: ActionUtil.diagnostics(bookingDetail.booking.productCode, bookingDetail.booking.id.toString())
        }
        const isWeb = AppUtil.isWeb(userContext)
        const isDesktop = AppUtil.isDesktop(userContext)

        if (isWeb) {
            super(action.url)
        } else {
            super(CARE_ORDER_CONFIRMATION_ACTION)
        }

        this.widgets = []

        if (isWeb) {
            this.pageAction = action
            this.meta = { showDiagnosticConfirmation: true }
        }

        const headerAction = this.getHeaderAction(bookingDetail, reportIssues)

        if (isDesktop) {
            this.widgets.push(this.getDiagnosticConfirmationDesktopWidget(userContext, order, bookingDetail, productInfos, billingInfo, productCodes, patientDetails))
        } else {
            this.widgets.push(this.getDiagnosticsConfirmationV2Widget(userContext, order, bookingDetail, productInfos, billingInfo, productCodes, patientDetails, reportIssues))
        }

        !isWeb && this.widgets.push(new ClpCalloutWidget({
            widgetType: "CLP_CALLOUT_WIDGET",
            title: "Add Home Collection to your calendar",
            linearGradientColors: ["#ffffff", "#ffffff"],
            icon: "TIMELY",
            hasDividerBelow: false,
            dividerType: "SMALL",
            titleStyle: { color: "#888e9e", fontSize: 14 },
            iconBackgroundColor: "#ffffff",
            borderRadius: 10,
            action: CareUtil.getDiagnosticHomeCollectionCalendarEventAction(userContext, bookingDetail),
            containerStyle: {
                marginLeft: 20,
                marginRight: 20,
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.2,
                shadowRadius: 2,
                elevation: 3,
                backgroundColor: "#ffffff",
                borderRadius: 10,
            }
        }))

        if (!_.isEmpty(drivenBannerWidget)) {
            this.widgets = [...this.widgets, ...drivenBannerWidget]
        }
        this.header = {
            title: "Your Order",
            newCheckoutNavigation: true,
            action: headerAction
        }
        this.vertical = "CARE_FIT"
        this.widgets = this.widgets.filter(Boolean)
    }

    getDiagnosticConfirmationDesktopWidget(userContext: UserContext, order: Order, bookingDetail: BookingDetail, productInfos: HealthfaceProductInfo[], billingInfo: BillingInfo, productCodes: string[], patientDetails: Patient): IDiagnosticsConfirmationDesktopWidget {
        const testResponse: DiagnosticsTestOrderResponse = bookingDetail.diagnosticsTestOrderResponse[0]
        const totalProducts = productCodes.length
        const testList: IDiagnosticConfirmationOrderProductDetail[] = []

        let timeText: string,
            address: string,
            name: string,
            gender: string,
            age: string,
            dateText: string,
            headingSection: IDiagnosticConfirmationHeading,
            testDetails: IDiagnosticConfirmationTestDetails,
            reportReadyEta: number = 0,
            paymentDetails: IDiagnosticConfirmationPaymentDetails,
            totalTestCount: number = 0

        if (!_.isEmpty(testResponse.atHomeDiagnosticOrder)) {
            timeText = CareUtil.getHomeCollectionTimeText(testResponse.atHomeDiagnosticOrder.startTime, testResponse.atHomeDiagnosticOrder.endTime, userContext)
            address = !_.isEmpty(testResponse.atHomeDiagnosticOrder.addressMetadata) ? testResponse.atHomeDiagnosticOrder.addressMetadata.address : ""
        }

        name = patientDetails.name
        gender = patientDetails.gender
        age = patientDetails.age ? `${patientDetails.age} yrs` : ""
        dateText = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, testResponse.atHomeDiagnosticOrder.sampleCollectionTime, "D MMM YYYY")

        productCodes.map(productCode => {
            const productInfo = productInfos.find(productInfo => productInfo.baseSellableProduct.productCode === productCode)
            const reportingTat = productInfo?.baseSellableProduct?.diagnosticProductResponse?.reportingTat
            const countTests = CareUtil.countParameters(productInfo?.baseSellableProduct?.diagnosticProductResponse)
            const countTestsText = `${countTests}${countTests > 1 ? ` Tests` : ` Test`}`

            reportReadyEta = Math.max(reportReadyEta, reportingTat)
            totalTestCount += countTests

            testList.push({ name: productInfo?.baseSellableProduct?.productName, countTestsText })
        })

        testDetails = {
            reportReadyText: `Reports in ${reportReadyEta} Hrs`,
            itemCountText: `${totalProducts} ITEM${totalProducts > 1 ? "S" : ""}`,
            testCountText: `${totalTestCount} TEST${totalTestCount > 1 ? "S" : ""}`,
            testList
        }

        headingSection = {
            name,
            gender,
            age,
            dateText,
            timeText,
            address
        }

        paymentDetails = {
            title: "Payment Details",
            priceDetails: this.getPriceDetails(billingInfo, order)
        }

        return new DiagnosticsConfirmationDesktopWidget("", headingSection, testDetails, paymentDetails)
    }

    getDiagnosticsConfirmationV2Widget(userContext: UserContext, order: Order, bookingDetail: BookingDetail, productInfos: HealthfaceProductInfo[], billingInfo: BillingInfo, productCodes: string[], patientDetails: Patient, reportIssues: IssueDetailView[]): IDiagnosticsConfirmationV2Widget {
        let testDetails,
            reportReadyEta,
            patientInfo,
            slotDetails,
            homeAddressDetails,
            maxReportEta = 0,
            priceDetails,
            headerAction,
            summaryDetails,
            orderDetails,
            paymentDetails,
            headerDetails,
            totalParameters = 0
        const orderProductDetails: { name: string, countTestsText: string }[] = []
        const testResponse: DiagnosticsTestOrderResponse = bookingDetail.diagnosticsTestOrderResponse[0]
        const totalProducts = productCodes.length

        if (!_.isEmpty(testResponse.atHomeDiagnosticOrder)) {
            slotDetails = CareUtil.getHomeCollectionTimeText(testResponse.atHomeDiagnosticOrder.startTime, testResponse.atHomeDiagnosticOrder.endTime, userContext)
            homeAddressDetails = !_.isEmpty(testResponse.atHomeDiagnosticOrder.addressMetadata) ? testResponse.atHomeDiagnosticOrder.addressMetadata.address : undefined
        }

        productCodes.map(productCode => {
            const productInfo = productInfos.find(productInfo => productInfo.baseSellableProduct.productCode === productCode)
            const reportingTat = productInfo?.baseSellableProduct?.diagnosticProductResponse?.reportingTat
            maxReportEta = maxReportEta <= reportingTat ? reportingTat : maxReportEta
            const countTests = CareUtil.countParameters(productInfo?.baseSellableProduct?.diagnosticProductResponse)
            totalParameters += countTests
            const countTestsText = `${countTests}${countTests > 1 ? ` Tests` : ` Test`}`
            orderProductDetails.push({ name: productInfo?.baseSellableProduct?.productName, countTestsText })
        })

        reportReadyEta = `Reports in ${maxReportEta} Hrs`
        testDetails = `${totalProducts}${totalProducts > 1 ? ` ITEMS` : ` ITEM`} | ${totalParameters}${totalParameters > 1 ? ` TESTS` : ` TEST`}`
        patientInfo = patientDetails ? `For ${patientDetails.name} | ${patientDetails.gender} | ${patientDetails.age}` : undefined

        priceDetails = this.getPriceDetails(billingInfo, order)
        headerAction = this.getHeaderAction(bookingDetail, reportIssues)
        summaryDetails = { title: "Order Confirmed", testDetails, reportReadyEta, patientInfo, slotDetails, homeAddressDetails }
        orderDetails = { title: "Order Details", orderProductDetails }
        paymentDetails = { title: "Payment Details", priceDetails }
        headerDetails = { title: "Your Order", headerAction }

        return new DiagnosticsConfirmationV2Widget(summaryDetails, orderDetails, paymentDetails, headerDetails)
    }

    getHeaderAction(bookingDetail: BookingDetail, reportIssues: IssueDetailView[]): Action {
        const actions: Action[] = []
        if (!_.isEmpty(reportIssues)) {
            const reportAction = this.getReportAction(reportIssues)
            actions.push(reportAction)
        }
        const rescheduleAction = this.getRescheduleAction(bookingDetail)
        actions.push(rescheduleAction)
        const cancelAction = this.getCancelAction(bookingDetail)
        actions.push(cancelAction)
        return {
            actionType: "ACTION_LIST",
            actions
        }
    }
    getReportAction(reportIssues: IssueDetailView[]): Action {
        const action: Action = {
            title: "Contact Support",
            actionType: "NAVIGATION",
            url: SUPPORT_DEEP_LINK,
            meta: {
                issues: reportIssues
            }
        }
        return action
    }
    getCancelAction(bookingDetail: BookingDetail): Action {
        const completionAction = { actionType: "NAVIGATION", url: "curefit://listpage?pageId=clphcu" }
        return {
            title: "Cancel and Refund",
            actionType: "CANCEL_HCU_TEST",
            meta: { tcBookingId: bookingDetail.booking.id },
            completionAction
        }
    }
    getRescheduleAction(bookingDetail: BookingDetail): Action {
        const diagnosticsTestOrderResponse = bookingDetail.diagnosticsTestOrderResponse[0]
        const addressId = diagnosticsTestOrderResponse?.atHomeDiagnosticOrder?.addressMetadata?.addressId
        const productId = diagnosticsTestOrderResponse.productCodes[0]
        const parentBookingId = diagnosticsTestOrderResponse.bookingId
        const productCodes = diagnosticsTestOrderResponse.productCodes.join(",")
        const patientId = diagnosticsTestOrderResponse.patient.id
        if (addressId) {
            return {
                title: "Reschedule",
                actionType: "NAVIGATION",
                url: `curefit://selectCareDateV1?patientId=${patientId}&productId=${productId}&parentBookingId=${parentBookingId}
                    &type=DIAGNOSTICS&category=AT_HOME_SLOT&isReschedule=true&productCodes=${productCodes}&addressIdV1=${addressId}`
            }
        }
    }
    getPriceDetails(billingInfo: BillingInfo, order?: Order): PriceComponent[] {
        const priceDetails: PriceComponent[] = []
        priceDetails.push({
            title: "Total",
            value: billingInfo.mrp.toFixed(2)
        })
        if (billingInfo.discount) {
            priceDetails.push(...OrderUtil.getDiscountPriceDetails(billingInfo))
        }

        if (billingInfo.fitCashPayable) {
            priceDetails.push({
                title: "Fitcash Discount",
                value: billingInfo.fitCashPayable.toFixed(2),
                isDiscount: true
            })
        }

        let total = billingInfo.total
        if (order?.collectionCharges?.total > 0) {
            priceDetails.push({
                title: `Home Sample Collection Charges`,
                value: order?.collectionCharges?.total.toString(),
            })
            total = total + order.collectionCharges.total
        }

        priceDetails.push({
            title: "Total Paid",
            value: total.toFixed(2)

        })
        return priceDetails
    }
}

abstract class FitnessClassConfirmationView extends ConfirmationView {
    constructor(userContext: UserContext, cultBooking: CultBooking) {
        super(ORDER_CONFIRMATION_ACTION)
        this.header = this.getOrderConfirmationHeader(userContext, cultBooking)
        this.body = this.getActivityDescription(cultBooking)
        this.footer = this.getOrderConfirmationFooter(cultBooking.Class, userContext)
        this.vertical = this.getVertical()
        this.widgets = [CultUtil.getCultBookingNextStepsWidget(cultBooking)]
        this.meta = {
            isCultUnbound: cultBooking?.isCultUnbound
        }
    }

    protected abstract getVertical(): Vertical

    protected abstract getActivityAction(cultBooking: CultBooking): string

    private getOrderConfirmationHeader(userContext: UserContext, cultBooking: CultBooking): Header {
        const tz = userContext.userProfile.timezone
        const cultClass = cultBooking.Class
        const workout: CultWorkout = cultClass.Workout
        const center: CultCenter = cultBooking.Center
        const classDateTime = TimeUtil.parseDateTime(cultClass.date + " " + cultClass.startTime, tz)
        let subTitle, creditsData
        if (workout.id === RUNNING_EVENT_WORKOUT_ID) {
            subTitle = `Your ${this.getActivityName()} for ${workout.name} on ${TimeUtil.formatDateStringInTimeZone(cultClass.date, tz, "ddd, D MMM")}`
                + " at " + TimeUtil.get12HRTimeFormat(classDateTime, tz, true)
                + " is confirmed!"
        } else {
            subTitle = `Your ${this.getActivityName()} for ${workout.name} on ${TimeUtil.formatDateStringInTimeZone(cultClass.date, tz, "ddd, D MMM")}`
                + " at " + TimeUtil.get12HRTimeFormat(classDateTime, tz, true)
                + " at " + center.name + " is confirmed!"
        }
            creditsData = cultBooking.creditCost ? new CreditsView(cultBooking.creditCost, `${cultBooking.creditCost > 1 ? "Credits" : "Credit"} used for this centre booking`) : null
        const headerWidget: Header = {
            title: "Class booked!",
            subTitle: subTitle,
            creditsData
        }
        return headerWidget
    }

    private getActivityDescription(cultBooking: CultBooking): UserActivity {
        const cultClass = cultBooking.Class
        const userActivityId = cultClass.id.toString()
        const activityDate = cultClass.date
        const bookCultActivity: UserActivity = {
            date: activityDate,
            activityType: this.getActivityType(),
            subActivityType: cultClass.Workout.name.toUpperCase(),
            activityName: this.getActivityName(),
            status: "TODO",
            title: cultClass.Workout.name,
            action: this.getActivityAction(cultBooking),
            userActivityId: userActivityId
        }
        return bookCultActivity
    }

    private getOrderConfirmationFooter(cultClass: CultClass, userContext: UserContext): Header {
        const footer: Header = {
            // title: `You will see this class in your Today on ${momentTz.tz(cultClass.date, "Asia/Kolkata").format("ddd, D MMM")}.` // TODO: Add back once todo is available on web
            title: `You will see this class in your Plan screen on ${TimeUtil.getDayText(cultClass.date, userContext.userProfile.timezone)}`
        }
        return footer
    }

    protected abstract getActivityName(): string

    protected abstract getActivityType(): ActivityType
}

class CultFitnessClassConfirmationView extends FitnessClassConfirmationView {
    protected getActivityName(): string {
        return "Cult class"
    }

    protected getActivityType(): ActivityType {
        return "CULT_CLASS"
    }

    protected getVertical(): Vertical {
        return "CULT_FIT"
    }

    protected getActivityAction(cultBooking: CultBooking): string {
        return `curefit://cultclass?bookingNumber=${cultBooking.bookingNumber}`
    }
}

class MindFitnessClassConfirmationView extends FitnessClassConfirmationView {
    protected getActivityName(): string {
        return "mind.fit class"
    }

    protected getActivityType(): ActivityType {
        return "MIND_CLASS"
    }

    protected getVertical(): Vertical {
        return "MIND_FIT"
    }

    protected getActivityAction(cultBooking: CultBooking): string {
        return `curefit://mindclass?bookingNumber=${cultBooking.bookingNumber}`
    }
}

class ProgramPackConfirmationView extends ConfirmationView {
    constructor(userContext: UserContext, product: Product, order: Order, programPack: ProgramPack) {
        super(ORDER_CONFIRMATION_ACTION)
        this.header = this.getOrderConfirmationHeader(product)
        const orderProduct: OrderProduct = order.products[0]
        this.body = this.getActivityDescription(userContext, orderProduct.option.centerId, product.productId, programPack)
        this.footer = this.getOrderConfirmationFooter()
        this.vertical = "MIND_FIT"
    }

    private getOrderConfirmationHeader(product: Product): Header {
        const headerWidget: Header = {
            title: "Order Confirmed!",
            subTitle: `Welcome to your ${product.title} pack!`
        }
        return headerWidget
    }

    private getActivityDescription(userContext: UserContext, centerId: string, productId: string, programPack: ProgramPack): UserActivity {
        const userActivity: UserActivity = {
            date: TimeUtil.todaysDateWithTimezone(userContext.userProfile.timezone),
            activityType: programPack.tenantId === "mind.fit" ? "DIY_MEDITATION" : "DIY_FITNESS",
            activityName: "Book your sessions",
            status: "TODO",
            title: "",
            action: "curefit://today",
            userActivityId: productId
        }
        if (programPack.meta.userEvaluation && !programPack.meta.userEvaluation.completed) {
            userActivity.activityName = "Take the quiz"
            const action: ChainedAction = {
                actionType: "OPEN_TYPEFORM",
                url: programPack.meta.userEvaluation.meta.typeFormUrl,
                nextAction: {
                    actionType: "REST_API",
                    meta: {
                        method: "POST",
                        url: "/program/syncEvaluation/",
                        body: {
                            evaluationId: programPack.meta.userEvaluation.evaluationId,
                            userEvaluationId: programPack.meta.userEvaluation.id
                        }
                    }
                }
            }
            userActivity.activityAction = action
        } else {
            userActivity.activityAction = {
                actionType: "NAVIGATION",
                url: "curefit://today"
            }
        }

        return userActivity
    }

    private getOrderConfirmationFooter(): Header {
        const footer: Header = {
            title: `Congratulations !All the best for your fitness journey.`
        }
        return footer
    }
}


abstract class FitnessPackConfirmationView extends ConfirmationView {
    constructor(product: OfflineFitnessPack, order: Order, userContext: UserContext,
        offers: { [key: string]: OfferV2 }, isInternalUser: boolean,
        membershipId?: string, recoWidgets?: WidgetWithMetric[], memExpDL?: string) {
        super(AppUtil.isNewPackOrderConformationSupported(userContext, isInternalUser) ? ORDER_CONFIRMATION_V1_ACTION : ORDER_CONFIRMATION_ACTION)
        this.header = this.getOrderConfirmationHeader(product, userContext, isInternalUser)
        const orderProduct: OrderProduct = order.products[0]
        this.body = this.getActivityDescription(orderProduct.option.centerId, product, userContext, membershipId)
        this.widgets = []
        if (AppUtil.isNewPackOrderConformationSupported(userContext, isInternalUser)) {
            if (AppUtil.isAuroraOrderConfirmationSupported(userContext)) {
                this.widgets.push(this.getOrderConfirmationInfoWidget(order, userContext, product))
            } else {
                this.widgets.push(new PackOrderInfoWidget(
                    this.header.title,
                    this.header.subTitle,
                    "TICK_GREEN",
                    { actionType: "NAVIGATION", url: product.productType === "FITNESS" ? "curefit://tabpage?pageId=cult" : "curefit://tabpage?pageId=mind" }
                ))
            }
            if (!_.isEmpty(order.payments)) {
                let isNoCostEmiOfferApplied = false
                order.payments.forEach((payment) => {
                    // Instant discount amount check is to see if user actually chose emi option on payment page
                    if (payment.instantDiscount && payment.instantDiscount.amount && payment.instantDiscount.isNoCostEmi) {
                        isNoCostEmiOfferApplied = true
                    }
                })
                if (isNoCostEmiOfferApplied) {
                    this.widgets.push(this.getNoCostEmiWidget(order))
                }
                const orderOffers = this.getOrderOffers(offers, userContext, isInternalUser)
                if (orderOffers) {
                    this.widgets.push(orderOffers)
                }
            }
            if (!_.isEmpty(recoWidgets)) {
                this.widgets.push(...recoWidgets as any[])
            }
        }
        this.footer = this.getOrderConfirmationFooter()
        this.vertical = "CULT_FIT"

        this.pageActions = []
        this.triggerUserStatus = false
        if (memExpDL) {
            const onboardingAction: Action = {
                title: "CREATE PLAN",
                actionType: "NAVIGATION",
                viewType: "PRIMARY_BUTTON",
                url: memExpDL,
            }
            this.triggerUserStatus = true
            this.pageActions.push(onboardingAction)
        }
    }

    private getOrderConfirmationInfoWidget(order: Order, userContext: UserContext, product: OfflineFitnessPack): any {
        const productSnapshot = order?.productSnapshots[0]
        const words = productSnapshot?.title?.split(" ")
        const packDays = productSnapshot?.attributes?.numDays
        const tz = userContext.userProfile.timezone
        const date = TimeUtil.parseDate(productSnapshot?.option?.startDate, tz)
        date?.setDate(date?.getDate() + packDays)
        const endDate = TimeUtil.formatDateInTimeZone(tz, date, "DD MMM, YY")
        let title: string[] = []
        let packEnding: string = ""

        if (product.productType === "PLAY") {
            const startDate = TimeUtil.parseDate(productSnapshot?.option?.startDate, tz)
            const startDateString = TimeUtil.formatDateInTimeZone(tz, startDate, "DD MMM, YY")

            packEnding = `${startDateString} - ${endDate}`
            title = words
        } else {
            const startDate = TimeUtil.parseDate(productSnapshot?.option?.startDate, tz)
            const startDateString = TimeUtil.formatDateInTimeZone(tz, startDate, "DD MMM, YY")

            packEnding = `Starts on ${startDateString}`
            title = words.splice(-2)
        }

        const heading = title?.map(t => t + " ")
        return {
            widgetType: "ORDER_CONFIRMATION_INFO_WIDGET",
            textInfoItems: [
                {
                    title: heading,
                    styleProps: {
                        variant: "header1Text",
                    },
                },
                {
                    title: packEnding,
                    styleProps: {
                        variant: "paragraph4Text",
                        opacity: 0.6,
                        color: "primary",
                    },
                }
            ]
        }
    }
    private getNoCostEmiWidget(order: Order): HighlightInfoWidget {
        return new HighlightInfoWidget(
            "important!",
            "You’ve successfully availed No Cost EMI. Don't worry if you get an SMS from your bank for the full charge. It’ll be converted into EMI in 4-7 days.",
            {
                title: "Know More",
                actionType: "NAVIGATION",
                url: OrderUtil.getNoCostEmiKnowMoreUrl(order)
            }
        )
    }

    private getOrderOffers(offers: { [key: string]: OfferV2 }, userContext: UserContext, isInternalUser: boolean): any {
        let offersList: OfferV2[] = _.map(offers, offer => offer)
        if (AppUtil.isNewPackOrderConformationSupported(userContext, isInternalUser)) {
            offersList = OfferUtil.segregateNoCostEMIOffers(offersList).get("OTHER_OFFERS")
        }
        const offerDataAll: any[] = AppUtil.getOffersWithAddonsAndLabels(offersList, "orderSummaryLabel")
        const offerDataItems: OfferData[] = _.map(offerDataAll, offer => { return { description: offer.description, tnc: offer.tnc, tncURL: offer.tncURL } })
        if (!offerDataItems.length) {
            return undefined
        }
        if (AppUtil.isAuroraOrderConfirmationSupported(userContext)) {
            const items = _.map(offerDataAll, offer => {
                return {
                    title: offer.description,
                    icon: "/image/offer-icon.png",
                    cardAction: {
                        actionType: "SHOW_OFFERS_TNC_MODAL",
                        icon: "chevron-right",
                        meta: {
                            title: "Offer Details",
                            dataItems: offer.tnc,
                            url: offer.tncURL,
                        }
                    }
                }
            })
            return {
                widgetType: "INFO_LIST_WIDGET",
                items,
                header: {
                    title: "What else you got",
                },
                isListExpanded: true
            }
        }
        return new OfferCalloutWidget(
            "What else you got",
            offerDataItems,
            undefined,
            true,
            "",
            "BULLET"
        )
    }


    private getOrderConfirmationHeader(product: OfflineFitnessPack, userContext: UserContext, isInternalUser: boolean): Header {
        const headerWidget: Header = {
            title: "Order Confirmed!",
            subTitle: AppUtil.isNewPackOrderConformationSupported(userContext, isInternalUser) ? `${product.title}` : `Welcome to your ${product.title}!`
        }
        return headerWidget
    }

    private getActivityDescription(centerId: string, product: OfflineFitnessPack, userContext: UserContext, membershipId: string): UserActivity {
        const bookCultActivity: UserActivity = {
            date: TimeUtil.todaysDateWithTimezone(userContext.userProfile.timezone),
            activityType: product.productType === "FITNESS" ? "DIY_FITNESS" : "DIY_MEDITATION",
            activityName: this.getActivityName(),
            status: "TODO",
            title: "",
            userActivityId: product.productId
        }

        const url = this.getActivityAction(userContext, centerId, product, membershipId)
        bookCultActivity.action = url
        return bookCultActivity
    }

    private getOrderConfirmationFooter(): Header {
        const footer: Header = {
            title: `Congratulations ! All the best for your fitness journey.`
        }
        return footer
    }

    protected abstract getActivityName(): string

    protected getActivityAction(userContext: UserContext, centerId: string, product: OfflineFitnessPack, membershipId: string) {
        const derivedProductType = product.productType === "FITNESS" || product.productType === "FITNESS_PREREGISTRATION" ? "FITNESS" : "MIND"
        const isNewClassBookingSupported = AppUtil.isNewClassBookingSuppoted(userContext)
        if (centerId && centerId !== "undefined") {
            return ActionUtil.getBookCultClassUrl(derivedProductType, isNewClassBookingSupported, "confirmationScreen", undefined, centerId)
        } else {
            return ActionUtil.getBookCultClassUrl(derivedProductType, isNewClassBookingSupported, "confirmationScreen")
        }
    }
}

class FitnessFirstPackConfirmationView extends ConfirmationView {
    constructor(userContext: UserContext, product: Product) {
        super(ORDER_CONFIRMATION_ACTION)
        this.header = this.getOrderConfirmationHeader(product)
        this.body = this.getActivityDescription(userContext, product.productId)
        this.footer = this.getOrderConfirmationFooter()
        this.vertical = "FITNESS_FIRST"
    }

    private getOrderConfirmationHeader(product: Product): Header {
        const headerWidget: Header = {
            title: "Order Confirmed!",
            subTitle: `To activate your ${product.title} membership, visit your selected club within 30 days. More details on your registered email ID`
        }
        return headerWidget
    }

    private getActivityDescription(userContext: UserContext, productId: string): UserActivity {
        const bookCultActivity: UserActivity = {
            date: TimeUtil.todaysDateWithTimezone(userContext.userProfile.timezone),
            activityType: "FITNESS_FIRST_PACK",
            activityName: "FITNESS_FIRST_PACK_PURCHASED",
            status: "TODO",
            title: "",
            userActivityId: productId,
            action: "curefit://mePage"
        }
        return bookCultActivity
    }

    private getOrderConfirmationFooter(): Header {
        const footer: Header = {
            title: `Congratulations ! All the best for your fitness journey.`
        }
        return footer
    }
}

class GymfitFitnessPackConfirmationView extends ConfirmationView {
    constructor(userContext: UserContext, product: Product, order: Order) {
        super(ORDER_CONFIRMATION_ACTION)
        this.header = this.getOrderConfirmationHeader(product)
        this.body = this.getActivityDescription(userContext, product.productId, order.orderId)
        this.footer = this.getOrderConfirmationFooter()
        this.vertical = "GYM_FIT"
        this.widgets = this.getOtherGymsWidgets()
        this.meta = {
            productType: "OTHER_GYMS"
        }
    }

    private getOtherGymsWidgets(): any {
        const widgets = []
        widgets.push({
            widgetType: "ORDER_SUCCESS_WIDGET",
            icon: "SUCCESS_BLUE",
            title: "To activate your membership, visit your selected center within 30 days."
        })
        widgets.push({
            widgetType: "GYM_PACK_INFO",
            title: "Order Confirmed",
            action: {
                actionType: "NAVIGATION",
                url: "curefit://order"
            }
        })
        return widgets
    }

    private getOrderConfirmationHeader(product: Product): Header {
        const headerWidget: Header = {
            title: "Order Confirmed!",
            subTitle: `To activate your membership, visit your selected center within 30 days.`
        }
        return headerWidget
    }

    private getActivityDescription(userContext: UserContext, productId: string, orderId: string): UserActivity {
        const bookCultActivity: UserActivity = {
            date: TimeUtil.todaysDateWithTimezone(userContext.userProfile.timezone),
            activityType: "GYMFIT_FITNESS",
            activityName: "GYMFIT_FITNESS_PACK_PURCHASED",
            status: "TODO",
            title: "",
            userActivityId: productId,
            action: `curefit://order`
        }
        return bookCultActivity
    }

    private getOrderConfirmationFooter(): Header {
        const footer: Header = {
            title: `Congratulations ! All the best for your fitness journey.`
        }
        return footer
    }
}

class GymFitnessProductConfirmationView extends ConfirmationView {
    constructor(userContext: UserContext, gymMembership: Membership) {
        super(ORDER_CONFIRMATION_ACTION)
        const asyncProcess = (async () => {
            this.widgets = [this.getOrderConfirmedWidget(), await this.getProductInfoWidget(userContext, gymMembership)]
        })()
        this.vertical = "GYM_FIT"
        this.meta = {
            productType: "GYM_FITNESS_PRODUCT"
        }
    }

    private getOrderConfirmedWidget(): OrderSuccessWidget {
        return {
            widgetType: "ORDER_SUCCESS_WIDGET",
            icon: "SUCCESS_BLUE",
            title: "Thank you for your purchase.\nPlease proceed to have a great workout!"
        }
    }

    private async getProductInfoWidget(userContext: UserContext, gymfitMembership: Membership): Promise<GymPackInfo> {
        const tz = userContext.userProfile.timezone
        const endDate = TimeUtil.getMomentForDate(new Date(gymfitMembership.end), tz)
        const startDate = TimeUtil.getMomentForDate(new Date(gymfitMembership.start), tz)
        const durationInDays = endDate.diff(startDate, "days")
        return {
            widgetType: "GYM_PACK_INFO",
            title: `${gymfitMembership.name} membership`,
            description: `Valid for ${AppUtil.getMonthsFromDays(durationInDays)} months`,
            action: {
                actionType: "NAVIGATION",
                url: AppUtil.isWeb(userContext) ? GymfitUtil.getGoldMembershipPackDetailsWebUrl(gymfitMembership) : await GymfitUtil.getGoldMembershipDetailsUrl(gymfitMembership, userContext)
            }
        }
    }
}

class CultFitnessPackConfirmationView extends FitnessPackConfirmationView {
    protected getActivityName(): string {
        return "Book your Cult Class"
    }
}

class MindFitnessPackConfirmationView extends FitnessPackConfirmationView {
    protected getActivityName(): string {
        return "Book your mind.fit Class"
    }
}

class PlayPackConfirmationView extends FitnessPackConfirmationView {
    protected getActivityName(): string {
        return "Book your sport session"
    }
}

class FoodPackConfirmationView extends ConfirmationView {
    public orderMeta: {
        orderId: string
        title: string
        orderType: "EAT_SINGLE" | "EAT_PACK" | "CULT_SINGLE" | "CULT_PACK" | "DIAGNOSTICS_SINGLE" | "TELECONSULTATION_SINGLE" | "OTHER" | "MIND_PACK" | "MIND_SINGLE"
        vertical: Vertical
        price: ProductPrice
        productIds?: string[]
    }
    constructor(userContext: UserContext, product: Product, order: Order, foodFulfilment: BaseFoodFulfilment, myGateWidget?: IBaseWidget) {
        super(ORDER_CONFIRMATION_ACTION)
        const tz = userContext.userProfile.timezone
        const orderProduct: OrderProduct = order.productSnapshots[0]
        const slotDate: string = TimeUtil.formatDateStringInTimeZone(orderProduct.option.startDate, tz, "ddd, D MMM")
        const slotTime: string = MealUtil.getSlotDisplayText(orderProduct.option.deliverySlot)
        this.header = this.getOrderConfirmationHeader(order.productSnapshots, orderProduct.option.startDate, slotTime)
        this.body = this.getActivityDescription(product, slotDate, slotTime, foodFulfilment, userContext)
        this.footer = this.getOrderConfirmationFooter(order, orderProduct.option.startDate, slotTime, userContext)
        this.vertical = "EAT_FIT"
        this.orderMeta = {
            orderId: order.orderId,
            price: {
                listingPrice: order.totalAmountPayable,
                mrp: order.totalAmountPayable,
                currency: orderProduct.price.currency
            },
            productIds: order?.productSnapshots?.map(product => product.productId),
            orderType: "EAT_PACK",
            title: product.title,
            vertical: "EAT_FIT"
        }
        this.meta = {
            cleverTap: {
                af_revenue: order.totalAmountPayable,
                af_content_type: order.productSnapshots[0].categoryId,
                af_content_id: orderProduct.productId,
                af_content: orderProduct.option.subscriptionType,
                af_date_a: orderProduct.option.startDate,
                af_date_b: TimeUtil.getDefaultMomentForDateString(orderProduct.option.startDate, tz).add(orderProduct.option.numDays).format("YYYY-MM-DD")
            }
        }
        this.widgets = []
        if (myGateWidget) {
            this.widgets.push(myGateWidget)
        }
    }

    private getOrderConfirmationHeader(products: OrderProductSnapshots[], slotDate: string, slotTime: string): Header {
        const orderProduct = products[0]
        const titles = _.map(products, (item) => {
            return item.title
        })
        const productTitle = titles.join(" + ")
        const headerWidget: Header = {
            title: "Order Confirmed!",
            subTitle: orderProduct.option.subscriptionType === undefined ? `Welcome to your ${productTitle} pack.`
                : `Welcome to your ${productTitle} subscription.`
        }
        return headerWidget
    }

    private getActivityDescription(product: Product, slotDate: string, slotTime: string, foodFulfilment: BaseFoodFulfilment, userContext: UserContext): UserActivity {
        const userActivityId = product.productId
        const activityDate = slotDate
        const seoParams: SeoUrlParams = {
            productName: product.title
        }
        return {
            date: activityDate,
            activityName: "First meal",
            activityType: "EATFIT_MEAL",
            status: "TODO",
            title: `${slotDate}, ${slotTime}`,
            action: ActionUtil.foodPack(product.productId, foodFulfilment.fulfilmentId, undefined, undefined, userContext.sessionInfo.userAgent, seoParams),
            userActivityId: userActivityId
        }
    }

    private getOrderConfirmationFooter(order: Order, slotDate: string, slotTime: string, userContext: UserContext): Header {
        const footer: Header = {
            // title: `You will see this meal in your Today on ${slotDate}.`
            title: `You will see this meal in your Today screen on ${TimeUtil.getDayText(slotDate, userContext.userProfile.timezone)}`
        }
        return footer
    }
}

class AccessoriesConfirmationView extends ConfirmationView {
    constructor(userContext: UserContext, product: Product, order: Order) {
        super(ORDER_CONFIRMATION_ACTION)
        const orderProduct: OrderProduct = order.products[0]
        this.header = this.getOrderConfirmationHeader()
        this.body = this.getActivityDescription(userContext, product)
        this.footer = this.getOrderConfirmationFooter()
        this.vertical = "EAT_FIT"
    }

    private getOrderConfirmationHeader(): Header {
        const headerWidget: Header = {
            title: "Order Confirmed!",
            subTitle: "Collect your band between 18th & 30th September from the selected center."
        }
        return headerWidget
    }

    private getActivityDescription(userContext: UserContext, product: Product): UserActivity {
        const userActivityId = product.productId
        const activityDate = TimeUtil.todaysDateWithTimezone(userContext.userProfile.timezone)
        return {
            date: activityDate,
            activityName: "Mi Band - HRX Edition",
            activityType: "CULT_CLASS",
            subActivityType: "HRX WORKOUT",
            status: "TODO",
            title: `Exclusive offer for cult members`,
            action: `curefit://today?userActivityId=${userActivityId}&activityDate=${activityDate}`,
            userActivityId: userActivityId
        }
    }

    private getOrderConfirmationFooter(): Header {
        const footer: Header = {
            title: `You can collect your band from the selected cult center`
        }
        return footer
    }
}

class EatMarketplaceConfirmationView extends ConfirmationView {
    constructor(order: Order) {
        const action = `${ORDER_CONFIRMATION_EAT_MARKETPLACE_ACTION}?orderId=${order.orderId}`
        super(action)
        this.vertical = "EAT_FIT"
    }
}

class FoodSingleConfirmationView extends ConfirmationView {

    constructor(userContext: UserContext, product: Product, order: Order, orderInfo: OrderInfo, onDemandText: string, params: ConfirmationRequestParams, deliverySlot: DeliverySlot) {
        super(ORDER_CONFIRMATION_ACTION)
        const tz = userContext.userProfile.timezone
        const orderProduct: OrderProduct = order.products[0]
        const slotDate: string = TimeUtil.formatDateStringInTimeZone(orderProduct.option.startDate, userContext.userProfile.timezone, "ddd, D MMM")
        const slotId: string = orderProduct.option.deliverySlot ? orderProduct.option.deliverySlot : undefined
        const isKiosk = order.userAddress.addressType === "KIOSK"
        let slotTime: string
        if (_.get(order, "eatOptions.listingBrand") === "WHOLE_FIT" && !_.isNil(deliverySlot) && AppUtil.isWeb(userContext)) {
            // Hack for wholefit web, todo: MOVE WHOLEFIT WEB TO ORDERCONFIRMATIONV1
            slotTime = MealUtil.getDisplayTextForSlot(deliverySlot, undefined, undefined, onDemandText)
        } else {
            slotTime = slotId ? MealUtil.getSlotDisplayText(slotId, undefined, undefined, onDemandText, false) :
                MealUtil.getSlotDisplayTextFromTime(orderProduct.option.deliveryWindow.startingHours, orderProduct.option.deliveryWindow.closingHours, isKiosk, momentTz.tz(orderProduct.option.startDate, tz).toDate())
        }
        let numProducts = 0
        order.products.forEach(orderProduct => {
            numProducts = numProducts + orderProduct.quantity
        })
        this.header = this.getOrderConfirmationHeader(product, numProducts, orderProduct.option.startDate, slotTime, userContext)
        this.body = this.getActivityDescription(product, slotDate, slotTime, numProducts, params, order.orderId)
        this.footer = this.getOrderConfirmationFooter(order, orderProduct.option.startDate, slotTime, userContext)
        this.vertical = "EAT_FIT"
        this.orderInfo = orderInfo
        const productMeta = _.map(order.productSnapshots, (productSnapshot) => {
            return {
                isVeg: productSnapshot.isVeg,
                productId: productSnapshot.productId,
                categoryId: productSnapshot.categoryId,
            }
        })
        this.meta = {
            cleverTap: {
                af_revenue: order.totalAmountPayable,
                af_content_type: order.productSnapshots.map((product) => { return product.categoryId }),
                af_content_id: order.productSnapshots.map((product) => { return product.productId }),
                af_content: order.productSnapshots.map((product) => { return product.title }),
            },
            productMeta: productMeta
        }
    }

    private getOrderConfirmationHeader(product: Product, numProducts: number, slotDate: string, slotTime: string, userContext: UserContext): Header {
        const header: Header = {
            title: "Order Confirmed!",
            subTitle: `Your order is confirmed for ${TimeUtil.getDayText(slotDate, userContext.userProfile.timezone)}, ${slotTime}`
        }
        return header
    }

    private getActivityDescription(product: Product, slotDate: string, slotTime: string, numProducts: number, params: ConfirmationRequestParams, orderId: string): UserActivity {
        const userActivityId = product.productId
        const activityDate = slotDate
        let action
        if (SUPPORTED_MICROAPPS_ORDER_SOURCE.has(params.orderSource)) {
            action = `curefit://orderpage?orderId=${orderId}`
        } else {
            action = `curefit://today?userActivityId=${userActivityId}&activityDate=${activityDate}`
        }
        return {
            date: activityDate,
            activityName: numProducts > 1 ? numProducts + " items ordered" : product.title,
            activityType: "EATFIT_MEAL",
            status: "TODO",
            title: `${slotDate}, ${slotTime}`,
            action: action,
            userActivityId: userActivityId
        }
    }

    private getOrderConfirmationFooter(order: Order, slotDate: string, slotTime: string, userContext: UserContext): Header {
        const orderProduct: OrderProduct = order.products[0]
        const footer: Header = {
            // title: `You will see this meal in your Today screen on ${slotDate}.`
            title: `You will see this meal in your Today screen on ${TimeUtil.getDayText(slotDate, userContext.userProfile.timezone)}`
        }
        return footer
    }
}

class SubscriptionConfirmationViewV2 extends DIYConfirmationView {
    constructor(userContext: UserContext, pack: DIYPack, sessionMap: { [productId: string]: Product }, fulfilment: DIYPackFulfilment) {
        super(ORDER_CONFIRMATION_ACTION)
        this.header = this.getOrderConfirmationHeader(pack)
        this.body = this.getActivityDescription(userContext, pack)
        this.footer = this.getOrderConfirmationFooter(userContext)
        this.meta = { "autoDowloadSession": this.getAutoDownloadSession(pack, sessionMap) }
        this.fulfilment = fulfilment
        if (pack.productType === "DIY_MEDITATION_PACK") {
            this.vertical = "MIND_FIT"
        } else if (pack.productType === "DIY_FITNESS_PACK") {
            this.vertical = "CULT_FIT"
        }
    }

    private getAutoDownloadSession(pack: DIYPack, sessionMap: { [productId: string]: Product }): DiyContentDetail {
        const firstActivityId: string = pack.sessionIds[0]
        const session = <DIYProduct>sessionMap[firstActivityId]
        const contentDetail = AtlasUtil.getContentDetailV2(session)
        return contentDetail

    }

    private getOrderConfirmationHeader(pack: DIYPack): Header {
        const header: Header = {
            title: "Pack Subscribed!",
            subTitle: `Welcome to your subscription for ${pack.title}.`,
        }
        return header
    }

    private getActivityDescription(userContext: UserContext, pack: DIYPack): UserActivity {
        let actionString
        const packId: string = pack.productId
        let activityType: ActivityType

        actionString = ActionUtilV1.diyPackProductPage(pack, userContext.sessionInfo.userAgent)
        if (pack.productType === "DIY_FITNESS_PACK") {
            activityType = "DIY_FITNESS"
        } else if (pack.productType === "DIY_MEDITATION_PACK") {
            activityType = "DIY_MEDITATION"
        }
        return {
            date: TimeUtil.todaysDateWithTimezone(userContext.userProfile.timezone),
            activityName: "First session",
            activityType: activityType,
            status: "TODO",
            title: pack.title,
            action: actionString,
            userActivityId: packId
        }
    }

    private getOrderConfirmationFooter(userContext: UserContext): Header {
        const tz = userContext.userProfile.timezone
        const footer: Header = {
            title: `You can also find this activity added to your To-do list on ${TimeUtil.formatDateStringInTimeZone(TimeUtil.todaysDateWithTimezone(tz), tz, "ddd, D MMM")}`
        }
        return footer
    }
}

class CultEventConfirmationView extends ConfirmationView {
    constructor(cultEvent: FitnessEvent) {
        super(ORDER_CONFIRMATION_ACTION)
        this.header = this.getOrderConfirmationHeader(cultEvent)
        this.body = this.getActivityDescription(cultEvent)
        this.footer = this.getOrderConfirmationFooter(cultEvent)
        this.vertical = "CULT_FIT"
    }

    private getOrderConfirmationHeader(cultEvent: FitnessEvent): Header {
        const header: Header = {
            title: "Event Registered!",
            subTitle: `You have been successfully registered for ${cultEvent.name}.`,
        }
        return header
    }

    private getActivityDescription(cultEvent: FitnessEvent): UserActivity {
        return {
            date: cultEvent.launchDate,
            activityName: cultEvent.name,
            activityType: "CULT_EVENT",
            status: "TODO",
            title: _.isNil(cultEvent.startTime) ? `${cultEvent.launchDate}` : `${cultEvent.launchDate}, ${cultEvent.startTime}`,
            action: `curefit://account`,
            userActivityId: cultEvent.productId
        }
    }

    private getOrderConfirmationFooter(cultEvent: FitnessEvent): Header {
        let title
        if (!_.isNil(cultEvent.Address))
            title = `Enjoy the event. Be there at ${cultEvent.Address.addressLine1}, ${cultEvent.Address.locality} `
        else
            title = `Enjoy the event.`
        const footer: Header = {
            title: title
        }
        return footer
    }
}
interface FeedbackInfo extends FeedbackRequest {
    feedbackTitle?: string
}
class GearOrderConfirmationView extends ConfirmationView {
    feedbackInfo: FeedbackInfo
    widgets: WidgetWithMetric[]
    orderId: string
    totalPayable?: number
    orderMeta?: any
    merchantInfoDetailsForGokwik?: any
    deliveryDetails?: any
    constructor(user: User, userContext: UserContext, order: Order, feedback?: FeedbackRequest, isNewCultsportOrderFlowSupported?: boolean, widgets?: WidgetWithMetric[], merchantInfoDetailsForGokwik?: any, address?: UserDeliveryAddress) {
        super(GEAR_ORDER_CONFIRMATION_ACTION)
        this.header = this.getOrderConfirmationHeader(userContext, order)
        this.body = this.getActivityDescription(userContext, order, isNewCultsportOrderFlowSupported)
        this.footer = this.getOrderConfirmationFooter()
        this.vertical = "CULT_GEAR"
        this.widgets = []
        this.widgets.push(new CSOrderDeliveryAddressWidget(user, address))
        this.widgets.push(new CSOrderDeliveryTrackWidget())
        this.widgets.push(this.getCSOrderItemsWidget(order))
        feedback && this.widgets.push(new CSOrderFeedbackInputWidget(feedback))

        this.totalPayable = order.totalPayable
        this.orderId = order.orderId
        this.feedbackInfo = feedback ? {...feedback, feedbackTitle: "Share your feedback"} : undefined
        this.orderMeta = {
            orderItems: order.productSnapshots?.map((productSnapshot: GearProductSnapshots, index: number) => {
                return {
                    ...CultsportUtil.getAnalyticsDataForOrderItem(order, productSnapshot),
                    offersApplied: productSnapshot?.option?.offersInfo?.map((offer) => offer.offerId)?.join(",")
                }
            }),
            orderType: "GEAR",
            ...CultsportUtil.getAnalyticsDataForOrder(order)
        }
      this.merchantInfoDetailsForGokwik = merchantInfoDetailsForGokwik
      if (widgets) {
        this.widgets = this.widgets.concat(widgets)
      }
      this.widgets = this.widgets.filter(Boolean)
    }

    getCsOrderUpdateUserInfo(order: Order) {
        const address: UserDeliveryAddress = order.userAddress
        const phoneNumber = address.phoneNumber

        if (phoneNumber != null) {
            return new CsOrderUpdateUserInfo(
                "Order Updates Sent To",
                phoneNumber,
                "image/cultsport/order_confirmation/phone.svg"
            )
        }
        return null
    }

    getCSOrderItemsWidget(order: Order) {
        const orderId = order.orderId

        const csOrderUpdateUserInfo = this.getCsOrderUpdateUserInfo(order)

        const cultsportOrderProductCardWidgets = order.productSnapshots?.flatMap(
            (productSnapshot: GearProductSnapshots) => {
                return productSnapshot.option.cultGearInventoryUnits.map(inventoryUnit => {
                        return new CultsportOrderProductCardWidget(
                        productSnapshot.title,
                        productSnapshot.subTitle,
                        productSnapshot.gearBrandName,
                        productSnapshot.imageUrls[0],
                        this.getFitstoreWebPdpAction(productSnapshot.masterProductId.toString()),
                        this.getOrderDetailsAction(inventoryUnit.id.toString(), orderId.toString()),
                        )
                })
            }
        ) ?? []

        return cultsportOrderProductCardWidgets.length > 0 ? new CSOrderItemsWidget(cultsportOrderProductCardWidgets, csOrderUpdateUserInfo) : null
    }

    getFitstoreWebPdpAction(productId: String): Action {
        return {
            actionType: "NAVIGATION" as ActionType,
            title: "EXPLORE",
            url: `curefit://detailspage?productId=${productId}`
        }
    }

    getOrderDetailsAction(inventoryUnitId: String, orderId: String): Action {
        return {
            actionType: "NAVIGATION" as ActionType,
            url: `order/${orderId}/${inventoryUnitId}`
        }
    }

     getOrderConfirmationHeader(userContext: UserContext, order: Order): Header {
        const header: Header = {
            title: "Order Confirmed!",
            subTitle:  this.getSubtitle(userContext, order)
        }
        return header
    }

    getSubtitle(userContext: UserContext, order: Order): string {
        return "Gear up for change"
    }

    getActivityDescription(userContext: UserContext, order: Order, isNewCultsportOrderFlowSupported: boolean): UserActivity {
        const numProducts: number = order.products.length
        const title = _.map(order.productSnapshots, p => `${p.title} ${p.subTitle} (${p.quantity})`).join(", ")
        return {
            date: TimeUtil.todaysDateWithTimezone(userContext.userProfile.timezone),
            activityName: `${numProducts} Product${numProducts > 1 ? "s" : ""} ordered`,
            activityType: "CULT_GEAR_ECOMMERCE",
            status: "TODO",
            title,
            action: isNewCultsportOrderFlowSupported ? "/me/orders" : ActionUtil.gearOrder(order.orderId),
            userActivityId: order.orderId
        }
    }

    getOrderConfirmationFooter(): Header {
        return {
            title: "You have successfully placed an order for these products"
        }
    }
}

class LivePackConfirmationView extends ConfirmationView {
    constructor(userContext: UserContext, order: Order, liveMembership: Membership, product: Product) {
        super(ORDER_CONFIRMATION_ACTION)
        this.header = this.getOrderConfirmationHeader()
        this.body = this.getActivityDescription(userContext, liveMembership, product)
        this.vertical = "LIVE_FIT"
    }

    getOrderConfirmationHeader(): Header {
        const header: Header = {
            title: "Payment Successful"
        }
        return header
    }

    getActivityDescription(userContext: UserContext, liveMembership: Membership, product: Product): UserActivity {
        return {
            activityType: "LIVE_PACK_BOOK",
            activityName: "",
            date: TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, liveMembership.start, "DD MMM, YYYY"),
            title: product.title + " " + LiveUtil.getLiveBranding(userContext),
            subTitle: "Start from " + TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, liveMembership.start, "DD MMM, YYYY"),
            userActivityId: product.productId,
            activityAction: {
                title: "BOOK LIVE CLASS",
                subTitle: "Enjoy unlimited access to LIVE classes & on-demand health content",
                actionType: "NAVIGATION",
                url: "curefit://liveclassbooking?productType=FITNESS&isLiveBookingPage=true",
            },
            action: "curefit://liveclassbooking?productType=FITNESS&isLiveBookingPage=true"
        }
    }
}

class VaccinationAppointmentConfirmationView extends ConfirmationView {
    constructor(userContext: UserContext, order: Order, product: Product, appointmentDetails: VaccineAppointmentResponse, vaccinationConfig: VaccinationConfig) {
        super(ORDER_CONFIRMATION_ACTION)
        this.header = this.getOrderConfirmationHeader()
        this.vertical = "CULT_FIT"
        this.productType = "VACCINE"
        this.widgets = []
        this.widgets.push(this.getVaccineConfirmationDetailsWidget(userContext, product, appointmentDetails))
        this.widgets.push(this.getStepsWidget(vaccinationConfig))
    }

    getOrderConfirmationHeader(): Header {
        const header: Header = {
            title: "Vaccination Slot Confirmed"
        }
        return header
    }

    getVaccineConfirmationDetailsWidget(userContext: UserContext, product: Product, appointmentDetails: VaccineAppointmentResponse) {
        const slotDateFormatted = TimeUtil.formatDateStringInTimeZone(appointmentDetails.slotDetails.date, userContext.userProfile.timezone)
        const slotStartTime = TimeUtil.formatDateStringInTimeZone(
            slotDateFormatted + " " + appointmentDetails.slotDetails.startTime,
            userContext.userProfile.timezone,
            "h:mm a"
        )
        const timeString = `${TimeUtil.formatDateStringInTimeZone(
            appointmentDetails.slotDetails.date,
            userContext.userProfile.timezone,
            "D MMM"
        )}, ${slotStartTime}`
        const items: ActionCard[] = [
            {
                title: appointmentDetails.slotDetails.centerDetails.name + ", " + appointmentDetails.cityDetails.name,
                icon: "/image/icons/howItWorks/vaccination/Time_Icon_2.png",
                subTitle: appointmentDetails.slotDetails.centerDetails.address,
                seeMoreAction: {
                    actionType: "EXTERNAL_DEEP_LINK",
                    title: "NAVIGATE",
                    url: appointmentDetails.slotDetails.centerDetails.mapUrl
                }
            },
            {
                title: timeString,
                icon: "/image/icons/howItWorks/vaccination/Location_Icon_2.png"
            },
            {
                title: appointmentDetails.slotDetails.vaccineType + " - " + appointmentDetails.doseType,
                icon: "/image/icons/howItWorks/vaccination/Vaccine_Icon_2.png"
            }]
        const widget: VaccinationSummaryWidget = {
            widgetType: "VACCINATION_SUMMARY_WIDGET",
            header: {
                title: appointmentDetails.name
            },
            items
        }
        return widget
    }

    getStepsWidget(vaccinationConfig: VaccinationConfig) {
        const widget = new ProductListWidget("SMALL", vaccinationConfig.configs.confirmationStepsHeader, vaccinationConfig.configs.confirmationSteps)
        return widget
    }

}


export class CultBikeConfirmationView extends ConfirmationView {
    constructor(
        userContext: UserContext,
        product: Product,
        user: User,
        order: Order,
        params: ConfirmationRequestParams
    ) {
        super(ORDER_CONFIRMATION_V1_ACTION)
        this.widgets = [
            this.geConfirmWidget(),
            this.getConfirmInfoWidget(userContext, product, user, order),
            this.geDeliveryAddressWidget(userContext, order),
            this.geHowItWorksWidget(userContext)
        ]
        // this.vertical =  ""
        this.widgets = this.widgets.filter(Boolean)
    }
    private getConfirmInfoWidget(userContext: UserContext, product: Product, user: User, order: Order): CareCheckoutInfoItemWidget {
        const items: CheckoutInfoItem[] = []

            items.push({
                title: `View order details`,
                imageType: "SMALL",
                imageUrl:  "/image/vm/fecff226-088b-408c-8393-00ddf431f934.png",
                showArrow: true,
                showDivider: true,
                titleStyleType: "MEDIUM_GRAY",
                action: {
                    actionType: "NAVIGATION",
                    url: `curefit://myorderdetail?orderId=${order.orderId}`
                },
                isCommonImageSpacing: true,
            })
            // items.push({
            //     title: "Need Help?",
            //     imageType: "SMALL",
            //     imageUrl:  "/image/vm/b208d8d2-892e-4704-90e6-2569699cc646.png",
            //     showArrow: true,
            //     titleStyleType: "MEDIUM_GRAY",
            //     action: {
            //         actionType: "EXTERNAL_DEEP_LINK",
            //         url: `mailto:${Constants.customerCareMail}`
            //     },
            //     isCommonImageSpacing: true,
            // })
        if (_.isEmpty(items)) {
            return undefined
        }
        return {
            widgetType: "CARE_CHECKOUT_INFO_ITEM_WIDGET",
            items
        }
    }

    private geConfirmWidget(): CareTCConfirmInfoWidget {
        return {
            widgetType: "CARE_TC_CONFIRM_INFO_WIDGET",
            imageUrl: "/image/icons/checkout/care/success_tick.png",
            textInfoItems: [
                {
                    title: `Order Placed`,
                    textStyleType: "BOLD"
                },
                {
                    title: "Expect delivery in 7 business days",
                    textStyleType: "MEDIUM_GRAY"
                }
            ]
        }
    }

    private geDeliveryAddressWidget(userContext: UserContext, order: Order): any {
        if (_.isEmpty(order?.userAddress)) {
            return undefined
        }
        return CareUtil.getWhatYouGetInstructionWidget({
            type: "WHAT_YOU_GET",
            title: "Delivering to",
            children: [
                {
                    "desc": `${order.userAddress.addressType}: ${order.userAddress.addressLine1}, ${order.userAddress.addressLine2}`,
                    imageUrl: "image/vm/8912d692-2171-487c-acd4-702d07515d3b.png",
                },
            ]
        }, userContext)
    }

    private geHowItWorksWidget(userContext: UserContext): any {
        return CareUtil.getWhatYouGetInstructionWidget({
            type: "WHAT_YOU_GET",
            title: "How it works",
            children: [
                {
                    "desc": `Free delivery within 7 business days. Our team will install and set up the bike for you.`,
                    imageUrl: "/image/vm/10b74a9c-8202-4bb3-bbb2-c07e70958930.png",
                },
                {
                    "desc": "Experience a worry-free 10-day returns policy. If you’re unhappy with the product, we’ll pick up and refund your entire amount. No questions asked.",
                    imageUrl: "/image/vm/a05d2aaf-d348-4c58-8ede-27ac523bf8e2.png",
                },
                {
                    "desc": "Please write to <NAME_EMAIL> for any cancellation/ refund requests, or queries",
                    imageUrl: "image/vm/0fee5622-f3c3-46c8-b946-dbf3a6c471ed.png",
                    // imageUrl: "/image/vm/5f3d8a90-f292-4e90-8f65-530ea4c73240.png",
                    // imageUrl: "image/vm/4e4a7fdf-52d5-42f3-a071-6979807874f0.png"
                }
            ]
        }, userContext)
    }
}

export default OrderConfirmationViewBuilder
