import * as express from "express"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import { Container, inject } from "inversify"
import * as moment from "moment"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { SortOrder } from "@curefit/mongo-utils"
import {
    CartReviewResponse,
    CreateGatewayOrderResponse,
    FulfillmentResult,
    IOrderService as IOmsApiClient,
    OMS_API_CLIENT_TYPES,
    OrderCheckoutRequest,
    OrderCheckoutResponse as OMSOrderCheckoutResponse,
    OrderCreate,
} from "@curefit/oms-api-client"
import { DELIVERY_CLIENT_TYPES, IGateService } from "@curefit/delivery-client"
import IUserBusiness from "../user/IUserBusiness"
import {
    BaseOrder,
    CultMembershipMetadata,
    CultMindMembershipUpgradeMetadata,
    isMembershipUpgradeClientMetadata,
    Option,
    Order,
    OrderProduct,
    OrderSource
} from "@curefit/order-common"
import { ProductType } from "@curefit/product-common"
import { Session, UserContext } from "@curefit/userinfo-common"
import { UserDeliveryAddress } from "@curefit/eat-common"
import AuthMiddleware from "../auth/AuthMiddleware"
import { CfApiCreateGatewayOrderResponse, PaymentChannel, PaymentData, PaymentMode } from "@curefit/payment-common"
import { TimeUtil } from "@curefit/util-common"
import { BASE_TYPES, Logger } from "@curefit/base"
import { GearOrderDetail } from "../common/views/WidgetView"
import OrderViewBuilder, { OrderDetail, OrderInfo } from "./OrderViewBuilder"
import BaseOrderConfirmationViewBuilder, {
    ConfirmationRequestParams,
    ConfirmationView,
    FitClubConfirmationView,
    OrderStatusView
} from "./BaseOrderConfirmationViewBuilder"
import { IOfferServiceV2, OFFER_SERVICE_CLIENT_TYPES, OfferServiceV3, OfferUtil } from "@curefit/offer-service-client"
import { CATALOG_CLIENT_TYPES, ICatalogueService, ICatalogueServicePMS } from "@curefit/catalog-client"
import * as _ from "lodash"
import AppUtil from "../util/AppUtil"
import OrderUtil, { SUPPORTED_MICROAPPS_ORDER_SOURCE } from "../util/OrderUtil"
import { CareUtil as BaseCareUtil, MealUtil, OrderUtil as EtherOrderUtil } from "@curefit/base-utils"
import { GearService, GEARVAULT_CLIENT_TYPES } from "@curefit/gearvault-client"
import GearOrderViewBuilder from "./GearOrderViewBuilder"
import { CacheHelper } from "../util/CacheHelper"
import { ICultBusiness } from "../cult/CultBusiness"
import OrderConfirmationViewBuilderV1 from "./OrderConfirmationViewBuilderV1"
import GearOrderWithShipmentViewBuilder from "./GearOrderWithShipmentViewBuilder"
import { CULT_CLIENT_TYPES, ICultServiceOld as ICultService, } from "@curefit/cult-client"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { MembershipTransferDetailResponse } from "@curefit/cult-common"
import {
    AlertInfo,
    OrderCategoryFilter,
    OrderDateRangeFilter,
    OrderMeta,
    OrderTopLevelFilter,
    SourceFilterSet,
    WidgetView
} from "@curefit/apps-common"
import { IUserService, USER_CLIENT_TYPES } from "@curefit/user-client"
import CareUtil from "../util/CareUtil"
import { IPaymentClient, PAYMENT_TYPES, PaymentPromUtil } from "@curefit/payment-client"
import { ErrorFactory, HTTP_CODE } from "@curefit/error-client"
import { ErrorCodes } from "../error/ErrorCodes"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { OfferAddon, OfferAddonType } from "@curefit/offer-common"
import LiveUtil from "../util/LiveUtil"
import { ConsultationProduct } from "@curefit/care-common"
import { ALBUS_CLIENT_TYPES, IHealthfaceService } from "@curefit/albus-client"
import { HAMLET_CLIENT_TYPES, IHamletService } from "@curefit/hamlet-client"
import { PromiseCache } from "../util/VMUtil"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { CenterResponse } from "@curefit/center-service-common"
import { CENTER_SERVICE_CLIENT_TYPES, ICenterService } from "@curefit/center-service-client"
import {
    Audit,
    PersonalTrainingBookingRequest,
    PersonalTrainingPpcBookingRequest,
    PtBookingSource,
    PtSession,
    PtSessionStatus,
    SessionSearchRequest,
    Source
} from "@curefit/personal-training-v2-common"
import { IPersonalTrainingService, PERSONAL_TRAINING_CLIENT_TYPES } from "@curefit/personal-training-v2-client"
import { AlertError } from "../common/errors/AlertError"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import { TataUtil } from "@curefit/third-party-integrations-client"
import { IUserSegmentClient, SEGMENTATION_CLIENT_TYPES } from "@curefit/segmentation-service-client"
import { TataNeuUtil } from "../util/TataNeuUtil"
import { PaymentUtil } from "@curefit/payment-models"
import { AttributeKeyType } from "@curefit/membership-commons"
import { GYMFIT_CLIENT_TYPES, IGymfitService } from "@curefit/gymfit-client"
import { UpgradePricingRequest } from "@curefit/gymfit-common/dist/src/Request"
import { GymfitAccessLevel, GymfitFitnessProduct, GymfitProductType } from "@curefit/gymfit-common"
import * as momentTz from "moment-timezone"
import GymfitUtil from "../util/GymfitUtil"
import { HeadersUtil } from "../../util/HeadersUtil"
import { User } from "@curefit/user-common"
import { IAppConfigStoreService } from "../appConfig/AppConfigStoreService"
import CatalogueServiceUtilities from "../util/CatalogueServiceUtilities"
import EmailValidator from "../common/validator/EmailValidator"
import { ProductSubType } from "@curefit/pack-management-service-common"
import { PILATES_CENTER_IDS } from "../util/CultUtil"

const crypto = require("crypto")

export interface OrderCheckoutResponse {
    orderMeta: OrderMeta,
    widgets: WidgetView[]
}

export interface IAPOrderReviewResponse {
    cartReviewResponse: CartReviewResponse,
    checkoutResponse: OrderCheckoutResponse
}

export interface OrderCheckoutAndGatewayOrderResponse {
    checkoutResponse: OrderCheckoutResponse,
    gatewayOrderResponse: CfApiCreateGatewayOrderResponse
}

function controllerFactory(kernel: Container) {
    @controller("/order", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class OrderController {

        constructor(
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
            @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
            @inject(CATALOG_CLIENT_TYPES.CatalogServiceReaderPMS) private catalogueServicePMS: ICatalogueServicePMS,
            @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
            @inject(CUREFIT_API_TYPES.UserBusiness) private userBusiness: IUserBusiness,
            @inject(DELIVERY_CLIENT_TYPES.GateService) private gateService: IGateService,
            @inject(CUREFIT_API_TYPES.OrderViewBuilder) private orderViewBuilder: OrderViewBuilder,
            @inject(CUREFIT_API_TYPES.OrderConfirmationViewBuilder) private orderConfirmationViewBuilder: BaseOrderConfirmationViewBuilder,
            @inject(CUREFIT_API_TYPES.OrderConfirmationViewBuilderV1) private orderConfirmationViewBuilderV1: OrderConfirmationViewBuilderV1,
            @inject(CUREFIT_API_TYPES.GearOrderViewBuilder) private gearOrderViewBuilder: GearOrderViewBuilder,
            @inject(CUREFIT_API_TYPES.GearOrderWithShipmentViewBuilder) private gearOrderWithShipmentViewBuilder: GearOrderWithShipmentViewBuilder,
            @inject(GEARVAULT_CLIENT_TYPES.GearService) private gearService: GearService,
            @inject(LOCATION_TYPES.CityService) private cityService: ICityService,
            @inject(CUREFIT_API_TYPES.CultBusiness) private cultBusiness: ICultBusiness,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
            @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness,
            @inject(ERROR_COMMON_TYPES.RollbarService) public rollbarService: RollbarService,
            @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) private offerServiceV2: IOfferServiceV2,
            @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) private offerServiceV3: OfferServiceV3,
            @inject(PAYMENT_TYPES.IPaymentClient) protected paymentClient: IPaymentClient,
            @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
            @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
            @inject(PAYMENT_TYPES.PaymentPromUtil) protected paymentPromUtil: PaymentPromUtil,
            @inject(OMS_API_CLIENT_TYPES.OrderService) private omsApiClient: IOmsApiClient,
            @inject(HAMLET_CLIENT_TYPES.HamletService) private hamletService: IHamletService,
            @inject(CENTER_SERVICE_CLIENT_TYPES.CenterService) private centerService: ICenterService,
            @inject(PERSONAL_TRAINING_CLIENT_TYPES.PersonalTrainingService) private ptService: IPersonalTrainingService,
            @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) public membershipService: IMembershipService,
            @inject(SEGMENTATION_CLIENT_TYPES.UserSegmentClient) public segmentationClient: IUserSegmentClient,
            @inject(OFFER_SERVICE_CLIENT_TYPES.OfferUtil) private offerUtil: OfferUtil,
            @inject(GYMFIT_CLIENT_TYPES.GymfitService) private gymfitService: IGymfitService,
            @inject(CUREFIT_API_TYPES.AppConfigStoreService) private appConfigStore: IAppConfigStoreService,
        ) {
        }

        /**
         *
         * This has to be before `/:orderId` route
         * Returns a static set of filters for order history
         */
        @httpGet("/filters")
        async getOrderFilters(req: express.Request): Promise<SourceFilterSet> {
            /**
             * Select a number randomly between 20 and 100
             */
            const allSelectedIdForCategory: number = Math.ceil(Math.random() * (100 - 20) + 20)
            /**
             * Select a number between 1000 and 2000
             */
            const allSelectedIdForDateRange: number = Math.ceil(Math.random() * (2000 - 1000) + 1000)
            const userContext: UserContext = req.userContext
            userContext.userProfile.hamletExperimentMap = await this.populateHamletExperimentMap(userContext)
            const topLevelFilters: Array<OrderTopLevelFilter> = [
                {
                    id: 1,
                    displayName: "Category",
                    name: "CATEGORY",
                    allSelectedId: allSelectedIdForCategory
                },
                {
                    id: 2,
                    displayName: "Date Range",
                    name: "DATE_RANGE",
                    allSelectedId: allSelectedIdForDateRange
                }
            ]

            let gearCategoryFilter: any
            if (AppUtil.isStoreTabSupported(userContext)) {
                gearCategoryFilter = {
                    displayName: "store",
                    filter: "STORE"
                }
            } else {
                gearCategoryFilter = {
                    displayName: "gear",
                    filter: "CULT_GEAR"
                }
            }
            let wellnessFilter: any
            if (AppUtil.isWellnessTabSupported(userContext)) {
                wellnessFilter = [{
                    displayName: "wellness",
                    filter: "WELLNESS"
                }]
            } else {
                wellnessFilter = [
                    {
                        displayName: "eat.fit",
                        filter: "EAT"
                    },
                    {
                        displayName: "mind.fit",
                        filter: "MIND"
                    },
                    {
                        displayName: "care.fit",
                        filter: "CARE"
                    },
                    gearCategoryFilter, // to support new store filter
                ]
            }

            const cultfilter = AppUtil.isInternationalApp(userContext) ? [
                {
                    displayName: "cult.fit",
                    filter: "CULT"
                },
                {
                    displayName: "cultpass",
                    filter: "GYMFIT"
                },
                {
                    displayName: LiveUtil.getLiveBranding(userContext),
                    filter: "LIVE"
                }
            ] : [
                {
                    displayName: "cultpass",
                    filter: "CULT_PASS"
                }
            ]

            const categoryFilters: Array<OrderCategoryFilter> = [
                {
                    displayName: "All",
                    filter: "ALL"
                },
                ...cultfilter
            ].map(
                (x, i) => ({ ...x, id: allSelectedIdForCategory + i })
            ) as Array<OrderCategoryFilter>
            const dateRangeFilters: Array<OrderDateRangeFilter> = [
                {
                    displayName: "All",
                    filter: "all"
                },
                {
                    displayName: "Yesterday",
                    filter: "yesterday"
                },
                {
                    displayName: "Past 7 Days",
                    filter: "seven-days",
                },
                {
                    displayName: "Past Month",
                    filter: "past-month"
                },
                {
                    displayName: "Past 6 Months",
                    filter: "past-six-months"
                }
            ].map(
                (x, i) => ({ ...x, id: allSelectedIdForDateRange + i })
            ) as Array<OrderDateRangeFilter>
            return {
                topLevelFilters,
                categoryFilters,
                dateRangeFilters
            }
        }

        @httpGet("/:orderId")
        async getOrder(req: express.Request): Promise<OrderInfo> {
            const orderId: string = req.params.orderId
            const session: Session = req.session
            const order = await this.fetchOrderWithValidation(orderId)
            if (order.userId !== session.userId) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_AUTHORIZED_TO_VIEW_ORDER, 401).withDebugMessage("Not authorized to view order").build()
            }
            const userContext: UserContext = req.userContext as UserContext
            return this.orderViewBuilder.getOrderInformation(order, userContext)
        }

        @httpGet("/gear/:orderId")
        async getGearOrderDetails(req: express.Request): Promise<GearOrderDetail> {
            const orderId: string = req.params.orderId
            const session: Session = req.session
            const userContext: UserContext = req.userContext as UserContext
            userContext.userProfile.hamletExperimentMap = await this.populateHamletExperimentMap(userContext)
            const order = await this.fetchOrderWithValidation(orderId)
            if (order.userId !== session?.userId) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_AUTHORIZED_TO_VIEW_ORDER, 401).withDebugMessage("Not authorized to view order").build()
            }
            const gearOrder = await this.gearService.getOrder(orderId)
            const totalPrepaymentAmount = await this.paymentClient.getTotalPrepaymentAmount(gearOrder.external_service_order_id)
            return this.gearOrderViewBuilder.buildOrderDetails(gearOrder, order, userContext, totalPrepaymentAmount)
        }

        @httpGet("/v2/gear/:orderId")
        async getGearOrderDetailsWithShipmentStatus(req: express.Request): Promise<GearOrderDetail> {
            const orderId: string = req.params.orderId
            const session: Session = req.session

            const order = await this.fetchOrderWithValidation(orderId)
            if (order.userId !== session?.userId) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_AUTHORIZED_TO_VIEW_ORDER, 401).withDebugMessage("Not authorized to view order").build()
            }

            const userContext: UserContext = req.userContext as UserContext
            userContext.userProfile.hamletExperimentMap = await this.populateHamletExperimentMap(userContext)
            const [gearDetails, gearOrder, alfredOrder] = await Promise.all([
                this.gearService.getOrder(orderId),
                this.gearService.getOrderWithShipmentStatus(orderId),
                this.omsApiClient.getOrder(orderId),
            ])

            const offerData = !_.isEmpty(alfredOrder.offersApplied) ? (await this.offerServiceV3.getOffersByIds(alfredOrder.offersApplied)).data : null
            // Getting the first Coupon offer and addonOfferType
            const addonOfferIds: { offerId: string, addonType: OfferAddonType }[] = !_.isEmpty(offerData) ?
                _.compact(Object.keys(offerData).map(offerId => {
                    if (!_.isEmpty(offerData[offerId].addons)) {
                        const addon: OfferAddon = offerData[offerId].addons.find(({ addonType }) => addonType === "COUPON")
                        return addon ? { offerId, addonType: addon.addonType } : null
                    }
                    return null
                })) : []

            if (!_.isEmpty(addonOfferIds)) {
                const rewardsData = await Promise.all(addonOfferIds.map(async ({ offerId, addonType }) => {
                    // Limiting the scope only for 3P coupons & fitcash
                    const offer = offerData[offerId]
                    let isEligible: boolean
                    if (addonType === "COUPON") {
                        const orderSummary = await this.gearService.getOrderSummary(orderId)
                        isEligible = this.offerUtil.speculateCouponForGear(order, offer, orderSummary)
                    }
                    return {
                        isEligible,
                        offer
                    }
                }))
                const orderView = await this.gearOrderWithShipmentViewBuilder.buildOrderDetails(alfredOrder, gearOrder, userContext, gearDetails, rewardsData)
                return orderView
            }

            const orderView = await this.gearOrderWithShipmentViewBuilder.buildOrderDetails(alfredOrder, gearOrder, userContext, gearDetails)
            return orderView
        }

        @httpPost("/:orderId/downloadInvoice")
        async downloadInvoice(req: express.Request): Promise<any> {
            const orderId = req.params.orderId
            const order = await this.fetchOrderWithValidation(orderId)
            const session: Session = req.session
            if (order.userId !== session.userId) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_AUTHORIZED_TO_VIEW_ORDER, 401).withDebugMessage("Not authorized to view order").build()
            }
            const downloadedInvoice: { url: string } = await this.omsApiClient.downloadInvoices(orderId)
            return {
                action: {
                    actionType: "SET_AND_CLICK_ANCHOR",
                    fileName: "Invoice",
                    "trigger": true,
                    url: downloadedInvoice.url
                }
            }
        }

        @httpPost("/:orderId/sendInvoice")
        async sendInvoice(req: express.Request): Promise<{ success: boolean, alertInfo: AlertInfo }> {
            const orderId = req.params.orderId
            const email = req.body.email
            if (!email) {
                throw this.errorFactory.withCode(ErrorCodes.EMAIL_ID_NOT_PROVIDED_ERR, 400).withDebugMessage("email id not provided to send invoice").build()
            }
            const user = await req.userContext.userPromise
            const order = await this.fetchOrderWithValidation(orderId)
            if (order.userId !== String(user.id)) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_AUTHORIZED_TO_VIEW_ORDER, 401).withDebugMessage("Not authorized to view order").build()
            }
            const isValidEmail: boolean = await new EmailValidator().validate(email)
            if (!isValidEmail) {
                throw this.errorFactory.withCode(ErrorCodes.INVALID_EMAIL, 400)
                    .withDebugMessage("Invalid Email: " + email)
                    .build()
            }
            const result = await this.omsApiClient.emailInvoiceForOrder(orderId, order.productSnapshots[0].productType, email)
            let alertInfo: AlertInfo
            if (!user.email) {
                alertInfo = {
                    title: "Invoice sent successfully",
                    subTitle: "Would you like to verify this email Id for future communications?",
                    actions: [{
                        actionType: "NAVIGATION",
                        title: "YES",
                        url: `curefit://updateemail?emailId=${email}`
                    },
                    {
                        actionType: undefined,
                        title: "NO",
                    }],
                }
            }

            return { success: result, alertInfo }
        }

        @httpGet("/v1/:orderId")
        async getOrderV1(req: express.Request): Promise<OrderDetail> {
            const orderId: string = req.params.orderId
            const session: Session = req.session
            const order: Order = await this.fetchOrderWithValidation(orderId)

            if (order.userId !== session.userId) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_AUTHORIZED_TO_VIEW_ORDER, 401).withDebugMessage("Not authorized to view order").build()
            }
            const userContext: UserContext = req.userContext as UserContext
            userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)

            if (!userContext.userProfile.subUserId) {
                userContext.userProfile.subUserId = order.clientMetadata && (order.clientMetadata as CultMembershipMetadata).subUserId
            }
            userContext.userProfile.hamletExperimentMap = await this.populateHamletExperimentMap(userContext)
            return this.orderViewBuilder.buildOrderDetailView(userContext, order, true, "ORDER_V1")
        }

        @httpGet("/")
        async getOrders(req: express.Request): Promise<OrderDetail[]> {
            const session: Session = req.session
            const offset: number = req.query.offset
            const limit: number = (req.query.limit !== undefined && req.query.limit !== "undefined") ? Number(req.query.limit) : undefined
            const pageToken = req.query.pageToken
            const pageFrom = req.query.pageFrom
            const direction = req.query.direction
            const productType: ProductType = req.query.productType
            const option: Option = req.query.option
            /**
             * Add filters from client side query
             */
            const excludeEatfitExceptCafe = req.query.excludeEatfitExceptCafe || "true"
            const categoryFilter: OrderCategoryFilter["filter"] = req.query.categoryFilter as OrderCategoryFilter["filter"]
            const dateRangeFilter: OrderDateRangeFilter["filter"] = req.query.dateRangeFilter as OrderDateRangeFilter["filter"]
            const userContext: UserContext = req.userContext as UserContext
            userContext.userProfile.hamletExperimentMap = await this.populateHamletExperimentMap(userContext)
            const { osName, appVersion } = userContext.sessionInfo
            const apiKey: string = req.headers["api-key"] as string || req.headers["apikey"] as string
            const orderSourceForFiltering: OrderSource[] = [OrderController.getOrderSourceForFiltering(apiKey)]
            let orderDetailPromises: Promise<OrderDetail>[]
            let nextPageToken = pageToken

            if (MealUtil.isAddMealSupported(userContext)) {
                let orders: BaseOrder[] = [], fetchOrders = true
                while (fetchOrders == true) {
                    fetchOrders = false
                    orders = await this.fetchOrders(session, categoryFilter, dateRangeFilter, nextPageToken, limit, direction, productType
                        , orderSourceForFiltering, osName, appVersion)
                    if (excludeEatfitExceptCafe === "true" && orders.length > 0) {
                        const lastOrder = orders[orders.length - 1]
                        orders = _.filter(orders, order => {
                            return !["FOOD", "GEAR"].includes(order.productSnapshots[0].productType) || order?.userAddress?.kioskType === "CAFE"
                                || !_.isNil(order?.userAddress?.kioskId)  // @TODO: @GauravSachdeva: Move this filtering logic to fetchOrders method
                        })
                        if (orders.length > 0) orders[orders.length - 1].createdDate = lastOrder.createdDate
                        else if (lastOrder.createdDate != undefined) {
                            fetchOrders = true
                            nextPageToken = lastOrder.createdDate
                        }
                    }
                }
                const user: User = await userContext.userPromise
                orderDetailPromises = _.map(orders, order => {
                    // Temp hack as some sub order entity does not have user id
                    order.userId = session.userId
                    return this.orderViewBuilder.buildOrderDetailView(userContext, order, false, "ORDER_V1", pageFrom, user)
                })
            } else {
                let orders: BaseOrder[] = [], fetchOrders = true, nextOffset = offset
                const defaultOrderSourcesToFilterOut: OrderSource[] = ["CULTGEAR_APP", "CULTGEAR_PHONEPE_APP"]
                const orderSourceFilter = !_.isEmpty(_.compact(orderSourceForFiltering)) ? {orderSources: orderSourceForFiltering, include: true} : {orderSources: defaultOrderSourcesToFilterOut, include: false}
                this.logger.info("DEBUG:: order sources - ", {orderSourceFilter, orderSourceForFiltering})
                while (fetchOrders == true) {
                    fetchOrders = false
                    orders = await this.omsApiClient.getViableOrdersForUser(session.userId, {
                        pageToken: undefined,
                        count: limit,
                        offset: nextOffset,
                        sortOrder: SortOrder.DESC,
                        productType: productType,
                        isPack: option === "PACK",
                        orderSourceFilter,
                        includePaymentFailures: true
                    })
                    if (excludeEatfitExceptCafe === "true" && orders.length > 0) {
                        const lastOrder = orders[orders.length - 1]
                        orders = _.filter(orders, order => {
                            return !["FOOD", "GEAR"].includes(order.productSnapshots[0].productType) || order?.userAddress?.kioskType === "CAFE"  // @TODO: @GauravSachdeva: Move this filtering logic to OMS getViableOrdersForUser method
                        })
                        if (orders.length > 0) orders[orders.length - 1].createdDate = lastOrder.createdDate
                        else if (lastOrder.createdDate != undefined) {
                            fetchOrders = true
                            nextOffset = nextOffset + limit
                        }
                    }
                }
                orderDetailPromises = _.map(orders, order => {
                    return this.orderViewBuilder.buildOrderDetailView(userContext, order, false, "ORDER_V1", pageFrom)
                })
            }
            const orderDetailsArray: OrderDetail[] = await Promise.all(orderDetailPromises)
            return _.filter(orderDetailsArray, orderDetail => !_.isEmpty(orderDetail))
        }

        @httpGet("/tata/isPaidOrderPresent")
        async hasTataOrders(req: express.Request): Promise<boolean> {
            const session: Session = req.session
            const orders: Order[] = await this.omsApiClient.getFilteredOrdersForUser(session.userId, {
                statusFilter: {
                    filterType: "PAYMENT",
                    status: "SUCCESS"
                },
                count: 1,
                sortKey: "UPDATED_DATE",
                after: Number(req.query.after)
            })
            return !_.isEmpty(orders)
        }

        @httpGet("/tata/PWA")
        async getTataPWAOrders(req: express.Request): Promise<OrderDetail[]> {
            const session: Session = req.session
            const nonFoodOrders: Order[] = await this.omsApiClient.getFilteredOrdersForUser(session.userId, {
                productFilters: {
                    productType: {
                        nin: ["FOOD"]
                    }
                },
                statusFilter: {
                    filterType: "PAYMENT",
                    status: "SUCCESS"
                },
                count: Number(req.query.count),
                before: Number(req.query.before),
                sortDirection: "DESC"
            })
            const orders: Order[] = []
            for (const order of nonFoodOrders) {
                if (TataUtil.isTataNeuProduct(order)) {
                    orders.push(order)
                }
            }
            const userContext: UserContext = req.userContext as UserContext
            userContext.userProfile.hamletExperimentMap = await this.populateHamletExperimentMap(userContext)
            const orderDetailPromises: Promise<OrderDetail>[] = _.map(orders, order => {
                return this.orderViewBuilder.buildOrderDetailView(userContext, order, false, "ORDER_V1").catch(err => {
                    this.logger.error(`Failed to build order view for tata PWA listing, err: `, { err })
                    return undefined
                })
            })
            const orderDetailsArray: OrderDetail[] = await Promise.all(orderDetailPromises)
            return _.filter(orderDetailsArray, orderDetail => !_.isEmpty(orderDetail))
        }

        public static getOrderSourceForFiltering(apiKey: string): OrderSource {
            const callSource: OrderSource = AppUtil.callSource(apiKey)
            if (SUPPORTED_MICROAPPS_ORDER_SOURCE.has(callSource)) {
                return callSource
            }
            return undefined
        }

        @httpPost("/createGatewayOrder/:orderId")
        async createGatewayOrder(req: express.Request): Promise<CreateGatewayOrderResponse> {
            const orderId: string = req.params.orderId
            const session: Session = req.session
            const gatewayType: string = req.body.gatewayType
            const paymentMode: PaymentMode = req.body.paymentMode
            const redirectUrl: string = req.body.redirectUrl
            const failureUrl: string = req.body.failureUrl
            const requestMeta: any = req.body.meta
            const userContext: UserContext = req.userContext as UserContext
            const appVersion: number = _.get(userContext, "sessionInfo.appVersion", 0)
            const osName: string = _.get(userContext, "sessionInfo.osName")
            const orderSource: OrderSource = _.get(userContext, "sessionInfo.orderSource")
            const isMweb: boolean = AppUtil.isMWeb(userContext)
            _.set(requestMeta, "isMweb", isMweb)
            return this.createGatewayOrderId(orderId, session, gatewayType, paymentMode, redirectUrl, failureUrl, requestMeta, appVersion, osName, orderSource)
        }

        private async createGatewayOrderId(orderId: string, session: Session, gatewayType: string, paymentMode: PaymentMode, redirectUrl: string, failureUrl: string, requestMeta: any, appVersion: number, osName: string, orderSource: OrderSource): Promise<CreateGatewayOrderResponse> {
            if (gatewayType === "JUSPAY" && appVersion >= 8.82 && _.isEmpty(redirectUrl)) {
                redirectUrl = "https://www.cult.fit/eat/about"
            }
            if ((gatewayType === "RAZORPAY" && appVersion < 9.43 && "CUREFIT_APP" === orderSource)
                || (gatewayType === "RAZORPAY" && appVersion < 2.08 && "SUGARFIT_APP" === orderSource)) {
                throw this.errorFactory.withCode("RAZORPAY_NOT_SUPPORTED", HTTP_CODE.BAD_REQUEST)
                    .withMessage("Please update your app to use this payment mode.")
                    .build()
            }
            const isOMSPaymentInitFlow: boolean = await this.shouldOMSInitiatePayment(paymentMode, gatewayType, orderId, session.userId, session.deviceId)
            this.logger.info(`createGatewayOrderId:isOMSPaymentInitFlow for order: ${orderId}  user: ${session.userId} = ${isOMSPaymentInitFlow}`)
            if (isOMSPaymentInitFlow) {
                const initiatePaymentResponse: CreateGatewayOrderResponse = await this.omsApiClient.initiatePayment({
                    orderId: orderId,
                    userId: session.userId,
                    gatewayType: gatewayType as PaymentChannel,
                    paymentMode: paymentMode,
                    redirectUrl: redirectUrl,
                    failureUrl: failureUrl,
                    meta: requestMeta
                })
                return initiatePaymentResponse
            } else {
                const { gatewayId, customerId, subscriptionId, subscriptionOrderId, paymentLinks, appleInAppPurchaseDetails, meta } = await this.omsApiClient.createGatewayOrder(
                    session.userId, orderId, gatewayType, paymentMode, undefined, undefined, redirectUrl, failureUrl, requestMeta)
                return {
                    gatewayId: gatewayId,
                    gatewayType: gatewayType,
                    customerId: customerId,
                    subscriptionId: subscriptionId,
                    subscriptionOrderId: subscriptionOrderId,
                    paymentLinks: paymentLinks,
                    appleInAppPurchaseDetails: appleInAppPurchaseDetails,
                    meta: meta,
                    cfPaymentId: undefined
                }
            }
        }

        private async shouldOMSInitiatePayment(paymentMode: PaymentMode, gatewayType: string, orderId: string, userId: string, deviceId: string): Promise<boolean> {
            if (paymentMode === "COD") {
                // alfred to initiate payment because COD orders are fulfilled at the time of payment-init.
                return false
            }
            if (gatewayType === "MANUAL") {
                // OMS does not support manual payment channel.
                this.logger.info(`Got MANUAL payment-init request for orderId: ${orderId}`)
                return false
            }
            // const order: BaseOrder = await this.omsService.getOrderById(orderId)
            // if (order?.productSnapshots?.[0]?.productType === "FOOD") {
            //     return false
            // }
            return true
        }

        @httpPost("/checkout")
        async checkout(req: express.Request): Promise<OrderCheckoutResponse> {
            const session: Session = req.session
            const orderProduct: OrderProduct = req.body.orderProduct
            let orderProducts: OrderProduct[] = req.body.orderProducts
            if (!(orderProducts?.length > 0)) {
                orderProducts = [orderProduct]
            }
            const dontCreateRazorpayOrder: boolean = req.body.dontCreateRazorpayOrder
            // const rescheduleSourceBookingNumber: string = req.body.rescheduleSourceBookingNumber
            const userId: string = session.userId
            const apiKey: string = req.headers["api-key"] as string || req.headers["apikey"] as string
            const userContext: UserContext = req.userContext as UserContext
            userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)

            const useFitCash: boolean = req.body.useFitCash ? req.body.useFitCash : false
            const advertiserId: string = req.body.advertiserId
            const clientMetadata: CultMembershipMetadata = req.body.clientMetadata ? req.body.clientMetadata : {}
            if (req.body.state === "ONBOARDING") {
                clientMetadata.isOnboardingSession = true
            }
            const countryId = _.get(userContext, "userProfile.city.countryId", null)
            if (countryId !== "IN") {
                req.body.useFitCash = false
            }
            let activateNeuPass: boolean = req.body.activateNeuPass
            const orderSource: OrderSource = AppUtil.callSource(apiKey)
            const product = await this.catalogueServicePMS.getCatalogProduct(orderProduct.productId)
            if (!activateNeuPass && orderProduct && orderProduct.productId
                && product?.productType && product.productType !== "GEAR"
                && TataUtil.isTataNeuProductIdOrType(orderProduct.productId, product.productType)
                && ["CUREFIT_APP", "CUREFIT_NEW_WEBSITE", "TATA_NEU_WEB_APP"].includes(orderSource)) {
                if (await this.isNeuPassActivationCheckboxShown(userId, userContext)) {
                    activateNeuPass = true
                }
            }
            return this.orderCheckout(session, orderProduct, orderProducts, dontCreateRazorpayOrder, apiKey, userContext, useFitCash, advertiserId, clientMetadata, req.body.utm, activateNeuPass, this.appConfigStore)
        }

        @httpPost("/toggleActivateNeuPass")
        async toggleActivateNeuPass(req: express.Request): Promise<void> {
            const orderId: string = req.body.orderId
            const order = await this.fetchOrderWithValidation(orderId)
            const session: Session = req.session
            if (order.userId !== session.userId) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_AUTHORIZED_TO_VIEW_ORDER, 401).withDebugMessage("Not authorized to view order").build()
            }
            const activateNeuPass: boolean = req.body.activateNeuPass
            await this.omsApiClient.toggleActivateNeuPass(orderId, activateNeuPass)
        }

        private static setOrderProductRequiredFields(orderProduct: OrderProduct) {
            if (CareUtil.isAnxietyTherapy(orderProduct.productId)) {
                /**
                 * Get virtual center ID from base utils
                 */
                orderProduct.option.centerId = BaseCareUtil.getVirtualCenterId().toString()
            }
            orderProduct.quantity = 1
            // Temp hack as website is passing offer id as empty
            orderProduct.option.offerId = !_.isEmpty(orderProduct.option.offerId) ? orderProduct.option.offerId : undefined
        }

        private async orderCheckout(session: Session, orderProduct: OrderProduct, orderProducts: OrderProduct[], dontCreateRazorpayOrder: boolean, apiKey: string, userContext: UserContext, useFitCash: boolean, advertiserId: string, clientMetadata: CultMembershipMetadata, utm: any, activateNeuPass: boolean, appConfigStore: IAppConfigStoreService): Promise<OrderCheckoutResponse> {
            const userId: string = session.userId
            let orderSource: OrderSource = AppUtil.callSource(apiKey)
            if (session.sessionData.attributionSource === "FITTERNITY") {
                orderSource = "FITTERNITY"
            }
            orderProducts.forEach(orderProduct => OrderController.setOrderProductRequiredFields(orderProduct))
            clientMetadata.subUserId = userContext.userProfile.subUserId ? userContext.userProfile.subUserId : clientMetadata.subUserId
            const isUpgradeMembership = (clientMetadata as any)?.isUpgradeMembership
            const dontApplyOffers = isUpgradeMembership || orderProduct?.option?.isTransferringMembership // don't apply offers for transfers and upgrades
            const orderCreate: OrderCreate = {
                userId: session.userId,
                deviceId: session.deviceId,
                products: orderProducts,
                source: orderSource,
                dontCreateRazorpayOrder: dontCreateRazorpayOrder,
                dontApplyOffers: dontApplyOffers ?? false,
                clientMetadata: clientMetadata,
                useOffersV2: true,
                cityId: session.sessionData.cityId,
                useFitCash: useFitCash,
                utm: utm,
                tenant: AppUtil.getTenantFromUserContext(userContext),
                activateNeuPass: activateNeuPass,
                osName: userContext.sessionInfo.osName,
                appVersion: (userContext.sessionInfo.appVersion ?? 0).toString()
            }

            const checkoutRequest: OrderCheckoutRequest = _.cloneDeep({
                userId: session.userId,
                deviceId: session.deviceId,
                orderProducts: orderProducts,
                source: orderSource,
                clientMetadata: clientMetadata,
                cityId: session.sessionData.cityId,
                useFitCash: useFitCash,
                utm: utm,
                dontApplyOffers: dontApplyOffers ?? false,
                tenant: AppUtil.getTenantFromUserContext(userContext),
                activateNeuPass: activateNeuPass,
                osName: userContext.sessionInfo.osName,
                advertiserId: advertiserId,
                userAddress: undefined,
                appVersion: (userContext.sessionInfo.appVersion ?? 0).toString()
            })

            if (!_.isNil(advertiserId)) orderCreate.advertiserId = advertiserId
            return this.catalogueServicePMS.getCatalogProduct(orderProduct.productId).then(async product => {
                const omsOrderProduct = checkoutRequest.orderProducts.find(omsProduct => omsProduct.productId === orderProduct.productId)
                if (orderProduct.option && orderProduct.option.offerId) {
                    orderProduct.option.offerV2Ids = [orderProduct.option.offerId]
                    orderProduct.option.offerId = undefined
                    if (omsOrderProduct) {
                        omsOrderProduct.option.offerV2Ids = [orderProduct.option.offerId]
                        omsOrderProduct.option.offerId = undefined
                    }
                }
                if (product.productType === "FOOD") {
                    throw this.errorFactory.withCode(ErrorCodes.ERR_METHOD_NOT_IMPLEMENTED, 500).withDebugMessage("Order Checkout for Food not implemented").build()
                }
                else if (product.productType === "GYM_PT_PRODUCT") {
                    const startTime: number = orderProduct.option.startTime
                    const endTime: number = orderProduct.option.endTime
                    const trainerId: number = orderProduct.option.doctorId
                    const centerId: number = Number(orderProduct.option.centerId)
                    const user = await this.userService.getUser(session.userId)
                    const orderMeta: OrderMeta = {
                        orderId: "dummmy#",
                        price: product.price,
                        customerName: user.firstName,
                        customerPhone: user.phone,
                        customerEmail: user.email,
                        orderType: "GYMFIT_FITNESS_PRODUCT",
                        productType: "GYM_PT_PRODUCT",
                        productId: orderProduct.productId
                    }
                    const service: string = "GYMFIT_PERSONAL_TRAINING"
                    const searchRequest: SessionSearchRequest = {
                        tenantId: 1,
                        userIds: [user.id.toString()],
                        identityIds: [trainerId.toString()],
                        centerIds: [centerId.toString()],
                        startTimeTo: endTime,
                        endTimeFrom: startTime,
                        statuses: [PtSessionStatus.CREATED],
                        pageNo: 0,
                        pageSize: 1
                    }
                    const sessions: PtSession[] = await this.ptService.searchSessions(searchRequest, HeadersUtil.getCommonHeaders(userContext))
                    if (sessions.length > 0) {
                        const cancelAudit: Audit = {
                            agentHost: "curefit-api",
                            agentId: userId,
                            annotation: "Re-book Session",
                            source: Source.CUREFIT_API
                        }
                        for (const session of sessions) {
                            await this.ptService.cancelSession(session.id, cancelAudit, HeadersUtil.getCommonHeaders(userContext))
                        }
                    }
                    const sessionBookRequest: PersonalTrainingBookingRequest = {
                        "userId": user.id.toString(),
                        "identityId": trainerId.toString(),
                        "centerId": centerId.toString(),
                        "service": service,
                        "startTime": startTime,
                        "endTime": endTime,
                        "sessionBookingSource": PtBookingSource.CUREFIT_APP,
                        source: Source.CUREFIT_API,
                        audit: {
                            agentHost: "curefit-api",
                            agentId: userId,
                            annotation: "Confirm Session",
                            source: Source.CUREFIT_API
                        }
                    }
                    try {
                        await this.ptService.blockSession(sessionBookRequest, HeadersUtil.getCommonHeaders(userContext))
                    } catch (err) {
                        if (err.status && err.status === 412) {
                            throw new AlertError("Invalid Request", err.message, [], err)
                        }
                        else {
                            throw err
                        }
                    }
                    return this.orderViewBuilder.getGymPtBookingConfirmationWidgets(userContext, startTime, endTime, user, trainerId, centerId, product, orderMeta)
                }
                else if (product.productType === "GYM_PT_PPC_PRODUCT") {
                    const user = await this.userService.getUser(session.userId)
                    const result = AppUtil.getUserAlertInfo(user, userContext)
                    const code: string = result.code
                    if (!_.isEmpty(code)) {
                        throw this.errorFactory.withCode(result.code, result.alertInfo.statusCode).build()
                    }
                    const startTime: number = orderProduct.option.startTime
                    const endTime: number = orderProduct.option.endTime
                    const trainerId: number = orderProduct.option.doctorId
                    const centerId: number = Number(orderProduct.option.centerId)
                    let order: Order
                    const searchRequest: SessionSearchRequest = {
                        tenantId: 1,
                        userIds: [user.id],
                        identityIds: [trainerId.toString()],
                        centerIds: [centerId.toString()],
                        startTimeTo: endTime,
                        endTimeFrom: startTime,
                        statuses: [PtSessionStatus.CREATED],
                        pageNo: 0,
                        pageSize: 1
                    }
                    const sessions: PtSession[] = await this.ptService.searchSessions(searchRequest, HeadersUtil.getCommonHeaders(userContext))
                    if (sessions.length > 0) {
                        const cancelAudit: Audit = {
                            agentHost: "curefit-api",
                            agentId: userId,
                            annotation: "Re-book Session",
                            source: Source.CUREFIT_API
                        }
                        for (const session of sessions) {
                            await this.ptService.cancelSession(session.id, cancelAudit, HeadersUtil.getCommonHeaders(userContext))
                        }
                        const service: string = "GYMFIT_PERSONAL_TRAINING"
                        const sessionBookRequest: PersonalTrainingPpcBookingRequest = {
                            "userId": userId.toString(),
                            "identityId": trainerId.toString(),
                            "centerId": centerId.toString(),
                            "service": service,
                            "startTime": startTime,
                            "endTime": endTime,
                            "accessId": sessions[0].accessId,
                            "accessType": sessions[0].accessType,
                            "sessionBookingSource": PtBookingSource.CUREFIT_APP,
                            source: Source.CUREFIT_API,
                            isOnboardingSession: (order.clientMetadata as CultMembershipMetadata)?.isOnboardingSession,
                            audit: {
                                agentHost: "curefit-api",
                                agentId: userId,
                                annotation: "Book Session",
                                source: Source.CUREFIT_API
                            }
                        }
                        try {
                            await this.ptService.bookPpcSession(sessionBookRequest, HeadersUtil.getCommonHeaders(userContext))
                        } catch (err) {
                            if (err.status && err.status === 412) {
                                throw new AlertError("Invalid Request", err.message, [], err)
                            }
                            else {
                                throw err
                            }
                        }
                        order = await this.omsApiClient.getOrder(sessions[0].accessId)
                    } else {
                        const centerServiceResponse: CenterResponse = await this.centerService.getCenterById(Number(centerId))
                        try {
                            const memberships = await this.membershipService.getMembershipsForUser(session.userId, "curefit", ["GYMFIT_GA", "GYMFIT_GX", "CULT", "LUX"], ["PURCHASED"], Number(orderProduct.option.startTime), Number(orderProduct.option.startTime))
                            if (!PILATES_CENTER_IDS.includes(centerServiceResponse.id) && memberships.length === 0) {
                                throw this.errorFactory.withCode("You do not have any active membership, please purchase cultpass that suits your needs to book session", HTTP_CODE.BAD_REQUEST).build()
                            }
                        } catch (err) {
                            throw new AlertError("Buy a cultpass", "You do not have any active membership, please purchase cultpass that suits your needs to book session", [], err)
                        }
                        let uniqueProducts = [...new Map(orderCreate.products.map(item => [item.productId, item])).values()]
                        const gymCenterId = centerServiceResponse.meta.gymfitCenterId
                        uniqueProducts = uniqueProducts.map(product => {
                            return {
                                ...product,
                                option: {
                                    centerId: gymCenterId.toString(),
                                    gymPtOrderOptions: {
                                        preferredTrainerId: trainerId.toString(),
                                        ppcSlotStartTime: startTime,
                                        ppcSlotEndTime: endTime
                                    }
                                }
                            }
                        })
                        const createOrderPayload: OrderCreate = {
                            ...orderCreate,
                            products: uniqueProducts,
                            offersVersion: 3
                        }
                        createOrderPayload.clientMetadata = Object.assign({}, createOrderPayload.clientMetadata, {
                            centerId: gymCenterId,
                            centerServiceCenterId: centerServiceResponse.id
                        })
                        checkoutRequest.clientMetadata = Object.assign({}, checkoutRequest.clientMetadata, {
                            centerId: gymCenterId,
                            centerServiceCenterId: centerServiceResponse.id
                        })
                        checkoutRequest.orderProducts = uniqueProducts
                        try {
                            if (await this.orderViewBuilder.isOMSCheckoutFlow(userContext?.userProfile?.userId, userContext?.sessionInfo?.deviceId, product, "CHECKOUT", AppUtil.getTenantFromUserContext(userContext))) {
                                const checkoutResponse: OMSOrderCheckoutResponse = await this.omsApiClient.checkoutOrder(checkoutRequest)
                                order = checkoutResponse.order
                            } else {
                                createOrderPayload.omsCheckoutRequest = checkoutRequest
                                order = await this.omsApiClient.createOrder(createOrderPayload)
                            }
                        } catch (err) {
                            if (err.statusCode && err.statusCode === 412) {
                                throw new AlertError("Invalid Request", err.code, [], err)
                            }
                            else {
                                this.logger.info(` unexpected `)
                                throw err
                            }
                        }
                    }
                    return this.orderViewBuilder.buildView({
                        userContext: userContext,
                        order: order,
                        product: order.productSnapshots[0],
                        apiType: "ORDER_CHECKOUT",
                        appConfigStore: appConfigStore
                    })
                }
                else {
                    const user = await this.userService.getUser(session.userId)
                    const result = AppUtil.getUserAlertInfo(user, userContext)
                    const tz = userContext.userProfile.timezone
                    const code: string = result.code
                    if (!_.isEmpty(code)) {
                        throw this.errorFactory.withCode(result.code, result.alertInfo.statusCode).build()
                    }
                    if ((product.productType === "FITNESS" || product.productType === "MIND" || product.productType === "CF_LIVE") && product.isPack) {
                        // Temp fix to handle the case where device is passing past date  as start date
                        if (orderCreate.products[0].option.startDate < TimeUtil.todaysDateWithTimezone(tz)) {
                            orderCreate.products[0].option.startDate = TimeUtil.todaysDateWithTimezone(tz)
                            checkoutRequest.orderProducts[0].option.startDate = TimeUtil.todaysDateWithTimezone(tz)
                        }

                        if (orderCreate.products[0].option.centerId) {
                            orderCreate.clientMetadata = Object.assign({}, orderCreate.clientMetadata, {
                                centerId: orderCreate.products[0].option.centerId
                            })
                            checkoutRequest.clientMetadata = Object.assign({}, checkoutRequest.clientMetadata, {
                                centerId: checkoutRequest.orderProducts[0].option.centerId
                            })
                        }

                        if (orderCreate.products[0].option.centerServiceCenterId) {
                            orderCreate.clientMetadata = Object.assign({}, orderCreate.clientMetadata, {
                                centerServiceCenterId: orderCreate.products[0].option.centerServiceCenterId
                            })
                            checkoutRequest.clientMetadata = Object.assign({}, checkoutRequest.clientMetadata, {
                                centerServiceCenterId: checkoutRequest.orderProducts[0].option.centerServiceCenterId
                            })
                        }

                        const metadata = await this.cultBusiness.getClientMetadataForOrder(userContext, orderCreate, product)
                        if (metadata) {
                            orderCreate.clientMetadata = Object.assign({}, orderCreate.clientMetadata, { ...metadata })
                            checkoutRequest.clientMetadata = Object.assign({}, checkoutRequest.clientMetadata, {...metadata})
                        }
                        if (product.productType === "FITNESS") {
                            const fitnessProduct = await this.catalogueServicePMS.getProduct(product.productId)
                            if (AppUtil.isWeb(userContext) && fitnessProduct?.product?.productSubType === ProductSubType.ADDONBUNDLE) {
                                const elitePlusAddOnIds = fitnessProduct?.baseAddOns?.map((addOn) => addOn?.packId)
                                if (elitePlusAddOnIds?.length > 0) {
                                    omsOrderProduct.addonProductIds = elitePlusAddOnIds
                                }
                            }
                            if (!_.isNil(fitnessProduct) && !_.isNil(fitnessProduct.clientMetadata.cityId)) {
                                orderCreate.cityId = fitnessProduct.clientMetadata.cityId
                                checkoutRequest.cityId = fitnessProduct.clientMetadata.cityId
                            }
                        }

                    }
                    if (product.productType === "PLAY") {
                        if (product.isPack) {
                            // Temp fix to handle the case where device is passing past date  as start date
                            if (orderCreate.products[0].option.startDate < TimeUtil.todaysDateWithTimezone(tz)) {
                                orderCreate.products[0].option.startDate = TimeUtil.todaysDateWithTimezone(tz)
                                checkoutRequest.orderProducts[0].option.startDate = TimeUtil.todaysDateWithTimezone(tz)
                            }

                            if (orderCreate.products[0].option.centerId) {
                                orderCreate.clientMetadata = Object.assign({}, orderCreate.clientMetadata, {
                                    centerId: orderCreate.products[0].option.centerId,
                                    centerServiceCenterId: orderCreate.products[0].option.centerId
                                })
                                checkoutRequest.clientMetadata = Object.assign({}, checkoutRequest.clientMetadata, {
                                    centerId: checkoutRequest.orderProducts[0].option.centerId,
                                    centerServiceCenterId: checkoutRequest.orderProducts[0].option.centerId
                                })
                            }

                            if (orderCreate.products[0].option.centerServiceCenterId) {
                                orderCreate.clientMetadata = Object.assign({}, orderCreate.clientMetadata, {
                                    centerServiceCenterId: orderCreate.products[0].option.centerServiceCenterId
                                })
                                checkoutRequest.clientMetadata = Object.assign({}, checkoutRequest.clientMetadata, {
                                    centerServiceCenterId: checkoutRequest.orderProducts[0].option.centerServiceCenterId
                                })
                            }

                            if (orderCreate.products[0].option.workoutId) {
                                orderCreate.clientMetadata = Object.assign({}, orderCreate.clientMetadata, {
                                    workoutId: orderCreate.products[0].option.workoutId
                                })
                                checkoutRequest.clientMetadata = Object.assign({}, checkoutRequest.clientMetadata, {
                                    workoutId: checkoutRequest.orderProducts[0].option.workoutId
                                })
                            }

                            const metadata = await this.cultBusiness.getPlayClientMetadataForOrder(userContext)
                            if (metadata) {
                                orderCreate.clientMetadata = Object.assign({}, orderCreate.clientMetadata, {...metadata})
                                checkoutRequest.clientMetadata = Object.assign({}, checkoutRequest.clientMetadata, {...metadata})
                            }
                        } else if (orderCreate.clientMetadata && isMembershipUpgradeClientMetadata(orderCreate.clientMetadata)) {
                            // For upgrade clientMetadata is already populated product options are added here.
                            const centerId = (orderCreate.clientMetadata as CultMindMembershipUpgradeMetadata).cultCenter.id.toString()
                            orderCreate.products[0].option.centerServiceCenterId = centerId
                            orderCreate.products[0].option.workoutId = String(orderCreate.clientMetadata.workoutId)
                            orderCreate.products[0].option.centerId = centerId
                            orderCreate.products[0].option.isUpgrade = true

                            checkoutRequest.orderProducts[0].option.centerId = centerId
                            checkoutRequest.orderProducts[0].option.workoutId = String(orderCreate.clientMetadata.workoutId)
                            checkoutRequest.orderProducts[0].option.centerServiceCenterId = centerId
                            checkoutRequest.orderProducts[0].option.isUpgrade = true
                        }
                    }

                    if (product.productType === "CONSULTATION" &&  CareUtil.isTransformDoctorType((product as ConsultationProduct).doctorType)) {
                       const selfPatient = await CareUtil.createOrUpdatePatient(userContext, this.userCache, this.healthfaceService)
                        orderProduct.option.patientId = selfPatient.id
                        if (omsOrderProduct?.option) {
                            omsOrderProduct.option.patientId = selfPatient.id
                        }
                    }
                    if (product.productType === "DIAGNOSTICS") {
                        const addressid: string = orderCreate.products[0].option.addressId
                        if (addressid) {
                            orderCreate.address = await this.userBusiness.getAddress(userId, addressid)
                            checkoutRequest.userAddress = _.cloneDeep(orderCreate.address)
                        }
                    }
                    // adding centerServiceCenterId for FITNESS orders, needed for OMS ledgers
                    if (product.productType === "FITNESS") {
                        // cult class order
                        // TODO : remove this if block once app version 9.65 or below is killed
                        if (orderProduct.option.classId && !orderProduct.option.centerId) {
                            const cultClass = await this.cultFitService.getCultClass(orderProduct.option.classId, userId, undefined, undefined, undefined, undefined, session.deviceId)
                            orderProduct.option.centerId = cultClass.centerID
                            orderProduct.option.centerServiceCenterId = cultClass.Center?.centerServiceId
                            if (omsOrderProduct?.option) {
                                omsOrderProduct.option.centerServiceCenterId = cultClass.Center?.centerServiceId
                                omsOrderProduct.option.centerId = cultClass.centerID
                            }
                        }
                        if (!orderCreate.products[0].option.centerServiceCenterId) {
                            let centerId = orderProduct.option.centerId
                            // for upgrade orders
                            if (orderCreate.clientMetadata && isMembershipUpgradeClientMetadata(orderCreate.clientMetadata)) {
                                centerId = (orderCreate.clientMetadata as CultMindMembershipUpgradeMetadata).cultCenter.id.toString()
                            }
                            const cultCenter = await this.catalogueService.getCultCenter(centerId)
                            orderProduct.option.centerServiceCenterId = cultCenter.centerServiceId
                            if (omsOrderProduct?.option) {
                                omsOrderProduct.option.centerServiceCenterId = cultCenter.centerServiceId
                            }

                            orderCreate.clientMetadata = Object.assign({}, orderCreate.clientMetadata, {
                                centerServiceCenterId: cultCenter.centerServiceId
                            })
                            checkoutRequest.clientMetadata = Object.assign({}, checkoutRequest.clientMetadata, {
                                centerServiceCenterId: cultCenter.centerServiceId
                            })
                        }

                    }

                    if (product.productType === "GYMFIT_FITNESS_PRODUCT") {
                        const gymfitFitnessProduct = await this.catalogueServicePMS.getProduct(orderProduct.productId)
                        if (AppUtil.isWeb(userContext) && gymfitFitnessProduct?.product?.productSubType === ProductSubType.ADDONBUNDLE) {
                            const proPlusAddOnIds = gymfitFitnessProduct?.baseAddOns?.map((addOn) => addOn?.packId)
                            if (proPlusAddOnIds?.length > 0) {
                                omsOrderProduct.addonProductIds = proPlusAddOnIds
                            }
                        }
                        const sessionCityId = session.sessionData.cityId
                        const productCityId = gymfitFitnessProduct.clientMetadata.cityId
                        orderCreate.cityId = productCityId
                        checkoutRequest.cityId = productCityId
                        if (sessionCityId !== productCityId && AppUtil.isWeb(userContext)) {
                            this.logger.error("Error: pack not available in city: " + sessionCityId + " packCity: " + productCityId + " userId: " + session.userId )
                            throw this.errorFactory.withCode(ErrorCodes.ERR_MEMBERSHIP_PURCHASE_PACK_NOT_AVAILABLE_IN_CITY, 400).withDebugMessage("Sorry! This pack is not available in your city.").build()
                        }
                        if (CatalogueServiceUtilities.getAccessLevel(gymfitFitnessProduct) === GymfitAccessLevel.CENTER) {

                            const centerServiceCenterId = CatalogueServiceUtilities.getExternalAccessLevelId(gymfitFitnessProduct)
                            const center = await this.centerService.getCenterById(Number(centerServiceCenterId))
                            const centerId = center.meta?.gymfitCenterId
                            if (centerId) {
                                orderCreate.clientMetadata = Object.assign({}, orderCreate.clientMetadata, {
                                    centerId: centerId
                                })
                                checkoutRequest.clientMetadata = Object.assign({}, checkoutRequest.clientMetadata, {
                                    centerId: centerId
                                })
                            }
                            if (centerServiceCenterId) {
                                orderCreate.clientMetadata = Object.assign({}, orderCreate.clientMetadata, {
                                    centerServiceCenterId: centerServiceCenterId
                                })
                                checkoutRequest.clientMetadata = Object.assign({}, checkoutRequest.clientMetadata, {
                                    centerServiceCenterId: centerServiceCenterId
                                })
                            }

                            if (centerId && centerServiceCenterId) {
                                checkoutRequest.orderProducts = checkoutRequest.orderProducts.map(product => {
                                    return {
                                        ...product,
                                        option: {...product.option, centerServiceCenterId: centerServiceCenterId, centerId: centerId?.toString()}
                                    }
                                })
                                orderCreate.products = orderCreate.products.map(product => {
                                    return {
                                        ...product,
                                        option: {...product.option, centerServiceCenterId: centerServiceCenterId, centerId: centerId?.toString()}
                                    }
                                })
                            }
                        }
                        const existingMembershipId = isUpgradeMembership ? Number((clientMetadata as any).membershipId) : undefined
                        if (isUpgradeMembership && existingMembershipId && CatalogueServiceUtilities.getAccessLevel(gymfitFitnessProduct) === GymfitAccessLevel.CITY) { // handle select upgrade to pro pass with credits
                            const originalMembershipData = await this.membershipService.getMembershipById(existingMembershipId)
                            const isSelectMembership = !_.isEmpty(originalMembershipData) && originalMembershipData?.attributes.findIndex(a => a.attrKey == AttributeKeyType.ACCESS_CENTER) != -1
                            if (isSelectMembership) {
                                const upgradePricePayload: UpgradePricingRequest = {
                                    membershipServiceId: existingMembershipId,
                                    targetAccessLevel: GymfitAccessLevel.CITY,
                                    targetAccessLevelId: productCityId,
                                    targetProductType: GymfitProductType.GYMFIT_FITNESS_PRODUCT
                                }
                                const proUpgradeFeeBreakdown = await this.gymfitService.getUpgradePrices(upgradePricePayload)
                                orderCreate.clientMetadata = {
                                    ...clientMetadata,
                                    accessCredits: proUpgradeFeeBreakdown.areCreditsTransferred ? ((proUpgradeFeeBreakdown.creditsLeft ?? 0) + proUpgradeFeeBreakdown.creditsToIncrease) : undefined
                                }
                                checkoutRequest.clientMetadata = {
                                    ...clientMetadata,
                                    accessCredits: proUpgradeFeeBreakdown.areCreditsTransferred ? ((proUpgradeFeeBreakdown.creditsLeft ?? 0) + proUpgradeFeeBreakdown.creditsToIncrease) : undefined
                                }
                            }
                        }
                    }

                    if (["BUNDLE", "CONSULTATION", "DIAGNOSTICS", "FITNESS", "MIND", "FOOD_MARKETPLACE", "PLAY"].includes(product.productType)) {
                        orderCreate.offersVersion = 3
                    }

                    let transferMembershipResponse: MembershipTransferDetailResponse = null, membershipTransferDetails: any = null
                    let selectTransferMembershipResponse: any = null
                    if (orderProduct.option.isTransferringMembership) {
                        const originalMembershipData = await this.membershipService.getMembershipById(Number(orderProduct.option.membershipId))
                        const isSelectMembership = !_.isEmpty(originalMembershipData) && originalMembershipData?.attributes.findIndex(a => a.attrKey == AttributeKeyType.ACCESS_CENTER) != -1
                        if (isSelectMembership) { // ELite Select and Pro Select
                            const fitnessProduct: GymfitFitnessProduct = product as GymfitFitnessProduct
                            const upgradePricePayload: UpgradePricingRequest = {
                                membershipServiceId: Number(orderProduct.option.membershipId),
                                targetAccessLevel: fitnessProduct.accessLevel, // CENTER
                                targetAccessLevelId: fitnessProduct.externalAccessLevelId, // center service id
                                targetProductType: fitnessProduct.productType as GymfitProductType
                            }
                            selectTransferMembershipResponse = await this.gymfitService.getUpgradePrices(upgradePricePayload)
                            const clientMetadata = {
                                productId: orderProduct.productId,
                                membershipId: Number(orderProduct.option.membershipId),
                                isMembershipTransfer: true,
                                isSelectMembershipTransfer: true,
                                transferredCenterName: fitnessProduct.centerName,
                                centerId: orderProduct.option.centerId.toString(),
                                centerServiceCenterId: orderProduct.option.centerServiceCenterId.toString(),
                                daysToIncrease: selectTransferMembershipResponse.daysToIncrease,
                                accessCredits: selectTransferMembershipResponse.areCreditsTransferred ? ((selectTransferMembershipResponse.creditsLeft ?? 0) + selectTransferMembershipResponse.creditsToIncrease) : undefined,
                                newMembershipEndDate: momentTz.tz(originalMembershipData.end, tz).add(selectTransferMembershipResponse.daysToIncrease, "days").format("DD MMM YYYY")
                            }
                            orderCreate.clientMetadata = clientMetadata
                            checkoutRequest.clientMetadata = clientMetadata
                            const currentProduct = await this.catalogueServicePMS.getProduct(originalMembershipData.productId)
                            const centerServiceResponse: CenterResponse = await this.centerService.getCenterById(Number(orderProduct.option.centerServiceCenterId))
                            membershipTransferDetails = {
                                destinationCityId: centerServiceResponse.city,
                                cityId: originalMembershipData.metadata.cityId,
                                selectedCenterName: fitnessProduct.centerName,
                                currentCenterName: currentProduct.clientMetadata.centerName,
                                membershipEndDate: originalMembershipData.end,
                                pauseDays: GymfitUtil.convertMillisToDays(originalMembershipData.remainingPauseDuration)
                            }
                        } else { // this will work only for Elite as there is no transfer for PRO
                            const destinationUserId = orderProduct.option.selectedUserId
                            const destinationCenterId = orderProduct.option.selectedCenterId
                            const destinationCultCity = await this.cityService.getCityById(orderProduct.option.selectedCityId)
                            const sourceMembershipId = orderProduct.option.membershipId
                            const membershipTransferType = orderProduct.option.membershipTransferType
                            const packId = orderProduct.option.packId
                            const sourceUserId = userId
                            membershipTransferDetails = {
                                destinationUserId,
                                destinationCenterId,
                                destinationCityId: orderProduct.option.selectedCityId,
                                sourceMembershipId,
                                sourceUserId,
                                membershipTransferType,
                                packId
                            }
                            transferMembershipResponse = await this.cultFitService.membershipTransferDetails(
                                destinationUserId, destinationCenterId, destinationCultCity.cultCityId, sourceMembershipId,
                                sourceUserId, orderProduct.option?.transferId, "CUREFIT_APP",
                                orderProduct.productId
                            )
                            orderCreate.clientMetadata = transferMembershipResponse
                            checkoutRequest.clientMetadata = transferMembershipResponse
                        }
                        checkoutRequest.appVersion = (userContext.sessionInfo.appVersion ?? 0).toString()
                        orderCreate.appVersion = (userContext.sessionInfo.appVersion ?? 0).toString()
                    }
                    let order
                    if (await this.orderViewBuilder.isOMSCheckoutFlow(userContext?.userProfile?.userId, userContext?.sessionInfo?.deviceId, product, "CHECKOUT", AppUtil.getTenantFromUserContext(userContext))) {
                        const checkoutResponse: OMSOrderCheckoutResponse = await this.omsApiClient.checkoutOrder(checkoutRequest)
                        order = checkoutResponse.order
                    }
                    else {
                        orderCreate.omsCheckoutRequest = checkoutRequest
                        order = await this.omsApiClient.createOrder(orderCreate)
                    }
                    return this.orderViewBuilder.buildView({
                        userContext: userContext,
                        order: order,
                        product: order.productSnapshots[0],
                        apiType: "ORDER_CHECKOUT",
                        transferMembershipResponse: transferMembershipResponse,
                        membershipTransferDetails: membershipTransferDetails,
                        selectTransferMembershipResponse: selectTransferMembershipResponse
                    })
                }
            })
        }

        private async isNeuPassActivationCheckboxShown(userId: string, userContext: UserContext): Promise<boolean> {
            let isTataActivationCheckboxShown: boolean = false
            try {
                const segmentsForTataNeu = TataNeuUtil.getSegmentsForTataNeu()
                const [isPotentialTataUser, isNeuPassConsentPending, isNeupassNotActivated, isTataOrganicTestUser] = await Promise.all([
                    TataNeuUtil.doesUserBelongToTataSegment(this.segmentationClient, segmentsForTataNeu.POTENTIAL_TATA_USER, userId),
                    TataNeuUtil.doesUserBelongToTataSegment(this.segmentationClient, segmentsForTataNeu.NEUPASS_CONSENT_PENDING, userId),
                    TataNeuUtil.doesUserBelongToTataSegment(this.segmentationClient, segmentsForTataNeu.NEUPASS_NOT_ACTIVATED, userId),
                    this.orderViewBuilder.isTataOrganicTestUser(userContext)
                ])
                this.logger.info(`isNeuPassActivationCheckboxShown for userId: ${userId}`, { isPotentialTataUser, isNeuPassConsentPending, isNeupassNotActivated })
                isTataActivationCheckboxShown = isTataOrganicTestUser && (isPotentialTataUser || isNeuPassConsentPending || isNeupassNotActivated)
            } catch (err) {
                this.logger.error(`Failed to do tata segment checks for userId: ${userId}`, { err })
            }
            return isTataActivationCheckboxShown
        }

        @httpPost("/appleIAP/checkout")
        public async checkoutAppleInAppOrder(req: express.Request): Promise<OrderCheckoutAndGatewayOrderResponse> {
            // Checkout params
            const session: Session = req.session
            const orderProduct: OrderProduct = req.body.orderProduct
            const apiKey: string = req.headers["api-key"] as string || req.headers["apikey"] as string
            const userContext: UserContext = req.userContext as UserContext
            userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)

            const advertiserId: string = req.body.advertiserId

            this.logger.info(`/appleIAP/checkout: req.body: ${JSON.stringify(req.body)}`)
            const iapReviewResponse: IAPOrderReviewResponse = await this.reviewIAPOrder(
                orderProduct,
                userContext,
                session,
                apiKey,
                req.body.utm,
                advertiserId
            )
            this.logger.info(`/appleIAP/checkout: iapReviewResponse: `, { iapReviewResponse })

            const appleIAPProductDetails = _.get(iapReviewResponse, "cartReviewResponse.order.productSnapshots[0].appleIAPProductDetails")
            const createGatewayOrderResponse: CfApiCreateGatewayOrderResponse = {
                gatewayId: iapReviewResponse?.cartReviewResponse?.order?.orderId,
                appleInAppPurchaseDetails: {
                    inAppProductId: appleIAPProductDetails?.productId,
                    inAppProductType: appleIAPProductDetails?.productType
                }
            }

            const response: OrderCheckoutAndGatewayOrderResponse = {
                checkoutResponse: iapReviewResponse?.checkoutResponse,
                gatewayOrderResponse: createGatewayOrderResponse
            }
            this.logger.info(`/appleIAP/checkout: response: `, { response })

            this.paymentPromUtil.reportPaymentOptionsFetched(
                "CUREFIT_APP",
                EtherOrderUtil.getVerticalName(_.get(iapReviewResponse, "cartReviewResponse.order.productSnapshots[0]"))
            )
            this.paymentPromUtil.reportPaymentInit(
                "APPLE_IN_APP",
                "APPLE_IN_APP",
                EtherOrderUtil.getVerticalName(_.get(iapReviewResponse, "cartReviewResponse.order.productSnapshots[0]")),
                iapReviewResponse?.cartReviewResponse?.order?.source
            )

            return response
        }

        @httpPost("/playStore/checkout")
        public async checkoutGoogleInAppOrder(req: express.Request): Promise<OrderCheckoutAndGatewayOrderResponse> {
            const session: Session = req.session
            const orderProduct: OrderProduct = req.body.orderProduct
            const apiKey: string = req.headers["api-key"] as string || req.headers["apikey"] as string
            const userContext: UserContext = req.userContext as UserContext
            const advertiserId: string = req.body.advertiserId

            this.logger.info(`/playStore/checkout: req.body: ${JSON.stringify(req.body)}`)
            const iapReviewResponse: IAPOrderReviewResponse = await this.reviewIAPOrder(
                orderProduct,
                userContext,
                session,
                apiKey,
                req.body.utm,
                advertiserId
            )
            this.logger.info(`/playStore/checkout: iapReviewResponse: `, { iapReviewResponse })

            const androidIAPProductDetails = _.get(iapReviewResponse, "cartReviewResponse.order.productSnapshots[0].androidIAPProductDetails")
            const createGatewayOrderResponse: CfApiCreateGatewayOrderResponse = {
                gatewayId: iapReviewResponse?.cartReviewResponse?.order?.orderId,
                androidIAPProductDetails: {
                    inAppProductId: androidIAPProductDetails?.productId,
                    inAppProductType: androidIAPProductDetails?.productType
                }
            }

            const response: OrderCheckoutAndGatewayOrderResponse = {
                checkoutResponse: iapReviewResponse.checkoutResponse,
                gatewayOrderResponse: createGatewayOrderResponse
            }
            this.logger.info(`/playStore/checkout: response: `, { response })

            this.paymentPromUtil.reportPaymentOptionsFetched(
                "CUREFIT_APP",
                EtherOrderUtil.getVerticalName(_.get(iapReviewResponse, "cartReviewResponse.order.productSnapshots[0]"))
            )
            this.paymentPromUtil.reportPaymentInit(
                "PLAY_STORE_BILLING",
                "PLAY_STORE_BILLING",
                EtherOrderUtil.getVerticalName(_.get(iapReviewResponse, "cartReviewResponse.order.productSnapshots[0]")),
                iapReviewResponse?.cartReviewResponse?.order?.source
            )

            return response
        }

        private async reviewIAPOrder(
            orderProduct: OrderProduct,
            userContext: UserContext,
            session: Session,
            apiKey: string,
            utm: any,
            advertiserId: string
        ): Promise<IAPOrderReviewResponse> {
            orderProduct.quantity = 1
            const clientMetadata: CultMembershipMetadata = {
                subUserId: userContext.userProfile.subUserId
            } as CultMembershipMetadata

            const orderCreate: OrderCreate = {
                userId: session.userId,
                deviceId: session.deviceId,
                products: [orderProduct],
                source: AppUtil.callSource(apiKey),
                dontCreateRazorpayOrder: true,
                clientMetadata: clientMetadata,
                useOffersV2: true,
                cityId: session.sessionData.cityId,
                useFitCash: false,
                utm: utm,
                reserve: true,
                tenant: AppUtil.getTenantFromUserContext(userContext)
            }

            const omsCartReviewPayload: OrderCheckoutRequest = _.cloneDeep({
                userId: session.userId,
                deviceId: session.deviceId,
                orderProducts: [orderProduct],
                source: AppUtil.callSource(apiKey),
                cityId: session.sessionData.cityId,
                useFitCash: false,
                tenant: AppUtil.getTenantFromUserContext(userContext),
                clientMetadata,
                reserve: true,
                advertiserId: advertiserId,
                userAddress: undefined,
                utm: utm
            })

            if (!_.isNil(advertiserId)) orderCreate.advertiserId = advertiserId
            if (orderProduct.option && orderProduct.option.offerId) {
                orderProduct.option.offerV2Ids = [orderProduct.option.offerId]
                orderProduct.option.offerId = undefined
            }
            const cartReviewResponse = await this.omsApiClient.reviewOrderCart(omsCartReviewPayload)
            const checkoutResponse: OrderCheckoutResponse = await this.orderViewBuilder.buildView({
                userContext: userContext,
                order: cartReviewResponse.order,
                product: cartReviewResponse.order.productSnapshots[0],
                apiType: "ORDER_CHECKOUT",
                transferMembershipResponse: null,
                membershipTransferDetails: null
            })
            return {
                cartReviewResponse: cartReviewResponse as CartReviewResponse,
                checkoutResponse: checkoutResponse
            }
        }

        @httpGet("/checkout/:orderId")
        async checkoutExistingOrder(req: express.Request): Promise<{ orderMeta: OrderMeta, widgets: WidgetView[] }> {
            const session: Session = req.session
            const orderId: string = req.params.orderId
            const userContext: UserContext = req.userContext as UserContext
            const order = await this.fetchOrderWithValidation(orderId)
            if (order.userId !== session.userId) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_AUTHORIZED_TO_VIEW_ORDER, 401).withDebugMessage("Not authorized to view order").build()
            }
            return this.orderViewBuilder.buildView({
                userContext: userContext,
                order: order,
                product: order.productSnapshots[0],
                apiType: "ORDER_CHECKOUT"
            })
        }

        @httpPost("/:orderId/paymentSuccess")
        async orderPaymentSuccess(req: express.Request): Promise<ConfirmationView | FitClubConfirmationView> {
            const userContext: UserContext = req.userContext as UserContext
            const session: Session = req.session
            userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
            const orderId: string = req.params.orderId
            const order = await this.fetchOrderWithValidation(orderId)
            if (order.userId !== session.userId) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_AUTHORIZED_TO_VIEW_ORDER, 401).withDebugMessage("Not authorized to modify order").build()
            }
            const data: { paymentData: PaymentData } = req.body
            let successOrder: BaseOrder
            if (data.paymentData.channel === "MANUAL" || (data.paymentData.amount === 0 && _.isEmpty(data.paymentData.channel))) {
                const paymentCompletionHandler = (await this.omsApiClient.getPaymentCompletionHandlerForOrder(orderId, "MANUAL")).paymentCompletionHandler
                switch (paymentCompletionHandler) {
                    case "OMS_IMMEDIATE":
                    case "OMS_CUSTOM":
                        const res = await this.omsApiClient.createFulfillment({
                            orderId: order.orderId,
                            initiationSource: "APP"
                        })
                        if (res.result !== "success") {
                            throw this.errorFactory.withCode("ERR_ALFRED_REFUND_INITIATED_FUFILMENT_FAILURE", HTTP_CODE.INTERNAL_SERVER_ERROR).build()
                        }
                        successOrder = await this.omsApiClient.getOrder(order.orderId)
                        break
                    case "ALFRED":
                    default:
                        successOrder = await this.omsApiClient.paymentSuccess(orderId, data, "APP")
                        break
                }
            } else {
                const paymentData: PaymentData = this.findPaymentDataFromOrder(order, data.paymentData)
                const paymentCompletionHandler = paymentData?.paymentCompletionHandler
                switch (paymentCompletionHandler) {
                    case "OMS_IMMEDIATE":
                    case "OMS_CUSTOM":
                        const res = await this.omsApiClient.paymentComplete({
                            orderId: order.orderId,
                            cfPaymentId: paymentData?.cfPaymentId,
                            initiationSource: "APP"
                        })
                        if (res.result !== "success") {
                            const errorCode = (res.currentStage === "PAYMENT_VERIFICATION")
                                ? "ERR_ALFRED_PAYMENT_VALIDATION_PENDING"
                                : "ERR_ALFRED_REFUND_INITIATED_FUFILMENT_FAILURE"
                            throw this.errorFactory.withCode(errorCode, HTTP_CODE.INTERNAL_SERVER_ERROR).build()
                        }
                        successOrder = await this.omsApiClient.getOrder(order.orderId)
                        break
                    case "ALFRED":
                    default:
                        successOrder = await this.omsApiClient.paymentSuccess(orderId, data, "APP")
                        break
                }
            }
            return this.getOrderConfirmationView(req, successOrder)
        }

        private findPaymentDataFromOrder(order: BaseOrder, reqPaymentData: PaymentData): PaymentData {
            let paymentData: PaymentData
            if (!_.isNil(reqPaymentData.cfPaymentId)) {
                paymentData = PaymentUtil.findPaymentByCfPaymentId(order, reqPaymentData.cfPaymentId)
            }
            if (_.isNil(paymentData) && !_.isNil(reqPaymentData.paymentId)) {
                paymentData = PaymentUtil.findPaymentByPaymentId(order, reqPaymentData.paymentId, reqPaymentData.channel)
            }
            if (_.isNil(paymentData) && !_.isNil(reqPaymentData.gatewayId)) {
                paymentData = PaymentUtil.findPaymentByGatewayId(order, reqPaymentData.gatewayId, reqPaymentData.channel)
            }
            if (_.isNil(paymentData) && !_.isEmpty(order.payments)) {
                paymentData = order.payments[order.payments.length - 1]
            }
            return paymentData
        }

        @httpPost("/juspay/codInitiated/:orderId")
        async juspayCodInitiated(req: express.Request): Promise<ConfirmationView | FitClubConfirmationView> {
            const userContext: UserContext = req.userContext as UserContext
            const session: Session = req.session
            userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
            const orderId: string = req.params.orderId
            const order = await this.fetchOrderWithValidation(orderId)
            if (order.userId !== session.userId) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_AUTHORIZED_TO_VIEW_ORDER, 401).withDebugMessage("Not authorized to modify order").build()
            }
            const successOrder = await this.omsApiClient.juspayRPCodInitiated(orderId)
            const res = await this.getOrderConfirmationView(req, successOrder)
            const paymentData: PaymentData = successOrder?.payments[successOrder.payments.length - 1]
            if (_.isEmpty(paymentData)) {
                throw this.errorFactory.withCode(ErrorCodes.PAYMENT_DATA_MISSING, 400).withDebugMessage("Payment Data missing for orderId: " + orderId).build()
            }
            switch (paymentData.paymentCompletionHandler) {
                case "OMS_CUSTOM":
                case "OMS_IMMEDIATE":
                    const status = successOrder?.fulfillmentDetails?.result
                    switch (status) {
                        case "pending":
                        case "under_review":
                            res.status = "pending"
                            break
                        case "failed":
                        case "success":
                            res.status = status
                            break
                        default:
                            res.status = "failed"
                            break
                    }
                    break
                case "ALFRED":
                default:
                    /*
                     * This case should only come up in case of ALFRED handler
                     */
                    if (successOrder.wasFulfilmentGenerated) {
                        res.status = "success"
                    } else {
                        res.status = "failed"
                    }
            }
            return res
        }

        @httpPost("/:orderId/paymentComplete")
        async orderPaymentComplete(req: express.Request): Promise<ConfirmationView | FitClubConfirmationView> {
            const userContext: UserContext = req.userContext as UserContext
            const session: Session = req.session
            userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)

            const orderId: string = req.params.orderId
            const cfPaymentId: string = req.body.cfPaymentId

            const order = await this.fetchOrderWithValidation(orderId)
            if (order.userId !== session.userId) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_AUTHORIZED_TO_VIEW_ORDER, 401).withDebugMessage("Not authorized to modify order").build()
            }

            const res = await this.omsApiClient.paymentComplete({orderId: order.orderId, cfPaymentId: cfPaymentId, initiationSource: "APP"})
            const clientVersion: number = Number(req.headers["clientversion"])
            if (res.result !== "success") {
                if (AppUtil.isWeb(userContext) || clientVersion >= 9.91) {
                    if (res.result === "failed") {
                        const confirmationView = new OrderStatusView("")
                        confirmationView.status = "failed"
                        if (res.currentStage === "PAYMENT_VERIFICATION") {
                            if (PaymentUtil.findPaymentByCfPaymentId(order, cfPaymentId).status === "failed") {
                                confirmationView.failureMessage = {
                                    title: "Payment failed",
                                    subTitle: `Your payment of Rs.${order.totalAmountPayable} failed. Try placing the order again.`
                                }
                            } else {
                                confirmationView.failureMessage = {
                                    title: "Payment not confirmed",
                                    subTitle: `Your payment of Rs.${order.totalAmountPayable} could not be confirmed. If any money is deducted, it will be refunded to your account.`
                                }
                            }
                        } else {
                            confirmationView.failureMessage = {
                                title: "Something went wrong!",
                                subTitle: "We could not place your order because of a technical issue. We've initiated a full refund against your order. The amount should reflect in your account within 2-3 working days. Try placing the order again."
                            }
                        }
                        return confirmationView
                    } else if (res.result === "pending" || res.result === "under_review") {
                        const confirmationView = new OrderStatusView(`curefit://paymentpending?orderId=${orderId}`)
                        confirmationView.status = "pending"
                        confirmationView.meta = {
                            ...confirmationView.meta,
                            text: "Please wait while we confirm your payment",
                            subtext: "Please don’t press back or close this page if you have completed payment successfully",
                            modalText: "This is taking a tad bit longer",
                            modalSubtext: "Please bear with us, we understand how frustrating this can be and we're here to help.\n\nWe'll notify you via email and SMS once it’s done.",
                            modalCTATitle: "GOT IT",
                            cancelModalCTA: "RETRY PAYMENT",
                            cancelModalText: "Are you sure you have not completed payment?\n\nRetry payment if you have not completed the payment."
                        }
                        return confirmationView
                    }
                } else {
                    throw this.errorFactory.withCode(res.resultMessage, HTTP_CODE.INTERNAL_SERVER_ERROR).withMessage("Problem in fulfilling order")
                }
            }
            const successOrder = await this.omsApiClient.getOrder(order.orderId)
            console.log("success order on play : " + JSON.stringify(successOrder))
            const resultView = await this.getOrderConfirmationView(req, successOrder)
            resultView.status = "success"
            return resultView
        }

        @httpGet("/:orderId/fulfillmentStatus")
        async orderFulfillmentStatus(req: express.Request): Promise<FulfillmentResult> {
            const orderId: string = req.params.orderId
            const res = await this.omsApiClient.getFulfillmentStatus(orderId)
            this.logger.info("OrderController::returned fulfilment status for: " + orderId + " as " + res.result + " and stage: " + res.currentStage)
            return res
        }

        @httpPost("/:orderId/codOrderSuccess")
        async codOrderSuccess(req: express.Request): Promise<ConfirmationView | FitClubConfirmationView> {
            const orderId: string = req.params.orderId
            const data: { paymentData: PaymentData } = req.body
            const session: Session = req.session
            const order = await this.fetchOrderWithValidation(orderId)
            if (order.userId !== session.userId) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_AUTHORIZED_TO_VIEW_ORDER, 401).withDebugMessage("Not authorized to modify order").build()
            }
            return this.getOrderConfirmationView(req, order)
        }

        private async getOrderConfirmationView(req: express.Request, order: Order): Promise<ConfirmationView | FitClubConfirmationView> {
            const appVersion: number = Number(req.headers["appversion"])
            const osName: string = req.headers["osname"] as string
            const codepushversion: number = Number(req.headers["codepushversion"])
            const apiKey: string = req.headers["api-key"] as string || req.headers["apikey"] as string
            const userContext = req.userContext as UserContext
            const orderSource = userContext.sessionInfo.orderSource

            // Added to update flutter app source
            if (req.body?.fromFlutter === "true" || req.query?.fromFlutter === "true") {
                userContext.sessionInfo.appSource = AppUtil.getFlutterAppSource()
            }
            const params: ConfirmationRequestParams = {
                orderSource,
                userContext: userContext
            }
            if (OrderUtil.isNewOrderConfirmationScreenSupported(osName, appVersion, codepushversion, orderSource)) {
                return this.orderConfirmationViewBuilderV1.buildOrderConfirmationView(userContext, order, params)
            }
            else {
                return this.orderConfirmationViewBuilder.buildOrderConfirmationView(userContext, order, params)
            }
        }

        @httpPost("/:orderId/paytmWithdraw", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        async paytmWithdraw(req: express.Request, res: express.Response): Promise<ConfirmationView> {
            const session: Session = req.session
            const userId = session.userId
            const orderId: string = req.params.orderId
            const appVersion: number = Number(req.headers["appversion"])
            const codepushversion: number = Number(req.headers["codepushversion"])
            const osName: string = req.headers["osname"] as string
            const apiKey: string = req.headers["api-key"] as string || req.headers["apikey"] as string
            const userContext = req.userContext as UserContext
            const order = await this.fetchOrderWithValidation(orderId)
            const orderSource = AppUtil.callSource(apiKey)

            if (order.userId !== session.userId) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_AUTHORIZED_TO_VIEW_ORDER, 401).withDebugMessage("Not authorized to modify order").build()
            }
            const params: ConfirmationRequestParams = {
                orderSource,
                userContext: userContext
            }
            let orderPromise
            if (EtherOrderUtil.isSubOrderId(orderId)) {
                const parentOrderId = EtherOrderUtil.getParentOrderId(orderId)
                orderPromise = this.omsApiClient.paytmWithdraw(userId, parentOrderId, orderId)
            } else
                orderPromise = this.omsApiClient.paytmWithdraw(userId, orderId)
            const successOrder = await orderPromise
            if (OrderUtil.isNewOrderConfirmationScreenSupported(osName, appVersion, codepushversion, orderSource)) {
                return this.orderConfirmationViewBuilderV1.buildOrderConfirmationView(req.userContext, successOrder, params)
            }
            return this.orderConfirmationViewBuilder.buildOrderConfirmationView(req.userContext, successOrder, params)
        }

        @httpPost("/:orderId/paymentFailure")
        async orderPaymentFailure(req: express.Request): Promise<Order> {
            const orderId: string = req.params.orderId
            const paymentData: { paymentData: any } = {
                paymentData: req.body
            }
            const session: Session = req.session

            const order = await this.fetchOrderWithValidation(orderId)
            if (order.userId !== session.userId) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_AUTHORIZED_TO_VIEW_ORDER, 401).withDebugMessage("Not authorized to modify order").build()
            }
            const res = await this.omsApiClient.paymentCancel({orderId, initiationSource: "ALFRED_FLOW", cfPaymentId: crypto.randomUUID(), meta: paymentData})
            return this.omsApiClient.getOrder(orderId)
        }

        private async fetchOrders(session: Session, categoryFilter: OrderCategoryFilter["filter"], dateRangeFilter: OrderDateRangeFilter["filter"], pageToken: string,
                                  limit: number, direction: SortOrder, productType: ProductType, orderSourceForFiltering: OrderSource[],
                                  osName: string, appVersion: number): Promise<BaseOrder[]> {

            const defaultOrderSourcesToFilterOut: OrderSource[] = ["CULTGEAR_APP", "CULTGEAR_PHONEPE_APP", "SUGARFIT_APP", "SUGARFIT_CMT"]
            const orderSourceFilter = !_.isEmpty(_.compact(orderSourceForFiltering)) ? {orderSources: orderSourceForFiltering, include: true} : {orderSources: defaultOrderSourcesToFilterOut, include: false}
            if (categoryFilter === undefined || dateRangeFilter === undefined || !AppUtil.isNewOrderFilteringSupported(osName, appVersion)) {
                return this.omsApiClient.getViableOrdersForUser(session.userId, {
                    pageToken: pageToken,
                    count: limit,
                    sortOrder: direction,
                    productType,
                    orderSourceFilter,
                    paginated: true,
                    includePaymentFailures: true
                })
            } else if (categoryFilter !== "ALL" || dateRangeFilter !== "all") {
                /**
                 * User has filtered orders on UI
                 * Use new API for filtering
                 */
                let timestamp: string | undefined = undefined // current date-time
                if (dateRangeFilter !== "all") {
                    // convert date range value to timestamp
                    switch (dateRangeFilter) {
                        case "yesterday":
                            timestamp = moment().subtract(1, "day").toISOString()
                            break
                        case "seven-days":
                            timestamp = moment().subtract(7, "days").toString()
                            break
                        case "past-month":
                            timestamp = moment().subtract(1, "month").toISOString()
                            break
                        case "past-six-months":
                            timestamp = moment().subtract(6, "months").toISOString()
                            break
                        default:
                            timestamp = moment().subtract(1, "year").toString()
                    }
                    return this.omsApiClient.getViableOrdersForUser(session.userId, {
                        pageToken: pageToken,
                        count: limit,
                        sortOrder: direction,
                        orderSourceFilter,
                        vertical: categoryFilter,
                        dateLimit: timestamp,
                        paginated: true,
                        includePaymentFailures: true
                    })
                } else {
                    return this.omsApiClient.getViableOrdersForUser(session.userId, {
                        pageToken: pageToken,
                        count: limit,
                        sortOrder: direction,
                        orderSourceFilter,
                        vertical: categoryFilter,
                        paginated: true,
                        includePaymentFailures: true
                    })
                }

            } else {
                return this.omsApiClient.getViableOrdersForUser(session.userId, {
                    pageToken: pageToken,
                    count: limit,
                    sortOrder: direction,
                    orderSourceFilter,
                    productType,
                    paginated: true,
                    includePaymentFailures: true
                })
            }
        }

        @httpPost("/:orderId/paymentCancel")
        async orderPaymentCancel(req: express.Request): Promise<boolean> {
            const orderId: string = req.params.orderId
            const cfPaymentId: string = req.body.cfPaymentId
            const meta: any = req.body.meta
            const session: Session = req.session
            const order = await this.fetchOrderWithValidation(orderId)
            if (order.userId !== session.userId) {
                throw this.errorFactory.withCode(ErrorCodes.NOT_AUTHORIZED_TO_VIEW_ORDER, 401).withDebugMessage("Not authorized to modify order").build()
            }
            const sdkPaymentStatus = _.get(meta, "sdkPaymentStatus")
            const paymentFailedStatus = ["authentication_failed", "authorization_failed", "api_failed"]
            const userContext: UserContext = req.userContext as UserContext
            const appVersion: number = _.get(userContext, "sessionInfo.appVersion", 0)

            let res
            try {
                res = await this.omsApiClient.paymentCancel({
                    orderId: orderId,
                    initiationSource: "APP",
                    cfPaymentId: cfPaymentId,
                    meta
                })
            }
            catch (err) {
                this.logger.error("DEBUG:: Error while hitting paymentCancel", {err})
                throw err
            }
            finally {
                if (appVersion < 10.06 && paymentFailedStatus.includes(sdkPaymentStatus)) {
                    this.logger.info("DEBUG:: Error includes payment state")
                    throw this.errorFactory.withCode(ErrorCodes.PAYMENT_FAILED_JUSPAY, 400).withDebugMessage("User payment failed").build()
                }
            }
            let paymentErrorObj = null
            if (res?.paymentError) {
                paymentErrorObj = {
                    title: res.paymentError?.errorTitle || null,
                    description: res.paymentError?.errorMessage || null,
                    icon: res.paymentError?.errorIconUrl || null,
                    lottieUrl: res.paymentError?.lottieUrl || null,
                    note: res.paymentError?.errorNote ? {
                        title: "Please Note",
                        description: res.paymentError?.errorNote || null,
                    } : null,
                    primaryActionTitle: "TRY AGAIN",
                }
                return {...res, paymentErrorObj}
            }
            return res
        }

        private async getUserAddress(session: Session, userContext: UserContext, mealSlot: string, isPack: boolean): Promise<UserDeliveryAddress> {
            const promises: Promise<any>[] = []
            const userPromise = this.userService.getUser(session.userId)
            let gatePromise
            let preferredLocationPromise
            promises.push(userPromise)
            const addressId = session.sessionData.locationPreferenceData ? session.sessionData.locationPreferenceData.addressId : undefined
            const gateId = session.sessionData.gateId
            if (addressId) {
                preferredLocationPromise = this.userBusiness.getPreferredLocation(userContext, session.userId, session.sessionData, undefined, undefined, mealSlot, isPack)
                promises.push(preferredLocationPromise)
            } else if (gateId) {
                gatePromise = this.gateService.getGate(gateId)
                promises.push(gatePromise)
            }

            await Promise.all(promises)
            if (addressId) {
                const user = await userPromise
                const preferredLocation = await preferredLocationPromise
                const result = AppUtil.getUserAlertInfo(user, userContext)
                const code: string = result.code
                if (!_.isEmpty(code)) {
                    throw this.errorFactory.withCode(result.code, result.alertInfo.statusCode).build()
                }
                return Object.assign(preferredLocation.address, { areaId: preferredLocation.area.areaId, name: user.firstName + " " + user.lastName, phoneNumber: user.phone })
            } else if (gateId) {
                const user = await userPromise
                const gate = await gatePromise
                if (!gate || gate.status !== "LIVE") {
                    throw new Error("Checkout not allowed from inactive gates")
                } else {
                    const address: UserDeliveryAddress = {
                        addressId: gate.gateId,
                        areaId: gate.areaId,
                        userId: user.id,
                        addressType: "OFFICE",
                        addressLine1: gate.name,
                        latLong: gate.latLong,
                        addressLine2: gate.addressString + ", " + gate.locality + ", " + gate.city,
                        gateId: gateId,
                        name: user.firstName + " " + user.lastName,
                        phoneNumber: user.phone
                    }
                    return address
                }
            } else {
                throw this.errorFactory.withCode(ErrorCodes.ADDRESS_DOES_NOT_EXIST_ERR, 400).withDebugMessage("No address id or gate id associated with user").build()
            }
        }

        private async populateHamletExperimentMap(userContext: UserContext) {
            let map = {}
            try {
                map = (await this.hamletBusiness.getUserAllocations(AppUtil.getHamletContext(userContext, []))).assignmentsMap
            } catch (e) {
                map = {}
                this.logger.error(`Hamlet user allocation failing for user: ${userContext.userProfile.userId}`)
                this.rollbarService.sendError(e, {user: {id: userContext.userProfile.userId}})
            }

            return map
        }

        private async fetchOrderWithValidation(orderId: string) {
            const order = await this.omsApiClient.getOrder(orderId)
            if (_.isNil(order)) {
                throw this.errorFactory.withCode(ErrorCodes.ORDER_DOES_NOT_EXIST_ERR, 400).withDebugMessage("Order does not exist for orderId: " + orderId).build()
            }
            return order
        }
    }

    return OrderController
}

export default controllerFactory
