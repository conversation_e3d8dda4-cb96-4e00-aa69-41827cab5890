import * as _ from "lodash"
import { injectable } from "inversify"

import {
    GearShipmentStatus,
    DeliveryPartnerMetadata,
    TrackShipmentResponseV2,
    ShipmentState,
    ShipmentType,
} from "@curefit/gear-common"
export {
    GearShipmentStatus,
} from "@curefit/gear-common"
import { Action, ProductDetailPage, WidgetView } from "../common/views/WidgetView"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { GearTrackingHeaderWidget, VerticalStepIndicatorWidget } from "../gear/shipments/GearShipmentViewBuilder"
import GearShipmentView from "./GearShipmentView"

export enum IssueState {
    ORDER_PLACED = "ORDER_PLACED",
    ORDER_DELIVERED = "ORDER_DELIVERED",
    ORDER_EXCHANGED = "ORDER_EXCHANGED",
    ORDER_RETURNED = "ORDER_RETURNED"
}

export const CUSTOMER_CARE_EMAIL_ID = "<EMAIL>"

const GEAR_FORWARD_SHIPMENT_STATES: ShipmentState[] = [
    ShipmentState.READY,
    ShipmentState.PACKED,
    ShipmentState.SHIPPED,
    ShipmentState.OUT_FOR_DELIVERY,
    ShipmentState.DELIVERED
]
const GEAR_HAND_IN_HAND_SHIPMENT_STATES: ShipmentState[] = [
    ShipmentState.READY,
    ShipmentState.PACKED,
    ShipmentState.SHIPPED,
    ShipmentState.OUT_FOR_DELIVERY,
    ShipmentState.EXCHANGE_PICKED_UP
]
const GEAR_RETURN_SHIPMENT_STATES: ShipmentState[] = [
    ShipmentState.RETURN_INITIATED_RETURN_SHIPMENT,
    ShipmentState.RETURN_APPROVED,
    ShipmentState.OUT_FOR_PICKUP,
    ShipmentState.PICKED_UP,
    ShipmentState.RECEIVED
]

interface BaseGearShipmentView extends ProductDetailPage {

}

interface TrackShipmentHeader {
    numberTitle: string
    number: string
    summary: string
    status: string
    shipmentHealth: string
    shipmentId: string
}

interface SectionDetails {
    title: string
    subtitle: string
}

export interface GearTrackShipmentView extends BaseGearShipmentView {

}

@injectable()
export default class GearShipmentViewBuilderV2 extends GearShipmentView {
    public async buildTrackingView(
        gearShipmentResponse: TrackShipmentResponseV2,
        shipmentId: string
    ): Promise<GearTrackShipmentView> {
        const widgetPromises: Promise<WidgetView>[] = []
        const timezone = "Asia/Kolkata"

        widgetPromises.push(this.buildHeaderInfo(gearShipmentResponse, shipmentId, timezone))
        widgetPromises.push(this.buildVerticalStepIndicatorWidgetView(gearShipmentResponse, timezone))

        const widgets = await Promise.all(widgetPromises)

        const actions: Action[] = [
            {
                actionType: "EXTERNAL_DEEP_LINK",
                title: "Need Help",
                url: `mailto:${CUSTOMER_CARE_EMAIL_ID}?subject=[Gear] Need help with [Order ${
                    gearShipmentResponse.external_service_order_id
                    }] [Shipment ${shipmentId}]`
            }
        ]

        return { widgets, actions }
    }

    private async buildHeaderInfo(trackShipmentResponse: TrackShipmentResponseV2, shipmentId: string, timezone: Timezone): Promise<WidgetView> {
        const edd: Date = trackShipmentResponse.customer_edd
            ? TimeUtil.parseDateTime(trackShipmentResponse.customer_edd, timezone)
            : undefined
        const currentState: ShipmentState = this.getCurrentState(trackShipmentResponse)
        const isForwardShipment: boolean = trackShipmentResponse.shipment_type === "forward"
        const summary: string = isForwardShipment
            ? this.getForwardShipmentSummary(currentState, edd, timezone)
            : this.getReturnShipmentSummary(currentState)
        const number: string = trackShipmentResponse.external_service_order_id || "Not Available"

        const trackingHeaderInfo: TrackShipmentHeader = {
            numberTitle: "Order ID",
            status: isForwardShipment
                ? this.getForwardShipmentStatus(currentState)
                : this.getReturnShipmentStatus(currentState),
            shipmentHealth: isForwardShipment
                ? this.getForwardShipmentHealth(currentState)
                : this.getReturnShipmentHealth(currentState),
            number,
            summary,
            shipmentId
        }

        return new GearTrackingHeaderWidget(trackingHeaderInfo)
    }

    private async buildVerticalStepIndicatorWidgetView(
        trackShipmentResponse: TrackShipmentResponseV2, timezone: Timezone
    ): Promise<VerticalStepIndicatorWidget> {
        const shipmentStatusData: GearShipmentStatus[] = this.getShipmentStatusTimeline(trackShipmentResponse, timezone)
        const shipmentMiniStates = this.getShipmentMiniStates(trackShipmentResponse, timezone)
        return new VerticalStepIndicatorWidget(shipmentStatusData, shipmentMiniStates)
    }

    public getShipmentStatusTimeline(shipment: TrackShipmentResponseV2, timezone: Timezone): GearShipmentStatus[] {
        const currentShipmentState = this.getCurrentState(shipment)

        // fetch relevant display states for shipment type and status
        const orderOfStatesToBeDisplayed = this.getOrderOfStatesFor(shipment)

        // create timeline data
        const shipmentStatusTimeline: GearShipmentStatus[] = []
        const placedAt = TimeUtil.parseDateTime(shipment.created_at, timezone)
        let stateChanges = shipment.state_change_history
        const edd = shipment.customer_edd ? TimeUtil.parseDateTime(shipment.customer_edd, timezone) : undefined
        let marked: boolean = false

        _.forEach(orderOfStatesToBeDisplayed, (state: ShipmentState, index: number) => {
            // Extract (and mutate) state change info from history
            // Some states occur more than once e.g. `failed_pickup`, `out_for_pickup` etc.
            const stateInfo = _.find(stateChanges, { state: state })
            if (stateInfo) {
                stateChanges = _.without(stateChanges, stateInfo)
            }
            const time = stateInfo && stateInfo.time ? TimeUtil.parseDateTime(stateInfo.time, timezone) : undefined

            // State is achieved if its statechange exists. If not, then check if it occurs before the current step in the chronology
            // Ideally, the OR condition should not be required if data sanity is maintained
            const isStateAchieved =
                Boolean(stateInfo) ||
                orderOfStatesToBeDisplayed.indexOf(currentShipmentState) >= orderOfStatesToBeDisplayed.indexOf(state)
            const metadata = stateInfo ? stateInfo.metadata : undefined

            const sectionDetails: SectionDetails =
                shipment.shipment_type === ShipmentType.FORWARD
                    ? this.getForwardSectionDetails(state, placedAt, metadata, timezone,  isStateAchieved, time, edd)
                    : this.getReturnSectionDetails(state, placedAt, timezone,  isStateAchieved, time)

            let completePercent = 0
            if (isStateAchieved) {
                completePercent = 1
            } else {
                completePercent = 0
                // Mark the last achieved state as 50% completed ¯\_(ツ)_/¯
                if (index && !marked) {
                    shipmentStatusTimeline[index - 1].completePercent = 0.5
                    marked = true
                }
            }
            shipmentStatusTimeline.push({
                title: sectionDetails.title,
                subtitle: sectionDetails.subtitle,
                completePercent,
                date: time || (isStateAchieved ? placedAt : edd)
            })
        })
        return shipmentStatusTimeline
    }

    private getForwardSectionDetails(
        state: ShipmentState,
        placedAt: Date,
        metadata: DeliveryPartnerMetadata,
        timezone: Timezone,
        completed: boolean,
        time: Date = undefined,
        edd: Date = undefined
    ): SectionDetails {
        const sectionDetails: SectionDetails = {
            title: "",
            subtitle: ""
        }

        switch (state) {
            case ShipmentState.READY:
                sectionDetails.title = "Order Confirmed"
                sectionDetails.subtitle = `Placed on ${TimeUtil.formatDateInTimeZone(timezone, placedAt, "Do MMMM, dddd [at] h:mm A")}`
                break

            case ShipmentState.PACKED:
                sectionDetails.title = "Order Packed"

                if (time) {
                    sectionDetails.subtitle = `Packed on ${TimeUtil.formatDateInTimeZone(timezone, time, "Do MMMM, dddd [at] h:mm A")}`
                }

                break

            case ShipmentState.SHIPPED:
                sectionDetails.title = "Order Shipped"

                if (time) {
                    sectionDetails.subtitle = `Shipped on ${TimeUtil.formatDateInTimeZone(timezone, time, "Do MMMM, dddd [at] h:mm A")}`
                }

                break

            case ShipmentState.OUT_FOR_DELIVERY:
                sectionDetails.title = "Out for Delivery"

                if (time) {
                    sectionDetails.subtitle = `Out for Delivery on ${TimeUtil.formatDateInTimeZone(
                        timezone, time,
                        "Do MMMM, dddd [at] h:mm A"
                    )}`
                }

                break

            case ShipmentState.FAILED_DELIVERY:
                sectionDetails.title = "Failed to Deliver"
                sectionDetails.subtitle =
                    metadata && metadata.reason_code
                        ? this.getNdrReason(metadata.reason_code)
                        : "Could not deliver due to unavoidable reason."
                break

            case ShipmentState.EXCHANGE_PICKED_UP:
                sectionDetails.title = "Replaced"

                if (time) {
                    sectionDetails.subtitle = `${TimeUtil.formatDateInTimeZone(timezone, time, "Do MMMM, dddd [at] h:mm A")}`
                }
                break

            case ShipmentState.DELIVERED:
                sectionDetails.title = "Delivered"

                if (completed) {
                    sectionDetails.title = "Delivered"
                    sectionDetails.subtitle = "Delivered"

                    if (time) {
                        sectionDetails.subtitle += ` on ${TimeUtil.formatDateInTimeZone(timezone, time, "Do MMMM, dddd [at] h:mm A")}`
                    }

                    if (metadata && metadata.remark) {
                        sectionDetails.subtitle += ` (${metadata.remark})`
                    }
                } else if (edd) {
                    sectionDetails.subtitle = `Expected to be delivered by ${TimeUtil.formatDateInTimeZone(timezone, edd, "Do MMMM h:mm a")}`
                }

                break

            case ShipmentState.RTO_INITIATED:
                sectionDetails.title = "Returning to Origin"
                sectionDetails.subtitle = "Not able to deliver"
                break

            case ShipmentState.EXCHANGE_INITIATED:
                sectionDetails.title = "Exchange Initiated"
                if (time) {
                    sectionDetails.subtitle = `${TimeUtil.formatDateInTimeZone(timezone, time, "Do MMMM, dddd [at] h:mm A")}`
                }
                break

            case ShipmentState.RECEIVED:
                sectionDetails.title = "Received at Origin"
                break

            case ShipmentState.REFUND_INITIATED:
                sectionDetails.title = "Refund Initiated"

                if (completed) {
                    sectionDetails.subtitle = "Refund initiated"

                    if (time) {
                        sectionDetails.subtitle += ` on ${TimeUtil.formatDateInTimeZone(timezone, time, "Do MMMM, dddd [at] h:mm A")}`
                    }

                    sectionDetails.subtitle +=
                        ". It should reflect in your original mode of payment in 5-7 business days depending on the mode of payment"
                }

                break
        }

        return sectionDetails
    }

    private getReturnSectionDetails(
        state: ShipmentState,
        placedAt: Date,
        timezone: Timezone,
        completed: boolean,
        time: Date = undefined
    ): SectionDetails {
        const sectionDetails: SectionDetails = {
            title: "",
            subtitle: ""
        }

        switch (state) {
            case ShipmentState.RETURN_INITIATED_RETURN_SHIPMENT:
                sectionDetails.title = "Return Initiated"
                sectionDetails.subtitle = `Initiated on ${TimeUtil.formatDateInTimeZone(timezone, placedAt, "Do MMMM, dddd [at] h:mm A")}`
                break

            case ShipmentState.RETURN_APPROVED:
                sectionDetails.title = "Return Approved"

                if (time) {
                    sectionDetails.subtitle = `Approved on ${TimeUtil.formatDateInTimeZone(timezone, time, "Do MMMM, dddd [at] h:mm A")}`
                }

                break

            case ShipmentState.OUT_FOR_PICKUP:
                sectionDetails.title = "Out for Pickup"

                if (time) {
                    sectionDetails.subtitle = `${TimeUtil.formatDateInTimeZone(timezone, time, "Do MMMM, dddd [at] h:mm A")}`
                }
                break

            case ShipmentState.FAILED_PICKUP:
                sectionDetails.title = "Pickup Failed"
                sectionDetails.subtitle = "Could not collect package from you"
                break

            case ShipmentState.PICKUP_CANCELED:
                sectionDetails.title = "Pickup Cancelled"
                sectionDetails.subtitle = "We will not attempt for pickup anymore"
                break

            case ShipmentState.PICKED_UP:
                sectionDetails.title = "Picked Up"

                if (completed) {
                    sectionDetails.subtitle = "Picked up"

                    if (time) {
                        sectionDetails.subtitle += ` on ${TimeUtil.formatDateInTimeZone(timezone, time, "Do MMMM, dddd [at] h:mm A")}`
                    }
                }
                break

            case ShipmentState.RECEIVED:
                sectionDetails.title = "Received at Origin"
                break

            case ShipmentState.REFUND_INITIATED:
                sectionDetails.title = "Refund Initiated"

                if (completed) {
                    sectionDetails.subtitle = "Refund initiated"

                    if (time) {
                        sectionDetails.subtitle += ` on ${TimeUtil.formatDateInTimeZone(timezone, time, "Do MMMM, dddd [at] h:mm A")}`
                    }

                    sectionDetails.subtitle +=
                        ". It should reflect in your original mode of payment in 5-7 business days depending on the mode of payment"
                }
                break
        }

        return sectionDetails
    }

    private getNdrReason(reasonCode: number) {
        switch (reasonCode) {
            case 0:
                return "Could not deliver due to unavoidable reason."

            case 1:
                return "Could not deliver as you were not available."

            case 2:
                return "Could not deliver. Our team will contact with you to understand your concern."

            case 3:
                return "Could not deliver. Will reattempt basis your request."

            case 4:
                return "Could not deliver due to unavoidable reason."

            case 5:
                return "Could not connect with you."

            case 6:
                return "Could not locate your address. We will contact you."

            case 7:
                return "Could not deliver. Our team will connect with you."

            case 8:
                return "Could not deliver to your address. Our team will connect with you."

            case 9:
                return "Order Already Cancelled."

            case 10:
                return "Self Collect."

            case 11:
                return "Shipment Seized By Customer."
        }
    }

    private getOrderOfStatesFor(shipment: TrackShipmentResponseV2): ShipmentState[] {
        const shipmentStates: any[] = _.map(shipment.state_change_history, "state")
        const currentState = this.getCurrentState(shipment)

        switch (shipment.shipment_type) {
            case ShipmentType.RETURN: {
                // RETURN shipment w/o failed pickup
                if (!_.includes(shipmentStates, ShipmentState.FAILED_PICKUP)) {
                    return GEAR_RETURN_SHIPMENT_STATES
                }

                // RETURN shipment with failed pickup
                if (_.includes([ShipmentState.OUT_FOR_PICKUP, ShipmentState.PICKED_UP], currentState)) {
                    const statesToAppend = [ShipmentState.PICKED_UP, ShipmentState.RECEIVED]
                    return _.concat(shipmentStates, _.drop(statesToAppend, statesToAppend.indexOf(currentState) + 1))
                }

                return shipmentStates
            }

            case ShipmentType.FORWARD: {
                // FORWARD shipment w/o RTO or failed delivery
                if (
                    !_.includes(shipmentStates, ShipmentState.RTO_INITIATED) &&
                    !_.includes(shipmentStates, ShipmentState.FAILED_DELIVERY)
                ) {
                    if (currentState === ShipmentState.EXCHANGE_INITIATED) {
                        return _.concat(GEAR_FORWARD_SHIPMENT_STATES, ShipmentState.EXCHANGE_INITIATED)
                    }
                    if (shipment.is_hand_in_hand_exchange) {
                        return GEAR_HAND_IN_HAND_SHIPMENT_STATES
                    }
                    return GEAR_FORWARD_SHIPMENT_STATES
                }

                // FORWARD shipment with failed delivery
                if (
                    _.includes(shipmentStates, ShipmentState.FAILED_DELIVERY) &&
                    currentState === ShipmentState.OUT_FOR_DELIVERY
                ) {
                    return _.concat(shipmentStates, [
                        shipment.is_hand_in_hand_exchange ? ShipmentState.EXCHANGE_PICKED_UP : ShipmentState.DELIVERED
                    ])
                }

                // FORWARD shipment with RTO states
                const hiddenStates = [
                    ShipmentState.QC_DONE,
                    ShipmentState.RTO_OUT_FOR_DELIVERY,
                    ShipmentState.RTO_INTRANSIT,
                    ShipmentState.RTO_DELIVERED
                ]
                if (_.includes(shipmentStates, ShipmentState.RTO_INITIATED) || currentState === ShipmentState.RTO_INITIATED) {
                    shipmentStates.push(ShipmentState.RECEIVED)
                }
                return _.difference(shipmentStates, hiddenStates)
            }
            default:
                return shipmentStates
        }
    }

    /*
          TODO: None of these functions should be present here.
          They should all be in Gearvault which will determine the text and summary that needs to be displayed to the user.
      */
    public getCurrentState(response: TrackShipmentResponseV2): ShipmentState {
        switch (response.state) {
            case ShipmentState.PENDING:
                return ShipmentState.READY
            case ShipmentState.QC_DONE:
                return ShipmentState.RECEIVED
            case ShipmentState.RTO_OUT_FOR_DELIVERY:
            case ShipmentState.RTO_INTRANSIT:
            case ShipmentState.RTO_DELIVERED:
                return ShipmentState.RTO_INITIATED
            default:
                return response.state
        }
    }

    public getForwardShipmentHealth(state: ShipmentState): string {
        switch (state) {
            case ShipmentState.READY:
            case ShipmentState.PACKED:
            case ShipmentState.SHIPPED:
            case ShipmentState.OUT_FOR_DELIVERY:
            case ShipmentState.FAILED_DELIVERY:
            case ShipmentState.RTO_INITIATED:
            case ShipmentState.RETURN_INITIATED:
                return "in_progress"

            case ShipmentState.DELIVERED:
            case ShipmentState.REFUND_INITIATED:
            case ShipmentState.EXCHANGE_PICKED_UP:
                return "complete"

            default:
                return ""
        }
    }

    // move this logic to gear vault api when /orders/ apis are updated
    public getIssueState(response: TrackShipmentResponseV2): string {
        if (response.is_hand_in_hand_exchange || response.state === ShipmentState.EXCHANGE_INITIATED) {
            return IssueState.ORDER_EXCHANGED
        }
        if (response.shipment_type === ShipmentType.RETURN || response.state === ShipmentState.RETURN_INITIATED) {
            return IssueState.ORDER_RETURNED
        }
        if (response.state === ShipmentState.DELIVERED) {
            return IssueState.ORDER_DELIVERED
        }
        return IssueState.ORDER_PLACED
    }

    public getReturnShipmentHealth(state: ShipmentState): string {
        switch (state) {
            case ShipmentState.RETURN_INITIATED_RETURN_SHIPMENT:
            case ShipmentState.RETURN_APPROVED:
            case ShipmentState.OUT_FOR_PICKUP:
            case ShipmentState.FAILED_PICKUP:
            case ShipmentState.PICKED_UP:
                return "in_progress"

            case ShipmentState.RECEIVED:
                return "complete"

            case ShipmentState.PICKUP_CANCELED:
                return "stale"

            default:
                return ""
        }
    }

    public getForwardShipmentStatus(state: ShipmentState): string {
        switch (state) {
            case "ready":
                return "Order Confirmed"

            case "packed":
                return "Ready to ship"

            case "shipped":
                return "Shipped"

            case "out_for_delivery":
                return "Out for Delivery"

            case "failed_delivery":
                return "Failed Delivery"

            case "delivered":
                return "Delivered"

            case "return_initiated":
                return "Return Initiated"

            case "rto_initiated":
                return "Returning to Origin"

            case "refund_initiated":
                return "Refund Initiated"

            case "exchange_picked_up":
                return "Replaced"

            default:
                return ""
        }
    }

    public getReturnShipmentStatus(state: ShipmentState): string {
        switch (state) {
            case "return_initiated_return_shipment":
                return "Return Initiated"

            case "return_approved":
                return "Return Approved"

            case "out_for_pickup":
                return "Out for Pickup"

            case "failed_pickup":
                return "Pickup Failed"

            case "pickup_canceled":
                return "Pickup Cancelled"

            case "picked_up":
                return "Picked Up"

            case "received":
                return "Received at Warehouse"

            default:
                return ""
        }
    }

    public getForwardShipmentSummary(state: ShipmentState, edd: Date, timezone: Timezone) {
        let summary: string = ""

        switch (state) {
            case "ready":
                summary = "Order Confirmed."

                if (edd) {
                    summary += ` Expected to be delivered by ${TimeUtil.formatDateInTimeZone(timezone, edd, "Do MMMM h:mm a")}`
                }

                break

            case "packed":
                summary = "Order Packed."

                if (edd) {
                    summary += ` Expected to be delivered by ${TimeUtil.formatDateInTimeZone(timezone, edd, "Do MMMM h:mm a")}`
                }

                break

            case "shipped":
                summary = "Order Shipped."

                if (edd) {
                    summary += ` Expected to be delivered by ${TimeUtil.formatDateInTimeZone(timezone, edd, "Do MMMM h:mm a")}`
                }

                break

            case "out_for_delivery":
                summary = "Order out for Delivery."

                if (edd) {
                    summary += ` Expected to be delivered by ${TimeUtil.formatDateInTimeZone(timezone, edd, "Do MMMM h:mm a")}`
                }

                break

            case "failed_delivery":
                summary += "Failed to deliver."
                break

            case "delivered":
                summary = "Order Delivered."
                break

            case "exchange_picked_up":
                summary = "Item Replaced."
                break

            case "rto_initiated":
                summary = "Returning to Origin."
                break

            case "refund_initiated":
                summary = "Refund has been initiated."
                break
        }

        return summary
    }

    public getReturnShipmentSummary(state: ShipmentState) {
        switch (state) {
            case "return_initiated_return_shipment":
                return "Return request has been initiated. Pickup details will be shared once your request is approved."

            case "return_approved":
                return "Your return request has been approved. Pickup details will be shared shortly."

            case "out_for_pickup":
                return "Your item is out for pickup. It will be picked up today by our logistics partner."

            case "failed_pickup":
                return "Could not collect package from you."

            case "pickup_canceled":
                return "We will not attempt for pickup anymore."

            case "picked_up":
                return "Item picked up successfully by our logistics partner. Refund will be initiated once we verify the item at our warehouse."

            case "received":
                return "Item received at warehouse."
        }
    }
}
