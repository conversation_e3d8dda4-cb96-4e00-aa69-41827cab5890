import { inject, injectable } from "inversify"
import { Action, Header, WidgetView, TransferMembershipConfirmationWidget, DisplayUserDetailWidget } from "../common/views/WidgetView"
import { FoodProduct as Product, UserDeliveryAddress } from "@curefit/eat-common"
import { ProductType } from "@curefit/product-common"
import { Vertical, UserAgentType as UserAgent } from "@curefit/base-common"
import { Order, OrderProduct, OrderSource, CultPackTransferMetadata } from "@curefit/order-common"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { ICatalogueService, CATALOG_CLIENT_TYPES, ICatalogueServicePMS } from "@curefit/catalog-client"
import { ICultServiceOld as ICultService, CULT_CLIENT_TYPES, ICultService as ICultServiceNew } from "@curefit/cult-client"
import { UserActivity } from "../user/TimelineView"
import OrderViewBuilder, { OrderInfo } from "./OrderViewBuilder"
import CultUtil, { isUpgradeMembershipOrder } from "../util/CultUtil"
import { IProgramBusiness } from "../program/IProgramBusiness"
import { PageWidget } from "../page/Page"
import { IHealthfaceService, ALBUS_CLIENT_TYPES } from "@curefit/albus-client"
import IRecommendationBusiness from "../user/IRecommendationBusiness"
import { FitnessShipment } from "@curefit/shipment-common"
import { IShipmentService } from "@curefit/alfred-client"
import IOrderConfirmationBusiness from "./IOrderConfirmationBusiness"
import { DIYPack, DIYPackFulfilment } from "@curefit/diy-common"
import TeleconsultationDetailsPageConfig from "../care/TeleconsultationDetailsPageConfig"
import HCUDetailsPageConfig from "../care/HCUDetailsPageConfig"
import { IFulfilmentService } from "@curefit/alfred-client"
import { IDeliveryAreaService, DELIVERY_CLIENT_TYPES, IDeliverySlotService } from "@curefit/delivery-client"
import { IBaseWidget, ISegmentService, UserContext } from "@curefit/vm-models"
import { Logger, BASE_TYPES, ILogger } from "@curefit/base"
import { ICapacityService, MASTERCHEF_CLIENT_TYPES } from "@curefit/masterchef-client"
import TherapyPageConfig from "../therapy/TherapyPageConfig"
import { IAlfredServiceEther as IAlfredService, ALFRED_CLIENT_TYPES } from "@curefit/alfred-client"
import { IPageService } from "@curefit/vm-models"
import BookingConfirmationPageConfig from "./BookingConfirmationPageConfig"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import VMPageBuilder from "../page/vm/VMPageBuilder"
import { IOfferServiceV2, IOfferService, OfferServiceV3 } from "@curefit/offer-service-client"
import { KiosksDemandService } from "@curefit/masterchef-client"
import { OFFER_SERVICE_CLIENT_TYPES } from "@curefit/offer-service-client"
import { ICultBusiness } from "../cult/CultBusiness"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { CacheHelper } from "../util/CacheHelper"
import { EmiInterestReadonlyDaoMongoImpl, PAYMENT_MODELS_TYPES } from "@curefit/payment-models"
import { HamletBusiness, HAMLET_TYPES } from "@curefit/hamlet-node-sdk"
import { CAESAR_CLIENT_TYPES, IMenuService } from "@curefit/caesar-client"
import { SHIPMENT_MODELS_TYPES, ICartShipmentReadonlyDao } from "@curefit/shipment-models"
import { GYMFIT_CLIENT_TYPES, IGymfitService } from "@curefit/gymfit-client"
import { CouponInfoWidget, User } from "@curefit/apps-common"
import { CouponUtil } from "../coupon/CouponUtil"
import _ = require("lodash")
import { ClassInviteLinkCreator } from "../cult/invitebuddy/ClassInviteLinkCreator"
import { FITCASH_CLIENT_TYPES, IFitcashService } from "@curefit/fitcash-client"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService } from "@curefit/diy-client"
import { FitclubBusiness } from "../fitclub/FitclubBusiness"
import { FITCLUB_CLIENT_TYPES, IFitClubService } from "@curefit/fitclub-client"
import { CFS_TYPES } from "@curefit/cfs-client"
import { ICFSClient as IFormService } from "@curefit/cfs-client/dist/src/ICFSClient"
import { SOCIAL_CLIENT_TYPES, SocialService } from "@curefit/social-client"
import { IConsumablesService, IRewardService, REWARD_CLIENT_TYPES } from "@curefit/reward-client"
import GearCatalogueLandingPageService from "../page/GearCatalogueLandingPageService"
import { IDENTITY_CLIENT_TYPES, IIdentityService } from "@curefit/identity-client"
import { ICareBusiness } from "../care/CareBusiness"
import {
    ISubSectionDataService,
    EHR_CLIENT_TYPES
} from "@curefit/ehr-client"
import IssueBusiness from "../crm/IssueBusiness"
import { IFeedback, WidgetWithMetric } from "@curefit/vm-common"
import { IAppFeedback } from "../page/vm/services/AppFeedbackService"
import { ConfigService, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import CFAPIJavaService from "../CFAPIJavaService"
import * as express from "express"
import { IOllivanderCityService, OLLIVANDER_CLIENT_TYPES } from "@curefit/ollivander-node-client"
import { CENTER_SERVICE_CLIENT_TYPES, ICenterService } from "@curefit/center-service-client"
import { OfferHelper, OMS_API_CLIENT_TYPES, IOrderService } from "@curefit/oms-api-client"
import AppUtil from "../util/AppUtil"
import { CULTSPORT_FEEDBACK_CLIENT_TYPES, FeedbackRequest, ICultsportFeedbackService, ProductFeedback } from "@curefit/cultsport-feedback-client"
import { GearService, GearUtil, GEARVAULT_CLIENT_TYPES } from "@curefit/gearvault-client"
import { TimeUtil } from "@curefit/util-common"
import { Moment } from "moment"
import MixpanelEventService from "../cultsport/MixpanelEventService"
import { IPersonalTrainingService, PERSONAL_TRAINING_CLIENT_TYPES } from "@curefit/personal-training-v2-client"
import { IUserService, USER_CLIENT_TYPES } from "@curefit/user-client"
import WidgetBuilder from "../page/vm/WidgetBuilder"
import { IOfflineFitnessPackService, PACK_CLIENT_TYPES } from "@curefit/pack-management-service-client"
import CreditsView from "../common/views/CreditsViewWidget"
import { CultBooking } from "@curefit/cult-common"
import { Consumable, OfferV2 } from "@curefit/offer-common"
import { OfferAddon } from "@curefit/offer-common/src/Offer"
import { AddonPackConfirmationViewV1 } from "./OrderConfirmationViewBuilder"

@injectable()
abstract class BaseOrderConfirmationViewBuilder {

    constructor(
        @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
        @inject(ALFRED_CLIENT_TYPES.AlfredService) protected alfredService: IAlfredService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) protected catalogueService: ICatalogueService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReaderPMS) protected catalogueServicePMS: ICatalogueServicePMS,
        @inject(ALFRED_CLIENT_TYPES.ShipmentService) private shipmentService: IShipmentService,
        @inject(CUREFIT_API_TYPES.OrderViewBuilder) protected orderViewBuilder: OrderViewBuilder,
        @inject(ALFRED_CLIENT_TYPES.FulfilmentService) protected fulfilmentService: IFulfilmentService,
        @inject(CUREFIT_API_TYPES.ProgramBusiness) protected programBusiness: IProgramBusiness,
        @inject(CULT_CLIENT_TYPES.CultFitService) protected cultFitService: ICultService,
        @inject(CULT_CLIENT_TYPES.ICultService) protected cultService: ICultServiceNew,
        @inject(CULT_CLIENT_TYPES.MindFitService) protected mindFitService: ICultService,
        @inject(CUREFIT_API_TYPES.OrderConfirmationBusiness) protected orderConfirmationBusiness: IOrderConfirmationBusiness,
        @inject(CUREFIT_API_TYPES.RecommendationBusiness) protected recommendationBusiness: IRecommendationBusiness,
        @inject(CUREFIT_API_TYPES.TeleconsultationDetailsPageConfig) protected tcDetailsPageConfig: TeleconsultationDetailsPageConfig,
        @inject(CUREFIT_API_TYPES.HCUDetailsPageConfig) protected hcuDetailsPageConfig: HCUDetailsPageConfig,
        @inject(CUREFIT_API_TYPES.TherapyPageConfig) protected therapyPageConfig: TherapyPageConfig,
        @inject(DELIVERY_CLIENT_TYPES.DeliveryAreaService) protected deliveryAreaService: IDeliveryAreaService,
        @inject(MASTERCHEF_CLIENT_TYPES.CapacityService) protected capacityService: ICapacityService,
        @inject(BASE_TYPES.ILogger) protected logger: Logger,
        @inject(ALBUS_CLIENT_TYPES.HealthfaceService) protected healthfaceService: IHealthfaceService,
        @inject(CUREFIT_API_TYPES.IssueBusiness) protected issueBusiness: IssueBusiness,
        @inject(CUREFIT_API_TYPES.PageService) protected pageService: IPageService,
        @inject(CUREFIT_API_TYPES.VMPageBuilder) protected VMPageBuilder: VMPageBuilder,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) protected serviceInterfaces: CFServiceInterfaces,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) protected offerServiceV2: IOfferServiceV2,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) protected offerServiceV3: OfferServiceV3,
        @inject(MASTERCHEF_CLIENT_TYPES.KiosksDemandService) protected kiosksDemandService: KiosksDemandService,
        @inject(CUREFIT_API_TYPES.BookingConfirmationPageConfig) protected bookingConfirmationConfig: BookingConfirmationPageConfig,
        @inject(DELIVERY_CLIENT_TYPES.DeliverySlotService) protected deliverySlotService: IDeliverySlotService,
        @inject(LOCATION_TYPES.CityService) protected cityService: ICityService,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
        @inject(CUREFIT_API_TYPES.CultBusiness) protected cultBusiness: ICultBusiness,
        @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness,
        @inject(PAYMENT_MODELS_TYPES.EmiInterestReadonlyDao) protected paymentDao: EmiInterestReadonlyDaoMongoImpl,
        @inject(CAESAR_CLIENT_TYPES.MenuService) protected menuService: IMenuService,
        @inject(SHIPMENT_MODELS_TYPES.CartShipmentReadonlyDao) protected cartShipmentDao: ICartShipmentReadonlyDao,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferService) private offerServiceV1: IOfferService,
        @inject(GYMFIT_CLIENT_TYPES.GymfitService) protected gymfitService: IGymfitService,
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) public membershipService: IMembershipService,
        @inject(CUREFIT_API_TYPES.ClassInviteLinkCreator) protected classInviteLinkCreator: ClassInviteLinkCreator,
        @inject(FITCASH_CLIENT_TYPES.FitcashService) protected fitcashService: IFitcashService,
        @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) protected diyFulfilmentService: IDIYFulfilmentService,
        @inject(CUREFIT_API_TYPES.FitclubBusiness) protected fitclubBusiness: FitclubBusiness,
        @inject(FITCLUB_CLIENT_TYPES.IFitClubService) protected fitclubService: IFitClubService,
        @inject(REWARD_CLIENT_TYPES.IConsumablesService) protected consumablesService: IConsumablesService,
        @inject(CFS_TYPES.ICFSClient) public formService: IFormService,
        @inject(SOCIAL_CLIENT_TYPES.SocialService) protected socialService: SocialService,
        @inject(CUREFIT_API_TYPES.CareBusiness) public careBusiness: ICareBusiness,
        @inject(CUREFIT_API_TYPES.GearCatalogueLandingPageService) protected gearCLPService: GearCatalogueLandingPageService,
        @inject(EHR_CLIENT_TYPES.ISubSectionDataService) public ehrService: ISubSectionDataService,
        @inject(CUREFIT_API_TYPES.AppFeedbackService) public userResearchAppFeedbackService: IAppFeedback,
        @inject(PAGE_CONFIG_TYPES.ConfigService) protected configService: ConfigService,
        @inject(CUREFIT_API_TYPES.CFAPIJavaService) public cFAPIJavaService: CFAPIJavaService,
        @inject(OLLIVANDER_CLIENT_TYPES.IOllivanderCityService) protected ollivanderService: IOllivanderCityService,
        @inject(CUREFIT_API_TYPES.SegmentService) public segmentService: ISegmentService,
        @inject(IDENTITY_CLIENT_TYPES.IdentityService) protected identityService: IIdentityService,
        @inject(CENTER_SERVICE_CLIENT_TYPES.CenterService) protected centerService: ICenterService,
        @inject(OMS_API_CLIENT_TYPES.OfferHelper) protected offerHelper: OfferHelper,
        @inject(OMS_API_CLIENT_TYPES.OrderService) protected omsApiClient: IOrderService,
        @inject(CULTSPORT_FEEDBACK_CLIENT_TYPES.Service) protected cultsportFeedbackService: ICultsportFeedbackService,
        @inject(CUREFIT_API_TYPES.MixpanelEventService) protected mixpanelEventService: MixpanelEventService,
        @inject(PERSONAL_TRAINING_CLIENT_TYPES.PersonalTrainingService) protected ptService: IPersonalTrainingService,
        @inject(GEARVAULT_CLIENT_TYPES.GearService) private gearService: GearService,
        @inject(CUREFIT_API_TYPES.WidgetBuilder) public widgetBuilder: WidgetBuilder,
        @inject(PACK_CLIENT_TYPES.OfflineFitnessPackService) protected offlineFitnessPackService: IOfflineFitnessPackService,
    ) {
    }

    async buildOrderConfirmationView(userContext: UserContext, order: Order, params: ConfirmationRequestParams): Promise<ConfirmationView> {
        const [detail, consumables] = await Promise.all([
            this.buildView(userContext, order, params),
            this.consumablesService.getConsumableForOrder(order.orderId).catch(err => {
                this.logger.error(`Error while fetching consumables for order: ${order.orderId}`, err)
                return undefined
            })
        ])
        if (consumables) {
            const couponInfoWidget: CouponInfoWidget = CouponUtil.getCouponsInfoWidget(consumables, userContext, null, order.orderId)
            if (couponInfoWidget) {
                if (_.isEmpty(detail.widgets)) {
                    detail.widgets = []
                }
                detail.widgets.push(couponInfoWidget)
            }
        }
        const orderOffers: {
            data: {
                [offerId: string]: OfferV2;
            }
        } = await this.offerServiceV3.getOffersByOffersInfo(order.offersInfo)
        const xoxoCoupons: OfferV2[] = Object.values(orderOffers.data).filter(offer =>
            offer.addons.some(addon => addon.addonType === "XOXODAY_COUPON")
        )
        if (xoxoCoupons && xoxoCoupons.length > 0 && AppUtil.isAppDynamicGIFBannerWidgetSupported(userContext)) {
            const filteredConsumables: Array<Consumable> =
                _.isEmpty(consumables) ? [] : consumables.filter((consumable: { type: string }) => consumable.type == "XOXODAY_COUPON")
            if (_.isEmpty(detail.widgets)) {
                detail.widgets = []
            }
            detail.widgets.push(this.buildVoucherWidget(order.orderId, filteredConsumables, userContext, xoxoCoupons))
        }
        return detail
    }

    buildVoucherWidget(orderId: string, consumableList: Array<Consumable>, userContext: UserContext, xoxoCoupons: OfferV2[]): any {
        const tz = userContext.userProfile.timezone
        const expiryDate = TimeUtil.addToDate(TimeUtil.getDateNow(tz), tz, xoxoCoupons[0]?.addons[0]?.config?.expiryInDays - 1 ?? 0, "days").format("Do MMM, YYYY")
        return {
            "widgetType": "DYNAMIC_GIF_BANNER_WIDGET",
            "title": "WELCOME TO CULT 🔥",
            "description": "Claim your FREE voucher today!",
            "subTitle": "Valid till " + ((!_.isEmpty(consumableList) && consumableList.length > 0) ? CouponUtil.getCouponExpiryDate(consumableList[0], userContext.userProfile.timezone) : expiryDate),
            "position": "left",
            "backgroundImageUrl": "/image/banners/cult/voucher_bk.png",
            "gifUrl": "/image/cultpass/coupon11.gif",
            "cardAction": (_.isEmpty(consumableList) || consumableList.length != 1) ? {
                actionType: "NAVIGATION",
                url: `curefit://myorderdetail?orderId=${orderId}`
            } : CouponUtil.showCustomBottomSheetForVoucher(orderId, consumableList[0])
        }
    }

    async buildView(userContext: UserContext, order: Order, params: ConfirmationRequestParams): Promise<ConfirmationView> {
        const orderProduct: OrderProduct = order.products[0]
        const product: Product = order.productSnapshots[0]
        if (product.isPack) {
            if (product.productType === "FOOD") {
                return this.buildFoodPackConfirmationView(userContext, product, order, params)
            } else if (product.productType === "FITNESS") {
                return this.buildFitnessPackConfirmationView(product, order, params)
            }
            else if (product.productType === "MIND") {
                return this.buildFitnessPackConfirmationView(product, order, params)
            }
            else if (product.productType === "PROGRAM") {
                return this.buildProgramPackConfirmationView(userContext, product, order, params)
            } else if (product.productType === "FITNESS_FIRST_PACK") {
                return this.buidFitnessFirstPackConfirmationView(userContext, product, params)
            } else if (product.productType === "GYMFIT_FITNESS_PACK" || product.productType === "THIRD_PARTY_FITNESS_PRODUCT") {
                return this.buildGymfitFitnessPackConfirmationView(userContext, product, order, params)
            } else if (product.productType === "GYMFIT_FITNESS_PRODUCT") {
                return this.buildGymfitFitnessProductConfirmationView(userContext, product, order, params)
            }
            else if (product.productType === "PULSE") {
                const fitnessShipment: FitnessShipment = await this.shipmentService.getFitnessShipmentByOrderId(order.orderId)
                return this.buildCultClassConfirmationView(userContext, fitnessShipment.cultBookingId, null, order.userId, params)
            }
            else if (product.productType === "CF_LIVE") {
                return this.buildCFLivePackConfirmationView(userContext, product, order, params)
            } else if (product.productType === "GYM_PT_PRODUCT" || product.productType === "GYM_PT_PPC_PRODUCT") {
                return this.buildGymPtConfirmationView(userContext, product, order)
            } else if (product.productType === "LUX_FITNESS_PRODUCT") {
                return this.buildLuxConfirmationView(userContext, product, order)
            } else if (product.productType === "PLAY") {
                return this.buildPlayPackConfirmationView(product, order, params)
            } else if (product.productType === "ADDON") {
                return this.buildAddonConfirmationView(product)
            }
        } else {
            if (product.productType === "LUX_FITNESS_PRODUCT") {
                return this.buildLuxConfirmationView(userContext, product, order)
            }
            if (product.productType === "CULT_BIKE") {
                return this.buildCultBikeConfirmationView(userContext, product, order, params)
            }
            if (product.productType === "VACCINE") {
                return this.buildVaccinationAppointmentConfirmationView(userContext, product, order, params)
            }
            if (order && order.clientMetadata && (order.clientMetadata as CultPackTransferMetadata).isMembershipTransfer) {
                return this.buildMembershipTransferConfirmationView(product, order, params)
            }
            if (order && isUpgradeMembershipOrder(order)) {
                return this.buildMembershipUpgradeConfirmationView(product, order, params)
            }
            if (product.productType === "FITNESS" && !CultUtil.isCultEventOrder(orderProduct)) {
                const fitnessShipment: FitnessShipment = await this.shipmentService.getFitnessShipmentByOrderId(order.orderId)
                return this.buildCultClassConfirmationView(userContext, fitnessShipment.cultBookingId, null, order.userId, params)
            }
            else if (product.productType === "FITNESS" && CultUtil.isCultEventOrder(orderProduct)) {
                return this.buildCultEventConfirmationView(orderProduct.option.fitnessEventId, order.userId, params)
            } else if (product.productType === "MIND" && !CultUtil.isCultEventOrder(orderProduct)) {
                const fitnessShipment: FitnessShipment = await this.shipmentService.getFitnessShipmentByOrderId(order.orderId)
                return this.buildMindClassConfirmationView(userContext, fitnessShipment.cultBookingId, order.userId, params)
            }
            else if (product.productType === "MIND" && CultUtil.isCultEventOrder(orderProduct)) {
                return this.buildCultMindEventConfirmationView(orderProduct.option.fitnessEventId, order.userId, params)
            } else if (product.productType === "FOOD") {
                if (!_.isNil(order.marketplaceOptions) && !_.isNil(order.marketplaceOptions.fcId)) {
                    return this.buildEatMarketplaceConfirmationView(product, order, params)
                } else {
                    return this.buildFoodSingleConfirmationView(product, order, params)
                }
            } else if (product.productType === "CONSULTATION") {
                return this.buildCareConsultationConfirmationView(userContext, product, order, params, params.userContext.sessionInfo.userAgent)
            } else if (product.productType === "GROUP_CLASS") {
                return this.buildGroupClassConfirmationView(product)
            } else if (product.productType === "BUNDLE" || ((product.productType === "DEVICE" || product.productType === "SF_CONSUMABLE") && AppUtil.isSugarFitApp(userContext))) {
                return this.buildCareBundleConfirmationView(userContext, product, order, params)
            } else if (product.productType === "DIAGNOSTICS") {
                return this.buildCareDiagnosticsConfirmationView(userContext, product, order, params)
            } else if (product.productType === "GEAR") {
                const createdDate: Moment = TimeUtil.getMomentForDate(new Date(order.createdDate) || new Date(), userContext.userProfile.timezone)
                const isNewCultsportOrderFlowSupported = true
                const user = await this.userService.getUser(userContext.userProfile.userId)
                const address: UserDeliveryAddress = order.userAddress
                let productFeedback
                this.mixpanelEventService.sendOrderSuccessEvents(userContext, order)
                try {
                    productFeedback =  await this.cultsportFeedbackService.createFeedback({
                        userId: userContext.userProfile.userId,
                        sku: GearService.cfProductIdToGearSkuName(order?.productSnapshots?.[0]?.productId),
                        productId: product.masterProductId,
                        productImage: order?.productSnapshots?.[0]?.imageUrls[0],
                        category: order?.productSnapshots?.[0]?.gearCategory,
                        articleType: order?.productSnapshots?.[0]?.gearArticleType,
                        orderId: order.orderId,
                        purchaseDate: createdDate?.valueOf(),
                        uniqueIdentifier: "POST_PURCHASE_" + order.orderId,
                        deliveryDate: undefined,
                        feedbackRequestType: "POST_PURCHASE",
                        orderSource: "CULTSPORT_WEBSITE",
                        userIdentityType: "USER_ID",
                        userIdentityId: userContext.userProfile.userId,
                        notificationRequested: false,
                        productName: `${order?.productSnapshots?.[0]?.title}${(order?.productSnapshots?.length && order?.productSnapshots?.length > 1) ? ` +${order?.productSnapshots?.length - 1}` : ""}`
                    })
                } catch (ex) {
                    this.logger.error("Error while creating feedback", ex)
                }
                let merchantInfoDetailsForGokwik
                try {
                    merchantInfoDetailsForGokwik = await this.gearService.getMerchantInfoForGokwik(order.orderId)
                } catch (ex) {
                    this.logger.error("Error while fetching merchant_info_", ex)
                }
                this.logger.info("Fetching installation details from gear for ", order.orderId)

                return this.buildGearOrderConfirmationView(user, userContext, product, order, params, productFeedback, isNewCultsportOrderFlowSupported, merchantInfoDetailsForGokwik, address)
            }
        }
        if (product.productType === "FITNESS_PREREGISTRATION") {
            return this.buildFitnessPackConfirmationView(product, order, params)
        } else if (product.productType === "MIND_PREREGISTRATION") {
            return this.buildFitnessPackConfirmationView(product, order, params)
        } else if (product.productType === "FITNESS_ACCESSORIES") {
            return this.buildAccessoriesConfirmationView(userContext, product, order, params)
        } else if (product.productType === "FOOD_MARKETPLACE") {
            return this.buildFoodMarketplaceConfirmationView(product, order, userContext)
        }
    }

    abstract buildFoodPackConfirmationView(userContext: UserContext, product: Product, order: Order, params?: ConfirmationRequestParams): Promise<ConfirmationView>

    abstract buildFitnessPackConfirmationView(product: Product, order: Order, params?: ConfirmationRequestParams): Promise<ConfirmationView>
    abstract buildAddonConfirmationView(product: Product): Promise<ConfirmationView>

    abstract buidFitnessFirstPackConfirmationView(userContext: UserContext, product: Product, params?: ConfirmationRequestParams): Promise<ConfirmationView>

    abstract buildGymfitFitnessPackConfirmationView(userContext: UserContext, product: Product, order: Order, params?: ConfirmationRequestParams): Promise<ConfirmationView>

    abstract buildGymfitFitnessProductConfirmationView(userContext: UserContext, product: Product, order: Order, params: ConfirmationRequestParams): Promise<ConfirmationView>

    abstract buildCultClassConfirmationView(userContext: UserContext, cultBookingId: string, cultBooking: CultBooking, userId: string, params?: ConfirmationRequestParams): Promise<ConfirmationView>

    abstract buildDIYSubscriptionConfirmationViewV2(userContext: UserContext, pack: DIYPack, sessionMap: { [productId: string]: Product }, fulfilment: DIYPackFulfilment): Promise<ConfirmationView>

    abstract buildCultEventConfirmationView(fitnessEventId: string, userId: string, params?: ConfirmationRequestParams): Promise<ConfirmationView>

    abstract buildMindClassConfirmationView(userContext: UserContext, cultBookingId: string, userId: string, params?: ConfirmationRequestParams): Promise<ConfirmationView>

    abstract buildCultMindEventConfirmationView(fitnessEventId: string, userId: string, params?: ConfirmationRequestParams): Promise<ConfirmationView>

    abstract buildFoodSingleConfirmationView(product: Product, order: Order, params?: ConfirmationRequestParams): Promise<ConfirmationView>

    abstract buildEatMarketplaceConfirmationView(product: Product, order: Order, params?: ConfirmationRequestParams): Promise<ConfirmationView>

    buildFoodMarketplaceConfirmationView(product: Product, order: Order, userContext: UserContext): Promise<ConfirmationView> {
        return undefined
    }

    abstract buildAccessoriesConfirmationView(userContext: UserContext, product: Product, order: Order, params?: ConfirmationRequestParams): Promise<ConfirmationView>

    abstract buildCareConsultationConfirmationView(userContext: UserContext, product: Product, order: Order, params?: ConfirmationRequestParams, userAgent?: UserAgent): Promise<ConfirmationView>

    abstract buildGroupClassConfirmationView(product: Product): Promise<ConfirmationView>

    abstract buildCareDiagnosticsConfirmationView(userContext: UserContext, product: Product, order: Order, params?: ConfirmationRequestParams): Promise<ConfirmationView>

    abstract buildCareBundleConfirmationView(userContext: UserContext, product: Product, order: Order, params?: ConfirmationRequestParams): Promise<ConfirmationView>

    abstract buildGymPtConfirmationView(userContext: UserContext, product: Product, order: Order): Promise<ConfirmationView>

    abstract buildLuxConfirmationView(userContext: UserContext, product: Product, order: Order): Promise<ConfirmationView>

    abstract buildProgramPackConfirmationView(userContext: UserContext, product: Product, order: Order, params?: ConfirmationRequestParams): Promise<ConfirmationView>

    abstract buildGearOrderConfirmationView(user: User, userContext: UserContext, product: Product, order: Order, params?: ConfirmationRequestParams, feedback?: FeedbackRequest, isNewCultsportOrderFlowSupported?: boolean, merchantInfoDetailsForGokwik?: any, address?: UserDeliveryAddress): Promise<ConfirmationView>

    abstract buildWaitlistClassConfirmationView(bookingNumber: string, productType: ProductType, userContext: UserContext, params?: ConfirmationRequestParams): Promise<ConfirmationView>

    abstract buildLiveClassConfirmationView(userContext: UserContext, classId: string, params?: ConfirmationRequestParams, req?: express.Request): Promise<ConfirmationView>

    buildFitClubWelcomeView(userContext: UserContext, order: Order, params: ConfirmationRequestParams): Promise<FitClubConfirmationView> {
        return undefined
    }

    buildMembershipTransferConfirmationView(product: Product, order: Order, params?: ConfirmationRequestParams): Promise<ConfirmationView> {
        return undefined
    }
    buildMembershipUpgradeConfirmationView(product: Product, order: Order, params?: ConfirmationRequestParams): Promise<ConfirmationView> {
        return undefined
    }

    abstract buildCFLivePackConfirmationView(userContext: UserContext, product: Product, order: Order, params?: ConfirmationRequestParams): Promise<ConfirmationView>

    abstract buildVaccinationAppointmentConfirmationView(userContext: UserContext, product: Product, order: Order, params?: ConfirmationRequestParams): Promise<ConfirmationView>

    abstract buildCultBikeConfirmationView(userContext: UserContext, product: Product, order: Order, params?: ConfirmationRequestParams): Promise<ConfirmationView>

    abstract buildPlayPackConfirmationView(product: Product, order: Order, params?: ConfirmationRequestParams): Promise<ConfirmationView>

}


export abstract class ConfirmationView {
    action: string
    pageAction?: Action
    vertical: Vertical | "ADDON"
    header?: Header
    body?: UserActivity
    footer?: Header
    widgets?: (WidgetView | PageWidget | IBaseWidget | DisplayUserDetailWidget | ProTipWidgetView)[]
    orderInfo?: OrderInfo
    splashScreenView?: SplashScreenView
    pageActions?: Action[]
    triggerUserStatus?: boolean
    meta?: any
    constructor(action: string) {
        this.action = action
    }
    hideNavigationBar?: boolean
    rescheduleClassHeader?: Header
    feedback?: IFeedback[]
    productType?: ProductType
    calendarEventAction?: Action
    confirmAction?: Action[]
    status?: "success" | "pending" | "failed"
    creditsData?: CreditsView
    navigateToHomeTab?: boolean
}

export interface ProTipWidgetView {
    widgetType: string
    title: string
    subTitle: string
    items: any[]
}

export abstract class FitClubConfirmationView {
    action: string
    title: string
    subTitle: string
    pageAction: Action
    vertical: Vertical
    status?: "success" | "pending" | "failed"
}

export abstract class DIYConfirmationView extends ConfirmationView {
    fulfilment?: DIYPackFulfilment
}

export class OrderStatusView extends ConfirmationView {
    failureMessage?: {
        title: string,
        subTitle: string
    }
    constructor(action: string) {
        super(action)
    }
}

export interface SplashScreenView {
    message: string
}

export interface ConfirmationRequestParams {
    orderSource: OrderSource,
    userContext: UserContext,
    freeTrialExpiryDateMs?: string,
    freeLiveWithTataNeu?: boolean,
    tataNeuActivation?: boolean,
}

export default BaseOrderConfirmationViewBuilder
