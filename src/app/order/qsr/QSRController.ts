import * as express from "express"
import { Container, inject } from "inversify"
import { controller, httpPost } from "inversify-express-utils"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import IFeedbackBusiness from "../../ugc/IFeedbackBusiness"
import { UserContext } from "@curefit/userinfo-common"

export function QSRControllerFactory(kernel: Container) {

    @controller("/qsr")
    class QSRController {
        constructor( @inject(CUREFIT_API_TYPES.FeedbackBusiness) private feedbackBusiness: IFeedbackBusiness) {
        }

        @httpPost("/feedback")
        public qsrFeedback(req: express.Request, resp: express.Response) {
            return this.feedbackBusiness.createQSRFeedback(req.body)
        }
    }

    return QSRController
}

export default QSRControllerFactory

