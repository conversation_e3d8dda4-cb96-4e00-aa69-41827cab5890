import { injectable } from "inversify"
import * as express from "express"
import {
    <PERSON><PERSON>Booking,
    CultCenter,
    CultWorkout,
    SocialDataResponse as SocialDataResponseCult,
    User
} from "@curefit/cult-common"
import { DeliverySlot, FoodProduct as Product, Kiosk } from "@curefit/eat-common"
import { ActivityType, ProductType } from "@curefit/product-common"
import { CGMProduct } from "@curefit/care-common/src/product"
import { StartEndTime, Vertical } from "@curefit/base-common"
import { CultPackTransferMetadata, isMembershipUpgradeClientMetadata, Order, OrderProduct } from "@curefit/order-common"
import { ActionUtil, OfferUtil } from "@curefit/base-utils"
import CultUtil, {
    CLASS_POST_CLASS_CONFIRMATION_WAITLIST_WIDGET_ID_PROD,
    CLASS_POST_CLASS_CONFIRMATION_WIDGET_ID_PROD,
    CULT_SHOW_NOTES_POST_BOOKING_CENTER_IDS,
    CULT_SHOW_NOTES_POST_BOOKING_WORKOUT_IDS,
    CULT_TRIAL_CLASS_SIPPER_WIDGET_ID_PROD,
    CULT_TRIAL_CLASS_SIPPER_WIDGET_ID_STAGE,
    getCafeEatClpUrl,
    getCultCafeWidget,
    SHOULD_NOT_SHOW_NEXT_STEPS_SEGMENT_ID,
    STRENGTH_PULSE,
    upgradePackName
} from "../util/CultUtil"
import { SlotUtil } from "@curefit/eat-util"
import * as momentTz from "moment-timezone"
import * as _ from "lodash"
import * as moment from "moment"
import {
    Action as AppsCommonAction,
    BuddiesInviteJoiningListWidgetV2,
    CultCardContainerWidget,
    CultCardListItem,
    InfoCard,
    LeaguePostBookingInfoWidget,
    OrderConfirmationInfoWidget,
    PageTypes,
    TextInfoItems,
    TLOrderConfirmationWidget,
    UpgradePackConfirmationDetailWidget,
    WidgetView
} from "@curefit/apps-common"
import { UserActivity } from "../user/TimelineView"
import { OrderInfo } from "./OrderViewBuilder"
import { RecommendationView } from "../user/RecommendationViewBuilder"
import OrderConfirmationViewBuilder, {
    AddonPackConfirmationViewV1,
    BundleSessionPackConfirmationView,
    BundleSingleConfirmationView,
    ChronicCareCGMConfirmationView,
    ChronicCareConfirmationView,
    ChronicCareConsultationConfirmationView,
    CultBikeConfirmationView,
    FitnessPackConfirmationViewV1,
    GymFitFitnessPackConfirmationViewV1,
    GymFitPersonalTrainingPackConfirmationView,
    GymFitPersonalTrainingSlotConfirmationView,
    LuxPackConfirmationView,
    MindTherapyPackConfirmationView,
    MultiConsultationConfirmationView,
    PlayPackConfirmationViewV1,
    SfEcomOrderConfirmationView,
    TeleconsultationSingleConfirmationView,
    TeleconsultationSingleConfirmationViewV2,
} from "./OrderConfirmationViewBuilder"
import {
    ConfirmationRequestParams,
    ConfirmationView,
    ProTipWidgetView,
    SplashScreenView,
} from "./BaseOrderConfirmationViewBuilder"
import {
    CARE_AURORA_ORDER_CONFIRMATION_V1_ACTION,
    CLASS_CONFIRMED_USER_JOURNEY_ACTION,
    CLASS_WAITLIST_CONFIRMED_USER_JOURNEY_ACTION,
    FLUTTER_ORDER_CONFIRMATION_ACTION,
    ORDER_CONFIRMATION_EAT_MARKETPLACE_ACTION,
    ORDER_CONFIRMATION_V1_ACTION,
    TRANSFORM_ORDER_CONFIRMATION_V1_ACTION
} from "../util/OrderUtil"
import { CultJourneyView } from "./IOrderConfirmationBusiness"
import { CdnUtil, eternalPromise, TimeUtil } from "@curefit/util-common"
import CareUtil, { CARE_RECO_EAT_AREA_IDS, CARE_RECO_MIND_CENTER_IDS, LIVE_SGT_SNC_PRODUCT_ID } from "../util/CareUtil"
import { BillingInfo, RUPEE_SYMBOL } from "@curefit/finance-common"
import { Action, BannerItem, Header, IBaseWidget } from "@curefit/vm-models"

import { IFeedback, WidgetWithMetric } from "@curefit/vm-common"
import AppUtil from "../util/AppUtil"
import { ListPage } from "../page/vm/VMPageBuilder"
import { OfferV2 } from "@curefit/offer-common"
import { UserContext } from "@curefit/userinfo-common"
import { KiosksDemandService } from "@curefit/masterchef-client"
import {
    ActionCell,
    BookingAction,
    CongoWidget,
    CultBuddiesJoiningListSmallView,
    GymPackInfo,
    HCUBundleConfirmationSummaryWidget,
    NavigationCardWidget,
    OfferCalloutWidget,
    OfferData,
    OrderSuccessWidget,
    ProductListWidget,
    SinglesOrderConfirmationWidget,
    TransformConfirmationCoachInfo,
    TransformConfirmationSummaryWidget,
} from "../common/views/WidgetView"
import { City } from "@curefit/location-common"
import { Action as ActionWidgetView } from "../../app/common/views/WidgetView"
import LiveUtil, { CULT_LIVE_CAMPAIGN_ID, LEAGUE_POST_BOOKING_INFO_TEXT } from "../util/LiveUtil"
import { DigitalCatalogueEntryV1, LiveFitWorkoutFormat } from "@curefit/diy-common"
import { BannerCardWidgetView, BannerCarouselWidget, OrderConfimationCarouselWidget } from "../page/PageWidgets"
import { OrderSource, UserAgentType as UserAgent } from "@curefit/base-common/dist/src/models/Common"
import {
    CARE_BUNDLE_SUBCATEGORY_CODES,
    ConsultationProduct,
    DiagnosticProduct,
    HealthfaceTenant,
    Patient,
    TRANSFORM_SUB_CATEGORY_CODE_TYPE
} from "@curefit/care-common"
import { BookingDetail, BundleSessionSellableProduct, Doctor } from "@curefit/albus-client"
import { Membership } from "@curefit/membership-commons"
import { Savings } from "../fitclub/FitclubMembershipSummaryPageView"
import { IOfferAddonWidgetParams, OfferAddonWidget } from "../common/views/OfferAddonWidget"
import { GEAR_CROSS_SELL_MAP } from "../gear/constants"
import { LivePackUtil } from "../util/LivePackUtil"
import AppActionUtil from "../util/ActionUtil"
import GymfitUtil from "../util/GymfitUtil"
import { CoachPreferenceWidget } from "../transform/widgets/CoachPreferenceWidget"
import { PAGE_ID, PageWidget } from "../page/Page"
import FoodMarketplaceUtil from "../util/FoodMarketplaceUtil"
import { NeuPassClickAction } from "@curefit/third-party-integrations-client"
import { GymPtProduct } from "@curefit/gymfit-common"
import { PromiseCache } from "../util/VMUtil"
import { IdentityResponse } from "@curefit/identity-common"
import { DoctorAssetsResponse } from "@curefit/ollivander-node-client/dist/src/OllivanderCityService"
import { PreferenceDetail } from "../cult/CultBusiness"
import { COMBINED_WEIGHT_LOSS_CLP_DEEPLINK, TransformUtil } from "../util/TransformUtil"
import { SortOrder } from "@curefit/mongo-utils"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"
import { HeadersUtil } from "../../util/HeadersUtil"
import PlayUtil from "../util/PlayUtil"
import DateTimeFormatOptions = Intl.DateTimeFormatOptions
import CultClassDetailViewV2 from "../cult/CultClassDetailViewV2"

const PAISE = 100
const MINIMUM_FITCASH_TO_EARN = 100 * PAISE

@injectable()
class OrderConfirmationViewBuilderV1 extends OrderConfirmationViewBuilder {
    async buildCultClassConfirmationView(userContext: UserContext, cultBookingId: string, cultBookingParam: CultBooking, userId: string, params?: ConfirmationRequestParams): Promise<ConfirmationView> {
        let cultBooking = null
        if (cultBookingParam) {
            cultBooking = cultBookingParam
        } else {
            cultBooking = await this.cultFitService.getBookingById(cultBookingId, userId)
        }
        // segment evaluation to not show next steps widget for users who have attended more than 2 classes in last 3 months.
        userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
        const shouldNotShowNextStepsSegmentPromise = this.segmentService.doesUserBelongToSegment(SHOULD_NOT_SHOW_NEXT_STEPS_SEGMENT_ID, userContext)
        const user = await this.userCache.getUser(userId)
        const isNUXSupported = user ? await AppUtil.isNUXSupported(params.userContext, user.isInternalUser, "FITNESS", this.hamletBusiness) : false
        // Disabling this as its not used and its heavy, enable this with perf check only
        const recommendationViewPromise: any = undefined // this.getRecommendationViewPromise(params, forceCareReco)
        const recommendationView = await this.getRecommendationView("FITNESS", cultBooking, params, recommendationViewPromise, isNUXSupported)
        const cultClass = cultBooking.CultClass
        const date: string = cultClass.date
        const classId: string = cultBooking.Center.id.toString()
        const kioskId: string = classId ? this.kiosksDemandService.getKioskIdGivenCenterId(classId) : undefined
        const kiosk: Kiosk = kioskId ? this.kiosksDemandService.getKiosk(kioskId) : undefined
        const startTime: string = cultClass.startTime
        const endTime: string = cultClass.endTime
        const start: string[] = startTime.split(":")
        const end: string[] = endTime.split(":")
        const startEndTime: StartEndTime = {
            startingHours: { hour: parseInt(start[0]), min: parseInt(start[1]) },
            closingHours: { hour: parseInt(end[0]), min: parseInt(end[1]) }
        }
        const cultClassID: string = cultBooking.Center.id.toString()
        const showAddSnackWidget = await this.kiosksDemandService.shouldShowAddSnackWidget(cultClassID, date, startEndTime)
        // here
        const areaForCafe = kiosk ? await this.deliveryAreaService.findAreaForKiosk(kiosk.kioskId) : undefined
        const isDateValidForAreaId = !areaForCafe ? true : await this.menuService.isDateValidForAreaId(date, areaForCafe.areaId)
        const isCafe = kiosk && kiosk.cafeConfig && kiosk.cafeConfig.enabled && showAddSnackWidget && _.isNil(cultBooking.wlBookingNumber) && isDateValidForAreaId
        let shareAction = undefined
        if (AppUtil.isShareActionWidgetSupported(userContext) && (CultUtil.isInviteBuddySupportedForWorkout(cultClass.Workout) || CultUtil.isCultRunWorkout(cultClass.workoutID))) {
            shareAction = CultUtil.getLazyInviteLinkAction(userContext, "FITNESS", cultBooking)
        }
        const areActionCellsSupported = AppUtil.isLiveClassBookingPageSupported(userContext)
        let leaguePostBookingInfoWidget: LeaguePostBookingInfoWidget
        if (await AppUtil.isSocialLeaguesSupported(userContext, "FITNESS") && !cultClass.Workout.isSportCategory) {
            const postBookingSquadWidgetResponse = await eternalPromise(LiveUtil.getLeaguePostBookingInfoWidget(LEAGUE_POST_BOOKING_INFO_TEXT, this.socialService, userContext, this.userCache, this.logger))
            if (postBookingSquadWidgetResponse.obj) {
                leaguePostBookingInfoWidget = postBookingSquadWidgetResponse.obj
            } else {
                this.logger.info(`Error in getLeaguePostBookingInfoWidgetResponse for cult in OrderConfirmationViewBuilderV1 classId:${classId}, error: ${postBookingSquadWidgetResponse.err}`)
            }
        }
        const pageId = PAGE_ID.CULT_BOOKING_RECO_VIEW
        const feedback: IFeedback[] = await this.userResearchAppFeedbackService.getAppFeedbackForPage(userContext, pageId)
        const shouldNotShowNextSteps = process.env.ENVIRONMENT === "STAGE" ? true : !_.isEmpty(await shouldNotShowNextStepsSegmentPromise)
        let calendarEventAction
        const classCalendarPreference: PreferenceDetail = (await eternalPromise(this.cultBusiness.getClassCalendarPreference(userContext, userContext.userProfile.userId, "FITNESS"))).obj
        if (classCalendarPreference && classCalendarPreference.bookingEmailPreference) {
            if (!(await AppUtil.doesUserBelongToM1RecommendationSegment(userContext, this.serviceInterfaces.segmentService))
                || !(await CultUtil.isOnlyOneUpcomingBooking(userContext, this.cultService))) {
                try {
                    calendarEventAction = await CultUtil.getCreateCalendarEventAction(userContext, cultBooking, this.hamletBusiness)
                } catch (error) {
                    this.logger.error("Error creating calendar event action: ", error)
                }
            }
        }
        const isFlutterRequest = userContext.sessionInfo.appSource === "flutter"
        if (isFlutterRequest || CultUtil.showFlutterOrderConfirmationScreenForWorkout(cultBooking, this.cultFitService)) {
            let smartWatchBanner = null
            if (cultClass.workoutID === STRENGTH_PULSE) {
                const banners = await this.widgetBuilder.buildWidgets(["3a88d250-9183-452b-9e4b-c75e00fce2bb"], this.serviceInterfaces, userContext, undefined, undefined)
                smartWatchBanner = banners.widgets[0]
            }
            if (_.isNil(cultBooking.creditCost) && (await AppUtil.doesUserBelongToM1RecommendationSegment(userContext, this.serviceInterfaces.segmentService))
                && (await CultUtil.isOnlyOneUpcomingBooking(userContext, this.cultService))) {
                return new CultFitnessClassConfirmationViewV2(this.kiosksDemandService, cultBooking, recommendationView, userContext, calendarEventAction, isCafe, shareAction, CLASS_CONFIRMED_USER_JOURNEY_ACTION, smartWatchBanner)
            }

            const workoutID = cultBooking.CultClass.workoutID
            this.logger.info("spotify banner post class confirmation main booking", JSON.stringify(cultBooking.CultClass), JSON.stringify(cultBooking.Class))
            let widget: any = null
            const widgetResponse = await this.widgetBuilder.buildWidgets([CLASS_POST_CLASS_CONFIRMATION_WIDGET_ID_PROD], this.serviceInterfaces, userContext, undefined, undefined)
            if (!_.isEmpty(widgetResponse) && !_.isEmpty(widgetResponse.widgets)) {
                try {
                    widget = widgetResponse.widgets[0] && widgetResponse.widgets[0].widgetType === "BANNER_CAROUSEL_WIDGET" ? widgetResponse.widgets[0] : undefined

                    if (widget?.data && widget?.data?.length > 0) {
                        let data: any[]
                        data = widget.data.filter((banner: any) => {
                            const split = banner?.bannerIdentifier?.split("_")
                            const formatID = split.length > 1 ? split[1] : null
                            return formatID === workoutID.toString()
                        })

                        this.logger.info("spotify banner post class confirmation widget format ", JSON.stringify(data))

                        if (data.length === 0) {
                            data = widget?.data.filter((banner: any) => {
                                const split = banner?.bannerIdentifier?.split("_")
                                const formatID = split.length > 1 ? split[1] : null
                                return formatID === "generic"
                            })
                        }

                        this.logger.debug("spotify banner post class confirmation widget generic format ", data)

                        widget.data = data
                    }

                    recommendationView.widgets.push(widget)

                } catch (error) {
                    this.logger.info("BookingDetailViewBuilderV2 add spotify banner error", error)
                }
            }
            return new CultFitnessClassConfirmationViewV2(this.kiosksDemandService, cultBooking, recommendationView, userContext, calendarEventAction, isCafe, shareAction, null, smartWatchBanner)
        }
        return new CultFitnessClassConfirmationView(this.kiosksDemandService, cultBooking, recommendationView, params, calendarEventAction, isCafe, shareAction, null, areActionCellsSupported, leaguePostBookingInfoWidget, feedback, shouldNotShowNextSteps)
    }

    async buildGymfitFitnessProductConfirmationView(userContext: UserContext, product: Product, order: Order, params: ConfirmationRequestParams): Promise<ConfirmationView> {
        const resp = await this.serviceInterfaces.membershipService.filter({ orderId: order.orderId })
        const goldMemberships = resp.elements

        if (await AppUtil.isCenterLevelPricingSupported(userContext, this.serviceInterfaces.segmentService)) { // Ashutosh check this
            const productId = product.productId
            const gymfitFitnessProduct = await this.catalogueServicePMS.getProduct(productId)

            return Promise.resolve(new GymFitFitnessPackConfirmationViewV1(gymfitFitnessProduct, order, userContext, goldMemberships))
        } else {
            const offerIds = order.offersInfo.map(offerInfo => offerInfo.offerId)
            const offers = (await this.offerServiceV3.getOffersByIds(offerIds)).data
            return Promise.resolve(new GymFitnessProductConfirmationView(userContext, product, offers, goldMemberships[0], (await params.userContext.userPromise).isInternalUser))
        }
    }

    async buildMembershipTransferConfirmationView(product: Product, order: Order, params?: ConfirmationRequestParams): Promise<ConfirmationView> {
        const clientMetadata = order.clientMetadata as any
        const isSelectMembershipTransfer = clientMetadata?.isSelectMembershipTransfer
        const membershipTransferCellWidget = await (isSelectMembershipTransfer ? this.selectMembershipTransferCellWidget(order, params.userContext) : this.membershipTransferCellWidget(order, params.userContext))
        return {
            vertical: "CULT_FIT",
            action: ORDER_CONFIRMATION_V1_ACTION,
            navigateToHomeTab: true,
            widgets: [membershipTransferCellWidget]
        }
    }

    async buildMembershipUpgradeConfirmationView(product: Product, order: Order, params?: ConfirmationRequestParams): Promise<ConfirmationView> {
        const { clientMetadata } = order
        if (!isMembershipUpgradeClientMetadata(clientMetadata)) {
            return
        }
        const { originalPack, originalMembership } = clientMetadata
        const isGymFitPack = originalPack.productType === "GYMFIT_FITNESS_PRODUCT"
        const isPlayPack = originalPack.productType == "PLAY"
        let finalPackTitle: string
        let title: string
        if (isPlayPack) {
            finalPackTitle = "cultpass play"
        } else if (!_.isEmpty(order.productSnapshots)) {
            finalPackTitle = `${order.productSnapshots[0].title}`
        } else {
            finalPackTitle = "Upgraded Pack"
        }
        title = `From ${originalPack.title} to ${finalPackTitle}`
        const orderConfirmationWidget: SinglesOrderConfirmationWidget = {
            widgetType: "SINGLES_ORDER_CONFIRMATION_WIDGET",
            activityType: "MEMBERSHIP_UPGRADE",
            title: title,
            activityName: "Upgrade Complete",
            action: {
                actionType: "NAVIGATION",
                url: "curefit://tabpage?pageId=cult"
            },
            header: {
                title: ""
            }
        }
        /**
         * Membership start date can be in future
         * This computes membership duration correctly
         * - for active packs
         * - for upcoming packs
         */
        const { endDate, startDate } = originalMembership
        const membershipDuration = moment(endDate).diff(moment(startDate), "days") + 1
        const numberOfDaysLeftInMembership = moment(startDate).isAfter(moment())
            ? membershipDuration
            : (moment(endDate).diff(moment(), "days") + 1)
        const upgradePackDetailsWidgets: UpgradePackConfirmationDetailWidget = {
            widgetType: "MEMBERSHIP_UPGRADE_SUMMARY_ORDER_CONFIRMATION_WIDGET",
            title: "UPGRADED PACK DETAILS",
            items: [
                {
                    label: "Membership End Date",
                    value: moment(originalMembership.endDate).format("D MMM YYYY")
                },
                {
                    label: "Remaining Days",
                    value: `${numberOfDaysLeftInMembership}`
                },
                {
                    label: "Pause Days left",
                    value: `${originalMembership.remainingPauseDays}`
                }
            ]
        }
        return {
            vertical: "CULT_FIT",
            action: ORDER_CONFIRMATION_V1_ACTION,
            widgets: [
                orderConfirmationWidget,
                upgradePackDetailsWidgets as WidgetView
            ]
        }
    }

    private async membershipTransferCellWidget(order: Order, userContext: UserContext): Promise<SinglesOrderConfirmationWidget> {
        const clientMetadata = order.clientMetadata as CultPackTransferMetadata
        const transferMemberCity: City = await this.cityService.getCityByCultCityId(clientMetadata.destinationCityId)
        const transferMemberDetails: User = clientMetadata.destinationUserId && clientMetadata.destinationUserId != clientMetadata.sourceUserId ? await this.userCache.getUser(clientMetadata.destinationUserId.toString()) : null
        return {
            widgetType: "SINGLES_ORDER_CONFIRMATION_WIDGET",
            activityType: "MEMBERSHIP_TRANSFER",
            activityName: "Transfer details",
            action: {
                actionType: "NAVIGATION",
                url: (clientMetadata.tenantId === 1) ? "curefit://tabpage?pageId=cult" : "curefit://tabpage?pageId=mind"
            },
            title: "Membership is transferred to",
            subTitle: transferMemberDetails ? `${transferMemberDetails.firstName} ${transferMemberDetails.lastName}, ${transferMemberCity.name}` : `${transferMemberCity.name}`,
            description: `${clientMetadata.packName} - ${momentTz.tz(clientMetadata.newMembershipEndDate, userContext.userProfile.timezone).format("DD MMM YYYY")}`,
            header: {
                title: "Transfer successful",
                description: `Thank you for being a valuable member at ${clientMetadata.tenantId === 1 ? "cult.fit" : "mind.fit"}`
            }
        }
    }

    private async selectMembershipTransferCellWidget(order: Order, userContext: UserContext): Promise<SinglesOrderConfirmationWidget> {
        const clientMetadata = order.clientMetadata as any
        return {
            widgetType: "SINGLES_ORDER_CONFIRMATION_WIDGET",
            activityType: "MEMBERSHIP_TRANSFER",
            activityName: "Transfer details",
            action: {
                actionType: "NAVIGATION",
                url: "curefit://hometab"
            },
            title: "Membership is transferred to",
            subTitle: `${clientMetadata.transferredCenterName}`,
            description: `This membership will ends on ${clientMetadata.newMembershipEndDate}`,
            header: {
                title: "Transfer successful",
                description: `Thank you for being a valuable member at cult.fit`
            }
        }
    }


    async buildMindClassConfirmationView(userContext: UserContext, cultBookingId: string, userId: string, params?: ConfirmationRequestParams): Promise<ConfirmationView> {
        const mindBookingPromise = this.mindFitService.getBookingById(cultBookingId, userId)
        const mindBooking = await mindBookingPromise
        let socialDataEternalPromise
        if (await AppUtil.isBuddiesJoiningClassWidgetsSupported(userContext)) {
            socialDataEternalPromise = eternalPromise(this.cultFitService.getSocialDataByBooking(userId, [], [parseInt(cultBookingId)], "CUREFIT_APP"), `Error in fetching social data for userId: ${userId}, bookingId: ${cultBookingId}`)
        }
        let forceCareReco: boolean = false
        if (CARE_RECO_MIND_CENTER_IDS.indexOf(mindBooking.Center.id) !== -1) {
            forceCareReco = true
        }
        // Disabling this as its not used and its heavy, enable this with perf check only
        const recommendationViewPromise: any = undefined // this.getRecommendationViewPromise(params, forceCareReco)
        const recommendationView = await this.getRecommendationView("MIND", mindBooking, params, recommendationViewPromise)
        const tz = userContext.userProfile.timezone
        let socialDataForBookingId: SocialDataResponseCult
        let buddiesJoiningListView: CultBuddiesJoiningListSmallView
        if (socialDataEternalPromise) {
            const socialDataForBookingIds = (await socialDataEternalPromise).obj
            const nCultBookingId = Number(cultBookingId)
            socialDataForBookingId = _.find(socialDataForBookingIds, (socialData) => socialData && (socialData.bookingID == nCultBookingId))
        }
        if ((await AppUtil.isBuddiesJoiningClassWidgetsSupported(userContext)) && socialDataForBookingId && !_.isEmpty(socialDataForBookingId.attendingUsers)) {
            buddiesJoiningListView = await CultUtil.getBuddiesJoiningListSmallViewOld(socialDataForBookingId.attendingUsers, this.userCache, PageTypes.OrderConfirmationV1) as CultBuddiesJoiningListSmallView
        }
        let shareAction = undefined
        if (AppUtil.isShareActionWidgetSupported(userContext)) {
            shareAction = CultUtil.getLazyInviteLinkAction(userContext, "MIND", mindBooking)
        }
        const areActionCellsSupported = AppUtil.isLiveClassBookingPageSupported(userContext)
        return new MindFitnessClassConfirmationView(this.kiosksDemandService, mindBooking, recommendationView, params, undefined, false, shareAction, buddiesJoiningListView, areActionCellsSupported)
    }

    async buildWaitlistClassConfirmationView(wlBookingNumber: string, productType: ProductType, userContext: UserContext, params?: ConfirmationRequestParams): Promise<ConfirmationView> {
        const userId = userContext.userProfile.userId
        const baseService = productType === "FITNESS" ? this.cultFitService : this.mindFitService
        // segment evaluation to not show next steps widget for users who have attended more than 2 classes in last 3 months.
        params.userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
        const shouldNotShowNextStepsSegmentPromise = this.segmentService.doesUserBelongToSegment(SHOULD_NOT_SHOW_NEXT_STEPS_SEGMENT_ID, params.userContext)
        let socialDataEternalPromise
        if (await AppUtil.isBuddiesJoiningClassWidgetsSupported(params.userContext)) {
            socialDataEternalPromise = eternalPromise(baseService.getUpcomingClassesForSquadFromCache(userId, "CUREFIT_APP"), `Error in fetching social data for userId: ${userId}, bookingNumber: ${wlBookingNumber}`)
        }
        const waitlistBooking = (await baseService.getBookingV2(wlBookingNumber, userId)).waitlist
        const tz = params.userContext.userProfile.timezone
        let socialDataForBookingIds = null
        if (socialDataEternalPromise && waitlistBooking.Class) {
            socialDataForBookingIds = (await socialDataEternalPromise).obj
            socialDataForBookingIds = (socialDataForBookingIds) ? socialDataForBookingIds.filter(squadData => squadData.classId == waitlistBooking.Class.id) : null
        }
        let shareAction
        if (AppUtil.isShareActionWidgetSupported(params.userContext)) {
            shareAction = CultUtil.getLazyInviteLinkAction(params.userContext, productType, waitlistBooking)
        }
        let buddiesJoiningListView: CultBuddiesJoiningListSmallView
        if (socialDataForBookingIds && !_.isEmpty(socialDataForBookingIds) && (await AppUtil.isBuddiesJoiningClassWidgetsSupported(userContext))) {
            const userIds = socialDataForBookingIds.map(socialDataForBookingId => socialDataForBookingId.userId)
            buddiesJoiningListView = await CultUtil.getBuddiesJoiningListSmallView(userIds, this.userCache, PageTypes.OrderConfirmationV1) as CultBuddiesJoiningListSmallView
        }
        const cultJourneyView: CultJourneyView = await this.orderConfirmationBusiness.getCultJourneyWidgetView(productType, waitlistBooking, params, params.userContext.sessionInfo.userAgent)
        if (productType === "FITNESS") {
            let calendarEventAction
            const classCalendarPreference: PreferenceDetail = (await eternalPromise(this.cultBusiness.getClassCalendarPreference(userContext, userContext.userProfile.userId, "FITNESS"))).obj
            if (classCalendarPreference && classCalendarPreference.bookingEmailPreference) {
                try {
                    calendarEventAction = await CultUtil.getCreateCalendarEventAction(userContext, waitlistBooking, this.hamletBusiness)
                } catch (error) {
                    this.logger.error("Error creating calendar event action: ", error)
                }
            }
            const shouldNotShowNextSteps = process.env.ENVIRONMENT === "STAGE" ? true : !_.isEmpty(await shouldNotShowNextStepsSegmentPromise)
            const isFlutterRequest = userContext.sessionInfo.appSource === "flutter"
            if (isFlutterRequest) {
                let smartWatchBanner = null
                if (waitlistBooking.CultClass.workoutID === STRENGTH_PULSE) {
                    const banners = await this.widgetBuilder.buildWidgets(["3a88d250-9183-452b-9e4b-c75e00fce2bb"], this.serviceInterfaces, userContext, undefined, undefined)
                    smartWatchBanner = banners.widgets[0]
                }
                if (_.isNil(waitlistBooking.creditCost) && await AppUtil.doesUserBelongToM1RecommendationSegment(userContext, this.serviceInterfaces.segmentService)
                    && (await CultUtil.isOnlyOneUpcomingBooking(userContext, this.cultService))) {
                    return new CultFitnessClassConfirmationViewV2(this.kiosksDemandService, waitlistBooking, cultJourneyView, userContext, calendarEventAction, null, shareAction, CLASS_WAITLIST_CONFIRMED_USER_JOURNEY_ACTION, smartWatchBanner)
                }
                const workoutID = waitlistBooking.CultClass.workoutID
                this.logger.info("spotify banner post class confirmation waitlist booking", JSON.stringify(waitlistBooking.CultClass), JSON.stringify(waitlistBooking.Class))
                let widget: any = null
                const widgetResponse = await this.widgetBuilder.buildWidgets([CLASS_POST_CLASS_CONFIRMATION_WAITLIST_WIDGET_ID_PROD], this.serviceInterfaces, userContext, undefined, undefined)
                if (!_.isEmpty(widgetResponse) && !_.isEmpty(widgetResponse.widgets)) {
                    try {
                        widget = widgetResponse.widgets[0] && widgetResponse.widgets[0].widgetType === "BANNER_CAROUSEL_WIDGET" ? widgetResponse.widgets[0] : undefined

                        if (widget?.data && widget?.data?.length > 0) {
                            let data: any[]
                            data = widget.data.filter((banner: any) => {
                                const split = banner?.bannerIdentifier?.split("_")
                                const formatID = split.length > 1 ? split[1] : null
                                return formatID === workoutID.toString()
                            })

                            this.logger.info("spotify banner post class confirmation widget format ", JSON.stringify(data))

                            if (data.length === 0) {
                                data = widget?.data.filter((banner: any) => {
                                    const split = banner?.bannerIdentifier?.split("_")
                                    const formatID = split.length > 1 ? split[1] : null
                                    return formatID === "generic"
                                })
                            }

                            this.logger.info("spotify banner post class confirmation final banner ", JSON.stringify(data))
                            widget.data = data
                        }

                        cultJourneyView.widgets.push(widget)

                    } catch (error) {
                        this.logger.info("BookingDetailViewBuilderV2 add spotify banner error", error)
                    }
                }

                return new CultFitnessClassConfirmationViewV2(this.kiosksDemandService, waitlistBooking, cultJourneyView, userContext, calendarEventAction, null, shareAction, null, smartWatchBanner)
            }
            return new CultFitnessClassConfirmationView(this.kiosksDemandService, waitlistBooking, cultJourneyView, params, calendarEventAction, null, shareAction, buddiesJoiningListView, false, undefined, undefined, shouldNotShowNextSteps)
        } else {
            return new MindFitnessClassConfirmationView(this.kiosksDemandService, waitlistBooking, cultJourneyView, params, undefined, null, shareAction, buddiesJoiningListView)
        }
    }

    async buildEatMarketplaceConfirmationView(product: Product, order: Order, params: ConfirmationRequestParams): Promise<ConfirmationView> {
        return Promise.resolve(new EatMarketplaceConfirmationView(order))
    }

    async buildFoodMarketplaceConfirmationView(product: Product, order: Order, userContext: UserContext): Promise<ConfirmationView> {

        const orderInfo = await this.orderViewBuilder.mapFoodMarketplaceOrderInfo(order, product)

        return await new FoodMarketplaceConfirmationView().buildView(userContext, product, order, orderInfo, undefined, undefined)
    }

    async buildFoodSingleConfirmationView(product: Product, order: Order, params?: ConfirmationRequestParams): Promise<ConfirmationView> {
        let forceCareReco: boolean = false
        if (CARE_RECO_EAT_AREA_IDS.indexOf(order.userAddress.areaId) !== -1) {
            forceCareReco = true
        }
        const orderInfo = await this.orderViewBuilder.getOrderInformation(order, params.userContext)
        // const recommendationViewPromise = this.getRecommendationViewPromise(params, forceCareReco)
        // const recommendationView = await this.getRecommendationView("FOOD", undefined, params, recommendationViewPromise)
        // const isFitclubMember = await this.fitclubBusiness.isFitclubMember(params.userContext.userProfile.userId)
        // const isFitClubApplicable = AppUtil.isFitClubApplicable(params.userContext, isFitclubMember)
        let onDemandText = ""
        if (order.products[0].option.deliverySlot === SlotUtil.ON_DEMAND_SLOT) {
            if (!order.eatOptions || !order.eatOptions.onDemandPromise) {

                this.logger.info(`Refetching on demand promise as its not present`)
                if (!order.eatOptions) {
                    order.eatOptions = {}
                }
                order.eatOptions.onDemandPromise = await this.capacityService.getDefaultOnDemandPromise(order.userAddress.areaId)
            }
            onDemandText = order.eatOptions.onDemandPromise.message
        }
        const isKiosk = order.userAddress.addressType === "KIOSK"
        const fitClubSavingsPromise = this.getFitclubSavings(orderInfo.orderId)
        const fitClubSavings = await fitClubSavingsPromise
        const slot = this.deliverySlotService.getSlotById(order.products[0].option.deliverySlot)
        return new FoodSingleConfirmationView().buildView(params.userContext, product, order, orderInfo, onDemandText, null, slot, false, fitClubSavings)
    }

    private async getFitclubSavings(orderId: string): Promise<Savings> {
        return Promise.resolve({
            freeDeliveries: 0,
            offer: 0,
            fitcash: 0,
            cultExtension: 0
        })
    }

    async buildCareConsultationConfirmationView(userContext: UserContext, product: Product, order: Order, params?: ConfirmationRequestParams, userAgent?: UserAgent): Promise<ConfirmationView> {
        const isChronicCareApp = CareUtil.getIfChronicCareFromUserContext(userContext)
        if (isChronicCareApp) {
            const bookingDetail: BookingDetail = await this.healthfaceService.getBookingDetailForBookingId(Number(product.option.bookingId), "SUGARFIT", true)
            let consultationType = "DOCTOR"
            if (_.get(bookingDetail, "consultationOrderResponse.doctor.type") === "LIFESTYLE_COACH") {
                consultationType = "COACH"
            }
            const resetNavigationStack = CareUtil.isSugarfitPhleboConsult(product?.productId) || CareUtil.isSugarfitExperienceCenterProduct(product as ConsultationProduct) || CareUtil.isSugarfitWellnessAtCenterProduct(product as ConsultationProduct) || CareUtil.isSugarfitExperienceCenterDiagnosticProduct(product as ConsultationProduct)
            const bookingIdOfBooking = order.products.length > 1 ? product.option.bookingId : order.products[0].option.bookingId
            return new ChronicCareConsultationConfirmationView(userContext, consultationType, product?.productId, bookingIdOfBooking, resetNavigationStack)
        }
        let action: string
        if (order.products.length > 1) {
            let isMPV2 = false
            let patient
            const doctors: Doctor[] = []
            const consultationProducts: ConsultationProduct[] = []
            await Promise.all(_.map(order.products, async product => {
                const bookingDetail = await this.healthfaceService.getBookingDetail(Number(product.option.bookingId), undefined, undefined, undefined, undefined, undefined, true)
                patient = _.get(bookingDetail, "consultationOrderResponse.patient", {})
                doctors.push(_.get(bookingDetail, "consultationOrderResponse.doctor", {}))
                consultationProducts.push(_.get(bookingDetail, "consultationOrderResponse.consultationProduct", {}))
                if (!isMPV2 && bookingDetail && bookingDetail.booking.rootBookingId && bookingDetail.booking.rootBookingId !== -1) {
                    const parentBooking = await this.healthfaceService.getBookingDetail(bookingDetail.booking.rootBookingId)
                    isMPV2 = parentBooking.booking.subCategoryCode === "MP_V2"
                    action = isMPV2 ? ActionUtil.carefitbundle(parentBooking.booking.productCode, parentBooking.booking.subCategoryCode, parentBooking.booking.id.toString()) : undefined
                }
            }))
            return new MultiConsultationConfirmationView(userContext, consultationProducts, this.tcDetailsPageConfig, doctors, patient, order, isMPV2, action)
        } else {
            const consultationProduct: ConsultationProduct = <ConsultationProduct>(await this.catalogueService.getProduct(product.productId))
            let bookingDetail: BookingDetail
            if (CareUtil.isLiveWorkoutConsultationDoctorType(consultationProduct.doctorType)) {
                bookingDetail = await this.healthfaceService.getBookingDetailV2({
                    bookingId: Number(order.products[0].option.bookingId),
                    tenant: consultationProduct.tenant,
                    options: {
                        isParentBookingRequired: false,
                        bundleBookingPaymentDetailsRequired: true,
                        consultationInfoParams: {
                            isDoctorRequired: true,
                            isConsultationProductRequired: true
                        },
                        isForceUpdate: false
                    }
                })
                let isLivePTOnboardingComplete
                let exploreGearAction
                try {
                    [{ isOnboarded: isLivePTOnboardingComplete }, exploreGearAction] = await Promise.all([
                        this.cultFitService.getLivePTOnboardingComplete({
                            appName: "CUREFIT_API",
                            userId: userContext.userProfile.userId
                        }),
                        this.getExploreGearAction(
                            userContext,
                            CareUtil.isLivePTProduct(consultationProduct) ? "LIVE_PERSONAL_TRAINING" : "LIVE_SGT",
                            _.get(bookingDetail, "consultationOrderResponse.consultationProduct.description")
                        )
                    ])
                } catch (err) {
                    this.logger.error("Error fetching details for live pt onboarding " + err.stack)
                    isLivePTOnboardingComplete = true
                }
                const isLiveSGTNewBookingSupported = await AppUtil.isNewSGTBookingEnabled(userContext, this.hamletBusiness)
                return new LivePtBookingConfirmationView(params.userContext, bookingDetail, consultationProduct, isLivePTOnboardingComplete, exploreGearAction, isLiveSGTNewBookingSupported)

            } else {
                // Overriding appsoure for new mind therapy aurora flow
                const isAuroraTheme = !AppUtil.isFromFlutterAppFlow(userContext) && AppUtil.isMindTherapyAuroraThemeSupported(userContext) && CareUtil.isMindDoctorType(consultationProduct.doctorType) && await AppUtil.checkIfUserPartOfMindTherapyAuroraExperiment(userContext, this.hamletBusiness)
                if (isAuroraTheme) {
                    userContext.sessionInfo.appSource = AppUtil.getFlutterAppSource()
                }

                bookingDetail = await this.healthfaceService.getBookingDetailForBookingId(Number(order.products[0].option.bookingId), consultationProduct.tenant, true)
                const user = await this.userCache.getUser(userContext.userProfile.userId)
                let isMPV2 = false, bannerWidgetPromise: Promise<WidgetWithMetric>,
                    refferalbannerPromise: Promise<WidgetWithMetric>
                if (bookingDetail && bookingDetail.booking.rootBookingId && bookingDetail.booking.rootBookingId !== -1) {
                    const parentBooking = await this.healthfaceService.getBookingDetail(bookingDetail.booking.rootBookingId, undefined, undefined, undefined, undefined, CareUtil.getCareProductTenant(consultationProduct))
                    isMPV2 = parentBooking && parentBooking.booking.subCategoryCode === "MP_V2"
                    action = isMPV2 ? ActionUtil.carefitbundle(parentBooking.booking.productCode, parentBooking.booking.subCategoryCode, parentBooking.booking.id.toString()) : undefined
                }
                if (CareUtil.showHowToVideoCallBanner(userContext, consultationProduct, bookingDetail)) {
                    bannerWidgetPromise = CareUtil.getBannerCarouselWidget(userContext, this.serviceInterfaces, CareUtil.getCareHowToJoinVideoCallWidgetId())
                }
                if (userContext.sessionInfo.userAgent === "APP") {
                    refferalbannerPromise = CareUtil.getBannerCarouselWidget(userContext, this.serviceInterfaces, CareUtil.getCareReferalBannerWidgetId())
                }
                const billingInfo: BillingInfo = this.offerHelper.getOrderBilling(order)
                const consultationInstructions = AppUtil.isFromFlutterAppFlow(userContext) ? await this.healthfaceService.getStaticInstructionsV3(
                    consultationProduct.productId,
                    consultationProduct.doctorType,
                    CareUtil.getConsultationMode(consultationProduct, bookingDetail),
                    CareUtil.getInstructionTenant(consultationProduct)
                ) : await this.healthfaceService.getConsultationInstructionsV2(
                    consultationProduct.productId,
                    consultationProduct.doctorType,
                    CareUtil.getConsultationMode(consultationProduct, bookingDetail),
                    CareUtil.getInstructionTenant(consultationProduct)
                )
                // this.logger.info(`Booking Info with Chat Channel in OrderConfirmation ${bookingDetail.booking.id} Channel: ${CareUtil.getChatChannel(bookingDetail)} Chat Enabled: ${CareUtil.getChatEnabled(bookingDetail)}`, bookingDetail.consultationOrderResponse.consultationUserState)
                const patientsList = CareUtil.isCoupleTherapist(consultationProduct.doctorType) ? await this.healthfaceService.getAllPatients(userContext.userProfile.userId) : []
                const showFeedbackWidget = !AppUtil.isWeb(userContext) && CareUtil.isTherapyOnlyDoctorType(consultationProduct.doctorType) && CareUtil.getSeekingReasonSegment(consultationProduct.doctorType) ?
                    _.isEmpty(await this.serviceInterfaces.segmentService.doesUserBelongToSegment(CareUtil.getSeekingReasonSegment(consultationProduct.doctorType), userContext)) &&
                    _.isEmpty(await this.ehrService.getPatientConsultationSubmittedReason(bookingDetail.consultationOrderResponse.patient.id.toString(), consultationProduct.doctorType))
                    : false
                let reasons: string[] = []
                if (showFeedbackWidget) {
                    reasons = await this.healthfaceService.getSessionSeekingReasons(consultationProduct.doctorType, CareUtil.getInstructionTenant(consultationProduct))
                }
                let drivenBannerWidgetPromise: Promise<WidgetWithMetric[]>
                if (!AppUtil.isFromFlutterAppFlow(userContext) && AppUtil.isDrivenBannerUISupported(userContext) && ["CARE", "MIND"].includes(consultationProduct.tenant)) {
                    drivenBannerWidgetPromise = this.buildVmWidgets(userContext, product.productType, false)
                }
                const ConfirmationView = AppUtil.isNewTCCheckoutUISupported(userContext, consultationProduct) ? TeleconsultationSingleConfirmationViewV2 : TeleconsultationSingleConfirmationView
                return new ConfirmationView(
                    params.userContext,
                    consultationProduct,
                    user,
                    bookingDetail,
                    this.tcDetailsPageConfig,
                    billingInfo,
                    isMPV2,
                    action,
                    consultationInstructions,
                    bannerWidgetPromise ? await bannerWidgetPromise : undefined,
                    refferalbannerPromise ? await refferalbannerPromise : undefined,
                    patientsList,
                    showFeedbackWidget,
                    reasons,
                    drivenBannerWidgetPromise ? await drivenBannerWidgetPromise : undefined,
                )
            }
        }
    }


    private async getExploreGearAction(userContext: UserContext, bookingType: ProductType, workoutType: string | LiveFitWorkoutFormat): Promise<ExploreGearView | undefined> {
        if (AppUtil.isInternationalApp(userContext)) { // Gear is not supported in international app
            return
        }

        const tz = userContext.userProfile.timezone
        // Expire in one week
        const now = TimeUtil.getDateNow(tz)
        if (now >= new Date("2020-07-29T14:00:00+05:30")) return

        if (!AppUtil.isLiveNewConfirmationPageDesignSupported(userContext)) return

        try {
            let crossSellMapping
            switch (bookingType) {
                case "LIVE_FITNESS":
                    const allowedFormats = ["SNC", "STRENGTH", "CARDIO", "HRX", "BOXING", "TABATA", "HIIT", "YOGA", "PILATES"]
                    if (allowedFormats.indexOf(workoutType) !== -1) {
                        crossSellMapping = GEAR_CROSS_SELL_MAP["YOGA"]
                    }
                    break
                case "LIVE_PERSONAL_TRAINING":
                    crossSellMapping = GEAR_CROSS_SELL_MAP[/yoga|dance/i.test(workoutType) ? "YOGA" : "STRENGTH"]
                    break
                default:
                    break
            }

            if (!crossSellMapping) return
            const { icon, title, collectionId } = crossSellMapping

            const [{ products: crossSellProducts }, gearOrders] = await Promise.all([
                // Get products in the collection
                this.gearCLPService.getCataloguePageWith(collectionId),

                // Get gear products ordered in the previous 2 months

                this.getRecentGearOrder(
                    userContext.userProfile.userId,
                    60,
                    TimeUtil.addDays(tz, TimeUtil.todaysDate(tz), 1)
                )
            ])
            const crossSellProductIDs = _.map(crossSellProducts, p => Number(p.id))
            const purchasedProductIDs = _.flatMap(
                gearOrders,
                (order: Order) => _.map(order.productSnapshots, s => Number(s.masterProductId))
            )
            this.logger.info(`Gear cross-sell: userId: ${userContext.userProfile.userId}, bookingType: ${bookingType}, collectionId: ${collectionId}, product IDs: ${crossSellProductIDs}, Purchased products: ${purchasedProductIDs}`)

            // If no products in stock, bail out
            if (_.isEmpty(crossSellProductIDs)) return

            // If already bought any of the cross sell products, bail out
            if (_.intersection(crossSellProductIDs, purchasedProductIDs).length) return

            return {
                title,
                icon,
                action: {
                    actionType: "NAVIGATION",
                    url: `curefit://microgear?pageId=gearlist&pageName=${collectionId}`,
                    otherParams: {
                        pageId: "gearlist"
                    }
                }
            }
        } catch (err) {
            this.logger.error(`Error occurred while fetching details for Gear cross sell widget, userId: ${userContext.userProfile.userId}, bookingType: ${bookingType} ` + err.stack)
        }
    }

    private getRecentGearOrder(userId: string, beforeNDays: number, untilThisDay?: string): Promise<Order[]> {
        const tz = TimeUtil.IST_TIMEZONE
        const createdDateBefore = TimeUtil.subtractDays(tz, TimeUtil.todaysDate(tz), beforeNDays)
        if (untilThisDay < createdDateBefore) {
            this.logger.info("No orders in the last 6 months as we are to check for a date before that")
            return null
        }
        return this.omsApiClient.getViableOrdersForUser(userId, {
            productType: "GEAR",
            purelySuccessful: true,
            orderSourceFilter: { orderSources: ["CULTGEAR_APP", "CULTGEAR_PHONEPE_APP"], include: false },
            pageToken: TimeUtil.parseDateUTC(untilThisDay, tz).toISOString(),
            dateLimit: TimeUtil.parseDate(createdDateBefore, tz).toISOString(),
            sortOrder: SortOrder.DESC,
            count: 50
        })
    }


    private getRecommendationViewPromise(params: ConfirmationRequestParams, forceCareReco?: boolean): Promise<{ obj: RecommendationView, err?: any }> {
        return eternalPromise(this.recommendationBusiness.getRecommendations(params.userContext, forceCareReco))
    }

    private async getRecommendationView(productType: ProductType, cultBooking: CultBooking, params: ConfirmationRequestParams, recommendationViewPromise: Promise<{ obj: RecommendationView, err?: any }>, isNuxSupported?: boolean): Promise<(CultJourneyView | RecommendationView)> {
        const recoWidgets = []
        const cultAndMindSummary = await this.userCache.getCultSummary(params.userContext.userProfile.userId)

        // vm widgets
        const vmPageWidgets = await this.buildVmWidgets(params.userContext, productType)
        if (!(isNuxSupported && CultUtil.isCompletedClassCountZero(cultAndMindSummary))) {
            const widgetId = process.env.APP_ENV === "PRODUCTION" || process.env.APP_ENV === "ALPHA" ? CULT_TRIAL_CLASS_SIPPER_WIDGET_ID_PROD : CULT_TRIAL_CLASS_SIPPER_WIDGET_ID_STAGE

            const vmPageWidgetsFinal = vmPageWidgets.filter(vmPageWidget => {
                if (vmPageWidget.widgetMetric?.widgetId === widgetId) {
                    if (cultBooking?.isFirstTrial) {
                        return true
                    } else {
                        return false
                    }
                }
                return true
            })

            if (!_.isEmpty(vmPageWidgetsFinal)) {
                recoWidgets.push(...vmPageWidgetsFinal)
            }
        } else {
            const widgetId = process.env.APP_ENV === "PRODUCTION" || process.env.APP_ENV === "ALPHA" ? CULT_TRIAL_CLASS_SIPPER_WIDGET_ID_PROD : CULT_TRIAL_CLASS_SIPPER_WIDGET_ID_STAGE

            const vmPageWidgetsFinal = vmPageWidgets.filter(vmPageWidget => {
                if (vmPageWidget.widgetMetric?.widgetId === widgetId) {
                    if (cultBooking?.isFirstTrial) {
                        return true
                    } else {
                        return false
                    }
                }
                return false
            })

            if (!_.isEmpty(vmPageWidgetsFinal)) {
                recoWidgets.push(...vmPageWidgetsFinal)
            }
        }

        // cult journey widgets
        if (cultBooking) {
            const cultJourneyView: CultJourneyView = await this.orderConfirmationBusiness.getCultJourneyWidgetView(productType, cultBooking, params, params.userContext.sessionInfo.userAgent)
            if (cultJourneyView && !_.isEmpty(cultJourneyView.widgets)) {
                recoWidgets.push(...cultJourneyView.widgets)
            }
        }

        if (!_.isEmpty(recoWidgets)) {
            return {
                widgets: recoWidgets
            }
        }

        if (recommendationViewPromise) {
            const recommendationView = await recommendationViewPromise
            return recommendationView.obj
        }
    }

    private async buildVmWidgets(userContext: UserContext, productType: ProductType, isPack?: boolean) {
        if (isPack && AppUtil.isWeb(userContext)) {
            return []
        }
        const bannerConfig = isPack
            ? this.bookingConfirmationConfig.packRecommendationConfigData(productType)
            : this.bookingConfirmationConfig.recommendationConfigData(productType)
        this.logger.info("recommendation view banner config=" + JSON.stringify(bannerConfig))
        if (!_.isEmpty(bannerConfig) && bannerConfig.pageId) {
            const recoPage: ListPage = await this.VMPageBuilder.getPage(bannerConfig.pageId, userContext, {
                "crossSellPageType": CareUtil.getCrossSellPageTypeFromProduct(productType, isPack)
            }) as ListPage
            const recoPageWidgets = []
            if (recoPage && recoPage.body) {
                for (const widget of recoPage.body) {
                    recoPageWidgets.push(widget)
                }
                return recoPageWidgets
            }
        }
    }

    private selectBannerRandomly(banners: BannerItem[]): BannerItem {
        const randomNumber = Math.floor((Math.random() * 100) + 1)
        const reco_prob: { rangeStart: number, rangeEnd: number, bannerIndex: number }[] = []
        let currentNumber: number = 0
        let index: number = 0
        for (const banner of banners) {
            const banner_prob: { rangeStart: number, rangeEnd: number, bannerIndex: number } = {
                rangeStart: parseInt(currentNumber as any),
                rangeEnd: parseInt(currentNumber as any) + parseInt(banner.recommendationRate as any),
                bannerIndex: index
            }
            reco_prob.push(banner_prob)
            currentNumber = currentNumber + banner.recommendationRate
            index = index + 1
        }
        const filteredBanners = reco_prob.filter((prob) => {
            return (randomNumber >= prob.rangeStart && randomNumber <= prob.rangeEnd)
        })
        if (_.isEmpty(filteredBanners)) {
            return null
        }
        return banners[filteredBanners[0].bannerIndex]
    }

    async buildLiveClassConfirmationView(userContext: UserContext, classId: string, params?: ConfirmationRequestParams, req?: express.Request) {
        const recommendationViewPromise: any = undefined
        const recommendationView = await this.getRecommendationView("LIVE_FITNESS", undefined, params, recommendationViewPromise)
        // this.logger.info("recommendationView: " + JSON.stringify(recommendationView))
        const [{ card, supported }, liveVideoResponse, { obj: socialData }] = await Promise.all([
            this.cultBusiness.isUserEligibleForInviteOffer(userContext, CULT_LIVE_CAMPAIGN_ID),
            this.diyFulfilmentService.getDigitalCatalogueEntry(classId),
            eternalPromise(this.diyFulfilmentService.getSocialDataForSession(userContext.userProfile.userId, classId, !AppUtil.isLiveLazyInviteLinkActionSupported(userContext), AppUtil.getTenantFromUserContext(userContext)))
            // LiveUtil.getLiveNotificationObject(userContext, this.hamletBusiness, this.cFAPIJavaService, this.logger, true)
        ])
        // const isTrialUser = !_.isEmpty(segment)
        const exploreGearAction = await this.getExploreGearAction(userContext, "LIVE_FITNESS", liveVideoResponse.format)
        const shareAction = await LiveUtil.getShareAction(userContext, this.classInviteLinkCreator, liveVideoResponse, socialData, card && {
            ...card,
            source: "order-confirmation"
        })
        // const cultPreference: PreferenceDetail = (await eternalPromise(this.cultBusiness.getClassRemindersPreference(userContext, userContext.sessionInfo.sessionData.cityId, userContext.userProfile.userId, "FITNESS"))).obj
        const bookingPreference = LiveUtil.isInteractiveSession(liveVideoResponse?.preferredStreamType) // (_.isEmpty(cultPreference)) ? false : cultPreference.bookingEmailPreference
        let calendarEventAction
        if (bookingPreference) {
            calendarEventAction = await LiveUtil.getCreateCalendarEventAction(userContext, liveVideoResponse, this.classInviteLinkCreator, bookingPreference)
        }
        const orderSource = params?.orderSource
        return LiveUtil.isVanillaLiveFitFormat(liveVideoResponse.format)
            ? new EatLiveClassConfirmationView(userContext, liveVideoResponse, calendarEventAction, recommendationView)
            : new LiveClassConfirmationView(
                {
                    shareAction,
                    videoResponse: liveVideoResponse,
                    calendarEventAction,
                    referralSupported: supported,
                    recommendationView,
                    exploreGearAction,
                    orderSource
                }, userContext, null
            )
    }

    async buildCFLivePackConfirmationView(userContext: UserContext, product: Product, order: Order, params?: ConfirmationRequestParams): Promise<ConfirmationView> {
        const liveMemberships = await this.diyFulfilmentService.getAllMembershipDetails(userContext.userProfile.userId, AppUtil.getTenantFromUserContext(userContext))
        this.logger.info(`buildCFLivePackConfirmationView product: ${JSON.stringify(product)}`)
        // const cfLiveProduct = await this.catalogueService.getCFLiveProduct(product.productId)
        // this.logger.info(`buildCFLivePackConfirmationView cfLiveProduct: ${JSON.stringify(cfLiveProduct)}`)
        const currentOrderMembership: Membership[] = []
        if (liveMemberships.length > 0) {
            liveMemberships.forEach((membership: Membership) => {
                if (membership.orderId === order.orderId) {
                    currentOrderMembership.push(membership)
                }
            })
        }
        const onboardingBanner = await LiveUtil.getOnboardingOrderConfirmationBanner(userContext, this.segmentService, this.hamletBusiness, this.diyFulfilmentService)
        const coachBanner = await LiveUtil.getCoachOrderConfirmationBanner(userContext, this.segmentService, this.hamletBusiness, this.diyFulfilmentService)
        currentOrderMembership.sort((a, b) => {
            return a.start - b.start
        })
        const notification: any = undefined
        // if (currentOrderMembership[0].type === MembershipType.TRIAL && AppUtil.isWhatsappCommunicationForNudgesSupported(userContext)) {
        //     notification = await LiveUtil.getLiveNotificationObject(userContext, this.hamletBusiness, this.cFAPIJavaService, this.logger, false)
        // }
        const recoWidgets = currentOrderMembership.length ? await this.buildVmWidgets(userContext, product.productType, true) : []
        return currentOrderMembership.length ? new LivePackConfirmationView(userContext, order, currentOrderMembership[0], product, params, notification, recoWidgets, onboardingBanner, coachBanner) : undefined
    }

    async buildTataNeuActivatedConfirmationView(neuPassAction: NeuPassClickAction, userContext: UserContext, isNeuPassConsentPending: boolean): Promise<ConfirmationView> {
        return new TataNeuConfirmationView(neuPassAction, userContext, isNeuPassConsentPending)
    }

    async buildCareBundleConfirmationView(userContext: UserContext, product: Product, order: Order): Promise<ConfirmationView> {
        const productId = order.products[0].option.parentProductCode ? order.products[0].option.parentProductCode : product.productId
        let diagnosticProduct = <DiagnosticProduct>await this.catalogueService.getProduct(productId)
        const orderSource = userContext.sessionInfo.orderSource
        const tenant: HealthfaceTenant = CareUtil.getHealthfaceTenant(diagnosticProduct.subCategoryCode, undefined, orderSource)
        const isSugarFitApp = CareUtil.getIfChronicCareFromUserContext(userContext)
        let bookingDetail: BookingDetail
        if (isSugarFitApp && product.productType === "SF_CONSUMABLE") {
            // Don't need booking details
            return new SfEcomOrderConfirmationView(order.orderId)
        } else {
            bookingDetail = await this.healthfaceService.getBookingDetailForBookingId(Number(order.products[0].option.bookingId), tenant)
        }
        const pageActions = []
        if (isSugarFitApp) {
            if (product.productType === "DEVICE") {
                const cgmProduct = <CGMProduct>await this.catalogueService.getProduct(productId)
                return new ChronicCareCGMConfirmationView(userContext, bookingDetail, cgmProduct)
            }
            return new ChronicCareConfirmationView(userContext, bookingDetail, diagnosticProduct)
        }
        if (diagnosticProduct.subCategoryCode === "LIVE_PERSONAL_TRAINING" || diagnosticProduct.subCategoryCode === "LIVE_SGT") {
            let isLivePTOnboardingComplete
            try {
                isLivePTOnboardingComplete = (await this.cultFitService.getLivePTOnboardingComplete({
                    appName: "CUREFIT_API",
                    userId: userContext.userProfile.userId
                })).isOnboarded
            } catch (err) {
                this.logger.error("Error fetching details for live pt onboarding " + err.stack)
                isLivePTOnboardingComplete = true
            }
            let offerAddonWidget: OfferAddonWidget
            if (diagnosticProduct.subCategoryCode === "LIVE_PERSONAL_TRAINING" && !_.isEmpty(order.offersInfo)) {
                const offerIds = order.offersInfo.map(offerInfo => offerInfo.offerId)
                const offerV2MapPromise = this.offerServiceV3.getOffersByIds(offerIds).then(o => o.data)
                const offerAddonWidgetParams: IOfferAddonWidgetParams = {
                    logger: this.logger,
                    offerV2MapPromise: offerV2MapPromise,
                    useLabel: "ORDER_LABEL"
                }
                offerAddonWidget = await new OfferAddonWidget().buildView(offerAddonWidgetParams)
            }
            if (diagnosticProduct.subCategoryCode === "LIVE_SGT") {
                const sgtBookAction = await this.careBusiness.getLivePTSessionBookAction(userContext, {
                    productId: LIVE_SGT_SNC_PRODUCT_ID,
                    actionTitle: "BOOK SESSION",
                    subCategoryCode: diagnosticProduct.subCategoryCode
                })
                pageActions.push(sgtBookAction)
            }
            return new LivePTPackConfirmationView(userContext, diagnosticProduct, bookingDetail, isLivePTOnboardingComplete, offerAddonWidget, pageActions)
        } else if (diagnosticProduct.subCategoryCode === "MIND_THERAPY") {
            if (AppUtil.isMindTherapyAuroraThemeSupported(userContext)) {
                const healthfaceTenant: HealthfaceTenant = CareUtil.getHealthfaceTenant(diagnosticProduct.subCategoryCode)
                const healthfaceProduct = <BundleSessionSellableProduct>(await this.healthfaceService.getProductInfoDetailsCached("BUNDLE", diagnosticProduct.subCategoryCode, diagnosticProduct.productId, healthfaceTenant))[0].baseSellableProduct
                return new MindTherapyPackFlutterConfirmationView(userContext, diagnosticProduct, bookingDetail, healthfaceProduct)
            }
            let drivenBannerWidgetPromise: Promise<WidgetWithMetric[]>
            if (AppUtil.isDrivenBannerUISupported(userContext)) {
                drivenBannerWidgetPromise = this.buildVmWidgets(userContext, product.productType, false)
            }
            return new MindTherapyPackConfirmationView(diagnosticProduct, bookingDetail, this.therapyPageConfig, userContext, drivenBannerWidgetPromise ? await drivenBannerWidgetPromise : undefined)
        } else if (diagnosticProduct.subCategoryCode === "PERSONAL_TRAINING") {
            return new BundleSessionPackConfirmationView(diagnosticProduct, bookingDetail)
        } else if (CareUtil.isTransformTenant(diagnosticProduct.subCategoryCode)) {
            let coachInfo: TransformConfirmationCoachInfo
            if (bookingDetail.bundleOrderResponse && bookingDetail.bundleOrderResponse.agentId) {
                const coachDetails = await this.healthfaceService.getDoctorDetails(bookingDetail.bundleOrderResponse.agentId)
                if (coachDetails) {
                    coachInfo = {
                        title: "Meet your coach",
                        name: coachDetails.name,
                        imageUrl: coachDetails.displayImage,
                        experience: `${coachDetails.experience} ${coachDetails.experience > 1 ? "years" : "year"} of experience`,
                        action: {
                            title: "SCHEDULE CONSULTATION",
                            actionType: "NAVIGATION",
                            url: ""
                        }
                    }
                }
            }
            return new TransformPackConfirmationView(diagnosticProduct, bookingDetail, coachInfo, userContext)
        }

        if (!_.isNaN(bookingDetail.booking.rootBookingId) && bookingDetail.booking.rootBookingId !== -1) {
            bookingDetail = await this.healthfaceService.getBookingDetailForBookingId(bookingDetail.booking.rootBookingId, tenant)
            diagnosticProduct = <DiagnosticProduct>await this.catalogueService.getProduct(bookingDetail.booking.productCode)
        }
        let drivenBannerWidgetPromise: Promise<WidgetWithMetric[]>
        if (CARE_BUNDLE_SUBCATEGORY_CODES.includes(diagnosticProduct.subCategoryCode) && AppUtil.isDrivenBannerUISupported(userContext)) {
            drivenBannerWidgetPromise = this.buildVmWidgets(userContext, product.productType, false)
        }
        return new BundleSingleConfirmationView(userContext, diagnosticProduct, bookingDetail, drivenBannerWidgetPromise ? await drivenBannerWidgetPromise : undefined)
    }

    async buildLuxConfirmationView(userContext: UserContext, product: Product, order: Order): Promise<ConfirmationView> {
        const productId = product.productId
        const luxPack: OfflineFitnessPack = await this.offlineFitnessPackService.getCachedPackById(productId)
        const orderId = order.orderId
        const membershipResponse = await this.membershipService.filter({ orderId })
        const luxMemberships = membershipResponse.elements

        return new LuxPackConfirmationView(luxPack, order, userContext, luxMemberships)

    }

    async buildFitnessPackConfirmationView(product: Product, order: Order, params?: ConfirmationRequestParams): Promise<ConfirmationView> {
        if (await AppUtil.isCenterLevelPricingSupported(params.userContext, this.serviceInterfaces.segmentService)) {
            const userContext = params.userContext
            const productId = product.productId
            const fitnessPack: OfflineFitnessPack = await this.catalogueServicePMS.getProduct(productId)

            const orderId = order.orderId
            const membershipResponse = await this.membershipService.filter({ orderId })
            const fitnessMemberships = membershipResponse.elements

            return new FitnessPackConfirmationViewV1(fitnessPack, order, userContext, fitnessMemberships)
        } else {
            // Remove this part of code when we move elite purchase to flutter
            return super.buildFitnessPackConfirmationView(product, order, params)
        }

    }

    async buildAddonConfirmationView(product: Product): Promise<ConfirmationView> {
        const productId = product.productId
        const offlineFitnessPack: OfflineFitnessPack = await this.catalogueServicePMS.getProduct(productId)
        return new AddonPackConfirmationViewV1(offlineFitnessPack)
    }

    async buildPlayPackConfirmationView(product: Product, order: Order, params: ConfirmationRequestParams): Promise<ConfirmationView> {
        if (await PlayUtil.isPlayPackPageSupported(params.userContext, this.serviceInterfaces.segmentService)) {
            const userContext = params.userContext
            const orderId = order.orderId
            const centerId: string = order.products[0]?.option?.centerId
            const centerResponsePromise = this.centerService.getCenterById(Number(centerId))
            const membershipResponse = await this.membershipService.filter({ orderId })
            const playMemberships = membershipResponse.elements
            let offersPromise: Promise<{ [key: string]: OfferV2 }>
            if (order.offersInfo && !_.isEmpty(order.offersInfo)) {
                const offerIds = order.offersInfo.map(offerInfo => offerInfo.offerId)
                offersPromise = this.offerServiceV3.getOffersByIds(offerIds).then(o => o.data)
            }

            return new PlayPackConfirmationViewV1(
                order,
                userContext,
                playMemberships,
                offersPromise,
                centerResponsePromise
            )
        } else {
            return super.buildPlayPackConfirmationView(product, order, params)
        }
    }

    async buildGymPtConfirmationView(userContext: UserContext, product: Product, order: Order): Promise<ConfirmationView> {
        const productId = product.productId
        const ptProduct: Product = await this.catalogueService.getProduct(productId)
        const preferredTrainerId: string = order.products[0]?.option?.gymPtOrderOptions?.preferredTrainerId
        const gymCenterId: string = order.products[0]?.option?.centerId
        if (!gymCenterId) {
            return undefined
        }
        const gymfitCenter = await this.gymfitService.getGymfitCenterById(gymCenterId)
        const centerResponse = await this.centerService.getCenterById(gymfitCenter.center.centerServiceId)

        let calendarEventAction
        const classCalendarPreference: PreferenceDetail = (
            await eternalPromise(
                this.cultBusiness.getClassCalendarPreference(
                    userContext,
                    userContext.userProfile.userId,
                    "FITNESS"
                )
            )
        ).obj
        if (
            classCalendarPreference &&
            classCalendarPreference.bookingEmailPreference &&
            order.products[0]?.option?.gymPtOrderOptions?.ppcSlotStartTime
        ) {
            try {
                const ptOrderOption = order.products[0]?.option?.gymPtOrderOptions
                calendarEventAction =
                    [GymfitUtil.getCreateCalendarEventAction(
                        userContext,
                        {
                            startTime: ptOrderOption.ppcSlotStartTime,
                            endTime: ptOrderOption?.ppcSlotEndTime,
                            id: ptOrderOption.ppcSlotStartTime
                        },
                        centerResponse,
                        this.hamletBusiness
                    )]
            } catch (error) {
                this.logger.error(
                    "Error creating calendar event action: ",
                    error
                )
            }
        }

        const identityResponse: IdentityResponse = await this.identityService.getIdentityById("CUREFIT", Number(preferredTrainerId))
        if (ptProduct.productType === "GYM_PT_PRODUCT") {
            const diffInDays = TimeUtil.diffInDays(userContext.userProfile.timezone, TimeUtil.todaysDate(userContext.userProfile.timezone), order.products[0].option.startDate)
            let ptMemberships
            let agentAssets: DoctorAssetsResponse
            const isBookSessionFlowEnabled = await AppUtil.isGymPTSessionBookingSupported(this.segmentService, userContext)
            if (diffInDays < 15 && isBookSessionFlowEnabled) {
                // if the pack starting in next 15 days then display book class widget
                const orderId = order.orderId
                const membershipResponse = await this.membershipService.filter({ orderId })
                ptMemberships = membershipResponse.elements
                const agentDetails: Doctor[] = await this.ollivanderService.getDoctorDetailsByIdentityId(Number(preferredTrainerId))
                agentAssets = await this.ollivanderService.getDoctorAssets(agentDetails[0].id)
            }

            const userId = userContext?.userProfile?.userId
            const doesNoShowCancellationPolicyV1Apply = await this.ptService.isNoShowCancellationPolicyV1ApplicableInCenter(centerResponse.id, Number(userId), HeadersUtil.getCommonHeaders(userContext))

            let durationInDays
            if (doesNoShowCancellationPolicyV1Apply.doesApply) {
                const ptProduct: GymPtProduct = await this.catalogueService.getGymPtProductById(productId)

                if (ptProduct?.durationInDays) {
                    durationInDays = ptProduct?.durationInDays
                }
            }

            return new GymFitPersonalTrainingPackConfirmationView(ptProduct, order, identityResponse, centerResponse, userContext, ptMemberships, agentAssets, doesNoShowCancellationPolicyV1Apply.doesApply, durationInDays, calendarEventAction)
        }
        const startTime: number = order.products[0]?.option?.gymPtOrderOptions?.ppcSlotStartTime
        if (!startTime) {
            return undefined
        }
        const date: string = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, Number(startTime), "dddd, MMM DD • h:mm a")
        return new GymFitPersonalTrainingSlotConfirmationView(date, identityResponse, centerResponse, false, null, userContext, true, calendarEventAction)
    }

    async buildCultBikeConfirmationView(userContext: UserContext, product: Product, order: Order, params: ConfirmationRequestParams): Promise<ConfirmationView> {
        const user = await this.userCache.getUser(userContext.userProfile.userId)
        return new CultBikeConfirmationView(userContext, product, user, order, params)
    }
}

abstract class ConfirmationViewV1 extends ConfirmationView {
    protected constructor(action?: string) {
        super(!_.isEmpty(action) ? action : ORDER_CONFIRMATION_V1_ACTION)
        this.widgets = []
        this.pageAction = null
    }
}

abstract class FitnessClassConfirmationViewV2 extends ConfirmationView {
    constructor(kiosksDemandService: KiosksDemandService, cultBooking: CultBooking, recommendationView: (CultJourneyView | RecommendationView), userContext: UserContext, calendarEventAction: ActionWidgetView, isCafe: boolean, shareAction: ActionWidgetView, bannerWidget: (PageWidget | IBaseWidget)) {
        super(FLUTTER_ORDER_CONFIRMATION_ACTION)
        this.widgets = []
        this.calendarEventAction = calendarEventAction
        const orderConfirmationWidget = this.getOrderConfirmationWidget(cultBooking, userContext)
        this.widgets.push(orderConfirmationWidget)
        if (bannerWidget != null) {
            this.widgets.push(bannerWidget)
        }
        orderConfirmationWidget.footer = {
            icon: "stopwatch",
            title: [216, 434].includes(cultBooking?.CultClass?.workoutID) ? "Reach early. No entry post 2 mins of class start." : "Reach early. No entry post 5 mins of class start."
        }
        const isTrialFlow: boolean = cultBooking?.CultClass.classType === "FREE" && cultBooking?.CultClass.amount === 0
        if (isTrialFlow) {
            const whatToBringWidget = new CultClassDetailViewV2().getPreWorkoutGearWidget(cultBooking?.CultClass, true)
            this.widgets.push(whatToBringWidget)
        }

        let orderConfirmationInfoWidget
        if (isCafe || !_.isNil(shareAction)) {
            orderConfirmationInfoWidget = this.getBookingActonListInfoWidget(kiosksDemandService, cultBooking, userContext, shareAction, isTrialFlow)
        }
        if (!_.isNil(orderConfirmationInfoWidget)) {
            this.widgets.push(orderConfirmationInfoWidget)
        }
        if (recommendationView && !_.isEmpty(recommendationView.widgets)) {
            this.widgets.push(...recommendationView.widgets)
        }
        this.meta = {
            classBookingType: cultBooking.bookingType
        }
        this.pageActions = recommendationView ? recommendationView.pageActions : undefined
    }

    private getBookingActonListInfoWidget(kiosksDemandService: KiosksDemandService, cultBooking: CultBooking, userContext: UserContext, shareAction: Action, isTrialFlow: boolean) {
        const cafeData = this.getCafeData(kiosksDemandService, cultBooking, userContext)
        const inviteData = this.getInviteData(shareAction)
        const bookingActions: BookingAction[] = []
        const isAppIVRReminderSupported: boolean = AppUtil.isAppIVRReminderSupported(userContext)
        const timeZone = userContext.userProfile.timezone
        let IVRReminderCardWidget

        // if appVersion >= 10.57 for the booking show new IVR_REMINDER_CARD_WIDGET else show older widget
        if (cultBooking.ivrConfig && cultBooking.ivrConfig.isIVRApplicable && isAppIVRReminderSupported) {
            const bookingNumber = cultBooking.bookingNumber ? cultBooking.bookingNumber : cultBooking.wlBookingNumber
            IVRReminderCardWidget = CultUtil.getIVRReminderCardWidget(userContext, cultBooking.ivrConfig, bookingNumber, "Call Reminder Enabled")
        } else {
            const reminderData = this.getCallReminderData(cultBooking)
            if (!_.isNil(reminderData)) {
                bookingActions.push({
                    title: reminderData.isReminderSet && reminderData.selectedCallTime != null
                        ? "Reminder set for " + reminderData.selectedCallTime : "Set Call Reminder",
                    trailingIcon: "chevron-right",
                    leadingIcon: "call",
                    titleType: "NORMAL",
                    action: {
                        actionType: "SET_CALL_REMINDER" as any,
                        meta: reminderData,
                        analyticsData: {
                            widgetName: "Call_Time_Selection_Click",
                            bookingId: cultBooking.id,
                            userId: userContext.userProfile.userId
                        }
                    }
                })
            }
        }
        if (!isTrialFlow) {
            if (!_.isNil(cafeData)) {
                bookingActions.push({
                    action: cafeData.action,
                    title: cafeData.title,
                    trailingIcon: "chevron-right",
                    leadingIcon: "gear",
                    titleType: "NORMAL",
                })
            }
        } else {
            bookingActions.push({
                action: {
                    actionType: "NAVIGATION",
                    url: "curefit://hometab"
                },
                title: "Booking Details",
                trailingIcon: "chevron-right",
                leadingIcon: "navigate",
                titleType: "NORMAL",
            })
        }
        if (!_.isNil(inviteData)) {
            bookingActions.push({
                action: inviteData.action,
                title: "Invite buddies for workout",
                trailingIcon: "chevron-right",
                leadingIcon: "share-2",
                titleType: "NORMAL",
            })
        }
        return {
            widgetType: "BOOKING_ACTION_LIST_WIDGET" as any,
            bookingActions: bookingActions,
            topDynamicWidget: IVRReminderCardWidget ? [IVRReminderCardWidget] : null
        }
    }

    private getCircularOrderConfimationInfoWidget(kiosksDemandService: KiosksDemandService, cultBooking: CultBooking, userContext: UserContext, shareAction: Action) {
        const reminderData = this.getCallReminderData(cultBooking)
        const cafeData = this.getCafeData(kiosksDemandService, cultBooking, userContext)
        const inviteData = this.getInviteData(shareAction)
        const items = []
        if (!_.isNil(reminderData)) {
            const metaData = reminderData
            items.push({
                title: "Set Reminder",
                image: "/image/vm/b823d0e7-c0c5-4c3a-8e9a-7da5a561cadf.png",
                isIvr: true,
                action: {
                    actionType: "SET_CALL_REMINDER",
                    meta: metaData,
                    analyticsData: {
                        widgetName: "Call_Time_Selection_Click",
                        bookingId: cultBooking.id,
                        userId: userContext.userProfile.userId
                    }
                }
            })
        }
        if (!_.isNil(cafeData)) items.push(cafeData)
        if (!_.isNil(inviteData)) items.push(inviteData)
        return new OrderConfimationCarouselWidget(items, this.getSpacing())
    }

    private getSpacing(): any {
        return {
            "spacing": {
                "top": "40",
                "bottom": "40"
            }
        }
    }

    private getCallReminderData(cultBooking: CultBooking) {
        if (cultBooking.ivrConfig && cultBooking.ivrConfig.isIVRApplicable) {
            return {
                bookingNumber: cultBooking.bookingNumber ? cultBooking.bookingNumber : cultBooking.wlBookingNumber,
                isWaitlisted: !cultBooking.bookingNumber,
                isEditable: cultBooking.ivrConfig.isIVREditable,
                selectedCallTime: cultBooking.ivrConfig.minutesBefore ? CultUtil.getCallTime(cultBooking.ivrConfig.minutesBefore, cultBooking.Class.startTime) : "",
                selectedCallTimeBefore: cultBooking.ivrConfig.minutesBefore ? cultBooking.ivrConfig.minutesBefore.toString() : "",
                isReminderSet: cultBooking.ivrConfig.status,
                slots: {
                    title: "Set time for call reminder",
                    checkBoxTitle: "Set Same reminder for all classes",
                    slotTimes: CultUtil.getSlotTimes(cultBooking.ivrConfig, true, "-", cultBooking.CultClass.startTime)
                },
                slotSelectedAction: CultUtil.getSlotSelectedAction(!cultBooking.bookingNumber, cultBooking.ivrConfig.status),
                notEditableAction: {
                    type: "ALERT",
                    title: "Call reminder time can not be changed",
                    message: "Call reminder time can not be changed too close to the class start time",
                    button: {
                        title: "Ok",
                        actionType: "HIDE_ALERT_MODAL"
                    }
                },
                isSportCategory: cultBooking?.Class?.Workout?.isSportCategory
            }
        }
    }

    private getCafeData(kiosksDemandService: KiosksDemandService, cultBooking: CultBooking, userContext: UserContext) {
        const tz = userContext.userProfile.timezone
        const cafeEatClpUrl = getCafeEatClpUrl(userContext, kiosksDemandService, cultBooking, tz)
        if (!_.isNil(cafeEatClpUrl)) {
            const classId: string = cultBooking?.Center?.id.toString()
            const kioskId: string = classId ? kiosksDemandService.getKioskIdGivenCenterId(classId) : undefined
            const kiosk: Kiosk = kioskId ? kiosksDemandService.getKiosk(kioskId) : undefined
            const cultCafeWidget = getCultCafeWidget(cafeEatClpUrl, true, undefined, _.get(kiosk, "cafeConfig.cafeCatalogType", undefined), cultBooking)
            if (!_.isNil(cultCafeWidget)) {
                return {
                    title: "Order Workout Gear & Snacks",
                    image: "/image/vm/b823d0e7-c0c5-4c3a-8e9a-7da5a561cadf.png",
                    action: cultCafeWidget.action,
                }
            }
        }
    }

    private getInviteData(shareAction: Action) {
        if (shareAction) {
            return {
                title: "Invite friends",
                action: shareAction,
                image: "/image/vm/b823d0e7-c0c5-4c3a-8e9a-7da5a561cadf.png"
            }
        }
    }

    private getOrderConfirmationWidget(cultBooking: CultBooking, userContext: UserContext): OrderConfirmationInfoWidget {
        const cultClass = cultBooking.CultClass
        const tz = userContext.userProfile.timezone
        const classStartTime = TimeUtil.formatDateStringInTimeZone(cultClass.date + " " + cultClass.startTime, tz, "h:mm A")
        const classEndTime = TimeUtil.formatDateStringInTimeZone(cultClass.date + " " + cultClass.endTime, tz, "h:mm A")
        const classTiming = `${TimeUtil.formatDateStringInTimeZone(cultClass.date, tz, "ddd, D MMM")} • ${classStartTime} - ${classEndTime}`
        const center = cultBooking.Center
        const className = `${cultClass.Workout?.name}`

        const centerFacilities: string[] = []
        for (let i = 0; i < center.facilities.length; i++) {
            if (center.facilities[i].available) centerFacilities.push(center.facilities[i].name)
        }

        const centerInfoCards: any = [
            {
                icon: "location-small",
                subTitle: center.Address.addressLine1,
                cardAction: {
                    actionType: "EXTERNAL_DEEP_LINK",
                    url: center.placeUrl,
                    title: "NAVIGATE"
                },
            }
        ]
        const textInfoItems: TextInfoItems[] = []
        if (cultBooking.wlBookingNumber) {
            textInfoItems.push({
                title: `WAITLISTED`,
                tag: { value: cultBooking.waitlistNumber.toString() },
                styleProps: {
                    variant: "paragraph2Text",
                    color: "paragraphGrey"
                },
                textContainerStyleProps: {
                    style: {
                        borderWidth: 1,
                        borderColor: "#e2e4e8",
                        paddingVertical: 2,
                        paddingHorizontal: 10,
                        borderRadius: 3,
                        marginBottom: 5
                    }
                }

            })
        }
        const cultConfirmationWidget: any = {
            widgetType: "ORDER_CONFIRMATION_INFO_WIDGET",
            textInfoItems: [
                ...textInfoItems,
                {
                    title: className,
                    styleProps: {
                        variant: "paragraph4Text",
                    },
                },
                {
                    title: classTiming,
                    styleProps: {
                        variant: "header1Text",
                    },
                }
            ],
            orderMeta: {
                title: center.name,
                isDropDown: true,
                dropDownInfo: centerInfoCards,
                styleProps: {
                    variant: "paragraph5Text",
                },
                icon: "location-small"
            },
        }

        if (!_.isNil(cultBooking.creditCost)) {
            cultConfirmationWidget.lottie_url = "/image/mem-exp/lottie/confirmation_lottie.json"
            cultConfirmationWidget.audio_url = "image/mem-exp/credit_class_success.mp3"
            cultConfirmationWidget.endAnimationOffset = 0.26
            cultConfirmationWidget.lottieTextList = [{originalText: "CREDITS_TEXT_PLACEHOLDER", finalText: cultBooking.creditCost + " Credit" + ((cultBooking.creditCost === 1) ? "" : "s") + " spent"}]
        } else {
            cultConfirmationWidget.audio_url = "image/mem-exp/class_success.mp3"
        }

        if (cultBooking.isRescheduled) {
            cultConfirmationWidget.imageDescription = "Reschedule Successful"
            cultConfirmationWidget.imageUrl = "/image/icons/checkout/care/success_tick.png"
        }
        return cultConfirmationWidget
    }

    private getOrderConfirmationInfoWidget(cultBooking: CultBooking): CultCardContainerWidget {
        const cards: CultCardListItem[] = []
        let ivrProps

        if (cultBooking.ivrConfig && cultBooking.ivrConfig.isIVRApplicable) {
            cards.push({
                title: "Set a reminder",
                leftIconName: "call",
                rightIconName: "chevron-right",
                isIvr: true,
            })
            ivrProps = this.getCallReminderData(cultBooking)
        }
        if (_.isEmpty(cards)) {
            return null
        }
        return {
            widgetType: "ORDER_CONFIRMATION_ADD_ONS_WIDGET",
            cards,
            ...ivrProps,
        }
    }

    protected abstract getActionUrl(bookingNumber: string): string

    protected abstract getActivityType(): ActivityType
}

abstract class FitnessClassConfirmationView extends ConfirmationViewV1 {
    constructor(kiosksDemandService: KiosksDemandService, cultBooking: CultBooking, recommendationView: (CultJourneyView | RecommendationView), params: ConfirmationRequestParams, calendarEventAction: ActionWidgetView, isCafe?: boolean, shareAction?: ActionWidgetView, buddiesJoiningListView?: CultBuddiesJoiningListSmallView, areActionCellsSupported?: boolean, leaguePostBookingInfoWidget?: LeaguePostBookingInfoWidget, feedback?: IFeedback[], shouldNotShowNextSteps?: boolean) {
        super()
        this.calendarEventAction = calendarEventAction
        this.body = this.getActivityDescription(kiosksDemandService, cultBooking, params, isCafe, shareAction, buddiesJoiningListView)
        const isOrderConfirmationV2Supported = AppUtil.isOrderConfirmationV2Supported(params.userContext)
        if (AppUtil.isWidgetizedOrderConfirmationSupported(params.userContext)) {
            if (isOrderConfirmationV2Supported) {
                this.widgets.push(this.getOrderConfirmationWidget(this.body, cultBooking, params.userContext))
                if (CULT_SHOW_NOTES_POST_BOOKING_CENTER_IDS.includes(parseInt(cultBooking.CultClass.centerID)) && CULT_SHOW_NOTES_POST_BOOKING_WORKOUT_IDS.includes(cultBooking.CultClass.workoutID)) {
                    this.widgets.push(CultUtil.getNoteWidget(cultBooking.Class))
                }
                this.widgets.push(this.getOrderConfirmationInfoWidget(this.body, cultBooking, calendarEventAction))
                const isSportCategory: boolean = cultBooking?.Class?.Workout?.isSportCategory
                if (cultBooking.shouldAllocateSpot && (!shouldNotShowNextSteps || isSportCategory)) {
                    const nextStepWidget: any = {
                        widgetType: "INFO_LIST_WIDGET",
                        items: CultUtil.getCultBookingNextStepsInfo(cultBooking, isSportCategory),
                        header: {
                            title: "Next Steps",
                            isExpandable: true,
                        },
                        isListExpanded: cultBooking.wlBookingNumber ? false : true
                    }
                    this.widgets.push(nextStepWidget)
                }
            } else {
                this.widgets.push(this.getOrderCellWidget(this.body, areActionCellsSupported, cultBooking.isRescheduled, calendarEventAction))
            }
        }
        if (!_.isEmpty(leaguePostBookingInfoWidget)) {
            this.widgets.push(leaguePostBookingInfoWidget)
        }
        if (!isOrderConfirmationV2Supported && cultBooking.shouldAllocateSpot) {
            this.widgets.push(CultUtil.getCultBookingNextStepsWidget(cultBooking))
        }
        if (recommendationView && !_.isEmpty(recommendationView.widgets)) {
            this.widgets.push(...recommendationView.widgets)
        }
        this.pageActions = recommendationView ? recommendationView.pageActions : undefined
        this.vertical = this.getVertical()
        this.splashScreenView = this.getSplashScreenView()
        this.meta = {
            classBookingType: cultBooking.bookingType
        }
        this.widgets = this.widgets.filter(Boolean)
        if (cultBooking.bookingType === "TRIAL") {
            this.feedback = feedback
        }
    }

    private getOrderCellWidget(activity: UserActivity, areActionCellsSupported: boolean, isRescheduled: boolean, calendarEventAction: ActionWidgetView): SinglesOrderConfirmationWidget {
        const orderCellWidget: SinglesOrderConfirmationWidget = {
            widgetType: "SINGLES_ORDER_CONFIRMATION_WIDGET",
            infoBlock: activity.infoBlock,
            activityType: activity.activityType,
            subActivityType: activity.subActivityType,
            title: activity.title,
            subTitle: activity.subTitle,
            activityName: activity.activityName,
            action: activity.activityAction,
            isPulseClass: activity.isPulseClass,
            pulseDeviceName: activity.pulseDeviceName,
            isWaitlistedClass: activity.meta && activity.meta.waitlistNumber,
            waitlistNumber: activity.meta && activity.meta.waitlistNumber,
            cultCafeBlock: activity.cultCafeBlock,
            ivrConfig: activity.ivrConfig,
            inviteBuddy: !areActionCellsSupported ? activity.inviteBuddy : undefined,
            actionCells: areActionCellsSupported && activity.inviteBuddy ? [activity.inviteBuddy] : undefined,
            buddiesJoining: activity.buddiesJoining
        }
        if (calendarEventAction) {
            orderCellWidget.actionCells.push({
                title: "Add to your calendar",
                subTitle: "Get reminders for upcoming classes",
                action: calendarEventAction,
                iconUrl: "/image/icons/referral/calendar_transparent_large.png",
                removeHorizontalPadding: true,
                hasTopDivider: true,
                tintColor: "#000000"
            })
        }
        if (isRescheduled) {
            orderCellWidget["rescheduleClassHeader"] = {
                title: "Reschedule Successful!"
            }
        }
        return orderCellWidget
    }

    protected abstract getVertical(): Vertical

    private getActivityDescription(kiosksDemandService: KiosksDemandService, cultBooking: CultBooking, params: ConfirmationRequestParams, isCafe?: boolean, shareAction?: ActionWidgetView, buddiesJoiningListView?: CultBuddiesJoiningListSmallView): UserActivity {
        const cultClass = cultBooking.Class
        const userActivityId = cultClass.id.toString()
        const activityDate = cultClass.date
        const workout: CultWorkout = cultClass.Workout
        const center: CultCenter = cultBooking.Center
        const tz = params.userContext.userProfile.timezone
        const creditCost = cultBooking.creditCost
        const classStartTime = TimeUtil.formatDateStringInTimeZone(cultClass.date + " " + cultClass.startTime, tz, "h:mm A")
        // let infoBlock
        const subTitle = center.name
        // if (!_.isEmpty(cultBooking.info) && !_.isNil(cultBooking.info.message)) {
        //     infoBlock = {
        //         title: "Note: ",
        //         message: cultBooking.info.message
        //     }
        // }
        // // app version check because old apps doesnt support message larger than 1 line
        // else if (CultUtil.isCustomerBiometricSupportedInCenter(center) && !_.isEmpty(cultBooking.info) && cultBooking.info.signInMedium === "BIOMETRIC" && !cultBooking.info.isBiometricsRegistered) {
        //     infoBlock = {
        //         title: "Note: ",
        //         message: "We see that you haven't registered for biometric attendance yet. Please get in touch with the center manager for the same.",
        //         calloutView: {
        //             tag: "PENDING",
        //             title: "Your Biometric Registration is pending!",
        //             subTitle: "Please reach out to the center manager before your next class."
        //         }
        //     }
        // }

        const cultUnderlyingClass = cultBooking.CultClass
        const isPulseEnabled = CultUtil.isClassAvailableForPulse(cultUnderlyingClass, AppUtil.isCultPulseFeatureSupported(params.userContext))

        const bookCultActivity: UserActivity = {
            date: activityDate,
            infoBlock: undefined,
            activityType: this.getActivityType(),
            subActivityType: CultUtil.pulsifyClassName(cultClass, params.userContext).toUpperCase(),
            title: `${TimeUtil.formatDateStringInTimeZone(cultClass.date, tz, "ddd D MMM")}, ${classStartTime}`,
            subtitle: center.name,
            subTitle: subTitle,
            status: "TODO",
            activityName: CultUtil.pulsifyClassName(cultClass, params.userContext),
            action: `curefit://today?userActivityId=${userActivityId}&activityDate=${activityDate}`,
            activityAction: {
                "url": this.getActionUrl(cultBooking.bookingNumber || cultBooking.wlBookingNumber),
                "actionType": "NAVIGATION"
            },
            userActivityId,
            isPulseClass: isPulseEnabled,
            pulseDeviceName: isPulseEnabled && cultBooking.pulseDeviceName ? CultUtil.pulsifyDeviceName(cultBooking) : ""
        }
        // add meta for waitlist booking
        if (cultBooking.wlBookingNumber) {
            bookCultActivity.meta = {
                waitlistNumber: cultBooking.waitlistNumber
            }
        }
        const isOrderConfirmationV2Supported = AppUtil.isOrderConfirmationV2Supported(params.userContext)
        if (isOrderConfirmationV2Supported && cultBooking.ivrConfig && cultBooking.ivrConfig.isIVRApplicable) {
            const reminderDescription = cultBooking.bookingNumber ? "Call Reminder has been successfully set for this class" : "If your waitlist gets confirmed then we will give you a call at the time selected by you "
            bookCultActivity.ivrConfig = {
                widgetType: "CALL_REMINDER_WIDGET",
                title: "Set Call Reminder before Class",
                bookingNumber: cultBooking.bookingNumber ? cultBooking.bookingNumber : cultBooking.wlBookingNumber,
                isWaitlisted: !cultBooking.bookingNumber,
                isEditable: cultBooking.ivrConfig.isIVREditable,
                selectedCallTime: cultBooking.ivrConfig.minutesBefore ? CultUtil.getCallTime(cultBooking.ivrConfig.minutesBefore, cultBooking.Class.startTime) : "",
                selectedCallTimeBefore: cultBooking.ivrConfig.minutesBefore ? cultBooking.ivrConfig.minutesBefore.toString() : "",
                isReminderSet: cultBooking.ivrConfig.status,
                slots: {
                    title: "Set time for call reminder",
                    checkBoxTitle: "Set same reminder for all classes",
                    slotTimes: CultUtil.getSlotTimes(cultBooking.ivrConfig, true, "-", cultBooking.CultClass.startTime)
                },
                slotSelectedAction: CultUtil.getSlotSelectedAction(!cultBooking.bookingNumber, cultBooking.ivrConfig.status),
                notEditableAction: {
                    type: "ALERT",
                    title: "Call reminder time can not be changed",
                    message: "Call reminder time can not be changed too close to the class start time",
                    button: {
                        title: "Ok",
                        actionType: "HIDE_ALERT_MODAL"
                    }
                },
                info: {
                    icon: "INFO",
                    actionType: "NAVIGATION",
                    url: "curefit://listpage?pageId=callreminderhiw"
                },
                listItems: [
                    { description: reminderDescription, image: "/image/icons/howItWorks/complete.png" },
                    {
                        description: "To set this as your default setting for all morning classes, go to Class Reminder section on your ",
                        image: "/image/icons/howItWorks/warning.png",
                        link: {
                            actionType: "NAVIGATION",
                            title: "Profile Page",
                            url: "curefit://accountview?expanded=BOOKING_PREFERENCE"
                        }
                    }
                ]
            }
        }
        let showingCultCafeWidget
        if (isCafe && AppUtil.isCultCafeSupported(params.userContext)) {
            const cafeEatClpUrl = getCafeEatClpUrl(params.userContext, kiosksDemandService, cultBooking, tz)
            if (!_.isNil(cafeEatClpUrl)) {
                const classId: string = cultBooking?.Center?.id.toString()
                const kioskId: string = classId ? kiosksDemandService.getKioskIdGivenCenterId(classId) : undefined
                const kiosk: Kiosk = kioskId ? kiosksDemandService.getKiosk(kioskId) : undefined
                showingCultCafeWidget = true
                bookCultActivity.cultCafeBlock = getCultCafeWidget(cafeEatClpUrl, !buddiesJoiningListView, undefined, _.get(kiosk, "cafeConfig.cafeCatalogType", undefined), cultBooking)
            }
        }

        if (isOrderConfirmationV2Supported && shareAction) {
            bookCultActivity.inviteBuddy = {
                title: "Invite friends & workout together",
                subTitle: "Have your friends join you for workout",
                action: shareAction,
                widgetType: "SHARE_ACTION_WIDGET",
                iconUrl: "/image/icons/referral/whatsapp.png",
                removeHorizontalPadding: true,
                hasTopDivider: !showingCultCafeWidget || !buddiesJoiningListView
            }
        }

        if (buddiesJoiningListView) {
            bookCultActivity.buddiesJoining = buddiesJoiningListView
        }
        if (creditCost) {
            bookCultActivity.creditCost = creditCost
        }
        return bookCultActivity
    }

    private getSplashScreenView(): SplashScreenView {
        const splashScreenView: SplashScreenView = {
            message: "Class booked!"
        }
        return splashScreenView
    }

    private getOrderConfirmationWidget(activity: UserActivity, cultBooking: CultBooking, userContext: UserContext): OrderConfirmationInfoWidget {
        const cultClass = cultBooking.CultClass
        const tz = userContext.userProfile.timezone
        const classStartTime = TimeUtil.formatDateStringInTimeZone(cultClass.date + " " + cultClass.startTime, tz, "h:mm A")
        const classEndTime = TimeUtil.formatDateStringInTimeZone(cultClass.date + " " + cultClass.endTime, tz, "h:mm A")
        const title = `${TimeUtil.formatDateStringInTimeZone(cultClass.date, tz, "ddd, D MMM")} • ${classStartTime} - ${classEndTime}`
        const center = cultBooking.Center

        const todaysDate = TimeUtil.todaysDate(tz)
        const diffInDays = TimeUtil.diffInDays(userContext.userProfile.timezone, todaysDate, cultClass.date)
        const isAuroraConfirmationSupported = AppUtil.isAuroraOrderConfirmationSupported(userContext)
        let daysText = `IN ${diffInDays} DAYS`
        if (diffInDays === 0) {
            daysText = "TODAY"
        } else if (diffInDays === 1) {
            daysText = "TOMORROW"
        }
        const className = `${activity.subActivityType} • ${daysText}`

        const centerFacilities: string[] = []
        for (let i = 0; i < center.facilities.length; i++) {
            if (center.facilities[i].available) centerFacilities.push(center.facilities[i].name)
        }

        const centerInfoCards: any = [
            {
                icon: isAuroraConfirmationSupported ? "location-small" : "/image/icons/cult/navigate_round.png",
                subTitle: center.Address.addressLine1,
                cardAction: {
                    actionType: "NAVIGATION",
                    url: `curefit://externaldeeplink?placeUrl=${center.placeUrl}`,
                    title: "NAVIGATE"
                },
            }
        ]
        if (centerFacilities && centerFacilities.length) {
            const facilities = {
                icon: isAuroraConfirmationSupported ? "center-facilities" : "/image/icons/cult/center_facilities_round.png",
                title: "Center facilities",
                subTitle: centerFacilities.length > 0 ? centerFacilities.join(" | ") : "",
            }
            centerInfoCards.push(facilities)
        }
        if (center && center.CenterManager) {
            const centerManagerInfo = {
                icon: isAuroraConfirmationSupported ? "user" : "/image/icons/cult/center_manager_round.png",
                title: "Center Manager",
                subTitle: center.CenterManager,
            }
            centerInfoCards.push(centerManagerInfo)
        }
        const textInfoItems: TextInfoItems[] = []
        if (cultBooking.wlBookingNumber) {
            textInfoItems.push({
                title: "WAITLIST",
                tag: { value: cultBooking.waitlistNumber.toString() },
                styleProps: {
                    variant: "paragraph2Text",
                    color: "paragraphGrey"
                },
                textContainerStyleProps: {
                    style: {
                        borderWidth: 1,
                        borderColor: "#e2e4e8",
                        paddingVertical: 2,
                        paddingHorizontal: 10,
                        borderRadius: 3,
                        marginBottom: 5
                    }
                }

            })
        }
        const cultConfirmationWidget: any = {
            widgetType: "ORDER_CONFIRMATION_INFO_WIDGET",
            textInfoItems: [
                ...textInfoItems,
                {
                    title: title,
                    styleProps: {
                        variant: "header1Text",
                    },
                },
                {
                    title: className,
                    styleProps: {
                        variant: "paragraph4Text",
                        opacity: 0.6,
                        color: "primary",
                    },
                }
            ],
            orderMeta: {
                title: activity.subTitle,
                isDropDown: true,
                dropDownInfo: centerInfoCards,
                styleProps: {
                    variant: "paragraph5Text",
                }
            }
        }

        if (!isAuroraConfirmationSupported) {
            cultConfirmationWidget.imageUrl = "/image/icons/checkout/care/success_tick.png"
            cultConfirmationWidget.orderMeta.imageUrl = "/image/icons/cult/navigate_black.png"
        } else {
            cultConfirmationWidget.orderMeta.icon = "location-small"
        }

        if (cultBooking.isRescheduled) {
            cultConfirmationWidget.imageDescription = "Reschedule Successful"
            cultConfirmationWidget.imageUrl = "/image/icons/checkout/care/success_tick.png"
        }
        return cultConfirmationWidget
    }

    private getOrderConfirmationInfoWidget(activity: UserActivity, cultBooking: CultBooking, calendarEventAction: ActionWidgetView): CultCardContainerWidget {
        const cards: CultCardListItem[] = []
        let ivrProps
        const isSportCategory: boolean = cultBooking?.Class?.Workout?.isSportCategory
        if (activity.cultCafeBlock) {
            cards.push({
                title: activity.cultCafeBlock.title,
                leftIconName: "gear",
                rightIconName: "chevron-right",
                action: activity.cultCafeBlock.action,
                analyticsData: CultUtil.getCultCafeBlockAnalytics(cultBooking)
            })
        }
        if (activity.ivrConfig) {
            cards.push({
                title: isSportCategory ? "Set call reminder before session" : "Set call reminder before class",
                leftIconName: "call",
                rightIconName: "chevron-right",
                isIvr: true,
            })
            ivrProps = {
                bookingNumber: activity.ivrConfig.bookingNumber,
                isWaitlisted: activity.ivrConfig.isWaitlisted,
                isEditable: activity.ivrConfig.isEditable,
                selectedCallTime: activity.ivrConfig.selectedCallTime,
                selectedCallTimeBefore: activity.ivrConfig.selectedCallTimeBefore,
                isReminderSet: activity.ivrConfig.isReminderSet,
                slots: activity.ivrConfig.slots,
                slotSelectedAction: activity.ivrConfig.slotSelectedAction,
                notEditableAction: activity.ivrConfig.notEditableAction,
                isSportsCategory: isSportCategory,
            }
        }
        if (activity.inviteBuddy) {
            cards.push({
                title: activity.inviteBuddy.title,
                leftIconName: "buddy",
                rightIconName: "chevron-right",
                action: activity.inviteBuddy.action
            })
        }
        if (_.isEmpty(cards)) {
            return undefined
        }
        return {
            widgetType: "ORDER_CONFIRMATION_ADD_ONS_WIDGET",
            cards,
            ...ivrProps,
        }
    }

    protected abstract getActionUrl(bookingNumber: string): string

    protected abstract getActivityType(): ActivityType
}

class CultFitnessClassConfirmationView extends FitnessClassConfirmationView {
    protected getActivityType(): ActivityType {
        return "CULT_CLASS"
    }

    protected getVertical(): Vertical {
        return "CULT_FIT"
    }

    protected getActionUrl(bookingNumber: string): string {
        return ActionUtil.cultFitBooking(bookingNumber)
    }
}

class MindFitnessClassConfirmationView extends FitnessClassConfirmationView {
    protected getActivityType(): ActivityType {
        return "MIND_CLASS"
    }

    protected getVertical(): Vertical {
        return "MIND_FIT"
    }

    protected getActionUrl(bookingNumber: string): string {
        return ActionUtil.mindFitBooking(bookingNumber)
    }
}

class CultFitnessClassConfirmationViewV2 extends FitnessClassConfirmationViewV2 {
    constructor(kiosksDemandService: KiosksDemandService, cultBooking: CultBooking, recommendationView: (CultJourneyView | RecommendationView), userContext: UserContext, calendarEventAction: ActionWidgetView, isCafe: boolean, shareAction: ActionWidgetView, action: string, bannerWidget: (PageWidget | IBaseWidget)) {
        super(kiosksDemandService, cultBooking, recommendationView, userContext, calendarEventAction, isCafe, shareAction, bannerWidget)
        this.action = FLUTTER_ORDER_CONFIRMATION_ACTION
    }

    protected getActionUrl(bookingNumber: string): string {
        return ActionUtil.cultFitBooking(bookingNumber)
    }

    protected getActivityType(): ActivityType {
        return "CULT_CLASS"
    }
}

class GymFitnessProductConfirmationView extends ConfirmationViewV1 {
    constructor(userContext: UserContext, product: Product, offers: { [key: string]: OfferV2 }, goldMembership: Membership, isInternalUser: boolean) {
        super()
        this.widgets.push(this.getOrderConfirmedWidget())
        const asyncProcess = (async () => {
            this.widgets.push(await this.getProductInfoWidget(userContext, goldMembership))
        })()
        const orderOffers = this.getOrderOffers(offers, userContext, isInternalUser)
        if (orderOffers) {
            this.widgets.push(orderOffers)
        }
    }

    private getOrderOffers(offers: { [key: string]: OfferV2 }, userContext: UserContext, isInternalUser: boolean): OfferCalloutWidget {
        let offersList: OfferV2[] = _.map(offers, offer => offer)
        if (AppUtil.isNewPackOrderConformationSupported(userContext, isInternalUser)) {
            offersList = OfferUtil.segregateNoCostEMIOffers(offersList).get("OTHER_OFFERS")
        }
        const offerDataAll: any[] = AppUtil.getOffersWithAddonsAndLabels(offersList, "orderSummaryLabel")
        const offerDataItems: OfferData[] = _.map(offerDataAll, offer => {
            return { description: offer.description, tnc: offer.tnc, tncURL: offer.tncURL }
        })
        if (!offerDataItems.length) {
            return undefined
        }
        return new OfferCalloutWidget(
            "What else you got",
            offerDataItems,
            undefined,
            true,
            "",
            "BULLET"
        )
    }

    private getOrderConfirmedWidget(): OrderSuccessWidget {
        return {
            widgetType: "ORDER_SUCCESS_WIDGET",
            icon: "",
            title: "Thanks for your purchase"
        }
    }

    private async getProductInfoWidget(userContext: UserContext, gymfitMembership: Membership): Promise<GymPackInfo> {
        const tz = userContext.userProfile.timezone
        const endDate = TimeUtil.getMomentForDate(new Date(gymfitMembership.end), tz)
        const startDate = TimeUtil.getMomentForDate(new Date(gymfitMembership.start), tz)
        const durationInDays = endDate.diff(startDate, "days")
        return {
            widgetType: "GYM_PACK_INFO",
            title: `${gymfitMembership.name}`,
            description: `Valid for ${AppUtil.getMonthsFromDays(durationInDays)} months`,
            action: {
                actionType: "NAVIGATION",
                url: await GymfitUtil.getGoldMembershipDetailsUrl(gymfitMembership, userContext)
            }
        }
    }
}

class FitClubConfirmationView extends ConfirmationView {
    action: string
    title: string
    subTitle: string
    pageAction: AppsCommonAction

    constructor(offers: { [key: string]: OfferV2 }) {
        super("curefit://fitclubwelcomepage")
        let fitcashDiscount = null
        for (const offerIds in offers) {
            const offer = offers[offerIds]
            if (offer.addons[0] && offer.addons[0].addonType === "FITCASH") {
                fitcashDiscount = offer.addons[0].config.fitcashbackPercent
            }
        }
        this.title = "Welcome to Fitclub!"
        this.subTitle = fitcashDiscount ? `Free delivery, Exclusive goodies & surprises and ${fitcashDiscount}% Fitcash on every meal!` : ""
        this.vertical = "EAT_FIT"
        this.pageAction = {
            actionType: "NAVIGATION",
            title: "ORDER NOW",
            url: "curefit://eatfitclp"
        }
    }
}

class EatMarketplaceConfirmationView extends ConfirmationView {
    constructor(order: Order) {
        const action = `${ORDER_CONFIRMATION_EAT_MARKETPLACE_ACTION}?orderId=${order.orderId}`
        super(action)
        this.vertical = "EAT_FIT"
    }
}

class FoodSingleConfirmationView extends ConfirmationViewV1 {

    constructor() {
        super()
    }

    async buildView(userContext: UserContext, product: Product, order: Order, orderInfo: OrderInfo, onDemandText: string, recommendationView: (CultJourneyView | RecommendationView), deliverySlot: DeliverySlot, isFitClubApplicable: boolean, savingsReward?: Savings, myGateWidget?: IBaseWidget) {
        const tz = userContext.userProfile.timezone
        const orderProduct: OrderProduct = order.products[0]
        const slotDate: string = TimeUtil.formatDateStringInTimeZone(orderProduct.option.startDate, tz, "ddd, D MMM")
        // Temp hack until we get ETA for KIOSK in payment success call
        let numProducts = 0
        let isMealChanged = false
        order.products.forEach((orderProduct: OrderProduct) => {
            isMealChanged = isMealChanged || (orderProduct.option && orderProduct.option.isChangedMeal)
            numProducts = numProducts + orderProduct.quantity
        })

        this.body = this.getActivityDescription(product, slotDate, undefined, numProducts, isMealChanged, order.totalAmountPayable, savingsReward, 0)
        this.vertical = "EAT_FIT"
        this.orderInfo = orderInfo
        this.splashScreenView = this.getSplashScreenView()
        const productMeta = _.map(order.productSnapshots, (productSnapshot) => {
            return {
                isVeg: productSnapshot.isVeg,
                productId: productSnapshot.productId,
                categoryId: productSnapshot.categoryId,
            }
        })
        this.meta = {
            cleverTap: {
                af_revenue: order.totalAmountPayable,
                af_content_type: order.productSnapshots.map((product) => {
                    return product.categoryId
                }),
                af_content_id: order.productSnapshots.map((product) => {
                    return product.productId
                }),
                af_content: order.productSnapshots.map((product) => {
                    return product.title
                }),
            },
            productMeta: productMeta
        }

        if (AppUtil.isWidgetizedOrderConfirmationSupported(userContext)) {
            this.widgets.push(this.getOrderCellWidget(this.body, isFitClubApplicable))
        }
        if (myGateWidget) {
            this.widgets.push(myGateWidget)
        }
        if (recommendationView && !_.isEmpty(recommendationView.widgets)) {
            this.widgets.push(...recommendationView.widgets)
        }
        this.pageActions = recommendationView ? recommendationView.pageActions : undefined
        return this
    }

    private getOrderCellWidget(activity: UserActivity, isFitClubApplicable: boolean): SinglesOrderConfirmationWidget {
        return {
            widgetType: "SINGLES_ORDER_CONFIRMATION_WIDGET",
            activityType: activity.activityType,
            title: activity.title,
            subTitle: activity.subTitle,
            activityName: activity.activityName,
            action: {
                actionType: "POP_ACTION"
            },
            savings: isFitClubApplicable ? activity.savings : null,
            fitcashSavings: activity.savings ? {
                style: {
                    background: {
                        backgroundColor: "#eeebf0",
                        marginHorizontal: 0,
                        paddingVertical: 9,
                        paddingHorizontal: 15,
                        borderTopRightRadius: 0,
                        borderTopLeftRadius: 0,
                    }
                },
                data: [{
                    text: activity.savings,
                    style: {
                        color: "#000000",
                        fontSize: 12,
                        fontFamily: "BrandonText-Regular",
                        lineHeight: 16,
                    }
                }],
                ...(activity.totalFitCashEarned &&
                {
                    totalFitCashData: [
                        {
                            text: "Total Fitcash earned till now",
                            style: {
                                color: "#000000",
                                fontFamily: "BrandonText-Regular",
                                fontSize: 12
                            }
                        },
                        {
                            text: activity.totalFitCashEarned,
                            style: {
                                color: "#000000",
                                fontFamily: "BrandonText-Bold",
                                fontSize: 12
                            }
                        }
                    ]
                })
            } : undefined
        }
    }

    private getActivityDescription(product: Product, slotDate: string, slotTime: string, numProducts: number, isMealChanged: boolean, totalAmount: number, savings?: Savings, totalFitCash?: number): UserActivity {
        const userActivityId = product.productId
        const activityDate = slotDate
        let activityName = numProducts > 1 ? numProducts + " items ordered" : product.title
        if (isMealChanged && numProducts > 1) {
            activityName = "Meal changed"
            numProducts -= 1
            if (numProducts === 1) {
                activityName += " +1 item"
            } else if (numProducts > 1) {
                activityName += " +" + numProducts + " items"
            }
        }
        let savingsText = undefined
        let totalFitCashEarned
        if (savings && !isMealChanged) {
            if (savings.fitcash) {
                savingsText = `Yay! You have earned ${savings.fitcash} fitcash. It will be credited upon meal delivery`
                if (savings.freeDeliveries) {
                    savingsText += " and saved " + RUPEE_SYMBOL + savings.freeDeliveries * 25 + " on delivery"
                }
                if (totalFitCash > MINIMUM_FITCASH_TO_EARN) {
                    totalFitCashEarned = ` ${RUPEE_SYMBOL}${Math.round(totalFitCash / PAISE)}`
                }
            } else if (savings.freeDeliveries) {
                savingsText = "You have saved " + RUPEE_SYMBOL + savings.freeDeliveries * 25 + " on delivery"
            }
        }
        return {
            date: activityDate,
            activityName: activityName,
            activityType: "EATFIT_MEAL",
            status: "TODO",
            title: slotTime ? `${slotDate}, ${slotTime}` : slotDate,
            subTitle: totalAmount < 0 ? (RUPEE_SYMBOL + Math.abs(totalAmount) + " will be refunded / adjusted against your next cycle") : undefined,
            action: `curefit://today?userActivityId=${userActivityId}&activityDate=${activityDate}`,
            userActivityId: userActivityId,
            savings: savingsText,
            totalFitCashEarned
        }
    }

    private getSplashScreenView(): SplashScreenView {
        const splashScreenView: SplashScreenView = {
            message: "Order Confirmed!"
        }
        return splashScreenView
    }
}

export class FoodMarketplaceConfirmationView extends ConfirmationViewV1 {
    constructor() {
        super()
    }

    protected getSplashScreenView(): SplashScreenView {
        const splashScreenView: SplashScreenView = {
            message: "Order Placed!"
        }
        return splashScreenView
    }

    async buildView(userContext: UserContext, product: Product, order: Order, orderInfo: OrderInfo, recommendationView: (CultJourneyView | RecommendationView), myGateWidget?: IBaseWidget): Promise<ConfirmationView> {
        const tz = userContext.userProfile.timezone
        const options: DateTimeFormatOptions = { weekday: "long", year: "numeric", month: "long", day: "numeric" }
        const slotDate = new Date().toLocaleDateString("en-US", options)
        const { externalServiceFulfilmentId, listingBrand = "FOOD_MARKETPLACE" } = order?.productTypeOptions
        const { orderId } = order

        let numProducts = 0
        order.products.forEach((orderProduct: OrderProduct) => {
            numProducts = numProducts + orderProduct.quantity
        })

        this.body = this.getActivityDescription(product, slotDate, numProducts, order.totalAmountPayable)
        this.vertical = "WELLNESS"
        this.orderInfo = orderInfo
        this.splashScreenView = this.getSplashScreenView()
        const productMeta = _.map(order.productSnapshots, (productSnapshot) => {
            return {
                isVeg: !!productSnapshot.isVeg,
                productId: productSnapshot.productId,
                categoryId: productSnapshot.categoryId,
            }
        })
        this.meta = {
            cleverTap: {
                af_revenue: order.totalAmountPayable,
                af_content_type: order.productSnapshots.map((product) => {
                    return product.categoryId
                }),
                af_content_id: order.productSnapshots.map((product) => {
                    return product.productId
                }),
                af_content: order.productSnapshots.map((product) => {
                    return product.title
                }),
            },
            productMeta: productMeta
        }

        if (AppUtil.isWidgetizedOrderConfirmationSupported(userContext)) {
            this.widgets.push(this.getOrderCellWidget(this.body))
        }
        if (myGateWidget) {
            this.widgets.push(myGateWidget)
        }
        if (recommendationView && !_.isEmpty(recommendationView.widgets)) {
            this.widgets.push(...recommendationView.widgets)
        }
        this.pageActions = recommendationView ? recommendationView.pageActions :
            [{
                title: "Track Order",
                actionType: "NAVIGATION",
                url: FoodMarketplaceUtil.generateFoodMarketplaceOrderTrackingUrl(externalServiceFulfilmentId, orderId, listingBrand)
            }]
        return this
    }

    private getOrderCellWidget(activity: UserActivity): SinglesOrderConfirmationWidget {
        return {
            widgetType: "SINGLES_ORDER_CONFIRMATION_WIDGET",
            activityType: activity.activityType,
            title: activity.title,
            subTitle: activity.subTitle,
            activityName: activity.activityName,
            action: {
                actionType: "POP_ACTION"
            }
        }
    }


    protected getActivityDescription(product: Product, slotDate: string, numProducts: number, totalAmount: number): UserActivity {


        const userActivityId = product.productId
        const activityDate = slotDate
        const activityName = numProducts > 1 ? numProducts + " items ordered" : product.title

        return {
            date: activityDate,
            activityName: activityName,
            activityType: "FOOD_MARKETPLACE_MEAL",
            status: "TODO",
            title: slotDate,
            subTitle: "Waiting for the restaurant to confirm",
            action: `curefit://today?userActivityId=${userActivityId}&activityDate=${activityDate}`,
            userActivityId: userActivityId,
            savings: undefined
        }
    }
}

interface ExploreGearView {
    title: string
    icon: string
    action: ActionWidgetView
}

interface LiveClassConfirmationOrderCellParams {
    shareAction: ActionWidgetView
    videoResponse: DigitalCatalogueEntryV1
    calendarEventAction?: ActionWidgetView
    referralSupported: boolean
    buddiesInviteJoiningListWidgetV2?: BuddiesInviteJoiningListWidgetV2
    exploreGearAction?: ExploreGearView
    orderSource?: OrderSource
    notification?: any
}


interface LiveClassConfirmationViewParams extends LiveClassConfirmationOrderCellParams {
    recommendationView?: any
    leaguePostBookingInfoWidget?: LeaguePostBookingInfoWidget
}


class LiveClassConfirmationView extends ConfirmationViewV1 {
    constructor(attributes: LiveClassConfirmationViewParams, userContext: UserContext, notification: any) {
        super()
        const {
            shareAction,
            videoResponse,
            calendarEventAction,
            referralSupported = false,
            recommendationView,
            buddiesInviteJoiningListWidgetV2,
            leaguePostBookingInfoWidget,
            exploreGearAction,
            orderSource
        } = attributes

        if (AppUtil.shouldShowEnergyMeterDisabledMessage(userContext) && videoResponse.format !== "MEDITATION" && videoResponse.format !== "YOGA" && videoResponse.format !== "MIND_PODCAST") {
            this.widgets.push(LivePackUtil.getSingleBannerCarouselWidget("image/livefit/app/strip_update.png", 1035, 185))
        }

        if (AppUtil.isAuroraOrderConfirmationSupported(userContext)) {
            this.widgets.push(
                this.getOrderConfirmationWidget({
                    shareAction,
                    videoResponse,
                    calendarEventAction,
                    referralSupported,
                    buddiesInviteJoiningListWidgetV2,
                    exploreGearAction,
                    orderSource,
                    notification
                }, userContext)
            )
            this.widgets.push(
                this.getTrainerInfoCardWidget({
                    shareAction,
                    videoResponse,
                    referralSupported,
                }, userContext)
            )
            this.widgets.push(
                this.getOrderConfirmationInfoWidget({
                    shareAction,
                    videoResponse,
                    calendarEventAction,
                    referralSupported,
                    buddiesInviteJoiningListWidgetV2,
                    exploreGearAction,
                    orderSource
                }, userContext)
            )
        } else {
            this.widgets.push(
                this.getOrderCellWidget({
                    shareAction,
                    videoResponse,
                    calendarEventAction,
                    referralSupported,
                    buddiesInviteJoiningListWidgetV2,
                    exploreGearAction,
                    orderSource,
                    notification
                }, userContext)
            )
        }
        // if (referralSupported) {
        //     this.widgets.push(this.getReferralBannerWidget())
        // }
        if (AppUtil.isTVAppWithOrderSource(orderSource)) {
            this.widgets.push(this.getProTipWidget())
        }

        if (leaguePostBookingInfoWidget) {
            this.widgets.push(leaguePostBookingInfoWidget)
        }

        if (recommendationView && !_.isEmpty(recommendationView.widgets)) {
            this.widgets.push(...recommendationView.widgets)
        }

        if (AppUtil.isInternationalTLApp(userContext)) {
            this.widgets = [this.getTrainerLedOrderConfirmationWidget(userContext, shareAction, videoResponse, calendarEventAction)]
        }

        this.pageActions = recommendationView ? recommendationView.pageActions : undefined// this.widgets.push(this.getProductListWidget())
        this.body = AppUtil.isSugarFitOrUltraFitApp(userContext) ? {
            activityType: "LIVE_CLASS",
            activityName: videoResponse.title,
            date: TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, videoResponse.scheduledTimeEpoch),
            title: videoResponse.trainerName,
            userActivityId: (<any>videoResponse)._id,
        } : {
            activityType: "LIVE_CLASS",
            activityName: videoResponse.title,
            date: TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, videoResponse.scheduledTimeEpoch),
            title: videoResponse.trainerName,
            userActivityId: (<any>videoResponse)._id,
            notification
        }
    }

    private getOrderConfirmationInfoWidget(attributes: LiveClassConfirmationOrderCellParams, userContext: UserContext): any {
        const {
            shareAction,
            calendarEventAction,
            referralSupported = false,
            exploreGearAction,
        } = attributes
        const cards = []
        if (calendarEventAction) {
            cards.push({
                title: "Add to your calendar",
                leftIconName: "calendar",
                rightIconName: "chevron-right",
                action: calendarEventAction
            })
        }
        if (shareAction && !(AppUtil.isSugarFitOrUltraFitApp(userContext))) {
            cards.push({
                title: referralSupported ? "Invite friends, workout together & earn upto Rs. 1000 Fitcash" : "Invite friends and workout with them",
                action: {
                    ...shareAction,
                    analyticsData: {
                        eventKey: "widget_click",
                        eventData: {
                            source: "invite_button",
                            pageFrom: "live_booking_confirmation"
                        }
                    }
                },
                leftIconName: "buddy",
                rightIconName: "chevron-right",
            })
        }
        if (exploreGearAction) {
            cards.push({
                title: exploreGearAction.title,
                action: {
                    ...exploreGearAction.action,
                    analyticsData: {
                        eventKey: "widget_click",
                        eventData: {
                            source: "shop_workout_gear",
                            pageFrom: "live_booking_confirmation",
                            navigateTo: exploreGearAction.action.url
                        }
                    }
                },
                rightIconName: "chevron-right",
            })
        }

        return {
            widgetType: "ORDER_CONFIRMATION_ADD_ONS_WIDGET",
            cards,
        }
    }

    private getTrainerInfoCardWidget(attributes: LiveClassConfirmationOrderCellParams, userContext: UserContext): any {
        const { videoResponse } = attributes
        const subTitle = videoResponse?.title ? videoResponse?.title + " Trainer" : null
        let trainerImage = null
        if (videoResponse?.trainerImages?.mobileImage) {
            trainerImage = videoResponse?.trainerImages?.mobileImage
        } else if (videoResponse?.trainerImages?.mobileImageV2) {
            trainerImage = videoResponse?.trainerImages?.mobileImageV2
        }
        return {
            widgetType: AppUtil.isHorizontalInfoCardSupported(userContext) ? "LIST_INFO_CARD_WIDGET" : "TRAINER_INFO_CARD_WIDGET",
            title: videoResponse?.trainerName,
            imageUrl: trainerImage,
            subTitle
        }
    }

    private getOrderConfirmationWidget(attributes: LiveClassConfirmationOrderCellParams, userContext: UserContext): any {
        const { videoResponse } = attributes
        const tz = userContext.userProfile.timezone
        const d = TimeUtil.parseDateFromEpochWithTimezone(tz, videoResponse.scheduledTimeEpoch)
        const classStartTime = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, videoResponse.scheduledTimeEpoch, "h:mm A")
        const classEndTime = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, (videoResponse.scheduledTimeEpoch + videoResponse?.duration), "h:mm A")
        const titleSuffix = videoResponse?.endAt ? ` • ${classStartTime} - ${classEndTime}` : videoResponse?.duration ? ` • ${classStartTime} • ${Math.ceil(videoResponse.duration / 60000)} Mins` : ` • ${classStartTime}`
        const title = `${TimeUtil.formatDateInTimeZone(tz, d, "ddd, D MMM")} ${titleSuffix}`

        const className = `${videoResponse.title} • At Home`

        return {
            widgetType: "ORDER_CONFIRMATION_INFO_WIDGET",
            textInfoItems: [
                {
                    title: title,
                    styleProps: {
                        variant: "header1Text",
                    },
                },
                {
                    title: className,
                    styleProps: {
                        variant: "paragraph4Text",
                        opacity: 0.6,
                        color: "primary",
                    },
                }
            ]
        }
    }

    private getProTipWidget(): ProTipWidgetView {
        return {
            "widgetType": "PRO_TIP_WIDGET",
            "title": "PRO TIP",
            "subTitle": "Track your performance LIVE using your phone",
            "items": [
                {
                    "title": "Click on notification",
                    "image": "/image/tvAppIcons/bellIcon.png"
                },
                {
                    "title": "Place your phone near tv",
                    "image": "/image/tvAppIcons/mobileIcon.png"
                },
                {
                    "title": "Enjoy energy meter on your tv",
                    "image": "/image/tvAppIcons/energyIcon.png"
                }
            ]
        }
    }

    private getTrainerLedOrderConfirmationWidget(userContext: UserContext, shareAction: ActionWidgetView, videoResponse: DigitalCatalogueEntryV1, calendarInviteAction: ActionWidgetView): TLOrderConfirmationWidget {
        const trainers = [...(videoResponse?.derivedInfo?.primaryTrainersInfo || []),
        ...(videoResponse?.derivedInfo?.secondaryTrainersInfo || [])].map(trainerInfo => ({
            id: trainerInfo.instructorId,
            name: trainerInfo.name,
            image: trainerInfo.profileImageUrl,
            trainerId: trainerInfo.instructorId,
            action: AppActionUtil.trainerDetailAction(trainerInfo.instructorId)
        }))

        const time = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, videoResponse.scheduledTimeEpoch, "hh:mm a")
        const date = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, videoResponse.scheduledTimeEpoch, "ddd, MMM DD")

        const isVideoCall = LiveUtil.isInteractiveSession(videoResponse?.preferredStreamType)
        const actionName = isVideoCall ? "ADD TO CALENDAR" : "INVITE FRIENDS"
        const action = isVideoCall ? {
            ...calendarInviteAction,
            title: "Add to Calendar",
            analyticsData: {
                eventKey: "widget_click",
                eventData: {
                    source: "add_to_calendar",
                    pageFrom: "live_booking_confirmation"
                }
            }
        } : {
            ...shareAction,
            title: "Invite Friends",
            analyticsData: {
                eventKey: "widget_click",
                eventData: {
                    source: "invite_button",
                    pageFrom: "live_booking_confirmation"
                }
            }
        }

        return {
            widgetType: "TL_ORDER_CONFIRMATION_WIDGET",
            title: `${time} · ${date}`,
            itemTitle: videoResponse.title,
            itemSubtitles: [LiveUtil.getFormattedTimeString(videoResponse.duration)],
            trainers,
            actionName,
            action
        }
    }

    private getReferralBannerWidget(): BannerCarouselWidget {
        return new BannerCarouselWidget("352:151", [{
            id: "fitcash_50_referral",
            image: "/image/banners/order_confirmation_referral_banner_v6.png",
            action: {
                "title": "How it works",
                "viewType": "LINK",
                "actionType": "NAVIGATION",
                "url": "curefit://referraltncpage?source=cult_live",
                "meta": {
                    "title": "How it works"
                }
            }
        }], {
            bannerWidth: 352,
            bannerHeight: 151,
            noVerticalPadding: false,
            backgroundColor: "",
            edgeToEdge: false,
            showVideoPlayIcon: true
        }, 1, true, true, false)
    }

    protected getOrderCellWidget(attributes: LiveClassConfirmationOrderCellParams, userContext: UserContext): SinglesOrderConfirmationWidget {
        const {
            shareAction,
            videoResponse,
            calendarEventAction,
            referralSupported = false,
            buddiesInviteJoiningListWidgetV2,
            exploreGearAction,
            orderSource
        } = attributes

        let duration = videoResponse ? `${Math.round(videoResponse.duration / 60000)} MIN` : undefined
        duration = (duration && AppUtil.isTVAppWithOrderSource(orderSource)) ? duration.toLowerCase() : duration
        const actionCells: Array<ActionCell> = []
        let liveCellLeftAction
        let liveCellRightAction
        const useLiveCell = true
        if (calendarEventAction) {
            if (AppUtil.isLiveNewConfirmationPageCellSupported(userContext) && useLiveCell) {
                liveCellLeftAction = {
                    ...calendarEventAction,
                    title: "Add to your calendar",
                    iconUrl: "/image/icons/referral/calendar_transparent_large.png",
                }
            } else {
                actionCells.push({
                    title: "Add to your calendar",
                    subTitle: "Get reminders for upcoming classes",
                    action: calendarEventAction,
                    iconUrl: "/image/icons/referral/calendar_transparent_large.png",
                    removeHorizontalPadding: true,
                    hasTopDivider: true,
                    tintColor: "#000000"
                })
            }
        }
        if (shareAction) {
            if (AppUtil.isLiveNewConfirmationPageDesignSupported(userContext) && useLiveCell) {
                actionCells.push({
                    title: referralSupported ? "Invite friends, workout together & earn upto Rs. 1000 Fitcash" : "Invite friends and workout with them",
                    action: {
                        ...shareAction,
                        analyticsData: {
                            eventKey: "widget_click",
                            eventData: {
                                source: "invite_button",
                                pageFrom: "live_booking_confirmation"
                            }
                        }
                    },
                    shareCellType: "LIVE_CLASS",
                    iconUrl: AppUtil.isNewLiveOrderConfirmationIconSupported(userContext) ? CdnUtil.getCdnUrl("curefit-content/image/livefit/app/buddies_colored_transparent.png") : CdnUtil.getCdnUrl("curefit-content/image/livefit/app/buddies_colored.png"),
                    removeHorizontalPadding: true,
                    hasTopDivider: true,
                    tintColor: "#000000",
                    inlineAction: referralSupported ? {
                        actionType: "NAVIGATION",
                        url: `curefit://referraltncpage?source=cult_live&showCTA=true&contentId=${videoResponse.originalContentId}`,
                        title: "T&C"
                    } : undefined
                })
            } else {
                if (AppUtil.isLiveNewConfirmationPageCellSupported(userContext) && useLiveCell) {
                    liveCellRightAction = {
                        ...shareAction,
                        title: "Invite Friends",
                        iconUrl: "/image/icons/referral/Friends_transparent.png",
                    }
                } else {
                    actionCells.push({
                        title: "Invite your friends",
                        subTitle: "Have your friends join you for workout",
                        action: shareAction,
                        iconUrl: "/image/icons/referral/Friends_transparent.png",
                        removeHorizontalPadding: true,
                        hasTopDivider: true,
                        tintColor: "#000000"
                    })
                }
            }
        }

        // Gear cross sell
        if (exploreGearAction) {
            actionCells.push({
                title: exploreGearAction.title,
                action: {
                    ...exploreGearAction.action,
                    analyticsData: {
                        eventKey: "widget_click",
                        eventData: {
                            source: "shop_workout_gear",
                            pageFrom: "live_booking_confirmation",
                            navigateTo: exploreGearAction.action.url
                        }
                    }
                },
                iconUrl: exploreGearAction.icon,
                removeHorizontalPadding: true,
                hasTopDivider: true,
                iconContainerStyle: {
                    width: 30,
                    height: 30,
                    marginRight: 18,
                    backgroundColor: "#ffffff"
                },
                tintColor: "#000000"
            })
        }

        let widget: SinglesOrderConfirmationWidget
        if (AppUtil.isLiveNewConfirmationPageDesignSupported(userContext)) {
            widget = {
                widgetType: "SINGLES_ORDER_CONFIRMATION_WIDGET",
                activityName: "Class Booked",
                activityType: "LIVE_CLASS",
                description: videoResponse.description,
                useLiveCell,
                title: `${videoResponse.title} by ${videoResponse.trainerName}`,
                subTitle: TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, videoResponse.scheduledTimeEpoch, "MMM DD, YYYY hh:mm a"),
                actionCells: !_.isEmpty(actionCells) ? actionCells : undefined,
                liveCellLeftAction,
                liveCellBuddiesJoiningV2: buddiesInviteJoiningListWidgetV2,
                duration,
                icon: LiveUtil.getFormatIcon(videoResponse.format)
            }
        } else {
            widget = {
                widgetType: "SINGLES_ORDER_CONFIRMATION_WIDGET",
                activityName: videoResponse.title,
                description: videoResponse.description,
                activityType: "LIVE_CLASS",
                useLiveCell,
                title: videoResponse.trainerName,
                subTitle: TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, videoResponse.scheduledTimeEpoch, "MMM DD, YYYY hh:mm a"),
                actionCells: !_.isEmpty(actionCells) ? actionCells : undefined,
                liveCellLeftAction,
                liveCellRightAction,
                liveCellBuddiesJoiningV2: buddiesInviteJoiningListWidgetV2,
                duration,
                icon: LiveUtil.getFormatIcon(videoResponse.format)
            }
        }

        return widget
    }

    getProductListWidget(): ProductListWidget {
        return {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "SMALL",
            header: {
                title: "Instructions"
            },
            hideSepratorLines: true,
            items: [
                {
                    subTitle: "Wear comfortable shoes and keep a workout mat, a water bottle and a napkin handy. ",
                    icon: "/image/icons/howItWorks/cast.png"
                },
                {
                    subTitle: "Have clear space around you to perform exercises",
                    icon: "/image/icons/howItWorks/cast.png"
                },
                {
                    subTitle: "Make sure that the device you’re joining the LIVE class on is fully charged",
                    icon: "/image/icons/howItWorks/time.png"
                },
                {
                    subTitle: "Ensure you have good internet connectivity",
                    icon: "/image/icons/howItWorks/info.png"
                }
            ]

        }
    }
}

class EatLiveClassConfirmationView extends ConfirmationViewV1 {

    constructor(userContext: UserContext, videoResponse: DigitalCatalogueEntryV1, calendarEventAction?: ActionWidgetView, recommendationView?: any) {
        super()
        this.widgets.push(this.getOrderCellWidget(userContext, videoResponse, calendarEventAction))
        if (recommendationView && !_.isEmpty(recommendationView.widgets)) {
            this.widgets.push(...recommendationView.widgets)
        }
        this.pageActions = recommendationView ? recommendationView.pageActions : undefined

        this.body = {
            activityType: "LIVE_CLASS",
            activityName: videoResponse.title,
            date: TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, videoResponse.scheduledTimeEpoch),
            title: videoResponse.trainerName,
            userActivityId: (<any>videoResponse)._id,
        }
    }

    private getOrderCellWidget(userContext: UserContext, videoResponse: DigitalCatalogueEntryV1, calendarEventAction?: ActionWidgetView): SinglesOrderConfirmationWidget {
        const actionCells = []
        if (calendarEventAction) {
            actionCells.push({
                title: "Add to your calendar",
                subTitle: "Get reminders for upcoming classes",
                action: calendarEventAction,
                iconUrl: "/image/icons/referral/calendar_transparent_large.png",
                removeHorizontalPadding: true,
                hasTopDivider: true,
                tintColor: "#000000"
            })
        }
        return {
            widgetType: "SINGLES_ORDER_CONFIRMATION_WIDGET",
            activityName: videoResponse.title,
            activityType: "LIVE_CLASS",
            title: videoResponse.trainerName,
            subTitle: TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, videoResponse.scheduledTimeEpoch, "MMM DD, YYYY hh:mm a"),
            actionCells,
            useLiveCell: true,
        }
    }
}

class TataNeuConfirmationView extends ConfirmationViewV1 {
    constructor(neuPassClickAction: NeuPassClickAction, userContext: UserContext, isNeuPassConsentPending: boolean) {
        super(`${ORDER_CONFIRMATION_V1_ACTION}?vertical=TATA_NEU&platform=${userContext.sessionInfo.osName}&subscriptionType=${"non-recurring"}&orderType=LIVEFIT_PACK`)
        if (!AppUtil.isWeb(userContext)) {
            this.widgets.push(this.getNeuActivatedWidget(isNeuPassConsentPending))
        }
        // const newPassClickWidget = this.getMyNeuAction(userContext, neuPassClickAction)
        // if (newPassClickWidget) {
        //     this.widgets.push(newPassClickWidget)
        // }
        this.widgets.push(this.getTataNeuBenefits(userContext))
        let title = "NeuPass is active"
        if (isNeuPassConsentPending) {
            title = "NeuPass benefits are active"
        }
        this.header = {
            title: title,
            subTitle: "12 months"
        }

        this.body = {
            activityType: "LIVE_PACK_BOOK",
            activityName: "",
            date: TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, new Date(), "DD MMM, YYYY"),
            title: "Neupass",
            userActivityId: "",
        }
    }

    getLiveMembershipBenefits(): ProductListWidget {
        const header: Header = {
            title: "You also get",
            titleProps: {
                style: {
                    fontSize: 18,
                    fontWeight: "700",
                },
            },
        }
        const commonStyle = {
            icon: "image/tata/check-box.png",
            subTitleFontSize: 14,
            layoutProps: {
                iconStyle: {
                    width: 20,
                    height: 20,
                    backgroundColor: "transparent",
                },
                tintColor: "black",
            },
        }
        const infoCards: InfoCard[] = [
            {
                subTitle: "Minimum 5% NeuCoins on all purchases",
                ...commonStyle
            },
            {
                subTitle: "Privilages across brands",
                ...commonStyle
            },
            {
                subTitle: "Free delivery and more",
                ...commonStyle
            },
        ]
        const widget = new ProductListWidget("SMALL", header, infoCards)
        widget.hideSepratorLines = true
        return widget
    }

    getTataNeuBenefits(userContext: UserContext): ProductListWidget {
        const isWeb = AppUtil.isWeb(userContext)

        const header: Header = {
            title: "How it works",
            titleProps: {
                style: {
                    fontSize: 18,
                    fontWeight: "700",
                },
            },
        }

        const commonStyle = {
            icon: isWeb ? "image/icons/black-icon-circle-2.jpeg" : "image/tata/check-box.png",
            subTitleFontSize: 14,
            layoutProps: {
                iconStyle: {
                    width: 20,
                    height: 20,
                    backgroundColor: "transparent",
                },
                tintColor: "black",
            },
        }
        const infoCards: InfoCard[] = [
            {
                subTitle: "Minimum 0.25% NeuCoins on all purchases",
                ...commonStyle
            },
            {
                subTitle: "Free delivery and more",
                ...commonStyle
            },
        ]
        const widget = new ProductListWidget("SMALL", header, infoCards)
        widget.hideSepratorLines = true
        widget.layoutProps = {
            marginTop: 20
        }
        return widget
    }

    getNeuActivatedWidget(isNeuPassConsentPending: boolean): any {
        let title = "NeuPass is active"
        if (isNeuPassConsentPending) {
            title = "NeuPass benefits are active"
        }
        return {
            widgetType: "ORDER_SUCCESS_WIDGET",
            icon: "image/tata/check.png",
            title: title,
            subTitle: "12 months",
        }
    }

    getMyNeuAction(userContext: UserContext, neuPassClickAction: NeuPassClickAction): any {
        const isWeb = AppUtil.isWeb(userContext)

        if (neuPassClickAction.action !== "NEUPASS_ACCOUNT") {
            return
        }

        const action: AppsCommonAction = {
            actionType: "NAVIGATION",
            url: `curefit://webview?uri=${encodeURIComponent(neuPassClickAction?.url)}&theme=dark&title=${encodeURIComponent("My NeuPass")}`,
        }

        if (isWeb) {
            return new NavigationCardWidget("My NeuPass", "", action, true, undefined, undefined, undefined, undefined, undefined, "TATA_NEU_LOGO")
        }
        return {
            widgetType: "LIST_INFO_CARD_WIDGET",
            title: "My NeuPass",
            imageUrl:
                "image/tata/tata-neu.png",
            imageSize: 25,
            layoutProps: {
                spacing: {
                    top: 15,
                    force: true,
                },
            },
            action
        }
    }

}

class LivePackConfirmationView extends ConfirmationViewV1 {
    constructor(userContext: UserContext, order: Order, liveMembership: Membership, product: Product, params: ConfirmationRequestParams, notification: any, recoWidgets?: WidgetWithMetric[], onboardingBanner?: any, coachBanner?: any) {
        super(`${ORDER_CONFIRMATION_V1_ACTION}?vertical=LIVE_FIT&productId=${product.productId}&platform=${userContext.sessionInfo.osName}&subscriptionType=${product.subscriptionOptions.autoRenewing ? "recurring" : "non-recurring"}&orderType=LIVEFIT_PACK`)
        this.widgets.push(this.getOrderCellWidget(userContext, order, product, liveMembership, params))
        if (onboardingBanner) {
            this.widgets.push(onboardingBanner)
        }
        if (coachBanner) {
            this.widgets.push(coachBanner)
        }
        if (!_.isEmpty(recoWidgets)) {
            this.widgets.push(...recoWidgets)
        }

        const titlePrefix: string = LiveUtil.getCultLiveSubscriptionTitle(userContext)
        this.body = {
            activityType: "LIVE_PACK_BOOK",
            activityName: "",
            date: TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, new Date(order.createdDate), "DD MMM, YYYY"),
            title: titlePrefix + " - " + product.title,
            userActivityId: product.productId,
            notification
        }
    }

    protected getOrderCellWidget(userContext: UserContext, order: Order, product: Product, liveMembership: Membership, params: ConfirmationRequestParams): SinglesOrderConfirmationWidget | TLOrderConfirmationWidget {
        const liveCellLeftAction = LiveUtil.getLiveClassBookingPageAction(userContext, "FITNESS", "orderConfirmation")
        liveCellLeftAction.title = "BOOK LIVE CLASS"
        liveCellLeftAction.meta = {
            subTitle: "Enjoy unlimited access to LIVE classes & on-demand health content",
            iconUrl: "/image/icons/referral/Friends_transparent.png"
        }
        let subTitle: string
        if (params?.freeTrialExpiryDateMs) {
            subTitle = "Free trial activated. Billing starts from " + TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, Number(params.freeTrialExpiryDateMs), "DD MMM, YYYY")
        } else {
            subTitle = "Starts " + TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, liveMembership.start, "DD MMM, YYYY")
        }
        const titlePrefix: string = LiveUtil.getCultLiveSubscriptionTitle(userContext)

        if (AppUtil.isInternationalTLApp(userContext)) {
            return {
                widgetType: "TL_ORDER_CONFIRMATION_WIDGET",
                title: "Pack Purchased",
                itemTitle: titlePrefix + " - " + product.title,
                itemSubtitles: [subTitle],
                trainers: [],
            }
        }

        return {
            widgetType: "SINGLES_ORDER_CONFIRMATION_WIDGET",
            activityName: "",
            activityType: "LIVE_PACK_BOOK",
            useLiveCell: true,
            title: titlePrefix + " - " + product.title,
            subTitle: subTitle,
            actionCells: undefined,
            liveCellLeftAction: liveCellLeftAction
        }
    }
}

class LivePtBookingConfirmationView extends ConfirmationViewV1 {
    constructor(userContext: UserContext, bookingDetail: BookingDetail, consultationProduct: ConsultationProduct, isLivePTOnboardingComplete: boolean, exploreGearAction: ExploreGearView, isLiveSGTNewBookingSupported?: boolean) {
        super()
        const isLivePTProduct = CareUtil.isLivePTProduct(consultationProduct)
        const isLiveSGTProduct = CareUtil.isLiveSGTProduct(consultationProduct)
        this.widgets.push(this.getOrderCellWidget(userContext, bookingDetail, consultationProduct, isLivePTOnboardingComplete, exploreGearAction))
        if (AppUtil.isNewNoteWidgetSuported(userContext)) {
            this.widgets.push(CareUtil.getLivePTSessionNoteListWidget(bookingDetail, userContext, "#ffffff", isLivePTProduct ? 20 : 5, isLiveSGTProduct))
        } else {
            this.widgets.push(CareUtil.getLivePTCancellationCutoffWidget(bookingDetail, userContext.userProfile.timezone))
        }
        // if (isLiveSGTProduct) {
        //     const banners: Banner[] = [{
        //         id: "/image/icons/cult/gx_confirmation_page_banner_2.png",
        //         image: "/image/icons/cult/gx_confirmation_page_banner_2.png",
        //         action: {
        //             actionType: "NAVIGATION",
        //             url: CultUtil.getSGTUpgradeCommUrl()
        //         }
        //     }]
        //     const sgtUpgradeBannerWidget = new BannerCarouselWidget("335:183", banners, {
        //         bannerWidth: 335,
        //         bannerHeight: 183,
        //         v2: true,
        //         noVerticalPadding: true,
        //         backgroundColor: "#ffffff",
        //         noAutoPlay: true,
        //     }, 4, false, false)
        //     this.widgets.push(sgtUpgradeBannerWidget)
        // }
        if (isLiveSGTProduct && isLiveSGTNewBookingSupported) {
            this.pageActions = [
                {
                    actionType: "NAVIGATION",
                    title: "Book Another Session",
                    url: `curefit://sgtbookingpage/parentBookingId=${bookingDetail.booking.parentBookingId}`
                }
            ]
        } else {
            this.pageActions = this.getPageActions(bookingDetail, consultationProduct.productId)
        }
    }


    private getOrderCellWidget(userContext: UserContext, bookingDetail: BookingDetail, consultationProduct: ConsultationProduct, isLivePTOnboardingComplete: boolean, exploreGearAction?: ExploreGearView): SinglesOrderConfirmationWidget {
        const isLiveSGTProduct = CareUtil.isLiveSGTProduct(consultationProduct)
        const trainerName = isLiveSGTProduct ? undefined : bookingDetail.consultationOrderResponse.doctor.name
        const workoutName = bookingDetail.consultationOrderResponse.consultationProduct.description
        const startTimeString = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, bookingDetail.consultationOrderResponse.startTime, "ddd D MMM, hh:mm a")
        const vertical = CareUtil.getVerticalForConsultation(consultationProduct?.doctorType)
        const actionUrl = ActionUtil.teleconsultationSingle(userContext, bookingDetail.booking.productCode, consultationProduct.urlPath, bookingDetail.booking.id.toString(), undefined, vertical)
        let tagView
        let focusAreaName = ""
        if (isLiveSGTProduct) {
            tagView = {
                bgColor: "#888e9e",
                icon: "/image/icons/livept/video_white.png",
                tagText: "ONLINE GROUP CLASS"
            }
            if (!_.isEmpty(bookingDetail.consultationOrderResponse.metadata)) {
                focusAreaName = bookingDetail.consultationOrderResponse.metadata.focusAreaName || ""
            }
        }
        const action: AppsCommonAction = {
            actionType: "NAVIGATION",
            url: actionUrl,
            analyticsData: {
                actionType: "SET_LIVE_PT_GOAL",
                workoutName: workoutName
            }
        }
        const actionCells: ActionCell[] = []
        if (!isLivePTOnboardingComplete) {
            actionCells.push({
                title: "Set your goal for personalized workout recommendations",
                iconUrl: "/image/icons/cult/pt_goal.png",
                action: {
                    actionType: "NAVIGATION",
                    url: isLiveSGTProduct ? "curefit://userform?formId=LIVE_SGT_ONBOARDING" : "curefit://userform?formId=LIVE_PT_ONBOARDING_2",
                    analyticsData: {
                        actionType: "SET_LIVE_PT_GOAL",
                        workoutName: workoutName
                    }
                },
                hasTopDivider: true,
                removeHorizontalPadding: true,
                iconContainerStyle: {
                    width: 30,
                    height: 30,
                    marginRight: 18,
                    backgroundColor: "#ffffff"
                }
            })
        }

        // Gear cross sell
        if (exploreGearAction) {
            actionCells.push({
                title: exploreGearAction.title,
                action: {
                    ...exploreGearAction.action,
                    analyticsData: {
                        eventKey: "widget_click",
                        eventData: {
                            source: "shop_workout_gear",
                            pageFrom: "pt_booking_confirmation",
                            workoutName,
                            navigateTo: exploreGearAction.action.url
                        }
                    }
                },
                iconUrl: exploreGearAction.icon,
                hasTopDivider: true,
                removeHorizontalPadding: true,
                iconContainerStyle: {
                    width: 30,
                    height: 30,
                    marginRight: 18,
                    backgroundColor: "#ffffff"
                },
                tintColor: "#000000"
            })
        }

        return {
            widgetType: "SINGLES_ORDER_CONFIRMATION_WIDGET",
            activityName: workoutName,
            title: isLiveSGTProduct ? focusAreaName : `${startTimeString}`,
            activityType: "BOOK_CONSULTATION",
            subTitle: isLiveSGTProduct ? `${startTimeString}` : `with ${trainerName}`,
            action: action,
            actionCells: !_.isEmpty(actionCells) ? actionCells : undefined,
            tagView
        }
    }

    private getPageActions(bookingDetail: BookingDetail, productId: string): AppsCommonAction[] {
        return [{
            actionType: "NAVIGATION",
            title: "Book Another Session",
            url: ActionUtil.selectCareDateV1(productId, undefined, bookingDetail.booking.parentBookingId)
        }]
    }
}

export class TransformPackConfirmationView extends ConfirmationViewV1 {
    constructor(product: any, bookingDetail: BookingDetail, coachInfo?: TransformConfirmationCoachInfo, userContext?: UserContext) {
        super(TRANSFORM_ORDER_CONFIRMATION_V1_ACTION)
        this.widgets = []
        const action: AppsCommonAction = {
            actionType: "NAVIGATION",
            url: ActionUtil.carefitbundle(product.productId, product.subCategoryCode, bookingDetail.booking.id.toString(), undefined, undefined, undefined, undefined, undefined, userContext.sessionInfo.userAgent)
        }
        const isTransformPlusProduct: boolean = product.subCategoryCode === TRANSFORM_SUB_CATEGORY_CODE_TYPE.TRANSFORM_PLUS
        const isTransformEliteBootcampProduct: boolean = !_.isNil(product.infoSection?.productTypeMeta?.isBootcampPlusElite) && product.infoSection?.productTypeMeta?.isBootcampPlusElite
        const isTransformBootcampOnlyProduct: boolean = product.subCategoryCode === TRANSFORM_SUB_CATEGORY_CODE_TYPE.BOOTCAMP
        const isTransformProduct: boolean = product.subCategoryCode === TRANSFORM_SUB_CATEGORY_CODE_TYPE.TRANSFORM
        const isTransformBootcampProduct: boolean = isTransformEliteBootcampProduct || isTransformBootcampOnlyProduct
        const isTransformLiftProduct: boolean = product.subCategoryCode === TRANSFORM_SUB_CATEGORY_CODE_TYPE.LIFT
        const isTransformNCProduct: boolean = product.subCategoryCode === TRANSFORM_SUB_CATEGORY_CODE_TYPE.TRANSFORM_NC
        const isTransformFCProduct: boolean = product.subCategoryCode === TRANSFORM_SUB_CATEGORY_CODE_TYPE.TRANSFORM_FC
        const isTransformPTProduct: boolean = product.subCategoryCode === TRANSFORM_SUB_CATEGORY_CODE_TYPE.TRANSFORM_PT
        const isCongoSupported = AppUtil.isCongoWidgetSupportedVersion(userContext) && (isTransformPlusProduct || isTransformProduct || isTransformNCProduct || isTransformPTProduct || isTransformFCProduct)

        const summary = product.title
        const durationInMonths = isTransformLiftProduct || isTransformBootcampOnlyProduct ? product.duration + " days" : (_.get(product, "duration") ? `${Math.floor(product.duration / 30)} months` : "")
        const numberOfSessions = `${product.productSpec?.numberOfSessions} sessions`
        const endDate = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, bookingDetail.bundleOrderResponse.expiryTimeEpoch, "DD-MM-YYYY")
        const subtitle = `${durationInMonths} till ${endDate}`

        const getCongoWidgetText = (subCategoryCode: TRANSFORM_SUB_CATEGORY_CODE_TYPE) => {
            switch (subCategoryCode) {
                case TRANSFORM_SUB_CATEGORY_CODE_TYPE.TRANSFORM_PLUS:
                case TRANSFORM_SUB_CATEGORY_CODE_TYPE.TRANSFORM_NC:
                case TRANSFORM_SUB_CATEGORY_CODE_TYPE.TRANSFORM_PT:
                case TRANSFORM_SUB_CATEGORY_CODE_TYPE.TRANSFORM_FC:
                    if (isTransformPTProduct) {
                        return `You have taken the positive step towards healthy lifestyle for the next ${numberOfSessions}!`
                    } else if (isTransformFCProduct || isTransformNCProduct) {
                        return `You have taken the positive step towards healthy lifestyle for the next ${durationInMonths}!`
                    }
                    return `You have taken the first step towards Transform PLUS for the next ${durationInMonths}!`
                case TRANSFORM_SUB_CATEGORY_CODE_TYPE.TRANSFORM:
                    return `You have taken the first step towards Transform for the next ${durationInMonths}!`
                default:
                    return `You have taken the positive step towards healthy lifestyle for the next ${durationInMonths}!`
            }
        }

        if (isCongoSupported) {
            const congoWidgetText = getCongoWidgetText(product.subCategoryCode)
            this.pageAction = {
                url: COMBINED_WEIGHT_LOSS_CLP_DEEPLINK,
                title: "Let's get Started",
                actionType: "NAVIGATION"
            }
            this.widgets.push(new CongoWidget("Congratulations!", congoWidgetText, null))
        } else {
            this.widgets.push(new TransformConfirmationSummaryWidget(summary, product.title, subtitle, null, action))
        }

        if (isTransformBootcampProduct || isTransformLiftProduct) {
            this.widgets.push(TransformUtil.getBootcampBookingConfirmationWidget(product))
            if (isTransformLiftProduct) {
                this.pageAction = {
                    url: "curefit://lift",
                    title: "DONE",
                    actionType: "RESET_NAVIGATION"
                }
            } else {
                this.pageAction = {
                    url: COMBINED_WEIGHT_LOSS_CLP_DEEPLINK,
                    title: "DONE",
                    actionType: "RESET_NAVIGATION"
                }
            }
        } else if (!coachInfo && !isCongoSupported) {
            this.widgets.push(new CoachPreferenceWidget(bookingDetail.booking.id.toString(), userContext, product.subCategoryCode))
        }
        if (isTransformBootcampProduct && AppUtil.isBootcampReferralSupported(userContext)) {
            const referralAction: Action = {
                title: "INVITE NOW",
                actionType: "NAVIGATION",
                url: "curefit://tf_referral"
            }
            this.widgets.push(new BannerCardWidgetView("Workout with a buddy!", "Achieve weight loss goals with friends. Gift them ₹500 OFF! Get a ₹1000 voucher when they join.", "image/transform/referral_card_bg.png", "image/transform/refer_and_earn1.gif", referralAction))
        }
        this.vertical = "TRANSFORM"
        this.header = {
            title: "Pack Purchased"
        }
    }
}


export class MindTherapyPackFlutterConfirmationView extends ConfirmationView {
    constructor(userContext: UserContext, product: DiagnosticProduct, bookingDetail: BookingDetail, bundleSellableProduct: BundleSessionSellableProduct) {
        super(CARE_AURORA_ORDER_CONFIRMATION_V1_ACTION)
        this.widgets = []
        const patient: Patient = bookingDetail.bundleOrderResponse.patient
        const action: AppsCommonAction = {
            actionType: "NAVIGATION",
            url: ActionUtil.carefitbundle(product.productId, product.subCategoryCode, bookingDetail.booking.id.toString(), undefined, undefined, undefined, undefined, undefined, userContext.sessionInfo.userAgent, undefined, userContext.sessionInfo.appSource)
        }
        const summary = `Thank you for your purchase\nPlease proceed to schedule session`
        const age = !_.isEmpty(patient) && !_.isEmpty(patient.formattedAge) ? patient.formattedAge.numOfYears : undefined
        const subtitle = age && age !== -1 ? `${patient.name} | ${age} years old` : `${patient.name}`
        const { packConfirmationInstruction } = CareUtil.getPackContent(bundleSellableProduct)
        this.widgets.push(new HCUBundleConfirmationSummaryWidget(summary, product.title, subtitle, action, "/image/icons/checkout/care/success_tick.png"))
        this.widgets.push(CareUtil.getHowItWorksWidget(packConfirmationInstruction, userContext.sessionInfo.userAgent))
        this.vertical = "CARE_FIT"
        this.header = {
            title: "Pack Purchased"
        }
    }
}

export class LivePTPackConfirmationView extends ConfirmationViewV1 {
    constructor(userContext: UserContext, product: DiagnosticProduct, bookingDetail: BookingDetail, isLivePTOnboardingComplete: boolean, offerAddonWidget: OfferAddonWidget, pageActions?: AppsCommonAction[]) {
        super()
        this.widgets.push(this.getPackOrderCellWidget(userContext, product, bookingDetail, isLivePTOnboardingComplete))
        if (!_.isEmpty(offerAddonWidget)) {
            offerAddonWidget.containerStyle = {
                paddingLeft: 25
            }
            offerAddonWidget.imageContainerStyle = {
                flex: 0.12
            }
            offerAddonWidget.bulletViewStyle = {
                flex: 0.12
            }
            this.widgets.push(offerAddonWidget)
        }
        const isLiveSGT = product.subCategoryCode === "LIVE_SGT"
        if (isLiveSGT) {
            this.widgets.push(CareUtil.getLiveSGTPackHowItWorksWidget(userContext))
        } else {
            this.widgets.push(CareUtil.getLivePTPackHowItWorksWidget(userContext))
        }
        if (pageActions) {
            this.pageActions = pageActions
        }
    }

    private getPackOrderCellWidget(userContext: UserContext, product: DiagnosticProduct, bookingDetail: BookingDetail, isLivePTOnboardingComplete: boolean): SinglesOrderConfirmationWidget {
        const isLiveSGT = product.subCategoryCode === "LIVE_SGT"
        const action: AppsCommonAction = {
            actionType: "NAVIGATION",
            url: ActionUtil.carefitbundle(product.productId, product.subCategoryCode, bookingDetail.booking.id.toString())
        }
        let actionCell: ActionCell
        if (!isLivePTOnboardingComplete) {
            actionCell = {
                title: "Select your fitness goal to get daily workout recommendations",
                iconUrl: "/image/icons/cult/pt_goal.png",
                action: {
                    actionType: "NAVIGATION",
                    url: isLiveSGT ? "curefit://userform?formId=LIVE_SGT_ONBOARDING" : "curefit://userform?formId=LIVE_PT_ONBOARDING_2"
                },
                hasTopDivider: true,
                removeHorizontalPadding: true,
                iconContainerStyle: {
                    width: 30,
                    height: 30,
                    marginRight: 18,
                    backgroundColor: "#ffffff"
                }
            }
        }
        const bundleProductSpec = bookingDetail.bundleOrderResponse.sellableProduct.productSpec
        return {
            widgetType: "SINGLES_ORDER_CONFIRMATION_WIDGET",
            activityName: isLiveSGT ? `${product.title}` : "Online Personal Training",
            activityType: "DIAGNOSTICS",
            title: isLiveSGT ? `${Math.floor(product.duration / 30)} months pack` : `${bundleProductSpec.numberOfSessions} sessions`,
            subTitle: ``, // TODO - start date for the pack
            action: action,
            actionCells: actionCell ? [actionCell] : undefined
        }
    }
}

export default OrderConfirmationViewBuilderV1
