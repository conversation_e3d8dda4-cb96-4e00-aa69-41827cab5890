import { inject, injectable } from "inversify"
import { DEFAULT_CACHE_REFRESH_TIME_IN_SECONDS, InMemoryCacheService } from "@curefit/memory-cache"
import { IPageConfigReadOnlyDao, IPageConfigReadWriteDao, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import { ProductType } from "@curefit/product-common"
import { Banner } from "../page/PageWidgets"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

@injectable()
class BookingConfirmationPageConfig extends InMemoryCacheService<any> {

    constructor(
        @inject(PAGE_CONFIG_TYPES.PageConfigReadOnlySecondaryDao) protected pageConfigDao: IPageConfigReadOnlyDao,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger, 35 * 60)
        this.load("BookingConfirmationPageConfig")
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    loadData(): Promise<any> {
        return this.pageConfigDao.findOne({ pageId: { $eq: "BookingConfirmationPageConfig" } }).then(pageConfig => {
            this.data = pageConfig.data
            return this.data
        })
    }
    public data: any

    public getJourneyContextData(contextId: string): any {
        return this.data[contextId]
    }

    public cultBookingBannerData(): { widgetId: string, pageId: string } {
        return this.data["CULT_PROMOTIONAL_BANNER"]
    }
    public mindBookingBannerData(): { widgetId: string, pageId: string } {
        return this.data["MIND_PROMOTIONAL_BANNER"]
    }
    public eatSinglesBookingBannerData(): {widgetId: string, pageId: string } {
        return this.data["EAT_SINGLES_PROMOTIONAL_BANNER"]
    }
    public bannerConfigData(productType: ProductType): { templateId: string, banners: Banner[] } {
        if (productType === "FITNESS") {
            return this.data["CULT_PROMOTIONAL_BANNER"]
        }
        else if (productType === "MIND") {
            return this.data["MIND_PROMOTIONAL_BANNER"]
        }
    }
    public recommendationConfigData(productType: ProductType): {pageId: string} {
        if (productType === "FITNESS") {
            return this.data["CULT_RECOMMENDATION_PAGE"]
        } else if (productType === "MIND") {
            return this.data["MIND_RECOMMENDATION_PAGE"]
        } else if (productType === "FOOD") {
            return this.data["EAT_SINGLES_RECOMMENDATION_PAGE"]
        } else if (productType === "LIVE_FITNESS") {
            return this.data["LIVE_BOOKING_CONFIRMATION_PAGE"]
        } else if (productType === "CONSULTATION") {
            return this.data["CARE_CONSULATION_RECOMMENDATION_PAGE"]
        } else if (productType === "BUNDLE") {
            return this.data["CARE_BUNDLE_RECOMMENDATION_PAGE"]
        } else if (productType === "DIAGNOSTICS") {
            return this.data["CARE_DIAGNOSTIC_RECOMMENDATION_PAGE"]
        }
    }

    public packRecommendationConfigData(productType: ProductType): { pageId: string } {
        if (productType === "FITNESS") {
            return this.data["CULT_PACK_BOOKING_CONFIRMATION_RECO_PAGE"]
        } else if (productType === "LIVE_FITNESS" || productType === "CF_LIVE") {
            return this.data["LIVE_PACK_BOOKING_CONFIRMATION_RECO_PAGE"]
        }
    }
}

export default BookingConfirmationPageConfig
