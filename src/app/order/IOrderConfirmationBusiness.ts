import { CultBooking, CultSummary } from "@curefit/cult-common"
import { Action, WidgetView } from "../common/views/WidgetView"
import { PageWidget } from "../page/Page"
import { ProductType } from "@curefit/product-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { ConfirmationRequestParams } from "./BaseOrderConfirmationViewBuilder"
import { IBaseWidget } from "@curefit/vm-models"

export interface CultJourneyView {
    widgets?: (WidgetView | PageWidget | IBaseWidget)[]
    pageActions?: Action[]
}

interface IOrderConfirmationBusiness {
    getCultJourneyWidgetView(productType: ProductType, cultBooking: CultBooking, requestParams: ConfirmationRequestParams, userAgent: UserAgent): Promise<CultJourneyView>,
}

export default IOrderConfirmationBusiness
