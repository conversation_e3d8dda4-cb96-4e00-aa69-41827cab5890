import { inject, injectable } from "inversify"
import IOrderConfirmationBusiness, { CultJourneyView } from "./IOrderConfirmationBusiness"
import { CultBooking, CultSummary } from "@curefit/cult-common"
import { Action, Header, InfoCard, ProductListWidget, WidgetView, DisplayUserDetailWidget } from "../common/views/WidgetView"
import { PageWidget } from "../page/Page"
import { BannerCarouselWidget, PackProgress, PackProgressListWidget } from "../page/PageWidgets"
import * as _ from "lodash"
import { CatalogueServiceV2Utilities, ICatalogueServicePMS } from "@curefit/catalog-client"
import { ICatalogueService, CATALOG_CLIENT_TYPES } from "@curefit/catalog-client"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import BookingConfirmationPageConfig from "./BookingConfirmationPageConfig"
import { UrlPathBuilder } from "@curefit/product-common"
import { OfferingItem } from "@curefit/product-common"
import { ProductType } from "@curefit/product-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { ConfirmationRequestParams } from "./BaseOrderConfirmationViewBuilder"
import CultUtil, { CULTSCORE_WORKOUT_IDS, SWIMMING_WORKOUT_IDS, YOGA_WORKOUT_ID, FIRST_CLASS_PRE_WORKOUT_THUMBNAIL, FIRST_CLASS_PRE_WORKOUT_VIDEO } from "../util/CultUtil"
import { IPageService, CardVideoItem, CardVideoWidget } from "@curefit/vm-models"
import { UserContext } from "@curefit/userinfo-common"
import { ActionUtil } from "@curefit/base-utils"
import AppUtil from "../util/AppUtil"
import { CacheHelper } from "../util/CacheHelper"
import { userInfo } from "os"
import { HamletBusiness, HAMLET_TYPES } from "@curefit/hamlet-node-sdk"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"
import CatalogueServiceUtilities from "../util/CatalogueServiceUtilities"
import { BASE_TYPES, Logger } from "@curefit/base"
const clone = require("clone")

export const CultJourneyContextIdsByPriority = ["CULT_PACK_EXPIRING", "MIND_PACK_EXPIRING", "CULT_BOOKING_ONBOARDING", "MIND_BOOKING_ONBOARDING"]
const CULTSCORE_CONTEXT_ID = "CULTSCORE_CLASS_BOOKING"
const WAITLIST_BOOKING_CONTEXT_ID = "WAITLIST_BOOKING"
const CULT_JUNIOR_ONBOARDING_CONTEXT_ID = "CULT_JUNIOR_BOOKING_ONBOARDING"
const SWIMMING_CONTEXT_ID = "CULT_SWIMMING_BOOKING_ONBOARDING"

@injectable()
class OrderConfirmationBusiness implements IOrderConfirmationBusiness {

    constructor(
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReaderPMS) private catalogueServicePMS: ICatalogueServicePMS,
        @inject(CUREFIT_API_TYPES.PageService) public pageService: IPageService,
        @inject(CUREFIT_API_TYPES.BookingConfirmationPageConfig) private bookingConfirmationConfig: BookingConfirmationPageConfig,
        @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness,
        @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
        @inject(BASE_TYPES.ILogger) protected logger: Logger,
    ) {
    }

    async getPromotionalBannerView(productType: ProductType, userContext: UserContext): Promise<PageWidget> {
        const bannerConfig = this.bookingConfirmationConfig.bannerConfigData(productType)
        return this.getBannerWidget(bannerConfig, userContext)
    }

    async getBannerWidget(bannerConfig: { templateId: string, banners: any[] }, userContext: UserContext): Promise<BannerCarouselWidget> {
        if (!_.isEmpty(bannerConfig)) {
            const widgetTemplate = await this.pageService.getWidgetTemplate(bannerConfig.templateId)
            let layoutProps
            if (!_.isEmpty(widgetTemplate)) {
                const userAgent = userContext.sessionInfo.userAgent
                if (userAgent === "APP" || userAgent === "MBROWSER") {
                    layoutProps = widgetTemplate.templateData.app
                } else {
                    layoutProps = widgetTemplate.templateData.web
                }
                return new BannerCarouselWidget(
                    layoutProps.bannerRatio,
                    bannerConfig.banners,
                    layoutProps
                )
            }
        }
    }

    async getCultJourneyWidgetView(productType: ProductType, cultBooking: CultBooking, requestParams: ConfirmationRequestParams, userAgent: UserAgent): Promise<CultJourneyView> {
        // special handling for cult score class
        const cultAndMindSummary = await this.userCache.getCultSummary(requestParams.userContext.userProfile.userId)
        // if (CULTSCORE_WORKOUT_IDS.indexOf(cultBooking.Class.Workout.id) > -1) {
        //     const config = this.bookingConfirmationConfig.getJourneyContextData(CULTSCORE_CONTEXT_ID)
        //     const widget = this.getCultInitialClassInfoWidget(config)
        //     widget.analyticsData = {
        //         recommendationCategory: CULTSCORE_CONTEXT_ID
        //     }
        //     const pageActions: Action[] = [this.getPageActionForClassInstruction()]
        //     const widgets = [widget]
        //     const view: CultJourneyView = {
        //         widgets: widgets,
        //         pageActions: pageActions
        //     }
        //     return view
        // }
        const widgets: (WidgetView | PageWidget | DisplayUserDetailWidget | CardVideoWidget)[] = []
        let pageActions: Action[] = []
        if (!_.isEmpty(cultBooking.journeyContexts)) {
            for (let i = 0; i < CultJourneyContextIdsByPriority.length; i++) {
                const journeyContextId = CultJourneyContextIdsByPriority[i]
                for (let i = 0; i < cultBooking.journeyContexts.length; i++) {
                    const journeyContext = cultBooking.journeyContexts[i]
                    if (journeyContext.contextId === journeyContextId) {
                        const config = clone(this.bookingConfirmationConfig.getJourneyContextData(journeyContextId))
                        let widget
                        switch (journeyContextId) {
                            case "CULT_PACK_EXPIRING":
                                widget = await this.getCultPackRenewalWidget(journeyContext.data, config, userAgent, requestParams)
                                break
                            // case "MIND_PACK_EXPIRING":
                            //     widget = await this.getMindPackRenewalWidget(journeyContext.data, config, userAgent, requestParams)
                            //     break
                            // case "CULT_BOOKING_ONBOARDING":
                            // case "MIND_BOOKING_ONBOARDING":
                            //     // handling for cult junior
                            //     if (cultBooking.CultClass.Workout.ageCategory === "JUNIOR") {
                            //         config = this.bookingConfirmationConfig.getJourneyContextData(CULT_JUNIOR_ONBOARDING_CONTEXT_ID)
                            //     }
                            //     // special handling for cult yoga class instructions
                            //     if (cultBooking.Class.Workout.id === YOGA_WORKOUT_ID) {
                            //         const mindConfig = this.bookingConfirmationConfig.getJourneyContextData("MIND_BOOKING_ONBOARDING")
                            //         config.itemList = mindConfig.itemList
                            //     } else if (SWIMMING_WORKOUT_IDS.indexOf(cultBooking.Class.Workout.id) !== -1) {
                            //         config = this.bookingConfirmationConfig.getJourneyContextData(SWIMMING_CONTEXT_ID)
                            //     }
                            //     widget = this.getCultInitialClassInfoWidget(config, journeyContext.data)
                            //     pageActions = [this.getPageActionForClassInstruction()]
                            //     break
                        }

                        if (widget) {
                            widget.analyticsData = {
                                recommendationCategory: journeyContextId
                            }
                            widgets.push(widget)
                        }
                    }
                }
            }
        }

        const user = await this.userCache.getUser(requestParams.userContext.userProfile.userId)
        // const isNUXSupported = await AppUtil.isNUXSupported(requestParams.userContext, user.isInternalUser, productType, this.hamletBusiness)
        // if (CultUtil.isCompletedClassCountZero(cultAndMindSummary) && isNUXSupported) {
        //     const centerFacilities: string[] = []
        //     const videoWidgetItem: CardVideoItem = {
        //         image: FIRST_CLASS_PRE_WORKOUT_THUMBNAIL,
        //         videoUri: FIRST_CLASS_PRE_WORKOUT_VIDEO,
        //         thumbnailVideoUri: "",
        //         header: "PREPARE FOR YOUR FIRST CLASS",
        //         displayPlayIcon: true,
        //         hasSeparatorAbove: true, // will remove this in next app release,
        //         hasDividerBelow: true
        //     }
        //     widgets.push(new CardVideoWidget(videoWidgetItem))
        // }
        // const centerFacilities: string[] = []
        // if (cultBooking.Center.CenterManager) {
        //     for (let i = 0; i < cultBooking.Center.facilities.length; i++) {
        //         if (cultBooking.Center.facilities[i].available) centerFacilities.push(cultBooking.Center.facilities[i].name)
        //     }
        //     const centerInfoHeader = {
        //         "title": "ABOUT YOUR CENTRE",
        //     }
        //     const centerInfoCards = [
        //         {
        //             icon: UrlPathBuilder.getCultOnboardingImages("location"),
        //             subTitle: cultBooking.Center.Address.addressLine1,
        //             cardAction: {
        //                 actionType: "NAVIGATION",
        //                 url: `curefit://internaldeeplink?uri=${cultBooking.Center.placeUrl}`,
        //                 title: "NAVIGATE"
        //             },
        //         },
        //         {
        //             icon: UrlPathBuilder.getCultOnboardingImages("center"),
        //             subTitle: `Center facilities:\n${centerFacilities.join(" | ")}`
        //         }
        //     ]
        //     widgets.push(new ProductListWidget("SMALL", centerInfoHeader, centerInfoCards, undefined, undefined, undefined, true))
        //     widgets.push({
        //         name: `${cultBooking.Center.CenterManager}`,
        //         designation: "Centre Manager ",
        //         profilePictureUrl: "",
        //         address: cultBooking.Center.name,
        //         widgetType: "DISPLAY_USER_DETAILS",
        //         hasDividerAbove: false,
        //         showElevatedCard: true,
        //         hasDividerBelow: true,
        //         hasSeparatorAbove: true // will remove this in next app release
        //     })
        // }
        // if (cultBooking.preWorkoutTips) {
        //     const preWorkoutTipsHeader = {
        //         "title": "WHAT TO DO",
        //     }
        //     const preWorkoutTipsInfoCards = cultBooking.preWorkoutTips.map((preWorkoutTips) => ({
        //         title: preWorkoutTips.heading,
        //         subTitle: preWorkoutTips.subHeading,
        //         icon: UrlPathBuilder.getCultOnboardingImages(preWorkoutTips.identifier),
        //     }))
        //     widgets.push(new ProductListWidget("SMALL", preWorkoutTipsHeader, preWorkoutTipsInfoCards, null, null, null, true))
        // }

        // build special widget for waitlist classes
        if (cultBooking.wlBookingNumber) {
            const config = clone(this.bookingConfirmationConfig.getJourneyContextData(WAITLIST_BOOKING_CONTEXT_ID))
            const isOrderConfirmationV2Supported = AppUtil.isOrderConfirmationV2Supported(requestParams.userContext)
            if (isOrderConfirmationV2Supported) {
                const cultClass = cultBooking.Class
                const waitlistInfoWidget: WidgetView = {
                    widgetType: "INFO_LIST_WIDGET",
                    items: config.itemList,
                    header: {
                        title: config.title,
                        isExpandable: true,
                    },
                    isListExpanded: true,
                    containerStyle: {
                        marginTop: 0,
                        borderTopWidth: 0
                    }
                }
                if (!_.isEmpty(cultClass) && cultClass?.wlNotificationTime) {
                    waitlistInfoWidget.items = [
                        {
                            "icon" : "/image/icons/howItWorks/up.png",
                            "subTitle" : "Jump-up the waitlist queue whenever there is a cancellation"
                        },
                        {
                            "icon" : "/image/icons/howItWorks/track_2.png",
                            "subTitle" : "Track your waitlist position from the ‘Home’ tab "
                        },
                        {
                            "icon" : "/image/icons/howItWorks/chat.png",
                            "subTitle" : "Get notified as soon as your waitlist is confirmed"
                        },
                        {
                            "icon" : "/image/icons/howItWorks/error.png",
                            "subTitle" : "If your waitlist can’t be confirmed we will inform you prior to the class start time"
                        },
                        {
                            "icon" : "/image/icons/howItWorks/cancel_3.png",
                            "subTitle" : "In case you change your mind, you can choose to leave the waitlist anytime"
                        }
                    ]
                }
                widgets.push(waitlistInfoWidget)
            } else {
                widgets.push(this.getWaitlistInfoWidget(config))
            }
            pageActions = [this.getPageActionForClassInstruction()]
        }
        if (!_.isEmpty(cultBooking.CultClass) && CultUtil.isClassAvailableForPulse(cultBooking.CultClass, AppUtil.isCultPulseFeatureSupported(requestParams.userContext)) && !_.isEmpty(cultBooking.CultClass.pulseDeviceName)) {
            widgets.push(this.getPulseNextStepWidget())
        }
        return {
            widgets: widgets,
            pageActions: pageActions
        }
    }

    // async getMindPackRenewalWidget(contextData: any, config: any, userAgent: UserAgent, requestParams: ConfirmationRequestParams): Promise<PageWidget> {
    //     const mindPackProductId: string = CatalogueServiceV2Utilities.getMindPackProductId(contextData.packId)
    //     const pack = await this.catalogueService.getMindPack(mindPackProductId)
    //     return this.getPackRenewalWidget(pack, contextData, config, userAgent, requestParams)
    // }

    async getCultPackRenewalWidget(contextData: any, config: any, userAgent: UserAgent, requestParams: ConfirmationRequestParams): Promise<PageWidget> {
        let cultPackProductId: string = contextData.productId
        if (_.isEmpty(cultPackProductId)) {
            cultPackProductId = CatalogueServiceV2Utilities.getCultPackProductId(contextData.packId)
            if (!_.isEmpty(cultPackProductId)) this.logger.error("PMS::DEPR getCultPackRenewalWidget - CultPackProductId not sent", { packId: contextData.packId})
        }
        const pack = await this.catalogueServicePMS.getProduct(cultPackProductId)
        return this.getPackRenewalWidget(pack, contextData, config, userAgent, requestParams)
    }

    private async getPackRenewalWidget(pack: OfflineFitnessPack, contextData: any, config: any, userAgent: UserAgent, requestParams: ConfirmationRequestParams): Promise<PageWidget> {
        const packBrowseAction = CultUtil.getCultMindClpBrowsePackAction(pack.productType)
        const expiryMessage = `Your ${pack.title} pack is`
        const highlightedExpiryMessage = `expiring in ${contextData.daysToExpiry} days.`
        const subtitle = `Renew now!`
        const packProgress: PackProgress[] = []
        packProgress.push({
            productType: pack.productType,
            state: pack.status,
            image: CatalogueServiceUtilities.getFitnessProductImage(pack, "MAGAZINE", userAgent),
            title: config.itemTitle,
            expiryMessage: expiryMessage,
            highlightedExpiryMessage: highlightedExpiryMessage,
            subTitle: subtitle,
            widgetActions: [{
                title: "View Packs",
                viewType: "PRIMARY_BUTTON",
                actionType: "NAVIGATION",
                url: packBrowseAction
            }],
            action: {
                actionType: "NAVIGATION",
                url: packBrowseAction
            },
            itemViewType: "EXPIRY",
        })
        const packProgressWidget: PackProgressListWidget = {
            widgetType: "PACK_PROGRESS_LIST_WIDGET",
            widgetTitle: config.widgetTitle,
            items: packProgress,
            hasBottomPadding: false,
            hasTopPadding: false
        }
        return packProgressWidget
    }

    private getPulseNextStepWidget(): ProductListWidget {
        const header: Header = {
            title: "Next Steps for PULSE class",
            color: "#000000"
        }
        const cards: InfoCard[] = [
            {
                subTitle: "Collect your Pulse device and a free personal chest band at the center",
                icon: "/image/pulse/pulse_device1.png",
            },
            {
                subTitle: `To avoid rush in the changing room, reach the center ${CultUtil.getPulseClassReachEarlyCutoff()} minutes early`,
                icon: "/image/pulse/pulse-time2.png"
            },
            {
                subTitle: "Show your device number to the center manager to collect your device",
                icon: "/image/pulse/hash_info1.png",
            },
            {
                subTitle: "Wear the Pulse device and join the workout",
                icon: "/image/pulse/pulse-wrap1.png",
            },
            {
                subTitle: "Return the Pulse device after workout completion",
                icon: "/image/pulse/pulse-return1.png",
            }
        ]
        return new ProductListWidget("SMALL", header, cards)
    }

    getCultInitialClassInfoWidget(config: any, contextData?: any): WidgetView {
        const numberToString = contextData ? this.getNumberToString(contextData.numClassesBooked) : ""
        const title = config.title.replace("%s", numberToString)
        const header: Header = {
            title: title,
            subTitle: config.subTitle,
            color: "#000000"
        }
        const cards: InfoCard[] = []
        const items: OfferingItem[] = config.itemList
        items.forEach(item => {
            cards.push({
                title: item.title,
                subTitle: item.description,
                icon: item.icon
            })
        })
        const widget = new ProductListWidget("SMALL", header, cards)
        widget.hideSepratorLines = true
        return widget
    }

    getWaitlistInfoWidget(config: any): WidgetView {
        const hiwWidget: ProductListWidget = {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "SMALL",
            items: config.itemList,
            hideSepratorLines: true,
            header: {
                title: config.title,
            }
        }
        return hiwWidget
    }

    getPageActionForClassInstruction(): Action {
        return {
            actionType: "NAVIGATION",
            url: "curefit://hometab",
            title: "Got it"
        }
    }

    private getNumberToString(number: number): string {
        switch (number) {
            case 1:
                return "first"
            case 2:
                return "second"
            case 3:
                return "third"
        }
    }
}

export default OrderConfirmationBusiness
