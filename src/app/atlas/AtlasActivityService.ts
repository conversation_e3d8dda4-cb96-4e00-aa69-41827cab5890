import { inject, injectable } from "inversify"
import { BASE_TYPES, FetchUtilV2, Lo<PERSON> } from "@curefit/base"
import { ILockAccess, RedlockAccess } from "@curefit/lock-utils"
import { StepsData } from "@curefit/atlas-client"
import { Constants, SleepActivityV2, StepsActivity } from "@curefit/base-utils"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { MultiMysqlAccess, MYSQL_TYPES } from "@curefit/mysql-utils"
import { eternalPromise, TimeUtil } from "@curefit/util-common"
import { FitbitUserInfo } from "@curefit/userinfo-common"
import { UserDeviceDbModel } from "../daos/mysql/atlas/device/UserDeviceModel"
import { UserTokenDbModel } from "../daos/mysql/atlas/token/UserTokenModel"
import { ClientDefaults } from "@curefit/user-client"
import { ILoggingService, LOGGING_CLIENT_TYPES } from "@curefit/logging-client"
import { IQueueService, SQS_CLIENT_TYPES } from "@curefit/sqs-client"
import * as _ from "lodash"
import { ErrorFactory } from "@curefit/error-client"
import { ErrorCodes } from "../error/ErrorCodes"
import { MultiRedisAccess, REDIS_TYPES } from "@curefit/redis-utils"
import { Activity } from "./FitbitCronService"
import { HealthMetricData, SubmitFDMetricsPayload } from "@curefit/logging-common/dist/src/models/ActivityStore"
import { BaseActivityPayload, HeartRateVariabilityActivityPayload } from "@curefit/logging-common"

const fetch = require("node-fetch")

export enum StepsSourceType {
    GOOGLE_FIT = "GOOGLE_FIT",
    APPLE_HEALTH = "APPLE_HEALTH",
    FITBIT = "FITBIT"
}

export interface UserDeviceAndToken {
    user_id: number
    fitbit: string
    fitbit_token: string
}

@injectable()
export class AtlasActivityService {

    private lock: ILockAccess

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(REDIS_TYPES.MultiRedisAccess) private multiRedisAccess: MultiRedisAccess,
        @inject(MYSQL_TYPES.MultiMysqlAccess) private mysqlAccess: MultiMysqlAccess,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.FetchUtilV2) private fetchHelper: FetchUtilV2,
        @inject(SQS_CLIENT_TYPES.QueueService) private queueService: IQueueService,
        @inject(LOGGING_CLIENT_TYPES.LoggingService) private loggingService: ILoggingService,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
    ) {
        this.lock = new RedlockAccess(this.multiRedisAccess, logger, "CFAPI-CACHE")
    }

    async updateStepsData(userId: string, platform: string, deviceId: string, stepsDataList: StepsData[]): Promise<boolean> {
        const lock = await this.lock.lockResource(`ATLAS-${userId}-${deviceId}-${platform}-STEPS`, 5000)
        try {
            await this.validateStepsForUser(stepsDataList)
            await this.updateStepsForUser(userId, platform, deviceId, stepsDataList)
            return true
        } catch (err) {
            this.logger.error(err)
            this.rollbarService.sendError(err)
            throw err
        } finally {
            await this.lock.unlockResource(lock)
        }
    }

    private async validateStepsForUser(stepsDataList: StepsData[]): Promise<boolean> {
        const dateSet = new Set(stepsDataList.map(x => x.date))
        if (dateSet.size !== stepsDataList.length) {
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("duplicate dates in input data").build()
        }
        return true
    }

    private async updateStepsForUser(userId: string, platform: string, deviceId: string, stepsDataList: StepsData[]): Promise<boolean> {
        const sourceType = AtlasActivityService.getSourceType(platform)

        _.forEach(stepsDataList, stepsData => {
            const stepsActivity: StepsActivity = {
                steps: stepsData.steps,
                date: stepsData.date,
                userId,
                platform: sourceType,
                deviceId,
            }
            const attributes = new Map()
            attributes.set("userId", userId)
            attributes.set("type", "ACTIVITY_STEPS")
            this.queueService.sendMessageAsync(Constants.getSQSQueue("ACTIVITYSTORE"), stepsActivity, attributes)
        })

        return true
    }

    public async updateActivitiesAsync(userId: string, activitiesList: Activity[]): Promise<boolean> {
        activitiesList.forEach((activity) => {
            const attributes = new Map()
            attributes.set("userId", userId)
            attributes.set("type", activity.activityMsgType)
            this.queueService.sendMessageAsync(Constants.getSQSQueue("ACTIVITYSTORE"), activity.activityData, attributes)
        })
        return true
    }

    private static getSourceType(platform: string) {
        let sourceType = StepsSourceType.GOOGLE_FIT
        if (platform && platform.toLowerCase() !== "android") {
            if (platform && platform.toLowerCase() !== "fitbit") {
                sourceType = StepsSourceType.APPLE_HEALTH
            } else {
                sourceType = StepsSourceType.FITBIT
            }
        }
        return sourceType
    }

    // async updateSleepData(tz: Timezone, userId: string, sleepDate: string, userSleepData: SleepUpdatePayload): Promise<boolean> {
    //     const knex = this.mysqlAccess.getMysqlConnection("ATLAS")
    //     const lock = await this.lock.lockResource(`ATLAS-${userId}-${sleepDate}-SLEEP`, 5000)

    //     try {
    //         const dbUserSleepEntries = await knex.table("user_sleep_all").where({
    //             userId,
    //             sleepDate,
    //             sourceType: userSleepData.sourceType,
    //             deviceId: userSleepData.deviceId
    //         }).limit(1)
    //         const dbUserSleep = dbUserSleepEntries.length > 0 && dbUserSleepEntries[0] || {} as UserSleepDbModel
    //         const userSleepDbModel = {
    //             userId,
    //             sleepDate,
    //             startTime: TimeUtil.parseDateFromEpochWithTimezone(tz, userSleepData.startTime).toISOString(),
    //             endTime: TimeUtil.parseDateFromEpochWithTimezone(tz, userSleepData.endTime).toISOString(),
    //             deviceId: userSleepData.deviceId || dbUserSleep.deviceId,
    //             sourceType: userSleepData.sourceType || dbUserSleep.sourceType,
    //             meta: userSleepData.meta || dbUserSleep.meta,
    //             platform: userSleepData.platform || dbUserSleep.platform,
    //             referenceSourceType: userSleepData.referenceSourceType || dbUserSleep.referenceSourceType,
    //             sourceAlgo: userSleepData.sourceAlgo || dbUserSleep.sourceAlgo,
    //         } as UserSleepDbModel

    //         if (dbUserSleep && dbUserSleep.id) {
    //             await knex.table("user_sleep_all").where({
    //                 userId,
    //                 sleepDate,
    //                 sourceType: userSleepData.sourceType,
    //                 deviceId: userSleepData.deviceId
    //             }).update(userSleepDbModel)
    //         } else {
    //             await knex.table("user_sleep_all").insert(userSleepDbModel)
    //         }
    //     } catch (err) {
    //         this.logger.error(err)
    //         this.rollbarService.sendError(err)
    //         return false
    //     } finally {
    //         await this.lock.unlockResource(lock)
    //     }
    //     return true
    // }

    async getFitbitDataForUser(userId: string): Promise<FitbitUserInfo> {
        this.logger.info("user info fitbit", userId)
        const knex = this.mysqlAccess.getMysqlConnection("USER_PROFILE")
        const userTokens = await knex.column(["user_token.user_id", "user_device.fitbit", "fitbit_token"]).from("user_token")
            .innerJoin("user_device", "user_token.user_id", "=", "user_device.user_id")
            .where("user_token.user_id", Number(userId)).whereNotNull("user_device.fitbit")
            .whereNotNull("fitbit_token").whereNot("fitbit_token", "").limit(1) as UserDeviceAndToken[]
        if (userTokens.length === 0) {
            return null
        }
        const userToken = userTokens[0] as UserDeviceAndToken
        return {
            userId,
            fitbitId: userToken.fitbit,
            fitbitToken: userToken.fitbit_token
        }
    }

    async setFitbitDataForUser(data: FitbitUserInfo): Promise<FitbitUserInfo> {
        const knex = this.mysqlAccess.getMysqlConnection("USER_PROFILE")
        const intUserId = parseInt(data.userId)

        // update device
        const dbUserDeviceEntries = await knex.table("user_device").where({
            user_id: intUserId
        }).limit(1)
        let dbUserDevice = dbUserDeviceEntries.length > 0 && dbUserDeviceEntries[0] as UserDeviceDbModel
        if (!dbUserDevice) {
            dbUserDevice = {
                user_id: intUserId,
                fitbit: data.fitbitId
            } as UserDeviceDbModel
            await knex.table("user_device").insert(dbUserDevice)
        } else {
            dbUserDevice.fitbit = data.fitbitId
            await knex.table("user_device").where({
                user_id: intUserId
            }).update(dbUserDevice)
        }

        // update token
        const dbUserTokenEntries = await knex.table("user_token").where({
            user_id: intUserId
        }).limit(1)
        let dbUserToken = dbUserTokenEntries.length > 0 && dbUserTokenEntries[0] as UserTokenDbModel
        if (!dbUserToken) {
            dbUserToken = {
                user_id: intUserId,
                fitbit_token: data.fitbitToken
            } as UserTokenDbModel
            await knex.table("user_token").insert(dbUserToken)
        } else {
            dbUserToken.fitbit_token = data.fitbitToken
            await knex.table("user_token").where({
                user_id: intUserId
            }).update(dbUserToken)
        }

        return data
    }

    async fitbitLogout(userId: string): Promise<FitbitUserInfo> {
        const info = await this.getFitbitDataForUser(userId)
        if (!info) {
            this.rollbarService.sendWarningMessage("Could not find fitbit user while logging out", {extra: {userId}})
            return Promise.reject(this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Could not find fitbit user").build())
        }
        const hash = ClientDefaults.getFitbitAppId() + ":" + ClientDefaults.getFitbitSecretKey()
        const body = {
            token: info.fitbitToken
        } as any
        const headers = {
            "Authorization": "Basic " + Buffer.from(hash).toString("base64")
        }
        await fetch("https://api.fitbit.com/oauth2/revoke", this.fetchHelper.post({body, headers}))
        const fitbitUserInfo = {
            userId,
            fitbitToken: null,
            fitbitId: null
        } as FitbitUserInfo
        await this.setFitbitDataForUser(fitbitUserInfo)
        return fitbitUserInfo
    }

    async isFitbitAuthenticated(userId: string): Promise<boolean> {
        const fitbitPromise = eternalPromise(this.getFitbitDataForUser(userId), "fitbit login details")
        const fitbitData: FitbitUserInfo = (await fitbitPromise).obj
        const isfitbitAuthenticated: boolean = !_.isEmpty(fitbitData) && !_.isNil(fitbitData.fitbitToken) && fitbitData.fitbitToken !== "" ? true : false
        return isfitbitAuthenticated
    }

    async updateActivitiesForUser(userId: string, metricsPayload: SubmitFDMetricsPayload): Promise<boolean> {
        if (_.isNil(metricsPayload.metricsData)) {
            this.logger.info(`could not submit data to activity Store Queue for userId: ${userId} as metrics Data came as null`)
            return false
        }
        try {
            const activities: Activity[] = this.getActivitiesFromMetricData(userId, metricsPayload.metricsData)
            return await this.updateActivitiesAsync(userId, activities)
        } catch (e) {
            this.logger.error(`could not submit data to activity Store Queue for userId: ${userId}` + e)
            return false
        }
    }


    private getActivitiesFromMetricData(userId: string, metricsData: HealthMetricData[]) {
        const activities = [] as Activity[]
        metricsData.forEach((metricData) => {
            switch (metricData.type) {
                case "STEPS" :
                    const stepsActivityPayload: StepsActivity = {
                        date: AtlasActivityService.formatDateEpoch(metricData.epoch),
                        deviceId: metricData.deviceId,
                        platform: AtlasActivityService.getPlatform(metricData.platform),
                        steps: _.isNil(metricData.value) ? 0 : metricData.value,
                        userId: userId,
                        unit: "NUMBER"
                    }
                    activities.push({
                        activityMsgType: "ACTIVITY_STEPS",
                        activityData: stepsActivityPayload
                    })
                    break
                case "SLEEP_IN_BED" :
                    const sleepPayload: SleepActivityV2 = {
                        platform: AtlasActivityService.getPlatform(metricData.platform),
                        device_id: metricData.deviceId,
                        duration_ms: metricData.value,
                        uuid: null,
                        id: null,
                        referenceSourceType: null,
                        sourceAlgo: null,
                        sleep_date: AtlasActivityService.formatDateEpoch(metricData.epoch),
                        source: AtlasActivityService.getPlatform(metricData.platform),
                        unit: "MILLISECOND",
                        end_time: null,
                        meta: null,
                        sourceType: null,
                        start_time: null,
                        timezone: undefined
                    }
                    activities.push({
                        activityMsgType: "ACTIVITY_SLEEP_V2",
                        activityData: sleepPayload
                    })
                    break
                case "HEART_RATE" :
                case "ACTIVE_ENERGY_BURNED" :
                case "EXERCISE_TIME":
                    const baseActivityPayload: BaseActivityPayload = {
                        date: AtlasActivityService.formatDateEpoch(metricData.epoch),
                        deviceId: metricData.deviceId,
                        platform: AtlasActivityService.getPlatform(metricData.platform),
                        data: {
                            value: metricData.value,
                            unit: metricData.type === "HEART_RATE" ? "BEATS_PER_MINUTE" : (metricData.type === "ACTIVE_ENERGY_BURNED" ? "KILOCALORIE" : "MINUTE")
                        },
                        userId: userId,
                    }
                    activities.push({
                        activityMsgType: metricData.type === "HEART_RATE" ? "HEART_RATE" : (metricData.type === "ACTIVE_ENERGY_BURNED" ? "ACTIVITY_CALORIES_BURNT" : "ACTIVITY_EXERCISE_DURATION"),
                        activityData: baseActivityPayload
                    })
                    break
                case "HEART_RATE_VARIABILITY_SDNN" :
                    const hrvPayload: HeartRateVariabilityActivityPayload = {
                        data: {
                            value: metricData.value,
                            unit: "MILLISECOND",
                            variableMeasurement: "SDNN"
                        },
                        date: AtlasActivityService.formatDateEpoch(metricData.epoch),
                        deviceId: metricData.deviceId,
                        platform: AtlasActivityService.getPlatform(metricData.platform),
                        userId: userId
                    }
                    activities.push({
                        activityMsgType: "HEART_RATE_VARIABILITY",
                        activityData: hrvPayload
                    })
                    break
            }
        })
        return activities
    }

    private static getPlatform(platform: string) {
        if (platform.toLowerCase() == "ios") {
            return "APPLE_HEALTH"
        } else if (platform.toLowerCase() == "android") {
            return "GOOGLE_FIT"
        }
        return ""
    }

    private static formatDateEpoch(value: any): any {
        const date = new Date(Math.round(Number(value)))
        const day = ("0" + date.getDate()).slice(-2)
        const month = ("0" + (date.getMonth() + 1)).slice(-2)
        return date.getFullYear() + "-" + (month) + "-" + (day)
    }

}
