import { Container, inject } from "inversify"
import { controller, httpGet } from "inversify-express-utils"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { FitbitCronService } from "./FitbitCronService"
import { Session } from "@curefit/userinfo-common"
import * as express from "express"

export function controllerFactory(kernel: Container) {
    @controller("/atlas")
    class AtlasController {
        constructor(
            @inject(CUREFIT_API_TYPES.FitbitCronService) private fitbitCronService: FitbitCronService,
        ) {
        }

        @httpGet("/fitbit/cron")
        public async triggerFitbitCron(request: any): Promise<boolean> {
            const {hcId} = request.query
            const trigger$ = this.fitbitCronService.updateActivitesFromFitbit(hcId)
            return true
        }

        @httpGet("/fitbit/sync/user")
        public async triggerFitbitSyncForUser(request: any): Promise<any> {
            const {userId} = request.query
            return this.fitbitCronService.getActivitiesForUser(userId)
        }

        @httpGet("/fitbit/sync/user/detailedStat")
        public async triggerFitbitStepsSyncForUser(request: express.Request): Promise<any> {
            const {userId} = request.query
            return this.fitbitCronService.updateStepsActivitesFromFitbit(userId)
        }
    }

    return AtlasController
}

export default controllerFactory
