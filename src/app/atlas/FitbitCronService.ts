import { inject, injectable } from "inversify"
import { BASE_TYPES, FetchUtilV2, Lo<PERSON> } from "@curefit/base"
import { ILockAccess, RedlockAccess } from "@curefit/lock-utils"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { MultiMysqlAccess, MYSQL_TYPES } from "@curefit/mysql-utils"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { AtlasActivityService, UserDeviceAndToken } from "./AtlasActivityService"
import { SleepActivityV2, StepsActivity } from "@curefit/base-utils"
import * as _ from "lodash"
import { ILoggingService, LOGGING_CLIENT_TYPES } from "@curefit/logging-client"
import { IQueueService, SQS_CLIENT_TYPES } from "@curefit/sqs-client"
import { ErrorFactory } from "@curefit/error-client"
import { ErrorCodes } from "../error/ErrorCodes"
import { MultiRedisAccess, REDIS_TYPES } from "@curefit/redis-utils"
import { BaseActivityPayload, HeartRateVariabilityActivityPayload } from "@curefit/logging-common"
import { HealthMetricData } from "@curefit/logging-common/dist/src/models/ActivityStore"


const fetch = require("node-fetch")

export interface FitbitSleeps {
    sleep: FitbitSleep[]
}

export interface FitbitSleep {
    dateOfSleep: string // only date
    duration: number // sleep duration in ms
    startTime: string // date+time;no tz
    endTime: string // date+time;no tz
    levels: FitbitSleepLevels
}

export interface FitbitSleepLevels {
    data: FitbitSleepLevel[]
}

export interface FitbitSleepLevel {
    datetime: string
    level: string
    seconds: number
}

export interface HRVFitbitResponse {
    "hrv": {
        "value": {
            "dailyRmssd": number,
            "deepRmssd": number
        },
        "dateTime": string
    }[]
}

export interface HeartRateIntradayFitbitResponse {
    "activities-heart": {
        dateTime: string,
        value: Object
    }[],
    "activities-heart-intraday": {
        dataset: HeartActivityTime[]
        datasetInterval: number
        datasetType: string
    },
}

interface HeartActivityTime {
    time: string,
    value: number
}

export interface Activity {
    activityMsgType: string,
    activityData: SleepActivityV2 | StepsActivity | BaseActivityPayload | HeartRateVariabilityActivityPayload
}

export interface ActivitySummary {
    "activities": [],
    "goals": {
        "activeMinutes": number,
        "caloriesOut": number,
        "distance": number,
        "floors": number,
        "steps": number
    },
    "summary": {
        "activeScore": number,
        "activityCalories": number,
        "calorieEstimationMu": number,
        "caloriesBMR": number,
        "caloriesOut": number,
        "caloriesOutUnestimated": number,
        "elevation": number,
        "fairlyActiveMinutes": number,
        "floors": number,
        "lightlyActiveMinutes": number,
        "marginalCalories": number,
        "restingHeartRate": number,
        "sedentaryMinutes": number,
        "steps": number,
        "useEstimation": boolean,
        "veryActiveMinutes": number
    }
}

export interface FitbitUserProfile {
    user: {
        timezone: Timezone,
    }
}

interface FitbitError {
    errorType: string
    message: string
}

@injectable()
export class FitbitCronService {

    private lock: ILockAccess

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(REDIS_TYPES.MultiRedisAccess) private multiRedisAccess: MultiRedisAccess,
        @inject(MYSQL_TYPES.MultiMysqlAccess) private mysqlAccess: MultiMysqlAccess,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.FetchUtilV2) private fetchHelper: FetchUtilV2,
        @inject(SQS_CLIENT_TYPES.QueueService) private queueService: IQueueService,
        @inject(LOGGING_CLIENT_TYPES.LoggingService) private loggingService: ILoggingService,
        @inject(CUREFIT_API_TYPES.AtlasActivityService) private atlasActivityService: AtlasActivityService,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
    ) {
        this.lock = new RedlockAccess(this.multiRedisAccess, logger, "CFAPI-CACHE")
    }

    private FITBIT_PLATFORM: string = "FITBIT"

    public async getActivitiesForUser(userId: string): Promise<any> {
        const lock = await this.lock.lockResource(`ATLAS-SYNC-FITBIT-${userId}`, 5_000)
        try {
            const fitbitUserInfo = await this.atlasActivityService.getFitbitDataForUser(userId)
            if (!fitbitUserInfo) {
                return {activities: []}
                throw this.errorFactory.withCode(ErrorCodes.FITBIT_USER_NOT_LOGGED_IN_ERR, 400).withContext({userId}).withDebugMessage(`user: ${userId} not logged in to fitbit`).build()
            }
            const userToken = {
                user_id: Number(userId),
                fitbit_token: fitbitUserInfo.fitbitToken,
                fitbit: fitbitUserInfo.fitbitId
            } as UserDeviceAndToken
            await this.lock.unlockResource(lock)
            return await this.getUserActivitiesPayload(userToken)
        } catch (err) {
            this.logger.error(err)
            this.rollbarService.sendError(err)
            await this.lock.unlockResource(lock)
            throw err
        }
    }

    public async updateActivitesFromFitbit(hcId: string): Promise<boolean> {
        const knex = this.mysqlAccess.getMysqlConnection("USER_PROFILE")
        const lock = await this.lock.lockResource(`ATLAS-CRON-FITBIT`, 50_000)
        try {
            this.logger.info(`informing fitbit cron hc start for hcId: ${hcId}`)
            await fetch(`https://hc-ping.com/${hcId}/start`, this.fetchHelper.get())
            const userTokens = await knex.column(["user_token.user_id", "user_device.fitbit", "fitbit_token"]).from("user_token")
                .innerJoin("user_device", "user_token.user_id", "=", "user_device.user_id")
                .whereNotNull("user_device.fitbit").whereNotNull("fitbit_token")
                .whereNot("fitbit_token", "") as UserDeviceAndToken[]
            this.logger.info(`found ${userTokens.length} valid fitbit tokens`)
            let num_success = 0, iteration = 0
            while (userTokens.length > 0 && iteration < 1_000) {
                iteration++
                const chunk = userTokens.splice(0, 20)
                const report = await Promise.all(chunk.map(userToken => this.updateUserActivitiesAsync(userToken))) as boolean[]
                num_success += report.filter(x => x).length
            }
            this.logger.info(`fitbit data synced for ${num_success} users. Informing fitbit cron hc success for hcId: ${hcId}`)
            await fetch(`https://hc-ping.com/${hcId}`, this.fetchHelper.get())
            await this.lock.unlockResource(lock)
            return true
        } catch (err) {
            // this.rollbarService.sendError(err)
            this.logger.error(`informing fitbit cron hc failure for hcId: ${hcId} and error is ${err.message}`)
            await fetch(`https://hc-ping.com/${hcId}/fail`, this.fetchHelper.get())
            await this.lock.unlockResource(lock)
            throw err
        }
    }

    public async updateStepsActivitesFromFitbit(userId: string): Promise<any> {
        const lock = await this.lock.lockResource(`ATLAS-SYNC-FITBIT-${userId}`, 5_000)
        try {
            this.logger.info("user id for fitbit data", userId)
            const fitbitUserInfo = await this.atlasActivityService.getFitbitDataForUser(userId)
            if (!fitbitUserInfo) {
                return false
                throw this.errorFactory.withCode(ErrorCodes.FITBIT_USER_NOT_LOGGED_IN_ERR, 400).withContext({userId}).withDebugMessage(`user: ${userId} not logged in to fitbit`).build()
            }
            const userToken = {
                user_id: Number(userId),
                fitbit_token: fitbitUserInfo.fitbitToken,
                fitbit: fitbitUserInfo.fitbitId
            } as UserDeviceAndToken

            return await this.updateUserActivitiesSync(userToken)
        } catch (err) {
            this.logger.error(err)
            this.rollbarService.sendError(err)
            throw err
        } finally {
            await this.lock.unlockResource(lock)
        }
    }

    private async updateUserActivitiesSync(userToken: UserDeviceAndToken): Promise<boolean> {
        try {
            const fitbitUserProfile = await this.getFitbitUserProfile(userToken) as FitbitUserProfile
            const activities: Activity[] = await this.getAllActivitiesByUserToken(userToken, fitbitUserProfile)
            this.logger.info("<<fitbit activity sync>> activities for user", activities)
            const response = await this.loggingService.putBulkActivities(userToken.user_id.toString(), {activities: activities})
            this.logger.info("<<fitbit activity sync>> activities set in db", response)
            return true
        } catch (e) {
            this.logger.error(`could not sync fitbit data for userId: ${userToken.user_id}` + e)
            return false
        }
    }

    private async updateUserActivitiesAsync(userToken: UserDeviceAndToken): Promise<boolean> {
        try {
            const fitbitUserProfile = await this.getFitbitUserProfile(userToken) as FitbitUserProfile
            const activities: Activity[] = await this.getAllActivitiesByUserToken(userToken, fitbitUserProfile)
            await this.atlasActivityService.updateActivitiesAsync(String(userToken.user_id), activities)
            return true
        } catch (e) {
            this.logger.error(`could not sync fitbit data for userId: ${userToken.user_id}` + e)
            return false
        }
    }

    private async getUserActivitiesPayload(userToken: UserDeviceAndToken): Promise<any> {
        try {
            const fitbitUserProfile = await this.getFitbitUserProfile(userToken) as FitbitUserProfile
            const activitiesData: Activity[] = await this.getAllActivitiesByUserToken(userToken, fitbitUserProfile)
            return {activities: activitiesData}
        } catch (e) {
            this.logger.error(`could not sync fitbit data for userId: ${userToken.user_id}` + e)
            return {activities: []}
        }
    }

    private async getAllActivitiesByUserToken(userToken: UserDeviceAndToken, fitbitUserProfile: FitbitUserProfile): Promise<Activity[]> {
        let activities = [] as Activity[]
        let results = await this.getActivitiesByUserToken(userToken, fitbitUserProfile)
        activities = [...activities, ...results]
        results = await this.getHRVByUserToken(userToken, fitbitUserProfile)
        activities = [...activities, ...results]
        results = await this.getAvgHeartRateByUserToken(userToken, fitbitUserProfile)
        activities = [...activities, ...results]
        results = await this.getSleepByUserToken(userToken, fitbitUserProfile)
        activities = [...activities, ...results]
        return activities
    }

    private async getActivitiesByUserToken(userDeviceAndToken: UserDeviceAndToken, fitbitUserProfile: FitbitUserProfile): Promise<Activity[]> {
        const deviceId = userDeviceAndToken.fitbit
        const userId: string = String(userDeviceAndToken.user_id)
        const todayDate: string = TimeUtil.todaysDate(fitbitUserProfile.user.timezone, "YYYY-MM-DD")
        const activitiesUrl = `https://api.fitbit.com/1/user/${userDeviceAndToken.fitbit}/activities/date/${todayDate}.json`
        const activities = [] as Activity[]
        const headers = {
            Authorization: `Bearer ${userDeviceAndToken.fitbit_token}`,
        }
        try {
            const fitbitResponse = await fetch(activitiesUrl, this.fetchHelper.get({headers}))
            const dailySummary = await this.fetchHelper.parseResponse(fitbitResponse) as ActivitySummary
            const activitiesSummary = dailySummary["summary"]

            if (activitiesSummary["steps"]) {
                const stepsActivityPayload: StepsActivity = {
                    date: todayDate,
                    deviceId: deviceId,
                    platform: this.FITBIT_PLATFORM,
                    steps: activitiesSummary["steps"],
                    userId: userId,
                    unit: "NUMBER"
                }
                activities.push({
                    activityMsgType: "ACTIVITY_STEPS",
                    activityData: stepsActivityPayload
                })
            }

            if (activitiesSummary["fairlyActiveMinutes"] && activitiesSummary["lightlyActiveMinutes"] && activitiesSummary["veryActiveMinutes"]) {
                const activeMinutes = Math.max(activitiesSummary["fairlyActiveMinutes"], activitiesSummary["lightlyActiveMinutes"], activitiesSummary["veryActiveMinutes"])
                const exercisePayload: BaseActivityPayload = {
                    data: {
                        value: activeMinutes,
                        unit: "MINUTE"
                    },
                    date: todayDate,
                    deviceId: deviceId,
                    platform: this.FITBIT_PLATFORM,
                    userId: userId
                }
                activities.push({
                    activityMsgType: "ACTIVITY_EXERCISE_DURATION",
                    activityData: exercisePayload
                })
            }

            if (activitiesSummary["activityCalories"]) {
                const caloriesPayload: BaseActivityPayload = {
                    data: {
                        value: activitiesSummary["activityCalories"],
                        unit: "KILOCALORIE"
                    },
                    date: todayDate,
                    deviceId: deviceId,
                    platform: this.FITBIT_PLATFORM,
                    userId: userId
                }
                activities.push({
                    activityMsgType: "ACTIVITY_CALORIES_BURNT",
                    activityData: caloriesPayload
                })
            }
            return activities
        } catch (e) {
            const responseBody = _.get(e, "context.upstream.responseBody", {})
            if (_.isArray(responseBody.errors) && (responseBody.errors as FitbitError[]).some(err => err.errorType === "insufficient_scope")) {
                this.logger.info(`removing expired fitbit token for user_id: ${userDeviceAndToken.user_id}`)
                await this.atlasActivityService.fitbitLogout(String(userDeviceAndToken.user_id))
            }
            throw e
        }
    }

    private async getHRVByUserToken(userDeviceAndToken: UserDeviceAndToken, fitbitUserProfile: FitbitUserProfile): Promise<Activity[]> {
        const deviceId = userDeviceAndToken.fitbit
        const userId: string = String(userDeviceAndToken.user_id)
        const todayDate: string = TimeUtil.todaysDate(fitbitUserProfile.user.timezone, "YYYY-MM-DD")
        const activitiesUrl = `https://api.fitbit.com/1/user/${userDeviceAndToken.fitbit}/hrv/date/${todayDate}.json`
        const headers = {
            Authorization: `Bearer ${userDeviceAndToken.fitbit_token}`,
        }
        try {
            const fitbitResponse = await fetch(activitiesUrl, this.fetchHelper.get({headers}))
            const hrvSummary = await this.fetchHelper.parseResponse(fitbitResponse) as HRVFitbitResponse
            if (hrvSummary?.hrv && hrvSummary.hrv.length > 0 && hrvSummary.hrv[0].value?.dailyRmssd) {
                const hrvPayload: HeartRateVariabilityActivityPayload = {
                    data: {
                        value: hrvSummary.hrv[0].value.dailyRmssd,
                        unit: "MILLISECOND",
                        variableMeasurement: "RMSSD"
                    },
                    date: todayDate,
                    deviceId: deviceId,
                    platform: this.FITBIT_PLATFORM,
                    userId: userId
                }
                return [{
                    activityMsgType: "HEART_RATE_VARIABILITY",
                    activityData: hrvPayload
                }]
            } else {
                return []
            }
        } catch (e) {
            const responseBody = _.get(e, "context.upstream.responseBody", {})
            if (_.isArray(responseBody.errors) && (responseBody.errors as FitbitError[]).some(err => err.errorType === "insufficient_scope")) {
                this.logger.info(`removing expired fitbit token for user_id: ${userDeviceAndToken.user_id}`)
                await this.atlasActivityService.fitbitLogout(String(userDeviceAndToken.user_id))
            }
            throw e
        }
    }

    private async getAvgHeartRateByUserToken(userDeviceAndToken: UserDeviceAndToken, fitbitUserProfile: FitbitUserProfile): Promise<Activity[]> {
        const deviceId = userDeviceAndToken.fitbit
        const userId: string = String(userDeviceAndToken.user_id)
        const todayDate: string = TimeUtil.todaysDate(fitbitUserProfile.user.timezone, "YYYY-MM-DD")
        const activitiesUrl = `https://api.fitbit.com/1/user/${userDeviceAndToken.fitbit}/activities/heart/date/${todayDate}/1d/15min.json`
        const headers = {
            Authorization: `Bearer ${userDeviceAndToken.fitbit_token}`,
        }
        try {
            const fitbitResponse = await fetch(activitiesUrl, this.fetchHelper.get({headers}))
            const heartRateRes = await this.fetchHelper.parseResponse(fitbitResponse) as HeartRateIntradayFitbitResponse
            if ("activities-heart-intraday" in heartRateRes && "dataset" in heartRateRes["activities-heart-intraday"]) {
                const heartRateIntraday = heartRateRes["activities-heart-intraday"]["dataset"] as HeartActivityTime[]
                if (_.isEmpty(heartRateIntraday)) {
                    this.logger.info(`FITBIT HEART_RATE empty data for userId ${userId} with heartRateData: ${JSON.stringify(heartRateRes)}`)
                    return []
                }
                const heartRateTotal = heartRateIntraday.reduce((total, current) => {
                    return total + current.value
                }, 0)
                const avg = heartRateTotal / heartRateIntraday.length
                const hearRatePayload: BaseActivityPayload = {
                    data: {
                        value: avg,
                        unit: "BEATS_PER_MINUTE"
                    },
                    date: todayDate,
                    deviceId: deviceId,
                    platform: this.FITBIT_PLATFORM,
                    userId: userId
                }
                return [{
                    activityMsgType: "HEART_RATE",
                    activityData: hearRatePayload
                }]
            } else {
                return []
            }
        } catch (e) {
            const responseBody = _.get(e, "context.upstream.responseBody", {})
            if (_.isArray(responseBody.errors) && (responseBody.errors as FitbitError[]).some(err => err.errorType === "insufficient_scope")) {
                this.logger.info(`removing expired fitbit token for user_id: ${userDeviceAndToken.user_id}`)
                await this.atlasActivityService.fitbitLogout(String(userDeviceAndToken.user_id))
            }
            throw e
        }
    }

    private async getSleepByUserToken(userDeviceAndToken: UserDeviceAndToken, fitbitUserProfile: FitbitUserProfile): Promise<Activity[]> {
        const tz = fitbitUserProfile.user.timezone
        const todayDateString = TimeUtil.todaysDate(tz)
        const fourDaysAgoString = TimeUtil.subtractDays(tz, todayDateString, 4)
        const stepsUrl = `https://api.fitbit.com/1.2/user/${userDeviceAndToken.fitbit}/sleep/date/${fourDaysAgoString}/${todayDateString}.json`
        const headers = {
            Authorization: `Bearer ${userDeviceAndToken.fitbit_token}`,
        }
        const fitbitResponse = await fetch(stepsUrl, this.fetchHelper.get({headers}))
        const fitbitSleeps = await this.fetchHelper.parseResponse(fitbitResponse) as FitbitSleeps
        const activities = [] as Activity[]
        for (const fitbitSleep of fitbitSleeps.sleep) {
            const sleepPayload: SleepActivityV2 = {
                platform: this.FITBIT_PLATFORM,
                device_id: userDeviceAndToken.fitbit,
                start_time: TimeUtil.parseDateTime(fitbitSleep.startTime, tz).getTime(),
                end_time: TimeUtil.parseDateTime(fitbitSleep.endTime, tz).getTime(),
                sourceType: "FITBIT",
                meta: "",
                duration_ms: fitbitSleep.duration,
                sleep_date: fitbitSleep.dateOfSleep,
                uuid: String(userDeviceAndToken.user_id),
                id: null,
                referenceSourceType: null,
                sourceAlgo: null,
                source: "FITBIT",
                timezone: tz,
                unit: "MILLISECOND"
            }
            activities.push({
                activityMsgType: "ACTIVITY_SLEEP_V2",
                activityData: sleepPayload
            })
        }
        return activities
    }

    private async getFitbitUserProfile(userToken: UserDeviceAndToken): Promise<FitbitUserProfile> {
        const profileUrl = `https://api.fitbit.com/1/user/${userToken.fitbit}/profile.json`
        const headers = {
            Authorization: `Bearer ${userToken.fitbit_token}`,
        }
        try {
            const fitbitResponse = await fetch(profileUrl, this.fetchHelper.get({headers}))
            // NOTE: intentionally await to catch expiry error
            return await this.fetchHelper.parseResponse<FitbitUserProfile>(fitbitResponse)
        } catch (e) {
            const responseBody = _.get(e, "context.upstream.responseBody", {})
            if (_.isArray(responseBody.errors) && (responseBody.errors as FitbitError[]).some(err => err.errorType === "expired_token")) {
                this.logger.info(`removing expired fitbit token for user_id: ${userToken.user_id}`)
                await this.atlasActivityService.fitbitLogout(String(userToken.user_id))
            }
            throw e
        }
    }
}
