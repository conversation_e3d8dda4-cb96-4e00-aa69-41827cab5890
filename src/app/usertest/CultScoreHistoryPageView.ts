import * as _ from "lodash"
import { ProductDetailPage, WidgetType, WidgetView } from "../common/views/WidgetView"
import { MAX_CULT_SCORE, MetricLevels, UserTest, UserTestMetricScore } from "@curefit/user-common"
import UserTestUtil from "./UserTestUtil"
import { CultScoreCard, CultScoreMetricDetailView } from "./CultScoreResultPageView"
import { TimeUtil } from "@curefit/util-common"
import { UserContext } from "@curefit/userinfo-common"

export class CultScoreHistoryWidget implements WidgetView {
    public widgetType: WidgetType = "CULTSCORE_HISTORY_WIDGET"
    constructor(
        public date: string,
        public card: CultScoreCard
    ) {
        this.date = date
        this.card = card
    }
}

class CultScoreHistoryPageView extends ProductDetailPage {

    constructor(userContext: UserContext, testResults: UserTest[], name: string) {
        super()
        _.map(testResults, testResult => {
            const cultScoreMetricScore = UserTestUtil.getMainTestMetricScore(testResult)
            const tz = userContext.userProfile.timezone

            this.widgets.push(this.getCultScoreHistoryWidget(userContext, testResult, cultScoreMetricScore, name))
        })
    }

    getCultScoreHistoryWidget(userContext: UserContext, testResult: UserTest, cultScoreMetricScore: UserTestMetricScore, name: string): CultScoreHistoryWidget {
        const card: CultScoreCard = this.getCultScoreCard(userContext, testResult, cultScoreMetricScore, name)
        const tz = userContext.userProfile.timezone
        const date: string = TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(testResult.createdAt), "DD MMMM YYYY")
        return new CultScoreHistoryWidget(date, card)
    }

    getCultScoreCard(userContext: UserContext, testResult: UserTest, cultScoreMetricScore: UserTestMetricScore, name: string): CultScoreCard {
        const metricDetails: CultScoreMetricDetailView[] = this.getMetricDetails(testResult.userTestMetricScores)
        const tz = userContext.userProfile.timezone
        const card: CultScoreCard = {
            userName: name,
            date: TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(testResult.createdAt), "DD/MM/YYYY"),
            cultScore: {
                title: "MY CULT SCORE",
                userScore: cultScoreMetricScore.score,
                total: MAX_CULT_SCORE
            },
            percentile: {
                title: "I'M BETTER THAN",
                percentileValue: `${cultScoreMetricScore.percentileRank.percentagePeople}%`,  // confirm if this is ever null/undefined
                subTitle: "OF THE PEOPLE!"
            },
            stats: {
                title: "MY STATS",
                metricDetails: metricDetails
            },
            footerText: "#WHATSYOURCULTSCORE"
        }
        return card
    }

    getMetricDetails(userTestMetricScores: UserTestMetricScore[]): CultScoreMetricDetailView[] {
        const metricDetails: CultScoreMetricDetailView[] = []
        _.map(userTestMetricScores, userTestMetricScore => {
            const testMetric = userTestMetricScore.testMetric
            if (testMetric.type !== "main") {
                metricDetails.push({
                    metricName: testMetric.name,
                    level: MetricLevels.indexOf(userTestMetricScore.meta.level) + 1
                })
            }
        })
        return metricDetails
    }
}

export default CultScoreHistoryPageView
