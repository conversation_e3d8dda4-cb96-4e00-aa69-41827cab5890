import * as _ from "lodash"
import { Action, WidgetType, WidgetView } from "../common/views/WidgetView"
import { TEST_SOURCE, TestDetail } from "@curefit/user-common"
import UserTestUtil from "./UserTestUtil"
import { UserTestDetailPageView } from "./UserTestViewBuilder"
import { ActionUtil } from "@curefit/base-utils"

export interface CultScoreSectionHeader {
    title: string
    info?: string
}

export interface CultScoreSectionItemCell {
    thumbnail?: string
    title?: string
    subTitle?: string
    footerText?: string
    seeMore?: Action
}

export interface CultScoreSectionView {
    header?: CultScoreSectionHeader,
    items: CultScoreSectionItemCell[]
}

export class CultScoreDetailWidget implements WidgetView {
    public widgetType: WidgetType = "CULTSCORE_DETAIL_WIDGET"
    constructor(public headerText: string, public sections: CultScoreSectionView[]) {
        this.headerText = headerText
        this.sections = sections
    }
}

class CultScoreDetailPageView extends UserTestDetailPageView {

    constructor(testDetail: TestDetail, testSource: TEST_SOURCE) {
        super()
        this.widgets.push(this.getCultScoreDetailWidget(testDetail))
        this.actions.push({
            title: "Begin",
            actionType: "NAVIGATION",
            url: `curefit://usertestflow?testId=${testDetail.id}&source=${testSource}`
        })
        this.pageName = "Cult Score"
    }

    getCultScoreDetailWidget(testDetail: TestDetail): CultScoreDetailWidget {
        const headerText = "WORKOUT\nAT HOME"
        const sections: CultScoreSectionView[] = []
        const warmupStep = testDetail.warmUp
        if (!_.isEmpty(warmupStep)) {
            const warmupDurationText = UserTestUtil.getDurationText(warmupStep.step.duration)
            const warmUpSection: CultScoreSectionView = {
                header: {
                    title: `1. Warm up - ${warmupDurationText}`
                },
                items: [{
                    thumbnail: UserTestUtil.getTestStepContentUrl(warmupStep.step.thumbnailContent, warmupStep, "STEP_THUMBNAIL", true),
                    subTitle: "Loosen your muscles and get ready to give your best"
                }]

            }
            sections.push(warmUpSection)
        }

        const workouts = testDetail.testSteps.length
        const workoutItemViews: CultScoreSectionItemCell[] = []
        _.forEach(testDetail.testSteps, testStep => {
            const workoutName = testStep.step.name
            const workoutDurationText = UserTestUtil.getDurationText(testStep.step.duration)
            const howToRelativeVideoUrl = UserTestUtil.getTestStepContentUrl(testStep.step.howToContent, testStep, "STEP_HOW_TO", true)
            const howToAbsoluteVideoUrl = UserTestUtil.getTestStepContentUrl(testStep.step.howToContent, testStep, "STEP_HOW_TO", false)
            const breakDuration = UserTestUtil.getDurationText(testStep.step.breakDuration)
            workoutItemViews.push({
                thumbnail: UserTestUtil.getTestStepContentUrl(testStep.step.thumbnailContent, testStep, "STEP_THUMBNAIL", true),
                title: workoutName,
                subTitle: workoutDurationText,
                footerText: `Rest ${breakDuration}`,
                seeMore: {
                    title: "SEE HOW",
                    actionType: "PLAY_VIDEO",
                    meta: {
                        showModal: true,
                        title: workoutName,
                        videoUrl: howToRelativeVideoUrl,
                        absoluteVideoUrl: howToAbsoluteVideoUrl
                    },
                    url: ActionUtil.videoUrl(howToRelativeVideoUrl),
                }
            })
        })
        const workoutsSection: CultScoreSectionView = {
            header: {
                title: "2. Exercises",
                info: `${workouts} Workouts`
            },
            items: workoutItemViews
        }
        const resultSection: CultScoreSectionView = {
            header: {
                title: "3. Get your Cult Score"
            },
            items: [{
                thumbnail: "/image/icons/cultscore/cultScore.png",
                subTitle: "Make sure you cool down by stretching your body and loosening your muscles"
            }]
        }
        sections.push(workoutsSection)
        sections.push(resultSection)
        return new CultScoreDetailWidget(headerText, sections)
    }


}
export default CultScoreDetailPageView
