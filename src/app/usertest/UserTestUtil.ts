import {
    MetricLevel,
    StepType,
    TestMetric,
    TestStep,
    USER_INPUT_SELECTOR,
    UserTest,
    UserTestContent,
    UserTestContentCategory,
    UserTestDuration,
    UserTestMetricScore,
    UserTestPercentileRank
} from "@curefit/user-common"
import { TestStepContent, USER_INPUT_TYPE, UserInput } from "./UserTestViewBuilder"
import { UrlPathBuilder } from "@curefit/product-common"
import * as _ from "lodash"
import { TimeUtil } from "@curefit/util-common"
import { Action } from "../common/views/WidgetView"
import { ActionUtil } from "@curefit/base-utils"
import { CULTSCORE_WORKOUT_CATEGORY_ID } from "../util/CultUtil"
import { UserContext } from "@curefit/userinfo-common"
import AppUtil from "../util/AppUtil"

class UserTestUtil {

    public static getMainTestMetric(testMetrics: TestMetric[]): TestMetric {
        return _.find(testMetrics, testMetric => { return testMetric.type === "main" })
    }

    public static getMainTestMetricScore(testResult: UserTest): UserTestMetricScore {
        const userTestMetricScores = testResult.userTestMetricScores
        const mainTestMetricScore = _.find(userTestMetricScores, userTestMetric => {
            return userTestMetric.testMetric.type === "main"
        })
        return mainTestMetricScore
    }

    public static getDurationText(duration: UserTestDuration): string {
        return duration.duration + " " + duration.unit
    }

    public static getDurationInSecs(duration: UserTestDuration): number {
        const durationValue = duration.duration
        const durationUnit = duration.unit
        switch (durationUnit) {
            case "min":
                return durationValue * TimeUtil.TIME_IN_SECONDS.MINUTE
            case "sec":
                return durationValue
            case "hour":
                return durationValue * TimeUtil.TIME_IN_SECONDS.HOUR
        }
    }

    public static getRestDuration(currentStep: TestStep, nextStep: TestStep): UserTestDuration {
        if (currentStep.step.breakDuration && currentStep.step.breakDuration.duration) {
            return currentStep.step.breakDuration
        }
        if (nextStep && nextStep.delayBeforeStart && nextStep.delayBeforeStart.duration) {
            return nextStep.delayBeforeStart
        }
        return undefined
    }

    public static getCultScorePercentileIcon(percentileRank: UserTestPercentileRank): string {
        switch (percentileRank.bucket) {
            case "good":
                return "/image/icons/cultscore/good.png"
            case "moderate":
                return "/image/icons/cultscore/moderate.png"
            case "excellent":
                return "/image/icons/cultscore/excellent.png"
            default:
                return "/image/icons/cultscore/good.png"
        }
    }

    public static getMetricLevelIconType(level: MetricLevel): string {
        if (level === "L1" || level == "L2") {
            return "moderate_progress"
        }
        else if (level === "L3" || level === "L4") {
            return "good_progress"
        }
        else if (level === "L5") {
            return "excellent_progress"
        }
    }

    static getUserInputType(userInputSelector: USER_INPUT_SELECTOR): USER_INPUT_TYPE {
        switch (userInputSelector) {
            case "range":
                return "NUMERIC"
            case "option":
                return "OPTION"
        }
    }

    static getUserOptionsValues(userInputSelector: USER_INPUT_SELECTOR, values: { [key: string]: string }): string[] {
        const options: string[] = []
        if (userInputSelector === "option") {
            const sortedKeys = Object.keys(values).sort((a, b) => {
                return a < b ? -1 : 1
            })
            sortedKeys.forEach((key) => {
                options.push(values[key])
            })
        }
        return options
    }

    public static getStepUserInputData(testSteps: TestStep[]): UserInput[] {
        const stepUserInputs: UserInput[] = []
        _.map(testSteps, (testStep) => {
            // build userInput data
            const userInput = testStep.step.userInput
            if (!_.isEmpty(userInput)) {
                stepUserInputs.push({
                    testStepId: testStep.id,
                    stepName: testStep.step.name,
                    inputType: this.getUserInputType(userInput.selector),
                    question: userInput.description,
                    title: "RECORD",
                    options: this.getUserOptionsValues(userInput.selector, userInput.values),
                    minValue: userInput.minValue,
                    maxValue: userInput.maxValue,
                    defaultValue: userInput.defaultValue,
                    unit: this.getTestStepUserInputUnit(testStep.step.type)
                })
            }
        })
        return stepUserInputs
    }

    public static getTestStepUserInputUnit(stepType: StepType): string {
        switch (stepType) {
            case "rep_count": return ""
            case "time": return "secs"
        }
    }

    public static getTestStepContent(testStep: TestStep, testContent: UserTestContent, category: UserTestContentCategory): TestStepContent {
        const content: TestStepContent = {
            id: this.getStepContentId(testStep.stepId, category),
            type: testContent.type,
            format: testContent.type === "video" ? "MP4" : testContent.type === "image" ? "JPG" : undefined,
            URL: this.getTestStepContentUrl(testContent, testStep, category, true),
            absoluteUrl: this.getTestStepContentUrl(testContent, testStep, category, false)
        }
        return content
    }

    public static getTestHeaderContent(testId: string, testContent: UserTestContent, category: UserTestContentCategory): TestStepContent {
        const content: TestStepContent = {
            id: this.getHeaderContentId(testId, category),
            type: testContent.type,
            format: testContent.type === "video" ? "MP4" : testContent.type === "image" ? "JPG" : undefined,
            URL: this.getUserTestHeaderContentUrl(testId, testContent, category, true),
            absoluteUrl: this.getUserTestHeaderContentUrl(testId, testContent, category, false)
        }
        return content
    }

    public static getStepContentId(stepId: string, category: UserTestContentCategory): string {
        return stepId + "_" + category
    }

    public static getHeaderContentId(testId: string, category: UserTestContentCategory): string {
        return testId + "_" + category
    }

    public static getContentFormat(stepContent: UserTestContent) {
        return stepContent.type === "video" ? "mp4" : "image" ? "jpg" : ""
    }

    public static getTestStepContentUrl(stepContent: UserTestContent, testStep: TestStep, category: UserTestContentCategory, relative: boolean = true): string {
        return UrlPathBuilder.getUserTestContentPath(stepContent, testStep.stepId, category, this.getContentFormat(stepContent), relative)
    }

    public static getUserTestHeaderContentUrl(testId: string, headerContent: UserTestContent, category: UserTestContentCategory, relative: boolean = true): string {
        return UrlPathBuilder.getUserTestContentPath(headerContent, testId, category, this.getContentFormat(headerContent), relative)
    }

    public static getUserTestActions(testId: string, title: string, userContext: UserContext, isInternalUser: boolean): Action[] {
        return [
            {
                // actionType: "NAVIGATION",
                // title: "Get Started",
                // url: ActionUtil.userTestDetailPage(testDetail.id, "DIY", "cultScoreSummary")
                actionType: "SELECT_WORKOUT_FORMAT_MODAL",
                title,
                meta: {
                    title,
                    cards: {
                        HOME: {
                            type: "CENTER",
                            title: "At Center",
                            texts: ["Group workout at the center", "Requires a smartphone with the app"],
                            action: {
                                title: "Book a Class",
                                actionType: "NAVIGATION",
                                url: ActionUtil.getBookCultClassUrl("FITNESS", AppUtil.isNewClassBookingSuppoted(userContext, isInternalUser), "cultScoreSummary", CULTSCORE_WORKOUT_CATEGORY_ID.toString())
                            }
                        },
                        CENTER: {
                            type: "HOME",
                            title: "At Home",
                            texts: ["Workout at your convenience of home", "1 min break between workouts"],
                            action: {
                                actionType: "NAVIGATION",
                                title: "Begin Workout",
                                url: ActionUtil.userTestDetailPage(testId, "DIY", "cultScoreSummary")
                            }
                        }
                    }
                }

            }
        ]
    }
}

export default UserTestUtil
