import { Action, ProductDetailPage, WidgetView } from "../common/views/WidgetView"
import { TEST_SOURCE, TestDetail, UserTest } from "@curefit/user-common"
import { ContentFormat } from "@curefit/base-common"
import { ContentType } from "@curefit/base-common"
import { User } from "@curefit/user-common"
import { UserContext } from "@curefit/userinfo-common"

export class UserTestDetailPageView {

    actions?: Action[] = []
    widgets: WidgetView[] = []
    pageName?: string
}

export type USER_INPUT_TYPE = "NUMERIC" | "OPTION"
export type TEST_STEP_TYPE = "WORKOUT" | "REST" | "WARMUP" | "PREWARMUP"

export type UserInputUnit = "REP" | "TIME"

export interface TestStepContent {
    id: string
    type: ContentType
    format: ContentFormat
    URL: string,
    absoluteUrl: string
}

export interface UserInput {
    testStepId: string
    stepName: string
    title: string
    question: string
    inputType: USER_INPUT_TYPE
    options?: string[]
    minValue?: string
    maxValue?: string
    defaultValue: string
    unit: string
}

export interface UserTestStepView {
    testStepId: string
    stepType: TEST_STEP_TYPE
    stepName: string
    instruction?: string
    content?: TestStepContent
    durationInSecs: number // seconds
    footerTexts?: string[]
    preStepStartText?: string
    preStepSubtitle?: string
    stepInstruction?: string
}

export interface TestScoreSummaryView {
    title?: string
    subTitle?: string
    actions?: Action[]
}

export class UserTestFlowView {
    userTestId: string
    totalWorkouts: number
    stepUserInputs: UserInput[] = []
    testStepViews: UserTestStepView[] = []
    testCompleteAction: Action
    abortTestAlertTitle?: {
        title: string
        message: string
    }
}

export class UserTestResultPageView {
    widgets: WidgetView[] = []
    actions?: Action[] = []
}

interface UserTestViewBuilder {

    buildTestSummaryPage(testDetail: TestDetail, userContext: UserContext, user: User): Promise<ProductDetailPage>
    buildTestDetailPage(testDetail: TestDetail, testSource: TEST_SOURCE): Promise<UserTestDetailPageView>
    buildTestFlowView(testDetail: TestDetail, userTest: UserTest): Promise<UserTestFlowView>
    buildTestCompletionPage(testresult: UserTest, user: User, userContext: UserContext): Promise<UserTestResultPageView>
    buildTestHistoryPage(userContext: UserContext, testresults: UserTest[], name: string): Promise<ProductDetailPage>
}
export default UserTestViewBuilder
