import { ProductDetailPage } from "../common/views/WidgetView"
import { inject, injectable } from "inversify"
import * as _ from "lodash"
import CultScoreSummaryPageView from "./CultScoreSummaryPageView"
import { TEST_SOURCE, TestDetail, UserTest } from "@curefit/user-common"
import UserTestViewBuilder, {
    UserTestDetailPageView,
    UserTestFlowView,
    UserTestResultPageView
} from "./UserTestViewBuilder"
import CultScoreDetailPageView from "./CultScoreDetailPageView"
import CultScoreTestFlowView from "./CultScoreTestFlowView"
import CultScoreResultPageView from "./CultScoreResultPageView"
import CultScoreHistoryPageView from "./CultScoreHistoryPageView"
import { ICatalogueService, CATALOG_CLIENT_TYPES } from "@curefit/catalog-client"
import { DIYPack } from "@curefit/diy-common"
import { User } from "@curefit/user-common"
import { UserContext } from "@curefit/userinfo-common"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService } from "@curefit/diy-client"

@injectable()
class CultScoreViewBuilder implements UserTestViewBuilder {

    constructor(
        @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) private diyService: IDIYFulfilmentService
    ) {
    }

    async buildTestSummaryPage(testDetail: TestDetail, userContext: UserContext, user: User): Promise<ProductDetailPage> {
        return new CultScoreSummaryPageView(testDetail, userContext, user)
    }

    async buildTestDetailPage(testDetail: TestDetail, testSource: TEST_SOURCE): Promise<UserTestDetailPageView> {
        return new CultScoreDetailPageView(testDetail, testSource)
    }

    async buildTestFlowView(testDetail: TestDetail, userTest: UserTest): Promise<UserTestFlowView> {
        return new CultScoreTestFlowView(testDetail, userTest)
    }

    async buildTestCompletionPage(testResult: UserTest, user: User, userContext: UserContext): Promise<UserTestResultPageView> {
        const packIds: string[] = []
        const recommendations = testResult.recommendation
        if (!_.isEmpty(recommendations)) {
            _.map(recommendations.sections, recommendation => {
                _.map(recommendation.atHomeRecommendations, atHomeRecommendation => {
                    packIds.push(atHomeRecommendation.diyPackId)
                })
            })
        }
        const userId = userContext.userProfile.userId
        const fitnessPacks: DIYPack[] = await this.diyService.getDIYFitnessPacksForIds(userId, packIds)
        const meditationPacksMap = await this.diyService.getDIYMeditationPacksForIds(userId, packIds)
        const fitnessPacksMap: Map<string, DIYPack> = new Map<string, DIYPack>()
        fitnessPacks.forEach((value) => {
            fitnessPacksMap.set(value.productId, value)
        })
        meditationPacksMap.forEach((value) => {
            fitnessPacksMap.set(value.productId, value)
        })
        return new CultScoreResultPageView(testResult, fitnessPacksMap, user, userContext)
    }

    async buildTestHistoryPage(userContext: UserContext, testResults: UserTest[], name: string): Promise<ProductDetailPage> {
        return new CultScoreHistoryPageView(userContext, testResults, name)
    }
}

export default CultScoreViewBuilder
