import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import UserTestViewBuilder from "./UserTestViewBuilder"
import { TestCode } from "@curefit/user-common"

@injectable()
class TestViewBuilderFactory {

    private TEST_CODE_TO_VIEW_BUILDER_MAP = new Map<TestCode, UserTestViewBuilder>()

    constructor(@inject(CUREFIT_API_TYPES.CultScoreViewBuilder) private cultScoreViewBuilder: UserTestViewBuilder) {
        this.TEST_CODE_TO_VIEW_BUILDER_MAP.set("cult_score", cultScoreViewBuilder)
    }

    getViewBuilder(testCode: TestCode): UserTestViewBuilder {
        return this.TEST_CODE_TO_VIEW_BUILDER_MAP.get(testCode)
    }

}
export default TestViewBuilderFactory
