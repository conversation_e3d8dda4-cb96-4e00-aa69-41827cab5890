import * as express from "express"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import * as _ from "lodash"
import { Container, inject } from "inversify"
import AuthMiddleware from "../auth/AuthMiddleware"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import { IUserTestService, USERTEST_CLIENT_TYPES } from "@curefit/user-test-client"
import { Session } from "@curefit/userinfo-common"
import { UserTestDetailPageView, UserTestFlowView, UserTestResultPageView } from "./UserTestViewBuilder"
import TestViewBuilderFactory from "./TestViewBuilderFactory"
import { ProductDetailPage } from "../common/views/WidgetView"
import UserTestUtil from "./UserTestUtil"
import { CacheHelper } from "../util/CacheHelper"
import { UserContext } from "@curefit/userinfo-common"

export function controllerFactory(kernel: Container) {
    @controller("/userTest",
        kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class UserTestController {

        constructor(
            @inject(USERTEST_CLIENT_TYPES.UserTestService) private userTestService: IUserTestService,
            @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
            @inject(CUREFIT_API_TYPES.TestViewBuilderFactory) private testViewBuilderFactory: TestViewBuilderFactory
        ) {
        }

        @httpGet("/summary/:testId")
        async getTestSummary(req: express.Request): Promise<ProductDetailPage> {
            const testId = req.params.testId
            const testDetail = await this.userTestService.getTestDetail(testId)
            const mainTestMetric = UserTestUtil.getMainTestMetric(testDetail.testMetrics)
            const testCode = mainTestMetric.code
            const userPromise = req.userContext.userPromise
            return this.testViewBuilderFactory.getViewBuilder(testCode).buildTestSummaryPage(testDetail, req.userContext as UserContext, await userPromise)
        }

        @httpGet("/detail/:testId")
        async getTestDetail(req: express.Request): Promise<UserTestDetailPageView> {
            const session: Session = req.session
            const testId = req.params.testId
            const source = req.query.source
            const testDetail = await this.userTestService.getTestDetail(testId)
            const mainTestMetric = UserTestUtil.getMainTestMetric(testDetail.testMetrics)
            const testCode = mainTestMetric.code
            return this.testViewBuilderFactory.getViewBuilder(testCode).buildTestDetailPage(testDetail, source)
        }

        @httpPost("/startTest/:testId")
        async startTest(req: express.Request): Promise<UserTestFlowView> {
            const session: Session = req.session
            const testId = req.params.testId
            const userId = session.userId
            const reqBody = req.body
            reqBody.userId = userId
            const userTest = await this.userTestService.createUserTest(testId, reqBody)
            const testDetail = await this.userTestService.getTestDetail(testId)
            const mainTestMetric = UserTestUtil.getMainTestMetric(testDetail.testMetrics)
            const testCode = mainTestMetric.code
            return this.testViewBuilderFactory.getViewBuilder(testCode).buildTestFlowView(testDetail, userTest)
        }

        @httpPost("/completeTest/:testId/:userTestId")
        async completeTest(req: express.Request): Promise<UserTestResultPageView> {
            const session: Session = req.session
            const testId = req.params.testId
            const userTestId = req.params.userTestId
            const reqBody = req.body
            const user = await this.userCache.getUser(session.userId)
            const userTestResult = await this.userTestService.completeUserTest(testId, userTestId, reqBody)
            const testMetrics = _.map(userTestResult.userTestMetricScores, userTestMetricScore => {
                return userTestMetricScore.testMetric
            })
            const mainTestMetric = UserTestUtil.getMainTestMetric(testMetrics)
            const testCode = mainTestMetric.code
            return this.testViewBuilderFactory.getViewBuilder(testCode).buildTestCompletionPage(userTestResult, user, req.userContext as UserContext)
        }

        @httpGet("/getHistory")
        async getHistory(req: express.Request): Promise<ProductDetailPage> {
            const session: Session = req.session
            const userId = session.userId
            const user = await this.userCache.getUser(session.userId)
            const userContext = req.userContext
            const userTestResults = await this.userTestService.searchUserTest(userId, undefined, undefined, false)
            let testMetrics
            if (!_.isEmpty(userTestResults)) {
                testMetrics = _.map(userTestResults[0].userTestMetricScores, userTestMetricScore => {
                    return userTestMetricScore.testMetric
                })
                const mainTestMetric = UserTestUtil.getMainTestMetric(testMetrics)
                const testCode = mainTestMetric.code
                const name = user.firstName + " " + user.lastName
                return this.testViewBuilderFactory.getViewBuilder(testCode).buildTestHistoryPage(userContext, userTestResults, name)
            }
            return undefined
        }

        @httpGet("/getResult")
        async getResult(req: express.Request): Promise<UserTestResultPageView> {
            const session: Session = req.session
            const user = await this.userCache.getUser(session.userId)
            const testId = req.query.testId
            const userTestId = req.query.userTestId
            let userTestResult
            if (!_.isEmpty(testId) && !_.isEmpty(userTestId)) {
                userTestResult = await this.userTestService.getUserTestDetail(testId, userTestId)
            } else {
                const userTestResults = await this.userTestService.searchUserTest(session.userId)
                if (!_.isEmpty(userTestResults)) {
                    userTestResult = userTestResults[0]
                }
            }
            if (!_.isEmpty(userTestResult)) {
                const testMetrics = _.map(userTestResult.userTestMetricScores, userTestMetricScore => {
                    return userTestMetricScore.testMetric
                })
                const mainTestMetric = UserTestUtil.getMainTestMetric(testMetrics)
                const testCode = mainTestMetric.code
                return this.testViewBuilderFactory.getViewBuilder(testCode).buildTestCompletionPage(userTestResult, user, req.userContext as UserContext)
            }
        }
    }

    return UserTestController
}

export default controllerFactory
