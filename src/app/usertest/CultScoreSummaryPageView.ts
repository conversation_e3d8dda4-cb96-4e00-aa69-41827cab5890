import { Action, Header, ProductDetailPage, WidgetType, WidgetView } from "../common/views/WidgetView"
import { TestDetail } from "@curefit/user-common"
import UserTestUtil from "./UserTestUtil"
import { ActionUtil } from "@curefit/base-utils"
import { UserContext } from "@curefit/userinfo-common"
import { User } from "@curefit/user-common"

export type STEP_TYPE = "SINGLE" | "REPEAT"

export interface StepData {
    icon: string,
    stepInfo: string | string[]
}

export interface CultScoreStepCard {
    stepType: STEP_TYPE
    data: StepData
}

export interface CultScoreInstruction {
    icon: string
    title: string
}

export class CultScoreSummaryWidget implements WidgetView {
    public widgetType: WidgetType = "CULTSCORE_SUMMARY_WIDGET"
    constructor(public title: string, public image: string, public summary: string, public videoAction?: Action) {
        this.title = title
        this.image = image
        this.summary = summary
        this.videoAction = videoAction
    }
}

export class CultScoreStepsInfoWidget implements WidgetView {
    public widgetType: WidgetType = "CULTSCORE_STEPS_INFO_WIDGET"
    constructor(
        public header: Header,
        public steps: CultScoreStepCard[]) {
        this.header = header
        this.steps = steps
    }
}

export class CultScoreInstructionsWidget implements WidgetView {
    public widgetType: WidgetType = "CULTSCORE_INSTRUCTIONS_WIDGET"
    constructor(
        public header: Header,
        public instructions: CultScoreInstruction[]) {
        this.header = header
        this.instructions = instructions
    }
}

class CultScoreSummaryPageView extends ProductDetailPage {

    constructor(testDetail: TestDetail, userContext: UserContext, user: User) {
        super()
        this.widgets.push(this.getCultScoreSummaryWidget(testDetail))
        this.widgets.push(this.getCultScoreStepsWidget(testDetail))
        this.widgets.push(this.getCultScoreInstructionsWidget())
        this.actions = this.getActions(testDetail, userContext, user.isInternalUser)
    }

    private getCultScoreSummaryWidget(testDetail: TestDetail): CultScoreSummaryWidget {
        const image = UserTestUtil.getUserTestHeaderContentUrl(testDetail.id, testDetail.thumbnailContent, "TEST_HEADER_HERO", true)
        const summary = testDetail.objective
        const relativeVideoUrl = UserTestUtil.getUserTestHeaderContentUrl(testDetail.id, testDetail.header, "TEST_HEADER", true)
        const videoAction: Action = {
            actionType: "NAVIGATION",
            url: ActionUtil.videoUrl(relativeVideoUrl, undefined, undefined, "LIGHT")
        }
        return new CultScoreSummaryWidget("Cult Score", image, summary, videoAction)
    }

    private getCultScoreStepsWidget(testDetail: TestDetail): CultScoreStepsInfoWidget {
        const durationText = UserTestUtil.getDurationText(testDetail.duration)
        const workouts = testDetail.testSteps.length
        const warmupDurationText = UserTestUtil.getDurationText(testDetail.warmUp.step.duration)
        const restDurationText = UserTestUtil.getDurationText(testDetail.testSteps[0].step.breakDuration)
        const subTitle = `The complete workout takes ${durationText}. It includes ${warmupDurationText}s of warmup and ${workouts} exercises of 30 sec - 2 min each with ${restDurationText} break between exercises. After each workout you will be required to record your performance on the app`
        const header: Header = {
            title: "How it works",
            subTitle: subTitle
        }

        const steps: CultScoreStepCard[] = []
        steps.push(
            {
                stepType: "SINGLE",
                data: {
                    icon: "/image/icons/cultscore/5minIcon.png",
                    stepInfo: `Warmup for ${warmupDurationText}`
                }

            },
            {
                stepType: "REPEAT",
                data: {
                    icon: "/image/icons/cultscore/repeatStepsIconNew.png",
                    stepInfo: [
                        `${workouts} Workouts`,
                        `${restDurationText} rest between workouts to record score`
                    ]
                }
            },
            {
                stepType: "SINGLE",
                data: {
                    icon: "/image/icons/cultscore/tickIcon.png",
                    stepInfo: "Get your Cult Score"
                }

            }
        )

        return new CultScoreStepsInfoWidget(header, steps)
    }

    private getCultScoreInstructionsWidget(): CultScoreInstructionsWidget {

        const header: Header = {
            title: "What you will need"
        }
        const instructions: CultScoreInstruction[] = []
        instructions.push(
            {
                icon: "/image/icons/cultscore/phone.png",
                title: "Phone"
            },
            {
                icon: "/image/icons/cultscore/mat.png",
                title: "Mat"
            },
            {
                icon: "/image/icons/cultscore/water.png",
                title: "Water"
            },
            {
                icon: "/image/icons/cultscore/towel.png",
                title: "Towel"
            }
        )

        return new CultScoreInstructionsWidget(header, instructions)
    }
    private getActions(testDetail: TestDetail, userContext: UserContext, isInternalUser: boolean): Action[] {
        if (userContext.sessionInfo.isUserLoggedIn === false) {
            return [
                {
                    actionType: "SHOW_ALERT_MODAL",
                    title: "Get Started",
                    meta: {
                        title: "Login Required!",
                        subTitle: "Please login to continue",
                        actions: [{ actionType: "LOGOUT", title: "Login" }]
                    }
                }
            ]
        }
        else {
            return UserTestUtil.getUserTestActions(testDetail.id, "Get Started", userContext, isInternalUser)
        }
    }

}
export default CultScoreSummaryPageView
