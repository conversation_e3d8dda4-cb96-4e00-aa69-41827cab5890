import * as _ from "lodash"
import { Action, WidgetType, WidgetView } from "../common/views/WidgetView"
import {
    MAX_CULT_SCORE,
    MetricLevels,
    UserTest,
    UserTestMetricScore,
    UserTestPercentileRank,
    UserTestRecommendationSection
} from "@curefit/user-common"
import UserTestUtil from "./UserTestUtil"
import { ActionUtil, ActionUtilV1, SeoUrlParams } from "@curefit/base-utils"
import { UserTestResultPageView } from "./UserTestViewBuilder"
import { TimeUtil } from "@curefit/util-common"
import { UrlPathBuilder } from "@curefit/product-common"
import { DIYPack } from "@curefit/diy-common"
import { User } from "@curefit/user-common"
import { UserContext } from "@curefit/userinfo-common"

export type SocialChannel = "FACEBOOK" | "TWITTER" | "WHATSAPP" | "INSTAGRAM"
export const SocialChannels: SocialChannel[] = ["FACEBOOK", "TWITTER", "WHATSAPP", "INSTAGRAM"]
export const NUMBER_OF_LEVELS = MetricLevels.length
export const LevelDetails: MetricLevelDetail[] = [{ color: "#ee676d" }, { color: "#f88a44" }, { color: "#f88a44" }, { color: "#08bac1" }, { color: "#08bac1" }]

export const CULTSCORE_PAGE_UPDATE_VERSION_ANDROID = 7.23
export const CULTSCORE_PAGE_UPDATE_VERSION_IOS = 7.23
export const CULTSCORE_PAGE_UPDATE_VERSION_ANDROID_INTERNAL = 7.22
export const CULTSCORE_PAGE_UPDATE_VERSION_IOS_INTERNAL = 7.22

export class CultScorePercentileWidget implements WidgetView {
    public widgetType: WidgetType = "CULTSCORE_PERCENTILE_WIDGET"
    constructor(
        public image: string,
        public title: string,
        public subTitle: string
    ) {
        this.image = image
        this.title = title
        this.subTitle = subTitle
    }
}

export class CultScoreResultWidget implements WidgetView {
    public widgetType: WidgetType = "CULTSCORE_RESULT_WIDGET"
    constructor(
        public title: string,
        public userScore: number,
        public total: number
    ) {
        this.title = title
        this.userScore = userScore
        this.total = total
    }
}

export class CultScoreShareWidget implements WidgetView {
    public widgetType: WidgetType = "CULTSCORE_SHARE_WIDGET"
    constructor(
        public channels: SocialChannel[],
        public shareTitle?: string,
        public shareMessage?: string,
    ) {
        this.channels = channels
        this.shareTitle = shareTitle
        this.shareMessage = shareMessage
    }
}

export interface CultScoreMetricDetailView {
    metricName: string
    level: number,
    icon?: string,
    instruction?: string
    numberOfLevels?: number
}

export interface MetricLevelDetail {
    color: string
}

export class CultScoreMetricsDetailWidget implements WidgetView {
    public widgetType: WidgetType = "CULTSCORE_METRICS_DETAIL_WIDGET"
    constructor(
        public levelDetails: MetricLevelDetail[],
        public metricDetails: CultScoreMetricDetailView[]
    ) {
        this.levelDetails = levelDetails
        this.metricDetails = metricDetails
    }
}

export interface CultScoreCard {
    userName: string
    date: string
    cultScore: {
        title: string
        userScore: number
        total: number
    }
    percentile: {
        title: string
        percentileValue: string
        subTitle: string
    }
    stats: {
        title: string
        metricDetails: CultScoreMetricDetailView[]
    }
    footerText: string
}

export class CultScoreCardWidget implements WidgetView {
    public widgetType: WidgetType = "CULTSCORE_CARD_WIDGET"
    constructor(
        public card: CultScoreCard,
        public action?: Action
    ) {
        this.card = card
        this.action = action
    }
}

export interface CultScoreMetricView {
    title: string
    description: string
    levelDetails: MetricLevelDetail[]
    improvementAreas: CultScoreMetricDetailView[]
    maintainAreas: CultScoreMetricDetailView[]
}

export interface RecommendationView {
    type: "home" | "center"
    title: string
    image?: string
    action?: Action
    packIntroAction?: string
    description?: string[]
}

export interface RecommendationCategory {
    title: string
    recommendations: RecommendationView[]
}

export interface CultScorePlanView {
    title: string
    description: string
    recommendationPlan: RecommendationCategory[]
}

export class CultScorePlanWidget implements WidgetView {
    public widgetType: WidgetType = "CULTSCORE_PLAN_WIDGET"
    constructor(
        public metrics: CultScoreMetricView,
        public plan: CultScorePlanView
    ) {
        this.metrics = metrics
        this.plan = plan
    }
}

class CultScoreResultPageView extends UserTestResultPageView {

    constructor(testResult: UserTest, packIds: Map<string, DIYPack>, user: User, userContext: UserContext) {
        super()
        const cultScoreMetricScore = UserTestUtil.getMainTestMetricScore(testResult)
        const name = user.firstName + " " + user.lastName
        if (this.isNewPageSupported(userContext.sessionInfo.osName, userContext.sessionInfo.appVersion, user.isInternalUser)) {
            this.widgets.push(this.getCultScoreCardWidget(userContext, testResult, cultScoreMetricScore, name))
            if (!_.isEmpty(testResult.recommendation)) {
                this.widgets.push(this.getCultScorePlanWidget(testResult.userTestMetricScores, testResult.recommendation.sections, packIds, userContext))
            }
        } else {
            this.widgets.push(new CultScoreResultWidget("Your Cult Score", cultScoreMetricScore.score, MAX_CULT_SCORE))
            this.widgets.push(this.getCultScorePercentileWidget(cultScoreMetricScore.percentileRank))
            this.widgets.push(this.getCultScoreMetricsDetailsWidget(testResult.userTestMetricScores))
        }
        this.actions.push({
            actionType: "SHARE_SCREENSHOT",
            title: "Share Score",
            meta: {
                shareTitle: `My Cult Score: ${cultScoreMetricScore.score}/${MAX_CULT_SCORE}`,
                shareMessage: `My Cult Score is ${cultScoreMetricScore.score}. #WHATSYOURCULTSCORE`,
            }
        })
    }

    isNewPageSupported(osName: string, appVersion: number, isInternalUser: boolean) {
        if (isInternalUser) {
            if (osName.toLowerCase() === "android") {
                return appVersion >= CULTSCORE_PAGE_UPDATE_VERSION_ANDROID_INTERNAL
            } else {
                return appVersion >= CULTSCORE_PAGE_UPDATE_VERSION_IOS_INTERNAL
            }
        } else {
            if (osName.toLowerCase() === "android") {
                return appVersion >= CULTSCORE_PAGE_UPDATE_VERSION_ANDROID
            } else {
                return appVersion >= CULTSCORE_PAGE_UPDATE_VERSION_IOS
            }
        }
    }

    getCultScorePercentileWidget(percentileRank: UserTestPercentileRank): CultScorePercentileWidget {
        if (!_.isEmpty(percentileRank)) {
            const image = UserTestUtil.getCultScorePercentileIcon(percentileRank)
            const title = "You are better than"
            const subTitle = `${percentileRank.percentagePeople}% of the people`
            return new CultScorePercentileWidget(image, title, subTitle)
        }
    }

    getCultScoreMetricsDetailsWidget(userTestMetricScores: UserTestMetricScore[]): CultScoreMetricsDetailWidget {
        const metricDetails: CultScoreMetricDetailView[] = []
        _.map(userTestMetricScores, userTestMetricScore => {
            const testMetric = userTestMetricScore.testMetric
            if (testMetric.type !== "main") {
                metricDetails.push({
                    metricName: testMetric.name,
                    level: MetricLevels.indexOf(userTestMetricScore.meta.level),
                    icon: UserTestUtil.getMetricLevelIconType(userTestMetricScore.meta.level),
                    instruction: userTestMetricScore.meta.toNextLevel
                })
            }
        })
        return new CultScoreMetricsDetailWidget(LevelDetails, metricDetails)
    }

    getCultScoreCardWidget(userContext: UserContext, testResult: UserTest, cultScoreMetricScore: UserTestMetricScore, name: string): CultScoreCardWidget {
        const card: CultScoreCard = this.getCultScoreCard(userContext, testResult, cultScoreMetricScore, name)
        let action: Action
        if (testResult.firstTest === undefined || testResult.firstTest === false) {
            action = {
                title: "VIEW HISTORY",
                actionType: "NAVIGATION",
                url: ActionUtil.getUserTestHistory(),
                meta: {
                    title: "Cult Score History"
                }
            }
        }
        return new CultScoreCardWidget(card, action)
    }

    getCultScoreCard(userContext: UserContext, testResult: UserTest, cultScoreMetricScore: UserTestMetricScore, name: string): CultScoreCard {
        const metricDetails: CultScoreMetricDetailView[] = this.getMetricDetails(testResult.userTestMetricScores)
        const card: CultScoreCard = {
            userName: name,
            date: TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, TimeUtil.parseDateFromEpoch(testResult.createdAt), "DD/MM/YYYY"),
            cultScore: {
                title: "MY CULT SCORE",
                userScore: cultScoreMetricScore.score,
                total: MAX_CULT_SCORE
            },
            percentile: {
                title: "I'M BETTER THAN",
                percentileValue: `${cultScoreMetricScore.percentileRank.percentagePeople}%`,  // confirm if this is ever null/undefined
                subTitle: "OF THE PEOPLE!"
            },
            stats: {
                title: "MY STATS",
                metricDetails: metricDetails
            },
            footerText: "#WHATSYOURCULTSCORE"
        }
        return card
    }

    getMetricDetails(userTestMetricScores: UserTestMetricScore[]): CultScoreMetricDetailView[] {
        const metricDetails: CultScoreMetricDetailView[] = []
        _.map(userTestMetricScores, userTestMetricScore => {
            const testMetric = userTestMetricScore.testMetric
            if (testMetric.name === "Muscle Endurance") {
                testMetric.name = "Endurance"
            }
            if (testMetric.type !== "main") {
                metricDetails.push({
                    metricName: testMetric.name,
                    level: MetricLevels.indexOf(userTestMetricScore.meta.level) + 1,
                    numberOfLevels: NUMBER_OF_LEVELS
                })
            }
        })
        return metricDetails
    }

    getCultScorePlanWidget(userTestMetricScores: UserTestMetricScore[], userTestRecommendations: UserTestRecommendationSection[], packsMap: Map<string, DIYPack>, userContext: UserContext): CultScorePlanWidget {
        const improvementAreas: CultScoreMetricDetailView[] = []
        const maintainAreas: CultScoreMetricDetailView[] = []
        _.map(userTestMetricScores, userTestMetricScore => {
            const testMetric = userTestMetricScore.testMetric
            if (testMetric.name === "Muscle Endurance") {
                testMetric.name = "Endurance"
            }
            const level = MetricLevels.indexOf(userTestMetricScore.meta.level) + 1
            if (testMetric.type !== "main") {
                if (level < 4) {
                    improvementAreas.push({
                        metricName: testMetric.name,
                        level: level,
                        numberOfLevels: NUMBER_OF_LEVELS
                    })
                } else {
                    maintainAreas.push({
                        metricName: testMetric.name,
                        level: level,
                        numberOfLevels: NUMBER_OF_LEVELS
                    })
                }
            }
        })
        improvementAreas.sort((a: CultScoreMetricDetailView, b: CultScoreMetricDetailView) => {
            const diff = b.level - a.level
            return diff > 0 ? 1 : diff < 0 ? -1 : diff
        })
        maintainAreas.sort((a: CultScoreMetricDetailView, b: CultScoreMetricDetailView) => {
            const diff = b.level - a.level
            return diff > 0 ? 1 : diff < 0 ? -1 : diff
        })
        const metrics: CultScoreMetricView = {
            title: "Improve your Cult Score",
            description: "Based on your current cult score, we have suggestions to take you to the next step",
            levelDetails: LevelDetails,
            improvementAreas: improvementAreas,
            maintainAreas: maintainAreas
        }

        const recommendationPlan: RecommendationCategory[] = []
        _.map(userTestRecommendations, userTestRecommendation => {
            recommendationPlan.push({
                title: userTestRecommendation.durationDays
                    + (userTestRecommendation.durationDays === 1 ? " Day" : " Days")
                    + " / Week - " + userTestRecommendation.workoutType,
                recommendations: this.getRecommendations(userTestRecommendation, packsMap, userContext)
            })
        })
        const plan: CultScorePlanView = {
            title: "Plan",
            description: "We have some suggestions for you to improve and maintain your cult score. Go through the steps below.",
            recommendationPlan: recommendationPlan
        }
        return new CultScorePlanWidget(metrics, plan)
    }

    getRecommendations(userTestRecommendation: UserTestRecommendationSection, packsMap: Map<string, DIYPack>, userContext: UserContext): RecommendationView[] {
        const recommendations: RecommendationView[] = []
        let workoutNames: string[] = []
        const workoutIds: string[] = []
        const uniqueWorkoutNames: Set<string> = new Set<string>()
        _.map(userTestRecommendation.inCentreRecommendations, recommendation => {
            uniqueWorkoutNames.add(recommendation.workoutName)
            workoutIds.push(recommendation.workoutCategoryId)
        })
        workoutNames = Array.from(uniqueWorkoutNames.keys())
        recommendations.push({
            type: "center",
            title: "BOOK CLASS",
            description: workoutNames,
            action: {
                actionType: "NAVIGATION",
                url: ActionUtil.bookCultClassV2MultipleWorkouts("FITNESS", "cultScoreRecommendation", workoutIds)
            }
        })
        _.map(userTestRecommendation.atHomeRecommendations, recommendation => {
            const pack = packsMap.get(recommendation.diyPackId)
            const seoParams: SeoUrlParams = {
              productName: pack.title
            }
            recommendations.push({
                type: "home",
                title: "AT HOME",
                image: UrlPathBuilder.prefixSlash(pack.imageDetails.magazineImage),
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtilV1.diyPackProductPage(pack, userContext.sessionInfo.userAgent)
                },
                packIntroAction: (pack.packIntroContentId ? (ActionUtil.videoUrl("curefit-content/video/" + pack.packIntroContentId + ".mp4", UrlPathBuilder.prefixSlash(pack.imageDetails.heroImage)) + `&contentId=${pack.packIntroContentId}`) : undefined)
            })
        })
        return recommendations
    }

}
export default CultScoreResultPageView
