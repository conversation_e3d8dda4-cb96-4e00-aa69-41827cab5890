import * as _ from "lodash"
import { TestDetail, TestStep, UserTest } from "@curefit/user-common"
import UserTestUtil from "./UserTestUtil"
import { UserTestFlowView, UserTestStepView } from "./UserTestViewBuilder"
import { ActionUtil } from "@curefit/base-utils"

class CultScoreTestFlowView extends UserTestFlowView {

    constructor(testDetail: TestDetail, userTest: UserTest) {
        super()
        // sort test steps according to sequence
        const sortedTestSteps = testDetail.testSteps.sort((a, b) => {
            return (a.sequence < b.sequence) ? -1 : 1
        })
        this.totalWorkouts = sortedTestSteps.length
        this.userTestId = userTest.id
        const warmupStep: TestStep = testDetail.warmUp
        if (warmupStep) {
            this.testStepViews.push(this.getPreWarmUpStepView(warmupStep))
            // For warmup, next step will be 0th index test step
            const nextStep = sortedTestSteps[0]
            const warmUpStepView = this.getWarmUpStepView(warmupStep, nextStep)
            this.testStepViews.push(warmUpStepView)
            const restStepView = this.getRestStepView(warmupStep, nextStep)
            if (restStepView) {
                this.testStepViews.push(restStepView)
            }
        }


        // build steps for all test steps
        _.map(sortedTestSteps, (testStep, index) => {
            const isLastStep = this.isLastStep(sortedTestSteps, index + 1)
            const nextTestStep = !isLastStep ? sortedTestSteps[index + 1] : undefined
            const testStepView = this.getWorkoutStepView(testStep, nextTestStep)
            this.testStepViews.push(testStepView)
            // add rest step if its present
            const restStepView = this.getRestStepView(testStep, nextTestStep, isLastStep)
            if (restStepView) {
                this.testStepViews.push(restStepView)
            }
        })

        // build step user input data
        this.stepUserInputs = UserTestUtil.getStepUserInputData(sortedTestSteps)

        this.testCompleteAction = {
            actionType: "NAVIGATION",
            url: ActionUtil.userTestScoreInputPage(testDetail.id, userTest.id),
            meta: {
                title: "SUMMARY",
                subTitle: "You can edit the Scores",
                userTestId: userTest.id,
                stepUserInputs: this.stepUserInputs,
                actions: [
                    {
                        actionType: "NAVIGATION",
                        title: "View Score",
                        url: ActionUtil.userTestCompletionPage(testDetail.id, userTest.id)
                    }
                ]
            }
        }

        this.abortTestAlertTitle = {
            title: "Are you sure you want to quit?",
            message: "If you quit now, you will need to start the test again to get your Cult Score"
        }
    }

    getPreWarmUpStepView(warmupStep: TestStep): UserTestStepView {
        const preWarmUpStepView: UserTestStepView = {
            testStepId: warmupStep.id,
            stepType: "PREWARMUP",
            stepName: warmupStep.step.name,
            preStepSubtitle: "STARTS IN",
            durationInSecs: 3
        }
        return preWarmUpStepView
    }

    getWarmUpStepView(warmupStep: TestStep, nextStep: TestStep): UserTestStepView {
        const warmUpStepDuration = warmupStep.step.duration
        const warmUpStepView: UserTestStepView = {
            testStepId: warmupStep.id,
            stepType: "WARMUP",
            stepName: warmupStep.step.name,
            durationInSecs: UserTestUtil.getDurationInSecs(warmUpStepDuration),
            content: UserTestUtil.getTestStepContent(warmupStep, warmupStep.step.instructionContent, "STEP_INSTRUCTION"),
            footerTexts: this.getFooterTexts(warmupStep, nextStep, false, true)
        }
        return warmUpStepView
    }

    getWorkoutStepView(testStep: TestStep, nextStep: TestStep): UserTestStepView {
        const testStepDuration = testStep.step.duration
        const testStepView: UserTestStepView = {
            testStepId: testStep.id,
            stepType: "WORKOUT",
            stepName: testStep.step.name,
            durationInSecs: UserTestUtil.getDurationInSecs(testStepDuration),
            content: UserTestUtil.getTestStepContent(testStep, testStep.step.instructionContent, "STEP_INSTRUCTION"),
            footerTexts: this.getFooterTexts(testStep, nextStep),
            preStepStartText: "GET READY",
            stepInstruction: this.getStepInstruction(testStep)
        }
        return testStepView
    }

    getRestStepView(currentStep: TestStep, nextStep: TestStep, isLastStep: boolean = false): UserTestStepView {
        const hasRestAfterStep = this.hasRestAfterStep(currentStep, nextStep)
        if (hasRestAfterStep) {
            const restDuration = UserTestUtil.getRestDuration(currentStep, nextStep)
            const restStepView: UserTestStepView = {
                stepType: "REST",
                stepName: "REST",
                preStepSubtitle: !isLastStep ? "NEXT WORKOUT IN" : undefined,
                durationInSecs: UserTestUtil.getDurationInSecs(restDuration),
                footerTexts: this.getFooterTexts(currentStep, nextStep, true),
                testStepId: currentStep.id
            }
            return restStepView
        }
    }

    hasRestAfterStep(currentStep: TestStep, nextStep: TestStep): boolean {
        const breakDuration = UserTestUtil.getRestDuration(currentStep, nextStep)
        if (breakDuration) {
            return true
        }
        return false
    }

    getFooterTexts(currentStep: TestStep, nextStep: TestStep, isRestStep: boolean = false, isWarmupStep: boolean = false): string[] {
        if (isRestStep) {
            const footerText = !_.isEmpty(nextStep) ? `NEXT: ${nextStep.step.name.toUpperCase()}` : ""
            return [footerText]
        }
        const footerTexts: string[] = []
        const breakDuration = UserTestUtil.getRestDuration(currentStep, nextStep)
        if (!_.isEmpty(breakDuration)) {
            footerTexts.push(`NEXT: ${UserTestUtil.getDurationText(breakDuration).toUpperCase()} REST`)
        }
        else if (!_.isEmpty(nextStep)) {
            const upperCaseStepName = nextStep.step.name.toUpperCase()
            footerTexts.push(`NEXT: ${upperCaseStepName}`)
        }
        return footerTexts
    }

    getStepInstruction(testStep: TestStep): string {
        if (testStep.step.type === "rep_count") {
            return "COUNT YOUR REPS"
        }
        else if (testStep.step.type === "time" || testStep.step.type === "posture") {
            return "COUNT YOUR TIME"
        }
    }

    isLastStep(testSteps: TestStep[], currentIndex: number) {
        return (currentIndex) > testSteps.length - 1
    }
}
export default CultScoreTestFlowView
