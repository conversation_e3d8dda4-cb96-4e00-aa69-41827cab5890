import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import * as _ from "lodash"
import { BASE_TYPES, Logger } from "@curefit/base"
import { LoggableActivitySlot } from "./ActivityLogging"
import {
    CATALOGUE_ENDPOINTS,
    FitnessLoggableItem,
    MindLoggableItem,
    SearchRequestParams
} from "@curefit/maverick-common"
import { IMaverickService, MAVERICK_TYPES } from "@curefit/maverick-client"
import { ILoggingService, LOGGING_CLIENT_TYPES } from "@curefit/logging-client"
import { MealSlot, MealSlots } from "@curefit/eat-common"
import { TimeUtil } from "@curefit/util-common"
import { GMF_CLIENT_TYPES, IGMFClient } from "@curefit/gmf-client"
import { InternalValueDisplayValue } from "@curefit/gmf-common"
import LoggableSlotsMetaCache from "./LoggableSlotsMetaCache"
import { SlotUtil } from "@curefit/eat-util"
import { HourMin } from "@curefit/base-common"
import { IRecommendationService, RECOMMENDATION_CLIENT_TYPES } from "@curefit/recommendation-client"

export interface IActivityLoggingBusiness {
    searchByWorkoutId(workoutId: string): Promise<FitnessLoggableItem>

    searchByMindWorkoutId(workoutId: string): Promise<MindLoggableItem>

    getLoggableSlotName(slotId: LoggableActivitySlot): string

    getLoggedSlotTime(slotId: LoggableActivitySlot): { start: HourMin, end: HourMin }
}

@injectable()
export class ActivityLoggingBusiness implements IActivityLoggingBusiness {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(MAVERICK_TYPES.IMaverickService) private maverickService: IMaverickService,
        @inject(LOGGING_CLIENT_TYPES.LoggingService) private loggingService: ILoggingService,
        @inject(RECOMMENDATION_CLIENT_TYPES.IRecommendationService) private recommendationService: IRecommendationService,
        @inject(GMF_CLIENT_TYPES.IGMFClient) private gmfService: IGMFClient,
        @inject(CUREFIT_API_TYPES.LoggableSlotsMetaCache) private loggableSlotsMetaCache: LoggableSlotsMetaCache
    ) {
    }


    public async searchByWorkoutId(workoutId: string): Promise<FitnessLoggableItem> {
        const searchRequestParams: SearchRequestParams = {
            indexPoint: CATALOGUE_ENDPOINTS.FITNESS.FitnessLoggableItem,
            keywords: [
                {
                    key: "itemCode",
                    value: workoutId
                }
            ],
            resultSize: 1
        }
        const searchResult: FitnessLoggableItem[] = await this.maverickService.getSearchItems<FitnessLoggableItem>(searchRequestParams)
        return searchResult[0]
    }

    public async searchByMindWorkoutId(workoutId: string): Promise<MindLoggableItem> {
        const searchRequestParams: SearchRequestParams = {
            indexPoint: CATALOGUE_ENDPOINTS.MIND.MindLoggableItem,
            keywords: [
                {
                    key: "itemCode",
                    value: workoutId
                }
            ],
            resultSize: 1
        }
        const searchResult: MindLoggableItem[] = await this.maverickService.getSearchItems<MindLoggableItem>(searchRequestParams)
        return searchResult[0]
    }

    public getLoggableSlotName(slotId: LoggableActivitySlot): string {
        const slotMeta: InternalValueDisplayValue = this.loggableSlotsMetaCache.getLoggableSlotDataById(slotId)
        if (_.isNil(slotMeta)) {
            return ""
        } else {
            return slotMeta.displayValue
        }
    }

    public getLoggedSlotTime(slotId: LoggableActivitySlot): { start: HourMin, end: HourMin } {
        if (MealSlots.findIndex((mealSlot) => mealSlot === slotId) !== -1) {
            return SlotUtil.getMealSlotTimes(<MealSlot>slotId)
        }
        if (slotId === "PRE_BREAKFAST" || slotId === "MID_MORNING_SNACK") {
            const slotTime: { start: HourMin, end: HourMin } = SlotUtil.getMealSlotTimes("BREAKFAST")
            slotTime.start = TimeUtil.addHourMin(slotTime.start, -30)
            return slotTime
        }
        if (slotId === "MID_DAY_MEAL") {
            return SlotUtil.getMealSlotTimes("LUNCH")
        }
        if (slotId === "PRE_WORKOUT") {
            const slotTime: { start: HourMin, end: HourMin } = SlotUtil.getMealSlotTimes("SNACKS")
            slotTime.start = TimeUtil.addHourMin(slotTime.start, -30)
            return slotTime
        }
        if (slotId === "POST_WORKOUT") {
            const slotTime: { start: HourMin, end: HourMin } = SlotUtil.getMealSlotTimes("SNACKS")
            slotTime.start = TimeUtil.addHourMin(slotTime.start, 30)
            return slotTime
        }
        if (slotId === "EARLY_AFTERNOON_SNACK") {
            const slotTime: { start: HourMin, end: HourMin } = SlotUtil.getMealSlotTimes("SNACKS")
            slotTime.start = TimeUtil.addHourMin(slotTime.start, -45)
            return slotTime
        }
        if (slotId === "LATE_EVENING_SNACK") {
            const slotTime: { start: HourMin, end: HourMin } = SlotUtil.getMealSlotTimes("SNACKS")
            slotTime.start = TimeUtil.addHourMin(slotTime.start, 45)
            return slotTime
        }
        if (slotId === "BEDTIME_SNACK") {
            const slotTime: { start: HourMin, end: HourMin } = SlotUtil.getMealSlotTimes("DINNER")
            slotTime.start = TimeUtil.addHourMin(slotTime.start, 30)
            return slotTime
        }
        // Default to dinner time
        return SlotUtil.getMealSlotTimes("DINNER")
    }
}
