import { inject, injectable } from "inversify"
import * as _ from "lodash"
import { DEFAULT_CACHE_REFRESH_TIME_IN_SECONDS, InMemoryCacheService } from "@curefit/memory-cache"
import { InternalValueDisplayValue } from "@curefit/gmf-common"
import { ILoggingService, LOGGING_CLIENT_TYPES } from "@curefit/logging-client"
import { DEFAULT_MEAL_SLOT_ID, LoggableActivitySlot, LoggableActivityType } from "./ActivityLogging"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

type WrappedInternalValueDisplayValue = {
    internalValueDisplayValue: InternalValueDisplayValue,
    loggableActivityType: LoggableActivityType
}
@injectable()
class LoggableSlotsMetaCache extends InMemoryCacheService<Map<string, WrappedInternalValueDisplayValue>> {

    constructor(
        @inject(LOGGING_CLIENT_TYPES.LoggingService) private loggingService: ILoggingService,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger, 33 * 60)
        this.load("LoggableSlotsMetaCache")
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    public async loadData(): Promise<Map<string, WrappedInternalValueDisplayValue>> {
        const loggableMealSlots: InternalValueDisplayValue[] = await this.loggingService.getMealSlots()
        const LoggableSlotsMap: Map<string, WrappedInternalValueDisplayValue> = this.getDefaultValues()
        loggableMealSlots.forEach((loggableMealSlotMeta: InternalValueDisplayValue) => {
            LoggableSlotsMap.set(loggableMealSlotMeta.internalValue, {
                internalValueDisplayValue: loggableMealSlotMeta,
                loggableActivityType: "DIET"
            })
        })
        return LoggableSlotsMap
    }

    public getLoggableSlotDataById(name: string): InternalValueDisplayValue {
        const wrappedData: WrappedInternalValueDisplayValue = this.cache.get(name)
        if (_.isNil(wrappedData)) return undefined
        else return wrappedData.internalValueDisplayValue
    }

    public getDietSlotById(slotId: string): InternalValueDisplayValue {
        const wrappedData: WrappedInternalValueDisplayValue = this.cache.get(slotId)
        if (_.isNil(wrappedData)) return undefined
        if (wrappedData.loggableActivityType !== "DIET") return undefined
        return wrappedData.internalValueDisplayValue
    }

    public getLoggableSlotDataByActivityType(loggableActivityType: LoggableActivityType): InternalValueDisplayValue[] {
        const result: InternalValueDisplayValue[] = []
        this.cache.forEach((wrappedValue: WrappedInternalValueDisplayValue, slotId: LoggableActivitySlot) => {
            if (wrappedValue.loggableActivityType === loggableActivityType) {
                result.push(wrappedValue.internalValueDisplayValue)
            }
        })
        return result
    }

    private getDefaultValues(): Map<string, WrappedInternalValueDisplayValue> {
        const LoggableSlotsMap: Map<string, WrappedInternalValueDisplayValue> = new Map<string, WrappedInternalValueDisplayValue>()
        LoggableSlotsMap.set(DEFAULT_MEAL_SLOT_ID, {
            internalValueDisplayValue: {
                displayValue: "Others",
                internalValue: DEFAULT_MEAL_SLOT_ID
            },
            loggableActivityType: "DIET"
        })
        // Workout related slots
        LoggableSlotsMap.set("MORNING", {
            internalValueDisplayValue: {
                displayValue: "Morning",
                internalValue: "MORNING"
            },
            loggableActivityType: "FITNESS"
        })
        LoggableSlotsMap.set("AFTERNOON", {
            internalValueDisplayValue: {
                displayValue: "Afternoon",
                internalValue: "AFTERNOON"
            },
            loggableActivityType: "FITNESS"
        })
        LoggableSlotsMap.set("EVENING", {
            internalValueDisplayValue: {
                displayValue: "Evening",
                internalValue: "EVENING"
            },
            loggableActivityType: "FITNESS"
        })
        // All day slot
        LoggableSlotsMap.set("ALLDAY", {
            internalValueDisplayValue: {
                displayValue: "All Day",
                internalValue: "ALLDAY"
            },
            loggableActivityType: "FITNESS"
        })
        return LoggableSlotsMap
    }
}

export default LoggableSlotsMetaCache
