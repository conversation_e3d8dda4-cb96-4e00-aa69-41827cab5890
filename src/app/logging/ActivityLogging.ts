import { Action } from "../common/views/WidgetView"
import { DietSlot, DietSlots } from "@curefit/gmf-common"

type DEFAULT_MEAL_SLOT_ID = "OTHERS"

export type LoggableActivityType = "FITNESS" | "MEDITATE" | "DIET" | "MEDICINE" | "METRICS" | "SYMPTOMS"
export const LoggableActivityTypeValues = ["FITNESS", "MEDITATE", "DIET", "MEDICINE", "METRICS", "SYMPTOMS"]

export type LoggableActivitySlot = DietSlot | "MORNING" | "AFTERNOON" | "EVENING" | "ALLDAY" | DEFAULT_MEAL_SLOT_ID
export const DEFAULT_MEAL_SLOT_ID: LoggableActivitySlot = "OTHERS"
export const LoggableActivitySlotValues = [...DietSlots, "MORNING", "AFTERNOON", "EVENING", "ALLDAY", DEFAULT_MEAL_SLOT_ID]
export const DietLoggableActivitySlotValues = [...DietSlots, DEFAULT_MEAL_SLOT_ID]

export type LoggableActivityMetricViewType = "INCREMENTOR" | "TEXT" | "SEGMENT"
export type LoggableSlotViewType = "IN_PLACE" | "EXPANDED"

export interface SlotwiseOrganisedLoggableActivityInfo {
    date: string
    slots: {
        id: LoggableActivitySlot,
        name: string
    }[]
    slotViewType: LoggableSlotViewType
    slotDetails: {
        [slotName: string]: LoggableActivityInfo[]
    },
    editable: boolean
}

export interface LoggableActivityInfo {
    id: string
    name: string,
    subTitle?: string,
    activityType: LoggableActivityType
    metrics: LoggableActivityMetricViewModel[],
    recentLogs?: RecentLogItem[],
    action?: Action
}

export interface RecentLogItem {
    name: string,
    time: string,
    subTitle?: string
}

export interface LoggableActivityMetricInfo {
    metricName: string
    metricUnit: string
    multiplier: number
    possibleQuantities?: string[]
    meta?: any
}

export interface LoggableActivityMetricViewModel {
    selectedMetric: LoggableActivityMetricInfo
    viewType: LoggableActivityMetricViewType
    quantity: string
    supportedMetrics?: LoggableActivityMetricInfo[]
}

export interface DietMeasurementUnitDetails {
    unitName: string
    caloriesPerUnit: number
}
