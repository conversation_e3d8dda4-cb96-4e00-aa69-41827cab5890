import { injectable, inject } from "inversify"
import { MysqlReadonlyDao } from "@curefit/mysql-utils"
import CUREFIT_API_TYPES from "../../../../../config/ioc/types"
import { UserDeviceDbModel, UserDeviceModel } from "./UserDeviceModel"
import { IUserDeviceReadonlyDao } from "./IUserDeviceDao"
import { UserDeviceSchema } from "./UserDeviceSchema"
import { UserDeviceAdapter } from "./UserDeviceAdapter"

@injectable()
export class UserDeviceReadonlyDaoMysqlImpl extends MysqlReadonlyDao<UserDeviceModel, UserDeviceDbModel> implements IUserDeviceReadonlyDao {
    constructor(
        @inject(CUREFIT_API_TYPES.UserDeviceSchema) schema: UserDeviceSchema,
        @inject(CUREFIT_API_TYPES.UserDeviceAdapter) adapter: UserDeviceAdapter
    ) {
        super(schema, adapter)
    }
}
