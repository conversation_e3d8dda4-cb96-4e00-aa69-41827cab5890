import { IRead, IWrite } from "@curefit/mysql-utils"
import { ITransaction } from "@curefit/mysql-utils"
import * as Knex from "knex"
import { UserDeviceDbModel } from "./UserDeviceModel"


export interface IUserDeviceReadonlyDao extends IRead<UserDeviceDbModel> {
}

export interface IUserDeviceReadWriteDao extends IRead<UserDeviceDbModel>, IWrite<UserDeviceDbModel>, ITransaction<UserDeviceDbModel, Knex.Transaction> {
}
