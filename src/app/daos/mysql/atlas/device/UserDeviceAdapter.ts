import { MysqlAdapter } from "@curefit/mysql-utils"
import { injectable } from "inversify"
import { UserDeviceDbModel, UserDeviceModel } from "./UserDeviceModel"

@injectable()
export class UserDeviceAdapter implements MysqlAdapter<UserDeviceModel, UserDeviceDbModel> {

    toModel(item: UserDeviceModel): UserDeviceDbModel {
        return item
    }

    toDbModel(item: UserDeviceDbModel): any {
        return item
    }
}
