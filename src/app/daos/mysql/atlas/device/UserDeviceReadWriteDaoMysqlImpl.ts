import { MysqlReadWriteDao } from "@curefit/mysql-utils"
import { injectable, inject } from "inversify"
import CUREFIT_API_TYPES from "../../../../../config/ioc/types"
import { UserDeviceDbModel, UserDeviceModel } from "./UserDeviceModel"
import { IUserDeviceReadWriteDao } from "./IUserDeviceDao"
import { UserDeviceSchema } from "./UserDeviceSchema"
import { UserDeviceReadonlyDaoMysqlImpl } from "./UserDeviceReadonlyDaoMysqlImpl"
import { UserDeviceAdapter } from "./UserDeviceAdapter"

@injectable()
export class UserDeviceReadWriteDaoMysqlImpl extends MysqlReadWriteDao<UserDeviceModel, UserDeviceDbModel>
    implements IUserDeviceReadWriteDao {

    constructor(
        @inject(CUREFIT_API_TYPES.UserDeviceSchema) schema: UserDeviceSchema,
        @inject(CUREFIT_API_TYPES.UserDeviceReadonlyDao) readonlyDao: UserDeviceReadonlyDaoMysqlImpl,
        @inject(CUREFIT_API_TYPES.UserDeviceAdapter) adapter: UserDeviceAdapter
    ) {
        super(schema, readonlyDao, adapter)
    }
}
