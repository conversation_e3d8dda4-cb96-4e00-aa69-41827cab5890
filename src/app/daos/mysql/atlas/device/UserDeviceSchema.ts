import { injectable, inject } from "inversify"
import { MultiMysqlAccess, MYSQL_TYPES, MysqlSchema } from "@curefit/mysql-utils"
import { UserDeviceModel } from "./UserDeviceModel"

@injectable()
export class UserDeviceSchema extends MysqlSchema<UserDeviceModel> {
    constructor(
        @inject(MYSQL_TYPES.MultiMysqlAccess) private mysqlAccess: MultiMysqlAccess,
    ) {
        super(mysqlAccess, "user_device")
    }
}
