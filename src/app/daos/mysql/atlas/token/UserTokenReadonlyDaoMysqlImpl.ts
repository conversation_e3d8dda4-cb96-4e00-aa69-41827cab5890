import { injectable, inject } from "inversify"
import { MysqlReadonlyDao } from "@curefit/mysql-utils"
import CUREFIT_API_TYPES from "../../../../../config/ioc/types"
import { UserTokenDbModel, UserTokenModel } from "./UserTokenModel"
import { IUserTokenReadonlyDao } from "./IUserTokenDao"
import { UserTokenSchema } from "./UserTokenSchema"
import { UserTokenAdapter } from "./UserTokenAdapter"

@injectable()
export class UserTokenReadonlyDaoMysqlImpl extends MysqlReadonlyDao<UserTokenModel, UserTokenDbModel> implements IUserTokenReadonlyDao {
    constructor(
        @inject(CUREFIT_API_TYPES.UserTokenSchema) schema: UserTokenSchema,
        @inject(CUREFIT_API_TYPES.UserTokenAdapter) adapter: UserTokenAdapter
    ) {
        super(schema, adapter)
    }
}
