import { IRead, IWrite } from "@curefit/mysql-utils"
import { ITransaction } from "@curefit/mysql-utils"
import * as K<PERSON> from "knex"
import { UserTokenDbModel } from "./UserTokenModel"


export interface IUserTokenReadonlyDao extends IRead<UserTokenDbModel> {
}

export interface IUserTokenReadWriteDao extends IRead<UserTokenDbModel>, IWrite<UserTokenDbModel>, ITransaction<UserTokenDbModel, Knex.Transaction> {
}
