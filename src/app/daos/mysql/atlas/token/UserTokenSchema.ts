import { injectable, inject } from "inversify"
import { MultiMysqlAccess, MYSQL_TYPES, MysqlSchema } from "@curefit/mysql-utils"
import { UserTokenModel } from "./UserTokenModel"

@injectable()
export class UserTokenSchema extends MysqlSchema<UserTokenModel> {
    constructor(
        @inject(MYSQL_TYPES.MultiMysqlAccess) private mysqlAccess: MultiMysqlAccess,
    ) {
        super(mysqlAccess, "user_token")
    }
}
