import { MysqlAdapter } from "@curefit/mysql-utils"
import { injectable } from "inversify"
import { UserTokenDbModel, UserTokenModel } from "./UserTokenModel"

@injectable()
export class UserTokenAdapter implements MysqlAdapter<UserTokenModel, UserTokenDbModel> {

    toModel(item: UserTokenModel): UserTokenDbModel {
        return item
    }

    toDbModel(item: UserTokenDbModel): any {
        return item
    }
}
