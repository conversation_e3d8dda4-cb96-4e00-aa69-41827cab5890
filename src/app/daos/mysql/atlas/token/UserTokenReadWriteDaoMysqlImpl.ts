import { MysqlReadWriteDao } from "@curefit/mysql-utils"
import { injectable, inject } from "inversify"
import CUREFIT_API_TYPES from "../../../../../config/ioc/types"
import { UserTokenDbModel, UserTokenModel } from "./UserTokenModel"
import { IUserTokenReadWriteDao } from "./IUserTokenDao"
import { UserTokenSchema } from "./UserTokenSchema"
import { UserTokenReadonlyDaoMysqlImpl } from "./UserTokenReadonlyDaoMysqlImpl"
import { UserTokenAdapter } from "./UserTokenAdapter"

@injectable()
export class UserTokenReadWriteDaoMysqlImpl extends MysqlReadWriteDao<UserTokenModel, UserTokenDbModel>
    implements IUserTokenReadWriteDao {

    constructor(
        @inject(CUREFIT_API_TYPES.UserTokenSchema) schema: UserTokenSchema,
        @inject(CUREFIT_API_TYPES.UserTokenReadonlyDao) readonlyDao: UserTokenReadonlyDaoMysqlImpl,
        @inject(CUREFIT_API_TYPES.UserTokenAdapter) adapter: UserTokenAdapter
    ) {
        super(schema, readonlyDao, adapter)
    }
}
