import { MysqlAdapter } from "@curefit/mysql-utils"
import { injectable } from "inversify"
import { UserSleepDbModel, UserSleepModel } from "./UserSleepModel"

@injectable()
export class UserSleepAdapter implements MysqlAdapter<UserSleepModel, UserSleepDbModel> {

    toModel(item: UserSleepModel): UserSleepDbModel {
        return item
    }

    toDbModel(item: UserSleepDbModel): any {
        return item
    }
}
