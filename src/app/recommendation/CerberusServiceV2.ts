import { inject, injectable } from "inversify"
import * as _ from "lodash"

import { MultiMysqlAccess, MYSQL_TYPES } from "@curefit/mysql-utils"
import { Logger, BASE_TYPES } from "@curefit/base"
import { MenuType } from "@curefit/eat-common"
import { RecommendedMeals } from "@curefit/vm-models"
import {
    ICerberusServiceV2, PaymentPreferenceList, PreferenceListObject,
    recommendedPaymentChannel
} from "@curefit/vm-models"

@injectable()
class CerberusServiceV2 implements ICerberusServiceV2 {
    constructor(
        @inject(MYSQL_TYPES.MultiMysqlAccess) private mysqlAccess: MultiMysqlAccess,
        @inject(BASE_TYPES.ILogger) private logger: Logger
    ) { }

    private async getRecommendedMealsData(userId: string, date: string, menuType?: MenuType): Promise<any[]> {
        // this.logger.info(`Get recommended meals for ${userId} ${date} ${menuType}`)
        const access = this.mysqlAccess.getMysqlConnection("USER_PROFILE")
        let query = access.table("eatfit_meal_preference").where({
            user_id: userId,
            meal_date: date
        })
        if (!_.isNil(menuType)) {
            query = query.andWhere({
                meal_slot: menuType
            })
        }
        return query
    }

    private async getRecommendedMealsResult(queryResult: any): Promise<RecommendedMeals[]> {
        return _.map(queryResult, (result: any) => {
            return {
                productId: result.product_id,
                mealSlot: result.meal_slot,
                preference: result.preference
            }
        })
    }

    async getRecommendedMeals(userId: string, date: string, menuType?: MenuType): Promise<RecommendedMeals[]> {
        const specificRecommendedMealsPromise = this.getRecommendedMealsData(userId, date, menuType)
        const genericRecommendedMealsPromise = this.getRecommendedMealsData("0", date, menuType)
        const [specificResult, genericResult] = await Promise.all([specificRecommendedMealsPromise, genericRecommendedMealsPromise])
        if (_.isEmpty(specificResult)) {
            // this.logger.info(`No results found for ${userId} ${date} ${menuType}, defaulting back to 0`)
            return this.getRecommendedMealsResult(genericResult)
        } else {
            // this.logger.info(`Fetched results: ${specificResult.length}`)
            return this.getRecommendedMealsResult(specificResult)
        }
    }

    private async getPaymentPreferenceData(userId: string): Promise<PaymentPreferenceList[]> {
        this.logger.info(`Getting payment preferences for ${userId}`)
        const access = this.mysqlAccess.getMysqlConnection("USER_PROFILE")
        return access.column("preferencelist").table("payment_preference_new").where({
            userid: userId
        })
    }

    private processRecommendedPaymentChannel(preferenceListObject: PreferenceListObject, vertical: string): recommendedPaymentChannel {
        if (!_.isEmpty(preferenceListObject[vertical])) {
            return preferenceListObject[vertical]
        }
        return preferenceListObject["generic"]
    }


    async getRecommendedPaymentChannel(userId: string, vertical: string, isPack?: boolean): Promise<recommendedPaymentChannel> {
        this.logger.info(`Getting payment preferences for ${userId} and ${vertical} and ${isPack}`)
        if (vertical === "EAT_FIT") {
            if (isPack) {
                vertical += "_PACK"
            } else {
                vertical += "_SINGLES"
            }
        }
        this.logger.info(`Getting recommended payment channel for ${userId} and ${vertical}`)
        const specificResultPromise: Promise<PaymentPreferenceList[]> = this.getPaymentPreferenceData(userId)
        const [specificResult] = await Promise.all([specificResultPromise])
        if (_.isEmpty(specificResult)) {
            this.logger.info(`No payment preferences exist for ${userId}`)
            return this.processRecommendedPaymentChannel(JSON.parse("{}"), vertical)
        }
        return this.processRecommendedPaymentChannel(JSON.parse(specificResult[0].preferencelist), vertical)
    }
}

export default CerberusServiceV2
