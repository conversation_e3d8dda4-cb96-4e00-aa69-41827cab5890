import { ProductPrice, ProductType } from "@curefit/product-common"
import { CultFormElement, FitnessEvent } from "@curefit/cult-common"
import {
    ActionType,
    CenterAddressWidget,
    DescriptionWidget,
    InfoCard,
    ProductDetailPage,
    WidgetView
} from "../common/views/WidgetView"
import { injectable } from "inversify"
import * as _ from "lodash"
import { UserContext } from "@curefit/userinfo-common"
import { TimeUtil } from "@curefit/util-common"

@injectable()
class EventDetailViewBuilder {

    constructor() {
    }

    async getView(userContext: UserContext,
        fitnessEvent: FitnessEvent, productType: ProductType, isLoggedOut: boolean): Promise<ProductDetailPage> {
        const productDetailPage: ProductDetailPage = new ProductDetailPage()

        let ctaText: string = undefined
        if (fitnessEvent.metaDataObject && fitnessEvent.metaDataObject.ctaText) {
            ctaText = fitnessEvent.metaDataObject.ctaText
        }
        let ctaActionType: ActionType = undefined
        let ctaActionURL: string = undefined
        if (fitnessEvent.metaDataObject && !_.isNil(fitnessEvent.metaDataObject.ctaActionURL)) {
            ctaActionType = "NAVIGATION_NEWTAB"
            ctaActionURL = fitnessEvent.metaDataObject.ctaActionURL
        }
        if (fitnessEvent.cultAppAvailableSeats > 0) {
            if (isLoggedOut) {
                productDetailPage.actions.push({
                    title: ctaText ? ctaText : "Login",
                    actionType: ctaActionType ? ctaActionType : "SHOW_LOGIN_MODAL",
                    url: ctaActionURL ? ctaActionURL : null

                })
            }
            else if (!_.isNil(fitnessEvent.metaDataObject.formData))
                productDetailPage.actions.push({
                    title: ctaText ? ctaText : "Register Now",
                    actionType: ctaActionType ? ctaActionType : "REGISTER_EVENT",
                    url: ctaActionURL ? ctaActionURL : null
                })
            else {
                productDetailPage.actions.push({
                    title: ctaText ? ctaText : "Join Now",
                    actionType: ctaActionType ? ctaActionType : "JOIN_EVENT",
                    url: ctaActionURL ? ctaActionURL : null
                })
            }
        } else {
            productDetailPage.actions.push({
                title: "Event Closed",
                actionType: "CLOSED_EVENT"
            })
        }
        productDetailPage.widgets.push(this.getSummaryWidget(fitnessEvent, productType, userContext))
        productDetailPage.widgets.push(this.getDescriptionWidget(fitnessEvent))
        if (!_.isNil(fitnessEvent.Address))
            productDetailPage.widgets.push(this.getCenterAddressWidget(fitnessEvent))
        return productDetailPage

    }

    private getCenterAddressWidget(fitnessEvent: FitnessEvent): CenterAddressWidget {
        let addressString = fitnessEvent.Address.addressLine1 + ", "
        if (_.isNil(fitnessEvent.Address.displayCityName)) {
            addressString += fitnessEvent.Address.City.name
        } else {
            addressString += fitnessEvent.Address.displayCityName
        }
        const centerAddressWidget: CenterAddressWidget = {
            widgetType: "CENTER_ADDRESS_WIDGET",
            header: {
                title: "Address",
            },
            mapUrl: fitnessEvent.Address.placeURL,
            addressText: addressString,
            latLong: {
                lat: fitnessEvent.Address.latitude,
                long: fitnessEvent.Address.longitude
            }
        }
        return centerAddressWidget
    }

    private getSummaryWidget(fitnessEvent: FitnessEvent, productType: ProductType, userContext: UserContext): WidgetView {
        const imageDoc = fitnessEvent.documents.filter((x) => { if (x.tagName === "PRODUCT_BNR") return true; return false })
        const tz = userContext.userProfile.timezone
        const summaryWidget: WidgetView & {
            title: string
            eventDate: string
            eventTime: string
            price: ProductPrice
            image: string
            productType: ProductType
            productId: string
            eventId: string
            form?: {
                header: { title: string, eventLocation: string, eventDate: string },
                elements: CultFormElement[]
            }
        } = {
            widgetType: "EVENT_DETAIL_SUMMARY",
            title: fitnessEvent.name,
            eventDate: _.isNil(fitnessEvent.launchDate) ? null : TimeUtil.formatDateStringInTimeZone(fitnessEvent.launchDate, tz, "Do MMM, YYYY"),
            eventTime: _.isNil(fitnessEvent.startTime) ? null : TimeUtil.formatDateStringInTimeZone(fitnessEvent.startTime, tz, "hh:mm A"),
            price: { mrp: fitnessEvent.price, listingPrice: fitnessEvent.price, currency: "INR" },
            image: _.isEmpty(imageDoc) ? null : imageDoc[0].URL,
            productId: fitnessEvent.productId,
            eventId: fitnessEvent.id.toString(),
            productType: productType,
            form: !_.isNil(fitnessEvent.metaDataObject.formData) ? {
                header: {
                    title: fitnessEvent.name,
                    eventLocation: _.isNil(fitnessEvent.Address) ? null :
                        fitnessEvent.Address.addressLine1 + " , " + fitnessEvent.Address.state,
                            eventDate: TimeUtil.formatDateStringInTimeZone(fitnessEvent.launchDate, tz, "Do MMM, YYYY") },
                elements: fitnessEvent.metaDataObject.formData
            } : null
        }
        return summaryWidget
    }
    private getDescriptionWidget(fitnessEvent: FitnessEvent): WidgetView {
        const infoCards: InfoCard[] = []
        const infoCard: InfoCard = {
            title: "About",
            subTitle: fitnessEvent.info
        }

        infoCards.push(infoCard)
        const descriptionWidget = new DescriptionWidget(infoCards)
        return descriptionWidget
    }
}



export default EventDetailViewBuilder
