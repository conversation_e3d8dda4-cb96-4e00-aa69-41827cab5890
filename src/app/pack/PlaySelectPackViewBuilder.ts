import { BASE_TYPES, Logger } from "@curefit/base"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { CENTER_SERVICE_CLIENT_TYPES, ICenterService } from "@curefit/center-service-client"
import { CenterResponse } from "@curefit/center-service-common"
import { CultWorkout } from "@curefit/cult-common"
import { ErrorFactory } from "@curefit/error-client"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import { Benefit, BenefitType } from "@curefit/membership-commons"
import { OfferV2 } from "@curefit/offer-common"
import { OFFER_SERVICE_CLIENT_TYPES, OfferServiceV3 } from "@curefit/offer-service-client"
import { OfferUtil as OffersUtil } from "@curefit/offer-service-client/dist/src/client/OfferUtil"
import { Order } from "@curefit/order-common"
import { ProductType } from "@curefit/product-common"
import { Tenant } from "@curefit/user-common"
import { UserContext } from "@curefit/userinfo-common"
import { TimeUtil } from "@curefit/util-common"
import { inject, injectable } from "inversify"
import * as _ from "lodash"
import * as moment from "moment"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { IOfferAddonWidgetParams, OfferAddonWidget } from "../common/views/OfferAddonWidget"
import {
    Action,
    CheckoutStepItem,
    PlaySelectCheckoutActionsWidget,
    WidgetView
} from "../common/views/WidgetView"
import { CultPackStartDateOptions } from "../cult/cultpackpage/CultPackCommonViewBuilder"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import WidgetBuilder from "../page/vm/WidgetBuilder"
import { CacheHelper } from "../util/CacheHelper"
import CultUtil from "../util/CultUtil"
import PlayUtil from "../util/PlayUtil"
import { BenefitEntry, OfflineFitnessPack } from "@curefit/pack-management-service-common"

@injectable()
class PlaySelectPackViewBuilder {
    constructor(
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) protected catalogueService: ICatalogueService,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) protected offerServiceV3: OfferServiceV3,
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) public membershipService: IMembershipService,
        @inject(CENTER_SERVICE_CLIENT_TYPES.CenterService) private centerService: ICenterService,
        @inject(LOCATION_TYPES.CityService) protected cityService: ICityService,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferUtil) protected offersUtil: OffersUtil,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) protected serviceInterfaces: CFServiceInterfaces,
        @inject(CUREFIT_API_TYPES.WidgetBuilder) public widgetBuilder: WidgetBuilder,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
    ) {
    }

    getWidgetPromises(
        userContext: UserContext,
        productType: ProductType,
        order: Order,
        playPack: OfflineFitnessPack,
        centerResponsePromise: Promise<CenterResponse>,
        workoutResponsePromise: Promise<CultWorkout>,
    ): Promise<WidgetView>[] {
        const playWidgetPromises: Promise<WidgetView>[] = []
        playWidgetPromises.push(this.addFitnessEMIWidget(order, playPack))
        playWidgetPromises.push(this.addOfferAddonWidget(order, true))
        if (productType === "PLAY_SPORT") {
            playWidgetPromises.push(this.addSportsPackHighlightWidget(centerResponsePromise, workoutResponsePromise, playPack))
            playWidgetPromises.push(this.addHowItWorksWidgetForSportsPack(centerResponsePromise, workoutResponsePromise))
        } else if (productType.toString() === "PLAY_CITY_SPORT") {
            playWidgetPromises.push(this.addSportsCityPackHighlightWidget(workoutResponsePromise, playPack))
            playWidgetPromises.push(this.addHowItWorksWidgetForSportsCityPack())
        } else {
            playWidgetPromises.push(this.addHighlightWidget(centerResponsePromise, playPack))
            playWidgetPromises.push(this.selectHowItWorksWidget())
        }

        return playWidgetPromises
    }

    private async addOfferAddonWidget(order: Order, enableOfferAction?: boolean): Promise<WidgetView> {
        if (_.isEmpty(order.offersApplied)) return
        const offerV2MapPromise: Promise<{
            [key: string]: OfferV2;
        }> = this.offerServiceV3.getOffersByIds(order.offersApplied).then(o => o.data)
        const offerAddonWidgetParams: IOfferAddonWidgetParams = {
            logger: this.logger,
            offerV2MapPromise: offerV2MapPromise,
            useLabel: "CART_LABEL"
        }
        if (enableOfferAction) {
            offerAddonWidgetParams.title = "Offers Applied"
        }
        let offerAddonWidget: OfferAddonWidget = new OfferAddonWidget()
        offerAddonWidget = await offerAddonWidget.buildView(offerAddonWidgetParams, true)
        if (_.isEmpty(offerAddonWidget)) {
            return undefined
        }
        if (enableOfferAction) {
            offerAddonWidget.actionSupported = true
            offerAddonWidget.data.offers.forEach(offer => {
                offer.action = {
                    actionType: "SHOW_OFFERS_TNC_MODAL",
                    meta: {
                        title: "Offer Details",
                        dataItems: offer.tnc,
                        url: offer.tncURL
                    }
                }
            })
        }


        return offerAddonWidget
    }

    private async addHighlightWidget(
        centerResponsePromise: Promise<CenterResponse>,
        playPack: OfflineFitnessPack
    ): Promise<WidgetView> {
        const centerInfo = await centerResponsePromise
        const offerAddonWidget: OfferAddonWidget = new OfferAddonWidget()
        offerAddonWidget.data.title = "Hightlights"
        offerAddonWidget.data.offers = [
            {
                description: `Unlimited access: Play unlimited sports at ${centerInfo.name}`,
                prefixImageurl: "image/icons/fitsoImages/schedule_confirm_1.png",
                maxLines: 4,
            },
            {
                description: `100% safe center: We follow strict rules to avoid contact and maintain social distancing at all our centers`,
                prefixImageurl: "image/icons/fitsoImages/security_shield_1.png",
                maxLines: 4
            }
        ]

        if (playPack.product.pauseDays > 0) {
            offerAddonWidget.data.offers.push({
                description: `Pause your pack any time for a maximum of ${playPack.product.pauseDays} days`,
                prefixImageurl: "image/icons/fitsoImages/pause_enclosed_1.png",
                maxLines: 4
            })
        }

        offerAddonWidget.actionSupported = true

        return offerAddonWidget
    }

    private async addSportsPackHighlightWidget(
        centerResponsePromise: Promise<CenterResponse>,
        workoutResponsePromise: Promise<CultWorkout>,
        playPack: OfflineFitnessPack
    ): Promise<WidgetView> {
        const centerInfo = await centerResponsePromise
        const workoutInfo = await workoutResponsePromise
        const offerAddonWidget: OfferAddonWidget = new OfferAddonWidget()
        offerAddonWidget.data.title = "What you’ll get"
        offerAddonWidget.data.offers = [
            {
                description: `Unlimited access of ${workoutInfo.name} at ${centerInfo.name}`,
                prefixImageurl: "image/icons/fitsoImages/schedule_confirm_1.png",
                maxLines: 4,
            },
            {
                description: `100% safe center: We follow strict rules to avoid contact and maintain social distancing at all our centers`,
                prefixImageurl: "image/icons/fitsoImages/security_shield_1.png",
                maxLines: 4
            }
        ]

        if (playPack.product.pauseDays > 0) {
            offerAddonWidget.data.offers.push({
                description: `Pause your pack any time for a maximum of ${playPack.product.pauseDays} days`,
                prefixImageurl: "image/icons/fitsoImages/pause_enclosed_1.png",
                maxLines: 4
            })
        }

        offerAddonWidget.actionSupported = true

        return offerAddonWidget
    }

    private async addSportsCityPackHighlightWidget(
        workoutResponsePromise: Promise<CultWorkout>,
        playPack: OfflineFitnessPack
    ): Promise<WidgetView> {
        const workoutInfo = await workoutResponsePromise
        const offerAddonWidget: OfferAddonWidget = new OfferAddonWidget()
        const liveBenefit: BenefitEntry = playPack.product.benefits.find(packBenefit => packBenefit.name === "CF_LIVE")
        const otherSportBenefit: BenefitEntry = playPack.product.benefits.find(packBenefit => packBenefit.name === "PLAY_OTHER_SPORTS")
        offerAddonWidget.data.title = `Benefit of cultpass ${workoutInfo.name.toUpperCase()}`
        offerAddonWidget.data.offers = [
            {
                description: `Unlimited ${workoutInfo.name} sessions at all centres in ${this.cityService.getCityById(playPack.clientMetadata.cityId).name ?? "your city"}`,
                prefixImageurl: "/image/icons/cult/tick_icon.svg",
                maxLines: 4,
            },
            {
                description: `Access ${otherSportBenefit.tickets} other sports sessions at any center in ${this.cityService.getCityById(playPack.clientMetadata.cityId).name ?? "your city"}`,
                prefixImageurl: "/image/icons/cult/tick_icon.svg",
                maxLines: 4
            },
        ]
        if (playPack.product.pauseDays > 0) {
            offerAddonWidget.data.offers.push({
                description: `Pause your pack any time for a maximum of ${playPack.product.pauseDays} days`,
                prefixImageurl: "/image/icons/cult/tick_icon.svg",
                maxLines: 4
            })
        }
        if (liveBenefit) {
            offerAddonWidget.data.offers.push({
                description: "1000+ online workout hours",
                prefixImageurl: "/image/icons/cult/tick_icon.svg",
                maxLines: 4
            })
        }

        offerAddonWidget.actionSupported = true

        return offerAddonWidget
    }

    public async addHowItWorksWidgetForSportsCityPack(): Promise<WidgetView> {
        return {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "SMALL",
            header: {
                title: "How It Works",
                style: {
                    fontSize: 18
                },
                color: "#000000"
            },
            items: [
                {
                    subTitle: `Book a session of the sport that you like. Reach the center on time & enjoy the session.`,
                    icon: "image/icons/fitsoImages/schedule_confirm_1.png",
                    padding: 5
                },
                {
                    subTitle: "Choose from the wide variety of online workouts and join in from anywhere.",
                    icon: "/image/icons/cult/live_icon.svg",
                    padding: 5
                }
            ],
            hideSepratorLines: true,
            hasDividerBelow: false,
            showSquareIcons: true,
            layoutProps: {
                spacing: {
                    bottom: 80,
                }
            }
        }
    }

    private async selectHowItWorksWidget(): Promise<WidgetView> {
        const offerAddonWidget: OfferAddonWidget = new OfferAddonWidget()
        offerAddonWidget.data.title = "How It Works"
        offerAddonWidget.data.offers = [
            {
                description: `Play sports: Book a sport at this center. Reach the center on time and enjoy your sport`,
                prefixImageurl: "image/icons/fitsoImages/schedule_confirm_1.png",
                maxLines: 4
            },
        ]

        offerAddonWidget.actionSupported = true

        return offerAddonWidget
    }

    private async addHowItWorksWidgetForSportsPack(
        centerResponsePromise: Promise<CenterResponse>,
        workoutResponsePromise: Promise<CultWorkout>,
    ): Promise<WidgetView> {
        const centerInfo = await centerResponsePromise
        const workoutInfo = await workoutResponsePromise
        const offerAddonWidget: OfferAddonWidget = new OfferAddonWidget()
        offerAddonWidget.data.title = "How It Works"
        offerAddonWidget.data.offers = [
            {
                description: `Book a session at ${centerInfo.name} any time via the cult app. `,
                prefixImageurl: "image/icons/fitsoImages/schedule_confirm_1.png",
                maxLines: 4
            },
            {
                description: `Visit ${centerInfo.name} at the selected time & get started!`,
                prefixImageurl: "image/icons/fitsoImages/schedule_confirm_1.png",
                maxLines: 4
            },
        ]

        offerAddonWidget.actionSupported = true

        return offerAddonWidget
    }

    private async addFitnessEMIWidget(order: Order, product: OfflineFitnessPack): Promise<WidgetView> {
        if (_.isEmpty(order.offersApplied)) return
        const offerV2Map: { [key: string]: OfferV2 } = await this.offerServiceV3.getOffersByIds(order.offersApplied).then(o => o.data)
        let emiOffer: OfferV2
        _.each(offerV2Map, (offer: OfferV2) => {
            if (offer.constraints.paymentChannel === "EMI") {
                emiOffer = offer
            }
        })
        if (emiOffer) {
            return this.getNoCostEMIWidgetView(product, emiOffer, order.totalAmountPayable)
        } else {
            return null
        }
    }

    public getNoCostEMIWidgetView(product: OfflineFitnessPack, emiOffer: OfferV2, totalPayable?: number): WidgetView {

        let maxEmiTenure = 12
        emiOffer.addons.forEach((addOn) => {
            if (addOn.config && addOn.config.maxEmiTenure) {
                maxEmiTenure = addOn.config.maxEmiTenure
            }
        })

        const numberOfMonths = CultUtil.getEmiTenureForPack(product.product.durationInDays, maxEmiTenure)
        const item = {
            imageUrl: "image/transform/EMI.png",
            title: "No Cost EMI starts at just",
            symbol: "₹",
            price: `${Math.round((totalPayable ?? product.price.listingPrice) / numberOfMonths)}`,
            suffix: "month*",
            action: {
                actionType: "NAVIGATION",
                url: `curefit://nocostemipage?packId=${product.productId}&productType=${product.productType}`,
                title: "DETAILS"
            }
        }

        const title = "No Cost EMI"
        return {
            widgetType: "NO_COST_EMI_WIDGET",
            title: title,
            hasDividerBelow: true,
            items: [item]
        }
    }

    getFooterWidgetPromises(
        playPack: OfflineFitnessPack,
        order: Order,
        userContext: UserContext,
        centerResponsePromise: Promise<CenterResponse>,
        workoutResponsePromise: Promise<CultWorkout>,
    ): Promise<WidgetView>[] {
        const playWidgetPromises: Promise<WidgetView>[] = []
        playWidgetPromises.push(this.getCheckoutActionsForPlaySelect(
            playPack.productId,
            centerResponsePromise,
            workoutResponsePromise,
            order,
            userContext,
            playPack
        ))

        return playWidgetPromises
    }

    getFooterWidgetPromisesForSLPCity(
        playPack: OfflineFitnessPack,
        order: Order,
        userContext: UserContext,
        centerResponsePromise: Promise<CenterResponse>,
        workoutResponsePromise: Promise<CultWorkout>,
    ): Promise<WidgetView>[] {
        const playWidgetPromises: Promise<WidgetView>[] = []
        playWidgetPromises.push(this.getFooterWidgetPromisesForCitySLP(
            playPack,
            order,
            userContext,
            centerResponsePromise,
            workoutResponsePromise,
        ))

        return playWidgetPromises
    }

    public async getFooterWidgetPromisesForCitySLP (
        playPack: OfflineFitnessPack,
        order: Order,
        userContext: UserContext,
        centerResponsePromise: Promise<CenterResponse>,
        workoutResponsePromise: Promise<CultWorkout>,
    ): Promise<WidgetView> {
        let center: CenterResponse
        if (centerResponsePromise) {
            center = await centerResponsePromise
        }
        let workout: CultWorkout
        if (workoutResponsePromise) {
            workout = await workoutResponsePromise
        }
        const stepItems: CheckoutStepItem[] = []
        const subCategoryCode = ""
        const selectedStartDate = order.products[0].option.startDate

        await this.buildCenterSelectionWidgetForCitySLP(
            stepItems,
            center?.id ? String(center?.id) : null,
            center,
            workout?.name,
            workout.id.toString(),
            playPack.productId,
            selectedStartDate
        )

        const startDateOptions = await this.getDateAction(
            stepItems,
            userContext,
            selectedStartDate,
            playPack,
            playPack.productId,
            centerResponsePromise
        )

        const currentAction = await this.getPrePurchasePageActionsForCitySLP(
            userContext,
            playPack,
            startDateOptions,
            order,
            center,
            workout
        )
        return new PlaySelectCheckoutActionsWidget(
            stepItems,
            currentAction,
            subCategoryCode
        )
    }

    private async buildCenterSelectionWidgetForCitySLP(
        stepItems: any[],
        preferredCenterId: string,
        centerResponse: CenterResponse,
        workoutName: string,
        workoutId: string,
        productId?: string,
        selectedStartDate?: string
    ) {
        const centerName = preferredCenterId && centerResponse ? centerResponse.name : undefined
        const preferredCenterName = workoutName && centerName ? workoutName + ": " + centerName : undefined

        const dateIcon = preferredCenterName ? "image/icons/fitsoImages/bounded_tick.png" : null
        const iconUrl = preferredCenterName ? undefined : "location"
        const hintText = preferredCenterName ? preferredCenterName : "Select preferred center"

        stepItems.push({
            id: "SPORT_CENTER_SELECTION",
            imageUrl: dateIcon,
            iconName: iconUrl,
            hintText: hintText,
            visible: true,
            showDivider: false,
            action: await this.getCenterAction(
                hintText,
                productId,
                workoutId
            )
        })
    }

    private async getCenterAction(
        title: string,
        productId: string,
        workoutId: string
    ): Promise<any> {
        const url = `curefit://fl_selectpackpreferredcenters?centerType=PLAY&productId=${productId}&workoutId=${workoutId}`
        return {
            title: title,
            url: url,
            actionType: "NAVIGATION",
            meta: {
                productId: productId,
            }
        }
    }

    public async getDateAction(
        stepItems: CheckoutStepItem[],
        userContext: UserContext,
        selectedStartDate: string,
        playPack: OfflineFitnessPack,
        productId: string,
        centerResponsePromise: Promise<CenterResponse>,
    ) {
        const playBenefits: Benefit[] = [{
            name: "PLAY",
            allowOverlap: false,
            type: BenefitType.STATIC,
        }]

        const packDurationInMs = playPack.product.durationInDays * 86400
        const earliestStartDatePlay = await this.membershipService.getEarliestStartDate(
            userContext?.userProfile?.userId,
            Tenant.CUREFIT_APP,
            playBenefits,
            packDurationInMs
        )

        const { minStartDate, maxStartDate } = await PlayUtil.getMinimumAndMaximumStartDate(
            centerResponsePromise,
            earliestStartDatePlay,
            packDurationInMs
        )

        const startDateOptions = this.getStartDateOptions(
            userContext,
            minStartDate,
            selectedStartDate,
            maxStartDate
        )

        const dateString = selectedStartDate
            ? ("Starts On: " + TimeUtil.formatDateStringInTimeZoneDateFns(selectedStartDate, userContext.userProfile.timezone, "dd MMM"))
            : "Pick a start date"

        const dateIcon = selectedStartDate ? "image/icons/fitsoImages/bounded_tick.png" : "image/icons/fitsoImages/time.png"

        // if (selectedStartDate) {
        stepItems.push({
            id: "DATE_SELECTION",
            imageUrl: dateIcon,
            hintText: dateString,
            visible: true,
            showDivider: false,
            action: await PlayUtil.getDatePickerAction("Pick a start date", startDateOptions, productId)
        })
        // }

        return startDateOptions
    }

    private async getPrePurchasePageActionsForCitySLP(
        userContext: UserContext,
        selectedPack: OfflineFitnessPack,
        startDateOptions: CultPackStartDateOptions,
        order: Order,
        center?: CenterResponse,
        workout?: CultWorkout
    ): Promise<Action> {

        if (!center) {
            return await this.getCenterAction(
                "Select preferred center",
                selectedPack.productId,
                workout?.id.toString()
            )
        } else if (!startDateOptions.selectedDate) {
            return await PlayUtil.getDatePickerAction("Select plan start date", startDateOptions, selectedPack.productId)
        }

        const isAvailable = true // CultUtil.isPackAvailableForBuy(builderParams.cultPack, builderParams.preferredCenter.id)
        if (isAvailable) {
            return {
                title: "BUY NOW",
                actionType: "GET_NEW_PLAY_SELECT_PACK",
                meta: {
                    orderProduct: order.products[0],
                    clientMetadata: order.clientMetadata,
                    payFree: (order.totalAmountPayable === 0)
                },
                url: "curefit://payment"
            }
        } else {
            return {
                title: "Sold out",
                actionType: "POP_ACTION"
            }
        }
    }

    public async getCheckoutActionsForPlaySelect(
        productId: string,
        centerResponsePromise: Promise<CenterResponse>,
        workoutResponsePromise: Promise<CultWorkout>,
        order: Order,
        userContext: UserContext,
        playPack: OfflineFitnessPack,
    ): Promise<WidgetView> {

        const playBenefits: Benefit[] = [{
            name: "PLAY",
            allowOverlap: false,
            type: BenefitType.STATIC,
        }]
        const earliestStartDatePlay = await this.membershipService.getEarliestStartDate(userContext?.userProfile?.userId, Tenant.CUREFIT_APP, playBenefits, playPack.product.durationInDays * 86400)
        let centerInfo: CenterResponse
        let centerLaunchDate = null
        centerInfo = await centerResponsePromise
        if (centerInfo.launchDate) {
            const centerLaunchDateMs = centerInfo.launchDate
            centerLaunchDate = centerLaunchDateMs ? moment(centerLaunchDateMs).startOf("day").valueOf() : null
        }

        let earliestStartDate = earliestStartDatePlay.start // moment(earliestStartDatePlay.start).add(1, "days").startOf("day").valueOf()
        if (centerLaunchDate) {
            earliestStartDate = Math.max(earliestStartDate, centerLaunchDate)
        }
        const minStartDate = moment(earliestStartDate).format("YYYY-MM-DD")
        const selectedStartDate = order.products[0].option.startDate
        const startDateOptions = this.getStartDateOptions(userContext, minStartDate, selectedStartDate, null)

        const dateString = selectedStartDate
            ? ("Starts On: " + TimeUtil.formatDateStringInTimeZoneDateFns(selectedStartDate, userContext.userProfile.timezone, "dd MMM"))
            : "Pick a start date"

        const dateIcon = selectedStartDate ? "image/icons/fitsoImages/bounded_tick.png" : "image/icons/fitsoImages/time.png"
        const stepItems: CheckoutStepItem[] = []

        if (selectedStartDate) {
            stepItems.push({
                id: "DATE_SELECTION",
                imageUrl: dateIcon,
                hintText: dateString,
                nextId: "SUBMISSION",
                visible: true,
                showDivider: false,
                action: {
                    title: "Pick a start date",
                    actionType: "SHOW_PLAY_SELECT_DATE_PICKER",
                    meta: {
                        firstDate: startDateOptions.minEligibleDate,
                        lastDate: startDateOptions.maxEligibleDate,
                        selectedDate: startDateOptions.selectedDate,
                        action: {
                            actionType: "UPDATE_CHECKOUT_V2_BLOC",
                            meta: {
                                refreshPage: true,
                                productId: productId,
                            }
                        }
                    }
                }
            })
        }

        const subCategoryCode = ""
        const currentAction = await this.getPrePurchasePageActions(userContext, playPack, startDateOptions, order)
        return new PlaySelectCheckoutActionsWidget(stepItems, currentAction, subCategoryCode)
    }

    private getStartDateOptions(userContext: UserContext, previousMembershipEndDate: string, selectedStartDate: string, maxStartDate: string): CultPackStartDateOptions {
        const tz = userContext.userProfile.timezone
        const today = TimeUtil.todaysDateWithTimezone(tz)
        let startDate: string = today
        const canChangeStartDate: boolean = true

        startDate = previousMembershipEndDate

        const { minimumDate, selectedDate, maximumDate } = PlayUtil.getPackStartDateOptions(userContext, null, canChangeStartDate, selectedStartDate, 30 , startDate, maxStartDate)
        const selectedDateFinal = selectedDate
        return {
            minEligibleDate: minimumDate,
            maxEligibleDate: maximumDate,
            selectedDate: selectedDateFinal,
            canChangeStartDate: canChangeStartDate
        }
    }

    private async getPrePurchasePageActions(
        userContext: UserContext,
        selectedPack: OfflineFitnessPack,
        startDateOptions: CultPackStartDateOptions,
        order: Order,
    ): Promise<Action> {
        if (!startDateOptions.selectedDate) {
            return {
                title: "Select plan start date",
                actionType: "SHOW_PLAY_SELECT_DATE_PICKER",
                meta: {
                    firstDate: startDateOptions.minEligibleDate,
                    lastDate: startDateOptions.maxEligibleDate,
                    selectedDate: startDateOptions.selectedDate,
                    action: {
                        actionType: "UPDATE_CHECKOUT_V2_BLOC",
                        meta: {
                            refreshPage: true,
                            productId: selectedPack.productId,
                        }
                    }
                }
            }
        }

        const isAvailable = true // CultUtil.isPackAvailableForBuy(builderParams.cultPack, builderParams.preferredCenter.id)
        if (isAvailable) {
            return {
                title: "BUY NOW",
                actionType: "GET_NEW_PLAY_SELECT_PACK",
                meta: {
                    orderProduct: order.products[0],
                    clientMetadata: order.clientMetadata,
                    payFree: (order.totalAmountPayable === 0)
                },
                url: "curefit://payment"
            }
        } else {
            return {
                title: "Sold out",
                actionType: "POP_ACTION"
            }
        }
    }


}

export default PlaySelectPackViewBuilder
