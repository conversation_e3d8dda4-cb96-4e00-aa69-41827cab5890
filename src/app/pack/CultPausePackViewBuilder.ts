import { injectable, inject } from "inversify"
import { SessionInfo, UserContext } from "@curefit/userinfo-common"
import { PauseInfo, PausePackInfo, ProductListWidget } from "../common/views/WidgetView"
import { eternalPromise, TimeUtil, Timezone } from "@curefit/util-common"
import { ICultServiceOld as ICultService, CULT_CLIENT_TYPES } from "@curefit/cult-client"
import * as momentTz from "moment-timezone"
import { CatalogueServiceUtilities } from "../util/CatalogueServiceUtilities"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CacheHelper } from "../util/CacheHelper"
import CultUtil, { transformCultSummary } from "../util/CultUtil"
import * as _ from  "lodash"
import { CultMembership } from "@curefit/cult-common"
import AppUtil from "../util/AppUtil"
import { CFUserProfile } from "../page/vm/CFUserProfile"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { UserInfo } from "@curefit/user-common"
import { BaseOfferRequestParams } from "@curefit/offer-common"
import { City } from "@curefit/location-common"
import WidgetBuilder from "../page/vm/WidgetBuilder"
import { PromiseCache } from "../util/VMUtil"
import { ErrorCodes } from "../error/ErrorCodes"
import { ErrorFactory } from "@curefit/error-client"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import { Membership } from "@curefit/membership-commons"
import OffersUtil from "../util/OffersUtil"
import { BASE_TYPES, ILogger } from "@curefit/base"
import GymfitUtil from "../util/GymfitUtil"
import * as moment from "moment"

@injectable()
export default class CultPausePackViewBuilder {
    constructor(
        @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
        @inject(CULT_CLIENT_TYPES.MindFitService) private mindFitService: ICultService,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(CUREFIT_API_TYPES.WidgetBuilder) public widgetBuilder: WidgetBuilder,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected cacheHelper: CacheHelper,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) public membershipService: IMembershipService,
        @inject(BASE_TYPES.ILogger) private logger: ILogger
    ) {
    }

    async getPausePackInfo(membershipId: number, userContext: UserContext, pauseStartDate: string, pauseEndDate: string, isEdit: boolean): Promise<PausePackInfo> {
        // TODO - remove this hack once this is fixed from app side
        const baseService: IMembershipService   = this.membershipService
        const membership: Membership = await baseService.getMembershipById(membershipId, null)
        const tz = userContext.userProfile.timezone
        let pauseStartDateFormatted
        let pauseEndDateFormatted = undefined
        if (pauseStartDate) {
            pauseStartDateFormatted = TimeUtil.formatDateStringInTimeZone(pauseStartDate, tz, "YYYY-MM-DD hh:mm A")
        } else if (isEdit && membership.activePause) {
            pauseStartDateFormatted = moment(membership.activePause.start).format("YYYY-MM-DD hh:mm A")
            pauseEndDateFormatted = moment(membership.activePause.end).subtract(1, "minutes").format("YYYY-MM-DD hh:mm A")
        } else {
            pauseStartDateFormatted = TimeUtil.formatDateStringInTimeZone(TimeUtil.addDays(tz, TimeUtil.todaysDate(tz), 1), tz, "YYYY-MM-DD hh:mm A")
        }
        const currentDate = TimeUtil.getMomentForDateString(TimeUtil.addDays(tz, TimeUtil.todaysDate(tz), 1), tz).subtract(1, "minutes").toDate()
        const pauseStartDateMoment = TimeUtil.getMomentForDateString(pauseStartDateFormatted?.toString(), tz).toDate()
        const maxEndDate = TimeUtil.addToDate(pauseStartDateMoment, tz, GymfitUtil.convertMillisToDays(membership.remainingPauseDuration), "days").toDate()
        const pauseEndDateMoment = TimeUtil.getMomentForDateString(pauseEndDate, tz, "YYYY-MM-DD hh:mm A").toDate()
        if (pauseStartDateFormatted && pauseEndDate && pauseStartDateMoment && pauseEndDateMoment &&
            !TimeUtil.isDateBetween(pauseStartDateMoment, maxEndDate, pauseEndDateMoment)) {
            throw this.errorFactory.withCode(ErrorCodes.CULT_PAUSE_END_DATE_EXCEEDING_LIMIT, 400).withDebugMessage("Pack pause end date exceeding the limit").build()
        }
        const allCultBookings = await this.cultFitService.getBookingsForMembership(membershipId, userContext.userProfile.userId)
        const allMindBookings = await this.mindFitService.getBookingsForMembership(membershipId, userContext.userProfile.userId)
        const allBookings = [...allCultBookings, ...allMindBookings]
        const cultBookings = allBookings
            .filter((booking) => {
                const classDate = momentTz.tz(booking.Class.date + " " + booking.Class.startTime, tz).toDate()
                return classDate.getTime() > currentDate.getTime() && pauseEndDateMoment.getTime() > classDate.getTime()
            })
            .map((booking) => {
                const date = booking.Class.date + " " + booking.Class.startTime
                return {workoutName: booking.Class.Workout.name, centerName: booking.Center.name, date, workoutImageUrl: CatalogueServiceUtilities.getWorkoutImage(booking.Class.Workout.id)}
            })
        const cultPausePackInfo = new PausePackInfo()

        const headerWidget = await eternalPromise(this.pausePackHeaderWidget(userContext))
        if (headerWidget && headerWidget.obj) {
            cultPausePackInfo.headerWidgets = headerWidget.obj
        }
        const previousPauseEndDate = membership.activePause
            ? moment(membership.activePause.end).format("YYYY-MM-DD hh:mm A")
            : null

        cultPausePackInfo.membershipInfo = {
            membershipId: membership.id,
            pauseMaxDays: GymfitUtil.convertMillisToDays(membership.maxPauseDuration),
            remainingPauseDays: GymfitUtil.convertMillisToDays(membership.remainingPauseDuration),
            startDateParam: {
                date: pauseStartDateFormatted?.toString(),
                limit: moment(membership.end).format("YYYY-MM-DD"),
                canEdit: false
            },
            endDate: pauseEndDateFormatted?.toString(),
            pauseInfo: this.getPauseInfo(
                tz,
                pauseStartDateFormatted?.toString(),
                (pauseEndDate || pauseStartDateFormatted)?.toString(),
                previousPauseEndDate,
                GymfitUtil.convertMillisToDays(membership.remainingPauseDuration),
                moment(membership.end).format("YYYY-MM-DD hh:mm A")
            )
        }
        this.logger.info("KABOOOOOMM membershipInfo:::: ", JSON.stringify(cultPausePackInfo.membershipInfo))

        const howItWorksWidget = this.getHowItWorksWidget(membership)
        cultPausePackInfo.widgets = [howItWorksWidget]
        const pauseStartDateTimeFormated = TimeUtil.getMomentForDateString(pauseStartDateFormatted?.toString(), tz).format("DD MMM YYYY")
        const pauseEndDateTimeFormated =  TimeUtil.getMomentForDateString(pauseEndDate, tz).format("DD MMM YYYY")
        cultPausePackInfo.existingBookings = {
            action: {
                positiveActionText: "DON'T PAUSE",
                negativeActionText: "PAUSE"
            },
            confirmationTitle: "Are you sure you want to pause this pack?",
            confirmationDescription: `All your classes during pause period (${pauseStartDateTimeFormated} to ${pauseEndDateTimeFormated}) will be auto-cancelled`,
            cultBookings
        }
        cultPausePackInfo.showPauseProgressBar = !isEdit
        return cultPausePackInfo
    }

    private async pausePackHeaderWidget(userContext: UserContext) {
        const userProfile: CFUserProfile = userContext.userProfile
        userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
        const sessionInfo: SessionInfo = userContext.sessionInfo
        const user = await userContext.userPromise
        const city: City = await this.serviceInterfaces.cityService.getCityById(userProfile.cityId)
        const userSegmentIds: string[] = await userProfile.promiseMapCache.getPromise("user-platform-segments", { userId: userProfile.userId })

        const userInfo: UserInfo = {
            userId: userProfile.userId,
            deviceId: userContext.sessionInfo.deviceId,
            email: user.email,
            phone: user.phone,
            workEmail: user.workEmail
        }
        const baseOfferRequestParam: BaseOfferRequestParams = {
            source: AppUtil.callSourceFromContext(userContext),
            userId: userProfile.userId,
            deviceId: sessionInfo.deviceId,
            cityId: userProfile.cityId
        }
        userProfile.getCultProductPrices = OffersUtil.getCultPackPricesWithProductIds(this.serviceInterfaces.offerServiceV3, {
            userInfo,
            source: baseOfferRequestParam.source,
            cityId: baseOfferRequestParam.cityId,
            cultCityId: city.cultCityId,
            userSegmentIds
        })
        // userProfile.mindProductPricesPromise = this.serviceInterfaces.offerServiceV3.getMindPackPrices({ // Mind packs are deprecated
        //     userInfo,
        //     source: baseOfferRequestParam.source,
        //     cityId: baseOfferRequestParam.cityId,
        //     cultCityId: city.cultCityId,
        //     userSegmentIds
        // })
        let bannerInterventionWidgetId = "d02c7036-7de8-42e0-9287-023b9b7f369e"
        if (process.env.ENVIRONMENT === "STAGE" || process.env.ENVIRONMENT === "LOCAL") {
            bannerInterventionWidgetId = "5da941d8-23be-4f22-ab67-50d10c5ee655"
        }
        const widgetResponse = await this.widgetBuilder.buildWidgets([bannerInterventionWidgetId], this.serviceInterfaces, userContext, undefined, undefined)
        return widgetResponse?.widgets
    }

    private getPauseInfo(tz: Timezone, pauseStartDate: string, pauseEndDate: string, previousPauseEndDate: string, remainingPauses: number, packEndDate: string): PauseInfo[] {
        // Extra day added since pause starts from next day's 12AM
        const splitPauseEndDate = pauseEndDate.split(" ")
        const strippedPauseEndDate = splitPauseEndDate.length ? splitPauseEndDate[0] : pauseEndDate
        const splitPauseStartDate = pauseStartDate.split(" ")
        const strippedPauseStartDate = splitPauseStartDate.length ? splitPauseStartDate[0] : pauseStartDate
        const pauseDays = TimeUtil.diffInDays(tz, strippedPauseStartDate, strippedPauseEndDate) + 1
        const changeInPauseDays = TimeUtil.getMomentForDateString(pauseEndDate, tz).diff(TimeUtil.getMomentForDateString(previousPauseEndDate, tz), "days") + 1
        const endsOn = ( previousPauseEndDate !== null ) ?
            TimeUtil.formatDateInTimeZone(tz, new Date(TimeUtil.addDays(tz, packEndDate, changeInPauseDays)), "DD MMM YYYY")
            : TimeUtil.formatDateInTimeZone(tz, new Date(TimeUtil.addDays(tz, packEndDate, pauseDays)), "DD MMM YYYY")
        const pauseInfo = [
            {title: "Pause days used in this duration", value: AppUtil.appendDays(pauseDays)}
        ]
        pauseInfo.push(
            {title: "Membership will now end on", value: endsOn},
            {title: "Pause days left", value: AppUtil.appendDays(remainingPauses - pauseDays)}
        )
        return pauseInfo
    }

    private getHowItWorksWidget(membership: Membership): ProductListWidget {
        const maxPauseDays = GymfitUtil.convertMillisToDays(membership.maxPauseDuration)
        const items = [
            {
                subTitle: `You can pause your pack as many times as you like until you reach ${maxPauseDays} pause days`,
                icon: "/image/icons/howItWorks/pause_3.png"
            },
            {
                subTitle: "You will not be able to book classes when in pause",
                icon: "/image/icons/howItWorks/noSlot.png"
            }
        ]
        items.push({
            subTitle: "Membership will get extended basis paused days used and pack end date would be updated accordingly",
            icon: "/image/icons/howItWorks/plus.png"
        }, {
            subTitle: "You can also manually unpause your membership before the specified date",
            icon: "/image/icons/howItWorks/resume.png"
        })
        const widget = new ProductListWidget(
            "SMALL",
            {title: "How it works"},
            items
        )
        widget.hideSepratorLines = true
        return widget
    }
}
