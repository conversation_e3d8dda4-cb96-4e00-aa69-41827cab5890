import { inject, injectable } from "inversify"
import { InMemoryCacheService } from "@curefit/memory-cache"
import { IPageConfigReadOnlyDao, IPageConfigReadWriteDao, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import * as _ from "lodash"
import { UrlPathBuilder } from "@curefit/product-common"
import { HowItWorksItem, PolicyItem, ProductType } from "@curefit/product-common"
import { InfoCard } from "../common/views/WidgetView"
import { PageTitle } from "../page/Page"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

interface BiometricInfo {
    icon: string
    infoTitle: string
    infoDescription: string
    seeMoreDescription: string
    data: {
        question: string
        answer: string
    }[]
    title: string
    subTitle: string
}

interface PolicyInfo extends BiometricInfo { }

export interface MoneyBackOfferInfo {
    offerIds: string[]
    limitInDays: number
    offerRedemptionTill: number
    cancellationEndDate: string
}

@injectable()
class CultPackPageConfig extends InMemoryCacheService<any> {

    constructor(
        @inject(PAGE_CONFIG_TYPES.PageConfigReadOnlySecondaryDao) protected pageConfigDao: IPageConfigReadOnlyDao,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger, 10 * 60)
        this.load()
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    loadData(): Promise<any> {
        return this.pageConfigDao.findOne({ pageId: { $eq: "CultPackPageConfig" } }).then(pageConfig => {
            const data = pageConfig.data
            this.biometricInfo = pageConfig.data.biometricInfo
            this.howItWorksTitle = data.howItWorksTitle
            this.howItWorksItemList = _.map(<HowItWorksItem[]>data.howItWorksItemList, item => {
                return {
                    text: item.text,
                    icon: UrlPathBuilder.getIconPath("HOWITWORKS", item.icon, 1)
                }
            })
            this.howItWorksItemListIOS = _.map(<HowItWorksItem[]>data.howItWorksItemListIOS, item => {
                return {
                    text: item.text,
                    icon: UrlPathBuilder.getIconPath("HOWITWORKS", item.icon, 1)
                }
            })
            this.groupPackHowItWorksItemList = _.map(<HowItWorksItem[]>data.groupPackHowItWorksItemList, item => {
                return {
                    text: item.text,
                    icon: UrlPathBuilder.getIconPath("HOWITWORKS", item.icon, 2)
                }
            })
            this.giftPackHowItWorksItemList = _.map(<HowItWorksItem[]>data.giftPackHowItWorksItemList, item => {
                return {
                    text: item.text,
                    icon: UrlPathBuilder.getIconPath("HOWITWORKS", item.icon, 2)
                }
            })
            this.subscriptionPackHowItWorksItemList = _.map(<HowItWorksItem[]>data.subscriptionPackHowItWorksItemList, item => {
                return {
                    text: item.text,
                    icon: UrlPathBuilder.getIconPath("HOWITWORKS", item.icon, 3)
                }
            })
            this.subscriptionPackPolicyItemList = _.map(<PolicyItem[]>data.subscriptionPackPolicyItemList, item => {
                return {
                    title: item.title,
                    icon: UrlPathBuilder.getIconPath("HOWITWORKS", item.icon, 3),
                    seeMoreText: item.seeMoreText,
                    policyTitle: item.policyTitle,
                    policyList: item.policyList
                }
            })
            this.whySubscribeItems = _.map(<HowItWorksItem[]>data.whySubscribe, item => {
                return {
                    title: item.text,
                    image: item.icon
                }
            })
            this.whyMindSubscribeItems = _.map(<HowItWorksItem[]>data.whyMindSubscribe, item => {
                return {
                    title: item.text,
                    image: item.icon
                }
            })
            this.centerSelectionHeader = data.centerSelectionHeader
            this.cultJuniorHowItWorksItemList = data.cultJuniorHowItWorksItemList
            this.noshowPolicyInfo = data.noshowPolicyInfo
            this.whyPackItems = data.whyPackItems
            this.moneybackOfferInfo = data.moneybackOfferInfo
            this.startDateWindow = data.startDateWindow
            this.membershipTransferInfo = data.membershipTransferInfo
            this.cultPassBlackPolicy = data.cultPassBlackPolicy
            this.cultPassBlackPolicyIOS = data.cultPassBlackPolicyIOS
            return data
        })
    }

    public getWhyPackItems(productType: ProductType, isRestrictedToCenter: boolean): InfoCard[] {
        if (!isRestrictedToCenter) {
            return productType === "FITNESS" ? this.whyPackItems["CULT_UNLIMITED_PACK"] : this.whyPackItems["MIND_UNLIMITED_PACK"]
        } else {
            return productType === "FITNESS" ? this.whyPackItems["CULT_SELECT_PACK"] : this.whyPackItems["MIND_SELECT_PACK"]
        }
    }

    public biometricInfo: BiometricInfo
    public howItWorksTitle: string
    public howItWorksItemList: HowItWorksItem[]
    public howItWorksItemListIOS: HowItWorksItem[]
    public cultJuniorHowItWorksItemList: HowItWorksItem[]
    public groupPackHowItWorksItemList: HowItWorksItem[]
    public giftPackHowItWorksItemList: HowItWorksItem[]
    public subscriptionPackHowItWorksItemList: HowItWorksItem[]
    public subscriptionPackPolicyItemList: PolicyItem[]
    public whySubscribeItems: InfoCard[]
    public whyMindSubscribeItems: InfoCard[]
    public centerSelectionHeader: PageTitle
    public noshowPolicyInfo: PolicyInfo
    public whyPackItems: { [key: string]: InfoCard[] }
    public moneybackOfferInfo: MoneyBackOfferInfo
    public startDateWindow: number
    public membershipTransferInfo: PolicyInfo
    public cultPassBlackPolicy: PolicyInfo
    public cultPassBlackPolicyIOS: PolicyInfo
}

export default CultPackPageConfig
