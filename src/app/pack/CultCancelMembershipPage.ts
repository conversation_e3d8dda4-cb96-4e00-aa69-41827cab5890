import { injectable } from "inversify"
import { Action, ExpandableListWidget, ProductListWidget, WidgetView } from "../common/views/WidgetView"
import CultPackPageConfig from "./CultPackPageConfig"
import { ProductType } from "@curefit/product-common"
import { CultMembership } from "@curefit/cult-common"
import CultUtil, { DUBAI_CULT_CITY_ID } from "../util/CultUtil"
import * as _ from "lodash"
import { UserContext } from "@curefit/userinfo-common"
import { ActionUtil as BaseActionUtil } from "@curefit/base-utils"
import AppUtil from "../util/AppUtil"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"

@injectable()
export class CultCancelMembershipPage { // Deprecated
    widgets: (WidgetView | ExpandableListWidget)[] = []
    constructor(userContext: UserContext, productType: ProductType, membership: CultMembership, packInfo: OfflineFitnessPack, cultPackPageConfig: CultPackPageConfig) {
        // const whyPackWidget = new ProductListWidget("SMALL", { "title": "Perks of being a Cult member" },
        //     cultPackPageConfig.getWhyPackItems(productType, false)
        // )
        // whyPackWidget.hideSepratorLines = true
        // this.widgets.push(whyPackWidget)
        // const membershipId = membership.id
        // const isPauseAllowed = membership.remainingPauseDays > 0
        // const pauseAction = CultUtil.pauseCultPackAction(userContext, productType, membership, cultPackPageConfig, packInfo)
        // const transferMembershipAction: Action  = this.getTransferMembershipAction(userContext, productType, membership)
        // const productId = packInfo.productId
        // const cancelActionMeta = {
        //     productId,
        //     membershipId,
        //     productType,
        //     isSubscription: membership.isSubscription
        // }
        // const travelActions: Action[] = []
        // travelActions.push(this.getCancelMembershipAction(cancelActionMeta, "Still Cancel", "TRAVEL"))
        // if (!_.isEmpty(pauseAction)) {
        //     pauseAction.title = "Pause"
        //     travelActions.push(pauseAction)
        // }
        // const isSelectPack = false
        // this.widgets.push(productType === "FITNESS" ? this.getCultExpandableListWidget(userContext, cancelActionMeta, isSelectPack, isPauseAllowed, pauseAction, transferMembershipAction) : this.getMindExpandableListWidget(userContext, cancelActionMeta, isSelectPack, isPauseAllowed, pauseAction, transferMembershipAction))
    }

    getCultExpandableListWidget(userContext: UserContext, cancelActionMeta: any, isSelectPack: boolean, isPauseAllowed: boolean, pauseAction: Action, transferMembershipAction: Action): WidgetView | ExpandableListWidget {
        const bookClassAction: Action = {
            actionType: "NAVIGATION",
            title: "Book a class",
            url: BaseActionUtil.getBookCultClassUrl("FITNESS", AppUtil.isNewClassBookingSuppoted(userContext), "cancelMembership")
        }
        const packBrowseAction: Action = {
            actionType: "NAVIGATION",
            title: "Browse Packs",
            url: CultUtil.getCultMindClpBrowsePackAction("FITNESS")
        }
        return {
            widgetType: "EXPANDABLE_LIST_WIDGET",
            title: "Tell us why you want to cancel",
            allowMultipleOpen: false,
            showDivider: true,
            widgets: [
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "I’m relocating soon",
                    rebuttal: transferMembershipAction && `If you are relocating, then you can choose to transfer your pack to another city for free.`,
                    actions: transferMembershipAction ? [transferMembershipAction, this.getCancelMembershipAction(cancelActionMeta, "Still Cancel", "RELOCATION")] : [this.getCancelMembershipAction(cancelActionMeta, "Cancel and Refund Membership", "RELOCATION")]
                },
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "I am busy or travelling and hence can’t attend classes regularly",
                    rebuttal: isPauseAllowed && "We understand that life happens :) You can choose to pause your pack when you need a break for work or personal commitments. We will extend your membership by the number of pause days.",
                    actions: pauseAction ? [pauseAction, this.getCancelMembershipAction(cancelActionMeta, "Still Cancel", "BUSY")] : [this.getCancelMembershipAction(cancelActionMeta, "Cancel and Refund Membership", "BUSY")]
                },
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "I didn’t find the experience COVID-19 safe",
                    cancelPrompt: "We’re terribly sorry to hear that. We’re constantly innovating to improve our experience. We’d love to hear what we could’ve done better.",
                    actions: [this.getCancelMembershipAction(cancelActionMeta, "Cancel and Refund Membership", "BAD_CLASSES")]
                },
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "I’m not sure if CULT can help me achieve my fitness goals",
                    cancelPrompt: "At Cult we strive to provide offerings that meet every fitness need. We'd love to help you find the right fit. Do let us know what went wrong",
                    actions: [this.getCancelMembershipAction(cancelActionMeta, "Cancel and Refund Membership", "GOAL_NOT_MET")]
                },
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "I am purchasing another pack",
                    rebuttal: "That's great! We are excited to see your commitment to fitness",
                    actions: [packBrowseAction, this.getCancelMembershipAction(cancelActionMeta, "Still Cancel", "ANOTHER_PACK")]
                },
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "My preferred slots are not available",
                    cancelPrompt: `We are terribly sorry to hear that. We’re constantly innovating to make more slots available to all our members. For classes that are running full, you can choose to join the waitlist. ${isSelectPack ? "Also, we urge you to try booking classes in nearby Cult centers." : ""}`,
                    actions: [bookClassAction, this.getCancelMembershipAction(cancelActionMeta, "Still Cancel", "SLOTS_NOT_AVAILABLE")]
                },
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "My reason is not listed",
                    cancelPrompt: "We are sorry to see you go! Tell us what went wrong",
                    actions: [this.getCancelMembershipAction(cancelActionMeta, "Cancel and Refund Membership", "NOT_LISTED")]
                }
            ]
        }
    }

    getMindExpandableListWidget(userContext: UserContext, cancelActionMeta: any, isSelectPack: boolean, isPauseAllowed: boolean, pauseAction: Action, transferMembershipAction: Action): WidgetView | ExpandableListWidget {
        const bookClassAction: Action = {
            actionType: "NAVIGATION",
            title: "Book a class",
            url: BaseActionUtil.getBookCultClassUrl("MIND", AppUtil.isNewClassBookingSuppoted(userContext), "cancelMembership")
        }
        const packBrowseAction: Action = {
            actionType: "NAVIGATION",
            title: "Browse Packs",
            url: BaseActionUtil.mindPackBrowse()
        }
        return {
            widgetType: "EXPANDABLE_LIST_WIDGET",
            title: "Tell us why you want to cancel",
            allowMultipleOpen: false,
            showDivider: true,
            widgets: [
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "I’m relocating soon",
                    rebuttal: transferMembershipAction && `If you are relocating, then you can choose to transfer your pack to another city where Mind.fit is active. You can also choose to transfer the membership to another person. You can transfer your membership yourself via the cult.fit app.`,
                    actions: transferMembershipAction ? [transferMembershipAction, this.getCancelMembershipAction(cancelActionMeta, "Still Cancel", "RELOCATION")] : [this.getCancelMembershipAction(cancelActionMeta, "Cancel and Refund Membership", "RELOCATION")]
                },
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "I am busy or travelling and hence can’t attend classes regularly",
                    rebuttal: isPauseAllowed && "We understand that life happens :) You can choose to pause your pack when you need a break for work or personal commitments. We will extend your membership by the number of pause days.",
                    actions: pauseAction ? [pauseAction, this.getCancelMembershipAction(cancelActionMeta, "Still Cancel", "BUSY")] : [this.getCancelMembershipAction(cancelActionMeta, "Cancel and Refund Membership", "BUSY")]
                },
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "I didn’t like the Mind.fit experience",
                    cancelPrompt: "We’re terribly sorry to hear that. We’re constantly innovating to improve our experience. We’d love to hear what we could’ve done better.",
                    actions: [this.getCancelMembershipAction(cancelActionMeta, "Cancel and Refund Membership", "BAD_CLASSES")]
                },
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "I’m not sure if MIND.fit can help reach my goals",
                    cancelPrompt: "At Mind.fit we strive to provide offerings that meet every mental wellness need. We'd love to help you find the right fit. Do let us know what went wrong",
                    actions: [this.getCancelMembershipAction(cancelActionMeta, "Cancel and Refund Membership", "GOAL_NOT_MET")]
                },
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "I am purchasing another pack",
                    rebuttal: "'That's great! We are excited to see your commitment to mind.fit",
                    actions: [packBrowseAction, this.getCancelMembershipAction(cancelActionMeta, "Cancel and Refund Membership", "ANOTHER_PACK")]
                },
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "My preferred slots are not available",
                    cancelPrompt: `We are terribly sorry to hear that. We’re constantly innovating to make more slots available to all our members. For classes that are running full, you can choose to join the waitlist. ${isSelectPack ? "Also, we urge you to try booking classes in nearby Cult centers." : ""}`,
                    actions: [bookClassAction, this.getCancelMembershipAction(cancelActionMeta, "Cancel and Refund Membership", "SLOTS_NOT_AVAILABLE")]
                },
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "My reason is not listed",
                    cancelPrompt: "We are sorry to see you go! Tell us what went wrong",
                    actions: [this.getCancelMembershipAction(cancelActionMeta, "Cancel and Refund Membership", "NOT_LISTED")]
                }
            ]
        }
    }

    private getCancelMembershipAction(actionMeta: any, actionTitle: string, reasonCode: string): Action {
        return {
            actionType: "CANCEL_CULT_SUBSCRIPTION",
            title: actionTitle,
            meta: {
                alertTitle: "Cancel Membership",
                alertMessage: `Are you sure you want to cancel your membership?`,
                ...actionMeta,
                reasonCode: reasonCode
            }

        }
    }
}
