import { CATALOG_CLIENT_TYPES, ICatalogueService, ICatalogueServicePMS } from "@curefit/catalog-client"
import { Benefit, BenefitType, Membership } from "@curefit/membership-commons"
import { OFFER_SERVICE_CLIENT_TYPES, OfferServiceV3, OfferUtil as OffersUtil } from "@curefit/offer-service-client"
import { Tenant, User } from "@curefit/user-common"
import { UserContext } from "@curefit/userinfo-common"
import { inject, injectable } from "inversify"
import * as moment from "moment"
import {
    Action,
    CenterSelectionWidget,
    DatePickerWidget, InfoWidget,
    ManageOptions,
    ManageOptionsWidget,
    ProductListWidget,
    WidgetView
} from "../common/views/WidgetView"
import { CultPackStartDateOptions, DerivedCityDetails } from "../cult/cultpackpage/CultPackCommonViewBuilder"

import {
    ActionType,
    CardContainerWidget,
    CultActivePackInfoWidget,
    CultPackInfoWidget,
    CultPackPage,
    InfoCard,
    ListItem,
    ListWidgetV2, PageTypes,
    ProductNote,
    ProductNoteWidget,
    ProductOffer,
    ProductOfferWidgetV2,
    WidgetHeader
} from "@curefit/apps-common"
import { ActionUtil, OfferUtil } from "@curefit/base-utils"
import { CENTER_SERVICE_CLIENT_TYPES, ICenterService } from "@curefit/center-service-client"
import { CenterResponse } from "@curefit/center-service-common"
import { ErrorFactory } from "@curefit/error-client"
import { City } from "@curefit/location-common"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import { CultProductPricesResponse, OfferV2 } from "@curefit/offer-common"
import { HowItWorksItem, ImageCategory, UrlPathBuilder } from "@curefit/product-common"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { Header } from "@curefit/vm-models"
import * as _ from "lodash"
import { clone } from "lodash"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import CultActivePackInfoWidgetViewBuilder from "../cult/viewbuilder/CultActivePackInfoWidget"
import { ErrorCodes } from "../error/ErrorCodes"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import WidgetBuilder from "../page/vm/WidgetBuilder"
import AppUtil, { getAppUrl } from "../util/AppUtil"
import { CacheHelper } from "../util/CacheHelper"
import CultUtil, { isMembershipCurrent } from "../util/CultUtil"
import GymfitUtil from "../util/GymfitUtil"
import { MembershipItemUtil } from "../util/MembershipItemUtil"
import PlayUtil, {
    PLAY_MEMBERSHIP_PRIMARY_BENEFITS,
    PLAY_NAS_PRICE_HIKE_BANNER_ID,
    PlaySelectPackDetails
} from "../util/PlayUtil"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"
import { DATE_SELECTION_WINDOW } from "../common/Constants"
import { IOrderService, OMS_API_CLIENT_TYPES } from "@curefit/oms-api-client"
import { FitnessPack } from "@curefit/cult-common"
import { BASE_TYPES, Logger } from "@curefit/base"

export type PlaySLPMembershipInfo = {
    accessWorkout: string;
    accessCenter: string;
    isSLPPack: boolean
}
export type PlayCitySLPMembershipInfo = {
    accessWorkout: string;
    accessCity: string;
    isCitySLPPack: boolean
}

export type PlayLimitedSessionMembershipInfo = {
    accessWorkout: string;
    accessCenter: string;
    isPlayLimitedSLPMembership: boolean
    maxLimitedSession: number
}

interface IPackStartDateResponse {
    membershipEndStartDate: string
    startDate: string
    dateSelectionWindow: number
    productTitle: string
}

@injectable()
class PlayPackDetailViewBuilder {
    constructor(
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) protected catalogueService: ICatalogueService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReaderPMS) protected catalogueServicePMS: ICatalogueServicePMS,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) protected offerServiceV3: OfferServiceV3,
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) public membershipService: IMembershipService,
        @inject(CENTER_SERVICE_CLIENT_TYPES.CenterService) private centerService: ICenterService,
        @inject(LOCATION_TYPES.CityService) protected cityService: ICityService,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferUtil) protected offersUtil: OffersUtil,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) protected serviceInterfaces: CFServiceInterfaces,
        @inject(OMS_API_CLIENT_TYPES.OrderService) private omsApiClient: IOrderService,
        @inject(CUREFIT_API_TYPES.WidgetBuilder) public widgetBuilder: WidgetBuilder,
        @inject(BASE_TYPES.ILogger) protected logger: Logger,
    ) {
    }

    async getPrePurchaseView(
        userContext: UserContext,
        productId: string,
        selectedStartDate: string,
        preferredCenterId: string,
        centerResponsePromise: Promise<CenterResponse>,
        workoutId: string,
        workoutName: string,
    ): Promise<CultPackPage> {
        const user = await userContext.userPromise
        const packPage = new CultPackPage()
        const tz = userContext.userProfile.timezone

        // const preferredCenterId: string = ""
        const selectedPack: OfflineFitnessPack = await this.catalogueServicePMS.getProduct(productId)
        const city = this.cityService.getCityById(userContext.userProfile.cityId)

        if (userContext.userProfile.cityId != selectedPack.clientMetadata.cityId) {
            throw this.errorFactory.withCode(ErrorCodes.ERR_PLAY_PACK_INVALID_CITY, 400).withDebugMessage("Pack not present in the given city").build()
        }

        const offerV3Promise: Promise<CultProductPricesResponse> = this.getOffersV3Promise(userContext, productId, preferredCenterId, city)
        const playBenefits: Benefit[] = [{
            name: "PLAY",
            allowOverlap: false,
            type: BenefitType.STATIC,
        }]
        const packDurationInSeconds = selectedPack.product.durationInDays * 86400
        const earliestStartDatePlay = await this.membershipService.getEarliestStartDate(userContext?.userProfile?.userId, Tenant.CUREFIT_APP, playBenefits, packDurationInSeconds)

        const { minStartDate, maxStartDate } = await this.getMiniumAndMaximumStartDate(centerResponsePromise, earliestStartDatePlay, packDurationInSeconds)
        const widgetPromises: Promise<WidgetView>[] = []

        const packOfferWidgetPromise = this.packOfferWidget(selectedPack, offerV3Promise)
        const packOfferWidget = await packOfferWidgetPromise
        const isPackOfferWidgetExist = packOfferWidget ? true : false
        widgetPromises.push(this.playPackInfoWidget(userContext, selectedPack, offerV3Promise, isPackOfferWidgetExist, true))
        if (packOfferWidgetPromise) {
            widgetPromises.push(packOfferWidgetPromise)
        }

        const priceHikeBannerWidgetPromise = this.buildPriceHikeBannerWidget(userContext)
        widgetPromises.push(priceHikeBannerWidgetPromise)

        const centerSelectionWidget = this.buildCenterSelectionWidget(
            userContext, preferredCenterId, await centerResponsePromise,
            workoutName, productId
        )
        const startDateOptions = this.getStartDateOptions(userContext, minStartDate, selectedStartDate, maxStartDate)
        const startDateWidget = this.buildStartDateWidget(startDateOptions)
        const wrapperWidgets: WidgetView[] = [centerSelectionWidget, startDateWidget]
        widgetPromises.push(this.cardContainerWidget(wrapperWidgets))
        const prePurchaseProductNote = await this.getPrePurchaseProductNote(selectedPack, offerV3Promise)
        if (prePurchaseProductNote && prePurchaseProductNote.length > 0) {
            widgetPromises.push(this.buildProductNoteWidget(prePurchaseProductNote))
        }
        widgetPromises.push(this.getPackBenefits(selectedPack, false, [], null, null, null, false))
        const policyWidget = this.buildPolicyWidgets(false, false)
        if (policyWidget) {
            widgetPromises.push(policyWidget)
        }
        widgetPromises.push(this.howItWorksWidget(false, false, false, false))


        packPage.widgets = await Promise.all(widgetPromises)
        packPage.widgets = _.filter(packPage.widgets, widget => !_.isEmpty(widget))

        packPage.actions = await this.getPrePurchasePageActions(userContext, selectedPack, preferredCenterId, startDateOptions)
        // packPage.breadCrumbs = this.getPackBreadCrumbs(selectedPack.title)

        const result = CultUtil.getPackPriceAndOfferIdV2(selectedPack, await offerV3Promise)
        const offerId = !_.isEmpty(result.offerIds) ? result.offerIds[0] : undefined
        packPage.packSummary = {
            productId: selectedPack.productId,
            productType: selectedPack.productType,
            offerId,
            startDateOptions: startDateOptions,
            preferredCenterId: preferredCenterId,
            centerServicePreferredCenterId: preferredCenterId,
            workoutId: workoutId,
        }

        if (startDateOptions.selectedDate) {
            if (startDateOptions.canChangeStartDate)
                packPage.packSummary.startDateOptions = TimeUtil.getDaysFrom(tz, startDateOptions.selectedDate, 7, false)
            else
                packPage.packSummary.startDateOptions = [startDateOptions.selectedDate]
        }
        return packPage
    }

    async getPostPurchaseView(userContext: UserContext, membershipId: number): Promise<CultPackPage> {
        const packPage = new CultPackPage()
        const tz = userContext.userProfile.timezone
        const userId = userContext.userProfile.userId

        const membership = await this.membershipService.getMembershipById(membershipId)
        if (membership.userId != userId) {
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 401).withDebugMessage("Operation not allowed").build()
        }

        const selectPackDetails: PlaySelectPackDetails = PlayUtil.isPlaySelectMembership(membership)
        const slpMembership = MembershipItemUtil.isPlaySportLevelMembership(membership)
        const limitedSessionMembership = MembershipItemUtil.isPlayLimitedSLPMembership(membership)
        const isCityLimitedMembership = PlayUtil.isEnterPriseLimitedMembership(membership)
        const centerNames: string[] = []
        if (selectPackDetails?.isSelectMembership && !_.isEmpty(selectPackDetails.centerIds)) {
            for (let i = 0; i < selectPackDetails.centerIds.length; i += 1) {
                const center = await this.centerService.getCenterById(Number(selectPackDetails.centerIds[i]))
                centerNames.push(center.name)
            }
        }
        if (slpMembership?.isSLPPack || limitedSessionMembership?.isPlayLimitedSLPMembership) {
            const center = await this.centerService.getCenterById(Number(slpMembership.accessCenter))
            centerNames.push(center.name)
        }


        const product: OfflineFitnessPack = await this.catalogueServicePMS.getProduct(membership.productId)

        const widgetPromises: Promise<WidgetView>[] = []

        widgetPromises.push(this.buildPlayActivePackInfoWidget(userContext, product, membership, tz,
            selectPackDetails, slpMembership.isSLPPack, limitedSessionMembership.isPlayLimitedSLPMembership))
        widgetPromises.push(this.getPackBenefits(
            product,
            selectPackDetails?.isSelectMembership,
            centerNames,
            slpMembership,
            limitedSessionMembership,
            membership,
            isCityLimitedMembership
        ))
        const policyWidget = this.buildPolicyWidgets(
            selectPackDetails?.isSelectMembership,
            limitedSessionMembership?.isPlayLimitedSLPMembership
        )
        if (policyWidget) {
            widgetPromises.push(policyWidget)
        }
        widgetPromises.push(this.howItWorksWidget(
            selectPackDetails?.isSelectMembership,
            slpMembership?.isSLPPack,
            limitedSessionMembership?.isPlayLimitedSLPMembership,
            isCityLimitedMembership
        ))

        packPage.widgets = await Promise.all(widgetPromises)
        packPage.widgets = _.filter(packPage.widgets, widget => !_.isEmpty(widget))

        packPage.packSummary = {
            productId: product.productId,
            productType: product.productType,
        }
        return packPage
    }

    private async playPackInfoWidget(
        userContext: UserContext,
        packProduct: OfflineFitnessPack,
        offersV3Promise: Promise<CultProductPricesResponse>,
        isPackOfferWidgetExist: boolean,
        isBuyFlow: boolean,
    ): Promise<CultPackInfoWidget> {
        const packInfo = packProduct
        let price = packInfo.price, title = ""
        const isPartOfGstSegment = await AppUtil.doesUserBelongToGSTsplitExperiment(userContext, this.serviceInterfaces)
        let priceBreakup = null
        if (isPartOfGstSegment) {
            const taxBreakup = await this.offersUtil.getTaxBreakUpForPlayPacks(await offersV3Promise)
            const priceBreakupMap = taxBreakup[packInfo.productId]
            priceBreakup = {
                basePrice: priceBreakupMap.basePrice,
                tax: priceBreakupMap.taxAmount
            }
        }
        if (isBuyFlow) {
            const result = CultUtil.getPackPriceAndOfferIdV2(packInfo, await offersV3Promise)
            price = result.price
            title = packInfo.title
        }
        const category: ImageCategory = ImageCategory.HERO
        const userAgent = userContext.sessionInfo.userAgent
        return {
            widgetType: "CULT_PACK_INFO_WIDGET",
            title: title,
            gradientColor: packInfo.productType === "PLAY" ? ["#fcc161", "#fa9f86"] : ["#bdd9ff", "#b6fdff"],
            price: price,
            dividerType: "NONE",
            image: PlayUtil.PLAY_PACK_HERO_IMAGE,
            contentContainerStyle: isPackOfferWidgetExist ? {} : { marginBottom: -20 },
            priceBreakup: priceBreakup
        }
    }

    async getOffersV3Promise(userContext: UserContext, productId: string, preferredCenterId: string, city: City): Promise<CultProductPricesResponse> {
        const user = await userContext.userPromise
        return await this.offerServiceV3.getPlayPackPrices({
            cultCityId: city.cultCityId,
            cityId: userContext.userProfile.cityId,
            userInfo: {
                userId: userContext.userProfile.userId,
                deviceId: userContext.sessionInfo.deviceId,
                email: user?.email,
                phone: user?.phone,
                workEmail: user?.workEmail
            },
            source: AppUtil.callSourceFromContext(userContext),
            centerId: preferredCenterId,
            productIds: [productId],
            centerServiceId: preferredCenterId
        })
    }


    private async packOfferWidget(
        packProduct: OfflineFitnessPack,
        offersV3Promise: Promise<CultProductPricesResponse>
    ): Promise<ProductOfferWidgetV2> {
        const offersV3Response = await offersV3Promise
        const offerItem = offersV3Response ? CultUtil.getOfferItem(offersV3Response, packProduct.productId) : null

        if (offerItem != null) {
            const actionType: ActionType = "SHOW_OFFERS_TNC_MODAL"
            const filteredOfferItems: OfferV2[] = []
            offerItem.offers = OfferUtil.segregateNoCostEMIOffers(offerItem.offers).get("OTHER_OFFERS")
            // Remove fit-club from offers as there is a separate widget
            offerItem.offers.forEach((offer) => {
                if (offer.addons) {
                    filteredOfferItems.push(offer)
                } else if (!_.isEmpty(offer.description)) {
                    filteredOfferItems.push(offer)
                }
            })
            const offerItems: ProductOffer[] = []
            filteredOfferItems.forEach(offer => {
                let offerText = undefined
                if (offer.uiLabels && !_.isEmpty(offer.uiLabels.cartLabel)) {
                    offerText = offer.uiLabels.cartLabel
                } else {
                    _.forEach(offer.addons, addon => {
                        if (addon && addon.uiLabels && !_.isEmpty(addon.uiLabels.cartLabel)) {
                            offerText = addon.uiLabels.cartLabel
                        }
                    })
                }
                if (!_.isEmpty(offerText)) {
                    offerItems.push({
                        title: offerText,
                        iconType: "/image/icons/cult/tick.png",
                        tnc: {
                            title: "T&C",
                            action: {
                                actionType: actionType,
                                meta: {
                                    title: "Offer Details",
                                    dataItems: offer.tNc,
                                    url: offer.tNcUrl
                                }
                            }
                        }
                    })
                }
            })

            if (offerItems.length > 0) {
                return {
                    widgetType: "PRODUCT_OFFER_WIDGET_V2",
                    offerItems: [...offerItems],
                    dividerType: "NONE",
                    contentContainerStyle: { marginBottom: 10 }
                }
            }
        }
        return null
    }

    private async buildPriceHikeBannerWidget(userContext: UserContext): Promise<any> {
        const widgetId = PLAY_NAS_PRICE_HIKE_BANNER_ID
        const widgetResponse = await this.widgetBuilder.buildWidgets([widgetId], this.serviceInterfaces, userContext, undefined, undefined)
        if (!_.isEmpty(widgetResponse) && !_.isEmpty(widgetResponse.widgets)) {
            const widget: any = widgetResponse.widgets[0] && widgetResponse.widgets[0].widgetType === "BANNER_CAROUSEL_WIDGET" ? widgetResponse.widgets[0] : undefined
            // updating layoutprops in same object was causing issue in same widget displaying in other place
            const bannerWidget = clone(widget)
            bannerWidget.layoutProps.backgroundColor = null
            bannerWidget.hasDividerBelow = false
            bannerWidget.layoutProps.v2 = true
            bannerWidget.dividerType = "DIVIDER30"
            bannerWidget.layoutProps.useShadow = true
            return bannerWidget
        }
        return undefined
    }


    private async cardContainerWidget(widgets: WidgetView[]): Promise<CardContainerWidget> {
        return {
            widgets: widgets,
            widgetType: "CARD_CONTAINER_WIDGET",
            dividerType: "DIVIDER30"
        }
    }

    private buildCenterSelectionWidget(
        userContext: UserContext,
        preferredCenterId: string,
        centerResponse: CenterResponse,
        workoutName: String,
        productId: string
    ): CenterSelectionWidget {
        const action: Action = {
            title: "Pick a center",
            showHelp: false,
            showFavourite: false,
            actionType: "SHOW_PLAY_PACK_SPORT_MODAL",
            meta: {
                productId: productId,
            }
        }
        const prefixText = ""
        const centerName = preferredCenterId && centerResponse ? centerResponse.name : undefined
        const preferredCenterName = workoutName && centerName ? workoutName + ": " + centerName : undefined

        const centerSelectionWidget: CenterSelectionWidget = {
            title: "Select preferred sport & center",
            prefixText: prefixText,
            canChangeCenter: true, // requestParams.canChangeCenter,
            preferredCenterId: Number(preferredCenterId),
            preferredCenterName: preferredCenterName,
            widgetType: "CENTER_PICKER_WIDGET",
            action: action,
            // infoAction: {
            //     actionType: "SHOW_ALERT_MODAL",
            //     meta: {
            //         title: infoActionTitle,
            //         subTitle: infoActionSubTitle,
            //         actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
            //     }
            // },
            showHighlightedText: !preferredCenterId
        }
        return centerSelectionWidget
    }


    private getStartDateOptions(
        userContext: UserContext,
        previousMembershipEndDate: string,
        selectedStartDate: string,
        maxStartDate: string
    ): CultPackStartDateOptions {
        const tz = userContext.userProfile.timezone
        const today = TimeUtil.todaysDateWithTimezone(tz)
        let startDate: string = today
        const canChangeStartDate: boolean = true

        startDate = previousMembershipEndDate

        const { minimumDate, selectedDate, maximumDate } = PlayUtil.getPackStartDateOptions(userContext, null, canChangeStartDate, selectedStartDate, 30, startDate, maxStartDate)
        const selectedDateFinal = selectedDate
        return {
            minEligibleDate: minimumDate,
            maxEligibleDate: maximumDate,
            selectedDate: selectedDateFinal,
            canChangeStartDate: canChangeStartDate
        }
    }

    private buildStartDateWidget(
        startDateOptions: CultPackStartDateOptions,
    ): DatePickerWidget {

        const datePickerWidget: DatePickerWidget = {
            title: "Pick a Start Date",
            startDate: startDateOptions.minEligibleDate,
            endDate: startDateOptions.maxEligibleDate,
            selectedDate: startDateOptions.selectedDate,
            canChangeStartDate: startDateOptions.canChangeStartDate,
            confirmActionType: "SET_PLAY_PACK_START_DATE",
            widgetType: "DATE_PICKER_WIDGET"
        }
        return datePickerWidget
    }

    private async getPrePurchasePageActions(
        userContext: UserContext,
        selectedPack: OfflineFitnessPack,
        selectedCenterId: string,
        startDateOptions: CultPackStartDateOptions,
    ): Promise<Action[]> {
        if (!selectedCenterId) {
            return [{
                title: "Pick a preferred centre",
                showHelp: false,
                showFavourite: false,
                actionType: "SHOW_PLAY_PACK_SPORT_MODAL",
                productType: selectedPack.productType,
                meta: {
                    productId: selectedPack.productId,
                }
                // meta: {
                //     packId: builderParams.packInfo.cultPackId,
                //     productType: requestParams.productType,
                //     ageCategory: builderParams.cultPack.ageCategory,
                //     showAvailableWorkouts: true,
                //     pageFrom: requestParams.productType === "FITNESS" ? "CultPack" : "MindPack",
                //     useCenterServiceId: true,
                // },
            }]
        }

        if (!startDateOptions.selectedDate) {
            return [{
                title: "Pick a start date",
                actionType: "SHOW_PLAY_DATE_PICKER",
                meta: {
                    startDate: startDateOptions.minEligibleDate,
                    endDate: startDateOptions.maxEligibleDate,
                    selectedDate: startDateOptions.selectedDate,
                    canChangeStartDate: startDateOptions.canChangeStartDate,
                }
            }]
        }

        const isAvailable = true // CultUtil.isPackAvailableForBuy(builderParams.cultPack, builderParams.preferredCenter.id)
        if (isAvailable) {
            return [{
                title: "BUY NOW",
                actionType: "GET_NEW_PLAY_PACK",
            }]
        } else {
            return [{
                title: "Sold out",
                actionType: "POP_ACTION"
            }]
        }
    }

    private async getDerivedCityDetails(userContext: UserContext, membership: Membership): Promise<DerivedCityDetails> {
        const tenant: Tenant = AppUtil.getTenantFromUserContext(userContext)
        const city = userContext.userProfile.city || this.cityService.getDefaultCity(tenant)
        return { city, cultCityId: city.cultCityId, cityId: city.cityId, defaultCultCenter: city.defaultCultCenter, defaultMindCenter: city.defaultMindCenter }
    }

    private async getPackBenefits(
        playPack: OfflineFitnessPack,
        isSelectMembership: boolean,
        centerNames: string[],
        slpMembership: PlaySLPMembershipInfo,
        limitedSessionMembership: PlayLimitedSessionMembershipInfo,
        membership: Membership,
        isCityLimitedMembership: boolean
    ): Promise<ListWidgetV2> {
        const items: ListItem[] = []

        if (limitedSessionMembership != null && limitedSessionMembership.isPlayLimitedSLPMembership) {
            const centerInfo: string = centerNames.join(",")
            items.push({
                title: "Sport access",
                subTitle: `Access ${limitedSessionMembership.maxLimitedSession} sessions of ${PlayUtil.getSportNameById(limitedSessionMembership.accessWorkout)} at ${centerInfo}`,
                icon: "/image/icons/howItWorks/court.png"
            })

            items.push({
                title: "100% Safe Center",
                subTitle: `We follow strict rules to avoid contact and maintain social distancing in all our centers`,
                icon: "/image/icons/howItWorks/<EMAIL>"
            })
        } else if (slpMembership != null && slpMembership.isSLPPack) {
            const centerInfo: string = centerNames.join(",")
            items.push({
                title: "Unlimited access",
                subTitle: `Play unlimited ${PlayUtil.getSportNameById(slpMembership.accessWorkout)} at ${centerInfo}`,
                icon: "/image/icons/howItWorks/<EMAIL>"
            })

            items.push({
                title: "100% Safe Center",
                subTitle: `We follow strict rules to avoid contact and maintain social distancing in all our classes`,
                icon: "/image/icons/howItWorks/<EMAIL>"
            })

            items.push({
                title: "Access 2 sessions per month",
                subTitle: "in other centres.",
                icon: UrlPathBuilder.getIconPath("HOWITWORKS", "groupWorkout", 3)
            })
        } else if (isSelectMembership) {
            const centerInfo: string = centerNames.join(",")
            items.push({
                title: "Unlimited access",
                subTitle: `Play unlimited sports at ${centerInfo}`,
                icon: "/image/icons/howItWorks/<EMAIL>"
            })

            items.push({
                title: "100% Safe Center",
                subTitle: `We follow strict rules to avoid contact and maintain social distancing in all our classes`,
                icon: "/image/icons/howItWorks/<EMAIL>"
            })
        } else if (isCityLimitedMembership) {
            items.push({
                title: "100% Safe Center",
                subTitle: `We follow strict rules to avoid contact and maintain social distancing in all our classes`,
                icon: "/image/icons/howItWorks/<EMAIL>"
            })

            const maxPauseDays = membership ? GymfitUtil.convertMillisToDays(membership.maxPauseDuration) : playPack.product.pauseDays
            items.push({
                title: "Pause your pack anytime",
                subTitle: `You can pause your pack any time for a maximum of ${maxPauseDays} days`,
                icon: "/image/icons/howItWorks/<EMAIL>"
            })

        } else {
            items.push({
                title: "Unlimited access",
                subTitle: "to all cult play centers and play unlimited sports",
                icon: "/image/icons/howItWorks/<EMAIL>"
            })

            items.push({
                title: "2 sessions per month",
                subTitle: "to all ELITE/PRO gyms or cult centers",
                icon: UrlPathBuilder.getIconPath("HOWITWORKS", "groupWorkout", 3)
            })

            items.push({
                title: "100% Safe Center",
                subTitle: `We follow strict rules to avoid contact and maintain social distancing in all our classes`,
                icon: "/image/icons/howItWorks/<EMAIL>"
            })
        }

        if (playPack.product.pauseDays > 0 && !isCityLimitedMembership) {
            items.push({
                title: "Pause your pack anytime",
                subTitle: `You can pause your pack any time for a maximum of ${playPack.product.pauseDays} days`,
                icon: "/image/icons/howItWorks/<EMAIL>"
            })
        }

        items.push({
            title: "For above 18 years of age",
            subTitle: `This pack is only eligible for people above 18 years of age`,
            icon: "/image/icons/howItWorks/age-plus-4.png"
        })

        if (items.length > 0) {
            const header = {
                title: "Highlights",
            }
            const packBenefitsWidget = new ListWidgetV2()
            packBenefitsWidget.items = items
            packBenefitsWidget.header = header
            return packBenefitsWidget
        }
        return null
    }

    async buildPolicyWidgets(
        isSelectMembership: boolean,
        isPlayLimitedSLPMembership: boolean
    ): Promise<ListWidgetV2> {
        let contentId = "cultpassplaypolicy"

        if (isSelectMembership) {
            contentId = "playselectpolicy"
        } else if (isPlayLimitedSLPMembership) {
            contentId = "playlimitedslppolicy"
        }

        const header: WidgetHeader = {
            title: "Policies",
            titleStyle: {
                marginBottom: 0,
            },
            seemore: {
                title: "VIEW ",
                url: ActionUtil.infoPage(contentId),
                actionType: "NAVIGATION",
            }
        }

        const policyListWidget = new ListWidgetV2()
        policyListWidget.items = []
        policyListWidget.header = header
        return policyListWidget
    }

    async howItWorksWidget(isSelectMembership: boolean, isSLPMembership: boolean, isPlayLimitedSLPMembership: boolean, isCityLimitedMembership: boolean): Promise<ProductListWidget> {
        try {
            const header: Header = {
                title: "How it works",
                titleProps: {
                    style: {
                        fontSize: 24
                    }
                }
            }

            const infoCards: InfoCard[] = []
            const howItWorksItemList: HowItWorksItem[] = [
                {
                    "icon": UrlPathBuilder.getIconPath("HOWITWORKS", "playSports", 2),
                    "text": "Play sports: Book a sport that you like. Reach the center on time and enjoy your sport"
                },
            ]

            if (!isSelectMembership && !isSLPMembership && !isPlayLimitedSLPMembership && !isCityLimitedMembership) {
                howItWorksItemList.push({
                    "icon": UrlPathBuilder.getIconPath("HOWITWORKS", "gymWorkout", 3),
                    "text": "Visit a cult gym at any time, check-in via your phone and start your workout. Access upto 2 sessions per month to all ELITE/PRO gyms or cult centers"
                })
            }


            howItWorksItemList.forEach(item => {
                infoCards.push({
                    subTitle: item.text,
                    icon: item.icon,
                    subTitleFontSize: 14
                })
            })
            return {
                widgetType: "PRODUCT_LIST_WIDGET",
                type: "SMALL",
                hideSepratorLines: true,
                header: header,
                items: infoCards,
                noTopPadding: true
            }
        }
        catch (e) {
            return undefined
        }
    }

    private async buildPlayActivePackInfoWidget(
        userContext: UserContext,
        packInfo: OfflineFitnessPack,
        membership: Membership,
        tz: Timezone,
        selectPackDetails: PlaySelectPackDetails,
        isSlpMembership: boolean,
        isLimitedSessionPack: boolean
    ): Promise<CultActivePackInfoWidget> {
        const user = await this.userCache.getUser(userContext.userProfile.userId)
        let actions
        actions = MembershipItemUtil.isCurrentOrFutureActiveMembership(membership)
            ? await this.getPlayPackActions(userContext, packInfo, membership, user, selectPackDetails, isSlpMembership, isLimitedSessionPack)
            : null

        const rightInfoText = this.getRightInfo(membership, userContext, tz)
        const activePackInfoParams = {
            userContext,
            packInfo,
            membership,
            tz,
            actions,
            rightInfoText,
        }

        return new CultActivePackInfoWidgetViewBuilder().buildView(activePackInfoParams, this.centerService)
    }

    async getPlayPackActions(
        userContext: UserContext,
        packInfo: OfflineFitnessPack,
        membership: Membership,
        user: User,
        selectPackDetails: PlaySelectPackDetails,
        isSlpMembership: boolean,
        isLimitedSessionPack: boolean
    ) {

        const { osName, appVersion, cpVersion } = userContext.sessionInfo
        const actions: Action[] = []
        const showPackTransferDisabledState = false
        let showPausePackDisableState = false
        const isCurrentMembership = PlayUtil.isCurrentPlayMembership(membership)

        if (isCurrentMembership) {
            const pausePackData = await PlayUtil.getPackPauseResumeDetails(membership, packInfo.productType, userContext)
            if (pausePackData && pausePackData.isPauseAllowed) {
                const packPauseResumeAction = PlayUtil.getPackPauseResumeAction(pausePackData, membership)
                if (packPauseResumeAction) {
                    actions.push(packPauseResumeAction)
                }
            } else if (pausePackData && pausePackData.isPauseAllowed === false && membership.maxPauseDuration !== 0) {
                showPausePackDisableState = true
            }

            const cancelPauseAction = await this.getCancelPauseAction(membership, packInfo.productType, userContext)
            if (cancelPauseAction) {
                actions.push(cancelPauseAction)
            }

            if (showPausePackDisableState && !isLimitedSessionPack) {
                actions.push(this.getPausePackDisableAction(pausePackData))
            }
        }

        if (selectPackDetails?.isSelectMembership
            && PlayUtil.isPlaySelectUpgradeSupported(userContext)
            && !PlayUtil.isUpcomingPause(membership)
            && membership.status !== "PAUSED"
            && !PlayUtil.isExpiredPlayMembership(membership)
        ) {
            actions.push(this.getUpgradeMembershipAction(membership, packInfo))
        }

        if (isSlpMembership
            && !isLimitedSessionPack
            && PlayUtil.isPlaySelectUpgradeSupported(userContext)
            && !PlayUtil.isUpcomingPause(membership)
            && membership.status !== "PAUSED"
            && !PlayUtil.isExpiredPlayMembership(membership)
        ) {
            actions.push(this.getUpgradeMembershipAction(membership, packInfo))
        }


        if (membership.maxPauseDuration !== 0 && isCurrentMembership) {
            actions.push(this.getStartDateDisableAction(membership))
        }

        const membershipStartDateNotEditable = (membership.status === "PURCHASED" && isMembershipCurrent(userContext, membership)) || membership.status === "PAUSED"
        if (!membershipStartDateNotEditable && membership.id && !PlayUtil.isExpiredPlayMembership(membership)) {
            actions.push(CultUtil.getChangeStartDateActionV2(membership, packInfo.productType))
        }

        return actions
    }

    private getUpgradeMembershipAction(membership: Membership, product: OfflineFitnessPack): Action {
        const upgradeTitle = "Upgrade to\ncultpass PLAY"
        const queryParams = {
            membershipId: membership.id,
            productType: product.productType,
            packId: product.productId,
            title: upgradeTitle,
            isMembershipServiceId: true,
        }
        return {
            iconUrl: "/image/icons/cult/upgrade.png",
            isEnabled: true,
            title: upgradeTitle,
            actionType: "NAVIGATION",
            url: getAppUrl(PageTypes.UpgradeMembership, queryParams)
        }
    }

    private async getCancelPauseAction(membership: Membership, productType: string, userContext: UserContext): Promise<Action | null> {
        const cancelPauseData = await this.buildUpcomingPauseWidget(membership, productType, userContext)
        if (cancelPauseData) {
            return {
                iconUrl: "/image/icons/cult/resume.png",
                title: "CANCEL PAUSE",
                actionType: cancelPauseData.manageOptions.options[0].type,
                meta: cancelPauseData.meta
            }
        }
        return null
    }

    private async buildUpcomingPauseWidget(membership: Membership, productType: string, userContext: UserContext): Promise<ManageOptionsWidget> {
        const tz = userContext.userProfile.timezone
        if (GymfitUtil.isUpcomingPause(membership)) {
            const isOptionEnabled = AppUtil.isCultPauseCancelSupported(userContext)
            const pauseStartDate = membership.activePause ? TimeUtil.parseDateFromEpochWithTimezone(tz, membership.activePause.start) : undefined
            const pauseEndDate = membership.activePause ? membership.activePause.end ? TimeUtil.parseDateFromEpochWithTimezone(tz, membership.activePause.end) : undefined : undefined
            const pauseEndDateFormatted = TimeUtil.formatDateInTimeZone(tz, pauseEndDate)
            const pauseStartDateFormatted = TimeUtil.formatDateInTimeZone(tz, pauseStartDate)
            let pauseEndDateWithTime
            if (pauseEndDate) {
                pauseEndDateWithTime = TimeUtil.getDefaultMomentForDateString(pauseEndDateFormatted, tz).startOf("day").subtract(1, "minute").toDate()
            }
            const startDateText = `${TimeUtil.formatDateInTimeZone(tz, pauseStartDate, "DD MMM hh:mm a")}`
            const daysDiff = TimeUtil.diffInDaysReal(tz, pauseStartDateFormatted, pauseEndDateFormatted)
            const remainingDaysInCurrentDuration = daysDiff < 0 ? 0 : daysDiff
            const limit = TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.end, "yyyy-MM-dd")
            const pauseDaysUsedInThisCycle = GymfitUtil.convertMillisToDays(membership.activePause.end - membership.activePause.start)
            const pauseDaysUsedTillDate = GymfitUtil.convertMillisToDays(membership.maxPauseDuration - membership.remainingPauseDuration)
            const meta: any = {
                membershipId: membership.id,
                packId: membership.productId,
                productType: productType,
                title: "Cancel Pause",
                pauseMaxDays: GymfitUtil.convertMillisToDays(membership.maxPauseDuration),
                remainingPauseDays: GymfitUtil.convertMillisToDays(membership.remainingPauseDuration),
                remainingDaysInCurrentDuration,
                pauseEndDate: pauseEndDate ? TimeUtil.formatDateInTimeZone(tz, pauseEndDate) : undefined,
                startDateParams: {
                    date: TimeUtil.todaysDate(tz),
                    limit,
                    canEdit: false,
                    pauseEndText: "Your pack is paused till"
                },
                action: {
                    primaryText: "YES",
                    secondaryText: "NO"
                },
                pauseInfoTitles: {
                    pauseUsed: "Pause days used",
                    membershipExtended: "Membership will be extended by",
                    membershipEndsOn: "Membership will now end on",
                    pauseLeft: "Pause days left"
                },
                pauseInfo: GymfitUtil.getPauseInfo(tz, pauseDaysUsedTillDate, pauseDaysUsedInThisCycle, limit, GymfitUtil.convertMillisToDays(membership.remainingPauseDuration), true),
                dateParam: {
                    date: TimeUtil.formatDateInTimeZone(tz, pauseStartDate, "YYYY-MM-DD hh:mm A"),
                    limit,
                    pauseEndDate: pauseEndDateWithTime ? TimeUtil.formatDateInTimeZone(tz, pauseEndDateWithTime, "DD MMM hh:mm a") : undefined,
                    pauseEndText: `Pause starting ${startDateText} till`
                },
                subTitle: `You are cancelling pause starting tonight at ${TimeUtil.formatDateInTimeZone(tz, pauseStartDate, "hh:mm a")} which ends on ${TimeUtil.formatDateInTimeZone(tz, pauseEndDateWithTime, "DD MMM hh:mm a")}. Your pause days remain intact`,
                refreshPageOnCompletion: true
            }
            const manageOptions: ManageOptions = {
                displayText: AppUtil.isCultPauseCancelSupported(userContext) ? "Cancel Pause" : "Pause Scheduled",
                icon: "PAUSE",
                options: [{
                    isEnabled: isOptionEnabled,
                    type: "CANCEL_CULT_PACK_PAUSE",
                    displayText: AppUtil.isCultPauseCancelSupported(userContext) ? "Cancel Pause" : "Pause Scheduled",
                    meta
                }],
                info: {
                    title: "About Pause",
                    subTitle: "You can pause your pack when you can't use it like during travel or illness.\n\nOnce you resume, your pack is extended by the number of days you were on pause"
                }
            }
            manageOptions.subTitle = pauseStartDate && pauseEndDateWithTime ? `Pause starting ${startDateText} till ${TimeUtil.formatDateInTimeZone(tz, pauseEndDateWithTime, "DD MMM hh:mm a")}` : ""
            const pauseWidget = new ManageOptionsWidget(manageOptions, meta)
            pauseWidget.isDisabled = !isOptionEnabled
            return pauseWidget
        }
    }

    private getPausePackDisableAction(pausePackData: any): Action {
        return {
            iconUrl: "/image/icons/cult/pause_black.png",
            title: "PAUSE",
            actionType: "SHOW_ALERT_MODAL",
            isDisabled: true,
            meta: {
                title: "Pack can not be paused",
                subTitle: pausePackData.manageOptions.subTitle,
                actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
            }
        }

    }

    private getStartDateDisableAction(membership: Membership): Action {
        return {
            iconUrl: "/image/icons/cult/calendar_black1.png",
            title: "START DATE",
            actionType: "SHOW_ALERT_MODAL",
            isDisabled: true,
            meta: {
                title: "Start date cannot be changed",
                subTitle: "Membership start date cannot be changed as membership already started",
                actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
            }
        }
    }

    getRightInfo(membership: Membership, userContext: UserContext, tz: Timezone) {
        if (membership.status === "PAUSED") {
            const pauseEndDate = membership.activePause ? TimeUtil.formatEpochInTimeZone(tz, membership.activePause.end) : undefined
            if (pauseEndDate) {
                const todaysDate = TimeUtil.todaysDate(tz)
                const diffDays = TimeUtil.diffInDays(userContext.userProfile.timezone, todaysDate, pauseEndDate)
                return diffDays === 0 ? `Resumes Tonight` : `Resumes in ${diffDays} days`
            }
        }
        return null
    }

    private async getPrePurchaseProductNote(packInfo: OfflineFitnessPack, offersV3Promise: Promise<CultProductPricesResponse>): Promise<ProductNote[] | undefined> {
        const offersV3Response = await offersV3Promise
        if (!offersV3Response) {
            return undefined
        }
        const offerItem = CultUtil.getOfferItem(offersV3Response, packInfo.productId)
        if (!offerItem) {
            return undefined
        }
        const offersMap = OfferUtil.segregateNoCostEMIOffers(offerItem.offers)
        const emiOffers = offersMap.get("NO_COST_EMI")
        if (!emiOffers.length) {
            return undefined
        }

        let maxEmiTenure = 12
        emiOffers.forEach((emiOffer) => {
            emiOffer.addons.forEach((addOn) => {
                // Max supported emi duration in months
                if (addOn.config && addOn.config.maxEmiTenure) {
                    maxEmiTenure = addOn.config.maxEmiTenure
                }
            })
        })
        const banks = emiOffers[0].description
        const numberOfMonths = CultUtil.getEmiTenureForPack(packInfo.product.durationInDays, maxEmiTenure)
        const result = CultUtil.getPackPriceAndOfferIdV2(packInfo, offersV3Response)
        return [{
            title: {
                plainText: "No cost EMI from ",
                boldText: `${Math.round(result.price.listingPrice / numberOfMonths)}/month*`
            },
            description: _.isEmpty(banks) ? undefined : banks,
            icon: "/image/icons/cult/emi-v1.png",
            action: {
                actionType: "NAVIGATION",
                url: `curefit://nocostemipage?packId=${packInfo.productId}&productType=${packInfo.productType}`,
                title: "DETAILS"
            }
        }]
    }

    async buildProductNoteWidget(data: ProductNote[]): Promise<ProductNoteWidget> {
        return {
            widgetType: "PRODUCT_NOTE_WIDGET",
            data: data,
            header: {
                title: "No Cost EMI"
            }
        }
    }

    private async getMiniumAndMaximumStartDate(
        centerResponsePromise: Promise<CenterResponse>,
        earliestStartDatePlay: any,
        packDuration: number
    ) {
        let centerInfo: CenterResponse
        let centerLaunchDate = null
        centerInfo = await centerResponsePromise
        if (centerInfo?.launchDate) {
            const centerLaunchDateMs = centerInfo.launchDate
            centerLaunchDate = centerLaunchDateMs ? moment(centerLaunchDateMs).startOf("day").valueOf() : null
        }

        let earliestStartDate = earliestStartDatePlay.start // moment(earliestStartDatePlay.start).add(1, "days").startOf("day").valueOf()
        if (centerLaunchDate) {
            earliestStartDate = Math.max(earliestStartDate, centerLaunchDate)
        }

        let centerEndDate = null
        if (centerInfo?.terminationDate) {
            const centerEndDateMs = centerInfo.terminationDate
            if (centerEndDateMs) {
                const packEndDateMs = centerInfo.terminationDate  - (packDuration * 1000)
                centerEndDate = moment(packEndDateMs).startOf("day").valueOf()
            }
        }

        const minStartDate = moment(earliestStartDate).format("YYYY-MM-DD")
        const maxStartDate = centerEndDate ? moment(centerEndDate).format("YYYY-MM-DD") : null

        return {
            minStartDate,
            maxStartDate
        }
    }

    public async getPlayStartDateResponse(membership: Membership, userContext: UserContext, tz: Timezone, membershipId: number, productType: string) {
        const membershipInitialStartDate = moment(membership.start).format("YYYY-MM-DD")

        let startDateResponse: IPackStartDateResponse
        if (productType === "PLAY") {
            startDateResponse = await this.calculatePackStartDate(membership, userContext, tz, membershipInitialStartDate)
        } else {
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).build()
        }
        const {membershipEndStartDate, startDate, dateSelectionWindow, productTitle} = startDateResponse

        await this.logger.info("Start Date Model : " + JSON.stringify(startDateResponse))
        const subtitle = `You can start your membership earlier or later, but new start date cannot be beyond ${dateSelectionWindow} days from original start date.`
        const infoWidget: InfoWidget = {
            title: "Change start date",
            subTitle: subtitle,
            widgetType: "INFO_WIDGET",
            icon: null
        }

        const datePickerWidget: DatePickerWidget = {
            widgetType: "DATE_PICKER_WIDGET",
            startDate: startDate,
            canChangeStartDate: true,
            endDate: null,
            selectedDate: null
        }
        const action = {
            actionType: "SHOW_ALERT_MODAL",
            title: "Submit",
            meta: {
                title: "Change start date",
                packName: productTitle,
                actions: [
                    {
                        actionType: "CONFIRM_START_DATE_CHANGE",
                        title: "CONFIRM",
                        payload: {
                            membershipId,
                        }
                    }
                ]
            }
        }
        return {
            validation: {
                endDate: TimeUtil.addDays(tz, moment(membershipInitialStartDate).format("YYYY-MM-DD"), dateSelectionWindow),
                errorMessage: `New start date should be on or before ${TimeUtil.addDays(tz, membershipInitialStartDate, dateSelectionWindow)}`
            },
            widgets: [
                infoWidget,
                datePickerWidget
            ],
            actions: [
                action
            ]
        }
    }

    private async calculatePackStartDate(membership: Membership, userContext: UserContext, tz: Timezone, membershipInitialStartDate: string): Promise<IPackStartDateResponse> {
        const playProduct: FitnessPack = await this.catalogueService.getPlayPack(membership.productId)
        const earliestStartDatePlay = await this.membershipService.getEarliestStartDate(userContext?.userProfile?.userId, Tenant.CUREFIT_APP, PLAY_MEMBERSHIP_PRIMARY_BENEFITS, playProduct.numDays * 86400)
        const earliestStartDate = moment(earliestStartDatePlay.start).add(0, "days").startOf("day").valueOf()
        const endDate = TimeUtil.addDays(tz, moment(membership.end).format("YYYY-MM-DD"),  1)
        const previousMembershipEndDate = moment(earliestStartDate).format("YYYY-MM-DD")
        this.logger.info("Play earliestStartDate : " + earliestStartDate)
        this.logger.info("Play endDate : " + endDate)
        this.logger.info("Play previousMembershipEndDate : " + previousMembershipEndDate)
        let startDate
        if (previousMembershipEndDate !== endDate) {
            startDate = previousMembershipEndDate
            this.logger.info("Start date if case : " + startDate)
        } else {
            const currentTime = Date.now()
            const laterDate = membership.start + 30 * 86400
            let calculatedStartDate = moment(Date.now())
            const endingMemberships = await this.membershipService.getMembershipsForUser(userContext?.userProfile?.userId, Tenant.CUREFIT_APP, ["PLAY"], ["PURCHASED", "PAUSED"], currentTime, laterDate)
            endingMemberships.forEach(msp => {
                if (msp.id !== membership.id && moment(msp.end) >= calculatedStartDate) {
                    calculatedStartDate = moment(msp.end).add(0, "days").startOf("day")
                }
            })
            startDate = moment(calculatedStartDate).format("YYYY-MM-DD")
            this.logger.info("Start date else case : " + startDate)
        }
        const dateSelectionWindow = DATE_SELECTION_WINDOW
        let membershipEndStartDate = TimeUtil.addDays(tz, moment(membershipInitialStartDate).format("YYYY-MM-DD"), dateSelectionWindow)
        if (startDate > membershipEndStartDate) {
            this.logger.info("Start date greater than membershipEndStartDate")
            membershipEndStartDate = startDate
        }
        this.logger.info("Final Start date  : " + startDate)
        this.logger.info("Final End date  : " + membershipEndStartDate)
        this.logger.info("Final Data  : " + dateSelectionWindow + "," + playProduct.title)

        return {
            membershipEndStartDate,
            startDate,
            dateSelectionWindow,
            productTitle: playProduct.title
        }
    }

}

export default PlayPackDetailViewBuilder
