import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>er, ProductDetailPage, ProductListWidget } from "../common/views/WidgetView"
import { UrlPathBuilder } from "@curefit/product-common"
import { pad, pluralizeStringIfRequired } from "@curefit/util-common"
import AtlasUtil, { DiyContentDetail, DiyContentMeta } from "../util/AtlasUtil"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { CFLiveProduct, DIYPack, DIYProduct, DIYUserFitnessPack, DIYUserMeditationPack, DIYSeries } from "@curefit/diy-common"
import { ActionUtil } from "@curefit/base-utils"
import AppUtil from "../util/AppUtil"
import { UserContext } from "@curefit/userinfo-common"
import * as _ from "lodash"
import CultUtil from "../util/CultUtil"
import { Action, ActionType } from "@curefit/apps-common"
import { inject } from "inversify"
import { BASE_TYPES, Logger } from "@curefit/base"
import LiveUtil from "../util/LiveUtil"

export interface DiySeriesActionCardTV {
    title: string,
    subTitle: string,
    description: string,
    image: string,
    action: Action,
    packProgress?: DIYPackProgress
}

class SeriesDetailViewTV extends ProductDetailPage {
    breadCrumbs?: {
        title: string,
        link?: string
    }[]
    constructor(logger: Logger, userAgent: UserAgent, isUserEligibeForMonetisation: boolean, diySeries: DIYSeries, packs: (DIYUserFitnessPack | DIYUserMeditationPack)[], durationMap: Map<string, number>, nextSessionMap: Map<string, DIYProduct>, blockInternationalUser: boolean, userContext?: UserContext, isInternalUser?: Boolean, isUserEligibleForLivePackTrial?: boolean) {
        super()
        this.widgets.push(this.getSessionsWidget(logger, isUserEligibeForMonetisation, diySeries, packs, durationMap, nextSessionMap, blockInternationalUser, userContext, isInternalUser, isUserEligibleForLivePackTrial))
        this.breadCrumbs = this.getBreadCrumbs(diySeries)
    }

    private getSessionsWidget(logger: Logger, isUserEligibleForMonetisation: boolean, diySeries: DIYSeries, packs: (DIYUserFitnessPack | DIYUserMeditationPack)[], durationMap: Map<string, number>, nextSessionMap: Map<string, DIYProduct>, blockInternationalUser: boolean, userContext?: UserContext, isInternalUser?: Boolean, isUserEligibleForLivePackTrial?: boolean): ProductListWidget {
        logger.debug("**********  session widget")
        const header: Header = {
            title: AppUtil.isWeb(userContext) ? diySeries.name : "About this series",
            subTitle: diySeries.description,
            image: diySeries.image
        }
        const productIds = diySeries.packIds
        const actionCards: DiySeriesActionCardTV[] = []
        for (let i = 0; i < productIds.length; i++) {
            const productId = productIds[i]
            const session = nextSessionMap.get(productId)
            const packDetail = packs[i]
            const packIntroAction = this.getSessionPlayAction(session, packDetail.pack)
            actionCards.push({
                title: `${packDetail.pack.title}`,
                subTitle: `${packDetail.pack.sessionIds.length} ${pluralizeStringIfRequired("session", packDetail.pack.sessionIds.length)}`,
                description: packDetail.pack.description,
                image: UrlPathBuilder.prefixSlash(packDetail.pack.imageDetails.heroImage),
                action: {
                    actionType: "NAVIGATION",
                    url: AtlasUtil.getDIYPackDetailsTVUrl(packDetail.pack.categoryId, productId)
                },
                packProgress: {
                    total: packDetail.pack.sessionIds.length,
                    completed: (packDetail.fulfilment && packDetail.fulfilment.completedProductIds) ? packDetail.fulfilment.completedProductIds.length : 0,
                    state: packDetail.fulfilment && packDetail.fulfilment.status,
                    type: packDetail.pack.productType === "DIY_FITNESS_PACK" ? "DIY_FITNESS" : "DIY_MEDITATION",
                    noPadding: true,
                    resumeAction: LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, session.locked, packIntroAction, isUserEligibleForLivePackTrial, session.productType, "diy_series_detail_widget", "", blockInternationalUser),
                    resumeNewAction: LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, session.locked, packIntroAction, isUserEligibleForLivePackTrial, session.productType, "diy_series_detail_widget", "", blockInternationalUser)
                }
            })
        }
        const productListWidget: ProductListWidget = new ProductListWidget("MAGAZINE_OFFER", header, actionCards)
        productListWidget.hideSepratorLines = true
        return productListWidget
    }

    private getSessionPlayAction(session: DIYProduct, pack: DIYPack) {
        return {
            actionType: "PLAY_VIDEO",
            meta: {
                content: AtlasUtil.getContentDetailV2(session),
                queryParams: AtlasUtil.getContentMetaV2(session, pack),
                title: session.title,
                checkDownloadStatus: true
            }
        }
    }
    private getBreadCrumbs(diySeries: DIYSeries) {
        const subUrl = diySeries.productType === "DIY_FITNESS" ? "cult" : "mind"
        return [
            { title: "Home", pathname: "/" },
            { title: "live", pathname: `/${subUrl}/home` },
            { title: diySeries.name, pathname: diySeries.name }
          ]
    }
}

export default SeriesDetailViewTV
