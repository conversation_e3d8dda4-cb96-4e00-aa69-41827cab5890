import { CustomerIssueType } from "@curefit/issue-common"
import {
    AccessLevel,
    CultBooking,
    CultCenter,
    CultMembership,
    CultPack,
    CultPackType,
    CultWorkoutV2,
} from "@curefit/cult-common"
import { MindPack } from "@curefit/mind-common"
import { ProductType, UrlPathBuilder } from "@curefit/product-common"
import { ICultServiceOld as ICultService, MembershipDetail } from "@curefit/cult-client"
import {
    Action,
    ActionCard,
    CenterSelectionWidget,
    DatePickerWidget,
    DescriptionWidget,
    getOffersWidget,
    Header,
    InfoCard,
    ManageOptions,
    ManageOptionsWidget,
    NoCostEmiWidget,
    OgmPackOfferWidget,
    PauseInfo,
    PickerWidget,
    ProductDetailPage,
    ProductGridWidget,
    ProductListWidget,
    SelectCenterAction,
    WidgetView
} from "../common/views/WidgetView"
import * as momentTz from "moment-timezone"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { CatalogueServiceUtilities } from "../util/CatalogueServiceUtilities"
import { CATALOG_CLIENT_TYPES, CatalogueServiceV2Utilities, ICatalogueService, ICatalogueServicePMS } from "@curefit/catalog-client"
import CultPackPageConfig from "./CultPackPageConfig"
import { inject, injectable } from "inversify"
import IProductBusiness from "../product/IProductBusiness"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import * as _ from "lodash"
import CultUtil, {
    CULT_NAS_PRICE_HIKE_BANNER_ID,
    CultPackProgressV2,
    ELITE_MEMBERSHIP_PRIMARY_BENEFITS,
    MIND_NAS_PRICE_HIKE_BANNER_ID
} from "../util/CultUtil"
import { ActionUtil, OfferUtil, SeoUrlParams } from "@curefit/base-utils"
import { ActionUtil as AppActionUtil } from "../util/ActionUtil"
import { CultProductPricesResponse, OfferV2 } from "@curefit/offer-common"
import {
    IOfferServiceV2,
    OFFER_SERVICE_CLIENT_TYPES,
    OfferServiceV3,
    OfferUtil as OfferUtilService,
    PackOffersResponse
} from "@curefit/offer-service-client"
import { UserContext } from "@curefit/userinfo-common"
import AppUtil from "../util/AppUtil"
import ICRMIssueService from "../crm/ICRMIssueService"
import {
    CultPackSummaryWidget,
    ICultPackSummaryWidgetBuilderParams,
    ICultPackSummaryWidgetBuilderParamsV2
} from "../common/views/CultPackSummaryWidget"
import { CultOgmPackOfferWidget } from "../common/views/CultOgmPackOfferWidget"
import { CacheHelper } from "../util/CacheHelper"
import { ICityService, ICountryService, LOCATION_TYPES } from "@curefit/location-mongo"
import { ICultBusiness, MoneyBackOfferDetail } from "../cult/CultBusiness"
import { Tenant, User } from "@curefit/user-common"
import { City } from "@curefit/location-common"
import IssueBusiness, { IssueDetailView } from "../crm/IssueBusiness"
import { EmiInterestReadonlyDaoMongoImpl, PAYMENT_MODELS_TYPES } from "@curefit/payment-models"
import WidgetBuilder from "../page/vm/WidgetBuilder"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { IBaseWidget } from "@curefit/vm-models"
import { CountryData } from "../user/UserController"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import { Membership } from "@curefit/membership-commons"
import GymfitUtil, { GYM_MEMBERSHIP_PRIMARY_BENEFITS } from "../util/GymfitUtil"
import { CENTER_SERVICE_CLIENT_TYPES, ICenterService } from "@curefit/center-service-client"
import { MembershipTransferable } from "../cult/cultpackpage/CultPackCommonViewBuilder"
import { BASE_TYPES, Logger } from "@curefit/base"
import CartViewBuilder from "../cart/CartViewBuilder"
import { PackStartDateAnalyticsEvent } from "../cfAnalytics/PackStartDateAnalyticsEvent"
import { AnalyticsEventName } from "../cfAnalytics/AnalyticsEventName"
import { ICFAnalytics } from "../cfAnalytics/CFAnalytics"
import { MembershipItemUtil } from "../util/MembershipItemUtil"
import PlayUtil from "../util/PlayUtil"
import { CenterResponse } from "@curefit/center-service-common"
import { SubAccessLevel } from "@curefit/gymfit-common"
import FitnessUtil from "../util/FitnessUtil"
import moment = require("moment-timezone")
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"
import { ListItem, ListWidgetV2 } from "@curefit/apps-common"
import { AugmentedOfflineFitnessPack } from "@curefit/pack-management-service-common"

const clone = require("clone")

export interface CultPackPageRequestParams {
    baseService: ICultService
    productType: ProductType
    productId: string
    packInfo: AugmentedOfflineFitnessPack
    membershipDetail: MembershipDetail
    canChangeCenter: boolean
    selectedStartDate?: string,
    selectedSubUserId?: string,
    selectedCenterId?: string
    subUserRelations: Array<{
        subUserId?: string,
        relation?: string
    }>
}

export interface CultPackPageRequestParamsV2 {
    baseService: ICultService
    productType: ProductType
    centerServiceId: number
    productId: string
    packInfo: OfflineFitnessPack
    membershipDetail: MembershipDetail
    canChangeCenter: boolean
    selectedStartDate?: string,
    selectedSubUserId?: string,
    selectedCenterId?: string
    subUserRelations: Array<{
        subUserId?: string,
        relation?: string
    }>

}

interface CultPackPageBuilderParamsV2 {
    isBuyFlow?: boolean
    workoutsPromise: Promise<CultWorkoutV2[]>
    offersPromise?: Promise<PackOffersResponse>
    offersV3Promise?: Promise<CultProductPricesResponse>
    bookingsPromise?: Promise<CultBooking[]>
    issuesMapPromise?: Promise<Map<string, CustomerIssueType[]>>
    startDateOptions?: CultPackStartDateOptions
    packInfo: AugmentedOfflineFitnessPack
    derivedCity: DerivedCityDetails
    preferredCenterId: string
    // preferredCenter: CultCenter
    cultPackProgress: CultPackProgressV2
    preAdvancePaid: boolean
    membership?: Membership
    allSubUsers: Array<User>,
    selectedSubUserDetails?: User,
    moneyBackOfferPromise?: Promise<MoneyBackOfferDetail>
    userContext: UserContext
    colocatedCenterIDs?: string[]
    membershipTransferable?: MembershipTransferable
    preferredCenter?: CultCenter | CenterResponse
}

interface CultPackPageBuilderParams {
    isBuyFlow?: boolean
    workoutsPromise: Promise<CultWorkoutV2[]>
    offersPromise?: Promise<PackOffersResponse>
    offersV3Promise?: Promise<CultProductPricesResponse>
    bookingsPromise?: Promise<CultBooking[]>
    issuesMapPromise?: Promise<Map<string, CustomerIssueType[]>>
    startDateOptions?: CultPackStartDateOptions
    packInfo: AugmentedOfflineFitnessPack
    derivedCity: DerivedCityDetails
    preferredCenterId: string
    centerServicePreferredCenterId?: string
    preferredCenter: CultCenter | CenterResponse
    cultPackProgress: CultPackProgressV2
    preAdvancePaid: boolean
    membership?: Membership
    allSubUsers: Array<User>,
    selectedSubUserDetails?: User,
    moneyBackOfferPromise?: Promise<MoneyBackOfferDetail>
    userContext: UserContext
    colocatedCenterIDs?: string[]
    isPreRegPack: boolean
    membershipTransferable?: MembershipTransferable
}

interface PausePackBuilderParams {
    membership: CultMembership
    userContext: UserContext
}

export interface DerivedCityDetails {
    city?: City
    cultCityId: number
    cityId: string
    defaultCultCenter: string
    defaultMindCenter: string
}

export interface CultPackStartDateOptions {
    minEligibleDate: string
    maxEligibleDate: string
    selectedDate: string
    canChangeStartDate: boolean
    calloutText?: string
}

@injectable()
abstract class CultPackDetailViewBuilder {
    constructor(@inject(CUREFIT_API_TYPES.ProductBusiness) protected productBusiness: IProductBusiness,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) protected catalogueService: ICatalogueService,
        @inject(LOCATION_TYPES.CityService) protected cityService: ICityService,
        @inject(CUREFIT_API_TYPES.CRMIssueService) protected CRMIssueService: ICRMIssueService,
        @inject(CUREFIT_API_TYPES.IssueBusiness) protected issueBusiness: IssueBusiness,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) protected offerServiceV2: IOfferServiceV2,
        @inject(CUREFIT_API_TYPES.CultPackPageConfig) protected cultPackPageConfig: CultPackPageConfig,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
        @inject(CUREFIT_API_TYPES.CultBusiness) private cultBusiness: ICultBusiness,
        @inject(PAYMENT_MODELS_TYPES.EmiInterestReadonlyDao) private paymentDao: EmiInterestReadonlyDaoMongoImpl,
        @inject(CUREFIT_API_TYPES.WidgetBuilder) public widgetBuilder: WidgetBuilder,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) protected offerServiceV3: OfferServiceV3,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) protected serviceInterfaces: CFServiceInterfaces,
        @inject(LOCATION_TYPES.CountryService) private countryService: ICountryService,
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) private membershipService: IMembershipService,
        @inject(CENTER_SERVICE_CLIENT_TYPES.CenterService) protected centerService: ICenterService,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferUtil) protected offerUtil: OfferUtilService,
        @inject(BASE_TYPES.ILogger) protected logger: Logger,
        @inject(CUREFIT_API_TYPES.CartViewBuilder) private cartViewBuilder: CartViewBuilder,
        @inject(CUREFIT_API_TYPES.CFAnalytics) private cfAnalytics: ICFAnalytics,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReaderPMS) private catalogueServicePMS: ICatalogueServicePMS

    ) {

    }

    abstract getDefaultCenterId(derivedCity: DerivedCityDetails): string
    abstract getPreferredCenterId(userContext: UserContext, derivedCity: DerivedCityDetails): string
    abstract getCenterServicePreferredCenterId(userContext: UserContext): string
    // abstract getOffersPromise(userContext: UserContext, packId: string, preferredCenterId: string): Promise<PackOffersResponse>
    abstract getOffersV3Promise(userContext: UserContext, productId: string, preferredCenterId: string): Promise<CultProductPricesResponse>
    // abstract getOffersV3PromiseV2(userContext: UserContext, productId: string, preferredCenterId: string): Promise<CultProductPricesResponse>
    abstract isSourceMembership(membership: CultMembership): boolean
    abstract getBiometricHeader(): string

    async getViewV2(userContext: UserContext, requestParams: CultPackPageRequestParamsV2): Promise<ProductDetailPage> {
        let membership: Membership
        let userIdForMemberships
        let membershipTransferable
        const isPlayWeb = PlayUtil.isPlayWebRequest(userContext, requestParams.packInfo?.productType)
        if (requestParams.membershipDetail) {
            membership = requestParams.membershipDetail.membership
            membershipTransferable = requestParams.membershipDetail.membershipTransferable
            userIdForMemberships = membership.userId
        } else {
            userIdForMemberships = userContext.userProfile.userId
        }
        let cultPackProgress
        if (userIdForMemberships) {
            // const activeMembershipPromise = requestParams.baseService.activeMembership(userIdForMemberships, false)
            const activeMemberships = await this.membershipService.getCachedMembershipsForUser(userIdForMemberships, AppUtil.getTenantFromUserContext(userContext), ["CULT", "PLAY"], ["PAUSED", "PURCHASED"])

            cultPackProgress = CultUtil.cultPackProgressV2(userContext, activeMemberships, membership)
        }
        const derivedCityDetailsPromise = this.getDerivedCityDetails(userContext, userContext.userProfile.userId, cultPackProgress)

        const packInfo = requestParams.packInfo
        const derivedCity = await derivedCityDetailsPromise
        const defaultCenterId = this.getDefaultCenterId(derivedCity)
        let preferredCenterId = this.getPreferredCenterId(userContext, derivedCity)
        if (isPlayWeb) {
            preferredCenterId = membership?.metadata?.centerServiceCenterId ?? membership?.metadata?.centerId
        }
        let fitnessWorkoutPromise, preferredCenter

        if (isPlayWeb && preferredCenterId) {
            preferredCenter = await this.centerService.getCenterDetails(Number(preferredCenterId), true)
            let workoutIds
            // Sport level filtering of workouts in a center in SLP
            const playPack = await this.catalogueService.getPlayPack(packInfo.productId) // TODO: Migrate to PMS construct
            if (PlayUtil.isSLPPack(playPack)) {
                workoutIds = playPack.subLevelAccessListings.filter(i => i?.subAccessLevel === SubAccessLevel.ACTIVITY)?.map(i => Number(i?.subAccessLevelId))
            } else {
                workoutIds = preferredCenter?.activities?.filter(i => i?.category === "PLAY" && i?.status === "ACTIVE")?.map(i => i?.id)
            }
            fitnessWorkoutPromise = requestParams.baseService.browseWorkoutsV2ByIDs(workoutIds, "CUREFIT_API", undefined, "")
        } else {
            fitnessWorkoutPromise = requestParams.baseService.browseWorkoutsV2(undefined, false, true, true, "CUREFIT_API", userContext.userProfile.userId, derivedCity.cultCityId)

        }
        // const preferredCenterPromise = this.getPreferredCenter(requestParams, membership, packInfo, defaultCenterId, preferredCenterId)
        let selectSubUserPromise, selectedSubUserDetails
        const allSubUsersPromise = Array.isArray(requestParams.subUserRelations) && requestParams.subUserRelations.map(async (subUsers) => {
            const userPromise = this.userCache.getUser(subUsers.subUserId)
            if (requestParams.selectedSubUserId === subUsers.subUserId) {
                selectSubUserPromise = userPromise
            }
            return await userPromise
        })

        if (selectSubUserPromise) {
            selectedSubUserDetails = await selectSubUserPromise
        }

        const allSubUsers = allSubUsersPromise && await Promise.all(allSubUsersPromise) || []

        // requestParams.subUserRelations && requestParams.subUserRelations.map(subUser)
        const builderParams: CultPackPageBuilderParamsV2 = {
            workoutsPromise: fitnessWorkoutPromise,
            packInfo: packInfo,
            derivedCity: derivedCity,
            preferredCenterId: preferredCenterId,
            // preferredCenter: await preferredCenterPromise,
            cultPackProgress: cultPackProgress,
            preAdvancePaid: false,
            membership: membership,
            allSubUsers: allSubUsers,
            selectedSubUserDetails: selectedSubUserDetails,
            userContext: userContext,
            membershipTransferable: membershipTransferable
        }
        builderParams.isBuyFlow = !membership
        if (builderParams.isBuyFlow) {

            return this.getPrePurchasePageV2(userContext, requestParams, builderParams)
        } else {
            if (isPlayWeb) {
                builderParams.preferredCenter = preferredCenter
            }
            return this.getPostPurchasePageV2(userContext, requestParams, builderParams)
        }
    }


    async getView(userContext: UserContext, requestParams: CultPackPageRequestParams): Promise<ProductDetailPage> {
        let membership: Membership
        let userIdForMemberships
        let membershipTransferable
        if (requestParams.membershipDetail) {
            membership = requestParams.membershipDetail.membership
            membershipTransferable = requestParams.membershipDetail.membershipTransferable
            userIdForMemberships = membership.userId
        } else {
            userIdForMemberships = userContext.userProfile.userId
        }
        let cultPackProgress
        if (userIdForMemberships) {
            // const activeMembershipPromise = requestParams.baseService.activeMembership(userIdForMemberships, false)
            const activeMemberships = await this.membershipService.getCachedMembershipsForUser(userIdForMemberships, AppUtil.getTenantFromUserContext(userContext), ["CULT", "PLAY"], ["PAUSED", "PURCHASED"])
            cultPackProgress = CultUtil.cultPackProgressV2(userContext, activeMemberships, membership)
        }
        const derivedCityDetailsPromise = this.getDerivedCityDetails(userContext, userContext.userProfile.userId, cultPackProgress)
        const packInfo = requestParams.packInfo
        const isPlayWeb = PlayUtil.isPlayWebRequest(userContext, requestParams?.productType)
        const derivedCity = await derivedCityDetailsPromise
        const defaultCenterId = this.getDefaultCenterId(derivedCity)
        let preferredCenterId = this.getPreferredCenterId(userContext, derivedCity)
        const centerServicePreferredCenterId = this.getCenterServicePreferredCenterId(userContext)
        let fitnessWorkoutPromise, preferredCenterPromise, preferredCenter

        if (isPlayWeb) {
            preferredCenterId = membership?.metadata?.centerServiceCenterId ?? membership?.metadata?.centerId
            preferredCenter = await this.centerService.getCenterDetails(Number(preferredCenterId), true)
            const workoutIds = PlayUtil.getWorkoutIdsByCityId(packInfo?.clientMetadata.cityId)
            fitnessWorkoutPromise = requestParams.baseService.browseWorkoutsV2ByIDs(workoutIds, "CUREFIT_API", undefined, "")
        } else {
            fitnessWorkoutPromise = requestParams.baseService.browseWorkoutsV2(undefined, false, true, true, "CUREFIT_API", userContext.userProfile.userId, derivedCity.cultCityId)
        }

        if (!isPlayWeb) {
            preferredCenterPromise = this.getPreferredCenter(requestParams, membership, defaultCenterId, preferredCenterId)
        }
        let selectSubUserPromise, selectedSubUserDetails
        const allSubUsersPromise = Array.isArray(requestParams.subUserRelations) && requestParams.subUserRelations.map(async (subUsers) => {
            const userPromise = this.userCache.getUser(subUsers.subUserId)
            if (requestParams.selectedSubUserId === subUsers.subUserId) {
                selectSubUserPromise = userPromise
            }
            return await userPromise
        })
        if (selectSubUserPromise) {
            selectedSubUserDetails = await selectSubUserPromise
        }
        const allSubUsers = allSubUsersPromise && await Promise.all(allSubUsersPromise) || []
        const builderParams: CultPackPageBuilderParams = {
            workoutsPromise: fitnessWorkoutPromise,
            packInfo: packInfo,
            derivedCity: derivedCity,
            preferredCenterId: preferredCenterId,
            centerServicePreferredCenterId: centerServicePreferredCenterId,
            preferredCenter: isPlayWeb ? preferredCenter : await preferredCenterPromise,
            cultPackProgress: cultPackProgress,
            preAdvancePaid: false,
            membership: membership,
            allSubUsers: allSubUsers,
            selectedSubUserDetails: selectedSubUserDetails,
            userContext: userContext,
            isPreRegPack: false,
            membershipTransferable: membershipTransferable
        }
        builderParams.isBuyFlow = !membership
        // here check
        if (builderParams.isBuyFlow) {
            return this.getPrePurchasePage(userContext, requestParams, builderParams)
        } else {
            return this.getPostPurchasePage(userContext, requestParams, builderParams)
        }
    }


    async getPrePurchasePageV2(userContext: UserContext, requestParams: CultPackPageRequestParamsV2, builderParams: CultPackPageBuilderParamsV2): Promise<ProductDetailPage> {
        const isWeb = AppUtil.isWeb(userContext)

        const widgetPromises: Promise<WidgetView | IBaseWidget>[] = []
        const productId = requestParams.packInfo.productId
        const offerCenterId = builderParams.preferredCenterId ?? requestParams.centerServiceId ? String(requestParams.centerServiceId) : undefined
        builderParams.offersPromise = undefined
        builderParams.offersV3Promise = this.getOffersV3Promise(userContext, productId, offerCenterId)
        builderParams.startDateOptions = await this.getStartDateOptionsV2(userContext, requestParams, builderParams)
        builderParams.colocatedCenterIDs = []


        if (!_.isEmpty(builderParams.colocatedCenterIDs)) {
            builderParams.colocatedCenterIDs = [...builderParams.colocatedCenterIDs, builderParams.preferredCenterId]
        }


        widgetPromises.push(this.buildSummaryWidgetV2(userContext, requestParams, builderParams))


        widgetPromises.push(this.buildPriceHikeBannerWidget(userContext, requestParams))


        widgetPromises.push(this.buildStartDateWidget(userContext, requestParams, builderParams))


        widgetPromises.push(this.buildOffersWidget(userContext, requestParams, builderParams))



        widgetPromises.push(this.buildNoShowPolicyWidget(builderParams))


        widgetPromises.push(this.cartViewBuilder.addEliteWhatYouGet(requestParams.packInfo, isWeb, false, userContext))

        widgetPromises.push(this.cartViewBuilder.addEliteHowItWorks(requestParams.packInfo, isWeb))



        const cultPackPrePurchasePage = new ProductDetailPage()


        cultPackPrePurchasePage.widgets = await Promise.all(widgetPromises)


        cultPackPrePurchasePage.widgets = _.filter(cultPackPrePurchasePage.widgets, widget => !_.isEmpty(widget))


        cultPackPrePurchasePage.actions = this.getPrePurchasePageActionsV2(userContext, requestParams, builderParams)



        const issues: IssueDetailView[] = await this.issueBusiness.getCultOrMindSubscriptionPrePurchaseIssues(builderParams.packInfo, userContext)


        const accessLevelCenter = CatalogueServiceUtilities.getAccessLevel(requestParams.packInfo) === AccessLevel.CENTER
        this.logger.info("getPrePurchasePageV2 " + accessLevelCenter + " " + builderParams.packInfo.product.durationInDays)

        const analyticsData = {
            // "packId": CatalogueServiceUtilities.extractPackId(builderParams.packInfo.productId),
            "title": accessLevelCenter ? builderParams.packInfo.title + " " + builderParams.packInfo.clientMetadata?.centerName : builderParams.packInfo.title,
            "productId": builderParams.packInfo.productId,
        }
        cultPackPrePurchasePage.navigationAction = {
            title: "HELP",
            action: {
                actionType: "REPORT_ISSUE",
                meta: {
                    title: "Help",
                    issues: issues
                }
            },
            textStyle: {},
            containerStyle: {},
            analyticsData: analyticsData
        }
        return cultPackPrePurchasePage
    }

    async getPrePurchasePage(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams): Promise<ProductDetailPage> {
        const widgetPromises: Promise<WidgetView | IBaseWidget>[] = []
        builderParams.offersPromise = undefined
        builderParams.offersV3Promise = this.getOffersV3Promise(userContext, requestParams.productId, builderParams.preferredCenterId)
        builderParams.startDateOptions = await this.getStartDateOptions(userContext, requestParams, builderParams)
        builderParams.colocatedCenterIDs = [] // Deprecated
        widgetPromises.push(this.buildSummaryWidget(userContext, requestParams, builderParams))
        widgetPromises.push(this.buildPriceHikeBannerWidget(userContext, requestParams))
        widgetPromises.push(this.buildCenterSelectionWidget(userContext, requestParams, builderParams))
        widgetPromises.push(this.buildStartDateWidget(userContext, requestParams, builderParams))
        // widgetPromises.push(this.buildOGMWidget(userContext, requestParams, builderParams))
        // widgetPromises.push(this.buildBuddyWidget(userContext, requestParams, builderParams))
        widgetPromises.push(this.buildOffersWidget(userContext, requestParams, builderParams))
        if (AppUtil.isNewPackOrderConformationSupported(userContext, (await userContext.userPromise).isInternalUser)) {
            widgetPromises.push(this.buildNoCostEmiOfferWidget(builderParams, requestParams.productId, requestParams.productType))
        }
        widgetPromises.push(this.buildPackDescriptionWidget(userContext, requestParams, builderParams))
        widgetPromises.push(this.buildColocatedCentersWidget(userContext, requestParams, builderParams))
        // widgetPromises.push(this.buildComplementaryPackWidget(userContext, requestParams, builderParams))
        widgetPromises.push(this.buildNoShowPolicyWidget(builderParams))
        // if (builderParams.packInfo.isRestrictedToCenter || builderParams.isPreRegPack) {
        //     widgetPromises.push(this.buildWorkoutsWidget(userContext, requestParams, builderParams))
        //     widgetPromises.push(this.buildHowItWorksWidget(userContext, requestParams, builderParams))
        // } else {
        widgetPromises.push(this.buildHowItWorksWidget(userContext, requestParams, builderParams))
        widgetPromises.push(this.buildWorkoutsWidget(userContext, requestParams, builderParams))
        // }
        widgetPromises.push(this.buildBiometricInfoWidget(builderParams))
        const cultPackPrePurchasePage = new ProductDetailPage()
        cultPackPrePurchasePage.widgets = await Promise.all(widgetPromises)
        cultPackPrePurchasePage.widgets = _.filter(cultPackPrePurchasePage.widgets, widget => !_.isEmpty(widget))
        cultPackPrePurchasePage.actions = this.getPrePurchasePageActions(userContext, requestParams, builderParams)

        const issues: IssueDetailView[] = await this.issueBusiness.getCultOrMindSubscriptionPrePurchaseIssues(builderParams.packInfo, userContext)
        const analyticsData = {
            // "packId": CatalogueServiceUtilities.extractPackId(builderParams.packInfo.productId).toString(),
            "productId": builderParams.packInfo.productId,
            "title": builderParams.packInfo.title,
        }
        cultPackPrePurchasePage.navigationAction = {
            title: "HELP",
            action: {
                actionType: "REPORT_ISSUE",
                meta: {
                    title: "Help",
                    issues: issues
                }
            },
            textStyle: {},
            containerStyle: {},
            analyticsData: analyticsData
        }
        return cultPackPrePurchasePage
    }

    async getPostPurchasePageV2(userContext: UserContext, requestParams: CultPackPageRequestParamsV2, builderParams: CultPackPageBuilderParamsV2): Promise<ProductDetailPage> {
        builderParams.issuesMapPromise = this.CRMIssueService.getIssuesMap()
        // builderParams.bookingsPromise = requestParams.baseService.getBookingsForMembership(builderParams.membership.id, userContext.userProfile.userId)
        builderParams.moneyBackOfferPromise = this.cultBusiness.checkMoneyBackOfferOnMembership(builderParams.membership, userContext)
        // dont show only purchased center in colocated centers widget

        const widgetPromises: Promise<WidgetView>[] = []

        const cultBenefit = builderParams.membership && builderParams.membership.benefits.find(benefit => benefit.name == "CULT")
        const allowedCultCenterIDs: string[] = cultBenefit ? cultBenefit.meta["allowedCenterIDs"] || [] : []
        if (!_.isEmpty(allowedCultCenterIDs) && allowedCultCenterIDs.length > 1) {
            builderParams.colocatedCenterIDs = allowedCultCenterIDs
        }

        widgetPromises.push(this.buildSummaryWidgetV2(userContext, requestParams, builderParams))
        widgetPromises.push(this.buildMoneyBackOfferDetailWidget(userContext, requestParams, builderParams))
        if (PlayUtil.isPlayWebRequest(userContext, requestParams?.productType)) {
            widgetPromises.push(this.buildCenterInfoWidget(userContext, requestParams, builderParams))
            widgetPromises.push(this.buildPackDescriptionWidget(userContext, requestParams, builderParams))
        }
        widgetPromises.push(this.buildNoShowPolicyWidget(builderParams))
        widgetPromises.push(this.buildWorkoutsWidgetV2(userContext, requestParams, builderParams))

        const cultPackPostPurchasePage = new ProductDetailPage()
        cultPackPostPurchasePage.widgets = await Promise.all(widgetPromises)
        cultPackPostPurchasePage.widgets = _.filter(cultPackPostPurchasePage.widgets, widget => !_.isEmpty(widget))
        cultPackPostPurchasePage.actions = await this.getPostPurchasePageActionsV2(userContext, requestParams, builderParams)
        return cultPackPostPurchasePage

    }

    async getPostPurchasePage(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams): Promise<ProductDetailPage> {
        builderParams.issuesMapPromise = this.CRMIssueService.getIssuesMap()
        // builderParams.bookingsPromise = requestParams.baseService.getBookingsForMembership(builderParams.membership.id, userContext.userProfile.userId)
        builderParams.moneyBackOfferPromise = this.cultBusiness.checkMoneyBackOfferOnMembership(builderParams.membership, userContext)
        // dont show only purchased center in colocated centers widget

        const cultBenefit = builderParams.membership && builderParams.membership.benefits.find(benefit => benefit.name == "CULT")
        const allowedCultCenterIDs: string[] = cultBenefit ? cultBenefit.meta["allowedCenterIDs"] || [] : []
        if (!_.isEmpty(allowedCultCenterIDs) && allowedCultCenterIDs.length > 1) {
            builderParams.colocatedCenterIDs = allowedCultCenterIDs
        }
        const widgetPromises: Promise<WidgetView>[] = []
        widgetPromises.push(this.buildSummaryWidget(userContext, requestParams, builderParams))
        widgetPromises.push(this.buildMoneyBackOfferDetailWidget(userContext, requestParams, builderParams))
        widgetPromises.push(this.buildCenterInfoWidget(userContext, requestParams, builderParams))
        // widgetPromises.push(this.buildOGMWidget(userContext, requestParams, builderParams)) // DEPRECATED
        widgetPromises.push(this.buildPackRenewWidget(userContext, requestParams, builderParams))
        if (requestParams?.productType !== "PLAY") {
            widgetPromises.push(this.buildPackPauseResumeWidget(builderParams, requestParams.productType))
            widgetPromises.push(this.buildUpcomingPauseWidget(builderParams, userContext))
        }

        widgetPromises.push(this.buildUpcomingClassesWidget(userContext, requestParams, builderParams))
        widgetPromises.push(this.buildPackDescriptionWidget(userContext, requestParams, builderParams))
        widgetPromises.push(this.buildColocatedCentersWidget(userContext, requestParams, builderParams))
        // widgetPromises.push(this.buildComplementaryPackWidget(userContext, requestParams, builderParams))
        widgetPromises.push(this.buildNoShowPolicyWidget(builderParams))
        widgetPromises.push(this.buildWorkoutsWidget(userContext, requestParams, builderParams))
        widgetPromises.push(this.buildBiometricInfoWidget(builderParams))
        const cultPackPostPurchasePage = new ProductDetailPage()
        cultPackPostPurchasePage.widgets = await Promise.all(widgetPromises)
        cultPackPostPurchasePage.widgets = _.filter(cultPackPostPurchasePage.widgets, widget => !_.isEmpty(widget))
        cultPackPostPurchasePage.actions = await this.getPostPurchasePageActions(userContext, requestParams, builderParams)
        return cultPackPostPurchasePage
    }

    private async getDerivedCityDetails(userContext: UserContext, userId: string, cultPackProgress: CultPackProgressV2): Promise<DerivedCityDetails> {
        const tenant: Tenant = AppUtil.getTenantFromUserContext(userContext)
        const city = userContext.userProfile.city || await this.cityService.getDefaultCity(tenant)
        if (this.cityService.checkIfCityIsOtherCity(city.cityId)) {
            if (cultPackProgress && cultPackProgress.currentMembership.metadata["cityId"]) {
                const derivedCity = await this.cityService.getCityById(cultPackProgress.currentMembership.metadata["cityId"])
                return {
                    city: derivedCity, cultCityId: derivedCity.cultCityId, cityId: derivedCity.cityId,
                    defaultCultCenter: derivedCity.defaultCultCenter, defaultMindCenter: derivedCity.defaultMindCenter
                }
            }
        }
        return { city, cultCityId: city.cultCityId, cityId: city.cityId, defaultCultCenter: city.defaultCultCenter, defaultMindCenter: city.defaultMindCenter }
    }

    private async getPreferredCenter(requestParams: CultPackPageRequestParams, membership: Membership, defaultCenterId: string, preferredCenterId: string): Promise<CultCenter> {
        let preferedCenter
        if ((membership && membership.metadata["centerServiceCenterId"])) {
            const centerId = await CultUtil.getCultCenterIdFromCenterService(membership.metadata["centerServiceCenterId"], this.centerService)
            if (centerId) {
                preferedCenter = await requestParams.baseService.getFitnessCenter(centerId)
            }
        } else if (preferredCenterId) {
            preferedCenter = await requestParams.baseService.getFitnessCenter(preferredCenterId)
        }
        if (_.isNil(preferedCenter) && defaultCenterId) {
            preferedCenter = await requestParams.baseService.getFitnessCenter(defaultCenterId)
        }
        return preferedCenter
    }

    private async getStartDateOptionsV2(userContext: UserContext, requestParams: CultPackPageRequestParamsV2, builderParams: CultPackPageBuilderParamsV2): Promise<CultPackStartDateOptions> {
        const isMandatoryStartDateSupported = AppUtil.isMandatoryStartDateSupported(userContext)
        const tz = userContext.userProfile.timezone
        const today = TimeUtil.todaysDateWithTimezone(tz)
        let startDate: string = today
        const maxStartDate: string = ""
        const canChangeStartDate: boolean = true
        startDate = await this.getEarliestStartDateForFitnessMembership(userContext, requestParams.packInfo.product.durationInDays)

        const { minimumDate, selectedDate, maximumDate } = CultUtil.getPackStartDateOptions(builderParams.userContext, null, canChangeStartDate, requestParams.selectedStartDate, this.cultPackPageConfig.startDateWindow, startDate, maxStartDate)
        const selectedDateFinal = !isMandatoryStartDateSupported ? minimumDate : selectedDate
        return {
            minEligibleDate: minimumDate,
            maxEligibleDate: maximumDate,
            selectedDate: selectedDateFinal,
            canChangeStartDate: canChangeStartDate
        }
    }

    private async getEarliestStartDateForFitnessMembership(userContext: UserContext, durationInDays: number) {
        const earliestStartDateCult = await this.membershipService.getEarliestStartDate(userContext?.userProfile?.userId, Tenant.CUREFIT_APP, ELITE_MEMBERSHIP_PRIMARY_BENEFITS, durationInDays * 86400)
        const earliestStartDateGym = await this.membershipService.getEarliestStartDate(userContext?.userProfile?.userId, Tenant.CUREFIT_APP, GYM_MEMBERSHIP_PRIMARY_BENEFITS, durationInDays * 86400)
        const earliestStartDate = earliestStartDateGym.start > earliestStartDateCult.start ? earliestStartDateGym.start : moment(earliestStartDateCult.start).add(1, "days").startOf("day").valueOf()
        const earliestStartDateFormatted = moment(earliestStartDate).tz(userContext.userProfile.timezone).format("YYYY-MM-DD")
        this.logger.info("startDateLog : CultPackDetailViewBuilder.getEarliestStartDateForFitnessMembership userId: " + userContext?.userProfile?.userId + ", cultBenefits: " + JSON.stringify(ELITE_MEMBERSHIP_PRIMARY_BENEFITS) +
            ", gymBenefits: " + JSON.stringify(GYM_MEMBERSHIP_PRIMARY_BENEFITS) + ", durationInSeconds: " + durationInDays * 86400 + ", earliestStartDateCult.start: " + earliestStartDateCult?.start + ", earliestStartDateGym.start: "
            + earliestStartDateGym?.start + ", earliestStartDate: " + earliestStartDate + ", earliestStartDateFormatted: " + earliestStartDateFormatted)
        this.cfAnalytics.sendEventFromUserContext(<PackStartDateAnalyticsEvent> {
            analyticsEventName: AnalyticsEventName.PACK_START_DATE,
            from: "CultPackDetailViewBuilder.getEarliestStartDateForFitnessMembership",
            packDuration: durationInDays * 86400,
            cultStartDate: moment(earliestStartDateCult?.start).tz(userContext.userProfile.timezone).format("YYYY-MM-DD"),
            gymfitStartDate: moment(earliestStartDateGym?.start).tz(userContext.userProfile.timezone).format("YYYY-MM-DD"),
            finalEarliestStartDate: moment(earliestStartDate).tz(userContext.userProfile.timezone).format("YYYY-MM-DD"),
            previousMembEndDate: moment(earliestStartDate).tz(userContext.userProfile.timezone).format("YYYY-MM-DD")
        }, userContext, false, true, true, false)
        this.logger.info("membership start Date: " + earliestStartDate)
        return earliestStartDateFormatted
    }

    private async getStartDateOptions(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams): Promise<CultPackStartDateOptions> {
        const isMandatoryStartDateSupported = AppUtil.isMandatoryStartDateSupported(userContext)
        const tz = userContext.userProfile.timezone
        const packInfo = builderParams.packInfo
        const startDate = await FitnessUtil.getEarliestStartDateForFitnessMembership(userContext, packInfo.product.durationInDays, builderParams.centerServicePreferredCenterId, packInfo.displayName, this.membershipService, this.centerService)
        const { minimumDate, selectedDate, maximumDate } = CultUtil.getPackStartDateOptions(builderParams.userContext, builderParams.preferredCenter as CultCenter, true, requestParams.selectedStartDate, this.cultPackPageConfig.startDateWindow, startDate, undefined)
        const selectedDateFinal = !isMandatoryStartDateSupported ? minimumDate : selectedDate
        return {
            minEligibleDate: minimumDate,
            maxEligibleDate: maximumDate,
            selectedDate: selectedDateFinal,
            canChangeStartDate: true
        }
    }

    private async buildMoneyBackOfferDetailWidget(userContext: UserContext, requestParams: CultPackPageRequestParams | CultPackPageRequestParamsV2, builderParams: CultPackPageBuilderParams | CultPackPageBuilderParamsV2): Promise<ProductListWidget> {
        const offerDetail = await builderParams.moneyBackOfferPromise
        if (!_.isEmpty(offerDetail) && !_.isEmpty(offerDetail.cancellationWindow)) {
            const tz = userContext.userProfile.timezone
            const cancellationEndDate = TimeUtil.formatDateStringInTimeZone(offerDetail.cancellationWindow.endDate, tz, "DD MMM YYYY")
            if (offerDetail.isOfferApplied) {
                const header: Header = {
                    title: "Cancel Membership"
                }
                const actionCards: ActionCard[] = []
                actionCards.push({
                    subTitle: `Cancellation allowed till:      ${cancellationEndDate}`,
                    icon: "/image/icons/cult/cancel_refund.png",
                    action: ActionUtil.infoPage("noshowpolicy")
                })

                return new ProductListWidget("SMALL", header, actionCards)
            }
        }
    }

    private async buildSummaryWidgetV2(userContext: UserContext, requestParams: CultPackPageRequestParamsV2, builderParams: CultPackPageBuilderParamsV2): Promise<CultPackSummaryWidget> {
        const summaryWidgetBuilderParams: ICultPackSummaryWidgetBuilderParamsV2 = {
            isBuyFlow: builderParams.isBuyFlow,
            userContext: userContext,
            // isPreRegPack: builderParams.isPreRegPack,
            preAdvancePaid: builderParams.preAdvancePaid,
            productBusiness: this.productBusiness,
            packInfo: builderParams.packInfo,
            membership: builderParams.membership,
            preferredCenterId: requestParams.centerServiceId,
            cultPackProgress: builderParams.cultPackProgress,
            canChangeCenter: requestParams.canChangeCenter,
            startDateOptions: builderParams.startDateOptions,
            offersPromise: builderParams.offersPromise,
            offersV3Promise: builderParams.offersV3Promise,
            issuesMapPromise: builderParams.issuesMapPromise,
            moneyBackOfferPromise: builderParams.moneyBackOfferPromise,
            userPromise: !builderParams.isBuyFlow && this.userCache.getUser(builderParams.membership.userId),
            membershipTransferable: builderParams.membershipTransferable,
            serviceInterfaces: this.serviceInterfaces,
            offerUtil: this.offerUtil,
            preferredCenter: builderParams.preferredCenter
        }
        return new CultPackSummaryWidget().buildViewV2(summaryWidgetBuilderParams)
    }

    private async buildSummaryWidget(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams): Promise<CultPackSummaryWidget> {
        const summaryWidgetBuilderParams: ICultPackSummaryWidgetBuilderParams = {
            isBuyFlow: builderParams.isBuyFlow,
            userContext: userContext,
            isPreRegPack: builderParams.isPreRegPack,
            preAdvancePaid: builderParams.preAdvancePaid,
            productBusiness: this.productBusiness,
            packInfo: builderParams.packInfo,
            membership: builderParams.membership,
            preferredCenter: builderParams.preferredCenter,
            cultPackProgress: builderParams.cultPackProgress,
            canChangeCenter: requestParams.canChangeCenter,
            startDateOptions: builderParams.startDateOptions,
            offersPromise: builderParams.offersPromise,
            offersV3Promise: builderParams.offersV3Promise,
            issuesMapPromise: builderParams.issuesMapPromise,
            moneyBackOfferPromise: builderParams.moneyBackOfferPromise,
            userPromise: !builderParams.isBuyFlow && this.userCache.getUser(builderParams.membership.userId),
            membershipTransferable: builderParams.membershipTransferable,
            serviceInterfaces: this.serviceInterfaces,
            offerUtil: this.offerUtil
        }
        return new CultPackSummaryWidget().buildView(summaryWidgetBuilderParams)
    }

    private async buildCenterSelectionWidget(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams): Promise<CenterSelectionWidget> {
        const action: SelectCenterAction = {
            title: "Pick a center",
            showHelp: true,
            showFavourite: false,
            actionType: "SELECT_CENTER",
            meta: {
                productId: builderParams.packInfo.productId,
                productType: requestParams.productType,
                ageCategory: "ADULT",
                showAvailableWorkouts: true,
                pageFrom: requestParams.productType === "FITNESS" ? "CultPack" : "MindPack"
            },
            productType: requestParams.productType
        }
        const prefixText = "Preferred centre: "
        const centerSelectionWidget: CenterSelectionWidget = {
            title: "Select center",
            prefixText: prefixText,
            canChangeCenter: requestParams.canChangeCenter,
            preferredCenterId: builderParams.preferredCenter ? builderParams.preferredCenter.id : undefined,
            preferredCenterName: builderParams.preferredCenter ? builderParams.preferredCenter.name : undefined,
            widgetType: "CENTER_PICKER_WIDGET",
            action: action
        }
        return centerSelectionWidget
    }

    private async buildCenterInfoWidget(userContext: UserContext, requestParams: CultPackPageRequestParams | CultPackPageRequestParamsV2, builderParams: CultPackPageBuilderParams | CultPackPageBuilderParamsV2): Promise<CenterSelectionWidget> {
        // in some cases, centerID is not present in membership and it will end up showing session preferred center
        if (builderParams.membership && builderParams.membership.metadata["centerId"]) {
            const prefixText =  "Preferred centre: "
            const centerInfoWidget: CenterSelectionWidget = {
                title: "Select center",
                prefixText: prefixText,
                canChangeCenter: false,
                preferredCenterId: builderParams.preferredCenter ? builderParams.preferredCenter.id : undefined,
                preferredCenterName: builderParams.preferredCenter ? builderParams.preferredCenter.name : undefined,
                widgetType: "CENTER_PICKER_WIDGET"
            }
            if (PlayUtil.isPlayWebRequest(userContext, requestParams?.productType) && builderParams.membership.metadata["workoutId"]) {
                centerInfoWidget.prefixText = "Preferred centre & sport"
                centerInfoWidget.preferredWorkoutId = builderParams.membership.metadata["workoutId"]
                centerInfoWidget.preferredWorkoutName = PlayUtil.getSportNameById(builderParams.membership.metadata["workoutId"].toString())
            }
            return centerInfoWidget
        }
    }

    private async buildStartDateWidget(userContext: UserContext, requestParams: CultPackPageRequestParams | CultPackPageRequestParamsV2, builderParams: CultPackPageBuilderParams | CultPackPageBuilderParamsV2): Promise<DatePickerWidget> {
        const startDateOptions = builderParams.startDateOptions
        const datePickerWidget: DatePickerWidget = {
            title: "Pick a Start Date",
            startDate: startDateOptions.minEligibleDate,
            endDate: startDateOptions.maxEligibleDate,
            selectedDate: startDateOptions.selectedDate,
            canChangeStartDate: startDateOptions.canChangeStartDate,
            calloutText: startDateOptions.calloutText,
            widgetType: "DATE_PICKER_WIDGET"
        }
        return datePickerWidget
    }

    private async buildColocatedCentersWidget(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams): Promise<CenterSelectionWidget> {
        if (AppUtil.isCultCentersViewOnlyModeSupported(userContext)) {
            if (!_.isEmpty(builderParams.colocatedCenterIDs)) {
                const action: SelectCenterAction = {
                    title: "Colocated Centers",
                    showHelp: false,
                    productType: requestParams.productType,
                    actionType: "SELECT_CENTER",
                    showFavourite: false,
                    meta: {
                        centerIds: builderParams.colocatedCenterIDs,
                        viewOnly: true
                    }
                }
                const centerSelectionWidget: CenterSelectionWidget = {
                    title: "See centres you have access to",
                    canChangeCenter: true,
                    widgetType: "CENTER_PICKER_WIDGET",
                    action: action
                }
                return centerSelectionWidget
            }
        }
    }

    /**
     * @deprecated
     */
    // private async buildOGMWidget(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams): Promise<OgmPackOfferWidget> {
    //     // const ogmDetail = await requestParams.baseService.ogmDetail(userContext.userProfile.userId, builderParams.derivedCity.cultCityId)
    //     // if (ogmDetail && builderParams.cultPack.isOGMApplicable) {
    //     //     return new CultOgmPackOfferWidget(ogmDetail, requestParams.productType)
    //     // }
    // }

    // private async buildBuddyWidget(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams): Promise<PickerWidget> {
    //     const isGiftPack = builderParams.packInfo.isGiftPack
    //     const tenant: Tenant = AppUtil.getTenantFromUserContext(userContext)
    //     const countries = await this.countryService.listCountries(tenant)
    //     const countriesData: CountryData[] = _.map(countries, country => {
    //         return {
    //             countryId: country.countryId,
    //             name: country.name,
    //             countryCallingCode: country.countryCallingCode,
    //             validationCountryCode: country.countryCode
    //         }
    //     })
    //     if (builderParams.packInfo.isGroupPack || isGiftPack) {
    //         const pickerWidget: PickerWidget = {
    //             widgetType: "PICKER_WIDGET",
    //             title: isGiftPack ? "Gift this to" : "Buddy",
    //             subTitle: isGiftPack ? "Pick your giftee" : "Pick your buddy",
    //             canChangeBuddy: true,
    //             action: {
    //                 actionType: "SELECT_CONTACT"
    //             },
    //             selectedOption: undefined
    //         }

    //         if (AppUtil.isAddBuddyInternationalSupported(userContext)) {
    //             pickerWidget.action = {
    //                 actionType: "NAVIGATION",
    //                 url: "curefit://buddytransferpage",
    //                 meta: {
    //                     selectedOption: undefined,
    //                     countriesData,
    //                     defaultCountryCode: userContext.userProfile.city.country.countryId,
    //                     title: isGiftPack ? "Pick your giftee" : "Pick your buddy",
    //                     relationTitle: isGiftPack ? "Giftee name" : "Buddy name"
    //                 }
    //             }
    //         }
    //         return pickerWidget
    //     }
    // }

    private async buildPackRenewWidget(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams): Promise<ManageOptionsWidget> {
        const packInfo = builderParams.packInfo
        if (this.isRenewEnabled(builderParams)) {
            const manageOptions: ManageOptions = {
                displayText: "Renew your pack",
                icon: "RESCHEDULE_OR_CANCEL",
                options: [{
                    isEnabled: true,
                    type: "NAVIGATION",
                    action: ActionUtil.cultPackBrowse(userContext.sessionInfo.userAgent, false, "ADULT"),
                    displayText: "Renew pack"
                }]
            }
            return new ManageOptionsWidget(manageOptions, {})
        }
    }

    private async buildOffersWidget(userContext: UserContext, requestParams: CultPackPageRequestParams | CultPackPageRequestParamsV2, builderParams: CultPackPageBuilderParams | CultPackPageBuilderParamsV2): Promise<WidgetView> {
        const offersResponse = await builderParams.offersPromise
        const offersV3Response = await builderParams.offersV3Promise
        const offerItem = Object.assign({}, offersV3Response ?
            CultUtil.getOfferItem(offersV3Response, builderParams.packInfo.productId) :
            offersResponse[builderParams.packInfo.productId])
        if (AppUtil.isNewPackOrderConformationSupported(userContext, (await userContext.userPromise).isInternalUser) && offerItem && !_.isEmpty(offerItem.offers)) {
            offerItem.offers = OfferUtil.segregateNoCostEMIOffers(offerItem.offers).get("OTHER_OFFERS")
            const newOffers: OfferV2[] = []
            // Remove fit-club from offers as there is a separate widget
            offerItem.offers.forEach((offer) => {
                newOffers.push(offer)
            })
            offerItem.offers = newOffers
        }
        if (offerItem && !_.isEmpty(offerItem.offers)) {
            // finally exclude all offers which don't have a description
            offerItem.offers = offerItem.offers.filter(offer => !_.isEmpty(offer.description))
            return getOffersWidget("Offer", offerItem.offers)
        }
    }

    private async buildNoCostEmiOfferWidget(builderParams: CultPackPageBuilderParams, packId: string, productType: string): Promise<NoCostEmiWidget> {
        const offersV3Response = await builderParams.offersV3Promise
        const offerItem = Object.assign({}, CultUtil.getOfferItem(offersV3Response, builderParams.packInfo.productId))
        if (!offerItem) {
            return undefined
        }
        const offersMap = OfferUtil.segregateNoCostEMIOffers(offerItem.offers)
        const emiOffers = offersMap.get("NO_COST_EMI")
        if (!emiOffers.length) {
            return undefined
        }
        let maxEmiTenure = 12
        emiOffers.forEach((emiOffer) => {
            emiOffer.addons.forEach((addOn) => {
                // Max supported emi duration in months
                if (addOn.config && addOn.config.maxEmiTenure) {
                    maxEmiTenure = addOn.config.maxEmiTenure
                }
            })
        })
        const banks = emiOffers[0].description
        const numberOfMonths = CultUtil.getEmiTenureForPack(builderParams.packInfo.product.durationInDays, maxEmiTenure)
        const result = CultUtil.getPackPriceAndOfferIdV2(builderParams.packInfo, offersV3Response)
        return new NoCostEmiWidget(
            `No cost EMI from ${Math.round(result.price.listingPrice / numberOfMonths)}/month*`,
            banks,
            "NO_COST_EMI",
            {
                actionType: "NAVIGATION",
                url: `curefit://nocostemipage?packId=${packId}&productType=${productType}`,
                title: "Know More"
            },
            "")
    }

    private async buildPackDescriptionWidget(userContext: UserContext, requestParams: CultPackPageRequestParams | CultPackPageRequestParamsV2, builderParams: CultPackPageBuilderParams | CultPackPageBuilderParamsV2): Promise<DescriptionWidget> {
        const benefits = await FitnessUtil.getSkuPlusBenefitsList(builderParams?.packInfo)
        this.logger.info("SKU_PLUS_WEB builderParams", builderParams)
        this.logger.info("SKU_PLUS_WEB builderParams?.packInfo", builderParams?.packInfo)
        const infoCards: InfoCard[] = []
        const infoCard: InfoCard = {
            title: "About this pack",
            // subTitle: builderParams.packInfo.subTitle
            subTitle: "",
            items: []
        }
        if (benefits) {
            infoCard.items = benefits.map((benefit: any) => {
                return {title: benefit.title} as InfoCard
            })
        }
        this.logger.info("SKU_PLUS_WEB benefits", benefits)
        infoCards.push(infoCard)
        this.logger.info("SKU_PLUS_WEB infoCards", infoCards)
        return new DescriptionWidget(infoCards)
    }

    /**
     * @deprecated
     */
    // private async buildComplementaryPackWidget(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams): Promise<ProductListWidget> {
        // const packInfo = builderParams.packInfo
        // if (!_.isEmpty(cultPack.includedProducts)) {
        //     const header = {
        //         title: cultPack.includedProducts[0].title
        //     }
        //     let complementaryPackProductId
        //     let complementaryPackProductType: ProductType

        //     if (cultPack.includedProducts[0].tenant === "mind.fit") {
        //         complementaryPackProductId = CatalogueServiceV2Utilities.getMindPackProductId(cultPack.includedProducts[0].id)
        //         complementaryPackProductType = "MIND"
        //     } else {
        //         complementaryPackProductId = CatalogueServiceV2Utilities.getCultPackProductId(cultPack.includedProducts[0].id)
        //         complementaryPackProductType = "FITNESS"
        //     }
        //     const infoCard: InfoCard = {
        //         title: cultPack.includedProducts[0].subTitle,
        //         subTitle: cultPack.includedProducts[0].tenant === "mind.fit" ? "Mind.fit brings you mindful Yoga and Meditation classes." : "Cult brings you a plethora of group fitness classes.",
        //         image: UrlPathBuilder.getPackImagePath(complementaryPackProductId, complementaryPackProductType, "MAGAZINE_OFFER", builderParams.packInfo.imageVersion, userContext.sessionInfo.userAgent),
        //     }
            // return new ProductListWidget("MAGAZINE_OFFER", header, [infoCard])
        // }
    // }

    private async buildHowItWorksWidget(userContext: UserContext, requestParams: CultPackPageRequestParams | CultPackPageRequestParamsV2, builderParams: CultPackPageBuilderParams | CultPackPageBuilderParamsV2): Promise<ProductListWidget> {
        try {
            const header: Header = {
                title: this.cultPackPageConfig.howItWorksTitle,
                subTitle: CatalogueServiceUtilities.getCultSubtitle(builderParams.packInfo)
            }

            const infoCards: InfoCard[] = []
            let howItWorksItemList
            // if (builderParams.packInfo.isGroupPack) {
            //     howItWorksItemList = this.cultPackPageConfig.groupPackHowItWorksItemList
            // } else if (builderParams.packInfo.isGiftPack) {
            //     howItWorksItemList = this.cultPackPageConfig.giftPackHowItWorksItemList
            // } else if (builderParams.packInfo.ageCategory === "JUNIOR") {
            //     howItWorksItemList = this.cultPackPageConfig.cultJuniorHowItWorksItemList
            // } else {
                howItWorksItemList = this.cultPackPageConfig.howItWorksItemList
            // }
            howItWorksItemList.forEach(item => {
                infoCards.push({
                    subTitle: item.text,
                    icon: item.icon
                })
            })
            return new ProductListWidget("SMALL", header, infoCards)
        }
        catch (e) {
            return undefined
        }
    }

    private async getPackBenefits(): Promise<ListWidgetV2> {
        const items: ListItem[] = []

        items.push({
            title: "Unlimited access",
            subTitle: "All PRO & ELITE gyms",
            icon: "/image/icons/unlimited_access.png"
        })

        items.push({
            title: "At-center group classes",
            subTitle: "Yoga, Dance fitness, Strength & more",
            icon: "/image/icons/group_classes.png"
        })

        if (items.length > 0) {
            const header = {
                title: "Benefits of cultpass ELITE",
            }
            const packBenefitsWidget = new ListWidgetV2()
            packBenefitsWidget.items = items
            packBenefitsWidget.header = header
            return packBenefitsWidget
        }
        return null
    }

    private async getBoosterPackBenefits(
        packInfo: OfflineFitnessPack
    ): Promise<ListWidgetV2> {
        const items: ListItem[] = []

        items.push({
            title: "Extra Pause Days",
            subTitle: "Take the well deserved break you need, stress free",
            icon: "/image/icons/booster_pause.png"
        })

        items.push({
            title: "Membership Transfer",
            subTitle: "Transfer your membership to other cult member",
            icon: "/image/icons/booster_membership_transfer.png"
        })

        items.push({
            title: "All India Acess",
            subTitle: `70 sessions in any center in India`,
            icon: "/image/icons/booster_all_city.png"
        })

        items.push({
            title: "1000+ workout videos",
            subTitle: `Workout from anywhere`,
            icon: "/image/icons/booster_workouts.png"
        })

        items.push({
            title: "upto 50% off on cultsports",
            subTitle: `Get anything for your fitness needs`,
            icon: "/image/icons/booster_cultsport.png"
        })

        if (items.length > 0) {
            const header = {
                title: "Booster Benefits",
            }
            const packBenefitsWidget = new ListWidgetV2()
            packBenefitsWidget.items = items
            packBenefitsWidget.header = header
            return packBenefitsWidget
        }
        return null
    }

    private async buildWorkoutsWidgetV2(userContext: UserContext, requestParams: CultPackPageRequestParams | CultPackPageRequestParamsV2, builderParams: CultPackPageBuilderParams | CultPackPageBuilderParamsV2): Promise<ProductGridWidget> {
        const workouts = await builderParams.workoutsPromise
        const isPlayWeb = PlayUtil.isPlayWebRequest(userContext, requestParams?.productType)
        const header: Header = {
            title: isPlayWeb ? "Sports available" : "Workouts"
        }
        const workoutActions: ActionCard[] = []
        workouts.forEach(workout => {
            const seoParams: SeoUrlParams = {
                productName: workout.name
            }
            let workoutAction: string
            if (isPlayWeb) {
                workoutAction = PlayUtil.getWorkoutWebPageAction(workout?.id?.toString())
            } else {
                workoutAction = ActionUtil.cultWorkoutV2(workout.id.toString(), "cultfitclp", userContext.sessionInfo.userAgent, seoParams, requestParams.productType)
            }
            let thumbDoc = undefined
            if (userContext.sessionInfo.userAgent === "APP") {
                thumbDoc = _.find(workout.documents, doc => doc.tagName === "M_CARD")
            } else {
                thumbDoc = _.find(workout.documents, doc => doc.tagName === "D_CARD")
            }
            workoutActions.push({
                title: workout.name,
                image: isPlayWeb ? PlayUtil.getPackImageByWorkoutId(workout?.id?.toString()) : thumbDoc ? "/" + thumbDoc.URL : undefined,
                action: workoutAction
            })
        })
        const workoutsWidget: ProductGridWidget = new ProductGridWidget("SQUARE", header, workoutActions)
        return workoutsWidget
    }

    private async buildWorkoutsWidget(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams): Promise<ProductGridWidget> {
        const workouts = await builderParams.workoutsPromise
        const isPlayWeb = PlayUtil.isPlayWebRequest(userContext, requestParams?.productType)
        const header: Header = {
            title: isPlayWeb ? "Sports available" : "Workouts"
        }
        const workoutActions: ActionCard[] = []
        workouts.forEach(workout => {
            const seoParams: SeoUrlParams = {
                productName: workout.name
            }
            let workoutAction: string
            if (isPlayWeb) {
                workoutAction = PlayUtil.getWorkoutWebPageAction(workout?.id?.toString())
            } else {
                workoutAction = ActionUtil.cultWorkoutV2(workout.id.toString(), "cultfitclp", userContext.sessionInfo.userAgent, seoParams, requestParams.productType)
            }
            let thumbDoc = undefined
            if (userContext.sessionInfo.userAgent === "APP") {
                thumbDoc = _.find(workout.documents, doc => doc.tagName === "M_CARD")
            } else {
                thumbDoc = _.find(workout.documents, doc => doc.tagName === "D_CARD")
            }
            workoutActions.push({
                title: workout.name,
                image: isPlayWeb ? PlayUtil.getPackImageByWorkoutId(workout?.id?.toString()) : thumbDoc ? "/" + thumbDoc.URL : undefined,
                action: workoutAction
            })
        })
        const workoutsWidget: ProductGridWidget = new ProductGridWidget("SQUARE", header, workoutActions)
        return workoutsWidget
    }

    private async buildBiometricInfoWidget(builderParams: CultPackPageBuilderParams): Promise<ProductListWidget> {
        if (CultUtil.isCustomerBiometricSupportedInCenter(builderParams.preferredCenter as CultCenter)) {
            const header: Header = {
                title: this.getBiometricHeader()
            }

            const actionCards: ActionCard[] = []
            actionCards.push({
                subTitle: this.cultPackPageConfig.biometricInfo.infoDescription,
                icon: this.cultPackPageConfig.biometricInfo.icon,
                action: ActionUtil.infoPage("biometric"),
                seeMoreText: this.cultPackPageConfig.biometricInfo.seeMoreDescription
            })

            return new ProductListWidget("SMALL", header, actionCards)
        }
    }

    private async buildNoShowPolicyWidget(builderParams: CultPackPageBuilderParams |  CultPackPageBuilderParamsV2): Promise<ProductListWidget> {
        // if (builderParams.packInfo.ageCategory !== "JUNIOR") {
            const header: Header = {
                title: this.cultPackPageConfig.noshowPolicyInfo.infoTitle
            }
            const actionCards: ActionCard[] = []
            actionCards.push({
                subTitle: this.cultPackPageConfig.noshowPolicyInfo.infoDescription,
                icon: this.cultPackPageConfig.noshowPolicyInfo.icon,
                action: ActionUtil.infoPage("noshowpolicy"),
                seeMoreText: this.cultPackPageConfig.noshowPolicyInfo.seeMoreDescription
            })

            return new ProductListWidget("SMALL", header, actionCards)
        // }
    }

    async buildPackPauseResumeWidget(builderParams: CultPackPageBuilderParams, productType: string): Promise<ManageOptionsWidget> {
        const membership = builderParams.membership, userContext = builderParams.userContext
        const tz: Timezone = userContext.userProfile.timezone
        const remainingPauseDays = GymfitUtil.convertMillisToDays(membership.remainingPauseDuration)
        if (GymfitUtil.isPauseResumeAllowed(userContext, membership)) {
            const isPauseAllowed = remainingPauseDays > 0
            const isPauseNotAllowed = _.isNil(remainingPauseDays)
            const isOptionEnabled = membership.status === "PAUSED" || isPauseAllowed
            const isEditPauseDateSupported = AppUtil.isPauseEditDateSupported(userContext, (await userContext.userPromise).isInternalUser)
            const infoText = membership.status === "PAUSED" ? "You can also manually resume your pack before the specified date.\nOnce you resume, your pack will be extended by the number of days you were on pause"
                : "You can pause your pack when you can't use it like during travel or illness.\n\nOnce you resume, your pack is extended by the number of days you were on pause"
            const manageOptions: ManageOptions = {
                displayText: membership.status === "PAUSED" ? (isEditPauseDateSupported ? "Modify Pause" : "Resume pack") : "Pause pack",
                icon: membership.status === "PAUSED" ? "RESUME" : "PAUSE",
                options: [{
                    isEnabled: isOptionEnabled,
                    type: membership.status === "PAUSED" ? "RESUME_MEMBERSHIP" : "PAUSE_MEMBERSHIP",
                    displayText: membership.status === "PAUSED" ? (isEditPauseDateSupported ? "Modify Pause" : "Resume pack") : "Pause pack"
                }],
                info: {
                    title: "About Pause",
                    subTitle: infoText
                }
            }
            const endDateString = TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.end, "yyyy-MM-dd")
            let pauseEndDate, pauseStartDate
            let limit = endDateString
            let remainingDaysInCurrentDuration
            if (membership.status === "PAUSED") {
                const pauseDate = membership.activePause ? membership.activePause.end ? TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.activePause.end, "yyyy-MM-dd") : undefined : undefined
                pauseEndDate = pauseDate
                manageOptions.subTitle = pauseDate ? `Paused till ${TimeUtil.formatDateInTimeZone(tz, new Date(pauseDate), "DD MMM")
                    }` : "Your pack is paused"
                pauseStartDate = membership.activePause ? TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.activePause.start, "yyyy-MM-dd") : undefined
                const pauseStartDateFormatted = TimeUtil.formatDateInTimeZone(tz, new Date(pauseStartDate))
                remainingDaysInCurrentDuration = TimeUtil.diffInDays(tz, pauseStartDateFormatted, TimeUtil.todaysDate(tz))
                limit = TimeUtil.addDays(tz, endDateString, remainingDaysInCurrentDuration)
            } else {
                if (isPauseAllowed) {
                    manageOptions.subTitle = `${remainingPauseDays} pause days remaining`
                } else {
                    manageOptions.subTitle = isPauseNotAllowed ? "Pauses not allowed for this pack" : "You have used all your pauses."
                }
            }

            const meta: any = {
                membershipId: membership.id,
                productType: productType,
                title: isEditPauseDateSupported ? "Resume/Edit Pause" : "Resume Pack",
                pauseMaxDays: GymfitUtil.convertMillisToDays(membership.maxPauseDuration),
                remainingPauseDays: remainingPauseDays,
                remainingDaysInCurrentDuration,
                pauseEndDate: pauseEndDate ? TimeUtil.formatDateInTimeZone(tz, new Date(pauseEndDate)) : undefined,
                startDateParams: {
                    date: TimeUtil.formatDateInTimeZone(tz, new Date(pauseStartDate), "YYYY-MM-DD hh:mm A"),
                    limit,
                    canEdit: false,
                    pauseEndText: "Your pack is paused till"
                },
                action: {
                    primaryText: "RESUME",
                    secondaryText: isEditPauseDateSupported ? "EDIT" : "CANCEL"
                },
                editPauseAction: {
                    meta: { isEdit: true, membershipId: membership.metadata["membershipId"], productType },
                    actionType: "PAUSE_CULT_MEMEBERSHIP"
                },
                pauseInfoTitles: {
                    pauseUsed: "Pause days used",
                    membershipExtended: "Membership will be extended by",
                    membershipEndsOn: "Membership will now end on",
                    pauseLeft: "Pause days left"
                },
                pauseInfo: this.getPauseInfo(tz, remainingDaysInCurrentDuration, limit, (remainingPauseDays - remainingDaysInCurrentDuration)),
                pauseReason: {
                    options: [
                        { optionText: "I am travelling out of town", optionId: "TRAVEL_OUT" },
                        { optionText: "I am injured", optionId: "INJURED" },
                        { optionText: "I am unwell", optionId: "UNWELL" },
                        { optionText: "Work is keeping me busy", optionId: "WORK_BUSY" },
                        { optionText: "I have personal commitments", optionId: "PERSONAL_COMITMENTS" }
                    ],
                    selectionOptionId: "TRAVEL_OUT",
                    allowOthers: true
                },
                description: "You can pause your pack as many times as you like until you reach this limit."
            }
            const pauseWidget = new ManageOptionsWidget(manageOptions, meta)
            pauseWidget.isDisabled = !isOptionEnabled
            return pauseWidget
        }
    }

    private getPauseInfo(tz: Timezone, pausedUsed: number, packEndDate: string, pauseDaysLeft: number): PauseInfo[] {
        const endsOn = TimeUtil.formatDateInTimeZone(tz, new Date(packEndDate), "DD MMM YYYY")
        return [
            { title: "Pause days used", value: AppUtil.appendDays(pausedUsed) },
            { title: "Membership will be extended by", value: AppUtil.appendDays(pausedUsed) },
            { title: "Membership will now end on", value: endsOn },
            { title: "Pause days left", value: AppUtil.appendDays(pauseDaysLeft) }
        ]
    }

    private async buildUpcomingPauseWidget(builderParams: CultPackPageBuilderParams, userContext: UserContext): Promise<ManageOptionsWidget> {
        const membership = builderParams.membership
        const isSelectMembership = MembershipItemUtil.isSelectMembership(membership)
        const tz = builderParams.userContext.userProfile.timezone
        if (GymfitUtil.isUpcomingPause(membership)) {
            const isOptionEnabled = AppUtil.isCultPauseCancelSupported(builderParams.userContext)
            const pauseStartDate = membership.activePause ? TimeUtil.parseDateFromEpochWithTimezone(tz, membership.activePause.start) : undefined
            const pauseEndDate = membership.activePause ? membership.activePause.end ? TimeUtil.parseDateFromEpochWithTimezone(tz, membership.activePause.end) : undefined : undefined
            const pauseEndDateFormatted = TimeUtil.formatDateInTimeZone(tz, pauseEndDate)
            const pauseStartDateFormatted = TimeUtil.formatDateInTimeZone(tz, pauseStartDate)
            let pauseEndDateWithTime
            if (pauseEndDate) {
                pauseEndDateWithTime = TimeUtil.getDefaultMomentForDateString(pauseEndDateFormatted, tz).startOf("day").subtract(1, "minute").toDate()
            }
            const startDateText = `${TimeUtil.formatDateInTimeZone(tz, pauseStartDate, "DD MMM hh:mm a")}`
            const daysDiff = TimeUtil.diffInDaysReal(tz, TimeUtil.todaysDate(tz), pauseStartDateFormatted)
            const remainingDaysInCurrentDuration = daysDiff < 0 ? 0 : daysDiff
            const limit = TimeUtil.addDays(tz, TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.end, "yyyy-MM-dd"), remainingDaysInCurrentDuration)
            const remainingPauseDays = GymfitUtil.convertMillisToDays(membership.remainingPauseDuration)
            const meta: any = {
                membershipId: membership.id,
                productType: builderParams.packInfo.productType,
                title: "Cancel Pause",
                pauseMaxDays: GymfitUtil.convertMillisToDays(membership.maxPauseDuration),
                remainingPauseDays: remainingPauseDays,
                remainingDaysInCurrentDuration,
                pauseEndDate: pauseEndDate ? TimeUtil.formatDateInTimeZone(tz, pauseEndDate) : undefined,
                startDateParams: {
                    date: TimeUtil.todaysDate(tz),
                    limit,
                    canEdit: false,
                    pauseEndText: "Your pack is paused till"
                },
                action: {
                    primaryText: "YES",
                    secondaryText: "NO"
                },
                pauseInfoTitles: {
                    pauseUsed: "Pause days used",
                    membershipExtended: "Membership will be extended by",
                    membershipEndsOn: "Membership will now end on",
                    pauseLeft: "Pause days left"
                },
                pauseInfo: this.getPauseInfo(tz, remainingDaysInCurrentDuration, limit, (remainingPauseDays - remainingDaysInCurrentDuration)),
                dateParam: {
                    date: TimeUtil.formatDateInTimeZone(tz, pauseStartDate, "YYYY-MM-DD hh:mm A"),
                    limit,
                    pauseEndDate: pauseEndDateWithTime ? TimeUtil.formatDateInTimeZone(tz, pauseEndDateWithTime, "DD MMM hh:mm a") : undefined,
                    pauseEndText: `Pause starting ${startDateText} till`
                },
                subTitle: `You are cancelling pause starting tonight till ${TimeUtil.formatDateInTimeZone(tz, pauseEndDateWithTime, "DD MMM hh:mm a")}. Your pause days remain intact`,
                refreshPageOnCompletion: true,
                viaDeepLink: true,
                showEmotionMeter: false,
                onCompletionAction: {
                    actionType: "POP_AND_TRIGGER_ACTION",
                    meta: {
                      nextAction: {
                        actionType: "NAVIGATION",
                        url: await CatalogueServiceUtilities.getCultMembershipDetailsPageAction(membership.id.toString(), userContext)
                      },
                    },
                  },
                additionalInfo: "All existing booking will get cancelled",
                performanceTitle: "On break for",
                performanceSubtitle: [
                    "Rest well, come back stronger",
                    "Rest well, come back stronger",
                    "Most athletes experience a 5% decrease in cardio-vascular fitness and strength after more than 2 weeks of inactivity",
                    "Most athletes experience a 10% decrease in cardio-vascular fitness and strength after more than 3 weeks of inactivity",
                    "Most athletes experience a 20% decrease in cardio-vascular fitness and strength after more than 4 weeks of inactivity",
                ],
                startDateTitle: "Select start date for your pause",
                endDateTitle: "Select end date for your pause",
                bannerUrl: "image/pause/pause_banner_2.png",
                pauseConfirmationData: {
                    confirmationLottieUrl: "/image/mem-exp/lottie/Confirmation.json",
                    daysLeftText: "PAUSE DAYS LEFT",
                    pauseDaysText: "Membership paused for",
                    endDateText: "Pack now ends on ",
                    suggestionTitle: "Pick a way to stay active",
                    homeWorkoutText: "Maintain your weekly streak with at home workout",
                    homeWorkoutImageUrl: "/image/pause/yoga_live.png",
                suggestionList: [
                        {
                            "iconUrl": "image/pause/Fitness.png",
                            "title": "Running or Cycling",
                            "isSelected": true,
                        },
                        {
                            "iconUrl": "image/pause/Shoes.png",
                            "title": "10K Steps Daily",
                            "isSelected": false,
                        },
                        {
                            "iconUrl": "image/pause/Sauna.png",
                            "title": "Not active",
                            "isSelected": false,
                        },
                    ]
                }
            }
            const manageOptions: ManageOptions = {
                displayText: AppUtil.isCultPauseCancelSupported(userContext) ? "Cancel Pause" : "Pause Scheduled",
                icon: "PAUSE",
                options: [{
                    isEnabled: isOptionEnabled,
                    type: "CANCEL_CULT_PACK_PAUSE",
                    displayText: AppUtil.isCultPauseCancelSupported(userContext) ? "Cancel Pause" : "Pause Scheduled",
                    meta
                }],
                info: {
                    title: "About Pause",
                    subTitle: "You can pause your pack when you can't use it like during travel or illness.\n\nOnce you resume, your pack is extended by the number of days you were on pause"
                }
            }
            manageOptions.subTitle = pauseStartDate && pauseEndDateWithTime ? `Pause starting ${startDateText} till ${TimeUtil.formatDateInTimeZone(tz, pauseEndDateWithTime, "DD MMM hh:mm a")}` : ""
            const pauseWidget = new ManageOptionsWidget(manageOptions, meta)
            pauseWidget.isDisabled = !isOptionEnabled
            return pauseWidget
        }
    }

    private async buildUpcomingClassesWidget(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams): Promise<ProductListWidget> {
        const actionCards: ActionCard[] = []
        const bookings = await builderParams.bookingsPromise
        const tz = userContext.userProfile.timezone
        if (!_.isEmpty(bookings)) {
            const currentDate = new Date()
            const upcomingBookings = bookings.filter(booking => {
                const classDate = momentTz.tz(booking.Class.date + " " + booking.Class.startTime, tz).toDate()
                return classDate.getTime() > currentDate.getTime()
            })
            if (!_.isEmpty(upcomingBookings)) {
                const sortedBookings = upcomingBookings.sort((a, b) => {
                    if (a.Class.date < b.Class.date) {
                        return -1
                    } else if (a.Class.date > b.Class.date) {
                        return 1
                    } else {
                        if (a.Class.startTime < b.Class.startTime) {
                            return -1
                        } else {
                            return 1
                        }
                    }
                })

                sortedBookings.forEach(booking => {
                    actionCards.push({
                        title: booking.Center ? booking.Class.Workout.name + " at " + booking.Center.name : booking.Class.Workout.name,
                        subTitle: TimeUtil.formatDateStringInTimeZone(booking.Class.date + " " + booking.Class.startTime, tz, "ddd, DD MMM hh:mm a"),
                        action: requestParams.productType === "FITNESS" ? ActionUtil.cultFitBooking(booking.bookingNumber) : ActionUtil.mindFitBooking(booking.bookingNumber),
                        image: CatalogueServiceUtilities.getWorkoutImage(booking.Class.Workout.id)
                    })
                })
                const header: Header = {
                    title: "Upcoming Classes:"
                }
                return new ProductListWidget("MEDIUM", header, actionCards)
            }

        }

    }

    private getPrePurchasePageActionsV2(userContext: UserContext, requestParams: CultPackPageRequestParamsV2, builderParams: CultPackPageBuilderParams | CultPackPageBuilderParamsV2): Action[] {
        if (!builderParams.startDateOptions.selectedDate) {
            return [{
                title: "Pick a start date",
                actionType: "SHOW_DATE_PICKER",
                meta: {
                    startDate: builderParams.startDateOptions.minEligibleDate,
                    endDate: builderParams.startDateOptions.maxEligibleDate,
                    selectedDate: builderParams.startDateOptions.selectedDate,
                    canChangeStartDate: builderParams.startDateOptions.canChangeStartDate,
                }
            }]
        }

        // dont go to checkout flow for app version which dont support international payment flows
        if (builderParams.derivedCity.city.countryId !== "IN" && !AppUtil.isInternationalPaymentSupported(userContext)) {
            const action = AppActionUtil.appUpdateAction(userContext)
            action.title = "Get pack"
            return [action]
        }

        return [{
            title: "Get pack",
            actionType: "GET_CULT_PACK",
        }]
    }

    private getPrePurchasePageActions(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams): Action[] {
        // if (!requestParams.selectedSubUserId && requestParams.packInfo.ageCategory === "JUNIOR") {
        //     return [this.userSelectionModalAction(builderParams)]
        // }
        if (!builderParams.startDateOptions.selectedDate) {
            return [{
                title: "Pick a start date",
                actionType: "SHOW_DATE_PICKER",
                meta: {
                    startDate: builderParams.startDateOptions.minEligibleDate,
                    endDate: builderParams.startDateOptions.maxEligibleDate,
                    selectedDate: builderParams.startDateOptions.selectedDate,
                    canChangeStartDate: builderParams.startDateOptions.canChangeStartDate,
                }
            }]
        }

        // dont go to checkout flow for app version which dont support international payment flows
        if (builderParams.derivedCity.city.countryId !== "IN" && !AppUtil.isInternationalPaymentSupported(userContext)) {
            const action = AppActionUtil.appUpdateAction(userContext)
            action.title = "Get pack"
            return [action]
        }
        return [{
            title: "Get pack",
            actionType: "GET_CULT_PACK",
        }]
    }

    private async getPostPurchasePageActionsV2(userContext: UserContext, requestParams: CultPackPageRequestParamsV2, builderParams: CultPackPageBuilderParams | CultPackPageBuilderParamsV2): Promise<Action[]> {
        if (PlayUtil.isPlayWebRequest(userContext, requestParams?.productType)) {
            return  [PlayUtil.getPlayWebBookSlotAction("packDetailPage", builderParams?.preferredCenterId, builderParams?.membership?.metadata?.workoutId)]
        }
        const isNewClassBookingSupported = AppUtil.isNewClassBookingSuppoted(userContext)

        const classBookingAction = ActionUtil.getBookCultClassUrl(builderParams.packInfo.productType, isNewClassBookingSupported, "packDetailPage", undefined)
        return [{
            title: "Book a class",
            url: classBookingAction,
            actionType: "NAVIGATION"
        }]
    }

    private async getPostPurchasePageActions(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams): Promise<Action[]> {
        if (PlayUtil.isPlayWebRequest(userContext, requestParams?.productType)) {
            return  [PlayUtil.getPlayWebBookSlotAction("packDetailPage", builderParams?.preferredCenterId, builderParams?.membership?.metadata?.workoutId)]
        }
        const isNewClassBookingSupported = AppUtil.isNewClassBookingSuppoted(userContext)
        const centerId = builderParams.preferredCenter && builderParams.preferredCenter.id ? builderParams.preferredCenter.id.toString() : undefined
        const classBookingAction = ActionUtil.getBookCultClassUrl(builderParams.packInfo.productType, isNewClassBookingSupported, "packDetailPage", undefined)
        return [{
            title: "Book a class",
            url: classBookingAction,
            actionType: "NAVIGATION"
        }]
    }

    private isRenewEnabled(builderParams: CultPackPageBuilderParams): boolean {
        const cultPackProgress = builderParams.cultPackProgress
        return cultPackProgress && !_.isEmpty(cultPackProgress.expiryMessage)
    }

    private async buildPriceHikeBannerWidget(userContext: UserContext, requestParams: CultPackPageRequestParams | CultPackPageRequestParamsV2): Promise<any> {
        const widgetId = requestParams.productType === "FITNESS" ? CULT_NAS_PRICE_HIKE_BANNER_ID : MIND_NAS_PRICE_HIKE_BANNER_ID
        const widgetResponse = await this.widgetBuilder.buildWidgets([widgetId], this.serviceInterfaces, userContext, undefined, undefined)
        if (!_.isEmpty(widgetResponse) && !_.isEmpty(widgetResponse.widgets)) {
            const widget: any = widgetResponse.widgets[0] && widgetResponse.widgets[0].widgetType === "BANNER_CAROUSEL_WIDGET" ? widgetResponse.widgets[0] : undefined
            // updating layoutprops in same object was causing issue in same widget displaying in other place
            const bannerWidget = clone(widget)
            bannerWidget.layoutProps.backgroundColor = null
            bannerWidget.hasDividerBelow = false
            return bannerWidget
        }
    }
}

@injectable()
export class CultFitPackDetailViewBuilder extends CultPackDetailViewBuilder {

    getDefaultCenterId(derivedCity: DerivedCityDetails): string {
        return derivedCity.defaultCultCenter
    }

    getPreferredCenterId(userContext: UserContext, derivedCity: DerivedCityDetails): string {
        const centerId = userContext.sessionInfo.sessionData.cultCenterId
        return (centerId && centerId !== "undefined" ? centerId : this.getDefaultCenterId(derivedCity))
    }

    getCenterServicePreferredCenterId(userContext: UserContext): string {
        return userContext.sessionInfo.sessionData.centerServiceCultCenterId
    }

    async getOffersPromise(userContext: UserContext, packId: string, preferredCenterId: string): Promise<PackOffersResponse> {
        return undefined
    }

    async getOffersV3Promise(userContext: UserContext, productId: string, preferredCenterId: string): Promise<CultProductPricesResponse> {
        const city = this.cityService.getCityById(userContext.userProfile.cityId)
        const user = await userContext.userPromise
        let centerServiceId
        if (preferredCenterId) {
            centerServiceId = await CultUtil.getCenterServiceIdFromCultCenterId(+preferredCenterId, this.centerService)
        }
        this.logger.info(`offerLog: getOffersV3Promise centerServiceId ${centerServiceId}`)
        return await this.offerServiceV3.getCultPackPrices({
            cultCityId: city.cultCityId,
            cityId: city.cityId,
            userInfo: {
                userId: userContext.userProfile.userId,
                deviceId: userContext.sessionInfo.deviceId,
                email: user.email,
                phone: user.phone,
                workEmail: user.workEmail
            },
            source: AppUtil.callSourceFromContext(userContext),
            centerId: preferredCenterId,
            centerServiceId: centerServiceId ? String(centerServiceId) : undefined,
            productIds: [productId],
        })
    }

    isSourceMembership(membership: CultMembership): boolean {
        return membership.sourceTenant === "cult.fit"
    }

    getBiometricHeader(): string {
        return "One touch access to CULT centres"
    }
}

@injectable()
export class CultMindPackDetailViewBuilder extends CultPackDetailViewBuilder {

    getDefaultCenterId(derivedCity: DerivedCityDetails): string {
        return derivedCity.defaultMindCenter
    }

    getPreferredCenterId(userContext: UserContext, derivedCity: DerivedCityDetails): string {
        const centerId = userContext.sessionInfo.sessionData.mindCenterId
        return (centerId && centerId !== "undefined" ? centerId : this.getDefaultCenterId(derivedCity))
    }

    getCenterServicePreferredCenterId(userContext: UserContext): string {
        return userContext.sessionInfo.sessionData.centerServiceCultCenterId
    }

    async getOffersPromise(userContext: UserContext, packId: string, preferredCenterId: string): Promise<PackOffersResponse> {
        return undefined
    }

    async getOffersV3Promise(userContext: UserContext, productId: string, preferredCenterId: string): Promise<CultProductPricesResponse> {
        const city = this.cityService.getCityById(userContext.userProfile.cityId)
        const user = await userContext.userPromise
        return await this.offerServiceV3.getMindPackPrices({
            cultCityId: city.cultCityId,
            cityId: city.cityId,
            userInfo: {
                userId: userContext.userProfile.userId,
                deviceId: userContext.sessionInfo.deviceId,
                email: user.email,
                phone: user.phone,
                workEmail: user.workEmail
            },
            source: AppUtil.callSourceFromContext(userContext),
            centerId: preferredCenterId,
            productIds: [productId]
        })
    }

    isSourceMembership(membership: CultMembership): boolean {
        return membership.sourceTenant === "mind.fit"
    }

    getBiometricHeader(): string {
        return "One touch access to mind.fit centres"
    }
}
export default CultPackDetailViewBuilder
