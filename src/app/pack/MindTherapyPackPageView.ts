import {
    Action,
    GradientCard,
    Header,
    InfoCard,
    PricingWidgetRecurringValue,
    ProductDetailPage,
    ProductListWidget,
    ProductPricingWidget,
    WidgetView
} from "../common/views/WidgetView"
import { ProductPrice } from "@curefit/product-common"
import { TherapyProduct } from "@curefit/care-common"
import * as _ from "lodash"
import { OfferUtil } from "@curefit/base-utils"
import { BundleSessionSellableProduct, Patient } from "@curefit/albus-client"
import { PackOffersResponse } from "@curefit/offer-common"
import TherapyPageConfig from "../therapy/TherapyPageConfig"
import { RUPEE_SYMBOL } from "@curefit/finance-common"
import { CareUtil } from "../util/CareUtil"

class MindTherapyPackPageView extends ProductDetailPage {
    public pageContext: any

    constructor(isNotLoggedIn: boolean,
        product: TherapyProduct,
        therapyPageConfig: TherapyPageConfig,
        therapyBundleProducts?: BundleSessionSellableProduct[],
        sellableProductId?: string,
        patientsList?: Patient[],
        bundleoffers?: PackOffersResponse) {
        super()
        const offerDetails = OfferUtil.getPackOfferAndPrice(product, bundleoffers)
        const offerIds = _.map(offerDetails.offers, offer => { return offer.offerId })
        this.widgets.push(this.summaryWidget(product, offerDetails.price, offerIds))
        this.widgets.push(this.getProductPricingWidget(therapyBundleProducts, sellableProductId, bundleoffers))
        this.widgets.push(this.getWhyPackWidget(therapyPageConfig))
        this.widgets.push(this.getHowItWorksWidget(therapyPageConfig))
        this.actions = this.getPreBookingActions(isNotLoggedIn, patientsList, therapyBundleProducts, sellableProductId, offerIds)
    }

    private getProductPricingWidget(therapyBundleProducts: BundleSessionSellableProduct[], sellableProductId?: string, bundleoffers?: PackOffersResponse): ProductPricingWidget {
        // todo integrate offers
        const recurringSection: PricingWidgetRecurringValue[] = []
        therapyBundleProducts.forEach(product => {
            const offerDetails = OfferUtil.getPackOfferAndPriceForCare(product, bundleoffers)
            product.listingPrice = offerDetails.price.listingPrice
            const perSessionPrice = Math.floor(product.listingPrice / product.infoSection.numberOfSessions)
            recurringSection.push({
                title: product.productName,
                subTitle: product.duration > 0 && `Valid for ${product.duration} days`,
                price: {
                    mrp: product.mrp,
                    listingPrice: product.listingPrice,
                    showPriceCut: product.listingPrice < product.mrp,
                    currency: offerDetails.price.currency
                },
                priceMeta: `${RUPEE_SYMBOL}${perSessionPrice} per session`, // TODO - per session price after applying offers
                selected: !_.isNil(sellableProductId) && product.productCode === sellableProductId,
                action: {
                    actionType: "NAVIGATION",
                    meta: {
                        sellableProductId: product.productCode
                    }
                }
            })
        })
        return {
            widgetType: "PRODUCT_PRICING_WIDGET",
            header: {
                title: "SELECT A PACK"
            },
            sections: [{
                type: "RECURRING",
                value: recurringSection
            }]
        }
    }

    private summaryWidget(product: TherapyProduct, price: ProductPrice, offerIds?: string[]): WidgetView {
        const therapySummaryWidget: WidgetView & {
            productId: string;
            title: string;
            subTitle: string
            image: string;
            offerIds?: string[],
            hasDividerBelow: boolean
        } = {
            widgetType: "PRODUCT_SUMMARY_WIDGET",
            subTitle: product.subTitle,
            title: product.title,
            productId: product.productId,
            image: product.heroImageUrl,
            offerIds: offerIds,
            hasDividerBelow: false
        }
        return therapySummaryWidget
    }

    private getWhyPackWidget(pageConfig: TherapyPageConfig): ProductListWidget {
        const header: Header = {
            title: pageConfig.whyPackTitle,
            color: "#000000"
        }

        const cards: GradientCard[] = []
        pageConfig.whyPackItemList.forEach(item => {
            cards.push({
                title: item.title,
                subTitle: item.shortDescription,
                shadowColor: item.shadowColor,
                gradientColors: item.gradientColors,
                icon: item.icon
            })
        })
        return new ProductListWidget("GARDIENT_CARD", header, cards)
    }

    private getHowItWorksWidget(pageConfig: TherapyPageConfig): ProductListWidget {
        const header: Header = {
            title: pageConfig.howItWorksTitle,
            color: "#000000"
        }
        const infoCards: InfoCard[] = []
        pageConfig.howItWorksItemList.forEach(item => {
            infoCards.push({
                subTitle: item.text,
                icon: item.icon
            })
        })
        const widget = new ProductListWidget("SMALL", header, infoCards)
        widget.hasDividerBelow = false
        return widget
    }

    private getPreBookingActions(isNotLoggedIn: boolean, patientsList: Patient[], therapyBundleProducts: BundleSessionSellableProduct[], sellableProductId: string, offerIds: string[]): Action[] {
        if (isNotLoggedIn === true) {
            return [
                {
                    actionType: "SHOW_ALERT_MODAL",
                    title: "Get Therapy Pack",
                    meta: {
                        title: "Login Required!",
                        subTitle: "Please login to continue",
                        actions: [{ actionType: "LOGOUT", title: "Login" }]
                    }
                }
            ]
        } else {
            const sellableProduct = _.find(therapyBundleProducts, bundleProduct => {
                return bundleProduct.productCode === sellableProductId
            })
            const actionTitle = `Buy ${sellableProduct.productName}`
            let actionString = `curefit://carecartcheckout?productId=${sellableProductId}&subCategoryCode=${sellableProduct?.subCategoryCode}`
            if (!_.isEmpty(offerIds)) {
                actionString += `&offerIds=${offerIds.join(",")}`
            }
            const action: Action = {
                actionType: "NAVIGATION",
                url: actionString
            }
            const calloutText = "Our Therapy packs are available only for yourself. This ensures data security and privacy."
            const pageAction = CareUtil.getSelfPatientSelectionModalAction(patientsList, action, actionTitle, calloutText, { formUserType: "THERAPY_USER" })
            return [pageAction]
        }

    }
}

export default MindTherapyPackPageView
