import { injectable, inject } from "inversify"
import * as _ from "lodash"
import { CFLiveMembershipType, CFLiveProduct, ImageData } from "@curefit/diy-common"
import { AssetDetails, LiveMembershipPacksPage, LivePackPickerWidget } from "../digital/ILivePackPage"
import { ImageOverlayCardContainerWidget, HeaderWidget } from "@curefit/apps-common"
import { ProductListWidget } from "../common/views/WidgetView"
import { BannerCarouselWidget } from "../page/PageWidgets"
import { UserContext } from "@curefit/userinfo-common"
import { LivePackUtil } from "../util/LivePackUtil"
import AppUtil from "../util/AppUtil"
import { LivePricesResponse, OfferV2 } from "@curefit/offer-common"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import WidgetBuilder from "../page/vm/WidgetBuilder"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { BASE_TYPES, Logger } from "@curefit/base"
import { OFFER_SERVICE_CLIENT_TYPES } from "@curefit/offer-service-client"
import { OfferUtil as OfferServiceUtil } from "@curefit/offer-service-client/dist/src/client/OfferUtil"
import { CareUtil } from "../util/CareUtil"
import { Orientation } from "@curefit/vm-common"

const PACK_PRICE_BANNER_ID = "c734b800-0396-499c-b841-f07a0f74da9e"

@injectable()
class LivePacksViewBuilder {

    LIVE_PACK_BENEFITS_IOS_INTL = "/image/livefit/app/live_pack_benefits_ios_intl.jpg"
    LIVE_PACK_BENEFITS_ANDROID_INTL = "/image/livefit/app/live_pack_benefits_android_intl.jpg"
    LIVE_PACK_COVER_IMAGE_INTL = "image/livefit/app/live_pack_cover_image_intl_v1.jpg"
    LIVE_PACK_BENEFITS = "/image/livefit/app/mobile_live_pack_benefits.png"
    LIVE_PACK_BENEFITS_WEB = "/image/livefit/app/web_benefits_live_pack.png"
    LIVE_IOS_PACK_BANNER = "image/livefit/app/ios_info_banner.png"
    LIVE_IOS_PACK_BANNER_TRIAL_EXPIRED = "image/livefit/app/live_ios_pack_banner_new.png"
    LIVE_TRIAL_PACK_BANNER_APP = "image/livefit/app/others_info_banner.png"
    LIVE_TRIAL_PACK_BANNER_WEB = "image/livefit/app/trial_pack_banner_web_change_1.png"
    LIVE_PACK_AUTO_RENEW_WITH_TRIAL = "image/livefit/app/live_pack_auto_renew_w_trial_v2.png"
    LIVE_PACK_AUTO_RENEW_WITHOUT_TRIAL = "image/livefit/app/live_pack_auto_renew_intl_v1.png"
    LIVE_PACK_AUTO_RENEW_INTL = "image/livefit/app/live_pack_auto_renew_intl_v1.png"

    constructor(
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(CUREFIT_API_TYPES.WidgetBuilder) public widgetBuilder: WidgetBuilder,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferUtil) protected offerUtil: OfferServiceUtil,
    ) {
    }

    public async getLivePacksView(userContext: UserContext, cfLivePacks: CFLiveProduct[],
        livePricesResponse: LivePricesResponse, offerIdOfferMap: { [offerId: string]: OfferV2 }, isMonetisationEnabled: boolean, productId: string, showTrialBanner: boolean, bucketId: string): Promise<LiveMembershipPacksPage> {
        const liveMembershipPacksPage = new LiveMembershipPacksPage()
        liveMembershipPacksPage.packs = cfLivePacks

        const sessionInfo = userContext.sessionInfo
        const isWeb = AppUtil.isWebUserAgent(sessionInfo.userAgent)
        const isDesktop = isWeb && !AppUtil.isMWeb(userContext)
        const image = await this.getBannerImage(userContext)
        let orientation: Orientation
        orientation = isWeb ? orientation = "RIGHT" : orientation = "TOP"
        if (!isWeb) {
            const imageOverlayWidget = this.getImageOverlayWidget(userContext, image)
            liveMembershipPacksPage.widgets.push(imageOverlayWidget)
        }

        const isIOS = sessionInfo.osName === "ios"
        const isPartOfGstSegment = await AppUtil.doesUserBelongToGSTsplitExperiment(userContext, this.serviceInterfaces)
        let livePriceBreakupResponse
        if ( isPartOfGstSegment ) livePriceBreakupResponse = await this.offerUtil.getTaxBreakUpForCareAndLivePacks(livePricesResponse)
        const { livePackPickerWidget, selectedProductId, selectedPackAction } = LivePackUtil.getLivePackPickerWidget(userContext, cfLivePacks, sessionInfo.osName, livePricesResponse, offerIdOfferMap, isMonetisationEnabled, "live_pack_purchase_page", productId, null, null, bucketId, livePriceBreakupResponse, isPartOfGstSegment)
        livePackPickerWidget.rootStyle = {
            marginTop: -40,
            marginBottom: 15
        }


        liveMembershipPacksPage.widgets.push(livePackPickerWidget)
        liveMembershipPacksPage.widgets.push(CareUtil.getBillingAddress(orientation, true))
        liveMembershipPacksPage.selectedProductId = selectedProductId
        liveMembershipPacksPage.pageAction = selectedPackAction
        const isIAPEnabled = AppUtil.isIAPEnabled(userContext)
        if (!isIAPEnabled && showTrialBanner) {
            const bannerUrl = isDesktop ? this.LIVE_TRIAL_PACK_BANNER_WEB : this.LIVE_TRIAL_PACK_BANNER_APP
            const width = isDesktop ? 964 : 334
            const height = isDesktop ? 216 : 75
            const bannerCarouselWidget: BannerCarouselWidget = LivePackUtil.getSingleBannerCarouselWidget(bannerUrl, width, height)
            bannerCarouselWidget.layoutProps = {
                verticalPadding: 0,
                bannerWidth: width,
                bannerHeight: height,
                v2: true,
            }
            liveMembershipPacksPage.widgets.push(bannerCarouselWidget)
        }

        if (isIAPEnabled) {
            const trialPack = cfLivePacks.find(livePack => livePack.membershipType === CFLiveMembershipType.TRIAL)
            const indiaAppBanner = trialPack ? this.LIVE_PACK_AUTO_RENEW_WITH_TRIAL : this.LIVE_PACK_AUTO_RENEW_WITHOUT_TRIAL
            const bannerUrl = AppUtil.isInternationalApp(userContext) ? this.LIVE_PACK_AUTO_RENEW_INTL : indiaAppBanner
            const width = 334
            const height = trialPack ? 110 : 63
            const bannerCarouselWidget: BannerCarouselWidget = LivePackUtil.getSingleBannerCarouselWidget(bannerUrl, width, height)
            bannerCarouselWidget.layoutProps = {
                verticalPadding: 0,
                bannerWidth: width,
                bannerHeight: height,
                v2: true,
            }
            liveMembershipPacksPage.widgets.push(bannerCarouselWidget)
        }

        const intlLiveBenefitsImage = userContext.sessionInfo.osName === "ios" ? this.LIVE_PACK_BENEFITS_IOS_INTL : this.LIVE_PACK_BENEFITS_ANDROID_INTL
        const liveBenefitsImage = AppUtil.isInternationalApp(userContext) ? intlLiveBenefitsImage : this.LIVE_PACK_BENEFITS
        // benefits image
        liveMembershipPacksPage.widgets.push(this.getBannerCarouselWidget(isDesktop ? this.LIVE_PACK_BENEFITS_WEB : liveBenefitsImage, isDesktop))
        if (!AppUtil.isInternationalApp(userContext)) {
            liveMembershipPacksPage.widgets.push(LivePackUtil.getFAQWidget(userContext))
        }
        liveMembershipPacksPage.widgets.push(this.getHowItWorksWidget())
        if (!isIOS) {
            const styles = {
                marginVertical: 0,
                paddingVertical: 15,
                paddingBottom: 20
            }
            liveMembershipPacksPage.widgets.push(LivePackUtil.getTermsNConditionsWidget(isIOS, styles))
            liveMembershipPacksPage.widgets.push(LivePackUtil.getTnCWidget(userContext))
        }
        liveMembershipPacksPage.bannerImages = this.getImageData(userContext, image)
        liveMembershipPacksPage.assets = this.getAssetDetails(userContext, image)

        return liveMembershipPacksPage
    }

    getAssetDetails(userContext: UserContext, image: string): AssetDetails[] {
        let assetUrl = image
        if (_.isEmpty(image) || image.includes("undefined"))
            assetUrl = this.getCoverImageUrl(userContext)
        return [
            {
                type: "VIDEO",
                mediaUrl: "https://cdn-media.cure.fit/video/cflive_pack_page_mock_v2_revised.mp4",
                assetUrl,
                thumbnailUrl: assetUrl
            }
        ]
    }

    getImageData(userContext: UserContext, image: string): ImageData {
        let url = image
        if (_.isEmpty(image) || image.includes("undefined"))
            url = this.getCoverImageUrl(userContext)
        return {
            mobileImage: url,
            mwebImage: url,
            webImage: url,
            webProductImage: url,
            webImageLarge: url
        }
    }

    getCoverImageUrl(userContext: UserContext) {
        return AppUtil.isInternationalApp(userContext) ? this.LIVE_PACK_COVER_IMAGE_INTL : "image/livefit/app/live_pack_purchase_banner.png"
    }

    getHeaderWidget(): HeaderWidget {
        const headerWidget = new HeaderWidget({ title: "What you get" })
        headerWidget.headerStyle = { color: "#000000", fontSize: 18, fontFamily: "BrandonText-Bold", marginLeft: 0, lineHeight: 33 }
        headerWidget.style = { marginTop: 20, marginHorizontal: 18 }
        return headerWidget
    }

    getBannerCarouselWidget(imageUrl: string, isDesktop: boolean) {
        return new BannerCarouselWidget("375:768", [{
            id: imageUrl,
            image: imageUrl,
            action: undefined
        }], {
            bannerHeight: isDesktop ? 1036 : 768,
            bannerWidth: isDesktop ? 750 : 375,
            backgroundColor: undefined
        }, 1, false, true, false)
    }

    getImageOverlayWidget(userContext: UserContext, image: string) {
        const imageOverlayContainerWidget = new ImageOverlayCardContainerWidget()
        imageOverlayContainerWidget.assets.push({
            assetType: "IMAGE",
            assetUrl: image || this.getCoverImageUrl(userContext)
        })
        return imageOverlayContainerWidget
    }

    private getHowItWorksWidget(): ProductListWidget {
        const hiwWidget: ProductListWidget = {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "SMALL",
            header: {
                title: "How it works"
            },
            hideSepratorLines: false,
            items: [
                {
                    "subTitle": "Sign up for a Live at-home session",
                    "icon": "/image/icons/howItWorks/hitButton_1.png"
                },
                {
                    "subTitle": "Pick a comfortable location at your home for the workout",
                    "icon": "/image/icons/howItWorks/cast.png"
                },
                {
                    "subTitle": "Open app/web and join the session at least 2 minutes prior to the start time",
                    "icon": "/image/icons/howItWorks/time.png"
                },
                {
                    "subTitle": "Follow instructions to set up your mobile/laptop device",
                    "icon": "/image/icons/howItWorks/info.png"
                },
                {
                    "subTitle": "Voila! Now enjoy the awesome LIVE experience",
                    "icon": "/image/icons/howItWorks/<EMAIL>"
                }
            ]
        }
        return hiwWidget
    }

    private async getBannerImage(userContext: UserContext): Promise<any> {
        const widgetId = PACK_PRICE_BANNER_ID
        const widgetResponse = await this.widgetBuilder.buildWidgets([widgetId], this.serviceInterfaces, userContext, undefined, undefined)
        if (!_.isEmpty(widgetResponse) && !_.isEmpty(widgetResponse.widgets)) {
            const widget: any = widgetResponse.widgets[0] && (widgetResponse?.widgets[0]?.widgetType === "BANNER_CAROUSEL_WIDGET" || widgetResponse?.widgets[0]?.widgetType === "BANNER_ACTION_WIDGET") ? widgetResponse.widgets[0] : undefined
            return widget?.data[0]?.image
        }
    }
}

export default LivePacksViewBuilder
