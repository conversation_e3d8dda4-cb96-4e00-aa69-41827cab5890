import { inject, injectable } from "inversify"
import { ICatalogueService, CATALOG_CLIENT_TYPES } from "@curefit/catalog-client"
import { FitnessFirstPack } from "@curefit/cult-common"
import { ProductTaxAndMargin } from "@curefit/product-common"
import { PackOffersResponse } from "@curefit/offer-common"
import {
    Action,
    ActionCard,
    DatePickerWidget,
    DescriptionWidget,
    getOffersWidget,
    InfoCard,
    ProductDetailPage,
    ProductListWidget,
    WidgetView
} from "../common/views/WidgetView"
import { Header } from "@curefit/vm-models"
import { ImageCategoryType as ImageCategory, ProductPrice, ProductType, UrlPathBuilder } from "@curefit/product-common"
import FitnessFirstPackPageConfig from "./FitnessFirstPackPageConfig"
import * as _ from "lodash"
import { OfferUtil } from "@curefit/base-utils"
import { ITaxService, PRODUCT_SERVICE_TYPES } from "@curefit/product-service"
import { TimeUtil } from "@curefit/util-common"
import { UserContext } from "@curefit/userinfo-common"

@injectable()
class FitnessFirstPackDetailViewBuilder {
    constructor(@inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) protected catalogueService: ICatalogueService,
        @inject(PRODUCT_SERVICE_TYPES.TaxService) private taxService: ITaxService
    ) {

    }

    async getView(userContext: UserContext, offerResponse: PackOffersResponse, packDetails: FitnessFirstPack, fitnessfirstpackPageConfig: FitnessFirstPackPageConfig): Promise<ProductDetailPage> {
        const productDetailPage: ProductDetailPage = new ProductDetailPage()
        const pageAction = this.getPageAction(packDetails)
        productDetailPage.pageAction = pageAction.action
        productDetailPage.actions = pageAction.actions
        const offerItem = offerResponse ? offerResponse[packDetails.productId] : null
        const startDateOptions = this.getStartDateOptions(userContext)
        productDetailPage.widgets.push(await this.getSummaryWidget(userContext, packDetails, offerResponse, startDateOptions.canChangeStartDate, startDateOptions.startDate))
        productDetailPage.widgets.push(this.getStartDateWidget(userContext, startDateOptions.startDate, startDateOptions.canChangeStartDate))
        if (offerItem && offerItem.offers.length > 0) {
            productDetailPage.widgets.push(getOffersWidget("Offer", offerItem.offers))
        }
        productDetailPage.widgets.push(this.getDescriptionWidget(packDetails))
        productDetailPage.widgets.push(this.getHowItWorksWidget(fitnessfirstpackPageConfig))
        return productDetailPage
    }

    private getDescriptionWidget(packInfo: FitnessFirstPack): DescriptionWidget {
        const infoCards: InfoCard[] = []
        let subTitle: string
        subTitle = packInfo.subTitle
        const infoCard: InfoCard = {
            title: "About this pack",
            subTitle: subTitle
        }

        infoCards.push(infoCard)
        return new DescriptionWidget(infoCards)
    }

    private getStartDateOptions(userContext: UserContext) {
        const tz = userContext.userProfile.timezone
        const dates = TimeUtil.getDaysFrom(tz, TimeUtil.todaysDate(tz), 2)
        let startDate: string = ""
        if (dates.length === 2) {
            startDate = dates[1]
        }
        return { startDate, canChangeStartDate: true }
    }

    private getStartDateWidget(userContext: UserContext, startDate: string, canChangeStartDate: boolean): DatePickerWidget {
        let endDate
        const tz = userContext.userProfile.timezone
        if (canChangeStartDate) {
            const allowedDates = TimeUtil.getDaysFrom(tz, startDate, 45, false)
            endDate = allowedDates[allowedDates.length - 1]
        } else {
            endDate = startDate
        }
        const datePickerWidget: DatePickerWidget = {
            startDate: startDate,
            endDate: endDate,
            selectedDate: startDate,
            canChangeStartDate: canChangeStartDate,
            widgetType: "DATE_PICKER_WIDGET"
        }
        return datePickerWidget
    }

    private async getSummaryWidget(userContext: UserContext, packInfo: FitnessFirstPack, offerResponse: PackOffersResponse, canChangeStartDate: boolean, startDate: string): Promise<WidgetView> {
        const category: ImageCategory = userContext.sessionInfo.userAgent === "DESKTOP" ? "MAGAZINE" : "HERO"
        let withoutTaxPrice = 0
        const summaryWidget: WidgetView & {
            packId: string,
            productType: ProductType,
            image: string,
            productId: string
            title: string,
            duration: number,
            price: ProductPrice,
            offerId?: string,
            offerIds?: string[],
            taxAndMargin?: ProductTaxAndMargin,
            startDateOptions?: string[]
        } = {
            widgetType: "FITNESS_PACK_SUMMARY",
            packId: packInfo.productId,
            productType: packInfo.productType,
            image: UrlPathBuilder.getPackImagePath(packInfo.productId, packInfo.productType, category, packInfo.imageVersion, userContext.sessionInfo.userAgent),
            productId: packInfo.productId,
            title: packInfo.title,
            duration: packInfo.duration,
            price: packInfo.price,
            taxAndMargin: packInfo.taxAndMargin
        }
        const result = this.getPackPriceAndOfferId(packInfo, offerResponse)
        summaryWidget.price = result.price
        const totalTaxes = this.taxService.getTotalTaxPercent(packInfo.taxAndMargin)
        if (packInfo.taxAndMargin && (totalTaxes > 0)) {
            withoutTaxPrice = (summaryWidget.price.listingPrice * 100) / (100 + totalTaxes)
            summaryWidget.price.listingPrice = Math.ceil(withoutTaxPrice)
        }
        summaryWidget.offerId = !_.isEmpty(result.offerIds) ? result.offerIds[0] : undefined
        if (startDate) {
            summaryWidget.startDateOptions = canChangeStartDate ? TimeUtil.getDaysFrom(userContext.userProfile.timezone, startDate, 7, false) : [startDate]
        }
        summaryWidget.offerIds = result.offerIds
        return summaryWidget
    }

    private getPackPriceAndOfferId(ffPack: FitnessFirstPack, packOffersResponse: PackOffersResponse): { price: ProductPrice, offerIds?: string[] } {
        const offerDetails = OfferUtil.getPackOfferAndPrice(ffPack, packOffersResponse)
        const offerIds = offerDetails.offers?.map(offer => { return offer.offerId }) || []
        return {
            price: offerDetails.price,
            offerIds: offerIds
        }
    }

    private getHowItWorksWidget(fitnessfirstpackPageConfig: FitnessFirstPackPageConfig): ProductListWidget {

        try {
            const header: Header = {
                title: fitnessfirstpackPageConfig.howItWorksTitle
            }

            const infoCards: InfoCard[] = []
            const howItWorksItemList = fitnessfirstpackPageConfig.howItWorksItemList
            howItWorksItemList.forEach(item => {
                infoCards.push({
                    subTitle: item.text,
                    icon: item.icon
                })
            })
            return new ProductListWidget("SMALL", header, infoCards)
        }
        catch (e) {
            return undefined
        }
    }

    private getPageAction(packDetails: FitnessFirstPack): {
        action: ActionCard,
        actions: Action[],
        isBuyEnabled: boolean
    } {
        if (packDetails.isActive && packDetails.isPublished) {
            return {
                action: {
                    title: "Get pack",
                    action: "GET_CULT_PACK"
                },
                actions: [{
                    title: "Get pack",
                    actionType: "GET_CULT_PACK",
                }],
                isBuyEnabled: true
            }
        } else {
            return {
                action: {
                    title: "Sold out",
                    action: "POP_ACTION"
                },
                actions: [{
                    title: "Sold out",
                    actionType: "POP_ACTION"
                }],
                isBuyEnabled: false
            }
        }
    }
}

export default FitnessFirstPackDetailViewBuilder
