import { injectable, inject } from "inversify"
import { UserContext } from "@curefit/userinfo-common"
import { PauseInfo, PausePackInfo, ProductListWidget } from "../common/views/WidgetView"
import { TimeUtil, Timezone } from "@curefit/util-common"
import AppUtil from "../util/AppUtil"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import { Membership } from "@curefit/membership-commons"
import GymfitUtil from "../util/GymfitUtil"
import { ErrorCodes } from "../error/ErrorCodes"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { ErrorFactory } from "@curefit/error-client"


@injectable()
export default class PlayPausePackViewBuilder {
    constructor(
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) public membershipService: IMembershipService,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
    ) {
    }

    async getPausePackInfo(membershipId: number, userContext: UserContext, pauseStartDate: string, pauseEndDate: string, isEdit: boolean): Promise<PausePackInfo> {
        const membership = await this.membershipService.getMembershipById(membershipId)
        const userId = userContext.userProfile.userId
        if (membership.userId != userId) {
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 401).withDebugMessage("Operation not allowed").build()
        }
        const tz = userContext.userProfile.timezone
        const cultBookings: any[] = []
        const cultPausePackInfo = new PausePackInfo()
        let pauseStartDateFormatted
        let pauseEndDateFormatted = undefined
        if (pauseStartDate) {
            pauseStartDateFormatted = TimeUtil.formatDateStringInTimeZone(pauseStartDate, tz, "YYYY-MM-DD hh:mm A")
        } else if (isEdit && membership.activePause) {
            pauseStartDateFormatted = TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.activePause.start, "yyyy-MM-dd hh:mm a")
            pauseEndDateFormatted = TimeUtil.getMomentForDateString(TimeUtil.addDays(tz, TimeUtil.formatEpochInTimeZone(tz, membership.activePause.end), 1), tz).subtract(1, "minutes").format("YYYY-MM-DD hh:mm A")
        } else {
            pauseStartDateFormatted = TimeUtil.formatDateStringInTimeZone(TimeUtil.addDays(tz, TimeUtil.todaysDate(tz), 1), tz, "YYYY-MM-DD hh:mm A")
        }
        const previousPauseEndDate = membership.activePause ? TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.activePause.end, "yyyy-MM-dd hh:mm a") : null
        cultPausePackInfo.membershipInfo = {
            membershipId: membership.id,
            pauseMaxDays: GymfitUtil.convertMillisToDays(membership.maxPauseDuration),
            remainingPauseDays: GymfitUtil.convertMillisToDays(membership.remainingPauseDuration),
            startDateParam: {
                date: pauseStartDateFormatted,
                limit: TimeUtil.addDays(tz, TimeUtil.todaysDate(tz), 1),
                canEdit: false
            },
            endDate: pauseEndDateFormatted,
            pauseInfo: this.getPauseInfo(tz, pauseStartDateFormatted, pauseEndDate ? pauseEndDate : pauseStartDateFormatted, previousPauseEndDate, GymfitUtil.convertMillisToDays(membership.remainingPauseDuration), TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.end, "yyyy-MM-dd"), true)
        }
        const howItWorksWidget = this.getHowItWorksWidget(membership)
        cultPausePackInfo.widgets = [howItWorksWidget]
        cultPausePackInfo.existingBookings = {
            action: {
                positiveActionText: "DON'T PAUSE",
                negativeActionText: "PAUSE"
            },
            confirmationTitle: "Are you sure you want to pause this pack?",
            confirmationDescription: "All your upcoming classes starting tomorrow will be auto-cancelled when you pause your pack",
            cultBookings
        }
        cultPausePackInfo.showPauseProgressBar = !isEdit
        return cultPausePackInfo
    }

    private getPauseInfo(tz: Timezone, pauseStartDate: string, pauseEndDate: string, previousPauseEndDate: string, remainingPauseDays: number, packEndDate: string, shouldAddExtendsOn: boolean): PauseInfo[] {
        // Extra day added since pause starts from next day's 12AM
        const splitPauseEndDate = pauseEndDate.split(" ")
        const strippedPauseEndDate = splitPauseEndDate.length ? splitPauseEndDate[0] : pauseEndDate
        const splitPauseStartDate = pauseStartDate.split(" ")
        const strippedPauseStartDate = splitPauseStartDate.length ? splitPauseStartDate[0] : pauseStartDate
        const pauseDays = TimeUtil.diffInDays(tz, strippedPauseStartDate, strippedPauseEndDate) + 1
        const changeInPauseDays = TimeUtil.diffInDays(tz, previousPauseEndDate, pauseEndDate)
        const endsOn = ( previousPauseEndDate !== null ) ?
            TimeUtil.formatDateInTimeZone(tz, new Date(TimeUtil.addDays(tz, packEndDate, changeInPauseDays)), "DD MMM YYYY")
            : TimeUtil.formatDateInTimeZone(tz, new Date(TimeUtil.addDays(tz, packEndDate, pauseDays)), "DD MMM YYYY")
        const pauseInfo = [
            {title: "Pause days used in this duration", value: AppUtil.appendDays(pauseDays)}
        ]
        if (shouldAddExtendsOn) {
            pauseInfo.push({title: "Membership will now end on", value: endsOn})
        }
        pauseInfo.push({title: "Pause days left", value: AppUtil.appendDays(remainingPauseDays - pauseDays)})
        return pauseInfo
    }

    private getHowItWorksWidget(membership: Membership): ProductListWidget {
        const maxPauseDays = GymfitUtil.convertMillisToDays(membership.remainingPauseDuration)
        const items = [
            {
                subTitle: `You can pause your pack as many times as you like until you reach ${maxPauseDays} pause days`,
                icon: "/image/icons/howItWorks/pause_3.png"
            },
            {
                subTitle: "You will not be able to book classes when in pause",
                icon: "/image/icons/howItWorks/noSlot.png"
            }
        ]
        items.push({
            subTitle: "Membership will get extended basis paused days used and pack end date would be updated accordingly",
            icon: "/image/icons/howItWorks/plus.png"
        })
        items.push({
            subTitle: "You can also manually unpause your membership before the specified date",
            icon: "/image/icons/howItWorks/resume.png"
        })
        const widget = new ProductListWidget(
            "SMALL",
            {title: "How it works"},
            items
        )
        widget.hideSepratorLines = true
        return widget
    }
}
