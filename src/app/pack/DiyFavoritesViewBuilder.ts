import { inject, injectable } from "inversify"
import { UserContext } from "@curefit/vm-models"
import {
    DIYFitnessProductExtended,
    DIYPack,
    DIYProduct,
    DIYUserBookmarks,
    DIYUserBookmarksProductsResponse
} from "@curefit/diy-common"
import { DiySeriesActionCard } from "./SeriesDetailViewV1"
import { ActionUtil, ActionUtilV1, SeoUrlParams } from "@curefit/base-utils"
import { Product, ProductType, UrlPathBuilder } from "@curefit/product-common"
import CultUtil from "../util/CultUtil"
import { pad, pluralizeStringIfRequired } from "@curefit/util-common"
import { Action, ProductListWidget } from "../common/views/WidgetView"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService } from "@curefit/diy-client"
import { LivePackUtil } from "../util/LivePackUtil"
import * as momentTz from "moment-timezone"
import AtlasUtil, { DiyContentDetail, DiyContentMeta } from "../util/AtlasUtil"
import { DiySessionActionCard } from "./ContentPackDetailViewV2"
import * as _ from "lodash"
import LiveUtil from "../util/LiveUtil"
import { FavoriteEmptyViewWidget, TabProductListWidget, WidgetView } from "@curefit/apps-common"
import AppUtil from "../util/AppUtil"
import { IMaxmindService, MAXMIND_CLIENT_TYPES } from "@curefit/maxmind-client"
import { BASE_TYPES, ILogger } from "@curefit/base"

@injectable()
class DiyFavoritesViewBuilder {

    constructor(
        @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) private diyFulfilmentService: IDIYFulfilmentService,
        @inject(MAXMIND_CLIENT_TYPES.IMaxmindService) private maxmindService: IMaxmindService,
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
    ) {
    }

    async getView(userContext: UserContext, productType: ProductType, isUserEligibleForMonetisation: boolean, isUserEligibleForLivePackTrial: boolean, bucketId: string): Promise<{ widgets: WidgetView[] }> {
        const userId = userContext.userProfile.userId
        const blockInternationalUser = await AppUtil.blockFitnessContentForInternationalUser(userContext, this.maxmindService, this.logger)
        let packBookmarks
        let sessionBookMarks
        switch (productType) {
            case "DIY_FITNESS":
            case "DIY_FITNESS_PACK":
                packBookmarks = await this.diyFulfilmentService.getDIYProductBookmarks(userId, "DIY_FITNESS_PACK")
                sessionBookMarks = await this.diyFulfilmentService.getDIYUserBookmarksForProducts(userId, "DIY_FITNESS")
                return await this.getDiyFitnessAndMindFavourites(userContext, packBookmarks, sessionBookMarks, "fitness", isUserEligibleForMonetisation, isUserEligibleForLivePackTrial, bucketId, blockInternationalUser)
            case "DIY_MEDITATION":
            case "DIY_MEDITATION_PACK":
                packBookmarks = await this.diyFulfilmentService.getDIYProductBookmarks(userId, "DIY_MEDITATION_PACK")
                sessionBookMarks = await this.diyFulfilmentService.getDIYUserBookmarksForProducts(userId, "DIY_MEDITATION")
                return await this.getDiyFitnessAndMindFavourites(userContext, packBookmarks, sessionBookMarks, "meditation", isUserEligibleForMonetisation, isUserEligibleForLivePackTrial, bucketId, false)
            default:
                return { widgets: [] }
        }
    }

    private async getDiyFitnessAndMindFavourites(userContext: UserContext, packBookmarks: DIYUserBookmarks, sessionBookmarks: DIYUserBookmarksProductsResponse, product: string, isUserEligibleForMonetisation: boolean, isUserEligibleForLivePackTrial: boolean, bucketId: string, blockInternationalUser: boolean): Promise<{ widgets: WidgetView[] }> {
        const widgets: WidgetView[] = []
        const packActionCards: DiySeriesActionCard[] = []
        const sessionActionCards: DiySessionActionCard[] = []
        const productMaps = new Map<string, { [productId: string]: Product; }>()
        const packEmptyView = this.getEmptyView(product === "fitness" ? "DIY_FITNESS_PACK" : "DIY_MEDITATION_PACK")
        const sessionEmptyView = this.getEmptyView(product === "fitness" ? "DIY_FITNESS" : "DIY_MEDITATION")
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        const isInternationalApp = AppUtil.isInternationalApp(userContext)
        let packProductListWidget: ProductListWidget
        let sessionProductListWidget: ProductListWidget
        if (!_.isEmpty(packBookmarks.productIds)) {
            for (const packId of packBookmarks.productIds) {
                const pack = product === "fitness" ? (await this.diyFulfilmentService.getDIYFitnessPacksForIds(userContext.userProfile.userId, [packId]))[0] :
                    (await this.diyFulfilmentService.getDIYMeditationPacksForIds(userContext.userProfile.userId, [packId]))[0]
                const products = pack.productType === "DIY_FITNESS_PACK" ? await this.diyFulfilmentService.getDIYFitnessProductsByProductIds(userContext.userProfile.userId, pack.sessionIds, tenant)
                    : await this.diyFulfilmentService.getDIYMeditationProductsByProductIds(userContext.userProfile.userId, pack.sessionIds, tenant)
                const productMap = LivePackUtil.getProductMap(products)
                productMaps.set(pack.productId, productMap)
                let duration = 0
                for (let j = 0; j < pack.sessionIds.length; j++) {
                    const session = <DIYProduct>productMap[pack.sessionIds[j]]
                    duration = duration + (session.duration / 1000) // duration is in seconds
                }
                const nextSessionIdx = 0
                const nextSession = <DIYProduct>productMap[pack.sessionIds[nextSessionIdx]]
                const content = AtlasUtil.getContentDetailV2(nextSession)
                const meta = AtlasUtil.getContentMetaV2(nextSession, pack)
                const nextSessionAction = (content.type === "audio" ? ActionUtil.audioUrl(content.URL, UrlPathBuilder.prefixSlash(pack.imageDetails.heroImage), meta) : ActionUtil.videoUrl(content.URL, UrlPathBuilder.prefixSlash(pack.imageDetails.heroImage), meta))
                const moreAction = CultUtil.getDiyShareAction(pack.productId, null, pack.productType, false, pack.title)
                const packIntroAction = pack.packIntroContentId ? this.getIntroAction(ActionUtil.videoUrl("curefit-content/video/" + pack.packIntroContentId + ".mp4", UrlPathBuilder.prefixSlash(pack.imageDetails.heroImage)) + `&contentId=${pack.packIntroContentId}`) :
                    this.getSessionPlayAction(content, meta)
                packActionCards.push({
                    title: `${pack.sessionIds.length} ${pluralizeStringIfRequired("session", pack.sessionIds.length)}`,
                    subTitle: `${pad(Math.round(duration / (pack.sessionIds.length * 60)), 2)} min`,
                    description: pack.description,
                    image: UrlPathBuilder.prefixSlash(pack.imageDetails.magazineImage),
                    action: ActionUtilV1.diyPackProductPage(pack, userContext.sessionInfo.userAgent),
                    series: true,
                    isCompleted: false,
                    isActive: true,
                    moreAction,
                    isLocked: isInternationalApp && AppUtil.isLiveContentLocked(userContext, nextSession.locked, isUserEligibleForMonetisation, isUserEligibleForLivePackTrial),
                    favAction: LiveUtil.getDIYFavAction(userContext, pack.productId, pack.productType, true),
                    packProgress: {
                        total: pack.sessionIds.length,
                        completed: 0,
                        state: "NOT_SUBCSCRIBED",
                        type: pack.productType === "DIY_FITNESS_PACK" ? "DIY_FITNESS" : "DIY_MEDITATION",
                        noPadding: true,
                        resumeAction: nextSessionAction,
                        resumeNewAction: LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, nextSession.locked, packIntroAction, isUserEligibleForLivePackTrial, pack.productType, "diy_favorite_pack_widget", bucketId, blockInternationalUser, nextSession.format)
                    },
                })
            }
            packProductListWidget = new ProductListWidget("MAGAZINE_OFFER", undefined, packActionCards)
        }
        if (!_.isEmpty(sessionBookmarks.results)) {
            for (const bookmark of sessionBookmarks.results) {
                const products = product === "fitness" ? await this.diyFulfilmentService.getDIYFitnessProductsByProductIds(userContext.userProfile.userId, [bookmark.productId], AppUtil.getTenantFromUserContext(userContext)) :
                    await this.diyFulfilmentService.getDIYMeditationProductsByProductIds(userContext.userProfile.userId, [bookmark.productId], AppUtil.getTenantFromUserContext(userContext))
                const packs = product === "fitness" ? await this.diyFulfilmentService.getDIYFitnessPacksForIds(userContext.userProfile.userId, [bookmark.packId]) :
                    await this.diyFulfilmentService.getDIYMeditationPacksForIds(userContext.userProfile.userId, [bookmark.packId])
                const session = products[0]
                const description = session.productType === "DIY_FITNESS" ? this.getExercisesDescription(<DIYFitnessProductExtended>session) : undefined
                const duration = momentTz.duration(typeof session.duration === "string" ? parseInt(session.duration) : session.duration).humanize()
                const isLocked = session.locked === undefined ? isUserEligibleForMonetisation : (session.locked && isUserEligibleForMonetisation)
                sessionActionCards.push({
                    title: session.title,
                    subTitle: duration,
                    description: description,
                    image: UrlPathBuilder.prefixSlash(session.imageDetails.thumbnailImage),
                    cardAction: this.getSessionWidgetItemAction(session, packs[0], isUserEligibleForLivePackTrial, isLocked, userContext, isUserEligibleForMonetisation, bucketId, blockInternationalUser),
                    moreAction: this.getShareAction(packs[0]?.productId, bookmark.productId, session.productType, packs[0]?.title, session.title),
                    content: AtlasUtil.getContentDetailV2(session),
                    meta: AtlasUtil.getContentMetaV2(session, packs[0]),
                    favAction: LiveUtil.getDIYFavAction(userContext, session.productId, session.productType, true),
                    isCompleted: false,
                    isActive: true,
                    roundedCorners: true,
                    isLocked: false
                })
            }
            sessionProductListWidget = new ProductListWidget("MEDIUM", undefined, sessionActionCards)
        }
        const tabProductListWidget: TabProductListWidget = {
            widgetType: "TAB_PRODUCT_LIST_WIDGET",
            tabTitles: ["Packs", "Sessions"],
            packs: packProductListWidget,
            sessions: sessionProductListWidget,
            packEmptyView,
            sessionEmptyView
        }
        tabProductListWidget.diySummaryData = {
            widgetType: "DIY_PACK_SUMMARY",
            title: "Favourites",
            subTitle: `${_.isEmpty(packActionCards) ? "0 packs" : packActionCards.length + pluralizeStringIfRequired(" pack", packActionCards.length)} ${_.isEmpty(sessionActionCards) ?
                ", 0 sessions" : ", " + sessionActionCards.length + pluralizeStringIfRequired(" session", sessionActionCards.length)}`,
            content: {
                image: "/image/favourite/favourites_banner.png"
            }
        }
        widgets.push(tabProductListWidget)
        return { widgets }
    }

    private getSessionPlayAction(content: DiyContentDetail, meta: DiyContentMeta): Action {
        return {
            actionType: "PLAY_VIDEO",
            meta: {
                content: {
                    id: content.id,
                    type: content.type,
                    format: content.format,
                    URL: content.type === "audio" ? UrlPathBuilder.getAudioPath(content.id, content.format) : UrlPathBuilder.getVideoPath(content.id, content.format),
                    absoluteUrl: content.type === "audio" ? UrlPathBuilder.getAudioAbsolutePath(content.id, content.format) : UrlPathBuilder.getVideoPath(content.id, content.format)
                },
                queryParams: {
                    activityId: meta.activityId,
                    packId: meta.packId,
                    contentId: content.id,
                    consumptionRequired: true,
                    activityType: meta.activityType,
                    title: meta.title,
                    image: meta.image
                },
                title: meta.title,
                checkDownloadStatus: true
            }
        }
    }

    private getIntroAction(url: string): Action {
        return {
            actionType: "NAVIGATION",
            url: url
        }
    }

    private getEmptyView(productType: ProductType): FavoriteEmptyViewWidget {
        return {
            widgetType: "FAVORITE_EMPTY_VIEW",
            prependableDescripton: "Tap ",
            image: "/image/favourite/happy_simley.png",
            appendableDescription: ` on ${productType === "DIY_FITNESS_PACK" || productType === "DIY_MEDITATION_PACK" ? "packs" : "sessions"} and your favourites will appear here`
        }
    }

    private getExercisesDescription(session: DIYFitnessProductExtended): string {
        let description
        if (!_.isEmpty(session.exercises)) {
            description = session.exercises.join(", ")
        }
        return description
    }

    private getSessionWidgetItemAction(session: DIYProduct, pack: DIYPack, isUserEligibleForTrial: boolean, isLocked: boolean, userContext: UserContext, isUserEligibleForMonetisaiton: boolean, bucketId: string, blockInternationalUser: boolean) {
        if (!userContext.sessionInfo.isUserLoggedIn) {
            return {
                actionType: "SHOW_LOGIN_MODAL",
                url: "curefit://loginmodal",
            }
        }
        const action = {
            actionType: "PLAY_VIDEO",
            meta: {
                content: AtlasUtil.getContentDetailV2(session),
                queryParams: AtlasUtil.getContentMetaV2(session, pack),
                title: session.title,
                checkDownloadStatus: true
            }
        }
        return LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisaiton, isLocked, action, isUserEligibleForTrial, pack.productType, "diy_page_session_item", bucketId, blockInternationalUser, session.format)
    }

    private getShareAction(packId: string, productId: string, productType: ProductType, packName: string, productName: string): Action {
        return {
            icon: "SHARE",
            actionType: "REST_API",
            showLoadingIndicator: true,
            meta: {
                method: "post",
                url: `/pack/diy/inviteLink`,
                body: {
                    productId,
                    packId,
                    productType,
                    isSession: true
                },
            },
            analyticsData: {
                type: productType,
                eventType: "SHARE_EVENT",
                packName,
                productName
            }
        }
    }
}

export default DiyFavoritesViewBuilder
