import {
    Action,
    ActionCard,
    DayPickerWidget,
    ProductDetailPage,
    ReminderWidget,
    TimePickerWidget
} from "../common/views/WidgetView"
import { DIYPackFulfilment } from "@curefit/diy-common"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { UserContext } from "@curefit/userinfo-common"

export interface PackSubscribeAction extends Action {
    packId: string
    selectedDays?: number[]
    selectedTimeSlot?: string
    reminder?: boolean
    otherParams?: any
}

class DIYScheduleDetail extends ProductDetailPage {

    constructor(userContext: UserContext, packId: string, productType: string, diyPackFulfilment: DIYPackFulfilment) {
        super()
        this.widgets.push(this.getDayPickerWidget(packId, productType, diyPackFulfilment))
        this.widgets.push(this.getTimePickerWidget(userContext, packId, diyPackFulfilment))
        this.widgets.push(this.getReminderWidget(packId, diyPackFulfilment))
        const pageAction = this.getPageActions(packId, diyPackFulfilment)
        this.pageAction = pageAction.action
        this.actions = pageAction.actions
    }

    private getDayPickerWidget(packId: string, productType: string, diyPackFulfilment: DIYPackFulfilment): DayPickerWidget {
        const dayPickerWidget: DayPickerWidget = new DayPickerWidget()
        dayPickerWidget.preferredDays = [0, 1, 2, 3, 4, 5, 6]
        // dayPickerWidget.preferredDays = (diyPackFulfilment === undefined || diyPackFulfilment.preferredDays === undefined) ? [0, 1, 2, 3, 4, 5, 6] : diyPackFulfilment.preferredDays
        dayPickerWidget.productId = packId
        dayPickerWidget.productType = productType
        dayPickerWidget.isExpanded = true
        return dayPickerWidget
    }

    private getTimePickerWidget(userContext: UserContext, packId: string, diyPackFulfilment: DIYPackFulfilment): TimePickerWidget {
        const tz = userContext.userProfile.timezone
        const timePickerWidget: TimePickerWidget = new TimePickerWidget()
        const preferredTime = (diyPackFulfilment === undefined || diyPackFulfilment.preferredTime === undefined) ? this._getDefaultTimeSlot(tz) : diyPackFulfilment.preferredTime
        timePickerWidget.preferredTime = preferredTime
        timePickerWidget.title = TimeUtil.getTimeIn12HRFormat(TimeUtil.formatDateInTimeZone(tz, new Date()), preferredTime.hour, preferredTime.min, true, tz)
        timePickerWidget.packId = packId
        return timePickerWidget
    }

    private getReminderWidget(packId: string, diyPackFulfilment: DIYPackFulfilment): ReminderWidget {
        const reminderWidget: ReminderWidget = new ReminderWidget()
        if (diyPackFulfilment === undefined) {
            reminderWidget.title = "Reminders: on"
            reminderWidget.toggleState = true
        } else {
            reminderWidget.title = "Reminders: " + (diyPackFulfilment.reminder === false) ? "off" : "on"
            reminderWidget.toggleState = (diyPackFulfilment.reminder === false) ? false : true
        }
        reminderWidget.subTitle = "Notifies you 15 min before session"
        reminderWidget.action = { actionType: "REMINDER_SWITCH_ACTION" }
        reminderWidget.packId = packId
        reminderWidget.expanded = true
        return reminderWidget
    }

    private getPageActions(packId: string, diyPackFulfilment: DIYPackFulfilment): { action: ActionCard, actions: (Action | PackSubscribeAction)[] } {
        return {
            action: {
                "title": "Done",
                "action": "PACK_SUBSCRIBE"
            },
            actions: [{
                "packId": packId,
                "title": "Done",
                "url": "curefit://orderconfirmation",
                "actionType": "PACK_SUBSCRIBE"
            }]
        }
    }

    _getDefaultTimeSlot(timezone: Timezone) {
        const date = TimeUtil.getMomentNow(timezone)
        date.set("minute", date.minutes() - (date.minutes() % 15))
        return { hour: date.hours(), min: date.minutes() }
    }

}
export default DIYScheduleDetail
