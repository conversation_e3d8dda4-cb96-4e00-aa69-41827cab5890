import { <PERSON><PERSON><PERSON>, D<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>er, ProductDetailPage, ProductListWidget } from "../common/views/WidgetView"
import { UrlPathBuilder } from "@curefit/product-common"
import { pad, pluralizeStringIfRequired } from "@curefit/util-common"
import AtlasUtil, { DiyContentDetail, DiyContentMeta } from "../util/AtlasUtil"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { CFLiveProduct, DIYPack, DIYProduct, DIYUserFitnessPack, DIYUserMeditationPack, DIYSeries } from "@curefit/diy-common"
import { ActionUtil, ActionUtilV1, SeoUrlParams } from "@curefit/base-utils"
import AppUtil from "../util/AppUtil"
import { UserContext } from "@curefit/userinfo-common"
import * as _ from "lodash"
import CultUtil from "../util/CultUtil"
import { Action, RouteNames } from "@curefit/apps-common"
import LiveUtil from "../util/LiveUtil"
import { next } from "inversify-express-utils"
import { Logger } from "@curefit/base"
import { WebActionTransformerUtil } from "../util/WebActionTransformerUtil"
import { WebUtil } from "../util/WebUtil"

export interface DiySeriesActionCard extends ActionCard {
    content?: DiyContentDetail
    description?: string
    meta?: DiyContentMeta
    isCompleted?: boolean
    series?: boolean
    isActive: boolean
    isLocked: boolean
    packProgress?: DIYPackProgress
    playAction?: DIYProduct
    packIntroAction?: string
    moreAction?: Action
    favAction?: Action
}

class SeriesDetailViewV1 extends ProductDetailPage {
    breadCrumbs?: {
        title: string,
        link?: string
    }[]

    constructor(userAgent: UserAgent, isUserEligibeForMonetisation: boolean, diySeries: DIYSeries, packs: (DIYUserFitnessPack | DIYUserMeditationPack)[], durationMap: Map<string, number>, nextSessionMap: Map<string, DIYProduct>, logger: Logger, blockInternationalUser: boolean, userContext?: UserContext, isInternalUser?: Boolean, isUserEligibleForLivePackTrial?: boolean, bucketId?: string) {
        super()
        this.widgets.push(this.getSessionsWidget(isUserEligibeForMonetisation, diySeries, packs, durationMap, nextSessionMap, logger, blockInternationalUser, userContext, isInternalUser, isUserEligibleForLivePackTrial, bucketId))
        this.breadCrumbs = []
        // this.getBreadCrumbs(diySeries, userContext)
    }

    private getSessionsWidget(isUserEligibleForMonetisation: boolean, diySeries: DIYSeries, packs: (DIYUserFitnessPack | DIYUserMeditationPack)[], durationMap: Map<string, number>, nextSessionMap: Map<string, DIYProduct>, logger: Logger, blockInternationalUser: boolean, userContext?: UserContext, isInternalUser?: Boolean, isUserEligibleForLivePackTrial?: boolean, bucketId?: string): ProductListWidget {
        const header: Header = {
            title: AppUtil.isWeb(userContext) ? diySeries.name : "About this series",
            subTitle: diySeries.description
        }
        const productIds = diySeries.packIds
        const actionCards: DiySeriesActionCard[] = []
        const isInternationalApp = AppUtil.isInternationalApp(userContext)
        const isSugarFitOrUltraFitApp = AppUtil.isSugarFitOrUltraFitApp(userContext)
        for (let i = 0; i < productIds.length; i++) {

            const productId = productIds[i]
            const packDetail = packs[i]
            const isSessionCompleted = false // Removing the completed text
            const isActive = true // Making all as active
            const session = nextSessionMap.get(productId)
            if (!isInternationalApp && !isSugarFitOrUltraFitApp && !session) {
                logger.info(`Session not found for ${productId} for seriesId:${diySeries.seriesId}`)
                continue
            }
            const content = AtlasUtil.getContentDetailV2(nextSessionMap.get(productId))
            const meta = AtlasUtil.getContentMetaV2(nextSessionMap.get(productId), packDetail.pack)
            const nextSession = (content.type === "audio" ? ActionUtil.audioUrl(content.URL, UrlPathBuilder.prefixSlash(packDetail.pack.imageDetails.heroImage), meta) : ActionUtil.videoUrl(content.URL, UrlPathBuilder.prefixSlash(packDetail.pack.imageDetails.heroImage), meta))
            const packIntroAction = this.getSessionPlayAction(session, packDetail.pack)
            const moreAction = !isSugarFitOrUltraFitApp ? CultUtil.getDiyShareAction(productId, null, packDetail.pack.productType, false, packDetail.pack.title) : undefined
            let state
            if (packDetail.fulfilment) {
                if (packDetail.fulfilment.status === "SUBSCRIBED" || packDetail.fulfilment.status === "COMPLETED") {
                    state = "SUBSCRIBED"
                } else {
                    state = packDetail.fulfilment.status
                }
            }
            actionCards.push({
                title: packDetail.pack.title,
                subTitle: `${packDetail.pack.sessionIds.length} ${pluralizeStringIfRequired("session", packDetail.pack.sessionIds.length)}`,
                description: packDetail.pack.description,
                image: UrlPathBuilder.prefixSlash(packDetail.pack.imageDetails.magazineImage),
                action: (packDetail.pack.cyclopsPageId && AppUtil.isWeb(userContext)) ? WebUtil.getCyclopsWebPageUrl(packDetail.pack.cyclopsPageId) : ActionUtilV1.diyPackProductPage(packDetail.pack, userContext.sessionInfo.userAgent),
                series: true,
                isCompleted: isSessionCompleted,
                isActive: isActive,
                playAction: nextSessionMap.get(productId),
                content: content,
                meta: meta,
                packIntroAction: nextSession,
                isLocked: isInternationalApp && AppUtil.isLiveContentLocked(userContext, session.locked, isUserEligibleForMonetisation, isUserEligibleForLivePackTrial),
                moreAction: moreAction && moreAction,
                // favAction: LiveUtil.getDIYFavAction(userContext, packDetail.pack.productId, packDetail.pack.productType, packDetail.pack.bookmarked),
                packProgress: {
                    total: packDetail.pack.sessionIds.length,
                    completed: (packDetail.fulfilment && packDetail.fulfilment.completedProductIds) ? packDetail.fulfilment.completedProductIds.length : 0,
                    state,
                    type: packDetail.pack.productType === "DIY_FITNESS_PACK" ? "DIY_FITNESS" : "DIY_MEDITATION",
                    noPadding: true,
                    resumeAction: LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, session.locked, packIntroAction, isUserEligibleForLivePackTrial, session.productType, "diy_series_detail_widget", bucketId, blockInternationalUser, session.format),
                    resumeNewAction: LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, session.locked, packIntroAction, isUserEligibleForLivePackTrial, session.productType, "diy_series_detail_widget", bucketId, blockInternationalUser, session.format)
                }
            })
        }
        const productListWidget: ProductListWidget = new ProductListWidget("MAGAZINE_OFFER", header, actionCards)
        productListWidget.hideSepratorLines = true
        return productListWidget
    }

    private getSessionPlayAction(session: DIYProduct, pack: DIYPack) {
        return {
            actionType: "PLAY_VIDEO",
            meta: {
                content: AtlasUtil.getContentDetailV2(session),
                queryParams: AtlasUtil.getContentMetaV2(session, pack),
                title: session.title,
                checkDownloadStatus: true
            }
        }
    }
    private getBreadCrumbs(diySeries: DIYSeries, userContext: UserContext) {
        const isInternationalUser = AppUtil.isInternationalApp(userContext)

        return LiveUtil.getDIYCategoryPageBreadCrumbs(diySeries, isInternationalUser)
    }
}

export default SeriesDetailViewV1
