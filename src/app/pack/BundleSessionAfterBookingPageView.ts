import { ActionUtil, NEW_SPECIALITY_BOOKING_PAGE_SUPPORTED } from "@curefit/base-utils"
import { ConsultationProduct, DOCTOR_TYPE } from "@curefit/care-common"
import { CustomerIssueType } from "@curefit/issue-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import {
    AggregatedMembershipInfo,
    BookingDetail,
    BundleSessionSellableProduct,
    CareTeam,
    IHealthfaceService,
    ManagedPlanPackInfo,
    Patient
} from "@curefit/albus-client"
import {
    Action,
    Header,
    InfoCard,
    ManageOptionPayload,
    ManageOptions,
    PackProgress,
    ProductDetailPage,
    ProductListWidget,
    WidgetView
} from "../common/views/WidgetView"
import {
    ActionableCardItem,
    CareTeamItem,
    CareTeamWidget,
    HorizontalActionableCardListingWidget
} from "../page/PageWidgets"
import { CareUtil } from "../util/CareUtil"
import * as _ from "lodash"
import { UserContext } from "@curefit/userinfo-common"
import { TimeUtil } from "@curefit/util-common"
import { Orientation } from "@curefit/vm-models"
import { ICultServiceOld } from "@curefit/cult-client"
import { RoundedIconListWidgetView } from "../page/vm/widgets/cult/RoundedIconListWidgetView"
import { PtGoalSummaryWidgetView } from "../page/vm/widgets/cult/PtGoalSummaryWidgetView"
import { CareBusiness, ICareBusiness } from "../care/CareBusiness"
import { ICFSClient as IFormService } from "@curefit/cfs-client/dist/src/ICFSClient"
import { HamletBusiness } from "@curefit/hamlet-node-sdk"
import { ICatalogueService } from "@curefit/catalog-client"
import { CacheHelper } from "../util/CacheHelper"
import AppUtil, { SUPPORT_DEEP_LINK } from "../util/AppUtil"
import { ProductType } from "@curefit/product-common"
import * as useragent from "express-useragent"
import { ActionIcon, ActionType } from "@curefit/apps-common"

class BundleSessionAfterBookingPageView extends ProductDetailPage {
    public pageContext: any

    async buildView(
        userContext: UserContext,
        source: string,
        careTeam: CareTeam[],
        product: BundleSessionSellableProduct,
        bookingInfo: BookingDetail,
        issuesMap: Map<string, CustomerIssueType[]>,
        newReportIssueManageOption: ManageOptionPayload,
        consultations: BookingDetail[],
        patientList: Patient[],
        aggregatedMembershipInfo: AggregatedMembershipInfo,
        preferedDoctor?: CareTeam[],
        healthfaceService?: IHealthfaceService,
        cultFitService?: ICultServiceOld,
        careBusiness?: ICareBusiness,
        catalogueService?: ICatalogueService
    ) {
        const isPhysioOrLCPack = bookingInfo.booking.subCategoryCode === "PHYSIOTHERAPY" || bookingInfo.booking.subCategoryCode === "NUTRITIONIST"
        const isLivePTPack = bookingInfo.booking.subCategoryCode === "LIVE_PERSONAL_TRAINING"
        const isLiveSGTPack = bookingInfo.booking.subCategoryCode === "LIVE_SGT"
        const isMindTherapyPack = bookingInfo.booking.subCategoryCode === "MIND_THERAPY"
        const howItWorksItem = _.find(product.infoSection.children, infoSection => infoSection.type === "PACK_STEPS")
        this.actions = await this.getPostBookingActions(userContext, source, patientList, aggregatedMembershipInfo, bookingInfo, isPhysioOrLCPack, isLivePTPack, isLiveSGTPack, preferedDoctor, careBusiness, isMindTherapyPack)
        this.widgets.push(this.summaryWidget(userContext, product, bookingInfo, issuesMap, newReportIssueManageOption, aggregatedMembershipInfo, isPhysioOrLCPack))
        if ((isLivePTPack || isLiveSGTPack) && !AppUtil.isWeb(userContext)) {
            this.widgets.push(...await this.getLivePtGoalWidgets(userContext, cultFitService, healthfaceService, careBusiness))
        }
        if (!_.isEmpty(careTeam)) {
            this.widgets.push(this.getCareTeamWidget(userContext, bookingInfo.booking.id, bookingInfo.booking.patientId, careTeam, isPhysioOrLCPack, isLivePTPack))
        }
        if (!_.isEmpty(consultations)) {
            this.widgets.push(await this.getConsultationsWidget(userContext, consultations, catalogueService))
        }
        this.widgets.push(CareUtil.getHowItWorksWidget(howItWorksItem, userContext.sessionInfo.userAgent))
        this.widgets = this.widgets.filter(Boolean)
        return this
    }

    private summaryWidget(userContext: UserContext, product: BundleSessionSellableProduct, bookingDetail: BookingDetail,
        issuesMap: Map<string, CustomerIssueType[]>, newReportIssueManageOption: ManageOptionPayload,
        aggregatedMembershipInfo: AggregatedMembershipInfo,
        isPhysioOrLCPack: boolean): WidgetView {
        let manageOptionsView: { manageOptions: ManageOptions; meta: any } = null
        if (!AppUtil.isWeb(userContext)) {
            manageOptionsView = this.getManageOptions(bookingDetail, bookingDetail.booking.cfOrderId,
                bookingDetail.booking.productCode, issuesMap, newReportIssueManageOption)
        }
        const isMindTherapyPack = product.subCategoryCode === "MIND_THERAPY"
        const summaryWidget: WidgetView & {
            productId: string
            title: string
            subTitle?: string
            image: string
            meta: any
            packProgress: PackProgress
            manageOptions: ManageOptions,
            breadcrumb?: { text: string, link?: string }[],
            orientation?: Orientation,
            actions?: Action[],
            productType?: ProductType,
            secondaryActions?: Action[]
            hasDividerBelow: boolean
        } = {
            widgetType: "HEALTHCHECKUP_SUMMARY_WIDGET",
            subTitle: isPhysioOrLCPack || isMindTherapyPack ? this.getSubTitlePacks(bookingDetail, aggregatedMembershipInfo, userContext) : this.getSubTitle(bookingDetail, userContext),
            title: product.productName,
            productId: product.productCode,
            image: product.heroImageUrl,
            manageOptions: manageOptionsView ? manageOptionsView.manageOptions : undefined,
            meta: manageOptionsView ? manageOptionsView.meta : undefined,
            packProgress: aggregatedMembershipInfo ? this.getPackProgress(product.productName, userContext, aggregatedMembershipInfo, bookingDetail, isPhysioOrLCPack) : undefined,
            orientation: userContext.sessionInfo.userAgent === "DESKTOP" && isPhysioOrLCPack ? "RIGHT" : undefined,
            breadcrumb: userContext.sessionInfo.userAgent === "DESKTOP" && isPhysioOrLCPack ? [{ text: "Home", link: "/" }, { text: "Care", link: "/care" }, { text: product.infoSection.packTitle }] : [],
            actions: userContext.sessionInfo.userAgent !== "APP" ? this.actions : [],
            hasDividerBelow: false
        }
        if (product.subCategoryCode === "LIVE_PERSONAL_TRAINING" || product.subCategoryCode === "LIVE_SGT") {
            summaryWidget.productType = <ProductType>product.subCategoryCode
        }
        if (isPhysioOrLCPack) {
            summaryWidget.productType = "BUNDLE"
        }
        if (isMindTherapyPack) {
            summaryWidget.productType = "MIND_THERAPY"
            summaryWidget.secondaryActions = []
            summaryWidget.breadcrumb = [{text: "Mind", link: "/mind"}, {text: "Therapy", link: "/mind/therapy"},  {text: "Packs"}]

            if (aggregatedMembershipInfo?.productMetas) {
                const consultationProducts = _.map(aggregatedMembershipInfo.productMetas, productMeta => <ConsultationProduct>productMeta.productDetail)
                consultationProducts.forEach(consultationProduct => {
                    if (CareUtil.isTherapyOnlyDoctorType(consultationProduct.doctorType) && !(CareUtil.isSupportGroupDoctorType(consultationProduct.doctorType) && AppUtil.isWeb(userContext))) {
                        const secondaryAction: {
                            actionType: ActionType,
                            displayText?: string,
                            action?: Action,
                            isEnabled?: boolean,
                            icon?: ActionIcon
                        } = { actionType: "NAVIGATION" }
                        let actionTitle = "Book At Center Consultation"
                        if (consultationProduct.consultationMode === "ONLINE") {
                            actionTitle = "Book Video Call Consultation"
                        }
                        const secAction = CareUtil.specialistListingAction(userContext, consultationProduct, undefined, undefined, bookingDetail.booking.id)
                        secondaryAction.displayText = actionTitle
                        secondaryAction.isEnabled = true
                        secondaryAction.action = secAction
                        secondaryAction.icon = "BOOK"
                        summaryWidget.secondaryActions.push(secondaryAction)
                    }
                })
            }
        }
        return summaryWidget
    }

    getPackProgress(title: string, userContext: UserContext, aggregatedMembershipInfo: AggregatedMembershipInfo, bookingDetail: BookingDetail, isPhysioOrLCPack: boolean): PackProgress {
        const totalSessions = aggregatedMembershipInfo.totalTickets
        const remainingSessions = totalSessions - aggregatedMembershipInfo.totalTicketsConsumed
        let packProgress: PackProgress
        const isLivePTPack = bookingDetail.booking.subCategoryCode === "LIVE_PERSONAL_TRAINING"
        const isliveSGTPack = bookingDetail.booking.subCategoryCode === "LIVE_SGT"
        const isMindTherapyPack = bookingDetail.booking.subCategoryCode === "MIND_THERAPY"
        if (isPhysioOrLCPack || isMindTherapyPack) {
            let packProgress: PackProgress
            if (_.isEmpty(aggregatedMembershipInfo.startEndEpoch)) {
                return
            }
            const completedSessions = aggregatedMembershipInfo.totalTicketsConsumed // _.filter(bookingDetail.childBookingInfos, childBooking => childBooking.booking.status === "COMPLETED").length
            const { total, completed, isCompleted, endDateFormatted } = CareUtil.getConsultationPackProgressInfo(userContext, aggregatedMembershipInfo, true)
            if (userContext.sessionInfo.userAgent === "APP") {
                packProgress = {
                    total,
                    completed,
                    state: "ACTIVE",
                    type: <ProductType>"BUNDLE",
                    leftText: `${completedSessions}/${totalSessions} Session Booked`,
                    rightText: endDateFormatted,
                    progressBarTextStyle: { marginTop: 10, fontSize: 14 },
                    progressBarColor: "#008300",
                    isSplitView: true
                }
            } else {
                packProgress = {
                    title: `${completedSessions}/${totalSessions} Session Booked` ,
                    subTitle: endDateFormatted,
                    total,
                    completed,
                    state: isCompleted ? "COMPLETED" : "ACTIVE",
                    packColor: "#008300",
                    type: "BUNDLE",
                    action: !isCompleted && this.actions && this.actions[0] ? this.actions[0] : undefined
                }
            }
            return packProgress
        }
        const completedSessions = _.filter(bookingDetail.childBookingInfos, childBooking => childBooking.booking.status === "COMPLETED").length
        if (userContext.sessionInfo.userAgent === "APP") {
            packProgress = {
                total: totalSessions,
                completed: completedSessions,
                state: "ACTIVE",
                type: "THERAPY",
                title: isPhysioOrLCPack ? `${completedSessions}/${totalSessions} Session Completed` : `Total: ${totalSessions} \u2022 Remaining: ${remainingSessions}`
            }
        } else {
            const isPackCompleted = totalSessions - completedSessions < 1
            const tz = userContext.userProfile.timezone
            const endDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(bookingDetail.bundleOrderResponse.expiryTimeEpoch)), tz)
            const endDateFormatted = endDate.format("D MMM YYYY")
            const startDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(bookingDetail.bundleOrderResponse.startTimeEpoch)), tz)
            const startDateFormatted = startDate.format("D MMM YYYY")
            const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDate(tz), tz)
            const numDaysToEndFromToday = endDate.diff(today, "days")
            title = isliveSGTPack ? `Start: ${startDateFormatted}` : title
            packProgress = {
                title: isliveSGTPack ? title : `${title}: ${totalSessions} ${totalSessions > 1 ? "sessions" : "session"}`,
                subTitle: isliveSGTPack ? `Ends: ${endDateFormatted}` : isPhysioOrLCPack ? `${completedSessions}/${totalSessions} session completed` : `Total: ${totalSessions} \u2022 Remaining: ${remainingSessions}`,
                total: isliveSGTPack ? endDate.diff(startDate, "days") : totalSessions,
                completed: isliveSGTPack ? numDaysToEndFromToday > 0 ? today.diff(startDate, "days") : endDate.diff(startDate, "days") : completedSessions,
                state: isPackCompleted ? "COMPLETED" : "ACTIVE",
                packColor: "#008300",
                type: "BUNDLE",
                displayText: !(isLivePTPack || isliveSGTPack) && _.get(bookingDetail, "bundleOrderResponse.patient.name") ? `For: ${bookingDetail.bundleOrderResponse.patient.name}` : undefined,
                action: !isPackCompleted && this.actions && this.actions[0] ? this.actions[0] : undefined
            }
        }
        return packProgress
    }

    private getIssueList(issuesMap: Map<string, CustomerIssueType[]>): { code: string, title: string, confirmation: string }[] {
        const issueList: { code: string, title: string, confirmation: string }[] = []
        issuesMap.get("DiagnosticsSingleBooked").forEach(customerIssueType => {
            issueList.push({
                code: customerIssueType.code,
                title: customerIssueType.subject,
                confirmation: customerIssueType.confirmation
            })
        })
        return issueList
    }

    private getSubTitle(bookingDetail: BookingDetail, userContext: UserContext) {
        const timezone = userContext.userProfile.timezone
        if (bookingDetail.booking.status === "CANCELLED") {
            return "Cancelled"
        } else {
            const startDate = TimeUtil.formatDateInTimeZone(timezone, TimeUtil.parseDateFromEpoch(bookingDetail.bundleOrderResponse.startTimeEpoch))
            const endDate = TimeUtil.formatDateInTimeZone(timezone, TimeUtil.parseDateFromEpoch(bookingDetail.bundleOrderResponse.expiryTimeEpoch))
            const today = TimeUtil.todaysDateWithTimezone(timezone)
            const remainingDays = TimeUtil.diffInDays(timezone, endDate, today)
            if (endDate < today) {
                // Handle expired state
                return "Expired"
            }
            else if (startDate > today) {
                // Handle future packs
                return `Starting on ${TimeUtil.formatDateStringInTimeZone(startDate, timezone, "DD MMM")}`
            }
            else if (remainingDays < 30) {
                return `${remainingDays} days remaining`
            }
            else {
                return `Ending on ${TimeUtil.formatDateStringInTimeZone(endDate, timezone, "DD MMM")}`
            }
        }
    }

    public getSubTitlePacks(bookingDetail: BookingDetail, aggregatedMembershipInfo: AggregatedMembershipInfo, userContext: UserContext) {
        if (bookingDetail.booking.status === "CANCELLED") {
            return "Cancelled"
        }
        if (_.isEmpty(aggregatedMembershipInfo)) {
            return _.get(bookingDetail, "bundleOrderResponse.patient.name") ? `For: ${bookingDetail.bundleOrderResponse.patient.name} | Pack Expired` : undefined
        }
        return _.get(bookingDetail, "bundleOrderResponse.patient.name") ? `For: ${bookingDetail.bundleOrderResponse.patient.name}` : undefined
    }

    private getManageOptions(bookingDetail: BookingDetail, orderId: string, productId: string,
        issuesMap: Map<string, CustomerIssueType[]>,
        newReportIssueManageOption: ManageOptionPayload): { manageOptions: ManageOptions, meta: any } {
        const options: ManageOptionPayload[] = []
        if (newReportIssueManageOption) {
            options.push(newReportIssueManageOption)
        } else {
            options.push(
                {
                    isEnabled: true,
                    displayText: "Need Help",
                    type: "REPORT_ISSUE",
                    meta: this.getIssueList(issuesMap),
                    action: SUPPORT_DEEP_LINK,
                }
            )
        }

        const manageOptions: ManageOptions = {
            displayText: "...",
            options: options
        }

        const meta: any = {
            orderId: orderId,
            productId: productId,
            tcBookingId: bookingDetail.booking.id
        }
        return { manageOptions: manageOptions, meta: meta }
    }

    private getCareTeamWidget(userContext: UserContext, bookingId: number, patientId: number, careTeam: CareTeam[], isPhysioOrLCPack: boolean, isLivePTPack: boolean): CareTeamWidget {
        const careTeamViewItems: CareTeamItem[] = []
        careTeam.map(item => {
            careTeamViewItems.push({
                title: item.doctor.name,
                subTitle: item.doctorTypeCode.displayValue,
                imageUrl: item.doctor.displayImage,
                actions: this.getCareTeamActions(userContext, item)
            })
        })
        const careTeamWidget =  new CareTeamWidget(careTeamViewItems,
            isPhysioOrLCPack ? "Care Team" : undefined,
            { backgroundColor: "white" },
            false
        )
        careTeamWidget.hasDividerBelow = false
        if (isLivePTPack) {
            careTeamWidget.productType = "LIVE_PERSONAL_TRAINING"
        }
        return careTeamWidget
    }

     private async getConsultationsWidget(userContext: UserContext, consultations: BookingDetail[], catalogueService: ICatalogueService): Promise<HorizontalActionableCardListingWidget> {
        const cardItems: ActionableCardItem[] = []

        await consultations.map(async consultation => cardItems.push(await this.getConsultationActionableCardWidget(userContext, consultation, catalogueService)))

        return {
            widgetType: "HORIZONTAL_ACTIONAL_CARD_LISTING_WIDGET",
            title: "Your Sessions",
            type: "CONSULTATION",
            cardItems,
            orientation: userContext.sessionInfo.userAgent === "DESKTOP" ? "RIGHT" : undefined,
            useNewWidgetInWeb: userContext.sessionInfo.userAgent !== "APP"
        }
    }

    async getConsultationActionableCardWidget(userContext: UserContext, booking: BookingDetail, catalogueService: ICatalogueService): Promise<ActionableCardItem> {
        const tz = userContext.userProfile.timezone
        const actions: Action[] = this.getActions(userContext, booking)
        const productCode = booking.booking.productCode
        const consultationProduct = <ConsultationProduct>await catalogueService.getProduct(productCode)
        let atCenterConsultationFooterText = `At Centre | ${TimeUtil.formatEpochInTimeZone(tz, booking.consultationOrderResponse.startTime, "ddd, D MMM, h:mm A")}`
        if (CareUtil.isPTSessionConsultation(booking.consultationOrderResponse)) {
            const centerName = booking.consultationOrderResponse.center.name
            atCenterConsultationFooterText = `${centerName} | ${TimeUtil.formatEpochInTimeZone(tz, booking.consultationOrderResponse.startTime, "ddd, D MMM, h:mm A")}`
        }
        const vertical = CareUtil.getVerticalForConsultation(booking.consultationOrderResponse.consultationProduct.doctorType)
        return {
            tag: CareUtil.getConsultationTag(booking.consultationOrderResponse.consultationUserState, booking.consultationOrderResponse.status),
            title: booking.consultationOrderResponse.doctor ? `Session with ${booking.consultationOrderResponse.doctor.name}` : "Session",
            subTitle: `for ${booking.consultationOrderResponse.patient.name}`,
            imageUrl: booking.consultationOrderResponse.doctor ? booking.consultationOrderResponse.doctor.displayImage : "",
            footer: CareUtil.getUpcomingConsultationFooterInfo(booking.consultationOrderResponse, tz, false, atCenterConsultationFooterText),
            actions: actions,
            cardAction: {
                actionType: "NAVIGATION",
                url: ActionUtil.teleconsultationSingle(userContext, booking.booking.productCode, consultationProduct.urlPath, booking.consultationOrderResponse.bookingId.toString(), undefined, vertical)
            }
        }
    }


    private getActions(userContext: UserContext, booking: BookingDetail): Action[] {
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "DESKTOP")
        const actions: Action[] = []
        const consultationOrderResponse = booking.consultationOrderResponse
        if (CareUtil.isComplteted(booking)) {
            if (!CareUtil.isPTSessionConsultation(consultationOrderResponse) && consultationOrderResponse.consultationProduct.doctorType !== "LIFESTYLE_COACH" && consultationOrderResponse.hasPrescription) {
                actions.push({
                    title: "Prescription",
                    actionType: "NAVIGATION",
                    icon: "PRESCRIPTION",
                    url: `curefit://carefitPrescription?tcBookingId=${booking.booking.id}&productId=${booking.booking.productCode}`
                })
            }
            if (consultationOrderResponse.doctorType === "PHYSIOTHERAPIST" && booking.consultationProduct && booking.consultationProduct.hasPlan) {
                actions.push({
                    actionType: "EMAIL_PLAN",
                    title: "Email Plan",
                    icon: "EMAIL",
                    meta: {
                        bookingId: booking.booking.id,
                        appointmentId: booking.consultationOrderResponse.id
                    }
                })
            }
        }

        if (consultationOrderResponse.appointmentActionsWithContext && consultationOrderResponse.appointmentActionsWithContext.chatActionWithContext && consultationOrderResponse.appointmentActionsWithContext.chatActionWithContext.action.actionPermitted) {
            actions.push({
                title: "Message",
                actionType: (userAgent === "DESKTOP" || userAgent === "MBROWSER") ? "OPEN_MESSAGE_DRAWER" : "NAVIGATION",
                icon: "MESSAGE",
                url: ActionUtil.chatMessageActionUrl(consultationOrderResponse.patient.id, CareUtil.getChatChannelWithAppointmentContext(consultationOrderResponse.appointmentActionsWithContext), consultationOrderResponse.doctor.name, consultationOrderResponse.doctor.displayImage, consultationOrderResponse.doctor.qualification, booking.booking.id, undefined, "mindtherapy")
            })
        }
        if (actions.length <= 2) {
            if (consultationOrderResponse.appointmentActionsWithContext && consultationOrderResponse.appointmentActionsWithContext.rescheduleActionWithContext && consultationOrderResponse.appointmentActionsWithContext.rescheduleActionWithContext.action.actionPermitted && consultationOrderResponse?.center?.id) {
                actions.push({
                    title: "Reschedule",
                    isEnabled: true,
                    actionType: "RESCHEDULE_TC",
                    icon: "RESCHEDULE",
                    url: `curefit://rescheduleTc?parentBookingId=${consultationOrderResponse.bookingId}&isReschedule=true`,
                    meta: {
                        "tcBookingId": consultationOrderResponse.bookingId,
                        "centerId": consultationOrderResponse.center.id,
                        "productId": booking.booking.productCode,
                        "patientId": consultationOrderResponse.patient.id
                    }
                })
            }
        }
        return actions
    }

    private getCareTeamActions(userContext: UserContext, item: CareTeam): Action[] {
        const actions: Action[] = []
        const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "DESKTOP")
        if (item.chatActionWithContext && item.chatActionWithContext.action && item.chatActionWithContext.action.actionPermitted) {
            actions.push({
                title: "Message",
                actionType: (userAgent === "DESKTOP" || userAgent === "MBROWSER") ? "OPEN_MESSAGE_DRAWER" : "NAVIGATION",
                icon: "MESSAGE",
                url: ActionUtil.chatMessageActionUrl(item.patientId, item.chatActionWithContext.context.twilioCommunicationMode.modeName, item.doctor.name, item.doctor.displayImage, item.doctor.qualification, undefined, undefined, "bundlesession")
            })
        }
        return actions
    }

    private async getPostBookingActions(userContext: UserContext, source: string, patientsList: Patient[], aggregatedMembershipInfo: AggregatedMembershipInfo, bookingInfo: BookingDetail, isPhysioOrLCPack: boolean, isLivePTPack: boolean, isLiveSGTPack: boolean, preferedDoctor?: CareTeam[], careBuisness?: ICareBusiness, isMindTherapyPack?: boolean): Promise<Action[]> {
        if (!aggregatedMembershipInfo) {
            return
        }
        if (aggregatedMembershipInfo.totalTickets - aggregatedMembershipInfo.totalTicketsConsumed < 1) {
            return
        }
        const pageActions: Action[] = []
        const selfPatient = !_.isEmpty(patientsList) ? _.find(patientsList, patient => patient.relationship === "Self") : undefined
        const consultationProducts = _.map(aggregatedMembershipInfo.productMetas, productMeta => <ConsultationProduct>productMeta.productDetail)
        let url = ActionUtil.selectCareDateV1(consultationProducts[0].productId, undefined, bookingInfo.booking.id, userContext)
        let action
        if (bookingInfo.booking.subCategoryCode === "NUTRITIONIST") {
            if (consultationProducts.length === 1) {
                if (consultationProducts[0].consultationMode === "INCENTRE") {
                    action = {
                        actionType: "NAVIGATION",
                        title: "BOOK CONSULTATION",
                        icon: "DATE",
                        url: `curefit://selectCareDateV1?productId=${consultationProducts[0].productId}&patientId=${bookingInfo.booking.patientId}&parentBookingId=${bookingInfo.booking.id}`
                    }
                } else {
                    action = CareUtil.specialistListingAction(userContext, consultationProducts[0], false, bookingInfo.booking.patientId, bookingInfo.booking.id, undefined, undefined, false, undefined, undefined, undefined, true, "Select a  Dietician")
                    action.title = "BOOK CONSULTATION"
                }
            } else {
                action = {
                    title: "BOOK CONSULTATION",
                    icon: "DATE",
                    actionType: "SHOW_CARE_ACTION_SHEET",
                    meta: {
                        title: "Schedule your consultation",
                        cards: consultationProducts.map(product => {
                            const action = {
                                actionType: "NAVIGATION",
                                url: `curefit://selectCareDateV1?productId=${product.productId}&patientId=${bookingInfo.booking.patientId}&parentBookingId=${bookingInfo.booking.id}`
                            }
                            if (product.consultationMode === "ONLINE") {
                                return {
                                    title: "Video call",
                                    icon: "VIDEO",
                                    action
                                }
                            } else if (product.consultationMode === "INCENTRE") {
                                return {
                                    title: "At Centre",
                                    icon: "CENTRE",
                                    action
                                }
                            }
                        })
                    }
                }
            }
        } else if (bookingInfo.booking.subCategoryCode === "PHYSIOTHERAPY") {
            const pagetitle = "Select a Physiotherapist"
            if (consultationProducts.length === 1) {
                action = {
                    ...CareUtil.specialistListingAction(userContext, consultationProducts[0], false, bookingInfo.booking.patientId, bookingInfo.booking.id, undefined, undefined, false, undefined, undefined, undefined, true, pagetitle),
                    meta: {
                        name: pagetitle
                    },
                    title: "BOOK CONSULTATION"
                }
            } else {
                action = {
                    title: "BOOK CONSULTATION",
                    icon: "DATE",
                    actionType: "SHOW_CARE_ACTION_SHEET",
                    meta: {
                        title: "Schedule your consultation",
                        cards: consultationProducts.map(product => {
                            const action = {
                                ...CareUtil.specialistListingAction(userContext, product, false, bookingInfo.booking.patientId, bookingInfo.booking.id, undefined, undefined, false, undefined, undefined, undefined, true, pagetitle),
                                meta: {
                                    name: pagetitle
                                }
                            }
                            if (product.consultationMode === "ONLINE") {
                                return {
                                    title: "Video call",
                                    icon: "VIDEO",
                                    action
                                }
                            } else if (product.consultationMode === "INCENTRE") {
                                return {
                                    title: "At Centre",
                                    icon: "CENTRE",
                                    action
                                }
                            }
                        })
                    }
                }
            }
        } else if (isLivePTPack || isLiveSGTPack) {
            action = await careBuisness.getLivePTSessionBookAction(userContext, { productId: consultationProducts[0].productId, actionTitle: "BOOK SESSION", parentBookingId: bookingInfo.booking.id, subCategoryCode: isLiveSGTPack ? "LIVE_SGT" : "LIVE_PERSONAL_TRAINING" })
        } else if (isMindTherapyPack) {
            if (AppUtil.isWeb(userContext)) {
                action = null
                return pageActions
            }
        } else {
            url += `&patientId=${selfPatient.id}`
            if (userContext.sessionInfo.appVersion >= NEW_SPECIALITY_BOOKING_PAGE_SUPPORTED && _.isEmpty(preferedDoctor)) {
                action = {
                    ...CareUtil.specialistListingAction(userContext, consultationProducts[0], true, selfPatient.id, bookingInfo.booking.id, undefined, undefined, true, "#f1f4f7,#f1f4f7"),
                    title: "Book A Session"
                }
            }
        }
        const sessionBookingAction: Action = action ? action : {
            actionType: "NAVIGATION",
            title: "Book A Session",
            url: url
        }
        pageActions.push(sessionBookingAction)
        return pageActions
    }

    private async getLivePtGoalWidgets(userContext: UserContext, cultFitService: ICultServiceOld, healthfaceService: IHealthfaceService, careBuisness: ICareBusiness): Promise<WidgetView[]> {
        const goalWidgets: WidgetView[] = []
        const userId = userContext.userProfile.userId
        const appName = "CUREFIT_APP"
        const goalExists = (await cultFitService.getLivePTOnboardingComplete({ userId, appName })).isOnboarded
        if (!goalExists) {
            goalWidgets.push(await new RoundedIconListWidgetView().getWidgetView(userContext, "LIVE_PERSONAL_TRAINING", cultFitService))
        } else {
            goalWidgets.push(await new PtGoalSummaryWidgetView().getWidgetView(userContext, "LIVE_PERSONAL_TRAINING", cultFitService, healthfaceService, careBuisness))
        }
        return goalWidgets
    }
}

export default BundleSessionAfterBookingPageView
