import { CustomerIssueType } from "@curefit/issue-common"
import { FitnessSubscriptionPackDetails } from "@curefit/cult-common"
import { CultPack } from "@curefit/cult-common"
import { CultBooking, CultCenter } from "@curefit/cult-common"
import { CultMembership } from "@curefit/cult-common"
import { CultPackProgress } from "@curefit/cult-client"
import {
    Action,
    ActionCard,
    DescriptionWidget, FitClubPackOfferWidget,
    getOffersWidget,
    Header,
    InfoCard,
    ManageOptionPayload,
    PackProgress, PauseInfo,
    ProductDetailPage,
    ProductListWidget,
    SubscriptionPriceDetail,
    SubscriptionPriceView,
    WidgetView
} from "../common/views/WidgetView"

import { CatalogueServiceUtilities } from "../util/CatalogueServiceUtilities"
import { ICatalogueService, CATALOG_CLIENT_TYPES, CatalogueServiceV2Utilities } from "@curefit/catalog-client"
import CultPackPageConfig from "./CultPackPageConfig"
import { inject, injectable } from "inversify"
import IProductBusiness from "../product/IProductBusiness"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import * as _ from "lodash"
import * as momentTz from "moment-timezone"
import { CultOfferRequestParams, CultProductPricesResponse } from "@curefit/offer-common"
import { IOfferServiceV2, OfferServiceV3, PackOffersResponse } from "@curefit/offer-service-client"
import CultUtil from "../util/CultUtil"
import { ImageCategoryType as ImageCategory, ProductType, UrlPathBuilder } from "@curefit/product-common"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { UserContext } from "@curefit/userinfo-common"
import { ActionUtil, OfferUtil } from "@curefit/base-utils"
import AppUtil from "../util/AppUtil"
import { CultPackPageRequestParams, DerivedCityDetails } from "./CultPackDetailViewBuilder"
import ICRMIssueService from "../crm/ICRMIssueService"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { BaseOfferItem, OFFER_SERVICE_CLIENT_TYPES } from "@curefit/offer-service-client"
import { OfferV2 } from "@curefit/offer-common"
import IssueBusiness, { IssueDetailView } from "../crm/IssueBusiness"
import { User, Tenant } from "@curefit/user-common"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import { CENTER_SERVICE_CLIENT_TYPES, ICenterService } from "@curefit/center-service-client"
import { BASE_TYPES, Logger } from "@curefit/base"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"


@injectable()
abstract class CultSubscriptionDetailViewBuilder {

    constructor(@inject(CUREFIT_API_TYPES.ProductBusiness) protected productBusiness: IProductBusiness,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) protected catalogueService: ICatalogueService,
        @inject(LOCATION_TYPES.CityService) protected cityService: ICityService,
        @inject(CUREFIT_API_TYPES.IssueBusiness) protected issueBusiness: IssueBusiness,
        @inject(CUREFIT_API_TYPES.CRMIssueService) protected CRMIssueService: ICRMIssueService,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) protected offerServiceV2: IOfferServiceV2,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) protected offerServiceV3: OfferServiceV3,
        @inject(CUREFIT_API_TYPES.CultPackPageConfig) protected cultPackPageConfig: CultPackPageConfig,
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) public membershipService: IMembershipService,
        @inject(CENTER_SERVICE_CLIENT_TYPES.CenterService) protected centerService: ICenterService,
        @inject(BASE_TYPES.ILogger) protected logger: Logger,
    ) {

    }

    abstract getCenter(centerId: string): Promise<CultCenter>
    abstract getDefaultCenterId(derivedCity: DerivedCityDetails): string
    abstract getPreferredCenterId(userContext: UserContext, derivedCity: DerivedCityDetails): string
    abstract getOffersV3Promise(userContext: UserContext, productId: string, preferredCenterId: string): Promise<CultProductPricesResponse>
    abstract isSourceMembership(membership: CultMembership): boolean
    abstract getBiometricHeader(): string
    abstract getWhySubscribeItems(cultPackPageConfig: CultPackPageConfig): InfoCard[]

    /**
     * @deprecated
     */
    async getView(userContext: UserContext, requestParams: CultPackPageRequestParams): Promise<ProductDetailPage> {
        this.logger.error("PMS::DEPR Cult Subscription Detail View Builder is deprecated", {requestParams})
        throw new Error("Deprecated")
    }


    // private getUpcomingPauseActionCard(userContext: UserContext, pageConfig: CultPackPageConfig, packInfo: FitnessPack, membership: CultMembership) {
    //     const tz = userContext.userProfile.timezone
    //     if (CultUtil.isUpcomingPause(membership)) {
    //         const pauseSeeMoreAction: Action = {
    //             actionType: "SHOW_OFFERS_TNC_MODAL",
    //             icon: "INFO",
    //             meta: {
    //                 title: pageConfig.subscriptionPackPolicyItemList[0].policyTitle,
    //                 dataItems: CultUtil.getFinalPolicyText(pageConfig.subscriptionPackPolicyItemList[0].policyList, packInfo)
    //             }
    //         }
    //         const pauseStartDate = membership.ActivePause ? membership.ActivePause.startTime : undefined
    //         const pauseEndDate = membership.ActivePause ? membership.ActivePause.maxEndDate ? membership.ActivePause.maxEndDate : undefined : undefined
    //         const pauseEndDateFormatted = TimeUtil.formatDateInTimeZone(tz, pauseEndDate)
    //         const pauseStartDateFormatted = TimeUtil.formatDateInTimeZone(tz, pauseStartDate)
    //         const totalPauseDays = TimeUtil.diffInDays(tz, pauseStartDateFormatted, pauseEndDateFormatted)
    //         let pauseEndDateWithTime
    //         if (pauseEndDate) {
    //             // pauseEndDateWithTime = TimeUtil.parseDate(pauseEndDateFormatted, tz)
    //             // pauseEndDateWithTime.setHours(23, 59, 0, 0)
    //             pauseEndDateWithTime = TimeUtil.getDefaultMomentForDateString(pauseEndDateFormatted, tz).startOf("day").subtract(1, "minute").toDate()
    //         }
    //         const startDateText = `${TimeUtil.formatDateInTimeZone(tz, pauseStartDate, "DD MMM hh:mm a")}`
    //         const daysDiff = TimeUtil.diffInDaysReal(tz, TimeUtil.todaysDate(tz), pauseStartDateFormatted)
    //         const remainingDaysInCurrentDuration = daysDiff < 0 ? 0 : daysDiff
    //         const limit = TimeUtil.addDays(tz, membership.endDate, remainingDaysInCurrentDuration)
    //         const meta: any = {
    //             membershipId: membership.id,
    //             packId: membership.packID,
    //             productType: packInfo.productType,
    //             title: "Cancel Pause",
    //             pauseMaxDays: membership.pauseMaxDays,
    //             remainingPauseDays: membership.remainingPauseDays,
    //             remainingDaysInCurrentDuration,
    //             pauseEndDate: pauseEndDate ? TimeUtil.formatDateInTimeZone(tz, pauseEndDate) : undefined,
    //             startDateParams: {
    //                 date: TimeUtil.todaysDate(tz),
    //                 limit,
    //                 canEdit: false,
    //                 pauseEndText: "Your pack is paused till"
    //             },
    //             action: {
    //                 primaryText: "YES",
    //                 secondaryText: "NO"
    //             },
    //             pauseInfoTitles: {
    //                 pauseUsed: "Pause days used",
    //                 membershipExtended: "Membership will be extended by",
    //                 membershipEndsOn: "Membership will now end on",
    //                 pauseLeft: "Pause days left"
    //             },
    //             pauseInfo: this.getPauseInfo(tz, remainingDaysInCurrentDuration, limit, (membership.remainingPauseDays - remainingDaysInCurrentDuration), true),
    //             dateParam: {
    //                 date: TimeUtil.formatDateInTimeZone(tz, pauseStartDate, "YYYY-MM-DD hh:mm A"),
    //                 limit,
    //                 pauseEndDate: pauseEndDateWithTime ? TimeUtil.formatDateInTimeZone(tz, pauseEndDateWithTime, "DD MMM hh:mm a") : undefined,
    //                 pauseEndText: `Pause starting ${startDateText} till`
    //             },
    //             subTitle: `You are cancelling pause starting tonight till ${TimeUtil.formatDateInTimeZone(tz, pauseEndDateWithTime, "DD MMM hh:mm a")}. Your pause days remain intact`
    //         }
    //         const action: Action = {
    //             actionType: "CANCEL_CULT_PACK_PAUSE",
    //             meta,
    //             shouldRefreshPage: true
    //         }
    //         return {
    //             icon: "PAUSE",
    //             title: AppUtil.isCultPauseCancelSupported(userContext) ? "Cancel Pause" : "Pause Scheduled",
    //             subTitle: pauseStartDate && pauseEndDateWithTime ? `Pause starting tonight till ${TimeUtil.formatDateInTimeZone(tz, pauseEndDateWithTime, "DD MMM hh:mm a")}` : "",
    //             seeMoreAction: pauseSeeMoreAction,
    //             cardAction: action
    //         }
    //     }
    // }

    private getWhySubscribeWidget(cultPackPageConfig: CultPackPageConfig) {
        return new ProductListWidget("INFO_CELL", { "title": "Why subscribe" }, this.getWhySubscribeItems(cultPackPageConfig), undefined, true)
    }

    // private getPauseNoShowPolicyWidget(packInfo: FitnessPack, cultPackPageConfig: CultPackPageConfig): ProductListWidget {
    //     const subscriptionTypeInfo = CultUtil.getSubscriptionTypeInfo(packInfo.subscriptionPack.renewalCycleUnit)
    //     const header: Header = {
    //         title: "Pause & No-show",
    //         color: "#000000"
    //     }

    //     const actionCards: ActionCard[] = []
    //     cultPackPageConfig.subscriptionPackPolicyItemList.forEach(item => {
    //         const text = item.title.replace("${freeNoShows}", packInfo.maxNoShow + "").replace("${renewalCycle}", subscriptionTypeInfo.renewalCycleText)
    //         const seeMoreAction: Action = {
    //             actionType: "SHOW_OFFERS_TNC_MODAL",
    //             meta: {
    //                 title: item.policyTitle,
    //                 dataItems: CultUtil.getFinalPolicyText(item.policyList, packInfo)
    //             }
    //         }
    //         if (actionCards.length < 2) {
    //             actionCards.push({
    //                 subTitle: text,
    //                 icon: item.icon,
    //                 seeMoreText: item.seeMoreText,
    //                 cardAction: seeMoreAction
    //             })
    //         }
    //     })
    //     const widget = new ProductListWidget("SMALL", header, actionCards)
    //     widget.hideSepratorLines = true
    //     return widget
    // }

    private getPackProgress(userContext: UserContext, membership: CultMembership, cultPackProgress: CultPackProgress): PackProgress {
        const tz = userContext.userProfile.timezone
        const endDate = TimeUtil.getMomentForDateString(membership.endDate, tz)
        const endDateFormatted = endDate.format("D MMM")
        const startDate = TimeUtil.getMomentForDateString(membership.startDate, tz)
        const startDateFormatted = startDate.format("D MMM")
        const renewalDate = TimeUtil.getMomentForDateString(membership.userSubscription.userSubscriptionCycle.nextBillingDate, tz)
        const renewalDateFormatted = renewalDate.format("D MMM")
        const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDate(tz), tz)
        const numDaysToEndFromToday = endDate.diff(today, "days")
        const isSubscriptionActive = membership.userSubscription.state === "ACTIVE"
        const startDateText = cultPackProgress.clientMembershipState === "FUTURE" ? `Starting from: ${startDateFormatted}` : cultPackProgress.clientMembershipState === "ACTIVE" ? `Started:  ${startDateFormatted}` : undefined
        const endDateText = cultPackProgress.clientMembershipState === "FUTURE" ? undefined : cultPackProgress.clientMembershipState === "EXPIRED" ? `Expired: ${endDateFormatted}` : isSubscriptionActive ? `Renews: ${renewalDateFormatted}` : `Ends on: ${endDateFormatted}`
        return {
            startDate: startDateText,
            endDate: endDateText,
            total: endDate.diff(startDate, "days"),
            completed: numDaysToEndFromToday > 0 ? today.diff(startDate, "days") : endDate.diff(startDate, "days"),
            state: numDaysToEndFromToday > 0 ? cultPackProgress.state : "COMPLETED", // for membership pause
            type: "FITNESS_SUBSCRIPTION" // change based on productType
        }
    }

    private getUpcomingSessionWidget(productType: ProductType, bookings: CultBooking[], userContext: UserContext): ProductListWidget {
        const actionCards: ActionCard[] = []
        const sortedBookings = bookings.sort((a, b) => {
            if (a.Class.date < b.Class.date) {
                return -1
            } else if (a.Class.date > b.Class.date) {
                return 1
            } else {
                if (a.Class.startTime < b.Class.startTime) {
                    return -1
                } else {
                    return 1
                }
            }
        })

        sortedBookings.forEach(booking => {
            actionCards.push({
                title: booking.Class.Workout.name,
                subTitle: TimeUtil.formatDateStringInTimeZone(booking.Class.date + " " + booking.Class.startTime, userContext.userProfile.timezone, "ddd, DD MMM hh:mm a"),
                action: productType === "FITNESS" ? `curefit://cultclass?bookingNumber=${booking.bookingNumber}` : `curefit://mindclass?bookingNumber=${booking.bookingNumber}`,
                image: CatalogueServiceUtilities.getWorkoutImage(booking.Class.Workout.id)
            })
        })
        const header: Header = {
            title: "Upcoming Classes:"
        }
        return new ProductListWidget("MEDIUM", header, actionCards)
    }

    private getPaymentInfo(cultPack: CultPack): SubscriptionPriceView {
        const initialPrice: SubscriptionPriceDetail = {
            title: cultPack.subscriptionPack.renewalCycleUnit === "MONTH" ? "FIRST\nMONTH" : "",
            price: {
                mrp: cultPack.subscriptionPack.initialPrice,
                listingPrice: cultPack.subscriptionPack.initialPrice,
                currency: cultPack.currency
            }
        }
        const renewalPriceInfo = this.getRenewalPrice(cultPack.subscriptionPack)
        const recurringPrice: SubscriptionPriceDetail = {
            title: cultPack.subscriptionPack.renewalCycleUnit === "MONTH" ? "SECOND MONTH\nONWARDS" : "",
            price: {
                mrp: renewalPriceInfo.price,
                listingPrice: renewalPriceInfo.price,
                currency: cultPack.currency
            },
            meta: renewalPriceInfo.meta
        }
        return { initialPrice: initialPrice, recurringPrice: recurringPrice }
    }

    // private async getPageAction(isBuyFlow: boolean, preferedCenter: CultCenter, packInfo: FitnessPack, cultPack: CultPack, membership: CultMembership, cultPackProgress: CultPackProgress, userContext: UserContext): Promise<{
    //     actions: Action[],
    //     isBuyEnabled: boolean
    // }> {

    //     if (!isBuyFlow) {
    //         const isNewClassBookingSupported = AppUtil.isNewClassBookingSuppoted(userContext, (await userContext.userPromise).isInternalUser)
    //         const centerId = preferedCenter && preferedCenter.id ? preferedCenter.id.toString() : undefined
    //         const classBookingAction = ActionUtil.getBookCultClassUrl(packInfo.productType, isNewClassBookingSupported, "subscriptionDetailPage", undefined)

    //         return {
    //             actions: [{
    //                 title: "Book a class",
    //                 url: classBookingAction,
    //                 actionType: "NAVIGATION"
    //             }],
    //             isBuyEnabled: false
    //         }
    //     }

    //     // const isAvailable = CultUtil.isPackAvailableForBuy(cultPack, preferedCenter.id) // DEPRECATED
    //     // if (isAvailable) {
    //         const subscriptionTypeInfo = CultUtil.getSubscriptionTypeInfo(cultPack.subscriptionPack.renewalCycleUnit)
    //         const startDate = this.getStartDate(userContext, cultPack, membership, preferedCenter, cultPackProgress)
    //         const query = {
    //             productId: cultPack.productId,
    //             subscriptionType: subscriptionTypeInfo.subscriptionType,
    //             startDate,
    //             centerId: preferedCenter ? preferedCenter.id : undefined,
    //             autorenewalEnabled: true
    //         }
    //         const actionUrl = `curefit://subscriptioncheckout`
    //         return {
    //             actions: [{
    //                 title: "Subscribe",
    //                 actionType: "NAVIGATION",
    //                 url: actionUrl,
    //                 meta: {
    //                     options: query
    //                 }
    //             }],
    //             isBuyEnabled: true
    //         }
    //     // } else {
    //     //     return {
    //     //         actions: [{
    //     //             title: "Sold out",
    //     //             actionType: "POP_ACTION"
    //     //         }],
    //     //         isBuyEnabled: false
    //     //     }
    //     // }

    // }

    private getStartDate(userContext: UserContext, cultPack: CultPack, membership: CultMembership, preferredCenter: CultCenter, cultPackProgress: CultPackProgress): string {
        const tz = userContext.userProfile.timezone
        const today = TimeUtil.todaysDateWithTimezone(tz)
        let startDate: string
        if (membership && this.isSourceMembership(membership)) {
            if (!membership.allowStartDateSelection) {
                startDate = TimeUtil.addDays(tz, membership.endDate, 1)
                const currentDate = TimeUtil.todaysDate(tz)
                if (TimeUtil.getMomentForDateString(currentDate, tz).valueOf() > (TimeUtil.getMomentForDateString(startDate, tz).valueOf())) {
                    startDate = currentDate
                }
            }
        }
        else {
            // Determining the start date after the current membership end date
            // Not honoring membership start date for gift pack
            if (!cultPack.isGiftPack && cultPackProgress && cultPackProgress.lastPackEndDate >= today) {
                startDate = TimeUtil.getDaysFrom(tz, cultPackProgress.lastPackEndDate, 2)[1]
            }
        }

        return startDate
    }

    private isBuyFlow(membership: CultMembership, forceEnableBuy: boolean) {
        if (!membership || forceEnableBuy) {
            return true
        }
        return false
    }

    private getRenewalPrice(subscriptionPack: FitnessSubscriptionPackDetails): { price: number, meta: string } {
        switch (subscriptionPack.renewalCycleUnit) {
            case "MONTH":
                return { price: subscriptionPack.renewalPrice, meta: "/month*" }
        }
    }

}
@injectable()
export class CultFitSubscriptionDetailViewBuilder extends CultSubscriptionDetailViewBuilder {

    async getCenter(centerId: string): Promise<CultCenter> {
        return this.catalogueService.getCultCenter(centerId)
    }
    getDefaultCenterId(derivedCity: DerivedCityDetails): string {
        return derivedCity.defaultCultCenter
    }

    getPreferredCenterId(userContext: UserContext, derivedCity: DerivedCityDetails): string {
        const centerId = userContext.sessionInfo.sessionData.cultCenterId
        return (centerId && centerId !== "undefined" ? centerId : this.getDefaultCenterId(derivedCity))
    }
    async getOffersPromise(userContext: UserContext, packId: string, preferredCenterId: string): Promise<PackOffersResponse> {
        return undefined
    }
    async getOffersV3Promise(userContext: UserContext, packId: string, preferredCenterId: string): Promise<CultProductPricesResponse> {
        const city = this.cityService.getCityById(userContext.userProfile.cityId)
        const user = await userContext.userPromise
        let centerServiceId
        if (preferredCenterId) {
            centerServiceId = await CultUtil.getCenterServiceIdFromCultCenterId(+preferredCenterId, this.centerService)
        }
        this.logger.info(`offerLog: CultSubscriptionDetailViewBuilder getOffersV3Promise centerServiceId ${centerServiceId}`)
        return await this.offerServiceV3.getCultPackPrices({
            cultCityId: city.cultCityId,
            cityId: city.cityId,
            userInfo: {
                userId: userContext.userProfile.userId,
                deviceId: userContext.sessionInfo.deviceId,
                email: user?.email,
                phone: user?.phone,
                workEmail: user?.workEmail
            },
            source: AppUtil.callSourceFromContext(userContext),
            centerId: preferredCenterId,
            centerServiceId: centerServiceId ? String(centerServiceId) : undefined,
            productIds: [CatalogueServiceV2Utilities.getCultPackProductId(packId)],
        })
    }
    isSourceMembership(membership: CultMembership): boolean {
        if (membership.sourceTenant === "cult.fit")
            return true
        else
            return false
    }

    getBiometricHeader(): string {
        return "One touch access to CULT centres anywhere"
    }

    getWhySubscribeItems(cultPackPageConfig: CultPackPageConfig): InfoCard[] {
        return cultPackPageConfig.whySubscribeItems
    }
}

@injectable()
export class CultMindSubscriptionDetailViewBuilder extends CultSubscriptionDetailViewBuilder {

    getDefaultCenterId(derivedCity: DerivedCityDetails): string {
        return derivedCity.defaultMindCenter
    }

    getPreferredCenterId(userContext: UserContext, derivedCity: DerivedCityDetails): string {
        const centerId = userContext.sessionInfo.sessionData.mindCenterId
        return (centerId && centerId !== "undefined" ? centerId : this.getDefaultCenterId(derivedCity))
    }

    async getOffersPromise(userContext: UserContext, packId: string, preferredCenterId: string): Promise<PackOffersResponse> {
        return undefined
    }

    async getOffersV3Promise(userContext: UserContext, packId: string, preferredCenterId: string): Promise<CultProductPricesResponse> {
        const city = this.cityService.getCityById(userContext.userProfile.cityId)
        const user = await userContext.userPromise
        return await this.offerServiceV3.getMindPackPrices({
            cultCityId: city.cultCityId,
            cityId: city.cityId,
            userInfo: {
                userId: userContext.userProfile.userId,
                deviceId: userContext.sessionInfo.deviceId,
                email: user?.email,
                phone: user?.phone,
                workEmail: user?.workEmail
            },
            source: AppUtil.callSourceFromContext(userContext),
            centerId: preferredCenterId,
            productIds: [CatalogueServiceV2Utilities.getMindPackProductId(packId)],
        })
    }

    // async getPack(cultPack: CultPack): Promise<MindPack> {
    //     return CatalogueServiceUtilities.toMindFitProduct(cultPack)
    // }
    async getCenter(centerId: string): Promise<CultCenter> {
        return this.catalogueService.getCultMindCenter(centerId)
    }

    isSourceMembership(membership: CultMembership): boolean {
        if (membership.sourceTenant === "mind.fit")
            return true
        else
            return false
    }

    getBiometricHeader(): string {
        return "One touch access to mind.fit centres anywhere"
    }

    getWhySubscribeItems(cultPackPageConfig: CultPackPageConfig): InfoCard[] {
        return cultPackPageConfig.whyMindSubscribeItems
    }
}
export default CultSubscriptionDetailViewBuilder
