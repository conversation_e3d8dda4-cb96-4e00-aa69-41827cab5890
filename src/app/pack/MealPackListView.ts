import { DeliveryChannel, FoodPack, FoodPackOption, MealSlot, MealSlots } from "@curefit/eat-common"
import { ProductPrice } from "@curefit/product-common"
import { capitalizeFirstLetter, pad, Timezone } from "@curefit/util-common"
import { UrlPathBuilder } from "@curefit/product-common"
import { OfferUtil, SeoUrlParams } from "@curefit/base-utils"
import * as _ from "lodash"
import EatUtil from "../util/EatUtil"
import { FoodPackOffersResponseV2 } from "@curefit/offer-common"
import { MealUtil } from "@curefit/base-utils"
import { ActionUtil } from "@curefit/base-utils"
import { UserContext } from "@curefit/userinfo-common"

class MealPackView {
    constructor(userContext: UserContext, packModel: FoodPack, packOffersResponse: FoodPackOffersResponseV2) {
        // In beta, we will have only one pack option
        const foodPackOption: FoodPackOption = MealUtil.findDefaultSubscriptionOption(packModel, packOffersResponse)
        const packOfferAndPrice = OfferUtil.getFoodPackOfferAndPrice(packModel, foodPackOption, packOffersResponse)
        if (foodPackOption.subscriptionType !== undefined) {
            packOfferAndPrice.price.mrp = Math.round(packOfferAndPrice.price.mrp / foodPackOption.numTickets)
            packOfferAndPrice.price.listingPrice = Math.round(packOfferAndPrice.price.listingPrice / foodPackOption.numTickets)
        }
        const seoParams: SeoUrlParams = {
            productName: packModel.title
        }
        this.id = packModel.productId
        this.title = packModel.title
        this.subTitle = foodPackOption.subscriptionType === undefined ? `${pad(foodPackOption.numTickets, 1)} meals at` : `Starting from`
        this.image = UrlPathBuilder.getPackImagePath(packModel.productId, "FOOD", "MAGAZINE", packModel.imageVersion, userContext.sessionInfo.userAgent)
        this.isVeg = packModel.isVeg
        this.price = packOfferAndPrice.price
        this.action = ActionUtil.foodPack(packModel.productId, undefined, foodPackOption.numTickets, true, userContext.sessionInfo.userAgent, seoParams)
        this.isOfferApplied = !_.isEmpty(packOfferAndPrice.offers)
    }
    public id: string
    public title: string
    public subTitle: string
    public image: string
    public isVeg: boolean
    public price: ProductPrice
    public action: string
    public isOfferApplied: boolean
}

class MealPackListView {
    constructor(userContext: UserContext, mealPacks: FoodPack[], packOffersResponse: FoodPackOffersResponseV2,
        availableMealSlots: MealSlot[], areaId: string, deliveryChannel: DeliveryChannel, packTz: Timezone) {
        let isOfferAvailable: boolean = false
        MealSlots.forEach(mealSlot => {
            this.list[mealSlot] = []
        })
        mealPacks.sort((a, b) => {
            return a.ordering < b.ordering ? -1 : 1
        })
        mealPacks.forEach(mealPack => {
            const mealPackView: MealPackView = new MealPackView(userContext, mealPack, packOffersResponse)
            this.list[mealPack.mealSlot].push(mealPackView)
            if (mealPackView.price.listingPrice < mealPackView.price.mrp)
                isOfferAvailable = true
        })
        MealSlots.forEach(mealSlot => {
            if (!_.isEmpty(this.list[mealSlot])) {
                this.section.push({
                    "id": mealSlot,
                    "mealName": capitalizeFirstLetter(mealSlot)
                })
            }
        })
        this.selectedTabIndex = EatUtil.getSelectedMealSlotIndex(availableMealSlots, deliveryChannel, packTz)
        this.isOfferAvailable = isOfferAvailable
    }
    public isOfferAvailable: boolean = false
    public selectedTabIndex: number = 0
    public section: { "id": string, "mealName": string }[] = []
    public list: { [id: string]: MealPackView[] } = {}

}
export default MealPackListView
