import { ConsultationProduct, DiagnosticProduct } from "@curefit/care-common"
import { CustomerIssueType } from "@curefit/issue-common"
import {
    AggregatedMembershipInfo,
    BookingDetail,
    BundleSessionSellableProduct,
    CareTeam,
    DiagnosticsTestOrderResponse,
    DOCTOR_TYPE,
    Patient
} from "@curefit/albus-client"
import {
    Action,
    Header,
    InfoCard,
    ManageOptionPayload,
    ManageOptions,
    PackProgress,
    ProductDetailPage,
    ProductListWidget,
    WidgetView
} from "../common/views/WidgetView"
import {
    ActionableCardItem,
    CareTeamItem,
    CareTeamWidget,
    DiagnosticTestReportItem,
    HorizontalActionableCardListingWidget
} from "../page/PageWidgets"
import { CareUtil, NEW_MIND_THERAPY_SPECIALITY_BOOKING_PAGE_SUPPORTED } from "../util/CareUtil"
import * as _ from "lodash"
import { TimeUtil } from "@curefit/util-common"
import { OfferUtil } from "@curefit/base-utils"
import { PackOffersResponse } from "@curefit/offer-common"
import { ActionUtil } from "@curefit/base-utils"
import TherapyPageConfig from "../therapy/TherapyPageConfig"
import { UserContext } from "@curefit/userinfo-common"
import { SUPPORT_DEEP_LINK } from "../util/AppUtil"

class MindTherapyBookingInfoProductPageView extends ProductDetailPage {
    public pageContext: any
    constructor(
        userContext: UserContext,
        careTeam: CareTeam[],
        product: DiagnosticProduct,
        bookingInfo: BookingDetail,
        therapyPageConfig: TherapyPageConfig,
        issuesMap: Map<string, CustomerIssueType[]>,
        newReportIssueManageOption: ManageOptionPayload,
        consultations: BookingDetail[],
        patientList: Patient[],
        aggregatedMembershipInfo: AggregatedMembershipInfo,
        offerResponse: PackOffersResponse,
        bundleProduct?: BundleSessionSellableProduct
    ) {
        super()
        this.widgets.push(this.getSummaryWidget(userContext, product, bookingInfo, issuesMap,
            newReportIssueManageOption, aggregatedMembershipInfo))
        if (!_.isEmpty(careTeam)) {
            this.widgets.push(this.getCareTeamWidget(userContext, careTeam))
        }
        if (!_.isEmpty(consultations)) {
            this.widgets.push(this.getConsultationsWidget(userContext, consultations))
        }
        if (bundleProduct) {
            const { packPdpHowItWorks } = CareUtil.getPackContent(bundleProduct)
            this.widgets.push(CareUtil.getHowItHelpsWidget(packPdpHowItWorks))
        } else {
            this.widgets.push(this.getHowItWorksWidget(therapyPageConfig))
        }

        if (!_.isEmpty(aggregatedMembershipInfo)) {
            this.actions = this.getPostBookingActions(userContext, patientList, aggregatedMembershipInfo, offerResponse, bookingInfo)
        }
    }

    private getSummaryWidget(
        userContext: UserContext,
        product: DiagnosticProduct, bookingDetail: BookingDetail,
        issuesMap: Map<string, CustomerIssueType[]>,
        newReportIssueManageOption: ManageOptionPayload,
        aggregatedMembershipInfo: AggregatedMembershipInfo): WidgetView {
        let manageOptionsView: { manageOptions: ManageOptions; meta: any } = null
        manageOptionsView = this.getManageOptions(bookingDetail, bookingDetail.booking.cfOrderId,
            bookingDetail.booking.productCode, issuesMap, newReportIssueManageOption)
        const summaryWidget: WidgetView & {
            productId: string
            title: string
            subTitle: string
            image: string
            meta: any
            packProgress: PackProgress
            manageOptions: ManageOptions,
            hasDividerBelow: boolean
        } = {
            widgetType: "HEALTHCHECKUP_SUMMARY_WIDGET",
            subTitle: this.getSubTitle(bookingDetail, aggregatedMembershipInfo),
            title: this.getSetCodeBasedTitle(product),
            productId: product.productId,
            image: product.heroImageUrl,
            manageOptions: manageOptionsView.manageOptions,
            meta: manageOptionsView.meta,
            packProgress: !_.isEmpty(aggregatedMembershipInfo) && this.getPackProgress(userContext, aggregatedMembershipInfo, bookingDetail),
            hasDividerBelow: false
        }
        return summaryWidget
    }

    getSetCodeBasedTitle(diagnosticsProduct: DiagnosticProduct) {
        switch (diagnosticsProduct?.setCode) {
            case "COUPLE_THERAPY_SET": return `Couple Therapy - ${diagnosticsProduct.title}`
            case "MIND_THERAPY_SET": return `Therapy - ${diagnosticsProduct.title}`
            default: return diagnosticsProduct.title
        }
    }

    getPackProgress(userContext: UserContext, aggregatedMembershipInfo: AggregatedMembershipInfo, bookingDetail: BookingDetail): PackProgress {
        const totalSessions = aggregatedMembershipInfo.totalTickets
        const completedSessions = aggregatedMembershipInfo.totalTicketsConsumed // _.filter(bookingDetail.childBookingInfos, childBooking => childBooking.booking.status === "COMPLETED").length
        let showViewPlan = false
        if (completedSessions > 0) {
            showViewPlan = true
        }
        let packProgress: PackProgress
        if (_.isEmpty(aggregatedMembershipInfo.startEndEpoch)) {
            return
        }
        const { total, completed, isCompleted, endDateFormatted } = CareUtil.getConsultationPackProgressInfo(userContext, aggregatedMembershipInfo, true)
        if (userContext.sessionInfo.userAgent === "APP") {
            packProgress = {
                total,
                completed,
                state: "ACTIVE",
                type: "THERAPY",
                leftText: `${completedSessions}/${totalSessions} Session Booked`,
                rightText: endDateFormatted,
                progressBarTextStyle: { marginTop: 10, fontSize: 14 },
                progressBarColor: "#008300",
                isSplitView: true
            }
        } else {
            packProgress = {
                title: `${completedSessions}/${totalSessions} Session Booked` ,
                subTitle: endDateFormatted,
                total,
                completed,
                state: isCompleted ? "COMPLETED" : "ACTIVE",
                packColor: "#008300",
                type: "THERAPY"
            }
        }
        return packProgress
    }

    private getIssueList(issuesMap: Map<string, CustomerIssueType[]>): { code: string, title: string, confirmation: string }[] {
        const issueList: { code: string, title: string, confirmation: string }[] = []
        issuesMap.get("DiagnosticsSingleBooked").forEach(customerIssueType => {
            issueList.push({
                code: customerIssueType.code,
                title: customerIssueType.subject,
                confirmation: customerIssueType.confirmation
            })
        })
        return issueList
    }

    public getSubTitle(bookingDetail: BookingDetail, aggregatedMembershipInfo: AggregatedMembershipInfo) {
        if (bookingDetail.booking.status === "CANCELLED") {
            return "Cancelled"
        }
        if (_.isEmpty(aggregatedMembershipInfo)) {
            return _.get(bookingDetail, "bundleOrderResponse.patient.name") ? `For: ${bookingDetail.bundleOrderResponse.patient.name} | Pack Expired` : undefined
        }
        return _.get(bookingDetail, "bundleOrderResponse.patient.name") ? `For: ${bookingDetail.bundleOrderResponse.patient.name}` : undefined
    }

    private getManageOptions(bookingDetail: BookingDetail, orderId: string, productId: string,
        issuesMap: Map<string, CustomerIssueType[]>,
        newReportIssueManageOption: ManageOptionPayload): { manageOptions: ManageOptions, meta: any } {
        const options: ManageOptionPayload[] = []
        if (newReportIssueManageOption) {
            options.push(newReportIssueManageOption)
        } else {
            options.push(
                {
                    isEnabled: true,
                    displayText: "Need Help",
                    type: "REPORT_ISSUE",
                    meta: this.getIssueList(issuesMap),
                    action: SUPPORT_DEEP_LINK,
                }
            )
        }

        const manageOptions: ManageOptions = {
            displayText: "...",
            options: options
        }

        const meta: any = {
            orderId: orderId,
            productId: productId,
            tcBookingId: bookingDetail.booking.id
        }
        return { manageOptions: manageOptions, meta: meta }
    }

    private getCareTeamWidget(userContext: UserContext, careTeam: CareTeam[]): CareTeamWidget {
        const careTeamViewItems: CareTeamItem[] = []
        careTeam.filter(item => item.doctorTypeCode.code !== "SUPPORT_GROUP")
                .map(item => {
            careTeamViewItems.push({
                title: item.doctor.name,
                subTitle: item.doctorTypeCode.displayValue,
                imageUrl: item.doctor.displayImage,
                actions: this.getCareTeamActions(userContext, item)
            })
        })
        if (_.isEmpty(careTeamViewItems)) {
            return undefined
        }
        return new CareTeamWidget(careTeamViewItems, "Team")
    }

    private getReportsWidget(reports: DiagnosticsTestOrderResponse[]): HorizontalActionableCardListingWidget {
        const cardItems: DiagnosticTestReportItem[] = []
        reports.map(report => cardItems.push(this.getReportActionableCardWidget(report)))
        return {
            widgetType: "HORIZONTAL_ACTIONAL_CARD_LISTING_WIDGET",
            title: "Reports",
            type: "REPORT",
            cardItems
        }
    }

    getReportActionableCardWidget(report: DiagnosticsTestOrderResponse): DiagnosticTestReportItem {
        const reportInfo = report.finalDiagnosticReport.diagnosticCheckUpReportInfo
        const testDetails = CareUtil.getTestReportDetailsView(reportInfo)
        return {
            header: {
                title: reportInfo.testId,
                // Not used by any function (Convert to User Profile Timezone instead of Asia/Kolkata)
                subtitle: `${TimeUtil.formatEpochInTimeZone(TimeUtil.IST_TIMEZONE, report.inCentreDiagnosticOrder.slot.workingStartTime, "D MMM")}`,
                action: {
                    url: ActionUtil.diagnosticReportPage(report.orderId, report.carefitOrderId),
                    actionType: "NAVIGATION"
                }
            },
            testInfos: testDetails
        }
    }

    private getConsultationsWidget(userContext: UserContext, consultations: BookingDetail[]): HorizontalActionableCardListingWidget {
        const cardItems: ActionableCardItem[] = []
        consultations.map(consultation => cardItems.push(this.getConsultationActionableCardWidget(userContext, consultation)))
        return {
            widgetType: "HORIZONTAL_ACTIONAL_CARD_LISTING_WIDGET",
            title: "Your Sessions",
            type: "CONSULTATION",
            cardItems
        }
    }

    getConsultationActionableCardWidget(userContext: UserContext, booking: BookingDetail): ActionableCardItem {
        const actions: Action[] = this.getActions(userContext, booking)
        const tz = userContext.userProfile.timezone
        const vertical = CareUtil.getVerticalForConsultation(booking?.consultationOrderResponse?.consultationProduct?.doctorType)
        return {
            tag: CareUtil.getConsultationTag(booking.consultationOrderResponse.consultationUserState, booking.consultationOrderResponse.status),
            title: `Consultation with ${booking.consultationOrderResponse.doctor.name}`,
            subTitle: `for ${booking.consultationOrderResponse.patient.name}`,
            imageUrl: booking.consultationOrderResponse.doctor.displayImage,
            footer: CareUtil.getUpcomingConsultationFooterInfo(booking.consultationOrderResponse, tz),
            actions: actions,
            cardAction: {
                actionType: "NAVIGATION",
                url: ActionUtil.teleconsultationSingle(userContext, booking.booking.productCode, booking?.consultationOrderResponse?.consultationProduct.urlPath, booking.consultationOrderResponse.bookingId.toString(), undefined, vertical)
            }
        }
    }

    private getHowItWorksWidget(pageConfig: TherapyPageConfig): ProductListWidget {
        const header: Header = {
            title: pageConfig.howItWorksTitle,
            color: "#000000"
        }
        const infoCards: InfoCard[] = []
        pageConfig.howItWorksItemList.forEach(item => {
            infoCards.push({
                subTitle: item.text,
                icon: item.icon
            })
        })
        const widget = new ProductListWidget("SMALL", header, infoCards)
        widget.hasDividerBelow = false
        return widget
    }

    private getActions(userContext: UserContext, booking: BookingDetail): Action[] {
        const actions: Action[] = []
        const consultationOrderResponse = booking.consultationOrderResponse
        if (consultationOrderResponse.consultationUserState === "COMPLETED" && !CareUtil.isMindDoctorType(consultationOrderResponse.doctor.type) && consultationOrderResponse.doctor.type !== "LIFESTYLE_COACH") {
            actions.push({
                title: "Prescription",
                actionType: "NAVIGATION",
                icon: "PRESCRIPTION",
                url: `curefit://carefitPrescription?tcBookingId=${booking.booking.id}&productId=${booking.booking.productCode}`
            })
        }

        // if (consultationOrderResponse.consultationUserState === "COMPLETED" && consultationOrderResponse.followUpContext && consultationOrderResponse.followUpContext.enabled) {
        //     const parentBookingId = booking.booking.id
        //     const followUpConsultationId = booking.consultationOrderResponse.followUpConsultationId

        //     actions.push(CareUtil.getFollowupAction(parentBookingId, consultationOrderResponse.followupProducts, followUpConsultationId, consultationOrderResponse.patient.id))
        // }

        if (consultationOrderResponse.appointmentActionsWithContext && consultationOrderResponse.appointmentActionsWithContext.chatActionWithContext && consultationOrderResponse.appointmentActionsWithContext.chatActionWithContext.action.actionPermitted) {
            const chatAction: any = CareUtil.getChatMessageAction(
                userContext,
                _.get(consultationOrderResponse, "appointmentActionsWithContext.chatActionWithContext", null),
                consultationOrderResponse.patient.id,
                consultationOrderResponse.doctor?.name,
                CareUtil.getChatChannelWithAppointmentContext(consultationOrderResponse.appointmentActionsWithContext),
                consultationOrderResponse.doctor?.displayImage,
                consultationOrderResponse.doctor?.qualification,
                booking.booking.id,
                null,
                "mindtherapy"
            )
            chatAction && actions.push(chatAction)
        }
        if (actions.length <= 2) {
            if (consultationOrderResponse.appointmentActionsWithContext && consultationOrderResponse.appointmentActionsWithContext.rescheduleActionWithContext && consultationOrderResponse.appointmentActionsWithContext.rescheduleActionWithContext.action.actionPermitted) {
                actions.push({
                    title: "Reschedule",
                    isEnabled: true,
                    actionType: "RESCHEDULE_TC",
                    icon: "RESCHEDULE",
                    url: `curefit://rescheduleTc?parentBookingId=${consultationOrderResponse.bookingId}&isReschedule=true`,
                    meta: {
                        "tcBookingId": consultationOrderResponse.bookingId,
                        "centerId": consultationOrderResponse.center.id,
                        "productId": booking.booking.productCode,
                        "patientId": consultationOrderResponse.patient.id
                    }
                })
            }
        }
        return actions
    }
    private getCareTeamActions(userContext: UserContext, item: CareTeam): Action[] {
        const actions: Action[] = []
        if (item.chatActionWithContext && item.chatActionWithContext.action && item.chatActionWithContext.action.actionPermitted) {
            const chatAction: any = CareUtil.getChatMessageAction(
                userContext,
                _.get(item, "appointmentActionsWithContext.chatActionWithContext", null),
                item.patientId,
                item.doctor?.name,
                item.chatActionWithContext.context.twilioCommunicationMode.modeName,
                item.doctor?.displayImage,
                item.doctor?.qualification,
                null,
                null,
                "mindtherapy"
            )
            chatAction && actions.push(chatAction)
        }
        return actions
    }

    private getPostBookingActions(userContext: UserContext, patientList: Patient[], aggregatedMembershipInfo: AggregatedMembershipInfo, offerResponse: PackOffersResponse, bookingInfo: BookingDetail): Action[] {
        const pageActions: Action[] = []
        const consultationProducts = _.map(aggregatedMembershipInfo.productMetas, productMeta => <ConsultationProduct>productMeta.productDetail)

        if (userContext.sessionInfo.appVersion >= NEW_MIND_THERAPY_SPECIALITY_BOOKING_PAGE_SUPPORTED) {
            let consultationProduct = consultationProducts.find(item => item.productId !== "CONS_MIND_PSY_ONLINE" && item.productId !== "CONS_MIND_PSY_INCENTRE")
            consultationProduct = consultationProduct || consultationProducts[0]
            const action = CareUtil.getSelectSpecialityAction(userContext, "BOOK SESSION", consultationProduct, bookingInfo.booking.id, bookingInfo.booking.patientId, true)
            if (aggregatedMembershipInfo.totalTickets - aggregatedMembershipInfo.totalTicketsConsumed <= 0) {
                action.disabled = true
            }
            pageActions.push(action)
        } else {
            const offerDetails = OfferUtil.getPackOfferAndPrice(consultationProducts[0], offerResponse)
            const offerIds = offerDetails.offers.map(offer => offer.offerId)
            const onlineConsultationProductCodesMap = new Map<DOCTOR_TYPE, string>()
            const incentreConsultationProductCodesMap = new Map<DOCTOR_TYPE, string>()
            consultationProducts.forEach(consultationProduct => {
                if (consultationProduct.consultationMode === "ONLINE") {
                    onlineConsultationProductCodesMap.set(consultationProduct.doctorType, consultationProduct.productId)
                }
                else {
                    incentreConsultationProductCodesMap.set(consultationProduct.doctorType, consultationProduct.productId)
                }
            })
            const onlineBookingAction = CareUtil.getTherapistSelectionModalAction(onlineConsultationProductCodesMap, offerIds, patientList, "Video Call", bookingInfo.booking.id, "packpage_online")
            const inCentreBookingAction = CareUtil.getTherapistSelectionModalAction(incentreConsultationProductCodesMap, offerIds, patientList, "Visit Center", bookingInfo.booking.id, "packpage_visitcenter")
            pageActions.push(onlineBookingAction)
            pageActions.push(inCentreBookingAction)
        }
        return pageActions
    }
}

export default MindTherapyBookingInfoProductPageView
