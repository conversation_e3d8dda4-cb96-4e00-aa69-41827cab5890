import { inject, injectable } from "inversify"
import { DEFAULT_CACHE_REFRESH_TIME_IN_SECONDS, InMemoryCacheService } from "@curefit/memory-cache"
import { IPageConfigReadOnlyDao, IPageConfigReadWriteDao, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import * as _ from "lodash"
import { UrlPathBuilder } from "@curefit/product-common"
import { HowItWorksItem } from "@curefit/product-common"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

@injectable()
class FitnessFirstPackPageConfig extends InMemoryCacheService<any> {

    constructor(
        @inject(PAGE_CONFIG_TYPES.PageConfigReadOnlySecondaryDao) protected pageConfigDao: IPageConfigReadOnlyDao,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger, 32 * 60)
        this.load("FitnessFirstPackPageConfig")
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    loadData(): Promise<any> {
        return this.pageConfigDao.findOne({ pageId: { $eq: "FitnessFirstPageConfig" } }).then(pageConfig => {
            const data = pageConfig.data
            this.howItWorksTitle = data.howItWorksTitle
            this.howItWorksItemList = _.map(<HowItWorksItem[]>data.howItWorksItemList, item => {
                return {
                    text: item.text,
                    icon: UrlPathBuilder.getIconPath("HOWITWORKS", item.icon, 1)
                }
            })
            return data
        })
    }

    public howItWorksTitle: string
    public howItWorksItemList: HowItWorksItem[]
}

export default FitnessFirstPackPageConfig
