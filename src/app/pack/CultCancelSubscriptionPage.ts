import { injectable } from "inversify"
import { Action, ExpandableListWidget, ProductListWidget, WidgetView } from "../common/views/WidgetView"
import CultPackPageConfig from "./CultPackPageConfig"
import { ProductType } from "@curefit/product-common"
import { CultMembership } from "@curefit/cult-common"
import CultUtil from "../util/CultUtil"
import { ActionUtil } from "@curefit/base-utils"
import * as _ from "lodash"
import { UserContext } from "@curefit/userinfo-common"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"
import CatalogueServiceUtilities from "../util/CatalogueServiceUtilities"

@injectable()
export class CultCancelSubscriptionPage { // Deprecated flow, TODO: Cleanup
    widgets: (WidgetView | ExpandableListWidget)[] = []
    constructor(userContext: UserContext, pageType: string, productType: ProductType, membership: CultMembership, packInfo: OfflineFitnessPack, cultPackPageConfig: CultPackPageConfig) {
        this.widgets.push(new ProductListWidget("INFO_ROW", { "title": "Don't lose out on these awesome benefits" },
            productType === "FITNESS" ? cultPackPageConfig.whySubscribeItems : cultPackPageConfig.whyMindSubscribeItems
        ))
        const membershipId = membership.id
        const isPauseAllowed = membership.userSubscription.remainingPauses > 0 && membership.remainingPauseDays > 0
        // const pauseAction = CultUtil.pauseSubscriptionPackAction(userContext, productType, membership, cultPackPageConfig, packInfo)
        // const packId = CatalogueServiceUtilities.extractPackId(packInfo.productId).toString()
        const initialPrice = membership.userSubscription.initialPrice
        const travelActions: Action[] = []
        // travelActions.push(this.getCancelSubscriptionAction(packId, membershipId, initialPrice, productType, "Still Cancel", "TRAVEL"))
        // if (!_.isEmpty(pauseAction)) {
        //     pauseAction.title = "Pause"
        //     travelActions.push(pauseAction)
        // }
        // this.widgets.push(productType === "FITNESS" ? this.getCultExpandableListWidget(productType, membershipId, packId, isPauseAllowed, travelActions, initialPrice) : this.getMindExpandableListWidget(productType, membershipId, packId, isPauseAllowed, travelActions, initialPrice))
    }

    getCultExpandableListWidget(productType: ProductType, membershipId: number, packId: string, isPauseAllowed: boolean, travelActions: Action[], initialPrice: number): WidgetView | ExpandableListWidget {
        return {
            widgetType: "EXPANDABLE_LIST_WIDGET",
            title: "Tell us why you want to cancel",
            allowMultipleOpen: false,
            showDivider: true,
            widgets: [
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "I'm travelling out of town",
                    rebuttal: isPauseAllowed && `Pause your subscription in case you are unable to attend classes like when you are travelling or you are not well `,
                    actions: travelActions
                },
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "I am purchasing another pack",
                    cancelPrompt: "We are excited to see your commitment towards fitness. We'd love to know your feedback on subscription",
                    actions: [this.getCancelSubscriptionAction(packId, membershipId, initialPrice, productType, "Cancel Subscription", "CHANGE_PACK")]
                },
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "I do not like the classes",
                    cancelPrompt: "We’re terribly sorry to hear that. We’re constantly innovating to improve our class experience. We’d love to hear what we could’ve done better",
                    actions: [this.getCancelSubscriptionAction(packId, membershipId, initialPrice, productType, "Cancel Subscription", "BAD_CLASSES")]
                },
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "I’m not sure if Cult.fit can help reach my fitness goals",
                    cancelPrompt: "We are terribly sorry that you feel this way. We’d love to hear how can we help you reach your fitness goals better",
                    actions: [this.getCancelSubscriptionAction(packId, membershipId, initialPrice, productType, "Cancel Subscription", "GOAL_NOT_MET")]
                },
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "I am busy and hence can’t be regular to classes",
                    rebuttal: "We know being regular is tough but 1 out of 24 hours is just 4% of your time :)  You can still decide to give your best this time!",
                    actions: [this.getCancelSubscriptionAction(packId, membershipId, initialPrice, productType, "Still Cancel", "BUSY"), {
                        actionType: "NAVIGATION",
                        url: ActionUtil.bookCultClass("cultCancelSubscription"),
                        title: "Book a Class"
                    }]
                },
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "My reason is not listed",
                    cancelPrompt: "We are sorry to let you go! Tell us what went wrong",
                    actions: [this.getCancelSubscriptionAction(packId, membershipId, initialPrice, productType, "Cancel Subscription", "NOT_LISTED")]
                }
            ]
        }
    }

    getMindExpandableListWidget(productType: ProductType, membershipId: number, packId: string, isPauseAllowed: boolean, travelActions: Action[], initialPrice: number): WidgetView | ExpandableListWidget {
        return {
            widgetType: "EXPANDABLE_LIST_WIDGET",
            title: "Tell us why you want to cancel",
            allowMultipleOpen: false,
            showDivider: true,
            widgets: [
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "I'm travelling out of town",
                    rebuttal: isPauseAllowed && `Pause your subscription in case you are unable to attend classes like when you are travelling or you are not well `,
                    actions: travelActions
                },
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "I am purchasing another pack",
                    cancelPrompt: "We are excited to see your commitment towards fitness. We'd love to know your feedback on subscription",
                    actions: [this.getCancelSubscriptionAction(packId, membershipId, initialPrice, productType, "Cancel Subscription", "CHANGE_PACK")]
                },
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "I do not like the classes",
                    cancelPrompt: "We’re terribly sorry to hear that. We’re constantly innovating to improve our class experience. We’d love to hear what we could’ve done better",
                    actions: [this.getCancelSubscriptionAction(packId, membershipId, initialPrice, productType, "Cancel Subscription", "BAD_CLASSES")]
                },
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "I’m not sure if Mind.fit can help reach my goals",
                    cancelPrompt: "We are terribly sorry that you feel this way. We’d love to hear how can we help you reach your fitness goals better",
                    actions: [this.getCancelSubscriptionAction(packId, membershipId, initialPrice, productType, "Cancel Subscription", "GOAL_NOT_MET")]
                },
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "I am busy and hence can’t be regular to classes",
                    rebuttal: "We know being regular is tough but 1 out of 24 hours is just 4% of your time :)  You can still decide to give your best this time!",
                    actions: [this.getCancelSubscriptionAction(packId, membershipId, initialPrice, productType, "Still Cancel", "BUSY"), {
                        actionType: "NAVIGATION",
                        url: ActionUtil.bookMindClass("mindCancelSubscription"),
                        title: "Book a Class"
                    }]
                },
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "My reason is not listed",
                    cancelPrompt: "We are sorry to let you go! Tell us what went wrong",
                    actions: [this.getCancelSubscriptionAction(packId, membershipId, initialPrice, productType, "Cancel Subscription", "NOT_LISTED")]
                }
            ]
        }
    }

    private getCancelSubscriptionAction(packId: string, membershipId: number, initialPrice: number, productType: ProductType, actionTitle: string, reasonCode: string): Action {
        return {
            actionType: "CANCEL_CULT_SUBSCRIPTION",
            title: actionTitle,
            meta: {
                alertTitle: "Cancel Subcription",
                alertMessage: `Are you sure? First month price of Rs.${initialPrice} will be applied the next time you purchase a subscription.`,
                packId: packId,
                membershipId: membershipId,
                productType: productType,
                reasonCode: reasonCode
            }

        }
    }
}
