import { inject, injectable } from "inversify"
import { DEFAULT_CACHE_REFRESH_TIME_IN_SECONDS, InMemoryCacheService } from "@curefit/memory-cache"
import { IPageConfigReadOnlyDao, IPageConfigReadWriteDao, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import * as _ from "lodash"
import { UrlPathBuilder } from "@curefit/product-common"
import { HowItWorksItem } from "@curefit/product-common"
import { InfoCard } from "../common/views/WidgetView"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

@injectable()
class MealPackPageConfig extends InMemoryCacheService<any> {

    constructor(
        @inject(PAGE_CONFIG_TYPES.PageConfigReadOnlySecondaryDao) protected pageConfigDao: IPageConfigReadOnlyDao,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger, 31 * 60)
        this.load("MealPackPageConfig")
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    loadData(): Promise<any> {
        return this.pageConfigDao.findOne({ pageId: { $eq: "MealPackPageConfig" } }).then(pageConfig => {
            const data = pageConfig.data
            this.isAvailabilityCheckNeeded = data.isAvailabilityCheckNeeded
            this.howItWorksTitle = data.howItWorksTitle
            this.howItWorksItemList = _.map(<HowItWorksItem[]>data.howItWorksItemList, item => {
                return {
                    text: item.text,
                    icon: UrlPathBuilder.getIconPath("HOWITWORKS", item.icon, 1)
                }
            })
            this.howItWorksItemListV2 = _.map(<HowItWorksItem[]>data.howItWorksItemListV2, item => {
                return {
                    text: item.text,
                    icon: UrlPathBuilder.getIconPath("HOWITWORKS", item.icon, 1)
                }
            })
            this.howItWorksItemListSubscription = _.map(<HowItWorksItem[]>data.howItWorksItemListSubscription, item => {
                return {
                    text: item.text,
                    icon: UrlPathBuilder.getIconPath("HOWITWORKS", item.icon, 1)
                }
            })
            this.howItWorksItemListKiosk = _.map(<HowItWorksItem[]>data.howItWorksItemListKiosk, item => {
                return {
                    text: item.text,
                    icon: UrlPathBuilder.getIconPath("HOWITWORKS", item.icon, 1)
                }
            })
            this.howItWorksItemListWL = _.map(<HowItWorksItem[]>data.howItWorksItemListWL, item => {
                return {
                    text: item.text,
                    icon: UrlPathBuilder.getIconPath("HOWITWORKS", item.icon, 1)
                }
            })
            this.howItWorksItemListKioskPack = _.map(<HowItWorksItem[]>data.howItWorksItemListKioskPack, item => {
                return {
                    text: item.text,
                    icon: UrlPathBuilder.getIconPath("HOWITWORKS", item.icon, 1)
                }
            })
            this.howItWorksItemListAllStar = _.map(<HowItWorksItem[]>data.howItWorksItemListAllStar, item => {
                return {
                    text: item.text,
                    icon: UrlPathBuilder.getIconPath("HOWITWORKS", item.icon, 1)
                }
            })
            this.howItWorksItemListWeightWise = _.map(<HowItWorksItem[]>data.howItWorksItemListWeightWise, item => {
                return {
                    text: item.text,
                    icon: UrlPathBuilder.getIconPath("HOWITWORKS", item.icon, 1)
                }
            })
            this.howItWorksItemListWeightWisePlus = _.map(<HowItWorksItem[]>data.howItWorksItemListWeightWisePlus, item => {
                return {
                    text: item.text,
                    icon: UrlPathBuilder.getIconPath("HOWITWORKS", item.icon, 1)
                }
            })
            this.whySubscribeItems = _.map(<HowItWorksItem[]>data.whySubscribe, item => {
                return {
                    title: item.text,
                    image: item.icon
                }
            })
            this.whySubscribeKioskPackItems = _.map(<HowItWorksItem[]>data.whySubscribeKioskPack, item => {
                return {
                    title: item.text,
                    image: item.icon
                }
            })
            this.whySubscribeWLPackItems = _.map(<HowItWorksItem[]>data.whySubscribeWLPackItems, item => {
                return {
                    title: item.text,
                    image: item.icon
                }
            })
            this.whySubscribeAllStar = _.map(<HowItWorksItem[]>data.whySubscribeAllStar, item => {
                return {
                    title: item.text,
                    image: item.icon
                }
            })
            this.whySubscribeWeightWise = _.map(<HowItWorksItem[]>data.whySubscribeWeightWise, item => {
                return {
                    title: item.text,
                    image: item.icon
                }
            })
            this.whySubscribeWeightWisePlus = _.map(<HowItWorksItem[]>data.whySubscribeWeightWisePlus, item => {
                return {
                    title: item.text,
                    image: item.icon
                }
            })
            return data
        })
    }

    public isAvailabilityCheckNeeded: boolean

    public howItWorksTitle: string
    public howItWorksItemList: HowItWorksItem[]
    public howItWorksItemListV2: HowItWorksItem[]
    public howItWorksItemListSubscription: HowItWorksItem[]
    public howItWorksItemListAllStar: HowItWorksItem[]
    public howItWorksItemListWeightWise: HowItWorksItem[]
    public howItWorksItemListWeightWisePlus: HowItWorksItem[]
    public howItWorksItemListKiosk: HowItWorksItem[]
    public howItWorksItemListWL: HowItWorksItem[]
    public howItWorksItemListKioskPack: HowItWorksItem[]
    public whySubscribeItems: InfoCard[]
    public whySubscribeWLPackItems: InfoCard[]
    public whySubscribeKioskPackItems: InfoCard[]
    public whySubscribeAllStar: InfoCard[]
    public whySubscribeWeightWise: InfoCard[]
    public whySubscribeWeightWisePlus: InfoCard[]

}

export default MealPackPageConfig
