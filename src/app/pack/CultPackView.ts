import { ProductPrice } from "@curefit/product-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import CultUtil from "../util/CultUtil"
import { UrlPathBuilder } from "@curefit/product-common"
import { CultProductPricesResponse, PackOffersResponse } from "@curefit/offer-common"
import { OfferUtil } from "@curefit/base-utils"
import { IS_NEW_CULT_OFFER_VERSION } from "@curefit/cult-client"
import { ActionUtil } from "@curefit/base-utils"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"
import CatalogueServiceUtilities from "../util/CatalogueServiceUtilities"


abstract class CultPackView {
    constructor(userAgent: UserAgent, packModel: OfflineFitnessPack, packOffersV3Response?: CultProductPricesResponse) {
        this.productId = packModel.productId
        this.title = packModel.title
        this.subTitle = CultUtil.getDescriptionFromPackId(packModel.productId)
        this.image =  CatalogueServiceUtilities.getFitnessProductImage(packModel, "MAGAZINE", userAgent)
        if (IS_NEW_CULT_OFFER_VERSION) {
            const offerDetails = CultUtil.getOfferDetailsPMS(packModel, packOffersV3Response)
            this.price = offerDetails.price
        } else {
            this.price = packModel.price
        }
        this.action = CatalogueServiceUtilities.getPackPageAction(packModel, userAgent)
    }
    public productId: string
    public title: string
    public subTitle: string
    public image: string
    public price: ProductPrice
    public action: string
}

export class CultFitnessPackView extends CultPackView {
}

export class CultMindPackView extends CultPackView {
}
