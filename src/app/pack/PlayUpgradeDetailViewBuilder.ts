import { inject, injectable } from "inversify"
import { CATALOG_CLIENT_TYPES, ICatalogueService, ICatalogueServicePMS } from "@curefit/catalog-client"
import { OFFER_SERVICE_CLIENT_TYPES, OfferServiceV3 } from "@curefit/offer-service-client"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import { CENTER_SERVICE_CLIENT_TYPES, ICenterService } from "@curefit/center-service-client"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CacheHelper } from "../util/CacheHelper"
import { ErrorFactory } from "@curefit/error-client"
import { OfferUtil as OffersUtil } from "@curefit/offer-service-client/dist/src/client/OfferUtil"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import WidgetBuilder from "../page/vm/WidgetBuilder"
import {
    CultPackPage, MembershipFeeInfoLineItemWidget, MembershipFeesDetailWidget,
    MembershipPostUpdateStatusWidget,
    MembershipUpgradeSummaryWidget,
    PageTypes,
    TextWidget,
    WidgetView
} from "@curefit/apps-common"
import { CultUpgradePack } from "@curefit/cult-common"
import { UserContext } from "@curefit/userinfo-common"
import { CULT_CLIENT_TYPES, CultMembership } from "@curefit/cult-client"
import { ICultServiceOld as ICultService } from "@curefit/cult-client/dist/src/ICultServiceOld"
import { Action, CenterSelectionWidget, ProductListWidget } from "../common/views/WidgetView"
import { Membership } from "@curefit/membership-commons"
import { CenterResponse } from "@curefit/center-service-common"
import { CultMindMembershipUpgradeMetadata, isMembershipUpgradeClientMetadata, Order } from "@curefit/order-common"
import moment = require("moment")
import { DeliveryCharge, ExtraCharge, Product, ProductType } from "@curefit/product-common"
import { BillingInfo } from "@curefit/finance-common"
import { OrderSummaryWidget, PaymentDetail, PriceComponent } from "../order/OrderViewBuilder"
import * as _ from "lodash"
import { UpgradeFeeBreakdownDetail } from "../cult/UpgradeMembershipViewBuilder"
import { ISportsApi, SPORT_API_CLIENT_TYPES } from "@curefit/sports-api-client-node"
import PlayUtil from "../util/PlayUtil"
import { ErrorCodes } from "../error/ErrorCodes"
import { SubAccessLevel, SubLevelAccessListing } from "@curefit/gymfit-common"
import { BASE_TYPES, Logger } from "@curefit/base"
import { DEFAULT_NEW_IMAGE_VERSION } from "../common/Constants"


@injectable()
class PlayUpgradeDetailViewBuilder {
    constructor(
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) protected catalogueService: ICatalogueService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReaderPMS) protected catalogueServicePMS: ICatalogueServicePMS,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) protected offerServiceV3: OfferServiceV3,
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) public membershipService: IMembershipService,
        @inject(CENTER_SERVICE_CLIENT_TYPES.CenterService) private centerService: ICenterService,
        @inject(LOCATION_TYPES.CityService) protected cityService: ICityService,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferUtil) protected offersUtil: OffersUtil,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) protected serviceInterfaces: CFServiceInterfaces,
        @inject(CUREFIT_API_TYPES.WidgetBuilder) public widgetBuilder: WidgetBuilder,
        @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
        @inject(SPORT_API_CLIENT_TYPES.SportsApi) private sportsApiService: ISportsApi,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
) {}

    async buildView(
        userContext: UserContext,
        centerId: number,
        packId: string,
        membershipId: number,
        workoutName: string,
        workoutId: string
    ): Promise<CultPackPage> {
        const originalMembership = await this.membershipService.getMembershipById(membershipId)
        if (originalMembership.status === "PAUSED" || PlayUtil.isUpcomingPause(originalMembership)) {
            throw this.errorFactory.withCode(ErrorCodes.PLAY_UPGRADE_SELECT_PAUSED_ERR, 400).withDebugMessage("Your membership is currently paused. Please unpause your membership to upgrade.").build()
        }

        const packPage = new CultPackPage()
        const destinationPacks: Array<CultUpgradePack> = await this.cultFitService.browsePlayUpgradePacks("CUREFIT_APP", userContext?.userProfile?.userId)
        /**
         * We want to make it extensible in future
         * To allow users to select a specific pack
         * Hence Upgrade target is returned as an array
         * For now, we'll use the first element of the array
         */
        const destinationPack = destinationPacks[0]
        // if (!destinationPack) {
        //     throw this.errorFactory.withCode(ErrorCodes.CULT_UPGRADE_MEMBERSHIP_TARGET_PACK_NOT_FOUND_ERR, 400).withDebugMessage("Target pack not found while trying to upgrade membership").build()
        // }
        /**
         * Since target of a Membership Upgrade is always to a Cult center
         * We would only concern ourselves only with CultCenters
         */
        let centerResponsePromise
        if (centerId) {
            centerResponsePromise = this.centerService.getCenterById(Number(centerId))
        }
        const originalPack = await this.catalogueServicePMS.getProduct(packId)

        const widgets = []
        widgets.push(this.getProductListWidget())
        widgets.push(this.getTextWidget())
        const { title, productId, productType } = originalPack
        const { id, end, remainingPauseDuration, start } = originalMembership as Membership
        const startDate = moment(start).format("YYYY-MM-DD")
        widgets.push(this.getCenterSelectionWidget(userContext, centerId,
            await centerResponsePromise, workoutName, productId))

        const originalMembershipData = {
            id,
            endDate: moment(end).format("YYYY-MM-DD"),
            startDate: startDate,
            remainingPauseDays: Math.floor( remainingPauseDuration / (24 * 60 * 60 * 1000) ),
        }

        const meta: CultMindMembershipUpgradeMetadata = {
            productId: destinationPack.productId,
            membershipId: id,
            isUpgradeMembership: true,
            isMembershipServiceId: true,
            originalMembership: originalMembershipData,
            originalPack: {
                title,
                productId,
                productType,
                imageVersion: DEFAULT_NEW_IMAGE_VERSION
            },
            workoutId: Number(workoutId)
        }

        const centerResponse: CenterResponse = await centerResponsePromise
        if (centerResponse) {
            meta.cultCenter = {
                id: centerId,
                name: centerResponse.name
            }
        }

        packPage.widgets = widgets
        packPage.actions = [
            {
                title: "NEXT",
                actionType: "UPGRADE_PLAY_MEMBERSHIP",
                meta,
                disabled: !Boolean(centerResponse)
            }
        ]

        return packPage
    }

    private getProductListWidget(): ProductListWidget {
        return new ProductListWidget(
            "SMALL",
            {
                title: "How it works",
                color: "#000000",
                titleFontSize: 18
            },
            [
                {
                    subTitle:
                        "Upgrade unlocks unlimited access to all PLAY centers in your city",
                    icon: "/image/icons/howItWorks/up.png",
                    subTitleFontSize: 14
                },
                {
                    subTitle: "PLAY costs more. You need to pay an upgrade fee as shown on next page",
                    icon: "/image/pack-upgrade/wallet1%402x.png",
                    subTitleFontSize: 14
                },
                {
                    subTitle:
                        "Remaining membership days will be same after upgrade",
                    icon: "/image/pack-upgrade/tickbox2%402x.png",
                    subTitleFontSize: 14
                },
                {
                    subTitle:
                        "PLAY has more pause days. Unused pause days of the existing pack are increased proportionally to the new pack",
                    icon: "/image/icons/howItWorks/pause_3.png",
                    subTitleFontSize: 14
                }
            ]
        )
    }

    private getTextWidget(): TextWidget {
        const textWidget: TextWidget = {
            widgetType: "TEXT_WIDGET",
            content: "Access all centers but pick a preferred PLAY center & sport to help us plan better",
            fontFace: "medium",
            maxNumberOfLines: 4,
            hasDividerBelow: true
        }

        return textWidget
    }

    private getCenterSelectionWidget(
        userContext: UserContext,
        preferredCenterId: number,
        centerResponse: CenterResponse,
        workoutName: String,
        productId: string,
    ): CenterSelectionWidget {
        const action: Action = {
            title: "Pick a center",
            showHelp: false,
            showFavourite: false,
            actionType: "SHOW_PLAY_PACK_SPORT_MODAL",
            meta: {
                flowType: "PLAY_SELECT_UPGRADE",
                productId: productId,
            }
        }
        const prefixText = ""
        const centerName = preferredCenterId && centerResponse ? centerResponse.name : undefined
        const preferredCenterName = workoutName && centerName ? workoutName + ": " + centerName : undefined

        const centerSelectionWidget: CenterSelectionWidget = {
            title: "Select preferred sport & center",
            prefixText: prefixText,
            canChangeCenter: true, // requestParams.canChangeCenter,
            preferredCenterId: Number(preferredCenterId),
            preferredCenterName: preferredCenterName,
            widgetType: "CENTER_PICKER_WIDGET",
            action: action,
            showHighlightedText: !preferredCenterId,
            hasDividerBelow: true
        }
        return centerSelectionWidget
    }

    async buildUpgradeMembershipOrderCheckoutSummary(
        product: Product,
        clientMetadata: Order["clientMetadata"],
        userContext: UserContext,
        subLevelAccessListings: SubLevelAccessListing[]): Promise<WidgetView[]> {
        if (!isMembershipUpgradeClientMetadata(clientMetadata)) {
            return []
        }
        const { originalPack, originalMembership, cultCenter, isMembershipServiceId = false } = clientMetadata
        const upgradeFeeBreakdown: UpgradeFeeBreakdownDetail = (await this.sportsApiService.getPlayMembershipUpgradeFees(
            originalMembership.id,
        )) as UpgradeFeeBreakdownDetail
        const currencySymbol: string = (() => {
            switch (upgradeFeeBreakdown.currency) {
                case "INR":
                    return "₹"
                default:
                    return ""
            }
        })()

        const upgradeMembershipSummaryWidget: MembershipUpgradeSummaryWidget = {
            widgetType: "MEMBERSHIP_UPGRADE_SUMMARY_WIDGET",
            title: "UPGRADE PACK",
            originalPack:  {
                title: "",
                image: "/image/icons/fitsoImages/play_lite_upgrade.png"
            },
            destinationPack: {
                title: "",
                image: "/image/icons/fitsoImages/play_upgrade.png"
            },
            arrowImageUrl: "image/down_arrow.png"
        }
        if (subLevelAccessListings != null && subLevelAccessListings?.length > 0) {
            for (const subAccessLevel of subLevelAccessListings) {
                await this.logger.info("Play Sub Access", subAccessLevel)
                if (subAccessLevel?.subAccessLevel == SubAccessLevel.ACTIVITY && subAccessLevel?.subAccessLevelId != null) {
                    upgradeMembershipSummaryWidget.originalPack.image = PlayUtil.getUpgradePlayIcon(subAccessLevel.subAccessLevelId)
                    upgradeMembershipSummaryWidget.destinationPack.image = "/image/icons/fitsoImages/play_upgrade_hd.png"
                    break
                }
            }
        }
        await this.logger.info("Play Summary Widget", upgradeMembershipSummaryWidget)
        const membershipPostUpdateStatusWidget: MembershipPostUpdateStatusWidget = {
            widgetType: "MEMBERSHIP_POST_UPDATE_STATUS_WIDGET",
            status: [
                {
                    header: "Membership Ends on",
                    infoText: moment(originalMembership.endDate).format("D MMM YYYY")
                },
                {
                    header: "Remaining days",
                    infoText: `${upgradeFeeBreakdown.nDaysLeft}`
                },
                {
                    header: "Pause days left",
                    infoText: originalMembership.remainingPauseDays.toString()
                }
            ]
        }
        const centerPickerWidget: CenterSelectionWidget = {
            title: "Pick Preferred Center",
            prefixText: "Preferred Center: ",
            canChangeCenter: false,
            preferredCenterId: cultCenter.id,
            preferredCenterName: cultCenter.name,
            widgetType: "CENTER_PICKER_WIDGET",
            action: {
                title: "Pick a center",
                showHelp: false,
                showFavourite: false,
                actionType: "SELECT_CENTER",
                meta: {},
                productType: originalPack.productType
            },
            colocatedCenter: null
        }
        const feesDetailWidget: MembershipFeesDetailWidget = {
            widgetType: "MEMBERSHIP_FEE_INFO_DETAIL_WIDGET",
            header: "Why upgrade fee?",
            subHeader:
                "You are being charged to upgrade because cultpass PLAY costs more than your current cultpass PLAY LITE",
            feeItemRows: [
                {
                    label: `Remaining days`,
                    data: `${upgradeFeeBreakdown.nDaysLeft}`
                },
                {
                    label: "Value of current cultpass",
                    data: `${currencySymbol}${upgradeFeeBreakdown.currentPackMRP}`
                },
                {
                    label: `Price difference for ${upgradeFeeBreakdown.nDaysLeft} days`,
                    data: `${currencySymbol}${upgradeFeeBreakdown.totalCost -
                    upgradeFeeBreakdown.upgradeFees}`
                },
                {
                    label: "Upgrade fee",
                    data: `${currencySymbol}${upgradeFeeBreakdown.upgradeFees}`
                },
            ],
            bottomLine: {
                label: "Total upgrade fee",
                data: `${currencySymbol}${upgradeFeeBreakdown.totalCost}`
            }
        }
        const totalFeeLineItemWidget: MembershipFeeInfoLineItemWidget = {
            widgetType: "MEMBERSHIP_FEE_INFO_LINE_WIDGET",
            text: "Total Payable",
            backgroundColor: "rgba(247, 247, 247, 1.0)",
            fees: `${currencySymbol}${upgradeFeeBreakdown.totalCost}`,
            infoIcon: true,
            infoIconAction: {
                type: "SHOW_UPGRADE_MEMBERSHIP_FEE_DETAILS_MODAL",
                actionType: "SHOW_UPGRADE_MEMBERSHIP_FEE_DETAILS_MODAL",
                meta: {
                    widgets: [{ ...feesDetailWidget }]
                }
            },
            faded: false
        }
        const widgets = [
            upgradeMembershipSummaryWidget,
            membershipPostUpdateStatusWidget,
            centerPickerWidget,
            totalFeeLineItemWidget
        ]
        return widgets
    }

    buildUpgradeOrderSummaryWidget(product: Product, billingInfo: BillingInfo, paymentDetail: PaymentDetail, getPriceDetails: (
        productType: ProductType, billingInfo: BillingInfo, deliveryCharge: DeliveryCharge, packagingCharge: ExtraCharge, removeBasePrice: boolean, removeTax: boolean, areDetailsForCheckout: boolean
    ) => PriceComponent[]): OrderSummaryWidget {
        const productTypeforIconUrl = product.productType.toLowerCase() + "_1"

        const orderSummaryWidget: OrderSummaryWidget = {
            productId: product.productId,
            widgetType: "ORDER_SUMMARY_WIDGET",
            title:  product.title,
            thumbnailImages: [],
            magazineImage: "",
            icon: "FITNESS",
            iconUrl: `/image/icons/checkout/${productTypeforIconUrl}.png`,
            price: {
                listingPrice: _.round(billingInfo.total, 2),
                mrp: _.round(billingInfo.total, 2),
                amountPaid: _.round(billingInfo.total, 2),
                currency: billingInfo.currency
            },
            priceDetails: getPriceDetails(product.productType, billingInfo, undefined, undefined, false, false, false),
            paymentDetail: paymentDetail
        }
        return orderSummaryWidget
    }

}

export default PlayUpgradeDetailViewBuilder
