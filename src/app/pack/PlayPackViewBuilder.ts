import { BASE_TYPES, Logger } from "@curefit/base"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { CENTER_SERVICE_CLIENT_TYPES, ICenterService } from "@curefit/center-service-client"
import { CenterResponse } from "@curefit/center-service-common"
import { AccessLevel, CultWorkout } from "@curefit/cult-common"
import { ErrorFactory } from "@curefit/error-client"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import { Benefit, BenefitType } from "@curefit/membership-commons"
import { OfferV2 } from "@curefit/offer-common"
import { OFFER_SERVICE_CLIENT_TYPES, OfferServiceV3 } from "@curefit/offer-service-client"
import { OfferUtil as OffersUtil } from "@curefit/offer-service-client/dist/src/client/OfferUtil"
import { Order } from "@curefit/order-common"
import { ProductType } from "@curefit/product-common"
import { Tenant } from "@curefit/user-common"
import { UserContext } from "@curefit/userinfo-common"
import { TimeUtil } from "@curefit/util-common"
import { inject, injectable } from "inversify"
import * as _ from "lodash"
import * as moment from "moment"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { IOfferAddonWidgetParams, OfferAddonWidget } from "../common/views/OfferAddonWidget"
import {
    Action,
    CheckoutStepItem,
    PlaySelectCheckoutActionsWidget,
    WidgetView
} from "../common/views/WidgetView"
import { CultPackStartDateOptions } from "../cult/cultpackpage/CultPackCommonViewBuilder"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import WidgetBuilder from "../page/vm/WidgetBuilder"
import { CacheHelper } from "../util/CacheHelper"
import CultUtil from "../util/CultUtil"
import PlayUtil from "../util/PlayUtil"
import { BenefitEntry, OfflineFitnessPack } from "@curefit/pack-management-service-common"
import CatalogueServiceUtilities from "../util/CatalogueServiceUtilities"



@injectable()
class PlayPackViewBuilder {
    constructor(
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) protected catalogueService: ICatalogueService,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) protected offerServiceV3: OfferServiceV3,
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) public membershipService: IMembershipService,
        @inject(CENTER_SERVICE_CLIENT_TYPES.CenterService) private centerService: ICenterService,
        @inject(LOCATION_TYPES.CityService) protected cityService: ICityService,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferUtil) protected offersUtil: OffersUtil,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) protected serviceInterfaces: CFServiceInterfaces,
        @inject(CUREFIT_API_TYPES.WidgetBuilder) public widgetBuilder: WidgetBuilder,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
    ) {
    }

    getWidgetPromises(
        productType: ProductType,
        order: Order,
        playPack: OfflineFitnessPack,
        centerResponsePromise: Promise<CenterResponse>,
        workoutResponsePromise: Promise<CultWorkout>,
    ): Promise<WidgetView>[] {
        const playWidgetPromises: Promise<WidgetView>[] = []
        playWidgetPromises.push(this.addFitnessEMIWidget(order, playPack))
        playWidgetPromises.push(this.addOfferAddonWidget(order, true))
        playWidgetPromises.push(this.addPlayWhatYouGet(playPack))
        playWidgetPromises.push(this.addPlayHowItWorks())


        return playWidgetPromises
    }

    private async addOfferAddonWidget(order: Order, enableOfferAction?: boolean): Promise<WidgetView> {
        if (_.isEmpty(order.offersApplied)) return
        const offerV2MapPromise: Promise<{
            [key: string]: OfferV2;
        }> = this.offerServiceV3.getOffersByIds(order.offersApplied).then(o => o.data)
        const offerAddonWidgetParams: IOfferAddonWidgetParams = {
            logger: this.logger,
            offerV2MapPromise: offerV2MapPromise,
            useLabel: "CART_LABEL"
        }
        if (enableOfferAction) {
            offerAddonWidgetParams.title = "Offers Applied"
        }
        let offerAddonWidget: OfferAddonWidget = new OfferAddonWidget()
        offerAddonWidget = await offerAddonWidget.buildView(offerAddonWidgetParams, true, true)
        if (_.isEmpty(offerAddonWidget)) {
            return undefined
        }
        if (enableOfferAction) {
            offerAddonWidget.actionSupported = true
            offerAddonWidget.data.offers.forEach(offer => {
                offer.action = {
                    actionType: "SHOW_OFFERS_TNC_MODAL",
                    meta: {
                        title: "Offer Details",
                        dataItems: offer.tnc,
                        url: offer.tncURL
                    }
                }

                offer.prefixImageurl = "/image/icons/cult/offer_icon.svg"
                offer.fontSize = "P5"
                offer.hexColor = "#AAAAAA"
            })
        }


        return offerAddonWidget
    }

    private async addFitnessEMIWidget(order: Order, product: OfflineFitnessPack): Promise<WidgetView> {
        if (_.isEmpty(order.offersApplied)) return
        const offerV2Map: { [key: string]: OfferV2 } = await this.offerServiceV3.getOffersByIds(order.offersApplied).then(o => o.data)
        let emiOffer: OfferV2
        _.each(offerV2Map, (offer: OfferV2) => {
            if (offer.constraints.paymentChannel === "EMI") {
                emiOffer = offer
            }
        })
        if (emiOffer) {
            return this.getNoCostEMIWidgetView(product, emiOffer)
        } else {
            return null
        }
    }

    public getNoCostEMIWidgetView(product: OfflineFitnessPack, emiOffer: OfferV2): WidgetView {

        let maxEmiTenure = 12
        emiOffer.addons.forEach((addOn) => {
            if (addOn.config && addOn.config.maxEmiTenure) {
                maxEmiTenure = addOn.config.maxEmiTenure
            }
        })

        const numberOfMonths = CultUtil.getEmiTenureForPack(product.product.durationInDays, maxEmiTenure)
        const item = {
            imageUrl: "image/transform/EMI.png",
            title: "No Cost EMI starts at just",
            symbol: "₹",
            price: `${Math.round(product.price.listingPrice / numberOfMonths)}`,
            suffix: "month*",
            action: {
                actionType: "NAVIGATION",
                url: `curefit://nocostemipage?packId=${product.productId}&productType=${product.productType}`,
                title: "DETAILS"
            }
        }

        const title = "No Cost EMI"
        return {
            widgetType: "NO_COST_EMI_WIDGET",
            title: title,
            hasDividerBelow: true,
            items: [item]
        }
    }

    getFooterWidgetPromises(
        playPack: OfflineFitnessPack,
        order: Order,
        userContext: UserContext,
        centerResponsePromise: Promise<CenterResponse>,
        workoutResponsePromise: Promise<CultWorkout>,
    ): Promise<WidgetView>[] {
        const playWidgetPromises: Promise<WidgetView>[] = []
        playWidgetPromises.push(this.getCheckoutActionsForPlay(
            playPack.productId,
            centerResponsePromise,
            workoutResponsePromise,
            order,
            userContext,
            playPack
        ))

        return playWidgetPromises
    }

    public async getCheckoutActionsForPlay(
        productId: string,
        centerResponsePromise: Promise<CenterResponse>,
        workoutResponsePromise: Promise<CultWorkout>,
        order: Order,
        userContext: UserContext,
        playPack: OfflineFitnessPack,
    ): Promise<WidgetView> {

        let center: CenterResponse
        if (centerResponsePromise) {
            center = await centerResponsePromise
        }
        let workout: CultWorkout
        if (workoutResponsePromise) {
            workout = await workoutResponsePromise
        }

        const firstStepHint: string = workout ? `${workout.name}: ${center.name}` : "Select preferred sport & center"

        const stepItems: CheckoutStepItem[] = []
        const selectedStartDate = order.products[0].option.startDate

        await this.buildCenterSelectionWidget(
            stepItems,
            center?.id ? String(center?.id) : null,
            center,
            workout?.name,
            productId,
            selectedStartDate
        )

        const startDateOptions = await this.getDateAction(
            stepItems,
            userContext,
            selectedStartDate,
            playPack,
            productId,
            centerResponsePromise
        )

        const subCategoryCode = ""
        const currentAction = await this.getPrePurchasePageActions(
            userContext,
            playPack,
            startDateOptions,
            order,
            center,
        )
        return new PlaySelectCheckoutActionsWidget(
            stepItems,
            currentAction,
            subCategoryCode
        )
    }

    private getStartDateOptions(
        userContext: UserContext,
        previousMembershipEndDate: string,
        selectedStartDate: string,
        maxStartDate: string
    ): CultPackStartDateOptions {
        const tz = userContext.userProfile.timezone
        const today = TimeUtil.todaysDateWithTimezone(tz)
        let startDate: string = today
        const canChangeStartDate: boolean = true

        startDate = previousMembershipEndDate

        const { minimumDate, selectedDate, maximumDate } = PlayUtil.getPackStartDateOptions(
            userContext,
            null,
            canChangeStartDate,
            selectedStartDate,
            30,
            startDate,
            maxStartDate
        )
        const selectedDateFinal = selectedDate
        return {
            minEligibleDate: minimumDate,
            maxEligibleDate: maximumDate,
            selectedDate: selectedDateFinal,
            canChangeStartDate: canChangeStartDate
        }
    }

    public async addPlayWhatYouGet(playProduct: OfflineFitnessPack, isWeb = false): Promise<WidgetView> {
        const awayBenefit: BenefitEntry = playProduct.product.benefits.find(packBenefit => packBenefit.name === "CULT")
        const liveBenefit: BenefitEntry = playProduct.product.benefits.find(packBenefit => packBenefit.name === "CF_LIVE")
        const items = []
        if (CatalogueServiceUtilities.getAccessLevel(playProduct) === AccessLevel.CITY) {
            const cityName = this.cityService.getCityById(playProduct.clientMetadata?.cityId).name
            items.push({
                subTitle: `Unlimited access to all cult play centers in ${cityName ?? "your city"}`,
                icon: isWeb ? "" : "/image/icons/cult/tick_icon.svg",
                padding: 5
            })
            if (awayBenefit && awayBenefit.tickets && awayBenefit.tickets > 0) {
                items.push({
                    subTitle: `Access ${awayBenefit.tickets} sessions${awayBenefit.type === BenefitType.MONTHLY ? "/ month" : ""} to all ELITE / PRO gyms or cult centers`,
                    icon: isWeb ? "" : "/image/icons/cult/tick_icon.svg",
                    padding: 5
                })
            }
        }
        if (playProduct.product.pauseDays && playProduct.product.pauseDays > 0) {
            items.push({
                subTitle: `${playProduct.product.pauseDays} days of membership pause`,
                icon: isWeb ? "" : "/image/icons/cult/tick_icon.svg",
                padding: 5
            })
        }

        if (liveBenefit) {
            items.push({
                subTitle: "1000+ online workout hours",
                icon: isWeb ? "" : "/image/icons/cult/tick_icon.svg",
                padding: 5
            })
        }

        items.push({
            subTitle: "Only for individuals over the age of 18 years",
            icon: isWeb ? "" : "/image/icons/cult/tick_icon.svg",
            padding: 5
        })
        return {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: isWeb ? "BULLET" : "SMALL",
            header: {
                title: "What you’ll get",
                style: {
                    fontSize: 18
                },
                color: "#000000"
            },
            items,
            hideSepratorLines: false,
            hasDividerBelow: false,
            showSquareIcons: true,
        }
    }

    public async addPlayHowItWorks(isWeb = false): Promise<WidgetView> {
        return {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "SMALL",
            header: {
                title: "How It Works",
                style: {
                    fontSize: 18
                },
                color: "#000000"
            },
            items: [
                {
                    subTitle: "Play sports: Book a sport that you like. Reach the center on time & enjoy your sport",
                    icon: isWeb ? "/image/icons/howItWorks/groupWorkout_1.png" : "/image/icons/howItWorks/swim_pool.svg",
                    padding: 5
                },
                {
                    subTitle: "Visit a Cult Gym at any time & check-in via your phone & start your workout.",
                    icon: isWeb ? "/image/icons/howItWorks/gymWorkout_1.png" : "/image/icons/cult/Gym_icon.svg",
                    padding: 5
                },
                {
                    subTitle: "Choose from the wide variety of online workouts and join in from anywhere.",
                    icon: isWeb ? "/image/icons/howItWorks/liveWorkout_1.png" : "/image/icons/cult/live_icon.svg",
                    padding: 5
                }
            ],
            hideSepratorLines: true,
            hasDividerBelow: false,
            showSquareIcons: true,
            layoutProps: {
                spacing: {
                    bottom: 60,
                }
            }
        }
    }

    private async getPrePurchasePageActions(
        userContext: UserContext,
        selectedPack: OfflineFitnessPack,
        startDateOptions: CultPackStartDateOptions,
        order: Order,
        center?: CenterResponse,
    ): Promise<Action> {

        if (!center) {
            return await this.getCenterAction(
                "Select preferred sport & center",
                selectedPack.productId
            )
        } else if (!startDateOptions.selectedDate) {
            return await PlayUtil.getDatePickerAction("Select plan start date", startDateOptions, selectedPack.productId)
        }

        const isAvailable = true // CultUtil.isPackAvailableForBuy(builderParams.cultPack, builderParams.preferredCenter.id)
        if (isAvailable) {
            return {
                title: "BUY NOW",
                actionType: "GET_NEW_PLAY_SELECT_PACK",
                meta: {
                    orderProduct: order.products[0],
                    clientMetadata: order.clientMetadata,
                    payFree: (order.totalAmountPayable === 0)
                },
                url: "curefit://payment"
            }
        } else {
            return {
                title: "Sold out",
                actionType: "POP_ACTION"
            }
        }
    }

    public async getDateAction(
        stepItems: CheckoutStepItem[],
        userContext: UserContext,
        selectedStartDate: string,
        playPack: OfflineFitnessPack,
        productId: string,
        centerResponsePromise: Promise<CenterResponse>,
    ) {
        const playBenefits: Benefit[] = [{
            name: "PLAY",
            allowOverlap: false,
            type: BenefitType.STATIC,
        }]

        const packDurationInMs = playPack.product.durationInDays * 86400
        const earliestStartDatePlay = await this.membershipService.getEarliestStartDate(
            userContext?.userProfile?.userId,
            Tenant.CUREFIT_APP,
            playBenefits,
            packDurationInMs
        )

        const { minStartDate, maxStartDate } = await this.getMiniumAndMaximumStartDate(
            centerResponsePromise,
            earliestStartDatePlay,
            packDurationInMs
        )

        const startDateOptions = this.getStartDateOptions(
            userContext,
            minStartDate,
            selectedStartDate,
            maxStartDate
        )

        const dateString = selectedStartDate
            ? ("Starts On: " + TimeUtil.formatDateStringInTimeZoneDateFns(selectedStartDate, userContext.userProfile.timezone, "dd MMM"))
            : "Pick a start date"

        const dateIcon = selectedStartDate ? "image/icons/fitsoImages/bounded_tick.png" : "image/icons/fitsoImages/time.png"

        // if (selectedStartDate) {
            stepItems.push({
                id: "DATE_SELECTION",
                imageUrl: dateIcon,
                hintText: dateString,
                visible: true,
                showDivider: false,
                action: await PlayUtil.getDatePickerAction("Pick a start date", startDateOptions, productId)
            })
        // }

        return startDateOptions
    }

    private async buildCenterSelectionWidget(
        stepItems: any[],
        preferredCenterId: string,
        centerResponse: CenterResponse,
        workoutName: string,
        productId?: string,
        selectedStartDate?: string
    ) {
        const prefixText = ""
        const centerName = preferredCenterId && centerResponse ? centerResponse.name : undefined
        const preferredCenterName = workoutName && centerName ? workoutName + ": " + centerName : undefined

        const dateIcon = preferredCenterName ? "image/icons/fitsoImages/bounded_tick.png" : null
        const iconUrl = preferredCenterName ? undefined : "location"
        const hintText = preferredCenterName ? preferredCenterName : "Select preferred sport & center"

        stepItems.push({
            id: "SPORT_CENTER_SELECTION",
            imageUrl: dateIcon,
            iconName: iconUrl,
            hintText: hintText,
            visible: true,
            showDivider: false,
            action: await this.getCenterAction(
                hintText,
                productId
            )
        })
    }

    private async getCenterAction(
        title: string,
        productId: string,
    ): Promise<any> {
        return {
            title: title,
            actionType: "SHOW_PLAY_SPORT_SHEET",
            meta: {
                productId: productId,
            }
        }
    }

    private async getMiniumAndMaximumStartDate(
        centerResponsePromise: Promise<CenterResponse>,
        earliestStartDatePlay: any,
        packDuration: number
    ) {
        let centerInfo: CenterResponse
        let centerLaunchDate = null
        centerInfo = await centerResponsePromise
        if (centerInfo?.launchDate) {
            const centerLaunchDateMs = centerInfo.launchDate
            centerLaunchDate = centerLaunchDateMs ? moment(centerLaunchDateMs).startOf("day").valueOf() : null
        }

        let earliestStartDate = earliestStartDatePlay.start // moment(earliestStartDatePlay.start).add(1, "days").startOf("day").valueOf()
        if (centerLaunchDate) {
            earliestStartDate = Math.max(earliestStartDate, centerLaunchDate)
        }

        let centerEndDate = null
        if (centerInfo?.terminationDate) {
            const centerEndDateMs = centerInfo.terminationDate
            if (centerEndDateMs) {
                const packEndDateMs = centerInfo.terminationDate  - (packDuration * 1000)
                centerEndDate = moment(packEndDateMs).startOf("day").valueOf()
            }
        }

        const minStartDate = moment(earliestStartDate).format("YYYY-MM-DD")
        const maxStartDate = centerEndDate ? moment(centerEndDate).format("YYYY-MM-DD") : null

        return {
            minStartDate,
            maxStartDate
        }
    }

}

export default PlayPackViewBuilder
