import { capitalizeFirst<PERSON>etter, eternalPromise, TimeUtil, Timezone } from "@curefit/util-common"
import * as express from "express"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import { Container, inject } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import {
    ALFRED_CLIENT_TYPES,
    IFulfilmentService,
    IOrderService,
    IPackService,
    IShipmentService,
} from "@curefit/alfred-client"
import {
    OrderCreate, IOrderService as IOMSService, OMS_API_CLIENT_TYPES
} from "@curefit/oms-api-client"
import { DELIVERY_CLIENT_TYPES, IDeliveryAreaService, IGateService } from "@curefit/delivery-client"
import {
    AccessLevel,
    AgeCategory,
    CultMembership,
    CultPack,
    FitnessEvent,
    FitnessFirstPack,
} from "@curefit/cult-common"
import { CULT_CLIENT_TYPES, ICultServiceOld as ICultService, MembershipDetail } from "@curefit/cult-client"
import { AugmentedOfflineFitnessPack } from "@curefit/pack-management-service-common"
import {
    CATALOG_CLIENT_TYPES,
    CatalogServiceV3Utilities,
    CatalogueServiceV2Utilities,
    ICatalogueService,
    ICatalogueServicePMS
} from "@curefit/catalog-client"
import { CustomerIssueType } from "@curefit/issue-common"
import {
    DeliveryChannel,
    DeliveryInstruction,
    DeliverySlot,
    FoodPack,
    MealSlot,
    ResumePackResponse
} from "@curefit/eat-common"
import { ProductType, UrlPathBuilder } from "@curefit/product-common"
import { Session } from "@curefit/userinfo-common"
import { HourMin, SubscriptionType, UserAgentType as UserAgent } from "@curefit/base-common"
import { MindPack } from "@curefit/mind-common"
import { FoodPackBooking } from "@curefit/shipment-common"
import AuthMiddleware from "../auth/AuthMiddleware"
import { CultFitnessPackView, CultMindPackView } from "./CultPackView"
import MealPackListView from "./MealPackListView"
import {
    Action,
    AlertInfo,
    CancelSubscriptionPage,
    CultBuddiesJoiningListSmallView,
    DatePickerWidget,
    EmiOptions,
    EmiValues,
    EmiWidget,
    InfoWidget,
    ManageOptionPayload,
    NoCostEmiDropdownWidget,
    OfferCalloutCardWidget,
    PausePackInfo,
    ProductDetailPage,
    WidgetView
} from "../common/views/WidgetView"
import { CatalogueServiceUtilities } from "../util/CatalogueServiceUtilities"
import { ErrorFactory, HTTP_CODE, UnauthorizedError } from "@curefit/error-client"
import BaseOrderConfirmationViewBuilder, { ConfirmationRequestParams, ConfirmationView } from "../order/BaseOrderConfirmationViewBuilder"
import ICRMIssueService from "../crm/ICRMIssueService"
import * as _ from "lodash"
import { ActionUtil, ISessionBusiness as ISessionService, MealUtil, OfferUtil } from "@curefit/base-utils"
import CultUtil from "../util/CultUtil"
import MealPackPageConfig from "./MealPackPageConfig"
import CultPackPageConfig from "./CultPackPageConfig"
import FitnessFirstPackPageConfig from "./FitnessFirstPackPageConfig"
import CultDIYPackPageConfig from "./CultDIYPackPageConfig"
import MindDIYPackPageConfig from "./MindDIYPackPageConfig"
import IUserBusiness, { UserCity } from "../user/IUserBusiness"
import { ICapacityService, MASTERCHEF_CLIENT_TYPES } from "@curefit/masterchef-client"
import IProductBusiness from "../product/IProductBusiness"
import CapacityServiceWrapper from "../product/CapacityServiceWrapper"
import { SlotUtil } from "@curefit/eat-util"
import EventDetailViewBuilder from "./EventDetailViewBuilder"
import AppUtil, { SUPPORT_DEEP_LINK } from "../util/AppUtil"
import { BASE_TYPES, Logger } from "@curefit/base"
import * as momentTz from "moment-timezone"
import { max } from "moment-timezone"
import { FoodFulfilment, Order, OrderSource } from "@curefit/order-common"
import { RUPEE_SYMBOL } from "@curefit/finance-common"
import { EmiInterest, PaymentData, PrePaymentData } from "@curefit/payment-common"
import { OrderMeta, PageTypes, User } from "@curefit/apps-common"
import {
    BaseOfferRequestParams,
    CultOfferRequestParams, CultProductPricesResponse,
    EatOfferRequestParams,
    EatOfferRequestParamsV3,
    FoodPackOffersResponseV2,
    FoodSinglePriceOfferResponse, GymFitProductPricesResponse,
    LivePricesResponse,
    OfferV2
} from "@curefit/offer-common"
import { IOfferServiceV2, OFFER_SERVICE_CLIENT_TYPES, PackOffersResponse } from "@curefit/offer-service-client"
import MealSubscriptionViewBuilder from "./MealSubscriptionViewBuilder"
import ContentPackDetailViewV2, {
    ContentPackDetailViewV3,
    FitnessDIYPackDetailViewV2,
    FitnessDIYPackDetailViewV3,
    MindDIYPackDetailViewV2,
    MindDIYPackDetailViewV3,
    TLDIYPackDetailView,
    TLCardListView
} from "./ContentPackDetailViewV2"
import SeriesDetailViewV1 from "./SeriesDetailViewV1"
import DIYScheduleDetail from "./DIYScheduleDetail"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService } from "@curefit/diy-client"
import {
    CFLiveProduct,
    DigitalCatalogueEntryV1,
    DIYFitnessPackExtended,
    DIYMeditationPackExtended,
    DIYPack,
    DIYProduct,
    DIYSeries,
    DIYSocialNodeType,
    DIYUserFitnessPack,
    DIYUserMeditationPack,
    DIYUserPack
} from "@curefit/diy-common"
import { IFoodFulfilmentReadonlyDao, ORDER_MODELS_TYPES } from "@curefit/order-models"
import {
    IBaseWidget,
    IServiceInterfaces,
    MembershipItem,
    PackBrowseProductType,
    UserContext
} from "@curefit/vm-models"
import AtlasUtil from "../util/AtlasUtil"
import FitnessFirstPackDetailViewBuilder from "./FitnessFirstPackDetailViewBuilder"
import CultSubscriptionDetailViewBuilder from "./CultSubscriptionDetailViewBuilder"
import { CultCancelSubscriptionPage } from "./CultCancelSubscriptionPage"
import { CAESAR_CLIENT_TYPES, IMenuService } from "@curefit/caesar-client"
import { ManagedPlanProductPageViewBuilder } from "../care/ManagedPlanProductPageViewBuilder"
import { CacheHelper } from "../util/CacheHelper"
import CultPackDetailViewBuilder, { CultPackPageRequestParams, CultPackPageRequestParamsV2 } from "./CultPackDetailViewBuilder"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { CultCancelMembershipPage } from "./CultCancelMembershipPage"
import { ICultBusiness, PreferenceDetail } from "../cult/CultBusiness"
import EatUtil from "../util/EatUtil"
import CultPausePackViewBuilder from "./CultPausePackViewBuilder"
import { EmiInterestReadonlyDaoMongoImpl, PAYMENT_MODELS_TYPES } from "@curefit/payment-models"
import { GST_RATE_INSTANT_DISCOUNT } from "../util/OrderUtil"
import { EatSubscriptionUtil } from "../util/EatSubscriptionUtil"
import { PackBrowseWidgetView } from "../page/vm/widgets/PackBrowseWidgetView"
import CultPackDetailViewBuilderV2 from "../cult/cultpackpage/CultPackDetailViewBuilderV2"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import LiveUtil from "../util/LiveUtil"
import { ClassInviteLinkCreator } from "../cult/invitebuddy/ClassInviteLinkCreator"
import LivePtPackPageViewBuilder from "../cult/ptathome/LivePtPackPageViewBuilder"
import LivePacksViewBuilder from "./LivePacksViewBuilder"
import { LiveMembershipPacksPage } from "../digital/ILivePackPage"
import { Tenant, UserInfo } from "@curefit/user-common"
import { NutritionistPlanProductPageViewBuilder } from "../eat/nutritionist/NutritionistPlanProductPageViewBuilder"
import { DaysRemainingWidgetView } from "../digital/DaysRemainingWidgetView"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { LivePackUtil } from "../util/LivePackUtil"
import { ErrorCodes } from "../error/ErrorCodes"
import { OfferServiceV3 } from "@curefit/offer-service-client/dist/src/client/OfferServiceV3"
import { DiscountBreakup } from "@curefit/offer-common/src/OfferV3"
import { MindTherapyPhysioPacksProductPageViewBuilder } from "../care/MindTherapyAndPhysioPacksProductPageBuilder"
import CareUtil from "../util/CareUtil"
import DiyFavoritesViewBuilder from "./DiyFavoritesViewBuilder"
import { DiagnosticProduct, } from "@curefit/care-common"
import SeriesDetailViewTV from "./SeriesDetailViewTV"
import { IBreadCrumb } from "../page/ondemand/OnDemandCommon"
import { GymfitAccessLevel, GymfitCheckIn, ICancelCheckInParams } from "@curefit/gymfit-common"
import TLLivePacksModalViewBuilder from "../digital/TLLivePacksModalViewBuilder"
import { GYMFIT_CLIENT_TYPES, IGymfitService } from "@curefit/gymfit-client"
import GymfitUtil from "../util/GymfitUtil"
import GymfitPackPageConfig from "../gymfit/GymfitPackPageConfig"
import { GymfitCancelMembershipPage } from "./GymfitCancelMembershipPage"
import {
    CancelPauseRequest,
    EditPauseRequest,
    GapHandling,
    IMembershipService,
    MEMBERSHIP_CLIENT_TYPES, UnpauseRequest
} from "@curefit/membership-client"
import { Benefit, BenefitType, Membership } from "@curefit/membership-commons"
import GymPausePackViewBuilder from "./GymPausePackViewBuilder"
import { PauseMembershipParams } from "@curefit/cult-common"
import IssueBusiness, { IssueDetailView } from "../crm/IssueBusiness"
import ActivePackViewBuilderV1, { ActivePackViewV1 } from "../user/ActivePackViewBuilderV1"
import PlayPausePackViewBuilder from "./PlayPausePackViewBuilder"
import { PromiseCache } from "../util/VMUtil"
import CartViewBuilder from "../cart/CartViewBuilder"
import moment = require("moment-timezone")
import { City } from "@curefit/location-common"
import LocationUtil from "../util/LocationUtil"
import PlayPackDetailViewBuilder from "./PlayPackDetailViewBuilder"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"
import { PACK_CLIENT_TYPES, IOfflineFitnessPackService } from "@curefit/pack-management-service-client"
import { MembershipItemUtil } from "../util/MembershipItemUtil"
import { ISegmentationCacheClient, SEGMENTATION_CLIENT_TYPES } from "@curefit/segmentation-service-client"
import { IMaxmindService, MAXMIND_CLIENT_TYPES } from "@curefit/maxmind-client"
import { ISportsApi, SPORT_API_CLIENT_TYPES } from "@curefit/sports-api-client-node"


function controllerFactory(kernel: Container) {

    @controller("/pack", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class PackController {

        constructor(
            @inject(CUREFIT_API_TYPES.ActivePackViewBuilderV1) private activePackViewBuilderV1: ActivePackViewBuilderV1,
            @inject(CUREFIT_API_TYPES.MealPackPageConfig) private mealPackPageConfig: MealPackPageConfig,
            @inject(CUREFIT_API_TYPES.CultPackPageConfig) private cultPackPageConfig: CultPackPageConfig,
            @inject(CUREFIT_API_TYPES.GymfitPackPageConfig) public gymfitPackPageConfig: GymfitPackPageConfig,
            @inject(CUREFIT_API_TYPES.CultDIYPackPageConfig) private cultDIYPackPageConfig: CultDIYPackPageConfig,
            @inject(CUREFIT_API_TYPES.MindDIYPackPageConfig) private mindDIYPackPageConfig: MindDIYPackPageConfig,
            @inject(ALFRED_CLIENT_TYPES.PackService) private packService: IPackService,
            @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
            @inject(GYMFIT_CLIENT_TYPES.GymfitService) private gymfitService: IGymfitService,
            @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) public membershipService: IMembershipService,
            @inject(CULT_CLIENT_TYPES.MindFitService) private mindFitService: ICultService,
            @inject(DELIVERY_CLIENT_TYPES.GateService) private gateService: IGateService,
            @inject(LOCATION_TYPES.CityService) private cityService: ICityService,
            @inject(SPORT_API_CLIENT_TYPES.SportsApi) private sportsApiService: ISportsApi,
            @inject(ALFRED_CLIENT_TYPES.FulfilmentService) private fulfilmentService: IFulfilmentService,
            @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
            @inject(CATALOG_CLIENT_TYPES.CatalogServiceReaderPMS) private catalogueServicePMS: ICatalogueServicePMS,
            @inject(CAESAR_CLIENT_TYPES.MenuService) private menuService: IMenuService,
            @inject(CUREFIT_API_TYPES.CRMIssueService) private CRMIssueService: ICRMIssueService,
            @inject(CUREFIT_API_TYPES.UserBusiness) private userBusiness: IUserBusiness,
            @inject(ALFRED_CLIENT_TYPES.ShipmentService) private shipmentService: IShipmentService,
            @inject(MASTERCHEF_CLIENT_TYPES.CapacityService) private capacityService: ICapacityService,
            @inject(CUREFIT_API_TYPES.CapacityServiceWrapper) private capacityServiceWrapper: CapacityServiceWrapper,
            @inject(CUREFIT_API_TYPES.SessionService) private sessionBusiness: ISessionService,
            @inject(CUREFIT_API_TYPES.MealSubscriptionViewBuilder) private mealSubscriptionDetailViewBuilder: MealSubscriptionViewBuilder,
            @inject(CUREFIT_API_TYPES.CultPausePackViewBuilder) private cultPausePackViewBuilder: CultPausePackViewBuilder,
            @inject(CUREFIT_API_TYPES.CultFitPackDetailViewBuilder) private cultFitPackDetailViewBuilder: CultPackDetailViewBuilder,
            @inject(CUREFIT_API_TYPES.CultFitPackDetailViewBuilderV2) private cultFitPackDetailViewBuilderV2: CultPackDetailViewBuilderV2,
            @inject(CUREFIT_API_TYPES.CultMindPackDetailViewBuilder) private cultMindPackDetailViewBuilder: CultPackDetailViewBuilder,
            @inject(CUREFIT_API_TYPES.CultMindPackDetailViewBuilderV2) private cultMindPackDetailViewBuilderV2: CultPackDetailViewBuilderV2,
            @inject(CUREFIT_API_TYPES.CultFitSubscriptionDetailViewBuilder) private cultFitSubscriptionDetailViewBuilder: CultSubscriptionDetailViewBuilder,
            @inject(CUREFIT_API_TYPES.CultMindSubscriptionDetailViewBuilder) private cultMindSubscriptionDetailViewBuilder: CultSubscriptionDetailViewBuilder,
            @inject(CUREFIT_API_TYPES.EventDetailViewBuilder) private eventDetailViewBuilder: EventDetailViewBuilder,
            @inject(CUREFIT_API_TYPES.ProductBusiness) private productBusiness: IProductBusiness,
            @inject(CUREFIT_API_TYPES.OrderConfirmationViewBuilder) private orderConfirmationViewBuilder: BaseOrderConfirmationViewBuilder,
            @inject(BASE_TYPES.ILogger) protected logger: Logger,
            @inject(ALFRED_CLIENT_TYPES.OrderService) protected alfredService: IOrderService,
            @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
            @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) protected DIYFulfilmentService: IDIYFulfilmentService,
            @inject(DELIVERY_CLIENT_TYPES.DeliveryAreaService) protected deliveryAreaService: IDeliveryAreaService,
            @inject(ORDER_MODELS_TYPES.FoodFulfilmentReadonlyDao) private foodDao: IFoodFulfilmentReadonlyDao,
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: IServiceInterfaces,
            @inject(CUREFIT_API_TYPES.FitnessFirstPackDetailViewBuilder) private fitnessFirstPackDetailViewBuilder: FitnessFirstPackDetailViewBuilder,
            @inject(CUREFIT_API_TYPES.FitnessFirstPackPageConfig) private fitnessFirstPackPageConfig: FitnessFirstPackPageConfig,
            @inject(CUREFIT_API_TYPES.ManagedPlanProductPageViewBuilder) private mpProductPageViewBuilder: ManagedPlanProductPageViewBuilder,
            @inject(CUREFIT_API_TYPES.LivePtPackPageViewBuilder) private livePtPackPageViewBuilder: LivePtPackPageViewBuilder,
            @inject(CUREFIT_API_TYPES.TLLivePacksModalViewBuilder) private TLLivePacksModalViewBuilder: TLLivePacksModalViewBuilder,
            @inject(CUREFIT_API_TYPES.CultBusiness) private cultBusiness: ICultBusiness,
            @inject(PAYMENT_MODELS_TYPES.EmiInterestReadonlyDao) private paymentDao: EmiInterestReadonlyDaoMongoImpl,
            @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness,
            @inject(CUREFIT_API_TYPES.ClassInviteLinkCreator) private classInviteLinkCreator: ClassInviteLinkCreator,
            @inject(CUREFIT_API_TYPES.LivePacksViewBuilder) private livePacksViewBuilder: LivePacksViewBuilder,
            @inject(CUREFIT_API_TYPES.NutritionistPlanProductPageViewBuilder) private nutritionistPlanProductPageViewBuilder: NutritionistPlanProductPageViewBuilder,
            @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) private diyFulfilmentService: IDIYFulfilmentService,
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private cfServiceInterfaces: CFServiceInterfaces,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
            @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) private offerServiceV3: OfferServiceV3,
            @inject(CUREFIT_API_TYPES.MindTherapyPhysioPacksProductPageViewBuilder) private mindTherapyPhysioPacksProductPageViewBuilder: MindTherapyPhysioPacksProductPageViewBuilder,
            @inject(CUREFIT_API_TYPES.DiyFavoritesViewBuilder) private diyFavoritesViewBuilder: DiyFavoritesViewBuilder,
            @inject(CUREFIT_API_TYPES.OrderConfirmationViewBuilderV1) private orderConfirmationViewBuilderV1: BaseOrderConfirmationViewBuilder,
            @inject(CUREFIT_API_TYPES.GymPausePackViewBuilder) private gymPausePackViewBuilder: GymPausePackViewBuilder,
            @inject(CUREFIT_API_TYPES.PlayPausePackViewBuilder) private playPausePackViewBuilder: PlayPausePackViewBuilder,
            @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness,
            @inject(OMS_API_CLIENT_TYPES.OrderService) protected omsApiClient: IOMSService,
            @inject(CUREFIT_API_TYPES.CartViewBuilder) private cartViewBuilder: CartViewBuilder,
            @inject(CUREFIT_API_TYPES.PlayPackDetailViewBuilder) private playPackDetailViewBuilder: PlayPackDetailViewBuilder,
            @inject(PACK_CLIENT_TYPES.OfflineFitnessPackService) private offlineFitnessPackService: IOfflineFitnessPackService,
            @inject(MAXMIND_CLIENT_TYPES.IMaxmindService) private maxmindService: IMaxmindService,
            @inject(SEGMENTATION_CLIENT_TYPES.SegmentationCacheClient) private segmentationCacheClient: ISegmentationCacheClient,

        ) {
        }

        @httpGet("/cult/browse")
        async browseFitnessPacks(req: express.Request): Promise<CultFitnessPackView[]> {
            const session: Session = req.session
            const userId: string = session.userId
            const userAgent: UserAgent = session.userAgent
            const userContext = req.userContext as UserContext
            const cultCityId = await this.getCultCityId(req, userContext, session.sessionData.cityId, userId)
            const city: City = this.cityService.getCityByCultCityId(cultCityId)
            let skipWeekendPack: boolean = req.query.skipWeekendPack === "true"
            skipWeekendPack = true
            const user = await this.userCache.getUser(req.session.userId)
            const cultFitnessPacks = await CatalogueServiceUtilities.getCultPMSPacks(this.offlineFitnessPackService, userId, city.cityId)
            const packOffersV3Response = await this.offerServiceV3.getCultPackPrices({
                cultCityId,
                cityId: userContext.userProfile.cityId,
                userInfo: {
                    userId: userContext.userProfile.userId,
                    deviceId: userContext.sessionInfo.deviceId,
                    phone: user?.phone,
                    email: user?.email,
                    workEmail: user?.workEmail
                },
                source: AppUtil.callSourceFromContext(userContext),
                productIds: cultFitnessPacks.map(f => f.productId)
            })
            return cultFitnessPacks.map(fitnessPack => { return new CultFitnessPackView(userAgent, fitnessPack, packOffersV3Response) })
        }

        private async getCultCityId(req: express.Request, userContext: UserContext, cityId: string, userId: string): Promise<number> {
            const userCity: UserCity = await LocationUtil.getUserCityFromReq(req, this.cityService, this.logger, null, null)
            const city: City = userCity.city
            if (this.cityService.checkIfCityIsOtherCity(city.cityId)) {
                const activeMembershipData = await this.cultFitService.activeMembership(userId)
                const membership = CultUtil.cultPackProgress(userContext, activeMembershipData.memberships)
                if (membership)
                    return membership.currentMembership.cityID
            }
            return city.cultCityId
        }

        /**
         *
         * @param {req} : Request from Client Side, to render Change Start Date Screen
         * @return {res} 401 unautorized, if user is not authorized to edit the membership start date
         * @return {res} Widgets and Action for the screen, otherwise
         */
        @httpGet("/changeStartDate/:membershipId")
        public async getPackChangeStartDateData(req: express.Request) {
            const session: Session = req.session
            const membershipId: number = parseInt(req.params.membershipId, 10)
            const productType: string = req.query.productType
            const userContext: UserContext = req.userContext as UserContext
            const tz = userContext.userProfile.timezone
            let service: ICultService | undefined
            let conflictingMembershipEndDate: any
            if (productType === "PLAY") {
                const membership = await this.membershipService.getMembershipById(membershipId)
                return this.playPackDetailViewBuilder.getPlayStartDateResponse(membership, userContext, tz, membershipId, productType)
            }
            switch (productType) {
                case "FITNESS":
                    service = this.cultFitService
                    break
                case "MIND":
                    service = this.mindFitService
                    break
                default:
                    service = undefined
            }
            if (!service) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage(`Bad Request: Must be Cult or Mind Pack, received ${productType}`).build()
            }
            const membershipData = await this.cultFitService.getMembershipByIdV3(membershipId, session.userId)
            if (!membershipData.isMembershipStartDateEditable.result) {
                /**
                 * Unauthorized
                 */
                throw new UnauthorizedError(membershipData.isMembershipStartDateEditable.message)
            }

            const memberships: Array<Membership> = await this.membershipService.getCachedMembershipsForUser(session.userId + "", "curefit", ["CULT", "CULT_COMPLIMENTARY_ACCESS"],
                ["PURCHASED", "PAUSED"],
                null, null)

            if (memberships.length > 1) {
                /**
                 * Might be conflicting memberships
                 */
                const otherMemberships = memberships.filter(x => x.id !== membershipId)
                // sort by endDates
                otherMemberships.sort((a, b) => {
                    if (moment(a.end).isBefore(moment(b.end))) {
                        return 1
                    }
                    return -1
                })
                conflictingMembershipEndDate = moment(otherMemberships[0].end)
            }

            /**
             * Need to send a Info Widget and a Date Picker Widget
             * newEndDate: the end date limit on datepicker - cannot be further than 1 month from original pack start date
             * newStartDate: today. A user can start a pack as early as today, even if it's scheduled to start later
             */
            const newEndDate: string = !_.isNil(membershipData?.originalStartDate) ? TimeUtil.addDays(tz, moment(membershipData.originalStartDate).format("YYYY-MM-DD"), 30) : TimeUtil.addDays(tz, moment(membershipData.membership.start).format("YYYY-MM-DD"), 30)
            let newStartDate: string = ((): string => {
                if (conflictingMembershipEndDate) {
                    if (TimeUtil.getMomentNow(tz).isBefore(conflictingMembershipEndDate)) {
                        return conflictingMembershipEndDate.add(1, "days").format("YYYY-MM-DD")
                    }
                }
                return TimeUtil.todaysDate(tz)
            })()

            let subtitle = "You can start your membership earlier or later, but new start date cannot be beyond 30 days from original start date. Start date can be changed up to 2 times."

            if (!_.isNil(membershipData.membership.metadata.centerId)) {
                const preferredCenter = await this.catalogueService.getCultCenter(membershipData.membership.metadata.centerId)
                if (!_.isNil(preferredCenter?.launchDate)) {
                    const today = TimeUtil.todaysDateWithTimezone(tz)
                    const centerLaunchDate = TimeUtil.formatDateStringInTimeZone(preferredCenter.launchDate, tz)
                    if (centerLaunchDate > today) {
                        subtitle += `\n\nCenter opens on ${preferredCenter.launchDate}, please select pack start date post that.`
                    }
                    newStartDate = CultUtil.getMinimumStartDateForCenter(preferredCenter, newStartDate)
                }
            }

            const packProductId = membershipData.membership.productId
            const pack: OfflineFitnessPack = await this.catalogueServicePMS.getProduct(packProductId)

            const infoWidget: InfoWidget = {
                title: "Change start date",
                subTitle: subtitle,
                widgetType: "INFO_WIDGET",
                icon: null
            }
            const datePickerWidget: DatePickerWidget = {
                widgetType: "DATE_PICKER_WIDGET",
                startDate: newStartDate,
                canChangeStartDate: true,
                endDate: null,
                selectedDate: null
            }
            const action = {
                actionType: "SHOW_ALERT_MODAL",
                title: "Submit",
                meta: {
                    title: "Change start date",
                    packName: pack.title,
                    actions: [
                        {
                            actionType: "CONFIRM_START_DATE_CHANGE",
                            title: "CONFIRM",
                            payload: {
                                membershipId,
                            }
                        }
                    ]
                }
            }
            return {
                validation: {
                    endDate: newEndDate,
                    errorMessage: `New start date should be on or before ${TimeUtil.formatDateStringInTimeZone(newEndDate, tz, "ddd, MMM DD YYYY")}`,
                },
                widgets: [
                    infoWidget,
                    datePickerWidget
                ],
                actions: [
                    action
                ]
            }
        }

        /**
         *
         * @param req : Post Request from client side with membershipID and new start date of pack
         * @return {CultMembershipData} if successful, else error with reason
         */
        @httpPost("/changeStartDate")
        public async setPackStartDate(req: express.Request) {
            const session: Session = req.session
            const userId: string = session.userId
            const userAgent: string = req.session.userAgent
            const membershipId: string = req.body.membershipId
            const newStartDate: string = req.body.newStartDate
            const productType: string = req.body.productType
            const userContext: UserContext = req.userContext as UserContext
            /**
             * Validations
             */
            let response
            const params = {
                newStartDate,
                userId,
                source: "User",
                reason: "In App",
            }
            if (productType === "PLAY") {
                this.logger.info("Play Membership Updated Date MS : " + new Date(newStartDate).getTime())
                const data = await this.sportsApiService.moveMembershipById(Number(membershipId), new Date(newStartDate).getTime(), params.userId, params.reason)
                response = {
                    productType,
                    message: `Pack start date has been updated. Your pack starts on ${TimeUtil.formatDateStringInTimeZone(moment(data[0].start).format("YYYY-MM-DD"), userContext.userProfile.timezone, "ddd, MMM DD YYYY")}`
                }
                return response
            }
            const service = (productType === "FITNESS") ? this.cultFitService : (productType === "MIND" ? this.mindFitService : null)
            if (!service) {
                return Promise.reject({
                    success: false,
                    reason: "Product Code must be FITNESS or MIND"
                })
            }
            const data = await service.editMembershipStartDateV2(membershipId, params, userAgent)
            response = {
                productType,
                message: `Pack start date has been updated. Your pack starts on ${TimeUtil.formatDateStringInTimeZone(moment(data.start).format("YYYY-MM-DD"), userContext.userProfile.timezone, "ddd, MMM DD YYYY")}`
            }
            return response
        }

        @httpGet("/cult/mind/browse")
        async browseCultMindPacks(req: express.Request): Promise<CultMindPackView[]> {
            const session: Session = req.session
            const userId: string = session.userId
            const userAgent: UserAgent = req.session.userAgent
            const userContext: UserContext = req.userContext as UserContext
            const cultCityId = await this.getCultCityId(req, userContext, session.sessionData.cityId, userId)
            let skipWeekendPack: boolean = req.query.skipWeekendPack === "true"
            const showOnlyOGMPacks: boolean = req.query.showOnlyOGMPacks === "true" || false
            const ageCategory: AgeCategory = req.query.ageCategory || "ADULT"
            skipWeekendPack = true
            const userPromise = this.userCache.getUser(req.session.userId)
            let mindPacks = [] as CultPack[] // Mind packs are deprecated
            const user = await userPromise
            mindPacks = _.filter(mindPacks, mindPack => {
                if (showOnlyOGMPacks && !mindPack.isOGMApplicable)
                    return false
                if (mindPack.id === 16 || mindPack.id === 17 || mindPack.id === 18
                    || mindPack.id === 19)
                    return false
                if (skipWeekendPack)
                    return 14 !== mindPack.id && mindPack.state === "ACTIVE"
                if (!CultUtil.isSubscriptionPackSupported(mindPack.subscriptionPack, req.userContext as UserContext))
                    return false
                else
                    return mindPack.state === "ACTIVE"
            })
            const filteredPacks: OfflineFitnessPack[] = [] // Mind packs are deprecated
            const packOffersV3Response = await this.offerServiceV3.getMindPackPrices({
                cultCityId,
                cityId: userContext.userProfile.cityId,
                userInfo: {
                    userId: userContext.userProfile.userId,
                    deviceId: userContext.sessionInfo.deviceId,
                    phone: user?.phone,
                    email: user?.email,
                    workEmail: user?.workEmail
                },
                source: AppUtil.callSourceFromContext(userContext),
                productIds: filteredPacks.map(f => f.productId)
            })
            return filteredPacks.map(fitnessPack => { return new CultMindPackView(userAgent, fitnessPack, packOffersV3Response) })
        }

        @httpGet("/eat/browse")
        async browseMealPacks(req: express.Request): Promise<MealPackListView> {
            const session: Session = req.session
            const userId: string = session.userId
            const userContext: UserContext = req.userContext as UserContext
            const deviceId: string = session.deviceId
            const lat: number = req.query.lat
            const lon: number = req.query.lon
            const preferredLocation = await this.userBusiness.getPreferredLocation(userContext, userId, session.sessionData, lon, lat, undefined, true, "EAT_FIT")
            const areaId = (preferredLocation && preferredLocation.area) ? preferredLocation.area.areaId : "1"
            const eatOfferRequestParam: EatOfferRequestParams = {
                userId: userId,
                cityId: userContext.userProfile.cityId,
                areaId: areaId,
                deviceId: deviceId,
                source: AppUtil.callSourceFromContext(userContext)
            }
            const packOffersPromise = Promise.resolve({} as FoodPackOffersResponseV2)
            const mealPacksPromise = this.packService.browseMealPacksForArea(areaId, true)
            const availableMealSlots = await this.deliveryAreaService.getMealSlotsForArea(areaId)

            const mealPacks = await mealPacksPromise
            const packOffersResponse = await packOffersPromise
            const packTz = await this.deliveryAreaService.getTimeZoneForAreaId(areaId)
            return new MealPackListView(userContext, mealPacks, packOffersResponse, availableMealSlots, areaId, preferredLocation.deliveryChannel, packTz)
        }

        @httpGet("/mind/browse")
        async browseMindDIYPacks(req: express.Request): Promise<{ widgets: IBaseWidget[] }> {
            return this.buildDIYBrowseWidget(req, "DIY_MEDITATION")
        }

        async buildDIYBrowseWidget(req: express.Request, productType: ProductType) {
            const session: Session = req.session
            const seriesId: string = req.query.seriesId
            const widget = new PackBrowseWidgetView()
            widget.layoutType = "GRID"
            widget.productType = <PackBrowseProductType>productType
            const userContext: UserContext = req.userContext as UserContext
            return { widgets: [await widget.buildView(this.serviceInterfaces, userContext, { diySeriesId: seriesId })] }
        }

        @httpGet("/cultDIY/browse")
        async browseFitnessDIYPacks(req: express.Request): Promise<{ widgets: IBaseWidget[] }> {
            return this.buildDIYBrowseWidget(req, "DIY_FITNESS")
        }


        @httpGet("/cultDIY/:packId")
        async getCultDIYPack(req: express.Request): Promise<FitnessDIYPackDetailViewV2> {
            const session: Session = req.session
            const packId: string = req.params.packId
            const appVersion: number = Number(req.headers["appversion"])
            const userAgent: UserAgent = session.userAgent
            const userContext: UserContext = req.userContext as UserContext
            const diyPack = await this.packService.getFitnessDIYPackV2(packId, session.userId)
            const products = await this.diyFulfilmentService.getDIYFitnessProductsByProductIds(session.userId, diyPack.pack.sessionIds, AppUtil.getTenantFromUserContext(req.userContext))
            const productMap = LivePackUtil.getProductMap(products)
            const daysRemainingWidget = await new DaysRemainingWidgetView().buildView(this.cfServiceInterfaces, req.userContext, { source: "cultdiypackpage" })
            const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(this.cultBusiness, this.diyFulfilmentService, req.userContext)
            const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, this.hamletBusiness)
            const blockInternationalUser = await AppUtil.blockFitnessContentForInternationalUser(userContext, this.maxmindService, this.logger)
            const userHasActiveMembership: { start: number, end: number } | undefined = await LiveUtil.getActiveMembershipDates(userContext, this.serviceInterfaces.diyService, this.serviceInterfaces.catalogueService)
            return new FitnessDIYPackDetailViewV2(userAgent, appVersion, diyPack, this.cultDIYPackPageConfig, productMap, [], blockInternationalUser, daysRemainingWidget, req.userContext, isUserEligibleForTrial, isUserEligibleForMonetisation, userHasActiveMembership, bucketId)
        }

        @httpGet("/cultDIY/v2/:packId")
        async getCultDIYPackV2(req: express.Request): Promise<ContentPackDetailViewV3> {
            const session: Session = req.session
            const userContext = req.userContext as UserContext
            const packId: string = req.params.packId
            const category: string = req.query.category
            const productId: string = req.query.productId
            const appVersion: number = Number(req.headers["appversion"])
            const nodeRelationId = req.query.nodeRelationID
            const userAgent: UserAgent = session.userAgent
            const userId = session.userId
            const apiKey: string = req.headers["api-key"] as string || req.headers["apikey"] as string
            this.logger.info(`Cult Pack details called for user id: ${session.userId}; nodeRelationId: ${nodeRelationId}; productId: ${productId}; packId: ${packId}`)
            const isInternationalUser = AppUtil.isInternationalApp(userContext)
            let blockInternationalUser = await AppUtil.blockFitnessContentForInternationalUser(userContext, this.maxmindService, this.logger)
            if (AppUtil.isTVAppWithApiKey(apiKey)) {
                blockInternationalUser = false
            }
            const tenant = AppUtil.getTenantFromUserContext(userContext)
            let diyPackPromise: Promise<DIYUserFitnessPack>

            const userPromise = this.userCache.getUser(session.userId)
            const daysRemainingWidgetPromise = new DaysRemainingWidgetView().buildView(this.cfServiceInterfaces, req.userContext, { source: "diypackpage" })

            diyPackPromise = this.packService.getFitnessDIYPackV2(packId, userId)

            let addDIYSocialNodeRelationPromise
            if (nodeRelationId) {
                const productType = productId ? "DIY_FITNESS" : "DIY_FITNESS_PACK"
                const nodeType = productId ? DIYSocialNodeType.SESSION : DIYSocialNodeType.PACK
                addDIYSocialNodeRelationPromise = this.diyFulfilmentService.addDIYSocialNodeRelation(userId, productType, nodeType, productId ? productId : packId, nodeRelationId)
            }
            const userHasActiveMembershiopPromise = LiveUtil.getActiveMembershipDates(userContext, this.serviceInterfaces.diyService, this.serviceInterfaces.catalogueService)
            const checkIfMonetisationEnabledAndEligibleForTrialPromise = LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(this.cultBusiness, this.diyFulfilmentService, userContext)
            const isDiyEnergyMeterSupportedPromise = AppUtil.isDiyEnergyMeterSupported(userContext, this.hamletBusiness)
            const isLegacyVideoBackgroundSupportedPromise = AppUtil.isLiveVideoPlayerBackgroundSupported(userContext, this.hamletBusiness)
            const [diyPack] = await Promise.all([diyPackPromise, addDIYSocialNodeRelationPromise])
            const packDetail = diyPack.pack.productType === "DIY_FITNESS_PACK" ? (await this.diyFulfilmentService.getDIYFitnessPacksForIds(session.userId, [diyPack.pack.productId]))[0] :
                (await this.diyFulfilmentService.getDIYMeditationPacksForIds(session.userId, [diyPack.pack.productId]))[0]
            this.logger.debug(`Cult Pack details called for user id: ${session.userId}; nodeRelationId: ${nodeRelationId}; productId: ${productId}; packId: ${packId}`)

            const productsV2 = await this.diyFulfilmentService.getProductsForPack(packId, userId, tenant)
            const isOldDIYConstruct = (packDetail.sessionIds?.length > 0)

            if ( !isOldDIYConstruct && productsV2.products && LivePackUtil.isProductV2DIY(productsV2.products)) {
                packDetail.sessionIds = LivePackUtil.getProductV2SessionIds(productsV2.products)
            }

            const productsPromise = this.diyFulfilmentService.getDIYFitnessProductsByProductIds(session.userId, packDetail.sessionIds, AppUtil.getTenantFromUserContext(userContext))
            const socialDataForPackPromise = this.diyFulfilmentService.getDIYSocialAssociatedParentNodes(session.userId, "DIY_FITNESS_PACK", DIYSocialNodeType.PACK, [packId])
            let socialDataForSessionsPromise
            if (isOldDIYConstruct) {
                socialDataForSessionsPromise = this.diyFulfilmentService.getDIYSocialAssociatedParentNodes(session.userId, "DIY_FITNESS", DIYSocialNodeType.SESSION, packDetail.sessionIds)
            }
            const [socialDataForPack, socialDataForSessions] = await Promise.all([socialDataForPackPromise, socialDataForSessionsPromise])

            let packRecommendedByBuddiesPromise: Promise<CultBuddiesJoiningListSmallView>
            if (socialDataForPack && socialDataForPack[packId] && !_.isEmpty(socialDataForPack[packId].userIds)) {
                const attendingUsers: { userId: string }[] = socialDataForPack[packId].userIds.map(userId => { return { userId } })
                packRecommendedByBuddiesPromise = LiveUtil.getBuddiesJoiningListSmallView(attendingUsers, this.userCache, PageTypes.CultDIYPack, true, "shared this")
            }
            const sessionRecommendedByBuddiesMap: { [sessionId: string]: CultBuddiesJoiningListSmallView } = {}
            const settingSessionRecommendedByBuddiesMapPromise = Promise.all(packDetail.sessionIds.map(async (sessionId) => {
                if (!socialDataForSessions || !socialDataForSessions[sessionId] || _.isEmpty(socialDataForSessions[sessionId].userIds)) return undefined
                const attendingUsers: { userId: string }[] = socialDataForSessions[sessionId].userIds.map(userId => { return { userId } })
                sessionRecommendedByBuddiesMap[sessionId] = await LiveUtil.getBuddiesJoiningListSmallView(attendingUsers, this.userCache, PageTypes.CultDIYPack, true, "shared this", 2)
            }))
            const [daysRemainingWidget, products, user, packRecommendedByBuddies, userHasActiveMembership, { isUserEligibleForMonetisation, isUserEligibleForTrial },
                isDiyEnergyMeterSupported, isLegacyVideoBackgroundSupported
            ] = await Promise.all([
                daysRemainingWidgetPromise, productsPromise, userPromise, packRecommendedByBuddiesPromise, userHasActiveMembershiopPromise, checkIfMonetisationEnabledAndEligibleForTrialPromise,
                isDiyEnergyMeterSupportedPromise, isLegacyVideoBackgroundSupportedPromise, settingSessionRecommendedByBuddiesMapPromise
            ])
            const diyEnergyMeterSupportedMap = LivePackUtil.getDiyEnergyMeterSupportedMap(products, ["ENERGY", "SCORE"], isDiyEnergyMeterSupported, isLegacyVideoBackgroundSupported)

            const productMap = LivePackUtil.getProductMap(products)
            // const productV2Map = LivePackUtil.getProductV2Map(productV2.products)

            const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, this.hamletBusiness)

            const breadCrumbs: IBreadCrumb[] = []
            diyPack.pack = packDetail
            // if (AppUtil.isInternationalTLApp(userContext) || AppUtil.isInternationalTLTVApp(userContext)) {
            //     return new TLDIYPackDetailView(userAgent, appVersion, diyPack, this.cultDIYPackPageConfig, productMap, packRecommendedByBuddies, sessionRecommendedByBuddiesMap, breadCrumbs, daysRemainingWidget, userContext, user.isInternalUser, isUserEligibleForTrial, isUserEligibleForMonetisation, userHasActiveMembership, "", diyEnergyMeterSupportedMap, packDetail)
            // }
            const response = new FitnessDIYPackDetailViewV3(userAgent, appVersion, diyPack, this.cultDIYPackPageConfig, productMap, packRecommendedByBuddies, sessionRecommendedByBuddiesMap, breadCrumbs, blockInternationalUser, daysRemainingWidget, userContext, user.isInternalUser, isUserEligibleForTrial, isUserEligibleForMonetisation, userHasActiveMembership, bucketId, diyEnergyMeterSupportedMap, productsV2.products)
            await response.preparePagePromise

            // TODO: remove this condition beyond mid-june 2021
            if (AppUtil.isTVAppWithApiKey(apiKey)) {
                const productListWidget: any = response.widgets.find((widget: any) => widget.widgetType === "PRODUCT_LIST_WIDGET")
                if (productListWidget) {
                    if (!_.isEmpty(productListWidget.items)) {
                        for (const item of productListWidget.items) {
                            item.action = item.cardAction // tv app 1.3 and before expects this
                        }
                    }
                }
            }

            return response
        }

        @httpGet("/DIY/event/suryanamaskar")
        async getSuryanamaskarStats(req: express.Request) {
            const userContext = req.userContext as UserContext
            const tz = userContext.userProfile.timezone
            const suryanamaskarStats = {}
            return { ...suryanamaskarStats }
        }

        @httpGet("/DIY/browse/v2")
        async getDIYSeries(req: express.Request): Promise<SeriesDetailViewV1 | SeriesDetailViewTV | TLCardListView> {
            const session: Session = req.session
            const seriesId: string = req.query.seriesId || req.query.productId
            const productType: ProductType = req.query.productType
            const userAgent: UserAgent = session.userAgent
            const userContext = req.userContext as UserContext
            const user = await this.userCache.getUser(session.userId)
            const countryId = AppUtil.getCountryId(userContext)
            const apiKey: string = req.headers["api-key"] as string || req.headers["apikey"] as string
            let diySeries: DIYSeries

            diySeries = await this.serviceInterfaces.diyService.getDIYSeriesById(session.userId, seriesId, productType, countryId)

            const packsPromise = _.map(diySeries.packIds, async packId => {
                return productType === "DIY_FITNESS" ? this.packService.getFitnessDIYPackV2(packId, session.userId) :
                    this.packService.getMindDIYPackV2(packId, session.userId)
            })
            const packs = await Promise.all(packsPromise)
            const durationMap = new Map<string, number>()
            const nextSessionMap = new Map<string, DIYProduct>()
            const tenant = AppUtil.getTenantFromUserContext(userContext)
            const packDetailMap: { [packId: string]: DIYFitnessPackExtended | DIYMeditationPackExtended } = {}
            const blockInternationalUser = await AppUtil.blockFitnessContentForInternationalUser(userContext, this.maxmindService, this.logger)
            for (let i = 0; i < packs.length; i++) {
                const pack = packs[i]
                // If new pack construct
                if (!(pack.pack.sessionIds?.length > 0)) {
                    const newPackConstruct = await this.diyFulfilmentService.getProductsForPack(pack.pack.productId, session.userId, tenant)
                    pack.pack.sessionIds = LivePackUtil.getProductV2DIYSessionIds(newPackConstruct.products)
                }
                const products = pack.pack.productType === "DIY_FITNESS_PACK" ? await this.diyFulfilmentService.getDIYFitnessProductsByProductIds(session.userId, pack.pack.sessionIds, tenant)
                    : await this.diyFulfilmentService.getDIYMeditationProductsByProductIds(session.userId, pack.pack.sessionIds, tenant)
                const packDetail = pack.pack.productType === "DIY_FITNESS_PACK" ? (await this.diyFulfilmentService.getDIYFitnessPacksForIds(session.userId, [pack.pack.productId]))[0] :
                    (await this.diyFulfilmentService.getDIYMeditationPacksForIds(session.userId, [pack.pack.productId]))[0]
                const productMap = LivePackUtil.getProductMap(products)
                packDetailMap[pack.pack.productId] = packDetail
                pack.pack.bookmarked = packDetail.bookmarked
                let duration = 0
                for (let j = 0; j < pack.pack.sessionIds.length; j++) {
                    const session = <DIYProduct>productMap[pack.pack.sessionIds[j]]
                    if (session) duration = duration + (session.duration / 1000) // duration is in seconds
                }
                durationMap.set(pack.pack.productId, duration / (pack.pack.sessionIds.length * 60))
                const nextSessionIdx = LiveUtil.getNextDIYSessionIdx(pack.pack, pack.fulfilment)
                let nextSession = <DIYProduct>productMap[pack.pack.sessionIds[nextSessionIdx]]
                if (_.isEmpty(nextSession)) {
                    nextSession = <DIYProduct>productMap[pack.pack.sessionIds[0]]
                }
                nextSessionMap.set(pack.pack.productId, nextSession)
            }
            const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(this.cultBusiness, this.diyFulfilmentService, userContext)
            const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, this.hamletBusiness)
            this.logger.debug("is TV app ", AppUtil.isTVApp(userContext))
            if (AppUtil.isInternationalTLApp(userContext) || AppUtil.isInternationalTLTVApp(userContext)) {
                const tlCardListView = new TLCardListView({
                    userContext,
                    header: diySeries.tlpHeaderImage ? diySeries.name : "",
                    description: diySeries.description,
                    headerImage: diySeries.tlpHeaderImage,
                    packs: packs.map(pack => pack.pack),
                    packDetails: packDetailMap,
                    nextSessionMap: nextSessionMap,
                })
                if (!diySeries.tlpHeaderImage) {
                    tlCardListView.widgets.unshift({
                        widgetType: "LIVE_HEADER_WIDGET",
                        title: diySeries.name,
                        headerContainerStyle: {
                            paddingTop: 45,
                            paddingLeft: 50
                        }
                    })
                }
                return tlCardListView
            }

            if (AppUtil.isTVAppWithApiKey(apiKey)) {
                return new SeriesDetailViewTV(this.logger, userAgent, isUserEligibleForMonetisation, diySeries, packs, durationMap, nextSessionMap, false, userContext, user.isInternalUser, isUserEligibleForTrial)
            }
            return new SeriesDetailViewV1(userAgent, isUserEligibleForMonetisation, diySeries, packs, durationMap, nextSessionMap, this.logger, blockInternationalUser, userContext, user.isInternalUser, isUserEligibleForTrial, bucketId)
        }

        @httpGet("/DIY/series/:seriesId")
        async getDIYSeriesDataBySeriesId(req: express.Request): Promise<DIYSeries> {
            const seriesId = req.params.seriesId
            const session: Session = req.session
            const userId = session.userId
            const productType = req.query.productType
            const userContext = req.userContext as UserContext
            const countryId = AppUtil.getCountryId(userContext)
            const diySeriesData = await this.serviceInterfaces.diyService.getDIYSeriesById(userId, seriesId, productType, countryId)

            return diySeriesData
        }

        @httpGet("/DIY/series/category/:category")
        async getDIYSeriesDataByCategory(req: express.Request): Promise<DIYSeries> {
            const category = req.params.category
            const session: Session = req.session
            const productType = req.query.productType
            const userContext = req.userContext as UserContext
            const countryId = AppUtil.getCountryId(userContext)
            let diySeriesData

            try {
                diySeriesData = await this.serviceInterfaces.diyService.getDIYSeriesByCategory(category, productType, countryId)
            } catch (err) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_PAGE_NOT_FOUND_ERROR, 404).withDebugMessage(`Cannot find Series for category ${category}`).build()
            }

            return diySeriesData
        }

        @httpGet("/DIY/cult/pack-details/:packId")
        async getCultDIYPackDetailsByPackId(req: express.Request): Promise<DIYFitnessPackExtended> {
            const packId = req.params.packId
            let packDetails: DIYFitnessPackExtended

            packDetails = await this.catalogueService.getDIYFitnessPack(packId)

            return packDetails
        }

        @httpGet("/DIY/mind/pack-details/:packId")
        async getMindDIYPackDetailsByPackId(req: express.Request): Promise<DIYMeditationPackExtended> {
            const packId = req.params.packId
            let packDetails: DIYMeditationPackExtended

            packDetails = await this.catalogueService.getDIYMeditationPack(packId)

            return packDetails
        }

        @httpGet("/DIY/pack-details/category/:category/subcategory/:subCategory")
        async getCultDIYPackDetailsByCategory(req: express.Request): Promise<DIYFitnessPackExtended | DIYMeditationPackExtended> {
            const { category, subCategory } = req.params
            const session: Session = req.session
            const productType: ProductType = req.query.productType
            const userId = session.userId

            let packDetails

            try {
                packDetails = productType === "DIY_FITNESS" ? await this.packService.getFitnessDIYPackByCategory(userId, category, subCategory) : await this.packService.getMindDIYPackByCategory(userId, category, subCategory)
            } catch (err) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_PAGE_NOT_FOUND_ERROR, 404).withDebugMessage(`Cannot find Pack Details for category: ${category}, sub-category: ${subCategory}`).build()
            }

            return packDetails.pack
        }

        @httpGet("/DIY/session/:packId")
        async getNextDiySession(req: express.Request): Promise<Action> {
            const session: Session = req.session
            const packId: string = req.params.packId
            const productPromise = this.catalogueService.getProduct(packId)
            const product = await productPromise
            const packDetailPromise = (product.productType === "DIY_FITNESS_PACK" ? this.packService.getFitnessDIYPackV2(packId, session.userId) : this.packService.getMindDIYPackV2(packId, session.userId))
            const packDetail = await packDetailPromise
            const tenant = AppUtil.getTenantFromReq(req)
            const products = product.productType === "DIY_FITNESS_PACK" ? await this.diyFulfilmentService.getDIYFitnessProductsByProductIds(session.userId, packDetail.pack.sessionIds, tenant) : await this.diyFulfilmentService.getDIYMeditationProductsByProductIds(session.userId, packDetail.pack.sessionIds, tenant)
            const productMap = LivePackUtil.getProductMap(products)
            return this.getNextSessionAction(packDetail, productMap)
        }

        private async getNextSessionAction(packDetail: DIYUserMeditationPack | DIYUserFitnessPack, productMap: any): Promise<Action> {
            const nextSessionIdx = LiveUtil.getNextDIYSessionIdx(packDetail.pack, packDetail.fulfilment)
            const nextSession = <DIYProduct>productMap[packDetail.pack.sessionIds[nextSessionIdx]]
            const content = AtlasUtil.getContentDetailV2(nextSession)
            const meta = AtlasUtil.getContentMetaV2(nextSession, packDetail.pack)
            return {
                actionType: "NAVIGATION",
                url: (content.type === "audio" ? ActionUtil.audioUrl(content.URL, UrlPathBuilder.prefixSlash(packDetail.pack.imageDetails.heroImage), meta) : ActionUtil.videoUrl(content.URL, UrlPathBuilder.prefixSlash(packDetail.pack.imageDetails.heroImage), meta)),
                meta: meta
            }
        }

        @httpPost("/DIY/favorite")
        async addOrRemoveDiyItemToFavorite(req: express.Request): Promise<{ success: boolean }> {
            const productType: ProductType = req.body.productType
            const productId = req.body.productId
            const action = req.body.action
            const userContext = req.userContext as UserContext
            try {
                if (action === "ADD") {
                    await this.diyFulfilmentService.addDIYProductBookmarks(userContext.userProfile.userId, productType, [productId])
                } else {
                    await this.diyFulfilmentService.removeDIYProductBookmarks(userContext.userProfile.userId, productType, [productId])
                }
                return { success: true }
            } catch (e) {
                return { success: false }
            }
        }

        @httpGet("/DIY/favorites")
        async getUserFavoritePackAndSessions(req: express.Request): Promise<{ widgets: WidgetView[] }> {
            const productType = req.query.productType
            const userContext = req.userContext as UserContext
            const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(this.cultBusiness, this.diyFulfilmentService, userContext)
            const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, this.hamletBusiness)
            return this.diyFavoritesViewBuilder.getView(userContext, productType, isUserEligibleForMonetisation, isUserEligibleForTrial, bucketId)
        }

        @httpGet("/scheduleDiy/:packId")
        async getDiySchedule(req: express.Request): Promise<DIYScheduleDetail> {
            const session: Session = req.session
            const packId: string = req.params.packId
            const productType: string = req.query.productType
            const productIds: string[] = [packId]
            const userContext = req.userContext as UserContext
            const diyPackFulfilment = await this.DIYFulfilmentService.getDIYPackFulfilmentsForUser(productIds, session.userId)
            return new DIYScheduleDetail(userContext, packId, productType, diyPackFulfilment.length === 0 ? undefined : diyPackFulfilment[0])
        }

        @httpGet("/mind/:packId")
        async getMindDIYPack(req: express.Request): Promise<MindDIYPackDetailViewV2> {
            const session: Session = req.session
            const packId: string = req.params.packId
            const appVersion: number = Number(req.headers["appversion"])
            const userAgent: UserAgent = session.userAgent
            const diyPack = await this.packService.getMindDIYPackV2(packId, session.userId)
            const tenant = AppUtil.getTenantFromReq(req)
            const products = await this.diyFulfilmentService.getDIYMeditationProductsByProductIds(session.userId, diyPack.pack.sessionIds, tenant)
            const productMap = LivePackUtil.getProductMap(products)
            const daysRemainingWidget = await new DaysRemainingWidgetView().buildView(this.cfServiceInterfaces, req.userContext, { source: "minddiypackpage" })
            const userContext: UserContext = req.userContext as UserContext
            const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(this.cultBusiness, this.diyFulfilmentService, userContext)
            const userHasActiveMembership: { start: number, end: number } | undefined = await LiveUtil.getActiveMembershipDates(userContext, this.serviceInterfaces.diyService, this.serviceInterfaces.catalogueService)
            return new MindDIYPackDetailViewV2(userAgent, appVersion, diyPack, this.mindDIYPackPageConfig, productMap, [], daysRemainingWidget, req.userContext, isUserEligibleForTrial, isUserEligibleForMonetisation, userHasActiveMembership)
        }

        @httpGet("/mindDIY/v2/:packId")
        async getMindDIYPackV2(req: express.Request): Promise<MindDIYPackDetailViewV3 | TLDIYPackDetailView> {
            const session: Session = req.session
            const packId: string = req.params.packId
            const category: string = req.query.category
            const productId: string = req.query.productId
            const appVersion: number = Number(req.headers["appversion"])
            const userAgent: UserAgent = session.userAgent
            const apiKey: string = req.headers["api-key"] as string || req.headers["apikey"] as string
            const tenant = AppUtil.getTenantFromReq(req)
            const userContext = req.userContext as UserContext
            const user = await this.userCache.getUser(session.userId)
            const userId = session.userId
            const nodeRelationId = req.query.nodeRelationID
            let diyPack: DIYUserMeditationPack

            diyPack = await this.packService.getMindDIYPackV2(packId, userId)

            const products = await this.diyFulfilmentService.getDIYMeditationProductsByProductIds(session.userId, diyPack.pack.sessionIds, tenant)
            const productMap = LivePackUtil.getProductMap(products)

            this.logger.debug(`Mind Pack details called for user id: ${session.userId}; nodeRelationId: ${nodeRelationId}; productId: ${productId}; packId: ${packId}`)

            if (nodeRelationId) {
                const productType = productId ? "DIY_MEDITATION" : "DIY_MEDITATION_PACK"
                const nodeType = productId ? DIYSocialNodeType.SESSION : DIYSocialNodeType.PACK
                await this.diyFulfilmentService.addDIYSocialNodeRelation(session.userId, productType, nodeType, productId ? productId : packId, nodeRelationId)
            }
            const packDetail = (await this.diyFulfilmentService.getDIYMeditationPacksForIds(session.userId, [diyPack.pack.productId]))[0]
            const socialDataForPack = await this.diyFulfilmentService.getDIYSocialAssociatedParentNodes(session.userId, "DIY_MEDITATION_PACK", DIYSocialNodeType.PACK, [packId])
            const socialDataForSessions = await this.diyFulfilmentService.getDIYSocialAssociatedParentNodes(session.userId, "DIY_MEDITATION", DIYSocialNodeType.SESSION, diyPack.pack.sessionIds)
            let packRecommendedByBuddies: CultBuddiesJoiningListSmallView
            const sessionRecommendedByBuddiesMap: { [sessionId: string]: CultBuddiesJoiningListSmallView } = {}
            if (socialDataForPack && socialDataForPack[packId] && !_.isEmpty(socialDataForPack[packId].userIds)) {
                const attendingUsers: { userId: string }[] = socialDataForPack[packId].userIds.map(userId => { return { userId } })
                packRecommendedByBuddies = await LiveUtil.getBuddiesJoiningListSmallView(attendingUsers, this.userCache, PageTypes.CultDIYPack, true, "shared this")
            }
            for (const sessionId of diyPack.pack.sessionIds) {
                if (!socialDataForSessions[sessionId] || _.isEmpty(socialDataForSessions[sessionId].userIds)) continue
                const attendingUsers: { userId: string }[] = socialDataForSessions[sessionId].userIds.map(userId => { return { userId } })
                sessionRecommendedByBuddiesMap[sessionId] = await LiveUtil.getBuddiesJoiningListSmallView(attendingUsers, this.userCache, PageTypes.CultDIYPack, true, "shared this", 2)
            }
            const userHasActiveMembership: { start: number, end: number } | undefined = await LiveUtil.getActiveMembershipDates(userContext, this.serviceInterfaces.diyService, this.serviceInterfaces.catalogueService)
            const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(this.cultBusiness, this.diyFulfilmentService, userContext)

            const breadCrumbs: IBreadCrumb[] = []

            const response = new MindDIYPackDetailViewV3(userAgent, appVersion, diyPack, this.mindDIYPackPageConfig, productMap, packRecommendedByBuddies, sessionRecommendedByBuddiesMap, breadCrumbs, null, userContext, user.isInternalUser, isUserEligibleForTrial, isUserEligibleForMonetisation, userHasActiveMembership)

            if (AppUtil.isInternationalTLApp(userContext) || AppUtil.isInternationalTLTVApp(userContext)) {
                return new TLDIYPackDetailView(userAgent, appVersion, diyPack, this.cultDIYPackPageConfig, productMap, packRecommendedByBuddies, sessionRecommendedByBuddiesMap, [], null, userContext, user.isInternalUser, isUserEligibleForTrial, isUserEligibleForMonetisation, userHasActiveMembership, "", null, packDetail)
            }

            return response
        }

        @httpGet("/cult/v2")
        async getCultFitPackV2(req: express.Request): Promise<ProductDetailPage> {
            const session: Session = req.session
            if (req.query.centerId && req.query.centerId !== "undefined") {
                session.sessionData.cultCenterId = req.query.centerId
                await this.sessionBusiness.updateSessionData(session.at, session.sessionData)
            }
            if (req.query.centerServiceCenterId && req.query.centerServiceCenterId !== "undefined") {
                session.sessionData.centerServiceCultCenterId = req.query.centerServiceCenterId
                await this.sessionBusiness.updateSessionData(session.at, session.sessionData)
            }
            const userContext = req.userContext as UserContext

            this.populatePromiseMapCache(userContext, this.cfServiceInterfaces)
            // PackId to ProductId migration: ignoring: older checkout page still uses packId for everything
            const packId: number = req.query.packId
            const membershipId: number = req.query.membershipId
            const productId: string = req.query.productId
            const centerServiceId: string = req.query.centerServiceId
            const isNewPackPageSupported = AppUtil.isNewPackPageSupported(userContext)
            return this.baseCultPackDetail(req, packId, membershipId, "FITNESS", productId, isNewPackPageSupported, centerServiceId)
        }

        // PackId to ProductId migration: Depricated for Fitness
        // Web still uses this
        @httpGet("/cult/:packId")
        async getCultFitPack(req: express.Request): Promise<ProductDetailPage> {
            const session: Session = req.session
            if (req.query.centerId && req.query.centerId !== "undefined") {
                session.sessionData.cultCenterId = req.query.centerId
                await this.sessionBusiness.updateSessionData(session.at, session.sessionData)
            }
            // PackId to ProductId migration: ignoring: older checkout page still uses packId for everything
            const packId: any = req.params.packId
            if (packId === "v2") {
                this.logger.info("~~packId is v2: " + packId)
                this.logger.info("~~original req query is: " + JSON.stringify(req.query))
                return
            }
            const membershipId: number = req.query.membershipId
            return this.baseCultPackDetail(req, packId, membershipId, "FITNESS")
        }

        @httpGet("/cult/:packId/emiOptions")
        async getCultPackEmiOptions(req: express.Request): Promise<EmiOptions> {
            // PackId to ProductId migration: ignoring: productId used as packId
            const packId = req.params.packId
            const productType = req.query.productType
            const userContext = req.userContext as UserContext
            const session: Session = req.session

            if (productType === "GYMFIT_FITNESS_PRODUCT" || productType === "LUX_FITNESS_PRODUCT") {
                const emiOptions = await this.paymentDao.find({ condition: {} })
                const user = await userContext.userPromise
                const packOffersV3Response = await this.serviceInterfaces.offerServiceV3.getGymFitProductPrices({
                    productIds: [packId],
                    userInfo: {
                        userId: userContext.userProfile.userId,
                        deviceId: userContext.sessionInfo.deviceId,
                        phone: user?.phone,
                        email: user?.email,
                        workEmail: user?.workEmail
                    },
                    cityId: userContext.userProfile.cityId,
                    source: "CUREFIT_APP"
                })
                if (productType === "GYMFIT_FITNESS_PRODUCT") {
                    const gymPack = await this.serviceInterfaces.catalogueServicePMS.getProduct(packId)
                    if (AppUtil.isWeb(userContext) && !GymfitUtil.isPackSupportedinWeb(gymPack)) {
                        throw this.errorFactory.withCode("Pack not supported", HTTP_CODE.BAD_REQUEST).build()
                    } else if (!GymfitUtil.isPackSupportedinApp(gymPack)) {
                        throw this.errorFactory.withCode("Pack not supported", HTTP_CODE.BAD_REQUEST).build()
                    }
                    return this.buildGymEmiOptions(gymPack, emiOptions, productType, packOffersV3Response)
                } else if (productType === "LUX_FITNESS_PRODUCT") {
                    const luxPack: OfflineFitnessPack = await this.offlineFitnessPackService.getCachedPackById(packId)
                    if (AppUtil.isWeb(userContext) && !GymfitUtil.isPackSupportedinWeb(luxPack)) {
                        throw this.errorFactory.withCode("Pack not supported", HTTP_CODE.BAD_REQUEST).build()
                    } else if (!GymfitUtil.isPackSupportedinApp(luxPack)) {
                        throw this.errorFactory.withCode("Pack not supported", HTTP_CODE.BAD_REQUEST).build()
                    }
                    return this.buildGymEmiOptions(luxPack, emiOptions, productType, packOffersV3Response)
                }
            } else if (productType === "PLAY") {
                const playPack = await this.serviceInterfaces.catalogueServicePMS.getProduct(packId)
                const emiOptions = await this.paymentDao.find({ condition: {} })
                const user = await userContext.userPromise
                const cultCityId = await this.getCultCityId(req, userContext, session.sessionData.cityId, session.userId)
                const packOffersV3Response = await this.offerServiceV3.getPlayPackPrices({
                    cultCityId: cultCityId,
                    cityId: userContext.userProfile.cityId,
                    userInfo: {
                        userId: userContext.userProfile.userId,
                        deviceId: userContext.sessionInfo.deviceId,
                        email: user?.email,
                        phone: user?.phone,
                        workEmail: user?.workEmail
                    },
                    source: AppUtil.callSourceFromContext(userContext),
                    productIds: [packId],
                })
                return this.buildFitnessEmiOptions(playPack, emiOptions, productType, packOffersV3Response)
            } else if (productType === "TRANSFORM") {
                // get product
                // const product: Product = await this.catalogueService.getProduct(productId)

                // get emiOptions

            }

            let productId = packId
            if (Number.isInteger(Number(productId))) { // If packId is cultPackId
                productId = productType === "MIND" ? CatalogueServiceV2Utilities.getMindPackProductId(packId) : CatalogueServiceV2Utilities.getCultPackProductId(packId)
            }
            const fitnessPack = await this.serviceInterfaces.catalogueServicePMS.getProduct(productId)
            const emiOptions = await this.paymentDao.find({ condition: {} })
            const user = await userContext.userPromise
            const cultCityId = await this.getCultCityId(req, userContext, session.sessionData.cityId, session.userId)
            const packOffersV3Response = await this.serviceInterfaces.offerServiceV3.getCultPackPrices({
                cultCityId: cultCityId,
                cityId: userContext.userProfile.cityId,
                userInfo: {
                    userId: userContext.userProfile.userId,
                    deviceId: userContext.sessionInfo.deviceId,
                    email: user?.email,
                    phone: user?.phone,
                    workEmail: user?.workEmail
                },
                source: AppUtil.callSourceFromContext(userContext),
                productIds: [productId],
                centerId: session.sessionData.cultCenterId
            })
            return this.buildFitnessEmiOptions(fitnessPack, emiOptions, productType, packOffersV3Response)
        }

        private async buildGymEmiOptions(pack: OfflineFitnessPack, emiOptions: EmiInterest[], productType: ProductType, packOffersV3Response: GymFitProductPricesResponse): Promise<EmiOptions> {
            let offerItem, offerDetails
            offerItem = CultUtil.getOfferItem(packOffersV3Response, pack.id)
            offerDetails = CultUtil.getOfferDetailsPMS(pack, packOffersV3Response)

            if (!offerItem) {
                return undefined
            }
            const offersMap = OfferUtil.segregateNoCostEMIOffers(offerItem.offers)
            const emiOffers = offersMap.get("NO_COST_EMI")
            if (!emiOffers.length) {
                return undefined
            }
            const emiWidgets: WidgetView[] = []
            let maxEmiTenure = 12
            emiOffers.forEach((emiOffer) => {
                emiOffer.addons.forEach((addOn) => {
                    if (!_.isEmpty(addOn.config?.eligibleNCEmiTenureInMonths)) {
                        maxEmiTenure = _.max((addOn.config.eligibleNCEmiTenureInMonths as number[]))
                    } else if (addOn.config && addOn.config.maxEmiTenure) {
                        maxEmiTenure = addOn.config.maxEmiTenure
                    }
                })
            })
            const offerCalloutWidget: OfferCalloutCardWidget = {
                widgetType: "CLP_CALLOUT_WIDGET",
                icon: "SAVINGS",
                subTitle: "Zero effective interest: you get upfront discount equal to the interest charged by bank"
            }
            emiWidgets.push(offerCalloutWidget)
            const packAmount = offerDetails.price.listingPrice
            _.map(emiOptions, (emiOption) => {
                const emiValues: EmiValues[][] = []
                emiOption.interestRates
                    .filter(interestRate => interestRate.duration <= maxEmiTenure)
                    .forEach((interestRate) => {
                        const months = interestRate.duration
                        if (months) {
                            const monthlyAmount = Math.round(packAmount / months)
                            const interest = Math.round(packAmount * (interestRate.effectiveInterest / 100))
                            emiValues.push([
                                { title: months.toString(), titleColor: "#585858", type: "MEDIUM" },
                                { title: `${RUPEE_SYMBOL}${monthlyAmount.toString()}`, titleColor: "#585858", type: "MEDIUM" },
                                { title: `+${RUPEE_SYMBOL}${interest.toString()}`, titleColor: "#d27373", bracketInfo: { title: `(${interestRate.interest}%)`, titleColor: "#878787" }, type: "MEDIUM" },
                                { title: `-${RUPEE_SYMBOL}${interest.toString()}`, titleColor: "#62af7d", type: "MEDIUM" }
                            ])
                        }
                    })
                const noCostEmiWidget: NoCostEmiDropdownWidget = {
                    widgetType: "NO_COST_EMI_WIDGET",
                    title: emiOption.bank,
                    expandIcon: "DROP_DOWN",
                    info: {
                        titles: [
                            { title: "Months", titleColor: "#a3a3a3", type: "REGULAR" },
                            { title: "EMI", titleColor: "#a3a3a3", type: "REGULAR" },
                            { title: "Interest(pa)", titleColor: "#a3a3a3", type: "REGULAR" },
                            { title: "Discount", titleColor: "#a3a3a3", type: "REGULAR" }
                        ],
                        values: emiValues
                    },
                    tnc: emiOption.tnc,
                    tncTitle: "How it works",
                    amountPayable: {
                        title: "Amount Payable",
                        amount: `${RUPEE_SYMBOL}${packAmount.toString()}*`
                    },
                    disclaimer: `*${GST_RATE_INSTANT_DISCOUNT * 100}% GST extra on interest/discount amount shown above`
                }
                if (!_.isUndefined(emiOption.tncUrl)) {
                    noCostEmiWidget.tncUrl = emiOption.tncUrl
                }
                const emiWidget: EmiWidget = {
                    widgetType: "EXPANDABLE_LIST_WIDGET",
                    defaultOpenIndex: -1,
                    widgets: [noCostEmiWidget],
                    fromPage: "nocostemipage",
                    allowMultipleOpen: false
                }
                emiWidgets.push(emiWidget)
            })
            return {
                title: "No Cost EMI",
                widgets: emiWidgets,
                allowMultipleOpen: false
            }
        }

        // /**
        //  * @deprecated
        //  */
        // private async buildEmiOptions(cultPack: CultPack, emiOptions: EmiInterest[], productType: ProductType, userContext: UserContext, preferredCenterId: string, packOffersResponse: PackOffersResponse, packOffersV3Response?: CultProductPricesResponse): Promise<EmiOptions> {
        //     const emiWidgets: WidgetView[] = []
        //     const offersMap = CultUtil.getOffersMap(cultPack, packOffersResponse, packOffersV3Response)
        //     if (!offersMap.get("NO_COST_EMI").length) {
        //         return undefined
        //     }
        //     const emiOffers = offersMap.get("NO_COST_EMI")
        //     let maxEmiTenure = 12
        //     emiOffers.forEach((emiOffer) => {
        //         emiOffer.addons.forEach((addOn) => {
        //             if (addOn.config && addOn.config.maxEmiTenure) {
        //                 maxEmiTenure = addOn.config.maxEmiTenure
        //             }
        //         })
        //     })
        //     maxEmiTenure = cultPack.duration ? CultUtil.getEmiTenureForPack(cultPack.duration, maxEmiTenure) : maxEmiTenure
        //     const offerCalloutWidget: OfferCalloutCardWidget = {
        //         widgetType: "CLP_CALLOUT_WIDGET",
        //         icon: "SAVINGS",
        //         subTitle: "Zero effective interest: you get upfront discount equal to the interest charged by bank"
        //     }
        //     emiWidgets.push(offerCalloutWidget)
        //     const packInfo = productType === "FITNESS" ? CatalogueServiceUtilities.toCultFitProduct(cultPack) : CatalogueServiceUtilities.toMindFitProduct(cultPack)
        //     const result = CultUtil.getPackPriceAndOfferId(packInfo, parseInt(preferredCenterId), packOffersResponse, packOffersV3Response)
        //     const packAmount = result.price.listingPrice
        //     _.map(emiOptions, (emiOption) => {
        //         const emiValues: EmiValues[][] = []
        //         emiOption.interestRates
        //             .filter(interestRate => interestRate.duration <= maxEmiTenure)
        //             .forEach((interestRate) => {
        //                 const months = interestRate.duration
        //                 if (months) {
        //                     const monthlyAmount = Math.round(packAmount / months)
        //                     const interest = Math.round(packAmount * (interestRate.effectiveInterest / 100))
        //                     emiValues.push([
        //                         { title: months.toString(), titleColor: "#585858", type: "MEDIUM" },
        //                         { title: `${RUPEE_SYMBOL}${monthlyAmount.toString()}`, titleColor: "#585858", type: "MEDIUM" },
        //                         { title: `+${RUPEE_SYMBOL}${interest.toString()}`, titleColor: "#d27373", bracketInfo: { title: `(${interestRate.interest}%)`, titleColor: "#878787" }, type: "MEDIUM" },
        //                         { title: `-${RUPEE_SYMBOL}${interest.toString()}`, titleColor: "#62af7d", type: "MEDIUM" }
        //                     ])
        //                 }
        //             })
        //         const noCostEmiWidget: NoCostEmiDropdownWidget = {
        //             widgetType: "NO_COST_EMI_WIDGET",
        //             title: emiOption.bank,
        //             expandIcon: "DROP_DOWN",
        //             info: {
        //                 titles: [
        //                     { title: "Months", titleColor: "#a3a3a3", type: "REGULAR" },
        //                     { title: "EMI", titleColor: "#a3a3a3", type: "REGULAR" },
        //                     { title: "Interest(pa)", titleColor: "#a3a3a3", type: "REGULAR" },
        //                     { title: "Discount", titleColor: "#a3a3a3", type: "REGULAR" }
        //                 ],
        //                 values: emiValues
        //             },
        //             tnc: emiOption.tnc,
        //             tncTitle: "How it works",
        //             amountPayable: {
        //                 title: "Amount Payable",
        //                 amount: `${RUPEE_SYMBOL}${packAmount.toString()}*`
        //             },
        //             disclaimer: `*${GST_RATE_INSTANT_DISCOUNT * 100}% GST extra on interest/discount amount shown above`
        //         }
        //         if (!_.isUndefined(emiOption.tncUrl)) {
        //             noCostEmiWidget.tncUrl = emiOption.tncUrl
        //         }
        //         const emiWidget: EmiWidget = {
        //             widgetType: "EXPANDABLE_LIST_WIDGET",
        //             defaultOpenIndex: -1,
        //             widgets: [noCostEmiWidget],
        //             fromPage: "nocostemipage",
        //             allowMultipleOpen: false
        //         }
        //         emiWidgets.push(emiWidget)
        //     })
        //     return {
        //         title: "No Cost EMI",
        //         widgets: emiWidgets,
        //         allowMultipleOpen: false
        //     }
        // }

        private async buildFitnessEmiOptions(fitnessPack: OfflineFitnessPack, emiOptions: EmiInterest[], productType: ProductType, packOffersV3Response?: CultProductPricesResponse): Promise<EmiOptions> {
            const emiWidgets: WidgetView[] = []
            const offerItem = CultUtil.getOfferItem(packOffersV3Response, fitnessPack.productId)
            if (!offerItem) {
                return undefined
            }
            const offersMap = OfferUtil.segregateNoCostEMIOffers(offerItem.offers)
            if (!offersMap.get("NO_COST_EMI").length) {
                return undefined
            }
            const emiOffers = offersMap.get("NO_COST_EMI")
            let maxEmiTenure = 12
            emiOffers.forEach((emiOffer) => {
                emiOffer.addons.forEach((addOn) => {
                    if (!_.isEmpty(addOn.config?.eligibleNCEmiTenureInMonths)) {
                        maxEmiTenure = _.max((addOn.config.eligibleNCEmiTenureInMonths as number[]))
                    } else if (addOn.config && addOn.config.maxEmiTenure) {
                        maxEmiTenure = addOn.config.maxEmiTenure
                    }
                })
            })
            const offerCalloutWidget: OfferCalloutCardWidget = {
                widgetType: "CLP_CALLOUT_WIDGET",
                icon: "SAVINGS",
                subTitle: "Zero effective interest: you get upfront discount equal to the interest charged by bank"
            }
            emiWidgets.push(offerCalloutWidget)
            const result = CultUtil.getPackPriceAndOfferIdV2(fitnessPack, packOffersV3Response)
            const packAmount = result.price.listingPrice
            _.map(emiOptions, (emiOption) => {
                const emiValues: EmiValues[][] = []
                emiOption.interestRates
                    .filter(interestRate => interestRate.duration <= maxEmiTenure)
                    .forEach((interestRate) => {
                        const months = interestRate.duration
                        if (months) {
                            const monthlyAmount = Math.round(packAmount / months)
                            const interest = Math.round(packAmount * (interestRate.effectiveInterest / 100))
                            emiValues.push([
                                { title: months.toString(), titleColor: "#585858", type: "MEDIUM" },
                                { title: `${RUPEE_SYMBOL}${monthlyAmount.toString()}`, titleColor: "#585858", type: "MEDIUM" },
                                { title: `+${RUPEE_SYMBOL}${interest.toString()}`, titleColor: "#d27373", bracketInfo: { title: `(${interestRate.interest}%)`, titleColor: "#878787" }, type: "MEDIUM" },
                                { title: `-${RUPEE_SYMBOL}${interest.toString()}`, titleColor: "#62af7d", type: "MEDIUM" }
                            ])
                        }
                    })
                const noCostEmiWidget: NoCostEmiDropdownWidget = {
                    widgetType: "NO_COST_EMI_WIDGET",
                    title: emiOption.bank,
                    expandIcon: "DROP_DOWN",
                    info: {
                        titles: [
                            { title: "Months", titleColor: "#a3a3a3", type: "REGULAR" },
                            { title: "EMI", titleColor: "#a3a3a3", type: "REGULAR" },
                            { title: "Interest(pa)", titleColor: "#a3a3a3", type: "REGULAR" },
                            { title: "Discount", titleColor: "#a3a3a3", type: "REGULAR" }
                        ],
                        values: emiValues
                    },
                    tnc: emiOption.tnc,
                    tncTitle: "How it works",
                    amountPayable: {
                        title: "Amount Payable",
                        amount: `${RUPEE_SYMBOL}${packAmount.toString()}*`
                    },
                    disclaimer: `*${GST_RATE_INSTANT_DISCOUNT * 100}% GST extra on interest/discount amount shown above`
                }
                if (!_.isUndefined(emiOption.tncUrl)) {
                    noCostEmiWidget.tncUrl = emiOption.tncUrl
                }
                const emiWidget: EmiWidget = {
                    widgetType: "EXPANDABLE_LIST_WIDGET",
                    defaultOpenIndex: -1,
                    widgets: [noCostEmiWidget],
                    fromPage: "nocostemipage",
                    allowMultipleOpen: false
                }
                emiWidgets.push(emiWidget)
            })
            return {
                title: "No Cost EMI",
                widgets: emiWidgets,
                allowMultipleOpen: false
            }
        }

        private async buildTransformEMIOptions(order: Order, product: DiagnosticProduct, emiOptions: EmiInterest[]): Promise<EmiOptions> {
            const emiWidgets: WidgetView[] = []

            if (_.isEmpty(order.offersApplied)) return
            const offersMap: { [key: string]: OfferV2 } = await this.offerServiceV3.getOffersByIds(order.offersApplied).then(o => o.data)
            let noEMIOffer: OfferV2 = undefined
            _.each(offersMap, (offer: OfferV2) => {
                if (offer.constraints.paymentChannel === "EMI") {
                    noEMIOffer = offer
                }
            })
            if (!noEMIOffer) {
                return undefined
            }
            let maxEmiTenure = 12
            noEMIOffer.addons.forEach((addOn) => {
                if (addOn.config && addOn.config.maxEmiTenure) {
                    maxEmiTenure = addOn.config.maxEmiTenure
                }
            })
            const offerCalloutWidget: OfferCalloutCardWidget = {
                widgetType: "CLP_CALLOUT_WIDGET",
                icon: "SAVINGS",
                subTitle: "Zero effective interest: you get upfront discount equal to the interest charged by bank"
            }
            emiWidgets.push(offerCalloutWidget)
            const packAmount = product.price.listingPrice
            _.map(emiOptions, (emiOption) => {
                const emiValues: EmiValues[][] = []
                emiOption.interestRates
                    .filter(interestRate => interestRate.duration <= maxEmiTenure)
                    .forEach((interestRate) => {
                        const months = interestRate.duration
                        if (months) {
                            const monthlyAmount = Math.round(packAmount / months)
                            const interest = Math.round(packAmount * (interestRate.effectiveInterest / 100))
                            emiValues.push([
                                { title: months.toString(), titleColor: "#585858", type: "MEDIUM" },
                                { title: `${RUPEE_SYMBOL}${monthlyAmount.toString()}`, titleColor: "#585858", type: "MEDIUM" },
                                { title: `+${RUPEE_SYMBOL}${interest.toString()}`, titleColor: "#d27373", bracketInfo: { title: `(${interestRate.interest}%)`, titleColor: "#878787" }, type: "MEDIUM" },
                                { title: `-${RUPEE_SYMBOL}${interest.toString()}`, titleColor: "#62af7d", type: "MEDIUM" }
                            ])
                        }
                    })
                const noCostEmiWidget: NoCostEmiDropdownWidget = {
                    widgetType: "NO_COST_EMI_WIDGET",
                    title: emiOption.bank,
                    expandIcon: "DROP_DOWN",
                    info: {
                        titles: [
                            { title: "Months", titleColor: "#a3a3a3", type: "REGULAR" },
                            { title: "EMI", titleColor: "#a3a3a3", type: "REGULAR" },
                            { title: "Interest(pa)", titleColor: "#a3a3a3", type: "REGULAR" },
                            { title: "Discount", titleColor: "#a3a3a3", type: "REGULAR" }
                        ],
                        values: emiValues
                    },
                    tnc: emiOption.tnc,
                    tncTitle: "How it works",
                    amountPayable: {
                        title: "Amount Payable",
                        amount: `${RUPEE_SYMBOL}${packAmount.toString()}*`
                    },
                    disclaimer: `*${GST_RATE_INSTANT_DISCOUNT * 100}% GST extra on interest/discount amount shown above`
                }
                if (!_.isUndefined(emiOption.tncUrl)) {
                    noCostEmiWidget.tncUrl = emiOption.tncUrl
                }
                const emiWidget: EmiWidget = {
                    widgetType: "EXPANDABLE_LIST_WIDGET",
                    defaultOpenIndex: -1,
                    widgets: [noCostEmiWidget],
                    fromPage: "nocostemipage",
                    allowMultipleOpen: false
                }
                emiWidgets.push(emiWidget)
            })
            return {
                title: "No Cost EMI",
                widgets: emiWidgets,
                allowMultipleOpen: false
            }
        }

        @httpGet("/cult/:membershipId/pausePackInfo")
        async getCultPausePackInfo(req: express.Request): Promise<PausePackInfo> {
            const membershipId: number = req.params.membershipId
            const pauseStartDate: string = req.query.pauseStartDate
            const pauseEndDate: string = req.query.pauseEndDate
            const isEdit = req.query.isEdit
            const productType = req.query.productType
            const userContext = req.userContext as UserContext
            if (productType === "GYMFIT_FITNESS_PRODUCT" || productType === "ONEPASS_PRODUCT" || productType === "LUX_FITNESS_PRODUCT") {
                return this.gymPausePackViewBuilder.getPausePackInfo(Number(membershipId), userContext, pauseStartDate, pauseEndDate, isEdit)
            } else if (productType === "PLAY") {
                return this.playPausePackViewBuilder.getPausePackInfo(Number(membershipId), userContext, pauseStartDate, pauseEndDate, isEdit)
            }
            return this.cultPausePackViewBuilder.getPausePackInfo(membershipId, userContext, pauseStartDate, pauseEndDate, isEdit)
        }

        @httpPost("/cult/:membershipId/editPauseDate")
        async editPauseDate(req: express.Request): Promise<Membership | CultMembership> {
            return this.editPauseDateV2(req)
        }

        @httpPost("/cult/membership/v2/:membershipId/editPauseDate")
        async editPauseDateV2(req: express.Request): Promise<Membership | CultMembership> {
            try {
                const membershipId: number = req.params.membershipId
                const pauseDurationDays = { pauseDurationDays: req.body.pauseDurationDays }
                const userContext = req.userContext as UserContext
                const userId = userContext.userProfile.userId
                const productType = req.query.productType
                const params = req.body
                const pauseStartDate = params.pauseStartDate
                const pauseDuration = params.pauseDurationDays
                const pauseMembershipParams: PauseMembershipParams = {
                    pauseDurationDays: params.pauseDurationDays,
                    pauseStartDate: params.pauseStartDate,
                    agentID: userContext.userProfile.userId,
                    comment: params.comment
                }
                if (productType === "GYMFIT_FITNESS_PRODUCT" || productType === "LUX_FITNESS_PRODUCT") {
                    return this.gymfitEditPauseEndDate(membershipId, userId, pauseMembershipParams, userContext)
                } else if (productType === "PLAY") {
                    return this.playEditPauseEndDate(membershipId, userId, pauseMembershipParams, userContext)
                }

                const membershipInfo = await this.membershipService.getMembershipById(membershipId)

                let pauseStartDateFormatted
                const tz = userContext.userProfile.timezone

                if (pauseStartDate) {
                    pauseStartDateFormatted = moment(pauseStartDate).format("YYYY-MM-DD hh:mm A")
                } else if (membershipInfo.activePause) {
                    pauseStartDateFormatted = moment(membershipInfo.activePause.start).format("YYYY-MM-DD hh:mm A")
                }

                if (pauseStartDateFormatted) {
                    const pauseStartDateMoment = TimeUtil.getMomentForDateString(pauseStartDateFormatted, tz).toDate()
                    const pauseEndDateMoment = TimeUtil.addToDate(pauseStartDateMoment, tz, pauseDuration, "days").toDate()
                    const maxEndDate = TimeUtil.addToDate(pauseStartDateMoment, tz, Math.floor( membershipInfo.remainingPauseDuration / (24 * 60 * 60 * 1000)), "days").toDate()

                    if (pauseStartDateFormatted && pauseDuration && pauseStartDateMoment && pauseEndDateMoment &&
                        !TimeUtil.isDateBetween(pauseStartDateMoment, maxEndDate, pauseEndDateMoment)) {
                        throw this.errorFactory.withCode(ErrorCodes.CULT_PAUSE_END_DATE_EXCEEDING_LIMIT, 400).withDebugMessage("Pack pause end date exceeding the limit").build()
                    }
                    const pauseStartDateFormat = TimeUtil.formatDateStringInTimeZone(pauseStartDate, tz, "YYYY-MM-DD")
                    const pausedEndDateFormat = TimeUtil.formatDateInTimeZone(tz, pauseEndDateMoment, "YYYY-MM-DD")
                    // if (req.body.cancelAllClasses) {
                    //     await this.cultFitService.cancelUpcomingBookingsForMembershipInDateRangeV2(Number(membershipId), userId, pauseStartDateFormat, pausedEndDateFormat)
                    // }
                    await this.cultFitService.cancelUpcomingBookingsForMembershipInDateRangeV2(Number(membershipId), userId, pauseStartDateFormat, pausedEndDateFormat)

                }
                return await this.cultFitService.editPauseEndDateV2({ membershipId, pauseDurationDays, userId, appName: process.env.APP_NAME })
            } catch (e) {
                this.logger.error(`Error while editing pause for user id ${req.session.userId}`, e)
                throw e
            }

        }

        // PackId to ProductId migration: TODO: it's used from web only So, unaware of this flow
        @httpGet("/fitnessfirst/:packId")
        async getFitnessFirstPack(req: express.Request): Promise<ProductDetailPage> {
            const packId: number = req.params.packId
            return this.baseFitnessFirstPackDetail(req, packId, "FITNESS_FIRST_PACK")
        }

        @httpGet("/event/mind/:eventId")
        async getMindEvent(req: express.Request): Promise<ProductDetailPage> {
            const session: Session = req.session
            const eventId: string = req.params.eventId
            const productType: ProductType = "MIND"
            return this.eventDetailPage(req, eventId, productType)
        }

        @httpGet("/event/fitness/:eventId")
        async getCultEvent(req: express.Request): Promise<ProductDetailPage> {
            const session: Session = req.session
            const eventId: string = req.params.eventId
            const productType: ProductType = "FITNESS"
            return this.eventDetailPage(req, eventId, productType)
        }

        @httpGet("/cult/mind/v2")
        async getMindFitPackV2(req: express.Request): Promise<ProductDetailPage> {
            const session: Session = req.session
            if (req.query.centerId && req.query.centerId !== "undefined") {
                session.sessionData.mindCenterId = req.query.centerId
                await this.sessionBusiness.updateSessionData(session.at, session.sessionData)
            }
            const packId: number = req.query.packId
            const membershipId: number = req.query.membershipId
            const userContext = req.userContext as UserContext
            const isNewPackPageSupported = AppUtil.isNewPackPageSupported(userContext)
            return this.baseCultPackDetail(req, packId, membershipId, "MIND", null, isNewPackPageSupported)
        }

        @httpGet("/cult/mind/:packId")
        async getCultMindPack(req: express.Request): Promise<ProductDetailPage> {
            const session: Session = req.session
            if (req.query.centerId && req.query.centerId !== "undefined") {
                session.sessionData.mindCenterId = req.query.centerId
                await this.sessionBusiness.updateSessionData(session.at, session.sessionData)
            }
            const membershipId: number = req.query.membershipId
            const packId: number = req.params.packId
            return this.baseCultPackDetail(req, packId, membershipId, "MIND")
        }


        @httpGet("/food/v1/:packId")
        async getFoodPackV1(req: express.Request): Promise<ProductDetailPage> {
            const session: Session = req.session
            const packId: string = req.params.packId
            const fulfilmentId: string = req.query.fulfilmentId
            const addressId: string = req.query.addressId
            const productAreaId: string = req.query.areaId
            const productSubArea: string = req.query.subArea
            const userId: string = session.userId
            const forceEnableBuy: boolean = req.query.forceEnableBuy === "true"
            const deviceId: string = session.deviceId
            const lat: number = req.query.lat
            const lon: number = req.query.lon
            const kioskId: string = req.query.kioskId
            const numTickets: number = req.query.numTickets
            const isSubPack: boolean = req.query.isSubPack === "true"
            const userContext: UserContext = req.userContext as UserContext
            const tenant: Tenant = AppUtil.getTenantFromReq(req)
            if (addressId) {
                await this.userBusiness.updateBrowseLocation(session, { addressId: addressId }, tenant)
            } else if (productAreaId) {
                const res = await this.userBusiness.updateBrowseLocation(session, { areaId: productAreaId, subArea: productSubArea }, tenant)
            } else if (kioskId) {
                const address = await this.userBusiness.addKiosk(userId, kioskId)
                await this.userBusiness.updateBrowseLocation(session, { addressId: address.addressId }, tenant)
            }
            let subscriptionType: SubscriptionType = undefined
            if (req.query.subscriptionType === "WEEKLY")
                subscriptionType = "WEEKLY"
            else if (req.query.subscriptionType === "MONTHLY")
                subscriptionType = "MONTHLY"
            const preferredLocation = await this.userBusiness.getPreferredLocation(userContext, userId, session.sessionData, lon, lat, undefined, true, "EAT_FIT")
            const areaId = preferredLocation.area ? preferredLocation.area.areaId : preferredLocation.defaultArea.areaId
            const foodPackResponse = await this.packService.getFoodPack(packId, userId, areaId, fulfilmentId)
            const packIds = []
            packIds.push(packId)
            if (_.get(foodPackResponse, "booking.subPackBookings") && !_.isEmpty(foodPackResponse.booking.subPackBookings)) {
                for (const booking of foodPackResponse.booking.subPackBookings) {
                    packIds.push(booking.packId)
                }
            }
            const issuesMapPromise = this.CRMIssueService.getIssuesMap()
            const eatPackOfferRequestParam: EatOfferRequestParams = {
                areaId: areaId,
                cityId: userContext.userProfile.cityId,
                userId: userId,
                deviceId: deviceId,
                productIds: packIds,
                source: AppUtil.callSourceFromContext(userContext)
            }
            const eatSingleOfferRequestParam: EatOfferRequestParamsV3 = {
                areaId: areaId,
                cityId: userContext.userProfile.cityId,
                userId: userId,
                deviceId: deviceId,
                dateMealSlotMap: await this.menuService.getDateMealSlotsMap(areaId),
                source: AppUtil.callSourceFromContext(userContext)
            }
            const packOffersPromise = Promise.resolve({} as FoodPackOffersResponseV2)

            const issuesMap: Map<string, CustomerIssueType[]> = await issuesMapPromise
            const packOffersResponse: FoodPackOffersResponseV2 = await packOffersPromise

            if (forceEnableBuy) {
                foodPackResponse.booking.packState = "NOT_BOUGHT"
            }
            const mealSlot: MealSlot = foodPackResponse.packInfo.mealSlot
            let weeklyWeekendsEnabled: boolean = req.query.weeklyWeekendsEnabled === "true" ? true : req.query.weeklyWeekendsEnabled === undefined && (mealSlot === "BREAKFAST" || mealSlot === "DINNER")
            let monthlyWeekendsEnabled: boolean = req.query.monthlyWeekendsEnabled === "true" ? true : req.query.monthlyWeekendsEnabled === undefined && (mealSlot === "BREAKFAST" || mealSlot === "DINNER")
            // We don't have weekend enabled in kiosk
            if (preferredLocation.address && preferredLocation.address.kioskId) {
                weeklyWeekendsEnabled = false
                monthlyWeekendsEnabled = false
            }
            if (req.query.monthlyWeekendsEnabled === undefined && foodPackResponse.packInfo.packType === "BUNDLE") {
                monthlyWeekendsEnabled = true
                weeklyWeekendsEnabled = true
            }
            return this.mealSubscriptionDetailViewBuilder.getView(req.userContext as UserContext, this.mealPackPageConfig, preferredLocation, foodPackResponse.packInfo, issuesMap, foodPackResponse.booking,
                foodPackResponse.menu, foodPackResponse.products, subscriptionType, packOffersResponse, {} as FoodSinglePriceOfferResponse, session, weeklyWeekendsEnabled, monthlyWeekendsEnabled, numTickets, isSubPack)
        }


        @httpPost("/renew/meal")
        async packRenewMeal(req: express.Request): Promise<{ url: string, orderMeta: OrderMeta, isSubscriptionRenew: boolean }> {
            const orderId = req.body.orderId
            const fulfilmentId = req.body.fulfilmentId
            const productId = req.body.productId
            const session: Session = req.session
            const userContext: UserContext = req.userContext as UserContext
            const foodpackPromise = this.catalogueService.getProduct(productId)
            const foodpack = <FoodPack>await foodpackPromise
            const preferredLocationPromise = this.userBusiness.getPreferredLocation(userContext, req.session.userId, req.session.sessionData, undefined, undefined, foodpack.mealSlot, true, "EAT_FIT")
            const orderPromise = this.omsApiClient.getOrder(orderId)
            const countryId = _.get(userContext, "userProfile.city.countryId", null)
            if (countryId !== "IN") {
                req.body.useFitCash = false
            }
            const useFitCash = !!req.body.useFitCash
            const userPromise = this.userCache.getUser(req.session.userId)

            const preferredLocation = await preferredLocationPromise
            const orderOriginal: Order = await orderPromise
            const areaId = (preferredLocation && preferredLocation.area) ? preferredLocation.area.areaId : "1"

            let productToRenew = orderOriginal.products.find((x) => (x.productId === productId))
            if (productToRenew === undefined)
                productToRenew = orderOriginal.products[0]
            const productOptions = productToRenew.option
            productOptions.previousFulfilmentId = fulfilmentId

            const eatOfferRequestParam: EatOfferRequestParams = {
                areaId: areaId,
                userId: session.userId,
                cityId: userContext.userProfile.cityId,
                deviceId: session.deviceId,
                productIds: [productId],
                source: AppUtil.callSourceFromContext(userContext)
            }
            const packOffersResponse = {} as FoodPackOffersResponseV2
            const foodPackOption = foodpack.options.find(option => { return option.numTickets === productOptions.numTickets })
            const priceAndOffer = OfferUtil.getFoodPackOfferAndPrice(foodpack, foodPackOption, packOffersResponse)
            if (!_.isEmpty(priceAndOffer.offers)) {
                productOptions.offerV2Ids = _.map(priceAndOffer.offers, offer => { return offer.offerId })
            }
            const fulfilment = await this.foodDao.findOne({ fulfilmentId })
            let orderCreated: Order
            if (!_.isNil(fulfilment.subscriptionId)) {
                orderCreated = await this.alfredService.createSubscriptionOrder(fulfilment.subscriptionId, req.body.startDate, useFitCash)
            } else {
                const orderDetails: OrderCreate = {
                    userId: req.session.userId,
                    deviceId: req.session.deviceId,
                    address: orderOriginal.userAddress,
                    products: [{
                        productId: productToRenew.productId,
                        option: productOptions,
                        productType: productToRenew.productType,
                        quantity: productToRenew.quantity
                    }],
                    useOffersV2: true,
                    source: "CUREFIT_APP",
                    dontCreateRazorpayOrder: true,
                    useFitCash: useFitCash,
                    tenant: AppUtil.getTenantFromUserContext(userContext),
                    osName: userContext.sessionInfo.osName,
                    appVersion: (userContext.sessionInfo.appVersion ?? 0).toString()
                }
                orderCreated = await this.omsApiClient.createOrder(orderDetails)
            }
            const user = await userPromise
            let numProducts = 0
            orderCreated.products.forEach(orderProduct => {
                numProducts = numProducts + orderProduct.quantity
            })

            let url = orderCreated.totalAmountPayable > 0 ? "curefit://payment?orderId=" + orderCreated.orderId : undefined
            url = !_.isNil(url) ? url + `&listingBrand=${_.get(orderOriginal, "eatOptions.listingBrand")}` : undefined

            return {
                isSubscriptionRenew: true,
                url: url,
                orderMeta: {
                    orderId: orderCreated.orderId,
                    customerName: user.firstName + " " + user.lastName,
                    customerEmail: user.email,
                    customerPhone: user.phone,
                    price: {
                        listingPrice: orderCreated.totalAmountPayable,
                        mrp: orderCreated.totalAmountPayable,
                        currency: orderCreated.productSnapshots[0].price.currency
                    },
                    productIds: orderCreated?.productSnapshots?.map(product => product.productId),
                    orderType: "EAT_PACK",
                    title: (orderCreated.productSnapshots[0].isPack) ? orderCreated.productSnapshots[0].title : (numProducts > 1 ? numProducts + " items ordered" : foodpack.title)
                }
            }
        }

        @httpPost("/food/:packId/resume")
        async resumePack(req: express.Request): Promise<ProductDetailPage | {
            message: {
                title: string,
                subTitle: string
            }
        }> {
            const session: Session = req.session
            const packId: string = req.params.packId
            const fulfilmentId: string = req.body.fulfilmentId
            const userId: string = session.userId
            let needDetails: boolean = req.body.needDetails
            let resumeDate: string = req.body.resumeDate
            let resumeSlotId: string = req.body.resumeSlotId
            const pageType: string = req.body.pageType
            const userContext = req.userContext as UserContext
            if (needDetails === undefined) {
                needDetails = true
            }

            const fulfilment = await this.fulfilmentService.getFoodFulfilmentByFulfilmentId(fulfilmentId)
            if (resumeSlotId === undefined) {
                resumeSlotId = fulfilment.deliverySlot
            }
            const areaTz: Timezone = await this.getTimezoneForFoodFulfilment(fulfilment)
            const packResponse: ResumePackResponse = await this.fulfilmentService.resumeFulfilment(fulfilmentId, resumeDate, resumeSlotId)
            const mealSlot: MealSlot = SlotUtil.getMealSlotForSlotId(resumeSlotId, areaTz)
            resumeDate = packResponse.resumeDate
            const calendarFormat = {
                sameDay: "[today]",
                nextDay: "[tomorrow]",
                nextWeek: "[on] dddd",
                sameElse: "[on] dddd"
            }
            const resumeDateFormatted = TimeUtil.getDayText(packResponse.resumeDate, areaTz, calendarFormat)
            const nextAvailableDay = TimeUtil.getDayText(packResponse.nextAvailableDate, areaTz, calendarFormat)
            const defaultSlotText = MealUtil.getSlotDisplayText(resumeSlotId)
            let title: string = undefined
            let subTitle: string = undefined
            let actions: Action[] = [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
            const foodFulfilment: FoodFulfilment = await this.fulfilmentService.getFoodFulfilmentByFulfilmentId(fulfilmentId)
            req.query.fulfilmentId = fulfilmentId
            const mealPackView = await this.getFoodPackV1(req)
            if (packResponse.success) {
                const resumeDay = TimeUtil.getDayText(packResponse.resumeDate, areaTz, calendarFormat)
                title = (foodFulfilment.subscriptionType === undefined ? "Pack Resumed" : "Meals Resumed")
                subTitle = "Your next meal will arrive " + resumeDay + " between " + defaultSlotText
            } else if (packResponse.error === undefined) {
                throw this.errorFactory.withCode(ErrorCodes.PACK_RESUME_FAILED, 400).withDebugMessage("Pack resume failed").build()
            } else if (MealUtil.isPackRedesignSupported(userContext)) {
                const meta = { needDetails: needDetails, pageType: pageType, packId: packId, fulfilmentId: fulfilmentId, resumeDate: packResponse.nextAvailableDate, resumeSlotId: resumeSlotId }
                if (packResponse.error === "SOLD_OUT") {
                    if (SlotUtil.isHardCutOffPassed(resumeDate, areaTz, resumeSlotId)) {
                        title = "Delivery Slot Closed"
                        subTitle = "Our apologies! Your regular delivery slot is closed. You can opt to have your next meal delivered " + nextAvailableDay + " between " + defaultSlotText
                        actions = [{
                            actionType: "RESUME_MEAL_PACK", title: "Deliver " + nextAvailableDay,
                            meta: meta
                        }, { actionType: "HIDE_ALERT_MODAL", title: "Stay Paused" }]
                    } else {
                        title = "Meal Sold Out"
                        subTitle = "Our apologies! This meal is sold out for " + resumeDateFormatted
                        //    + ". You can opt to change your meal and have it delivered " + resumeDateFormatted
                        actions = [
                            //     {
                            //     actionType: "NAVIGATION", url: ActionUtil.changeMealPage(fulfilment.products[0].productId, resumeDate, mealSlot, fulfilmentId, false),
                            //     title: "Change Meal"
                            // },
                            { actionType: "HIDE_ALERT_MODAL", title: "Okay" }]
                    }
                } else if (packResponse.error === "PAST_CUTOFF") {
                    const nowHM = TimeUtil.now(areaTz)
                    const deliveryChannel: DeliveryChannel = EatUtil.getDeliveryChannel(fulfilment.userAddress)
                    const nextDeliverySlot: DeliverySlot = SlotUtil.getDeliverySlotsForMealSlotAndChannel(mealSlot, deliveryChannel).find(ds =>
                        SlotUtil.compare(nowHM, ds.cutoffTime) < 0
                    )
                    const nextSlotText: string = MealUtil.getSlotDisplayText(nextDeliverySlot.slotId)
                    title = "Delivery Slot Closed"
                    subTitle = "Our apologies! Your regular delivery slot is closed.  You can opt to have your meal delivered between "
                        + MealUtil.getSlotDisplayText(nextDeliverySlot.slotId) + " or "
                        + nextAvailableDay + " between " + defaultSlotText + "."
                    actions = [{ actionType: "RESUME_MEAL_PACK", title: capitalizeFirstLetter(nextAvailableDay), meta: meta },
                    {
                        actionType: "RESUME_MEAL_PACK", title: nextSlotText,
                        meta: { ...meta, resumeDate: resumeDate, resumeSlotId: nextDeliverySlot.slotId }
                    }]
                }
            } else {
                const packRetryResponse: ResumePackResponse = await this.fulfilmentService.resumeFulfilment(fulfilmentId, packResponse.nextAvailableDate, resumeSlotId)
                if (packRetryResponse.success) {
                    const foodBooking = await this.shipmentService.getUpcomingFoodShipment(userId, fulfilmentId)
                    title = "Pack resumed"
                    subTitle = "Your next meal will arrive " + nextAvailableDay + ", " + MealUtil.getSlotDisplayTextFoodBooking(foodBooking)
                } else {
                    throw this.errorFactory.withCode(ErrorCodes.PACK_RESUME_FAILED, 400).withDebugMessage("Pack resume failed").build()
                }
            }

            this.setMessage(mealPackView, title, subTitle, actions)
            if (!needDetails) {
                return {
                    message: {
                        title: title,
                        subTitle: subTitle
                    },
                    alertInfo: {
                        title: title,
                        subTitle: subTitle,
                        actions: actions
                    }
                }
            }
            return mealPackView
        }

        private setMessage(productDetailPage: ProductDetailPage, title: string, subTitle: string, actions?: Action[], listItems?: { title: string, subTitle: string }[]) {
            productDetailPage.message = { title, subTitle }
            if (actions) {
                productDetailPage.alertInfo = {
                    title: title,
                    subTitle: subTitle,
                    actions: actions,
                    listItems: listItems
                }
            }
        }
        @httpPost("/cult/membership/v2/:membershipId/pause")
        async pauseCultMembershipV2(req: express.Request): Promise<ProductDetailPage> {
            return this.pauseCultMembershipV3(req)
        }

        @httpPost("/cult/membership/v3/:membershipId/pause")
        async pauseCultMembershipV3(req: express.Request): Promise<ProductDetailPage> {
            try {
                const membershipId: string = req.params.membershipId
                const params = req.body
                const session: Session = req.session
                const userContext = req.userContext as UserContext
                const pauseStartDate = params.pauseStartDate
                const pauseDuration = params.pauseDurationDays
                const membershipInfo = await this.membershipService.getMembershipById(Number(membershipId))
                const tz = userContext.userProfile.timezone

                if (pauseStartDate) {
                    const pauseStartDateFormatted = moment(pauseStartDate).format("YYYY-MM-DD hh:mm A")
                    const pauseStartDateMoment = TimeUtil.getMomentForDateString(pauseStartDateFormatted, tz).toDate()
                    const pauseEndDateMoment = TimeUtil.addToDate(pauseStartDateMoment, tz, pauseDuration, "days").toDate()
                    const maxEndDate = TimeUtil.addToDate(pauseStartDateMoment, tz, Math.floor(membershipInfo.remainingPauseDuration / (24 * 60 * 60 * 1000)), "days").toDate()
                    if (pauseStartDateFormatted && pauseDuration && pauseStartDateMoment && pauseEndDateMoment &&
                        !TimeUtil.isDateBetween(pauseStartDateMoment, maxEndDate, pauseEndDateMoment)) {
                        throw this.errorFactory.withCode(ErrorCodes.CULT_PAUSE_END_DATE_EXCEEDING_LIMIT, 400).withDebugMessage("Pack pause end date exceeding the limit").build()
                    }
                }
                if (params.cancelAllClasses) {
                    await this.cultFitService.cancelUpcomingBookingsForMembershipInDateRangeV2(Number(membershipId), session.userId, params.pauseStartDate, TimeUtil.addDays(tz, req.body.pauseStartDate, req.body.pauseDurationDays))
                }
                if (userContext && AppUtil.isPausePackStartDateSentAsToday(userContext)) {
                    // Handle older versions of app which sent today's date as start date
                    params.pauseStartDate = TimeUtil.addDays(userContext.userProfile.timezone, params.pauseStartDate, 1)
                }
                const pauseParams = { ...params }
                delete pauseParams.cancelAllClasses
                const membershipDetails = await this.cultFitService.pauseMembershipV3(session.userId, membershipId, pauseParams)
                return this.baseCultPackDetail(req, undefined, Number(membershipId), "FITNESS", membershipDetails.productId)
            } catch (e) {
                this.logger.error(`Error while pausing membership for user id ${req.session.userId}`, e)
                throw e
            }
        }

        @httpPost("/cult/membership/:membershipId/pause")
        async pauseCultMembership(req: express.Request): Promise<ProductDetailPage> {
            const membershipId: string = req.params.membershipId
            const session: Session = req.session
            const membership = await this.cultFitService.pauseMembership(membershipId, { userId: session.userId })
            const foreignMembershipId = await CultUtil.getMembershipIdByCultMembershipId(req.userContext as UserContext, membership.id.toString(), this.membershipService)
            return this.baseCultPackDetail(req, membership.packID, Number(foreignMembershipId), "FITNESS")
        }

        @httpPost("/cult/membership/:membershipId/resume")
        async resumeCultMembership(req: express.Request): Promise<ProductDetailPage> {
            return this.resumeCultMembershipV2(req)
        }

        @httpPost("/cult/membership/v2/:membershipId/resume")
        async resumeCultMembershipV2(req: express.Request): Promise<ProductDetailPage> {
            try {
                const membershipId: string = req.params.membershipId
                const session: Session = req.session
                const { productType } = req.body
                if (productType === "LUX_FITNESS_PRODUCT" || productType === "GYMFIT_FITNESS_PRODUCT") {
                    const unpauseRequest: UnpauseRequest = {
                        membershipId: Number(membershipId),
                        gapHandling: GapHandling.FILL
                    }
                    const membership: Membership = await this.membershipService.unpauseMembership(unpauseRequest, session.userId, "Unpause user request")
                    return new ProductDetailPage()
                }
                const membership = await this.cultFitService.resumeMembershipV3(membershipId, { userId: session.userId })
                return new ProductDetailPage()
            } catch (e) {
                this.logger.error(`Error while unpausing membership for user id ${req.session.userId}`, e)
                throw e
            }
        }

        @httpPost("/mind/membership/v2/:membershipId/pause")
        async pauseMindMembershipV2(req: express.Request): Promise<ProductDetailPage> {
            const membershipId: string = req.params.membershipId
            const params = req.body
            const session: Session = req.session
            const userContext = req.userContext as UserContext
            if (params.cancelAllClasses) {
                await this.cultFitService.cancelBookingsForMembershipFromDate(Number(membershipId), session.userId, params.pauseStartDate, process.env.APP_NAME, session.deviceId)
            }
            if (userContext && AppUtil.isPausePackStartDateSentAsToday(userContext)) {
                // Handle older versions of app which sent today's date as start date
                params.pauseStartDate = TimeUtil.addDays(userContext.userProfile.timezone, params.pauseStartDate, 1)
            }
            const pauseParams = { ...params }
            delete pauseParams.cancelAllClasses
            const membership = await this.mindFitService.pauseMembershipV2(session.userId, membershipId, pauseParams)
            return this.baseCultPackDetail(req, membership.packID, membership.id, "MIND")
        }

        @httpPost("/mind/membership/:membershipId/pause")
        async pauseMindMembership(req: express.Request): Promise<ProductDetailPage> {
            const membershipId: string = req.params.membershipId
            const session: Session = req.session
            const membership = await this.mindFitService.pauseMembership(membershipId, { userId: session.userId })
            return this.baseCultPackDetail(req, membership.packID, membership.id, "MIND")
        }

        @httpPost("/mind/membership/:membershipId/resume")
        async resumeMindMembership(req: express.Request): Promise<ProductDetailPage> {
            const membershipId: string = req.params.membershipId
            const session: Session = req.session
            const membership = await this.mindFitService.resumeMembership(membershipId, { userId: session.userId }) as CultMembership
            return this.baseCultPackDetail(req, membership.packID, membership.id, "MIND")
        }

        @httpPost("/cult/membership/:membershipId/cancelPause")
        async cancelPauseForMembership(req: express.Request): Promise<{ success: boolean }> {
            return this.cancelPauseForMembershipV2(req)
        }

        @httpPost("/cult/membership/v2/:membershipId/cancelPause")
        async cancelPauseForMembershipV2(req: express.Request): Promise<{ success: boolean }> {
            try {
                const membershipId: string = req.params.membershipId
                const productType: ProductType = req.body.productType
                const session: Session = req.session
                if (productType === "GYMFIT_FITNESS_PRODUCT" || productType === "ONEPASS_PRODUCT" || productType === "LUX_FITNESS_PRODUCT") {
                    return this.gymfitCancelPause(Number(membershipId), session.userId)
                } else if (productType === "PLAY") {
                    return this.playCancelPause(Number(membershipId), session.userId)
                }
                if (!this.cultFitService) {
                    throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage(`Bad Request: Must be Cult or Mind Pack, received ${productType}`).build()
                }
                return await this.cultFitService.cancelPauseForMembershipV2(membershipId, session.userId, "CUREFIT_API")
            } catch (e) {
                this.logger.error(`Error while cancelling pause for user id ${req.session.userId}`, e)
                throw e
            }

        }

        private async eventDetailPage(req: express.Request, eventId: string, productType: ProductType): Promise<ProductDetailPage> {
            let fitnessEvent: FitnessEvent = null
            const userContext = req.userContext as UserContext
            if (productType === "FITNESS") {
                fitnessEvent = await this.cultFitService.getFitnessEvent(eventId, process.env.APP_NAME)
            } else if (productType === "MIND") {
                fitnessEvent = await this.mindFitService.getFitnessEvent(eventId, process.env.APP_NAME)
            }
            const userId: string = req.session.userId
            return this.eventDetailViewBuilder.getView(userContext, fitnessEvent, productType, AppUtil.isNotLoggedinUserId(userId))

        }

        private async baseFitnessFirstPackDetail(req: express.Request, packId: number, productType: ProductType): Promise<ProductDetailPage> {
            const session: Session = req.session
            const userId = session.userId
            const userAgent: UserAgent = session.userAgent
            const userContext = req.userContext
            const deviceId: string = session.deviceId
            let packDetails: FitnessFirstPack
            if (packId) {
                packDetails = await this.catalogueService.getFitnessFirstPack(packId.toString())
            }
            const offerRequestParams: BaseOfferRequestParams = {
                userId,
                cityId: userContext.userProfile.cityId,
                deviceId,
                productIds: [packDetails.productId],
                source: AppUtil.callSourceFromContext(userContext)
            }
            this.logger.info("CARE::DEBUG Call attempted to offer-service-v2 to fetch fitness first offers", {offerRequestParams})
            const offersResponse: PackOffersResponse = undefined
            return this.fitnessFirstPackDetailViewBuilder.getView(userContext, offersResponse, packDetails, this.fitnessFirstPackPageConfig)
        }


        private async baseCultPackDetailV2(req: express.Request, membershipDetail: MembershipDetail, product: AugmentedOfflineFitnessPack, centerServiceId?: string): Promise<ProductDetailPage> {
            const baseService: ICultService = product.productType !== "MIND" ? this.cultFitService : this.mindFitService

            const centerId: string = req.query.centerId && req.query.centerId !== "undefined" ? req.query.centerId : undefined
            this.logger.info("baseCultPackDetail v2 2 " + centerId)

            const canChangeCenter: boolean = req.query.canChangeCenter !== "false"
            const selectedStartDate: string = req.query.startDate
            const selectedSubUserId: string = req.query.subUserId
            const userPromise = this.userCache.getUser(req.session.userId)
            this.logger.info("baseCultPackDetail v2 5 " + selectedSubUserId + " : " + selectedStartDate)


            const user = await userPromise

            const packPageRequestParams: CultPackPageRequestParamsV2 = {
                baseService: baseService,
                productType: product.productType,
                productId: product.productId,
                packInfo: product,
                membershipDetail,
                canChangeCenter: canChangeCenter,
                selectedStartDate: selectedStartDate,
                selectedSubUserId: selectedSubUserId,
                selectedCenterId: req.query.centerId && req.query.centerId !== "undefined" ? req.query.centerId : undefined,
                subUserRelations: user.subUserRelations,
                centerServiceId: Number(centerServiceId),
            }

            return this.cultFitPackDetailViewBuilder.getViewV2(req.userContext as UserContext, packPageRequestParams)
        }

        private async baseCultPackDetail(req: express.Request, packId: number, membershipId: number, productType: ProductType, productId?: string, isNewPackPageSupported?: boolean, centerServiceId?: string): Promise<ProductDetailPage> {
            const baseService: ICultService = productType !== "MIND" ? this.cultFitService : this.mindFitService
            this.logger.info("baseCultPackDetail:: details", {packId, centerServiceId, productId, membershipId})
            const userContext = req.userContext as UserContext
            const isWeb = AppUtil.isWeb(userContext)
            if (productId === "CULTPACK0") productId = undefined // Hack: Remove when using a seperate action for membership details page

            let membershipDetail: MembershipDetail
            if (membershipId) {
                membershipDetail = await baseService.getMembershipByIdV2(membershipId, userContext.userProfile.userId)
            }

            let canonicalUrl: string
            if (!_.isEmpty(productId) && CatalogueServiceV2Utilities.isProdIdMigratingToPMS(productId) && !CatalogueServiceV2Utilities.isProdIdMigratedToPMS(productId)) {
                const pmsPack = await CatalogueServiceUtilities.getPackByReferenceId(this.offlineFitnessPackService, productId)
                if (pmsPack) canonicalUrl = CatalogueServiceUtilities.getPackWebAction(pmsPack)
                else this.logger.error("baseCultPackDetail:: canonicalUrl not found for productId", {productId})
            }

            if (productId && isWeb) {
                if (membershipDetail) {
                    productId = membershipDetail.membership.productId
                }
                this.logger.info("baseCultPackDetail productId" + productId)

                const userSegments =  await this.segmentationCacheClient.getUserSegments(userContext.userProfile.userId)
                const product: AugmentedOfflineFitnessPack = await this.catalogueServicePMS.getAugmentedPackById(productId, {includeExhaustiveBenefits: true, includeExtraCharges: true, context: {userSegmentIds: userSegments}})
                if (!product) {
                    return null
                }

                // access level
                if (
                    (product.productType === "FITNESS" && CatalogueServiceUtilities.isPack(product) && CatalogueServiceUtilities.getAccessLevel(product) === AccessLevel.CENTER)
                    || (product.productType === "PLAY" && CatalogueServiceUtilities.getAccessLevel(product) !== AccessLevel.CITY) ) {
                        centerServiceId = centerServiceId ?? CatalogueServiceUtilities.getExternalAccessLevelId(product)
                        const response: ProductDetailPage = await this.baseCultPackDetailV2(req, membershipDetail, product, centerServiceId)
                        if (!membershipDetail && canonicalUrl) _.set(response, "meta.canonicalUrl", canonicalUrl) // Setting only if it's for pack detail page
                        return response
                    }
            }

            if (_.isNil(packId) && _.isNil(productId)) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage(`Bad Request: packId/productId is undefined!`).build()
            }

            if (membershipDetail) {
                const isSelectMembership = MembershipItemUtil.isSelectMembership(membershipDetail.membership)
                if (isSelectMembership) {
                    packId = 0
                    productId = membershipDetail.membership?.productId
                }
            }


            if (_.isEmpty(productId) || productId === packId?.toString()) { // ProductId === packId if cultPackId is being passed as productId from app
                if (membershipDetail) productId = membershipDetail.membership.productId
                else productId = CatalogueServiceV2Utilities.getCultPackProductId(packId)
            }
            const userSegments =  await this.segmentationCacheClient.getUserSegments(userContext.userProfile.userId)
            const product: AugmentedOfflineFitnessPack = await this.catalogueServicePMS.getAugmentedPackById(productId, {
                includeExhaustiveBenefits: true,
                includeExtraCharges: true,

                context: {userSegmentIds: userSegments}
            })

            const canChangeCenter: boolean = req.query.canChangeCenter !== "false"
            const selectedStartDate: string = req.query.startDate
            const selectedSubUserId: string = req.query.subUserId
            const userPromise = this.userCache.getUser(req.session.userId)


            const user = await userPromise


            const packPageRequestParams: CultPackPageRequestParams = {
                baseService: baseService,
                productType: productType,
                productId: product.productId,
                packInfo: product,
                membershipDetail,
                canChangeCenter: canChangeCenter,
                selectedStartDate: selectedStartDate,
                selectedSubUserId: selectedSubUserId,
                selectedCenterId: req.query.centerId && req.query.centerId !== "undefined" ? req.query.centerId : undefined,
                subUserRelations: user.subUserRelations
            }
            const baseViewBuilder =  isNewPackPageSupported ? this.cultFitPackDetailViewBuilderV2 : this.cultFitPackDetailViewBuilder
            const view: ProductDetailPage = await baseViewBuilder.getView(req.userContext as UserContext, packPageRequestParams)
            if (!membershipDetail && canonicalUrl) _.set(view, "meta.canonicalUrl", canonicalUrl)
            return view
        }

        @httpPost("/food/:packId/changeSlot")
        async changePackSlot(req: express.Request): Promise<ProductDetailPage> {
            const session: Session = req.session
            const fulfilmentId: string = req.body.fulfilmentId
            const slotId: string = req.body.slotId
            const userId: string = session.userId
            const userContext = req.userContext as UserContext
            const result = await this.fulfilmentService.changeDeliverySlot(userId, { fulfilmentId: fulfilmentId }, slotId)
            if (result) {
                const foodFulfilment = await this.fulfilmentService.getFoodFulfilmentByFulfilmentId(fulfilmentId)
                const fulfilmentTz = await this.getTimezoneForFoodFulfilment(foodFulfilment)
                const isTodayAvailable = SlotUtil.isCancelCutOffPassed(TimeUtil.todaysDate(fulfilmentTz), fulfilmentTz, foodFulfilment.deliverySlot)
                req.query.fulfilmentId = fulfilmentId
                const mealPackView = await this.getFoodPackV1(req)
                this.mealSubscriptionDetailViewBuilder.setMessage(mealPackView, "Slot changed", "Your delivery slot has been changed" + isTodayAvailable ? "" : " starting tomorrow")
                return mealPackView
            } else {
                Promise.reject("Change pack slot failed from alfred")
            }
        }

        @httpPost("/food/:packId/changeInstruction")
        async changePackInstruction(req: express.Request): Promise<ProductDetailPage> {
            const session: Session = req.session
            const fulfilmentId: string = req.body.fulfilmentId
            const userId: string = session.userId
            const eatDeliveryInstruction: DeliveryInstruction = req.body.eatDeliveryInstruction
            const isWeekendAddress: boolean = req.body.isWeekendAddress
            const userContext = req.userContext as UserContext
            if (!MealUtil.isCutleryChangeSupported(userContext)) {
                if (eatDeliveryInstruction.dropInstruction === "ME") {
                    eatDeliveryInstruction.contactInstruction = "CALL_ME"
                } else {
                    eatDeliveryInstruction.contactInstruction = "DONT_CALL"
                }
            }
            const result = await this.fulfilmentService.changeDeliveryInstructionForFulfilment(userId, { fulfilmentId: fulfilmentId }, eatDeliveryInstruction, isWeekendAddress)
            if (result) {
                const foodFulfilment = await this.fulfilmentService.getFoodFulfilmentByFulfilmentId(fulfilmentId)
                const timezone = await this.getTimezoneForFoodFulfilment(foodFulfilment)
                const isTodayAvailable = SlotUtil.isCancelCutOffPassed(TimeUtil.todaysDate(timezone), timezone, foodFulfilment.deliverySlot)
                req.query.fulfilmentId = fulfilmentId
                const mealPackView = await this.getFoodPackV1(req)
                this.mealSubscriptionDetailViewBuilder.setMessage(mealPackView, "Instruction changed", "Your delivery instruction has been changed" + isTodayAvailable ? "" : " starting tomorrow")
                return mealPackView
            } else {
                Promise.reject("Change pack instruction failed from alfred")
            }
        }

        @httpPost("/meditationDIY/:id/subscribe")
        async subscribeMindDIYPack(req: express.Request): Promise<ConfirmationView> {
            const session: Session = req.session
            const packId: string = req.params.id
            const preferredTime: HourMin = req.body.preferredTime
            const preferredDays: number[] = req.body.preferredDays
            const reminder: boolean = req.body.reminder
            const userContext: UserContext = req.userContext as UserContext
            return this.subscribeDIYPack(userContext, packId, session.userId, preferredTime, preferredDays, reminder)
        }

        @httpPost("/fitnessDIY/:id/subscribe")
        async subscribeFitnessDIYPack(req: express.Request): Promise<ConfirmationView> {
            const session: Session = req.session
            const packId: string = req.params.id
            const preferredTime: HourMin = req.body.preferredTime
            const preferredDays: number[] = req.body.preferredDays
            const reminder: boolean = req.body.reminder
            const userContext: UserContext = req.userContext as UserContext
            return this.subscribeDIYPack(userContext, packId, session.userId, preferredTime, preferredDays, reminder)
        }

        @httpPost("/contentSubscription/:contentId")
        async contentSubscription(req: express.Request): Promise<any> {
            const session: Session = req.session
            const subscriptionValue = req.body.status
            const advertiserId = req.body.advertiserId
            req.body.userId = session.userId
            const userContext: UserContext = req.userContext
            const user: User = await userContext.userPromise
            req.body.tenant = AppUtil.getTenantFromUserContext(userContext)

            if (!_.isEmpty(advertiserId)) {
                req.body.analyticsMeta = {
                    advertiserId,
                    selectedCity: {
                        city: userContext.userProfile.city.cityId,
                        country: userContext.userProfile.city.countryId
                    },
                    detectedCity: session.sessionData.detectedCity,
                    tenant: AppUtil.callSourceFromContext(userContext)
                }
            }

            const sessionDetail: DigitalCatalogueEntryV1 = await this.DIYFulfilmentService.getDigitalCatalogueEntry(req.params.contentId)

            /* To prevent calendar modal opening twice after after booking class */
            const cultPreference: PreferenceDetail = (subscriptionValue === "SUBSCRIBED" && req.body.noCalendarAddEvent) ? undefined : (await eternalPromise(this.cultBusiness.getClassRemindersPreference(userContext, userContext.sessionInfo.sessionData.cityId, userContext.userProfile.userId, "FITNESS"))).obj
            const calendarPref = (_.isEmpty(cultPreference)) ? false : cultPreference.bookingEmailPreference
            const result: {
                action?: Action
            } = {}

            if (subscriptionValue === "SUBSCRIBED") {
                if (AppUtil.isSugarFitOrUltraFitApp(userContext)
                    && sessionDetail && sessionDetail.tags
                    && sessionDetail.tags.includes("WEBINAR")) {
                    this.userBusiness.publishUserActivityEventToRashi(session.userId, "SF_WEBINAR_BOOKED", {
                        start_time: sessionDetail?.scheduledTimeEpoch,
                        content_id: req.params.contentId,
                        user_id: session.userId,
                    }, AppUtil.getAppTenantFromUserContext(userContext))
                }

                req.body.whatsappEligibilityReminder = true
                await this.DIYFulfilmentService.subscribeUser(req.body).then((value) => {
                    this.DIYFulfilmentService.addSocialNode(session.userId, req.params.contentId)
                    return value
                })
                result.action = await LiveUtil.getCreateCalendarEventAction(userContext, sessionDetail, this.classInviteLinkCreator, calendarPref)
            } else {
                if (AppUtil.isSugarFitOrUltraFitApp(userContext)
                    && sessionDetail && sessionDetail.tags
                    && sessionDetail.tags.includes("WEBINAR")) {
                    this.userBusiness.publishUserActivityEventToRashi(session.userId, "SF_WEBINAR_CANCELLED", {
                        start_time: sessionDetail?.scheduledTimeEpoch,
                        content_id: req.params.contentId,
                        user_id: session.userId,
                    }, AppUtil.getAppTenantFromUserContext(userContext))
                }
                // req.body.status = "SUBSCRIBED"
                await this.DIYFulfilmentService.unsubscribeUser(req.body)
                result.action = LiveUtil.getDeleteCalendarEventAction(sessionDetail, calendarPref, userContext.sessionInfo, user)
            }
            return result
        }

        // Used for TV App to book class and return a confirmation view
        @httpPost("/bookClass/:contentId")
        async subscription(req: express.Request): Promise<ConfirmationView | any> {
            const result = await this.contentSubscription(req)
            if (!_.isEmpty(result) && result.action?.actionType !== "DELETE_CALENDAR_EVENT") {
                const classId = req.params.contentId
                const userContext: UserContext = req.userContext
                const apiKey: string = req.headers["api-key"] as string || req.headers["apikey"] as string
                const params: ConfirmationRequestParams = {
                    orderSource: AppUtil.callSource(apiKey),
                    userContext: userContext
                }
                return await this.orderConfirmationViewBuilderV1.buildLiveClassConfirmationView(userContext, classId, params, req)
            }
            return result
        }

        @httpPost("/DIY/:id/subscriptionUpdate")
        async updateSubscriptionDIYPack(req: express.Request): Promise<DIYUserPack> {

            const session: Session = req.session
            const packId: string = req.params.id
            const preferredTime: HourMin = req.body.preferredTime
            const preferredDays: number[] = req.body.preferredDays
            const reminder: boolean = req.body.reminder
            const subscribePromise = this.DIYFulfilmentService.updatePackSubscriptionForUser(packId, session.userId, preferredTime, preferredDays, reminder)
            const result = await subscribePromise
            if (!result) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("Update Subscription failed. Try again").build()
            }
            const packDetailPromise = (result.productType === "DIY_MEDITATION_PACK") ? this.packService.getMindDIYPackV2(packId, session.userId) : this.packService.getFitnessDIYPackV2(packId, session.userId)
            const packDetail = await packDetailPromise
            return packDetail
        }

        private async subscribeDIYPack(userContext: UserContext, packId: string, userId: string, preferredTime: HourMin, preferredDays: number[], reminder: boolean) {
            const subscribePromise = this.DIYFulfilmentService.subscribeDIYPackForUser(packId, userId, preferredTime, preferredDays, reminder, undefined, userContext.userProfile.timezone, AppUtil.getCountryId(userContext))
            const packPromsise = this.catalogueService.getProduct(packId)
            const result = await subscribePromise
            const pack = <DIYPack>await packPromsise
            const tenant = AppUtil.getTenantFromUserContext(userContext)
            const products = pack.productType === "DIY_FITNESS_PACK" ? await this.diyFulfilmentService.getDIYFitnessProductsByProductIds(userId, pack.sessionIds, tenant) : await this.diyFulfilmentService.getDIYMeditationProductsByProductIds(userId, pack.sessionIds, tenant)
            // To avoid relegated sessions in pack.sessionIds.
            pack.sessionIds = products.map(product => product.productId)
            const productMap = LivePackUtil.getProductMap(products)
            if (!result) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("Subscription failed. Try again").build()
            }
            return this.orderConfirmationViewBuilder.buildDIYSubscriptionConfirmationViewV2(userContext, pack, productMap, result.DIYPackFulfilment)

        }

        @httpPost("/meditationDIY/:id/unSubscribe")
        unSubscribeMindDIYPack(req: express.Request): Promise<ContentPackDetailViewV2> {
            const session: Session = req.session
            const packId: string = req.params.id
            const appVersion: number = Number(req.headers["appversion"])
            const userAgent: UserAgent = session.userAgent
            const userContext = req.userContext
            return this.unSubscribeDIYPack(userAgent, appVersion, packId, session.userId, "DIY_MEDITATION", userContext)
        }

        @httpPost("/meditationDIY/v2/:id/unSubscribe")
        unSubscribeMindDIYPackV2(req: express.Request): Promise<ContentPackDetailViewV3 | {}> {
            const session: Session = req.session
            const packId: string = req.params.id
            const requestSource: string = req.body.requestSource
            const appVersion: number = Number(req.headers["appversion"])
            const userAgent: UserAgent = session.userAgent
            const userContext = req.userContext
            return this.unSubscribeDIYPackV2(userAgent, appVersion, packId, session.userId, "DIY_MEDITATION", userContext, requestSource)
        }

        private async unSubscribeDIYPack(userAgent: UserAgent, appVersion: number, packId: string, userId: string, productType: ProductType, userContext: UserContext): Promise<ContentPackDetailViewV2> {
            const unSubscribePromise = this.DIYFulfilmentService.unSubscribeDIYPackForUser(packId, userId, AppUtil.getCountryId(userContext), userContext.userProfile.timezone)
            const productPromise = this.catalogueService.getProduct(packId)
            const result = await unSubscribePromise
            const product = await productPromise

            const userPackPromsise = (product.productType === "DIY_FITNESS_PACK" ? this.packService.getFitnessDIYPackV2(packId, userId) : this.packService.getMindDIYPackV2(packId, userId))
            const userPack = await userPackPromsise
            if (!result) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("Unsubscribe failed. Try again").build()
            }
            const tenant = AppUtil.getTenantFromUserContext(userContext)
            const products = userPack.pack.productType === "DIY_FITNESS_PACK" ? await this.diyFulfilmentService.getDIYFitnessProductsByProductIds(userId, userPack.pack.sessionIds, tenant) : await this.diyFulfilmentService.getDIYMeditationProductsByProductIds(userId, userPack.pack.sessionIds, tenant)
            const productMap = LivePackUtil.getProductMap(products)
            const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(this.cultBusiness, this.diyFulfilmentService, userContext)
            const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, this.hamletBusiness)
            const blockInternationalUser = await AppUtil.blockFitnessContentForInternationalUser(userContext, this.maxmindService, this.logger)
            const daysRemainingWidget = await new DaysRemainingWidgetView().buildView(this.cfServiceInterfaces, userContext, { source: "diypackpage" })
            const userHasActiveMembership: { start: number, end: number } | undefined = await LiveUtil.getActiveMembershipDates(userContext, this.serviceInterfaces.diyService, this.serviceInterfaces.catalogueService)
            if (userPack.pack.productType === "DIY_FITNESS_PACK") {
                return new FitnessDIYPackDetailViewV2(userAgent, appVersion, <DIYUserFitnessPack>userPack, this.cultDIYPackPageConfig, productMap, [], blockInternationalUser, daysRemainingWidget, userContext, isUserEligibleForTrial, isUserEligibleForMonetisation, userHasActiveMembership, bucketId)
            } else {
                return new MindDIYPackDetailViewV2(userAgent, appVersion, <DIYUserMeditationPack>userPack, this.mindDIYPackPageConfig, productMap, [], daysRemainingWidget, userContext, isUserEligibleForTrial, isUserEligibleForMonetisation, userHasActiveMembership)
            }

        }

        private async unSubscribeDIYPackV2(userAgent: UserAgent, appVersion: number, packId: string, userId: string, productType: ProductType, userContext?: UserContext, requestSource?: string): Promise<ContentPackDetailViewV3 | {}> {
            const unSubscribePromise = this.DIYFulfilmentService.unSubscribeDIYPackForUser(packId, userId, AppUtil.getCountryId(userContext), userContext.userProfile.timezone)
            const productPromise = this.catalogueService.getProduct(packId)
            const result = await unSubscribePromise
            const product = await productPromise
            const user = await this.userCache.getUser(userId)
            const userPackPromsise = (product.productType === "DIY_FITNESS_PACK" ? this.packService.getFitnessDIYPackV2(packId, userId) : this.packService.getMindDIYPackV2(packId, userId))
            const userPack = await userPackPromsise
            if (!result) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("Unsubscribe failed. Try again").build()
            }

            if (requestSource === "HOME_PAGE") {
                return {}
            }
            const tenant = AppUtil.getTenantFromUserContext(userContext)

            let productsV2
            if (userPack.pack.productType === "DIY_FITNESS_PACK") {
                productsV2 = await this.diyFulfilmentService.getProductsForPack(packId, userId, tenant)

                if (!(userPack.pack.sessionIds?.length > 0) && productsV2.products && LivePackUtil.isProductV2DIY(productsV2.products)) {
                    userPack.pack.sessionIds = LivePackUtil.getProductV2SessionIds(productsV2.products)
                }
            }

            const products = userPack.pack.productType === "DIY_FITNESS_PACK" ? await this.diyFulfilmentService.getDIYFitnessProductsByProductIds(userId, userPack.pack.sessionIds, tenant) : await this.diyFulfilmentService.getDIYMeditationProductsByProductIds(userId, userPack.pack.sessionIds, tenant)
            const productMap = LivePackUtil.getProductMap(products)
            const daysRemainingWidget = await new DaysRemainingWidgetView().buildView(this.cfServiceInterfaces, userContext, { source: "diypackpage" })
            const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(this.cultBusiness, this.diyFulfilmentService, userContext)
            const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, this.hamletBusiness)
            const blockInternationalUser = await AppUtil.blockFitnessContentForInternationalUser(userContext, this.maxmindService, this.logger)
            const userHasActiveMembership: { start: number, end: number } | undefined = await LiveUtil.getActiveMembershipDates(userContext, this.serviceInterfaces.diyService, this.serviceInterfaces.catalogueService)
            if (userPack.pack.productType === "DIY_FITNESS_PACK") {
                return new FitnessDIYPackDetailViewV3(userAgent, appVersion, <DIYUserFitnessPack>userPack, this.cultDIYPackPageConfig, productMap, null, null, [], blockInternationalUser, daysRemainingWidget, userContext, user.isInternalUser, isUserEligibleForTrial, isUserEligibleForMonetisation, userHasActiveMembership, bucketId, null, productsV2?.products)
            } else {
                return new MindDIYPackDetailViewV3(userAgent, appVersion, <DIYUserMeditationPack>userPack, this.mindDIYPackPageConfig, productMap, null, null, [], daysRemainingWidget, userContext, user.isInternalUser, isUserEligibleForTrial, isUserEligibleForMonetisation, userHasActiveMembership)
            }

        }

        @httpPost("/fitnessDIY/:id/unSubscribe")
        unSubscribeFitnessDIYPack(req: express.Request): Promise<ContentPackDetailViewV2> {
            const session: Session = req.session
            const packId: string = req.params.id
            const appVersion: number = Number(req.headers["appversion"])
            const userAgent: UserAgent = session.userAgent
            return this.unSubscribeDIYPack(userAgent, appVersion, packId, session.userId, "DIY_FITNESS", req.userContext)
        }

        @httpPost("/fitnessDIY/v2/:id/unSubscribe")
        unSubscribeFitnessDIYPackV2(req: express.Request): Promise<ContentPackDetailViewV3 | {}> {
            const session: Session = req.session
            const packId: string = req.params.id
            const requestSource: string = req.body.requestSource
            const appVersion: number = Number(req.headers["appversion"])
            const userAgent: UserAgent = session.userAgent
            const userContext = req.userContext
            return this.unSubscribeDIYPackV2(userAgent, appVersion, packId, session.userId, "DIY_FITNESS", userContext, requestSource)
        }

        @httpGet("/food/:packId/cancelPage")
        async getFoodCancelPage(req: express.Request): Promise<CancelSubscriptionPage> {
            const fulfilmentId: string = req.query.fulfilmentId
            const foodFulfilment: FoodFulfilment = await this.fulfilmentService.getFoodFulfilmentByFulfilmentId(fulfilmentId)
            const cancelSubscriptionPage = new CancelSubscriptionPage("food", foodFulfilment, this.mealPackPageConfig, req.body.packIds)
            return cancelSubscriptionPage
        }

        @httpPost("/food/cancelPage/v2")
        async getFoodCancelPageV2(req: express.Request): Promise<CancelSubscriptionPage> {
            const fulfilmentId: string = req.body.fulfilmentId
            const foodFulfilment: FoodFulfilment = await this.fulfilmentService.getFoodFulfilmentByFulfilmentId(fulfilmentId)
            const cancelSubscriptionPage = new CancelSubscriptionPage("food", foodFulfilment, this.mealPackPageConfig, req.body.packIds)
            return cancelSubscriptionPage
        }

        @httpPost("/food/cancel")
        async cancelPackV2(req: express.Request): Promise<AlertInfo> {
            const userContext = req.userContext
            const fulfilmentId = req.body.fulfilmentId
            const apiKey: string = req.headers["apikey"] as string
            const orderSource: OrderSource = AppUtil.callSource(apiKey)
            let packIds = req.body.packIds
            if (_.isNil(packIds)) {
                packIds = [req.body.packId]
            }
            const result = await this.fulfilmentService.cancelSubscriptionFulfilment(fulfilmentId, orderSource, req.body.reason, req.body.reasonCode, packIds)
            return this.getCancelPackAction(result, req)
        }

        private async getCancelPackAction(result: any, req: express.Request): Promise<AlertInfo> {
            if (result) {
                const listItems = []
                const response: { booking?: FoodPackBooking, menu: { [date: string]: string } } = await this.shipmentService.getFoodShipmentsByFulfilmentId(req.body.fulfilmentId)
                let subTitle = "Your subscription has been successfully cancelled. "
                if (response.booking.refundDueDetails) {
                    if (response.booking.refundDueDetails.amountPayable) {
                        listItems.push({
                            title: "Amount refund",
                            subTitle: `${RUPEE_SYMBOL} ${Math.round(response.booking.refundDueDetails.amountPayable)} has been refunded to you`
                        })
                    }

                    if (response.booking.refundDueDetails.fitCashPayable) {
                        listItems.push({
                            title: "Fitcash refund",
                            subTitle: `Fitcash ${Math.round(response.booking.refundDueDetails.fitCashPayable)} has been refunded to you`
                        })
                    }
                } else if (response.booking.ticketsCancelled > 0) {
                    subTitle += "You had " + response.booking.ticketsCancelled + " meals that were remaining / cancelled."
                }
                return {
                    title: "Subscription Cancelled",
                    subTitle: subTitle,
                    listItems: listItems,
                    actions: [{ actionType: "POP_ACTION", title: "Thanks" }]
                }
            } else {
                console.log("Subscription cancel failed " + req.body.fulfilmentId)
                return {
                    title: "Cancellation Failed",
                    subTitle: "There was an error cancelling your subscription. Please try again",
                    actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
                }
            }
        }

        @httpPost("/food/:packId/cancel")
        async cancelPack(req: express.Request): Promise<AlertInfo> {
            const result = await this.fulfilmentService.cancelSubscriptionFulfilment(req.body.fulfilmentId, req.body.source, req.body.reason, req.body.reasonCode)
            return this.getCancelPackAction(result, req)
        }

        @httpGet("/cult/:packId/cancelPage") // DEPRECATED
        async getCultSubscriptionCancelPage(req: express.Request): Promise<CultCancelSubscriptionPage | CultCancelMembershipPage | GymfitCancelMembershipPage> {
            const membershipId: string = req.query.membershipId
            const productType = req.query.productType
            const packId: number = req.params.packId
            const userId = req.session.userId
            const userContext: UserContext = req.userContext as UserContext
            this.logger.error("PMS::DEPR Membership cancel is deprecated", { membershipId, productType, packId, userId })
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Membership cancel is deprecated").build()
            // if (productType === "GYMFIT_FITNESS_PRODUCT") {
            //     const membership = await this.membershipService.getMembershipById(parseInt(membershipId))
            //     if (req.query.pageFrom === "money_back_offer") {
            //         const moneybackfferDetail = await GymfitUtil.checkMoneyBackOfferOnMembership(membership, userContext, this.omsApiClient, this.gymfitPackPageConfig, this.logger)
            //         if (!_.isEmpty(moneybackfferDetail) && moneybackfferDetail.isOfferExpired) {
            //             const cancellationEndDate = TimeUtil.getMomentForDateString(moneybackfferDetail.cancellationWindow.endDate, userContext.userProfile.timezone).format("D MMM YYYY")
            //             throw this.errorFactory.withCode(ErrorCodes.CULT_MONEY_BACK_CANCEL_ERR, 400).withMeta({ cancellationEndDate: cancellationEndDate }).build()
            //         }
            //     }
            //     return new GymfitCancelMembershipPage(userContext, productType, membership, this.gymfitPackPageConfig)
            // }
            // // PackId to ProductId migration: ignoring: packId is used to convert to productid
            // const packProductId = productType === "MIND" ? CatalogueServiceV2Utilities.getMindPackProductId(packId) : CatalogueServiceV2Utilities.getCultPackProductId(packId)
            // const product = await this.catalogueServicePMS.getProduct(packProductId)
            // const membership = productType === "FITNESS" ? await this.cultFitService.getMembershipById(parseInt(membershipId), req.session.userId) : await this.mindFitService.getMembershipById(parseInt(membershipId), userId)
            // if (membership.isSubscription) {
            //     return new CultCancelSubscriptionPage(userContext, "cult", productType, membership, product, this.cultPackPageConfig)
            // } else {
            //     // if (req.query.pageFrom === "money_back_offer") {
            //     //     const userMemberships = await this.membershipService.getCachedMembershipsForUser(userId, AppUtil.getTenantFromUserContext(userContext), ["CULT"], ["PURCHASED", "PAUSED"])
            //     //     const currentMembership = userMemberships.find(userMembership => userMembership.metadata["membershipId"] == membership.id)
            //     //     const moneybackfferDetail = await this.cultBusiness.checkMoneyBackOfferOnMembership(currentMembership, userContext)
            //     //     if (!_.isEmpty(moneybackfferDetail) && moneybackfferDetail.isOfferExpired) {
            //     //         const cancellationEndDate = TimeUtil.getMomentForDateString(moneybackfferDetail.cancellationWindow.endDate, userContext.userProfile.timezone).format("D MMM YYYY")
            //     //         throw this.errorFactory.withCode(ErrorCodes.CULT_MONEY_BACK_CANCEL_ERR, 400).withMeta({ cancellationEndDate: cancellationEndDate }).build()
            //     //     }
            //     // } // Deprecated
            //     return new CultCancelMembershipPage(userContext, productType, membership, product, this.cultPackPageConfig)
            // }
        }

        @httpPost("/cult/:packId/cancel")
        async cancelCultSubscription(req: express.Request): Promise<AlertInfo> {
            const membershipId: string = req.body.membershipId
            const productType = req.body.productType
            if (productType === "GYMFIT_FITNESS_PRODUCT") {
                return await this.cancelGymMembership(req.body, parseInt(membershipId), req.userContext as UserContext)
            }
            const membership = productType === "FITNESS" ? await this.cultFitService.getMembershipById(parseInt(membershipId), req.session.userId) : await this.mindFitService.getMembershipById(parseInt(membershipId), req.session.userId)
            if (membership.isSubscription) {
                const result = await this.fulfilmentService.cancelFitnessSubscription(req.body.membershipId, req.body.source, req.body.reason, req.body.reasonCode)
                if (result) {
                    const productType = req.body.productType
                    let subTitle = "Your subscription has been successfully cancelled. "
                    const endDateString = membership.endDate
                    subTitle += `Your membership ends on ${endDateString}.`
                    return {
                        title: "Subscription Cancelled",
                        subTitle: subTitle,
                        actions: [{ actionType: "POP_ACTION", title: "Thanks" }]
                    }
                } else {
                    this.logger.error("Subscription cancel failed " + req.body.membershipId)
                    return {
                        title: "Cancellation Failed",
                        subTitle: "There was an error cancelling your subscription. Please try again",
                        actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
                    }
                }
            } else {
                return await this.cancelCultMembership(req.body, membership, req.userContext as UserContext)
            }
        }

        @httpGet("/mindTherapy/:productId")
        async getMindTherapyPackDetail(req: express.Request): Promise<ProductDetailPage> {
            const session: Session = req.session
            const { subCategoryCode, bookingId } = req.query
            const userContext = req.userContext as UserContext
            if (_.isEmpty(bookingId)) {
                const sellableProductId = req.query.sellableProductId || req.params.productId
                const baseProduct = <DiagnosticProduct>await this.catalogueService.getProduct(sellableProductId)
                const setCode = req.query.setCode || baseProduct?.setCode
                const clubCode = req.query.clubCode || baseProduct?.clubCode
                if (CareUtil.isNewPhysioMindTherapyProductPageSupported(userContext, subCategoryCode)) {
                    return this.mindTherapyPhysioPacksProductPageViewBuilder.getBeforeBookingBundleSessionsPage(
                        userContext,
                        sellableProductId,
                        subCategoryCode,
                        clubCode,
                        setCode
                    )
                }
                return this.mpProductPageViewBuilder.getBeforeBookingBundleSessionsPage(
                    userContext,
                    session.isNotLoggedIn,
                    session.userId,
                    session.deviceId,
                    sellableProductId,
                    subCategoryCode
                )
            } else {
                return this.mpProductPageViewBuilder.getAfterBookingPage(userContext, req.query.sellableProductId || req.params.productId, bookingId)
            }
        }

        @httpGet("/mindservice/:productId")
        async getMindTherapyServicePage(req: express.Request): Promise<ProductDetailPage> {
            const { subCategoryCode, groupType } = req.query
            const userContext = req.userContext as UserContext

            const sellableProductId = req.query.sellableProductId || req.params.productId
            const baseProduct = <DiagnosticProduct>await this.catalogueService.getProduct(sellableProductId)
            const setCode = req.query.setCode || baseProduct?.setCode
            const clubCode = req.query.clubCode || baseProduct?.clubCode
            return this.mindTherapyPhysioPacksProductPageViewBuilder.getBeforeBookingServiceDetailsPage(
                userContext,
                subCategoryCode,
                sellableProductId,
                groupType,
                clubCode,
                setCode
            )

        }

        @httpGet("/bundleSession/:productId")
        async getBundledSessionsPage(req: express.Request): Promise<ProductDetailPage> {
            const session: Session = req.session
            const userContext = req.userContext as UserContext
            const sellableProductId = req.query.sellableProductId || req.params.productId
            const baseProduct = <DiagnosticProduct>await this.catalogueService.getProduct(sellableProductId)
            const useCultMemberShipForPayment = req.query.useCultMemberShipForPayment === "true"
            const selectedDate = req.query.startDate
            const source = req.headers["user-agent"] as string
            const { subCategoryCode, membershipPaymentType, bookingId, filterType } = req.query
            const setCode = req.query.setCode || baseProduct?.setCode
            const clubCode = req.query.clubCode || baseProduct?.clubCode
            if (_.isEmpty(baseProduct)) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Product Details is Missing").build()
            }
            if (_.isEmpty(bookingId)) {
                if (subCategoryCode === "NUTRITIONIST" && AppUtil.isNewNutritionistFlowSupported(userContext)) {
                    return this.nutritionistPlanProductPageViewBuilder.getBeforeBookingBundleSessionsPage(
                        userContext,
                        sellableProductId,
                        subCategoryCode,
                        filterType,
                        setCode,
                        clubCode
                    )
                }
                if (CareUtil.isNewPhysioMindTherapyProductPageSupported(userContext, subCategoryCode) || CareUtil.isMindTherapyPageWeb(userContext, subCategoryCode)) {
                    return this.mindTherapyPhysioPacksProductPageViewBuilder.getBeforeBookingBundleSessionsPage(
                        userContext,
                        sellableProductId,
                        subCategoryCode,
                        clubCode,
                        setCode
                    )
                }
                if (AppUtil.isNewLivePtProductPageSupported(req.userContext, subCategoryCode)) {
                    return this.livePtPackPageViewBuilder.getBeforeBookingBundleSessionsPage(
                        userContext,
                        session.isNotLoggedIn,
                        session.userId,
                        session.deviceId,
                        sellableProductId,
                        subCategoryCode,
                        filterType,
                        useCultMemberShipForPayment,
                        membershipPaymentType,
                        selectedDate
                    )
                }
                return this.mpProductPageViewBuilder.getBeforeBookingBundleSessionsPage(
                    userContext,
                    session.isNotLoggedIn,
                    session.userId,
                    session.deviceId,
                    sellableProductId,
                    subCategoryCode,
                    filterType,
                    useCultMemberShipForPayment,
                    membershipPaymentType,
                    clubCode
                )
            } else {
                if (AppUtil.isLiveSgtProductPageSupported(req.userContext, subCategoryCode)) {
                    return this.livePtPackPageViewBuilder.getAfterBookingPage(userContext, req.params.productId, bookingId, session.userId, session.deviceId)
                }
                return this.mpProductPageViewBuilder.getAfterBookingPage(userContext, req.params.productId, bookingId, source)
            }
        }

        @httpGet("/live/browse")
        async getLivePacks(req: express.Request): Promise<LiveMembershipPacksPage> {
            const { selectedProductId } = req.query
            const userContext: UserContext = req.userContext as UserContext
            const requestSource = AppUtil.getRequestSource(userContext)
            const userId = userContext.userProfile.userId
            const deviceId = userContext.sessionInfo.deviceId
            const user = await this.userCache.getUser(userId)
            const tenant = AppUtil.getTenantFromUserContext(userContext)
            const livePacks: CFLiveProduct[] = await this.DIYFulfilmentService.getCFLiveProducts(userId, requestSource, tenant, deviceId)
            const userInfo: UserInfo = {
                userId: userId,
                deviceId: userContext.sessionInfo.deviceId,
                email: user.email,
                phone: user.phone,
                workEmail: user.workEmail
            }
            const productIds = LivePackUtil.getProductIds(livePacks)
            const livePricesResponse: LivePricesResponse = await this.offerServiceV3.getLivePackPrices(LivePackUtil.getProductIds(livePacks), userInfo)
            let offerIdOfferMap: { [offerId: string]: OfferV2 } = {}
            const offerIds: string[] = []
            productIds.forEach(productId => {
                const prices = livePricesResponse.priceMap[productId]
                if (prices && !_.isEmpty(prices.breakup)) {
                    prices.breakup.forEach((priceBreakup: DiscountBreakup) => {
                        offerIds.push(priceBreakup.offerId)
                    })
                }
            })

            if (AppUtil.isInternationalTLApp(userContext)) {
                return this.TLLivePacksModalViewBuilder.buildView(userContext, livePacks, livePricesResponse)
            }

            if (!_.isEmpty(offerIds)) {
                const offerResposne = await this.offerServiceV3.getOffersByIds(offerIds)
                offerIdOfferMap = offerResposne.data
            }
            const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, this.hamletBusiness)
            const isUserEligibleForMonetisation = AppUtil.isLivePackSupported(userContext)
            const segments: string[] = await this.cfServiceInterfaces.clsUtil.getPlatformSegments()
            let showTrialBanner = true
            if (!_.isEmpty(segments) && (segments.includes("cflive-free-trial-expired") || segments.includes("cflive-subscription-expired") || segments.includes("cflive-subscription-active"))) {
                showTrialBanner = false
            }
            return await this.livePacksViewBuilder.getLivePacksView(userContext, livePacks, livePricesResponse, offerIdOfferMap, isUserEligibleForMonetisation, selectedProductId, showTrialBanner, bucketId)
        }

        @httpPost("/diy/inviteLink")
        async getDiyInviteLinkAction(req: express.Request): Promise<{ action: Action }> {
            const userContext: UserContext = req.userContext as UserContext
            const packId: string = req.body.packId
            const productId: string = req.body.productId
            const productType: ProductType = req.body.productType
            const isSession = req.body.isSession
            const userId = userContext.userProfile.userId
            if (!userContext.sessionInfo.isUserLoggedIn) {
                return {
                    action: {
                        title: "LOGIN",
                        actionType: "SHOW_LOGIN_MODAL",
                        url: "curefit://loginmodal",
                    }
                }
            }
            const isFitnessProduct = productType === "DIY_FITNESS_PACK" || productType === "DIY_FITNESS"
            const diyPack = isFitnessProduct ? await this.packService.getFitnessDIYPackV2(packId, userId) :
                await this.packService.getMindDIYPackV2(packId, userId)
            const appTitle = AppUtil.getAppTitle(userContext)
            if (isSession) {
                const tenant = AppUtil.getTenantFromUserContext(userContext)
                const diyProducts = isFitnessProduct ? await this.diyFulfilmentService.getDIYFitnessProductsByProductIds(userId, [productId], tenant) :
                    await this.diyFulfilmentService.getDIYMeditationProductsByProductIds(userId, [productId], tenant)
                const diyProduct = diyProducts[0]
                const duration = momentTz.duration(typeof diyProduct.duration === "string" ? parseInt(diyProduct.duration) : diyProduct.duration).humanize()
                const metaText = isFitnessProduct ? `Hey, Try out ${duration} ${diyProduct.title} workout. Explore home workouts on ${appTitle} app and stay fit!`
                    : `Hey, Try out ${duration} ${diyProduct.title} meditation series on ${appTitle} app. Discover meditation for stress, anxiety, sleep & more on cult.fit app`
                const image = diyPack.pack.imageDetails.heroImage ? diyPack.pack.imageDetails.heroImage : diyPack.pack.imageDetails.magazineImage
                const url = await this.classInviteLinkCreator.getDiyClassInviteLink(userContext, diyPack.pack, productType, diyProduct.title, DIYSocialNodeType.SESSION, image, productId, diyProduct.title)
                return { action: CultUtil.buildDiyShareActionFromShareLink(metaText, url, productType) }
            } else {
                const metaText = isFitnessProduct ? `Hey, I found ${diyPack.pack.title} workout. Explore home workouts on ${appTitle} app and stay fit!.`
                    : `Hey, I found ${diyPack.pack.title} meditation series on ${appTitle} app. Discover meditation for stress, anxiety, sleep & more on cult.fit app`
                const image = diyPack.pack.imageDetails.heroImage ? diyPack.pack.imageDetails.heroImage : diyPack.pack.imageDetails.magazineImage
                const url = await this.classInviteLinkCreator.getDiyClassInviteLink(userContext, diyPack.pack, productType, diyPack.pack.title, DIYSocialNodeType.PACK, image)
                return { action: CultUtil.buildDiyShareActionFromShareLink(metaText, url, productType) }
            }
        }
        @httpGet("/cultpack/issues")
        async getCultPackIssues(req: express.Request): Promise<{packInfo: {title: string, description: string, startDate?: string, endDate?: string}, issuePayload: Action}[]> {
            const userContext: UserContext = req.userContext as UserContext
            const list: {packInfo: {title: string, description: string, startDate?: string, endDate?: string}, issuePayload: Action}[] = []
            const gymMemberships = await this.membershipService.getCachedMembershipsForUser(userContext.userProfile.userId, "curefit", ["GYMFIT_GA", "GYMFIT_GX"])
            const cultMemberships = await this.membershipService.getCachedMembershipsForUser(userContext.userProfile.userId, "curefit", ["CULT", "CULT_COMPLIMENTARY_ACCESS"])
            if (gymMemberships) {
                 await Promise.all(gymMemberships.map(async membership => {
                    const issueDetailView = await this.issueBusiness.getCultOrMindSubscriptionIssuesV2(userContext, membership, "FITNESS")
                    const payload = this.issueBusiness.toSupportPackActionPayload(issueDetailView, true)
                    const view = this.activePackViewBuilderV1.buildCultPackIssueView(userContext, membership)
                    list.push({packInfo: view, issuePayload: payload})
                }))
            }
            if (cultMemberships) {
                await Promise.all(cultMemberships.map(async membership => {
                    const issueDetailView = await this.issueBusiness.getCultOrMindSubscriptionIssuesV2(userContext, membership, "FITNESS")
                    const payload = this.issueBusiness.toSupportPackActionPayload(issueDetailView, true)
                    const view = this.activePackViewBuilderV1.buildCultPackIssueView(userContext, membership)
                    list.push({packInfo: view, issuePayload: payload})
                }))
            }
            const issueDetailView = await this.issueBusiness.getCultPackPreRegSubscriptionIssues(userContext)
            const payload = this.issueBusiness.toSupportPackActionPayload(issueDetailView, true)
            list.push({packInfo: {
                title: (cultMemberships && cultMemberships.length > 0) || (gymMemberships && gymMemberships.length > 0) ? "None of the above" : "Know about Cult pass", description: ""}, issuePayload: {
                    actionType: "NAVIGATION",
                    url: SUPPORT_DEEP_LINK,
                }})
            return list
        }
        private async cancelGymMembership(reqBody: any, membershipId: number, userContext: UserContext): Promise<AlertInfo> {
            const { productType, reasonCode, reason } = reqBody
            const userId = userContext.userProfile.userId
            const membership: Membership = await this.membershipService.getMembershipById(membershipId)
            const order: Order = await this.omsApiClient.getOrder(membership.orderId)
            const paymentdata: PaymentData = order.payments.find((x) => x.status === "paid")
            const prePaymentData: PrePaymentData = !_.isEmpty(order.prePayments) ? order.prePayments.find((x) => x.status === "paid") : undefined
            const paymentAmount: number = paymentdata ? paymentdata.amount : 0
            const prePaymentAmount: number = prePaymentData ? prePaymentData.amount : 0
            const refundAmount: number = paymentAmount + prePaymentAmount
            const refunds = [
                {
                    productId: order.products[0].productId,
                    quantity: order.products[0].quantity,
                    refundAmount: refundAmount
                }
            ]
            this.logger.info("Attempting to cancel order at OMS", {orderId: order.orderId})
            const result = await this.omsApiClient.refundOrderOMS(order.orderId, refunds, `${reasonCode}_${reason}`, false)
            this.logger.info("Cancel order at OMS results", {result})
            if (result) {
                const subTitle = "Your membership has been successfully cancelled. Amount paid will be credited to your account within 5-7 working days from today."
                const navigationUrl = "curefit://today"
                // if (reasonCode === "ANOTHER_PACK" || reasonCode === "RELOCATION") {
                //     navigationUrl = productType === "FITNESS" ? ActionUtil.cultPackBrowse(userContext.sessionInfo.userAgent) : ActionUtil.mindPackBrowse(userContext.sessionInfo.userAgent)
                // }
                return {
                    title: "Membership Cancelled",
                    subTitle: subTitle,
                    actions: [{ actionType: "NAVIGATION", url: navigationUrl, title: "Thanks" }]
                }
            } else {
                this.logger.error("cultpass PRO Membership cancel failed " + membershipId)
                return {
                    title: "Cancellation Failed",
                    subTitle: "There was an error cancelling your membership. Please try again",
                    actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
                }
            }
        }

        private async cancelCultMembership(reqBody: any, membership: CultMembership, userContext: UserContext): Promise<AlertInfo> {
            const { productType, membershipId, reasonCode, reason } = reqBody
            const userId = userContext.userProfile.userId
            const order: Order = await this.omsApiClient.getOrder(membership.cfOrderId)
            const paymentdata: PaymentData = order.payments.find((x) => x.status === "paid")
            const prePaymentData: PrePaymentData = !_.isEmpty(order.prePayments) ? order.prePayments.find((x) => x.status === "paid") : undefined
            const paymentAmount: number = paymentdata ? paymentdata.amount : 0
            const prePaymentAmount: number = prePaymentData ? prePaymentData.amount : 0
            const refundAmount: number = paymentAmount + prePaymentAmount
            const refunds = [
                {
                    productId: order.products[0].productId,
                    quantity: order.products[0].quantity,
                    refundAmount: refundAmount
                }
            ]
            this.logger.info("Attempting to cancel order at OMS", {orderId: order.orderId})
            const result = await this.omsApiClient.refundOrderOMS(order.orderId, refunds, `${reasonCode}_${reason}`, false)
            this.logger.info("Cancel order at OMS results", {result})
            if (result) {
                const subTitle = "Your membership has been successfully cancelled. Amount paid will be credited to your account within 5-7 working days from today."
                const navigationUrl = "curefit://today"
                return {
                    title: "Membership Cancelled",
                    subTitle: subTitle,
                    actions: [{ actionType: "NAVIGATION", url: navigationUrl, title: "Thanks" }]
                }
            } else {
                this.logger.error("Cult Membership cancel failed " + membershipId)
                return {
                    title: "Cancellation Failed",
                    subTitle: "There was an error cancelling your membership. Please try again",
                    actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
                }
            }
        }

        private async getTimezoneForFoodFulfilment(fulfilment: FoodFulfilment): Promise<Timezone> {
            return fulfilment && fulfilment.userAddress && fulfilment.userAddress.areaId ?
                this.deliveryAreaService.getTimeZoneForAreaId(fulfilment.userAddress.areaId) : TimeUtil.IST_TIMEZONE
        }

        private async gymfitEditPauseEndDate (membershipId: number, userId: string, pauseParams: PauseMembershipParams, userContext: UserContext): Promise<CultMembership> {
            const membership: Membership = await this.membershipService.getMembershipById(membershipId)
            const pauseStart = membership.activePause ? membership.activePause.start : null
            const pauseEnd = GymfitUtil.convertDaysToMillis(pauseParams.pauseDurationDays) + pauseStart - 1000
            let editPauseRequest: EditPauseRequest = {
                membershipId: membershipId,
                start: pauseStart,
                end: pauseEnd
            }
            if (membership.status === "PAUSED") {
                editPauseRequest = {
                    membershipId: membershipId,
                    end: pauseEnd
                }
            }
            this.logger.info("edit pause request - ", JSON.stringify(editPauseRequest), JSON.stringify(pauseEnd))
            const annotation = membership.activePause ? membership.activePause.reason : ""
            const checkins: GymfitCheckIn[] = await this.gymfitService.getGymfitCheckinByDate(userContext.userProfile.userId, pauseStart, pauseEnd)
            if (checkins) {
                checkins.forEach((checkin) => {
                    if (checkin.state === "CREATED" && checkin.startTime > TimeUtil.getCurrentEpoch()) {
                        const cancelCheckInParams: ICancelCheckInParams = {
                            agent: "system",
                            agentType: "system",
                            comment: "Cancel for pause membership"
                        }
                        this.gymfitService.cancelCheckIn(checkin.id, userContext.userProfile.userId, cancelCheckInParams )
                    }
                })
            }
            const endDate = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, pauseEnd + GymfitUtil.convertDaysToMillis(1))
            await this.cultFitService.cancelUpcomingBookingsForMembershipInDateRangeV2(membershipId, userId, TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, TimeUtil.parseDateFromEpoch(pauseStart)), endDate, process.env.APP_NAME)
            await this.membershipService.editPauseMembership(editPauseRequest, userId, annotation)
            return null
        }

        private async playEditPauseEndDate(membershipId: number, userId: string, pauseParams: PauseMembershipParams, userContext: UserContext): Promise<CultMembership> {
            const membership: Membership = await this.membershipService.getMembershipById(membershipId)
            if (membership.userId != userId) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 401).withDebugMessage("Operation not allowed").build()
            }

            const pauseStart = membership.activePause ? membership.activePause.start : null
            const pauseEnd = GymfitUtil.convertDaysToMillis(pauseParams.pauseDurationDays) + pauseStart - 1000
            let editPauseRequest: EditPauseRequest = {
                membershipId: membershipId,
                start: pauseStart,
                end: pauseEnd
            }
            if (membership.status === "PAUSED") {
                editPauseRequest = {
                    membershipId: membershipId,
                    end: pauseEnd
                }
            }
            const annotation = membership.activePause ? membership.activePause.reason : ""
            await this.membershipService.editPauseMembership(editPauseRequest, userId, annotation)
            return null
        }

        private async gymfitCancelPause (membershipId: number, userId: string): Promise<{ success: boolean }> {
            const membership = await this.membershipService.getMembershipById(membershipId)

            if (!membership) {
                throw new Error("Membership not found")
            } else if (membership.userId !== userId) {
                throw new Error("Membership does not belong to the user")
            }

            const cancelPauseRequest: CancelPauseRequest = {
                membershipId: membershipId,
                gapHandling: GapHandling.FILL
            }

            await this.membershipService.cancelPause(cancelPauseRequest, userId, "")
            return { success: true }
        }

        private async playCancelPause (membershipId: number, userId: string): Promise<{ success: boolean }> {
            const membership = await this.membershipService.getMembershipById(membershipId)
            if (membership?.userId != userId) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 401).withDebugMessage("Operation not allowed").build()
            }
            const cancelPauseRequest: CancelPauseRequest = {
                membershipId: membershipId,
                gapHandling: GapHandling.FILL
            }
            const membershipPause: Membership = await this.membershipService.cancelPause(cancelPauseRequest, userId, "")
            return { success: true }
        }

        private populatePromiseMapCache(userContext: UserContext, serviceInterfaces: CFServiceInterfaces) {
            if (!userContext.userProfile.promiseMapCache) {
                userContext.userProfile.promiseMapCache ??= new PromiseCache(serviceInterfaces)
            }
        }

    }

    return PackController
}

export default controllerFactory
