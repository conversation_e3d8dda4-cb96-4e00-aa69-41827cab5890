import { injectable } from "inversify"
import { Action, ExpandableListWidget, WidgetView, ProductListWidget } from "../common/views/WidgetView"
import { ProductType } from "@curefit/product-common"
import { UserContext } from "@curefit/userinfo-common"
import { ActionUtil as BaseActionUtil } from "@curefit/base-utils"
import AppUtil from "../util/AppUtil"
import { GymfitMembership } from "@curefit/gymfit-common"
import GymfitPackPageConfig from "../gymfit/GymfitPackPageConfig"
import { Membership } from "@curefit/membership-commons"

@injectable()
export class GymfitCancelMembershipPage {
    widgets: (WidgetView | ExpandableListWidget)[] = []
    constructor(userContext: UserContext, productType: ProductType, membership: Membership, gymfitPackPageConfig: GymfitPackPageConfig) {
        this.widgets.push(new ProductListWidget("SMALL", { "title": "Perks of being a cultpass PRO member" },
            gymfitPackPageConfig.whyPackItems
        ))
        const membershipId = membership.id
        const packId = parseInt(membership.productId.replace("GYMFIT", ""))
        const cancelActionMeta = {
            packId,
            membershipId,
            productType,
            isSubscription: false
        }
        const travelActions: Action[] = []
        travelActions.push(this.getCancelMembershipAction(cancelActionMeta, "Still Cancel", "TRAVEL"))
        this.widgets.push(this.getExpandableListWidget(userContext, cancelActionMeta))
    }

    getExpandableListWidget(userContext: UserContext, cancelActionMeta: any): WidgetView | ExpandableListWidget {
        const bookClassAction: Action = {
            actionType: "NAVIGATION",
            title: "Book a class",
            url: BaseActionUtil.getBookCultClassUrl("FITNESS", AppUtil.isNewClassBookingSuppoted(userContext), "cancelMembership")
        }
        const packBrowseAction: Action = {
            actionType: "NAVIGATION",
            title: "Browse Packs",
            url: BaseActionUtil.cultPackBrowse()
        }
        return {
            widgetType: "EXPANDABLE_LIST_WIDGET",
            title: "Tell us why you want to cancel",
            allowMultipleOpen: false,
            showDivider: true,
            widgets: [
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "I didn’t like cultpass PRO experience",
                    cancelPrompt: "We’re terribly sorry to hear that. We’re constantly innovating to improve our experience. We’d love to hear what we could’ve done better.",
                    actions: [this.getCancelMembershipAction(cancelActionMeta, "Cancel and Refund", "BAD_CLASSES")]
                },
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "I didn’t find the experience COVID-19 safe",
                    cancelPrompt: "We’re terribly sorry to hear that. We’re constantly innovating to improve our experience. We’d love to hear what we could’ve done better.",
                    actions: [this.getCancelMembershipAction(cancelActionMeta, "Cancel and Refund", "BAD_CLASSES")]
                },
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "I wanted unlimited access to cult classes",
                    cancelPrompt: "We are sorry that you got confused, but we are excited to see your commitment to fitness.",
                    actions: [this.getCancelMembershipAction(cancelActionMeta, "Cancel and Refund", "BAD_CLASSES")]
                },
                {
                    widgetType: "CANCEL_OPTION_WIDGET",
                    reason: "My reason is not listed",
                    cancelPrompt: "We are sorry to see you go! Tell us what went wrong",
                    actions: [this.getCancelMembershipAction(cancelActionMeta, "Cancel and Refund", "NOT_LISTED")]
                }
            ]
        }
    }
    private getCancelMembershipAction(actionMeta: any, actionTitle: string, reasonCode: string): Action {
        return {
            actionType: "CANCEL_CULT_SUBSCRIPTION",
            title: actionTitle,
            meta: {
                alertTitle: "Cancel Membership",
                alertMessage: `Are you sure you want to cancel your membership?`,
                ...actionMeta,
                reasonCode: reasonCode
            }

        }
    }
}
