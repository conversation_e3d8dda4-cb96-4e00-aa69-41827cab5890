import { inject, injectable } from "inversify"
import { UserContext } from "@curefit/userinfo-common"
import {
    Action,
    ActionCard,
    DatePickerWidget,
    DescriptionWidget,
    getOffersWidget,
    Header,
    InfoCard,
    InfoWidget,
    ManageOptionPayload,
    ManageOptions,
    ManageOptionsWidget,
    PackProgress,
    ProductDetailPage,
    ProductListWidget,
    ProductSummaryWidgetV2,
    WidgetView
} from "../common/views/WidgetView"
import { CATALOG_CLIENT_TYPES, CatalogueServiceV2Utilities, ICatalogueService, ICatalogueServicePMS } from "@curefit/catalog-client"
import {
    GymfitAccessLevel,
    GymfitCenter,
    GymFitCenterSearchFilters,
    GymfitListingCategoryRestriction,
    GymfitMembership,
    GymfitProductType,
    MediaType,
    UpgradePricingRequest
} from "@curefit/gymfit-common"
import * as _ from "lodash"
import {
    IOfferServiceV2,
    OFFER_SERVICE_CLIENT_TYPES,
    OfferServiceV3,
    OfferUtil as OfferServiceUtil,
    ProductTaxBreakup
} from "@curefit/offer-service-client"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import GymfitPackPageConfig from "../gymfit/GymfitPackPageConfig"
import AppUtil, { CITY_SPLIT_ENABLED_CITY_IDS, CREDIT_PILL_ICON, SUPPORT_DEEP_LINK } from "../util/AppUtil"
import { ActionUtil, ActionUtilV1, OfferUtil, SeoUrlParams } from "@curefit/base-utils"
import { TimeUtil, Timezone } from "@curefit/util-common"
import {
    BannerCarouselWidget,
    CardContainerCellWidget,
    HorizontalImageListWidget,
    HorizontalList,
    HorizontalListItem,
    ProductGuranteeWidget
} from "../page/PageWidgets"
import { GYMFIT_CLIENT_TYPES, IGymfitService } from "@curefit/gymfit-client"
import {
    CardContainerWidget,
    GymPackMembershipSummary,
    MediaAsset,
    PackInfoItem,
    PageTypes,
    ProductNote,
    ProductNoteWidget
} from "@curefit/apps-common"
import IssueBusiness from "../crm/IssueBusiness"
import { IssueProductState } from "@curefit/issue-common"
import GymfitUtil, { GYM_MEMBERSHIP_PRIMARY_BENEFITS, LUX_MEMBERSHIPS_PRIMARY_BENEFITS } from "../util/GymfitUtil"
import { BASE_TYPES, Logger } from "@curefit/base"
import { GymFitProductPricesResponse } from "@curefit/offer-common"
import CultUtil, {
    ELITE_MEMBERSHIP_PRIMARY_BENEFITS,
    PRO_NAS_PRICE_HIKE_BANNER_ID
} from "../util/CultUtil"
import { MoneyBackOfferDetail } from "../cult/CultBusiness"
import { IBreadCrumb } from "../page/ondemand/OnDemandCommon"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import { AttributeKeyType, Membership } from "@curefit/membership-commons"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { Tenant } from "@curefit/user-common"
import * as moment from "moment"
import { ActionUtil as AppActionUtil } from "../util/ActionUtil"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import WidgetBuilder from "../page/vm/WidgetBuilder"
import { ErrorFactory } from "@curefit/error-client"
import { Product } from "@curefit/product-common"
import { IOrderService, OMS_API_CLIENT_TYPES } from "@curefit/oms-api-client"
import { CenterResponse, CenterVertical } from "@curefit/center-service-common"
import { CENTER_SERVICE_CLIENT_TYPES, ICenterService } from "@curefit/center-service-client"
import CartViewBuilder from "../cart/CartViewBuilder"
import CultPackDetailViewBuilderV2 from "../cult/cultpackpage/CultPackDetailViewBuilderV2"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { UpgradeFeeBreakdownDetail } from "../cult/UpgradeMembershipViewBuilder"
import { CULT_CLIENT_TYPES } from "@curefit/cult-client/dist/src/CultClientTypes"
import { ICultServiceOld as ICultService } from "@curefit/cult-client"
import { MembershipItemUtil } from "../util/MembershipItemUtil"
import { ICFAnalytics } from "../cfAnalytics/CFAnalytics"
import { PackStartDateAnalyticsEvent } from "../cfAnalytics/PackStartDateAnalyticsEvent"
import { AnalyticsEventName } from "../cfAnalytics/AnalyticsEventName"
import { ISegmentService } from "@curefit/vm-models/dist/src/services/ISegmentService"
import { ErrorCodes } from "../error/ErrorCodes"
import {
    AugmentedOfflineFitnessPack,
    ExtraChargeType,
    OfflineFitnessPack
} from "@curefit/pack-management-service-common"
import { IOfflineFitnessPackService, PACK_CLIENT_TYPES } from "@curefit/pack-management-service-client"
import { PromiseCache } from "../util/VMUtil"
import FitnessUtil, { upgradeTypes } from "../util/FitnessUtil"
import CatalogueServiceUtilities from "../util/CatalogueServiceUtilities"
import { DATE_SELECTION_WINDOW } from "../common/Constants"
import { generateTaxAndFeeBreakup } from "../common/views/CultPackSummaryWidget"
import { ISegmentationCacheClient, SEGMENTATION_CLIENT_TYPES } from "@curefit/segmentation-service-client"
import { Benefit } from "@curefit/membership-commons/src/common"

const clone = require("clone")

interface IPackStartDateResponse {
    membershipEndStartDate: string
    startDate: string
    dateSelectionWindow: number
    productTitle: string
}

@injectable()
class GymfitPackDetailViewBuilder {

    constructor(@inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) protected catalogueService: ICatalogueService,
                @inject(CATALOG_CLIENT_TYPES.CatalogServiceReaderPMS) protected catalogueServicePMS: ICatalogueServicePMS,
                @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) protected offerServiceV2: IOfferServiceV2,
                @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) protected offerServiceV3: OfferServiceV3,
                @inject(GYMFIT_CLIENT_TYPES.GymfitService) private gymfitService: IGymfitService,
                @inject(CUREFIT_API_TYPES.GymfitPackPageConfig) public gymfitPackPageConfig: GymfitPackPageConfig,
                @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness,
                @inject(OMS_API_CLIENT_TYPES.OrderService) private omsApiClient: IOrderService,
                @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) public membershipService: IMembershipService,
                @inject(BASE_TYPES.ILogger) private logger: Logger,
                @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
                @inject(OFFER_SERVICE_CLIENT_TYPES.OfferUtil) protected offerUtil: OfferServiceUtil,
                @inject(CUREFIT_API_TYPES.ServiceInterfaces) protected serviceInterfaces: CFServiceInterfaces,
                @inject(CUREFIT_API_TYPES.WidgetBuilder) public widgetBuilder: WidgetBuilder,
                @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
                @inject(CENTER_SERVICE_CLIENT_TYPES.CenterService) private centerService: ICenterService,
                @inject(CUREFIT_API_TYPES.CartViewBuilder) private cartViewBuilder: CartViewBuilder,
                @inject(LOCATION_TYPES.CityService) protected cityService: ICityService,
                @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
                @inject(CUREFIT_API_TYPES.CultFitPackDetailViewBuilderV2) private cultFitPackDetailViewBuilderV2: CultPackDetailViewBuilderV2,
                @inject(CUREFIT_API_TYPES.CFAnalytics) private cfAnalytics: ICFAnalytics,
                @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService,
                @inject(PACK_CLIENT_TYPES.OfflineFitnessPackService) private offlineFitnessPackService: IOfflineFitnessPackService,
                @inject(SEGMENTATION_CLIENT_TYPES.SegmentationCacheClient) private segmentationCacheClient: ISegmentationCacheClient
    ) {
    }

    async getPrePurchaseView(userContext: UserContext, productId: string, selectedStartDate: string): Promise<any> {

        const isWeb = AppUtil.isWeb(userContext)

        this.logger.info("productId debug" + " : " + productId)

        const user = await userContext.userPromise
        userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
        const offerV3Promise = this.offerServiceV3.getGymFitProductPrices({
            productIds: [productId],
            userInfo: {
                userId: userContext.userProfile.userId,
                deviceId: userContext.sessionInfo.deviceId,
                phone: user?.phone,
                email: user?.email,
                workEmail: user?.workEmail
            },
            cityId: userContext.userProfile.cityId,
            source: AppUtil.callSourceFromContext(userContext)
        })
        const  isCenterLevelPricingSupportedWeb = await AppUtil.isCenterLevelPricingSupportedWeb(userContext, this.serviceInterfaces.segmentService)
        const isCenterLevelPricingV2SupportedWeb = await AppUtil.isCenterLevelPricingV2SupportedWeb(userContext, this.serviceInterfaces.segmentService)
        const userSegments =  await this.segmentationCacheClient.getUserSegments(userContext.userProfile.userId)
        const selectedPack: AugmentedOfflineFitnessPack = await this.catalogueServicePMS.getAugmentedPackById(productId, {
            includeExhaustiveBenefits: true,
            includeExtraCharges: true,

            context: {userSegmentIds: userSegments}
        })

        let canonicalUrl: string
        if (CatalogueServiceV2Utilities.isProdIdMigratingToPMS(productId) && !CatalogueServiceV2Utilities.isProdIdMigratedToPMS(productId)) {
            this.logger.error("CATALOG::DEPR Catalog productId being fetched", {productId, userId: user?.id})
            const pmsPack = await CatalogueServiceUtilities.getPackByReferenceId(this.offlineFitnessPackService, productId)
            if (pmsPack) canonicalUrl = CatalogueServiceUtilities.getPackWebAction(pmsPack)
            else this.logger.error("CATALOG::DEPR Catalog productId not found in PMS", {productId, userId: user?.id})
        }

        const accessLevelCenter = CatalogueServiceUtilities.getAccessLevel(selectedPack) === GymfitAccessLevel.CENTER
        this.logger.info("selected Pack debug accessLevelCenter" + " : " + JSON.stringify(accessLevelCenter))
        this.logger.info("selected Pack debug 0.3.1")

        const earliestStartDateCult = await this.membershipService.getEarliestStartDate(userContext?.userProfile?.userId, Tenant.CUREFIT_APP, ELITE_MEMBERSHIP_PRIMARY_BENEFITS, selectedPack.product.durationInDays * 86400)
        this.logger.info("selected Pack earliestStartDateCult" + " : " + JSON.stringify(earliestStartDateCult))

        const earliestStartDateGym = await this.membershipService.getEarliestStartDate(userContext?.userProfile?.userId, Tenant.CUREFIT_APP, GYM_MEMBERSHIP_PRIMARY_BENEFITS, selectedPack.product.durationInDays * 86400)
        this.logger.info("selected Pack debug earliestStartDateGym" + " : " + JSON.stringify(earliestStartDateGym))
        // const earliestStart = await this.membershipService.get

        const earliestStartDate = earliestStartDateGym.start > earliestStartDateCult.start ? earliestStartDateGym.start : moment(earliestStartDateCult.start).add(1, "days").startOf("day").valueOf()

        const previousMembershipEndDate = moment(earliestStartDate).format("YYYY-MM-DD")

        this.logger.info("startDateLog : GymfitPackDetailViewBuilder.getPrePurchaseView userId: " + userContext?.userProfile?.userId  + ", cultBenefits: " + JSON.stringify(ELITE_MEMBERSHIP_PRIMARY_BENEFITS) + ", gymBenefits: "
            + JSON.stringify(GYM_MEMBERSHIP_PRIMARY_BENEFITS) + ", durationInSeconds: " + selectedPack.product.durationInDays * 86400 + ", earliestStartDateCult.start: " + earliestStartDateCult?.start + ", earliestStartDateGym.start: " + earliestStartDateGym?.start + ", earliestStartDate: " +
            earliestStartDate + ", previousMembershipEndDate: " + previousMembershipEndDate + ", selectedStartDate: " + selectedStartDate)
        this.cfAnalytics.sendEventFromUserContext(<PackStartDateAnalyticsEvent> {
            analyticsEventName: AnalyticsEventName.PACK_START_DATE,
            from: "GymfitPackDetailViewBuilder.getPrePurchaseView",
            cultBenefit: JSON.stringify(ELITE_MEMBERSHIP_PRIMARY_BENEFITS),
            gymfitBenefit: JSON.stringify(GYM_MEMBERSHIP_PRIMARY_BENEFITS),
            packDuration: (selectedPack?.product.durationInDays ?? 0) * 86400,
            packName: selectedPack?.title,
            productId: selectedPack?.productId,
            cultStartDate: moment(earliestStartDateCult?.start).tz(userContext.userProfile.timezone).format("YYYY-MM-DD"),
            gymfitStartDate: moment(earliestStartDateGym?.start).tz(userContext.userProfile.timezone).format("YYYY-MM-DD"),
            finalEarliestStartDate: moment(earliestStartDate).tz(userContext.userProfile.timezone).format("YYYY-MM-DD"),
            previousMembEndDate: moment(earliestStartDate).tz(userContext.userProfile.timezone).format("YYYY-MM-DD"),
            selectedStartDate: moment(selectedStartDate).tz(userContext.userProfile.timezone).format("YYYY-MM-DD")
        }, userContext, false, true, true, false)
        const widgetPromises: Promise<WidgetView>[] = []


        widgetPromises.push(this.buildSummaryWidget(userContext, selectedPack, selectedStartDate, previousMembershipEndDate, productId))


        widgetPromises.push(this.buildPriceHikeBannerWidget(userContext))


        widgetPromises.push(this.buildOffersWidget(productId, offerV3Promise))

        if (isWeb) {
            widgetPromises.push(this.buildBenefitWidget(selectedPack))
        }

        widgetPromises.push(this.getPrePurchaseProductNoteWidget(userContext, selectedPack, offerV3Promise))


        // widgetPromises.push(addEliteWhatYouGet(cultProduct))
        // widgetPromises.push(CartViewBuilder.addGymFitWhatYouGet(selectedPack))
        // CartViewBuilder cartViewBuilder = new CartViewBuilder()
        // Todo Prathibha : isCenterLevelPricingV2SupportedWeb check to be removed once Hyd is addd in isCenterLevelPricingSupportedWeb segment
        if ((isCenterLevelPricingSupportedWeb || isCenterLevelPricingV2SupportedWeb) && AppUtil.isWeb(userContext) && accessLevelCenter) {

            widgetPromises.push(this.cartViewBuilder.addGymFitWhatYouGet(selectedPack, AppUtil.isWeb(userContext)))
            widgetPromises.push(this.cartViewBuilder.addProHowItWorks(isWeb))

        } else {
            widgetPromises.push(this.buildHowItWorksWidget(userContext))
        }

        const packPage = new ProductDetailPage()
        packPage.widgets = await Promise.all(widgetPromises)

        packPage.actions = await this.getPrePurchasePageActions(userContext, selectedStartDate, selectedPack, offerV3Promise, previousMembershipEndDate)

        if (isWeb && accessLevelCenter) {
            packPage.breadCrumbs = this.getSelectPackBreadCrumbs("cultpass Select Membership")
        } else {
            // packPage.breadCrumbs = this.getPackBreadCrumbs("cultpass PRO Membership")
            packPage.breadCrumbs = this.getPackBreadCrumbs(selectedPack.title)
        }

        if (canonicalUrl) {
            this.logger.info("CATALOG::DEPR Setting canonical URL", {canonicalUrl})
            _.set(packPage, "meta.canonicalUrl", canonicalUrl)
        }

        return packPage
    }

    async getPostPurchaseView(userContext: UserContext, membershipId: number): Promise<ProductDetailPage> {
        const membership = await this.membershipService.getMembershipById(membershipId)
        if (GymfitUtil.isLuxMembership(membership)) {
            return await this.getLuxPostPurchaseView(userContext, membership)
        }
        if (MembershipItemUtil.getProductTypeFromMembership(membership) === "ONEPASS_PRODUCT") {
            return this.getOnePassPostPurchaseView(userContext, membership)
        }
        const product: OfflineFitnessPack = await this.catalogueServicePMS.getProduct(membership.productId)
        const widgetPromises: Promise<WidgetView>[] = []
        // todo remove it later
        this.logger.info(`membership details ${JSON.stringify(membership)}`)
        const moneyBackOffer = await GymfitUtil.checkMoneyBackOfferOnMembership(membership, userContext, this.omsApiClient, this.gymfitPackPageConfig, this.logger)
        const isSelectMembership = MembershipItemUtil.isSelectMembership(membership)
        const membershipCredits = MembershipItemUtil.getMembershipAccessCredits(this.serviceInterfaces.rollbarService, membership)
        let selectCenterName: string
        let actionUrl
        let actionTitle
        if (isSelectMembership) {
            const centerIds: string[] = []
            membership.attributes.forEach((a) => {
                if (a.attrKey == AttributeKeyType.ACCESS_CENTER) {
                    centerIds.push(a.attrValue)
                }
            })
            let gym
            let cultCenter
            for (const centerId of centerIds) {
                    const center: CenterResponse = await this.centerService?.getCenterById(Number(centerId))
                    if (center.vertical == CenterVertical.CULT) {
                     cultCenter = center
                    } else {
                     gym = center
                    }
            }
            selectCenterName = cultCenter != null ? cultCenter.name : gym.name
            if (!_.isEmpty(gym)) {
                const centerId = gym.meta.gymfitCenterId
                actionUrl = "curefit://gymquickcheckin?centerId=" + centerId
                if (!_.isNil(membershipCredits) && !_.isNil(membershipCredits?.remainingCredits)) {
                    actionUrl += "&centerCredit" + membershipCredits?.remainingCredits
                }
                actionTitle = "Check in"
            } else if (!_.isEmpty(cultCenter)) {
                const centerId = cultCenter.meta.cultCenterId
                actionUrl = "curefit://classbookingv2?centerId=" + centerId
                actionTitle = "Book Now"
            }
        }
        if (GymfitUtil.isPauseSupported(userContext)) {
            widgetPromises.push(this.buildPostPurchaseSummaryWidgetV2(userContext, membership, product, moneyBackOffer, isSelectMembership, selectCenterName))
        } else {
            widgetPromises.push(this.buildPostPurchaseSummaryWidgetV1(userContext, membership, product, moneyBackOffer, isSelectMembership))
        }
        // widgetPromises.push(this.buildIncludedGymsWidget(userContext, membership.meta.restrictions))
        const isWeb = AppUtil.isWeb(userContext)
        if (isWeb) {
            const moneybackWidget = this.buildMoneyBackOfferDetailWidgetForWeb(userContext, moneyBackOffer)
            if (moneybackWidget) {
                widgetPromises.push(moneybackWidget)
            }
        } else {
            const postPurchaseProductNote = await this.getPostPurchaseProductNote(userContext, moneyBackOffer)
            if (postPurchaseProductNote && postPurchaseProductNote.length > 0) {
                widgetPromises.push(this.buildProductNoteWidget(postPurchaseProductNote))
            }
        }
        if (!_.isNil(membershipCredits)) {
            const wrapperWidgets: WidgetView[] = [this.creditCardCellWidget(membershipCredits?.remainingCredits, membership.id)]
            widgetPromises.push(this.cardContainerWidget(wrapperWidgets))
        }
        userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
        const  isCenterLevelPricingSupportedWeb = await AppUtil.isCenterLevelPricingSupportedWeb(userContext, this.serviceInterfaces.segmentService)
        const isCenterLevelPricingV2SupportedWeb = await AppUtil.isCenterLevelPricingV2SupportedWeb(userContext, this.serviceInterfaces.segmentService)
        const accessLevelCenter = CatalogueServiceUtilities.getAccessLevel(product) === GymfitAccessLevel.CENTER

        const cultBenefit = membership && membership.benefits.find(benefit => benefit.name == "CULT")
        widgetPromises.push(this.buildWhatYouGetWidget(userContext, cultBenefit))
        // Todo Prathibha : isCenterLevelPricingV2SupportedWeb check to be removed once Hyd is addd in isCenterLevelPricingSupportedWeb segment
        if ((isCenterLevelPricingSupportedWeb || isCenterLevelPricingV2SupportedWeb) && AppUtil.isWeb(userContext) && accessLevelCenter) {
            widgetPromises.push(this.cartViewBuilder.addGymFitWhatYouGet(product, AppUtil.isWeb(userContext)))
            widgetPromises.push(this.cartViewBuilder.addProHowItWorks(AppUtil.isWeb(userContext)))

        } else {
            widgetPromises.push(this.buildPostPurchaseHowItWorksWidget(userContext))

        }
        const packPage = new ProductDetailPage()
        packPage.widgets = await Promise.all(widgetPromises)
        packPage.actions = isSelectMembership ? this.getSelectPackActions(actionTitle, actionUrl) : this.getPostPurchaseAction()

        this.logger.info("isSelectMembership post purchase flow " + isSelectMembership)

        if (isWeb && isSelectMembership) {
            packPage.breadCrumbs = this.getSelectPackBreadCrumbs("cultpass Select Membership")
        } else {
            packPage.breadCrumbs = this.getPackBreadCrumbs("cultpass PRO Membership")

        }
        return packPage
    }

    private creditCardCellWidget(creditsLeft: number, membershipId: number): CardContainerCellWidget {
        return {
            widgetType: "CARD_CONTAINER_CELL_WIDGET",
            title: creditsLeft + " Credit" + ((creditsLeft === 1) ? "" : "s") + " left",
            subTitle: "View transaction history",
            prefixImage: CREDIT_PILL_ICON,
            action: {
                actionType: "NAVIGATION",
                url: "curefit://credit_history_page?membershipId=" + membershipId,
                analyticsData: {
                    creditsLeft,
                    membershipId
                },
                viaDeepLink: true,
                meta: {
                    viaDeeplink: true,
                }
            }
        }
    }

    private async cardContainerWidget(widgets: WidgetView[]): Promise<CardContainerWidget> {
        return {
            widgets: widgets,
            widgetType: "CARD_CONTAINER_WIDGET",
            dividerType: "DIVIDER30"
        }
    }

    private async getOnePassPostPurchaseView(userContext: UserContext, membership: Membership): Promise<ProductDetailPage> {
        const widgetPromises: Promise<WidgetView>[] = []
        const product = await this.catalogueService.getProduct(membership.productId)
        widgetPromises.push(this.buildOnePassPostPurchaseSummaryWidgetV2(userContext, membership, product))
        widgetPromises.push(this.buildOnePassPostPurchaseHowItWorksWidget(userContext))
        const packPage = new ProductDetailPage()
        packPage.widgets = await Promise.all(widgetPromises)
        packPage.actions = this.getOnePassPostPurchaseAction(userContext)
        return packPage
    }

    public async getGymfitStartDateResponse(membership: Membership, userContext: UserContext, tz: Timezone, membershipId: number, productType: string) {
        const orderDetails = await this.omsApiClient.getOrder(membership.orderId)
        let membershipInitialStartDate = (orderDetails && orderDetails.products.length > 0) ? orderDetails?.products[0]?.option?.startDate : null
        if (membershipInitialStartDate == null) {
            membershipInitialStartDate = moment(membership.start).format("YYYY-MM-DD")
        }

        let startDateResponse: IPackStartDateResponse
        if (productType === "LUX_FITNESS_PRODUCT") {
            startDateResponse = await this.calculateLuxPackStartDate(membership, userContext, tz, membershipInitialStartDate)
        } else if (productType === "GYMFIT_FITNESS_PRODUCT") {
            startDateResponse = await this.calculateGymfitPackStartDate(membership, userContext, tz, membershipInitialStartDate)
        } else {
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).build()
        }
        const {membershipEndStartDate, startDate, dateSelectionWindow, productTitle} = startDateResponse

        const subtitle = `You can start your membership earlier or later, but new start date cannot be beyond ${dateSelectionWindow} days from original start date.`
        const infoWidget: InfoWidget = {
            title: "Change start date",
            subTitle: subtitle,
            widgetType: "INFO_WIDGET",
            icon: null
        }

        const datePickerWidget: DatePickerWidget = {
            widgetType: "DATE_PICKER_WIDGET",
            startDate: startDate,
            canChangeStartDate: true,
            endDate: membershipEndStartDate,
            selectedDate: null
        }
        const action = {
            actionType: "SHOW_ALERT_MODAL",
            title: "Submit",
            meta: {
                title: "Change start date",
                packName: productTitle,
                actions: [
                    {
                        actionType: "CONFIRM_START_DATE_CHANGE",
                        title: "CONFIRM",
                        payload: {
                            membershipId,
                        }
                    }
                ]
            }
        }
        return {
            validation: {
                endDate: TimeUtil.addDays(tz, moment(membershipInitialStartDate).format("YYYY-MM-DD"), dateSelectionWindow),
                errorMessage: `New start date should be on or before ${TimeUtil.addDays(tz, membershipInitialStartDate, dateSelectionWindow)}`
            },
            widgets: [
                infoWidget,
                datePickerWidget
            ],
            actions: [
                action
            ]
        }
    }

    private async calculateGymfitPackStartDate(membership: Membership, userContext: UserContext, tz: Timezone, membershipInitialStartDate: string): Promise<IPackStartDateResponse> {
        const product: OfflineFitnessPack = await this.catalogueServicePMS.getProduct(membership.productId)
        const earliestStartDateCult = await this.membershipService.getEarliestStartDate(userContext?.userProfile?.userId, Tenant.CUREFIT_APP, ELITE_MEMBERSHIP_PRIMARY_BENEFITS, product.product.durationInDays * 86400)
        const earliestStartDateGym = await this.membershipService.getEarliestStartDate(userContext?.userProfile?.userId, Tenant.CUREFIT_APP, GYM_MEMBERSHIP_PRIMARY_BENEFITS, product.product.durationInDays * 86400)
        const earliestStartDate = earliestStartDateGym.start >= earliestStartDateCult.start
            ? moment(earliestStartDateGym.start).add(1, "days").startOf("day").valueOf()
            : moment(earliestStartDateCult.start).add(1, "days").startOf("day").valueOf()
        let startDate
        const endDate = TimeUtil.addDays(tz, moment(membership.end).format("YYYY-MM-DD"),  2)
        const previousMembershipEndDate = moment(earliestStartDate).format("YYYY-MM-DD")
        if (previousMembershipEndDate !== endDate) {
            startDate = previousMembershipEndDate
        } else {
            const currentTime = Date.now()
            const laterDate = membership.start + 30 * 86400
            let calculatedStartDate = moment(Date.now())
            const endingMemberships = await this.membershipService.getMembershipsForUser(userContext?.userProfile?.userId, Tenant.CUREFIT_APP, ["GYMFIT_GA", "CULT"], ["PURCHASED", "PAUSED"], currentTime, laterDate)
            endingMemberships.forEach(msp => {
                if (msp.id !== membership.id && moment(msp.end) >= calculatedStartDate) {
                    calculatedStartDate = moment(msp.end).add(1, "days").startOf("day")
                }
            })
            startDate = moment(calculatedStartDate).format("YYYY-MM-DD")
        }
        const dateSelectionWindow = DATE_SELECTION_WINDOW
        let membershipEndStartDate = TimeUtil.addDays(tz, moment(membershipInitialStartDate).format("YYYY-MM-DD"), dateSelectionWindow)
        if (startDate > membershipEndStartDate) {
            membershipEndStartDate = startDate
        }

        return {
            membershipEndStartDate,
            startDate,
            dateSelectionWindow,
            productTitle: product.title
        }
    }

    private async calculateLuxPackStartDate(membership: Membership, userContext: UserContext, tz: Timezone, membershipInitialStartDate: string): Promise<IPackStartDateResponse> {
        const luxPack: OfflineFitnessPack = await this.offlineFitnessPackService.getCachedPackById(membership.productId)
        const earliestMembershipStartDate = await this.membershipService.getEarliestStartDate(userContext?.userProfile?.userId, Tenant.CUREFIT_APP, LUX_MEMBERSHIPS_PRIMARY_BENEFITS, luxPack.product.durationInDays * 86400)
        const earliestStartDate = earliestMembershipStartDate?.start

        let startDate
        const endDate = TimeUtil.addDays(tz, moment(membership.end).format("YYYY-MM-DD"), 1)
        const previousMembershipEndDate = moment(earliestStartDate).format("YYYY-MM-DD")
        if (previousMembershipEndDate !== endDate) {
            startDate = previousMembershipEndDate
        } else {
            const currentTime = Date.now()
            const laterDate = membership.start + 30 * 86400
            let calculatedStartDate = moment(Date.now())
            const endingMemberships = await this.membershipService.getMembershipsForUser(userContext?.userProfile?.userId, Tenant.CUREFIT_APP, ["LUX"], ["PURCHASED", "PAUSED"], currentTime, laterDate)
            endingMemberships.forEach(msp => {
                if (msp.id !== membership.id && moment(msp.end) >= calculatedStartDate) {
                    calculatedStartDate = moment(msp.end).add(1, "days").startOf("day")
                }
            })
            startDate = moment(calculatedStartDate).format("YYYY-MM-DD")
        }
        const dateSelectionWindow = 30
        let membershipEndStartDate = TimeUtil.addDays(tz, moment(membershipInitialStartDate).format("YYYY-MM-DD"), dateSelectionWindow)
        if (startDate > membershipEndStartDate) {
            membershipEndStartDate = startDate
        }

        return {
            membershipEndStartDate,
            startDate,
            dateSelectionWindow,
            productTitle: luxPack.product.title
        }
    }

    private async getLuxPostPurchaseView(userContext: UserContext, membership: Membership): Promise<ProductDetailPage> {
        const luxPack: OfflineFitnessPack = await this.offlineFitnessPackService.getCachedPackById(membership.productId)
        const centerServiceId = membership.metadata["centerServiceCenterId"]
        const center = centerServiceId && await this.centerService.getCenterById(centerServiceId)
        const widgetPromises: Promise<WidgetView>[] = []
        widgetPromises.push(this.buildLuxPostPurchaseSummaryWidget(userContext, membership, luxPack, center?.name))
        widgetPromises.push(this.buildLuxPostPurchaseHowItWorksWidget(userContext))
        const packPage = new ProductDetailPage()
        packPage.widgets = await Promise.all(widgetPromises)
        packPage.actions = this.getPostPurchaseAction()
        const centerId = center?.meta?.gymfitCenterId
        if (centerId) {
            packPage.actions = this.getLuxPostPurchaseAction(centerId.toString())
        }
        return packPage
    }

    private async buildUpcomingPauseWidget(membership: Membership, productType: string, userContext: UserContext): Promise<ManageOptionsWidget> {
        const tz = userContext.userProfile.timezone
        if (GymfitUtil.isUpcomingPause(membership)) {
            const isOptionEnabled = AppUtil.isCultPauseCancelSupported(userContext)
            const pauseStartDate = membership.activePause ? TimeUtil.parseDateFromEpochWithTimezone(tz, membership.activePause.start) : undefined
            const pauseEndDate = membership.activePause ? membership.activePause.end ? TimeUtil.parseDateFromEpochWithTimezone(tz, membership.activePause.end - 1000) : undefined : undefined
            const pauseEndDateFormatted = TimeUtil.formatDateInTimeZone(tz, pauseEndDate)
            const pauseStartDateFormatted = TimeUtil.formatDateInTimeZone(tz, pauseStartDate)
            let pauseEndDateWithTime
            if (pauseEndDate) {
                pauseEndDateWithTime = TimeUtil.getDefaultMomentForDateString(pauseEndDateFormatted, tz).endOf("day").toDate()
            }
            const startDateText = `${TimeUtil.formatDateInTimeZone(tz, pauseStartDate, "DD MMM hh:mm a")}`
            const daysDiff = TimeUtil.diffInDaysReal(tz, pauseStartDateFormatted, pauseEndDateFormatted)
            const remainingDaysInCurrentDuration = daysDiff < 0 ? 0 : daysDiff
            const limit = TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.end, "yyyy-MM-dd")
            const pauseDaysUsedInThisCycle = GymfitUtil.convertMillisToDays(membership.activePause.end - membership.activePause.start)
            const pauseDaysUsedTillDate = GymfitUtil.convertMillisToDays(membership.maxPauseDuration - membership.remainingPauseDuration)
            const meta: any = {
                membershipId: membership.id,
                productType: productType,
                title: "Cancel Pause",
                pauseMaxDays: GymfitUtil.convertMillisToDays(membership.maxPauseDuration),
                remainingPauseDays: GymfitUtil.convertMillisToDays(membership.remainingPauseDuration),
                remainingDaysInCurrentDuration,
                pauseEndDate: pauseEndDate ? TimeUtil.formatDateInTimeZone(tz, pauseEndDate) : undefined,
                startDateParams: {
                    date: TimeUtil.todaysDate(tz),
                    limit,
                    canEdit: false,
                    pauseEndText: "Your pack is paused till"
                },
                action: {
                    primaryText: "YES",
                    secondaryText: "NO"
                },
                pauseInfoTitles: {
                    pauseUsed: "Pause days used",
                    membershipExtended: "Membership will be extended by",
                    membershipEndsOn: "Membership will now end on",
                    pauseLeft: "Pause days left"
                },
                pauseInfo: GymfitUtil.getPauseInfo(tz, pauseDaysUsedTillDate, pauseDaysUsedInThisCycle, limit, GymfitUtil.convertMillisToDays(membership.remainingPauseDuration), true),
                dateParam: {
                    date: TimeUtil.formatDateInTimeZone(tz, pauseStartDate, "YYYY-MM-DD hh:mm A"),
                    limit,
                    pauseEndDate: pauseEndDate ? TimeUtil.formatDateInTimeZone(tz, AppUtil.doesUserBelongsToNewPauseExperiment(userContext, this.serviceInterfaces) ? pauseEndDate : pauseEndDateWithTime, "DD MMM hh:mm a") : undefined,
                    pauseEndText: `Pause starting ${startDateText} till`
                },
                subTitle: `You are cancelling pause starting tonight till ${TimeUtil.formatDateInTimeZone(tz, pauseEndDateWithTime, "DD MMM hh:mm a")}. Your pause days remain intact`,
                refreshPageOnCompletion: true,
                viaDeepLink: true,
                showEmotionMeter: false,
                onCompletionAction: {
                    actionType: "POP_AND_TRIGGER_ACTION",
                    meta: {
                      nextAction: {
                        actionType: "NAVIGATION",
                        url: await CatalogueServiceUtilities.getCultMembershipDetailsPageAction(membership.id?.toString(), userContext),
                      },
                    },
                  },
                additionalInfo: "All existing booking will get cancelled",
                performanceTitle: "On break for",
                performanceSubtitle: [
                    "Rest well, come back stronger",
                    "Rest well, come back stronger",
                    "Most athletes experience a 5% decrease in cardio-vascular fitness and strength after more than 2 weeks of inactivity",
                    "Most athletes experience a 10% decrease in cardio-vascular fitness and strength after more than 3 weeks of inactivity",
                    "Most athletes experience a 20% decrease in cardio-vascular fitness and strength after more than 4 weeks of inactivity",
                ],
                startDateTitle: "Select start date for your pause",
                endDateTitle: "Select end date for your pause",
                bannerUrl: "image/pause/pause_banner_2.png",
                pauseConfirmationData: {
                    confirmationLottieUrl: "/image/mem-exp/lottie/Confirmation.json",
                    daysLeftText: "PAUSE DAYS LEFT",
                    pauseDaysText: "Membership paused for",
                    endDateText: "Pack now ends on ",
                    suggestionTitle: "Pick a way to stay active",
                    homeWorkoutText: "Maintain your weekly streak with at home workout",
                    homeWorkoutImageUrl: "/image/pause/yoga_live.png",
                suggestionList: [
                        {
                            "iconUrl": "image/pause/Fitness.png",
                            "title": "Running or Cycling",
                            "isSelected": true,
                        },
                        {
                            "iconUrl": "image/pause/Shoes.png",
                            "title": "10K Steps Daily",
                            "isSelected": false,
                        },
                        {
                            "iconUrl": "image/pause/Sauna.png",
                            "title": "Not active",
                            "isSelected": false,
                        },
                    ]
                }
            }
            const manageOptions: ManageOptions = {
                displayText: AppUtil.isCultPauseCancelSupported(userContext) ? "Cancel Pause" : "Pause Scheduled",
                icon: "PAUSE",
                options: [{
                    isEnabled: isOptionEnabled,
                    type: "CANCEL_CULT_PACK_PAUSE",
                    displayText: AppUtil.isCultPauseCancelSupported(userContext) ? "Cancel Pause" : "Pause Scheduled",
                    meta
                }],
                info: {
                    title: "About Pause",
                    subTitle: "You can pause your pack when you can't use it like during travel or illness.\n\nOnce you resume, your pack is extended by the number of days you were on pause"
                }
            }
            manageOptions.subTitle = pauseStartDate && pauseEndDateWithTime ? `Pause starting ${startDateText} till ${TimeUtil.formatDateInTimeZone(tz, pauseEndDateWithTime, "DD MMM hh:mm a")}` : ""
            const pauseWidget = new ManageOptionsWidget(manageOptions, meta)
            pauseWidget.isDisabled = !isOptionEnabled
            return pauseWidget
        }
    }

    private async getPostPurchaseProductNote(userContext: UserContext, offerDetail: MoneyBackOfferDetail): Promise<ProductNote[] | undefined> {
        if (!_.isEmpty(offerDetail) && !_.isEmpty(offerDetail.cancellationWindow)) {
            const tz = userContext.userProfile.timezone
            const cancellationEndDate = TimeUtil.formatDateStringInTimeZone(offerDetail.cancellationWindow.endDate, tz, "DD MMM YYYY")
            if (offerDetail.isOfferApplied) {
                return [{
                    title: {
                        plainText: "Cancellation allowed till: ",
                        boldText: cancellationEndDate
                    },
                    icon: "/image/icons/cult/cancel_refund.png",
                }]
            }
        }
    }

    private async buildMoneyBackOfferDetailWidgetForWeb(userContext: UserContext, offerDetail: MoneyBackOfferDetail): Promise<ProductListWidget> {
        if (!_.isEmpty(offerDetail) && !_.isEmpty(offerDetail.cancellationWindow)) {
            const tz = userContext.userProfile.timezone
            const cancellationEndDate = TimeUtil.formatDateStringInTimeZone(offerDetail.cancellationWindow.endDate, tz, "DD MMM YYYY")
            if (offerDetail.isOfferApplied) {
                const header: Header = {
                    title: "Cancel Membership"
                }
                const actionCards: ActionCard[] = []
                actionCards.push({
                    subTitle: `Cancellation allowed till:      ${cancellationEndDate}`,
                    icon: "/image/icons/cult/cancel_refund.png",
                    action: ActionUtil.infoPage("noshowpolicy")
                })

                return new ProductListWidget("SMALL", header, actionCards)
            }
        }
    }
    async buildProductNoteWidget(data: ProductNote[]): Promise<ProductNoteWidget> {
        return {
            widgetType: "PRODUCT_NOTE_WIDGET",
            data: data,
            header: {
                title: "Cancel Membership"
            }
        }
    }

    private getManageOptions(membershipId: number) {
        const manageOptions: ManageOptions = {
            displayText: "Manage",
            options: []
        }
        const reportIssueOption: ManageOptionPayload = {
            isEnabled: true,
            displayText: "Need Help",
            type: "REPORT_ISSUE",
            // meta: this.getCultPackIssueList(issuesMap),
            action: SUPPORT_DEEP_LINK,
        }
        manageOptions.options.push(reportIssueOption)
        return manageOptions
    }

    private async buildSummaryWidget(userContext: UserContext, pack: AugmentedOfflineFitnessPack, selectedStartDate: string, previousMembershipEndDate: string, productId: string): Promise<ProductSummaryWidgetV2> {
        const user = await userContext.userPromise
        const offersV3 = await this.offerServiceV3.getGymFitProductPrices({
            productIds: [pack.productId],
            userInfo: {
                userId: userContext.userProfile.userId,
                deviceId: userContext.sessionInfo.deviceId,
                phone: user?.phone,
                email: user?.email,
                workEmail: user?.workEmail
            },
            cityId: userContext.userProfile.cityId,
            source: AppUtil.callSourceFromContext(userContext),
        })
        const taxBreakup: ProductTaxBreakup = this.offerUtil.getTaxBreakUpForCultAndGymPacks(offersV3)
        const imageAssets: MediaAsset[] = []
        const appCheck = userContext.sessionInfo.appVersion <= 8.63
        pack.product.images?.map(image => {
            imageAssets.push({
                assetType: "IMAGE",
                assetUrl: image.imageURL
            })
        })
        const productPoints: any[] = CatalogueServiceUtilities.getGymfitProductPoints(pack, userContext.sessionInfo?.osName)

        let style = {}
        if (appCheck) {
            style = {
                dateContainerStyle: {
                    marginTop: -15,
                    paddingLeft: 15,
                    borderBottomLeftRadius: 0,
                    borderBottomRightRadius: 0,
                }
            }
        } else {
            style = {
                dateContainerStyle: {
                    backgroundColor: "#ffffff",
                    borderRadius: 7,
                    elevation: 5,
                    shadowColor: "#000000",
                    shadowOffset: {
                        height: 2,
                        width: 0
                    },
                    shadowOpacity: 0.25,
                    shadowRadius: 3.84,
                    zIndex: 1,
                    marginHorizontal: 15,
                    marginBottom: 5,
                },
                infoCardContainer: {
                    marginTop: -25,
                    shadowRadius: 5,
                    borderRadius: 10,
                },
                priceContainer: {
                    flex: 1,
                    flexDirection: "column-reverse",
                    alignItems: "flex-end",
                    alignSelf: "flex-end",
                    justifyContent: "flex-start",
                },
                originalMrp: {
                    color: "#888e9e",
                    fontSize: 13,
                    marginHorizontal: 0,
                },
                iconContainer: {
                    width: "97%",
                    flex: 1
                },
                productIcon: {
                    marginRight: 0,
                },
                productText: {
                    flexWrap: "wrap",
                    paddingHorizontal: 10,
                    width: "100%",
                    flexShrink: 1
                }
            }
        }
        const isPartOfGstSegment = await AppUtil.doesUserBelongToGSTsplitExperiment(userContext, this.serviceInterfaces)
        const price = CultUtil.getOfferDetailsPMS(pack, offersV3).price
        const priceBreakupMap = taxBreakup[productId]
        let priceBreakup = null
        const extraChargesPlatformFee = pack?.augments?.extraCharges?.filter(charge => charge.extraChargeType === ExtraChargeType.PLATFORM_FEE)?.[0]?.extraChargeFee
        const isPlatformFeeApplicable = extraChargesPlatformFee && Object.keys(extraChargesPlatformFee).length > 0
        if (isPartOfGstSegment) {
            priceBreakup = {
                basePrice: priceBreakupMap.basePrice,
                tax: isPlatformFeeApplicable ? (priceBreakupMap?.taxAmount + extraChargesPlatformFee?.totalFee) : priceBreakupMap?.taxAmount
            }
        }
        const isWeb = AppUtil.isWeb(userContext)
        const accessLevelCenter = CatalogueServiceUtilities.getAccessLevel(pack) === GymfitAccessLevel.CENTER
        userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
        this.logger.info("buildSummaryWidget accessLevelCenter debug " + accessLevelCenter)
        const  isCenterLevelPricingSupportedWeb = await AppUtil.isCenterLevelPricingSupportedWeb(userContext, this.serviceInterfaces.segmentService)
        const isCenterLevelPricingV2SupportedWeb = await AppUtil.isCenterLevelPricingV2SupportedWeb(userContext, this.serviceInterfaces.segmentService)
        this.logger.info("buildSummaryWidget accessLevelCenter isCenterLevelPricingSupportedWeb " + isCenterLevelPricingSupportedWeb)
        this.logger.info("buildSummaryWidget accessLevelCenter isCenterLevelPricingV2SupportedWeb " + isCenterLevelPricingV2SupportedWeb)


        // Math.floor(pack.durationInDays / 30) + " months"
        const shouldAddFeeInListing = isWeb && !(isPartOfGstSegment && priceBreakupMap)
        const summaryWidget: ProductSummaryWidgetV2 = {
            widgetType: "PRODUCT_SUMMARY_WIDGET_V2",
            // Todo Prathibha : isCenterLevelPricingV2SupportedWeb check to be removed once Hyd is addd in isCenterLevelPricingSupportedWeb segment
            title: isWeb && (isCenterLevelPricingSupportedWeb || isCenterLevelPricingV2SupportedWeb) && accessLevelCenter ? Math.floor(pack.product.durationInDays / 30) + " months " + pack.clientMetadata.centerName :  pack.title,
            assets: imageAssets,
            productId: pack.productId,
            productType: "GYMFIT_FITNESS_PRODUCT",
            help: undefined,
            productPoints,
            style,
            price: {
                mrp: price.mrp,
                listingPrice: shouldAddFeeInListing ? price.listingPrice + extraChargesPlatformFee?.totalFee : price.listingPrice,
                showPriceCut: price.listingPrice < price.mrp,
                currency: price.currency
            },
            priceBreakup: priceBreakup,
            ...(isPlatformFeeApplicable ? {taxAndFeeItemList: generateTaxAndFeeBreakup({gstObject: {
                    amount: priceBreakupMap?.taxAmount,
                    rate: priceBreakupMap?.taxPercent
                }, extraChargeFee: pack?.augments?.extraCharges?.filter(charge => charge.extraChargeType === ExtraChargeType.PLATFORM_FEE)?.[0]?.extraChargeFee})} : {}),
            devPackInfo: pack // TODO: remove before PR merge
        }
        if (userContext.sessionInfo.isUserLoggedIn) {
            if (appCheck) {
                summaryWidget.dateMeta = this.buildStartDateWidget(userContext, pack, selectedStartDate, previousMembershipEndDate)
            } else {
                summaryWidget.outerDateMeta = this.buildStartDateWidget(userContext, pack, selectedStartDate, previousMembershipEndDate)
            }
        }
        return summaryWidget
    }

    private async buildPriceHikeBannerWidget(userContext: UserContext): Promise<any> {
        const widgetId = PRO_NAS_PRICE_HIKE_BANNER_ID
        const widgetResponse = await this.widgetBuilder.buildWidgets([widgetId], this.serviceInterfaces, userContext, undefined, undefined)
        if (!_.isEmpty(widgetResponse) && !_.isEmpty(widgetResponse.widgets)) {
            const widget: any = widgetResponse.widgets[0] && widgetResponse.widgets[0].widgetType === "BANNER_CAROUSEL_WIDGET" ? widgetResponse.widgets[0] : undefined
            // updating layoutprops in same object was causing issue in same widget displaying in other place
            const bannerWidget = clone(widget)
            bannerWidget.layoutProps.backgroundColor = null
            bannerWidget.hasDividerBelow = false
            bannerWidget.layoutProps.v2 = true
            bannerWidget.dividerType = "NONE"
            bannerWidget.layoutProps.useShadow = true
            return bannerWidget
        }
    }


    private async buildOnePassPostPurchaseSummaryWidgetV2(userContext: UserContext, membership: Membership, product: Product): Promise<GymPackMembershipSummary> {
        const imageAsset: MediaAsset = {
            assetType: "IMAGE",
            assetUrl: product.imageUrls?.length ? product.imageUrls[0] : null
        }
        const membershipStateMeta = GymfitUtil.isPauseEnabled() ? GymfitUtil.getMembershipStateMeta(membership, userContext, this.logger) : null
        const summaryWidget: GymPackMembershipSummary = {
            widgetType: "GYM_PACK_MEMBERSHIP_SUMMARY",
            title: "fitternity ONEPASS",
            imageAsset,
            moreActions: null,
            status: this.getPackStatus(userContext.userProfile.timezone, membership),
            packInfoItems: this.getPackInfoItems(userContext, membership),
            actions: MembershipItemUtil.isCurrentOrFutureActiveMembership(membership) ? await this.getMembershipActions(userContext, membership, product, true) : [],
            rightText: membershipStateMeta ? membershipStateMeta.title : null,
            noteInfo: null
        }
        return summaryWidget
    }
    private async buildPostPurchaseSummaryWidgetV2(userContext: UserContext, membership: Membership, product: OfflineFitnessPack, moneyBackOffer: MoneyBackOfferDetail, isSelectMembership?: boolean, selectCenterName?: string): Promise<GymPackMembershipSummary> {
        const imageAsset: MediaAsset = {
            assetType: "IMAGE",
            assetUrl: product.product.images?.length ? product.product.images[0].imageURL : null
        }
        const membershipStateMeta = GymfitUtil.isPauseEnabled() ? GymfitUtil.getMembershipStateMeta(membership, userContext, this.logger) : null
        let title = isSelectMembership ? `cultpass SELECT ${selectCenterName}` : "cultpass PRO"
        if (MembershipItemUtil.isPlusMembership(membership) && !title.toUpperCase().includes("PLUS")) {
            title = title + " PLUS"
        } else if (MembershipItemUtil.isBoosterMembership(membership) && !title.toUpperCase().includes("BOOSTER")) {
            title = title + " + Booster"
        }
        const summaryWidget: GymPackMembershipSummary = {
            widgetType: "GYM_PACK_MEMBERSHIP_SUMMARY",
            title: title,
            imageAsset,
            moreActions: await this.buildMoreActions(userContext, membership, moneyBackOffer),
            status: this.getPackStatus(userContext.userProfile.timezone, membership),
            packInfoItems: this.getPackInfoItems(userContext, membership),
            actions: MembershipItemUtil.isCurrentOrFutureActiveMembership(membership) ? await this.getMembershipActions(userContext, membership, product, null, isSelectMembership) : [],
            rightText: membershipStateMeta ? membershipStateMeta.title : null,
            noteInfo: null
        }
        return summaryWidget
    }

    private async buildPostPurchaseSummaryWidgetV1(userContext: UserContext, membership: Membership, product: OfflineFitnessPack, moneyBackOffer: MoneyBackOfferDetail, isSelectMembership: boolean): Promise<GymPackMembershipSummary> {
        const imageAsset: MediaAsset = {
            assetType: "IMAGE",
            assetUrl: product.product.images.length ? product.product.images[0].imageURL : null
        }

        let title = "cultpass PRO"
        const isWeb = AppUtil.isWeb(userContext)
        if (isSelectMembership && isWeb) {
            title = "cultpass SELECT"
        }

        const summaryWidget: GymPackMembershipSummary = {
            widgetType: "GYM_PACK_MEMBERSHIP_SUMMARY",
            title: title,
            imageAsset,
            moreActions: await this.buildMoreActions(userContext, membership, moneyBackOffer),
            status: this.getPackStatus(userContext.userProfile.timezone, membership),
            packInfoItems: this.getPackInfoItems(userContext, membership),
            noteInfo: null
        }
        return summaryWidget
    }

    // HERE
    private async buildLuxPostPurchaseSummaryWidget(userContext: UserContext, membership: Membership, pack: OfflineFitnessPack, centerName: string): Promise<GymPackMembershipSummary> {
        const imageAsset: MediaAsset = {
            assetType: "IMAGE",
            assetUrl: !_.isEmpty(pack.product?.images) ? pack.product.images[0].imageURL : GymfitUtil.getDefaultLuxMagazineImage()
        }
        const actions: Action[] = []
        if (MembershipItemUtil.isCurrentOrFutureActiveMembership(membership)) {
            actions.push(...GymfitUtil.getChangeStartDateAction(membership, pack.productType))
            if (AppUtil.isAppLuxPauseSupported(userContext)) {
                const pausePackData = await GymfitUtil.getPackPauseResumeDetails(membership, "LUX_FITNESS_PRODUCT", userContext, true)
                if (pausePackData && pausePackData.isPauseAllowed) {
                    const packPauseResumeAction = GymfitUtil.getPackPauseResumeAction(pausePackData, membership, true)
                    if (packPauseResumeAction) {
                        actions.push(packPauseResumeAction)
                    }
                } else if (pausePackData && pausePackData.isPauseAllowed === false && membership.maxPauseDuration !== 0) {
                    actions.push(this.getPausePackDisableAction(pausePackData))
                }
            }
        }
        const summaryWidget: GymPackMembershipSummary = {
            widgetType: "GYM_PACK_MEMBERSHIP_SUMMARY",
            title: centerName ?? pack.clientMetadata?.centerName,
            imageAsset,
            moreActions: null,
            status: this.getPackStatus(userContext.userProfile.timezone, membership),
            packInfoItems: this.getPackInfoItems(userContext, membership),
            noteInfo: null,
            actions: actions,
            rightText: GymfitUtil.getMembershipStateMeta(membership, userContext, this.logger)?.title ?? null
        }
        return summaryWidget
    }

    private async buildOnePassPostPurchaseSummaryWidget(userContext: UserContext, membership: Membership, product: Product): Promise<GymPackMembershipSummary> {
        const imageAsset: MediaAsset = {
            assetType: "IMAGE",
            assetUrl: product.imageUrl ?? GymfitUtil.getDefaultMagazineImage()
        }
        const summaryWidget: GymPackMembershipSummary = {
            widgetType: "GYM_PACK_MEMBERSHIP_SUMMARY",
            title: "Onepass",
            imageAsset,
            moreActions: null,
            status: this.getPackStatus(userContext.userProfile.timezone, membership),
            packInfoItems: this.getPackInfoItems(userContext, membership),
            noteInfo: null
        }
        return summaryWidget
    }

    private getPackInfoItems(userContext: UserContext, membership: Membership): PackInfoItem[] {
        const packInfoItems: PackInfoItem[] = []
        const tz = userContext.userProfile.timezone
        const endDate = TimeUtil.getMomentForDate(new Date(membership.end), tz)
        const endDateFormatted = endDate.format("D MMM YYYY")
        const startDate = TimeUtil.getMomentForDate(new Date(membership.start), tz)
        const startDateFormatted = startDate.format("D MMM YYYY")
        const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDate(tz), tz)
        const numDaysToEndFromToday = endDate.diff(today, "days")
        packInfoItems.push({
            title: "Duration",
            subTitle: `${startDateFormatted} - ${endDateFormatted}`,
            total: endDate.diff(startDate, "days"),
            completed: numDaysToEndFromToday > 0 ? today.diff(startDate, "days") : endDate.diff(startDate, "days"),
            progressColor: "#000000",
            type: "LINE",
            leftText: startDate > today ? `Starts: ${startDateFormatted}` : `Started: ${startDateFormatted}`,
            rightText: `Ends: ${endDateFormatted} ${(membership.remainingPauseCount === membership.maxPauseCount && _.isEmpty(membership.activePause)) ? "" : "(updated)"}`
        })
        const isLuxMembership = GymfitUtil.isLuxMembership(membership)
        const cultBenefit = GymfitUtil.getCultBenefit(membership)
        if (!isLuxMembership && cultBenefit) {
            const { maxTickets, ticketsUsed } = cultBenefit
            const remainingCount = maxTickets - ticketsUsed
            packInfoItems.push({
                title: GymfitUtil.isPremiumGymSupported(userContext) ? "Sessions at ELITE gyms or cult centers" : "Remaining cult classes",
                subTitle: `${remainingCount}/${maxTickets}`,
                total: maxTickets,
                completed: ticketsUsed,
                progressColor: "#000000",
                type: "LINE",
                leftText: `Sessions at ELITE gyms or cult centers: ${remainingCount}/${maxTickets}`
            })
        }
        // const limitedClassRestriction = membership.restrictions.find(restriction => restriction.restrictionType === RestrictionType.LIMITED || restriction.restrictionType === RestrictionType.MONTHLY)
        // if (limitedClassRestriction && limitedClassRestriction.maxCount > 0) {
        //     const totalMaxCount = limitedClassRestriction.maxCount
        //     packInfoItems.push({
        //         title: "Remaining cult classes",
        //         subTitle: `${totalMaxCount - limitedClassRestriction.usedCount}/${totalMaxCount}`,
        //         total: totalMaxCount,
        //         completed: limitedClassRestriction.usedCount,
        //         progressColor: "#008300",
        //         type: "LINE"
        //     })
        // }
        return packInfoItems
    }

    private getPackStatus(tz: Timezone, membership: Membership) {
        switch (membership.status) {
            case "PURCHASED":
                const today = TimeUtil.getMomentNow(tz).toDate().getTime()
                const startDate = TimeUtil.parseDateFromEpochWithTimezone(tz, membership.start).getTime()
                const endDate = TimeUtil.parseDateFromEpochWithTimezone(tz, membership.end).getTime()
                if (startDate > today) {
                    return {
                        color: "#d1d1d1",
                        text: "UPCOMING"
                    }
                }
                if (endDate < today) {
                    return {
                        color: "#e05343",
                        text: "EXPIRED"
                    }
                }
                return {
                    color: "#5bdbb6",
                    text: "ACTIVE"
                }
            case "PAUSED":
                return {
                    color: "#f5a623",
                    text: "PAUSED"
                }
            case "SUSPENDED":
            case "CANCELLED":
                return {
                    color: "#e05343",
                    text: "CANCELLED"
                }
        }
    }

    private async getMembershipActions(userContext: UserContext, membership: Membership, product: OfflineFitnessPack | Product, isOnepassProduct?: boolean, isSelectMembership?: boolean) {
        isSelectMembership = isSelectMembership || false
        let actions: Action[] = []
        const isCurrentMembership = GymfitUtil.isCurrentGoldMembership(membership)
        let showPausePackDisableState = false
        let packPauseResumeAction
        if (GymfitUtil.isPauseEnabled() && isCurrentMembership) {
            const isNewPauseFlowEnabled = await AppUtil.doesUserBelongsToNewPauseExperiment(userContext, this.serviceInterfaces)
            const pausePackData = await GymfitUtil.getPackPauseResumeDetails(membership, product.productType, userContext, isNewPauseFlowEnabled)
            if (pausePackData && pausePackData.isPauseAllowed) {
                packPauseResumeAction = GymfitUtil.getPackPauseResumeAction(pausePackData, membership, isNewPauseFlowEnabled)
                if (packPauseResumeAction) {
                    actions.push(packPauseResumeAction)
                }
            } else if (pausePackData && pausePackData.isPauseAllowed === false && membership.maxPauseDuration !== 0) {
                showPausePackDisableState = true
            }

            const cancelPauseAction = await this.getCancelPauseAction(membership, product.productType, userContext)
            if (cancelPauseAction) {
                actions.push(cancelPauseAction)
            }

            if (showPausePackDisableState) {
                actions.push(this.getPausePackDisableAction(pausePackData))
            }

            if (isSelectMembership) {
                if (GymfitUtil.isPackUpgradeAble(membership)) {
                    try {
                        actions.push(await this.getProSelectPackUpgradeAction(userContext, membership, product.productId, cancelPauseAction, packPauseResumeAction))
                    } catch (e) {
                        this.logger.error(`select pack upgrade action fail : ` + e)
                    }
                }
            } else if (!MembershipItemUtil.isPlusMembership(membership) && AppUtil.isGymPackUpgradeSupported(userContext) && !isOnepassProduct) {
                // removing older upgrade action completely, older version app will get the prompt upgrade the app to use this feature
                actions.push(FitnessUtil.getUpgradeAction(upgradeTypes.PRO_TO_ELITE, membership.id.toString(), product.productId))
            }
        }

        // Moved the disabled action to the last
        if (GymfitUtil.isPauseEnabled() && membership.maxPauseDuration !== 0 && isCurrentMembership && !isOnepassProduct) {
            actions.push(this.getStartDateDisableAction(membership))
        }

        if (isSelectMembership && isCurrentMembership && !MembershipItemUtil.isPlusMembership(membership)) {
            const status =  await this.gymfitService.getTransferValidationStatus(membership.id, userContext.userProfile.userId)
            if (status.result) {
                actions.push(FitnessUtil.getTransferMembershipPageAction(membership.id.toString()))
            } else {
                actions.push(this.getDisableTransferMembershipAction(status.message))
            }
        }

        if (!isCurrentMembership) {
            actions = actions.concat(GymfitUtil.getChangeStartDateAction(membership, product.productType))
        }
        return actions
    }

    private async getCancelPauseAction(membership: Membership, productType: string, userContext: UserContext): Promise<Action | null> {
        const cancelPauseData = await this.buildUpcomingPauseWidget(membership, productType, userContext)
        if (cancelPauseData) {
            return {
                iconUrl: "/image/icons/cult/resume.png",
                title: "CANCEL PAUSE",
                actionType: cancelPauseData.manageOptions.options[0].type,
                meta: cancelPauseData.meta
            }
        }
        return null
    }

    private getStartDateDisableAction(membership: Membership): Action {
        return {
            iconUrl: "/image/icons/cult/calendar_black1.png",
            title: "START DATE",
            actionType: "SHOW_ALERT_MODAL",
            isDisabled: true,
            meta: {
                title: "Start date cannot be changed",
                subTitle: "Membership start date cannot be changed as membership already started",
                actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
            }
        }
    }

    private getPausePackDisableAction(pausePackData: any): Action {
        return {
            iconUrl: "/image/icons/cult/pause_black.png",
            title: "PAUSE",
            actionType: "SHOW_ALERT_MODAL",
            isDisabled: true,
            meta: {
                title: "Pack can not be paused",
                subTitle: pausePackData.manageOptions.subTitle,
                actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
            }
        }

    }

    private async getProSelectPackUpgradeAction(userContext: UserContext, membership: Membership, productId: string, cancelPauseAction?: Action, packPauseResumeAction?: Action): Promise<Action> {
        const pauseAlert = CultUtil.getAlertWhileUpgradingDuringPause(membership, "Upgrade to Cultpass PRO/ELITE", cancelPauseAction, packPauseResumeAction)
       if (pauseAlert) {
         return pauseAlert
        }
        const eliteUpgradeFeeBreakdown: UpgradeFeeBreakdownDetail = (await this.cultFitService.getPackUpgradeFees(
            userContext.userProfile.userId,
            String(membership.id),
            "CUREFIT_APP",
            true
        )) as UpgradeFeeBreakdownDetail
        const upgradeProduct = await this.catalogueService.getProUpgradePack(membership.metadata.cityId)
        const upgradePricePayload: UpgradePricingRequest = {
            membershipServiceId: membership.id,
            targetAccessLevel: upgradeProduct.accessLevel,
            targetAccessLevelId: upgradeProduct.externalAccessLevelId,
            targetProductType: upgradeProduct.productType as GymfitProductType
        }
        const proUpgradeFeeBreakdown = await this.gymfitService.getUpgradePrices(upgradePricePayload)

        return {
            actionType: "SHOW_SELECT_UPGRADE_MODAL" as any,
            title: "Upgrade to Cultpass PRO/ELITE",
            iconUrl: "/image/icons/cult/upgrade.png",
            meta: {
                "title": "Choose your membership",
                packs: [
                    {
                      "title": "Cultpass PRO",
                      "subtitle": "Unlimited access to all pro gyms",
                      "price": `₹${proUpgradeFeeBreakdown.totalAmount}`,
                      "action": FitnessUtil.getUpgradeAction(upgradeTypes.PRO_SELECT_TO_PRO, membership.id.toString(), productId),
                      "imageUrl": "/image/packs/gymfit/GYMFIT25/2_mag.jpg"
                    },
                    {
                      "title": "Cultpass ELITE",
                      "subtitle": "Unlimited access to cult classes, all gyms",
                      "price": `₹${eliteUpgradeFeeBreakdown.totalCost}`,
                      "action": FitnessUtil.getUpgradeAction(upgradeTypes.PRO_SELECT_TO_ELITE, membership.id.toString(), productId),
                      "imageUrl": "/image/gymfit/products/bd3e04af-16a4-4079-abce-aa2321ad8e525ee2f2ed-da0e-463e-970f-77735050124dcultpass_PRO_-_Centre_05.png"
                    }
                  ]
            }
        }
    }

    private async buildMoreActions(userContext: UserContext, membership: Membership, moneyBackOfferDetail: MoneyBackOfferDetail): Promise<Action> {
        let productState: IssueProductState = null
        if (membership.status === "PURCHASED" || membership.status === "PAUSED") {
            productState = "AFTER_PURCHASE"
        } else if (membership.status === "CANCELLED" || membership.status === "SUSPENDED") {
            productState = "CANCELLATION"
        } else {
            productState = "BEFORE_PURCHASE"
        }
        const issues = await this.issueBusiness.getGymfitFitnessProductIssues(userContext, productState, membership.orderId)
        const actions: Action[] = []
        actions.push({
            actionType: "REPORT_ISSUE",
            actionId: "REPORT_ISSUE",
            title: "Need Help",
            url:  SUPPORT_DEEP_LINK,
            meta: {issues}
        })
        return {actionType: "ACTION_LIST", actions}
    }

    private buildStartDateWidget(userContext: UserContext, pack: OfflineFitnessPack, selectedStartDate: string, previousMembershipEndDate: string): DatePickerWidget {
        const tz = userContext.userProfile.timezone
        const startDate = previousMembershipEndDate ?? TimeUtil.todaysDateWithTimezone(tz)
        this.cfAnalytics.sendEventFromUserContext(<PackStartDateAnalyticsEvent> {
            analyticsEventName: AnalyticsEventName.PACK_START_DATE,
            from: "GymfitPackDetailViewBuilder.buildStartDateWidget",
            packDuration: (pack?.product.durationInDays ?? 0) * 86400,
            packName: pack?.title,
            productId: pack?.productId,
            finalEarliestStartDate: moment(startDate).tz(userContext.userProfile.timezone).format("YYYY-MM-DD"),
            previousMembEndDate: moment(previousMembershipEndDate).tz(userContext.userProfile.timezone).format("YYYY-MM-DD"),
            selectedStartDate: moment(selectedStartDate).tz(userContext.userProfile.timezone).format("YYYY-MM-DD")
        }, userContext, false, true, true, false)
        const datePickerWidget: DatePickerWidget = {
            title: "Pick a Start Date",
            startDate: startDate,
            endDate: TimeUtil.addDays(tz, startDate, DATE_SELECTION_WINDOW),
            selectedDate: _.isEmpty(selectedStartDate) ? null : selectedStartDate,
            canChangeStartDate: userContext.sessionInfo.isUserLoggedIn,
            widgetType: "DATE_PICKER_WIDGET",
            titleColour: "#000000",
            confirmActionType: "SET_START_DATE_FOR_GYM_PACK"
        }
        return datePickerWidget
    }

    private async buildOffersWidget(productId: string, offerV3Promise: Promise<GymFitProductPricesResponse>): Promise<WidgetView> {
        const offersV3Response = await offerV3Promise
        const offerItem = CultUtil.getOfferItem(offersV3Response, productId)
        if (offerItem && !_.isEmpty(offerItem.offers)) {
            const style = {
                offerTextStyle: {
                    color: "#333747",
                    fontSize: 14,
                    fontWeight: "500",
                },
                offerIconStyle: {
                    tintColor: "#000",
                    marginRight: 15,
                },
                tncIconStyle: {
                    height: 16,
                    width: 10,
                },
                offerInnerContainer: {
                    flexDirection: "row",
                    alignItems: "center",
                    marginBottom: 15,
                },
                backgroundColor: "#ffffff",
                gradientColors: ["#ffffff", "#ffffff"],
                titleStyle: {
                    color: "#000",
                }
            }
            return getOffersWidget("Offers", offerItem.offers, null, null, style)
        }
    }

    private async buildBenefitWidget(pack: AugmentedOfflineFitnessPack): Promise<WidgetView> {
        const benefits = await FitnessUtil.getSkuPlusBenefitsList(pack)
        const infoCards: InfoCard[] = []
        const infoCard: InfoCard = {
            title: "About this pack",
            subTitle: "",
            items: []
        }
        if (benefits) {
            infoCard.items = benefits.map((benefit: any) => {
                return {title: benefit.title} as InfoCard
            })
        }
        this.logger.info("SKU_PLUS_WEB benefits", benefits)
        infoCards.push(infoCard)
        this.logger.info("SKU_PLUS_WEB infoCards", infoCards)
        return new DescriptionWidget(infoCards)
    }

    private async getPrePurchaseProductNoteWidget(userContext: UserContext, pack: OfflineFitnessPack, offerV3Promise: Promise<GymFitProductPricesResponse>): Promise<ProductNoteWidget | undefined> {
        const offersV3Response = await offerV3Promise
        const offerItem = CultUtil.getOfferItem(offersV3Response, pack.productId)
        if (!offerItem) {
            return undefined
        }
        const offersMap = OfferUtil.segregateNoCostEMIOffers(offerItem.offers)
        const emiOffers = offersMap.get("NO_COST_EMI")
        if (!emiOffers.length) {
            return undefined
        }
        let maxEmiTenure = 12
        emiOffers.forEach((emiOffer) => {
            emiOffer.addons.forEach((addOn) => {
                // Max supported emi duration in months
                if (addOn.config && addOn.config.maxEmiTenure) {
                    maxEmiTenure = addOn.config.maxEmiTenure
                }
            })
        })
        const banks = emiOffers[0].description
        const numberOfMonths = CultUtil.getEmiTenureForPack(pack.product.durationInDays, maxEmiTenure)
        const offerDetails = CultUtil.getOfferDetailsPMS(pack, offersV3Response)
        const offerIds = _.map(offerDetails.offers, offer => { return offer.offerId })
        const result = {
            price: offerDetails.price,
            offerIds: offerIds
        }
        const action = AppUtil.isWeb(userContext) ? {
            actionType: "SHOW_NO_COST_EMI_DETAILS_MODAL",
            meta: {
                packId: pack.productId, // PackId to ProductId migration: ignoring: productId used as packId
                productType: pack.productType
            },
            title: "DETAILS"
        } : {
                actionType: "NAVIGATION",
                // PackId to ProductId migration: ignoring: productId used as packId
                url: `curefit://nocostemipage?packId=${pack.productId}&productType=${pack.productType}`,
                title: "DETAILS"
            }
        const data = [{
            title: {
                plainText: "Starting from ",
                boldText: `${Math.round(result.price.listingPrice / numberOfMonths)}/month*`
            },
            icon: "/image/icons/cult/emi-v1.png",
            action,
        }] as ProductNote[]
        return {
            widgetType: "PRODUCT_NOTE_WIDGET",
            data,
            header: {
                title: "No Cost EMI"
            },
            style: {
                container: {
                    marginHorizontal: 25,
                },
                headerText: {
                    fontSize: 18,
                },
                plainText: {
                    fontSize: 14,
                    color: "#333747"
                },
                boldText: {
                    color: "#000000",
                    fontSize: 14,
                },
                description: {
                    fontSize: 12,
                },
                action: {
                    fontSize: 13,
                },
                icon: {
                    height: 50,
                    width: 50,
                    marginLeft: -10,
                    marginRight: 0,
                },
            }
        }
    }

    private async buildIncludedGymsWidget(userContext: UserContext, restrictions?: GymfitListingCategoryRestriction[]) {
        const cityId = userContext.userProfile.cityId
        const userId = userContext.userProfile.userId

        const horizontalImageListWidget: HorizontalImageListWidget = new HorizontalImageListWidget()
        horizontalImageListWidget.header = {title: "CENTERS INCLUDED"}
        const listItems: HorizontalList[] = []

        const searchParams: GymFitCenterSearchFilters = {
            sortBy: "DISTANCE",
            latitude: userContext.sessionInfo.lat,
            longitude: userContext.sessionInfo.lon,
            pageNumber: 1,
            pageSize: 10,
            cityId,
            userId
        }
        if (!restrictions) {
            return null
        }
        for (const restriction of restrictions) {
            searchParams.categories = restriction.centerCategories
            const centers: GymfitCenter[] = await this.gymfitService.searchCenter(cityId, searchParams, userId)
            const items = centers.map(center => {
                const seoUrlParam: SeoUrlParams = GymfitUtil.getCenterSeoUrlParam(center)
                const listItem: HorizontalListItem = new HorizontalListItem
                const centerImageMedia = center.media && center.media.clpMedia ? center.media.clpMedia.find(media => media.type === MediaType.IMAGE) : undefined
                listItem.title = center.name
                listItem.image = centerImageMedia ? centerImageMedia.mediaUrl : undefined
                listItem.itemAction = {actionType: "NAVIGATION", url: AppUtil.isWeb(userContext) ? ActionUtilV1.getGymCenterWebUrl(seoUrlParam, center.id) : `curefit://gymfitcenter?centerId=${center.id}`}
                return listItem
            })
            const horizontalList: HorizontalList = new HorizontalList()
            horizontalList.header = {
                title: restriction.meta.accessType,
                subTitle: restriction.meta.accessLimit
            }
            let params = ""
            if (AppUtil.isWeb(userContext)) {
                searchParams.categories.forEach((category, index) => {
                    params += `categories[]=${category}${(index !== searchParams.categories.length - 1) ? "&" : ""}`
                })
            } else {
                params = `categories[]=${searchParams.categories.join(",")}`
            }
            horizontalList.action = {
                actionType: "NAVIGATION",
                url: `curefit://allgyms?${params}`
            }
            horizontalList.items = items
            listItems.push(horizontalList)
        }
        horizontalImageListWidget.listData = listItems
        return horizontalImageListWidget
    }

    private async buildImportantUpdatesWidget(userContext: UserContext): Promise<BannerCarouselWidget> {
        return GymfitUtil.getImportantUpdatesWidget(userContext, { containerStyle: { marginBottom: 25 } })
    }

    private async buildWhatYouGetWidget(userContext: UserContext, cultBenefit?: Benefit): Promise<BannerCarouselWidget> {
        return GymfitUtil.getWhatYouGetWidget(userContext, cultBenefit, { containerStyle: { marginBottom: 25 } })
    }

    private async buildHowItWorksWidget(userContext: UserContext): Promise<ProductListWidget> {
        try {
            const header: Header = {
                title: this.gymfitPackPageConfig.howItWorksTitle,
                titleProps: {
                    style: { marginTop: 20, fontSize: 17 },
                }
            }
            const infoCards: InfoCard[] = []
            const howItWorksItemList = userContext.sessionInfo.osName.toLowerCase() === "ios" ? this.gymfitPackPageConfig.howItWorksItemListIOS : this.gymfitPackPageConfig.howItWorksItemList
            howItWorksItemList.forEach(item => {
                infoCards.push({
                    subtitleStyle: {
                        color: "#333747",
                        fontFamily: "Inter-Medium",
                        fontWeight: "500",
                    },
                    subTitle: item.text,
                    icon: item.icon,
                    cellStyle: {
                        paddingLeft: 12,
                        backgroundColor: "#ffffff",
                    }
                })
            })
            const widget = new ProductListWidget("SMALL", header, infoCards)
            widget.hideSepratorLines = true
            widget.layoutProps = {
                iconStyle: {
                    tintColor: "#ffffff",
                }
            }
            return widget
        } catch (e) {
            return undefined
        }
    }

    private async buildPostPurchaseHowItWorksWidget(userContext: UserContext): Promise<ProductListWidget> {
        try {
            const header: Header = {
                title: this.gymfitPackPageConfig.howItWorksTitle
            }
            const infoCards: InfoCard[] = []
            infoCards.push({
                subTitle: "Visit a cult gym at any time, check-in via your phone and start your workout",
                icon: "/image/gymfit/services/gym.png"
            })
            infoCards.push({
                subTitle: "Book a cult class that you like. Reach the center on time and enjoy your workout",
                icon: "/image/gymfit/services/cult.png"
            })
            if (userContext.sessionInfo.osName.toLowerCase() !== "ios") {
                infoCards.push({
                    subTitle: "Choose from the wide variety of online workouts and join in from anywhere",
                    icon: "/image/gymfit/services/live.png"
                })
            }
            const widget = new ProductListWidget("SMALL", header, infoCards)
            widget.hideSepratorLines = true
            return widget
        } catch (e) {
            return undefined
        }
    }

    private async buildLuxPostPurchaseHowItWorksWidget(userContext: UserContext): Promise<ProductListWidget> {
        try {
            const header: Header = {
                title: this.gymfitPackPageConfig.howItWorksTitle
            }
            const infoCards: InfoCard[] = []
            infoCards.push({
                subTitle: "Visit a cult gym at any time, check-in via your phone and start your workout", // TODO - update this
                icon: "/image/gymfit/services/gym.png"
            })
            infoCards.push({
                subTitle: "Book a cult class that you like. Reach the center on time and enjoy your workout",
                icon: "/image/gymfit/services/cult.png"
            })
            if (userContext.sessionInfo.osName.toLowerCase() !== "ios") {
                infoCards.push({
                    subTitle: "Choose from the wide variety of online workouts and join in from anywhere",
                    icon: "/image/gymfit/services/live.png"
                })
            }
            const widget = new ProductListWidget("SMALL", header, infoCards)
            widget.hideSepratorLines = true
            return widget
        } catch (e) {
            return undefined
        }
    }

    private async buildOnePassPostPurchaseHowItWorksWidget(userContext: UserContext): Promise<ProductListWidget> {
        try {
            const header: Header = {
                title: this.gymfitPackPageConfig.howItWorksTitle
            }
            const infoCards: InfoCard[] = []
            infoCards.push({
                subTitle: "Visit a onepass gym at any time, check-in via your phone and start your workout", // TODO - update this
                icon: "/image/gymfit/services/gym.png"
            })
            infoCards.push({
                subTitle: "Book a onepass class that you like. Reach the center on time and enjoy your workout",
                icon: "/image/gymfit/services/cult.png"
            })
            if (userContext.sessionInfo.osName.toLowerCase() !== "ios") {
                infoCards.push({
                    subTitle: "Choose from the wide variety of online workouts and join in from anywhere",
                    icon: "/image/gymfit/services/live.png"
                })
            }
            const widget = new ProductListWidget("SMALL", header, infoCards)
            widget.hideSepratorLines = true
            return widget
        } catch (e) {
            return undefined
        }
    }

    private async buildGymfitPromiseWidget(): Promise<ProductGuranteeWidget> {
        try {
            const guranteeInfo = this.gymfitPackPageConfig.guranteeInfo
            const guranteeWidget: ProductGuranteeWidget = {
                widgetType: "PRODUCT_GURANTEE_WIDGET",
                productType: "GYMFIT_PROMISE",
                header: {
                    title: "What You Get",
                    subTitle: guranteeInfo.length + " reasons to buy cultpass PRO",
                    titleFontSize: 18,
                },
                containerStyle: { marginHorizontal: 10, marginTop: 30 },
                layoutType: "GRID",
                data: guranteeInfo
            }
            return guranteeWidget
        } catch (e) {
            return undefined
        }
    }

    private async getPrePurchasePageActions(userContext: UserContext, selectedStartDate: string, pack: OfflineFitnessPack, offerV3Promise: Promise<GymFitProductPricesResponse>, previousMembershipEndDate: string): Promise<Action[]> {
        const tz = userContext.userProfile.timezone
        const startDate = previousMembershipEndDate ?? TimeUtil.todaysDateWithTimezone(tz)
        if (!selectedStartDate) {
            if ((AppUtil.isWeb(userContext) || userContext.sessionInfo.appVersion >= 8.67) && !userContext.sessionInfo.isUserLoggedIn) {
                return [
                    {
                        actionType: "SHOW_ALERT_MODAL",
                        title: "Pick a start date",
                        meta: {
                            title: "Login Required!",
                            subTitle: "Please login to continue",
                            actions: [{ actionType: "LOGOUT", title: "Login" }]
                        }
                    }
                ]
            }
            return [{
                title: "Pick a start date",
                actionType: "SHOW_DATE_PICKER",
                meta: {
                    startDate: startDate,
                    endDate: TimeUtil.addDays(tz, startDate, DATE_SELECTION_WINDOW),
                    selectedDate: _.isEmpty(selectedStartDate) ? null : selectedStartDate,
                    canChangeStartDate: true,
                    confirmActionType: "SET_START_DATE_FOR_PACK"
                }
            }]
        }
        const offersV3Response = await offerV3Promise
        const offerItem = CultUtil.getOfferItem(offersV3Response, pack.productId)
        const offerIds = offerItem.offers.map((offer) => offer.offerId)
        const city = userContext.userProfile.city
        if (city && city.parentCityId && CITY_SPLIT_ENABLED_CITY_IDS.includes(city.parentCityId) && await AppUtil.isCitySplitFeatureSupported(userContext) && !AppUtil.isCitySplitSelectionSupported(userContext)) {
            return [AppActionUtil.appUpdateAction(userContext, "Buy Now", "Update app to see the latest pack price. Lower prices in Thane & Navi Mumbai vs rest of Mumbai.", "Mumbai is split into two areas")]
        }
        return [{
            title: "Buy Now",
            actionType: "GET_GYMFIT_PACK",
            meta: {
                orderProduct: {
                    productId: pack.productId,
                    option: {
                        startDate: selectedStartDate,
                        offerV2Ids: offerIds
                    },
                    quantity: 1
                },
                useFitCash: true
            }
        }]

    }

    private getSelectPackActions(title: string, url: string): Action[] {
        return [{
            title: title,
            url: url,
            actionType: "NAVIGATION"
        }]
    }

    private getPostPurchaseAction(): Action[] {
        return [{actionType: "NAVIGATION", title: "CHECK-IN", url: "curefit://allgyms"}]
    }

    private getOnePassPostPurchaseAction(userContext: UserContext): Action[] {
        const isOnePassSupported = AppUtil.isOnePassSupported(userContext)
        if (!isOnePassSupported) {
            return [{
                actionType: "SHOW_ALERT_MODAL",
                title: "BOOK A SESSION",
                meta: {
                    title: "Update the app",
                    subTitle: "This action is not supported in the current app version. Please update the app.",
                    actions: [{  actionType: "EMPTY_ACTION", title: "OK" }]
                }
            }]
        }
        return [{actionType: "NAVIGATION", title: "BOOK A SESSION", url: "curefit://allgyms?centerType=ONEPASS"}]
    }

    private getLuxPostPurchaseAction(centerId: String): Action[] {
        return [{actionType: "NAVIGATION", title: "CHECK-IN", url: `curefit://gymquickcheckin?centerId=${centerId}`}]
    }

    private getPackBreadCrumbs(title: string): IBreadCrumb[] {
        return [{
            title: "Home",
            pathname: "/",
        }, {
            title: "Cult",
            pathname: "/cult"
        }, {
            title: "cultpass PRO",
            pathname: "/cult/cult-pass"
        }, {
            title
        }]
    }

    private getSelectPackBreadCrumbs(title: string): IBreadCrumb[] {
        return [{
            title: "Home",
            pathname: "/",
        }, {
            title: "Cult",
            pathname: "/cult"
        }, {
            title: "cultpass Select",
            pathname: "/cult/cult-pass"
        }, {
            title
        }]
    }
    private getDisableTransferMembershipAction(message: String): Action {
        return {
            iconUrl: "/image/icons/cult/transfer_black.png",
            title: "TRANSFER",
            isDisabled: true,
            actionType: "SHOW_ALERT_MODAL",
            meta: {
                title: "Membership can't be transferred",
                subTitle: message,
                actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
            }
        }
    }
}

export default GymfitPackDetailViewBuilder
