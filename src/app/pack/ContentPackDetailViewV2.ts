import {
    Action,
    ActionCard,
    CultBuddiesJoiningListSmallView,
    DayPickerWidget,
    DayTimePickerWidget,
    DescriptionWidget,
    DiyDownloadAllSessionWidget,
    DiyPackSummaryWidget,
    Header,
    InfoCard,
    ListSubType,
    ManageOptionPayload,
    ManageOptions,
    ManageOptionsWidget,
    ProductDetailPage,
    ProductListWidget,
    ReminderWidget,
    StructuredSchemaVideoWidget,
    TimePickerWidget
} from "../common/views/WidgetView"
import { ImageCategoryType as ImageCategory, UrlPathBuilder } from "@curefit/product-common"
import { CdnUtil, pad, pluralizeStringIfRequired, TimeUtil, Timezone } from "@curefit/util-common"
import AtlasUtil, { DiyContentDetail, DiyContentMeta } from "../util/AtlasUtil"
import { FoodProduct as Product } from "@curefit/eat-common"
import { HourMin, UserAgentType as UserAgent } from "@curefit/base-common"
import MindDIYPackPageConfig from "./MindDIYPackPageConfig"
import CultDIYPackPageConfig from "./CultDIYPackPageConfig"
import * as momentTz from "moment-timezone"
import * as _ from "lodash"
import AppUtil, { DIY_SESSION_PAGE_REMOVAL_VERSION } from "../util/AppUtil"
import { TitleDescriptionListWidget } from "../page/vm/widgets/TitleDescriptionListWidget"
import { IBreadCrumb } from "../page/ondemand/OnDemandCommon"

import {
    AdditionalResource,
    DIYFitnessPack,
    DIYFitnessPackExtended,
    DIYFitnessProductExtended,
    DIYMeditationPack,
    DIYMeditationPackExtended,
    DIYMeditationProductExtended,
    DIYPack,
    DIYPackFulfilment,
    DIYProduct,
    DIYUserFitnessPack,
    DIYUserMeditationPack,
    FitnessProgramProductResponse,
    SessionCompletionStatus,
} from "@curefit/diy-common"
import { ActionUtil, ActionUtilV1 } from "@curefit/base-utils"
import { ActionUtil as AppActionUtil } from "../util/ActionUtil"
import { UserContext } from "@curefit/userinfo-common"
import {
    CultWorkoutTagsWidget,
    ImageOverlayCardContainerWidget,
    ImagesInText,
    ListWidgetV2,
    TLCollectionGridItem,
    TLCollectionGridWidget,
    TLCollectionSingle,
    TLCollectionSinglesWidget,
    TLDIYPackDetailsWidget
} from "@curefit/apps-common"
import LiveUtil from "../util/LiveUtil"
import { DaysRemainingWidget } from "@curefit/vm-models"
import { IBaseWidget } from "@curefit/vm-common"
import { IdVsValueMap } from "../digital/DigitalSocialLeagueTabPageViewBuilder"
import { Fonts } from "../digital/DigitalLeagueSectionViewBuilder"
import { LivePackUtil } from "../util/LivePackUtil"
import CultUtil from "../util/CultUtil"
import { ListItem } from "@curefit/apps-common/dist/src/widgets/interfaces"

export interface PackSubscribeAction extends Action {
    packId: string
    selectedDays?: number[]
    selectedTimeSlot?: string
    reminder?: boolean
    otherParams?: any
}
export interface DiySessionActionCard extends ActionCard {
    content?: DiyContentDetail
    description?: string
    meta?: DiyContentMeta
    isCompleted?: boolean
    isActive: boolean
    moreAction?: Action
    playAction?: Action
    favAction?: Action
    roundedCorners?: boolean
    footer?: CultBuddiesJoiningListSmallView
    isLocked?: boolean
    upNextAction?: Action
    actions?: Action[]
}

type ImageInTextSource = "IMAGE_OVERLAY_WIDGET" | "PRODUCT_LIST_WIDGET_CELL"

type SummaryWidgetSource = "IMAGE_OVERLAY_WIDGET"

abstract class ContentPackDetailViewV2 extends ProductDetailPage {

    constructor() {
        super()
    }

    protected preparePage(userAgent: UserAgent, appVersion: number, userPackInfo: (DIYUserFitnessPack | DIYUserMeditationPack), pageConfig: MindDIYPackPageConfig | CultDIYPackPageConfig,
        sessionMap: { [productId: string]: Product }, blockInternationalUser: boolean, breadCrumbs: IBreadCrumb[], daysRemainingWidget?: DaysRemainingWidget, userContext?: UserContext, isInternalUser?: Boolean,
        packRecommendedByBuddies?: CultBuddiesJoiningListSmallView, sessionRecommendedByBuddiesMap?: { [sessionId: string]: CultBuddiesJoiningListSmallView }, isUserEligibleForTrial?: boolean, isUserElibgibleForMonetisation?: boolean, userHasActiveMembership?: { start: number, end: number } | undefined, bucketId?: string) {
        const pack = userPackInfo.pack
        const packFulfilment = userPackInfo.fulfilment
        const isWeb = AppUtil.isWebUserAgent(userAgent)
        const isMWebUserAgent = AppUtil.isMWeb(userContext)
        if (isWeb) {
            this.breadCrumbs = breadCrumbs ? breadCrumbs : []
        }
        if (isWeb && !isMWebUserAgent && daysRemainingWidget) {
            this.widgets.push(daysRemainingWidget)
        }
        this.widgets.push(this.getSummaryWidget(pack, packFulfilment, userAgent, userContext))
        if (isMWebUserAgent && daysRemainingWidget) {
            this.widgets.push(daysRemainingWidget)
        }
        if (packFulfilment && packFulfilment.status === "SUBSCRIBED") {
            const totalSessions = pack.sessionIds.length
            const sessionCompleted = packFulfilment.completedProductIds ? packFulfilment.completedProductIds.length : 0
            if (!pack.isCollection) {
                this.widgets.push(this.getDownloadAllSessionWidget(pack, sessionMap))
            }
            if (sessionCompleted !== totalSessions) {
                const nextSessionIdx = LiveUtil.getNextDIYSessionIdx(pack, packFulfilment)
                const session = <DIYProduct>sessionMap[pack.sessionIds[nextSessionIdx]]
                this.widgets.push(this.getUpNextWidget(session, pack, packFulfilment, userContext, blockInternationalUser, isUserEligibleForTrial, isUserElibgibleForMonetisation, bucketId))
            }
            this.widgets.push(this.getSessionsWidget(sessionMap, pack, packFulfilment, appVersion, userAgent, blockInternationalUser, userContext, undefined, undefined, isUserEligibleForTrial, isUserElibgibleForMonetisation, bucketId))
        }
        if (pack.packIntroContentId)
            this.widgets.push(this.getIntroductionVideoWidget(pack))
        this.widgets.push(this.getDescriptionWidget(pack.subTitle))
        if (!_.isEmpty(pack.benefits)) {
            this.widgets.push(this.getBenefitsWidget(pack.benefits))
        }
        if (!packFulfilment || packFulfilment.status !== "SUBSCRIBED") {
            this.widgets.push(this.getHowItWorksWidget(pageConfig))
        }
        const pageAction = this.getPageActions(pack, packFulfilment)
        this.pageAction = pageAction.action
        this.actions = pageAction.actions
    }

    _getDefaultTimeSlot(timezone: Timezone) {
        const date = TimeUtil.getMomentNow(timezone)
        date.set("minute", date.minutes() - (date.minutes() % 15))
        return { hour: date.hours(), min: date.minutes() }
    }

    protected getExercisesDescription(session: DIYFitnessProductExtended): string {
        let description
        if (!_.isEmpty(session.exercises)) {
            description = session.exercises.join(", ")
        }
        return description
    }

    protected getUpNextWidget(session: DIYProduct, pack: DIYPack, packFulfilment: DIYPackFulfilment, userContext: UserContext, blockInternationalUser: boolean, isUserEligibleForTrial?: boolean, isUserElibgibleForMonetisation?: boolean, bucketId?: string): ProductListWidget {
        const header: Header = {
            title: "Up Next"
        }
        const actionCards = this.getUpNextWidgetCards(session, pack, packFulfilment, userContext, blockInternationalUser, isUserEligibleForTrial, isUserElibgibleForMonetisation, bucketId)
        return new ProductListWidget("LARGE", header, actionCards)
    }


    protected getUpNextWidgetCards(session: DIYProduct, pack: DIYPack, packFulfilment: DIYPackFulfilment, userContext: UserContext, blockInternationalUser: boolean, isUserEligibleForTrial?: boolean, isUserElibgibleForMonetisation?: boolean, bucketId?: string): DiySessionActionCard[] {
        const actionCards: DiySessionActionCard[] = []
        const description = session.productType === "DIY_FITNESS" ? this.getExercisesDescription(<DIYFitnessProductExtended>session) : undefined
        const duration = momentTz.duration(typeof session.duration === "string" ? parseInt(session.duration) : session.duration).humanize()
        const typeCastedSession = session.productType === "DIY_MEDITATION" ? <DIYMeditationProductExtended>session : <DIYFitnessProductExtended>session
        let detailAction = this.getSessionPageAction(userContext, isUserElibgibleForMonetisation, isUserEligibleForTrial, pack, session, 0, bucketId, blockInternationalUser)
        if (AppUtil.isWeb(userContext)) {
            detailAction = this.getSessionWidgetItemAction(session, pack, packFulfilment, isUserEligibleForTrial, session.locked, userContext, isUserElibgibleForMonetisation, bucketId, blockInternationalUser)
        }
        actionCards.push({
            title: session.title,
            subTitle: duration,
            description: description,
            image: UrlPathBuilder.prefixSlash(session.imageDetails.thumbnailImage),
            action: this.getActivityScreenURL(pack.productId, session.productId),
            detailAction: detailAction,
            content: AtlasUtil.getContentDetailV2(session),
            meta: AtlasUtil.getContentMetaV2(session, pack),
            isCompleted: false,
            isActive: true,
            moreAction: LiveUtil.getDIYShareAction(pack.productId, session.productId, session.productType, pack.title, session.title),
            // favAction: LiveUtil.getDIYFavAction(userContext, session.productId, session.productType, typeCastedSession.bookmarked),
            roundedCorners: true,
            cardAction: this.getSessionWidgetItemAction(session, pack, packFulfilment, isUserEligibleForTrial, session.locked, userContext, isUserElibgibleForMonetisation, bucketId, blockInternationalUser),
            isLocked: AppUtil.isLiveContentLocked(userContext, session.locked, isUserElibgibleForMonetisation, isUserEligibleForTrial)
        })
        return actionCards
    }

    protected getSessionsWidget(sessionMap: { [productId: string]: Product }, pack: DIYPack, packFulfilment: DIYPackFulfilment, appVersion: number, userAgent: UserAgent, blockInternationalUser: boolean, userContext?: UserContext, isInternalUser?: Boolean,
        sessionRecommendedByBuddiesMap?: { [sessionId: string]: CultBuddiesJoiningListSmallView }, isUserEligibleForTrial?: boolean, isUserEligibleForMonetisation?: boolean, bucketId?: string): ProductListWidget {
        const header: Header = {
            title: "Sessions in this pack"
        }
        const actionCards = this.getSessionsWidgetItems(sessionMap, pack, packFulfilment, appVersion, blockInternationalUser, userContext, sessionRecommendedByBuddiesMap, isUserEligibleForTrial, isUserEligibleForMonetisation, bucketId)
        const productListWidget: ProductListWidget = new ProductListWidget("LARGE", header, actionCards)
        productListWidget.hideSepratorLines = true
        return productListWidget
    }

    private getSessionPageActionUrl(pack: DIYPack, session: DIYProduct | FitnessProgramProductResponse, appVersion: number, userAgent: UserAgent, isSugarFitOrUltraFitApp: boolean) {
        if (userAgent === "APP" && (appVersion >= DIY_SESSION_PAGE_REMOVAL_VERSION || isSugarFitOrUltraFitApp)) {
            return undefined
        }
        return this.getActivityScreenURL(pack.productId, session.productId)
    }

    private getSessionPageAction(userContext: UserContext, isUserEligibleForMonetisation: boolean, isUserEligibleForTrial: boolean, pack: DIYPack, session: DIYProduct, appVersion: number, bucketId: string, blockInternationalUser: boolean) {
        const userAgent = userContext.sessionInfo.userAgent
        if (userAgent === "APP" && appVersion >= DIY_SESSION_PAGE_REMOVAL_VERSION) {
            return undefined
        }
        const url = this.getActivityScreenURL(pack.productId, session.productId)
        const action: Action = {
            actionType: "NAVIGATION",
            url: url
        }
        return LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, session.locked, action, isUserEligibleForTrial, session.productType, "diy_page_session_item", bucketId, blockInternationalUser, session.format)
    }

    private getSessionV2PageAction(userContext: UserContext, isUserEligibleForMonetisation: boolean, isUserEligibleForTrial: boolean, pack: DIYPack, session: FitnessProgramProductResponse, appVersion: number, bucketId: string, blockInternationalUser: boolean) {
        const userAgent = userContext.sessionInfo.userAgent
        if (userAgent === "APP" && appVersion >= DIY_SESSION_PAGE_REMOVAL_VERSION && session.productType != "DIY") {
            return undefined
        }
        const url = this.getActivityScreenURL(pack.productId, session.productId)
        const action: Action = {
            actionType: "NAVIGATION",
            url: url
        }
        return LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, session.locked, action, isUserEligibleForTrial, "DIY_FITNESS_PACK", "diy_page_session_item", bucketId, blockInternationalUser, session.format)
    }

    protected getSessionsWidgetItems(sessionMap: { [productId: string]: Product }, pack: DIYPack, packFulfilment: DIYPackFulfilment, appVersion: number, blockInternationalUser: boolean, userContext?: UserContext,
        sessionRecommendedByBuddiesMap?: { [sessionId: string]: CultBuddiesJoiningListSmallView }, isUserEligibleForTrial?: boolean, isUserEligibleForMonetisaiton?: boolean, bucketId?: string, diyEnergyMeterSupportedMap?: IdVsValueMap<boolean>): DiySessionActionCard[] {
        const productIds = pack.sessionIds
        const actionCards: DiySessionActionCard[] = []
        const allSessionsHaveEnergyMeterSupported = LivePackUtil.canShowPackLevelEnergyIcon(diyEnergyMeterSupportedMap)
        let completedSessionIds: string[] = []
        if (packFulfilment !== undefined && packFulfilment.completedProductIds !== undefined) {
            completedSessionIds = packFulfilment.completedProductIds
        }
        for (let i = 0; i < productIds.length; i++) {
            const productId = productIds[i]
            const session = <DIYProduct>sessionMap[productId]
            if (_.isEmpty(session)) {
                continue
            }
            const widgetActions: Action[] = []
            const isSugarFitOrUltraFitApp = AppUtil.isSugarFitOrUltraFitApp(userContext)
            const diyEnergyIconSupported = diyEnergyMeterSupportedMap?.[productId] && !allSessionsHaveEnergyMeterSupported && session.productType === "DIY_FITNESS"
            const description = session.productType === "DIY_FITNESS" ? this.getExercisesDescription(<DIYFitnessProductExtended>session) : undefined
            const isSessionCompleted = completedSessionIds.includes(productId)
            // const isActive = i <= packInfo.userStatus.numberOfSessionsCompleted ? true : false
            const isActive = true // Making all as active
            const duration = LiveUtil.getFormattedTimeString(typeof session.duration === "string" ? parseInt(session.duration) : session.duration)
            const shareAction = LiveUtil.getDIYShareAction(pack.productId, productId, session.productType, pack.title, session.title)
            const isLocked = session.locked === undefined ? isUserEligibleForMonetisaiton : (session.locked && isUserEligibleForMonetisaiton)
            const downloadAction = !isSugarFitOrUltraFitApp && !isLocked ? LiveUtil.getDIYDownloadAction(userContext, session.format, blockInternationalUser) : undefined
            shareAction && widgetActions.push(shareAction)
            downloadAction && widgetActions.push(downloadAction)
            let detailAction = this.getSessionPageAction(userContext, isUserEligibleForMonetisaiton, isUserEligibleForTrial, pack, session, appVersion, bucketId, blockInternationalUser)
            if (AppUtil.isWeb(userContext)) {
                detailAction = this.getSessionWidgetItemAction(session, pack, packFulfilment, isUserEligibleForTrial, isLocked, userContext, isUserEligibleForMonetisaiton, bucketId, blockInternationalUser)
            }
            const isTVApp = AppUtil.isTVApp(userContext)
            const classDetailAction = LiveUtil.getDIYSessionDetailAction(session, pack.productId, undefined, session.format, userContext, "")
            actionCards.push({
                title: session.title,
                subTitle: !diyEnergyIconSupported ? duration : undefined,
                subtitleStyle: { color: "#868686", fontFamily: Fonts.Regular },
                textOrder: [
                    "TITLE",
                    "DESCRIPTION",
                    "IMAGES_IN_TEXT",
                    "FOOTER",
                    "SUBTITLE",
                ],
                imagesInTextProps: diyEnergyIconSupported ? this.getImagesInTextProps(duration, "PRODUCT_LIST_WIDGET_CELL") : undefined,
                flexOneContainerStyle: { paddingBottom: 5, paddingTop: 2 },
                description: description,
                imageV2: {
                    url: UrlPathBuilder.prefixSlash(session.imageDetails.thumbnailImage),
                    cloudinaryConfig: ["c_fill", "g_face"]
                },
                image: session.isPastLiveV2 ? `https://cdn-images.cure.fit/www-curefit-com/image/upload/w_200,h_200,c_fill,g_face` + UrlPathBuilder.prefixSlash(session.imageDetails.thumbnailImage) : UrlPathBuilder.prefixSlash(session.imageDetails.thumbnailImage),
                action: this.getSessionPageActionUrl(pack, session, appVersion, userContext.sessionInfo.userAgent, isSugarFitOrUltraFitApp),
                actions: widgetActions,
                cardAction:  session.productType === "DIY_FITNESS" && !isTVApp ? classDetailAction : this.getSessionWidgetItemAction(session, pack, packFulfilment, isUserEligibleForTrial, isLocked, userContext, isUserEligibleForMonetisaiton, bucketId, blockInternationalUser),
                playAction: this.getSessionWidgetItemAction(session, pack, packFulfilment, isUserEligibleForTrial, isLocked, userContext, isUserEligibleForMonetisaiton, bucketId, blockInternationalUser),
                // moreAction: shareAction,
                // favAction: LiveUtil.getDIYFavAction(userContext, session.productId, session.productType, typeCastedSession.bookmarked),
                detailAction: session.productType === "DIY_FITNESS" && !isTVApp ? classDetailAction : detailAction,
                content: AtlasUtil.getContentDetailV2(session),
                meta: AtlasUtil.getContentMetaV2(session, pack),
                isCompleted: isSessionCompleted,
                isActive: isActive,
                roundedCorners: true,
                footer: sessionRecommendedByBuddiesMap && sessionRecommendedByBuddiesMap[session.productId] ? sessionRecommendedByBuddiesMap[session.productId] : undefined,
                isLocked: AppUtil.isLiveContentLocked(userContext, session.locked, isUserEligibleForMonetisaiton, isUserEligibleForTrial)
            })
        }
        return actionCards
    }

    protected getSessionsWidgetItemsV2(sessionsV2: FitnessProgramProductResponse[], pack: DIYPack, packFulfilment: DIYPackFulfilment, appVersion: number, blockInternationalUser: boolean, userContext?: UserContext
        , sessionRecommendedByBuddiesMap?: { [sessionId: string]: CultBuddiesJoiningListSmallView }, isUserEligibleForTrial?: boolean, isUserEligibleForMonetisaiton?: boolean, bucketId?: string, diyEnergyMeterSupportedMap?: IdVsValueMap<boolean>): DiySessionActionCard[] {
        const actionCards: DiySessionActionCard[] = []
        const allSessionsHaveEnergyMeterSupported = LivePackUtil.canShowPackLevelEnergyIcon(diyEnergyMeterSupportedMap)

        for (let i = 0; i < sessionsV2.length; i++) {
            const session = sessionsV2[i]
            if (_.isEmpty(session)) {
                continue
            }
            const productId = session.productId
            // const session = sessionMapV2[productId]
            const widgetActions: Action[] = []
            const isSugarFitOrUltraFitApp = AppUtil.isSugarFitOrUltraFitApp(userContext)
            const diyEnergyIconSupported = diyEnergyMeterSupportedMap?.[productId] && !allSessionsHaveEnergyMeterSupported && session.productType === "DIY"
            const week = Math.floor((session.dayOfTheProgram - 1 ) / 7) + 1
            const day = (session.dayOfTheProgram - 1) % 7 + 1
            const description = `Week ${week} | Day ${day}` + "\n" + session.description
            const isSessionCompleted = session.sessionCompletionStatus === SessionCompletionStatus.COMPLETED
            // const isActive = i <= packInfo.userStatus.numberOfSessionsCompleted ? true : false
            const isActive = true // Making all as active
            let duration = LiveUtil.getFormattedTimeString(typeof session.duration === "string" ? parseInt(session.duration) : session.duration)
            if (session.productType != "DIY") {
                duration += ", " + TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, session.scheduledTimeEpoch, "DD MMM h:mm A")
            }

            let shareAction
            if (!isSugarFitOrUltraFitApp) {
                if (session.productType === "DIY") {
                    shareAction = LiveUtil.getDIYShareAction(pack.productId, productId, "DIY_FITNESS_PACK", pack.title, session.title)
                }
                else {
                    shareAction = LiveUtil.getLiveShareAction(productId, session.title, session.productType, pack.productId)
                }
            }
            const isLocked = session.locked === undefined ? isUserEligibleForMonetisaiton : (session.locked && isUserEligibleForMonetisaiton)
            const downloadAction = !isSugarFitOrUltraFitApp && session.productType === "DIY" && !isLocked ? LiveUtil.getDIYDownloadAction(userContext, session.format , blockInternationalUser) : undefined
            shareAction && widgetActions.push(shareAction)
            downloadAction && widgetActions.push(downloadAction)

            let detailAction = this.getSessionV2PageAction(userContext, isUserEligibleForMonetisaiton, isUserEligibleForTrial, pack, session, appVersion, bucketId, blockInternationalUser)
            if (AppUtil.isWeb(userContext)) {
                detailAction = this.getSessionV2WidgetItemAction(session, pack, packFulfilment, isUserEligibleForTrial, isLocked, userContext, isUserEligibleForMonetisaiton, bucketId, blockInternationalUser)
            }
            const isTVApp = AppUtil.isTVApp(userContext)
            let classDetailAction


            if (session.productType === "DIY") {
                classDetailAction = LiveUtil.getDIYSessionDetailAction(session, pack.productId, undefined, session.format, userContext, session.title)
            }
            else {
                classDetailAction = LiveUtil.getLiveSessionDetailActionFromLiveClassId(session.productId, session.productId, "diy_pack_widget", session.format, userContext, session.title)
            }
            actionCards.push({
                title: session.title,
                subTitle: !diyEnergyIconSupported ? duration : undefined,
                subtitleStyle: {color: "#868686", fontFamily: Fonts.Regular},
                textOrder: [
                    "TITLE",
                    "DESCRIPTION",
                    "IMAGES_IN_TEXT",
                    "FOOTER",
                    "SUBTITLE",
                ],
                imagesInTextProps: diyEnergyIconSupported ? this.getImagesInTextProps(duration, "PRODUCT_LIST_WIDGET_CELL") : undefined,
                flexOneContainerStyle: {paddingBottom: 5, paddingTop: 2},
                description: description,
                imageV2: {
                    url: UrlPathBuilder.prefixSlash(session.imageUrl),
                    cloudinaryConfig: ["c_fill", "g_face"]
                },
                image: session.imageUrl,
                action: this.getSessionPageActionUrl(pack, session, appVersion, userContext.sessionInfo.userAgent, isSugarFitOrUltraFitApp),
                actions: widgetActions,
                cardAction: !isTVApp ? classDetailAction : this.getSessionV2WidgetItemAction(session, pack, packFulfilment, isUserEligibleForTrial, isLocked, userContext, isUserEligibleForMonetisaiton, bucketId, blockInternationalUser),
                playAction: this.getSessionV2WidgetItemAction(session, pack, packFulfilment, isUserEligibleForTrial, isLocked, userContext, isUserEligibleForMonetisaiton, bucketId, blockInternationalUser),
                // moreAction: shareAction,
                // favAction: LiveUtil.getDIYFavAction(userContext, session.productId, session.productType, typeCastedSession.bookmarked),
                detailAction: classDetailAction,
                content: AtlasUtil.getContentDetailV3(session),
                meta: AtlasUtil.getContentMetaV3(session, pack),
                isCompleted: isSessionCompleted,
                isActive: isActive,
                roundedCorners: true,
                footer: sessionRecommendedByBuddiesMap && sessionRecommendedByBuddiesMap[session.productId] ? sessionRecommendedByBuddiesMap[session.productId] : undefined,
                isLocked: AppUtil.isLiveContentLocked(userContext, session.locked, isUserEligibleForMonetisaiton, isUserEligibleForTrial)
            })
        }

        return actionCards
    }

    protected getSessionWidgetItemAction(session: DIYProduct, pack: DIYPack, packFulfilment: DIYPackFulfilment, isUserEligibleForTrial: boolean, isLocked: boolean, userContext: UserContext, isUserEligibleForMonetisaiton: boolean, bucketId: string, blockInternationalUser: boolean) {
        if (!userContext.sessionInfo.isUserLoggedIn) {
            return {
                actionType: "SHOW_LOGIN_MODAL",
                url: "curefit://loginmodal",
            }
        }
        const action = {
            actionType: "PLAY_VIDEO",
            content: AtlasUtil.getContentDetailV2(session),
            meta: {
                content: AtlasUtil.getContentDetailV2(session),
                queryParams: AtlasUtil.getContentMetaV2(session, pack),
                title: session.title,
                packId: pack.productId,
                productType: session.productType,
                checkDownloadStatus: true
            }
        }
        return LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisaiton, isLocked, action, isUserEligibleForTrial, session.productType, "diy_page_session_item", bucketId, blockInternationalUser, session.format)
    }

    protected getSessionV2WidgetItemAction(session: FitnessProgramProductResponse, pack: DIYPack, packFulfilment: DIYPackFulfilment, isUserEligibleForTrial: boolean, isLocked: boolean, userContext: UserContext, isUserEligibleForMonetisaiton: boolean, bucketId: string, blockInternationalUser: boolean) {
        if (!userContext.sessionInfo.isUserLoggedIn) {
            return {
                actionType: "SHOW_LOGIN_MODAL",
                url: "curefit://loginmodal",
            }
        }
        let action
        if ( session.productType == "DIY") {
            action = {
                actionType: "PLAY_VIDEO",
                content: AtlasUtil.getContentDetailV3(session),
                meta: {
                    content: AtlasUtil.getContentDetailV3(session),
                    queryParams: AtlasUtil.getContentMetaV3(session, pack),
                    title: session.title,
                    packId: pack.productId,
                    productType: session.productType,
                    checkDownloadStatus: true
                }
            }
        }
        else {
            action = LiveUtil.getLiveSessionDetailActionFromLiveClassId(session.productId, session.productId, "diy_pack_widget", session.format, userContext, session.title)
        }
        return LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisaiton, isLocked, action, isUserEligibleForTrial, "DIY_FITNESS_PACK", "diy_page_session_item", bucketId, blockInternationalUser, session.format)
    }

    protected getDownloadAllSessionWidget(pack: DIYPack, sessionMap: { [productId: string]: Product }, userContext?: UserContext, isInternalUser?: Boolean): DiyDownloadAllSessionWidget {
        const diyContentDetails: DiyContentDetail[] = _.compact(pack.sessionIds.map(sessionId => {
            const session = <DIYProduct>sessionMap[sessionId]
            if (!session) return undefined
            return AtlasUtil.getContentDetailV2(session)
        }))

        let downloadAllSessionWidget: DiyDownloadAllSessionWidget = {
            widgetType: "DIY_DOWNLOAD_ALL_SESSIONS_WIDGET",
            title: "Download and play",
            subTitle: "", // TODO: Add file size impl
            diyContentDetails: diyContentDetails,
        }
        if (AppUtil.diyPackRedesignSupported(userContext, isInternalUser)) {
            downloadAllSessionWidget = {
                ...downloadAllSessionWidget,
                childOfImageOverlayCard: true,
                hasDividerAbove: true,
                title: "Download now",
                subTitle: "Available offline",
            }
        }
        return downloadAllSessionWidget
    }

    protected getIntroductionVideoWidget(packInfo: DIYFitnessPack | DIYMeditationPack, userContext?: UserContext, isInternalUser?: Boolean) {
        const diyPackRedesignSupported = AppUtil.diyPackRedesignSupported(userContext, isInternalUser)
        const manageOptions: ManageOptions = {
            displayText: diyPackRedesignSupported ? "Pack introduction" : "Watch pack introduction",
            icon: "PLAY",
            options: [{
                isEnabled: true,
                displayText: diyPackRedesignSupported ? "Pack introduction" : "Watch pack introduction",
                action: ActionUtil.videoUrl("curefit-content/video/" + packInfo.packIntroContentId + ".mp4", UrlPathBuilder.prefixSlash(packInfo.imageDetails.heroImage)) + `&contentId=${packInfo.packIntroContentId}`,
                type: "PACK_INTRO"
            }],
        }
        if (diyPackRedesignSupported)
            return new ManageOptionsWidget(manageOptions, {}, undefined, true)
        else
            return new ManageOptionsWidget(manageOptions, {})
    }

    protected async getPackHeaderDescriptionWidget(packInfo: DIYFitnessPack | DIYMeditationPack): Promise<IBaseWidget> {
        return await new TitleDescriptionListWidget(packInfo.multiLevelDescription, 18, 14, true).buildView()
    }

    protected _getUnsubscribedActions(pack: DIYPack, startTitle: string, scheduleTitle: string, diyProduct: DIYProduct, content: DiyContentDetail, meta: DiyContentMeta, packFulfilment: DIYPackFulfilment, blockInternationalUser: boolean, isLocked?: boolean, isUserEligibleForTrial?: boolean, userContext?: UserContext, isUserEligibleForMonetisation?: boolean, bucketId?: string): { action: ActionCard, actions: (Action | PackSubscribeAction)[] } {
        const playerAction = {
            actionType: "PLAY_VIDEO",
            content: content,
            title: startTitle,
            meta: {
                content: content,
                queryParams: meta,
                title: startTitle,
                packId: pack.productId,
                productType: meta.activityType,
                checkDownloadStatus: true
            }
        }

        const contentDetail = AtlasUtil.getContentDetailV2(diyProduct)
        const contentMeta = AtlasUtil.getContentMetaV2(diyProduct, pack)

        const restartPackAction = {
            "title": "Start Again",
            "actionType": "PACK_SUBSCRIBE",
            "packId": pack.productId,
            url: ActionUtil.diyVideoUrl(diyProduct, contentDetail.URL, UrlPathBuilder.prefixSlash(pack.imageDetails.heroImage), contentMeta),
        } as Action

        const navigationAction = {
            "title": scheduleTitle,
            "url": ActionUtil.diySchedule(pack.productId, pack.productType === "DIY_FITNESS_PACK" ? "DIY_FITNESS" : "DIY_MEDITATION"),
            "actionType": "NAVIGATION",
        }
        const packSubscribeAction = {
            "title": "Get pack",
            "action": "PACK_SUBSCRIBE"
        }

        const playAction = LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, isLocked, playerAction, isUserEligibleForTrial, pack.productType, "diy_page_action", bucketId, blockInternationalUser, diyProduct.format)
        const navAction = LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, isLocked, navigationAction, isUserEligibleForTrial, pack.productType, "diy_page_action", bucketId, blockInternationalUser, diyProduct.format)

        let actions: Action[] = []
        if (pack.isCollection) {
            if (packFulfilment?.status === "SUBSCRIBED") {
                actions = [playAction]
            }
        } else if (userContext && AppUtil.isInternationalTLApp(userContext)) {
            playAction.isPrimaryButton = true
            navAction.isPrimaryButton = false
            actions = [navAction, playAction]
        } else if (userContext && AppUtil.isSugarFitOrUltraFitApp(userContext)) {
            if (packFulfilment?.status === "COMPLETED") {
                actions = [restartPackAction]
            } else {
                actions = [playAction]
            }
        } else {
            if (packFulfilment?.status === "COMPLETED") {
                actions = [restartPackAction, navAction]
            } else {
                actions = [playAction, navAction]
            }
        }

        return {
            action: LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, isLocked, packSubscribeAction, isUserEligibleForTrial, pack.productType, "diy_page_action", bucketId, blockInternationalUser, diyProduct.format),
            actions
        }
    }

    private getPageActions(pack: DIYPack, packFulfilment: DIYPackFulfilment): { action: ActionCard, actions: (Action | PackSubscribeAction)[] } {
        if (!packFulfilment) {
            return {
                action: {
                    "title": "Get pack",
                    "action": "PACK_SUBSCRIBE"
                },
                actions: [{
                    "packId": pack.productId,
                    "title": "Get pack",
                    "url": "curefit://orderconfirmation",
                    "actionType": "PACK_SUBSCRIBE"
                }]
            }
        }
        switch (packFulfilment.status) {
            case "SUBSCRIBED":
                return { action: undefined, actions: undefined }
            case "NOT_SUBCSCRIBED":
            case "ABORTED":
                return {
                    action: {
                        "title": "Get pack",
                        "action": "PACK_SUBSCRIBE"
                    },
                    actions: [{
                        "packId": pack.productId,
                        "title": "Get pack",
                        "url": "curefit://orderconfirmation",
                        "actionType": "PACK_SUBSCRIBE"
                    }]
                }
            case "COMPLETED":
                return {
                    action: {
                        "title": "Subscribe again",
                        "action": "PACK_SUBSCRIBE"
                    },
                    actions: [{
                        "packId": pack.productId,
                        "title": "Subscribe again",
                        "url": "curefit://orderconfirmation",
                        "actionType": "PACK_SUBSCRIBE"
                    }]
                }
        }
        return undefined
    }

    protected getDescriptionWidget(description: string): DescriptionWidget {
        const descriptions: InfoCard[] = []
        descriptions.push({
            title: "About this pack",
            subTitle: description,
        })
        const descriptionWidget: DescriptionWidget = new DescriptionWidget(descriptions)
        descriptionWidget.iconType = "INFO"
        return descriptionWidget
    }

    protected getSeoVideoSchemaWidget(pack: DIYPack): StructuredSchemaVideoWidget {
        const { title, subTitle, imageDetails, createdDate } = pack,
            thumbnailUrl = ["https://cdn-images.cure.fit/www-curefit-com/image/upload" + UrlPathBuilder.prefixSlash(imageDetails.heroImage)],
            videoUrl = CdnUtil.getCdnUrl("curefit-content/video/" + pack.packIntroContentId + ".mp4")

        return new StructuredSchemaVideoWidget(title, subTitle, thumbnailUrl, new Date(createdDate).toISOString(), videoUrl)
    }

    protected getSummaryWidget(pack: DIYPack, packFulfilment: DIYPackFulfilment, userAgent: UserAgent, userContext: UserContext, source?: SummaryWidgetSource, diyEnergyMeterSupportedMap?: IdVsValueMap<boolean>): DiyPackSummaryWidget {
        const summaryWidget: DiyPackSummaryWidget = new DiyPackSummaryWidget()
        summaryWidget.title = pack.title
        summaryWidget.packId = pack.productId
        summaryWidget.productType = pack.productType === "DIY_FITNESS_PACK" ? "DIY_FITNESS" : "DIY_MEDITATION"
        const category: ImageCategory = userAgent === "DESKTOP" ? "MAGAZINE" : "HERO"
        summaryWidget.description = pack.subTitle
        const totalSessions = pack.sessionIds.length > 0 ? pack.sessionIds.length : pack.sessions.length

        const subTitle = `${pad(totalSessions, 2)} ${pluralizeStringIfRequired(pack.metadata && pack.metadata.isLiveClassPack ? "Class" : "Session", totalSessions, pack.metadata && pack.metadata.isLiveClassPack ? "es" : "s")}`
        if (LivePackUtil.canShowPackLevelEnergyIcon(diyEnergyMeterSupportedMap) && source === "IMAGE_OVERLAY_WIDGET" && summaryWidget.productType === "DIY_FITNESS") {
            summaryWidget.imagesInTextProps = this.getImagesInTextProps(subTitle, "IMAGE_OVERLAY_WIDGET")
        } else {
            summaryWidget.subTitle = subTitle
        }
        summaryWidget.content = {
            image: UrlPathBuilder.prefixSlash((userAgent === "APP" ? pack.imageDetails.heroImage : pack.imageDetails.heroImage)),
            video: pack.packIntroContentId && ActionUtil.videoUrl("curefit-content/video/" + pack.packIntroContentId + ".mp4", UrlPathBuilder.prefixSlash(pack.imageDetails.heroImage)) + `&contentId=${pack.packIntroContentId}`,
        }

        if (packFulfilment && packFulfilment.status === "SUBSCRIBED") {
            summaryWidget.packProgress = {
                total: totalSessions,
                completed: packFulfilment.completedProductIds ? packFulfilment.completedProductIds.length : 0,
                state: packFulfilment.status,
                type: summaryWidget.productType
            }
            summaryWidget.meta = {
                packId: pack.productId,
                productType: summaryWidget.productType
            }
            if (!pack.isCollection) {
                summaryWidget.manageOptions = {
                    displayText: "Manage",
                    options: []
                }
                summaryWidget.manageOptions.options.push(this.getManageOptions())
            }
        }
        return summaryWidget
    }

    protected getManageOptions(): ManageOptionPayload {
        return {
            isEnabled: true,
            displayText: "Unsubscribe pack",
            type: "PACK_UN_SUBSCRIBE"
        }
    }

    protected getBenefitsWidget(benefits: string[]): ProductListWidget {
        const header: Header = {
            title: "Benefits"
        }

        const infoCards: InfoCard[] = []
        benefits.forEach(benefit => {
            infoCards.push({
                subTitle: benefit
            })
        })
        return new ProductListWidget("SMALL", header, infoCards)
    }

    protected getHowItWorksWidget(pageConfig: MindDIYPackPageConfig | CultDIYPackPageConfig): ProductListWidget {

        const header: Header = {
            title: pageConfig.howItWorksTitle
        }

        const infoCards: InfoCard[] = []
        pageConfig.howItWorksItemList.forEach(item => {
            infoCards.push({
                subTitle: item.text,
                icon: item.icon
            })
        })
        return new ProductListWidget("SMALL", header, infoCards)

    }

    private getImagesInTextProps(subTitle: string, source: ImageInTextSource): ImagesInText {
        return {
            textStyle: source === "IMAGE_OVERLAY_WIDGET" ? {
                fontSize: 16,
                fontFamily: "BrandonText-Regular",
            } : undefined,
            containerStyle: source === "PRODUCT_LIST_WIDGET_CELL" ? {
                marginLeft: 20,
                marginTop: "auto",
            } : undefined,
            data: [
                { text: `${subTitle} | ` },
                { iconType: "ENERGY", style: { height: 16 } },
                // { text: " Energy Meter" },
            ],
        }
    }

    protected abstract getActivityScreenURL(packId: string, activityId: string): string
}

export default ContentPackDetailViewV2

export class FitnessDIYPackDetailViewV2 extends ContentPackDetailViewV2 {
    constructor(userAgent: UserAgent, appVersion: number, userPackInfo: DIYUserFitnessPack, pageConfig: CultDIYPackPageConfig, sessionMap: { [productId: string]: Product }, breadCrumbs: IBreadCrumb[], blockInternationalUser: boolean, daysRemainingWidget?: DaysRemainingWidget,
        userContext?: UserContext, isUserEligibleForTrial?: boolean, isUserEligibleForMonetisation?: boolean, userHasActiveMembership?: { start: number, end: number } | undefined, bucketId?: string) {
        super()
        this.preparePage(userAgent, appVersion, userPackInfo, pageConfig, sessionMap, blockInternationalUser, breadCrumbs, daysRemainingWidget, userContext, undefined, undefined, undefined, isUserEligibleForTrial, isUserEligibleForMonetisation, userHasActiveMembership, bucketId)
    }

    protected getActivityScreenURL(packId: string, activityId: string): string {
        return ActionUtil.cultDiySingle(packId, activityId, "DIY_FITNESS")
    }
}

export class MindDIYPackDetailViewV2 extends ContentPackDetailViewV2 {
    constructor(userAgent: UserAgent, appVersion: number, userPackInfo: DIYUserMeditationPack, pageConfig: MindDIYPackPageConfig, sessionMap: { [productId: string]: Product }, breadCrumbs: IBreadCrumb[], daysRemainingWidget?: DaysRemainingWidget,
        userContext?: UserContext, isUserEligibleForTrial?: boolean, isUserEligibleForMonetisation?: boolean, userHasActiveMembership?: { start: number, end: number } | undefined) {
        super()
        this.preparePage(userAgent, appVersion, userPackInfo, pageConfig, sessionMap, false, breadCrumbs, daysRemainingWidget, userContext, undefined, undefined, undefined, isUserEligibleForTrial, isUserEligibleForMonetisation, userHasActiveMembership)
    }
    protected getActivityScreenURL(packId: string, activityId: string): string {
        return ActionUtil.mindDiySingle(packId, activityId, "DIY_MEDITATION")
    }
}

export abstract class ContentPackDetailViewV3 extends ContentPackDetailViewV2 {

    protected async preparePage(userAgent: UserAgent, appVersion: number, userPackInfo: (DIYUserFitnessPack | DIYUserMeditationPack), pageConfig: MindDIYPackPageConfig | CultDIYPackPageConfig,
        sessionMap: { [productId: string]: Product }, blockInternationalUser: boolean, breadCrumbs?: IBreadCrumb[], daysRemainingWidget?: DaysRemainingWidget, userContext?: UserContext, isInternalUser?: Boolean, packRecommendedByBuddies?: CultBuddiesJoiningListSmallView,
        sessionRecommendedByBuddiesMap?: { [sessionId: string]: CultBuddiesJoiningListSmallView }, isUserEligibleForTrial?: boolean, isUserElibgibleForMonetisation?: boolean, userHasActiveMembership?: { start: number, end: number } | undefined, bucketId?: string,
        diyEnergyMeterSupportedMap?: IdVsValueMap<boolean>, sessionsV2?: FitnessProgramProductResponse[]) {
        const pack = userPackInfo.pack
        const packFulfilment = userPackInfo.fulfilment
        const isWeb = AppUtil.isWeb(userContext)
        const isOldDIYConstruct = pack.sessionIds?.length > 0

        if (isWeb) {
            this.breadCrumbs = breadCrumbs ? breadCrumbs : []
        }
        if (isWeb && daysRemainingWidget) {
            this.widgets.push(daysRemainingWidget)
        }
        if (AppUtil.diyPackRedesignSupported(userContext, isInternalUser)) {
            const imageOverlayData = await this.buildImageOverlayData(pack, packFulfilment, userAgent, sessionMap, userContext, isInternalUser, packRecommendedByBuddies, userHasActiveMembership, diyEnergyMeterSupportedMap)
            this.widgets.push(imageOverlayData)
        }
        else {
            isWeb && this.widgets.push(this.getSummaryWidget(pack, packFulfilment, userAgent, userContext))

            if (isWeb && packFulfilment && packFulfilment.status === "SUBSCRIBED" && isOldDIYConstruct) {
                const totalSessions = pack.sessionIds.length
                const sessionCompleted = packFulfilment.next

                if (sessionCompleted !== totalSessions) {
                    const session = <DIYProduct>sessionMap[pack.sessionIds[sessionCompleted]]
                    this.widgets.push(this.getUpNextWidget(session, pack, packFulfilment, userContext, blockInternationalUser, isUserEligibleForTrial, isUserElibgibleForMonetisation, bucketId))
                }
            }
        }
        this.widgets.push(this.getSessionsWidget(sessionMap, pack, packFulfilment, appVersion, userAgent, blockInternationalUser, userContext, isInternalUser, sessionRecommendedByBuddiesMap, isUserEligibleForTrial, isUserElibgibleForMonetisation, bucketId, diyEnergyMeterSupportedMap, sessionsV2))
        this.widgets.push(this.getDescriptionWidget(pack.subTitle))
        if (!_.isEmpty(pack.benefits)) {
            this.widgets.push(this.getBenefitsWidget(pack.benefits))
        }
        if (!AppUtil.isWeb(userContext)) {
            let pageAction = undefined
            if (isOldDIYConstruct) {
                pageAction = await this.getPageActionsV2(pack, packFulfilment, sessionMap, isUserEligibleForTrial, userContext, isUserElibgibleForMonetisation, bucketId, blockInternationalUser)
            }
            if ((!packFulfilment || packFulfilment.status !== "SUBSCRIBED") && !AppUtil.isSugarFitOrUltraFitApp(userContext) && (pageAction?.actions?.length > 0)) {
                this.widgets.push(this.getHowItWorksWidget(pageConfig))
            }
            if (!_.isEmpty(pack.linkedResources) && this.getResourcesWidget(pack.linkedResources)) {
                this.widgets.push(this.getResourcesWidget(pack.linkedResources))
            }
            this.pageAction = pageAction?.action
            this.actions = pageAction?.actions
        }
        if (AppUtil.isWeb(userContext)) {
            if (!_.isEmpty(pack.multiLevelDescription)) {
                this.widgets.push(await this.getPackHeaderDescriptionWidget(pack))
            }

            this.widgets.push(this.getSeoVideoSchemaWidget(pack))
        }
    }

    protected getDayTimePickerWidget(pack: DIYPack, packFulfilment: DIYPackFulfilment, userContext?: UserContext, isInternalUser?: Boolean): DayTimePickerWidget {
        const dayTimePickerWidget: DayTimePickerWidget = new DayTimePickerWidget()
        dayTimePickerWidget.productId = pack.productId
        const preferredDays = packFulfilment.preferredDays
        dayTimePickerWidget.preferredDays = preferredDays
        const preferredTime = packFulfilment.preferredTime
        dayTimePickerWidget.preferredTime = preferredTime
        if (preferredTime === undefined) {
            dayTimePickerWidget.errorString = pack.productType === "DIY_FITNESS_PACK" ? "Please select a workout slot" : "Please select a meditation slot"
        }
        if (AppUtil.diyPackRedesignSupported(userContext, isInternalUser)) {
            dayTimePickerWidget.hasDividerAbove = true
            dayTimePickerWidget.childOfImageOverlayCard = true
        }
        return dayTimePickerWidget
    }

    protected getDayPickerWidget(pack: DIYPack, preferredDays: number[]): DayPickerWidget {
        const dayPickerWidget: DayPickerWidget = new DayPickerWidget()
        dayPickerWidget.productId = pack.productId
        dayPickerWidget.preferredDays = preferredDays
        dayPickerWidget.productType = pack.productType === "DIY_FITNESS_PACK" ? "DIY_FITNESS" : "DIY_MEDITATION"
        return dayPickerWidget
    }

    protected getTimeSlotPickerWidget(userContext: UserContext, pack: DIYPack, preferredTime: HourMin): TimePickerWidget {
        const timePickerWidget: TimePickerWidget = new TimePickerWidget()
        timePickerWidget.preferredTime = preferredTime
        const tz = userContext.userProfile.timezone
        if (preferredTime != undefined) {
            timePickerWidget.title = TimeUtil.getTimeIn12HRFormat(TimeUtil.formatDateInTimeZone(tz, new Date()), preferredTime.hour, preferredTime.min, true, tz)
        } else {
            timePickerWidget.errorString = pack.productType === "DIY_FITNESS_PACK" ? "Please select a workout slot" : "Please select a meditation slot"
        }
        timePickerWidget.packId = pack.productId
        return timePickerWidget
    }

    protected getReminderWidget(pack: DIYPack, reminder: boolean, preferredTime: HourMin, userContext?: UserContext, isInternalUser?: Boolean): ReminderWidget {
        const reminderWidget: ReminderWidget = new ReminderWidget()
        reminderWidget.title = "Reminders: " + ((reminder === true) ? "on" : "off")
        reminderWidget.toggleState = (reminder === true)
        reminderWidget.subTitle = "Notifies you 15 min before session"
        reminderWidget.action = { actionType: "REMINDER_SWITCH_ACTION" }
        reminderWidget.packId = pack.productId
        reminderWidget.disabled = (preferredTime === undefined)
        reminderWidget.childOfImageOverlayCard = AppUtil.diyPackRedesignSupported(userContext, isInternalUser) ? true : undefined
        return reminderWidget
    }

    protected getManageOptions(): ManageOptionPayload {
        return {
            isEnabled: true,
            displayText: "Remove Series",
            type: "PACK_UN_SUBSCRIBE"
        }
    }

    protected getPageActionsV2(pack: DIYPack, packFulfilment: DIYPackFulfilment, sessionMap: { [productId: string]: Product }, isUserEligibleForTrial: boolean, userContext: UserContext, isUserEligibleForMonetisation: boolean, bucketId: string, blockInternationalUser: boolean): { action: ActionCard, actions: (Action | PackSubscribeAction)[] } {
        const nextSessionIdx = LiveUtil.getSessionMapAwareNextDIYSessionIdx(pack, packFulfilment, sessionMap)
        const nextSession = <DIYProduct>sessionMap[pack.sessionIds[nextSessionIdx]]
        const isLocked = nextSession.locked && isUserEligibleForMonetisation
        const content = AtlasUtil.getContentDetailV2(nextSession)
        const meta = AtlasUtil.getContentMetaV2(nextSession, pack)
        const isInternationalTLApp = AppUtil.isInternationalTLApp(userContext)
        const startTitle = isInternationalTLApp ? "BEGIN" : "Start Now"
        const startNextTitle = isInternationalTLApp ? "RESUME" : "Start Next Session"
        const scheduleText = isInternationalTLApp ? "SCHEDULE" : "Schedule"
        const isCollection = !_.isEmpty(pack.isCollection) && pack.isCollection === true
        if (isCollection) {
            if (packFulfilment && packFulfilment.status === "COMPLETED") {
                return undefined
            }
        }
        if (!packFulfilment) {
            return this._getUnsubscribedActions(pack, startTitle, scheduleText, nextSession, content, meta, packFulfilment, isLocked, isUserEligibleForTrial, blockInternationalUser, userContext, isUserEligibleForMonetisation, bucketId)
        }
        switch (packFulfilment.status) {
            case "SUBSCRIBED":
                const playAction = {
                    actionType: "PLAY_VIDEO",
                    content: content,
                    title: startNextTitle,
                    meta: {
                        content: content,
                        queryParams: meta,
                        title: nextSession.title,
                        packId: pack.productId,
                        productType: nextSession.productType,
                        checkDownloadStatus: true
                    }
                }
                return {
                    action: undefined, actions: [LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, isLocked, playAction, isUserEligibleForTrial, nextSession.productType, "diy_page_action", bucketId, blockInternationalUser, nextSession.format)]
                }
            case "NOT_SUBCSCRIBED":
            case "ABORTED":
                return this._getUnsubscribedActions(pack, "Start Sessions", "Schedule", nextSession, content, meta, packFulfilment, isLocked, isUserEligibleForTrial, blockInternationalUser, userContext, isUserEligibleForMonetisation, bucketId)
            case "COMPLETED":
                return this._getUnsubscribedActions(pack, "Start Again", "Schedule Again", nextSession, content, meta, packFulfilment, isLocked, isUserEligibleForTrial, blockInternationalUser, userContext, isUserEligibleForMonetisation, bucketId)
        }
        return undefined
    }

    protected getSessionsWidget(sessionMap: { [productId: string]: Product }, pack: DIYPack, packFulfilment: DIYPackFulfilment, appVersion: number, userAgent: UserAgent, blockInternationalUser: boolean, userContext?: UserContext, isInternalUser?: Boolean,
        sessionRecommendedByBuddiesMap?: { [sessionId: string]: CultBuddiesJoiningListSmallView }, isUserEligibleForTrial?: boolean, isUserEligibleForMonetisation?: boolean, bucketId?: string, diyEnergyMeterSupportedMap?: IdVsValueMap<boolean>, sessionsV2?: FitnessProgramProductResponse[]): ProductListWidget {
        const header: Header = {
            title: pack.metadata && pack.metadata.isLiveClassPack ? "Classes in this pack" : "Sessions in this pack"
        }
        let actionCards
        if (pack.sessionIds?.length > 0) {
            actionCards = this.getSessionsWidgetItems(sessionMap, pack, packFulfilment, appVersion, blockInternationalUser, userContext, sessionRecommendedByBuddiesMap, isUserEligibleForTrial, isUserEligibleForMonetisation, bucketId, diyEnergyMeterSupportedMap)
        }
        else {
            actionCards = this.getSessionsWidgetItemsV2(sessionsV2, pack, packFulfilment, appVersion, blockInternationalUser, userContext, sessionRecommendedByBuddiesMap, isUserEligibleForTrial, isUserEligibleForMonetisation, bucketId, diyEnergyMeterSupportedMap)
        }
        let LIST_SIZE: ListSubType = "MEDIUM"
        if (AppUtil.isWeb(userContext)) {
            LIST_SIZE = "LARGE"
        }
        const productListWidget: ProductListWidget = new ProductListWidget(LIST_SIZE, header, actionCards)
        productListWidget.hideSepratorLines = true
        return productListWidget
    }

    protected async buildImageOverlayData(pack: DIYPack, packFulfilment: DIYPackFulfilment, userAgent: UserAgent, sessionMap: { [productId: string]: Product }, userContext?: UserContext, isInternalUser?: Boolean,
        packRecommendedByBuddies?: CultBuddiesJoiningListSmallView, userHasActiveMembership?: { start: number, end: number } | undefined, diyEnergyMeterSupportedMap?: IdVsValueMap<boolean>): Promise<ImageOverlayCardContainerWidget> {
        const ImageOverlayCard: ImageOverlayCardContainerWidget = new ImageOverlayCardContainerWidget()
        let fetchDownloadWidget = true
        ImageOverlayCard.widgetType = "IMAGE_OVERLAY_CARD_CONTAINER_WIDGET"
        ImageOverlayCard.widgets = []
        ImageOverlayCard.cardContainerStyle = {
            paddingHorizontal: 15,
            paddingVertical: 15
        }
        const tagWidget = this.getWorkoutTag(pack)
        if (tagWidget !== null)
            ImageOverlayCard.widgets.push(tagWidget)
        const summaryWidget = this.getSummaryWidget(pack, packFulfilment, userAgent, userContext, "IMAGE_OVERLAY_WIDGET", diyEnergyMeterSupportedMap)
        summaryWidget.childOfImageOverlayCard = true
        ImageOverlayCard.widgets.push(summaryWidget)
        const isSugarFitOrUltraFitApp = AppUtil.isSugarFitOrUltraFitApp(userContext)
        if (packRecommendedByBuddies && !_.isEmpty(packRecommendedByBuddies.buddies)) {
            ImageOverlayCard.widgets.push({ widgetType: "CULT_BUDDIES_JOINING_LIST_SMALL_WIDGET", ...packRecommendedByBuddies })
        }

        if ((!packFulfilment || packFulfilment.status !== "SUBSCRIBED") && pack.packIntroContentId)
            ImageOverlayCard.widgets.push(this.getIntroductionVideoWidget(pack, userContext, isInternalUser))

        if ((pack && pack.metadata && pack.metadata.isLiveClassPack === true) || pack.isCollection || isSugarFitOrUltraFitApp)
            fetchDownloadWidget = false
        if (userHasActiveMembership && fetchDownloadWidget && pack.sessionIds?.length > 0 )
            ImageOverlayCard.widgets.push(this.getDownloadAllSessionWidget(pack, sessionMap, userContext, isInternalUser))

        if (packFulfilment && packFulfilment.status === "SUBSCRIBED" && !pack.isCollection && !isSugarFitOrUltraFitApp) {
            ImageOverlayCard.widgets.push(this.getDayTimePickerWidget(pack, packFulfilment, userContext, isInternalUser))
            ImageOverlayCard.widgets.push(this.getReminderWidget(pack, packFulfilment.reminder, packFulfilment.preferredTime, userContext, isInternalUser))
        }
        ImageOverlayCard.assets = []
        ImageOverlayCard.assets.push({
            assetType: "IMAGE",
            assetUrl: UrlPathBuilder.prefixSlash(pack.imageDetails.heroImage),
        })
        return ImageOverlayCard
    }

    protected getWorkoutTag(pack: DIYPack): CultWorkoutTagsWidget {
        if (pack.gmsTags && pack.gmsTags.length > 0) {
            const tags: any = []
            tags.push({
                text: pack.gmsTags[0].value.toUpperCase(),
                bgColour: "#5bdbb6",
                tagStyle: { borderRadius: 3, fontFamily: "BrandonText-Bold", fontSize: 12 }
            })

            return {
                widgetType: "CULT_WORKOUT_TAGS_WIDGET",
                tags: tags,
                containerStyle: { paddingTop: 0 }
            }
        }
        else {
            return null
        }
    }

    private getResourcesWidget(resources: AdditionalResource[]): ListWidgetV2 {
        const items: ListItem[] = resources.filter((resource) => {
            if (resource.resourceType === "MEAL_PLAN") {
                return false
            }
            return true
        }).map((resource) => {
            return {
                title: resource.title,
                subTitle: "",
                icon: resource.image,
                seeMore: resource.url ? {
                    title: "VIEW",
                    url: resource.url
                } as Action : undefined,
                sceneStyle: {paddingTop: 10}
            }
        })
        if (items && items.length > 0) {
            return {
                widgetType: "LIST_WIDGET_V2",
                header: {title: "Resources", titleStyle: {marginTop: 20}},
                items
            }
        }
        return null
    }

}

export class FitnessDIYPackDetailViewV3 extends ContentPackDetailViewV3 {
    preparePagePromise: Promise<void>
    constructor(userAgent: UserAgent, appVersion: number, userPackInfo: DIYUserFitnessPack, pageConfig: CultDIYPackPageConfig, sessionMap: { [productId: string]: Product }, packRecommendedByBuddies: CultBuddiesJoiningListSmallView,
        sessionRecommendedByBuddiesMap: { [sessionId: string]: CultBuddiesJoiningListSmallView }, breadCrumbs: IBreadCrumb[], blockInternationalUser: boolean, daysRemainingWidget?: DaysRemainingWidget, userContext?: UserContext, isInternalUser?: Boolean, isUserEligibleForTrial?: boolean,
        isUserEligibleForMonetisaiton?: boolean, userHasActiveMembership?: { start: number, end: number } | undefined, bucketId?: string, diyEnergyMeterSupportedMap?: IdVsValueMap<boolean>, sessionsV2?: FitnessProgramProductResponse[] ) {
        super()
        this.preparePagePromise = this.preparePage(userAgent, appVersion, userPackInfo, pageConfig, sessionMap, blockInternationalUser, breadCrumbs, daysRemainingWidget, userContext, isInternalUser, packRecommendedByBuddies, sessionRecommendedByBuddiesMap, isUserEligibleForTrial, isUserEligibleForMonetisaiton, userHasActiveMembership, bucketId, diyEnergyMeterSupportedMap, sessionsV2)
    }

    protected getActivityScreenURL(packId: string, activityId: string): string {
        return ActionUtil.cultDiySingle(packId, activityId, "DIY_FITNESS")
    }
}

export class MindDIYPackDetailViewV3 extends ContentPackDetailViewV3 {
    constructor(userAgent: UserAgent, appVersion: number, userPackInfo: DIYUserMeditationPack, pageConfig: MindDIYPackPageConfig, sessionMap: { [productId: string]: Product },
        packRecommendedByBuddies: CultBuddiesJoiningListSmallView, sessionRecommendedByBuddiesMap: { [sessionId: string]: CultBuddiesJoiningListSmallView }, breadcrumbs: IBreadCrumb[], daysRemainingWidget?: DaysRemainingWidget, userContext?: UserContext, isInternalUser?: Boolean, isUserEligibleForTrial?: boolean, isUserEligibleForMonetisaiton?: boolean, userHasActiveMembership?: { start: number, end: number } | undefined) {
        super()
        this.preparePage(userAgent, appVersion, userPackInfo, pageConfig, sessionMap, false, breadcrumbs, daysRemainingWidget, userContext, isInternalUser, packRecommendedByBuddies, sessionRecommendedByBuddiesMap, isUserEligibleForTrial, isUserEligibleForMonetisaiton, userHasActiveMembership)
    }
    protected getActivityScreenURL(packId: string, activityId: string): string {
        return ActionUtil.mindDiySingle(packId, activityId, "DIY_MEDITATION")
    }
}

export class TLDIYPackDetailView extends ContentPackDetailViewV3 {
    constructor(userAgent: UserAgent, appVersion: number, userPackInfo: DIYUserFitnessPack | DIYUserMeditationPack, pageConfig: CultDIYPackPageConfig, sessionMap: { [productId: string]: Product }, packRecommendedByBuddies: CultBuddiesJoiningListSmallView,
        sessionRecommendedByBuddiesMap: { [sessionId: string]: CultBuddiesJoiningListSmallView }, breadCrumbs: IBreadCrumb[], daysRemainingWidget?: DaysRemainingWidget, userContext?: UserContext, isInternalUser?: Boolean, isUserEligibleForTrial?: boolean,
        isUserEligibleForMonetisaiton?: boolean, userHasActiveMembership?: { start: number, end: number } | undefined, bucketId?: string, diyEnergyMeterSupportedMap?: IdVsValueMap<boolean>, packDetail?: DIYFitnessPackExtended | DIYMeditationPackExtended) {
        super()
        this.preparePage(userAgent, appVersion, userPackInfo, pageConfig, sessionMap, false, breadCrumbs, daysRemainingWidget, userContext, isInternalUser, packRecommendedByBuddies, sessionRecommendedByBuddiesMap, isUserEligibleForTrial, isUserEligibleForMonetisaiton, userHasActiveMembership, bucketId, diyEnergyMeterSupportedMap, null, packDetail)
    }

    private createDIYPackDetailView(pack: DIYFitnessPack, packDetail: DIYFitnessPackExtended | DIYMeditationPackExtended): TLDIYPackDetailsWidget {
        const trainerInfos = [...(packDetail.derivedInfo?.primaryTrainersInfo || []), ...(packDetail.derivedInfo?.secondaryTrainersInfo || [])]
        const trainerLinks = trainerInfos.map(trainerInfo => ({
            id: trainerInfo.instructorId,
            name: trainerInfo.name,
            image: trainerInfo.profileImageUrl,
            action: AppActionUtil.trainerDetailAction(trainerInfo.instructorId)
        }))
        const shareAction = CultUtil.getDiyShareAction(packDetail.productId, null, packDetail.productType, false, packDetail.title)

        return {
            widgetType: "TL_COLLECTION_PACK_DETAILS_WIDGET",
            name: pack.title,
            trainerLinks: trainerLinks,
            subtitle: pack.subTitle,
            description: "",
            shareAction: shareAction,
            image: UrlPathBuilder.prefixSlash(pack.imageDetails.tlpHeaderImage),
            hasDividerBelow: false
        }
    }

    private getCollectionImage(userContext: UserContext, session: DIYProduct): string {
        if (AppUtil.isInternationalTLTVApp(userContext)) {
            return UrlPathBuilder.prefixSlash(session?.tlpHeaderImage || session.imageDetails.thumbnailImage)
        }
        return UrlPathBuilder.prefixSlash(session.imageDetails.thumbnailImage)
    }

    private getSubtitle(userContext: UserContext, description: string, duration: string): string[] {
        if (AppUtil.isInternationalTLTVApp(userContext)) {
            return [duration].filter(Boolean)
        }
        return [description, duration].filter(Boolean)
    }

    protected getSinglesWidget(sessionMap: { [productId: string]: Product }, pack: DIYPack, packFulfilment: DIYPackFulfilment, blockInternationalUser: boolean, userContext?: UserContext, isUserEligibleForTrial?: boolean, isUserEligibleForMonetisation?: boolean, bucketId?: string): TLCollectionSinglesWidget {
        const singleCards: TLCollectionSingle[] = pack.sessionIds.map(sessionId => {
            const session = <DIYProduct>sessionMap[sessionId]
            const duration = LiveUtil.getFormattedTimeString(typeof session.duration === "string" ? parseInt(session.duration) : session.duration)
            const isLocked = AppUtil.isLiveContentLocked(userContext, session.locked, isUserEligibleForMonetisation, isUserEligibleForTrial)
            const playAction = this.getSessionWidgetItemAction(session, pack, packFulfilment, isUserEligibleForTrial, isLocked, userContext, isUserEligibleForMonetisation, bucketId, blockInternationalUser)
            const description = session.productType === "DIY_FITNESS" ? this.getExercisesDescription(<DIYFitnessProductExtended>session) : undefined
            const trainerInfos = [...(session.derivedInfo?.primaryTrainersInfo || []), ...(session.derivedInfo?.secondaryTrainersInfo || [])]
            const trainerLinks = trainerInfos.map(trainerInfo => ({
                id: trainerInfo.instructorId,
                name: trainerInfo.name,
                image: trainerInfo.profileImageUrl,
                action: AppActionUtil.trainerDetailAction(trainerInfo.instructorId)
            }))
            return {
                name: session.title,
                trainerLinks,
                subtitles: this.getSubtitle(userContext, description, duration),
                playAction,
                shareAction: LiveUtil.getDIYShareAction(pack.productId, sessionId, session.productType, pack.title, session.title),
                image: this.getCollectionImage(userContext, session),
                progress: 0,
                isLocked
            }
        })
        return {
            widgetType: "TL_COLLECTION_SINGLES_WIDGET",
            items: singleCards
        }
    }

    private getManageActions(packId: string): Action {
        return {
            actionType: "ACTION_LIST",
            title: "Manage",
            viewType: "ICON",
            iconName: "dots",
            iconSize: 20,
            actions: [
                {
                    title: "Unsubscribe pack",
                    actionType: "REST_API",
                    meta: {
                        url: `/pack/fitnessDIY/v2/${packId}/unSubscribe`,
                        method: "POST",
                        body: {
                            requestSource: "PackDetailPage",
                        },
                    },
                    isEnabled: true,
                    refreshOnComplete: true
                },
            ],
        }
    }

    protected async preparePage(userAgent: UserAgent, appVersion: number, userPackInfo: (DIYUserFitnessPack | DIYUserMeditationPack), pageConfig: MindDIYPackPageConfig | CultDIYPackPageConfig,
        sessionMap: { [productId: string]: Product }, blockInternationalUser: boolean, breadCrumbs?: IBreadCrumb[], daysRemainingWidget?: DaysRemainingWidget, userContext?: UserContext, isInternalUser?: Boolean, packRecommendedByBuddies?: CultBuddiesJoiningListSmallView,
        sessionRecommendedByBuddiesMap?: { [sessionId: string]: CultBuddiesJoiningListSmallView }, isUserEligibleForTrial?: boolean, isUserElibgibleForMonetisation?: boolean, userHasActiveMembership?: { start: number, end: number } | undefined, bucketId?: string,
        diyEnergyMeterSupportedMap?: IdVsValueMap<boolean>, sessionsV2?: FitnessProgramProductResponse[], packDetail?: DIYFitnessPackExtended | DIYMeditationPackExtended) {
        const pack = userPackInfo.pack
        const packFulfilment = userPackInfo.fulfilment
        this.widgets.push(this.createDIYPackDetailView(pack, packDetail))
        this.widgets.push(this.getSinglesWidget(sessionMap, pack, packFulfilment, blockInternationalUser, userContext, isUserEligibleForTrial, isUserElibgibleForMonetisation, bucketId))
        const pageAction = await this.getPageActionsV2(pack, packFulfilment, sessionMap, isUserEligibleForTrial, userContext, isUserElibgibleForMonetisation, bucketId, blockInternationalUser)
        this.pageAction = pageAction.action
        this.actions = pageAction.actions
        if (packFulfilment?.status === "SUBSCRIBED") {
            this.actions.push(this.getManageActions(pack.productId))
        }
    }

    protected getActivityScreenURL(packId: string, activityId: string): string {
        return ActionUtil.cultDiySingle(packId, activityId, "DIY_FITNESS")
    }
}


export type TLCardListViewProps = {
    header: string,
    description?: string,
    headerImage?: string,
    userContext: UserContext,
    packs: (DIYFitnessPack | DIYMeditationPack)[],
    packDetails?: { [packId: string]: DIYFitnessPackExtended | DIYMeditationPackExtended },
    nextSessionMap?: Map<string, DIYProduct>
}

export class TLCardListView extends ProductDetailPage {
    constructor(tlCardListViewProps: TLCardListViewProps) {
        super()
        this.widgets.push(this.getCollectionGridWidget(tlCardListViewProps))
    }

    private getCollectionGridWidget({ userContext, header, description, headerImage, packs, packDetails = {}, nextSessionMap }: TLCardListViewProps): TLCollectionGridWidget {
        const cards: TLCollectionGridItem[] = packs.map(pack => {
            const productId = pack.productId
            const packDetail = packDetails?.[productId]
            const isLocked = nextSessionMap?.get(productId)?.locked
            const trainerInfos = [...(packDetail?.derivedInfo?.primaryTrainersInfo || []), ...(packDetail?.derivedInfo?.secondaryTrainersInfo || [])]
            const trainerLinks = trainerInfos.slice(0, 1).map(trainerInfo => ({
                id: trainerInfo.instructorId,
                name: trainerInfo.name,
                image: trainerInfo.profileImageUrl,
                action: AppActionUtil.trainerDetailAction(trainerInfo.instructorId)
            }))
            return {
                title: pack.title,
                image: UrlPathBuilder.prefixSlash(pack.imageDetails.tlpThumbnailImage),
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtilV1.diyPackProductPage(pack, userContext.sessionInfo.userAgent)
                },
                isLocked,
                trainerLinks,
                description: pack.description
            }
        })
        return {
            widgetType: "TL_COLLECTION_GRID_WIDGET",
            header,
            items: cards,
            numColumns: 2,
            aspectRatio: 1,
            description,
            headerImage
        }
    }
}
