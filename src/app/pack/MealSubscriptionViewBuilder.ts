import {
    Action,
    ActionCard,
    ActionList,
    getOffersWidget,
    Header,
    InfoCard,
    ManageOptionPayload,
    ManageOptions,
    ManageOptionsWidget,
    PackAction,
    PackDurationPickerWidget,
    PackDurationWidget,
    PackProgress,
    PickupWidget,
    ProductDetailPage,
    ProductListWidget,
    WidgetType,
    WidgetView
} from "../common/views/WidgetView"
import { FoodBooking, FoodPackBooking, SubFoodPackBooking } from "@curefit/shipment-common"
import { DeliveryChannel, DeliverySlot, FoodPack, FoodPackOption, MealSlot, FoodProduct as Product } from "@curefit/eat-common"
import { CustomerIssueType } from "@curefit/issue-common"
import { Session } from "@curefit/userinfo-common"
import { ProductPrice } from "@curefit/product-common"
import { SubscriptionType, UserAgentType } from "@curefit/base-common"
import { UserDeliveryAddress } from "@curefit/eat-common"
import { PackState } from "@curefit/eat-common"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { SlotUtil } from "@curefit/eat-util"
import EatUtils from "../util/EatUtil"
import EatUtil, { MAX_UPCOMING_MEALS, NonKioskStandingInstructionList } from "../util/EatUtil"
import { MealUtil, RENEWAL_WINDOW_DAYS, SeoUrlParams } from "@curefit/base-utils"
import { capitalizeFirstLetter } from "@curefit/util-common"
import { UrlPathBuilder } from "@curefit/product-common"
import * as _ from "lodash"
import { RUPEE_SYMBOL } from "@curefit/finance-common"
import MealPackPageConfig from "./MealPackPageConfig"
import { PreferredLocation } from "@curefit/userinfo-common"
import { OfferUtil } from "@curefit/base-utils"
import { inject, injectable } from "inversify"
import IProductBusiness from "../product/IProductBusiness"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import * as momentTz from "moment-timezone"
import { ActionUtil } from "@curefit/base-utils"
import { FoodPackOffersResponse, FoodSinglePriceOfferResponse, EatOfferRequestParams, FoodPackOffersResponseV2 } from "@curefit/offer-common"
import { IOfferServiceV2 } from "@curefit/offer-service-client"
import { UserContext } from "@curefit/userinfo-common"
import { ICatalogueService, CATALOG_CLIENT_TYPES } from "@curefit/catalog-client"
import { DataError } from "@curefit/base"
import { OFFER_SERVICE_CLIENT_TYPES } from "@curefit/offer-service-client"
import { BaseWidget, DailyMealWidget } from "@curefit/vm-models"
import { getWidgetByType } from "../page/vm/WidgetMap"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import AppUtil from "../util/AppUtil"
import { EatSubscriptionUtil } from "../util/EatSubscriptionUtil"
import { Action as VMAction } from "@curefit/vm-models"
import { ICapacityService, MASTERCHEF_CLIENT_TYPES } from "@curefit/masterchef-client"
import { OptInPermissionWidgetView } from "../page/vm/widgets/timeline/OptInPermissionWidgetView"
import { IUserService, USER_CLIENT_TYPES } from "@curefit/user-client"
import { WhatsappEnum } from "@curefit/user-common"

export class PackAddressWidget implements WidgetView {
    address: string
    isDisabled: boolean
    actions: (Action | ActionList)[]
    widgetType: WidgetType = "PACK_ADDRESS_WIDGET"
    title: string
    deliveryInstruction: string

    constructor(address: UserDeliveryAddress, meta: any, userContext: UserContext, isDisabled?: boolean) {
        let addressText = ""
        if (address && address.addressId !== "DUMMY") {
            if (address.addressLine1)
                addressText = address.addressLine1 + ","
            addressText += address.addressLine2
            this.deliveryInstruction = EatUtils.getStandingInstruction(address.eatDeliveryInstruction)
        }
        this.title = meta.isWeekendAddress ? "Weekends:" : "Weekdays:"
        const isKiosk = address.kioskId !== undefined
        this.isDisabled = isKiosk || isDisabled
        this.address = addressText.length > 0 ? capitalizeFirstLetter(address.addressType.toLowerCase()) + ": " + addressText : undefined

        this.actions = [{
            actionType: "CHANGE_PACK_ADDRESS",
            url: "curefit://selectaddress",
            meta: meta
        }]
        if (!isKiosk) {
            if (MealUtil.isCutleryChangeSupported(userContext)) {
                this.actions.push(EatUtil.getCutleryInstructionAction(address.eatDeliveryInstruction, meta, "UPDATE_PACK_INSTRUCTION"))
            } else {
                this.actions.push({
                    actionType: "ACTION_LIST",
                    actions: _.map(NonKioskStandingInstructionList, (instruction): Action => {
                        return {
                            actionType: "UPDATE_PACK_INSTRUCTION",
                            title: instruction.displayName,
                            meta: {
                                ...meta,
                                eatDeliveryInstruction: instruction.payload
                            }
                        }
                    })
                })
            }
        }
    }
}


@injectable()
class MealSubscriptionViewBuilder {
    constructor(@inject(CUREFIT_API_TYPES.ProductBusiness) private productBusiness: IProductBusiness,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) private offerService: IOfferServiceV2,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(MASTERCHEF_CLIENT_TYPES.CapacityService) protected capacityService: ICapacityService,
        @inject(USER_CLIENT_TYPES.IUserService) protected userService: IUserService,

    ) {

    }

    async getView(
        userContext: UserContext,
        mealPackPageConfig: MealPackPageConfig,
        preferredLocation: PreferredLocation,
        packInfo: FoodPack,
        issuesMap: Map<string, CustomerIssueType[]>,
        packBooking: FoodPackBooking,
        menu: { [date: string]: string },
        products: { [productId: string]: Product },
        subscriptionType?: SubscriptionType,
        packOffersResponse?: FoodPackOffersResponseV2,
        singleOffersResponse?: FoodSinglePriceOfferResponse,
        session?: Session,
        weeklyWeekendsEnabled?: boolean,
        monthlyWeekendsEnabled?: boolean,
        numTickets?: number,
        isSubPack?: boolean
    ): Promise<ProductDetailPage> {


        const productDetailPage: ProductDetailPage = new ProductDetailPage()
        const packBookingTz = packBooking.timezone
        let price
        let packOption: FoodPackOption
        let offerIds
        let packOfferAndPrice
        const availableAreaId: string = _.get(preferredLocation, "area") ? preferredLocation.area.areaId : preferredLocation.defaultArea.areaId
        monthlyWeekendsEnabled = packInfo.tags?.indexOf("WEEKENDS_TOGGLE_NOT_SUPPORTED") >= 0 ? true : monthlyWeekendsEnabled
        if (packBooking.packState === "NOT_BOUGHT") {
            const weekendsEnabled = subscriptionType === "MONTHLY" ? monthlyWeekendsEnabled : weeklyWeekendsEnabled
            if (subscriptionType === undefined) {
                packOption = MealUtil.findDefaultSubscriptionOption(packInfo, packOffersResponse, weeklyWeekendsEnabled, monthlyWeekendsEnabled)
                if (packOption) {
                    subscriptionType = packOption.subscriptionType
                    numTickets = packOption.numTickets
                } else {
                    throw new DataError("Subscription not found")
                }
            } else {
                packOption = packInfo.options.find(option => {
                    return (option.subscriptionType === subscriptionType) &&
                        (option.weekendsEnabled === weekendsEnabled) &&
                        (option.numTickets === Number(numTickets))
                })
            }
        } else {
            packOption = packInfo.status === "LIVE" ? packInfo.options.find(option => { return packBooking.subscriptionType ? option.subscriptionType === packBooking.subscriptionType && option.weekendsEnabled === packBooking.weekendsEnabled : option.numTickets === packBooking.schedule.ticketsTotal })
                : packInfo.options[0]
        }
        packOfferAndPrice = OfferUtil.getFoodPackOfferAndPrice(packInfo, packOption, packOffersResponse)
        offerIds = !_.isEmpty(packOfferAndPrice.offers) ? _.map(packOfferAndPrice.offers, offer => {
            return offer.offerId
        }) : undefined
        if (packBooking.packState === "NOT_BOUGHT") {
            price = packOfferAndPrice.price
            if (isSubPack) {
                productDetailPage.actions = []
            } else if (session.isNotLoggedIn) {
                productDetailPage.actions.push({
                    actionType: "SHOW_ALERT_MODAL",
                    title: "Subscribe",
                    meta: {
                        title: "Login Required!",
                        subTitle: "Please login to continue",
                        actions: [{ actionType: "LOGOUT", title: "Login" }]
                    }
                })
            } else {
                if (packInfo.packType === "BUNDLE" && !MealUtil.isFoodPackBundleSupported(userContext)) {
                    productDetailPage.actions.push({
                        actionType: "SHOW_ALERT_MODAL",
                        title: "Subscribe",
                        meta: {
                            title: "Update App",
                            subTitle: "Please update your app to subscribe to this pack",
                            actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
                        }
                    })
                } else {
                    let packAction: PackAction
                    if (AppUtil.isSubscriptionAttachSupported(userContext)) {
                        packAction = {
                            actionType: "GET_SUBSCRIPTION_ADDONS",
                            title: "Subscribe",
                            orderProduct: {
                                productId: packInfo.productId,
                                quantity: 1,
                                productType: "FOOD",
                                price: price,
                                option: {
                                    offerV2Ids: offerIds,
                                    offerId: !_.isEmpty(offerIds) ? offerIds[0] : undefined,
                                    isPack: true,
                                    subscriptionType: subscriptionType,
                                    weekendEnabled: subscriptionType === "WEEKLY" ? weeklyWeekendsEnabled : monthlyWeekendsEnabled,
                                    numTickets: packOption.numTickets,
                                    autorenewalEnabled: MealUtil.isAutoRenewDefault(packInfo),
                                }
                            },
                            mealSlot: {
                                id: packInfo.mealSlot,
                                name: capitalizeFirstLetter(packInfo.mealSlot.toLowerCase())
                            }
                        }
                        packAction.actionType = "GET_MEAL_PACK"
                    } else {
                        packAction = {
                            actionType: "GET_MEAL_PACK",
                            title: "Subscribe", // for " + RUPEE_SYMBOL + price.listingPrice,
                            orderProduct: {
                                price: price,
                                productId: packInfo.productId,
                                quantity: 1,
                                productType: "FOOD",
                                option: {
                                    offerV2Ids: offerIds,
                                    offerId: !_.isEmpty(offerIds) ? offerIds[0] : undefined,
                                    isPack: true,
                                    subscriptionType: subscriptionType,
                                    weekendEnabled: subscriptionType === "WEEKLY" ? weeklyWeekendsEnabled : monthlyWeekendsEnabled,
                                    numTickets: packOption.numTickets,
                                    autorenewalEnabled: MealUtil.isAutoRenewDefault(packInfo)
                                }
                            },
                            mealSlot: {
                                id: packInfo.mealSlot,
                                name: capitalizeFirstLetter(packInfo.mealSlot.toLowerCase())
                            }
                        }
                    }
                    productDetailPage.actions.push(packAction)
                }
            }
        } else {
            const mainPackOrderProduct = {
                price: packOfferAndPrice.price,
                productId: packInfo.productId,
                quantity: 1,
                productType: "FOOD",
                option: {
                    previousFulfilmentId: packBooking.fulfilmentId,
                    offerIds: offerIds,
                    offerId: !_.isEmpty(offerIds) ? offerIds[0] : undefined,
                    numTickets: packOption.numTickets,
                    weekendEnabled: packBooking.weekendsEnabled,
                    isPack: true,
                    numDays: packOption.numDays,
                    subscriptionType: packBooking.subscriptionType
                },
            }
            const orderProducts = []
            orderProducts.push(mainPackOrderProduct)
            for (const addonBooking of packBooking.subPackBookings) {
                const addonPackInfo = await this.catalogueService.getFoodPack(addonBooking.packId)
                const priceOption = this.getPriceOption(addonPackInfo.options, packOption.numTickets, packOption.subscriptionType)
                const packOfferAndPrice = OfferUtil.getFoodPackOfferAndPrice(addonPackInfo, priceOption, packOffersResponse)
                orderProducts.push({
                    price: packOfferAndPrice.price, ///
                    productId: addonBooking.packId,
                    quantity: 1,
                    productType: "FOOD",
                    option: {
                        previousFulfilmentId: addonBooking.fulfilmentId,
                        offerIds: offerIds,
                        offerId: !_.isEmpty(offerIds) ? offerIds[0] : undefined,
                        numTickets: packOption.numTickets,
                        weekendEnabled: packBooking.weekendsEnabled,
                        isPack: true,
                        numDays: packOption.numDays,
                        subscriptionType: packBooking.subscriptionType
                    }
                })
            }
            if (packBooking.packState === "HALTED" || packBooking.packState === "CANCELLED") {
                productDetailPage.actions.push({
                    actionType: "RESTART_SUBSCRIPTION",
                    title: "Restart Subscription",
                    url: "curefit://cartcheckout",
                    meta: {
                        listingBrand: "EAT_FIT",
                        orderProductList: orderProducts,
                        orderProduct: mainPackOrderProduct,
                        addressId: packBooking.address.addressId,
                        weekendAddressId: packBooking.weekendAddress ? packBooking.weekendAddress.addressId : undefined,
                        deliverySlot: packBooking.packSlot.slotId,
                        mealSlot: {
                            id: packInfo.mealSlot
                        },
                        deliveryInfo: [{
                            mealSlot: packInfo.mealSlot, addressId: packBooking.address.addressId,
                            weekendAddressId: packBooking.weekendAddress ? packBooking.weekendAddress.addressId : undefined, deliverySlot: packBooking.packSlot.slotId
                        }]
                    }
                })
            } else if (MealUtil.isRenewAllowed(packBooking, packInfo)) {
                if (packInfo.status !== "LIVE") {
                    productDetailPage.actions.push({
                        actionType: "NAVIGATION",
                        title: "Renew",
                        url: ActionUtil.packBrowsePage()
                    })
                } else if (packBooking.subscriptionType === undefined) {
                    const foodPack = await this.catalogueService.getFoodPack(packBooking.packId)
                    const seoParams: SeoUrlParams = {
                        productName: foodPack.title
                    }
                    productDetailPage.actions.push({
                        actionType: "NAVIGATION",
                        title: "Renew",
                        url: ActionUtil.foodPack(packBooking.packId, undefined, undefined, true, userContext.sessionInfo.userAgent, seoParams)
                    })
                } else if (packBooking.packState === "PENDING") {
                    const deliverySlots: DeliverySlot[] = SlotUtil.getDeliverySlotsForMealSlotAndChannel(packInfo.mealSlot, packBooking.address.kioskId ? "KIOSK" : "ONLINE")
                    const deliverySlot = SlotUtil.getMaxSlot(deliverySlots)
                    productDetailPage.actions.push({
                        actionType: "RENEW_MEAL_PACK_V2",
                        title: this.getRenewPendingSubsText(packBooking.earliestRenewalDate, deliverySlot, packBookingTz),
                        url: `curefit://payment?listingBrand=${"EAT_FIT"}`,
                        meta: {
                            orderProduct: mainPackOrderProduct,
                            orderProductList: orderProducts,
                            orderId: packBooking.orderId,
                            fulfilmentId: packBooking.fulfilmentId,
                            productId: packBooking.packId,
                            listingBrand: "EAT_FIT",
                            startDate: packBooking.earliestRenewalDate,
                            mealSlot: packInfo.mealSlot
                        }
                    })
                } else if (!packBooking.autoRenewSupported) {
                    productDetailPage.actions.push({
                        actionType: "RENEW_MEAL_PACK_V2",
                        title: this.getRenewOngoingSubsText(packBooking.earliestRenewalDate, packBooking.packEndDate, packBookingTz),
                        url: `curefit://payment?listingBrand=${"EAT_FIT"}`,
                        meta: {
                            orderProduct: mainPackOrderProduct,
                            orderProductList: orderProducts,
                            orderId: packBooking.orderId,
                            fulfilmentId: packBooking.fulfilmentId,
                            productId: packBooking.packId,
                            listingBrand: "EAT_FIT",
                            startDate: packBooking.earliestRenewalDate,
                            mealSlot: packInfo.mealSlot
                        }
                    })
                }
            }
        }
        const isWeb = userContext.sessionInfo.userAgent === "DESKTOP" || userContext.sessionInfo.userAgent === "MBROWSER"
        productDetailPage.widgets.push(await this.getSummaryWidget(userContext,
            price, packInfo, issuesMap, packBooking, menu, packOption, offerIds, isWeb))
        if (packBooking.packState === "NOT_BOUGHT") {
            if (!isSubPack) {
                if (packInfo.packType !== "KIOSKPACK")
                    productDetailPage.widgets.push(this.getWhySubscribeWidget(mealPackPageConfig, packInfo, userContext))
                if (packInfo.packType === "BUNDLE") {
                    productDetailPage.widgets.push(this.getPackDurationWidget(packInfo, packOption, packOffersResponse, subscriptionType, monthlyWeekendsEnabled, weeklyWeekendsEnabled, numTickets))
                } else {
                    productDetailPage.widgets.push(this.getPackDurationPickerWidget(packInfo, preferredLocation.deliveryChannel, packOffersResponse, subscriptionType, weeklyWeekendsEnabled, monthlyWeekendsEnabled, numTickets))
                }
                if (!_.isEmpty(packInfo.subProductIds)) {
                    productDetailPage.widgets.push(await this.getSubPacksGridWidgets(packInfo, userContext))
                }
            }
        } else {
            if (packBooking.packState !== "COMPLETED" && packBooking.packState !== "HALTED" && packBooking.packState !== "CANCELLED") {
                if (packBooking.packType === "KIOSKPACK") {
                    productDetailPage.widgets.push(new PickupWidget(packBooking.nextPickupUrl, packBooking.address, packBooking))
                }
                if (MealUtil.isRenewAllowed(packBooking, packInfo)) {
                    const renewalOffer = await this.offerService.getRenewalOffers(packBooking.fulfilmentId)
                    if (renewalOffer && !_.isEmpty(renewalOffer.offers))
                        productDetailPage.widgets.push(getOffersWidget("Renewal Offer", renewalOffer.offers))
                } else if (!_.isEmpty(packOfferAndPrice.offers) && packBooking.packState !== "ACTIVE" && packBooking.packState !== "PAUSED")
                    productDetailPage.widgets.push(getOffersWidget("Offers applied", packOfferAndPrice.offers))

                const mealPackManageOptions = await this.productBusiness.getMealPackManageOptions(userContext, menu, packBooking, issuesMap, true, false, isWeb, packInfo)
                if (mealPackManageOptions.manageOptions.options.length > 0)
                    productDetailPage.widgets.push(this.getManageOptionsWidget(mealPackManageOptions.manageOptions,
                        mealPackManageOptions.meta))
                const nextAvailableDate = this.getNextAvailableDate(packBooking, menu, userContext)
                productDetailPage.widgets.push(this.getChangeSlotWidget(packBooking, nextAvailableDate, userContext))
                const isChangeAddressAllowed = MealUtil.isChangeAddressAllowed(packInfo)
                productDetailPage.widgets.push(new PackAddressWidget(packBooking.address, this.getMealPackMeta(packBooking), userContext, !isChangeAddressAllowed))
                if (packBooking.weekendAddress)
                    productDetailPage.widgets.push(new PackAddressWidget(packBooking.weekendAddress, { ...this.getMealPackMeta(packBooking), isWeekendAddress: true }, userContext, !isChangeAddressAllowed))

                const state = await this.userService.getUserWhatsappState(userContext.userProfile.userId)
                if (state && state.length > 0 && state[0] && parseInt(state[0].attrValue) === WhatsappEnum.PENDING)
                    productDetailPage.widgets.push(this.getOptInWidget())
            }
        }

        if (packBooking.packState === "NOT_BOUGHT") {
            if (!isSubPack) {
                if (!_.isEmpty(packOfferAndPrice.offers)) {
                    productDetailPage.widgets.push(getOffersWidget("Offers applied", packOfferAndPrice.offers))
                }
                productDetailPage.widgets.push(this.getHowItWorksWidget(mealPackPageConfig, preferredLocation.address ? preferredLocation.address.addressType === "KIOSK" : false, packInfo))
            }
            if (packInfo.packType !== "KIOSKPACK" && packInfo.packType !== "BUNDLE") {
                const upcomingMenusToShow: { [date: string]: string } = {}
                Object.keys(menu).forEach(date => {
                    if (MealUtil.isAnySlotHardCutOffNotPassed(packInfo.mealSlot, preferredLocation.deliveryChannel, date, packBookingTz)) {
                        upcomingMenusToShow[date] = menu[date]
                    }
                })
                productDetailPage.widgets.push(await this.getNextFewDaysWidget(packBooking.packState, upcomingMenusToShow, products, packInfo.productId, MAX_UPCOMING_MEALS, packInfo.mealSlot, userContext, packBookingTz, singleOffersResponse, packInfo))
            }
        } else if (packBooking.packState !== "COMPLETED") {
            const upcomingShipment = this.productBusiness.getUpcomingShipment(packBooking, packBookingTz)
            const upcomingShipmentDate = upcomingShipment ? upcomingShipment.deliveryDate : undefined
            if (packInfo.packType !== "KIOSKPACK")
                productDetailPage.widgets.push(await this.getNextFewDaysWidget(packBooking.packState, menu, products, packInfo.productId, MAX_UPCOMING_MEALS, packInfo.mealSlot, userContext, packBookingTz, singleOffersResponse, packInfo, upcomingShipmentDate, packBooking.futureShipment, packBooking.activeShipment, packBooking.subPackBookings, packBooking.fulfilmentId))
        }
        if (packInfo.packType === "KIOSKPACK") {
            const lunchData = await this.getSlotMenu("LUNCH", preferredLocation, packInfo, userContext, packBookingTz)
            const snacksData = await this.getSlotMenu("SNACKS", preferredLocation, packInfo, userContext, packBookingTz)
            if (lunchData.date > snacksData.date) {
                if (snacksData.menu.items.length > 0)
                    productDetailPage.widgets.push(snacksData.menu)
                if (lunchData.menu.items.length > 0)
                    productDetailPage.widgets.push(lunchData.menu)
            } else {
                if (lunchData.menu.items.length > 0)
                    productDetailPage.widgets.push(lunchData.menu)
                if (snacksData.menu.items.length > 0)
                    productDetailPage.widgets.push(snacksData.menu)
            }
        }

        if (packBooking.packState !== "NOT_BOUGHT" && !_.isEmpty(packBooking.pastShipment))
            productDetailPage.widgets.push(this.getCompletedMealsWidget(packBooking.pastShipment, products, packInfo.mealSlot, userContext.sessionInfo.userAgent))

        // disabling action for deprecating eat fit
        if (productDetailPage?.actions) {
            productDetailPage.actions = []
        }
        return productDetailPage
    }

    private getPriceOption(priceOptions: FoodPackOption[], numTickets: number, subscriptionType: string): FoodPackOption {
        let priceOption
        priceOptions.forEach(price => {
            if (price.numTickets === numTickets && price.subscriptionType === subscriptionType) {
                priceOption = price
            }
        })
        return priceOption
    }

    private getOptInWidget() {
        const action: Action = {
            actionType: "OPT_IN",
            title: "OPT IN",
            url: "/user/whatsappOptIn"
        }

        return new OptInPermissionWidgetView(
            "Would you like to get your meal subscription updates on WhatsApp?",
            "Don't worry we will not spam and you can always opt out",
            "https://cdn-images.cure.fit/www-curefit-com/image/upload/image/optin/whatsapp_icon.png",
            action
        )
    }

    private async createSubscriptionAddonPacks(packInfo: FoodPack, preferredLocation: PreferredLocation, userContext: UserContext, areaId: string, session: Session): Promise<FoodPack[]> {
        const addonPacksPricing: FoodPack[] = []
        const packAvailabilityMap = await EatSubscriptionUtil.createPackAvailabilityMap(this.capacityService, areaId, packInfo.mealSlot)
        const addonFoodPacks = await this.catalogueService.getFoodPacksByMealSlot(packInfo.mealSlot, preferredLocation.deliveryChannel, "ATTACH")
        const productIds = _.map(addonFoodPacks, (pack) => {
            return pack.productId
        })
        const request: EatOfferRequestParams = {
            mealSlots: [packInfo.mealSlot],
            productIds: productIds,
            userId: userContext.userProfile.userId,
            cityId: userContext.userProfile.cityId,
            areaId: areaId,
            deviceId: session.deviceId,
            source: AppUtil.callSourceFromContext(userContext)
        }
        const packOfferResponse = {} as FoodPackOffersResponseV2

        if (addonFoodPacks.length > 0) {
            for (const item of addonFoodPacks) {
                const productOption = packOfferResponse.products[item.productId]
                if (packAvailabilityMap.get(item.productId) && productOption) {
                    const offerOptions = _.map(productOption.packOptionOffers, (offer) => {
                        return offer.packOption
                    })
                    offerOptions.forEach(price => {
                        if (price.price.mrp !== price.price.listingPrice) {
                            price.price = {
                                ...price.price,
                                showPriceCut: true
                            }
                        }
                    })
                    const addonItem = { ...item }
                    addonItem.options = offerOptions
                    addonPacksPricing.push(addonItem)
                }
            }
            return addonPacksPricing
        } else {
            return []
        }
    }

    public setMessage(productDetailPage: ProductDetailPage, title: string, subTitle: string, actions?: Action[], listItems?: { title: string, subTitle: string }[]) {
        productDetailPage.message = { title, subTitle }
        if (actions) {
            productDetailPage.alertInfo = {
                title: title,
                subTitle: subTitle,
                actions: actions,
                listItems: listItems
            }
        }
    }

    private async getSummaryWidget(
        userContext: UserContext,
        price: ProductPrice,
        packInfo: FoodPack, issuesMap: Map<string, CustomerIssueType[]>,
        foodPackBooking: FoodPackBooking,
        menu: { [date: string]: string },
        foodPackOption?: FoodPackOption,
        offerIds?: string[],
        isWeb?: boolean
    ): Promise<WidgetView> {
        let image
        const userAgent = userContext.sessionInfo.userAgent
        if (userAgent === "DESKTOP") {
            image = UrlPathBuilder.getPackImagePath(packInfo.productId, "FOOD", "MAGAZINE", packInfo.imageVersion, userAgent)
        } else {
            image = UrlPathBuilder.getPackImagePath(packInfo.productId, "FOOD", "HERO", packInfo.imageVersion, userAgent)
        }
        const summaryWidget: WidgetView & {
            title: string
            subTitle?: string
            packId: string
            fulfilmentId: string
            image: string
            numTickets: number
            numDays: number
            price: ProductPrice
            manageOptions?: { displayText: string, options: ManageOptionPayload[] }
            meta?: any
            packProgress?: PackProgress
            offerId: string
            offerIds: string[]
            addonTitles: string[]
        } = {
            widgetType: "MEAL_PACK_SUMMARY",
            title: packInfo.title,
            subTitle: foodPackBooking.packState === "NOT_BOUGHT" ? packInfo.subTitle : undefined,
            packId: packInfo.productId,
            fulfilmentId: foodPackBooking.fulfilmentId,
            image: image,
            numTickets: foodPackOption ? foodPackOption.numTickets : foodPackBooking.schedule.ticketsTotal,
            numDays: foodPackOption ? foodPackOption.numDays : packInfo.options[0].numDays,
            price: price,
            offerIds: offerIds,
            offerId: !_.isEmpty(offerIds) ? offerIds[0] : undefined,
            addonTitles: []
        }

        if (foodPackBooking.packState !== "NOT_BOUGHT") {
            summaryWidget.packProgress = this.getPackProgress(foodPackBooking, userContext)

            const mealPackManageOptions = await this.productBusiness.getMealPackManageOptions(userContext, menu, foodPackBooking, issuesMap, false, true, isWeb, packInfo)
            summaryWidget.manageOptions = mealPackManageOptions.manageOptions
            summaryWidget.meta = mealPackManageOptions.meta
        }

        if (EatSubscriptionUtil.isAddonPresentInPack(userContext, foodPackBooking.subPackBookings) && foodPackBooking.packState !== "NOT_BOUGHT") {
            for (const item of foodPackBooking.subPackBookings) {
                const packInfo = await this.catalogueService.getFoodPack(item.packId)
                if (item.packState === "ACTIVE") {
                    summaryWidget.addonTitles.push(packInfo.title)
                }
            }
        }
        return summaryWidget
    }

    private getManageOptionsWidget(manageOptions: ManageOptions, meta: any) {
        return new ManageOptionsWidget(manageOptions, meta)
    }

    private getWhySubscribeWidget(mealPackPageConfig: MealPackPageConfig, packInfo: FoodPack, userContext: UserContext) {
        let whySubscribe: InfoCard[]
        if (packInfo.packType === "KIOSKPACK") {
            whySubscribe = mealPackPageConfig.whySubscribeKioskPackItems
        } else if (!_.isEmpty(packInfo.tags) && packInfo.tags.indexOf("WEIGHT_LOSS") > -1) {
            whySubscribe = mealPackPageConfig.whySubscribeWLPackItems
        } else if (!_.isEmpty(packInfo.tags) && packInfo.tags.indexOf("FREE_SWITCH") > -1) {
            whySubscribe = mealPackPageConfig.whySubscribeAllStar
        } else if (!_.isEmpty(packInfo.tags) && packInfo.tags.indexOf("WEIGHT_WISE") > -1 &&
            AppUtil.isNewWeightWiseIconSupported(userContext)) {
            whySubscribe = mealPackPageConfig?.whySubscribeWeightWise
        } else if (!_.isEmpty(packInfo.tags) && packInfo.tags.indexOf("WEIGHT_WISE_30+") > -1 &&
            AppUtil.isNewWeightWiseIconSupported(userContext)) {
            whySubscribe = mealPackPageConfig?.whySubscribeWeightWisePlus
        } else {
            whySubscribe = mealPackPageConfig.whySubscribeItems
        }
        return new ProductListWidget("INFO_CELL", { "title": "Why subscribe" }, whySubscribe, undefined, true)
    }

    private async getSubPacksGridWidgets(packInfo: FoodPack, userContext: UserContext) {
        const foodPackCards: Promise<InfoCard>[] = _.map(packInfo.subProductIds, async (productId) => {
            const foodPack = await this.catalogueService.getFoodPack(productId)
            const seoParams: SeoUrlParams = {
                productName: foodPack.title
            }
            const infoCard: ActionCard = {
                id: productId,
                title: undefined,
                action: ActionUtil.foodPack(foodPack.productId, undefined, undefined, true, userContext.sessionInfo.userAgent, seoParams, true),
                image: UrlPathBuilder.getPackImagePath(foodPack.productId, "FOOD", "MAGAZINE", foodPack.imageVersion, userContext.sessionInfo.userAgent)
            }
            return Promise.resolve(infoCard)
        })
        return new ProductListWidget("MAGAZINE_CELL", { "title": "What's included" }, await Promise.all(foodPackCards), undefined, false, undefined, false, 2)
    }

    private getHowItWorksWidget(mealPackPageConfig: MealPackPageConfig, isKiosk: boolean, packInfo: FoodPack): ProductListWidget {
        try {
            const header: Header = {
                title: mealPackPageConfig.howItWorksTitle
            }
            const infoCards: InfoCard[] = []
            if (packInfo.packType === "KIOSKPACK") {
                mealPackPageConfig.howItWorksItemListKioskPack.forEach(item => {
                    infoCards.push({
                        subTitle: item.text,
                        icon: item.icon
                    })
                })
            } else if (isKiosk) {
                mealPackPageConfig.howItWorksItemListKiosk.forEach(item => {
                    infoCards.push({
                        subTitle: item.text,
                        icon: item.icon
                    })
                })
            } else if (packInfo.tags.indexOf("WEIGHT_LOSS") > -1) {
                mealPackPageConfig.howItWorksItemListWL.forEach(item => {
                    infoCards.push({
                        subTitle: item.text,
                        icon: item.icon
                    })
                })
            } else if (packInfo.tags.indexOf("FREE_SWITCH") > -1) {
                mealPackPageConfig.howItWorksItemListAllStar.forEach(item => {
                    infoCards.push({
                        subTitle: item.text,
                        icon: item.icon
                    })
                })
            } else if (packInfo.tags.indexOf("WEIGHT_WISE") > -1) {
                mealPackPageConfig.howItWorksItemListWeightWise.forEach(item => {
                    infoCards.push({
                        subTitle: item.text,
                        icon: item.icon
                    })
                })
            }  else if (packInfo.tags.indexOf("WEIGHT_WISE_30+") > -1) {
                mealPackPageConfig.howItWorksItemListWeightWisePlus.forEach(item => {
                    infoCards.push({
                        subTitle: item.text,
                        icon: item.icon
                    })
                })
            } else {
                mealPackPageConfig.howItWorksItemListSubscription.forEach(item => {
                    infoCards.push({
                        subTitle: item.text,
                        icon: item.icon
                    })
                })
            }
            return new ProductListWidget("SMALL", header, infoCards)
        } catch (e) {
            return undefined
        }
    }

    private getPackProgress(foodPackBooking: FoodPackBooking, userContext: UserContext): PackProgress {
        const packBookingTz = foodPackBooking.timezone
        let title = foodPackBooking.subscriptionType === "MONTHLY" ? "Monthly plan, " :
            foodPackBooking.subscriptionType === "WEEKLY" ? "Weekly plan, " : ""
        if (foodPackBooking.subscriptionType === undefined) {
            title += foodPackBooking.weekendsEnabled ? "Delivers everyday" : "Delivers weekdays"
        } else {
            title += foodPackBooking.weekendsEnabled ? "delivers everyday" : "delivers weekdays"
        }
        let displayText = "Refund due: " + RUPEE_SYMBOL + (foodPackBooking.refundDue ? Math.round(foodPackBooking.refundDue) : 0)
            + " (" + (foodPackBooking.ticketsCancelled ? foodPackBooking.ticketsCancelled : 0) + " meals) on "
            + TimeUtil.addToDateString(foodPackBooking.packEndDate, packBookingTz, foodPackBooking.autoRenewSupported ? 0 : RENEWAL_WINDOW_DAYS, "days").format("Do MMM")
        let subTitle = (foodPackBooking.autoRenewSupported ? "Renews " : "Ends ") + TimeUtil.getDayText(foodPackBooking.packEndDate, packBookingTz, {
            sameDay: "[today]",
            nextDay: "[tomorrow]",
            nextWeek: "[on] Do MMM",
            sameElse: "[on] Do MMM"
        })
        if (foodPackBooking.packType === "KIOSKPACK") {
            displayText = subTitle
            subTitle = foodPackBooking.schedule.ticketsLeft + " meals left"
        } else if (foodPackBooking.subscriptionType === undefined) {
            displayText = undefined
            subTitle = (foodPackBooking.schedule.ticketsTotal - foodPackBooking.schedule.ticketsLeft) + " of " + foodPackBooking.schedule.ticketsTotal + " meals served"
        } else if (foodPackBooking.packState === "CANCELLED") {
            subTitle = "Subscription Cancelled"
            displayText = undefined
        } else if (foodPackBooking.packState === "HALTED") {
            subTitle = "Subscription Halted"
            displayText = undefined
        } else if (foodPackBooking.packState === "PENDING") {
            subTitle = "Payment Pending"
        }
        let progressComplete: number = foodPackBooking.schedule.ticketsTotal - foodPackBooking.schedule.ticketsLeft
        if (foodPackBooking.packType !== "KIOSKPACK" && foodPackBooking.subscriptionType !== undefined && momentTz.tz(foodPackBooking.packStartDate, packBookingTz).isSameOrBefore(TimeUtil.todaysDateWithTimezone(packBookingTz))) {
            progressComplete = foodPackBooking.weekendsEnabled ? TimeUtil.getMomentNow(packBookingTz).diff(momentTz.tz(foodPackBooking.packStartDate, packBookingTz), "days") : TimeUtil.weekDayDiffInDays(packBookingTz, foodPackBooking.packStartDate, TimeUtil.todaysDateWithTimezone(packBookingTz))
            progressComplete += SlotUtil.isCancelCutOffPassed(TimeUtil.todaysDateWithTimezone(packBookingTz), packBookingTz, foodPackBooking.packSlot.slotId) ? 1 : 0
        }
        return {
            startDate: foodPackBooking.packStartDate,
            endDate: foodPackBooking.packEndDate,
            total: foodPackBooking.schedule.ticketsTotal,
            completed: progressComplete,
            state: foodPackBooking.packState,
            title: title,
            displayText: displayText,
            subTitle: subTitle,
            type: "FOOD"
        }
    }

    private getNextAvailableDate(packBooking: FoodPackBooking, menu: { [date: string]: string }, userContext: UserContext): string {
        const packBookingTz = packBooking.timezone
        const today: string = TimeUtil.todaysDateWithTimezone(packBookingTz)
        const isTodayAvailable: boolean = today in menu && !SlotUtil.isCancelCutOffPassed(today, packBookingTz, packBooking.packSlot.slotId)
        let nextAvailableDate: string

        if (isTodayAvailable) {
            nextAvailableDate = today
        } else {
            nextAvailableDate = Object.keys(menu).filter(val => val !== today).sort()[0]
        }
        return nextAvailableDate
    }

    private getMealPackMeta(packBooking: FoodPackBooking): any {
        return {
            packId: packBooking.packId,
            fulfilmentId: packBooking.fulfilmentId
        }
    }

    private getChangeSlotWidget(foodPackBooking: FoodPackBooking, nextAvailableDate: string, userContext: UserContext): ManageOptionsWidget {
        let changeSlotText
        const tz = foodPackBooking.timezone
        const sameDayText = `Change slot (cut-off: ${MealUtil.getCancelCutOffTimeDisplayText(foodPackBooking.packSlot.slotId)})`
        changeSlotText = TimeUtil.getDayText(nextAvailableDate, tz, {
            sameDay: `[${sameDayText}]`,
            nextDay: "[Change slot from tomorrow]",
            nextWeek: "[Change slot from] dddd"
        })

        let manageOptions: ManageOptions = {
            displayText: changeSlotText,
            helperText: MealUtil.getSlotDisplayTextFoodPackBooking(foodPackBooking),
            options: [],
            icon: "DROP_DOWN"
        }

        manageOptions = {
            displayText: MealUtil.getSlotDisplayTextFoodPackBooking(foodPackBooking),
            options: [],
            icon: "TIME"
        }

        const mealSlot: MealSlot = SlotUtil.getMealSlotForSlotId(foodPackBooking.packSlot.slotId, tz)
        const channel: DeliveryChannel = EatUtil.getDeliveryChannel(foodPackBooking.address)
        SlotUtil.getDeliverySlotsForMealSlotAndChannel(mealSlot, channel).forEach(deliverySlot => {
            if (!_.isEqual(deliverySlot, foodPackBooking.packSlot)) {
                manageOptions.options.push({
                    isEnabled: true,
                    displayText: MealUtil.getDisplayTextForSlot(deliverySlot, undefined, undefined, undefined, foodPackBooking.address && foodPackBooking.address.kioskType === "CAFE"), // only for packs so should not break
                    type: "CHANGE_PACK_SLOT",
                    meta: deliverySlot
                })
            }
        })

        const deliverySlot = SlotUtil.getSlotById(foodPackBooking.packSlot.slotId)
        return {
            widgetType: "MANAGE_OPTIONS_WIDGET",
            manageOptions: manageOptions,
            isDisabled: (deliverySlot.deliveryChannel === "KIOSK" || deliverySlot.deliveryChannel === "CAFE") ? true : false,
            invertStyle: true,
            meta: this.getMealPackMeta(foodPackBooking)
        }
    }

    private getPackDurationWidget(foodPack: FoodPack, foodPackOption: FoodPackOption, packOffersResponse: FoodPackOffersResponseV2, subscriptionType: SubscriptionType, monthlyWeekendsEnabled: boolean, weeklyWeekendsEnabled: boolean, numTickets: number): PackDurationWidget {
        return new PackDurationWidget(foodPack, foodPackOption, subscriptionType, monthlyWeekendsEnabled, weeklyWeekendsEnabled, numTickets, packOffersResponse)
    }

    private getPackDurationPickerWidget(foodPack: FoodPack, deliveryChannel: DeliveryChannel, packOffersResponse: FoodPackOffersResponseV2, subscriptionType: SubscriptionType, weeklyWeekendsEnabled: boolean, monthlyWeekendsEnabled: boolean, numTickets: number): PackDurationPickerWidget {
        return new PackDurationPickerWidget(foodPack, deliveryChannel, packOffersResponse, subscriptionType, weeklyWeekendsEnabled, monthlyWeekendsEnabled, numTickets)
    }

    private async getSlotMenu(
        mealSlot: MealSlot,
        preferredLocation: PreferredLocation,
        foodPack: FoodPack, userContext: UserContext,
        packBookingTz: Timezone
    ): Promise<{ menu: ProductListWidget, date: string }> {
        const menuAvailability = await this.productBusiness.getNextAvailableMenu(userContext, mealSlot, preferredLocation)
        const header: Header = {
            title: TimeUtil.getDayText(menuAvailability.day, packBookingTz, {
                sameDay: "[Today's]",
                nextDay: "[Tomorrow's]",
                nextWeek: "Do MMM",
                sameElse: "Do MMM"
            }) + " " + capitalizeFirstLetter(mealSlot),
        }
        const items: ActionCard[] = []
        _.map(menuAvailability.menus, async (menu) => {
            const product = await this.catalogueService.getProduct(menu.productId)
            const seoParams: SeoUrlParams = {
                productName: product.title
            }
            items.push({
                id: product.productId,
                title: product.title,
                state: "NOT_BOUGHT",
                image: UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, "FOOD", "THUMBNAIL", product.imageVersion),
                subTitle: product.subTitle,
                action: ActionUtil.foodSingleFromPack(product.productId, menuAvailability.day, mealSlot, false, foodPack.productId, undefined, userContext.sessionInfo.userAgent, seoParams)
            })
        })
        return { menu: new ProductListWidget("LARGE", header, items), date: menuAvailability.day }
    }

    private async getNextFewDaysWidget(
        packState: PackState,
        menu: { [date: string]: string },
        products: { [productId: string]: Product },
        packId: string,
        numberOfMealToInclude: number,
        mealSlot: MealSlot,
        userContext: UserContext,
        packBookingTz: Timezone,
        singleOffersResponse?: FoodSinglePriceOfferResponse,
        packInfo?: FoodPack,
        upcomingShipmentDate?: string,
        futureShipment?: FoodBooking[],
        activeShipment?: FoodBooking,
        subPackBookings?: SubFoodPackBooking[],
        fulfilmentId?: string
    ): Promise<ProductListWidget> {
        const header: Header = {
            title: "Upcoming meals",
            subTitle: packState === "NOT_BOUGHT" && !packInfo.tags.includes("WEIGHT_LOSS") && !packInfo.tags.includes("WEEKENDS_TOGGLE_NOT_SUPPORTED") ? "Sat & Sun meals will not be delivered if deliver on weekends is off" : ""
        }
        const items: ActionCard[] = []
        const isAddChangeMealNotSupportedTagPresent = !_.isEmpty(packInfo) && packInfo.tags?.indexOf(EatSubscriptionUtil.ADD_CHANGE_MEAL_NOT_SUPPORTED_TAG) > -1
        if (packState === "NOT_BOUGHT" || packState === "HALTED") {
            let menuDates: string[] = Object.keys(menu)
            if (!_.isEmpty(menuDates)) {
                menuDates.sort()
                if (numberOfMealToInclude) {
                    menuDates = menuDates.slice(0, numberOfMealToInclude)
                }
                // disabling action for deprecating eat fit
                const isBuyEnabled =  false // MealUtil.isBuySingleFromPackAllow(packInfo)
                menuDates.forEach((date, index) => {
                    const product: Product = products[menu[date]]
                    const seoParams: SeoUrlParams = {
                        productName: product.title
                    }
                    items.push({
                        id: product.productId,
                        title: product.title,
                        date: date,
                        state: "NOT_BOUGHT",
                        image: UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, "FOOD", "THUMBNAIL", product.imageVersion),
                        subTitle: product.subTitle,
                        action: ActionUtil.foodSingleFromPack(product.productId, date, mealSlot, index === 0 && isBuyEnabled, packId, undefined, userContext.sessionInfo.userAgent, seoParams, "EAT_FIT")
                    })
                })

                const firstDate = menuDates[0]
                const firstMenu = menu[firstDate]
                const firstProduct = products[firstMenu]
                const singleOfferAndPrice = OfferUtil.getSingleOfferAndPrice(firstProduct, firstDate, singleOffersResponse, mealSlot)
                if (isBuyEnabled) {
                    let offerBasedTitle: string
                    if (singleOfferAndPrice.price.listingPrice === 0)
                        offerBasedTitle = "Try for free"
                    else
                        offerBasedTitle = `Try for ${RUPEE_SYMBOL}${singleOfferAndPrice.price.listingPrice}`
                    const seoParams: SeoUrlParams = {
                        productName: firstProduct.title
                    }
                    items[0].tryAction = {
                        title: offerBasedTitle,
                        action: ActionUtil.foodSingleFromPack(firstProduct.productId, firstDate, mealSlot, MealUtil.isBuySingleFromPackAllow(packInfo), packId, undefined, userContext.sessionInfo.userAgent, seoParams, "EAT_FIT")
                    }
                }
            }
            return new ProductListWidget("LARGE", header, items)
        } else {
            const foodPerDate: { [date: string]: { productId: string, state: string, enableChange: boolean }[] } = {}
            const newItems: DailyMealWidget[] = []
            if (activeShipment) {
                const changeMealAction = ActionUtil.changeMealPage(activeShipment.productId, activeShipment.deliveryDate, mealSlot, activeShipment.fulfilmentId, activeShipment.packId === undefined)
                const product: Product = products[activeShipment.productId]
                let enableChange = MealUtil.isAddMealSupported(userContext) && activeShipment.state !== "CANCELLED" && activeShipment.state !== "DELIVERED" && !SlotUtil.isCancelCutOffPassed(activeShipment.deliveryDate, packBookingTz, activeShipment.deliverySlot.slotId) && activeShipment.changeMealAllowed
                if (!enableChange) {
                    enableChange = userContext.sessionInfo.userAgent === "APP" && upcomingShipmentDate === activeShipment.deliveryDate && activeShipment.state !== "CANCELLED" && packState === "ACTIVE" && activeShipment.changeMealAllowed
                }
                enableChange = enableChange && !isAddChangeMealNotSupportedTagPresent
                let action = undefined
                if (enableChange) {
                    action = {
                        title: MealUtil.isAddMealSupported(userContext) ? "Change / Add items" : "Change meal",
                        action: changeMealAction
                    }
                }
                if (EatSubscriptionUtil.isSubscriptionAddonSupported(userContext, subPackBookings)) {
                    const seoParams: SeoUrlParams = {
                        productName: product.title
                    }
                    items.push({
                        id: product.productId,
                        title: product.title,
                        state: activeShipment.state,
                        date: activeShipment.deliveryDate,
                        image: UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, "FOOD", "THUMBNAIL", product.imageVersion),
                        subTitle: product.subTitle,
                        action: activeShipment.products.length > 1 ? ActionUtil.foodCart(activeShipment.fulfilmentId, mealSlot, activeShipment.deliveryDate) : ActionUtil.foodSingleFromPack(activeShipment.productId, activeShipment.deliveryDate, mealSlot, false, activeShipment.packId, activeShipment.fulfilmentId, userContext.sessionInfo.userAgent, seoParams),
                        tryAction: action
                    })
                } else {
                    activeShipment.products.forEach(product => {
                        if (foodPerDate[activeShipment.deliveryDate])
                            foodPerDate[activeShipment.deliveryDate].push({ productId: product.productId, state: activeShipment.state, enableChange: enableChange })
                        else
                            foodPerDate[activeShipment.deliveryDate] = [{ productId: product.productId, state: activeShipment.state, enableChange: enableChange }]
                    })
                    for (const pack of subPackBookings) {
                        if (pack.activeShipment) {
                            const addonPackActiveShipment = pack.activeShipment
                            if (foodPerDate[addonPackActiveShipment.deliveryDate])
                                foodPerDate[addonPackActiveShipment.deliveryDate].push({ productId: addonPackActiveShipment.productId, state: addonPackActiveShipment.state, enableChange: enableChange })
                            else
                                foodPerDate[addonPackActiveShipment.deliveryDate] = [{ productId: addonPackActiveShipment.productId, state: addonPackActiveShipment.state, enableChange: enableChange }]
                        }
                    }
                }
            }

            if (!_.isEmpty(futureShipment)) { // assuming the number of future shipments of main and addon packs should be the same (If  not cancelled by the user)
                // for old versions and new vesrons without addon packs
                if (EatSubscriptionUtil.isSubscriptionAddonSupported(userContext, subPackBookings)) {
                    futureShipment.forEach(async (shipment: FoodBooking, date) => {
                        const product: Product = products[shipment.productId]
                        let enableChange = MealUtil.isAddMealSupported(userContext) && shipment.state !== "CANCELLED" && shipment.changeMealAllowed
                        if (!enableChange) {
                            enableChange = userContext.sessionInfo.userAgent === "APP" && upcomingShipmentDate === shipment.deliveryDate && shipment.state !== "CANCELLED" && packState === "ACTIVE" && shipment.changeMealAllowed
                        }
                        enableChange = enableChange && !isAddChangeMealNotSupportedTagPresent
                        const seoParams: SeoUrlParams = {
                            productName: product.title
                        }
                        items.push({
                            id: product.productId,
                            title: product.title,
                            state: shipment.state,
                            date: shipment.deliveryDate,
                            image: UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, "FOOD", "THUMBNAIL", product.imageVersion),
                            subTitle: product.subTitle,
                            action: shipment.products.length > 1 ? ActionUtil.foodCart(shipment.fulfilmentId, mealSlot, shipment.deliveryDate) : ActionUtil.foodSingleFromPack(shipment.productId, shipment.deliveryDate, mealSlot, false, shipment.packId, shipment.fulfilmentId, userContext.sessionInfo.userAgent, seoParams),
                            tryAction: enableChange ? {
                                title: MealUtil.isAddMealSupported(userContext) ? "Change / Add items" : "Change meal",
                                action: ActionUtil.changeMealPage(shipment.productId, shipment.deliveryDate, mealSlot, shipment.fulfilmentId, shipment.packId === undefined)
                            } : undefined
                        })
                    })
                } else { // for new versions with addons
                    let enableChange: boolean
                    futureShipment.forEach(async (shipment: FoodBooking, date) => {
                        enableChange = MealUtil.isAddMealSupported(userContext) && shipment.state !== "CANCELLED" && shipment.changeMealAllowed
                        if (!enableChange) {
                            enableChange = userContext.sessionInfo.userAgent === "APP" && upcomingShipmentDate === shipment.deliveryDate && shipment.state !== "CANCELLED" && packState === "ACTIVE" && shipment.changeMealAllowed
                        }
                        enableChange = enableChange && !isAddChangeMealNotSupportedTagPresent
                        shipment.products.forEach(product => {
                            if (foodPerDate[shipment.deliveryDate])
                                foodPerDate[shipment.deliveryDate].push({ productId: product.productId, state: shipment.state, enableChange: enableChange })
                            else
                                foodPerDate[shipment.deliveryDate] = [{ productId: product.productId, state: shipment.state, enableChange: enableChange }]
                        })
                    })
                    subPackBookings.forEach(product => {
                        if (product.futureShipment) {
                            product.futureShipment.forEach(date => {
                                if (foodPerDate[date.deliveryDate])
                                    foodPerDate[date.deliveryDate].push({ productId: date.productId, state: date.state, enableChange: enableChange })
                                else
                                    foodPerDate[date.deliveryDate] = [{ productId: date.productId, state: date.state, enableChange: enableChange }]
                            })
                        }
                    })
                }
            }
            if (EatSubscriptionUtil.isAddonPresentInPack(userContext, subPackBookings) && !_.isEmpty(foodPerDate)) {
                for (const date of Object.keys(foodPerDate)) {
                    let enableChangeForDate: boolean
                    let state: string
                    const productIdPerDay = foodPerDate[date]
                    const productsPerDay: Product[] = []
                    for (const id of productIdPerDay) {
                        if (_.isNil(enableChangeForDate)) {
                            enableChangeForDate = id.enableChange
                        }
                        if (_.isNil(state)) {
                            state = id.state
                        }
                        await this.getProductInfo(id.productId).then(function (value: Product) {
                            productsPerDay.push(value)
                        })
                    }
                    const widgetDataPerDay: {
                        id: string,
                        title: string,
                        imageUrl: string
                    }[] = []
                    productsPerDay.forEach(item => {
                        widgetDataPerDay.push({
                            id: item.productId,
                            title: item.title,
                            imageUrl: UrlPathBuilder.getSingleImagePath(item.productId, "FOOD", "THUMBNAIL", item.imageVersion),
                        })
                    })

                    const action: VMAction = {
                        url: ActionUtil.foodCart(fulfilmentId, mealSlot, date),
                        actionType: "NAVIGATION"
                    }
                    const isMealCancelled: boolean = state === "CANCELLED"
                    const dateMealWidget = await new DailyMealWidget(date, widgetDataPerDay, action, (enableChangeForDate && state !== "CANCELLED") ? {
                        actionType: "NAVIGATION",
                        url: `curefit://addchangemealv2?fulfilmentId=${fulfilmentId}&date=${date}&slot=${mealSlot}`,
                        title: MealUtil.isAddMealSupported(userContext) ? "Change / Add items" : "Change meal",
                        meta: {
                            fulfilmentId: fulfilmentId,
                            date: date,
                            mealSlot: mealSlot,
                        }
                    } : undefined, isMealCancelled).buildView()
                    newItems.push(dateMealWidget)
                }
            }
            if (items.length === 0 && newItems.length === 0) {
                header.subTitle = "These meals will show up 2 days before your subscription begins"
            }
            let nextFewDaysWidget: ProductListWidget
            if (!_.isEmpty(items)) {
                nextFewDaysWidget = new ProductListWidget("LARGE", header, items)
            } else {
                nextFewDaysWidget = new ProductListWidget("DAILY_MEAL_WIDGET_CARD", header, newItems)
            }
            return nextFewDaysWidget
        }
    }
    async getProductInfo(productId: string): Promise<Product> {
        return await this.catalogueService.getProduct(productId)
    }


    private getCompletedMealsWidget(foodBookings: FoodBooking[],
        products: { [productId: string]: Product }, mealSlot: MealSlot, userAgent: UserAgentType): ProductListWidget {
        const items: ActionCard[] = []
        foodBookings.reverse()
        foodBookings.forEach(foodBooking => {
            const product: Product = products[foodBooking.productId]
            const seoParams: SeoUrlParams = {
                productName: product.title
            }
            items.push({
                id: product.productId,
                image: UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, "FOOD", "THUMBNAIL", product.imageVersion),
                title: product.title,
                subTitle: product.subTitle,
                date: foodBooking.deliveryDate,
                state: foodBooking.state,
                action: foodBooking.products.length > 1 ? ActionUtil.foodCart(foodBooking.fulfilmentId, mealSlot, foodBooking.deliveryDate) : ActionUtil.foodSingleFromPack(foodBooking.productId, foodBooking.deliveryDate, mealSlot, false, foodBooking.packId, foodBooking.fulfilmentId, userAgent, seoParams),
            })
        })
        const completedItemsCount: number = items ? items.length : 0
        const header: Header = {
            title: "Completed meals" + " (" + completedItemsCount + ")"
        }
        const completedMealsWidget: ProductListWidget = new ProductListWidget("LARGE", header, items)
        return completedMealsWidget
    }

    private isDelivered(booking: FoodBooking) {
        if (booking.state === "DELIVERED") {
            return true
        } else {
            return false
        }
    }


    private isCancelled(booking: FoodBooking) {
        if (booking.state === "CANCELLED") {
            return true
        } else {
            return false
        }
    }

    private getRenewOngoingSubsText(earliestRenewalDate: string, packEndDate: string, packBookingTz: Timezone) {
        if (_.isEmpty(earliestRenewalDate)) {
            return "Renew from " + TimeUtil.formatDateStringInTimeZone(packEndDate, packBookingTz, "Do MMM") // + RUPEE_SYMBOL + (packOfferAndPrice.price.listingPrice - packBooking.refundDue),
        } else {
            return "Renew from " + TimeUtil.formatDateStringInTimeZone(earliestRenewalDate, packBookingTz, "Do MMM")
        }
    }

    private getRenewPendingSubsText(earliestRenewalDate: string, deliverySlot: DeliverySlot, packBookingTz: Timezone) {
        const isTodaysCutoffPassed = SlotUtil.isHardCutOffPassed(TimeUtil.todaysDateWithTimezone(packBookingTz), packBookingTz, deliverySlot.slotId)
        const todaysDate = TimeUtil.todaysDate(packBookingTz)
        const proposedStartDate = isTodaysCutoffPassed ? TimeUtil.addDays(packBookingTz, todaysDate, 1) : todaysDate
        if (_.isEmpty(earliestRenewalDate) || earliestRenewalDate <= proposedStartDate) {
            return "Renew from " + (isTodaysCutoffPassed ? "Tomorrow" : "Today")
        }
        return "Renew from " + TimeUtil.formatDateStringInTimeZone(earliestRenewalDate, packBookingTz, "Do MMM")
    }
}

export default MealSubscriptionViewBuilder
