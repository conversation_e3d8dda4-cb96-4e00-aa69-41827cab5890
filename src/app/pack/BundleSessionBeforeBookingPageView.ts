import {
    Action,
    CenterSelectionWidget,
    getOffersWidget,
    GradientCard,
    Header,
    InfoCard,
    PricingSingleSelectionValue,
    PricingWidgetRecurringValue,
    ProductDetailPage,
    ProductListWidget,
    ProductPricingWidget,
    WidgetView
} from "../common/views/WidgetView"
import * as _ from "lodash"
import { OfferUtil } from "@curefit/base-utils"
import { BundleSessionSellableProduct, ManagedPlanPackInfo, Patient } from "@curefit/albus-client"
import { PackOffersResponse } from "@curefit/offer-common"
import { RUPEE_SYMBOL } from "@curefit/finance-common"
import { CareUtil } from "../util/CareUtil"
import { UserContext } from "@curefit/userinfo-common"
import { UserAgentType } from "@curefit/base-common"
import { Orientation } from "@curefit/vm-models"
import { CacheHelper } from "../util/CacheHelper"
import AppUtil from "../util/AppUtil"
import { ICultServiceOld as ICultService } from "@curefit/cult-client"
import { MembershipPaymentType } from "@curefit/order-common"
import { IPayByMembershipService  } from "@curefit/alfred-client"
import {
    PayByMembershipDetails,
    ProductPayByMembershipMap
} from "@curefit/oms-api-client"
import { BannerCarouselWidget } from "../page/PageWidgets"
import { pluralizeStringIfRequired } from "@curefit/util-common"
class BundleSessionBeforeBookingPageView extends ProductDetailPage {
    public pageContext: any

    async buildView(
        userContext: UserContext,
        isNotLoggedIn: boolean,
        bundleProducts?: BundleSessionSellableProduct[],
        product?: BundleSessionSellableProduct,
        patientsList?: Patient[],
        bundleoffers?: PackOffersResponse,
        userCache?: CacheHelper,
        useCultMemberShipForPayment?: boolean,
        membershipPaymentType?: MembershipPaymentType,
        payByMembershipService?: IPayByMembershipService) {
        const offerDetails = OfferUtil.getPackOfferAndPriceForCare(product, bundleoffers)
        const offerIds = _.map(offerDetails.offers, (offer: any) => { return offer.offerId })
        const appliedOffers: any[] = []
        const isPhysioOrLCPack = product.subCategoryCode === "PHYSIOTHERAPY" || product.subCategoryCode === "NUTRITIONIST"
        const isTransformPack = CareUtil.isTransformTenant(product.subCategoryCode)
        let howItWorksItem, whatsInPackItem
        product.infoSection.children.map(infoSection => {
            switch (infoSection.type) {
                case "PACK_STEPS": howItWorksItem = infoSection; break
                case "PACK_CONTENTS_DETAILED": whatsInPackItem = infoSection; break
            }
        })
        this.widgets.push(this.summaryWidget(product, offerIds, userContext.sessionInfo.userAgent, isPhysioOrLCPack))
        if (product.subCategoryCode === "PERSONAL_TRAINING") {
            this.widgets.push(this.getAvailableCentersWidget(product))
        }
        const productPricingWidget = this.getProductPricingWidget(userContext, bundleProducts, product.productCode, isPhysioOrLCPack, bundleoffers, appliedOffers, userContext.sessionInfo.userAgent)
        // Rearranging Offer and Price Widget only for Physio
        if (isPhysioOrLCPack) {
            this.widgets.push(productPricingWidget)
            if (!_.isEmpty(appliedOffers)) {
                this.widgets.push(getOffersWidget("Offers Applied", appliedOffers, userContext.sessionInfo.userAgent))
            }
        } else if (isTransformPack) {
            this.widgets.push(productPricingWidget)
            if (!_.isEmpty(appliedOffers)) {
                this.widgets.push(getOffersWidget("Offers Applied", appliedOffers, userContext.sessionInfo.userAgent))
            }
        } else {
            if (!_.isEmpty(appliedOffers)) {
                this.widgets.push(getOffersWidget("Offers Applied", appliedOffers, userContext.sessionInfo.userAgent))
            }
            this.widgets.push(productPricingWidget)
        }
        this.widgets.push(CareUtil.getWhyPackWidget(whatsInPackItem, userContext.sessionInfo.userAgent))
        this.widgets.push(CareUtil.getHowItWorksWidget(howItWorksItem, userContext.sessionInfo.userAgent))
        this.widgets = this.widgets.filter(Boolean)
        this.actions = this.getPreBookingActions(userContext, isNotLoggedIn, patientsList, product, offerIds, isPhysioOrLCPack, useCultMemberShipForPayment, membershipPaymentType)
        return this
    }
    private getAvailableCentersWidget(product: BundleSessionSellableProduct): CenterSelectionWidget {
        const action: Action = {
            actionType: "NAVIGATION",
            url: `curefit://selectcarecenter?productId=${product.productCode}&viewOnly=true`
        }
        const centerSelectionWidget: CenterSelectionWidget = {
            title: product.subCategoryCode === "PERSONAL_TRAINING" ? "View Centres (At Select Centres Only)" : "View Available Centres",
            canChangeCenter: true,
            widgetType: "CENTER_PICKER_WIDGET",
            action: action
        }
        return centerSelectionWidget
    }

    private getProductPricingWidget(userContext: UserContext, bundleProducts: BundleSessionSellableProduct[], sellableProductId: string, isPhysioOrLCPack: boolean, bundleoffers?: PackOffersResponse, appliedOffers?: any, userAgent?: UserAgentType): ProductPricingWidget {
        const recurringSection: PricingWidgetRecurringValue[] = []
        bundleProducts.forEach((product: BundleSessionSellableProduct) => {
            const offerDetails = OfferUtil.getPackOfferAndPriceForCare(product, bundleoffers)
            if (product.productCode === sellableProductId && offerDetails && offerDetails.offers) {
                appliedOffers.push(...offerDetails.offers)
            }
            const isLivePTPack = product.subCategoryCode === "LIVE_PERSONAL_TRAINING"
            product.listingPrice = offerDetails.price.listingPrice
            const perSessionPrice = Math.floor(product.listingPrice / product.infoSection.numberOfSessions)
            const recurringSectionItem: PricingWidgetRecurringValue = {
                title: isLivePTPack ? product.infoSection.sellingTitle : `${product.infoSection.numberOfSessions} ${pluralizeStringIfRequired("Session", product.infoSection.numberOfSessions)}`,
                subTitle: isPhysioOrLCPack ? "" : (product.duration > 0 && `Validity: ${product.duration} days`),
                price: {
                    mrp: product.mrp,
                    listingPrice: product.listingPrice,
                    showPriceCut: product.listingPrice < product.mrp,
                    currency: offerDetails.price.currency
                },
                priceMeta: `${RUPEE_SYMBOL}${perSessionPrice}/Session`,
                productId: product.productCode,
                selected: product.productCode === sellableProductId,
                action: {
                    actionType: "NAVIGATION",
                    meta: {
                        sellableProductId: product.productCode
                    }
                }
            }
            recurringSection.push(recurringSectionItem)
        })
        return {
            widgetType: "PRODUCT_PRICING_WIDGET",
            header: {
                title: "SELECT A PACK"
            },
            sections: [{
                type: "RECURRING",
                value: recurringSection
            }],
            orientation: isPhysioOrLCPack && userAgent === "DESKTOP" ? "RIGHT" : undefined
        }
    }

    private summaryWidget(product: BundleSessionSellableProduct, offerIds?: string[], userAgent?: UserAgentType, isPhysioOrLCPack?: boolean): WidgetView {
        const summaryWidget: WidgetView & {
            productId: string;
            title: string;
            subTitle: string
            image: string;
            offerIds?: string[],
            hasDividerBelow: boolean,
            actions?: Action[],
            breadcrumb?: { text: string, link?: string }[]
            orientation?: Orientation,
        } = {
            widgetType: userAgent !== "APP" && isPhysioOrLCPack ? "HEALTHCHECKUP_SUMMARY_WIDGET" : "PRODUCT_SUMMARY_WIDGET",
            subTitle: product.infoSection.packDescription,
            title: product.infoSection.packTitle,
            productId: product.productCode,
            image: product.heroImageUrl,
            offerIds: offerIds,
            hasDividerBelow: false,
            orientation: userAgent === "DESKTOP" && isPhysioOrLCPack ? "RIGHT" : undefined,
            breadcrumb: userAgent === "DESKTOP" && isPhysioOrLCPack ? [{ text: "Home", link: "/" }, { text: "Care", link: "/care" }, { text: product.infoSection.packTitle }] : [],
            actions: userAgent !== "APP" ? this.actions : []
        }
        return summaryWidget
    }
    private getPreBookingActions(userContext: UserContext, isNotLoggedIn: boolean, patientsList: Patient[], sellableProduct: BundleSessionSellableProduct, offerIds: string[], isPhysioOrLCPack: boolean, useCultMemberShipForPayment: boolean, membershipPaymentType: MembershipPaymentType): Action[] {
        if (isNotLoggedIn === true) {
            return [
                {
                    actionType: "SHOW_ALERT_MODAL",
                    title: `Buy ${sellableProduct.infoSection.numberOfSessions} ${pluralizeStringIfRequired("Session", sellableProduct.infoSection.numberOfSessions)}`,
                    meta: {
                        title: "Login Required!",
                        subTitle: "Please login to continue",
                        actions: [{ actionType: "LOGOUT", title: "Login" }]
                    }
                }
            ]
        } else {
            const actionTitle = sellableProduct.subCategoryCode === "LIVE_PERSONAL_TRAINING" ? `Buy ${sellableProduct.infoSection.sellingTitle}` : `Buy ${sellableProduct.infoSection.numberOfSessions} ${pluralizeStringIfRequired("Session", sellableProduct.infoSection.numberOfSessions)}`
            let actionString = CareUtil.isTransformTenant(sellableProduct.subCategoryCode) ? `curefit://tf_checkout?productId=${sellableProduct.productCode}` : `curefit://carecartcheckout?productId=${sellableProduct.productCode}&subCategoryCode=${sellableProduct?.subCategoryCode}`
            if (!_.isEmpty(offerIds)) {
                actionString += `&offerIds=${offerIds.join(",")}`
            }
            const action: Action = {
                title: actionTitle,
                actionType: "NAVIGATION",
                url: actionString,
                meta: {
                    optionMeta: {
                        cultMemberShipPaymentOptions: {
                            useCultMemberShipForPayment: useCultMemberShipForPayment,
                            membershipPaymentType: membershipPaymentType
                        }
                    }
                }
            }
            let pageAction: Action
            if (isPhysioOrLCPack) {
                pageAction = CareUtil.getPatientSelectionModalAction(patientsList, action, actionTitle)
            } else if (sellableProduct.subCategoryCode === "MIND_THERAPY") {
                const calloutText = "Our Therapy packs are available only for yourself. This ensures data security and privacy."
                pageAction = CareUtil.getSelfPatientSelectionModalAction(patientsList, action, actionTitle, calloutText, { formUserType: "THERAPY_USER" })
            } else if (sellableProduct.subCategoryCode === "LIVE_PERSONAL_TRAINING") {
                pageAction = AppUtil.isSkipCreatePatientSupported(userContext) ? action : CareUtil.getBundleSessionPurchaseAction(userContext, undefined, patientsList, action, actionTitle, sellableProduct.subCategoryCode)
            } else {
                pageAction = CareUtil.getBundleSessionPurchaseAction(userContext, undefined, patientsList, action, actionTitle, sellableProduct.subCategoryCode)
            }
            return [pageAction]
        }

    }
}

export function getPreBookingActions(userContext: UserContext, numberOfSessions: number, showPatientSelectionModal: boolean, offerIds: string[], url: string, patientsList: Patient[]): any[] {
    if (!userContext.sessionInfo.isUserLoggedIn) {
        return [
            {
                actionType: "SHOW_ALERT_MODAL",
                title: `Book ${numberOfSessions} Sessions`,
                meta: {
                    title: "Login Required!",
                    subTitle: "Please login to continue",
                    actions: [{ actionType: "LOGOUT", title: "Login" }]
                }
            }
        ]
    } else {
        const actionTitle = `Book ${numberOfSessions} ${numberOfSessions > 1 ? "Sessions" : "Session"}`
        if (!_.isEmpty(offerIds)) {
            url += `&offerIds=${offerIds.join(",")}`
        }
        const action: Action = {
            title: actionTitle,
            actionType: "NAVIGATION",
            url: url,
        }
        if (showPatientSelectionModal) {
            const pageAction = CareUtil.getPatientSelectionModalAction(patientsList, action, actionTitle)
            return [pageAction]
        } else {
            return [action]
        }
    }
}

export default BundleSessionBeforeBookingPageView
