import { inject, injectable } from "inversify"
import { ICrudKeyValue, REDIS_TYPES, IMultiCrudKeyValue } from "@curefit/redis-utils"
import { BASE_TYPES, Logger } from "@curefit/base"



export interface CenterBookingCache {
    fetchExistingBookingsData(centerId: number, cultClassDate: string): Promise<any>
    isDailyLimitBreachedOnCenter(centerId: number, cultClassDate: string): Promise<any>
}

export interface CenterBookingCacheData {
    bookings: number
    cancellations: number
}

const CENTER_RESTRICTION = "CENRESTR:"

@injectable()
export class CenterBookingCacheImpl implements CenterBookingCache {

    private crudDao: ICrudKeyValue

    private JPMC_CENTER_DAILY_LIMIT_MAPPING: Map<number, number> = new Map<number, number>([
        [293, 10],
        [393, 10],
        [1031, 3],
        [294, 2]
    ])

    constructor(
        @inject(REDIS_TYPES.MultiCrudKeyValueDao) private multiCrudKeyValueDao: IMultiCrudKeyValue,
        @inject(BASE_TYPES.ILogger) private logger: Logger
    ) {
        this.crudDao = this.multiCrudKeyValueDao.getICrudKeyValue("CULT-CACHE")
    }



    async isDailyLimitBreachedOnCenter(centerId: any, cultClassDate: string): Promise<any> {
        const obj = await this.fetchExistingBookingsData(parseInt(centerId), cultClassDate)
        const actualBookings: number = Math.max(obj.bookings - obj.cancellations , 0)
        if ( actualBookings >= this.JPMC_CENTER_DAILY_LIMIT_MAPPING.get(centerId)) {
            return true
        }
        return false
    }

     async fetchExistingBookingsData(centerId: number, cultClassDate: string): Promise<CenterBookingCacheData> {
        const existingCacheDataStr = await this.crudDao.read(this.getCenterBookingCacheKey(centerId, cultClassDate))
        this.logger.info(`CenterBookingCacheServiceImpl:fetchExistingBookingsData show value for existing bookings ${existingCacheDataStr}}`)
        let bookings: number
        let cancellations: number
        if (!existingCacheDataStr) {
            return {bookings: 0, cancellations: 0}
        } else {
            const existingCacheData: CenterBookingCacheData = JSON.parse(existingCacheDataStr)
            this.logger.info(`CenterBookingCacheServiceImpl:fetchExistingBookingsData show value for existing existingCacheData ${JSON.stringify(existingCacheData)}}`)
            bookings = existingCacheData.bookings
            cancellations = existingCacheData.cancellations
        }
        return {bookings, cancellations}
    }

    private getCenterBookingCacheKey(centerId: number, cultClassDate: string): string {
        const key = `${CENTER_RESTRICTION}${cultClassDate}_${centerId.toString()}`
        this.logger.info(`CenterBookingCacheServiceImpl: getCenterBookingCacheKey::: ${key}`)
        return key
    }

}