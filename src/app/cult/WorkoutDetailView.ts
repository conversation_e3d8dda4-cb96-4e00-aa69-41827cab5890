import { CultWorkout, CultWorkoutCategory } from "@curefit/cult-common"
import { ProductType } from "@curefit/product-common"
import { User } from "@curefit/user-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import {
    AboutProductWidget,
    Action,
    ActionCard,
    InfoCard,
    ProductColumnTagWidget,
    ProductDetailPage,
    ProductRowTagWidget,
    ProductSummaryWidget
} from "../common/views/WidgetView"
import { ActionUtil as EtherActionutil } from "@curefit/base-utils"
import * as _ from "lodash"
import { CdnUtil } from "@curefit/util-common"
import { UserContext } from "@curefit/userinfo-common"
import { ActionUtil } from "../util/ActionUtil"
import AppUtil from "../util/AppUtil"
import { User as UserModel } from "@curefit/user-common/dist/src/User"
import { ICultBusiness } from "./CultBusiness"
import { CULT_FREE_CLASS_BOOK } from "../util/CultUtil"

abstract class BaseWorkoutDetailView extends ProductDetailPage {
    constructor() {
        super()
    }

    protected getPageAction(workout: CultWorkout | CultWorkoutCategory, productType: ProductType, centerId: string, isNewClassBookingSupported: boolean, trialClassBooking: boolean): Action {
        return {
            actionType: "SHOW_ALERT_MODAL",
            title: trialClassBooking ? CULT_FREE_CLASS_BOOK : "Book Class",
            meta: {
                title: "Booking closed",
                subTitle:
                    "Due to Covid-19 situation, our cult centers are closed and we are not taking any bookings.",
                actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
            }
        }
        if (!_.isEmpty(workout.centers)) {
            const centerHasPreferredWorkout = _.find(workout.centers, center => {
                if (centerId === center.id.toString())
                    return true
                return false
            })
            centerId = centerHasPreferredWorkout ? centerId : workout.centers[0].id.toString()
        }
        const action: Action = {
            actionType: "NAVIGATION",
            title: trialClassBooking ? CULT_FREE_CLASS_BOOK : "Book Class",
            url: EtherActionutil.getBookCultClassUrl(productType, isNewClassBookingSupported, "workoutpage", workout.id.toString())
        }
        return action
    }

    protected getWorkoutSummayWidget(workout: CultWorkout | CultWorkoutCategory): ProductSummaryWidget {
        const document = _.find(workout.documents, { tagName: "PRODUCT_BNR" })
        const productSummaryWidget: ProductSummaryWidget = {
            widgetType: "PRODUCT_SUMMARY_WIDGET",
            title: workout.name,
            subTitle: "",
            image: document ? "/" + document.URL : undefined
        }
        return productSummaryWidget
    }

    protected getWorkoutIntroWidget(workout: CultWorkout | CultWorkoutCategory, userAgent: UserAgent): AboutProductWidget {
        let videoDoc
        let thumbDoc
        if (userAgent === "APP") {
            thumbDoc = _.find(workout.documents, doc => doc.tagName === "M_VIDEO_THUMBNAIL")
            videoDoc = _.find(workout.documents, { tagName: "M_VIDEO" })
        } else {
            thumbDoc = _.find(workout.documents, doc => doc.tagName === "D_VIDEO_THUMBNAIL")
            videoDoc = _.find(workout.documents, { tagName: "D_VIDEO" })
        }
        if (_.isEmpty(videoDoc) || _.isEmpty(thumbDoc) || _.isEmpty(videoDoc.URL) || _.isEmpty(thumbDoc.URL)) {
            return undefined
        }
        const workoutIntroWidget: AboutProductWidget = {
            widgetType: "ABOUT_PRODUCT_WIDGET",
            header: { title: `About ${workout.name}` },
            video: videoDoc ? `curefit://videoplayer?videoUrl=${encodeURIComponent(videoDoc.URL)}&absoluteVideoUrl=${encodeURIComponent(CdnUtil.getCdnUrl(videoDoc.URL))}` : undefined,
            image: thumbDoc ? "/" + thumbDoc.URL : undefined,
            description: workout.description
        }
        return workoutIntroWidget
    }

    protected getWorkoutBenefitsWidget(workout: CultWorkout | CultWorkoutCategory, userContext?: UserContext, productType?: ProductType): ProductRowTagWidget {
        const benefits: InfoCard[] = []
        workout.benefits.forEach(benefit => {
            benefits.push({ title: benefit })
        })
        let suffix = ""

        if (AppUtil.isWeb(userContext) && productType === "MIND") {
            if (workout.name) {
                suffix = ` of ${workout.name}`
            }
        }

        const workoutBenefitsWidget: ProductRowTagWidget = {
            widgetType: "PRODUCT_ROW_TAG_WIDGET",
            header: { title: `Benefits${suffix}` },
            items: benefits
        }
        return workoutBenefitsWidget
    }

    protected getAvailableCentersWidget(workout: CultWorkout | CultWorkoutCategory, productType: ProductType, isNewClassBookingSupported: boolean): ProductColumnTagWidget {
        const centers: ActionCard[] = []
        workout.centers.forEach(center => {
            const url = EtherActionutil.getBookCultClassUrl(productType, isNewClassBookingSupported, "workoutPage", workout.id.toString())

            centers.push({ title: center.name, action: url })
        })
        const availableCentersWidget: ProductColumnTagWidget = {
            widgetType: "PRODUCT_COLUMN_TAG_WIDGET",
            header: { title: `Available at these ${productType === "FITNESS" ? "cult.fit" : "mind.fit"} centres` },
            items: centers
        }
        return availableCentersWidget
    }
}

export class WorkoutDetailView extends BaseWorkoutDetailView {
    constructor(workout: CultWorkout, productType: ProductType, userContext: UserContext, user?: User, subUsers?: UserModel[], subProductType?: ProductType, action?: Action) {
        super()
        const isNewClassBookingSupported = AppUtil.isNewClassBookingSuppoted(userContext, user.isInternalUser)
        this.widgets.push(this.getWorkoutSummayWidget(workout))
        const introWidget = this.getWorkoutIntroWidget(workout, userContext.sessionInfo.userAgent)
        if (!_.isEmpty(introWidget)) {
            this.widgets.push(introWidget)
        }
        if (!_.isEmpty(workout.benefits))
            this.widgets.push(this.getWorkoutBenefitsWidget(workout, userContext, productType))
        // Showing center only for standard workout and not for primary workous like S&C
        if (!_.isEmpty(workout.centers) && workout.centers.length <= 6) {
            this.widgets.push(this.getAvailableCentersWidget(workout, productType, isNewClassBookingSupported))
        }
        if (subProductType !== "LIVE_PERSONAL_TRAINING") {
            this.actions = [{
                actionType: "SHOW_ALERT_MODAL",
                title: "Book a class",
                meta: {
                    title: "Booking closed",
                    subTitle:
                        "Due to Covid-19 situation, our cult centers are closed and we are not taking any bookings.",
                    actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
                }
            }]
            // if (workout.ageCategory !== "JUNIOR") {
            //     this.actions = [ActionUtil.getBookClassAction(workout, productType, isNewClassBookingSupported, "workoutPage")]
            // } else if (workout.ageCategory === "JUNIOR" && AppUtil.isChildUserBookingSupported(userContext)) {
            //     const action = ActionUtil.getCultJuniorClassBookingAction(userContext, productType, user, subUsers, "workoutPage")
            //     action.title = "Book a class"
            //     this.actions = [action]
            // }
        } else if (!_.isEmpty(action)) {
            this.actions = [action]
        }
    }
}

export class WorkoutCategoryDetailView extends BaseWorkoutDetailView {
    constructor() {
        super()
    }
    async buildView(workoutCategory: CultWorkoutCategory, productType: ProductType, userContext: UserContext, user?: User, cultBusiness?: ICultBusiness) {
        const isNewClassBookingSupported = AppUtil.isNewClassBookingSuppoted(userContext, user.isInternalUser)
        this.widgets.push(this.getWorkoutSummayWidget(workoutCategory))
        if (workoutCategory.Workouts.length > 1) {
            workoutCategory.Workouts.forEach(workout => {
                const introWidget = this.getWorkoutIntroWidget(workout, userContext.sessionInfo.userAgent)
                if (!_.isEmpty(introWidget)) {
                    this.widgets.push(introWidget)
                }
            })
        } else {
            const introWidget = this.getWorkoutIntroWidget(workoutCategory, userContext.sessionInfo.userAgent)
            if (!_.isEmpty(introWidget)) {
                this.widgets.push(introWidget)
            }
        }
        if (!_.isEmpty(workoutCategory.benefits))
            this.widgets.push(this.getWorkoutBenefitsWidget(workoutCategory, userContext, productType))
        // Showing center only for standard workout and not for primary workous like S&C
        if (!_.isEmpty(workoutCategory.centers) && workoutCategory.centers.length <= 6)
            this.widgets.push(this.getAvailableCentersWidget(workoutCategory, productType, isNewClassBookingSupported))
        const centerId = productType === "FITNESS" ? userContext.sessionInfo.sessionData.cultCenterId : userContext.sessionInfo.sessionData.mindCenterId
        const { cult, mind } = await cultBusiness.isBookFreeCultClassSupported(userContext)
        const freeTrial = productType === "MIND" ? mind : cult
        this.actions = [this.getPageAction(workoutCategory, productType, centerId, isNewClassBookingSupported, freeTrial)]
        return this
    }
}
