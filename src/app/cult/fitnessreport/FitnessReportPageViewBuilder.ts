import { inject, injectable } from "inversify"
import {
    ClassMissedBannerWidget,
    FitnessReportPageView,
    FitnessReportSummaryCardWidget,
    GraphValue,
    InfoSection,
    ReportGraphWidget,
    ReportMetric,
    ReportMetricDetailWidget,
    ReportType,
    Tag,
    TagViewWidget
} from "./FitnessReportPageView"
import {
    Action,
    ClpCalloutWidget,
    DescriptionWidget,
    ProductFeedbackWidget,
    WidgetView
} from "../../common/views/WidgetView"
import { UserContext } from "@curefit/userinfo-common"
import {
    Benefit,
    BodyPart,
    FitnessReport,
    FitnessReportResponse,
    GraphEntry,
    ImageFilter,
    MilestoneCategory,
    UserMilestoneWithMessage,
    WorkoutCount
} from "@curefit/cult-common"
import { appendZeroInSingleDigitNumber } from "../../util/StringUtil"
import { CdnUtil, TimeUtil } from "@curefit/util-common"
import {
    BannerCarouselWidget,
    HeaderWidget,
    ProductListWidget,
    QuickLinkWidget,
    TypographyWidget
} from "../../page/PageWidgets"
import * as _ from "lodash"
import { IMetricServiceClient as IMetricService, METRIC_TYPES } from "@curefit/metrics"
import { getMeasurementOptions } from "../../util/UnitUtil"
import { CULT_CLIENT_TYPES, ICultServiceOld as ICultService } from "@curefit/cult-client"
import * as momentTz from "moment-timezone"
import CultUtil, { ACHIEVEMENT_ICON, transformCultSummary } from "../../util/CultUtil"
import { CacheHelper } from "../../util/CacheHelper"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { User } from "@curefit/user-common"
import FitnessReportPageConfig from "./FitnessReportPageConfig"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { FEEDBACK_MONGO_TYPES, IFeedbackReadOnlyDao } from "@curefit/feedback-mongo"
import { Feedback } from "@curefit/feedback-common"
import FeedbackPageConfigV2Cache from "../../ugc/FeedbackPageConfigV2Cache"
import CultPackDetailViewBuilder from "../../pack/CultPackDetailViewBuilder"
import LiveUtil from "../../util/LiveUtil"
import AppUtil from "../../util/AppUtil"
import { ProductType } from "@curefit/product-common"
import { CFServiceInterfaces } from "../../page/vm/ServiceInterfaces"
import { LIVE_PT_SNC_PRODUCT_ID, LIVE_SGT_SNC_PRODUCT_ID } from "../../util/CareUtil"

const WEIGHT_METRIC_ID = 3, HEIGHT_METRIC_ID = 2

@injectable()
class FitnessReportPageViewBuilder {
    constructor(@inject(METRIC_TYPES.MetricServiceClient) private metricService: IMetricService,
        @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
        @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
        @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
        @inject(FEEDBACK_MONGO_TYPES.FeedbackReadOnlyDao) private feedbackDao: IFeedbackReadOnlyDao,
        @inject(CUREFIT_API_TYPES.FeedbackPageConfigV2Cache) private feedbackPageConfigV2Cache: FeedbackPageConfigV2Cache,
        @inject(CUREFIT_API_TYPES.CultFitPackDetailViewBuilder) private cultFitPackDetailViewBuilder: CultPackDetailViewBuilder,
        @inject(CUREFIT_API_TYPES.FitnessReportPageConfig) private fitnessReportPageConfig: FitnessReportPageConfig,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces) {
    }

    async buildWeeklyReportView(
        userContext: UserContext,
        fitnessReportData: FitnessReportResponse,
        reportType: ReportType,
        startDate: string,
        productType?: ProductType
    ): Promise<FitnessReportPageView> {
        let widgets: WidgetView[] = []
        const tz = userContext.userProfile.timezone
        let actions: Action[] = undefined
        const { fitnessReport: reportData, weightHistory, classesAttendedHistory, info, launchWeek, previousActiveWeekStartDate, nextActiveWeekStartDate } = fitnessReportData
        const currentWeekStartDate = momentTz.tz(TimeUtil.getCurrentWeekStartDate(), tz).format("YYYY-MM-DD")
        const header = {
            pageTitle: "Weekly Reports",
            meta: {
                latestWeekStartDate: currentWeekStartDate,
                launchWeekStartData: launchWeek.startDate,
                previousActiveWeekStartDate: previousActiveWeekStartDate,
                nextActiveWeekStartDate: nextActiveWeekStartDate,
            }
        }
        const isClassAttendedInPast = this.checkIfClassAttended(classesAttendedHistory)
        const isInternationalApp = AppUtil.isInternationalApp(userContext)
        if (reportData) {
            const feedback: Feedback = await this.feedbackDao.findOne({ shipmentId: reportData.id, productType: "FITNESS_REPORT" })
            // const userProTipWithMessages = await this.cultFitService.getUserProTips(reportData.startDate, reportData.endDate, userContext.userProfile.userId, "CUREFIT_API")
            const userMilestoneWithMessages = await this.cultFitService.getUserMilestoneWithMessages(reportData.startDate, reportData.endDate, userContext.userProfile.userId, "CUREFIT_API")
            const userDetails = await this.userCache.getUser(userContext.userProfile.userId)
            widgets.push(this.getReportShareCardWidget(userContext, reportData, reportType, userDetails, userMilestoneWithMessages))
            widgets.push(this.getClassesAttendedDetailWidget(reportData, info))
            const classAttendedMilestoneWidget = this.getClassesAttendedMilestoneWidget(userMilestoneWithMessages)
            if (classAttendedMilestoneWidget) {
                widgets.push(classAttendedMilestoneWidget)
            }
            isClassAttendedInPast && widgets.push(this.getClassesGraphWidget(userContext, classesAttendedHistory, reportData))
            widgets.push(this.getHeaderWidget())
            widgets.push(this.getWorkoutCountsWidget(reportData.workoutCounts))
            if (reportData.bodyParts || reportData.benefits) {
                widgets.push(this.getBenefitsWidget(reportData.bodyParts, reportData.benefits, info))
            }
            // widgets.push(LiveUtil.getProTipWidget("weekly_report_pro_tip"))
            // if (!isInternationalApp) {
            //     const isWeightAvailableInPast = this.checkIfWeightAvailable(weightHistory)
            //     widgets.push(await this.getWeightDetailWidget(reportData, info, userContext, userDetails.isInternalUser))
            //     isWeightAvailableInPast && widgets.push(this.getWeightGraphWidget(userContext, weightHistory, reportData, info))
            // }
            const weightLossMilestoneWidget = this.getWeightLossMilestoneWidget(userMilestoneWithMessages)
            if (weightLossMilestoneWidget && !isInternationalApp) {
                widgets.push(weightLossMilestoneWidget)
            } else {
                widgets[widgets.length - 1]["dividerType"] = "LARGE"
            }
            if (feedback && (feedback.rating === "NOT_RATED" || feedback.rating === "DISMISSED")) {
                const { ratings } = await this.feedbackPageConfigV2Cache.getFeedbackObjV2(feedback)
                const icons = ratings.map(rating => rating.icon)
                widgets.push(new ProductFeedbackWidget(await this.feedbackPageConfigV2Cache.getQuestionV2(feedback), feedback.feedbackId, undefined, icons, "CENTER", "Your feedback will help us serve you better"))
            }
        } else {
            const lastClassAttendedWeek = CultUtil.getLastClassAttended(classesAttendedHistory)
            const actionUrl = isInternationalApp
                ? "curefit://liveclassbooking?productType=FITNESS&isLiveBookingPage=true"
                : await this.getActionUrl(productType, userContext)
            if (startDate === currentWeekStartDate && lastClassAttendedWeek >= 0) {
                widgets.push(await this.getClassesMissedBannerWidget(userContext, classesAttendedHistory))
                const fitnessRoutineSteps = this.getFitnessRoutineSteps(classesAttendedHistory)
                if (fitnessRoutineSteps) {
                    widgets.push({ ...fitnessRoutineSteps, dividerType: "LARGE" })
                }
                const videoWidget = this.getVideoWidget(classesAttendedHistory)
                if (videoWidget) {
                    widgets.push({ ...videoWidget, pageDividerType: "LARGE" })
                }
                const headerTextForLastSixWeekReport = "YOU WERE DOING SO WELL \nDON’T LOSE THE PROGRESS YOU MADE"
                if (lastClassAttendedWeek === 0) {
                    isClassAttendedInPast && widgets.push(this.getClassesGraphWidget(userContext, classesAttendedHistory, undefined, headerTextForLastSixWeekReport, true))
                }
                widgets.push(this.getTypographyWidget("WE WANT YOU BACK", "WE ARE CULT"))
                actions = [{
                    actionType: "NAVIGATION",
                    title: "BOOK A CLASS",
                    url: actionUrl
                }]
            } else {
                widgets.push(await this.getClassesMissedBannerWidget(userContext, classesAttendedHistory, "MEMBER_PREV_WEEK_ZERO_STATE"))
                const fitnessRoutineSteps = this.getFitnessRoutineSteps(classesAttendedHistory, true)
                if (fitnessRoutineSteps) {
                    widgets.push({ ...fitnessRoutineSteps, dividerType: "LARGE" })
                }
                widgets.push(this.getTypographyWidget("GET BACK ON TRACK", "BE FEARLESS "))
                actions = [{
                    actionType: "NAVIGATION",
                    title: "BOOK A CLASS",
                    url: actionUrl
                }]
            }
        }
        widgets = widgets.filter(v => !_.isNil(v) && !_.isNull(v))
        // const currentWeekToastText = (currentWeekStartDate === startDate) ? "This report is not yet complete." : ""
        return new FitnessReportPageView(widgets, header, reportType, startDate, actions)
    }

    async getActionUrl(
        productType: ProductType,
        userContext: UserContext,
    ): Promise<string> {
        if (productType === "LIVE_SGT") {
            const newAction = <Action> await this.serviceInterfaces.careBusiness.getLivePTSessionBookAction(userContext,
                {productId: LIVE_SGT_SNC_PRODUCT_ID, subCategoryCode: "LIVE_SGT"})
            return newAction.url
        } else if (productType === "LIVE_PERSONAL_TRAINING") {
            const newAction = <Action> await this.serviceInterfaces.careBusiness.getLivePTSessionBookAction(userContext,
                {productId: LIVE_PT_SNC_PRODUCT_ID, subCategoryCode: "LIVE_PERSONAL_TRAINING"})
            return newAction.url
        }
        return "curefit://classbookingv2?productType=FITNESS"
    }

    getHeaderWidget(): HeaderWidget {
        const headerWidget = new HeaderWidget({ title: "THIS WEEK YOU DID" })
        headerWidget.headerStyle = { color: "#f2be6f", fontSize: 30, fontFamily: "BrandonText-Black", marginLeft: 0, lineHeight: 33 }
        headerWidget.style = { marginTop: 20, marginHorizontal: 20 }
        return headerWidget
    }

    getReportTypeText(reportType: string) {
        switch (reportType) {
            case "WEEKLY":
                return "WEEKLY REPORT"
        }
    }

    getReportShareCardWidget(userContext: UserContext, reportData: FitnessReport, reportType: ReportType, userDetails: User, userMilestoneWithMessages: UserMilestoneWithMessage[]): FitnessReportSummaryCardWidget {
        const tz = userContext.userProfile.timezone
        let calloutText = ""
        if (!_.isEmpty(userMilestoneWithMessages)) {
            const classAttendedMilestone = userMilestoneWithMessages.find((item) => item.userMilestoneCategory === MilestoneCategory.TOTAL_CLASSES_ATTENDED)
            if (classAttendedMilestone) {
                calloutText = classAttendedMilestone.userMilestoneMessage
            } else {
                const weightLossMilestone = userMilestoneWithMessages.find((item) => item.userMilestoneCategory === MilestoneCategory.WEEKLY_WEIGHT_LOSS)
                if (weightLossMilestone) {
                    calloutText = weightLossMilestone.userMilestoneMessage
                }
            }
        }
        const shareAction: Action = {
            actionType: "SHARE_SCREENSHOT",
            meta: {
                shareTitle: `Fitness Report`,
                shareMessage: "Here's how my week went! You too can workout from home on https://cure.app.link/rN3QncFzFkb !\n#WeAreCULT"
            }
        }
        const userName = userDetails && userDetails.firstName ? userDetails.firstName.toUpperCase() : ""
        return {
            widgetType: "FITNESS_REPORT_SUMMARY_CARD_WIDGET",
            imageUrl: reportData.imageUrl,
            imageFilter: reportData.imageFilter || ImageFilter.KUDOS_USER,
            startDate: reportData.startDate,
            endDate: reportData.endDate,
            reportType: reportType,
            classAttended: appendZeroInSingleDigitNumber(reportData.classesAttended),
            caloriesBurnt: appendZeroInSingleDigitNumber(reportData.caloriesBurned),
            shareAction: shareAction,
            dividerType: "LARGE",
            startDateDisplayText: momentTz.tz(reportData.startDate, tz).format("DD MMM"),
            endDateDisplayText: momentTz.tz(reportData.endDate, tz).format("DD MMM"),
            reportTypeDisplayText: this.getReportTypeText(reportType),
            achievement: calloutText,
            shareIcon: userContext.sessionInfo.osName === "android" ? "/image/pulse/share-icon-android.png" : "/image/pulse/share-icon.png",
            fitnessReportFilters: CultUtil.getFitnessReportFilter(userName),
            reportId: reportData.id,
            analyticsData: {
                startDate: reportData.startDate,
                type: "fitness_history"
            },
        }
    }

    getClassesAttendedDetailWidget(reportData: FitnessReport, info: any): ReportMetricDetailWidget {
        const metricSection: ReportMetric = {
            metricTitle: "CLASSES\nATTENDED",
            metricDisplayValue: `${appendZeroInSingleDigitNumber(reportData.classesAttended)}`,
            metricTitleColor: "#ff7f82",
        }
        if (reportData.classesAttended === reportData.maxClassesAttended) {
            metricSection.tag = "YOUR BEST"
        }

        const classAttendedMetricDetailWidget: ReportMetricDetailWidget = {
            widgetType: "REPORT_METRIC_DETAIL_WIDGET",
            metricSection: metricSection,
        }
        if (reportData.percentile) {
            classAttendedMetricDetailWidget["infoSection"] = {
                header: "YOU ARE\nBETTER THAN",
                dataText: `${reportData.percentile}%`,
                subText: "people",
                info: { actionType: "SHOW_ALERT_MODAL", meta: { ...info.CLASS_ATTENDED_PERCENTILE_INFO, actions: [{ title: "OK", actionType: "HIDE_ALERT_MODAL" }] }, }
            }
        }
        return classAttendedMetricDetailWidget
    }

    checkIfClassAttended(classesAttendedHistory: GraphEntry[]) {
        const classDetails = classesAttendedHistory.find((item) => {
            return item.value > 0
        })
        return classDetails ? true : false
    }

    checkIfWeightAvailable(weightHistory: GraphEntry[]) {
        const weightDetails = weightHistory.find((item) => {
            return item.value > 0
        })
        return weightDetails ? true : false
    }

    getClassesGraphWidget(userContext: UserContext, classesAttendedHistory: GraphEntry[], reportData?: FitnessReport, headerText?: string, showDivider?: boolean): ReportGraphWidget {
        let footer: InfoSection
        if (reportData) {
            footer = {
                header: "YOU’VE BEEN REGULAR FOR",
                dataText: `${appendZeroInSingleDigitNumber(reportData.streak)} ${reportData.streak > 1 ? "WEEKS" : "WEEK"}`,
                subText: reportData.streak > 1 ? "in a row" : ""
            }
        }
        const graphData: GraphValue[] = classesAttendedHistory.map((item, index) => {
            return {
                label: TimeUtil.formatDateStringInTimeZone(item.startDate, userContext.userProfile.timezone, "DD MMM"),
                value: item.value,
                isCurrentValue: (index === (classesAttendedHistory.length - 1)),
                colors: ["#ff8181", "#ff9c75"]
            }
        })
        const classesAttendedGraphWidget: ReportGraphWidget = {
            widgetType: "REPORT_GRAPH_WIDGET",
            header: headerText || "YOUR LAST 6 WEEKS",
            backgroundColor: "#f5f5f5",
            graphData: graphData,
            footer: footer
        }
        if (showDivider) {
            classesAttendedGraphWidget["dividerType"] = "LARGE"
        }
        return classesAttendedGraphWidget
    }

    getVideoWidget(classesAttendedHistory: GraphEntry[]) {
        const lastClassAttendedWeek: string | number = CultUtil.getLastClassAttended(classesAttendedHistory)
        const classMissedData = this.fitnessReportPageConfig.stateData[CultUtil.getClassConfigByLastClassAttended(lastClassAttendedWeek)]
        const relativeVideoUrl = classMissedData.videoUrl
        if (relativeVideoUrl) {
            let videoPlayerUrl = "curefit://videoplayer?videoUrl=" + encodeURIComponent(relativeVideoUrl)
            videoPlayerUrl += "&absoluteVideoUrl=" + encodeURIComponent(CdnUtil.getCdnUrl(relativeVideoUrl))
            const videoThumbnail = classMissedData.videoThumbnail
            return {
                ...new BannerCarouselWidget("345:202", [{
                    id: "FITNESS_VIDEO",
                    image: videoThumbnail,
                    action: {
                        "actionType": "NAVIGATION",
                        "url": videoPlayerUrl
                    }
                }], {
                    bannerWidth: 375,
                    bannerHeight: 202,
                    noVerticalPadding: false,
                    backgroundColor: "white",
                    edgeToEdge: true,
                    showVideoPlayIcon: true
                }, 1, true, true), header: {
                    title: "GET BACK TO FITNESS!",
                    titleStyle: { fontSize: 12, fontWeight: "bold", color: "#5e5e5e" }
                }, footer: {
                    style: { paddingBottom: 0 },
                    title: "It’s been a while and it’s alright",
                    titleStyle: { fontSize: 20, color: "#000000", lineHeight: 25, paddingBottom: 0 }
                }
            }
        }
        return null
    }

    getWorkoutCountsWidget(workoutCounts: WorkoutCount[]): TagViewWidget {
        const tags = workoutCounts.map(workout => {
            return {
                title: workout.name,
                value: appendZeroInSingleDigitNumber(workout.count).toString(),
                disabled: false
            }
        })
        return {
            widgetType: "TAG_VIEW_WIDGET",
            tags: tags,
            tagValueType: "TEXT",
            hasShadow: true,
            noPaddingLeft: true
        }
    }

    getBenefits(benefits: Benefit[]) {
        const fitnessBenefits: string[] = []
        benefits.forEach((benefit, index) => {
            if (index < 2) {
                fitnessBenefits.push(benefit.name.toUpperCase())
            }
        })
        return fitnessBenefits.join(" +\n")
    }

    getBenefitsWidget(bodyParts: BodyPart[], benefits: Benefit[], info: any): TagViewWidget {
        let showBodyParts
        const tags: Tag[] = []
        bodyParts.forEach(bodyPart => {
            if (bodyPart.isActive) {
                showBodyParts = true
            }
            if (bodyPart.display) {
                tags.push({
                    title: bodyPart.title,
                    value: "tick",
                    disabled: !bodyPart.isActive
                })
            }
        })
        const benefitsTagViewWidget: TagViewWidget = {
            widgetType: "TAG_VIEW_WIDGET",
            backgroundColor: "#f5f5f5",
            dividerType: "LARGE"
        }
        if (showBodyParts) {
            benefitsTagViewWidget["header"] = "YOU FOCUSSED ON"
            benefitsTagViewWidget["tags"] = tags
            benefitsTagViewWidget["hasShadow"] = false
            benefitsTagViewWidget["tagValueType"] = "ICON"
        }
        if (benefits.length > 0) {
            benefitsTagViewWidget["footer"] = {
                header: "YOU’VE IMPROVED YOUR",
                dataText: this.getBenefits(benefits),
                info: { actionType: "SHOW_ALERT_MODAL", meta: { ...info.FITNESS_INFO, actions: [{ title: "OK", actionType: "HIDE_ALERT_MODAL" }] } }
            }
        }
        return benefitsTagViewWidget
    }

    async getWeightDetailWidget(reportData: FitnessReport, info: any, userContext: UserContext, isInternalUser: boolean): Promise<ReportMetricDetailWidget> {
        const weightMetric = await this.metricService.getMetricById(WEIGHT_METRIC_ID)
        const heightMetric = await this.metricService.getMetricById(HEIGHT_METRIC_ID)
        const weightMetricOption = getMeasurementOptions(weightMetric, userContext, isInternalUser, reportData.weight)
        const heightMetricOption = getMeasurementOptions(heightMetric, userContext, isInternalUser)
        const tz = userContext.userProfile.timezone
        const currentWeekStartDate = momentTz.tz(TimeUtil.getCurrentWeekStartDate(), tz).format("YYYY-MM-DD")
        const metricSection: ReportMetric = {
            metricTitle: "CURRENT\nWEIGHT",
            metricTitleColor: "#ffb97d",
            metricDisplayValue: reportData.weight ? `${reportData.weight}` : "N/A",
            unit: reportData.weight ? "kg" : "",
            info: { actionType: "SHOW_ALERT_MODAL", meta: { ...info.WEIGHT_INFO, actions: [{ title: "OK", actionType: "HIDE_ALERT_MODAL" }] } }
        }
        const infoSection: InfoSection = {
            header: "YOUR \nHEIGHT",
            dataText: reportData.height ? `${reportData.height}` : "N/A",
            subText: reportData.height ? "cm" : ""
        }
        if (reportData.startDate === currentWeekStartDate) {
            metricSection["action"] = {
                actionType: "UPDATE_METRIC",
                title: reportData.weight ? "UPDATE WEIGHT" : "ADD WEIGHT",
                icon: "EDIT",
                meta: { ...weightMetric, metricOption: weightMetricOption, pageFrom: "fitnessreport" },
                shouldRefreshPage: true
            }
            infoSection["action"] = {
                actionType: "UPDATE_METRIC",
                title: reportData.height ? "UPDATE HEIGHT" : "ADD HEIGHT",
                icon: "EDIT",
                meta: { ...heightMetric, metricOption: heightMetricOption, pageFrom: "fitnessreport" },
                shouldRefreshPage: true
            }
        }
        return {
            widgetType: "REPORT_METRIC_DETAIL_WIDGET",
            metricSection: metricSection,
            infoSection: infoSection
        }
    }

    getWeightGraphWidget(userContext: UserContext, weightHistory: GraphEntry[], reportData: FitnessReport, info: any): ReportGraphWidget {
        const graphData: GraphValue[] = weightHistory.map(item => {
            return {
                label: TimeUtil.formatDateStringInTimeZone(item.startDate, userContext.userProfile.timezone, "DD MMM"),
                value: item.value,
                isCurrentValue: item.startDate === reportData.startDate,
                colors: ["#ff8181", "#ff9c75"]
            }
        })
        const weightGraphWidget: ReportGraphWidget = {
            widgetType: "REPORT_GRAPH_WIDGET",
            header: "WEIGHT LAST 6 WEEKS",
            backgroundColor: "#f5f5f5",
            graphData: graphData
        }
        if (reportData.bmi) {
            weightGraphWidget["footer"] = {
                header: "CURRENT BODY MASS INDEX ",
                dataText: `${Number(reportData.bmi).toFixed(2)}`,
                subText: "(Healthy Range: 18-25)",
                info: { actionType: "SHOW_ALERT_MODAL", meta: { ...info.BMI_INFO, actions: [{ title: "OK", actionType: "HIDE_ALERT_MODAL" }] } }
            }
        }
        return weightGraphWidget
    }

    private getClassesAttendedMilestoneWidget(userMilestoneWithMessages: UserMilestoneWithMessage[]): WidgetView {
        if (!_.isEmpty(userMilestoneWithMessages)) {
            const classAttendedMilestone = userMilestoneWithMessages.find((item) => item.userMilestoneCategory === MilestoneCategory.TOTAL_CLASSES_ATTENDED)
            if (classAttendedMilestone) {
                const calloutText = classAttendedMilestone.userMilestoneMessage
                const imageUri = ACHIEVEMENT_ICON
                return new ClpCalloutWidget({
                    widgetType: "CLP_CALLOUT_WIDGET",
                    title: calloutText,
                    linearGradientColors: ["#f37373", "#ff9c75"],
                    imageUri,
                    dividerType: "LARGE",
                    fromPage: "fitnessweeklyreports"
                })
            }
        }
        return undefined
    }

    private getWeightLossMilestoneWidget(userMilestoneWithMessages: UserMilestoneWithMessage[]) {
        if (!_.isEmpty(userMilestoneWithMessages)) {
            const weightLossMilestone = userMilestoneWithMessages.find((item) => item.userMilestoneCategory === MilestoneCategory.WEEKLY_WEIGHT_LOSS)
            if (weightLossMilestone) {
                const calloutText = weightLossMilestone.userMilestoneMessage
                const imageUri = ACHIEVEMENT_ICON
                return new ClpCalloutWidget({
                    widgetType: "CLP_CALLOUT_WIDGET",
                    subTitle: calloutText,
                    linearGradientColors: ["#f37373", "#ff9c75"],
                    imageUri,
                    dividerType: "LARGE",
                    fromPage: "fitnessweeklyreports",
                    imageStyle: { height: 31, width: 27, resizeMode: "contain" }
                })
            }
        }
        return undefined
    }

    private async getClassesMissedBannerWidget(userContext: UserContext, classesAttendedHistory: GraphEntry[], zeroState?: string): Promise<ClassMissedBannerWidget> {
        const lastClassAttendedWeek = zeroState || CultUtil.getLastClassAttended(classesAttendedHistory)
        const classMissedData = this.fitnessReportPageConfig.stateData[CultUtil.getClassConfigByLastClassAttended(lastClassAttendedWeek)]
        const cardType = "EMOJI"
        return {
            widgetType: "CLASS_MISSED_WIDGET",
            title: classMissedData[cardType].title,
            image: classMissedData[cardType].image,
            backgroundColor: classMissedData[cardType].background,
            alignIcon: "LEFT",
            cardType: "FULL_SCREEN"
        }
    }

    private getFitnessRoutineSteps(classesAttendedHistory: GraphEntry[], isPastClass?: boolean): ProductListWidget {
        const lastClassAttendedWeek: string | number = isPastClass ? "MEMBER_PREV_WEEK_ZERO_STATE" : CultUtil.getLastClassAttended(classesAttendedHistory)
        const classMissedData = this.fitnessReportPageConfig.stateData[CultUtil.getClassConfigByLastClassAttended(lastClassAttendedWeek)]
        if (classMissedData.steps) {
            const items = classMissedData.steps.items.map((item, index) => {
                return {
                    subTitle: item.description,
                    bulletText: `#${index + 1}`,
                    icon: "/image/icons/howItWorks/info.png"
                }
            })
            return {
                type: "SMALL",
                widgetType: "PRODUCT_LIST_WIDGET",
                header: {
                    title: classMissedData.steps.title,
                    style: { paddingRight: 16, color: "#5f5f5f", fontSize: 14, fontFamily: "BrandonText-Medium" }
                },
                items: items
            }
        }
        return null
    }

    private getTypographyWidget(lineOneText: string, lineTwoText: string): TypographyWidget {
        return {
            widgetType: "TYPOGRAPHY_WIDGET",
            tagColor: "#6ebcff",
            data: [{
                text: lineOneText,
                fontColor: "#6e6e6e",
                fontSize: 35
            }, {
                text: lineTwoText,
                fontColor: "#cccccc",
                fontSize: 33
            }],
            dividerType: "LARGE"
        }
    }

    private getQuickLinkWidget(title: string): QuickLinkWidget {
        return {
            widgetType: "QUICK_LINKS_WIDGET",
            data: [{
                title: title
            }]

        }
    }

    getDescriptionWidget(description?: string): DescriptionWidget {
        const descriptionWidget: DescriptionWidget = {
            widgetType: "DESCRIPTION_WIDGET",
            showDivider: false,
            descriptions: [{
                title: "TAKING A BREAK?",
                subTitle: description || "It’s been a while since you have attended a class at Cult.fit."
            }]
        }
        return descriptionWidget
    }

}

export default FitnessReportPageViewBuilder
