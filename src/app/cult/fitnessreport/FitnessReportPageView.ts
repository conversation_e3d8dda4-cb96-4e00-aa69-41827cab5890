import { Action, Header, WidgetType, WidgetView } from "../../common/views/WidgetView"
import { PageWidget } from "../../page/Page"
import { ImageFilter } from "@curefit/cult-common"

export type ReportType = "WEEKLY" | "CHALLENGE"

export class FitnessReportPageView {
    header: {
        pageTitle: string
    }
    reportType: ReportType
    startDate: string
    widgets: (WidgetView | PageWidget)[]
    actions?: Action[]
    currentWeekToastText?: string

    constructor(widgets: WidgetView[], header: any, reportType: ReportType, startDate?: string, actions?: Action[], currentWeekToastText?: string) {
        this.widgets = widgets
        this.header = header
        this.reportType = reportType
        this.startDate = startDate
        this.actions = actions
        this.currentWeekToastText = currentWeekToastText
    }
}

export class FitnessReportSummaryCardWidget implements WidgetView {
    public widgetType: WidgetType = "FITNESS_REPORT_SUMMARY_CARD_WIDGET"
    imageUrl?: string
    imageFilter?: ImageFilter
    milestone?: string
    startDate: string
    endDate: string
    reportType: ReportType
    classAttended: string | number
    caloriesBurnt: string | number
    startDateDisplayText: string
    endDateDisplayText: string
    reportTypeDisplayText: string
    fitnessReportFilters: Object[]
    achievement?: string
    shareAction?: Action
    dividerType?: "LARGE" | "SMALL" | "NONE"
    shareIcon?: string
    reportId: number
    analyticsData?: any
}

export class ReportMetricDetailWidget implements WidgetView {
    widgetType: WidgetType = "REPORT_METRIC_DETAIL_WIDGET"
    metricSection: ReportMetric
    infoSection?: InfoSection
}

export class ReportGraphWidget implements WidgetView {
    widgetType: WidgetType = "REPORT_GRAPH_WIDGET"
    header: string
    backgroundColor: string
    graphData?: GraphValue[]
    showSeparator?: boolean
    footer?: InfoSection
    dividerType?: "LARGE" | "SMALL" | "NONE"
}

export class ProTipWidget implements WidgetView {
    widgetType: WidgetType = "PRO_TIP_WIDGET"
    title: string
    description: string
    imageUrl: string
    gradientColor: string[]
    dividerType?: string
    action?: Action
}

export interface Tag {
    title: string
    value: string
    disabled?: boolean
    style?: any
}

export class TagViewWidget implements WidgetView {
    widgetType: WidgetType = "TAG_VIEW_WIDGET"
    header?: string
    backgroundColor?: string
    footer?: InfoSection
    tags?: Tag[]
    tagValueType?: "ICON" | "TEXT"
    hasShadow?: boolean
    noPaddingLeft?: boolean
    dividerType?: string
    headerTextStyle?: any
    tagContainerStyle?: any
}

export class ClassMissedBannerWidget implements WidgetView {
    widgetType: WidgetType = "CLASS_MISSED_WIDGET"
    title: {
        value: string,
        color: string
    }
    icon?: string
    backgroundColor: string
    alignIcon: "LEFT" | "RIGHT"
    image?: string
    dividerType?: string
    cardType?: string
}

export interface ReportMetric {
    metricTitle: string
    metricDisplayValue: string
    unit?: string
    tag?: string
    info?: Action
    action?: Action,
    metricTitleColor?: string
}

export interface InfoSection {
    header: string
    dataText: string
    subText?: string
    extraText?: string
    unit?: string
    info?: Action
    action?: Action
}

export interface GraphValue {
    label: string
    value: number
    colors: string[]
    isCurrentValue?: boolean
}