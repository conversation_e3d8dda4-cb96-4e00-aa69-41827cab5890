import { inject, injectable } from "inversify"
import { InMemoryCacheService } from "@curefit/memory-cache"
import { IPageConfigReadOnlyDao, IPageConfigReadWriteDao, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import * as _ from "lodash"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

interface ClassesMissedInfo {
    steps: {
        title: string,
        items: { description: string }[]
    },
    videoUrl: string,
    videoThumbnail: string,
    EMOJI: {
        background: string,
        image: string,
        title: {
            color: string,
            value: string,
        },
        subTitle: {
            color: string,
            value: string,
        }
    },
    IMAGE: {
        background: string,
        image: string,
        title: {
            color: string,
            value: string,
        },
        subTitle: {
            color: string,
            value: string,
        }
    }
}

@injectable()
class FitnessReportPageConfig extends InMemoryCacheService<any> {

    constructor(
        @inject(PAGE_CONFIG_TYPES.PageConfigReadOnlySecondaryDao) protected pageConfigDao: IPageConfigReadOnlyDao,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger, 32 * 60)
        this.load("FitnessReportPageConfig")
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    loadData(): Promise<any> {
        return this.pageConfigDao.findOne({ pageId: { $eq: "FitnessReportPageConfig" } }).then(pageConfig => {
            const data = pageConfig.data
            this.stateData = data.stateData
            return data
        })
    }

    public stateData: {
        "ZERO_CLASSES_0": ClassesMissedInfo,
        "ZERO_CLASSES_1": ClassesMissedInfo,
        "ZERO_CLASSES_2": ClassesMissedInfo,
        "ZERO_CLASSES_3": ClassesMissedInfo,
        "ZERO_CLASSES_4": ClassesMissedInfo,
        "MEMBER_PREV_WEEK_ZERO_STATE": ClassesMissedInfo,
        "MEMBERSHIP_PAUSED": ClassesMissedInfo
    }
}

export default FitnessReportPageConfig
