import { CULT_CUSTOMER_TYPE } from "@curefit/cult-client"
import { CultClass } from "@curefit/cult-common"
import { ProductType } from "@curefit/product-common"
import CreditsView from "../common/views/CreditsViewWidget"

export type ClassState = "BOOKED" | "SEAT_NOT_AVAILABLE" | "OVERLAPPING_BOOKING" | "AVAILABLE"

class ClassView {
    constructor(productType: ProductType, cultClass: CultClass, workoutNameMap: { [id: string]: string }, customerType: CULT_CUSTOMER_TYPE) {
        this.id = cultClass.id.toString()
        this.productType = productType
        this.date = cultClass.date
        this.startTime = cultClass.startTime
        this.endTime = cultClass.endTime
        this.workoutId = cultClass.workoutCategoryID
        this.centerID = Number(cultClass.centerID)
        this.availableSeats = Math.min(cultClass.cultAppAvailableSeats, 2)
        this.workoutName = workoutNameMap[cultClass.workoutID].toUpperCase()
        this.state = "AVAILABLE"
        if (cultClass.bookingNumber) {
            this.isBooked = true
            this.state = "BOOKED"
            this.action = productType === "FITNESS" ? `curefit://cultclass?bookingNumber=${cultClass.bookingNumber}` :
                `curefit://mindclass?bookingNumber=${cultClass.bookingNumber}`
        } else if (this.availableSeats <= 0) {
            this.state = "SEAT_NOT_AVAILABLE"
        // This has been commented out to enable ppc sessions for users whose trials are exhausted
        // } else if (cultClass.allowTrial === false && customerType === "FREE") {
        //     this.state = "SEAT_NOT_AVAILABLE"
        } else if (cultClass.allowPPC === false && customerType === "PAID") {
            this.state = "SEAT_NOT_AVAILABLE"
        }
        this.creditCost = cultClass.creditCost ? new CreditsView(cultClass.creditCost) : null
    }

    public id: string
    public date: string
    public startTime: string
    public endTime: string
    public workoutId: number
    public workoutName: string
    public availableSeats: number
    public action?: string
    public isBooked?: boolean
    public state: ClassState
    public centerID: number
    public productType: ProductType
    public creditCost: CreditsView
}

export default ClassView
