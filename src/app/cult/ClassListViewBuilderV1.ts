import { inject, injectable } from "inversify"
import { Cult<PERSON><PERSON> } from "@curefit/cult-common"
import { CULT_CUSTOMER_TYPE } from "@curefit/cult-client"
import { ProductType } from "@curefit/product-common"
import ClassView from "./ClassView"
import * as _ from "lodash"
import ClassListViewBuilder, { ClassByDate, ClassByTime } from "./ClassListViewBuilder"
import { ICatalogueService, CATALOG_CLIENT_TYPES } from "@curefit/catalog-client"
import CultUtil from "../util/CultUtil"

export interface ClassByDateNearby extends ClassByDate {
    classByTimeList: ClassByTimeNearby[],
}

export interface ClassByTimeNearby extends ClassByTime {
    nearbyCenters?: NearbyClasses[],
    nearbyText?: string
}

export interface NearbyClasses {
    centerId: number,
    centerName: string,
    classes: ClassView[],
    address: string
}

@injectable()
class ClassListViewBuilderV1 extends ClassListViewBuilder {

    constructor(
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService
    ) {
        super()
    }

    async getAllCultCenters(): Promise<{ [id: number]: CultCenter }> {
        const cultCenters = await this.catalogueService.getCultCenters()
        const cultCentersMap = _.keyBy(cultCenters, "id")

        return cultCentersMap
    }

    async getAllMindCenters(): Promise<{ [id: number]: CultCenter }> {
        const cultCenters = await this.catalogueService.getCultMindCenters()
        const cultCentersMap = _.keyBy(cultCenters, "id")

        return cultCentersMap
    }
    async getClassByDateList(productType: ProductType, cultCenter: CultCenter,
        workoutMap: { [id: string]: string }, customerType: CULT_CUSTOMER_TYPE): Promise<ClassByDate[]> {

        // get list of cult centers
        let cultCentersMap: { [id: number]: CultCenter }
        if (productType === "FITNESS") {
            cultCentersMap = await this.getAllCultCenters()
        } else {
            cultCentersMap = await this.getAllMindCenters()
        }

        const classByDateList: ClassByDateNearby[] = []

        this.cultClassDates.forEach(cultClassDate => {
            classByDateList.push({
                id: cultClassDate.date,
                widgetType: "BROWSE_CLASS_LIST",
                classByTimeList: []
            })
        })

        this.cultClasses.forEach(cultClass => {
            const classByDate = classByDateList.find(classByDate => (classByDate.id === cultClass.date))
            // This check is needed as the cult api is returning classes for an additional day but the date mapping is not present
            if (classByDate) {
                let classByTime = classByDate.classByTimeList.find(classByTime => (classByTime.id === cultClass.startTime))
                if (!classByTime) {
                    classByTime = {
                        id: cultClass.startTime,
                        disableGroup: false,
                        classes: [],
                        nearbyCenters: [],
                        nearbyText: ""
                    }
                    classByDate.classByTimeList.push(classByTime)
                }
                if (cultClass.bookingNumber) {
                    classByTime.disableGroup = true
                }

                if (cultCenter.id === parseInt(cultClass.centerID)) {
                    classByTime.classes.push(new ClassView(productType, cultClass, workoutMap, customerType))
                }

                // adding nearby centers
                let nearbyCenters = classByTime.nearbyCenters.find(nearbyCenters => (nearbyCenters.centerId === parseInt(cultClass.centerID)))
                if (!nearbyCenters) {

                    const center = cultCentersMap[parseInt(cultClass.centerID)]
                    const addressString = CultUtil.getCultCenterAddress(center)
                    nearbyCenters = {
                        centerId: parseInt(cultClass.centerID),
                        centerName: center.name,
                        classes: [],
                        address: addressString
                    }
                    if (nearbyCenters.centerId !== cultCenter.id) {
                        classByTime.nearbyCenters.push(nearbyCenters)
                    }
                }

                const cultClassView = new ClassView(productType, cultClass, workoutMap, customerType)
                if (cultClassView.state === "AVAILABLE" || cultClassView.state === "BOOKED") {
                    nearbyCenters.classes.push(cultClassView)
                }

            }
        })

        classByDateList.forEach(classByDate => {
            // Sort the classes by time
            classByDate.classByTimeList.sort((a, b) => {
                if (a.id < b.id)
                    return -1
                else
                    return 1
            })

            classByDate.classByTimeList.forEach(classByTime => {

                // Set the OVERLAPPING_BOOKING state
                if (classByTime.disableGroup) {
                    classByTime.classes.forEach(clazz => {
                        if (clazz.state === "AVAILABLE") {
                            clazz.state = "OVERLAPPING_BOOKING"
                        }
                    })
                }

                let uniqueNearbyClasses: ClassView[] = []

                classByTime.nearbyCenters = classByTime.nearbyCenters.filter(nearbyCenterClass => {

                    const shouldShowNearby = true
                    const selectedCenterClasses = classByTime.classes
                    let nearbyCenterClasses = nearbyCenterClass.classes


                    nearbyCenterClasses = nearbyCenterClasses.filter(nearClass => {
                        let shouldShowClass = true
                        for (let idx = 0; idx < selectedCenterClasses.length; idx++) {
                            const centerClass = selectedCenterClasses[idx]
                            if (
                                nearClass.workoutId === centerClass.workoutId &&
                                (centerClass.state === "AVAILABLE" || centerClass.state === "BOOKED")
                            ) {
                                shouldShowClass = false
                            }
                        }
                        return shouldShowClass
                    })

                    nearbyCenterClass.classes = nearbyCenterClasses

                    if (nearbyCenterClasses.length > 0) {
                        uniqueNearbyClasses = _.unionBy(uniqueNearbyClasses, nearbyCenterClasses, "workoutId")
                        return true
                    }
                    return false
                })

                if (classByTime.nearbyCenters.length === 0) {
                    delete classByTime.nearbyCenters
                }

                // create nearby text
                let displayText = ""
                if (uniqueNearbyClasses.length > 0 && classByTime.nearbyCenters.length > 0) {

                    const numberOfCenters = classByTime.nearbyCenters.length

                    displayText = uniqueNearbyClasses[0].workoutName
                    if (uniqueNearbyClasses.length > 1) {
                        displayText = displayText + " and " + (uniqueNearbyClasses.length - 1) + " more"
                    }
                    displayText = displayText + " available at "
                    if (classByTime.nearbyCenters.length > 1) {
                        displayText = displayText + classByTime.nearbyCenters.length + " centers"
                    } else {
                        const center = cultCentersMap[classByTime.nearbyCenters[0].centerId]
                        displayText = displayText + center.name
                    }
                    displayText = displayText + " nearby"
                }
                classByTime.nearbyText = displayText
            })
        })

        return classByDateList
    }
}

export default ClassListViewBuilderV1
