import { <PERSON>ult<PERSON><PERSON>, CultClass, CultDocument, CultMembership } from "@curefit/cult-common"
import * as momentTz from "moment-timezone"
import * as _ from "lodash"
import {
  Action, ActionType,
  Header,
  WidgetView
} from "../common/views/WidgetView"
import { PageWidget } from "../page/Page"
import {
  FormatSummaryWidget,
  WorkoutMediaData,
  MapWidgetV2,
  CenterTimingWidget,
  ProductGridWidgetV2,
  CollapsibleProperties,
  FacilitiesWidget,
  WODInfoWidgetV2,
  WorkoutMovementInfo,
  WaitlistWidget,
  WaitlistInfo,
  WaitlistConfirmationTimePicker,
  HorizontalListSrcollWidget,
  WorkoutWaitlistTimingSlot,
  CardItemSubTitle,
  ActionWidget,
  InstructionsWithMediaV2,
  Facility,
  CFMediaDataWidget,
  CFMediaWidget,
  CFBottomSheetActionWidget,
  CreditPillData,
} from "../page/PageWidgets"
import Cult<PERSON><PERSON>, {
  ATLEAST_ONE_GX_CLASS_ATTENDED_SEGMENT,
  BEGINNER_FRIENDLY_WORKOUT_IDS,
  BOXING_BAG_WORKOUT_ID,
  BOXING_GLOVES_PROVIDED_CENTER_IDS,
  CULT_CLASS_ATTENDED_PLATFORM_SEGMENT,
  CULT_WAITLIST_EXTENSION_RECOMMENDED_TIME,
  getTimeSlotsForWaitlistExtensionWidget,
  HATHA_YOGA_WORKOUT_ID,
  YOGA_WORKOUT_ID,
  showInterventionAFMSlotBooking,
  DUMMY_EVOLVE_YOGA_WORKOUT_ID,
  DUMMY_STRENGTH_PLUS_WORKOUT_ID,
  STRENGTH_PULSE,
  NUDGE_BANNER_ID,
  WEEKEND_AT_CULT
} from "../util/CultUtil"
import { ProductType } from "@curefit/product-common"
import { UserContext } from "@curefit/userinfo-common"
import { eternalPromise, TimeUtil, Timezone } from "@curefit/util-common"
import { SimplePart, SimpleWod } from "@curefit/fitness-common"
import { ISegmentService } from "@curefit/vm-models"
import { IMembershipService } from "@curefit/membership-client"
import { Media, MediaType } from "@curefit/gymfit-common"
import { CultMembershipDetails } from "./CultClassDetailView"
import { PRIMARY_ACTION_VARIANT, SECONDARY_ACTION_VARIANT } from "../util/ActionUtil"
import AppUtil, { BOXING_INSTRUCTION_MODAL_SUPPORTED } from "../util/AppUtil"
import { IUserAttributeClient } from "@curefit/rashi-client"
import UserUtil, { LocationDataKey, LocationPreferenceResponseEntity } from "../util/UserUtil"
import { LatLong } from "@curefit/location-common"
import LocationUtil from "../util/LocationUtil"
import { CLSUtil, Logger } from "@curefit/base"
import { AttributeKeyType, Membership } from "@curefit/membership-commons"
import { CultBuddiesJoiningListSmallWidget, InfoCard } from "@curefit/apps-common"
import serviceInterfaces, { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import WidgetBuilder from "../page/vm/WidgetBuilder"
import { ICrudKeyValue } from "@curefit/redis-utils"
import { CenterBookingCache } from "./CenterBookingCache"
import * as moment from "moment"
import { ISegmentationCacheClient } from "@curefit/segmentation-service-client"
import { MembershipItemUtil } from "../util/MembershipItemUtil"
import CultClassUtil from "../util/CultClassUtil"
import { ICultServiceOld } from "@curefit/cult-client"

class CultClassDetailViewV2 {
  public message?: string
  public isWodAvailable?: boolean
  public widgets: (WidgetView | PageWidget)[]
  public actionsWidget: PageWidget
  public changeBackgroundColorIndex: number
  public isFullScreen?: boolean
  public workoutFormat?: string
  public onPageOpenAction?: Action
  private BUY_CULTPASS_BUTTON_TITLE: string = "BUY CULTPASS"

  async buildView(
    userContext: UserContext,
    cultClass: CultClass,
    wod: SimpleWod,
    cultMembershipDetails: CultMembershipDetails,
    productType: ProductType,
    rescheduleSourceBookingNumber: string,
    membershipList: Membership[],
    userAttributeClient: IUserAttributeClient,
    prefLocationType: any,
    segmentService: ISegmentService,
    logger: Logger,
    clsUtil: CLSUtil,
    buddiesJoining?: CultBuddiesJoiningListSmallWidget,
    membershipService?: IMembershipService,
    serviceInterfaces?: CFServiceInterfaces,
    widgetBuilder?: WidgetBuilder,
    redisVMCrudDao?: ICrudKeyValue,
    centerBookingCache?: CenterBookingCache,
    segmentCacheClient?: ISegmentationCacheClient,
    cultClassUtil?: CultClassUtil,
    cultFitService?: ICultServiceOld
  ): Promise<CultClassDetailViewV2> {

    if (cultClass.cultAppAvailableSeats <= 0 && !cultClass.isWaitlistAvailable) {
      this.setFullSlotMessage(userContext, cultClass)
      return this
    }


    let isFirstTimeUser = true
    const segments: string[] = await clsUtil.getPlatformSegments()
    if (!_.isEmpty(segments) && (segments.includes(CULT_CLASS_ATTENDED_PLATFORM_SEGMENT))) {
      isFirstTimeUser = false
    }
    let recommendationLevel: number = null, workoutLevel: number = null, recomendationWidget: WidgetView
    if (await AppUtil.doesUserBelongToM1RecommendationSegment(userContext, segmentService)) {
      const user = await userContext.userPromise
      recommendationLevel = await CultUtil.getUserRecommendationLevel(user.id, userAttributeClient)
      workoutLevel = CultUtil.getWorkoutRecommendationLevel(cultClass.Workout.name)
    }
    logger.debug("recomendationTest detail", { recommendationLevel, workoutLevel })
    if (recommendationLevel && workoutLevel) {
      if (recommendationLevel == workoutLevel) {
        recomendationWidget = CultUtil.getRecommendationMatchWidget(true)
      } else if (workoutLevel > recommendationLevel) {
        recomendationWidget = CultUtil.getRecommendationMatchWidget(false)
      }
    }

    const isUserEligibleForMiniGoal = await CultUtil.isUserEligibleForMiniGoal(userContext, serviceInterfaces, cultClass.workoutID)
    const isUserEligibleForMiniGoalBanner = await CultUtil.isUserEligibleForMiniGoalBanner(userContext, serviceInterfaces, cultClass.workoutID)
    const isUserEligibleForGXLogging = await CultUtil.isUserEligibleForGXLogging(userContext, serviceInterfaces)
    const isMiniGoalModalShown = await serviceInterfaces.featureStateCache.match(userContext.userProfile.userId, "mini_goal_info_key", "shown")
    const isUserEligibleForMiniGoalModal = isUserEligibleForMiniGoal && !_.isEmpty(segments) // && (segments.includes(HRX_USER_D30))
    const isMiniGoalModalNotShownForEligibleUser = isUserEligibleForMiniGoalModal && !isMiniGoalModalShown

    if (isMiniGoalModalNotShownForEligibleUser) {
      this.onPageOpenAction = CultUtil.getMiniGoalIntroCustomBottomSheetAction({
        actionType: "POP_NAVIGATION",
        url: "",
        title: "GOT IT"
      }, userContext)
      serviceInterfaces.featureStateCache.set(userContext.userProfile.userId, "mini_goal_info_key", "shown")
    }

    let miniGoalDetails: {
      miniGoal?: {
        goalTitle: string
        minClass: number
        numClassesAttended: number
        goalDescription?: string
      },
      focusArea?: {
        title?: string
        focusDescription?: string
        imageUrl?: string
      }
    }
    let loggedMovements: any
    if (isUserEligibleForMiniGoal || isUserEligibleForMiniGoalBanner) {
      miniGoalDetails = await cultClassUtil.getMiniGoalDetails(
        userContext.userProfile.userId,
        serviceInterfaces,
        wod,
        AppUtil.getTimeInMilliseconds(cultClass.startDateTimeUTC).toString(),
      )
    }

    const widgetList: (WidgetView | PageWidget)[] = []
    const formatSummarywidget = this.getFormatSummaryWidgetPromise(cultClass, isFirstTimeUser, userContext)

    if (!_.isEmpty(miniGoalDetails) && !_.isEmpty(miniGoalDetails.focusArea)) {
      formatSummarywidget.mediaDetails = []
      formatSummarywidget.layoutProps = { spacing: { top: 0, bottom: 0 } }
    }

    widgetList.push(formatSummarywidget)

    const bannerwidgetPromise = cultClassUtil.buildBannerWidget(userContext, NUDGE_BANNER_ID, widgetBuilder, serviceInterfaces)
    if (bannerwidgetPromise) {
      widgetList.push(await bannerwidgetPromise)
    }

    const isAndroid: boolean = userContext.sessionInfo.osName.toLowerCase() === "android"
    if (cultClass.workoutID === STRENGTH_PULSE && (!isAndroid || userContext.sessionInfo.appVersion >= 10.96)) {
      const watchAppSetUpWidget: WidgetView = {
        widgetType: "WATCH_SYNC_BANNER_WIDGET",
        infoText: (isAndroid) ? "Have a\nSmart watch?" : "Have an\nApple watch?",
        buttonText: "SYNC",
        icon: "assets/watch_icon.png",
        action: {
          "actionType": "SHOW_CUSTOM_BOTTOM_SHEET",
          "meta": {
            "showTopNotch": true,
            "backgroundColor": 0xCC000000,
            "widgets": [
              { "widgetType": "WATCH_SETUP_PAGE" }
            ]
          }
        }
      }
      widgetList.push(watchAppSetUpWidget)
    }

    if (recomendationWidget) {
      widgetList.push(recomendationWidget)
    }
    const wlAvailable: boolean = CultUtil.isClassAvailableForWaitlist(cultClass)
    if (!_.isNil(buddiesJoining)) {
      buddiesJoining.hasDividerBelow = wlAvailable ? true : false
      widgetList.push(buddiesJoining)
    }
    // widgetList.push(this.getCenterTimingWidget(userContext, cultClass))
    if (wlAvailable) {
      const getwaitlistCnfProbabilityColor = await AppUtil.isAppNewWaitlistColorCodingSupported(serviceInterfaces.segmentService, userContext)
      const waitlistWidget = this.getWaitlistWidget(cultClass, userContext.userProfile.timezone, isFirstTimeUser, getwaitlistCnfProbabilityColor)
      if (!_.isEmpty(miniGoalDetails) && !_.isEmpty(miniGoalDetails.focusArea)) {
        waitlistWidget.layoutProps = { spacing: { top: 0, bottom: 0 } }
      }
      widgetList.push(waitlistWidget)
    }

    // mini goal banner widget
    try {
      if (!_.isEmpty(miniGoalDetails)) {
        // serviceInterfaces.logger.info(`MINIGOAL: 01: ${userContext.userProfile.userId}`, miniGoalDetails.focusArea, miniGoalDetails.miniGoal, miniGoalDetails.miniGoal.goalTitle, miniGoalDetails.miniGoal.minClass)
        if (!_.isEmpty(miniGoalDetails.focusArea)) {
          const miniGoalBannerWidget = cultClassUtil.getMiniGoalBannerWidgetPromise(miniGoalDetails, userContext)
          if (miniGoalBannerWidget) {
            miniGoalBannerWidget.layoutProps = { spacing: { top: wlAvailable ? 40 : 20, bottom: 0 } }
            widgetList.push(miniGoalBannerWidget)
          }
        }
      }

      // get prelogged weights
      if (isUserEligibleForGXLogging && !_.isEmpty(wod) && !_.isEmpty(wod.parts)) {
        loggedMovements = await cultClassUtil.getPreLoggedMovementInfo(userContext, serviceInterfaces, wod, cultClass)
      }
    } catch (error) {
      serviceInterfaces.logger.error(`Error while creating miniGoal banner details for user: ${userContext.userProfile.userId}`, error)
    }

    if (!_.isNil(wod)) {
      const wodInfoWidgetV2 = cultClassUtil.getWodInfoWidgetV2(userContext, wod, isFirstTimeUser, loggedMovements)
      if (!_.isEmpty(miniGoalDetails) && !_.isEmpty(miniGoalDetails.focusArea) && !_.isEmpty(wodInfoWidgetV2)) {
        wodInfoWidgetV2.layoutProps = { spacing: { top: 20, bottom: 0 } }
      }
      widgetList.push(wodInfoWidgetV2)
    }

    // mini goal widget
    try {
      if (!_.isEmpty(miniGoalDetails)) {
        if (!_.isEmpty(miniGoalDetails.miniGoal)) {
          const miniGoalWidget = cultClassUtil.getMiniGoalWidgetPromise(miniGoalDetails, cultClass.date, userContext)
          if (miniGoalWidget) {
            miniGoalWidget.layoutProps = { spacing: { top: 30, bottom: 0 } }
            widgetList.push(miniGoalWidget)
          }
        }
      }
    } catch (error) {
      serviceInterfaces.logger.error(`Error while creating miniGoal widget details for user: ${userContext.userProfile.userId}`, error)
    }

    const productGridWidgetV2: ProductGridWidgetV2 = this.getPreWorkoutGearWidget(cultClass, isFirstTimeUser)
    if (productGridWidgetV2) {
      widgetList.push(productGridWidgetV2)
    }



    const key = "{merchandise:images:}FITNESS::Format Image::Format:" + cultClass.workoutID + "::UI Target:format_details"
    const formatImages: string = await redisVMCrudDao.read(key)
    const parsedObj = JSON.parse(formatImages)

    const variants = parsedObj?.imageConfigurations?.variants
    const images: String[] = []
    variants?.forEach((image: any) => {
      images.push(image.image)
    })

    if (images && images.length > 0) {
      const formatInfoWidget = CultUtil.getFormatInfoWidget(images, this.getSpacing("20", "0"))
      widgetList.push(formatInfoWidget)
    }

    widgetList.push(this.getFacilitiesWidget(cultClass.Center, isFirstTimeUser))
    const notesWidget = CultUtil.getWorkoutNotesWidgetV2(cultClass, this.getSpacing("60", "0"))
    if (!_.isNil(notesWidget)) {
      notesWidget.hasDividerBelow = false
      widgetList.push(notesWidget)
    }

    if (CultUtil.isCultRunWorkout(cultClass.workoutID)) {
      const address = `${cultClass.Center.Address.addressLine1} ${cultClass.Center.Address.addressLine2}`
      widgetList.push(await this.getCenterMapWidget(userContext, cultClass.Center, cultClass.Center.Address.latitude, cultClass.Center.Address.longitude, address, userAttributeClient, logger, prefLocationType, "Meetup location:", cultClass.Center?.placeUrl))
      widgetList.push(await this.getCenterMapWidget(userContext, cultClass.Center, cultClass.Center.Address.latitude, cultClass.Center.Address.longitude, cultClass.Center?.metadata?.parkingLocation?.address, userAttributeClient, logger, prefLocationType, "Parking location:", cultClass.Center?.metadata?.parkingLocation?.navigation))
    } else {
      widgetList.push(await this.getCenterMapWidgetPromise(userContext, cultClass.Center, userAttributeClient, logger, prefLocationType))
    }

    if (WEEKEND_AT_CULT.includes(cultClass.workoutID)) {
      const centrumBanner = await widgetBuilder.buildWidgets(["fea52c71-831a-4865-b0bf-b42d451a0283"], serviceInterfaces, userContext, undefined, undefined)
      if (!_.isEmpty(centrumBanner?.widgets) && centrumBanner.widgets[0] != null) {
        widgetList.push(centrumBanner.widgets[0])
      }
    }

    this.widgets = widgetList.filter(widget => !_.isNil(widget))
    this.actionsWidget = await this.getActionWidget(cultClass, userContext, cultMembershipDetails, productType, rescheduleSourceBookingNumber, membershipList, logger, membershipService, segmentService, centerBookingCache, serviceInterfaces, segmentCacheClient, cultFitService)
    this.workoutFormat = cultClass.Workout.name
    return this
  }

  private setFullSlotMessage(userContext: UserContext, cultClass: CultClass) {
    const classStartTime = TimeUtil.formatDateStringInTimeZone(cultClass.date + " " + cultClass.startTime, userContext.userProfile.timezone, "h:mm")
    if (cultClass.isWaitlistFull) {
      this.message = `Sorry, waitlist is full for chosen preferred time. ${cultClass.Workout.name} ${classStartTime} slot is no longer available. Please modify the timing preferences or choose another slot.`
    } else {
      this.message = `${cultClass.Workout.name} ${classStartTime} slot is no longer available. Please choose another slot.`
    }
  }

  private getFormatSummaryWidgetPromise(cultClass: CultClass, isFirstTimeUser: boolean, userContext: UserContext): FormatSummaryWidget {

    const title = cultClass.Workout.name
    const intensityLevel = BEGINNER_FRIENDLY_WORKOUT_IDS.includes(cultClass.workoutID) ? "BEGINNER FRIENDLY" : null
    const subTitleList: CardItemSubTitle[] = []
    const mediaDetails = this.getMediaDetailsForWorkout(cultClass.Workout.documents, isFirstTimeUser)
    const centerName: string = cultClass.Center.name
    const navigateAction: Action = { url: cultClass.Center.placeUrl, title: "NAVIGATE", actionType: "EXTERNAL_DEEP_LINK" }

    const tz = userContext.userProfile.timezone
    const classStartTime = TimeUtil.formatDateStringInTimeZone(cultClass.date + " " + cultClass.startTime, tz, "h:mm")
    const classEndTime = TimeUtil.formatDateStringInTimeZone(cultClass.date + " " + cultClass.endTime, tz, "h:mm A")

    const timingDetails: string = `${TimeUtil.formatDateStringInTimeZone(cultClass.date, tz, "ddd D MMM")}, ${classStartTime}-${classEndTime}`
    const creditCost = cultClass?.creditCost || null
    const creditPillData: CreditPillData = creditCost ? {
      credit: creditCost.toString()
    } : null

    return new FormatSummaryWidget(title, null, centerName, navigateAction, timingDetails, true, intensityLevel, subTitleList, mediaDetails, this.getSpacing("0", "0"), creditPillData)
  }

  private getBenefitsWidget(benefits: string[]): HorizontalListSrcollWidget {
    if (_.isEmpty(benefits)) { return null }
    const items = benefits.map(benefit => benefit.toUpperCase())
    return new HorizontalListSrcollWidget(items, this.getSpacing("15", "0"))
  }

  private getSpacing(top: string, bottom: string): any {
    return {
      "spacing": {
        "top": top,
        "bottom": bottom
      }
    }
  }

  private getMediaDetailsForWorkout(documents: CultDocument[], isFirstTimeUser: boolean): WorkoutMediaData[] {
    const mediaList: WorkoutMediaData[] = []
    const imageDocument: CultDocument = _.find(documents, (document) => document.tagName === "PRODUCT_BNR")
    const videoDocument: CultDocument = _.find(documents, (document) => document.tagName === "M_VIDEO")
    const videoThumbnail: CultDocument = _.find(documents, (document) => document.tagName === "M_VIDEO_THUMBNAIL")

    if (isFirstTimeUser && !_.isNil(videoDocument) && !_.isNil(videoDocument.URL)) {
      const mediaUrl = CultUtil.getCultVideoCdnUrl(videoDocument.URL)
      const videoPlayerUrl = `curefit://videoplayer?absoluteVideoUrl=${encodeURIComponent(mediaUrl)}`
      const media: Media = { type: MediaType.VIDEO, mediaUrl: mediaUrl, thumbnailUrl: videoThumbnail.URL }
      const action: Action = { url: videoPlayerUrl, actionType: "PLAY_VIDEO" }
      mediaList.push({ media: media, action: action })
    } else if (!_.isNil(imageDocument) && !_.isNil(imageDocument.URL)) {
      const media: Media = { type: MediaType.IMAGE, mediaUrl: imageDocument.URL }
      mediaList.push({ media: media })
    }
    return mediaList
  }

  private async getCenterMapWidget(userContext: UserContext, cultCenter: CultCenter, latitude: number, longitude: number, address: string, userAttributeClient: IUserAttributeClient, logger: Logger, prefLocationType: any, headerTitle?: string, mapUrl?: string): Promise<MapWidgetV2> {
    const action: Action = { url: mapUrl, title: "NAVIGATE", actionType: "EXTERNAL_DEEP_LINK" }
    let title: string = ""

    try {
      const userId = userContext.userProfile.userId
      const cityId = userContext.userProfile.cityId
      const locationPreference: LocationPreferenceResponseEntity = {
        prefLocationType: undefined
      }
      if (!userContext.sessionInfo.isUserLoggedIn) {
        UserUtil.getLocationPreferenceResponseEntityFromSessionData(userContext.sessionInfo.sessionData, cityId, locationPreference)
      } else {
        await UserUtil.getLocationPreferenceForCoordinates(userId, cityId, userAttributeClient, locationPreference, prefLocationType)
      }

      let coordinates: LatLong
      if (locationPreference != null) {
        if (locationPreference.prefLocationType === LocationDataKey.CURRENT_LOC) {
          coordinates = { lat: userContext.sessionInfo.lat, long: userContext.sessionInfo.lon }
        } else if (locationPreference.prefLocationType === LocationDataKey.COORDINATES && !_.isNil(locationPreference.coordinates)) {
          coordinates = locationPreference.coordinates
        }
      }
      if (!_.isNil(coordinates) && UserUtil.isValidCoordinates(coordinates.lat, coordinates.long)) {
        const distance = LocationUtil.getDistanceFromLatLonInKm(coordinates.lat, coordinates.long, latitude, longitude)
        title = `${distance.toFixed(1)} KM • `
      }
    } catch (error) {
      logger.info("In CultClassDetailViewV2::getCenterMapWidgetPromise, user location preference coordinate not set")
    }

    title += cultCenter.name
    return new MapWidgetV2(address, action, !_.isNil(headerTitle) ? headerTitle : title, this.getSpacing("25", "0"))
  }

  private async getCenterMapWidgetPromise(userContext: UserContext, cultCenter: CultCenter, userAttributeClient: IUserAttributeClient, logger: Logger, prefLocationType: any): Promise<MapWidgetV2> {
    const address = `${cultCenter.Address.addressLine1} ${cultCenter.Address.addressLine2}`
    return this.getCenterMapWidget(userContext, cultCenter, cultCenter.Address.latitude, cultCenter.Address.longitude, address, userAttributeClient, logger, prefLocationType, null, cultCenter.placeUrl)
  }

  private getCenterTimingWidget(userContext: UserContext, cultClass: CultClass): CenterTimingWidget {
    const tz = userContext.userProfile.timezone
    const classStartTime = TimeUtil.formatDateStringInTimeZone(cultClass.date + " " + cultClass.startTime, tz, "h:mm A")
    const classEndTime = TimeUtil.formatDateStringInTimeZone(cultClass.date + " " + cultClass.endTime, tz, "h:mm A")
    const schedule = `${TimeUtil.formatDateStringInTimeZone(cultClass.date, tz, "ddd D MMM")}, ${classStartTime} - ${classEndTime}`
    return new CenterTimingWidget(schedule, this.getSpacing("15", "0"))
  }

  private getWorkoutGearSubtitle(workoutGearList: InfoCard[]) {
    let subTitle = workoutGearList[0].title + " • "
    if (workoutGearList!.length >= 2) {
      subTitle += workoutGearList[1].title
    }
    if (workoutGearList.length >= 3) {
      subTitle += " • +" + (workoutGearList.length - 2).toString()
    }
    return subTitle
  }

  public getPreWorkoutGearWidget(cultClass: CultClass, isFirstTimeUser: boolean, withBlurBackground: boolean = false): ProductGridWidgetV2 {
    const workoutGearList = !_.isEmpty(cultClass.Workout.preWorkoutGears) ? CultUtil.getWorkoutGearAuroraInfoCardList(cultClass.Workout.preWorkoutGears, cultClass.Center.id) : []
    if (_.isEmpty(workoutGearList)) {
      return null
    }
    const collapsibleProperties: CollapsibleProperties = withBlurBackground ? null : {
      isCollapsible: true,
      isCollapsed: !isFirstTimeUser
    }
    let footer: string = null
    let header: Header
    const centerId = Number(cultClass.centerID)
    const subTitle = !_.isNil(workoutGearList) && !_.isEmpty(workoutGearList) ? this.getWorkoutGearSubtitle(workoutGearList) : ""
    header = {
      title: "What to bring",
      subTitle,
    }
    if (cultClass.workoutID === BOXING_BAG_WORKOUT_ID && !BOXING_GLOVES_PROVIDED_CENTER_IDS.includes(centerId)) {
      footer = "Boxing gloves are mandatory. Members without them will not be allowed to participate."
    } else if (cultClass.workoutID === YOGA_WORKOUT_ID || cultClass.workoutID === HATHA_YOGA_WORKOUT_ID) {
      header = {
        title: "What to bring",
        subTitle
      }
      footer = "Yoga mat is mandatory. Members without them will not be allowed to participate."
    }
    const instructions = this.getInstructionMediaList(cultClass)
    const widget = new ProductGridWidgetV2(header, workoutGearList, footer, instructions, collapsibleProperties, this.getSpacing("20", "0"))
    if (!_.isNil(widget) && withBlurBackground) {
      widget.items.forEach(item => item.extraWidth = 60)
      widget.customIcon = { url: "", height: "35", width: "35" }
      widget.disableCollapse = true
      widget.showTopDivider = false
      widget.showBackground = true
      widget.footer = null
    }
    return widget
  }

  private getInstructionMediaList(cultClass: CultClass): InstructionsWithMediaV2[] {
    const instructionsWithMedia: InstructionsWithMediaV2[] = _.map(cultClass.instructions, (instruction) => this.getInstructionMedia(instruction))
    return instructionsWithMedia.filter(Boolean)
  }

  private getInstructionMedia(instruction: any): InstructionsWithMediaV2 {

    if (_.isNil(instruction) || _.isNil(instruction.text) || _.isEmpty(instruction.media)) {
      return null
    }
    const media = instruction.media[0]
    if (_.isNil(media.url) || _.isNil(media.type) || media.type !== "VIDEO") {
      return null
    }

    const videoUrl = CultUtil.getCultVideoCdnUrl(media.url)
    const action: Action = { actionType: "PLAY_VIDEO_FLUTTER" as any, url: videoUrl }
    const fullScreenIcon = "/image/gymfit/fullscreen.png"

    return {
      text: instruction.text,
      media: {
        url: media.url,
        type: media.type,
        videoUrl: videoUrl,
        action: action,
        fullScreenIcon: fullScreenIcon
      }
    }
  }

  private getFacilitiesSubtile(facilities: Facility[]) {
    let subTitle = facilities[0].name + " • "
    if (facilities!.length >= 2) {
      subTitle += facilities[1].name
    }
    if (facilities.length >= 3) {
      subTitle += " • +" + (facilities.length - 2).toString()
    }
    return subTitle
  }

  private getFacilitiesWidget(cultCenter: CultCenter, isFirstTimeUser: boolean): FacilitiesWidget {
    const facilities = CultUtil.getCenterFacilitiesList(cultCenter)
    if (_.isEmpty(facilities)) { return null }
    const title = "Center Facilities"
    const subTitle = this.getFacilitiesSubtile(facilities)
    const collapsibleProperties: CollapsibleProperties = {
      isCollapsible: true,
      isCollapsed: !isFirstTimeUser
    }
    return new FacilitiesWidget(title, subTitle, facilities, collapsibleProperties, this.getSpacing("20", "0"))
  }


  private getPreWorkoutInstructions(warmUp: SimplePart): WorkoutMovementInfo {
    let preWorkoutInstructions: WorkoutMovementInfo
    if (!_.isEmpty(warmUp) && !_.isEmpty(warmUp.movements)) {
      const warmUpMovementsArray = _.map(warmUp.movements, movement => movement.title)
      preWorkoutInstructions = {
        title: "WARM UP",
        subtitle: warmUpMovementsArray.join(", ")
      }
    }
    return preWorkoutInstructions
  }

  private getPostWorkoutInstructions(coolDown: SimplePart): WorkoutMovementInfo {
    let postWorkoutInstructions: WorkoutMovementInfo
    if (!_.isEmpty(coolDown) && !_.isEmpty(coolDown.movements)) {
      const coolDownMovementsArray = _.map(coolDown.movements, movement => movement.title)
      postWorkoutInstructions = {
        title: "COOL DOWN",
        subtitle: coolDownMovementsArray.join(", ")
      }
    }
    return postWorkoutInstructions
  }

  private getWaitlistWidget(cultClass: CultClass, tz: Timezone, isFirstTimeUser: boolean, getwaitlistCnfProbabilityColor: boolean): WaitlistWidget {
    const waitlistInfo = this.getWaitlistInfo(cultClass, getwaitlistCnfProbabilityColor)
    const timePicker = this.getWaitlistTimePicker(cultClass, tz, isFirstTimeUser)
    return new WaitlistWidget("WAITLIST", cultClass.waitlistedUserCount + 1, waitlistInfo, timePicker, cultClass.bookingNumber, this.getSpacing("20", "0"))
  }

  private getWaitlistInfo(cultClass: CultClass, getwaitlistCnfProbabilityColor: boolean): WaitlistInfo {
    const waitlistCount = cultClass.waitlistedUserCount + 1
    const title = !_.isNil(cultClass.wlConfirmationThreshold) ? `WL upto #${cultClass.wlConfirmationThreshold} usually gets confirmed` : ""
    const infoAction: Action = { actionType: "NAVIGATION", url: "curefit://listpage?pageId=waitlisthiw" }
    const waitlistPrediction = "You will be informed in case of cancellation"
    const waitlistCnfProbability = CultUtil.getwaitlistCnfProbability(cultClass)
    const waitlistColor = CultUtil.getwaitlistCnfProbabilityColor(cultClass, getwaitlistCnfProbabilityColor)

    if (cultClass.wlConfirmationThreshold) {
      return {
        waitlistCount: waitlistCount,
        title: title,
        infoAction: infoAction,
        waitlistPrediction: waitlistPrediction,
        waitlistCnfProbability,
        waitlistColor
      }
    } else {
      return {
        waitlistCount: waitlistCount,
        title: title,
        infoAction: infoAction,
      }
    }
  }

  private getWaitlistTimePicker(cultClass: CultClass, tz: Timezone, isFirstTimeUser: boolean): WaitlistConfirmationTimePicker {

    const classStartTime = TimeUtil.getMomentForDateString(cultClass.date + " " + cultClass.startTime, cultClass.timezone as Timezone, TimeUtil.HH_MM_SS_DATE_FORMAT)
    const startTimeInEpoch = TimeUtil.getEpochFromDate(classStartTime.toDate())
    const { notificationTimeSlots } = getTimeSlotsForWaitlistExtensionWidget(cultClass, tz, classStartTime.format())
    const isWaitlistTimePreSelected = !_.isNil(cultClass.wlNotificationTime)
    const wlConfirmTime = isWaitlistTimePreSelected ? TimeUtil.subtractFromDate(classStartTime.toDate(), tz, cultClass?.wlNotificationTime, "minute") : null

    const timeSlot: WorkoutWaitlistTimingSlot[] = _.map(notificationTimeSlots, (time) => {
      return {
        title: `${time} mins before the class`,
        time: time,
        isSelected: cultClass?.wlNotificationTime === time,
        isRecommended: CULT_WAITLIST_EXTENSION_RECOMMENDED_TIME === time
      }
    })

    return {
      title: isWaitlistTimePreSelected ? "Edit waitlist confirmation time" : "Pick waitlist confirmation time",
      cultClassId: cultClass.id,
      description: "The closer you are to the class time, higher the chances of confirmation",
      collapsedDescription: `Waitlist will be confirmed by ${wlConfirmTime?.format("hh:mm A")}`,
      classTimeInEpochs: startTimeInEpoch,
      timeSlots: timeSlot,
      collapsibleProperties: {
        isCollapsible: true,
        isCollapsed: isWaitlistTimePreSelected
      }
    }
  }

  public async checkIFOnboardingIsAppkicable(cultClass: any, logger: Logger) {
    const time = moment(new Date(cultClass.startDateTimeUTC)).utcOffset("+0530")
    const hoursIST = time.hours()
    const minutesIST = time.minutes()
    const week = time.day()

    logger.info("primary action cult book class time check mintues", hoursIST, minutesIST, time, week)

    const classTimeInMs = (new Date("" + cultClass.startDateTimeUTC + " UTC")).valueOf()
    const currentTimeInMs = (new Date()).valueOf()
    const classTimeDiffInMin = CultUtil.convertMillisToMinutes(Math.abs(currentTimeInMs - classTimeInMs))


    const inGivenHours = (hoursIST >= 2) && (hoursIST < 16 || (hoursIST === 16 && minutesIST <= 30))
    const isNotSunday = week != 0
    const saturdayMorning = week == 6 && hoursIST >= 1 && hoursIST <= 6
    const fridayMorning = week == 5 && hoursIST >= 1 && hoursIST <= 6

    logger.info("all checks primary action cult book class time check", inGivenHours, isNotSunday, saturdayMorning, fridayMorning)
    return inGivenHours && isNotSunday && !saturdayMorning && !fridayMorning && classTimeDiffInMin > 30

  }

  private async getActionWidget(
    cultClass: CultClass,
    userContext: UserContext,
    cultMembershipDetails: CultMembershipDetails,
    productType: ProductType,
    rescheduleSourceBookingNumber: string,
    membershipList: Membership[],
    logger: Logger,
    membershipService: IMembershipService,
    segmentService: ISegmentService,
    centerBookingCache?: CenterBookingCache,
    serviceInterfaces?: CFServiceInterfaces,
    segmentCacheClient?: ISegmentationCacheClient,
    cultFitService?: ICultServiceOld
  ): Promise<ActionWidget> {

    const tz = userContext.userProfile.timezone

    let primaryAction: Action
    let secondaryAction: Action
    let description: string
    let style: any

    const limitedEliteMembership = await CultUtil.getActiveLimitedEliteMembership(userContext, membershipService)
    const isLimitedElitePack = !_.isEmpty(limitedEliteMembership)
    const selectMembershipV2 = await CultUtil.getActiveCultSelectMembershipV2(userContext, membershipService)
    const isLimitedCenterElitePack = !_.isEmpty(selectMembershipV2)
    let sessionsLeftText
    if (isLimitedElitePack) {
      sessionsLeftText = CultUtil.getLimitedEliteSessionLeftText(limitedEliteMembership, userContext)
    }

    if (isLimitedCenterElitePack) {
      const selectCenters = JSON.parse(selectMembershipV2?.attributes?.find(attr => attr.attrKey === "accessCenterIds")?.attrValue ?? "[]")?.map(String)
      if (!selectCenters.includes(cultClass.Center.centerServiceId)) {
        sessionsLeftText = "No free sessions allowed at this center"
      }
    }
    switch (cultClass.membershipClassRejectionCause) {
      case "PAUSE": {
        const membershipServiceMembership = membershipList.find(membership => membership.id === cultClass.membership?.membershipServiceId)
        const pauseEndDate = membershipServiceMembership.activePause ? membershipServiceMembership.activePause.end ? TimeUtil.parseDateFromEpochWithTimezone(tz, membershipServiceMembership.activePause.end) : undefined : undefined
        description = `Your membership is paused till ${moment(pauseEndDate).subtract(1, "day").format("Do MMM")}`
        primaryAction = this.getUnpauseAndBookAction(cultClass, productType, membershipList)
        style = { "color": "#F7C744" }
        break
      }
      case "EXPIRED": {
        const previousMembership = CultUtil.getPreviousMembershipFromMembershipList(userContext, membershipList)
        description = `Your membership has expired on ${momentTz.tz(previousMembership?.end, tz)?.format("Do MMM")}`
        const centerLevelRenewalEnabled = await AppUtil.isCenterLevelRenewalEnabled(userContext, segmentService)
        primaryAction = CultUtil.getBuyCultPassEliteAction("RENEW CULTPASS", centerLevelRenewalEnabled)
        secondaryAction = this.getClassBuyAction(cultClass)
        style = { "color": "#FF5942" }
        break
      }
      case "UPCOMING": {
        const startDate = cultClass.membership.startDate ? momentTz.tz(cultClass.membership.startDate, userContext.userProfile.timezone).format("Do MMM") : ""
        description = cultClass.membership.startDate ? `Your membership starts at ${startDate}` : "Your membership has not started"
        primaryAction = this.getEditStartDateAction(cultClass.membership, productType)
        secondaryAction = this.getClassBuyAction(cultClass)
        break
      }
      case "OUTSTATION_EXHAUSTED": {
        description = isLimitedElitePack ? sessionsLeftText : "All free outstation classes used."
        primaryAction = this.getClassBuyAction(cultClass)
        style = { "color": "#FF5942" }
        break
      }
      case "ELITE_TICKETS_USED": {
        description = isLimitedElitePack ? sessionsLeftText : "No free ELITE sessions left this month"
        primaryAction = this.getClassBuyAction(cultClass)
        style = { "color": "#FF5942" }
        break
      }
      case "SELECT": {
        description = `Your membership has access to only ${cultMembershipDetails.centerName}`
        primaryAction = this.getClassBuyAction(cultClass)
        /** ASK: How to cases for this
         * 1- Action url and type
         * 2- for example if you are currently in prebooking page for Badminton for some center say Indranagar
         *    and you have cult pass select for say HSR Cult, which does not have a Badminton Workout
         *    how to handle this case.
         */
        // Currently skiping this case
        break
      }
      case "ALL_TRIALS_USED": {
        description = "You have used your free trials"
        primaryAction = CultUtil.getBuyCultPassEliteAction(this.BUY_CULTPASS_BUTTON_TITLE)
        secondaryAction = this.getClassBuyAction(cultClass)
        break
      }
      case "CENTER_AWAY_OUTSTATION_EXHAUSTED": {
        description = isLimitedCenterElitePack ? sessionsLeftText : "No free other center session left this month."
        primaryAction = this.getClassBuyAction(cultClass)
        break
      }
      case "CENTER_AWAY_NOT_ALLOWED": {
        description = "Your membership does not provide access to this center."
        primaryAction = this.getClassBuyAction(cultClass)
        break
      }
      case "CITY_AWAY_OUTSTATION_EXHAUSTED": {
        primaryAction = this.getClassBuyAction(cultClass)
        break
      }
      case "ACCESS_CREDITS_EXHAUSTED": {
        description = "You don't have enough credits to book the class."
        primaryAction = this.getClassBuyAction(cultClass)
        break
      }
      default: {
        const selectMembership = await CultUtil.getActiveCultSelectMembership(userContext, membershipService)
        logger.info("primary action cult book class time check select membership", selectMembership)
        const isCultSelectPack = !_.isEmpty(selectMembership)
        logger.info("primary action cult book class time check select membership", isCultSelectPack)
        const membershipCredits = MembershipItemUtil.getMembershipAccessCredits(serviceInterfaces.rollbarService, selectMembership)
        logger.info("primary action cult book class time check membership credits", membershipCredits)
        if (isCultSelectPack && !membershipCredits) {
          const selectCenters: string[] = []
          selectMembership.attributes.forEach(a => {
            if (a.attrKey == AttributeKeyType.ACCESS_CENTER) {
              logger.info("primary action cult book class time check select membership centers", a.attrValue)
              selectCenters.push(a.attrValue)
            }
          })
          // hack to accomodate memberships given to JPMC users
          logger.info("primary action cult book class time check select membership centers 2", selectCenters)
          if (selectMembership?.metadata?.jpmcMembership) {
            const isSelectCenterClass = selectCenters.includes(cultClass.Center.centerServiceId)
            logger.info("primary action cult book class time check select membership centers 3", isSelectCenterClass)
            if (!isSelectCenterClass) {
              logger.info("primary action cult book class time check select membership centers", isSelectCenterClass)
              description = "No sessions allowed in this centers"
            }
            else {
              if (await centerBookingCache.isDailyLimitBreachedOnCenter(parseInt(cultClass.Center.centerServiceId), cultClass.date)) {
                logger.info("primary action cult book class time check select membership centers 5", isSelectCenterClass)
                description = "Your company exhausted daily limit of sessions allowed"
                return new ActionWidget(description, style, [], this.getSpacing("30", "0"))
              }
              else {
                logger.info("primary action cult book class time check select membership centers 6", isSelectCenterClass)
                primaryAction = this.getBookingAction(cultClass)
                break
              }
            }
          }
          const cultBenefit = selectMembership.benefits.find(benefit => benefit.name == "CENTER_AWAY")
          const isSelectCenterClass = selectCenters.includes(cultClass.Center.centerServiceId)
          logger.info("primary action cult book class time check select membership centers 4", isSelectCenterClass, cultBenefit)
          if (!isSelectCenterClass) {
            if (cultBenefit) {
              const ticketsRemaining = cultBenefit.maxTickets - cultBenefit.ticketsUsed
              logger.info("primary action cult book class time check select membership centers 5", ticketsRemaining)
              description = ticketsRemaining <= 0 ? "No free other center sessions left this month." : `${ticketsRemaining} of ${cultBenefit.maxTickets} free other center sessions left this month`
            }
          }
        }
        if (isLimitedElitePack) {
          description = sessionsLeftText
          logger.info("primary action cult book class time check limited elite pack", description)
        }
        let showIntervention = false

        try {
          showIntervention = await showInterventionAFMSlotBooking(userContext, serviceInterfaces.userAttributeClient, segmentService, cultClass.Center.id, serviceInterfaces, segmentCacheClient)
          logger.info("primary action cult book class time check showIntervention", showIntervention)
        } catch (error) {
          logger.info("primary action cult book class time error segemnt check", error)
          showIntervention = false
        }
        const isOnboardingSessionEnabled = await this.checkIFOnboardingIsAppkicable(cultClass, logger)
        logger.info("primary action cult book class time check onboarding enabled", isOnboardingSessionEnabled)
        if (showIntervention && isOnboardingSessionEnabled) {
          const meta: any = {}
          const widgets: any = []
          const mediaDataWidget: CFMediaDataWidget = new CFMediaDataWidget("image/mem-exp/afm_slot_booking.png", "image", 450, 450, 0, 15)
          const cfMediaWidget = new CFMediaWidget(mediaDataWidget)
          widgets.push(cfMediaWidget)
          const action = this.getBookingAction(cultClass)
          const getwaitlistCnfProbabilityColor = await AppUtil.isAppNewWaitlistColorCodingSupported(serviceInterfaces.segmentService, userContext)
          const bookingAction = this.prepActionWithoutChecks(userContext, action, cultClass, rescheduleSourceBookingNumber, productType, getwaitlistCnfProbabilityColor)
          const actionList = new CFBottomSheetActionWidget([bookingAction], { "top": 0, "bottom": 20 })
          widgets.push(actionList)
          meta.showTopNotch = true
          meta.blurEnabled = true
          meta.widgets = widgets
          meta.height = 0.7
          meta.pageId = "afm_intervention"
          primaryAction = {
            actionType: "SHOW_CUSTOM_BOTTOM_SHEET",
            title: action.title,
            meta,
          }
          logger.info("primary action cult book class inside if", primaryAction)
        } else {
          primaryAction = this.getBookingAction(cultClass)
        }
        break
      }
    }

    logger.info("primary action cult book class", primaryAction)

    if (_.isNil(primaryAction)) {
      primaryAction = this.getBookingAction(cultClass)
    }

    primaryAction.variant = PRIMARY_ACTION_VARIANT
    if (!_.isNil(secondaryAction)) {
      secondaryAction.variant = SECONDARY_ACTION_VARIANT
    }

    const isUnbound = await CultUtil.isUnboundWorkoutId(cultClass.workoutID, cultFitService)
    const getwaitlistCnfProbabilityColor = await AppUtil.isAppNewWaitlistColorCodingSupported(segmentService, userContext)
    const actionList = _.map([secondaryAction, primaryAction].filter(Boolean),
      action => this.prepAction(userContext, action, cultClass, rescheduleSourceBookingNumber, productType, getwaitlistCnfProbabilityColor, isUnbound))

    return new ActionWidget(description, style, actionList, this.getSpacing("30", "0"))
  }

  private prepAction(userContext: UserContext, action: Action, cultClass: CultClass, rescheduleSourceBookingNumber: string, productType: ProductType, getwaitlistCnfProbabilityColor: boolean, isUnbound: Boolean): Action {
    if (_.isNil(action)) { return null }
    const isBookingAction = action.actionType !== "NAVIGATION"
    if (isBookingAction) {
      action.meta = {
        ...action.meta,
        rescheduleSourceBookingNumber: rescheduleSourceBookingNumber,
        cultClass: cultClass,
        wlConfirmationProbablity: CultUtil.getwaitlistCnfProbability(cultClass),
        waitlistColor: CultUtil.getwaitlistCnfProbabilityColor(cultClass, getwaitlistCnfProbabilityColor),
      }
      action.meta = this.getMetaWithSlot(action, cultClass, productType)
    }
    const boxingInstructionModalAppCheck = userContext.sessionInfo.clientVersion >= BOXING_INSTRUCTION_MODAL_SUPPORTED
    if (([BOXING_BAG_WORKOUT_ID, DUMMY_EVOLVE_YOGA_WORKOUT_ID, DUMMY_STRENGTH_PLUS_WORKOUT_ID].includes(cultClass.workoutID) || isUnbound) && isBookingAction && boxingInstructionModalAppCheck) {
      const boxingBagInstructionWidget = CultUtil.getInstructionForWorkout(cultClass.workoutID, cultClass.Center.id, isUnbound)
      const buttonTitle = isUnbound ? "AGREE AND CONTINUE" : "PROCEED WITH BOOKING"
      action = CultUtil.getInstructionModalAction(action, boxingBagInstructionWidget, buttonTitle)
    }
    return action
  }

  private prepActionWithoutChecks(userContext: UserContext, action: Action, cultClass: CultClass, rescheduleSourceBookingNumber: string, productType: ProductType, getwaitlistCnfProbabilityColor: boolean): Action {
    if (_.isNil(action)) { return null }
    const isBookingAction = action.actionType !== "NAVIGATION"
    if (isBookingAction) {
      action.meta = {
        ...action.meta,
        rescheduleSourceBookingNumber: rescheduleSourceBookingNumber,
        cultClass: cultClass,
        wlConfirmationProbablity: CultUtil.getwaitlistCnfProbability(cultClass),
        waitlistColor: CultUtil.getwaitlistCnfProbabilityColor(cultClass, getwaitlistCnfProbabilityColor),
      }
      action.meta = this.getMetaWithSlot(action, cultClass, productType)
    }
    return action
  }

  private getMetaWithSlot(action: Action, cultClass: CultClass, productType: ProductType) {
    const meta = {
      ...action.meta,
      slot: {
        id: cultClass.id.toString(),
        productType: productType,
        state: CultUtil.getCultClassState(cultClass),
        workoutName: cultClass.Workout.name,
        centerID: cultClass.centerID,
        classTime: cultClass.startTime,
        classBookingType: "TRIAL",
        pageId: "prebookclass"
      },
    }
    return meta
  }

  private getUnpauseAndBookAction(cultClass: CultClass, productType: ProductType, membershipList: Membership[]): Action {
    const isUpcomingPause: boolean = cultClass.membership?.state !== "PAUSED" && CultUtil.isUpcomingPause(cultClass.membership)
    const isWaitlistClass: boolean = CultUtil.isClassAvailableForWaitlist(cultClass)
    const membershipServiceId = cultClass.membership?.membershipServiceId
    if (!_.isNil(membershipServiceId)) {
      const currentMembership = membershipList.find(membership => membership.id === membershipServiceId)
      if (!_.isNil(currentMembership)) {
        productType = MembershipItemUtil.getProductTypeFromMembership(currentMembership) ?? productType
      }
    }

    return {
      actionType: isWaitlistClass ? "CULT_UNPAUSE_AND_JOIN_WAITLIST" : "CULT_RESUME_MEMBERSHIP_BOOK_CLASS",
      title: isWaitlistClass ? "UNPAUSE & JOIN WAITLIST" : "UNPAUSE AND BOOK",
      meta: {
        membershipId: cultClass.membership?.membershipServiceId.toString(),
        productType: productType,
        toastMessage: "Membership unpaused successfully",
        isUpcomingPause: isUpcomingPause
      }
    }

  }

  private getClassBuyAction(cultClass: CultClass): Action {
    const currency: string = cultClass.currency
    return {
      actionType: "CULT_BUY_CLASS",
      title: _.isString(currency) ? `Book for ${currency} ${cultClass.amount}` : `Book for ${cultClass.amount}`,
    }
  }

  private getEditStartDateAction(membership: CultMembership, productType: ProductType): Action {
    return {
      actionType: "NAVIGATION",
      title: "EDIT START DATE",
      url: CultUtil.getStartDatePageUrl(membership.id, productType)
    }
  }

  private getBookingAction(cultClass: CultClass): Action {
    const isWaitlistClass: boolean = CultUtil.isClassAvailableForWaitlist(cultClass)
    const creditCost = cultClass?.creditCost
    if (isWaitlistClass) {
      return {
        actionType: "CULT_JOIN_WAITLIST",
        title: creditCost ? `JOIN WAITLIST FOR ${creditCost} CREDIT${((creditCost === 1) ? "" : "s")}` : "JOIN WAITLIST",
        analyticsData: {
          "classCredit": creditCost
        }
      }
    } else if (cultClass.amount > 0) {
      return this.getClassBuyAction(cultClass)
    } else {
      return {
        actionType: "CULT_BOOK_CLASS",
        title: creditCost ? `BOOK NOW FOR ${creditCost} CREDIT${(creditCost == 1) ? "" : "S"}` : "BOOK NOW",
        analyticsData: {
          "classCredit": creditCost
        }
      }
    }
  }

}

export default CultClassDetailViewV2
