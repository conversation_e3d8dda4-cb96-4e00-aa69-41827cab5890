import { injectable } from "inversify"
import { CULT_CUSTOMER_TYPE } from "@curefit/cult-client"
import {
    CultCenter,
    CultClass,
    CultClassDate,
    CultClassesResponse,
    CultWorkout,
    CultWorkoutCategory
} from "@curefit/cult-common"
import { Address } from "@curefit/eat-common"
import { ProductType } from "@curefit/product-common"
import ClassListView from "./ClassListView"
import ClassView from "./ClassView"
import { WidgetType } from "../common/views/WidgetView"
import CreditsView from "../common/views/CreditsViewWidget"
import * as _ from "lodash"
import { TimeUtil } from "@curefit/util-common"
import CultUtil from "../util/CultUtil"
import { UserContext } from "@curefit/userinfo-common"

export interface IClassListViewBuilder {
    build: (userContext: UserContext, productType: ProductType, cultClassesResponse: CultClassesResponse,
            cultCenter: CultCenter, customerType: CULT_CUSTOMER_TYPE) => Promise<ClassListView>
}

export interface ClassByDate {
    widgetType: WidgetType,
    id: string
    classByTimeList: ClassByTime[],
}

export interface ClassByTime {
    id: string,
    disableGroup: boolean,
    classes: ClassView[],
}

@injectable()
class ClassListViewBuilder implements IClassListViewBuilder {

    protected cultClassDates: CultClassDate[]
    protected cultClasses: CultClass[]
    protected workouts: CultWorkout[]
    protected workoutCategories: CultWorkoutCategory[]
    protected cultClassByCenterId: { [key: number]: CultClass[] }

    private setPrivateVariables(cultClassesResponse: CultClassesResponse, cultCenter: CultCenter) {
        this.cultClassDates = cultClassesResponse.dates
        this.cultClasses = cultClassesResponse.classes.filter(cultClass => !CultUtil.isClassAvailableForPulse(cultClass, true))
        this.workouts = cultClassesResponse.workouts
        this.workoutCategories = cultClassesResponse.workoutCategories
        // this.cultClassByCenterId = _.groupBy(cultClassesResponse.classes, "centerID");
    }

    private getWorkoutMap(): { [id: string]: string } {
        const workoutMap: { [id: string]: string } = {}
        this.workouts.forEach(workout => {
            workoutMap[workout.id] = workout.name
        })
        return workoutMap
    }

    private getAvailableWorkoutDays(): { id: string, day: string, month: string }[] {
        const days: { id: string, day: string, month: string }[] = this.cultClassDates.map(cultClassDate => {
            return {
                "id": cultClassDate.date,
                "day": cultClassDate.day,
                "month": cultClassDate.month
            }
        })
        return days
    }

    private getAvailableWorkoutFilter(): { id: number, name: string, displayText: string }[] {
        let workoutFilters: {
            id: number
            name: string
            displayText: string
        }[] = _.map(this.workoutCategories, workoutCategory => {
            return {
                id: workoutCategory.id,
                name: workoutCategory.name,
                displayText: workoutCategory.name
            }
        })

        workoutFilters = workoutFilters.sort((a, b) => {
            return (a.name > b.name) ? 1 : -1
        })

        return workoutFilters
    }

    private getAddressInfo(cultCenter: CultCenter): Address {
        const addressInfo: Address = {
            addressString: CultUtil.getCultCenterAddress(cultCenter),
            city: cultCenter.Address.City.name,
            locality: cultCenter.Address.locality,
            latLong: {
                lat: cultCenter.Address.latitude,
                long: cultCenter.Address.longitude
            },
            mapUrl: cultCenter.placeUrl,
            pincode: cultCenter.Address.pinCode ? cultCenter.Address.pinCode.toString() : ""
        }
        return addressInfo
    }

    async getClassByDateList(productType: ProductType, cultCenter: CultCenter,
                             workoutMap: { [id: string]: string }, customerType: CULT_CUSTOMER_TYPE): Promise<ClassByDate[]> {
        const classByDateList: ClassByDate[] = []

        this.cultClassDates.forEach(cultClassDate => {
            classByDateList.push({
                id: cultClassDate.date,
                widgetType: "BROWSE_CLASS_LIST",
                classByTimeList: []
            })
        })

        this.cultClasses.forEach(cultClass => {
            const classByDate = classByDateList.find(classByDate => (classByDate.id === cultClass.date))
            // This check is needed as the cult api is returning classes for an additional day but the date mapping is not present
            const isBootcampClass: boolean = CultUtil.isBootcampClass(cultClass.workoutID)
            if (isBootcampClass) {
                return
            }
            if (classByDate) {
                let classByTime = classByDate.classByTimeList.find(classByTime => (classByTime.id === cultClass.startTime))
                if (!classByTime) {
                    classByTime = {
                        id: cultClass.startTime,
                        disableGroup: false,
                        classes: []
                    }
                    classByDate.classByTimeList.push(classByTime)
                }
                if (cultClass.bookingNumber) {
                    classByTime.disableGroup = true
                }
                classByTime.classes.push(new ClassView(productType, cultClass, workoutMap, customerType))
            }
        })

        classByDateList.forEach(classByDate => {
            // Sort the classes by time
            classByDate.classByTimeList.sort((a, b) => {
                if (a.id < b.id)
                    return -1
                else
                    return 1
            })
            // Set the OVERLAPPING_BOOKING state
            classByDate.classByTimeList.forEach(classByTime => {
                if (classByTime.disableGroup) {
                    classByTime.classes.forEach(clazz => {
                        if (clazz.state === "AVAILABLE") {
                            clazz.state = "OVERLAPPING_BOOKING"
                        }
                    })
                }
            })
        })

        return classByDateList
    }

    public async build(userContext: UserContext, productType: ProductType, cultClassesResponse: CultClassesResponse, cultCenter: CultCenter, customerType: CULT_CUSTOMER_TYPE): Promise<ClassListView> {
        this.setPrivateVariables(cultClassesResponse, cultCenter)
        const document = _.find(cultCenter.documents, {tagName: "PRODUCT_BNR"})
        const header: {
            widgetType: WidgetType,
            title: string,
            image: string,
        } = {
            widgetType: "HEADER",
            title: cultCenter.name,
            image: document ? document.URL : cultCenter?.documents[0]?.URL
        }


        // get workout map
        const workoutMap: { [id: string]: string } = this.getWorkoutMap()

        // get available workout days
        const days: { id: string, day: string, month: string }[] = this.getAvailableWorkoutDays()

        // get available workouts for filter
        const workoutFilters: { id: number, name: string, displayText: string }[] = this.getAvailableWorkoutFilter()

        // get classes by date
        let classByDateList: ClassByDate[] = await this.getClassByDateList(productType, cultCenter, workoutMap, customerType)

        // get address info for cult center
        const addressInfo: Address = this.getAddressInfo(cultCenter)

        const daysWithClass: { id: string, day: string, month: string }[] = []
        classByDateList = classByDateList.filter(classList => {
            if (classList.classByTimeList.length > 0) {
                daysWithClass.push(days.find(day => day.id === classList.id))
                return true
            }
            return false
        })

        let creditCostForCenter
        cultClassesResponse.classes.forEach(cultClass => {
            if (cultCenter.id && String(cultClass.centerID) === String(cultCenter.id) && cultClass.creditCost) {
                creditCostForCenter = cultClass.creditCost
            }
        })
        const creditHeader = creditCostForCenter ? new CreditsView(creditCostForCenter) : null
        const tz = userContext.userProfile.timezone
        const response: ClassListView = {
            creditCost: creditCostForCenter,
            title: cultCenter.name,
            header: {...header, creditData: creditHeader},
            days: daysWithClass,
            workoutFilters: workoutFilters,
            classByDateList: classByDateList,
            addressInfo: addressInfo,
            bannerWidget: CultUtil.getImportantUpdatesWidget(userContext, undefined, undefined, undefined, undefined, cultCenter),
            message: cultCenter.launchDate > TimeUtil.todaysDate(tz) ? "Classes scheduled from " + TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDate(cultCenter.launchDate, tz), "Do, MMM") + " onwards" : undefined
        }

        return response
    }
}

export default ClassListViewBuilder
