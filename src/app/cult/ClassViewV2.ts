import * as _ from "lodash"
import { CultClass, CultWorkout, WaitlistProbability } from "@curefit/cult-common"
import CultUtil, { DANCE_FITNESS_REVAMP_WORKOUT_ID, STRENGTH_REVAMP_WORKOUT_ID } from "../util/CultUtil"
import { IClassView, Action, Label } from "@curefit/apps-common"
import { ProductType } from "@curefit/product-common"
import { AllAction } from "../page/vm/widgets/card/CardListWidget"
import { GENDER, LiveFitWorkoutFormat, LiveWorkoutIntensityLevel, TrainerBasicInfo } from "@curefit/diy-common"
import { LiveFitInteractiveFormat, LiveFitMasterClassFormat } from "../util/LiveUtil"
import * as momentTz from "moment-timezone"
import { SquadClassBooking } from "@curefit/cult-client"
import { UserContext } from "@curefit/userinfo-common"
import { TimeUtil } from "@curefit/util-common"
import { CacheHelper } from "../util/CacheHelper"
import { notStrictEqual } from "assert"
import { TransformUtil } from "../util/TransformUtil"
import { CULT_AREA_TOOL_TIP } from "./ClassListViewBuilderV2"
import AppUtil from "../util/AppUtil"
import { inject } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { ErrorFactory } from "@curefit/error-client"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { User } from "@curefit/user-common"

export type ClassState =
    "BOOKED"
    | "SEAT_NOT_AVAILABLE"
    | "OVERLAPPING_BOOKING"
    | "AVAILABLE"
    | "WAITLIST_AVAILABLE"
    | "WAITLISTED"
    | "WAITLIST_FULL"
    | "BOOTCAMP"

export interface SquadsListInClass {
    avatarUrlList: String[],
    namesList: String[],
    suffixText: String
}

export interface BootcampClassData {
    dismissAction: Action,
    toolTipTitle: String,
    cacheAction: Action,
    showToolTip: boolean,
    iconImageUrl: String
}

export interface ClassLevelToolTip {
    announcementAction?: Action
    onboardingTooltip?: string
    onboardingAnimation?: string
    onboardingImage?: string
    showCoachmark?: boolean
    onboardingTitle?: string
    onboardingSubtitle?: string
    showBottomSheet?: boolean
}

class ClassViewV2 implements IClassView {
    constructor(
        cultClass: CultClass,
        workoutNameMap: { [id: string]: CultWorkout },
        getwaitlistCnfProbabilityColor: boolean,
        isClassReadyForPulse?: boolean | 0 | 1 | null,
        isWaitlistConfirmationProbabilitySupported?: boolean | 0 | 1 | null,
        isNewBookingFlow?: boolean,
        squadData?: Map<string, User[]>,
        userCache?: CacheHelper,
        isBootcampToolTipVisible?: boolean,
        showWorkoutLevelToolTip?: boolean,
        bootcampBatchStartDateEpoch?: number,
        isRecommendedWorkout?: boolean,
        isMiniGoalModalNotShownForEligibleUser?: boolean,
        userContext?: UserContext,
        serviceInterfaces?: CFServiceInterfaces,
    ) {
        this.id = cultClass.id.toString()
        this.productType = workoutNameMap[cultClass.workoutID].tenantID === 1 ? "FITNESS" : "MIND"
        this.date = cultClass.date
        this.startTime = cultClass.startTime
        this.endTime = cultClass.endTime
        this.workoutId = cultClass.workoutCategoryID // old class schedule API
        this.workoutCategoryId = cultClass.workoutCategoryID
        this.isRecommendedWorkout = isRecommendedWorkout
        this.centerID = Number(cultClass.centerID)
        this.availableSeats = Math.min(cultClass.cultAppAvailableSeats, 2)
        this.workoutName = `${isClassReadyForPulse ? "PULSE - " : ""}${workoutNameMap[cultClass.workoutID].name.toUpperCase()}`
        // this.workoutName = workoutNameMap[cultClass.workoutID].name.toUpperCase()
        this.state = "AVAILABLE"
        this.waitlistInfo = {}
        if (isClassReadyForPulse) {
            this.isPulseEnabled = true
        }
        const proTipText = ""
        const classTime = momentTz
            .tz(`${cultClass.date} ${cultClass.startTime}+0530`, "Asia/Kolkata")
            .toDate()
        if (classTime) {
            const currentDate = new Date().getDate()
            const classDate = classTime.getDate()
            const isTodaysDate = !(classDate - currentDate > 0)
            // proTipText = isTodaysDate ? "\n\nPro Tip- Book 24 hours in advance" : ""
        }
        const seatUnavailableAction: Action = {
            title: "Class Booking",
            subTitle: `All slots for the class are booked${proTipText}`,
            actionType: "SHOW_CLASS_BOOKING_ERROR_MODAL",
        }
        if (CultUtil.isBootcampClass(cultClass.workoutID)) {
            this.state = "BOOTCAMP"
            this.action = "curefit://fl_listpage?pageId=transform_bootcamp&disableAnimation=true"
            this.cardAction = TransformUtil.getBootcampModalAction()
            const dismissAction: Action = {
                actionType: "REST_API",
                meta: {
                    method: "POST",
                    url: `/transform/bootcampDismissToolTip`,
                    body: {"state": "CLOSED", productType: "BOOTCAMP"}
                }
            }
            const cacheAction: Action = {
                actionType: "REST_API",
                meta: {
                    method: "POST",
                    url: `/transform/bootcampImpressionCount`,
                    body: {productType: "BOOTCAMP"}
                }
            }
            this.bootcampClassData = {
                dismissAction: dismissAction,
                toolTipTitle: "Exclusive Program",
                cacheAction: cacheAction,
                showToolTip: isBootcampToolTipVisible,
                iconImageUrl: "image/transform/sparkle_icon.png"
            }
        } else if (cultClass.bookingNumber) {
            if (!_.isEmpty(cultClass.pulseDeviceName)) {
                this.pulseDeviceName = CultUtil.pulsifyDeviceName(cultClass)
            }
            this.isBooked = true
            this.state = "BOOKED"
            this.action = this.productType === "FITNESS" ? `curefit://cultclass?bookingNumber=${cultClass.bookingNumber}` :
                `curefit://mindclass?bookingNumber=${cultClass.bookingNumber}`
            this.cardAction = {
                actionType: "NAVIGATION",
                url: `curefit://cultclass?bookingNumber=${cultClass.bookingNumber}`
            }
        } else if (cultClass.wlBookingNumber) {
            this.state = "WAITLISTED"
            this.waitlistInfo.waitlistNumber = cultClass.waitlistNumber
            if (cultClass.wlConfirmationThreshold && isWaitlistConfirmationProbabilitySupported) {
                this.waitlistInfo.waitlistCnfProbability = CultUtil.getwaitlistCnfProbability(cultClass)
                this.waitlistInfo.waitlistColor = CultUtil.getwaitlistCnfProbabilityColor(cultClass, getwaitlistCnfProbabilityColor)
            }

            this.action = this.productType === "FITNESS" ? `curefit://cultclass?bookingNumber=${cultClass.wlBookingNumber}&isWaitlistBooking=true` :
                `curefit://mindclass?bookingNumber=${cultClass.wlBookingNumber}&isWaitlistBooking=true`
            this.cardAction = {
                actionType: "NAVIGATION",
                url: `curefit://cultclass?bookingNumber=${cultClass.wlBookingNumber}&isWaitlistBooking=true`
            }
        } else if (CultUtil.isClassAvailableForWaitlist(cultClass)) {
            this.state = "WAITLIST_AVAILABLE"
            this.waitlistInfo.waitlistedUserCount = isNewBookingFlow ? cultClass.waitlistedUserCount + 1 : cultClass.waitlistedUserCount
            if (cultClass.wlConfirmationThreshold && isWaitlistConfirmationProbabilitySupported) {
                this.waitlistInfo.confirmationProbability = "Upto #" + cultClass.wlConfirmationThreshold +
                    " usually gets confirmed"
                this.waitlistInfo.confProbabilityTextStart = "Upto "
                this.waitlistInfo.confProbabilityTextEnd = " usually gets confirmed"
                this.waitlistInfo.confProbabilityTextMiddle = `#${cultClass.wlConfirmationThreshold}`
                this.waitlistInfo.waitlistCnfProbability = CultUtil.getwaitlistCnfProbability(cultClass)
                this.waitlistInfo.waitlistColor = CultUtil.getwaitlistCnfProbabilityColor(cultClass, getwaitlistCnfProbabilityColor)
            }
        } else if (this.availableSeats <= 0 && cultClass.isWaitlistFull) {
            this.state = "WAITLIST_FULL"
            this.cardAction = {
                title: "Waitlist Full",
                subTitle: "Waitlist for this class is currently full",
                actionType: "SHOW_CLASS_BOOKING_ERROR_MODAL",
            }
        } else if (this.availableSeats <= 0) {
            this.state = "SEAT_NOT_AVAILABLE"
            this.cardAction = seatUnavailableAction
        } else if (cultClass.allowTrial === false && cultClass.amount === 0) {
            this.state = "SEAT_NOT_AVAILABLE"
            this.cardAction = seatUnavailableAction
        } else if (cultClass.allowPPC === false) {
            this.state = "SEAT_NOT_AVAILABLE"
            this.cardAction = seatUnavailableAction
        }

        if (workoutNameMap[cultClass.workoutID].isNewWorkout && (this.state === "AVAILABLE" || this.state === "WAITLIST_AVAILABLE")) {
            this.isNewClass = true
        }

        if (this.state === "AVAILABLE" || this.state === "WAITLIST_AVAILABLE") {
            this.cardAction = {
                actionType: "NAVIGATION",
                url: `curefit://prebookclass?classId=${cultClass.id}`
            }
        }

        if (isMiniGoalModalNotShownForEligibleUser) {
            this.cardAction = CultUtil.getMiniGoalIntroCustomBottomSheetAction(this.cardAction, userContext)
            serviceInterfaces.featureStateCache.set(userContext.userProfile.userId, "mini_goal_info_key", "shown")
        }


        if (cultClass.workoutID == DANCE_FITNESS_REVAMP_WORKOUT_ID && showWorkoutLevelToolTip) {
            const dismissAction: Action = {
                actionType: "REST_API",
                title: "Got it",
                meta: {
                    method: "POST",
                    url: `/cult/cultClassDismissToolTip`,
                    body: {"state": "DISMISSED"}
                }
            }
            this.classLevelToolTip = {
                announcementAction: dismissAction,
                onboardingTooltip : "New Edition",
                onboardingTitle : "New Playlist",
                onboardingSubtitle : "New curated playlist.",
                onboardingAnimation : null,
                onboardingImage : "/image/cultpass/waitlist_onboarding_6.png",
                showBottomSheet : false
            }
        }

        if (squadData && squadData.has(cultClass.id.toString())) {
            const avatarUrls: String[] = []
            const namesList: String[] = []
            const users: User[] = squadData.get(cultClass.id.toString())
            users?.forEach( (user: User) => {
                avatarUrls.push(!_.isNil(user.profilePictureUrl) ? user.profilePictureUrl : "")
                namesList.push(!_.isNil(user.firstName) ? user.firstName : "")
            })

            this.squadJoining = {avatarUrlList: avatarUrls, namesList: namesList, suffixText: "joining"}
        }
    }

    public id: string
    public date: string
    public startTime: string
    public endTime: string
    public workoutId: number
    public workoutCategoryId: number
    public workoutName: string
    public availableSeats: number
    public action?: string
    public isBooked?: boolean
    public isPulseEnabled?: boolean
    public pulseDeviceName?: string
    public state: ClassState
    public centerID: number
    public productType: ProductType
    public waitlistInfo?: {
        waitlistNumber?: number
        waitlistedUserCount?: number
        confirmationProbability?: string
        confProbabilityTextStart?: string
        confProbabilityTextEnd?: string
        confProbabilityTextMiddle?: string
        waitlistCnfProbability?: WaitlistProbability,
        waitlistColor?: string,
    }
    public buttonAction?: AllAction
    public cardAction?: Action
    public shareAction?: AllAction
    public trainerName?: string
    public formattedTimeString?: string
    public format?: Array<LiveFitWorkoutFormat | LiveFitMasterClassFormat | LiveFitInteractiveFormat>
    public tagColor?: string
    public isLocked?: boolean
    public trainer?: TrainerBasicInfo
    public trainers?: any
    public tags?: string[]
    public labels?: Label[]
    public image?: string
    public imageAspectRatio?: number
    public workoutFormat?: LiveFitWorkoutFormat
    public trainerAction?: Action
    public intensityLevel?: LiveWorkoutIntensityLevel
    public isLive?: Boolean
    public isNewClass?: boolean
    public sortPreference?: number
    public squadJoining?: SquadsListInClass
    public bootcampClassData?: BootcampClassData
    public classLevelToolTip?: ClassLevelToolTip
    public isRecommendedWorkout?: boolean
}

export default ClassViewV2
