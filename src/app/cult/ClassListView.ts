import ClassView from "./ClassView"
import { WidgetType, WidgetView } from "../common/views/WidgetView"
import { Address } from "@curefit/eat-common"
import { BannerCarouselWidget } from "../page/PageWidgets"
import CreditsView from "../common/views/CreditsViewWidget"

interface ClassListView {
    title: string
    message?: string
    header: WidgetView & {
        title: string
        image: string
        creditData?: CreditsView
    }

    days: {
        id: string
        day: string
        month: string
    }[]

    classByDateList: {
        widgetType: WidgetType,
        id: string
        classByTimeList: {
            id: string,
            disableGroup: boolean
            classes: ClassView[]
        }[],
    }[]

    workoutFilters: {
        id: number
        name: string
        displayText: string
    }[]

    addressInfo: Address

    bannerWidget?: BannerCarouselWidget
    creditCost?: number
}

export default ClassListView
